.setting
.settings
/.settings/
*.project
*.classpath
*.iml
.idea/
##ignore this file##
/target/
**/target/
/.svn/ 
.classpath
.project
.settings      
*/.settings/*
##filter databfile��sln file##
*.mdb  
*.ldb  
*.sln    
##class file##
*.com  
*.class  
*.dll  
*.exe  
*.o  
*.so  
# compression file
*.7z  
*.dmg  
*.gz  
*.iso 
*.rar  
*.tar  
*.zip  
*.via
*.tmp
*.err 
# OS generated files #  
.DS_Store  
.DS_Store?  
ehthumbs.db  
Thumbs.db  
hsf
/.metadata/
/.recommenders/
*.bak
*.log
bin
build.module.sh
config/src/main/resources/git.properties
copy-all.sh
package.sh