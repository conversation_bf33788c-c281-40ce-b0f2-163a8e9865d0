# 构建阶段：添加maven并编译代码
FROM maven:3.8.5-openjdk-17 as builder

WORKDIR /app
RUN echo '<?xml version="1.0" encoding="UTF-8"?>' >> maven-settings.xml
RUN echo '<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 http://maven.apache.org/xsd/settings-1.0.0.xsd"><mirrors><mirror><id>nexus-aliyun</id><mirrorOf>central</mirrorOf><name>Nexus aliyun</name><url>http://maven.aliyun.com/nexus/content/groups/public</url></mirror><mirror><id>maven-default-http-blocker</id><mirrorOf>blocker</mirrorOf><name>用于突破maven-3.8.1版本以后默认禁用http仓库的限制</name><url>http://0.0.0.0/</url></mirror></mirrors></settings>' >> maven-settings.xml
# 拷贝项目源码
COPY . /app

ARG module_path
# 执行maven打包命令
RUN mvn clean package -pl $module_path -am -Dmaven.test.skip=true -s maven-settings.xml

FROM  openjdk:17-jdk
WORKDIR /app

ARG module_path
# 复制 jar 文件
COPY --from=builder /app/$module_path/target/*.jar app.jar

# 设置环境变量
ENV LANG C.UTF-8
ENV SPRING_CLOUD_CONFIG_URI=http://*************:8888
ENV SPRING_CLOUD_CONFIG_LABEL=asset-2025
ENV SPRING_CLOUD_CONFIG_PROFILE=dev
ENV JAVA_TOOL_OPTIONS="-Xms1g -Xmx1g -XX:+UseSerialGC"

ENTRYPOINT ["java", "-server","-jar", "app.jar"]