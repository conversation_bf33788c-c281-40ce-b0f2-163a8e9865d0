# 说明

# 说明

1. 在base数据库中修改`ba_res_service`

    ```sql
    ALTER TABLE `ba_res_service` 
    MODIFY COLUMN `service_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL AFTER `service_code`;
    ```

2. 下载源码
    ```shell
    mvn dependency:sources -DdownloadSources=true  -DdownloadJavadocs=true
    ```
3. 启动脚本
    ```shell
    # 请按照脚本顺序依次启动
    echo 'sudo nohup java -server -Xmx512m -Xms512m -jar eureka-2.1.4-RELEASE.jar >eureka.log 2>&1 &' > eureka.sh
    
    # 以下服务 可以添加 -Dswagger.host参数调整swagger接口host与eureka status page的domain
    echo 'sudo nohup java -Dspring.cloud.config.server.git.uri=http://************:2223/amrjlg/config.git -Dspring.rabbitmq.host=************ -server -Xmx512m -Xms512m -jar config-2.1.4-RELEASE.jar >config.log 2>&1 &' >config.sh
    echo 'sudo nohup java -server -Xmx512m -Xms512m -jar hystrix-2.1.4-RELEASE.jar >hystrix.log 2>&1 &' > hystrix.sh
    
    # service for web
    echo 'sudo nohup java -server -Xmx512m -Xms512m -jar base.jar >service-base.log 2>&1 &' > service-base.sh
    echo 'sudo nohup java -server -Xmx512m -Xms512m -jar goods.jar >service-goods.log 2>&1 &' > service-goods.sh
    echo 'sudo nohup java -server -Xmx512m -Xms512m -jar information.jar >service-information.log 2>&1 &' > service-information.sh
    echo 'sudo nohup java -server -Xmx512m -Xms512m -jar member.jar >service-member.log 2>&1 &' > service-member.sh
    echo 'sudo nohup java -server -Xmx512m -Xms512m -jar order.jar >service-order.log 2>&1 &' > service-order.sh
    echo 'sudo nohup java -server -Xmx512m -Xms512m -jar pay.jar >service-pay.log 2>&1 &' > service-pay.sh
    
    # web service
    echo 'sudo nohup java -server -Xmx512m -Xms512m -jar web-base.jar >web-base.log 2>&1 &' > web-base.sh
    echo 'sudo nohup java -server -Xmx512m -Xms512m -jar web-buyer.jar >web-buyer.log 2>&1 &' > web-buyer.sh
    echo 'sudo nohup java -server -Xmx512m -Xms512m -jar web-emall.jar >web-emall.log 2>&1 &' > web-emall.sh
    echo 'sudo nohup java -server -Xmx512m -Xms512m -jar web-platform.jar >web-platform.log 2>&1 &' > web-platform.sh
    echo 'sudo nohup java -server -Xmx512m -Xms512m -jar web-seller.jar >web-seller.log 2>&1 &' > web-seller.sh
    ```
4. 构建

```shell
# 构建全部
mvn clean package -D maven.test.skip=true
# 构建制定模块
# mvn clean package -pl 模块 -am

mvn clean package -pl config -am -D maven.test.skip=true
mvn clean package -pl web/web-base/ -am -D maven.test.skip=true

```




