# TODO 

- [x] 核实原新增员工是否创建账号，若无需新增

  核实已创建账号 2021年11月3日，密码为随机8位数字

  - `/sellerApi/account/registerSubAccountByMaster`

    企业主账号创建子账号（含角色、数据权限、组织机构、销售区域

  - `/platformApi/account/registerSubAccountByAdmin`

    平台管理员创建子账号

  - `/platformApi/account/registerAccountByAdmin`

    平台管理员代注册个人账户，不需要校验短信

  - `/platformApi/account/registerSellerByAdmin`

    平台管理员创建销售员子账号（账户默认有销售员资质）

- [ ] 商品历史记录 订单是否有快照 ，若无需新增

  

- [ ] 确认商品禁用是否下架商品 ，若无需新增

  接口只检查了标准商品状态 是否为已禁用，下架商品为做

  - `/platformApi/goods/disableBaseGoods`
  - `/platformApi/goods/disableGoodsPlatform`

- [x] 二次验证 - 接口变更 校验是否为平台管理员 改为批量 

  - `/baseApi/account/openSecondaryAuthentication`

    **`新增接口`** 原接口未做任何处理

  - `/platformApi/account/secondaryAuthentication`

    | URL                                            | method | content-type       |
    | ---------------------------------------------- | ------ | ------------------ |
    | `/platformApi/account/secondaryAuthentication` | `POST` | `application/json` |

    ```json
    {
        "open":"true 开启短信二次认证 false 为关闭",
        "accounts":["选中的用户id  如果为空 则表示操作所有用户"]
    }
    ```

    

- [x] 手机号唯一性保证 2021年11月9日

- [x] 登陆图形验证码 2021年11月9日

  - `/baseApi/anon/account/v2/captcha/get`

    获取图形验证码 `GET` 不需要传参数

  - `/baseApi/anon/account/v2/captcha/check`

    校验图形验证码 `POST` 使用组件默认参数

- [ ] 商品分类修改

- [ ] 商品sku修改

- [ ] 商品挂牌处理

- [ ] 修改短信模板

- [ ] 预订单模块修改

- [ ] 短信、邮箱服务 （外部）

- [ ] 去掉物流相关代码

  - `com.ecommerce.web.platform.controller.WaybillController`
  - `com.ecommerce.web.platform.controller.VehicleTypeController`
  - `com.ecommerce.web.platform.controller.VehicleRealtimeController`
  - `...`
  
- [ ] 标准商品审核

  | URL                          | METHOD | CONENT-TYPE        |
  | ---------------------------- | ------ | ------------------ |
  | `/sellerApi/base/good/apply` | `POST` | `application/json` |

  `BODY`

  ```json
  {
      "goodsId":"商品ID 新建无",
      "goodsName":"商品名称",
      "categoryAttributeDTOS":[
          {
              "categoryAttributeId":"属性id",
              "categoryAttributeValueDTOs":[
                 ...
              ]
          }
      ]
  }
  ```

  | URL                         | METHOD |      |
  | --------------------------- | ------ | ---- |
  | `sellerApi/base/good/audit` | `post` |      |

  `QUERY PARAM`

  | name      | comment              |
  | --------- | -------------------- |
  | `auditId` | 审核id               |
  | `pass`    | true 通过 false 驳回 |

  

  