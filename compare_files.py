#!/usr/bin/env python3
import csv
import re

def extract_java_files_from_csv(csv_file):
    """从CSV文件中提取Java文件路径"""
    java_files = set()
    
    with open(csv_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            component = row['Component']
            # 提取Java文件路径，去掉项目前缀
            if 'Ecommerce-all-sprint2:' in component:
                java_path = component.split('Ecommerce-all-sprint2:')[1]
                java_files.add(java_path)
    
    return sorted(java_files)

def extract_java_files_from_changelist(changelist_text):
    """从change list文本中提取Java文件路径"""
    java_files = set()
    
    lines = changelist_text.strip().split('\n')
    for line in lines:
        line = line.strip()
        if line.startswith('M ') and line.endswith('.java'):
            java_path = line[2:].strip()  # 去掉 'M ' 前缀
            java_files.add(java_path)
    
    return sorted(java_files)

# Change list内容（从git status --porcelain输出）
changelist_content = """
 M common/src/main/java/com/ecommerce/common/utils/OkHttpUtil.java
 M generator/src/main/java/com/ecommerce/plugin/GenerateUtil.java
 M mq/cmq-topic/src/main/java/com/qcloud/cmq/Json/JSONArray.java
 M mq/cmq-topic/src/main/java/com/qcloud/cmq/Subscription.java
 M mq/mq-core/src/main/java/com/ecommerce/mq/core/handler/MessageHandlerFactory.java
 M mq/mq-core/src/main/java/com/ecommerce/mq/core/service/IMQProducer.java
 M mq/rabbitmq-sdk/src/main/java/com/ecommerce/rabbitmq/consumer/InitConsumerConfig.java
 M service/service-base/base-api/src/main/java/com/ecommerce/base/api/dto/authRes/VUEMenuDTO.java
 M service/service-base/base-api/src/main/java/com/ecommerce/base/api/dto/cloud/AttachmentBase.java
 M service/service-base/base/src/main/java/com/ecommerce/base/biz/ISysLogBiz.java
 M service/service-base/base/src/main/java/com/ecommerce/base/biz/ISysLogExceptionBiz.java
 M service/service-base/base/src/main/java/com/ecommerce/base/biz/impl/AuthMenuBiz.java
 M service/service-base/base/src/main/java/com/ecommerce/base/biz/impl/RegionBiz.java
 M service/service-base/base/src/main/java/com/ecommerce/base/biz/impl/SysLogBiz.java
 M service/service-base/base/src/main/java/com/ecommerce/base/biz/impl/SysLogExceptionBiz.java
 M service/service-base/base/src/main/java/com/ecommerce/base/biz/impl/WarehouseErpInfoBiz.java
 M service/service-base/base/src/main/java/com/ecommerce/base/biz/impl/WarehouseZoneMapBiz.java
 M service/service-base/base/src/main/java/com/ecommerce/base/service/impl/AliyunCloudService.java
 M service/service-base/base/src/main/java/com/ecommerce/base/service/impl/RetryServiceImpl.java
 M service/service-base/base/src/main/java/com/ecommerce/base/service/impl/SysLogQueryService.java
 M service/service-base/base/src/main/java/com/ecommerce/base/service/impl/TencentCloudService.java
 M service/service-base/base/src/main/java/com/ecommerce/base/service/message/impl/MessageReceiverService.java
 M service/service-base/base/src/main/java/com/ecommerce/base/service/message/impl/receiver/MessageReceiverCarrierDriver.java
 M service/service-base/base/src/main/java/com/ecommerce/base/service/message/impl/receiver/MessageReceiverSellerSalesMan.java
 M service/service-base/base/src/main/java/com/ecommerce/base/service/message/impl/receiver/MessageReceiverSellerSalesManager.java
 M service/service-base/base/src/main/java/com/ecommerce/base/service/message/impl/receiver/MessageReceiverSellerSalesPerson.java
 M service/service-base/base/src/main/java/com/ecommerce/base/utils/JasperHelper.java
 M service/service-common/src/main/java/com/ecommerce/common/aop/log/LogAop.java
 M service/service-common/src/main/java/com/ecommerce/common/cache/RedisCache.java
 M service/service-common/src/main/java/com/ecommerce/common/config/redis/BizRedisConfig.java
 M service/service-common/src/main/java/com/ecommerce/common/config/redis/DefaultRedisConfig.java
 M service/service-common/src/main/java/com/ecommerce/common/config/redis/RedisConfig.java
 M service/service-common/src/main/java/com/ecommerce/common/exception/GlobalExceptionHandler.java
 M service/service-common/src/main/java/com/ecommerce/common/service/IBaseBiz.java
 M service/service-common/src/main/java/com/ecommerce/common/service/IBaseService.java
 M service/service-common/src/main/java/com/ecommerce/common/service/IUrlReferenceCacheService.java
 M service/service-common/src/main/java/com/ecommerce/common/service/MySpecialProvider.java
 M service/service-common/src/main/java/com/ecommerce/common/service/RedisService.java
 M service/service-common/src/main/java/com/ecommerce/common/service/common/BaseService.java
 M service/service-common/src/main/java/com/ecommerce/common/service/lock/DistributedLock.java
 M service/service-goods/goods-api/src/main/java/com/ecommerce/goods/api/service/IGoodsCategoryService.java
 M service/service-goods/goods/src/main/java/com/ecommerce/goods/biz/IExternalExceptionBizService.java
 M service/service-goods/goods/src/main/java/com/ecommerce/goods/biz/impl/ConcreteVarietyBiz.java
 M service/service-goods/goods/src/main/java/com/ecommerce/goods/biz/impl/ContractBatchAdjustPriceBiz.java
 M service/service-goods/goods/src/main/java/com/ecommerce/goods/biz/impl/ContractBatchAdjustPriceResultBiz.java
 M service/service-goods/goods/src/main/java/com/ecommerce/goods/biz/impl/ResourceGoodsBiz.java
 M service/service-goods/goods/src/main/java/com/ecommerce/goods/biz/impl/StockAgentBiz.java
 M service/service-goods/goods/src/main/java/com/ecommerce/goods/biz/impl/StockSellerBiz.java
 M service/service-goods/goods/src/main/java/com/ecommerce/goods/cache/GoodsAttrCacheService.java
 M service/service-goods/goods/src/main/java/com/ecommerce/goods/cache/GoodsCategoryCacheService.java
 M service/service-goods/goods/src/main/java/com/ecommerce/goods/common/DocTemplateUtils.java
 M service/service-goods/goods/src/main/java/com/ecommerce/goods/config/SpringCacheConfig.java
 M service/service-goods/goods/src/main/java/com/ecommerce/goods/controller/GoodsCategoryController.java
 M service/service-goods/goods/src/main/java/com/ecommerce/goods/controller/GoodsController.java
 M service/service-goods/goods/src/main/java/com/ecommerce/goods/controller/OrderPrepareController.java
 M service/service-goods/goods/src/main/java/com/ecommerce/goods/service/impl/ContractBatchAdjustPriceService.java
 M service/service-goods/goods/src/main/java/com/ecommerce/goods/service/impl/GoodsService.java
 M service/service-goods/goods/src/main/java/com/ecommerce/goods/service/resource/impl/PriceRuleBeanWrapper.java
 M service/service-information/information/src/main/java/com/ecommerce/information/bill/check/biz/impl/BillCheckWaybillBaseDataBiz.java
 M service/service-information/information/src/main/java/com/ecommerce/information/biz/impl/AdvertisementBiz.java
 M service/service-information/information/src/main/java/com/ecommerce/information/biz/impl/ComplaintsOpinionBiz.java
 M service/service-information/information/src/main/java/com/ecommerce/information/biz/impl/ContractBreachConfigBiz.java
 M service/service-information/information/src/main/java/com/ecommerce/information/biz/impl/ContractBreachRecordBiz.java
 M service/service-information/information/src/main/java/com/ecommerce/information/biz/impl/ContractBreachRecordProcessBiz.java
 M service/service-information/information/src/main/java/com/ecommerce/information/biz/impl/FavoriteCollectionBiz.java
 M service/service-information/information/src/main/java/com/ecommerce/information/biz/impl/MemberIntegralBiz.java
 M service/service-information/information/src/main/java/com/ecommerce/information/biz/impl/MemberIntegralChangeInfoBiz.java
 M service/service-information/information/src/main/java/com/ecommerce/information/biz/impl/NewsBiz.java
 M service/service-information/information/src/main/java/com/ecommerce/information/biz/impl/NewsCommentBiz.java
 M service/service-information/information/src/main/java/com/ecommerce/information/biz/impl/NewsTechnologyEquipmentBiz.java
 M service/service-information/information/src/main/java/com/ecommerce/information/biz/impl/PriceInfoBiz.java
 M service/service-information/information/src/main/java/com/ecommerce/information/biz/impl/TenderingInfoBiz.java
 M service/service-information/information/src/main/java/com/ecommerce/information/service/impl/AdvertisementSpaceService.java
 M service/service-information/information/src/main/java/com/ecommerce/information/service/impl/IndexPageInfoService.java
 M service/service-information/information/src/main/java/com/ecommerce/information/util/MapUtils.java
 M service/service-interface/open-api/src/main/java/com/ecommerce/open/api/dto/apicenter/ApiResult.java
 M service/service-interface/open-api/src/main/java/com/ecommerce/open/api/service/IPingAnJZConnectorService.java
 M service/service-interface/open-api/src/main/java/com/ecommerce/open/api/service/apicenter/IOpenAPIInvokeService.java
 M service/service-interface/open/src/main/java/com/cnoocshell/open/service/payment/impl/PingAnJZConnector.java
 M service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/util/Bytes.java
 M service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/util/db/GxExample.java
 M service/service-order/order/src/main/java/com/ecommerce/order/controller/CartResourceController.java
 M service/service-order/order/src/main/java/com/ecommerce/order/controller/OrderController.java
 M service/service-order/order/src/main/java/com/ecommerce/order/controller/OrderStatisticsController.java
 M service/service-pay/pay/src/main/java/com/ecommerce/pay/util/XMLUtil.java
 M service/service-pay/pay/src/main/java/com/ecommerce/pay/v2/biz/impl/BankCardInfoBiz.java
 M service/service-pay/pay/src/main/java/com/ecommerce/pay/v2/biz/impl/DriverDayCarriageBiz.java
 M service/service-pay/pay/src/main/java/com/ecommerce/pay/v2/biz/impl/DriverPayInfoBiz.java
 M service/service-pay/pay/src/main/java/com/ecommerce/pay/v2/biz/impl/DriverSummaryInfoBiz.java
 M service/service-pay/pay/src/main/java/com/ecommerce/pay/v2/biz/impl/PaymentExceptionBiz.java
 M service/service-pay/pay/src/main/java/com/ecommerce/pay/v2/biz/impl/PlatformDayCarriageBiz.java
 M service/service-pay/pay/src/main/java/com/ecommerce/pay/v2/service/impl/PaymentCheckService.java
 M service/service-pay/pay/src/main/java/com/ecommerce/pay/v2/util/gnete/RSAUtil.java
 M service/service-price/price/src/main/java/com/ecommerce/price/controller/EmptyLoadFeeController.java
 M service/service-trace/trace-api/src/main/java/com/ecommerce/trace/api/enums/UnloadTypeEnum.java
 M service/service-trace/trace-api/src/main/java/com/ecommerce/trace/api/enums/WarnTypeEnum.java
 M web/web-base/src/main/java/com/ecommerce/web/base/controller/AccountAnonController.java
 M web/web-base/src/main/java/com/ecommerce/web/base/controller/FavoriteCollectionController.java
 M web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberAreaController.java
 M web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberController.java
 M web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberRelationController.java
 M web/web-base/src/main/java/com/ecommerce/web/base/controller/OrgInfoController.java
 M web/web-buyer/src/main/java/com/ecommerce/web/buyer/controller/GoodsAddItemController.java
 M web/web-open-api/src/main/java/com/ecommerce/open/web/service/impl/HttpServiceImpl.java
"""

def main():
    # 提取CSV中的Java文件
    csv_files = extract_java_files_from_csv('sonar_issues_java_S3740.csv')
    
    # 提取change list中的Java文件
    changelist_files = extract_java_files_from_changelist(changelist_content)
    
    print("=== CSV文件中的Java文件总数 ===")
    print(f"总计: {len(csv_files)} 个文件")
    
    print("\n=== Change List中的Java文件总数 ===")
    print(f"总计: {len(changelist_files)} 个文件")
    
    # 找出CSV中有但change list中没有的文件
    missing_in_changelist = set(csv_files) - set(changelist_files)
    
    # 找出change list中有但CSV中没有的文件
    extra_in_changelist = set(changelist_files) - set(csv_files)
    
    print(f"\n=== 对比结果 ===")
    print(f"CSV中的文件数量: {len(csv_files)}")
    print(f"Change List中的文件数量: {len(changelist_files)}")
    print(f"匹配的文件数量: {len(set(csv_files) & set(changelist_files))}")
    print(f"CSV中有但Change List中没有的文件: {len(missing_in_changelist)}")
    print(f"Change List中有但CSV中没有的文件: {len(extra_in_changelist)}")
    
    if missing_in_changelist:
        print(f"\n=== CSV中有但Change List中没有的文件 ({len(missing_in_changelist)}个) ===")
        for file in sorted(missing_in_changelist):
            print(f"  - {file}")
    
    if extra_in_changelist:
        print(f"\n=== Change List中有但CSV中没有的文件 ({len(extra_in_changelist)}个) ===")
        for file in sorted(extra_in_changelist):
            print(f"  + {file}")
    
    # 计算覆盖率
    coverage = len(set(csv_files) & set(changelist_files)) / len(csv_files) * 100 if csv_files else 0
    print(f"\n=== 覆盖率统计 ===")
    print(f"修复覆盖率: {coverage:.1f}% ({len(set(csv_files) & set(changelist_files))}/{len(csv_files)})")

if __name__ == "__main__":
    main()
