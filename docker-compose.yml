include:
  - path:
      - eureka/docker-compose.yml
      - config/docker-compose.yml
      - web/web-base/docker-compose.yml
      - web/web-buyer/docker-compose.yml
      - web/web-emall/docker-compose.yml
      - web/web-platform/docker-compose.yml
      - web/web-seller/docker-compose.yml
      - service/service-base/base/docker-compose.yml
      - service/service-goods/goods/docker-compose.yml
      - service/service-information/information/docker-compose.yml
      - service/service-member/member/docker-compose.yml
      - service/service-order/order/docker-compose.yml
      - service/service-pay/pay/docker-compose.yml

services:
    nginx:
        image: nginx:1.29.0-alpine
        container_name: img-nginx
        volumes:
            - ./nginx/img.conf:/etc/nginx/nginx.conf
            - /usr/local/share/img:/usr/local/share/img
        ports:
            - "80:80"
