#!/bin/bash

# 默认的服务名为空，表示操作所有服务
SERVICE_NAME=""
ACTION=""

# 解析命令行参数
while [[ "$#" -gt 0 ]]; do
    case $1 in
        -n|--name) SERVICE_NAME="$2"; shift ;;
        -a|--action)
            ACTION=$(echo "$2" | tr '[:upper:]' '[:lower:]')
            if [[ ! " start restart stop " =~ " ${ACTION} " ]]; then
                echo "错误：无效的动作 '$ACTION'，可选值: start, restart, stop"
                exit 1
            fi
            shift
            ;;
        *)
            echo "未知参数: $1"
            exit 1
            ;;
    esac
    shift
done

# 检查是否提供了 action
if [[ -z "$ACTION" ]]; then
    echo "错误：必须指定 --action 参数"
    exit 1
fi

# 执行对应的操作
case "$ACTION" in
    start)
        if [[ -n "$SERVICE_NAME" ]]; then
            docker compose -f docker-compose.yml up -d "$SERVICE_NAME"
        else
            docker compose -f docker-compose.yml up -d
        fi
        ;;
    restart)
        if [[ -n "$SERVICE_NAME" ]]; then
            docker compose -f docker-compose.yml restart "$SERVICE_NAME"
        else
            docker compose -f docker-compose.yml restart
        fi
        ;;
    stop)
        if [[ -n "$SERVICE_NAME" ]]; then
            docker compose -f docker-compose.yml down "$SERVICE_NAME"
        else
            docker compose -f docker-compose.yml down
        fi
        ;;
esac

echo "✅ Docker Compose 操作 [动作: $ACTION] 已完成。"