properties([
    parameters([
        gitParameter(
                name: 'BRANCH_TAG',
                type: 'PT_BRANCH_TAG',
                branchFilter: 'origin/(.*)',
                defaultValue: 'master',
                selectedValue: 'DEFAULT',
                sortMode: 'DESCENDING_SMART',
                description: 'Select your branch or tag.'),
    ])
])

node {
    def MVN_HOME
    def CONFIG_IP
    def EUREKA_SERVER_ADDRESS
    def CREDENTIAL_ID
    stage('Preparation') {
        MVN_HOME = tool 'maven 3.6.3'
        //根据Job内配置的ENV参数来区分不同环境，设置不同环境所需要的环境变量名称
        switch ("${ENV}"){
            case 'test':
                CONFIG_IP = "${TEST_CONFIG_IP}"
                EUREKA_SERVER_ADDRESS = "${TEST_EUREKA_SERVER_ADDRESS}"
                CREDENTIAL_ID = 'jenkins_dev'
                break;
            case 'sit':
                CONFIG_IP = "${SIT_CONFIG_IP}"
                EUREKA_SERVER_ADDRESS = "${SIT_EUREKA_SERVER_ADDRESS}"
                CREDENTIAL_ID = 'jenkins_sit'
                break;
            case 'uat':
                CONFIG_IP = "${UAT_CONFIG_IP}"
                EUREKA_SERVER_ADDRESS = "${UAT_EUREKA_SERVER_ADDRESS}"
                CREDENTIAL_ID = 'jenkins-uat'
                break;
            case 'prod':
                CONFIG_IP = "${PROD_CONFIG_IP}"
                EUREKA_SERVER_ADDRESS = "${PROD_EUREKA_SERVER_ADDRESS}"
                CREDENTIAL_ID = 'jenkins4ssh'
                break;
        }
    }
    stage('git checkout') {
        checkout([$class: 'GitSCM',
                  branches: [[name: "${params.BRANCH_TAG}"]],
                  doGenerateSubmoduleConfigurations: false,
                  extensions: [],
                  submoduleCfg: [],
                  userRemoteConfigs: [[credentialsId: 'gitlab4Jenkins', url: 'http://********:80/common/crc-otter.git']]])
    }
    stage('maven build') {
        sh "${MVN_HOME}/bin/mvn -P${ENV} clean package -Dmaven.test.skip=true docker:build"
    }
    stage("deploy") {
        //从pom文件中获取服务的版本
        def VERSION = sh(script: "${MVN_HOME}/bin/mvn org.apache.maven.plugins:maven-help-plugin:3.1.0:evaluate -Dexpression=project.version -q -DforceStdout", returnStdout: true).trim()
        def IMAGE_URL = "${DOCKER_SERVER}/otter:${VERSION}"
        //从Job变量、全局环境变量及自定义的变量中获取相关的值来替换docker-run.sh及docker-compose.yml文件中的值
        sh "sed -i \"s#{IMAGE}#${IMAGE_URL}#g\" docker-run.sh"
        sh "sed -e \"s#{IMAGE}#${IMAGE_URL}#g;s#{CONFIG_PROFILE}#${ENV}#g;s#{CONFIG_IP}#${CONFIG_IP}#g;s#{EUREKA_SERVER_ADDRESS}#${EUREKA_SERVER_ADDRESS}#g\" docker-compose.yml > otter.yml"

        if("${ENV}" == 'test') {
            sh "echo '\n      - DEV_IP=${DEV_IP}' >> otter.yml"
            sh "echo '      - PREFER_IP=false' >> otter.yml"
        }

        withCredentials([usernamePassword(credentialsId: "${CREDENTIAL_ID}", passwordVariable: 'PASSWD', usernameVariable: 'USERNAME')]) {
            def remote = [:]
            remote.name = "${ENV}-otter"
            remote.host = "${TARGET_SERVER}"
            remote.user = USERNAME
            remote.password = PASSWD
            remote.allowAnyHosts = true
            try {
                //发送otter.yml文件到远程目标服务器的Jenkins用户目录下
                sshRemove remote: remote, path: "otter.yml"
                sshPut remote: remote, from: 'otter.yml', into: '.'
                //在远程目标服务器上运行docker-run.sh文件
                sshScript remote: remote,script: "docker-run.sh"
            } catch (error) {
                println error
            }
        }
    }
}