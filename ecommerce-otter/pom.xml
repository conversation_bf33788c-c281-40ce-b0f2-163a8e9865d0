<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.ecommerce</groupId>
        <artifactId>ecommerce-root</artifactId>
        <version>2.1.4-RELEASE</version>
    </parent>
    <artifactId>ecommerce-otter</artifactId>
    <name>ecommerce-otter</name>
    <version>2.1.4-RELEASE</version>
    <description>synchronized data in time</description>

    <properties>
        <docker.deploy.version>${project.version}</docker.deploy.version>
    </properties>
    
    <distributionManagement>
		<repository>
			<id>test</id>
			<name>Department Repository</name>
			<url>${distribution.url}</url>
		</repository>
	</distributionManagement>

	<repositories>
		<repository>
			<id>test</id>
			<name>Department Repository</name>
			<url>${repository.url}</url>
			<snapshots>
				<enabled>true</enabled>
				<updatePolicy>always</updatePolicy>
				<checksumPolicy>warn</checksumPolicy>
			</snapshots>
			<releases>
				<enabled>true</enabled>
				<updatePolicy>always</updatePolicy>
			</releases>
		</repository>
	</repositories>

    <dependencies>
        <!--定时任务和@Slf4j注解日志的依赖-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
		<dependency>
			<groupId>com.ecommerce</groupId>
			<artifactId>common</artifactId>
			<version>2.1.4-RELEASE</version>
		</dependency>
    </dependencies>
    <build>
        <finalName>otter</finalName>
        <resources>
            <resource>
                <filtering>true</filtering>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.*</include>
                </includes>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>docker-maven-plugin</artifactId>
                <version>0.4.13</version>
                <configuration>
                    <dockerHost>http://********:2323</dockerHost>
                    <imageName>********:5000/${project.artifactId}:${docker.deploy.version}</imageName>
                    <baseImage>java:8</baseImage>
                    <registryUrl>http://********:5000</registryUrl>
                    <serverId>docker-registry</serverId>
                    <dockerDirectory>${project.basedir}/target/classes/docker</dockerDirectory>
                    <!--<entryPoint>["java", "-jar", "/${project.build.finalName}.jar"]</entryPoint>-->
                    <resources>
                        <resource>
                            <targetPath>/</targetPath>
                            <directory>${project.build.directory}</directory>
                            <include>${project.build.finalName}.jar</include>
                        </resource>
                    </resources>
                    <forceTags>true</forceTags>
                    <pushImage>true</pushImage>
                </configuration>
            </plugin>
        </plugins>
    </build>
    
    <profiles>
		<profile>
			<id>sonar</id>
			<properties>
				<sonar.host.url>http://***********:9000</sonar.host.url>
			</properties>
		</profile>
		<profile>
			<id>dev</id>
			<activation>
				<activeByDefault>true</activeByDefault>
			</activation>
			<build>
				<resources>
					<resource>
						<directory>build/dev</directory>
						<targetPath>docker</targetPath>
					</resource>
				</resources>
			</build>
			<properties>
				<distribution.url>https://maven.dashuini.cn/repository/ecommerce-snapshot-release/</distribution.url>
				<repository.url>https://maven.dashuini.cn/repository/ecommerce-group/</repository.url>
			</properties>
		</profile>
		<profile>
			<id>test</id>
			<build>
				<resources>
					<resource>
						<directory>build/test</directory>
						<targetPath>docker</targetPath>
					</resource>
				</resources>
			</build>
			<properties>
				<distribution.url>http://********:8081/repository/ecommerce-snapshot-release/</distribution.url>
				<repository.url>http://********:8081/repository/ecommerce-group/</repository.url>
			</properties>
		</profile>
		<profile>
			<id>sit</id>
			<build>
				<resources>
					<resource>
						<directory>build/sit</directory>
						<targetPath>docker</targetPath>
					</resource>
				</resources>
			</build>
			<properties>
				<distribution.url>http://********:8081/repository/ecommerce-snapshot-release/</distribution.url>
				<repository.url>http://********:8081/repository/ecommerce-group/</repository.url>
			</properties>
		</profile>
		<profile>
			<id>uat</id>
			<build>
				<resources>
					<resource>
						<directory>build/uat</directory>
						<targetPath>docker</targetPath>
					</resource>
				</resources>
			</build>
			<properties>
				<distribution.url>http://********:8081/repository/ecommerce-snapshot-release/</distribution.url>
				<repository.url>http://********:8081/repository/ecommerce-group/</repository.url>
			</properties>
		</profile>
		<profile>
            <id>newuat</id>
            <build>
                <resources>
                    <resource>
                        <directory>build/newuat</directory>
                        <targetPath>docker</targetPath>
                    </resource>
                </resources>
            </build>
			<properties>
				<distribution.url>http://********:8081/repository/ecommerce-snapshot-release/</distribution.url>
				<repository.url>http://********:8081/repository/ecommerce-group/</repository.url>
			</properties>
        </profile>
		<profile>
            <id>prod</id>
            <build>
                <resources>
                    <resource>
                        <directory>build/prod</directory>
                        <targetPath>docker</targetPath>
                    </resource>
                </resources>
            </build>
			<properties>
				<distribution.url>http://********:8081/repository/ecommerce-snapshot-release/</distribution.url>
				<repository.url>http://********:8081/repository/ecommerce-group/</repository.url>
			</properties>
        </profile>
	</profiles>

</project>
