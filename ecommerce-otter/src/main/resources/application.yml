logging:
  config: classpath:log/logback-${spring.profiles.active}.xml
  level:
    com.netflix.discovery.DiscoveryClient: WARN
spring:
  profiles:
    active: dev
  application:
    name: otter
  datasource:
    order:
      name: db
      type: com.alibaba.druid.pool.DruidDataSource
      jdbc-url: ${order.mysql.db.url:***********************************************************************************************************}
      username: ${order.mysql.db.username:test}
      password: ${order.mysql.db.password:123456}
      driver-class-name: com.mysql.cj.jdbc.Driver
      minIdle: 5
      maxActive: 100
      initialSize: 10
      maxWait: 60000
      timeBetweenEvictionRunsMillis: 60000
      minEvictableIdleTimeMillis: 300000
      validationQuery: select 'x'
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      poolPreparedStatements: true
      maxPoolPreparedStatementPerConnectionSize: 50
      removeAbandoned: true
      filters: stat
    goods:
      name: db
      type: com.alibaba.druid.pool.DruidDataSource
      jdbc-url: ${goods.mysql.db.url:***************************************************************************************************************}
      username: ${goods.mysql.db.username:test}
      password: ${goods.mysql.db.password:123456}
      driver-class-name: com.mysql.cj.jdbc.Driver
      minIdle: 5
      maxActive: 100
      initialSize: 10
      maxWait: 60000
      timeBetweenEvictionRunsMillis: 60000
      minEvictableIdleTimeMillis: 300000
      validationQuery: select 'x'
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      poolPreparedStatements: true
      maxPoolPreparedStatementPerConnectionSize: 50
      removeAbandoned: true
      filters: stat
    logistics:
      name: db
      type: com.alibaba.druid.pool.DruidDataSource
      jdbc-url: ${logistics.mysql.db.url:*******************************************************************************************************************}
      username: ${logistics.mysql.db.username:test}
      password: ${logistics.mysql.db.password:123456}
      driver-class-name: com.mysql.cj.jdbc.Driver
      minIdle: 5
      maxActive: 100
      initialSize: 10
      maxWait: 60000
      timeBetweenEvictionRunsMillis: 60000
      minEvictableIdleTimeMillis: 300000
      validationQuery: select 'x'
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      poolPreparedStatements: true
      maxPoolPreparedStatementPerConnectionSize: 50
      removeAbandoned: true
      filters: stat
eureka:
  client:
    serviceUrl:
      defaultZone: ${EUREKA_SERVER_ADDRESS:http://dev-euraka.dashuini.cn/eureka/}
#  instance:
#    hostname: ${DEV_IP:${spring.cloud.client.ipAddress}}
#    instance-id: ${DEV_IP:${spring.cloud.client.ipAddress}}:${server.port}
#    prefer-ip-address: ${PREFER_IP:true}
server:
  port: ${otter.server.port:9013}
  context-path: /otterApi

datasubscribe:
  secretid: ${datasubscribe_secretid:AKID6RjqmZuXlxJh4HnwFQaefVBh4GOOqWs7}
  secretkey: ${datasubscribe_secretkey:GNXebdlBcw54ub71tb8V3GwRxED2jQJ8}
  regin: ${datasubscribe_regin:ap-guangzhou}
  guid: ${datasubscribe_guid:dts-channel-7RUCdmALjQ2r7nHa}
  serviceip: ${datasubscribe_serviceip}
  serviceport: ${datasubscribe_serviceport}