#!/usr/bin/env python3
"""
<PERSON>ript to fix SonarQube java:S112 violations by removing 'throws Exception' from Controller methods.
This script processes the CSV file and removes throws Exception from all Controller files.
"""

import csv
import re
import os
from pathlib import Path

def extract_file_path(component_path):
    """Extract the actual file path from the SonarQube component path."""
    # Remove the project prefix "Ecommerce-all-sprint2:"
    if ":" in component_path:
        return component_path.split(":", 1)[1]
    return component_path

def is_controller_file(file_path):
    """Check if the file is a Controller file."""
    return "controller" in file_path.lower() and file_path.endswith(".java")

def read_csv_and_extract_controllers(csv_file_path):
    """Read the CSV file and extract all Controller files that need fixing."""
    controller_files = set()
    
    with open(csv_file_path, 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        for row in reader:
            component = row['Component']
            file_path = extract_file_path(component)
            
            if is_controller_file(file_path):
                controller_files.add(file_path)
    
    return sorted(controller_files)

def fix_throws_exception_in_file(file_path):
    """Remove 'throws Exception' from method signatures in a Java file."""
    if not os.path.exists(file_path):
        print(f"File not found: {file_path}")
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        original_content = content
        
        # Pattern to match method signatures with 'throws Exception'
        # This pattern looks for method signatures ending with 'throws Exception{'
        pattern = r'(\s+public\s+[^{]+?)\s*throws\s+Exception\s*(\{)'
        replacement = r'\1\2'
        
        content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
        
        # Also handle cases where throws Exception is at the end of line before {
        pattern2 = r'(\s+public\s+[^{]+?)\s*throws\s+Exception\s*$'
        def replace_func(match):
            return match.group(1)
        
        lines = content.split('\n')
        modified_lines = []
        
        for i, line in enumerate(lines):
            # Check if this line has throws Exception and the next line starts with {
            if 'throws Exception' in line and i + 1 < len(lines) and lines[i + 1].strip().startswith('{'):
                line = re.sub(r'\s*throws\s+Exception\s*$', '', line)
            elif 'throws Exception{' in line:
                line = line.replace('throws Exception{', '{')
            elif line.strip().endswith('throws Exception{'):
                line = line.replace('throws Exception{', '{')
            
            modified_lines.append(line)
        
        content = '\n'.join(modified_lines)
        
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(content)
            print(f"Fixed: {file_path}")
            return True
        else:
            print(f"No changes needed: {file_path}")
            return False
            
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return False

def main():
    """Main function to process all Controller files."""
    csv_file = "sonar/sonar_issues_java_S112.csv"
    base_path = "."
    
    if not os.path.exists(csv_file):
        print(f"CSV file not found: {csv_file}")
        return
    
    print("Extracting Controller files from CSV...")
    controller_files = read_csv_and_extract_controllers(csv_file)
    
    print(f"Found {len(controller_files)} Controller files to process:")
    for file_path in controller_files:
        print(f"  - {file_path}")
    
    print("\nProcessing Controller files...")
    fixed_count = 0
    
    for file_path in controller_files:
        full_path = os.path.join(base_path, file_path)
        if fix_throws_exception_in_file(full_path):
            fixed_count += 1
    
    print(f"\nProcessing complete!")
    print(f"Fixed {fixed_count} out of {len(controller_files)} Controller files.")
    
    # Also process some non-controller files that are commonly problematic
    print("\nProcessing other important files...")
    other_files = [
        "common/src/main/java/com/ecommerce/common/excel/XssExcel.java",
        "generator/src/main/java/com/ecommerce/plugin/GenerateUtil.java",
        "mq/cmq-topic/src/main/java/com/qcloud/cmq/CMQHttp.java",
        "mq/mq-core/src/main/java/com/ecommerce/mq/core/handler/MessageHandlerFactory.java",
        "mq/mq-core/src/main/java/com/ecommerce/mq/core/serializer/KryoSerializer.java",
        "mq/rabbitmq-sdk/src/main/java/com/ecommerce/rabbitmq/consumer/InitConsumerConfig.java"
    ]
    
    for file_path in other_files:
        full_path = os.path.join(base_path, file_path)
        if os.path.exists(full_path):
            print(f"Processing: {file_path}")
            # For non-controller files, we need different handling
            # These might have legitimate throws Exception that should be replaced with specific exceptions
            # For now, just report them
            print(f"  Note: {file_path} needs manual review")

if __name__ == "__main__":
    main()
