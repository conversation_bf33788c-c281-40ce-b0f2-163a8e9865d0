<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<artifactId>ecommerce-mq</artifactId>
		<groupId>com.ecommerce</groupId>
		<version>2.1.4-RELEASE</version>
	</parent>
	<artifactId>mq-starter</artifactId>
	<packaging>pom</packaging>
	<version>2.1.4-RELEASE</version>
	<name>mq-starter</name>
	<description>Demo project for Spring Boot</description>

  	<modules>
		<module>mq-starter-parent</module>
		<module>kafka-spring-boot-starter</module>
		<module>rabbitmq-spring-boot-starter</module>
	</modules>

	<dependencies>

	</dependencies>
</project>
