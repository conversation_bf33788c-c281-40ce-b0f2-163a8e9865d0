import pdfplumber
from markdownify import markdownify

def pdf_to_markdown(pdf_path, output_path):
    with pdfplumber.open(pdf_path) as pdf:
        text = ""
        for page in pdf.pages:
            text += page.extract_text() + "\n"

        # 转换为 Markdown 格式
        markdown_text = markdownify(text)

        # 保存到文件
        with open(output_path, "w", encoding="utf-8") as f:
            f.write(markdown_text)

# 示例使用
pdf_to_markdown("pdf/Ecommerce-all-sprint2.pdf", "Ecommerce-all-sprint2.pdf.md")
