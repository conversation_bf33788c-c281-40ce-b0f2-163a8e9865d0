<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.3.13</version>
        <relativePath/>
    </parent>

    <groupId>com.ecommerce</groupId>
    <artifactId>ecommerce-root</artifactId>
    <packaging>pom</packaging>
    <version>2.1.4-RELEASE</version>
    <modules>
        <module>common</module>
        <module>eureka</module>
        <module>generator</module>
        <module>config</module>
        <module>ecommerce-otter</module>
        <module>mq</module>
        <module>service</module>
        <module>web</module>
    </modules>

    <name>Ecommerce</name>

    <properties>
        <sonar.sourceEncoding>UTF-8</sonar.sourceEncoding>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>17</java.version>

        <spring-cloud.version>2023.0.3</spring-cloud.version>
        <spring-boot.version>3.3.13</spring-boot.version>


        <bouncycastle.bcprov-jdk18on.version>1.75</bouncycastle.bcprov-jdk18on.version>
        <nimbusds.version>9.37.3</nimbusds.version>

        <xxl-job.version>2.3.0</xxl-job.version>
        <poi.version>4.1.2</poi.version>
        <easeypoi-base.version>4.5.0</easeypoi-base.version>

        <redisson.version>3.24.3</redisson.version>
        <elasticsearch.version>6.8.23</elasticsearch.version>

        <statemachine.version>2.1.3.RELEASE</statemachine.version>

        <jpush-client.version>3.7.6</jpush-client.version>
        <commons-beanutils.version>1.9.4</commons-beanutils.version>
        <commons-collections4.version>4.4</commons-collections4.version>
        <commons-lang3.version>3.14.0</commons-lang3.version>
        <commons-compress.version>1.26.0</commons-compress.version>
        <commons-csv.version>1.10.0</commons-csv.version>
        <opencsv.version>4.6</opencsv.version>
        <easyexcel.version>3.3.3</easyexcel.version>
        <guava.version>33.0.0-jre</guava.version>

        <druid.version>1.2.18</druid.version>

        <pagehelper.version>6.1.0</pagehelper.version>
        <pagehelper-spring-boot-starter.version>2.1.0</pagehelper-spring-boot-starter.version>

        <tk.mybatis-starter.version>4.2.3</tk.mybatis-starter.version>
        <mybatis-spring-boot-starter.version>3.0.3</mybatis-spring-boot-starter.version>
        <mybatis-plus.version>3.5.5</mybatis-plus.version>
        <mybatis.generator.version>1.3.6</mybatis.generator.version>

        <swagger.version>3.0.0</swagger.version>
        <mq-amqp-client.version>1.0.3</mq-amqp-client.version>

        <fastjson.version>2.0.49</fastjson.version>
        <fastjson2.version>2.0.49</fastjson2.version>

        <mysql-connector-java.version>8.0.29</mysql-connector-java.version>
        <protobuf-java.version>3.19.6</protobuf-java.version>
        <supalle.auto-trim.version>1.0.3</supalle.auto-trim.version>
        <cuisongliu.kaptcha.version>1.3</cuisongliu.kaptcha.version>

        <aliyun-java-sdk-core.version>4.6.1</aliyun-java-sdk-core.version>
        <alipay-sdk-java.version>3.3.49.ALL</alipay-sdk-java.version>

        <zxing.version>3.4.0</zxing.version>
        <cxf.version>3.1.12</cxf.version>
        <dom4j.version>2.1.4</dom4j.version>
        <shedlock.version>5.12.0</shedlock.version>
        <hutool-all.version>5.8.25</hutool-all.version>
    </properties>


    <dependencies>
        <dependency>
            <groupId>jakarta.mail</groupId>
            <artifactId>jakarta.mail-api</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
    </dependencies>


    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool-all.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
                <version>2.5.0</version> <!-- 支持 Spring Boot 3.3 的最新稳定版 -->
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.session</groupId>
                        <artifactId>spring-session-data-redis</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.cuisongliu</groupId>
                <artifactId>kaptcha-spring-boot-autoconfigure</artifactId>
                <version>1.3</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${mybatis-spring-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql-connector-java.version}</version>
            </dependency>

            <!-- 分页插件 -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper</artifactId>
                <version>${pagehelper.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>

            <dependency>
                <groupId>tk.mybatis</groupId>
                <artifactId>mapper-spring-boot-starter</artifactId>
                <version>${tk.mybatis-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>tk.mybatis</groupId>
                <artifactId>mapper</artifactId>
                <version>${tk.mybatis-starter.version}</version>
            </dependency>


            <dependency>
                <groupId>javax.persistence</groupId>
                <artifactId>persistence-api</artifactId>
                <version>1.0.2</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-collections</groupId>
                <artifactId>commons-collections</artifactId>
                <version>3.2.2</version>
            </dependency>
            <dependency>
                <groupId>commons-beanutils</groupId>
                <artifactId>commons-beanutils</artifactId>
                <version>${commons-beanutils.version}</version>
            </dependency>

            <!--poi-->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-scratchpad</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <!-- xxl -->
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl-job.version}</version>
            </dependency>

            <!-- 开源多维码生成工具 -->
            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>core</artifactId>
                <version>${zxing.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>javase</artifactId>
                <version>${zxing.version}</version>
            </dependency>


            <!--自定义jar包-->
            <dependency>
                <groupId>com.ecommerce</groupId>
                <artifactId>service-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ecommerce</groupId>
                <artifactId>common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ecommerce</groupId>
                <artifactId>web-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ecommerce</groupId>
                <artifactId>rabbitmq-spring-boot-starter</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ecommerce</groupId>
                <artifactId>kafka-spring-boot-starter</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.ecommerce</groupId>
                <artifactId>base-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ecommerce</groupId>
                <artifactId>member-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ecommerce</groupId>
                <artifactId>goods-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ecommerce</groupId>
                <artifactId>order-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ecommerce</groupId>
                <artifactId>information-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ecommerce</groupId>
                <artifactId>logistics-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ecommerce</groupId>
                <artifactId>trace-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ecommerce</groupId>
                <artifactId>open-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ecommerce</groupId>
                <artifactId>pay-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ecommerce</groupId>
                <artifactId>price-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ecommerce</groupId>
                <artifactId>report-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ecommerce</groupId>
                <artifactId>storage-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ecommerce</groupId>
                <artifactId>ecommerce-otter</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ecommerce</groupId>
                <artifactId>mq-core</artifactId>
                <version>${project.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>
</project>
