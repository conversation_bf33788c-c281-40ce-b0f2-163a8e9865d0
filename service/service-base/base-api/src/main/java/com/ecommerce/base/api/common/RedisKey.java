package com.ecommerce.base.api.common;

/**
 * <AUTHOR>
 * @Date: 09/08/2018 18:10
 * @DESCRIPTION:
 */
public class RedisKey {

    public static final String VALUE_SET_CACHE = "base:cache:value:";

    public static final String ATTACHMENT_CATEGORY_CACHE = "base:cache:attachment:category";

    public static final String WAREHOUSE_SERVICE_AREA = "base:cache:warehouse:service:area";

    public static final String TRANSPORT_ROUTE = "base:cache:transport:route";

    //如果要使用redis缓存，请把key添加在这里


    public static String join(String prefix, Object... args) {
        StringBuilder sb = new StringBuilder(prefix);
        for (Object arg : args) {
            sb.append(":").append(arg);
        }
        return sb.toString();
    }
    private RedisKey() {
    }

}
