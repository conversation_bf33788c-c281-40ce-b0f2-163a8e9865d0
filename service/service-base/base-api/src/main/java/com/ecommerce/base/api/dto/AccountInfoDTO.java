package com.ecommerce.base.api.dto;

import com.ecommerce.base.api.dto.permission.PermissionDetailDTO;
import com.ecommerce.base.api.dto.role.RoleDTO;
import com.ecommerce.base.api.dto.saleregion.SaleRegionDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class AccountInfoDTO {

	/**
	 * 账号id
	 */
	@Schema(description = "账号id")
	private String accountId;

	/**
	 * 角色id的List
	 */
	@Schema(description = "角色id的List")
	private List<Integer> roleIdList;

	/**
	 * 销售区域id的List
	 */
	@Schema(description = "销售区域id的List")
	private List<String> salesRegionIdList;

	/**
	 * 许可权限DTO的List
	 */
	@Deprecated(since = "2.1.4-RELEASE")
	@Schema(description = "许可权限DTO的List")
	private List<PermissionDetailDTO> permissionDTOList;

	/**
	 * 操作人id
	 */
	@Schema(description = "操作人id")
	private String operatorId;

	/**
	 * 角色DTO的List
	 */
	@Schema(description = "角色DTO的List")
	private List<RoleDTO> roleList;

	/**
	 * 销售区域的List
	 */
	@Schema(description = "销售区域的List")
	private List<SaleRegionDTO> saleRegionList;

}
