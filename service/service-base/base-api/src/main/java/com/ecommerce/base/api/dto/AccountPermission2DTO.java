package com.ecommerce.base.api.dto;

import com.ecommerce.base.api.dto.authRes.VUEMenuDTO;
import com.ecommerce.base.api.dto.permission.FunctionButtonDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.ArrayList;
import java.util.List;


public class AccountPermission2DTO {

	@Schema(description = "平台类型")
	private String projectType;

	/**
	 * 菜单树DTO的List
	 */
	@Schema(description = "菜单树DTO的List")
	private List<VUEMenuDTO> menuTreeList;

	/**
	 * 功能按钮DTO的List
	 */
	@Schema(description = "功能按钮DTO的List")
	private List<FunctionButtonDTO> buttonList;

	public String getProjectType() {
		return projectType;
	}

	public List<VUEMenuDTO> getMenuTreeList() {
		return menuTreeList;
	}

	public List<FunctionButtonDTO> getButtonList() {
		return buttonList;
	}

	public void setProjectType(String projectType) {
		this.projectType = projectType;
	}

	public void setMenuTreeList(List<VUEMenuDTO> menuTreeList) {
		this.menuTreeList = new ArrayList<>(menuTreeList);
	}

	public void setButtonList(List<FunctionButtonDTO> buttonList) {
		this.buttonList = new ArrayList<>(buttonList);
	}
}
