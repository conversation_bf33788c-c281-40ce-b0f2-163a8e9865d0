package com.ecommerce.base.api.dto;

import com.ecommerce.base.api.dto.authRes.VUEMenuDTO;
import com.ecommerce.base.api.dto.authRes.VUEPageCrumbsDTO;
import com.ecommerce.base.api.dto.authRes.VUEPageDTO;
import com.ecommerce.base.api.dto.permission.FunctionButtonDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class AccountPermissionDTO implements Serializable {

	@Schema(description = "平台类型")
	private String projectType;

	/**
	 * 菜单树DTO的List
	 */
	@Schema(description = "菜单树DTO的List")
	private List<VUEMenuDTO> menuTreeList;

	/**
	 * 页面权限的list
	 */
	@Schema(description = "页面DTO的List")
	private Set<VUEPageDTO> pageList;
	/**
	 * 功能按钮DTO的List
	 */
	@Schema(description = "功能按钮DTO的List")
	private List<FunctionButtonDTO> buttonList;

	@Schema(description = "平台对应的面包屑集合")
	private Map<String,List<VUEPageCrumbsDTO>> crumbsMap;

	@Schema(description = "存放菜单上挂的页面的code(platform端专用)")
	private List<String> menuPageCodeList;

	public String getProjectType() {
		return projectType;
	}

	public List<VUEMenuDTO> getMenuTreeList() {
		return menuTreeList;
	}

	public Set<VUEPageDTO> getPageList() {
		return pageList;
	}

	public List<FunctionButtonDTO> getButtonList() {
		return buttonList;
	}

	public Map<String, List<VUEPageCrumbsDTO>> getCrumbsMap() {
		return crumbsMap;
	}

	public List<String> getMenuPageCodeList() {
		return menuPageCodeList;
	}

	public void setProjectType(String projectType) {
		this.projectType = projectType;
	}

	public void setMenuTreeList(List<VUEMenuDTO> menuTreeList) {
		this.menuTreeList = new ArrayList<>(menuTreeList);
	}

	public void setPageList(Set<VUEPageDTO> pageList) {
		this.pageList = new HashSet<>(pageList);
	}

	public void setButtonList(List<FunctionButtonDTO> buttonList) {
		this.buttonList = new ArrayList<>(buttonList);
	}

	public void setCrumbsMap(Map<String, List<VUEPageCrumbsDTO>> crumbsMap) {
		this.crumbsMap = new HashMap<>(crumbsMap);
	}

	public void setMenuPageCodeList(List<String> menuPageCodeList) {
		this.menuPageCodeList = new ArrayList<>(menuPageCodeList);
	}
}