package com.ecommerce.base.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2018-09-29 16:53
 * @description:
 **/

public class AccountRoleIdListDTO {
    /**
     * 账号id的List
     */
    @Schema(description = "账号id的List")
    private List<String> accountIdList;

    /**
     * 角色id的List
     */
    @Schema(description = "角色id的List")
    private List<Integer> roleIdList;

    public List<String> getAccountIdList() {
        return accountIdList;
    }

    public List<Integer> getRoleIdList() {
        return roleIdList;
    }

    public void setAccountIdList(List<String> accountIdList) {
        this.accountIdList = new ArrayList<>(accountIdList);
    }

    public void setRoleIdList(List<Integer> roleIdList) {
        this.roleIdList = new ArrayList<>(roleIdList);
    }
}
