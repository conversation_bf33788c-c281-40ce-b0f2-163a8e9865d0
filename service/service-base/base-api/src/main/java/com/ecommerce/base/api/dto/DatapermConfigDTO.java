package com.ecommerce.base.api.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Deprecated(since = "2.1.4-RELEASE")
@Data
public class DatapermConfigDTO {
	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private String permconfigId;
	/**
	 * 账号id
	 */
	@ApiModelProperty("账号id")
	private String accountId;
	/**
	 * 选项名称
	 */
	@ApiModelProperty("选项名称")
	private String selectName;
	/**
	 * 选项值
	 */
	@ApiModelProperty("选项值")
	private String selectValue;
	/**
	 * 数据权限code
	 */
	@ApiModelProperty("数据权限code")
	private String permCode;
	/**
	 * 是否选择
	 */
	@ApiModelProperty("是否选择")
	@JsonInclude(content=Include.NON_NULL)
	private String selected;
}
