package com.ecommerce.base.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Deprecated(since = "2.1.4-RELEASE")
@Data
public class DatapermConfigOptionDTO implements Serializable {

	private static final long serialVersionUID = 3205130568646657067L;
	/**
	 * 名称
	 */
	@ApiModelProperty("名称")
	private String name;
	/**
	 * 值
	 */
	@ApiModelProperty("值")
	private String value;
	/**
	 * 是否选择
	 */
	@ApiModelProperty("是否选择")
	private Boolean selected;
}
