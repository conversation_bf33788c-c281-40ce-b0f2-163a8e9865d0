package com.ecommerce.base.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Deprecated(since = "2.1.4-RELEASE")
@Data
public class DatapermConfigQueryDTO {
	/**
	 * 会员id
	 */
	@ApiModelProperty("会员id")
	private String memberId;
	/**
	 * 数据权限编码code
	 */
	@ApiModelProperty("数据权限编码code")
	private String datapermCode;
	/**
	 * 账号id
	 */
	@ApiModelProperty("账号id")
	private String accountId;
}
