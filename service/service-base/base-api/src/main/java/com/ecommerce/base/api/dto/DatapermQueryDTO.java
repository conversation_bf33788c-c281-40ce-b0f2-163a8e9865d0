
package com.ecommerce.base.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class DatapermQueryDTO {

	/**
	 * 数据权限名称
	 */
	@Schema(description = "数据权限名称")
	private String permName;

	/**
	 * 数据权限编码code
	 */
	@Schema(description = "数据权限编码code")
	private String permCode;

	/**
	 * 账号id
	 */
	@Schema(description = "账号id")
	private String accountId;

	/**
	 * 是否全部显示
	 */
	@Schema(description = "是否全部显示")
	private Boolean showAll=true;

    @Schema(description = "当前页数")
	private Integer pageNum=1;
    @Schema(description = "分页大小")
	private Integer pageSize=10;
}
