package com.ecommerce.base.api.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


@Data
@EqualsAndHashCode(callSuper = true)
public class DriverLicenceBackDTO extends ImgParseDTO {

	/**
	 * 档案编号
	 */
	@Schema(description = "档案编号")
	private String archiveNo;

	@Schema(description = "华为云新增（如：实习期至2005年08月26日）")
	private String licenceRecord;
}
