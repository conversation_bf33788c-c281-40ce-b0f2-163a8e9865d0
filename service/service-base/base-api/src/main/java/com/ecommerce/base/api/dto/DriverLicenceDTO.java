package com.ecommerce.base.api.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotBlank;


@Data
@EqualsAndHashCode(callSuper = true)
public class DriverLicenceDTO extends ImgParseDTO {
	public static final String DRIVER_LICENCE_ID = "证号";
	public static final String DRIVER_LICENCE_NAME = "姓名";
	public static final String DRIVER_LICENCE_SEX = "性别";
	public static final String DRIVER_LICENCE_NATIONALITY = "国籍";
	public static final String DRIVER_LICENCE_ADDRESS = "住址";
	public static final String DRIVER_LICENCE_BIRTHDATE = "出生日期";
	public static final String DRIVER_LICENCE_FIRSTISSUEDATE = "领证日期";
	public static final String DRIVER_LICENCE_TYPE = "准驾车型";
	public static final String DRIVER_LICENCE_BEGINDATE = "起始日期";
	public static final String DRIVER_LICENCE_VALIDITYDATE = "有效日期";
	public static final String DRIVER_LICENCE_REDCHAPTER = "红章";

	/**
     *  证号
     */
	@Schema(description = "证号")
	@NotBlank
	private String id;
	
	/**
     *  姓名
     */
	@Schema(description = "姓名")
	@NotBlank
	private String name;
	
	/**
     *  性别
     */
	@Schema(description = "性别")
	@NotBlank
	private String sex;
	
	/**
     *  国籍
     */
	@Schema(description = "国籍")
	@NotBlank
	private String nationality;
	
	/**
     *  住址
     */
	@Schema(description = "住址")
	@NotBlank
	private String address;
	
	/**
     *  出生日期
     */
	@Schema(description = "出生日期")
	@NotBlank
	private String birthDate;
	
	/**
     *  领证日期
     */
	@Schema(description = "领证日期")
	@NotBlank
	private String firstIssueDate;
	
	/**
     *  准驾车型
     */
	@Schema(description = "准驾车型")
	@NotBlank
	private String type;
	
	/**
     *  起始日期
     */
	@Schema(description = "起始日期")
	@NotBlank
	private String beginDate;
	/**
     *  有效日期
     */
	@Schema(description = "有效日期")
	@NotBlank
	private String validityDate;
	
	
	 /**
     *  红章
     */
	 @Schema(description = "红章")
	@NotBlank
	private String redChapter;
}
