package com.ecommerce.base.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class DrivingLicenceBackDTO extends ImgParseDTO {

	/**
	 * 核定载人数
	 */
	@Schema(description = "核定载人数")
	private String appprovedPassengerCapacity;
	
	/**
	 * 核定载质量
	 */
	@Schema(description = "核定载质量")
	private String approvedLoad;
	
	/**
	 * 档案编号
	 */
	@Schema(description = "档案编号")
	private String fileNo;
	
	/**
	 * 总质量
	 */
	@Schema(description = "总质量")
	private String grossMass;
	
	/**
	 * 检验记录
	 */
	@Schema(description = "检验记录")
	private String inspectionRecord;
	
	/**
	 * 外廓尺寸
	 */
	@Schema(description = "外廓尺寸")
	private String overallDimension;
	
	/**
	 * 准牵引总质量
	 */
	@Schema(description = "准牵引总质量")
	private String tractionMass;
	
	/**
	 * 整备质量
	 */
	@Schema(description = "整备质量")
	private String unladenMass;
	
	/**
	 * 车牌号码
	 */
	@Schema(description = "车牌号码")
	private String carId;

	//以下2个字段为华为云新增 https://support.huaweicloud.com/api-ocr/ocr_03_0034.html
	@Schema(description = "备注")
	private String remarks;
	@Schema(description = "条码号")
	private String codeNumber;
}
