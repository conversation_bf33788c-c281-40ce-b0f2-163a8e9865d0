package com.ecommerce.base.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotBlank;

@Data
@EqualsAndHashCode(callSuper = true)
public class DrivingLicenceDTO extends ImgParseDTO {
    public static final String CAR_TYPE = "车辆类型";
    public static final String CAR_ID = "车牌号码";
    public static final String CAR_NAME = "所有人";
    public static final String CAR_ADDRESS = "住址";
    public static final String CAR_MODEL = "品牌型号";
    public static final String CAR_USE_CHARACTER = "使用性质";
    public static final String CAR_VIN = "识别代码";
    public static final String CAR_ENGINE_NUMBER = "发动机号";
    public static final String CAR_REGISTER_DATE = "注册日期";
    public static final String CAR_ISSUE_DATE = "发证日期";
    public static final String CAR_REDCHAPTER = "红章";

    /**
     * 号牌号码
     */
    @Schema(description = "号牌号码")
    @NotBlank
    private String carId;

    /**
     * 车辆类型
     */
    @Schema(description = "车辆类型")
    @NotBlank
    private String carType;

    /**
     * 所有人
     */
    @Schema(description = "所有人")
    @NotBlank
    private String name;

    /**
     * 住址
     */
    @Schema(description = "住址")
    @NotBlank
    private String address;

    /**
     * 使用性质
     */
    @Schema(description = "使用性质")
    @NotBlank
    private String useCharacter;

    /**
     * 品牌型号
     */
    @Schema(description = "品牌型号")
    @NotBlank
    private String model;


    /**
     * 车辆识别代号
     */
    @Schema(description = "车辆识别代号")
    @NotBlank
    private String vin;

    /**
     * 发动机号码
     */
    @Schema(description = "发动机号码")
    @NotBlank
    private String engineNumber;

    /**
     * 注册日期
     */
    @Schema(description = "注册日期")
    @NotBlank
    private String registerDate;

    /**
     * 发证日期
     */
    @Schema(description = "发证日期")
    @NotBlank
    private String issueDate;

    /**
     * 红章
     */
    @Schema(description = "红章")
    @NotBlank
    private String redChapter;


}
