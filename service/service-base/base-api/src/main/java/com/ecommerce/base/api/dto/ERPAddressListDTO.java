package com.ecommerce.base.api.dto;

import lombok.Data;

/**
 *
 * <AUTHOR>
 * Date: Create in 下午4:34 20/6/11
 */
@Data
public class ERPAddressListDTO {
    /**
     * 电商收货地址ID
     */
    private String id;

    /**
     * 归属卖家ID
     */
    private String sellerId;

    /**
     * erp卸货点编码
     */
    private String erpUnloadAddressId;

    /**
     * 地址别名
     */
    private String alias;

    /**
     * 省份名称
     */
    private String provinceName;
    /**
     * 省份编码
     */
    private String provinceCode;
    /**
     * 城市名称
     */
    private String cityName;
    /**
     * 城市编码
     */
    private String cityCode;
    /**
     * 区域名称
     */
    private String districtName;
    /**
     * 区域编码
     */
    private String districtCode;
    /**
     * 街道名称
     */
    private String streetName;
    /**
     * 详细地址
     */
    private String address;
    /**
     * 收货人姓名
     */
    private String consigneeName;
    /**
     * 收货人电话
     */
    private String mobilePhone;
    /**
     * 地址坐标
     */
    private String coordinate;
    /**
     * 卸货点类型 00配送，01自提，02混合
     */
    private String type;
}
