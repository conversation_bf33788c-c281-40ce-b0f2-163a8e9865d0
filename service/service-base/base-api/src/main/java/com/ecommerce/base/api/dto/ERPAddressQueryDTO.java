package com.ecommerce.base.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * Date: Create in 下午4:34 20/6/11
 */
@Data
@Schema(name = "ERP地址查询对象")
public class ERPAddressQueryDTO {
    /**
     * 查询卖家ID
     */
    @Schema(description = "卖家ID")
    private String sellerId;

    /**
     * 查询用户ID
     */
    @Schema(description = "用户Id")
    private String userId;

    /**
     * 合同ID
     */
    @Schema(description = "合同ID")
    private String contractId;

    /**
     * 省份名称
     */
    @Schema(description = "省份名称")
    private String provinceName;

    /**
     * 省份编码
     */
    @Schema(description = "省份编码")
    private String provinceCode;

    /**
     * 城市名称
     */
    @Schema(description = "城市名称")
    private String cityName;

    /**
     * 城市编码
     */
    @Schema(description = "城市编码")
    private String cityCode;

    /**
     * 区域名称
     */
    @Schema(description = "区域名称")
    private String districtName;

    /**
     * 区域编码
     */
    @Schema(description = "区域编码")
    private String districtCode;

    /**
     * 街道名称
     */
    @Schema(description = "街道名称")
    private String streetName;

    /**
     * 街道编码
     */
    @Schema(description = "街道编码")
    private String streetCode;


    /**
     * 地址别名搜索关键字
     */
    @Schema(description = "地址别名搜索关键字")
    private String searchKey;

    /**
     * 配送方式
     */
    @Schema(description = "配送方式")
    private String deliveryWay;
}
