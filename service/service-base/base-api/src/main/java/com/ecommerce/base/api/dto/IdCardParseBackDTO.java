package com.ecommerce.base.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotBlank;

@Data
@EqualsAndHashCode(callSuper = true)
public class IdCardParseBackDTO extends ImgParseDTO {

    /**
     * 发证机关
     */
    @Schema(description = "发证机关")
    @NotBlank
    private String issued;

    /**
     * 有效期
     */
    @Schema(description = "有效期")
    @NotBlank
    private String validityDate;

    /**
     * 开始日期
     */
    @Schema(description = "开始日期")
    private String startDate;

    /**
     * 结束日期
     */
    @Schema(description = "结束日期")
    private String endDate;

}
