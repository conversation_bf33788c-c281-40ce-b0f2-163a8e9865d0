package com.ecommerce.base.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotBlank;


@Data
@EqualsAndHashCode(callSuper = true)
public class IdCardParseFrontDTO extends ImgParseDTO {

    /**
     * 姓名
     */
    @Schema(description = "姓名")
    @NotBlank
    private String name;

    /**
     * 性别
     */
    @Schema(description = "性别")
    @NotBlank
    private String sex;

    /**
     * 民族
     */
    @Schema(description = "民族")
    @NotBlank
    private String nation;

    /**
     * 生日
     */
    @Schema(description = "生日")
    @NotBlank
    private String birthDate;

    /**
     * 住址
     */
    @Schema(description = "住址")
    @NotBlank
    private String address;


    /**
     * 身份证号码
     */
    @Schema(description = "身份证号码")
    @NotBlank
    private String cardId;
}
