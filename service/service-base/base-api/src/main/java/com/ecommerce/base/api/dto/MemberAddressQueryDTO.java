package com.ecommerce.base.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * Date: Create in 下午5:24 20/6/11
 */
@Data
public class MemberAddressQueryDTO {
    /**
     * 会员id
     */
    @Schema(description = "会员id")
    private String memberId;

    /**
     * 卖家ID
     */
    @Schema(description = "卖家ID")
    private String sellerId;

    /**
     * 配送方式
     */
    @Schema(description = "配送方式")
    private String deliveryWay;

    /**
     * 是否需要流量监控 1:是 2:否
     */
    @Schema(description = "是否需要流量监控")
    private String flowMonitor;

}
