

package com.ecommerce.base.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Data
public class MessageConfigAndRoleDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = -3187806224107595919L;

    @Schema(description = "消息配置DTO的List")
    private List<MessageConfigDTO> configList;

    @Schema(description = "消息角色List")
    private List<MessageRoleDTO> messageRoleList;
}
