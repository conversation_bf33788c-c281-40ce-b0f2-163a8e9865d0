package com.ecommerce.base.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date: 18/08/2018 17:00
 * @DESCRIPTION:
 */
@Schema(name = "消息配置创建")
@Data
public class MessageConfigCreateDTO {
    /**
     * 名称
     */
    @Schema(description = "名称",required = true)
    @NotBlank
    private String name;
    /**
     * 引用code
     */
    @Schema(description = "引用code",required = true)
    @NotBlank
    private String code;

    /**
     * 类型,0-短信，1-站内信
     */
    @Schema(description = "类型,0-短信，1-站内信 2-app",required = true)
    @NotNull
    private Integer type;

    /**
     * 是否可以关闭
     */
    @Schema(description = "是否可以关闭")
    private Boolean canclose = Boolean.FALSE;

    /**
     * 简介
     */
    @Schema(description = "简介")
    private String info;
    /**
     * 备注说明
     */
    @Schema(description = "备注说明")
    private String remark;

    /**
     * 推送消息-通知栏提示文字
     */
    @Schema(description = "推送消息-通知栏提示文字")
    private String ticker;
    /**
     * 推送消息-标题
     */
    @Schema(description = "推送消息-标题")
    private String title;

    /**
     * 模板
     */
    @Schema(description = "模板",required = true)
    @NotBlank
    private String template;
    /**
     * 状态
     */
    @Schema(description = "状态",required = true)
    private Boolean status = Boolean.TRUE;

    /**
     * 创建用户
     */
    @Schema(description = "创建用户",required = true)
    private String createUser;

    /**
     * 第三方短信平台短信模板id
     */
    @Schema(description = "第三方短信平台短信模板id")
    private String smsTemplateId;
}
