

package com.ecommerce.base.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date: 14/08/2018 22:43
 * @DESCRIPTION:
 */
@Schema(name = "消息模板配置")
@Data
public class MessageConfigDTO implements Serializable {

    @Schema(description = "模板类型-短信")
    public static final int MESSAGE_TYPE_SMS = 0;
    @Schema(description = "模板类型-站内信")
    public static final int MESSAGE_TYPE_INNER = 1;
    @Schema(description = "模板类型-app推送")
    public static final int MESSAGE_TYPE_APP = 2;

    @Schema(description = "模板id")
    private String id;
    /**
     * 名称
     */
    @Schema(description = "模板名称")
    private String name;
    /**
     * 引用code
     */
    @Schema(description = "引用code，与MessageConfigCodeEnum code保持一致")
    private String code;

    /**
     * 类型
     */
    @Schema(description = "类型")
    private Integer type;

    /**
     * 是否可以关闭
     */
    @Schema(description = "是否可以关闭")
    private Boolean canclose;

    @Schema(description = "会员消息模板查询则返回该属性 1 开 0 关")
    private Boolean openFlg;
    /**
     * 简介
     */
    @Schema(description = "简介")
    private String info;

    /**
     * 第三方短信模板id
     */
    @Schema(description = "第三方短信平台对应的短信模板id")
    private String smsTemplateId;

    /**
     * 推送消息-通知栏提示文字
     */
    @Schema(description = "推送消息-通知栏提示文字")
    private String ticker;
    /**
     * 推送消息-标题
     */
    @Schema(description = "推送消息-标题")
    private String title;

    /**
     * 模板
     */
    @Schema(description = "模板")
    private String template;
    /**
     * 状态
     */
    @Schema(description = "状态 状态 (0可用  1禁用)")
    private Boolean status;
    /**
     * 备注说明
     */
    @Schema(description = "备注说明")
    private String remark;
    /**
     * 删除标记
     */
    @Schema(description = "删除标记")
    private Boolean delFlg;
    /**
     * 创建用户
     */
    @Schema(description = "创建用户")
    private String createUser;
    /**
     * 更新用户
     */
    @Schema(description = "更新用户")
    private String updateUser;
    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;
    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;
}
