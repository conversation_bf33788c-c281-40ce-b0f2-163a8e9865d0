

package com.ecommerce.base.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 14/08/2018 22:43
 * @DESCRIPTION:
 */
@Schema(name = "消息模板禁用DTO")
@Data
public class MessageConfigDisableMemberDTO implements Serializable {


    @Schema(description = "模板id集合(与allFlag不可同时为空)")
    private List<String> ids;

    @Schema(description = "操作者")
    @NotBlank
    private String operator;

    @Schema(description = "类型")
    private Integer type;
    @Schema(description = "禁用所有的")
    private Boolean allFlag;

    @Schema(description = "会员id")
    @NotBlank
    private String memberId;
}
