package com.ecommerce.base.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date: 15/08/2018 19:23
 * @DESCRIPTION:
 */
@Schema(name = "消息配置查询")
@Data
public class MessageConfigQueryDTO {
    /**
     * 消息配置id
     */
    @Schema(description = "消息配置id")
    private String code;

    /**
     * 名称
     */
    @Schema(description = "名称")
    private String name;

    /**
     * 类型
     */
    @Schema(description = "类型")
    private Integer type;

    /**
     * 是否可以关闭
     */
    @Schema(description = "是否可以关闭")
    private Boolean canclose;
    /**
     * 状态
     */
    @Schema(description = "状态")
    private Boolean status;

    /**
     * 当前页
     */
    @Schema(description = "pageNum")
    private int pageNum = 1;
    /**
     * 每页的数量
     */
    @Schema(description = "pageSize")
    private int pageSize = 20;

}
