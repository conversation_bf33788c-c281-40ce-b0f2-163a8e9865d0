

package com.ecommerce.base.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date: 18/08/2018 17:00
 * @DESCRIPTION:
 */
@Schema(name = "消息配置更新")
@Data
public class MessageConfigUpdateDTO {
    @NotBlank
    @Schema(description = "模板id")
    private String id;
    /**
     * 名称
     */
    @Schema(description = "名称", required = true)
    @NotBlank
    private String name;
    /**
     * 引用code
     */
    @Schema(description = "引用code", required = true)
    @NotBlank
    private String code;

    /**
     * 类型
     */
    @Schema(description = "类型", required = true)
    @NotNull
    private Integer type;

    /**
     * 是否可以关闭
     */
    @Schema(description = "是否可以关闭")
    private Boolean canclose = Boolean.FALSE;

    /**
     * 简介
     */
    @Schema(description = "简介")
    private String info;

    /**
     * 备注说明
     */
    @Schema(description = "备注说明")
    private String remark;

    /**
     * 推送消息-通知栏提示文字
     */
    @Schema(description = "推送消息-通知栏提示文字")
    private String ticker;
    /**
     * 推送消息-标题
     */
    @Schema(description = "推送消息-标题")
    private String title;

    /**
     * 模板
     */
    @Schema(description = "模板", required = true)
    @NotBlank
    private String template;
    /**
     * 状态
     */
    @Schema(description = "状态")
    private Boolean status = Boolean.TRUE;

    /**
     * 修改用户
     */
    @Schema(description = "修改用户")
    private String updateUser;

    /**
     * 第三方短信模板id
     */
    @Schema(description = "第三方短信平台对应的短信模板id")
    private String smsTemplateId;
}
