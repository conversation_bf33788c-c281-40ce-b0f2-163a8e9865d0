package com.ecommerce.base.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class MessageRoleDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 4406148519449976210L;

    @Schema(description = "角色代码")
    private String messageRoleCode;

    @Schema(description = "角色描述")
    private String messageRoleName;

    @Schema(description = "备注说明")
    private String remark;
}
