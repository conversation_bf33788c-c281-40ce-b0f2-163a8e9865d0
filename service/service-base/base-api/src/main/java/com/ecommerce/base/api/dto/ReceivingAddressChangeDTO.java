package com.ecommerce.base.api.dto;

import lombok.Data;

@Data
public class ReceivingAddressChangeDTO
{
    /**
     * 会员id
     */
    private String memberId;

    /**
     * 收货人姓名
     */
    private String consigneeName;

    /**
     * 电话
     */
    private String mobilePhone;

    /**
     * 省份编码
     */
    private String provinceCode;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 区县编码
     */
    private String districtCode;

    /**
     * 街道编码
     */
    private String streetCode;

    /**
     * 地址
     */
    private String address;

    /**
     * 详细地址
     */
    private String addressDetail;

    /**
     * 经纬度
     */
    private String coordinate;

    /**
     * 收货地别名
     */
    private String alias;
}
