package com.ecommerce.base.api.dto;

import com.ecommerce.base.api.dto.wharf.WharfMapInfoDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2018-09-06 14:59
 * @description:
 **/
@Schema(name = "收货地址创建")
@Data
public class ReceivingAddressCreateDTO {

    public static final String PHONE_PATTERN = "^[\\d]{11}$";

    /**
     * 收货人
     */
    @Schema(description = "收货人姓名（不能为空）")
    @Pattern(regexp = "^[A-Za-z0-9\\u4e00-\\u9fa5]{0,16}$",message = "由数字、字母和汉字组成，不能超过16个字")
    @NotBlank
    private String consigneeName;

    /**
     * 省
     */
    @Schema(description = "省份名称（不能为空）")
    @NotBlank(message = "省份名称不能为空")
    private String provinceName;

    /**
     * 省份编码（不能为空）
     */
    @Schema(description = "省份编码（不能为空）")
    @NotBlank
    private String provinceCode;

    /**
     * 市
     */
    @Schema(description = "城市名称（不能为空）")
    @NotBlank(message = "城市名称不能为空")
    private String cityName;

    /**
     * 城市编码（不能为空）
     */
    @Schema(description = "城市编码（不能为空）")
    @NotBlank
    private String cityCode;

    /**
     * 区
     */
    @Schema(description = "区县名称")
    private String districtName;

    /**
     * 区县编码
     */
    @Schema(description = "区县编码")
    private String districtCode;
    /**
     * 街道
     */
    @Schema(description = "街道名称")
    private String streetName;
    /**
     * 街道编码
     */
    @Schema(description = "街道编码")
    private String streetCode;


    /**
     * 地址
     */
    @Schema(description = "地址（不能为空）")
    @Pattern(regexp = "^.{0,150}$",message = "地址长度不能超过150个字符")
    @NotBlank
    private String address;

    /**
     * 详细地址
     */
    @Schema(description = "详细地址（不能为空）")
    @Pattern(regexp = "^.{0,150}$",message = "详细地址长度不能超过150个字符")
    private String addressDetail;

    /**
     * 默认收货地址
     */
    @Schema(description = "默认收货地址")
    private Integer isDefault = 0;

    /**
     * 地址别名
     */
    @Schema(description = "收货地别名")
    private String alias;

    /**
     * 经纬度
     */
    @Schema(description = "经纬度（不能为空）")
    @NotBlank(message = "经纬度不能为空")
    private String coordinate;

    /**
     * 手机号码
     */
    @Schema(description = "手机号码（不能为空）")
    @Pattern(regexp = PHONE_PATTERN,message = "手机号格式不正确")
    @NotBlank
    private String mobilePhone;

    /**
     * 固定电话
     */
    @Schema(description = "固定电话")
    private String phone;

    /**
     * xxx-客户卸货点类型
     */
    @Schema(description = "xxx-客户卸货点类型")
    private Integer unloadType;

    /**
     * 操作用户
     */
    @Schema(description = "操作用户")
    private String operator;

    /**
     * 会员id
     */
    @Schema(description = "会员id")
    private String memberId;

    /**
     * erp地址对象
     */
    @Schema(description = "erp地址对象")
    private ERPAddressListDTO erpAddressListDTO;

    /**
     * 收货地址类型
     */
    @Schema(description = "收货地址类型（不能为空）")
    private Integer type;

    /**
     * 码头id
     */
    @Schema(description = "码头id")
    private String wharfId;

    /**
     * 码头名称
     */
    @Schema(description = "码头名称")
    private String wharfName;

    @Schema(description = "码头映射信息")
    private List<WharfMapInfoDTO> wharfMapInfoDTOList;
}
