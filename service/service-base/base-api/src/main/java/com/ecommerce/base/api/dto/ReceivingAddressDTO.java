package com.ecommerce.base.api.dto;

import com.ecommerce.base.api.dto.wharf.WharfMapListDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ReceivingAddressDTO {
    /**
     *  ID
     */
    @Schema(description = "ID")
    private String id;
    /**
     * 会员id
     */
    @Schema(description = "会员id")
    private String memberId;
    /**
     * 收货人
     */
    @Schema(description = "收货人")
    private String consigneeName;

    /**
     * 省
     */
    @Schema(description = "省份名称")
    private String provinceName;

    /**
     * 省份编码
     */
    @Schema(description = "省份编码")
    private String provinceCode;

    /**
     * 市
     */
    @Schema(description = "城市名称")
    private String cityName;

    /**
     * 城市编码
     */
    @Schema(description = "城市编码")
    private String cityCode;

    /**
     * 区
     */
    @Schema(description = "区县名称")
    private String districtName;

    /**
     * 区县编码
     */
    @Schema(description = "区县编码")
    private String districtCode;

    /**
     * 街道
     */
    @Schema(description = "街道名称")
    private String streetName;

    /**
     * 街道编码
     */
    @Schema(description = "街道编码")
    private String streetCode;

    /**
     * 地址
     */
    @Schema(description = "详细地址")
    private String address;

    /**
     * 详细地址
     */
    @Schema(description = "详细地址")
    private String addressDetail;

    /**
     * 地址别名
     */
    @Schema(description = "收货地别名")
    private String alias;

    /**
     * 经纬度
     */
    @Schema(description = "经纬度")
    private String coordinate;

    /**
     * 手机号码
     */
    @Schema(description = "手机号码")
    private String mobilePhone;

    /**
     * 固定电话
     */
    @Schema(description = "固定电话")
    private String phone;

    @Schema(description = "卸货点地址ID")
    private Integer unloadType;

    @Schema(description = "卸货点地址名称")
    private String unloadTypeName;

    /**
     * 是否默认（1默认）
     */
    @Schema(description = "是否默认（1默认）")
    private Integer isDefault;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createUser;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 修改人
     */
    @Schema(description = "修改人")
    private String updateUser;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    private Date updateTime;

    /**
     * 收货地址类型
     */
    @Schema(description = "收货地址类型（不能为空）")
    private Integer type;

    /**
     * 码头id
     */
    @Schema(description = "码头id")
    private String wharfId;

    /**
     * 码头名称
     */
    @Schema(description = "码头名称")
    private String wharfName;


    @Schema(description = "码头映射信息")
    private List<WharfMapListDTO> wharfMapInfoDTOList;

    @Schema(description = "ERP码头映射信息")
    private List<ERPAddressListDTO> erpAddressListDTOList;
}
