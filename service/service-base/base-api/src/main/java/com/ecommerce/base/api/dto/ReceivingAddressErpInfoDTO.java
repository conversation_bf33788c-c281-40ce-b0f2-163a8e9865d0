package com.ecommerce.base.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 07/05/2019 11:03
 */
@Data
public class ReceivingAddressErpInfoDTO {

    /**
     * ID（查询可能用）
     */
    @Schema(description = "ID")
    private String id;

    /**
     * 收货地id
     */
    @Schema(description = "买家收货地id")
    private String receivingAddressId;

    @Schema(description = "买家收货地")
    private String receivingAddress;

    /**
     * 买家会员id
     */
    @Schema(description = "买家会员id")
    private String memberId;

    @Schema(description = "核对方式")
    private String checkMode;
    /**
     * 卖家会员id
     */
    @Schema(description = "卖家会员id")
    private String sellerMemberId;

    /**
     * 卖家卸货地code
     */
    @Schema(description = "卖家卸货地code")
    private String erpAddressId;

    @Schema(description = "卖家卸货地")
    private String erpAddress;
    /**
     * 开始时间
     */
    @Schema(description = "开始时间")
    private Date beginTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间")
    private Date endTime;

    /**
     * 操作人
     */
    @Schema(description = "操作人")
    private String operatorId;
}
