
package com.ecommerce.base.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 22/05/2019 09:45
 * @DESCRIPTION:
 */
@Data
public class ReceivingAddressErpInfoUpdateDTO {
    /**
     * 收货地id
     */
    @Schema(description = "买家收货地")
    private List<ReceivingAddressErpInfoUpdateItemDTO> list;

    @Schema(description = "卖家ID")
    private String sellerId;
    /**
     * 操作人
     */
    @Schema(description = "操作人")
    private String operatorId;
}
