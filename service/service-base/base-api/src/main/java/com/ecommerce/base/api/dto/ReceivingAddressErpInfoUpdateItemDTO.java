package com.ecommerce.base.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date: 22/05/2019 09:45
 * @DESCRIPTION:
 */
@Data
public class ReceivingAddressErpInfoUpdateItemDTO {
    /**
     * 收货地id
     */
    @Schema(description = "买家收货地id")
    private String receivingAddressId;

    /**
     * 买家会员id
     */
    @Schema(description = "买家会员id")
    private String memberId;

    /**
     * 卖家会员id
     */
    @Schema(description = "卖家会员id")
    private String sellerMemberId;
    /**
     * 卖家卸货地code
     */
    @Schema(description = "卖家卸货地code")
    private String erpAddressId;

    /**
     * 操作人
     */
    @Schema(description = "操作人")
    private String operatorId;
}
