package com.ecommerce.base.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name = "买家收货地址管理")
public class ReceivingAddressMapDTO {
    /**
     * ID
     */
    @Schema(description = "ID")
    private String id;

    /**
     * 地址ID
     */
    @Schema(description = "地址ID")
    private String addressId;

    /**
     * 买家ID
     */
    @Schema(description = "买家ID")
    private String buyerId;

    /**
     * 买家名称
     */
    @Schema(description = "买家名称")
    private String buyerName;

    /**
     * 买家类型 1：普通买家、2：经销商
     */
    @Schema(description = "买家类型 1：普通买家、2：经销商")
    private Boolean buyerType;

    /**
     * 卖家ID
     */
    @Schema(description = "卖家ID")
    private String sellerId;

    /**
     * 卖家名称
     */
    @Schema(description = "卖家名称")
    private String sellerName;

    /**
     * 厂商ID
     */
    @Schema(description = "厂商ID")
    private String firmId;

    /**
     * 厂商名称
     */
    @Schema(description = "厂商名称")
    private String firmName;

    /**
     * 用户的省ID
     */
    @Schema(description = "销售省ID")
    private String provinceCode;

    /**
     * 用户的省名称
     */
    @Schema(description = "用户的省名称")
    private String provinceName;

    /**
     * 用户的市ID
     */
    @Schema(description = "销售市ID")
    private String cityCode;

    /**
     * 用户的市名称
     */
    @Schema(description = "用户的市名称")
    private String cityName;

    /**
     * 用户的区ID
     */
    @Schema(description = "用户的区ID")
    private String areaCode;

    /**
     * 用户的区名称
     */
    @Schema(description = "用户的区名称")
    private String areaName;

    /**
     * 用户的详细地址
     */
    @Schema(description = "用户的详细地址")
    private String address;

    /**
     * 地址状态 10：经销商审核中、20：厂商审核中、30：审核通过
     */
    @Schema(description = "地址状态 10：经销商审核中、20：厂商审核中、30：审核通过")
    private Integer auditStatus;

    /**
     * 销售区域ID
     */
    @Schema(description = "销售区域ID")
    private String saleRegionId;

    private String saleRegionId2;

    /**
     * 销售区域名称
     */
    @Schema(description = "销售区域名称")
    private String saleRegionName;

    /**
     * ERP地址ID
     */
    @Schema(description = "ERP地址ID")
    private String erpAddressId;

    @Schema(description = "ERP码头编号")
    private String erpWharfCode;

    @Schema(description = "ERP码头名称")
    private String erpWharfName;

    @Schema(description = "操作人")
    private String operator;

}
