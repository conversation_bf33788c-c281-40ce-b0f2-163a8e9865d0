package com.ecommerce.base.api.dto;

import java.util.Set;

import lombok.Data;

@Data
public class ReceivingAddressMapQueryDTO {
    private String addressId;
    private String buyerId;
    private String buyerName;
    private String sellerId;
    private String sellerName;
    private String firmId;
    private String firmName;
    private String provinceCode;
    private String cityCode;
    private String areaCode;
    private Integer auditStatus;
    private Set<Integer> auditStatusList;
    /**
     * ERP码头名称，支持模糊搜索
     */
    private String erpWharfName;
    private int pageNum = 1;
    private int pageSize = 20;
    /**
     * 用户地址详情
     */
    private String receiverAddress;
}
