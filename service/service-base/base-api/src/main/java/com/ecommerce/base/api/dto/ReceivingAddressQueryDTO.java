package com.ecommerce.base.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: ReceivingAddressQueryDTO
 * <AUTHOR>
 * @Date: 09/12/2020 10:14
 */
@Data
public class ReceivingAddressQueryDTO {

    @Schema(description = "会员ID")
    private String memberId;

    @Schema(description = "销售区域ID")
    private String saleRegionId;

    @Schema(description = "关键字")
    private String keyWords;

    @Schema(description = "收货地址类型（不能为空）")
    private Integer type;

    @Schema(description = "合同ID")
    private String contractId;

    @Schema(description = "关购物车ID")
    private String cartId;
}
