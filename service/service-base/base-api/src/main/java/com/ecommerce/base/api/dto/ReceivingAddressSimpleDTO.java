package com.ecommerce.base.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class ReceivingAddressSimpleDTO {

    /**
     *  ID
     */
    @Schema(description = "ID")
    private String id;

    /**
     * 收货人
     */
    @Schema(description = "收货人")
    private String consigneeName;

    /**
     * 省
     */
    @Schema(description = "省份名称")
    private String provinceName;

    /**
     * 市
     */
    @Schema(description = "城市名称")
    private String cityName;

    /**
     * 区
     */
    @Schema(description = "区县名称")
    private String districtName;

    /**
     * 街道
     */
    @Schema(description = "街道名称")
    private String streetName;

    /**
     * 地址
     */
    @Schema(description = "详细地址")
    private String address;

    /**
     * 地址备注
     */
    @Schema(description = "地址备注")
    private String alias;
}
