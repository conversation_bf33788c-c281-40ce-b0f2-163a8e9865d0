package com.ecommerce.base.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * ReferenceUrl DTO
 *
 * <AUTHOR>
 */
@Schema(name = "ReferenceUrl")
@Data
public class ReferenceUrlDTO {

    /**
     * url
     */
    @Schema(description = "url")
    private String url;

    /**
     * projectName
     */
    @Schema(description = "projectName")
    private String projectName;

    /**
     * referenceLocation
     */
    @Schema(description = "referenceLocation")
    private String referenceLocation;
}
