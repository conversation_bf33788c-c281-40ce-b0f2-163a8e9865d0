package com.ecommerce.base.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * ReferenceUrl 查询 DTO
 * <AUTHOR>
 */
@Schema(name = "调用链查询")
@Data
public class ReferenceUrlQueryDTO {

    /**
     * url
     */
    @Schema(description = "url")
    private String url;

    /**
     * projectName
     */
    @Schema(description = "projectName")
    private String projectName;

    /**
     * 当前页
     */
    @Schema(description = "pageNum")
    private int pageNum = 1;

    /**
     * 每页的数量
     */
    @Schema(description = "pageSize")
    private int pageSize = 10;
}
