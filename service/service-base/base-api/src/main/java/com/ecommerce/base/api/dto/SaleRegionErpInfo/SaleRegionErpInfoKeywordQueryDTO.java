

package com.ecommerce.base.api.dto.SaleRegionErpInfo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Date: 09/04/2019 09:19
 * @DESCRIPTION:
 */
@Data
public class SaleRegionErpInfoKeywordQueryDTO {

    @NotBlank
    @Schema(description = "会员id")
    private String memberId;

    @NotBlank
    @Schema(description = "关键字")
    private String keyword;

    @NotBlank
    @Schema(description = "ERP销售大区编码")
    private String saleRegionCode;
}
