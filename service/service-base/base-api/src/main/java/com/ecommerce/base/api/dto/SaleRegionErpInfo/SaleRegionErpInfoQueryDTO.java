
package com.ecommerce.base.api.dto.SaleRegionErpInfo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name = "销售区域erp信息")
public class SaleRegionErpInfoQueryDTO {

    @Schema(description = "会员id eq")
    private String memberId;
    /**
     * 销售组织编码(销售区域id)
     */
    @Schema(description = "销售组织编码(销售区域id) eq")
    private String orgCode;

    @Schema(description = "销售组织名称(销售区域名称) like")
    private String orgName;

    @Schema(description = "销售组织说明 like")
    private String orgDes;

    @Schema(description = "父组织编码 eq")
    private String parentCode;

    @Schema(description = "当前页数")
    private Integer pageNum = 1;
    @Schema(description = "分页大小")
    private Integer pageSize = 20;
}
