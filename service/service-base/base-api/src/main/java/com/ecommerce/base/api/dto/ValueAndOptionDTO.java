package com.ecommerce.base.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2018-08-20 10:29
 * @description:
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class ValueAndOptionDTO extends ValueSetDTO {
    /**
     * 删除的ids
     */
    @Schema(description = "删除的ids")
    private List<String> deleteIds;

    /**
     * 值集项集合
     */
    @Schema(description = "值集项集合")
    List<ValueSetOptionDTO> list;

}
