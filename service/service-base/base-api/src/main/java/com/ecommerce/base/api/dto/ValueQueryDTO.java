package com.ecommerce.base.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date: 2018-08-17 17:21
 * @description:
 **/
@Data
public class ValueQueryDTO {
    /**
     * 属性
     */
    @Schema(description = "属性")
    private String attribute;
    /**
     * name
     */
    @Schema(description = "name")
    private String name;
    /**
     * 引用代码
     */
    @Schema(description = "引用代码")
    private String referenceCode;
    /**
     * 会员id
     */
    @Schema(description = "会员id")
    private String memberId;

    /**
     * pageNum
     */
    @Schema(description = "pageNum")
    private Integer pageNum = 1;

    /**
     * pageSize
     */
    @Schema(description = "pageSize")
    private Integer pageSize = 10;


}
