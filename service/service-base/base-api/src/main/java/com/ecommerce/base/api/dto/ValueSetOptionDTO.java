package com.ecommerce.base.api.dto;

import com.ecommerce.base.api.validation.IllegalCharacter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class ValueSetOptionDTO {

    /**
     * optionId
     */
    @Schema(description = "optionId")
    private String optionId;

    /**
     * 值集项的id
     */
    @Schema(description = "值集项的id")
    private String id;

    /**
     * key
     */
    @Schema(description = "key")
    @IllegalCharacter
    private String optionKey;

    /**
     * value
     */
    @Schema(description = "value")
    @IllegalCharacter
    private String optionValue;

    /**
     * 值集简介
     */
    @Schema(description = "值集简介")
    private String optionInfo;

    /**
     * 顺序
     */
    @Schema(description = "顺序")
    private Integer optionOrder;

    /**
     * 父选项id
     */
    @Schema(description = "父选项id")
    private String parentId;
    /**
     * 引用代码(仅用于查询返回)
     */
    @Schema(description = "引用代码(仅用于查询返回)")
    private String referenceCode;

}
