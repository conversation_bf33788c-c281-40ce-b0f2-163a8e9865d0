package com.ecommerce.base.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.LinkedList;
import java.util.List;

/**
 *  值集树
 *
 * <AUTHOR>
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class ValueSetTreeDTO extends ValueSetOptionDTO{

    /**
     * 值集树
     */
    @Schema(description = "值集树")
    private List<ValueSetTreeDTO> treeList = new LinkedList<ValueSetTreeDTO>();
}
