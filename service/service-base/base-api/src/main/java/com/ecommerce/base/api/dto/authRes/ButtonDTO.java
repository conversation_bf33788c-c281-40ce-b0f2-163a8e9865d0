package com.ecommerce.base.api.dto.authRes;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Data
public class ButtonDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 5973897660508085568L;
    /**
     * 按钮id
     */
    @Schema(description = "按钮id")
    private String buttonId;

    /**
     * 按钮code
     */
    @Schema(description = "按钮code")
    private String buttonCode;

    /**
     * 按钮名称
     */
    @Schema(description = "按钮名称")
    private String butttonName;

    /**
     * 页面id
     */
    @Schema(description = "页面Code")
    private String pageCode;

    /**
     * 页面名称
     */
    @Schema(description = "页面名称")
    private String pageName;

    /**
     *
     */
    @Schema(description = "")
    private String buttonRemark;

    /**
     * URL/按钮/新页面
     */
    @Schema(description = "URL/按钮/新页面")
    private String urlType;

    /**
     * 页面url
     */
    @Schema(description = "页面url")
    private String pageUrl;

    /**
     * 按钮是否加入权限管理,不加入无法授权
     */
    @Schema(description = "按钮是否加入权限管理,不加入无法授权")
    private String needPriv;

    @Schema(description = "页面下的按钮，如果授权被选中，则为true")
    private Boolean selected = Boolean.FALSE;


    /**
     * 按钮在页面中的cssId
     */
    @Schema(description = "按钮在页面中的cssId")
    private String htmlId;
    /**
     * 删除标识
     */
    @Schema(description = "删除标识")
    private Boolean delFlg;

    /**
     * 创建用户
     */
    @Schema(description = "创建用户")
    private String createUser;

    /**
     * 更新用户
     */
    @Schema(description = "更新用户")
    private String updateUser;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;

    /**
     * 数据版本
     */
    @Schema(description = "数据版本")
    private Long version;

    @Schema(description = "翻页页码")
    private Integer pageNum = 1;

    @Schema(description = "翻页每页数据量")
    private Integer pageSize = 10;
}
