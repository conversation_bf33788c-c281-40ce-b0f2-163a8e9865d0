package com.ecommerce.base.api.dto.authRes;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class DataPermDTO {
    @Schema(description = "标准区域权限列表")
    private List<String> standardRegionPerms;
    @Schema(description = "销售区域权限列表")
    private List<String> saleRegionPerms;

    @Schema(description = "仓库权限列表")
    private List<String> storehousePerms;

    @Schema(description = "仓库权限列表")
    private List<DataPermERPAccountNameDTO> erpAccountNames;
}
