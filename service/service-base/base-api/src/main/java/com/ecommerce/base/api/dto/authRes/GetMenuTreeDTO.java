package com.ecommerce.base.api.dto.authRes;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 20/03/2019 15:07
 * @DESCRIPTION:
 */
@Data
public class GetMenuTreeDTO {

    @NotBlank
    @Schema(description = "平台类型")
    private String platform;

    @NotNull
    @Schema(description = "角色id集合")
    private List<Integer> roleIdList;
}
