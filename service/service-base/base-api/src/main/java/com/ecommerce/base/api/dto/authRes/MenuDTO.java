package com.ecommerce.base.api.dto.authRes;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Data
public class MenuDTO implements Serializable {

    /**
     * 菜单代码
     */
    @Schema(description = "菜单代码")
    private String menuId;

    /**
     * 菜单Code
     */
    @Schema(description = "菜单Code")
    private String menuCode;

    /**
     * 菜单名称
     */
    @Schema(description = "菜单名称")
    private String menuName;

    @Schema(description = "页面代码")
    private String pageCode;

    /**
     * 菜单对应的URL
     */
    @Schema(description = "菜单对应的URL")
    private String menuUrl;

    /**
     * 默认打开的页面
     */
    @Schema(description = "默认打开的页面")
    private String defaultPage;

    /**
     * 上级菜单代码
     */
    @Schema(description = "上级菜单代码")
    private String parentCode;

    /**
     * 上级菜单名称
     */
    @Schema(description = "上级菜单名称")
    private String parentName;

    /**
     * 菜单所属项目取值集JZ0001
     */
    @Schema(description = "菜单所属项目(取值集JZ0001)")
    private String appType;

    @Schema(description = "菜单图标")
    private String icon;

    /**
     * 菜单根
     */
    @Schema(description = "菜单根")
    private String menuRoot;

    /**
     * 当前有效状态
     */
    @Schema(description = "当前有效状态")
    private String aliveFlg;


    @Schema(description = "排序字段 顺序排列")
    private Integer orderNumber;

    @Schema(description = "子权限")
    private List<MenuDTO> children=new ArrayList<>();


    /**
     * 创建用户
     */
    @Schema(description = "创建用户")
    private String createUser;

    /**
     * 更新用户
     */
    @Schema(description = "更新用户")
    private String updateUser;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;


    @Schema(description = "翻页页码")
    private Integer pageNum = 1;

    @Schema(description = "翻页每页数据量")
    private Integer pageSize = 10;

    /**
     * 菜单关联的数据权限
     */
    @Schema(description = "菜单关联的数据权限")
    private List<DataPermissionDTO> dataPermList;
}
