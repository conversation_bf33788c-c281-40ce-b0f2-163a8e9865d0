package com.ecommerce.base.api.dto.authRes;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Data
public class PageDTO implements Serializable {

    /**
     * 页面id
     */
    @Schema(description = "页面id")
    private String pageId;

    /**
     * 页面代码
     */
    @Schema(description = "页面代码")
    private String pageCode;

    /**
     * 页面名称
     */
    @Schema(description = "页面名称")
    private String pageName;

    /**
     * 页面url
     */
    @Schema(description = "页面url")
    private String pageUrl;

    /**
     * 页面所在app 取值集JZ0001
     */
    @Schema(description = "页面所在app(取值集JZ0001)")
    private String pageApp;

    /**
     *
     */
    @Schema(description = "一级模块")
    private String muduleL1;

    /**
     *
     */
    @Schema(description = "二级模块")
    private String muduleL2;

    /**
     * 页面文件名称
     */
    @Schema(description = "页面文件名称")
    private String pageFileName;

    /**
     * VUE/NODE
     */
    @Schema(description = "VUE/NODE")
    private String implWay;

    /**
     * 需要/不需要/登录前后不一样
     */
    @Schema(description = "需要/不需要/登录前后不一样")
    private String needLogin;

    /**
     * 主页面/弹出页面/跳转画面/子页面
     */
    @Schema(description = "主页面/弹出页面/跳转画面/子页面")
    private String urlType;

    /**
     * url说明
     */
    @Schema(description = "url说明")
    private String pageRemark;

    /**
     * 删除标识(true表示已删除)
     */
    @Schema(description = "删除标识(true表示已删除)")
    private Boolean delFlg;

    /**
     * 创建用户
     */
    @Schema(description = "创建用户")
    private String createUser;

    /**
     * 更新用户
     */
    @Schema(description = "更新用户")
    private String updateUser;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;

    /**
     * 数据版本
     */
    @Schema(description = "数据版本")
    private Long version;

    @Schema(description = "翻页页码")
    private Integer pageNum = 1;

    @Schema(description = "翻页每页数据量")
    private Integer pageSize = 10;

    /**
     * 父页面id
     */
    @Schema(description = "父页面id")
    private String parentPageId;

    /**
     * 父页面code
     */
    @Schema(description = "父页面code")
    private String parentCode;

    /**
     * 调用关系(弹出/跳转)
     */
    @Schema(description = "调用关系")
    private String callRelationShip;
    /**
     * 调用说明(父页面id存在时有用)
     */
    @Schema(description = "调用说明")
    private String callThat;

    /**
     * 是否有子页面(方便前端显示)
     */
    @Schema(description = "是否有子页面(方便前端显示)")
    private Boolean haveChild = false;

    /**
     * 子孙页面
     */
    @Schema(description = "子孙页面")
    private List<PageDTO> childs;

}
