package com.ecommerce.base.api.dto.authRes;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class PageDetailDTO extends PageDTO {

    @Schema(description = "服务资源集合")
    private List<ServiceDTO> serviceList;

    @Schema(description = "按钮资源集合")
    private List<ButtonDTO> buttonList;

    @Schema(description = "数据权限资源集合")
    private List<DataPermissionDTO> dataPermList;

    @Schema(description = "子页面资源集合")
    private List<PageDetailDTO> childPages;
}
