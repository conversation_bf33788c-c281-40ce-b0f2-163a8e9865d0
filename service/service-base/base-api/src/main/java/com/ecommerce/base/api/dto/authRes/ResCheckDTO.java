package com.ecommerce.base.api.dto.authRes;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

@Schema(name = "判断账号是否有某资源")
@NoArgsConstructor
@AllArgsConstructor
@Data
public class ResCheckDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = -3163780632331855901L;

    @Schema(description = "来自哪个应用(com.ecommerce.common.enums.AppNames.platform)")
    private String appName;

    @Schema(description = "资源类型(com.ecommerce.base.api.enums.ResourceTypeEnum)")
    private String resourceType;

    @Schema(description = "账户id")
    private String accountId;

    @Schema(description = "资源代码code")
    private String code;

}
