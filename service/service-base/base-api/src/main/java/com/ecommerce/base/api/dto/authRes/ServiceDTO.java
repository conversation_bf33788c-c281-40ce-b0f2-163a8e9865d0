
package com.ecommerce.base.api.dto.authRes;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Data
public class ServiceDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 8706872343600321890L;
    /**
     * 服务id
     */
    @Schema(description = "服务id")
    private String serviceId;
    /**
     * 服务code
     */
    @Schema(description = "服务code(后端生成)")
    private String serviceCode;
    /**
     * 服务名称
     */
    @Schema(description = "服务名称")
    private String serviceName;
    /**
     * 服务地址
     */
    @Schema(description = "服务地址")
    private String url;

    /**
     * 服务类型
     */
    @Schema(description = "服务类型")
    private String serviceClass;
    /**
     * 服务功能
     */
    @Schema(description = "服务功能")
    private String serviceFunction;
    /**
     * 备注
     */
    @Schema(description = "备注")
    private String urlRemark;

    /**
     * 是否需要混淆
     */
    @Schema(description = "是否需要混淆")
    private String needConfusion;

    /**
     * 是/否都支持
     */
    @Schema(description = "是/否都支持")
    private String needLogin;

    /**
     * 所属模块
     */
    @Schema(description = "所属模块(取值集JZ0001)")
    private String urlApp;

    /**
     * 删除标识
     */
    @Schema(description = "删除标识")
    private Boolean delFlg;

    /**
     * 创建用户
     */
    @Schema(description = "创建用户")
    private String createUser;

    /**
     * 更新用户
     */
    @Schema(description = "更新用户")
    private String updateUser;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;

    /**
     * 数据版本
     */
    @Schema(description = "数据版本")
    private Long version;

    @Schema(description = "当前页数")
    private Integer pageNum = 1;
    @Schema(description = "分页大小")
    private Integer pageSize = 10;

    /**
     *
     */
    @Schema(description = "一级模块")
    private String muduleL1;

    /**
     *
     */
    @Schema(description = "二级模块")
    private String muduleL2;

    /**
     * 关联表查询使用(页面code)
     */
    @Schema(description = "关联表查询使用(页面code)")
    private String pageCode;
}
