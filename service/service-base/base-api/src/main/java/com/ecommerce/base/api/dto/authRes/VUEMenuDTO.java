package com.ecommerce.base.api.dto.authRes;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.*;

/**
 * 参考 ： https://panjiachen.github.io/vue-element-admin-site/zh/guide/essentials/router-and-nav.html#%E9%85%8D%E7%BD%AE%E9%A1%B9
 *兼容之前版本的数据
 */
@Data
public class VUEMenuDTO implements Comparable<VUEMenuDTO>, Serializable {
    @Serial
    private static final long serialVersionUID = 3861870537694857625L;
	/**
	 * 权限名称
	 */
	@Schema(description = "权限名称")
	private String name;

	/**
	 * 菜单path
	 */
	@Schema(description = "菜单path")
	private String path;

	/**
	 * 组件名称
	 */
	@Schema(description = "组件名称")
	private String componentObjectName;

	/**
	 * 所属平台
	 */
	@Schema(description = "所属平台")
	private String platform;

	/**
	 * 组件显示名称
	 */
	@Schema(description = "组件显示名称")
	private String componentView;

	/**
	 * 强制显示
	 */
	@Schema(description = "强制显示")
	private Boolean alwaysShow;

	/**
	 * 隐藏
	 */
	@Schema(description = "隐藏")
	private Boolean hidden;

	/**
	 * 前端路由位置
	 */
	@Schema(description = "前端路由位置")
	@JsonInclude(content=JsonInclude.Include.NON_NULL)
	private String redirect;

	/**
	 * title、icon、noCache的map
	 */
	@Schema(description = "title、icon、noCache的map")
	private HashMap<String, Object> meta = new HashMap<>();

	/**
	 * 子权限
	 */
	@Schema(description = "子权限")
	private TreeSet<VUEMenuDTO> children=new TreeSet<>();

	//===================================================菜单本来有的字段：
	/**
	 * 菜单Code
	 */
    @Schema(description = "菜单Code")
	private String menuCode;

	/**
	 * 菜单名称
	 */
	@Schema(description = "菜单名称")
	private String menuName;

	@Schema(description = "页面代码")
	private String pageCode;

	/**
	 * 菜单对应的URL
	 */
    @Schema(description = "菜单对应的URL")
	private String menuUrl;

	/**
	 * 默认打开的页面
	 */
    @Schema(description = "默认打开的页面")
	private String defaultPage;

	/**
	 * 上级菜单代码
	 */
    @Schema(description = "父页面code")
	private String parentCode;

	/**
	 * 上级菜单名称
	 */
    @Schema(description = "上级菜单名称")
	private String parentName;

	/**
	 * 菜单所属项目
	 */
    @Schema(description = "菜单所属项目(取值集JZ0001)")
	private String appType;

	@Schema(description = "排序字段 顺序排列")
	private Integer orderNumber;

	@Override
    public int compareTo(VUEMenuDTO o) {
		if( this.orderNumber == null ||this.menuCode == null ){
			return -1;
		}
        if (o == null || o.getOrderNumber() == null) {
			return -1;
		}
        if (this.menuCode.equals(o.getMenuCode())) {
			return 0;
		}
        if (this.orderNumber.intValue() <= o.getOrderNumber().intValue()) {
			return -1;
		}
		return 1;
	}
}
