package com.ecommerce.base.api.dto.authRes;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 *
 */
@Data
public class VUEPageDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 5671469763827690145L;
    /**
     * 页面代码
     */
    @Schema(description = "页面代码")
    private String pageCode;

    /**
     * 页面名称
     */
    @Schema(description = "页面名称")
    private String pageName;

    /**
     * 页面url
     */
    @Schema(description = "页面url")
    private String pageUrl;

    /**
     * 页面文件名称
     */
    @Schema(description = "页面文件名称")
    private String pageFileName;


}
