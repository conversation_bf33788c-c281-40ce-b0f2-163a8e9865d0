package com.ecommerce.base.api.dto.cloud;

import io.swagger.v3.oas.annotations.media.Schema;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.Date;

public class AttachmentBase<T> {

	/**
	 * 主键
	 */
	@Schema(description = "主键")
	private long id;

	/**
	 * 用户id
	 */
	@Schema(description = "用户id")
	private String userId;

	/**
	 * 文件类型
	 */
	@Schema(description = "文件类型")
	private String type;

	/**
	 * key
	 */
	@Schema(description = "key")
	private String key;

	/**
	 * value
	 */
	@Schema(description = "value")
	private T value;

	/**
	 * 文件大小
	 */
	@Schema(description = "文件大小")
	private long length;

	/**
	 * 桶名
	 */
	@Schema(description = "桶名")
	private String bucketName;

	/**
	 * 提交时间
	 */
	@Schema(description = "提交时间")
	private Date uploadTime;

	/**
	 * 有效时间
	 */
	@Schema(description = "有效时间")
	private Date effectiveTime;

	/**
	 * 访问权限
	 */
	@Schema(description = "访问权限")
	private int accessAuthority;

	/**
	 * 提交方ip
	 */
	@Schema(description = "提交方ip")
	private int uploadIp;

	/**
	 * 获取子类传递给他的具体泛型类型
	 */
	@Schema(description = "获取子类传递给他的具体泛型类型")
    private Class<?> clazz;
	
	
	public AttachmentBase() {
		// 1获取子类的class(在创建子类对象的时候,会返回父类的构造方法)
        clazz = this.getClass();
        // 2获取当前类的带有泛型的父类类型
        ParameterizedType type = (ParameterizedType) clazz.getGenericSuperclass();
        // 3返回实际参数类型(泛型可以写多个)
        Type[] types = type.getActualTypeArguments();
        // 4 获取第一个参数(泛型的具体类) Person.class
        clazz = (Class) types[0];

	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getUserId() {
		return userId;
	}
	public void setUserId(String userId) {
		this.userId = userId;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public String getKey() {
		return key;
	}
	public void setKey(String key) {
		this.key = key;
	}
	public T getValue() {
		return value;
	}
	public void setValue(T value) {
		this.value = value;
	}

	public long getLength() {
		return length;
	}

	public void setLength(long length) {
		this.length = length;
	}

	public String getBucketName() {
		return bucketName;
	}
	public void setBucketName(String bucketName) {
		this.bucketName = bucketName;
	}
	public Date getUploadTime() {
		return uploadTime;
	}
	public void setUploadTime(Date uploadTime) {
		this.uploadTime = uploadTime;
	}
	public Date getEffectiveTime() {
		return effectiveTime;
	}
	public void setEffectiveTime(Date effectiveTime) {
		this.effectiveTime = effectiveTime;
	}
	public int getAccessAuthority() {
		return accessAuthority;
	}
	public void setAccessAuthority(int accessAuthority) {
		this.accessAuthority = accessAuthority;
	}
	public int getUploadIp() {
		return uploadIp;
	}
	public void setUploadIp(int uploadIp) {
		this.uploadIp = uploadIp;
	}

    public Class<?> getClazz() {
		return clazz;
	}

    public void setClazz(Class<?> clazz) {
		this.clazz = clazz;
	}
	
	
}
