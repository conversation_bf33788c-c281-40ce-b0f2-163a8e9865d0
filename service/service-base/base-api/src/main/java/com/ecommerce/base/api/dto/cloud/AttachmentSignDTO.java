package com.ecommerce.base.api.dto.cloud;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 *
 * <AUTHOR>
 *
 */
@Schema(name = "获取上传签名dto")
@Data
public class AttachmentSignDTO {

	/**
	 * 读写级别：0,默认, 1 私有读写, 2 公有读私有写, 3 公有读写
	 */
	@Schema(description = "读写级别：0,默认, 1 私有读写, 2 公有读私有写, 3 公有读写")
	private Integer readWriteLevel = 0;

	/**
	 * 文件的全名称，包含后缀名
	 */
	@NotBlank
	@Schema(description= "文件的全名称，包含后缀名", name = "fileName", example = "yyzz")
	private String fileName;

	/**
	 * 系统账户id
	 */
	@Schema(description = "系统账户id", name = "accountId")
	private String accountId;

	/**
	 * 业务场景的英文简写
	 */
	@Schema(description = "业务场景的英文简写", name = "businessScenario")
	private String businessScenario;
}
