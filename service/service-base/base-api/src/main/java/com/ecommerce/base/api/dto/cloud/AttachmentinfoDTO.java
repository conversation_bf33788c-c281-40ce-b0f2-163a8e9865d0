package com.ecommerce.base.api.dto.cloud;

import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

@Data
public class AttachmentinfoDTO {

    /**
     * 附件id
     */
	@Schema(description = "附件id")
	private String attachmentId;

    /**
     * 业务场景id, 或者叫组id
     */
	@Schema(description = "业务场景id, 或者叫组id")
	private String bid;

    /**
     * 会员id
     */
	@Schema(description = "会员id")
    private String memberId;

    /**
     * 账号id
     */
	@Schema(description = "账号id")
    private String accountId;

    /**
     * 附件名
     */
	@Schema(description = "附件名")
    @NotBlank
    private String attcName;

    /**
     * 附件类型
     */
	@Schema(description = "附件类型")
    @NotBlank
    private  String attcType;

    /**
     * 附件大小
     */
	@Schema(description = "附件大小")
    @NotBlank
    private Long attcSize;

    /**
     * 附件路径
     */
	@Schema(description = "附件路径")
    @NotBlank
    private String attcPath;

    /**
     * 附件状态
     */
	@Schema(description = "附件状态", hidden = true)
    private Integer attcStatus;

    /**
     * 创建时间
     */
	@Schema(description = "创建时间", hidden = true)
    private Date createTime;

    /**
     * 有效时间
     */
	@Schema(description = "有效时间")
    private Date effectiveTime;

    /**
     * ip
     */
	@Schema(description = "ip", hidden = true)
    private  String uploadIp;

    /**
     * 应用场景，如ios
     */
	@Schema(description = "应用场景，如ios")
    private String apply;

    /**
     * 删除标记，0 未删除，1删除
     */
	@Schema(description = "删除标记，0 未删除，1删除", hidden = true)
    private Boolean delFlg;

    /**
     * 关键字
     */
	@Schema(description = "关键字")
    private String keywords;
}
