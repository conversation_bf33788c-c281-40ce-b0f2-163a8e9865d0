package com.ecommerce.base.api.dto.cloud;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class CloudSignDTO {

	/**
	 * 桶名
	 */
	@Schema(description = "桶名")
	private String bucketName;

	/**
	 * 读写级别
	 */
	@Schema(description = "读写级别")
	private int readWriteLevel;

	/**
	 * 附件名
	 */
	@Schema(description = "附件名")
	private String fileName;

	/**
	 * 失效时间
	 */
	@Schema(description = "失效时间")
	private Long expiredSeconds = 600L;

	/**
	 * 方法名称
	 */
	@Schema(description = "方法名称")
	private String methodName;

	/**
	 * 账户id
	 */
	@Schema(description = "账户id")
	private String accountId;

	/**
	 * 业务场景的英文简写
	 */
	@Schema(description = "业务场景的英文简写")
	private String businessScenario;

	/**
	 * 完整路径
	 */
	@Schema(description = "完整路径")
	private String fullPath;
}
