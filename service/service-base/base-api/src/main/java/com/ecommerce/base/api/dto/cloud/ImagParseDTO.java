package com.ecommerce.base.api.dto.cloud;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

@Data
public class ImagParseDTO{

	/**
	 * 图片url
	 */
	@Schema(description = "图片url")
	@NotBlank
	private String imgUrl;

	/**
	 * 图片类型
	 */
	@Schema(description = "图片类型")
	@NotBlank
	private String imgType;

	/**
	 * 桶名
	 */
	@Schema(description = "桶名")
	@NotBlank
	private String bucketName;

	/**
	 * 图片转换类型
	 */
	@Schema(description = "图片转换类型")
	@NotBlank
	private Integer imgParseType;
}
