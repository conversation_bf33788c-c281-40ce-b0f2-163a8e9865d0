package com.ecommerce.base.api.dto.cloud;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class UploadFileDTO {
	
	
	/**
     *  腾讯云桶
     */
	@Schema(description = "腾讯云桶")
	private String  bucketName;

	/**
     *  腾讯云 key
     */
	@Schema(description = "腾讯云 key")
	private String key;
	
	/**
     *  文件输入流
     */
	@Schema(description = "文件输入流")
	private String fileContent;
	
	/**
     *  文件大小
     */
	@Schema(description = "文件大小")
	private Long length;

}
