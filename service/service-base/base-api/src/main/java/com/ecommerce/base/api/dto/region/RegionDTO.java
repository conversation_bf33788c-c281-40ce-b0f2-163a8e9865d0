package com.ecommerce.base.api.dto.region;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

import jakarta.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 14/08/2018 22:10
 * @DESCRIPTION:
 */
@Schema(name = "行政区域dto")
@Data
public class RegionDTO {

    /**
     * 子节点
     */
    @Schema(description = "子节点")
    private List<RegionDTO>  childs;


    /**
     * 行政区划级别
     */
    @Schema(description = "行政区划级别",required = true)
    @NotBlank
    private String level;

    /**
     * 区域代码
     */
    @Schema(description = "区域代码",required = true)
    @NotBlank
    private String adcode;

    /**
     * 城市编码
     */
    @Schema(description = "城市编码",required = true)
    @NotBlank
    private String citycode;

    /**
     * 行政区名称
     */
    @Schema(description = "行政区名称",required = true)
    @NotBlank
    private String name;

    /**
     * 中心位置
     */
    @Schema(description = "中心位置",required = true)
    @NotBlank
    private String center;

    /**
     * 行政区边界坐标点
     */
    @Schema(description = "行政区边界坐标点",required = true)
    @NotBlank
    private String polyline;

    /**
     * 删除标记
     */
    @Schema(description = "删除标记",required = true)
    @NotBlank
    private Boolean delFlg;

    /**
     * 创建人
     */
    @Schema(description = "创建人",required = true)
    @NotBlank
    private String createUser;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间",required = true)
    @NotBlank
    private Date createTime;

    /**
     * 修改人
     */
    @Schema(description = "修改人",required = true)
    @NotBlank
    private String updateUser;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间",required = true)
    @NotBlank
    private Date updateTime;

}
