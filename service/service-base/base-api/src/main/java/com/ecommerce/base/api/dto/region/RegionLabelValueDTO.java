package com.ecommerce.base.api.dto.region;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 14/08/2018 22:10
 * @DESCRIPTION:
 */
@Data
public class RegionLabelValueDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = -3174138670542114319L;
    /**
     * 区域编码
     */
    @Schema(description = "区域编码")
    private String value;

    /**
     * 行政区名称
     */
    @Schema(description = "行政区名称")
    private String label;

    /**
     * 子节点
     */
    @Schema(description = "子节点")
    private List<RegionLabelValueDTO>  childs;

}
