

package com.ecommerce.base.api.dto.region;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date: 14/08/2018 22:10
 * @DESCRIPTION:
 */
@Schema(name = "行政区域SampleDTO")
@Data
public class RegionSampleDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = -3174138670542114511L;

    /**
     * 上级区域代码
     */
    @Schema(description = "上级区域代码")
    private String parentAdcode;
    /**
     * 区域代码
     */
    @Schema(description = "区域代码")
    private String adcode;

    /**
     * 城市编码
     */
    @Schema(description = "城市编码")
    private String citycode;

    /**
     * 行政区名称
     */
    @Schema(description = "行政区名称")
    private String name;

    /**
     * 中心位置
     */
    @Schema(description = "中心位置")
    private String center;

    @Schema(description = "行政区划级别")
    private String level;

    @Schema(description = "标记删除(如果停用则标记为true)")
    private Boolean delFlg;
}
