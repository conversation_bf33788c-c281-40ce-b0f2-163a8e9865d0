package com.ecommerce.base.api.dto.report;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(name = "报表配置信息")
public class ReportInfoDTO {

    @Schema(description = "报表主键")
    private Integer configId;

    @Schema(description = "报表编码")
    private String reportCode;

    @Schema(description = "报表名")
    private String reportName;

    @Schema(description = "html模板名")
    private String htmlTemplateName;

    @Schema(description = "excel模板名")
    private String excelTemplateName;

    @Schema(description = "报表参数列表")
    private List<ReportParamDTO> reportParamDTOList;

}
