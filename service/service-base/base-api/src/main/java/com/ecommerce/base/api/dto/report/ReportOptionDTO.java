package com.ecommerce.base.api.dto.report;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @Auther: colu
 * @Date: 2019-08-20 15:23
 * @Description: ReportOptionDTO
 */
@Data
@Schema(name = "报表下拉列表项")
public class ReportOptionDTO {

    @Schema(description = "报表编码")
    private String reportCode;

    @Schema(description = "报表名")
    private String reportName;

    @Schema(description = "报表简介")
    private String reportIntro;

    @Schema(description = "报表关联角色集合")
    private List<String> roleList;
}
