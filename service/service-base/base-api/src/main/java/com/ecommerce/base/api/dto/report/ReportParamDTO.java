package com.ecommerce.base.api.dto.report;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(name = "报表参数")
public class ReportParamDTO {

    @Schema(description = "报表编码")
    private String reportCode;

    @Schema(description = "参数")
    private String paramCode;

    @Schema(description = "参数描述")
    private String paramLabel;

    @Schema(description = "参数类型: comboBox, input, date")
    private String paramType;

    @Schema(description = "值集父级编码")
    private String paramValuesetCode;

    @Schema(description = "下拉列表项")
    private List<ReportValuesetItem> valuesetItemList;

}
