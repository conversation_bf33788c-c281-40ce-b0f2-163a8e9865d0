package com.ecommerce.base.api.dto.retry;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class RetryDTO {
    @Schema(description = "关联ID")
    private String objId;

    @Schema(description = "重试策略")
    private String retryRule;

    @Schema(description = "重试参数")
    private String param;

    @Schema(description = "方法")
    private String method;

    @Schema(description = "类名")
    private String className;
}
