package com.ecommerce.base.api.dto.sysLog;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date: 24/12/2018 13:24
 * @DESCRIPTION:
 */
@Schema(name = "系统日志")
@Data
public class SysLogDTO {

    /**
     * id
     */
    @Schema(description = "id")
    private String id;

    /**
     * 请求会话id
     */
    @Schema(description = "请求会话id")
    private String sessionId;

    /**
     * 中心或web层应用名称
     */
    @Schema(description = "中心或web层应用名称")
    private String centerName;

    /**
     * 中心或web层应用所在ip
     */
    @Schema(description = "中心或web层应用所在ip")
    private String certerIp;

    /**
     * 操作模块(服务名称)
     */
    @Schema(description = "操作模块(服务名称)")
    private String moduleName;

    /**
     * 操作动作(服务方法名称)
     */
    @Schema(description = "操作动作(服务方法名称)")
    private String operMethod;

    /**
     * 操作耗时(服务内部调用具体类名+方法名称)
     */
    @Schema(description = "操作耗时(服务内部调用具体类名+方法名称)")
    private String operSubMethod;

    /**
     * 操作结果(success/fail/exception)
     */
    @Schema(description = "操作结果(success/fail/exception)")
    private String operResult;

    /**
     * 业务编号
     */
    @Schema(description = "业务编号")
    private String businessUmber;

    /**
     * 异常名称(类名)
     */
    @Schema(description = "异常名称(类名)")
    private String exception;

    /**
     * 异常代码
     */
    @Schema(description = "异常代码")
    private String exceptionCode;

    /**
     * 操作开始时间
     */
    @Schema(description = "操作开始时间")
    private Date operStartTime;

    /**
     * 操作结束时间
     */
    @Schema(description = "操作结束时间")
    private Date operEndTime;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 操作耗时(毫秒)
     */
    @Schema(description = "操作耗时(毫秒)")
    private Long operCostTime;

    /**
     * 操作人所在会员id
     */
    @Schema(description = "操作人所在会员id")
    private String operMemberId;

    /**
     * 操作人所在会员code
     */
    @Schema(description = "操作人所在会员code")
    private String operMemberCode;

    /**
     * 操作人id
     */
    @Schema(description = "操作人id")
    private String createUser;

    /**
     * 操作人账户名
     */
    @Schema(description = "操作人账户名")
    private String createUserName;

    /**
     * 操作人ip
     */
    @Schema(description = "操作人ip")
    private String createUserIp;

    /**
     * 创建时间(日志插入数据库的时间)
     */
    @Schema(description = "创建时间(日志插入数据库的时间)")
    private Date createTime;

    /**
     * 操作入参
     */
    @Schema(description = "操作入参")
    private String operInputParams;

    /**
     * 操作出参(返回值)
     */
    @Schema(description = "操作出参(返回值)")
    private String operReturnValue;

    /**
     * 错误消息
     */
    @Schema(description = "错误消息")
    private String errorMessage;

    /**
     * 异常堆栈
     */
    @Schema(description = "异常堆栈")
    private String stackTraceInfo;
}
