

package com.ecommerce.base.api.dto.sysLog;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date: 24/12/2018 13:25
 * @DESCRIPTION:
 */
@Data
public class SysLogQueryDTO {

    /**
     * 请求会话id like
     */
    @Schema(description = "请求会话id like")
    private String sessionId;

    /**
     * 中心或web层应用名称 eq
     */
    @Schema(description = "中心或web层应用名称 eq")
    private String centerName;

    /**
     * 中心或web层应用所在ip eq
     */
    @Schema(description = "中心或web层应用所在ip eq")
    private String certerIp;

    /**
     * 操作模块(服务名称) eq
     */
    @Schema(description = "操作模块(服务名称) eq")
    private String moduleName;

    /**
     * 操作动作(服务方法名称) like
     */
    @Schema(description = "操作动作(服务方法名称) like")
    private String operMethod;

    /**
     * 操作耗时(服务内部调用具体类名+方法名称) like
     */
    @Schema(description = "操作耗时(服务内部调用具体类名+方法名称) like")
    private String operSubMethod;

    /**
     * 操作结果(success/fail/exception) eq
     */
    @Schema(description = "操作结果(success/fail/exception) eq")
    private String operResult;

    /**
     * 业务编号 like
     */
    @Schema(description = "业务编号 like")
    private String businessUmber;

    /**
     * 异常名称(类名) like
     */
    @Schema(description = "异常名称(类名) like")
    private String exception;

    /**
     * 异常代码 eq
     */
    @Schema(description = "异常代码 eq")
    private String exceptionCode;

    /**
     * 备注 like
     */
    @Schema(description = "备注 like")
    private String remark;

    /**
     * 操作耗时(毫秒) gte
     */
    @Schema(description = "操作耗时(毫秒) gte")
    private Long operCostTime;

    /**
     * 操作人所在会员id eq
     */
    @Schema(description = "操作人所在会员id eq")
    private String operMemberId;

    /**
     * 操作人所在会员code eq
     */
    @Schema(description = "操作人所在会员code eq")
    private String operMemberCode;

    /**
     * 操作人id eq
     */
    @Schema(description = "操作人id eq")
    private String createUser;

    /**
     * 操作人id like
     */
    @Schema(description = "操作人id like")
    private String createUserName;

    /**
     * 操作人IP like
     */
    @Schema(description = "操作人IP like")
    private String createUserIp;

    /**
     * 创建时间(日志插入数据库的时间) between
     */
    @Schema(description = "创建时间(日志插入数据库的时间) between")
    private Date createTimeBegin;

    /**
     * 创建时间(日志插入数据库的时间) and
     */
    @Schema(description = "创建时间(日志插入数据库的时间) and")
    private Date createTimeEnd;

    /**
     * 错误消息 like
     */
    @Schema(description = "错误消息 like")
    private String errorMessage;

    @Schema(description = "翻页页码")
    private Integer pageNum = 1;

    @Schema(description = "翻页每页数据量")
    private Integer pageSize = 10;
}
