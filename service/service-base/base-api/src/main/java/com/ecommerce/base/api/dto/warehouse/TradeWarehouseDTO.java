package com.ecommerce.base.api.dto.warehouse;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description:
 * @Date: Create in 上午9:36 18/9/12
 */
@Data
@Schema(name = "交易仓库对象")
public class TradeWarehouseDTO implements Serializable{

    /**
     * 仓库名字
     */
    @NotBlank(message = "仓库名字不能为空")
    @Schema(description = "仓库名字",required = true)
    private String name;

    /**
     * 仓库类型
     */
    @NotNull(message = "仓库类型不能为空")
    @Schema(description = "仓库类型",required = true)
    private String type;

    /**
     * 是否支持水边交货 0不支持 1支持
     */
    @Schema(description = "是否支持水边交货 0不支持 1支持")
    private Byte hasWharf;

    /**
     * 省
     */
    @NotBlank(message = "省不能为空")
    @Schema(description = "省",required = true)
    private String province;

    /**
     * 省份代码
     */
    @NotBlank(message = "省份代码不能为空")
    @Schema(description = "省份代码",required = true)
    private String provinceCode;

    /**
     * 市
     */
    @NotBlank(message = "市不能为空")
    @Schema(description = "市",required = true)
    private String city;

    /**
     * 城市代码
     */
    @NotBlank(message = "城市代码不能为空")
    @Schema(description = "城市代码",required = true)
    private String cityCode;

    /**
     * 区
     */
    @NotBlank(message = "区不能为空")
    @Schema(description = "区",required = true)
    private String district;

    /**
     * 地区代码
     */
    @NotBlank(message = "地区代码不能为空")
    @Schema(description = "地区代码",required = true)
    private String districtCode;

    /**
     * 街道地址
     */
    @NotBlank(message = "街道地址不能为空")
    @Schema(description = "街道地址",required = true)
    private String street;

    /**
     * 街道Code
     */
    @NotBlank(message = "街道Code不能为空")
    @Schema(description = "街道Code",required = true)
    private String streetCode;

    /**
     * 定位地址
     */
    @NotBlank(message = "定位地址不能为空")
    @Schema(description = "定位地址")
    private String locationAddress;

    /**
     * 详细地址
     */
    @Schema(description = "详细地址",required = true)
    private String address;

    /**
     * 地址经纬度
     */
    @Schema(description = "地址经纬度",required = true)
    private String location;
}
