package com.ecommerce.base.api.dto.wharf;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(name = "码头省份DTO")
public class TreeProvinceDTO {

    @Schema(description = "省份")
    private String name;

    @Schema(description = "省份代码")
    private String code;

    @Schema(description = "城市信息集合")
    private List<TreeCityDTO> childrenList;

}
