package com.ecommerce.base.api.dto.wharf;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: TODO
 * <AUTHOR>
 * @Date: 29/10/2020 19:59
 */
@Data
@Schema(name = "码头信息新增DTO")
public class WharfAddDTO {

    @Schema(description = "码头名字")
    private String wharfName;

    @Schema(description = "码头联系人名称")
    private String contactName;

    @Schema(description = "码头联系人电话")
    private String contactPhone;

    @Schema(description = "省份")
    private String province;

    @Schema(description = "省份代码")
    private String provinceCode;

    @Schema(description = "城市")
    private String city;

    @Schema(description = "城市代码")
    private String cityCode;

    @Schema(description = "地区")
    private String district;

    @Schema(description = "地区代码")
    private String districtCode;

    @Schema(description = "详细地址")
    private String address;

    @Schema(description = "地址经纬度")
    private String location;

    @Schema(description = "码头最小吃水深度")
    private Integer minDepth;

    @Schema(description = "码头最大吃水深度")
    private Integer maxDepth;

    @Schema(description = "最大靠泊能力")
    private Integer maxBerth;

    @Schema(description = "船舶类型")
    private String shipType;

    @Schema(description = "启用状态（0-启用，1-禁用，2-信息不全）")
    private  Byte status;

    @Schema(description = "操作人")
    private String operatorId;
}
