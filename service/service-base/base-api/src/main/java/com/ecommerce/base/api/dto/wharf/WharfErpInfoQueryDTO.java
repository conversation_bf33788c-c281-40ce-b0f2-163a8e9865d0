package com.ecommerce.base.api.dto.wharf;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: WharfErpInfoQueryDTO
 * <AUTHOR>
 * @Date: 06/01/2021 15:42
 */
@Data
@Schema(name = "erp码头信息查询DTO")
public class WharfErpInfoQueryDTO {

    @Schema(description = "会员Id")
    private String memberId;

    @Schema(description = "码头名称")
    private String wharfName;

    @Schema(description = "是否装船码头(1: 是、0: 否)")
    private String loadingWhart;

    @Schema(description = "码头归属客户编号")
    private String memCode;
}
