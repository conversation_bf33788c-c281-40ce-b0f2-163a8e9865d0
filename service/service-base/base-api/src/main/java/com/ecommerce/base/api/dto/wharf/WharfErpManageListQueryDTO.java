package com.ecommerce.base.api.dto.wharf;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


/**
 * ERP码头管理查询对象
 * <AUTHOR>
 * @Version 1.0
 */
@Data
@Schema(name = "ERP码头管理查询对象DTO")
public class WharfErpManageListQueryDTO {

    @Schema(description = "主键ID")
    private String id;

    @Schema(description = "卖家ID")
    private String memberId;

    @Schema(description = "买家名称")
    private String buyerName;

    @Schema(description = "省份编码")
    private String provinceCode;

    @Schema(description = "城市编码")
    private String cityCode;

    @Schema(description = "区编码")
    private String areaCode;

    @Schema(description = "erp码头名称")
    private String erpWharfName;

}
