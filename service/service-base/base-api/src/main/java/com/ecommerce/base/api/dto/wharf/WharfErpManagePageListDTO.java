package com.ecommerce.base.api.dto.wharf;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


/**
 * <AUTHOR>
 * @Version 1.0
 */
@Data
@Schema(name = "ERP码头管理查询对象DTO")
public class WharfErpManagePageListDTO {

    @Schema(description = "主键ID")
    private String id;

    @Schema(description = "买家名称")
    private String buyerName;

    @Schema(description = "买家地址")
        private String buyerAddress;

    @Schema(description = "厂商名称")
    private String firmName;

    @Schema(description = "erp码头名称")
    private String erpWharfName;

}
