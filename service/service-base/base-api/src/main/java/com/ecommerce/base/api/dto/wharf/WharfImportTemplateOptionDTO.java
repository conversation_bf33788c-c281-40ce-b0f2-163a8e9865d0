package com.ecommerce.base.api.dto.wharf;

import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @Description: TODO
 * <AUTHOR>
 * @Date: 02/11/2020 10:48
 */
@Data
@Schema(name = "码头批量导入准备数据DTO")
public class WharfImportTemplateOptionDTO {

    public WharfImportTemplateOptionDTO(){}

    public WharfImportTemplateOptionDTO(List<String> provinceList,
                                        List<String> provinceCodeList,
                                        List<String> cityList,
                                        List<String> cityCodeList,
                                        List<String> cityParentList,
                                        List<String> districtList,
                                        List<String> districtCodeList,
                                        List<String> districtParentList){
        this.provinceList = provinceList;
        this.provinceCodeList = provinceCodeList;
        this.cityList = cityList;
        this.cityCodeList = cityCodeList;
        this.cityParentList = cityParentList;
        this.districtList = districtList;
        this.districtCodeList = districtCodeList;
        this.districtParentList = districtParentList;
    }

    @Schema(description = "省份")
    List<String> provinceList;

    @Schema(description = "省份Code")
    List<String> provinceCodeList;

    @Schema(description = "市")
    List<String> cityList;

    @Schema(description = "市Code")
    List<String> cityCodeList;

    @Schema(description = "市所属省")
    List<String> cityParentList;

    @Schema(description = "区")
    List<String> districtList;

    @Schema(description = "区Code")
    List<String> districtCodeList;

    @Schema(description = "区所属省")
    List<String> districtParentList;

    @Schema(description = "船舶类型名称")
    List<String> shipTypeList = Lists.newArrayList("开仓船","罐船","皮带自卸船","江海直达船","海船","其他船只");

    @Schema(description = "船舶类型Code")
    List<String> shipTypeCodeList = Lists.newArrayList("030250100","030250200","030250300","030250400","030250500","030251000");
}
