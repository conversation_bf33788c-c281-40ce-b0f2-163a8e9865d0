package com.ecommerce.base.api.dto.wharf;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: TODO
 * <AUTHOR>
 * @Date: 29/10/2020 19:49
 */
@Data
@Schema(name = "码头列表信息DTO")
public class WharfListDTO {

    @Schema(description = "码头id")
    private String wharfId;

    @Schema(description = "码头编号")
    private String wharfNumber;

    @Schema(description = "码头名字")
    private String wharfName;

    @Schema(description = "码头联系人名称")
    private String contactName;

    @Schema(description = "码头联系人电话")
    private String contactPhone;

    @Schema(description = "详细地址")
    private String address;

    @Schema(description = "最大靠泊能力")
    private Integer maxBerth;

    @Schema(description = "船舶类型")
    private String shipType;

    @Schema(description = "启用状态（0-启用，1-禁用，2-信息不全）")
    private  Byte status;
}
