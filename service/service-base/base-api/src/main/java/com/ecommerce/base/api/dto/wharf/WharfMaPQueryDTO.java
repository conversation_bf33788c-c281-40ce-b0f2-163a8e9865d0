package com.ecommerce.base.api.dto.wharf;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @Description: WharfMaPQueryDTO
 * <AUTHOR>
 * @Date: 07/01/2021 10:16
 */
@Data
@Schema(name = "码头映射关系查询DTO")
public class WharfMaPQueryDTO {

    @Schema(description = "关联会员id")
    private String memberId;

    @Schema(description = "关联类型 0：收货码头(收货地址) 1：装货码头(仓库)")
    private String type;

    @Schema(description = "电商码头Id")
    private String ecWharfId;

    @Schema(description = "电商码头名称")
    private String ecWharfName;

    @Schema(description = "erp会员id")
    private String erpMemberId;

    @Schema(description = "关联会员id列表")
    private List<String> memberIdList;

    @Schema(description = "电商码头名称列表")
    private List<String> ecWharfNameList;
}
