package com.ecommerce.base.api.dto.wharf;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @Description: WharfMapAddDTO
 * <AUTHOR>
 * @Date: 07/01/2021 10:12
 */
@Data
@Schema(name = "码头映射信息保存DTO")
public class WharfMapAddDTO {

    @Schema(description = "码头映射信息")
    private List<WharfMapInfoDTO> wharfMapInfoDTOList;

    @Schema(description = "关联会员id")
    private String memberId;

    @Schema(description = "关联类型 0：收货码头(收货地址) 1：装货码头(仓库)")
    private String type;

    @Schema(description = "电商码头编号")
    private String ecWharfId;

    @Schema(description = "电商码头名称")
    private String ecWharfName;

    @Schema(description = "操作人id")
    private String operatorId;


}
