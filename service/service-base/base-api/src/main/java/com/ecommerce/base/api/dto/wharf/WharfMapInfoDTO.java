package com.ecommerce.base.api.dto.wharf;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: WharfMapInfoDTO
 * <AUTHOR>
 * @Date: 07/01/2021 10:41
 */
@Data
@Schema(name = "码头映射信息DTO")
public class WharfMapInfoDTO {

    @Schema(description = "ERP码头编号")
    private String erpWharfCode;

    @Schema(description = "ERP码头名称")
    private String erpWharfName;

    @Schema(description = "ERP卖家会员id")
    private String erpMemberId;

    @Schema(description = "ERP卖家会员名称")
    private String erpMemberName;
}
