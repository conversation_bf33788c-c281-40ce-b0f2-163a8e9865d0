package com.ecommerce.base.api.dto.wharf;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: WharfMapListDTO
 * <AUTHOR>
 * @Date: 07/01/2021 10:15
 */
@Data
@Schema(name = "码头映射关系列表DTO")
public class WharfMapListDTO {

    @Schema(description = "电商码头编号")
    private String ecWharfId;

    @Schema(description = "电商码头名称")
    private String ecWharfName;

    @Schema(description = "关联会员id")
    private String memberId;

    @Schema(description = "ERP卖家会员id")
    private String erpMemberId;

    @Schema(description = "ERP卖家会员名称")
    private String erpMemberName;

    @Schema(description = "ERP码头编号")
    private String erpWharfCode;

    @Schema(description = "ERP码头名称")
    private String erpWharfName;
}
