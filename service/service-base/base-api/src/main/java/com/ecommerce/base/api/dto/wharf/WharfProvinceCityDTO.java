package com.ecommerce.base.api.dto.wharf;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name = "省份城市码头信息DTO")
public class WharfProvinceCityDTO {

    @Schema(description = "码头id")
    private String wharfId;

    @Schema(description = "码头编号")
    private String wharfNumber;

    @Schema(description = "码头名字")
    private String wharfName;

    @Schema(description = "省份")
    private String province;

    @Schema(description = "省份代码")
    private String provinceCode;

    @Schema(description = "城市")
    private String city;

    @Schema(description = "城市代码")
    private String cityCode;

}
