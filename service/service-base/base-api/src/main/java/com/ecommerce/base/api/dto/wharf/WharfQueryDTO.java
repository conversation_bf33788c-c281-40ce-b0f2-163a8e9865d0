package com.ecommerce.base.api.dto.wharf;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: TODO
 * <AUTHOR>
 * @Date: 29/10/2020 19:49
 */
@Data
@Schema(name = "码头列表查询DTO")
public class WharfQueryDTO {

    @Schema(description = "码头编号")
    private String wharfNumber;

    @Schema(description = "省份代码")
    private String provinceCode;

    @Schema(description = "城市代码")
    private String cityCode;

    @Schema(description = "启用状态（0-启用，1-禁用，2-信息不全）")
    private  Byte status;
}
