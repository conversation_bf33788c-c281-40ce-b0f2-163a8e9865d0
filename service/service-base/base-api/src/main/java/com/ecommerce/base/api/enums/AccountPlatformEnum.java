package com.ecommerce.base.api.enums;

import lombok.Getter;

@Getter
public enum AccountPlatformEnum {

    PLATFORM("web-platform", "platform"),
    SELLER("web-seller", "seller"),
    BUYER("web-buyer", "buyer"),
    CARRIER("web-carrier", "carrier"),
    SUPPLIER("web-supplier", "supplier");

    private String registerApp;

    private String roleType;

    AccountPlatformEnum(String registerApp, String roleType) {
        this.registerApp = registerApp;
        this.roleType = roleType;
    }
}
