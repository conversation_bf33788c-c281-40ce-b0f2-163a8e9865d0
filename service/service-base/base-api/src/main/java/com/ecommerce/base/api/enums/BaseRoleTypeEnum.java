package com.ecommerce.base.api.enums;

public enum BaseRoleTypeEnum {
	INDIVIDUAL_BUYER(1,"个人买家","individual_buyer"),
	INDIVIDUAL_DRIVER(2,"个人司机","individual_driver"),
	ENTERPRISE_BUYER(3,"企业买家","enterprise_buyer"),
	ENTERPRISE_SELLER(4,"企业卖家","enterprise_seller"),
	ENTERPRISE_SELLER_CONCRETE(20,"卖家","enterprise_seller_concrete"),
	ENTERPRISE_SUPPLIER(83,"企业供应商","enterprise_supplier"),
	CARRIER(5,"承运商","carrier"),
	ANONYMOUS(6,"匿名角色","anonymous"),
	DEFAULT_SUB_ACCOUNT(7,"默认子账号","default_sub_account"),
	SELLER_SALESPERSON(8,"卖家业务员","seller_salesperson"),
	CARRIER_DRIVER(9,"承运商司机","carrier_driver"),
	PLATFORM(10,"平台主账号","platform"),
	SALES_MANAGER(11,"销售经理","sales_manager"),
	SALES_ASSISTANT(12,"销售助理","sales_assistant"),
	STORAGE_ADMIN_SELLER(34,"卖家仓库管理员","storage_admin_seller"),
	STORAGE_ADMIN_PLATFORM(35,"仓库管理员","storage_admin_platform"),
	SELLER_LOGISTICSDISPATH(52,"物流调度","seller_logisticsdispatch"),
	CARRIER_YARDMAN(54,"调度人员","carrier_yardman"),
	BUYER_PURCHASE(65,"买家采购人员","buyer_purchase"),
	BUYER_FINANCE(66, "买家财务人员","buyer_finance"),
	CAPTAIN(156, "船长", "captain"),
	PERSON_SHIPOWNER(160, "个体船东", "person_shipowner"),
	//对账确认相关角色
	BILL_CHECK_FINANCIAL_SELLER(170, "对账-卖家财务角色", "bill_check_financial_seller"),
	BILL_CHECK_FINANCIAL_PLATFORM(171, "对账-平台财务角色", "bill_check_financial_platform"),
	BILL_CHECK_FINANCIAL_CARRIER(172, "对账-承运商财务角色", "bill_check_financial_carrier"),

	BILL_CHECK_BUSINESS_CONFIRM_SELLER(173, "对账-卖家业务确认角色", "bill_check_business_confirm_seller"),
	BILL_CHECK_BUSINESS_CONFIRM_PLATFORM(174, "对账-平台业务确认角色", "bill_check_business_confirm_platform"),
	BILL_CHECK_BUSINESS_CONFIRM_CARRIER(175, "对账-承运商业务确认角色", "bill_check_business_confirm_carrier"),

	BILL_CHECK_CUSTOMER_CONFIRM_SELLER(176, "对账-客户(卖家)确认角色", "bill_check_customer_confirm_seller"),
	BILL_CHECK_CUSTOMER_CONFIRM_PLATFORM(177, "对账-客户(平台)确认角色", "bill_check_customer_confirm_platform"),
	BILL_CHECK_CUSTOMER_CONFIRM_CARRIER(178, "对账-客户(承运商)确认角色", "bill_check_customer_confirm_carrier"),
	BILL_CHECK_CUSTOMER_CONFIRM_BUYER(179, "对账-客户(买家)确认角色", "bill_check_customer_confirm_buyer");


	private String info;
	private Integer id;
	private String name;
	
	private BaseRoleTypeEnum(Integer id,String name,String info) {
		this.info=info;
		this.id=id;
		this.name=name;
	}

	public String getInfo() {
		return info;
	}

	public Integer getId() {
		return id;
	}

	public String getName() {
		return name;
	}
}	
