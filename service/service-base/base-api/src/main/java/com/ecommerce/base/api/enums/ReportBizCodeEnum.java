package com.ecommerce.base.api.enums;

/**
 * @Auther: colu
 * @Date: 2019-08-01 14:24
 * @Description: 报表业务编码枚举
 */
public enum ReportBizCodeEnum {

    HUI_XIAO_CHE_SNATCH_STATIS("hui_xiao_che_snatch_statis", "惠小车抢单统计");

    private String code;

    private String message;

    private ReportBizCodeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public static ReportBizCodeEnum valueOfCode(String code) {
        ReportBizCodeEnum[] enums = values();
        for (ReportBizCodeEnum item : enums) {
            if (item.code.equals(code)) {
                return item;
            }
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.message;
    }



}
