package com.ecommerce.base.api.enums;

public enum ResourceTypeEnum {
	//btn(按钮),menu（菜单）,page（页面）,service（服务）
	BUTTON("btn","按钮资源"),
	MENU("menu","菜单资源"),
	PAGE("page","页面资源"),
	SERVICE("service","服务资源"),
	DATA("data","数据权限资源");

	private String info;
	private String name;

	private ResourceTypeEnum(String name, String info) {
		this.info=info;
		this.name=name;
	}

	public String getInfo() {
		return info;
	}


	public String getName() {
		return name;
	}

	public static ResourceTypeEnum getByName(String name){
		if( name == null ){
			return null;
		}
		for (ResourceTypeEnum value : ResourceTypeEnum.values()) {
			if( value.getName().equals(name)){
				return value;
			}
		}
		return null;
	}
}	
