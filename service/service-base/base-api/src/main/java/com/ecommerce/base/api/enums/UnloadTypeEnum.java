package com.ecommerce.base.api.enums;

public enum UnloadTypeEnum
{
    TYPE10(10, "经销商"),
    T<PERSON><PERSON><PERSON>(11, "管桩厂"),
    <PERSON><PERSON><PERSON><PERSON>(12, "搅拌站"),
    <PERSON><PERSON><PERSON><PERSON>(13, "预制件厂"),
    <PERSON><PERSON><PERSON><PERSON>(15, "重点工程"),
    <PERSON><PERSON><PERSON><PERSON>(16, "门店"),
    <PERSON><PERSON><PERSON><PERSON>(17, "熟料客户"),
    <PERSON><PERSON><PERSON><PERSON>(18, "散户"),
    <PERSON><PERSON><PERSON><PERSON>(19, "一般工程"),
    <PERSON><PERSON><PERSON><PERSON>(22, "房地产/建筑商"),
    <PERSON><PERSON><PERSON><PERSON>(30, "配送片区"),
    <PERSON><PERSON><PERSON><PERSON>(31, "市政"),
    T<PERSON><PERSON><PERSON>(32, "工业"),
    TYPE33(33, "零星工程");

    private int id;
    private String name;

    private UnloadTypeEnum(int id, String name)
    {
        this.id = id;
        this.name = name;
    }

    public int getId()
    {
        return id;
    }
    public String getName()
    {
        return name;
    }

    public static UnloadTypeEnum getById(int id)
    {
        UnloadTypeEnum[] enums = values();
        for(UnloadTypeEnum item : enums)
        {
            if(item.id == id)
            {
                return item;
            }
        }
        return null;
    }
}
