package com.ecommerce.base.api.enums;

public enum UserRoleEnum {

    BUYER(1, "买家"),

    SELL<PERSON>(2, "卖家"),

    CARRIER(3, "承运商"),

    PERSONAL_DRIVER(4, "个人司机"),

    PLATFORM(5, "平台");

    /**
     * 描述
     */
    private final Integer code;

    /**
     * 编码
     */
    private final String desc;

    UserRoleEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static UserRoleEnum valueOfCode(Integer code) {
        UserRoleEnum[] enums = values();
        for (UserRoleEnum item : enums) {
            if (item.code.equals(code)) {
                return item;
            }
        }
        return null;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}
