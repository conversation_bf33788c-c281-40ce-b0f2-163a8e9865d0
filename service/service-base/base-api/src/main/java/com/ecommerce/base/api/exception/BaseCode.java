package com.ecommerce.base.api.exception;

import com.ecommerce.common.exception.CodeMeta;

/**
 * <AUTHOR>
 * @Date: 20/10/2018 14:35
 * @DESCRIPTION:
 */
public class BaseCode {
    public static final CodeMeta CONFIG_ERROR = new CodeMeta("010001", "CONFIG_ERROR", "配置错误: {}", " config error: {}");
    public static final CodeMeta ATTACHMENT_CATEGORY_NOT_EXISTS = new CodeMeta("010002", "ATTACHMENT_CATEGORY_NOT_EXISTS", "应用场景{}不存在", " apply: {} not exists!");
    public static final CodeMeta ATTACHMENT_CATEGORY_REPEAT = new CodeMeta("010003", "ATTACHMENT_CATEGORY_REPEAT", "应用场景{}重复了", " apply: {} has repeat!");

    public static final CodeMeta ATTACHMENT_CATEGORY_DISABLED = new CodeMeta("010004", "ATTACHMENT_CATEGORY_DISABLED", "应用场景{}已停用", " apply: {} has disabled!");

    public static final CodeMeta IMAGE_OCR_ERROR = new CodeMeta("010005", "IMAGE_OCR_ERROR", "图片信息识别错误", "get img info error:{}");

    public static final CodeMeta METHOD_NOT_FOUND = new CodeMeta("010006", "METHOD_NOT_FOUND", "找不到对应的方法:{}", "can not find method error:{}");

    public static final CodeMeta SERVICE_NOT_FOUND = new CodeMeta("010007", "SERVICE_NOT_FOUND", "找不到对应的服务:{}", "can not find service error:{}");

    public static final CodeMeta SERIALIZE_ERROR = new CodeMeta("010008", "SERIALIZE_ERROR", "序列化出错", "serialize error");
}
