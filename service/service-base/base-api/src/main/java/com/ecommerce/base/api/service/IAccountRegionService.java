
package com.ecommerce.base.api.service;

import com.ecommerce.base.api.dto.accountregion.AccountRegionDTO;
import com.ecommerce.base.api.dto.accountregion.AccountRegionShowDTO;
import com.ecommerce.base.api.dto.accountregion.AccountRegionUpdateDTO;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;


/**
 * 账号行政区域的数据权限服务
*/

@FeignClient(name = "service-base")
@Tag(name = "IAccountRegionService", description = "账号行政区域的数据权限服务")
public interface IAccountRegionService {


   @Operation(summary = "通过账号id查询行政区域编码")
   @PostMapping( path = "/accountRegion/findAdCodeByAccountId", consumes = "application/json")
   public List<String> findAdCodeByAccountId(@RequestParam String arg0);


   @Operation(summary = "更新单个账户行政区域")
   @PostMapping( path = "/accountRegion/updateAccountRegion", consumes = "application/json")
   public void updateAccountRegion(@RequestBody AccountRegionUpdateDTO arg0);


   @Operation(summary = "通过账户查询他相关的行政区域，并按3级方式返回")
   @PostMapping( path = "/accountRegion/findShowInfoByAccountId", consumes = "application/json")
   public List<AccountRegionShowDTO> findShowInfoByAccountId(@RequestParam String arg0);


   @Operation(summary = "批量修改 账号行政区域数据")
   @PostMapping( path = "/accountRegion/updateBatch", consumes = "application/json")
   public void updateBatch(@RequestBody List<AccountRegionDTO> arg0,@RequestParam String arg1);


   @Operation(summary = "通过账号id查询详细信息")
   @PostMapping( path = "/accountRegion/findByAccountId", consumes = "application/json")
   public List<AccountRegionDTO> findByAccountId(@RequestParam String arg0);


   @Operation(summary = "批量添加 账号行政区域数据")
   @PostMapping( path = "/accountRegion/addBatch", consumes = "application/json")
   public void addBatch(@RequestBody List<AccountRegionDTO> arg0,@RequestParam String arg1);



}
