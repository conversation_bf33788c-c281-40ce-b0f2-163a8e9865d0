package com.ecommerce.base.api.service;

import com.ecommerce.base.api.dto.address.AddressDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name ="service-base")
public interface IAddressService
{
    @GetMapping("/address/getSaleRegion")
    public AddressDTO getSaleRegion(@RequestParam String addressId, @RequestParam String memberId);
}
