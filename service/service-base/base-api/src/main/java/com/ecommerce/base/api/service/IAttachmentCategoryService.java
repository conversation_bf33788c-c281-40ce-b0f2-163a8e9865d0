
package com.ecommerce.base.api.service;

import com.ecommerce.base.api.dto.attachment.AttachmentCategoryAddDTO;
import com.ecommerce.base.api.dto.attachment.AttachmentCategoryDTO;
import com.ecommerce.base.api.dto.attachment.AttachmentCategoryQueryDTO;
import com.ecommerce.base.api.dto.attachment.AttachmentCategoryUpdateDTO;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;


/**
 * @Created锛�Sat Oct 20 18:23:14 CST 2018
 * <AUTHOR>
 * @Version:2
 * @Description::null
*/

@FeignClient(name = "service-base")
@Tag(name = "IAttachmentCategoryService", description = "附件存储桶路径及使用场景配置")
public interface IAttachmentCategoryService {


   @Operation(summary = "启用")
   @PostMapping( path = "/attachmentCategory/enable", consumes = "application/json")
   public void enable(@RequestParam String arg0,@RequestParam String arg1,@RequestParam String arg2);


   @Operation(summary = "更新")
   @PostMapping( path = "/attachmentCategory/update", consumes = "application/json")
   public void update(@RequestBody AttachmentCategoryUpdateDTO arg0);


   @Operation(summary = "创建")
   @PostMapping( path = "/attachmentCategory/create", consumes = "application/json")
   public void create(@RequestBody AttachmentCategoryAddDTO arg0);


   @Operation(summary = "翻页查询")
   @PostMapping( path = "/attachmentCategory/list", consumes = "application/json")
   public PageInfo<AttachmentCategoryDTO> list(@RequestBody AttachmentCategoryQueryDTO arg0);


   @Operation(summary = "刷新附件分类定义缓存")
   @PostMapping( path = "/attachmentCategory/refashCache", consumes = "application/json")
   public void refashCache(@RequestParam String arg0);


   @PostMapping( path = "/attachmentCategory/deleteByApply", consumes = "application/json")
   public void deleteByApply(@RequestParam String arg0,@RequestParam String arg1,@RequestParam String arg2);


   @Operation(summary = "禁用")
   @PostMapping( path = "/attachmentCategory/disabled", consumes = "application/json")
   public void disabled(@RequestParam String arg0,@RequestParam String arg1,@RequestParam String arg2);


   @Operation(summary = "根据应用场景查询")
   @PostMapping( path = "/attachmentCategory/findByApply", consumes = "application/json")
   public AttachmentCategoryDTO findByApply(@RequestParam String arg0);


   @Operation(summary = "判断路径是否存在")
   @PostMapping( path = "/attachmentCategory/pathIsExists", consumes = "application/json")
   public boolean pathIsExists(@RequestParam String arg0);



}
