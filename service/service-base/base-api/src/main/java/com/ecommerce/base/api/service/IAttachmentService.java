
package com.ecommerce.base.api.service;

import com.ecommerce.base.api.dto.BizLicenseDTO;
import com.ecommerce.base.api.dto.DriverLicenceBackDTO;
import com.ecommerce.base.api.dto.DriverLicenceDTO;
import com.ecommerce.base.api.dto.DrivingLicenceBackDTO;
import com.ecommerce.base.api.dto.DrivingLicenceDTO;
import com.ecommerce.base.api.dto.IdCardParseBackDTO;
import com.ecommerce.base.api.dto.IdCardParseFrontDTO;
import com.ecommerce.base.api.dto.ImgParseDTO;
import com.ecommerce.base.api.dto.TencentOcrDTO;
import com.ecommerce.base.api.dto.attachment.AttachmentDeleteDTO;
import com.ecommerce.base.api.dto.attachment.AttachmentFileDTO;
import com.ecommerce.base.api.dto.cloud.AttachmentSignDTO;
import com.ecommerce.base.api.dto.cloud.AttachmentinfoDTO;
import com.ecommerce.base.api.dto.cloud.tencent.TencentCloudSignDTO;
import com.ecommerce.common.result.ItemResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;


/**
 * @Created锛�Sat Sep 08 15:15:52 CST 2018
 * <AUTHOR>
 * @Version:2
 * @Description::null
 */

@FeignClient(name = "service-base")
@Tag(name = "IAttachmentService", description = "文件服务")
public interface IAttachmentService {


    @PostMapping( value = "/attachment/uploadFile", consumes = MediaType.APPLICATION_JSON_VALUE)
    ItemResult<String> uploadFile(@RequestBody byte[] file, @RequestParam String bucketName, @RequestParam String key);

    @PostMapping( path = "/attachment/getDownloadAuthorizationStr", consumes = "application/json")
    public String getDownloadAuthorizationStr(@RequestParam String bucketName, @RequestParam String key, @RequestParam Long expireMinutes);


    @Operation(summary = "【行驶证】解析行驶证")
    @PostMapping( path = "/attachment/parseOcrDrivingLicenceImg", consumes = "application/json")
    public DrivingLicenceDTO parseOcrDrivingLicenceImg(@RequestBody TencentOcrDTO tencentOcrDTO);

    @Operation(summary = "【行驶证】解析行驶证反面")
    @PostMapping( path = "/attachment/parseOcrDrivingLicenceBackImg", consumes = "application/json")
    public DrivingLicenceBackDTO parseOcrDrivingLicenceBackImg(@RequestBody TencentOcrDTO tencentOcrDTO);


    @PostMapping( path = "/attachment/buildAuthorizationStr", consumes = "application/json")
    public String buildAuthorizationStr(@RequestBody TencentCloudSignDTO tencentCloudSignDTO);


    @Operation(summary = "【营业执照】解析营业执照")
    @PostMapping( path = "/attachment/parseOcrBizLicenseImg", consumes = "application/json")
    public BizLicenseDTO parseOcrBizLicenseImg(@RequestBody TencentOcrDTO tencentOcrDTO);


    @Operation(summary = "【删除】通过aid,或者bid删除附件,逻辑删除")
    @PostMapping( path = "/attachment/deleteAtachmentById", consumes = "application/json")
    public Boolean deleteAtachmentById(@RequestParam String bID, @RequestParam String aID);

    @Operation(summary = "【删除】通过id批量删除")
    @PostMapping( path = "/attachment/deleteAtachmentByIds", consumes = "application/json")
    public Boolean deleteAtachmentByIds(@RequestBody AttachmentDeleteDTO dto);

    @Operation(summary = "【身份证】解析身份证正面")
    @PostMapping( path = "/attachment/parseOcrIdCardFrontImg", consumes = "application/json")
    public IdCardParseFrontDTO parseOcrIdCardFrontImg(@RequestBody TencentOcrDTO tencentOcrDTO);


    @Operation(summary = "【身份证】解析身份证背面")
    @PostMapping( path = "/attachment/parseOcrIdCardBackImg", consumes = "application/json")
    public IdCardParseBackDTO parseOcrIdCardBackImg(@RequestBody TencentOcrDTO tencentOcrDTO);


    @Operation(summary = "【删除】通过Url删除文件，逻辑删除")
    @PostMapping( path = "/attachment/deleteCloudFileByUrl", consumes = "application/json")
    public Boolean deleteCloudFileByUrl(@RequestParam String url);


    @Operation(summary = "【查询】通过bid查询附件")
    @PostMapping( path = "/attachment/getAttachmentByBID", consumes = "application/json")
    public List<AttachmentinfoDTO> getAttachmentByBID(@RequestParam String bID);


    @Operation(summary = "【驾驶证】解析驾驶证")
    @PostMapping( path = "/attachment/parseOcrDriverLicenceImg", consumes = "application/json")
    public DriverLicenceDTO parseOcrDriverLicenceImg(@RequestBody TencentOcrDTO tencentOcrDTO);

    @Operation(summary = "【驾驶证】解析驾驶证反面")
    @PostMapping( path = "/attachment/parseOcrDriverLicenceBackImg", consumes = "application/json")
    public DriverLicenceBackDTO parseOcrDriverLicenceBackImg(@RequestBody TencentOcrDTO tencentOcrDTO);


    @Operation(summary = "【上传签名】获取上传签名")
    @PostMapping( path = "/attachment/getUploadAuthorizationStr", consumes = "application/json")
    public String getUploadAuthorizationStr(@RequestBody AttachmentSignDTO attachmentSignDTO);

    @Operation(summary = "【上传签名】批量获取上传签名")
    @PostMapping( path = "/attachment/getUploadAuthorizationList", consumes = "application/json")
    public String getUploadAuthorizationList(@RequestBody List<AttachmentSignDTO> attachmentSignDTOList);

    @Operation(summary = "【通用解析】解析base64图片")
    @PostMapping( path = "/attachment/parseImg", consumes = "application/json")
    public Map<String, String> parseCommonImg(@RequestBody ImgParseDTO imgParseDTO);

    @Operation(summary = "【上传持久化】新增或修改,(带aid参数过来代表修改，不带aid代表新增，带bid过来表示业务场景不变)")
    @PostMapping( path = "/attachment/insertFileInfo", consumes = "application/json")
    public String insertFileInfo(@RequestBody List<AttachmentinfoDTO> fileList);

    @Operation(summary = "还原【上传持久化】更新的数据（实名认证审批拒绝专用）")
    @PostMapping( path = "/attachment/recover", consumes = "application/json")
    void recover(@RequestParam String attachmentId);


    @Operation(summary = "【确认】确认上传，上传成功后通知我改变状态")
    @PostMapping( path = "/attachment/confirmUpload", consumes = "application/json")
    public String confirmUpload(@RequestParam String ids);

    @Operation(summary = "通过URL下载文件")
    @PostMapping( path = "/attachment/downLoadFileByUrl", consumes = "application/json")
    public AttachmentFileDTO downLoadFileByUrl(@RequestParam String url);

    @PostMapping( path = "/attachment/getBucketName", consumes = "application/json")
    public String getBucketName(@RequestParam String clazz, @RequestParam String method);


    @PostMapping( path = "/attachment/downLoadFile", consumes = "application/json")
    public String downLoadFile(@RequestParam String bucketName, @RequestParam String key);

    @Operation(summary = "【全路径】获取带签名的url")
    @PostMapping( path = "/attachment/getFullPath", consumes = "application/json")
    public String getFullPath(@RequestParam String url);

    @Operation(summary = "【全路径】批量获取带签名的url")
    @PostMapping( path = "/attachment/getFullPathList", consumes = "application/json")
    public List<String> getFullPathList(@RequestBody List<String> urlList);

    @Operation(summary = "【收集司机】")
    @PostMapping( path = "/attachment/insertDriverCollection", consumes = "application/json")
    public Boolean insertDriverCollection(@RequestBody Map<String, String> map);

    @PostMapping( path = "/attachment/findById", consumes = "application/json")
    public AttachmentinfoDTO findById(@RequestParam String id);

    @PostMapping( path = "/attachment/findByIds", consumes = "application/json")
    public List<AttachmentinfoDTO> findByIds(@RequestBody List<String> ids);

    @Operation(summary = "cdn文件刷新")
    @PostMapping( path = "/attachment/refreshUrl", consumes = "application/json")
    public void refreshUrl(@RequestBody List<String> urls);

    @Operation(summary = "通过bid集合查询所有附件")
    @PostMapping( path = "/attachment/findByBids", consumes = "application/json")
    public List<AttachmentinfoDTO> findByBids(@RequestBody List<String> bids);

}
