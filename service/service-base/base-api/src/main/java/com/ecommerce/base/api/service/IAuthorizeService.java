
package com.ecommerce.base.api.service;

import com.ecommerce.base.api.dto.authorize.AuthorizeButtonDTO;
import com.ecommerce.base.api.dto.authorize.AuthorizeDTO;
import com.ecommerce.base.api.dto.authorize.AuthorizeMenuDTO;
import com.ecommerce.base.api.dto.authorize.AuthorizePageDTO;
import com.ecommerce.base.api.dto.role.RoleDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;


/**
 * @Created锛�Fri Mar 22 20:00:54 CST 2019
 * <AUTHOR>
 * @Version:2
 * @Description::null
*/

@FeignClient(name = "service-base")
@Tag(name = "IAuthorizeService", description = "授权服务")
public interface IAuthorizeService {


   @Operation(summary = "查询所有角色")
   @PostMapping( path = "/authorize/findAllRole", consumes = "application/json")
   public List<RoleDTO> findAllRole();


   @Operation(summary = "角色授权")
   @PostMapping( path = "/authorize/authorize", consumes = "application/json")
   public boolean authorize(@RequestBody AuthorizeDTO arg0);


   @Operation(summary = "初始化后端鉴权数据")
   @PostMapping( path = "/authorize/initAuthticate", consumes = "application/json")
   public void initAuthticate();


   @Operation(summary = "刷新前后端鉴权数据")
   @PostMapping( path = "/authorize/refreshRedisData", consumes = "application/json")
   public void refreshRedisData();


   @Operation(summary = "页面授权")
   @PostMapping( path = "/authorize/authorizePage", consumes = "application/json")
   public boolean authorizePage(@RequestBody AuthorizePageDTO arg0);


   @Operation(summary = "初始化前端鉴权数据")
   @PostMapping( path = "/authorize/initRedisData", consumes = "application/json")
   public void initRedisData(@RequestParam(required = false) Integer roleId);


   @Operation(summary = "按钮授权")
   @PostMapping( path = "/authorize/authorizeButton", consumes = "application/json")
   public boolean authorizeButton(@RequestBody AuthorizeButtonDTO arg0);


   @Operation(summary = "菜单授权")
   @PostMapping( path = "/authorize/authorizeMenu", consumes = "application/json")
   public boolean authorizeMenu(@RequestBody AuthorizeMenuDTO arg0);


   @Operation(summary = "角色授权")
   @PostMapping( path = "/authorize/findRoleByCondition", consumes = "application/json")
   public List<RoleDTO> findRoleByCondition(@RequestBody RoleDTO arg0);



}
