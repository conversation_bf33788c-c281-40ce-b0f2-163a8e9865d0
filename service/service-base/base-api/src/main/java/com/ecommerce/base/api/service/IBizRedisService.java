package com.ecommerce.base.api.service;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;


@FeignClient(name = "service-base")
@Tag(name = "IBizRedisService", description = "redis缓存操作")
public interface IBizRedisService {

    @Operation(summary = "根据key前缀匹配批量删除")
    @PostMapping( path = "/bizRedis/delByKeyPrefix", consumes = "application/json")
    public void delByKeyPrefix(@RequestParam String pattern, @RequestParam String operatorId);
}
