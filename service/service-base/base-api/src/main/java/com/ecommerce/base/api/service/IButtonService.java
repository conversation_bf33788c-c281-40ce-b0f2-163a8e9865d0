
package com.ecommerce.base.api.service;

import com.ecommerce.base.api.dto.authRes.ButtonDTO;
import com.ecommerce.base.api.dto.authRes.RoleAuthenticateDTO;
import com.ecommerce.base.api.dto.permission.BindRelationshipDTO;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;


/**
 * @Created锛�Fri Mar 08 11:19:16 CST 2019
 * <AUTHOR>
 * @Version:2
 * @Description::资源管理_安妮
*/

@FeignClient(name = "service-base")
@Tag(name = "IButtonService", description = "资源管理_按钮")
public interface IButtonService {


   @Operation(summary = "解除绑定页面按钮关系")
   @PostMapping( path = "/button/unbindBtne2Page", consumes = "application/json")
   public void unbindBtne2Page(@RequestBody BindRelationshipDTO bindDTO);


   @Operation(summary = "检查重复(id和code任何一个重复视为重复)")
   @PostMapping( path = "/button/checkExist", consumes = "application/json")
   public boolean checkExist(@RequestParam String code);

   @Operation(summary = "检查htmlId是否重复")
   @PostMapping( path = "/button/checkHtmlId", consumes = "application/json")
   public Boolean checkHtmlId(@RequestParam String htmlId,@RequestParam(required = false) String buttonId);


   @Operation(summary = "通过角色查询按钮")
   @PostMapping( path = "/button/findBtnByRoleId", consumes = "application/json")
   public List<ButtonDTO> findBtnByRoleId(@RequestParam String roleId);


   @Operation(summary = "删除按钮")
   @PostMapping( path = "/button/delBtn", consumes = "application/json")
   public int delBtn(@RequestParam String id,@RequestParam String operator);


   @Operation(summary = "添加按钮")
   @PostMapping( path = "/button/createBtn", consumes = "application/json")
   public ButtonDTO createBtn(@RequestBody ButtonDTO btn);


   @Operation(summary = "通过主键查询按钮")
   @PostMapping( path = "/button/findBtnById", consumes = "application/json")
   public ButtonDTO findBtnById(@RequestParam String id);


   @Operation(summary = "修改按钮")
   @PostMapping( path = "/button/updateBtn", consumes = "application/json")
   public int updateBtn(@RequestBody ButtonDTO btn);


   @Operation(summary = "通过页面查询按钮")
   @PostMapping( path = "/button/findBtnByPage", consumes = "application/json")
   public List<ButtonDTO> findBtnByPage(@RequestParam String pageId);


   @Operation(summary = "绑定页面按钮关系")
   @PostMapping( path = "/button/bindBtn2Page", consumes = "application/json")
   public void bindBtn2Page(@RequestBody BindRelationshipDTO bindDTO);


   @Operation(summary = "通过角色查询已授权按钮")
   @PostMapping( path = "/button/findSelectBtnByRoleId", consumes = "application/json")
   public List<ButtonDTO> findSelectBtnByRoleId(@RequestParam String roleId);


   @Operation(summary = "条件查询按钮")
   @PostMapping( path = "/button/findBtnByCondiftion", consumes = "application/json")
   public PageInfo<ButtonDTO> findBtnByCondiftion(@RequestBody ButtonDTO btn);

   @Operation(summary = "通过页面查询按钮(授权专用) 参数：selectedPageCodeList,platform")
   @PostMapping( path = "/button/findButtonByRoleAndPlatform", consumes = "application/json")
   RoleAuthenticateDTO findButtonByRoleAndPlatform(@RequestBody RoleAuthenticateDTO dto);

}
