
package com.ecommerce.base.api.service;

import com.ecommerce.base.api.dto.ImgParseDTO;
import com.ecommerce.base.api.dto.cloud.tencent.TencentCloudSignDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.io.InputStream;
import java.net.URL;


/**
 * @Created锛�Sat Sep 08 15:15:52 CST 2018
 * <AUTHOR>
 * @Version:2
 * @Description::封装第三方云服务（对应的实现可以切换）
*/

@FeignClient(name = "service-base")
@Tag(name = "ICloudService", description = "封装第三方云服务（对应的实现可以切换）")
public interface ICloudService {


   @Operation(summary = "获取腾讯云预签名URL")
   @PostMapping( path = "/cloud/generatePresignedUrl", consumes = "application/json")
   public URL generatePresignedUrl(@RequestParam String paramJson);


   @Operation(summary = "获取腾讯云签名")
   @PostMapping( path = "/cloud/buildAuthorizationStr", consumes = "application/json")
   public String buildAuthorizationStr(@RequestBody TencentCloudSignDTO tencentCloudSignDTO);


   @Operation(summary = "删除云上的文件")
   @PostMapping( path = "/cloud/deleteCloudFileByUrl", consumes = "application/json")
   public void deleteCloudFileByUrl(@RequestParam String url);


   @Operation(summary = "通用解析图片类容")
   @PostMapping( path = "/cloud/parseImg", consumes = "application/json")
   public ImgParseDTO parseImg(@RequestParam String paramJson);


   @Operation(summary = "上传文件到云")
   @PostMapping( path = "/cloud/uploadFile", consumes = "application/json")
   public String uploadFile(@RequestParam String bucketName,@RequestParam String key,@RequestParam InputStream input,@RequestBody long length);


   @Operation(summary = "下载云上文件")
   @PostMapping( path = "/cloud/downLoadFile", consumes = "application/json")
   public String downLoadFile(@RequestParam String bucketName,@RequestParam String key);



}
