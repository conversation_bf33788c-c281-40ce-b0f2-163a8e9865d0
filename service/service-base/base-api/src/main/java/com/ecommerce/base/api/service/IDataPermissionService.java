package com.ecommerce.base.api.service;

import com.ecommerce.base.api.dto.authRes.DataPermissionDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;


/**
 * @Created锛�Fri Mar 08 11:23:06 CST 2019
 * <AUTHOR>
 * @Version:2
 * @Description::资源管理_数据权限类型定义服务
*/
@FeignClient(name = "service-base")
@Tag(name = "IDataPermissionService", description = "资源管理_数据权限类型定义")
public interface IDataPermissionService {

   @Operation(summary = "更新数据权限类型定义")
   @PostMapping( path = "/dataPermission/updateDataPermision", consumes = "application/json")
   public int updateDataPermision(@RequestBody DataPermissionDTO dataPermission);

   @Operation(summary = "添加数据权限类型定义")
   @PostMapping( path = "/dataPermission/createDataPermission", consumes = "application/json")
   public DataPermissionDTO createDataPermission(@RequestBody DataPermissionDTO dataPermission);

   @Operation(summary = "根据id删除单条数据权限类型定义(同时会删除该类型下所有的人员数据权限关系)")
   @PostMapping( path = "/dataPermission/delDataPermission", consumes = "application/json")
   public int delDataPermission(@RequestParam String id,@RequestParam String operator);

   @Operation(summary = "根据id查询单条数据权限类型定义")
   @PostMapping( path = "/dataPermission/findDataById", consumes = "application/json")
   public DataPermissionDTO findDataById(@RequestParam String id);

   @Operation(summary = "查询数据权限类型定义code是否存在")
   @PostMapping( path = "/dataPermission/checkExist", consumes = "application/json")
   public boolean checkExist(@RequestParam String code);

   @Operation(summary = "查询所有的数据权限类型定义")
   @PostMapping( path = "/dataPermission/findAll", consumes = "application/json")
   List<DataPermissionDTO> findAll();

   @Operation(summary = "根据账号查询拥有的数据权限类型定义")
   @PostMapping( path = "/dataPermission/findByAccountId", consumes = "application/json")
   List<DataPermissionDTO> findByAccountId(@RequestParam String accountId);

}
