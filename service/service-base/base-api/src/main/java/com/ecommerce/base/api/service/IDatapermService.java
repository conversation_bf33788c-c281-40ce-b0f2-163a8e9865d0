package com.ecommerce.base.api.service;

import com.ecommerce.base.api.dto.authRes.DataPermAccountCreateDTO;
import com.ecommerce.base.api.dto.authRes.DataPermDTO;
import com.ecommerce.base.api.dto.authRes.DataPermERPAccountNameDTO;
import com.ecommerce.common.result.ItemResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;


/**
 * @Created锛�Sat Sep 08 15:15:53 CST 2018
 * <AUTHOR>
 * @Version:2
 * @Description::null
*/
@FeignClient(name = "service-base")
@Tag(name = "IDataPermissionService", description = "资源管理_数据权限-账号关系")
public interface IDatapermService {

   @Operation(summary = "根据账号id查询其数据权限")
   @PostMapping( path = "/dataperm/findDataByAccountId", consumes = "application/json")
   ItemResult<DataPermDTO> findDataByAccountId(@RequestParam String accountId);

   @Operation(summary = "根据账号id查询其erp账号数据权限")
   @PostMapping( path = "/dataperm/findErpAccountNameByAccountId", consumes = "application/json")
   ItemResult<List<DataPermERPAccountNameDTO>> findErpAccountNameByAccountId(@RequestParam String accountId);

   @Operation(summary = "根据账号id和数据权限类型查询其数据权限")
   @PostMapping( path = "/dataperm/findByAccountIdAndDataPrivCode", consumes = "application/json")
   ItemResult<List<String>> findByAccountIdAndDataPrivCode(@RequestParam String accountId, @RequestParam String dataPrivCode);

   @Operation(summary = "添加账号数据权限")
   @PostMapping( path ="/dataperm/addAccountDataPerms", consumes = "application/json")
   ItemResult<Boolean> addAccountDataPerms(@RequestBody DataPermAccountCreateDTO dataPermAccountCreateDTO);

   @Operation(summary = "更新账号数据权限")
   @PostMapping( path = "/dataperm/updateAccountDataPerms", consumes = "application/json")
   ItemResult<Boolean> updateAccountDataPerms(@RequestBody DataPermAccountCreateDTO dataPermAccountCreateDTO);
}
