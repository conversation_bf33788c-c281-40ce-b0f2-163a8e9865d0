
package com.ecommerce.base.api.service;

import com.ecommerce.base.api.dto.saleregion.SaleStoreDTO;
import com.ecommerce.base.api.dto.saleregion.SetDefaultStoreDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;


/**
 * @Created锛�Wed Aug 28 18:22:12 CST 2019
 * <AUTHOR>
 * @Version:2
 * @Description:null
*/

@FeignClient(name = "service-base")
@Tag(name = "IDefaultStoreService", description = "默认仓库")
public interface IDefaultStoreService {


   @Operation(summary = "设置默认仓库")
   @PostMapping( path = "/defaultStore/setDefaultStore", consumes = "application/json")
   public void setDefaultStore(@RequestBody SetDefaultStoreDTO dto,@RequestParam String operator);


   @Operation(summary = "取消默认仓库规则")
   @PostMapping( path = "/defaultStore/cancelDefaultStoreRule", consumes = "application/json")
   public void cancelDefaultStoreRule(@RequestParam String saleRegionId,@RequestParam String operator);


   @Operation(summary = "使用默认仓库规则查询销售区域仓库")
   @PostMapping( path = "/defaultStore/getStoreWithDefaultRule", consumes = "application/json")
   public SaleStoreDTO getStoreWithDefaultRule(@RequestParam String saleRegionId,@RequestBody List<String> storeIds);



}
