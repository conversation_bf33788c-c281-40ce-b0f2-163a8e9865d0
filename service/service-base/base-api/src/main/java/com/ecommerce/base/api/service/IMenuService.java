
package com.ecommerce.base.api.service;

import com.ecommerce.base.api.dto.AccountPermissionDTO;
import com.ecommerce.base.api.dto.authRes.GetMenuTreeDTO;
import com.ecommerce.base.api.dto.authRes.MenuDTO;
import com.ecommerce.base.api.dto.authRes.RoleAuthenticateDTO;
import com.ecommerce.base.api.dto.permission.BindRelationshipDTO;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;


/**
 * @Created锛�Fri Mar 08 11:24:22 CST 2019
 * <AUTHOR>
 * @Version:2
 * @Description::资源管理_菜单
*/

@FeignClient(name = "service-base")
@Tag(name = "IMenuService", description = "资源管理_菜单")
public interface IMenuService {

   @Operation(summary = "修改菜单")
   @PostMapping( path = "/menu/updateMenu", consumes = "application/json")
   public int updateMenu(@RequestBody MenuDTO menu);

   @Operation(summary = "绑定菜单数据权限关系")
   @PostMapping( path = "/menu/bindData2Menu", consumes = "application/json")
   public void bindData2Menu(@RequestBody BindRelationshipDTO bindDTO);


   @Operation(summary = "解绑菜单数据权限关系")
   @PostMapping( path = "/menu/unbindData2Menu", consumes = "application/json")
   public void unbindData2Menu(@RequestBody BindRelationshipDTO bindDTO);


   @Operation(summary = "通过主键查询菜单")
   @PostMapping( path = "/menu/findMenuById", consumes = "application/json")
   public MenuDTO findMenuById(@RequestParam String id);


   @Operation(summary = "删除菜单")
   @PostMapping( path = "/menu/delMenu", consumes = "application/json")
   public int delMenu(@RequestParam String id,@RequestParam String operator);


   @Operation(summary = "检查重复")
   @PostMapping( path = "/menu/checkExist", consumes = "application/json")
   public boolean checkExist(@RequestParam String code);


   @Operation(summary = "添加菜单")
   @PostMapping( path = "/menu/createMenu", consumes = "application/json")
   public MenuDTO createMenu(@RequestBody MenuDTO menu);


   @Operation(summary = "条件查询菜单")
   @PostMapping( path = "/menu/findMenuByCondiftion", consumes = "application/json")
   public PageInfo<MenuDTO> findMenuByCondiftion(@RequestBody MenuDTO menu);

   @PostMapping( path = "/menu/findByRoldId", consumes = "application/json")
   public List<MenuDTO> findByRoldId(@RequestParam Integer roleId, @RequestParam String platform);

   @Operation(summary = "获取登录菜单")
   @PostMapping( path = "/menu/getMenuTree", consumes = "application/json")
   public AccountPermissionDTO getMenuTree(GetMenuTreeDTO dto);

   @Operation(summary = "通过角色和平台查询菜单(授权专用) 参数：roleId(可选),platform")
   @PostMapping( path = "/menu/findMenuByRoleAndPlatform", consumes = "application/json")
   public RoleAuthenticateDTO findMenuByRoleAndPlatform(@RequestBody RoleAuthenticateDTO dto);

   @Operation(summary = "通过关键字查询菜单（菜单代码、菜单名称、url、菜单根）")
   @PostMapping( path = "/menu/findByKeyword", consumes = "application/json")
   public List<MenuDTO> findByKeyword(@RequestParam String keyword);

   @Operation(summary = "通过菜单根和父级菜单查询菜单列表")
   @PostMapping( path = "/menu/findListByMenuRootAndParentCode", consumes = "application/json")
   List<MenuDTO> findListByMenuRootAndParentCode(@RequestParam String menuRoot, @RequestParam String parentCode);

   @Operation(summary = "批量修改菜单排序")
   @PostMapping( path = "/menu/batchUpdateMenuOrderNumber", consumes = "application/json")
   void batchUpdateMenuOrderNumber(@RequestBody List<MenuDTO> list);
}
