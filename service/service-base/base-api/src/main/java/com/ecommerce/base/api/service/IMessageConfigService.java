
package com.ecommerce.base.api.service;

import com.ecommerce.base.api.dto.MessageConfigAndRoleDTO;
import com.ecommerce.base.api.dto.MessageConfigCreateDTO;
import com.ecommerce.base.api.dto.MessageConfigDTO;
import com.ecommerce.base.api.dto.MessageConfigDisableAccountDTO;
import com.ecommerce.base.api.dto.MessageConfigDisableMemberDTO;
import com.ecommerce.base.api.dto.MessageConfigEnableAccountDTO;
import com.ecommerce.base.api.dto.MessageConfigEnableMemberDTO;
import com.ecommerce.base.api.dto.MessageConfigQueryDTO;
import com.ecommerce.base.api.dto.MessageConfigUpdateDTO;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;


/**
 * @Created锛�Thu Sep 20 19:19:36 CST 2018
 * <AUTHOR>
 * @Version:2
 * @Description::null
*/

@FeignClient(name = "service-base")
@Tag(name = "IMessageConfigService", description = "null")
public interface IMessageConfigService {


   @Operation(summary = "禁用")
   @PostMapping( path = "/messageConfig/disable", consumes = "application/json")
   public void disable(@RequestParam String id,@RequestParam String operator);


   @Operation(summary = "启用消息模板")
   @PostMapping( path = "/messageConfig/enable", consumes = "application/json")
   public void enable(@RequestParam String id,@RequestParam String operator);


   @Operation(summary = "修改消息模板")
   @PostMapping( path = "/messageConfig/update", consumes = "application/json")
   public MessageConfigDTO update(@RequestBody MessageConfigUpdateDTO messageConfigDTO);


   @Operation(summary = "新增消息模板")
   @PostMapping( path = "/messageConfig/create", consumes = "application/json")
   public MessageConfigDTO create(@RequestBody MessageConfigCreateDTO messageConfigCreateDTO);


   @Operation(summary = "查询所有消息模板")
   @PostMapping( path = "/messageConfig/list", consumes = "application/json")
   public PageInfo<MessageConfigDTO> list(@RequestBody MessageConfigQueryDTO messageSearchDTO);


   @Operation(summary = "通过名称查询消息模板列表")
   @PostMapping( path = "/messageConfig/findByName", consumes = "application/json")
   public List<MessageConfigDTO> findByName(@RequestParam String name);


   @Operation(summary = "通过编号查询消息模板列表")
   @PostMapping( path = "/messageConfig/findByCode", consumes = "application/json")
   public List<MessageConfigDTO> findByCode(@RequestParam String code);

   @Operation(summary = "通过编号查询消息模板列表和额外配置")
   @PostMapping( path = "/messageConfig/findConfigAndRoleByConfigCode", consumes = "application/json")
   MessageConfigAndRoleDTO findConfigAndRoleByConfigCode(@RequestParam String code);


   @Operation(summary = "删除消息模板")
   @PostMapping( path = "/messageConfig/deleteById", consumes = "application/json")
   public void deleteById(@RequestParam String id,@RequestParam String operator);


   @Operation(summary = "通过消息模板ID查询消息模板")
   @PostMapping( path = "/messageConfig/findById", consumes = "application/json")
   public MessageConfigDTO findById(@RequestParam String id);


   @Operation(summary = "查询会员站内信消息模板配置")
   @PostMapping( path = "/messageConfig/findMemberInnerMessageConfig", consumes = "application/json")
   public List<MessageConfigDTO> findMemberInnerMessageConfig(@RequestParam String memberId);


   @Operation(summary = "根据会员id和code查询需要发送消息的消息模板id")
   @PostMapping( path = "/messageConfig/findByMemberIdAndCode", consumes = "application/json")
   public List<String> findByMemberIdAndCode(@RequestParam String memberId,@RequestParam String messconfigCode);


   @Operation(summary = "查询会员消息模板配置")
   @PostMapping( path = "/messageConfig/findMemberMessageConfig", consumes = "application/json")
   public List<MessageConfigDTO> findMemberMessageConfig(@RequestParam String memberId,@RequestParam Integer type);


   @Operation(summary = "查询会员短信消息模板配置")
   @PostMapping( path = "/messageConfig/findMemberSMSMessageConfig", consumes = "application/json")
   public List<MessageConfigDTO> findMemberSMSMessageConfig(@RequestParam String memberId);


   @Operation(summary = "会员启用消息")
   @PostMapping( path = "/messageConfig/enableMemberConfig", consumes = "application/json")
   public Boolean enableMemberConfig(@RequestBody MessageConfigEnableMemberDTO messageConfigEnableMemberDTO);

   @Operation(summary = "会员禁用消息")
   @PostMapping( path = "/messageConfig/disableMemberConfig", consumes = "application/json")
   public Boolean disableMemberConfig(@RequestBody MessageConfigDisableMemberDTO messageConfigDisableMemberDTO);

   @Operation(summary = "账号启用消息")
   @PostMapping( path = "/messageConfig/enableAccountConfig", consumes = "application/json")
   public Boolean enableAccountConfig(@RequestBody MessageConfigEnableAccountDTO messageConfigEnableAccountDTO);

   @Operation(summary = "账号禁用消息")
   @PostMapping( path = "/messageConfig/disableAccountConfig", consumes = "application/json")
   public Boolean disableAccountConfig(@RequestBody MessageConfigDisableAccountDTO messageConfigDisableAccountDTO);


   @Operation(summary = "查询会员是否已禁用（拒绝接收）该消息(前3个参数至少要1个 )")
   @PostMapping( path = "/messageConfig/isDisabled", consumes = "application/json")
   public Boolean isDisabled(@RequestParam String memberId,@RequestParam String accountId,@RequestParam String mobile,@RequestParam String messConfigId);

   @Operation(summary = "分页查询会员消息模板配置")
   @PostMapping( path = "/messageConfig/pageMemberMessageConfig", consumes = "application/json")
   PageInfo<MessageConfigDTO> pageMemberMessageConfig(@RequestParam String memberId,
                                                                  @RequestParam Integer messageType,
                                                                  @RequestParam Integer pageSize,
                                                                       @RequestParam Integer pageNum);

   @Operation(summary = "分页查询账号消息模板配置")
   @PostMapping( path = "/messageConfig/pageAccountMessageConfig", consumes = "application/json")
   PageInfo<MessageConfigDTO> pageAccountMessageConfig(@RequestParam("accountId") String memberId,
                                                                   @RequestParam Integer messageType,
                                                                   @RequestParam Integer pageSize,
                                                                       @RequestParam Integer pageNum);
}
