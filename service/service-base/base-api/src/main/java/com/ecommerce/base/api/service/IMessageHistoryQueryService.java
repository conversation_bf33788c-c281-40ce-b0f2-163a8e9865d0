package com.ecommerce.base.api.service;

import com.ecommerce.base.api.dto.message.sms.MessageHistorySmsDTO;
import com.ecommerce.base.api.dto.message.sms.MessageHistorySmsQueryDTO;
import com.ecommerce.base.api.dto.warehouse.PageQuery;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


@FeignClient(name = "service-base")
@Tag(name = "IMessageConfigService", description = "历史消息查询服务")
public interface IMessageHistoryQueryService {

    @Schema(description = "短信历史消息翻页查询")
    @PostMapping( path = "/message/history/query/pageSMSInfo", consumes = "application/json")
    PageInfo<MessageHistorySmsDTO> pageSMSInfo(@RequestBody PageQuery<MessageHistorySmsQueryDTO> queryDTO);

}
