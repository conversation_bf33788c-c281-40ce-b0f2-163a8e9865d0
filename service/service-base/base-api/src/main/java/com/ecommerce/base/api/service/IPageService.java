
package com.ecommerce.base.api.service;

import com.ecommerce.base.api.dto.authRes.PageDTO;
import com.ecommerce.base.api.dto.authRes.PageDetailDTO;
import com.ecommerce.base.api.dto.authRes.ResCallRelationDTO;
import com.ecommerce.base.api.dto.authRes.RoleAuthenticateDTO;
import com.ecommerce.base.api.dto.permission.BindRelationshipDTO;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;


/**
 * @Created锛�Fri Mar 08 11:25:01 CST 2019
 * <AUTHOR>
 * @Version:2
 * @Description::资源管理_页面
*/

@FeignClient(name = "service-base")
@Tag(name = "IPageService", description = "资源管理_页面")
public interface IPageService {


   @Operation(summary = "通过菜单查询页面")
   @PostMapping( path = "/page/findPageByMenu", consumes = "application/json")
   public List<PageDTO> findPageByMenu(@RequestParam String menuId);


   @Operation(summary = "通过角色查询已授权页面")
   @PostMapping( path = "/page/findSelectPageByRoleId", consumes = "application/json")
   public List<PageDTO> findSelectPageByRoleId(@RequestParam String roleId);


   @Operation(summary = "条件查询页面")
   @PostMapping( path = "/page/findPageByCondiftion", consumes = "application/json")
   public PageInfo<PageDTO> findPageByCondiftion(@RequestBody PageDTO page);


   @Operation(summary = "解绑页面服务")
   @PostMapping( path = "/page/unbindService2Page", consumes = "application/json")
   public void unbindService2Page(@RequestBody BindRelationshipDTO bindDTO);


   @Operation(summary = "通过角色查询页面及子页面")
   @PostMapping( path = "/page/findPageByRoleId", consumes = "application/json")
   public List<PageDTO> findPageByRoleId(@RequestParam String roleId);


   @Operation(summary = "通过主键查询页面")
   @PostMapping( path = "/page/findPageById", consumes = "application/json")
   public PageDTO findPageById(@RequestParam String id);

   @Operation(summary = "通过菜单批量查询页面")
   @PostMapping( path = "/page/findPageByMenuIds", consumes = "application/json")
   public List<PageDTO> findPageByMenuIds(@RequestBody List<String> menuIds);

   @Operation(summary = "解除绑定页面关系")
   @PostMapping( path = "/page/unbindPage2Page", consumes = "application/json")
   public void unbindPage2Page(@RequestBody BindRelationshipDTO bindDTO);


   @Operation(summary = "通过页面ID查询页面详情(按钮，service，页面关系)")
   @PostMapping( path = "/page/findPageDetail", consumes = "application/json")
   public PageDetailDTO findPageDetail(@RequestParam String pageId);


   @Operation(summary = "绑定页面关系")
   @PostMapping( path = "/page/bindPage2Page", consumes = "application/json")
   public void bindPage2Page(@RequestBody BindRelationshipDTO bindDTO);


   @Operation(summary = "修改页面")
   @PostMapping( path = "/page/updatePage", consumes = "application/json")
   public int updatePage(@RequestBody PageDTO page);


   @Operation(summary = "绑定页面服务关系")
   @PostMapping( path = "/page/bindService2Page", consumes = "application/json")
   public void bindService2Page(@RequestBody BindRelationshipDTO bindDTO);


   @Operation(summary = "删除页面")
   @PostMapping( path = "/page/delPage", consumes = "application/json")
   public int delPage(@RequestParam String id,@RequestParam String operator);


   @Operation(summary = "检查重复")
   @PostMapping( path = "/page/checkExist", consumes = "application/json")
   public boolean checkExist(@RequestParam String code);


   @Operation(summary = "添加页面")
   @PostMapping( path = "/page/createPage", consumes = "application/json")
   public PageDTO createPage(@RequestBody PageDTO page);

   @Operation(summary = "条件查询调用关系")
   @PostMapping( path = "/page/findCallRelationByCondition", consumes = "application/json")
   public PageInfo<ResCallRelationDTO> findCallRelationByCondition(@RequestBody ResCallRelationDTO dto);

   @Operation(summary = "查看调用关系详情")
   @PostMapping( path = "/page/findCallRelationDetail", consumes = "application/json")
   public PageDetailDTO findCallRelationDetail(@RequestParam String callId);

   @Operation(summary = "通过页面id和父页面id查询页面(弹出,跳转关系)")
   @PostMapping( path = "/page/findPageBy2Id", consumes = "application/json")
   public PageDTO findPageBy2Id(@RequestBody PageDTO page);

   @Operation(summary = "通过菜单查询页面(授权专用) 参数：roleId(可选),platform")
   @PostMapping( path = "/page/findPageByRoleAndPlatform", consumes = "application/json")
   RoleAuthenticateDTO findPageByRoleAndPlatform(@RequestBody RoleAuthenticateDTO dto);

   @Operation(summary = "根据名称或者代码查询")
   @PostMapping( path = "/page/findByKeyword", consumes = "application/json")
   List<PageDTO> findByKeyword(@RequestParam String keyword);
}
