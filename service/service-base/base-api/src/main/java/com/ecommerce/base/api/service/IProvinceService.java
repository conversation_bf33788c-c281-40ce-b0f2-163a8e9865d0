
package com.ecommerce.base.api.service;

import com.ecommerce.base.api.dto.region.RegionDTO;
import com.ecommerce.base.api.dto.serviceProvince.ProvinceDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;


/**
 * @Created锛�Tue Feb 19 10:06:49 CST 2019
 * <AUTHOR>
 * @Version:2
 * @Description::维护系统上线的省份
*/

@FeignClient(name = "service-base")
@Tag(name = "IProvinceService", description = "维护系统上线的省份")
public interface IProvinceService {


   @Operation(summary = "根据区域代码更新省份")
   @PostMapping( path = "/province/update", consumes = "application/json")
   public void update(@RequestBody List<String> adcodeList,@RequestParam String operator);


   @Operation(summary = "查询所有的省份")
   @PostMapping( path = "/province/findAll", consumes = "application/json")
   public List<RegionDTO> findAll();

   @Operation(summary = "查询所有的省份和城市")
   @PostMapping( path = "/province/findProvinceAndCity", consumes = "application/json")
   public List<ProvinceDTO> findProvinceAndCity();

}
