package com.ecommerce.base.api.service;

import com.ecommerce.base.api.dto.SyncReceiveAddressDTO;
import com.ecommerce.common.result.ItemResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "service-base")
public interface IReceivingAddressMapService
{
    @PostMapping(value="/receivingAddressMap/syncAddress")
    public ItemResult<Integer> syncAddress(@RequestBody SyncReceiveAddressDTO dto);
}
