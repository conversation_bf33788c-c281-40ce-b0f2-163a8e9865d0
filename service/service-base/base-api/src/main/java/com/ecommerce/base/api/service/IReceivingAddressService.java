
package com.ecommerce.base.api.service;

import com.ecommerce.base.api.dto.ERPAddressListDTO;
import com.ecommerce.base.api.dto.ERPAddressQueryDTO;
import com.ecommerce.base.api.dto.MemberSearchDTO;
import com.ecommerce.base.api.dto.ReceivingAddressCreateDTO;
import com.ecommerce.base.api.dto.ReceivingAddressDTO;
import com.ecommerce.base.api.dto.ReceivingAddressErpInfoDTO;
import com.ecommerce.base.api.dto.ReceivingAddressErpInfoUpdateDTO;
import com.ecommerce.base.api.dto.ReceivingAddressMapAuditDTO;
import com.ecommerce.base.api.dto.ReceivingAddressMapDTO;
import com.ecommerce.base.api.dto.ReceivingAddressMapQueryDTO;
import com.ecommerce.base.api.dto.ReceivingAddressQueryDTO;
import com.ecommerce.base.api.dto.ReceivingAddressSimpleDTO;
import com.ecommerce.base.api.dto.ReceivingAddressUpdateDTO;
import com.ecommerce.base.api.dto.warehouse.PageQuery;
import com.ecommerce.base.api.dto.wharf.ReceivingAddrMapWharfDTO;
import com.ecommerce.common.result.ItemResult;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collection;
import java.util.List;


/**
 * @Created锛�Wed Sep 12 14:27:47 CST 2018
 * <AUTHOR>
 * @Version:2
 * @Description::收货地址相关service
*/

@FeignClient(name = "service-base")
@Tag(name = "IReceivingAddressService", description = "收货地址相关service")
public interface IReceivingAddressService {

   @Operation(summary = "根据买家卖家厂家查询ERP地址Id")
   @PostMapping(value="/receivingAddress/getReceivingAddressMapInfo")
   public ReceivingAddressMapDTO getReceivingAddressMapInfo(@RequestBody ReceivingAddressMapQueryDTO dto);

   @Operation(summary = "更新收货地址中的销售区域")
   @PostMapping(value="/receivingAddress/updateAddressMap")
   public void updateAddressMap(@RequestBody ReceivingAddressMapDTO dto);

   @Operation(summary = "买家卖家厂家查询")
   @PostMapping(value="/receivingAddress/memberSearch")
   public List<MemberSearchDTO> memberSearch(@RequestBody MemberSearchDTO dto);

   @Operation(summary = "买家收货地址Map")
   @PostMapping(value="/receivingAddress/addressMap")
   public PageInfo<ReceivingAddressMapDTO> addressMap(@RequestBody ReceivingAddressMapQueryDTO dto);

   @Operation(summary = "审核买家收货地址")
   @PostMapping(value="/receivingAddress/addressAudit")
   public void addressAudit(@Parameter(name = "memberType", description = "操作人类型 10：买家、20：卖家、30：厂商") @RequestParam int memberType, @Parameter(name = "operator", description = "操作人") @RequestParam String operator, @RequestBody List<ReceivingAddressMapAuditDTO> dataList);


   @Operation(summary = "修改收货地址")
   @PostMapping( path = "/receivingAddress/update", consumes = "application/json")
   public void update(@RequestBody ReceivingAddressUpdateDTO receivingAddressUpdateDTO);


   @Operation(summary = "创建收货地址")
   @PostMapping( path = "/receivingAddress/create", consumes = "application/json")
   public ItemResult<String> create(@RequestBody ReceivingAddressCreateDTO receivingAddressCreateDTO);


   @Operation(summary = "通过会员Id查询收货地址")
   @PostMapping( path = "/receivingAddress/findByMemberId", consumes = "application/json")
   public List<ReceivingAddressDTO> findByMemberId(@RequestParam String memberId);

   @Operation(summary = "通过会员Ids查询收货地址")
   @PostMapping( path = "/receivingAddress/findByMemberIds", consumes = "application/json")
   List<ReceivingAddressDTO> findByMemberIds(@RequestBody Collection<String> memberIds);

   @Operation(summary = "根据id查找收货地址")
   @PostMapping( path = "/receivingAddress/findById", consumes = "application/json")
   public ReceivingAddressDTO findById(@RequestParam String id);

   @Operation(summary = "根据ids批量查找收货地址")
   @PostMapping( path = "/receivingAddress/findByIds", consumes = "application/json")
   public List<ReceivingAddressDTO> findByIds(@RequestBody Collection<String> idList);

   @Operation(summary = "设置默认收货地址")
   @PostMapping( path = "/receivingAddress/setDefaultById", consumes = "application/json")
   public void setDefaultById(@RequestParam String id,@RequestParam String memberId,@RequestParam String operator);


   @Operation(summary = "根据id删除收货地址")
   @PostMapping( path = "/receivingAddress/deleteById", consumes = "application/json")
   public void deleteById(@RequestParam String id,@RequestParam String operator);


   @Operation(summary = "根据会员id获取默认收货地址（企业员工则根据企业id获取默认收货地址）")
   @PostMapping( path = "/receivingAddress/getDefaultByMemberId", consumes = "application/json")
   public ReceivingAddressDTO getDefaultByMemberId(@RequestParam String memberId);


   @Operation(summary = "查询某会员的所有收货地址")
   @PostMapping( path = "/receivingAddress/findPageByMemberId", consumes = "application/json")
   public PageInfo<ReceivingAddressDTO> findPageByMemberId(@RequestBody PageQuery<ReceivingAddressQueryDTO> pageQuery);

   @Operation(summary = "查询erp收货地址信息")
   @PostMapping( path = "/receivingAddress/findErpInfo", consumes = "application/json")
   public List<ReceivingAddressErpInfoDTO> findErpInfo(@RequestBody ReceivingAddressErpInfoDTO dto);


   @Operation(summary = "保存erp收货地址信息")
   @PostMapping( path = "/receivingAddress/saveErpInfo", consumes = "application/json")
   public void saveErpInfo(@RequestBody ReceivingAddressErpInfoDTO dto);

   @Operation(summary = "查询erp收货地址关系表信息")
   @PostMapping( path = "/receivingAddress/findErpInfoBySellerIdAndBuyerId", consumes = "application/json")
   public List<ReceivingAddressErpInfoDTO> findErpInfoBySellerIdAndBuyerId(@RequestParam String sellerId,@RequestParam String buyerId);

   @Operation(summary = "保存erp收货地址信息")
   @PostMapping( path = "/receivingAddress/updateErpInfo", consumes = "application/json")
   public void updateErpInfo(@RequestBody ReceivingAddressErpInfoUpdateDTO dto);

   @Operation(summary = "查询erp卸货点列表")
   @PostMapping( path = "/receivingAddress/queryERPAddressList", consumes = "application/json")
   public List<ERPAddressListDTO> queryERPAddressList(@RequestBody ERPAddressQueryDTO erpAddressQueryDTO);

   @Operation(summary = "根据ids批量查找收货地址简化信息")
   @PostMapping( path = "/receivingAddress/findSimpleByIds", consumes = "application/json")
   public List<ReceivingAddressSimpleDTO> findSimpleByIds(@RequestBody Collection<String> ids);

   @Operation(summary = "查询码头Id是否绑定收货地址")
   @PostMapping( path = "/receivingAddress/isWharfIdBindingAddress", consumes = "application/json")
   public Boolean isWharfIdBindingAddress(@RequestParam String wharfId);

   @Operation(summary = "查询地址是否已同步ERP")
   @PostMapping( path = "/receivingAddress/hasERPAddress", consumes = "application/json")
   boolean hasERPAddress(@RequestBody ReceivingAddressMapQueryDTO queryDTO);

   @Operation(summary = "查询收货地址是否绑定ERP码头")
   @PostMapping( path = "/receivingAddress/hasERPWharf", consumes = "application/json")
   boolean hasERPWharf(@RequestParam String addressId, @RequestParam String sellerId);


   @Operation(summary = "ERP码头管理列表")
   @PostMapping( path = "/receivingAddress/queryErpWharfListByQuery", consumes = "application/json")
   PageInfo<ReceivingAddressMapDTO> queryErpWharfListByQuery(@RequestBody ReceivingAddressMapQueryDTO dto);

   @Operation(summary = "修改ERP码头名称")
   @PostMapping( path = "/receivingAddress/updateErpWharf", consumes = "application/json")
   void updateErpWharf(@RequestBody ReceivingAddressMapDTO dto);

   @Operation(summary = "通过码头名称模糊搜索ERP码头")
   @PostMapping( path = "/receivingAddress/queryErpWharfList", consumes = "application/json")
   List<ReceivingAddrMapWharfDTO> queryErpWharfList(@RequestBody ReceivingAddressMapQueryDTO dto);

	
   @Operation(summary = "根据卖家ID模糊查询买家地址")
   @PostMapping( path = "/receivingAddress/queryAddressBySellerId", consumes = "application/json")
   public List<ReceivingAddressMapDTO> queryAddressBySellerId(@RequestBody ReceivingAddressMapQueryDTO dto);

   @Operation(summary = "通过会员码头获取收货地址")
   @PostMapping( path = "/receivingAddress/getReceivingAddressByWharfId", consumes = "application/json")
   ReceivingAddressDTO getReceivingAddressByWharfId(@RequestBody ReceivingAddressDTO dto);

}
