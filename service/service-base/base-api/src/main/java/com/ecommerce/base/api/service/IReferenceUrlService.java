
package com.ecommerce.base.api.service;

import com.ecommerce.base.api.dto.ReferenceUrlDTO;
import com.ecommerce.base.api.dto.ReferenceUrlQueryDTO;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;


@FeignClient(name = "service-base")
@Tag(name = "IReferenceUrlService", description = "调用链管理")
public interface IReferenceUrlService {

   @Operation(summary = "分页查询调用链")
    @PostMapping( path = "/referenceUrl/list", consumes = "application/json")
    public PageInfo<ReferenceUrlDTO> list(@RequestBody ReferenceUrlQueryDTO referenceUrlQueryDTO);

   @Operation(summary = "通过url和projectName查询调用链")
    @PostMapping( path = "/referenceUrl/findByUrlAndProjectName", consumes = "application/json")
    public List<ReferenceUrlDTO> findByUrlAndProjectName(@RequestParam String url, @RequestParam String projectName);
}
