
package com.ecommerce.base.api.service;

import com.ecommerce.base.api.dto.region.RegionDTO;
import com.ecommerce.base.api.dto.region.RegionLabelValueDTO;
import com.ecommerce.base.api.dto.region.RegionSampleDTO;
import com.ecommerce.base.api.dto.wharf.WharfImportTemplateOptionDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;
import java.util.Set;


/**
 * @Created锛�Sat Sep 08 15:15:55 CST 2018
 * <AUTHOR>
 * @Version:2
 * @Description::null
*/

@FeignClient(name = "service-base")
@Tag(name = "IRegionService", description = "行政区域查询服务")
public interface IRegionService {


   @Operation(summary = "查询所有数据")
   @PostMapping( path = "/region/findProvinceByNameLike", consumes = "application/json")
   public List<RegionDTO> findProvinceByNameLike(@RequestParam String provinceNameLike);


   @Operation(summary = "查询所有数据并转换成label-Value格式")
   @PostMapping( path = "/region/findAll2LabelValue", consumes = "application/json")
   public List<RegionLabelValueDTO> findAll2LabelValue();

   @Operation(summary = "查询省市区数据并转换成label-Value格式")
   @PostMapping( path = "/region/findProvinceCityDistrict2LabelValue", consumes = "application/json")
   public List<RegionLabelValueDTO> findProvinceCityDistrict2LabelValue();

   @Operation(summary = "根据区域代码查询区/县或街道")
   @PostMapping( path = "/region/findByAdcodeAndLevel", consumes = "application/json")
   public List<RegionDTO> findByAdcodeAndLevel(@RequestParam String adcode,@RequestParam String level);


   @Operation(summary = "查询城市")
   @PostMapping( path = "/region/findCityByNameLike", consumes = "application/json")
   public List<RegionDTO> findCityByNameLike(@RequestParam String cityNameLike);


   @Operation(summary = "根据区域代码查询区/县或街道")
   @PostMapping( path = "/region/findByAdcodeAndLevelAndNameLike", consumes = "application/json")
   public List<RegionDTO> findByAdcodeAndLevelAndNameLike(@RequestParam String adcode,@RequestParam String level,@RequestParam String nameLike);


   @Operation(summary = "根据区关键字，并且返回子区域，功能类似该接口功能（https://lbs.amap.com/api/webservice/guide/api/district），但比它简单点")
   @PostMapping( path = "/region/findDistrict", consumes = "application/json")
   public List<RegionDTO> findDistrict(@RequestParam String keywords,@RequestParam int subdistrict,@RequestParam int pageSize,@RequestParam boolean needPolyline);


   @PostMapping( path = "/region/findAll", consumes = "application/json")
   public List<RegionDTO> findAll();

   @Operation(summary = "根据当前层级adcode查找下一层级的数据")
   @PostMapping( path = "/region/findByParentAdCode", consumes = "application/json")
   List<RegionSampleDTO> findByParentAdCode(@RequestParam String parentAdcode);

   @Operation(summary = "根据上层级adcode和当前层级的名称查找当前层级的adcode")
   @PostMapping( path = "/region/findAdCodeByParentAdCodeAndName", consumes = "application/json")
   String findAdCodeByParentAdCodeAndName(@RequestParam String parentAdcode,@RequestParam String name);

   @Operation(summary = "根据省份名称匹配查找城市的数据")
   @PostMapping( path = "/region/findCityByProvinceName", consumes = "application/json")
   List<RegionSampleDTO> findCityByProvinceName(@RequestParam String provinceNameLike);

   @Operation(summary = "查询所有省")
   @PostMapping( path = "/region/findAllProvince", consumes = "application/json")
   List<RegionDTO> findAllProvince();

   @Operation(summary = "根据code查询名称")
   @PostMapping( path = "/region/findNameByAdCode", consumes = "application/json")
   String findNameByAdCode(@RequestParam String adcode);

   @Operation(summary = "根据code批量查询名称")
   @PostMapping( path = "/region/findNameByAdCodeList", consumes = "application/json")
   public Map<String,String> findNameByAdCodeList(@RequestBody Set<String> adcodeList);

   @Operation(summary = "查询省市区街道数据并转换成label-Value格式")
   @PostMapping( path = "/region/findProvinceCityDistrictStreet2LabelValue", consumes = "application/json")
   public List<RegionLabelValueDTO> findProvinceCityDistrictStreet2LabelValue();

   @Operation(summary = "查询行政区域(批量上传码头调用）")
   @PostMapping( path = "/region/findRegionForWharfDTO", consumes = "application/json")
   public WharfImportTemplateOptionDTO findRegionForWharfDTO();
}
