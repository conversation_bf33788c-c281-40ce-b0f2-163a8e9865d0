
package com.ecommerce.base.api.service;

import com.ecommerce.base.api.dto.report.ReportInfoDTO;
import com.ecommerce.base.api.dto.report.ReportOptionDTO;
import com.ecommerce.common.result.ItemResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;


/**
 * @Created锛�Tue Aug 20 16:34:27 CST 2019
 * <AUTHOR>
 * @Version:2
 * @Description:null
*/
@FeignClient(name = "service-base")
@Tag(name = "IReportService", description = "报表服务")
public interface IReportService {


   @Operation(summary = "报表编码获取详情")
   @PostMapping( path = "/report/queryConfigByReportCode", consumes = "application/json")
   public ItemResult<ReportInfoDTO> queryConfigByReportCode(@RequestParam String reportCode);


   @Operation(summary = "报表下拉列表")
   @PostMapping( path = "/report/reportOptions", consumes = "application/json")
   public ItemResult<List<ReportOptionDTO>> reportOptions(@RequestBody List<String> roleList);


   @Operation(summary = "导出HTML")
   @PostMapping( path = "/report/exportToHTML", consumes = "application/json")
   public ItemResult<String> exportToHTML(@RequestBody Map<String,Object> params,@RequestParam String reportTemplateName);


   @Operation(summary = "导出Excel")
   @PostMapping( path = "/report/exportExcel", consumes = "application/json")
   public ItemResult<byte[]> exportExcel(@RequestBody Map<String,Object> params,@RequestParam String reportTemplateName);

   @Operation(summary = "报表绑定角色")
   @PostMapping( path = "/report/bindingReportAndRole", consumes = "application/json")
   public ItemResult<Void> bindingReportAndRole(@RequestParam String reportCode,@RequestBody List<String> roleList);

}
