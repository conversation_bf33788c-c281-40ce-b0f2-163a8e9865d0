package com.ecommerce.base.api.service;

import com.ecommerce.base.api.dto.retry.RetryDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


@FeignClient(name = "service-base")
@Tag(name = "IRetryService", description = "业务重试服务")
public interface IRetryService {

   @Operation(summary = "业务重试服务")
   @PostMapping( path = "/retry/triggerRetry", consumes = "application/json")
   public void triggerRetry(@RequestBody RetryDTO retryDTO);

}
