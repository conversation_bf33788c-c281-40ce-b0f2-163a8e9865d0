
package com.ecommerce.base.api.service;

import com.ecommerce.base.api.dto.AccountRoleIdListDTO;
import com.ecommerce.base.api.dto.authRes.ResCheckDTO;
import com.ecommerce.base.api.dto.authRes.RoleAuthenticateDTO;
import com.ecommerce.base.api.dto.role.AccountRoleDTO;
import com.ecommerce.base.api.dto.role.HasAnyRoleDTO;
import com.ecommerce.base.api.dto.role.MemberRoleDTO;
import com.ecommerce.base.api.dto.role.RoleDTO;
import com.ecommerce.base.api.dto.role.RoleQueryDTO;
import com.ecommerce.base.api.enums.BaseRoleTypeEnum;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;
import java.util.Set;


/**
 * @Created锛�Wed Mar 20 10:36:21 CST 2019
 * <AUTHOR>
 * @Version:2
 * @Description::角色相关service
*/

@FeignClient(name = "service-base")
@Tag(name = "IRoleService", description = "角色相关service")
public interface IRoleService {


   @Operation(summary = "null")
   @PostMapping( path = "/role/findByRoleNameList", consumes = "application/json")
   public List<RoleDTO> findByRoleNameList(@RequestBody List<String> arg0);


   @Operation(summary = "获取会员可用的权限")
   @PostMapping( path = "/role/getRoleByMemberId", consumes = "application/json")
   public List<RoleDTO> getRoleByMemberId(@RequestParam String arg0);

   @Operation(summary = "获取会员可用的权限")
   @PostMapping( path = "/role/getRoleByMemberId2", consumes = "application/json")
   public MemberRoleDTO getRoleByMemberId2(@RequestBody MemberRoleDTO dto);

   @Operation(summary = "更新账户角色关系")
   @PostMapping( path = "/role/updateAccountRole", consumes = "application/json")
   public void updateAccountRole(@RequestBody AccountRoleDTO arg0);


   @Operation(summary = "变更主账号角色")
   @PostMapping( path = "/role/updateMainAccountRoleType", consumes = "application/json")
   public void updateMainAccountRoleType(@RequestParam String arg0,@RequestParam String arg1,@RequestBody List<BaseRoleTypeEnum> arg2,@RequestParam String arg3);


   @Operation(summary = "筛选出含有roleId的所有accountId")
   @PostMapping( path = "/role/accountRoleFilter", consumes = "application/json")
   public List<String> accountRoleFilter(@RequestBody AccountRoleIdListDTO arg0);


   @Operation(summary = "变更承运商主账号 更新角色")
   @PostMapping( path = "/role/updateCarrierMainAccount", consumes = "application/json")
   public void updateCarrierMainAccount(@RequestParam String arg0,@RequestParam String arg1,@RequestParam String arg2);


   @Operation(summary = "给一个账户设置为企业卖家角色")
   @PostMapping( path = "/role/setEnterpriseSeller2Account", consumes = "application/json")
   public void setEnterpriseSeller2Account(@RequestParam String arg0,@RequestParam String arg1,@RequestParam String arg2);


   @Operation(summary = "变更企业买家主账号 更新角色")
   @PostMapping( path = "/role/updateEnterpriseBuyerMainAccount", consumes = "application/json")
   public void updateEnterpriseBuyerMainAccount(@RequestParam String arg0,@RequestParam String arg1,@RequestParam String arg2);


   @Operation(summary = "给一个账户设置为企业买家角色")
   @PostMapping( path = "/role/setEnterpriseBuyer2Account", consumes = "application/json")
   public void setEnterpriseBuyer2Account(@RequestParam String arg0,@RequestParam String arg1,@RequestParam String arg2);


   @Operation(summary = "获取所有角色，不分页，按会员id查,会员授权使用")
   @PostMapping( path = "/role/getAllRoleListAndMemberSelected", consumes = "application/json")
   public List<RoleDTO> getAllRoleListAndMemberSelected(@RequestParam String arg0);


   @Operation(summary = "根据账户id查询已分配的角色")
   @PostMapping( path = "/role/getRoleByAccountId", consumes = "application/json")
   public List<RoleDTO> getRoleByAccountId(@RequestParam String arg0);

   @Operation(summary = "根据账户id查询已分配的角色")
   @PostMapping( path = "/role/getRoleByAccountId2", consumes = "application/json")
   public AccountRoleDTO getRoleByAccountId2(@RequestBody AccountRoleDTO dto);

   @Operation(summary = "根据账户id查询可访问的web端")
   @PostMapping( path = "/role/getPlatformByAccountId", consumes = "application/json")
   public Set<String>  getPlatformByAccountId(@RequestParam String accountId);

   @Operation(summary = "根据账户id批量查询可访问的web端")
   @PostMapping( path = "/role/getPlatformByAccountIds", consumes = "application/json")
   public Map<String,Set<String>> getPlatformByAccountIds(@RequestBody Set<String> accountIds);

   @Operation(summary = "给一个账户设置为供应商角色")
   @PostMapping( path = "/role/setSupplier2Account", consumes = "application/json")
   public void setSupplier2Account(@RequestParam String arg0,@RequestParam String arg1,@RequestParam String arg2);


   @Operation(summary = "给一个账户设置为个人司机角色")
   @PostMapping( path = "/role/setIndividualDriver2Account", consumes = "application/json")
   public void setIndividualDriver2Account(@RequestParam String arg0,@RequestParam String arg1);

   @Operation(summary = "给一个账户设置为个体船东角色")
   @PostMapping( path = "/role/setIndividualShipowner2Account", consumes = "application/json")
   public void setIndividualShipowner2Account(@RequestParam String arg0,@RequestParam String arg1);


   @Operation(summary = "给一个账户设置为承运商角色")
   @PostMapping( path = "/role/setCarrier2Account", consumes = "application/json")
   public void setCarrier2Account(@RequestParam String memberId,@RequestParam String mainAccountId,@RequestParam(required = false) String carrierType,@RequestParam String operator);


   @Operation(summary = "创建role")
   @PostMapping( path = "/role/createRole", consumes = "application/json")
   public Integer createRole(@RequestBody RoleDTO arg0);


   @Operation(summary = "更新角色")
   @PostMapping( path = "/role/updateRole", consumes = "application/json")
   public void updateRole(@RequestBody RoleDTO arg0);


   @PostMapping( path = "/role/findByRoleName", consumes = "application/json")
   public RoleDTO findByRoleName(@RequestParam String arg0);


   @Operation(summary = "添加会员角色关系")
   @PostMapping( path = "/role/addMemberRole", consumes = "application/json")
   public void addMemberRole(@RequestBody MemberRoleDTO arg0);


   @Operation(summary = "更新会员角色")
   @PostMapping( path = "/role/updateMemberRole", consumes = "application/json")
   public void updateMemberRole(@RequestBody MemberRoleDTO arg0);

   @Operation(summary = "移除会员角色(如果会员角色表有授权)")
   @PostMapping( path = "/role/removeMemberRole", consumes = "application/json")
   public void removeMemberRole(@RequestBody MemberRoleDTO arg0);

   @Operation(summary = "获取所有角色列表")
   @PostMapping( path = "/role/findAll", consumes = "application/json")
   public PageInfo<RoleDTO> findAll(@RequestBody RoleQueryDTO arg0);


   @Operation(summary = "添加账户角色关系")
   @PostMapping( path = "/role/addAccountRole", consumes = "application/json")
   public void addAccountRole(@RequestBody AccountRoleDTO arg0);


   @Operation(summary = "查询单个角色")
   @PostMapping( path = "/role/findByIds", consumes = "application/json")
   public List<RoleDTO> findByIds(@RequestBody List<Integer> arg0);


   @Operation(summary = "根据id删除角色")
   @PostMapping( path = "/role/deleteRoleById", consumes = "application/json")
   public void deleteRoleById(@RequestParam Integer arg0,@RequestParam String arg1);


   @Operation(summary = "设置角色可用")
   @PostMapping( path = "/role/enableRole", consumes = "application/json")
   public void enableRole(@RequestParam Integer arg0,@RequestParam String arg1);


   @Operation(summary = "判断账号是否拥有角色")
   @PostMapping( path = "/role/hasAnyRole", consumes = "application/json")
   public boolean hasAnyRole(@RequestBody HasAnyRoleDTO arg0);


   @PostMapping( path = "/role/findById", consumes = "application/json")
   public RoleDTO findById(@RequestParam Integer arg0);

   @Operation(summary = "查询单个角色和其权限")
   @PostMapping( path = "/role/findRoleAndPermission", consumes = "application/json")
   public RoleAuthenticateDTO findRoleAndPermission(@RequestParam Integer roleId);


   @Operation(summary = "设置角色不可用")
   @PostMapping( path = "/role/disableRole", consumes = "application/json")
   public void disableRole(@RequestParam Integer arg0,@RequestParam String arg1);


   @Operation(summary = "获取会员角色，不分页，按会员id、账户id查,子账户授权使用")
   @PostMapping( path = "/role/getMemberRoleListAndAccountSelected", consumes = "application/json")
   public List<RoleDTO> getMemberRoleListAndAccountSelected(@RequestParam String arg0,@RequestParam String arg1);


   @Operation(summary = "变更企业卖家主账号 更新角色")
   @PostMapping( path = "/role/updateEnterpriseSellerMainAccount", consumes = "application/json")
   public void updateEnterpriseSellerMainAccount(@RequestParam String arg0,@RequestParam String arg1,@RequestParam String arg2);

   @Operation(summary = "校验是否有资源权限")
   @PostMapping( path = "/role/checkRes", consumes = "application/json")
   Boolean checkRes(@RequestBody ResCheckDTO dto);

}
