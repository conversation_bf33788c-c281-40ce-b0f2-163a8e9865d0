
package com.ecommerce.base.api.service;

import com.ecommerce.base.api.dto.SaleRegionErpInfo.SaleRegionErpInfoDTO;
import com.ecommerce.base.api.dto.SaleRegionErpInfo.SaleRegionErpInfoKeywordQueryDTO;
import com.ecommerce.base.api.dto.SaleRegionErpInfo.SaleRegionErpInfoQueryDTO;
import com.ecommerce.base.api.dto.saleregion.AccountSaleRegionUpdateDTO;
import com.ecommerce.base.api.dto.saleregion.FindDetailRegionbyLevelDTO;
import com.ecommerce.base.api.dto.saleregion.MemberApproveSaleRegionCheckDTO;
import com.ecommerce.base.api.dto.saleregion.RegionQueryDTO;
import com.ecommerce.base.api.dto.saleregion.SaleLevelDTO;
import com.ecommerce.base.api.dto.saleregion.SaleRegionAddDTO;
import com.ecommerce.base.api.dto.saleregion.SaleRegionCondDTO;
import com.ecommerce.base.api.dto.saleregion.SaleRegionDTO;
import com.ecommerce.base.api.dto.saleregion.SaleRegionOption;
import com.ecommerce.base.api.dto.saleregion.SaleRegionRegionQueryDTO;
import com.ecommerce.base.api.dto.saleregion.SaleRegionRelationDTO;
import com.ecommerce.base.api.dto.saleregion.SaleRegionSampleDTO;
import com.ecommerce.base.api.dto.saleregion.SaleRegionStoreAddDTO;
import com.ecommerce.base.api.dto.saleregion.SaleRegionStoreDeleteDTO;
import com.ecommerce.base.api.dto.saleregion.SaleRegionTreeDTO;
import com.ecommerce.base.api.dto.saleregion.SaleRegionUpdateDTO;
import com.ecommerce.base.api.dto.saleregion.SaleRgionRelationAddDTO;
import com.ecommerce.base.api.dto.saleregion.SaleStoreDTO;
import com.ecommerce.base.api.dto.saleregion.SetDefaultStoreDTO;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Set;


/**
 * @Created锛�Sat Sep 08 15:15:56 CST 2018
 * <AUTHOR>
 * @Version:2
 * @Description::1、销售区域为一个树行结构数据,表：ba_sale_region<br/> 2、每个销售区域节点下关联一个或者多个仓库信息，表：ba_sale_region_store<br/> 2、每个销售区域节点下关联一个或者多个行政区域信息，表：ba_sale_region_relation<br/>
*/

@FeignClient(name = "service-base")
@Tag(name = "ISaleRegionService", description = "1、销售区域为一个树行结构数据,表：ba_sale_region<br/> 2、每个销售区域节点下关联一个或者多个仓库信息，表：ba_sale_region_store<br/> 2、每个销售区域节点下关联一个或者多个行政区域信息，表：ba_sale_region_relation<br/>")
public interface ISaleRegionService {


   @Operation(summary = "查询指定区域下一级的区域信息(不含仓库信息)")
   @PostMapping( path = "/saleRegion/findOnlyChildsById", consumes = "application/json")
   public List<SaleRegionSampleDTO> findOnlyChildsById(@RequestParam String id);


   @Operation(summary = "添加销售区域下的行政区域")
   @PostMapping( path = "/saleRegion/addRegionRelation", consumes = "application/json")
   public void addRegionRelation(@RequestBody List<SaleRgionRelationAddDTO> saleRgionRelationAddDTOList,@RequestParam String operator);


   @Operation(summary = "删除销售区域下的行政区域")
   @PostMapping( path = "/saleRegion/deleteRegionRelation", consumes = "application/json")
   public void deleteRegionRelation(@RequestBody List<String> ids,@RequestParam String operator);


   @Operation(summary = "查询一个会员下的销售区域层级")
   @PostMapping( path = "/saleRegion/findSaleLevelByMemberId", consumes = "application/json")
   public List<SaleLevelDTO> findSaleLevelByMemberId(@RequestParam String memberId);

   @Operation(summary = "查询一个账号下某个层级的销售区域（包括仓库）")
   @PostMapping( path = "/saleRegion/findDetailRegionbyLevelAndAccountId", consumes = "application/json")
   public List<SaleRegionDTO> findDetailRegionbyLevelAndAccountId(@RequestParam Integer level,@RequestParam String accountId);

   @Operation(summary = "根据销售区域id 查询其下和所有子区域下的行政区域列表（注意过滤重复）")
   @PostMapping( path = "/saleRegion/findAllRgionRelationById", consumes = "application/json")
   public List<SaleRegionRelationDTO> findAllRgionRelationById(@RequestParam String id);


   @Operation(summary = "查询一个会员下某个层级的销售区域（包括仓库）")
   @PostMapping( path = "/saleRegion/findDetailRegionbyLevel", consumes = "application/json")
   public List<SaleRegionDTO> findDetailRegionbyLevel(@RequestParam Integer level,@RequestParam String memberId);

   @Operation(summary = "查询一个会员下某个层级的销售区域（包括仓库）,并过滤")
   @PostMapping( path = "/saleRegion/findDetailRegionbyLevelAndFilter", consumes = "application/json")
   public List<SaleRegionDTO> findDetailRegionbyLevelAndFilter(@RequestBody FindDetailRegionbyLevelDTO dto);


   @Operation(summary = "查询一个会员下的所有销售区域(含仓库信息)")
   @PostMapping( path = "/saleRegion/findAllDetailByMemberId", consumes = "application/json")
   public List<SaleRegionDTO> findAllDetailByMemberId(@RequestParam String memberId);


   @Operation(summary = "查询一个会员下的所有销售区域(不含仓库信息)")
   @PostMapping( path = "/saleRegion/findAllSampleByMemberId", consumes = "application/json")
   public List<SaleRegionSampleDTO> findAllSampleByMemberId(@RequestParam String memberId);


   @Operation(summary = "表：ba_account_sale_region 添加或删除人员-销售区域关系")
   @PostMapping( path = "/saleRegion/updateAccountSaleRegion", consumes = "application/json")
   public void updateAccountSaleRegion(@RequestBody AccountSaleRegionUpdateDTO accountSaleRegionUpdateDTO,@RequestParam String operator);


   @Operation(summary = "根据销售区域id 查询其下的行政区域列表")
   @PostMapping( path = "/saleRegion/findRgionRelationById", consumes = "application/json")
   public List<SaleRegionRelationDTO> findRgionRelationById(@RequestParam String id);


   @Operation(summary = "查询单个销售区域信息(含0个或多个仓库和行政信息)")
   @PostMapping( path = "/saleRegion/findById", consumes = "application/json")
   public SaleRegionDTO findById(@RequestParam String id);


   @Operation(summary = "更新销售区域，整体更新")
   @PostMapping( path = "/saleRegion/updateSaleRegion", consumes = "application/json")
   public void updateSaleRegion(@RequestBody SaleRegionUpdateDTO saleRegionUpdateDTO,@RequestParam String operator);


   @Operation(summary = "添加销售区域(含0个或多个仓库信息)")
   @PostMapping( path = "/saleRegion/addSaleRegion", consumes = "application/json")
   public void addSaleRegion(@RequestBody SaleRegionAddDTO saleRegionAddDTO,@RequestParam String operator);


   @Operation(summary = "删除销售区域下的仓库")
   @PostMapping( path = "/saleRegion/deleteStore", consumes = "application/json")
   public void deleteStore(@RequestBody SaleRegionStoreDeleteDTO saleRegionStoreDeleteDTO,@RequestParam String operator);


   @Operation(summary = "删除销售区域（会同时删除子区域），并且删除对应的仓库和区域关联关系")
   @PostMapping( path = "/saleRegion/deleteSaleRegion", consumes = "application/json")
   public void deleteSaleRegion(@RequestParam String id,@RequestParam String operator);


   @Operation(summary = "表：ba_account_sale_region 查询人员-销售区域关系表关系")
   @PostMapping( path = "/saleRegion/findByAccountId", consumes = "application/json")
   public List<SaleRegionDTO> findByAccountId(@RequestParam String accountId);

   @Operation(summary = "表：ba_account_sale_region 查询人员-销售区域关系表关系-a")
   @PostMapping( path = "/saleRegion/findByAccountIdWithDelFlg", consumes = "application/json")
   public List<SaleRegionDTO> findByAccountIdWithDelFlg(@RequestParam String accountId);

   @Operation(summary = "查询指定区域下一级的区域信息(含仓库信息)")
   @PostMapping( path = "/saleRegion/findChildsById", consumes = "application/json")
   public List<SaleRegionDTO> findChildsById(@RequestParam String id);


   @Operation(summary = "根据销售区域id 查询其下和所有子区域下的仓库列表（注意过滤重复）")
   @PostMapping( path = "/saleRegion/findAllStoreById", consumes = "application/json")
   public List<SaleStoreDTO> findAllStoreById(@RequestParam String id);

   @Operation(summary = "查询卖家所有仓库")
   @PostMapping( path = "/saleRegion/findStoreBySellerId", consumes = "application/json")
   public List<SaleStoreDTO> findStoreBySellerId(@RequestParam String sellerId);

   @Operation(summary = "获取默认仓库")
   @PostMapping( path = "/saleRegion/getDefaultStore", consumes = "application/json")
   public SaleStoreDTO getDefaultStore(@RequestParam String id);


   @Operation(summary = "查询单个销售区域信息(不含0个或多个仓库和行政信息)")
   @PostMapping( path = "/saleRegion/findSampleById", consumes = "application/json")
   public SaleRegionSampleDTO findSampleById(@RequestParam String id);

   @Operation(summary = "查询批量的销售区域信息")
   @PostMapping( path = "/saleRegion/findSampleByIdList", consumes = "application/json")
   public List<SaleRegionSampleDTO> findSampleByIdList(@RequestParam List<String> saleRegionIdList);


   @Operation(summary = "设置默认仓库")
   @PostMapping( path = "/saleRegion/setDefaultStore", consumes = "application/json")
   public void setDefaultStore(@RequestBody SetDefaultStoreDTO dto,@RequestParam String operator);

   @Operation(summary = "清除默认仓库")
   @PostMapping( path = "/saleRegion/cancelDefaultStore", consumes = "application/json")
   public void cancelDefaultStore(@RequestParam String id,@RequestParam String saleRegionId,@RequestParam String operator);

   @Operation(summary = "根据销售区域id 查询其下的仓库列表")
   @PostMapping( path = "/saleRegion/findStoreById", consumes = "application/json")
   public List<SaleStoreDTO> findStoreById(@RequestParam String id);


   @Operation(summary = "添加销售区域下的仓库")
   @PostMapping( path = "/saleRegion/addStore", consumes = "application/json")
   public void addStore(@RequestBody SaleRegionStoreAddDTO saleRegionStoreAddDTO,@RequestParam String operator);

   @Operation(summary = "查询一个会员下的所有销售区域树形结构(不含仓库信息)")
   @PostMapping( path = "/saleRegion/findTreeByMemberId", consumes = "application/json")
   public List<SaleRegionTreeDTO> findTreeByMemberId(@RequestParam String memberId);


   @Operation(summary = "翻页查找销售区域erp信息表")
   @PostMapping( path = "/saleRegion/findAllErpInfo", consumes = "application/json")
   public PageInfo<SaleRegionErpInfoDTO> findAllErpInfo(@RequestBody SaleRegionErpInfoQueryDTO dto);

   @Operation(summary = "查找单个销售区域erp信息表")
   @PostMapping( path = "/saleRegion/findErpInfo", consumes = "application/json")
   public SaleRegionErpInfoDTO findErpInfo(@RequestParam String memberId,@RequestParam String erpOrgCode);

   @Operation(summary = "根据关键字查找销售区域erp信息表")
   @PostMapping( path = "/saleRegion/findErpInfoByKeyword", consumes = "application/json")
   public List<SaleRegionErpInfoDTO> findErpInfoByKeyword(@RequestBody SaleRegionErpInfoKeywordQueryDTO dto);

   @Operation(summary = "同步销售区域erp信息")
   @PostMapping( path = "/saleRegion/syncErpInfo", consumes = "application/json")
   public Boolean syncErpInfo(@RequestParam String memberId ,@RequestParam String operatorId);

   @Operation(summary = "根据会员id和erpCode查询")
   @PostMapping( path = "/saleRegion/findByMemberIdAndErpCode", consumes = "application/json")
   public SaleRegionDTO findByMemberIdAndErpCode(@RequestParam String memberId,@RequestParam String erpCode);

   @Operation(summary = "销售区域的批量查询")
   @PostMapping( path = "/saleRegion/batchQuerySaleRegion", consumes = "application/json")
   public List<SaleRegionSampleDTO> batchQuerySaleRegion(@RequestBody SaleRegionCondDTO saleRegionCondDTO);

   @Operation(summary = "查询销售区域id->名字映射")
   @PostMapping( value="/saleRegion/querySaleRegionOptions", consumes = "application/json")
   public List<SaleRegionOption> querySaleRegionOptions(@RequestBody Set<String> saleRegionIdSet);

   @Operation(summary = "根据行政区域查询销售区域")
   @PostMapping( value="/saleRegion/findByRegion", consumes = "application/json")
   List<SaleRegionSampleDTO> findByRegion(RegionQueryDTO dto);
   
   @Operation(summary = "根据名称模糊查询一级销售区域")
   @PostMapping( value="/saleRegion/querySaleRegionBySaleRegionName", consumes = "application/json")
   public List<SaleRegionOption> querySaleRegionBySaleRegionName(@RequestBody SaleRegionOption dto);

   @Operation(summary = "会员注册审批使用，判断哪些会员与企业买家注册时的省代码有交集，返回有交集的省代码")
   @PostMapping( value="/saleRegion/hasIntersection", consumes = "application/json")
   public List<String> hasIntersection(@RequestBody MemberApproveSaleRegionCheckDTO dto);

   @Operation(summary = "查询会员的销售区域覆盖的省代码")
   @PostMapping( value="/saleRegion/findRegionByMemberId", consumes = "application/json")
   public List<String> findRegionByMemberId(@RequestBody SaleRegionRegionQueryDTO dto);
}
