package com.ecommerce.base.api.service;

import com.ecommerce.base.api.dto.saleregion.RegionQueryDTO;
import com.ecommerce.base.api.dto.saleregion.SaleRegion2DTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(name ="service-base")
public interface ISaleRegionV2Service
{
    @PostMapping("/v2/saleRegion/getSaleRegionId")
    public List<SaleRegion2DTO> getSaleRegionId(@RequestBody RegionQueryDTO query);
}
