
package com.ecommerce.base.api.service;

import com.ecommerce.base.api.dto.authRes.PageDTO;
import com.ecommerce.base.api.dto.authRes.ServiceDTO;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;


/**
 * @Created锛�Fri Mar 08 11:25:40 CST 2019
 * <AUTHOR>
 * @Version:2
 * @Description::资源管理_服务
*/

@FeignClient(name = "service-base")
@Tag(name = "IServeiceManagerService", description = "资源管理_服务")
public interface IServeiceManagerService {


   @Operation(summary = "删除服务")
   @PostMapping( path = "/serveiceManager/delService", consumes = "application/json")
   public int delService(@RequestParam String id,@RequestParam String operator);


   @Operation(summary = "通过主键查询服务")
   @PostMapping( path = "/serveiceManager/findServiceById", consumes = "application/json")
   public ServiceDTO findServiceById(@RequestParam String id);


   @Operation(summary = "添加服务")
   @PostMapping( path = "/serveiceManager/createService", consumes = "application/json")
   public ServiceDTO createService(@RequestBody ServiceDTO service);


   @Operation(summary = "修改服务")
   @PostMapping( path = "/serveiceManager/updateService", consumes = "application/json")
   public int updateService(@RequestBody ServiceDTO service);


   @Operation(summary = "检查重复")
   @PostMapping( path = "/serveiceManager/checkExist", consumes = "application/json")
   public boolean checkExist(@RequestParam String code);


   @Operation(summary = "通过角色查询服务")
   @PostMapping( path = "/serveiceManager/findServiceByRoleId", consumes = "application/json")
   public List<ServiceDTO> findServiceByRoleId(@RequestParam String roleId);


   @Operation(summary = "条件查询服务")
   @PostMapping( path = "/serveiceManager/findServiceByCondiftion", consumes = "application/json")
   public PageInfo<ServiceDTO> findServiceByCondiftion(@RequestBody ServiceDTO service);


   @Operation(summary = "通过角色查询已授权服务")
   @PostMapping( path = "/serveiceManager/findSelectServiceByRoleId", consumes = "application/json")
   public List<ServiceDTO> findSelectServiceByRoleId(@RequestParam String roleId);


   @Operation(summary = "通过页面查询服务")
   @PostMapping( path = "/serveiceManager/findServiceByPage", consumes = "application/json")
   public List<ServiceDTO> findServiceByPage(@RequestParam String pageId);


   @Operation(summary = "通过服务查询所属页面")
   @PostMapping( path = "/serveiceManager/findPageByService", consumes = "application/json")
   public List<PageDTO> findPageByService(@RequestParam String serviceId);

   @Operation(summary = "通过关键字查询服务（服务代码、服务名称、url、url说明）")
   @PostMapping( path = "/serveiceManager/findByKeyword", consumes = "application/json")
   public List<ServiceDTO> findByKeyword(@RequestParam String keyword);


}
