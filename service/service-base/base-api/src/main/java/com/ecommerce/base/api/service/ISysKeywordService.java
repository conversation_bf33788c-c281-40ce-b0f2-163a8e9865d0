package com.ecommerce.base.api.service;

import com.ecommerce.base.api.dto.keyword.SysKeywordAddDTO;
import com.ecommerce.base.api.dto.keyword.SysKeywordCondDTO;
import com.ecommerce.base.api.dto.keyword.SysKeywordRemoveDTO;
import com.ecommerce.base.api.dto.keyword.SysKeywordResultDTO;
import com.ecommerce.base.api.dto.keyword.SysKeywordUpdateDTO;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;


/**
 * 关键词过滤服务
 *  ISysKeywordService
 *
 * <AUTHOR>
 */
@FeignClient(name = "service-base")
@Tag(name = "ISysKeywordService", description = "关键词过滤服务")
public interface ISysKeywordService {

    /**
     * 新增关键词过滤
     * @param sysKeywordAddDTO
     * @return
     */
    @Operation(summary = "新增关键词过滤")
    @PostMapping( path = "/sysKeyword/addKeyword", consumes = "application/json")
    public ItemResult<Integer> addKeyword(@RequestBody SysKeywordAddDTO sysKeywordAddDTO);

    /**
     * 删除关键词过滤
     * @param sysKeywordRemoveDTO
     * @return
     */
    @Operation(summary = "删除关键词过滤")
    @PostMapping( path = "/sysKeyword/removeKeyword", consumes = "application/json")
    public ItemResult<Void> removeKeyword(@RequestBody SysKeywordRemoveDTO sysKeywordRemoveDTO);

    /**
     * ID查询关键词
     * @param id
     * @return
     */
    @Operation(summary = "ID查询关键词")
    @PostMapping( path = "/sysKeyword/queryKeywordById", consumes = "application/json")
    public ItemResult<SysKeywordResultDTO> queryKeywordById(@RequestParam Integer id);

    /**
     * 条件查询关键词
     * @param sysKeywordCondDTO
     * @return
     */
    @Operation(summary = "条件查询关键词")
    @PostMapping( path = "/sysKeyword/queryKeywordByCond", consumes = "application/json")
    public ItemResult<PageData<SysKeywordResultDTO>> queryKeywordByCond(@RequestBody SysKeywordCondDTO sysKeywordCondDTO);

    /**
     * 修改关键词
     * @param sysKeywordUpdateDTO
     * @return
     */
    @Operation(summary = "修改关键词")
    @PostMapping( path = "/sysKeyword/modifyKeyword", consumes = "application/json")
    public ItemResult<Void> modifyKeyword(@RequestBody SysKeywordUpdateDTO sysKeywordUpdateDTO);

}
