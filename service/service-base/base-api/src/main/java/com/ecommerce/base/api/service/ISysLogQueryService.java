
package com.ecommerce.base.api.service;

import com.ecommerce.base.api.dto.sysLog.SysLogDTO;
import com.ecommerce.base.api.dto.sysLog.SysLogQueryDTO;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;


/**
 * @Created锛�Fri Dec 28 16:16:57 CST 2018
 * <AUTHOR>
 * @Version:2
 * @Description::null
*/

@FeignClient(name = "service-base")
@Tag(name = "ISysLogQueryService", description = "null")
public interface ISysLogQueryService {


   @Operation(summary = "查询单条日志，含异常堆栈信息")
   @PostMapping( path = "/sysLogQuery/findDetailById", consumes = "application/json")
   public SysLogDTO findDetailById(@RequestParam String id);


   @Operation(summary = "查询单条日志")
   @PostMapping( path = "/sysLogQuery/findById", consumes = "application/json")
   public SysLogDTO findById(@RequestParam String id);


   @Operation(summary = "根据条件翻页查询")
   @PostMapping( path = "/sysLogQuery/findAll", consumes = "application/json")
   public PageInfo<SysLogDTO> findAll(@RequestBody SysLogQueryDTO arg0);


   @Operation(summary = "根据条件翻页查询(异常日志)")
   @PostMapping( path = "/sysLogQuery/findAllException", consumes = "application/json")
   public PageInfo<SysLogDTO> findAllException(@RequestBody SysLogQueryDTO arg0);



}
