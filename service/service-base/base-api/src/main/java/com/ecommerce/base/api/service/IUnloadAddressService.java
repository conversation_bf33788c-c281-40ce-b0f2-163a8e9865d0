
package com.ecommerce.base.api.service;

import com.ecommerce.base.api.dto.unloadaddress.UnloadAddressDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;


/**
 * @Created锛�Wed May 22 10:51:31 CST 2019
 * <AUTHOR>
 * @Version:2
 * @Description:
*/

@FeignClient(name = "service-base")
@Tag(name = "IUnloadAddressService", description = "ERP卸货点")
public interface IUnloadAddressService {


   @Operation(summary = "根据卖家id和买家id查询ERP卸货地")
   @PostMapping( path = "/unloadAddress/findBySellerIdAndBuyerId", consumes = "application/json")
   public List<UnloadAddressDTO> findBySellerIdAndBuyerId(@RequestParam String sellerId,@RequestParam String buyerId,@RequestParam(required = false) String keyword);


   @Operation(summary = "同步ERP卸货地")
   @PostMapping( path = "/unloadAddress/syncERPUnloadAddress", consumes = "application/json")
   public void syncERPUnloadAddress(@RequestParam String sellerId,@RequestParam String buyerId,@RequestParam String operator);



}
