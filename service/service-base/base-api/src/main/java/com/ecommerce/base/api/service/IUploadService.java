package com.ecommerce.base.api.service;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

@FeignClient(value = "base")
public interface IUploadService {

	@Deprecated(since = "2.1.4-RELEASE")
	@PostMapping(value = "/attachment/uploadFile2Txcloud", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
	public Map<String, String> uploadFile2Txcloud(
				@RequestParam Integer readWriteLevel, 
				@RequestParam String fileName, 
				@RequestParam String accountId, 
				@RequestParam String keywords, 
				@RequestParam String uploadIp, 
				@RequestParam String businessScenario, 
				@RequestPart(value = "file") MultipartFile file);

}