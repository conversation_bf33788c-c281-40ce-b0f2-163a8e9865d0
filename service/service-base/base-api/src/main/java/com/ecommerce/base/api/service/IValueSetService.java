
package com.ecommerce.base.api.service;

import com.ecommerce.base.api.dto.ValueAndOptionDTO;
import com.ecommerce.base.api.dto.ValueQueryDTO;
import com.ecommerce.base.api.dto.ValueSetDTO;
import com.ecommerce.base.api.dto.ValueSetOptionDTO;
import com.ecommerce.base.api.dto.ValueSetTreeDTO;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;


/**
 * @Created锛�Sat Sep 08 15:15:56 CST 2018
 * <AUTHOR>
 * @Version:2
 * @Description::: 值集服务
*/

@FeignClient(name = "service-base")
@Tag(name = "IValueSetService", description = ": 值集服务")
public interface IValueSetService {


   @Operation(summary = "更新值集和值集项")
   @PostMapping( path = "/valueSet/updateValueAndOption", consumes = "application/json")
   public void updateValueAndOption(@RequestBody ValueAndOptionDTO valueAndOptionDTO,@RequestParam String operator);


   @Operation(summary = "创建值集和值集项")
   @PostMapping( path = "/valueSet/createValueAndOption", consumes = "application/json")
   public void createValueAndOption(@RequestBody ValueAndOptionDTO valueAndOptionDTO,@RequestParam String operator);


   @Operation(summary = "创建值集项")
   @PostMapping( path = "/valueSet/createValueSetOption", consumes = "application/json")
   public void createValueSetOption(@RequestBody ValueSetOptionDTO valueSet,@RequestParam String operator);


   @Operation(summary = "根据值集id查询值集项")
   @PostMapping( path = "/valueSet/listValueSetOption", consumes = "application/json")
   public List<ValueSetOptionDTO> listValueSetOption(@RequestParam String valueSetId);


   @Operation(summary = "根据code获取值集子节点树")
   @PostMapping( path = "/valueSet/getValueSetByReferenceCode", consumes = "application/json")
   public ValueSetTreeDTO getValueSetByReferenceCode(@RequestParam String referenceCode);

   @Operation(summary = "根据code获取值集子节点树,并根据关键字过滤")
   @PostMapping( path = "/valueSet/getValueSetByReferenceCodeAndKeyWord", consumes = "application/json")
   public List<ValueSetOptionDTO> getValueSetByReferenceCodeAndKeyWord(@RequestParam String referenceCode,@RequestParam String keyWord);

   @Operation(summary = "修改值集项")
   @PostMapping( path = "/valueSet/updateValueSetOptions", consumes = "application/json")
   public void updateValueSetOptions(@RequestBody List<ValueSetOptionDTO> valueSetOptions,@RequestParam String operator);


   @Operation(summary = "根据父节点id获取子节点树")
   @PostMapping( path = "/valueSet/getChildValueSetOption", consumes = "application/json")
   public ValueSetTreeDTO getChildValueSetOption(@RequestParam String parentOptionId);


   @Operation(summary = "根据值集项id得到值集项")
   @PostMapping( path = "/valueSet/getValueSetOption", consumes = "application/json")
   public ValueSetOptionDTO getValueSetOption(@RequestParam String valueSetOptionId);


   @Operation(summary = "通过值集id删除值集")
   @PostMapping( path = "/valueSet/deleteValueSetOption", consumes = "application/json")
   public void deleteValueSetOption(@RequestParam String valueSetId,@RequestParam String operator);


   @Operation(summary = "通过key的集合查询值集合")
   @PostMapping( path = "/valueSet/batchFindOption", consumes = "application/json")
   public List<ValueSetOptionDTO> batchFindOption(@RequestBody List<String> keys);


   @Operation(summary = "修改值集")
   @PostMapping( path = "/valueSet/updateValueSet", consumes = "application/json")
   public void updateValueSet(@RequestBody ValueSetDTO valueSet,@RequestParam String operator);


   @Operation(summary = "值集分页查询")
   @PostMapping( path = "/valueSet/pageValueSet", consumes = "application/json")
   public PageInfo<ValueSetDTO> pageValueSet(@RequestBody ValueQueryDTO valueQueryDTO);


   @Operation(summary = "该值集是否可修改")
   @PostMapping( path = "/valueSet/canValueSetEdit", consumes = "application/json")
   public Boolean canValueSetEdit(@RequestParam String valueSetId);


   @Operation(summary = "创建值集")
   @PostMapping( path = "/valueSet/createValueSet", consumes = "application/json")
   public void createValueSet(@RequestBody ValueSetDTO valueSet,@RequestParam String operator);


   @Operation(summary = "通过key查询值集合")
   @PostMapping( path = "/valueSet/findOptionByKey", consumes = "application/json")
   public List<ValueSetOptionDTO> findOptionByKey(@RequestParam String key);


   @Operation(summary = "根据值集id得到值集")
   @PostMapping( path = "/valueSet/getValueSet", consumes = "application/json")
   public ValueSetDTO getValueSet(@RequestParam String valueSetId);


   @PostMapping( path = "/valueSet/deleteValueSet", consumes = "application/json")
   public void deleteValueSet(@RequestParam String valueSetId,@RequestParam String operator);

   @Operation(summary = "通过角色查询平台")
   @PostMapping( path = "/valueSet/findPlatformByRoleType", consumes = "application/json")
   public List<String[]> findPlatformByRoleType(@RequestBody List<String> roleTypeList);

    @PostMapping( path = "/valueSet/findPlatformByRole", consumes = "application/json")
   public List<String[]> findPlatformByRole(@RequestBody List<Integer> roleIdList);

   @Operation(summary = "重新加载redis缓存")
   @PostMapping( path = "/valueSet/reloadRedisCache", consumes = "application/json")
   public void reloadRedisCache();

}
