
package com.ecommerce.base.api.service;

import com.ecommerce.base.api.dto.WareHouseErpInfo.WarehouseErpInfoDTO;
import com.ecommerce.base.api.dto.WareHouseErpInfo.WarehouseErpInfoKeywordQueryDTO;
import com.ecommerce.base.api.dto.WareHouseErpInfo.WarehouseErpInfoQueryDTO;
import com.ecommerce.base.api.dto.warehouse.CentralWarehouseQueryDTO;
import com.ecommerce.base.api.dto.warehouse.PageQuery;
import com.ecommerce.base.api.dto.warehouse.QueryZoneCarriageDTO;
import com.ecommerce.base.api.dto.warehouse.QueryZoneDTO;
import com.ecommerce.base.api.dto.warehouse.RefWarehouseQueryDTO;
import com.ecommerce.base.api.dto.warehouse.SearchAddressDTO;
import com.ecommerce.base.api.dto.warehouse.StoreQueryDTO;
import com.ecommerce.base.api.dto.warehouse.TradeWarehouseDTO;
import com.ecommerce.base.api.dto.warehouse.WarehouseAddDTO;
import com.ecommerce.base.api.dto.warehouse.WarehouseAllocationDTO;
import com.ecommerce.base.api.dto.warehouse.WarehouseBaseDataDTO;
import com.ecommerce.base.api.dto.warehouse.WarehouseCommonDTO;
import com.ecommerce.base.api.dto.warehouse.WarehouseDetailsDTO;
import com.ecommerce.base.api.dto.warehouse.WarehouseDetailsReqDTO;
import com.ecommerce.base.api.dto.warehouse.WarehouseEditDTO;
import com.ecommerce.base.api.dto.warehouse.WarehouseExternalQueryDTO;
import com.ecommerce.base.api.dto.warehouse.WarehouseIdsQueryDTO;
import com.ecommerce.base.api.dto.warehouse.WarehouseInsertReturnDTO;
import com.ecommerce.base.api.dto.warehouse.WarehouseListDTO;
import com.ecommerce.base.api.dto.warehouse.WarehouseListQueryDTO;
import com.ecommerce.base.api.dto.warehouse.WarehouseOptionDTO;
import com.ecommerce.base.api.dto.warehouse.WarehouseOptionQueryDTO;
import com.ecommerce.base.api.dto.warehouse.WarehousePlafromDTO;
import com.ecommerce.base.api.dto.warehouse.WarehousePlafromQueryDTO;
import com.ecommerce.base.api.dto.warehouse.WarehouseQueryDTO;
import com.ecommerce.base.api.dto.warehouse.WarehouseRemoveDTO;
import com.ecommerce.base.api.dto.warehouse.WarehouseSellerDTO;
import com.ecommerce.base.api.dto.warehouse.WarehouseSellerQueryDTO;
import com.ecommerce.base.api.dto.warehouse.WarehouseStorageDetailDTO;
import com.ecommerce.base.api.dto.warehouse.WarehouseStorageListDTO;
import com.ecommerce.base.api.dto.warehouse.WarehouseStorageQueryDTO;
import com.ecommerce.base.api.dto.warehouse.ZoneCarriageDTO;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Set;


/**
 * @Created锛�Tue Oct 16 11:32:28 CST 2018
 * <AUTHOR>
 * @Version:2
 * @Description::仓库服务
*/

@FeignClient(name = "service-base")
@Tag(name = "IWarehouseService", description = "仓库服务")
public interface IWarehouseService {


   @Operation(summary = "新增仓库")
   @PostMapping( path = "/warehouse/saveWarehouse", consumes = "application/json")
   public WarehouseInsertReturnDTO saveWarehouse(@RequestBody WarehouseAddDTO warehouseAddDTO);


   @Operation(summary = "编辑仓库")
   @PostMapping( path = "/warehouse/updateWarehouse", consumes = "application/json")
   public void updateWarehouse(@RequestBody WarehouseEditDTO warehouseEditDTO);


   @Operation(summary = "根据门店Code区域查询门店")
   @PostMapping( path = "/warehouse/queryStoreByCode", consumes = "application/json")
   public WarehouseDetailsDTO queryStoreByCode(@RequestBody StoreQueryDTO storeQueryDTO);

   @Operation(summary = "根据门店Code区域查询门店")
   @PostMapping( path = "/warehouse/queryStoreListByCode", consumes = "application/json")
   public List<WarehouseDetailsDTO> queryStoreListByCode(@RequestBody StoreQueryDTO storeQueryDTO);

   @PostMapping( path = "/warehouse/searchAddress", consumes = "application/json")
   public String searchAddress(@RequestBody SearchAddressDTO searchAddressDTO);


   @Operation(summary = "删除仓库")
   @PostMapping( path = "/warehouse/removeWarehouse", consumes = "application/json")
   public void removeWarehouse(@RequestBody WarehouseRemoveDTO warehouseRemoveDTO);


   @Operation(summary = "查询平台中心仓和平台门店")
   @PostMapping( path = "/warehouse/queryCentralWarehouseByArea", consumes = "application/json")
   public WarehouseDetailsDTO queryCentralWarehouseByArea(@RequestBody CentralWarehouseQueryDTO centralWarehouseQueryDTO);


   @Operation(summary = "查询仓库的基础数据")
   @PostMapping( path = "/warehouse/selectWarehouseBaseData", consumes = "application/json")
   public WarehouseBaseDataDTO selectWarehouseBaseData(@RequestParam String warehouseId);


   @Operation(summary = "查询区域出货的中心仓或门店")
   @PostMapping( path = "/warehouse/queryZoneWarehouse", consumes = "application/json")
   public TradeWarehouseDTO queryZoneWarehouse(@RequestBody WarehouseQueryDTO warehouseQueryDTO);


   @Operation(summary = "获取仓库列表")
   @PostMapping( path = "/warehouse/queryWarehouseList", consumes = "application/json")
   public PageData<WarehouseListDTO> queryWarehouseList(@RequestBody PageQuery<WarehouseListQueryDTO> pageQuery);

   @Operation(summary = "根据仓库管理员查询仓库id")
   @PostMapping( path = "/warehouse/queryWarehouseIdByAccountId", consumes = "application/json")
   public List<String> queryWarehouseIdByAccountId(@RequestParam String accountId);

   @Operation(summary = "获取仓库库位列表")
   @GetMapping(value="/warehouse/getWarehouseAllocationList")
   public List<WarehouseAllocationDTO> getWarehouseAllocationList(@RequestParam String warehouseId);

   @Operation(summary = "查询仓库详情")
   @PostMapping( path = "/warehouse/queryWarehouseDetails", consumes = "application/json")
   public WarehouseDetailsDTO queryWarehouseDetails(@RequestParam String warehouseId);

   @Operation(summary = "根据id批量查询")
   @PostMapping( path = "/warehouse/queryWarehouseListByIds", consumes = "application/json")
   List<WarehouseListDTO> queryWarehouseListByIds(@RequestBody List<String> warehouseIdList);

   @Operation(summary = "根据id批量查询(包括已经逻辑删除的数据)")
   @PostMapping( path = "/warehouse/queryWarehouseListIgnoreDelFlg", consumes = "application/json")
   List<WarehouseListDTO> queryWarehouseListIgnoreDelFlg(@RequestBody List<String> warehouseIdList);

   @Operation(summary = "查询仓库下拉列表")
   @PostMapping( path = "/warehouse/queryWarehouseIdAndName", consumes = "application/json")
   public List<WarehouseOptionDTO> queryWarehouseIdAndName(@RequestBody WarehouseOptionQueryDTO warehouseOptionQueryDTO);


   @PostMapping( path = "/warehouse/selectSeckillWarehouseByUserId", consumes = "application/json")
   public List<WarehouseOptionDTO> selectSeckillWarehouseByUserId(@RequestParam String userId);

   @Operation(summary = "根据范围查询可配送的范围")
   @PostMapping( path = "/warehouse/queryZoneCarriage", consumes = "application/json")
   public List<ZoneCarriageDTO> queryZoneCarriage(@RequestBody List<QueryZoneDTO> queryZoneList,@RequestParam String queryType);

   @Operation(summary = "根据范围查询可配送的范围2")
   @PostMapping( path = "/warehouse/queryZoneCarriage2", consumes = "application/json")
   public List<ZoneCarriageDTO> queryZoneCarriage2(@RequestBody QueryZoneCarriageDTO dto);

   @Operation(summary = "清理查询缓存")
   @PostMapping( path = "/warehouse/cleanQueryZoneCarriageCache", consumes = "application/json")
   public void cleanQueryZoneCarriageCache();

   @Operation(summary = "查询仓库erp信息")
   @PostMapping( path = "/warehouse/findAllErpInfo", consumes = "application/json")
   public PageInfo<WarehouseErpInfoDTO> findAllErpInfo(@RequestBody WarehouseErpInfoQueryDTO dto);

   @Operation(summary = "根据卖家id和关键字查询仓库erp信息")
   @PostMapping( path = "/warehouse/findErpInfoByKeyword", consumes = "application/json")
   public List<WarehouseErpInfoDTO> findErpInfoByKeyword(@RequestBody WarehouseErpInfoKeywordQueryDTO dto);

   @Operation(summary = "同步erp仓库")
   @PostMapping( path = "/warehouse/syncErpInfo", consumes = "application/json")
   public Boolean syncErpInfo(@RequestParam String memberId,@RequestParam String operatorId);

   @Operation(summary = "根据erpCode查询")
   @PostMapping( path = "/warehouse/findByErpCode", consumes = "application/json")
   public WarehouseBaseDataDTO findByErpCode(@RequestParam String erpCode,@RequestParam String erpOrgCode);

   @Operation(summary = "查询经销商的映射仓库信息")
   @PostMapping( path = "/warehouse/findProxyWarehouse", consumes = "application/json")
   public WarehouseBaseDataDTO findProxyWarehouse(@RequestParam String refWarehouseId, @RequestParam String userId);

   @Operation(summary = "卖家查询仓库存储列表")
   @PostMapping( path = "/warehouse/queryStorageBySeller", consumes = "application/json")
   public ItemResult<PageData<WarehouseStorageListDTO>> queryStorageBySeller(@RequestBody PageQuery<WarehouseStorageQueryDTO> pageQuery);

   @Operation(summary = "平台查询仓库存储列表")
   @PostMapping( path = "/warehouse/queryStorageByPlatform", consumes = "application/json")
   public ItemResult<PageData<WarehouseStorageListDTO>> queryStorageByPlatform(@RequestBody PageQuery<WarehouseStorageQueryDTO> pageQuery);

   @Operation(summary = "仓库存储详情查询")
   @PostMapping( path = "/warehouse/queryStorageDetail", consumes = "application/json")
   public ItemResult<WarehouseStorageDetailDTO> queryStorageDetail(@RequestBody WarehouseStorageQueryDTO warehouseStorageQueryDTO);

   @PostMapping( path = "/warehouse/queryCentralWarehouse",consumes = "application/json")
   public ItemResult<List<WarehouseListDTO>> queryCentralWarehouse(@RequestBody PageQuery<WarehouseListQueryDTO> pageQuery);

   @Operation(summary = "查询卖家关联的平台中心仓和平台门店")
   @PostMapping( path = "/warehouse/queryPlatformWarehouseWithUserRelation",consumes = "application/json")
   public ItemResult<List<WarehouseListDTO>> queryPlatformWarehouseWithUserRelation(@RequestParam String userId);

   @Operation(summary = "查询仓库详情（新接口）")
   @PostMapping( path = "/warehouse/queryWarehouseDetailsByReq",consumes = "application/json")
   public WarehouseDetailsDTO queryWarehouseDetailsByReq(@RequestBody WarehouseDetailsReqDTO dto);

   @Operation(summary = "根据ERP编码批量查询")
    @PostMapping( path = "/warehouse/queryWarehouseListByCode", consumes = "application/json")
    public ItemResult<List<WarehouseListDTO>> queryWarehouseListByCode(@RequestBody WarehouseExternalQueryDTO warehouseExternalQueryDTO);

   @Operation(summary = "仓库ID集合查询仓库基础数据")
   @PostMapping( path = "/warehouse/queryWarehouseCommonList",consumes = "application/json")
   public ItemResult<List<WarehouseCommonDTO>> queryWarehouseCommonList(@RequestBody Set<String> warehouseIdSet);

   @Operation(summary = "通过省市区code查询仓库Id")
   @PostMapping( path = "/warehouse/queryWarehouseIdList",consumes = "application/json")
   public ItemResult<List<String>> queryWarehouseIdList(@RequestBody WarehouseIdsQueryDTO warehouseIdsQueryDTO);

   @Operation(summary = "根据厂家仓库id查询被引用的仓库")
   @PostMapping( path = "/warehouse/queryRefWarehouseInfo",consumes = "application/json")
   public ItemResult<List<WarehouseListDTO>> queryRefWarehouseInfo(@RequestBody RefWarehouseQueryDTO dto);

   @Operation(summary = "查询码头Id是否绑定收货地址")
   @PostMapping( path = "/warehouse/isWharfIdBindingWarehouse",consumes = "application/json")
   public Boolean isWharfIdBindingWarehouse(@RequestParam String wharfId);

   @Operation(summary = "查询仓库详情")
   @PostMapping( path = "/warehouse/queryWarehouseDetail",consumes = "application/json")
   ItemResult<WarehouseDetailsDTO> queryWarehouseDetail(@RequestParam String warehouseId);

   @Operation(summary = "查询库位详情")
   @PostMapping( path = "/warehouse/queryWarehouseAllocationListByWarehouseId",consumes = "application/json")
   public ItemResult<List<WarehouseAllocationDTO>> queryWarehouseAllocationListByWarehouseId(WarehouseAllocationDTO dto);
   
   @Operation(summary = "根据ID查询库位详情")
   @PostMapping( path = "/warehouse/queryWarehouseAllocationById",consumes = "application/json")
   public WarehouseAllocationDTO queryWarehouseAllocationById(@RequestParam String id);

   @Operation(summary = "根据ID查询库位详情")
   @PostMapping( path = "/warehouse/getWarehouseIdAndName",consumes = "application/json")
   public ItemResult<List<WarehouseOptionDTO>> getWarehouseIdAndName(WarehouseOptionQueryDTO warehouseOptionQueryDTO);

   
   @Operation(summary = "根据ID查询库位详情")
   @PostMapping( path = "/warehouse/getWarehouseByAccountId",consumes = "application/json")
   public ItemResult<List<WarehousePlafromDTO>> getWarehouseByAccountId(WarehousePlafromQueryDTO plafromQueryDTO);


   @Operation(summary = "查询仓库库位库存列表")
   @PostMapping( path = "/warehouse/getWarehouseListBymemberId",consumes = "application/json")
   public ItemResult<List<WarehouseSellerDTO>> getWarehouseListBymemberId(@RequestBody WarehouseSellerQueryDTO sellerQueryDTO);

}
