
package com.ecommerce.base.api.service;

import com.ecommerce.base.api.dto.warehouse.WarehouseUserRelationAddDTO;
import com.ecommerce.base.api.dto.warehouse.WarehouseUserRelationUpdateDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;


/**
 * @Created锛�Thu Aug 22 09:33:39 CST 2019
 * <AUTHOR>
 * @Version:2
 * @Description::中心仓--卖家映射
*/

@FeignClient(name = "service-base")
@Tag(name = "IWarehouseUserRelationService", description = "中心仓--卖家映射")
public interface IWarehouseUserRelationService {


   @Operation(summary = "修改中心仓-卖家映射")
   @PostMapping( path = "/warehouseUserRelation/updateWarehouseUserRelation", consumes = "application/json")
   public Boolean updateWarehouseUserRelation(@RequestBody WarehouseUserRelationUpdateDTO updateDTO);


   @Operation(summary = "删除中心仓-卖家关系映射")
   @PostMapping( path = "/warehouseUserRelation/deleteWarehouseUserRelation", consumes = "application/json")
   public Boolean deleteWarehouseUserRelation(@RequestParam String userId,@RequestParam String warehouseId);


   @Operation(summary = "根据用户ID,查询使用的中心仓/平台门店")
   @PostMapping( path = "/warehouseUserRelation/queryWarehouseIdFromRelation", consumes = "application/json")
   public List<String> queryWarehouseIdFromRelation(@RequestParam String sellerID);


   @Operation(summary = "创建中心仓-卖家映射")
   @PostMapping( path = "/warehouseUserRelation/createWarehouseUserRelation", consumes = "application/json")
   public Boolean createWarehouseUserRelation(@RequestBody List<WarehouseUserRelationAddDTO> addDTOs);



}
