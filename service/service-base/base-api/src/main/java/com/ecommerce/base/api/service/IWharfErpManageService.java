package com.ecommerce.base.api.service;

import com.ecommerce.base.api.dto.warehouse.PageQuery;
import com.ecommerce.base.api.dto.wharf.WharfErpManageListQueryDTO;
import com.ecommerce.base.api.dto.wharf.WharfErpManagePageListDTO;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


/**
 * <AUTHOR>
 * @Version 1.0
 */
@FeignClient(name = "service-base")
@Tag(name = "IWharfErpManageService", description = "ERP码头管理服务")
public interface IWharfErpManageService {

    @Operation(summary = "查询ERP码头列表")
    @PostMapping( path = "/wharfErpManage/queryWharfErpPageList", consumes = "application/json")
    ItemResult<PageData<WharfErpManagePageListDTO>> queryWharfErpPageList(@RequestBody PageQuery<WharfErpManageListQueryDTO> pageQueryDTO);

}
