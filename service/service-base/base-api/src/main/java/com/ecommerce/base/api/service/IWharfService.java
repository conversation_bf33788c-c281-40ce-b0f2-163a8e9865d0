package com.ecommerce.base.api.service;

import com.ecommerce.base.api.dto.warehouse.PageQuery;
import com.ecommerce.base.api.dto.wharf.*;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * :: TODO
 *
 * <AUTHOR>
*/

@FeignClient(name ="service-base")
public interface IWharfService {


   @PostMapping( path = "/wharf/deleteWharf", consumes = "application/json")
   public ItemResult<Void> deleteWharf(@RequestBody List<String> wharfIds,@RequestParam String operatorId);


   @PostMapping( path = "/wharf/updateWharf", consumes = "application/json")
   public ItemResult<Void> updateWharf(@RequestBody WharfUpdateDTO wharfUpdateDTO);


   @PostMapping( path = "/wharf/getWharfById", consumes = "application/json")
   public ItemResult<WharfInfoDTO> getWharfById(@RequestParam String id);


   @PostMapping( path = "/wharf/getWharfList", consumes = "application/json")
   public ItemResult<PageData<WharfListDTO>> getWharfList(@RequestBody PageQuery<WharfQueryDTO> pageQuery);


   @PostMapping( path = "/wharf/saveWharf", consumes = "application/json")
   public ItemResult<String> saveWharf(@RequestBody WharfAddDTO wharfAddDTO);

   @PostMapping( path = "/wharf/batchSaveWharf", consumes = "application/json")
   public ItemResult<Void> batchSaveWharf(@RequestBody List<WharfAddDTO> wharfAddDTOS);


   @PostMapping( path = "/wharf/getAllWharf", consumes = "application/json")
   public ItemResult<List<WharfInfoDTO>> getAllWharf();


   @PostMapping( path = "/wharf/changeWharfStatus", consumes = "application/json")
   public ItemResult<Void> changeWharfStatus(@RequestBody List<String> wharfIds,@RequestParam Byte status,@RequestParam String operatorId);

   @PostMapping( path = "/wharf/getAllWharfByGroup", consumes = "application/json")
   ItemResult<List<TreeProvinceDTO>> getAllWharfByGroup();

   @PostMapping( path = "/wharf/getWharfInfoList", consumes = "application/json")
   List<WharfProvinceCityDTO> getWharfInfoList(@RequestBody List<String> idList);

   @PostMapping( path = "/wharf/findErpInfoList", consumes = "application/json")
   ItemResult<List<WharfErpInfoListDTO>> findErpInfoList(@RequestBody WharfErpInfoQueryDTO queryDTO);

   @PostMapping( path = "/wharf/syncErpInfo", consumes = "application/json")
   ItemResult<Boolean> syncErpInfo(@RequestParam String memberId, @RequestParam String operatorId);

   @PostMapping( path = "/wharf/saveWharfMap", consumes = "application/json")
   ItemResult<String> saveWharfMap(@RequestBody WharfMapAddDTO wharfMapAddDTO);

   @PostMapping( path = "/wharf/queryWharfMapList", consumes = "application/json")
   ItemResult<List<WharfMapListDTO>> queryWharfMapList(@RequestBody WharfMaPQueryDTO wharfMaPQueryDTO);

   @PostMapping( path = "/wharf/queryEcWharfName", consumes = "application/json")
   ItemResult<Map<String,String>> queryEcWharfName(@RequestBody WharfMaPQueryDTO wharfMaPQueryDTO);

   @PostMapping( path = "/wharf/queryUsingEcWharfName", consumes = "application/json")
   ItemResult<Map<String, String>> queryUsingEcWharfName(@RequestBody WharfMaPQueryDTO wharfMaPQueryDTO);

   @PostMapping( path = "/wharf/queryEcWharf", consumes = "application/json")
   ItemResult<List<WharfMapListDTO>> queryEcWharf(@RequestBody WharfErpQueryDTO wharfErpQueryDTO);

   @GetMapping( path = "/wharf/queryWharfMapListByAddressId", consumes = "application/json")
   List<WharfMapListDTO> queryWharfMapListByAddressId(@Parameter(name = "addressId", description = "收货地址ID") @RequestParam String addressId);
   
   @PostMapping( path = "/wharf/getWharfIdAndName", consumes = "application/json")
   ItemResult<List<WharfInfoDTO>> getWharfIdAndName(@RequestBody WharfInfoDTO infoDTO);
}
