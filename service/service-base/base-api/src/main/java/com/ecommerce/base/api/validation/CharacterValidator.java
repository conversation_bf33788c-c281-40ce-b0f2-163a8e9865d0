package com.ecommerce.base.api.validation;


import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * <AUTHOR>
 * @Date: 2018-09-26 10:18
 * @description:
 **/
public class CharacterValidator implements ConstraintValidator<IllegalCharacter,String> {

    @Override
    public void initialize(IllegalCharacter illegalCharacter) {
        //do nothing
    }

    @Override
    public boolean isValid(String s, ConstraintValidatorContext constraintValidatorContext) {
        String regEx = "[`~!@#$%^&*()+=|{}':;'\\[\\]<>/?~！@#￥%……&*——+|{}【】‘；：”“’。，、？\\s]";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(s);
        return m.find();
    }
}
