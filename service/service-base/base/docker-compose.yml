
services:
  service-base:
    image: ecommerce-service-base:1.0
    volumes:
      - /remote/logs/base:/var/log
      - /remote/skywalking:/home/<USER>
      - /remote/config/auth:/usr/local/auth
      - /usr/local/share/img/img:/usr/local/share/img/img
    container_name: base
    restart: always
    deploy:
      resources:
        limits:
          memory: 800m
    ports:
      - "9004:9004"
    environment:
      - SPRING_CLOUD_CONFIG_URI=http://************:8888
      - SPRING_CLOUD_CONFIG_LABEL=asset-2025
      - SPRING_CLOUD_CONFIG_PROFILE=dev
      - SPRING_CLOUD_CONFIG_NAME=base,db,redis,rabbitmq,hystrix,vip-db,vip-server,xxl-job,eureka,kafka
      - TZ=Asia/Shanghai
      - JAVA_TOOL_OPTIONS=-Xms256m -Xmx512m -XX:+UseSerialGC
    depends_on:
      - config
      - eureka