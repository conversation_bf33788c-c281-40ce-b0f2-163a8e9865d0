package com.ecommerce.base.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.base.api.dto.*;
import com.ecommerce.base.service.IMessageConfigService;
import com.ecommerce.base.service.message.impl.MessageSendService;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * @Created锛�Thu Sep 20 19:19:08 CST 2018
 * <AUTHOR>
 * @Version:2
 * @Description:null
*/

@RestController
@Tag(name = "MessageConfig", description = "null")
@RequestMapping("/messageConfig")
public class MessageConfigController {

   @Autowired 
   private IMessageConfigService iMessageConfigService;

   @Operation(summary = "禁用")
   @PostMapping(value="/disable")
   public void disable(@RequestParam String id,@RequestParam String operator){
      iMessageConfigService.disable(id,operator);

   }


   @Operation(summary = "启用消息模板")
   @PostMapping(value="/enable")
   public void enable(@RequestParam String id,@RequestParam String operator){
      iMessageConfigService.enable(id,operator);

   }


   @Operation(summary = "修改消息模板")
   @PostMapping(value="/update")
   public MessageConfigDTO update(@RequestBody MessageConfigUpdateDTO messageConfigDTO){
      return iMessageConfigService.update(messageConfigDTO);
   }


   @Operation(summary = "新增消息模板")
   @PostMapping(value="/create")
   public MessageConfigDTO create(@RequestBody MessageConfigCreateDTO messageConfigCreateDTO){
      return iMessageConfigService.create(messageConfigCreateDTO);
   }


   @Operation(summary = "查询所有消息模板")
   @PostMapping(value="/list")
   public PageInfo<MessageConfigDTO> list(@RequestBody MessageConfigQueryDTO messageSearchDTO){
      return iMessageConfigService.list(messageSearchDTO);
   }


   @Operation(summary = "通过名称查询消息模板列表")
   @PostMapping(value="/findByName")
   public List<MessageConfigDTO> findByName(@RequestParam String name){
      return iMessageConfigService.findByName(name);
   }


   @Operation(summary = "通过编号查询消息模板列表")
   @PostMapping(value="/findByCode")
   public List<MessageConfigDTO> findByCode(@RequestParam String code){
      return iMessageConfigService.findByCode(code);
   }

   @Operation(summary = "通过编号查询消息模板列表和额外配置")
   @PostMapping(value="/findConfigAndRoleByConfigCode")
   public MessageConfigAndRoleDTO findConfigAndRoleByConfigCode(@RequestParam String code){
      return iMessageConfigService.findConfigAndRoleByConfigCode(code);
   }


   @Operation(summary = "删除消息模板")
   @PostMapping(value="/deleteById")
   public void deleteById(@RequestParam String id,@RequestParam String operator){
      iMessageConfigService.deleteById(id,operator);

   }


   @Operation(summary = "通过消息模板ID查询消息模板")
   @PostMapping(value="/findById")
   public MessageConfigDTO findById(@RequestParam String id){
      return iMessageConfigService.findById(id);
   }

   @Operation(summary = "根据会员id和code查询需要发送消息的消息模板id")
   @PostMapping(value="/findByMemberIdAndCode")
   public List<String> findByMemberIdAndCode(@RequestParam String memberId,@RequestParam String messconfigCode){
      return iMessageConfigService.findByMemberIdAndCode(memberId,messconfigCode);
   }


   @Operation(summary = "分页查询会员消息模板配置")
   @PostMapping(value="/pageMemberMessageConfig")
   public PageInfo<MessageConfigDTO> pageMemberMessageConfig(@RequestParam String memberId,
                                                                              @RequestParam Integer messageType,
                                                                              @RequestParam Integer pageSize,
                                                              @RequestParam Integer pageNum){
      return iMessageConfigService.pageMemberMessageConfig(memberId,messageType,pageSize,pageNum);
   }

   @Operation(summary = "分页查询账号消息模板配置")
   @PostMapping(value="/pageAccountMessageConfig")
   public PageInfo<MessageConfigDTO> pageAccountMessageConfig(@RequestParam String accountId,
                                                                          @RequestParam Integer messageType,
                                                                          @RequestParam Integer pageSize,
                                                                              @RequestParam Integer pageNum){
      return iMessageConfigService.pageAccountMessageConfig(accountId,messageType,pageSize,pageNum);
   }

   @Operation(summary = "查询会员站内信消息模板配置")
   @PostMapping(value="/findMemberInnerMessageConfig")
   public List<MessageConfigDTO> findMemberInnerMessageConfig(@RequestParam String memberId){
      return iMessageConfigService.findMemberInnerMessageConfig(memberId);
   }


   @Operation(summary = "会员禁用消息")
   @PostMapping(value="/disableMemberConfig")
   public Boolean disableMemberConfig(@RequestBody MessageConfigDisableMemberDTO messageConfigDisableMemberDTO){
      return iMessageConfigService.disableMemberConfig(messageConfigDisableMemberDTO);
   }

   @Operation(summary = "会员启用消息")
   @PostMapping(value="/enableMemberConfig")
   public Boolean enableMemberConfig(@RequestBody MessageConfigEnableMemberDTO messageConfigEnableMemberDTO){
      return iMessageConfigService.enableMemberConfig(messageConfigEnableMemberDTO);
   }

   @Operation(summary = "账号禁用消息")
   @PostMapping(value="/disableAccountConfig")
   public Boolean disableAccountConfig(@RequestBody MessageConfigDisableAccountDTO messageConfigDisableAccountDTO){
      return iMessageConfigService.disableAccountConfig(messageConfigDisableAccountDTO);
   }

   @Operation(summary = "账号启用消息")
   @PostMapping(value="/enableAccountConfig")
   public Boolean enableAccountConfig(@RequestBody MessageConfigEnableAccountDTO messageConfigEnableAccountDTO){
      return iMessageConfigService.enableAccountConfig(messageConfigEnableAccountDTO);
   }


   @Operation(summary = "查询会员短信消息模板配置")
   @PostMapping(value="/findMemberSMSMessageConfig")
   public List<MessageConfigDTO> findMemberSMSMessageConfig(@RequestParam String memberId){
      return iMessageConfigService.findMemberSMSMessageConfig(memberId);
   }


   @Operation(summary = "查询会员消息模板配置")
   @PostMapping(value="/findMemberMessageConfig")
   public List<MessageConfigDTO> findMemberMessageConfig(@RequestParam String memberId,@RequestParam Integer type){
      return iMessageConfigService.findMemberMessageConfig(memberId,type);
   }

   @Operation(summary = "查询会员是否已禁用（只传递会员id,则判断会员消息是否禁用，传账号或手机号则判断对应账号是否禁用)")
   @PostMapping(value="/isDisabled")
   public Boolean isDisabled(@RequestParam(required = false) String memberId,
                             @RequestParam(required = false) String accountId,
                             @RequestParam(required = false) String mobile,
                             @RequestParam String messConfigId){
      return iMessageConfigService.isDisabled(memberId,accountId,mobile,messConfigId);
   }

   @Autowired
   private MessageSendService messageSendService;


   @Operation(summary = "消息手工重试接口")
   @PostMapping(value="/retry")
   public void retry(@RequestParam String mqId){
      messageSendService.retryTest(mqId);
   }

}
