package com.ecommerce.base.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.base.api.dto.authRes.PageDTO;
import com.ecommerce.base.api.dto.authRes.PageDetailDTO;
import com.ecommerce.base.api.dto.authRes.ResCallRelationDTO;
import com.ecommerce.base.api.dto.authRes.RoleAuthenticateDTO;
import com.ecommerce.base.api.dto.permission.BindRelationshipDTO;
import com.ecommerce.base.service.permission.resource.IPageService;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * @Created锛�Fri Mar 08 11:18:44 CST 2019
 * <AUTHOR>
 * @Version:2
 * @Description:资源管理_页面
*/

@RestController
@Tag(name = "Page", description = "资源管理_页面")
@RequestMapping("/page")
public class PageController {

   @Autowired 
   private IPageService iPageService;

   @Operation(summary = "通过角色查询已授权页面")
   @PostMapping(value="/findSelectPageByRoleId")
   public List<PageDTO> findSelectPageByRoleId(@RequestParam String roleId){
      return iPageService.findSelectPageByRoleId(roleId);
   }


   @Operation(summary = "解绑页面服务")
   @PostMapping(value="/unbindService2Page")
   public void unbindService2Page(@RequestBody BindRelationshipDTO bindDTO){
      iPageService.unbindService2Page(bindDTO);

   }


   @Operation(summary = "条件查询页面")
   @PostMapping(value="/findPageByCondiftion")
   public PageInfo<PageDTO> findPageByCondiftion(@RequestBody PageDTO page){
      return iPageService.findPageByCondiftion(page);
   }


   @Operation(summary = "通过菜单查询页面")
   @PostMapping(value="/findPageByMenu")
   public List<PageDTO> findPageByMenu(@RequestParam String menuId){
      return iPageService.findPageByMenu(menuId);
   }

   @Operation(summary = "通过菜单批量查询页面")
   @PostMapping(value="/findPageByMenuIds")
   public List<PageDTO> findPageByMenuIds(@RequestBody List<String> menuIds){
      return iPageService.findPageByMenuIds(menuIds);
   }

   @Operation(summary = "通过角色查询页面及子页面")
   @PostMapping(value="/findPageByRoleId")
   public List<PageDTO> findPageByRoleId(@RequestParam String roleId){
      return iPageService.findPageByRoleId(roleId);
   }


   @Operation(summary = "修改页面")
   @PostMapping(value="/updatePage")
   public int updatePage(@RequestBody PageDTO page){
      return iPageService.updatePage(page);
   }


   @Operation(summary = "检查重复")
   @PostMapping(value="/checkExist")
   public boolean checkExist(@RequestParam String code){
      return iPageService.checkExist(code);
   }


   @Operation(summary = "解除绑定页面关系")
   @PostMapping(value="/unbindPage2Page")
   public void unbindPage2Page(@RequestBody BindRelationshipDTO bindDTO){
      iPageService.unbindPage2Page(bindDTO);

   }


   @Operation(summary = "绑定页面服务关系")
   @PostMapping(value="/bindService2Page")
   public void bindService2Page(@RequestBody BindRelationshipDTO bindDTO){
      iPageService.bindService2Page(bindDTO);

   }


   @Operation(summary = "添加页面")
   @PostMapping(value="/createPage")
   public PageDTO createPage(@RequestBody PageDTO page){
      return iPageService.createPage(page);
   }


   @Operation(summary = "删除页面")
   @PostMapping(value="/delPage")
   public int delPage(@RequestParam String id,@RequestParam String operator){
      return iPageService.delPage(id,operator);
   }


   @Operation(summary = "通过页面ID查询页面详情(按钮，service，页面关系)")
   @PostMapping(value="/findPageDetail")
   public PageDetailDTO findPageDetail(@RequestParam String pageId){
      return iPageService.findPageDetail(pageId);
   }


   @Operation(summary = "绑定页面关系")
   @PostMapping(value="/bindPage2Page")
   public void bindPage2Page(@RequestBody BindRelationshipDTO bindDTO){
      iPageService.bindPage2Page(bindDTO);

   }


   @Operation(summary = "通过主键查询页面")
   @PostMapping(value="/findPageById")
   public PageDTO findPageById(@RequestParam String id){
      return iPageService.findPageById(id);
   }

   @Operation(summary = "条件查询调用关系")
   @PostMapping(value="/findCallRelationByCondition")
   public PageInfo<ResCallRelationDTO> findCallRelationByCondition( @RequestBody ResCallRelationDTO dto){
      return iPageService.findCallRelationByCondition(dto);
   }

   @Operation(summary = "查看调用关系详情")
   @PostMapping(value="/findCallRelationDetail")
   public PageDetailDTO findCallRelationDetail(@RequestParam String callId){
      return iPageService.findCallRelationDetail(callId);
   }

    @Operation(summary = "根据名称或者代码查询")
    @PostMapping(value="/findByKeyword")
    public List<PageDTO> findByKeyword(@RequestParam String keyword){
        return iPageService.findByKeyword(keyword);
    }

   @Operation(summary = "通过页面id和父页面id查询页面(弹出,跳转关系)")
   @PostMapping(value="/findPageBy2Id")
   public PageDTO findPageBy2Id(@RequestBody PageDTO page){
      return iPageService.findPageBy2Id(page);
   }

   @Operation(summary = "通过菜单查询页面(授权专用) 参数：roleId(可选),platform")
   @PostMapping(value="/findPageByRoleAndPlatform")
   public RoleAuthenticateDTO findPageByRoleAndPlatform(@RequestBody RoleAuthenticateDTO dto) {
      return iPageService.findPageByRoleAndPlatform(dto);
   }
}
