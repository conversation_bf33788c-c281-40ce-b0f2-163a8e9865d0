package com.ecommerce.base.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.base.api.dto.region.RegionDTO;
import com.ecommerce.base.api.dto.serviceProvince.ProvinceDTO;
import com.ecommerce.base.service.IProvinceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * @Created锛�Tue Feb 19 10:06:13 CST 2019
 * <AUTHOR>
 * @Version:2
 * @Description:Author:   jacoyang Date:     18/02/2019
*/

@RestController
@Tag(name = "Province", description = "维护系统上线的省份")
@RequestMapping("/province")
public class ProvinceController {

   @Autowired 
   private IProvinceService iProvinceService;

   @Operation(summary = "根据区域代码更新省份")
   @PostMapping(value="/update")
   public void update(@RequestBody List<String> adcodeList,@RequestParam String operator){
      iProvinceService.update(adcodeList,operator);

   }


   @Operation(summary = "查询所有的省份")
   @PostMapping(value="/findAll")
   public List<RegionDTO> findAll(){
      return iProvinceService.findAll();
   }

   @Operation(summary = "查询所有的省份和城市")
   @PostMapping(value="/findProvinceAndCity")
   public List<ProvinceDTO> findProvinceAndCity(){
      return iProvinceService.findProvinceAndCity();
   }


}
