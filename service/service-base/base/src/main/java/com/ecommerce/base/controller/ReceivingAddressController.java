package com.ecommerce.base.controller;

import com.ecommerce.base.api.dto.ERPAddressListDTO;
import com.ecommerce.base.api.dto.ERPAddressQueryDTO;
import com.ecommerce.base.api.dto.MemberAddressQueryDTO;
import com.ecommerce.base.api.dto.MemberSearchDTO;
import com.ecommerce.base.api.dto.OrderAddressDTO;
import com.ecommerce.base.api.dto.ReceivingAddressCreateDTO;
import com.ecommerce.base.api.dto.ReceivingAddressDTO;
import com.ecommerce.base.api.dto.ReceivingAddressErpInfoDTO;
import com.ecommerce.base.api.dto.ReceivingAddressErpInfoUpdateDTO;
import com.ecommerce.base.api.dto.ReceivingAddressMapAuditDTO;
import com.ecommerce.base.api.dto.ReceivingAddressMapDTO;
import com.ecommerce.base.api.dto.ReceivingAddressMapQueryDTO;
import com.ecommerce.base.api.dto.ReceivingAddressQueryDTO;
import com.ecommerce.base.api.dto.ReceivingAddressSimpleDTO;
import com.ecommerce.base.api.dto.ReceivingAddressUpdateDTO;
import com.ecommerce.base.api.dto.warehouse.PageQuery;
import com.ecommerce.base.api.dto.wharf.ReceivingAddrMapWharfDTO;
import com.ecommerce.base.service.IReceivingAddressMapService;
import com.ecommerce.base.service.IReceivingAddressService;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.result.ItemResult;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.util.Collection;
import java.util.List;


/**
 * @Created锛�Wed Sep 12 14:27:09 CST 2018
 * <AUTHOR>
 * @Version:2
 * @Description:收货地址相关service
 */

@RestController
@Tag(name = "ReceivingAddress", description = "收货地址相关service")
@RequestMapping("/receivingAddress")
public class ReceivingAddressController {

    @Autowired
    private IReceivingAddressService iReceivingAddressService;

    @Autowired
    private IReceivingAddressMapService receivingAddressMapService;

    @Operation(summary = "根据买家卖家厂家查询ERP地址Id")
    @PostMapping(value="/getReceivingAddressMapInfo")
    public ReceivingAddressMapDTO getReceivingAddressMapInfo(@RequestBody ReceivingAddressMapQueryDTO dto)
    {
        return receivingAddressMapService.getReceivingAddressMapInfo(dto);
    }

    @Operation(summary = "买家卖家厂家查询")
    @PostMapping(value="/memberSearch")
    public List<MemberSearchDTO> memberSearch(@RequestBody MemberSearchDTO dto)
    {
        return receivingAddressMapService.getSearchMemberList(dto);
    }

    @Operation(summary = "买家收货地址Map")
    @PostMapping(value="/addressMap")
    public PageInfo<ReceivingAddressMapDTO> addressMap(@RequestBody ReceivingAddressMapQueryDTO dto)
    {
        Page<ReceivingAddressMapDTO> page = receivingAddressMapService.getReceivingAddressMapList(dto);
        return new PageInfo<>(page);
    }

    @Operation(summary = "审核买家收货地址")
    @PostMapping(value="/addressAudit")
    public void addressAudit(@Parameter(name = "memberType", description = "操作人类型 10：买家、20：卖家、30：厂商") @RequestParam int memberType, @Parameter(name = "operator", description = "操作人") @RequestParam String operator, @RequestBody List<ReceivingAddressMapAuditDTO> dataList)
    {
        receivingAddressMapService.auditReceivingAddressMap(dataList, memberType, operator);
    }

    @Operation(summary = "更新收货地址中的销售区域")
    @PostMapping(value="/updateAddressMap")
    public void updateAddressMap(@RequestBody ReceivingAddressMapDTO dto)
    {
        receivingAddressMapService.updateAddressMapById(dto);
    }

    @Operation(summary = "查询地址是否已同步ERP")
    @PostMapping(value = "/hasERPAddress")
    public boolean hasERPAddress(@RequestBody ReceivingAddressMapQueryDTO queryDTO) {
        return receivingAddressMapService.hasERPAddress(queryDTO);
    }

    @Operation(summary = "修改收货地址")
    @PostMapping(value="/update")
    public void update(@Valid @RequestBody ReceivingAddressUpdateDTO receivingAddressUpdateDTO){
        iReceivingAddressService.update(receivingAddressUpdateDTO);

    }


    @Operation(summary = "创建收货地址")
    @PostMapping(value="/create")
    public ItemResult<String> create(@Valid @RequestBody ReceivingAddressCreateDTO receivingAddressCreateDTO){
        return iReceivingAddressService.create(receivingAddressCreateDTO);

    }


    @Operation(summary = "根据id查找收货地址")
    @PostMapping(value="/findById")
    public ReceivingAddressDTO findById(@RequestParam String id){
        return iReceivingAddressService.findById(id);
    }

    @Operation(summary = "根据ids批量查找收货地址")
    @PostMapping(value="/findByIds")
    public List<ReceivingAddressDTO> findByIds(@RequestBody Collection<String> idList){
        return iReceivingAddressService.findByIds(idList);
    }

    @Operation(summary = "设置默认收货地址")
    @PostMapping(value="/setDefaultById")
    public void setDefaultById(@RequestParam String id,@RequestParam String memberId,@RequestParam String operator){
        iReceivingAddressService.setDefaultById(id,memberId,operator);

    }


    @Operation(summary = "根据id删除收货地址")
    @PostMapping(value="/deleteById")
    public void deleteById(@RequestParam String id,@RequestParam String operator){
        iReceivingAddressService.deleteById(id,operator);

    }


    @Operation(summary = "通过会员Id查询收货地址")
    @PostMapping(value="/findByMemberId")
    public List<ReceivingAddressDTO> findByMemberId(@RequestParam String memberId, @RequestParam(required = false) String contractId, @RequestParam(required = false) String cartId){
        return iReceivingAddressService.findByMemberId2(memberId, contractId, cartId);
    }

    @Operation(summary = "通过会员Ids查询收货地址")
    @PostMapping(value="/findByMemberIds")
    public List<ReceivingAddressDTO> findByMemberIds(@RequestBody Collection<String> memberIds) {
        return iReceivingAddressService.findByMemberIds(memberIds);
    }

    @Operation(summary = "根据会员id获取默认收货地址（企业员工则根据企业id获取默认收货地址）")
    @PostMapping(value="/getDefaultByMemberId")
    public ReceivingAddressDTO getDefaultByMemberId(@RequestParam String memberId){
        return iReceivingAddressService.getDefaultByMemberId(memberId);
    }


    @Operation(summary = "查询某会员的所有收货地址")
    @PostMapping(value="/findPageByMemberId")
    public PageInfo<ReceivingAddressDTO> findPageByMemberId(@RequestBody PageQuery<ReceivingAddressQueryDTO> pageQuery){
        return iReceivingAddressService.findPageByMemberId(pageQuery);
    }


    @Operation(summary = "查询erp收货地址信息")
    @PostMapping(value="/findErpInfo")
    public List<ReceivingAddressErpInfoDTO> findErpInfo(@RequestBody ReceivingAddressErpInfoDTO dto){
        return iReceivingAddressService.findErpInfo(dto);
    }

    @Operation(summary = "保存erp收货地址信息")
    @PostMapping(value="/saveErpInfo")
    public void saveErpInfo(@RequestBody ReceivingAddressErpInfoDTO dto){
        iReceivingAddressService.saveErpInfo(dto);
    }

    @Operation(summary = "查询erp收货地址关系表信息")
    @PostMapping(value="/findErpInfoBySellerIdAndBuyerId")
    public List<ReceivingAddressErpInfoDTO> findErpInfoBySellerIdAndBuyerId(@RequestParam String sellerId,@RequestParam String buyerId){
        return iReceivingAddressService.findErpInfoBySellerIdAndBuyerId(sellerId,buyerId);
    }

    @Operation(summary = "保存erp收货地址信息")
    @PostMapping(value="/updateErpInfo")
    public void updateErpInfo(@RequestBody ReceivingAddressErpInfoUpdateDTO dto){
        iReceivingAddressService.updateErpInfo(dto);
    }

    @Operation(summary = "查询erp卸货点列表")
    @PostMapping(value="/queryERPAddressList")
    public List<ERPAddressListDTO> queryERPAddressList(@RequestBody ERPAddressQueryDTO erpAddressQueryDTO){
        return iReceivingAddressService.queryERPAddressList(erpAddressQueryDTO);
    }

    @Operation(summary = "查询下单地址列表")
    @PostMapping(value="/queryOrderAddressList")
    public List<OrderAddressDTO> queryOrderAddressList(@RequestBody MemberAddressQueryDTO memberAddressQueryDTO){
        return iReceivingAddressService.queryOrderAddressList(memberAddressQueryDTO);
    }

    @Operation(summary = "根据ids批量查找收货地址简化信息")
    @PostMapping(value="/findSimpleByIds")
    public List<ReceivingAddressSimpleDTO> findSimpleByIds(@RequestBody Collection<String> ids) {
        return iReceivingAddressService.findSimpleByIds(ids);
    }

    @Operation(summary = "查询码头Id是否绑定收货地址")
    @PostMapping(value="/isWharfIdBindingAddress")
    public Boolean isWharfIdBindingAddress(@Parameter(description = "码头Id") @RequestParam String wharfId) {
        return iReceivingAddressService.isWharfIdBindingAddress(wharfId);
    }

    @Operation(summary = "查询收货地址是否绑定ERP码头")
    @PostMapping(value="/hasERPWharf")
    public boolean hasERPWharf(@RequestParam String addressId, @RequestParam String sellerId) {
        return iReceivingAddressService.hasERPWharf(addressId, sellerId);
    }

    @Operation(summary = "ERP码头管理列表")
    @PostMapping(value="/queryErpWharfListByQuery")
    public PageInfo<ReceivingAddressMapDTO> queryErpWharfListByQuery(@RequestBody ReceivingAddressMapQueryDTO dto) {
        Page<ReceivingAddressMapDTO> page = receivingAddressMapService.queryErpWharfListByQuery(dto);
        return new PageInfo<>(page);
    }

    @Operation(summary = "修改ERP码头名称")
    @PostMapping(value="/updateErpWharf")
    public void updateErpWharf(@RequestBody ReceivingAddressMapDTO dto) {
        receivingAddressMapService.updateErpWharf(dto);
    }


    @Operation(summary = "通过码头名称模糊搜索ERP码头")
    @PostMapping(value="/queryErpWharfList")
    public List<ReceivingAddrMapWharfDTO> queryErpWharfList(@RequestBody ReceivingAddressMapQueryDTO dto) {
        return receivingAddressMapService.queryErpWharfList(dto);
    }
    
    @Operation(summary = "根据卖家ID模糊查询地址")
    @PostMapping(value="/queryAddressBySellerId")
    public List<ReceivingAddressMapDTO> queryAddressBySellerId(@RequestBody ReceivingAddressMapQueryDTO dto){
    	return receivingAddressMapService.queryAddressBySellerId(dto);
    }

    @Operation(summary = "通过会员码头获取收货地址")
    @PostMapping(value="/getReceivingAddressByWharfId")
    public ReceivingAddressDTO getReceivingAddressByWharfId(@RequestBody ReceivingAddressDTO dto) throws BizException {
        return iReceivingAddressService.getReceivingAddressByWharfId(dto);
    }

}
