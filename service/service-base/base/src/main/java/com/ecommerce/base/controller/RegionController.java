package com.ecommerce.base.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.base.api.dto.region.RegionDTO;
import com.ecommerce.base.api.dto.region.RegionForWharfDTO;
import com.ecommerce.base.api.dto.region.RegionLabelValueDTO;
import com.ecommerce.base.api.dto.region.RegionSampleDTO;
import com.ecommerce.base.api.dto.wharf.WharfImportTemplateOptionDTO;
import com.ecommerce.base.service.IRegionService;
import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Executor;


/**
 * null
 *
 * <AUTHOR>
*/
@Slf4j
@Tag(name = "RegionController", description = "行政区域查询服务")
@RestController
@RequestMapping("/region")
public class RegionController {

   @Autowired
   private IRegionService iRegionService;
   @Lazy
   @Autowired
   @Qualifier("myTaskAsyncPool")
   private Executor executor;

   @Operation(summary = "根据区域代码查询区/县或街道")
   @PostMapping(value="/findByAdcodeAndLevel")
   public List<RegionDTO> findByAdcodeAndLevel(@RequestParam String adcode,@RequestParam String level){
      return iRegionService.findByAdcodeAndLevel(adcode,level);
   }


   @Operation(summary = "查询城市")
   @PostMapping(value="/findCityByNameLike")
   public List<RegionDTO> findCityByNameLike(@RequestParam String cityNameLike){
      return iRegionService.findCityByNameLike(cityNameLike);
   }


   @Operation(summary = "根据区域代码查询区/县或街道")
   @PostMapping(value="/findByAdcodeAndLevelAndNameLike")
   public List<RegionDTO> findByAdcodeAndLevelAndNameLike(@RequestParam String adcode,@RequestParam String level,@RequestParam String nameLike){
      return iRegionService.findByAdcodeAndLevelAndNameLike(adcode,level,nameLike);
   }


   @Operation(summary = "查询省份")
   @PostMapping(value="/findProvinceByNameLike")
   public List<RegionDTO> findProvinceByNameLike(@RequestParam String provinceNameLike){
      return iRegionService.findProvinceByNameLike(provinceNameLike);
   }


   @Operation(summary = "查询所有数据")
   @PostMapping(value="/findAll")
   public List<RegionDTO> findAll(){
      return iRegionService.findAll();
   }


   @Operation(summary = "从高德同步数据，接口：https://lbs.amap.com/api/webservice/guide/api/district 目前需要同步，但不保存到Region表，保存到变更表，以便后续查找区县变化规律规律")
   @PostMapping(value="/syncDataFromAmap")
   public void syncDataFromAmap(@RequestParam String key){
      executor.execute(()->{
         try{
            Thread.currentThread().setName("syncAmapRegion");
            iRegionService.syncDataFromAmap(key);
         }catch (Exception e){
            log.error(e.getMessage(),e);
         }
      });
   }


   @Operation(summary = "根据区关键字，并且返回子区域，功能类似该接口功能（https://lbs.amap.com/api/webservice/guide/api/district），但比它简单点")
   @PostMapping(value="/findDistrict")
   public List<RegionDTO> findDistrict(@RequestParam String keywords,@RequestBody int subdistrict,@RequestBody int pageSize,@RequestBody boolean needPolyline){
      return iRegionService.findDistrict(keywords,subdistrict,pageSize,needPolyline);
   }


   @Operation(summary = "查询所有数据并转换成label-Value格式")
   @PostMapping(value="/findAll2LabelValue")
   public List<RegionLabelValueDTO> findAll2LabelValue() {
      return iRegionService.findAll2LabelValue();
   }

   @Operation(summary = "查询省市区数据并转换成label-Value格式")
   @PostMapping(value="/findProvinceCityDistrict2LabelValue")
   public List<RegionLabelValueDTO> findProvinceCityDistrict2LabelValue() {
      List<RegionLabelValueDTO> list = iRegionService.findProvinceCityDistrict2LabelValue();
      return list;
   }

   @Operation(summary = "根据当前层级adcode查找下一层级的数据")
   @PostMapping(value="/findByParentAdCode")
   public List<RegionSampleDTO> findByParentAdCode(@RequestParam String parentAdcode) {
      return iRegionService.findByParentAdCode(parentAdcode);
   }

   @Operation(summary = "根据上层级adcode和当前层级的名称查找当前层级的adcode")
   @PostMapping(value="/findAdCodeByParentAdCodeAndName")
   public String findAdCodeByParentAdCodeAndName(@RequestParam String parentAdcode,@RequestParam String name) {
      return iRegionService.findAdCodeByParentAdCodeAndName(parentAdcode,name);
   }

   @Operation(summary = "根据省份名称匹配查找城市的数据")
   @PostMapping(value="/findCityByProvinceName")
   public List<RegionSampleDTO> findCityByProvinceName(@RequestParam String provinceNameLike) {
      return iRegionService.findCityByProvinceName(provinceNameLike);
   }

   @Operation(summary = "查询所有省")
   @PostMapping(value="/findAllProvince")
   public List<RegionDTO> findAllProvince() {
      return iRegionService.findAllProvince();
   }

   @Operation(summary = "根据code查询名称")
   @PostMapping(value="/findNameByAdCode")
   public String findNameByAdCode(@RequestParam String adcode) {
      return iRegionService.findNameByAdCode(adcode);
   }

   @Operation(summary = "根据code批量查询名称")
   @PostMapping(value="/findNameByAdCodeList")
   public Map<String,String> findNameByAdCodeList(@RequestBody Set<String> adcodeList) {
      return iRegionService.findNameByAdCodeList(adcodeList);
   }

   @Operation(summary = "查询省市区街道数据并转换成label-Value格式")
   @PostMapping(value="/findProvinceCityDistrictStreet2LabelValue")
   public List<RegionLabelValueDTO> findProvinceCityDistrictStreet2LabelValue() {
      return iRegionService.findProvinceCityDistrictStreet2LabelValue();
   }

   @Operation(summary = "查询行政区域(批量上传码头调用）")
   @PostMapping(value="/findRegionForWharfDTO")
   public WharfImportTemplateOptionDTO findRegionForWharfDTO() {

      //查询省份数据
      List<RegionForWharfDTO>  province = iRegionService.findRegionForWharfDTO("province");
      //查询城市数据
      List<RegionForWharfDTO> city = iRegionService.findRegionForWharfDTO("city");
      //查询区县数据
      List<RegionForWharfDTO> district = iRegionService.findRegionForWharfDTO("district");

      List<String> provinceList = Lists.newLinkedList();
      List<String> provinceCodeList = Lists.newLinkedList();
      List<String> provinceParentList = Lists.newLinkedList();
      List<String> cityList = Lists.newLinkedList();
      List<String> cityCodeList = Lists.newLinkedList();
      List<String> cityParentList = Lists.newLinkedList();
      List<String> districtList = Lists.newLinkedList();
      List<String> districtCodeList = Lists.newLinkedList();
      List<String> districtParentList = Lists.newLinkedList();

      this.addDate(province,provinceList,provinceCodeList,provinceParentList);
      this.addDate(city,cityList,cityCodeList,cityParentList);
      this.addDate(district,districtList,districtCodeList,districtParentList);

      return new WharfImportTemplateOptionDTO(provinceList,provinceCodeList,cityList,cityCodeList,cityParentList,districtList,districtCodeList,districtParentList);
   }

   private void addDate(List<RegionForWharfDTO> regionForWharfDTOS,List<String> list,List<String> codeList,List<String> parentList){
      for (RegionForWharfDTO regionForWharfDTO : regionForWharfDTOS){
         list.add(regionForWharfDTO.getName());
         codeList.add(regionForWharfDTO.getCode());
         parentList.add(regionForWharfDTO.getParentName());
      }
   }

}
