package com.ecommerce.base.controller;

import com.ecommerce.base.api.dto.AccountRoleIdListDTO;
import com.ecommerce.base.api.dto.authRes.ResCheckDTO;
import com.ecommerce.base.api.dto.authRes.RoleAuthenticateDTO;
import com.ecommerce.base.api.dto.role.*;
import com.ecommerce.base.api.enums.BaseRoleTypeEnum;
import com.ecommerce.base.service.IRoleService;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.service.IRightAccessCacheService;
import com.ecommerce.common.service.access.RightAccessCache;
import com.ecommerce.member.api.dto.account.AccountChangeHistoryDTO;
import com.ecommerce.member.api.dto.account.AccountDTO;
import com.ecommerce.member.api.dto.member.enums.AccountChangeWayEnum;
import com.ecommerce.member.api.service.IAccountChangeHistoryService;
import com.ecommerce.member.api.service.IAccountService;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;


/**
 * @Created锛�Wed Mar 20 10:35:37 CST 2019
 * <AUTHOR>
 * @Version:2
 * @Description:角色相关service
*/
@Slf4j
@Tag(name = "Role", description = "角色相关service")
@RestController
@RequestMapping("/role")
public class RoleController {

   @Autowired 
   private IRoleService iRoleService;
   @Autowired
   private IAccountChangeHistoryService iAccountChangeHistoryService;
   @Autowired
   private IAccountService iAccountService;

   @Operation(summary = "null")
   @PostMapping(value="/findByRoleNameList")
   public List<RoleDTO> findByRoleNameList(@RequestBody List<String> arg0){
      return iRoleService.findByRoleNameList(arg0);
   }


   @Operation(summary = "根据账户id查询已分配的角色")
   @PostMapping(value="/getRoleByAccountId")
   public List<RoleDTO> getRoleByAccountId(@RequestParam String arg0){
      return iRoleService.getRoleByAccountId(arg0);
   }

   @Operation(summary = "根据账户id查询可访问的web端")
   @PostMapping(value="/getPlatformByAccountId")
   public Set<String> getPlatformByAccountId(@RequestParam String accountId){
      return iRoleService.getPlatformByAccountId(accountId);
   }

   @Operation(summary = "根据账户id批量查询可访问的web端")
   @PostMapping(value="/getPlatformByAccountIds")
   public Map<String,Set<String>> getPlatformByAccountIds(@RequestBody Set<String> accountIds){
      return iRoleService.getPlatformByAccountIds(accountIds);
   }



   @Operation(summary = "根据账户id查询已分配的角色")
   @PostMapping(value="/getRoleByAccountId2")
   public AccountRoleDTO getRoleByAccountId2(@RequestBody AccountRoleDTO dto){
      return iRoleService.getRoleByAccountId2(dto);
   }

   @Operation(summary = "更新账户角色关系")
   @PostMapping(value="/updateAccountRole")
   public void updateAccountRole(@RequestBody AccountRoleDTO arg0){
      log.info("updateAccountRole:{}",arg0);
      check(arg0);
      if(arg0.getIsImmediateEffect() == 1){
         //更新账户角色关系之前判断是否有未生效记录
         AccountChangeHistoryDTO accountChangeHistoryDTO = new AccountChangeHistoryDTO();
         accountChangeHistoryDTO.setAccountId(arg0.getAccountId());
         accountChangeHistoryDTO.setDelFlg(0);
         accountChangeHistoryDTO.setIsEffect(0);
         List<AccountChangeHistoryDTO> list = iAccountChangeHistoryService.findByQuery(accountChangeHistoryDTO);
         if( CollectionUtils.isNotEmpty(list) ){
            //如果存在未生效的历史记录，直接删除
            for(AccountChangeHistoryDTO accountChangeHistoryDTO1 : list){
               accountChangeHistoryDTO1.setDelFlg(1);
               iAccountChangeHistoryService.update(accountChangeHistoryDTO1,arg0.getOperatorId());
            }
         }
         iRoleService.updateAccountRole(arg0);
         //更新账户角色关系完成后添加历史记录
         AccountChangeHistoryDTO accountChangeHistoryDTO3 = new AccountChangeHistoryDTO();
         AccountDTO accountDTO1 = iAccountService.findById(arg0.getAccountId());
         AccountDTO accountDTO2 = iAccountService.findById(arg0.getOperatorId());
         accountChangeHistoryDTO3.setAccountId(arg0.getAccountId());
         accountChangeHistoryDTO3.setRealName(accountDTO1 == null ? "" : accountDTO1.getRealName());
         accountChangeHistoryDTO3.setOperatroName(accountDTO2 == null ? "" : accountDTO2.getRealName());
         accountChangeHistoryDTO3.setWay(AccountChangeWayEnum.AUTHORIZATION.getCode());
         accountChangeHistoryDTO3.setRoleIdList(arg0.getRoleIds());
         accountChangeHistoryDTO3.setReason("角色授权");
         accountChangeHistoryDTO3.setIsEffect(1);
         accountChangeHistoryDTO3.setIsImmediateEffect(1);
         accountChangeHistoryDTO3.setEffectTime(new Date());
         iAccountChangeHistoryService.add(accountChangeHistoryDTO3,arg0.getOperatorId());
      }else {
         //更新账户角色关系完成后添加历史记录
         AccountChangeHistoryDTO accountChangeHistoryDTO4 = new AccountChangeHistoryDTO();
         AccountDTO accountDTO1 = iAccountService.findById(arg0.getAccountId());
         AccountDTO accountDTO2 = iAccountService.findById(arg0.getOperatorId());
         accountChangeHistoryDTO4.setAccountId(arg0.getAccountId());
         accountChangeHistoryDTO4.setRealName(accountDTO1 == null ? "" : accountDTO1.getRealName());
         accountChangeHistoryDTO4.setOperatroName(accountDTO2 == null ? "" : accountDTO2.getRealName());
         accountChangeHistoryDTO4.setWay(AccountChangeWayEnum.AUTHORIZATION.getCode());
         accountChangeHistoryDTO4.setRoleIdList(arg0.getRoleIds());
         accountChangeHistoryDTO4.setReason("角色授权");
         accountChangeHistoryDTO4.setIsEffect(0);
         accountChangeHistoryDTO4.setIsImmediateEffect(0);
         accountChangeHistoryDTO4.setEffectTime(arg0.getEffectTime());
         iAccountChangeHistoryService.add(accountChangeHistoryDTO4,arg0.getOperatorId());
      }
   }

   private void check(AccountRoleDTO arg0){
      if(arg0.getIsImmediateEffect() == null){
         throw new BizException(BasicCode.PARAM_NULL, "是否立即生效");
      }
   }


   @Operation(summary = "给一个账户设置为个人司机角色")
   @PostMapping(value="/setIndividualDriver2Account")
   public void setIndividualDriver2Account(@RequestParam String arg0,@RequestParam String arg1){
      iRoleService.setIndividualDriver2Account(arg0,arg1);

   }

   @Operation(summary = "给一个账户设置为个体船东角色")
   @PostMapping(value="/setIndividualShipowner2Account")
   public void setIndividualShipowner2Account(@RequestParam String arg0,@RequestParam String arg1){
      iRoleService.setIndividualShipowner2Account(arg0,arg1);

   }


   @Operation(summary = "给一个账户设置为供应商角色")
   @PostMapping(value="/setSupplier2Account")
   public void setSupplier2Account(@RequestParam String arg0,@RequestParam String arg1,@RequestParam String arg2){
      iRoleService.setSupplier2Account(arg0,arg1,arg2);

   }


   @Operation(summary = "筛选出含有roleId的所有accountId")
   @PostMapping(value="/accountRoleFilter")
   public List<String> accountRoleFilter(@RequestBody AccountRoleIdListDTO arg0){
      return iRoleService.accountRoleFilter(arg0);
   }


   @Operation(summary = "变更承运商主账号 更新角色")
   @PostMapping(value="/updateCarrierMainAccount")
   public void updateCarrierMainAccount(@RequestParam String arg0,@RequestParam String arg1,@RequestParam String arg2){
      iRoleService.updateCarrierMainAccount(arg0,arg1,arg2);

   }


   @Operation(summary = "变更企业买家主账号 更新角色")
   @PostMapping(value="/updateEnterpriseBuyerMainAccount")
   public void updateEnterpriseBuyerMainAccount(@RequestParam String arg0,@RequestParam String arg1,@RequestParam String arg2){
      iRoleService.updateEnterpriseBuyerMainAccount(arg0,arg1,arg2);

   }


   @Operation(summary = "给一个账户设置为企业卖家角色")
   @PostMapping(value="/setEnterpriseSeller2Account")
   public void setEnterpriseSeller2Account(@RequestParam String arg0,@RequestParam String arg1,@RequestParam String arg2){
      iRoleService.setEnterpriseSeller2Account(arg0,arg1,arg2);

   }


   @Operation(summary = "给一个账户设置为企业买家角色")
   @PostMapping(value="/setEnterpriseBuyer2Account")
   public void setEnterpriseBuyer2Account(@RequestParam String arg0,@RequestParam String arg1,@RequestParam String arg2){
      iRoleService.setEnterpriseBuyer2Account(arg0,arg1,arg2);

   }


   @Operation(summary = "获取会员可用的权限")
   @PostMapping(value="/getRoleByMemberId")
   public List<RoleDTO> getRoleByMemberId(@RequestParam String arg0){
      return iRoleService.getRoleByMemberId(arg0);
   }

   @Operation(summary = "获取会员可用的权限")
   @PostMapping(value="/getRoleByMemberId2")
   public MemberRoleDTO getRoleByMemberId2(@RequestBody MemberRoleDTO dto){
      return iRoleService.getRoleByMemberId2(dto);
   }

   @Operation(summary = "给一个账户设置为承运商角色")
   @PostMapping(value="/setCarrier2Account")
   public void setCarrier2Account(@RequestParam String memberId,@RequestParam String mainAccountId,@RequestParam(required = false) String carrierType,@RequestParam String operator){
      iRoleService.setCarrier2Account(memberId,mainAccountId,carrierType,operator);

   }


   @Operation(summary = "获取所有角色，不分页，按会员id查,会员授权使用")
   @PostMapping(value="/getAllRoleListAndMemberSelected")
   public List<RoleDTO> getAllRoleListAndMemberSelected(@RequestParam String arg0){
      return iRoleService.getAllRoleListAndMemberSelected(arg0);
   }


   @Operation(summary = "变更主账号角色")
   @PostMapping(value="/updateMainAccountRoleType")
   public void updateMainAccountRoleType(@RequestParam String arg0,@RequestParam String arg1,@RequestBody List<BaseRoleTypeEnum> arg2,@RequestParam String arg3){
      iRoleService.updateMainAccountRoleType(arg0,arg1,arg2,arg3);

   }


   @Operation(summary = "根据id删除角色")
   @PostMapping(value="/deleteRoleById")
   public void deleteRoleById(@RequestParam Integer arg0,@RequestParam String arg1){
      iRoleService.deleteRoleById(arg0,arg1);

   }


   @Operation(summary = "查询单个角色")
   @PostMapping(value="/findById")
   public RoleDTO findById(@RequestParam Integer arg0){
      return iRoleService.findById(arg0);
   }

   @Operation(summary = "查询单个角色和其权限")
   @PostMapping(value="/findRoleAndPermission")
   public RoleAuthenticateDTO findRoleAndPermission(@RequestParam Integer roleId) {
      return iRoleService.findRoleAndPermission(roleId);
   }


   @Operation(summary = "设置角色可用")
   @PostMapping(value="/enableRole")
   public void enableRole(@RequestParam Integer arg0,@RequestParam String arg1){
      iRoleService.enableRole(arg0,arg1);

   }


   @Operation(summary = "创建role")
   @PostMapping(value="/createRole")
   public Integer createRole(@RequestBody RoleDTO arg0){
      return iRoleService.createRole(arg0);

   }


   @Operation(summary = "添加账户角色关系")
   @PostMapping(value="/addAccountRole")
   public void addAccountRole(@RequestBody AccountRoleDTO arg0){
      iRoleService.addAccountRole(arg0);

   }


   @Operation(summary = "更新角色")
   @PostMapping(value="/updateRole")
   public void updateRole(@RequestBody RoleDTO arg0){
      iRoleService.updateRole(arg0);

   }


   @Operation(summary = "判断账号是否拥有角色")
   @PostMapping(value="/hasAnyRole")
   public boolean hasAnyRole(@RequestBody HasAnyRoleDTO arg0){
      return iRoleService.hasAnyRole(arg0);
   }


   @Operation(summary = "获取所有角色列表")
   @PostMapping(value="/findAll")
   public PageInfo<RoleDTO> findAll(@RequestBody RoleQueryDTO arg0){
      return iRoleService.findAll(arg0);
   }


   @Operation(summary = "null")
   @PostMapping(value="/findByRoleName")
   public RoleDTO findByRoleName(@RequestParam String arg0){
      return iRoleService.findByRoleName(arg0);
   }


   @Operation(summary = "设置角色不可用")
   @PostMapping(value="/disableRole")
   public void disableRole(@RequestParam Integer arg0,@RequestParam String arg1){
      iRoleService.disableRole(arg0,arg1);

   }


   @Operation(summary = "添加会员角色关系")
   @PostMapping(value="/addMemberRole")
   public void addMemberRole(@RequestBody MemberRoleDTO arg0){
      iRoleService.addMemberRole(arg0);

   }


   @Operation(summary = "null")
   @PostMapping(value="/findByIds")
   public List<RoleDTO> findByIds(@RequestBody List<Integer> arg0){
      return iRoleService.findByIds(arg0);
   }


   @Operation(summary = "更新会员角色")
   @PostMapping(value="/updateMemberRole")
   public void updateMemberRole(@RequestBody MemberRoleDTO arg0){
      iRoleService.updateMemberRole(arg0);
   }

   @Operation(summary = "移除会员角色(如果会员角色表有授权)")
   @PostMapping(value="/removeMemberRole")
   public void removeMemberRole(@RequestBody MemberRoleDTO arg0){
      iRoleService.removeMemberRole(arg0);
   }


   @Operation(summary = "获取会员角色，不分页，按会员id、账户id查,子账户授权使用")
   @PostMapping(value="/getMemberRoleListAndAccountSelected")
   public List<RoleDTO> getMemberRoleListAndAccountSelected(@RequestParam String arg0,@RequestParam String arg1){
      return iRoleService.getMemberRoleListAndAccountSelected(arg0,arg1);
   }


   @Operation(summary = "变更企业卖家主账号 更新角色")
   @PostMapping(value="/updateEnterpriseSellerMainAccount")
   public void updateEnterpriseSellerMainAccount(@RequestParam String arg0,@RequestParam String arg1,@RequestParam String arg2){
      iRoleService.updateEnterpriseSellerMainAccount(arg0,arg1,arg2);

   }

   @Operation(summary = "校验是否有资源权限")
   @PostMapping(value="/checkRes")
   public Boolean checkRes(@RequestBody ResCheckDTO dto){
      return iRoleService.checkRes(dto);
   }

   @Operation(summary = "变更企业卖家主账号 更新角色")
   @PostMapping(value="/clearCache")
   public void clearCache(@RequestParam("operator") String arg0){
      iRoleService.clearCache(arg0);

   }

   @Autowired
   private IRightAccessCacheService rightAccessCacheService;

   //开发测试使用
   @Operation(summary = "测试角色是否有url的访问权限")
   @PostMapping(value="/hasRightAccess")
   public Boolean hasRightAccess(@RequestBody Map<String,Object> map){
      if( !map.containsKey("url") || !map.containsKey("roleId")){
         log.info("请求json中必须包含url roleId");
         return false;
      }
      String url = (String)map.get("url");
      int role = (Integer)map.get("roleId");
      RightAccessCache rightAccessCache = rightAccessCacheService.getRightAccess(url);
      if(rightAccessCache == null){
         log.info("url is not exit in controller,return default false");
         return false;
      }
      long[] roles = rightAccessCache.getRoles();
      log.info("rightAccessCache.getRoles():{}",roles);
      long tmp = 1L << (role % 64 - 1);
      if ((roles[role / 65] & tmp) == tmp) {
         return true;
      }
      List<Integer> roleIdList = Lists.newArrayList();
      for (int i = 1; i <1000; i++) {
         long tmp1 = 1L << (i % 64 - 1);
         if ((roles[i / 65] & tmp1) == tmp1) {
            roleIdList.add(i);
         }
      }
      log.info("可访问url的角色有:{}",roleIdList);
      return false;
   }

}
