package com.ecommerce.base.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.base.api.dto.SaleRegionErpInfo.SaleRegionErpInfoDTO;
import com.ecommerce.base.api.dto.SaleRegionErpInfo.SaleRegionErpInfoKeywordQueryDTO;
import com.ecommerce.base.api.dto.SaleRegionErpInfo.SaleRegionErpInfoQueryDTO;
import com.ecommerce.base.api.dto.saleregion.AccountSaleRegionUpdateDTO;
import com.ecommerce.base.api.dto.saleregion.FindDetailRegionbyLevelDTO;
import com.ecommerce.base.api.dto.saleregion.MemberApproveSaleRegionCheckDTO;
import com.ecommerce.base.api.dto.saleregion.RegionQueryDTO;
import com.ecommerce.base.api.dto.saleregion.SaleLevelDTO;
import com.ecommerce.base.api.dto.saleregion.SaleRegionAddDTO;
import com.ecommerce.base.api.dto.saleregion.SaleRegionCondDTO;
import com.ecommerce.base.api.dto.saleregion.SaleRegionDTO;
import com.ecommerce.base.api.dto.saleregion.SaleRegionOption;
import com.ecommerce.base.api.dto.saleregion.SaleRegionRegionQueryDTO;
import com.ecommerce.base.api.dto.saleregion.SaleRegionRelationDTO;
import com.ecommerce.base.api.dto.saleregion.SaleRegionSampleDTO;
import com.ecommerce.base.api.dto.saleregion.SaleRegionStoreAddDTO;
import com.ecommerce.base.api.dto.saleregion.SaleRegionStoreDeleteDTO;
import com.ecommerce.base.api.dto.saleregion.SaleRegionTreeDTO;
import com.ecommerce.base.api.dto.saleregion.SaleRegionUpdateDTO;
import com.ecommerce.base.api.dto.saleregion.SaleRgionRelationAddDTO;
import com.ecommerce.base.api.dto.saleregion.SaleStoreDTO;
import com.ecommerce.base.api.dto.saleregion.SetDefaultStoreDTO;
import com.ecommerce.base.service.ISaleRegionService;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Set;


/**
 * 1、销售区域为一个树行结构数据,表：ba_sale_region<br/> 2、每个销售区域节点下关联一个或者多个仓库信息，表：ba_sale_region_store<br/> 2、每个销售区域节点下关联一个或者多个行政区域信息，表：ba_sale_region_relation<br/>
 *
 * <AUTHOR>
*/

@RestController
@Tag(name = "SaleRegion", description = "1、销售区域为一个树行结构数据,表：ba_sale_region<br/> 2、每个销售区域节点下关联一个或者多个仓库信息，表：ba_sale_region_store<br/> 2、每个销售区域节点下关联一个或者多个行政区域信息，表：ba_sale_region_relation<br/>")
@RequestMapping("/saleRegion")
public class SaleRegionController {

   @Autowired 
   private ISaleRegionService iSaleRegionService;

   @Operation(summary = "查询指定区域下一级的区域信息(不含仓库信息)")
   @PostMapping(value="/findOnlyChildsById")
   public List<SaleRegionSampleDTO> findOnlyChildsById(@RequestParam String id){
      return iSaleRegionService.findOnlyChildsById(id);
   }


   @Operation(summary = "添加销售区域下的行政区域")
   @PostMapping(value="/addRegionRelation")
   public void addRegionRelation(@RequestBody List<SaleRgionRelationAddDTO> saleRgionRelationAddDTOList,@RequestParam String operator){
      iSaleRegionService.addRegionRelation(saleRgionRelationAddDTOList,operator);

   }


   @Operation(summary = "删除销售区域下的行政区域")
   @PostMapping(value="/deleteRegionRelation")
   public void deleteRegionRelation(@RequestBody List<String> ids,@RequestParam String operator){
      iSaleRegionService.deleteRegionRelation(ids,operator);

   }

   @Operation(summary = "清除默认仓库")
   @PostMapping(value="/cancelDefaultStore")
   public void cancelDefaultStore(@RequestParam String id,@RequestParam String saleRegionId,@RequestParam String operator){
      iSaleRegionService.cancelDefaultStore(id,saleRegionId,operator);
   }

   @Operation(summary = "设置默认仓库")
   @PostMapping(value="/setDefaultStore")
   public void setDefaultStore(@RequestBody SetDefaultStoreDTO dto,@RequestParam String operator){
      iSaleRegionService.setDefaultStore(dto,operator);

   }


   @Operation(summary = "删除销售区域（会同时删除子区域），并且删除对应的仓库和区域关联关系")
   @PostMapping(value="/deleteSaleRegion")
   public void deleteSaleRegion(@RequestParam String id,@RequestParam String operator){
      iSaleRegionService.deleteSaleRegion(id,operator);

   }


   @Operation(summary = "更新销售区域，整体更新")
   @PostMapping(value="/updateSaleRegion")
   public void updateSaleRegion(@RequestBody SaleRegionUpdateDTO saleRegionUpdateDTO,@RequestParam String operator){
      iSaleRegionService.updateSaleRegion(saleRegionUpdateDTO,operator);

   }


   @Operation(summary = "添加销售区域(含0个或多个仓库信息)")
   @PostMapping(value="/addSaleRegion")
   public void addSaleRegion(@RequestBody SaleRegionAddDTO saleRegionAddDTO,@RequestParam String operator){
      iSaleRegionService.addSaleRegion(saleRegionAddDTO,operator);

   }


   @Operation(summary = "表：ba_account_sale_region 查询人员-销售区域关系表关系")
   @PostMapping(value="/findByAccountId")
   public List<SaleRegionDTO> findByAccountId(@RequestParam String accountId){
      return iSaleRegionService.findByAccountId(accountId);
   }

   @Operation(summary = "表：ba_account_sale_region 查询人员-销售区域关系表关系-a")
   @PostMapping(value="/findByAccountIdWithDelFlg")
   public List<SaleRegionDTO> findByAccountIdWithDelFlg(@RequestParam String accountId){
      return iSaleRegionService.findByAccountIdWithDelFlg(accountId);
   }

   @Operation(summary = "根据销售区域id 查询其下的仓库列表")
   @PostMapping(value="/findStoreById")
   public List<SaleStoreDTO> findStoreById(@RequestParam String id){
      return iSaleRegionService.findStoreById(id);
   }


   @Operation(summary = "根据销售区域id 查询其下和所有子区域下的仓库列表（注意过滤重复）")
   @PostMapping(value="/findAllStoreById")
   public List<SaleStoreDTO> findAllStoreById(@RequestParam String id){
      return iSaleRegionService.findAllStoreById(id);
   }


   @Operation(summary = "获取默认仓库")
   @PostMapping(value="/getDefaultStore")
   public SaleStoreDTO getDefaultStore(@RequestParam String id){
      return iSaleRegionService.getDefaultStore(id);
   }


   @Operation(summary = "查询单个销售区域信息(不含0个或多个仓库和行政信息)")
   @PostMapping(value="/findSampleById")
   public SaleRegionSampleDTO findSampleById(@RequestParam String id){
      return iSaleRegionService.findSampleById(id);
   }

   @Operation(summary = "查询批量的销售区域信息")
   @PostMapping(value = "/findSampleByIdList")
   public List<SaleRegionSampleDTO> findSampleByIdList(@RequestParam List<String> saleRegionIdList) {
      return iSaleRegionService.findSampleByIdList(saleRegionIdList);
   }

   @Operation(summary = "查询指定区域下一级的区域信息(含仓库信息)")
   @PostMapping(value="/findChildsById")
   public List<SaleRegionDTO> findChildsById(@RequestParam String id){
      return iSaleRegionService.findChildsById(id);
   }


   @Operation(summary = "查询单个销售区域信息(含0个或多个仓库和行政信息)")
   @PostMapping(value="/findById")
   public SaleRegionDTO findById(@RequestParam String id){
      return iSaleRegionService.findById(id);
   }


   @Operation(summary = "删除销售区域下的仓库")
   @PostMapping(value="/deleteStore")
   public void deleteStore(@RequestBody SaleRegionStoreDeleteDTO saleRegionStoreDeleteDTO,@RequestParam String operator){
      iSaleRegionService.deleteStore(saleRegionStoreDeleteDTO,operator);

   }


   @Operation(summary = "添加销售区域下的仓库")
   @PostMapping(value="/addStore")
   public void addStore(@RequestBody SaleRegionStoreAddDTO saleRegionStoreAddDTO,@RequestParam String operator){
      iSaleRegionService.addStore(saleRegionStoreAddDTO,operator);

   }


   @Operation(summary = "查询一个会员下的所有销售区域(含仓库信息)")
   @PostMapping(value="/findAllDetailByMemberId")
   public List<SaleRegionDTO> findAllDetailByMemberId(@RequestParam String memberId){
      return iSaleRegionService.findAllDetailByMemberId(memberId);
   }


   @Operation(summary = "查询一个会员下某个层级的销售区域（包括仓库）")
   @PostMapping(value="/findDetailRegionbyLevel")
   public List<SaleRegionDTO> findDetailRegionbyLevel(@RequestParam Integer level,@RequestParam String memberId){
      return iSaleRegionService.findDetailRegionbyLevel(level,memberId);
   }

   @Operation(summary = "根据会员id和erpCode查询")
   @PostMapping(value="/findByMemberIdAndErpCode")
   public SaleRegionDTO findByMemberIdAndErpCode(@RequestParam String memberId,@RequestParam String erpCode) {
      return iSaleRegionService.findByMemberIdAndErpCode(memberId,erpCode);
   }

   @Operation(summary = "查询一个会员下某个层级的销售区域（包括仓库）,并过滤")
   @PostMapping(value="/findDetailRegionbyLevelAndFilter")
   public List<SaleRegionDTO> findDetailRegionbyLevelAndFilter(@RequestBody FindDetailRegionbyLevelDTO dto){
      return iSaleRegionService.findDetailRegionbyLevelAndFilter(dto);
   }

   @Operation(summary = "查询一个账号下某个层级的销售区域（包括仓库）")
   @PostMapping(value="/findDetailRegionbyLevelAndAccountId")
   public List<SaleRegionDTO> findDetailRegionbyLevelAndAccountId(@RequestParam Integer level,@RequestParam String accountId){
      return iSaleRegionService.findDetailRegionbyLevelAndAccountId(level,accountId);
   }

   @Operation(summary = "根据销售区域id 查询其下和所有子区域下的行政区域列表（注意过滤重复）")
   @PostMapping(value="/findAllRgionRelationById")
   public List<SaleRegionRelationDTO> findAllRgionRelationById(@RequestParam String id){
      return iSaleRegionService.findAllRgionRelationById(id);
   }


   @Operation(summary = "查询一个会员下的所有销售区域(不含仓库信息)")
   @PostMapping(value="/findAllSampleByMemberId")
   public List<SaleRegionSampleDTO> findAllSampleByMemberId(@RequestParam String memberId){
      return iSaleRegionService.findAllSampleByMemberId(memberId);
   }

   @Operation(summary = "查询一个会员下的所有销售区域树形结构(不含仓库信息)")
   @PostMapping(value="/findTreeByMemberId")
   public List<SaleRegionTreeDTO> findTreeByMemberId(@RequestParam String memberId){
      return iSaleRegionService.findTreeByMemberId(memberId);
   }


   @Operation(summary = "查询一个会员下的销售区域层级")
   @PostMapping(value="/findSaleLevelByMemberId")
   public List<SaleLevelDTO> findSaleLevelByMemberId(@RequestParam String memberId){
      return iSaleRegionService.findSaleLevelByMemberId(memberId);
   }


   @Operation(summary = "表：ba_account_sale_region 添加或删除人员-销售区域关系")
   @PostMapping(value="/updateAccountSaleRegion")
   public void updateAccountSaleRegion(@RequestBody AccountSaleRegionUpdateDTO accountSaleRegionUpdateDTO,@RequestParam String operator){
      iSaleRegionService.updateAccountSaleRegion(accountSaleRegionUpdateDTO,operator);

   }


   @Operation(summary = "根据销售区域id 查询其下的行政区域列表")
   @PostMapping(value="/findRgionRelationById")
   public List<SaleRegionRelationDTO> findRgionRelationById(@RequestParam String id){
      return iSaleRegionService.findRgionRelationById(id);
   }

   @Operation(summary = "翻页查找销售区域erp信息表")
   @PostMapping(value="/findAllErpInfo")
   public PageInfo<SaleRegionErpInfoDTO> findAllErpInfo(@RequestBody SaleRegionErpInfoQueryDTO dto) {
      return iSaleRegionService.findAllErpInfo(dto);
   }

   @Operation(summary = "查找单个销售区域erp信息表")
   @PostMapping(value="/findErpInfo")
   public SaleRegionErpInfoDTO findErpInfo(@RequestParam String memberId,@RequestParam String erpOrgCode) {
      return iSaleRegionService.findErpInfo(memberId,erpOrgCode);
   }

   @Operation(summary = "根据关键字查找销售区域erp信息表")
   @PostMapping(value="/findErpInfoByKeyword")
   public List<SaleRegionErpInfoDTO> findErpInfoByKeyword(@RequestBody SaleRegionErpInfoKeywordQueryDTO dto) {
      return iSaleRegionService.findErpInfoByKeyword(dto);
   }

   @Operation(summary = "同步销售区域erp信息")
   @PostMapping(value="/syncErpInfo")
   public Boolean syncErpInfo(@RequestParam String memberId,@RequestParam String operatorId) {
      return iSaleRegionService.syncErpInfo(memberId,operatorId);
   }

   @Operation(summary = "查询卖家所有仓库")
   @PostMapping(value="/findStoreBySellerId")
   public List<SaleStoreDTO> findStoreBySellerId(@RequestParam String sellerId) {
      return iSaleRegionService.findStoreBySellerId(sellerId);
   }

   @Operation(summary = "销售区域的批量查询")
   @PostMapping(value="/batchQuerySaleRegion")
   public List<SaleRegionSampleDTO> batchQuerySaleRegion(@RequestBody SaleRegionCondDTO saleRegionCondDTO) {
      return iSaleRegionService.batchQuerySaleRegion(saleRegionCondDTO);
   }

   /**
    * 查询销售区域id->名字映射
    * @param saleRegionIdSet
    * @return
    */
   @Operation(summary = "查询销售区域id->名字映射")
   @PostMapping(value="/querySaleRegionOptions")
   public List<SaleRegionOption> querySaleRegionOptions(@RequestBody Set<String> saleRegionIdSet) {
      return iSaleRegionService.querySaleRegionOptions(saleRegionIdSet);
   }

   @Operation(summary = "根据行政区域查询销售区域")
   @PostMapping(value="/findByRegion")
   public List<SaleRegionSampleDTO> findByRegion(@RequestBody RegionQueryDTO dto) {
      return iSaleRegionService.findByRegion(dto);
   }

   @Operation(summary = "根据名称模糊查询一级销售区域")
   @PostMapping(value="/querySaleRegionBySaleRegionName")
   public List<SaleRegionOption> querySaleRegionBySaleRegionName(@RequestBody SaleRegionOption dto) {
      return iSaleRegionService.querySaleRegionBySaleRegionName(dto);
   }

   @Operation(summary = "会员注册审批使用，判断哪些会员与企业买家注册时的省代码有交集，返回有交集的省代码")
   @PostMapping(value="/hasIntersection")
   public List<String> hasIntersection(@RequestBody MemberApproveSaleRegionCheckDTO dto) {
      return iSaleRegionService.hasIntersection(dto);
   }

   @Operation(summary = "查询会员的销售区域覆盖的省代码")
   @PostMapping(value="/findRegionByMemberId")
   public List<String> findRegionByMemberId(@RequestBody SaleRegionRegionQueryDTO dto) {
      return iSaleRegionService.findRegionByMemberId(dto);
   }
}
