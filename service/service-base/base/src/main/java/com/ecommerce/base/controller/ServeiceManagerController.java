package com.ecommerce.base.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.base.api.dto.authRes.PageDTO;
import com.ecommerce.base.api.dto.authRes.ServiceDTO;
import com.ecommerce.base.service.permission.resource.IServeiceManagerService;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * @Created锛�Fri Mar 08 11:18:55 CST 2019
 * <AUTHOR>
 * @Version:2
 * @Description:资源管理_服务
*/

@RestController
@Tag(name = "ServeiceManager", description = "资源管理_服务")
@RequestMapping("/serveiceManager")
public class ServeiceManagerController {

   @Autowired 
   private IServeiceManagerService iServeiceManagerService;

   @Operation(summary = "添加服务")
   @PostMapping(value="/createService")
   public ServiceDTO createService(@RequestBody ServiceDTO service){
      return iServeiceManagerService.createService(service);
   }


   @Operation(summary = "通过主键查询服务")
   @PostMapping(value="/findServiceById")
   public ServiceDTO findServiceById(@RequestParam String id){
      return iServeiceManagerService.findServiceById(id);
   }


   @Operation(summary = "删除服务")
   @PostMapping(value="/delService")
   public int delService(@RequestParam String id,@RequestParam String operator){
      return iServeiceManagerService.delService(id,operator);
   }


   @Operation(summary = "检查重复")
   @PostMapping(value="/checkExist")
   public boolean checkExist(@RequestParam String code){
      return iServeiceManagerService.checkExist(code);
   }


   @Operation(summary = "修改服务")
   @PostMapping(value="/updateService")
   public int updateService(@RequestBody ServiceDTO service){
      return iServeiceManagerService.updateService(service);
   }


   @Operation(summary = "条件查询服务")
   @PostMapping(value="/findServiceByCondiftion")
   public PageInfo<ServiceDTO> findServiceByCondiftion(@RequestBody ServiceDTO service){
      return iServeiceManagerService.findServiceByCondiftion(service);
   }


   @Operation(summary = "通过页面查询服务")
   @PostMapping(value="/findServiceByPage")
   public List<ServiceDTO> findServiceByPage(@RequestParam String pageId){
      return iServeiceManagerService.findServiceByPage(pageId);
   }


   @Operation(summary = "通过角色查询已授权服务")
   @PostMapping(value="/findSelectServiceByRoleId")
   public List<ServiceDTO> findSelectServiceByRoleId(@RequestParam String roleId){
      return iServeiceManagerService.findSelectServiceByRoleId(roleId);
   }


   @Operation(summary = "通过服务查询所属页面")
   @PostMapping(value="/findPageByService")
   public List<PageDTO> findPageByService(@RequestParam String serviceId){
      return iServeiceManagerService.findPageByService(serviceId);
   }


   @Operation(summary = "通过角色查询服务")
   @PostMapping(value="/findServiceByRoleId")
   public List<ServiceDTO> findServiceByRoleId(@RequestParam String roleId){
      return iServeiceManagerService.findServiceByRoleId(roleId);
   }

   @Operation(summary = "通过关键字查询服务（服务代码、服务名称、url、url说明）")
   @PostMapping(value="/findByKeyword")
   public List<ServiceDTO> findByKeyword(@RequestParam String keyword){
      return iServeiceManagerService.findByKeyword(keyword);
   }

   @Operation(summary = "通过平台查询服务资源")
   @PostMapping(value="/findByPlatform")
   public List<ServiceDTO> findByPlatform(@RequestParam String platform){
      return iServeiceManagerService.findByPlatform(platform);
   }
}
