package com.ecommerce.base.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.base.api.dto.sysLog.SysLogDTO;
import com.ecommerce.base.api.dto.sysLog.SysLogQueryDTO;
import com.ecommerce.base.service.ISysLogQueryService;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * @Created锛�Tue Dec 25 15:41:35 CST 2018
 * <AUTHOR>
 * @Version:2
 * @Description:null
*/

@RestController
@Tag(name = "SysLogQuery", description = "null")
@RequestMapping("/sysLogQuery")
public class SysLogQueryController {

   @Autowired 
   private ISysLogQueryService sysLogQueryService;

   @Operation(summary = "查询单条日志，含异常堆栈信息")
   @PostMapping(value="/findDetailById")
   public SysLogDTO findDetailById(@RequestParam String id){
      return sysLogQueryService.findDetailById(id);
   }

   @Operation(summary = "查询单条日志")
   @PostMapping(value="/findById")
   public SysLogDTO findById(@RequestParam String id){
      return sysLogQueryService.findById(id);
   }

   @Operation(summary = "根据条件翻页查询")
   @PostMapping(value="/findAll")
   public PageInfo<SysLogDTO> findAll(@RequestBody SysLogQueryDTO dto){
      return sysLogQueryService.findAll(dto);
   }

   @Operation(summary = "根据条件翻页查询(异常日志)")
   @PostMapping(value="/findAllException")
   public PageInfo<SysLogDTO> findAllException(@RequestBody SysLogQueryDTO dto){
      return sysLogQueryService.findAllException(dto);
   }

}
