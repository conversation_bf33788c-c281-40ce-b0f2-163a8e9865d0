package com.ecommerce.base.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.base.api.dto.*;
import com.ecommerce.base.service.IValueSetService;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * : 值集服务
 *
 * <AUTHOR>
*/

@RestController
@Tag(name = "ValueSet", description = ": 值集服务")
@RequestMapping("/valueSet")
public class ValueSetController {

   @Autowired 
   private IValueSetService iValueSetService;

   @Operation(summary = "更新值集和值集项")
   @PostMapping(value="/updateValueAndOption")
   public void updateValueAndOption(@RequestBody ValueAndOptionDTO valueAndOptionDTO,@RequestParam String operator){
      iValueSetService.updateValueAndOption(valueAndOptionDTO,operator);

   }


   @Operation(summary = "创建值集和值集项")
   @PostMapping(value="/createValueAndOption")
   public void createValueAndOption(@RequestBody ValueAndOptionDTO valueAndOptionDTO,@RequestParam String operator){
      iValueSetService.createValueAndOption(valueAndOptionDTO,operator);

   }


   @Operation(summary = "创建值集项")
   @PostMapping(value="/createValueSetOption")
   public void createValueSetOption(@RequestBody ValueSetOptionDTO valueSet,@RequestParam String operator){
      iValueSetService.createValueSetOption(valueSet,operator);

   }


   @Operation(summary = "通过值集id删除值集")
   @PostMapping(value="/deleteValueSet")
   public void deleteValueSet(@RequestParam String valueSetId,@RequestParam String operator){
      iValueSetService.deleteValueSet(valueSetId,operator);

   }


   @Operation(summary = "根据值集id得到值集")
   @PostMapping(value="/getValueSet")
   public ValueSetDTO getValueSet(@RequestParam String valueSetId){
      return iValueSetService.getValueSet(valueSetId);
   }


   @Operation(summary = "值集分页查询")
   @PostMapping(value="/pageValueSet")
   public PageInfo<ValueSetDTO> pageValueSet(@RequestBody ValueQueryDTO valueQueryDTO){
      return iValueSetService.pageValueSet(valueQueryDTO);
   }


   @Operation(summary = "该值集是否可修改")
   @PostMapping(value="/canValueSetEdit")
   public Boolean canValueSetEdit(@RequestParam String valueSetId){
      return iValueSetService.canValueSetEdit(valueSetId);
   }


   @Operation(summary = "创建值集")
   @PostMapping(value="/createValueSet")
   public void createValueSet(@RequestBody ValueSetDTO valueSet,@RequestParam String operator){
      iValueSetService.createValueSet(valueSet,operator);

   }


   @Operation(summary = "修改值集")
   @PostMapping(value="/updateValueSet")
   public void updateValueSet(@RequestBody ValueSetDTO valueSet,@RequestParam String operator){
      iValueSetService.updateValueSet(valueSet,operator);

   }


   @Operation(summary = "通过key查询值集合")
   @PostMapping(value="/findOptionByKey")
   public List<ValueSetOptionDTO> findOptionByKey(@RequestParam String key){
      return iValueSetService.findOptionByKey(key);
   }


   @Operation(summary = "通过key的集合查询值集合")
   @PostMapping(value="/batchFindOption")
   public List<ValueSetOptionDTO> batchFindOption(@RequestBody List<String> keys){
      return iValueSetService.batchFindOption(keys);
   }


   @Operation(summary = "通过值集项id删除值集项")
   @PostMapping(value="/deleteValueSetOption")
   public void deleteValueSetOption(@RequestParam String valueSetId,@RequestParam String operator){
      iValueSetService.deleteValueSetOption(valueSetId,operator);

   }


   @Operation(summary = "根据值集项id得到值集项")
   @PostMapping(value="/getValueSetOption")
   public ValueSetOptionDTO getValueSetOption(@RequestParam String valueSetOptionId){
      return iValueSetService.getValueSetOption(valueSetOptionId);
   }


   @Operation(summary = "根据父节点id获取子节点树")
   @PostMapping(value="/getChildValueSetOption")
   public ValueSetTreeDTO getChildValueSetOption(@RequestParam String parentOptionId){
      return iValueSetService.getChildValueSetOption(parentOptionId);
   }


   @Operation(summary = "根据code获取值集子节点树")
   @PostMapping(value="/getValueSetByReferenceCode")
   public ValueSetTreeDTO getValueSetByReferenceCode(@RequestParam String referenceCode){
      return iValueSetService.getValueSetByReferenceCode(referenceCode);
   }

   @Operation(summary = "根据code获取值集子节点树,并根据关键字过滤")
   @PostMapping(value="/getValueSetByReferenceCodeAndKeyWord")
   public List<ValueSetOptionDTO> getValueSetByReferenceCodeAndKeyWord(@RequestParam String referenceCode,@RequestParam String keyWord){
      return iValueSetService.getValueSetByReferenceCodeAndKeyWord(referenceCode,keyWord);
   }


   @Operation(summary = "根据值集id查询值集项")
   @PostMapping(value="/listValueSetOption")
   public List<ValueSetOptionDTO> listValueSetOption(@RequestParam String valueSetId){
      return iValueSetService.listValueSetOption(valueSetId);
   }


   @Operation(summary = "修改值集项")
   @PostMapping(value="/updateValueSetOptions")
   public void updateValueSetOptions(@RequestBody List<ValueSetOptionDTO> valueSetOptions,@RequestParam String operator){
      iValueSetService.updateValueSetOptions(valueSetOptions,operator);

   }

   @Operation(summary = "通过角色类型查询平台")
   @PostMapping(value="/findPlatformByRoleType")
   public List<String[]> findPlatformByRoleType(@RequestBody List<String> roleTypeList){
      return iValueSetService.findPlatformByRoleType(roleTypeList);
   }

    @Operation(summary = "通过角色查询平台")
    @PostMapping(value="/findPlatformByRole")
    public List<String[]> findPlatformByRole(@RequestBody List<Integer> roleIdList){
        return iValueSetService.findPlatformByRole(roleIdList);
    }

   @Operation(summary = "重新加载redis缓存")
   @PostMapping(value="/reloadRedisCache")
   public void reloadRedisCache(){
      iValueSetService.reloadRedisCache();
   }


}
