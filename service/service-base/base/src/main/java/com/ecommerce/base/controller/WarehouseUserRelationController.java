package com.ecommerce.base.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.base.api.dto.warehouse.WarehouseUserRelationDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ecommerce.base.service.IWarehouseUserRelationService;
import java.util.List;
import com.ecommerce.base.api.dto.warehouse.WarehouseUserRelationAddDTO;
import com.ecommerce.base.api.dto.warehouse.WarehouseUserRelationUpdateDTO;
import java.lang.String;


/**
 * @Created锛�Thu Aug 22 09:33:26 CST 2019
 * <AUTHOR>
 * @Version:2
 * @Description:中心仓--卖家映射
*/

@RestController
@Tag(name = "WarehouseUserRelation", description = "中心仓--卖家映射")
@RequestMapping("/warehouseUserRelation")
public class WarehouseUserRelationController {

   @Autowired 
   private IWarehouseUserRelationService iWarehouseUserRelationService;

   @Operation(summary = "删除中心仓-卖家关系映射")
   @PostMapping(value="/deleteWarehouseUserRelation")
   public Boolean deleteWarehouseUserRelation(@RequestParam String userId,@RequestParam String warehouseId){
      return iWarehouseUserRelationService.deleteWarehouseUserRelation(userId,warehouseId);
   }


   @Operation(summary = "根据用户ID,查询使用的中心仓/平台门店")
   @PostMapping(value="/queryWarehouseIdFromRelation")
   public List<WarehouseUserRelationDTO> queryWarehouseIdFromRelation(@RequestParam String sellerID){
      return iWarehouseUserRelationService.queryWarehouseUserRelationList(sellerID);
   }


   @Operation(summary = "创建中心仓-卖家映射")
   @PostMapping(value="/createWarehouseUserRelation")
   public Boolean createWarehouseUserRelation(@RequestBody List<WarehouseUserRelationAddDTO> addDTOs){
      return iWarehouseUserRelationService.createWarehouseUserRelation(addDTOs);
   }


   @Operation(summary = "修改中心仓-卖家映射")
   @PostMapping(value="/updateWarehouseUserRelation")
   public Boolean updateWarehouseUserRelation(@RequestBody WarehouseUserRelationUpdateDTO updateDTO){
      return iWarehouseUserRelationService.updateWarehouseUserRelation(updateDTO);
   }



}
