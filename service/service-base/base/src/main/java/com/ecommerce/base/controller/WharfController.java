package com.ecommerce.base.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.base.api.dto.wharf.*;
import com.ecommerce.base.service.IWharfErpInfoService;
import com.ecommerce.base.service.IWharfMapService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ecommerce.base.service.IWharfService;
import com.ecommerce.common.result.ItemResult;
import java.lang.Byte;
import java.util.List;
import java.lang.String;
import com.ecommerce.common.result.PageData;

import java.lang.Void;
import java.util.Map;

import com.ecommerce.base.api.dto.warehouse.PageQuery;


/**
 * : 码头服务
 *
 * <AUTHOR>
*/

@RestController
@Tag(name = "Wharf", description = ": 码头服务")
@RequestMapping("/wharf")
public class WharfController {

   @Autowired 
   private IWharfService iWharfService;

   @Autowired
   private IWharfMapService wharfMapService;

   @Autowired
   private IWharfErpInfoService wharfErpInfoService;

   @Operation(summary = "修改码头状态")
   @PostMapping(value="/changeWharfStatus")
   public ItemResult<Void> changeWharfStatus(@RequestBody List<String> wharfIds,@RequestParam Byte status,@RequestParam String operatorId){
      return iWharfService.changeWharfStatus(wharfIds,status,operatorId);
   }


   @Operation(summary = "通过Id查询码头")
   @PostMapping(value="/getWharfById")
   public ItemResult<WharfInfoDTO> getWharfById(@RequestParam String id){
      return iWharfService.getWharfById(id);
   }


   @Operation(summary = "删除码头")
   @PostMapping(value="/deleteWharf")
   public ItemResult<Void> deleteWharf(@RequestBody List<String> wharfIds,@RequestParam String operatorId){
      return iWharfService.deleteWharf(wharfIds,operatorId);
   }


   @Operation(summary = "编辑码头")
   @PostMapping(value="/updateWharf")
   public ItemResult<Void> updateWharf(@RequestBody WharfUpdateDTO wharfUpdateDTO){
      return iWharfService.updateWharf(wharfUpdateDTO);
   }


   @Operation(summary = "分页查询码头列表")
   @PostMapping(value="/getWharfList")
   public ItemResult<PageData<WharfListDTO>> getWharfList(@RequestBody PageQuery<WharfQueryDTO> pageQuery){
      return iWharfService.getWharfList(pageQuery);
   }


   @Operation(summary = "查询所有码头")
   @PostMapping(value="/getAllWharf")
   public ItemResult<List<WharfInfoDTO>> getAllWharf(){
      return iWharfService.getAllWharf();
   }

   @Operation(summary = "新增码头")
   @PostMapping(value="/saveWharf")
   public ItemResult<String> saveWharf(@RequestBody WharfAddDTO wharfAddDTO){
      return iWharfService.saveWharf(wharfAddDTO);
   }

   @Operation(summary = "批量新增码头")
   @PostMapping(value="/batchSaveWharf")
   public ItemResult<Void> batchSaveWharf(@RequestBody List<WharfAddDTO> wharfAddDTOS){
      return iWharfService.batchSaveWharf(wharfAddDTOS);
   }



   @Operation(summary = "查询所有码头并通过省市区分组")
   @PostMapping(value="/getAllWharfByGroup")
   public ItemResult<List<TreeProvinceDTO>> getAllWharfByGroup(){
      return iWharfService.getAllWharfByGroup();
   }

   @Operation(summary = "根据码头ID集合查询码头列表")
   @PostMapping(value="/getWharfInfoList")
   public List<WharfProvinceCityDTO> getWharfInfoList(@RequestBody List<String> idList){
      return iWharfService.getWharfInfoList(idList);
   }

   @Operation(summary = "查询erp码头信息(考虑有效期)")
   @PostMapping(value="/findErpInfoList")
   public ItemResult<List<WharfErpInfoListDTO>> findErpInfoList(@RequestBody WharfErpInfoQueryDTO queryDTO){
      return wharfErpInfoService.findErpInfoList(queryDTO);
   }

   @Operation(summary = "同步码头erp信息")
   @PostMapping(value="/syncErpInfo")
   public ItemResult<Boolean> syncErpInfo(@Parameter(name = "memberId", description = "会员id")@RequestParam String memberId,@Parameter(name = "operatorId", description = "操作人账号Id") @RequestParam String operatorId){
      return wharfErpInfoService.syncErpInfo(memberId,operatorId);
   }

   @Operation(summary = "保存码头映射关系")
   @PostMapping(value="/saveWharfMap")
   public ItemResult<String> saveWharfMap(@RequestBody WharfMapAddDTO wharfMapAddDTO){
      return wharfMapService.saveWharfMap(wharfMapAddDTO);
   }

   @Operation(summary = "查询码头映射关系列表")
   @PostMapping(value="/queryWharfMapList")
   public ItemResult<List<WharfMapListDTO>> queryWharfMapList(@RequestBody WharfMaPQueryDTO wharfMaPQueryDTO){
      return wharfMapService.queryWharfMapList(wharfMaPQueryDTO);
   }

   @Operation(summary = "根据会员Id和类型查询其所有电商码头")
   @PostMapping(value="/queryEcWharfName")
   public ItemResult<Map<String,String>> queryEcWharfName(@RequestBody WharfMaPQueryDTO wharfMaPQueryDTO){
      return wharfMapService.queryEcWharfName(wharfMaPQueryDTO);
   }

   @Operation(summary = "根据会员Id和类型查询使用中的电商码头")
   @PostMapping(value = "/queryUsingEcWharfName")
   public ItemResult<Map<String, String>> queryUsingEcWharfName(@RequestBody WharfMaPQueryDTO wharfMaPQueryDTO) {
      return wharfMapService.queryUsingEcWharfName(wharfMaPQueryDTO);
   }

   @Operation(summary = "根据会员Id和类型查询所有电商ERP码头集合")
   @PostMapping(value="/queryEcWharf")
   public ItemResult<List<WharfMapListDTO>> queryEcWharf(@RequestBody WharfErpQueryDTO wharfErpQueryDTO){
      return wharfMapService.queryEcWharf(wharfErpQueryDTO);
   }

   @Operation(summary = "根据收货地址ID集合查询码头映射关系列表")
   @GetMapping(value="/queryWharfMapListByAddressId")
   public List<WharfMapListDTO> queryWharfMapListByAddressId(@Parameter(name = "addressId", description = "收货地址ID") @RequestParam String addressId) {
      return wharfMapService.queryWharfMapListByAddressId(addressId);
   }
   
   @Operation(summary = "根据码头名模糊查询码头信息")
   @PostMapping(value="/getWharfIdAndName")
   public ItemResult<List<WharfInfoDTO>> getWharfIdAndName(@RequestBody WharfInfoDTO infoDTO){
	 return iWharfService.getWharfIdAndName(infoDTO);
   }
   
   
}
