package com.ecommerce.base.dao.mapper;

import com.ecommerce.base.dao.vo.AccountSaleRegion;
import com.ecommerce.base.dao.vo.AccountSaleRegionWithParent;
import com.ecommerce.common.service.IBaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface AccountSaleRegionMapper extends IBaseMapper<AccountSaleRegion> {

    @Select("""
            SELECT \
              asr.*,\
              sr.`sale_region_parent_id` \
            FROM
              `ba_account_sale_region` asr \
              LEFT JOIN `ba_sale_region` sr \
                ON asr.`sale_region_id` = sr.`sale_region_id` \
            WHERE asr.`del_flg` = 0 \
              AND asr.`account_id` = #{accountId} """)
    List<AccountSaleRegionWithParent> queryWithParentId(@Param("accountId") String accountId);


    @Update("update ba_account_sale_region set del_flg=1,update_time=NOW(),update_user=#{operator} where sale_region_id=#{saleRegionId}")
    Integer deleteAccountSaleRegion(@Param("saleRegionId") String saleRegionId, @Param("operator") String operator);
}