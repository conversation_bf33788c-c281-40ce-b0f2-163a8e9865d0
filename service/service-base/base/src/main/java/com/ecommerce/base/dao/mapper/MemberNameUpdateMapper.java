package com.ecommerce.base.dao.mapper;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

public interface MemberNameUpdateMapper {

    @Update("update ${tableName} set member_name='${memberName}' where member_id='${memberId}' ")
    int updateMemberName(@Param("tableName") String tableName,@Param("memberId") String memberId, @Param("memberName") String memberName);

    @Update("update ${tableName} set ${prefix}_name='${memberName}' where ${prefix}_id='${memberId}' ")
    int updateMemberName2(@Param("tableName") String tableName,@Param("prefix") String prefix,@Param("memberId") String memberId, @Param("memberName") String memberName);
}