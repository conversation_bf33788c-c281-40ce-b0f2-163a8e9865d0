package com.ecommerce.base.dao.mapper;

import com.ecommerce.base.api.dto.ReceivingAddressMapDTO;
import com.ecommerce.base.api.dto.ReceivingAddressMapQueryDTO;
import com.ecommerce.base.dao.vo.ReceivingAddressMap;
import com.ecommerce.common.service.IBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface ReceivingAddressMapMapper extends IBaseMapper<ReceivingAddressMap>
{
    public int updateReceivingAddressInfoById(ReceivingAddressMap receivingAddressMap);
    public int deleteReceivingAddressMapInfoByAddress(@Param("addressId") String addressId);
    public ReceivingAddressMap getReceivingAddressInfoById(@Param("id") String id);
    public ReceivingAddressMap getReceivingAddressMapInfoByAddressIdAndSellerIdAndFirmId(@Param("addressId") String addressId, @Param("sellerId") String sellerId, @Param("firmId") String firmId);
    public List<ReceivingAddressMap> getReceivingAddressMapListByQuery(ReceivingAddressMapQueryDTO dto);

    /**
     * 查询ERP码头管理列表
     * @param dto
     * @return
     */
    List<ReceivingAddressMap> queryErpWharfListByQuery(ReceivingAddressMapQueryDTO dto);
    
    /**
     * 根据卖家模糊查询买家地址
     * @param dto
     * @return
     */
    public List<ReceivingAddressMapDTO> queryAddressBySellerId(ReceivingAddressMapQueryDTO dto);

    public List<ReceivingAddressMap> getReceivingAddressMapListByAddressIds(@Param("addressIds") Collection<String> addressIds);
}
