package com.ecommerce.base.dao.mapper;

import com.ecommerce.base.api.dto.ReceivingAddressDTO;
import com.ecommerce.base.api.dto.ReceivingAddressQueryDTO;
import com.ecommerce.base.api.dto.ReceivingAddressSimpleDTO;
import com.ecommerce.base.api.dto.wharf.WharfErpManagePageListDTO;
import com.ecommerce.base.dao.vo.ReceivingAddress;
import com.ecommerce.common.service.IBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;

public interface ReceivingAddressMapper extends IBaseMapper<ReceivingAddress> {

    ReceivingAddress selectLastOneByMemberId(@Param("memberId") String memberId,@Param("type") Integer type);

    List<ReceivingAddressSimpleDTO> findSimpleByIds(@Param("ids") Collection<String> ids);

    Integer getCountByMemberId(@Param("memberId") String memberId);

    List<ReceivingAddress> findReceivingAddressByKeyWords(ReceivingAddressQueryDTO queryDTO);

    ReceivingAddress getReceivingAddressInfoById(@Param("id") String id);

    List<WharfErpManagePageListDTO> queryWharfErpPageList(HashMap<String, Object> param);

    /**
     * 通过会员码头获取收货地址
     * @param memberId
     * @param coordinate
     * @param wharfId
     * @return
     */
    ReceivingAddressDTO getReceivingAddressByWharfId(@Param("memberId") String memberId,
                                                     @Param("coordinate") String coordinate,
                                                     @Param("wharfId") String wharfId);


}
