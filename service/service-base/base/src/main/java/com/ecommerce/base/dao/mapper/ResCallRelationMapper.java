package com.ecommerce.base.dao.mapper;

import com.ecommerce.base.api.dto.authRes.ResCallRelationDTO;
import com.ecommerce.base.dao.vo.ResCallRelation;
import com.ecommerce.common.service.IBaseMapper;

import java.util.List;

public interface ResCallRelationMapper extends IBaseMapper<ResCallRelation> {


    List<ResCallRelationDTO> findCallRelationByCondition(ResCallRelationDTO dto);

    Integer countCallRelationByCondition(ResCallRelationDTO dto);
}