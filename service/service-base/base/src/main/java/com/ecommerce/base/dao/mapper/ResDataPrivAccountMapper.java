package com.ecommerce.base.dao.mapper;

import com.ecommerce.base.dao.vo.ResDataPrivAccount;
import com.ecommerce.common.service.IBaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Collection;
import java.util.List;

public interface ResDataPrivAccountMapper extends IBaseMapper<ResDataPrivAccount> {

    @Select("SELECT " +
            "  a.*," +
            "  b.`sale_region_parent_id`  as targetParentCode FROM " +
            "  `ba_res_data_priv_account` a " +
            "  LEFT JOIN `ba_sale_region` b " +
            "    ON a.`target_code` = b.`sale_region_id` " +
            "WHERE a.`data_priv_code` = 'sale_region' and a.`del_flg` = 0 " +
            "  AND a.`account_id` = #{accountId} ")
    List<ResDataPrivAccount> querySaleRegionWithParentId(@Param("accountId") String accountId);

    @Select("SELECT " +
            "  a.*," +
            "  b.`parent_adcode` as targetParentCode FROM " +
            "  `ba_res_data_priv_account` a " +
            "  LEFT JOIN `ba_region` b " +
            "    ON a.`target_code` = b.`adcode` " +
            "WHERE a.`data_priv_code` = 'region' and a.`account_id` = #{accountId} " +
            "  AND a.`del_flg` = 0 ")
    List<ResDataPrivAccount> queryRegionWithParentId(@Param("accountId") String accountId);

    @Select("<script>"
            +"select distinct account_id from ba_res_data_priv_account where target_code in"+
            "<foreach item='item' index='index' collection='targetCodeList' open='(' separator=',' close=')'>"
            +"#{item}"
            +"</foreach>"
            +"</script>")
    List<String> findAccountIdByTargetCodes(@Param("targetCodeList") Collection<String> targetCodeList);





    @Update("update ba_res_data_priv_account set del_flg=1,update_time=NOW(),update_user=#{operator} where target_code=#{targetCode}")
    Integer deleteByTargetCode(@Param("targetCode") String targetCode, @Param("operator") String operator);

    @Update("update ba_res_data_priv_account set del_flg=1,update_time=NOW(),update_user=#{operator} where data_priv_code=#{dataPrivCode} and target_code=#{targetCode}")
    Integer deleteByDataPrivCodeAndTargetCode(@Param("dataPrivCode") String dataPrivCode, @Param("targetCode") String targetCode, @Param("operator") String operator);


}