package com.ecommerce.base.dao.mapper;

import com.ecommerce.base.dao.vo.SaleRegionErpInfo;
import com.ecommerce.common.service.IBaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface SaleRegionErpInfoMapper extends IBaseMapper<SaleRegionErpInfo> {

    @Select("select * from ba_sale_region_erp_info where del_flg = 0 and member_id = '${memberId}' and (org_code like concat('%','${keyword}','%') or org_name like concat('%','${keyword}','%')) order by org_name asc limit 20")
    List<SaleRegionErpInfo> findErpInfoByKeyword(@Param("memberId") String memberId,@Param("keyword") String keyword);

    @Select("select * from ba_sale_region_erp_info where del_flg = 0 and org_code = '${orgCode}' and member_id = '${memberId}' ")
    SaleRegionErpInfo findErpInfoByOrgCode(@Param("memberId") String memberId,@Param("orgCode") String orgCode);

}