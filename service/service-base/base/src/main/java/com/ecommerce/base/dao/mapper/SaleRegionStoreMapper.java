package com.ecommerce.base.dao.mapper;

import com.ecommerce.base.dao.vo.SaleRegionStore;
import com.ecommerce.base.dao.vo.SaleRegionStoreWithLevel;
import com.ecommerce.common.service.IBaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface SaleRegionStoreMapper extends IBaseMapper<SaleRegionStore> {

    @Select("select srs.*,sr.level,sr.create_time as saleRegionCreateTime,sr.sale_region_name as saleRegionName from ba_sale_region_store srs" +
            " left join ba_sale_region sr on srs.sale_region_id=sr.sale_region_id" +
            " left join ba_warehouse wh on srs.store_id=wh.warehouse_id" +
            " where srs.del_flg = 0 and srs.member_id = '${memberId}' " +
            " order by sr.create_time asc,wh.create_time asc")
    List<SaleRegionStoreWithLevel> findStoreBySellerId(@Param("memberId") String memberId);
}