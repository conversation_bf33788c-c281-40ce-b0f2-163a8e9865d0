package com.ecommerce.base.dao.mapper;

import com.ecommerce.base.api.dto.warehouse.WarehouseAllocationDTO;
import com.ecommerce.base.dao.vo.WarehouseAllocation;
import com.ecommerce.common.service.IBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface WarehouseAllocationMapper extends IBaseMapper<WarehouseAllocation>
{
    public List<WarehouseAllocation> getWarehouseAllocationListByWarehouseId(@Param("warehouseId") String warehouseId);

    public int deleteWarehouseAllocationListByWarehouseId(@Param("warehouseId") String warehouseId);

	/**
	 * 查询仓库库位
	 * @param dto
	 * @return
	 */
	public List<WarehouseAllocationDTO> queryWarehouseAllocationListByWarehouseId(WarehouseAllocationDTO dto);
}
