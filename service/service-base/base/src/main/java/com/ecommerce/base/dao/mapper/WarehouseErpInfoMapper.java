package com.ecommerce.base.dao.mapper;

import com.ecommerce.base.dao.vo.WarehouseErpInfo;
import com.ecommerce.common.service.IBaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface WarehouseErpInfoMapper extends IBaseMapper<WarehouseErpInfo> {

    @Select("select * from ba_warehouse_erp_info " +
            "where del_flg = 0 and member_id ='${memberId}' " +
            "and ( pickup_point_name like concat('%','${keyword}','%') " +
            "or pickup_point_code like concat('%','${keyword}','%') " +
            "or pickup_point_org_code like concat('%','${keyword}','%') " +
            "or address like concat('%','${keyword}','%') ) order by pickup_point_name asc limit 500")
    List<WarehouseErpInfo> findErpInfoByKeyword(@Param("memberId") String memberId,@Param("keyword") String keyword);

    @Select("select * from ba_warehouse_erp_info where del_flg = 0 and member_id ='${memberId}' and pickup_point_code ='${pickupPointCode}' and pickup_point_org_code ='${pickupPointOrgCode}' ")
    WarehouseErpInfo findErpInfoBypickupPointCodeAndPickupPoint(@Param("memberId") String memberId,@Param("pickupPointCode") String pickupPointCode,@Param("pickupPointOrgCode") String pickupPointOrgCode);
}