spring:
  config:
    import: 'optional:configserver:'
  cloud:
    config:
      uri: http://127.0.0.1:8888
      profile: dev
      label: asset-2025
      name: base,db,redis,rabbitmq,hystrix,vip-db,vip-server,xxl-job,eureka
  main:
    #该项配置 似乎配置中心配置无效
    allow-bean-definition-overriding: true
    allow-circular-references: true
  datasource:
    url: 
mybatis:
  typeAliasesPackage: com.ecommerce.base.dao
  mapperScanPackage: com.ecommerce.base.dao
  mapperLocations: "classpath:/mapper/*.xml"
  configLocation: "classpath:/mybatis-config.xml"
logging:
  pattern:
    # Logging pattern containing traceId and spanId; no longer provided through Sleuth by default
    level: "%5p [${spring.application.name:},%X{traceId:-},%X{spanId:-}]"
    
mapper:
  resolve-class: com.ecommerce.common.tk.MapperEntityResolve