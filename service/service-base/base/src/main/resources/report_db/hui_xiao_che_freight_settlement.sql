-- 宏驰 jufg03mttk0fk7c34323ih8e
-- 邓师傅 vmjmmp6upn7ij1erd1jc57b8
-- 灏晟 19vnfq7ab2qj0tcxinckftfjg
-- 润恒 w7c68cfnk9qad769c9gnf0fn
-- 宏骏 1ae2t34c08gvup6ku0kuh2w5k
-- 华鑫盛 2v26qaa0puh6qcr7x0f5bx5e6
-- 每月只需调整create_time
SELECT DISTINCT
	a.waybill_num AS 运单号,
	(
		CASE a.STATUS
		WHEN '030070100' then '待整合'
		WHEN '030070200' then '待审核'
		WHEN '030070300' then '待发布'
		WHEN '030070400' then '待接单'
		WHEN '030070500' then '待配送'
		WHEN '030070600' then '配送中'
		WHEN '030070700' then '已完成'
		WHEN '030070800' then '已取消'
		WHEN '030070900' then '已关闭'
		WHEN '030071100' then '待指派'
		WHEN '030071200' then '待确认'
		end
	) as 运单状态,
	b.vehicle_num AS 配送车辆,
	e. NAME AS 出货点名称,
	d.delivery_quantity AS 配送数量,
	b.published_carriage AS 预估运费,
	date_format(b.receive_time, '%Y-%m-%d %H:%i:%s') AS 抢单时间,
	a.actual_quantity AS 实际出厂量,
	b.actual_carriage AS 实际运费,
	date_format(b.leave_warehouse_time, '%Y-%m-%d %H:%i:%s') AS 出站时间,
	date_format(a.complete_time, '%Y-%m-%d %H:%i:%s') AS 运单完成时间,
	c.member_name AS 运力名称,
	concat(
		f.province,
		f.city,
		f.district,
		f.street,
		f.address
	) AS 收货地址
FROM
	lgs_waybill a,
	lgs_waybill_info b,
	mb_account c,
	lgs_product_quantity_map d,
	ba_warehouse e,
	lgs_picking_bill f
WHERE
	d.map_id = a.waybill_id
AND a.picking_bill_id = f.picking_bill_id
AND f.warehouse_id = e.warehouse_id
AND a.waybill_id = b.waybill_id
AND a.carrier_id = c.member_id
AND a.create_time > '2019-07-31'
AND a.create_time < '2019-08-31'
ORDER BY
	a.waybill_num ASC