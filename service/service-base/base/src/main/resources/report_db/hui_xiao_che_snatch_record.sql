SELECT DISTINCT
	date_format(o.create_time, '%Y-%m-%d %H:%i:%s') AS '创建时间',
	date_format(o.picking_time, '%Y-%m-%d %H:%i:%s') AS '提货时间',
	CASE mb.member_type
		WHEN 203 THEN mb.member_name
		ELSE act.real_name END AS '抢单人',
	CASE mb.member_type
		WHEN 203 THEN '承运商'
		ELSE '个体车主' END AS '性质',
	IF(o.status = '030070800' or o.status = '030070900', '是', '否')  AS '是否取消/关闭',
	if(sel.status=1, '否', if(sel.status=2, '是', '运单未完成')) AS '是否结算',
	w.address AS '提货点',
	p.address AS '目的地',
	o.waybill_num AS '运单号',
	o.actual_quantity AS '出厂量（系统值）',
	t.actual_carriage AS '配送费用',
	t.vehicle_num AS '配送车辆',
	t.driver_name AS '配送司机',
	date_format(t.arrive_warehouse_time, '%Y-%m-%d %H:%i:%s') AS '到场时间',
  	date_format(t.leave_warehouse_time, '%Y-%m-%d %H:%i:%s') AS '出场时间'


FROM
	lgs_waybill o
LEFT JOIN lgs_waybill_info t ON o.waybill_id = t.waybill_id
LEFT JOIN lgs_picking_bill p ON o.picking_bill_id = p.picking_bill_id
LEFT JOIN ba_warehouse w ON w.warehouse_id = p.warehouse_id
LEFT JOIN mb_member mb ON mb.member_id = o.carrier_id
LEFT JOIN mb_account act ON mb.member_id = act.member_id
LEFT JOIN lgs_waybill_settlement  sel ON sel.waybill_id = o.waybill_id
WHERE
o.create_time BETWEEN $P{start_time}
AND $P{end_time}
#AND length(o.carrier_id) > 0
AND w.address not like '%重庆%'
AND p.address not like '%重庆%'
#AND LENGTH(t.driver_name) > 0
AND o.type = '*********'#type='*********'为社会承运商抢单
ORDER BY o.create_time
