select total.*, item.snatch_name, item.snatch_count as item_snatch_count
from (
     select
         detail.create_date, -- 创建日期
         min(detail.snatch_name) min_snatch_name, -- 最小签收人
         max(detail.snatch_name) max_snatch_name, -- 最大签收人
         count(1) as waybill_count, -- 总放单数
         sum(detail.close_flag) as close_ocunt, -- 关闭订单数
         sum(detail.settlement_flag) as settlement_count, -- 已结算
         sum(detail.un_settlement_flag) as un_settlement_count, -- 未完成
         sum(detail.un_finish_flag) as un_finish_ocunt, -- 未结算
         sum(detail.snatch_count) as snatch_count
     from (
              SELECT DISTINCT waybill.create_time,  -- 运单创建时间
                  date_format(waybill.create_time, '%Y/%m/%d') as create_date,  -- 运单创建日期
                  waybill.picking_time,
                  if(member.member_type = 203, member.member_name, account.real_name)    AS snatch_name, -- 抢单人

                  IF(waybill.status = '*********' or waybill.status = '*********', 1, 0) AS close_flag,-- 是否关闭
                  if(settlement.status = 2, 1, 0)                                        as settlement_flag,    -- 是否已结算
                  if(settlement.status = 1, 1, 0)                                        as un_settlement_flag, -- 是否未结算
                  if(settlement.status is null or (settlement.status != 1 and settlement.status != 2),
                     1,
                     0)                                                                  as un_finish_flag,     -- 是否未完成
                  if( seller_name is null, 0, 1) as snatch_count,
                  settlement.status
              FROM lgs_waybill waybill
                   LEFT JOIN lgs_picking_bill picking ON waybill.picking_bill_id = picking.picking_bill_id
                   LEFT JOIN ba_warehouse warehouse ON warehouse.warehouse_id = picking.warehouse_id
                   LEFT JOIN mb_member member ON member.member_id = waybill.carrier_id
                   LEFT JOIN mb_account account ON member.member_id = account.member_id
                   LEFT JOIN lgs_waybill_settlement settlement ON settlement.waybill_id = waybill.waybill_id
              WHERE waybill.create_time BETWEEN '2019-01-01 00:00:00' AND NOW()
                AND waybill.type = '*********' -- type='*********'为社会承运商抢单
          ) as detail

     group by detail.create_date
) total
left join (
    select
        detail.create_date, -- 创建日期
        detail.snatch_name, -- 创建时间
        sum(detail.snatch_count) as snatch_count
    from (
             SELECT DISTINCT waybill.create_time,  -- 运单创建时间
                             date_format(waybill.create_time, '%Y/%m/%d') as create_date,  -- 运单创建日期
                             waybill.picking_time,
                             if(member.member_type = 203, member.member_name, account.real_name)    AS snatch_name, -- 抢单人
                             if( seller_name is null, 0, 1) as snatch_count,
                             settlement.status

             FROM lgs_waybill waybill
                      LEFT JOIN lgs_picking_bill picking ON waybill.picking_bill_id = picking.picking_bill_id
                      LEFT JOIN ba_warehouse warehouse ON warehouse.warehouse_id = picking.warehouse_id
                      LEFT JOIN mb_member member ON member.member_id = waybill.carrier_id
                      LEFT JOIN mb_account account ON member.member_id = account.member_id
                      LEFT JOIN lgs_waybill_settlement settlement ON settlement.waybill_id = waybill.waybill_id
             WHERE waybill.create_time BETWEEN '2019-01-01 00:00:00' AND NOW()
               AND waybill.type = '*********' -- type='*********'为社会承运商抢单
         ) as detail
    where detail.snatch_name is not null
    group by detail.create_date, detail.snatch_name
) item on total.create_date = item.create_date
order by total.create_date, item.snatch_name