select
    orderDetail.order_code,
    orderDetail.create_time,
    orderDetail.buyer_name,
    orderDetail.take_time,
    orderDetail.origin_resource_amount,
    orderDetail.origin_order_amount,
    orderDetail.coupon_order_amount,
    orderDetail.actual_logistic_amount,
    orderDetail.actual_order_amount,
    orderDetail.goods_name,
    orderDetail.item_quantity,
    orderDetail.actual_amount_price,
    orderDetail.consignee_name,
    orderDetail.mobile_phone,
    orderDetail.deliver_way,
    orderDetail.province_name,
    orderDetail.city_name,
    orderDetail.district_name,
    orderDetail.street_name,
    orderDetail.address_detail,
    orderDetail.province,
    orderDetail.city,
    orderDetail.district,
    orderDetail.street,
    orderDetail.address,
    orderDetail.shop_code,
    orderDetail.order_status,
    CASE referrer.referrer_type
        WHEN 1 THEN
            'A类门店推广'
        WHEN 2 THEN
            'B类门店推广'
        ELSE
            '客户自发下单'
        END AS referrer_type, -- '下单途径',
    referrer.referrer_name, -- '推广门店',
    if(pickingInfo.order_code is null, '', orderDetail.warehouse_name) as shop_name, -- '配送门店',
    orderSum.order_quantity,
    orderDetail.order_item_id,
    if(orderSum.order_item_id = orderDetail.order_item_id, '0', '1') AS merge_flag
from (
         select
             orderItem.order_item_id, -- 订单项ID
             orderInfo.order_id, -- 订单ID
             orderInfo.order_code, -- 订单号
             orderInfo.create_time, -- 下单时间
             orderInfo.buyer_name, -- 买家名称
             date_format(orderInfo.take_time, '%Y/%m/%d') as take_time, -- 期望配送时间
             ifnull(orderInfo.origin_resource_amount, 0) as origin_resource_amount, -- 商品总额
             ifnull(orderInfo.origin_order_amount, 0) as origin_order_amount, -- 订单金额
             ifnull(orderInfo.origin_order_amount, 0) - ifnull(orderInfo.actual_order_amount, 0) as coupon_order_amount, -- 优惠金额
             ifnull(orderInfo.actual_logistic_amount, 0) as actual_logistic_amount, -- 运费
             ifnull(orderInfo.actual_order_amount, 0) as actual_order_amount, -- 实际支付金额
             orderItem.goods_name, -- 商品名称
             orderItem.item_quantity, -- 商品数量
             orderItem.actual_amount_price, -- 商品单价
             address.consignee_name, -- 收货人姓名
             address.mobile_phone, -- 收货人联系电话
             case orderInfo.deliver_way WHEN 030060100 THEN '自提' ELSE '配送' END AS deliver_way, -- 配送方式

             orderInfo.province_name, -- '省（收货地址）',
             orderInfo.city_name, -- '市（收货地址）',
             orderInfo.district_name, -- '区（收货地址）',
             orderInfo.street_name, -- '乡镇（收货地址）',
             orderInfo.address_detail, -- '详细地址（收货地址）',

             warehouse.province, -- '省（发货地址）',
             warehouse.city, -- '市（发货地址）',
             warehouse.district, -- '区（发货地址）',
             '' AS  street, -- '乡镇（发货地址）',
             warehouse.address, -- '详细地址（发货地址）',
             warehouse.name as warehouse_name, -- 仓库地址

             orderInfo.shop_code, -- 门店编码

             CASE orderInfo.order_status
                 WHEN 'in_payment' THEN
                     '支付中'
                 WHEN 'cancel' THEN
                     '已取消'
                 WHEN 'in_delivery' THEN
                     '配送中'
                 WHEN 'wait_delivered' THEN
                     '待配送'
                 WHEN 'wait_payment' THEN
                     '待支付'
                 WHEN 'completed' THEN
                     '已完成'
                 WHEN 'closed' THEN
                     '已关闭'
                 WHEN 'confirming' THEN
                     '待确认'
                 ELSE
                     '未知状态'
                 END AS order_status -- '订单状态'
         from
             tr_order_info orderInfo,
             tr_order_item orderItem,
             tr_order_payinfo payInfo,
             ba_receiving_address address,
             ba_warehouse warehouse
         where orderInfo.order_id = orderItem.order_id
           and orderInfo.order_id = payInfo.object_id
           and orderInfo.address_id = address.id
           and orderItem.store_id = warehouse.warehouse_id
           and orderInfo.del_flg = 0 and orderItem.del_flg = 0 and payInfo.del_flg = 0

           and orderInfo.create_time between '2019-01-01 00:00:00' and now()
           and orderItem.goods_name like '%%'
           and payInfo.payinfo_way in ('2', 'online', 'weixin')
           AND payInfo.payinfo_type = 'single'
     )
         as orderDetail
         left join (
    select orderInfo.order_id,
           min(orderItem.order_item_id) as order_item_id,
           sum(orderItem.item_quantity) as order_quantity
    from
        tr_order_info orderInfo,
        tr_order_item orderItem,
        tr_order_payinfo payInfo
    where orderInfo.order_id = orderItem.order_id
      and orderInfo.order_id = payInfo.object_id
      and orderInfo.del_flg = 0 and orderItem.del_flg = 0 and payInfo.del_flg = 0
      and orderInfo.create_time between '2019-01-01 00:00:00' and now()
      and orderItem.goods_name like '%%'
      and payInfo.payinfo_way in ('2', 'online', 'weixin')
      and payInfo.payinfo_type = 'single'
    group by orderInfo.order_id
) orderSum on orderDetail.order_id = orderSum.order_id
         left join mb_referrer_info referrer on referrer.referrer_code = orderDetail.shop_code
         left join (
    select distinct takeInfo.order_code
    from  od_take_info takeInfo, lgs_picking_bill picking
    where takeInfo.take_code = picking.delivery_sheet_num and takeInfo.del_flg = 0 and picking.del_flg = 0
      and  picking.type = '030060500'
) pickingInfo on orderDetail.order_code = pickingInfo.order_code


order by orderDetail.order_code asc