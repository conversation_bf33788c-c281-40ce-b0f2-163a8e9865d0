package com.ecommerce.common.config.feign;

import com.alibaba.fastjson.JSON;
import com.ecommerce.common.enums.AppNames;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.exception.ForcedLogoutException;
import com.ecommerce.common.exception.NoLoginException;
import com.ecommerce.common.exception.NoPermissionException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.utils.CsStringUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import feign.FeignException;
import feign.RetryableException;
import io.github.resilience4j.circuitbreaker.CallNotPermittedException;
import jakarta.annotation.PostConstruct;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.http.HttpStatus;
import org.springframework.validation.Errors;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.support.WebExchangeBindException;

import java.io.IOException;
import java.util.Optional;


@ControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

    @Autowired(required = false)
    private ObjectMapper objectMapper = new ObjectMapper();

    @Value("${spring.application.name}")
    private String applicationName;

    @PostConstruct
    public void init() {
        log.info("GlobalExceptionHandler init success.");
    }

    @ExceptionHandler(value = RetryableException.class)
    public ItemResult<Object> feignRetryableException(RetryableException exception, HttpServletRequest req) {
        errorLog("BizException", req, exception);
        String service = Optional.ofNullable(exception).map(Throwable::getCause).map(Throwable::getMessage).orElse("");
        return new ItemResult<>(BasicCode.NOT_FOUND.getCode(), service + "暂时不支持");
    }

    @ExceptionHandler(value = BizException.class)
    @ResponseBody
    public ItemResult<String> bizExceptionHandle(BizException exception, HttpServletRequest req) {
        errorLog("BizException", req, exception);
        ItemResult<String> result = new ItemResult<>();
        result.setSuccess(false);
        result.setCode(exception.getErrorCode().getCode());
        result.setDescription(getMessage(exception.getMessage()));
        log.info("BizExceptionHandle : {}", JSON.toJSONString(result));
        return result;
    }

    @ExceptionHandler(value = BizHystrixBadRequestException.class)
    @ResponseBody
    public ItemResult<String> bizHystrixBadRequestExceptionHandler(BizHystrixBadRequestException exception, HttpServletRequest req) {
        errorLog("BizHystrixBadRequestException", req, exception);
        ItemResult<String> result = new ItemResult<>();
        result.setSuccess(false);
        result.setCode(exception.getCode());
        result.setDescription(getMessage(exception.getMessage()));
        log.info("BizHystrixBadRequestException : {}", JSON.toJSONString(result));
        return result;
    }

    @ExceptionHandler(value = FeignException.class)
    @ResponseBody
    public ItemResult<String> feignExceptionHandle(FeignException exception, HttpServletRequest req) {
        errorLog("FeignException", req, exception);
        ItemResult<String> result = new ItemResult<>();
        result.setSuccess(false);
        result.setDescription(getMessage(exception.contentUTF8()));
        log.info("FeignException : {}", JSON.toJSONString(result));
        return result;
    }


    @ExceptionHandler(value = CallNotPermittedException.class)
    @ResponseBody
    public ItemResult<String> hystrixRuntimeExceptionHandle(CallNotPermittedException exception, HttpServletRequest req) {
        log.error("---CallNotPermittedException Handler---Host {} invokes url {} Parameter {} ERROR: {}",
                req.getRemoteHost(), req.getRequestURL(), toJson(req.getParameterMap()), exception == null ? "" : exception.getMessage(), exception);
        ItemResult<String> result = new ItemResult<>();
        result.setSuccess(false);
        result.setDescription("系统异常，请联系管理员");
        return result;
    }

    @ExceptionHandler(value = Exception.class)
    @ResponseBody
    public ItemResult<String> exceptionHandle(Exception exception, HttpServletRequest req) {
        log.error("---Exception Handler---Host {} invokes url {} Parameter {} ERROR: {}",
                req.getRemoteHost(), req.getRequestURL(), toJson(req.getParameterMap()), exception == null ? "" : exception.getMessage(), exception);
        ItemResult<String> result = new ItemResult<>();
        result.setSuccess(false);
        result.setDescription("系统异常，请联系管理员");
        return result;
    }

    @ResponseStatus(value = HttpStatus.BAD_REQUEST)
    @ExceptionHandler(WebExchangeBindException.class)
    public ItemResult<String> webExchangeBindException(WebExchangeBindException exception, HttpServletRequest req) {
        errorLog("WebExchangeBindException", req, exception);
        ItemResult<String> result = new ItemResult<>();
        result.setSuccess(false);
        result.setDescription("数据绑定异常:[" + Optional.of(exception.getBindingResult()).map(Errors::getFieldError).map(DefaultMessageSourceResolvable::getDefaultMessage).orElse("") + "]");
        return result;
    }

    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    @ResponseBody
    public ItemResult<String> validationHandle(MethodArgumentNotValidException exception, HttpServletRequest req) {
        errorLog("MethodArgumentNotValidException", req, exception);
        ItemResult<String> result = new ItemResult<>();
        result.setSuccess(false);
        result.setDescription("参数验证失败:[" + Optional.of(exception.getBindingResult()).map(Errors::getFieldError).map(DefaultMessageSourceResolvable::getDefaultMessage).orElse("") + "]");
        return result;
    }

    @ExceptionHandler(value = NoLoginException.class)
    @ResponseBody
    public ItemResult<String> noLoginExceptionHandle(NoLoginException exception, HttpServletRequest req) {
        errorLog("NoLoginException", req, exception);
        ItemResult<String> result = new ItemResult<>();
        result.setSuccess(false);
        result.setCode("401");
        result.setDescription("登录信息失效，请重新登陆");
        return result;
    }

    @ExceptionHandler(value = NoPermissionException.class)
    @ResponseBody
    public ItemResult<String> noPermissionExceptionHandle(NoPermissionException exception, HttpServletRequest req) {
        errorLog("NoPermissionException", req, exception);
        ItemResult<String> result = new ItemResult<>();
        result.setSuccess(false);
        result.setCode("402");
        result.setDescription("用户无此操作权限");
        return result;
    }

    @ExceptionHandler(value = ForcedLogoutException.class)
    @ResponseBody
    public ItemResult<String> forcedLogoutExceptionHandle(ForcedLogoutException exception, HttpServletRequest req) {
        errorLog("ForcedLogoutException", req, exception);
        ItemResult<String> result = new ItemResult<>();
        result.setSuccess(false);
        result.setCode("403");
        result.setDescription("该账号已在其它地方登陆");
        return result;
    }

    private void errorLog(String type, HttpServletRequest req, Exception exception) {
        checkException();
        log.error("---{} Handler---Host {} invokes url {} Parameter {} ERROR", type, req.getRemoteHost(), req.getRequestURL(), toJson(req.getParameterMap()), exception);
    }

    private String getMessage(String message) {
        return CsStringUtils.isBlank(message) ? "操作失败" : message;
    }

    private String toJson(Object object) {
        try {
            return objectMapper.writeValueAsString(object);
        } catch (IOException ignored) {
        }
        return "";
    }

    private void checkException() {
        if (AppNames.isMicroService(applicationName)) {
            log.info("中心层服务异常");
        }
    }

}
