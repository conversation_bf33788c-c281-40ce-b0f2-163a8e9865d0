package com.ecommerce.common.config.redis;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;

/**
 * <AUTHOR>
 * @Description:
 * @Date: Create in 11:27 10/09/2018
 */
@Configuration
@EnableCaching
public class BizRedisConfig extends RedisConfig {
    @Value("${spring.biz-redis.database}")
    private int dbIndex;

    @Value("${spring.biz-redis.host}")
    private String host;

    @Value("${spring.biz-redis.port}")
    private int port;

    @Value("${spring.biz-redis.password}")
    private String password;

    @Value("${spring.biz-redis.timeout}")
    private int timeout;

    @Value("${spring.redis.lettuce.pool.max-active}")
    private int redisPoolMaxActive;

    @Value("${spring.redis.lettuce.pool.max-wait}")
    private int redisPoolMaxWait;

    @Value("${spring.redis.lettuce.pool.max-idle}")
    private int redisPoolMaxIdle;

    @Value("${spring.redis.lettuce.pool.min-idle}")
    private int redisPoolMinIdle;

    /**
     * 配置redis连接工厂
     * @return
     */
    @Bean(name = "bizRedisConnectionFactory")
    public RedisConnectionFactory bizRedisConnectionFactory() {
        return createJedisConnectionFactory(dbIndex, host, port, password, timeout);
    }

    /**
     * <AUTHOR>
     * @discription: fastjosn序列化redistemplate
     * @creat_date: 2018/1/10
     * @creat_time: 11:01
     **/
    @Bean(name = "bizRedisTemplate")
    public RedisTemplate<String, Object> redisTemplate() {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(bizRedisConnectionFactory());
        setSerializer(template);
        template.afterPropertiesSet();
        return template;
    }
}
