package com.ecommerce.common.service.access;

import com.ecommerce.common.service.IAuthorizeService;
import com.ecommerce.common.service.IRightAccessCacheService;
import lombok.extern.slf4j.Slf4j;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
@Slf4j
@Service
public class AuthorizeService implements IAuthorizeService {
    @Autowired
    private IRightAccessCacheService rightAccessCacheService;

    @Override
    public boolean authorize(String url, List<Integer> roleList) {
        if (CsStringUtils.isBlank(url)) {
            return false;
        }
        RightAccessCache rightAccessCache = rightAccessCacheService.getRightAccess(url);
        if(rightAccessCache == null){
            log.info("url is not exit in controller,return default false");
            return false;
        }
        for(Integer role:roleList){
            if(rightAccessCacheService.hasRightAccess(url,role,rightAccessCache.getRoles())){
                return true;
            }
        }
        return false;
    }
}
