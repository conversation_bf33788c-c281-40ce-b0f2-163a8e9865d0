package com.ecommerce.common.service.common.bsid;

/**
 * <AUTHOR>
 * @Description:
 * @Date: Create in 13:57 28/08/2018
 */
public interface IBusinessIdGenerator {
    /**
     * <AUTHOR>
     * @Description: 默认根据businessCodePrefix生成id
     * @Date: Create in 14:21 28/08/2018
     */
    String incrementCode();

    /**
     * <AUTHOR>
     * @Description: 前缀自定义
     * @Date: Create in 14:16 28/08/2018
     */
    String businessCodePrefix();

    /**
     * <AUTHOR>
     * @Description: 根据系统名称
     * @Date: Create in 14:16 28/08/2018
     */
    Long gain();

    /**
     * <AUTHOR>
     * @Description: 默认六位，不满六位补0
     * @Date: Create in 14:24 28/08/2018
     */
    String gainString();

    /**
     * 年月日字符串：yymmdd
     * @return
     */
    String gainDateString();
}
