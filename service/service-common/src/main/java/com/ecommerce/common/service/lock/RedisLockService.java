package com.ecommerce.common.service.lock;

import com.ecommerce.common.exception.DistributeLockException;
import com.ecommerce.common.service.ILockService;
import com.ecommerce.common.utils.CsStringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
@Slf4j
@Component
public class RedisLockService implements ILockService{
    @Value("${redis.lock.key.timeout:10000}")
    private long keyTimeout;
    @Value("${redis.lock.acquire.timeout:5000}")
    private long acquireTimeout;
    @Autowired
    private DistributedLock lock ;
    public RedisLockService(){
    }

    @Override
    public String lock(String key) {
        // 返回锁的value值，供释放锁时候进行判断
        String identifier = "";
        try{
            identifier = lock.lockWithTimeout(key, acquireTimeout, keyTimeout);
        }catch (Exception e){
            log.error("redis lock fail,key:"+key,e);
            throw new DistributeLockException("get redis lock fail,"+e.getMessage());
        }
        if (CsStringUtils.isBlank(identifier)) {
            throw new DistributeLockException("get redis lock fail!");
        }

        return identifier;
    }

    @Override
    public String lockWithTimeout(String key, Long acquireTimeout1, Long timeout1) {
        // 返回锁的value值，供释放锁时候进行判断
        String identifier = "";
        try{
            identifier = lock.lockWithTimeout(key,acquireTimeout1 != null ? acquireTimeout1 : acquireTimeout,timeout1 != null ? timeout1 : keyTimeout);
        }catch (Exception e){
            log.error("redis lock fail,key:"+key,e);
            throw new DistributeLockException("get redis lock fail,"+e.getMessage());
        }
        if (CsStringUtils.isBlank(identifier)) {
            throw new DistributeLockException("get redis lock fail!");
        }

        return identifier;
    }

    /**
     * 获取锁等待时间为0，一次没有成功则马上返回
     * @param key
     * @return
     */
    @Override
    public String lockFast(String key) {
        // 返回锁的value值，供释放锁时候进行判断
        String identifier = "";
        try{
            identifier = lock.lockWithTimeout(key,keyTimeout);
        }catch (Exception e){
            log.error("redis lock fail,key:"+key,e);
            throw new DistributeLockException("get redis lock fail,"+e.getMessage());
        }
        if (CsStringUtils.isBlank(identifier)) {
            throw new DistributeLockException("get redis lock fail!");
        }

        return identifier;
    }

    @Override
    public String lockFast(String key, int retryCount) {
        for (int i = 0; i < retryCount; i++) {
            try {
                return lockFast(key);
            }catch (DistributeLockException e){
                log.info("retryCount:{},key:{},error:{}",i+1,key,e.getMessage());
                if( i == retryCount-1 ){
                    throw e;
                }
            }
        }
        return null;
    }

    @Override
    public void unlock(String key,String identifier){
        lock.releaseLock(key, identifier);
    }
}
