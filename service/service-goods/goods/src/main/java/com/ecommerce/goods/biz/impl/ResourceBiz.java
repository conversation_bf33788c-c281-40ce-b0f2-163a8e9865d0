package com.ecommerce.goods.biz.impl;

import com.alibaba.fastjson.JSON;
import com.ecommerce.base.api.dto.saleregion.SaleLevelDTO;
import com.ecommerce.base.api.dto.saleregion.SaleRegionDTO;
import com.ecommerce.base.api.dto.saleregion.SaleRegionRelationDTO;
import com.ecommerce.base.api.dto.saleregion.SaleStoreDTO;
import com.ecommerce.base.api.dto.warehouse.QueryZoneCarriageDTO;
import com.ecommerce.base.api.dto.warehouse.QueryZoneDTO;
import com.ecommerce.base.api.dto.warehouse.StoreQueryDTO;
import com.ecommerce.base.api.dto.warehouse.WarehouseBaseDataDTO;
import com.ecommerce.base.api.dto.warehouse.WarehouseDetailsDTO;
import com.ecommerce.base.api.dto.warehouse.WarehouseZoneMapDTO;
import com.ecommerce.base.api.dto.warehouse.ZoneCarriageDTO;
import com.ecommerce.base.api.service.IReceivingAddressService;
import com.ecommerce.base.api.service.ISaleRegionService;
import com.ecommerce.base.api.service.IWarehouseService;
import com.ecommerce.common.annotation.PrintArgs;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.service.common.BaseBiz;
import com.ecommerce.common.service.common.uuid.UUIDGenerator;
import com.ecommerce.common.utils.BeanConvertUtils;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.common.utils.DateUtil;
import com.ecommerce.common.utils.RandomUtil;
import com.ecommerce.common.utils.mail.CollectionKit;
import com.ecommerce.goods.api.dto.AdministrativeRegionDTO;
import com.ecommerce.goods.api.dto.AttributeDTO;
import com.ecommerce.goods.api.dto.AttributeValueDTO;
import com.ecommerce.goods.api.dto.CategoryAttributeDTO;
import com.ecommerce.goods.api.dto.CategoryAttributeValueDTO;
import com.ecommerce.goods.api.dto.ChangeHistoryDTO;
import com.ecommerce.goods.api.dto.ChangePriceAndNumDTO;
import com.ecommerce.goods.api.dto.ConfirmResourceStoreDTO;
import com.ecommerce.goods.api.dto.ContractResourceDTO;
import com.ecommerce.goods.api.dto.DynamicAttributeDTO;
import com.ecommerce.goods.api.dto.EmallResourceDetailDTO;
import com.ecommerce.goods.api.dto.EmallResourceDetailReqDTO;
import com.ecommerce.goods.api.dto.GetGoodsResourceStoreDTO;
import com.ecommerce.goods.api.dto.GoodsAttributeDTO;
import com.ecommerce.goods.api.dto.GoodsBaseDTO;
import com.ecommerce.goods.api.dto.GoodsCategoryAttrDTO;
import com.ecommerce.goods.api.dto.GoodsCategoryAttrQueryDTO;
import com.ecommerce.goods.api.dto.GoodsCollecttionDTO;
import com.ecommerce.goods.api.dto.GoodsConsignDTO;
import com.ecommerce.goods.api.dto.GoodsDTO;
import com.ecommerce.goods.api.dto.GoodsOtherDTO;
import com.ecommerce.goods.api.dto.GoodsPriceDTO;
import com.ecommerce.goods.api.dto.GoodsQuantityDTO;
import com.ecommerce.goods.api.dto.GoodsResourceDTO;
import com.ecommerce.goods.api.dto.GoodsResourceIdDTO;
import com.ecommerce.goods.api.dto.GoodsResourceListDTO;
import com.ecommerce.goods.api.dto.GoodsResourceStoreDTO;
import com.ecommerce.goods.api.dto.LogisticsResourceDTO;
import com.ecommerce.goods.api.dto.PriceModeDTO;
import com.ecommerce.goods.api.dto.PromptOnsaleResourceDTO;
import com.ecommerce.goods.api.dto.QueryResourceConditionDTO;
import com.ecommerce.goods.api.dto.ReqChangePriceAndNumDTO;
import com.ecommerce.goods.api.dto.ReqConfirmResourceStoreDTO;
import com.ecommerce.goods.api.dto.ReqContractResourceDTO;
import com.ecommerce.goods.api.dto.ReqCreateResourceDTO;
import com.ecommerce.goods.api.dto.ReqGoodsCollecttionDTO;
import com.ecommerce.goods.api.dto.ReqGoodsResourceDTO;
import com.ecommerce.goods.api.dto.ReqLogisticsResourceDTO;
import com.ecommerce.goods.api.dto.ReqPromptOnsaleResourceDTO;
import com.ecommerce.goods.api.dto.ReqResourceDetailDTO;
import com.ecommerce.goods.api.dto.ReqResourcePlatformDTO;
import com.ecommerce.goods.api.dto.ReqResourceSellerDTO;
import com.ecommerce.goods.api.dto.ReqResourceStoreDTO;
import com.ecommerce.goods.api.dto.ReqSellerShopDTO;
import com.ecommerce.goods.api.dto.ReqUpdateResourceDTO;
import com.ecommerce.goods.api.dto.ResourceBuyInfoDTO;
import com.ecommerce.goods.api.dto.ResourceDTO;
import com.ecommerce.goods.api.dto.ResourceDetailDTO;
import com.ecommerce.goods.api.dto.ResourceDetailSearchDTO;
import com.ecommerce.goods.api.dto.ResourceGoodsDTO;
import com.ecommerce.goods.api.dto.ResourceLogisticCostDTO;
import com.ecommerce.goods.api.dto.ResourceOrderPrepareDTO;
import com.ecommerce.goods.api.dto.ResourceOrderPrepareReqDTO;
import com.ecommerce.goods.api.dto.ResourcePlatformDTO;
import com.ecommerce.goods.api.dto.ResourceSellerDTO;
import com.ecommerce.goods.api.dto.ResourceStoreDTO;
import com.ecommerce.goods.api.dto.SalesVolumeDTO;
import com.ecommerce.goods.api.dto.SelectResourceDTO;
import com.ecommerce.goods.api.dto.SellerGoodsDTO;
import com.ecommerce.goods.api.dto.SellerShopDTO;
import com.ecommerce.goods.api.dto.StoreDTO;
import com.ecommerce.goods.api.dto.TmpResourceStoreDTO;
import com.ecommerce.goods.api.dto.TransactionPriceDTO;
import com.ecommerce.goods.api.dto.TransactionPriceReqDTO;
import com.ecommerce.goods.api.dto.UnitConverDTO;
import com.ecommerce.goods.api.dto.UnitDTO;
import com.ecommerce.goods.api.enums.BooleanEnum;
import com.ecommerce.goods.api.enums.CategoryTypeEnum;
import com.ecommerce.goods.api.enums.CurrencyTypeEnum;
import com.ecommerce.goods.api.enums.DeliveryWayEnum;
import com.ecommerce.goods.api.enums.DynamicAttributeEnum;
import com.ecommerce.goods.api.enums.EnableStatusEnum;
import com.ecommerce.goods.api.enums.GoodsCollectionTypeEnum;
import com.ecommerce.goods.api.enums.GoodsStatusEnum;
import com.ecommerce.goods.api.enums.LogisticsCostTypeEnum;
import com.ecommerce.goods.api.enums.MergeAttrPrefixEnum;
import com.ecommerce.goods.api.enums.MergeRegionPrefixEnum;
import com.ecommerce.goods.api.enums.OperateStatusEnum;
import com.ecommerce.goods.api.enums.PaydateTypeEnum;
import com.ecommerce.goods.api.enums.PriceWayEnum;
import com.ecommerce.goods.api.enums.ResourceStatusEnum;
import com.ecommerce.goods.api.enums.StoreTypeEnum;
import com.ecommerce.goods.api.enums.TakedateTypeEnum;
import com.ecommerce.goods.api.enums.ToleranceTypeEnum;
import com.ecommerce.goods.api.enums.TradeStatusEnum;
import com.ecommerce.goods.biz.IGoodsAttributeValueBiz;
import com.ecommerce.goods.biz.IGoodsBiz;
import com.ecommerce.goods.biz.IGoodsCategoryAttrBiz;
import com.ecommerce.goods.biz.IGoodsCategoryBiz;
import com.ecommerce.goods.biz.IResourceBiz;
import com.ecommerce.goods.biz.IResourceDetailAttrBiz;
import com.ecommerce.goods.dao.mapper.GoodsAttributeValueMapper;
import com.ecommerce.goods.dao.mapper.GoodsResourceMapper;
import com.ecommerce.goods.dao.mapper.ResourceHistoryMapper;
import com.ecommerce.goods.dao.mapper.ResourceMapper;
import com.ecommerce.goods.dao.mapper.ResourceRegionMapper;
import com.ecommerce.goods.dao.mapper.ResourceUneffectiveMapper;
import com.ecommerce.goods.dao.vo.Goods;
import com.ecommerce.goods.dao.vo.GoodsAttributeValue;
import com.ecommerce.goods.dao.vo.GoodsCategory;
import com.ecommerce.goods.dao.vo.GoodsCategoryAttribute;
import com.ecommerce.goods.dao.vo.GoodsResource;
import com.ecommerce.goods.dao.vo.Resource;
import com.ecommerce.goods.dao.vo.ResourceHistory;
import com.ecommerce.goods.dao.vo.ResourceRegion;
import com.ecommerce.goods.dao.vo.ResourceUneffective;
import com.ecommerce.goods.exception.ResourceCode;
import com.ecommerce.goods.service.IGoodsService;
import com.ecommerce.goods.service.impl.SMSMessageProducerImpl;
import com.ecommerce.member.api.dto.account.AccountDTO;
import com.ecommerce.member.api.dto.referrer.ReferrerInfoDTO;
import com.ecommerce.member.api.service.IAccountService;
import com.ecommerce.member.api.service.IBuyerAndReferrerService;
import com.ecommerce.member.api.service.IMemberRelationService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ReflectionUtils;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ThreadPoolExecutor;


@Slf4j
@Service
@EnableAsync
public class ResourceBiz extends BaseBiz<Resource> implements IResourceBiz {

    private static final Double HOUR = 3600d;
    private static final Double DAY = 86400d;
    private static final String CENTER_STORE_ID = "1234567890";
    private static final String NO_BRAND = "无";

    private static final String GOODS_TYPE = "goodsType";
    private static final String SALE_AREA = "saleArea";
    private static final String SALE_AREA_CODE = "saleAreaCode";
    private static final String SALE_AREA_CODE2 = "saleAreaCode2";
    private static final String SALE_AREA_CODE3 = "saleAreaCode3";
    private static final String SALE_AREA_CODE4 = "saleAreaCode4";
    private static final String SALE_AREA_CODE5 = "saleAreaCode5";
    private static final String RESOURCE_CODE = "resourceCode";
    private static final String IF_PROTOCOL_PRICE = "ifProtocolPrice";
    private static final String UP_TIME = "upTime";
    private static final String UPDATE_TIME = "updateTime";
    private static final String GOODS_ID = "goodsId";
    private static final String STORE_ID = "storeId";
    private static final String SALE_AREA_REAL_CODE = "saleAreaRealCode";
    private static final String ADMIN = "admin";
    private static final String GOODS_RESOURCE_ID = "goodsResourceId";
    private static final String RESOURCE_ATTRIBUTES = "resourceAttributes";
    private static final String CATEGORY_CODE = "categoryCode";
    private static final String SELLER_ID = "sellerId";
    private static final String DEL_FLG = "delFlg";
    private static final String RESOURCE_ID = "resourceId";
    private static final String STATUS = "status";
    private static final String RESOURCE_REGIONS = "resourceRegions";
    private static final String COUNTRY_NAME = "countryName";
    private static final String CITY_NAME = "cityName";
    private static final String PROVINCE_NAME = "provinceName";
    private static final String AREA_NAME = "areaName";
    private static final String STREET_NAME = "streetName";
    private static final String COUNTRY_CODE = "countryCode";
    private static final String PROVINCE_CODE = "provinceCode";
    private static final String CITY_CODE = "cityCode";
    private static final String AREA_CODE = "areaCode";
    private static final String STREET_CODE = "streetCode";
    private static final String GOODS_DESCRIBE = "goodsDescribe";
    private static final String SPU_ID = "spuId";
    private static final String PAGE_NUM_BEGIN_FROM_1 = "页码从1开始";
    private static final String PAGE_SIZE_MUST_MORE_THAN_0 = "页面大小必须大于0";
    private static final String RESOURCE_CODE_LIST = "资源编号集合";
    private static final String CAN_NOT_CHANGE_THIS_STATUS_RESOURCE = "不能修改该状态的资源";

    @jakarta.annotation.Resource
    private ResourceMapper resourceMapper;
    @jakarta.annotation.Resource
    private ResourceHistoryMapper resourceHistoryMapper;
    @jakarta.annotation.Resource
    private ResourceUneffectiveMapper resourceUneffectiveMapper;
    @jakarta.annotation.Resource
    private ResourceRegionMapper resourceRegionMapper;
    @jakarta.annotation.Resource
    private GoodsResourceMapper goodsResourceMapper;
    @jakarta.annotation.Resource
    private GoodsAttributeValueMapper goodsAttributeValueMapper;

    @Autowired
    private IGoodsService goodsService;
    @Autowired
    private ISaleRegionService iSaleRegionService;
    @Autowired
    private IAccountService iAccountService;
    @Autowired
    private IWarehouseService warehouseService;
    @Autowired
    private IBuyerAndReferrerService iBuyerAndReferrerService;
    @Autowired
    private UUIDGenerator uuidGenerator;
    @Autowired
    private IResourceDetailAttrBiz iResourceDetailAttrBiz;
    @Autowired
    private IGoodsBiz iGoodsBiz;
    @Autowired
    private ResourceElasticsearchBiz resourceElasticsearchBiz;
    @Autowired
    private IGoodsCategoryBiz goodsCategoryBiz;
    @Autowired
    private IGoodsAttributeValueBiz goodsAttributeValueBiz;
    @Autowired
    private IGoodsCategoryAttrBiz goodsCategoryAttrBiz;
    @Autowired
    protected IReceivingAddressService receivingAddressService;
    @Autowired
    private SMSMessageProducerImpl smsMessageProducer;
    @Autowired
    private ThreadPoolExecutor threadPoolExecutor;

    @Autowired
    private IMemberRelationService memberRelationService;

    @Override
    public PageInfo<ResourceSellerDTO> pageResourceSeller(ReqResourceSellerDTO reqDto) {
        // 参数检测
        checkParams(reqDto);
        // 初始化结果
        PageInfo<ResourceSellerDTO> pageData = new PageInfo<>();
        try {
            // 入参封装
            Example example = new Example(Resource.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo(SELLER_ID, reqDto.getSellerId());
            criteriaAddParams(reqDto, criteria);
            criteria.andEqualTo(DEL_FLG, BooleanEnum.NO.code());
            example.orderBy(UPDATE_TIME).desc();
            // 分页查询数据库
            Page<Resource> page = PageMethod.startPage(reqDto.getPageNum(), reqDto.getPageSize(), true)
                    .doSelectPage(() -> resourceMapper.selectByExample(example));
            // 结果封装
            if (page.size() < 1) {
                pageData.setPages(0);
                pageData.setTotal(0);
                return pageData;
            }
            pageData.setPages(page.getPages());
            pageData.setTotal(page.getTotal());
            List<ResourceSellerDTO> list = new ArrayList<>();
            List<Resource> resources = page.getResult();
            listAddValues(resources, list);
            pageData.setList(list);
        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("pageResourceSeller Exception Message:", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
        return pageData;
    }

    private void listAddValues(List<Resource> resources, List<ResourceSellerDTO> list) {
        for (Resource resource : Optional.ofNullable(resources).orElse(Collections.emptyList())) {
            ResourceSellerDTO dto = new ResourceSellerDTO();
            BeanUtils.copyProperties(resource, dto);
            GoodsDTO goodsInfo = getGoodsInfo(resource.getGoodsId());
            if (goodsInfo != null) {
                dto.setImgs(goodsInfo.getImgs());
            }
            dto.setSaleNum(resource.getSaleNum().add(resource.getCansaleNum()));
            // 设置特殊值
            // 交易状态
            boolean tradeStatus = checkResourceTradeStatus(resource);
            setStatus(tradeStatus, dto);
            dto.setSaleArea(resource.getSaleArea());
            // 查询是否有资源历史记录
            dto.setIsHaveChangeHistory(BooleanEnum.YES.code());
            dto.setPriceUnit(resource.getPriceUnit());
            List<String> deliveryWays = getDeliveryWays(resource, dto);
            dto.setDeliveryWays(deliveryWays);
            dto.setIfProtocolPrice(resource.getIfProtocolPrice());
            list.add(dto);
        }
    }

    @NotNull
    private List<String> getDeliveryWays(Resource resource, ResourceSellerDTO dto) {
        if (resource.getUpdateTime() == null) {
            dto.setUpdateTime(resource.getCreateTime());
        }
        AccountDTO account = this.iAccountService.findById(resource.getCreateUser());
        if (account != null) {
            dto.setCreateUser(account.getAccountName());
        }
        List<String> deliveryWays = Lists.newArrayList();
        if (resource.getIfTakeSelf()) {
            deliveryWays.add(DeliveryWayEnum.BUYER_TAKE.message());
        }
        if (resource.getIfPlatformDelivery()) {
            deliveryWays.add(DeliveryWayEnum.PLATFORM_DELIVERY.message());
        }
        if (resource.getIfSellerDelivery()) {
            deliveryWays.add(DeliveryWayEnum.SELLER_DELIVERY.message());
        }
        return deliveryWays;
    }

    private static void setStatus(boolean tradeStatus, ResourceSellerDTO dto) {
        if (tradeStatus) {
            dto.setTradeStatus(TradeStatusEnum.TRADE_STATUS100.code());
        } else {
            dto.setTradeStatus(TradeStatusEnum.TRADE_STATUS200.code());
        }
    }

    private static void criteriaAddParams(ReqResourceSellerDTO reqDto, Example.Criteria criteria) {
        if (!CsStringUtils.isBlank(reqDto.getGoodsType())) {
            criteria.andEqualTo(GOODS_TYPE, reqDto.getGoodsType());
        }
        if (!CsStringUtils.isBlank(reqDto.getGoodsDescribe())) {
            criteria.andLike(GOODS_DESCRIBE, "%" + reqDto.getGoodsDescribe() + "%");
        }
        if (!CsStringUtils.isBlank(reqDto.getCategoryCode())) {
            criteria.andLike(CATEGORY_CODE, reqDto.getCategoryCode() + "%");
        }
        if (!CsStringUtils.isBlank(reqDto.getSaleArea())) {
            criteria.andLike(SALE_AREA, "%" + reqDto.getSaleArea() + "%");
        }
        if (!CsStringUtils.isBlank(reqDto.getSaleAreaCode())) {
            criteria.andEqualTo(SALE_AREA_CODE, reqDto.getSaleAreaCode());
        }
        if (!CsStringUtils.isBlank(reqDto.getSaleAreaCode2())) {
            criteria.andEqualTo(SALE_AREA_CODE2, reqDto.getSaleAreaCode2());
        }
        if (!CsStringUtils.isBlank(reqDto.getSaleAreaCode3())) {
            criteria.andEqualTo(SALE_AREA_CODE3, reqDto.getSaleAreaCode3());
        }
        if (!CsStringUtils.isBlank(reqDto.getSaleAreaCode4())) {
            criteria.andEqualTo(SALE_AREA_CODE4, reqDto.getSaleAreaCode4());
        }
        if (!CsStringUtils.isBlank(reqDto.getSaleAreaCode5())) {
            criteria.andEqualTo(SALE_AREA_CODE5, reqDto.getSaleAreaCode5());
        }
        if (!CsStringUtils.isBlank(reqDto.getResourceCode())) {
            criteria.andLike(RESOURCE_CODE, "%" + reqDto.getResourceCode() + "%");
        }
        if (!CsStringUtils.isBlank(reqDto.getStoreName())) {
            criteria.andLike("storeName", "%" + reqDto.getStoreName() + "%");
        }
        if (reqDto.getIfProtocolPrice() != null) {
            criteria.andEqualTo(IF_PROTOCOL_PRICE, reqDto.getIfProtocolPrice());
        }
        if (reqDto.getStartTime() != null) {
            criteria.andGreaterThanOrEqualTo(UP_TIME, reqDto.getStartTime());
        }
        if (reqDto.getEndTime() != null) {
            criteria.andLessThanOrEqualTo(UP_TIME, reqDto.getEndTime());
        }
        if (!CsStringUtils.isBlank(reqDto.getStatus())) {
            criteria.andEqualTo(STATUS, reqDto.getStatus());
        }
    }

    private static void checkParams(ReqResourceSellerDTO reqDto) {
        if (reqDto == null) {
            throw new BizException(ResourceCode.PARAM_NULL, "入参");
        }
        if (CsStringUtils.isBlank(reqDto.getSellerId())) {
            throw new BizException(ResourceCode.PARAM_NULL, "卖家编号");
        }
        if (reqDto.getPageNum() == null) {
            throw new BizException(ResourceCode.PARAM_NULL, "页码");
        }
        if (reqDto.getPageNum() <= 0) {
            throw new BizException(ResourceCode.PARAM_ERROR, PAGE_NUM_BEGIN_FROM_1);
        }
        if (reqDto.getPageSize() == null) {
            throw new BizException(ResourceCode.PARAM_NULL, "页面大小");
        }
        if (reqDto.getPageNum() <= 0) {
            throw new BizException(ResourceCode.PARAM_ERROR, PAGE_SIZE_MUST_MORE_THAN_0);
        }
    }

    /**
     * @param reqDto
     * @return
     */
    @Override
    public PageInfo<ResourceSellerDTO> pageResourceSellerWithDataPerm(ReqResourceSellerDTO reqDto) {
        log.info("访问数据权限查挂牌列表");
        return PageMethod.startPage(reqDto.getPageNum(), reqDto.getPageSize())
                .doSelectPageInfo(() -> resourceMapper.pageQueryWithDataPerm(reqDto));
    }


    @Override
    public PageInfo<ResourcePlatformDTO> pageResourcePlatform(ReqResourcePlatformDTO reqDto) {
        // 参数检测
        checkParams(reqDto);
        // 初始化结果
        PageInfo<ResourcePlatformDTO> pageData = new PageInfo<>();
        try {
            // 入参封装
            Example example = new Example(Resource.class);
            Example.Criteria criteria = example.createCriteria();
            criteriaAddItems(reqDto, criteria);
            criteria.andEqualTo(DEL_FLG, BooleanEnum.NO.code());
            example.orderBy(UPDATE_TIME).desc();
            // 分页查询数据库
            PageMethod.startPage(reqDto.getPageNum(), reqDto.getPageSize(), true);
            Page<Resource> page = PageMethod.startPage(reqDto.getPageNum(), reqDto.getPageSize(), true)
                    .doSelectPage(() -> resourceMapper.selectByExample(example));
            // 结果封装
            if (page.size() < 1) {
                pageData.setPages(0);
                pageData.setTotal(0);
                return pageData;
            }
            pageData.setPages(page.getPages());
            pageData.setTotal(page.getTotal());
            List<ResourcePlatformDTO> list = new ArrayList<>();
            List<Resource> resources = page.getResult();
            listAddItems(resources, list);
            pageData.setList(list);
        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("pageResourcePlatform Exception Message: {}", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
        return pageData;
    }

    private void listAddItems(List<Resource> resources, List<ResourcePlatformDTO> list) {
        Map<String, String> goodsCategoryMap = Maps.newHashMap();
        Map<String, GoodsDTO> goodsMap = Maps.newHashMap();
        for (Resource resource : Optional.ofNullable(resources).orElse(Collections.emptyList())) {
            ResourcePlatformDTO dto = new ResourcePlatformDTO();
            BeanUtils.copyProperties(resource, dto);
            dto.setSaleNum(resource.getSaleNum().add(resource.getCansaleNum()));
            dto.setPriceUnit(resource.getPriceUnit());
            // 设置特殊值
            if (resource.getUpdateTime() == null) {
                dto.setUpdateTime(resource.getCreateTime());
            }
            GoodsDTO goodsInfo = goodsMap.get(resource.getGoodsId());
            if (goodsInfo == null) {
                goodsInfo = getGoodsInfo(resource.getGoodsId());
                goodsMap.put(goodsInfo.getGoodsId(), goodsInfo);
            }
            List<String> goodsCategoryList = Lists.newArrayList();
            Set<String> goodsQueryCategorySet = Sets.newHashSet();
            dtoSetGoodsTypeStr(goodsInfo, goodsCategoryMap, goodsQueryCategorySet, goodsCategoryList, dto);
            List<String> saleAreaList = Lists.newArrayList();
            saleAreaListAddValues(resource, saleAreaList);
            log.info("saleAreaList:" + JSON.toJSONString(saleAreaList));
            dto.setSaleArea(CsStringUtils.join(saleAreaList, ">"));
            list.add(dto);
        }
    }

    private void dtoSetGoodsTypeStr(GoodsDTO goodsInfo, Map<String, String> goodsCategoryMap, Set<String> goodsQueryCategorySet, List<String> goodsCategoryList, ResourcePlatformDTO dto) {
        if (CsStringUtils.isNotEmpty(goodsInfo.getCategoryCode())) {
            String currentCategoryCode = goodsInfo.getCategoryCode();
            while (currentCategoryCode.length() > 6) {
                if (goodsCategoryMap.get(currentCategoryCode) == null) {
                    goodsQueryCategorySet.add(currentCategoryCode);
                }
                goodsCategoryList.add(currentCategoryCode);
                currentCategoryCode = CsStringUtils.substring(currentCategoryCode, 0, currentCategoryCode.length() - 3);
            }
            if (!CollectionUtils.isEmpty(goodsQueryCategorySet)) {
                Condition condition = new Condition(GoodsCategory.class);
                Example.Criteria categoryConditionCriteria = condition.createCriteria();
                categoryConditionCriteria.andIn(CATEGORY_CODE, goodsQueryCategorySet);
                List<GoodsCategory> categoryList = goodsCategoryBiz.findByCondition(condition);
                if (!CollectionUtils.isEmpty(categoryList)) {
                    categoryList.stream().forEach(
                            category -> goodsCategoryMap.put(category.getCategoryCode(), category.getCategoryName()));
                }
            }
            List<String> goodsCategoryNameList = Lists.newArrayList();
            log.info("goodsCategoryList:" + JSON.toJSONString(goodsCategoryList));
            goodsCategoryList.stream().forEach(categoryCode -> {
                if (goodsCategoryMap.get(categoryCode) != null) {
                    goodsCategoryNameList.add(goodsCategoryMap.get(categoryCode));
                }
            });
            Collections.reverse(goodsCategoryNameList);
            log.info("goodsCategoryNameList:" + JSON.toJSONString(goodsCategoryNameList));
            dto.setGoodsTypeStr(CsStringUtils.join(goodsCategoryNameList, ">"));
        }
    }

    private static void saleAreaListAddValues(Resource resource, List<String> saleAreaList) {
        if (CsStringUtils.isNotEmpty(resource.getSaleAreaName())) {
            saleAreaList.add(resource.getSaleAreaName());
        }
        if (CsStringUtils.isNotEmpty(resource.getSaleAreaName2())) {
            saleAreaList.add(resource.getSaleAreaName2());
        }
        if (CsStringUtils.isNotEmpty(resource.getSaleAreaName3())) {
            saleAreaList.add(resource.getSaleAreaName3());
        }
        if (CsStringUtils.isNotEmpty(resource.getSaleAreaName4())) {
            saleAreaList.add(resource.getSaleAreaName4());
        }
        if (CsStringUtils.isNotEmpty(resource.getSaleAreaName5())) {
            saleAreaList.add(resource.getSaleAreaName5());
        }
    }

    private static void criteriaAddItems(ReqResourcePlatformDTO reqDto, Example.Criteria criteria) {
        if (!CsStringUtils.isBlank(reqDto.getSellerId())) {
            criteria.andEqualTo(SELLER_ID, reqDto.getSellerId());
        }
        if (!CsStringUtils.isBlank(reqDto.getSellerName())) {
            criteria.andLike("sellerName", "%" + reqDto.getSellerName() + "%");
        }
        if (!CsStringUtils.isBlank(reqDto.getGoodsType())) {
            criteria.andEqualTo(GOODS_TYPE, reqDto.getGoodsType());
        }
        if (!CsStringUtils.isBlank(reqDto.getCategoryCode())) {
            criteria.andLike(CATEGORY_CODE, reqDto.getCategoryCode() + "%");
        }
        if (!CsStringUtils.isBlank(reqDto.getGoodsDescribe())) {
            criteria.andLike(GOODS_DESCRIBE, "%" + reqDto.getGoodsDescribe() + "%");
        }
        if (!CsStringUtils.isBlank(reqDto.getSaleArea())) {
            criteria.andLike(SALE_AREA, "%" + reqDto.getSaleArea() + "%");
        }
        if (!CsStringUtils.isBlank(reqDto.getSaleAreaCode())) {
            criteria.andEqualTo(SALE_AREA_CODE, reqDto.getSaleAreaCode());
        }
        if (!CsStringUtils.isBlank(reqDto.getSaleAreaCode2())) {
            criteria.andEqualTo(SALE_AREA_CODE2, reqDto.getSaleAreaCode2());
        }
        if (!CsStringUtils.isBlank(reqDto.getSaleAreaCode3())) {
            criteria.andEqualTo(SALE_AREA_CODE3, reqDto.getSaleAreaCode3());
        }
        if (!CsStringUtils.isBlank(reqDto.getSaleAreaCode4())) {
            criteria.andEqualTo(SALE_AREA_CODE4, reqDto.getSaleAreaCode4());
        }
        if (!CsStringUtils.isBlank(reqDto.getSaleAreaCode5())) {
            criteria.andEqualTo(SALE_AREA_CODE5, reqDto.getSaleAreaCode5());
        }
        if (!CsStringUtils.isBlank(reqDto.getResourceCode())) {
            criteria.andLike(RESOURCE_CODE, "%" + reqDto.getResourceCode() + "%");
        }
        if (reqDto.getIfProtocolPrice() != null) {
            criteria.andEqualTo(IF_PROTOCOL_PRICE, reqDto.getIfProtocolPrice());
        }
        if (!CsStringUtils.isBlank(reqDto.getStatus())) {
            criteria.andEqualTo(STATUS, reqDto.getStatus());
        }
    }

    private static void checkParams(ReqResourcePlatformDTO reqDto) {
        if (reqDto == null) {
            throw new BizException(ResourceCode.PARAM_NULL, "入参");
        }
        if (reqDto.getPageNum() == null) {
            throw new BizException(ResourceCode.PARAM_NULL, "页码");
        }
        if (reqDto.getPageNum() <= 0) {
            throw new BizException(ResourceCode.PARAM_ERROR, "页码，从1开始");
        }
        if (reqDto.getPageSize() == null) {
            throw new BizException(ResourceCode.PARAM_NULL, "页面大小");
        }
        if (reqDto.getPageNum() <= 0) {
            throw new BizException(ResourceCode.PARAM_ERROR, "页面大小，必须大于0");
        }
    }

    @Override
    public GoodsPriceDTO getGoodsResourceStore(GetGoodsResourceStoreDTO getGoodsResourceStoreDTO) {
        Integer level = getGoodsResourceStoreDTO.getLevel();
        String goodsId = getGoodsResourceStoreDTO.getGoodsId();
        String sellerId = getGoodsResourceStoreDTO.getSellerId();
        String accountId = getGoodsResourceStoreDTO.getAccountId();
        List<String> filterIdList = getGoodsResourceStoreDTO.getFilterIdList();
        // 参数检测
        checkParams(level, goodsId, sellerId);
        // 仓库-资源聚合
        GoodsPriceDTO goodsPriceDTO = new GoodsPriceDTO();
        goodsPriceDTO.setAreaLevel(level);
        List<GoodsResourceStoreDTO> list = new ArrayList<>();
        try {
            // 查询商品基本属性
            if (level == 0) { // 不按区域定价
                GoodsResourceStoreDTO goodsResourceStoreDTO = new GoodsResourceStoreDTO();
                goodsResourceStoreDTO.setCurrency(CurrencyTypeEnum.CNY.code());
                goodsResourceStoreDTO.setOperateStatus(EnableStatusEnum.ENABLE_STATUS100.code());
                list.add(goodsResourceStoreDTO);
            } else { // 按区域定价
                // 查询卖家仓库
                List<SaleRegionDTO> saleRegionDTOS = findDetailRegionbyLevel(level, accountId, filterIdList);
                listAddValues2(saleRegionDTOS, level, goodsId, list, sellerId);
            }
            // 按销售区域排序
            Collections.sort(list);
            goodsPriceDTO.setGoodsResourceStoreDTOS(list);
        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("getGoodsResourceStore Exception Message: {}", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
        return goodsPriceDTO;
    }

    private void listAddValues2(List<SaleRegionDTO> saleRegionDTOS, Integer level, String goodsId, List<GoodsResourceStoreDTO> list, String sellerId) {
        for (SaleRegionDTO saleRegionDTO : Optional.ofNullable(saleRegionDTOS).orElse(Collections.emptyList())) {
            List<SaleStoreDTO> saleStoreDTOS = saleRegionDTO.getSaleStoreDTOS();
            List<SaleRegionRelationDTO> regionDTOS = saleRegionDTO.getRegionDTOS();
            listAddValues3(level, goodsId, list, saleRegionDTO, saleStoreDTOS);
            // 中心仓信息
            GoodsResourceStoreDTO zxcgGoodsResourceStoreDTO = new GoodsResourceStoreDTO();
            zxcgGoodsResourceStoreDTO.setCurrency(CurrencyTypeEnum.CNY.code());
            zxcgGoodsResourceStoreDTO.setStoreId(CENTER_STORE_ID);
            zxcgGoodsResourceStoreDTO.setStoreType(StoreTypeEnum.STORE_TYPE200.code());
            zxcgGoodsResourceStoreDTO.setStoreName(StoreTypeEnum.STORE_TYPE200.message());
            zxcgGoodsResourceStoreDTO.setOperateStatus(OperateStatusEnum.Forbidden.code());
            zxcgGoodsResourceStoreDTO = setSaleArea(zxcgGoodsResourceStoreDTO, saleRegionDTO, level);
            Example resourceExample = new Example(Resource.class);
            Example.Criteria resourceCriteria = resourceExample.createCriteria();
            resourceCriteria.andEqualTo(GOODS_ID, goodsId);
            resourceCriteria.andEqualTo(DEL_FLG, 0);
            resourceCriteria.andEqualTo(STORE_ID, CENTER_STORE_ID);
            resourceCriteria.andEqualTo(SALE_AREA_REAL_CODE, zxcgGoodsResourceStoreDTO.getSaleAreaRealCode());
            List<Resource> resources = resourceMapper.selectByExample(resourceExample);
            if (!CollectionUtils.isEmpty(resources)) {
                Resource resource = resources.get(0);
                zxcgGoodsResourceStoreDTO.setPriceWay(resource.getPriceWay());
                zxcgGoodsResourceStoreDTO.setPrice(resource.getPrice());
                zxcgGoodsResourceStoreDTO.setArrivePrice(resource.getArrivePrice());
                zxcgGoodsResourceStoreDTO.setFactoryPrice(resource.getFactoryPrice());
                zxcgGoodsResourceStoreDTO.setSaleNum(resource.getSaleNum());
                zxcgGoodsResourceStoreDTO.setCansaleNum(resource.getCansaleNum());
                zxcgGoodsResourceStoreDTO.setOperateStatus(OperateStatusEnum.Blocked.code());
            }
            // 设置行政区域
            List<AdministrativeRegionDTO> administrativeRegions = new ArrayList<>();
            List<QueryZoneDTO> queryZonelist = new ArrayList<>();
            queryZonelistAddValues(regionDTOS, queryZonelist);
            List<ZoneCarriageDTO> zoneCarriageList = queryZoneCarriage(queryZonelist, "030140100", sellerId);
            zxcgGoodsResourceStoreDTOSetAdministrativeRegions(zoneCarriageList, administrativeRegions, zxcgGoodsResourceStoreDTO);
            if (!CsStringUtils.isNullOrBlank(zxcgGoodsResourceStoreDTO.getSaleAreaCodes()) && CollectionKit.isNotEmpty(zoneCarriageList)) {
                list.add(zxcgGoodsResourceStoreDTO);
            }
        }
    }

    private void listAddValues3(Integer level, String goodsId, List<GoodsResourceStoreDTO> list, SaleRegionDTO saleRegionDTO, List<SaleStoreDTO> saleStoreDTOS) {
        for (SaleStoreDTO saleStore : Optional.ofNullable(saleStoreDTOS).orElse(Collections.emptyList())) {
            // 添加仓库
            GoodsResourceStoreDTO goodsResourceStoreDTO = new GoodsResourceStoreDTO();
            // 区域定价层级
            // 数据库中无数据，初始化数据
            goodsResourceStoreDTO.setCurrency(CurrencyTypeEnum.CNY.code());
            goodsResourceStoreDTO.setStoreId(saleStore.getStoreId());
            goodsResourceStoreDTO.setStoreName(saleStore.getStoreName());
            goodsResourceStoreDTO.setStoreType(saleStore.getStoreType());
            goodsResourceStoreDTO.setStoreAddress(saleStore.getStoreLocation());
            goodsResourceStoreDTO.setOperateStatus(OperateStatusEnum.Forbidden.code());
            goodsResourceStoreDTO = setSaleArea(goodsResourceStoreDTO, saleRegionDTO, level);
            Example resourceExample = new Example(Resource.class);
            Example.Criteria resourceCriteria = resourceExample.createCriteria();
            resourceCriteria.andEqualTo(GOODS_ID, goodsId);
            resourceCriteria.andEqualTo(DEL_FLG, 0);
            resourceCriteria.andEqualTo(STORE_ID, saleStore.getStoreId());
            resourceCriteria.andEqualTo(SALE_AREA_REAL_CODE, goodsResourceStoreDTO.getSaleAreaRealCode());
            List<Resource> resources = resourceMapper.selectByExample(resourceExample);
            if (!CollectionUtils.isEmpty(resources)) {
                Resource resource = resources.get(0);
                goodsResourceStoreDTO.setPriceWay(resource.getPriceWay());
                goodsResourceStoreDTO.setPrice(resource.getPrice());
                goodsResourceStoreDTO.setArrivePrice(resource.getArrivePrice());
                goodsResourceStoreDTO.setFactoryPrice(resource.getFactoryPrice());
                goodsResourceStoreDTO.setSaleNum(resource.getSaleNum());
                goodsResourceStoreDTO.setCansaleNum(resource.getCansaleNum());
                goodsResourceStoreDTO.setOperateStatus(OperateStatusEnum.Blocked.code());
            }
            // 设置行政区域
            List<AdministrativeRegionDTO> administrativeRegions = new ArrayList<>();
            WarehouseDetailsDTO warehouseDetailsDTO = warehouseService.queryWarehouseDetails(saleStore.getStoreId());
            List<WarehouseZoneMapDTO> deliveryAreas = warehouseDetailsDTO.getDeliveryAreas();
            goodsResourceStoreDTOSetAdministrativeRegions(deliveryAreas, administrativeRegions, goodsResourceStoreDTO);
            if (!CsStringUtils.isNullOrBlank(goodsResourceStoreDTO.getSaleAreaCodes())) {
                list.add(goodsResourceStoreDTO);
            }
        }
    }

    private static void goodsResourceStoreDTOSetAdministrativeRegions(List<WarehouseZoneMapDTO> deliveryAreas, List<AdministrativeRegionDTO> administrativeRegions, GoodsResourceStoreDTO goodsResourceStoreDTO) {
        for (WarehouseZoneMapDTO deliveryArea : Optional.ofNullable(deliveryAreas).orElse(Collections.emptyList())) {
            AdministrativeRegionDTO administrativeRegionDTO = new AdministrativeRegionDTO();
            administrativeRegionDTO.setProvinceName(deliveryArea.getProvince());
            administrativeRegionDTO.setProvinceCode(deliveryArea.getProvinceCode());
            administrativeRegionDTO.setCityName(deliveryArea.getCity());
            administrativeRegionDTO.setCityCode(deliveryArea.getCityCode());
            administrativeRegionDTO.setAreaName(deliveryArea.getDistrict());
            administrativeRegionDTO.setAreaCode(deliveryArea.getDistrictCode());
            administrativeRegionDTO.setStreetName(deliveryArea.getStreet());
            administrativeRegionDTO.setIsCarriage(BooleanEnum.YES.code());
            administrativeRegions.add(administrativeRegionDTO);
        }
        goodsResourceStoreDTO.setAdministrativeRegions(administrativeRegions);
    }

    private static void queryZonelistAddValues(List<SaleRegionRelationDTO> regionDTOS, List<QueryZoneDTO> queryZonelist) {
        for (SaleRegionRelationDTO region : Optional.ofNullable(regionDTOS).orElse(Collections.emptyList())) {
            QueryZoneDTO queryZoneDTO = new QueryZoneDTO();
            queryZoneDTO.setProvince(region.getProvinceName());
            queryZoneDTO.setProvinceCode(region.getProvinceCode());
            queryZoneDTO.setCity(region.getCityName());
            queryZoneDTO.setCityCode(region.getCityCode());
            queryZoneDTO.setDistrict(region.getDistrictName());
            queryZoneDTO.setDistrictCode(region.getDistrictCode());
            queryZonelist.add(queryZoneDTO);
        }
    }

    private static void zxcgGoodsResourceStoreDTOSetAdministrativeRegions(List<ZoneCarriageDTO> zoneCarriageList, List<AdministrativeRegionDTO> administrativeRegions, GoodsResourceStoreDTO zxcgGoodsResourceStoreDTO) {
        for (ZoneCarriageDTO carriageDTO : Optional.ofNullable(zoneCarriageList).orElse(Collections.emptyList())) {
            AdministrativeRegionDTO administrativeRegionDTO = new AdministrativeRegionDTO();
            administrativeRegionDTO.setStreetName(carriageDTO.getStreet());
            administrativeRegionDTO.setAreaName(carriageDTO.getDistrict());
            administrativeRegionDTO.setAreaCode(carriageDTO.getDistrictCode());
            administrativeRegionDTO.setCityName(carriageDTO.getCity());
            administrativeRegionDTO.setCityCode(carriageDTO.getCityCode());
            administrativeRegionDTO.setProvinceName(carriageDTO.getProvince());
            administrativeRegionDTO.setProvinceCode(carriageDTO.getProvinceCode());
            if (carriageDTO.getDeliveryFlag().equals(BooleanEnum.NO.code().toString())) {
                administrativeRegionDTO.setIsCarriage(BooleanEnum.NO.code());
            } else if (carriageDTO.getDeliveryFlag().equals(BooleanEnum.YES.code().toString())) {
                administrativeRegionDTO.setIsCarriage(BooleanEnum.YES.code());
            }
            administrativeRegions.add(administrativeRegionDTO);
        }
        zxcgGoodsResourceStoreDTO.setAdministrativeRegions(administrativeRegions);
    }

    private static void checkParams(Integer level, String goodsId, String sellerId) {
        if (level == null || level < 0) {
            throw new BizException(ResourceCode.PARAM_NULL, "销售区域层级");
        }
        if (CsStringUtils.isBlank(goodsId)) {
            throw new BizException(ResourceCode.PARAM_NULL, "商品编号");
        }
        if (CsStringUtils.isBlank(sellerId)) {
            throw new BizException(ResourceCode.PARAM_NULL, "卖家编号");
        }
    }

    @Override
    @Transactional
    public void createResource(ReqCreateResourceDTO reqCreateResourceDTO) {
        // 参数检查
        createResourceCheck(reqCreateResourceDTO);
        // 入参
        String sellerId = reqCreateResourceDTO.getSellerId();
        String sellerName = reqCreateResourceDTO.getSellerName();
        String operator = reqCreateResourceDTO.getOperator();
        String spuId = reqCreateResourceDTO.getSpuId();
        List<GoodsBaseDTO> goodsBaseDTOs = reqCreateResourceDTO.getGoodsBaseDTOs();
        GoodsPriceDTO goodsPriceDTO = reqCreateResourceDTO.getGoodsPriceDTO();
        GoodsQuantityDTO goodsQuantityDTO = reqCreateResourceDTO.getGoodsQuantityDTO();
        GoodsConsignDTO goodsConsignDTO = reqCreateResourceDTO.getGoodsConsignDTO();
        GoodsOtherDTO goodsOtherDTO = reqCreateResourceDTO.getGoodsOtherDTO();
        log.info("===============>reqCreateResourceDTOgetGoodsConsignDTO{}", reqCreateResourceDTO.getGoodsConsignDTO());
        String machineRuleId = "";
        String emptyLoadRuleId = "";
        if (goodsConsignDTO != null) {
            machineRuleId = goodsConsignDTO.getMachineRuleId() == null ? "" : goodsConsignDTO.getMachineRuleId();
            emptyLoadRuleId = goodsConsignDTO.getEmptyLoadRuleId() == null ? "" : goodsConsignDTO.getEmptyLoadRuleId();
        }
        try {
            for (GoodsBaseDTO goodsBaseDTO : goodsBaseDTOs) {
                //检查商品基本信息
                checkGoodsBaseDTO(goodsBaseDTO);
                // 查询商品信息
                String goodsId = goodsBaseDTO.getGoodsId();
                GoodsDTO goodsDTO = getGoodsInfo(goodsId);
                Integer goodsStatus = goodsDTO.getGoodsStatus();
                if (GoodsStatusEnum.ENABLE.getCode() != goodsStatus) {
                    throw new BizException(BasicCode.CUSTOM_ERROR, goodsDTO.getGoodsName() + "的状态异常,不能挂牌");
                }
                // 新增资源
                List<GoodsResourceStoreDTO> goodsResourceStoreDTOS = goodsPriceDTO.getGoodsResourceStoreDTOS();
                resourceAddParams(reqCreateResourceDTO, goodsBaseDTO, goodsResourceStoreDTOS, spuId, goodsId, goodsPriceDTO, goodsQuantityDTO, goodsConsignDTO, goodsOtherDTO, machineRuleId, emptyLoadRuleId, goodsDTO, sellerId, sellerName, operator);
            }
        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("createResource Exception Message: {}", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
    }

    private void resourceAddParams(ReqCreateResourceDTO reqCreateResourceDTO, GoodsBaseDTO goodsBaseDTO, List<GoodsResourceStoreDTO> goodsResourceStoreDTOS, String spuId, String goodsId, GoodsPriceDTO goodsPriceDTO, GoodsQuantityDTO goodsQuantityDTO, GoodsConsignDTO goodsConsignDTO, GoodsOtherDTO goodsOtherDTO, String machineRuleId, String emptyLoadRuleId, GoodsDTO goodsDTO, String sellerId, String sellerName, String operator) {
        if (goodsResourceStoreDTOS == null || goodsResourceStoreDTOS.size() == 0) {
            return;
        }
        for (GoodsResourceStoreDTO dto : goodsResourceStoreDTOS) {
            log.info(" >>> priceWay: {}", dto.getPriceWay());
            // 查询是否挂牌了该资源
            String saleAreaRealCode = "";
            saleAreaRealCode = getSaleAreaRealCode(dto);
            boolean isCanCreateResource = checkIsCanCreateResource(spuId, goodsId, dto.getStoreId(), saleAreaRealCode);
            // 只有启用的资源才存库
            if (EnableStatusEnum.ENABLE_STATUS100.code().equals(dto.getOperateStatus()) && isCanCreateResource) {
                //检查商品其他信息
                checkGoodsOtherInfo(goodsPriceDTO, goodsQuantityDTO, goodsConsignDTO, goodsOtherDTO, dto);
                //初始化
                Resource resource = new Resource();
                // 商品基本信息
                resource.setResourceId(uuidGenerator.gain());
                //设置空载费和台班费规则
                resource.setMachineRuleId(machineRuleId);
                resource.setEmptyLoadRuleId(emptyLoadRuleId);
                resource.setSpuId(spuId);
                resource.setGoodsId(goodsId);
                resource.setGoodsName(goodsBaseDTO.getGoodsName());
                resource.setGoodsType(Objects.toString(goodsDTO.getCategoryType(), null));
                resource.setCategoryCode(goodsDTO.getCategoryCode());
                resource.setGoodsDescribe(goodsDTO.getSearchKeywords());
                resourceSetBrand(goodsDTO, resource);
                resource.setResourceName(goodsDTO.getGoodsName());
                resource.setResourceCode(generatorResourceCode()); //生成资源编号

                // 商品定价信息
                resource.setAreaLevel(goodsPriceDTO.getAreaLevel());
                resource.setPriceDescribe(goodsPriceDTO.getPriceDescribe());
                resource.setCurrency(CurrencyTypeEnum.CNY.code());// 设置币种信息
                resource.setCurrencySymbol(CurrencyTypeEnum.CNY.code());
                resource.setCurrencyName(CurrencyTypeEnum.CNY.message());

                resource.setPriceWay(dto.getPriceWay()); // 定价模式
                resource.setFactoryPrice(dto.getFactoryPrice());
                resource.setArrivePrice(dto.getArrivePrice());
                if (PriceWayEnum.PRICE_TYPE2.code().equals(dto.getPriceWay())) {
                    resource.setArrivePrice(null);
                }
                resource.setPrice(dto.getFactoryPrice());
                // 袋 -> 吨 (换算率)
                SellerGoodsDTO sellerGoodsDetail = iGoodsBiz.getSellerGoodsDetail(goodsDTO.getGoodsId());
                resource.setConvertRate(sellerGoodsDetail.getConversionRate());

                resource.setPriceUnit(goodsDTO.getMeasureUnit());
                resource.setSaleUnit(goodsDTO.getUnit());
                //设置是否可议价
                resource.setIfProtocolPrice(goodsPriceDTO.getIfProtocolPrice());
                // 设置仓库信息
                resource.setStoreId(dto.getStoreId());
                resource.setStoreName(dto.getStoreName());
                resource.setStoreType(dto.getStoreType());
                resource.setStoreAddress(dto.getStoreAddress());
                // 设置销售区域
                resource.setSaleAreaCode(dto.getSaleAreaCode());
                resource.setSaleAreaCode2(dto.getSaleAreaCode2());
                resource.setSaleAreaCode3(dto.getSaleAreaCode3());
                resource.setSaleAreaCode4(dto.getSaleAreaCode4());
                resource.setSaleAreaCode5(dto.getSaleAreaCode5());
                resource.setSaleAreaRealCode(saleAreaRealCode);
                resource.setSaleAreaName(dto.getSaleAreaName());
                resource.setSaleAreaName2(dto.getSaleAreaName2());
                resource.setSaleAreaName3(dto.getSaleAreaName3());
                resource.setSaleAreaName4(dto.getSaleAreaName4());
                resource.setSaleAreaName5(dto.getSaleAreaName5());
                String saleArea = dto.getSaleAreaName() + dto.getSaleAreaName2() + dto.getSaleAreaName3()
                        + dto.getSaleAreaName4() + dto.getSaleAreaName5();
                resource.setSaleArea(saleArea);

                // 商品数量信息
                resource.setSaleNum(BigDecimal.ZERO);// 初始化货物总销售数量
                resource.setCansaleNum(dto.getSaleNum());
                resource.setLockNum(BigDecimal.ZERO);
                resource.setOrderminNum(goodsQuantityDTO.getOrderminNum());
                resource.setOrdermaxNum(goodsQuantityDTO.getOrdermaxNum());
                resource.setOrderminchangeNum(goodsQuantityDTO.getOrderminchangeNum());
                resource.setDaymaxNum(goodsQuantityDTO.getDaymaxNum());
                resource.setToleranceType(ToleranceTypeEnum.RELATIVE.code());
                resource.setTolerance(goodsQuantityDTO.getRelativeTolerance());

                // 物流价格
                resource.setIfTakeSelf(goodsConsignDTO.getIfTakeSelf());
                resourceAddParams2(goodsBaseDTO, goodsConsignDTO, goodsDTO, dto, resource);
                // 商品交付信息
                StringBuilder payWayStr = new StringBuilder();
                List<String> payWays = goodsConsignDTO.getPayWay();
                setPayWayStr(payWays, payWayStr, resource);

                // 商品其他信息
                resource.setEffectTime(new Date()); // 生效时间，新增立即生效
                resource.setResourceVersion(calculateResourceVersion(resource.getResourceId())); // 资源版本号
                resource.setSellerId(sellerId); // 设置卖家信息
                resource.setSellerName(sellerName);
                resource.setSellerNickName(reqCreateResourceDTO.getSellerNickName());
                resource.setSalesId(reqCreateResourceDTO.getSalesId());
                resource.setSalesName(reqCreateResourceDTO.getSalesName());
                resource.setContactPhone(reqCreateResourceDTO.getContactPhone());
                resource.setOrgId(reqCreateResourceDTO.getOrgId());
                resource.setOrgName(reqCreateResourceDTO.getOrgName());
                // 设置上下架时间
                resource.setIfup(goodsOtherDTO.getIfup());
                resourceSetFixDowntime(goodsOtherDTO, resource);
                // 交易时间、(支付有效期、提货/发货有效期,存到数据库采用毫秒形式)
                resource.setTradeStarttime(goodsOtherDTO.getTradeStarttime());
                resource.setTradeEndtime(goodsOtherDTO.getTradeEndtime());
                resource.setPaydateType(goodsOtherDTO.getPaydateType());
                resourceSetParams4(goodsOtherDTO, resource);

                // 设置商品属性
                String resourceAttributes = "";
                List<String> attributesList = new ArrayList<>();
                // 查询商品属性
                List<GoodsCategoryAttrDTO> attrVals = goodsService.getCategoryAttrByGoodsId(goodsId);
                attributesListAddItems(attrVals, attributesList);
                Collections.sort(attributesList);
                resourceAttributes = getResourceAttributes(attributesList, resourceAttributes);
                resource.setResourceAttributes(resourceAttributes);

                // 区域聚合
                List<ResourceRegion> resourceRegions = new ArrayList<>();
                resourceRegionsAddValues(dto, resourceRegions);
                resourceSetResourceRegions(resourceRegions, resource);

                // 资源存库
                resource.setCreateUser(operator);
                resource.setCreateTime(new Date());
                resource.setUpdateUser(operator);
                resource.setUpdateTime(new Date());
                resource.setDelFlg(false);
                log.info(" >>> resourceMapper-priceWay: {}", dto.getPriceWay());
                resourceMapper.insert(resource);

                // 历史资源存库
                ResourceHistory history = new ResourceHistory();
                BeanUtils.copyProperties(resource, history);
                history.setResourceHistoryId(uuidGenerator.gain());
                resourceHistoryMapper.insert(history);

                // 行政区域存库
                int regionCount = 0;
                List<AdministrativeRegionDTO> administrativeRegions = dto.getAdministrativeRegions();
                checkCount(administrativeRegions, regionCount, saleArea, resource);
                resourceRegionMapperInsert(operator, administrativeRegions, resource);
                // 聚合商品资源
                this.aggregationGoodsResource(spuId, sellerId, operator, resource.getBrand());
                //ES异步创建资源索引
                Resource createResource = resourceMapper.selectByPrimaryKey(resource.getResourceId());
                resourceElasticsearchBiz.asyncEsCreateResource(createResource);
            }
        }
    }

    private static String getResourceAttributes(List<String> attributesList, String resourceAttributes) {
        for (String attributeStr : Optional.ofNullable(attributesList).orElse(Collections.emptyList())) {
            resourceAttributes = resourceAttributes.concat(attributeStr)
                    .concat(MergeAttrPrefixEnum.OUTSIDE.getCode());
        }
        return resourceAttributes;
    }

    private static void resourceSetBrand(GoodsDTO goodsDTO, Resource resource) {
        if (CsStringUtils.isNullOrBlank(goodsDTO.getBrand())) {
            resource.setBrand(NO_BRAND);
        } else {
            resource.setBrand(goodsDTO.getBrand());
        }
    }

    private void resourceRegionMapperInsert(String operator, List<AdministrativeRegionDTO> administrativeRegions, Resource resource) {
        if (CollectionUtils.isEmpty(administrativeRegions)) {
            return;
        }
        for (AdministrativeRegionDTO region : administrativeRegions) {
            if (region.getIsCarriage().intValue() == BooleanEnum.YES.code().intValue()) {
                String streetNameStr = region.getStreetName();
                String[] streetNames = streetNameStr.split("、");
                if (streetNames.length > 0) {
                    for (String streetName : streetNames) {
                        ResourceRegion regionRecord = new ResourceRegion();
                        BeanUtils.copyProperties(region, regionRecord);
                        regionRecord.setStreetName(streetName);
                        regionRecord.setResourceRegionId(uuidGenerator.gain());
                        regionRecord.setResourceId(resource.getResourceId());
                        regionRecord.setResourceVersion(resource.getResourceVersion());
                        regionRecord.setCreateUser(operator);
                        regionRecord.setCreateTime(new Date());
                        regionRecord.setUpdateUser(operator);
                        regionRecord.setUpdateTime(new Date());
                        regionRecord.setDelFlg(false);
                        resourceRegionMapper.insert(regionRecord);
                    }
                }
            }
        }
    }

    private static void checkCount(List<AdministrativeRegionDTO> administrativeRegions, int regionCount, String saleArea, Resource resource) {
        for (AdministrativeRegionDTO region : Optional.ofNullable(administrativeRegions).orElse(Collections.emptyList())) {
            if (region.getIsCarriage().intValue() == BooleanEnum.YES.code().intValue()) {
                regionCount = regionCount + 1;
            }
        }
        if (regionCount <= 0) {
            throw new BizException(ResourceCode.CAN_NOT_CREATE,
                    "该商品在{" + saleArea + "}区域{" + resource.getStoreName() + "}仓库不可配送，请不要启用！");
        }
    }

    private void resourceSetResourceRegions(List<ResourceRegion> resourceRegions, Resource resource) {
        if (CollectionKit.isNotEmpty(resourceRegions)) {
            List<String> regionStrs = mergeGoodsRegion(resourceRegions);
            if (CollectionKit.isNotEmpty(regionStrs)) {
                String resourceRegionsStr = regionStrs.get(0);
                resource.setResourceRegions(resourceRegionsStr);
            }
        }
    }

    private static void resourceRegionsAddValues(GoodsResourceStoreDTO dto, List<ResourceRegion> resourceRegions) {
        if (dto.getAdministrativeRegions() == null && dto.getAdministrativeRegions().size() == 0) {
            return;
        }
        for (AdministrativeRegionDTO region : dto.getAdministrativeRegions()) {
            if (region.getIsCarriage().intValue() == BooleanEnum.YES.code().intValue()) {
                String streetNameStr = region.getStreetName();
                String[] streetNames = streetNameStr.split("、");
                if (streetNames.length > 0) {
                    for (String streetName : streetNames) {
                        ResourceRegion regionRecord = new ResourceRegion();
                        BeanUtils.copyProperties(region, regionRecord);
                        regionRecord.setStreetName(streetName);
                        resourceRegions.add(regionRecord);
                    }
                }
            }
        }
    }

    private static void attributesListAddItems(List<GoodsCategoryAttrDTO> attrVals, List<String> attributesList) {
        for (GoodsCategoryAttrDTO goodsCategoryAttrDTO : Optional.ofNullable(attrVals).orElse(Collections.emptyList())) {
            String attriValueCode = goodsCategoryAttrDTO.getValueCode();
            String goodsAttriId = goodsCategoryAttrDTO.getGoodsAttriId();
            if (CsStringUtils.isNotBlank(attriValueCode)
                    && CsStringUtils.isNotBlank(goodsAttriId)) {
                String attributeStr = goodsAttriId.concat(MergeAttrPrefixEnum.INSTIDE.getCode())
                        .concat(attriValueCode);
                if (!attributesList.contains(attributeStr)) {
                    attributesList.add(attributeStr);
                }
            }

        }
    }

    private static void resourceSetParams4(GoodsOtherDTO goodsOtherDTO, Resource resource) {
        if (!goodsOtherDTO.getPaydateType().equals(PaydateTypeEnum.PAYDATE_TYPE1.code())) {
            resource.setPaydateLimit(Math.round(goodsOtherDTO.getPaydateLimit() * HOUR));
        }
        if (goodsOtherDTO.getTakedateType().equals(TakedateTypeEnum.TAKEDATE_TYPE1.code())) {
            resource.setTakedateType(goodsOtherDTO.getTakedateType());
        } else {
            if (goodsOtherDTO.getTakedateHour() == -1) {
                resource.setTakedateLimit(Math.round(goodsOtherDTO.getTakedateLimit() * DAY));
                resource.setTakedateType(TakedateTypeEnum.TAKEDATE_TYPE2.code());
            } else {
                resource.setTakedateLimit(Math.round(goodsOtherDTO.getTakedateLimit() * DAY)
                        + Math.round(goodsOtherDTO.getTakedateHour() * HOUR));
                resource.setTakedateType(TakedateTypeEnum.TAKEDATE_TYPE3.code());
            }
        }
    }

    private static void resourceSetFixDowntime(GoodsOtherDTO goodsOtherDTO, Resource resource) {
        if (goodsOtherDTO.getIfup()) {
            resource.setUpTime(new Date());
            resource.setFixUptime(new Date());
            resource.setStatus(ResourceStatusEnum.RES_STATUS100.code());
        } else {
            if (goodsOtherDTO.getFixUptime() == null) {
                throw new BizException(ResourceCode.UNKNOWN_ERROR, "定时上架需要选择定时上架时间！");
            }
            resource.setFixUptime(goodsOtherDTO.getFixUptime());
            resource.setStatus(ResourceStatusEnum.RES_STATUS400.code());
        }
        resource.setIfdown(goodsOtherDTO.getIfdown());
        if (!goodsOtherDTO.getIfdown()) {
            if (goodsOtherDTO.getFixDowntime() == null) {
                throw new BizException(ResourceCode.UNKNOWN_ERROR, "定时下架需要选择定时下架时间！");
            }
            resource.setFixDowntime(goodsOtherDTO.getFixDowntime());
        }
    }

    private static void setPayWayStr(List<String> payWays, StringBuilder payWayStr, Resource resource) {
        for (String payWay : Optional.ofNullable(payWays).orElse(Collections.emptyList())) {
            if (payWayStr.length() == 0) {
                payWayStr.append(payWay);
            } else {
                payWayStr.append(",").append(payWay);
            }
        }
        resource.setPayWay(payWayStr.toString());
    }

    private static void resourceAddParams2(GoodsBaseDTO goodsBaseDTO, GoodsConsignDTO goodsConsignDTO, GoodsDTO goodsDTO, GoodsResourceStoreDTO dto, Resource resource) {
        if (CsStringUtils.isNullOrBlank(goodsDTO.getLogistics())
                && goodsConsignDTO.getIfPlatformDelivery()) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR,
                    "该商品{" + goodsBaseDTO.getGoodsName() + "}不可平台配送！");
        }
        if (goodsConsignDTO.getIfPlatformDelivery() && PriceWayEnum.PRICE_TYPE1.code().equals(dto.getPriceWay())) {
            resource.setArrivePrice(null);
            resource.setIfPlatformDelivery(false);
        }
        if (dto.getArrivePrice() == null && PriceWayEnum.PRICE_TYPE1.code().equals(dto.getPriceWay())) {
            resource.setIfPlatformDelivery(false);
            resource.setIfSellerDelivery(false);
        } else {
            resource.setIfPlatformDelivery(goodsConsignDTO.getIfPlatformDelivery());
            resource.setIfSellerDelivery(goodsConsignDTO.getIfSellerDelivery());
        }
        if (goodsConsignDTO.getLogisticsWeight() != null) {
            resource.setLogisticsWeight(goodsConsignDTO.getLogisticsWeight());
        }
        if (goodsConsignDTO.getLogisticsType() != null) {
            resource.setLogisticsType(goodsConsignDTO.getLogisticsType());
        }
        if (goodsConsignDTO.getLogisticsUnit() != null) {
            resource.setLogisticsUnit(goodsConsignDTO.getLogisticsUnit());
        }
        if (goodsConsignDTO.getLogisticsPrice() != null) {
            resource.setLogisticsPrice(goodsConsignDTO.getLogisticsPrice());
        }
        // 搬运价格
        if (CsStringUtils.isNotBlank(goodsConsignDTO.getCartageRuleId())) {
            resource.setCartageRuleId(goodsConsignDTO.getCartageRuleId());
        }
    }

    private static String getSaleAreaRealCode(GoodsResourceStoreDTO dto) {
        String saleAreaRealCode;
        if (CsStringUtils.isNotBlank(dto.getSaleAreaCode5())) {
            saleAreaRealCode = dto.getSaleAreaCode5();
        } else if (CsStringUtils.isNotBlank(dto.getSaleAreaCode4())) {
            saleAreaRealCode = dto.getSaleAreaCode4();
        } else if (CsStringUtils.isNotBlank(dto.getSaleAreaCode3())) {
            saleAreaRealCode = dto.getSaleAreaCode3();
        } else if (CsStringUtils.isNotBlank(dto.getSaleAreaCode2())) {
            saleAreaRealCode = dto.getSaleAreaCode2();
        } else {
            saleAreaRealCode = dto.getSaleAreaCode();
        }
        return saleAreaRealCode;
    }

    private void checkGoodsOtherInfo(GoodsPriceDTO goodsPriceDTO, GoodsQuantityDTO goodsQuantityDTO, GoodsConsignDTO goodsConsignDTO, GoodsOtherDTO goodsOtherDTO, GoodsResourceStoreDTO dto) {
        if (goodsPriceDTO == null) {
            throw new BizException(ResourceCode.PARAM_NULL, "商品定价信息");
        }
        if (goodsConsignDTO == null) {
            throw new BizException(ResourceCode.PARAM_NULL, "商品配送信息");
        }
        if (goodsOtherDTO == null) {
            throw new BizException(ResourceCode.PARAM_NULL, "商品其他信息");
        }
        if (dto == null) {
            throw new BizException(ResourceCode.PARAM_NULL, "商品仓库信息");
        }

        if (goodsPriceDTO.getAreaLevel() == null) {
            throw new BizException(ResourceCode.PARAM_NULL, "定价层级");
        }

        if (dto.getSaleNum() == null) {
            throw new BizException(ResourceCode.PARAM_NULL, "上架数量");
        }
        if (dto.getSaleNum().compareTo(BigDecimal.ZERO) <= 0) {
            throw new BizException(ResourceCode.PARAM_ERROR, "上架数量");
        }
        if (CsStringUtils.isBlank(dto.getStoreId())) {
            throw new BizException(ResourceCode.PARAM_NULL, "仓库编号");
        }
        if (CsStringUtils.isBlank(dto.getStoreType())) {
            throw new BizException(ResourceCode.PARAM_NULL, "仓库类型");
        }
        if (CsStringUtils.isBlank(dto.getStoreName())) {
            throw new BizException(ResourceCode.PARAM_NULL, "仓库名称");
        }
    }

    /**
     * 检查商品基本信息
     *
     * @param goodsBaseDTO
     */
    private void checkGoodsBaseDTO(GoodsBaseDTO goodsBaseDTO) {
        if (goodsBaseDTO == null) {
            throw new BizException(ResourceCode.PARAM_NULL, "商品基本信息");
        }
        if (CsStringUtils.isBlank(goodsBaseDTO.getGoodsId())) {
            throw new BizException(ResourceCode.PARAM_NULL, "商品编号");
        }
        if (CsStringUtils.isBlank(goodsBaseDTO.getGoodsName())) {
            throw new BizException(ResourceCode.PARAM_NULL, "商品名称");
        }
    }

    /**
     * 查询是否挂牌了该资源
     *
     * @param spuId
     * @param goodsId
     * @param storeId
     * @return
     */
    private boolean checkIsCanCreateResource(String spuId, String goodsId, String storeId, String saleAreaRealCode) {
        Example example = new Example(Resource.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(SPU_ID, spuId);
        criteria.andEqualTo(GOODS_ID, goodsId);
        criteria.andEqualTo(STORE_ID, storeId);
        criteria.andEqualTo(SALE_AREA_REAL_CODE, saleAreaRealCode);
        criteria.andEqualTo(DEL_FLG, BooleanEnum.NO.code());
        List<Resource> resources = resourceMapper.selectByExample(example);
        return CollectionKit.isEmpty(resources);
    }

    /**
     * 创建资源参数检查
     *
     * @param reqCreateResourceDTO 入参
     */
    private void createResourceCheck(ReqCreateResourceDTO reqCreateResourceDTO) {
        if (reqCreateResourceDTO == null) {
            throw new BizException(ResourceCode.PARAM_NULL, "入参");
        }
        if (CsStringUtils.isBlank(reqCreateResourceDTO.getSellerId())) {
            throw new BizException(ResourceCode.PARAM_NULL, "卖家编号");
        }
        if (CsStringUtils.isBlank(reqCreateResourceDTO.getSpuId())) {
            throw new BizException(ResourceCode.PARAM_NULL, "SPU编号");
        }
        if (CsStringUtils.isBlank(reqCreateResourceDTO.getOperator())) {
            throw new BizException(ResourceCode.PARAM_NULL, "操作人");
        }
        if (CsStringUtils.isBlank(reqCreateResourceDTO.getUnit())) {
            throw new BizException(ResourceCode.PARAM_NULL, "单位");
        }
        if (reqCreateResourceDTO.getGoodsBaseDTOs() == null || reqCreateResourceDTO.getGoodsBaseDTOs().size() == 0) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, "未选择商品");
        }
    }

    @Override
    public Boolean ifCanSelectAreaPrice(String sellerId, String goodsId, Integer level) {
        // 参数检查
        if (CsStringUtils.isBlank(goodsId)) {
            throw new BizException(ResourceCode.PARAM_NULL, "商品编号");
        }
        if (level == null || level < 0) {
            throw new BizException(ResourceCode.PARAM_NULL, "销售区域层级");
        }
        // 初始化结果
        Boolean ifCanSelectAreaPrice = false;
        try {
            // 查询资源
            Example example = new Example(Resource.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo(GOODS_ID, goodsId);
            criteria.andNotEqualTo("areaLevel", level);
            criteria.andEqualTo(DEL_FLG, BooleanEnum.NO.code());
            List<Resource> resources = resourceMapper.selectByExample(example);
            if (null == resources || resources.size() == 0) {
                ifCanSelectAreaPrice = true;
            }
        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("ifCanSelectAreaPrice Exception Message: {}", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
        return ifCanSelectAreaPrice;
    }

    @PrintArgs
    @Transactional
    @Override
    public void updateResource(ReqUpdateResourceDTO req) {
        // 参数检查
        checkParams(req);
        try {
            // 查询老数据
            String operator = req.getOperator();
            String resourceId = req.getResourceId();
            Boolean ifEffectNow = req.getIfEffectNow();
            Resource resource = resourceMapper.selectByPrimaryKey(resourceId);
            if (ifEffectNow) { // 立即生效
                if (Objects.isNull(resource)) {
                    throw new BizException(ResourceCode.CAN_NOT_UPDATE, "不能修改已下架的资源！");
                }
                if ((resource.getStatus().equals(ResourceStatusEnum.RES_STATUS100.code())
                        || resource.getStatus().equals(ResourceStatusEnum.RES_STATUS300.code()))) {
                    throw new BizException(ResourceCode.CAN_NOT_UPDATE, "不能修改已上架、下架处理中状态的资源！");
                }
                // 修改资源
                resource.setEffectTime(new Date());
                resource.setUpdateUser(operator);
                resource.setUpdateTime(new Date());
                resource.setResourceVersion(calculateResourceVersion(resourceId));
                resource.setApprovalMessage(req.getApprovalMessage());
                // 设置上下架时间
                resourceSetTime(req, resource);

                //交易时间
                resource.setPaydateType(req.getPaydateType());
                resourceSetTakedateLimit(req, resource);

                //上架数量
                resource.setSaleNum(resource.getSaleNum());
                log.info("setCansaleNum:{}", req.getCansaleNum());
                resource.setCansaleNum(req.getCansaleNum());
                resource.setOrderminNum(req.getOrderminNum());
                resource.setOrdermaxNum(req.getOrdermaxNum());
                resource.setOrderminchangeNum(req.getOrderminchangeNum());
                resource.setDaymaxNum(req.getDaymaxNum());
                resource.setToleranceType(ToleranceTypeEnum.RELATIVE.code());
                resource.setTolerance(req.getTolerance());

                // 商品支付信息
                StringBuilder payWayStr = new StringBuilder();
                List<String> payWays = req.getPayWay();
                payWaysAddPayWay(payWays, payWayStr);
                resource.setPayWay(payWayStr.toString());

                // 设置商品属性
                String resourceAttributes = "";
                List<String> attributesList = new ArrayList<>();
                // 查询商品属性
                List<GoodsCategoryAttrDTO> attrVals = goodsService.getCategoryAttrByGoodsId(resource.getGoodsId());
                attributesListAddItem(attrVals, attributesList);
                Collections.sort(attributesList);
                for (String attributeStr : Optional.ofNullable(attributesList).orElse(Collections.emptyList())) {
                    resourceAttributes = resourceAttributes.concat(attributeStr)
                            .concat(MergeAttrPrefixEnum.OUTSIDE.getCode());
                }
                resource.setResourceAttributes(resourceAttributes);

                //价格属性
                resource.setIfProtocolPrice(req.getIfProtocolPrice());
                resource.setPriceDescribe(req.getPriceDescribe());
                resource.setPriceWay(req.getPriceWay()); // 定价模式
                resource.setFactoryPrice(req.getFactoryPrice());
                resource.setArrivePrice(req.getArrivePrice());
                resource.setPrice(req.getFactoryPrice());
                resourceAddValue1(req, resource);
                //搬运费规则
                resource.setMachineRuleId(req.getMachineRuleId());
                resource.setEmptyLoadRuleId(req.getEmptyLoadRuleId());
                resource.setTradeStarttime(req.getTradeStarttime());
                resource.setTradeEndtime(req.getTradeEndtime());
                resourceMapper.updateByPrimaryKey(resource);

                // 添加历史资源记录
                ResourceHistory historyResource = new ResourceHistory();
                BeanUtils.copyProperties(resource, historyResource);
                historyResource.setResourceHistoryId(uuidGenerator.gain());
                resourceHistoryMapper.insert(historyResource);
                // 聚合商品资源
                this.aggregationGoodsResource(resource.getGoodsId(), resource.getSellerId(), operator, resource.getBrand());
                //异步更新资源ES
                resource = resourceMapper.selectByPrimaryKey(resourceId);
                resourceElasticsearchBiz.asyncEsUpdateResource(resource);
            } else if (!ifEffectNow) { // 定时生效
                BeanUtils.copyProperties(req, resource);
                resource.setSaleNum(resource.getSaleNum());
                resource.setCansaleNum(req.getCansaleNum());
                resource.setEffectTime(req.getEffectTime());
                resource.setResourceVersion(calculateResourceVersion(resourceId));
                // 插入未生效资源检查
                checkUneffectiveResource(resourceId);
                // 插入未生效资源表
                ResourceUneffective uneffective = new ResourceUneffective();
                BeanUtils.copyProperties(resource, uneffective);
                uneffective.setEffectiveId(uuidGenerator.gain());
                uneffective.setDelFlg(false);
                uneffective.setCreateUser(operator);
                uneffective.setCreateTime(new Date());
                uneffective.setUpdateTime(new Date());
                uneffective.setUpdateUser(operator);
                resourceUneffectiveMapper.insert(uneffective);
            }
        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("updateResource Exception Message: {}", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
    }

    private static void resourceAddValue1(ReqUpdateResourceDTO req, Resource resource) {
        if (req.getIfPlatformDelivery() && PriceWayEnum.PRICE_TYPE1.code().equals(req.getPriceWay())) {
            resource.setArrivePrice(null);
            resource.setIfPlatformDelivery(false);
        }
        if (req.getArrivePrice() == null && PriceWayEnum.PRICE_TYPE1.code().equals(req.getPriceWay())) {
            resource.setIfPlatformDelivery(false);
            resource.setIfSellerDelivery(false);
        } else {
            resource.setIfPlatformDelivery(req.getIfPlatformDelivery());
            resource.setIfSellerDelivery(req.getIfSellerDelivery());
        }
        if (PriceWayEnum.PRICE_TYPE2.code().equals(req.getPriceWay())) {
            resource.setArrivePrice(null);
        }
    }

    private static void attributesListAddItem(List<GoodsCategoryAttrDTO> attrVals, List<String> attributesList) {
        for (GoodsCategoryAttrDTO goodsCategoryAttrDTO : Optional.ofNullable(attrVals).orElse(Collections.emptyList())) {
            String goodsAttriId = goodsCategoryAttrDTO.getGoodsAttriId();
            String attriValueCode = goodsCategoryAttrDTO.getValueCode();
            if (CsStringUtils.isNotBlank(attriValueCode)
                    && CsStringUtils.isNotBlank(goodsAttriId)) {
                String attributeStr = goodsAttriId.concat(MergeAttrPrefixEnum.INSTIDE.getCode())
                        .concat(attriValueCode);
                if (!attributesList.contains(attributeStr)) {
                    attributesList.add(attributeStr);
                }
            }

        }
    }

    private static void payWaysAddPayWay(List<String> payWays, StringBuilder payWayStr) {
        for (String payWay : Optional.ofNullable(payWays).orElse(Collections.emptyList())) {
            if (payWayStr.length() == 0) {
                payWayStr.append(payWay);
            } else {
                payWayStr.append(",").append(payWay);
            }
        }
    }

    private static void resourceSetTakedateLimit(ReqUpdateResourceDTO req, Resource resource) {
        if (!req.getPaydateType().equals(PaydateTypeEnum.PAYDATE_TYPE1.code())) {
            resource.setPaydateLimit(Math.round(req.getPaydateLimit() * HOUR));
        }
        if (req.getTakedateType().equals(TakedateTypeEnum.TAKEDATE_TYPE1.code())) {
            resource.setTakedateType(req.getTakedateType());
        } else {
            if (req.getTakedateHour() == null || req.getTakedateHour() == -1) {
                resource.setTakedateType(TakedateTypeEnum.TAKEDATE_TYPE2.code());
                resource.setTakedateLimit(Math.round(req.getTakedateLimit() * DAY));
            } else {
                resource.setTakedateType(TakedateTypeEnum.TAKEDATE_TYPE3.code());
                resource.setTakedateLimit(
                        Math.round(req.getTakedateLimit() * DAY) + Math.round(req.getTakedateHour() * HOUR));
            }
        }
    }

    private static void resourceSetTime(ReqUpdateResourceDTO req, Resource resource) {
        resource.setIfup(req.getIfup());
        if (req.getIfup()) {
            resource.setUpTime(new Date());
            resource.setFixUptime(new Date());
            resource.setStatus(ResourceStatusEnum.RES_STATUS100.code());
        } else {
            if (req.getFixUptime() == null) {
                throw new BizException(ResourceCode.UNKNOWN_ERROR, "定时上架需要选择定时上架时间！");
            }
            resource.setFixUptime(req.getFixUptime());
            resource.setStatus(ResourceStatusEnum.RES_STATUS400.code());
        }
        resource.setIfdown(req.getIfdown());
        if (!req.getIfdown()) {
            if (req.getFixDowntime() == null) {
                throw new BizException(ResourceCode.UNKNOWN_ERROR, "定时下架需要选择定时下架时间！");
            }
            resource.setFixDowntime(req.getFixDowntime());
        }
    }

    private static void checkParams(ReqUpdateResourceDTO req) {
        if (CsStringUtils.isBlank(req.getOperator())) {
            throw new BizException(ResourceCode.PARAM_NULL, "操作人");
        }
        if (CsStringUtils.isBlank(req.getResourceId())) {
            throw new BizException(ResourceCode.PARAM_NULL, "资源编号");
        }
        if (req.getIfEffectNow() == null) {
            throw new BizException(ResourceCode.PARAM_NULL, "是否生效");
        }
    }

    @Override
    public ResourceDTO getResourceDetail(String resourceId) {
        // 参数检查
        if (CsStringUtils.isBlank(resourceId)) {
            throw new BizException(ResourceCode.PARAM_NULL, "资源编号");
        }
        // 初始化操作
        ResourceDTO resourceDTO = new ResourceDTO();
        try {
            if (CsStringUtils.isBlank(resourceId)) {
                return null;
            }
            Resource resource = resourceMapper.selectByPrimaryKey(resourceId);
            if (resource != null) {
                BeanUtils.copyProperties(resource, resourceDTO);
                // 查询商品聚合信息
                GoodsDTO goodsInfo = getGoodsInfo(resource.getGoodsId());
                resourceDTO.setGoodsDTO(goodsInfo);
                // 处理其他信息
                List<String> payWays = new ArrayList<>();
                String[] payWayArr = resource.getPayWay().split(",");
                if (payWayArr.length > 0) {
                    payWays.addAll(Arrays.asList(payWayArr));
                }
                resourceDTO.setPayWay(payWays);

                if (resource.getPaydateLimit() != null) {
                    Long paydateLimit = resource.getPaydateLimit() / HOUR.longValue();
                    resourceDTO.setPaydateLimit(paydateLimit);
                }

                String takedateType = resource.getTakedateType();
                resourceDTOSetHour(takedateType, resourceDTO, resource);
                // 设置交易状态
                boolean tradeStatus = checkResourceTradeStatus(resource);
                if (tradeStatus) {
                    resourceDTO.setTradeStatus(TradeStatusEnum.TRADE_STATUS100.code());
                } else {
                    resourceDTO.setTradeStatus(TradeStatusEnum.TRADE_STATUS200.code());
                }
            }
        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("getResourceDetail Exception Message", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
        return resourceDTO;
    }

    private static void resourceDTOSetHour(String takedateType, ResourceDTO resourceDTO, Resource resource) {
        if (takedateType.equals(TakedateTypeEnum.TAKEDATE_TYPE1.code())) {
            resourceDTO.setTakedateType(takedateType);
        } else if (takedateType.equals(TakedateTypeEnum.TAKEDATE_TYPE2.code())) {
            resourceDTO.setTakedateType(takedateType);
            long takedateLimit = resource.getTakedateLimit().longValue() / DAY.longValue();
            resourceDTO.setTakedateLimit(takedateLimit);
            resourceDTO.setTakedateHour(null);
        } else if (takedateType.equals(TakedateTypeEnum.TAKEDATE_TYPE3.code())) {
            resourceDTO.setTakedateType(TakedateTypeEnum.TAKEDATE_TYPE2.code());
            long takedateLimit = resource.getTakedateLimit() / DAY.longValue();
            int takedateHourTmp = (int) (resource.getTakedateLimit() % DAY.longValue() / HOUR.longValue());
            // 根据前端选择规则，小时不会为空 TakedateTypeEnum.TAKEDATE_TYPE3
            resourceDTO.setTakedateLimit(takedateHourTmp != 0 ? takedateLimit : takedateLimit - 1);
            resourceDTO.setTakedateHour(takedateHourTmp != 0 ? takedateHourTmp : 24);
        }
    }

    @Override
    public List<PriceModeDTO> getPriceMode(String sellerId) {
        // 参数检查
        if (CsStringUtils.isBlank(sellerId)) {
            throw new BizException(ResourceCode.PARAM_NULL, "卖家编号");
        }
        // 初始化结果
        List<PriceModeDTO> list = new ArrayList<>();
        try {
            if (CsStringUtils.isBlank(sellerId)) {
                return null;
            }
            List<SaleLevelDTO> saleLevelByMemberId = findSaleLevelByMemberId(sellerId);
            for (SaleLevelDTO levelDTO : Optional.ofNullable(saleLevelByMemberId).orElse(Collections.emptyList())) {
                PriceModeDTO dto1 = new PriceModeDTO();
                dto1.setModeCode(levelDTO.getLevel());
                dto1.setModeName(levelDTO.getName());
                list.add(dto1);
            }
        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("getPriceMode Exception Message: {}", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
        return list;
    }

    @Transactional
    @Override
    public void deleteResource(List<String> resourceIds, String operatorId) {
        // 参数检查
        checkParams2(resourceIds, operatorId);
        try {
            for (String resourceId : resourceIds) {
                Resource resource = resourceMapper.selectByPrimaryKey(resourceId);
                checkParams3(resource);
                resourceElasticsearchBizAsyncEsDeleteResource(operatorId, resourceId, resource);
            }

        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("deleteResource Exception Message: {}", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
    }

    private void resourceElasticsearchBizAsyncEsDeleteResource(String operatorId, String resourceId, Resource resource) {
        if (resource != null) {
            if (!(resource.getStatus().equals(ResourceStatusEnum.RES_STATUS100.code())
                    || resource.getStatus().equals(ResourceStatusEnum.RES_STATUS300.code()))) {
                // 逻辑删除
                resource.setDelFlg(true);
                resource.setUpdateTime(new Date());
                resource.setUpdateUser(operatorId);
                resource.setApprovalMessage("删除资源");
                resourceMapper.updateByPrimaryKeySelective(resource);
                // 删除资源销售区域
                Example regionExample = new Example(ResourceRegion.class);
                Example.Criteria regionExampleCriteria = regionExample.createCriteria();
                regionExampleCriteria.andEqualTo(RESOURCE_ID, resourceId);
                regionExampleCriteria.andEqualTo(DEL_FLG, BooleanEnum.NO.code());
                List<ResourceRegion> regions = resourceRegionMapper.selectByExample(regionExample);
                for (ResourceRegion region : Optional.ofNullable(regions).orElse(Collections.emptyList())) {
                    region.setUpdateUser(operatorId);
                    region.setUpdateTime(new Date());
                    region.setDelFlg(true);
                    resourceRegionMapper.updateByPrimaryKey(region);
                }
                // 添加历史资源记录
                ResourceHistory history = new ResourceHistory();
                BeanUtils.copyProperties(resource, history);
                history.setResourceHistoryId(uuidGenerator.gain());
                resourceHistoryMapper.insert(history);
                // 聚合商品资源
                this.aggregationGoodsResource(resource.getGoodsId(), resource.getSellerId(), operatorId, resource.getBrand());
                //删除资源ES
                resourceElasticsearchBiz.asyncEsDeleteResource(resourceId);
            }
        }
    }

    private static void checkParams3(Resource resource) {
        if (Objects.isNull(resource)) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, "已上架商品为空");
        }
        if (resource.getStatus().equals(ResourceStatusEnum.RES_STATUS100.code())) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, "已上架商品【" + resource.getResourceCode() + "】不能删除");
        }
        if (resource.getStatus().equals(ResourceStatusEnum.RES_STATUS300.code())) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, "下架处理中商品【" + resource.getResourceCode() + "】不能删除");
        }
    }

    private static void checkParams2(List<String> resourceIds, String operatorId) {
        if (resourceIds == null || resourceIds.size() == 0) {
            throw new BizException(ResourceCode.PARAM_NULL, RESOURCE_CODE_LIST);
        }
        if (CsStringUtils.isBlank(operatorId)) {
            throw new BizException(ResourceCode.PARAM_NULL, "操作人");
        }
    }

    @Override
    public PageInfo<GoodsResourceListDTO> searchGoodsResourceBuyer(ReqGoodsResourceDTO req) {
        if (req.getPageNum() == null) {
            throw new BizException(ResourceCode.PARAM_NULL, "页码");
        }
        if (req.getPageNum() <= 0) {
            throw new BizException(ResourceCode.PARAM_ERROR, PAGE_NUM_BEGIN_FROM_1);
        }
        if (req.getPageSize() == null) {
            throw new BizException(ResourceCode.PARAM_NULL, "页面大小");
        }
        if (req.getPageNum() <= 0) {
            throw new BizException(ResourceCode.PARAM_ERROR, PAGE_SIZE_MUST_MORE_THAN_0);
        }
        // 初始化结果
        PageInfo<GoodsResourceListDTO> pageData = new PageInfo<>();
        try {
            log.info("======searchResourceBuyer--req:{}", JSON.toJSON(req));
            long start = System.currentTimeMillis();
            pageData = resourceElasticsearchBiz.esSearchResource(req);
            long end = System.currentTimeMillis();
            long spend = end - start;
            log.info("======searchResourceBuyer-->spend time {} ms", spend);
        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("searchResourceBuyer Exception Message: {}", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
        return pageData;
    }

    @Override
    public GoodsResourceDTO searchGoodsResourceDetailBuyer(String req) {
        // 参数检查
        if (CsStringUtils.isBlank(req)) {
            throw new BizException(ResourceCode.PARAM_NULL, "资源商品编号");
        }
        GoodsResourceDTO goodsResourceDTO = new GoodsResourceDTO();
        try {
            if (CsStringUtils.isNullOrBlank(req)) {
                return null;
            }
            GoodsResource goodsResource = goodsResourceMapper.selectByPrimaryKey(req);
            if (goodsResource != null) {
                BeanUtils.copyProperties(goodsResource, goodsResourceDTO);
            }
            // TODO
        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("searchGoodsResourceDetailBuyer Exception Message: {}", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
        return goodsResourceDTO;
    }

    @Override
    public ResourceDetailDTO searchResourceDetailBuyer(ReqResourceDetailDTO req) {
        // 参数检查
        if (CsStringUtils.isBlank(req.getSellerId())) {
            throw new BizException(ResourceCode.PARAM_NULL, "卖家编号");
        }
        if (CsStringUtils.isBlank(req.getGoodsId())) {
            throw new BizException(ResourceCode.PARAM_NULL, "商品编号");
        }
        if (CsStringUtils.isBlank(req.getUnit())) {
            throw new BizException(ResourceCode.PARAM_NULL, "单位");
        }
        if (req.getBuyNum() == null) {
            throw new BizException(ResourceCode.PARAM_NULL, "购买数量");
        }
        ResourceDetailDTO resourceDTO = new ResourceDetailDTO();
        resourceDTO.setIsHaveGoods(BooleanEnum.NO.code());
        BigDecimal cansaleNumTmp;
        BigDecimal price;
        return resourceDTO;
    }

    @Override
    public List<ChangePriceAndNumDTO> getChangePriceAndNumList(List<String> resourceIds, String operator) {
        // 参数检查
        if (resourceIds == null || resourceIds.size() == 0) {
            throw new BizException(ResourceCode.PARAM_NULL, RESOURCE_CODE_LIST);
        }
        if (CsStringUtils.isBlank(operator)) {
            throw new BizException(ResourceCode.PARAM_NULL, "操作人");
        }
        // 初始化出参
        List<ChangePriceAndNumDTO> list = new ArrayList<>();
        try {
            Example example = new Example(Resource.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo(DEL_FLG, BooleanEnum.NO.code());
            if (CsStringUtils.isNotBlank(operator)) {
                criteria.andEqualTo(SELLER_ID, operator);
            }
            if (resourceIds.size() > 0) {
                criteria.andIn(RESOURCE_ID, resourceIds);
            }
            List<Resource> resources = resourceMapper.selectByExample(example);
            for (Resource resource : Optional.ofNullable(resources).orElse(Collections.emptyList())) {
                ChangePriceAndNumDTO dto = new ChangePriceAndNumDTO();
                BeanUtils.copyProperties(resource, dto);
                dto.setPriceUnit(resource.getPriceUnit());
                dto.setGoodsDescribe(resource.getResourceName());
                dto.setFactoryPrice(resource.getFactoryPrice());
                dto.setArrivePrice(resource.getArrivePrice());
                dto.setUpFactoryPrice(resource.getFactoryPrice());
                dto.setUpArrivePrice(resource.getArrivePrice());

                dto.setUptCansaleNum(resource.getCansaleNum());
                list.add(dto);
            }
        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("getChangePriceAndNumList Exception Message: {}", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
        return list;
    }

    @Override
    public List<ChangeHistoryDTO> getChangeHistoryList(String resourceId, String operator) {
        // 参数检查
        checkParams(resourceId, operator);
        // 初始化出参
        List<ChangeHistoryDTO> list = new ArrayList<>();
        try {
            Example example = new Example(ResourceHistory.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo(RESOURCE_ID, resourceId);
            criteria.andEqualTo(SELLER_ID, operator);
            example.orderBy("createTime").asc();
            List<ResourceHistory> histories = resourceHistoryMapper.selectByExample(example);
            if (histories == null) {
                histories = new ArrayList<>();
            }
            ResourceUneffective uneffective = this.getResourceUneffective(resourceId);
            if (uneffective != null) {
                ResourceHistory history = new ResourceHistory();
                BeanUtils.copyProperties(uneffective, history);
                history.setResourceHistoryId(uneffective.getEffectiveId());
                histories.add(history);
            }

            // 上一次记录价格、数量
            BigDecimal lastFactoryPrice = BigDecimal.ZERO;
            BigDecimal lastArrivePrice = BigDecimal.ZERO;
            BigDecimal lastNum = BigDecimal.ZERO;
            listAddItems(histories, lastNum, lastFactoryPrice, lastArrivePrice, list);
            // 顺序反转
            Collections.reverse(list);

        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("getChangeHistoryList Exception Message: {}", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
        return list;
    }

    private static void listAddItems(List<ResourceHistory> histories, BigDecimal lastNum, BigDecimal lastFactoryPrice, BigDecimal lastArrivePrice, List<ChangeHistoryDTO> list) {
        for (ResourceHistory history : Optional.ofNullable(histories).orElse(Collections.emptyList())) {
            ChangeHistoryDTO dto = new ChangeHistoryDTO();
            dto.setChangeNum(BigDecimal.ZERO);
            dto.setChangeArrivePrice(BigDecimal.ZERO);
            dto.setChangeFactoryPrice(BigDecimal.ZERO);
            dto.setApprovalMessage(history.getApprovalMessage());
            BeanUtils.copyProperties(history, dto);
            dto.setPriceUnit(history.getPriceUnit());
            dto.setUpdateTime(dto.getCreateTime());
            lastNum = getLastNum(history, lastNum, dto);
            dto.setFactoryPrice(history.getFactoryPrice() == null ? BigDecimal.ZERO : history.getFactoryPrice());
            dto.setArrivePrice(history.getArrivePrice() == null ? BigDecimal.ZERO : history.getArrivePrice());
            lastFactoryPrice = getLastFactoryPrice(dto, lastFactoryPrice, lastArrivePrice);
            lastArrivePrice = dto.getArrivePrice();

            if (dto.getChangeNumType() != null || dto.getChangePriceType() != null) {
                list.add(dto);
            }
        }
    }

    private static BigDecimal getLastNum(ResourceHistory history, BigDecimal lastNum, ChangeHistoryDTO dto) {
        if (history.getCansaleNum() != null && !history.getCansaleNum().equals(lastNum)) {
            if (dto.getCansaleNum().compareTo(lastNum) > 0) { // 加量
                dto.setChangeNum(dto.getCansaleNum().subtract(lastNum));
                dto.setChangeNumType(BooleanEnum.YES.code());
            } else if (dto.getCansaleNum().compareTo(lastNum) < 0) { // 降量
                dto.setChangeNum(lastNum.subtract(dto.getCansaleNum()));
                dto.setChangeNumType(BooleanEnum.NO.code());
            }
            lastNum = history.getCansaleNum();
        }
        return lastNum;
    }

    private static BigDecimal getLastFactoryPrice(ChangeHistoryDTO dto, BigDecimal lastFactoryPrice, BigDecimal lastArrivePrice) {
        // 出厂价
        if (dto.getFactoryPrice().compareTo(lastFactoryPrice) > 0) { // 加价
            dto.setChangeFactoryPrice(dto.getFactoryPrice().subtract(lastFactoryPrice));
            dto.setChangePriceType(BooleanEnum.YES.code());
        } else if (dto.getFactoryPrice().compareTo(lastFactoryPrice) < 0) { // 降价
            dto.setChangeFactoryPrice(lastFactoryPrice.subtract(dto.getFactoryPrice()));
            dto.setChangePriceType(BooleanEnum.NO.code());
        }
        lastFactoryPrice = dto.getFactoryPrice();
        // 到位价
        if (dto.getArrivePrice().compareTo(lastArrivePrice) > 0) { // 加价
            dto.setChangeArrivePrice(dto.getArrivePrice().subtract(lastArrivePrice));
            dto.setChangePriceType(BooleanEnum.YES.code());
        } else if (dto.getArrivePrice().compareTo(lastArrivePrice) < 0) { // 降价
            dto.setChangeArrivePrice(lastArrivePrice.subtract(dto.getArrivePrice()));
            dto.setChangePriceType(BooleanEnum.NO.code());
        }
        return lastFactoryPrice;
    }

    private static void checkParams(String resourceId, String operator) {
        if (CsStringUtils.isBlank(resourceId)) {
            throw new BizException(ResourceCode.PARAM_NULL, "资源编号");
        }
        if (CsStringUtils.isBlank(operator)) {
            throw new BizException(ResourceCode.PARAM_NULL, "操作人");
        }
    }

    private ResourceUneffective getResourceUneffective(String resourceId) {
        Example example = new Example(ResourceUneffective.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(RESOURCE_ID, resourceId);
        criteria.andEqualTo(DEL_FLG, BooleanEnum.NO.code());
        example.orderBy("effectTime").desc();
        List<ResourceUneffective> uneffectives = resourceUneffectiveMapper.selectByExample(example);
        if (uneffectives != null && !uneffectives.isEmpty()) {
            return uneffectives.get(0);
        }
        return null;
    }

    @Transactional
    @Override
    public void changePriceAndNum(ReqChangePriceAndNumDTO reqChangePriceAndNumDTO, String operator) {
        // 参数检查
        checkParams2(reqChangePriceAndNumDTO, operator);
        try {
            List<ChangePriceAndNumDTO> list = reqChangePriceAndNumDTO.getList();
            long changeNum = 0;
            if (!CollectionUtils.isEmpty(list)) {
                changeNum = list.stream()
                        .filter(dto -> dto.getCansaleNum().compareTo(dto.getUptCansaleNum()) != 0 ||
                                (dto.getArrivePrice() != null && dto.getArrivePrice().compareTo(dto.getUpArrivePrice()) != 0) ||
                                (dto.getFactoryPrice() != null && dto.getFactoryPrice().compareTo(dto.getUpFactoryPrice()) != 0))
                        .count();
            }
            if (changeNum == 0) {
                throw new BizException(ResourceCode.NOT_CHANGE, "你未做任何商品数量或价格的变更，请确认后再提交!");
            }
            for (ChangePriceAndNumDTO dto : list) {
                String resourceId = dto.getResourceId();
                Resource resource = resourceMapper.selectByPrimaryKey(resourceId);
                Integer isEffect = reqChangePriceAndNumDTO.getIsEffect();
                if (resource == null) {
                    continue;
                }
                if (isEffect == 1) { // 立即生效
                    dealIseffect(reqChangePriceAndNumDTO, operator, dto, resource, resourceId);
                } else if (isEffect == 0) { // 定时生效
                    resourceUneffectiveMapperInsert(reqChangePriceAndNumDTO, operator, dto, resource, resourceId);
                }
            }
        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("changePriceAndNum Exception Message: {}", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
    }

    private void resourceUneffectiveMapperInsert(ReqChangePriceAndNumDTO reqChangePriceAndNumDTO, String operator, ChangePriceAndNumDTO dto, Resource resource, String resourceId) {
        // 修改资源
        resource.setApprovalMessage(reqChangePriceAndNumDTO.getApprovalMessage());
        resource.setPrice(dto.getUpFactoryPrice());
        resource.setFactoryPrice(dto.getUpFactoryPrice());
        resource.setArrivePrice(dto.getUpArrivePrice());
        resource.setSaleNum(resource.getSaleNum());
        resource.setCansaleNum(dto.getUptCansaleNum());
        resource.setEffectTime(reqChangePriceAndNumDTO.getEffectTime());
        resource.setResourceVersion(calculateResourceVersion(resourceId));
        // 检查是否有未生效资源
        Example effectExample = new Example(ResourceUneffective.class);
        Example.Criteria effectExampleCriteria = effectExample.createCriteria();
        effectExampleCriteria.andEqualTo(DEL_FLG, BooleanEnum.NO.code());
        effectExampleCriteria.andEqualTo(RESOURCE_ID, resourceId);
        List<ResourceUneffective> uneffectives = resourceUneffectiveMapper
                .selectByExample(effectExample);
        if (uneffectives != null && uneffectives.size() > 0) {
            ResourceUneffective uneffective = uneffectives.get(0);
            BeanUtils.copyProperties(resource, uneffective);
            uneffective.setFactoryPrice(dto.getUpFactoryPrice());
            uneffective.setArrivePrice(dto.getUpArrivePrice());
            uneffective.setUpdateUser(operator);
            uneffective.setUpdateTime(new Date());
            resourceUneffectiveMapper.updateByPrimaryKey(uneffective);
        } else {
            // 插入未生效资源表
            ResourceUneffective uneffective = new ResourceUneffective();
            BeanUtils.copyProperties(resource, uneffective);
            uneffective.setEffectiveId(uuidGenerator.gain());
            uneffective.setFactoryPrice(dto.getUpFactoryPrice());
            uneffective.setArrivePrice(dto.getUpArrivePrice());
            uneffective.setDelFlg(false);
            uneffective.setCreateUser(operator);
            uneffective.setCreateTime(new Date());
            resourceUneffectiveMapper.insert(uneffective);
        }
    }

    private void dealIseffect(ReqChangePriceAndNumDTO reqChangePriceAndNumDTO, String operator, ChangePriceAndNumDTO dto, Resource resource, String resourceId) {
        if (!resource.getStatus().equals(ResourceStatusEnum.RES_STATUS100.code())
                && !resource.getStatus().equals(ResourceStatusEnum.RES_STATUS300.code())) {
            // 修改资源
            resource.setApprovalMessage(reqChangePriceAndNumDTO.getApprovalMessage());
            resource.setPrice(dto.getUpFactoryPrice());
            resource.setFactoryPrice(dto.getUpFactoryPrice());
            resource.setArrivePrice(dto.getUpArrivePrice());
            if (PriceWayEnum.PRICE_TYPE2.code().equals(dto.getPriceWay())) {
                resource.setArrivePrice(null);
            }
            resource.setSaleNum(resource.getSaleNum());
            resource.setCansaleNum(dto.getUptCansaleNum());
            resource.setEffectTime(new Date());
            resource.setUpdateTime(new Date());
            resource.setUpdateUser(operator);
            resource.setResourceVersion(calculateResourceVersion(resourceId));
            resourceMapper.updateByPrimaryKey(resource);
            // 添加历史资源记录
            ResourceHistory history = new ResourceHistory();
            BeanUtils.copyProperties(resource, history);
            history.setFactoryPrice(dto.getUpFactoryPrice());
            history.setArrivePrice(dto.getUpArrivePrice());
            history.setResourceHistoryId(uuidGenerator.gain());
            resourceHistoryMapper.insert(history);
            // 聚合商品资源
            this.aggregationGoodsResource(resource.getGoodsId(), resource.getSellerId(), operator, resource.getBrand());
            //异步修改资源ES
            resource = resourceMapper.selectByPrimaryKey(resourceId);
            resourceElasticsearchBiz.asyncEsUpdateResource(resource);
        } else {
            log.error("changePriceAndNum Exception Message: {}", "已上架挂牌商品不能立即生效");
            throw new BizException(ResourceCode.CAN_NOT_UPDATE, "已上架挂牌商品不能立即生效");
        }
    }

    private static void checkParams2(ReqChangePriceAndNumDTO reqChangePriceAndNumDTO, String operator) {
        if (reqChangePriceAndNumDTO.getIsEffect() == null) {
            throw new BizException(ResourceCode.PARAM_NULL, "是否立即生效");
        }
        if (CsStringUtils.isBlank(operator)) {
            throw new BizException(ResourceCode.PARAM_NULL, "操作人");
        }
    }

    @Transactional
    @Override
    public void repealResourceUpdate(String resourceHistoryId, String operator) {
        // 参数检查
        if (CsStringUtils.isBlank(resourceHistoryId)) {
            throw new BizException(ResourceCode.PARAM_NULL, "资源历史编号");
        }
        if (CsStringUtils.isBlank(operator)) {
            throw new BizException(ResourceCode.PARAM_NULL, "操作人");
        }
        try {
            ResourceUneffective uneffective = resourceUneffectiveMapper.selectByPrimaryKey(resourceHistoryId);
            if (uneffective != null) {
                uneffective.setUpdateTime(new Date());
                uneffective.setUpdateUser(operator);
                uneffective.setDelFlg(true);
                resourceUneffectiveMapper.updateByPrimaryKeySelective(uneffective);
            }
        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("repealResourceUpdate Exception Message: {}", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
    }

    @Override
    public void offSaleResourceBatch(List<String> resourceIds, String operator) {
        // 参数检查
        checkOffsale(resourceIds, operator);
        if (resourceIds.isEmpty()) {
            return;
        }
        try {
            for (String resourceId : resourceIds) {
                Resource resource = resourceMapper.selectByPrimaryKey(resourceId);
                if (resource != null) {
                    if (!resource.getStatus().equals(ResourceStatusEnum.RES_STATUS100.code())) {
                        throw new BizException(ResourceCode.UNKNOWN_ERROR, "商品【" + resource.getResourceCode() + "】不能下架，只能下架已上架状态商品");
                    }
                    asyncEsDeleteResource(operator, resourceId, resource);
                }
            }
        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("offSaleResourceBatch Exception Message: {}", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
    }

    private void asyncEsDeleteResource(String operator, String resourceId, Resource resource) {
        if (resource.getStatus().equals(ResourceStatusEnum.RES_STATUS100.code())
                && operator.equals(resource.getSellerId())) {
            if (resource.getLockNum().compareTo(BigDecimal.ZERO) > 0) {
                resource.setStatus(ResourceStatusEnum.RES_STATUS300.code());
            } else {
                resource.setStatus(ResourceStatusEnum.RES_STATUS200.code());
            }
            resource.setUpdateTime(new Date());
            resource.setUpdateUser(operator);
            resource.setResourceVersion(calculateResourceVersion(resource.getResourceId()));
            resource.setDownTime(new Date());
            resourceMapper.updateByPrimaryKeySelective(resource);
            // 记录历史
            ResourceHistory history = new ResourceHistory();
            BeanUtils.copyProperties(resource, history);
            history.setResourceHistoryId(uuidGenerator.gain());
            resourceHistoryMapper.insert(history);
            // 聚合商品资源
            this.aggregationGoodsResource(resource.getGoodsId(), resource.getSellerId(), operator, resource.getBrand());
            //异步修改资源ES
            resourceElasticsearchBiz.asyncEsDeleteResource(resourceId);
        }
    }

    private static void checkOffsale(List<String> resourceIds, String operator) {
        if (resourceIds == null || resourceIds.size() == 0) {
            throw new BizException(ResourceCode.PARAM_NULL, RESOURCE_CODE_LIST);
        }
        if (CsStringUtils.isBlank(operator)) {
            throw new BizException(ResourceCode.PARAM_NULL, "操作人");
        }
    }

    @Override
    public void onSaleResourceBatch(List<String> resourceIds, String operator) {
        // 参数检查
        checkOnsaleRe(resourceIds, operator);
        try {
            for (String resourceId : resourceIds) {
                Resource resource = resourceMapper.selectByPrimaryKey(resourceId);
                if (Objects.isNull(resource)) {
                    throw new BizException(BasicCode.DATA_NOT_EXIST, resourceId);
                }
                checkIfDownTime(resource);
                GoodsDTO goodsDTO = getGoodsInfo(resource.getGoodsId());
                Integer goodsStatus = goodsDTO.getGoodsStatus();
                if (GoodsStatusEnum.ENABLE.getCode() != goodsStatus) {
                    throw new BizException(BasicCode.CUSTOM_ERROR, resource.getResourceName() + "商品状态异常,不能挂牌");
                }
                if (!(resource.getStatus().equals(ResourceStatusEnum.RES_STATUS200.code())
                        || resource.getStatus().equals(ResourceStatusEnum.RES_STATUS400.code()))) {
                    throw new BizException(ResourceCode.UNKNOWN_ERROR,
                            "商品【" + resource.getResourceCode() + "】不能上架，只能上架已下架状态、未上架商品");
                }
                asyncEsUpdateResource2(operator, resourceId, resource);
            }
        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("onSaleResourceBatch Exception Message", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
    }

    private void asyncEsUpdateResource2(String operator, String resourceId, Resource resource) {
        if ((resource.getStatus().equals(ResourceStatusEnum.RES_STATUS200.code())
                || resource.getStatus().equals(ResourceStatusEnum.RES_STATUS400.code()))
                && operator.equals(resource.getSellerId())) {
            resource.setUpdateUser(operator);
            resource.setUpTime(new Date());
            resource.setUpdateTime(new Date());
            resource.setStatus(ResourceStatusEnum.RES_STATUS100.code());
            resource.setResourceVersion(calculateResourceVersion(resource.getResourceId()));
            resourceMapper.updateByPrimaryKeySelective(resource);
            // 记录历史
            ResourceHistory history = new ResourceHistory();
            BeanUtils.copyProperties(resource, history);
            history.setResourceHistoryId(uuidGenerator.gain());
            resourceHistoryMapper.insert(history);
            // 聚合商品资源
            this.aggregationGoodsResource(resource.getGoodsId(), resource.getSellerId(), operator, resource.getBrand());
            //异步修改资源ES
            resource = resourceMapper.selectByPrimaryKey(resourceId);
            resourceElasticsearchBiz.asyncEsUpdateResource(resource);
        }
    }

    private static void checkOnsaleRe(List<String> resourceIds, String operator) {
        if (resourceIds == null || resourceIds.size() == 0) {
            throw new BizException(ResourceCode.PARAM_NULL, "资源ID集合");
        }
        if (CsStringUtils.isBlank(operator)) {
            throw new BizException(ResourceCode.PARAM_NULL, "操作人");
        }
    }


    private void checkIfDownTime(Resource resource) {
        if (resource != null && BooleanUtils.isTrue(resource.getIfdown()) &&
                resource.getFixDowntime() != null && resource.getFixDowntime().before(new Date())) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "商品挂牌失败：定时下架时间为过去的时间！");
        }
    }

    @Override
    public void offSaleResourcePlatform(String resourceId, String operator) {
        // 参数检查
        if (CsStringUtils.isBlank(resourceId)) {
            throw new BizException(ResourceCode.PARAM_NULL, "资源编号");
        }
        if (CsStringUtils.isBlank(operator)) {
            throw new BizException(ResourceCode.PARAM_NULL, "操作人");
        }
        try {
            Resource resource = resourceMapper.selectByPrimaryKey(resourceId);
            if (resource != null) {
                if (resource.getStatus().equals(ResourceStatusEnum.RES_STATUS100.code())) {
                    if (resource.getLockNum().compareTo(BigDecimal.ZERO) > 0) {
                        resource.setStatus(ResourceStatusEnum.RES_STATUS300.code());
                    } else {
                        resource.setStatus(ResourceStatusEnum.RES_STATUS200.code());
                    }
                    resource.setUpdateUser(operator);
                    resource.setUpdateTime(new Date());
                    resource.setDownTime(new Date());
                    resource.setResourceVersion(calculateResourceVersion(resource.getResourceId()));
                    resourceMapper.updateByPrimaryKeySelective(resource);
                    // 记录历史
                    ResourceHistory history = new ResourceHistory();
                    BeanUtils.copyProperties(resource, history);
                    history.setResourceHistoryId(uuidGenerator.gain());
                    resourceHistoryMapper.insert(history);
                    // 聚合商品资源
                    this.aggregationGoodsResource(resource.getGoodsId(), resource.getSellerId(), operator, resource.getBrand());
                    //异步修改资源ES
                    resourceElasticsearchBiz.asyncEsDeleteResource(resourceId);
                    smsMessageProducer.sendMessageForOffSaleResourcePlatform(resource.getSellerId(), resource.getGoodsName(), resource.getSaleAreaRealCode());
                } else {
                    throw new BizException(ResourceCode.CAN_NOT_UPDATE, CAN_NOT_CHANGE_THIS_STATUS_RESOURCE);
                }
            } else {
                throw new BizException(ResourceCode.DATA_NOT_FOUND, "资源" + resourceId);
            }
        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("offSaleResourcePlatform Exception Message: {}", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
    }

    @Override
    public void onSaleResourcePlatform(String resourceId, String operator) {
        // 参数检查
        if (CsStringUtils.isBlank(resourceId)) {
            throw new BizException(ResourceCode.PARAM_NULL, "资源编号");
        }
        if (CsStringUtils.isBlank(operator)) {
            throw new BizException(ResourceCode.PARAM_NULL, "操作人");
        }
        try {
            Resource resource = resourceMapper.selectByPrimaryKey(resourceId);
            checkIfDownTime(resource);

            if ((resource.getStatus().equals(ResourceStatusEnum.RES_STATUS200.code())
                    || resource.getStatus().equals(ResourceStatusEnum.RES_STATUS400.code()))) {
                resource.setUpdateUser(operator);
                resource.setStatus(ResourceStatusEnum.RES_STATUS100.code());
                resource.setUpdateTime(new Date());
                resource.setUpTime(new Date());
                resource.setResourceVersion(calculateResourceVersion(resource.getResourceId()));
                resourceMapper.updateByPrimaryKeySelective(resource);
                // 记录历史
                ResourceHistory history = new ResourceHistory();
                BeanUtils.copyProperties(resource, history);
                history.setResourceHistoryId(uuidGenerator.gain());
                resourceHistoryMapper.insert(history);
                // 聚合商品资源
                this.aggregationGoodsResource(resource.getGoodsId(), resource.getSellerId(), operator, resource.getBrand());
                //异步修改资源ES
                resource = resourceMapper.selectByPrimaryKey(resourceId);
                resourceElasticsearchBiz.asyncEsUpdateResource(resource);
            } else {
                throw new BizException(ResourceCode.CAN_NOT_UPDATE, CAN_NOT_CHANGE_THIS_STATUS_RESOURCE);
            }

        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("onSaleResourcePlatform Exception Message: {}", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
    }

    @Override
    public void offSaleResource(String resourceId, String operator) {
        // 参数检查
        checkOffSale3(resourceId, operator);
        try {
            Resource resource = resourceMapper.selectByPrimaryKey(resourceId);
            if (resource != null) {
                if (!operator.equals(resource.getSellerId())) {
                    if (resource.getStatus().equals(ResourceStatusEnum.RES_STATUS100.code())) {
                        setStatus(resource);
                        resource.setUpdateTime(new Date());
                        resource.setDownTime(new Date());
                        resource.setUpdateUser(operator);
                        resource.setResourceVersion(calculateResourceVersion(resource.getResourceId()));
                        resourceMapper.updateByPrimaryKeySelective(resource);
                        // 记录历史
                        ResourceHistory history = new ResourceHistory();
                        BeanUtils.copyProperties(resource, history);
                        history.setResourceHistoryId(uuidGenerator.gain());
                        resourceHistoryMapper.insert(history);
                        // 聚合商品资源
                        this.aggregationGoodsResource(resource.getGoodsId(), resource.getSellerId(), operator, resource.getBrand());
                        //异步修改资源ES
                        resourceElasticsearchBiz.asyncEsDeleteResource(resourceId);
                    } else {
                        throw new BizException(ResourceCode.CAN_NOT_UPDATE, CAN_NOT_CHANGE_THIS_STATUS_RESOURCE);
                    }
                } else {
                    throw new BizException(ResourceCode.CAN_NOT_UPDATE, "只能下架卖家自己的资源");
                }
            } else {
                throw new BizException(ResourceCode.DATA_NOT_FOUND, "资源" + resourceId);
            }
        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("offSaleResource Exception Message: {}", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
    }

    private static void setStatus(Resource resource) {
        if (resource.getLockNum().compareTo(BigDecimal.ZERO) > 0) {
            resource.setStatus(ResourceStatusEnum.RES_STATUS300.code());
        } else {
            resource.setStatus(ResourceStatusEnum.RES_STATUS200.code());
        }
    }

    private static void checkOffSale3(String resourceId, String operator) {
        if (CsStringUtils.isBlank(resourceId)) {
            throw new BizException(ResourceCode.PARAM_NULL, "资源编号");
        }
        if (CsStringUtils.isBlank(operator)) {
            throw new BizException(ResourceCode.PARAM_NULL, "操作人");
        }
    }

    @Override
    public void onSaleResource(String resourceId, String operator) {
        // 参数检查
        if (CsStringUtils.isBlank(resourceId)) {
            throw new BizException(ResourceCode.PARAM_NULL, "资源编号");
        }
        if (CsStringUtils.isBlank(operator)) {
            throw new BizException(ResourceCode.PARAM_NULL, "操作人");
        }
        try {
            Resource resource = resourceMapper.selectByPrimaryKey(resourceId);
            checkIfDownTime(resource);

            if (!operator.equals(resource.getSellerId())) {
                if ((resource.getStatus().equals(ResourceStatusEnum.RES_STATUS200.code())
                        || resource.getStatus().equals(ResourceStatusEnum.RES_STATUS400.code()))) {
                    resource.setStatus(ResourceStatusEnum.RES_STATUS100.code());
                    resource.setUpdateUser(operator);
                    resource.setUpdateTime(new Date());
                    resource.setUpTime(new Date());
                    resource.setResourceVersion(calculateResourceVersion(resource.getResourceId()));
                    resourceMapper.updateByPrimaryKeySelective(resource);
                    // 记录历史
                    ResourceHistory history = new ResourceHistory();
                    BeanUtils.copyProperties(resource, history);
                    history.setResourceHistoryId(uuidGenerator.gain());
                    resourceHistoryMapper.insert(history);
                    // 聚合商品资源
                    this.aggregationGoodsResource(resource.getGoodsId(), resource.getSellerId(), operator, resource.getBrand());
                    //异步修改资源ES
                    resource = resourceMapper.selectByPrimaryKey(resourceId);
                    resourceElasticsearchBiz.asyncEsUpdateResource(resource);
                } else {
                    throw new BizException(ResourceCode.CAN_NOT_UPDATE, "不能上架该状态的资源");
                }
            } else {
                throw new BizException(ResourceCode.CAN_NOT_UPDATE, "只能上架卖家自己的资源");
            }

        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("onSaleResource Exception Message: {}", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
    }

    @Override
    public void offSaleResourceBatchPlatform(List<String> resourceIds, String operator) {
        // 参数检查
        if (checkParams4(resourceIds, operator)) return;
        try {
            Map<String, Set<String>> sellerGoodsNameMap = Maps.newHashMap();
            dealSellerGoodsNameMap(resourceIds, operator, sellerGoodsNameMap);
            sellerGoodsNameMap.forEach((k, v) -> {
                String[] strings = k.split("_");
                smsMessageProducer.sendMessageForOffSaleResourcePlatform(strings[0], v.size() == 1 ? v.iterator().next() : CsStringUtils.join(v, ","), strings.length == 2 ? strings[1] : null);
            });
        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("offSaleResourceBatchPlatform Exception Message: {}", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
    }

    private void dealSellerGoodsNameMap(List<String> resourceIds, String operator, Map<String, Set<String>> sellerGoodsNameMap) {
        for (String resourceId : resourceIds) {
            Resource resource = resourceMapper.selectByPrimaryKey(resourceId);
            if (resource == null) {
                continue;
            }
            if (!resource.getStatus().equals(ResourceStatusEnum.RES_STATUS100.code())) {
                throw new BizException(ResourceCode.UNKNOWN_ERROR, "商品【" + resource.getResourceCode() + "】不能下架，只能下架已上架状态商品");
            }
            if (resource.getStatus().equals(ResourceStatusEnum.RES_STATUS100.code())) {
                setStatus2(resource);
                resource.setDownTime(new Date());
                resource.setUpdateUser(operator);
                resource.setUpdateTime(new Date());
                resource.setResourceVersion(calculateResourceVersion(resource.getResourceId()));
                resourceMapper.updateByPrimaryKeySelective(resource);
                // 记录历史
                ResourceHistory history = new ResourceHistory();
                BeanUtils.copyProperties(resource, history);
                history.setResourceHistoryId(uuidGenerator.gain());
                resourceHistoryMapper.insert(history);
                // 聚合商品资源
                this.aggregationGoodsResource(resource.getGoodsId(), resource.getSellerId(), operator, resource.getBrand());
                //异步修改资源ES
                resourceElasticsearchBiz.asyncEsDeleteResource(resourceId);
                String key = resource.getSellerId() + "_" + (CsStringUtils.isBlank(resource.getSaleAreaRealCode()) ? "" : resource.getSaleAreaRealCode());
                Set<String> goodsNameSet = sellerGoodsNameMap.getOrDefault(key, Sets.newHashSet());
                goodsNameSet.add(resource.getGoodsName());
                sellerGoodsNameMap.put(key, goodsNameSet);
            }
        }
    }

    private static boolean checkParams4(List<String> resourceIds, String operator) {
        if (resourceIds == null || resourceIds.size() == 0) {
            throw new BizException(ResourceCode.PARAM_NULL, RESOURCE_CODE_LIST);
        }
        if (CsStringUtils.isBlank(operator)) {
            throw new BizException(ResourceCode.PARAM_NULL, "操作人");
        }
        if (resourceIds == null || resourceIds.size() == 0) {
            return true;
        }
        return false;
    }

    private static void setStatus2(Resource resource) {
        if (resource.getLockNum().compareTo(BigDecimal.ZERO) > 0) {
            resource.setStatus(ResourceStatusEnum.RES_STATUS300.code());
        } else {
            resource.setStatus(ResourceStatusEnum.RES_STATUS200.code());
        }
    }

    @Override
    public void onSaleResourceBatchPlatform(List<String> resourceIds, String operator) {
        // 参数检查
        if (CollectionUtils.isEmpty(resourceIds)) {
            throw new BizException(ResourceCode.PARAM_NULL, RESOURCE_CODE_LIST);
        }
        if (CsStringUtils.isBlank(operator)) {
            throw new BizException(ResourceCode.PARAM_NULL, "操作人");
        }
        try {
            for (String resourceId : resourceIds) {
                Resource resource = resourceMapper.selectByPrimaryKey(resourceId);
                if (resource == null) {
                    return;
                }
                if (!(resource.getStatus().equals(ResourceStatusEnum.RES_STATUS200.code())
                        || resource.getStatus().equals(ResourceStatusEnum.RES_STATUS400.code()))) {
                    throw new BizException(ResourceCode.UNKNOWN_ERROR,
                            "商品【" + resource.getResourceCode() + "】不能上架，只能上架已下架状态、未上架商品");
                }
                asyncEsUpdateResource4(operator, resourceId, resource);
            }
        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("onSaleResourceBatchPlatform Exception Message error:", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
    }

    private void asyncEsUpdateResource4(String operator, String resourceId, Resource resource) {
        if ((resource.getStatus().equals(ResourceStatusEnum.RES_STATUS200.code())
                || resource.getStatus().equals(ResourceStatusEnum.RES_STATUS400.code()))) {
            resource.setUpdateUser(operator);
            resource.setUpdateTime(new Date());
            resource.setStatus(ResourceStatusEnum.RES_STATUS100.code());
            resource.setUpTime(new Date());
            resource.setResourceVersion(calculateResourceVersion(resource.getResourceId()));
            resourceMapper.updateByPrimaryKeySelective(resource);
            // 记录历史
            ResourceHistory history = new ResourceHistory();
            BeanUtils.copyProperties(resource, history);
            history.setResourceHistoryId(uuidGenerator.gain());
            resourceHistoryMapper.insert(history);
            // 聚合商品资源
            this.aggregationGoodsResource(resource.getGoodsId(), resource.getSellerId(), operator, resource.getBrand());
            //异步修改资源ES
            resource = resourceMapper.selectByPrimaryKey(resourceId);
            resourceElasticsearchBiz.asyncEsUpdateResource(resource);
        }
    }

    @Override
    public List<LogisticsResourceDTO> searchResourceByDescribe(ReqLogisticsResourceDTO req) {
        List<LogisticsResourceDTO> list = new ArrayList<>();
        try {
            Example example = new Example(Resource.class);
            Example.Criteria criteria = example.createCriteria();
            criteriaAddparams2(req, criteria);
            criteria.andEqualTo(DEL_FLG, BooleanEnum.NO.code());
            List<Resource> resources = resourceMapper.selectByExample(example);
            for (Resource resource : Optional.ofNullable(resources).orElse(Collections.emptyList())) {
                LogisticsResourceDTO logisticsResourceDTO = new LogisticsResourceDTO();
                BeanUtils.copyProperties(resource, logisticsResourceDTO);
                GoodsDTO goodsInfo = getGoodsInfo(resource.getGoodsId());
                if (goodsInfo != null) {
                    logisticsResourceDTO.setLogistics(goodsInfo.getLogistics());
                }
                String[] imgs = goodsInfo.getImgs();
                String img = "";
                if (goodsInfo.getImgs().length > 0) {
                    img = imgs[0];
                }
                logisticsResourceDTO.setUnit(resource.getPriceUnit());
                logisticsResourceDTO.setImgs(img);
                logisticsResourceDTO.setSaleUnit(resource.getSaleUnit());
                logisticsResourceDTO.setSpecialFlag(BooleanEnum.NO.code());
                logisticsResourceDTO.setPrice(resource.getPrice().multiply(getRatio(resource.getGoodsId(), resource.getSaleUnit())));
                list.add(logisticsResourceDTO);
            }
        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("searchResourceByDescribe Exception Message: {}", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
        return list;
    }

    private static void criteriaAddparams2(ReqLogisticsResourceDTO req, Example.Criteria criteria) {
        if (CsStringUtils.isNotBlank(req.getGoodsDescribe())) {
            criteria.andLike(GOODS_DESCRIBE, "%" + req.getGoodsDescribe() + "%");
        }
        if (CsStringUtils.isNotBlank(req.getSellerId())) {
            criteria.andEqualTo(SELLER_ID, req.getSellerId());
        }
        if (CsStringUtils.isNotBlank(req.getStoreId())) {
            criteria.andEqualTo(STORE_ID, req.getStoreId());
        }
        if (CsStringUtils.isNotBlank(req.getStoreType())) {
            criteria.andEqualTo("storeType", req.getStoreType());
        }
    }

    @Override
    public ContractResourceDTO confirmContractResource(ReqContractResourceDTO req) {
        // 参数检查
        if (CsStringUtils.isBlank(req.getGoodsId())) {
            throw new BizException(ResourceCode.PARAM_NULL, "商品编号");
        }
        if (CsStringUtils.isBlank(req.getUnit())) {
            throw new BizException(ResourceCode.PARAM_NULL, "商品单位");
        }
        ContractResourceDTO contractResourceDTO = new ContractResourceDTO();
        contractResourceDTO.setIsHaveGoods(BooleanEnum.NO.code());
        BigDecimal cansaleNumTmp;
        BigDecimal price;
        try {
            BigDecimal ratio = getRatio(req.getGoodsId(), req.getUnit());
            String unitId = getUnitId(req.getGoodsId(), req.getUnit());
            // 查询商品信息
            // 查询商品对应的资源
            Example example = new Example(Resource.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo(GOODS_ID, req.getGoodsId());
            criteria.andEqualTo(STATUS, ResourceStatusEnum.RES_STATUS100.code());
            criteria.andEqualTo(DEL_FLG, BooleanEnum.NO.code());
            List<Resource> resources = resourceMapper.selectByExample(example);
            // 查询所有可以出售的资源
            List<String> resourceIds = getResourceIds(resources);
            // 匹配行政区域
            Condition regionExample = new Condition(ResourceRegion.class);
            Example.Criteria regionCriteria = regionExample.createCriteria();
            regionCriteria.andIn(RESOURCE_ID, resourceIds);
            regionCriteria.andEqualTo(DEL_FLG, BooleanEnum.NO.code());
            regionExampleAddValues(req, regionExample);
            List<ResourceRegion> regions = resourceRegionMapper.selectByCondition(regionExample);
            // 回溯资源列表
            List<String> resourceIdList = new ArrayList<>();
            resourceIdListAddValues(regions, resourceIdList);
            // 计算最低价资源
            Resource resourceTmp = null;
            BigDecimal lowestPrice = BigDecimal.ZERO;
            resourceTmp = getResourceTmp(resourceIdList, lowestPrice, resourceTmp);
            contractResourceDTOAddItems(req, resourceTmp, ratio, contractResourceDTO, unitId);
        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("searchResourceDetailBuyer Exception Message: {}", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
        return contractResourceDTO;
    }

    private static void regionExampleAddValues(ReqContractResourceDTO req, Condition regionExample) {
        if (CsStringUtils.isNotBlank(req.getCountryName())) {
            Example.Criteria criteria1 = regionExample.createCriteria()
                    .andEqualTo(COUNTRY_NAME, req.getCountryName()).orIsNull(COUNTRY_NAME)
                    .orEqualTo(COUNTRY_NAME, "");
            regionExample.and(criteria1);
        }
        if (CsStringUtils.isNotBlank(req.getCityName())) {
            Example.Criteria criteria2 = regionExample.createCriteria()
                    .andEqualTo(CITY_NAME, req.getCityName()).orIsNull(CITY_NAME).orEqualTo(CITY_NAME, "");
            regionExample.and(criteria2);
        }
        if (CsStringUtils.isNotBlank(req.getProvinceName())) {
            Example.Criteria criteria3 = regionExample.createCriteria()
                    .andEqualTo(PROVINCE_NAME, req.getProvinceName()).orIsNull(PROVINCE_NAME)
                    .orEqualTo(PROVINCE_NAME, "");
            regionExample.and(criteria3);
        }
        if (CsStringUtils.isNotBlank(req.getAreaName())) {
            Example.Criteria criteria4 = regionExample.createCriteria()
                    .andEqualTo(AREA_NAME, req.getAreaName()).orIsNull(AREA_NAME).orEqualTo(AREA_NAME, "");
            regionExample.and(criteria4);
        }
        if (CsStringUtils.isNotBlank(req.getStreetName())) {
            Example.Criteria criteria5 = regionExample.createCriteria()
                    .andEqualTo(STREET_NAME, req.getStreetName()).orIsNull(STREET_NAME)
                    .orEqualTo(STREET_NAME, "");
            regionExample.and(criteria5);
        }
        if (CsStringUtils.isNotBlank(req.getCountryCode())) {
            Example.Criteria criteria6 = regionExample.createCriteria()
                    .andEqualTo(COUNTRY_CODE, req.getCountryCode()).orIsNull(COUNTRY_CODE)
                    .orEqualTo(COUNTRY_CODE, "");
            regionExample.and(criteria6);
        }
        if (CsStringUtils.isNotBlank(req.getProvinceCode())) {
            Example.Criteria criteria7 = regionExample.createCriteria()
                    .andEqualTo(PROVINCE_CODE, req.getProvinceCode()).orIsNull(PROVINCE_CODE)
                    .orEqualTo(PROVINCE_CODE, "");
            regionExample.and(criteria7);
        }
        if (CsStringUtils.isNotBlank(req.getCityCode())) {
            Example.Criteria criteria8 = regionExample.createCriteria()
                    .andEqualTo(CITY_CODE, req.getCityCode()).orIsNull(CITY_CODE).orEqualTo(CITY_CODE, "");
            regionExample.and(criteria8);
        }
        if (CsStringUtils.isNotBlank(req.getAreaCode())) {
            Example.Criteria criteria9 = regionExample.createCriteria()
                    .andEqualTo(AREA_CODE, req.getAreaCode()).orIsNull(AREA_CODE).orEqualTo(AREA_CODE, "");
            regionExample.and(criteria9);
        }
        if (CsStringUtils.isNotBlank(req.getStreetCode())) {
            Example.Criteria criteria10 = regionExample.createCriteria()
                    .andEqualTo(STREET_CODE, req.getStreetCode()).orIsNull(STREET_CODE)
                    .orEqualTo(STREET_CODE, "");
            regionExample.and(criteria10);
        }
    }

    private static void resourceIdListAddValues(List<ResourceRegion> regions, List<String> resourceIdList) {
        for (ResourceRegion reagion : Optional.ofNullable(regions).orElse(Collections.emptyList())) {
            if (!resourceIdList.contains(reagion.getResourceId())) {
                resourceIdList.add(reagion.getResourceId());
            }
        }
    }

    private Resource getResourceTmp(List<String> resourceIdList, BigDecimal lowestPrice, Resource resourceTmp) {
        for (String resourceIdTmp : Optional.ofNullable(resourceIdList).orElse(Collections.emptyList())) {
            Resource resource = resourceMapper.selectByPrimaryKey(resourceIdTmp);
            if (resource != null) {
                BigDecimal priceTmp = resource.getPrice();
                if (lowestPrice.compareTo(BigDecimal.ZERO) == 0) {
                    lowestPrice = priceTmp;
                    resourceTmp = resource;
                }
                if (priceTmp.compareTo(lowestPrice) < 0) {
                    resourceTmp = resource;
                    lowestPrice = priceTmp;
                }
            }
        }
        return resourceTmp;
    }

    private static void contractResourceDTOAddItems(ReqContractResourceDTO req, Resource resourceTmp, BigDecimal ratio, ContractResourceDTO contractResourceDTO, String unitId) {
        BigDecimal price;
        BigDecimal cansaleNumTmp;
        if (resourceTmp != null) {
            cansaleNumTmp = resourceTmp.getCansaleNum().multiply(ratio);
            price = resourceTmp.getPrice();
            contractResourceDTO.setIsHaveGoods(BooleanEnum.YES.code());
            contractResourceDTO.setPriceWay(resourceTmp.getPriceWay());
            contractResourceDTO.setCansaleNum(cansaleNumTmp);
            contractResourceDTO.setPrice(price);
            contractResourceDTO.setUnit(req.getUnit());
            contractResourceDTO.setUnitId(unitId);
            contractResourceDTO.setSellerId(req.getSellerId());
            contractResourceDTO.setResourceId(resourceTmp.getResourceId());
            contractResourceDTO.setGoodsId(req.getGoodsId());
            contractResourceDTO.setCurrency(resourceTmp.getCurrency());
            contractResourceDTO.setGoodsName(resourceTmp.getGoodsName());
            contractResourceDTO.setGoodsType(resourceTmp.getGoodsType());
            contractResourceDTO.setResourceName(resourceTmp.getResourceName());
            contractResourceDTO.setResourceCode(resourceTmp.getResourceCode());
        }
    }

    @NotNull
    private List<String> getResourceIds(List<Resource> resources) {
        List<String> resourceIds = new ArrayList<>();
        for (Resource resource : Optional.ofNullable(resources).orElse(Collections.emptyList())) {
            if (checkResourceTradeStatus(resource)) {
                resourceIds.add(resource.getResourceId());
            }
        }
        return resourceIds;
    }

    @Override
    public void autoOnsaleCheckAsync() {
        threadPoolExecutor.execute(() -> {
            String oldName = Thread.currentThread().getName();
            try {
                Thread.currentThread().setName(uuidGenerator.gain());
                autoOnsaleCheck();
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            } finally {
                Thread.currentThread().setName(oldName);
            }
        });
    }

    @Override
    public void autoOnsaleCheck() {
        try {
            long startTime = System.currentTimeMillis();
            log.info("autoOnsaleCheck start time: {}", startTime);
            Example example = new Example(Resource.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo("ifup", BooleanEnum.YES.code());
            criteria.andLessThanOrEqualTo("fixUptime", new Date());
            criteria.andEqualTo(STATUS, ResourceStatusEnum.RES_STATUS400.code());
            criteria.andEqualTo(DEL_FLG, BooleanEnum.NO.code());
            //按上架时间、创建时间顺序处理
            example.setOrderByClause("fix_uptime asc,create_time asc");
            List<Resource> resources = resourceMapper.selectByExample(example);
            for (Resource resource : Optional.ofNullable(resources).orElse(Collections.emptyList())) {
                // 上架处理
                resource = checkResource(resource);
                resource.setStatus(ResourceStatusEnum.RES_STATUS100.code());
                resource.setUpTime(new Date());
                resource.setUpdateTime(new Date());
                resource.setUpdateUser(resource.getSellerId());
                resource.setResourceVersion(calculateResourceVersion(resource.getResourceId()));
                resourceMapper.updateByPrimaryKeySelective(resource);
                // 插入历史记录
                ResourceHistory history = new ResourceHistory();
                history.setResourceHistoryId(uuidGenerator.gain());
                BeanUtils.copyProperties(resource, history);
                resourceHistoryMapper.insert(history);
                // 聚合商品资源
                this.aggregationGoodsResource(resource.getGoodsId(), resource.getSellerId(), ADMIN, resource.getBrand());
                //异步修改资源ES
                resource = resourceMapper.selectByPrimaryKey(resource.getResourceId());
                resourceElasticsearchBiz.asyncEsUpdateResource(resource);
            }
            long endTime = System.currentTimeMillis();
            log.info("autoOnsaleCheck end time: {}", endTime);
        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("autoOnsaleCheck Exception Message: {}", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
    }

    /**
     * 校验要上架的资源对应的区域（相同的卖家、商品、仓库、销售区域）是否有已上架的资源,autoOnsaleCheck专用
     *
     * @param resource 当前资源
     * @return 有则更新原来的资源、删除当前，返回原来的资源,无则返回当前资源
     */
    private Resource checkResource(Resource resource) {
        Example example = new Example(Resource.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andNotEqualTo("resourceId", resource.getResourceId());
        criteria.andEqualTo("sellerId", resource.getSellerId());
        criteria.andEqualTo("goodsId", resource.getGoodsId());
        criteria.andEqualTo("storeId", resource.getStoreId());
        criteria.andEqualTo("saleAreaRealCode", resource.getSaleAreaRealCode());
        criteria.andEqualTo(STATUS, ResourceStatusEnum.RES_STATUS100.code());
        criteria.andEqualTo(DEL_FLG, BooleanEnum.NO.code());
        example.orderBy("fixUptime").asc();
        List<Resource> resourceList = resourceMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(resourceList)) {
            return resource;
        }
        int size = resourceList.size();
        if (size > 1) {
            log.error("sellerId:{},goodsId:{},storeId:{},saleAreaRealCode:{},有{}个已上架资源",
                    resource.getSellerId(), resource.getGoodsId(), resource.getStoreId(), resource.getSaleAreaRealCode(), size);
        }
        Resource existsResource = resourceList.get(0);
        String existsResourceId = existsResource.getResourceId();
        //更新已上架的资源信息
        BeanUtils.copyProperties(resource, existsResource);
        existsResource.setResourceId(existsResourceId);
        // 插入历史记录
        resource.setApprovalMessage(existsResourceId);
        ResourceHistory history = new ResourceHistory();
        history.setResourceHistoryId(uuidGenerator.gain());
        BeanUtils.copyProperties(resource, history);
        resourceHistoryMapper.insert(history);
        //删除编辑记录
        resource.setDelFlg(true);
        resource.setUpTime(new Date());
        resource.setUpdateUser("autoOnsaleCheck");
        resourceMapper.updateByPrimaryKeySelective(resource);

        return existsResource;
    }

    @Override
    public void autoOffsaleCheck() {
        try {
            long startTime = System.currentTimeMillis();
            log.info("autoOffsaleCheck start time: {}", startTime);
            Example example = new Example(Resource.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo("ifdown", BooleanEnum.YES.code());
            criteria.andLessThanOrEqualTo("fixDowntime", new Date());
            criteria.andEqualTo(DEL_FLG, BooleanEnum.NO.code());
            criteria.andEqualTo(STATUS, ResourceStatusEnum.RES_STATUS100.code());
            List<Resource> resources = resourceMapper.selectByExample(example);
            for (Resource resource : Optional.ofNullable(resources).orElse(Collections.emptyList())) {
                // 下架处理
                if (resource.getLockNum().compareTo(BigDecimal.ZERO) > 0) {
                    resource.setStatus(ResourceStatusEnum.RES_STATUS300.code());
                } else {
                    resource.setStatus(ResourceStatusEnum.RES_STATUS200.code());
                }
                resource.setDownTime(new Date());
                resource.setUpdateTime(new Date());
                resource.setUpdateUser(resource.getSellerId());
                resource.setResourceVersion(calculateResourceVersion(resource.getResourceId()));
                resourceMapper.updateByPrimaryKeySelective(resource);
                // 插入历史记录
                ResourceHistory history = new ResourceHistory();
                history.setResourceHistoryId(uuidGenerator.gain());
                BeanUtils.copyProperties(resource, history);
                resourceHistoryMapper.insert(history);
                // 聚合商品资源
                this.aggregationGoodsResource(resource.getGoodsId(), resource.getSellerId(), ADMIN, resource.getBrand());
                //异步修改资源ES
                resourceElasticsearchBiz.asyncEsDeleteResource(resource.getResourceId());
            }
            long endTime = System.currentTimeMillis();
            log.info("autoOffsaleCheck end time: {}", endTime);
        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("autoOffsaleCheck Exception Message: {}", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
    }

    @Override
    public void autoEffectCheck() {
        try {
            long startTime = System.currentTimeMillis();
            log.info("autoEffectCheck start time: {}", startTime);
            Example example = new Example(ResourceUneffective.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andLessThanOrEqualTo("effectTime", new Date());
            criteria.andEqualTo(DEL_FLG, BooleanEnum.NO.code());
            List<ResourceUneffective> uneffectives = resourceUneffectiveMapper.selectByExample(example);
            for (ResourceUneffective uneffective : Optional.ofNullable(uneffectives).orElse(Collections.emptyList())) {
                // 删除未生效资源
                uneffective.setDelFlg(true);
                resourceUneffectiveMapper.updateByPrimaryKeySelective(uneffective);
                // 修改资源状态
                Resource resource = resourceMapper.selectByPrimaryKey(uneffective.getResourceId());
                BeanUtils.copyProperties(uneffective, resource);
                resource.setDelFlg(false);
                resource.setResourceVersion(calculateResourceVersion(resource.getResourceId()));
                resource.setUpdateUser(resource.getSellerId());
                resource.setUpdateTime(new Date());
                resource.setApprovalMessage("未生效资源到时间自动生效");
                resourceMapper.updateByPrimaryKeySelective(resource);
                // 插入历史资源记录
                ResourceHistory history = new ResourceHistory();
                history.setResourceHistoryId(uuidGenerator.gain());
                BeanUtils.copyProperties(resource, history);
                resourceHistoryMapper.insert(history);
                // 聚合商品资源
                this.aggregationGoodsResource(resource.getGoodsId(), resource.getSellerId(), ADMIN, resource.getBrand());
                //异步修改资源ES
                resource = resourceMapper.selectByPrimaryKey(resource.getResourceId());
                resourceElasticsearchBiz.asyncEsUpdateResource(resource);
            }
            long endTime = System.currentTimeMillis();
            log.info("autoEffectCheck end time: {}", endTime);
        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("autoEffectCheck Exception Message: {}", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
    }

    @Override
    public void autoOffsalingCheck() {
        try {
            long startTime = System.currentTimeMillis();
            log.info("autoOffsalingCheck start time: {}", startTime);
            Example example = new Example(Resource.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo(STATUS, ResourceStatusEnum.RES_STATUS300.code());
            List<Resource> resources = resourceMapper.selectByExample(example);
            if (CollectionKit.isNotEmpty(resources)) {
                for (Resource resource : resources) {
                    if (resource.getLockNum().compareTo(BigDecimal.ZERO) == 0) {
                        resource.setStatus(ResourceStatusEnum.RES_STATUS200.code());
                        resource.setResourceVersion(calculateResourceVersion(resource.getResourceId()));
                        resource.setUpdateUser(resource.getSellerId());
                        resource.setUpdateTime(new Date());
                        resource.setApprovalMessage("撤牌处理中流程下架");
                        resourceMapper.updateByPrimaryKeySelective(resource);
                        // 插入历史资源记录
                        ResourceHistory history = new ResourceHistory();
                        history.setResourceHistoryId(uuidGenerator.gain());
                        BeanUtils.copyProperties(resource, history);
                        resourceHistoryMapper.insert(history);
                        // 聚合商品资源
                        this.aggregationGoodsResource(resource.getGoodsId(), resource.getSellerId(), ADMIN, resource.getBrand());
                        //异步修改资源ES
                        resourceElasticsearchBiz.asyncEsDeleteResource(resource.getResourceId());
                    }
                }
            }
            long endTime = System.currentTimeMillis();
            log.info("autoOffsalingCheck end time: {}", endTime);
        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("autoOffsalingCheck Exception Message", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
    }

    @Override
    public ResourceDTO getHistoryResourceDetail(String resourceId, String resourceVersion) {
        // 参数检查
        if (CsStringUtils.isBlank(resourceId)) {
            throw new BizException(ResourceCode.PARAM_NULL, "资源编号");
        }
        if (CsStringUtils.isBlank(resourceVersion)) {
            throw new BizException(ResourceCode.PARAM_NULL, "资源版本号");
        }
        ResourceDTO resourceDTO = new ResourceDTO();
        try {
            Example example = new Example(ResourceHistory.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo(RESOURCE_ID, resourceId);
            criteria.andEqualTo("resourceVersion", resourceVersion);
            List<ResourceHistory> histories = resourceHistoryMapper.selectByExample(example);
            if (CollectionKit.isNotEmpty(histories)) {
                ResourceHistory history = histories.get(0);
                if (history != null) {
                    BeanUtils.copyProperties(history, resourceDTO);
                    GoodsDTO goodsInfo = getGoodsInfo(history.getGoodsId());
                    // 查询商品信息
                    resourceDTO.setGoodsDTO(goodsInfo);
                }
            }
        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("getHistoryResourceDetail Exception Message", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
        return resourceDTO;
    }

    @Override
    public List<GoodsResourceDTO> searchGoodsResourceListBuyer(List<String> resourceGoodsIds) {
        // 参数检查
        if (resourceGoodsIds == null || resourceGoodsIds.size() == 0) {
            throw new BizException(ResourceCode.PARAM_NULL, "资源商品编号集合");
        }
        List<GoodsResourceDTO> list = new ArrayList<>();
        try {
            for (String resourceGoodsId : resourceGoodsIds) {
                GoodsResource resourceGoods = goodsResourceMapper.selectByPrimaryKey(resourceGoodsId);
                if (resourceGoods != null) {
                    GoodsResourceDTO goodsResourceDTO = new GoodsResourceDTO();
                    BeanUtils.copyProperties(resourceGoods, goodsResourceDTO);
                    list.add(goodsResourceDTO);
                }
            }
        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("searchGoodsResourceListBuyer Exception Message: {}", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
        return list;
    }

    /**
     * 是否可以下架商品
     */
    @Deprecated(since = "2.1.4-RELEASE")
    @Override
    public Boolean ifCanGoodsUnShelve(String goodsId) {
        // 参数检查
        if (CsStringUtils.isBlank(goodsId)) {
            throw new BizException(ResourceCode.PARAM_NULL, "商品编号");
        }
        Boolean result = Boolean.TRUE;
        try {
            Example example = new Example(Resource.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo(GOODS_ID, goodsId);
            List<Resource> resources = resourceMapper.selectByExample(example);
            for (Resource resource : Optional.ofNullable(resources).orElse(Collections.emptyList())) {
                if (resource.getStatus().equals(ResourceStatusEnum.RES_STATUS100.code())
                        || resource.getStatus().equals(ResourceStatusEnum.RES_STATUS300.code())
                        || resource.getLockNum().compareTo(BigDecimal.ZERO) > 0) {
                    return Boolean.FALSE;
                }
            }
        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("ifCanGoodsUnShelve Exception Message", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
        return result;
    }

    @Override
    public ResourceStoreDTO confirmResourceStoreBuyer(List<ReqResourceStoreDTO> reqResourceStoreDTOs, String buyerId,
                                                      String shopCode) {
        // 参数检查
        if (reqResourceStoreDTOs == null || reqResourceStoreDTOs.size() == 0) {
            throw new BizException(ResourceCode.PARAM_NULL, "入参对象");
        }
        if (CsStringUtils.isBlank(buyerId)) {
            throw new BizException(ResourceCode.PARAM_NULL, "买家编号");
        }
        ResourceStoreDTO resourceStoreDTO = new ResourceStoreDTO();
        resourceStoreDTO.setIsHaveGoods(BooleanEnum.NO.code());
        try {
            // 第一步：分别找出每个商品可以购买的资源Map<Goods,List<TmpResourceStoreDTO>>
            Map<String, List<TmpResourceStoreDTO>> dtoMap = new HashMap<>();
            dtoMapPutValues(reqResourceStoreDTOs, shopCode, dtoMap);

            // 第二步：取出每个商品对应的仓库-资源，并根据仓库取出交集Map<Goods,TmpResourceStoreDTO>
            Map<String, TmpResourceStoreDTO> commonDtoMap = new HashMap<>();
            commonDtoMapPutValues(reqResourceStoreDTOs, dtoMap, commonDtoMap);

            // 第三步 、结果封装
            resourceStoreDTOSetValues(commonDtoMap, resourceStoreDTO);
        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("confirmResourceStoreBuyer Exception Message", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
        return resourceStoreDTO;
    }

    private void dtoMapPutValues(List<ReqResourceStoreDTO> reqResourceStoreDTOs, String shopCode, Map<String, List<TmpResourceStoreDTO>> dtoMap) {
        for (ReqResourceStoreDTO reqResourceStoreDTO : reqResourceStoreDTOs) {
            String goodsId = reqResourceStoreDTO.getGoodsId();
            BigDecimal buyNum = reqResourceStoreDTO.getBuyNum();
            String unit = reqResourceStoreDTO.getUnit();
            if (CsStringUtils.isBlank(unit)) {
                throw new BizException(ResourceCode.PARAM_NULL, "单位");
            }
            BigDecimal ratio = getRatio(goodsId, unit);
            // 查询商品对应的资源
            Example example = new Example(Resource.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo(GOODS_ID, reqResourceStoreDTO.getGoodsId());
            criteria.andEqualTo(STATUS, ResourceStatusEnum.RES_STATUS100.code());
            criteria.andEqualTo(DEL_FLG, BooleanEnum.NO.code());
            List<Resource> resources = resourceMapper.selectByExample(example);
            // 查询所有可以出售的资源
            List<String> resourceIds = new ArrayList<>();
            resourceIdsAddValues(resources, buyNum, ratio, resourceIds);
            // 匹配行政区域，找出可以出货的资源
            List<String> resourceIdList = new ArrayList<>();
            List<TmpResourceStoreDTO> resourceStores = new ArrayList<>();
            if (resourceIds.size() > 0) {
                Condition regionExample = new Condition(ResourceRegion.class);
                Example.Criteria regionCriteria = regionExample.createCriteria();
                regionCriteria.andIn(RESOURCE_ID, resourceIds);
                regionCriteria.andEqualTo(DEL_FLG, BooleanEnum.NO.code());
                regionCriteriaAdd(reqResourceStoreDTO, regionExample);
                List<ResourceRegion> regions = resourceRegionMapper.selectByCondition(regionExample);
                // 回溯资源列表
                if (regions != null && !regions.isEmpty()) {
                    resourceStoresAddItems(shopCode, reqResourceStoreDTO, regions, resourceIdList, resourceStores);
                }
            }
            if (!resourceStores.isEmpty()) {
                dtoMap.put(reqResourceStoreDTO.getGoodsId(), resourceStores);
            }
        }
    }

    private static void regionCriteriaAdd(ReqResourceStoreDTO reqResourceStoreDTO, Condition regionExample) {
        if (CsStringUtils.isNotBlank(reqResourceStoreDTO.getCountryCode())) {
            Example.Criteria criteria1 = regionExample.createCriteria()
                    .andEqualTo(COUNTRY_CODE, reqResourceStoreDTO.getCountryCode())
                    .orIsNull(COUNTRY_CODE).orEqualTo(COUNTRY_CODE, "");
            regionExample.and(criteria1);
        }
        if (CsStringUtils.isNotBlank(reqResourceStoreDTO.getCityCode())) {
            Example.Criteria criteria2 = regionExample.createCriteria()
                    .andEqualTo(CITY_CODE, reqResourceStoreDTO.getCityCode()).orIsNull(CITY_CODE)
                    .orEqualTo(CITY_CODE, "");
            regionExample.and(criteria2);
        }
        if (CsStringUtils.isNotBlank(reqResourceStoreDTO.getProvinceCode())) {
            Example.Criteria criteria3 = regionExample.createCriteria()
                    .andEqualTo(PROVINCE_CODE, reqResourceStoreDTO.getProvinceCode())
                    .orIsNull(PROVINCE_CODE).orEqualTo(PROVINCE_CODE, "");
            regionExample.and(criteria3);
        }
        if (CsStringUtils.isNotBlank(reqResourceStoreDTO.getAreaCode())) {
            Example.Criteria criteria4 = regionExample.createCriteria()
                    .andEqualTo(AREA_CODE, reqResourceStoreDTO.getAreaCode()).orIsNull(AREA_CODE)
                    .orEqualTo(AREA_CODE, "");
            regionExample.and(criteria4);
        }
        if (CsStringUtils.isNotBlank(reqResourceStoreDTO.getStreetCode())) {
            Example.Criteria criteria5 = regionExample.createCriteria()
                    .andEqualTo(STREET_CODE, reqResourceStoreDTO.getStreetCode())
                    .orIsNull(STREET_CODE).orEqualTo(STREET_CODE, "");
            regionExample.and(criteria5);
        }
    }

    private void resourceStoreDTOSetValues(Map<String, TmpResourceStoreDTO> commonDtoMap, ResourceStoreDTO resourceStoreDTO) {
        if (commonDtoMap.size() > 0) {
            resourceStoreDTO.setIsHaveGoods(BooleanEnum.YES.code());
            List<GoodsResourceIdDTO> goodsResourceIdDTOs = new ArrayList<>();
            TmpResourceStoreDTO tmp = null;
            for (Map.Entry<String, TmpResourceStoreDTO> entry : commonDtoMap.entrySet()) {
                TmpResourceStoreDTO tmpResourceStoreDTO = entry.getValue();
                GoodsResourceIdDTO dto = new GoodsResourceIdDTO();
                dto.setGoodsId(entry.getKey());
                dto.setResourceId(tmpResourceStoreDTO.getResourceId());
                Resource resource = resourceMapper.selectByPrimaryKey(tmpResourceStoreDTO.getResourceId());
                dto.setPrice(resource.getPrice());
                goodsResourceIdDTOs.add(dto);
                tmp = tmpResourceStoreDTO;
            }
            if (tmp == null) {
                throw new BizException(ResourceCode.DATA_NOT_EXIST, "数据为空");
            }
            resourceStoreDTO.setStoreId(tmp.getStoreId());
            resourceStoreDTO.setStoreName(tmp.getStoreName());
            resourceStoreDTO.setStoreType(tmp.getStoreType());
            resourceStoreDTO.setStoreAddress(tmp.getStoreAddress());
            WarehouseBaseDataDTO warehouseBaseDataDTO = warehouseService.selectWarehouseBaseData(tmp.getStoreId());
            if (warehouseBaseDataDTO != null) {
                resourceStoreDTO.setAdministrator(warehouseBaseDataDTO.getAdministrator());
                resourceStoreDTO.setAdministratorPhone(warehouseBaseDataDTO.getAdministratorPhone());
                resourceStoreDTO.setCity(warehouseBaseDataDTO.getCity());
                resourceStoreDTO.setDistrict(warehouseBaseDataDTO.getDistrict());
                resourceStoreDTO.setProvince(warehouseBaseDataDTO.getProvince());
                resourceStoreDTO.setStoreAddress(warehouseBaseDataDTO.getAddress());
            }
            resourceStoreDTO.setGoodsResourceIdDTOs(goodsResourceIdDTOs);
        }
    }

    private static void commonDtoMapPutValues(List<ReqResourceStoreDTO> reqResourceStoreDTOs, Map<String, List<TmpResourceStoreDTO>> dtoMap, Map<String, TmpResourceStoreDTO> commonDtoMap) {
        if (dtoMap.size() == reqResourceStoreDTOs.size()) {
            // 计算相同的仓库
            String storeCommon = null;
            List<String> storeCommons = new ArrayList<>();
            int start = 0;
            storeCommons = getStoreCommons(dtoMap, start, storeCommons);
            // 取第一个共同仓库
            if (storeCommons.size() > 0) {
                storeCommon = storeCommons.get(0);
            }
            commonDtoMapPutValues(dtoMap, commonDtoMap, storeCommon);
        }
    }

    private static void commonDtoMapPutValues(Map<String, List<TmpResourceStoreDTO>> dtoMap, Map<String, TmpResourceStoreDTO> commonDtoMap, String storeCommon) {
        if (CsStringUtils.isNotBlank(storeCommon)) {
            // 取出交集
            for (Map.Entry<String, List<TmpResourceStoreDTO>> entry : dtoMap.entrySet()) {
                List<TmpResourceStoreDTO> tmpResourceStoreDTOS = entry.getValue();
                TmpResourceStoreDTO tmp = null;
                for (TmpResourceStoreDTO dto : tmpResourceStoreDTOS) {
                    if (dto.getStoreId().equals(storeCommon)) {
                        tmp = dto;
                    }
                }
                commonDtoMap.put(entry.getKey(), tmp);
            }
        }
    }

    private static List<String> getStoreCommons(Map<String, List<TmpResourceStoreDTO>> dtoMap, int start, List<String> storeCommons) {
        for (List<TmpResourceStoreDTO> value : dtoMap.values()) {
            if (start == 0) {
                for (TmpResourceStoreDTO dto : value) {
                    storeCommons.add(dto.getStoreId());
                }
                start = start + 1;
            } else {
                List<String> storeTmp = new ArrayList<>();
                for (TmpResourceStoreDTO dto : value) {
                    if (storeCommons.contains(dto.getStoreId())) {
                        storeTmp.add(dto.getStoreId());
                    }
                }
                storeCommons = storeTmp;
            }
        }
        return storeCommons;
    }

    private void resourceStoresAddItems(String shopCode, ReqResourceStoreDTO reqResourceStoreDTO, List<ResourceRegion> regions, List<String> resourceIdList, List<TmpResourceStoreDTO> resourceStores) {
        for (ResourceRegion reagion : regions) {
            if (!resourceIdList.contains(reagion.getResourceId())) {
                resourceIdList.add(reagion.getResourceId());
                TmpResourceStoreDTO dto = new TmpResourceStoreDTO();
                dto.setResourceId(reagion.getResourceId());
                Resource resource = resourceMapper.selectByPrimaryKey(reagion.getResourceId());
                if (resource != null) {
                    // 判断中心仓
                    if (resource.getStoreType().equals(StoreTypeEnum.STORE_TYPE200.code())) {
                        //中心仓
                        dealCenter(shopCode, reqResourceStoreDTO, resourceStores, dto, resource);
                    } else {
                        dto.setStoreId(resource.getStoreId());
                        dto.setStoreName(resource.getStoreName());
                        dto.setStoreType(resource.getStoreType());
                        dto.setStoreAddress(resource.getStoreAddress());
                        resourceStores.add(dto);
                    }
                }
            }
        }
    }

    private void dealCenter(String shopCode, ReqResourceStoreDTO reqResourceStoreDTO, List<TmpResourceStoreDTO> resourceStores, TmpResourceStoreDTO dto, Resource resource) {
        if (CsStringUtils.isNotBlank(shopCode)) { //这里的shopCode是推荐人表主键，如果有，查询推荐人表，获取关联门店仓库ID
            dealShopCodeIsNotBlank(shopCode, reqResourceStoreDTO, resourceStores, dto, resource);
        } else {
            WarehouseDetailsDTO warehouseDetailsDTO = queryStoreByCode(null,
                    reqResourceStoreDTO.getProvinceCode(),
                    reqResourceStoreDTO.getCityCode(),
                    reqResourceStoreDTO.getAreaCode(), resource.getSellerId());
            if (warehouseDetailsDTO != null) {
                dto.setStoreId(warehouseDetailsDTO.getWarehouseId());
                dto.setStoreName(warehouseDetailsDTO.getName());
                dto.setStoreType(warehouseDetailsDTO.getType());
                dto.setStoreAddress(warehouseDetailsDTO.getAddress());
                resourceStores.add(dto);
            }
        }
    }

    private void dealShopCodeIsNotBlank(String shopCode, ReqResourceStoreDTO reqResourceStoreDTO, List<TmpResourceStoreDTO> resourceStores, TmpResourceStoreDTO dto, Resource resource) {
        log.info("iBuyerAndReferrerService->findById->req:{}" + shopCode);
        ReferrerInfoDTO referrerInfoDTO = iBuyerAndReferrerService.findById(shopCode);
        if (referrerInfoDTO != null) {
            log.info("iBuyerAndReferrerService->findById->response:{}" + referrerInfoDTO.toString());
            String referrerStoreId = referrerInfoDTO.getReferrerStoreId();
            if (CsStringUtils.isNotBlank(referrerStoreId)) {
                log.info("warehouseService->selectWarehouseBaseData->req:{}" + referrerStoreId);
                WarehouseBaseDataDTO warehouseBaseDataDTO = warehouseService.selectWarehouseBaseData(referrerStoreId);
                dealReferrerStoreIdIsNotBlank(reqResourceStoreDTO, resourceStores, dto, resource, warehouseBaseDataDTO);
            } else {
                WarehouseDetailsDTO warehouseDetailsDTO = queryStoreByCode(null,
                        reqResourceStoreDTO.getProvinceCode(),
                        reqResourceStoreDTO.getCityCode(),
                        reqResourceStoreDTO.getAreaCode(), resource.getSellerId());
                if (warehouseDetailsDTO != null) {
                    dto.setStoreId(warehouseDetailsDTO.getWarehouseId());
                    dto.setStoreName(warehouseDetailsDTO.getName());
                    dto.setStoreType(warehouseDetailsDTO.getType());
                    dto.setStoreAddress(warehouseDetailsDTO.getAddress());
                    resourceStores.add(dto);
                }
            }
        } else {
            WarehouseDetailsDTO warehouseDetailsDTO = queryStoreByCode(null,
                    reqResourceStoreDTO.getProvinceCode(),
                    reqResourceStoreDTO.getCityCode(),
                    reqResourceStoreDTO.getAreaCode(), resource.getSellerId());
            if (warehouseDetailsDTO != null) {
                dto.setStoreId(warehouseDetailsDTO.getWarehouseId());
                dto.setStoreName(warehouseDetailsDTO.getName());
                dto.setStoreType(warehouseDetailsDTO.getType());
                dto.setStoreAddress(warehouseDetailsDTO.getAddress());
                resourceStores.add(dto);
            }
        }
    }

    private void dealReferrerStoreIdIsNotBlank(ReqResourceStoreDTO reqResourceStoreDTO, List<TmpResourceStoreDTO> resourceStores, TmpResourceStoreDTO dto, Resource resource, WarehouseBaseDataDTO warehouseBaseDataDTO) {
        if (warehouseBaseDataDTO != null) {
            log.info("warehouseService->selectWarehouseBaseData->response:{}" + warehouseBaseDataDTO.toString());
            dto.setStoreId(warehouseBaseDataDTO.getWarehouseId());
            dto.setStoreName(warehouseBaseDataDTO.getName());
            dto.setStoreType(resource.getStoreType());
            dto.setStoreAddress(warehouseBaseDataDTO.getAddress());
            resourceStores.add(dto);
        } else {
            WarehouseDetailsDTO warehouseDetailsDTO = queryStoreByCode(null,
                    reqResourceStoreDTO.getProvinceCode(),
                    reqResourceStoreDTO.getCityCode(),
                    reqResourceStoreDTO.getAreaCode(), resource.getSellerId());
            if (warehouseDetailsDTO != null) {
                dto.setStoreId(warehouseDetailsDTO.getWarehouseId());
                dto.setStoreName(warehouseDetailsDTO.getName());
                dto.setStoreType(warehouseDetailsDTO.getType());
                dto.setStoreAddress(warehouseDetailsDTO.getAddress());
                resourceStores.add(dto);
            }
        }
    }

    private void resourceIdsAddValues(List<Resource> resources, BigDecimal buyNum, BigDecimal ratio, List<String> resourceIds) {
        if (CollectionUtils.isEmpty(resources)) {
            return;
        }
        for (Resource resource : resources) {
            if (!checkResourceTradeStatus(resource)) {
                continue;
            }
            if (buyNum.compareTo(resource.getCansaleNum().multiply(ratio)) < 0
                    || buyNum.compareTo(resource.getCansaleNum().multiply(ratio)) == 0) {
                resourceIdsAdd(buyNum, resourceIds, resource);
            }
        }
    }

    private static void resourceIdsAdd(BigDecimal buyNum, List<String> resourceIds, Resource resource) {
        if (resource.getOrderminNum() == null && resource.getOrdermaxNum() == null) {
            resourceIds.add(resource.getResourceId());
        } else if (resource.getOrderminNum() == null && resource.getOrdermaxNum() != null
                && buyNum.compareTo(resource.getOrdermaxNum()) <= 0) {
            resourceIds.add(resource.getResourceId());
        } else if (resource.getOrderminNum() != null && resource.getOrdermaxNum() == null
                && buyNum.compareTo(resource.getOrderminNum()) >= 0) {
            resourceIds.add(resource.getResourceId());
        } else if (resource.getOrderminNum() != null && resource.getOrdermaxNum() != null
                && buyNum.compareTo(resource.getOrderminNum()) >= 0 && buyNum.compareTo(resource.getOrdermaxNum()) <= 0) {
            resourceIds.add(resource.getResourceId());
        }
    }

    @Override
    public SalesVolumeDTO getGoodsSalesVolume(String resourceGoodsId) {
        SalesVolumeDTO dto = new SalesVolumeDTO();
        dto.setIsHaveGoods(BooleanEnum.NO.code());
        if (CsStringUtils.isBlank(resourceGoodsId)) {
            throw new BizException(ResourceCode.PARAM_NULL, "商品资源聚合编号");
        }
        return dto;
    }

    @Override
    public List<String> queryEffectResourceIds(List<String> resourceIds) {
        List<String> effectResourceIds = new ArrayList<>();
        try {
            if (resourceIds != null && resourceIds.size() > 0) {
                Example example = new Example(Resource.class);
                Example.Criteria criteria = example.createCriteria();
                criteria.andEqualTo(DEL_FLG, BooleanEnum.NO.code());
                criteria.andIn(RESOURCE_ID, resourceIds);
                List<Resource> resources = resourceMapper.selectByExample(example);
                for (Resource resource : Optional.ofNullable(resources).orElse(Collections.emptyList())) {
                    if (resource.getStatus().equals(ResourceStatusEnum.RES_STATUS100.code())
                            && checkResourceTradeStatus(resource)) {
                        effectResourceIds.add(resource.getResourceId());
                    }
                }
            }
        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("queryEffectResourceIds Exception Message", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
        return effectResourceIds;
    }

    @Override
    public List<Resource> findByIds(Collection<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        Condition condition = newCondition();
        if (ids.size() == 1) {
            condition.createCriteria().andEqualTo(RESOURCE_ID, ids.iterator().next()).andEqualTo(DEL_FLG, false);
        } else {
            condition.createCriteria().andIn(RESOURCE_ID, ids).andEqualTo(DEL_FLG, false);
        }
        return findByCondition(condition);
    }

    @Override
    public GoodsResourceDTO searchGoodsByResource(String resourceId) {
        // 参数检查
        if (CsStringUtils.isBlank(resourceId)) {
            throw new BizException(ResourceCode.PARAM_NULL, "资源编号");
        }
        GoodsResourceDTO goodsResourceDTO = new GoodsResourceDTO();
        try {
            if (CsStringUtils.isNullOrBlank(resourceId)) {
                return null;
            }
            Resource resource = resourceMapper.selectByPrimaryKey(resourceId);
            if (resource != null) {
                Example regionExample = new Example(ResourceRegion.class);
                Example.Criteria regionExampleCriteria = regionExample.createCriteria();
                regionExampleCriteria.andEqualTo(RESOURCE_ID, resource.getResourceId());
                regionExampleCriteria.andEqualTo(DEL_FLG, BooleanEnum.NO.code());
                List<ResourceRegion> regions = resourceRegionMapper.selectByExample(regionExample);
                List<String> provinces = new ArrayList<>();
                for (ResourceRegion region : Optional.ofNullable(regions).orElse(Collections.emptyList())) {
                    if (!provinces.contains(region.getProvinceName())) {
                        provinces.add(region.getProvinceName());
                    }
                }
            }
        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("searchGoodsResourceDetailBuyer Exception Message", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
        return goodsResourceDTO;
    }

    @Override
    public List<GoodsCollecttionDTO> searchGoodsCollectionList(List<ReqGoodsCollecttionDTO> reqGoodsCollecttionDTOs) {
        // 初始化结果
        List<GoodsCollecttionDTO> list = new ArrayList<>();
        try {
            if (reqGoodsCollecttionDTOs != null && !reqGoodsCollecttionDTOs.isEmpty()) {
                dealList(reqGoodsCollecttionDTOs, list);
            }
        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("searchGoodsCollecttionList Exception Message", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }

        return list;
    }

    private void dealList(List<ReqGoodsCollecttionDTO> reqGoodsCollecttionDTOs, List<GoodsCollecttionDTO> list) {
        for (ReqGoodsCollecttionDTO reqGoodsCollecttionDTO : reqGoodsCollecttionDTOs) {
            GoodsCollecttionDTO dto = new GoodsCollecttionDTO();
            // 参数检查
            String objectId = reqGoodsCollecttionDTO.getObjectId();
            String objectType = reqGoodsCollecttionDTO.getObjectType();
            if (CsStringUtils.isBlank(objectId) || CsStringUtils.isBlank(objectType)) {
                throw new BizException(ResourceCode.PARAM_ERROR, "收藏对象编号和类型");
            }
            // 判断收藏类型
            if (objectType.equals(GoodsCollectionTypeEnum.LIST.code())) { // 商品
            } else if (objectType.equals(GoodsCollectionTypeEnum.DETAIL.code())) { // 资源
                listAddItems(objectId, dto, list);
            }
        }
    }

    private void listAddItems(String objectId, GoodsCollecttionDTO dto, List<GoodsCollecttionDTO> list) {
        Resource resource = resourceMapper.selectByPrimaryKey(objectId);
        if (resource != null) {
            dto.setObjectId(objectId);
            dto.setObjectType(GoodsCollectionTypeEnum.DETAIL.code());
            ResourceDTO resourceDTO = new ResourceDTO();
            BeanUtils.copyProperties(resource, resourceDTO);
            // 查询商品信息
            GoodsDTO goodsInfo = getGoodsInfo(resource.getGoodsId());
            GoodsResourceDTO goodsResourceDTO = new GoodsResourceDTO();
            BeanUtils.copyProperties(goodsInfo, goodsResourceDTO);
            dto.setGoodsResourceDTO(goodsResourceDTO);
            // 处理其他信息
            List<String> payWays = new ArrayList<>();
            String[] payWayArr = resource.getPayWay().split(",");
            if (payWayArr.length > 0) {
                Collections.addAll(payWays, payWayArr);
            }
            resourceDTO.setPayWay(payWays);
            boolean ifdown = resource.getFixDowntime() != null;
            resourceDTO.setIfdown(ifdown);

            resourceDTOAddValues(resource, resourceDTO);
            // 交易状态
            boolean tradeStatus = checkResourceTradeStatus(resource);
            if (tradeStatus) {
                resourceDTO.setTradeStatus(TradeStatusEnum.TRADE_STATUS100.code());
            } else {
                resourceDTO.setTradeStatus(TradeStatusEnum.TRADE_STATUS200.code());
            }
            resourceDTO.setGoodsDTO(goodsInfo);
            dto.setResourceDTO(resourceDTO);
            list.add(dto);
        }
    }

    private static void resourceDTOAddValues(Resource resource, ResourceDTO resourceDTO) {
        if (resource.getPaydateLimit() != null) {
            Long payDateLimit = Math.round(resource.getPaydateLimit() / HOUR);
            resourceDTO.setPaydateLimit(payDateLimit);
        }
        String takedateType = resource.getTakedateType();
        if (takedateType.equals(TakedateTypeEnum.TAKEDATE_TYPE1.code())) {
            resourceDTO.setTakedateType(takedateType);
        } else if (takedateType.equals(TakedateTypeEnum.TAKEDATE_TYPE2.code())) {
            resourceDTO.setTakedateType(takedateType);
            Long takeDateLimit = Math.round(resource.getTakedateLimit() / DAY);
            resourceDTO.setTakedateHour(null);
            resourceDTO.setTakedateLimit(takeDateLimit);
        } else if (takedateType.equals(TakedateTypeEnum.TAKEDATE_TYPE3.code())) {
            resourceDTO.setTakedateType(TakedateTypeEnum.TAKEDATE_TYPE2.code());
            long takeDateLimit = Math.round(resource.getTakedateLimit() / DAY);
            int takedateHourTmp = (int) (resource.getTakedateLimit() % DAY.longValue() / HOUR.longValue());
            // 根据前端选择规则，小时不会为空 TakedateTypeEnum.TAKEDATE_TYPE3
            resourceDTO.setTakedateLimit(takedateHourTmp != 0 ? takeDateLimit : takeDateLimit - 1);
            resourceDTO.setTakedateHour(takedateHourTmp != 0 ? takedateHourTmp : 24);
        }
    }

    /**
     * 判断是否中心仓发货
     *
     * @param buyNum
     * @param unit
     * @param goodsId
     * @return
     */
    private Boolean isCenterStoreSend(Integer categoryType, BigDecimal buyNum, String unit, String goodsId) {
        boolean isCenterStore = false;
        try {
            if (categoryType == CategoryTypeEnum.CEMENT.getCode()) {
                BigDecimal ratio = getRatio(goodsId, unit);
                if (buyNum.compareTo(getCenterStoreLimited().multiply(ratio)) < 0
                        || buyNum.compareTo(getCenterStoreLimited().multiply(ratio)) == 0) {
                    isCenterStore = true;
                }
            }
        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("isCenterStoreSend Exception Message", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
        return isCenterStore;
    }

    /**
     * 获取历史资源记录
     *
     * @param resourceId
     * @return
     */
    private List<ResourceHistory> getResourceHistoryList(String resourceId) {
        // 参数检查
        if (CsStringUtils.isBlank(resourceId)) {
            throw new BizException(ResourceCode.PARAM_NULL, "资源编号");
        }
        List<ResourceHistory> list;
        try {
            if (CsStringUtils.isBlank(resourceId)) {
                return null;
            }
            Condition condition = new Condition(ResourceHistory.class);
            Example.Criteria criteria = condition.createCriteria();
            criteria.andEqualTo(RESOURCE_ID, resourceId);
            criteria.andEqualTo(DEL_FLG, BooleanEnum.NO.code());
            list = resourceHistoryMapper.selectByCondition(condition);
        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("getTrResourceHistoryList Exception Message", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
        return list;
    }

    /**
     * 查询商品基本属性
     *
     * @return
     */
    private GoodsDTO getGoodsInfo(String goodsId) {
        try {
            GoodsDTO dto;
            if (CsStringUtils.isBlank(goodsId)) {
                throw new BizException(ResourceCode.DATA_NOT_FOUND, "商品ID不能为空");
            }
            dto = goodsService.findGoodsById(goodsId);
            if (dto == null) {
                throw new BizException(ResourceCode.DATA_NOT_FOUND, "商品信息不存在");
            }
            return dto;
        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("getGoodsInfo Exception Message", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
    }

    /**
     * 查询商品换算比率
     */
    private List<UnitConverDTO> getUnitConverInfo(String goodsId) {
        List<UnitConverDTO> unitConverDTOList;
        try {
            if (CsStringUtils.isBlank(goodsId)) {
                return null;
            }
            unitConverDTOList = goodsService.getUnitConverInfo(goodsId);
        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("getUnitConverInfo Exception Message", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
        return unitConverDTOList;
    }

    /**
     * 获取默认的换算率
     *
     * @param goodsId
     * @param unit
     * @return
     */
    private BigDecimal getRatio(String goodsId, String unit) {
        BigDecimal ratio = BigDecimal.ONE;
        List<UnitConverDTO> unitConverInfo = getUnitConverInfo(goodsId);
        for (UnitConverDTO unitConverDTO : Optional.ofNullable(unitConverInfo).orElse(Collections.emptyList())) {
            if (unitConverDTO.isDefault()) {
                if (unit.equals(unitConverDTO.getUnit1())) {
                    ratio = BigDecimal.ONE;
                } else if (unit.equals(unitConverDTO.getUnit2())) {
                    ratio = unitConverDTO.getRatio();
                }
            }
        }
        return ratio;
    }

    /**
     * 获取UnitId
     *
     * @param goodsId
     * @param unit
     * @return
     */
    private String getUnitId(String goodsId, String unit) {
        String unitId = "";
        List<UnitConverDTO> unitConverInfo = getUnitConverInfo(goodsId);
        for (UnitConverDTO unitConverDTO : Optional.ofNullable(unitConverInfo).orElse(Collections.emptyList())) {
            if (unitConverDTO.isDefault()) {
                if (unit.equals(unitConverDTO.getUnit1())) {
                    unitId = unitConverDTO.getUnitId1();
                } else if (unit.equals(unitConverDTO.getUnit2())) {
                    unitId = unitConverDTO.getUnitId2();
                }
            }
        }
        return unitId;
    }

    /**
     * 获取商品所有单位
     *
     * @param goodsId
     * @return
     */
    private List<String> getUnits(String goodsId) {
        List<String> units = new ArrayList<>();
        List<UnitConverDTO> unitConverInfo = getUnitConverInfo(goodsId);
        for (UnitConverDTO unitConverDTO : Optional.ofNullable(unitConverInfo).orElse(Collections.emptyList())) {
            if (!units.contains(unitConverDTO.getUnit1())) {
                units.add(unitConverDTO.getUnit1());
            }
        }
        return units;
    }

    /**
     * 查询卖家销售区域层级
     *
     * @param sellerId 卖家ID
     */
    private List<SaleLevelDTO> findSaleLevelByMemberId(String sellerId) {
        List<SaleLevelDTO> list;
        try {
            if (CsStringUtils.isBlank(sellerId)) {
                return null;
            }
            list = iSaleRegionService.findSaleLevelByMemberId(sellerId);
        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("findSaleLevelByMemberId Exception Message", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
        return list;
    }

    /**
     * 查询卖家某个层级的销售区域
     *
     * @param level     层级编号
     * @param accountId 操作人
     */
    private List<SaleRegionDTO> findDetailRegionbyLevel(Integer level, String accountId, List<String> filterIdList) {
        List<SaleRegionDTO> list;
        try {
            if (CsStringUtils.isBlank(accountId)) {
                return null;
            }
            log.info("findDetailRegionbyLevelAndAccountId req: {},{}", level, accountId);
            list = iSaleRegionService.findDetailRegionbyLevelAndAccountId(level, accountId);
        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("findDetailRegionbyLevelAndAccountId Exception Message", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
        return list;
    }

    /**
     * 查询销售区域详情
     *
     * @param saleRegionId 销售区域id
     * @return
     */
    private SaleRegionDTO findSaleRegionById(String saleRegionId) {
        try {
            SaleRegionDTO saleRegionDTO;
            if (CsStringUtils.isBlank(saleRegionId)) {
                throw new BizException(ResourceCode.DATA_NOT_EXIST, "销售地区ID不能为空");
            }
            saleRegionDTO = iSaleRegionService.findById(saleRegionId);
            if (saleRegionDTO == null) {
                throw new BizException(ResourceCode.DATA_NOT_EXIST, "销售地区信息不存在");
            }
            return saleRegionDTO;
        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("findSaleRegionById Exception Message", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
    }

    /**
     * 查询物流配送区域详情
     */
    private List<ZoneCarriageDTO> queryZoneCarriage(List<QueryZoneDTO> queryZoneDTOs, String queryType, String memberId) {
        List<ZoneCarriageDTO> list;
        try {
            QueryZoneCarriageDTO queryZoneCarriageDTO = new QueryZoneCarriageDTO();
            queryZoneCarriageDTO.setQueryZoneList(queryZoneDTOs);
            queryZoneCarriageDTO.setQueryType(queryType);
            queryZoneCarriageDTO.setMemberId(memberId);
            log.info("queryZoneCarriage2,req:" + queryZoneCarriageDTO.toString());
            list = warehouseService.queryZoneCarriage2(queryZoneCarriageDTO);
            log.info("queryZoneCarriage2,response:" + list.toString());
        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("findSaleRegionById Exception Message", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
        return list;
    }

    /**
     * 查询中心仓
     *
     * @param storeCode
     * @param privinceCode
     * @param cityCode
     * @param districtCode
     * @return
     */
    private WarehouseDetailsDTO queryStoreByCode(String storeCode, String privinceCode, String cityCode,
                                                 String districtCode, String memberId) {
        WarehouseDetailsDTO data;
        try {
            StoreQueryDTO queryDTO = new StoreQueryDTO();
            if (CsStringUtils.isNotBlank(memberId)) {
                queryDTO.setMemberId(memberId);
            }
            if (CsStringUtils.isNotBlank(storeCode)) {
                queryDTO.setStoreCode(storeCode);
            }
            if (CsStringUtils.isNotBlank(privinceCode)) {
                queryDTO.setProvinceCode(privinceCode);
            }
            if (CsStringUtils.isNotBlank(cityCode)) {
                queryDTO.setCityCode(cityCode);
            }
            if (CsStringUtils.isNotBlank(districtCode)) {
                queryDTO.setDistrictCode(districtCode);
            }
            log.info("查询门店中心仓库结果 {} ", queryDTO);
            data = warehouseService.queryStoreByCode(queryDTO);
            log.info("查询门店中心仓库结果 {} , {} , {} , {} , {}", storeCode, privinceCode, cityCode, districtCode, data);
        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("queryStoreByCode Exception Message", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
        return data;
    }

    /**
     * 查询中心仓
     *
     * @param storeCode
     * @param privinceCode
     * @param cityCode
     * @param districtCode
     * @return
     */
    private List<WarehouseDetailsDTO> queryStoreListByCode(String storeCode, String privinceCode, String cityCode,
                                                           String districtCode, String memberId) {
        List<WarehouseDetailsDTO> data;
        try {
            StoreQueryDTO queryDTO = new StoreQueryDTO();
            if (CsStringUtils.isNotBlank(memberId)) {
                queryDTO.setMemberId(memberId);
            }
            if (CsStringUtils.isNotBlank(storeCode)) {
                queryDTO.setStoreCode(storeCode);
            }
            if (CsStringUtils.isNotBlank(privinceCode)) {
                queryDTO.setProvinceCode(privinceCode);
            }
            if (CsStringUtils.isNotBlank(cityCode)) {
                queryDTO.setCityCode(cityCode);
            }
            if (CsStringUtils.isNotBlank(districtCode)) {
                queryDTO.setDistrictCode(districtCode);
            }
            log.info("查询门店中心仓库结果 {} ", queryDTO);
            data = warehouseService.queryStoreListByCode(queryDTO);
            log.info("查询门店中心仓库结果 {} , {} , {} , {} , {}", storeCode, privinceCode, cityCode, districtCode, data);
        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("queryStoreByCode Exception Message", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
        return data;
    }

    /**
     * 设置销售区域
     *
     * @param goodsResourceStoreDTO
     * @param level
     * @return
     */
    private GoodsResourceStoreDTO setSaleArea(GoodsResourceStoreDTO goodsResourceStoreDTO, SaleRegionDTO saleRegionDTO,
                                              Integer level) {
        goodsResourceStoreDTO.setSaleAreaRealCode(saleRegionDTO.getSaleRegionId());
        if (level == 1) {
            goodsResourceStoreDTO.setSaleAreaName(saleRegionDTO.getSaleRegionName());
            goodsResourceStoreDTO.setSaleAreaCode(saleRegionDTO.getSaleRegionId());
        } else if (level == 2) {
            goodsResourceStoreDTO.setSaleAreaName2(saleRegionDTO.getSaleRegionName());
            goodsResourceStoreDTO.setSaleAreaCode2(saleRegionDTO.getSaleRegionId());

            String saleRegionParentId = saleRegionDTO.getSaleRegionParentId();
            SaleRegionDTO saleRegionParent = findSaleRegionById(saleRegionParentId);
            goodsResourceStoreDTO.setSaleAreaCode(saleRegionParentId);
            goodsResourceStoreDTO.setSaleAreaName(saleRegionParent.getSaleRegionName());
        } else if (level == 3) {
            goodsResourceStoreDTO.setSaleAreaName3(saleRegionDTO.getSaleRegionName());
            goodsResourceStoreDTO.setSaleAreaCode3(saleRegionDTO.getSaleRegionId());

            String saleRegionParentId2 = saleRegionDTO.getSaleRegionParentId();
            SaleRegionDTO saleRegionParent2 = findSaleRegionById(saleRegionParentId2);
            goodsResourceStoreDTO.setSaleAreaCode2(saleRegionParentId2);
            goodsResourceStoreDTO.setSaleAreaName2(saleRegionParent2.getSaleRegionName());

            String saleRegionParentId = saleRegionParent2.getSaleRegionParentId();
            SaleRegionDTO saleRegionParent = findSaleRegionById(saleRegionParentId);
            goodsResourceStoreDTO.setSaleAreaCode(saleRegionParentId);
            goodsResourceStoreDTO.setSaleAreaName(saleRegionParent.getSaleRegionName());
        } else if (level == 4) {
            goodsResourceStoreDTO.setSaleAreaName4(saleRegionDTO.getSaleRegionName());
            goodsResourceStoreDTO.setSaleAreaCode4(saleRegionDTO.getSaleRegionId());

            String saleRegionParentId3 = saleRegionDTO.getSaleRegionParentId();
            SaleRegionDTO saleRegionParent3 = findSaleRegionById(saleRegionParentId3);
            goodsResourceStoreDTO.setSaleAreaCode3(saleRegionParentId3);
            goodsResourceStoreDTO.setSaleAreaName3(saleRegionParent3.getSaleRegionName());

            String saleRegionParentId2 = saleRegionParent3.getSaleRegionParentId();
            SaleRegionDTO saleRegionParent2 = findSaleRegionById(saleRegionParentId2);
            goodsResourceStoreDTO.setSaleAreaCode2(saleRegionParentId2);
            goodsResourceStoreDTO.setSaleAreaName2(saleRegionParent2.getSaleRegionName());

            String saleRegionParentId = saleRegionParent2.getSaleRegionParentId();
            SaleRegionDTO saleRegionParent = findSaleRegionById(saleRegionParentId);
            goodsResourceStoreDTO.setSaleAreaCode(saleRegionParentId);
            goodsResourceStoreDTO.setSaleAreaName(saleRegionParent.getSaleRegionName());
        } else if (level == 5) {
            goodsResourceStoreDTO.setSaleAreaCode5(saleRegionDTO.getSaleRegionId());
            goodsResourceStoreDTO.setSaleAreaName5(saleRegionDTO.getSaleRegionName());

            String saleRegionParentId4 = saleRegionDTO.getSaleRegionParentId();
            SaleRegionDTO saleRegionParent4 = findSaleRegionById(saleRegionParentId4);
            goodsResourceStoreDTO.setSaleAreaName4(saleRegionParent4.getSaleRegionName());
            goodsResourceStoreDTO.setSaleAreaCode4(saleRegionParentId4);

            String saleRegionParentId3 = saleRegionParent4.getSaleRegionParentId();
            SaleRegionDTO saleRegionParent3 = findSaleRegionById(saleRegionParentId3);
            goodsResourceStoreDTO.setSaleAreaCode3(saleRegionParentId3);
            goodsResourceStoreDTO.setSaleAreaName3(saleRegionParent3.getSaleRegionName());

            String saleRegionParentId2 = saleRegionParent3.getSaleRegionParentId();
            SaleRegionDTO saleRegionParent2 = findSaleRegionById(saleRegionParentId2);
            goodsResourceStoreDTO.setSaleAreaCode2(saleRegionParentId2);
            goodsResourceStoreDTO.setSaleAreaName2(saleRegionParent2.getSaleRegionName());

            String saleRegionParentId = saleRegionParent2.getSaleRegionParentId();
            SaleRegionDTO saleRegionParent = findSaleRegionById(saleRegionParentId);
            goodsResourceStoreDTO.setSaleAreaCode(saleRegionParentId);
            goodsResourceStoreDTO.setSaleAreaName(saleRegionParent.getSaleRegionName());
        }
        // 设置销售区域编码集
        goodsResourceStoreDTO.setSaleAreaCodes(goodsResourceStoreDTO.getSaleAreaCode()
                + goodsResourceStoreDTO.getSaleAreaCode2() + goodsResourceStoreDTO.getSaleAreaCode3()
                + goodsResourceStoreDTO.getSaleAreaCode4() + goodsResourceStoreDTO.getSaleAreaCode5());
        return goodsResourceStoreDTO;
    }

    /**
     * 聚合商品资源
     *
     * @param sellerGoodsId 卖家商品ID
     * @param operator      操作人
     */
    @Override
    public String aggregationGoodsResource(String sellerGoodsId, String sellerId, String operator, String brand) {
        try {
            // 参数检测
            if (CsStringUtils.isNullOrBlank(sellerGoodsId) || CsStringUtils.isNullOrBlank(operator)) {
                throw new BizException(ResourceCode.PARAM_ERROR, "品类ID或者操作员");
            }
            // 查询聚合商品资源(fixed 按卖家的标准商品维度进行聚合 2019-12-23 sup字段改为存储卖家商品ID)
            Example goodsResourceExample = new Example(GoodsResource.class);
            Example.Criteria goodsResourceCriteria = goodsResourceExample.createCriteria();
            goodsResourceCriteria.andEqualTo(SPU_ID, sellerGoodsId);
            goodsResourceCriteria.andEqualTo(SELLER_ID, sellerId);
            goodsResourceCriteria.andEqualTo(DEL_FLG, BooleanEnum.NO.code());
            GoodsResource goodsResource = goodsResourceMapper.selectOneByExample(goodsResourceExample);
            // 查询资源
            Example resourceExample = new Example(Resource.class);
            Example.Criteria resourceCriteria = resourceExample.createCriteria();
            resourceCriteria.andEqualTo(GOODS_ID, sellerGoodsId);
            resourceCriteria.andEqualTo(SELLER_ID, sellerId);
            resourceCriteria.andEqualTo(STATUS, ResourceStatusEnum.RES_STATUS100.code());
            resourceCriteria.andEqualTo(DEL_FLG, BooleanEnum.NO.code());
            List<Resource> resources = resourceMapper.selectByExample(resourceExample);
            // 查询商品信息
            GoodsDTO goodsInfo = getGoodsInfo(sellerGoodsId);
            String goodsResourceId = "";
            goodsResourceId = getGoodsResourceIds(sellerGoodsId, sellerId, operator, brand, goodsResource, resources, goodsInfo, goodsResourceId);

            // 更新goodsResourceId到resource表
            for (Resource resource : Optional.ofNullable(resources).orElse(Collections.emptyList())) {
                resource.setGoodsResourceId(goodsResourceId);
                resourceMapper.updateByPrimaryKeySelective(resource);
            }
            return goodsResourceId;
        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("aggregationGoodsResource Exception Message", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
    }

    private String getGoodsResourceIds(String sellerGoodsId, String sellerId, String operator, String brand, GoodsResource goodsResource, List<Resource> resources, GoodsDTO goodsInfo, String goodsResourceId) {if (goodsResource != null) { // 有，更新
                if (resources == null || resources.size() == 0) { // 无资源，逻辑删除聚合
                    goodsResource.setUpdateTime(new Date());
                    goodsResource.setUpdateUser(operator);
                    goodsResource.setDelFlg(true);
                    goodsResourceMapper.updateByPrimaryKeySelective(goodsResource);
                } else { // 有，重聚合
                    BeanUtils.copyProperties(goodsInfo, goodsResource);
                    goodsResource.setSearchKeywords(brand + goodsInfo.getSearchKeywords());
                    goodsResource.setBrand(brand);
                    goodsResource.setSpuId(sellerGoodsId);
                    goodsResource.setSellerId(sellerId);
                    String imgStr = "";
                    if (goodsInfo.getImgs() != null && goodsInfo.getImgs().length > 0) {
                        imgStr = CsStringUtils.join(goodsInfo.getImgs(), ",");
                    }
                    goodsResource.setImgs(imgStr);
                    String qualityStandardStr = goodsInfo.getQualityStandard();
                    goodsResource.setQualityStandard(qualityStandardStr);
                    String useRangeStr = goodsInfo.getUseRange();
                    goodsResource.setUseRange(useRangeStr);
                    // 设置聚合商品资源属性
                    setAggregationAttribute(goodsResource, resources);
                    goodsResource.setCreateUser(operator);
                    goodsResource.setCreateTime(goodsResource.getCreateTime());
                    goodsResource.setUpdateTime(new Date());
                    goodsResource.setUpdateUser(operator);
                    goodsResourceMapper.updateByPrimaryKeySelective(goodsResource);
                }
                goodsResourceId = goodsResource.getGoodsResourceId();
            } else { // 无，新增
                goodsResourceId = getGoodsResourceId(sellerGoodsId, sellerId, operator, brand, resources, goodsInfo, goodsResourceId);
        }
        return goodsResourceId;
    }

    private String getGoodsResourceId(String sellerGoodsId, String sellerId, String operator, String brand, List<Resource> resources, GoodsDTO goodsInfo, String goodsResourceId) {
        GoodsResource goodsResource;
        if (CollectionKit.isNotEmpty(resources)) {
            goodsResource = new GoodsResource();
            BeanUtils.copyProperties(goodsInfo, goodsResource);
            goodsResource.setSpuId(sellerGoodsId);
            String imgStr = "";
            if (goodsInfo.getImgs() != null && goodsInfo.getImgs().length > 0) {
                imgStr = CsStringUtils.join(goodsInfo.getImgs(), ",");
            }
            goodsResource.setImgs(imgStr);
            String qualityStandardStr = goodsInfo.getQualityStandard();
            goodsResource.setQualityStandard(qualityStandardStr);
            String useRangeStr = goodsInfo.getUseRange();
            goodsResource.setUseRange(useRangeStr);
            // 设置聚合商品资源属性
            setAggregationAttribute(goodsResource, resources);
            goodsResourceId = uuidGenerator.gain();
            goodsResource.setSellerId(sellerId);
            goodsResource.setGoodsResourceId(goodsResourceId);
            goodsResource.setSearchKeywords(brand + goodsInfo.getSearchKeywords());
            goodsResource.setBrand(brand);
            goodsResource.setCreateTime(new Date());
            goodsResource.setCreateUser(operator);
            goodsResource.setUpdateTime(new Date());
            goodsResource.setUpdateUser(operator);
            goodsResource.setDelFlg(false);
            goodsResourceMapper.insert(goodsResource);
        }
        return goodsResourceId;
    }

    /**
     * 设置聚合属性
     */
    private void setAggregationAttribute(GoodsResource goodsResource, List<Resource> resources) {

        // 商品属性
        String goodsAttributes = "";
        List<String> attributesList = new ArrayList<>();
        for (Resource resource : resources) {
            String resourceAttributeStr = "";
            String goodsId = resource.getGoodsId(); // sku
            // 查询商品属性
            List<GoodsCategoryAttrDTO> attrVals = goodsService.getCategoryAttrByGoodsId(goodsId);
            List<String> attributeStrs = new ArrayList<>();
            attributeStrsAdd(attrVals, attributeStrs);
            // 排序
            Collections.sort(attributeStrs);
            resourceAttributeStr = getResourceAttributeStr(attributeStrs, resourceAttributeStr);
            if (!attributesList.contains(resourceAttributeStr)) {
                attributesList.add(resourceAttributeStr);
            }
        }
        if (CollectionKit.isNotEmpty(attributesList)) {
            for (String attributeStr : attributesList) {
                goodsAttributes = goodsAttributes.concat(attributeStr).concat(MergeAttrPrefixEnum.OUTSIDE.getCode());
            }
        }
        goodsResource.setGoodsAttributes(goodsAttributes);

        // 区域属性
        String goodsRegions = "";
        String goodsRegions2 = "";
        List<ResourceRegion> resourceRegions = new ArrayList<>();
        Set<SaleRegionRelationDTO> regionSet = new HashSet<>();
        resourceRegionsAddValues(resources, regionSet, resourceRegions);
        goodsResourceSetValue(goodsResource, resourceRegions);

        // 资源属性
        String goodsResources = "";
        List<String> goodsResourcesList = new ArrayList<>();
        goodsResources = getGoodsResources(resources, goodsResourcesList, goodsResources);
        goodsResource.setGoodsResources(goodsResources);
    }

    private static String getGoodsResources(List<Resource> resources, List<String> goodsResourcesList, String goodsResources) {
        for (Resource resource : resources) {
            String resourceId = resource.getResourceId();
            String goodsResourceStr = resourceId;
            if (!goodsResourcesList.contains(goodsResourceStr)) {
                goodsResourcesList.add(goodsResourceStr);
            }
        }
        for (String goodsResourceStr : Optional.ofNullable(goodsResourcesList).orElse(Collections.emptyList())) {
            goodsResources = goodsResources.concat(goodsResourceStr).concat(MergeAttrPrefixEnum.OUTSIDE.getCode());
        }
        return goodsResources;
    }

    private void goodsResourceSetValue(GoodsResource goodsResource, List<ResourceRegion> resourceRegions) {
        String goodsRegions2;
        String goodsRegions;
        if (!CollectionUtils.isEmpty(resourceRegions)) {
            List<String> regionStrs = mergeGoodsRegion(resourceRegions);
            if (regionStrs != null && regionStrs.size() == 1) {
                goodsRegions = regionStrs.get(0);
                goodsResource.setGoodsRegions(goodsRegions);
            }
            if (regionStrs != null && regionStrs.size() > 1) {
                goodsRegions = regionStrs.get(0);
                goodsRegions2 = regionStrs.get(1);
                goodsResource.setGoodsRegions(goodsRegions);
                goodsResource.setGoodsRegions2(goodsRegions2);
            }
        }
    }

    private void resourceRegionsAddValues(List<Resource> resources, Set<SaleRegionRelationDTO> regionSet, List<ResourceRegion> resourceRegions) {
        for (Resource resource : resources) {
            SaleRegionDTO saleRegionDTO = iSaleRegionService.findById(resource.getSaleAreaRealCode());
            List<SaleRegionRelationDTO> regions = saleRegionDTO.getRegionDTOS();
            if (CollectionUtils.isEmpty(regions)) {
                continue;
            }
            for (SaleRegionRelationDTO regionDTO : regions) {
                if (regionSet.contains(regionDTO)) {
                    continue;
                }
                regionSet.add(regionDTO);
                List<String> streetCodeList = regionDTO.getStreetCode();
                resourceRegionsAddItems(resourceRegions, regionDTO, streetCodeList);
            }
        }
    }

    private static void resourceRegionsAddItems(List<ResourceRegion> resourceRegions, SaleRegionRelationDTO regionDTO, List<String> streetCodeList) {
        List<String> codes = Optional.ofNullable(streetCodeList).filter(CollectionKit::isNotEmpty).orElse(Collections.singletonList(null));
        for (String streetCode : codes) {
            ResourceRegion regionRecord = new ResourceRegion();
            BeanUtils.copyProperties(regionDTO, regionRecord);
            regionRecord.setAreaCode(regionDTO.getDistrictCode());
            regionRecord.setAreaName(regionDTO.getDistrictName());
            if (streetCode != null) {
                regionRecord.setStreetCode(streetCode);
            }
            resourceRegions.add(regionRecord);
        }
    }

    private static String getResourceAttributeStr(List<String> attributeStrs, String resourceAttributeStr) {
        for (String attributeStr : Optional.ofNullable(attributeStrs).orElse(Collections.emptyList())) {
            if (CsStringUtils.isNullOrBlank(resourceAttributeStr)) {
                resourceAttributeStr = attributeStr;
            } else {
                resourceAttributeStr = resourceAttributeStr.concat(MergeAttrPrefixEnum.GROUPSIDE.getCode())
                        .concat(attributeStr);
            }
        }
        return resourceAttributeStr;
    }

    private static void attributeStrsAdd(List<GoodsCategoryAttrDTO> attrVals, List<String> attributeStrs) {
        for (GoodsCategoryAttrDTO goodsAttributeDTO : Optional.ofNullable(attrVals).orElse(Collections.emptyList())) {
            String goodsAttriId = goodsAttributeDTO.getGoodsAttriId();
            String attriValueCode = goodsAttributeDTO.getValueCode();
            if (CsStringUtils.isNotBlank(goodsAttriId) && CsStringUtils.isNotBlank(attriValueCode)) {
                String attributeStr = goodsAttriId.concat(MergeAttrPrefixEnum.INSTIDE.getCode())
                        .concat(attriValueCode);
                attributeStrs.add(attributeStr);
            }
        }
    }

    /**
     * 区域聚合
     *
     * @param regions
     * @return
     */
    @Override
    public List<String> mergeGoodsRegion(List<ResourceRegion> regions) {
        log.info(" 开始聚合商品行政区域:" + JSON.toJSONString(regions));
        List<String> result = new ArrayList<>();

        Map<String, String> tempResult = new HashMap<>();
        String tmpResult = null;
        for (ResourceRegion resourceRegion : regions) {
            String country = resourceRegion.getCountryName();
            String province = resourceRegion.getProvinceCode();
            String city = resourceRegion.getCityCode();
            String area = resourceRegion.getAreaCode();
            String street = resourceRegion.getStreetCode();

            if (CsStringUtils.isNotEmpty(street)) {
                tmpResult = MergeRegionPrefixEnum.NOSUB.getCode() + street + MergeRegionPrefixEnum.ENDSUB.getCode();
                if (!tempResult.containsKey(tmpResult)) {
                    tempResult.put(tmpResult, tmpResult);
                }
            }

            dealTempResult(area, tempResult, street);

            dealTempResultCity(city, tempResult, area);

            dealTempResultProvince(province, tempResult, city);

            dealTempResultCountry(country, tempResult, province);

        }

        StringBuilder builder = new StringBuilder();
        for (java.util.Iterator<Map.Entry<String, String>> iterator = tempResult.entrySet().iterator(); iterator
                .hasNext(); ) {
            builder.append(iterator.next().getValue());
            if (builder.length() > 2000) {
                result.add(builder.toString());
                builder = new StringBuilder();
            }
        }
        if (result.isEmpty()) {
            result.add(builder.toString());
        }
        log.info("  商品行政区域聚合结果 {}", result);
        return result;
    }

    private static void dealTempResultCountry(String country, Map<String, String> tempResult, String province) {
        String tmpResult;
        if (CsStringUtils.isNotEmpty(country)) {
            tmpResult = MergeRegionPrefixEnum.NOSUB.getCode() + country + MergeRegionPrefixEnum.ENDSUB.getCode();
            if (!tempResult.containsKey(tmpResult)) {
                if (CsStringUtils.isEmpty(province)) {
                    tempResult.remove(MergeRegionPrefixEnum.HASSUB.getCode() + country + MergeRegionPrefixEnum.ENDSUB.getCode());
                    tempResult.put(MergeRegionPrefixEnum.NOSUB.getCode() + country + MergeRegionPrefixEnum.ENDSUB.getCode(),
                            MergeRegionPrefixEnum.NOSUB.getCode() + country + MergeRegionPrefixEnum.ENDSUB.getCode());
                } else {
                    tempResult.put(MergeRegionPrefixEnum.HASSUB.getCode() + country + MergeRegionPrefixEnum.ENDSUB.getCode(),
                            MergeRegionPrefixEnum.HASSUB.getCode() + country + MergeRegionPrefixEnum.ENDSUB.getCode());
                }
            }
        }
    }

    private static void dealTempResultProvince(String province, Map<String, String> tempResult, String city) {
        String tmpResult;
        if (CsStringUtils.isNotEmpty(province)) {
            tmpResult = MergeRegionPrefixEnum.NOSUB.getCode() + province + MergeRegionPrefixEnum.ENDSUB.getCode();
            if (!tempResult.containsKey(tmpResult)) {
                if (CsStringUtils.isEmpty(city)) {
                    tempResult.remove(MergeRegionPrefixEnum.HASSUB.getCode() + province + MergeRegionPrefixEnum.ENDSUB.getCode());
                    tempResult.put(MergeRegionPrefixEnum.NOSUB.getCode() + province + MergeRegionPrefixEnum.ENDSUB.getCode(),
                            MergeRegionPrefixEnum.NOSUB.getCode() + province + MergeRegionPrefixEnum.ENDSUB.getCode());
                } else {
                    tempResult.put(MergeRegionPrefixEnum.HASSUB.getCode() + province + MergeRegionPrefixEnum.ENDSUB.getCode(),
                            MergeRegionPrefixEnum.HASSUB.getCode() + province + MergeRegionPrefixEnum.ENDSUB.getCode());
                }
            }
        }
    }

    private static void dealTempResultCity(String city, Map<String, String> tempResult, String area) {
        String tmpResult;
        if (CsStringUtils.isNotEmpty(city)) {
            tmpResult = MergeRegionPrefixEnum.NOSUB.getCode() + city + MergeRegionPrefixEnum.ENDSUB.getCode();
            if (!tempResult.containsKey(tmpResult)) {
                if (CsStringUtils.isEmpty(area)) {
                    tempResult.remove(MergeRegionPrefixEnum.HASSUB.getCode() + city + MergeRegionPrefixEnum.ENDSUB.getCode());
                    tempResult.put(MergeRegionPrefixEnum.NOSUB.getCode() + city + MergeRegionPrefixEnum.ENDSUB.getCode(),
                            MergeRegionPrefixEnum.NOSUB.getCode() + city + MergeRegionPrefixEnum.ENDSUB.getCode());
                } else {
                    tempResult.put(MergeRegionPrefixEnum.HASSUB.getCode() + city + MergeRegionPrefixEnum.ENDSUB.getCode(),
                            MergeRegionPrefixEnum.HASSUB.getCode() + city + MergeRegionPrefixEnum.ENDSUB.getCode());
                }
            }
        }
    }

    private static void dealTempResult(String area, Map<String, String> tempResult, String street) {
        String tmpResult;
        if (CsStringUtils.isNotEmpty(area)) {
            tmpResult = MergeRegionPrefixEnum.NOSUB.getCode() + area + MergeRegionPrefixEnum.ENDSUB.getCode();
            if (!tempResult.containsKey(tmpResult)) {
                if (CsStringUtils.isEmpty(street)) {
                    tempResult.remove(MergeRegionPrefixEnum.HASSUB.getCode() + area + MergeRegionPrefixEnum.ENDSUB.getCode());
                    tempResult.put(MergeRegionPrefixEnum.NOSUB.getCode() + area + MergeRegionPrefixEnum.ENDSUB.getCode(),
                            MergeRegionPrefixEnum.NOSUB.getCode() + area + MergeRegionPrefixEnum.ENDSUB.getCode());
                } else {
                    tempResult.put(MergeRegionPrefixEnum.HASSUB.getCode() + area + MergeRegionPrefixEnum.ENDSUB.getCode(),
                            MergeRegionPrefixEnum.HASSUB.getCode() + area + MergeRegionPrefixEnum.ENDSUB.getCode());
                }
            }
        }
    }

    /**
     * 生成资源CODE
     *
     * @return
     */
    private String generatorResourceCode() {
        String defaultDateTimeStr = DateUtil.format(new Date(), "yyyyMMddHHmmss");
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < 6; i++) {
            result.append(RandomUtil.RANDOM.nextInt(10));
        }
        return "SP" + defaultDateTimeStr + result;
    }

    /**
     * 计算资源版本
     *
     * @param resourceId 资源ID
     * @return
     */
    @Override
    public Integer calculateResourceVersion(String resourceId) {
        Integer resourceVersion = 1;
        try {
            Example example = new Example(ResourceHistory.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo(RESOURCE_ID, resourceId);
            criteria.andEqualTo(DEL_FLG, BooleanEnum.NO.code());
            example.orderBy("resourceVersion").desc();
            List<ResourceHistory> histories = resourceHistoryMapper.selectByExample(example);
            if (histories != null && histories.size() > 0) {
                resourceVersion = histories.get(0).getResourceVersion() + 1;
            }
        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("calculateResourceVersion Exception Message", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
        return resourceVersion;
    }

    @Override
    public void batchUpdateByPrimaryKeySelective(List<Resource> resources) {
        if (!CollectionUtils.isEmpty(resources)) {
            resources.forEach(resource -> resourceMapper.updateByPrimaryKeySelective(resource));
        }
    }

    @Override
    public void batchUpdateResources(List<Resource> resources) {
        if (CollectionUtils.isEmpty(resources)) {
            return;
        }
        resources.forEach(resource -> resourceMapper.updateByPrimaryKey(resource));
    }

    @Override
    public PageInfo<Resource> pageResource(ReqResourceSellerDTO reqDto) {
        // 入参封装
        Example example = new Example(Resource.class);
        Example.Criteria criteria = example.createCriteria();
        criteriaAddParams2(reqDto, criteria);
        if (!CsStringUtils.isBlank(reqDto.getSaleAreaCode2())) {
            criteria.andEqualTo(SALE_AREA_CODE2, reqDto.getSaleAreaCode2());
        }
        if (!CsStringUtils.isBlank(reqDto.getSaleAreaCode3())) {
            criteria.andEqualTo(SALE_AREA_CODE3, reqDto.getSaleAreaCode3());
        }
        if (!CsStringUtils.isBlank(reqDto.getSaleAreaCode4())) {
            criteria.andEqualTo(SALE_AREA_CODE4, reqDto.getSaleAreaCode4());
        }
        if (!CsStringUtils.isBlank(reqDto.getSaleAreaCode5())) {
            criteria.andEqualTo(SALE_AREA_CODE5, reqDto.getSaleAreaCode5());
        }
        if (!CsStringUtils.isBlank(reqDto.getResourceCode())) {
            criteria.andLike(RESOURCE_CODE, "%" + reqDto.getResourceCode() + "%");
        }
        if (!CsStringUtils.isBlank(reqDto.getStoreName())) {
            criteria.andLike("storeName", "%" + reqDto.getStoreName() + "%");
        }
        if (reqDto.getIfProtocolPrice() != null) {
            criteria.andEqualTo(IF_PROTOCOL_PRICE, reqDto.getIfProtocolPrice());
        }
        if (reqDto.getStartTime() != null) {
            criteria.andGreaterThanOrEqualTo(UP_TIME, reqDto.getStartTime());
        }
        if (reqDto.getEndTime() != null) {
            criteria.andLessThanOrEqualTo(UP_TIME, reqDto.getEndTime());
        }
        if (!CsStringUtils.isBlank(reqDto.getStatus())) {
            criteria.andEqualTo(STATUS, reqDto.getStatus());
        }
        criteria.andEqualTo(DEL_FLG, BooleanEnum.NO.code());
        example.orderBy(UPDATE_TIME).desc();
        // 分页查询数据库
        PageInfo<Resource> pageInfo = PageMethod.startPage(reqDto.getPageNum(), reqDto.getPageSize(), true).doSelectPageInfo(() -> resourceMapper.selectByExample(example));
        return pageInfo;
    }

    private static void criteriaAddParams2(ReqResourceSellerDTO reqDto, Example.Criteria criteria) {
        if (!CsStringUtils.isBlank(reqDto.getSellerId())) {
            criteria.andEqualTo(SELLER_ID, reqDto.getSellerId());
        }
        if (!CsStringUtils.isBlank(reqDto.getGoodsType())) {
            criteria.andEqualTo(GOODS_TYPE, reqDto.getGoodsType());
        }
        if (!CsStringUtils.isBlank(reqDto.getGoodsDescribe())) {
            criteria.andLike("goodsName", "%" + reqDto.getGoodsDescribe() + "%");
        }
        if (!CsStringUtils.isBlank(reqDto.getCategoryCode())) {
            criteria.andLike(CATEGORY_CODE, reqDto.getCategoryCode() + "%");
        }
        if (!CsStringUtils.isBlank(reqDto.getSaleArea())) {
            criteria.andLike(SALE_AREA, "%" + reqDto.getSaleArea() + "%");
        }
        if (!CsStringUtils.isBlank(reqDto.getSaleAreaCode())) {
            criteria.andEqualTo(SALE_AREA_CODE, reqDto.getSaleAreaCode());
        }
    }

    @Override
    public PageInfo<Resource> pageResourceWithDataPerm(ReqResourceSellerDTO reqDto) {
        // 入参封装
        reqDto.setPageNum(reqDto.getPageNum() != null ? reqDto.getPageNum() : 1);
        reqDto.setPageSize(reqDto.getPageSize() != null ? reqDto.getPageSize() : 10 );

        return PageMethod.startPage(reqDto.getPageNum(), reqDto.getPageSize()).doSelectPageInfo(
                () -> resourceMapper.pageQueryWithDataPerm(reqDto));
    }

    /**
     * 获取商品定价模式
     *
     * @param goodsId
     * @return
     */
    private Integer getDafultPriceWay(String goodsId) {
        Integer priceWay;
        try {
            GoodsDTO goodsDTO = getGoodsInfo(goodsId);
            priceWay = goodsDTO.getPricingMode();
        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("getDafultPriceWay Exception Message", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
        return priceWay;
    }

    /**
     * 获取商品默认单位
     *
     * @param goodsId
     * @return
     */
    private String getDafultUnit(String goodsId) {
        String unit = "";
        try {
            // 查询商品基本属性
            GoodsDTO goodsDTO = getGoodsInfo(goodsId);
            // 获取默认单位
            List<UnitConverDTO> unitConverInfos = getUnitConverInfo(goodsId);
            if (unitConverInfos != null && !unitConverInfos.isEmpty()) {
                for (UnitConverDTO unitConverInfo : unitConverInfos) {
                    if (unitConverInfo.isDefault()) {
                        unit = unitConverInfo.getUnit1();
                    }
                }
            } else {
                unit = goodsDTO.getUnit();
            }
        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("getDafultUnit Exception Message", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
        return unit;
    }

    /**
     * 检查
     */
    private void checkUneffectiveResource(String resourceId) {
        // 插入未生效资源检查
        Example effectExample = new Example(ResourceUneffective.class);
        Example.Criteria effectExampleCriteria = effectExample.createCriteria();
        effectExampleCriteria.andEqualTo(DEL_FLG, BooleanEnum.NO.code());
        effectExampleCriteria.andEqualTo(RESOURCE_ID, resourceId);
        List<ResourceUneffective> uneffectives = resourceUneffectiveMapper.selectByExample(effectExample);
        if (uneffectives != null && uneffectives.size() > 0) {
            throw new BizException(ResourceCode.CAN_NOT_UPDATE, "不能修改该资源，当前挂牌资源有一条变更信息未生效！");
        }
    }

    /**
     * 获取中心仓限量，单位吨
     *
     * @return
     */
    private static BigDecimal getCenterStoreLimited() {
        return BigDecimal.valueOf(10.0);
    }

    /**
     * 判断资源是否可以交易
     */
    @Override
    public boolean checkResourceTradeStatus(Resource resource) {
        boolean tradeStatus = false;
        Date now = new Date();
        Date tradeStarttime = resource.getTradeStarttime();
        Date tradeEndtime = resource.getTradeEndtime();
        String status = resource.getStatus();
        if (status.equals(ResourceStatusEnum.RES_STATUS100.code()) && tradeStarttime == null && tradeEndtime == null) {
            tradeStatus = true;
        } else if (status.equals(ResourceStatusEnum.RES_STATUS100.code()) && tradeStarttime == null && now.before(tradeEndtime)) {
            tradeStatus = true;
        } else if (status.equals(ResourceStatusEnum.RES_STATUS100.code()) && tradeStarttime != null
                && now.after(tradeStarttime) && tradeEndtime == null) {
            tradeStatus = true;
        } else {
            tradeStatus = status.equals(ResourceStatusEnum.RES_STATUS100.code()) && tradeStarttime != null && tradeEndtime != null
                    && now.before(tradeEndtime) && now.after(tradeStarttime);
        }
        return tradeStatus;
    }

    @Override
    public ResourceDetailDTO searchResourceDetailWithAttr(ResourceDetailSearchDTO resourceDetailSearchDTO) {
        log.info(" 查询资源详情:" + JSON.toJSONString(resourceDetailSearchDTO));
        Resource oldResource = this.resourceMapper.selectByPrimaryKey(resourceDetailSearchDTO.getResourceId());
        log.info(" 原资源详情 {}", oldResource);
        if (oldResource == null || CsStringUtils.isEmpty(oldResource.getResourceId())) {
            throw new BizException(ResourceCode.DATA_NOT_FOUND, "未能查找到相关资源");
        }
        //查询goodsResourceId聚合的资源集合
        String goodsResourceId = oldResource.getGoodsResourceId();
        resourceDetailSearchDTO.setGoodsResourceId(goodsResourceId);
        Example example = new Example(Resource.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(STATUS, ResourceStatusEnum.RES_STATUS100.getCode());
        criteria.andEqualTo(DEL_FLG, BooleanEnum.NO.code());
        criteria.andEqualTo(GOODS_RESOURCE_ID, goodsResourceId);
        this.processResourceRegionsExample(RESOURCE_REGIONS, example, resourceDetailSearchDTO.getCountryName(),
                resourceDetailSearchDTO.getProvinceCode(), resourceDetailSearchDTO.getCityCode(),
                resourceDetailSearchDTO.getAreaCode(), resourceDetailSearchDTO.getStreetCode());

        criteriaAndLike(resourceDetailSearchDTO, criteria);
        List<Resource> resources = this.resourceMapper.selectByExample(example);// .searchResourceDetail(resourceDetailSearchDTO);
        log.info(" 查询结果 : {}", resources);
        //查询可以出售的
        ResourceDTO result = null;
        Resource resourceChoose = null;
        if (resources == null || resources.isEmpty()) { //如果查询结果为空，无货
            result = new ResourceDTO();
            BeanUtils.copyProperties(oldResource, result);
            resourceChoose = oldResource;
            result.setPayWay(Arrays.asList(oldResource.getPayWay().split(",")));
            result.setIsHaveGoods(0);
        } else { //按价格从低到高排序
            resources.sort((o1, o2) -> o1.getPrice().compareTo(o2.getPrice()));
            //设置购买数量
            BigDecimal quantity = BigDecimal.ZERO;
            if (resourceDetailSearchDTO.getBuyNum() != null) {
                quantity = resourceDetailSearchDTO.getBuyNum();
            }
            //查询可以选择购买的资源
            List<Resource> selectedResources = new ArrayList<>();
            List<String> selectedResourceIds = new ArrayList<>();
            for (Resource resource : resources) {
                //新的出货逻辑
                selectedResourceIdsAdd(resource, quantity, selectedResources, selectedResourceIds);
            }
            log.info(" 查询可以出售的结果 : {}", selectedResources);
            log.info(" 查询可以出售资源ID的结果 : {}", selectedResourceIds);
            if (!selectedResources.isEmpty()) {//有，选择一个最优惠的资源
                selectedResources.sort((o1, o2) -> o1.getPrice().compareTo(o2.getPrice()));
                result = new ResourceDTO();
                resourceChoose = getResourceChoose(selectedResourceIds, oldResource, selectedResources);
                BeanUtils.copyProperties(resourceChoose, result);
                result.setPayWay(Arrays.asList(resourceChoose.getPayWay().split(",")));
                result.setIsHaveGoods(1);
            }
            //无，无货
            if (result == null) {
                result = new ResourceDTO();
                resourceChoose = oldResource;
                BeanUtils.copyProperties(resourceChoose, result);
                result.setPayWay(Arrays.asList(resourceChoose.getPayWay().split(",")));
                result.setIsHaveGoods(0);
            }
        }
        Goods goods = iGoodsBiz.get(result.getGoodsId());
        log.info("资源详情 商品查询结果 {} ", goods);
        ResourceDetailDTO resultDTO = new ResourceDetailDTO();
        if (CsStringUtils.isNotBlank(goods.getImgs())) {
            resultDTO.setImgs(String.join(",", goods.getImgs()));
        }
        BeanConvertUtils.copyProperties(goods, resultDTO);
        BeanConvertUtils.copyProperties(result, resultDTO);
        resultDTO.setSellerName(result.getSellerName());
        resultDTOSetValues(result, resultDTO, goods);
        resultDTO.setSearchAttributes(this.getGoodsResourceAttributes(goodsResourceId, result.getGoodsId()));
        resultDTO.setCanSale(this.checkResourceTradeStatus(resourceChoose));
        resultDTO.setUnit(result.getSaleUnit());
        resultDTO.setUnitId(result.getSaleUnit());
        resultDTO.setIsHaveGoods(result.getIsHaveGoods());
        resultDTO.setPack(goods.getPack());
        resultDTO.setBrand(resourceChoose.getBrand());
        resultDTO.setSpecs(goods.getSpecs());
        resultDTO.setMark(goods.getMark());
        resultDTO.setOrderMinChangeNum(result.getOrderminchangeNum());
        resultDTO.setLogisticsId(goods.getLogistics());

        DynamicAttributeDTO dynamicAttributeDTO = goodsService.findDynamicAttribute(goods.getGoodsId());
        log.info("dynamicAttributeDTO:{}", JSON.toJSON(dynamicAttributeDTO));
        resultDTO.setManufacturer(dynamicAttributeDTO.getValue(DynamicAttributeEnum.MANUFACTURER.getCode()));
        resultDTO.setIsSteelBar(dynamicAttributeDTO.check(DynamicAttributeEnum.STEEL_BAR.getCode()));
        resultDTO.setIsSupportAddItem(goodsService.ifSupportAdditem(goods.getGoodsId()));
        resultDTO.setIsHidePrice(dynamicAttributeDTO.check(DynamicAttributeEnum.HIDE_PRICE.getCode()));
        resultDTO.setIsNeedSalesman(dynamicAttributeDTO.check(DynamicAttributeEnum.SALESMAN.getCode()));
        resultDTO.setIsCarry(goodsService.ifComputePorterage(goods.getGoodsId()));
        resultDTO.setIsTransactionTerms(dynamicAttributeDTO.check(DynamicAttributeEnum.TRANSACTION_TERMS.getCode()));
        resultDTO.setIsStoreDiscount(dynamicAttributeDTO.check(DynamicAttributeEnum.STORE_DISCOUNT.getCode()));
        resultDTO.setDeliveryTimeType(dynamicAttributeDTO.getValue(DynamicAttributeEnum.DELIVERY_TIME_TYPE.getCode()));

        log.info("资源详情 查询结果 {} ", resultDTO);
        return resultDTO;
    }

    private static void resultDTOSetValues(ResourceDTO result, ResourceDetailDTO resultDTO, Goods goods) {
        if (result.getIfSellerDelivery()) {
            resultDTO.setIfSellerDelivery(true);
        }
        if (result.getOrdermaxNum() != null) {
            resultDTO.setCansaleNum(result.getOrdermaxNum());
        }
        if (result.getOrderminNum() != null) {
            resultDTO.setSalesVolume(result.getOrderminNum());
        }
        if (CsStringUtils.isNotBlank(goods.getCartageRule())) {
            resultDTO.setIsHaveCartageRule(true);
        } else {
            resultDTO.setIsHaveCartageRule(false);
        }
    }

    private static Resource getResourceChoose(List<String> selectedResourceIds, Resource oldResource, List<Resource> selectedResources) {
        Resource resourceChoose;
        if (selectedResourceIds.contains(oldResource.getResourceId())) {
            resourceChoose = oldResource;
        } else {
            resourceChoose = selectedResources.get(0);
        }
        return resourceChoose;
    }

    private void selectedResourceIdsAdd(Resource resource, BigDecimal quantity, List<Resource> selectedResources, List<String> selectedResourceIds) {
        if (checkResourceTradeStatus(resource)) {
            if (resource.getCansaleNum() != null && resource.getCansaleNum().compareTo(BigDecimal.ZERO) > 0
                    && quantity.compareTo(resource.getCansaleNum()) <= 0) {
                if (resource.getOrderminNum() == null && resource.getOrdermaxNum() == null) {
                    selectedResources.add(resource);
                    selectedResourceIds.add(resource.getResourceId());
                } else if (resource.getOrderminNum() == null && resource.getOrdermaxNum() != null
                        && quantity.compareTo(resource.getOrdermaxNum()) <= 0) {
                    selectedResources.add(resource);
                    selectedResourceIds.add(resource.getResourceId());
                } else if (resource.getOrderminNum() != null && resource.getOrdermaxNum() == null
                        && quantity.compareTo(resource.getOrderminNum()) >= 0) {
                    selectedResources.add(resource);
                    selectedResourceIds.add(resource.getResourceId());
                } else if (resource.getOrderminNum() != null && resource.getOrdermaxNum() != null
                        && quantity.compareTo(resource.getOrderminNum()) >= 0 && quantity.compareTo(resource.getOrdermaxNum()) <= 0) {
                    selectedResources.add(resource);
                    selectedResourceIds.add(resource.getResourceId());
                }
            }
        }
    }

    private static void criteriaAndLike(ResourceDetailSearchDTO resourceDetailSearchDTO, Example.Criteria criteria) {
        if (resourceDetailSearchDTO.getAttributes() != null && !resourceDetailSearchDTO.getAttributes().isEmpty()) {
            for (AttributeDTO attr : resourceDetailSearchDTO.getAttributes()) {
                if (attr.getAttributeValueDTOs() != null) {
                    for (AttributeValueDTO attrValue : attr.getAttributeValueDTOs()) {
                        criteria.andLike(RESOURCE_ATTRIBUTES,
                                "%" + attr.getAttributeNo() + ":" + attrValue.getAttributeValueNo() + "%");
                    }
                }
            }
        }
    }

    @Override
    public void processResourceRegionsExample(String name, Example example, String countryName, String provinceName,
                                              String cityName, String areaName, String streetName) {
        List<String> regions = new ArrayList<>();
        if (CsStringUtils.isNotBlank(countryName)) {
            regions.add(countryName);
        }
        if (CsStringUtils.isNotBlank(provinceName)) {
            regions.add(provinceName);
        }
        if (CsStringUtils.isNotBlank(cityName)) {
            regions.add(cityName);
        }
        if (CsStringUtils.isNotBlank(areaName)) {
            regions.add(areaName);
        }
        if (CsStringUtils.isNotBlank(streetName)) {
            regions.add(streetName);
        }
        Example.Criteria criteria = example.createCriteria();
        if (!CollectionUtils.isEmpty(regions)) {
            for (int i = 0; i < regions.size(); i++) {
                if (i == regions.size() - 1) {
                    criteria.orLike(name, "%" + regions.get(i) + "\\" + MergeRegionPrefixEnum.ENDSUB.getCode() + "%");
                } else {
                    criteria.orLike(name, "%" + MergeRegionPrefixEnum.NOSUB.getCode() + regions.get(i)
                            + "\\" + MergeRegionPrefixEnum.ENDSUB.getCode() + "%");
                }
            }
            example.and(criteria);
        }
    }

    @Override
    public List<AttributeDTO> searchGoodsResourceAttributes(String goodsResourceId, String skuId) {
        log.info(" 查询商品聚合属性 {}， {} ", goodsResourceId, skuId);
        GoodsResource goodsResource = iResourceDetailAttrBiz.get(goodsResourceId);
        GoodsCategoryAttrQueryDTO query = new GoodsCategoryAttrQueryDTO();
        query.setDetailShow(true);
        List<GoodsCategoryAttrDTO> goodsAttrs = goodsService.getCategoryAttrByGoodsId(skuId, query);
        log.info(" 查询商品属性信息 {}  ", goodsAttrs);
        Map<String, GoodsCategoryAttrDTO> goodsAttrMap = new HashMap<>();
        if (goodsAttrs == null || goodsAttrs.isEmpty()) {
            log.error(" 查询商品属性信息失败  ");
            return null;
        }
        for (GoodsCategoryAttrDTO goodsAttr : goodsAttrs) {
            if (goodsAttr.getDetailShow()) {
                goodsAttrMap.put(goodsAttr.getGoodsAttriId(), goodsAttr);
            }
        }
        Map<String, List<String>> resourceAttrsMap = new HashMap<>();
        List<AttributeDTO> result = new ArrayList<>();
        resourceAttrsMapAddItem(goodsResourceId, skuId, goodsResource, resourceAttrsMap);
        log.info(" 聚合属性处理结果 {}  ", resourceAttrsMap);
        dealResourceAttrsMap(resourceAttrsMap, goodsAttrMap, result);
        return result;
    }

    private static void dealResourceAttrsMap(Map<String, List<String>> resourceAttrsMap, Map<String, GoodsCategoryAttrDTO> goodsAttrMap, List<AttributeDTO> result) {
        for (Map.Entry<String, List<String>> entry : resourceAttrsMap.entrySet()) {
            String attr = entry.getKey();
            List<String> valueIds = entry.getValue();

            AttributeDTO attributeDTO = new AttributeDTO();
            attributeDTO.setAttributeValueDTOs(new ArrayList<>());
            attributeDTO.setAttributeNo(attr);
            attributeDTO.setAttributeName(goodsAttrMap.get(attr).getAttriName());
            List<GoodsAttributeDTO> valueList = goodsAttrMap.get(attr).getValueList();
            log.info("valueIds:{}", valueIds);
            log.info("valueList:{}", valueList);
            attributeDTOAddValues(goodsAttrMap, valueIds, valueList, attr, attributeDTO);
            result.add(attributeDTO);
        }
    }

    private static void attributeDTOAddValues(Map<String, GoodsCategoryAttrDTO> goodsAttrMap, List<String> valueIds, List<GoodsAttributeDTO> valueList, String attr, AttributeDTO attributeDTO) {
        if (CollectionUtils.isEmpty(valueIds) || CollectionUtils.isEmpty(valueList)) {
            return;
        }
                for (String valueId : valueIds) {

                    GoodsAttributeDTO goodsAttributeValueDTO = null;
                    for (GoodsAttributeDTO goodsAttributeValue : valueList) {
                        if (goodsAttributeValue.getAttriValueCode().equals(valueId)) {
                            goodsAttributeValueDTO = goodsAttributeValue;
                        }
                    }

                    log.info(" 查找商品属性结果  {} ", goodsAttributeValueDTO);
                    if (goodsAttributeValueDTO != null) {
                        AttributeValueDTO attributeValueDTO = new AttributeValueDTO();

                if (goodsAttrMap.get(attr).getValueCode().equals(valueId)) {
                    attributeValueDTO.setSelected(true);
                }
                attributeValueDTO.setAttributeValue(goodsAttributeValueDTO.getAttriValue());
                attributeValueDTO.setAttributeValueNo(valueId);
                attributeDTO.getAttributeValueDTOs().add(attributeValueDTO);
            }
        }
    }

    private static void resourceAttrsMapAddItem(String goodsResourceId, String skuId, GoodsResource goodsResource, Map<String, List<String>> resourceAttrsMap) {
        if (Objects.isNull(goodsResource)) {
            log.info(" 无法找到相应的聚合商品 {}， {} ", goodsResourceId, skuId);
            return;
        }
        String attrs = goodsResource.getGoodsAttributes();
        log.info(" 商品聚合属性查询结果 {} ", attrs);
        if (CsStringUtils.isEmpty(attrs)) {
            return;
        }
        String[] group = attrs.split(MergeAttrPrefixEnum.OUTSIDE.getCode());
        for (String attributeGroup : group) {
            if (attributeGroup.contains(MergeAttrPrefixEnum.GROUPSIDE.getCode())) {
                String[] attrSet = attributeGroup.split(MergeAttrPrefixEnum.GROUPSIDE.getCode());
                resourcemapAdd(resourceAttrsMap, attrSet);
            }
        }
    }

    private static void resourcemapAdd(Map<String, List<String>> resourceAttrsMap, String[] attrSet) {
        for (String attr : attrSet) {
            String[] attrArray = attr.split(MergeAttrPrefixEnum.INSTIDE.getCode());
            if (!resourceAttrsMap.containsKey(attrArray[0])) {
                resourceAttrsMap.put(attrArray[0], new ArrayList<String>());
            }
            List<String> attrValue = resourceAttrsMap.get(attrArray[0]);
            boolean needAdd = true;
            for (String oldValue : attrValue) {
                if (oldValue.equals(attrArray[1])) {
                    needAdd = false;
                    break;
                }
            }
            if (needAdd) {
                resourceAttrsMap.get(attrArray[0]).add(attrArray[1]);
            }
        }
    }

    @Override
    public List<AttributeDTO> getGoodsResourceAttributes(String goodsResourceId, String skuId) {
        //初始化结果
        List<AttributeDTO> result = new ArrayList<>();
        //查询商品聚合信息
        GoodsResource goodsResource = iResourceDetailAttrBiz.get(goodsResourceId);
        Map<String, List<String>> resourceAttrsMap = new HashMap<>();
        if (goodsResource == null) {
            log.info(" 无法找到相应的聚合商品 {} ", goodsResourceId);
            return result;
        }
        String attrs = goodsResource.getGoodsAttributes();
        log.info(" 聚合商品属性查询结果 {} ", attrs);
        if (CsStringUtils.isEmpty(attrs)) {
            return result;
        }
        String[] group = attrs.split(MergeAttrPrefixEnum.OUTSIDE.getCode());
        for (String attributeGroup : group) {
            dealResourceAttrsMap(attributeGroup, resourceAttrsMap);
        }
        //
        Map<String, List<String>> resourceAttrsMap2 = new HashMap<>();
        if (MapUtils.isNotEmpty(resourceAttrsMap)) {
            resourceAttrsMap.keySet().stream().forEach(key -> {
                if (resourceAttrsMap.get(key).size() > 1) {
                    resourceAttrsMap2.put(key, resourceAttrsMap.get(key));
                }
            });
        }
        log.info(" 聚合属性处理结果 {}  ", resourceAttrsMap2);
        resourceAttrsMap2.keySet().stream().forEach(key -> {
            AttributeDTO attributeDTO = new AttributeDTO();
            GoodsCategoryAttribute goodsCategoryAttr = new GoodsCategoryAttribute();
            goodsCategoryAttr.setGoodsAttriId(key);
            goodsCategoryAttr.setSpu(false);
            goodsCategoryAttr.setDelFlg(false);
            goodsCategoryAttr.setDetailShow(true);
            List<GoodsCategoryAttribute> goodsCategoryAttributes = goodsCategoryAttrBiz.find(goodsCategoryAttr);
            reslutAddItems(skuId, key, goodsCategoryAttributes, attributeDTO, resourceAttrsMap2, result);
        });
        return result;
    }

    private void reslutAddItems(String skuId, String key, List<GoodsCategoryAttribute> goodsCategoryAttributes, AttributeDTO attributeDTO, Map<String, List<String>> resourceAttrsMap2, List<AttributeDTO> result) {
        if (CollectionKit.isNotEmpty(goodsCategoryAttributes)) {
            GoodsCategoryAttribute goodsCategoryAttribute = goodsCategoryAttributes.get(0);
            attributeDTO.setAttributeName(goodsCategoryAttribute.getAttriName());
            attributeDTO.setAttributeNo(key);
            List<AttributeValueDTO> attributeValueDTOs = Lists.newArrayList();
            resourceAttrsMap2.get(key).stream().forEach(value -> {
                AttributeValueDTO attributeValueDTO = new AttributeValueDTO();
                attributeValueDTO.setAttributeValueNo(value);

                GoodsAttributeValue goodsAttributeVal = new GoodsAttributeValue();
                goodsAttributeVal.setGoodsAttriId(key);
                goodsAttributeVal.setAttriValueCode(value);
                goodsAttributeVal.setDelFlg(false);
                List<GoodsAttributeValue> goodsAttributeValues = goodsAttributeValueBiz.find(goodsAttributeVal);
                if (CollectionKit.isNotEmpty(goodsAttributeValues)) {
                    GoodsAttributeValue goodsAttributeValue = goodsAttributeValues.get(0);
                    attributeValueDTO.setAttributeValue(goodsAttributeValue.getAttriValue());
                    GoodsDTO goodsInfo = getGoodsInfo(skuId);
                    String tableField = goodsCategoryAttribute.getTableField();
                    if (!CsStringUtils.isNullOrBlank(tableField)) {
                        String getMothod = "get" + tableField.substring(0, 1).toUpperCase() + tableField.substring(1);
                        log.info(" =====getMothod==== {}  ", getMothod);
                        Object fieldValue = ReflectionUtils.invokeMethod(ReflectionUtils.findMethod(GoodsDTO.class, getMothod), goodsInfo);
                        attributeValueDTOSetSelected(fieldValue, goodsAttributeValue, attributeValueDTO);
                    }
                }
                attributeValueDTOs.add(attributeValueDTO);
            });
            attributeDTO.setAttributeValueDTOs(attributeValueDTOs);
            result.add(attributeDTO);
        }
    }

    private static void attributeValueDTOSetSelected(Object fieldValue, GoodsAttributeValue goodsAttributeValue, AttributeValueDTO attributeValueDTO) {
        if (fieldValue != null) {
            log.info(" =====fieldValue==== {}  ", fieldValue.toString());
            if (fieldValue.toString().equals(goodsAttributeValue.getAttriValue())) {
                attributeValueDTO.setSelected(true);
            } else {
                attributeValueDTO.setSelected(false);
            }
        } else {
            attributeValueDTO.setSelected(false);
        }
    }

    private static void dealResourceAttrsMap(String attributeGroup, Map<String, List<String>> resourceAttrsMap) {
        if (attributeGroup.contains(MergeAttrPrefixEnum.GROUPSIDE.getCode())) {
            String[] attrSet = attributeGroup.split(MergeAttrPrefixEnum.GROUPSIDE.getCode());
            for (String attr : attrSet) {
                String[] attrArray = attr.split(MergeAttrPrefixEnum.INSTIDE.getCode());
                List<String> resourceAttrsValues = Lists.newArrayList();
                resourceAttrsValues.add(attrArray[1]);
                if (!resourceAttrsMap.containsKey(attrArray[0])) {
                    resourceAttrsMap.put(attrArray[0], resourceAttrsValues);
                } else {
                    if (!resourceAttrsMap.get(attrArray[0]).contains(attrArray[1])) {
                        resourceAttrsMap.get(attrArray[0]).add(attrArray[1]);
                    }
                }
            }
        }
    }

    @Override
    public boolean checkResourceLogisticRule(List<String> resourceIds) {
        if (resourceIds == null || resourceIds.isEmpty()) {
            throw new BizException(ResourceCode.INVALID_PARAM, "资源参数为空");
        }
        List<Resource> resources = new ArrayList<>();
        for (String resourceId : resourceIds) {
            resources.add(this.get(resourceId));

        }
        return checkResourceLogisticRuleData(resources);
    }

    @Override
    public List<SellerShopDTO> querySellerShopByGoodsType(ReqSellerShopDTO req) {
        log.info("ReqSellerShopDTO:" + JSON.toJSONString(req));
        //初始化出参
        List<SellerShopDTO> list = Lists.newArrayList();
        //根据商品分类查询资源信息
        Example example = new Example(Resource.class);
        Example.Criteria criteria = example.createCriteria();
        this.processResourceRegionsExample(RESOURCE_REGIONS, example, req.getCountryName(),
                req.getProvinceCode(), req.getCityCode(), req.getAreaCode(), req.getStreetCode());
        if (CsStringUtils.isNotBlank(req.getCategoryId())) {
            GoodsCategory goodsCategory = goodsCategoryBiz.get(req.getCategoryId());
            if (goodsCategory != null) {
                criteria.andLike(CATEGORY_CODE, goodsCategory.getCategoryCode() + "%");
            }
        }

        criteria.andEqualTo(DEL_FLG, BooleanEnum.NO.code());
        criteria.andEqualTo(STATUS, ResourceStatusEnum.RES_STATUS100.code());
        List<Resource> resources = resourceMapper.selectByExample(example);
        //封装结果
        List<Resource> resourceList = Lists.newArrayList();

        if (resources != null && resources.size() > 0) {
            // 先把有客户关系的添加到resourceList里面
            resourceListAdd(req, resources, resourceList);

            for (Resource resource : resources) {
                boolean b = resourceList.stream().anyMatch(u -> u.getSellerId().equals(resource.getSellerId()));
                if (!b) {
                    resourceList.add(resource);
                }
            }
        }
        resourceList.forEach(resource -> {
            SellerShopDTO sellerShopDTO = new SellerShopDTO();
            sellerShopDTO.setSellerId(resource.getSellerId());
            sellerShopDTO.setSellerName(resource.getSellerName());
            sellerShopDTOSetSellerNickName(resource, sellerShopDTO);
            list.add(sellerShopDTO);
        });
        return list;
    }

    private static void sellerShopDTOSetSellerNickName(Resource resource, SellerShopDTO sellerShopDTO) {
        if (CsStringUtils.isNotBlank(resource.getSellerNickName())) {
            sellerShopDTO.setSellerNickName(resource.getSellerNickName());
        } else {
            sellerShopDTO.setSellerNickName(resource.getSellerName());
        }
    }

    private void resourceListAdd(ReqSellerShopDTO req, List<Resource> resources, List<Resource> resourceList) {
        if (CsStringUtils.isNotBlank(req.getSearchBuyerId())) {
            List<String> sellerIdList = memberRelationService.findSellerIdByCustomerId(req.getSearchBuyerId());
            for (Resource resource : Optional.ofNullable(resources).orElse(Collections.emptyList())) {
                if (sellerIdList.contains(resource.getSellerId())) {
                    sellerIdList.remove(resource.getSellerId());
                    resourceList.add(resource);
                }
            }
        }
    }

    @Override
    public PageInfo<GoodsResourceListDTO> searchGoodsResourceEmall(ReqGoodsResourceDTO req) {
        if (req.getPageNum() == null) {
            throw new BizException(ResourceCode.PARAM_NULL, "页码");
        }
        if (req.getPageNum() <= 0) {
            throw new BizException(ResourceCode.PARAM_ERROR, PAGE_NUM_BEGIN_FROM_1);
        }
        if (req.getPageNum() <= 0) {
            throw new BizException(ResourceCode.PARAM_ERROR, PAGE_SIZE_MUST_MORE_THAN_0);
        }
        if (req.getPageSize() == null) {
            throw new BizException(ResourceCode.PARAM_NULL, "页面大小");
        }
        // 初始化结果
        PageInfo<GoodsResourceListDTO> pageData = new PageInfo<>();
        try {
            long start = System.currentTimeMillis();
            pageData = resourceElasticsearchBiz.esSearchResource(req);
            long end = System.currentTimeMillis();
            long spend = end - start;
            log.info("======searchGoodsResourceEmall-->spend time{} ms", spend);
        } catch (BizException e) {
            throw new BizException(ResourceCode.UNKNOWN_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("searchGoodsResourceEmall Exception Message", e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e);
        }
        return pageData;
    }

    @Override
    public List<PromptOnsaleResourceDTO> promptOnsaleResource(ReqPromptOnsaleResourceDTO reqPromptOnsaleResourceDTO) {
        //参数检查
        if (reqPromptOnsaleResourceDTO == null) {
            throw new BizException(ResourceCode.PARAM_NULL, "入参");
        }
        String goodsId = reqPromptOnsaleResourceDTO.getGoodsId();
        String spuId = reqPromptOnsaleResourceDTO.getSpuId();
        if (CsStringUtils.isNullOrBlank(goodsId) || CsStringUtils.isNullOrBlank(spuId)) {
            throw new BizException(ResourceCode.PARAM_NULL, "入参");
        }
        //初始化结果
        List<PromptOnsaleResourceDTO> list = Lists.newArrayList();
        //查询结果
        Example example = new Example(Resource.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(SPU_ID, spuId);
        criteria.andEqualTo(GOODS_ID, goodsId);
        criteria.andEqualTo(DEL_FLG, BooleanEnum.NO.code());
        List<Resource> resourceList = resourceMapper.selectByExample(example);
        for (Resource resource : Optional.ofNullable(resourceList).orElse(Collections.emptyList())) {
            PromptOnsaleResourceDTO dto = new PromptOnsaleResourceDTO();
            BeanUtils.copyProperties(resource, dto);
            list.add(dto);
        }
        return list;
    }

    @Override
    public List<PromptOnsaleResourceDTO> promptOnsaleResourceWithDataPerm(ReqPromptOnsaleResourceDTO reqPromptOnsaleResourceDTO) {
        //参数检查
        if (reqPromptOnsaleResourceDTO == null) {
            throw new BizException(ResourceCode.PARAM_NULL, "入参");
        }
        String goodsId = reqPromptOnsaleResourceDTO.getGoodsId();
        String spuId = reqPromptOnsaleResourceDTO.getSpuId();
        if (CsStringUtils.isNullOrBlank(goodsId) || CsStringUtils.isNullOrBlank(spuId)) {
            throw new BizException(ResourceCode.PARAM_NULL, "入参");
        }
        //初始化结果
        List<PromptOnsaleResourceDTO> list = Lists.newArrayList();
        List<Resource> resourceList = resourceMapper.promptOnsaleResourceWithDataPerm(reqPromptOnsaleResourceDTO);
        for (Resource resource : Optional.ofNullable(resourceList).orElse(Collections.emptyList())) {
            PromptOnsaleResourceDTO dto = new PromptOnsaleResourceDTO();
            BeanUtils.copyProperties(resource, dto);
            list.add(dto);
        }
        return list;
    }

    @Override
    public void manualResourceToElasticsearch() {
        long start = System.currentTimeMillis();
        log.info("====manualResourceToElasticsearch====start====");
        log.info(" === reindex begin");
        Example example = new Example(Resource.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(DEL_FLG, BooleanEnum.NO.code());
        List<Resource> resourceList = resourceMapper.selectByExample(example);
        resourceElasticsearchBiz.refreshAll(resourceList);
        long end = System.currentTimeMillis();
        log.info(" === reindex end.");
        log.info("====manualResourceToElasticsearch====end====");
        log.info("====manualResourceToElasticsearch====spend time {} ms", (end - start));
    }

    @Override
    public List<TransactionPriceDTO> todayTransactionPrice(TransactionPriceReqDTO transactionPriceReqDTO) {
        //参数检查
        if (CsStringUtils.isNullOrBlank(transactionPriceReqDTO.getSellerId())) {
            throw new BizException(BasicCode.PARAM_NULL, "卖家ID");
        }
        if (CsStringUtils.isNullOrBlank(transactionPriceReqDTO.getCategoryCode())) {
            throw new BizException(BasicCode.PARAM_NULL, "分类编码");
        }
        //初始化结果
        List<TransactionPriceDTO> list = Lists.newArrayList();
        //
        Condition categoryCondition = new Condition(GoodsCategory.class);
        Example.Criteria categoryConditionCriteria = categoryCondition.createCriteria();
        categoryConditionCriteria.andEqualTo(DEL_FLG, false);
        categoryConditionCriteria.andLike(CATEGORY_CODE, "%" + transactionPriceReqDTO.getCategoryCode() + "%");
        categoryCondition.orderBy(CATEGORY_CODE).asc();
        List<GoodsCategory> categories = goodsCategoryBiz.findByCondition(categoryCondition);
        //查询挂牌资源
        Example resourceExample = new Example(Resource.class);
        Example.Criteria resourceExampleCriteria = resourceExample.createCriteria();
        resourceExampleCriteria.andEqualTo(DEL_FLG, false);
        resourceExampleCriteria.andEqualTo(SELLER_ID, transactionPriceReqDTO.getSellerId());
        resourceExampleCriteria.andLike(CATEGORY_CODE, "%" + transactionPriceReqDTO.getCategoryCode() + "%");
        List<Resource> resourceList = resourceMapper.selectByExample(resourceExample);
        if (!CollectionUtils.isEmpty(resourceList)) {
            categories.forEach(category -> resourceList.forEach(resource -> {
                TransactionPriceDTO transactionPriceDTO = new TransactionPriceDTO();
                if (CsStringUtils.isNotBlank(resource.getCategoryCode()) && category.getCategoryCode().equals(resource.getCategoryCode())) {
                    transactionPriceDTO.setKey(category.getCategoryId());
                    transactionPriceDTO.setName(category.getCategoryName());
                    transactionPriceDTO.setUnit(resource.getPriceUnit());
                    transactionPriceDTO.setValue(resource.getPrice().multiply(getRatio(resource.getGoodsId(), resource.getSaleUnit())));
                    list.add(transactionPriceDTO);
                }
            }));
        }
        Collections.sort(list);
        return list;
    }

    @Override
    public EmallResourceDetailDTO queryEmallResourceDetail(EmallResourceDetailReqDTO req) {
        log.info("===queryEmallResourceDetail===->{}", JSON.toJSONString(req));
        //参数检测
        if (CsStringUtils.isNullOrBlank(req.getGoodsResourceId())) {
            throw new BizException(BasicCode.PARAM_NULL, "聚合商品ID");
        }
        EmallResourceDetailDTO emallResourceDetailDTO = new EmallResourceDetailDTO();
        //原商品
        Resource oldResource = null;
        Resource newResource = null;
        oldResource = getOldResource(req, oldResource);
        EmallResourceDetailDTO emallResourceDetailDTO1 = getEmallResourceDetailDTO(oldResource, emallResourceDetailDTO);
        if (emallResourceDetailDTO1 != null) return emallResourceDetailDTO1;
        log.info("===1===oldResource===->{}", JSON.toJSONString(oldResource));

        //初始化结果
        BigDecimal minArrivePrice = BigDecimal.ZERO;
        BigDecimal maxArrivePrice = BigDecimal.ZERO;
        Map<String, StoreDTO> storeMap = new HashMap<>();

        //逻辑处理
        Example resourceExample = new Example(Resource.class);
        Example.Criteria resourceCriteria = resourceExample.createCriteria();
        resourceCriteria.andEqualTo(DEL_FLG, BooleanEnum.NO.code());
        resourceCriteria.andEqualTo(STATUS, ResourceStatusEnum.RES_STATUS100.getCode());
        resourceCriteria.andEqualTo(GOODS_RESOURCE_ID, req.getGoodsResourceId());
        resourceCriteriaAddandLike(req, resourceCriteria);
        this.processResourceRegionsExample(RESOURCE_REGIONS, resourceExample, req.getCountryName(),
                req.getProvinceCode(), req.getCityCode(), req.getAreaCode(), req.getStreetCode());
        List<Resource> selectResources = resourceMapper.selectByExample(resourceExample);
        log.info("===2===selectResources===->{}", JSON.toJSONString(selectResources));

        //判断是否有货
        //查询可以选择购买的资源
        List<Resource> cansaleResources = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(selectResources)) { //有货
            //设置购买数量
            BigDecimal quantity = BigDecimal.ZERO;
            if (req.getQuantity() != null) {
                quantity = req.getQuantity();
            }
            cansaleResourcesAddItems(req, selectResources, quantity, cansaleResources);
            //价格排序
            cansaleResources.sort((o1, o2) -> o1.getPrice().compareTo(o2.getPrice()));
            log.info("===3===cansaleResources===->{}", JSON.toJSONString(cansaleResources));
            newResource = getResource(req, cansaleResources, newResource, storeMap);
        }

        //计算价格
        newResource = getNewResource(req, newResource, cansaleResources, minArrivePrice, maxArrivePrice, oldResource, emallResourceDetailDTO, storeMap);

        Goods goods = iGoodsBiz.get(emallResourceDetailDTO.getGoodsId());
        log.info("===4===goods===->{}", JSON.toJSONString(goods));
        //查询商品属性
        //查询商品分类基本属性
        Condition categoryAttributeCondition = new Condition(GoodsCategoryAttribute.class);
        Example.Criteria categoryAttributeCriteria = categoryAttributeCondition.createCriteria();
        categoryAttributeCriteria.andEqualTo(DEL_FLG, false);
        categoryAttributeCriteria.andEqualTo("spu", false);
        categoryAttributeCriteria.andEqualTo("categoryType", goods.getCategoryType());
        List<GoodsCategoryAttribute> goodsCategoryAttributeList = goodsCategoryAttrBiz.findByCondition(categoryAttributeCondition);
        if (CollectionKit.isEmpty(goodsCategoryAttributeList)) {
            Condition conditionAll = new Condition(GoodsCategoryAttribute.class);
            Example.Criteria criteriaAll = conditionAll.createCriteria();
            criteriaAll.andEqualTo(DEL_FLG, false);
            criteriaAll.andEqualTo("spu", false);
            criteriaAll.andIsNull("categoryType");
            conditionAll.orderBy("sort").asc();
            goodsCategoryAttributeList = goodsCategoryAttrBiz.findByCondition(conditionAll);
        }
        List<CategoryAttributeDTO> categoryAttributeDTOS = Lists.newArrayList();
        List<String> goodsAttriIds = Lists.newArrayList();
        goodsCategoryAttributeList.forEach(goodsCategoryAttribute -> {
            goodsAttriIds.add(goodsCategoryAttribute.getGoodsAttriId());
            CategoryAttributeDTO categoryAttributeDTO = new CategoryAttributeDTO();
            BeanUtils.copyProperties(goodsCategoryAttribute, categoryAttributeDTO);

            Condition condition = new Condition(GoodsAttributeValue.class);
            Example.Criteria criteria = condition.createCriteria();
            criteria.andEqualTo(DEL_FLG, false);
            criteria.andEqualTo("goodsAttriId", goodsCategoryAttribute.getGoodsAttriId());
            criteria.andEqualTo(GOODS_ID, goods.getGoodsId());
            List<GoodsAttributeValue> goodsAttributeValues = goodsAttributeValueMapper.selectByCondition(condition);
            List<CategoryAttributeValueDTO> categoryAttributeValueDTOs = Lists.newArrayList();
            goodsAttributeValues.forEach(goodsAttributeValue -> {
                CategoryAttributeValueDTO categoryAttributeValueDTO = new CategoryAttributeValueDTO();
                BeanUtils.copyProperties(goodsAttributeValue, categoryAttributeValueDTO);
                categoryAttributeValueDTO.setAttributeValue(goodsAttributeValue.getAttriValue());
                categoryAttributeValueDTO.setAttributeValueCode(goodsAttributeValue.getAttriValueCode());
                categoryAttributeValueDTOs.add(categoryAttributeValueDTO);
            });
            if (CollectionKit.isNotEmpty(categoryAttributeValueDTOs)) {
                categoryAttributeDTO.setCategoryAttributeValueDTOs(categoryAttributeValueDTOs);
                categoryAttributeDTOS.add(categoryAttributeDTO);
            }
        });
        emallResourceDetailDTO.setCategoryAttributeDTOS(categoryAttributeDTOS);
        //查询商品扩展属性
        Condition condition = new Condition(GoodsAttributeValue.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(DEL_FLG, false);
        criteria.andEqualTo(GOODS_ID, goods.getGoodsId());
        criteria.andNotIn("goodsAttriId", goodsAttriIds);
        List<GoodsAttributeValue> goodsAttributeValues = goodsAttributeValueMapper.selectByCondition(condition);
        List<GoodsAttributeDTO> goodsAttributeDTOS = Lists.newArrayList();
        Optional.ofNullable(goodsAttributeValues)
                .orElse(Collections.emptyList())
                .forEach(goodsAttributeValue -> {
                    GoodsAttributeDTO goodsAttributeDTO = new GoodsAttributeDTO();
                    BeanUtils.copyProperties(goodsAttributeValue, goodsAttributeDTO);
                    goodsAttributeDTOS.add(goodsAttributeDTO);
                });
        emallResourceDetailDTO.setGoodsAttributeDTOS(goodsAttributeDTOS);
        emallResourceDetailDTO.setAppMemo1(goods.getAppMemo1());
        emallResourceDetailDTO.setAppMemo2(goods.getAppMemo2());
        emallResourceDetailDTO.setAppMemo3(goods.getAppMemo3());
        emallResourceDetailDTO.setAppMemo4(goods.getAppMemo4());
        emallResourceDetailDTO.setAppMemo5(goods.getAppMemo5());
        emallResourceDetailDTO.setMemo1(goods.getMemo1());
        emallResourceDetailDTO.setMemo2(goods.getMemo2());
        emallResourceDetailDTO.setMemo3(goods.getMemo3());
        emallResourceDetailDTO.setMemo4(goods.getMemo4());
        emallResourceDetailDTO.setMemo5(goods.getMemo5());
        emallResourceDetailDTOSetParmas2(goods, emallResourceDetailDTO);
        emallResourceDetailDTO.setSearchAttributes(this.getGoodsResourceAttributes(
                emallResourceDetailDTO.getGoodsResourceId(), emallResourceDetailDTO.getGoodsId()));

        emallResourceDetailDTO.setPayWay(Arrays.asList(newResource.getPayWay().split(",")));
        emallResourceDetailDTO.setUnit(req.getUnit());
        emallResourceDetailDTO.setPack(goods.getPack());
        emallResourceDetailDTO.setBrand(newResource.getBrand());
        emallResourceDetailDTO.setSpecs(goods.getSpecs());
        emallResourceDetailDTO.setMark(goods.getMark());
        emallResourceDetailDTO.setLogisticsId(goods.getLogistics());
        //动态属性
        DynamicAttributeDTO dynamicAttributeDTO = goodsService.findDynamicAttribute(goods.getGoodsId());
        log.info("===5===dynamicAttributeDTO===->{}", JSON.toJSONString(dynamicAttributeDTO));
        emallResourceDetailDTO.setManufacturer(dynamicAttributeDTO.getValue(DynamicAttributeEnum.MANUFACTURER.getCode()));
        emallResourceDetailDTO.setIsSteelBar(dynamicAttributeDTO.check(DynamicAttributeEnum.STEEL_BAR.getCode()));
        emallResourceDetailDTO.setIsSupportAddItem(goodsService.ifSupportAdditem(goods.getGoodsId()));
        emallResourceDetailDTO.setIsHidePrice(dynamicAttributeDTO.check(DynamicAttributeEnum.HIDE_PRICE.getCode()));
        emallResourceDetailDTO.setIsNeedSalesman(dynamicAttributeDTO.check(DynamicAttributeEnum.SALESMAN.getCode()));
        emallResourceDetailDTO.setIsCarry(goodsService.ifComputePorterage(goods.getGoodsId()));
        emallResourceDetailDTO.setIsTransactionTerms(dynamicAttributeDTO.check(DynamicAttributeEnum.TRANSACTION_TERMS.getCode()));
        emallResourceDetailDTO.setIsStoreDiscount(dynamicAttributeDTO.check(DynamicAttributeEnum.STORE_DISCOUNT.getCode()));
        emallResourceDetailDTO.setDeliveryTimeType(dynamicAttributeDTO.getValue(DynamicAttributeEnum.DELIVERY_TIME_TYPE.getCode()));
        log.info("===6===emallResourceDetailDTO===->{}", JSON.toJSONString(emallResourceDetailDTO));
        return emallResourceDetailDTO;
    }

    @NotNull
    private Resource getNewResource(EmallResourceDetailReqDTO req, Resource newResource, List<Resource> cansaleResources, BigDecimal minArrivePrice, BigDecimal maxArrivePrice, Resource oldResource, EmallResourceDetailDTO emallResourceDetailDTO, Map<String, StoreDTO> storeMap) {
        BigDecimal maxPrice;
        BigDecimal minPrice;
        BigDecimal minFactoryPrice;
        List<String> units;
        BigDecimal maxFactoryPrice;
        if (newResource != null) {
            minPrice = newResource.getPrice();
            maxPrice = cansaleResources.get(cansaleResources.size() - 1).getPrice();
            minFactoryPrice = newResource.getFactoryPrice();
            maxFactoryPrice = cansaleResources.get(cansaleResources.size() - 1).getFactoryPrice();
            for (Resource cansaleResource : cansaleResources) {
                if (cansaleResource.getArrivePrice() != null) {
                    if (minArrivePrice.compareTo(BigDecimal.ZERO) == 0) {
                        minArrivePrice = cansaleResource.getArrivePrice();
                    } else if (minArrivePrice.compareTo(cansaleResource.getArrivePrice()) > 0) {
                        minArrivePrice = cansaleResource.getArrivePrice();
                    }
                    if (maxArrivePrice.compareTo(BigDecimal.ZERO) == 0) {
                        maxArrivePrice = cansaleResource.getArrivePrice();
                    } else if (maxArrivePrice.compareTo(cansaleResource.getArrivePrice()) < 0) {
                        maxArrivePrice = cansaleResource.getArrivePrice();
                    }
                }
            }
            if (CsStringUtils.isNotEmpty(req.getResourceId())) {
                minPrice = newResource.getPrice();
                maxPrice = newResource.getPrice();
                minFactoryPrice = newResource.getFactoryPrice();
                maxFactoryPrice = newResource.getFactoryPrice();
                if (newResource.getArrivePrice() != null) {
                    minArrivePrice = newResource.getArrivePrice();
                    maxArrivePrice = newResource.getArrivePrice();
                } else {
                    minArrivePrice = BigDecimal.ZERO;
                    maxArrivePrice = BigDecimal.ZERO;
                }
            }
        } else {
            minPrice = oldResource.getPrice();
            maxPrice = oldResource.getPrice();
            minFactoryPrice = oldResource.getFactoryPrice();
            maxFactoryPrice = oldResource.getFactoryPrice();
            if (oldResource.getArrivePrice() != null) {
                minArrivePrice = oldResource.getArrivePrice();
                maxArrivePrice = oldResource.getArrivePrice();
            }
        }

        //设置价格
        newResource = getNewResource(newResource, emallResourceDetailDTO, storeMap, oldResource);
        // 设置销售区域
        emallResourceDetailDTO.setSaleAreaCode(newResource.getSaleAreaRealCode());
        //单位转换
        List<UnitDTO> unitDTOs = Lists.newArrayList();
        units = Arrays.asList(newResource.getSaleUnit().split(","));
        if (CsStringUtils.isNullOrBlank(req.getUnit())) {
            req.setUnit(units.get(0));
        }
        unitDTOsAdd(req, units, unitDTOs);
        emallResourceDetailDTO.setUnits(unitDTOs);


        BigDecimal convertRate = BigDecimal.ONE;
        if (newResource.getConvertRate() != null && !newResource.getPriceUnit().equals(req.getUnit())) {
            convertRate = newResource.getConvertRate();
        }
        if (!newResource.getPriceUnit().equals(req.getUnit())) {
            minPrice = minPrice.multiply(convertRate).setScale(2, BigDecimal.ROUND_HALF_UP);
            maxPrice = maxPrice.multiply(convertRate).setScale(2, BigDecimal.ROUND_HALF_UP);
            minFactoryPrice = minFactoryPrice.multiply(convertRate).setScale(2, BigDecimal.ROUND_HALF_UP);
            maxFactoryPrice = maxFactoryPrice.multiply(convertRate).setScale(2, BigDecimal.ROUND_HALF_UP);
            minArrivePrice = minArrivePrice.multiply(convertRate).setScale(2, BigDecimal.ROUND_HALF_UP);
            maxArrivePrice = maxArrivePrice.multiply(convertRate).setScale(2, BigDecimal.ROUND_HALF_UP);
        }
        emallResourceDetailDTO.setMinPrice(minPrice);
        emallResourceDetailDTO.setMaxPrice(maxPrice);
        emallResourceDetailDTO.setMinFactoryPrice(minFactoryPrice);
        emallResourceDetailDTO.setMaxFactoryPrice(maxFactoryPrice);

        emallResourceDetailDTOSetItems(minArrivePrice, emallResourceDetailDTO, maxArrivePrice, newResource, convertRate);
        return newResource;
    }

    private void emallResourceDetailDTOSetParmas2(Goods goods, EmallResourceDetailDTO emallResourceDetailDTO) {
        if (CsStringUtils.isNotBlank(goods.getImgs())) {
            emallResourceDetailDTO.setImgs(String.join(",", goods.getImgs()));
        }
        if (CsStringUtils.isNotBlank(goods.getCartageRule())) {
            emallResourceDetailDTO.setIsHaveCartageRule(true);
        } else {
            emallResourceDetailDTO.setIsHaveCartageRule(false);
        }
    }

    private void emallResourceDetailDTOSetItems(BigDecimal minArrivePrice, EmallResourceDetailDTO emallResourceDetailDTO, BigDecimal maxArrivePrice, Resource newResource, BigDecimal convertRate) {
        if (minArrivePrice.compareTo(BigDecimal.ZERO) > 0) {
            emallResourceDetailDTO.setMinArrivePrice(minArrivePrice);
        }
        if (maxArrivePrice.compareTo(BigDecimal.ZERO) > 0) {
            emallResourceDetailDTO.setMaxArrivePrice(maxArrivePrice);
        }

        if (newResource.getCansaleNum() != null) {
            emallResourceDetailDTO.setCansaleNum(newResource.getCansaleNum().divide(convertRate, 0, BigDecimal.ROUND_HALF_UP));
        }
        if (newResource.getSaleNum() != null) {
            emallResourceDetailDTO.setSaleNum(newResource.getSaleNum().divide(convertRate, 0, BigDecimal.ROUND_HALF_UP));
        }
        if (newResource.getOrderminNum() != null) {
            emallResourceDetailDTO.setOrderminNum(newResource.getOrderminNum().divide(convertRate, 0, BigDecimal.ROUND_HALF_UP));
        }
        if (newResource.getOrdermaxNum() != null) {
            emallResourceDetailDTO.setOrdermaxNum(newResource.getOrdermaxNum().divide(convertRate, 0, BigDecimal.ROUND_HALF_UP));
        }
        if (newResource.getOrderminchangeNum() != null) {
            emallResourceDetailDTO.setOrderminchangeNum(newResource.getOrderminchangeNum().divide(convertRate, 0, BigDecimal.ROUND_HALF_UP));
        }
        if (newResource.getDaymaxNum() != null) {
            emallResourceDetailDTO.setDaymaxNum(newResource.getDaymaxNum().divide(convertRate, 0, BigDecimal.ROUND_HALF_UP));
        }
        if (newResource.getOrdermaxNum() != null) {
            emallResourceDetailDTO.setCansaleNum(newResource.getOrdermaxNum().divide(convertRate, 0, BigDecimal.ROUND_HALF_UP));
        }
        if (newResource.getOrderminNum() != null) {
            emallResourceDetailDTO.setSalesVolume(newResource.getOrderminNum().divide(convertRate, 0, BigDecimal.ROUND_HALF_UP));
        }
    }

    private void unitDTOsAdd(EmallResourceDetailReqDTO req, List<String> units, List<UnitDTO> unitDTOs) {
        units.stream().forEach(unit -> {
            UnitDTO unitDTO = new UnitDTO();
            unitDTO.setUnit(unit);
            if (unit.equals(req.getUnit())) {
                unitDTO.setIsSelected(true);
            } else {
                unitDTO.setIsSelected(false);
            }
            unitDTOs.add(unitDTO);
        });
    }

    @NotNull
    private Resource getNewResource(Resource newResource, EmallResourceDetailDTO emallResourceDetailDTO, Map<String, StoreDTO> storeMap, Resource oldResource) {
        if (newResource != null) {
            BeanUtils.copyProperties(newResource, emallResourceDetailDTO);
            emallResourceDetailDTO.setIsHaveGoods(1);
            emallResourceDetailDTO.setStoreDTOS(Lists.newArrayList(storeMap.values()));
        } else {
            newResource = oldResource;
            BeanUtils.copyProperties(oldResource, emallResourceDetailDTO);
            emallResourceDetailDTO.setIsHaveGoods(0);
        }
        return newResource;
    }

    private Resource getResource(EmallResourceDetailReqDTO req, List<Resource> cansaleResources, Resource newResource, Map<String, StoreDTO> storeMap) {
        if (!CollectionUtils.isEmpty(cansaleResources)) {
            newResource = getNewResource(req, cansaleResources, newResource);
            //获取当前销售区域的所有仓库列表
            Map<String, Integer> defaultStroreMap = new HashMap<>();
            defaultStroreMapPutValues(cansaleResources, defaultStroreMap);
            //可以选择的仓库
            for (Resource cansaleResource : cansaleResources) {
                StoreDTO storeDTO = new StoreDTO();
                BeanUtils.copyProperties(cansaleResource, storeDTO);
                if (StoreTypeEnum.STORE_TYPE200.code().equals(cansaleResource.getStoreType())) {
                    storeDTO.setStoreName(cansaleResource.getSaleArea() + cansaleResource.getStoreName());
                }
                //当前销售区域多仓库可选时设置默认仓库属性
                storeMapPutValues(storeMap, defaultStroreMap, storeDTO);
            }
        }
        return newResource;
    }

    private Resource getNewResource(EmallResourceDetailReqDTO req, List<Resource> cansaleResources, Resource newResource) {
        if (CsStringUtils.isNotEmpty(req.getResourceId())) {
            for (Resource cansaleResource : cansaleResources) {
                if (req.getResourceId().equals(cansaleResource.getResourceId())) {
                    newResource = cansaleResource;
                    break;
                }
            }
        } else {
            newResource = cansaleResources.get(0);
        }
        return newResource;
    }

    private void defaultStroreMapPutValues(List<Resource> cansaleResources, Map<String, Integer> defaultStroreMap) {
        if (CsStringUtils.isNotEmpty(cansaleResources.get(0).getSaleAreaRealCode())) {
            SaleRegionDTO saleRegionDTO = iSaleRegionService.findById(cansaleResources.get(0).getSaleAreaRealCode());
            if (saleRegionDTO != null) {
                for (SaleStoreDTO saleStoreDTO : Optional.ofNullable(saleRegionDTO.getSaleStoreDTOS()).orElse(Collections.emptyList())) {
                    defaultStroreMap.put(saleStoreDTO.getStoreId(), saleStoreDTO.getStoreDefault());
                }
            }
        }
    }

    private void storeMapPutValues(Map<String, StoreDTO> storeMap, Map<String, Integer> defaultStroreMap, StoreDTO storeDTO) {
        if (defaultStroreMap.get(storeDTO.getStoreId()) != null) {
            storeDTO.setStoreDefault(defaultStroreMap.get(storeDTO.getStoreId()));
        } else {
            storeDTO.setStoreDefault(0);
        }
        if (storeMap.get(storeDTO.getStoreId()) == null) {
            storeMap.put(storeDTO.getStoreId(), storeDTO);
        }
    }

    private void cansaleResourcesAddItems(EmallResourceDetailReqDTO req, List<Resource> selectResources, BigDecimal quantity, List<Resource> cansaleResources) {
        for (Resource resource : selectResources) {
            BigDecimal convertRate = BigDecimal.ONE;
            if (resource.getConvertRate() != null && !resource.getPriceUnit().equals(req.getUnit())) {
                convertRate = resource.getConvertRate();
            }

            if (quantity.compareTo(BigDecimal.ZERO) == 0) {
                if (resource.getOrderminNum() != null && BigDecimal.ZERO.compareTo(resource.getOrderminNum().divide(convertRate, 0, BigDecimal.ROUND_HALF_UP)) < 0) {
                    quantity = resource.getOrderminNum().divide(convertRate, 0, BigDecimal.ROUND_HALF_UP);
                } else {
                    quantity = BigDecimal.ONE;
                }
            }
            //新的出货逻辑
            log.info("====quantity===->{}", quantity);
            cansaleResourcesAddSouce(quantity, cansaleResources, resource, convertRate);
        }
    }

    private void cansaleResourcesAddSouce(BigDecimal quantity, List<Resource> cansaleResources, Resource resource, BigDecimal convertRate) {
        if (checkResourceTradeStatus(resource)) {
            if (resource.getCansaleNum() != null && resource.getCansaleNum().compareTo(BigDecimal.ZERO) > 0
                    && quantity.compareTo(resource.getCansaleNum().divide(convertRate, 0, BigDecimal.ROUND_HALF_UP)) <= 0) {
                if (resource.getOrderminNum() == null && resource.getOrdermaxNum() == null) {
                    cansaleResources.add(resource);
                } else if (resource.getOrderminNum() == null && resource.getOrdermaxNum() != null
                        && quantity.compareTo(resource.getOrdermaxNum().divide(convertRate, 0, BigDecimal.ROUND_HALF_UP)) <= 0) {
                    cansaleResources.add(resource);
                } else if (resource.getOrderminNum() != null && resource.getOrdermaxNum() == null
                        && quantity.compareTo(resource.getOrderminNum().divide(convertRate, 0, BigDecimal.ROUND_HALF_UP)) >= 0) {
                    cansaleResources.add(resource);
                } else if (resource.getOrderminNum() != null && resource.getOrdermaxNum() != null
                        && quantity.compareTo(resource.getOrderminNum().divide(convertRate, 0, BigDecimal.ROUND_HALF_UP)) >= 0
                        && quantity.compareTo(resource.getOrdermaxNum().divide(convertRate, 0, BigDecimal.ROUND_HALF_UP)) <= 0) {
                    cansaleResources.add(resource);
                }
            }
        }
    }

    private void resourceCriteriaAddandLike(EmallResourceDetailReqDTO req, Example.Criteria resourceCriteria) {
        if (req.getAttributes() != null && !req.getAttributes().isEmpty()) {
            for (AttributeDTO attr : req.getAttributes()) {
                if (attr.getAttributeValueDTOs() != null) {
                    for (AttributeValueDTO attrValue : attr.getAttributeValueDTOs()) {
                        resourceCriteria.andLike(RESOURCE_ATTRIBUTES, "%" + attr.getAttributeNo() + ":" + attrValue.getAttributeValueNo() + "%");
                    }
                }
            }
        }
    }

    private Resource getOldResource(EmallResourceDetailReqDTO req, Resource oldResource) {
        if (CsStringUtils.isNotEmpty(req.getResourceId())) {
            oldResource = resourceMapper.selectByPrimaryKey(req.getResourceId());
        } else {
            Example resourceQuery = new Example(Resource.class);
            Example.Criteria queryCriteria = resourceQuery.createCriteria();
            queryCriteria.andEqualTo(GOODS_RESOURCE_ID, req.getGoodsResourceId());
            List<Resource> resources = resourceMapper.selectByExample(resourceQuery);
            if (!CollectionUtils.isEmpty(resources)) {
                oldResource = resources.get(0);
            }
        }
        return oldResource;
    }

    @Nullable
    private static EmallResourceDetailDTO getEmallResourceDetailDTO(Resource oldResource, EmallResourceDetailDTO emallResourceDetailDTO) {
        if (oldResource == null || Boolean.TRUE.equals(oldResource.getDelFlg())) {
            log.info(" === 资源不存在");
            emallResourceDetailDTO.setStatus(String.valueOf(HttpStatus.NOT_FOUND.value()));
            return emallResourceDetailDTO;
        }
        if (!ResourceStatusEnum.RES_STATUS100.code().equals(oldResource.getStatus())) {
            log.info(" === 资源状态异常");
            emallResourceDetailDTO.setStatus(oldResource.getStatus());
            return emallResourceDetailDTO;
        }
        return null;
    }

    @Override
    public ResourceOrderPrepareDTO checkResourceOrderPrepare(ResourceOrderPrepareReqDTO resourceOrderPrepareReqDTO) {
        return null;
    }

    @Override
    public ResourceOrderPrepareDTO resourceOrderPrepare(ResourceOrderPrepareReqDTO req) {
        log.info("===resourceOrderPrepare===->{}", JSON.toJSONString(req));
        //参数检查
        List<SelectResourceDTO> selectResourceDTOS = req.getSelectResourceDTOS();
        if (CollectionUtils.isEmpty(selectResourceDTOS)) {
            throw new BizException(BasicCode.PARAM_NULL, "选择的商品");
        }
        String deliveryWay = req.getDeliveryWay();
        if (CsStringUtils.isNullOrBlank(req.getAddressId())) {
            throw new BizException(BasicCode.PARAM_NULL, "地址ID");
        }
        //初始化结果
        ResourceOrderPrepareDTO resourceOrderPrepareDTO = new ResourceOrderPrepareDTO();
        List<StoreDTO> storeDTOS = Lists.newArrayList();
        List<ResourceGoodsDTO> resourceGoodsDTOS = Lists.newArrayList();

        //逻辑处理一：每一个购买的商品可以出货的仓库
        Map<String, List<Resource>> firstMap = Maps.newHashMap(); //每种商品可以出售的资源对应到具体的仓库
        for (SelectResourceDTO selectResourceDTO : selectResourceDTOS) { //选择的商品
            List<Resource> canResources = Lists.newArrayList();
            //设置购买数量
            BigDecimal quantity = getQuantity(selectResourceDTO);
            //老资源
            Resource oldResource = resourceMapper.selectByPrimaryKey(selectResourceDTO.getResourceId());
            log.info("===1===oldResource===->{}", JSON.toJSONString(oldResource));
            Resource newResource = null;
            //查询可以出货的资源
            Example resourceExample = new Example(Resource.class);
            Example.Criteria resourceCriteria = resourceExample.createCriteria();
            resourceCriteria.andEqualTo(DEL_FLG, 0);
            resourceCriteria.andEqualTo("goodsResourceId", oldResource.getGoodsResourceId());
            resourceCriteria.andEqualTo(STATUS, ResourceStatusEnum.RES_STATUS100.getCode());

            resourceCriteriaAndresourceAttributes(selectResourceDTO, resourceCriteria);

            this.processResourceRegionsExample(RESOURCE_REGIONS, resourceExample, req.getCountryName(), req.getProvinceName()
                    , req.getCityName(), req.getAreaName(), req.getStreetName());

            resourceCriteriaAddDeliveryWay(deliveryWay, resourceCriteria);

            List<Resource> selectResources = resourceMapper.selectByExample(resourceExample);
            //判断是否有货
            if (!CollectionUtils.isEmpty(selectResources)) {
                //查询可以选择购买的资源
                List<Resource> cansaleResources = Lists.newArrayList();
                cansaleResourcesAddValues(selectResources, quantity, cansaleResources);

                if (!CollectionUtils.isEmpty(cansaleResources)) {
                    for (Resource resource : cansaleResources) {
                        // 暂时不做控制 制酒添加
                        canResources.add(resource);
                        //仓库控制
                        /*if(StoreTypeEnum.STORE_TYPE200.code().equals(resource.getStoreType())){ //中心仓
                            if(CsStringUtils.isNotBlank(req.getShopCode())){ //这里的shopCode是推荐人表主键，如果有，查询推荐人表，获取关联门店仓库ID
                                log.info("iBuyerAndReferrerService->findById->req:"+req.getShopCode());
                                ReferrerInfoDTO referrerInfoDTO = iBuyerAndReferrerService.findById(req.getShopCode());
                                if(referrerInfoDTO != null){
                                    log.info("iBuyerAndReferrerService->findById->response:"+referrerInfoDTO.toString());
                                    String referrerStoreId = referrerInfoDTO.getReferrerStoreId();
                                    if(CsStringUtils.isNotBlank(referrerStoreId)){
                                        log.info("warehouseService->selectWarehouseBaseData->req:"+referrerStoreId);
                                        WarehouseBaseDataDTO warehouseBaseDataDTO = warehouseService.selectWarehouseBaseData(referrerStoreId);
                                        if(warehouseBaseDataDTO != null){
                                            log.info("warehouseService->selectWarehouseBaseData->response:"+warehouseBaseDataDTO.toString());
                                            if(!DeliveryWayEnum.BUYER_TAKE.code().equals(deliveryWay)){
                                                List<CategoryQuantityDTO> quantityList = Lists.newArrayList();//物流信息
                                                if (PriceWayEnum.PRICE_TYPE2.code().equals(resource.getPriceWay())) {
                                                    CategoryQuantityDTO lquantity = new CategoryQuantityDTO();
                                                    GoodsDTO goodsDTO = goodsService.findGoodsById(resource.getGoodsId());
                                                    lquantity.setTransportCategoryId(goodsDTO.getLogistics());//物流类型
                                                    lquantity.setProductQuantity(quantity);//计量单位数量
                                                    quantityList.add(lquantity);
                                                }
                                                //物流信息
                                                Boolean isHaveRuleFlag = true;
                                                if (CollectionKit.isNotEmpty(quantityList)) {
                                                    CarriageComputeDTO carriageDTO = new CarriageComputeDTO();
                                                    carriageDTO.setPickingBillType(deliveryWay);
                                                    carriageDTO.setUserId(resource.getSellerId());
                                                    carriageDTO.setReceiveAddressId(req.getAddressId());
                                                    ContractAddressDTO addressDTO = receivingAddressService.findById(req.getAddressId());
                                                    if(addressDTO != null){
                                                        carriageDTO.setReceiveAddressLocation(addressDTO.getCoordinate());
                                                    }
                                                    carriageDTO.setWarehouseId(warehouseBaseDataDTO.getWarehouseId());
                                                    carriageDTO.setCategoryQuantityList(quantityList);
                                                    carriageDTO.setProvinceCode(req.getProvinceCode());
                                                    carriageDTO.setCityCode(req.getCityCode());
                                                    carriageDTO.setDistrictCode(req.getAreaCode());
                                                    carriageDTO.setStreetCode(req.getStreetCode());
                                                    ItemResult<List<CarriageRuleResultDTO>> computeResult;
                                                    try {
                                                        log.info("queryCarriageRule_start:" + JSON.toJSONString(carriageDTO));
                                                        computeResult = ruleComputeService.queryCarriageRule(carriageDTO);//运费
                                                        log.info("queryCarriageRule_end:" + JSON.toJSONString(computeResult));
                                                    } catch (Exception e) {
                                                        log.error("queryCarriageRule_error", e);
                                                        throw new BizException(BasicCode.UNDEFINED_ERROR, "查询运费规则出错");
                                                    }

                                                    for (CarriageRuleResultDTO compute : Optional.ofNullable(computeResult.getData()).orElse(Collections.emptyList())) {
                                                        Boolean ruleFlag = compute.getRuleFlag();
                                                        if (!ruleFlag) {
                                                            isHaveRuleFlag = false;
                                                        }
                                                    }
                                                }
                                                if (isHaveRuleFlag) {
                                                    resource.setStoreId(warehouseBaseDataDTO.getWarehouseId());
                                                    canResources.add(resource);
                                                }
                                            }else {
                                                canResources.add(resource);
                                            }
                                        }else {
                                            List<WarehouseDetailsDTO> warehouseDetailsDTOs = queryStoreListByCode(null,
                                                    req.getProvinceCode(), req.getCityCode(), req.getAreaCode(),resource.getSellerId());
                                            if(!CollectionUtils.isEmpty(warehouseDetailsDTOs)){
                                                for (WarehouseDetailsDTO warehouseDetailsDTO : warehouseDetailsDTOs) {
                                                    if(!DeliveryWayEnum.BUYER_TAKE.code().equals(deliveryWay)){
                                                        List<CategoryQuantityDTO> quantityList = Lists.newArrayList();//物流信息
                                                        if (PriceWayEnum.PRICE_TYPE2.code().equals(resource.getPriceWay())) {
                                                            CategoryQuantityDTO lquantity = new CategoryQuantityDTO();
                                                            GoodsDTO goodsDTO = goodsService.findGoodsById(resource.getGoodsId());
                                                            lquantity.setTransportCategoryId(goodsDTO.getLogistics());//物流类型
                                                            lquantity.setProductQuantity(quantity);//计量单位数量
                                                            quantityList.add(lquantity);
                                                        }
                                                        //物流信息
                                                        Boolean isHaveRuleFlag = true;
                                                        if (CollectionKit.isNotEmpty(quantityList)) {
                                                            CarriageComputeDTO carriageDTO = new CarriageComputeDTO();
                                                            carriageDTO.setPickingBillType(deliveryWay);
                                                            carriageDTO.setUserId(resource.getSellerId());
                                                            carriageDTO.setReceiveAddressId(req.getAddressId());
                                                            ContractAddressDTO addressDTO = receivingAddressService.findById(req.getAddressId());
                                                            if(addressDTO != null){
                                                                carriageDTO.setReceiveAddressLocation(addressDTO.getCoordinate());
                                                            }
                                                            carriageDTO.setWarehouseId(warehouseDetailsDTO.getWarehouseId());
                                                            carriageDTO.setCategoryQuantityList(quantityList);
                                                            carriageDTO.setProvinceCode(req.getProvinceCode());
                                                            carriageDTO.setCityCode(req.getCityCode());
                                                            carriageDTO.setDistrictCode(req.getAreaCode());
                                                            carriageDTO.setStreetCode(req.getStreetCode());
                                                            ItemResult<List<CarriageRuleResultDTO>> computeResult;
                                                            try {
                                                                log.info("queryCarriageRule_start:" + JSON.toJSONString(carriageDTO));
                                                                computeResult = ruleComputeService.queryCarriageRule(carriageDTO);//运费
                                                                log.info("queryCarriageRule_end:" + JSON.toJSONString(computeResult));
                                                            } catch (Exception e) {
                                                                log.error("queryCarriageRule_error", e);
                                                                throw new BizException(BasicCode.UNDEFINED_ERROR, "查询运费规则出错");
                                                            }

                                                            for (CarriageRuleResultDTO compute : Optional.ofNullable(computeResult.getData()).orElse(Collections.emptyList())) {
                                                                Boolean ruleFlag = compute.getRuleFlag();
                                                                if (!ruleFlag) {
                                                                    isHaveRuleFlag = false;
                                                                }
                                                            }
                                                        }
                                                        if (isHaveRuleFlag) {
                                                            resource.setStoreId(warehouseDetailsDTO.getWarehouseId());
                                                            canResources.add(resource);
                                                        }
                                                    }else {
                                                        canResources.add(resource);
                                                    }
                                                }
                                            }
                                        }
                                    }else {
                                        List<WarehouseDetailsDTO> warehouseDetailsDTOs = queryStoreListByCode(null,
                                                req.getProvinceCode(), req.getCityCode(), req.getAreaCode(),resource.getSellerId());
                                        if(!CollectionUtils.isEmpty(warehouseDetailsDTOs)){
                                            for (WarehouseDetailsDTO warehouseDetailsDTO : warehouseDetailsDTOs) {
                                                if(!DeliveryWayEnum.BUYER_TAKE.code().equals(deliveryWay)){
                                                    List<CategoryQuantityDTO> quantityList = Lists.newArrayList();//物流信息
                                                    if (PriceWayEnum.PRICE_TYPE2.code().equals(resource.getPriceWay())) {
                                                        CategoryQuantityDTO lquantity = new CategoryQuantityDTO();
                                                        GoodsDTO goodsDTO = goodsService.findGoodsById(resource.getGoodsId());
                                                        lquantity.setTransportCategoryId(goodsDTO.getLogistics());//物流类型
                                                        lquantity.setProductQuantity(quantity);//计量单位数量
                                                        quantityList.add(lquantity);
                                                    }
                                                    //物流信息
                                                    Boolean isHaveRuleFlag = true;
                                                    if (CollectionKit.isNotEmpty(quantityList)) {
                                                        CarriageComputeDTO carriageDTO = new CarriageComputeDTO();
                                                        carriageDTO.setPickingBillType(deliveryWay);
                                                        carriageDTO.setUserId(resource.getSellerId());
                                                        carriageDTO.setReceiveAddressId(req.getAddressId());
                                                        ContractAddressDTO addressDTO = receivingAddressService.findById(req.getAddressId());
                                                        if(addressDTO != null){
                                                            carriageDTO.setReceiveAddressLocation(addressDTO.getCoordinate());
                                                        }
                                                        carriageDTO.setWarehouseId(warehouseDetailsDTO.getWarehouseId());
                                                        carriageDTO.setCategoryQuantityList(quantityList);
                                                        carriageDTO.setProvinceCode(req.getProvinceCode());
                                                        carriageDTO.setCityCode(req.getCityCode());
                                                        carriageDTO.setDistrictCode(req.getAreaCode());
                                                        carriageDTO.setStreetCode(req.getStreetCode());
                                                        ItemResult<List<CarriageRuleResultDTO>> computeResult;
                                                        try {
                                                            log.info("queryCarriageRule_start:" + JSON.toJSONString(carriageDTO));
                                                            computeResult = ruleComputeService.queryCarriageRule(carriageDTO);//运费
                                                            log.info("queryCarriageRule_end:" + JSON.toJSONString(computeResult));
                                                        } catch (Exception e) {
                                                            log.error("queryCarriageRule_error", e);
                                                            throw new BizException(BasicCode.UNDEFINED_ERROR, "查询运费规则出错");
                                                        }

                                                        for (CarriageRuleResultDTO compute : Optional.ofNullable(computeResult.getData()).orElse(Collections.emptyList())) {
                                                            Boolean ruleFlag = compute.getRuleFlag();
                                                            if (!ruleFlag) {
                                                                isHaveRuleFlag = false;
                                                            }
                                                        }
                                                    }
                                                    if (isHaveRuleFlag) {
                                                        resource.setStoreId(warehouseDetailsDTO.getWarehouseId());
                                                        canResources.add(resource);
                                                    }
                                                }else {
                                                    canResources.add(resource);
                                                }
                                            }
                                        }
                                    }
                                }else {
                                    List<WarehouseDetailsDTO> warehouseDetailsDTOs = queryStoreListByCode(null,
                                            req.getProvinceCode(), req.getCityCode(), req.getAreaCode(),resource.getSellerId());
                                    if(!CollectionUtils.isEmpty(warehouseDetailsDTOs)){
                                        for (WarehouseDetailsDTO warehouseDetailsDTO : warehouseDetailsDTOs) {
                                            if(!DeliveryWayEnum.BUYER_TAKE.code().equals(deliveryWay)){
                                                List<CategoryQuantityDTO> quantityList = Lists.newArrayList();//物流信息
                                                if (PriceWayEnum.PRICE_TYPE2.code().equals(resource.getPriceWay())) {
                                                    CategoryQuantityDTO lquantity = new CategoryQuantityDTO();
                                                    GoodsDTO goodsDTO = goodsService.findGoodsById(resource.getGoodsId());
                                                    lquantity.setTransportCategoryId(goodsDTO.getLogistics());//物流类型
                                                    lquantity.setProductQuantity(quantity);//计量单位数量
                                                    quantityList.add(lquantity);
                                                }
                                                //物流信息
                                                Boolean isHaveRuleFlag = true;
                                                if (CollectionKit.isNotEmpty(quantityList)) {
                                                    CarriageComputeDTO carriageDTO = new CarriageComputeDTO();
                                                    carriageDTO.setPickingBillType(deliveryWay);
                                                    carriageDTO.setUserId(resource.getSellerId());
                                                    carriageDTO.setReceiveAddressId(req.getAddressId());
                                                    ContractAddressDTO addressDTO = receivingAddressService.findById(req.getAddressId());
                                                    if(addressDTO != null){
                                                        carriageDTO.setReceiveAddressLocation(addressDTO.getCoordinate());
                                                    }
                                                    carriageDTO.setWarehouseId(warehouseDetailsDTO.getWarehouseId());
                                                    carriageDTO.setCategoryQuantityList(quantityList);
                                                    carriageDTO.setProvinceCode(req.getProvinceCode());
                                                    carriageDTO.setCityCode(req.getCityCode());
                                                    carriageDTO.setDistrictCode(req.getAreaCode());
                                                    carriageDTO.setStreetCode(req.getStreetCode());
                                                    ItemResult<List<CarriageRuleResultDTO>> computeResult;
                                                    try {
                                                        log.info("queryCarriageRule_start:" + JSON.toJSONString(carriageDTO));
                                                        computeResult = ruleComputeService.queryCarriageRule(carriageDTO);//运费
                                                        log.info("queryCarriageRule_end:" + JSON.toJSONString(computeResult));
                                                    } catch (Exception e) {
                                                        log.error("queryCarriageRule_error", e);
                                                        throw new BizException(BasicCode.UNDEFINED_ERROR, "查询运费规则出错");
                                                    }

                                                    for (CarriageRuleResultDTO compute : Optional.ofNullable(computeResult.getData()).orElse(Collections.emptyList())) {
                                                        Boolean ruleFlag = compute.getRuleFlag();
                                                        if (!ruleFlag) {
                                                            isHaveRuleFlag = false;
                                                        }
                                                    }
                                                }
                                                if (isHaveRuleFlag) {
                                                    resource.setStoreId(warehouseDetailsDTO.getWarehouseId());
                                                    canResources.add(resource);
                                                }
                                            }else {
                                                canResources.add(resource);
                                            }
                                        }
                                    }
                                }
                            }else {
                                List<WarehouseDetailsDTO> warehouseDetailsDTOs = queryStoreListByCode(null,
                                        req.getProvinceCode(), req.getCityCode(), req.getAreaCode(),resource.getSellerId());
                                if(!CollectionUtils.isEmpty(warehouseDetailsDTOs)){
                                    for (WarehouseDetailsDTO warehouseDetailsDTO : warehouseDetailsDTOs) {
                                        if(!DeliveryWayEnum.BUYER_TAKE.code().equals(deliveryWay)){
                                            List<CategoryQuantityDTO> quantityList = Lists.newArrayList();//物流信息
                                            if (PriceWayEnum.PRICE_TYPE2.code().equals(resource.getPriceWay())) {
                                                CategoryQuantityDTO lquantity = new CategoryQuantityDTO();
                                                GoodsDTO goodsDTO = goodsService.findGoodsById(resource.getGoodsId());
                                                lquantity.setTransportCategoryId(goodsDTO.getLogistics());//物流类型
                                                lquantity.setProductQuantity(quantity);//计量单位数量
                                                quantityList.add(lquantity);
                                            }
                                            //物流信息
                                            Boolean isHaveRuleFlag = true;
                                            if (CollectionKit.isNotEmpty(quantityList)) {
                                                CarriageComputeDTO carriageDTO = new CarriageComputeDTO();
                                                carriageDTO.setPickingBillType(deliveryWay);
                                                carriageDTO.setUserId(resource.getSellerId());
                                                carriageDTO.setReceiveAddressId(req.getAddressId());
                                                ContractAddressDTO addressDTO = receivingAddressService.findById(req.getAddressId());
                                                if(addressDTO != null){
                                                    carriageDTO.setReceiveAddressLocation(addressDTO.getCoordinate());
                                                }
                                                carriageDTO.setWarehouseId(warehouseDetailsDTO.getWarehouseId());
                                                carriageDTO.setCategoryQuantityList(quantityList);
                                                carriageDTO.setProvinceCode(req.getProvinceCode());
                                                carriageDTO.setCityCode(req.getCityCode());
                                                carriageDTO.setDistrictCode(req.getAreaCode());
                                                carriageDTO.setStreetCode(req.getStreetCode());
                                                ItemResult<List<CarriageRuleResultDTO>> computeResult;
                                                try {
                                                    log.info("queryCarriageRule_start:" + JSON.toJSONString(carriageDTO));
                                                    computeResult = ruleComputeService.queryCarriageRule(carriageDTO);//运费
                                                    log.info("queryCarriageRule_end:" + JSON.toJSONString(computeResult));
                                                } catch (Exception e) {
                                                    log.error("queryCarriageRule_error", e);
                                                    throw new BizException(BasicCode.UNDEFINED_ERROR, "查询运费规则出错");
                                                }

                                                for (CarriageRuleResultDTO compute : Optional.ofNullable(computeResult.getData()).orElse(Collections.emptyList())) {
                                                    Boolean ruleFlag = compute.getRuleFlag();
                                                    if (!ruleFlag) {
                                                        isHaveRuleFlag = false;
                                                    }
                                                }
                                            }
                                            if (isHaveRuleFlag) {
                                                resource.setStoreId(warehouseDetailsDTO.getWarehouseId());
                                                canResources.add(resource);
                                            }
                                        }else {
                                            canResources.add(resource);
                                        }
                                    }
                                }
                            }
                        }else {
                            canResources.add(resource);
                        }*/
                    }
                }
            }
            firstMap.put(selectResourceDTO.getResourceId(), canResources);
        }
        log.info("===2===firstMap===->{}", JSON.toJSONString(firstMap));

        //逻辑处理二：每一个购买的商品可以出货的仓库去重
        Map<String, List<String>> storeMap = Maps.newHashMap();

        storeMapPutValues(firstMap, storeMap);

        log.info("===3===storeMap===->{}", JSON.toJSONString(storeMap));

        //逻辑处理三：每一个可以出货仓库的计量
        Map<String, Integer> storeCountMap = Maps.newHashMap();
        storeCountMapPutValues(storeMap, storeCountMap);
        log.info("===4===storeCountMap===->{}", JSON.toJSONString(storeCountMap));

        //逻辑处理四：可以一起出货的仓库
        List<String> storeIds = getStoreIds(storeCountMap, storeMap);
        log.info("===5===storeIds===->{}", JSON.toJSONString(storeIds));

        //自提仓库
        storeDTOSAddValues(storeIds, storeDTOS);

        //出货资源
        resourceGoodsDTOSAddValues(req, firstMap, storeIds, resourceGoodsDTOS);

        dealResourceGoodsDTOS(resourceGoodsDTOS, selectResourceDTOS, deliveryWay);

        resourceOrderPrepareDTO.setResourceGoodsDTOS(resourceGoodsDTOS);
        resourceOrderPrepareDTO.setStoreDTOS(storeDTOS);
        log.info("===7===resourceOrderPrepareDTO===->{}", JSON.toJSONString(resourceOrderPrepareDTO));
        return resourceOrderPrepareDTO;
    }


    private static BigDecimal getQuantity(SelectResourceDTO selectResourceDTO) {
        BigDecimal quantity = BigDecimal.ZERO;
        if (selectResourceDTO.getQuantity() != null) {
            quantity = selectResourceDTO.getQuantity();
        }
        if (CsStringUtils.isNullOrBlank(selectResourceDTO.getResourceId())) {
            throw new BizException(BasicCode.PARAM_NULL, "资源ID");
        }
        return quantity;
    }

    private void resourceGoodsDTOSAddValues(ResourceOrderPrepareReqDTO req, Map<String, List<Resource>> firstMap, List<String> storeIds, List<ResourceGoodsDTO> resourceGoodsDTOS) {
        for (Map.Entry<String, List<Resource>> entry : firstMap.entrySet()) {
            String key = entry.getKey();
            List<Resource> resources = entry.getValue();
            ResourceGoodsDTO resourceGoodsDTO = new ResourceGoodsDTO();
            Resource oldResource = resourceMapper.selectByPrimaryKey(key);
            Resource newResource = oldResource;
            if (CollectionKit.isNotEmpty(resources)) {
                resourceGoodsDTOAddItems(req, firstMap, storeIds, key, newResource, resourceGoodsDTO);
            } else { // 无货
                BeanUtils.copyProperties(newResource, resourceGoodsDTO);
                resourceGoodsDTO.setOldResourceId(key);
                resourceGoodsDTO.setIsHaveGoods(false);
            }
            //wsm后面加的需要验证
            resourceGoodsDTOS.add(resourceGoodsDTO);
        }
        log.info("===6===resourceGoodsDTOS===->{}", JSON.toJSONString(resourceGoodsDTOS));
    }

    private static void resourceGoodsDTOAddItems(ResourceOrderPrepareReqDTO req, Map<String, List<Resource>> firstMap, List<String> storeIds, String key, Resource newResource, ResourceGoodsDTO resourceGoodsDTO) {
        List<String> resourceIds = firstMap.get(key).stream().map(Resource::getResourceId).toList();
        for (Resource resource : firstMap.get(key)) {
            if (resourceIds.contains(key) && storeIds.contains(resource.getStoreId())) {
                if (CsStringUtils.isNotEmpty(req.getStoreId())) {
                    if (req.getStoreId().equals(resource.getStoreId()) && key.equals(resource.getResourceId())) {
                        newResource = resource;
                        BeanUtils.copyProperties(newResource, resourceGoodsDTO);
                        resourceGoodsDTO.setOldResourceId(key);
                        resourceGoodsDTO.setIsHaveGoods(true);
                        break;
                    } else if (req.getStoreId().equals(resource.getStoreId())) {
                        newResource = resource;
                        BeanUtils.copyProperties(newResource, resourceGoodsDTO);
                        resourceGoodsDTO.setOldResourceId(key);
                        resourceGoodsDTO.setIsHaveGoods(true);
                        break;
                    }
                } else {
                    if (key.equals(resource.getResourceId())) {
                        newResource = resource;
                        BeanUtils.copyProperties(newResource, resourceGoodsDTO);
                        resourceGoodsDTO.setOldResourceId(key);
                        resourceGoodsDTO.setIsHaveGoods(true);
                        break;
                    }
                }
            } else if (!resourceIds.contains(key) && storeIds.contains(resource.getStoreId())) {
                if (CsStringUtils.isNotEmpty(req.getStoreId())) {
                    if (req.getStoreId().equals(resource.getStoreId())) {
                        newResource = resource;
                        BeanUtils.copyProperties(newResource, resourceGoodsDTO);
                        resourceGoodsDTO.setOldResourceId(key);
                        resourceGoodsDTO.setIsHaveGoods(true);
                        break;
                    }
                } else {
                    if (storeIds.get(0).equals(resource.getStoreId())) {
                        newResource = resource;
                        BeanUtils.copyProperties(newResource, resourceGoodsDTO);
                        resourceGoodsDTO.setOldResourceId(key);
                        resourceGoodsDTO.setIsHaveGoods(true);
                        break;
                    }
                }
            } else {
                BeanUtils.copyProperties(newResource, resourceGoodsDTO);
                resourceGoodsDTO.setOldResourceId(key);
                resourceGoodsDTO.setIsHaveGoods(false);
            }
        }
    }

    private void dealResourceGoodsDTOS(List<ResourceGoodsDTO> resourceGoodsDTOS, List<SelectResourceDTO> selectResourceDTOS, String deliveryWay) {
        for (ResourceGoodsDTO resourceGoodsDTO : resourceGoodsDTOS) {
            for (SelectResourceDTO selectResourceDTO : selectResourceDTOS) {
                if (selectResourceDTO.getResourceId().equals(resourceGoodsDTO.getOldResourceId())) {
                    resourceGoodsDTO.setQuantity(selectResourceDTO.getQuantity());
                    break;
                }
            }
            GoodsDTO goodsInfo = getGoodsInfo(resourceGoodsDTO.getGoodsId());
            resourceGoodsDTO.setImgs(String.join(",", goodsInfo.getImgs()));
            if (DeliveryWayEnum.BUYER_TAKE.code().equals(deliveryWay)) {
                resourceGoodsDTO.setUnitPrice(resourceGoodsDTO.getFactoryPrice());
            } else {
                if (PriceWayEnum.PRICE_TYPE2.code().equals(resourceGoodsDTO.getPriceWay())) {
                    resourceGoodsDTO.setUnitPrice(resourceGoodsDTO.getFactoryPrice());
                } else {
                    resourceGoodsDTO.setUnitPrice(resourceGoodsDTO.getArrivePrice());
                }
            }
            resourceGoodsDTO.setTotalPrice(resourceGoodsDTO.getUnitPrice().multiply(resourceGoodsDTO.getQuantity()));
            resourceGoodsDTO.setUnit(resourceGoodsDTO.getSaleUnit());
            WarehouseDetailsDTO warehouseDetailsDTO = warehouseService.queryWarehouseDetails(resourceGoodsDTO.getStoreId());
            if (warehouseDetailsDTO != null) {
                resourceGoodsDTO.setStoreName(warehouseDetailsDTO.getName());
                resourceGoodsDTO.setStoreType(warehouseDetailsDTO.getType());
                resourceGoodsDTO.setStoreAddress(warehouseDetailsDTO.getAddress());
            }
        }
    }

    private void storeDTOSAddValues(List<String> storeIds, List<StoreDTO> storeDTOS) {
        if (!CollectionUtils.isEmpty(storeIds)) {
            for (String storeId : storeIds) {
                StoreDTO storeDTO = new StoreDTO();
                WarehouseDetailsDTO warehouseDetailsDTO = warehouseService.queryWarehouseDetails(storeId);
                if (warehouseDetailsDTO != null) {
                    BeanUtils.copyProperties(warehouseDetailsDTO, storeDTO);
                    storeDTO.setStoreId(storeId);
                    storeDTO.setStoreName(warehouseDetailsDTO.getName());
                    storeDTO.setStoreAddress(warehouseDetailsDTO.getAddress());
                    storeDTO.setStoreType(warehouseDetailsDTO.getType());
                    storeDTO.setAdministrator(warehouseDetailsDTO.getAdministrator());
                    storeDTO.setAdministratorPhone(warehouseDetailsDTO.getAdministratorPhone());
                    storeDTOS.add(storeDTO);
                }
            }
        }
    }

    @NotNull
    private static List<String> getStoreIds(Map<String, Integer> storeCountMap, Map<String, List<String>> storeMap) {
        List<String> storeIds = Lists.newArrayList();
        if (!storeCountMap.isEmpty()) {
            for (Map.Entry<String, Integer> entry : storeCountMap.entrySet()) {
                if (storeMap.size() == entry.getValue()) {
                    storeIds.add(entry.getKey());
                }
            }
        }
        return storeIds;
    }

    private static void storeCountMapPutValues(Map<String, List<String>> storeMap, Map<String, Integer> storeCountMap) {
        if (!storeMap.isEmpty()) {
            for (Map.Entry<String, List<String>> entry : storeMap.entrySet()) {
                for (String storeId : entry.getValue()) {
                    if (storeCountMap.containsKey(storeId)) {
                        Integer storeCount = storeCountMap.get(storeId);
                        storeCountMap.remove(storeId);
                        storeCountMap.put(storeId, storeCount + 1);
                    } else {
                        storeCountMap.put(storeId, 1);
                    }
                }
            }
        }
    }

    private static void storeMapPutValues(Map<String, List<Resource>> firstMap, Map<String, List<String>> storeMap) {
        for (Map.Entry<String, List<Resource>> entry : firstMap.entrySet()) {
            List<Resource> resources = entry.getValue();
            List<String> stores = Lists.newArrayList();
            for (Resource resource : Optional.ofNullable(resources).orElse(Collections.emptyList())) {
                if (!stores.contains(resource.getStoreId())) {
                    stores.add(resource.getStoreId());
                }
            }
            if (CollectionKit.isNotEmpty(stores)) {
                storeMap.put(entry.getKey(), stores);
            }
        }
    }

    private static void resourceCriteriaAndresourceAttributes(SelectResourceDTO selectResourceDTO, Example.Criteria resourceCriteria) {
        if (selectResourceDTO.getAttributes() != null && !selectResourceDTO.getAttributes().isEmpty()) {
            for (AttributeDTO attr : selectResourceDTO.getAttributes()) {
                if (attr.getAttributeValueDTOs() != null) {
                    for (AttributeValueDTO attrValue : attr.getAttributeValueDTOs()) {
                        resourceCriteria.andLike("resourceAttributes",
                                "%" + attr.getAttributeNo() + ":" + attrValue.getAttributeValueNo() + "%");
                    }
                }
            }
        }
    }

    private void cansaleResourcesAddValues(List<Resource> selectResources, BigDecimal quantity, List<Resource> cansaleResources) {
        for (Resource resource : selectResources) {
            if (checkResourceTradeStatus(resource) &&
                    resource.getCansaleNum() != null && resource.getCansaleNum().compareTo(BigDecimal.ZERO) > 0 && quantity.compareTo(resource.getCansaleNum()) <= 0) {
                    if (resource.getOrderminNum() == null && resource.getOrdermaxNum() == null) {
                        cansaleResources.add(resource);
                    } else if (resource.getOrderminNum() == null && resource.getOrdermaxNum() != null
                            && quantity.compareTo(resource.getOrdermaxNum()) <= 0) {
                        cansaleResources.add(resource);
                    } else if (resource.getOrderminNum() != null && resource.getOrdermaxNum() == null
                            && quantity.compareTo(resource.getOrderminNum()) >= 0) {
                        cansaleResources.add(resource);
                    } else if (resource.getOrderminNum() != null && resource.getOrdermaxNum() != null
                            && quantity.compareTo(resource.getOrderminNum()) >= 0
                            && quantity.compareTo(resource.getOrdermaxNum()) <= 0) {
                        cansaleResources.add(resource);
                    }
            }
        }
    }

    private static void resourceCriteriaAddDeliveryWay(String deliveryWay, Example.Criteria resourceCriteria) {
        if (DeliveryWayEnum.BUYER_TAKE.code().equals(deliveryWay)) {
            resourceCriteria.andEqualTo("ifTakeSelf", true);
        } else if (DeliveryWayEnum.SELLER_DELIVERY.code().equals(deliveryWay)) {
            resourceCriteria.andEqualTo("ifSellerDelivery", true);
        } else if (DeliveryWayEnum.PLATFORM_DELIVERY.code().equals(deliveryWay)) {
            resourceCriteria.andEqualTo("ifPlatformDelivery", true);
        }
    }

    @Override
    public List<ResourceDTO> resourceOrderPrepareDetail(ResourceOrderPrepareReqDTO req) {
        log.info("===resourceOrderPrepareDetail===->{}", JSON.toJSONString(req));
        //参数检查
        SelectResourceDTO selectResourceDTO = req.getSelectResourceDTOS().get(0);
        String deliveryWay = getDeliveryWay(req, selectResourceDTO);
        //初始化结果
        List<ResourceDTO> resourceList = Lists.newArrayList();
        List<Resource> cansaleResources = Lists.newArrayList();
        //设置购买数量
        BigDecimal quantity = BigDecimal.ZERO;
        if (selectResourceDTO.getQuantity() != null) {
            quantity = selectResourceDTO.getQuantity();
        }
        if (CsStringUtils.isNullOrBlank(selectResourceDTO.getResourceId())) {
            throw new BizException(BasicCode.PARAM_NULL, "资源ID");
        }
        //老资源
        Resource oldResource = resourceMapper.selectByPrimaryKey(selectResourceDTO.getResourceId());
        log.info("===1===oldResource===->{}", JSON.toJSONString(oldResource));
        //查询可以出货的资源
        Example resourceExample = new Example(Resource.class);
        Example.Criteria resourceCriteria = resourceExample.createCriteria();
        resourceCriteria.andEqualTo(DEL_FLG, 0);
        resourceCriteria.andEqualTo(GOODS_RESOURCE_ID, oldResource.getGoodsResourceId());
        resourceCriteria.andEqualTo(STATUS, ResourceStatusEnum.RES_STATUS100.getCode());
        resourceCriteriaAddLike(selectResourceDTO, resourceCriteria);
        this.processResourceRegionsExample(RESOURCE_REGIONS, resourceExample, req.getCountryName(), req.getProvinceCode()
                , req.getCityCode(), req.getAreaCode(), req.getStreetCode());
        if (DeliveryWayEnum.BUYER_TAKE.code().equals(deliveryWay)) {
            resourceCriteria.andEqualTo("ifTakeSelf", true);
        } else {
            resourceExample.and(resourceExample.createCriteria()
                    .andEqualTo("ifSellerDelivery", true)
                    .orEqualTo("ifPlatformDelivery", true));
        }
        List<Resource> selectResources = resourceMapper.selectByExample(resourceExample);
        //判断是否有货
        if (!CollectionUtils.isEmpty(selectResources)) {
            //查询可以选择购买的资源

            for (Resource resource : selectResources) {

                BigDecimal convertRate = BigDecimal.ONE;
                if (resource.getConvertRate() != null && !resource.getPriceUnit().equals(selectResourceDTO.getUnit())) {
                    convertRate = resource.getConvertRate();
                }

                quantity = getQuantity(resource, quantity, convertRate);
                //新的出货逻辑
                cansaleResourcesAddItems(resource, quantity, convertRate, cansaleResources);
            }
        }
        cansaleResources.stream().forEach(resource -> {
            ResourceDTO resourceDTO = new ResourceDTO();
            BeanUtils.copyProperties(resource, resourceDTO);
            resourceList.add(resourceDTO);
        });
        return resourceList;
    }

    private BigDecimal getQuantity(Resource resource, BigDecimal quantity, BigDecimal convertRate) {
        if (quantity.compareTo(BigDecimal.ZERO) == 0) {
            if (resource.getOrderminNum() != null && BigDecimal.ZERO.compareTo(resource.getOrderminNum().divide(convertRate, 0, RoundingMode.HALF_UP)) == 0) {
                quantity = resource.getOrderminNum().divide(convertRate, 0, RoundingMode.HALF_UP);
            } else {
                quantity = BigDecimal.ONE;
            }
        }
        return quantity;
    }

    private void cansaleResourcesAddItems(Resource resource, BigDecimal quantity, BigDecimal convertRate, List<Resource> cansaleResources) {
        if (checkResourceTradeStatus(resource)) {
            if (resource.getCansaleNum() != null && resource.getCansaleNum().compareTo(BigDecimal.ZERO) > 0
                    && quantity.compareTo(resource.getCansaleNum().divide(convertRate, 0, RoundingMode.HALF_UP)) <= 0) {
                if (resource.getOrderminNum() == null && resource.getOrdermaxNum() == null) {
                    cansaleResources.add(resource);
                } else if (resource.getOrderminNum() == null && resource.getOrdermaxNum() != null
                        && quantity.compareTo(resource.getOrdermaxNum().divide(convertRate, 0, RoundingMode.HALF_UP)) <= 0) {
                    cansaleResources.add(resource);
                } else if (resource.getOrderminNum() != null && resource.getOrdermaxNum() == null
                        && quantity.compareTo(resource.getOrderminNum().divide(convertRate, 0, RoundingMode.HALF_UP)) >= 0) {
                    cansaleResources.add(resource);
                } else if (resource.getOrderminNum() != null && resource.getOrdermaxNum() != null
                        && quantity.compareTo(resource.getOrderminNum().divide(convertRate, 0, RoundingMode.HALF_UP)) >= 0
                        && quantity.compareTo(resource.getOrdermaxNum().divide(convertRate, 0, RoundingMode.HALF_UP)) <= 0) {
                    cansaleResources.add(resource);
                }
            }
        }
    }

    private void resourceCriteriaAddLike(SelectResourceDTO selectResourceDTO, Example.Criteria resourceCriteria) {
        if (selectResourceDTO.getAttributes() != null && !selectResourceDTO.getAttributes().isEmpty()) {
            for (AttributeDTO attr : selectResourceDTO.getAttributes()) {
                if (attr.getAttributeValueDTOs() != null) {
                    for (AttributeValueDTO attrValue : attr.getAttributeValueDTOs()) {
                        resourceCriteria.andLike(RESOURCE_ATTRIBUTES,
                                "%" + attr.getAttributeNo() + ":" + attrValue.getAttributeValueNo() + "%");
                    }
                }
            }
        }
    }

    private String getDeliveryWay(ResourceOrderPrepareReqDTO req, SelectResourceDTO selectResourceDTO) {
        if (selectResourceDTO == null) {
            throw new BizException(BasicCode.PARAM_NULL, "选择的商品");
        }
        String deliveryWay = req.getDeliveryWay();
        if (CsStringUtils.isNullOrBlank(deliveryWay)) {
            throw new BizException(BasicCode.PARAM_NULL, "配送方式");
        }
        return deliveryWay;
    }

    @Override
    public List<Resource> getTimedOnsaleResource() {
        Example example = new Example(Resource.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("ifup", BooleanEnum.NO.code());
        criteria.andLessThanOrEqualTo("fixUptime", new Date());
        criteria.andEqualTo(STATUS, ResourceStatusEnum.RES_STATUS400.code());
        criteria.andEqualTo(DEL_FLG, BooleanEnum.NO.code());
        return resourceMapper.selectByExample(example);
    }

    @Override
    public List<Resource> getTimedOffsaleResource() {
        Example example = new Example(Resource.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("ifdown", BooleanEnum.NO.code());
        criteria.andLessThanOrEqualTo("fixDowntime", new Date());
        criteria.andEqualTo(DEL_FLG, BooleanEnum.NO.code());
        criteria.andEqualTo(STATUS, ResourceStatusEnum.RES_STATUS100.code());
        return resourceMapper.selectByExample(example);
    }

    @Override
    public List<Resource> getTimedOffsalingResource() {
        Example example = new Example(Resource.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(STATUS, ResourceStatusEnum.RES_STATUS300.code());
        return resourceMapper.selectByExample(example);
    }

    @Override
    public List<String> getDeliveryWays(List<String> resourceIds) {
        //参数检查
        if (CollectionUtils.isEmpty(resourceIds)) {
            throw new BizException(ResourceCode.PARAM_NULL, "购买商品IDs");
        }
        //初始化出参
        List<String> deliveryWays = Lists.newArrayList();
        //数据库查询
        Map<String, List<String>> map = Maps.newHashMap();
        resourceIds.forEach(resourceId -> {
            List<String> deliverys = Lists.newArrayList();
            Resource resource = resourceMapper.selectByPrimaryKey(resourceId);
            if (resource == null) {
                throw new BizException(ResourceCode.DATA_NOT_FOUND, "资源");
            }
            if (resource.getIfTakeSelf()) {
                deliverys.add(DeliveryWayEnum.BUYER_TAKE.code());
            }
            if (resource.getIfSellerDelivery()) {
                deliverys.add(DeliveryWayEnum.SELLER_DELIVERY.code());
            }
            if (resource.getIfPlatformDelivery()) {
                deliverys.add(DeliveryWayEnum.PLATFORM_DELIVERY.code());
            }
            map.put(resourceId, deliverys);
        });
        //去重
        deliveryWays = getDeliveryWays(map, deliveryWays);
        return deliveryWays;
    }

    private List<String> getDeliveryWays(Map<String, List<String>> map, List<String> deliveryWays) {
        int start = 0;
        for (List<String> value : map.values()) {
            if (start == 0) {
                for (String delivery : value) {
                    deliveryWays.add(delivery);
                }
                start = start + 1;
            } else {
                List<String> deliveryTmp = new ArrayList<>();
                for (String delivery : value) {
                    if (deliveryWays.contains(delivery)) {
                        deliveryTmp.add(delivery);
                    }
                }
                deliveryWays = deliveryTmp;
            }
        }
        return deliveryWays;
    }

    @Override
    public ConfirmResourceStoreDTO confirmResourceStore(ReqConfirmResourceStoreDTO req) {
        // 参数检查
        List<ResourceBuyInfoDTO> buyInfoDTOs = getResourceBuyInfoDTOS(req);
        //初始化结果
        ConfirmResourceStoreDTO dto = new ConfirmResourceStoreDTO();
        //取出可以出货的资源
        List<Resource> canBuyResources = Lists.newArrayList();
        buyInfoDTOs.forEach(buyInfoDTO -> {
            Resource resource = null;
            Example example = new Example(Resource.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo(RESOURCE_ID, buyInfoDTO.getResourceId());
            criteria.andEqualTo(DEL_FLG, false);
            criteria.andEqualTo(STATUS, ResourceStatusEnum.RES_STATUS100.code());
            this.processResourceRegionsExample(RESOURCE_REGIONS, example, req.getCountryName(),
                    req.getProvinceCode(), req.getCityCode(), req.getAreaCode(), req.getStreetCode());
            canBuyResourcesAddItems(buyInfoDTO, example, resource, canBuyResources);
        });
        //取出可以出货的仓库
        if (buyInfoDTOs.size() == canBuyResources.size()) {
            //获取仓库
            Map<String, List<String>> resourceStoresMap = getResourceStoresMap(req, canBuyResources);
            //去重
            List<String> storeIds = getStoreIds(resourceStoresMap, dto);
            //结果封装
            buildResult(req, storeIds, dto);
        } else { //无货
            dto.setIsHaveGoods(BooleanEnum.NO.code());
        }
        return dto;
    }

    @NotNull
    private List<String> getStoreIds(Map<String, List<String>> resourceStoresMap, ConfirmResourceStoreDTO dto) {
        if (CollectionUtils.isEmpty(resourceStoresMap)) {
            dto.setIsHaveGoods(BooleanEnum.NO.code());
        }
        List<String> storeIds = Lists.newArrayList();
        int start = 0;
        for (List<String> value : resourceStoresMap.values()) {
            if (start == 0) {
                for (String storeId : value) {
                    storeIds.add(storeId);
                }
                start = start + 1;
            } else {
                List<String> storeIdTmp = new ArrayList<>();
                for (String storeId : value) {
                    if (storeIds.contains(storeId)) {
                        storeIdTmp.add(storeId);
                    }
                }
                storeIds = storeIdTmp;
            }
        }
        return storeIds;
    }

    private void buildResult(ReqConfirmResourceStoreDTO req, List<String> storeIds, ConfirmResourceStoreDTO dto) {
        if (CollectionUtils.isEmpty(storeIds)) {
            dto.setIsHaveGoods(BooleanEnum.NO.code());
        } else {
            dto.setIsHaveGoods(BooleanEnum.YES.code());
            List<String> storeIdsTmp = Lists.newArrayList();
            if (DeliveryWayEnum.BUYER_TAKE.code().equals(req.getDeliveryWay())) { //自提
                storeIdsTmp = storeIds;
            } else { //配送
                storeIdsTmp.add(storeIds.get(0));
            }
            List<StoreDTO> storeDTOS = Lists.newArrayList();
            storeIdsTmp.forEach(storeId -> {
                StoreDTO storeDTO = new StoreDTO();
                WarehouseBaseDataDTO warehouse = warehouseService.selectWarehouseBaseData(storeId);
                if (warehouse != null) {
                    storeDTO.setStoreId(warehouse.getWarehouseId());
                    storeDTO.setStoreName(warehouse.getName());
                    storeDTO.setStoreType(warehouse.getType());
                    storeDTO.setStoreAddress(warehouse.getAddress());
                    storeDTO.setAdministrator(warehouse.getAdministrator());
                    storeDTO.setAdministratorPhone(warehouse.getAdministratorPhone());
                    storeDTO.setCity(warehouse.getCity());
                    storeDTO.setDistrict(warehouse.getDistrict());
                    storeDTO.setProvince(warehouse.getProvince());
                    storeDTOS.add(storeDTO);
                }
            });
            dto.setStoreDTOList(storeDTOS);
        }
    }

    @NotNull
    private Map<String, List<String>> getResourceStoresMap(ReqConfirmResourceStoreDTO req, List<Resource> canBuyResources) {
        Map<String, List<String>> resourceStoresMap = new HashMap<>();
        canBuyResources.forEach(resource -> {
            List<String> stores = Lists.newArrayList();
            if (!StoreTypeEnum.STORE_TYPE200.code().equals(resource.getStoreType())) { //卖家仓库
                stores.add(resource.getStoreId());
                resourceStoresMap.put(resource.getResourceId(), stores);
            } else { //中心仓
                if (CsStringUtils.isNotBlank(req.getShopCode())) { //这里的shopCode是推荐人表主键，如果有，查询推荐人表，获取关联门店仓库ID
                    log.info("iBuyerAndReferrerService->findById->req:" + req.getShopCode());
                    ReferrerInfoDTO referrerInfoDTO = iBuyerAndReferrerService.findById(req.getShopCode());
                    dealWarehouseDetailsDTOs(req, resource, referrerInfoDTO, stores);
                } else {
                    List<WarehouseDetailsDTO> warehouseDetailsDTOs = queryStoreListByCode(null,
                            req.getProvinceCode(), req.getCityCode(), req.getAreaCode(), resource.getSellerId());
                    if (!CollectionUtils.isEmpty(warehouseDetailsDTOs)) {
                        warehouseDetailsDTOs.forEach(warehouseDetailsDTO -> stores.add(warehouseDetailsDTO.getWarehouseId()));
                    }
                }
                resourceStoresMap.put(resource.getResourceId(), stores);
            }
        });
        return resourceStoresMap;
    }

    private void dealWarehouseDetailsDTOs(ReqConfirmResourceStoreDTO req, Resource resource, ReferrerInfoDTO referrerInfoDTO, List<String> stores) {
        if (referrerInfoDTO != null) {
            log.info("iBuyerAndReferrerService->findById->response:" + referrerInfoDTO.toString());
            String referrerStoreId = referrerInfoDTO.getReferrerStoreId();
            warehouseDetailsDTO(req, resource, stores, referrerStoreId);
        } else {
            List<WarehouseDetailsDTO> warehouseDetailsDTOs = queryStoreListByCode(null,
                    req.getProvinceCode(), req.getCityCode(), req.getAreaCode(), resource.getSellerId());
            if (!CollectionUtils.isEmpty(warehouseDetailsDTOs)) {
                warehouseDetailsDTOs.forEach(warehouseDetailsDTO -> stores.add(warehouseDetailsDTO.getWarehouseId()));
            }
        }
    }

    private void warehouseDetailsDTO(ReqConfirmResourceStoreDTO req, Resource resource, List<String> stores, String referrerStoreId) {
        if (CsStringUtils.isNotBlank(referrerStoreId)) {
            log.info("warehouseService->selectWarehouseBaseData->req:" + referrerStoreId);
            WarehouseBaseDataDTO warehouseBaseDataDTO = warehouseService.selectWarehouseBaseData(referrerStoreId);
            if (warehouseBaseDataDTO != null) {
                log.info("warehouseService->selectWarehouseBaseData->response:" + warehouseBaseDataDTO.toString());
                stores.add(warehouseBaseDataDTO.getWarehouseId());
            } else {
                List<WarehouseDetailsDTO> warehouseDetailsDTOs = queryStoreListByCode(null,
                        req.getProvinceCode(), req.getCityCode(), req.getAreaCode(), resource.getSellerId());
                if (!CollectionUtils.isEmpty(warehouseDetailsDTOs)) {
                    warehouseDetailsDTOs.forEach(warehouseDetailsDTO -> stores.add(warehouseDetailsDTO.getWarehouseId()));
                }
            }
        } else {
            List<WarehouseDetailsDTO> warehouseDetailsDTOs = queryStoreListByCode(null,
                    req.getProvinceCode(), req.getCityCode(), req.getAreaCode(), resource.getSellerId());
            if (!CollectionUtils.isEmpty(warehouseDetailsDTOs)) {
                warehouseDetailsDTOs.forEach(warehouseDetailsDTO -> stores.add(warehouseDetailsDTO.getWarehouseId()));
            }
        }
    }

    private void canBuyResourcesAddItems(ResourceBuyInfoDTO buyInfoDTO, Example example, Resource resource, List<Resource> canBuyResources) {
        List<Resource> resourceList = resourceMapper.selectByExample(example);
        if (!CollectionUtils.isEmpty(resourceList)) {
            resource = resourceList.get(0);
        }
        if (resource != null) {
            if (checkResourceTradeStatus(resource)) {
                dealResource(buyInfoDTO, resource, canBuyResources);
            }
        }
    }

    private void dealResource(ResourceBuyInfoDTO buyInfoDTO, Resource resource, List<Resource> canBuyResources) {
        if (resource.getCansaleNum() != null && resource.getCansaleNum().compareTo(BigDecimal.ZERO) > 0
                && buyInfoDTO.getBuyNum().compareTo(resource.getCansaleNum()) <= 0) {
            if (resource.getOrderminNum() == null && resource.getOrdermaxNum() == null) {
                canBuyResources.add(resource);
            } else if (resource.getOrderminNum() == null && resource.getOrdermaxNum() != null
                    && buyInfoDTO.getBuyNum().compareTo(resource.getOrdermaxNum()) <= 0) {
                canBuyResources.add(resource);
            } else if (resource.getOrderminNum() != null && resource.getOrdermaxNum() == null
                    && buyInfoDTO.getBuyNum().compareTo(resource.getOrderminNum()) >= 0) {
                canBuyResources.add(resource);
            } else if (resource.getOrderminNum() != null && resource.getOrdermaxNum() != null
                    && buyInfoDTO.getBuyNum().compareTo(resource.getOrderminNum()) >= 0
                    && buyInfoDTO.getBuyNum().compareTo(resource.getOrdermaxNum()) <= 0) {
                canBuyResources.add(resource);
            }
        }
    }

    private List<ResourceBuyInfoDTO> getResourceBuyInfoDTOS(ReqConfirmResourceStoreDTO req) {
        if (req == null) {
            throw new BizException(ResourceCode.PARAM_NULL, "入参对象");
        }
        if (CsStringUtils.isBlank(req.getBuyerId())) {
            throw new BizException(ResourceCode.PARAM_NULL, "买家编号");
        }
        if (CsStringUtils.isBlank(req.getDeliveryWay())) {
            throw new BizException(ResourceCode.PARAM_NULL, "配送类型");
        }
        List<ResourceBuyInfoDTO> buyInfoDTOs = req.getResourceBuyInfoDTOs();
        if (CollectionUtils.isEmpty(buyInfoDTOs)) {
            throw new BizException(ResourceCode.PARAM_NULL, "购买商品信息");
        }
        return buyInfoDTOs;
    }

    private boolean checkResourceLogisticRuleData(List<Resource> resources) {

        boolean platformSend = false;
        boolean sellerSend = false;

        for (Resource resource : resources) {

            if (resource.getIfPlatformDelivery()) {
                platformSend = true;
            }
            if (resource.getIfSellerDelivery()) {
                sellerSend = true;
            }
        }
        if (platformSend == sellerSend == true) {
            return false;
        }

        return true;

    }

    @Override
    public ResourceLogisticCostDTO computeResourceLogisticCost(Map<String, BigDecimal> resources, boolean isSelfTake) {

        if (resources == null || resources.isEmpty()) {
            throw new BizException(ResourceCode.INVALID_PARAM, "资源参数为空");
        }
        ResourceLogisticCostDTO result = new ResourceLogisticCostDTO();
        result.setNeedCheckWithLogisitc(true);
        result.setLogisticCost(BigDecimal.ZERO);
        List<Resource> resourcesData = new ArrayList<>();

        resourcesDataAddItems(resources, isSelfTake, resourcesData);

        ResourceLogisticCostDTO result1 = getResourceLogisticCostDTO2(resources, isSelfTake, resourcesData, result);
        if (result1 != null) return result1;
        return result;
    }

    @Nullable
    private ResourceLogisticCostDTO getResourceLogisticCostDTO2(Map<String, BigDecimal> resources, boolean isSelfTake, List<Resource> resourcesData, ResourceLogisticCostDTO result) {
        for (Resource resource : resourcesData) {
            if (isSelfTake) {
                if (!resource.getIfTakeSelf()) {
                    throw new BizException(ResourceCode.INVALID_PARAM, " 商品不允许自提 ");
                }
                result.setNeedCheckWithLogisitc(false);
                BigDecimal discount = BigDecimal.ZERO;
                if (resource.getTakeSelfDiscounts() != null) {
                    discount = resource.getTakeSelfDiscounts();
                }
                result.setResourcePrice(resource.getResourceId(), resource.getPrice().subtract(discount));
                result.setLogisticCost(BigDecimal.ZERO);
                result.setNeedCheckWithLogisitc(false);
                result.setResourceLogisticCost(resource.getResourceId(), BigDecimal.ZERO);
            } else {
                ResourceLogisticCostDTO result1 = getResourceLogisticCostDTO(resources, resource, result);
                if (result1 != null) return result1;
            }
        }
        return null;
    }

    @Nullable
    private ResourceLogisticCostDTO getResourceLogisticCostDTO(Map<String, BigDecimal> resources, Resource resource, ResourceLogisticCostDTO result) {
        if (resource.getIfSellerDelivery()) {
            result.setNeedCheckWithLogisitc(false);
            // free
            if (resource.getLogisticsWeight() != null && resources.get(resource.getResourceId()) != null
                    && resources.get(resource.getResourceId()).compareTo(resource.getLogisticsWeight()) >= 0) {
                if (LogisticsCostTypeEnum.PER_ORDER.getCode().equals(resource.getLogisticsType())) {
                    result.setLogisticCost(BigDecimal.ZERO);
                    return result;
                }
            } else {
                dealReslut(resources, resource, result);
            }
        }
        if (resource.getIfPlatformDelivery()) {
            result.setNeedCheckWithLogisitc(true);
            return result;
        }
        return null;
    }

    private void dealReslut(Map<String, BigDecimal> resources, Resource resource, ResourceLogisticCostDTO result) {
        BigDecimal cost = BigDecimal.ZERO;
        if (resource.getLogisticsPrice() != null) {
            cost = resource.getLogisticsPrice();
        }
        if (LogisticsCostTypeEnum.PER_ORDER.getCode().equals(resource.getLogisticsType())) {
            if (result.getLogisticCost() != null && cost.compareTo(result.getLogisticCost()) > 0) {
                result.setResourceLogisticCost(resource.getResourceId(), cost);
            }
        } else {
            result.setResourceLogisticCost(resource.getResourceId(), result.getLogisticCost().add(
                    resource.getLogisticsPrice().multiply(resources.get(resource.getResourceId()))));
        }
    }

    private void resourcesDataAddItems(Map<String, BigDecimal> resources, boolean isSelfTake, List<Resource> resourcesData) {
        for (Iterator<String> iterator = resources.keySet().iterator(); iterator.hasNext(); ) {
            String resourceId = iterator.next();
            Resource resource = this.get(resourceId);
            if (resource == null) {
                throw new BizException(ResourceCode.INVALID_PARAM, " 未能查找到资源 " + resourceId);
            }
            resourcesData.add(resource);
        }
        if (!isSelfTake) {
            if (!checkResourceLogisticRuleData(resourcesData)) {
                throw new BizException(ResourceCode.INVALID_PARAM, " 商品配送规则不同，不可同时购买 ");
            }
        }
    }

    @Override
    public List<ResourceDTO> queryResourceListByCondition(QueryResourceConditionDTO queryResourceConditionDTO) {
        log.info("===queryResourceListByCondition===->{}", JSON.toJSONString(queryResourceConditionDTO));
        //查询可以出货的资源
        Example resourceExample = new Example(Resource.class);
        Example.Criteria resourceCriteria = resourceExample.createCriteria();
        resourceCriteria.andEqualTo(DEL_FLG, 0);
        resourceCriteria.andEqualTo(STATUS, ResourceStatusEnum.RES_STATUS100.getCode());
        if (CsStringUtils.isNotBlank(queryResourceConditionDTO.getBindingSaleRegionId())) {
            log.info("绑定卖家销售区域===queryResourceListByCondition===bindingSaleRegionId=" + queryResourceConditionDTO.getBindingSaleRegionId());
            resourceCriteria.andEqualTo(SALE_AREA_REAL_CODE, queryResourceConditionDTO.getBindingSaleRegionId());
        } else {
            this.processResourceRegionsExample(RESOURCE_REGIONS, resourceExample, "",
                    queryResourceConditionDTO.getProvinceCode(),
                    queryResourceConditionDTO.getCityCode(),
                    queryResourceConditionDTO.getAreaCode(),
                    queryResourceConditionDTO.getStreetCode());
        }
        if (!CollectionUtils.isEmpty(queryResourceConditionDTO.getResourceIdList())) {
            resourceCriteria.andIn(RESOURCE_ID, queryResourceConditionDTO.getResourceIdList());
        }
        if (!CollectionUtils.isEmpty(queryResourceConditionDTO.getGoodsIdList())) {
            resourceCriteria.andIn(GOODS_ID, queryResourceConditionDTO.getGoodsIdList());
        }
        if (CsStringUtils.isNotEmpty(queryResourceConditionDTO.getSellerId())) {
            resourceCriteria.andEqualTo(SELLER_ID, queryResourceConditionDTO.getSellerId());
        }
        if (CsStringUtils.isNotEmpty(queryResourceConditionDTO.getStoreId())) {
            resourceCriteria.andEqualTo(STORE_ID, queryResourceConditionDTO.getStoreId());
        }
        if (DeliveryWayEnum.BUYER_TAKE.code().equals(queryResourceConditionDTO.getDeliveryWay())) {
            resourceCriteria.andEqualTo("ifTakeSelf", true);
        }
        if (DeliveryWayEnum.SELLER_DELIVERY.code().equals(queryResourceConditionDTO.getDeliveryWay())) {
            resourceCriteria.andEqualTo("ifSellerDelivery", true);
        }
        if (DeliveryWayEnum.PLATFORM_DELIVERY.code().equals(queryResourceConditionDTO.getDeliveryWay())) {
            resourceCriteria.andEqualTo("ifPlatformDelivery", true);
        }
        List<Resource> selectResources = resourceMapper.selectByExample(resourceExample);
        List<ResourceDTO> resourceList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(selectResources)) {
            selectResources.stream().forEach(resource -> {
                ResourceDTO resourceDTO = new ResourceDTO();
                BeanUtils.copyProperties(resource, resourceDTO);
                resourceList.add(resourceDTO);
            });
        }

        return resourceList;
    }
}
