package com.ecommerce.goods.controller;

import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.goods.api.dto.contract.DownloadResponseDTO;
import com.ecommerce.goods.api.dto.contract.ReqSellerContractTemplateDTO;
import com.ecommerce.goods.api.dto.contract.TrContractTemplateDTO;
import com.ecommerce.goods.service.IContractService;
import com.ecommerce.goods.service.IContractTemplateService;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * @Created锛�Mon Aug 05 19:10:28 CST 2019
 * <AUTHOR>
 * @Version:2
 * @Description:null
*/

@RestController
@Tag(name = "ContractTemplate", description = "合同模板服务")
@RequestMapping("/contractTemplate")
public class ContractTemplateController {

   @Autowired 
   private IContractTemplateService iContractTemplateService;
   @Autowired
   private IContractService iContractService;

   @Operation(summary = "创建合同模板")
   @PostMapping(value="/createContractTemplate")
   public Boolean createContractTemplate(@RequestBody @Parameter(description = "合同模板DTO") TrContractTemplateDTO createDTO){
      return iContractTemplateService.createContractTemplate(createDTO);
   }


   @Operation(summary = "查看合同模板")
   @PostMapping(value="/getContractTemplateDetail")
   public TrContractTemplateDTO getContractTemplateDetail(@RequestParam @Parameter(description = "模板ID") String templateId){
      return iContractTemplateService.getContractTemplateDetail(templateId);
   }


   @Operation(summary = "合同模板分页查询")
   @PostMapping(value="/pageContractTemplate")
   public PageInfo<TrContractTemplateDTO> pageContractTemplate(@RequestBody @Parameter(description = "请求卖家合同模板DTO") ReqSellerContractTemplateDTO reqDTO,@RequestParam @Parameter(description = "会员ID") String memberId){
      return iContractTemplateService.pageContractTemplate(reqDTO,memberId);
   }


   @Operation(summary = "获取合同列表")
   @PostMapping(value="/getContractTemplateList")
   public List<TrContractTemplateDTO> getContractTemplateList(@RequestBody @Parameter(description = "请求卖家合同模板DTO") ReqSellerContractTemplateDTO req,@RequestParam @Parameter(description = "会员ID") String memberId){
      return iContractTemplateService.getContractTemplateList(req,memberId);
   }


   @Operation(summary = "逻辑删除合同模板")
   @PostMapping(value="/deleteContractTemplate")
   public Boolean deleteContractTemplate(@RequestParam @Parameter(description = "模板ID") String templateId,@RequestParam @Parameter(description = "操作人") String operator){
      if(!iContractService.ifCanDeleteTemplate(templateId,null,operator)){
         throw new BizException(BasicCode.UNDEFINED_ERROR, "该模板被已生效合同使用，不能删除");
      }
      return iContractTemplateService.deleteContractTemplate(templateId);
   }

   @Operation(summary = "更新模板状态")
   @PostMapping(value="/updateContractTemplateStatus")
   public Boolean updateContractTemplateStatus(@RequestParam @Parameter(description = "模板ID") String templateId,@RequestParam @Parameter(description = "模板状态") Integer status,@RequestParam @Parameter(description = "操作人") String operator){
      if(status == 2 && !iContractService.ifCanDeleteTemplate(templateId,null,operator)){
         throw new BizException(BasicCode.UNDEFINED_ERROR, "该模板被已生效合同使用，不能禁用");
      }
      return iContractTemplateService.updateTemplateStatus(templateId,status,operator);
   }

   @Operation(summary = "下载合同模板")
   @PostMapping(value="/downloadFileByContract")
   public DownloadResponseDTO downloadFileByContract(@RequestParam @Parameter(description = "合同ID") String contractId){
      return iContractTemplateService.downloadFileByContract(contractId);
   }

   @Operation(summary = "下载调价函模板")
   @PostMapping(value="/downloadFileByAdjustPrice")
   public DownloadResponseDTO downloadFileByAdjustPrice(@RequestParam @Parameter(description = "调价函ID") String adjustPriceId){
      return iContractTemplateService.downloadFileByAdjustPrice(adjustPriceId);
   }


}
