package com.ecommerce.goods.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.goods.api.dto.AddItemQueryConditionDTO;
import com.ecommerce.goods.api.dto.GoodsAddItemDTO;
import com.ecommerce.goods.service.IGoodsAddItemService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;



@RestController
@Tag(name = "GoodsAddItem", description = "商品加价项服务")
@RequestMapping("/goodsAddItem")
public class GoodsAddItemController {

   @Autowired 
   private IGoodsAddItemService iGoodsAddItemService;

   @Operation(summary = "商家修改模板，list中可以放1条或者多条数据，根据主键修改")
   @PostMapping(value="/businessUpdateTemplate")
   public Boolean businessUpdateTemplate(@RequestBody @Parameter(description = "商品加价项值") List<GoodsAddItemDTO> arg0, @RequestParam @Parameter(description = "操作人ID") String arg1){
      return iGoodsAddItemService.businessUpdateTemplate(arg0,arg1);
   }


   @Operation(summary = "商家创建模板")
   @PostMapping(value="/businessCreateTemplate")
   public Boolean businessCreateTemplate(@RequestBody @Parameter(description = "商品加价项值") List<GoodsAddItemDTO> arg0,@RequestParam @Parameter(description = "会员ID") String arg1,@RequestParam @Parameter(description = "插座人ID") String arg2){
      return iGoodsAddItemService.businessCreateTemplate(arg0,arg1,arg2);
   }


   @Operation(summary = "平台模板改变了以后，更新卖家模板")
   @PostMapping(value="/updateGoodsAddItems")
   public Boolean updateGoodsAddItems(@RequestParam @Parameter(description = "会员ID") String arg0){
      return iGoodsAddItemService.updateGoodsAddItems(arg0);
   }


   @Operation(summary = "根据商品分类查询商家模板")
   @PostMapping(value="/listBusinessTemplate")
   public List<GoodsAddItemDTO> listBusinessTemplate(@RequestParam @Parameter(description = "商品分类Code") Integer arg0,@RequestParam @Parameter(description = "会员ID") String arg1){
      return iGoodsAddItemService.listBusinessTemplate(arg0,arg1);
   }
   
   @Operation(summary = "根据商品分类查询商家模板")
   @PostMapping(value="/listBusinessTemplateByResource")
   public List<GoodsAddItemDTO> listBusinessTemplateByResource(@RequestParam @Parameter(description = "资源ID") String arg0 ,@RequestParam @Parameter(description = "会员ID") String arg1){
      return iGoodsAddItemService.listBusinessTemplateByResource(arg0,arg1);
   }


   @Operation(summary = "根据商品分类查询平台模板")
   @PostMapping(value="/listBaseTemplate")
   public List<GoodsAddItemDTO> listBaseTemplate(@RequestParam @Parameter(description = "商品分类Code") Integer arg0){
      return iGoodsAddItemService.listBaseTemplate(arg0);
   }


   @Operation(summary = "通过addItemId加价项，多项查询以逗号分开")
   @PostMapping(value="/getGoodsAddItems")
   public List<GoodsAddItemDTO> getGoodsAddItems(@RequestParam @Parameter(description = "加价项ID") String arg0){
      return iGoodsAddItemService.getGoodsAddItems(arg0);
   }


   @Operation(summary = "获取平台加价项模板版本")
   @PostMapping(value="/getItemVersion")
   public String getItemVersion(){
      return iGoodsAddItemService.getItemVersion();
   }

   @Operation(summary = "同步ERP加价项")
   @PostMapping(value="/syncERPGoodsAddItem")
   public void syncERPGoodsAddItem(@RequestParam @Parameter(description = "卖家ID") String arg0 ,@RequestParam @Parameter(description = "操作人") String arg1){
      iGoodsAddItemService.syncERPGoodsAddItem(arg0, arg1);
   }

   @Operation(summary = "通过Code获取加价项")
   @PostMapping(value="/getGoodsAddItemByCode")
   public GoodsAddItemDTO getGoodsAddItemByCode(@RequestParam @Parameter(description = "erpCode") String arg0 ,@RequestParam @Parameter(description = "卖家ID") String arg1){
      return iGoodsAddItemService.getGoodsAddItemByCode(arg0, arg1);
   }

   @Operation(summary = "批量通过Code获取加价项")
   @PostMapping(value="/getGoodsAddItemsByCode")
   public List<GoodsAddItemDTO> getGoodsAddItemsByCode(@RequestParam @Parameter(description = "erpCode列表") List<String> arg0 ,@RequestParam @Parameter(description = "卖家ID") String arg1){
      return iGoodsAddItemService.getGoodsAddItemsByCode(arg0, arg1);
   }

   @Operation(summary = "批量通过Id获取加价项")
   @PostMapping(value="/getGoodsAddItemListById")
   public List<GoodsAddItemDTO> getGoodsAddItemListById(@RequestParam List<String> addItemIdList, @RequestParam String sellerId){
      return iGoodsAddItemService.getGoodsAddItemListById(addItemIdList, sellerId);
   }

   @Operation(summary = "条件查询加价项列表")
   @PostMapping(value="/queryAddItemByCondition")
   public List<GoodsAddItemDTO> queryAddItemByCondition(@RequestBody AddItemQueryConditionDTO addItemQueryConditionDTO){
      return iGoodsAddItemService.queryAddItemByCondition(addItemQueryConditionDTO);
   }

   @Operation(summary = "绑定ERP加价项")
   @PostMapping(value="/bindERPAddItem")
   public void bindERPAddItem(@RequestBody GoodsAddItemDTO dto,
                              @Parameter(description = "操作人ID") @RequestParam String operatorId) {
      iGoodsAddItemService.bindERPAddItem(dto, operatorId);
   }
}
