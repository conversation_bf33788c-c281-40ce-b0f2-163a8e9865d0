package com.ecommerce.goods.service.attibute.impl;

import com.ecommerce.goods.biz.IGoodsBiz;
import com.ecommerce.goods.biz.IGoodsCategoryBiz;
import com.ecommerce.goods.dao.vo.Goods;
import com.ecommerce.goods.service.attibute.ICategoryAttributeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class CategoryArributeService implements ICategoryAttributeService{

    @Autowired
    private IGoodsBiz goodsBiz;

    @Autowired
    private IGoodsCategoryBiz goodsCategoryBiz;

    @Override
    public Boolean ifSupportAdditem(String goodsId) {
        Goods goodsDTO = goodsBiz.get(goodsId);
        return goodsCategoryBiz.ifSupportAdditem(goodsDTO.getCategoryType());
    }

    @Override
    public Boolean ifComputePorterage(String goodsId) {
        Goods goodsDTO = goodsBiz.get(goodsId);
        return goodsCategoryBiz.ifComputePorterage(goodsDTO.getCategoryType());
    }

    @Override
    public Boolean ifSingleBuy(String goodsId) {
        Goods goodsDTO = goodsBiz.get(goodsId);
        return goodsCategoryBiz.ifSingleBuy(goodsDTO.getCategoryType());
    }

    @Override
    public Boolean getSplitBillNode(String goodsId) {
        Goods goodsDTO = goodsBiz.get(goodsId);
        return goodsCategoryBiz.getSplitBillNode(goodsDTO.getCategoryType());
    }

    @Override
    public Boolean isSellerConfirm(String goodsId) {
        Goods goodsDTO = goodsBiz.get(goodsId);
        return goodsCategoryBiz.isSellerConfirm(goodsDTO.getCategoryType());
    }
}
