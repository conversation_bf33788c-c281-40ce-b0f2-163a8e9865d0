package com.ecommerce.goods.service.handler;

import com.alibaba.fastjson.JSON;
import com.ecommerce.goods.biz.IGoodsEvaluateBiz;
import com.ecommerce.goods.service.handler.bean.MqOrderEvaluateData;
import com.ecommerce.mq.core.MQMessage;
import com.ecommerce.mq.core.handler.MessageHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date: 2019/5/7 6:02 PM
 * @Description 订单评价完成，针对商品评价消息监听
 */
@Slf4j
@Component
public class GoodsEvaluateConsumerHandler implements MessageHandler{

    private static final String QUEUENAME = "order_evaluate_queue_goods";

    @Autowired
    protected IGoodsEvaluateBiz iGoodsEvaluateBiz;

    @Override
    public boolean handle(MQMessage mqMessage) {
        String data = (String) mqMessage.getData();
        log.info("\n ### Goods 收到评价商品信息 --->>> {} \n", data);
        MqOrderEvaluateData mqOrderEvaluateData = JSON.parseObject(data,MqOrderEvaluateData.class);
        try{
            boolean res = iGoodsEvaluateBiz.saveGoodsEvaluateInfo(mqOrderEvaluateData);
            if(!res){
                log.info("\n ### Goods false 同步商品评价信息失败 \n");
                return false;
            }
            return true;
        }catch (Exception e){
            log.info("\n ### Goods Exception 同步商品评价方法异常 \n");
            return false;
        }
    }

    @Override
    public void handleRetryMax(MQMessage mqMessage) {
        log.info("{}", mqMessage);
    }

    @Override
    public String handleType() {
        return QUEUENAME;
    }
}
