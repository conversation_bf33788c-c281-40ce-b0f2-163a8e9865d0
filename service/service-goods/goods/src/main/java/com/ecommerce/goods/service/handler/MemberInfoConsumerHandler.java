package com.ecommerce.goods.service.handler;

import com.alibaba.fastjson.JSON;
import com.ecommerce.goods.dao.mapper.MemberNameUpdateMapper;
import com.ecommerce.member.api.dto.member.MemberSimpleDTO;
import com.ecommerce.mq.core.MQMessage;
import com.ecommerce.mq.core.handler.MessageHandler;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 会员信息更新，同步更新冗余的会员名
 */
@Slf4j
@Component
public class MemberInfoConsumerHandler implements MessageHandler {

    @Value("${spring.application.name}")
    private String applicationName;

    @Autowired
    private MemberNameUpdateMapper memberNameUpdateMapper;

    private static final String BUYER = "buyer";
    private static final String SELLER = "seller";
    // fixme 移除不存在的表 amrjlg 2021-12-6 16:33:50
    private final List<TableInfo> tableInfoList = Lists.newArrayList(
            new TableInfo("tr_contract", BUYER, SELLER),
            new TableInfo("tr_contract_adjust_price", BUYER, SELLER),
            new TableInfo("tr_contract_adjust_price_template_variable", SELLER),
            //new TableInfo("tr_resource",SELLER),
            new TableInfo("tr_resource_goods", SELLER),
            new TableInfo("tr_resource_history", SELLER),
            new TableInfo("tr_uneffective_resource", SELLER),
            new TableInfo("go_purchase", SELLER),
            new TableInfo("go_purchase_history", SELLER),
            new TableInfo("go_resource", SELLER),
            new TableInfo("go_resource_history", SELLER),
            new TableInfo("go_resource_uneffective", SELLER),
            new TableInfo("tr_contract_batch_adjust_price_result", BUYER)
    );

    @Override
    public boolean handle(MQMessage mqMessage) {
        try {
            String data = (String) mqMessage.getData();
            log.info("synchronize memberInfo receive --->>> {}", data);
            MemberSimpleDTO member = JSON.parseObject(data, MemberSimpleDTO.class);
            if (CsStringUtils.isEmpty(member.getMemberId()) || CsStringUtils.isEmpty(member.getMemberName())) {
                return true;
            }
            String memberId = member.getMemberId();
            String memberName = member.getMemberName();

            for (TableInfo tableInfo : tableInfoList) {
                String table = tableInfo.getTableName();
                if (tableInfo.getPrefix() != null && tableInfo.getPrefix().length > 0) {
                    for (String prefix : tableInfo.getPrefix()) {
                        int count = memberNameUpdateMapper.updateMemberName2(table, prefix, memberId, memberName);
                        log.info("update table:{} {}_Name:{},result count:{}", table, prefix, memberName, count);
                    }
                } else {
                    int count = memberNameUpdateMapper.updateMemberName(table, memberId, memberName);
                    log.info("update table:{} memberName:{},result count:{}", table, memberName, count);
                }
            }
        } catch (Exception e) {
            log.info("同步会员名称失败:{}", e.getMessage(), e);
            return false;
        }
        return true;
    }

    @Override
    public void handleRetryMax(MQMessage mqMessage) {
        log.info("{}", mqMessage);
    }

    @Override
    public String handleType() {
        return applicationName + "_member_info";
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TableInfo {
        private String[] prefix;
        private String tableName;

        private TableInfo(String tableName, String... prefix) {
            this.tableName = tableName;
            this.prefix = prefix;
        }
    }
}
