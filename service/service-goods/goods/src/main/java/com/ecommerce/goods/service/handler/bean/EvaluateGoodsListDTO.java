package com.ecommerce.goods.service.handler.bean;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class EvaluateGoodsListDTO {

    /**
     * 商品ID
     */
    private String goodsId;

    /**
     * 商品图片
     */
    private String goodsImg;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 购买情况（比如：23吨，数量+单位）
     */
    private String goodsQuantity;

    /**
     * 商品评分
     */
    private BigDecimal score;

    /**
     * 商品文字评论
     */
    private String content;
}
