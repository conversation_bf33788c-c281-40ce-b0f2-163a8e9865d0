package com.ecommerce.goods.service.impl;

import com.ecommerce.goods.api.dto.BaseGoodDetailAuditDto;
import com.ecommerce.goods.api.dto.BaseGoodsAuditDTO;
import com.ecommerce.goods.api.enums.AuditAction;
import com.ecommerce.goods.biz.IGoodsAuditBiz;
import com.ecommerce.goods.service.IBaseGoodAuditService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class BaseGoodAuditService implements IBaseGoodAuditService {

    private final IGoodsAuditBiz goodsAuditBiz;

    public BaseGoodAuditService(IGoodsAuditBiz goodsAuditBiz) {
        this.goodsAuditBiz = goodsAuditBiz;
    }

    @Override
    public boolean applyGoodsAudit(BaseGoodsAuditDTO dto, AuditAction action, String operator) {
        return goodsAuditBiz.applyGoodsAudit(dto, action, operator);
    }

    @Override
    public boolean applyCreateBaseGood(BaseGoodsAuditDTO dto, String operator) {
        return goodsAuditBiz.applyCreateBaseGood(dto, operator);
    }

    @Override
    public boolean applyUpdateBaseGood(BaseGoodsAuditDTO dto, String operator) {
        return goodsAuditBiz.applyUpdateBaseGood(dto, operator);
    }

    @Override
    public boolean applyDeleteGoods(List<String> goodsId, String operator) {
        return goodsAuditBiz.applyDeleteGoods(goodsId, operator);
    }

    @Override
    public boolean doAudit(String goodId, boolean pass, String msg, String operator) {
        return goodsAuditBiz.doAudit(goodId, pass, msg, operator);
    }
    @Override
    public BaseGoodDetailAuditDto baseGoodDetails(String goodId){
        return goodsAuditBiz.baseGoodDetails(goodId);
    }
}
