package com.ecommerce.goods.service.impl;

import com.ecommerce.goods.api.dto.base.PageQuery;
import com.ecommerce.goods.api.dto.contract.variety.*;
import com.ecommerce.goods.biz.IConcreteVarietyBiz;
import com.ecommerce.goods.service.IConcreteVarietyService;
import com.github.pagehelper.PageInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class ConcreteVarietyService implements IConcreteVarietyService {


    private final IConcreteVarietyBiz concreteVarietyBiz;

    @Override
    public Boolean add(VarietyAddDTO addDTO) {
        return concreteVarietyBiz.add(addDTO);
    }

    @Override
    public Boolean update(VarietyUpdateDTO updateDTO) {
        return concreteVarietyBiz.update(updateDTO);
    }

    @Override
    public Boolean delete(VarietyDeleteDTO deleteDTO) {
        return concreteVarietyBiz.delete(deleteDTO);
    }

    @Override
    public Boolean disabled(VarietyDisableDTO disableDTO) {
        return concreteVarietyBiz.disabled(disableDTO);
    }

    @Override
    public Boolean enabled(VarietyEnableDTO enableDTO) {
        return concreteVarietyBiz.enabled(enableDTO);
    }

    @Override
    public PageInfo<VarietyDTO> findAll(PageQuery<VarietyQueryDTO> query) {
        return concreteVarietyBiz.findAll(query);
    }

    @Override
    public List<VarietyDTO> findByMemberId(String memberId) {
        return concreteVarietyBiz.findByMemberId(memberId);
    }
}
