package com.ecommerce.goods.service.impl;

import com.ecommerce.goods.api.dto.contract.ContractAdjustPriceAddItemDTO;
import com.ecommerce.goods.biz.IContractAdjustPriceAddItemBiz;
import com.ecommerce.goods.service.IContractAdjustPriceAddItemService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class ContractAdjustPriceAddItemService implements IContractAdjustPriceAddItemService {

    private final IContractAdjustPriceAddItemBiz contractAdjustPriceAddItemBiz;

    @Override
    public List<ContractAdjustPriceAddItemDTO> getContractAdjustPriceAddItem(String contractAdjustPriceId) {
        return contractAdjustPriceAddItemBiz.getContractAdjustPriceAddItem(contractAdjustPriceId);
    }

    @Override
    public List<ContractAdjustPriceAddItemDTO> findContractAdjustPriceAddItemList(String contractSequence) {
        return contractAdjustPriceAddItemBiz.findContractAdjustPriceAddItemList(contractSequence);
    }
}
