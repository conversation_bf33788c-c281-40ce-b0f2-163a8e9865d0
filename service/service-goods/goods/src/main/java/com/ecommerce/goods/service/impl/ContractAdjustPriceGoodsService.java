package com.ecommerce.goods.service.impl;

import com.ecommerce.goods.api.dto.contract.ContractAdjustPriceGoodsDTO;
import com.ecommerce.goods.biz.IContractAdjustPriceGoodsBiz;
import com.ecommerce.goods.service.IContractAdjustPriceGoodsService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class ContractAdjustPriceGoodsService implements IContractAdjustPriceGoodsService {

    private final IContractAdjustPriceGoodsBiz contractAdjustPriceGoodsBiz;

    @Override
    public List<ContractAdjustPriceGoodsDTO> getContractAdjustPriceGoods(String contractAdjustPriceId) {
        return contractAdjustPriceGoodsBiz.getContractAdjustPriceGoods(contractAdjustPriceId);
    }

    @Override
    public List<ContractAdjustPriceGoodsDTO> findContractAdjustPriceGoodsList(String contractSequence) {
        return contractAdjustPriceGoodsBiz.findContractAdjustPriceGoodsList(contractSequence);
    }
}
