package com.ecommerce.goods.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.ecommerce.base.api.dto.attachment.AttachmentFileDTO;
import com.ecommerce.base.api.service.IAttachmentService;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.service.common.uuid.UUIDGenerator;
import com.ecommerce.common.utils.BeanConvertUtils;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.goods.api.dto.contract.ContractAdjustPriceAddItemDTO;
import com.ecommerce.goods.api.dto.contract.ContractAdjustPriceDTO;
import com.ecommerce.goods.api.dto.contract.ContractAdjustPriceGoodsDTO;
import com.ecommerce.goods.api.dto.contract.TrContractAdditemDTO;
import com.ecommerce.goods.api.dto.contract.TrContractDTO;
import com.ecommerce.goods.api.dto.contract.TrContractGoodsDTO;
import com.ecommerce.goods.api.dto.contract.TrContractTemplateDTO;
import com.ecommerce.goods.api.enums.contract.ContractAdjustStatusEnum;
import com.ecommerce.goods.biz.IContractAddItemBiz;
import com.ecommerce.goods.biz.IContractAdjustPriceBiz;
import com.ecommerce.goods.biz.IContractBatchAdjustPriceResultBiz;
import com.ecommerce.goods.biz.IContractBiz;
import com.ecommerce.goods.biz.IContractGoodsBiz;
import com.ecommerce.goods.biz.IContractGoodsHistoryBiz;
import com.ecommerce.goods.biz.IContractTemplateBiz;
import com.ecommerce.goods.biz.impl.vo.contract.ContractAndChilds;
import com.ecommerce.goods.common.DocTemplateUtils;
import com.ecommerce.goods.dao.vo.ContractAdjustPrice;
import com.ecommerce.goods.dao.vo.ContractBatchAdjustPriceResult;
import com.ecommerce.goods.dao.vo.TrContract;
import com.ecommerce.goods.dao.vo.TrContractAdditem;
import com.ecommerce.goods.dao.vo.TrContractGoods;
import com.ecommerce.goods.service.IContractAdjustPriceService;
import com.ecommerce.order.api.dto.OrderSimpleQueryContractGoodsDTO;
import com.ecommerce.order.api.service.IOrderService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayInputStream;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ContractAdjustPriceService implements IContractAdjustPriceService {

    @Autowired
    private IContractAdjustPriceBiz iContractAdjustPriceBiz;
    @Autowired
    private IContractBiz contractBiz;
    @Autowired
    private IContractGoodsBiz contractGoodsBiz;
    @Autowired
    private IContractGoodsHistoryBiz contractGoodsHistoryBiz;
    @Autowired
    private IContractAddItemBiz contractAddItemBiz;
    @Autowired
    private IContractTemplateBiz contractTemplateBiz;
    @Lazy
    @Autowired
    private IContractBatchAdjustPriceResultBiz contractBatchAdjustPriceResultBiz;
    @Autowired
    private UUIDGenerator uuidGenerator;

    @Autowired
    private IAttachmentService attachmentService;
    @Autowired
    private IOrderService orderService;

    //只创建调价函，不调价
    @Transactional
    @Override
    public Boolean createContractAdjustPrice(TrContractDTO contractDTO, String operatorId) {
        log.info(" == >> 创建调价函 << ==");
        this.checkContractId(contractDTO, operatorId);

        List<TrContractGoodsDTO> goodsDTOList = contractDTO.getContractGoodsDTOS();
        List<TrContractAdditemDTO> addItemDTOList = contractDTO.getContractAdditemDTOS();
        TrContractDTO oldContractDTO = contractBiz.getContract(contractDTO.getContractId());
        log.info("oldContractDTO.oldContractDTO.size:{}",oldContractDTO.getContractGoodsDTOS() == null ? 0 : oldContractDTO.getContractGoodsDTOS().size());

        this.checkContractAndList(goodsDTOList, addItemDTOList, oldContractDTO);

        // 查询历史调价记录
        if (isExistUnExecute(oldContractDTO.getContractSequence(), oldContractDTO.getContractNumber())) {
            log.info(" 存在历史未执行的调价记录");

            iContractAdjustPriceBiz.cancel(oldContractDTO.getSellerId(),oldContractDTO.getBuyerId(),oldContractDTO.getContractSequence(), oldContractDTO.getContractNumber(),operatorId);
        }

        ContractAdjustPriceDTO contractAdjustPriceDTO = this.createProcess(contractDTO,  goodsDTOList, addItemDTOList, oldContractDTO);

        return iContractAdjustPriceBiz.createContractAdjustPrice(contractAdjustPriceDTO, operatorId);
    }

    @Transactional
    @Override
    public Boolean createContractAdjustPrice2(TrContractDTO contractDTO, String operatorId) {
        //如果是立即执行
        if (contractDTO != null && CsStringUtils.isNotEmpty(operatorId) && BooleanUtils.isTrue(contractDTO.getIsImmediately())) {
            String msg = CsStringUtils.isBlank(contractDTO.getReason()) ? "合同调价" : contractDTO.getReason();
            Date now = new Date();
            //合同转历史
            ContractAndChilds contractAndChilds = contractBiz.updateContractToHistory(contractDTO.getContractId(), null, operatorId, now);
            //新版本的合同
            TrContract contract = contractAndChilds.getContract();
            //新版本的合同id
            String contractId = contract.getContractId();
            List<ContractBatchAdjustPriceResult> logList = Lists.newArrayList();
            if( !CollectionUtils.isEmpty(contractDTO.getContractGoodsDTOS())) {
                //合同调价 如果有
                List<TrContractGoods> goodsList = BeanConvertUtils.convertList(contractDTO.getContractGoodsDTOS(), TrContractGoods.class);
                // 2021.5.19 合同调价不关闭订单
                //如果有商品有价格变化
                /*Collection<OrderSimpleQueryContractGoodsDTO> changePriceContractGoods = getChangePriceGoodsIds(contractAndChilds.getGoodsList(),goodsList);
                if(!changePriceContractGoods.isEmpty()) {
                    List<OrderSimpleQueryContractDTO> query = contractBiz.getContractIdAndGoodsIds(contractId,changePriceContractGoods);
                    log.info("合同调价尝试关闭订单,tryAutoCloseOrder.OrderSimpleQueryDTO:{}", JSON.toJSONString(query));
                    orderService.tryAutoCloseOrder(new OrderSimpleQueryDTO(query));
                }*/
                List<ContractBatchAdjustPriceResult> logList1 = contractGoodsBiz.updateContractGoodsPrice(contract, goodsList, operatorId, now,msg);
                logList.addAll(logList1);
            }
            if( !CollectionUtils.isEmpty(contractDTO.getContractAdditemDTOS())) {
                //加价项调价 如果有
                List<TrContractAdditem> additemList = BeanConvertUtils.convertList(contractDTO.getContractAdditemDTOS(), TrContractAdditem.class);
                List<ContractBatchAdjustPriceResult> logList2 = contractAddItemBiz.updateContractAddItemPrice(contract, additemList, operatorId, now,msg);
                logList.addAll(logList2);
            }
            contractDTO.setUpdateTime(now);
            contractDTO.setUpdateUser(operatorId);
            contractDTO.setEffectiveTime(now);
            //记录价格变化历史
            //添加调价日志
            contractBatchAdjustPriceResultBiz.insert(logList,now.getTime());
            contract = new TrContract();
            contract.setContractId(contractId);
            contract.setPriceUpdateTime(now);
            contract.setUpdateFlg(1);
            contract.setBatchAdjustPriceCode("");
            contract.setUpdateRemark(msg);
            //更新调价信息
            contractBiz.updateSelective(contract);

            return createContractAdjustPrice(contractDTO,operatorId);
        }
        return Boolean.FALSE;
    }

    private List<OrderSimpleQueryContractGoodsDTO> getChangePriceGoodsIds(List<TrContractGoods> oldGoodsList,List<TrContractGoods> goodsList){
        List<OrderSimpleQueryContractGoodsDTO> changeGoods = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(oldGoodsList)) {
            Set<String> changeGoodsId = Sets.newHashSet();
            Map<String, TrContractGoods> goodsDTOMap = oldGoodsList.stream().filter(item -> CsStringUtils.isNotBlank(item.getContractGoodsId())).collect(Collectors.toMap(TrContractGoods::getContractGoodsId, Function.identity()));
            for (TrContractGoods goods : goodsList) {
                TrContractGoods oldGoodsDTO = goodsDTOMap.get(goods.getContractGoodsId());
                if (oldGoodsDTO == null) {
                    log.info(" >> 原商品不存在 ContractGoodsId: {}", goods.getContractGoodsId());
                    continue;
                }
                BigDecimal oldFactoryPrice = checkNumberAsZero(oldGoodsDTO.getOutFactoryPrice());
                BigDecimal oldShipPrice = checkNumberAsZero(oldGoodsDTO.getShipPrice());
                //如果为true 则表示价格发生变化
                if ((oldFactoryPrice.compareTo(goods.getOutFactoryPrice()) != 0 || oldShipPrice.compareTo(goods.getShipPrice()) != 0) &&
                        !changeGoodsId.contains(oldGoodsDTO.getTransportType() +"_"+ oldGoodsDTO.getGoodsId())) {
                    changeGoods.add(new OrderSimpleQueryContractGoodsDTO(oldGoodsDTO.getTransportType(), oldGoodsDTO.getGoodsId()));
                    changeGoodsId.add(oldGoodsDTO.getTransportType() +"_"+ oldGoodsDTO.getGoodsId());
                }
            }
        }
        return changeGoods;
    }

    @Transactional
    @Override
    public Boolean createContractAdjustPriceForNumChange(TrContractDTO contractDTO, String operatorId) {
        log.info(" == >> 创建调价函--记录数量改变 << ==");
        this.checkContractId(contractDTO, operatorId);

        List<TrContractGoodsDTO> goodsDTOList = contractDTO.getContractGoodsDTOS();
        List<TrContractAdditemDTO> addItemDTOList = contractDTO.getContractAdditemDTOS();
        TrContractDTO oldContractDTO = contractBiz.getContract(contractDTO.getContractId());

        this.checkContractAndList(goodsDTOList, addItemDTOList, oldContractDTO);

        ContractAdjustPriceDTO contractAdjustPriceDTO = this.createProcess(contractDTO, goodsDTOList, addItemDTOList, oldContractDTO);

        return iContractAdjustPriceBiz.createContractAdjustPriceForNumChange(contractAdjustPriceDTO, operatorId);
    }

    private void checkContractId(TrContractDTO contractDTO, String operatorId) {
        log.info(" param: {}", contractDTO);
        if (Objects.isNull(contractDTO) || CsStringUtils.isEmpty(contractDTO.getContractId()) || CsStringUtils.isEmpty(operatorId)) {
            throw new BizException(BasicCode.PARAM_NULL, "合同ID");
        }
    }

    private void checkContractAndList(List<TrContractGoodsDTO> goodsDTOList, List<TrContractAdditemDTO> addItemDTOList, TrContractDTO oldContractDTO) {
        if (CollectionUtils.isEmpty(goodsDTOList) && CollectionUtils.isEmpty(addItemDTOList)) {
            throw new BizException(BasicCode.PARAM_NULL);
        }
        if (oldContractDTO == null || oldContractDTO.getDelFlg() == 1) {
            throw new BizException(BasicCode.DB_DATA_NOT_FOUND);
        }
    }

    private ContractAdjustPriceDTO createProcess(TrContractDTO contractDTO,List<TrContractGoodsDTO> goodsDTOList, List<TrContractAdditemDTO> addItemDTOList, TrContractDTO oldContractDTO) {
        log.info("oldContractDTO.oldContractDTO.size:{}",oldContractDTO.getContractGoodsDTOS() == null ? 0 : oldContractDTO.getContractGoodsDTOS().size());
        Boolean isImmediately = contractDTO.getIsImmediately();
        Date time = contractDTO.getEffectiveTime();
        checkParams(isImmediately, time);
        int executeStatus = getExecuteStatus(isImmediately);

        ContractAdjustPriceDTO contractAdjustPriceDTO = new ContractAdjustPriceDTO();
        List<ContractAdjustPriceGoodsDTO> goodsList = Lists.newArrayList();
        List<ContractAdjustPriceAddItemDTO> addItemList = Lists.newArrayList();

        log.info(" >> 商品:{}",goodsDTOList == null ? 0 : goodsDTOList.size());
        addGoodsList(goodsDTOList, oldContractDTO, goodsList);
        log.info(" >> 加价项");
        addItemList(addItemDTOList, oldContractDTO, addItemList);

        if (CollectionUtils.isEmpty(goodsList) && CollectionUtils.isEmpty(addItemList)) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "价格无变化，无需调价");
        }

        BeanUtils.copyProperties(oldContractDTO, contractAdjustPriceDTO);
        contractAdjustPriceDTO.setContractAdjustPriceTemplateId(contractDTO.getAdjustPriceTemplateId());
        if (CsStringUtils.isBlank(contractAdjustPriceDTO.getContractAdjustPriceTemplateId())) {
            contractAdjustPriceDTO.setContractAdjustPriceTemplateId(oldContractDTO.getAdjustPriceTemplateId());
        }
        contractAdjustPriceDTO.setExecuteStatus(executeStatus);
        contractAdjustPriceDTO.setEffectiveTime(contractDTO.getEffectiveTime());
        contractAdjustPriceDTO.setAdjustPriceContent(contractDTO.getReason());
        contractAdjustPriceDTO.setContractAdjustPriceGoodsDTOList(goodsList);
        contractAdjustPriceDTO.setContractAdjustPriceAddItemDTOList(addItemList);
        return contractAdjustPriceDTO;
    }

    private void addItemList(List<TrContractAdditemDTO> addItemDTOList, TrContractDTO oldContractDTO, List<ContractAdjustPriceAddItemDTO> addItemList) {
        if (!CollectionUtils.isEmpty(addItemDTOList)) {
            List<TrContractAdditemDTO> oldAddItemDTOList = oldContractDTO.getContractAdditemDTOS();
            if (!CollectionUtils.isEmpty(oldAddItemDTOList)) {
                for (TrContractAdditemDTO addItemDTO : addItemDTOList) {
                    Optional<TrContractAdditemDTO> optional = oldAddItemDTOList.stream().filter(item -> item.getContractAdditemId().equals(addItemDTO.getContractAdditemId())).findFirst();
                    if (!optional.isPresent()) {
                        log.info(" >> 原加价项不存在, ContractAdditemId: {}", addItemDTO.getContractAdditemId());
                        continue;
                    }
                    TrContractAdditemDTO oldAddItemDTO = optional.get();
                    BigDecimal oldPrice = checkNumberAsZero(oldAddItemDTO.getAdditemPrice());
                    if (oldPrice.compareTo(addItemDTO.getAdditemPrice()) == 0) {
                        log.info(" >> 价格没变化continue, ContractAdditemId: {}", addItemDTO.getContractAdditemId());
                        continue;
                    }
                    // 查找父节点
                    findAddItemParentNode(addItemList, oldAddItemDTOList, oldAddItemDTO.getParentId());

                    ContractAdjustPriceAddItemDTO adjustPriceAddItemDTO = new ContractAdjustPriceAddItemDTO();
                    BeanUtils.copyProperties(oldAddItemDTO, adjustPriceAddItemDTO);
                    adjustPriceAddItemDTO.setOldAdditemPrice(oldPrice);
                    adjustPriceAddItemDTO.setNewAdditemPrice(addItemDTO.getAdditemPrice());
                    addItemList.add(adjustPriceAddItemDTO);
                }
            }
        }
    }

    private void addGoodsList(List<TrContractGoodsDTO> goodsDTOList, TrContractDTO oldContractDTO, List<ContractAdjustPriceGoodsDTO> goodsList) {
        if (!CollectionUtils.isEmpty(goodsDTOList)) {
            List<TrContractGoodsDTO> oldGoodsList = oldContractDTO.getContractGoodsDTOS();
            if (!CollectionUtils.isEmpty(oldGoodsList)) {
                for (TrContractGoodsDTO goodsDTO : goodsDTOList) {
                    Optional<TrContractGoodsDTO> oldGoodsDTOOptional = oldGoodsList.stream().filter(item -> item.getContractGoodsId().equals(goodsDTO.getContractGoodsId())).findFirst();
                    if (oldGoodsDTOOptional.isEmpty()) {
                        log.info(" >> 原商品不存在 ContractGoodsId: {}", goodsDTO.getContractGoodsId());
                        continue;
                    }
                    TrContractGoodsDTO oldGoodsDTO = oldGoodsDTOOptional.get();
                    BigDecimal oldFactoryPrice = checkNumberAsZero(oldGoodsDTO.getOutFactoryPrice());
                    BigDecimal oldShipPrice = checkNumberAsZero(oldGoodsDTO.getShipPrice());
                    BigDecimal oldPlanGoodsAmount = checkNumberAsZero(oldGoodsDTO.getPlanGoodsAmount());
                    if (oldFactoryPrice.compareTo(goodsDTO.getOutFactoryPrice()) == 0 &&
                            oldShipPrice.compareTo(goodsDTO.getShipPrice()) == 0 &&
                            oldPlanGoodsAmount.compareTo(goodsDTO.getPlanGoodsAmount()) == 0) {
                        log.info(" >> 价格&数量没变化continue, ContractGoodsId: {}", goodsDTO.getContractGoodsId());
                        continue;
                    }

                    ContractAdjustPriceGoodsDTO adjustPriceGoodsDTO = new ContractAdjustPriceGoodsDTO();
                    BeanUtils.copyProperties(oldGoodsDTO,adjustPriceGoodsDTO);
                    adjustPriceGoodsDTO.setStartShipPrice(oldShipPrice);
                    adjustPriceGoodsDTO.setStartOutFactoryPrice(oldFactoryPrice);
                    adjustPriceGoodsDTO.setStartPlanGoodsAmount(oldPlanGoodsAmount);
                    adjustPriceGoodsDTO.setEndShipPrice(goodsDTO.getShipPrice());
                    adjustPriceGoodsDTO.setEndOutFactoryPrice(goodsDTO.getOutFactoryPrice());
                    adjustPriceGoodsDTO.setEndPlanGoodsAmount(goodsDTO.getPlanGoodsAmount());
                    adjustPriceGoodsDTO.setTransportType(goodsDTO.getTransportType());
                    goodsList.add(adjustPriceGoodsDTO);
                }
            }
        }
    }

    private static int getExecuteStatus(Boolean isImmediately) {
        int executeStatus;
        if (Boolean.TRUE.equals(isImmediately)) {
            executeStatus = ContractAdjustStatusEnum.EXECUTED.getCode();
        } else {
            executeStatus = ContractAdjustStatusEnum.UN_EXECUTE.getCode();
        }
        return executeStatus;
    }

    private static void checkParams(Boolean isImmediately, Date time) {
        if (Boolean.FALSE.equals(isImmediately) && time == null) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "定时生效未指定生效时间");
        }
        if (Boolean.FALSE.equals(isImmediately) && new Date().compareTo(time) >= 0) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "错误的生效时间");
        }
    }

    /**
     * 获取合同变化信息
     * @param contract 老版本合同
     * @param goodsList 新合同商品
     * @param additemList 新合同加价项
     * @param operator 操作人
     * @param updateTime 更新时间
     * @return priceIsChange true(合同商品价格已变化),numIsChange true(合同商品数量已变化) logList (List<ContractBatchAdjustPriceResult>)变化日志
     */
    private JSONObject getChangeInfo(TrContractDTO contract, List<TrContractGoodsDTO> goodsList, List<TrContractAdditemDTO> additemList, String operator, Date updateTime, String remark){
        JSONObject result = new JSONObject();
        result.put("priceIsChange",false);
        result.put("numIsChange",false);
        List<ContractBatchAdjustPriceResult> logList = Lists.newArrayList();
        result.put("logList",logList);
        List<TrContractGoodsDTO> oldGoodsList = contract.getContractGoodsDTOS();
        List<TrContractAdditemDTO> oldAdditemDTOS = contract.getContractAdditemDTOS();
        //如果合同商品价格有变化
        addLogList(contract, goodsList, operator, updateTime, remark, oldGoodsList, result, logList);
        //判断是否加价项价格有变化，仅记录日志，不用于返回priceIsChange标记
        updateLogList(contract, additemList, operator, updateTime, remark, oldAdditemDTOS, logList);
        return result;
    }

    private void updateLogList(TrContractDTO contract, List<TrContractAdditemDTO> additemList, String operator, Date updateTime, String remark, List<TrContractAdditemDTO> oldAdditemDTOS, List<ContractBatchAdjustPriceResult> logList) {
        if( org.apache.commons.collections.CollectionUtils.isNotEmpty(oldAdditemDTOS)){
            Map<String, TrContractAdditemDTO> oldAdditemDTOMap = oldAdditemDTOS.stream().filter(item -> CsStringUtils.isNotBlank(item.getContractAdditemId())).collect(Collectors.toMap(TrContractAdditemDTO::getContractAdditemId, Function.identity()));
            for (TrContractAdditemDTO addItemDTO : additemList) {
                TrContractAdditemDTO oldAdditemDTO = oldAdditemDTOMap.get(addItemDTO.getContractAdditemId());
                if(oldAdditemDTO == null ){
                    continue;
                }
                //组装执行结果
                ContractBatchAdjustPriceResult exeResultItem = new ContractBatchAdjustPriceResult();
                exeResultItem.setResultId(uuidGenerator.gain());
                exeResultItem.setDelFlg(false);
                exeResultItem.setCreateUser(operator);
                exeResultItem.setCreateTime(updateTime);
                exeResultItem.setBatchId(null);
                exeResultItem.setSellerId(contract.getSellerId());
                exeResultItem.setBuyerId(contract.getBuyerId());
                exeResultItem.setBuyerName(contract.getBuyerName());
                exeResultItem.setContractId(contract.getContractId());
                exeResultItem.setContractNumber(contract.getContractNumber());
                exeResultItem.setContractStatus(contract.getContractStatus());
                exeResultItem.setPriceUpdateTime(updateTime);
                exeResultItem.setStartDate(contract.getStartDate());
                exeResultItem.setContractEnd(contract.getContractEnd());
                exeResultItem.setProjectName(contract.getProjectName());

                exeResultItem.setContractGoodsId(addItemDTO.getContractAdditemId());

                exeResultItem.setAdjustPriceTemplateId(CsStringUtils.isBlank(contract.getAdjustPriceTemplateId()) ? null : contract.getAdjustPriceTemplateId());
                exeResultItem.setGoodsId(oldAdditemDTO.getAdditemId());
                exeResultItem.setGoodsName(oldAdditemDTO.getAdditemName());
                //修改前
                exeResultItem.setArrivePrice(oldAdditemDTO.getAdditemPrice());
                //修改后
                exeResultItem.setArrivePriceResult(addItemDTO.getAdditemPrice());
                exeResultItem.setNewFlg(false);
                exeResultItem.setRemark(remark);
                logList.add(exeResultItem);
            }
        }
    }

    private void addLogList(TrContractDTO contract, List<TrContractGoodsDTO> goodsList, String operator, Date updateTime, String remark, List<TrContractGoodsDTO> oldGoodsList, JSONObject result, List<ContractBatchAdjustPriceResult> logList) {
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(oldGoodsList)) {
            Map<String, TrContractGoodsDTO> goodsDTOMap = oldGoodsList.stream().filter(item -> CsStringUtils.isNotBlank(item.getContractGoodsId())).collect(Collectors.toMap(TrContractGoodsDTO::getContractGoodsId, Function.identity()));
            addLogLists(contract, goodsList, operator, updateTime, remark, result, logList, goodsDTOMap);
        }
    }

    private void addLogLists(TrContractDTO contract, List<TrContractGoodsDTO> goodsList, String operator, Date updateTime, String remark, JSONObject result, List<ContractBatchAdjustPriceResult> logList, Map<String, TrContractGoodsDTO> goodsDTOMap) {
        for (TrContractGoodsDTO goods : goodsList) {
            TrContractGoodsDTO oldGoodsDTO = goodsDTOMap.get(goods.getContractGoodsId());
            if (oldGoodsDTO == null) {
                log.info(" >> 原商品不存在 ContractGoodsId: {}", goods.getContractGoodsId());
                continue;
            }
            BigDecimal oldFactoryPrice = checkNumberAsZero(oldGoodsDTO.getOutFactoryPrice());
            BigDecimal oldShipPrice = checkNumberAsZero(oldGoodsDTO.getShipPrice());
            BigDecimal oldPlanGoodsAmount = checkNumberAsZero(oldGoodsDTO.getPlanGoodsAmount());
            //如果为true 则表示价格发生变化
            boolean priceChange = oldFactoryPrice.compareTo(goods.getOutFactoryPrice()) != 0 || oldShipPrice.compareTo(goods.getShipPrice()) != 0;
            //如果为true,则表示数量发生变化
            boolean numChange = oldPlanGoodsAmount.compareTo(goods.getPlanGoodsAmount()) != 0;
            if (!priceChange && !numChange) {
                log.info(" >> 价格没变化continue, ContractGoodsId: {}", goods.getContractGoodsId());
                continue;
            }
            resultPutValue(result, priceChange, numChange);
            //组装执行结果
            ContractBatchAdjustPriceResult exeResultItem = new ContractBatchAdjustPriceResult();
            exeResultItem.setResultId(uuidGenerator.gain());
            exeResultItem.setDelFlg(false);
            exeResultItem.setCreateUser(operator);
            exeResultItem.setCreateTime(updateTime);
            exeResultItem.setBatchId(null);
            exeResultItem.setSellerId(contract.getSellerId());
            exeResultItem.setBuyerId(contract.getBuyerId());
            exeResultItem.setBuyerName(contract.getBuyerName());
            exeResultItem.setContractId(contract.getContractId());
            exeResultItem.setContractNumber(contract.getContractNumber());
            exeResultItem.setContractStatus(contract.getContractStatus());
            exeResultItem.setPriceUpdateTime(updateTime);
            exeResultItem.setStartDate(contract.getStartDate());
            exeResultItem.setContractEnd(contract.getContractEnd());
            exeResultItem.setProjectName(contract.getProjectName());
            exeResultItem.setContractGoodsId(goods.getContractGoodsId());
            exeResultItem.setAdjustPriceTemplateId(CsStringUtils.isBlank(contract.getAdjustPriceTemplateId()) ? null : contract.getAdjustPriceTemplateId());

            exeResultItem.setSaleRegionId(oldGoodsDTO.getSaleAreaRealCode());
            exeResultItem.setSaleRegionName(oldGoodsDTO.getSaleAreaName());
            exeResultItem.setTransportType(oldGoodsDTO.getTransportType());
            exeResultItem.setGoodsId(oldGoodsDTO.getGoodsId());
            exeResultItem.setGoodsName(oldGoodsDTO.getGoodsName());
            exeResultItem.setOutGoodsAddress(oldGoodsDTO.getOutGoodsAddress());
            exeResultItem.setOutGoodsAddressId(oldGoodsDTO.getOutGoodsAddressId());
            //修改前
            exeResultItem.setArrivePrice(oldGoodsDTO.getShipPrice());
            exeResultItem.setFactoryPrice(oldGoodsDTO.getOutFactoryPrice());
            //修改后
            exeResultItem.setArrivePriceResult(goods.getShipPrice());
            exeResultItem.setFactoryPriceResult(goods.getOutFactoryPrice());
            //数量无变化
            exeResultItem.setPlanGoodsAmount(oldGoodsDTO.getPlanGoodsAmount());
            exeResultItem.setPlanGoodsAmountResult(oldGoodsDTO.getPlanGoodsAmount());

            exeResultItem.setNewFlg(false);
            exeResultItem.setRemark(remark);
            logList.add(exeResultItem);
        }
    }

    private static void resultPutValue(JSONObject result, boolean priceChange, boolean numChange) {
        if (priceChange) {
            result.put("priceIsChange", true);
        }
        if (numChange) {
            result.put("numIsChange", true);
        }
    }


    @Override
    public Boolean revokeContractAdjustPrice(String contractAdjustPriceId, String operatorId) {
        log.info(" == >>> 撤销调价  <<< == ");
        if (CsStringUtils.isEmpty(contractAdjustPriceId)) {
            throw new BizException(BasicCode.PARAM_NULL);
        }
        ContractAdjustPrice price = iContractAdjustPriceBiz.get(contractAdjustPriceId);
        if (ContractAdjustStatusEnum.EXECUTED.getCode().equals(price.getExecuteStatus())) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "当前调价已经执行, 无法撤销");
        }
        if (ContractAdjustStatusEnum.REVOKE.getCode().equals(price.getExecuteStatus())) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "当前调价已撤销");
        }
        price.setExecuteStatus(ContractAdjustStatusEnum.REVOKE.getCode());
        iContractAdjustPriceBiz.updateSelective(price);
        return true;
    }

    @Override
    public Boolean updateContractAdjustPrice(ContractAdjustPriceDTO contractAdjustPriceDTO, String operatorId) {
        return iContractAdjustPriceBiz.updateContractAdjustPrice(contractAdjustPriceDTO, operatorId);
    }

    @Override
    public ContractAdjustPriceDTO getContractAdjustPriceByBuyer(String contractAdjustPriceId, String buyerId) {
        ContractAdjustPriceDTO adjustPriceDTO = iContractAdjustPriceBiz.getContractAdjustPriceByBuyer(contractAdjustPriceId, buyerId);
        return resolveContent2html(adjustPriceDTO);
    }

    @Override
    public ContractAdjustPriceDTO getContractAdjustPriceBySeller(String contractAdjustPriceId, String sellerId) {
        ContractAdjustPriceDTO adjustPriceDTO = iContractAdjustPriceBiz.getContractAdjustPriceBySeller(contractAdjustPriceId, sellerId);
        return resolveContent2html(adjustPriceDTO);
    }

    @Override
    public PageInfo<ContractAdjustPriceDTO> pageContractAdjustPrice(ContractAdjustPriceDTO contractAdjustPriceDTO) {
        Page<ContractAdjustPriceDTO> page = iContractAdjustPriceBiz.pageContractAdjustPrice(contractAdjustPriceDTO);
        return new PageInfo<>(page);
    }

    @Override
    public PageInfo<ContractAdjustPriceDTO> pageContractAdjustPriceDetail(ContractAdjustPriceDTO contractAdjustPriceDTO) {
        Page<ContractAdjustPriceDTO> page = iContractAdjustPriceBiz.pageContractAdjustPriceDetail(contractAdjustPriceDTO);
        return new PageInfo<>(page);
    }

    @Override
    public Boolean sendContractAdjustPrice(ContractAdjustPriceDTO trContractAdjustPriceDTO) {
        return iContractAdjustPriceBiz.sendContractAdjustPrice(trContractAdjustPriceDTO);
    }

    @Override
    public Boolean isExistenceRecord(String contractId) {
        if (CsStringUtils.isEmpty(contractId)) {
            return false;
        }
        TrContract contract = contractBiz.get(contractId);
        if (contract == null || contract.getDelFlg() == 1) {
            throw new BizException(BasicCode.DB_DATA_NOT_FOUND);
        }
        return isExistUnExecute(contract.getContractSequence(), contract.getContractNumber());
    }

    private boolean isExistUnExecute(String sequence, String number) {
        ContractAdjustPrice query = new ContractAdjustPrice();
        query.setContractSequence(sequence);
        query.setContractNumber(number);
        query.setExecuteStatus(ContractAdjustStatusEnum.UN_EXECUTE.getCode());
        query.setDelFlg(0);
        List<ContractAdjustPrice> unExecute = iContractAdjustPriceBiz.find(query);
        return !CollectionUtils.isEmpty(unExecute);
    }

    private BigDecimal checkNumberAsZero(BigDecimal num) {
        return num == null ? BigDecimal.ZERO : num;
    }

    private void findAddItemParentNode(List<ContractAdjustPriceAddItemDTO> addItemList, List<TrContractAdditemDTO> oldContractAddItems, String parentId) {
        //查找父节点
        for (ContractAdjustPriceAddItemDTO addItemDTO : addItemList) {
            if (addItemDTO.getAdditemId().equals(parentId)) {
                return;
            }
        }
        // 没有父节点 添加
        ContractAdjustPriceAddItemDTO contractAdjustPriceAddItemDTO1 = new ContractAdjustPriceAddItemDTO();
        for (TrContractAdditemDTO trContractAdditem1 : oldContractAddItems) {
            if (trContractAdditem1.getAdditemId().equals(parentId)) {
                BeanUtils.copyProperties(trContractAdditem1, contractAdjustPriceAddItemDTO1);
                addItemList.add(contractAdjustPriceAddItemDTO1);
            }
        }
    }

    private ContractAdjustPriceDTO resolveContent2html(ContractAdjustPriceDTO adjustPriceDTO) {
        if (adjustPriceDTO == null) {
            return null;
        }
        adjustPriceDTO.setHtmlContent("未选择模板");
        if (CsStringUtils.isEmpty(adjustPriceDTO.getContractAdjustPriceTemplateId())) {
            return adjustPriceDTO;
        }
        TrContractTemplateDTO templateDTO = contractTemplateBiz.getContractTemplateDetail(adjustPriceDTO.getContractAdjustPriceTemplateId());
        if (templateDTO == null) {
            return adjustPriceDTO;
        }
        AttachmentFileDTO fileDTO = attachmentService.downLoadFileByUrl(templateDTO.getTemplateUrl());
        if (fileDTO == null || fileDTO.getFile().length == 0) {
            adjustPriceDTO.setHtmlContent("模板文件不存在");
            return adjustPriceDTO;
        }

        log.info(" >> fileDTO -> fileName:{}, len: {}", fileDTO.getFileName(), fileDTO.getFileSize());
        ByteArrayInputStream input = new ByteArrayInputStream(fileDTO.getFile());

        try {
            byte[] bytes = contractTemplateBiz.downloadFileByAdjustPrice(adjustPriceDTO, input);
            String html = DocTemplateUtils.doc2Html(new ByteArrayInputStream(bytes));
            adjustPriceDTO.setHtmlContent(html);
        } catch (Exception e) {
            adjustPriceDTO.setHtmlContent("模板文件解析失败");
        }
        return adjustPriceDTO;
    }
}
