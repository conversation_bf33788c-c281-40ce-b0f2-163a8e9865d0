package com.ecommerce.goods.service.impl;

import com.alibaba.fastjson.JSON;
import com.ecommerce.base.api.dto.saleregion.SaleRegionSampleDTO;
import com.ecommerce.base.api.dto.warehouse.WarehouseListDTO;
import com.ecommerce.base.api.service.ISaleRegionService;
import com.ecommerce.base.api.service.IWarehouseService;
import com.ecommerce.common.annotation.RedisLock;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.exception.CodeMeta;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.service.common.SpringContextHolder;
import com.ecommerce.common.utils.BeanConvertUtils;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.goods.api.dto.base.PageQuery;
import com.ecommerce.goods.api.dto.contract.ContractAdjustPriceDTO;
import com.ecommerce.goods.api.dto.contract.ContractPriceChangeHistoryQueryDTO;
import com.ecommerce.goods.api.dto.contract.batch.adjust.price.BatchAdjustPriceContractQueryContractGoodsInfoDTO;
import com.ecommerce.goods.api.dto.contract.batch.adjust.price.BatchAdjustPriceContractQueryContractInfoDTO;
import com.ecommerce.goods.api.dto.contract.batch.adjust.price.BatchAdjustPriceContractQueryDTO;
import com.ecommerce.goods.api.dto.contract.batch.adjust.price.ContractBatchAdjustPriceConfirmDTO;
import com.ecommerce.goods.api.dto.contract.batch.adjust.price.ContractBatchAdjustPriceCreateDTO;
import com.ecommerce.goods.api.dto.contract.batch.adjust.price.ContractBatchAdjustPriceDTO;
import com.ecommerce.goods.api.dto.contract.batch.adjust.price.ContractBatchAdjustPriceDeleteDTO;
import com.ecommerce.goods.api.dto.contract.batch.adjust.price.ContractBatchAdjustPriceListDTO;
import com.ecommerce.goods.api.dto.contract.batch.adjust.price.ContractBatchAdjustPriceListQueryDTO;
import com.ecommerce.goods.api.dto.contract.batch.adjust.price.ContractBatchAdjustPriceSendLetterDTO;
import com.ecommerce.goods.api.dto.contract.batch.adjust.price.ContractBatchAdjustPriceUpdateDTO;
import com.ecommerce.goods.api.dto.contract.batch.adjust.price.ContractBatchAdjustPriceUpdateHistoryDTO;
import com.ecommerce.goods.api.dto.contract.batch.adjust.price.GoodsPriceLimitDTO;
import com.ecommerce.goods.api.dto.contract.batch.adjust.price.IdAndName;
import com.ecommerce.goods.api.enums.ExternalMethodTypeEnum;
import com.ecommerce.goods.api.enums.contract.ContractAdjustStatusEnum;
import com.ecommerce.goods.biz.IContractAdjustPriceBiz;
import com.ecommerce.goods.biz.IContractBatchAdjustPriceBiz;
import com.ecommerce.goods.biz.IContractBatchAdjustPriceOpLogBiz;
import com.ecommerce.goods.biz.IContractBatchAdjustPriceResultBiz;
import com.ecommerce.goods.biz.IContractBiz;
import com.ecommerce.goods.biz.IExternalExceptionBizService;
import com.ecommerce.goods.biz.IGoodsAttributeValueBiz;
import com.ecommerce.goods.biz.IGoodsBiz;
import com.ecommerce.goods.biz.IGoodsCategoryAttrBiz;
import com.ecommerce.goods.dao.vo.ContractBatchAdjustPrice;
import com.ecommerce.goods.dao.vo.Goods;
import com.ecommerce.goods.dao.vo.GoodsAttributeValue;
import com.ecommerce.goods.dao.vo.GoodsCategoryAttribute;
import com.ecommerce.goods.dao.vo.TrContract;
import com.ecommerce.goods.service.IContractBatchAdjustPriceService;
import com.ecommerce.logistics.api.enums.TransportToolTypeEnum;
import com.ecommerce.member.api.dto.account.AccountSimpleDTO;
import com.ecommerce.member.api.dto.member.MemberSimpleDTO;
import com.ecommerce.member.api.dto.member.config.MemberConfigDTO;
import com.ecommerce.member.api.dto.member.config.MemberIdsAndKeyCodesDTO;
import com.ecommerce.member.api.service.IAccountService;
import com.ecommerce.member.api.service.IMemberConfigService;
import com.ecommerce.member.api.service.IMemberService;
import com.ecommerce.open.api.dto.apicenter.ApiResult;
import com.ecommerce.open.api.dto.apicenter.member.ECModifyPriceDTO;
import com.ecommerce.open.api.dto.apicenter.member.ECModifyPriceItemDTO;
import com.ecommerce.open.api.service.apicenter.IOpenAPIInvokeService;
import com.ecommerce.open.enums.BizCodeEnum;
import com.ecommerce.order.api.dto.OrderSimpleDTO;
import com.ecommerce.order.api.dto.OrderSimpleQueryContractGoodsDTO;
import com.ecommerce.order.api.dto.OrderSimpleQueryDTO;
import com.ecommerce.order.api.enums.MemberOrderConfigEnum;
import com.ecommerce.order.api.service.IOrderService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Condition;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ecommerce.common.service.common.BaseBiz.setOperInfo;

/**
 *合同批量调价服务
 */
@Slf4j
@Service
public class ContractBatchAdjustPriceService implements IContractBatchAdjustPriceService {

    @Autowired
    private IAccountService accountService;
    @Autowired
    private IMemberService memberService;
    @Autowired
    private IMemberConfigService memberConfigService;
    @Autowired
    private IWarehouseService warehouseService;
    @Autowired
    private ISaleRegionService saleRegionService;
    @Autowired
    private IOrderService orderService;
    @Autowired
    private IContractBatchAdjustPriceBiz contractBatchAdjustPriceBiz;
    @Autowired
    private IContractBatchAdjustPriceResultBiz contractBatchAdjustPriceResultBiz;
    @Autowired
    private IContractBatchAdjustPriceOpLogBiz contractBatchAdjustPriceOpLogBiz;
    @Autowired
    private IContractAdjustPriceBiz contractAdjustPriceBiz;
    @Autowired
    private IGoodsBiz goodsBiz;
    @Autowired
    private IContractBiz contractBiz;
    @Autowired
    private IGoodsCategoryAttrBiz goodsCategoryAttrBiz;
    @Autowired
    private IGoodsAttributeValueBiz goodsAttributeValueBiz;
    @Autowired
    private TradeIdGenerator tradeIdGenerator;
    @Autowired
    private SMSMessageProducerImpl smsMessageProducer;
    @Autowired
    private ThreadPoolExecutor threadPoolExecutor;
    @Autowired
    private IOpenAPIInvokeService openAPIInvokeService;
    @Autowired
    private IExternalExceptionBizService externalExceptionBizService;
    private volatile ContractBatchAdjustPriceService thisProxy;

    @Override
    public Boolean create(ContractBatchAdjustPriceCreateDTO dto) {
        if( dto.getFixUptime().before(new Date()) ){
            throw new BizException(BasicCode.CUSTOM_ERROR,"调价执行时间不能早于当前时间");
        }
        if( !TransportToolTypeEnum.WATER_TRANSPORT.getCode().equals(dto.getTransportType()) && !TransportToolTypeEnum.ROAD_TRANSPORT.getCode().equals(dto.getTransportType()) ){
            log.info("contractBatchAdjustPriceCreateDTO:{}",dto);
            throw new BizException(BasicCode.CUSTOM_ERROR,"运输方式不可为空或不正确");
        }
        ContractBatchAdjustPrice contractBatchAdjustPrice = new ContractBatchAdjustPrice();
        contractBatchAdjustPrice.setSellerId(dto.getSellerId());
        contractBatchAdjustPrice.setBuyerIdsList(IdAndName.getIdList(dto.getBuyerIds()));
        contractBatchAdjustPrice.setSaleRegionIdsList(IdAndName.getIdList(dto.getSaleRegionIds()));
        contractBatchAdjustPrice.setGoodsIdsList(IdAndName.getIdList(dto.getGoodsIds()));
        contractBatchAdjustPrice.setStoreIdsList(IdAndName.getIdList(dto.getStoreIds()));
        contractBatchAdjustPrice.setExcludeContractIdsList(dto.getExcludeContractIds());
        contractBatchAdjustPrice.setTransportType(dto.getTransportType());
        contractBatchAdjustPrice.setFactoryPrice(dto.getFactoryPrice());
        contractBatchAdjustPrice.setArrivePrice(dto.getArrivePrice());
        contractBatchAdjustPrice.setIfup(true);
        contractBatchAdjustPrice.setFixUptime(dto.getFixUptime());
        contractBatchAdjustPrice.setCreateUser(dto.getCreateUser());
        contractBatchAdjustPrice.setUpdateUser(dto.getCreateUser());
        contractBatchAdjustPrice.setBatchCode(tradeIdGenerator.businessCodePrefix(""));
        contractBatchAdjustPrice.setStatus(ContractAdjustStatusEnum.DRAFT.getCode());
        contractBatchAdjustPrice.setSendFlg(false);
        log.info("contractBatchAdjustPriceBiz.save:{}",contractBatchAdjustPrice);
        contractBatchAdjustPriceBiz.save(contractBatchAdjustPrice,dto.getCreateUser());
        //如果是立即执行
        if(  dto.getFixUptime().getTime() <= System.currentTimeMillis() ) {
            contractBatchAdjustPriceOpLogBiz.addLog(null,contractBatchAdjustPrice,"新建调价方案");
            threadPoolExecutor.execute(() -> {
                try {
                    batchAdjustPrice();
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
            });
        }else{
            contractBatchAdjustPriceOpLogBiz.addLogSync(null,contractBatchAdjustPrice,"新建调价方案");
        }
        return true;
    }

    @Override
    public Boolean delete(ContractBatchAdjustPriceDeleteDTO dto) {
        ContractBatchAdjustPrice contractBatchAdjustPrice = contractBatchAdjustPriceBiz.get(dto.getBatchId());
        if( contractBatchAdjustPrice == null || BooleanUtils.isTrue(contractBatchAdjustPrice.getDelFlg())){
            throw new BizException(BasicCode.CUSTOM_ERROR,"调价方案不存在或已删除");
        }
        if (!CsStringUtils.equals(dto.getSellerId(), contractBatchAdjustPrice.getSellerId())) {
            throw new BizException(BasicCode.CUSTOM_ERROR,"没有操作权限");
        }
        //如果是30秒内要执行的，则不可删除 先锁定，马上就要执行了，不能再改了
        if(ContractAdjustStatusEnum.UN_EXECUTE.getCode().equals(contractBatchAdjustPrice.getStatus()) &&
                Math.abs(contractBatchAdjustPrice.getFixUptime().getTime() - System.currentTimeMillis()) < 30000L){
            throw new BizException(BasicCode.CUSTOM_ERROR,"马上就要执行的不可删除");
        }
        contractBatchAdjustPrice.setDelFlg(true);
        contractBatchAdjustPrice.setUpdateTime(new Date());
        contractBatchAdjustPrice.setUpdateUser(dto.getUpdateUser());
        contractBatchAdjustPriceBiz.updateSelective(contractBatchAdjustPrice);
        contractBatchAdjustPriceOpLogBiz.addLogSync(contractBatchAdjustPrice,contractBatchAdjustPrice,"删除调价方案");
        return true;
    }

    @Override
    public Boolean update(ContractBatchAdjustPriceUpdateDTO dto) {
        ContractBatchAdjustPrice contractBatchAdjustPrice1 = getContractBatchAdjustPrice(dto);
        ContractBatchAdjustPrice old = BeanConvertUtils.convert(contractBatchAdjustPrice1,ContractBatchAdjustPrice.class);

        contractBatchAdjustPrice1.setIfup(true);
        contractBatchAdjustPrice1.setFixUptime(dto.getFixUptime());
        contractBatchAdjustPrice1.setStatus(ContractAdjustStatusEnum.UN_EXECUTE.getCode());

        contractBatchAdjustPrice1.setUpdateUser(dto.getUpdateUser());
        contractBatchAdjustPrice1.setUpdateTime(new Date());

        contractBatchAdjustPrice1.setFactoryPrice(dto.getFactoryPrice());
        contractBatchAdjustPrice1.setArrivePrice(dto.getArrivePrice());

        contractBatchAdjustPrice1.setBuyerIdsList(IdAndName.getIdList(dto.getBuyerIds()));
        contractBatchAdjustPrice1.setGoodsIdsList(IdAndName.getIdList(dto.getGoodsIds()));
        contractBatchAdjustPrice1.setSaleRegionIdsList(IdAndName.getIdList(dto.getSaleRegionIds()));
        contractBatchAdjustPrice1.setStoreIdsList(IdAndName.getIdList(dto.getStoreIds()));
        contractBatchAdjustPrice1.setExcludeContractIdsList(dto.getExcludeContractIds());
        contractBatchAdjustPrice1.setTransportType(dto.getTransportType());

        contractBatchAdjustPriceBiz.update(contractBatchAdjustPrice1);

        //如果是立即执行
        if(  dto.getFixUptime().getTime() <= System.currentTimeMillis() ) {
            contractBatchAdjustPriceOpLogBiz.addLog(old,contractBatchAdjustPrice1,"修改调价方案");
            threadPoolExecutor.execute(() -> {
                try {
                    batchAdjustPrice();
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
            });
        }else{
            contractBatchAdjustPriceOpLogBiz.addLogSync(old,contractBatchAdjustPrice1,"修改调价方案");
        }
        return true;
    }

    @NotNull
    private ContractBatchAdjustPrice getContractBatchAdjustPrice(ContractBatchAdjustPriceUpdateDTO dto) {
        if (CsStringUtils.isBlank(dto.getBatchId())) {
            throw new BizException(BasicCode.CUSTOM_ERROR,"batchId不可为空");
        }
        if( dto.getFactoryPrice() == null && dto.getArrivePrice() == null ){
            throw new BizException(BasicCode.CUSTOM_ERROR,"要调整的出厂价和到位价不可同时为空");
        }
        if( !TransportToolTypeEnum.WATER_TRANSPORT.getCode().equals(dto.getTransportType()) && !TransportToolTypeEnum.ROAD_TRANSPORT.getCode().equals(dto.getTransportType()) ){
            log.info("contractBatchAdjustPriceUpdateDTO:{}", dto);
            throw new BizException(BasicCode.CUSTOM_ERROR,"运输方式不可为空或不正确");
        }
        ContractBatchAdjustPrice contractBatchAdjustPrice1 = contractBatchAdjustPriceBiz.get(dto.getBatchId());
        if(contractBatchAdjustPrice1 == null || BooleanUtils.isTrue(contractBatchAdjustPrice1.getDelFlg() )){
            throw new BizException(BasicCode.CUSTOM_ERROR,"batchId不正确");
        }
        //sellerId必须相同 此操作为卖家操作
        if (!CsStringUtils.equals(contractBatchAdjustPrice1.getSellerId(), dto.getSellerId())) {
            throw new BizException(BasicCode.CUSTOM_ERROR,"batchId不正确");
        }
        if(Objects.equals(contractBatchAdjustPrice1.getStatus(), ContractAdjustStatusEnum.EXECUTED.getCode()) ||
                Objects.equals(contractBatchAdjustPrice1.getStatus(), ContractAdjustStatusEnum.EXECUTE_DOING.getCode())){
            throw new BizException(BasicCode.CUSTOM_ERROR,"执行中或执行过的调价方案不可修改");
        }
        //如果是30秒内要执行的，则不可删除 先锁定，马上就要执行了，不能再改了
        if(Objects.equals(contractBatchAdjustPrice1.getStatus(), ContractAdjustStatusEnum.UN_EXECUTE.getCode()) &&
                Math.abs(contractBatchAdjustPrice1.getFixUptime().getTime() - System.currentTimeMillis()) < 30000L){
            throw new BizException(BasicCode.CUSTOM_ERROR,"马上就要执行的不可修改");
        }
        return contractBatchAdjustPrice1;
    }

    /**
     * 批量调价-根据id查询详情
     */
    @Override
    public ContractBatchAdjustPriceDTO findByMemberIdAndBatchId(String sellerId, String batchId) {
        ContractBatchAdjustPrice contractBatchAdjustPrice = contractBatchAdjustPriceBiz.findByBatchIdOrBatchCode(sellerId,batchId);
        if (Objects.isNull(contractBatchAdjustPrice)) {
            return null;
        }
        ContractBatchAdjustPriceDTO dto = convert(contractBatchAdjustPrice);
        if(!Objects.equals(contractBatchAdjustPrice.getStatus(), ContractAdjustStatusEnum.EXECUTED.getCode())){
            updateGoodsPriceLimitInfoList(dto);
        }
        return dto;
    }

    /**
     * 批量调价-发送调价函
     */
    @Override
    public Boolean sendLetter(ContractBatchAdjustPriceSendLetterDTO dto) {
        ContractBatchAdjustPrice contractBatchAdjustPrice = contractBatchAdjustPriceBiz.get(dto.getBatchId());
        if( contractBatchAdjustPrice == null || BooleanUtils.isTrue(contractBatchAdjustPrice.getDelFlg()) ||
                !CsStringUtils.equals(dto.getSellerId(), contractBatchAdjustPrice.getSellerId())) {
            log.error("调价方案不存在，或者卖家id不一致,batchId:{},sellerId:{},operator:{}",dto.getBatchId(),dto.getSellerId(),dto.getOperator());
            return false;
        }
        if( BooleanUtils.isTrue(contractBatchAdjustPrice.getSendFlg())){
            log.info("已经发送过调价方案了,batchId:{}",dto.getBatchId());
            return false;
        }
        if(ContractAdjustStatusEnum.EXECUTED.getCode().equals(contractBatchAdjustPrice.getStatus())){
            //根据调价执行结果
            contractBatchAdjustPriceResultBiz.sendLetter(contractBatchAdjustPrice,dto.getOperator());
        }else{
            contractBatchAdjustPriceResultBiz.sendLetter(contractBatchAdjustPrice,contractBiz.getContractList(contractBatchAdjustPrice,true),dto.getOperator());
        }
        ContractBatchAdjustPrice contractBatchAdjustPriceUpdate = new ContractBatchAdjustPrice();
        contractBatchAdjustPriceUpdate.setBatchId(contractBatchAdjustPrice.getBatchId());
        //更新状态为已发送
        contractBatchAdjustPriceUpdate.setSendFlg(true);
        contractBatchAdjustPriceBiz.updateSelective(contractBatchAdjustPriceUpdate);
        contractBatchAdjustPriceOpLogBiz.addLog(dto.getBatchId(),"发送调价函","发送调价函",dto.getOperator());
        return true;
    }

    /**
     * 批量调价-列表查询
     */
    @Override
    public PageInfo<ContractBatchAdjustPriceListDTO> findAll(PageQuery<ContractBatchAdjustPriceListQueryDTO> query) {
        Page<ContractBatchAdjustPrice> page = contractBatchAdjustPriceBiz.findAll(query);
        PageInfo<ContractBatchAdjustPriceListDTO> pageinfo = new PageInfo<>();
        BeanUtils.copyProperties(page, pageinfo);
        pageinfo.setList(Lists.newArrayList());

        Set<String> goodIds = Sets.newHashSet();
        Set<String> saleRegionIds = Sets.newHashSet();
        Set<String> buyerIds = Sets.newHashSet();
        Set<String> storeIds = Sets.newHashSet();
        page.forEach(item->{
            goodIds.addAll(item.getGoodsIdsList());
            saleRegionIds.addAll(item.getSaleRegionIdsList());
            buyerIds.addAll(item.getBuyerIdsList());
            storeIds.addAll(item.getStoreIdsList());
        });

        List<IdAndName> goodsList = goodsBiz.selectByIds(goodIds).stream()
                .map(item -> new IdAndName(item.getGoodsId(), item.getGoodsName())).toList();
        List<IdAndName> buyerList = Lists.newArrayList();
        List<IdAndName> warehouseList = Lists.newArrayList();
        Map<String, SaleRegionSampleDTO> saleRegionMap = Maps.newHashMap();
        //准备数据
        List<MemberSimpleDTO> memberSimpleDTOList = memberService.findMemberSimpleByIds(Lists.newArrayList(buyerIds));
        if( CollectionUtils.isNotEmpty(memberSimpleDTOList)) {
            buyerList = memberSimpleDTOList.stream().map(item -> new IdAndName(item.getMemberId(), item.getMemberName())).toList();
        }

        List<WarehouseListDTO> warehouseListDTOList = warehouseService.queryWarehouseListByIds(Lists.newArrayList(storeIds));
        if( CollectionUtils.isNotEmpty(warehouseListDTOList)){
            warehouseList = warehouseListDTOList.stream().map(item -> new IdAndName(item.getWarehouseId(), item.getName())).toList();
        }

        List<SaleRegionSampleDTO> saleRegionList = saleRegionService.findAllSampleByMemberId(query.getQueryDTO().getSellerId());
        if( CollectionUtils.isNotEmpty(saleRegionList) ){
            saleRegionMap = saleRegionList.stream().collect(Collectors.toMap(SaleRegionSampleDTO::getSaleRegionId, Function.identity(),(k1,k2)->k1));
        }
        //dto批量转换,设置名称等信息
        for (ContractBatchAdjustPrice contractBatchAdjustPrice : page) {
            ContractBatchAdjustPriceListDTO dto = new ContractBatchAdjustPriceListDTO();
            dto.setBatchId(contractBatchAdjustPrice.getBatchId());
            dto.setBatchCode(contractBatchAdjustPrice.getBatchCode());
            dto.setFactoryPrice(contractBatchAdjustPrice.getFactoryPrice());
            dto.setArrivePrice(contractBatchAdjustPrice.getArrivePrice());
            dto.setIfup(contractBatchAdjustPrice.getIfup());
            dto.setFixUptime(contractBatchAdjustPrice.getFixUptime());
            dto.setStatus(contractBatchAdjustPrice.getStatus());
            dto.setSendFlg(contractBatchAdjustPrice.getSendFlg());
            dto.setExcludeContractIds(contractBatchAdjustPrice.getExcludeContractIdsList());
            dto.setTransportType(contractBatchAdjustPrice.getTransportType());

            if( CollectionUtils.isNotEmpty(contractBatchAdjustPrice.getGoodsIdsList())){
                dto.setGoodsIds(goodsList.stream().filter(item->contractBatchAdjustPrice.getGoodsIds().contains(item.getId())).toList());
            }
            if( CollectionUtils.isNotEmpty(contractBatchAdjustPrice.getBuyerIdsList())){
                    dto.setBuyerIds(buyerList.stream().filter(item->contractBatchAdjustPrice.getBuyerIds().contains(item.getId())).toList());
            }
            if( CollectionUtils.isNotEmpty(contractBatchAdjustPrice.getStoreIdsList())){
                dto.setStoreIds(warehouseList.stream().filter(item->contractBatchAdjustPrice.getStoreIds().contains(item.getId())).toList());
            }
            if( CollectionUtils.isNotEmpty(contractBatchAdjustPrice.getSaleRegionIdsList())){
                dto.setSaleRegionIds(getSaleRegionList(saleRegionMap,contractBatchAdjustPrice.getSaleRegionIdsList()));
            }
            pageinfo.getList().add(dto);
        }
        return pageinfo;
    }

    /**
     * 批量调价-根据条件筛选(新增或修改时)
     */
    @Override
    public ContractBatchAdjustPriceDTO findByBatchAdjustPriceCondition(BatchAdjustPriceContractQueryDTO query) {
        ContractBatchAdjustPrice contractBatchAdjustPrice = new ContractBatchAdjustPrice();
        contractBatchAdjustPrice.setSellerId(query.getSellerId());
        contractBatchAdjustPrice.setStatus(ContractAdjustStatusEnum.UN_EXECUTE.getCode());
        contractBatchAdjustPrice.setBuyerIdsList(IdAndName.getIdList(query.getBuyerIds()));
        contractBatchAdjustPrice.setSaleRegionIdsList(IdAndName.getIdList(query.getSaleRegionIds()));
        contractBatchAdjustPrice.setGoodsIdsList(IdAndName.getIdList(query.getGoodsIds()));
        contractBatchAdjustPrice.setStoreIdsList(IdAndName.getIdList(query.getStoreIds()));
        contractBatchAdjustPrice.setTransportType(query.getTransportType());

        ContractBatchAdjustPriceDTO dto = convert(contractBatchAdjustPrice);

        dto.setFactoryPrice(query.getFactoryPrice());
        dto.setArrivePrice(query.getArrivePrice());
        updateGoodsPriceLimitInfoList(dto);
        return dto;
    }

    /**
     * 商品的价格限制信息
     */
    private void updateGoodsPriceLimitInfoList(ContractBatchAdjustPriceDTO dto){
        //按商品维度 统计并获取商品的价格限制信息
        if(Objects.isNull(dto) || CollectionUtils.isEmpty(dto.getContractList()) ) {
            return;
        }
        dto.setGoodsPriceLimitInfoList(Lists.newArrayList());
        //获取到所有的商品，以及他们的最大最小出厂价和到位价
        Map<String,GoodsPriceLimitDTO> map = Maps.newHashMap();
        goodsPriceLimitDTOAddItem(dto, map);
        if( !map.isEmpty() ){
            //参考 GoodsBiz.findBaseGoodsAttribute
            List<GoodsCategoryAttribute> categoryAttributes = goodsCategoryAttrBiz.findByCategoryTypeAndSpu(null,false);
            if(CollectionUtils.isNotEmpty(categoryAttributes)){
                Set<String> attrIds = categoryAttributes.stream().map(item -> item.getGoodsAttriId()).collect(Collectors.toSet());
                List<GoodsAttributeValue> goodsAttributeValueList = goodsAttributeValueBiz.findByGoodsIdAndAttrId(map.keySet(), attrIds);
                //写死 0120	价格上限 0121	价格下限
                Map<String, List<GoodsAttributeValue>> goodsAttributeValueGroup = goodsAttributeValueList.stream()
                        .filter(item->"0120".equals(item.getGoodsAttriId()) || "0121".equals(item.getGoodsAttriId()))
                        .collect(Collectors.groupingBy(GoodsAttributeValue::getGoodsId));

                dealGoodsItemMap(dto, map, goodsAttributeValueGroup);
            }
        }
    }

    private static void dealGoodsItemMap(ContractBatchAdjustPriceDTO dto, Map<String, GoodsPriceLimitDTO> map, Map<String, List<GoodsAttributeValue>> goodsAttributeValueGroup) {
        map.forEach((goodsId, goodsPriceLimitDTO)->{
            GoodsAttributeValue maxValue = goodsAttributeValueGroup.getOrDefault(goodsId, Lists.newArrayList()).stream().filter(item -> "0120".equals(item.getGoodsAttriId())).findAny().orElse(null);
            GoodsAttributeValue minValue = goodsAttributeValueGroup.getOrDefault(goodsId, Lists.newArrayList()).stream().filter(item -> "0121".equals(item.getGoodsAttriId())).findAny().orElse(null);

            String attriValue = (maxValue != null) ? maxValue.getAttriValue() : null;
            BigDecimal upPrice = (attriValue == null || CsStringUtils.isBlank(attriValue)) ? BigDecimal.valueOf(Long.MAX_VALUE) : new BigDecimal(attriValue.trim());
            goodsPriceLimitDTO.setUpPrice(upPrice);
            String attriDownValue = (minValue != null) ? minValue.getAttriValue().trim() : null;
            BigDecimal downPrice = (attriDownValue == null || CsStringUtils.isBlank(attriDownValue)) ? BigDecimal.ZERO : new BigDecimal(attriDownValue);
            goodsPriceLimitDTO.setDownPrice(downPrice);
            dto.getGoodsPriceLimitInfoList().add(goodsPriceLimitDTO);
        });
    }

    private void goodsPriceLimitDTOAddItem(ContractBatchAdjustPriceDTO dto, Map<String, GoodsPriceLimitDTO> map) {
        for (BatchAdjustPriceContractQueryContractInfoDTO contractInfoDTO : dto.getContractList()) {
            if( !contractInfoDTO.getExcludeFlag() && CollectionUtils.isNotEmpty(contractInfoDTO.getGoodsList())) {
                for (BatchAdjustPriceContractQueryContractGoodsInfoDTO goodsInfoDTO : contractInfoDTO.getGoodsList()) {
                    String goodsId = goodsInfoDTO.getGoodsId();
                    if( !map.containsKey(goodsId)){
                        GoodsPriceLimitDTO goodsPriceLimitDTO = new GoodsPriceLimitDTO();
                        goodsPriceLimitDTO.setGoodsId(goodsId);
                        goodsPriceLimitDTO.setGoodsName(goodsInfoDTO.getGoodsName());
                        goodsPriceLimitDTO.setMaxArrivePrice(zeroAsNull(goodsInfoDTO.getArrivePrice()));
                        goodsPriceLimitDTO.setMinArrivePrice(zeroAsNull(goodsInfoDTO.getArrivePrice()));
                        goodsPriceLimitDTO.setMaxFactoryPrice(zeroAsNull(goodsInfoDTO.getFactoryPrice()));
                        goodsPriceLimitDTO.setMinFactoryPrice(zeroAsNull(goodsInfoDTO.getFactoryPrice()));
                        map.put(goodsId,goodsPriceLimitDTO);
                    }else{
                        //最小值：取大于0的最小值 最大值：取大于0的   0表示没有价格 所以不取
                        GoodsPriceLimitDTO goodsPriceLimitDTO2 = map.get(goodsId);
                        goodsPriceLimitDTO2.setMaxArrivePrice(getMax(goodsInfoDTO.getArrivePrice(),goodsPriceLimitDTO2.getMaxArrivePrice()));
                        goodsPriceLimitDTO2.setMinArrivePrice(getMin(goodsInfoDTO.getArrivePrice(),goodsPriceLimitDTO2.getMinArrivePrice()));
                        goodsPriceLimitDTO2.setMaxFactoryPrice(getMax(goodsInfoDTO.getFactoryPrice(),goodsPriceLimitDTO2.getMaxFactoryPrice()));
                        goodsPriceLimitDTO2.setMinFactoryPrice(getMin(goodsInfoDTO.getFactoryPrice(),goodsPriceLimitDTO2.getMinFactoryPrice()));
                    }
                }
            }
        }
    }

    private static BigDecimal zeroAsNull(BigDecimal a ){
        return a == null || a.compareTo(BigDecimal.ZERO) == 0 ? null : a;
    }
    private static BigDecimal getMax(BigDecimal a ,BigDecimal b){
        //最小值：取大于0的最小值 最大值：取大于0的   0表示没有价格 所以不取
        if( a == null && b == null ){
            return null;
        }
        if( a == null && b != null ){
            return b.compareTo(BigDecimal.ZERO) >0 ? b : null ;
        }
        if( a != null && b == null ){
            return a.compareTo(BigDecimal.ZERO) >0 ? a : null;
        }
        a = a.compareTo(b) >0 ? a : b;
        return a.compareTo(BigDecimal.ZERO) >0 ? a : null;
    }

    private BigDecimal getMin(BigDecimal a ,BigDecimal b){
        //最小值：取大于0的最小值 最大值：取大于0的   0表示没有价格 所以不取
        if (checkParamsIsNull(a, b)) return null;
        if (checkAisNullAndBisNotNull(a, b)) return b.compareTo(BigDecimal.ZERO) > 0 ? b : null;
        if(b == null){
            return a.compareTo(BigDecimal.ZERO) >0 ? a : null;
        }
        if(a.compareTo(BigDecimal.ZERO) < 0 && b.compareTo(BigDecimal.ZERO) < 0){
            return null;
        }
        if( a.compareTo(BigDecimal.ZERO) > 0 && b.compareTo(BigDecimal.ZERO) > 0 ){
            return a.compareTo(b) <0 ? a : b;
        }
        return a.compareTo(BigDecimal.ZERO) > 0 ? a : b;
    }

    private static boolean checkAisNullAndBisNotNull(BigDecimal a, BigDecimal b) {
        if( a == null && b != null ){
            return true;
        }
        return false;
    }

    private static boolean checkParamsIsNull(BigDecimal a, BigDecimal b) {
        if( a == null && b == null ){
            return true;
        }
        return false;
    }

    /**
     * 批量调价-操作记录查询
     */
    @Override
    public List<ContractBatchAdjustPriceUpdateHistoryDTO> findUpdateHistory(String batchId) {
        if (CsStringUtils.isBlank(batchId)) {
            return Lists.newArrayList();
        }
        //如果传入的是batchCode
        if (CsStringUtils.length(batchId) < 16) {
            ContractBatchAdjustPrice contractBatchAdjustPrice = contractBatchAdjustPriceBiz.findByBatchCode(batchId);
            if( contractBatchAdjustPrice == null ){
                return Lists.newArrayList();
            }
            batchId = contractBatchAdjustPrice.getBatchId();
        }
        return contractBatchAdjustPriceOpLogBiz.findByBatchId(batchId);
    }

    /**
     * 实体转DTO，同时查询各种id对应的名称，并查询对应的合同，标记排除的合同
     * @param contractBatchAdjustPrice 实体
     * @return DTO
     */
    private ContractBatchAdjustPriceDTO convert(ContractBatchAdjustPrice contractBatchAdjustPrice){
        if( contractBatchAdjustPrice == null ){
            return null;
        }
        ContractBatchAdjustPriceDTO dto = new ContractBatchAdjustPriceDTO();
        dto.setBatchId(contractBatchAdjustPrice.getBatchId());
        dto.setBatchCode(contractBatchAdjustPrice.getBatchCode());
        dto.setSellerId(contractBatchAdjustPrice.getSellerId());
        dto.setFactoryPrice(contractBatchAdjustPrice.getFactoryPrice());
        dto.setArrivePrice(contractBatchAdjustPrice.getArrivePrice());
        dto.setIfup(contractBatchAdjustPrice.getIfup());
        dto.setFixUptime(contractBatchAdjustPrice.getFixUptime());
        dto.setStatus(contractBatchAdjustPrice.getStatus());
        dto.setSendFlg(contractBatchAdjustPrice.getSendFlg());
        dto.setCreateTime(contractBatchAdjustPrice.getUpdateTime());
        dto.setCreateUser(contractBatchAdjustPrice.getCreateUser());
        dto.setUpdateTime(contractBatchAdjustPrice.getUpdateTime());
        dto.setUpdateUser(contractBatchAdjustPrice.getUpdateUser());
        dto.setExcludeContractIds(contractBatchAdjustPrice.getExcludeContractIdsList());
        dto.setTransportType(contractBatchAdjustPrice.getTransportType());

        if( CollectionUtils.isNotEmpty(contractBatchAdjustPrice.getGoodsIdsList())){
            //查询商品名称
            List<Goods> goodsList = goodsBiz.selectByIds(contractBatchAdjustPrice.getGoodsIdsList());
            List<IdAndName> goodsIdAndNameList = goodsList.stream().map(item -> new IdAndName(item.getGoodsId(), item.getGoodsName()))
                    .toList();
            dto.setGoodsIds(goodsIdAndNameList);
        }
        if( CollectionUtils.isNotEmpty(contractBatchAdjustPrice.getBuyerIdsList())){
            //查询买家名称
            List<MemberSimpleDTO> buyerList = memberService.findMemberSimpleByIds(contractBatchAdjustPrice.getBuyerIdsList());
            if( CollectionUtils.isNotEmpty(buyerList)) {
                dto.setBuyerIds(buyerList.stream().map(item -> new IdAndName(item.getMemberId(), item.getMemberName())).toList());
            }
        }
        if( CollectionUtils.isNotEmpty(contractBatchAdjustPrice.getStoreIdsList())){
            //查询仓库名称
            List<WarehouseListDTO> warehouseList = warehouseService.queryWarehouseListByIds(contractBatchAdjustPrice.getStoreIdsList());
            dto.setStoreIds(warehouseList.stream().map(item -> new IdAndName(item.getWarehouseId(), item.getName())).toList());
        }
        //查询销售区域，名称按层级往下显示 如 A大区->A中区->A小区
        List<SaleRegionSampleDTO> saleRegionList = saleRegionService.findAllSampleByMemberId(contractBatchAdjustPrice.getSellerId());
        Map<String, SaleRegionSampleDTO>  saleRegionMap = Maps.newHashMap();
        if(CollectionUtils.isNotEmpty(saleRegionList)) {
            saleRegionMap = saleRegionList.stream().collect(Collectors.toMap(SaleRegionSampleDTO::getSaleRegionId, Function.identity(), (k1, k2) -> k1));
        }
        if( CollectionUtils.isNotEmpty(contractBatchAdjustPrice.getSaleRegionIdsList())){
            dto.setSaleRegionIds(getSaleRegionList(saleRegionMap,contractBatchAdjustPrice.getSaleRegionIdsList()));
        }
        //如果是成功执行了的
        if( ContractAdjustStatusEnum.EXECUTED.getCode() == contractBatchAdjustPrice.getStatus().intValue() ){
            //查询执行结果返回
            dto.setContractList(contractBatchAdjustPriceResultBiz.findByBatchId2Dto(contractBatchAdjustPrice.getBatchId()));
        }else {
            //会根据条件筛选对应的合同，标记在excludeContractIds中的合同排除标记为true
            dto.setContractList(contractBiz.getContractList(contractBatchAdjustPrice,false));
        }
        return dto;
    }

    /**
     * 获取销售区域id、名称（如：一级销售区域名称>二级销售区域名称>三级销售区域名称）
     */
    private List<IdAndName> getSaleRegionList(Map<String, SaleRegionSampleDTO> saleRegionMap, Collection<String> saleRegionIds){
        List<IdAndName> result = Lists.newArrayList();
        for (String saleRegionId : saleRegionIds) {
            SaleRegionSampleDTO saleRegionSampleDTO = saleRegionMap.get(saleRegionId);
            if( saleRegionSampleDTO != null ){
                List<String> nameList = Lists.newArrayList(saleRegionSampleDTO.getSaleRegionName());
                getParentSaleRegion(nameList,saleRegionSampleDTO.getSaleRegionParentId(),saleRegionMap);
                nameList.sort(Comparator.reverseOrder());
                result.add(new IdAndName(saleRegionId, CsStringUtils.join(nameList, ">")));
            }
        }
        return result;
    }

    /**
     * 递归获取上级销售区域名称
     */
    private void getParentSaleRegion(List<String> nameList,String saleRegionId,Map<String, SaleRegionSampleDTO> resource){
        SaleRegionSampleDTO saleRegionSampleDTO = resource.get(saleRegionId);
        if( saleRegionSampleDTO != null ){
            nameList.add(saleRegionSampleDTO.getSaleRegionName());
            getParentSaleRegion(nameList,saleRegionSampleDTO.getSaleRegionParentId(),resource);
        }
    }

    /**
     *合同批量调价job执行的方法(实时)
     */
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    @Override
    public void batchAdjustPrice() {
        Condition condition = new Condition(ContractBatchAdjustPrice.class);
        condition.createCriteria()
                //未删除
                .andEqualTo("delFlg",false)
                //待执行
                .andEqualTo("status",ContractAdjustStatusEnum.UN_EXECUTE.getCode())
                //小于等于当前时间
                .andLessThanOrEqualTo("fixUptime",new Date());
        List<ContractBatchAdjustPrice> list = contractBatchAdjustPriceBiz.findByCondition(condition);
        if( CollectionUtils.isEmpty(list)){
            return;
        }
        for (ContractBatchAdjustPrice contractBatchAdjustPrice : list) {
            getCurrentProxy().batchItemAdjustPrice(contractBatchAdjustPrice);
        }
    }

    @RedisLock(fieldName = "batchId")
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.NOT_SUPPORTED)
    public void batchItemAdjustPrice(ContractBatchAdjustPrice contractBatchAdjustPrice){
        Date now = new Date();
        ContractBatchAdjustPrice update = new ContractBatchAdjustPrice();
        update.setBatchId(contractBatchAdjustPrice.getBatchId());
        update.setStatus(ContractAdjustStatusEnum.EXECUTE_DOING.getCode());
        String operator = CsStringUtils.isBlank(contractBatchAdjustPrice.getUpdateUser()) ? contractBatchAdjustPrice.getCreateUser() : contractBatchAdjustPrice.getUpdateUser();
        try{
            update.setUpdateUser(operator);
            update.setUpdateTime(now);
            contractBatchAdjustPriceBiz.updateSelective(update);
            //获取调价方案对应的合同
            List<BatchAdjustPriceContractQueryContractInfoDTO> contractList = contractBiz.getContractList(contractBatchAdjustPrice,true);
            //执行调价
            contractBatchAdjustPriceBiz.adjustPriceProcess(contractBatchAdjustPrice,contractList,now);
            //根据卖家参数看看是否需要推送 需要先查询卖家参数在过滤后再执行后续操作
            update.setStatus(ContractAdjustStatusEnum.EXECUTED.getCode());
            //更新调价函状态
            contractAdjustPriceBiz.updateStatusByBatchCode(contractBatchAdjustPrice.getBatchCode(),ContractAdjustStatusEnum.EXECUTED.getCode());
        }catch (Exception e){
            update.setStatus(ContractAdjustStatusEnum.EXECUTE_ERROR.getCode());
            log.error("批量调价执行失败:"+e.getMessage(),e);
        }
        update.setUpdateUser(operator);
        update.setUpdateTime(now);
        contractBatchAdjustPriceBiz.updateSelective(update);
        contractBatchAdjustPriceOpLogBiz.addLog(contractBatchAdjustPrice.getBatchId(),"执行调价方案","执行调价方案",operator);
    }

    /**
     * 获取调价合同id和对应的商品id，返回查询对象
     */
    private OrderSimpleQueryDTO getContractIdAndGoodsIds(List<BatchAdjustPriceContractQueryContractInfoDTO> list){
        OrderSimpleQueryDTO queryDTO = new OrderSimpleQueryDTO();
        queryDTO.setList(Lists.newArrayList());
        if(CollectionUtils.isNotEmpty(list)){
            for (BatchAdjustPriceContractQueryContractInfoDTO item : list) {
                Set<OrderSimpleQueryContractGoodsDTO> collect = item.getGoodsList().stream().map(i -> new OrderSimpleQueryContractGoodsDTO(i.getTransportType(), i.getGoodsId())).collect(Collectors.toSet());
                queryDTO.getList().addAll(contractBiz.getContractIdAndGoodsIds(item.getContractId(), collect));
            }
        }
        return queryDTO;
    }

    /**
     * 间隔执行
     * @param hour
     */
    @Override
    public void perbatchAdjustPriceRemind(Integer hour,Integer intervalSecond) {
        //1、查询6小时后执行的调价方案
        Condition condition = new Condition(ContractBatchAdjustPrice.class);
        condition.createCriteria()
                //未删除
                .andEqualTo("delFlg",false)
                //待执行
                .andEqualTo("status",ContractAdjustStatusEnum.UN_EXECUTE.getCode())
                //大于等于当前时间+6小时
                .andGreaterThanOrEqualTo("fixUptime",new Date(System.currentTimeMillis()+hour*3600L*1000L))
                //小于等于当前时间+6小时+任务执行间隔时间(当前设置为10分钟)
                .andLessThanOrEqualTo("fixUptime",new Date(System.currentTimeMillis()+hour*3600L*1000L + intervalSecond*1000L));
        List<ContractBatchAdjustPrice> list = contractBatchAdjustPriceBiz.findByCondition(condition);
        if( CollectionUtils.isEmpty(list)){
            log.info("没有找到调价方案");
            return;
        }
        log.info("找到调价方案:{}",list.stream().map(ContractBatchAdjustPrice::getBatchCode).collect(Collectors.joining(",")));
        log.info("找到的调价方案对应的卖家id有:{}",list.stream().map(ContractBatchAdjustPrice::getSellerId).distinct().collect(Collectors.joining(",")));
        //2、 根据卖家参数看看是否需要推送 需要先查询卖家参数在过滤后再执行后续操作
        MemberIdsAndKeyCodesDTO memberIdsAndKeyCodesDTO = new MemberIdsAndKeyCodesDTO(list.stream().map(item -> item.getSellerId()).collect(Collectors.toSet()), Lists.newArrayList(MemberOrderConfigEnum.CONTRACT_ADJUST_AUTO_CLOSE.getCode()));
        List<MemberConfigDTO> configList = memberConfigService.findByMemberIdsAndKeyCodes(memberIdsAndKeyCodesDTO);
        if( CollectionUtils.isEmpty(configList)){
            log.info("没有找到会员参数:{},args:{}",MemberOrderConfigEnum.CONTRACT_ADJUST_AUTO_CLOSE.getCode(),memberIdsAndKeyCodesDTO);
            return;
        }
        Set<String> sellerIds = configList.stream()
                .filter(item -> CsStringUtils.isNotBlank(item.getMemberId()))
                .filter(item -> "1".equals(item.getValue()))
                .map(MemberConfigDTO::getMemberId).collect(Collectors.toSet());
        if( CollectionUtils.isEmpty(configList)){
            log.info("过滤后,没有找到要提现的会员id");
            return;
        }
        log.info("根据卖家参数{}过滤后的卖家id有:{}",MemberOrderConfigEnum.CONTRACT_ADJUST_AUTO_CLOSE.getCode(),sellerIds);
        //3、筛选结果1中的合同id和对应的商品id 根据卖家参数过滤
        //根据调价规则查询合同和合同下的商品
        List<BatchAdjustPriceContractQueryContractInfoDTO> contractList = list.parallelStream()
                .filter(item->sellerIds.contains(item.getSellerId()))
                .map(item -> contractBiz.getContractList(item,true)).flatMap(Collection::stream).toList();
        OrderSimpleQueryDTO queryDTO = getContractIdAndGoodsIds(contractList);
        //4、根据合同id和合同商品id查询对应的订单信息
        List<OrderSimpleDTO> orderList = orderService.queryOrderByContractBatchAdjustPriceInfo(queryDTO);
        if( CollectionUtils.isEmpty(orderList)){
            log.info("未找到相应的合同订单买家信息");
            return;
        }
        log.info("根据合同和合同商品查询到的要关闭的订单有:{}",orderList.stream().map(item->item.getOrderCode()).collect(Collectors.joining(",")));
        //5、根据买家id和创建人id分组
        Map<String, Set<String>> buyerIdAndCreateUserIdGroupList = orderList.stream()
                .collect(Collectors.groupingBy(item -> item.getBuyerId() + "_" + item.getCreateUser(),
                        Collectors.mapping(OrderSimpleDTO::getOrderCode, Collectors.toSet())));
        log.info("根据买家id和创建人id分组:{}",buyerIdAndCreateUserIdGroupList);
        //6、查询创建人信息
        Set<String> createUserSet = orderList.stream().map(OrderSimpleDTO::getCreateUser).filter(Objects::nonNull).collect(Collectors.toSet());
        List<AccountSimpleDTO> accountList = accountService.findSimpleByIds(Lists.newArrayList(createUserSet));
        Map<String, AccountSimpleDTO> accountMap = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(accountList)) {
            accountMap = accountList.stream().collect(Collectors.toMap(AccountSimpleDTO::getAccountId, Function.identity()));
        }
        log.info("accountMap:{}",accountMap);

        for (Map.Entry<String, Set<String>> entry : buyerIdAndCreateUserIdGroupList.entrySet()) {
            String[] buyerIdAndCreateUser = entry.getKey().split("_");
            String buyerId = buyerIdAndCreateUser[0];
            String createUser = buyerIdAndCreateUser.length>=2 ? buyerIdAndCreateUser[1] : null;
            List<String> orderCodeList = Lists.newArrayList(entry.getValue());
            //7、判断是否代客下单: 如果是代客下单的，则创建人的memberid与买家id不相同
            if (createUser != null && accountMap.containsKey(createUser) && !CsStringUtils.equals(buyerId, accountMap.get(createUser).getMemberId())) {
                createUser = null;
            }
            //8、发送提醒通知  **************** 20个订单发一次，不能超过短信最长限制 如果超了，需要分多次发送
            setMessage(hour, orderCodeList, buyerId, createUser);
        }
    }

    private void setMessage(Integer hour, List<String> orderCodeList, String buyerId, String createUser) {
        if( orderCodeList.size() > 20 ) {
            for (int i = 0; i*20 < orderCodeList.size(); i++) {
                int begin = i*20;
                int end = (i+1)*20 < orderCodeList.size() ? (i+1)*20 : orderCodeList.size();
                log.info("orderCodeList.subList:{}-{}",begin,end);
                smsMessageProducer.sendMessageForContractBatchAdjustPriceRemind(buyerId, createUser, orderCodeList.subList(begin,end), hour);
            }
        }else{
            smsMessageProducer.sendMessageForContractBatchAdjustPriceRemind(buyerId, createUser, orderCodeList, hour);
        }
    }

    @Override
    public PageInfo<ContractAdjustPriceDTO> findPriceChangeHistoryByContractId(ContractPriceChangeHistoryQueryDTO dto) {
        return contractBatchAdjustPriceResultBiz.findByContractId(dto);
    }

    @Override
    public void confirm(ContractBatchAdjustPriceConfirmDTO confirmDTO) {
        log.info("confirm:{}",confirmDTO);
        //修改调价单的状态为0
        ContractBatchAdjustPrice contractBatchAdjustPrice = new ContractBatchAdjustPrice();
        contractBatchAdjustPrice.setBatchId(confirmDTO.getBatchId());
        contractBatchAdjustPrice.setStatus(ContractAdjustStatusEnum.UN_EXECUTE.getCode());
        setOperInfo(contractBatchAdjustPrice,confirmDTO.getUpdateUser(),false);
        contractBatchAdjustPriceBiz.updateSelective(contractBatchAdjustPrice);

        //异步调用erp电商价格调整（事前）接口
        CompletableFuture.runAsync(()->{
            this.modifyPriceNoticeERP(confirmDTO);
        });
    }

    @Override
    public void modifyPriceNoticeERP(ContractBatchAdjustPriceConfirmDTO confirmDTO) {
        log.info("modifyPriceNoticeERP:{}",confirmDTO);
        try{
            ContractBatchAdjustPriceDTO contractBatchAdjustPriceDTO = this.findByMemberIdAndBatchId(confirmDTO.getSellerId(), confirmDTO.getBatchId());
            ECModifyPriceDTO ecModifyPriceDTO = new ECModifyPriceDTO();
            ecModifyPriceDTO.setPricechangeNo(contractBatchAdjustPriceDTO.getBatchCode());
            ecModifyPriceDTO.setPricechangeValiddate(contractBatchAdjustPriceDTO.getFixUptime());
            ecModifyPriceDTO.setOperType("I");
            Set<String> excludeContractIdSet = Sets.newHashSet();
            if (CollectionUtils.isNotEmpty(contractBatchAdjustPriceDTO.getExcludeContractIds())) {
                contractBatchAdjustPriceDTO.getExcludeContractIds().stream().forEach(excludeContractIdSet::add);
            }
            List<ECModifyPriceItemDTO> modifyPriceItemDTOList = Lists.newArrayList();
            Set<String> contractIds = new HashSet<>();
            Set<String> warehouseIds = new HashSet<>();
            Set<String> saleRegionIds = new HashSet<>();
            Set<String> goodsIds = new HashSet<>();

            //遍历批量调价合同商品
            addGoodsIds(contractBatchAdjustPriceDTO, excludeContractIdSet, contractIds, warehouseIds, saleRegionIds, goodsIds);
            //查询合同信息
            log.info("contractIds:{}",contractIds);
            List<TrContract> contractList = contractBiz.findByIds(contractIds);
            log.info("contractList:{}",JSON.toJSONString(contractList));
            Map<String,TrContract> contractMap = contractList.stream().collect(Collectors.toMap(TrContract::getContractId,Function.identity()));

            //查询仓库信息
            log.info("warehouseIds:{}",warehouseIds);
            List<WarehouseListDTO> warehouseListDTOList = warehouseService.queryWarehouseListByIds(new ArrayList<>(warehouseIds));
            log.info("warehouseListDTOList:{}",JSON.toJSONString(warehouseListDTOList));
            Map<String,WarehouseListDTO> warehouseMap = warehouseListDTOList.stream().collect(Collectors.toMap(WarehouseListDTO::getWarehouseId,Function.identity()));

            //查询销售区域信息
            log.info("saleRegionIds:{}",saleRegionIds);
            List<SaleRegionSampleDTO> saleRegionList = saleRegionService.findSampleByIdList(new ArrayList<>(saleRegionIds));
            log.info("saleRegionList:{}",JSON.toJSONString(saleRegionList));
            Map<String,SaleRegionSampleDTO> saleRegionMap = saleRegionList.stream().collect(Collectors.toMap(SaleRegionSampleDTO::getSaleRegionId,Function.identity()));

            //查询商品信息
            log.info("goodsIds:{}",goodsIds);
            List<Goods> goodsList = goodsBiz.selectByIds(goodsIds);
            log.info("goodsList:{}",JSON.toJSONString(goodsIds));
            Map<String,Goods> goodsMap = goodsList.stream().collect(Collectors.toMap(Goods::getGoodsId,Function.identity()));

            addModifyPriceItemDTOList(contractBatchAdjustPriceDTO, excludeContractIdSet, contractMap, warehouseMap, saleRegionMap, goodsMap, modifyPriceItemDTOList);
            ecModifyPriceDTO.setModifyPriceItemDTOList(modifyPriceItemDTOList);
            log.info("事前调价: {}", JSON.toJSONString(ecModifyPriceDTO));
            ApiResult apiResult = openAPIInvokeService.invoke(BizCodeEnum.EC_PRI_A1.code(), confirmDTO.getSellerId(), JSON.toJSONString(ecModifyPriceDTO));
            log.info("apiResult:{}",apiResult);
            if (!apiResult.isSuccess()) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, apiResult.getDescription());
            }
        }catch (Exception e){
            String description = "合同批量调价（事前调整）电商通知ERP异常";
            CodeMeta errorCode = BasicCode.UNKNOWN_ERROR;
            if (e instanceof BizException) {
                BizException be = (BizException)e;
                errorCode = be.getErrorCode();
                description = description + "," + be.getMessage();
            }
            ItemResult<Object> itemResult = new ItemResult<>(errorCode.getCode(), description);
            externalExceptionBizService.externalExceptionHandle(itemResult,confirmDTO.getBatchId(), ExternalMethodTypeEnum.CONTRACT_BATCH_ADJUST_PRICE.getCode(),confirmDTO.getSellerId());
            log.error("合同批量调价（事前调整）电商通知ERP报错", e);
        }
    }

    private static void addModifyPriceItemDTOList(ContractBatchAdjustPriceDTO contractBatchAdjustPriceDTO, Set<String> excludeContractIdSet, Map<String, TrContract> contractMap, Map<String, WarehouseListDTO> warehouseMap, Map<String, SaleRegionSampleDTO> saleRegionMap, Map<String, Goods> goodsMap, List<ECModifyPriceItemDTO> modifyPriceItemDTOList) {
        if (CollectionUtils.isNotEmpty(contractBatchAdjustPriceDTO.getContractList())){
            //出厂价调整额度
            BigDecimal factoryPrice = contractBatchAdjustPriceDTO.getFactoryPrice() == null ? BigDecimal.ZERO : contractBatchAdjustPriceDTO.getFactoryPrice();
            //到位价调整额度
            BigDecimal arrivePrice = contractBatchAdjustPriceDTO.getArrivePrice() == null ? BigDecimal.ZERO : contractBatchAdjustPriceDTO.getArrivePrice();

            modifyPriceItemDTOListAdd(contractBatchAdjustPriceDTO, excludeContractIdSet, contractMap, warehouseMap, saleRegionMap, goodsMap, modifyPriceItemDTOList, factoryPrice, arrivePrice);
        }
    }

    private static void modifyPriceItemDTOListAdd(ContractBatchAdjustPriceDTO contractBatchAdjustPriceDTO, Set<String> excludeContractIdSet, Map<String, TrContract> contractMap, Map<String, WarehouseListDTO> warehouseMap, Map<String, SaleRegionSampleDTO> saleRegionMap, Map<String, Goods> goodsMap, List<ECModifyPriceItemDTO> modifyPriceItemDTOList, BigDecimal factoryPrice, BigDecimal arrivePrice) {
        contractBatchAdjustPriceDTO.getContractList().forEach(batchAdjustPriceContractQueryContractInfoDTO -> {
            if (excludeContractIdSet.contains(batchAdjustPriceContractQueryContractInfoDTO.getContractId())) {
                return;
            }
            if (CollectionUtils.isNotEmpty(batchAdjustPriceContractQueryContractInfoDTO.getGoodsList())){
                batchAdjustPriceContractQueryContractInfoDTO.getGoodsList().forEach(batchAdjustPriceContractQueryContractGoodsInfoDTO -> {
                    ECModifyPriceItemDTO ecModifyPriceItemDTO = new ECModifyPriceItemDTO();
                    TrContract contract = contractMap.get(batchAdjustPriceContractQueryContractInfoDTO.getContractId());
                    WarehouseListDTO warehouseListDTO = warehouseMap.get(batchAdjustPriceContractQueryContractGoodsInfoDTO.getOutGoodsAddressId());
                    SaleRegionSampleDTO saleRegionSampleDTO = saleRegionMap.get(batchAdjustPriceContractQueryContractGoodsInfoDTO.getSaleRegionId());
                    Goods goods = goodsMap.get(batchAdjustPriceContractQueryContractGoodsInfoDTO.getGoodsId());

                    ecModifyPriceItemDTO.setMdmCode(contract.getMdmCode());
                    ecModifyPriceItemDTO.setContractCode(contract.getErpContractNum());
                    ecModifyPriceItemDTO.setPickupPointCode(warehouseListDTO.getErpCode());
                    ecModifyPriceItemDTO.setPickupPointOrgId(warehouseListDTO.getErpOrgCode());
                    String erpCode = saleRegionSampleDTO.getErpCode();
                    if (CsStringUtils.isNotEmpty(erpCode) && erpCode.contains("/")) {
                        ecModifyPriceItemDTO.setSaleBigArea(erpCode.substring(0,erpCode.indexOf("/")));
                        ecModifyPriceItemDTO.setSaleArea(erpCode.substring(erpCode.indexOf("/") + 1));
                    }
                    ecModifyPriceItemDTO.setCommodityCode(goods.getCommodityCode());

                    ecModifyPriceItemDTO.setPricechg(factoryPrice);
                    ecModifyPriceItemDTO.setPricechg2(arrivePrice);
                    ecModifyPriceItemDTO.setPriceafter1(factoryPrice.add(batchAdjustPriceContractQueryContractGoodsInfoDTO.getFactoryPrice()));
                    ecModifyPriceItemDTO.setPriceafter2(arrivePrice.add(batchAdjustPriceContractQueryContractGoodsInfoDTO.getArrivePrice()));
                    modifyPriceItemDTOList.add(ecModifyPriceItemDTO);
                });
            }
        });
    }

    private static void addGoodsIds(ContractBatchAdjustPriceDTO contractBatchAdjustPriceDTO, Set<String> excludeContractIdSet, Set<String> contractIds, Set<String> warehouseIds, Set<String> saleRegionIds, Set<String> goodsIds) {
        if (CollectionUtils.isNotEmpty(contractBatchAdjustPriceDTO.getContractList())){
            contractBatchAdjustPriceDTO.getContractList().forEach(batchAdjustPriceContractQueryContractInfoDTO -> {
                if (excludeContractIdSet.contains(batchAdjustPriceContractQueryContractInfoDTO.getContractId())) {
                    return;
                }
                contractIds.add(batchAdjustPriceContractQueryContractInfoDTO.getContractId());
                if (CollectionUtils.isNotEmpty(batchAdjustPriceContractQueryContractInfoDTO.getGoodsList())){
                    batchAdjustPriceContractQueryContractInfoDTO.getGoodsList().forEach(batchAdjustPriceContractQueryContractGoodsInfoDTO -> {
                        warehouseIds.add(batchAdjustPriceContractQueryContractGoodsInfoDTO.getOutGoodsAddressId());
                        saleRegionIds.add(batchAdjustPriceContractQueryContractGoodsInfoDTO.getSaleRegionId());
                        goodsIds.add(batchAdjustPriceContractQueryContractGoodsInfoDTO.getGoodsId());
                    });
                }
            });
        }
    }

    private ContractBatchAdjustPriceService getCurrentProxy() {
        if (thisProxy != null) {
            return thisProxy;
        }

        synchronized (this) {
            if (thisProxy != null) {
                return thisProxy;
            }
            thisProxy = SpringContextHolder.getBean(ContractBatchAdjustPriceService.class);
        }
        return thisProxy;
    }

}
