package com.ecommerce.goods.service.impl;

import com.ecommerce.base.api.dto.attachment.AttachmentFileDTO;
import com.ecommerce.base.api.service.IAttachmentService;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.utils.DateUtil;
import com.ecommerce.goods.api.dto.contract.*;
import com.ecommerce.goods.biz.IContractAdjustPriceBiz;
import com.ecommerce.goods.biz.IContractBiz;
import com.ecommerce.goods.biz.IContractTemplateBiz;
import com.ecommerce.goods.service.IContractTemplateService;
import com.github.pagehelper.PageInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class ContractTemplateService implements IContractTemplateService {


    private final IContractTemplateBiz contractTemplateBiz;


    private final IContractBiz contractBiz;


    private final IAttachmentService attachmentService;


    private final IContractAdjustPriceBiz contractAdjustPriceBiz;

    @Override
    public PageInfo<TrContractTemplateDTO> pageContractTemplate(ReqSellerContractTemplateDTO reqDTO, String memberId) {
        return contractTemplateBiz.pageContractTemplate(reqDTO,memberId);
    }

    @Override
    public Boolean createContractTemplate(TrContractTemplateDTO createDTO) {
        return contractTemplateBiz.createContractTemplate(createDTO);
    }

    @Override
    public TrContractTemplateDTO getContractTemplateDetail(String templateId) {
        return contractTemplateBiz.getContractTemplateDetail(templateId);
    }

    @Override
    public Boolean deleteContractTemplate(String templateId) {
        return contractTemplateBiz.deleteContractTemplate(templateId);
    }

    @Override
    public Boolean updateTemplateStatus(String templateId, Integer status, String operator) {
        return contractTemplateBiz.updateTemplateStatus(templateId,status,operator);
    }

    @Override
    public List<TrContractTemplateDTO> getContractTemplateList(ReqSellerContractTemplateDTO req, String memberId) {
        return contractTemplateBiz.getContractTemplateList(req,memberId);
    }

    @Override
    public DownloadResponseDTO downloadFileByContract(String contractId) {
        if (CsStringUtils.isEmpty(contractId)) {
            throw new BizException(BasicCode.PARAM_NULL);
        }
        TrContractDTO contractDTO = contractBiz.getContract(contractId);
        if (contractDTO == null) {
            throw new BizException(BasicCode.DB_DATA_NOT_FOUND);
        }
        List<TrContractGoodsDTO> dtos = contractDTO.getContractGoodsDTOS();
        if (CollectionUtils.isNotEmpty(dtos)) {
            for (TrContractGoodsDTO dto : dtos) {
                dto.setPriceFlag("2".equals(dto.getPriceStatus()));
            }
        }
        String templateId = contractDTO.getContractTemplateId();
        if (CsStringUtils.isEmpty(templateId)) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "数据异常: 模板ID为空");
        }
        TrContractTemplateDTO templateDTO = contractTemplateBiz.getContractTemplateDetail(templateId);
        if (CsStringUtils.isEmpty(templateDTO.getTemplateUrl())) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "数据异常: 模板url为空");
        }
        AttachmentFileDTO fileDTO = attachmentService.downLoadFileByUrl(templateDTO.getTemplateUrl());
        if (fileDTO == null || fileDTO.getFile().length == 0) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "数据异常: 模板文件不存在");
        }

        log.info(" >> fileDTO -> fileName:{}, len: {}", fileDTO.getFileName(), fileDTO.getFileSize());
        ByteArrayInputStream input = new ByteArrayInputStream(fileDTO.getFile());

        byte[] bytes = contractTemplateBiz.downloadFileByContract(contractDTO, input);
        log.info("result: len={}", bytes.length);
        DownloadResponseDTO responseDTO = new DownloadResponseDTO();
        responseDTO.setBytes(bytes);
        String type = ".docx";
        String fileName = DateUtil.format(new Date(), "yyyyMMddHHmmss");
        responseDTO.setFileName(fileName + type);
        responseDTO.setType(type);
        return responseDTO;
    }

    @Override
    public DownloadResponseDTO downloadFileByAdjustPrice(String adjustPriceId) {
        if (CsStringUtils.isEmpty(adjustPriceId)) {
            throw new BizException(BasicCode.PARAM_NULL);
        }
        ContractAdjustPriceDTO contractAdjustPriceDTO = contractAdjustPriceBiz.getContractAdjustPriceById(adjustPriceId);
        if (contractAdjustPriceDTO == null) {
            throw new BizException(BasicCode.DB_DATA_NOT_FOUND);
        }
        String templateId = contractAdjustPriceDTO.getContractAdjustPriceTemplateId();
        if (CsStringUtils.isEmpty(templateId)) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "数据异常: 模板ID为空");
        }
        TrContractTemplateDTO templateDTO = contractTemplateBiz.getContractTemplateDetail(templateId);
        if (CsStringUtils.isEmpty(templateDTO.getTemplateUrl())) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "数据异常: 模板url为空");
        }
        AttachmentFileDTO fileDTO = attachmentService.downLoadFileByUrl(templateDTO.getTemplateUrl());
        if (fileDTO == null || fileDTO.getFile().length == 0) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "数据异常: 模板文件不存在");
        }

        log.info(" >> fileDTO -> fileName:{}, len: {}", fileDTO.getFileName(), fileDTO.getFileSize());
        ByteArrayInputStream input = new ByteArrayInputStream(fileDTO.getFile());

        byte[] bytes = contractTemplateBiz.downloadFileByAdjustPrice(contractAdjustPriceDTO, input);
        log.info("result: len={}", bytes.length);
        DownloadResponseDTO responseDTO = new DownloadResponseDTO();
        responseDTO.setBytes(bytes);
        String type = ".docx";
        String fileName = DateUtil.format(new Date(), "yyyyMMddHHmmss");
        responseDTO.setFileName(fileName + type);
        responseDTO.setType(type);
        return responseDTO;
    }
}
