package com.ecommerce.goods.service.impl;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.goods.api.dto.externalexception.ExternalExceptionRetryDTO;
import com.ecommerce.goods.biz.IExternalExceptionBizService;
import com.ecommerce.goods.service.IExternalExceptionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @Description: externalExceptionRetryHandler
 * <AUTHOR>
 * @Date: 2021-05-20 14:11
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ExternalExceptionService implements IExternalExceptionService {


    private final IExternalExceptionBizService externalExceptionBizService;

    @Override
    public ItemResult<Void> externalExceptionRetryHandler(ExternalExceptionRetryDTO externalExceptionRetryDTO) {
        log.info("externalExceptionRetryHandler:{}", externalExceptionRetryDTO);
        externalExceptionBizService.externalExceptionRetryHandler(externalExceptionRetryDTO);
        return new ItemResult<>(null);
    }
}
