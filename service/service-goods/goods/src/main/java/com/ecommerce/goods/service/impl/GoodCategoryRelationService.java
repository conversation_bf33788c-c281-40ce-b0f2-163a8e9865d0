package com.ecommerce.goods.service.impl;

import com.ecommerce.goods.api.dto.GoodCategoryRelationDto;
import com.ecommerce.goods.biz.IGoodCategoryRelationBiz;
import com.ecommerce.goods.service.IGoodCategoryRelationService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class GoodCategoryRelationService implements IGoodCategoryRelationService {

    private final IGoodCategoryRelationBiz goodCategoryRelationBiz;

    public GoodCategoryRelationService(IGoodCategoryRelationBiz goodCategoryRelationBiz) {
        this.goodCategoryRelationBiz = goodCategoryRelationBiz;
    }

    @Override
    public boolean save(List<GoodCategoryRelationDto> dtos) {
        return goodCategoryRelationBiz.save(dtos);
    }
}
