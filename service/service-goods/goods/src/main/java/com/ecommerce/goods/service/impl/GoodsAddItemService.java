package com.ecommerce.goods.service.impl;

import com.alibaba.fastjson.JSON;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.service.common.BaseService;
import com.ecommerce.goods.api.dto.AddItemQueryConditionDTO;
import com.ecommerce.goods.api.dto.GoodsAddItemDTO;
import com.ecommerce.goods.api.dto.contract.TrContractAdditemDTO;
import com.ecommerce.goods.biz.IGoodsAddItemBiz;
import com.ecommerce.goods.dao.vo.GoodsAddItem;
import com.ecommerce.goods.service.IGoodsAddItemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Auther: chenjun
 * @Description:
 * @Date: 22/08/2018 11:39
 */
@Slf4j
@Service
public class GoodsAddItemService extends BaseService<GoodsAddItem> implements IGoodsAddItemService {

    @Autowired
    IGoodsAddItemBiz goodsAddItemBiz;

    @Override
    public Boolean businessCreateTemplate(List<GoodsAddItemDTO> itemValue, String memberId,String operatorId) {
        return goodsAddItemBiz.businessCreateTemplate(itemValue,memberId,operatorId);
    }

    @Override
    public Boolean businessUpdateTemplate(List<GoodsAddItemDTO> itemValue, String operatorId) {
        return goodsAddItemBiz.businessUpdateTemplate(itemValue,operatorId);
    }


    @Override
    public List<GoodsAddItemDTO> listBusinessTemplate(Integer goodsTypeCode, String memberId) {
        return goodsAddItemBiz.listBusinessTemplate(goodsTypeCode,memberId);
    }
    
    @Override
    public List<GoodsAddItemDTO> listBusinessTemplateByResource( String resourceId, String memberId) {
        return goodsAddItemBiz.listBusinessTemplateByResource(resourceId,memberId);
    }

    @Override
    public void syncERPGoodsAddItem(String sellerId, String operator) {
        goodsAddItemBiz.syncERPGoodsAddItem(sellerId,operator);
    }

    @Override
    public GoodsAddItemDTO getGoodsAddItemByCode(String erpCode, String sellerId) {
        return goodsAddItemBiz.getGoodsAddItemByCode(erpCode,sellerId);
    }

    @Override
    public List<GoodsAddItemDTO> getGoodsAddItemsByCode(List<String> erpCodes, String sellerId) {
        return goodsAddItemBiz.getGoodsAddItemsByCode(erpCodes,sellerId);
    }

    @Override
    public List<GoodsAddItemDTO> getGoodsAddItemListById(List<String> addItemIdList, String sellerId) {
        return goodsAddItemBiz.getGoodsAddItemListById(addItemIdList, sellerId);
    }

    @Override
    public List<GoodsAddItemDTO> listBaseTemplate(Integer goodsTypeCode) {
        return goodsAddItemBiz.listBaseTemplate(goodsTypeCode);
    }

    @Override
    public List<GoodsAddItemDTO> getGoodsAddItems(String addItemIds) {
        return goodsAddItemBiz.getGoodsAddItems(addItemIds);
    }

    @Override
    public String getItemVersion() {
        return goodsAddItemBiz.getItemVersion();
    }

    @Override
    public Boolean updateGoodsAddItems(String memberId) {
        return goodsAddItemBiz.updateGoodsAddItems(memberId);
    }

    @Override
    public List<GoodsAddItemDTO> queryAddItemByCondition(AddItemQueryConditionDTO addItemQueryConditionDTO) {
        if (addItemQueryConditionDTO.getAddItemType() == null) {
            throw new BizException(BasicCode.INVALID_PARAM, "查询类型");
        }
        if (CollectionUtils.isEmpty(addItemQueryConditionDTO.getAddItemIdList())) {
            throw new BizException(BasicCode.INVALID_PARAM, "加价项id列表");
        }
        if (CsStringUtils.isEmpty(addItemQueryConditionDTO.getSellerId())) {
            throw new BizException(BasicCode.INVALID_PARAM, "卖家ID");
        }
        List<GoodsAddItemDTO> goodsAddItemList = goodsAddItemBiz.getGoodsAddItemListById(
                addItemQueryConditionDTO.getAddItemIdList(),
                addItemQueryConditionDTO.getSellerId());
        log.info("goodsAddItemList:" + JSON.toJSONString(goodsAddItemList));
        if (addItemQueryConditionDTO.getAddItemType() == 2) {
            if (CsStringUtils.isEmpty(addItemQueryConditionDTO.getContractId())) {
                throw new BizException(BasicCode.INVALID_PARAM, "合同ID");
            }
            List<TrContractAdditemDTO> contractAdditemList = goodsAddItemBiz.getContractAddItemListById(
                    addItemQueryConditionDTO.getAddItemIdList(),
                    addItemQueryConditionDTO.getContractId());
            log.info("contractAdditemList:" + JSON.toJSONString(contractAdditemList));
            Map<String, TrContractAdditemDTO> contractAdditemMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(contractAdditemList)) {
                contractAdditemList.stream().forEach(contractAdditem -> contractAdditemMap.put(contractAdditem.getAdditemId(), contractAdditem));
            }
            //设置加价项价格
            if (CollectionUtils.isNotEmpty(goodsAddItemList)) {
                goodsAddItemList.stream().forEach(goodsAddItem -> {
                    if (contractAdditemMap.get(goodsAddItem.getAdditemId()) == null) return;
                    goodsAddItem.setAdditemPrice(contractAdditemMap.get(goodsAddItem.getAdditemId()).getAdditemPrice());
                });
            }
        }

        return goodsAddItemList;

    }

    @Override
    public void bindERPAddItem(GoodsAddItemDTO dto, String operatorId) {
        goodsAddItemBiz.bindERPAddItem(dto, operatorId);
    }
}
