package com.ecommerce.goods.service.impl;

import com.ecommerce.common.annotation.AddLog;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.utils.BeanConvertUtils;
import com.ecommerce.common.utils.CommonConstants;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.goods.api.dto.CategoryAttributeDTO;
import com.ecommerce.goods.api.dto.CategoryAttributeValueDTO;
import com.ecommerce.goods.api.dto.CategoryDTO;
import com.ecommerce.goods.api.dto.CategoryTreeDTO;
import com.ecommerce.goods.api.dto.GoodsAttributeDTO;
import com.ecommerce.goods.api.dto.GoodsCategoryAttrDTO;
import com.ecommerce.goods.api.dto.GoodsCategoryDTO;
import com.ecommerce.goods.api.dto.GoodsCategorySimpleDTO;
import com.ecommerce.goods.api.dto.HomeMenuCategoryTreeDTO;
import com.ecommerce.goods.biz.IGoodsBiz;
import com.ecommerce.goods.biz.IGoodsCategoryAttrBiz;
import com.ecommerce.goods.biz.IGoodsCategoryAttrValueBiz;
import com.ecommerce.goods.biz.IGoodsCategoryBiz;
import com.ecommerce.goods.cache.CategoryTreeCache;
import com.ecommerce.goods.cache.ICategoryTreeCacheService;
import com.ecommerce.goods.cache.ICodeNumCacheService;
import com.ecommerce.goods.dao.mapper.GoodCategoryRelationMapper;
import com.ecommerce.goods.dao.vo.GoodCategoryRelation;
import com.ecommerce.goods.dao.vo.GoodsCategory;
import com.ecommerce.goods.dao.vo.GoodsCategoryAttribute;
import com.ecommerce.goods.dao.vo.GoodsCategoryAttributeValue;
import com.ecommerce.goods.exception.GoodsCode;
import com.ecommerce.goods.service.IGoodsCategoryService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

@Slf4j
@EnableAsync
@Service
@RequiredArgsConstructor
public class GoodsCategoryService implements IGoodsCategoryService {

    private static final String DEL_FLG = "delFlg";
    private static final String CATEGORY_TYPE = "categoryType";
    private static final String PARENT_ID = "parentId";
    private static final String CATEGORY_CODE = "categoryCode";
    private static final String GOODS_CATEGORY_ID = "商品分类ID";
    private static final String GOODS_CATEGORY_CODE = "商品分类编号";


    private final IGoodsBiz goodsBiz;

    private final IGoodsCategoryBiz goodsCategoryBiz;

    private final IGoodsCategoryAttrBiz goodsCategoryAttrBiz;

    private final ICodeNumCacheService codeNumCacheService;

    private final ICategoryTreeCacheService categoryTreeCacheService;

    private final IGoodsCategoryAttrValueBiz goodsCategoryAttrValueBiz;

    @Resource
    private GoodCategoryRelationMapper goodCategoryRelationMapper;
    /**
     * 的分类code
     */
    @Value("${cement-code}")
    private String cementCode;

    @Override
    public boolean checkCementByCategoryCode(String categoryCode) {
        if (CsStringUtils.isEmpty(categoryCode)) {
            return false;
        }
        return categoryCode.trim().startsWith(cementCode);
    }

    @AddLog(operatorIndex = 1)
    @Override
    public GoodsCategoryDTO createCategory(GoodsCategoryDTO goodsCategoryDTO, String operator) {
        GoodsCategory query = new GoodsCategory();
        GoodsCategory goodsCategory = BeanConvertUtils.convert(goodsCategoryDTO, GoodsCategory.class);
        String parentId = goodsCategory.getParentId();
        String code = goodsCategory.getCategoryCode();
        String name = goodsCategory.getCategoryName();
        if (CsStringUtils.isBlank(code)) {
            code = generateCategoryCode(parentId);
            goodsCategory.setCategoryCode(code);
        }
        if (CsStringUtils.isBlank(name)) {
            throw new BizException(GoodsCode.VALUE_NOT_EMPTY, "名称");
        }
        query.setCategoryCode(code);
        List<GoodsCategory> datas = goodsCategoryBiz.find(query);
        if (CollectionUtils.isNotEmpty(datas)) {
            throw new BizException(GoodsCode.DATA_DUPLICATE, "编码");
        }
        query = new GoodsCategory();
        query.setCategoryName(name);
        datas = goodsCategoryBiz.find(query);
        if (CollectionUtils.isNotEmpty(datas)) {
            throw new BizException(GoodsCode.DATA_DUPLICATE, "名称");
        }
        goodsCategory = goodsCategoryBiz.save(goodsCategory, operator);
        return BeanConvertUtils.convert(goodsCategory, GoodsCategoryDTO.class);
    }

    @Override
    public GoodsCategoryDTO findCategoryById(String categoryId) {
        GoodsCategory goodsCategory = goodsCategoryBiz.get(categoryId);
        return BeanConvertUtils.convert(goodsCategory, GoodsCategoryDTO.class);
    }

    @AddLog(operatorIndex = 1)
    @Override
    public GoodsCategoryDTO updateCategory(GoodsCategoryDTO goodsCategoryDTO, String operator) {
        GoodsCategoryDTO newCategory = null;
        if (goodsCategoryDTO != null &&
                CsStringUtils.isNotBlank(goodsCategoryDTO.getCategoryId())) {

            if (CsStringUtils.isBlank(goodsCategoryDTO.getCategoryName())) {
                throw new BizException(GoodsCode.VALUE_NOT_EMPTY, "名称");
            }

            //名称重复Check
            Condition condition = new Condition(GoodsCategory.class);
            Criteria criteria = condition.createCriteria();
            criteria.andEqualTo("categoryName", goodsCategoryDTO.getCategoryName());
            criteria.andNotEqualTo("categoryId", goodsCategoryDTO.getCategoryId());
            List<GoodsCategory> categorys = goodsCategoryBiz.findByCondition(condition);
            if (CollectionUtils.isNotEmpty(categorys)) {
                throw new BizException(GoodsCode.DATA_DUPLICATE, "名称");
            }

            GoodsCategory goodsCategory = goodsCategoryBiz.get(goodsCategoryDTO.getCategoryId());
            if (goodsCategory != null) {
                GoodsCategory updCategory = BeanConvertUtils.convert(goodsCategoryDTO, GoodsCategory.class);
                BeanConvertUtils.copyPropertiesIgnoreNull(updCategory, goodsCategory);
                goodsCategory = goodsCategoryBiz.save(goodsCategory, operator);
                newCategory = BeanConvertUtils.convert(goodsCategory, GoodsCategoryDTO.class);
            }
        }

        return newCategory;
    }

    @AddLog(operatorIndex = 1)
    @Override
    public void deleteCategory(String categoryId, String operator) {
        if (CsStringUtils.isNotBlank(categoryId)) {
            GoodsCategory goodsCategory = goodsCategoryBiz.get(categoryId);
            if (goodsCategory != null) {
                goodsCategory.setDelFlg(true);
                goodsCategory.setUpdateUser(operator);
                goodsCategory.setUpdateTime(new Date());
                goodsCategoryBiz.updateSelective(goodsCategory);
            }
        }
    }

    /**
     * generateCategoryCode
     */
    private String generateCategoryCode(GoodsCategory parent) {
        String categoryCode;
        Condition condition = new Condition(GoodsCategory.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(PARENT_ID, parent.getCategoryId());
        criteria.andEqualTo(DEL_FLG, Boolean.FALSE);
        condition.orderBy(CATEGORY_CODE).desc();
        List<GoodsCategory> byCondition = goodsCategoryBiz.findByCondition(condition);
        if (CollectionUtils.isEmpty(byCondition)) {
            categoryCode = parent.getCategoryCode() + "001";
        } else {
            GoodsCategory goodsCategory = byCondition.get(0);
            String code = goodsCategory.getCategoryCode();
            String substring = code.substring(code.length() - 3);
            int i = Integer.parseInt(substring) + 1;
            String xs = String.format("%03d", i);
            categoryCode = parent.getCategoryCode() + xs;
        }
        return categoryCode;
    }

    /**
     * generateCategoryType
     */
    private Integer generateCategoryType() {
        Condition condition = new Condition(GoodsCategory.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(DEL_FLG, Boolean.FALSE);
        condition.orderBy(CATEGORY_TYPE).desc();
        List<GoodsCategory> list = goodsCategoryBiz.findByCondition(condition);
        GoodsCategory goodsCategory = list.get(0);
        Integer categoryType = goodsCategory.getCategoryType() + 1;
        return categoryType;
    }

    /**
     * batchInsertCategoryAttribute
     */
    private void batchInsertCategoryAttribute(List<CategoryAttributeDTO> categoryAttrs, String operator, Integer categoryType) {
        List<GoodsCategoryAttribute> goodsCategoryAttributes = Lists.newArrayList();
        Condition condition = new Condition(GoodsCategoryAttribute.class);
        Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(DEL_FLG, false);
        criteria.andIsNull(CATEGORY_TYPE);
        List<GoodsCategoryAttribute> goodsCategoryAttributeList = goodsCategoryAttrBiz.findByCondition(condition);
        if (CollectionUtils.isNotEmpty(goodsCategoryAttributeList)) {
            goodsCategoryAttributeList.forEach(goodsCategoryAttribute -> {
                if (CollectionUtils.isNotEmpty(categoryAttrs)) {
                    categoryAttrs.forEach(categoryAttr -> {
                        if (categoryAttr.getAttriName().equals(goodsCategoryAttribute.getAttriName())) {
                            goodsCategoryAttribute.setCategoryType(categoryType);
                            goodsCategoryAttributes.add(goodsCategoryAttribute);
                        }
                    });
                }
            });
            //插入商品分类基本属性
            if (CollectionUtils.isNotEmpty(goodsCategoryAttributes)) {
                goodsCategoryAttrBiz.batchInsert(goodsCategoryAttributes, operator);
            }
        }
    }

    @Override
    public void addCategory(CategoryDTO categoryDTO, String operator) {
        //TODO Platform 商品分类校验 @amrjlg
        if (CsStringUtils.isBlank(categoryDTO.getParentId())) {
            throw new BizException(GoodsCode.VALUE_NOT_EMPTY, "商品分类父ID");
        }
        if (CsStringUtils.isBlank(categoryDTO.getCategoryName())) {
            throw new BizException(GoodsCode.VALUE_NOT_EMPTY, "商品分类名称");
        }
        if (CsStringUtils.isBlank(operator)) {
            throw new BizException(GoodsCode.VALUE_NOT_EMPTY, "操作人");
        }
        GoodsCategory father = goodsCategoryBiz.get(categoryDTO.getParentId());
        if (father == null) {
            throw new BizException(GoodsCode.DATA_NOT_FOUND, "商品父类");
        }
        if (CsStringUtils.isBlank(categoryDTO.getCategoryName())) {
            throw new BizException(GoodsCode.VALUE_NOT_EMPTY, "分类名称");
        }
        //插入商品分类
        GoodsCategory goodsCategory = new GoodsCategory();
        BeanUtils.copyProperties(categoryDTO, goodsCategory);
        goodsCategory.setCategoryCode(generateCategoryCode(father));
        goodsCategory.setCategoryType(generateCategoryType());
        if (categoryDTO.getIfComputePorterage() == null) {
            goodsCategory.setIfComputePorterage(father.getIfComputePorterage());
        }
        if (categoryDTO.getIfSingleBuy() == null) {
            goodsCategory.setIfSingleBuy(father.getIfSingleBuy());
        }
        if (categoryDTO.getIfSupportAdditem() == null) {
            goodsCategory.setIfSupportAdditem(father.getIfSupportAdditem());
        }
        if (categoryDTO.getIfComputePorterage() == null) {
            goodsCategory.setIfComputePorterage(father.getIfComputePorterage());
        }
        goodsCategoryBiz.save(goodsCategory, operator);
        //异步缓存
        categoryTreeCacheService.setCategoryTreeCache();
        //插入商品分类基本属性
        batchInsertCategoryAttribute(categoryDTO.getCategoryAttributeDTOS(), operator, goodsCategory.getCategoryType());
    }

    @Override
    public void uptCategory(CategoryDTO categoryDTO, String operator) {
        if (CsStringUtils.isBlank(categoryDTO.getCategoryId())) {
            throw new BizException(GoodsCode.VALUE_NOT_EMPTY, GOODS_CATEGORY_ID);
        }
        if (CsStringUtils.isBlank(categoryDTO.getCategoryName())) {
            throw new BizException(GoodsCode.VALUE_NOT_EMPTY, "商品分类名称");
        }
        GoodsCategory goodsCategory = goodsCategoryBiz.get(categoryDTO.getCategoryId());
        if (!goodsCategory.getParentId().equals(categoryDTO.getParentId())) {
            throw new BizException(GoodsCode.UNDEFINED_ERROR, "商品父级id不能修改");
        }
        if (!goodsCategory.getCategoryCode().equals(categoryDTO.getCategoryCode())) {
            throw new BizException(GoodsCode.UNDEFINED_ERROR, "商品分类编码不能修改");
        }
        if (goodsCategory.getCategoryType().intValue() != categoryDTO.getCategoryType().intValue()) {
            throw new BizException(GoodsCode.UNDEFINED_ERROR, "商品类型不能修改");
        }
        String origin = goodsCategory.getCategoryName();
        //更新商品分类
        BeanUtils.copyProperties(categoryDTO, goodsCategory);
        goodsCategory.setCategoryName(categoryDTO.getCategoryName());
        goodsCategory.setImgs(categoryDTO.getImgs());
        goodsCategory.setAppImgs(categoryDTO.getAppImgs());
        goodsCategory.setMiniImgs(categoryDTO.getMiniImgs());
        goodsCategoryBiz.save(goodsCategory, operator);
        // 分类名称变更时 修改关系表冗余字段
        if (!Objects.equals(origin, categoryDTO.getCategoryName())) {
            String categoryId = goodsCategory.getCategoryId();
            GoodCategoryRelation relation = new GoodCategoryRelation();
            relation.setCategoryId(categoryId);
            for (GoodCategoryRelation goodCategoryRelation : goodCategoryRelationMapper.select(relation)) {
                goodCategoryRelation.setCategoryName(goodsCategory.getCategoryName());
                String[] fullIds = goodCategoryRelation.getCategoryString().split(",");
                String[] fullNames = goodCategoryRelation.getCategoryFullName().split(",");
                for (int i = 0; i < fullIds.length; i++) {
                    if (Objects.equals(fullIds[i], goodsCategory.getCategoryId())) {
                        fullNames[i] = goodsCategory.getCategoryName();
                    }
                }
                goodCategoryRelation.setCategoryFullName(String.join(",", fullNames));
                goodCategoryRelationMapper.updateByPrimaryKey(goodCategoryRelation);
            }
        }
        //异步缓存
        categoryTreeCacheService.setCategoryTreeCache();
        //更新商品分类基本属性
        batchInsertCategoryAttribute(categoryDTO.getCategoryAttributeDTOS(), operator, goodsCategory.getCategoryType());
    }

    @Override
    public CategoryDTO getCategory() {
        CategoryDTO categoryDTO = new CategoryDTO();
        List<CategoryAttributeDTO> categoryAttributeDTOS = Lists.newArrayList();
        Condition conditionAll = new Condition(GoodsCategoryAttribute.class);
        Criteria criteriaAll = conditionAll.createCriteria();
        criteriaAll.andEqualTo(DEL_FLG, false);
        criteriaAll.andEqualTo("spu", true);
        criteriaAll.andIsNull(CATEGORY_TYPE);
        conditionAll.orderBy("sort").asc();
        List<GoodsCategoryAttribute> goodsCategoryAttributeAllList = goodsCategoryAttrBiz.findByCondition(conditionAll);
        if (CollectionUtils.isNotEmpty(goodsCategoryAttributeAllList)) {
            goodsCategoryAttributeAllList.forEach(attribute -> {
                CategoryAttributeDTO categoryAttrDTO = new CategoryAttributeDTO();
                BeanUtils.copyProperties(attribute, categoryAttrDTO);
                //属性值填充
                List<CategoryAttributeValueDTO> categoryAttributeValueDTOS = Lists.newArrayList();
                Condition attrValueCondition = new Condition(GoodsCategoryAttributeValue.class);
                Criteria attrValueConditionCriteria = attrValueCondition.createCriteria();
                attrValueConditionCriteria.andEqualTo(DEL_FLG, false);
                attrValueConditionCriteria.andEqualTo("attributeId", attribute.getCategoryAttributeId());
                attrValueCondition.orderBy("sort").asc();
                List<GoodsCategoryAttributeValue> categoryAttributeValues = goodsCategoryAttrValueBiz.findByCondition(attrValueCondition);
                categoryAttributeValues.forEach(goodsCategoryAttributeValue -> {
                    CategoryAttributeValueDTO categoryAttributeValueDTO = new CategoryAttributeValueDTO();
                    BeanUtils.copyProperties(goodsCategoryAttributeValue, categoryAttributeValueDTO);
                    categoryAttributeValueDTOS.add(categoryAttributeValueDTO);
                });
                categoryAttrDTO.setCategoryAttributeValueDTOs(categoryAttributeValueDTOS);
                //属性值填充-end
                categoryAttributeDTOS.add(categoryAttrDTO);
                categoryDTO.setCategoryAttributeDTOS(categoryAttributeDTOS);
            });
        }
        return categoryDTO;
    }

    /**
     * 删除商品分类检测
     */
    private void checkDelCategory(GoodsCategory goodsCategory) {
        Condition condition = new Condition(GoodsCategory.class);
        Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(PARENT_ID, goodsCategory.getCategoryId());
        criteria.andEqualTo(DEL_FLG, false);
        List<GoodsCategory> categories = goodsCategoryBiz.findByCondition(condition);
        if (CollectionUtils.isNotEmpty(categories)) {
            throw new BizException(GoodsCode.UNDEFINED_ERROR, "该商品分类包含子分类，不允许删除！");
        }
        List<GoodCategoryRelation> relations = goodCategoryRelationMapper.findGoodCategoryRelationByCategoryId(goodsCategory.getCategoryId());
        if (CollectionUtils.isNotEmpty(relations)) {
            throw new BizException(GoodsCode.UNDEFINED_ERROR, "该商品分类已创建商品，不允许删除！");
        }
    }

    @Override
    public void delCategory(String categoryId, String operator) {
        if (CsStringUtils.isBlank(categoryId)) {
            throw new BizException(GoodsCode.VALUE_NOT_EMPTY, GOODS_CATEGORY_ID);
        }
        if (CsStringUtils.isBlank(operator)) {
            throw new BizException(GoodsCode.VALUE_NOT_EMPTY, "操作人");
        }
        GoodsCategory goodsCategory = goodsCategoryBiz.get(categoryId);
        if (goodsCategory == null) {
            throw new BizException(GoodsCode.DATA_NOT_FOUND, "商品分类");
        }
        //商品分类删除检测
        checkDelCategory(goodsCategory);
        goodsCategory.setDelFlg(true);
        goodsCategoryBiz.save(goodsCategory, operator);
        //异步缓存
        categoryTreeCacheService.setCategoryTreeCache();
    }

    @Override
    public void refreshCategoryTreeCache(String operator) {
        log.info("refreshCategoryTreeCache operator:{}", operator);
        categoryTreeCacheService.setCategoryTreeCache();
    }

    @Override
    public List<CategoryTreeDTO> findCategoryTree(String categoryCode) {
        //获取缓存
        CategoryTreeCache categoryTreeCache = categoryTreeCacheService.getCategoryTreeCache();
        return categoryTreeCache.getList();
    }

    @Override
    public List<CategoryTreeDTO> findCategoryTreeByCode(String categoryCode) {
        //
        if (CsStringUtils.isNullOrBlank(categoryCode)) {
            throw new BizException(GoodsCode.VALUE_NOT_EMPTY, GOODS_CATEGORY_CODE);
        }
        //初始化结果
        List<CategoryTreeDTO> list = Lists.newArrayList();
        Condition condition = new Condition(GoodsCategory.class);
        Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(DEL_FLG, false);
        criteria.andEqualTo(CATEGORY_CODE, categoryCode);
        condition.orderBy(CATEGORY_CODE).asc();
        List<GoodsCategory> fathers = goodsCategoryBiz.findByCondition(condition);
        if (CollectionUtils.isNotEmpty(fathers)) {
            fathers.forEach(father -> {
                CategoryTreeDTO dto = new CategoryTreeDTO();
                BeanUtils.copyProperties(father, dto);
                childBuild(father, dto);
                list.add(dto);
            });
        }
        return list;
    }

    @Override
    public HomeMenuCategoryTreeDTO homeMenuCategoryTree(String code) {
        if (CsStringUtils.isNullOrBlank(code)) {
            throw new BizException(GoodsCode.VALUE_NOT_EMPTY, GOODS_CATEGORY_CODE);
        }
        HomeMenuCategoryTreeDTO homeMenuCategoryTreeDTO = new HomeMenuCategoryTreeDTO();
        //获取缓存
        CategoryTreeCache categoryTreeCache = categoryTreeCacheService.getCategoryTreeCache();
        List<CategoryTreeDTO> fathers = categoryTreeCache.getList();
        if (CollectionUtils.isEmpty(fathers)) {
            return homeMenuCategoryTreeDTO;
        }
        for (CategoryTreeDTO father : fathers) {
            List<CategoryTreeDTO> childs = father.getChilds();
            if (CollectionUtils.isEmpty(childs)) {
                continue;
            }
            for (CategoryTreeDTO child : childs) {
                if ("002001".equals(child.getCategoryCode())) {
                    homeMenuCategoryTreeDTO.setGoodsCategorys(child.getChilds());
                } else if ("002002".equals(child.getCategoryCode())) {
                    homeMenuCategoryTreeDTO.setPurchaseCategorys(child.getChilds());
                }
            }
        }
        return homeMenuCategoryTreeDTO;
    }

    @Override
    public List<CategoryTreeDTO> goodsCategoryTree(String code) {
        if (CsStringUtils.isNullOrBlank(code)) {
            throw new BizException(GoodsCode.VALUE_NOT_EMPTY, GOODS_CATEGORY_CODE);
        }
        List<CategoryTreeDTO> list = Lists.newArrayList();
        //获取缓存
        CategoryTreeCache categoryTreeCache = categoryTreeCacheService.getCategoryTreeCache();
        List<CategoryTreeDTO> fathers = categoryTreeCache.getList();
        if (CollectionUtils.isEmpty(fathers)) {
            return list;
        }
        for (CategoryTreeDTO father : fathers) {
            List<CategoryTreeDTO> childs = father.getChilds();
            if (CollectionUtils.isNotEmpty(childs)) {
                for (CategoryTreeDTO child : childs) {
                    if ("002001".equals(child.getCategoryCode())) {
                        list = child.getChilds();
                    }
                }
            }
        }
        return list;
    }

    @Override
    public List<CategoryTreeDTO> purchaseCategoryTree(String code) {
        if (CsStringUtils.isNullOrBlank(code)) {
            throw new BizException(GoodsCode.VALUE_NOT_EMPTY, GOODS_CATEGORY_CODE);
        }
        List<CategoryTreeDTO> list = Lists.newArrayList();
        //获取缓存
        CategoryTreeCache categoryTreeCache = categoryTreeCacheService.getCategoryTreeCache();
        List<CategoryTreeDTO> fathers = categoryTreeCache.getList();
        if (CollectionUtils.isEmpty(fathers)) {
            return list;
        }
        for (CategoryTreeDTO father : fathers) {
            List<CategoryTreeDTO> childs = father.getChilds();
            if (CollectionUtils.isNotEmpty(childs)) {
                for (CategoryTreeDTO child : childs) {
                    if ("002002".equals(child.getCategoryCode())) {
                        list = child.getChilds();
                    }
                }
            }
        }
        return list;
    }

    /**
     * 构建子分类
     */
    public void childBuild(GoodsCategory father, CategoryTreeDTO dto) {
        Condition condition = new Condition(GoodsCategory.class);
        Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(DEL_FLG, false);
        criteria.andEqualTo(PARENT_ID, father.getCategoryId());
        condition.orderBy(CATEGORY_CODE).asc();
        List<GoodsCategory> childCategories = goodsCategoryBiz.findByCondition(condition);
        List<CategoryTreeDTO> childs = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(childCategories)) {
            childCategories.forEach(child -> {
                CategoryTreeDTO childDto = new CategoryTreeDTO();
                BeanUtils.copyProperties(child, childDto);
                childDto.setParentName(father.getCategoryName());
                childs.add(childDto);
                childBuild(child, childDto);
            });
            dto.setChilds(childs);
        }
    }


    @Override
    public List<GoodsCategoryDTO> findCategoryList(String name) {
        List<GoodsCategoryDTO> categDtoList = null;
        Condition condition = new Condition(GoodsCategory.class);
        Criteria criteria = condition.createCriteria();
        if (!CsStringUtils.isBlank(name)) {
            criteria.andLike("categoryName", "%" + name + "%");
        }
        criteria.andNotEqualTo(DEL_FLG, CommonConstants.DEL_FLAG_TRUE);
        condition.orderBy(CATEGORY_CODE).asc();

        List<GoodsCategory> categorys = goodsCategoryBiz.findByCondition(condition);
        if (CollectionUtils.isNotEmpty(categorys)) {
            categDtoList = new ArrayList<GoodsCategoryDTO>();
            for (GoodsCategory cate : categorys) {
                categDtoList.add(BeanConvertUtils.convert(cate, GoodsCategoryDTO.class));
            }
        }
        return categDtoList;
    }

    @Override
    public List<GoodsCategoryDTO> findCategoryListByParent(String parentId) {
        List<GoodsCategoryDTO> categDtoList = null;
        Condition condition = new Condition(GoodsCategory.class);
        Criteria criteria = condition.createCriteria();
        if (!CsStringUtils.isBlank(parentId)) {
            criteria.andEqualTo(PARENT_ID, parentId);
        }
        criteria.andEqualTo(DEL_FLG, CommonConstants.DEL_FLAG_FALSE);
        condition.orderBy(CATEGORY_TYPE).asc();

        List<GoodsCategory> categorys = goodsCategoryBiz.findByCondition(condition);
        if (CollectionUtils.isNotEmpty(categorys)) {
            categDtoList = new ArrayList<GoodsCategoryDTO>();
            for (GoodsCategory cate : categorys) {
                categDtoList.add(BeanConvertUtils.convert(cate, GoodsCategoryDTO.class));
            }
        }
        return categDtoList;
    }

    /**
     * 生成编码 格式001001001
     *
     * @param parentId
     * @return
     */
    public String generateCategoryCode(String parentId) {
        String code = "";
        int baseLeng = 3;
        int parentCodeLeng = 0;
        String parentCode = "";
        if (!CsStringUtils.isBlank(parentId)) {
            GoodsCategory parentCategory = goodsCategoryBiz.get(parentId);
            parentCode = parentCategory.getCategoryCode();
            parentCodeLeng = parentCategory.getCategoryCode().length();
        }

        String codeNumKey = "parentCategory_" + parentCode;
        Long codeNum = codeNumCacheService.getIncrCodeNum(codeNumKey);
        if (codeNum == null) {
            /**
             * 取得当前最大的code
             */
            Condition condition = new Condition(GoodsCategory.class);
            Criteria criteria = condition.createCriteria();
            criteria.andCondition("LENGTH(category_code) = " + (parentCodeLeng + baseLeng));
            condition.orderBy(CATEGORY_CODE).desc();
            List<GoodsCategory> categorys = goodsCategoryBiz.findByCondition(condition);
            if (CollectionUtils.isNotEmpty(categorys)) {
                String maxCode = categorys.get(0).getCategoryCode();
                codeNumCacheService.initCodeNum(codeNumKey, Long.parseLong(maxCode.substring(parentCodeLeng)));
            } else {
                codeNumCacheService.initCodeNum(codeNumKey, 0L);
            }
            codeNum = codeNumCacheService.getIncrCodeNum(codeNumKey);
        }

        code = parentCode + CsStringUtils.leftPad(codeNum + "", baseLeng, "0");
        return code;
    }

    @Override
    public List<GoodsCategoryDTO> findCategoryAttrList(GoodsCategoryAttrDTO queryDTO) {
        GoodsCategoryAttribute query = BeanConvertUtils.convert(queryDTO, GoodsCategoryAttribute.class);
        List<GoodsCategoryAttribute> categoryAttrList = goodsCategoryAttrBiz.find(query);
        Map<String, GoodsCategoryDTO> categoryAttrMap = Maps.newHashMap();
        for (GoodsCategoryAttribute categoryAttr : categoryAttrList) {
            String key = categoryAttr.getCategoryId() + "#" + categoryAttr.getCategoryType();
            GoodsCategoryDTO category = categoryAttrMap.get(key);
            if (category == null) {
                category = new GoodsCategoryDTO();
                category.setCategoryId(categoryAttr.getCategoryId());
                category.setCategoryType(categoryAttr.getCategoryType());
                category.setCategoryName(categoryAttr.getCategoryName());
                category.setCategoryAttrs(Lists.newArrayList());
            }

            GoodsCategoryAttrDTO attrDTO = BeanConvertUtils.convert(categoryAttr, GoodsCategoryAttrDTO.class);
            if (queryDTO.isSearchValue()) {
                List<GoodsAttributeDTO> valueList = goodsBiz.getAttrValListByAttrName(
                        categoryAttr.getAttriName(), categoryAttr.getCategoryType());//属性值
                attrDTO.setValueList(valueList);
            }

            List<GoodsCategoryAttrDTO> aList = category.getCategoryAttrs();
            aList.add(attrDTO);

            categoryAttrMap.put(key, category);
        }

        List<GoodsCategoryDTO> resault = categoryAttrMap.values().stream()
                .sorted(Comparator.comparing(GoodsCategoryDTO::getCategoryType))
                .toList();

        return resault;
    }

    @PostConstruct
    public void init() {
        codeNumCacheService.clearAllCodeNum();
    }

    @Override
    public Boolean ifSupportAdditem(String goodsId) {
        return false;
    }

    @Override
    public Boolean ifComputePorterage(String goodsId) {
        return false;
    }

    @Override
    public Boolean ifSingleBuy(String goodsId) {
        return false;
    }

    @Override
    public Boolean isSellerConfirm(String goodsId) {
        return false;
    }

    @Override
    public Boolean getSplitBillNode(String goodsId) {
        return false;
    }

    @Override
    public List<GoodsCategorySimpleDTO> findCategorySimpleList(Set<String> categoryCodeSet) {
        return goodsCategoryBiz.findCategorySimpleList(categoryCodeSet);
    }

    @Override
    public GoodsCategoryDTO getGoodsCategoryByGoodsId(String goodsId) {
        return goodsCategoryBiz.getGoodsCategoryByGoodsId(goodsId);
    }

}
