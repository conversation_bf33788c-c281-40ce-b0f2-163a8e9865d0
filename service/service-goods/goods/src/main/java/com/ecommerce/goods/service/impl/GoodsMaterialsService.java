package com.ecommerce.goods.service.impl;

import com.ecommerce.goods.api.dto.MaterialsDTO;
import com.ecommerce.goods.api.dto.PageSellerMaterialsDTO;
import com.ecommerce.goods.biz.IGoodsMaterialsBiz;
import com.ecommerce.goods.service.IGoodsMaterialsService;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class GoodsMaterialsService implements IGoodsMaterialsService{

    @Autowired
    private IGoodsMaterialsBiz goodsMaterialsBiz;

    @Override
    public void syncGoodsMaterials(String sellerId, String operator) {
        goodsMaterialsBiz.syncGoodsMaterials(sellerId,operator);
    }

    @Override
    public PageInfo<MaterialsDTO> pageSellerMaterials(PageSellerMaterialsDTO pageSellerMaterialsDTO) {
        return goodsMaterialsBiz.pageSellerMaterials(pageSellerMaterialsDTO);
    }

    @Override
    public List<MaterialsDTO> getSellerMaterialsList(String sellerId) {
        return goodsMaterialsBiz.getSellerMaterialsList(sellerId);
    }

    @Override
    public MaterialsDTO getMaterialsById(String materialsId) {
        return goodsMaterialsBiz.getMaterialsById(materialsId);
    }

    @Override
    public MaterialsDTO getMaterialsByCode(String commodityCode) {
        return goodsMaterialsBiz.getMaterialsByCode(commodityCode);
    }

    @Override
    public MaterialsDTO getMaterialsCodeByGoods(String goodsId) {
        return goodsMaterialsBiz.getMaterialsCodeByGoods(goodsId);
    }

    @Override
    public MaterialsDTO getMaterialsCodeByResource(String resourceId) {
        return goodsMaterialsBiz.getMaterialsCodeByResource(resourceId);
    }
}
