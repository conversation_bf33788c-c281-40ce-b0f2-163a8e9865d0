package com.ecommerce.goods.service.impl;

import com.alibaba.fastjson.JSON;
import com.ecommerce.base.api.dto.ReceivingAddressDTO;
import com.ecommerce.base.api.service.IReceivingAddressService;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.goods.api.dto.DynamicAttributeDTO;
import com.ecommerce.goods.api.dto.SpecialGoodsAttributeDTO;
import com.ecommerce.goods.api.dto.base.ContractAddressDTO;
import com.ecommerce.goods.api.dto.contract.*;
import com.ecommerce.goods.api.dto.order.OrderSaleRegionDTO;
import com.ecommerce.goods.api.enums.DynamicAttributeEnum;
import com.ecommerce.goods.biz.IOrderPrepareBiz;
import com.ecommerce.goods.service.IGoodsService;
import com.ecommerce.goods.service.IOrderPrepareService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class OrderPrepareService implements IOrderPrepareService {

    @Autowired
    private IOrderPrepareBiz orderPrepareBiz;
    @Autowired
    private IGoodsService goodsService;
    @Autowired
    protected IReceivingAddressService receivingAddressService;

    @Override
    public String addOrderPrepare(List<OrderPrepareDTO> orderPrepareDTOS) {
        return orderPrepareBiz.addOrderPrepare(orderPrepareDTOS);
    }

    @Override
    public List<OrderPrepareDTO> queryOrderPrepare(String key) {
        return orderPrepareBiz.queryOrderPrepare(key);
    }

    @Override
    public String addCementPrepare(CementPrepareDTO cementPrepareDTO) {
        return orderPrepareBiz.addCementPrepare(cementPrepareDTO);
    }

    @Override
    public CementPrepareDTO queryCementPrepare(String key) {
        return orderPrepareBiz.queryCementPrepare(key);
    }

    @Override
    public String addRebarPrepare(RebarPrepareDTO rebarPrepareDTO) {
        return orderPrepareBiz.addRebarPrepare(rebarPrepareDTO);
    }

    @Override
    public RebarPrepareDTO queryRebarPrepare(String key) {
        return orderPrepareBiz.queryRebarPrepare(key);
    }

    @Override
    public String addRebarTypePrepare(RebarTypePrepareDTO rebarTypePrepareDTO) {
        return orderPrepareBiz.addRebarTypePrepare(rebarTypePrepareDTO);
    }

    @Override
    public RebarTypePrepareDTO queryRebarTypePrepare(String key) {
        return orderPrepareBiz.queryRebarTypePrepare(key);
    }

    @Override
    public OrderSaleRegionDTO getOrderSaleRegionIds(String contractId, String cartId)
    {
        return orderPrepareBiz.getOrderSaleRegionIds(contractId, cartId);
    }

    @Override
    public String addOrderPrepareCommon(List<OrderPrepareCommonDTO> list) {
        for (OrderPrepareCommonDTO orderPrepareCommonDTO:list) {
            DynamicAttributeDTO dynamicAttributeDTO = goodsService.findDynamicAttribute(orderPrepareCommonDTO.getGoodsId());
            orderPrepareCommonDTO.setNeedSalesman(dynamicAttributeDTO.check(DynamicAttributeEnum.SALESMAN.getCode()));
            orderPrepareCommonDTO.setNeedSellerConfirm(goodsService.isSellerConfirm(orderPrepareCommonDTO.getGoodsId()));
            orderPrepareCommonDTO.setSupportStoreDiscount(dynamicAttributeDTO.check(DynamicAttributeEnum.STORE_DISCOUNT.getCode()));
            orderPrepareCommonDTO.setDeliveryTimeType(dynamicAttributeDTO.getValue(DynamicAttributeEnum.DELIVERY_TIME_TYPE.getCode()));
            SpecialGoodsAttributeDTO specialGoodsAttributeDTO = goodsService.getSpecialGoodsAttribute(orderPrepareCommonDTO.getGoodsId());
            orderPrepareCommonDTO.setConcreteFlag(specialGoodsAttributeDTO.getConcreteFlag());
            orderPrepareCommonDTO.setAddItemFlag(specialGoodsAttributeDTO.getAddItemFlag());
            orderPrepareCommonDTO.setSupportCarryFlag(specialGoodsAttributeDTO.getSupportCarryFlag());
        }
        return orderPrepareBiz.addOrderPrepareCommon(list);
    }

    @Override
    public List<OrderPrepareCommonDTO> queryOrderPrepareCommon(String key) {
        List<OrderPrepareCommonDTO> commonList = orderPrepareBiz.queryOrderPrepareCommon(key);
        if (CollectionUtils.isNotEmpty(commonList) && CsStringUtils.isNotEmpty(commonList.get(0).getAddressId())) {
            //获取合同地址
            ReceivingAddressDTO receivingAddressDTO = receivingAddressService.findById(commonList.get(0).getAddressId());
            if (receivingAddressDTO == null) {
                throw new BizException(BasicCode.DATA_NOT_EXIST, "地址ID:" + commonList.get(0).getAddressId());
            }
            ContractAddressDTO contractAddress = new ContractAddressDTO();
            BeanUtils.copyProperties(receivingAddressDTO, contractAddress);
            commonList.get(0).setContractAddressDTO(contractAddress);
        }
        log.info("commonList:" + JSON.toJSONString(commonList));
        return commonList;
    }
}
