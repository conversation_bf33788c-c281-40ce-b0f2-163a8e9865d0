package com.ecommerce.goods.service.impl;

import com.ecommerce.common.annotation.AddLog;
import com.ecommerce.goods.biz.IResourceNumTCCBiz;
import com.ecommerce.goods.service.IResourceNumTCCService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;

/**
 * ResourceNumTCCService
 *
 * <AUTHOR>
 */
@Service
public class ResourceNumTCCService implements IResourceNumTCCService {

    @Autowired
    private IResourceNumTCCBiz iResourceNumTCCBiz;

    @Override
    public void checkResourceNumCert() {
        //
    }

    @AddLog(operatorIndex = 2)
    @Override
    public void tryResourceNumResourceToOrder(HashMap<String, BigDecimal> map, String transactionId, String operator, String objectId) {
        iResourceNumTCCBiz.tryResourceNumResourceToOrder(map,transactionId,operator,objectId);
    }

    @AddLog(operatorIndex = 1)
    @Override
    public void confirmResourceNumResourceToOrder(String transactionId, String operator, String objectId) {
        iResourceNumTCCBiz.confirmResourceNumResourceToOrder(transactionId,operator,objectId);
    }

    @AddLog(operatorIndex = 1)
    @Override
    public void cancelResourceNumResourceToOrder(String transactionId, String operator, String objectId) {
        iResourceNumTCCBiz.cancelResourceNumResourceToOrder(transactionId,operator,objectId);
    }

    @AddLog(operatorIndex = 2)
    @Override
    public void tryResourceNumOrderToResource(HashMap<String, BigDecimal> map, String transactionId, String operator, String objectId) {
        iResourceNumTCCBiz.tryResourceNumOrderToResource(map, transactionId, operator,objectId);
    }

    @AddLog(operatorIndex = 1)
    @Override
    public void confirmResourceNumOrderToResource(String transactionId, String operator, String objectId) {
        iResourceNumTCCBiz.confirmResourceNumOrderToResource(transactionId, operator,objectId);
    }

    @AddLog(operatorIndex = 1)
    @Override
    public void cancelResourceNumOrderToResource(String transactionId, String operator, String objectId) {
        iResourceNumTCCBiz.cancelResourceNumOrderToResource(transactionId, operator,objectId);
    }

    @AddLog(operatorIndex = 2)
    @Override
    public void tryResourceNumGoodToResource(HashMap<String, BigDecimal> map, String transactionId, String operator, String objectId) {
        iResourceNumTCCBiz.tryResourceNumGoodToResource(map, transactionId, operator,objectId);
    }

    @AddLog(operatorIndex = 1)
    @Override
    public void confirmResourceNumGoodToResource(String transactionId, String operator, String objectId) {
        iResourceNumTCCBiz.confirmResourceNumGoodToResource(transactionId, operator,objectId);
    }

    @AddLog(operatorIndex = 1)
    @Override
    public void cancelResourceNumGoodToResource(String transactionId, String operator, String objectId) {
        iResourceNumTCCBiz.cancelResourceNumGoodToResource(transactionId, operator,objectId);
    }

    @AddLog(operatorIndex = 2)
    @Override
    public void tryResourceNumResourceToGood(HashMap<String, BigDecimal> map, String transactionId, String operator, String objectId) {
        iResourceNumTCCBiz.tryResourceNumResourceToGood(map, transactionId, operator,objectId);
    }

    @AddLog(operatorIndex = 1)
    @Override
    public void confirmResourceNumResourceToGood(String transactionId, String operator, String objectId) {
        iResourceNumTCCBiz.confirmResourceNumResourceToGood(transactionId, operator,objectId);
    }

    @AddLog(operatorIndex = 1)
    @Override
    public void cancelResourceNumResourceToGood(String transactionId, String operator, String objectId) {
        iResourceNumTCCBiz.cancelResourceNumResourceToGood(transactionId, operator,objectId);
    }

	@Override
	public void updateResourceNumForCreateOrder(HashMap<String, BigDecimal> map, String operator) {
		iResourceNumTCCBiz.updateResourceNumForCreateOrder(map, operator);
	}
}
