package com.ecommerce.goods.service.impl;

import com.ecommerce.base.api.common.MessageKey;
import com.ecommerce.goods.api.dto.stock.StockAgentDTO;
import com.ecommerce.goods.api.dto.stock.StockItemDTO;
import com.ecommerce.open.api.dto.MessageConfigCodeEnum;
import com.ecommerce.open.api.dto.SMSMessageDTO;
import com.ecommerce.open.api.service.impl.SMSMessageProducer;
import com.ecommerce.open.enums.MessagePushAppNameEnum;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.RoundingMode;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ThreadPoolExecutor;


@Slf4j
@Service
public class SMSMessageProducerImpl extends SMSMessageProducer {

    private static final String APPLICATION_NAME = "applicationName";

    @Autowired
    private ThreadPoolExecutor threadPoolExecutor;

    @Override
    public SMSMessageDTO object2Message(Object o) {
        return null;
    }

    //卖家的销售经理
    //卖家的销售经理	【大电商】您挂牌的商品{goodsName}已被平台管理员强制下架，您可登录平台查看详情，如有疑问，请联系平台客服热线400-[hotline]。
    public void sendMessageForOffSaleResourcePlatform(String sellerId,String goodsName,String saleRegionId){
        SMSMessageDTO messageDTO = new SMSMessageDTO();
        //短信模板编号 MessageConfigCodeEnum
        messageDTO.setMsgconfigCode(MessageConfigCodeEnum.RESOURCE_FORCE_OFF_SALE.getCode());
        //卖家id 消息处理时通过卖家id查找卖家经理
        messageDTO.addContextValue(MessageKey.SELLER_ID,sellerId);
        //销售区域，用于人员查找过滤
        if (CsStringUtils.isNotBlank(saleRegionId)) {
            messageDTO.addContextValue(MessageKey.SALE_REGION_ID, saleRegionId);
        }
        messageDTO.addTemplateParams("goodsName", CsStringUtils.isBlank(goodsName) ? "" : goodsName);
        messageDTO.addTemplateParams(APPLICATION_NAME,MessagePushAppNameEnum.SELLER_APP.getCode());
        log.info("============》商品强制下架消息:{}",messageDTO.toString());
        sendMessage(messageDTO);
    }

    //企业买家的采购人员 已同前端海心确认，发给买家拥有采购员角色的人（一个或者多个）
    //卖家{sellerName}已对您的合同{contractId}进行了新增或修改，您可通过该合同进行下单，您可登录平台查看详情。
    public void sendMessageForContractChange(String buyerId,String sellerName,String contractId,String contractNumber){
        SMSMessageDTO messageDTO = new SMSMessageDTO();
        //短信模板编号 MessageConfigCodeEnum
        messageDTO.setMsgconfigCode(MessageConfigCodeEnum.CONTRACT_CHANGE.getCode());
        //卖家id 消息处理时通过卖家id查找卖家经理
        messageDTO.addContextValue(MessageKey.BUYER_ID,buyerId);

        messageDTO.addTemplateParams("sellerName", sellerName);
        messageDTO.addTemplateParams("contractId", CsStringUtils.isNotBlank(contractNumber) ? contractNumber : contractId);
        messageDTO.addTemplateParams(APPLICATION_NAME,MessagePushAppNameEnum.BUYER_APP.getCode());
        log.info("============》合同信息变更消息:{}",messageDTO.toString());
        sendMessage(messageDTO);
    }

    //卖家的销售经理及此合同绑定销售人员
    //您有[contractSize]份合同于7日内即将到期，请您尽快处理，详情可登录平台查看。
    public void sendMessageForContractExpiredRemind(String sellerId, String sellerSalesman, Integer contractSize,Long remainDays, Set<String> saleRegionIdSet){
        SMSMessageDTO messageDTO = new SMSMessageDTO();
        //短信模板编号 MessageConfigCodeEnum
        messageDTO.setMsgconfigCode(MessageConfigCodeEnum.CONTRACT_EXPIRED_REMIND.getCode());
        //卖家id 消息处理时通过卖家id查找卖家经理
        messageDTO.addContextValue(MessageKey.SELLER_ID,sellerId);
        messageDTO.addContextValue(MessageKey.SELLER_SALES_PERSON_ID,sellerSalesman);
        //销售区域，用于销售经理查找过滤
        if(CollectionUtils.isNotEmpty(saleRegionIdSet)) {
            messageDTO.addContextValue(MessageKey.SALE_REGION_ID_LIST, saleRegionIdSet);
        }
        messageDTO.addTemplateParams("contractSize", contractSize+"");
        messageDTO.addTemplateParams("remainDays", remainDays+"");
        messageDTO.addTemplateParams(APPLICATION_NAME,MessagePushAppNameEnum.SELLER_APP.getCode());
        log.info("============》合同信息变更消息:{}",messageDTO.toString());
        sendMessage(messageDTO);
    }

//    新增短信模板：合同批量调价方案执行前6小时提醒
//    模板内容：你的交易订单{order1,order2,order3...}于{hour}小时内即将失效，请您尽快处理，详情可登录平台查看
//    接收对象：买家订单创建人或（如果是代客下单，则为买家下单员角色(采购员)对应的人）
//    发送方式：短信、站内信、app推送
//    腾讯短信模板id： 649189
    public void sendMessageForContractBatchAdjustPriceRemind(String buyerId,String buyerPurchaserAccountId, List<String> orderCodes, Integer hours){
        SMSMessageDTO messageDTO = new SMSMessageDTO();
        //短信模板编号 MessageConfigCodeEnum
        messageDTO.setMsgconfigCode(MessageConfigCodeEnum.CONTRACT_BATCH_ADJUST_PRICE_REMIND.getCode());
        //卖家id 消息处理时通过卖家id查找卖家经理
        messageDTO.addContextValue(MessageKey.BUYER_ID,buyerId);
        if (CsStringUtils.isNotBlank(buyerPurchaserAccountId)) {
            messageDTO.addContextValue(MessageKey.BUYER_PURCHASER_ACCOUNT_ID, buyerPurchaserAccountId);
        }
        messageDTO.addTemplateParams("orderCode", CsStringUtils.join(orderCodes, ","));
        messageDTO.addTemplateParams("hours", hours+"");
        messageDTO.addTemplateParams(APPLICATION_NAME,MessagePushAppNameEnum.BUYER_APP.getCode());
        log.info("============》合同批量调价提醒消息:{}",messageDTO.toString());
        sendMessage(messageDTO);
    }

//    新增短信模板：厂家库存分配新增、修改 提醒买家
//    模板内容：您{year}年{month}月获得卖家：{seller}的{goodsName}商品的最新可购库存为{quantity}吨，请尽快购买。如有疑问，请联系卖家。
//    接收对象：买家主账号
//    发送方式：短信
//    腾讯短信模板id： 700568
    public void sendMessageForStockRemind(String allocationDate,String sellerName, String goodsName, Collection<StockAgentDTO> list){
        if(CollectionUtils.isEmpty(list)){
            log.info("agent list is empty.");
            return;
        }
        threadPoolExecutor.execute(()->{
            try{
                for (StockAgentDTO stockAgent : list) {
                    if(StockItemDTO.OTHER.equals(stockAgent.getBuyerId())){
                        continue;
                    }
                    SMSMessageDTO messageDTO = new SMSMessageDTO();
                    //短信模板编号 MessageConfigCodeEnum
                    messageDTO.setMsgconfigCode(MessageConfigCodeEnum.SELLER_STOCK_REMIND.getCode());
                    messageDTO.addTemplateParams("year", allocationDate.substring(0,4));
                    messageDTO.addTemplateParams("month", Integer.valueOf(allocationDate.substring(4))+"");
                    messageDTO.addTemplateParams("seller", sellerName);
                    messageDTO.addTemplateParams("goodsName", goodsName);
                    messageDTO.addTemplateParams("quantity", stockAgent.getQuantityAllocation().setScale(2, RoundingMode.HALF_UP).toPlainString());
                    messageDTO.addContextValue(MessageKey.MEMBER_ID_LIST, Lists.newArrayList(stockAgent.getBuyerId()));
                    log.info("============》厂家库存分配调价提醒消息:{}",messageDTO.toString());
                    sendMessage(messageDTO);
                }
            }catch (Exception e){
                log.error(e.getMessage(),e);
            }
        });

    }
}
