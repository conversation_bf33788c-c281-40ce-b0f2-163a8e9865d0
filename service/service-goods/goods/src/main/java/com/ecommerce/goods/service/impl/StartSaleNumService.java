package com.ecommerce.goods.service.impl;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.goods.api.dto.PageStartSaleNumDTO;
import com.ecommerce.goods.api.dto.QueryStartSaleNumDTO;
import com.ecommerce.goods.api.dto.StartSaleNumDTO;
import com.ecommerce.goods.biz.IStartSaleNumBiz;
import com.ecommerce.goods.dao.vo.StartSaleNum;
import com.ecommerce.goods.service.IStartSaleNumService;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class StartSaleNumService implements IStartSaleNumService {

    @Autowired
    private IStartSaleNumBiz startSaleNumBiz;

    @Override
    public ItemResult<Void> addStartSaleNum(StartSaleNumDTO startSaleNumDTO, String operator) {
        StartSaleNum startSaleNum = dto2StartSaleNumVo(startSaleNumDTO);
        startSaleNumBiz.add(startSaleNum,operator);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> updateStartSaleNum(StartSaleNumDTO startSaleNumDTO, String operator) {
        StartSaleNum startSaleNum = dto2StartSaleNumVo(startSaleNumDTO);
        startSaleNumBiz.update(startSaleNum,operator);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> deleteStartSaleNum(String startSaleId, String operator) {
        StartSaleNum startSaleNum = startSaleNumBiz.get(startSaleId);
        if(startSaleNum != null){
            startSaleNum.setDelFlg(1);
            startSaleNum.setUpdateUser(operator);
            startSaleNum.setUpdateTime(new Date());
            startSaleNumBiz.updateSelective(startSaleNum);
        }
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<StartSaleNumDTO> findById(String startSaleId) {
        StartSaleNum startSaleNum = startSaleNumBiz.get(startSaleId);
        return new ItemResult<>(vo2StartSaleNumDTO(startSaleNum));
    }

    @Override
    public ItemResult<List<StartSaleNumDTO>> findByQuery(QueryStartSaleNumDTO queryStartSaleNumDTO) {
        List<StartSaleNumDTO> list = new ArrayList<>();
        List<StartSaleNum> startSaleNumList = startSaleNumBiz.findByQuery(queryStartSaleNumDTO);
        if(startSaleNumList != null && !startSaleNumList.isEmpty()){
            list = startSaleNumList.stream().map(item -> vo2StartSaleNumDTO(item)).toList();
        }
        return new ItemResult<>(list);
    }

    @Override
    public ItemResult<PageInfo<StartSaleNumDTO>> pageStartSaleNum(PageStartSaleNumDTO pageStartSaleNumDTO) {
        PageInfo<StartSaleNumDTO> pageInfo = new PageInfo<>();
        PageInfo<StartSaleNum> result = startSaleNumBiz.pageStartSaleNum(pageStartSaleNumDTO);
        if(result != null){
            BeanUtils.copyProperties(result,pageInfo);
            if( result.getList() != null && !result.getList().isEmpty() ) {
                pageInfo.setList(result.getList().stream().map(item -> vo2StartSaleNumDTO(item)).toList());
            }
        }
        return new ItemResult<>(pageInfo);
    }

    @Override
    public Boolean isExist(StartSaleNumDTO startSaleNumDTO) {
        StartSaleNum startSaleNum = dto2StartSaleNumVo(startSaleNumDTO);
        return startSaleNumBiz.isExist(startSaleNum);
    }

    private StartSaleNum dto2StartSaleNumVo(StartSaleNumDTO dto){
        StartSaleNum vo = new StartSaleNum();
        if( dto != null ){
            BeanUtils.copyProperties(dto,vo);
        }
        return vo;
    }

    private StartSaleNumDTO vo2StartSaleNumDTO(StartSaleNum startSaleNum){
        if(startSaleNum == null){
            return null;
        }
        StartSaleNumDTO dto = new StartSaleNumDTO();
        BeanUtils.copyProperties(startSaleNum,dto);
        return dto;
    }
}
