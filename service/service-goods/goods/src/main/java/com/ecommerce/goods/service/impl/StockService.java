package com.ecommerce.goods.service.impl;

import com.ecommerce.base.api.dto.authRes.DataPrivCodeEnum;
import com.ecommerce.base.api.dto.saleregion.SaleRegionDTO;
import com.ecommerce.base.api.dto.saleregion.SaleRegionSampleDTO;
import com.ecommerce.base.api.dto.saleregion.SaleStoreDTO;
import com.ecommerce.base.api.dto.warehouse.RefWarehouseQueryDTO;
import com.ecommerce.base.api.dto.warehouse.WarehouseListDTO;
import com.ecommerce.base.api.service.IDatapermService;
import com.ecommerce.base.api.service.ISaleRegionService;
import com.ecommerce.base.api.service.IWarehouseService;
import com.ecommerce.common.annotation.PrintArgs;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.service.lock.RedisLockService;
import com.ecommerce.common.utils.BeanConvertUtils;
import com.ecommerce.common.utils.DateUtil;
import com.ecommerce.goods.api.dto.base.PageQuery;
import com.ecommerce.goods.api.dto.stock.AgentStockInfoDTO;
import com.ecommerce.goods.api.dto.stock.CurrentMonthTookQuantityDTO;
import com.ecommerce.goods.api.dto.stock.CustomerInfoCompletionDTO;
import com.ecommerce.goods.api.dto.stock.CustomerInfoDTO;
import com.ecommerce.goods.api.dto.stock.SaleRegionAndStockItemDTO;
import com.ecommerce.goods.api.dto.stock.SaleRegionAndWarehouseAndStockItemDTO;
import com.ecommerce.goods.api.dto.stock.StockAgentDTO;
import com.ecommerce.goods.api.dto.stock.StockBuyerAndItemDTO;
import com.ecommerce.goods.api.dto.stock.StockChangeDTO;
import com.ecommerce.goods.api.dto.stock.StockCreateDTO;
import com.ecommerce.goods.api.dto.stock.StockCustomerInfoDTO;
import com.ecommerce.goods.api.dto.stock.StockCustomerInfoForUpdateDTO;
import com.ecommerce.goods.api.dto.stock.StockGoodsQueryDTO;
import com.ecommerce.goods.api.dto.stock.StockItemCreateDTO;
import com.ecommerce.goods.api.dto.stock.StockItemDTO;
import com.ecommerce.goods.api.dto.stock.StockItemQueryDTO;
import com.ecommerce.goods.api.dto.stock.StockItemUpdateDTO;
import com.ecommerce.goods.api.dto.stock.StockQueryDTO;
import com.ecommerce.goods.api.dto.stock.StockSellerAndItemDTO;
import com.ecommerce.goods.api.dto.stock.StockSellerDTO;
import com.ecommerce.goods.api.dto.stock.StockTableHeadAndLastMonthDataDTO;
import com.ecommerce.goods.api.dto.stock.StockTableHeadDTO;
import com.ecommerce.goods.api.dto.stock.StockTableHeadQueryDTO;
import com.ecommerce.goods.api.dto.stock.StockTableHeadWarehouseDTO;
import com.ecommerce.goods.api.dto.stock.StockUpdateDTO;
import com.ecommerce.goods.biz.IGoodsBiz;
import com.ecommerce.goods.biz.IGoodsCategoryBiz;
import com.ecommerce.goods.biz.IStockItemBiz;
import com.ecommerce.goods.dao.vo.Goods;
import com.ecommerce.goods.dao.vo.GoodsCategory;
import com.ecommerce.goods.dao.vo.StockItem;
import com.ecommerce.goods.service.IStockService;
import com.ecommerce.member.api.dto.account.AccountDTO;
import com.ecommerce.member.api.dto.account.AccountSimpleDTO;
import com.ecommerce.member.api.dto.member.enums.MemberPlatform;
import com.ecommerce.member.api.service.IAccountService;
import com.ecommerce.order.api.dto.OrderSendQuantityDTO;
import com.ecommerce.order.api.service.IOrderSendQuantityService;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import com.ecommerce.common.utils.CsStringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.Collection;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 库存分配服务
 */
@Slf4j
@Service
public class StockService implements IStockService {

    @Autowired
    private IStockItemBiz stockItemBiz;
    @Autowired
    private IGoodsBiz goodsBiz;
    @Autowired
    private IGoodsCategoryBiz goodsCategoryBiz;
    @Autowired
    private RedisLockService redisLockService;

    @Autowired
    private IDatapermService dataPermService;
    @Autowired
    private ISaleRegionService saleRegionService;
    @Autowired
    private IOrderSendQuantityService orderSendQuantityService;
    @Autowired
    private IWarehouseService warehouseService;
    @Autowired
    private IAccountService accountService;
    @Autowired
    private SMSMessageProducerImpl smsMessageProducer;
    @Autowired
    private ThreadPoolExecutor threadPoolExecutor;


    @Override
    public StockSellerAndItemDTO findBySellerIdAndAllocationDate(String sellerId, String allocationDate, String goodsId, String operatorId) {
        StockSellerAndItemDTO result = new StockSellerAndItemDTO();

        Set<String> saleRegionIds = null;
        if(checkAccountMasterFlag(operatorId)) {
            //查询数据权限
            List<SaleRegionDTO> saleRegionList = saleRegionService.findByAccountId(operatorId);
            saleRegionIds = CollectionUtils.isEmpty(saleRegionList) ? Sets.newHashSet() : saleRegionList.stream().map(SaleRegionSampleDTO::getSaleRegionId).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(saleRegionIds)) {
                log.info("没有找到销售区域权限operatorId:{}", operatorId);
                result.setItemList(Lists.newArrayList());
                result.setStockAgentList(Lists.newArrayList());
                result.setSaleRegionItemList(Lists.newArrayList());
                result.setSaleRegionWarehouseList(Lists.newArrayList());
                result.setError("没有找到销售区域数据权限");
                return result;
            }
        }
        List<StockItem> stockItems = stockItemBiz.findBySellerIdAndAllocationDate(sellerId,allocationDate,goodsId,saleRegionIds);
        List<StockSellerDTO> stockSellerDTOList = summary2Seller(stockItems);
        result.setStockSeller(CollectionUtils.isEmpty(stockSellerDTOList) ? null : stockSellerDTOList.get(0));
        result.setItemList(BeanConvertUtils.convertList(stockItems,StockItemDTO.class));
        result.setStockAgentList(summary2Agent(stockItems));
        result.setSaleRegionItemList(groupBySaleRegion(result.getItemList()));
        result.setSaleRegionWarehouseList(groupBySaleRegionAndWarehouse(result.getItemList()));
        return result;
    }

    @Override
    public StockBuyerAndItemDTO findByBuyerIdAndAllocationDate(String buyerId, String allocationDate, String goodsId, String operatorId) {
        StockBuyerAndItemDTO result = new StockBuyerAndItemDTO();
        result.setItemList(Lists.newArrayList());
        //查询厂家库存分配明细数据
        List<StockItem> stockItems = stockItemBiz.findByBuyerIdAndAllocationDate(buyerId,allocationDate,goodsId);
        boolean other = false;
        if( CollectionUtils.isEmpty(stockItems)){
            stockItems = stockItemBiz.findByBuyerIdAndAllocationDate(StockItemDTO.OTHER,allocationDate,goodsId);
            other = true;
        }
        if( CollectionUtils.isEmpty(stockItems)){
            result.setError("厂家库存分配没有找到对应的经销商商品");
            return result;
        }
        //过滤明细中的厂家仓库
        Set<String> warehouseIds = stockItems.stream().map(StockItem::getWarehouseId).collect(Collectors.toSet());
        log.info("stockItems.size:{},goodsId:{},warehouseIds:{}",stockItems.size(),goodsId,warehouseIds);
        //根据厂家商品找到经销商商品
        List<Goods> refGoods = goodsBiz.findRefGoodsBySellerGoodsIds(Lists.newArrayList(goodsId), buyerId);
        log.info("refGoods.size:{},goodsId:{},buyerId:{}",refGoods.size(),goodsId,buyerId);
        Goods refGood = CollectionUtils.isEmpty(refGoods) ? goodsBiz.get(goodsId) : refGoods.get(0);
        Map<String,String> goodsInfo = Maps.newHashMap();
        goodsInfo.put("pack",refGood.getPack());
        goodsInfo.put("categoryName","");
        GoodsCategory goodsCategory = goodsCategoryBiz.findCategoryByCategoryCode(refGood.getCategoryCode());
        if( goodsCategory != null ) {
            goodsInfo.put("categoryName",goodsCategory.getCategoryName());
        }
        result.setGoodsInfo(goodsInfo);
        //子账号则判断其仓库数据权限
        if(checkAccountMasterFlag(operatorId)) {
            //查询数据权限
            ItemResult<List<String>> storeHouseIds = dataPermService.findByAccountIdAndDataPrivCode(operatorId, DataPrivCodeEnum.STOREHOUSE.getCode());
            if (storeHouseIds == null || CollectionUtils.isEmpty(storeHouseIds.getData())) {
                log.info("没有找到仓库数据权限operatorId:{}", operatorId);
                result.setError("没有找到仓库数据权限");
                return result;
            }
            //取交集，剔除没有数据集权限的仓库
            warehouseIds.retainAll(storeHouseIds.getData());
            if( CollectionUtils.isEmpty(warehouseIds)){
                result.setError("没有仓库数据权限");
                return result;
            }
        }
        for (StockItem stockItem : stockItems) {
            StockItemDTO stockItemDTO = new StockItemDTO();
            BeanUtils.copyProperties(stockItem,stockItemDTO);
            stockItemDTO.setBuyerGoodsId(refGood.getGoodsId());
            stockItemDTO.setBuyerGoodsName(refGood.getGoodsName());
            if(other){
                stockItemDTO.setBuyerId(buyerId);
                stockItemDTO.setBuyerName(null);
            }
            result.getItemList().add(stockItemDTO);
        }
        Map<String,Goods> refGoodsMap = Maps.newHashMap();
        refGoodsMap.put(buyerId+"_"+allocationDate+"_"+goodsId,refGood);
        List<StockSellerDTO> stockSellerDTOList = summary2Seller(stockItems);
        result.setStockSeller(CollectionUtils.isEmpty(stockSellerDTOList) ? null : stockSellerDTOList.get(0));
        List<StockAgentDTO> stockAgentDTOS = summary2Agent(stockItems,refGoodsMap);
        result.setStockAgent(CollectionUtils.isEmpty(stockAgentDTOS) ? null : stockAgentDTOS.get(0));
        result.setBuyerList(result.getStockAgent());
        result.setSaleRegionItemList(groupBySaleRegion(result.getItemList()));
        result.setSaleRegionWarehouseList(groupBySaleRegionAndWarehouse(result.getItemList()));
        return result;
    }

    @Override
    public List<StockItemDTO> findStockItemByQuery(StockItemQueryDTO dto) {
        log.info("findStockItemByQuery:{}",dto);
        List<StockItem> stockItemList = stockItemBiz.findStockItemByQuery(dto);
        if (CollectionUtils.isEmpty(stockItemList) && CsStringUtils.isNotBlank(dto.getBuyerId())) {
                dto.setBuyerId(StockItemDTO.OTHER);
                stockItemList = stockItemBiz.findStockItemByQuery(dto);
        }
        if( CollectionUtils.isEmpty(stockItemList)){
            log.info("findStockItemByQuery result is empty.");
            return Lists.newArrayList();
        }
        Map<String,Set<String>> buyerGoodsIdMap = Maps.newHashMap();
        Map<String,Set<String>> buyerWarehouseIdMap = Maps.newHashMap();
        for (StockItem stockItem : stockItemList) {
            Set<String> warehouseSet = buyerWarehouseIdMap.getOrDefault(stockItem.getBuyerId(), Sets.newHashSet());
            warehouseSet.add(stockItem.getWarehouseId());
            buyerWarehouseIdMap.put(stockItem.getBuyerId(),warehouseSet);
            Set<String> goodsIdSet = buyerGoodsIdMap.getOrDefault(stockItem.getBuyerId(), Sets.newHashSet());
            goodsIdSet.add(stockItem.getGoodsId());
            buyerGoodsIdMap.put(stockItem.getBuyerId(),goodsIdSet);
        }
        log.info("stockItems.size:{},goodsIds:{},warehouseIds:{}",stockItemList.size(),buyerGoodsIdMap.values(),buyerWarehouseIdMap.values());
        Map<String,List<WarehouseListDTO>> buyerWarehouseDTOMap = Maps.newHashMap();
        //根据厂家商品找到经销商仓库
        buyerWarehouseIdMap.forEach((k,v)->{
            RefWarehouseQueryDTO refWarehouseQueryDTO = new RefWarehouseQueryDTO();
            refWarehouseQueryDTO.setUserId(k);
            refWarehouseQueryDTO.setRefWarehouseIds(v);
            ItemResult<List<WarehouseListDTO>> listItemResult = warehouseService.queryRefWarehouseInfo(refWarehouseQueryDTO);
            if( listItemResult != null && !CollectionUtils.isEmpty(listItemResult.getData()) ){
                buyerWarehouseDTOMap.put(k, listItemResult.getData());
            }
        });
        //根据厂家商品找到经销商商品
        Map<String,List<Goods>> buyerGoodsMap = Maps.newHashMap();
        buyerGoodsIdMap.forEach((k,v)->{
            List<Goods> refGoods = goodsBiz.findRefGoodsBySellerGoodsIds(v, k);
            if( !CollectionUtils.isEmpty(refGoods) ) {
                buyerGoodsMap.put(k, refGoods);
            }
        });
        List<StockItemDTO> result = Lists.newArrayList();
        resultAddItem(stockItemList, buyerGoodsMap, buyerWarehouseDTOMap, result);
        log.info("findStockItemByQuery result:{}",result);
        return result;
    }

    private static void resultAddItem(List<StockItem> stockItemList, Map<String, List<Goods>> buyerGoodsMap, Map<String, List<WarehouseListDTO>> buyerWarehouseDTOMap, List<StockItemDTO> result) {
        for (StockItem stockItem : stockItemList) {
            StockItemDTO stockItemDTO = new StockItemDTO();
            BeanUtils.copyProperties(stockItem,stockItemDTO);
            List<Goods> refGoods = buyerGoodsMap.getOrDefault(stockItem.getBuyerId(),Lists.newArrayList());
            Goods goods = refGoods.stream().filter(item -> item.getRefGoodsId() != null && item.getRefGoodsId().equals(stockItem.getGoodsId())).findFirst().orElse(null);
            if( goods != null ) {
                stockItemDTO.setBuyerGoodsId(goods.getGoodsId());
                stockItemDTO.setBuyerGoodsName(goods.getGoodsName());
            }
            List<WarehouseListDTO> refWarehouse = buyerWarehouseDTOMap.getOrDefault(stockItem.getBuyerId(), Lists.newArrayList());
            WarehouseListDTO warehouseListDTO = refWarehouse.stream().filter(item -> item.getRefWarehouseId() != null && item.getRefWarehouseId().equals(stockItem.getWarehouseId())).findFirst().orElse(null);
            if( warehouseListDTO != null ) {
                stockItemDTO.setBuyerWarehouseId(warehouseListDTO.getWarehouseId());
                stockItemDTO.setBuyerWarehouseName(warehouseListDTO.getName());
            }
            result.add(stockItemDTO);
        }
    }

    @PrintArgs
    @Transactional(rollbackFor = Exception.class,noRollbackFor = BizException.class)
    @Override
    public Boolean create(StockCreateDTO dto) {
        if(dto == null || CollectionUtils.isEmpty(dto.getList())){
            throw new BizException(BasicCode.CUSTOM_ERROR,"库存分配明细数据不可为空");
        }
        //先新增明细，再汇总到厂家和经销商
        Date now = new Date();
        String allocationDate = dto.getAllocationDate();
        String goodsId = dto.getGoodsId();
        String sellerId = dto.getSellerId();
        String sellerName = dto.getSellerName();

        if( DateUtil.getCurrentDateStr("yyyyMM").compareTo(allocationDate) > 0 ){
            throw new BizException(BasicCode.CUSTOM_ERROR,"请选择当前月份或未来月份");
        }
        Map<String,String> warehouseIds = Maps.newHashMap();
        Map<String,String> saleRegionIds = Maps.newHashMap();
        Set<String> warehouseIdAndSaleRegionIds = Sets.newHashSet();
        Map<String,BigDecimal> warehouseQuantity = Maps.newHashMap();//按仓库汇总库存
        Set<String> doubleCheckIds = Sets.newHashSet();
        dealStockItemCreateDTOS(dto, warehouseIds, saleRegionIds, warehouseQuantity, doubleCheckIds, warehouseIdAndSaleRegionIds);
        //如果区域不包含其它客户，则添加一个
        warehouseIdAndSaleRegionIds.stream().filter(item->!doubleCheckIds.contains(item+"_"+StockItemDTO.OTHER)).forEach(item->{
            String [] array = item.split("_");
            StockItemCreateDTO stockItemCreateDTO = new StockItemCreateDTO();
            stockItemCreateDTO.setQuantityAllocation(BigDecimal.ZERO);
            stockItemCreateDTO.setQuantityTookPastYearAve(BigDecimal.ZERO);
            stockItemCreateDTO.setSaleRegionId(array[0]);
            stockItemCreateDTO.setSaleRegionName(saleRegionIds.get(array[0]));
            stockItemCreateDTO.setWarehouseId(array[1]);
            stockItemCreateDTO.setWarehouseName(warehouseIds.get(array[1]));
            stockItemCreateDTO.setBuyerName("其他客户");
            stockItemCreateDTO.setBuyerId(StockItemDTO.OTHER);
            stockItemCreateDTO.setMonthNum(0);
            dto.getList().add(stockItemCreateDTO);
        });
        Goods goods = goodsBiz.get(goodsId);
        if( goods == null || BooleanUtils.isTrue(goods.getDelFlg())){
            throw new BizException(BasicCode.CUSTOM_ERROR,"选择的商品不存在或已被删除");
        }
        //校验allocationDate月份的商品配置是否存在
        List<StockItem> existsList = stockItemBiz.findByAllocationDateAndGoods(allocationDate, goodsId);
        String existsSaleRegionAndWarehouse = existsList.stream()
                .filter(item->saleRegionIds.containsKey(item.getSaleRegionId()))
                .filter(item->warehouseIds.containsKey(item.getWarehouseId()))
                .map(item->item.getSaleRegionName() +"-"+item.getWarehouseName()).distinct().collect(Collectors.joining(","));
        if (CsStringUtils.isNotBlank(existsSaleRegionAndWarehouse)) {
            throw new BizException(BasicCode.CUSTOM_ERROR,"当前月份已存在数据:"+existsSaleRegionAndWarehouse);
        }
        //当前卖家商品-对应的厂家商品(引用商品)，经销商仓库-厂家仓库(引用仓库)
        Map<String,List<StockItem>> refWarehouseMap = Maps.newHashMap();//当前仓库对应的上级厂家仓库的库存分配明细数据
        dealRefGoods(dto, goods, allocationDate, sellerId, refWarehouseMap, goodsId);
        //如果经销商库存分配的商品 对应的厂家有库存限制,需校验
        checkParams(warehouseQuantity, refWarehouseMap, sellerId);

        if(checkAccountMasterFlag(dto.getOperator())) {
            //查询数据权限
            List<SaleRegionDTO> saleRegionList = saleRegionService.findByAccountId(dto.getOperator());
            if (CollectionUtils.isEmpty(saleRegionList)) {
                log.info("没有找到销售区域权限operatorId:{}", dto.getOperator());
                throw new BizException(BasicCode.CUSTOM_ERROR,"你没有销售区域数据权限,无法新增");
            }
        }

        GoodsCategory goodsCategory = goodsCategoryBiz.findCategoryByCategoryCode(goods.getCategoryCode());
        if( goodsCategory != null ){
            dto.setGoodsCategoryName(goodsCategory.getCategoryName());
        }
        Map<String,StockAgentDTO> map = Maps.newHashMap();
        List<StockItem> stockItems = Lists.newArrayListWithCapacity(dto.getList().size());
        for (StockItemCreateDTO item : dto.getList()) {
            StockItem stockItem = BeanConvertUtils.convert(item, StockItem.class);
            stockItem.setStockItemId(stockItemBiz.getUuidGeneratorGain());
            stockItem.setSellerId(sellerId);
            stockItem.setSellerName(sellerName);
            stockItem.setAllocationDate(allocationDate);
            stockItem.setGoodsId(goodsId);
            stockItem.setGoodsName(goods.getGoodsName());
            stockItem.setGoodsCategoryName(dto.getGoodsCategoryName());
            stockItem.setRefGoodsId(goods.getRefGoodsId());
            stockItem.setPack(goods.getPack());
            stockItem.setDelFlg(false);
            stockItem.setOtherFlg(false);
            stockItem.setCreateTime(now);
            stockItem.setCreateUser(dto.getOperator());
            stockItem.setUpdateTime(now);
            stockItem.setUpdateUser(dto.getOperator());
            stockItem.setQuantityAllocation(reasonable(stockItem.getQuantityAllocation()));
            stockItem.setQuantitySurplusFirst(stockItem.getQuantityAllocation());
            stockItem.setQuantitySurplusSecond(stockItem.getQuantityAllocation());
            stockItem.setQuantitySoldFirst(BigDecimal.ZERO);
            stockItem.setQuantitySoldSecond(BigDecimal.ZERO);
            stockItem.setQuantityTookCurrentMonth(BigDecimal.ZERO);
            stockItems.add(stockItem);
            //汇总到客户（终端买家或者是经销商）
            StockAgentDTO stockAgent = map.get(stockItem.getBuyerId());
            if( stockAgent == null ){
                stockAgent = new StockAgentDTO();
                stockAgent.setBuyerId(stockItem.getBuyerId());
                stockAgent.setQuantityAllocation(BigDecimal.ZERO);
            }
            stockAgent.setQuantityAllocation(stockAgent.getQuantityAllocation().add(stockItem.getQuantityAllocation()));
            map.put(stockItem.getBuyerId(),stockAgent);
        }
        stockItemBiz.insertList(stockItems);
        smsMessageProducer.sendMessageForStockRemind(allocationDate,sellerName,goods.getGoodsName(),map.values());
        return true;
    }

    private void dealStockItemCreateDTOS(StockCreateDTO dto, Map<String, String> warehouseIds, Map<String, String> saleRegionIds, Map<String, BigDecimal> warehouseQuantity, Set<String> doubleCheckIds, Set<String> warehouseIdAndSaleRegionIds) {
        for (StockItemCreateDTO item : dto.getList()) {
            checkNull(item.getBuyerId(),"买家id");
            checkNull(item.getBuyerName(),"买家名称");
            checkNull(item.getSaleRegionId(),"销售区域id");
            checkNull(item.getSaleRegionName(),"销售区域名称");
            checkNull(item.getWarehouseId(),"仓库id");
            checkNull(item.getWarehouseName(),"仓库名称");
            checkNull(item.getQuantityAllocation(),"分配库存量");
            warehouseIds.put(item.getWarehouseId(),item.getWarehouseName());
            saleRegionIds.put(item.getSaleRegionId(),item.getSaleRegionName());
            BigDecimal total = warehouseQuantity.getOrDefault(item.getWarehouseId(),BigDecimal.ZERO);
            warehouseQuantity.put(item.getWarehouseId(),total.add(item.getQuantityAllocation()));
            String key = item.getSaleRegionId()+"_"+item.getWarehouseId()+"_"+item.getBuyerId();
            if(doubleCheckIds.contains(key)){
                throw new BizException(BasicCode.UNDEFINED_ERROR,"数据重复:"+item.getSaleRegionName()+"-"+item.getWarehouseName()+"-"+item.getBuyerName());
            }
            doubleCheckIds.add(key);
            warehouseIdAndSaleRegionIds.add(item.getSaleRegionId()+"_"+item.getWarehouseId());
        }
    }

    private static void checkParams(Map<String, BigDecimal> warehouseQuantity, Map<String, List<StockItem>> refWarehouseMap, String sellerId) {
        warehouseQuantity.forEach((warehouseId, total)->{
            if(refWarehouseMap.containsKey(warehouseId)){
                List<StockItem> stockItems = refWarehouseMap.getOrDefault(warehouseId,Lists.newArrayList());
                StockItem stockItem1 = CollectionUtils.isEmpty(stockItems) ? null : stockItems.stream().filter(item -> CsStringUtils.equals(sellerId, item.getBuyerId())).findFirst().orElse(null);
                StockItem stockItem2 = CollectionUtils.isEmpty(stockItems) ? null : stockItems.stream().filter(item -> CsStringUtils.equals(StockItemDTO.OTHER, item.getBuyerId())).findFirst().orElse(null);
                checkStockItem1(warehouseId, total, stockItem1, stockItem2);
            }
        });
    }

    private static void checkStockItem1(String warehouseId, BigDecimal total, StockItem stockItem1, StockItem stockItem2) {
        if( stockItem1 != null ){
            log.info("warehouseId:{},total:{},stockItem1.id:{},stockItem1.getQuantitySurplusSecond:{}", warehouseId, total, stockItem1.getStockItemId(), stockItem1.getQuantitySurplusSecond());
            if (total.compareTo(stockItem1.getQuantitySurplusSecond()) > 0) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "厂家仓库:"+ stockItem1.getWarehouseName()+"库存限制为:" + stockItem1.getQuantitySurplusSecond().setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString()+"吨");
            }
        }else if( stockItem2 != null ){
            log.info("warehouseId:{},total:{},stockItem2.id:{},stockItem2.getQuantitySurplusSecond:{}", warehouseId, total, stockItem2.getStockItemId(), stockItem2.getQuantitySurplusSecond());
            if (total.compareTo(stockItem2.getQuantitySurplusSecond()) > 0) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "厂家仓库:"+ stockItem2.getWarehouseName()+"库存限制为:" + stockItem2.getQuantitySurplusSecond().setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString()+"吨");
            }
        }else{
            log.info("warehouseId:{},厂家没有配置库存", warehouseId);
        }
    }

    private void dealRefGoods(StockCreateDTO dto, Goods goods, String allocationDate, String sellerId, Map<String, List<StockItem>> refWarehouseMap, String goodsId) {
        if (CsStringUtils.isNotBlank(goods.getRefGoodsId())) {//如果当前商品是背靠背商品
            Goods refGoods = goodsBiz.get(goods.getRefGoodsId());
            if( refGoods != null && !BooleanUtils.isTrue(refGoods.getDelFlg()) ) {
                List<SaleStoreDTO> storeList = saleRegionService.findStoreBySellerId(dto.getSellerId());
                if(!CollectionUtils.isEmpty(storeList)){
                    //厂家仓库
                    Set<String> refWarehouseIds = storeList.stream().filter(item -> CsStringUtils.isNotBlank(item.getRefWarehouseId())).map(SaleStoreDTO::getRefWarehouseId).collect(Collectors.toSet());
                    //查询厂家给当前经销商的库存分配
                    Map<String, List<StockItem>> listMap = stockItemBiz.findByAllocationDateAndGoodsAndWarehouseIds(allocationDate, refGoods.getGoodsId(), refWarehouseIds)
                            .stream().filter(item -> CsStringUtils.equals(sellerId, item.getBuyerId()) || CsStringUtils.equals(StockItemDTO.OTHER, item.getBuyerId()))
                            .collect(Collectors.groupingBy(StockItem::getWarehouseId));
                    storeList.stream().filter(item -> CsStringUtils.isNotBlank(item.getRefWarehouseId()))
                            .forEach(item->{
                                //按经销商的仓库分组保存数据
                                refWarehouseMap.put(item.getStoreId(),listMap.get(item.getRefWarehouseId()));
                            });
                }
            }else{
                log.info("不是引用商品 goodsId:{}", goodsId);
            }
        }
    }

    @PrintArgs
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean update(StockUpdateDTO dto) {
        if(dto == null || CollectionUtils.isEmpty(dto.getList())){
            throw new BizException(BasicCode.CUSTOM_ERROR,"库存分配明细数据不可为空");
        }
        //先新增明细，再汇总到厂家和经销商
        Date now = new Date();
        String allocationDate = setAllocationDate(dto.getAllocationDate());
        String goodsId = dto.getGoodsId();
        String sellerId = dto.getSellerId();
        String sellerName = dto.getSellerName();

        if( DateUtil.getCurrentDateStr("yyyyMM").compareTo(allocationDate) > 0 ){
            throw new BizException(BasicCode.CUSTOM_ERROR,"请选择当前月份或未来月份");
        }
        Map<String,String> warehouseIds = Maps.newHashMap();
        Map<String,String> saleRegionIds = Maps.newHashMap();
        Set<String> warehouseIdAndSaleRegionIds = Sets.newHashSet();
        Map<String,BigDecimal> warehouseQuantity = Maps.newHashMap();//按仓库汇总库存
        Set<String> doubleCheckIds = Sets.newHashSet();
        for (StockItemUpdateDTO item : dto.getList()) {
            checkNull(item.getBuyerId(),"买家id");
            checkNull(item.getBuyerName(),"买家名称");
            checkNull(item.getSaleRegionId(),"销售区域id");
            checkNull(item.getSaleRegionName(),"销售区域名称");
            checkNull(item.getWarehouseId(),"仓库id");
            checkNull(item.getWarehouseName(),"仓库名称");
            checkNull(item.getQuantitySurplusFirst(),"最新可售库存");
            BigDecimal total = warehouseQuantity.getOrDefault(item.getWarehouseId(),BigDecimal.ZERO);
            warehouseQuantity.put(item.getWarehouseId(),total.add(item.getQuantitySurplusFirst()));
            String key = item.getSaleRegionId()+"_"+item.getWarehouseId()+"_"+item.getBuyerId();
            if(doubleCheckIds.contains(key)){
                throw new BizException(BasicCode.UNDEFINED_ERROR,"数据重复:"+item.getSaleRegionName()+"-"+item.getWarehouseName()+"-"+item.getBuyerName());
            }
            doubleCheckIds.add(key);
            warehouseIdAndSaleRegionIds.add(item.getSaleRegionId()+"_"+item.getWarehouseId());
            warehouseIds.put(item.getWarehouseId(),item.getWarehouseName());
            saleRegionIds.put(item.getSaleRegionId(),item.getSaleRegionName());
        }
        //如果区域不包含其它客户，则添加一个
        warehouseIdAndSaleRegionIds.stream().filter(item->!doubleCheckIds.contains(item+"_"+StockItemDTO.OTHER)).forEach(item->{
            String [] array = item.split("_");
            StockItemUpdateDTO stockItemUpdateDTO = new StockItemUpdateDTO();
            stockItemUpdateDTO.setQuantitySurplusFirst(BigDecimal.ZERO);
            stockItemUpdateDTO.setQuantityTookPastYearAve(BigDecimal.ZERO);
            stockItemUpdateDTO.setSaleRegionId(array[0]);
            stockItemUpdateDTO.setSaleRegionName(saleRegionIds.get(array[0]));
            stockItemUpdateDTO.setWarehouseId(array[1]);
            stockItemUpdateDTO.setWarehouseName(warehouseIds.get(array[1]));
            stockItemUpdateDTO.setBuyerName("其他客户");
            stockItemUpdateDTO.setBuyerId(StockItemDTO.OTHER);
            stockItemUpdateDTO.setMonthNum(0);
            dto.getList().add(stockItemUpdateDTO);
        });

        Goods goods = goodsBiz.get(goodsId);
        if( goods == null || BooleanUtils.isTrue(goods.getDelFlg())){
            throw new BizException(BasicCode.CUSTOM_ERROR,"选择的商品不存在或已被删除");
        }

        Map<String, List<StockItem>> refWarehouseMap = getStringListMap(dto, goods, allocationDate, sellerId, goodsId);
        checkParams2(warehouseQuantity, refWarehouseMap, sellerId);

        GoodsCategory goodsCategory = goodsCategoryBiz.findCategoryByCategoryCode(goods.getCategoryCode());
        String goodsCategoryName = goodsCategory == null ? "" : goodsCategory.getCategoryName();
        log.info("goodsCategoryBiz.findCategoryByCategoryCode: goodsCategoryCode:{},goodsCategoryName:{}",goods.getCategoryCode(),goodsCategoryName);
        if(checkAccountMasterFlag(dto.getOperator())) {
            //查询数据权限
            List<SaleRegionDTO> saleRegionList = saleRegionService.findByAccountId(dto.getOperator());
            if (CollectionUtils.isEmpty(saleRegionList)) {
                log.info("没有找到销售区域权限operatorId:{}", dto.getOperator());
                throw new BizException(BasicCode.CUSTOM_ERROR,"你没有销售区域数据权限,无法新增");
            }
        }

        Map<String,BigDecimal> buyerAndQuantity = Maps.newHashMap();
        Map<String,StockItem> otherBuyerAndQuantity = Maps.newHashMap();
        List<StockItem> stockItemAddList = Lists.newArrayList();
        List<StockItem> stockItemUpdateList = Lists.newArrayList();
        buyerAndQuantityPutValues(dto, allocationDate, goodsId, now, goods, goodsCategoryName, otherBuyerAndQuantity, sellerId, sellerName, stockItemUpdateList, stockItemAddList, buyerAndQuantity);
        StockItem stockItem = stockItemUpdateList.stream().filter(item->StockItemDTO.OTHER.equals(item.getBuyerId())).findFirst().orElse(null);
        stockItemSetValues(stockItem, allocationDate, goodsId, otherBuyerAndQuantity);
        List<StockAgentDTO> stockAgentDTOList = Lists.newArrayList();
        for (Map.Entry<String, BigDecimal> entry : buyerAndQuantity.entrySet()) {
            if(entry.getValue().compareTo(BigDecimal.ZERO) == 0 ){
                continue;//无变化则忽略提醒
            }
            List<StockItem> oldBuyerList = stockItemBiz.findByBuyerIdAndAllocationDate(entry.getKey(), allocationDate, goodsId);
            //汇总买家库存分配
            BigDecimal reduce = oldBuyerList.stream().map(StockItem::getQuantitySurplusFirst).reduce(entry.getValue(), BigDecimal::add);
            StockAgentDTO stockAgentDTO = new StockAgentDTO();
            stockAgentDTO.setBuyerId(entry.getKey());
            stockAgentDTO.setQuantityAllocation(reduce);
            stockAgentDTOList.add(stockAgentDTO);
        }
        //更新明细
        stockItemBiz.insertList(stockItemAddList);
        stockItemUpdateList.forEach(item-> stockItemBiz.updateSelective(item));
        //买家库存分配变动提醒
        smsMessageProducer.sendMessageForStockRemind(allocationDate,sellerName,goods.getGoodsName(),stockAgentDTOList);
        return true;
    }

    private static void stockItemSetValues(StockItem stockItem, String allocationDate, String goodsId, Map<String, StockItem> otherBuyerAndQuantity) {
        if( stockItem != null ) {
            //如果调整时有新增客户，则需要把销量和提货量从other中减掉
            String key = allocationDate + "_" + goodsId + "_" + stockItem.getSaleRegionId() + "_" + stockItem.getWarehouseId();
            if (StockItemDTO.OTHER.equals(stockItem.getBuyerId()) && otherBuyerAndQuantity.containsKey(key)) {
                StockItem stockItem1 = otherBuyerAndQuantity.get(key);
                stockItem.setQuantitySoldFirst(reasonable(stockItem.getQuantitySoldFirst().subtract(stockItem1.getQuantitySoldFirst())));
                stockItem.setQuantitySoldSecond(reasonable(stockItem.getQuantitySoldSecond().subtract(stockItem1.getQuantitySoldSecond())));
                stockItem.setQuantityTookCurrentMonth(reasonable(stockItem.getQuantityTookCurrentMonth().subtract(stockItem1.getQuantityTookCurrentMonth())));
            }
        }
    }

    private void buyerAndQuantityPutValues(StockUpdateDTO dto, String allocationDate, String goodsId, Date now, Goods goods, String goodsCategoryName, Map<String, StockItem> otherBuyerAndQuantity, String sellerId, String sellerName, List<StockItem> stockItemUpdateList, List<StockItem> stockItemAddList, Map<String, BigDecimal> buyerAndQuantity) {
        for (StockItemUpdateDTO item : dto.getList()) {
            boolean update;
            StockItem stockItem;
            //新增库存量
            BigDecimal addQuantity;
            if (CsStringUtils.isBlank(item.getStockItemId())) {
                addQuantity = reasonable(item.getQuantitySurplusFirst());
                //查询该买家是否已经存在提货量数据（那种原来在Other中的数据，现在独立出来）
                stockItem = stockItemBiz.findOtherBuyerByGoodsIdAndSaleRegionIdAndWarehouseId(allocationDate, goodsId,item.getSaleRegionId(),item.getWarehouseId(),item.getBuyerId());
                if( stockItem != null ){
                    update = true;
                    stockItem.setOtherFlg(false);
                    stockItem.setCreateTime(now);
                    stockItem.setCreateUser(dto.getOperator());
                    stockItem.setGoodsId(goodsId);
                    stockItem.setGoodsName(goods.getGoodsName());
                    stockItem.setGoodsCategoryName(goodsCategoryName);

                    //库存设置为最新值
                    stockItem.setQuantityAllocation(addQuantity);
                    //销量如果存在则沿用，否则初始化为0
                    stockItem.setQuantitySoldFirst(reasonable(stockItem.getQuantitySoldFirst()));
                    stockItem.setQuantitySoldSecond(reasonable(stockItem.getQuantitySoldSecond()));
                    stockItem.setQuantityTookCurrentMonth(reasonable(stockItem.getQuantityTookCurrentMonth()));
                    //一级剩余可售库存＝分配库存总量 － 一级订单已售
                    stockItem.setQuantitySurplusFirst(addQuantity.subtract(stockItem.getQuantitySoldFirst()));
                    //二级剩余可售库存＝分配库存总量 － 二级订单已售
                    stockItem.setQuantitySurplusSecond(addQuantity.subtract(stockItem.getQuantitySoldSecond()));

                    String key = allocationDate +"_"+ goodsId +"_"+item.getSaleRegionId()+"_"+item.getWarehouseId();
                    StockItem otherStockItem;
                    otherStockItem = getStockItem(otherBuyerAndQuantity, key, stockItem);
                    otherBuyerAndQuantity.put(key,otherStockItem);
                }else{
                    stockItem = BeanConvertUtils.convert(item, StockItem.class);
                    update = false;
                    stockItem.setOtherFlg(false);
                    stockItem.setStockItemId(stockItemBiz.getUuidGeneratorGain());
                    stockItem.setCreateTime(now);
                    stockItem.setCreateUser(dto.getOperator());
                    stockItem.setGoodsId(goodsId);
                    stockItem.setGoodsName(goods.getGoodsName());
                    stockItem.setGoodsCategoryName(goodsCategoryName);

                    //库存设置为最新值
                    stockItem.setQuantityAllocation(addQuantity);
                    stockItem.setQuantitySurplusFirst(addQuantity);
                    stockItem.setQuantitySurplusSecond(addQuantity);
                    //销量初始化为0
                    stockItem.setQuantitySoldFirst(BigDecimal.ZERO);
                    stockItem.setQuantitySoldSecond(BigDecimal.ZERO);
                    stockItem.setQuantityTookCurrentMonth(BigDecimal.ZERO);
                }
            }else{
                stockItem = stockItemBiz.get(item.getStockItemId());
                update = true;
                if( stockItem == null ){
                    throw new BizException(BasicCode.CUSTOM_ERROR,"修改对象id:"+item.getStockItemId() + "不存在");
                }
                //新增库存量 = 新的库存量 - 当前库存量
                addQuantity = reasonable(item.getQuantitySurplusFirst()).subtract(stockItem.getQuantitySurplusFirst());
                //库存设置为最新值
                stockItem.setQuantityAllocation(reasonable(stockItem.getQuantityAllocation().add(addQuantity)));
                stockItem.setQuantitySurplusFirst(reasonable(stockItem.getQuantitySurplusFirst().add(addQuantity)));
                //二级剩余可售库存＝分配库存总量 － 二级订单已售
                stockItem.setQuantitySurplusSecond(reasonable(stockItem.getQuantityAllocation().subtract(stockItem.getQuantitySoldSecond())));
            }
            stockItem.setSellerId(sellerId);
            stockItem.setSellerName(sellerName);
            stockItem.setAllocationDate(allocationDate);
            stockItem.setGoodsId(goodsId);
            stockItem.setDelFlg(false);
            stockItem.setUpdateTime(now);
            stockItem.setUpdateUser(dto.getOperator());
            stockItemUpdateListAddValue(stockItemUpdateList, stockItemAddList, update, stockItem);
            //记录买家库存增量
            BigDecimal buyerTotalChangeQuantity = buyerAndQuantity.getOrDefault(stockItem.getBuyerId(),BigDecimal.ZERO);
            buyerAndQuantity.put(stockItem.getBuyerId(),buyerTotalChangeQuantity.add(addQuantity));
        }
    }

    private static void stockItemUpdateListAddValue(List<StockItem> stockItemUpdateList, List<StockItem> stockItemAddList, boolean update, StockItem stockItem) {
        if(update){
            stockItemUpdateList.add(stockItem);
        }else{
            stockItemAddList.add(stockItem);
        }
    }

    @NotNull
    private static StockItem getStockItem(Map<String, StockItem> otherBuyerAndQuantity, String key, StockItem stockItem) {
        StockItem otherStockItem;
        if( otherBuyerAndQuantity.containsKey(key)) {
            otherStockItem = otherBuyerAndQuantity.get(key);
            //已经售
            otherStockItem.setQuantitySoldSecond(otherStockItem.getQuantitySoldSecond().add(stockItem.getQuantitySoldSecond()));
            otherStockItem.setQuantitySoldFirst(otherStockItem.getQuantitySoldFirst().add(stockItem.getQuantitySoldFirst()));
            //已提
            otherStockItem.setQuantityTookCurrentMonth(otherStockItem.getQuantityTookCurrentMonth().add(reasonable(stockItem.getQuantityTookCurrentMonth())));
        }else{
            otherStockItem = new StockItem();
            otherStockItem.setQuantitySoldSecond(reasonable(stockItem.getQuantitySoldSecond()));
            otherStockItem.setQuantitySoldFirst(reasonable(stockItem.getQuantitySoldFirst()));
            otherStockItem.setQuantityTookCurrentMonth(reasonable(stockItem.getQuantityTookCurrentMonth()));
        }
        return otherStockItem;
    }

    private static void checkParams2(Map<String, BigDecimal> warehouseQuantity, Map<String, List<StockItem>> refWarehouseMap, String sellerId) {
        //如果经销商库存分配的商品 对应的厂家有库存限制,需校验
        warehouseQuantity.forEach((warehouseId, total)->{
            if(refWarehouseMap.containsKey(warehouseId)){
                List<StockItem> stockItems = refWarehouseMap.getOrDefault(warehouseId,Lists.newArrayList());
                StockItem stockItem1 = CollectionUtils.isEmpty(stockItems) ? null : stockItems.stream().filter(item -> CsStringUtils.equals(sellerId, item.getBuyerId())).findFirst().orElse(null);
                StockItem stockItem2 = CollectionUtils.isEmpty(stockItems) ? null : stockItems.stream().filter(item -> CsStringUtils.equals(StockItemDTO.OTHER, item.getBuyerId())).findFirst().orElse(null);
                dealstockItems(warehouseId, total, stockItem1, stockItem2);
            }
        });
    }

    private static void dealstockItems(String warehouseId, BigDecimal total, StockItem stockItem1, StockItem stockItem2) {
        if( stockItem1 != null ){
            log.info("warehouseId:{},total:{},stockItem1.id:{},stockItem1.getQuantitySurplusSecond:{}", warehouseId, total, stockItem1.getStockItemId(), stockItem1.getQuantitySurplusSecond());
            if (total.compareTo(stockItem1.getQuantitySurplusSecond()) > 0) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "厂家仓库:"+ stockItem1.getWarehouseName()+"库存限制为:" + stockItem1.getQuantitySurplusSecond().setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString()+"吨");
            }
        }else if( stockItem2 != null ){
            log.info("warehouseId:{},total:{},stockItem2.id:{},stockItem2.getQuantitySurplusSecond:{}", warehouseId, total, stockItem2.getStockItemId(), stockItem2.getQuantitySurplusSecond());
            if (total.compareTo(stockItem2.getQuantitySurplusSecond()) > 0) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "厂家仓库:"+ stockItem2.getWarehouseName()+"库存限制为:" + stockItem2.getQuantitySurplusSecond().setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString()+"吨");
            }
        }else{
            log.info("warehouseId:{},厂家没有配置库存", warehouseId);
        }
    }

    @NotNull
    private Map<String, List<StockItem>> getStringListMap(StockUpdateDTO dto, Goods goods, String allocationDate, String sellerId, String goodsId) {
        //当前卖家商品-对应的厂家商品(引用商品)，经销商仓库-厂家仓库(引用仓库)
        Map<String,List<StockItem>> refWarehouseMap = Maps.newHashMap();//当前仓库对应的上级厂家仓库的库存分配明细数据
        if (CsStringUtils.isNotBlank(goods.getRefGoodsId())) {
            Goods refGoods = goodsBiz.get(goods.getRefGoodsId());
            if( refGoods != null && !BooleanUtils.isTrue(refGoods.getDelFlg()) ) {
                List<SaleStoreDTO> storeList = saleRegionService.findStoreBySellerId(dto.getSellerId());
                if(!CollectionUtils.isEmpty(storeList)){
                    //厂家仓库
                    Set<String> refWarehouseIds = storeList.stream().filter(item -> CsStringUtils.isNotBlank(item.getRefWarehouseId())).map(SaleStoreDTO::getRefWarehouseId).collect(Collectors.toSet());
                    //厂家分配给当前经销商的库存量
                    Map<String, List<StockItem>> listMap = stockItemBiz.findByAllocationDateAndGoodsAndWarehouseIds(allocationDate, refGoods.getGoodsId(), refWarehouseIds)
                            .stream().filter(item -> CsStringUtils.equals(sellerId, item.getBuyerId()) || CsStringUtils.equals(StockItemDTO.OTHER, item.getBuyerId()))
                            .collect(Collectors.groupingBy(StockItem::getWarehouseId));
                    storeList.stream().filter(item -> CsStringUtils.isNotBlank(item.getRefWarehouseId()))
                            .forEach(item->refWarehouseMap.put(item.getStoreId(),listMap.get(item.getRefWarehouseId())));
                }
            }else{
                log.info("不是引用商品 goodsId:{}", goodsId);
            }
        }
        return refWarehouseMap;
    }

    private void checkNull(Object value,String fieldName){
        if(value == null ){
            throw new BizException(BasicCode.UNDEFINED_ERROR,fieldName+"不可为空");
        }
        if (value instanceof String string && CsStringUtils.isBlank(string)) {
            throw new BizException(BasicCode.UNDEFINED_ERROR,fieldName+"不可为空");
        }
        if( value instanceof Collection<?> collection && CollectionUtils.isEmpty(collection)){
            throw new BizException(BasicCode.UNDEFINED_ERROR,fieldName+"不可为空");
        }
    }

    @Override
    public PageInfo<StockSellerDTO> pageStock(PageQuery<StockQueryDTO> dto) {
        String operatorId = dto.getQueryDTO().getOperatorId();
        //如果不是主账号，则限制其销售区域数据权限
        if(checkAccountMasterFlag(operatorId)) {
            //查询数据权限
            List<SaleRegionDTO> saleRegionList = saleRegionService.findByAccountId(operatorId);
            Set<String> saleRegionIds = CollectionUtils.isEmpty(saleRegionList) ? Sets.newHashSet() : saleRegionList.stream().map(SaleRegionDTO::getSaleRegionId).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(saleRegionIds)) {
                log.info("没有找到销售区域权限operatorId:{}", operatorId);
                return new PageInfo<>();
            }
            dto.getQueryDTO().setSaleRegionIds(saleRegionIds);
        }
        List<StockSellerDTO> stockSellerDTOS = summary2Seller(stockItemBiz.findByQuery(dto.getQueryDTO()));
        PageInfo<StockSellerDTO> pageInfo = new PageInfo<>();
        pageInfo.setTotal(stockSellerDTOS.size());
        pageInfo.setPageNum(dto.getPageNum());
        pageInfo.setPageSize(dto.getPageSize());
        pageInfo.setPages(pageInfo.getTotal()%pageInfo.getPageSize() == 0 ? (int)pageInfo.getTotal()/pageInfo.getPageSize() : (int)pageInfo.getTotal()/pageInfo.getPageSize()+1);
        int begin = (pageInfo.getPageNum()-1)*pageInfo.getPageSize();
        begin = Math.max(begin, 0);
        int end = pageInfo.getPageNum()*pageInfo.getPageSize();
        end = Math.min(end, (int) pageInfo.getTotal());
        stockSellerDTOS.sort(Comparator.comparing(StockSellerDTO::getAllocationDate).thenComparing(StockSellerDTO::getUpdateTime).reversed());
        pageInfo.setList(stockSellerDTOS.subList(begin,end));
        return pageInfo;
    }

    @Override
    public PageInfo<StockAgentDTO> pageStockAgent(PageQuery<StockQueryDTO> dto) {
        String operatorId = dto.getQueryDTO().getOperatorId();
        //如果不是主账号，则限制其仓库数据权限
        if(checkAccountMasterFlag(operatorId)) {
            //查找操作人仓库权限
            ItemResult<List<String>> storeHouseIds = dataPermService.findByAccountIdAndDataPrivCode(operatorId, DataPrivCodeEnum.STOREHOUSE.getCode());
            if (storeHouseIds == null || CollectionUtils.isEmpty(storeHouseIds.getData())) {
                log.info("没有找到仓库数据权限operatorId:{}", operatorId);
                return new PageInfo<>();
            }
            List<WarehouseListDTO> warehouseListDTOS = warehouseService.queryWarehouseListByIds(storeHouseIds.getData());
            if (CollectionUtils.isEmpty(warehouseListDTOS)) {
                log.info("没有找到仓库数据权限operatorId:{}", operatorId);
                return new PageInfo<>();
            }
            //查找操作人仓库对应的厂家
            Set<String> refWarehouseIds = warehouseListDTOS.stream().filter(item -> CsStringUtils.isNotBlank(item.getRefWarehouseId())).map(WarehouseListDTO::getRefWarehouseId).collect(Collectors.toSet());
            refWarehouseIds.addAll(storeHouseIds.getData());
            dto.getQueryDTO().setWarehouseIds(refWarehouseIds);
        }
        List<StockAgentDTO> stockAgentDTOList = summary2Agent(stockItemBiz.findByQuery(dto.getQueryDTO()));
        PageInfo<StockAgentDTO> pageInfo = new PageInfo<>();
        pageInfo.setTotal(stockAgentDTOList.size());
        pageInfo.setPageNum(dto.getPageNum());
        pageInfo.setPageSize(dto.getPageSize());
        pageInfo.setPages(pageInfo.getTotal()%pageInfo.getPageSize() == 0 ? (int)pageInfo.getTotal()/pageInfo.getPageSize() : (int)pageInfo.getTotal()/pageInfo.getPageSize()+1);
        int begin = (pageInfo.getPageNum()-1)*pageInfo.getPageSize();
        begin = Math.max(begin, 0);
        int end = pageInfo.getPageNum()*pageInfo.getPageSize();
        end = Math.min(end, (int) pageInfo.getTotal());
        stockAgentDTOList.sort(Comparator.comparing(StockAgentDTO::getAllocationDate).thenComparing(StockAgentDTO::getUpdateTime).reversed());
        pageInfo.setList(stockAgentDTOList.subList(begin,end));
        return pageInfo;
    }

    private static final String KEY_PREFIX = "stock_change_";

    @Transactional(rollbackFor = Exception.class)
    @Override
    public synchronized Boolean stockChange(StockChangeDTO dto) {
        log.info("stockChange:{}",dto);
        if (checkParams(dto)) {
            return false;
        }
        List<StockItem> items = stockItemBiz.findByBuyerId(dto.getAllocationDate(),dto.getGoodsId(),dto.getSaleRegionId(),dto.getWarehouseId(),Lists.newArrayList(dto.getBuyerId(),StockItemDTO.OTHER));
        if(CollectionUtils.isEmpty(items) ){
            int count = stockItemBiz.countByAllocationDate(dto.getAllocationDate(),dto.getGoodsId());
            log.info("stockChange findStockItemByQuery查询结果为空,商品:{},月份:{},库存分配记录数:{}",dto.getGoodsId(),dto.getAllocationDate(),count);
            return false;
        }
        log.info("stockChange: findByBuyerId result.size:{}",items.size());
        StockItem buyerIdStockItem = items.stream().filter(item -> CsStringUtils.equals(dto.getBuyerId(), item.getBuyerId())).findFirst().orElse(null);
        StockItem otherStockItem = items.stream().filter(item -> CsStringUtils.equals(dto.getBuyerId(), StockItemDTO.OTHER)).findFirst().orElse(null);
        //正常有库存的大客户或者其它客户(buyer=dto.getBuyerId 或者取其它buyer=other)
        StockItem stockItem = buyerIdStockItem != null && BooleanUtils.isNotTrue(buyerIdStockItem.getOtherFlg()) ? buyerIdStockItem : otherStockItem;
        if( stockItem == null ){
            log.info("stockChange 库存分配没有找到,items:{}",items);
            return false;
        }
        //当stockItem.buyer=other时，记录dto.buyerId的销量 解决bug 8620  2020.9.21
        StockItem otherBuyerStockItem = buyerIdStockItem != null && BooleanUtils.isTrue(buyerIdStockItem.getOtherFlg()) ? buyerIdStockItem : null;
        boolean otherBuyerExists = otherBuyerStockItem != null;

        String key = KEY_PREFIX + dto.getGoodsId() +"_"+dto.getBuyerId()+"_"+dto.getSaleRegionId()+"_"+dto.getWarehouseId();
        String identifier = null;
        try{
            identifier = redisLockService.lockFast(key);
            String orderCode = dto.getOrderCode();
            BigDecimal changeQuantity = dto.getChangeQuantity();
            StockItem stockItemUpdate = new StockItem();
            StockItem otherBuyerStockItemUpdate = new StockItem();
            stockItemUpdate.setStockItemId(stockItem.getStockItemId());
            otherBuyerStockItemUpdate.setStockItemId(otherBuyerExists ? otherBuyerStockItem.getStockItemId() : null);
            //一级订单
            stockAgentChange2(dto, stockItemUpdate, stockItem, changeQuantity, otherBuyerStockItemUpdate, otherBuyerExists, otherBuyerStockItem, orderCode);
            stockItemBiz.updateSelective(stockItemUpdate);
            stockItemBizInsert2(dto, otherBuyerExists, otherBuyerStockItemUpdate, stockItem, changeQuantity);
            return true;
        }finally {
            redisLockService.unlock(key,identifier);
        }
    }

    private void stockAgentChange2(StockChangeDTO dto, StockItem stockItemUpdate, StockItem stockItem, BigDecimal changeQuantity, StockItem otherBuyerStockItemUpdate, boolean otherBuyerExists, StockItem otherBuyerStockItem, String orderCode) {
        if( dto.getOrderLevel() != null && dto.getOrderLevel() == 1){
            //增加销售量
            stockItemUpdate.setQuantitySoldFirst(reasonable(add(stockItem.getQuantitySoldFirst(), changeQuantity)));
            //只记录销量
            otherBuyerStockItemUpdate.setQuantitySoldFirst(otherBuyerExists ? reasonable(add(otherBuyerStockItem.getQuantitySoldFirst(), changeQuantity)) : null);
            log.info("stockChange 一级订单:{},增加销售量,累计销量:{} + 本次销量:{} = 新的累计销量:{}", orderCode, stockItem.getQuantitySoldFirst(), changeQuantity, stockItemUpdate.getQuantitySoldFirst());
            //剩余库存 = 分配库存量 - （销量、提货量二者取大的一个）
            BigDecimal sold = stockItemUpdate.getQuantitySoldFirst();
            //如果有超发
            if(sold.compareTo(stockItem.getQuantityTookCurrentMonth()) < 0 ){
                sold = stockItem.getQuantityTookCurrentMonth();
            }
            stockItemUpdate.setQuantitySurplusFirst(reasonable(subtract(stockItem.getQuantityAllocation(),sold)));
            log.info("stockChange:{} 一级订单:{},减少库存,总分配库存:{} - (新的累计销量:{}或本月累计提货量:{}二者取大的一个:{}) = {}",
                    stockItem.getStockItemId(), orderCode,
                    stockItem.getQuantityAllocation(), stockItemUpdate.getQuantitySoldFirst(),
                    stockItem.getQuantityTookCurrentMonth(),sold, stockItemUpdate.getQuantitySurplusFirst());
        }else{
            //二级订单
            //增加销售量
            stockItemUpdate.setQuantitySoldSecond(reasonable(add(stockItem.getQuantitySoldSecond(), changeQuantity)));
            //只记录销量
            otherBuyerStockItemUpdate.setQuantitySoldSecond(otherBuyerExists ? reasonable(add(otherBuyerStockItem.getQuantitySoldSecond(), changeQuantity)) : null);
            log.info("stockChange:{} 二级订单:{},增加销售量,累计销量:{} + 本次销量:{} = 新的累计销量:{}",
                    stockItem.getStockItemId(), orderCode,
                    stockItem.getQuantitySoldSecond(), changeQuantity, stockItemUpdate.getQuantitySoldSecond());
            //剩余库存 = 分配库存量 - （销量、提货量二者取大的一个）
            BigDecimal sold = stockItemUpdate.getQuantitySoldSecond();
            //如果有超发
            if(sold.compareTo(stockItem.getQuantityTookCurrentMonth()) < 0 ){
                sold = stockItem.getQuantityTookCurrentMonth();
            }
            stockItemUpdate.setQuantitySurplusSecond(reasonable(subtract(stockItem.getQuantityAllocation(),sold)));
            log.info("stockChange:{} 二级订单:{},减少库存,总分配库存:{} - (新的累计销量:{}或本月累计提货量:{}二者取大的一个:{}) = {}",
                    stockItem.getStockItemId(), orderCode,
                    stockItem.getQuantityAllocation(),
                    stockItemUpdate.getQuantitySoldSecond(),
                    stockItem.getQuantityTookCurrentMonth(),sold,
                    stockItemUpdate.getQuantitySurplusSecond());
            if (CsStringUtils.isNotBlank(dto.getAgentGoodsId())) {
                stockAgentChange(dto);//经销商库存变化修改
            }
        }
    }

    private static boolean checkParams(StockChangeDTO dto) {
        if (CsStringUtils.isBlank(dto.getGoodsId()) || CsStringUtils.isBlank(dto.getSaleRegionId()) ||
                CsStringUtils.isBlank(dto.getWarehouseId()) || CsStringUtils.isBlank(dto.getBuyerId()) || Objects.isNull(dto.getChangeQuantity())) {
            log.info("goodsId:{},saleRegionId:{},warehouseId:{},buyerId:{},changeQuantity:{}", dto.getGoodsId(), dto.getSaleRegionId(), dto.getWarehouseId(), dto.getBuyerId(), dto.getChangeQuantity());
            return true;
        }
        if (CsStringUtils.isBlank(dto.getAllocationDate())) {
            dto.setAllocationDate(DateUtil.getCurrentDateStr("yyyyMM"));
        }
        //返回结果应该只有1条或者2条记录
        return false;
    }

    private void stockItemBizInsert2(StockChangeDTO dto, boolean otherBuyerExists, StockItem otherBuyerStockItemUpdate, StockItem stockItem, BigDecimal changeQuantity) {
        if(otherBuyerExists){
            stockItemBiz.updateSelective(otherBuyerStockItemUpdate);
        }else if( StockItemDTO.OTHER.equals(stockItem.getBuyerId())){
            StockItem stockItem1 = new StockItem();
            BeanUtils.copyProperties(stockItem,stockItem1);

            stockItem1.setStockItemId(stockItemBiz.getUuidGeneratorGain());
            stockItem1.setBuyerId(dto.getBuyerId());
            stockItem1.setBuyerName(dto.getBuyerName());
            stockItem1.setOtherFlg(true);

            stockItem1.setQuantityAllocation(BigDecimal.ZERO);
            stockItem1.setQuantitySurplusFirst(BigDecimal.ZERO);
            stockItem1.setQuantitySurplusSecond(BigDecimal.ZERO);
            stockItem1.setQuantityTookPastYearAve(BigDecimal.ZERO);
            stockItem1.setQuantityTookCurrentMonth(BigDecimal.ZERO);

            stockItem1.setQuantitySoldFirst(dto.getOrderLevel() != null && dto.getOrderLevel() == 1 ? changeQuantity : BigDecimal.ZERO);
            stockItem1.setQuantitySoldSecond(dto.getOrderLevel() != null && dto.getOrderLevel() == 1 ? BigDecimal.ZERO : changeQuantity);
            stockItemBiz.insert(stockItem1);
            log.info("insert stockItem:{}",stockItem1);
        }
    }

    private void stockAgentChange(StockChangeDTO dto){
        if (CsStringUtils.isBlank(dto.getAgentBuyerId()) || CsStringUtils.isBlank(dto.getAgentGoodsId()) ||
                CsStringUtils.isBlank(dto.getAgentSaleRegionId()) || CsStringUtils.isBlank(dto.getAgentWarehouseId())) {
            log.info("agentGoodsId:{},agentSaleRegionId:{},agentWarehouseId:{},agentBuyerId:{},changeQuantity:{}",dto.getGoodsId(),dto.getSaleRegionId(),dto.getWarehouseId(),dto.getBuyerId(),dto.getChangeQuantity());
            return ;
        }
        //返回结果应该只有1条或者2条记录
        List<StockItem> items = stockItemBiz.findByBuyerId(dto.getAllocationDate(),dto.getAgentGoodsId(),dto.getAgentSaleRegionId(),dto.getAgentWarehouseId(),Lists.newArrayList(dto.getAgentBuyerId(),StockItemDTO.OTHER));
        if(CollectionUtils.isEmpty(items) ){
            int count = stockItemBiz.countByAllocationDate(dto.getAllocationDate(),dto.getAgentGoodsId());
            log.info("stockChange findStockItemByQuery查询结果为空,商品:{},月份:{},库存分配记录数:{}",dto.getAgentGoodsId(),dto.getAllocationDate(),count);
            return ;
        }
        log.info("stockChange: findByBuyerId result.size:{}",items.size());
        StockItem buyerIdStockItem = items.stream().filter(item -> CsStringUtils.equals(dto.getAgentBuyerId(), item.getBuyerId())).findFirst().orElse(null);
        StockItem otherStockItem = items.stream().filter(item -> CsStringUtils.equals(dto.getAgentBuyerId(), StockItemDTO.OTHER)).findFirst().orElse(null);
        //正常有库存的大客户或者其它客户(buyer=dto.getAgentBuyerId 或者取其它buyer=other)
        StockItem stockItem = buyerIdStockItem != null && BooleanUtils.isNotTrue(buyerIdStockItem.getOtherFlg()) ? buyerIdStockItem : otherStockItem;
        if( stockItem == null ){
            log.info("stockChange 库存分配没有找到,items:{}",items);
            return ;
        }
        //当stockItem.buyer=other时，记录dto.AgentBuyerId的销量 解决bug 8620  2020.9.21
        StockItem otherBuyerStockItem = buyerIdStockItem != null && BooleanUtils.isTrue(buyerIdStockItem.getOtherFlg()) ? buyerIdStockItem : null;
        boolean otherBuyerExists = otherBuyerStockItem != null;

        String orderCode = dto.getOrderCode();
        BigDecimal changeQuantity = dto.getChangeQuantity();
        StockItem stockItemUpdate = new StockItem();
        StockItem otherBuyerStockItemUpdate = new StockItem();
        stockItemUpdate.setStockItemId(stockItem.getStockItemId());
        otherBuyerStockItemUpdate.setStockItemId(otherBuyerExists ? otherBuyerStockItem.getStockItemId() : null);
        //增加销售量
        stockItemUpdate.setQuantitySoldFirst(reasonable(add(stockItem.getQuantitySoldFirst(),changeQuantity)));
        //只记录销量
        otherBuyerStockItemUpdate.setQuantitySoldFirst(otherBuyerExists ? reasonable(add(otherBuyerStockItem.getQuantitySoldFirst(),changeQuantity)) : null);
        log.info("stockChange:{} 一级订单:{},增加销售量,累计销量:{} + 本次销量:{} = 新的累计销量:{}",stockItem.getStockItemId(),orderCode,stockItem.getQuantitySoldFirst(),changeQuantity,stockItemUpdate.getQuantitySoldFirst());
        //剩余库存 = 分配库存量 - （销量、提货量二者取大的一个）
        BigDecimal sold = stockItemUpdate.getQuantitySoldFirst();
        //如果有超发
        if(sold.compareTo(stockItem.getQuantityTookCurrentMonth()) < 0 ){
            sold = stockItem.getQuantityTookCurrentMonth();
        }
        stockItemUpdate.setQuantitySurplusFirst(reasonable(subtract(stockItem.getQuantityAllocation(),sold)));
        log.info("stockChange:{} 一级订单:{},减少库存,总分配库存:{} - (新的累计销量:{}或本月累计提货量:{}二者取大的一个:{}) = {}",
                stockItem.getStockItemId(),orderCode,
                stockItem.getQuantityAllocation(), stockItemUpdate.getQuantitySoldFirst(),
                stockItem.getQuantityTookCurrentMonth(),sold, stockItemUpdate.getQuantitySurplusFirst());

        stockItemBiz.updateSelective(stockItemUpdate);
        stockItemBizInsert(dto, otherBuyerExists, otherBuyerStockItemUpdate, stockItem, changeQuantity);
    }

    private void stockItemBizInsert(StockChangeDTO dto, boolean otherBuyerExists, StockItem otherBuyerStockItemUpdate, StockItem stockItem, BigDecimal changeQuantity) {
        if(otherBuyerExists){
            stockItemBiz.updateSelective(otherBuyerStockItemUpdate);
        }else if( StockItemDTO.OTHER.equals(stockItem.getBuyerId())){
            StockItem stockItem1 = new StockItem();
            BeanUtils.copyProperties(stockItem,stockItem1);

            stockItem1.setStockItemId(stockItemBiz.getUuidGeneratorGain());
            stockItem1.setBuyerId(dto.getAgentBuyerId());
            stockItem1.setBuyerName(dto.getAgentBuyerName());
            stockItem1.setOtherFlg(true);

            stockItem1.setQuantityAllocation(BigDecimal.ZERO);
            stockItem1.setQuantitySurplusFirst(BigDecimal.ZERO);
            stockItem1.setQuantitySurplusSecond(BigDecimal.ZERO);
            stockItem1.setQuantityTookPastYearAve(BigDecimal.ZERO);
            stockItem1.setQuantityTookCurrentMonth(BigDecimal.ZERO);

            stockItem1.setQuantitySoldFirst(dto.getOrderLevel() != null && dto.getOrderLevel() == 1 ? changeQuantity : BigDecimal.ZERO);
            stockItem1.setQuantitySoldSecond(dto.getOrderLevel() != null && dto.getOrderLevel() == 1 ? BigDecimal.ZERO : changeQuantity);
            stockItemBiz.insert(stockItem1);
            log.info("insert stockItem:{}",stockItem1);
        }
    }

    @Override
    public StockTableHeadAndLastMonthDataDTO findTableHeadInfo(StockTableHeadQueryDTO stockTableHeadQueryDTO) {
        log.info("findTableHeadInfo:{}",stockTableHeadQueryDTO);
        StockTableHeadAndLastMonthDataDTO result = new StockTableHeadAndLastMonthDataDTO();
        String sellerId = stockTableHeadQueryDTO.getSellerId();
        String operatorId = stockTableHeadQueryDTO.getOperatorId();
        //查询当前账号的销售区域数据权限
        List<SaleRegionDTO> saleRegionList = saleRegionService.findByAccountId(operatorId);
        if( CollectionUtils.isEmpty(saleRegionList) ){
            log.info("saleRegionService.findByAccountId:{}",saleRegionList);
            throw new BizException(BasicCode.CUSTOM_ERROR,"没有销售区域数据权限");
        }
        List<SaleStoreDTO> saleRegionStoreList = saleRegionService.findStoreBySellerId(sellerId);
        if(CollectionUtils.isEmpty(saleRegionList) || CollectionUtils.isEmpty(saleRegionStoreList) ){
            log.info("findSampleByIdList:{}",saleRegionList);
            log.info("findStoreBySellerId:{}",saleRegionStoreList);
            throw new BizException(BasicCode.CUSTOM_ERROR,"销售区仓库信息没有配置");
        }
        Set<String> saleRegionIds = saleRegionList.stream().map(SaleRegionDTO::getSaleRegionId).collect(Collectors.toSet());
        log.info("查询当前账号的销售区域数据权限saleRegionIds:{}",saleRegionIds);
        log.info("查询当前账号的销售区域数据权限对应的销售区域信息saleRegionList:{}",saleRegionList.size());
        log.info("查询卖家:{}的销售区域仓库信息:{}",sellerId,saleRegionStoreList);
        if( !CollectionUtils.isEmpty(stockTableHeadQueryDTO.getSaleRegionIds())){
            saleRegionIds.retainAll(stockTableHeadQueryDTO.getSaleRegionIds());
            log.info("查询当前账号的销售区域数据权限与前端选择的销售区域:{}取交集:{}",stockTableHeadQueryDTO.getSaleRegionIds(),saleRegionIds);
        }

        saleRegionStoreList.sort(Comparator.comparing(SaleStoreDTO::getStoreId));
        Map<String, SaleRegionSampleDTO> saleRegionSampleDTOMap = saleRegionList.stream().collect(Collectors.toMap(SaleRegionSampleDTO::getSaleRegionId, Function.identity(), (k1, k2) -> k2));
        Map<String, List<SaleStoreDTO>> saleRegionStoreMap = saleRegionStoreList.stream()
                //数据权限过滤
                .filter(item->saleRegionIds.contains(item.getSaleRegionId()))
                .collect(Collectors.groupingBy(SaleStoreDTO::getSaleRegionId));
        log.info("查询卖家:{},当前账号:{},数据权限过滤后的的销售区域仓库信息:{}",sellerId,operatorId,saleRegionStoreMap);

        List<StockTableHeadDTO> headInfoList = Lists.newArrayList();
        saleRegionStoreMap.forEach((saleRegionId,list)->{
            StockTableHeadDTO stockTableHeadDTO = new StockTableHeadDTO();
            stockTableHeadDTO.setSaleRegionId(saleRegionId);
            stockTableHeadDTO.setSaleRegionName(saleRegionSampleDTOMap.containsKey(saleRegionId) ? saleRegionSampleDTOMap.get(saleRegionId).getSaleRegionName() : "");
            stockTableHeadDTO.setWarehouseList(list.stream().map(item->new StockTableHeadWarehouseDTO(item.getStoreId(),item.getStoreName())).toList());
            headInfoList.add(stockTableHeadDTO);
        });
        result.setHeadInfoList(headInfoList);
        result.setLastMonthData(Lists.newArrayList());
        if( !CollectionUtils.isEmpty(headInfoList) && BooleanUtils.isTrue(stockTableHeadQueryDTO.getSearchLastMonthData())){
            result.setLastMonthData(searchLastMonthData(stockTableHeadQueryDTO.getGoodsId(),stockTableHeadQueryDTO.getAllocationDate(),headInfoList));
        }
        return result;
    }

    private List<StockCustomerInfoDTO> searchLastMonthData(String goodsId,String allocationDate,List<StockTableHeadDTO> headInfoList){
        LocalDate lastMonthLocalDate = CsStringUtils.isBlank(allocationDate) ? LocalDate.now() : LocalDate.of(Integer.parseInt(allocationDate.substring(0, 4)), Integer.parseInt(allocationDate.substring(4)), 1);
        lastMonthLocalDate = lastMonthLocalDate.minusMonths(1);
        String lastMonth = lastMonthLocalDate.getYear()+"%02d".formatted(lastMonthLocalDate.getMonthValue());

        List<StockItem> lastMonthDataList = stockItemBiz.findByAllocationDateAndGoods(lastMonth,goodsId);
        if(CollectionUtils.isEmpty(lastMonthDataList)){
            log.info("上月数据查询结果为空.");
            return Lists.newArrayList();
        }
        Set<String> buyerIdSet = Sets.newHashSet(StockItemDTO.OTHER);
        List<CustomerInfoDTO> buyerList = Lists.newArrayList();
        CustomerInfoDTO other = null;
        buyerListAddValue(lastMonthDataList, buyerIdSet, buyerList, other);

        List<Map<String,String>> mapList = Lists.newArrayList();
        for (CustomerInfoDTO customerInfo : buyerList) {
            Map<String,String> map = Maps.newHashMap();
            map.put("buyerId",customerInfo.getBuyerId());
            map.put("buyerName",customerInfo.getBuyerName());

            //查询平均提货量
            OrderSendQuantityDTO orderSendQuantityDTO = new OrderSendQuantityDTO();
            orderSendQuantityDTO.setBuyerId(customerInfo.getBuyerId());
            orderSendQuantityDTO.setGoodsId(goodsId);
            LocalDate localDate = LocalDate.of(Integer.parseInt(allocationDate.substring(0,4)),Integer.parseInt(allocationDate.substring(4)),1);
            localDate = localDate.minusMonths(1);
            orderSendQuantityDTO.setEndDate(localDate.getYear()+"%02d".formatted(localDate.getMonthValue()));
            localDate = localDate.minusMonths(11);
            orderSendQuantityDTO.setBeginDate(localDate.getYear()+"%02d".formatted(localDate.getMonthValue()));
            List<OrderSendQuantityDTO>  aveList = StockItemDTO.OTHER.equals(customerInfo.getBuyerId()) ? Lists.newArrayList() : orderSendQuantityService.findAveByQuertDTO(orderSendQuantityDTO);
            Map<String,OrderSendQuantityDTO> aveMap = CollectionUtils.isEmpty(aveList) ? Maps.newHashMap() : aveList.stream().collect(Collectors.toMap(item -> item.getBuyerId() + "_" + item.getGoodsId() + "_" + item.getSaleRegionId() + "_" + item.getWarehouseId(),Function.identity(),(k1,k2)->k2));

            mapPutValues(goodsId, headInfoList, customerInfo, lastMonthDataList, aveMap, map);
            mapList.add(map);
        }
        return Lists.newArrayList(new StockCustomerInfoDTO(mapList,Lists.newArrayList()));
    }

    private static void mapPutValues(String goodsId, List<StockTableHeadDTO> headInfoList, CustomerInfoDTO customerInfo, List<StockItem> lastMonthDataList, Map<String, OrderSendQuantityDTO> aveMap, Map<String, String> map) {
        for (StockTableHeadDTO item : headInfoList) {
            List<StockItem> stockItemList = lastMonthDataList.stream()
                    .filter(stockItem -> CsStringUtils.equals(customerInfo.getBuyerId(), stockItem.getBuyerId()))
                    .filter(stockItem -> CsStringUtils.equals(item.getSaleRegionId(), stockItem.getSaleRegionId()))
                    .toList();
            for (int i = 0; i < item.getWarehouseList().size(); i++) {
                dealItems(goodsId, customerInfo, aveMap, map, item, i, stockItemList);
            }
            map.put(item.getSaleRegionId()+"_total","0");
        }
    }

    private static void dealItems(String goodsId, CustomerInfoDTO customerInfo, Map<String, OrderSendQuantityDTO> aveMap, Map<String, String> map, StockTableHeadDTO item, int i, List<StockItem> stockItemList) {
        String warehouseId = item.getWarehouseList().get(i).getWarehouseId();
        StockItem stockItem1 = stockItemList.stream().filter(stockItem -> CsStringUtils.equals(warehouseId, stockItem.getWarehouseId())).findFirst().orElse(null);

        String key = customerInfo.getBuyerId() +"_" + goodsId +"_" + item.getSaleRegionId()+"_"+ item.getWarehouseList().get(i).getWarehouseId();
        BigDecimal ave = aveMap.containsKey(key) ? aveMap.get(key).getTotalActualQuantityAve() : BigDecimal.ZERO;
        Integer aveMonths = aveMap.containsKey(key) ? aveMap.get(key).getMonths() : null;

        BigDecimal quantityAllocation = stockItem1 ==null || stockItem1.getQuantityAllocation() == null ? BigDecimal.ZERO : stockItem1.getQuantityAllocation();
        map.put(item.getSaleRegionId()+"_"+ item.getWarehouseList().get(i).getWarehouseId()+"_month",quantityAllocation.setScale(2, RoundingMode.HALF_UP).toPlainString());//上月分配量
        //平均提货量包含的月份数
        map.put(item.getSaleRegionId()+"_"+ item.getWarehouseList().get(i).getWarehouseId()+"_monthNum",aveMonths==null ? "0": aveMonths +"");
        map.put(item.getSaleRegionId()+"_"+ item.getWarehouseList().get(i).getWarehouseId()+"_avg",ave.setScale(2, RoundingMode.HALF_UP).toPlainString());
        map.put(item.getSaleRegionId()+"_name", item.getSaleRegionName());
        map.put(item.getWarehouseList().get(i).getWarehouseId()+"_name", item.getWarehouseList().get(i).getWarehouseName());
    }

    private static void buyerListAddValue(List<StockItem> lastMonthDataList, Set<String> buyerIdSet, List<CustomerInfoDTO> buyerList, CustomerInfoDTO other) {
        for (StockItem stockItem : lastMonthDataList) {
            if( BooleanUtils.isTrue(stockItem.getOtherFlg())){
                continue;
            }
            if(!buyerIdSet.contains(stockItem.getBuyerId())){
                buyerList.add(new CustomerInfoDTO(stockItem.getBuyerId(), stockItem.getBuyerName()));
                buyerIdSet.add(stockItem.getBuyerId());
            }
            if(StockItemDTO.OTHER.equals(stockItem.getBuyerId()) ){
                other = new CustomerInfoDTO(stockItem.getBuyerId(), stockItem.getBuyerName());
            }
        }
        if( other != null ){
            buyerList.add(other);
        }
    }

    @Override
    public StockCustomerInfoDTO completionCustomerInfo(CustomerInfoCompletionDTO dto) {
        dto.setAllocationDate(setAllocationDate(dto.getAllocationDate()));
        if( dto.getUpdateFlg() != null && dto.getUpdateFlg() ){
            return completionCustomerInfoForUpdate(dto);
        }
        StockTableHeadQueryDTO stockTableHeadQueryDTO = new StockTableHeadQueryDTO();
        stockTableHeadQueryDTO.setSellerId(dto.getSellerId());
        stockTableHeadQueryDTO.setOperatorId(dto.getOperatorId());
        stockTableHeadQueryDTO.setSaleRegionIds(dto.getSaleRegionIds());
        stockTableHeadQueryDTO.setSearchLastMonthData(false);
        if (CsStringUtils.isNotBlank(dto.getSaleRegionId())) {
            stockTableHeadQueryDTO.setSaleRegionIds(Lists.newArrayList(dto.getSaleRegionId()));
        }
        List<StockTableHeadDTO> tableHeadInfo = findTableHeadInfo(stockTableHeadQueryDTO).getHeadInfoList();
        List<Map<String,String>> result = Lists.newArrayList();
        if(dto.getReturnOtherInfo() != null && dto.getReturnOtherInfo()){
            dto.getList().add(new CustomerInfoDTO(StockItemDTO.OTHER,"其他客户"));
        }
        resultAddItems(dto, tableHeadInfo, result);
        return new StockCustomerInfoDTO(result,Lists.newArrayList());
    }

    private void resultAddItems(CustomerInfoCompletionDTO dto, List<StockTableHeadDTO> tableHeadInfo, List<Map<String, String>> result) {
        for (CustomerInfoDTO customerInfo : dto.getList()) {
            Map<String,String> map = Maps.newHashMap();
            map.put("buyerId",customerInfo.getBuyerId());
            map.put("buyerName",customerInfo.getBuyerName());

            //查询平均提货量
            OrderSendQuantityDTO orderSendQuantityDTO = new OrderSendQuantityDTO();
            orderSendQuantityDTO.setBuyerId(customerInfo.getBuyerId());
            orderSendQuantityDTO.setGoodsId(dto.getGoodsId());
            LocalDate localDate = LocalDate.of(Integer.parseInt(dto.getAllocationDate().substring(0,4)),Integer.parseInt(dto.getAllocationDate().substring(4)),1);
            localDate = localDate.minusMonths(1);
            orderSendQuantityDTO.setEndDate(localDate.getYear()+"%02d".formatted(localDate.getMonthValue()));
            localDate = localDate.minusMonths(11);
            orderSendQuantityDTO.setBeginDate(localDate.getYear()+"%02d".formatted(localDate.getMonthValue()));
            List<OrderSendQuantityDTO>  aveList = StockItemDTO.OTHER.equals(customerInfo.getBuyerId()) ? Lists.newArrayList() : orderSendQuantityService.findAveByQuertDTO(orderSendQuantityDTO);
            Map<String,OrderSendQuantityDTO> aveMap = CollectionUtils.isEmpty(aveList) ? Maps.newHashMap() : aveList.stream().collect(Collectors.toMap(item -> item.getBuyerId() + "_" + item.getGoodsId() + "_" + item.getSaleRegionId() + "_" + item.getWarehouseId(),Function.identity(),(k1,k2)->k2));

            mapPutValues(dto, tableHeadInfo, customerInfo, aveMap, map);
            result.add(map);
        }
    }

    private static void mapPutValues(CustomerInfoCompletionDTO dto, List<StockTableHeadDTO> tableHeadInfo, CustomerInfoDTO customerInfo, Map<String, OrderSendQuantityDTO> aveMap, Map<String, String> map) {
        for (StockTableHeadDTO item : tableHeadInfo) {
            for (int i = 0; i < item.getWarehouseList().size(); i++) {
                String key = customerInfo.getBuyerId() +"_" + dto.getGoodsId()+"_" + item.getSaleRegionId()+"_"+ item.getWarehouseList().get(i).getWarehouseId();
                BigDecimal ave = aveMap.containsKey(key) ? aveMap.get(key).getTotalActualQuantityAve() : BigDecimal.ZERO;
                Integer aveMonths = aveMap.containsKey(key) ? aveMap.get(key).getMonths() : null;
                map.put(item.getSaleRegionId()+"_"+item.getWarehouseList().get(i).getWarehouseId()+"_month","0");//本月分配量
                //平均提货量包含的月份数
                map.put(item.getSaleRegionId()+"_"+item.getWarehouseList().get(i).getWarehouseId()+"_monthNum",aveMonths==null ? "0": aveMonths +"");
                map.put(item.getSaleRegionId()+"_"+item.getWarehouseList().get(i).getWarehouseId()+"_avg",ave.setScale(2, RoundingMode.HALF_UP).toPlainString());
                map.put(item.getSaleRegionId()+"_name",item.getSaleRegionName());
                map.put(item.getWarehouseList().get(i).getWarehouseId()+"_name",item.getWarehouseList().get(i).getWarehouseName());
            }
            map.put(item.getSaleRegionId()+"_total","0");
        }
    }

    private StockCustomerInfoDTO completionCustomerInfoForUpdate(CustomerInfoCompletionDTO dto) {
        List<StockCustomerInfoForUpdateDTO> result = Lists.newArrayList();
        StockCustomerInfoDTO stockCustomerInfoDTO = new StockCustomerInfoDTO(Lists.newArrayList(),result);
        if(dto == null ){
            log.info("dto is null ");
            return stockCustomerInfoDTO;
        }
        if (CsStringUtils.isBlank(dto.getGoodsId()) || CsStringUtils.isBlank(dto.getSaleRegionId()) || CsStringUtils.isBlank(dto.getWarehouseId()) ||
                CollectionUtils.isEmpty(dto.getList())){
            log.info("goodsId:{},saleRegionId:{},warehouseId:{}",dto.getGoodsId(),dto.getSaleRegionId(),dto.getWarehouseId());
            return stockCustomerInfoDTO;
        }
        List<StockItem> existsList = stockItemBiz.findByFields(dto.getAllocationDate(),dto.getGoodsId(), dto.getSaleRegionId(), dto.getWarehouseId());
        if( CollectionUtils.isEmpty(existsList)){
            throw new BizException(BasicCode.UNDEFINED_ERROR,"当前区域下的仓库还没有数据,请新添加");
        }
        Set<String> existsBuyerIds = existsList.stream().map(StockItem::getBuyerName).collect(Collectors.toSet());
        for (CustomerInfoDTO customerInfo : dto.getList()) {
            if( existsBuyerIds.contains(customerInfo.getBuyerId())){
                //忽略已存在的
                continue;
            }
            StockCustomerInfoForUpdateDTO resultDTO = new StockCustomerInfoForUpdateDTO();
            resultDTO.setBuyerId(customerInfo.getBuyerId());
            resultDTO.setBuyerName(customerInfo.getBuyerName());
            resultDTO.setGoodsId(existsList.get(0).getGoodsId());
            resultDTO.setGoodsName(existsList.get(0).getGoodsName());
            resultDTO.setGoodsCategoryName(existsList.get(0).getGoodsCategoryName());
            resultDTO.setPack(existsList.get(0).getPack());
            resultDTO.setSaleRegionId(existsList.get(0).getSaleRegionId());
            resultDTO.setSaleRegionName(existsList.get(0).getSaleRegionName());
            resultDTO.setWarehouseId(existsList.get(0).getWarehouseId());
            resultDTO.setWarehouseName(existsList.get(0).getWarehouseName());
            //查询平均提货量
            OrderSendQuantityDTO orderSendQuantityDTO = new OrderSendQuantityDTO();
            orderSendQuantityDTO.setBuyerId(customerInfo.getBuyerId());
            orderSendQuantityDTO.setGoodsId(dto.getGoodsId());
            LocalDate localDate = LocalDate.of(Integer.parseInt(dto.getAllocationDate().substring(0,4)),Integer.parseInt(dto.getAllocationDate().substring(4)),1);
            localDate = localDate.minusMonths(1);
            orderSendQuantityDTO.setEndDate(localDate.getYear()+"%02d".formatted(localDate.getMonthValue()));
            localDate = localDate.minusMonths(11);
            orderSendQuantityDTO.setBeginDate(localDate.getYear()+"%02d".formatted(localDate.getMonthValue()));
            List<OrderSendQuantityDTO>  aveList = orderSendQuantityService.findAveByQuertDTO(orderSendQuantityDTO);
            Map<String,OrderSendQuantityDTO> aveMap = CollectionUtils.isEmpty(aveList) ? Maps.newHashMap() : aveList.stream().collect(Collectors.toMap(item -> item.getBuyerId() + "_" + item.getGoodsId() + "_" + item.getSaleRegionId() + "_" + item.getWarehouseId(),Function.identity(),(k1,k2)->k2));
            String key = customerInfo.getBuyerId() +"_" + dto.getGoodsId()+"_" + dto.getSaleRegionId()+"_"+ dto.getWarehouseId();
            BigDecimal ave = aveMap.containsKey(key) ? aveMap.get(key).getTotalActualQuantityAve() : BigDecimal.ZERO;
            resultDTO.setQuantityTookPastYearAve(ave.setScale(2, RoundingMode.HALF_UP));
            resultDTO.setMonth(BigDecimal.ZERO);
            resultDTO.setMonthNum(aveMap.containsKey(key) ? aveMap.get(key).getMonths() : 0);

            resultDTO.setQuantityAllocation(BigDecimal.ZERO);
            resultDTO.setQuantitySurplusFirst(BigDecimal.ZERO);
            resultDTO.setQuantitySoldFirst(BigDecimal.ZERO);
            resultDTO.setQuantityTookCurrentMonth(BigDecimal.ZERO);

            StockItem otherStockItem = stockItemBiz.findOtherBuyerByGoodsIdAndSaleRegionIdAndWarehouseId(dto.getAllocationDate(),dto.getGoodsId(),dto.getSaleRegionId(),dto.getWarehouseId(),customerInfo.getBuyerId());
            if( otherStockItem != null ) {
                resultDTO.setQuantitySoldFirst(otherStockItem.getQuantitySoldFirst());
                resultDTO.setQuantityTookCurrentMonth(otherStockItem.getQuantityTookCurrentMonth());
            }
            result.add(resultDTO);
        }
        return stockCustomerInfoDTO;
    }

    @Override
    public AgentStockInfoDTO findStockGoodsInfo(StockGoodsQueryDTO dto) {
        AgentStockInfoDTO agentStockInfoDTO  = new AgentStockInfoDTO();
        agentStockInfoDTO.setGoodsList(stockItemBiz.findGoodsInfo(dto.getBuyerId()));
        agentStockInfoDTO.setAllocationDateList(stockItemBiz.findAllocationDate(dto.getBuyerId()));
        return agentStockInfoDTO;
    }

    @PrintArgs
    @Override
    public Boolean updateCurrentMonthTookQuantity(List<CurrentMonthTookQuantityDTO> dto) {
        if(CollectionUtils.isEmpty(dto)){
            return false;
        }
        Date now = new Date();
        Map<String,BigDecimal> otherMap = Maps.newHashMap();
        //更新明细表
        stockItemBizInsert(dto, now, otherMap);
        stockItemBizUpdateSelective(otherMap);
        return true;
    }

    private void stockItemBizUpdateSelective(Map<String, BigDecimal> otherMap) {
        if( !otherMap.isEmpty() ){
            for (Map.Entry<String, BigDecimal> entry : otherMap.entrySet()) {
                String [] string = entry.getKey().split("_");
                CurrentMonthTookQuantityDTO query = new CurrentMonthTookQuantityDTO();
                query.setAllocationDate(string[0]);
                query.setGoodsId(string[1]);
                query.setSaleRegionId(string[2]);
                query.setWarehouseId(string[3]);
                query.setBuyerId(StockItemDTO.OTHER);
                StockItem stockItem = stockItemBiz.findCurrentMonthTookQuantity(query);
                if( stockItem != null ){
                    String key = KEY_PREFIX + stockItem.getGoodsId() +"_"+stockItem.getBuyerId()+"_"+stockItem.getSaleRegionId()+"_"+stockItem.getWarehouseId();
                    String identifier = null;
                    try {
                        identifier = redisLockService.lockFast(key);
                        StockItem upStockItem = new StockItem();
                        upStockItem.setStockItemId(stockItem.getStockItemId());
                        upStockItem.setQuantityTookCurrentMonth(entry.getValue());
                        //提货量增加，剩余提货量则减少
                        upStockItemSetItems(entry, stockItem, upStockItem);
                        stockItemBiz.updateSelective(upStockItem);
                    }finally {
                        redisLockService.unlock(key,identifier);
                    }
                }else{
                    log.info("没有buyerId=other的库存配置,allocationDate:{}goodsId:{},saleRegionId:{},warehouseId:{}",string[0],string[1],string[2],string[3]);
                }
            }
        }
    }

    private static void upStockItemSetItems(Map.Entry<String, BigDecimal> entry, StockItem stockItem, StockItem upStockItem) {
        if( !BooleanUtils.isTrue(stockItem.getOtherFlg()) ){
            //一级销量 总提货量 取大的一个
            BigDecimal sold1 = stockItem.getQuantitySoldFirst();
            //如果有超发
            if(sold1.compareTo(entry.getValue()) < 0 ){
                sold1 = entry.getValue();
            }
            BigDecimal sold2 = stockItem.getQuantitySoldSecond();
            //如果有超发
            if(sold2.compareTo(entry.getValue()) < 0 ){
                sold2 = entry.getValue();
            }
            upStockItem.setQuantitySurplusFirst(reasonable(subtract(stockItem.getQuantityAllocation(),sold1)));
            upStockItem.setQuantitySurplusSecond(reasonable(subtract(stockItem.getQuantityAllocation(),sold2)));
            log.info("updateCurrentMonthTookQuantity2 一级库存减少,总分配库存:{} - (新的累计销量:{}或本月累计提货量:{}二者取大的一个:{}) = {}",
                    stockItem.getQuantityAllocation(), stockItem.getQuantitySoldFirst(),
                    entry.getValue(),sold1, upStockItem.getQuantitySurplusFirst());
            log.info("updateCurrentMonthTookQuantity2 二级库存减少,总分配库存:{} - (新的累计销量:{}或本月累计提货量:{}二者取大的一个:{}) = {}",
                    stockItem.getQuantityAllocation(), stockItem.getQuantitySoldSecond(),
                    entry.getValue(),sold2, upStockItem.getQuantitySurplusSecond());
        }
    }

    private void stockItemBizInsert(List<CurrentMonthTookQuantityDTO> dto, Date now, Map<String, BigDecimal> otherMap) {
        for (CurrentMonthTookQuantityDTO item : dto) {
            StockItem stockItem = stockItemBiz.findCurrentMonthTookQuantity(item);
            if( stockItem != null){
                String key = KEY_PREFIX + stockItem.getGoodsId() +"_"+stockItem.getBuyerId()+"_"+stockItem.getSaleRegionId()+"_"+stockItem.getWarehouseId();
                String identifier = null;
                try {
                    identifier = redisLockService.lockFast(key);
                    StockItem upStockItem = new StockItem();
                    upStockItem.setStockItemId(stockItem.getStockItemId());
                    upStockItem.setQuantityTookCurrentMonth(item.getQuantityTookCurrentMonth());
                    //提货量增加，剩余提货量则减少
                    upStockItemAddValues(item, stockItem, upStockItem);
                    stockItemBiz.updateSelective(upStockItem);
                }finally {
                    redisLockService.unlock(key,identifier);
                }
            }else{
                stockItem = stockItemBiz.findOneByAllocationDateAndGoods(item.getAllocationDate(),item.getGoodsId());
                if( stockItem == null ){
                    log.warn("findOneByAllocationDateAndGoods is null,allocationDate:{},goodsId:{} update ignore,CurrentMonthTookQuantityDTO:{}",item.getAllocationDate(),item.getGoodsId(),item);
                }else {
                    stockItem.setStockItemId(stockItemBiz.getUuidGeneratorGain());
                    stockItem.setBuyerId(item.getBuyerId());
                    stockItem.setBuyerName(item.getBuyerName());
                    stockItem.setOtherFlg(true);
                    stockItem.setDelFlg(false);
                    stockItem.setCreateTime(now);
                    stockItem.setCreateUser(MemberPlatform.SYSTEM_OPERATOR.getId());
                    stockItem.setUpdateTime(now);
                    stockItem.setUpdateUser(MemberPlatform.SYSTEM_OPERATOR.getId());

                    stockItem.setQuantityAllocation(BigDecimal.ZERO);
                    stockItem.setQuantitySurplusFirst(BigDecimal.ZERO);
                    stockItem.setQuantitySurplusSecond(BigDecimal.ZERO);
                    stockItem.setQuantityTookPastYearAve(BigDecimal.ZERO);
                    stockItem.setQuantityTookCurrentMonth(item.getQuantityTookCurrentMonth());

                    stockItem.setQuantitySoldFirst(BigDecimal.ZERO);
                    stockItem.setQuantitySoldSecond(BigDecimal.ZERO);
                    stockItemBiz.insert(stockItem);
                }
            }
            if( stockItem == null || BooleanUtils.isTrue(stockItem.getOtherFlg())){
                String key = item.getAllocationDate()+"_"+item.getGoodsId()+"_"+item.getSaleRegionId()+"_"+item.getWarehouseId();
                otherMap.put(key,reasonable(add(item.getQuantityTookCurrentMonth(), otherMap.getOrDefault(key,BigDecimal.ZERO))));
            }
        }
    }

    private static void upStockItemAddValues(CurrentMonthTookQuantityDTO item, StockItem stockItem, StockItem upStockItem) {
        if( !BooleanUtils.isTrue(stockItem.getOtherFlg()) ){
            //一级销量 总提货量 取大的一个
            BigDecimal sold1 = stockItem.getQuantitySoldFirst();
            BigDecimal sold2 = stockItem.getQuantitySoldSecond();
            if(sold1.compareTo(item.getQuantityTookCurrentMonth()) < 0 ){
                sold1 = item.getQuantityTookCurrentMonth();
            }
            //二级销量 总提货量 取大的一个
            if(sold2.compareTo(item.getQuantityTookCurrentMonth()) < 0 ){
                sold2 = item.getQuantityTookCurrentMonth();
            }
            upStockItem.setQuantitySurplusFirst(reasonable(subtract(stockItem.getQuantityAllocation(),sold1)));
            upStockItem.setQuantitySurplusSecond(reasonable(subtract(stockItem.getQuantityAllocation(),sold2)));
            log.info("updateCurrentMonthTookQuantity1 一级库存减少,总分配库存:{} - (新的累计销量:{}或本月累计提货量:{}二者取大的一个:{}) = {}",
                    stockItem.getQuantityAllocation(), stockItem.getQuantitySoldFirst(),
                    item.getQuantityTookCurrentMonth(),sold1, upStockItem.getQuantitySurplusFirst());
            log.info("updateCurrentMonthTookQuantity1 二级库存减少,总分配库存:{} - (新的累计销量:{}或本月累计提货量:{}二者取大的一个:{}) = {}",
                    stockItem.getQuantityAllocation(), stockItem.getQuantitySoldSecond(),
                    item.getQuantityTookCurrentMonth(),sold2, upStockItem.getQuantitySurplusSecond());
        }
    }

    private static BigDecimal reasonable(BigDecimal bigDecimal){
        if( bigDecimal == null || bigDecimal.compareTo(BigDecimal.ZERO) < 0){
            return BigDecimal.ZERO;
        }
        return bigDecimal;
    }

    private static BigDecimal subtract(BigDecimal base,BigDecimal change){
        base = base == null ? BigDecimal.ZERO : base;
        change = change == null ? BigDecimal.ZERO : change;
        return base.subtract(change);
    }

    private static BigDecimal add(BigDecimal base,BigDecimal change){
        base = base == null ? BigDecimal.ZERO : base;
        change = change == null ? BigDecimal.ZERO : change;
        return base.add(change);
    }

    private boolean checkAccountMasterFlag(String accountId){
        AccountSimpleDTO accountSimpleDTO = accountService.findSimpleById(accountId);

        return accountSimpleDTO == null || accountSimpleDTO.getAccountType() == null
                || AccountDTO.ACCOUNT_TYPE_MEMBER_MASTER != accountSimpleDTO.getAccountType();
    }

    //按厂家汇总
    private List<StockSellerDTO> summary2Seller(List<StockItem> list){
        if( CollectionUtils.isEmpty(list)){
            return Lists.newArrayList();
        }
        Map<String,StockSellerDTO> map = Maps.newHashMap();
        for (StockItem stockItem : list) {
            String key = stockItem.getSellerId()+"_"+stockItem.getAllocationDate()+"_"+stockItem.getGoodsId();
            StockSellerDTO stockSeller = map.get(key);
            if(stockSeller == null ){
                stockSeller = new StockSellerDTO();
                BeanUtils.copyProperties(stockItem,stockSeller);
                //初始化为0
                stockSeller.setQuantityAllocation(BigDecimal.ZERO);
                stockSeller.setQuantitySurplusFirst(BigDecimal.ZERO);
                stockSeller.setQuantitySoldFirst(BigDecimal.ZERO);
                stockSeller.setQuantityTookCurrentMonth(BigDecimal.ZERO);
            }
            stockSeller.setQuantityAllocation(stockSeller.getQuantityAllocation().add(stockItem.getQuantityAllocation()));
            stockSeller.setQuantitySurplusFirst(stockSeller.getQuantitySurplusFirst().add(stockItem.getQuantitySurplusFirst()));
            stockSeller.setQuantitySoldFirst(stockSeller.getQuantitySoldFirst().add(stockItem.getQuantitySoldFirst()));
            stockSeller.setQuantityTookCurrentMonth(stockSeller.getQuantityTookCurrentMonth().add(stockItem.getQuantityTookCurrentMonth()));
            //排序使用
            if( stockSeller.getUpdateTime().before(stockItem.getUpdateTime())){
                stockSeller.setUpdateTime(stockItem.getUpdateTime());
            }
            map.put(key,stockSeller);
        }
        return Lists.newArrayList(map.values());
    }

    private List<StockAgentDTO> summary2Agent(List<StockItem> list){
        return summary2Agent(list,Maps.newHashMap());
    }
    //按到经销商汇总
    private List<StockAgentDTO> summary2Agent(List<StockItem> list,Map<String,Goods> refGoodsMap){
        if( CollectionUtils.isEmpty(list)){
            return Lists.newArrayList();
        }
        Map<String,StockAgentDTO> buyerMap = Maps.newHashMap();
        for (StockItem stockItem : list) {
            String key = stockItem.getBuyerId()+"_"+stockItem.getAllocationDate()+"_"+stockItem.getGoodsId();
            StockAgentDTO stockAgentDTO = buyerMap.get(key);
            if( stockAgentDTO == null ) {
                stockAgentDTO = new StockAgentDTO();
                BeanUtils.copyProperties(stockItem, stockAgentDTO);

                Goods refGood = refGoodsMap.get(key);
                refGood = getRefGood(refGoodsMap, stockItem, refGood, key);
                if( refGood != null ){
                    stockAgentDTO.setBuyerGoodsId(refGood.getGoodsId());
                    stockAgentDTO.setBuyerGoodsName(refGood.getGoodsName());
                }
                //初始化为0
                stockAgentDTO.setQuantityAllocation(BigDecimal.ZERO);
                stockAgentDTO.setQuantitySurplusFirst(BigDecimal.ZERO);
                stockAgentDTO.setQuantitySoldFirst(BigDecimal.ZERO);
                stockAgentDTO.setQuantityTookCurrentMonth(BigDecimal.ZERO);
                stockAgentDTO.setQuantitySurplusSecond(BigDecimal.ZERO);
                stockAgentDTO.setQuantitySoldSecond(BigDecimal.ZERO);
            }

            stockAgentDTO.setQuantityAllocation(stockAgentDTO.getQuantityAllocation().add(stockItem.getQuantityAllocation()));
            stockAgentDTO.setQuantitySurplusFirst(stockAgentDTO.getQuantitySurplusFirst().add(stockItem.getQuantitySurplusFirst()));
            stockAgentDTO.setQuantitySoldFirst(stockAgentDTO.getQuantitySoldFirst().add(stockItem.getQuantitySoldFirst()));
            stockAgentDTO.setQuantityTookCurrentMonth(stockAgentDTO.getQuantityTookCurrentMonth().add(stockItem.getQuantityTookCurrentMonth()));
            stockAgentDTO.setQuantitySurplusSecond(stockAgentDTO.getQuantitySurplusSecond().add(stockItem.getQuantitySurplusSecond()));
            stockAgentDTO.setQuantitySoldSecond(stockAgentDTO.getQuantitySoldSecond().add(stockItem.getQuantitySoldSecond()));
            //排序使用
            if( stockAgentDTO.getUpdateTime().before(stockItem.getUpdateTime())){
                stockAgentDTO.setUpdateTime(stockItem.getUpdateTime());
            }
            buyerMap.put(key,stockAgentDTO);
        }
        return Lists.newArrayList(buyerMap.values());
    }

    @Nullable
    private Goods getRefGood(Map<String, Goods> refGoodsMap, StockItem stockItem, Goods refGood, String key) {
        if( refGood == null ){
            List<Goods> refGoods = goodsBiz.findRefGoodsBySellerGoodsIds(Lists.newArrayList(stockItem.getGoodsId()), stockItem.getBuyerId());
            log.info("findRefGoodsBySellerGoodsIds refGoods.size:{}",refGoods.size());
            if( CollectionUtils.isEmpty(refGoods)){
                log.info("根据厂家库存分配没有找到对应的经销商商品,厂家商品id:{},买家id:{}", stockItem.getGoodsId(), stockItem.getBuyerId());
            }else{
                refGood = refGoods.get(0);
                refGoodsMap.put(key, refGood);
            }
        }
        return refGood;
    }

    //按到经销商汇总
    private List<StockItemDTO> summaryRegionList2Agent(List<StockItemDTO> list){
        if( CollectionUtils.isEmpty(list)){
            return Lists.newArrayList();
        }
        Map<String,StockItemDTO> buyerMap = Maps.newHashMap();
        StockItemDTO other = null;
        for (StockItemDTO stockItem : list) {
            String key = stockItem.getBuyerId()+"_"+stockItem.getAllocationDate()+"_"+stockItem.getGoodsId();
            StockItemDTO stockItemDTO = buyerMap.get(key);
            if( stockItemDTO == null ) {
                stockItemDTO = new StockItemDTO();
                stockItemDTO.setBuyerId(stockItem.getBuyerId());
                stockItemDTO.setBuyerName(stockItem.getBuyerName());
                //初始化为0
                stockItemDTO.setQuantityAllocation(BigDecimal.ZERO);
                stockItemDTO.setQuantitySurplusFirst(BigDecimal.ZERO);
                stockItemDTO.setQuantitySoldFirst(BigDecimal.ZERO);
                stockItemDTO.setQuantityTookCurrentMonth(BigDecimal.ZERO);
                stockItemDTO.setQuantitySurplusSecond(BigDecimal.ZERO);
                stockItemDTO.setQuantitySoldSecond(BigDecimal.ZERO);
                if(StockItemDTO.OTHER.equals(stockItem.getBuyerId())){
                    other = stockItemDTO;
                }
            }

            stockItemDTO.setQuantityAllocation(stockItemDTO.getQuantityAllocation().add(stockItem.getQuantityAllocation()));
            stockItemDTO.setQuantitySurplusFirst(stockItemDTO.getQuantitySurplusFirst().add(stockItem.getQuantitySurplusFirst()));
            stockItemDTO.setQuantitySoldFirst(stockItemDTO.getQuantitySoldFirst().add(stockItem.getQuantitySoldFirst()));
            stockItemDTO.setQuantityTookCurrentMonth(stockItemDTO.getQuantityTookCurrentMonth().add(stockItem.getQuantityTookCurrentMonth()));
            stockItemDTO.setQuantitySurplusSecond(stockItemDTO.getQuantitySurplusSecond().add(stockItem.getQuantitySurplusSecond()));
            stockItemDTO.setQuantitySoldSecond(stockItemDTO.getQuantitySoldSecond().add(stockItem.getQuantitySoldSecond()));
            buyerMap.put(key,stockItemDTO);
        }
        List<StockItemDTO> result = Lists.newArrayList(buyerMap.values());
        if(other != null ) {
            result.remove(other);
            result.add(other);
        }
        return result;
    }

    //库存分配明细-按销售区域分组
    private List<SaleRegionAndStockItemDTO> groupBySaleRegion(List<StockItemDTO> list){
        if( CollectionUtils.isEmpty(list)){
            return Lists.newArrayList();
        }
        List<SaleRegionAndStockItemDTO>  saleRegionItemList = Lists.newArrayList();
        Map<String, String> saleRegionMap = list.stream().collect(Collectors.toMap(StockItemDTO::getSaleRegionId, StockItemDTO::getSaleRegionName, (k1, k2) -> k2));
        Map<String, List<StockItemDTO>> saleRegionGroupMap = list.stream().collect(Collectors.groupingBy(StockItemDTO::getSaleRegionId));
        saleRegionMap.forEach((saleRegionId, saleRegionName) -> saleRegionItemList.add(new SaleRegionAndStockItemDTO(saleRegionId,saleRegionName,saleRegionGroupMap.getOrDefault(saleRegionId,Lists.newArrayList()),null)));
        return saleRegionItemList;
    }

    //库存分配明细-按销售区域分组
    private List<SaleRegionAndStockItemDTO> groupBySaleRegionAndWarehouse(List<StockItemDTO> itemList){
        if( CollectionUtils.isEmpty(itemList)){
            return Lists.newArrayList();
        }
        Map<String, String> saleRegionMap = Maps.newHashMap();//保存销售区域id、销售区域名称
        Map<String, String> warehouseMap = Maps.newHashMap();//保存仓库id、仓库名称
        Map<String, Set<String>> warehouseMapSet = Maps.newHashMap();//保存销售区域分组仓库的信息
        Map<String, List<StockItemDTO>> saleRegionGroupMap = Maps.newHashMap();//保存销售区域-仓库2个字段分组结果
        List<SaleRegionAndStockItemDTO> saleRegionWarehouseList = Lists.newArrayList();
        itemList.forEach(item->{
            saleRegionMap.put(item.getSaleRegionId(),item.getSaleRegionName());
            warehouseMap.put(item.getWarehouseId(),item.getWarehouseName());

            Set<String> set = warehouseMapSet.getOrDefault(item.getSaleRegionId(), Sets.newHashSet());
            set.add(item.getWarehouseId());
            warehouseMapSet.put(item.getSaleRegionId(),set);

            String key = item.getSaleRegionId() + "-" + item.getWarehouseId();
            List<StockItemDTO> list = saleRegionGroupMap.getOrDefault(key, Lists.newArrayList());
            if(StockItemDTO.OTHER.equals(item.getBuyerId())) {
                list.add(item);
            }else{
                list.add(item);
                //other放最后
                StockItemDTO other = list.stream().filter(item2 -> StockItemDTO.OTHER.equals(item2.getBuyerId())).findFirst().orElse(null);
                if( other != null ) {
                    list.remove(other);
                    list.add(other);
                }
            }
            saleRegionGroupMap.put(item.getSaleRegionId() + "-" + item.getWarehouseId(),list);
        });

        warehouseMapSet.forEach((saleRegionId,warehouseIds)->{
            SaleRegionAndStockItemDTO saleRegionAndStockItemDTO = new SaleRegionAndStockItemDTO();
            saleRegionAndStockItemDTO.setSaleRegionId(saleRegionId);
            saleRegionAndStockItemDTO.setSaleRegionName(saleRegionMap.get(saleRegionId));
            saleRegionAndStockItemDTO.setWarehouseList(Lists.newArrayList());

            List<StockItemDTO> saleRegionStockItemList = Lists.newArrayList();
            warehouseIds.forEach(warehouseId->{
                SaleRegionAndWarehouseAndStockItemDTO saleRegionAndWarehouseAndStockItemDTO = new SaleRegionAndWarehouseAndStockItemDTO();
                saleRegionAndWarehouseAndStockItemDTO.setWarehouseId(warehouseId);
                saleRegionAndWarehouseAndStockItemDTO.setWarehouseName(warehouseMap.get(warehouseId));
                saleRegionAndWarehouseAndStockItemDTO.setStockItemList(saleRegionGroupMap.getOrDefault(saleRegionId+"-"+warehouseId,Lists.newArrayList()));
                saleRegionAndStockItemDTO.getWarehouseList().add(saleRegionAndWarehouseAndStockItemDTO);
                saleRegionStockItemList.addAll(saleRegionAndWarehouseAndStockItemDTO.getStockItemList());
            });
            //所有仓库
            SaleRegionAndWarehouseAndStockItemDTO saleRegionAndWarehouseAndStockItemDTO = new SaleRegionAndWarehouseAndStockItemDTO();
            saleRegionAndWarehouseAndStockItemDTO.setWarehouseId(null);
            saleRegionAndWarehouseAndStockItemDTO.setWarehouseName("所有仓库");
            saleRegionAndWarehouseAndStockItemDTO.setStockItemList(summaryRegionList2Agent(saleRegionStockItemList));
            saleRegionAndStockItemDTO.getWarehouseList().add(saleRegionAndWarehouseAndStockItemDTO);
            saleRegionWarehouseList.add(saleRegionAndStockItemDTO);
        });
        return saleRegionWarehouseList;
    }

    public void updateQuantityTookPastYearAve(StockQueryDTO stockQueryDTO){
        threadPoolExecutor.execute(()->{
            try {
                //参考 completionCustomerInfo
                List<StockItem> list = stockItemBiz.findByQuery(stockQueryDTO);

                Set<String> keySet = list.stream().filter(item->!StockItemDTO.OTHER.equals(item.getBuyerId()))
                        .map(item -> item.getAllocationDate() + "_" + item.getBuyerId() + "_" + item.getGoodsId()).collect(Collectors.toSet());

                Map<String,OrderSendQuantityDTO> aveMap = Maps.newHashMap();
                for (String s : keySet) {
                    String [] ss = s.split("_");
                    String allocationDate = ss[0];
                    String buyerId = ss[1];
                    String goodsId = ss[2];
                    //查询平均提货量
                    OrderSendQuantityDTO orderSendQuantityDTO = new OrderSendQuantityDTO();
                    orderSendQuantityDTO.setBuyerId(buyerId);
                    orderSendQuantityDTO.setGoodsId(goodsId);
                    if (CsStringUtils.isNotBlank(allocationDate)) {
                        LocalDate localDate = LocalDate.of(Integer.parseInt(allocationDate.substring(0,4)),Integer.parseInt(allocationDate.substring(4)),1);
                        localDate = localDate.minusMonths(1);
                        orderSendQuantityDTO.setEndDate(localDate.getYear()+"%02d".formatted(localDate.getMonthValue()));
                        localDate = localDate.minusMonths(11);
                        orderSendQuantityDTO.setBeginDate(localDate.getYear()+"%02d".formatted(localDate.getMonthValue()));
                    }
                    List<OrderSendQuantityDTO>  aveList = orderSendQuantityService.findAveByQuertDTO(orderSendQuantityDTO);
                    if( !CollectionUtils.isEmpty(aveList)) {
                        aveMap.putAll(aveList.stream().collect(Collectors.toMap(item -> item.getBuyerId() + "_" + item.getGoodsId() + "_" + item.getSaleRegionId() + "_" + item.getWarehouseId(),
                                Function.identity(), (k1, k2) -> k2)));
                    }
                }
                extractstockItemBizUpdateSelective(list, aveMap);
            }catch (Exception e){
                log.error(e.getMessage(),e);
            }
        });
    }

    private void extractstockItemBizUpdateSelective(List<StockItem> list, Map<String, OrderSendQuantityDTO> aveMap) {
        for (StockItem stockItem : list) {
            if( StockItemDTO.OTHER.equals(stockItem.getBuyerId()) ){
                continue;
            }
            String key =  stockItem.getBuyerId() + "_" + stockItem.getGoodsId() + "_" + stockItem.getSaleRegionId() + "_" + stockItem.getWarehouseId();
            StockItem upStockItem = new StockItem();
            upStockItem.setStockItemId(stockItem.getStockItemId());
            upStockItem.setQuantityTookPastYearAve(BigDecimal.ZERO);
            upStockItem.setMonthNum(0);
            if( aveMap.containsKey(key)){
                OrderSendQuantityDTO orderSendQuantityDTO = aveMap.get(key);
                upStockItem.setQuantityTookPastYearAve(orderSendQuantityDTO.getTotalActualQuantityAve());
                upStockItem.setMonthNum(orderSendQuantityDTO.getMonths());
            }
            stockItemBiz.updateSelective(upStockItem);
        }
    }

    private String setAllocationDate(String allocationDate){
        if (CsStringUtils.isBlank(allocationDate)) {
            LocalDate now = LocalDate.now();
            return now.getYear()+"%02d".formatted(now.getMonthValue());
        }
        return allocationDate;
    }
}
