
package com.ecommerce.information.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Data
public class ApproveDTO {

    @Schema(description = "id")
    private String id;

    @Schema(description = "审批状态 eq")
    private Integer checkStatus;

    @Schema(description = "审批注释")
    private String comment;

    @Schema(description = "创建人")
    private String createUser;

    @Schema(description = "创建时间")
    private Date createTime;
}
