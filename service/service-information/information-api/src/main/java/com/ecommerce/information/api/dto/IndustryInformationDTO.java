
package com.ecommerce.information.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

import jakarta.validation.constraints.NotBlank;

@Data
public class IndustryInformationDTO {

	@Schema(description = "id")
	private String iiId;
	
	/**
	 * 标题
	 */
	@NotBlank
    @Schema(description = "标题")
	private String title;
	
	
	/**
	 * 类别
	 */
	@NotBlank
	@Schema(description = "类别")
	private Integer category;
	
	
	/**
	 * 状态
	 */
    @Schema(description = "状态")
	private Integer status;
	
	
	
	/**
	 * 发布者
	 */
    @Schema(description = "创建人")
	private String createUser;
	
	
	/**
	 * 发布时间
	 */
    @Schema(description = "创建时间")
	private Date createTime;
	
	/**
	 * 修改人
	 */
    @Schema(description = "修改人")
	private String updateUser;
	
	/**
	 * 修改时间
	 */
    @Schema(description = "修改时间")
	private Date updateTime;
	
	/**
	 * 开始时间
	 */
	@NotBlank
    @Schema(description = "开始时间")
	private Date beginTime;
	
	/**
	 * 结束时间
	 */
	@Schema(description = "结束时间")
	private Date failureTime;

	@Schema(description = "进行状态０未开始　3 进行中，4 已关闭")
	private Integer processStatus=0;
	
	
	/**
	 * 消息来源
	 */
	@Schema(description = "消息来源")
	private String fromSource;
	
	
	/**
	 * 富文本url
	 */
	@NotBlank
	@Schema(description = "富文本url")
	private String attachUrl;
	
	
	/**
	 * 富文本内容
	 */
	@NotBlank
    @Schema(description = "新闻内容")
	private String content;
	
	
	/**
	 * 删除标记
	 */
    @Schema(description = "删除标记")
	private Boolean delFlg;
	
	/**
	 * 关键字
	 */
    @Schema(description = "关键字")
	private String keywords; 
	
    @Schema(description = "页数")
	private Integer pageNumber = 1;
    @Schema(description = "每页条数")
	private Integer pageSize = 20;
	
}
