package com.ecommerce.information.api.dto.advertisement;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 *  广告位创建
 *
 * <AUTHOR>
 **/
@Data
public class AdverisementSpaceCreateDTO {

    /**
     * 广告位编码
     */
    @NotBlank
    @Schema(description = "广告位编码")
    private String spaceCode;

    /**
     * 广告位名称
     */
    @NotBlank
    @Schema(description = "广告位名称")
    private String spaceTitle;

    /**
     * 广告位类型（弹窗、轮播等）
     */
    @NotBlank
    @Schema(description = "广告位类型（弹窗、轮播等）")
    private String spaceType;


    /**
     * 广告范围  如平台广告、卖家广告、买家广告
     */
    @NotNull
    @Schema(description = "广告范围  如平台广告、卖家广告、买家广告")
    private String adRange;

    /**
     * 所在页面(页面注册的pageCode)
     */
    @NotNull
    @Schema(description = "所在页面(页面注册的pageCode)")
    private String spacePage;

    /**
     * 广告位置
     */
    @NotBlank
    @Schema(description = "广告位置")
    private String position;


    /**
     * 是否需要上传图片( 1 有 0 没有)
     */
    @NotBlank
    @Schema(description = "是否需要上传图片( 1 有 0 没有)")
    private Integer imageExists;

    /**
     * 图片高度
     */
    @NotBlank
    @Schema(description = "图片高度")
    private String imageHeight;

    /**
     * 图片宽度
     */
    @NotBlank
    @Schema(description = "图片宽度")
    private String imageWidth;

    /**
     * 图片大小限制
     */
    @NotBlank
    @Schema(description = "图片大小限制")
    private String imageSizeLimit;

    /**
     * 图片数量限制
     */
    @NotBlank
    @Schema(description = "图片数量限制")
    private Integer imageCountLimit;

    /**
     * 广告位可填充的广告的类型(商品、咨询等)
     */
    @NotBlank
    @Schema(description = "广告位可填充的广告的类型(商品、咨询等)")
    private String adType;

    /**
     * 广告数量
     */
    @NotNull
    @Schema(description = "广告数量")
    private Integer spaceNumber;

    /**
     * 是否需要排序（是否有序）
     */
    @NotNull
    @Schema(description = "是否需要排序（是否有序 0否 1是）")
    private Integer orderFlg;

    /**
     * 广告位状态 1 启用 0 禁用
     */
    @NotNull
    @Schema(description = "广告位状态 1 启用 0 禁用")
    private Integer spaceStatus;

}
