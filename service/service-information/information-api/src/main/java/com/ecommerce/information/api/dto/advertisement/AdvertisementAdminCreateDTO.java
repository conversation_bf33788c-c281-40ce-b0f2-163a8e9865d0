package com.ecommerce.information.api.dto.advertisement;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date: 21/08/2018 15:31
 * @DESCRIPTION:
 */
@Data
public class AdvertisementAdminCreateDTO {

    /**
     * 是否企业广告
     */
    @NotNull
    @Schema(description = "是否企业广告")
    private Integer memberAd;

    /**
     * 广告归属会员id
     */
    @Schema(description = "广告归属会员id")
    private String memberId;

    /**
     * 广告归属会员名称
     */
    @Schema(description = "广告归属会员名称")
    private String memberName;

    /**
     * 广告名称
     */
    @NotBlank
    @Schema(description = "广告名称")
    private String adName;

    /**
     * 显示顺序
     */
    @NotNull
    @Schema(description = "显示顺序")
    private Integer orderNumber;

    /**
     * 有效开始时间
     */
    @NotNull
    @Schema(description = "有效开始时间")
    private Date effectiveStartTime;

    /**
     * 有效结束时间
     */
    @NotNull
    @Schema(description = "有效结束时间")
    private Date effectiveEndTime;

    /**
     * 广告图片URL
     */
    @NotBlank
    @Schema(description = "广告图片URL")
    private String adImageUrl;

    /**
     * 点击广告图片跳转的目的地址
     */
    @Schema(description = "点击广告图片跳转的目的地址")
    private String adResourceUrl;

    /**
     * 提示信息
     */
    @Schema(description = "提示信息")
    private String hint;

    /**
     * 广告位编码
     */
    @NotBlank
    @Schema(description = "广告位编码")
    private String spaceCode;

    /**
     * 广告类型
     */
    @NotBlank
    @Schema(description = "广告类型(商品、咨询等)")
    private String adType;

    /**
     * 广告位可填充的广告的类型(商品、咨询等)的id
     */
    @Schema(description = "广告位可填充的广告的类型(商品、咨询等)的id")
    private String adBusinessId;

    /**
     * 广告位可填充的广告的类型(商品、咨询等)的(搜索id的)关键字
     */
    @Schema(description = "广告位可填充的广告的类型(商品、咨询等)的(搜索id的)关键字")
    private String adBusinessTitle;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createUser;
}
