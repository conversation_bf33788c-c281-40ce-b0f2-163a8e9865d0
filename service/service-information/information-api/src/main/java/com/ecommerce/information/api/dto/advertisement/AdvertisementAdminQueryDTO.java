package com.ecommerce.information.api.dto.advertisement;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date: 2018-08-28 17:55
 * @description:
 **/
@Data
public class AdvertisementAdminQueryDTO {
    /**
     * 是否企业广告
     */
    @Schema(description = "是否企业广告")
    private Integer memberAd;

    /**
     * 广告归属会员id
     */
    @Schema(description = "广告归属会员id")
    private String memberId;

    /**
     * 广告归属会员名称
     */
    @Schema(description = "广告归属会员名称")
    private String memberName;

    /**
     * 广告名称
     */
    @Schema(description = "广告名称")
    private String adName;

    /**
     * 显示顺序
     */
    @Schema(description = "显示顺序")
    private Integer orderNumber;

    /**
     * 审批状态
     */
    @Schema(description = "审批状态")
    private Integer checkStatus;

    /**
     * 停用状态 0 未停用  1 已经停用
     */
    @Schema(description = "停用状态 0 未停用  1 已经停用")
    private Integer stoptatus;

    /**
     * 有效开始时间
     */
    @Schema(description = "有效开始时间")
    private Date effectiveStartTime;

    /**
     * 有效结束时间
     */
    @Schema(description = "有效结束时间")
    private Date effectiveEndTime;

    /**
     * 广告图片URL
     */
    @Schema(description = "广告图片URL")
    private String adImageUrl;

    /**
     * 点击广告图片跳转的目的地址
     */
    @Schema(description = "点击广告图片跳转的目的地址")
    private String adResourceUrl;

    /**
     * 提示信息
     */
    @Schema(description = "提示信息")
    private String hint;

    /**
     * 广告位编码
     */
    @Schema(description = "广告位编码")
    private String spaceCode;

    /**
     * 广告类型
     */
    @Schema(description = "广告类型(商品、咨询等)")
    private String adType;

    /**
     * 广告位可填充的广告的类型(商品、咨询等)的id
     */
    @Schema(description = "广告位可填充的广告的类型(商品、咨询等)的id")
    private String adBusinessId;

    /**
     * 广告位可填充的广告的类型(商品、咨询等)的(搜索id的)关键字
     */
    @Schema(description = "广告位可填充的广告的类型(商品、咨询等)的(搜索id的)关键字")
    private String adBusinessTitle;

    /**
     * 页数
     */
    @Schema(description = "页数")
    private Integer pageNum;

    /**
     * 每页条数
     */
    @Schema(description = "每页条数")
    private Integer pageSize;
}
