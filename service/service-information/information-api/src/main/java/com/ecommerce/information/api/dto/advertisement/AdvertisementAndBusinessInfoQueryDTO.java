package com.ecommerce.information.api.dto.advertisement;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 05/04/2019 23:40
 * @DESCRIPTION:
 */
@Data
public class AdvertisementAndBusinessInfoQueryDTO {

    @NotNull
    @Schema(description = "广告位编码集合")
    private List<SpaceCodeAndLimitSize> spaceCodeList;

}
