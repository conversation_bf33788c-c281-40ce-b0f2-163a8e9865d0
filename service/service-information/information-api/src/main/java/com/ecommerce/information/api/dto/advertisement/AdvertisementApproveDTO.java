
package com.ecommerce.information.api.dto.advertisement;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Date: 21/08/2018 15:29
 * @DESCRIPTION:
 */
@Data
public class AdvertisementApproveDTO {

    //广告id
    @NotBlank
    @Schema(description = "广告id")
    private String advertisement;

    @Schema(description = "注释")
    private String comment;

    @Schema(description = "操作者")
    private String operator;

}
