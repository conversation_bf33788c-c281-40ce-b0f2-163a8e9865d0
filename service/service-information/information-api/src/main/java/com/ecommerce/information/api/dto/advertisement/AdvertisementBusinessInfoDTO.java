
package com.ecommerce.information.api.dto.advertisement;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date: 05/04/2019 23:33
 * @DESCRIPTION:
 */
@Data
public class AdvertisementBusinessInfoDTO {

    @Schema(description = "id")
    private String id;

    @Schema(description = "关键字")
    private String keyword;
    @Schema(description = "业务对象DTO 可能是商品")
    private Object businessDTO;
}
