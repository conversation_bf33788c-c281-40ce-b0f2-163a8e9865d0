
package com.ecommerce.information.api.dto.advertisement;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date: 05/04/2019 23:33
 * @DESCRIPTION:
 */
@Data
public class AdvertisementBusinessInfoQueryDTO {

    @Schema(description = "会员id")
    private String searchMemberId;

    @Schema(description = "广告位编码")
    private String spaceCode;

    @Schema(description = "关键字")
    private String keyword;

    @Schema(description = "广告位可填充的广告的类型(商品、咨询等)")
    private String adType;
}
