package com.ecommerce.information.api.dto.advertisement;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 *  审核记录
 *
 * <AUTHOR>
 **/
@Data
public class AdvertisementCheckDTO {

    /**
     * 状态（0 待审批，1 拒绝，2通过，3 进行中，4 已关闭）
     */
    @Schema(description = "状态（0 待审批，1 拒绝，2通过，3 进行中，4 已关闭）")
    private Integer checkStatus;

    /**
     * 审批内容
     */
    @Schema(description = "审批内容")
    private String checkContent;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createUser;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;

}
