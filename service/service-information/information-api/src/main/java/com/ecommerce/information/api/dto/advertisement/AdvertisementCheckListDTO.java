package com.ecommerce.information.api.dto.advertisement;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2018-08-31 19:22
 * @description:
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class AdvertisementCheckListDTO extends AdvertisementDetailDTO{

    /**
     * 审批信息
     */
    @Schema(description = "审批信息")
    List<AdvertisementCheckDTO> checkDTOS;
}
