package com.ecommerce.information.api.dto.advertisement;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date: 20/08/2018 15:53
 * @DESCRIPTION:
 */
@Data
public class AdvertisementDTO {

    /**
     * 主键
     */
    @Schema(description = "主键")
    private String advertisement;

    /**
     * 是否企业广告
     */
    @Schema(description = "是否企业广告")
    private Integer memberAd;

    /**
     * 企业名称
     */
    @Schema(description = "企业名称")
    private String memberId;

    /**
     * 广告归属会员名称
     */
    @Schema(description = "广告归属会员名称")
    private String memberName;

    /**
     * 广告名称
     */
    @Schema(description = "广告名称")
    private String adName;

    /**
     * 显示顺序
     */
    @Schema(description = "显示顺序")
    private Integer orderNumber;

    /**
     * 停用状态 0 未停用  1 已经停用
     */
    @Schema(description = "停用状态 0 未停用  1 已经停用")
    private Integer stopStatus;

    /**
     * 审批状态
     */
    @Schema(description = "审批状态")
    private Integer checkStatus;

    /**
     * 有效开始时间
     */
    @Schema(description = "有效开始时间")
    private Date effectiveStartTime;

    /**
     * 有效结束时间
     */
    @Schema(description = "有效结束时间")
    private Date effectiveEndTime;

    /**
     * 广告图片URL
     */
    @Schema(description = "广告图片URL")
    private String adImageUrl;

    /**
     * 点击广告图片跳转的目的地址
     */
    @Schema(description = "点击广告图片跳转的目的地址")
    private String adResourceUrl;

    /**
     * 提示信息
     */
    @Schema(description = "提示信息")
    private String hint;

    /**
     * 广告位编码
     */
    @Schema(description = "广告位编码")
    private String spaceCode;

    /**
     * 广告类型(来自数据字典配置，所以用varchar32)
     */
    @Schema(description = "广告类型(来自数据字典配置，所以用varchar32)")
    private String adType;

    /**
     * 广告位可填充的广告的类型(商品、咨询等)的id
     */
    @Schema(description = "广告位可填充的广告的类型(商品、咨询等)的id")
    private String adBusinessId;

    /**
     * 广告位可填充的广告的类型(商品、咨询等)的(搜索id的)关键字
     */
    @Schema(description = "广告位可填充的广告的类型(商品、咨询等)的(搜索id的)关键字")
    private String adBusinessTitle;


}
