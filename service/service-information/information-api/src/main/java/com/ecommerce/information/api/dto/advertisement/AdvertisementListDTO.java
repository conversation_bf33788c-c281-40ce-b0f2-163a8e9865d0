package com.ecommerce.information.api.dto.advertisement;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 *  显示广告
 *
 * <AUTHOR>
 **/
@Data
public class AdvertisementListDTO {

    /**
     * 图片高度
     */
    @Schema(description = "图片高度")
    private String image_height;

    /**
     * 图片宽度
     */
    @Schema(description = "图片宽度")
    private String image_width;

    /**
     * 广告数量
     */
    @Schema(description = "广告数量")
    private Integer space_number;

    /**
     * 停用状态 0 未停用  1 已经停用
     */
    @Schema(description = "停用状态 0 未停用  1 已经停用")
    private Integer stop_status;

    /**
     * 图片对象集合
     */
    @Schema(description = "图片对象集合")
    List<AdvertisementDTO> list;
}
