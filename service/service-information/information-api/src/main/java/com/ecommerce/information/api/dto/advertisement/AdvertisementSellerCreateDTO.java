package com.ecommerce.information.api.dto.advertisement;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date: 21/08/2018 15:31
 * @DESCRIPTION:
 */
@Data
public class AdvertisementSellerCreateDTO {

    /**
     * 是否企业广告
     */
    @Schema(description = "是否企业广告")
    private Integer memberAd;

    /**
     * 企业名称
     */
    @Schema(description = "企业名称")
    @NotBlank
    private String memberId;

    /**
     * 广告归属会员名称
     */
    @Schema(description = "广告归属会员名称")
    @NotBlank
    private String memberName;

    /**
     * 广告范围  1店铺，0平台
     */
    @Schema(description = "广告范围  1店铺，0平台")
    @NotNull
    private Integer adRange;

    /**
     * 广告类型(来自数据字典配置，所以用varchar32)
     */
    @Schema(description = "广告类型(来自数据字典配置，所以用varchar32)")
    @NotBlank
    private String adType;

    /**
     * 关联的招标信息id
     */
    @Schema(description = "关联的招标信息id")
    private String tenderingId;


    /**
     * 广告名称
     */
    @Schema(description = "广告名称")
    @NotBlank
    private String adName;

    /**
     * 提示信息
     */
    @Schema(description = "提示信息")
    private String hint;

    /**
     * 显示顺序
     */
    @Schema(description = "显示顺序")
    @NotNull
    private Integer orderNumber;

    /**
     * 有效开始时间
     */
    @Schema(description = "有效开始时间")
    @NotNull
    private Date effectiveStartTime;

    /**
     * 有效结束时间
     */
    @Schema(description = "有效结束时间")
    @NotNull
    private Date effectiveEndTime;

    /**
     * 广告图片URL
     */
    @Schema(description = "广告图片URL")
    @NotBlank
    private String adImageUrl;

    /**
     * 点击广告图片跳转的目的地址
     */
    @Schema(description = "点击广告图片跳转的目的地址")
    @NotBlank
    private String adResourceUrl;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createUser;

    /**
     * 广告位编码
     */
    @Schema(description = "广告位编码")
    @NotBlank
    private String spaceCode;
}
