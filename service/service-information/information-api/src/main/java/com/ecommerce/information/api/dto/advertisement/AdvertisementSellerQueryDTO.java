package com.ecommerce.information.api.dto.advertisement;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

import jakarta.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Date: 21/08/2018 15:29
 * @DESCRIPTION:
 */
@Data
public class AdvertisementSellerQueryDTO {

    /**
     * 企业名称
     */
    @Schema(description = "企业名称")
    @NotBlank
    private String memberId;

    /**
     * 广告名称
     */
    @Schema(description = "广告名称")
    private String adName;


    /**
     * 广告类型(来自数据字典配置，所以用varchar32)
     */
    @Schema(description = "广告类型(来自数据字典配置，所以用varchar32)")
    private String adType;


    /**
     * 审批状态
     */
    @Schema(description = "审批状态")
    private Integer checkStatus;

    /**
     * 停用状态 0 未停用  1 已经停用
     */
    @Schema(description = "停用状态 0 未停用  1 已经停用")
    private Integer stopStatus;

    /**
     * 有效开始时间
     */
    @Schema(description = "有效开始时间")
    private Date effectiveStartTime;

    /**
     * 有效结束时间
     */
    @Schema(description = "有效结束时间")
    private Date effectiveEndTime;

    /**
     * 页数
     */
    @Schema(description = "页数")
    private Integer pageNum;

    /**
     * 每页条数
     */
    @Schema(description = "每页条数")
    private Integer pageSize;
}
