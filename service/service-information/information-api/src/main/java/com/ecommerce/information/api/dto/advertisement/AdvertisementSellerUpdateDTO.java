package com.ecommerce.information.api.dto.advertisement;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date: 21/08/2018 15:32
 * @DESCRIPTION:
 */
@Data
public class AdvertisementSellerUpdateDTO {
    /**
     * 广告id
     */
    @Schema(description = "广告id")
    @NotBlank
    private String advertisement;

    /**
     * 是否企业广告
     */
    @Schema(description = "是否企业广告")
    private Integer memberAd;

    /**
     *
     */
    @Schema(description = "")
    @NotBlank
    private String memberId;

    /**
     * 企业名称
     */
    @Schema(description = "企业名称")
    @NotBlank
    private String memberName;

    /**
     * 广告范围  1店铺，0平台
     */
    @Schema(description = "广告范围  1店铺，0平台")
    @NotNull
    private Integer adRange;

    /**
     * 广告类型
     */
    @Schema(description = "广告类型")
    @NotBlank
    private String adType;

    /**
     * 关联的招标信息id
     */
    @Schema(description = "关联的招标信息id")
    private String tenderingId;

    /**
     * 广告位置(数据字典定义)
     */
    @Schema(description = "广告位置(数据字典定义)")
    @NotBlank
    private String position;

    /**
     * 广告名称
     */
    @Schema(description = "广告名称")
    @NotBlank
    private String adName;

    /**
     * 提示信息
     */
    @Schema(description = "提示信息")
    private String hint;

    /**
     * 显示顺序
     */
    @Schema(description = "显示顺序")
    @NotNull
    private Integer orderNumber;

    /**
     * 有效开始时间
     */
    @Schema(description = "有效开始时间")
    @NotNull
    private Date effectiveStartTime;

    /**
     * 有效结束时间
     */
    @Schema(description = "有效结束时间")
    @NotNull
    private Date effectiveEndTime;

    /**
     * 广告图片URL
     */
    @Schema(description = "广告图片URL")
    @NotBlank
    private String adImageUrl;

    /**
     * 点击广告图片跳转的目的地址
     */
    @Schema(description = "点击广告图片跳转的目的地址")
    @NotBlank
    private String adResourceUrl;

    /**
     * 修改人
     */
    @Schema(description = "修改人")
    private String updateUser;
}
