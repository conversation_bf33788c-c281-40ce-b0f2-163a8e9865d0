package com.ecommerce.information.api.dto.announcement;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

import jakarta.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Date: 25/08/2018 11:46
 * @DESCRIPTION:
 */
@Data
public class AnnouncementApprovalDTO {

    /**
     * 审批状态 0待审批 1审批通过 2审批未通过
     */
    @NotBlank
    @Schema(description = "审批状态 0待审批 1审批通过 2审批未通过")
    private Integer approvalStatus;

    /**
     * 主键
     */
    @NotBlank
    @Schema(description = "主键")
    private String announcementId;

    /**
     * 审批注释
     */
    @NotBlank
    @Schema(description = "审批注释")
    private String comment;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createUser;

    /**
     *
     */
    @Schema(description = "")
    private String createUserName;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 修改人
     */
    @Schema(description = "修改人")
    private String updateUser;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    private Date updateTime;


}
