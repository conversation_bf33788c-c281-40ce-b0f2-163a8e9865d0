package com.ecommerce.information.api.dto.announcement;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date: 25/08/2018 11:46
 * @DESCRIPTION:
 */
@Schema(name = "公告DTO")
@Data
public class AnnouncementDTO {
    /**
     * id
     */
    @Schema(description = "id")
    private String announcementId;

    /**
     * 标题
     */
    @Schema(description = "标题")
    private String title;

    /**
     * 生效开始时间
     */
    @Schema(description = "生效开始时间")
    private Date startTime;

    /**
     * 生效结束时间
     */
    @Schema(description = "生效结束时间")
    private Date endTime;

    /**
     * 进行状态０未开始　3 进行中，4 已关闭"
     */
    @Schema(description = "进行状态０未开始　3 进行中，4 已关闭")
    private Integer processStatus=0;

    /**
     * 公告内容url
     */
    @Schema(description = "公告内容url")
    private String contentUrl;

    /**
     * 关键字
     */
    @Schema(description = "关键字")
    private String keywords;

    /**
     * 公告内容
     */
    @Schema(description = "公告内容")
    private String content;

    /**
     * 可用状态 0启用  1暂停
     */
    @Schema(description = "可用状态 0启用  1暂停")
    private Integer availableStatus;

    /**
     * 审批状态 0待审批 1审批通过 2审批未通过
     */
    @Schema(description = "审批状态 0待审批 1审批通过 2审批未通过")
    private Integer approvalStatus;

    /**
     * 公告类型 0 默认  1 弹框公告 2轮播公告
     */
    @Schema(description = "公告类型 0 默认  1 弹框公告 2轮播公告")
    private Integer announcementType;

    /**
     * 排序序号
     */
    @Schema(description = "排序序号")
    private Integer weight;

    /**
     * 删除状态
     */
    @Schema(description = "删除状态")
    private Integer delFlg;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createUser;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 修改人
     */
    @Schema(description = "修改人")
    private String updateUser;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    private Date updateTime;
}

