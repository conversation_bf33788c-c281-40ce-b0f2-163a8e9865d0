package com.ecommerce.information.api.dto.announcement;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date: 25/08/2018 11:47
 * @DESCRIPTION:
 */
@Data
public class AnnouncementIndexQueryDTO {

    /**
     * 公告类型 0 默认  1 弹框公告 2轮播公告
     */
    @Schema(description = "公告类型 0 默认  1 弹框公告 2轮播公告")
    private Integer announcementType;

    private int size=3;
}
