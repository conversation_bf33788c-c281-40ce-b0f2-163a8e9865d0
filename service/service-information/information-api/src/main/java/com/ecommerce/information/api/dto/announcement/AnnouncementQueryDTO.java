
package com.ecommerce.information.api.dto.announcement;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date: 25/08/2018 11:47
 * @DESCRIPTION:
 */
@Data
public class AnnouncementQueryDTO {


    /**
     * 审批状态 0待审批 1审批通过 2审批未通过
     */
    @Schema(description = "审批状态 0待审批 1审批通过 2审批未通过")
    private Integer approvalStatus;

    /**
     * 公告类型 0 默认  1 弹框公告 2轮播公告
     */
    @Schema(description = "公告类型 0 默认  1 弹框公告 2轮播公告")
    private Integer announcementType;

    /**
     * 生效开始时间
     */
    @Schema(description = "生效开始时间")
    private Date startTime;

    /**
     * 生效结束时间
     */
    @Schema(description = "生效结束时间")
    private Date endTime;

    /**
     * 标题
     */
    @Schema(description = "标题")
    private String title;

    /**
     * 关键字
     */
    @Schema(description = "关键字")
    private String keywords;

    /**
     * 可用状态 0启用  1暂停
     */
    @Schema(description = "可用状态 0启用  1暂停")
    private Integer availableStatus;

    @Schema(description = "页数")
    private Integer pageNumber = 0;
    @Schema(description = "每页条数")
    private Integer pageSize = 10;
}
