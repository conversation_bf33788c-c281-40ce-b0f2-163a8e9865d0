package com.ecommerce.information.api.dto.announcement;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date: 25/08/2018 11:46
 * @DESCRIPTION:
 */
@Data
public class AnnouncementUpdateDTO {


    /**
     * 主键
     */
    @Schema(description = "主键")
    private String announcementId;

    /**
     * 公告类型 0 默认  1 弹框公告 2轮播公告
     */
    @NotNull
    @Schema(description = "公告类型 0 默认  1 弹框公告 2轮播公告")
    private Integer announcementType;

    /**
     * 标题
     */
    @NotBlank
    @Schema(description = "标题")
    private String title;

    /**
     * 生效开始时间
     */
    @Schema(description = "开始生效时间")
    private Date startTime;

    /**
     * 生效结束时间
     */
    @Schema(description = "生效结束时间")
    private Date endTime;

    /**
     * 公告内容url
     */
    @Schema(description = "公告内容url")
    private String contentUrl;

    /**
     * 排序序号
     */
    @Schema(description = "排序序号")
    private Integer weight;

    /**
     * 关键字
     */
    @NotBlank
    @Schema(description = "关键字")
    private String keywords;

    /**
     * 公告内容
     */
    @Schema(description = "公告内容")
    private String content;

    /**
     * 操作者
     */
    @Schema(description = "操作者")
    private String operator;
}
