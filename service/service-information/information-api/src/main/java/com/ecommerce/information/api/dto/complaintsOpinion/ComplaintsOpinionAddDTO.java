package com.ecommerce.information.api.dto.complaintsOpinion;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

import jakarta.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Date: 29/11/2018 15:32
 * @DESCRIPTION:
 */
@Data
public class ComplaintsOpinionAddDTO {
    /**
     * 投诉类型
     */
    @NotBlank
    @Schema(description = "投诉类型")
    private String type;

    /**
     * 投诉类型名称
     */
    @Schema(description = "投诉类型名称")
    private String typeName;

    /**
     * 投诉方会员id
     */
    @NotBlank
    @Schema(description = "投诉方会员id")
    private String createMemberId;

    /**
     * 投诉方会员名称
     */
    @Schema(description = "投诉方会员名称")
    private String createMemberName;

    /**
     * 投诉方联系电话
     */
    @Schema(description = "投诉方联系电话")
    private String createPhone;

    /**
     * 订单号
     */
    @Schema(description = "订单号")
    private String orderNum;

    /**
     * 被投诉方会员id
     */
    @NotBlank
    @Schema(description = "被投诉方会员id")
    private String defendantMemberId;

    /**
     * 被投诉方会员名称
     */
    @Schema(description = "被投诉方会员名称")
    private String defendantMemberName;

    /**
     * 投诉内容关键字
     */
    @Schema(description = "投诉内容关键字")
    private String contentKeyword;

    /**
     * 投诉内容url
     */
    @Schema(description = "投诉内容url")
    private String contentUrl;

    /**
     * 投诉内容
     */
    @Schema(description = "投诉内容")
    private String content;

    /**
     * 是否需要平台介入
     */
    @Schema(description = "是否需要平台介入")
    private Integer needPlatform = 0;

    /**
     * 附件id集合
     */
    @Schema(description = "附件id集合")
    private List<String> attachIdList;

    /**
     * 投诉建议人
     */
    @NotBlank
    @Schema(description = "投诉建议人")
    private String createUser;
}
