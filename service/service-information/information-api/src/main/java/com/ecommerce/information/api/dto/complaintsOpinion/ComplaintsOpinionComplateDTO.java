package com.ecommerce.information.api.dto.complaintsOpinion;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Date: 29/11/2018 09:35
 * @DESCRIPTION:
 */
@Data
public class ComplaintsOpinionComplateDTO {

    /**
     * 投诉建议id
     */
    @NotBlank
    @Schema(description = "投诉建议id")
    private String complaintsOpinionId;

    /**
     * 投诉状态 1关闭　2取消
     */
    @Schema(description = " 投诉状态 1关闭　2取消")
    private Integer status;

    /**
     * 操作者
     */
    @NotBlank
    @Schema(description = "操作者")
    private String operator;
}
