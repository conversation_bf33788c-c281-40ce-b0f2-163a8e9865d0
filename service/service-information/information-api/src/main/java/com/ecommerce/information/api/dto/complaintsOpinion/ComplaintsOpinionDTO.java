package com.ecommerce.information.api.dto.complaintsOpinion;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Schema(name = "投诉建议")
@Data
public class ComplaintsOpinionDTO {

	/**
	 * id
	 */
	@Schema(description = "id")
	private String complaintsOpinionId;

	/**
	 * 投诉类型
	 */
	@Schema(description = "投诉类型")
	private String type;

	/**
	 * 投诉类型名称
	 */
	@Schema(description = "投诉类型名称")
	private String typeName;

	/**
	 * 投诉状态 0进行中 1已完结 2已取消
	 */
	@Schema(description = "投诉状态 0进行中 1已完结 2已取消")
	private Integer status;

	/**
	 * 投诉方会员id
	 */
	@Schema(description = "投诉方会员id")
	private String createMemberId;

	/**
	 * 投诉方会员名称
	 */
	@Schema(description = "投诉方会员名称")
	private String createMemberName;

	/**
	 * 投诉方联系电话
	 */
	@Schema(description = "投诉方联系电话")
	private String createPhone;

	/**
	 * 订单号
	 */
	@Schema(description = "订单号")
	private String orderNum;

	/**
	 * 被投诉方会员id
	 */
	@Schema(description = "被投诉方会员id")
	private String defendantMemberId;


	/**
	 * 被投诉方会员名称
	 */
	@Schema(description = "被投诉方会员名称")
	private String defendantMemberName;

	/**
	 * 投诉内容关键字
	 */
	@Schema(description = "投诉内容关键字")
	private String contentKeyword;

	/**
	 * 投诉内容url
	 */
	@Schema(description = "投诉内容url")
	private String contentUrl;

	/**
	 * 投诉内容
	 */
	@Schema(description = "投诉内容")
	private String content;

	/**
	 * 附件id集合
	 */
	@Schema(description = "附件id集合")
	private List<String> attachIdList;

	/**
	 * 投诉建议人
	 */
	@Schema(description = "投诉建议人")
	private String createUser;


	/**
	 * 投诉建议创建时间
	 */
	@Schema(description = "投诉建议创建时间")
	private Date createTime;


	/**
	 * 修改人
	 */
	@Schema(description = "修改人")
	private String updateUser;


	/**
	 * 修改时间
	 */
	@Schema(description = "修改时间")
	private Date updateTime;

}
