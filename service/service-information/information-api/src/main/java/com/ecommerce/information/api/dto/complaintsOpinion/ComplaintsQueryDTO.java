
package com.ecommerce.information.api.dto.complaintsOpinion;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date: 29/11/2018 09:31
 * @DESCRIPTION:
 */
@Data
public class ComplaintsQueryDTO {

    /**
     * 投诉类型(数据字典) eq
     */
    @Schema(description = "投诉类型(数据字典) eq")
    private String type;

    /**
     * 投诉状态 eq　0正在处理 1已完成 2已取消
     */
    @Schema(description = "投诉状态 eq　0正在处理 1已完成 2已取消")
    private Integer status;

    /**
     * 投诉方会员名称　eq
     */
    @Schema(description = "投诉方会员名称　eq")
    private String createMemberId;

    /**
     * "投诉方会员名称　like
     */
    @Schema(description = "投诉方会员名称　like")
    private String createMemberName;

    /**
     * 被投诉方会员名称　eq
     */
    @Schema(description = "被投诉方会员名称　eq")
    private String defendantMemberId;

    /**
     * 被投诉方会员名称　like
     */
    @Schema(description = "被投诉方会员名称　like")
    private String defendantMemberName;

    /**
     * 订单号 eq
     */
    @Schema(description = "订单号 eq")
    private String orderNum;

    /**
     * 投诉内容关键字 like
     */
    @Schema(description = "投诉内容关键字 like")
    private String contentKeyword;

    /**
     * 是否需要平台参与 0 不需要 1需要 2平台已经参与
     */
    @Schema(description = "是否需要平台参与 0 不需要 1需要 2平台已经参与")
    private Integer needPlatform;

    @Schema(description = "每页条数")
    private Integer pageSize;
    @Schema(description = "页码")
    private Integer pageNum;
}
