package com.ecommerce.information.api.dto.complaintsOpinion;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

import jakarta.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Date: 29/11/2018 15:39
 * @DESCRIPTION:
 */
@Data
public class ProcessingRecordsAddDTO {
    /**
     * 投诉意见id
     */
    @NotBlank
    @Schema(description = "投诉意见id")
    private String complaintsOpinionId;

    /**
     * 处理人会员id
     */
    @NotBlank
    @Schema(description = "处理人会员id")
    private String processMemberId;

    /**
     * 处理人会员名称
     */
    @Schema(description = "处理人会员名称")
    private String processMemberName;

    /**
     * 处理意见Url
     */
    @Schema(description = "处理意见Url")
    private String processContentUrl;

    /**
     * 处理意见
     */
    @Schema(description = "处理意见")
    private String processContent;

    /**
     * 附件id集合
     */
    @Schema(description = "附件id集合")
    private List<String> attachIdList;

    /**
     * 处理人
     */
    @NotBlank
    @Schema(description = "处理人")
    private String createUser;
}
