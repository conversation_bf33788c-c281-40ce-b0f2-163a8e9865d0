package com.ecommerce.information.api.dto.complaintsOpinion;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Schema(name = "投诉意见处理记录")
@Data
public class ProcessingRecordsDTO implements Serializable {

    /**
     * id
     */
    @Schema(description = "id")
    private String complaintsOpinionRecordId;

    /**
     * 投诉意见id
     */
    @Schema(description = "投诉意见id")
    private String complaintsOpinionId;

    /**
     * 处理方
     */
    @Schema(description = "处理方")
    private String processType;

    /**
     * 处理人会员id
     */
    @Schema(description = "处理人会员id")
    private String processMemberId;

    /**
     * 处理人会员名称
     */
    @Schema(description = "处理人会员名称")
    private String processMemberName;

    /**
     * 处理意见Url
     */
    @Schema(description = "处理意见Url")
    private String processContentUrl;

    /**
     * 处理意见
     */
    @Schema(description = "处理意见")
    private String processContent;

    /**
     * 附件id集合
     */
    @Schema(description = "附件id集合")
    private List<String> attachIdList;

    /**
     * 处理人
     */
    @Schema(description = "处理人")
    private String createUser;

    /**
     * 处理时间
     */
    @Schema(description = "处理时间")
    private Date createTime;

}