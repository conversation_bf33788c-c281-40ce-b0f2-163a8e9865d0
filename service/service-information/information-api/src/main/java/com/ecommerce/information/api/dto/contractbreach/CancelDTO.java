package com.ecommerce.information.api.dto.contractbreach;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Date: 23/11/2018 16:20
 * @DESCRIPTION:
 */
@Schema(name = "取消违约")
@Data
public class CancelDTO {

    /**
     * id
     */
    @NotBlank
    @Schema(description = "id")
    private String contractBreachRecordId;

    /**
     * 操作人会员id
     */
    @NotBlank
    @Schema(description = "操作人会员id")
    private String memberId;

    /**
     * 操作人会员代码
     */
    @Schema(description = "操作人会员代码")
    private String memberCode;

    /**
     * 操作人会员名称
     */
    @Schema(description = "操作人会员名称")
    private String memberName;

    /**
     * 原因
     */
    @Schema(description = "原因")
    private String reason;

    /**
     * 修改人
     */
    @NotBlank
    @Schema(description = "修改人")
    private String updateUser;

}
