package com.ecommerce.information.api.dto.contractbreach;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 违约管理
 *
 * <AUTHOR>
 */
@Schema(name = "违约配置")
@Data
public class ContractBreachConfigDTO {

    /**
     * id
     */
    @Schema(description = "id")
    private String contractBreachConfigId;

    /**
     * 规则类型(0 卖家 1平台
     */
    @Schema(description = "规则类型(0 卖家 1平台")
    private Integer configType;

    /**
     * 规则归属会员
     */
    @Schema(description = "规则归属会员")
    private String memberId;

    /**
     * 规则归属会员名称
     */
    @Schema(description = "规则归属会员名称")
    private String memberName;

    /**
     * 规则归属会员代码
     */
    @Schema(description = "规则归属会员代码")
    private String memberCode;

    /**
     * 规则针对客户
     */
    @Schema(description = "规则针对客户")
    private String customerId;

    /**
     * 规则针对客户名称
     */
    @Schema(description = "规则针对客户名称")
    private String customerName;

    /**
     * 规则针对客户代码
     */
    @Schema(description = "规则针对客户代码")
    private String customerCode;

    /**
     * 挂牌违约开关
     */
    @Schema(description = "挂牌违约开关")
    private Boolean onSaleFlg;

    /**
     * 挂牌违约次数
     */
    @Schema(description = "挂牌违约次数")
    private Integer onSaleNum;

    /**
     * 挂牌违约规则描述
     */
    @Schema(description = "挂牌违约规则描述")
    private String onSaleDesc;

    /**
     * 团购未提货开关
     */
    @Schema(description = "团购未提货开关")
    private Boolean groupBuyNotPickUpFlg;

    /**
     * 团购未提货次数
     */
    @Schema(description = "团购未提货次数")
    private Integer groupBuyNotPickUpNum;

    /**
     * 团购未提货规则描述
     */
    @Schema(description = "团购未提货规则描述")
    private String groupBuyNotPickUpDesc;

    /**
     * 提交未支付开关
     */
    @Schema(description = "提交未支付开关")
    private Boolean buyUnpaidFlg;

    /**
     * 提交未支付次数
     */
    @Schema(description = "提交未支付次数")
    private Integer buyUnpaidNum;

    /**
     * 提交未支付规则描述
     */
    @Schema(description = "提交未支付规则描述")
    private String buyUnpaidDesc;

    /**
     * 支付未提货开关
     */
    @Schema(description = "支付未提货开关")
    private Boolean paidNotPickUpFlg;

    /**
     * 支付未提货次数
     */
    @Schema(description = "支付未提货次数")
    private Integer paidNotPickUpNum;

    /**
     * 支付未提货规则描述
     */
    @Schema(description = "支付未提货规则描述")
    private String paidNotPickUpDesc;

    /**
     * 融资违约开关
     */
    @Schema(description = "融资违约开关")
    private Boolean financingAuthFlg;

    /**
     * 融资违约次数
     */
    @Schema(description = "融资违约次数")
    private Integer financingAuthNum;

    /**
     * 融资违约规则描述
     */
    @Schema(description = "融资违约规则描述")
    private String financingAuthDesc;

    /**
     * 预支付违约开关
     */
    @Schema(description = "预支付违约开关")
    private Boolean prepaymentFlg;

    /**
     * 预支付违约次数
     */
    @Schema(description = "预支付违约次数")
    private Integer prepaymentNum;

    /**
     * 预支付违约规则描述
     */
    @Schema(description = "预支付违约规则描述")
    private String prepaymentDesc;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createUser;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 修改人
     */
    @Schema(description = "修改人")
    private String updateUser;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    private Date updateTime;
}
