
package com.ecommerce.information.api.dto.contractbreach;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date: 23/11/2018 16:18
 * @DESCRIPTION:
 */
@Data
public class ContractBreachConfigQueryDTO {

    /**
     * 违约配置表id
     */

    @Schema(description = "违约配置表id")
    private String contractBreachConfigId;
    @Schema(description = "规则类型(卖家／平台)")
    private Integer configType;
    @Schema(description = "规则归属会员")
    private String memberId;
    @Schema(description = " 规则归属会员名称 like")
    private String memberName;
    @Schema(description = "会员code")
    private String memberCode;
    @Schema(description = "规则针对客户")
    private String customerId;
    @Schema(description = "规则针对客户名称 like")
    private String customerName;
    @Schema(description = "规则针对客户代码")
    private String customerCode;

    @Schema(description = "每页条数")
    private Integer pageSize;
    @Schema(description = "页码")
    private Integer pageNum;

}
