package com.ecommerce.information.api.dto.contractbreach;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Date: 24/11/2018 14:01
 * @DESCRIPTION:
 */
@Data
public class ContractBreachConfigUpdateDTO {

    @NotBlank
    @Schema(description = "id")
    private String contractBreachConfigId;

    @Schema(description = "规则类型(0 卖家 1平台")
    private Integer configType;

    @Schema(description = "规则归属会员")
    private String memberId;

    @Schema(description = "规则归属会员名称")
    private String memberName;

    @Schema(description = "规则归属会员代码")
    private String memberCode;

    @Schema(description = "规则针对客户")
    private String customerId;

    @Schema(description = "规则针对客户名称")
    private String customerName;

    @Schema(description = "规则针对客户代码")
    private String customerCode;

    @Schema(description = "挂牌违约开关")
    private Boolean onSaleFlg;

    @Schema(description = "挂牌违约次数")
    private Integer onSaleNum;

    @Schema(description = "挂牌违约规则描述")
    private String onSaleDesc;

    @Schema(description = "团购未提货开关")
    private Boolean groupBuyNotPickUpFlg;

    @Schema(description = "团购未提货次数")
    private Integer groupBuyNotPickUpNum;

    @Schema(description = "团购未提货规则描述")
    private String groupBuyNotPickUpDesc;

    @Schema(description = "提交未支付开关")
    private Boolean buyUnpaidFlg;

    @Schema(description = "提交未支付次数")
    private Integer buyUnpaidNum;

    @Schema(description = "提交未支付规则描述")
    private String buyUnpaidDesc;

    @Schema(description = "支付未提货开关")
    private Boolean paidNotPickUpFlg;

    @Schema(description = "支付未提货次数")
    private Integer paidNotPickUpNum;

    @Schema(description = "支付未提货规则描述")
    private String paidNotPickUpDesc;

    @Schema(description = "融资违约开关")
    private Boolean financingAuthFlg;

    @Schema(description = "融资违约次数")
    private Integer financingAuthNum;

    @Schema(description = "融资违约规则描述")
    private String financingAuthDesc;

    @Schema(description = "预支付违约开关")
    private Boolean prepaymentFlg;

    @Schema(description = "预支付违约次数")
    private Integer prepaymentNum;

    @Schema(description = "预支付违约规则描述")
    private String prepaymentDesc;

    @NotBlank
    @Schema(description = "修改人")
    private String updateUser;
}
