package com.ecommerce.information.api.dto.contractbreach;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date: 23/11/2018 16:21
 * @DESCRIPTION:
 */
@Data
public class ContractBreachRecordCheckDTO {

    @Schema(description = "id")
    private String contractBreachRecordProcessId;

    @Schema(description = "违约记录表id")
    private String contractBreachRecordId;

    @Schema(description = "操作人会员id")
    private String memberId;

    @Schema(description = "操作人会员代码")
    private String memberCode;

    @Schema(description = "操作人会员名称")
    private String memberName;

    @Schema(description = "操作类型")
    private String operateType;

    @Schema(description = "操作结果")
    private String operateComment;

    @Schema(description = "创建人")
    private String createUser;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "修改人")
    private String updateUser;

    @Schema(description = "修改时间")
    private Date updateTime;
}
