
package com.ecommerce.information.api.dto.contractbreach;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Date: 24/11/2018 15:23
 * @DESCRIPTION:
 */
@Data
public class ContractBreachRecordCheckQueryDTO {

    @NotBlank
    @Schema(description = "违约记录表id")
    private String contractBreachRecordId;

    @Schema(description = "每页条数")
    private Integer pageSize;
    @Schema(description = "页码")
    private Integer pageNum;
}
