package com.ecommerce.information.api.dto.contractbreach;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Date: 23/11/2018 16:20
 * @DESCRIPTION:
 */
@Data
public class ContractBreachRecordCheckUpdateDTO {
    @NotBlank
    @Schema(description = "id")
    private String contractBreachRecordProcessId;

    @NotBlank
    @Schema(description = "违约记录表id")
    private String contractBreachRecordId;

    @NotBlank
    @Schema(description = "操作人会员id")
    private String memberId;

    @Schema(description = "操作人会员代码")
    private String memberCode;

    @Schema(description = "操作人会员名称")
    private String memberName;

    @Schema(description = "操作类型")
    private String operateType;

    @Schema(description = "操作结果")
    private String operateComment;

    @NotBlank
    @Schema(description = "修改人")
    private String updateUser;
}
