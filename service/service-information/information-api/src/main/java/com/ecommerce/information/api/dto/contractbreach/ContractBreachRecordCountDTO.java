package com.ecommerce.information.api.dto.contractbreach;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date: 24/11/2018 16:23
 * @DESCRIPTION:
 */
@Schema(name = "违约次数统计")
@Data
public class ContractBreachRecordCountDTO {

    @Schema(description = "规则针对客户")
    private String customerId;

    @Schema(description = "规则针对客户名称")
    private String customerName;

    @Schema(description = "规则针对客户代码")
    private String customerCode;

    @Schema(description = "挂牌违约次数")
    private Long onSaleNum = 0L;

    @Schema(description = "团购未违约次数")
    private Long groupBuyNotPickUpNum = 0L;

    @Schema(description = "提交未支付违约次数")
    private Long buyUnpaidNum = 0L;

    @Schema(description = "支付未提货违约次数")
    private Long paidNotPickUpNum = 0L;

    @Schema(description = "融资违约违约次数")
    private Long financingAuthNum = 0L;

    @Schema(description = "预支付违约违约次数")
    private Long prepaymentNum = 0L;

    @Schema(description = "合计")
    private Long total;
}
