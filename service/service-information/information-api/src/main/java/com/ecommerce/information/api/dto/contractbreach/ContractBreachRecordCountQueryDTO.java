
package com.ecommerce.information.api.dto.contractbreach;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date: 24/11/2018 16:24
 * @DESCRIPTION:
 */
@Data
public class ContractBreachRecordCountQueryDTO {

    @Schema(description = "规则类型")
    private Integer configType;
    @Schema(description = "会员id")
    private String memberId;
    @Schema(description = " like")
    private String memberName;
    @Schema(description = "会员code")
    private String memberCode;
    @Schema(description = "规则针对客户")
    private String customerId;
    @Schema(description = " like")
    private String customerName;
    @Schema(description = "规则针对客户代码")
    private String customerCode;

    @Schema(description = "违约次数大于等于")
    private Integer countGreateThen;

    @Schema(description = "违约次数小于等于")
    private Integer countLessThen;

    @Schema(description = "订单时间大于等于")
    private Date orderTimeGreaterThan;

    @Schema(description = "订单时间小于等于")
    private Date orderTimeLessThan;

    @Schema(description = "每页条数")
    private Integer pageSize;
    @Schema(description = "页码")
    private Integer pageNum;
    @Schema(description = "")
    private Integer pageStar;
}
