package com.ecommerce.information.api.dto.contractbreach;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

import jakarta.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Date: 23/11/2018 16:19
 * @DESCRIPTION:
 */
@Data
public class ContractBreachRecordCreateDTO {


    @Schema(description = "规则类型")
    private Integer configType;

    @Schema(description = "规则归属会员id")
    private String memberId;

    @Schema(description = "规则归属会员代码")
    private String memberCode;

    @Schema(description = "规则归属会员名称")
    private String memberName;

    @Schema(description = "规则针对客户")
    private String customerId;

    @Schema(description = "规则针对客户名称")
    private String customerName;

    @Schema(description = "规则针对客户代码")
    private String customerCode;

    @Schema(description = "交易订单号")
    private String orderNumber;

    @Schema(description = "订单时间")
    private Date orderTime;

    @Schema(description = "商品类型")
    private String goodsType;

    @Schema(description = "商品类型代码")
    private String goodsTypeCode;

    @Schema(description = "违约规则")
    private String ruleName;

    @Schema(description = "违约原因")
    private String reason;

    @NotBlank
    @Schema(description = "创建人")
    private String createUser;


}
