
package com.ecommerce.information.api.dto.contractbreach;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date: 23/11/2018 16:19
 * @DESCRIPTION:
 */
@Data
public class ContractBreachRecordQueryDTO {

    @Schema(description = "违约记录表id")
    private String contractBreachRecordId;
    @Schema(description = "规则类型")
    private Integer configType;
    @Schema(description = "会员id")
    private String memberId;
    @Schema(description = " like")
    private String memberName;
    @Schema(description = "会员code")
    private String memberCode;
    @Schema(description = "规则针对客户")
    private String customerId;
    @Schema(description = "客户名称 like")
    private String customerName;
    @Schema(description = "规则针对客户代码")
    private String customerCode;

    @Schema(description = "显示顺序")
    private String orderNumber;
    @Schema(description = "商品类型")
    private String goodsType;
    @Schema(description = "商品类型代码")
    private String goodsTypeCode;

    @Schema(description = "订单时间大于等于")
    private Date orderTimeGreaterThan;

    @Schema(description = "订单时间小于等于")
    private Date orderTimeLessThan;

    @Schema(description = "违约规则")
    private String ruleName;
    @Schema(description = "理由")
    private String reason;
    @Schema(description = "违约取消状态")
    private Boolean cancelFlg;

    @Schema(description = "每页条数")
    private Integer pageSize;
    @Schema(description = "页码")
    private Integer pageNum;
}
