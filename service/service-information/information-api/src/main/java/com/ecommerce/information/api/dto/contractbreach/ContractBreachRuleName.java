package com.ecommerce.information.api.dto.contractbreach;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 规则名称
 *
 * <AUTHOR>
 */
@Schema(name = "规则名称")
public class ContractBreachRuleName {
    @Schema(description = "违约规则名称-挂牌违约")
    public static final String RULE_NAME_ONSALE = "onSale";
    @Schema(description = "违约规则名称-团购未提货")
    public static final String RULE_NAME_GROUPBUYNOTPICKUP = "groupBuyNotPickUp";
    @Schema(description = "违约规则名称-提交未支付")
    public static final String RULE_NAME_BUYUNPAID = "buyUnpaid";
    @Schema(description = "违约规则名称-支付未提货")
    public static final String RULE_NAME_PAIDNOTPICKU = "paidNotPickUp";
    @Schema(description = "违约规则名称-融资违约")
    public static final String RULE_NAME_FINANCINGAUTH = "financingAuth";
    @Schema(description = "违约规则名称-预支付违约")
    public static final String RULE_NAME_PREPAYMENT = "prepayment";
}
