package com.ecommerce.information.api.dto.exception;

import com.alibaba.fastjson.JSON;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.exception.CodeMeta;
import com.google.common.collect.ImmutableMap;

/**
 * <AUTHOR>
 * @Date: 07/10/2018 16:58
 * @DESCRIPTION:
 */
public class InformationBizException extends BizException {

    public InformationBizException(CodeMeta errorCode) {
        super(errorCode);
    }

    public InformationBizException(CodeMeta errorCode, Object... args) {
        super(errorCode,args);
    }

    @Override
    public String getMessage() {
        return JSON.toJSONString(ImmutableMap.of("code",getErrorCode().getCode(),"name",getErrorCode().getName(),"message",super.getMessage()));
    }

    public String getOnlyMessage(){
        return super.getMessage();
    }
}
