package com.ecommerce.information.api.dto.indexPage;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @created 11:24 23/01/2019
 * @description TODO
 */
@Data
public class IndexPageInfoDTO {

    /**
     * id
     */
    @Schema(description = "id")
    private String indexPageInfoId;

    /**
     * 类型编号
     */
    @Schema(description = "类型编号")
    private String typeCode;

    /**
     * 类型
     */
    @Schema(description = "类型")
    private String typeText;

    /**
     * 图标
     */
    @Schema(description = "图标")
    private String iconUrl;

    /**
     * 标题
     */
    @Schema(description = "标题")
    private String title;

    /**
     * 图片地址
     */
    @Schema(description = "图片地址")
    private String imageUrl;

    /**
     * 跳转地址
     */
    @Schema(description = "跳转地址")
    private String redirectUrl;

    /**
     * 推荐指数
     */
    @Schema(description = "推荐指数")
    private Integer star;

    /**
     * 来源方ID
     */
    @Schema(description = "来源方ID")
    private String originId;

    /**
     * 显示顺序
     */
    @Schema(description = "显示顺序")
    private Integer displayOrder;

    /**
     * 显示类别
     */
    @Schema(description = "显示类别")
    private String displayType;

    /**
     * 发布时间
     */
    @Schema(description = "发布时间")
    private Date publishTime;

    /**
     * 扩展
     */
    @Schema(description = "预留")
    private String ext2;

    /**
     * 内容
     */
    @Schema(description = "内容")
    private String content;
}
