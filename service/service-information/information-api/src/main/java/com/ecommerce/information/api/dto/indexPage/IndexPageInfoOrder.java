package com.ecommerce.information.api.dto.indexPage;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @created 14:10 23/01/2019
 * @description TODO
 */
@Data
public class IndexPageInfoOrder {

    /**
     * id
     */
    @Schema(description = "id")
    private String indexPageInfoId;

    /**
     * 显示顺序
     */
    @Schema(description = "显示顺序")
    private Integer displayOrder;

}
