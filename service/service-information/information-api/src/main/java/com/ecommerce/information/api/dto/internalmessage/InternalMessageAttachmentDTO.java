package com.ecommerce.information.api.dto.internalmessage;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class InternalMessageAttachmentDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = -8202456293484039861L;

    @Schema(description = "附件id")
    private String attachmentId;

    @Schema(description = "附件url")
    private String url;

    @Schema(description = "附件名称")
    private String name;

}
