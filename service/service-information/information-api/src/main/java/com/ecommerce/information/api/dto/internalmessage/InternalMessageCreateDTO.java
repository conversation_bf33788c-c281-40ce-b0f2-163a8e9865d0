
package com.ecommerce.information.api.dto.internalmessage;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

import jakarta.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 25/08/2018 18:45
 * @DESCRIPTION:
 */
@Data
public class InternalMessageCreateDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 9201728177503267946L;

    @Schema(description = "消息接收人账户id集合")
    private List<String> receiveAccountIds;

    @Schema(description = "收信人会员id集合(如果不为空，则表示这些会员下的所有人（此时receiveAccountIds无效）)")
    private List<String> receiveMemberIds;

    @Schema(description = "消息标题")
    @NotBlank
    private String title;

    @Schema(description = "消息附件url")
    private String attachmentUrl;

    @Schema(description = "消息内容")
    private String content;

    @Schema(description = "消息内容URL(与消息内容二者不能都为空)")
    private String contentUrl;

    @Schema(description = "操作者")
    private String operator;

    @Schema(description = "消息id")
    private String messageHistoryMqId;
}
