package com.ecommerce.information.api.dto.internalmessage;

import io.swagger.v3.oas.annotations.media.Schema;
import com.ecommerce.common.utils.CsStringUtils;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;


@Data
public class InternalMessageDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = -1033934568624934717L;

    @Schema(description = "消息ID")
    private String id;

    @Schema(description = "收信人id")
    private String receiveUserId;

    @Schema(description = "收信人")
    private String receiveUserName;

    @Schema(description = "收件会员id")
    private String receiveMemberId;

    @Schema(description = "收件会员id")
    private String receiveMemberName;

    @Schema(description = "状态 0 未读 1 已读")
    private Integer status;

    @Schema(description = "创建时间")
    private Date createTime;


    @Schema(description = "消息标题")
    private String title;

    @Schema(description = "消息内容")
    private String content;

    @Schema(description = "消息内容前10个字符+...")
    private String contentShort;

    @Schema(description = "消息内容URL")
    private String contentUrl;

    @Schema(description = "附件集合")
    private List<InternalMessageAttachmentDTO> attachmentList;

    @Schema(description = "发信人id")
    private String senderId;

    @Schema(description = "发信人姓名")
    private String senderName;

    @Schema(description = "发信人会员id")
    private String senderMemberId;

    @Schema(description = "发信人会员名")
    private String senderMemberName;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
        if (CsStringUtils.isNotBlank(content)) {
            contentShort = content.length() > 23 ? content.substring(0,20)+"..." : content;
        }
    }

    public String getContentShort() {
        return contentShort;
    }

    public void setContentShort(String contentShort) {
        //do nothing
    }
}
