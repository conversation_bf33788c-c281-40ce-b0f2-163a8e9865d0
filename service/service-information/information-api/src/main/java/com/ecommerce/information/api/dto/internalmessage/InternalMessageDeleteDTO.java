package com.ecommerce.information.api.dto.internalmessage;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 25/08/2018 18:45
 * @DESCRIPTION:
 */
@Data
public class InternalMessageDeleteDTO {
    /**
     * 消息ID
     */
    @Schema(description = "消息ID")
    @NotNull
    private List<String> ids;

    /**
     * 操作者
     */
    @Schema(description = "操作者")
    private String operator;

    /**
     * 会员id
     */
    @Schema(description = "会员id")
    private String memberId;

    @Schema(description = "账号id")
    private String accountId;
}
