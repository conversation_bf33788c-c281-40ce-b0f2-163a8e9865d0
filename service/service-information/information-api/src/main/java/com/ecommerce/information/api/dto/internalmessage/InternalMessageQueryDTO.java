
package com.ecommerce.information.api.dto.internalmessage;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class InternalMessageQueryDTO {

    @Schema(description = "消息标题")
    private String title;

    @Schema(description = "读取状态  0未读 1已读")
    private Integer status;

    @Schema(description = "会员id")
    private String memberId;

    @Schema(description = "接收人账号id(账号id和会员id不可都为空)")
    private String accountId;

    @Schema(description = "每页条数")
    private Integer pageSize = 20;
    @Schema(description = "页数")
    private Integer pageNumber = 1;
}
