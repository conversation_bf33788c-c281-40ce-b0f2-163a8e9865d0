package com.ecommerce.information.api.dto.internalmessage;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Data
public class InternalMessageReadStatusUpdateDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 8529040198116436117L;

    @Schema(description = "更新一条或多条为已读")
    private List<String> ids;

    @Schema(description = "更新所有为已读")
    private Boolean allRead;

    @Schema(description = "操作者")
    private String operator;

    @Schema(description = "会员id")
    private String memberId;

    @Schema(description = "账号id")
    private String accountId;
}
