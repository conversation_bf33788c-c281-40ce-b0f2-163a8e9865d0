package com.ecommerce.information.api.dto.memberIntegral;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Data
public class DeleteIntegralChangeInfoDTO{


    /**
     * id
     */
    @Schema(description = "id")
    private List<String> ids;

    /**
     * 操作人
     */
    @Schema(description = "操作人")
    private String operatorId;
}
