package com.ecommerce.information.api.dto.memberIntegral;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 24/01/2019 12:46
 * @DESCRIPTION:
 */
@Data
public class DeleteIntegralDTO {

    /**
     * id集合
     */
    @Schema(description = "id集合")
    private List<String> memberIdList;
    /**
     * 操作人
     */
    @Schema(description = "操作人")
    private String operatorId;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因")
    private String reason;

}
