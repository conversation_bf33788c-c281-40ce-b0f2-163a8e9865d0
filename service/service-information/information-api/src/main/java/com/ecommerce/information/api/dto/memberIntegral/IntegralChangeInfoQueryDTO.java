
package com.ecommerce.information.api.dto.memberIntegral;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

import jakarta.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Date: 22/02/2019 10:55
 * @DESCRIPTION:
 */
@Data
public class IntegralChangeInfoQueryDTO {

    /**
     * 变更消息
     */
    @Schema(description = "变更消息 like")
    private String remark;

    /**
     * 会员id
     */
    @Schema(description = "会员id eq")
    @NotBlank
    private String memberId;

    /**
     * 会员code
     */
    @Schema(description = "会员code eq ")
    private String memberCode;

    /**
     * 会员名称
     */
    @Schema(description = "会员名称 like")
    private String memberName;

    /**
     * 会员当时的会员类型
     */
    @Schema(description = "会员当时的会员类型 eq ")
    private String memberType;

    /**
     * 数据字典(积分类型)
     */
    @Schema(description = "数据字典(积分类型)")
    private String integralType;

    /**
     * 开始
     */
    @Schema(description = "开始")
    private Date beginTime;

    /**
     * 结束
     */
    @Schema(description = "结束")
    private Date endTime;


    @Schema(description = "页码")
    private Integer pageNum = 1;
    @Schema(description = "每页条数")
    private Integer pageSize = 10;

}
