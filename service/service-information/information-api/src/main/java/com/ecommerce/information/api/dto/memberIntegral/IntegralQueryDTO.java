
package com.ecommerce.information.api.dto.memberIntegral;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

import jakarta.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Date: 24/01/2019 12:47
 * @DESCRIPTION:
 */
@Data
public class IntegralQueryDTO {

    /**
     * 会员id
     */
    @Schema(description = "会员id eq")
    @NotBlank
    private String memberId;

    /**
     * 会员code
     */
    @Schema(description = "会员code eq ")
    private String memberCode;

    /**
     * 会员名称
     */
    @Schema(description = "会员名称 like")
    private String memberName;

    /**
     * 会员类型
     */
    @Schema(description = "会员类型")
    private String memberType;

    /**
     * 开始
     */
    @Schema(description = "开始")
    private Date beginTime;

    /**
     * 结束
     */
    @Schema(description = "结束")
    private Date endTime;

    /**
     * 操作人
     */
    @Schema(description = "操作人")
    private String operatorId;

    @Schema(description = "页码")
    private Integer pageNum = 1;
    @Schema(description = "每页条数")
    private Integer pageSize = 20;
}
