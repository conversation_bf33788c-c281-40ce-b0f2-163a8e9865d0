package com.ecommerce.information.api.dto.memberIntegral;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date: 24/01/2019 12:43
 * @DESCRIPTION:
 */
@Data
public class MemberIntegralChangeInfoDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 5748081339429985740L;

    /**
     * id
     */
    @Schema(description = "id")
    private String id;

    /**
     * 会员id
     */
    @Schema(description = "会员id")
    @NotBlank
    private String memberId;

    /**
     * 会员类型
     */
    @Schema(description = "会员类型")
    private String memberType;

    /**
     * 会员code
     */
    @Schema(description = "会员code")
    private String memberCode;

    /**
     * 会员名称
     */
    @Schema(description = "会员名称")
    private String memberName;

    /**
     * 订单号
     */
    @Schema(description = "订单号")
    private String orderNumber;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @NotBlank
    private String remark;
    /**
     * 积分
     */
    @Schema(description = "积分")
    @NotNull
    private Integer integral;

    /**
     * 带符号的积分
     */
    @Schema(description = "带符号的积分")
    private String integralStr;

    /**
     * 数据字典(积分类型)
     */
    @Schema(description = "数据字典(积分类型)")
    private String integralType;

    /**
     * 操作人
     */
    @Schema(description = "操作人")
    private String operatorId;

    /**
     * 创建日期
     */
    @Schema(description = "创建日期")
    private Date createTime;
}
