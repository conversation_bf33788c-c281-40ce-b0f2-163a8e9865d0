package com.ecommerce.information.api.dto.memberIntegral;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

import jakarta.validation.constraints.NotBlank;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date: 24/01/2019 12:43
 * @DESCRIPTION:
 */
@Data
public class MemberIntegralDTO {

    /**
     * 会员id
     */
    @Schema(description = "会员id")
    @NotBlank
    private String memberId;

    /**
     * 会员类型
     */
    @Schema(description = "会员类型")
    private String memberType;


    /**
     * 会员code
     */
    @Schema(description = "会员code")
    private String memberCode;

    /**
     * 会员名称
     */
    @Schema(description = "会员名称")
    private String memberName;

    /**
     * 会员等级
     */
    @Schema(description = "会员等级")
    private Integer memberLevel;

    /**
     * 积分
     */
    @Schema(description = "积分")
    private BigDecimal memberIntegral;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createUser;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 更新人
     */
    @Schema(description = "更新人")
    private String updateUser;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;
}
