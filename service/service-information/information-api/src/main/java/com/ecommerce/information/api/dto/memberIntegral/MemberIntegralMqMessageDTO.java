package com.ecommerce.information.api.dto.memberIntegral;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;

import java.util.List;

/**
 * 积分消息DTO
 *
 * <AUTHOR>
 */
@Data
public class MemberIntegralMqMessageDTO{

    @NotBlank
    @Schema(description = "归属会员id")
    private String buyerId;

    @NotBlank
    @Schema(description = "卖家会员id")
    private String sellerId;

    @NotBlank
    @Schema(description = "发生时间")
    private Date eventTime;


    @Schema(description = "备注说明")
    private String remark;

    @NotBlank
    @Schema(description = "订单号")
    private String orderNumber;

    @NotEmpty
    @Schema(description = "订单商品类型和数量")
    private List<MemberIntegralMqMessageGoodsInfoDTO> goodsList;
}
