package com.ecommerce.information.api.dto.memberIntegral;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date: 02/03/2019 17:41
 * @DESCRIPTION:
 */
@Data
public class MemberIntegralMqMessageGoodsInfoDTO {

    @NotBlank
    @Schema(description = "商品分类大类代码")
    private String categoryCode;

    @NotBlank
    @Schema(description = "商品名称")
    private String goodsName;

    @NotNull
    @Schema(description = "交易数量")
    private BigDecimal amount;
}
