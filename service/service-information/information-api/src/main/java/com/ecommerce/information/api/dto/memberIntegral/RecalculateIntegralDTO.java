package com.ecommerce.information.api.dto.memberIntegral;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Data
public class RecalculateIntegralDTO {

    /**
     * 会员id的List
     */
    @Schema(description = "会员id的List")
    List<String> memberIdList;

    /**
     * 会员code的List
     */
    @Schema(description = "会员code的List")
    List<String> memberCodeList;

    /**
     * 全部刷新的标记
     */
    @Schema(description = "全部刷新的标记")
    Boolean recalculateAll = false;

    /**
     * 操作者
     */
    @Schema(description = "操作者")
    String operatorId;

    /**
     * 修改原因
     */
    @Schema(description = "修改原因")
    String reason;
}
