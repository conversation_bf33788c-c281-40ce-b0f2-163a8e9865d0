package com.ecommerce.information.api.dto.memberIntegralConfig;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Data
public class MemberIntegralConfigDTO {

    /**
     * 等级表配置详情
     */
    @Schema(description = "等级表配置详情")
    private List<MemberIntegralLeaveConfigDTO> leaveConfig;

    /**
     * 成长表配置详情
     */
    @Schema(description = "成长表配置详情")
    private List<MemberIntegralTradeConfigDTO> tradeConfig;

    /**
     * 操作人
     */
    @Schema(description = "操作人")
    private String operatorId;
}
