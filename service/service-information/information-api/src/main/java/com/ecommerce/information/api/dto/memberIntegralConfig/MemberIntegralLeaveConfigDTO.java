package com.ecommerce.information.api.dto.memberIntegralConfig;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Data
public class MemberIntegralLeaveConfigDTO {

    /**
     * 会员等级配置表id
     */
    @Schema(description = "会员等级配置表id")
    private String integralLevelConfigId;

    /**
     * 类型 0 平台积分 1卖家积分
     */
    @Schema(description = "类型 0 平台积分 1卖家积分")
    private Integer integralType;

    /**
     * 客户类型 0 个人买家 1企业卖家
     */
    @Schema(description = "客户类型 0 个人买家 1企业卖家")
    private String customerType;

    /**
     * 普通会员
     */
    @Schema(description = "普通会员")
    private Integer levelNormal;

    /**
     * 铜牌会员(积分大于等于该值)
     */
    @Schema(description = "铜牌会员(积分大于等于该值)")
    private Integer levelBronze;

    /**
     * 银牌会员(积分大于等于该值)
     */
    @Schema(description = "银牌会员(积分大于等于该值)")
    private Integer levelSliver;

    /**
     * 金牌会员(积分大于等于该值)
     */
    @Schema(description = "金牌会员(积分大于等于该值)")
    private Integer levelGold;

    /**
     * 钻石会员(积分大于等于该值)
     */
    @Schema(description = "钻石会员(积分大于等于该值)")
    private Integer levelDiamond;

    /**
     * 删除标记 0未删除 1已删除
     */
    @Schema(description = "删除标记 0未删除 1已删除")
    private Boolean delFlg;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createUser;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @Schema(description = "更新人")
    private String updateUser;

}
