package com.ecommerce.information.api.dto.memberIntegralConfig;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 *
 * <AUTHOR>
 */
@Data
public class MemberIntegralTradeConfigDTO {

    /**
     * 个人配置
     */
    @Schema(description = "个人配置")
    private TradeConfigDTO personConfigDTO;
    /**
     * 企业配置
     */
    @Schema(description = "企业配置")
    private TradeConfigDTO enterpriseConfigDTO;
}
