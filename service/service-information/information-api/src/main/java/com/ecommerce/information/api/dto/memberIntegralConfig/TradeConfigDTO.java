package com.ecommerce.information.api.dto.memberIntegralConfig;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Data
public class TradeConfigDTO {

    /**
     * 会员积分配置表id
     */
    @Schema(description = "会员积分配置表id")
    private String integralTradeConfigId;

    /**
     * 客户类型 0 个人买家 1企业卖家
     */
    @Schema(description = "客户类型 0 个人买家 1企业卖家")
    private String customerType;

    /**
     * 商品类型编码 动态获取
     */
    @Schema(description = "商品类型编码 动态获取")
    private String goodsCategoryCode;

    /**
     * 商品类型名称
     */
    @Schema(description = "商品类型名称")
    private String goodsCategoryName;

    /**
     * 商品单位名称
     */
    @Schema(description = "商品单位名称")
    private String goodsUnitName;

    /**
     * 采购数量
     */
    @Schema(description = "采购数量")
    private Integer tradeNum;

    /**
     * 采购频次限制
     */
    @Schema(description = "采购频次限制")
    private Integer tradeFrequencyLimit;

    /**
     * 自然月采购频次积分上限
     */
    @Schema(description = "自然月采购频次积分上限")
    private Integer tradeMonthScoreMax;

    /**
     * 每次采购获取的积分
     */
    @Schema(description = "每次采购获取的积分")
    private Integer tradeScorce;

    /**
     * 禁用状态 0 可用  1 不可用
     */
    @Schema(description = "禁用状态 0 可用  1 不可用")
    private Boolean status;

    /**
     * 删除标记 0未删除 1已删除
     */
    @Schema(description = "删除标记 0未删除 1已删除")
    private Boolean delFlg;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createUser;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @Schema(description = "更新人")
    private String updateUser;
}
