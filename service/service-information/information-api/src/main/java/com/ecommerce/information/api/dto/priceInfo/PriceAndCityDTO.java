package com.ecommerce.information.api.dto.priceInfo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date: 08/04/2019 15:01
 * @DESCRIPTION:
 */
@Data
public class PriceAndCityDTO  implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "地区集合")
    private String citys;

    /**
     * 价格合并字符串
     */
    @Schema(description = "价格合并字符串")
    private String price;
}
