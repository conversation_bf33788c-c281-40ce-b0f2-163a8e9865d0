package com.ecommerce.information.api.dto.priceInfo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date: 03/01/2019 11:09
 * @DESCRIPTION:
 */
@Data
public class PriceChartDataQueryDTO {

    /**
     * 开始时间
     */
    @Schema(description = "开始时间")
    private Date beginTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间")
    private Date endTime;

    /**
     * 省
     */
    @Schema(description = "省")
    private String province;

    /**
     * 城市
     */
    @Schema(description = "城市")
    private String city;

    /**
     * 品种
     */
    @Schema(description = "品种")
    private String categoryId;

    /**
     * 品牌
     */
    @Schema(description = "品牌")
    private String brand;
}
