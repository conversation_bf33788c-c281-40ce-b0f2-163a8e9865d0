package com.ecommerce.information.api.dto.priceInfo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date: 02/01/2019 21:20
 * @DESCRIPTION:
 */
@Data
public class PriceInfoDTO implements Serializable {


    @Serial
    private static final long serialVersionUID = -6809188058600170344L;

    /**
     * id
     */
    @Schema(description = "id")
    private String id;

    /**
     * 商品分类id
     */
    @Schema(description = "商品分类id")
    private String categoryId;

    /**
     * 商品分类名称
     */
    @Schema(description = "商品分类名称")
    private String categoryName;

    /**
     * 价格时间
     */
    @Schema(description = "价格时间")
    private Date priceTime;

    /**
     * 价格时间字符串
     */
    @Schema(description = "价格时间字符串")
    private String priceTimeStr;

    /**
     * 省
     */
    @Schema(description = "省")
    private String province;

    /**
     * 城市
     */
    @Schema(description = "城市")
    private String city;

    /**
     * 品牌
     */
    @Schema(description = "品牌")
    private String brand;

    /**
     * 单位
     */
    @Schema(description = "单位")
    private String priceUnit;

    /**
     * /型号
     */
    @Schema(description = "/型号")
    private String currency;

    /**
     * 价格合并字符串
     */
    @Schema(description = "价格合并字符串")
    private String price;

    /**
     * 最低价
     */
    @Schema(description = "最低价")
    private BigDecimal lowestPrice;

    /**
     * 最高价
     */
    @Schema(description = "最高价")
    private BigDecimal highestPrice;

    /**
     * 删除标记
     */
    @Schema(description = "删除标记")
    private Boolean delFlg;

    /**
     * 创建人/审批人
     */
    @Schema(description = "创建人/审批人")
    private String createUser;

    /**
     * 创建时间/审批时间
     */
    @Schema(description = "创建时间/审批时间")
    private Date createTime;

    /**
     * 修改人
     */
    @Schema(description = "修改人")
    private String updateUser;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    private Date updateTime;
}
