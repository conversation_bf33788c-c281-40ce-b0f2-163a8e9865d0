
package com.ecommerce.information.api.dto.priceInfo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date: 02/01/2019 21:21
 * @DESCRIPTION:
 */
@Data
public class PriceInfoQueryDTO {

    /**
     * 开始时间
     */
    @Schema(description = "开始时间")
    private Date beginTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间")
    private Date endTime;

    /**
     * 省
     */
    @Schema(description = "省 like")
    private String province;

    /**
     * 城市
     */
    @Schema(description = "城市 like")
    private String city;

    /**
     * 品种
     */
    @Schema(description = "品种")
    private String categoryId;

    /**
     * 品牌
     */
    @Schema(description = "品牌")
    private String brand;

    @Schema(description = "/型号")
    private String currency;

    /**
     * 每页条数
     */
    @Schema(description = "每页条数")
    private Integer pageSize;

    /**
     * 页数
     */
    @Schema(description = "页数")
    private Integer pageNum;
}
