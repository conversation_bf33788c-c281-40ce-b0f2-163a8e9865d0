package com.ecommerce.information.api.dto.priceInfo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

import jakarta.validation.constraints.NotBlank;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date: 02/01/2019 21:20
 * @DESCRIPTION:
 */
@Data
public class PriceInfoUpdateDTO {

    /**
     * id
     */
    @NotBlank
    @Schema(description = "id")
    private String id;

    /**
     * 商品分类id
     */
    @Schema(description = "商品分类id")
    private String categoryId;

    /**
     * 商品分类名称
     */
    @Schema(description = "商品分类名称")
    private String categoryName;

    /**
     * 价格时间
     */
    @NotBlank
    @Schema(description = "价格时间")
    private Date priceTime;

    /**
     * 省
     */
    @Schema(description = "省")
    private String province;

    /**
     * 城市
     */
    @Schema(description = "城市")
    private String city;

    /**
     * 品牌
     */
    @Schema(description = "品牌")
    private String brand;

    /**
     * 单位
     */
    @Schema(description = "单位")
    private String priceUnit;

    /**
     * 币种
     */
    @Schema(description = "币种")
    private String currency;

    /**
     * 价格合并字符串
     */
    @Schema(description = "价格合并字符串")
    private String price;

    /**
     * 最低价
     */
    @Schema(description = "最低价")
    private BigDecimal lowestPrice;

    /**
     * 最高价
     */
    @Schema(description = "最高价")
    private BigDecimal highestPrice;

    /**
     * 修改人
     */
    @NotBlank
    @Schema(description = "修改人")
    private String updateUser;
}
