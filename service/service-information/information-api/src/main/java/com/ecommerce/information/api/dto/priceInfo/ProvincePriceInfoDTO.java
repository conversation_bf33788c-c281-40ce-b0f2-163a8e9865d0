package com.ecommerce.information.api.dto.priceInfo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 08/04/2019 14:58
 * @DESCRIPTION:
 */
@Data
public class ProvincePriceInfoDTO implements Serializable {


    @Serial
    private static final long serialVersionUID = 2365780853947882817L;

    @Schema(description = "更新时间")
    private Date updateTime;

    @Schema(description = "排序 0 正序 1 倒序")
    private Integer order = 0;
    /**
     * /型号
     */
    @Schema(description = "/型号")
    private String currency;
    /**
     * 单位
     */
    @Schema(description = "单位")
    private String priceUnit;

    @Schema(description = "城市价格")
    private List<PriceAndCityDTO> priceAndCityList;
}
