package com.ecommerce.information.api.dto.priceInfo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * task 1606任务结果
 *
 * <AUTHOR>
 */
@Data
public class ProvincePriceInfoIndexDTO implements Serializable {


    @Serial
    private static final long serialVersionUID = -3982855418729756671L;
    @Schema(description = "更新时间")
    private Date updateTime;

    @Schema(description = "排序 0 正序 1 倒序")
    private Integer order = 0;


    @Schema(description = "品牌 - 价格")
    private List<ProvincePriceInfoIndexItemDTO> priceList;

    private String city;
}
