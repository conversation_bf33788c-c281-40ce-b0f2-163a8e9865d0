package com.ecommerce.information.api.dto.priceInfo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * task 1606任务结果
 *
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class ProvincePriceInfoIndexItemDTO implements Serializable {


    @Serial
    private static final long serialVersionUID = -4197430616435108137L;

    @Schema(description = "/型号")
    private String currency;

    @Schema(description = "最低价")
    private BigDecimal lowestPrice;

    @Schema(description = "最高价")
    private BigDecimal highestPrice;
}
