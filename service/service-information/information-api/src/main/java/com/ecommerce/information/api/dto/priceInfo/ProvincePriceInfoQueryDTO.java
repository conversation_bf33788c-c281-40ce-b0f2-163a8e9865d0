
package com.ecommerce.information.api.dto.priceInfo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date: 08/04/2019 15:31
 * @DESCRIPTION:
 */
@Data
public class ProvincePriceInfoQueryDTO {

    @NotNull
    @Schema(description = "省")
    private String province;

    @Schema(description = "类型id 是查还是")
    private String categoryId;

    @Schema(description = "查询记录限制")
    private Integer limitSize = 100;

    @Schema(description = "排序 0 正序 1 倒序")
    private Integer order = 0;
}
