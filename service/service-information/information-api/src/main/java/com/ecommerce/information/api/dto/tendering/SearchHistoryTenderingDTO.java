package com.ecommerce.information.api.dto.tendering;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class SearchHistoryTenderingDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

	/**
	 * 创建人
	 */
	@Schema(description = "创建人")
	private String createUser;

	/**
	 * 页数
	 */
	@Schema(description = "页数")
    private Integer pageNumber = 1;

	/**
	 * 每页条数
	 */
	@Schema(description = "每页条数")
	private Integer pageSize = 10;

	/**
	 *
	 */
	@Schema(description = "")
	private Long pageStar = 0L;
}