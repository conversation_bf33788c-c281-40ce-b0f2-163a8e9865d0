
package com.ecommerce.information.api.dto.tendering;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

@Data
public class SearchTenderingDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 企业id
     */
    @Schema(description = "企业id")
    private String memberId;

    /**
     * 企业名称
     */
    @Schema(description = "企业名称")
    private String memberName;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private Integer status;

    /**
     * 标题
     */
    @Schema(description = "标题")
    private String title;

    /**
     * 招标所在城市
     */
    @Schema(description = "招标所在城市")
    private String city;

    /**
     * 招标信息来源于
     */
    @Schema(description = "招标信息来源于")
    private String source;

    /**
     * 招标信息来与类型 0 系统添加 1爬虫抓取
     */
    @Schema(description = "招标信息来与类型 0 系统添加 1爬虫抓取")
    private Integer sourceType;

    /**
     * 品名
     */
    @Schema(description = "品名")
    private String name;

    /**
     * 型号
     */
    @Schema(description = "型号")
    private String type;

    /**
     * 省
     */
    @Schema(description = "省")
    private String provinceCode;

    /**
     * 市
     */
    @Schema(description = "市")
    private String cityCode;

    /**
     * 区县
     */
    @Schema(description = "区县")
    private String districtCode;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间")
    private Date beginTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间")
    private Date endTime;

    /**
     * 如果是3则只查询当前正在进行中的数据
     */
    @Schema(description = "如果是3则只查询当前正在进行中的数据")
    private Integer processStatus;

    /**
     * 是否查询招标项目详细信息
     */
    @Schema(description = "是否查询招标项目详细信息")
    private Boolean onlySearchTenderingInfo;

    @Schema(description = "页数")
    private Integer pageNumber = 1;
    @Schema(description = "每页条数")
    private Integer pageSize = 10;
    @Schema(description = "起始页")
    private Long pageStar = 0L;

}
