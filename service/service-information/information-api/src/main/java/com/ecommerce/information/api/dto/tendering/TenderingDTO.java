
package com.ecommerce.information.api.dto.tendering;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

import jakarta.validation.constraints.NotBlank;
import java.util.List;

@Data
public class TenderingDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

	@NotBlank
	@Schema(description = "招标信息")
	private TenderingInfoDTO tenderingInfoDTO;

	/**
	 * 招标项目信息集合
	 */
	@NotBlank
	@Schema(description = "招标项目信息集合")
	private List<TenderingProductInfoDTO> tenderingProductInfoDTOList;
	
    @Schema(description = "页数")
    private Integer pageNumber = 0;
    @Schema(description = "每页条数")
	private Integer pageSize = 10;

}
