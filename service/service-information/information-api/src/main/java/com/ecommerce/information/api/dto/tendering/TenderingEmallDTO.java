package com.ecommerce.information.api.dto.tendering;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class TenderingEmallDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 3188234325015967584L;
    /**
     * 市
     */
    @Schema(description = "市")
    private String city;

    /**
     * 标题
     */
    @Schema(description = "标题")
    private String title;

    /**
     * 资讯来源
     */
    @Schema(description = "资讯来源")
    private String source;

    /**
     * 发布时间(begintime或者空)
     */
    @Schema(description = "发布时间(begintime或者空)")
    private String deployTime;

    /**
     * 页数
     */
    @Schema(description = "页数")
    private Integer pageNumber = 1;

    /**
     * 每页条数
     */
    @Schema(description = "每页条数")
    private Integer pageSize = 22;

}