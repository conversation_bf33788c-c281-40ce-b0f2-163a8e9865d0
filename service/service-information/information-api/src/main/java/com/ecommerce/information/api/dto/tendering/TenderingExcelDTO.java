package com.ecommerce.information.api.dto.tendering;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class TenderingExcelDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 企业名称
     */
    @Schema(description = "企业名称")
    private String memberName;

    /**
     * 标题
     */
    @Schema(description = "标题")
    private String title;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private Integer status;

    /**
     * 品名
     */
    @Schema(description = "品名")
    private String name;

    /**
     * 型号
     */
    @Schema(description = "型号")
    private String type;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer amount;

    /**
     * 商品单位
     */
    @Schema(description = "商品单位")
    private String unit;

    /**
     * 省
     */
    @Schema(description = "省")
    private String provinceName;

    /**
     * 市
     */
    @Schema(description = "市")
    private String cityName;

    /**
     * 区县
     */
    @Schema(description = "区县")
    private String districtName;

    public String[] getValues() {
        return new String[]{this.getMemberName(), this.getTitle(), this.getName(), this.getType(), (this.getAmount() != null ? this.getAmount().toString() : ""),
                this.getProvinceName(), this.getCityName(), this.getDistrictName(), this.getStatus().toString()};
    }

    public static String[] getSheetTile() {
        return new String[]{
                "企业名称", "标题", "品名", "型号", "数量", "省", "市", "区", "状态"
        };
    }
}