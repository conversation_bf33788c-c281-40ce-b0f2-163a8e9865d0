package com.ecommerce.information.api.dto.tendering;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

@Data
public class TenderingInfoDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Schema(description = "主键")
    private String tenderingId;

    /**
     * 企业id
     */
    @Schema(description = "企业id")
    private String memberId;
    /**
     * 企业名称
     */
    @Schema(description = "企业名称")
    private String memberName;

    /**
     * 标题
     */
    @Schema(description = "标题")
    private String title;
    /**
     * 招标地点
     */
    @Schema(description = "招标地点")
    private String city;
    /**
     * 招标信息来源于
     */
    @Schema(description = "招标信息来源于")
    private String source;
    /**
     * 招标信息来与类型 0 系统添加 1爬虫抓取
     */
    @Schema(description = "招标信息来与类型 0 系统添加 1爬虫抓取")
    private Integer sourceType;

    /**
     * 其他需求内容
     */
    @Schema(description = "其他需求内容")
    private String otherRequireContent;

    /**
     * 其他需求附件url
     */
    @Schema(description = "其他需求附件url")
    private String otherRequireUrl;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private Integer status;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间")
    private Date beginTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间")
    private Date endTime;

    /**
     * 进行状态０未开始　3 进行中，4 已关闭
     */
    @Schema(description = "进行状态０未开始　3 进行中，4 已关闭")
    private Integer processStatus = 0;

    /**
     * 广告id
     */
    @Schema(description = "广告id")
    private String advertisingId;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createUser;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 修改人
     */
    @Schema(description = "修改人")
    private String updateUser;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    private Date updateTime;

    /**
     * 删除标记
     */
    @Schema(description = "删除标记")
    private Boolean delFlg;


    /**
     * 关键字
     */
    @Schema(description = "关键字")
    private String keywords;

}