package com.ecommerce.information.api.dto.tendering;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

@Data
public class TenderingProductInfoDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Schema(description = "主键")
	private String tenderingProductId;

    /**
     * 招标id
     */
    @Schema(description = "招标id")
    private String tenderingId;

    /**
     *
     */
    @Schema(description = "")
    private String memberId;

    /**
     * 品名
     */
    @Schema(description = "品名")
    private String name;

    /**
     * 型号
     */
    @Schema(description = "型号")
    private String type;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer amount;

    /**
     * 商品单位
     */
    @Schema(description = "商品单位")
    private String unit;

    /**
     * 省
     */
    @Schema(description = "省")
    private String provinceName;

    /**
     * 省代码
     */
    @Schema(description = "省代码")
    private String provinceCode;

    /**
     * 市
     */
    @Schema(description = "市")
    private String cityName;

    /**
     * 市代码
     */
    @Schema(description = "市代码")
    private String cityCode;

    /**
     * 区县
     */
    @Schema(description = "区县")
    private String districtName;

    /**
     *  区县代码
     */
    @Schema(description = "区县代码")
    private String districtCode;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createUser;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 修改人
     */
    @Schema(description = "修改人")
    private String updateUser;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    private Date updateTime;

    /**
     * 删除标记
     */
    @Schema(description = "删除标记")
    private Boolean delFlg;


}