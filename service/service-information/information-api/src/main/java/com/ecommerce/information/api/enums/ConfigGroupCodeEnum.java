package com.ecommerce.information.api.enums;

/**
 * <AUTHOR>
 * @Description:
 * @Date: Create in 下午9:32 18/9/10
 */
public enum ConfigGroupCodeEnum {

    BASE("base", "通用"),

    PLATFORM("platform", "平台"),

    SELLER("seller", "卖家"),

    SELLER_APP("sellerApp", "卖家APP"),

    BUYER("buyer", "买家"),

    BUYER_APP("buyerApp", "买家APP"),

    CARRIER("carrier", "承运商"),

    DRIVER_APP("driverApp", "司机App")
    ;

    /**
     * 描述
     */
    private final String code;

    /**
     * 编码
     */
    private final String desc;

    ConfigGroupCodeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ConfigGroupCodeEnum valueOfCode(String code) {
        ConfigGroupCodeEnum[] enums = values();
        for (ConfigGroupCodeEnum item : enums) {
            if (item.code.equals(code)) {
                return item;
            }
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}
