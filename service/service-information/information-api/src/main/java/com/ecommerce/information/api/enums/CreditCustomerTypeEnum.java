package com.ecommerce.information.api.enums;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "信用客户类型")
public enum CreditCustomerTypeEnum {

    AGENT("AGENT", "经销商"),

    MIXING_STATION("MIXING_STATION", "搅拌站"),

    PIPE_PILE_PLANT("PIPE_PILE_PLANT", "管桩厂");

    private final String code;

    private final String desc;

    CreditCustomerTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static CreditCustomerTypeEnum valueOfCode(String code) {
        CreditCustomerTypeEnum[] enums = values();
        for (CreditCustomerTypeEnum item : enums) {
            if (item.code.equals(code)) {
                return item;
            }
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }

}
