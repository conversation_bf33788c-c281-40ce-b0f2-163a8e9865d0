package com.ecommerce.information.api.enums;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "信用状态枚举")
public enum CreditStatusEnum {

    EFFECTIVE("EFFECTIVE", "有效"),

    INEFFECTIVE("INEFFECTIVE", "失效");

    private final String code;

    private final String desc;

    CreditStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static CreditStatusEnum valueOfCode(String code) {
        CreditStatusEnum[] enums = values();
        for (CreditStatusEnum item : enums) {
            if (item.code.equals(code)) {
                return item;
            }
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}
