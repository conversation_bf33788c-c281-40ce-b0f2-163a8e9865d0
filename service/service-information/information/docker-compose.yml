
services:
  service-information:
    image: ecommerce-service-information:1.0
    volumes:
      - /remote/logs/information:/var/log
      - /remote/skywalking:/home/<USER>
    deploy:
      resources:
        limits:
          memory: 800m
    container_name: information
    restart: always
    ports:
      - "9005:9005"
    environment:
      - SPRING_CLOUD_CONFIG_URI=http://************:8888
      - SPRING_CLOUD_CONFIG_LABEL=asset-2025
      - SPRING_CLOUD_CONFIG_PROFILE=dev
      - SPRING_CLOUD_CONFIG_NAME=information,db,redis,rabbitmq,hystrix,vip-server,kafka,xxl-job,eureka,elasticsearch
      - TZ=Asia/Shanghai
      - JAVA_TOOL_OPTIONS=-Xms256m -Xmx512m -XX:+UseSerialGC
    depends_on:
      - config
      - eureka