<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>service-information</artifactId>
        <groupId>com.ecommerce</groupId>
        <version>2.1.4-RELEASE</version>
    </parent>
    <artifactId>information</artifactId>
    <packaging>jar</packaging>

    <name>information</name>
    <description>Information Service</description>

    <properties>
        <docker.deploy.version>${project.version}</docker.deploy.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-config</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-eureka-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bus-amqp</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ecommerce</groupId>
            <artifactId>service-common</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>

        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-tracing-bridge-brave</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-freemarker</artifactId>
        </dependency>



        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>

        <!--session同步-->
        <dependency>
            <groupId>org.springframework.session</groupId>
            <artifactId>spring-session-data-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
        </dependency>


        <dependency>
            <artifactId>information-api</artifactId>
            <groupId>com.ecommerce</groupId>
        </dependency>
        <dependency>
            <groupId>com.ecommerce</groupId>
            <artifactId>report-api</artifactId>
        </dependency>
        <!--		<dependency>-->
        <!--			<groupId>org.freemarker</groupId>-->
        <!--			<artifactId>freemarker</artifactId>-->
        <!--			<version>2.3.23</version>-->
        <!--		</dependency>-->
        <dependency>
            <artifactId>base-api</artifactId>
            <groupId>com.ecommerce</groupId>
        </dependency>
        <dependency>
            <artifactId>member-api</artifactId>
            <groupId>com.ecommerce</groupId>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <dependency>
            <groupId>tk.mybatis</groupId>
            <artifactId>mapper</artifactId>
        </dependency>
        <dependency>
            <groupId>jakarta.annotation</groupId>
            <artifactId>jakarta.annotation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-scratchpad</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ecommerce</groupId>
            <artifactId>goods-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ecommerce</groupId>
            <artifactId>order-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ecommerce</groupId>
            <artifactId>logistics-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
        </dependency>


        <!--        <dependency>-->
        <!--            <groupId>com.ecommerce/groupId>-->
        <!--            <artifactId>kafka-spring-boot-starter</artifactId>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>com.ecommerce</groupId>
            <artifactId>rabbitmq-spring-boot-starter</artifactId>
        </dependency>
        <!--Hutool工具包 -->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.8.24</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>information</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.14.0</version>
                <configuration>
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                    <release>${java.version}</release>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
