package com.ecommerce.information;

import com.ecommerce.common.annotation.ExcludeFromComponetScan;
import com.ecommerce.rabbitmq.annotation.EnableRabbitMq;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import tk.mybatis.spring.annotation.MapperScan;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

@SpringBootApplication
@EnableFeignClients({
		"com.ecommerce.base.api.service",
		"com.ecommerce.base.api",
		"com.ecommerce.logistics.api",
		"com.ecommerce.order.api",
		"com.ecommerce.goods.api",
		"com.ecommerce.report.api",
		"com.ecommerce.member.api"})
@EnableDiscoveryClient
//@EnableKafka
@EnableRabbitMq
@ComponentScan(value = {
		"com.ecommerce.information",
		"com.ecommerce.common.config",
		"com.ecommerce.common.service",
		"com.ecommerce.common.filter"
}, excludeFilters = {
		@ComponentScan.Filter(type = FilterType.ANNOTATION, value = ExcludeFromComponetScan.class) })
@MapperScan(value = {
        "com.ecommerce.information.dao",
        "com.ecommerce.information.bill.check.dao"
})
@EnableCaching
public class InformationApplication {

	public static void main(String[] args) {
		SpringApplication.run(InformationApplication.class, args);
	}

	@Bean("myExecutor")
	public Executor myTaskAsyncPool() {
		ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
		//核心线程池大小
		executor.setCorePoolSize(20);
		//最大线程数
		executor.setMaxPoolSize(40);
		//队列容量
		executor.setQueueCapacity(300);
		//活跃时间
		executor.setKeepAliveSeconds(50);
		//线程名字前缀
		executor.setThreadNamePrefix("informationExecutor-");

		// setRejectedExecutionHandler：当pool已经达到max size的时候，如何处理新任务
		// CallerRunsPolicy：不在新线程中执行任务，而是由调用者所在的线程来执行
		executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
		executor.initialize();
		return executor;
	}
}
