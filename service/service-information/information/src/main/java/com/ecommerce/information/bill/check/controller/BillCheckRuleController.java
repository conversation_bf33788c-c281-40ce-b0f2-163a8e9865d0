package com.ecommerce.information.bill.check.controller;

import com.ecommerce.information.api.dto.bill.check.rule.BillCheckRuleQueryDTO;
import com.ecommerce.information.api.dto.bill.check.rule.BillCheckRuleCreateDTO;
import com.ecommerce.information.api.dto.bill.check.rule.BillCheckRuleDTO;
import com.ecommerce.information.api.dto.bill.check.rule.BillCheckRuleDeleteDTO;
import com.ecommerce.information.api.dto.bill.check.rule.BillCheckRuleUpdateDTO;
import com.ecommerce.information.api.dto.bill.check.rule.MemberIdAndNameDTO;
import com.ecommerce.information.api.dto.bill.check.rule.MemberIdAndNameQueryDTO;
import com.ecommerce.information.api.dto.bill.check.rule.PageQuery;
import com.ecommerce.information.bill.check.service.IBillCheckRuleService;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@RestController
@Tag(name = "BillCheckRule", description = "对账单规则服务")
@RequestMapping("/billCheckRule")
public class BillCheckRuleController {

   @Autowired 
   private IBillCheckRuleService iBillCheckRuleService;

   @Operation(summary = "新增配置")
   @PostMapping(value="/add")
   public Boolean add(@RequestBody BillCheckRuleCreateDTO dto){
      return iBillCheckRuleService.add(dto);
   }

   @Operation(summary = "修改配置")
   @PostMapping(value="/update")
   public Boolean update(@RequestBody BillCheckRuleUpdateDTO dto){
      return iBillCheckRuleService.update(dto);
   }

   @Operation(summary = "逻辑删除配置")
   @PostMapping(value="/delete")
   public Boolean delete(@RequestBody BillCheckRuleDeleteDTO dto){
      return iBillCheckRuleService.delete(dto);
   }


   @Operation(summary = "翻页查询")
   @PostMapping(value="/findAll")
   public PageInfo<BillCheckRuleDTO> findAll(@RequestBody PageQuery<BillCheckRuleQueryDTO> query){
      return iBillCheckRuleService.findAll(query);
   }


   @Operation(summary = "根据id查询")
   @PostMapping(value = "/findById")
   public BillCheckRuleDTO findById(@RequestParam String billCheckRuleId) {
      return iBillCheckRuleService.findById(billCheckRuleId);
   }

   @Operation(summary = "初始化对账规则")
   @PostMapping(value = "/init")
   public void init(@RequestBody BillCheckRuleCreateDTO dto) {
      iBillCheckRuleService.init(dto);
   }

   @Operation(summary = "根据条件查询收款方或者付款方会员id和名称，一次查询最多返回20个")
   @PostMapping(value = "/findMemberName")
   public List<MemberIdAndNameDTO> findMemberName(@RequestBody MemberIdAndNameQueryDTO queryDTO) {
      return iBillCheckRuleService.findMemberName(queryDTO);
   }

   @Operation(summary = "查询规则对应的账期")
   @PostMapping(value = "/findCheckDate")
   public List<String> findCheckDate(@RequestBody BillCheckRuleQueryDTO queryDTO) {
      return iBillCheckRuleService.findCheckDate(queryDTO);
   }
}
