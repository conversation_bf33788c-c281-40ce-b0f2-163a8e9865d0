package com.ecommerce.information.bill.check.service;

import com.ecommerce.information.api.dto.bill.check.BillCheckApprovalDTO;
import com.ecommerce.information.api.dto.bill.check.BillCheckCreateDTO;
import com.ecommerce.information.api.dto.bill.check.BillCheckDeleteDTO;
import com.ecommerce.information.api.dto.bill.check.BillCheckExportDTO;
import com.ecommerce.information.api.dto.bill.check.BillCheckGoodsInfoDTO;
import com.ecommerce.information.api.dto.bill.check.BillCheckGoodsInfoQueryDTO;
import com.ecommerce.information.api.dto.bill.check.BillCheckInfoDTO;
import com.ecommerce.information.api.dto.bill.check.BillCheckInfoTabDTO;
import com.ecommerce.information.api.dto.bill.check.BillCheckQueryDTO;
import com.ecommerce.information.api.dto.bill.check.BillCheckReDoDTO;
import com.ecommerce.information.api.dto.bill.check.BillCheckUpdateDTO;
import com.ecommerce.information.api.dto.bill.check.BillCheckWaybillInfoDTO;
import com.ecommerce.information.api.dto.bill.check.BillCheckWaybillInfoQueryDTO;
import com.ecommerce.information.api.dto.bill.check.KeyValueDTO;
import com.ecommerce.information.api.dto.bill.check.KeyValueQueryDTO;
import com.ecommerce.information.api.dto.bill.check.rule.PageQuery;
import com.github.pagehelper.PageInfo;
import org.springframework.web.bind.annotation.RequestBody;

import jakarta.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 对账单服务
 */
public interface IBillCheckService {

    /**
     * 手工触发生成对账单
     */
    Boolean generate(BillCheckCreateDTO dto);
    /**
     * 重新生成对账单
     */
    Boolean regenerate(BillCheckReDoDTO dto);
    /**
     * 审批(确认)
     */
    Boolean approve(BillCheckApprovalDTO dto);
    /**
     * 修改对账单（仅能修改这些信息: 添加/删除运单明细项、添加/删除汇总数据的手工行数据、添加/修改备注）
     */
    Boolean updateBillCheck(BillCheckUpdateDTO billCheckUpdateDTO);

    /**
     * 删除对账单
     */
    Boolean deleteBillCheck(BillCheckDeleteDTO dto);

    /**
     * 对账单翻页查询
     */
    PageInfo<BillCheckInfoDTO> findAll(PageQuery<BillCheckQueryDTO> query);

    /**
     * 根据条件查询所有tab的数量
     */
    List<BillCheckInfoTabDTO> tabCountByQuery(BillCheckQueryDTO query);
    /**
     * 商品汇总信息翻页查询（excel导出调用此接口在web层导出）
     */
    PageInfo<BillCheckGoodsInfoDTO> pageGoodsInfo(@RequestBody PageQuery<BillCheckGoodsInfoQueryDTO> query);
    /**
     * 运单翻页查询（excel导出调用此接口在web层导出）
     */
    PageInfo<BillCheckWaybillInfoDTO> pageWaybillInfo(PageQuery<BillCheckWaybillInfoQueryDTO> query);
    /**
     * 根据id查询(返回运单以外的对账单所有信息)
     */
    BillCheckInfoDTO findById(String billCheckId);

    /**
     * 根据id查询(含运单)
     */
    BillCheckInfoDTO findDetailById(String billCheckId);
    /**
     * 搜索下拉数据查询，根据关键字查询 id 和name
     */
    List<KeyValueDTO> findDropDownData(KeyValueQueryDTO query);

    void logisticsBillCheckExport(BillCheckExportDTO dto, HttpServletResponse response);
}
