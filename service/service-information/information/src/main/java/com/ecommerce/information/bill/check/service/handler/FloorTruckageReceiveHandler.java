package com.ecommerce.information.bill.check.service.handler;

import com.alibaba.fastjson.JSON;
import com.ecommerce.common.service.common.uuid.UUIDGenerator;
import com.ecommerce.information.bill.check.biz.IBillCheckWaybillBaseDataBiz;
import com.ecommerce.mq.core.MQMessage;
import com.ecommerce.mq.core.handler.AbstractMessageHandler;
import com.ecommerce.order.api.dto.OrderDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 订单完成，同步台班费给对账使用
 */
@Slf4j
@Component
public class FloorTruckageReceiveHandler extends AbstractMessageHandler<OrderDTO> {

    @Autowired
    private IBillCheckWaybillBaseDataBiz billCheckWaybillBaseDataBiz;
    @Autowired
    private UUIDGenerator uuidGenerator;

    @Override
    public boolean handle(OrderDTO orderDTO) {
        String old = Thread.currentThread().getName();
        try{
            Thread.currentThread().setName(uuidGenerator.gain());
            log.info("orderDTO : {} ", JSON.toJSONString(orderDTO));
            if(orderDTO != null && CollectionUtils.isNotEmpty(orderDTO.getOrderItems())) {
                billCheckWaybillBaseDataBiz.perfectInfo(orderDTO.getOrderCode(),orderDTO.getOrderItems().get(0).getActualMachinePrice());
            }
        }catch (Exception e){
            log.error(e.getMessage(),e);
        }finally {
            Thread.currentThread().setName(old);
        }
        return true;
    }

    @Override
    public void handleRetryMax(MQMessage mqMessage) {
        log.info("{}", mqMessage);
    }

    @Override
    public String handleType() {
        return "com.ecommerce.order.floorTruckage.notify.queue";
    }

}
