package com.ecommerce.information.bill.check.service.handler;

import com.alibaba.fastjson.JSON;
import com.ecommerce.common.service.common.uuid.UUIDGenerator;
import com.ecommerce.information.bill.check.biz.IBillCheckWaybillBaseDataBiz;
import com.ecommerce.information.bill.check.biz.impl.BillCheckRuleBiz;
import com.ecommerce.information.bill.check.dao.vo.BillCheckWaybillBaseData;
import com.ecommerce.logistics.api.dto.shipbill.CheckWaybillInfoDTO;
import com.ecommerce.member.api.dto.member.enums.MemberPlatform;
import com.ecommerce.mq.core.MQMessage;
import com.ecommerce.mq.core.handler.AbstractMessageHandler;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Condition;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 运单完成，同步给对账使用
 */
@Slf4j
@Component
public class WaybillReceiveHandler extends AbstractMessageHandler<List<CheckWaybillInfoDTO>> {

    @Autowired
    private IBillCheckWaybillBaseDataBiz billCheckWaybillBaseDataBiz;
    @Autowired
    private UUIDGenerator uuidGenerator;
    @Lazy
    @Autowired
    private WaybillRefundReceiveHandler waybillRefundReceiveHandler;

    @Value("${rabbitmq.profile}")
    private String profile;



    @Override
    public boolean handle(List<CheckWaybillInfoDTO> data) {
        String old = Thread.currentThread().getName();
        try{
            Thread.currentThread().setName(uuidGenerator.gain());
            log.info("CheckWaybillInfoDTO list: {} ", JSON.toJSONString(data));
            Set<String> waybillNumSet = Sets.newHashSet();
            if(CollectionUtils.isNotEmpty(data)) {
                Date now = new Date();
                List<BillCheckWaybillBaseData> list = data.stream().map(item -> {
                    BillCheckWaybillBaseData vo = new BillCheckWaybillBaseData();
                    BeanUtils.copyProperties(item, vo);
                    waybillNumSet.add(item.getWaybillNum());
                    vo.setEmptyLoadFee(item.getEmptyLoadCharge());
                    vo.setPayerMemberId(item.getConsignorId());
                    vo.setPayerMemberName(item.getConsignorName());
                    vo.setPayeeMemberId(item.getCarrierId());
                    vo.setPayeeMemberName(item.getCarrierName());
                    vo.setWaybillDeliverWay(item.getDeliverWay());
                    vo.setDeliverWay(null);
                    if(BillCheckRuleBiz.PLATFORM_ID.equals(vo.getCarrierId())){
                        vo.setCarrierId(MemberPlatform.PLATFORM_MEMBERID.getId());
                        vo.setCarrierName(MemberPlatform.PLATFORM_MEMBERID.getInfo());
                    }
                    if(BillCheckRuleBiz.PLATFORM_ID.equals(vo.getConsignorId())){
                        vo.setConsignorId(MemberPlatform.PLATFORM_MEMBERID.getId());
                        vo.setConsignorName(MemberPlatform.PLATFORM_MEMBERID.getInfo());
                    }
                    if(BillCheckRuleBiz.PLATFORM_ID.equals(vo.getBuyerId())){
                        vo.setBuyerId(MemberPlatform.PLATFORM_MEMBERID.getId());
                        vo.setBuyerName(MemberPlatform.PLATFORM_MEMBERID.getInfo());
                    }
                    if(BillCheckRuleBiz.PLATFORM_ID.equals(vo.getSellerId())){
                        vo.setSellerId(MemberPlatform.PLATFORM_MEMBERID.getId());
                        vo.setSellerName(MemberPlatform.PLATFORM_MEMBERID.getInfo());
                    }
                    if(BillCheckRuleBiz.PLATFORM_ID.equals(vo.getPayeeMemberId())){
                        vo.setPayeeMemberId(MemberPlatform.PLATFORM_MEMBERID.getId());
                        vo.setPayeeMemberName(MemberPlatform.PLATFORM_MEMBERID.getInfo());
                    }
                    if(BillCheckRuleBiz.PLATFORM_ID.equals(vo.getPayerMemberId())){
                        vo.setPayerMemberName(MemberPlatform.PLATFORM_MEMBERID.getId());
                        vo.setPayerMemberName(MemberPlatform.PLATFORM_MEMBERID.getInfo());
                    }

                    vo.setWaybillDataId(uuidGenerator.gain());
                    vo.setCreateTime(now);
                    vo.setCreateUser(MemberPlatform.SYSTEM_OPERATOR.getId());
                    vo.setDelFlg(false);
                    vo.setPerfectFlg(false);
                    //如果是重复数据，则删除已存在的数据
                    Condition condition = new Condition(BillCheckWaybillBaseData.class);
                    condition.createCriteria()
                            .andEqualTo("takeCode",vo.getTakeCode())
                            .andEqualTo("deliveryBillNum",vo.getDeliveryBillNum())
                            .andEqualTo("waybillNum",vo.getWaybillNum())
                            .andEqualTo("carrierId",vo.getCarrierId())
                            .andEqualTo("consignorId",vo.getConsignorId())
                            .andEqualTo("delFlg",false);
                    BillCheckWaybillBaseData up = new BillCheckWaybillBaseData();
                    up.setDelFlg(true);
                    up.setUpdateTime(now);
                    up.setUpdateUser(MemberPlatform.SYSTEM_OPERATOR.getId());
                    billCheckWaybillBaseDataBiz.getMapper().updateByConditionSelective(up,condition);
                    return vo;
                }).filter(Objects::nonNull).toList();
                billCheckWaybillBaseDataBiz.getMapper().insertList(list);
                //完善数据
                billCheckWaybillBaseDataBiz.perfectInfo(list);
                //查询如果有未生成退货数据，则生成退货数据
                waybillRefundReceiveHandler.createRefundDate(Lists.newArrayList(waybillNumSet));
            }
        }catch (Exception e){
            log.error(e.getMessage(),e);
        }finally {
            Thread.currentThread().setName(old);
        }
        return true;
    }

    @Override
    public void handleRetryMax(MQMessage mqMessage) {
        log.info("{}", mqMessage);
    }

    @Override
    public String handleType() {
        return "send.checkWaybill."+profile;
    }
}
