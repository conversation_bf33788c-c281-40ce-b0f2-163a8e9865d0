package com.ecommerce.information.bill.check.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.ecommerce.base.api.dto.saleregion.SaleRegionSampleDTO;
import com.ecommerce.base.api.enums.BaseRoleTypeEnum;
import com.ecommerce.base.api.service.ISaleRegionService;
import com.ecommerce.common.enums.AppNames;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.exception.DistributeLockException;
import com.ecommerce.common.service.common.uuid.UUIDGenerator;
import com.ecommerce.common.utils.ArithUtils;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.information.api.dto.bill.check.BillCheckAdjustInfoDTO;
import com.ecommerce.information.api.dto.bill.check.BillCheckApprovalDTO;
import com.ecommerce.information.api.dto.bill.check.BillCheckChangeLogDTO;
import com.ecommerce.information.api.dto.bill.check.BillCheckCreateDTO;
import com.ecommerce.information.api.dto.bill.check.BillCheckDeleteDTO;
import com.ecommerce.information.api.dto.bill.check.BillCheckExportDTO;
import com.ecommerce.information.api.dto.bill.check.BillCheckGoodsInfoDTO;
import com.ecommerce.information.api.dto.bill.check.BillCheckGoodsInfoQueryDTO;
import com.ecommerce.information.api.dto.bill.check.BillCheckGoodsInfoUpdateDTO;
import com.ecommerce.information.api.dto.bill.check.BillCheckInfoDTO;
import com.ecommerce.information.api.dto.bill.check.BillCheckInfoTabDTO;
import com.ecommerce.information.api.dto.bill.check.BillCheckQueryDTO;
import com.ecommerce.information.api.dto.bill.check.BillCheckReDoDTO;
import com.ecommerce.information.api.dto.bill.check.BillCheckUpdateDTO;
import com.ecommerce.information.api.dto.bill.check.BillCheckWaybillInfoDTO;
import com.ecommerce.information.api.dto.bill.check.BillCheckWaybillInfoQueryDTO;
import com.ecommerce.information.api.dto.bill.check.KeyValueDTO;
import com.ecommerce.information.api.dto.bill.check.KeyValueQueryDTO;
import com.ecommerce.information.api.dto.bill.check.enums.BillCheckCycleEnum;
import com.ecommerce.information.api.dto.bill.check.enums.BillCheckStatusEnum;
import com.ecommerce.information.api.dto.bill.check.enums.BillCheckTypeEnum;
import com.ecommerce.information.api.dto.bill.check.enums.CostTypeEnum;
import com.ecommerce.information.api.dto.bill.check.rule.MemberIdAndNameDTO;
import com.ecommerce.information.api.dto.bill.check.rule.PageQuery;
import com.ecommerce.information.bill.check.biz.IBillCheckChangeLogBiz;
import com.ecommerce.information.bill.check.biz.IBillCheckGoodsInfoBiz;
import com.ecommerce.information.bill.check.biz.IBillCheckInfoBiz;
import com.ecommerce.information.bill.check.biz.IBillCheckRuleBiz;
import com.ecommerce.information.bill.check.biz.IBillCheckWaybillBaseDataBiz;
import com.ecommerce.information.bill.check.biz.IBillCheckWaybillInfoBiz;
import com.ecommerce.information.bill.check.biz.impl.BillCheckInfoBiz;
import com.ecommerce.information.bill.check.biz.impl.BillCheckRuleBiz;
import com.ecommerce.information.bill.check.dao.vo.BillCheckChangeLog;
import com.ecommerce.information.bill.check.dao.vo.BillCheckGoodsInfo;
import com.ecommerce.information.bill.check.dao.vo.BillCheckInfo;
import com.ecommerce.information.bill.check.dao.vo.BillCheckRule;
import com.ecommerce.information.bill.check.service.IBillCheckService;
import com.ecommerce.information.bill.check.service.job.BillCheckPayableDriverJob;
import com.ecommerce.information.bill.check.service.job.BillCheckPayableLogisticsRoadJob;
import com.ecommerce.information.bill.check.service.job.BillCheckPayableLogisticsWaterJob;
import com.ecommerce.information.bill.check.service.job.BillCheckReceivableConcreteJob;
import com.ecommerce.information.bill.check.service.job.BillCheckReceivableGoodsJob;
import com.ecommerce.information.bill.check.service.job.BillCheckReceivableLogisticsRoadJob;
import com.ecommerce.information.bill.check.service.job.BillCheckReceivableLogisticsWaterJob;
import com.ecommerce.information.util.BillCheckExcelUtil2;
import com.ecommerce.member.api.dto.member.MemberSimpleDTO;
import com.ecommerce.member.api.service.IAccountService;
import com.ecommerce.member.api.service.IMemberService;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BillCheckService implements IBillCheckService {

    @Autowired
    private UUIDGenerator uuidGenerator;
    @Autowired
    private IBillCheckChangeLogBiz billCheckChangeLogBiz;
    @Autowired
    private IBillCheckGoodsInfoBiz billCheckGoodsInfoBiz;
    @Autowired
    private IBillCheckInfoBiz billCheckInfoBiz;
    @Autowired
    private IBillCheckRuleBiz billCheckRuleBiz;
    @Autowired
    private IBillCheckWaybillInfoBiz billCheckWaybillInfoBiz;
    @Autowired
    private IBillCheckWaybillBaseDataBiz billCheckWaybillBaseDataBiz;
    @Autowired
    private IAccountService accountService;
    @Autowired
    private IMemberService memberService;
    @Autowired
    private ISaleRegionService saleRegionService;
    @Lazy
    @Autowired
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    //job 应收
    @Autowired
    private BillCheckReceivableGoodsJob billCheckReceivableGoodsJob;
    @Autowired
    private BillCheckReceivableConcreteJob billCheckReceivableConcreteJob;
    @Autowired
    private BillCheckReceivableLogisticsRoadJob billCheckReceivableLogisticsRoadJob;
    @Autowired
    private BillCheckReceivableLogisticsWaterJob billCheckReceivableLogisticsWaterJob;
    //job 应付
    @Autowired
    private BillCheckPayableDriverJob billCheckPayableDriverJob;
    @Autowired
    private BillCheckPayableLogisticsRoadJob billCheckPayableLogisticsRoadJob;
    @Autowired
    private BillCheckPayableLogisticsWaterJob billCheckPayableLogisticsWaterJob;

    //对账相关角色集合
    private static final Set<String> ROLE_FINANCIAL = Sets.newHashSet(BaseRoleTypeEnum.BILL_CHECK_FINANCIAL_PLATFORM.getInfo(),
            BaseRoleTypeEnum.BILL_CHECK_FINANCIAL_SELLER.getInfo(),
            BaseRoleTypeEnum.BILL_CHECK_FINANCIAL_CARRIER.getInfo());

    private static final Set<String> ROLE_BUSINESS = Sets.newHashSet(BaseRoleTypeEnum.BILL_CHECK_BUSINESS_CONFIRM_PLATFORM.getInfo(),
            BaseRoleTypeEnum.BILL_CHECK_BUSINESS_CONFIRM_SELLER.getInfo(),
            BaseRoleTypeEnum.BILL_CHECK_BUSINESS_CONFIRM_CARRIER.getInfo());

    private static final Set<String> ROLE_CUSTOMER = Sets.newHashSet(BaseRoleTypeEnum.BILL_CHECK_CUSTOMER_CONFIRM_PLATFORM.getInfo(),
            BaseRoleTypeEnum.BILL_CHECK_CUSTOMER_CONFIRM_SELLER.getInfo(),
            BaseRoleTypeEnum.BILL_CHECK_CUSTOMER_CONFIRM_CARRIER.getInfo(),
            BaseRoleTypeEnum.BILL_CHECK_CUSTOMER_CONFIRM_BUYER.getInfo());

    @Override
    // TODO: 重构此方法以降低认知复杂度 (当前: 80, 目标: ≤15)
    // 建议: 1) 提取子方法 2) 使用早期返回 3) 简化条件表达式
    public Boolean generate(BillCheckCreateDTO dto) {
        log.info("手工触发对账单生成: {}",JSON.toJSONString(dto));
        CsStringUtils.checkIsNull(dto.getOperator(),"operator");
        CsStringUtils.checkIsNull(dto.getOperatorMemberId(),"operatorMemberId");
        CsStringUtils.checkIsNull(dto.getCostType(),"costType");
        CsStringUtils.checkIsNull(dto.getBillCheckType(),"billCheckType");
        if( CollectionUtils.isEmpty(dto.getOperatorRoleNameSet()) || !dto.getOperatorRoleNameSet().stream().anyMatch(item->ROLE_FINANCIAL.contains(item)) ){
            log.info("操作人没有对账发起角色(财务角色):{},operatorRoleNameSet:{}",ROLE_FINANCIAL,dto.getOperatorRoleNameSet());
            throw new BizException(BasicCode.CUSTOM_ERROR,"你没有发起对账的权限,请联系管理员授权对账-财务角色");
        }
        if( !BillCheckTypeEnum.PAYABLE.getCode().equals(dto.getBillCheckType()) && !BillCheckTypeEnum.RECEIVABLE.getCode().equals(dto.getBillCheckType()) ){
            throw new BizException(BasicCode.CUSTOM_ERROR, "对账类型不正确");
        }
        //如果有一个为空，则2个都为空
        if (CsStringUtils.isBlank(dto.getBillCheckDateBegin()) || CsStringUtils.isBlank(dto.getBillCheckDateEnd())) {
            dto.setBillCheckDateBegin(null);
            dto.setBillCheckDateEnd(null);
        }
        getAllSaleRegion(dto);
        //卖家功能
        if( AppNames.WEB_SERVICE_SELLER.getPlatform().equals(dto.getAppName())) {
            //如果没有销售区域、没有时间区域 有收款方或者付款方 取默认规则
            if ((CsStringUtils.isNotBlank(dto.getPayeeMemberId()) || CsStringUtils.isNotBlank(dto.getPayerMemberId())) &&
                    CsStringUtils.isBlank(dto.getBillCheckDateBegin())) {
                log.info("--------1------------");
                //取默认规则
                return generateItem(dto);
            } else {
                List<MemberIdAndNameDTO> buyerList = Lists.newArrayList();
                List<String> buyerIds = null;
                //如果收款方或者付款方为空，销售区域不为空，则查询数据获取买家
                if (CsStringUtils.isBlank(dto.getPayeeMemberId()) &&
                        CsStringUtils.isBlank(dto.getPayerMemberId()) &&
                        CollectionUtils.isNotEmpty(dto.getSaleRegionIds())) {
                    log.info("--------2------------");
                    if (BillCheckTypeEnum.PAYABLE.getCode().equals(dto.getBillCheckType())) {
                        buyerList = billCheckWaybillBaseDataBiz.findCarrierList(dto.getOperatorMemberId(), dto.getSaleRegionIds(), dto.getBillCheckDateBegin(), dto.getBillCheckDateEnd());
                    } else {
                        buyerList = billCheckWaybillBaseDataBiz.findBuyerList(dto.getOperatorMemberId(), dto.getSaleRegionIds(), dto.getBillCheckDateBegin(), dto.getBillCheckDateEnd());
                    }

                    if (CollectionUtils.isEmpty(buyerList)) {
                        throw new BizException(BasicCode.CUSTOM_ERROR, "没有找到有数据的买家");
                    }
                    buyerIds = buyerList.stream().map(MemberIdAndNameDTO::getMemberId).toList();
                }else{
                    log.info("--------3------------");
                    //否则取入参
                    if (CsStringUtils.isNotBlank(dto.getPayeeMemberId())) {
                        buyerIds = Lists.newArrayList(dto.getPayeeMemberId());
                        MemberIdAndNameDTO memberIdAndNameDTO = new MemberIdAndNameDTO();
                        memberIdAndNameDTO.setMemberId(dto.getPayeeMemberId());
                        MemberSimpleDTO memberSimple = memberService.findMemberSimpleById(dto.getPayeeMemberId());
                        memberIdAndNameDTO.setMemberName(memberSimple == null ? null : memberSimple.getMemberName());
                        buyerList.add( memberIdAndNameDTO);
                    } else if (CsStringUtils.isNotBlank(dto.getPayerMemberId())) {
                        buyerIds = Lists.newArrayList(dto.getPayerMemberId());
                        MemberIdAndNameDTO memberIdAndNameDTO = new MemberIdAndNameDTO();
                        memberIdAndNameDTO.setMemberId(dto.getPayerMemberId());
                        MemberSimpleDTO memberSimple = memberService.findMemberSimpleById(dto.getPayerMemberId());
                        memberIdAndNameDTO.setMemberName(memberSimple == null ? null : memberSimple.getMemberName());
                        buyerList.add( memberIdAndNameDTO);
                    }
                }
                log.info("buyerIds:{}",buyerIds);
                //如果账期为空，则查询默认规则，如果规则不存在，则不予生成
                List<BillCheckRule> checkRules;
                if (CsStringUtils.isBlank(dto.getBillCheckDateBegin())) {
                    log.info("--------4------------");
                    Condition condition = new Condition(BillCheckRule.class);
                    Example.Criteria criteria = condition.createCriteria()
                            .andNotEqualTo("checkCycle", BillCheckCycleEnum.CUSTOM.getCode())
                            .andEqualTo("billCheckType", dto.getBillCheckType())
                            .andEqualTo("costType",dto.getCostType());
                    if(BillCheckTypeEnum.PAYABLE.getCode().equals(dto.getBillCheckType())){
                        //如果是应付 则当前会员是付款方
                        criteria.andEqualTo("payerMemberId",dto.getOperatorMemberId());
                        criteria.andIn("payeeMemberId",buyerIds);
                    }else if(BillCheckTypeEnum.RECEIVABLE.getCode().equals(dto.getBillCheckType())){
                        //如果是应收 则当前会员是收款款方
                        criteria.andIn("payerMemberId",buyerIds);
                        criteria.andEqualTo("payeeMemberId",dto.getOperatorMemberId());
                    }
                    criteria.andEqualTo("delFlg",false);
                    checkRules = billCheckRuleBiz.findByCondition(condition);
                    if( CollectionUtils.isEmpty(checkRules)){
                        throw new BizException(BasicCode.CUSTOM_ERROR, "没有找到对应的对账规则");
                    }
                }else{
                    log.info("--------5------------");
                    checkRules = billCheckRuleBiz.addCustomList(dto, buyerList);
                }
                log.info("--------6------------checkRules.size:{}", checkRules.size());
                List<String> errorList = Lists.newArrayList();
                if(CollUtil.isNotEmpty(checkRules)){
                    for (BillCheckRule billCheckRule : checkRules) {
                        String msg = dto.getOperatorMemberId().equals(billCheckRule.getPayerMemberId()) ? billCheckRule.getPayeeMemberName() : billCheckRule.getPayerMemberName();
                        try {
                            billCheckTimeWindowCheck(billCheckRule,dto.getOperator());
                            invokeJob(dto.getAppName(), billCheckRule, null, dto.getOperator(), dto.getOperatorMemberId(), false);
                        }catch (Exception e){
                            log.error(e.getMessage(),e);
                            if (CsStringUtils.isNotBlank(e.getMessage())) {
                                errorList.add(msg + "对账单生成错误: " + e.getMessage());
                            }
                        }
                    }
                }
                if( !errorList.isEmpty() ){
                    throw new BizException(BasicCode.CUSTOM_ERROR, String.join("\n",errorList)+"\n"+"其他客户账单已生成");
                }
                return true;
            }
        }
        return generateItem(dto);
    }

    private void getAllSaleRegion(BillCheckCreateDTO dto ){
        if( BooleanUtils.isTrue(dto.getSelectedAllSaleRegion())){
            List<SaleRegionSampleDTO> dtoList = saleRegionService.findAllSampleByMemberId(dto.getOperatorMemberId());
            if( CollectionUtils.isNotEmpty(dtoList)){
                dto.setSaleRegionIds(dtoList.stream().map(SaleRegionSampleDTO::getSaleRegionId).distinct().toList());
            }
        }
    }

    private Boolean generateItem(BillCheckCreateDTO dto) {
        Condition condition = new Condition(BillCheckRule.class);
        Example.Criteria criteria = condition.createCriteria()
                .andEqualTo("billCheckType", dto.getBillCheckType())
                .andNotEqualTo("checkCycle", BillCheckCycleEnum.CUSTOM.getCode())
                .andEqualTo("costType",dto.getCostType());

        if(BillCheckTypeEnum.PAYABLE.getCode().equals(dto.getBillCheckType())){
            //如果是应付 则当前会员是付款方
            CsStringUtils.checkIsNull(dto.getPayeeMemberId(),"payeeMemberId");
            criteria.andEqualTo("payerMemberId",dto.getOperatorMemberId());
            criteria.andEqualTo("payeeMemberId",dto.getPayeeMemberId());
        }else if(BillCheckTypeEnum.RECEIVABLE.getCode().equals(dto.getBillCheckType())){
            //如果是应收 则当前会员是收款款方
            CsStringUtils.checkIsNull(dto.getPayerMemberId(),"payerMemberId");
            criteria.andEqualTo("payerMemberId",dto.getPayerMemberId());
            criteria.andEqualTo("payeeMemberId",dto.getOperatorMemberId());
        }else{
            throw new BizException(BasicCode.CUSTOM_ERROR, "对账类型异常");
        }
        criteria.andEqualTo("delFlg",false);
        List<BillCheckRule> checkRules = billCheckRuleBiz.findByCondition(condition);
        if( CollectionUtils.isEmpty(checkRules) ){
            throw new BizException(BasicCode.CUSTOM_ERROR,"对账规则没有配置");
        }
        if( checkRules.size() != 1 ){
            throw new BizException(BasicCode.CUSTOM_ERROR,"发现多条符合条件的对账规则");
        }
        if( BooleanUtils.isTrue(checkRules.get(0).getAutoCreate()) ){
            throw new BizException(BasicCode.CUSTOM_ERROR,"不能手工创建(对应规则为自动创建)");
        }
        BillCheckRule billCheckRule = checkRules.get(0);
        if (CsStringUtils.isNotBlank(dto.getBillCheckDateBegin())) {
            billCheckRule.setCheckDateBegin(dto.getBillCheckDateBegin());
            billCheckRule.setCheckDateEnd(dto.getBillCheckDateEnd());
        }
        billCheckTimeWindowCheck(billCheckRule,dto.getOperator());
        return invokeJob(dto.getAppName(), billCheckRule, null, dto.getOperator(), dto.getOperatorMemberId(), false);
    }

    /**
     * 对账单时间区间重叠校验,校验当前要生成的对账单和已完成的对账单
     */
    private void billCheckTimeWindowCheck(BillCheckRule rule,String operator){
        if (CsStringUtils.isBlank(rule.getCheckDateBegin())) {
            Map<String, String> billCheckDateInfo = billCheckRuleBiz.getBillCheckDateInfo(rule, LocalDateTime.now());
            rule.setCheckDateBegin(billCheckDateInfo.get(BillCheckRuleBiz.BILL_CHECK_DATE_BEGIN).substring(0,10));
            rule.setCheckDateEnd(billCheckDateInfo.get(BillCheckRuleBiz.BILL_CHECK_DATE_END).substring(0,10));
        }
        String begin = rule.getCheckDateBegin()+" 00:00:00";
        String end = rule.getCheckDateEnd()+" 23:59:59";

        List<String> status = Lists.newArrayList(
                BillCheckStatusEnum.WAIT_BUSINESS_CONFIRM.getCode(),
                BillCheckStatusEnum.WAIT_CUSTOMER_CONFIRM.getCode(),
                BillCheckStatusEnum.COMPLETED.getCode());

        Condition condition = new Condition(BillCheckInfo.class);
        condition.createCriteria().andEqualTo("delFlg",false)
                .andEqualTo("billCheckType",rule.getBillCheckType())
                .andEqualTo("costType",rule.getCostType())
                .andEqualTo("payeeMemberId",rule.getPayeeMemberId())
                .andEqualTo("payerMemberId",rule.getPayerMemberId())
                //时间窗口：A - B  时间窗口： 1 - 2
                //  A  1  2 B
                //  A  1  B 2
                //  1  A  2 B
                //  1  A  B 2
                // A < 2 && B > 1
                .andLessThan("billCheckBegin",end)
                .andGreaterThan("billCheckEnd",begin);

        List<BillCheckInfo> list = billCheckInfoBiz.findByCondition(condition);
        if( CollectionUtils.isNotEmpty(list)){
            log.info("手工触发对账单生成 billCheckTimeWindowCheck list.size: {}",list.size());
            if( list.stream().anyMatch(item->status.contains(item.getBillCheckStatus())) ) {
                throw new BizException(BasicCode.CUSTOM_ERROR, "与对账单账期有重叠:" + list.stream().map(BillCheckInfo::getBillCheckNo).collect(Collectors.joining(",")));
            }else{
                for (BillCheckInfo billCheckInfo : list) {
                    String delId = uuidGenerator.gain();
                    billCheckInfoBiz.deleteByBillCheckId(billCheckInfo.getBillCheckId(),delId,operator);
                    billCheckGoodsInfoBiz.deleteByBillCheckId(billCheckInfo.getBillCheckId(),delId,operator);
                    billCheckWaybillInfoBiz.deleteByBillCheckId(billCheckInfo.getBillCheckId(),delId,operator);
                    billCheckChangeLogBiz.deleteByBillCheckId(billCheckInfo.getBillCheckId(),delId,operator);
                }
            }
        }else{
            log.info("手工触发对账单生成 billCheckTimeWindowCheck list.size: null");
        }
    }

    @Override
    public Boolean regenerate(BillCheckReDoDTO dto) {
        log.info("手工触发对账单重新生成: {}",JSON.toJSONString(dto));
        CsStringUtils.checkIsNull(dto.getOperator(),"operator");
        CsStringUtils.checkIsNull(dto.getBillCheckId(),"billCheckId");
        if( CollectionUtils.isEmpty(dto.getOperatorRoleNameSet()) || !dto.getOperatorRoleNameSet().stream().anyMatch(item->ROLE_FINANCIAL.contains(item)) ){
            log.info("操作人没有对账发起角色(财务角色):{},operatorRoleNameSet:{}",ROLE_FINANCIAL,dto.getOperatorRoleNameSet());
            throw new BizException(BasicCode.CUSTOM_ERROR,"你没有发起对账的权限,请联系管理员授权对账-财务角色");
        }
        BillCheckInfo billCheckInfo = billCheckInfoBiz.get(dto.getBillCheckId());
        if( billCheckInfo == null || BooleanUtils.isTrue(billCheckInfo.getDelFlg())){
            throw new BizException(BasicCode.CUSTOM_ERROR,"对账单不存在或已被删除");
        }
        if( BillCheckStatusEnum.COMPLETED.getCode().equals(billCheckInfo.getBillCheckStatus()) ){
            throw new BizException(BasicCode.CUSTOM_ERROR,"对账单单状态为已完成");
        }
        if (!CsStringUtils.equals(dto.getOperatorMemberId(), billCheckInfo.getPayeeMemberId()) &&
                !CsStringUtils.equals(dto.getOperatorMemberId(), billCheckInfo.getPayerMemberId())) {
            log.info("你不是对账单的收款方或者付款方,payeeMemberId: {},payerMemberId: {},operatorMemberId: {}",
                    billCheckInfo.getPayeeMemberId(),billCheckInfo.getPayerMemberId(),dto.getOperatorMemberId());
            throw new BizException(BasicCode.CUSTOM_ERROR,"你没有操作权限");
        }
        String ruleId = billCheckInfo.getBillCheckRuleId();
        return invokeJob(dto.getAppName(), billCheckRuleBiz.get(ruleId), dto.getBillCheckId(), dto.getOperator(), dto.getOperatorMemberId(), true);
    }

    private Boolean invokeJob(String memberRole,BillCheckRule billCheckRule,String billCheckId,String operator,String operatorMemberId,boolean retry){
        try {
            if (billCheckRule == null || BooleanUtils.isTrue(billCheckRule.getDelFlg())) {
                throw new BizException(BasicCode.CUSTOM_ERROR, "对账单规则不存在或已被删除");
            }
            if (!CsStringUtils.equals(operatorMemberId, billCheckRule.getPayeeMemberId()) && !CsStringUtils.equals(operatorMemberId, billCheckRule.getPayerMemberId())) {
                throw new BizException(BasicCode.CUSTOM_ERROR, "对账单规则不存在或已被删除");
            }
            if (!CsStringUtils.equals(billCheckRule.getMemberRole(), memberRole)) {
                throw new BizException(BasicCode.CUSTOM_ERROR, "只有对账发起方可以操作");
            }
            CostTypeEnum costTypeEnum = CostTypeEnum.getByCode(billCheckRule.getCostType());
            if (costTypeEnum == null) {
                log.error("对账单规则费用类型为空了:{}", billCheckRule);
                throw new BizException(BasicCode.CUSTOM_ERROR, "对账单规则费用类型为空了");
            }
            if (CsStringUtils.isBlank(billCheckId)) {
                //账期
                String createDate = billCheckRuleBiz.getBillCheckDateInfo(billCheckRule, LocalDateTime.now()).get(BillCheckRuleBiz.BILL_CHECK_DATE);
                // 以合同为单位创建对账单 重新手工触发时 ，如果有验证完成的对账单，则不重新生成
                if (CostTypeEnum.CONCRETE != costTypeEnum) {
                    List<BillCheckInfo>  billCheckInfoList = billCheckInfoBiz.findByRule(billCheckRule.getCostType(), createDate, billCheckRule.getPayeeMemberId(), billCheckRule.getPayerMemberId());
                    for (BillCheckInfo billCheckInfo : billCheckInfoList) {
                        if (billCheckInfo != null && !BillCheckStatusEnum.DRAFT.getCode().equals(billCheckInfo.getBillCheckStatus()) && !BillCheckStatusEnum.RETURNED.getCode().equals(billCheckInfo.getBillCheckStatus())) {
                            throw new BizException(BasicCode.CUSTOM_ERROR, "对账单已存在(" + billCheckInfo.getBillCheckNo() + ")状态为: " + BillCheckStatusEnum.getByCode(billCheckInfo.getBillCheckStatus()).getMessage());
                        }
                    }
                }
            }
            billCheckId = CsStringUtils.isBlank(billCheckId) ? uuidGenerator.gain() : billCheckId;
            log.info("手工触发{}生成对账单,billCheckId:{},billCheckRule:{}", retry ? "重新" : "", billCheckId, billCheckRule);
            LocalDateTime now = LocalDateTime.now();
            //应收
            if (BillCheckTypeEnum.RECEIVABLE.getCode().equals(billCheckRule.getBillCheckType())) {
                switch (costTypeEnum) {
                    case GOODS: {
                        billCheckReceivableGoodsJob.execute(billCheckRule, billCheckId, operator, retry, now);
                        break;
                    }
                    case CONCRETE: {
                        billCheckReceivableConcreteJob.execute(billCheckRule, billCheckId, operator, retry, now);
                        break;
                    }
                    case LOGISTICS_ROAD: {
                        billCheckReceivableLogisticsRoadJob.execute(billCheckRule, billCheckId, operator, retry, now);
                        break;
                    }
                    case LOGISTICS_SHIP: {
                        billCheckReceivableLogisticsWaterJob.execute(billCheckRule, billCheckId, operator, retry, now);
                        break;
                    }
                    case DRIVER:
                        throw new BizException(BasicCode.CUSTOM_ERROR, "司机应收费用只能由平台发起");
                    case ENTREPOT_STORAGE_CONTRACTOR:
                        throw new BizException(BasicCode.CUSTOM_ERROR, "不支持的费用类型");
                }
            } else if (BillCheckTypeEnum.PAYABLE.getCode().equals(billCheckRule.getBillCheckType())) {
                //应付
                switch (costTypeEnum) {
                    case GOODS:
                    case CONCRETE:
                        throw new BizException(BasicCode.CUSTOM_ERROR, "不可发起应付商品款");
                    case LOGISTICS_ROAD: {
                        //应付物流款对账单-汽运
                        billCheckPayableLogisticsRoadJob.execute(billCheckRule, billCheckId, operator, retry, now);
                        break;
                    }
                    case LOGISTICS_SHIP: {
                        //应付物流款对账单-船运
                        billCheckPayableLogisticsWaterJob.execute(billCheckRule, billCheckId, operator, retry, now);
                        break;
                    }
                    case DRIVER: {
                        String billCheckId2 = billCheckId;
                        threadPoolTaskExecutor.execute(()->{
                            if( retry ){
                                //平台发起司机应付对账单
                                billCheckPayableDriverJob.executeRetry(billCheckRule, billCheckId2, operator, true, now);
                            }else{
                                //平台发起司机应付对账单
                                billCheckPayableDriverJob.execute(billCheckRule, billCheckId2, operator, false, now);
                            }
                        });
                        break;
                    }
                    case ENTREPOT_STORAGE_CONTRACTOR:
                        throw new BizException(BasicCode.CUSTOM_ERROR, "不支持的费用类型");
                }
            } else {
                throw new BizException(BasicCode.CUSTOM_ERROR, "对账类型异常");
            }
            return true;
        }catch (DistributeLockException e){
            log.error(e.getMessage(),e);
            throw new BizException(BasicCode.CUSTOM_ERROR,"请稍后重试");
        }
    }

    @Transactional
    @Override
    public Boolean approve(BillCheckApprovalDTO dto) {
        log.info("确认对账单:{}",JSON.toJSONString(dto));
        if( dto == null ){
            throw new BizException(BasicCode.CUSTOM_ERROR,"请求参数不可为空");
        }
        CsStringUtils.checkIsNull(dto.getPass(),"pass");
        CsStringUtils.checkIsNull(dto.getOperator(),"operator");
        CsStringUtils.checkIsNull(dto.getOperatorMemberId(),"operatorMemberId");
        CsStringUtils.checkIsNull(dto.getOperatorName(),"operatorName");
        CsStringUtils.checkIsNull(dto.getAppName(),"appName");

        if (CsStringUtils.isBlank(dto.getBillCheckId()) && CollectionUtils.isEmpty(dto.getBillCheckIdList())) {
            log.error("请求参数billCheckId或billCheckIdList不可都为空");
            throw new BizException(BasicCode.PARAM_NULL, "对账单id");
        }

        if(!AppNames.WEB_SERVICE_DRIVER.getPlatform().equals(dto.getAppName())) {
            if (CollectionUtils.isEmpty(dto.getOperatorRoleNameSet()) ||
                    !dto.getOperatorRoleNameSet().stream().anyMatch(item -> ROLE_FINANCIAL.contains(item) || ROLE_BUSINESS.contains(item) || ROLE_CUSTOMER.contains(item))) {
                log.info("操作人没有确认角色:{},{},{},operatorRoleNameSet:{}", ROLE_FINANCIAL, ROLE_BUSINESS, ROLE_CUSTOMER, dto.getOperatorRoleNameSet());
                throw new BizException(BasicCode.CUSTOM_ERROR, "您没有确认权限");
            }
        }

        if (CsStringUtils.isNotBlank(dto.getBillCheckId())) {
            approveItem(dto);
        }else if( CollectionUtils.isNotEmpty(dto.getBillCheckIdList())){
            for (String billCheckId : dto.getBillCheckIdList()) {
                dto.setBillCheckId(billCheckId);
                approveItem(dto);
            }
        }
        return true;
    }

    private void approveItem(BillCheckApprovalDTO dto) {
        log.info("确认对账单 {}",JSON.toJSONString(dto));
        BillCheckInfo billCheckInfo = billCheckInfoBiz.get(dto.getBillCheckId());
        if( billCheckInfo == null || BooleanUtils.isTrue(billCheckInfo.getDelFlg())){
            throw new BizException(BasicCode.CUSTOM_ERROR,"对账单不存在或已被删除");
        }
        if(BillCheckStatusEnum.COMPLETED.getCode().equals(billCheckInfo.getBillCheckStatus())){
            throw new BizException(BasicCode.CUSTOM_ERROR,"对账确认已结束");
        }
        if (!CsStringUtils.equals(dto.getOperatorMemberId(), billCheckInfo.getPayeeMemberId()) &&
                !CsStringUtils.equals(dto.getOperatorMemberId(), billCheckInfo.getPayerMemberId())) {
            log.info("你不是对账单的收款方或者付款方,payeeMemberId: {},payerMemberId: {},operatorMemberId: {}",
                    billCheckInfo.getPayeeMemberId(),billCheckInfo.getPayerMemberId(),dto.getOperatorMemberId());
            throw new BizException(BasicCode.CUSTOM_ERROR,"你没有操作权限");
        }
        String status = billCheckInfo.getBillCheckStatus();
        String newStatus = null;
        String nodeName = "";
        if( (BillCheckStatusEnum.DRAFT.getCode().equals(status) || BillCheckStatusEnum.RETURNED.getCode().equals(status))){
            if( !dto.getOperatorRoleNameSet().stream().anyMatch(item->ROLE_FINANCIAL.contains(item)) ){
                throw new BizException(BasicCode.CUSTOM_ERROR,"您没有财务确认权限");
            }
            if (!CsStringUtils.equals(dto.getAppName(), billCheckInfo.getMemberRole())) {
                throw new BizException(BasicCode.CUSTOM_ERROR,"只有对账单发起方有财务确认权限");
            }
            newStatus = BillCheckStatusEnum.WAIT_BUSINESS_CONFIRM.getCode();
            //物流款不需要业务确认 2021.3.24 与娟姐确认 司机款也不需要业务确认
            if(CostTypeEnum.LOGISTICS_SHIP.getCode().equals(billCheckInfo.getCostType()) ||
                    CostTypeEnum.LOGISTICS_ROAD.getCode().equals(billCheckInfo.getCostType()) ||
                    CostTypeEnum.DRIVER.getCode().equals(billCheckInfo.getCostType())){
                newStatus = BillCheckStatusEnum.WAIT_CUSTOMER_CONFIRM.getCode();
            }
            nodeName = "财务";
        }else if(BillCheckStatusEnum.WAIT_BUSINESS_CONFIRM.getCode().equals(status)){
            if( !dto.getOperatorRoleNameSet().stream().anyMatch(item->ROLE_BUSINESS.contains(item)) ) {
                throw new BizException(BasicCode.CUSTOM_ERROR, "您没有业务确认权限");
            }
            newStatus = BillCheckStatusEnum.WAIT_CUSTOMER_CONFIRM.getCode();
            nodeName = "业务";
        }else if(BillCheckStatusEnum.WAIT_CUSTOMER_CONFIRM.getCode().equals(status) ){
            //如果是不是司机费用，且没权限
            if( !CostTypeEnum.DRIVER.getCode().equals(billCheckInfo.getCostType()) &&
                    !dto.getOperatorRoleNameSet().stream().anyMatch(item->ROLE_CUSTOMER.contains(item)) ) {
                throw new BizException(BasicCode.CUSTOM_ERROR, "您没有客户确认权限");
            }
            newStatus = BillCheckStatusEnum.COMPLETED.getCode();
            nodeName = "客户";
        }
        //退回
        if(BooleanUtils.isNotTrue(dto.getPass())){
            newStatus = BillCheckStatusEnum.RETURNED.getCode();
        }
        String msg = (dto.getPass() ? "确认对账单" : "退回对账单") + (dto.getRemark() == null ? "" : "("+dto.getRemark().trim()+")");
        //记录changeLog
        BillCheckChangeLog billCheckChangeLog = new BillCheckChangeLog();
        billCheckChangeLog.setBillCheckChangeLogId(uuidGenerator.gain());
        billCheckChangeLog.setBillCheckId(billCheckInfo.getBillCheckId());
        billCheckChangeLog.setRemark(msg);
        billCheckChangeLog.setCreateUser(dto.getOperator());
        billCheckChangeLog.setCreateUserName(dto.getOperatorName());
        billCheckChangeLog.setStatus(dto.getPass());
        billCheckChangeLog.setNodeName(nodeName);
        billCheckChangeLog.setDelFlg(false);
        billCheckChangeLog.setCreateTime(new Date());
        billCheckChangeLogBiz.getMapper().insert(billCheckChangeLog);
        //更新对账单状态
        BillCheckInfo updateBillCheck = new BillCheckInfo();
        updateBillCheck.setBillCheckId(billCheckInfo.getBillCheckId());
        updateBillCheck.setBillCheckStatus(newStatus);
        updateBillCheck.setUpdateTime(billCheckChangeLog.getCreateTime());
        updateBillCheck.setUpdateUser(dto.getOperator());
        billCheckInfoBiz.updateSelective(updateBillCheck);
    }

    @Transactional
    @Override
    public Boolean updateBillCheck(BillCheckUpdateDTO dto) {
        log.info("updateBillCheck: {}",JSON.toJSONString(dto));
        if( dto == null ){
            log.error("billCheckUpdateDTO is null.");
            return false;
        }
        CsStringUtils.checkIsNull(dto.getOperator(),"operator");
        CsStringUtils.checkIsNull(dto.getOperatorMemberId(),"operatorMemberId");
        CsStringUtils.checkIsNull(dto.getBillCheckId(),"billCheckId");

        BillCheckInfo billCheckInfo = billCheckInfoBiz.get(dto.getBillCheckId());
        if( billCheckInfo == null || BooleanUtils.isTrue(billCheckInfo.getDelFlg())){
            throw new BizException(BasicCode.CUSTOM_ERROR,"对账单不存在或已被删除");
        }
        //财务可修改的状态才可以编辑
        if( !BillCheckStatusEnum.DRAFT.getCode().equals(billCheckInfo.getBillCheckStatus()) &&
                !BillCheckStatusEnum.RETURNED.getCode().equals(billCheckInfo.getBillCheckStatus()) ){
            throw new BizException(BasicCode.CUSTOM_ERROR,"当前状态不可修改");
        }
        if (!CsStringUtils.equals(dto.getOperatorMemberId(), billCheckInfo.getPayeeMemberId()) &&
                !CsStringUtils.equals(dto.getOperatorMemberId(), billCheckInfo.getPayerMemberId())) {
            log.info("你不是对账单的收款方或者付款方,payeeMemberId: {},payerMemberId: {},operatorMemberId: {}",
                    billCheckInfo.getPayeeMemberId(),billCheckInfo.getPayerMemberId(),dto.getOperatorMemberId());
            throw new BizException(BasicCode.CUSTOM_ERROR,"你没有操作权限");
        }
        //财务角色才可以修改
        if( CollectionUtils.isEmpty(dto.getOperatorRoleNameSet()) ||
                !dto.getOperatorRoleNameSet().stream().anyMatch(item->ROLE_FINANCIAL.contains(item)) ){
            log.info("操作人没有财务确认角色:{},operatorRoleNameSet:{}",ROLE_FINANCIAL,dto.getOperatorRoleNameSet());
            throw new BizException(BasicCode.CUSTOM_ERROR,"只有发起方财务角色可以修改");
        }
        //发起方要一致
        BillCheckRule billCheckRule = billCheckRuleBiz.get(billCheckInfo.getBillCheckRuleId());
        if (!CsStringUtils.equals(billCheckRule.getMemberRole(), dto.getAppName())) {
            throw new BizException(BasicCode.CUSTOM_ERROR,"只有发起方财务角色可以修改");
        }
        Date now = new Date();
        if (CsStringUtils.isNotBlank(dto.getRemark())) {
            BillCheckInfo updateRemark = new BillCheckInfo();
            updateRemark.setBillCheckId(dto.getBillCheckId());
            updateRemark.setRemark(dto.getRemark());
            updateRemark.setUpdateUser(dto.getOperator());
            updateRemark.setUpdateTime(now);
            billCheckInfoBiz.updateSelective(updateRemark);
        }

        List<BillCheckGoodsInfo> goodsInfoList = billCheckGoodsInfoBiz.findByBillCheckId(dto.getBillCheckId());
        log.info("goodsInfoList.size : {}",goodsInfoList.size());
        Map<String, BillCheckGoodsInfo> goodsInfoMap = goodsInfoList.stream().collect(Collectors.toMap(BillCheckGoodsInfo::getBillCheckGoodsInfoId, Function.identity(),(k1,k2)->k2));
        Set<String> oldIdSet = Sets.newHashSet(goodsInfoMap.keySet());
        int updateCount = 0;
        int insertCount = 0;
        int deleteCount = 0;
        if( CollectionUtils.isNotEmpty(dto.getGoodsInfoList())) {
            for (BillCheckGoodsInfoUpdateDTO updateDTO : dto.getGoodsInfoList()) {
                BillCheckGoodsInfo billCheckGoodsInfoOld = goodsInfoMap.get(updateDTO.getBillCheckGoodsInfoId());
                if (billCheckGoodsInfoOld == null) {
                    log.info("ignore billCheckGoodsInfoId:{},BillCheckGoodsInfoUpdateDTO:{},goodsInfoMap: {}",updateDTO.getBillCheckGoodsInfoId(), updateDTO,goodsInfoMap);
                    continue;
                }
                BillCheckGoodsInfo billCheckGoodsInfo = new BillCheckGoodsInfo();
                //如果是手工插入的行 且不是已存在的手工添加行
                if ( BooleanUtils.isTrue(updateDTO.getAddFlg()) ) {
                    //复制原有的行
                    BeanUtils.copyProperties(billCheckGoodsInfoOld, billCheckGoodsInfo);
                    billCheckGoodsInfo.setBillCheckGoodsInfoId(uuidGenerator.gain());
                    billCheckGoodsInfo.setUserAddFlg(true);
                    billCheckGoodsInfo.setDelFlg(false);
                    billCheckGoodsInfo.setCreateTime(now);
                    billCheckGoodsInfo.setCreateUser(dto.getOperator());
                    billCheckGoodsInfo.setUpdateTime(now);
                    billCheckGoodsInfo.setUpdateUser(dto.getOperator());

                    billCheckGoodsInfo.setUnitGoodsPrice(BigDecimal.ZERO);
                    billCheckGoodsInfo.setUnitLogisticPrice(BigDecimal.ZERO);
                    billCheckGoodsInfo.setGoodsAmount(BigDecimal.ZERO);
                    billCheckGoodsInfo.setEmptyLoadFee(BigDecimal.ZERO);
                    billCheckGoodsInfo.setFloorTruckage(BigDecimal.ZERO);
                    //插入行修改的字段
                    //商品
                    billCheckGoodsInfo.setGoodsId(updateDTO.getGoodsId());
                    billCheckGoodsInfo.setGoodsName(updateDTO.getGoodsName());
                    //手工调整项类型
                    billCheckGoodsInfo.setUserAddType(updateDTO.getUserAddType());
                    //备注
                    billCheckGoodsInfo.setUserAddRemark(updateDTO.getUserAddRemark());
                    //调整金额
                    billCheckGoodsInfo.setGoodsAmount(updateDTO.getGoodsAmount());
                    //调整数量
                    billCheckGoodsInfo.setWaybillQuantity(updateDTO.getWaybillQuantity());
                    //销售区域
                    billCheckGoodsInfo.setSaleRegionId(updateDTO.getSaleRegionId());
                    billCheckGoodsInfo.setSaleRegionName(updateDTO.getSaleRegionName());
                    //配送方式
                    billCheckGoodsInfo.setDeliverWay(updateDTO.getDeliverWay());
                    //开始日期
                    billCheckGoodsInfo.setBillCheckBegin(updateDTO.getBillCheckBegin());
                    //结束日期
                    billCheckGoodsInfo.setBillCheckEnd(updateDTO.getBillCheckEnd());
                    //发货点
                    billCheckGoodsInfo.setWarehouseId(updateDTO.getWarehouseId());
                    billCheckGoodsInfo.setWarehouseName(updateDTO.getWarehouseName());
                    //收货点
                    billCheckGoodsInfo.setReceiveAddressId(updateDTO.getReceiveAddressId());
                    billCheckGoodsInfo.setReceiveAddressName(updateDTO.getReceiveAddressName());
                    //单价
                    billCheckGoodsInfo.setUnitGoodsPrice(updateDTO.getPrice());

                    billCheckGoodsInfoBiz.getMapper().insert(billCheckGoodsInfo);
                    insertCount++;
                    continue;
                }
                //更新了备注字段的行
                if (!CsStringUtils.equals(billCheckGoodsInfoOld.getRemark(), updateDTO.getRemark())) {
                    billCheckGoodsInfo.setBillCheckGoodsInfoId(billCheckGoodsInfoOld.getBillCheckGoodsInfoId());
                    //更新行的备注字段
                    billCheckGoodsInfo.setRemark(updateDTO.getRemark());
                    billCheckGoodsInfoBiz.updateSelective(billCheckGoodsInfo);
                    updateCount++;
                }
                oldIdSet.remove(updateDTO.getBillCheckGoodsInfoId());
            }
        }
        if( !oldIdSet.isEmpty() ) {
            //删除原有数据中更新操作中不存才的行
            for (String item : oldIdSet) {
                BillCheckGoodsInfo billCheckGoodsInfo = new BillCheckGoodsInfo();
                billCheckGoodsInfo.setBillCheckGoodsInfoId(item);
                billCheckGoodsInfo.setDelFlg(true);
                billCheckGoodsInfo.setUpdateTime(now);
                billCheckGoodsInfo.setUpdateUser(dto.getOperator());
                billCheckGoodsInfoBiz.updateSelective(billCheckGoodsInfo);
                deleteCount++;
            }
        }
        log.info("updateBillCheck billCheckId:{},更新了{}行，插入了{}行,删除了{}行手工插入的行.",
                dto.getBillCheckId(), updateCount, insertCount, deleteCount);
        //汇总金额
        billCheckInfoBiz.updateAmount(dto.getBillCheckId());
        return true;
    }

    @Transactional
    @Override
    public Boolean deleteBillCheck(BillCheckDeleteDTO dto) {
        if( dto == null ){
            log.error("billCheckUpdateDTO is null.");
            return false;
        }
        CsStringUtils.checkIsNull(dto.getOperator(),"operator");
        CsStringUtils.checkIsNull(dto.getOperatorMemberId(),"operatorMemberId");
        CsStringUtils.checkIsNull(dto.getBillCheckId(),"billCheckId");

        BillCheckInfo billCheckInfo = billCheckInfoBiz.get(dto.getBillCheckId());
        if( billCheckInfo == null || BooleanUtils.isTrue(billCheckInfo.getDelFlg())){
            log.info("对账单不存在或已被删除");
            return true;
        }
        //财务可修改的状态才可以编辑
        if( !BillCheckStatusEnum.DRAFT.getCode().equals(billCheckInfo.getBillCheckStatus()) &&
                !BillCheckStatusEnum.RETURNED.getCode().equals(billCheckInfo.getBillCheckStatus()) ){
            throw new BizException(BasicCode.CUSTOM_ERROR,"当前状态不可删除");
        }
        if (!CsStringUtils.equals(dto.getOperatorMemberId(), billCheckInfo.getPayeeMemberId()) &&
                !CsStringUtils.equals(dto.getOperatorMemberId(), billCheckInfo.getPayerMemberId())) {
            log.info("你不是对账单的收款方或者付款方,payeeMemberId: {},payerMemberId: {},operatorMemberId: {}",
                    billCheckInfo.getPayeeMemberId(),billCheckInfo.getPayerMemberId(),dto.getOperatorMemberId());
            throw new BizException(BasicCode.CUSTOM_ERROR,"你没有操作权限");
        }
        //财务角色才可以修改
        if( CollectionUtils.isEmpty(dto.getOperatorRoleNameSet()) ||
                !dto.getOperatorRoleNameSet().stream().anyMatch(item->ROLE_FINANCIAL.contains(item)) ){
            log.info("操作人没有财务确认角色:{},operatorRoleNameSet:{}",ROLE_FINANCIAL,dto.getOperatorRoleNameSet());
            throw new BizException(BasicCode.CUSTOM_ERROR,"只有发起方财务角色可以修改");
        }
        //发起方要一致
        BillCheckRule billCheckRule = billCheckRuleBiz.get(billCheckInfo.getBillCheckRuleId());
        if (!CsStringUtils.equals(billCheckRule.getMemberRole(), dto.getAppName())) {
            throw new BizException(BasicCode.CUSTOM_ERROR,"只有发起方财务角色可以删除");
        }
        String delId = uuidGenerator.gain();
        billCheckInfoBiz.deleteByBillCheckId(billCheckInfo.getBillCheckId(),delId,dto.getOperator());
        billCheckGoodsInfoBiz.deleteByBillCheckId(billCheckInfo.getBillCheckId(),delId,dto.getOperator());
        billCheckWaybillInfoBiz.deleteByBillCheckId(billCheckInfo.getBillCheckId(),delId,dto.getOperator());
        billCheckChangeLogBiz.deleteByBillCheckId(billCheckInfo.getBillCheckId(),delId,dto.getOperator());
        return true;
    }

    @Override
    public PageInfo<BillCheckInfoDTO> findAll(PageQuery<BillCheckQueryDTO> query) {
        log.info("findAll: {}",JSON.toJSONString(query));
        if( query== null || query.getQueryDTO() == null ){
            throw new BizException(BasicCode.CUSTOM_ERROR,"查询条件不可为空");
        }
        CsStringUtils.checkIsNull(query.getQueryDTO().getOperatorMemberId(),"operatorMemberId");
        CsStringUtils.checkIsNull(query.getQueryDTO().getBillCheckType(),"billCheckType");
        CsStringUtils.checkIsNull(query.getQueryDTO().getAppName(),"appName");
        //前端传参错误兼容
        if (CsStringUtils.isNotBlank(query.getQueryDTO().getBillCheckNo()) && !query.getQueryDTO().getBillCheckNo().startsWith(BillCheckInfoBiz.DZD)) {
            query.getQueryDTO().setBillCheckId(query.getQueryDTO().getBillCheckNo());
            query.getQueryDTO().setBillCheckNo(null);
        }
        PageInfo<BillCheckInfoDTO> result = billCheckInfoBiz.findAll(query);
        if( AppNames.WEB_SERVICE_DRIVER.getPlatform().equals(query.getQueryDTO().getAppName())){
            Map<String, Long> waybillCount = this.billCheckWaybillInfoBiz.countByBillCheckId(result.getList().stream().map(BillCheckInfoDTO::getBillCheckId).collect(Collectors.toSet()));
            for (BillCheckInfoDTO billCheckInfoDTO : result.getList()) {
                billCheckInfoDTO.setWaybillInfoSize(waybillCount.getOrDefault(billCheckInfoDTO.getBillCheckId(),0L));
            }
        }
        return result;
    }

    @Override
    public List<BillCheckInfoTabDTO> tabCountByQuery(BillCheckQueryDTO query) {
        log.info("tabCountByQuery : {}",JSON.toJSONString(query));
        CsStringUtils.checkIsNull(query.getOperatorMemberId(),"operatorMemberId");
        CsStringUtils.checkIsNull(query.getBillCheckType(),"billCheckType");
        CsStringUtils.checkIsNull(query.getAppName(),"appName");
        //前端传参错误兼容
        if (CsStringUtils.isNotBlank(query.getBillCheckNo()) && !query.getBillCheckNo().startsWith(BillCheckInfoBiz.DZD)) {
            query.setBillCheckId(query.getBillCheckNo());
            query.setBillCheckNo(null);
        }
        return billCheckInfoBiz.tabCountByQuery(query);
    }

    @Override
    public PageInfo<BillCheckGoodsInfoDTO> pageGoodsInfo(PageQuery<BillCheckGoodsInfoQueryDTO> query) {
        log.info("waybillFindAll:{}",JSON.toJSONString(query));
        if( query == null || query.getQueryDTO() == null ){
            return new PageInfo<>(Lists.newArrayList());
        }
        BillCheckInfo billCheckInfo = billCheckInfoBiz.get(query.getQueryDTO().getBillCheckId());
        if( billCheckInfo == null || BooleanUtils.isTrue(billCheckInfo.getDelFlg())){
            throw new BizException(BasicCode.CUSTOM_ERROR,"对账单不存在或已被删除");
        }
        if (!CsStringUtils.equals(billCheckInfo.getPayeeMemberId(), query.getQueryDTO().getOperatorMemberId()) &&
                !CsStringUtils.equals(billCheckInfo.getPayerMemberId(), query.getQueryDTO().getOperatorMemberId())) {
            throw new BizException(BasicCode.CUSTOM_ERROR,"你没有查询权限");
        }
        return billCheckGoodsInfoBiz.findDTOAll(query);
    }

    @Override
    public PageInfo<BillCheckWaybillInfoDTO> pageWaybillInfo(PageQuery<BillCheckWaybillInfoQueryDTO> query) {
        log.info("waybillFindAll:{}",JSON.toJSONString(query));
        if( query == null || query.getQueryDTO() == null ){
            return new PageInfo<>(Lists.newArrayList());
        }
        BillCheckInfo billCheckInfo = billCheckInfoBiz.get(query.getQueryDTO().getBillCheckId());
        if( billCheckInfo == null || BooleanUtils.isTrue(billCheckInfo.getDelFlg())){
            throw new BizException(BasicCode.CUSTOM_ERROR,"对账单不存在或已被删除");
        }
        return billCheckWaybillInfoBiz.findDTOAll(query);
    }

    //根据id查询(返回运单以外的对账单所有信息)
    @Override
    public BillCheckInfoDTO findById(String billCheckId) {
        return findById(billCheckId,false);
    }
    @Override
    public BillCheckInfoDTO findDetailById(String billCheckId) {
        return findById(billCheckId,true);
    }

    //根据id查询(返回运单以外的对账单所有信息)
    private BillCheckInfoDTO findById(String billCheckId,boolean getWaybillInfo) {
        if (CsStringUtils.isBlank(billCheckId)) {
            return null;
        }
        BillCheckInfo billCheckInfo = billCheckInfoBiz.get(billCheckId);
        if( billCheckInfo == null || BooleanUtils.isTrue(billCheckInfo.getDelFlg())){
            throw new BizException(BasicCode.CUSTOM_ERROR,"对账单不存在或已被删除");
        }
        BillCheckInfoDTO dto = new BillCheckInfoDTO();
        BeanUtils.copyProperties(billCheckInfo,dto);
        if (CsStringUtils.isNotBlank(dto.getBillCheckBegin()) && dto.getBillCheckBegin().length() >= 10) {
            dto.setBillCheckBeginDate(dto.getBillCheckBegin().substring(0,10));
        }
        if (CsStringUtils.isNotBlank(dto.getBillCheckEnd()) && dto.getBillCheckEnd().length() >= 10) {
            dto.setBillCheckEndDate(dto.getBillCheckEnd().substring(0,10));
        }
        dto.setApprovalInfoList(billCheckChangeLogBiz.findDTOByBillCheckId(billCheckInfo.getBillCheckId()));
        dto.setGoodsInfoList(billCheckGoodsInfoBiz.findDTOByBillCheckId(billCheckInfo.getBillCheckId()));
        dto.setWaybillInfoList(Lists.newArrayList());
        if(CollectionUtils.isNotEmpty(dto.getGoodsInfoList())){
            Map<String,BillCheckAdjustInfoDTO> map = Maps.newHashMap();
            dto.getGoodsInfoList().stream()
                    .filter(item->BooleanUtils.isTrue(item.getUserAddFlg()))
                    .forEach(item->{
                        BillCheckAdjustInfoDTO adjustInfoDTO = map.get(item.getUserAddType());
                        if( adjustInfoDTO == null ){
                            map.put(item.getUserAddType(),new BillCheckAdjustInfoDTO(item.getUserAddType(),item.getUserAddRemark(),item.getGoodsAmount()));
                        }else{
                            adjustInfoDTO.setValue(ArithUtils.add(item.getGoodsAmount(),adjustInfoDTO.getValue()));
                            map.put(item.getUserAddType(),adjustInfoDTO);
                        }
            });
            dto.setAdjustDetailList(Lists.newArrayList(map.values()));
        }
        if(getWaybillInfo || CostTypeEnum.DRIVER.getCode().equals(billCheckInfo.getCostType())) {
            dto.setWaybillInfoList(billCheckWaybillInfoBiz.findDTOByBillCheckId(billCheckInfo.getBillCheckId()));
            if(CollectionUtils.isNotEmpty(dto.getApprovalInfoList())) {
                //司机确认的问题
                if( BillCheckStatusEnum.COMPLETED.getCode().equals(dto.getBillCheckStatus())){
                    dto.setCheckTime(dto.getApprovalInfoList().get(dto.getApprovalInfoList().size()-1).getCreateTime());
                }
                BillCheckChangeLogDTO changeLogDTO = dto.getApprovalInfoList().stream()
                        .filter(item->"财务".equals(item.getNodeName()) && BooleanUtils.isTrue(item.getStatus()))
                        .findFirst().orElse(null);
                if (changeLogDTO != null ) {
                    //发送给司机确认的时间
                    dto.setSubmitTime(changeLogDTO.getCreateTime());
                }
            }
        }
        return dto;
    }

    @Override
    public List<KeyValueDTO> findDropDownData(KeyValueQueryDTO query) {
        CsStringUtils.checkIsNull(query.getOperatorMemberId(),"operatorMemberId");
        switch (query.getSearchType()){
            case "memberName":{
                CsStringUtils.checkIsNull(query.getBillCheckType(),"billCheckType");
                if(BillCheckTypeEnum.RECEIVABLE.getCode().equals(query.getBillCheckType())) {
                    return billCheckInfoBiz.findPayerMemberIdAndName(query);
                }else{
                    return billCheckInfoBiz.findPayeeMemberIdAndName(query);
                }
            }
            case "billCheckNo":{
                CsStringUtils.checkIsNull(query.getBillCheckType(),"billCheckType");
                return billCheckInfoBiz.findCheckInfoIdAndNo(query);
            }
            case "waybill":{
                CsStringUtils.checkIsNull(query.getBillCheckId(),"billCheckId");
                return billCheckWaybillInfoBiz.findWayBillIdAndNo(query);
            }
            case "car":{
                CsStringUtils.checkIsNull(query.getBillCheckId(),"billCheckId");
                return billCheckWaybillInfoBiz.findCarIdAndName(query);
            }
            case "ship":{
                CsStringUtils.checkIsNull(query.getBillCheckId(),"billCheckId");
                return billCheckWaybillInfoBiz.findShipIdAndName(query);
            }
            case "warehouse":{
                CsStringUtils.checkIsNull(query.getBillCheckId(),"billCheckId");
                return billCheckWaybillInfoBiz.findWarehouseIdAndName(query);
            }
        }
        return Lists.newArrayList();
    }

    @Override
    public void logisticsBillCheckExport(BillCheckExportDTO dto, HttpServletResponse response){
        BillCheckInfoDTO checkInfoDTO = findById(dto.getBillCheckId());
        if (checkInfoDTO == null || (!CsStringUtils.equals(dto.getOperatorMemberId(), checkInfoDTO.getPayeeMemberId()) &&
                !CsStringUtils.equals(dto.getOperatorMemberId(), checkInfoDTO.getPayerMemberId()))) {
            log.info("对账单不存在");
            return;
        }
        BillCheckExcelUtil2.logisticsBillCheckInfoExport(this,checkInfoDTO,response);
    }
}
