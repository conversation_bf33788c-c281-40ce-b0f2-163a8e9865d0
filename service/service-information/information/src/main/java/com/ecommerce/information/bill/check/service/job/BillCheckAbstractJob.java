package com.ecommerce.information.bill.check.service.job;

import com.alibaba.fastjson.JSON;
import com.ecommerce.base.api.service.ISaleRegionService;
import com.ecommerce.common.enums.AppNames;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.filter.LogParameterFilter;
import com.ecommerce.common.service.common.bsid.CommonBusinessIdGenerator;
import com.ecommerce.common.service.common.uuid.UUIDGenerator;
import com.ecommerce.common.service.lock.RedisLockService;
import com.ecommerce.common.utils.ArithUtils;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.common.utils.DateUtil;
import com.ecommerce.goods.api.enums.DeliveryWayEnum;
import com.ecommerce.information.api.constant.InfoNumberConstant;
import com.ecommerce.information.api.dto.bill.check.enums.BillCheckCycleEnum;
import com.ecommerce.information.api.dto.bill.check.enums.BillCheckStatusEnum;
import com.ecommerce.information.api.dto.bill.check.enums.BillCheckTypeEnum;
import com.ecommerce.information.api.dto.bill.check.enums.CostTypeEnum;
import com.ecommerce.information.bill.check.biz.IBillCheckChangeLogBiz;
import com.ecommerce.information.bill.check.biz.IBillCheckGoodsInfoBiz;
import com.ecommerce.information.bill.check.biz.IBillCheckInfoBiz;
import com.ecommerce.information.bill.check.biz.IBillCheckRuleBiz;
import com.ecommerce.information.bill.check.biz.IBillCheckWaybillBaseDataBiz;
import com.ecommerce.information.bill.check.biz.IBillCheckWaybillInfoBiz;
import com.ecommerce.information.bill.check.biz.impl.BillCheckInfoBiz;
import com.ecommerce.information.bill.check.biz.impl.BillCheckRuleBiz;
import com.ecommerce.information.bill.check.dao.vo.BillCheckChangeLog;
import com.ecommerce.information.bill.check.dao.vo.BillCheckInfo;
import com.ecommerce.information.bill.check.dao.vo.BillCheckRule;
import com.ecommerce.information.bill.check.dao.vo.BillCheckWaybillBaseData;
import com.ecommerce.information.bill.check.dao.vo.BillCheckWaybillInfo;
import com.ecommerce.logistics.api.enums.PickingBillTypeEnum;
import com.ecommerce.logistics.api.enums.TransportToolTypeEnum;
import com.ecommerce.member.api.dto.account.AccountSimpleDTO;
import com.ecommerce.member.api.dto.member.enums.MemberPlatform;
import com.ecommerce.member.api.dto.relation.MemberRelationDTO;
import com.ecommerce.member.api.dto.relation.MemberRelationQueryDTO;
import com.ecommerce.member.api.service.IAccountService;
import com.ecommerce.member.api.service.IMemberRelationService;
import com.github.pagehelper.Page;
import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.IJobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 对账单job抽象类
 */
@Slf4j
public abstract class BillCheckAbstractJob extends IJobHandler {

    @Autowired
    protected IBillCheckInfoBiz billCheckInfoBiz;
    @Autowired
    protected IBillCheckGoodsInfoBiz billCheckGoodsInfoBiz;
    @Autowired
    protected IBillCheckWaybillInfoBiz billCheckWaybillInfoBiz;
    @Autowired
    protected IBillCheckChangeLogBiz billCheckChangeLogBiz;
    @Autowired
    protected IBillCheckRuleBiz billCheckRuleBiz;
    @Autowired
    protected UUIDGenerator uuidGenerator;
    @Autowired
    protected IAccountService accountService;
    @Autowired
    private IMemberRelationService memberRelationService;
    @Autowired
    protected IMemberRelationService relationService;
    @Autowired
    protected ISaleRegionService saleRegionService;
    @Autowired
    protected CommonBusinessIdGenerator businessIdGenerator;
    @Autowired
    protected RedisLockService redisLockService;
    @Autowired
    protected IBillCheckWaybillBaseDataBiz billCheckWaybillBaseDataBiz;


    protected static final String LOCK_PREFIX = "bill_check_lock:";
    protected static final String DATE_FORMAT_PATTERN = "yyyy-MM-dd HH:mm:ss";

    protected abstract String getJobName();
    @Transactional
    @Override
    // TODO: 重构此方法以降低认知复杂度 (当前: 32, 目标: ≤15)
    // 建议: 1) 提取子方法 2) 使用早期返回 3) 简化条件表达式
    public void execute() throws Exception {
        String param = XxlJobHelper.getJobParam();
        String old = Thread.currentThread().getName();
        //指定线程数并行执行
        try {
            //设置线程名称 保证每次job执行线程名不一样
            String jobMainThreadName = uuidGenerator.gain();
            Thread.currentThread().setName(jobMainThreadName);
            //设置日志标记字段
            String jobName = getJobName();
            MDC.put(LogParameterFilter.USER_NAME, jobName);
            //当前时间
            LocalDateTime now = LocalDateTime.now();
            int month = now.getMonthValue();
            int day = now.getDayOfMonth();
            long nowTime = System.currentTimeMillis();
            //规则表查询条件
            Map<String, String> paramMap = CsStringUtils.isBlank(param) ? Maps.newHashMap() : JSON.parseObject(param, Map.class);
            Condition condition = new Condition(BillCheckRule.class);
            Example.Criteria criteria = condition.createCriteria();
            if (MapUtils.isNotEmpty(paramMap)) {
                log.info("use param : {}", param);
                paramMap.entrySet().forEach(item -> {
                    criteria.andEqualTo(item.getKey(), item.getValue());
                });
            } else {
                //应收应付
                criteria.andEqualTo("billCheckType", getBillCheckType().getCode())
                        //费用类型
                        .andEqualTo("costType", getCostType().getCode())
                        //自动创建
                        .andEqualTo("autoCreate", true);

            }

            int i = 1;
            int ruleIdCount = 1;
            //子线程并发线程数
            Set<Thread> threadSet = Sets.newHashSet();
            while (true) {
                Page<BillCheckRule> ruleList = PageMethod.startPage(i++, 500, false, false, false)
                        .doSelectPage(() -> billCheckRuleBiz.findByCondition(condition));
                if (CollectionUtils.isEmpty(ruleList)) {
                    log.info("ruleList is empty.");
                    break;
                }
                for (BillCheckRule checkRule : ruleList) {
                    //非账单生成日则忽略
                    if (!billCheckRuleBiz.isCreateDay(checkRule, month, day)) {
                        log.info("非账单生成日则忽略job触发,now: {}-{}-{},ruleId: {},cycle: {},checkDay: {},createDay: {}",
                                now.getYear(), month, day, checkRule.getBillCheckRuleId(), checkRule.getCheckCycle(), checkRule.getCheckDay(), checkRule.getCreateDay());
                        continue;
                    }
                    //获取线程信号量 ，如果当前有5个线程正在执行，则等待
//                    semaphore.acquire();//InterruptedException 导致redisLock失败
                    if (threadSet.size() >= 5) {
                        //等待子线程处理
                        while (threadSet.size() >= 5) {
                            try {
                                Thread.sleep(300);
                            } catch (InterruptedException e) {
                                log.error("Thread sleep exception:",e);
                                Thread.currentThread().interrupt();
                            }
                        }
                    }
                    int totalThreadCount = ruleIdCount++;
                    Thread thread = new Thread(() -> {
                        try {
                            //子线程日志标记
                            MDC.put(LogParameterFilter.USER_NAME, jobName + "-" + jobMainThreadName + "-" + checkRule.getBillCheckRuleId() + "-" + totalThreadCount);

                            checkRule.setInvoiceType(checkRule.getInvoiceType() == null ? InfoNumberConstant.ONE_INTEGER : checkRule.getInvoiceType());

                            log.info("billCheck job begin ruleId: {}", checkRule.getBillCheckRuleId());

                            billCheckTimeWindowCheck(checkRule, MemberPlatform.SYSTEM_OPERATOR.getId());
                            long s = System.currentTimeMillis();
                            execute(checkRule, null, MemberPlatform.SYSTEM_OPERATOR.getId(), false, now);
                            log.info("billCheck job end ruleId: {} cost time {} ms", checkRule.getBillCheckRuleId(), System.currentTimeMillis() - s);
                        } catch (Throwable e) {
                            log.error("对账单生成失败 checkRule: {}", JSON.toJSONString(checkRule));
                            log.error(e.getMessage(), e);
                        } finally {
                            //释放信号
                            threadSet.remove(Thread.currentThread());
                            log.info("threadSet remove currThread,size: {} ", threadSet.size());
                        }
                    });
                    threadSet.add(thread);
                    log.info("threadSet add thread size: {} ", threadSet.size());
                    thread.start();
                }
            }
            log.info("job execute finished cost time: {} ms.", System.currentTimeMillis() - nowTime);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            Thread.currentThread().interrupt();
        } finally {
            Thread.currentThread().setName(old);
        }
    }

    public abstract BillCheckTypeEnum getBillCheckType();

    public abstract CostTypeEnum getCostType();

    @Transactional
    public void execute(BillCheckRule rule, String billCheckId, String operator, boolean retry, LocalDateTime now) {
        String identifier = null;
        try {
            log.info("execute ruleId:{},billCheckId:{},retry:{}", rule.getBillCheckRuleId(), billCheckId, retry);
            //同一时间一个规则只能执行一个
            if (MemberPlatform.SYSTEM_OPERATOR.getId().equals(operator)) {
                identifier = redisLockService.lock(LOCK_PREFIX + rule.getBillCheckRuleId());
            } else {
                //如果是手工操作，则快速失败
                identifier = redisLockService.lockFast(LOCK_PREFIX + rule.getBillCheckRuleId());
            }
            Date date = new Date();
            BillCheckInfo billCheckInfo = new BillCheckInfo();
            if (retry) {
                //如果是重新创建，保留单号，则先删除 再创建
                BillCheckInfo old = billCheckInfoBiz.get(billCheckId);
                billCheckInfo.setBillCheckId(old.getBillCheckId());
                billCheckInfo.setBillCheckNo(old.getBillCheckNo());
                billCheckInfo.setBillCheckBegin(old.getBillCheckBegin());
                billCheckInfo.setBillCheckEnd(old.getBillCheckEnd());
                billCheckInfo.setBillCheckDate(old.getBillCheckDate());

                String delId = uuidGenerator.gain();
                billCheckInfoBiz.deleteByBillCheckId(billCheckId, delId, operator);
                billCheckGoodsInfoBiz.deleteByBillCheckId(billCheckId, delId, operator);
                billCheckWaybillInfoBiz.deleteByBillCheckId(billCheckId, delId, operator);
                log.info("手工重新生成，根据对账单id: {} 删除已有数据.", billCheckId);
            } else {
                billCheckInfo.setBillCheckNo(businessIdGenerator.incrementCode(BillCheckInfoBiz.DZD));
                List<BillCheckInfo> existsList = billCheckInfoBiz.findByRule(rule.getCostType(), getBillCheckDate(rule, now), rule.getPayeeMemberId(), rule.getPayerMemberId());
                if (CollectionUtils.isNotEmpty(existsList)) {
                    for (BillCheckInfo exists : existsList) {
                        String delId = uuidGenerator.gain();
                        billCheckInfoBiz.deleteByBillCheckId(exists.getBillCheckId(), delId, operator);
                        billCheckGoodsInfoBiz.deleteByBillCheckId(exists.getBillCheckId(), delId, operator);
                        billCheckWaybillInfoBiz.deleteByBillCheckId(exists.getBillCheckId(), delId, operator);
                        log.info("系统重新生成，根据对账单id: {} no:{} 删除已有数据.", exists.getBillCheckId(), exists.getBillCheckNo());
                    }
                }
                billCheckInfo.setBillCheckId(uuidGenerator.gain());
            }
            //如果是卖家发起的2票物流对账，需要再次校验是否2票制
            //卖家发起
            if (CsStringUtils.equals(AppNames.WEB_SERVICE_SELLER.getPlatform(), rule.getMemberRole()) &&
                    //2票
                    (rule.getInvoiceType() != null && rule.getInvoiceType() == 2) &&
                    //物流款
                    (CostTypeEnum.LOGISTICS_ROAD.getCode().equals(rule.getCostType()) || CostTypeEnum.LOGISTICS_SHIP.getCode().equals(rule.getCostType()))
            ) {
                MemberRelationQueryDTO memberRelationQueryDTO = new MemberRelationQueryDTO();
                memberRelationQueryDTO.setMemberId(rule.getPayeeMemberId());
                memberRelationQueryDTO.setCustomerId(rule.getPayerMemberId());
                memberRelationQueryDTO.setInvoiceType(2);
                List<MemberRelationDTO> relationMemberList = memberRelationService.findRelationMemberList(memberRelationQueryDTO);
                if (CollectionUtils.isEmpty(relationMemberList)) {
                    throw new BizException(BasicCode.CUSTOM_ERROR, "一票制客户不单独进行物流对账");
                }
            }
            billCheckId = billCheckInfo.getBillCheckId();
            billCheckInfo.setBillCheckRuleId(rule.getBillCheckRuleId());
            billCheckInfo.setBillCheckType(rule.getBillCheckType());
            billCheckInfo.setCheckCycle(rule.getCheckCycle());
            setBillCheckDate(billCheckInfo, rule, now, retry);
            billCheckInfo.setCostType(rule.getCostType());
            billCheckInfo.setMemberRole(rule.getMemberRole());

            billCheckInfo.setPayeeMemberId(rule.getPayeeMemberId());
            billCheckInfo.setPayeeMemberName(rule.getPayeeMemberName());
            billCheckInfo.setPayerMemberId(rule.getPayerMemberId());
            billCheckInfo.setPayerMemberName(rule.getPayerMemberName());
            billCheckInfo.setBillCheckQuantity(BigDecimal.ZERO);
            billCheckInfo.setBillCheckAmount(BigDecimal.ZERO);
            billCheckInfo.setBillCheckAmountAdjusted(BigDecimal.ZERO);

            billCheckInfo.setDelFlg(false);
            billCheckInfo.setCreateTime(date);
            billCheckInfo.setCreateUser(operator);
            billCheckInfo.setUpdateTime(date);
            billCheckInfo.setUpdateUser(operator);
            billCheckInfo.setBillCheckStatus(BillCheckStatusEnum.DRAFT.getCode());

            log.info("{} execute prepare billCheckInfo:{},rule:{}", billCheckId, billCheckInfo.getBillCheckId(), rule);

            long s = System.currentTimeMillis();
            if (!billCheckInfoBiz.exists(billCheckInfo.getBillCheckId())) {
                billCheckInfoBiz.getMapper().insert(billCheckInfo);
            } else {
                billCheckInfoBiz.getMapper().updateByPrimaryKey(billCheckInfo);
            }
            log.info("{} saveBillCheckInfo cost time: {} ms", billCheckId, (System.currentTimeMillis() - s));

            //可能有很多，所以要先存着
            s = System.currentTimeMillis();
            int count = saveBillCheckWaybillInfoList(billCheckInfo, rule);
            log.info("{} saveBillCheckWaybillInfoList count: {}，cost time: {} ms", billCheckId, count, (System.currentTimeMillis() - s));
            if (count == 0) {
                billCheckInfoBiz.updateRemark(billCheckInfo.getBillCheckId(), "无数据");
            }

            s = System.currentTimeMillis();
            count = saveBillCheckGoodsInfo(billCheckInfo, rule);
            billCheckInfoBiz.updateQuantityAndAmount(billCheckInfo.getBillCheckId(), billCheckInfo.getBillCheckQuantity(), billCheckInfo.getBillCheckAmount(), billCheckInfo.getShowCustomer());
            log.info("{} saveBillCheckGoodsInfo count: {},cost time: {} ms", billCheckId, count, (System.currentTimeMillis() - s));

            String remark = retry ? "重新创建对账单" : (CsStringUtils.equals(operator, MemberPlatform.SYSTEM_OPERATOR.getId()) ? "系统自动创建对账单" : "手动创建对账单");
            if (count == 0) {
                remark += "(没有数据)";
            }
            BillCheckChangeLog billCheckChangeLog = new BillCheckChangeLog();
            billCheckChangeLog.setNodeName(retry ? "财务" : "系统");
            billCheckChangeLog.setCreateUser(operator);
            billCheckChangeLog.setCreateUserName(getOperatorName(operator));
            billCheckChangeLog.setCreateTime(date);
            billCheckChangeLog.setUpdateUser(operator);
            billCheckChangeLog.setUpdateTime(date);
            billCheckChangeLog.setStatus(true);
            billCheckChangeLog.setRemark(remark);
            billCheckChangeLog.setBillCheckId(billCheckInfo.getBillCheckId());
            billCheckChangeLog.setBillCheckChangeLogId(uuidGenerator.gain());
            billCheckChangeLog.setDelFlg(false);
            billCheckChangeLogBiz.getMapper().insert(billCheckChangeLog);
        } finally {
            redisLockService.unlock(LOCK_PREFIX + rule.getBillCheckRuleId(), identifier);
        }
    }


    public int saveBillCheckWaybillInfoList(BillCheckInfo billCheckInfo, BillCheckRule rule) {
        long s = System.currentTimeMillis();
        //如果是卖家发起的商品款或者物流款，才需要区分票制
        Integer invoiceType = null;
        if (AppNames.WEB_SERVICE_SELLER.getPlatform().equals(rule.getMemberRole()) &&
                (CostTypeEnum.GOODS.getCode().equals(rule.getCostType()) || CostTypeEnum.CONCRETE.getCode().equals(rule.getCostType()))) {
            invoiceType = relationService.getInvoiceType(billCheckInfo.getPayeeMemberId(), billCheckInfo.getPayerMemberId());
            invoiceType = invoiceType == null ? InfoNumberConstant.ONE_INTEGER : invoiceType;
        }
        log.info("{} invoiceType:{},memberId:{},customerId:{} cost time:{} ms",
                billCheckInfo.getBillCheckId(), invoiceType, billCheckInfo.getPayeeMemberId(), billCheckInfo.getPayerMemberId(), System.currentTimeMillis() - s);

        Condition condition = new Condition(BillCheckWaybillBaseData.class);
        Example.Criteria criteria = condition.createCriteria();
        if (CostTypeEnum.GOODS.getCode().equals(rule.getCostType()) || CostTypeEnum.CONCRETE.getCode().equals(rule.getCostType())) {
            criteria.andEqualTo("buyerId", rule.getPayerMemberId())
                    .andEqualTo("sellerId", rule.getPayeeMemberId())
                    .andIn("consignorId", Lists.newArrayList(rule.getPayerMemberId(), rule.getPayeeMemberId(), MemberPlatform.PLATFORM_MEMBERID.getId()));
        } else {
            //收款方肯定是承运方
            criteria.andEqualTo("carrierId", rule.getPayeeMemberId());
            //委托方如果订单是平台配送，则委托方是买家，其它情况委托方是付款方
            criteria.andCondition(" (consignor_id = '" + rule.getPayerMemberId() + "' or (seller_id = '" + rule.getPayerMemberId() + "' and consignor_id=buyer_id and deliver_way='" + DeliveryWayEnum.PLATFORM_DELIVERY.getCode() + "'))");
        }
        criteria.andGreaterThanOrEqualTo("leaveWarehouseTime", billCheckInfo.getBillCheckBegin())
                .andLessThanOrEqualTo("leaveWarehouseTime", billCheckInfo.getBillCheckEnd());
        if (CostTypeEnum.LOGISTICS_ROAD.getCode().equals(rule.getCostType())) {
            criteria.andEqualTo("transportType", TransportToolTypeEnum.ROAD_TRANSPORT.getCode());
        }
        if (CostTypeEnum.LOGISTICS_SHIP.getCode().equals(rule.getCostType())) {
            criteria.andEqualTo("transportType", TransportToolTypeEnum.WATER_TRANSPORT.getCode());
        }
        //参考平台商品树查询 /platformApi/category/anon/findCategoryTree
        if (CostTypeEnum.GOODS.getCode().equals(rule.getCostType())) {
            criteria.andLike("goodsCategoryCode", "002001001%");
        }
        if (CostTypeEnum.CONCRETE.getCode().equals(rule.getCostType())) {
            criteria.andLike("goodsCategoryCode", "002001003%");
        }
        criteria.andEqualTo("delFlg", false);
        if (CostTypeEnum.GOODS.getCode().equals(rule.getCostType()) || CostTypeEnum.CONCRETE.getCode().equals(rule.getCostType())) {
            //按委托单排序，同一个运单取第一个
            condition.setOrderByClause("delivery_bill_num asc,complete_time asc");
        } else {
            condition.orderBy("completeTime");
        }
        log.info("billCheckId: {} ruleId: {} queryBillCheckWaybillInfoList payerId: {},payeeId: {},completeTimeBegin: {},completeTimeEnd: {}",
                billCheckInfo.getBillCheckId(), rule.getBillCheckRuleId(), billCheckInfo.getPayerMemberId(), billCheckInfo.getPayeeMemberId(), billCheckInfo.getBillCheckBegin(), billCheckInfo.getBillCheckEnd());

        List<BillCheckWaybillInfo> billCheckWaybillInfoList = Lists.newArrayList();
        int totalCount = 0;
        int pageNumber = 1;
        boolean hasNextPage = true;
        Set<String> filetSet = Sets.newHashSet();
        while (hasNextPage) {
            //1. 翻页查询 根据条件查询运单明细: 运单完成时间段(开始时间  结束时间) 买家 卖家 运输方式、商品类型( or非)
            s = System.currentTimeMillis();
            log.info("{} billCheckWaybillBaseDataBiz.findByCondition query: {}", billCheckInfo.getBillCheckId(), JSON.toJSONString(condition.getOredCriteria()));
            Page<BillCheckWaybillBaseData> list =PageMethod.startPage(pageNumber++, 300, false, false, false)
                    .doSelectPage(() -> billCheckWaybillBaseDataBiz.findByCondition(condition));
            if (CollectionUtils.isEmpty(list)) {
                log.info("{} billCheckWaybillBaseDataBiz.findByCondition {} cost time: {} ms", billCheckInfo.getBillCheckId(), pageNumber > 2 ? "运单明细查询结束" : "运单明细查询结果为空", System.currentTimeMillis() - s);
                break;
            }
            hasNextPage = CollectionUtils.size(list) == 300;
            log.info("{} queryBillCheckWaybillInfoList.size: {},hasNextPage: {},waybillNum: {},deliveryBillNum: {} cost time: {} ms",
                    billCheckInfo.getBillCheckId(),
                    list.size(), hasNextPage,
                    list.stream().map(BillCheckWaybillBaseData::getWaybillNum).collect(Collectors.joining(",")),
                    list.stream().map(BillCheckWaybillBaseData::getDeliverWay).distinct().collect(Collectors.joining(",")),
                    System.currentTimeMillis() - s
            );
            for (BillCheckWaybillBaseData item : list) {
                if (CostTypeEnum.GOODS.getCode().equals(rule.getCostType()) || CostTypeEnum.CONCRETE.getCode().equals(rule.getCostType())) {
                    if (filetSet.contains(item.getWaybillNum())) {
                        continue;//商品款运单不能重复
                    }
                    filetSet.add(item.getWaybillNum());
                }
                if (CsStringUtils.isBlank(item.getTakeCode())) {
                    log.info("{} takeInfoDTO is null takeCode: {},waybillNum: {}", billCheckInfo.getBillCheckId(), item.getTakeCode(), item.getWaybillNum());
                    continue;
                }
                if (CsStringUtils.isBlank(item.getOrderCode())) {
                    log.info("{} orderCode is null takeCode: {},waybillNum: {}", billCheckInfo.getBillCheckId(), item.getTakeCode(), item.getWaybillNum());
                    continue;
                }

                BillCheckWaybillInfo waybillInfo = new BillCheckWaybillInfo();
                waybillInfo.setBillCheckWaybillInfo(uuidGenerator.gain());
                waybillInfo.setBillCheckId(billCheckInfo.getBillCheckId());
                waybillInfo.setBillCheckNo(billCheckInfo.getBillCheckNo());
                waybillInfo.setMemberRole(rule.getMemberRole());
                waybillInfo.setCostType(billCheckInfo.getCostType());
                waybillInfo.setOrderCode(item.getOrderCode());
                waybillInfo.setTakeCode(item.getTakeCode());
                waybillInfo.setDeliveryBillNum(item.getDeliveryBillNum());
                waybillInfo.setWaybillNum(item.getWaybillNum());
                waybillInfo.setTransportType(item.getTransportType());
                waybillInfo.setInvoiceType(invoiceType);
                waybillInfo.setBuyerId(item.getBuyerId());
                waybillInfo.setBuyerName(item.getBuyerName());
                waybillInfo.setSellerId(item.getSellerId());
                waybillInfo.setSellerName(item.getSellerName());
                waybillInfo.setConsignorId(item.getConsignorId());
                waybillInfo.setConsignorName(item.getConsignorName());
                waybillInfo.setCarrierId(item.getCarrierId());
                waybillInfo.setCarrierName(item.getCarrierName());
                waybillInfo.setGoodsId(item.getGoodsId());
                waybillInfo.setGoodsName(item.getGoodsName());
                waybillInfo.setTransportCategoryId(item.getTransportCategoryId());
                waybillInfo.setTransportCategoryName(item.getTransportCategoryName());
                //来自订单信息
                waybillInfo.setSaleRegionId(item.getSaleRegionId());
                waybillInfo.setSaleRegionName(item.getSaleRegionName());
                waybillInfo.setVehicleNum(item.getVehicleNum());
                waybillInfo.setVehiclePayload(item.getVehiclePayload());
                waybillInfo.setDeliverWay(item.getDeliverWay());
                waybillInfo.setWaybillDeliverWay(item.getWaybillDeliverWay());
                waybillInfo.setPayerMemberId(billCheckInfo.getPayerMemberId());
                waybillInfo.setPayerMemberName(billCheckInfo.getPayerMemberName());
                waybillInfo.setPayeeMemberId(billCheckInfo.getPayeeMemberId());
                waybillInfo.setPayeeMemberName(billCheckInfo.getPayeeMemberName());
                waybillInfo.setWarehouseId(item.getWarehouseId());
                waybillInfo.setWarehouseName(item.getWarehouseName());
                waybillInfo.setWarehouseAddress(item.getWarehouseAddress());
                waybillInfo.setReceiveAddressId(item.getReceiveAddressId());
                waybillInfo.setReceiveAddressName(item.getReceiveAddressName());
                waybillInfo.setReceiveAddress(item.getReceiveAddress());
                waybillInfo.setPickingWharfId(item.getPickingWharfId());
                waybillInfo.setPickingWharfName(item.getPickingWharfName());
                waybillInfo.setPickingWharfAddress(item.getPickingWharfAddress());
                waybillInfo.setReceivingWharfId(item.getReceivingWharfId());
                waybillInfo.setReceivingWharfName(item.getReceivingWharfName());
                waybillInfo.setReceivingWharfAddress(item.getReceivingWharfAddress());
                waybillInfo.setUnloadPortId(item.getUnloadPortId());
                waybillInfo.setUnloadPortName(item.getUnloadPortName());
                waybillInfo.setUnitGoodsPrice(item.getUnitGoodsPrice());
                if (waybillInfo.getUnitGoodsPrice() == null || waybillInfo.getUnitGoodsPrice().compareTo(BigDecimal.ZERO) < 0) {
                    waybillInfo.setUnitGoodsPrice(BigDecimal.ZERO);
                    log.info("{} unitGoodsPrice is null waybillNum: {}", billCheckInfo.getBillCheckId(), waybillInfo.getWaybillNum());
                }
                waybillInfo.setUnitLogisticPrice(item.getUnitLogisticPrice());
                if (waybillInfo.getUnitLogisticPrice() == null || waybillInfo.getUnitLogisticPrice().compareTo(BigDecimal.ZERO) < 0) {
                    waybillInfo.setUnitLogisticPrice(BigDecimal.ZERO);
                    log.info("{} unitLogisticPrice is null waybillNum: {}", billCheckInfo.getBillCheckId(), waybillInfo.getWaybillNum());
                }
                waybillInfo.setSendQuantity(item.getSendQuantity());
                //为空则为默认值
                item.setNewestGoodsPrice(item.getNewestGoodsPrice() == null ? item.getUnitGoodsPrice() : item.getNewestGoodsPrice());
                item.setNewestLogisticPrice(item.getNewestGoodsPrice() == null ? item.getUnitLogisticPrice() : item.getNewestLogisticPrice());

                //初始价格
                BigDecimal price = BigDecimal.ZERO;
                //最新价格
                BigDecimal priceNew = BigDecimal.ZERO;
                if (CostTypeEnum.GOODS.getCode().equals(rule.getCostType())) {
                    // 货款对账单汇总表和运单明细页面的商品单价展示不按票制区分，都展示货款+物流款的金额
                    price = ArithUtils.add(item.getUnitGoodsPrice(), item.getUnitLogisticPrice());
                    priceNew = ArithUtils.add(item.getNewestGoodsPrice(), item.getNewestLogisticPrice());
                    waybillInfo.setUnitGoodsPrice(price);
                    if (PickingBillTypeEnum.PLATFORM_DELIVERY.getCode().equals(item.getDeliverWay())) {
                        price = item.getUnitGoodsPrice();
                        priceNew = item.getNewestGoodsPrice();
                        waybillInfo.setUnitGoodsPrice(price);
                        waybillInfo.setNewestGoodsPrice(priceNew);
                        log.info("order: {} 为平台配送,  price = unitGoodsPrice: {}", price);
                    }
                } else if (CostTypeEnum.CONCRETE.getCode().equals(rule.getCostType())) {
                    price = ArithUtils.add(item.getUnitGoodsPrice(), item.getUnitLogisticPrice());
                    priceNew = ArithUtils.add(item.getNewestGoodsPrice(), item.getNewestLogisticPrice());
                    waybillInfo.setUnitGoodsPrice(price);
                    waybillInfo.setNewestGoodsPrice(priceNew);
                } else if (CostTypeEnum.LOGISTICS_SHIP.getCode().equals(rule.getCostType()) ||
                        CostTypeEnum.LOGISTICS_ROAD.getCode().equals(rule.getCostType()) ||
                        CostTypeEnum.DRIVER.getCode().equals(rule.getCostType())) {
                    price = item.getUnitLogisticPrice();
                    priceNew = item.getNewestLogisticPrice();
                }
                waybillInfo.setAmount(ArithUtils.multiply(item.getSendQuantity(), price));
                waybillInfo.setAdjustAmount(ArithUtils.multiply(item.getSendQuantity(), priceNew));
                if (price.compareTo(BigDecimal.ZERO) <= 0) {
                    log.info("忽略无费用的数据 {} waybillNum: {},invoiceType: {},costType:{},amount: {} = quantity: {} * price: {},goodsPrice:{},logisticsPrice:{}",
                            billCheckInfo.getBillCheckId(), item.getWaybillNum(), invoiceType, rule.getCostType(), waybillInfo.getAmount(), waybillInfo.getSendQuantity(), price, item.getUnitGoodsPrice(), item.getUnitLogisticPrice());
                    continue;
                } else {
                    log.info("{} waybillNum: {},invoiceType: {},costType:{},amount: {} = quantity: {} * price: {},goodsPrice:{},logisticsPrice:{}",
                            billCheckInfo.getBillCheckId(), item.getWaybillNum(), invoiceType, rule.getCostType(), waybillInfo.getAmount(), waybillInfo.getSendQuantity(), price, item.getUnitGoodsPrice(), item.getUnitLogisticPrice());
                }

                waybillInfo.setLeaveWarehouseTime(item.getLeaveWarehouseTime());
                waybillInfo.setCompleteTime(item.getCompleteTime());
                waybillInfo.setDistance(item.getDistance());
                waybillInfo.setContractId(item.getContractId());
                waybillInfo.setContractId(item.getContractId());
                waybillInfo.setContractNumber(item.getContractNumber());
                waybillInfo.setContractProjectName(item.getContractProjectName());
                waybillInfo.setContractAddressName(item.getContractAddressName());
                //如果是货款
                if (CostTypeEnum.CONCRETE.getCode().equals(rule.getCostType())) {

                    waybillInfo.setStrengthLevel(item.getGoodsName());
                    waybillInfo.setSigner(item.getSigner());
                    waybillInfo.setSignerPhone(item.getSignerPhone());
                    waybillInfo.setSignQuantity(item.getSignQuantity());
                    waybillInfo.setSignTime(item.getSignTime());
                    waybillInfo.setAddItemPrice(item.getAddItemPrice());
                    if (waybillInfo.getAddItemPrice() == null || waybillInfo.getAddItemPrice().compareTo(BigDecimal.ZERO) < 0) {
                        waybillInfo.setAddItemPrice(BigDecimal.ZERO);
                        log.info("{} addItemPrice is null waybillNum: {}", billCheckInfo.getBillCheckId(), waybillInfo.getWaybillNum());
                    }
                    waybillInfo.setAddItemAmount(waybillInfo.getAmount());
                    waybillInfo.setAddItemSlump(item.getAddItemSlump());
                    //运单没有，订单才有 当前解决方案：算到其中一个运单上
                    waybillInfo.setFloorTruckage(null);
                    waybillInfo.setEmptyLoadFee(item.getEmptyLoadFee());
                }
                waybillInfo.setTareWeight(item.getTareWeight());
                waybillInfo.setGrossWeight(item.getGrossWeight());
                waybillInfo.setReturnReason(item.getReturnReason());
                waybillInfo.setDelFlg(billCheckInfo.getDelFlg());
                waybillInfo.setCreateUser(billCheckInfo.getCreateUser());
                waybillInfo.setCreateTime(billCheckInfo.getCreateTime());
                waybillInfo.setUpdateTime(billCheckInfo.getUpdateTime());
                waybillInfo.setUpdateUser(billCheckInfo.getUpdateUser());
                billCheckWaybillInfoList.add(waybillInfo);
                totalCount++;
                //汇总到对账单
                billCheckInfo.setBillCheckQuantity(ArithUtils.add(billCheckInfo.getBillCheckQuantity(), waybillInfo.getSendQuantity()));
                billCheckInfo.setBillCheckAmount(ArithUtils.add(billCheckInfo.getBillCheckAmount(), waybillInfo.getAmount()));
                billCheckInfo.setBillCheckAmountAdjusted(billCheckInfo.getBillCheckAmount());
                if (billCheckWaybillInfoList.size() >= 100) {
                    billCheckWaybillInfoBiz.getMapper().insertList(billCheckWaybillInfoList);
                    billCheckWaybillInfoList.clear();
                }
            }
        }
        if (!billCheckWaybillInfoList.isEmpty()) {
            billCheckWaybillInfoBiz.getMapper().insertList(billCheckWaybillInfoList);
        }
        return totalCount;
    }

    /**
     * 保存对账单汇总数据
     */
    public abstract int saveBillCheckGoodsInfo(BillCheckInfo billCheckInfo, BillCheckRule rule);

    protected void setBillCheckDate(BillCheckInfo billCheckInfo, BillCheckRule billCheckRule, LocalDateTime now, boolean retry) {
        if (retry && CsStringUtils.isNotBlank(billCheckInfo.getBillCheckBegin())) {
            return;
        }
        if (BillCheckCycleEnum.CUSTOM.getCode().equals(billCheckRule.getCheckCycle())) {
            billCheckInfo.setBillCheckBegin(billCheckRule.getCheckDateBegin() + " 00:00:00");
            billCheckInfo.setBillCheckEnd(billCheckRule.getCheckDateEnd() + " 23:59:59");
            billCheckInfo.setBillCheckDate(DateUtil.getCurrentDateStr("yyyy-MM-dd"));
            return;
        }
        Map<String, String> billCheckDateInfo = billCheckRuleBiz.getBillCheckDateInfo(billCheckRule, now);
        billCheckInfo.setBillCheckBegin(billCheckDateInfo.get(BillCheckRuleBiz.BILL_CHECK_DATE_BEGIN));
        billCheckInfo.setBillCheckEnd(billCheckDateInfo.get(BillCheckRuleBiz.BILL_CHECK_DATE_END));
        billCheckInfo.setBillCheckDate(billCheckDateInfo.get(BillCheckRuleBiz.BILL_CHECK_DATE));
    }

    protected String getBillCheckDate(BillCheckRule billCheckRule, LocalDateTime now) {
        if (BillCheckCycleEnum.CUSTOM.getCode().equals(billCheckRule.getCheckCycle())) {
            return DateUtil.getCurrentDateStr("yyyy-MM-dd");
        }
        return billCheckRuleBiz.getBillCheckDateInfo(billCheckRule, now).get(BillCheckRuleBiz.BILL_CHECK_DATE);
    }

    private ThreadPoolTaskExecutor getExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //核心线程池大小
        executor.setCorePoolSize(5);
        //最大线程数
        executor.setMaxPoolSize(5);
        //队列容量
        executor.setQueueCapacity(100);
        //活跃时间
        executor.setKeepAliveSeconds(60);
        //线程名字前缀
        executor.setThreadNamePrefix("billCheckJob-");
        executor.initialize();
        return executor;
    }

    protected String getOperatorName(String operator) {
        if (CsStringUtils.isBlank(operator)) {
            return null;
        }
        if (MemberPlatform.SYSTEM_OPERATOR.getId().equals(operator)) {
            return operator;
        }
        AccountSimpleDTO accountSimpleDTO = accountService.findSimpleById(operator);
        if (accountSimpleDTO == null) {
            log.info("accountService.findSimpleById({}) is null.", operator);
            return null;
        }
        return accountSimpleDTO.getAccountName();
    }

    /**
     * 对账单时间区间重叠校验,校验当前要生成的对账单和已完成的对账单
     */
    protected void billCheckTimeWindowCheck(BillCheckRule rule, String operator) {
        if (CsStringUtils.isBlank(rule.getCheckDateBegin())) {
            Map<String, String> billCheckDateInfo = billCheckRuleBiz.getBillCheckDateInfo(rule, LocalDateTime.now());
            rule.setCheckDateBegin(billCheckDateInfo.get(BillCheckRuleBiz.BILL_CHECK_DATE_BEGIN).substring(0, 10));
            rule.setCheckDateEnd(billCheckDateInfo.get(BillCheckRuleBiz.BILL_CHECK_DATE_END).substring(0, 10));
        }
        String begin = rule.getCheckDateBegin() + " 00:00:00";
        String end = rule.getCheckDateEnd() + " 23:59:59";

        List<String> status = Lists.newArrayList(
                BillCheckStatusEnum.WAIT_BUSINESS_CONFIRM.getCode(),
                BillCheckStatusEnum.WAIT_CUSTOMER_CONFIRM.getCode(),
                BillCheckStatusEnum.COMPLETED.getCode());

        Condition condition = new Condition(BillCheckInfo.class);
        condition.createCriteria().andEqualTo("delFlg", false)
                .andEqualTo("billCheckType", rule.getBillCheckType())
                .andEqualTo("costType", rule.getCostType())
                .andEqualTo("payeeMemberId", rule.getPayeeMemberId())
                .andEqualTo("payerMemberId", rule.getPayerMemberId())
                .andCondition(" ( (bill_check_begin between '" + begin + "' and '" + end + "') or (bill_check_end between '" + begin + "' and '" + end + "'))");

        List<BillCheckInfo> list = billCheckInfoBiz.findByCondition(condition);
        if (CollectionUtils.isNotEmpty(list)) {
            log.info("billCheckTimeWindowCheck list.size: {}", list.size());
            if (list.stream().anyMatch(item -> status.contains(item.getBillCheckStatus()))) {
                throw new BizException(BasicCode.CUSTOM_ERROR, "与对账单账期有重叠:" + list.stream().map(BillCheckInfo::getBillCheckNo).collect(Collectors.joining(",")));
            } else {
                for (BillCheckInfo billCheckInfo : list) {
                    String delId = uuidGenerator.gain();
                    billCheckInfoBiz.deleteByBillCheckId(billCheckInfo.getBillCheckId(), delId, operator);
                    billCheckGoodsInfoBiz.deleteByBillCheckId(billCheckInfo.getBillCheckId(), delId, operator);
                    billCheckWaybillInfoBiz.deleteByBillCheckId(billCheckInfo.getBillCheckId(), delId, operator);
                    billCheckChangeLogBiz.deleteByBillCheckId(billCheckInfo.getBillCheckId(), delId, operator);
                }
            }
        } else {
            log.info("billCheckTimeWindowCheck list.size: null");
        }
    }
}
