package com.ecommerce.information.biz.impl;

import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.service.common.BaseBiz;
import com.ecommerce.common.service.common.BizRedisService;
import com.ecommerce.common.service.common.uuid.UUIDGenerator;
import com.github.pagehelper.page.PageMethod;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.information.api.common.RedisKey;
import com.ecommerce.information.api.dto.announcement.*;
import com.ecommerce.information.biz.IAnnouncementBiz;
import com.ecommerce.information.dao.mapper.AnnouncementApproveMapper;
import com.ecommerce.information.dao.mapper.AnnouncementMapper;
import com.ecommerce.information.dao.vo.Announcement;
import com.ecommerce.information.dao.vo.AnnouncementApprove;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
@Transactional
@Service
public class AnnouncementBiz extends BaseBiz<Announcement> implements IAnnouncementBiz {

    @Autowired
    private AnnouncementMapper announcementMapper;

    @Autowired
    private AnnouncementApproveMapper announcementApproveMapper;


    @Autowired
    private BizRedisService bizRedisService;

    @Autowired
    private UUIDGenerator uuidGenerator;

    private static final String DEL_FLG = "delFlg";
    private static final String ANNOUNCEMENT_ID = "announcementId";

    private void insertIntoAnnouncementApprove(String announcementId, String operator) {
        //AnnoucentmentApprove生成
        AnnouncementApprove announcementApprove = new AnnouncementApprove();
        announcementApprove.setId(uuidGenerator.gain());
        announcementApprove.setAnnouncementId(announcementId);
        announcementApprove.setApprovalStatus(0);
        announcementApprove.setComment("修改公告");
        announcementApprove.setCreateUser(operator);
        announcementApprove.setCreateTime(new Date());
        announcementApprove.setDelFlg(0);
        announcementApproveMapper.insertSelective(announcementApprove);
    }

    @Override
    public void create(AnnouncementCreateDTO announcementCreateDTO) {
        //Announcement根据传参进行插入
        Announcement announcement = new Announcement();
        BeanUtils.copyProperties(announcementCreateDTO, announcement);
        String id = uuidGenerator.gain();
        announcement.setAnnouncementId(id);
        announcement.setApprovalStatus(0);
        announcement.setAvailableStatus(1);
        announcement.setCreateUser(announcementCreateDTO.getOperator());
        announcement.setCreateTime(new Date());
        announcement.setDelFlg(0);
        announcementMapper.insert(announcement);

        //添加审核记录
        AnnouncementApprove announcementApprove = new AnnouncementApprove();
        announcementApprove.setAnnouncementId(id);
        announcementApprove.setId(uuidGenerator.gain());
        announcementApprove.setComment("创建公告");
        announcementApprove.setApprovalStatus(0);
        announcementApprove.setCreateUser(announcementCreateDTO.getOperator());
        announcementApprove.setCreateTime(new Date());
        announcementApprove.setDelFlg(0);
        announcementApproveMapper.insert(announcementApprove);
        cleanRedis();
    }

    @Override
    public void delete(String id, String operator) {
        Condition condition = new Condition(Announcement.class);
        condition.createCriteria().andEqualTo(DEL_FLG,0)
                .andEqualTo(ANNOUNCEMENT_ID,id);
            //根据id删除咨询
        Announcement announcement = new Announcement();
        //1的意思
        announcement.setDelFlg(1);
        announcement.setUpdateTime(new Date());
        announcement.setUpdateUser(operator);
        announcementMapper.updateByConditionSelective(announcement,condition);

        //同时删除相关的所有审批记录
        Condition condition1 = new Condition(AnnouncementApprove.class);
        condition1.createCriteria()
                .andEqualTo(DEL_FLG, 0)
                .andEqualTo(ANNOUNCEMENT_ID, id);
        AnnouncementApprove announcementApprove = new AnnouncementApprove();
        announcementApprove.setDelFlg(1);
        announcementApprove.setUpdateTime(new Date());
        announcementApprove.setUpdateUser(operator);
        announcementApproveMapper.updateByConditionSelective(announcementApprove,condition1);
        cleanRedis();
    }

    @Override
    public void update(AnnouncementUpdateDTO announcementUpdateDTO) {
        Announcement announcement = new Announcement();
        announcement.setUpdateUser(announcementUpdateDTO.getOperator());
        announcement.setUpdateTime(new Date());
        announcement.setApprovalStatus(0);
        announcement.setAvailableStatus(0);
        BeanUtils.copyProperties(announcementUpdateDTO,announcement);
        Condition condition = new Condition(Announcement.class);
        condition.createCriteria().andEqualTo(DEL_FLG,0)
                .andEqualTo("createUser",announcementUpdateDTO.getOperator())
                .andEqualTo(ANNOUNCEMENT_ID,announcementUpdateDTO.getAnnouncementId());
        announcementMapper.updateByConditionSelective(announcement,condition);

        //插入审核记录
        insertIntoAnnouncementApprove(announcement.getAnnouncementId(), announcementUpdateDTO.getOperator());
        cleanRedis();
    }

    @Override
    public void approval(AnnouncementApprovalDTO announcementApprovalDTO) {
        //将待审批的Announcement设置为审批通过,只有待审批的咨询才能被通过。
        Announcement announcement = announcementMapper.selectByPrimaryKey(announcementApprovalDTO.getAnnouncementId());
        if(announcement == null){
            throw new BizException(BasicCode.DATA_NOT_EXIST,announcementApprovalDTO.getAnnouncementId());
        }
        String comment = "";
        if(announcementApprovalDTO.getApprovalStatus() == 2){
            comment = CsStringUtils.isBlank(announcementApprovalDTO.getComment()) ? "审核通过" : "审核通过: " + announcementApprovalDTO.getComment();
            announcement.setApprovalStatus(2);
            announcement.setAvailableStatus(0);
        }else if (announcementApprovalDTO.getApprovalStatus() == 1){
            if (CsStringUtils.isBlank(announcementApprovalDTO.getComment())) {
                throw new BizException(BasicCode.INVALID_PARAM,"拒绝理由不可为空");
            }
            comment = "审核未通过: " + announcementApprovalDTO.getComment();
            announcement.setApprovalStatus(1);
            announcement.setAvailableStatus(1);
        }else {
            throw new BizException(BasicCode.INVALID_PARAM,announcementApprovalDTO.getApprovalStatus());
        }
        announcement.setUpdateUser(announcementApprovalDTO.getUpdateUser());
        announcement.setUpdateTime(new Date());
        announcementMapper.updateByPrimaryKey(announcement);


        //插入一条审批通过的新记录
        AnnouncementApprove announcementApprove = new AnnouncementApprove();
        announcementApprove.setAnnouncementId(announcement.getAnnouncementId());
        announcementApprove.setId(uuidGenerator.gain());
        announcementApprove.setComment(comment);
        announcementApprove.setApprovalStatus(announcementApprovalDTO.getApprovalStatus());
        announcementApprove.setCreateTime(new Date());
        announcementApprove.setCreateUser(announcementApprovalDTO.getUpdateUser());
        announcementApprove.setDelFlg(0);
        announcementApproveMapper.insertSelective(announcementApprove);
        cleanRedis();
    }

    @Override
    public void pause(String id, String operator) {
        Announcement announcement = announcementMapper.selectByPrimaryKey(id);
        if (announcement != null) {
            //可用状态 0启用  1暂停
            announcement.setAvailableStatus(1);
            announcement.setUpdateTime(new Date());
            announcement.setUpdateUser(operator);
            announcementMapper.updateByPrimaryKeySelective(announcement);
            cleanRedis();
        }
    }

    @Override
    public void goon(String id, String operator) {
        //不处理时间
        Announcement announcement = announcementMapper.selectByPrimaryKey(id);
        if (announcement != null && announcement.getAvailableStatus() == 1) {
            announcement.setUpdateTime(new Date());
            announcement.setUpdateUser(operator);
            announcement.setAvailableStatus(0);
            announcementMapper.updateByPrimaryKeySelective(announcement);
            cleanRedis();
        }
    }

    @Override
    public AnnouncementDTO findById(String id) {
        Announcement announcement = announcementMapper.selectByPrimaryKey(id);
        if (announcement == null) {
            throw new BizException(BasicCode.DATA_NOT_EXIST,id);
        }
        AnnouncementDTO announcementDTO = new AnnouncementDTO();
        BeanUtils.copyProperties(announcement, announcementDTO);
        return announcementDTO;
    }

    @Override
    public AnnouncementDetailDTO findDetailById(String id) {
        Announcement announcement = announcementMapper.selectByPrimaryKey(id);
        AnnouncementDetailDTO announcementDetailDTO = new AnnouncementDetailDTO();
        List<AnnouncementApprovalDTO> list = new ArrayList<>();
        if (announcement != null) {
            BeanUtils.copyProperties(announcement, announcementDetailDTO);

            Condition condition = new Condition(AnnouncementApprove.class);
            condition.createCriteria().andEqualTo(DEL_FLG,0).andEqualTo(ANNOUNCEMENT_ID, id);
            condition.setOrderByClause("create_time desc");
            List<AnnouncementApprove> queryList = announcementApproveMapper.selectByCondition(condition);

            if(queryList != null && !queryList.isEmpty() ){
                list = queryList.stream().map(item -> {
                    AnnouncementApprovalDTO announcementApprovalDTO = new AnnouncementApprovalDTO();
                    BeanUtils.copyProperties(item, announcementApprovalDTO);
                    return announcementApprovalDTO;
                }).toList();
            }
        }
        announcementDetailDTO.setList(list);
        return announcementDetailDTO;
    }

    @Override
    public PageInfo<Announcement> findAll(AnnouncementQueryDTO query) {
        Condition condition = new Condition(Announcement.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(DEL_FLG,0);
        //约束条件1审批状态，2公告类型，3暂停状态，4关键字
        if (query.getApprovalStatus() != null) {
            criteria.andEqualTo("approvalStatus", query.getApprovalStatus());
        }
        if (query.getAnnouncementType() != null) {
            criteria.andEqualTo("announcementType", query.getAnnouncementType());
        }
        if (query.getAvailableStatus() != null) {
            criteria.andEqualTo("availableStatus", query.getAvailableStatus());
        }
        if (CsStringUtils.isNotBlank(query.getKeywords())) {
            criteria.andLike("keywords", "%" + query.getKeywords() + "%");
        }
        if (CsStringUtils.isNotBlank(query.getTitle())) {
            criteria.andLike("title", "%" + query.getTitle() + "%");
        }
        if(query.getStartTime()!=null){
            criteria.andGreaterThanOrEqualTo("startTime",query.getStartTime());
        }
        if(query.getEndTime()!=null){
            criteria.andLessThanOrEqualTo("endTime",query.getEndTime());
        }
        condition.setOrderByClause("weight desc,update_time desc");
        PageMethod.startPage(query.getPageNumber(),query.getPageSize());
        return new PageInfo<>(announcementMapper.selectByCondition(condition));
    }

    @Override
    public List<Announcement> findForIndex(AnnouncementIndexQueryDTO dto) {
        String key = RedisKey.CACHE_ANNO_FINDFORINDEX;
        String redisField = new StringBuilder()
                .append(dto.getAnnouncementType() == null ? "null" : dto.getAnnouncementType())
                .append("_")
                .append(dto.getSize())
                .toString();
        if( bizRedisService.hasHashKey( key,redisField )){
            return bizRedisService.hget(key,redisField,ArrayList.class);
        }

        String now = LocalDateTime.now().format(dateTimeFormatter);

        List<Announcement> list = null;
        if( dto.getAnnouncementType() == null ){
            list = this.announcementMapper.findForIndex1(now,dto.getSize());
        }else{
            list = this.announcementMapper.findForIndex2(dto.getAnnouncementType(),now,dto.getSize());
        }
        if( list != null && !list.isEmpty() ) {
            bizRedisService.hset(key, redisField, list);
        }

        return list;
    }

    @Override
    public void cleanRedis(){
        bizRedisService.del(RedisKey.CACHE_ANNO_FINDFORINDEX);
    }

    private static final DateTimeFormatter  dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
}
