package com.ecommerce.information.biz.impl;

import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.service.common.BaseBiz;
import com.ecommerce.common.service.common.BizRedisService;
import com.ecommerce.common.service.common.uuid.UUIDGenerator;
import com.ecommerce.common.service.lock.RedisLockService;
import com.ecommerce.information.api.constant.InfoNumberConstant;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.information.api.common.RedisKey;
import com.ecommerce.information.api.dto.ApproveDTO;
import com.ecommerce.information.api.dto.news.NewsDTO;
import com.ecommerce.information.api.dto.news.NewsQueryDTO;
import com.ecommerce.information.biz.INewsTechnologyEquipmentBiz;
import com.ecommerce.information.dao.mapper.NewsTechnologyEquipmentCheckMapper;
import com.ecommerce.information.dao.mapper.NewsTechnologyEquipmentInfoMapper;
import com.ecommerce.information.dao.vo.NewsTechnologyEquipmentCheck;
import com.ecommerce.information.dao.vo.NewsTechnologyEquipmentInfo;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.concurrent.Executor;

/**
 *
 * <AUTHOR>
 */
@Transactional
@Slf4j
@Service
@CacheConfig(cacheManager = "inforCacheManager")
public class NewsTechnologyEquipmentBiz extends BaseBiz<NewsTechnologyEquipmentInfo> implements INewsTechnologyEquipmentBiz {

    @Autowired
    private NewsTechnologyEquipmentInfoMapper newsMapper;
    @Autowired
    private NewsTechnologyEquipmentCheckMapper newsCheckMapper;
    @Autowired
    private BizRedisService bizRedisService;
    @Autowired
    @Qualifier("myExecutor")
    private Executor executor;
    @Autowired
    private UUIDGenerator uuidGenerator;
    @Autowired
    private RedisLockService redisLockService;

    private static final String DEL_FLG = "delFlg";
    private static final String CATEGORY_ID = "categoryId";

    @Override
    public NewsTechnologyEquipmentInfo create(NewsTechnologyEquipmentInfo news) {
        String newsId = uuidGenerator.gain();
        Date now = new Date();
        news.setNewsId(newsId);
        news.setCreateTime(now);
        news.setDelFlg(false);
        news.setCheckStatus(0);

        NewsTechnologyEquipmentCheck newsCheck = new NewsTechnologyEquipmentCheck();
        newsCheck.setCheckId(uuidGenerator.gain());
        newsCheck.setNewsId(newsId);
        newsCheck.setCheckStatus(0);
        newsCheck.setCreateTime(now);
        newsCheck.setCreateUser(news.getCreateUser());
        newsCheck.setReplyContent("发起申请。");
        newsCheck.setDelFlg(false);
        newsCheckMapper.insert(newsCheck);
        int rs = newsMapper.insertSelective(news);
        if( rs == 0 ){
            throw new BizException(BasicCode.DB_INSERT_FAILED,news.getCreateUser()+"create News");
        }
        return news;
    }

    @Override
    public void deleteById(String id,String operator) {
        NewsTechnologyEquipmentInfo news = get(id);
        if(news != null ){
            news.setDelFlg(true);
            news.setUpdateTime(new Date());
            news.setUpdateUser(operator);
            newsMapper.updateByPrimaryKeySelective(news);
            clearCache(operator);
        }
    }

    @Override
    public void deleteByIds(List<String> idList,String operator) {
        Condition condition = new Condition(NewsTechnologyEquipmentInfo.class);
        condition.createCriteria().andIn("newsId",idList);
        NewsTechnologyEquipmentInfo news = new NewsTechnologyEquipmentInfo();
        news.setDelFlg(true);
        news.setUpdateTime(new Date());
        news.setUpdateUser(operator);
        newsMapper.updateByCondition(news,condition);
        cacheEvict(idList);
    }

    @CachePut(value = RedisKey.CACHE_NEWS,key = "'"+RedisKey.CACHE_NEWS+"'+#news.newsId",unless = "#result==null")
    @Override
    public NewsTechnologyEquipmentInfo update(NewsTechnologyEquipmentInfo news) {
        if(news == null || news.getNewsId() == null ){
            log.warn("invalid param");
            return null;
        }
        NewsTechnologyEquipmentInfo old = get(news.getNewsId());
        if(old == null ) {
            log.warn("update,can not find by newsId : {}",news.getNewsId());
            return null;
        }
        Date now = new Date();
        BeanUtils.copyProperties(news,old);
        if(old.getCheckStatus() == null || old.getCheckStatus().intValue() != 2 ) {
            old.setCheckStatus(0);
            NewsTechnologyEquipmentCheck newsCheck = new NewsTechnologyEquipmentCheck();
            newsCheck.setCheckId(uuidGenerator.gain());
            newsCheck.setNewsId(old.getNewsId());
            newsCheck.setCheckStatus(0);
            newsCheck.setCreateTime(now);
            newsCheck.setCreateUser(news.getUpdateUser());
            newsCheck.setReplyContent("重新申请。");
            newsCheck.setDelFlg(false);
            newsCheckMapper.insert(newsCheck);
        }
        old.setUpdateTime(now);
        newsMapper.updateByPrimaryKeySelective(old);
        return old;
    }

    @Cacheable(value = RedisKey.CACHE_NEWS,key = "'"+RedisKey.CACHE_NEWS+"'+#root.args[0]",unless = "#result==null")
    @Override
    public NewsTechnologyEquipmentInfo findById(String id) {
        NewsTechnologyEquipmentInfo news = get(id);
        if( news == null || (news.getDelFlg() !=null && news.getDelFlg().booleanValue()) ){
            return null;
        }
        return news;
    }

    @Override
    public List<NewsTechnologyEquipmentInfo> findByIds(List<String> idList) {
        if( idList == null || idList.isEmpty() ){
            return Lists.newArrayList();
        }
        List<NewsTechnologyEquipmentInfo> result = Lists.newArrayListWithCapacity(idList.size());
        idList.forEach(item->{
            NewsTechnologyEquipmentInfo news = findById(item);
            if( news != null ){
                result.add(news);
            }
        });
        return result;
    }

    @Override
    public List<NewsTechnologyEquipmentInfo> findByCategory(List<String> categoryList) {
        if( categoryList == null || categoryList.isEmpty() ){
            return Lists.newArrayList();
        }
        Condition condition = new Condition(NewsTechnologyEquipmentInfo.class);
        condition.createCriteria().andEqualTo(DEL_FLG,false).andIn(CATEGORY_ID,categoryList);
        return newsMapper.selectByCondition(condition);
    }

    @Override
    public void clearCache(String operator) {
        log.info(" clear cache ,operator : {}",operator);

        bizRedisService.del(RedisKey.CACHE_NEWS_TECHNOLOGYPROJECTNEWS);
        bizRedisService.del(RedisKey.CACHE_NEWS_HOTPRODUCTNEWS);

        Set<String> keys = bizRedisService.getRedisTemplate().keys(RedisKey.CACHE_NEWS+"*");
        if(keys != null && !keys.isEmpty() ){
            keys.forEach(item->bizRedisService.del(item));
        }
        bizRedisService.del(RedisKey.CACHE_NEWS);
    }

    @Override
    public PageInfo<NewsTechnologyEquipmentInfo> findAll(NewsQueryDTO query) {
        Condition condition = new Condition(NewsTechnologyEquipmentInfo.class);
        Example.Criteria criteria = condition.createCriteria().andEqualTo(DEL_FLG,false);
        if (CsStringUtils.isNotBlank(query.getCategoryName())) {
            criteria.andEqualTo("categoryName",query.getCategoryName());
        }
        if (CsStringUtils.isNotBlank(query.getCategoryId())) {
            criteria.andEqualTo(CATEGORY_ID,query.getCategoryId());
        }
        if( query.getCategoryIdNotIn() != null && !query.getCategoryIdNotIn().isEmpty() ){
            criteria.andNotIn(CATEGORY_ID,query.getCategoryIdNotIn());
        }
        if( query.getCategoryIdIn() != null && !query.getCategoryIdIn().isEmpty() ){
            criteria.andIn(CATEGORY_ID,query.getCategoryIdIn());
        }
        if (CsStringUtils.isNotBlank(query.getTitle())) {
            criteria.andLike("title",like(query.getTitle()));
        }
        if (CsStringUtils.isNotBlank(query.getBanner())) {
            criteria.andLike("banner",like(query.getBanner()));
        }
        if (CsStringUtils.isNotBlank(query.getKeywords())) {
            criteria.andLike("keywords",like(query.getKeywords()));
        }
        if( query.getShowInTop() != null ){
            criteria.andEqualTo("showInTop",query.getShowInTop());
        }
        if( query.getShowInIndex() != null ){
            criteria.andEqualTo("showInIndex",query.getShowInIndex());
        }
        if( query.getPicturlNews() != null ){
            criteria.andEqualTo("picturlNews",query.getPicturlNews());
        }
        if( query.getAllowComments() != null ){
            criteria.andEqualTo("allowComments",query.getAllowComments());
        }
        if (CsStringUtils.isNotBlank(query.getDescription())) {
            criteria.andLike("description",like(query.getDescription()));
        }
        if( query.getCheckStatus() != null ){
            criteria.andEqualTo("checkStatus",query.getCheckStatus());
        }
        if( query.getPartnerFlag() != null ){
            criteria.andEqualTo("partnerFlag",query.getPartnerFlag());
        }
        if (CsStringUtils.isNotBlank(query.getProductName())) {
            criteria.andLike("productName",like(query.getProductName()));
        }
        if (CsStringUtils.isNotBlank(query.getProductModel())) {
            criteria.andLike("productModel",like(query.getProductModel()));
        }
        if (CsStringUtils.isNotBlank(query.getProductFeatures())) {
            criteria.andLike("productFeatures",like(query.getProductFeatures()));
        }
        if (CsStringUtils.isNotBlank(query.getProductScope())) {
            criteria.andLike("productScope",like(query.getProductScope()));
        }
        if (CsStringUtils.isNotBlank(query.getProductOwner())) {
            criteria.andLike("productOwner",like(query.getProductOwner()));
        }
        if (CsStringUtils.isNotBlank(query.getProductContact())) {
            criteria.andLike("productContact",like(query.getProductContact()));
        }
        if (CsStringUtils.isNotBlank(query.getProductContactPhone())) {
            criteria.andLike("productContactPhone",like(query.getProductContactPhone()));
        }
        if (CsStringUtils.isNotBlank(query.getEditor())) {
            criteria.andLike("editor",like(query.getEditor()));
        }
        if (CsStringUtils.isNotBlank(query.getSource())) {
            criteria.andLike("source",like(query.getSource()));
        }
        if( query.getOrderByShowInTop() != null && query.getOrderByShowInTop()) {
            // 资讯_行业资讯/政策法规：平台设置了置顶的资讯信息，但是资讯大厅展示的资讯未将置顶资讯放在顶部展示
            condition.setOrderByClause("show_in_top desc,create_time desc");
        }else{
            condition.orderBy("createTime").desc();
        }

        PageInfo<NewsTechnologyEquipmentInfo> pageInfo = new PageInfo<>();
        pageInfo.setPageSize(query.getPageSize() == null || query.getPageSize().intValue() < 1 ? 20 : query.getPageSize().intValue());
        pageInfo.setPageNum(query.getPageNum() == null || query.getPageNum().intValue() < 1 ? 1 : query.getPageNum().intValue());
        return super.pageInfo(condition,pageInfo);
    }

    private String like (String s){
        return "%"+s+"%";
    }


    @Override
    public void top(String id, String operator) {
        executor.execute(()->{
            String lock = null;
            try {
                lock =  redisLockService.lock(id);
                NewsTechnologyEquipmentInfo news = get(id);
                if (news != null) {
                    Long num = news.getPraiseNum();
                    num = num == null || num.longValue() < 0 ? 1 : (num.longValue() == Long.MAX_VALUE ? Long.MAX_VALUE : num.longValue() + 1L);
                    news.setPraiseNum(num);
                    news.setUpdateTime(new Date());
                    news.setUpdateUser(operator);
                    newsMapper.updateByPrimaryKeySelective(news);
                    cacheEvict(id);
                }
            }finally {
                redisLockService.unlock(id,lock);
            }
        });
    }

    @Override
    public void stepOn(String id, String operator) {
        executor.execute(()->{
            String lock = null;
            try {
                lock =  redisLockService.lock(id);
                NewsTechnologyEquipmentInfo news = get(id);
                if (news != null) {
                    Long num = news.getBadReviewNum();
                    num = num == null || num.longValue() < 0 ? 1 : (num.longValue() == Long.MAX_VALUE ? Long.MAX_VALUE : num.longValue() + 1L);
                    news.setBadReviewNum(num);
                    news.setUpdateTime(new Date());
                    news.setUpdateUser(operator);
                    newsMapper.updateByPrimaryKeySelective(news);
                    cacheEvict(id);
                }
            }finally {
                redisLockService.unlock(id,lock);
            }
        });
    }

    @Override
    public void addHits(String id, String operator) {
        executor.execute(()->{
            String lock = null;
            try {
                lock =  redisLockService.lock(id);
                NewsTechnologyEquipmentInfo news = get(id);
                if (news != null) {
                    Long num = news.getHits();
                    num = num == null || num.longValue() < 0 ? 1 : (num.longValue() == Long.MAX_VALUE ? Long.MAX_VALUE : num.longValue() + 1L);
                    news.setHits(num);
                    news.setUpdateTime(new Date());
                    news.setUpdateUser(operator);
                    newsMapper.updateByPrimaryKeySelective(news);
                    cacheEvict(id);
                }
            }finally {
                redisLockService.unlock(id,lock);
            }
        });
    }

    @CachePut(value = RedisKey.CACHE_NEWS,key = "'"+RedisKey.CACHE_NEWS+"'+#dto.id",unless = "#result==null")
    @Override
    public NewsTechnologyEquipmentInfo approved(ApproveDTO dto) {
        Date now = new Date();
        NewsTechnologyEquipmentInfo news = get(dto.getId());
        if(news == null ){
            log.warn("approved can not find news,newsId: {}",dto.getId());
            return null;
        }
        news.setCheckStatus(2);
        news.setCreateTime(now);
        news.setCreateUser(dto.getCreateUser());
        newsMapper.updateByPrimaryKeySelective(news);

        NewsTechnologyEquipmentCheck newsCheck = new NewsTechnologyEquipmentCheck();
        newsCheck.setCheckId(uuidGenerator.gain());
        newsCheck.setNewsId(dto.getId());
        newsCheck.setCheckStatus(2);
        newsCheck.setCreateTime(now);
        newsCheck.setCreateUser(dto.getCreateUser());
        newsCheck.setReplyContent(CsStringUtils.isNotBlank(dto.getComment()) ? "审批通过: " + dto.getComment() : "审批通过。");
        newsCheck.setDelFlg(false);
        newsCheckMapper.insert(newsCheck);
        clearCache(dto.getCreateUser());
        return news;
    }

    @CachePut(value = RedisKey.CACHE_NEWS,key = "'"+RedisKey.CACHE_NEWS+"'+#dto.id",unless = "#result==null")
    @Override
    public NewsTechnologyEquipmentInfo rejected(ApproveDTO dto) {
        Date now = new Date();
        NewsTechnologyEquipmentInfo news = get(dto.getId());
        if(news == null ){
            log.warn("rejected can not find news,newsId: {}",dto.getId());
            return null;
        }
        news.setCheckStatus(1);
        news.setCreateTime(now);
        news.setCreateUser(dto.getCreateUser());
        newsMapper.updateByPrimaryKeySelective(news);

        NewsTechnologyEquipmentCheck newsCheck = new NewsTechnologyEquipmentCheck();
        newsCheck.setCheckId(uuidGenerator.gain());
        newsCheck.setNewsId(dto.getId());
        newsCheck.setCheckStatus(1);
        newsCheck.setCreateTime(now);
        newsCheck.setCreateUser(dto.getCreateUser());
        newsCheck.setReplyContent(CsStringUtils.isNotBlank(dto.getComment()) ? "审批未通过: " + dto.getComment() : "审批未通过。");
        newsCheck.setDelFlg(false);
        newsCheckMapper.insert(newsCheck);
        return news;
    }

    @Override
    public List<NewsTechnologyEquipmentCheck> findApproveListById(String newsId) {
        Condition condition = new Condition(NewsTechnologyEquipmentCheck.class);
        condition.createCriteria().andEqualTo(DEL_FLG,false)
                .andEqualTo("newsId",newsId);
        condition.orderBy("createTime").desc();
        return newsCheckMapper.selectByCondition(condition);
    }

    /**
     * 资讯_最新资讯
     * @param size
     * @return
     */
    @Override
    public List<NewsDTO> latestNews(Integer size) {
        String key = RedisKey.CACHE_NEWS_LATESTNEWS;
        List<NewsDTO> result = null;
        if( bizRedisService.hasKey(key)){
            result = bizRedisService.get(key);
        }
        if( result != null ){
            return result;
        }
        NewsQueryDTO query = new NewsQueryDTO();
        query.setCategoryIdNotIn(newCategoryNotIn);
        query.setOrderByShowInTop(true);
        result = getPageList(query,size);
        if( result != null ){
            bizRedisService.set(key,result);
        }
        return result;
    }

    private static List<String> newCategoryNotIn = Lists.newArrayList("INFO_TYPE_HOT_PRODUCT,INFO_TYPE_TECHNOLOGY_PROJECT,INFO_TYPE_INVEST,INFO_TYPE_PARTNER,INFO_TYPE_DECORATE,INFO_TYPE_PROD,INFO_TYPE_BUILD,INFO_TYPE_MANAGE,INFO_TYPE_SHOW,INFO_TYPE_DESIGN,INFO_TYPE_APPLICATION".split(","));

    /**
     * 资讯_全国资讯
     * @param size
     * @return
     */
    @Override
    public List<NewsDTO> nationalNews(Integer size) {
        String key = RedisKey.CACHE_NEWS_NATIONALNEWS;
        List<NewsDTO> result = null;
        if( bizRedisService.hasKey(key)){
            result = bizRedisService.get(key);
        }
        if( result != null ){
            return result;
        }
        NewsQueryDTO query = new NewsQueryDTO();
        //国家新闻
        query.setCategoryId("INFO_TYPE_NATIONAL");
        query.setOrderByShowInTop(true);
        result = getPageList(query,size);
        if( result != null ){
            bizRedisService.set(key,result);
        }
        return result;
    }

    /**
     * 资讯企业
     * @param size
     * @return
     */
    @Override
    public List<NewsDTO> corporateNews(Integer size) {
        String key = RedisKey.CACHE_NEWS_CORPORATENEWS;
        List<NewsDTO> result = null;
        if( bizRedisService.hasKey(key)){
            result = bizRedisService.get(key);
        }
        if( result != null ){
            return result;
        }
        NewsQueryDTO query = new NewsQueryDTO();
        query.setCategoryId("INFO_TYPE_CORPORATE");
        query.setOrderByShowInTop(true);
        result = getPageList(query,size);
        if( result != null ){
            bizRedisService.set(key,result);
        }
        return result;
    }

    /**
     * 资讯_政策法规
     * @param size
     * @return
     */
    @Override
    public List<NewsDTO> policiesAndRegulations(Integer size) {
        String key = RedisKey.CACHE_NEWS_POLICIESANDREGULATIONS;
        List<NewsDTO> result = null;
        if( bizRedisService.hasKey(key)){
            result = bizRedisService.get(key);
        }
        if( result != null ){
            return result;
        }
        NewsQueryDTO query = new NewsQueryDTO();
        query.setCategoryId("INFO_TYPE_POLICIES_AND_REGULATIONS");
        query.setOrderByShowInTop(true);
        result = getPageList(query,size);
        if( result != null ){
            bizRedisService.set(key,result);
        }
        return result;
    }

    /**
     * 技术装备_热门产品
     * @param size
     * @return
     */
    @Override
    public List<NewsDTO> hotProductNews(Integer size) {
        String key = RedisKey.CACHE_NEWS_HOTPRODUCTNEWS;
        List<NewsDTO> result = null;
        if( bizRedisService.hasKey(key)){
            result = bizRedisService.get(key);
        }
        if( result != null ){
            return result;
        }
        NewsQueryDTO query = new NewsQueryDTO();
        query.setCategoryId("INFO_TYPE_HOT_PRODUCT");
        query.setOrderByShowInTop(true);
        result = getPageList(query,size);
        if( result != null ){
            bizRedisService.set(key,result);
        }
        return result;
    }

    /**
     * 技术装备_技术专题
     * @param size
     * @return
     */
    @Override
    public List<NewsDTO> technologyProjectNews(Integer size) {
        String key = RedisKey.CACHE_NEWS_TECHNOLOGYPROJECTNEWS;
        List<NewsDTO> result = null;
        if( bizRedisService.hasKey(key)){
            result = bizRedisService.get(key);
        }
        if( result != null ){
            return result;
        }
        NewsQueryDTO query = new NewsQueryDTO();
        query.setCategoryId("INFO_TYPE_TECHNOLOGY_PROJECT");
        query.setOrderByShowInTop(true);
        result = getPageList(query,size);
        if( result != null ){
            bizRedisService.set(key,result);
        }
        return result;
    }

    /**
     * 装配式建筑_投资开发
     * @param size
     * @return
     */
    @Override
    public List<NewsDTO> investProjectNews(Integer size) {
        String key = RedisKey.CACHE_NEWS_INVESTPRODUCTNEWS;
        List<NewsDTO> result = null;
        if( bizRedisService.hasKey(key)){
            result = bizRedisService.get(key);
        }
        if( result != null ){
            return result;
        }
        NewsQueryDTO query = new NewsQueryDTO();
        query.setCategoryId("INFO_TYPE_INVEST");
        query.setOrderByShowInTop(true);
        result = getPageList(query,size);
        if( result != null ){
            bizRedisService.set(key,result);
        }
        return result;
    }

    /**
     * 装配式建筑_工业化生产
     * @param size
     * @return
     */
    @Override
    public List<NewsDTO> prodProjectNews(Integer size) {
        String key = RedisKey.CACHE_NEWS_PRODPROJECTNEWS;
        List<NewsDTO> result = null;
        if( bizRedisService.hasKey(key)){
            result = bizRedisService.get(key);
        }
        if( result != null ){
            return result;
        }
        NewsQueryDTO query = new NewsQueryDTO();
        query.setCategoryId("INFO_TYPE_PROD");
        query.setOrderByShowInTop(true);
        result = getPageList(query,size);
        if( result != null ){
            bizRedisService.set(key,result);
        }
        return result;
    }

    /**
     * 装配式建筑_标准化设计
     * @param size
     * @return
     */
    @Override
    public List<NewsDTO> designProjectNews(Integer size) {
        String key = RedisKey.CACHE_NEWS_DESIGNPRODUCTNEWS;
        List<NewsDTO> result = null;
        if( bizRedisService.hasKey(key)){
            result = bizRedisService.get(key);
        }
        if( result != null ){
            return result;
        }
        NewsQueryDTO query = new NewsQueryDTO();
        query.setCategoryId("INFO_TYPE_DESIGN");
        query.setOrderByShowInTop(true);
        result = getPageList(query,size);
        if( result != null ){
            bizRedisService.set(key,result);
        }
        return result;
    }

    /**
     * 装配式建筑_装配化施工
     * @param size
     * @return
     */
    @Override
    public List<NewsDTO> buildProjectNews(Integer size) {
        String key = RedisKey.CACHE_NEWS_BUILDPROJECTNEWS;
        List<NewsDTO> result = null;
        if( bizRedisService.hasKey(key)){
            result = bizRedisService.get(key);
        }
        if( result != null ){
            return result;
        }
        NewsQueryDTO query = new NewsQueryDTO();
        query.setCategoryId("INFO_TYPE_BUILD");
        query.setOrderByShowInTop(true);
        result = getPageList(query,size);
        if( result != null ){
            bizRedisService.set(key,result);
        }
        return result;
    }

    /**
     * 装配式建筑_一体化装修
     * @param size
     * @return
     */
    @Override
    public List<NewsDTO> decorateProjectNews(Integer size) {
        String key = RedisKey.CACHE_NEWS_DECORATEPRODUCTNEWS;
        List<NewsDTO> result = null;
        if( bizRedisService.hasKey(key)){
            result = bizRedisService.get(key);
        }
        if( result != null ){
            return result;
        }
        NewsQueryDTO query = new NewsQueryDTO();
        query.setCategoryId("INFO_TYPE_DECORATE");
        query.setOrderByShowInTop(true);
        result = getPageList(query,size);
        if( result != null ){
            bizRedisService.set(key,result);
        }
        return result;
    }

    /**
     * 装配式建筑_信息化管理
     * @param size
     * @return
     */
    @Override
    public List<NewsDTO> manageProjectNews(Integer size) {
        String key = RedisKey.CACHE_NEWS_MANAGEPROJECTNEWS;
        List<NewsDTO> result = null;
        if( bizRedisService.hasKey(key)){
            result = bizRedisService.get(key);
        }
        if( result != null ){
            return result;
        }
        NewsQueryDTO query = new NewsQueryDTO();
        query.setCategoryId("INFO_TYPE_MANAGE");
        query.setOrderByShowInTop(true);
        result = getPageList(query,size);
        if( result != null ){
            bizRedisService.set(key,result);
        }
        return result;
    }

    /**
     * 装配式建筑_智能化应用
     * @param size
     * @return
     */
    @Override
    public List<NewsDTO> applicationProjectNews(Integer size) {
        String key = RedisKey.CACHE_NEWS_APPLICATIONRODUCTNEWS;
        List<NewsDTO> result = null;
        if( bizRedisService.hasKey(key)){
            result = bizRedisService.get(key);
        }
        if( result != null ){
            return result;
        }
        NewsQueryDTO query = new NewsQueryDTO();
        query.setCategoryId("INFO_TYPE_APPLICATION");
        query.setOrderByShowInTop(true);
        result = getPageList(query,size);
        if( result != null ){
            bizRedisService.set(key,result);
        }
        return result;
    }

    /**
     * 装配式建筑_项目展示
     * @param size
     * @return
     */
    @Override
    public List<NewsDTO> showProjectNews(Integer size) {
        String key = RedisKey.CACHE_NEWS_SHOWROJECTNEWS;
        List<NewsDTO> result = null;
        if( bizRedisService.hasKey(key)){
            result = bizRedisService.get(key);
        }
        if( result != null ){
            return result;
        }
        NewsQueryDTO query = new NewsQueryDTO();
        query.setCategoryId("INFO_TYPE_SHOW");
        query.setOrderByShowInTop(true);
        result = getPageList(query,size);
        if( result != null ){
            bizRedisService.set(key,result);
        }
        return result;
    }

    /**
     * 装配式建筑_战略合作伙伴
     * @param size
     * @return
     */
    @Override
    public List<NewsDTO> partnerProjectNews(Integer size) {
        String key = RedisKey.CACHE_NEWS_PARTNERPROJECTNEWS;
        List<NewsDTO> result = null;
        if( bizRedisService.hasKey(key)){
            result = bizRedisService.get(key);
        }
        if( result != null ){
            return result;
        }
        NewsQueryDTO query = new NewsQueryDTO();
        query.setCategoryId("INFO_TYPE_PARTNER");
        query.setOrderByShowInTop(true);
        result = getPageList(query,size);
        if( result != null ){
            bizRedisService.set(key,result);
        }
        return result;
    }

    private List<NewsDTO> getPageList(NewsQueryDTO query, Integer size){
        query = query == null ? new NewsQueryDTO() : query;
        query.setPageNum(1);
        query.setPageSize(size==null || size> InfoNumberConstant.ONE_HUNDRED_INTEGER || size<InfoNumberConstant.ONE_INTEGER ? InfoNumberConstant.TEN_INTEGER : size);
        query.setCheckStatus(2);
        PageInfo<NewsTechnologyEquipmentInfo> pageInfo = findAll(query);
        if( pageInfo != null && pageInfo.getList() != null && !pageInfo.getList().isEmpty() ){
            List<NewsDTO> result = Lists.newArrayListWithCapacity(pageInfo.getList().size());
            pageInfo.getList().forEach(item ->{
                NewsDTO dto = vo2Dto(item);
                if( dto != null ){
                    result.add(dto);
                }
            });
            if( !result.isEmpty() ){
                return result;
            }
        }
        return Lists.newArrayList();
    }

    private NewsDTO vo2Dto(NewsTechnologyEquipmentInfo vo){
        if( vo == null ){
            return null;
        }
        NewsDTO dto = new NewsDTO();
        BeanUtils.copyProperties(vo,dto);
        return dto;
    }

    private void cacheEvict(List<String> idList){
        if(idList != null && !idList.isEmpty() ){
            idList.forEach(item ->bizRedisService.del(RedisKey.CACHE_NEWS+item));
        }
    }

    private void cacheEvict(String id){
        if(id != null ){
            bizRedisService.del(RedisKey.CACHE_NEWS+id);
        }
    }
}
