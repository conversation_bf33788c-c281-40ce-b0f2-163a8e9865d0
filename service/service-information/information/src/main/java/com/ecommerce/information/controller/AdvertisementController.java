package com.ecommerce.information.controller;

import com.ecommerce.information.api.dto.ApproveDTO;
import com.ecommerce.information.api.dto.advertisement.*;
import com.ecommerce.information.service.IAdvertisementService;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * null
 *
 * <AUTHOR>
*/
@RestController
@Tag(name = "Advertisement", description = "null")
@RequestMapping("/advertisement")
public class AdvertisementController {

   @Autowired 
   private IAdvertisementService iAdvertisementService;

   @Operation(summary = "禁用（平台管理方法）")
   @PostMapping(value="/disable")
   public void disable(@RequestParam String id,@RequestParam String operator){
      iAdvertisementService.disable(id,operator);

   }


   @Operation(summary = "启用（平台管理方法）")
   @PostMapping(value="/enable")
   public void enable(@RequestParam String id,@RequestParam String operator){
      iAdvertisementService.enable(id,operator);

   }


   @Operation(summary = "查询显示广告")
   @PostMapping(value="/list")
   public AdvertisementListDTO list(@RequestBody AdvertisementQueryDTO query){
      return iAdvertisementService.list(query);
   }


   /*@ApiOperation("卖家删除广告")
   @PostMapping(value="/deleteBySeller")
   public void deleteBySeller(@RequestParam("id") String id,@RequestParam("operator") String operator){
      iAdvertisementService.deleteBySeller(id,operator);

   }*/


   @Operation(summary = "根据条件翻页查询(平台广告管理界面列表页使用、平台审批界面列表页使用)")
   @PostMapping(value="/findAllByAdmin")
   public PageInfo<AdvertisementDetailDTO> findAllByAdmin(@RequestBody AdvertisementAdminQueryDTO query){
      return iAdvertisementService.findAllByAdmin(query);
   }


   @Operation(summary = "通过广告id查询审核记录")
   @PostMapping(value="/findCheckbyId")
   public List<AdvertisementCheckDTO> findCheckbyId(@RequestParam String id){
      return iAdvertisementService.findCheckbyId(id);
   }


   /*@ApiOperation("根据条件翻页查询(卖家广告管理界面列表页使用)")
   @PostMapping(value="/findAllBySeller")
   public PageInfo<AdvertisementDetailDTO> findAllBySeller(@RequestBody AdvertisementSellerQueryDTO query){
      return iAdvertisementService.findAllBySeller(query);
   }*/


   /*@ApiOperation("卖家编辑广告")
   @PostMapping(value="/updateBySeller")
   public AdvertisementDetailDTO updateBySeller(@RequestBody AdvertisementSellerUpdateDTO advertisementSellerUpdateDTO){
      return iAdvertisementService.updateBySeller(advertisementSellerUpdateDTO);
   }*/


   @Operation(summary = "查询单个广告详情（含审批信息）")
   @PostMapping(value="/findDetailById")
   public AdvertisementCheckListDTO findDetailById(@RequestParam String id){
      return iAdvertisementService.findDetailById(id);
   }


   @Operation(summary = "平台创建广告")
   @PostMapping(value="/createByAdmin")
   public AdvertisementDetailDTO createByAdmin(@RequestBody AdvertisementAdminCreateDTO advertisementAdminCreateDTO){
      return iAdvertisementService.createByAdmin(advertisementAdminCreateDTO);
   }


   @Operation(summary = "查询单个广告（不含审批信息）")
   @PostMapping(value="/findById")
   public AdvertisementDetailDTO findById(@RequestParam String id){
      return iAdvertisementService.findById(id);
   }


   @Operation(summary = "审批被驳回（平台审批方法）")
   @PostMapping(value="/rejected")
   public void rejected(@RequestBody ApproveDTO dto){
      iAdvertisementService.rejected(dto);

   }


   @Operation(summary = "平台删除广告")
   @PostMapping(value="/deleteByAdmin")
   public void deleteByAdmin(@RequestParam String id,@RequestParam String operator){
      iAdvertisementService.delete(id,operator);

   }


   @Operation(summary = "平台编辑广告")
   @PostMapping(value="/updateByAdmin")
   public AdvertisementDetailDTO updateByAdmin(@RequestBody AdvertisementAdminUpdateDTO advertisementAdminUpdateDTO){
      return iAdvertisementService.updateByAdmin(advertisementAdminUpdateDTO);
   }


   /*@ApiOperation("卖家创建广告")
   @PostMapping(value="/createBySeller")
   public AdvertisementDetailDTO createBySeller(@RequestBody AdvertisementSellerCreateDTO advertisementSellerCreateDTO){
      return iAdvertisementService.createBySeller(advertisementSellerCreateDTO);
   }*/


   @Operation(summary = "审批通过（平台审批方法）")
   @PostMapping(value="/approved")
   public void approved(@RequestBody ApproveDTO dto){
      iAdvertisementService.approved(dto);

   }

   @Operation(summary = "根据广告位code查找广告")
   @PostMapping(value="/findBySpaceCode")
   public List<AdvertisementDetailDTO> findBySpaceCode(@RequestParam String spaceCode){
      return iAdvertisementService.findBySpaceCode(spaceCode);
   }

   @Operation(summary = "通过广告位编码集合查询广告")
   @PostMapping(value="/findAdvertisementAndBusinessInfoBySpaceCode")
   public List<AdvertisementAndBusinessInfoDTO> findAdvertisementAndBusinessInfoBySpaceCode(@RequestBody AdvertisementAndBusinessInfoQueryDTO dto){
      return iAdvertisementService.findAdvertisementAndBusinessInfoBySpaceCode(dto);
   }

   @Operation(summary = "添加广告时，通过广告位编码和关键字查询对应业务类型的数据：如商品、资讯等等")
   @PostMapping(value="/findBusinessBySpaceCodeAndKeyword")
   public List<AdvertisementBusinessInfoDTO> findBusinessBySpaceCodeAndKeyword(@RequestBody AdvertisementBusinessInfoQueryDTO dto){
      return iAdvertisementService.findBusinessBySpaceCodeAndKeyword(dto);
   }
}
