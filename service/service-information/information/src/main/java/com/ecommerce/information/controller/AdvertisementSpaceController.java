package com.ecommerce.information.controller;

import com.ecommerce.information.api.dto.advertisement.AdverisementSpaceCreateDTO;
import com.ecommerce.information.api.dto.advertisement.AdverisementSpaceDTO;
import com.ecommerce.information.api.dto.advertisement.AdverisementSpaceUpdateDTO;
import com.ecommerce.information.api.dto.advertisement.AdvertisementSpaceQueryDTO;
import com.ecommerce.information.service.IAdvertisementSpaceService;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * null
 *
 * <AUTHOR>
*/

@RestController
@Tag(name = "AdvertisementSpace", description = "null")
@RequestMapping("/advertisementSpace")
public class AdvertisementSpaceController {

   @Autowired 
   private IAdvertisementSpaceService iAdvertisementSpaceService;

   @Operation(summary = "禁用广告位（平台管理方法）")
   @PostMapping(value="/disable")
   public void disable(@RequestParam String id,@RequestParam String operator)throws Exception{
      iAdvertisementSpaceService.disable(id,operator);

   }


   @Operation(summary = "启用广告位（平台管理方法）")
   @PostMapping(value="/enable")
   public void enable(@RequestParam String id,@RequestParam String operator)throws Exception{
      iAdvertisementSpaceService.enable(id,operator);

   }


   @Operation(summary = "修改广告位")
   @PostMapping(value="/update")
   public void update(@RequestBody AdverisementSpaceUpdateDTO adverisementSpaceUpdateDTO,@RequestParam String operator)throws Exception{
      iAdvertisementSpaceService.update(adverisementSpaceUpdateDTO,operator);

   }


   @Operation(summary = "删除广告位")
   @PostMapping(value="/delete")
   public void delete(@RequestParam String id,@RequestParam String operator)throws Exception{
      iAdvertisementSpaceService.delete(id,operator);

   }


   @Operation(summary = "新增广告位")
   @PostMapping(value="/create")
   public void create(@RequestBody AdverisementSpaceCreateDTO adverisementSpaceCreateDTO,@RequestParam String operator)throws Exception{
      iAdvertisementSpaceService.create(adverisementSpaceCreateDTO,operator);

   }


   @Operation(summary = "查找所有广告位")
   @PostMapping(value="/findAll")
   public PageInfo<AdverisementSpaceDTO> findAll(@RequestBody AdvertisementSpaceQueryDTO query)throws Exception{
      return iAdvertisementSpaceService.findAll(query);
   }


   @Operation(summary = "根据id查找广告位")
   @PostMapping(value="/findById")
   public AdverisementSpaceCreateDTO findById(@RequestParam String id)throws Exception{
      return iAdvertisementSpaceService.findById(id);
   }

   @Operation(summary = "根据关键字查找广告位")
   @PostMapping(value="/findByKeyWord")
   public List<AdverisementSpaceDTO> findByKeyWord(@RequestParam String keyWord)throws Exception{
      return iAdvertisementSpaceService.findByKeyWord(keyWord);
   }

}
