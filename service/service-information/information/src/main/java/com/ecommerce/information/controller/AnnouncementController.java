package com.ecommerce.information.controller;

import com.ecommerce.common.exception.BusinessOperationException;import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ecommerce.information.service.IAnnouncementService;
import com.ecommerce.information.api.dto.announcement.AnnouncementDTO;
import java.util.List;
import com.ecommerce.information.api.dto.announcement.AnnouncementCreateDTO;
import com.ecommerce.information.api.dto.announcement.AnnouncementQueryDTO;
import com.ecommerce.information.api.dto.announcement.AnnouncementApprovalDTO;
import java.lang.String;
import com.ecommerce.information.api.dto.announcement.AnnouncementIndexQueryDTO;
import com.ecommerce.information.api.dto.announcement.AnnouncementDetailDTO;
import com.ecommerce.information.api.dto.announcement.AnnouncementUpdateDTO;
import com.github.pagehelper.PageInfo;


/**
 * 咨询管理接口
 *
 * <AUTHOR>
*/

@RestController
@Tag(name = "Announcement", description = "null")
@RequestMapping("/announcement")
public class AnnouncementController {

   @Autowired 
   private IAnnouncementService iAnnouncementService;

   @Operation(summary = "修改一个公告（如果修改审批 通过的公共，审批状态应该改为未审批，等待再次审批）")
   @PostMapping(value="/update")
   public void update(@RequestBody AnnouncementUpdateDTO announcementUpdateDTO){
      iAnnouncementService.update(announcementUpdateDTO);

   }


   @Operation(summary = "删除一个公告")
   @PostMapping(value="/delete")
   public void delete(@RequestParam String id,@RequestParam String operator){
      iAnnouncementService.delete(id,operator);

   }


   @Operation(summary = "添加一个公告")
   @PostMapping(value="/create")
   public void create(@RequestBody AnnouncementCreateDTO announcementCreateDTO){
      iAnnouncementService.create(announcementCreateDTO);

   }


   @Operation(summary = "按条件翻页查询公告")
   @PostMapping(value="/findAll")
   public PageInfo<AnnouncementDTO> findAll(@RequestBody AnnouncementQueryDTO query){
      return iAnnouncementService.findAll(query);
   }


   @Operation(summary = "查询单条公告详情(不含审批记录)")
   @PostMapping(value="/findById")
   public AnnouncementDTO findById(@RequestParam String id){
      return iAnnouncementService.findById(id);
   }


   @Operation(summary = "查询单条公告详情(含审批记录)")
   @PostMapping(value="/findDetailById")
   public AnnouncementDetailDTO findDetailById(@RequestParam String id){
      return iAnnouncementService.findDetailById(id);
   }


   @Operation(summary = "禁止（暂停）公告")
   @PostMapping(value="/pause")
   public void pause(@RequestParam String id,@RequestParam String operator){
      iAnnouncementService.pause(id,operator);

   }


   @Operation(summary = "启用已暂停的公告(如果当前时间已经过期，当前公告也不会显示)")
   @PostMapping(value="/goon")
   public void goon(@RequestParam String id,@RequestParam String operator){
      iAnnouncementService.goon(id,operator);

   }


   @Operation(summary = "公告审批")
   @PostMapping(value="/approval")
   public void approval(@RequestBody AnnouncementApprovalDTO announcementApprovalDTO){
      iAnnouncementService.approval(announcementApprovalDTO);

   }


   @Operation(summary = "查询主页显示的公共，查询对象可以为空")
   @PostMapping(value="/findForIndex")
   public List<AnnouncementDTO> findForIndex(@RequestBody AnnouncementIndexQueryDTO query){
      return iAnnouncementService.findForIndex(query);
   }



}
