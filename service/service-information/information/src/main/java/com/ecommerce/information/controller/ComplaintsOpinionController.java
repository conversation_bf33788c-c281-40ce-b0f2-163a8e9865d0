package com.ecommerce.information.controller;

import com.ecommerce.information.api.dto.complaintsOpinion.*;
import com.ecommerce.information.service.IComplaintsOpinionService;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * @Created锛�Thu Nov 29 15:43:56 CST 2018
 * <AUTHOR>
 * @Version:2
 * @Description:投诉建议服务接口
*/

@RestController
@Tag(name = "ComplaintsOpinion", description = "投诉建议服务接口")
@RequestMapping("/complaintsOpinion")
public class ComplaintsOpinionController {

   @Autowired 
   private IComplaintsOpinionService iComplaintsOpinionService;

   @Operation(summary = "添加投诉建议(投诉方)")
   @PostMapping(value="/addComplaintsOpinion")
   public void addComplaintsOpinion(@RequestBody ComplaintsOpinionAddDTO arg0){
      iComplaintsOpinionService.addComplaintsOpinion(arg0);
   }

   @Operation(summary = "处理投诉建议(投诉方)")
   @PostMapping(value="/addRecordByBuyer")
   public void addRecordByBuyer(@RequestBody ProcessingRecordsAddDTO arg0){
      iComplaintsOpinionService.addRecordByBuyer(arg0);
   }

   @Operation(summary = "处理投诉建议(被投诉方)")
   @PostMapping(value="/addRecordBySeller")
   public void addRecordBySeller(@RequestBody ProcessingRecordsAddDTO arg0){
      iComplaintsOpinionService.addRecordBySeller(arg0);
   }

   @Operation(summary = "处理投诉建议(被投诉方)")
   @PostMapping(value="/needPlatform")
   public void needPlatform(@RequestParam String coid,@RequestParam String operator){
      iComplaintsOpinionService.needPlatform(coid,operator);
   }

   @Operation(summary = "处理投诉建议(平台)")
   @PostMapping(value="/addRecordByPlatform")
   public void addRecordByPlatform(@RequestBody ProcessingRecordsAddDTO arg0){
      iComplaintsOpinionService.addRecordByPlatform(arg0);
   }

   @Operation(summary = "完结投诉建议")
   @PostMapping(value="/complateComplaintsOpinion")
   public void complateComplaintsOpinion(@RequestBody ComplaintsOpinionComplateDTO arg0)throws Exception{
      iComplaintsOpinionService.complateComplaintsOpinion(arg0);
   }

   @Operation(summary = "取消投诉建议")
   @PostMapping(value="/cancelComplaintsOpinion")
   public void cancelComplaintsOpinion(@RequestBody ComplaintsOpinionComplateDTO arg0)throws Exception{
      iComplaintsOpinionService.cancelComplaintsOpinion(arg0);
   }

   @Operation(summary = "删除投诉建议")
   @PostMapping(value="/deleteComplaintsOpinion")
   public void deleteComplaintsOpinion(@RequestBody ComplaintsOpinionDeleteDTO arg0)throws Exception{
      iComplaintsOpinionService.deleteComplaintsOpinion(arg0);
   }

   @Operation(summary = "按条件分页查询投诉建议")
   @PostMapping(value="/getComplaintsOpinionListByCondition")
   public PageInfo<ComplaintsOpinionDTO> getComplaintsOpinionListByCondition(@RequestBody ComplaintsQueryDTO arg0)throws Exception{
      return iComplaintsOpinionService.getComplaintsOpinionListByCondition(arg0);
   }

   @Operation(summary = "查询单条投诉建议（平台）")
   @PostMapping(value="/findById")
   public ComplaintsOpinionDTO findById(@RequestParam String arg0)throws Exception{
      return iComplaintsOpinionService.findById(arg0);
   }

   @Operation(summary = "查询单条投诉建议(投诉方)")
   @PostMapping(value="/findByIdAndCreateMemberId")
   public ComplaintsOpinionDTO findByIdAndCreateMemberId(@RequestParam String arg0,@RequestParam String arg1)throws Exception{
      return iComplaintsOpinionService.findByIdAndCreateMemberId(arg0,arg1);
   }
   @Operation(summary = "查询单条投诉建议(被投诉方)")
   @PostMapping(value="/findByIdAndDefendantMemberId")
   public ComplaintsOpinionDTO findByIdAndDefendantMemberId(@RequestParam String arg0,@RequestParam String arg1)throws Exception{
      return iComplaintsOpinionService.findByIdAndDefendantMemberId(arg0,arg1);
   }

   @Operation(summary = "查询单条投诉建议(投诉人)")
   @PostMapping(value="/findByIdAndAccountId")
   public ComplaintsOpinionDTO findByIdAndAccountId(@RequestParam String arg0,@RequestParam String arg1)throws Exception{
      return iComplaintsOpinionService.findByIdAndAccountId(arg0,arg1);
   }

   @Operation(summary = "查询投诉建议处理记录（平台）")
   @PostMapping(value="/findProcessingRecordsById")
   public List<ProcessingRecordsDTO> findProcessingRecordsById(@RequestParam String arg0)throws Exception{
      return iComplaintsOpinionService.findProcessingRecordsById(arg0);
   }

   @Operation(summary = "查询投诉建议处理记录(投诉方)")
   @PostMapping(value="/findProcessingRecordsByIdAndCreateMemberId")
   public List<ProcessingRecordsDTO> findProcessingRecordsByIdAndCreateMemberId(@RequestParam String arg0,@RequestParam String arg1)throws Exception{
      return iComplaintsOpinionService.findProcessingRecordsByIdAndCreateMemberId(arg0,arg1);
   }

   @Operation(summary = "查询投诉建议处理记录(被投诉方)")
   @PostMapping(value="/findProcessingRecordsByIdAndDefendantMemberId")
   public List<ProcessingRecordsDTO> findProcessingRecordsByIdAndDefendantMemberId(@RequestParam String arg0,@RequestParam String arg1)throws Exception{
      return iComplaintsOpinionService.findProcessingRecordsByIdAndDefendantMemberId(arg0,arg1);
   }

   @Operation(summary = "查询投诉建议处理记录(投诉人)")
   @PostMapping(value="/findProcessingRecordsByIdAndAccountId")
   public List<ProcessingRecordsDTO> findProcessingRecordsByIdAndAccountId(@RequestParam String arg0,@RequestParam String arg1)throws Exception{
      return iComplaintsOpinionService.findProcessingRecordsByIdAndAccountId(arg0,arg1);
   }

}
