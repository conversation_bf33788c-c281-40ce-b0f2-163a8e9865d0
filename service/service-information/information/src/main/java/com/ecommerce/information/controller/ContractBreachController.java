package com.ecommerce.information.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.information.api.dto.contractbreach.*;
import com.ecommerce.information.service.IContractBreachService;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * @Created锛�Sun Nov 25 21:52:00 CST 2018
 * <AUTHOR>
 * @Version:2
 * @Description:null
*/

@RestController
@Tag(name = "ContractBreach", description = "违约接口服务")
@RequestMapping("/contractBreach")
public class ContractBreachController {

   @Autowired 
   private IContractBreachService iContractBreachService;

   @Operation(summary = "平台修改处理结果")
   @PostMapping(value="/updateCheckByPlatform")
   public void updateCheckByPlatform(@RequestBody ContractBreachRecordCheckUpdateDTO arg0)throws Exception{
      iContractBreachService.updateCheckByPlatform(arg0);

   }


   @Operation(summary = "修改违约记录")
   @PostMapping(value="/updateRecord")
   public void updateRecord(@RequestBody ContractBreachRecordUpdateDTO arg0)throws Exception{
      iContractBreachService.updateRecord(arg0);

   }


   @Operation(summary = "根据id查找违约规则")
   @PostMapping(value="/findConfigById")
   public ContractBreachConfigDTO findConfigById(@RequestParam String arg0)throws Exception{
      return iContractBreachService.findConfigById(arg0);
   }


   @Operation(summary = "违约规则翻页查询")
   @PostMapping(value="/findAllConfig")
   public PageInfo<ContractBreachConfigDTO> findAllConfig(@RequestBody ContractBreachConfigQueryDTO arg0)throws Exception{
      return iContractBreachService.findAllConfig(arg0);
   }


   @Operation(summary = "根据处理记录id查找单个处理")
   @PostMapping(value="/findCheckById")
   public ContractBreachRecordCheckDTO findCheckById(@RequestParam String arg0)throws Exception{
      return iContractBreachService.findCheckById(arg0);
   }


   @Operation(summary = "修改违约规则")
   @PostMapping(value="/updateConfig")
   public ContractBreachConfigDTO updateConfig(@RequestBody ContractBreachConfigUpdateDTO arg0)throws Exception{
      return iContractBreachService.updateConfig(arg0);
   }


   @Operation(summary = "添加违约记录")
   @PostMapping(value="/createRecord")
   public void createRecord(@RequestBody ContractBreachRecordCreateDTO arg0)throws Exception{
      iContractBreachService.createRecord(arg0);

   }


   @Operation(summary = "违约记录多条件翻页查询")
   @PostMapping(value="/findAllRecord")
   public PageInfo<ContractBreachRecordDTO> findAllRecord(@RequestBody ContractBreachRecordQueryDTO arg0)throws Exception{
      return iContractBreachService.findAllRecord(arg0);
   }


   @Operation(summary = "根据id删除处理结果")
   @PostMapping(value="/deleteCheckByIds")
   public void deleteCheckByIds(@RequestBody List<String> arg0,@RequestParam String arg1)throws Exception{
      iContractBreachService.deleteCheckByIds(arg0,arg1);

   }


   @Operation(summary = "翻页查询违约记录处理结果")
   @PostMapping(value="/findAllCheck")
   public PageInfo<ContractBreachRecordCheckDTO> findAllCheck(@RequestBody ContractBreachRecordCheckQueryDTO arg0)throws Exception{
      return iContractBreachService.findAllCheck(arg0);
   }


   @Operation(summary = "删除违约配置")
   @PostMapping(value="/deleteConfigById")
   public void deleteConfigById(@RequestParam String arg0,@RequestParam String arg1)throws Exception{
      iContractBreachService.deleteConfigById(arg0,arg1);

   }


   @Operation(summary = "删除违约记录")
   @PostMapping(value="/deleteRecordById")
   public void deleteRecordById(@RequestParam String arg0,@RequestParam String arg1)throws Exception{
      iContractBreachService.deleteRecordById(arg0,arg1);

   }


   @Operation(summary = "根据违约记录id和会员id查找违约记录")
   @PostMapping(value="/findRecordById")
   public ContractBreachRecordDTO findRecordById(@RequestParam String arg0)throws Exception{
      return iContractBreachService.findRecordById(arg0);
   }


   @Operation(summary = "根据违约记录id和会员id查找违约记录")
   @PostMapping(value="/findRecordByIdAndMemberId")
   public ContractBreachRecordDTO findRecordByIdAndMemberId(@RequestParam String arg0,@RequestParam String arg1)throws Exception{
      return iContractBreachService.findRecordByIdAndMemberId(arg0,arg1);
   }


   @Operation(summary = "发起申诉")
   @PostMapping(value="/addAppealReason")
   public void addAppealReason(@RequestBody ContractBreachRecordCheckAddDTO arg0)throws Exception{
      iContractBreachService.addAppealReason(arg0);

   }


   @Operation(summary = "创建违约规则")
   @PostMapping(value="/createConfig")
   public ContractBreachConfigDTO createConfig(@RequestBody ContractBreachConfigCreateDTO arg0)throws Exception{
      return iContractBreachService.createConfig(arg0);
   }


   @Operation(summary = "买家处理")
   @PostMapping(value="/addCheckByBuyer")
   public void addCheckByBuyer(@RequestBody ContractBreachRecordCheckAddDTO arg0)throws Exception{
      iContractBreachService.addCheckByBuyer(arg0);

   }


   @Operation(summary = "卖家处理")
   @PostMapping(value="/addCheckBySeller")
   public void addCheckBySeller(@RequestBody ContractBreachRecordCheckAddDTO arg0)throws Exception{
      iContractBreachService.addCheckBySeller(arg0);

   }


   @Operation(summary = "根据id和会员id批量删除违约规则")
   @PostMapping(value="/deleteConfigByIdsAndMemberId")
   public void deleteConfigByIdsAndMemberId(@RequestBody List<String> arg0,@RequestParam String arg1,@RequestParam String arg2)throws Exception{
      iContractBreachService.deleteConfigByIdsAndMemberId(arg0,arg1,arg2);

   }


   @Operation(summary = "批量删除违约配置")
   @PostMapping(value="/deleteRecordByIds")
   public void deleteRecordByIds(@RequestBody List<String> arg0,@RequestParam String arg1)throws Exception{
      iContractBreachService.deleteRecordByIds(arg0,arg1);

   }


   @Operation(summary = "卖家修改处理结果")
   @PostMapping(value="/updateCheckBySeller")
   public void updateCheckBySeller(@RequestBody ContractBreachRecordCheckUpdateDTO arg0)throws Exception{
      iContractBreachService.updateCheckBySeller(arg0);

   }


   @Operation(summary = "违约记录统计结果翻页查询")
   @PostMapping(value="/findAllRecordCount")
   public PageInfo<ContractBreachRecordCountDTO> findAllRecordCount(@RequestBody ContractBreachRecordCountQueryDTO arg0)throws Exception{
      return iContractBreachService.findAllRecordCount(arg0);
   }


   @Operation(summary = "根据会员id和会员id查找违约规则")
   @PostMapping(value="/findConfigByMemberId")
   public List<ContractBreachConfigDTO> findConfigByMemberId(@RequestParam String arg0)throws Exception{
      return iContractBreachService.findConfigByMemberId(arg0);
   }


   @Operation(summary = "取消违约")
   @PostMapping(value="/cancelContractBreak")
   public void cancelContractBreak(@RequestBody CancelDTO arg0)throws Exception{
      iContractBreachService.cancelContractBreak(arg0);

   }


   @Operation(summary = "平台处理")
   @PostMapping(value="/addCheckByPlatform")
   public void addCheckByPlatform(@RequestBody ContractBreachRecordCheckAddDTO arg0)throws Exception{
      iContractBreachService.addCheckByPlatform(arg0);

   }


   @Operation(summary = "买家修改处理结果")
   @PostMapping(value="/updateCheckByBuyer")
   public void updateCheckByBuyer(@RequestBody ContractBreachRecordCheckUpdateDTO arg0)throws Exception{
      iContractBreachService.updateCheckByBuyer(arg0);

   }


   @Operation(summary = "批量删除违约配置")
   @PostMapping(value="/deleteConfigByIds")
   public void deleteConfigByIds(@RequestBody List<String> arg0,@RequestParam String arg1)throws Exception{
      iContractBreachService.deleteConfigByIds(arg0,arg1);

   }


   @Operation(summary = "根据id和会员id删除违约规则")
   @PostMapping(value="/deleteConfigByIdAndMemberId")
   public void deleteConfigByIdAndMemberId(@RequestParam String arg0,@RequestParam String arg1,@RequestParam String arg2)throws Exception{
      iContractBreachService.deleteConfigByIdAndMemberId(arg0,arg1,arg2);

   }


   @Operation(summary = "根据违约记录查找处理结果")
   @PostMapping(value="/findCheckByRecordId")
   public List<ContractBreachRecordCheckDTO> findCheckByRecordId(@RequestParam String arg0)throws Exception{
      return iContractBreachService.findCheckByRecordId(arg0);
   }


   @Operation(summary = "根据违约记录删除处理结果")
   @PostMapping(value="/deleteCheckByRecordId")
   public void deleteCheckByRecordId(@RequestBody List<String> arg0,@RequestParam String arg1)throws Exception{
      iContractBreachService.deleteCheckByRecordId(arg0,arg1);

   }



}
