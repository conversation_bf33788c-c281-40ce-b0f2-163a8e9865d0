package com.ecommerce.information.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.information.api.dto.credit.CreditQuotaDTO;
import com.ecommerce.information.api.dto.credit.CreditQuotaDeleteDTO;
import com.ecommerce.information.api.dto.credit.CreditQuotaNodeDTO;
import com.ecommerce.information.api.dto.credit.CreditQuotaOptionSaveDTO;
import com.ecommerce.information.api.dto.credit.CreditQuotaQueryDTO;
import com.ecommerce.information.api.dto.credit.CreditQuotaSaveDTO;
import com.ecommerce.information.service.ICreditQuotaService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 采购招标公告服务
 * <AUTHOR>
 * Description: CreditQuotaController
 */
@RestController
@Tag(name = "CreditQuota", description = "信用指标服务")
@RequestMapping("/creditQuota")
public class CreditQuotaController {

    @Autowired
    private ICreditQuotaService creditQuotaService;

    @Operation(summary = "创建信用指标")
    @PostMapping(value="/creteCreditQuota")
    public ItemResult<String> creteCreditQuota(@RequestBody CreditQuotaSaveDTO creditQuotaSaveDTO) {
        return creditQuotaService.creteCreditQuota(creditQuotaSaveDTO);
    }

    @Operation(summary = "编辑信用指标")
    @PostMapping(value="/editCreditQuota")
    public ItemResult<Void> editCreditQuota(@RequestBody CreditQuotaSaveDTO creditQuotaSaveDTO) {
        return creditQuotaService.editCreditQuota(creditQuotaSaveDTO);
    }

    @Operation(summary = "删除信用指标")
    @PostMapping(value="/deleteCreditQuota")
    public ItemResult<Void> deleteCreditQuota(@RequestBody CreditQuotaDeleteDTO creditQuotaDeleteDTO) {
        return creditQuotaService.deleteCreditQuota(creditQuotaDeleteDTO);
    }

    @Operation(summary = "查询信用指标详情")
    @PostMapping(value="/queryCreditQuotaDetail")
    public ItemResult<CreditQuotaDTO> queryCreditQuotaDetail(@RequestParam String creditQuotaId) {
        return creditQuotaService.queryCreditQuotaDetail(creditQuotaId);
    }

    @Operation(summary = "保存信用指标选项")
    @PostMapping(value="/saveCreditQuotaOption")
    public ItemResult<Void> saveCreditQuotaOption(@RequestBody CreditQuotaOptionSaveDTO creditQuotaOptionSaveDTO) {
        return creditQuotaService.saveCreditQuotaOption(creditQuotaOptionSaveDTO);
    }

    @Operation(summary = "查询信用指标节点树列表")
    @PostMapping(value="/queryCreditQuotaNodeList")
    public ItemResult<List<CreditQuotaNodeDTO>> queryCreditQuotaNodeList(@RequestBody CreditQuotaQueryDTO creditQuotaQueryDTO) {
        return creditQuotaService.queryCreditQuotaNodeList(creditQuotaQueryDTO);
    }
}