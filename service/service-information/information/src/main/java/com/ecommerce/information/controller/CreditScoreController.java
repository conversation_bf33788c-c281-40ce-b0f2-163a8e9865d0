package com.ecommerce.information.controller;

import com.ecommerce.common.exception.BusinessOperationException;import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ecommerce.information.service.ICreditScoreService;
import com.ecommerce.common.result.ItemResult;
import java.util.List;
import com.ecommerce.information.api.dto.credit.CreditScoreUpdateDTO;
import com.ecommerce.information.api.dto.credit.CreditScoreSaveDTO;
import com.ecommerce.information.api.dto.bill.check.rule.PageQuery;
import java.lang.String;
import com.ecommerce.common.result.PageData;
import java.lang.Void;
import com.ecommerce.information.api.dto.credit.CreditQuotaDetailDTO;
import com.ecommerce.information.api.dto.credit.CreditScoreListDTO;
import com.ecommerce.information.api.dto.credit.CreditScoreResultDTO;
import com.ecommerce.information.api.dto.credit.CreditSourceQueryDTO;


/**
 * Author: colu Description: Date: Create in 上午10:34 21/7/6
 *
 * <AUTHOR>
*/

@RestController
@Tag(name = "CreditScore", description = "Author: colu Description: Date: Create in 上午10:34 21/7/6")
@RequestMapping("/creditScore")
public class CreditScoreController {

   @Autowired 
   private ICreditScoreService iCreditScoreService;

   @Operation(summary = "创建新的信用的分时,返回指标模板")
   @PostMapping(value="/fetchQuotaTemplate")
   public ItemResult<List<CreditQuotaDetailDTO>> fetchQuotaTemplate(@RequestBody CreditSourceQueryDTO creditSourceQueryDTO)throws Exception{
      return iCreditScoreService.fetchQuotaTemplate(creditSourceQueryDTO);
   }


   @Operation(summary = "修改状态")
   @PostMapping(value="/updateCreditStatus")
   public ItemResult<Void> updateCreditStatus(@RequestBody CreditScoreUpdateDTO creditScoreUpdateDTO)throws Exception{
      return iCreditScoreService.updateCreditStatus(creditScoreUpdateDTO);
   }


   @Operation(summary = "删除得分")
   @PostMapping(value="/deleteCreditScore")
   public ItemResult<Void> deleteCreditScore(@RequestBody CreditScoreUpdateDTO creditScoreUpdateDTO)throws Exception{
      return iCreditScoreService.deleteCreditScore(creditScoreUpdateDTO);
   }


   @Operation(summary = "主键查看积分详情")
   @PostMapping(value="/queryScoreResultById")
   public ItemResult<CreditScoreResultDTO> queryScoreResultById(@RequestParam String creditScoreId)throws Exception{
      return iCreditScoreService.queryScoreResultById(creditScoreId);
   }


   @Operation(summary = "保持新建的得分")
   @PostMapping(value="/saveCreditScore")
   public ItemResult<String> saveCreditScore(@RequestBody CreditScoreSaveDTO creditScoreSaveDTO)throws Exception{
      return iCreditScoreService.saveCreditScore(creditScoreSaveDTO);
   }


   @Operation(summary = "分页查询得分")
   @PostMapping(value="/pageQueryByCond")
   public ItemResult<PageData<CreditScoreListDTO>> pageQueryByCond(@RequestBody PageQuery<CreditSourceQueryDTO> pageQuery)throws Exception{
      return iCreditScoreService.pageQueryByCond(pageQuery);
   }


   @Operation(summary = "列表查询得分")
   @PostMapping(value="/listQueryByCond")
   public ItemResult<List<CreditScoreListDTO>> listQueryByCond(@RequestBody CreditSourceQueryDTO creditSourceQueryDTO)throws Exception{
      return iCreditScoreService.listQueryByCond(creditSourceQueryDTO);
   }



}
