package com.ecommerce.information.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.information.api.dto.favorite.*;
import com.ecommerce.information.service.IFavoriteCollectionService;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 收藏对外服务接口
 *
 * <AUTHOR>
*/

@RestController
@Tag(name = "FavoriteCollection", description = "收藏对外服务接口")
@RequestMapping("/favoriteCollection")
public class FavoriteCollectionController {

   @Autowired 
   private IFavoriteCollectionService iFavoriteCollectionService;

   @Operation(summary = "添加收藏")
   @PostMapping(value="/add")
   public void add(@RequestBody FavoriteCollectionAddDTO favoriteCollectionAddDTO)throws Exception{
      iFavoriteCollectionService.add(favoriteCollectionAddDTO);
   }

   @Operation(summary = "批量添加收藏")
   @PostMapping(value="/addList")
   public void addList(@RequestBody List<FavoriteCollectionAddDTO> favoriteCollectionAddDTOList)throws Exception{
      iFavoriteCollectionService.addList(favoriteCollectionAddDTOList);
   }

   @Operation(summary = "查询会员id是否有收藏该商品")
   @PostMapping(value="/favoriteGoodsExists")
   public boolean favoriteGoodsExists(@RequestParam String memberId,@RequestParam String objectId)throws Exception{
      return iFavoriteCollectionService.favoriteGoodsExists(memberId,objectId);
   }

   @Operation(summary = "查询会员id是否有收藏该店铺")
   @PostMapping(value="/favoriteShopExists")
   public boolean favoriteShopExists(@RequestParam String memberId,@RequestParam String objectId)throws Exception{
      return iFavoriteCollectionService.favoriteShopExists(memberId,objectId);
   }

   @Operation(summary = "查询会员id是否有收藏该资讯")
   @PostMapping(value="/favoriteInformationExists")
   public boolean favoriteInformationExists(@RequestParam String memberId,@RequestParam String objectId)throws Exception{
      return iFavoriteCollectionService.favoriteInformationExists(memberId,objectId);
   }


   @Operation(summary = "添加商品浏览记录")
   @PostMapping(value="/addBrowseRecord")
   public void addBrowseRecord(@RequestBody FavoriteCollectionAddBrowseRecordDTO favoriteCollectionAddBrowseRecordDTO)throws Exception{
      iFavoriteCollectionService.addBrowseRecord(favoriteCollectionAddBrowseRecordDTO);
   }
   @Operation(summary = "批量添加商品浏览记录")
   @PostMapping(value="/addBrowseRecordList")
   public void addBrowseRecordList(@RequestBody List<FavoriteCollectionAddBrowseRecordDTO> list)throws Exception{
      iFavoriteCollectionService.addBrowseRecordList(list);
   }

   @Operation(summary = "移除收藏(逻辑删除即可)")
   @PostMapping(value="/removeById")
   public void removeById(@RequestParam String id,@RequestParam String operator)throws Exception{
      iFavoriteCollectionService.removeById(id,operator);

   }

   @Operation(summary = "添加资讯收藏")
   @PostMapping(value="/addInformation")
   public void addInformation(@RequestBody FavoriteCollectionAddInformationDTO favoriteCollectionAddInformationDTO)throws Exception{
      iFavoriteCollectionService.addInformation(favoriteCollectionAddInformationDTO);

   }
   @Operation(summary = "批量添加资讯收藏")
   @PostMapping(value="/addInformationList")
   public void addInformationList(@RequestBody List<FavoriteCollectionAddInformationDTO> list)throws Exception{
      iFavoriteCollectionService.addInformationList(list);
   }


   @Operation(summary = "置顶")
   @PostMapping(value="/top")
   public void top(@RequestParam String id,@RequestParam String operator)throws Exception{
      iFavoriteCollectionService.top(id,operator);
   }

   @Operation(summary = "添加商品收藏")
   @PostMapping(value="/addGoods")
   public void addGoods(@RequestBody FavoriteCollectionAddGoodsDTO favoriteCollectionAddGoodsDTO)throws Exception{
      iFavoriteCollectionService.addGoods(favoriteCollectionAddGoodsDTO);
   }
   @Operation(summary = "批量添加商品收藏")
   @PostMapping(value="/addGoodsList")
   public void addGoodsList(@RequestBody List<FavoriteCollectionAddGoodsDTO> list)throws Exception{
      iFavoriteCollectionService.addGoodsList(list);
   }

   @Operation(summary = "查询会员id是否有收藏该对象")
   @PostMapping(value="/favoriteExists")
   public boolean favoriteExists(@RequestParam String memberId,@RequestParam Integer favoriteType,@RequestParam String objectId)throws Exception{
      return iFavoriteCollectionService.favoriteExists(memberId,favoriteType,objectId);
   }

   @Operation(summary = "添加店铺收藏")
   @PostMapping(value="/addShop")
   public void addShop(@RequestBody FavoriteCollectionAddShopDTO favoriteCollectionAddShopDTO)throws Exception{
      iFavoriteCollectionService.addShop(favoriteCollectionAddShopDTO);
   }
   @Operation(summary = "批量添加店铺收藏")
   @PostMapping(value="/addShopList")
   public void addShopList(@RequestBody List<FavoriteCollectionAddShopDTO> list)throws Exception{
      iFavoriteCollectionService.addShopList(list);
   }
   @Operation(summary = "移除收藏")
   @PostMapping(value="/removeByIds")
   public void removeByIds(@RequestBody List<String> ids,@RequestParam String operator)throws Exception{
      iFavoriteCollectionService.removeByIds(ids,operator);
   }

   @Operation(summary = "根据对象类型和id移除收藏")
   @PostMapping(value="/removeByObjectIds")
   public void removeByObjectIds(@RequestBody List<String> ids,@RequestParam Integer favoriteType,@RequestParam String memberId,@RequestParam String operator)throws Exception{
      iFavoriteCollectionService.removeByObjectIds(favoriteType,ids,memberId,operator);
   }

   @Operation(summary = "根据会员id和收藏类型查询其收藏")
   @PostMapping(value="/findByMemberId")
   public PageInfo<FavoriteCollectionDTO> findByMemberId(@RequestBody FavoriteCollectionQueryDTO query)throws Exception{
      return iFavoriteCollectionService.findByMemberId(query);
   }

   @Operation(summary = "根据主键ID查询收藏表")
   @PostMapping(value="/findFavoriteCollectionById")
   public FavoriteCollectionDTO findFavoriteCollectionById(@RequestParam String id)throws Exception{
      return iFavoriteCollectionService.findFavoriteCollectionDTOByID(id);
   }

   @Operation(summary = "根据收藏类型和对象ID查询收藏表")
   @PostMapping(value="/findFavoriteCollectionByFavoriteTypeAndObjectId")
   public List<FavoriteCollectionDTO> findFavoriteCollectionByFavoriteTypeAndObjectId(@RequestBody List<String> ids,@RequestParam Integer favoriteType,@RequestParam String memberId)throws Exception{
      return iFavoriteCollectionService.findFavoriteCollectionByFavoriteTypeAndObjectId(favoriteType,ids,memberId);
   }
}
