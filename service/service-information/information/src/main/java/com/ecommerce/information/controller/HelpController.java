package com.ecommerce.information.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.information.api.dto.help.*;
import com.ecommerce.information.service.IHelpService;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 帮助中心服务
 *
 * <AUTHOR>
*/

@RestController
@Tag(name = "Help", description = "帮助中心服务")
@RequestMapping("/help")
public class HelpController {

   @Autowired 
   private IHelpService iHelpService;

   @Operation(summary = "查找整颗树(去掉根节点后的两层数据，按树形结构组装数据)")
   @PostMapping(value="/findAllCategoryToTreeAndRemoveRoot")
   public List<HelpCategoryDTO> findAllCategoryToTreeAndRemoveRoot(){
      return iHelpService.findAllCategoryToTreeAndRemoveRoot();
   }


   @Operation(summary = "null")
   @PostMapping(value="/findByCategory")
   public PageInfo<HelpQuestionDTO> findByCategory(@RequestBody HelpQueryByCategoryDTO arg0){
      return iHelpService.findByCategory(arg0);
   }


   @Operation(summary = "添加分类")
   @PostMapping(value="/createHelpCategory")
   public HelpCategoryDTO createHelpCategory(@RequestBody HelpCategoryCreateDTO arg0){
      return iHelpService.createHelpCategory(arg0);
   }


   @Operation(summary = "删除分类")
   @PostMapping(value="/deleteHelpCategory")
   public void deleteHelpCategory(@RequestParam String arg0,@RequestParam String arg1){
      iHelpService.deleteHelpCategory(arg0,arg1);

   }


   @Operation(summary = "修改分类")
   @PostMapping(value="/updateHelpCategory")
   public void updateHelpCategory(@RequestBody HelpCategoryUpdateDTO arg0){
      iHelpService.updateHelpCategory(arg0);

   }


   @Operation(summary = "禁用")
   @PostMapping(value="/disableHelpCategory")
   public void disableHelpCategory(@RequestParam String arg0,@RequestParam String arg1){
      iHelpService.disableHelpCategory(arg0,arg1);

   }


   @Operation(summary = "启用")
   @PostMapping(value="/enableHelpCategory")
   public void enableHelpCategory(@RequestParam String arg0,@RequestParam String arg1){
      iHelpService.enableHelpCategory(arg0,arg1);

   }


   @Operation(summary = "删除一个问题")
   @PostMapping(value="/deleteHelpQuestion")
   public void deleteHelpQuestion(@RequestParam String arg0,@RequestParam String arg1){
      iHelpService.deleteHelpQuestion(arg0,arg1);

   }


   @Operation(summary = "修改一个问题")
   @PostMapping(value="/updateHelpQuestion")
   public void updateHelpQuestion(@RequestBody HelpQuestionUpdateDTO arg0){
      iHelpService.updateHelpQuestion(arg0);

   }


   @Operation(summary = "根据条件翻页查询")
   @PostMapping(value="/findAllHelpQuestion")
   public PageInfo<HelpQuestionDTO> findAllHelpQuestion(@RequestBody HelpQueryDTO arg0){
      return iHelpService.findAllHelpQuestion(arg0);
   }


   @Operation(summary = "添加一个问题")
   @PostMapping(value="/createHelpQuestion")
   public HelpQuestionDTO createHelpQuestion(@RequestBody HelpQuestionCreateDTO arg0){
      return iHelpService.createHelpQuestion(arg0);
   }


   @Operation(summary = "根据id查询单个帮助")
   @PostMapping(value="/findHelpQuestionById")
   public HelpQuestionDTO findHelpQuestionById(@RequestParam String arg0){
      return iHelpService.findHelpQuestionById(arg0);
   }


   @Operation(summary = "按条件分页查询")
   @PostMapping(value="/findAllHelpCategory")
   public PageInfo<HelpCategoryDTO> findAllHelpCategory(@RequestBody HelpCategoryQueryDTO arg0){
      return iHelpService.findAllHelpCategory(arg0);
   }


   @Operation(summary = "根据分类id查找单个分类")
   @PostMapping(value="/findHelpCategoryById")
   public HelpCategoryDTO findHelpCategoryById(@RequestParam String arg0){
      return iHelpService.findHelpCategoryById(arg0);
   }
   @Operation(summary = "根据分类名称查找单个分类")
   @PostMapping(value="/findHelpCategoryByName")
   public HelpCategoryDTO findHelpCategoryByName(@RequestParam String categoryName){
      return iHelpService.findHelpCategoryByName(categoryName);
   }
   @Operation(summary = "查找分类名称是否存在")
   @PostMapping(value="/existsHelpCategoryByName")
   public Boolean existsHelpCategoryByName(@RequestParam String categoryName,@RequestParam(required = false) String id){
      return iHelpService.existsHelpCategoryByName(categoryName,id);
   }

   @Operation(summary = "反馈问题是否解决")
   @PostMapping(value="/feedbackHelpQuestion")
   public Boolean feedbackHelpQuestion(@RequestParam Boolean solved,@RequestParam String helpQuestionId,@RequestParam String operator){
      return iHelpService.feedbackHelpQuestion(solved, helpQuestionId,operator);
   }

   @Operation(summary = "按分类名称查找问题")
   @PostMapping(value="/findQuestionByCategoryName")
   public List<HelpQuestionDTO> findQuestionByCategoryName(@RequestParam String categoryName){
      return iHelpService.findQuestionByCategoryName(categoryName);
   }

   @Operation(summary = "根据问题code查找问题")
   @PostMapping(value="/findHelpQuestionByQuestionCode")
   public HelpQuestionDTO  findHelpQuestionByQuestionCode(@RequestParam("questionCode)")String questionCode){
      return iHelpService.findHelpQuestionByQuestionCode(questionCode);
   }

   @Operation(summary = "根据问题分类code查找分类")
   @PostMapping(value="/findHelpCategoryByCode")
   public HelpCategoryDTO findHelpCategoryByCode(@RequestParam String categoryCode){
      return iHelpService.findHelpCategoryByCode(categoryCode);
   }

   @Operation(summary = "分类code是否存在")
   @PostMapping(value="/existsHelpCategoryByCode")
   public Boolean existsHelpCategoryByCode(@RequestParam String code,@RequestParam(required = false) String id){
      return iHelpService.existsHelpCategoryByCode(code,id);
   }

   @Operation(summary = "问题code是否存在")
   @PostMapping(value="/existsHelpQuestionByCode")
   public Boolean existsHelpQuestionByCode(@RequestParam String code,@RequestParam(required = false) String id){
      return iHelpService.existsHelpQuestionByCode(code,id);
   }

}
