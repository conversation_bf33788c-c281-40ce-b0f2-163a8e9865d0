package com.ecommerce.information.controller;

import com.ecommerce.information.api.dto.indexPage.*;
import com.ecommerce.information.service.IIndexPageInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <AUTHOR>
 * @created 16:34 23/01/2019
 * @description TODO
 */
@RestController
@Tag(name = "IndexPageInfo", description = "首页服务")
@RequestMapping("/indexPageInfo")
public class IndexPageInfoController {

    @Autowired
    private IIndexPageInfoService indexPageInfoService;

    /**
     * 按照类型查找
     * @return
     */
    @Operation(summary = "按照类型查找")
    @PostMapping("/findByDisplayType")
    public List<IndexPageInfoDTO> findByDisplayType(@RequestParam String displayType) {
        return indexPageInfoService.findByDisplayType(displayType);
    }

    /**
     * 调整显示顺序
     * @param orderList 数据实体列表
     * @param displayType 显示类型
     * @param operatorId 操作者
     */
    @Operation(summary = "调整显示顺序")
    @PostMapping("/updateOrder")
    public void updateOrder(@RequestBody List<IndexPageInfoOrder> orderList, @RequestParam String displayType, @RequestParam String operatorId) {
        indexPageInfoService.updateOrder(orderList, displayType, operatorId);
    }

    /**
     * 保存
     * @param indexPageInfoDTO 数据实体
     * @param operatorId 操作者
     */
    @Operation(summary = "保存")
    @PostMapping("/save")
    public void save(@RequestBody IndexPageInfoDTO indexPageInfoDTO, @RequestParam String operatorId) {
        indexPageInfoService.save(indexPageInfoDTO, operatorId);
    }

    /**
     * 标记删除
     * @param indexPageInfoId 主键
     * @param displayType 显示类型
     * @param operatorId 操作者
     */
    @Operation(summary = "标记删除")
    @PostMapping("/deleteById")
    public void deleteById(@RequestParam String indexPageInfoId, @RequestParam String displayType, @RequestParam String operatorId) {
        indexPageInfoService.deleteById(indexPageInfoId, displayType, operatorId);
    }

    /**
     * 批量保存
     * @param indexPageInfoDTOList 实体列表
     * @param displayType 显示类型
     * @param operatorId 操作者
     */
    @Operation(summary = "批量保存")
    @PostMapping("/batchSave")
    public void batchSave(@RequestBody List<IndexPageInfoDTO> indexPageInfoDTOList, @RequestParam String displayType, @RequestParam String operatorId) {
        indexPageInfoService.batchSave(indexPageInfoDTOList, displayType, operatorId);
    }


}
