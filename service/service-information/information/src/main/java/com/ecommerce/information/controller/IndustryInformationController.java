package com.ecommerce.information.controller;

import com.ecommerce.common.exception.BusinessOperationException;import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ecommerce.information.service.IIndustryInformationService;
import com.ecommerce.information.api.dto.IndustryInformationDTO;
import com.github.pagehelper.PageInfo;
import java.lang.String;


/**
 * null
 *
 * <AUTHOR>
*/

@RestController
@Tag(name = "IndustryInformation", description = "null")
@RequestMapping("/industryInformation")
public class IndustryInformationController {

   @Autowired 
   private IIndustryInformationService iIndustryInformationService;

   @Operation(summary = "删除行业资讯")
   @PostMapping(value="/deleteIndustryInformationById")
   public Boolean deleteIndustryInformationById(@RequestParam String id)throws Exception{
      return iIndustryInformationService.deleteIndustryInformationById(id);
   }


   @Operation(summary = "添加行业资讯")
   @PostMapping(value="/addIndustryInformation")
   public Boolean addIndustryInformation(@RequestBody IndustryInformationDTO industryInformationDTO)throws Exception{
      return iIndustryInformationService.addIndustryInformation(industryInformationDTO);
   }


   @Operation(summary = "修改行业资讯")
   @PostMapping(value="/updateIndustryInformation")
   public Boolean updateIndustryInformation(@RequestBody IndustryInformationDTO industryInformationDTO)throws Exception{
      return iIndustryInformationService.updateIndustryInformation(industryInformationDTO);
   }


   @Operation(summary = "条件查询行业资讯列表")
   @PostMapping(value="/selectIndustryInformationList")
   public PageInfo<IndustryInformationDTO> selectIndustryInformationList(@RequestBody IndustryInformationDTO industryInformationDTO)throws Exception{
      return iIndustryInformationService.selectIndustryInformationList(industryInformationDTO);
   }


   @Operation(summary = "查询行业资讯详情")
   @PostMapping(value="/selectIndustryInformationById")
   public IndustryInformationDTO selectIndustryInformationById(@RequestParam String id)throws Exception{
      return iIndustryInformationService.selectIndustryInformationById(id);
   }



}
