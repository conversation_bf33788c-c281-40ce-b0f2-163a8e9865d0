package com.ecommerce.information.controller;

import com.ecommerce.information.api.dto.internalmessage.*;
import com.ecommerce.information.service.IInternalMessageService;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


/**
 * @Created锛�Fri Sep 28 11:47:39 CST 2018
 * <AUTHOR>
 * @Version:2
 * @Description:站内信服务
*/

@RestController
@Tag(name = "InternalMessage", description = "站内信服务")
public class InternalMessageController implements com.ecommerce.information.api.service.IInternalMessageService {

   @Autowired 
   private IInternalMessageService internalMessageService;

   @Override
   public Boolean sendMessage(@RequestBody InternalMessageCreateDTO internalMessageCreateDTO) {
      return internalMessageService.sendMessage(internalMessageCreateDTO);
   }

   @Override
   public Boolean delete(@RequestBody InternalMessageDeleteDTO internalMessageDeleteDTO) {
      return internalMessageService.delete(internalMessageDeleteDTO);
   }

   @Override
   public Boolean updateReadStatus(@RequestBody InternalMessageReadStatusUpdateDTO internalMessageReadStatusUpdateDTO) {
      return internalMessageService.updateReadStatus(internalMessageReadStatusUpdateDTO);
   }

   @Override
   public PageInfo<InternalMessageDTO> findAll(@RequestBody InternalMessageQueryDTO internalMessageQueryDTO) {
      return internalMessageService.findAll(internalMessageQueryDTO);
   }

   @Override
   public Integer count(@RequestParam String accountId) {
      return internalMessageService.count(accountId);
   }

   @Override
   public InternalMessageDTO findById(@RequestParam String id) {
      return internalMessageService.findById(id);
   }
}
