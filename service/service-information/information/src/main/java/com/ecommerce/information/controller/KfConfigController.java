package com.ecommerce.information.controller;

import com.ecommerce.common.exception.BusinessOperationException;import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ecommerce.information.service.IKfConfigService;
import com.ecommerce.common.result.ItemResult;
import java.lang.Void;
import com.ecommerce.information.api.dto.kf.KfConfigCondDTO;
import java.lang.String;


/**
 * 客服配置信息服务
 *
 * <AUTHOR>
*/

@RestController
@Tag(name = "KfConfig", description = "客服配置信息服务")
@RequestMapping("/kfConfig")
public class KfConfigController {

   @Autowired 
   private IKfConfigService iKfConfigService;

   @Operation(summary = "刷新缓存")
   @PostMapping(value="/refreshCache")
   public ItemResult<Void> refreshCache()throws Exception{
      return iKfConfigService.refreshCache();
   }


   @Operation(summary = "获取客服url")
   @PostMapping(value="/getKfUrl")
   public ItemResult<String> getKfUrl(@RequestBody KfConfigCondDTO kfConfigCondDTO)throws Exception{
      return iKfConfigService.getKfUrl(kfConfigCondDTO);
   }



}
