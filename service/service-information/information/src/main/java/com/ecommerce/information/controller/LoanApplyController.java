package com.ecommerce.information.controller;

import com.ecommerce.information.api.dto.loan.LoanApplyDTO;
import com.ecommerce.information.api.dto.loan.LoanApplyQueryDTO;
import com.ecommerce.information.loan.service.ILoanApplyService;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@RestController
@Tag(name = "LoanApply", description = "金融留资申请")
@RequestMapping("/loanApply")
public class LoanApplyController
{
    @Autowired
    private ILoanApplyService loanApplyService;

    @Operation(summary = "留资申请")
    @PostMapping("/addApply")
    public boolean addApply(@Parameter(name = "dto", description = "申请数据") @RequestBody LoanApplyDTO dto)
    {
        return loanApplyService.addLoanApplyInfo(dto);
    }

    @Operation(summary = "留资列表 有分页")
    @PostMapping("/getLoanList")
    public PageInfo<LoanApplyDTO> getLoanList(@Parameter(name = "query", description = "查询条件") @RequestBody LoanApplyQueryDTO query)
    {
        return loanApplyService.getLoanApplyListByQuery(query);
    }

    @Operation(summary = "留资详情")
    @PostMapping("/getApplyInfo")
    public LoanApplyDTO getApplyInfo(@Parameter(name = "id", description = "用户申请ID") @RequestParam String id)
    {
        return loanApplyService.getLoanApplyInfoById(id);
    }

    @Operation(summary = "更新留资详情")
    @PostMapping("/updateApply")
    public boolean updateApply(@Parameter(name = "dto", description = "留资详情数据") @RequestBody LoanApplyDTO dto, @Parameter(name = "operator", description = "操作人") @RequestParam String operator)
    {
        return loanApplyService.updateLoanApplyInfoById(dto, operator);
    }
}
