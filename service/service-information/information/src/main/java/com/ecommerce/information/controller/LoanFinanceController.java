package com.ecommerce.information.controller;

import com.ecommerce.information.api.dto.loan.LoanFinanceDTO;
import com.ecommerce.information.api.dto.loan.LoanFinanceQueryDTO;
import com.ecommerce.information.loan.service.ILoanFinanceService;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@RestController
@Tag(name = "LoanFinance", description = "金融留资产品")
@RequestMapping("/loanFinance")
public class LoanFinanceController
{
    @Autowired
    private ILoanFinanceService loanFinanceService;

    @Operation(summary = "留资金融产品详情")
    @GetMapping("/getFinanceInfo")
    public LoanFinanceDTO getFinanceInfo(@Parameter(name = "id", description = "金融产品ID") @RequestParam String id)
    {
        return loanFinanceService.getLoanFinanceInfoById(id);
    }

    @Operation(summary = "留资金融产品列表 有分页")
    @PostMapping("/getFinanceListByQuery")
    public PageInfo<LoanFinanceDTO> getFinanceListByQuery(@RequestBody LoanFinanceQueryDTO query)
    {
        return loanFinanceService.getLoanFinanceListByQuery(query);
    }

    @Operation(summary = "留资金融产品列表")
    @GetMapping("/getFinanceList")
    public List<LoanFinanceDTO> getFinanceList(@RequestParam String memberId)
    {
        return loanFinanceService.getLoanFinanceList(memberId);
    }
}
