package com.ecommerce.information.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.information.api.dto.loan.LoanMemberAddDTO;
import com.ecommerce.information.api.dto.loan.LoanMemberCheckDTO;
import com.ecommerce.information.api.dto.loan.LoanMemberDTO;
import com.ecommerce.information.api.dto.loan.LoanMemberQueryDTO;
import com.ecommerce.information.loan.service.ILoanMemberService;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@RestController
@Tag(name = "LoanMember", description = "金融留资会员")
@RequestMapping(value = "/loanMember")
public class LoanMemberController
{
    @Autowired
    private ILoanMemberService loanMemberService;

    @Operation(summary = "检查用户是否有查看留资金融产品的权限")
    @PostMapping("/checkPermission")
    public ItemResult<Boolean> checkPermission(@RequestBody LoanMemberCheckDTO query)
    {
        boolean result = loanMemberService.checkMemberPermission(query.getMemberId(), query.getFinanceId());
        return new ItemResult<>(result);
    }

    @Operation(summary = "添加可以看见的留资的会员")
    @PostMapping("/addMember")
    public ItemResult<Boolean> addMember(@RequestBody LoanMemberAddDTO dto)
    {
        boolean result = loanMemberService.addLoanMemberList(dto.getMemberIds(), dto.getOperator());
        return new ItemResult<>(result);
    }

    @Operation(summary = "可以看见的留资的会员列表")
    @PostMapping("/getMemberList")
    public PageInfo<LoanMemberDTO> getMemberList(@Parameter(name = "query", description = "查询条件") @RequestBody LoanMemberQueryDTO query)
    {
        return loanMemberService.getLoanMemberListByQuery(query);
    }

    @Operation(summary = "删除可以看见的留资的会员")
    @PostMapping("/delMember")
    public Boolean delMember(@RequestParam String id, @RequestParam String operator)
    {
        LoanMemberDTO memberDTO = new LoanMemberDTO();
        memberDTO.setId(id);
        memberDTO.setDelFlg(1);
        loanMemberService.updateLoanMemberInfoById(memberDTO, operator);
        return true;
    }
}
