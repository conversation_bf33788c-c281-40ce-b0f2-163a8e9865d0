package com.ecommerce.information.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.information.api.dto.CategoryTreeDTO;
import com.ecommerce.information.api.dto.memberIntegral.*;
import com.ecommerce.information.api.dto.memberIntegralConfig.MemberIntegralConfigDTO;
import com.ecommerce.information.service.IMemberIntegralService;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * @Created锛�Fri Mar 01 14:07:57 CST 2019
 * <AUTHOR>
 * @Version:2
 * @Description:null
*/

@RestController
@Tag(name = "MemberIntegral", description = "会员积分服务")
@RequestMapping("/memberIntegral")
public class MemberIntegralController {

   @Autowired 
   private IMemberIntegralService iMemberIntegralService;



   @Operation(summary = "查询会员积分等级信息")
   @PostMapping(value="/findByMemberId")
   public MemberIntegralDTO findByMemberId(@RequestParam String memberId){
      return iMemberIntegralService.findByMemberId(memberId);
   }


   @Operation(summary = "根据条件翻页查询积分信息")
   @PostMapping(value="/findAllIntegral")
   public PageInfo<MemberIntegralDTO> findAllIntegral(@RequestBody IntegralQueryDTO dto){
      return iMemberIntegralService.findAllIntegral(dto);
   }


   @Operation(summary = "批量查询会员积分等级信息")
   @PostMapping(value="/findByMemberIds")
   public List<MemberIntegralDTO> findByMemberIds(@RequestBody List<String> memberIds){
      return iMemberIntegralService.findByMemberIds(memberIds);
   }


   @Operation(summary = "查询会员积分等级信息")
   @PostMapping(value="/findByMemberCode")
   public MemberIntegralDTO findByMemberCode(@RequestParam String memberCode){
      return iMemberIntegralService.findByMemberCode(memberCode);
   }


   @Operation(summary = "刷新积分 获取过去一年积分,然后更新进")
   @PostMapping(value="/recalculateIntegral")
   public Boolean recalculateIntegral(@RequestBody RecalculateIntegralDTO dto){
      return iMemberIntegralService.recalculateIntegral(dto);
   }


   @Operation(summary = "批量查询会员积分等级信息")
   @PostMapping(value="/findByMemberCodes")
   public List<MemberIntegralDTO> findByMemberCodes(@RequestBody List<String> memberCodes){
      return iMemberIntegralService.findByMemberCodes(memberCodes);
   }


   @Operation(summary = "根据条件翻页查询积分变更记录")
   @PostMapping(value="/findAllChangeInfo")
   public PageInfo<MemberIntegralChangeInfoDTO> findAllChangeInfo(@RequestBody IntegralChangeInfoQueryDTO dto){
      return iMemberIntegralService.findAllChangeInfo(dto);
   }


   @Operation(summary = "删除会员积分记录")
   @PostMapping(value="/deleteIntegralChangeInfo")
   public void deleteIntegralChangeInfo(@RequestBody DeleteIntegralChangeInfoDTO dto){
      iMemberIntegralService.deleteIntegralChangeInfo(dto);

   }


   @Operation(summary = "查询2张积分表的配置详情")
   @PostMapping(value="/findMemberIntegralConfig")
   public MemberIntegralConfigDTO findMemberIntegralConfig(@RequestBody List<CategoryTreeDTO> goods){
      return iMemberIntegralService.findMemberIntegralConfig(goods);
   }


   @Operation(summary = "更新积分表的配置")
   @PostMapping(value="/updateMemberIntegralConfig")
   public Boolean updateMemberIntegralConfig(@RequestBody MemberIntegralConfigDTO config){
      return iMemberIntegralService.updateMemberIntegralConfig(config);
   }



}
