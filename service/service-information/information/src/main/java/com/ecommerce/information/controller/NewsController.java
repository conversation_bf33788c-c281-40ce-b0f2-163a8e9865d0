package com.ecommerce.information.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.information.api.dto.ApproveDTO;
import com.ecommerce.information.api.dto.news.*;
import com.ecommerce.information.service.INewsService;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * @Created锛�Sat Nov 17 14:02:09 CST 2018
 * <AUTHOR>
 * @Version:2
 * @Description:null
*/

@RestController
@Tag(name = "News", description = "新闻相关接口")
@RequestMapping("/news")
public class NewsController {

   @Autowired 
   private INewsService newsService;

   @Operation(summary = "更新新闻")
   @PostMapping(value="/update")
   public NewsDTO update(@RequestBody NewsUpdateDTO dto)throws Exception{
      return newsService.update(dto);
   }


   @Operation(summary = "新增新闻")
   @PostMapping(value="/create")
   public NewsDTO create(@RequestBody NewsCreateDTO dto)throws Exception{
      return newsService.create(dto);
   }


   @Operation(summary = "根据分类查询")
   @PostMapping(value="/findByCategory")
   public List<NewsDTO> findByCategory(@RequestBody List<String> dto)throws Exception{
      return newsService.findByCategory(dto);
   }


   @Operation(summary = "顶　＋１")
   @PostMapping(value="/top")
   public void top(@RequestParam String newsId,@RequestParam String operator)throws Exception{
      newsService.top(newsId,operator);

   }


   @Operation(summary = "清除缓存数据")
   @PostMapping(value="/clearCache")
   public void clearCache(@RequestParam String operator)throws Exception{
      newsService.clearCache(operator);

   }


   @Operation(summary = "踩　＋１")
   @PostMapping(value="/stepOn")
   public void stepOn(@RequestParam String newsId,@RequestParam String operator)throws Exception{
      newsService.stepOn(newsId,operator);

   }


   @Operation(summary = "根据id批量查询")
   @PostMapping(value="/findByIds")
   public List<NewsDTO> findByIds(@RequestBody List<String> idList)throws Exception{
      return newsService.findByIds(idList);
   }


   @Operation(summary = "点击量　＋１")
   @PostMapping(value="/addHits")
   public void addHits(@RequestParam String newsId,@RequestParam String operator)throws Exception{
      newsService.addHits(newsId,operator);

   }


   @Operation(summary = "添加新闻评论")
   @PostMapping(value="/addComment")
   public CommentDTO addComment(@RequestBody CommentAddDTO dto)throws Exception{
      return newsService.addComment(dto);
   }


   @Operation(summary = "修改评论")
   @PostMapping(value="/modifyComment")
   public CommentDTO modifyComment(@RequestBody CommentUpdateDTO dto)throws Exception{
      return newsService.modifyComment(dto);
   }


   @Operation(summary = "根据id删除")
   @PostMapping(value="/deleteById")
   public void deleteById(@RequestParam String newsId,@RequestParam String operator)throws Exception{
      newsService.deleteById(newsId,operator);

   }


   @Operation(summary = "根据id批量删除")
   @PostMapping(value="/deleteByIds")
   public void deleteByIds(@RequestBody List<String> idList,@RequestParam String operator)throws Exception{
      newsService.deleteByIds(idList,operator);

   }


   @Operation(summary = "根据id查询")
   @PostMapping(value="/findById")
   public NewsDTO findById(@RequestParam String newsId)throws Exception{
      return newsService.findById(newsId);
   }


   @Operation(summary = "翻页查询")
   @PostMapping(value="/findAll")
   public PageInfo<NewsDTO> findAll(@RequestBody NewsQueryDTO query)throws Exception{
      return newsService.findAll(query);
   }


   @Operation(summary = "根据评论id删除评论")
   @PostMapping(value="/deleteCommentById")
   public void deleteCommentById(@RequestParam String commentId,@RequestParam String operator)throws Exception{
      newsService.deleteCommentById(commentId,operator);

   }


   @Operation(summary = "统计评论的回复评论")
   @PostMapping(value="/countReplyCommentByCommentId")
   public Long countReplyCommentByCommentId(@RequestParam String commentId)throws Exception{
      return newsService.countReplyCommentByCommentId(commentId);
   }


   @Operation(summary = "统计新闻的评论数")
   @PostMapping(value="/countCommentByNewsId")
   public Long countCommentByNewsId(@RequestParam String newsId)throws Exception{
      return newsService.countCommentByNewsId(newsId);
   }


   @Operation(summary = "批量删除评论")
   @PostMapping(value="/deleteCommentByIds")
   public void deleteCommentByIds(@RequestBody List<String> commentIdList,@RequestParam String operator)throws Exception{
      newsService.deleteCommentByIds(commentIdList,operator);

   }


   @Operation(summary = "删除新闻的所有评论")
   @PostMapping(value="/deleteCommentByNewsId")
   public void deleteCommentByNewsId(@RequestParam String newsId,@RequestParam String operator)throws Exception{
      newsService.deleteCommentByNewsId(newsId,operator);
   }

   @Operation(summary = "审批通过")
   @PostMapping(value="/approved")
   public void approved(@RequestBody ApproveDTO dto)throws Exception{
      newsService.approved(dto);
   }

   @Operation(summary = "审批拒绝通过")
   @PostMapping(value="/rejected")
   public void rejected(@RequestBody ApproveDTO dto)throws Exception{
      newsService.rejected(dto);
   }

   @Operation(summary = "查询审批历史")
   @PostMapping(value="/findApproveListById")
   public List<ApproveDTO> findApproveListById(@RequestParam String newsId)throws Exception{
      return newsService.findApproveListById(newsId);
   }


   @Operation(summary = "根据评论id查找单条评论")
   @PostMapping(value="/findCommentByCommentId")
   public CommentDTO findCommentByCommentId(@RequestParam String commentId)throws Exception{
      return newsService.findCommentByCommentId(commentId);
   }


   @Operation(summary = "查找当前评论的回复评论")
   @PostMapping(value="/findReplyCommentByCommentId")
   public List<CommentDTO> findReplyCommentByCommentId(@RequestParam String commentId)throws Exception{
      return newsService.findReplyCommentByCommentId(commentId);
   }


   @Operation(summary = "查找新闻的所有评论")
   @PostMapping(value="/findCommentByNewsId")
   public List<CommentDTO> findCommentByNewsId(@RequestParam String newsId,@RequestParam Integer pageSize,@RequestParam Integer pageNumber)throws Exception{
      return newsService.findCommentByNewsId(newsId,pageSize,pageNumber);
   }

   @Operation(summary = "emall查询-最新新闻")
   @PostMapping(value="/latestNews")
   public List<NewsDTO> latestNews(@RequestParam Integer size)throws Exception{
      return newsService.latestNews(size);
   }
   @Operation(summary = "emall查询-全国新闻(INFO_TYPE_NATIONAL)")
   @PostMapping(value="/nationalNews")
   public List<NewsDTO> nationalNews(@RequestParam Integer size)throws Exception{
      return newsService.nationalNews(size);
   }
   @Operation(summary = "emall查询-企业新闻(INFO_TYPE_CORPORATE)")
   @PostMapping(value="/corporateNews")
   public List<NewsDTO> corporateNews(@RequestParam Integer size)throws Exception{
      return newsService.corporateNews(size);
   }
   @Operation(summary = "emall查询-政策法规(INFO_TYPE_POLICIES_AND_REGULATIONS)")
   @PostMapping(value="/policiesAndRegulations")
   public List<NewsDTO> policiesAndRegulations(@RequestParam Integer size)throws Exception{
      return newsService.policiesAndRegulations(size);
   }
   @Operation(summary = "emall查询-行情动态(INFO_TYPE_MARKET_DYNAMICS)")
   @PostMapping(value="/marketDynamics")
   public List<NewsDTO> marketDynamics(@RequestParam Integer size)throws Exception{
      return newsService.marketDynamics(size);
   }
   @Operation(summary = "emall查询-技术装备-热门产品的新闻(INFO_TYPE_HOT_PRODUCT)")
   @PostMapping(value="/hotProductNews")
   public List<NewsDTO> hotProductNews(@RequestParam Integer size)throws Exception{
      return newsService.hotProductNews(size);
   }
   @Operation(summary = "emall查询-技术装备-最新技术新闻(INFO_TYPE_TECHNOLOGY_PROJECT)")
   @PostMapping(value="/technologyProjectNews")
   public List<NewsDTO> technologyProjectNews(@RequestParam Integer size)throws Exception{
      return newsService.technologyProjectNews(size);
   }

   @Operation(summary = "emall查询-装配式建筑-投资开发(INFO_TYPE_INVEST)")
   @PostMapping(value="/investProjectNews")
   public List<NewsDTO> investProjectNews(@RequestParam Integer size)throws Exception{
      return newsService.investProjectNews(size);
   }
   @Operation(summary = "emall查询-装配式建筑-工业化生产(INFO_TYPE_PROD)")
   @PostMapping(value="/prodProjectNews")
   public List<NewsDTO> prodProjectNews(@RequestParam Integer size)throws Exception{
      return newsService.prodProjectNews(size);
   }
   @Operation(summary = "emall查询-装配式建筑-标准化设计(INFO_TYPE_DESIGN)")
   @PostMapping(value="/designProjectNews")
   public List<NewsDTO> designProjectNews(@RequestParam Integer size)throws Exception{
      return newsService.designProjectNews(size);
   }
   @Operation(summary = "emall查询-装配式建筑-装配化施工(INFO_TYPE_BUILD)")
   @PostMapping(value="/buildProjectNews")
   public List<NewsDTO> buildProjectNews(@RequestParam Integer size)throws Exception{
      return newsService.buildProjectNews(size);
   }
   @Operation(summary = "emall查询-装配式建筑-一体化装修(INFO_TYPE_DECORATE)")
   @PostMapping(value="/decorateProjectNews")
   public List<NewsDTO> decorateProjectNews(@RequestParam Integer size)throws Exception{
      return newsService.decorateProjectNews(size);
   }
   @Operation(summary = "emall查询-装配式建筑-信息化管理(INFO_TYPE_MANAGE)")
   @PostMapping(value="/manageProjectNews")
   public List<NewsDTO> manageProjectNews(@RequestParam Integer size)throws Exception{
      return newsService.manageProjectNews(size);
   }
   @Operation(summary = "emall查询-装配式建筑-智能化应用(INFO_TYPE_APPLICATION)")
   @PostMapping(value="/applicationProjectNews")
   public List<NewsDTO> applicationProjectNews(@RequestParam Integer size)throws Exception{
      return newsService.applicationProjectNews(size);
   }
   @Operation(summary = "emall查询-装配式建筑-项目展示(INFO_TYPE_SHOW)")
   @PostMapping(value="/showProjectNews")
   public List<NewsDTO> showProjectNews(@RequestParam Integer size)throws Exception{
      return newsService.showProjectNews(size);
   }
   @Operation(summary = "emall查询-装配式建筑-战略合作伙伴(INFO_TYPE_PARTNER)")
   @PostMapping(value="/partnerProjectNews")
   public List<NewsDTO> partnerProjectNews(@RequestParam Integer size)throws Exception{
      return newsService.partnerProjectNews(size);
   }

}
