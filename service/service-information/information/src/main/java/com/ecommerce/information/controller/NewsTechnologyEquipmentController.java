package com.ecommerce.information.controller;

import com.ecommerce.common.exception.BusinessOperationException;import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ecommerce.information.service.INewsTechnologyEquipmentService;
import java.lang.Integer;
import com.ecommerce.information.api.dto.news.NewsDTO;
import java.lang.String;
import com.github.pagehelper.PageInfo;
import com.ecommerce.information.api.dto.news.NewsQueryDTO;
import com.ecommerce.information.api.dto.news.NewsCreateDTO;
import com.ecommerce.information.api.dto.ApproveDTO;
import com.ecommerce.information.api.dto.news.NewsUpdateDTO;
import java.util.List;


/**
 * @Created锛�Mon Feb 18 15:49:36 CST 2019
 * <AUTHOR>
 * @Version:2
 * @Description:null
*/

@RestController
@Tag(name = "NewsTechnologyEquipment", description = "null")
@RequestMapping("/newsTechnologyEquipment")
public class NewsTechnologyEquipmentController {

   @Autowired 
   private INewsTechnologyEquipmentService iNewsTechnologyEquipmentService;

   @Operation(summary = "更新新闻")
   @PostMapping(value="/update")
   public NewsDTO update(@RequestBody NewsUpdateDTO dto)throws Exception{
      return iNewsTechnologyEquipmentService.update(dto);
   }


   @Operation(summary = "新增新闻")
   @PostMapping(value="/create")
   public NewsDTO create(@RequestBody NewsCreateDTO dto)throws Exception{
      return iNewsTechnologyEquipmentService.create(dto);
   }


   @Operation(summary = "清除缓存数据")
   @PostMapping(value="/clearCache")
   public void clearCache(@RequestParam String operator)throws Exception{
      iNewsTechnologyEquipmentService.clearCache(operator);

   }


   @Operation(summary = "装配式建筑-标准化设计")
   @PostMapping(value="/designProjectNews")
   public List<NewsDTO> designProjectNews(@RequestParam Integer size)throws Exception{
      return iNewsTechnologyEquipmentService.designProjectNews(size);
   }


   @Operation(summary = "装配式建筑-投资开发")
   @PostMapping(value="/investProjectNews")
   public List<NewsDTO> investProjectNews(@RequestParam Integer size)throws Exception{
      return iNewsTechnologyEquipmentService.investProjectNews(size);
   }


   @Operation(summary = "装配式建筑-战略合作伙伴")
   @PostMapping(value="/partnerProjectNews")
   public List<NewsDTO> partnerProjectNews(@RequestParam Integer size)throws Exception{
      return iNewsTechnologyEquipmentService.partnerProjectNews(size);
   }


   @Operation(summary = "技术装备-最新技术新闻")
   @PostMapping(value="/technologyProjectNews")
   public List<NewsDTO> technologyProjectNews(@RequestParam Integer size)throws Exception{
      return iNewsTechnologyEquipmentService.technologyProjectNews(size);
   }


   @Operation(summary = "查找审批历史")
   @PostMapping(value="/findApproveListById")
   public List<ApproveDTO> findApproveListById(@RequestParam String newsId)throws Exception{
      return iNewsTechnologyEquipmentService.findApproveListById(newsId);
   }


   @Operation(summary = "装配式建筑-信息化管理")
   @PostMapping(value="/manageProjectNews")
   public List<NewsDTO> manageProjectNews(@RequestParam Integer size)throws Exception{
      return iNewsTechnologyEquipmentService.manageProjectNews(size);
   }


   @Operation(summary = "装配式建筑-智能化应用")
   @PostMapping(value="/applicationProjectNews")
   public List<NewsDTO> applicationProjectNews(@RequestParam Integer size)throws Exception{
      return iNewsTechnologyEquipmentService.applicationProjectNews(size);
   }


   @Operation(summary = "政策法规")
   @PostMapping(value="/policiesAndRegulations")
   public List<NewsDTO> policiesAndRegulations(@RequestParam Integer size)throws Exception{
      return iNewsTechnologyEquipmentService.policiesAndRegulations(size);
   }


   @Operation(summary = "装配式建筑-一体化装修")
   @PostMapping(value="/decorateProjectNews")
   public List<NewsDTO> decorateProjectNews(@RequestParam Integer size)throws Exception{
      return iNewsTechnologyEquipmentService.decorateProjectNews(size);
   }


   @Operation(summary = "批量删除新闻")
   @PostMapping(value="/deleteByIds")
   public void deleteByIds(@RequestBody List<String> idList,@RequestParam String operator)throws Exception{
      iNewsTechnologyEquipmentService.deleteByIds(idList,operator);

   }


   @Operation(summary = "全国新闻")
   @PostMapping(value="/nationalNews")
   public List<NewsDTO> nationalNews(@RequestParam Integer size)throws Exception{
      return iNewsTechnologyEquipmentService.nationalNews(size);
   }


   @Operation(summary = "踩　＋１")
   @PostMapping(value="/stepOn")
   public void stepOn(@RequestParam String id,@RequestParam String operator)throws Exception{
      iNewsTechnologyEquipmentService.stepOn(id,operator);

   }


   @Operation(summary = "审批通过")
   @PostMapping(value="/approved")
   public void approved(@RequestBody ApproveDTO dto)throws Exception{
      iNewsTechnologyEquipmentService.approved(dto);

   }


   @Operation(summary = "根据Id删除新闻")
   @PostMapping(value="/deleteById")
   public void deleteById(@RequestParam String id,@RequestParam String operator)throws Exception{
      iNewsTechnologyEquipmentService.deleteById(id,operator);

   }


   @Operation(summary = "翻页查询")
   @PostMapping(value="/findAll")
   public PageInfo<NewsDTO> findAll(@RequestBody NewsQueryDTO query)throws Exception{
      return iNewsTechnologyEquipmentService.findAll(query);
   }


   @Operation(summary = "根据Id查找新闻")
   @PostMapping(value="/findById")
   public NewsDTO findById(@RequestParam String id)throws Exception{
      return iNewsTechnologyEquipmentService.findById(id);
   }


   @Operation(summary = "最新新闻")
   @PostMapping(value="/latestNews")
   public List<NewsDTO> latestNews(@RequestParam Integer size)throws Exception{
      return iNewsTechnologyEquipmentService.latestNews(size);
   }


   @Operation(summary = "装配式建筑-装配化施工")
   @PostMapping(value="/buildProjectNews")
   public List<NewsDTO> buildProjectNews(@RequestParam Integer size)throws Exception{
      return iNewsTechnologyEquipmentService.buildProjectNews(size);
   }


   @Operation(summary = "审批被驳回")
   @PostMapping(value="/rejected")
   public void rejected(@RequestBody ApproveDTO dto)throws Exception{
      iNewsTechnologyEquipmentService.rejected(dto);

   }


   @Operation(summary = "企业新闻")
   @PostMapping(value="/corporateNews")
   public List<NewsDTO> corporateNews(@RequestParam Integer size)throws Exception{
      return iNewsTechnologyEquipmentService.corporateNews(size);
   }


   @Operation(summary = "技术装备-热门产品的新闻")
   @PostMapping(value="/hotProductNews")
   public List<NewsDTO> hotProductNews(@RequestParam Integer size)throws Exception{
      return iNewsTechnologyEquipmentService.hotProductNews(size);
   }


   @Operation(summary = "顶　＋１")
   @PostMapping(value="/top")
   public void top(@RequestParam String id,@RequestParam String operator)throws Exception{
      iNewsTechnologyEquipmentService.top(id,operator);

   }


   @Operation(summary = "装配式建筑-工业化生产")
   @PostMapping(value="/prodProjectNews")
   public List<NewsDTO> prodProjectNews(@RequestParam Integer size)throws Exception{
      return iNewsTechnologyEquipmentService.prodProjectNews(size);
   }


   @Operation(summary = "装配式建筑-项目展示")
   @PostMapping(value="/showProjectNews")
   public List<NewsDTO> showProjectNews(@RequestParam Integer size)throws Exception{
      return iNewsTechnologyEquipmentService.showProjectNews(size);
   }


   @Operation(summary = "根据分类查询")
   @PostMapping(value="/findByCategory")
   public List<NewsDTO> findByCategory(@RequestBody List<String> categoryList)throws Exception{
      return iNewsTechnologyEquipmentService.findByCategory(categoryList);
   }


   @Operation(summary = "点击量　＋１")
   @PostMapping(value="/addHits")
   public void addHits(@RequestParam String id,@RequestParam String operator)throws Exception{
      iNewsTechnologyEquipmentService.addHits(id,operator);

   }


   @Operation(summary = "批量查找新闻")
   @PostMapping(value="/findByIds")
   public List<NewsDTO> findByIds(@RequestBody List<String> idList)throws Exception{
      return iNewsTechnologyEquipmentService.findByIds(idList);
   }



}
