package com.ecommerce.information.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.information.api.dto.priceInfo.PriceChartDataQueryDTO;
import com.ecommerce.information.api.dto.priceInfo.PriceInfoAddDTO;
import com.ecommerce.information.api.dto.priceInfo.PriceInfoDTO;
import com.ecommerce.information.api.dto.priceInfo.PriceInfoQueryDTO;
import com.ecommerce.information.api.dto.priceInfo.PriceInfoUpdateDTO;
import com.ecommerce.information.api.dto.priceInfo.ProvincePriceInfoDTO;
import com.ecommerce.information.api.dto.priceInfo.ProvincePriceInfoIndexDTO;
import com.ecommerce.information.api.dto.priceInfo.ProvincePriceInfoQueryDTO;
import com.ecommerce.information.service.IPriceInfoService;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.OutputStream;
import java.util.List;


/**
 * @Created锛�Fri Jan 04 11:38:15 CST 2019
 * <AUTHOR>
 * @Version:2
 * @Description:null
*/

@RestController
@Tag(name = "PriceInfo", description = "价格信息查询接口")
@RequestMapping("/priceInfo")
public class PriceInfoController {

   @Autowired 
   private IPriceInfoService iPriceInfoService;

   @Operation(summary = "添加记录")
   @PostMapping(value="/add")
   public PriceInfoDTO add(@RequestBody PriceInfoAddDTO arg0)throws Exception{
      return iPriceInfoService.add(arg0);
   }


   @Operation(summary = "更新记录")
   @PostMapping(value="/update")
   public PriceInfoDTO update(@RequestBody PriceInfoUpdateDTO arg0)throws Exception{
      return iPriceInfoService.update(arg0);
   }


   @Operation(summary = "价格走势图数据查询")
   @PostMapping(value="/findPriceChartData")
   public List<PriceInfoDTO> findPriceChartData(@RequestBody PriceChartDataQueryDTO arg0)throws Exception{
      return iPriceInfoService.findPriceChartData(arg0);
   }


   @Operation(summary = "资讯首页查显示的")
   @PostMapping(value="/findCement")
   public List<PriceInfoDTO> findCement(@RequestParam(required = false) Integer arg0)throws Exception{
      return iPriceInfoService.findCement(arg0);
   }


   @Operation(summary = "资讯首页查显示的")
   @PostMapping(value="/findConcrete")
   public List<PriceInfoDTO> findConcrete(@RequestParam(required = false) Integer arg0)throws Exception{
      return iPriceInfoService.findConcrete(arg0);
   }


   @Operation(summary = "根据id集合删除记录")
   @PostMapping(value="/deleteByIds")
   public void deleteByIds(@RequestBody List<String> arg0,@RequestParam String arg1)throws Exception{
      iPriceInfoService.deleteByIds(arg0,arg1);

   }


   @Operation(summary = "根据id删除记录")
   @PostMapping(value="/deleteById")
   public void deleteById(@RequestParam String arg0,@RequestParam String arg1)throws Exception{
      iPriceInfoService.deleteById(arg0,arg1);

   }


   @Operation(summary = "翻页查询")
   @PostMapping(value="/findAll")
   public PageInfo<PriceInfoDTO> findAll(@RequestBody PriceInfoQueryDTO arg0)throws Exception{
      return iPriceInfoService.findAll(arg0);
   }


   @Operation(summary = "查询品牌")
   @PostMapping(value="/findCementBrand")
   public List<String> findCementBrand(@RequestParam(required = false) String arg0)throws Exception{
      return iPriceInfoService.findCementBrand(arg0);
   }
   @Operation(summary = "查询品种")
   @PostMapping(value="/findCementVarieties")
   public List<String> findCementVarieties(@RequestParam(required = false) String arg0)throws Exception{
      return iPriceInfoService.findCementVarieties(arg0);
   }
   @Operation(summary = "查询品牌")
   @PostMapping(value="/findConcreteBrand")
   public List<String> findConcreteBrand(@RequestParam(required = false) String arg0)throws Exception{
      return iPriceInfoService.findConcreteBrand(arg0);
   }
   @Operation(summary = "查询品种")
   @PostMapping(value="/findConcreteVarieties")
   public List<String> findConcreteVarieties(@RequestParam(required = false) String arg0)throws Exception{
      return iPriceInfoService.findConcreteVarieties(arg0);
   }


   @Operation(summary = "根据id查询单条记录")
   @PostMapping(value="/findById")
   public PriceInfoDTO findById(@RequestParam String arg0)throws Exception{
      return iPriceInfoService.findById(arg0);
   }


   @Operation(summary = "导入记录")
   @PostMapping(value="/excelImport")
   public void excelImport(@RequestBody MultipartFile arg0,@RequestParam String arg1)throws Exception{
      iPriceInfoService.excelImport(arg0,arg1);

   }


   @Operation(summary = "获取excel导入模板")
   @PostMapping(value="/getExcelTemplete")
   public OutputStream getExcelTemplete()throws Exception{
      return iPriceInfoService.getExcelTemplete();
   }

   @Operation(summary = "查询有数据的省份")
   @PostMapping(value="/findProvince")
   public List<String> findProvince()throws Exception{
      return iPriceInfoService.findProvince();
   }
   @Operation(summary = "根据id查询单条记录")
   @PostMapping(value="/findProvincePriceInfo")
   public ProvincePriceInfoDTO findProvincePriceInfo(@RequestBody ProvincePriceInfoQueryDTO dto)throws Exception{
      return iPriceInfoService.findProvincePriceInfo(dto);
   }

   @Operation(summary = "根据省份查询价格信息")
   @PostMapping("/findProvincePriceInfo2")
   public ProvincePriceInfoIndexDTO findProvincePriceInfo2(@RequestBody ProvincePriceInfoQueryDTO dto){
      return iPriceInfoService.findProvincePriceInfo2(dto);
   }

}
