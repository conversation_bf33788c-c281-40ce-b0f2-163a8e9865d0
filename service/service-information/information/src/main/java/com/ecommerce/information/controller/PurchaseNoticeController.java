package com.ecommerce.information.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.information.api.dto.purchase.PurchaseNoticeAddDTO;
import com.ecommerce.information.api.dto.purchase.PurchaseNoticeAnonDTO;
import com.ecommerce.information.api.dto.purchase.PurchaseNoticeCondDTO;
import com.ecommerce.information.api.dto.purchase.PurchaseNoticeDeleteDTO;
import com.ecommerce.information.api.dto.purchase.PurchaseNoticeDetailDTO;
import com.ecommerce.information.api.dto.purchase.PurchaseNoticeListDTO;
import com.ecommerce.information.api.dto.purchase.PurchaseNoticeStatusDTO;
import com.ecommerce.information.api.dto.purchase.PurchaseNoticeUpdateDTO;
import com.ecommerce.information.service.IPurchaseNoticeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 采购招标公告服务
 *  PurchaseNoticeController
 *
 * <AUTHOR>
 */
@RestController
@Tag(name = "PurchaseNotice", description = "采购招标公告服务")
@RequestMapping("/purchaseNotice")
public class PurchaseNoticeController {

    @Autowired
    private IPurchaseNoticeService purchaseNoticeService;

    /**
     * 采购公告添加
     * @param purchaseNoticeAddDTO
     * @return
     */
    @Operation(summary = "采购公告添加")
    @PostMapping(value="/createNotice")
    public ItemResult<String> createNotice(@RequestBody PurchaseNoticeAddDTO purchaseNoticeAddDTO) {
        return purchaseNoticeService.createNotice(purchaseNoticeAddDTO);
    }

    /**
     * 批量删除公告
     * @param purchaseNoticeDeleteDTO
     * @return
     */
    @Operation(summary = "批量删除公告")
    @PostMapping(value="/deleteNoticeList")
    public ItemResult<Void> deleteNoticeList(@RequestBody PurchaseNoticeDeleteDTO purchaseNoticeDeleteDTO) {
        return purchaseNoticeService.deleteNoticeList(purchaseNoticeDeleteDTO);
    }

    /**
     * 匿名查看公告
     * @param purchaseNoticeId
     * @return
     */
    @Operation(summary = "匿名查看公告")
    @PostMapping(value="/anonQueryById")
    public ItemResult<PurchaseNoticeAnonDTO> anonQueryById(@RequestParam String purchaseNoticeId) {
        return purchaseNoticeService.anonQueryById(purchaseNoticeId);
    }

    /**
     * 明细查询
     * @param purchaseNoticeId
     * @return
     */
    @Operation(summary = "明细查询")
    @PostMapping(value="/queryDetailById")
    public ItemResult<PurchaseNoticeDetailDTO> queryDetailById(@RequestParam String purchaseNoticeId) {
        return purchaseNoticeService.queryDetailById(purchaseNoticeId);
    }

    /**
     * 分页条件查询
     * @param purchaseNoticeCondDTO
     * @return
     */
    @Operation(summary = "分页条件查询")
    @PostMapping(value="/queryByCond")
    public ItemResult<PageData<PurchaseNoticeListDTO>> queryByCond(@RequestBody PurchaseNoticeCondDTO purchaseNoticeCondDTO) {
        return purchaseNoticeService.queryByCond(purchaseNoticeCondDTO);
    }

    /**
     * 更新公告内容
     * @param purchaseNoticeUpdateDTO
     * @return
     */
    @Operation(summary = "更新公告内容")
    @PostMapping(value="/updateNoticeInfo")
    public ItemResult<Void> updateNoticeInfo(@RequestBody PurchaseNoticeUpdateDTO purchaseNoticeUpdateDTO) {
        return purchaseNoticeService.updateNoticeInfo(purchaseNoticeUpdateDTO);
    }

    /**
     * 更改公告状态
     * @param purchaseNoticeStatusDTO
     * @return
     */
    @Operation(summary = "更改公告状态")
    @PostMapping(value="/updateStatus")
    public ItemResult<Void> updateStatus(@RequestBody PurchaseNoticeStatusDTO purchaseNoticeStatusDTO) {
        return purchaseNoticeService.updateStatus(purchaseNoticeStatusDTO);
    }

    /**
     * SRM推送采购信息添加
     */
    @Operation(summary = "SRM推送采购公告添加")
    @PostMapping(value="/srmCreateNotice")
    public ItemResult<String> srmCreateNotice(@RequestBody List<PurchaseNoticeAddDTO> purchaseNoticeAddDTOList) {
        return purchaseNoticeService.srmCreateNotice(purchaseNoticeAddDTOList);
    }
}
