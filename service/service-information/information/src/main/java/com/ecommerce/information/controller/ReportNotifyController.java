package com.ecommerce.information.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.information.service.IReportNotifyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@RestController
@Tag(name = "ReportNotify", description = "温馨日报发邮件")
@RequestMapping("/reportNotify")
public class ReportNotifyController {

    @Autowired
    private IReportNotifyService mailService;

    /**
     * 温馨日报发送邮件
     * @return
     */
    @Operation(summary = "温馨日报发送邮件")
    @PostMapping(value="/sendDailyReportAllMail")
    public ItemResult<Void> sendDailyReportAllMail() {
        try {
            mailService.sendDailyReportAllMail();
        } catch (Exception e) {
            ItemResult<Void> itemResult = new ItemResult<>();
            itemResult.setDescription(e.getMessage());
            return itemResult;
        }
        return new ItemResult<>(null);
    }

    /**
     * 发送模板ID的报表
     * @param reportTemplateId
     */
    @Operation(summary = "发送模板ID的报表")
    @PostMapping(value="/sendDailyReportByTemplateId")
    public ItemResult<Void> sendDailyReportByTemplateId(@RequestParam String reportTemplateId) {
        try {
            mailService.sendDailyReportByTemplateId(reportTemplateId);
        } catch (Exception e) {
            ItemResult<Void> itemResult = new ItemResult<>();
            itemResult.setDescription(e.getMessage());
            return itemResult;
        }
        return new ItemResult<>(null);
    }


}
