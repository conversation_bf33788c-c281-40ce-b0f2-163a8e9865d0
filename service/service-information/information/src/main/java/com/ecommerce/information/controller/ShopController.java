package com.ecommerce.information.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.information.api.dto.shop.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ecommerce.information.service.IShopService;

import java.lang.String;

import com.github.pagehelper.PageInfo;

import java.util.List;


/**
 * @Created锛�Tue May 07 15:33:26 CST 2019
 * <AUTHOR>
 * @Version:2
 * @Description::店铺服务
*/

@RestController
@Tag(name = "Shop", description = ":店铺服务")
@RequestMapping("/shop")
public class ShopController {

   @Autowired 
   private IShopService iShopService;

   @Operation(summary = "禁用店铺")
   @PostMapping(value="/disable")
   public void disable(@RequestParam String shopId,@RequestParam String operator)throws Exception{
      iShopService.disable(shopId,operator);

   }

   @Operation(summary = "启用店铺")
   @PostMapping(value="/enable")
   public void enable(@RequestParam String shopId,@RequestParam String operator)throws Exception{
      iShopService.enable(shopId,operator);

   }

   @Operation(summary = "店铺轮播图详情")
   @PostMapping(value="/getShopCarouselDetail")
   public ShopCarouselDTO getShopCarouselDetail(@RequestParam String carouselId)throws Exception{
      return iShopService.getShopCarouselDetail(carouselId);
   }

   @Operation(summary = "修改店铺轮播图")
   @PostMapping(value="/updateShopCarousel")
   public void updateShopCarousel(@RequestBody UpdateShopCarouselDTO updateShopCarouselDTO,@RequestParam String operator)throws Exception{
      iShopService.updateShopCarousel(updateShopCarouselDTO,operator);

   }

   @Operation(summary = "删除店铺轮播图")
   @PostMapping(value="/deleteShopCarousel")
   public void deleteShopCarousel(@RequestParam String carouselId,@RequestParam String operator)throws Exception{
      iShopService.deleteShopCarousel(carouselId,operator);

   }


   @Operation(summary = "新增店铺商品广告")
   @PostMapping(value="/addShopAdvertisement")
   public void addShopAdvertisement(@RequestBody AddShopAdvertisementDTO addShopAdvertisementDTO,@RequestParam String operator)throws Exception{
      iShopService.addShopAdvertisement(addShopAdvertisementDTO,operator);

   }


   @Operation(summary = "修改店铺商品广告")
   @PostMapping(value="/updateShopAdvertisement")
   public void updateShopAdvertisement(@RequestBody UpdateShopAdvertisementDTO updateShopAdvertisementDTO,@RequestParam String operator)throws Exception{
      iShopService.updateShopAdvertisement(updateShopAdvertisementDTO,operator);

   }


   @Operation(summary = "启用店铺商品广告")
   @PostMapping(value="/enableShopAdvertisement")
   public void enableShopAdvertisement(@RequestParam String shopAdvertisementId,@RequestParam String operator)throws Exception{
      iShopService.enableShopAdvertisement(shopAdvertisementId,operator);

   }


   @Operation(summary = "禁用店铺商品广告")
   @PostMapping(value="/disableShopAdvertisement")
   public void disableShopAdvertisement(@RequestParam String shopAdvertisementId,@RequestParam String operator)throws Exception{
      iShopService.disableShopAdvertisement(shopAdvertisementId,operator);

   }


   @Operation(summary = "店铺商品广告详情")
   @PostMapping(value="/getShopAdvertisementDetail")
   public ShopAdvertisementDTO getShopAdvertisementDetail(@RequestParam String shopAdvertisementId)throws Exception{
      return iShopService.getShopAdvertisementDetail(shopAdvertisementId);
   }

   @Operation(summary = "查询店铺所有广告")
   @PostMapping(value="/getShopAdvertisementByShopId")
   public List<ShopAdvertisementDTO> getShopAdvertisementByShopId(@RequestParam String shopId,@RequestParam String advertisementType)throws Exception{
      return iShopService.getShopAdvertisementByShopId(shopId,advertisementType);
   }

   @Operation(summary = "店铺商品广告分页查询")
   @PostMapping(value="/getShopAdvertisementList")
   public PageInfo<ShopAdvertisementDTO> getShopAdvertisementList(@RequestBody PageShopAdvertisementListDTO pageShopAdvertisementListDTO)throws Exception{
      return iShopService.getShopAdvertisementList(pageShopAdvertisementListDTO);
   }


   @Operation(summary = "删除店铺广告")
   @PostMapping(value="/deleteShopAdvertisement")
   public void deleteShopAdvertisement(@RequestParam String shopAdvertisementId,@RequestParam String operator)throws Exception{
      iShopService.deleteShopAdvertisement(shopAdvertisementId,operator);

   }


   @Operation(summary = "店铺轮播图列表")
   @PostMapping(value="/getShopCarouselList")
   public List<ShopCarouselListDTO> getShopCarouselList(@RequestBody GetShopCarouselListDTO getShopCarouselListDTO)throws Exception{
      return iShopService.getShopCarouselList(getShopCarouselListDTO);
   }


   @Operation(summary = "分页店铺图集列表")
   @PostMapping(value="/pageShopPictureList")
   public PageInfo<ShopPictureListDTO> pageShopPictureList(@RequestBody PageShopPictureListDTO pageShopPictureListDTO)throws Exception{
      return iShopService.pageShopPictureList(pageShopPictureListDTO);
   }


   @Operation(summary = "店铺图集详情")
   @PostMapping(value="/getShopPictureDetail")
   public ShopPictureDTO getShopPictureDetail(@RequestParam String pictureId)throws Exception{
      return iShopService.getShopPictureDetail(pictureId);
   }


   @Operation(summary = "店铺活动公告详情")
   @PostMapping(value="/getShopNoticeDetail")
   public ShopNoticeDTO getShopNoticeDetail(@RequestParam String noticeId)throws Exception{
      return iShopService.getShopNoticeDetail(noticeId);
   }


   @Operation(summary = "店铺图集列表")
   @PostMapping(value="/getShopPictureList")
   public List<ShopPictureListDTO> getShopPictureList(@RequestBody GetShopPictureListDTO getShopPictureListDTO)throws Exception{
      return iShopService.getShopPictureList(getShopPictureListDTO);
   }


   @Operation(summary = "分页查看店铺申请列表")
   @PostMapping(value="/pageShopApplyList")
   public PageInfo<ShopApplyDTO> pageShopApplyList(@RequestBody PageShopApplyListDTO pageShopApplyListDTO)throws Exception{
      return iShopService.pageShopApplyList(pageShopApplyListDTO);
   }


   @Operation(summary = "下移店铺活动公告")
   @PostMapping(value="/moveDownShopNotice")
   public void moveDownShopNotice(@RequestParam String noticeId,@RequestParam String operator)throws Exception{
      iShopService.moveDownShopNotice(noticeId,operator);

   }


   @Operation(summary = "店铺活动公告列表")
   @PostMapping(value="/getShopNoticeList")
   public List<ShopNoticeListDTO> getShopNoticeList(@RequestBody GetShopNoticeListDTO getShopNoticeListDTO)throws Exception{
      return iShopService.getShopNoticeList(getShopNoticeListDTO);
   }


   @Operation(summary = "分页店铺活动公告列表")
   @PostMapping(value="/pageShopNoticeList")
   public PageInfo<ShopNoticeListDTO> pageShopNoticeList(@RequestBody PageShopNoticeListDTO pageShopNoticeListDTO)throws Exception{
      return iShopService.pageShopNoticeList(pageShopNoticeListDTO);
   }


   @Operation(summary = "上传店铺图集")
   @PostMapping(value="/uploadShopPicture")
   public void uploadShopPicture(@RequestBody ShopPictureDTO shopPictureDTO,@RequestParam String operator)throws Exception{
      iShopService.uploadShopPicture(shopPictureDTO,operator);

   }


   @Operation(summary = "店铺企业动态详情")
   @PostMapping(value="/getShopNewsDetail")
   public ShopNewsDTO getShopNewsDetail(@RequestParam String newsId)throws Exception{
      return iShopService.getShopNewsDetail(newsId);
   }


   @Operation(summary = "下移店铺轮播图")
   @PostMapping(value="/moveDownShopCarousel")
   public void moveDownShopCarousel(@RequestParam String carouselId,@RequestParam String operator)throws Exception{
      iShopService.moveDownShopCarousel(carouselId,operator);

   }


   @Operation(summary = "查看店铺申请详情")
   @PostMapping(value="/getShopApplyDetail")
   public ShopApplyDTO getShopApplyDetail(@RequestParam String memberId)throws Exception{
      return iShopService.getShopApplyDetail(memberId);
   }


   @Operation(summary = "上移店铺轮播图")
   @PostMapping(value="/moveUpShopCarousel")
   public void moveUpShopCarousel(@RequestParam String carouselId,@RequestParam String operator)throws Exception{
      iShopService.moveUpShopCarousel(carouselId,operator);

   }


   @Operation(summary = "置顶店铺企业动态")
   @PostMapping(value="/toTopShopNews")
   public void toTopShopNews(@RequestParam String newsId,@RequestParam String operator)throws Exception{
      iShopService.toTopShopNews(newsId,operator);

   }


   @Operation(summary = "上移店铺活动公告")
   @PostMapping(value="/moveUpShopNotice")
   public void moveUpShopNotice(@RequestParam String noticeId,@RequestParam String operator)throws Exception{
      iShopService.moveUpShopNotice(noticeId,operator);

   }


   @Operation(summary = "会员是否可以提出申请")
   @PostMapping(value="/isExistShopApply")
   public Boolean isExistShopApply(@RequestParam String memberId)throws Exception{
      return iShopService.isExistShopApply(memberId);
   }


   @Operation(summary = "会员开店")
   @PostMapping(value="/addShopInfo")
   public void addShopInfo(@RequestBody AddShopInfoDTO addShopInfoDTO,@RequestParam String operator)throws Exception{
      iShopService.addShopInfo(addShopInfoDTO,operator);

   }


   @Operation(summary = "会员修改店铺信息")
   @PostMapping(value="/updateShopInfo")
   public void updateShopInfo(@RequestBody UpdateShopInfoDTO updateShopInfoDTO,@RequestParam String operator)throws Exception{
      iShopService.updateShopInfo(updateShopInfoDTO,operator);

   }


   @Operation(summary = "查看店铺详情")
   @PostMapping(value="/getShopInfo")
   public ShopInfoDTO getShopInfo(@RequestParam String shopId)throws Exception{
      return iShopService.getShopInfo(shopId);
   }

   @Operation(summary = "通过memberId查看店铺详情")
   @PostMapping(value="/getShopInfoByMemberId")
   public ShopInfoDTO getShopInfoByMemberId(@RequestParam String memberId)throws Exception{
      ShopInfoDTO shopInfoDTO = new ShopInfoDTO();
      shopInfoDTO.setMemberId(memberId);
      List<ShopInfoDTO> list = iShopService.getShopInfoByQuery(shopInfoDTO);
      if (list != null && list.size() > 0) {
         return list.get(0);
      }
      return null;
   }

   @Operation(summary = "通过域名前缀查询店铺")
   @PostMapping(value="/getShopInfoByUrl")
   public ShopInfoDTO getShopInfoByUrl(@RequestParam String shopUrlPrefix)throws Exception{
      ShopInfoDTO shopInfoDTO = new ShopInfoDTO();
      shopInfoDTO.setShopUrlPrefix(shopUrlPrefix);
      List<ShopInfoDTO> list = iShopService.getShopInfoByQuery(shopInfoDTO);
      if (list != null && list.size() > 0) {
         return list.get(0);
      }
      return null;
   }

   @Operation(summary = "新增店铺轮播图（最多五条，如果多于五条则删除序号最小的一条）")
   @PostMapping(value="/addShopCarousel")
   public void addShopCarousel(@RequestBody AddShopCarouselDTO addShopCarouselDTO,@RequestParam String operator)throws Exception{
      iShopService.addShopCarousel(addShopCarouselDTO,operator);

   }


   @Operation(summary = "下移店铺企业动态")
   @PostMapping(value="/moveDownShopNews")
   public void moveDownShopNews(@RequestParam String newsId,@RequestParam String operator)throws Exception{
      iShopService.moveDownShopNews(newsId,operator);

   }


   @Operation(summary = "删除店铺申请")
   @PostMapping(value="/deleteShopApply")
   public void deleteShopApply(@RequestParam String applyId,@RequestParam String operator)throws Exception{
      iShopService.deleteShopApply(applyId,operator);

   }


   @Operation(summary = "移除店铺图集")
   @PostMapping(value="/deletShopPicture")
   public void deletShopPicture(@RequestParam String pictureId,@RequestParam String operator)throws Exception{
      iShopService.deletShopPicture(pictureId,operator);

   }


   @Operation(summary = "分页店铺企业动态列表")
   @PostMapping(value="/pageShopNewsList")
   public PageInfo<ShopNewsListDTO> pageShopNewsList(@RequestBody PageShopNewsListDTO pageShopNewsListDTO)throws Exception{
      return iShopService.pageShopNewsList(pageShopNewsListDTO);
   }


   @Operation(summary = "店铺企业动态列表")
   @PostMapping(value="/getShopNewsList")
   public List<ShopNewsListDTO> getShopNewsList(@RequestBody GetShopNewsListDTO getShopNewsListDTO)throws Exception{
      return iShopService.getShopNewsList(getShopNewsListDTO);
   }


   @Operation(summary = "修改店铺活动公告")
   @PostMapping(value="/updateShopNotice")
   public void updateShopNotice(@RequestBody ShopNoticeDTO shopNoticeDTO,@RequestParam String operator)throws Exception{
      iShopService.updateShopNotice(shopNoticeDTO,operator);

   }


   @Operation(summary = "删除店铺活动公告")
   @PostMapping(value="/deleteShopNotice")
   public void deleteShopNotice(@RequestParam String noticeId,@RequestParam String operator)throws Exception{
      iShopService.deleteShopNotice(noticeId,operator);

   }


   @Operation(summary = "置顶店铺活动公告")
   @PostMapping(value="/toTopShopNotice")
   public void toTopShopNotice(@RequestParam String noticeId,@RequestParam String operator)throws Exception{
      iShopService.toTopShopNotice(noticeId,operator);

   }


   @Operation(summary = "创建店铺申请")
   @PostMapping(value="/addShopApply")
   public void addShopApply(@RequestBody AddShopApplyDTO addShopApplyDTO,@RequestParam String operator)throws Exception{
      iShopService.addShopApply(addShopApplyDTO,operator);

   }


   @Operation(summary = "修改店铺企业动态")
   @PostMapping(value="/updateShopNews")
   public void updateShopNews(@RequestBody ShopNewsDTO shopNewsDTO,@RequestParam String operator)throws Exception{
      iShopService.updateShopNews(shopNewsDTO,operator);

   }


   @Operation(summary = "新增店铺活动公告")
   @PostMapping(value="/addShopNotice")
   public void addShopNotice(@RequestBody ShopNoticeDTO shopNoticeDTO,@RequestParam String operator)throws Exception{
      iShopService.addShopNotice(shopNoticeDTO,operator);

   }


   @Operation(summary = "新增店铺企业动态")
   @PostMapping(value="/addShopNews")
   public void addShopNews(@RequestBody ShopNewsDTO shopNewsDTO,@RequestParam String operator)throws Exception{
      iShopService.addShopNews(shopNewsDTO,operator);

   }


   @Operation(summary = "删除店铺企业动态")
   @PostMapping(value="/deletShopNews")
   public void deletShopNews(@RequestParam String newsId,@RequestParam String operator)throws Exception{
      iShopService.deletShopNews(newsId,operator);

   }


   @Operation(summary = "上移店铺企业动态")
   @PostMapping(value="/moveUpShopNews")
   public void moveUpShopNews(@RequestParam String newsId,@RequestParam String operator)throws Exception{
      iShopService.moveUpShopNews(newsId,operator);

   }


   @Operation(summary = "编辑店铺申请")
   @PostMapping(value="/updateShopApply")
   public void updateShopApply(@RequestBody ShopApplyDTO shopApplyDTO,@RequestParam String operator)throws Exception{
      iShopService.updateShopApply(shopApplyDTO,operator);

   }


   @Operation(summary = "审批店铺申请")
   @PostMapping(value="/approved")
   public void approved(@RequestBody shopApproveDTO dto,@RequestParam String operator)throws Exception{
      iShopService.approved(dto,operator);

   }


   @Operation(summary = "会员是否可以开店")
   @PostMapping(value="/isExistShop")
   public Boolean isExistShop(@RequestParam String memberId)throws Exception{
      return iShopService.isExistShop(memberId);
   }


   @Operation(summary = "会员关店")
   @PostMapping(value="/closeShopInfo")
   public void closeShopInfo(@RequestParam String shopId,@RequestParam String operator)throws Exception{
      iShopService.closeShopInfo(shopId,operator);

   }


   @Operation(summary = "分页查询店铺列表")
   @PostMapping(value="/pageShopList")
   public PageInfo<ShopListDTO> pageShopList(@RequestBody PageShopListDTO pageShopListDTO)throws Exception{
      return iShopService.pageShopList(pageShopListDTO);
   }

}
