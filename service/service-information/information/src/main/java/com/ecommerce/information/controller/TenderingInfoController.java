package com.ecommerce.information.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.information.api.dto.ApproveDTO;
import com.ecommerce.information.api.dto.advertisement.AdvertisementDetailDTO;
import com.ecommerce.information.api.dto.tendering.*;
import com.ecommerce.information.service.ITenderingInfoService;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * @Created锛�Sun Nov 18 17:54:40 CST 2018
 * <AUTHOR>
 * @Version:2
 * @Description:招标服务接口
*/

@RestController
@Tag(name = "TenderingInfo", description = "招标服务接口")
@RequestMapping("/tenderingInfo")
public class TenderingInfoController {

   @Autowired 
   private ITenderingInfoService iTenderingInfoService;

   @Operation(summary = "获取招标广告信息")
   @PostMapping(value="/getTenderingAdvertisingByTid")
   public AdvertisementDetailDTO getTenderingAdvertisingByTid(@RequestParam String arg0)throws Exception{
      return iTenderingInfoService.getTenderingAdvertisingByTid(arg0);
   }


   @Operation(summary = "获取招标信息详情(不含商品信息)")
   @PostMapping(value="/getTenderingInfoByTid")
   public TenderingInfoDTO getTenderingInfoByTid(@RequestParam String arg0)throws Exception{
      return iTenderingInfoService.getTenderingInfoByTid(arg0);
   }

   @Operation(summary = "获取招标信息详情(不含商品信息)")
   @PostMapping(value="/getTenderingInfoByTidAndMemberId")
   public TenderingInfoDTO getTenderingInfoByTidAndMemberId(@RequestParam String tid,@RequestParam String memberId)throws Exception{
      return iTenderingInfoService.getTenderingInfoByTidAndMemberId(tid,memberId);
   }


   @Operation(summary = "获取招标信息详情(含商品信息)")
   @PostMapping(value="/getTenderingDetailInfoByTid")
   public TenderingDTO getTenderingDetailInfoByTid(@RequestParam String arg0)throws Exception{
      return iTenderingInfoService.getTenderingDetailInfoByTid(arg0);
   }

   @Operation(summary = "获取招标信息详情(含商品信息)")
   @PostMapping(value="/getTenderingDetailInfoByTidAndMemberId")
   public TenderingDTO getTenderingDetailInfoByTidAndMemberId(@RequestParam String tid,@RequestParam String memberId)throws Exception{
      return iTenderingInfoService.getTenderingDetailInfoByTidAndMemberId(tid,memberId);
   }


   @Operation(summary = "分页获取招标信息")
   @PostMapping(value="/getTenderingPageInfo")
   public PageInfo<TenderingDTO> getTenderingPageInfo(@RequestBody SearchTenderingDTO arg0)throws Exception{
      return iTenderingInfoService.getTenderingPageInfo(arg0);
   }


   @Operation(summary = "查找审批历史")
   @PostMapping(value="/findApproveListById")
   public List<ApproveDTO> findApproveListById(@RequestParam String arg0)throws Exception{
      return iTenderingInfoService.findApproveListById(arg0);
   }


   @Operation(summary = "修改招标信息")
   @PostMapping(value="/modifyTenderingInfo")
   public void modifyTenderingInfo(@RequestBody TenderingDTO arg0)throws Exception{
      iTenderingInfoService.modifyTenderingInfo(arg0);

   }


   @Operation(summary = "获取招标信息对应的商品信息")
   @PostMapping(value="/getTenderingProductByTid")
   public List<TenderingProductInfoDTO> getTenderingProductByTid(@RequestParam String arg0)throws Exception{
      return iTenderingInfoService.getTenderingProductByTid(arg0);
   }


   @Operation(summary = "添加一个招标信息")
   @PostMapping(value="/addTenderingInfo")
   public String addTenderingInfo(@RequestBody TenderingDTO arg0)throws Exception{
      return iTenderingInfoService.addTenderingInfo(arg0);
   }


   @Operation(summary = "审批通过（平台审批方法）")
   @PostMapping(value="/approved")
   public void approved(@RequestBody ApproveDTO arg0)throws Exception{
      iTenderingInfoService.approved(arg0);

   }


   @Operation(summary = "审批被驳回（平台审批方法）")
   @PostMapping(value="/rejected")
   public void rejected(@RequestBody ApproveDTO arg0)throws Exception{
      iTenderingInfoService.rejected(arg0);
   }

   @Operation(summary = "根据id删除")
   @PostMapping(value="/deleteById")
   public void deleteById(@RequestParam String id,@RequestParam String operator)throws Exception{
      iTenderingInfoService.deleteById(id,operator);
   }

   @Operation(summary = "根据id批量删除")
   @PostMapping(value="/deleteByIds")
   public void deleteByIds(@RequestBody List<String> ids,@RequestParam String operator)throws Exception{
      iTenderingInfoService.deleteByIds(ids,operator);
   }

   @Operation(summary = "根据id删除")
   @PostMapping(value="/deleteByIdAndMemberId")
   public void deleteByIdAndMemberId(@RequestParam String id,@RequestParam String memberId,@RequestParam String operator)throws Exception{
      iTenderingInfoService.deleteByIdAndMemberId(id,memberId,operator);
   }

   @Operation(summary = "根据id批量删除")
   @PostMapping(value="/deleteByIdsAndMemberId")
   public void deleteByIdsAndMemberId(@RequestBody List<String> ids,@RequestParam String memberId,@RequestParam String operator)throws Exception{
      iTenderingInfoService.deleteByIdsAndMemberId(ids,memberId,operator);
   }



   @Operation(summary = "导出筛选过后的招标信息")
   @PostMapping(value="/exportTendering")
   public void exportTendering(@RequestBody SearchTenderingDTO arg0)throws Exception{
      iTenderingInfoService.exportTendering(arg0);

   }

   @Operation(summary = "采购资讯emall查询方法")
   @PostMapping(value="/findForEmall")
   public PageInfo<TenderingEmallDTO> findForEmall(@RequestBody TenderingEmallDTO dto){
      return iTenderingInfoService.findForEmall(dto);
   }


}
