package com.ecommerce.information.dao.vo;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import jakarta.persistence.*;

@Table(name = "ii_news_technology_equipment_info")
public class NewsTechnologyEquipmentInfo implements Serializable {
    /**
     * 新闻id
     */
    @Id
    @Column(name = "news_id")
    private String newsId;

    /**
     * 分类id
     */
    @Column(name = "category_id")
    private String categoryId;

    /**
     * 标题
     */
    private String title;

    /**
     * 新闻横幅
     */
    private String banner;

    /**
     * 图片
     */
    @Column(name = "picture_url")
    private String pictureUrl;

    /**
     * 点击图片的链接
     */
    @Column(name = "picture_href")
    private String pictureHref;

    /**
     * 关键字
     */
    private String keywords;

    /**
     * 是否置顶
     */
    @Column(name = "show_in_top")
    private Boolean showInTop;

    /**
     * 新闻内容
     */
    private String content;

    /**
     * 新闻内容2
     */
    @Column(name = "content_url")
    private String contentUrl;

    /**
     * 分类名称
     */
    @Column(name = "category_name")
    private String categoryName;

    /**
     * 是否在首页中显示
     */
    @Column(name = "show_in_index")
    private Boolean showInIndex;

    /**
     * 是否图片新闻
     */
    @Column(name = "picturl_news")
    private Boolean picturlNews;

    /**
     * 是否允许评论
     */
    @Column(name = "allow_comments")
    private Boolean allowComments;

    /**
     * 描述
     */
    private String description;

    /**
     * 点击数量
     */
    private Long hits;

    /**
     * 顶
     */
    @Column(name = "praise_num")
    private Long praiseNum;

    /**
     * 踩
     */
    @Column(name = "bad_review_num")
    private Long badReviewNum;

    /**
     * 审批状态（0 待审批，1 拒绝，2通过，3 进行中，4 已关闭）
     */
    @Column(name = "check_status")
    private Integer checkStatus;

    /**
     * 是否合作伙伴
     */
    @Column(name = "partner_flag")
    private Byte partnerFlag;

    /**
     * 技术装备-热门产品-产品名称
     */
    @Column(name = "product_name")
    private String productName;

    /**
     * 技术装备-热门产品-产品规则型号
     */
    @Column(name = "product_model")
    private String productModel;

    /**
     * 技术装备-热门产品-产品特点
     */
    @Column(name = "product_features")
    private String productFeatures;

    /**
     * 技术装备-热门产品-产品适用范围
     */
    @Column(name = "product_scope")
    private String productScope;

    /**
     * 技术装备-热门产品-卖家
     */
    @Column(name = "product_owner")
    private String productOwner;

    /**
     * 技术装备-热门产品-卖家联系人
     */
    @Column(name = "product_contact")
    private String productContact;

    /**
     * 技术装备-热门产品-卖家联系电话
     */
    @Column(name = "product_contact_phone")
    private String productContactPhone;

    /**
     * 技术装备-热门产品-卖家联系电话
     */
    @Column(name = "product_url")
    private String productUrl;

    /**
     * 新闻编辑人
     */
    private String editor;

    /**
     * 咨询来源
     */
    private String source;

    /**
     * 删除标记
     */
    @Column(name = "DEL_FLG")
    private Boolean delFlg;

    /**
     * 创建人
     */
    @Column(name = "CREATE_USER")
    private String createUser;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_TIME")
    private Date createTime;

    /**
     * 修改人
     */
    @Column(name = "UPDATE_USER")
    private String updateUser;

    /**
     * 修改时间
     */
    @Column(name = "UPDATE_TIME")
    private Date updateTime;

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 获取新闻id
     *
     * @return news_id - 新闻id
     */
    public String getNewsId() {
        return newsId;
    }

    /**
     * 设置新闻id
     *
     * @param newsId 新闻id
     */
    public void setNewsId(String newsId) {
        this.newsId = newsId == null ? null : newsId.trim();
    }

    /**
     * 获取分类id
     *
     * @return category_id - 分类id
     */
    public String getCategoryId() {
        return categoryId;
    }

    /**
     * 设置分类id
     *
     * @param categoryId 分类id
     */
    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId == null ? null : categoryId.trim();
    }

    /**
     * 获取标题
     *
     * @return title - 标题
     */
    public String getTitle() {
        return title;
    }

    /**
     * 设置标题
     *
     * @param title 标题
     */
    public void setTitle(String title) {
        this.title = title == null ? null : title.trim();
    }

    /**
     * 获取新闻横幅
     *
     * @return banner - 新闻横幅
     */
    public String getBanner() {
        return banner;
    }

    /**
     * 设置新闻横幅
     *
     * @param banner 新闻横幅
     */
    public void setBanner(String banner) {
        this.banner = banner == null ? null : banner.trim();
    }

    /**
     * 获取图片
     *
     * @return picture_url - 图片
     */
    public String getPictureUrl() {
        return pictureUrl;
    }

    /**
     * 设置图片
     *
     * @param pictureUrl 图片
     */
    public void setPictureUrl(String pictureUrl) {
        this.pictureUrl = pictureUrl == null ? null : pictureUrl.trim();
    }

    /**
     * 获取点击图片的链接
     *
     * @return picture_href - 点击图片的链接
     */
    public String getPictureHref() {
        return pictureHref;
    }

    /**
     * 设置点击图片的链接
     *
     * @param pictureHref 点击图片的链接
     */
    public void setPictureHref(String pictureHref) {
        this.pictureHref = pictureHref == null ? null : pictureHref.trim();
    }

    /**
     * 获取关键字
     *
     * @return keywords - 关键字
     */
    public String getKeywords() {
        return keywords;
    }

    /**
     * 设置关键字
     *
     * @param keywords 关键字
     */
    public void setKeywords(String keywords) {
        this.keywords = keywords == null ? null : keywords.trim();
    }

    /**
     * 获取是否置顶
     *
     * @return show_in_top - 是否置顶
     */
    public Boolean getShowInTop() {
        return showInTop;
    }

    /**
     * 设置是否置顶
     *
     * @param showInTop 是否置顶
     */
    public void setShowInTop(Boolean showInTop) {
        this.showInTop = showInTop;
    }

    /**
     * 获取新闻内容
     *
     * @return content - 新闻内容
     */
    public String getContent() {
        return content;
    }

    /**
     * 设置新闻内容
     *
     * @param content 新闻内容
     */
    public void setContent(String content) {
        this.content = content == null ? null : content.trim();
    }

    /**
     * 获取新闻内容2
     *
     * @return content_url - 新闻内容2
     */
    public String getContentUrl() {
        return contentUrl;
    }

    /**
     * 设置新闻内容2
     *
     * @param contentUrl 新闻内容2
     */
    public void setContentUrl(String contentUrl) {
        this.contentUrl = contentUrl == null ? null : contentUrl.trim();
    }

    /**
     * 获取分类名称
     *
     * @return category_name - 分类名称
     */
    public String getCategoryName() {
        return categoryName;
    }

    /**
     * 设置分类名称
     *
     * @param categoryName 分类名称
     */
    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName == null ? null : categoryName.trim();
    }

    /**
     * 获取是否在首页中显示
     *
     * @return show_in_index - 是否在首页中显示
     */
    public Boolean getShowInIndex() {
        return showInIndex;
    }

    /**
     * 设置是否在首页中显示
     *
     * @param showInIndex 是否在首页中显示
     */
    public void setShowInIndex(Boolean showInIndex) {
        this.showInIndex = showInIndex;
    }

    /**
     * 获取是否图片新闻
     *
     * @return picturl_news - 是否图片新闻
     */
    public Boolean getPicturlNews() {
        return picturlNews;
    }

    /**
     * 设置是否图片新闻
     *
     * @param picturlNews 是否图片新闻
     */
    public void setPicturlNews(Boolean picturlNews) {
        this.picturlNews = picturlNews;
    }

    /**
     * 获取是否允许评论
     *
     * @return allow_comments - 是否允许评论
     */
    public Boolean getAllowComments() {
        return allowComments;
    }

    /**
     * 设置是否允许评论
     *
     * @param allowComments 是否允许评论
     */
    public void setAllowComments(Boolean allowComments) {
        this.allowComments = allowComments;
    }

    /**
     * 获取描述
     *
     * @return description - 描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * 设置描述
     *
     * @param description 描述
     */
    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    /**
     * 获取点击数量
     *
     * @return hits - 点击数量
     */
    public Long getHits() {
        return hits;
    }

    /**
     * 设置点击数量
     *
     * @param hits 点击数量
     */
    public void setHits(Long hits) {
        this.hits = hits;
    }

    /**
     * 获取顶
     *
     * @return praise_num - 顶
     */
    public Long getPraiseNum() {
        return praiseNum;
    }

    /**
     * 设置顶
     *
     * @param praiseNum 顶
     */
    public void setPraiseNum(Long praiseNum) {
        this.praiseNum = praiseNum;
    }

    /**
     * 获取踩
     *
     * @return bad_review_num - 踩
     */
    public Long getBadReviewNum() {
        return badReviewNum;
    }

    /**
     * 设置踩
     *
     * @param badReviewNum 踩
     */
    public void setBadReviewNum(Long badReviewNum) {
        this.badReviewNum = badReviewNum;
    }

    /**
     * 获取审批状态（0 待审批，1 拒绝，2通过，3 进行中，4 已关闭）
     *
     * @return check_status - 审批状态（0 待审批，1 拒绝，2通过，3 进行中，4 已关闭）
     */
    public Integer getCheckStatus() {
        return checkStatus;
    }

    /**
     * 设置审批状态（0 待审批，1 拒绝，2通过，3 进行中，4 已关闭）
     *
     * @param checkStatus 审批状态（0 待审批，1 拒绝，2通过，3 进行中，4 已关闭）
     */
    public void setCheckStatus(Integer checkStatus) {
        this.checkStatus = checkStatus;
    }

    /**
     * 获取是否合作伙伴
     *
     * @return partner_flag - 是否合作伙伴
     */
    public Byte getPartnerFlag() {
        return partnerFlag;
    }

    /**
     * 设置是否合作伙伴
     *
     * @param partnerFlag 是否合作伙伴
     */
    public void setPartnerFlag(Byte partnerFlag) {
        this.partnerFlag = partnerFlag;
    }

    /**
     * 获取技术装备-热门产品-产品名称
     *
     * @return product_name - 技术装备-热门产品-产品名称
     */
    public String getProductName() {
        return productName;
    }

    /**
     * 设置技术装备-热门产品-产品名称
     *
     * @param productName 技术装备-热门产品-产品名称
     */
    public void setProductName(String productName) {
        this.productName = productName == null ? null : productName.trim();
    }

    /**
     * 获取技术装备-热门产品-产品规则型号
     *
     * @return product_model - 技术装备-热门产品-产品规则型号
     */
    public String getProductModel() {
        return productModel;
    }

    /**
     * 设置技术装备-热门产品-产品规则型号
     *
     * @param productModel 技术装备-热门产品-产品规则型号
     */
    public void setProductModel(String productModel) {
        this.productModel = productModel == null ? null : productModel.trim();
    }

    /**
     * 获取技术装备-热门产品-产品特点
     *
     * @return product_features - 技术装备-热门产品-产品特点
     */
    public String getProductFeatures() {
        return productFeatures;
    }

    /**
     * 设置技术装备-热门产品-产品特点
     *
     * @param productFeatures 技术装备-热门产品-产品特点
     */
    public void setProductFeatures(String productFeatures) {
        this.productFeatures = productFeatures == null ? null : productFeatures.trim();
    }

    /**
     * 获取技术装备-热门产品-产品适用范围
     *
     * @return product_scope - 技术装备-热门产品-产品适用范围
     */
    public String getProductScope() {
        return productScope;
    }

    /**
     * 设置技术装备-热门产品-产品适用范围
     *
     * @param productScope 技术装备-热门产品-产品适用范围
     */
    public void setProductScope(String productScope) {
        this.productScope = productScope == null ? null : productScope.trim();
    }

    /**
     * 获取技术装备-热门产品-卖家
     *
     * @return product_owner - 技术装备-热门产品-卖家
     */
    public String getProductOwner() {
        return productOwner;
    }

    /**
     * 设置技术装备-热门产品-卖家
     *
     * @param productOwner 技术装备-热门产品-卖家
     */
    public void setProductOwner(String productOwner) {
        this.productOwner = productOwner == null ? null : productOwner.trim();
    }

    /**
     * 获取技术装备-热门产品-卖家联系人
     *
     * @return product_contact - 技术装备-热门产品-卖家联系人
     */
    public String getProductContact() {
        return productContact;
    }

    /**
     * 设置技术装备-热门产品-卖家联系人
     *
     * @param productContact 技术装备-热门产品-卖家联系人
     */
    public void setProductContact(String productContact) {
        this.productContact = productContact == null ? null : productContact.trim();
    }

    /**
     * 获取技术装备-热门产品-卖家联系电话
     *
     * @return product_contact_phone - 技术装备-热门产品-卖家联系电话
     */
    public String getProductContactPhone() {
        return productContactPhone;
    }

    /**
     * 设置技术装备-热门产品-卖家联系电话
     *
     * @param productContactPhone 技术装备-热门产品-卖家联系电话
     */
    public void setProductContactPhone(String productContactPhone) {
        this.productContactPhone = productContactPhone == null ? null : productContactPhone.trim();
    }

    /**
     * 获取技术装备-热门产品-卖家联系电话
     *
     * @return product_url - 技术装备-热门产品-卖家联系电话
     */
    public String getProductUrl() {
        return productUrl;
    }

    /**
     * 设置技术装备-热门产品-卖家联系电话
     *
     * @param productUrl 技术装备-热门产品-卖家联系电话
     */
    public void setProductUrl(String productUrl) {
        this.productUrl = productUrl == null ? null : productUrl.trim();
    }

    /**
     * 获取新闻编辑人
     *
     * @return editor - 新闻编辑人
     */
    public String getEditor() {
        return editor;
    }

    /**
     * 设置新闻编辑人
     *
     * @param editor 新闻编辑人
     */
    public void setEditor(String editor) {
        this.editor = editor == null ? null : editor.trim();
    }

    /**
     * 获取咨询来源
     *
     * @return source - 咨询来源
     */
    public String getSource() {
        return source;
    }

    /**
     * 设置咨询来源
     *
     * @param source 咨询来源
     */
    public void setSource(String source) {
        this.source = source == null ? null : source.trim();
    }

    /**
     * 获取删除标记
     *
     * @return DEL_FLG - 删除标记
     */
    public Boolean getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标记
     *
     * @param delFlg 删除标记
     */
    public void setDelFlg(Boolean delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建人
     *
     * @return CREATE_USER - 创建人
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建人
     *
     * @param createUser 创建人
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return CREATE_TIME - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取修改人
     *
     * @return UPDATE_USER - 修改人
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置修改人
     *
     * @param updateUser 修改人
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取修改时间
     *
     * @return UPDATE_TIME - 修改时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置修改时间
     *
     * @param updateTime 修改时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", newsId=").append(newsId);
        sb.append(", categoryId=").append(categoryId);
        sb.append(", title=").append(title);
        sb.append(", banner=").append(banner);
        sb.append(", pictureUrl=").append(pictureUrl);
        sb.append(", pictureHref=").append(pictureHref);
        sb.append(", keywords=").append(keywords);
        sb.append(", showInTop=").append(showInTop);
        sb.append(", content=").append(content);
        sb.append(", contentUrl=").append(contentUrl);
        sb.append(", categoryName=").append(categoryName);
        sb.append(", showInIndex=").append(showInIndex);
        sb.append(", picturlNews=").append(picturlNews);
        sb.append(", allowComments=").append(allowComments);
        sb.append(", description=").append(description);
        sb.append(", hits=").append(hits);
        sb.append(", praiseNum=").append(praiseNum);
        sb.append(", badReviewNum=").append(badReviewNum);
        sb.append(", checkStatus=").append(checkStatus);
        sb.append(", partnerFlag=").append(partnerFlag);
        sb.append(", productName=").append(productName);
        sb.append(", productModel=").append(productModel);
        sb.append(", productFeatures=").append(productFeatures);
        sb.append(", productScope=").append(productScope);
        sb.append(", productOwner=").append(productOwner);
        sb.append(", productContact=").append(productContact);
        sb.append(", productContactPhone=").append(productContactPhone);
        sb.append(", productUrl=").append(productUrl);
        sb.append(", editor=").append(editor);
        sb.append(", source=").append(source);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}