package com.ecommerce.information.service;

import com.ecommerce.information.api.dto.ApproveDTO;
import com.ecommerce.information.api.dto.advertisement.AdvertisementAdminCreateDTO;
import com.ecommerce.information.api.dto.advertisement.AdvertisementAdminQueryDTO;
import com.ecommerce.information.api.dto.advertisement.AdvertisementAdminUpdateDTO;
import com.ecommerce.information.api.dto.advertisement.AdvertisementAndBusinessInfoDTO;
import com.ecommerce.information.api.dto.advertisement.AdvertisementAndBusinessInfoQueryDTO;
import com.ecommerce.information.api.dto.advertisement.AdvertisementBusinessInfoDTO;
import com.ecommerce.information.api.dto.advertisement.AdvertisementBusinessInfoQueryDTO;
import com.ecommerce.information.api.dto.advertisement.AdvertisementCheckDTO;
import com.ecommerce.information.api.dto.advertisement.AdvertisementCheckListDTO;
import com.ecommerce.information.api.dto.advertisement.AdvertisementDetailDTO;
import com.ecommerce.information.api.dto.advertisement.AdvertisementListDTO;
import com.ecommerce.information.api.dto.advertisement.AdvertisementQueryDTO;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 广告接口服务
 *
 * <AUTHOR>
 */
public interface IAdvertisementService {
    //含admin的表示是平台可操作的方法，含Seller的表示是卖家可操作的方法
    //卖家和平台在创建和修改的时候可能判断不一样，所以分开
    //各DTO类的属性需要自行完善

    /**
     * 平台创建广告
     * @param advertisementAdminCreateDTO 平台创建广告对象
     * @return 广告信息dto
     */
    AdvertisementDetailDTO createByAdmin(AdvertisementAdminCreateDTO advertisementAdminCreateDTO);

    /**
     * 买家创建广告
     * @param advertisementSellerCreateDTO 买家创建广告对象
     * @return 广告信息dto
     */

    /**
     * 平台编辑广告
     * @param advertisementAdminUpdateDTO 平台编辑广告对象
     * @return 广告信息dto
     */
    AdvertisementDetailDTO updateByAdmin(AdvertisementAdminUpdateDTO advertisementAdminUpdateDTO);

    /**
     * 卖家编辑广告
     * @param advertisementSellerUpdateDTO 买家编辑广告对象
     * @return 广告信息dto
     */
    /**
     * 审批通过（平台审批方法）
     * @param dto 审批广告对象
     */
    void approved(ApproveDTO dto);
    /**
     * 审批被驳回（平台审批方法）
     * @param dto 审批广告对象
     */
    void rejected(ApproveDTO dto);
    /**
     * 启用（平台管理方法）
     * @param id    广告id
     * @param operator    操作者
     */
    void disable(String id,String operator);
    /**
     * 禁用（平台管理方法）
     * @param id    广告id
     * @param operator    操作者
     */
    void enable(String id,String operator);

    /**
     * 平台删除广告
     * @param id    广告id
     * @param operator    操作者
     */
    void delete(String id,String operator);

    /**
     * 平台删除广告
     * @param id    广告id
     * @param operator    操作者
     */

    /**
     * 卖家删除广告
     * @param id    广告id
     * @param operator    操作者
     */

    /**
     * 查询单个广告（不含审批信息）
     * @param id    广告id
     * @return 广告信息dto
     */
    AdvertisementDetailDTO findById(String id);

    /**
     * 查询单个广告详情（含审批信息）
     * @param id    广告id
     * @return 带审批信息的广告dto
     */
    AdvertisementCheckListDTO findDetailById(String id);

    /**
     * 根据spaceCode查询当前可用的广告，供前端显示使用
     * @param spaceCode 广告位编码
     * @return 广告信息dto的List
     */
    List<AdvertisementDetailDTO> findBySpaceCode(String spaceCode);

    /**
     * 根据条件翻页查询(平台广告管理界面列表页使用、平台审批界面列表页使用)
     * @param query 条件集合
     * @return 广告信息dto的翻页信息
     */
    PageInfo<AdvertisementDetailDTO> findAllByAdmin(AdvertisementAdminQueryDTO query);

    /**
     * 根据条件翻页查询(卖家广告管理界面列表页使用)
     * @param query 条件集合
     * @return 广告信息dto的翻页信息
     */

    /**
     * 查询显示广告
     * @param query 条件集合
     * @return 广告集合
     */
    AdvertisementListDTO list(AdvertisementQueryDTO query);
    /**
     * 通过广告id查询审核记录
     * @param id 广告id
     * @return 审核记录集合
     */
    List<AdvertisementCheckDTO> findCheckbyId(String id);

    /**
     * 添加广告时，通过广告位编码和关键字查询对应业务类型的数据：如商品、资讯等等
     * @param dto
     * @return
     */
    List<AdvertisementBusinessInfoDTO> findBusinessBySpaceCodeAndKeyword(AdvertisementBusinessInfoQueryDTO dto);
    /**
     * 通过广告位编码查询对应广告数据和业务id对应的业务数据：如商品、资讯等等
     * @param dto
     * @return
     */
    List<AdvertisementAndBusinessInfoDTO> findAdvertisementAndBusinessInfoBySpaceCode(AdvertisementAndBusinessInfoQueryDTO dto);

    void clearCache(String spaceCode,String operator,String reason);
}
