package com.ecommerce.information.service;

import com.ecommerce.information.api.dto.advertisement.AdverisementSpaceCreateDTO;
import com.ecommerce.information.api.dto.advertisement.AdverisementSpaceDTO;
import com.ecommerce.information.api.dto.advertisement.AdverisementSpaceUpdateDTO;
import com.ecommerce.information.api.dto.advertisement.AdvertisementSpaceQueryDTO;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 *  广告位置服务
 *
 * <AUTHOR>
 **/
public interface IAdvertisementSpaceService {
    /**
     * 新增广告位
     * @param adverisementSpaceCreateDTO 平台创建广告对象
     * @param operator 操作者
     */
    void create(AdverisementSpaceCreateDTO adverisementSpaceCreateDTO,String operator);

    /**
     * 删除广告位
     * @param id 广告位id
     * @param operator 操作者
     */
    void delete(String id,String operator);

    /**
     * 修改广告位
     * @param adverisementSpaceUpdateDTO 更新广告对象
     * @param operator 操作者
     */
    void update(AdverisementSpaceUpdateDTO adverisementSpaceUpdateDTO,String operator);

    /**
     * 根据id查找广告位
     * @param id 广告位id
     * @return 广告位创建dto
     */
    AdverisementSpaceCreateDTO findById(String id);

    AdverisementSpaceCreateDTO findByAdType(String adType);

    /**
     * 查找所有广告位
     * @param query 条件集合
     * @return 广告位的分页信息
     */
    PageInfo<AdverisementSpaceDTO> findAll(AdvertisementSpaceQueryDTO query);

    /**
     * 启用广告位（平台管理方法）
     * @param id    广告位id
     */
    void disable(String id,String operator);
    /**
     * 禁用广告位（平台管理方法）
     * @param id    广告位id
     */
    void enable(String id,String operator);

    /**
     * 根据广告位code查找广告位
     * @param spaceCodeList 广告位编码的List<String>
     * @return 广告位集合
     */
    List<AdverisementSpaceDTO> findBySpaceCode(List<String> spaceCodeList);

    AdverisementSpaceDTO findBySpaceCode(String spaceCode);

    /**
     * 通过关键词查找广告位
     * @param keyWord 关键字（space_code or space_title or ad_type like）
     * @return 广告位集合
     */
    List<AdverisementSpaceDTO> findByKeyWord(String keyWord);
}
