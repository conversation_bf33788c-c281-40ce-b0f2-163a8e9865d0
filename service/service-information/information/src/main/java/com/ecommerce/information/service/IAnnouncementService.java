package com.ecommerce.information.service;

import com.ecommerce.information.api.dto.announcement.*;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 公告管理
 *
 * <AUTHOR>
 */
public interface IAnnouncementService {
    /**
     * 添加一个公告
     * @param announcementCreateDTO 公告创建对象
     * @return
     */
    void create(AnnouncementCreateDTO announcementCreateDTO);
    /**
     * 删除一个公告
     * @param id    公共id
     * @param operator     操作者
     * @return
     */
    void delete(String id,String operator);

    /**
     * 修改一个公告（如果修改审批 通过的公共，审批状态应该改为未审批，等待再次审批）
     * @param announcementUpdateDTO 公告修改对象
     * @return
     */
    void update(AnnouncementUpdateDTO announcementUpdateDTO);

    /**
     * 公告审批
     * @param announcementApprovalDTO 公告审批对象
     */
    void approval(AnnouncementApprovalDTO announcementApprovalDTO);

    /**
     * 禁止（暂停）公告
     * @param id    公共id
     * @param operator  操作人
     */
    void pause(String id,String operator);
    /**
     * 启用已暂停的公告(如果当前时间已经过期，当前公告也不会显示)
     * @param id    公共id
     * @param operator  操作人
     */
    void goon(String id,String operator);

    /**
     * 查询单条公告详情(不含审批记录)
     * @param id    公共id
     * @return 公告DTO
     */
    AnnouncementDTO findById(String id);
    /**
     * 查询单条公告详情(含审批记录)
     * @param id    公共id
     * @return 公告详情DTO
     */
    AnnouncementDetailDTO findDetailById(String id);

    /**
     * 按条件翻页查询公告
     * @param query 公告查询对象
     * @return 公告DTO的分页信息
     */
    PageInfo<AnnouncementDTO> findAll(AnnouncementQueryDTO query);

    /**
     * 查询主页显示的公共，查询对象可以为空
     * @param query 公告查询对象
     * @return 公告DTO的分页信息
     */
    List<AnnouncementDTO> findForIndex(AnnouncementIndexQueryDTO query);

}
