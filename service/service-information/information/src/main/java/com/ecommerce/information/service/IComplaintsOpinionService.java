package com.ecommerce.information.service;

import com.ecommerce.information.api.dto.complaintsOpinion.*;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 投诉建议服务接口
 * <AUTHOR>
 *  2018.11.29 修改
 */
public interface IComplaintsOpinionService {
	/**
	 * 添加投诉建议(投诉方)
	 * @param dto 添加投诉对象
	 */
	void addComplaintsOpinion(ComplaintsOpinionAddDTO dto);

	/**
	 * 处理投诉建议(投诉方)
	 * @param dto 处理投诉对象
	 */
	void addRecordByBuyer(ProcessingRecordsAddDTO dto);
	/**
	 * 处理投诉建议(被投诉方)
	 * @param dto 处理投诉对象
	 */
	void addRecordBySeller(ProcessingRecordsAddDTO dto);
	/**
	 * 处理投诉建议(平台)
	 * @param dto 处理投诉对象
	 */
	void addRecordByPlatform(ProcessingRecordsAddDTO dto);
	/**
	 * 需要平台参与
	 * @param coid 投诉建议id
	 * @param operator 操作者
	 */
	void needPlatform(String coid,String operator);
	/**
	 * 完结投诉建议
	 * @param dto 投诉完成对象
	 */
	void complateComplaintsOpinion(ComplaintsOpinionComplateDTO dto);
	/**
	 * 取消投诉建议
	 * @param dto 投诉完成对象
	 */
	void cancelComplaintsOpinion(ComplaintsOpinionComplateDTO dto);
	/**
	 * 删除投诉建议
	 * @param dto 投诉删除对象
	 */
	void deleteComplaintsOpinion(ComplaintsOpinionDeleteDTO dto);

	/**
	 * 按条件分页查询投诉建议
	 * @param dto 投诉查询对象
	 * @return 投诉建议dto的分页信息
	 */
	PageInfo<ComplaintsOpinionDTO> getComplaintsOpinionListByCondition(ComplaintsQueryDTO dto);

	/**
	 * 查询单条投诉建议（平台）
	 * @param coid 投诉建议id
	 * @return 投诉建议dto
	 */
	public ComplaintsOpinionDTO findById(String coid);
	/**
	 * 查询单条投诉建议(投诉方)
	 * @param coid 投诉建议id
	 * @param memberId 投诉方会员id\
	 * @return 投诉建议dto
	 */
	public ComplaintsOpinionDTO findByIdAndCreateMemberId(String coid,String memberId);

	/**
	 * 查询单条投诉建议(被投诉方)
	 * @param coid 投诉建议id
	 * @param memberId 被投诉方会员id
	 * @return 投诉建议
	 */
	public ComplaintsOpinionDTO findByIdAndDefendantMemberId(String coid,String memberId);
	/**
	 * 查询单条投诉建议(投诉人)
	 * @param coid 投诉建议id
	 * @param accountId 投诉人(create_user)
	 * @return 投诉建议dto
	 */
	public ComplaintsOpinionDTO findByIdAndAccountId(String coid,String accountId);

	////////////////////////////////////处理记录查询///////////////////////////////////////////////////////
	/**
	 * 查询投诉建议处理记录（平台）
	 * @param coid 投诉建议id
	 * @return 投诉意见处理记录的List
	 */
	public List<ProcessingRecordsDTO> findProcessingRecordsById(String coid);
	/**
	 * 查询投诉建议处理记录(投诉方)
	 * @param coid 投诉建议id
	 * @param memberId 投诉方会员id
	 * @return 投诉意见处理记录的List
	 */
	public List<ProcessingRecordsDTO> findProcessingRecordsByIdAndCreateMemberId(String coid,String memberId);

	/**
	 * 查询投诉建议处理记录(被投诉方)
	 * @param coid 投诉建议id
	 * @param memberId 被投诉方会员id
	 * @return 投诉意见处理记录的List
	 */
	public List<ProcessingRecordsDTO> findProcessingRecordsByIdAndDefendantMemberId(String coid,String memberId);
	/**
	 * 查询投诉建议处理记录(投诉人)
	 * @param coid 投诉建议id
	 * @param accountId 投诉人(create_user)
	 * @return 投诉意见处理记录的List
	 */
	public List<ProcessingRecordsDTO> findProcessingRecordsByIdAndAccountId(String coid,String accountId);

}
