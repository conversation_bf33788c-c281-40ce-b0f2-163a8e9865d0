package com.ecommerce.information.service;

import com.ecommerce.information.api.dto.contractbreach.*;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 违约服务接口
 *
 * <AUTHOR>
 */
public interface IContractBreachService {
    /////////////////////违约配置///////////////////////////////////////////////////////////////////////
    /**
     * 创建违约规则
     * @param dto 违约配置创建对象
     * @return 违约配置dto
     */
    ContractBreachConfigDTO createConfig(ContractBreachConfigCreateDTO dto);
    /**
     *修改违约规则
     * @param dto 违约配置修改对象
     * @return 违约配置dto
     */
    ContractBreachConfigDTO updateConfig(ContractBreachConfigUpdateDTO dto);
    /**
     * 删除违约配置
     * @param id 违约配置id
     * @param operator 操作者
     */
    void deleteConfigById(String id,String operator);
    /**
     *根据id和会员id删除违约规则
     * @param id 违约配置id
     * @param memberId 规则归属会员id
     * @param operator 操作者
     */
    void deleteConfigByIdAndMemberId(String id,String memberId,String operator);
    /**
     * 批量删除违约配置
     * @param ids 违约配置id的List
     * @param operator 操作者
     */
    void deleteConfigByIds(List<String> ids, String operator);
    /**
     *根据id和会员id批量删除违约规则
     * @param ids 违约配置id的List
     * @param memberId 规则归属会员id
     * @param operator 操作者
     */
    void deleteConfigByIdsAndMemberId(List<String> ids,String memberId,String operator);
    /**
     *根据id查找违约规则
     * @param id 违约配置id
     * @return 违约配置dto
     */
    ContractBreachConfigDTO findConfigById(String id);
    /**
     * 根据会员id和会员id查找违约规则
     * @param id 违约配置id
     * @return 违约配置的List
     */
    List<ContractBreachConfigDTO> findConfigByMemberId(String id);
    /**
     * 违约规则翻页查询
     * @param dto 违约配置查询对象
     * @return 违约配置的分页信息
     */
    PageInfo<ContractBreachConfigDTO> findAllConfig(ContractBreachConfigQueryDTO dto);

/////////////////////违约记录///////////////////////////////////////////////////////////////////////
    /**
     * 添加违约记录
     * @param dto 添加违约记录对象
     * @return
     */
    void createRecord(ContractBreachRecordCreateDTO dto);
    /**
     *删除违约记录
     * @param id 违约记录id
     * @param operator 操作者
     */
    void deleteRecordById(String id,String operator);
    /**
     * 批量删除违约记录
     * @param ids 违约记录id
     * @param operator 操作者
     */
    void deleteRecordByIds(List<String> ids, String operator);
    /**
     *修改违约记录
     * @param dto 修改违约记录对象
     */
    void updateRecord(ContractBreachRecordUpdateDTO dto);
    /**
     * 根据违约记录id查找违约记录
     * @param recordId 违约记录id
     * @return 违约记录dto
     */
    ContractBreachRecordDTO findRecordById(String recordId);
    /**
     *根据违约记录id和会员id查找违约记录
     * @param recordId 违约记录id
     * @param memberId 会员id
     * @return 违约记录dto
     */
    ContractBreachRecordDTO findRecordByIdAndMemberId(String recordId, String memberId);
    /**
     *违约记录多条件翻页查询
     * @param dto 违约记录查询对象
     * @return 违约记录dto的分页信息
     */
    PageInfo<ContractBreachRecordDTO> findAllRecord(ContractBreachRecordQueryDTO dto);

    /**
     * 违约记录统计结果翻页查询
     * @param dto 违约记录统计结果查询对象
     * @return 违约次数统计的分页信息
     */
    PageInfo<ContractBreachRecordCountDTO> findAllRecordCount(ContractBreachRecordCountQueryDTO dto);

/////////////////////违约记录处理///////////////////////////////////////////////////////////////////////
    /**
     * 取消违约
     * @param dto 取消违约记录对象
     */
    void cancelContractBreak(CancelDTO dto);

    /**
     *发起申诉
     * @param dto 添加违约记录处理对象
     */
    void addAppealReason(ContractBreachRecordCheckAddDTO dto);
    /**
     *买家处理
     * @param dto 添加违约记录处理对象
     */
    void addCheckByBuyer(ContractBreachRecordCheckAddDTO dto);
    /**
     *卖家处理
     * @param dto 添加违约记录处理对象
     */
    void addCheckBySeller(ContractBreachRecordCheckAddDTO dto);
    /**
     *平台处理
     * @param dto 添加违约记录处理对象
     */
    void addCheckByPlatform(ContractBreachRecordCheckAddDTO dto);
    /**
     *买家修改处理结果
     * @param dto 修改违约记录处理对象
     */
    void updateCheckByBuyer(ContractBreachRecordCheckUpdateDTO dto);
    /**
     *卖家修改处理结果
     * @param dto 修改违约记录处理对象
     */
    void updateCheckBySeller(ContractBreachRecordCheckUpdateDTO dto);
    /**
     *平台修改处理结果
     * @param dto 修改违约记录处理对象
     */
    void updateCheckByPlatform(ContractBreachRecordCheckUpdateDTO dto);
    /**
     *根据处理记录id查找单个处理
     * @param id 违约记录处理id
     * @return 处理dto
     */
    ContractBreachRecordCheckDTO findCheckById(String id);
    /**
     *根据违约记录查找处理结果
     * @param recordId 违约记录表id
     * @return 处理dto的list
     */
    List<ContractBreachRecordCheckDTO> findCheckByRecordId(String recordId);
    /**
     * 翻页查询违约记录处理结果
     * @param dto 违约记录处理查询条件
     * @return 处理dto的分页信息
     */
    PageInfo<ContractBreachRecordCheckDTO> findAllCheck(ContractBreachRecordCheckQueryDTO dto);
    /**
     * 根据id删除处理结果
     * @param idList 违约记录处理id的List
     * @param operator 操作者
     */
    void deleteCheckByIds(List<String> idList,String operator);
    /**
     * 根据违约记录删除处理结果
     * @param recordIdList 违约记录表id的List
     * @param operator 操作者
     */
    void deleteCheckByRecordId(List<String> recordIdList,String operator);

}
