package com.ecommerce.information.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.information.api.dto.bill.check.rule.PageQuery;
import com.ecommerce.information.api.dto.credit.CreditQuotaDetailDTO;
import com.ecommerce.information.api.dto.credit.CreditScoreListDTO;
import com.ecommerce.information.api.dto.credit.CreditScoreResultDTO;
import com.ecommerce.information.api.dto.credit.CreditScoreSaveDTO;
import com.ecommerce.information.api.dto.credit.CreditScoreUpdateDTO;
import com.ecommerce.information.api.dto.credit.CreditSourceQueryDTO;

import java.util.List;

/**
 * * Date: Create in 上午10:34 21/7/6
 * <AUTHOR>
 */
public interface ICreditScoreService {

    /**
     * 创建新的信用的分时,返回指标模板
     * @param creditSourceQueryDTO
     * @return
     */
    ItemResult<List<CreditQuotaDetailDTO>> fetchQuotaTemplate(CreditSourceQueryDTO creditSourceQueryDTO);

    /**
     * 保持新建的得分
     * @param creditScoreSaveDTO
     * @return
     */
    ItemResult<String> saveCreditScore(CreditScoreSaveDTO creditScoreSaveDTO);

    /**
     * 删除得分
     * @param creditScoreUpdateDTO
     */
    ItemResult<Void> deleteCreditScore(CreditScoreUpdateDTO creditScoreUpdateDTO);

    /**
     * 修改状态
     * @param creditScoreUpdateDTO
     */
    ItemResult<Void> updateCreditStatus(CreditScoreUpdateDTO creditScoreUpdateDTO);

    /**
     * 主键查看积分详情
     * @param creditScoreId
     * @return
     */
    ItemResult<CreditScoreResultDTO> queryScoreResultById(String creditScoreId);

    /**
     * 分页查询得分
     * @param pageQuery
     * @return
     */
    ItemResult<PageData<CreditScoreListDTO>> pageQueryByCond(PageQuery<CreditSourceQueryDTO> pageQuery);

    /**
     * 列表查询得分
     * @param creditSourceQueryDTO
     * @return
     */
    ItemResult<List<CreditScoreListDTO>> listQueryByCond(CreditSourceQueryDTO creditSourceQueryDTO);

}
