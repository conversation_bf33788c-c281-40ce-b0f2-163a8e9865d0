package com.ecommerce.information.service;


import com.ecommerce.information.api.dto.favorite.*;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * @Description 收藏对外服务接口
 * <AUTHOR>
 *
 */
public interface IFavoriteCollectionService {
	/**
	 * 添加收藏
	 * @param favoriteCollectionAddDTO	新增收藏DTO
	 */
	void add(FavoriteCollectionAddDTO favoriteCollectionAddDTO);

	/**
	 * 批量添加收藏
	 * @param favoriteCollectionAddDTOList 新增收藏DTO的List
	 */
	void addList(List<FavoriteCollectionAddDTO> favoriteCollectionAddDTOList);
	/**
	 * 添加商品浏览记录
	 * @param favoriteCollectionAddBrowseRecordDTO	添加商品浏览记录DTO
	 */
	void addBrowseRecord(FavoriteCollectionAddBrowseRecordDTO favoriteCollectionAddBrowseRecordDTO);

	/**
	 * 批量添加商品浏览记录
	 * @param list 添加商品浏览记录DTO的List
	 */
	void addBrowseRecordList(List<FavoriteCollectionAddBrowseRecordDTO> list);
	/**
	 * 添加商品收藏
	 * @param favoriteCollectionAddGoodsDTO	新增商品收藏DTO
	 */
	void addGoods(FavoriteCollectionAddGoodsDTO favoriteCollectionAddGoodsDTO);

	/**
	 * 批量添加商品收藏
	 * @param list 新增商品收藏DTO的List
	 */
	void addGoodsList(List<FavoriteCollectionAddGoodsDTO> list);
	/**
	 * 添加店铺收藏
	 * @param favoriteCollectionAddShopDTO	新增店铺收藏DTO
	 */
	void addShop(FavoriteCollectionAddShopDTO favoriteCollectionAddShopDTO);

	/**
	 * 批量添加店铺收藏
	 * @param list 新增店铺收藏DTO的List
	 */
	void addShopList(List<FavoriteCollectionAddShopDTO> list);
	/**
	 * 添加资讯收藏
	 * @param favoriteCollectionAddInformationDTO	新增咨询收藏DTO
	 */
	void addInformation(FavoriteCollectionAddInformationDTO favoriteCollectionAddInformationDTO);

	/**
	 * 批量添加资讯收藏
	 * @param list 新增咨询收藏DTO的List
	 */
	void addInformationList(List<FavoriteCollectionAddInformationDTO> list);

	/**
	 * 移除收藏(逻辑删除即可)
	 * @param id	主键id
	 * @param operator 操作者
	 */
	void removeById(String id, String operator);
    /**
     * 置顶
     * @param id	主键id
	 * @param operator 操作者
	 */
	void top(String id, String operator);
	/**
	 * 移除收藏
	 * @param ids	主键ids
	 * @param operator 操作者
	 */
	void removeByIds(List<String> ids, String operator);

	/**
	 * 移除收藏
	 * @param favoriteType	收藏类型
	 * @param objectIds	收藏对象ids
	 * @param operator	操作人id
	 */
	void removeByObjectIds(Integer favoriteType,List<String> objectIds,String memberId, String operator);
	/**
	 * 根据会员id和收藏类型查询其收藏
	 * @param query 查询条件集合
	 * @return	会员收藏
	 */
	PageInfo<FavoriteCollectionDTO> findByMemberId(FavoriteCollectionQueryDTO query);

	/**
	 * 查询会员id是否有收藏该对象
	 * @param memberId	会员id
	 * @param favoriteType	收藏类型
	 * @param objectId	对象id
	 * @return	true则表示为存在
	 */
	boolean favoriteExists(String memberId, int favoriteType, String objectId);

    /**
     * 查询会员id是否有收藏该商品
     * @param memberId	会员id
     * @param objectId	商品id
     * @return	true则表示为存在
     */
    boolean favoriteGoodsExists(String memberId, String objectId);

    /**
     * 查询会员id是否有收藏该店铺
     * @param memberId	会员id
     * @param objectId	店铺的id
     * @return	true则表示为存在
     */
    boolean favoriteShopExists(String memberId, String objectId);

    /**
     * 查询会员id是否有收藏该资讯
     * @param memberId	会员id
     * @param 	objectId	 具体信息id
     * @return	true则表示为存在
     */
    boolean favoriteInformationExists(String memberId, String objectId);

	/**
	 * 根据主键ID查询收藏表
	 * @param id 主键ID
	 * @return	收藏表
	 */
	FavoriteCollectionDTO findFavoriteCollectionDTOByID(String id);
	/**
	 * 根据收藏类型和对象ID查询收藏表
	 */
	List<FavoriteCollectionDTO> findFavoriteCollectionByFavoriteTypeAndObjectId(Integer favoriteType,List<String> objectIds,String memberId);
}
