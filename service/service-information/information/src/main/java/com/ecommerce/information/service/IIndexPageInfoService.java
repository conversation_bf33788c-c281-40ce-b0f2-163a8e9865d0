package com.ecommerce.information.service;

import com.ecommerce.information.api.dto.indexPage.*;

import java.util.List;

/**
 * <AUTHOR>
 * @created 13:41 23/01/2019
 * @description TODO
 */
public interface IIndexPageInfoService {

    /**
     * 按照类型查找
     * @param displayType
     * @return
     */
    List<IndexPageInfoDTO> findByDisplayType(String displayType);

    /**
     * 调整显示顺序
     * @param orderList 数据实体列表
     * @param displayType 显示类型
     * @param operatorId 操作者
     */
    void updateOrder(List<IndexPageInfoOrder> orderList, String displayType, String operatorId);

    /**
     * 保存
     * @param indexPageInfoDTO 数据实体
     * @param operatorId 操作者
     */
    void save(IndexPageInfoDTO indexPageInfoDTO, String operatorId);

    /**
     * 标记删除
     * @param indexPageInfoId 主键
     * @param displayType 显示类型
     * @param operatorId 操作者
     */
    void deleteById(String indexPageInfoId, String displayType, String operatorId);

    /**
     * 批量保存
     * @param indexPageInfoDTOList 实体列表
     * @param displayType 显示类型
     * @param operatorId 操作者
     */
    void batchSave(List<IndexPageInfoDTO> indexPageInfoDTOList, String displayType, String operatorId);

}
