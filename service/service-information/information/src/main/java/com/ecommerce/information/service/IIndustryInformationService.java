package com.ecommerce.information.service;

import com.ecommerce.information.api.dto.IndustryInformationDTO;
import com.github.pagehelper.PageInfo;

public interface IIndustryInformationService {

	
	/**
	 * 添加行业资讯
	 * @param industryInformationDTO 行业资讯对象
	 * @return 是否添加成功
	 */
	public Boolean addIndustryInformation(IndustryInformationDTO industryInformationDTO);
	
	
	
	/**
	 * 条件查询行业资讯列表
	 * @param industryInformationDTO 行业资讯对象
	 * @return 资讯详情分页信息
	 */
	public PageInfo<IndustryInformationDTO> selectIndustryInformationList(IndustryInformationDTO industryInformationDTO);
	
	
	
	/**
	 * 查询行业资讯详情
	 * @param id 行业资讯id
	 * @return 资讯详情
	 */
	public IndustryInformationDTO selectIndustryInformationById(String id);
	
	
	
	/**
	 * 删除行业资讯
	 * @param id 行业资讯id
	 * @return 是否删除行业资讯
	 */
	public Boolean deleteIndustryInformationById(String id);
	
	/**
	 * 修改行业资讯
	 * @param industryInformationDTO 行业资讯对象
	 * @return 是否修改行业资讯
	 */
	public Boolean updateIndustryInformation(IndustryInformationDTO industryInformationDTO);
	
	
	
}
