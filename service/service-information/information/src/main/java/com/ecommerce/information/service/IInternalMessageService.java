package com.ecommerce.information.service;

import com.ecommerce.information.api.dto.internalmessage.*;
import com.github.pagehelper.PageInfo;

/**
 * 站内信服务
 * 站内信服务
 *
 * <AUTHOR>
 */
public interface IInternalMessageService {
    /**
     * 发送站内信
     * @param internalMessageCreateDTO 站内信创建对象
     * @return 消息信息dto
     */
    Boolean sendMessage(InternalMessageCreateDTO internalMessageCreateDTO);
    /**
     * 删除消息
     * @param internalMessageDeleteDTO 站内信删除对象
     * @return
     */
    Boolean delete(InternalMessageDeleteDTO internalMessageDeleteDTO);
    /**
     * 修改消息读取状态为已读
     * @param internalMessageReadStatusUpdateDTO 站内信读取状态修改对象
     * @return
     */
    Boolean updateReadStatus(InternalMessageReadStatusUpdateDTO internalMessageReadStatusUpdateDTO);

    /**
     * 根据条件翻页查询
     * @param internalMessageQueryDTO 站内信查询对象
     * @return 消息集合分页信息
     */
    PageInfo<InternalMessageDTO> findAll(InternalMessageQueryDTO internalMessageQueryDTO);

    /**
     * 根据账号查询未读消息数量
     * @param accountId 收件人
     * @return 统计消息信息
     */
    Integer count(String accountId);

    /**
     * 根据消息id查询消息详情
     * @param id 消息id
     * @return 消息详情dto
     */
    InternalMessageDTO findById(String id);


}
