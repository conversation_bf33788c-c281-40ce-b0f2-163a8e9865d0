package com.ecommerce.information.service;

import com.ecommerce.information.api.dto.CategoryTreeDTO;
import com.ecommerce.information.api.dto.memberIntegral.*;
import com.ecommerce.information.api.dto.memberIntegralConfig.MemberIntegralConfigDTO;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 会员积分等级接口服务
 *
 * <AUTHOR>
 */
public interface IMemberIntegralService {

    /**
     * 查询会员积分等级信息
     * @param memberId 会员id
     * @return 会员积分信息dto
     */
    MemberIntegralDTO findByMemberId(String memberId);

    /**
     * 查询会员积分等级信息
     * @param memberCode 会员code
     * @return 会员积分信息dto
     */
    MemberIntegralDTO findByMemberCode(String memberCode);

    /**
     * 批量查询会员积分等级信息
     * @param memberIds 会员id集合
     * @return 会员积分信息dto集合
     */
    List<MemberIntegralDTO> findByMemberIds(List<String> memberIds);

    /**
     * 批量查询会员积分等级信息
     * @param memberCodes 会员code集合
     * @return 会员积分信息dto集合
     */
    List<MemberIntegralDTO> findByMemberCodes(List<String> memberCodes);

    /**
     * 根据条件翻页查询积分信息
     * @param dto 会员积分查找dto
     * @return 翻页查询结果
     */
    PageInfo<MemberIntegralDTO> findAllIntegral(IntegralQueryDTO dto);

    /**
     * 根据条件翻页查询积分变更记录
     * 显示 日期 积分 说明
     * 1笔订单显示一个总的积分
     * 1笔订单积分 = changeInfo表 + frequencInfo表 (sum union group by,按照订单number标记)
     * 1笔订单 -> N种商品 -> 按吨或立方算的积分 和 频率奖励的积分
     * @param dto 会员积分查找dto
     * @return 翻页查询结果
     */
    PageInfo<MemberIntegralChangeInfoDTO> findAllChangeInfo(IntegralChangeInfoQueryDTO dto);


    /**
     * 删除会员积分记录
     * @param dto
     */
    void deleteIntegralChangeInfo(DeleteIntegralChangeInfoDTO dto);

    /**
     * 手动刷新积分的方法
     * 1.recalculateAll = true
     *   缩小计算范围
     * 2.通过id或者code刷新
     * 3.一个积分不同会员类型,等级可能不一样
     * @param dto 积分记录信息
     * @return true表示刷新成功
     */
    Boolean recalculateIntegral(RecalculateIntegralDTO dto);

    /**
     * 查询2张积分表的配置详情
     * @return 配置详情
     */
    MemberIntegralConfigDTO findMemberIntegralConfig(List<CategoryTreeDTO> goods);

    /**
     *  更新积分表的配置
     * @param config 配置
     * @return ture表示更新成功
     */
    Boolean updateMemberIntegralConfig(MemberIntegralConfigDTO config);
}
