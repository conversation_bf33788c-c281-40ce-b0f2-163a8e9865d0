package com.ecommerce.information.service;

import com.ecommerce.information.api.dto.ApproveDTO;
import com.ecommerce.information.api.dto.news.NewsCreateDTO;
import com.ecommerce.information.api.dto.news.NewsDTO;
import com.ecommerce.information.api.dto.news.NewsQueryDTO;
import com.ecommerce.information.api.dto.news.NewsUpdateDTO;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 装配式建筑服务接口
 *
 * <AUTHOR>
 */
public interface INewsAssemblyBuildingService {

    /**
     * 新增新闻
     * @param dto 新闻添加对象
     * @return 消息dto
     */
    NewsDTO create(NewsCreateDTO dto);

    /**
     * 根据Id删除新闻
     * @param id 新闻id
     * @param operator 操作者
     */
    void deleteById(String id, String operator);

    /**
     * 批量删除新闻
     * @param idList 新闻id的List
     * @param operator 操作者
     */
    void deleteByIds(List<String> idList, String operator);

    /**
     * 更新新闻
     * @param dto 新闻更新对象
     * @return 消息dto
     */
    NewsDTO update(NewsUpdateDTO dto);

    /**
     * 根据Id查找新闻
     * @param id 新闻id
     * @return 消息dto
     */
    NewsDTO findById(String id);

    /**
     * 批量查找新闻
     * @param idList 新闻id的List
     * @return 消息dto集合
     */
    List<NewsDTO> findByIds(List<String> idList);

    /**
     * 根据分类查询
     * @param categoryList 新闻分类的List
     * @return 消息dto集合
     */
    List<NewsDTO> findByCategory(List<String> categoryList);

    /**
     * 清除缓存数据
     * @param operator 操作者
     */
    void clearCache(String operator);
    /**
     * 翻页查询
     * @param query 查询条件
     * @return 消息dto翻页查询
     */
    PageInfo<NewsDTO> findAll(NewsQueryDTO query);

    /**
     * 顶　＋１
     * @param id 新闻id
     * @param operator 操作者
     */
    void top(String id, String operator);

    /**
     * 踩　＋１
     * @param id 新闻id
     * @param operator 操作者
     */
    void stepOn(String id, String operator);

    /**
     * 点击量　＋１
     * @param id 新闻id
     * @param operator 操作者
     */
    void addHits(String id, String operator);

    /**
     * 审批通过
     * @param dto 审批对象
     */
    void approved(ApproveDTO dto);
    /**
     * 审批被驳回
     * @param dto 审批对象
     */
    void rejected(ApproveDTO dto);

    /**
     * 查找审批历史
     * @param newsId 审批记录id
     * @return 审批历史详情
     */
    List<ApproveDTO> findApproveListById(String newsId);


    /////////////////////////////////////////////////////

    /**
     * 最新新闻
     * @param size 显示数量
     * @return 最新新闻
     */
    List<NewsDTO> latestNews(Integer size);
    /**
     * 全国新闻
     * @param size 显示数量
     * @return 全国新闻
     */
    List<NewsDTO> nationalNews(Integer size);
    /**
     * 企业新闻
     * @param size 显示数量
     * @return 企业新闻
     */
    List<NewsDTO> corporateNews(Integer size);
    /**
     * 政策法规
     * @param size 显示数量
     * @return 政策法规的新闻
     */
    List<NewsDTO> policiesAndRegulations(Integer size);
    /**
     * 技术装备-热门产品的新闻
     * @param size 显示数量
     * @return 热门产品的新闻
     */
    List<NewsDTO> hotProductNews(Integer size);
    /**
     * 技术装备-最新技术新闻
     * @param size 显示数量
     * @return 新闻DTO集合
     */
    List<NewsDTO> technologyProjectNews(Integer size);


    /**
     * 装配式建筑-投资开发
     * @param size 显示数量
     * @return 投资开发新闻
     */
    List<NewsDTO> investProjectNews(Integer size);

    /**
     * 装配式建筑-工业化生产
     * @param size 显示数量
     * @return 工业化生产新闻
     */
    List<NewsDTO> prodProjectNews(Integer size);

    /**
     * 装配式建筑-标准化设计
     * @param size 显示数量
     * @return 标准化设计新闻
     */
    List<NewsDTO> designProjectNews(Integer size);

    /**
     * 装配式建筑-装配化施工
     * @param size 显示数量
     * @return 装配化施工新闻
     */
    List<NewsDTO> buildProjectNews(Integer size);

    /**
     * 装配式建筑-一体化装修
     * @param size 显示数量
     * @return 一体化装修新闻
     */
    List<NewsDTO> decorateProjectNews(Integer size);

    /**
     * 装配式建筑-信息化管理
     * @param size 显示数量
     * @return 信息化管理新闻
     */
    List<NewsDTO> manageProjectNews(Integer size);

    /**
     * 装配式建筑-智能化应用
     * @param size 显示数量
     * @return 智能化应用新闻
     */
    List<NewsDTO> applicationProjectNews(Integer size);

    /**
     * 装配式建筑-项目展示
     * @param size 显示数量
     * @return 项目展示新闻
     */
    List<NewsDTO> showProjectNews(Integer size);

    /**
     * 装配式建筑-战略合作伙伴
     * @param size 显示数量
     * @return 战略合作伙伴新闻
     */
    List<NewsDTO> partnerProjectNews(Integer size);
}
