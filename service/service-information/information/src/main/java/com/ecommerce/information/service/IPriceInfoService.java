package com.ecommerce.information.service;

import com.ecommerce.information.api.dto.priceInfo.*;
import com.github.pagehelper.PageInfo;
import org.springframework.web.multipart.MultipartFile;

import java.io.OutputStream;
import java.util.List;

/**
 * 、价格信息
 *
 * <AUTHOR>
 */
public interface IPriceInfoService {
    /**
     * 添加记录
     * @param dto 价格信息添加对象
     * @return 价格信息
     */
    PriceInfoDTO add(PriceInfoAddDTO dto);

    /**
     * 导入记录
     * @param file 导入excel
     * @param operator 操作者
     */
    void excelImport(MultipartFile file,String operator);

    /**
     * 获取excel导入模板
     * @return 输出流
     */
    OutputStream getExcelTemplete();

    /**
     * 根据id删除记录
     * @param id 价格信息id
     * @param operator 操作者
     */
    void deleteById(String id ,String operator);

    /**
     * 根据id集合删除记录
     * @param ids 价格信息id的List
     * @param operator 操作者
     */
    void deleteByIds(List<String> ids, String operator);

    /**
     * 更新记录
     * @param dto 价格信息更新对象
     * @return 价格信息
     */
    PriceInfoDTO update(PriceInfoUpdateDTO dto);
    /**
     * 根据id查询单条记录
     * @param id 价格信息id
     * @return 价格信息
     */
    PriceInfoDTO findById(String id);

    /**
     * 翻页查询
     * @param dto 价格信息查询对象
     * @return 价格信息分页
     */
    PageInfo<PriceInfoDTO> findAll(PriceInfoQueryDTO dto);

    /**
     * 资讯首页查显示的
     * @param size 显示数量
     * @return 价格信息分页
     */
    List<PriceInfoDTO> findCement(Integer size);

    /**
     * 资讯首页查显示的
     * @param size 显示数量
     * @return 价格信息集合
     */
    List<PriceInfoDTO> findConcrete(Integer size);

    /**
     * 查询品牌
     * @param brand 品牌
     * @return 品牌集合
     */
    List<String> findCementBrand(String brand);
    /**
     * 查询品种
     * @param varieties 品种
     * @return 品种集合
     */
    List<String> findCementVarieties(String varieties);
    /**
     * 查询品牌
     * @param brand 品牌
     * @return 品牌集合
     */
    List<String> findConcreteBrand(String brand);
    /**
     * 查询品种
     * @param varieties 品种
     * @return 品种集合
     */
    List<String> findConcreteVarieties(String varieties);
    /**
     * 价格走势图数据查询
     * @param dto 价格趋势查询对象
     */
    List<PriceInfoDTO> findPriceChartData(PriceChartDataQueryDTO dto);

    /**
     * 查询有数据的省的名称
     * @return
     */
    List<String> findProvince();

    /**
     *
     * @param dto
     * @return
     */
    ProvincePriceInfoDTO findProvincePriceInfo(ProvincePriceInfoQueryDTO dto);

    ProvincePriceInfoIndexDTO findProvincePriceInfo2(ProvincePriceInfoQueryDTO dto);

}
