package com.ecommerce.information.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.information.api.dto.purchase.PurchaseNoticeAddDTO;
import com.ecommerce.information.api.dto.purchase.PurchaseNoticeAnonDTO;
import com.ecommerce.information.api.dto.purchase.PurchaseNoticeCondDTO;
import com.ecommerce.information.api.dto.purchase.PurchaseNoticeDeleteDTO;
import com.ecommerce.information.api.dto.purchase.PurchaseNoticeDetailDTO;
import com.ecommerce.information.api.dto.purchase.PurchaseNoticeListDTO;
import com.ecommerce.information.api.dto.purchase.PurchaseNoticeStatusDTO;
import com.ecommerce.information.api.dto.purchase.PurchaseNoticeUpdateDTO;

import java.util.List;

/**
 * 采购公告服务接口
 *  IPurchaseNoticeService
 *
 * <AUTHOR>
 */
public interface IPurchaseNoticeService {

    /**
     * 采购公告添加
     * @param purchaseNoticeAddDTO
     * @return
     */
    ItemResult<String> createNotice(PurchaseNoticeAddDTO purchaseNoticeAddDTO);


    /**
     * 批量删除公告
     * @param purchaseNoticeDeleteDTO
     * @return
     */
    ItemResult<Void> deleteNoticeList(PurchaseNoticeDeleteDTO purchaseNoticeDeleteDTO);

    /**
     * 匿名查看公告
     * @param purchaseNoticeId
     * @return
     */
    ItemResult<PurchaseNoticeAnonDTO> anonQueryById(String purchaseNoticeId);

    /**
     * 明细查询
     * @param purchaseNoticeId
     * @return
     */
    ItemResult<PurchaseNoticeDetailDTO> queryDetailById(String purchaseNoticeId);

    /**
     * 分页条件查询
     * @param purchaseNoticeCondDTO
     * @return
     */
    ItemResult<PageData<PurchaseNoticeListDTO>> queryByCond(PurchaseNoticeCondDTO purchaseNoticeCondDTO);

    /**
     * 更新公告内容
     * @param purchaseNoticeUpdateDTO
     * @return
     */
    ItemResult<Void> updateNoticeInfo(PurchaseNoticeUpdateDTO purchaseNoticeUpdateDTO);

    /**
     * 更改公告状态
     * @param purchaseNoticeStatusDTO
     * @return
     */
    ItemResult<Void> updateStatus(PurchaseNoticeStatusDTO purchaseNoticeStatusDTO);

    /**
     * SRM推送采购信息添加
     */
    ItemResult<String> srmCreateNotice(List<PurchaseNoticeAddDTO> purchaseNoticeAddDTOList);

}
