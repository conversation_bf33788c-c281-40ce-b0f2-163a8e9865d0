package com.ecommerce.information.service;

import com.ecommerce.information.api.dto.ApproveDTO;
import com.ecommerce.information.api.dto.advertisement.AdvertisementDetailDTO;
import com.ecommerce.information.api.dto.tendering.*;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 招标服务接口
 * <AUTHOR>
 *
 */
public interface ITenderingInfoService {


	/**
	 * 添加一个招标信息
	 * @param tenderingDTO 招标信息对象
	 * @return 返回主键
	 */
	public String addTenderingInfo(TenderingDTO tenderingDTO);
	
	
	/**
	 * 修改招标信息
	 * @param tenderingDTO 招标信息对象
	 */
	public void modifyTenderingInfo(TenderingDTO tenderingDTO);
	
	
	
	/**
	 * 获取招标信息详情(含商品信息)
	 * @param tid 招标信息id
	 * @return 招标信息详情
	 */
	public TenderingDTO getTenderingDetailInfoByTid(String tid);

	/**
	 * 获取招标信息详情
	 * @param tid 招标信息id
	 * @param memberId 企业id
	 * @return 招标信息详情
	 */
	public TenderingDTO getTenderingDetailInfoByTidAndMemberId(String tid,String memberId);
	/**
	 * 获取招标信息详情(不含商品信息)
	 * @param tid 招标信息id
	 * @return 招标信息详情
	 */
	public TenderingInfoDTO getTenderingInfoByTid(String tid);

	/**
	 * 获取招标信息详情(不含商品信息)
	 * @param tid 招标信息id
	 * @param memberId 企业id
	 * @return 招标信息详情
	 */
	public TenderingInfoDTO getTenderingInfoByTidAndMemberId(String tid,String memberId);
	/**
	 * 获取招标信息对应的商品信息
	 * @param tid 招标信息id
	 * @return 招标信息详情
	 */
	public List<TenderingProductInfoDTO> getTenderingProductByTid(String tid);

	/**
	 * 获取招标广告信息
	 * @param tid 招标信息id
	 * @return 招标信息详情
	 */
	public AdvertisementDetailDTO getTenderingAdvertisingByTid(String tid);

	
	/**
	 * 分页获取招标信息
	 * @param searchTenderingDTO 招标信息查询对象
	 * @return 招标信息详情
	 */
	public PageInfo<TenderingDTO> getTenderingPageInfo(SearchTenderingDTO searchTenderingDTO);

	/**
	 * 审批通过（平台审批方法）
	 * @param dto 审批对象
	 */
	void approved(ApproveDTO dto);
	/**
	 * 审批被驳回（平台审批方法）
	 * @param dto 审批对象
	 */
	void rejected(ApproveDTO dto);

	/**
	 * 查找审批历史
	 * @param tenderingInfoId 审批记录id
	 * @return 审批历史集合
	 */
	List<ApproveDTO> findApproveListById(String tenderingInfoId);
	
	/**
	 * 导出筛选过后的招标信息
	 * @param searchTenderingDTO 招标信息查询对象
	 */
	public void exportTendering(SearchTenderingDTO searchTenderingDTO);

	/**
	 * 根据id删除
	 * @param id 招标信息id
	 * @param operator 操作者
	 */
	void deleteById(String id,String operator);

	/**
	 * 根据Id批量删除
	 * @param ids 招标信息id集合
	 * @param operator 操作者
	 */
	void deleteByIds(List<String> ids,String operator);

	/**
	 * 根据id和企业Id删除
	 * @param id 招标信息id
	 * @param memberId 企业id
	 * @param operator 操作者
	 */
	void deleteByIdAndMemberId(String id,String memberId,String operator);

	/**
	 *根据id和企业Id批量删除
	 * @param ids 招标信息id的List
	 * @param memberId 企业id
	 * @param operator 操作者
	 */
	void deleteByIdsAndMemberId(List<String> ids,String memberId,String operator);

	/**
	 * 通过email翻页查找招标信息
	 * @param dto 邮箱查找对象
	 * @return 招标信息分页信息
	 */
	PageInfo<TenderingEmallDTO> findForEmall(TenderingEmallDTO dto);
}
