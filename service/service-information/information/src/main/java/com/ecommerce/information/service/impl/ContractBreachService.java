package com.ecommerce.information.service.impl;

import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.information.api.dto.contractbreach.*;
import com.ecommerce.information.biz.IContractBreachConfigBiz;
import com.ecommerce.information.biz.IContractBreachRecordBiz;
import com.ecommerce.information.biz.IContractBreachRecordProcessBiz;
import com.ecommerce.information.dao.vo.ContractBreachConfig;
import com.ecommerce.information.dao.vo.ContractBreachRecord;
import com.ecommerce.information.dao.vo.ContractBreachRecordProcess;
import com.ecommerce.information.service.IContractBreachService;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date: 24/11/2018 16:11
 * @DESCRIPTION:
 */
@Slf4j
@Service
public class ContractBreachService implements IContractBreachService {

    @Autowired
    private IContractBreachConfigBiz contractBreachConfigBiz;

    @Autowired
    private IContractBreachRecordBiz contractBreachRecordBiz;

    @Autowired
    private IContractBreachRecordProcessBiz contractBreachRecordProcessBiz;

    @Override
    public ContractBreachConfigDTO createConfig(ContractBreachConfigCreateDTO dto) {
        return convert(contractBreachConfigBiz.createConfig(convert(dto,ContractBreachConfig.class)),ContractBreachConfigDTO.class);
    }

    @Override
    public ContractBreachConfigDTO updateConfig(ContractBreachConfigUpdateDTO dto) {
        return convert(contractBreachConfigBiz.updateConfig(convert(dto,ContractBreachConfig.class)),ContractBreachConfigDTO.class);
    }

    @Override
    public void deleteConfigById(String id, String operator) {
        contractBreachConfigBiz.deleteConfigById(id,operator);
    }

    @Override
    public void deleteConfigByIdAndMemberId(String id, String memberId, String operator) {
        contractBreachConfigBiz.deleteConfigByIdAndMemberId(id,memberId,operator);
    }

    @Override
    public void deleteConfigByIds(List<String> ids, String operator) {
        contractBreachConfigBiz.deleteConfigByIds(ids,operator);
    }

    @Override
    public void deleteConfigByIdsAndMemberId(List<String> ids, String memberId, String operator) {
        contractBreachConfigBiz.deleteConfigByIdsAndMemberId(ids,memberId,operator);
    }

    @Override
    public ContractBreachConfigDTO findConfigById(String id) {
        return convert(contractBreachConfigBiz.findConfigById(id),ContractBreachConfigDTO.class);
    }

    @Override
    public List<ContractBreachConfigDTO> findConfigByMemberId(String id) {
        List<ContractBreachConfig> list = contractBreachConfigBiz.findConfigByMemberId(id);
        return list == null || list.isEmpty() ? null : list.stream().map(item ->convert(item,ContractBreachConfigDTO.class)).toList();
    }

    @Override
    public PageInfo<ContractBreachConfigDTO> findAllConfig(ContractBreachConfigQueryDTO dto) {
        PageInfo<ContractBreachConfig> pageInfo = contractBreachConfigBiz.findAllConfig(dto);
        PageInfo<ContractBreachConfigDTO> result = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo,result);
        return result;
    }

    @Override
    public void createRecord(ContractBreachRecordCreateDTO dto) {
        contractBreachRecordBiz.createRecord(convert(dto,ContractBreachRecord.class));
    }

    @Override
    public void deleteRecordById(String id, String operator) {
        contractBreachRecordBiz.deleteRecordById(id,operator);
    }

    @Override
    public void deleteRecordByIds(List<String> ids, String operator) {
        contractBreachRecordBiz.deleteRecordByIds(ids,operator);
    }

    @Override
    public void updateRecord(ContractBreachRecordUpdateDTO dto) {
        contractBreachRecordBiz.updateRecord(convert(dto,ContractBreachRecord.class));
    }

    @Override
    public ContractBreachRecordDTO findRecordById(String recordId) {
        return convert(contractBreachRecordBiz.findRecordById(recordId),ContractBreachRecordDTO.class);
    }

    @Override
    public ContractBreachRecordDTO findRecordByIdAndMemberId(String recordId, String memberId) {
        return convert(contractBreachRecordBiz.findRecordById(recordId,memberId),ContractBreachRecordDTO.class);
    }

    @Override
    public PageInfo<ContractBreachRecordDTO> findAllRecord(ContractBreachRecordQueryDTO dto) {
        PageInfo<ContractBreachRecord> pageInfo = contractBreachRecordBiz.findAllRecord(dto);
        PageInfo<ContractBreachRecordDTO> result = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo,result);
        return result;
    }

    @Override
    public PageInfo<ContractBreachRecordCountDTO> findAllRecordCount(ContractBreachRecordCountQueryDTO dto) {
        return contractBreachRecordBiz.findAllRecordCount(dto);
    }

    @Override
    public void cancelContractBreak(CancelDTO dto) {
        if (CsStringUtils.isBlank(dto.getContractBreachRecordId())) {
            log.info("contractBreachRecordId is null");
            return;
        }
        Date now = new Date();
        ContractBreachRecord record = contractBreachRecordBiz.findRecordById(dto.getContractBreachRecordId());
        record.setCancelFlg(true);
        record.setUpdateTime(now);
        record.setUpdateUser(dto.getUpdateUser());
        contractBreachRecordBiz.updateRecord(record);
        ContractBreachRecordProcess cancel = new ContractBreachRecordProcess();
        //TODO ...
        contractBreachRecordProcessBiz.addCheckByBuyer(cancel);

    }

    @Override
    public void addAppealReason(ContractBreachRecordCheckAddDTO dto) {
        // TODO
    }

    @Override
    public void addCheckByBuyer(ContractBreachRecordCheckAddDTO dto) {
        contractBreachRecordProcessBiz.addCheckByBuyer(convert(dto,ContractBreachRecordProcess.class));
    }

    @Override
    public void addCheckBySeller(ContractBreachRecordCheckAddDTO dto) {
        contractBreachRecordProcessBiz.addCheckBySeller(convert(dto,ContractBreachRecordProcess.class));
    }

    @Override
    public void addCheckByPlatform(ContractBreachRecordCheckAddDTO dto) {
        contractBreachRecordProcessBiz.addCheckByPlatform(convert(dto,ContractBreachRecordProcess.class));
    }

    @Override
    public void updateCheckByBuyer(ContractBreachRecordCheckUpdateDTO dto) {
        contractBreachRecordProcessBiz.updateCheckByBuyer(convert(dto,ContractBreachRecordProcess.class));
    }

    @Override
    public void updateCheckBySeller(ContractBreachRecordCheckUpdateDTO dto) {
        contractBreachRecordProcessBiz.updateCheckBySeller(convert(dto,ContractBreachRecordProcess.class));
    }

    @Override
    public void updateCheckByPlatform(ContractBreachRecordCheckUpdateDTO dto) {
        contractBreachRecordProcessBiz.updateCheckByPlatform(convert(dto,ContractBreachRecordProcess.class));
    }

    @Override
    public ContractBreachRecordCheckDTO findCheckById(String id) {
        return convert(contractBreachRecordProcessBiz.findCheckById(id),ContractBreachRecordCheckDTO.class);
    }

    @Override
    public List<ContractBreachRecordCheckDTO> findCheckByRecordId(String recordId) {
        List<ContractBreachRecordProcess>  list = contractBreachRecordProcessBiz.findCheckByRecordId(recordId);
        return list == null || list.isEmpty() ? null : list.stream().map(item ->convert(item,ContractBreachRecordCheckDTO.class)).toList();
    }

    @Override
    public PageInfo<ContractBreachRecordCheckDTO> findAllCheck(ContractBreachRecordCheckQueryDTO dto) {
        PageInfo<ContractBreachRecordProcess> pageInfo = contractBreachRecordProcessBiz.findAllCheck(dto);
        PageInfo<ContractBreachRecordCheckDTO> result = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo,result);
        return result;
    }

    @Override
    public void deleteCheckByIds(List<String> idList,String operator) {
        contractBreachRecordProcessBiz.deleteCheckByIds(idList,operator);
    }

    @Override
    public void deleteCheckByRecordId(List<String> recordIdList,String operator) {
        contractBreachRecordProcessBiz.deleteCheckByRecordId(recordIdList,operator);
    }

    private <T> T convert(Object source,Class<T> targetClass){
        if( source == null ){
            return null;
        }
        try {
            T target = targetClass.newInstance();
            BeanUtils.copyProperties(source,target);
            return target;
        } catch (Exception e) {
            log.error(e.getMessage(),e);
        }
        return null;
    }
}
