package com.ecommerce.information.util;

import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.utils.ArithUtils;
import com.ecommerce.common.utils.DateUtil;
import com.ecommerce.information.api.dto.bill.check.BillCheckGoodsInfoDTO;
import com.ecommerce.information.api.dto.bill.check.BillCheckInfoDTO;
import com.ecommerce.information.api.dto.bill.check.BillCheckWaybillInfoDTO;
import com.ecommerce.information.api.dto.bill.check.BillCheckWaybillInfoQueryDTO;
import com.ecommerce.information.api.dto.bill.check.enums.CostTypeEnum;
import com.ecommerce.information.api.dto.bill.check.rule.PageQuery;
import com.ecommerce.information.bill.check.service.IBillCheckService;
import com.ecommerce.logistics.api.enums.TransportToolTypeEnum;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.PaperSize;
import org.apache.poi.ss.usermodel.PrintOrientation;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFPrintSetup;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.http.HttpHeaders;

import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 物流对账单导出
 */
@Slf4j
public class BillCheckExcelUtil2 {

    /**
     *
     * 运费对账单标准版:
     *                  xxxx年xx月大平台物流运费对账单
     * 日期从：xxxx/xx/xx
     * 日期至：xxxx/xx/xx
     * 序号	商品规格	运输起点	运输终点	"运费单价（元/吨）"	"重量（吨）"	"运费金额（元）"
     * 1
     * 2
     * 3
     * 合计					                             0.00	       0.00
     *
     * 	付款方（托运人）确认（盖章）：			收款方（承运人）确认（盖章）：		制表人:
     *
     * 	日期：			                日期：		                       制表日期：
     *
     *
     * 标准模板对账单明细表:
     * 	xxxx年xx月大平台配送明细表
     * 合同编号：
     * 序号	商品规格	物流方式	车牌号/船号	运输路线	发货单号	生产厂家	出厂时间	"发货数量（吨）"	"收货数量（吨）"	"磅差（吨）"	"单价(元/吨)"	"运费金额（元）"	"费用调整（元）"	备注
     * 1													                                                                                        0
     * 2													                                                                                        0
     * 3													                                                                                        0
     * 4													                                                                                        0
     * 5													                                                                                        0
     * 合计
     *
     *
     * 	付款方（托运人）（盖章）：						"收款方（承运人）（盖章）："						制表人：
     *                                                                                          制表日期：
     *
     */
    public static void logisticsBillCheckInfoExport(IBillCheckService billCheckService, BillCheckInfoDTO checkInfoDTO, HttpServletResponse response){
        try {
            XSSFWorkbook wb = getExcel(billCheckService,checkInfoDTO);

            response.setCharacterEncoding("UTF-8");
            response.setHeader(HttpHeaders.CONTENT_TYPE, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            String fileName = "logisticsBillCheckInfo-" + System.currentTimeMillis()+".xlsx";
            response.setHeader("Content-Disposition", fileName);
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.setHeader("Cache-Control", "must-revalidate, post-check=0, pre-check=0");
            OutputStream os = response.getOutputStream();
            wb.write(os);
            os.close();
            wb.close();
        } catch (Exception e) {
            if( e instanceof BizException exception ){
                throw exception;
            }
            log.error(e.getMessage(), e);
            throw new BizException(BasicCode.CUSTOM_ERROR,"excel导出失败");
        }
    }
    public static XSSFWorkbook getExcel(IBillCheckService billCheckService, BillCheckInfoDTO checkInfoDTO){
        if( checkInfoDTO == null ){
            throw new BizException(BasicCode.CUSTOM_ERROR,"对账单不可为空");
        }
        CostTypeEnum costType = CostTypeEnum.getByCode(checkInfoDTO.getCostType());
        if( costType == null ){
            log.error("checkInfoId:{},costType:{}",checkInfoDTO.getBillCheckId(),checkInfoDTO.getCostType());
            throw new BizException(BasicCode.CUSTOM_ERROR,"对账单费用类型不正确");
        }
        if( CostTypeEnum.LOGISTICS_ROAD != costType && CostTypeEnum.LOGISTICS_SHIP != costType){
            log.info("checkInfoId:{},costType:{}",checkInfoDTO.getBillCheckId(),checkInfoDTO.getCostType());
            //需求如此
            throw new BizException(BasicCode.CUSTOM_ERROR,"当前导出仅支持物流费对账");
        }
        XSSFWorkbook wb = new XSSFWorkbook();
        wb.createSheet();
        XSSFSheet sheet = wb.getSheetAt(0);
        XSSFPrintSetup printSetup = sheet.getPrintSetup();
        printSetup.setFitHeight((short)0);
        printSetup.setHResolution((short)600);
        printSetup.setLandscape(true);
        printSetup.setNoOrientation(false);
        printSetup.setOrientation(PrintOrientation.LANDSCAPE);
        printSetup.setPaperSize(PaperSize.A4_PAPER);
        printSetup.setScale((short)89);
        printSetup.setVResolution((short)600);
        wb.setSheetName(0,"运费对账单标准版");

        //2021-04-27 00:00:00	2021-05-27 23:59:59
        String year = checkInfoDTO.getBillCheckBegin().substring(0,4);
        String month = checkInfoDTO.getBillCheckBegin().substring(5,7);
        String day = checkInfoDTO.getBillCheckBegin().substring(8,10);
        String title = year +"年"+month+"月大平台物流运费对账单";
        List<String> header = Lists.newArrayList("序号","商品规格","运输起点","运输终点","运费单价\r\n（元/吨）","重量\r\n（吨）","运费金额\r\n（元）");
        setTitle(wb,sheet,title,header.size(),20);
        //日期从: xxxx/xx/xx
        Row row1 = sheet.createRow(1);
        row1.createCell(0).setCellValue("日期从:"+year+"/"+month+"/"+day);
        sheet.addMergedRegion(new CellRangeAddress(1,1,0,3));
        // 创建表头单元格样式
        CellStyle style1 = wb.createCellStyle();
        Font fontStyle1 = wb.createFont();
        // 字体
        fontStyle1.setFontName("Calibri");
        // 加粗
        fontStyle1.setBold(true);
        // 颜色
        fontStyle1.setColor(IndexedColors.BLACK.getIndex());
        fontStyle1.setFontHeightInPoints((short)12);
        style1.setFont(fontStyle1);
        // 水平居中
        style1.setAlignment(HorizontalAlignment.LEFT);
        row1.getCell(0).setCellStyle(style1);

        //日期至: xxxx/xx/xx
        //2021-04-27 00:00:00	2021-05-27 23:59:59
        year = checkInfoDTO.getBillCheckEnd().substring(0,4);
        month = checkInfoDTO.getBillCheckEnd().substring(5,7);
        day = checkInfoDTO.getBillCheckEnd().substring(8,10);
        Row row2 = sheet.createRow(2);
        row2.createCell(0).setCellValue("日期至:"+year+"/"+month+"/"+day);
        sheet.addMergedRegion(new CellRangeAddress(2,2,0,3));
        // 创建表头单元格样式
        CellStyle style2 = wb.createCellStyle();
        Font fontStyle2 = wb.createFont();
        // 字体
        fontStyle2.setFontName("Calibri");
        // 加粗
        fontStyle2.setBold(true);
        // 颜色
        fontStyle2.setColor(IndexedColors.BLACK.getIndex());
        fontStyle2.setFontHeightInPoints((short)12);
        style2.setFont(fontStyle2);
        // 水平居中
        style2.setAlignment(HorizontalAlignment.LEFT);
        row2.getCell(0).setCellStyle(style2);
        //表头行
        XSSFRow row3 = sheet.createRow(3);
        CellStyle headStyle = getCellStyle(wb, "Calibri", 12, true, true);
        for (int i = 0; i < header.size(); i++) {
            XSSFCell cell3_ = row3.createCell(i);
            cell3_.setCellValue(header.get(i));
            cell3_.setCellStyle(headStyle);
            cell3_.getCellStyle().setAlignment(HorizontalAlignment.CENTER);
            switch (i){
                case 0:{
                //29 设置列宽
                    sheet.setColumnWidth(i,1060);
                    cell3_.setCellStyle( getCellStyle(wb, "Calibri", 12, false, false));
                    break;
                }
                //191
                case 1:{sheet.setColumnWidth(i,6948);break;}
                //124
                case 2:{sheet.setColumnWidth(i,4534);break;}
                //107
                case 3:{sheet.setColumnWidth(i,3913);break;}
                //215
                case 4:{sheet.setColumnWidth(i,7862);break;}
                //111
                case 5:{sheet.setColumnWidth(i,4059);break;}
                //125
                case 6:{sheet.setColumnWidth(i,4571);break;}
            }
        }
        //汇总数据
        Map<String, List<BillCheckGoodsInfoDTO>> group1 = checkInfoDTO.getGoodsInfoList().stream().collect(Collectors.groupingBy(BillCheckExcelUtil2::getKey1));
        Map<String,String> goodsMap = Maps.newHashMap();
        Map<String,String> warehouseMap = Maps.newHashMap();
        Map<String,String> receiveAddressMap = Maps.newHashMap();
        for (BillCheckGoodsInfoDTO item : checkInfoDTO.getGoodsInfoList()) {
            goodsMap.put(item.getGoodsId(),item.getGoodsName());
            warehouseMap.put(item.getWarehouseId(),item.getWarehouseName());
            receiveAddressMap.put(item.getReceiveAddressId(),item.getReceiveAddressName());
        }
        BigDecimal totalQuantity = BigDecimal.ZERO;
        BigDecimal totalAmount = BigDecimal.ZERO;
        int begin = sheet.getLastRowNum()+2;
        int end = begin;
        int totalRowCount = sheet.getLastRowNum()+1;
        boolean hasData = !group1.isEmpty();
        CellStyle cellStyle = getCellStyle(wb, 12);
        CellStyle cellLeftStyle = getLeftCellStyle(wb, 12);
        if( hasData ) {
            end = begin + group1.size() -1;
            for (Map.Entry<String, List<BillCheckGoodsInfoDTO>> entry : group1.entrySet()) {
                String[] keyArray = entry.getKey().split(SPLIT_STR);
                XSSFRow row = sheet.createRow(totalRowCount++);
                int column = 0;
                row.createCell(column).setCellStyle(cellStyle);
                row.getCell(column++).setCellValue(totalRowCount - 4d);

                row.createCell(column).setCellStyle(cellStyle);
                row.getCell(column++).setCellValue(goodsMap.getOrDefault(keyArray[0], ""));

                row.createCell(column).setCellStyle(cellStyle);
                row.getCell(column++).setCellValue(warehouseMap.getOrDefault(keyArray[1], ""));

                row.createCell(column).setCellStyle(cellStyle);
                row.getCell(column++).setCellValue(receiveAddressMap.getOrDefault(keyArray[2], ""));

                row.createCell(column).setCellStyle(cellStyle);
                row.getCell(column++).setCellValue(keyArray.length >= 4 ? keyArray[3] : "-");

                BigDecimal quantity = BigDecimal.ZERO;
                BigDecimal amount = BigDecimal.ZERO;
                for (BillCheckGoodsInfoDTO goodsInfoDTO : entry.getValue()) {
                    quantity = ArithUtils.add(quantity, goodsInfoDTO.getWaybillQuantity());
                    amount = ArithUtils.add(amount, goodsInfoDTO.getGoodsAmount());
                }
                totalQuantity = ArithUtils.add(totalQuantity, quantity);
                totalAmount = ArithUtils.add(totalAmount, amount);

                row.createCell(column).setCellStyle(cellStyle);
                setCellValue(row.getCell(column++),quantity);

                row.createCell(column).setCellStyle(cellStyle);
                setCellValue(row.getCell(column),amount);
            }
        }
        boolean oneRow = begin == end;
        //合计
        XSSFRow totalRow = sheet.createRow(totalRowCount);
        for (int i = 0; i <= 6; i++) {
            totalRow.createCell(i).setCellStyle(cellStyle);
            totalRow.getCell(i).setCellValue("");
        }
        totalRow.getCell(0).setCellValue("合计");
        //居中
        totalRow.getCell(0).getCellStyle().setAlignment(HorizontalAlignment.CENTER);
        sheet.addMergedRegion(new CellRangeAddress(totalRowCount,totalRowCount++,0,3));
        if( hasData ) {
            totalRow.getCell(5).setCellFormula(oneRow ? "F" + begin : "SUM(F" + begin + ":F" + end + ")");
            totalRow.getCell(6).setCellFormula(oneRow ? "G" + begin : "SUM(G" + begin + ":F" + end + ")");
        }
        sheet.setForceFormulaRecalculation(true);

        totalRowCount++;//空一行
        //付款方（托运人）确认（盖章）：			收款方（承运人）确认（盖章）：		制表人:
        XSSFRow userSignRow = sheet.createRow(totalRowCount);
        for (int i = 0; i <= 6; i++) {
            userSignRow.createCell(i).setCellValue("");
        }
        CellStyle leftCellStyleBlue = getLeftCellStyle(wb, 12);
        userSignRow.getCell(1).setCellValue("付款方（托运人）确认（盖章）：");
        userSignRow.getCell(1).setCellStyle(leftCellStyleBlue);
        userSignRow.getCell(1).getCellStyle().getFont().setColor(IndexedColors.BLACK.getIndex());
        sheet.addMergedRegion(new CellRangeAddress(totalRowCount,totalRowCount,1,2));
        userSignRow.getCell(4).setCellValue("收款方（承运人）确认（盖章）：");
        userSignRow.getCell(4).setCellStyle(leftCellStyleBlue);
        sheet.addMergedRegion(new CellRangeAddress(totalRowCount,totalRowCount++,4,5));
        userSignRow.getCell(6).setCellValue("制表人:");
        userSignRow.getCell(6).setCellStyle(leftCellStyleBlue);
        totalRowCount++;//空一行
        //日期：			日期：		制表日期：
        XSSFRow dateRow = sheet.createRow(totalRowCount);
        for (int i = 0; i <= 6; i++) {
            dateRow.createCell(i).setCellValue("");
        }
        dateRow.getCell(1).setCellValue("日期：");
        dateRow.getCell(1).setCellStyle(cellLeftStyle);
        dateRow.getCell(4).setCellValue("日期：");
        dateRow.getCell(4).setCellStyle(cellLeftStyle);
        dateRow.getCell(6).setCellValue("制表日期：");
        dateRow.getCell(6).setCellStyle(cellLeftStyle);

        //设置sheet2: 标准模板对账单明细表
        createSheet2(billCheckService,checkInfoDTO,wb);

        return wb;
    }

    // TODO: 重构此方法以降低认知复杂度 (当前: 30, 目标: ≤15)
    // 建议: 1) 提取子方法 2) 使用早期返回 3) 简化条件表达式
    private static void createSheet2(IBillCheckService billCheckService, BillCheckInfoDTO checkInfoDTO, XSSFWorkbook wb){
        wb.createSheet();
        XSSFSheet sheet = wb.getSheetAt(1);
        //{"fitHeight":1,"hResolution":600, "landscape":false,"noOrientation":true, "orientation":"DEFAULT",  "paperSizeEnum":"LETTER_PAPER","scale":100,"vResolution":600, }
        //{"fitHeight":0,"hResolution":1200,"landscape":true, "noOrientation":false,"orientation":"LANDSCAPE","paperSizeEnum":"A4_PAPER",     "scale":72,"vResolution":1200,}
        XSSFPrintSetup printSetup = sheet.getPrintSetup();
        printSetup.setFitHeight((short)0);
        printSetup.setHResolution((short)1200);
        printSetup.setLandscape(true);
        printSetup.setNoOrientation(false);
        printSetup.setOrientation(PrintOrientation.LANDSCAPE);
        printSetup.setPaperSize(PaperSize.A4_PAPER);
        printSetup.setScale((short)72);
        printSetup.setVResolution((short)1200);
        wb.setSheetName(1,"标准模板对账单明细表");

        List<String> header = Lists.newArrayList("序号","商品规格","物流方式","车牌号/船号","运输路线","发货单号","生产厂家","出厂时间","发货数量\r\n（吨）","收货数量\r\n（吨）","磅差\r\n（吨）","单价\r\n(元/吨)","运费金额\r\n（元）","费用调整\r\n（元）","备注");

        //2021-04-27 00:00:00	2021-05-27 23:59:59
        String year = checkInfoDTO.getBillCheckBegin().substring(0,4);
        String month = checkInfoDTO.getBillCheckBegin().substring(5,7);
        String title = year +"年"+month+"月大平台配送明细表";
        setTitle(wb,sheet,title,header.size(),22);

        //合同编号：
        Row row1 = sheet.createRow(1);
        row1.createCell(0).setCellValue("合同编号：");
        sheet.addMergedRegion(new CellRangeAddress(1,1,0,header.size()-1));
        // 创建表头单元格样式
        CellStyle style1 = wb.createCellStyle();
        Font fontStyle1 = wb.createFont();
        // 字体
        fontStyle1.setFontName("Calibri");
        // 颜色
        fontStyle1.setColor(IndexedColors.BLACK.getIndex());
        fontStyle1.setFontHeightInPoints((short)11);
        style1.setFont(fontStyle1);
        // 水平居中
        style1.setAlignment(HorizontalAlignment.LEFT);
        row1.getCell(0).setCellStyle(style1);

        //表头行
        XSSFRow headRow = sheet.createRow(2);
        headRow.setHeightInPoints(23.25f);

        XSSFCellStyle headStyle = (XSSFCellStyle)getCellStyle(wb,"宋体", 10, true,true);
        for (int i = 0; i < header.size(); i++) {
            XSSFCell cell = headRow.createCell(i);
            cell.setCellValue(header.get(i));
            cell.setCellStyle(headStyle);
            //宽带值由读取excel获取到的
            switch (i){
                case 0:{sheet.setColumnWidth(i,1353);break;}
                //36 设置列宽
                case 1:{sheet.setColumnWidth(i,2852);break;}
                //78
                case 2:{sheet.setColumnWidth(i,2925);break;}
                //80
                case 3:{sheet.setColumnWidth(i,3693);break;}
                //101
                case 4:{sheet.setColumnWidth(i,3291);break;}
                //91
                case 5:{sheet.setColumnWidth(i,3510);break;}
                //96
                case 6:{sheet.setColumnWidth(i,4461);break;}
                //122
                case 7:{sheet.setColumnWidth(i,3035);break;}
                //84
                case 8:{sheet.setColumnWidth(i,2377);break;}
                //65
                case 9:{sheet.setColumnWidth(i,3364);break;}
                //92
                case 10:
                    //71
                case 11:{sheet.setColumnWidth(i,2596);break;}
                case 12:
                    //66
                case 13:{sheet.setColumnWidth(i,2413);break;}
                //73
                case 14:{sheet.setColumnWidth(i,2669);break;}
            }
        }

        int totalRowCount = 3;
        boolean hasData = false;
        CellStyle cellStyle = getCellStyle(wb, 11);
        //序号	商品规格	物流方式	车牌号/船号	运输路线	发货单号	生产厂家	出厂时间
        int pageNum = 1;
        PageQuery<BillCheckWaybillInfoQueryDTO> pageQuery = new PageQuery<>();
        pageQuery.setPageSize(1000);
        pageQuery.setQueryDTO(new BillCheckWaybillInfoQueryDTO());
        pageQuery.getQueryDTO().setBillCheckId(checkInfoDTO.getBillCheckId());
        while (true){
            pageQuery.setPageNum(pageNum++);
            PageInfo<BillCheckWaybillInfoDTO> pageInfo = billCheckService.pageWaybillInfo(pageQuery);
            if(pageInfo == null || CollectionUtils.isEmpty(pageInfo.getList())){
                break;
            }
            for (BillCheckWaybillInfoDTO item : pageInfo.getList()) {
                XSSFRow row = sheet.createRow(totalRowCount++);
                int column = 0;
                hasData = true;
                row.createCell(column).setCellStyle(cellStyle);
                row.getCell(column++).setCellValue(totalRowCount - 3d);

                row.createCell(column).setCellStyle(cellStyle);
                row.getCell(column++).setCellValue(item.getGoodsName());

                row.createCell(column).setCellStyle(cellStyle);
                row.getCell(column++).setCellValue(TransportToolTypeEnum.ROAD_TRANSPORT.getCode().equals(item.getTransportType()) ? "汽运" : "船运" );

                row.createCell(column).setCellStyle(cellStyle);
                row.getCell(column++).setCellValue(item.getVehicleNum());

                row.createCell(column).setCellStyle(cellStyle);
                //起点 + 终点
                row.getCell(column++).setCellValue(item.getWarehouseName()+" - "+item.getReceiveAddressName());

                row.createCell(column).setCellStyle(cellStyle);
                row.getCell(column++).setCellValue(item.getWaybillNum());

                row.createCell(column).setCellStyle(cellStyle);
                row.getCell(column++).setCellValue(item.getSellerName());

                row.createCell(column).setCellStyle(cellStyle);
                setCellValue(row.getCell(column++),item.getLeaveWarehouseTime());

                // "发货数量（吨）"	"收货数量（吨）"	"磅差（吨）"	"单价(元/吨)"	"运费金额（元）"	"费用调整（元）"	备注
                row.createCell(column).setCellStyle(cellStyle);
                setCellValue(row.getCell(column++),item.getSendQuantity());

                row.createCell(column).setCellStyle(cellStyle);
                setCellValue(row.getCell(column++),item.getSignQuantity());

                row.createCell(column).setCellStyle(cellStyle);
                //磅差（吨）
                setCellValue(row.getCell(column++),item.getQuantityMargin());

                row.createCell(column).setCellStyle(cellStyle);
                setCellValue(row.getCell(column++),item.getUnitLogisticPrice());

                row.createCell(column).setCellStyle(cellStyle);
                setCellValue(row.getCell(column++),item.getAdjustAmount());

                row.createCell(column).setCellStyle(cellStyle);
                //费用调整
                BigDecimal adjust = ArithUtils.subtract(item.getAdjustAmount(),item.getAmount());
                setCellValue(row.getCell(column++),adjust.compareTo(BigDecimal.ZERO) != 0 ? adjust : null);

                row.createCell(column).setCellStyle(cellStyle);
                row.getCell(column).setCellValue(item.getReturnReason());
            }
        }
        //合计
        XSSFRow row = sheet.createRow(totalRowCount++);
        for (int i = 0; i < header.size(); i++) {
            row.createCell(i).setCellStyle(cellStyle);
            row.getCell(i).setCellValue("");
        }
        row.getCell(0).setCellValue("合计");

        int column=8;
        int begin=4,end=totalRowCount-1;
        if( hasData ) {
            boolean oneRow = begin == end;
            // "发货数量（吨）"	"收货数量（吨）"	"磅差（吨）"	"单价(元/吨)"	"运费金额（元）"	"费用调整（元）"	备注
            row.getCell(column++).setCellFormula(oneRow ? "I" + begin : "SUM(I" + begin + ":I" + end + ")");
            row.getCell(column++).setCellFormula(oneRow ? "J" + begin : "SUM(J" + begin + ":J" + end + ")");
            row.getCell(column++).setCellFormula(oneRow ? "K" + begin : "SUM(K" + begin + ":K" + end + ")");
            row.getCell(column++).setCellFormula(oneRow ? "L" + begin : "SUM(L" + begin + ":L" + end + ")");
            row.getCell(column++).setCellFormula(oneRow ? "M" + begin : "SUM(M" + begin + ":M" + end + ")");
            row.getCell(column).setCellFormula(oneRow ? "N" + begin : "SUM(N" + begin + ":N" + end + ")");
        }


        XSSFCellStyle cellLeftStyle = (XSSFCellStyle)getLeftCellStyle(wb, 12);
        cellLeftStyle.getFont().setColor(IndexedColors.BLACK.getIndex());
        //付款方（托运人）确认（盖章）：			收款方（承运人）确认（盖章）：		制表人:
        XSSFRow userSignRow = sheet.createRow(++totalRowCount);
        for (int i = 0; i < header.size(); i++) {
            userSignRow.createCell(i).setCellStyle(cellLeftStyle);
            userSignRow.getCell(i).setCellValue("");
        }
        userSignRow.getCell(1).setCellValue("付款方（托运人）（盖章）：");
        sheet.addMergedRegion(new CellRangeAddress(totalRowCount,totalRowCount,1,4));
        userSignRow.getCell(7).setCellValue("收款方（承运人）（盖章）：");
        sheet.addMergedRegion(new CellRangeAddress(totalRowCount,totalRowCount++,7,10));
        userSignRow.getCell(13).setCellValue("制表人：");
        //空一行
        totalRowCount++;
        //制表日期：
        XSSFRow dateRow = sheet.createRow(totalRowCount);
        dateRow.createCell(0).setCellValue("");
        sheet.addMergedRegion(new CellRangeAddress(totalRowCount,totalRowCount,0,12));
        dateRow.createCell(13).setCellValue("制表日期：");
        dateRow.getCell(13).setCellStyle(cellLeftStyle);
        sheet.setForceFormulaRecalculation(true);
    }

    private static void setTitle(XSSFWorkbook wb,Sheet sheet,String title,int columSize,int fontHeight){
        // 创建第一行
        Row row = sheet.createRow(0);
        row.createCell(0).setCellValue(title);
        sheet.addMergedRegion(new CellRangeAddress(0,0,0,columSize-1));
        //设置字体样式
        // 创建表头单元格样式
        CellStyle style = wb.createCellStyle();
        Font fontStyle = wb.createFont();
        // 字体
        fontStyle.setFontName("Calibri");
        // 加粗
        fontStyle.setBold(true);
        // 颜色
        fontStyle.setColor(IndexedColors.BLACK.getIndex());
        fontStyle.setFontHeightInPoints((short)fontHeight);
        style.setFont(fontStyle);
        // 水平居中
        style.setAlignment(HorizontalAlignment.CENTER);
        row.getCell(0).setCellStyle(style);
    }

    private static CellStyle getCellStyle(XSSFWorkbook wb,int fontHeight) {
        return getCellStyle(wb,"Calibri",fontHeight,true,true);
    }

    private static CellStyle getCellStyle(XSSFWorkbook wb,String fontName,int fontHeight,boolean bold,boolean wrapText) {
        // 创建表头单元格样式
        CellStyle style = wb.createCellStyle();
        Font fontStyle = wb.createFont();
        // 字体
        fontStyle.setFontName(fontName);
        fontStyle.setFontHeightInPoints((short)fontHeight);
        // 颜色
        fontStyle.setColor(IndexedColors.BLACK.getIndex());
        fontStyle.setBold(bold);
        style.setFont(fontStyle);
        style.setAlignment(HorizontalAlignment.LEFT);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        // 上边框
        style.setBorderTop(BorderStyle.THIN);
        // 下边框
        style.setBorderBottom(BorderStyle.THIN);
        // 左边框
        style.setBorderLeft(BorderStyle.THIN);
        // 右边框
        style.setBorderRight(BorderStyle.THIN);
        // 锁定
        style.setLocked(true);
        style.setWrapText(wrapText);
        return style;
    }

    private static CellStyle getLeftCellStyle(XSSFWorkbook wb,int fontHeight) {
        // 创建表头单元格样式
        CellStyle style = wb.createCellStyle();
        Font fontStyle = wb.createFont();
        // 字体
        fontStyle.setFontName("Calibri");
        fontStyle.setFontHeightInPoints((short)fontHeight);
        // 颜色
        fontStyle.setColor(IndexedColors.BLACK.getIndex());
        style.setFont(fontStyle);
        style.setAlignment(HorizontalAlignment.LEFT);
        // 锁定
        style.setLocked(true);
        return style;
    }

    private static void setCellValue(XSSFCell cell,BigDecimal value){
        if( value == null ){
            return;
        }
        cell.setCellValue(value.setScale(2, RoundingMode.HALF_UP).doubleValue());
    }
    private static void setCellValue(XSSFCell cell,Date value){
        if( value == null ){
            return;
        }
        cell.setCellValue(DateUtil.format(value,"yyyy/MM/dd HH:mm:ss"));
    }

    private static final String SPLIT_STR = "-";

    private static String getKey1(BillCheckGoodsInfoDTO item){
        String price = item.getPrice() == null ? "" : item.getPrice().setScale(2, RoundingMode.HALF_UP).toString();
        return item.getGoodsId()+SPLIT_STR+ item.getWarehouseId() +SPLIT_STR+ item.getReceiveAddressId() +SPLIT_STR+ price;
    }

}
