logging:
  config: classpath:log/logback-${spring.cloud.config.profile}.xml
  level:
    com.netflix.discovery.DiscoveryClient: WARN
    com.ecommerce.information.service: DEBUG
    com.ecommerce.information.dao: DEBUG
    com.ecommerce.information.biz: DEBUG
  pattern:
    # Logging pattern containing traceId and spanId; no longer provided through Sleuth by default
    level: "%5p [${spring.application.name:},%X{traceId:-},%X{spanId:-}]"
mybatis:
  typeAliasesPackage: com.ecommerce.information.dao
  mapperScanPackage: com.ecommerce.information.dao
     mapperLocations: "classpath:/mapper/*.xml"
     configLocation: "classpath:/mybatis-config.xml"
spring:
  config:
    import: 'optional:configserver:'
  cloud:
    config:
      uri: http://127.0.0.1:8888
      profile: dev
      label: asset-2025
      name: information,db,redis,rabbitmq,hystrix,vip-server,kafka,xxl-job,eureka,elasticsearch
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true


mapper:
  resolve-class: com.ecommerce.common.tk.MapperEntityResolve