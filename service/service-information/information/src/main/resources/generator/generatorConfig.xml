<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>

    <context id="Mysql" targetRuntime="MyBatis3Simple" defaultModelType="flat">
        <property name="beginningDelimiter" value="`"/>
        <property name="endingDelimiter" value="`"/>
        <property name="javaFileEncoding" value="UTF-8"/>
        <plugin type="org.mybatis.generator.plugins.SerializablePlugin" />
        <plugin type="org.mybatis.generator.plugins.ToStringPlugin" />

        <plugin type="tk.mybatis.mapper.generator.MapperPlugin">
            <property name="mappers" value="com.ecommerce.common.service.IBaseMapper"/>
        </plugin>

        <jdbcConnection driverClass="com.mysql.cj.jdbc.Driver"
                        connectionURL="*******************************************"
                        userId="id1"
                        password="111111">
        </jdbcConnection>

        <javaModelGenerator targetPackage="com.ecommerce.information.dao.vo" targetProject="src/main/java">
            <property name="enableSubPackages" value="true" />
            <property name="trimStrings" value="true" />
        </javaModelGenerator>

        <sqlMapGenerator targetPackage="mapper" targetProject="src/main/resources">
            <property name="enableSubPackages" value="true" />
        </sqlMapGenerator>

        <javaClientGenerator targetPackage="com.ecommerce.information.dao.mapper" targetProject="src/main/java"
                             type="XMLMAPPER"/>

<!--
        <table tableName="mb_member" domainObjectName="Member" enableCountByExample="true"
               enableUpdateByExample="true" enableDeleteByExample="true"
               enableSelectByExample="true" selectByExampleQueryId="true">
        </table>
        <table tableName="sys_permission" domainObjectName="Permission" enableCountByExample="true"
               enableUpdateByExample="true" enableDeleteByExample="true"
               enableSelectByExample="true" selectByExampleQueryId="true">
        </table>
        <table tableName="sys_role" domainObjectName="Role" enableCountByExample="true"
               enableUpdateByExample="true" enableDeleteByExample="true"
               enableSelectByExample="true" selectByExampleQueryId="true">
        </table>
        <table tableName="sys_role_permission" domainObjectName="RolePermission" enableCountByExample="true"
               enableUpdateByExample="true" enableDeleteByExample="true"
               enableSelectByExample="true" selectByExampleQueryId="true">
        </table> -->
        <!--<table tableName="mb_member_integral" domainObjectName="mmemberIntegral" enableCountByExample="true"-->
               <!--enableUpdateByExample="true" enableDeleteByExample="true"-->
               <!--enableSelectByExample="true" selectByExampleQueryId="true">-->
        <!--</table>-->
        <!--<table tableName="mb_member_integral_change_info" domainObjectName="memberIntegralChangeInfo" enableCountByExample="true"-->
               <!--enableUpdateByExample="true" enableDeleteByExample="true"-->
               <!--enableSelectByExample="true" selectByExampleQueryId="true">-->
        <!--</table>-->
        <!--<table tableName="ii_prefabricated_building" domainObjectName="PrefabricatedBuilding" enableCountByExample="true"-->
               <!--enableUpdateByExample="true" enableDeleteByExample="true"-->
               <!--enableSelectByExample="true" selectByExampleQueryId="true">-->
        <!--</table>-->
        <!--<table tableName="ii_prefabricated_building_partners" domainObjectName="PrefabricatedBuildingPartners" enableCountByExample="true"-->
               <!--enableUpdateByExample="true" enableDeleteByExample="true"-->
               <!--enableSelectByExample="true" selectByExampleQueryId="true">-->
        <!--</table>-->
        <!--<table tableName="ii_technology_equipment_info" domainObjectName="TechnologyEquipment" enableCountByExample="true"-->
               <!--enableUpdateByExample="true" enableDeleteByExample="true"-->
               <!--enableSelectByExample="true" selectByExampleQueryId="true">-->
        <!--</table>-->
        <!--<table tableName="ii_technology_equipment_check" domainObjectName="TechnologyEquipmentCheck" enableCountByExample="true"-->
               <!--enableUpdateByExample="true" enableDeleteByExample="true"-->
               <!--enableSelectByExample="true" selectByExampleQueryId="true">-->
        <!--</table>-->

        <!--<table tableName="ii_member_integral" domainObjectName="MemberIntegral" enableCountByExample="true"-->
               <!--enableUpdateByExample="true" enableDeleteByExample="true"-->
               <!--enableSelectByExample="true" selectByExampleQueryId="true">-->
        <!--</table>-->
        <!--<table tableName="ii_member_integral_change_info" domainObjectName="MemberIntegralChangeInfo" enableCountByExample="true"-->
               <!--enableUpdateByExample="true" enableDeleteByExample="true"-->
               <!--enableSelectByExample="true" selectByExampleQueryId="true">-->
        <!--</table>-->
        <!--<table tableName="ii_member_integral_level_config" domainObjectName="MemberIntegralLevelConfig" enableCountByExample="true"-->
               <!--enableUpdateByExample="true" enableDeleteByExample="true"-->
               <!--enableSelectByExample="true" selectByExampleQueryId="true">-->
        <!--</table>-->
        <!--<table tableName="ii_member_integral_trade_config" domainObjectName="MemberIntegralTradeConfig" enableCountByExample="true"-->
               <!--enableUpdateByExample="true" enableDeleteByExample="true"-->
               <!--enableSelectByExample="true" selectByExampleQueryId="true">-->
        <!--</table>-->
        <!--<table tableName="bill_check_waybill_base_data" domainObjectName="BillCheckWaybillBaseData" enableCountByExample="true"-->
               <!--enableUpdateByExample="true" enableDeleteByExample="true"-->
               <!--enableSelectByExample="true" selectByExampleQueryId="true">-->
        <!--</table>-->
        <table tableName="ff_loan_apply" domainObjectName="LoanApply" enableCountByExample="true"
               enableUpdateByExample="true" enableDeleteByExample="true"
               enableSelectByExample="true" selectByExampleQueryId="true">
        </table>
        <table tableName="ff_loan_finance" domainObjectName="LoanFinance" enableCountByExample="true"
               enableUpdateByExample="true" enableDeleteByExample="true"
               enableSelectByExample="true" selectByExampleQueryId="true">
        </table>
        <table tableName="ff_loan_member" domainObjectName="LoanMember" enableCountByExample="true"
               enableUpdateByExample="true" enableDeleteByExample="true"
               enableSelectByExample="true" selectByExampleQueryId="true">
        </table>
    </context>
</generatorConfiguration>
