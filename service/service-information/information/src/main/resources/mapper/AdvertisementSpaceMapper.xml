<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.information.dao.mapper.AdvertisementSpaceMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.information.dao.vo.AdvertisementSpace">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="space_id" jdbcType="VARCHAR" property="spaceId" />
    <result column="space_code" jdbcType="VARCHAR" property="spaceCode" />
    <result column="space_title" jdbcType="VARCHAR" property="spaceTitle" />
    <result column="space_type" jdbcType="VARCHAR" property="spaceType" />
    <result column="ad_range" jdbcType="VARCHAR" property="adRange" />
    <result column="space_page" jdbcType="VARCHAR" property="spacePage" />
    <result column="position" jdbcType="VARCHAR" property="position" />
    <result column="image_exists" jdbcType="BIT" property="imageExists" />
    <result column="image_height" jdbcType="VARCHAR" property="imageHeight" />
    <result column="image_width" jdbcType="VARCHAR" property="imageWidth" />
    <result column="image_size_limit" jdbcType="VARCHAR" property="imageSizeLimit" />
    <result column="image_format_limit" jdbcType="VARCHAR" property="imageFormatLimit" />
    <result column="image_count_limit" jdbcType="INTEGER" property="imageCountLimit" />
    <result column="ad_type" jdbcType="VARCHAR" property="adType" />
    <result column="space_number" jdbcType="INTEGER" property="spaceNumber" />
    <result column="order_flg" jdbcType="BIT" property="orderFlg" />
    <result column="space_status" jdbcType="BIT" property="spaceStatus" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
  </resultMap>
</mapper>