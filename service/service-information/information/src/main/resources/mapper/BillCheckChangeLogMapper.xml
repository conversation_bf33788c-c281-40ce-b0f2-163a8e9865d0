<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.information.bill.check.dao.mapper.BillCheckChangeLogMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.information.bill.check.dao.vo.BillCheckChangeLog">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="bill_check_change_log_id" jdbcType="VARCHAR" property="billCheckChangeLogId" />
    <result column="bill_check_id" jdbcType="VARCHAR" property="billCheckId" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="node_name" jdbcType="VARCHAR" property="nodeName" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="status" jdbcType="BIT" property="status" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>
</mapper>