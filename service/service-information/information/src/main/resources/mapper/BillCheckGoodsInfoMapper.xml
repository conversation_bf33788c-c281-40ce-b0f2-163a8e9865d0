<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.information.bill.check.dao.mapper.BillCheckGoodsInfoMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.information.bill.check.dao.vo.BillCheckGoodsInfo">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="bill_check_goods_info_id" jdbcType="VARCHAR" property="billCheckGoodsInfoId" />
    <result column="bill_check_id" jdbcType="VARCHAR" property="billCheckId" />
    <result column="bill_check_no" jdbcType="VARCHAR" property="billCheckNo" />
    <result column="cost_type" jdbcType="VARCHAR" property="costType" />
    <result column="bill_check_begin" jdbcType="VARCHAR" property="billCheckBegin" />
    <result column="bill_check_end" jdbcType="VARCHAR" property="billCheckEnd" />
    <result column="buyer_id" jdbcType="VARCHAR" property="buyerId" />
    <result column="buyer_name" jdbcType="VARCHAR" property="buyerName" />
    <result column="invoice_type" jdbcType="BIT" property="invoiceType" /><!-- 转java的int -->
    <result column="goods_id" jdbcType="VARCHAR" property="goodsId" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="transport_category_id" jdbcType="VARCHAR" property="transportCategoryId" />
    <result column="transport_category_name" jdbcType="VARCHAR" property="transportCategoryName" />
    <result column="sale_region_id" jdbcType="VARCHAR" property="saleRegionId" />
    <result column="sale_region_name" jdbcType="VARCHAR" property="saleRegionName" />
    <result column="deliver_way" jdbcType="VARCHAR" property="deliverWay" />
    <result column="waybill_deliver_way" jdbcType="VARCHAR" property="waybillDeliverWay" />
    <result column="transport_type" jdbcType="VARCHAR" property="transportType" />
    <result column="warehouse_id" jdbcType="VARCHAR" property="warehouseId" />
    <result column="warehouse_name" jdbcType="VARCHAR" property="warehouseName" />
    <result column="warehouse_address" jdbcType="VARCHAR" property="warehouseAddress" />
    <result column="picking_wharf_id" jdbcType="VARCHAR" property="pickingWharfId" />
    <result column="picking_wharf_name" jdbcType="VARCHAR" property="pickingWharfName" />
    <result column="picking_wharf_address" jdbcType="VARCHAR" property="pickingWharfAddress" />
    <result column="receiving_wharf_id" jdbcType="VARCHAR" property="receivingWharfId" />
    <result column="receiving_wharf_name" jdbcType="VARCHAR" property="receivingWharfName" />
    <result column="receiving_wharf_address" jdbcType="VARCHAR" property="receivingWharfAddress" />
    <result column="receive_address_id" jdbcType="VARCHAR" property="receiveAddressId" />
    <result column="receive_address_name" jdbcType="VARCHAR" property="receiveAddressName" />
    <result column="receive_address" jdbcType="VARCHAR" property="receiveAddress" />
    <result column="unload_port_id" jdbcType="VARCHAR" property="unloadPortId" />
    <result column="unload_port_name" jdbcType="VARCHAR" property="unloadPortName" />
    <result column="unload_port_address" jdbcType="VARCHAR" property="unloadPortAddress" />
    <result column="waybill_count" jdbcType="INTEGER" property="waybillCount" />
    <result column="waybill_quantity" jdbcType="DECIMAL" property="waybillQuantity" />
    <result column="unit_goods_price" jdbcType="DECIMAL" property="unitGoodsPrice" />
    <result column="newest_goods_price" jdbcType="DECIMAL" property="newestGoodsPrice" />
    <result column="unit_logistic_price" jdbcType="DECIMAL" property="unitLogisticPrice" />
    <result column="newest_logistic_price" jdbcType="DECIMAL" property="newestLogisticPrice" />
    <result column="adjust_amount" jdbcType="DECIMAL" property="adjustAmount" />
    <result column="goods_amount" jdbcType="DECIMAL" property="goodsAmount" />
    <result column="add_item_price" jdbcType="DECIMAL" property="addItemPrice" />
    <result column="floor_truckage" jdbcType="DECIMAL" property="floorTruckage" />
    <result column="empty_load_fee" jdbcType="DECIMAL" property="emptyLoadFee" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="user_add_remark" jdbcType="VARCHAR" property="userAddRemark" />
    <result column="user_add_type" jdbcType="VARCHAR" property="userAddType" />
    <result column="user_add_flg" jdbcType="BIT" property="userAddFlg" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>
</mapper>