<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.information.bill.check.dao.mapper.BillCheckInfoMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.information.bill.check.dao.vo.BillCheckInfo">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="bill_check_id" jdbcType="VARCHAR" property="billCheckId" />
    <id column="bill_check_rule_id" jdbcType="VARCHAR" property="billCheckRuleId" />
    <result column="bill_check_type" jdbcType="VARCHAR" property="billCheckType" />
    <result column="bill_check_no" jdbcType="VARCHAR" property="billCheckNo" />
    <result column="check_cycle" jdbcType="VARCHAR" property="checkCycle" />
    <result column="member_role" jdbcType="VARCHAR" property="memberRole" />
    <result column="bill_check_date" jdbcType="VARCHAR" property="billCheckDate" />
    <result column="bill_check_begin" jdbcType="VARCHAR" property="billCheckBegin" />
    <result column="bill_check_end" jdbcType="VARCHAR" property="billCheckEnd" />
    <result column="cost_type" jdbcType="VARCHAR" property="costType" />
    <result column="project_name" jdbcType="VARCHAR" property="projectName" />
    <result column="payee_member_id" jdbcType="VARCHAR" property="payeeMemberId" />
    <result column="payee_member_name" jdbcType="VARCHAR" property="payeeMemberName" />
    <result column="payee_account_id" jdbcType="VARCHAR" property="payeeAccountId" />
    <result column="payer_member_id" jdbcType="VARCHAR" property="payerMemberId" />
    <result column="payer_member_name" jdbcType="VARCHAR" property="payerMemberName" />
    <result column="contract_number" jdbcType="VARCHAR" property="contractNumber" />
    <result column="bill_check_quantity" jdbcType="DECIMAL" property="billCheckQuantity" />
    <result column="floor_truckage" jdbcType="DECIMAL" property="floorTruckage" />
    <result column="empty_load_fee" jdbcType="DECIMAL" property="emptyLoadFee" />
    <result column="bill_check_amount" jdbcType="DECIMAL" property="billCheckAmount" />
    <result column="bill_check_amount_adjusted" jdbcType="DECIMAL" property="billCheckAmountAdjusted" />
    <result column="bill_check_status" jdbcType="VARCHAR" property="billCheckStatus" />
    <result column="bank_card" jdbcType="VARCHAR" property="bankCard" />
    <result column="pay_serial_number" jdbcType="VARCHAR" property="paySerialNumber" />
    <result column="pay_status" jdbcType="VARCHAR" property="payStatus" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="show_customer" jdbcType="BIT" property="showCustomer" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="adjust_detail" jdbcType="LONGVARCHAR" property="adjustDetail" />
  </resultMap>

  <select id="tabCount" parameterType="com.ecommerce.information.api.dto.bill.check.BillCheckQueryDTO" resultType="com.ecommerce.information.api.dto.bill.check.BillCheckInfoTabDTO" >
    select bill_check_status as tabId,count(1) as num from bill_check_info where 1=1
    <if test = "billCheckId != null and billCheckId != '' ">
      and bill_check_id = #{billCheckId}
    </if>
    <if test="appName != null and (appName == 'buyer' or appName == 'buyerApp')">
        and payer_member_id = #{operatorMemberId}
        and bill_check_type = 'receivable'
        and bill_check_status in ('completed','wait_customer_confirm' )
    </if>
    <if test="appName != null and appName == 'driver_app'">
      and payee_member_id = #{operatorMemberId}
      and payer_member_id = '36ne053c6s1bvd2aijdjtcwkg'
      and cost_type = 'driver'
      and bill_check_status in ('completed','wait_customer_confirm' )
    </if>
    <if test="appName != null and (appName == 'platform' or appName == 'seller' or appName=='carrier')">
      <if test="billCheckType == 'receivable'">
        and payee_member_id = #{operatorMemberId}
        and ( (bill_check_type = 'receivable'  and member_role = #{appName} ) or  (bill_check_type = 'payable'  and member_role != #{appName} and bill_check_status not in ('draft','returned' ) ) )
      </if>
      <if test="billCheckType == 'payable'">
        and payer_member_id = #{operatorMemberId}
        and ( (bill_check_type = 'payable'  and member_role = #{appName} ) or  (bill_check_type = 'receivable'  and member_role != #{appName} and bill_check_status not in ('draft','returned' ) ) )
      </if>
      <if test="businessConfirm != null and businessConfirm == true">
        and bill_check_status in ('completed','wait_customer_confirm' )
      </if>
      <!-- 如果是司机应付 司机费用查询和应付查询分开 -->
      <if test="appName != null and appName == 'platform' and  billCheckType == 'payable' and costType != 'driver'">
        and cost_type != 'driver'
      </if>
    </if>
    <if test="memberRole != null and memberRole != ''">
      and member_role = #{memberRole}
    </if>
    <if test="billCheckNo != null and billCheckNo != ''">
      and bill_check_no = #{billCheckNo}
    </if>
    <if test="costType != null and costType != ''">
      and cost_type = #{costType}
    </if>
    <if test="billCheckDate != null and billCheckDate != ''">
      and bill_check_date = #{billCheckDate}
    </if>
    <if test="billCheckDateBegin != null and billCheckDateBegin != '' and billCheckDateEnd != null and billCheckDateEnd != ''">
      and bill_check_begin &gt;= #{billCheckDateBegin} and bill_check_end &lt;= #{billCheckDateEnd}
    </if>
    <if test="saleRegionIdStr != null and saleRegionIdStr != '' ">
      and bill_check_id in ( select distinct bill_check_id from bill_check_waybill_info where  del_flg=0 and sale_region_id in ( ${saleRegionIdStr} ) )
    </if>
      and del_flg = 0
    group by bill_check_status
  </select>

  <!-- 查询应收对账单中的付款人 -->
  <select id="findPayerMemberIdAndName" parameterType="com.ecommerce.information.api.dto.bill.check.KeyValueQueryDTO" resultType="com.ecommerce.information.api.dto.bill.check.KeyValueDTO" >
    select distinct payer_member_id as id,payer_member_name as name from bill_check_info where
    del_flg = 0
    and payee_member_id=#{operatorMemberId}
    <if test="billCheckId != null and billCheckId != ''">
      and bill_check_id = #{billCheckId}
    </if>
    <if test="keyword != null and keyword != ''">
      and payer_member_name like concat('%',#{keyword},'%')
    </if>
    order by name
    limit 20
  </select>

  <!-- 查询应付款对账单中的收款人 -->
  <select id="findPayeeMemberIdAndName" parameterType="com.ecommerce.information.api.dto.bill.check.KeyValueQueryDTO" resultType="com.ecommerce.information.api.dto.bill.check.KeyValueDTO" >
    select distinct payee_member_id as id,payee_member_name as name from bill_check_info where
    del_flg = 0
    and payer_member_id=#{operatorMemberId}
    <if test="billCheckId != null and billCheckId != ''">
      and bill_check_id = #{billCheckId}
    </if>
    <if test="keyword != null and keyword != ''">
      and payee_member_name like concat('%',#{keyword},'%')
    </if>
    order by name
    limit 20
  </select>

  <!-- 根据对账单号like对账单 -->
  <select id="findCheckInfoIdAndNo" parameterType="com.ecommerce.information.api.dto.bill.check.KeyValueQueryDTO" resultType="com.ecommerce.information.api.dto.bill.check.KeyValueDTO" >
    select distinct bill_check_id as id,bill_check_no as name from bill_check_info where
    del_flg = 0
    and (payee_member_id=#{operatorMemberId}   or  payer_member_id=#{operatorMemberId})
    <if test="billCheckId != null and billCheckId != ''">
      and bill_check_id = #{billCheckId}
    </if>
    <if test="keyword != null and keyword != ''">
      and bill_check_no like concat('%',#{keyword},'%')
    </if>
    order by name desc
    limit 20
  </select>

</mapper>