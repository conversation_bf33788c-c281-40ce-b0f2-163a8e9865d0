<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.information.bill.check.dao.mapper.BillCheckRuleMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.information.bill.check.dao.vo.BillCheckRule">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="bill_check_rule_id" jdbcType="VARCHAR" property="billCheckRuleId" />
    <result column="member_role" jdbcType="VARCHAR" property="memberRole" />
    <result column="bill_check_type" jdbcType="VARCHAR" property="billCheckType" />
    <result column="cost_type" jdbcType="VARCHAR" property="costType" />
    <result column="check_cycle" jdbcType="VARCHAR" property="checkCycle" />
    <result column="check_day" jdbcType="TINYINT" property="checkDay" />
    <result column="auto_create" jdbcType="BIT" property="autoCreate" />
    <result column="create_day" jdbcType="TINYINT" property="createDay" />
    <result column="payer_member_id" jdbcType="VARCHAR" property="payerMemberId" />
    <result column="payer_member_name" jdbcType="VARCHAR" property="payerMemberName" />
    <result column="payee_member_id" jdbcType="VARCHAR" property="payeeMemberId" />
    <result column="payee_member_name" jdbcType="VARCHAR" property="payeeMemberName" />
    <result column="invoice_type" jdbcType="BIT" property="invoiceType" />
    <result column="driver_flg" jdbcType="BIT" property="driverFlg" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

    <select id="getAllList" resultType="com.ecommerce.information.api.dto.bill.check.rule.BillCheckRuleDTO">
        SELECT *
        FROM bill_check_rule
        <where>
          del_flg = 0 AND check_cycle != 'custom'
          <if test="queryDTO.costType != null and queryDTO.costType != ''">
            AND cost_type = #{queryDTO.costType}
          </if>
          <!-- 手工生成对账单时，付款方搜索下拉数据重复-->
          <if test="queryDTO.billCheckType != null and queryDTO.billCheckType != ''">
            AND bill_check_type = #{queryDTO.billCheckType}
          </if>
          <if test="queryDTO.checkCycle != null and queryDTO.checkCycle != ''">
            AND check_cycle = #{queryDTO.checkCycle}
          </if>
          <if test="queryDTO.memberRole != null and queryDTO.memberRole != ''">
            AND member_role = #{queryDTO.memberRole}
          </if>
          <if test="queryDTO.payerMemberId != null and queryDTO.payerMemberId != ''">
            AND payer_member_id = #{queryDTO.payerMemberId}
          </if>
          <if test="queryDTO.payerMemberName != null and queryDTO.payerMemberName != ''">
            AND payer_member_name LIKE CONCAT('%', #{queryDTO.payerMemberName}, '%')
          </if>
          <if test="queryDTO.payeeMemberId != null and queryDTO.payeeMemberId != ''">
            AND payee_member_id = #{queryDTO.payeeMemberId}
          </if>
          <if test="queryDTO.payeeMemberName != null and queryDTO.payeeMemberName != ''">
            AND payee_member_name LIKE CONCAT('%', #{queryDTO.payeeMemberName}, '%')
          </if>
          <if test="queryDTO.autoCreate != null ">
            AND auto_areate = #{queryDTO.auto_areate}
          </if>
          <if test="list != null and list.size() > 0">
            AND
            <foreach collection="list" item="item" open="(" separator="OR" close=")">
              (
              member_role = #{item.memberRole}
              <if test="item.payerMemberId != null and item.payerMemberId != ''">
                AND payer_member_id = #{item.payerMemberId}
              </if>
              <if test="item.payeeMemberId != null and item.payeeMemberId != ''">
                AND payee_member_id = #{item.payeeMemberId}
              </if>
              )
            </foreach>
          </if>
        </where>
        ORDER BY create_time DESC
    </select>
</mapper>