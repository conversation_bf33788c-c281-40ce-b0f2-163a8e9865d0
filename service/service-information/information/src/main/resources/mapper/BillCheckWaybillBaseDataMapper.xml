<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.information.bill.check.dao.mapper.BillCheckWaybillBaseDataMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.information.bill.check.dao.vo.BillCheckWaybillBaseData">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="waybill_data_id" jdbcType="VARCHAR" property="waybillDataId" />
    <result column="order_code" jdbcType="VARCHAR" property="orderCode" />
    <result column="take_code" jdbcType="VARCHAR" property="takeCode" />
    <result column="delivery_bill_num" jdbcType="VARCHAR" property="deliveryBillNum" />
    <result column="waybill_num" jdbcType="VARCHAR" property="waybillNum" />
    <result column="transport_type" jdbcType="VARCHAR" property="transportType" />
    <result column="invoice_type" jdbcType="BIT" property="invoiceType" />
    <result column="buyer_id" jdbcType="VARCHAR" property="buyerId" />
    <result column="buyer_name" jdbcType="VARCHAR" property="buyerName" />
    <result column="seller_id" jdbcType="VARCHAR" property="sellerId" />
    <result column="seller_name" jdbcType="VARCHAR" property="sellerName" />
    <result column="consignor_id" jdbcType="VARCHAR" property="consignorId" />
    <result column="consignor_name" jdbcType="VARCHAR" property="consignorName" />
    <result column="carrier_id" jdbcType="VARCHAR" property="carrierId" />
    <result column="carrier_name" jdbcType="VARCHAR" property="carrierName" />
    <result column="receive_provinces" jdbcType="VARCHAR" property="receiveProvinces" />
    <result column="goods_id" jdbcType="VARCHAR" property="goodsId" />
    <result column="goods_category_code" jdbcType="VARCHAR" property="goodsCategoryCode" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="transport_category_id" jdbcType="VARCHAR" property="transportCategoryId" />
    <result column="transport_category_name" jdbcType="VARCHAR" property="transportCategoryName" />
    <result column="sale_region_id" jdbcType="VARCHAR" property="saleRegionId" />
    <result column="sale_region_name" jdbcType="VARCHAR" property="saleRegionName" />
    <result column="vehicle_num" jdbcType="VARCHAR" property="vehicleNum" />
    <result column="vehicle_payload" jdbcType="DECIMAL" property="vehiclePayload" />
    <result column="deliver_way" jdbcType="VARCHAR" property="deliverWay" />
    <result column="waybill_deliver_way" jdbcType="VARCHAR" property="waybillDeliverWay" />
    <result column="payer_member_id" jdbcType="VARCHAR" property="payerMemberId" />
    <result column="payer_member_name" jdbcType="VARCHAR" property="payerMemberName" />
    <result column="payee_member_id" jdbcType="VARCHAR" property="payeeMemberId" />
    <result column="payee_member_name" jdbcType="VARCHAR" property="payeeMemberName" />
    <result column="warehouse_id" jdbcType="VARCHAR" property="warehouseId" />
    <result column="warehouse_name" jdbcType="VARCHAR" property="warehouseName" />
    <result column="warehouse_address" jdbcType="VARCHAR" property="warehouseAddress" />
    <result column="receive_address_id" jdbcType="VARCHAR" property="receiveAddressId" />
    <result column="receive_address_name" jdbcType="VARCHAR" property="receiveAddressName" />
    <result column="receive_address" jdbcType="VARCHAR" property="receiveAddress" />
    <result column="picking_wharf_id" jdbcType="VARCHAR" property="pickingWharfId" />
    <result column="picking_wharf_name" jdbcType="VARCHAR" property="pickingWharfName" />
    <result column="picking_wharf_address" jdbcType="VARCHAR" property="pickingWharfAddress" />
    <result column="receiving_wharf_id" jdbcType="VARCHAR" property="receivingWharfId" />
    <result column="receiving_wharf_name" jdbcType="VARCHAR" property="receivingWharfName" />
    <result column="receiving_wharf_address" jdbcType="VARCHAR" property="receivingWharfAddress" />
    <result column="unload_port_id" jdbcType="VARCHAR" property="unloadPortId" />
    <result column="unload_port_name" jdbcType="VARCHAR" property="unloadPortName" />
    <result column="unload_port_address" jdbcType="VARCHAR" property="unloadPortAddress" />
    <result column="unit_goods_price" jdbcType="DECIMAL" property="unitGoodsPrice" />
    <result column="newest_goods_price" jdbcType="DECIMAL" property="newestGoodsPrice" />
    <result column="unit_logistic_price" jdbcType="DECIMAL" property="unitLogisticPrice" />
    <result column="newest_logistic_price" jdbcType="DECIMAL" property="newestLogisticPrice" />
    <result column="send_quantity" jdbcType="DECIMAL" property="sendQuantity" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />
    <result column="leave_warehouse_time" jdbcType="TIMESTAMP" property="leaveWarehouseTime" />
    <result column="complete_time" jdbcType="TIMESTAMP" property="completeTime" />
    <result column="distance" jdbcType="VARCHAR" property="distance" />
    <result column="contract_id" jdbcType="VARCHAR" property="contractId" />
    <result column="contract_number" jdbcType="VARCHAR" property="contractNumber" />
    <result column="contract_project_name" jdbcType="VARCHAR" property="contractProjectName" />
    <result column="contract_address_name" jdbcType="VARCHAR" property="contractAddressName" />
    <result column="adjust_amount" jdbcType="DECIMAL" property="adjustAmount" />
    <result column="strength_level" jdbcType="VARCHAR" property="strengthLevel" />
    <result column="sign_account_id" jdbcType="VARCHAR" property="signAccountId" />
    <result column="sign_account_name" jdbcType="VARCHAR" property="signAccountName" />
    <result column="sign_time" jdbcType="VARCHAR" property="signTime" />
    <result column="sign_quantity" jdbcType="DECIMAL" property="signQuantity" />
    <result column="signer" jdbcType="VARCHAR" property="signer" />
    <result column="signer_phone" jdbcType="VARCHAR" property="signerPhone" />
    <result column="tare_weight" jdbcType="DECIMAL" property="tareWeight" />
    <result column="gross_weight" jdbcType="DECIMAL" property="grossWeight" />
    <result column="add_item_price" jdbcType="DECIMAL" property="addItemPrice" />
    <result column="add_item_amount" jdbcType="DECIMAL" property="addItemAmount" />
    <result column="add_item_slump" jdbcType="VARCHAR" property="addItemSlump" />
    <result column="floor_truckage" jdbcType="DECIMAL" property="floorTruckage" />
    <result column="empty_load_fee" jdbcType="DECIMAL" property="emptyLoadFee" />
    <result column="return_reason" jdbcType="VARCHAR" property="returnReason" />
    <result column="perfect_flg" jdbcType="BIT" property="perfectFlg" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>
</mapper>