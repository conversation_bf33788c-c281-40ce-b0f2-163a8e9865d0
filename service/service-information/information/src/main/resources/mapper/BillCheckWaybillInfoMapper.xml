<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.information.bill.check.dao.mapper.BillCheckWaybillInfoMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.information.bill.check.dao.vo.BillCheckWaybillInfo">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="bill_check_waybill_info" jdbcType="VARCHAR" property="billCheckWaybillInfo" />
    <result column="bill_check_id" jdbcType="VARCHAR" property="billCheckId" />
    <result column="bill_check_no" jdbcType="VARCHAR" property="billCheckNo" />
    <result column="cost_type" jdbcType="VARCHAR" property="costType" />
    <result column="member_role" jdbcType="VARCHAR" property="memberRole" />
    <result column="order_code" jdbcType="VARCHAR" property="orderCode" />
    <result column="take_code" jdbcType="VARCHAR" property="takeCode" />
    <result column="delivery_bill_num" jdbcType="VARCHAR" property="deliveryBillNum" />
    <result column="waybill_num" jdbcType="VARCHAR" property="waybillNum" />
    <result column="transport_type" jdbcType="VARCHAR" property="transportType" />
    <result column="invoice_type" jdbcType="BIT" property="invoiceType" />
    <result column="buyer_id" jdbcType="VARCHAR" property="buyerId" />
    <result column="buyer_name" jdbcType="VARCHAR" property="buyerName" />
    <result column="seller_id" jdbcType="VARCHAR" property="sellerId" />
    <result column="seller_name" jdbcType="VARCHAR" property="sellerName" />
    <result column="consignor_id" jdbcType="VARCHAR" property="consignorId" />
    <result column="consignor_name" jdbcType="VARCHAR" property="consignorName" />
    <result column="carrier_id" jdbcType="VARCHAR" property="carrierId" />
    <result column="carrier_name" jdbcType="VARCHAR" property="carrierName" />
    <result column="goods_id" jdbcType="VARCHAR" property="goodsId" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="transport_category_id" jdbcType="VARCHAR" property="transportCategoryId" />
    <result column="transport_category_name" jdbcType="VARCHAR" property="transportCategoryName" />
    <result column="sale_region_id" jdbcType="VARCHAR" property="saleRegionId" />
    <result column="sale_region_name" jdbcType="VARCHAR" property="saleRegionName" />
    <result column="vehicle_num" jdbcType="VARCHAR" property="vehicleNum" />
    <result column="vehicle_payload" jdbcType="DECIMAL" property="vehiclePayload" />
    <result column="deliver_way" jdbcType="VARCHAR" property="deliverWay" />
    <result column="waybill_deliver_way" jdbcType="VARCHAR" property="waybillDeliverWay" />
    <result column="payer_member_id" jdbcType="VARCHAR" property="payerMemberId" />
    <result column="payer_member_name" jdbcType="VARCHAR" property="payerMemberName" />
    <result column="payee_member_id" jdbcType="VARCHAR" property="payeeMemberId" />
    <result column="payee_member_name" jdbcType="VARCHAR" property="payeeMemberName" />
    <result column="warehouse_id" jdbcType="VARCHAR" property="warehouseId" />
    <result column="warehouse_name" jdbcType="VARCHAR" property="warehouseName" />
    <result column="warehouse_address" jdbcType="VARCHAR" property="warehouseAddress" />
    <result column="receive_address_id" jdbcType="VARCHAR" property="receiveAddressId" />
    <result column="receive_address_name" jdbcType="VARCHAR" property="receiveAddressName" />
    <result column="receive_address" jdbcType="VARCHAR" property="receiveAddress" />
    <result column="picking_wharf_id" jdbcType="VARCHAR" property="pickingWharfId" />
    <result column="picking_wharf_name" jdbcType="VARCHAR" property="pickingWharfName" />
    <result column="picking_wharf_address" jdbcType="VARCHAR" property="pickingWharfAddress" />
    <result column="receiving_wharf_id" jdbcType="VARCHAR" property="receivingWharfId" />
    <result column="receiving_wharf_name" jdbcType="VARCHAR" property="receivingWharfName" />
    <result column="receiving_wharf_address" jdbcType="VARCHAR" property="receivingWharfAddress" />
    <result column="unload_port_id" jdbcType="VARCHAR" property="unloadPortId" />
    <result column="unload_port_name" jdbcType="VARCHAR" property="unloadPortName" />
    <result column="unload_port_address" jdbcType="VARCHAR" property="unloadPortAddress" />
    <result column="unit_goods_price" jdbcType="DECIMAL" property="unitGoodsPrice" />
    <result column="newest_goods_price" jdbcType="DECIMAL" property="newestGoodsPrice" />
    <result column="unit_logistic_price" jdbcType="DECIMAL" property="unitLogisticPrice" />
    <result column="newest_logistic_price" jdbcType="DECIMAL" property="newestLogisticPrice" />
    <result column="send_quantity" jdbcType="DECIMAL" property="sendQuantity" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />
    <result column="leave_warehouse_time" jdbcType="TIMESTAMP" property="leaveWarehouseTime" />
    <result column="complete_time" jdbcType="TIMESTAMP" property="completeTime" />
    <result column="distance" jdbcType="VARCHAR" property="distance" />
    <result column="contract_id" jdbcType="VARCHAR" property="contractId" />
    <result column="contract_number" jdbcType="VARCHAR" property="contractNumber" />
    <result column="contract_project_name" jdbcType="VARCHAR" property="contractProjectName" />
    <result column="contract_address_name" jdbcType="VARCHAR" property="contractAddressName" />
    <result column="adjust_amount" jdbcType="DECIMAL" property="adjustAmount" />
    <result column="strength_level" jdbcType="VARCHAR" property="strengthLevel" />
    <result column="signer" jdbcType="VARCHAR" property="signer" />
    <result column="sign_phone" jdbcType="VARCHAR" property="signPhone" />
    <result column="sign_time" jdbcType="TIMESTAMP" property="signTime" />
    <result column="sign_quantity" jdbcType="DECIMAL" property="signQuantity" />
    <result column="tare_weight" jdbcType="DECIMAL" property="tareWeight" />
    <result column="gross_weight" jdbcType="DECIMAL" property="grossWeight" />

    <result column="add_item_price" jdbcType="DECIMAL" property="addItemPrice" />
    <result column="add_item_amount" jdbcType="DECIMAL" property="addItemAmount" />
    <result column="add_item_slump" jdbcType="VARCHAR" property="addItemSlump" />
    <result column="floor_truckage" jdbcType="DECIMAL" property="floorTruckage" />
    <result column="empty_load_fee" jdbcType="DECIMAL" property="emptyLoadFee" />
    <result column="return_reason" jdbcType="VARCHAR" property="returnReason" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>

  <!-- 查询运单号 -->
  <select id="findWaybillNum" parameterType="com.ecommerce.information.api.dto.bill.check.KeyValueQueryDTO" resultType="com.ecommerce.information.api.dto.bill.check.KeyValueDTO" >
    select distinct null as id,waybill_num as name from bill_check_waybill_info where
    del_flg = 0
    and bill_check_id = #{billCheckId}
    <if test="keyword != null and keyword != ''">
      and waybill_num like concat('%',#{keyword},'%')
    </if>
    order by name
    limit 20
  </select>

  <!-- 查询车、船 -->
  <select id="findVehicleNum" parameterType="com.ecommerce.information.api.dto.bill.check.KeyValueQueryDTO" resultType="com.ecommerce.information.api.dto.bill.check.KeyValueDTO" >
    select distinct null as id,vehicle_num as name from bill_check_waybill_info where
    del_flg = 0
    and bill_check_id = #{billCheckId}
    <!-- 如果是查询车 则运输类型未路运 com.ecommercelogistics.api.enums.TransportToolTypeEnum-->
    <if test="searchType != null and searchType == 'car'">
      and transport_type = '030230100'
    </if>
    <if test="searchType != null and searchType == 'ship'">
      and transport_type = '030230200'
    </if>
    <if test="keyword != null and keyword != ''">
      and vehicle_num like concat('%',#{keyword},'%')
    </if>
    order by name
    limit 20
  </select>

  <!-- 查询出库点 -->
  <select id="findWarehouseIdAndName" parameterType="com.ecommerce.information.api.dto.bill.check.KeyValueQueryDTO" resultType="com.ecommerce.information.api.dto.bill.check.KeyValueDTO" >
    select distinct warehouse_id as id,warehouse_name as name from bill_check_waybill_info where
    del_flg = 0
    and bill_check_id = #{billCheckId}
    <if test="keyword != null and keyword != ''">
      and warehouse_name like concat('%',#{keyword},'%')
    </if>
    order by name
    limit 20
  </select>

  <!--
   来自原型： xxx-数字化转型项目_页面原型_财务对账管理-v0.6-20210324.rp  逻辑说明页面
   4 对账单详情的汇总条件:
    a. 货款（除）：按【客户】、【商品名称】、【运输品类】、【销售区域】、【出库点】、【单价】、【时间段】汇总，其中【时间段】为同一单价所执行的运单的【时间区间】，例如1-10号价格420，11-20价格430，21-30价格420，应该汇总为3条记录
    b. 货款：（一个合同一个对账单），按【商品名称】、【单价】、【时间段】汇总
    c. 汽运、船运物流款：
        i. 平台、卖家发起的，收款方为承运商（一个承运商一个对账单）；以【客户】、【商品名称】、【运输品类】、【运输起点】、【运输终点】、【运费单价】、【时间段】汇总，其中【时间段】为同一单价所执行的运单的【时间区间】
        ii. 承运商发起的,付款人为买家（一个买家一个对账单）；以【商品名称】、【运输品类】、【运输起点】、【运输终点】、【运费单价】、【时间段】汇总，其中【时间段】为同一单价所执行的运单的【时间区间】
    d. 社会司机运费：
        i. 平台发起，对各社会司机运费对账，（一个社会司机一个对账单）
   -->
  <select id="distinctKey" resultType="com.ecommerce.information.bill.check.dao.vo.BillCheckWaybillInfo" resultMap="BaseResultMap" >
    <if test="costType == 'goods'">
    select distinct buyer_id,goods_id,transport_category_id,sale_region_id,warehouse_id,deliver_way
    </if>
    <if test="costType == 'concrete'">
      select distinct contract_number,goods_id
    </if>
    <if test="costType == 'logistics_road' ">
      <if test="memberRole == 'platform' or memberRole == 'seller'">
        select distinct buyer_id,goods_id,transport_category_id,warehouse_id,receive_address_id
      </if>
      <if test="memberRole == 'carrier'">
        select distinct goods_id,transport_category_id,warehouse_id,receive_address_id
      </if>
    </if>
    <if test=" costType == 'logistics_ship' ">
      <if test="memberRole == 'platform' or memberRole == 'seller'">
        select distinct buyer_id,goods_id,transport_category_id,picking_wharf_id,receiving_wharf_id
      </if>
      <if test="memberRole == 'carrier'">
        select distinct goods_id,transport_category_id,picking_wharf_id,receiving_wharf_id
      </if>
    </if>
    <if test="costType == 'driver'">
      <!-- 不需要 -->
      select distinct buyer_id
    </if>
    <if test="costType == 'entrepot_storage_contractor'">
      <!-- 不需要 -->
      select distinct buyer_id
    </if>
    from bill_check_waybill_info where bill_check_id = #{billCheckId}
  </select>

</mapper>