<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.information.dao.mapper.BillCheckWaybillRefundBaseDataMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.information.dao.vo.BillCheckWaybillRefundBaseData">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="refund_data_id" jdbcType="VARCHAR" property="refundDataId" />
    <result column="take_code" jdbcType="VARCHAR" property="takeCode" />
    <result column="goods_id" jdbcType="VARCHAR" property="goodsId" />
    <result column="waybill_id" jdbcType="VARCHAR" property="waybillId" />
    <result column="waybill_num" jdbcType="VARCHAR" property="waybillNum" />
    <result column="refund_time" jdbcType="VARCHAR" property="refundTime" />
    <result column="refund_reason" jdbcType="VARCHAR" property="refundReason" />
    <result column="refund_remark" jdbcType="VARCHAR" property="refundRemark" />
    <result column="refund_create_flg" jdbcType="BIT" property="refundCreateFlg" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>
</mapper>