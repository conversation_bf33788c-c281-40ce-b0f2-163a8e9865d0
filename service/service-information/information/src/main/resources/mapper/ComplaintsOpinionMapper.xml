<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.information.dao.mapper.ComplaintsOpinionMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.information.dao.vo.ComplaintsOpinion">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="complaints_opinion_id" jdbcType="VARCHAR" property="complaintsOpinionId" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="type_name" jdbcType="VARCHAR" property="typeName" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_member_id" jdbcType="VARCHAR" property="createMemberId" />
    <result column="create_member_name" jdbcType="VARCHAR" property="createMemberName" />
    <result column="create_phone" jdbcType="VARCHAR" property="createPhone" />
    <result column="order_num" jdbcType="VARCHAR" property="orderNum" />
    <result column="defendant_member_id" jdbcType="VARCHAR" property="defendantMemberId" />
    <result column="defendant_member_name" jdbcType="VARCHAR" property="defendantMemberName" />
    <result column="content_keyword" jdbcType="VARCHAR" property="contentKeyword" />
    <result column="content_url" jdbcType="VARCHAR" property="contentUrl" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="need_platform" jdbcType="INTEGER" property="needPlatform" />
    <result column="attachment_id" jdbcType="VARCHAR" property="attachmentId" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
  </resultMap>
</mapper>