<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.information.dao.mapper.ComplaintsOpinionRecordMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.information.dao.vo.ComplaintsOpinionRecord">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="complaints_opinion_record_id" jdbcType="VARCHAR" property="complaintsOpinionRecordId" />
    <result column="complaints_opinion_id" jdbcType="VARCHAR" property="complaintsOpinionId" />
    <result column="process_type" jdbcType="VARCHAR" property="processType" />
    <result column="process_member_id" jdbcType="VARCHAR" property="processMemberId" />
    <result column="process_member_name" jdbcType="VARCHAR" property="processMemberName" />
    <result column="process_content_url" jdbcType="VARCHAR" property="processContentUrl" />
    <result column="process_content" jdbcType="VARCHAR" property="processContent" />
    <result column="process_attachment_id" jdbcType="VARCHAR" property="processAttachmentId" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
  </resultMap>
</mapper>