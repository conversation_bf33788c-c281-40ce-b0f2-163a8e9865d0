<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.information.dao.mapper.ContractBreachConfigMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.information.dao.vo.ContractBreachConfig">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="contract_breach_config_id" jdbcType="VARCHAR" property="contractBreachConfigId" />
    <result column="config_type" jdbcType="INTEGER" property="configType" />
    <result column="member_id" jdbcType="VARCHAR" property="memberId" />
    <result column="member_name" jdbcType="VARCHAR" property="memberName" />
    <result column="member_code" jdbcType="VARCHAR" property="memberCode" />
    <result column="customer_id" jdbcType="VARCHAR" property="customerId" />
    <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
    <result column="customer_code" jdbcType="VARCHAR" property="customerCode" />
    <result column="on_sale_flg" jdbcType="BIT" property="onSaleFlg" />
    <result column="on_sale_num" jdbcType="INTEGER" property="onSaleNum" />
    <result column="on_sale_desc" jdbcType="VARCHAR" property="onSaleDesc" />
    <result column="group_buy_not_pick_up_flg" jdbcType="BIT" property="groupBuyNotPickUpFlg" />
    <result column="group_buy_not_pick_up_num" jdbcType="INTEGER" property="groupBuyNotPickUpNum" />
    <result column="group_buy_not_pick_up_desc" jdbcType="VARCHAR" property="groupBuyNotPickUpDesc" />
    <result column="buy_unpaid_flg" jdbcType="BIT" property="buyUnpaidFlg" />
    <result column="buy_unpaid_num" jdbcType="INTEGER" property="buyUnpaidNum" />
    <result column="buy_unpaid_desc" jdbcType="VARCHAR" property="buyUnpaidDesc" />
    <result column="paid_not_pick_up_flg" jdbcType="BIT" property="paidNotPickUpFlg" />
    <result column="paid_not_pick_up_num" jdbcType="INTEGER" property="paidNotPickUpNum" />
    <result column="paid_not_pick_up_desc" jdbcType="VARCHAR" property="paidNotPickUpDesc" />
    <result column="financing_auth_flg" jdbcType="BIT" property="financingAuthFlg" />
    <result column="financing_auth_num" jdbcType="INTEGER" property="financingAuthNum" />
    <result column="financing_auth_desc" jdbcType="VARCHAR" property="financingAuthDesc" />
    <result column="prepayment_flg" jdbcType="BIT" property="prepaymentFlg" />
    <result column="prepayment_num" jdbcType="INTEGER" property="prepaymentNum" />
    <result column="prepayment_desc" jdbcType="VARCHAR" property="prepaymentDesc" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

</mapper>