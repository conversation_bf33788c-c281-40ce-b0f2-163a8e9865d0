<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.information.dao.mapper.ContractBreachRecordMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.information.dao.vo.ContractBreachRecord">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="contract_breach_record_id" jdbcType="VARCHAR" property="contractBreachRecordId" />
    <result column="config_type" jdbcType="INTEGER" property="configType" />
    <result column="member_id" jdbcType="VARCHAR" property="memberId" />
    <result column="member_code" jdbcType="VARCHAR" property="memberCode" />
    <result column="member_name" jdbcType="VARCHAR" property="memberName" />
    <result column="customer_id" jdbcType="VARCHAR" property="customerId" />
    <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
    <result column="customer_code" jdbcType="VARCHAR" property="customerCode" />
    <result column="order_number" jdbcType="VARCHAR" property="orderNumber" />
    <result column="order_time" jdbcType="TIMESTAMP" property="orderTime" />
    <result column="goods_type" jdbcType="VARCHAR" property="goodsType" />
    <result column="goods_type_code" jdbcType="VARCHAR" property="goodsTypeCode" />
    <result column="rule_name" jdbcType="VARCHAR" property="ruleName" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="appeal_reason" jdbcType="VARCHAR" property="appealReason" />
    <result column="cancel_flg" jdbcType="BIT" property="cancelFlg" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

</mapper>