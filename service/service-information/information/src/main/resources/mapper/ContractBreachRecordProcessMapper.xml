<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.information.dao.mapper.ContractBreachRecordProcessMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.information.dao.vo.ContractBreachRecordProcess">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="contract_breach_record_process_id" jdbcType="CHAR" property="contractBreachRecordProcessId" />
    <result column="contract_breach_record_id" jdbcType="VARCHAR" property="contractBreachRecordId" />
    <result column="member_id" jdbcType="VARCHAR" property="memberId" />
    <result column="member_code" jdbcType="VARCHAR" property="memberCode" />
    <result column="member_name" jdbcType="VARCHAR" property="memberName" />
    <result column="operate_type" jdbcType="VARCHAR" property="operateType" />
    <result column="operate_comment" jdbcType="VARCHAR" property="operateComment" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>