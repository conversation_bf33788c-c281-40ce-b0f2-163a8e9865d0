<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.information.dao.mapper.CreditQuotaMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.information.dao.vo.CreditQuota">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="credit_quota_id" jdbcType="VARCHAR" property="creditQuotaId" />
    <result column="quota_name" jdbcType="VARCHAR" property="quotaName" />
    <result column="quota_code" jdbcType="VARCHAR" property="quotaCode" />
    <result column="parent_quota_id" jdbcType="VARCHAR" property="parentQuotaId" />
    <result column="parent_quota_code" jdbcType="VARCHAR" property="parentQuotaCode" />
    <result column="quota_desc" jdbcType="VARCHAR" property="quotaDesc" />
    <result column="quota_source" jdbcType="VARCHAR" property="quotaSource" />
    <result column="quota_scale" jdbcType="DECIMAL" property="quotaScale" />
    <result column="quota_level" jdbcType="INTEGER" property="quotaLevel" />
    <result column="quota_option_type" jdbcType="VARCHAR" property="quotaOptionType" />
    <result column="quota_max_score" jdbcType="INTEGER" property="quotaMaxScore" />
    <result column="quota_sort" jdbcType="INTEGER" property="quotaSort" />
    <result column="del_flg" jdbcType="INTEGER" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <resultMap id="DetailDTOResultMap" type="com.ecommerce.information.api.dto.credit.CreditQuotaDetailDTO">
    <result column="credit_quota_id" jdbcType="VARCHAR" property="creditQuotaId" />
    <result column="quota_name" jdbcType="VARCHAR" property="quotaName" />
    <result column="quota_code" jdbcType="VARCHAR" property="quotaCode" />
    <result column="parent_quota_id" jdbcType="VARCHAR" property="parentQuotaId" />
    <result column="parent_quota_code" jdbcType="VARCHAR" property="parentQuotaCode" />
    <result column="quota_desc" jdbcType="VARCHAR" property="quotaDesc" />
    <result column="quota_source" jdbcType="VARCHAR" property="quotaSource" />
    <result column="quota_scale" jdbcType="DECIMAL" property="quotaScale" />
    <result column="quota_level" jdbcType="INTEGER" property="quotaLevel" />
    <result column="quota_option_type" jdbcType="VARCHAR" property="quotaOptionType" />
    <result column="quota_max_score" jdbcType="INTEGER" property="quotaMaxScore" />
    <result column="quota_sort" jdbcType="INTEGER" property="quotaSort" />
  </resultMap>

  <select id="queryQuotaLevelMaxQuotaCode" resultType="java.lang.String">
    select max(quota_code) from ii_credit_quota where quota_level = #{quotaLevel} and parent_quota_id = #{parentQuotaId}
  </select>

  <select id="queryAllQuotaList" resultMap="DetailDTOResultMap">
    select icq.credit_quota_id,
           icq.quota_name,
           icq.quota_code,
           icq.parent_quota_id,
           icq.parent_quota_code,
           icq.quota_desc,
           icq.quota_source,
           icq.quota_scale,
           icq.quota_level,
           icq.quota_option_type,
           icq.quota_max_score,
           icq.quota_sort
    from ii_credit_quota as icq
    where icq.del_flg = 0
    order by icq.quota_level, icq.quota_sort
  </select>

</mapper>