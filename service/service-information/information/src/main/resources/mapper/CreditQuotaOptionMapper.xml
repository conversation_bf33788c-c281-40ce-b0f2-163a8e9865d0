<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.information.dao.mapper.CreditQuotaOptionMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.information.dao.vo.CreditQuotaOption">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="quota_option_id" jdbcType="VARCHAR" property="quotaOptionId" />
    <result column="credit_quota_id" jdbcType="VARCHAR" property="creditQuotaId" />
    <result column="quota_code" jdbcType="VARCHAR" property="quotaCode" />
    <result column="option_name" jdbcType="VARCHAR" property="optionName" />
    <result column="option_score" jdbcType="INTEGER" property="optionScore" />
    <result column="option_sort" jdbcType="INTEGER" property="optionSort" />
    <result column="del_flg" jdbcType="INTEGER" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <resultMap id="DetailResultMap" type="com.ecommerce.information.api.dto.credit.CreditQuotaOptionDetailDTO">
    <result column="quota_option_id" jdbcType="VARCHAR" property="quotaOptionId" />
    <result column="credit_quota_id" jdbcType="VARCHAR" property="creditQuotaId" />
    <result column="quota_code" jdbcType="VARCHAR" property="quotaCode" />
    <result column="option_name" jdbcType="VARCHAR" property="optionName" />
    <result column="option_score" jdbcType="INTEGER" property="optionScore" />
    <result column="option_sort" jdbcType="INTEGER" property="optionSort" />
  </resultMap>

  <select id="queryAllOptionList" resultMap="DetailResultMap">
    select op.quota_option_id,
           op.credit_quota_id,
           op.quota_code,
           op.option_name,
           op.option_score,
           op.option_sort
    from ii_credit_quota_option as op
    where op.del_flg = 0
    order by op.quota_code, op.option_sort
  </select>

</mapper>