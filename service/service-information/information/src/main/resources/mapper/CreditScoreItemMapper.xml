<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.information.dao.mapper.CreditScoreItemMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.information.dao.vo.CreditScoreItem">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="score_item_id" jdbcType="VARCHAR" property="scoreItemId" />
    <result column="credit_score_id" jdbcType="VARCHAR" property="creditScoreId" />
    <result column="quota_code" jdbcType="VARCHAR" property="quotaCode" />
    <result column="quota_option_id" jdbcType="VARCHAR" property="quotaOptionId" />
    <result column="quota_option_name" jdbcType="VARCHAR" property="quotaOptionName" />
    <result column="option_score" jdbcType="VARCHAR" property="optionScore" />
    <result column="del_flg" jdbcType="INTEGER" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <select id="selectOptionsForCustomer" resultType="com.ecommerce.information.api.dto.credit.CreditQuotaOptionDetailDTO">
    select item.quota_option_id as quotaOptionId,
           item.quota_code as quotaCode,
           item.quota_option_name as optionName,
           item.option_score as optionScore
    from ii_credit_score as score
    left join ii_credit_score_item as item on score.credit_score_id = item.credit_score_id
    where score.customer_id = #{customerId}
      and score.customer_type = #{customerType}
      and score.credit_period = #{creditPeriod}
    <if test="creditStatus != null and creditStatus != ''">
      and score.credit_status = #{creditStatus}
    </if>
      and score.del_flg = 0
      and item.del_flg = 0
    order by item.quota_code
  </select>

</mapper>