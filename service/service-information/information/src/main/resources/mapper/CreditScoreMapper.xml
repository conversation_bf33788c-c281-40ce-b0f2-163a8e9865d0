<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.information.dao.mapper.CreditScoreMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.information.dao.vo.CreditScore">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="credit_score_id" jdbcType="VARCHAR" property="creditScoreId" />
    <result column="customer_id" jdbcType="VARCHAR" property="customerId" />
    <result column="customer_code" jdbcType="VARCHAR" property="customerCode" />
    <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
    <result column="customer_type" jdbcType="VARCHAR" property="customerType" />
    <result column="credit_score" jdbcType="DECIMAL" property="creditScore" />
    <result column="credit_period" jdbcType="VARCHAR" property="creditPeriod" />
    <result column="credit_status" jdbcType="VARCHAR" property="creditStatus" />
    <result column="del_flg" jdbcType="INTEGER" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <select id="listQueryByCond" parameterType="com.ecommerce.information.api.dto.credit.CreditSourceQueryDTO"
          resultType="com.ecommerce.information.api.dto.credit.CreditScoreListDTO">
    select ics.credit_score_id as creditScoreId,
           ics.credit_period as creditPeriod,
           ics.customer_code as customerCode,
           ics.customer_name as customerName,
           ics.customer_id as customerId,
           ics.customer_type as customerType,
           ics.credit_score as creditScore,
           ics.credit_status as creditStatus
    from ii_credit_score as ics
    where del_flg = 0
    <if test="creditPeriod != null and creditPeriod != ''">
      and ics.credit_period = #{creditPeriod}
    </if>
    <if test="customerName != null and customerName != ''">
      and ics.customer_name like concat('%', #{customerName}, '%')
    </if>
    <if test="customerId != null and customerId != ''">
      and ics.customer_id = #{customerId}
    </if>
    <if test="customerType != null and customerType != ''">
      and ics.customer_type = #{customerType}
    </if>
    <if test="creditStatus != null and creditStatus != ''">
      and ics.credit_status = #{creditStatus}
    </if>
    order by ics.update_time desc
  </select>

</mapper>