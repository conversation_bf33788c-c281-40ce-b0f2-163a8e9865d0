<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.information.dao.mapper.CreditScoreQuotaMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.information.dao.vo.CreditScoreQuota">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="score_quota_id" jdbcType="VARCHAR" property="scoreQuotaId" />
    <result column="credit_score_id" jdbcType="VARCHAR" property="creditScoreId" />
    <result column="credit_quota_id" jdbcType="VARCHAR" property="creditQuotaId" />
    <result column="quota_name" jdbcType="VARCHAR" property="quotaName" />
    <result column="quota_code" jdbcType="VARCHAR" property="quotaCode" />
    <result column="parent_quota_id" jdbcType="VARCHAR" property="parentQuotaId" />
    <result column="parent_quota_code" jdbcType="VARCHAR" property="parentQuotaCode" />
    <result column="quota_desc" jdbcType="VARCHAR" property="quotaDesc" />
    <result column="quota_source" jdbcType="VARCHAR" property="quotaSource" />
    <result column="quota_scale" jdbcType="DECIMAL" property="quotaScale" />
    <result column="quota_level" jdbcType="INTEGER" property="quotaLevel" />
    <result column="quota_option_type" jdbcType="VARCHAR" property="quotaOptionType" />
    <result column="quota_max_score" jdbcType="INTEGER" property="quotaMaxScore" />
    <result column="quota_sort" jdbcType="INTEGER" property="quotaSort" />
    <result column="del_flg" jdbcType="INTEGER" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <select id="queryQuotasByCreditScoreId" resultType="com.ecommerce.information.api.dto.credit.CreditQuotaDetailDTO">
    select q.credit_quota_id as creditQuotaId,
           q.parent_quota_id as parentQuotaId,
           q.quota_code as quotaCode,
           q.quota_name as quotaName,
           q.parent_quota_code as parentQuotaCode,
           q.quota_desc as quotaDesc,
           q.quota_source as quotaSource,
           q.quota_scale as quotaScale,
           q.quota_level as quotaLevel,
           q.quota_option_type as quotaOptionType,
           q.quota_max_score as quotaMaxScore,
           q.quota_sort as quotaSort
    from ii_credit_score_quota as q
    where q.credit_score_id = #{creditScoreId}
    order by q.quota_code, q.quota_sort
  </select>

</mapper>