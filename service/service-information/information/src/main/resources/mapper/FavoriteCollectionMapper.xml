<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.information.dao.mapper.FavoriteCollectionMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.information.dao.vo.FavoriteCollection">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="member_id" jdbcType="VARCHAR" property="memberId" />
    <result column="account_id" jdbcType="VARCHAR" property="accountId" />
    <result column="favorite_type" jdbcType="INTEGER" property="favoriteType" />
    <result column="prod_id" jdbcType="VARCHAR" property="prodId" />
    <result column="prod_name" jdbcType="VARCHAR" property="prodName" />
    <result column="object_id" jdbcType="VARCHAR" property="objectId" />
    <result column="object_id_type" jdbcType="VARCHAR" property="objectIdType" />
    <result column="order_number" jdbcType="INTEGER" property="orderNumber" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="district" jdbcType="VARCHAR" property="district" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>