<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.information.dao.mapper.HelpCategoryMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.information.dao.vo.HelpCategory">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="parent_id" jdbcType="VARCHAR" property="parentId" />
    <result column="level" jdbcType="INTEGER" property="level" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="order_number" jdbcType="INTEGER" property="orderNumber" />
    <result column="disable_status" jdbcType="CHAR" property="disableStatus" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>