<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.information.dao.mapper.HelpQuestionMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.information.dao.vo.HelpQuestion">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="help_id" jdbcType="VARCHAR" property="helpId" />
    <result column="category_id" jdbcType="VARCHAR" property="categoryId" />
    <result column="help_title" jdbcType="VARCHAR" property="helpTitle" />
    <result column="keyword" jdbcType="VARCHAR" property="keyword" />
    <result column="help_content_url" jdbcType="VARCHAR" property="helpContentUrl" />
    <result column="order_number" jdbcType="INTEGER" property="orderNumber" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="help_content" jdbcType="LONGVARCHAR" property="helpContent" />
  </resultMap>
</mapper>