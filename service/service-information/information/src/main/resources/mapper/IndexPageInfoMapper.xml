<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.information.dao.mapper.IndexPageInfoMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.information.dao.vo.IndexPageInfo">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="index_page_info_id" jdbcType="VARCHAR" property="indexPageInfoId" />
    <result column="type_code" jdbcType="VARCHAR" property="typeCode" />
    <result column="type_text" jdbcType="VARCHAR" property="typeText" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="image_url" jdbcType="VARCHAR" property="imageUrl" />
    <result column="redirect_url" jdbcType="VARCHAR" property="redirectUrl" />
    <result column="star" jdbcType="INTEGER" property="star" />
    <result column="origin_id" jdbcType="VARCHAR" property="originId" />
    <result column="display_order" jdbcType="INTEGER" property="displayOrder" />
    <result column="display_type" jdbcType="VARCHAR" property="displayType" />
    <result column="publish_time" jdbcType="VARCHAR" property="publishTime" />
    <result column="ext2" jdbcType="VARCHAR" property="ext2" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>