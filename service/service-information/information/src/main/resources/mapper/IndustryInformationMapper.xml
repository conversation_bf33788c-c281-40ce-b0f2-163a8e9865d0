<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.information.dao.mapper.IndustryInformationMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.information.dao.vo.IndustryInformation">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="ii_id" jdbcType="VARCHAR" property="iiId" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="category" jdbcType="TINYINT" property="category" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="begin_time" jdbcType="TIMESTAMP" property="beginTime" />
    <result column="failure_time" jdbcType="TIMESTAMP" property="failureTime" />
    <result column="from_source" jdbcType="VARCHAR" property="fromSource" />
    <result column="attach_url" jdbcType="VARCHAR" property="attachUrl" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="content" jdbcType="LONGVARCHAR" property="content" />
    <result column="keywords" jdbcType="VARCHAR" property="keywords" />
  </resultMap>
</mapper>