<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.information.dao.mapper.KfConfigMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.information.dao.vo.KfConfig">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="kf_config_id" jdbcType="VARCHAR" property="kfConfigId" />
    <result column="group_code" jdbcType="VARCHAR" property="groupCode" />
    <result column="member_id" jdbcType="VARCHAR" property="memberId" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="websiteid" jdbcType="VARCHAR" property="websiteid" />
    <result column="wc" jdbcType="VARCHAR" property="wc" />
    <result column="memo" jdbcType="VARCHAR" property="memo" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>