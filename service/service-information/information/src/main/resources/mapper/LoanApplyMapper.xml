<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.information.dao.mapper.LoanApplyMapper">
    <resultMap id="BaseResultMap" type="com.ecommerce.information.dao.vo.LoanApply">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="CHAR" property="id"/>
        <result column="member_id" jdbcType="CHAR" property="memberId"/>
        <result column="member_name" jdbcType="VARCHAR" property="memberName"/>
        <result column="member_code" jdbcType="VARCHAR" property="memberCode" />
        <result column="member_type" jdbcType="VARCHAR" property="memberType" />
        <result column="member_type_name" jdbcType="VARCHAR" property="memberTypeName" />
        <result column="finance_id" jdbcType="VARCHAR" property="financeId"/>
        <result column="finance_name" jdbcType="VARCHAR" property="financeName"/>
        <result column="finance_agent" jdbcType="VARCHAR" property="financeAgent"/>
        <result column="finance_amount" jdbcType="DECIMAL" property="financeAmount"/>
        <result column="reg_address" jdbcType="VARCHAR" property="regAddress"/>
        <result column="legal_name" jdbcType="VARCHAR" property="legalName"/>
        <result column="contact_name" jdbcType="VARCHAR" property="contactName"/>
        <result column="contact_mobile" jdbcType="VARCHAR" property="contactMobile"/>
        <result column="business_license_code" jdbcType="VARCHAR" property="businessLicenseCode"/>
        <result column="province_code" jdbcType="VARCHAR" property="provinceCode"/>
        <result column="province_name" jdbcType="VARCHAR" property="provinceName"/>
        <result column="city_code" jdbcType="VARCHAR" property="cityCode"/>
        <result column="city_name" jdbcType="VARCHAR" property="cityName"/>
        <result column="district_code" jdbcType="VARCHAR" property="districtCode"/>
        <result column="district_name" jdbcType="VARCHAR" property="districtName"/>
        <result column="apply_time" jdbcType="TIMESTAMP" property="applyTime"/>
        <result column="audit_time" jdbcType="TIMESTAMP" property="auditTime"/>
        <result column="complete_time" jdbcType="TIMESTAMP" property="completeTime" />
        <result column="audit_amount" jdbcType="INTEGER" property="auditAmount"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="memo" jdbcType="VARCHAR" property="memo"/>
        <result column="mail" jdbcType="VARCHAR" property="mail"/>
        <result column="del_flg" jdbcType="INTEGER" property="delFlg"/>
        <result column="create_user" jdbcType="CHAR" property="createUser"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user" jdbcType="CHAR" property="updateUser"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <select id="getLoanApplyListByQuery" parameterType="com.ecommerce.information.api.dto.loan.LoanApplyQueryDTO" resultType="com.ecommerce.information.dao.vo.LoanApply">
        SELECT * FROM `ff_loan_apply` WHERE `del_flg` = 0
        <if test="memberId != null and memberId != ''"> AND `member_id` = #{memberId}</if>
        <if test="memberName != null and memberName != ''"> AND `member_name` LIKE CONCAT('%', #{memberName}, '%')</if>
        <if test="financeId != null and financeId != ''"> AND `finance_id` = #{financeId}</if>
        <if test="financeAgent != null and financeAgent != ''"> AND `finance_agent` = #{financeAgent}</if>
        <if test="regAddress != null and regAddress != ''"> AND `reg_address` = #{regAddress}</if>
        <if test="legalName != null and legalName != ''"> AND `legal_name` = #{legalName}</if>
        <if test="contactName != null and contactName != ''"> AND `contact_name` = #{contactName}</if>
        <if test="contactMobile != null and contactMobile != ''"> AND `contact_mobile` = #{contactMobile}</if>
        <if test="businessLicenseCode != null and businessLicenseCode != ''"> AND `business_license_code` = #{businessLicenseCode}</if>
        <if test="applyTimeStart != null and applyTimeStart != ''"> AND `apply_time` &gt;= #{applyTimeStart}</if>
        <if test="applyTimeEnd != null and applyTimeEnd != ''"> AND `apply_time` &lt;= #{applyTimeEnd}</if>
        <if test="auditTimeStart != null and auditTimeStart != ''"> AND `audit_time` &gt;= #{auditTimeStart}</if>
        <if test="auditTimeEnd != null and auditTimeEnd != ''"> AND `audit_time` &lt;= #{auditTimeEnd}</if>
        <if test="status != null"> AND `status` = #{status}</if>
        ORDER BY `create_time` DESC
    </select>

    <select id="getLoanApplyInfoById" resultType="com.ecommerce.information.dao.vo.LoanApply">
        SELECT * FROM `ff_loan_apply` WHERE `id` = #{id} AND `del_flg` = 0
    </select>
</mapper>
