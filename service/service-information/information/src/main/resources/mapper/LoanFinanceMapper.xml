<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.information.dao.mapper.LoanFinanceMapper">
    <resultMap id="BaseResultMap" type="com.ecommerce.information.dao.vo.LoanFinance">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="CHAR" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="image" jdbcType="VARCHAR" property="image"/>
        <result column="sort" jdbcType="INTEGER" property="sort"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="del_flg" jdbcType="INTEGER" property="delFlg"/>
        <result column="create_user" jdbcType="CHAR" property="createUser"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user" jdbcType="CHAR" property="updateUser"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <select id="getLoanFinanceInfoById" resultType="com.ecommerce.information.dao.vo.LoanFinance">
        SELECT * FROM `ff_loan_finance` WHERE `id` = #{id} AND `del_flg` = 0
    </select>

    <select id="getLoanFinanceListByQuery" parameterType="com.ecommerce.information.api.dto.loan.LoanFinanceQueryDTO" resultType="com.ecommerce.information.dao.vo.LoanFinance">
        SELECT * FROM `ff_loan_finance` WHERE `del_flg` = 0
        <if test="name != null and name != ''"> AND `name` = #{name}</if>
        <if test="status != null"> AND `status` = #{status}</if>
        ORDER BY `sort` DESC
    </select>

    <select id="getLoanFinanceList" resultType="com.ecommerce.information.dao.vo.LoanFinance">
        SELECT * FROM `ff_loan_finance` WHERE `del_flg` = 0 ORDER BY `sort` DESC
    </select>
</mapper>
