<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.information.dao.mapper.LoanMemberMapper">
    <resultMap id="BaseResultMap" type="com.ecommerce.information.dao.vo.LoanMember">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="CHAR" property="id"/>
        <result column="member_id" jdbcType="CHAR" property="memberId"/>
        <result column="member_name" jdbcType="VARCHAR" property="memberName"/>
        <result column="member_code" jdbcType="VARCHAR" property="memberCode"/>
        <result column="member_type" jdbcType="VARCHAR" property="memberType"/>
        <result column="reg_address" jdbcType="VARCHAR" property="regAddress"/>
        <result column="finance_ids" jdbcType="VARCHAR" property="financeIds"/>
        <result column="del_flg" jdbcType="INTEGER" property="delFlg"/>
        <result column="create_user" jdbcType="CHAR" property="createUser"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user" jdbcType="CHAR" property="updateUser"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <select id="getLoanMemberInfoByMemberId" resultType="com.ecommerce.information.dao.vo.LoanMember">
        SELECT * FROM `ff_loan_member` WHERE `member_id` = #{memberId}
    </select>

    <select id="getLoanMemberListByQuery" parameterType="com.ecommerce.information.api.dto.loan.LoanMemberQueryDTO" resultType="com.ecommerce.information.dao.vo.LoanMember">
        SELECT * FROM `ff_loan_member` WHERE `del_flg` = 0
        <if test="memberId != null and memberId != ''"> AND `member_id` = #{memberId}</if>
        <if test="memberName != null and memberName != ''"> AND `member_name` LIKE CONCAT('%', #{memberName}, '%')</if>
        <if test="regAddress != null and regAddress != ''"> AND `reg_address` LIKE CONCAT('%', #{regAddress}, '%')</if>
        <if test="financeId != null and financeId != ''"> AND FIND_IN_SET(#{financeId}, `finance_ids`)</if>
    </select>
</mapper>
