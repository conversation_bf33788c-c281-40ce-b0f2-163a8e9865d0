<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.information.dao.mapper.MemberIntegralChangeInfoMapper">
    <resultMap id="BaseResultMap" type="com.ecommerce.information.dao.vo.MemberIntegralChangeInfo">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="member_id" jdbcType="VARCHAR" property="memberId" />
        <result column="member_code" jdbcType="VARCHAR" property="memberCode" />
        <result column="member_name" jdbcType="VARCHAR" property="memberName" />
        <result column="member_type" jdbcType="VARCHAR" property="memberType" />
        <result column="integral_type" jdbcType="VARCHAR" property="integralType" />
        <result column="integral" jdbcType="INTEGER" property="integral" />
        <result column="order_number" jdbcType="VARCHAR" property="orderNumber" />
        <result column="del_flg" jdbcType="BIT" property="delFlg" />
        <result column="create_date" jdbcType="DATE" property="createDate" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="create_user" jdbcType="VARCHAR" property="createUser" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
        <result column="remark" jdbcType="LONGVARCHAR" property="remark" />
    </resultMap>

</mapper>