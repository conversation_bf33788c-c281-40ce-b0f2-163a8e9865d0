<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.information.dao.mapper.MemberIntegralLevelConfigMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.information.dao.vo.MemberIntegralLevelConfig">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="integral_level_config_id" jdbcType="VARCHAR" property="integralLevelConfigId" />
    <result column="customer_type" jdbcType="VARCHAR" property="customerType" />
    <result column="level_normal" jdbcType="INTEGER" property="levelNormal" />
    <result column="level_bronze" jdbcType="INTEGER" property="levelBronze" />
    <result column="level_sliver" jdbcType="INTEGER" property="levelSliver" />
    <result column="level_gold" jdbcType="INTEGER" property="levelGold" />
    <result column="level_diamond" jdbcType="INTEGER" property="levelDiamond" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>
</mapper>