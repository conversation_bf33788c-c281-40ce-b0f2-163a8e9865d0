<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.information.dao.mapper.MemberIntegralTradeConfigMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.information.dao.vo.MemberIntegralTradeConfig">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="integral_trade_config_id" jdbcType="VARCHAR" property="integralTradeConfigId" />
    <result column="customer_type" jdbcType="VARCHAR" property="customerType" />
    <result column="goods_category_code" jdbcType="VARCHAR" property="goodsCategoryCode" />
    <result column="goods_category_name" jdbcType="VARCHAR" property="goodsCategoryName" />
    <result column="trade_num" jdbcType="INTEGER" property="tradeNum" />
    <result column="trade_frequency_limit" jdbcType="TINYINT" property="tradeFrequencyLimit" />
    <result column="trade_month_score_max" jdbcType="INTEGER" property="tradeMonthScoreMax" />
    <result column="trade_scorce" jdbcType="INTEGER" property="tradeScorce" />
    <result column="status" jdbcType="BIT" property="status" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>
</mapper>