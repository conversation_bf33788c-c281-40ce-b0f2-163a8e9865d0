<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.information.dao.mapper.NewsAssemblyBuildingCheckMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.information.dao.vo.NewsAssemblyBuildingCheck">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="check_id" jdbcType="VARCHAR" property="checkId" />
    <result column="news_id" jdbcType="VARCHAR" property="newsId" />
    <result column="check_status" jdbcType="INTEGER" property="checkStatus" />
    <result column="reply_content" jdbcType="VARCHAR" property="replyContent" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
  </resultMap>
</mapper>