<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.information.dao.mapper.NewsMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.information.dao.vo.News">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="news_id" jdbcType="VARCHAR" property="newsId" />
    <result column="category_id" jdbcType="VARCHAR" property="categoryId" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="banner" jdbcType="VARCHAR" property="banner" />
    <result column="picture_url" jdbcType="VARCHAR" property="pictureUrl" />
    <result column="keywords" jdbcType="VARCHAR" property="keywords" />
    <result column="show_in_top" jdbcType="BIT" property="showInTop" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="content_url" jdbcType="VARCHAR" property="contentUrl" />
    <result column="category_name" jdbcType="VARCHAR" property="categoryName" />
    <result column="show_in_index" jdbcType="BIT" property="showInIndex" />
    <result column="picturl_news" jdbcType="BIT" property="picturlNews" />
    <result column="allow_comments" jdbcType="BIT" property="allowComments" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="hits" jdbcType="BIGINT" property="hits" />
    <result column="praise_num" jdbcType="BIGINT" property="praiseNum" />
    <result column="bad_review_num" jdbcType="BIGINT" property="badReviewNum" />
    <result column="check_status" jdbcType="INTEGER" property="checkStatus" />
    <result column="DEL_FLG2" jdbcType="BIT" property="delFlg2" />
    <result column="CREATE_USER" jdbcType="VARCHAR" property="createUser" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_USER" jdbcType="VARCHAR" property="updateUser" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>