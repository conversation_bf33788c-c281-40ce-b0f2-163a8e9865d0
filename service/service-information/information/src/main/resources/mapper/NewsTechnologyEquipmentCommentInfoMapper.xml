<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.information.dao.mapper.NewsTechnologyEquipmentCommentInfoMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.information.dao.vo.NewsTechnologyEquipmentCommentInfo">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="comment_id" jdbcType="VARCHAR" property="commentId" />
    <result column="reply_comment_id" jdbcType="VARCHAR" property="replyCommentId" />
    <result column="news_id" jdbcType="VARCHAR" property="newsId" />
    <result column="comment_content" jdbcType="VARCHAR" property="commentContent" />
    <result column="comment_content_url" jdbcType="VARCHAR" property="commentContentUrl" />
    <result column="DEL_FLG" jdbcType="BIT" property="delFlg" />
    <result column="CREATE_USER" jdbcType="VARCHAR" property="createUser" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_USER" jdbcType="VARCHAR" property="updateUser" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>