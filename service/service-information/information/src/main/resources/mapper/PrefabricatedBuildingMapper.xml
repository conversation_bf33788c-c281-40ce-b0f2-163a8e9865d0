<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.information.dao.mapper.PrefabricatedBuildingMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.information.dao.vo.PrefabricatedBuilding">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="prefabricated_building_id" jdbcType="VARCHAR" property="prefabricatedBuildingId" />
    <result column="category_id" jdbcType="VARCHAR" property="categoryId" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="picture_url" jdbcType="VARCHAR" property="pictureUrl" />
    <result column="keywords" jdbcType="VARCHAR" property="keywords" />
    <result column="show_in_top" jdbcType="BIT" property="showInTop" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="content_url" jdbcType="VARCHAR" property="contentUrl" />
    <result column="category_name" jdbcType="VARCHAR" property="categoryName" />
    <result column="show_in_index" jdbcType="BIT" property="showInIndex" />
    <result column="picturl_flg" jdbcType="BIT" property="picturlFlg" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="check_status" jdbcType="INTEGER" property="checkStatus" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>