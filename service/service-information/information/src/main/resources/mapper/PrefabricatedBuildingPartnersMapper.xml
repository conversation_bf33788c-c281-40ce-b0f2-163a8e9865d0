<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.information.dao.mapper.PrefabricatedBuildingPartnersMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.information.dao.vo.PrefabricatedBuildingPartners">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="prefabricated_building_partners_id" jdbcType="VARCHAR" property="prefabricatedBuildingPartnersId" />
    <result column="category_id" jdbcType="VARCHAR" property="categoryId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="picture_url" jdbcType="VARCHAR" property="pictureUrl" />
    <result column="keywords" jdbcType="VARCHAR" property="keywords" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>