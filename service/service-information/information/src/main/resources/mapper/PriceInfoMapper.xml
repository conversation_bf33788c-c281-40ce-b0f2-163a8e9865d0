<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.information.dao.mapper.PriceInfoMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.information.dao.vo.PriceInfo">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="category_id" jdbcType="VARCHAR" property="categoryId" />
    <result column="category_name" jdbcType="VARCHAR" property="categoryName" />
    <result column="price_time" jdbcType="TIMESTAMP" property="priceTime" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="brand" jdbcType="VARCHAR" property="brand" />
    <result column="price_unit" jdbcType="VARCHAR" property="priceUnit" />
    <result column="currency" jdbcType="VARCHAR" property="currency" />
    <result column="price" jdbcType="VARCHAR" property="price" />
    <result column="lowest_price" jdbcType="DECIMAL" property="lowestPrice" />
    <result column="highest_price" jdbcType="DECIMAL" property="highestPrice" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>