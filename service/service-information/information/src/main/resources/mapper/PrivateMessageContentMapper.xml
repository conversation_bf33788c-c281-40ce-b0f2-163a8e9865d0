<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.information.dao.mapper.PrivateMessageContentMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.information.dao.vo.PrivateMessageContent">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="msg_id" jdbcType="VARCHAR" property="msgId" />
    <result column="msg_type" jdbcType="INTEGER" property="msgType" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="attachment_url" jdbcType="VARCHAR" property="attachmentUrl" />
    <result column="msg_content_url" jdbcType="VARCHAR" property="msgContentUrl" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="msg_content" jdbcType="LONGVARCHAR" property="msgContent" />
  </resultMap>
  <resultMap id="MessageResultMap" type="com.ecommerce.information.api.dto.internalmessage.InternalMessageDTO">
    <result column="msg_id" jdbcType="VARCHAR" property="msgId" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="send_user_id" jdbcType="VARCHAR" property="senderId" />
    <result column="receive_user_id" jdbcType="VARCHAR" property="receiveUserId" />
    <result column="receive_member_id" jdbcType="VARCHAR" property="receiveMemberId" />
    <result column="status" jdbcType="INTEGER" property="readStatus" />
    <result column="update_time" jdbcType="TIMESTAMP" property="time" />
  </resultMap>
  <select id="findDeleteMessage" parameterType="com.ecommerce.information.api.dto.internalmessage.InternalMessageQueryDTO" resultMap="MessageResultMap">
     (select a.send_user_id,a.update_time,a.status,a.msg_id,b.title,a.receive_user_id,a.receive_member_id
     from ii_private_message_inbox as a
     left join ii_private_message_content as b
     on a.msg_id=b.msg_id
     where a.msg_type=3 and a.del_flg=0 and (a.receive_member_id = #{memberId} or a.receive_user_id = #{operator}))
    UNION
     (select c.create_user,c.create_time,c.status,d.msg_id,d.title,c.receive_user_ids,receive_member_ids
     from ii_private_message_outbox as c
     left join ii_private_message_content as d
     on d.msg_id=c.msg_id
     where c.msg_type=3 and c.del_flg=0 and c.create_user = #{operator}
     ORDER by a.update_time desc)
  </select>
</mapper>