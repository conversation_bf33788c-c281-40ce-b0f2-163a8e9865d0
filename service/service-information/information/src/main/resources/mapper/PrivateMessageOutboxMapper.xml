<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.information.dao.mapper.PrivateMessageOutboxMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.information.dao.vo.PrivateMessageOutbox">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="msg_type" jdbcType="TINYINT" property="msgType" />
    <result column="msg_id" jdbcType="VARCHAR" property="msgId" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="receive_user_ids" jdbcType="LONGVARCHAR" property="receiveUserIds" />
    <result column="receive_member_ids" jdbcType="LONGVARCHAR" property="receiveMemberIds" />
  </resultMap>
  <resultMap id="outboxMessageResultMap" type="com.ecommerce.information.api.dto.internalmessage.InternalMessageDTO">
    <result column="msg_id" jdbcType="VARCHAR" property="msgId" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="create_user" jdbcType="VARCHAR" property="senderId" />
    <result column="receive_member_ids" jdbcType="VARCHAR" property="receiveMemberId" />
    <result column="receive_user_ids" jdbcType="VARCHAR" property="receiveUserId" />
    <result column="send_user_id" jdbcType="VARCHAR" property="senderId" />
    <result column="status" jdbcType="INTEGER" property="readStatus" />
    <result column="update_time" jdbcType="TIMESTAMP" property="time" />
  </resultMap>
  <select id="findOutboxMessage" parameterType="com.ecommerce.information.api.dto.internalmessage.InternalMessageQueryDTO" resultMap="outboxMessageResultMap">
    select * from ii_private_message_outbox as a
    left join ii_private_message_content as b
    on a.msg_id=b.msg_id
    where a.del_flg=0 and a.create_user =#{operator}
    and a.msg_type=#{typeStatus}
    <if test="title != null and title!=''">
      and title like concat('%',#{title},'%')
    </if>
    ORDER by a.update_time desc
  </select>
</mapper>