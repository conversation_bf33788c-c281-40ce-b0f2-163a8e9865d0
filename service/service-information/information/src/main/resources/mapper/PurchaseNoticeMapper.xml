<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.information.dao.mapper.PurchaseNoticeMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.information.dao.vo.PurchaseNotice">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="purchase_notice_id" jdbcType="VARCHAR" property="purchaseNoticeId" />
    <result column="tender_num" jdbcType="VARCHAR" property="tenderNum" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="project_name" jdbcType="VARCHAR" property="projectName" />
    <result column="keyword" jdbcType="VARCHAR" property="keyword" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="first_category_code" jdbcType="VARCHAR" property="firstCategoryCode" />
    <result column="first_category" jdbcType="VARCHAR" property="firstCategory" />
    <result column="second_category_code" jdbcType="VARCHAR" property="secondCategoryCode" />
    <result column="second_category" jdbcType="VARCHAR" property="secondCategory" />
    <result column="purchase_notice_status" jdbcType="VARCHAR" property="purchaseNoticeStatus" />
    <result column="purchase_notice_type" jdbcType="VARCHAR" property="purchaseNoticeType" />
    <result column="notice_source_type" jdbcType="VARCHAR" property="noticeSourceType" />
    <result column="start_date" jdbcType="DATE" property="startDate" />
    <result column="end_date" jdbcType="DATE" property="endDate" />
    <result column="agency_name" jdbcType="VARCHAR" property="agencyName" />
    <result column="member_id" jdbcType="VARCHAR" property="memberId" />
    <result column="member_name" jdbcType="VARCHAR" property="memberName" />
    <result column="content_url" jdbcType="VARCHAR" property="contentUrl" />
    <result column="attachment_bid" jdbcType="VARCHAR" property="attachmentBid" />
    <result column="ext_attachment_name" jdbcType="VARCHAR" property="extAttachmentName" />
    <result column="audit_fail_reason" jdbcType="VARCHAR" property="auditFailReason" />
    <result column="end_reason" jdbcType="VARCHAR" property="endReason" />
    <result column="notice_source_url" jdbcType="VARCHAR" property="noticeSourceUrl" />
    <result column="editor_id" jdbcType="VARCHAR" property="editorId" />
    <result column="editor_name" jdbcType="VARCHAR" property="editorName" />
    <result column="auditor_id" jdbcType="VARCHAR" property="auditorId" />
    <result column="auditor_name" jdbcType="VARCHAR" property="auditorName" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>


  <select id="queryNoticeListByCond" parameterType="com.ecommerce.information.api.dto.purchase.PurchaseNoticeCondDTO" resultType="com.ecommerce.information.api.dto.purchase.PurchaseNoticeListDTO">
    SELECT
      purchase_notice_id AS purchaseNoticeId,
      tender_num AS tenderNum,
      project_name AS projectName,
      title,
      keyword,
      province_code AS provinceCode,
      province,
      city_code AS cityCode,
      city,
      first_category_code AS firstCategoryCode,
      first_category AS firstCategory,
      second_category_code AS secondCategoryCode,
      second_category AS secondCategory,
      purchase_notice_status AS purchaseNoticeStatus,
      purchase_notice_type AS purchaseNoticeType,
      notice_source_type AS noticeSourceType,
      DATE_FORMAT(start_date,'%Y-%m-%d') AS startDateStr,
      DATE_FORMAT(end_date,'%Y-%m-%d') AS endDateStr,
      agency_name AS agencyName,
      member_id AS memberId,
      member_name AS memberName,
      content_url AS contentUrl,
      attachment_bid AS attachmentBid,
      join_url AS joinUrl,
      audit_fail_reason AS auditFailReason,
      end_reason AS endReason,
      notice_source_url AS noticeSourceUrl,
      editor_id AS editorId,
      editor_name AS editorName,
      auditor_id AS auditorId,
      auditor_name AS auditorName,
      mobile
    FROM ii_purchase_notice
    WHERE del_flg = 0
    <if test="titleLike != null and titleLike != ''">
      and title like concat('%', #{titleLike}, '%')
    </if>
    <if test="memberId != null and memberId !=''">
      AND member_id = #{memberId}
    </if>
    <if test="editorId != null and editorId != ''">
      AND editor_id = #{editorId}
    </if>
    <if test="purchaseNoticeStatus != null and purchaseNoticeStatus != ''">
      AND purchase_notice_status = #{purchaseNoticeStatus}
    </if>
    <if test="purchaseNoticeStatusList != null">
      AND purchase_notice_status IN
      <foreach collection="purchaseNoticeStatusList" item="pns" index="pnsi" open="(" close=")" separator=",">
        #{pns}
      </foreach>
    </if>
    <if test="purchaseNoticeType != null and purchaseNoticeType != ''">
      AND purchase_notice_type = #{purchaseNoticeType}
    </if>
    <if test="startDateStr != null and startDateStr != ''">
      AND start_date &gt;= #{startDateStr}
    </if>
    <if test="endDateStr != null and endDateStr != ''">
      AND end_date &lt;= #{endDateStr}
    </if>
    <if test="provinceCode != null and provinceCode != ''">
      AND province_code = #{provinceCode}
    </if>
    <if test="cityCode != null and cityCode != ''">
      AND city_code = #{cityCode}
    </if>
    <if test="firstCategoryCode != null and firstCategoryCode != ''">
      AND first_category_code = #{firstCategoryCode}
    </if>
    <if test="secondCategoryCode != null and secondCategoryCode != ''">
      AND second_category_code = #{secondCategoryCode}
    </if>
    ORDER BY create_time DESC
  </select>

  <update id="updateStatus">
    UPDATE ii_purchase_notice
    SET purchase_notice_status = #{newStatus}, update_user = #{updateUser}
    WHERE
    purchase_notice_status IN
    <foreach collection="oldStatusList" item="opns" index="opnsi" open="(" close=")" separator=",">
      #{opns}
    </foreach>
    AND purchase_notice_id IN
    <foreach collection="purchaseNoticeIdList" item="pni" index="pnii" open="(" close=")" separator=",">
      #{pni}
    </foreach>
    AND del_flg = 0
  </update>

</mapper>