<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.information.dao.mapper.ReportContactMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.information.dao.vo.ReportContact">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="report_contact_id" jdbcType="VARCHAR" property="reportContactId" />
    <result column="report_template_id" jdbcType="VARCHAR" property="reportTemplateId" />
    <result column="report_contact_info" jdbcType="VARCHAR" property="reportContactInfo" />
    <result column="report_contact_desc" jdbcType="VARCHAR" property="reportContactDesc" />
    <result column="cc_flag" jdbcType="INTEGER" property="ccFlag" />
    <result column="del_flg" jdbcType="INTEGER" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <select id="selectByTemplateId" resultMap="BaseResultMap">
    select *
    from ii_report_contact
    where report_template_id = #{reportTemplateId} and del_flg = 0
    order by create_time
  </select>

</mapper>