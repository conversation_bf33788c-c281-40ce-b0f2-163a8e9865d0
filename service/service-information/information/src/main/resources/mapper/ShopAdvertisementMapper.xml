<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.information.dao.mapper.ShopAdvertisementMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.information.dao.vo.ShopAdvertisement">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="advertisement_id" jdbcType="VARCHAR" property="advertisementId" />
    <result column="shop_id" jdbcType="VARCHAR" property="shopId" />
    <result column="shop_name" jdbcType="VARCHAR" property="shopName" />
    <result column="ad_name" jdbcType="VARCHAR" property="adName" />
    <result column="order_number" jdbcType="INTEGER" property="orderNumber" />
    <result column="advertisement_type" jdbcType="VARCHAR" property="advertisementType" />
    <result column="stop_status" jdbcType="BIT" property="stopStatus" />
    <result column="effective_start_time" jdbcType="TIMESTAMP" property="effectiveStartTime" />
    <result column="effective_end_time" jdbcType="TIMESTAMP" property="effectiveEndTime" />
    <result column="ad_image_url" jdbcType="VARCHAR" property="adImageUrl" />
    <result column="ad_resource_url" jdbcType="VARCHAR" property="adResourceUrl" />
    <result column="hint" jdbcType="VARCHAR" property="hint" />
    <result column="ad_business_id" jdbcType="VARCHAR" property="adBusinessId" />
    <result column="ad_business_title" jdbcType="VARCHAR" property="adBusinessTitle" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>