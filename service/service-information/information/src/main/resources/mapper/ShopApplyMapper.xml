<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.information.dao.mapper.ShopApplyMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.information.dao.vo.ShopApply">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="apply_id" jdbcType="VARCHAR" property="applyId" />
    <result column="member_id" jdbcType="VARCHAR" property="memberId" />
    <result column="member_name" jdbcType="VARCHAR" property="memberName" />
    <result column="shop_url_prefix" jdbcType="VARCHAR" property="shopUrlPrefix" />
    <result column="shop_type" jdbcType="VARCHAR" property="shopType" />
    <result column="reply_content" jdbcType="VARCHAR" property="replyContent" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="apply_status" jdbcType="INTEGER" property="applyStatus" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
  </resultMap>
</mapper>