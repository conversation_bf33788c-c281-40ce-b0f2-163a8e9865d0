<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.information.dao.mapper.ShopCarouselMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.information.dao.vo.ShopCarousel">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="carousel_id" jdbcType="VARCHAR" property="carouselId" />
    <result column="shop_id" jdbcType="VARCHAR" property="shopId" />
    <result column="carousel_pic" jdbcType="VARCHAR" property="carouselPic" />
    <result column="carousel_name" jdbcType="VARCHAR" property="carouselName" />
    <result column="carousel_time" jdbcType="INTEGER" property="carouselTime" />
    <result column="carousel_sort" jdbcType="INTEGER" property="carouselSort" />
    <result column="carousel_memo" jdbcType="VARCHAR" property="carouselMemo" />
    <result column="carousel_url" jdbcType="VARCHAR" property="carouselUrl" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_time" jdbcType="VARCHAR" property="createTime" />
    <result column="create_user" jdbcType="TIMESTAMP" property="createUser" />
    <result column="update_time" jdbcType="VARCHAR" property="updateTime" />
    <result column="update_user" jdbcType="TIMESTAMP" property="updateUser" />
  </resultMap>
</mapper>