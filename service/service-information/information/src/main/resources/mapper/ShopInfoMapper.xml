<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.information.dao.mapper.ShopInfoMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.information.dao.vo.ShopInfo">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="shop_id" jdbcType="VARCHAR" property="shopId" />
    <result column="member_id" jdbcType="VARCHAR" property="memberId" />
    <result column="member_name" jdbcType="VARCHAR" property="memberName" />
    <result column="member_list" jdbcType="VARCHAR" property="memberList" />
    <result column="shop_code" jdbcType="VARCHAR" property="shopCode" />
    <result column="shop_name" jdbcType="VARCHAR" property="shopName" />
    <result column="shop_short_name" jdbcType="VARCHAR" property="shopShortName" />
    <result column="shop_url_prefix" jdbcType="VARCHAR" property="shopUrlPrefix" />
    <result column="shop_type" jdbcType="VARCHAR" property="shopType" />
    <result column="shop_level" jdbcType="INTEGER" property="shopLevel" />
    <result column="shop_logo" jdbcType="VARCHAR" property="shopLogo" />
    <result column="shop_mini_logo" jdbcType="VARCHAR" property="shopMiniLogo" />
    <result column="shop_app_logo" jdbcType="VARCHAR" property="shopAppLogo" />
    <result column="shop_memo" jdbcType="VARCHAR" property="shopMemo" />
    <result column="shop_status" jdbcType="VARCHAR" property="shopStatus" />
    <result column="is_seller" jdbcType="BIT" property="isSeller" />
    <result column="is_carrier" jdbcType="BIT" property="isCarrier" />
    <result column="is_supplier" jdbcType="BIT" property="isSupplier" />
    <result column="country_code" jdbcType="VARCHAR" property="countryCode" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="area_code" jdbcType="VARCHAR" property="areaCode" />
    <result column="street_code" jdbcType="VARCHAR" property="streetCode" />
    <result column="country_name" jdbcType="VARCHAR" property="countryName" />
    <result column="province_name" jdbcType="VARCHAR" property="provinceName" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
    <result column="area_name" jdbcType="VARCHAR" property="areaName" />
    <result column="street_name" jdbcType="VARCHAR" property="streetName" />
    <result column="address_detail" jdbcType="VARCHAR" property="addressDetail" />
    <result column="rich_text1" jdbcType="VARCHAR" property="richText1" />
    <result column="rich_text2" jdbcType="VARCHAR" property="richText2" />
    <result column="rich_text3" jdbcType="VARCHAR" property="richText3" />
    <result column="rich_text4" jdbcType="VARCHAR" property="richText4" />
    <result column="rich_text5" jdbcType="VARCHAR" property="richText5" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>