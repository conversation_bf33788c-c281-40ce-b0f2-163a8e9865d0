<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.information.dao.mapper.ShopNewsMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.information.dao.vo.ShopNews">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="news_id" jdbcType="VARCHAR" property="newsId" />
    <result column="shop_id" jdbcType="VARCHAR" property="shopId" />
    <result column="news_title" jdbcType="VARCHAR" property="newsTitle" />
    <result column="news_content" jdbcType="VARCHAR" property="newsContent" />
    <result column="news_pics" jdbcType="VARCHAR" property="newsPics" />
    <result column="news_memo" jdbcType="VARCHAR" property="newsMemo" />
    <result column="news_sort" jdbcType="INTEGER" property="newsSort" />
    <result column="is_top" jdbcType="BIT" property="isTop" />
    <result column="hits" jdbcType="BIGINT" property="hits" />
    <result column="supports" jdbcType="BIGINT" property="supports" />
    <result column="despises" jdbcType="BIGINT" property="despises" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>