<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.information.dao.mapper.ShopNoticeMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.information.dao.vo.ShopNotice">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="notice_id" jdbcType="VARCHAR" property="noticeId" />
    <result column="shop_id" jdbcType="VARCHAR" property="shopId" />
    <result column="notice_title" jdbcType="VARCHAR" property="noticeTitle" />
    <result column="notice_content" jdbcType="VARCHAR" property="noticeContent" />
    <result column="notice_pics" jdbcType="VARCHAR" property="noticePics" />
    <result column="notice_memo" jdbcType="VARCHAR" property="noticeMemo" />
    <result column="notice_sort" jdbcType="INTEGER" property="noticeSort" />
    <result column="is_top" jdbcType="BIT" property="isTop" />
    <result column="hits" jdbcType="BIGINT" property="hits" />
    <result column="supports" jdbcType="BIGINT" property="supports" />
    <result column="despises" jdbcType="BIGINT" property="despises" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>