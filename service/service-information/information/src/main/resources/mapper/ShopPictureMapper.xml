<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.information.dao.mapper.ShopPictureMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.information.dao.vo.ShopPicture">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="picture_id" jdbcType="VARCHAR" property="pictureId" />
    <result column="shop_id" jdbcType="VARCHAR" property="shopId" />
    <result column="picture_name" jdbcType="VARCHAR" property="pictureName" />
    <result column="picture_pic" jdbcType="VARCHAR" property="picturePic" />
    <result column="picture_sort" jdbcType="INTEGER" property="pictureSort" />
    <result column="picture_type" jdbcType="VARCHAR" property="pictureType" />
    <result column="picture_memo" jdbcType="VARCHAR" property="pictureMemo" />
    <result column="picture_url" jdbcType="VARCHAR" property="pictureUrl" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>