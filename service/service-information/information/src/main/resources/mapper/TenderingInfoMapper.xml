<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.information.dao.mapper.TenderingInfoMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.information.dao.vo.TenderingInfo">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="tendering_id" jdbcType="VARCHAR" property="tenderingId" />
    <result column="member_id" jdbcType="VARCHAR" property="memberId" />
    <result column="member_name" jdbcType="VARCHAR" property="memberName" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="source" jdbcType="VARCHAR" property="source" />
    <result column="source_type" jdbcType="INTEGER" property="sourceType" />
    <result column="other_require_content" jdbcType="VARCHAR" property="otherRequireContent" />
    <result column="other_require_url" jdbcType="VARCHAR" property="otherRequireUrl" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="begin_time" jdbcType="TIMESTAMP" property="beginTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="advertising_id" jdbcType="VARCHAR" property="advertisingId" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="keywords" jdbcType="VARCHAR" property="keywords" />
  </resultMap>
</mapper>