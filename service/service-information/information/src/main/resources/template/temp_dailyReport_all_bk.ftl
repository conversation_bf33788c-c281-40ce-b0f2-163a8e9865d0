<html>
<head>
    <meta http-equiv="Content-Type" content="text/html" ; charset="utf-8">
    <style lang="css">
        .o-el-wrap {
            font-size: 12px;
        }
        .o-el-wrap .report-content {
            display: flex;
            justify-content: center;
        }

        .o-el-wrap .report-content .report-info {
            background-image: url("bg_dailyReport.png");
            background-size: 100% 100%;
            height: 430px;
            width: 580px;
            margin-top: 100px;
            margin-bottom: 100px;
            transform: scale(1.5);
            position: relative;
        }

        .o-el-wrap .report-content .report-info .info-title {
            position: absolute;
            top: 18px;
            left: 80px;
            font-size: 20px;
            color: #fff;
        }

        .o-el-wrap .report-content .report-info .info-left {
            position: absolute;
            top: 130px;
            left: 50px;
            line-height: 30px;
            text-align: center;
        }

        .o-el-wrap .report-content .report-info .info-left .header {
            font-size: 24px;
        }

        .o-el-wrap .report-content .report-info .info-left .date {
            font-size: 16px;
            text-shadow: 1px 1px 1px #666666;
        }

        .o-el-wrap .report-content .report-info .info-right {
            position: absolute;
            text-align: center;
            top: 65px;
            right: 20px;
            width: 350px;
        }

        .o-el-wrap .report-content .report-info .info-right .title {
            font-size: 18px;
            font-weight: bold;
        }

        .o-el-wrap .report-content .report-info .info-right .content {
            padding: 10px 20px;
            text-align: left;
            line-height: 18px;
        }

        .o-el-wrap .report-content .report-info .info-right .content .font-date {
            text-decoration: underline;
            font-size: 14px;
        }

        .o-el-wrap .report-content .report-info .info-right .content .font-date i {
            color: #FFA500;
        }

        .o-el-wrap .report-content .report-info .info-right .content .font-area i {
            color: #FFA500;
        }

        .o-el-wrap .report-content .report-info .info-right .content ul {
            margin: 0;
        }

        .o-el-wrap .report-content .report-info .info-right .content .num {
            display: inline-block;
            text-decoration: underline;
            font-weight: bold;
        }

        .o-el-wrap .report-content .report-info .info-right .content .total {
            color: #5B9BD5;
            font-weight: bold;
            font-style: italic;
            font-size: 16px;
        }
    </style>
</head>
<body>

<div>

    <p class=MsoNormal style='text-align:justify;text-justify:inter-ideograph;
text-indent:32.0pt'><span lang=ZH-CN style='font-size:16.0pt;color:black'>您好！</span><span
                style='font-family:"Calibri",sans-serif;color:black'></span></p>

    <p class=MsoNormal style='mso-margin-top-alt:auto;mso-margin-bottom-alt:auto;
text-indent:32.0pt'><span lang=ZH-CN style='font-size:16.0pt;color:black'>截止到</span><span
                style='font-size:16.0pt;color:black'>${dailyData.year}<span lang=ZH-CN>年</span>${dailyData.month}<span
                    lang=ZH-CN>月</span>${dailyData.day}<span lang=ZH-CN>日</span>24<span lang=ZH-CN>时，xxx-电商平台业务线上化运营数据如下：</span></span><span
                style='font-family:"Calibri",sans-serif;color:black'></span></p>

    <p class=MsoNormal style='mso-margin-top-alt:auto;mso-margin-bottom-alt:auto;
    text-indent:32.0pt'><span style='font-size:16.0pt;color:black'>1<span
                    lang=ZH-CN>）交易数据：当日平台订单量</span>${orderData.dailySendCount}<span lang=ZH-CN>笔，发货量</span>${orderData.dailySendQuantity}<span
                    lang=ZH-CN>吨；当月累计发货量</span>${dailyData.monthSendQuantity}<span lang=ZH-CN>万吨；当年累计发货量</span>${dailyData.yearSendQuantity}<span
                    lang=ZH-CN>万吨；</span></span><span style='font-family:"Calibri",sans-serif;
color:black'></span></p>

    <p class=MsoNormal style='mso-margin-top-alt:auto;mso-margin-bottom-alt:auto;
text-indent:32.0pt'><span style='font-size:16.0pt;color:black'>2<span
                    lang=ZH-CN>）物流数据：当月累计平台配送量</span>${waybillData.monthPlatformQuantity}<span lang=ZH-CN>吨；当年累计平台配送量</span>${waybillData.yearPlatformQuantity}<span
                    lang=ZH-CN>吨</span>;</span><span style='font-family:"Calibri",sans-serif;
color:black'></span></p>

    <p class=MsoNormal style='mso-margin-top-alt:auto;mso-margin-bottom-alt:auto;
text-indent:32.0pt'><span style='font-size:16.0pt;color:black'>3<span
                    lang=ZH-CN>）金融数据：当年累计授信额度</span>${yearCreditE}<span lang=ZH-CN>亿元，当年累计实际放款</span>${yearActualLoanW}<span
                    lang=ZH-CN>万元。</span></span><span style='font-family:"Calibri",sans-serif;
color:black'></span></p>

    <p class=MsoNormal style='mso-margin-top-alt:auto;mso-margin-bottom-alt:auto;
text-indent:32.0pt'><span lang=ZH-CN style='font-size:16.0pt;color:black'>具体数据详见下图。请各位领导查阅知悉！谢谢</span><span
                style='font-size:16.0pt;color:black'>!</span><span style='font-family:"Calibri",sans-serif;
color:black'></span></p>

</div>
<img src="bg_dailyReport.png" />
<div class="o-el-wrap">
    <div class="report-content">
        <div class="report-info">
            <div class="info-left">
                <div class="header">温馨日报</div>
                <div class="date">${dailyData.month}月${dailyData.day}日（${dailyData.week}）</div>
            </div>
            <div class="info-right">
                <div class="title">xxx-电商平台业务线上化运营数据</div>
                <div class="content">
                    <div class="font-date">
                        <i class="el-icon-position"></i>
                        <span>${dailyData.month}月${dailyData.day}日小计：</span>
                    </div>
                    <div class="font-area">
                        <i class="el-icon-s-flag"></i>
                        <span>广东大区：订单数 <div class="num total">${dailyData.gdOrderCount}</div> 笔，发货量 <div class="num total">${dailyData.gdSendQuantity}</div> 吨</span>
                    </div>
                    <ul>
                        <li>袋装：订单数 <div class="num">${dailyData.gdBagOrderCount}</div> 笔，发货量 <div class="num">${dailyData.gdBagSendQuantity}</div> 吨</li>
                        <li>散装：订单数 <div class="num">${dailyData.gdBulkOrderCount}</div> 笔，发货量 <div class="num">${dailyData.gdBulkSendQuantity}</div> 吨</li>
                        <li>盈信：订单数 <div class="num">${dailyData.gdYXOrderCount}</div> 笔，发货量 <div class="num">${dailyData.gdYXSendQuantity}</div> 吨</li>
                    </ul>
                    <div class="font-area">
                        <i class="el-icon-s-flag"></i>
                        <span>广西大区：订单数 <div class="num total">${dailyData.gxOrderCount}</div> 笔，发货量 <div class="num total">${dailyData.gxSendQuantity}</div> 吨</span>
                    </div>
                    <ul>
                        <li>袋装：订单数 <div class="num">${dailyData.gxBagOrderCount}</div> 笔，发货量 <div class="num">${dailyData.gxBagSendQuantity}</div> 吨</li>
                        <li>散装：订单数 <div class="num">${dailyData.gxBulkOrderCount}</div> 笔，发货量 <div class="num">${dailyData.gxBulkSendQuantity}</div> 吨</li>
                        <li>恒亮：订单数 <div class="num">${dailyData.gxHLOrderCount}</div> 笔，发货量 <div class="num">${dailyData.gxHLSendQuantity}</div> 吨</li>
                    </ul>
                    <div class="font-date margin-top-10">
                        <i class="el-icon-position"></i>
                        <span>截至当日24点累计：</span>
                    </div>
                    <div>
                        <span>交易: 当月累计发货量 <div class="num total">${dailyData.monthSendQuantity}</div> 万吨，年累计发货量 <div class="num total">${dailyData.yearSendQuantity}</div> 万吨</span>
                    </div>
                    <ul>
                        <li>袋装：月发货量 <div class="num">${dailyData.monthBagSendQuantity}</div> 万吨，年发货量 <div class="num">${dailyData.yearBagSendQuantity}</div> 万吨</li>
                        <li>散装：月发货量 <div class="num">${dailyData.monthBulkSendQuantity}</div> 万吨，年发货量 <div class="num">${dailyData.yearBulkSendQuantity}</div> 万吨</li>
                    </ul>
                    <div class="font-area">
                        <i class="el-icon-s-flag"></i>
                        <span>物流：当月累计平台配送量 <div class="num total">${waybillData.monthPlatformQuantity}</div> 吨，年累计平台配送量 <div class="num total">${waybillData.yearPlatformQuantity}</div> 吨</span>
                    </div>
                    <div class="font-area">
                        <i class="el-icon-s-flag"></i>
                        <span>金融：年累计授信额度 <div class="num total">${yearCreditE}</div> 亿元，年累计实际放款 <div class="num total">${yearActualLoanW}</div> 万元</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div>

    <div>

        <div>

            <div>

                <p style='background:white'><span lang=ZH-CN style='font-family:仿宋;color:black'>地址：深圳市罗湖区地王大厦</span><span
                            style='font-family:仿宋;color:black'>1609<span lang=ZH-CN>室</span></span><span
                            style='font-family:"Calibri",sans-serif;color:black'></span></p>

                <p style='background:white'><span lang=ZH-CN style='font-family:仿宋;color:black'>邮箱：</span><span
                            style='font-family:"Calibri",sans-serif;color:black'><a
                                href="mailto:<EMAIL>" target="_blank" id=LPNoLP><span
                                    style='font-family:仿宋'><EMAIL></span></a></span></p>

                <p style='background:white'><span lang=ZH-CN style='font-family:仿宋;color:black'>（本邮件由罗子芊发出，联系电话：</span><span
                            style='font-family:仿宋;color:black'>166</span><span style='font-family:"Calibri",sans-serif;
color:black'>&nbsp;</span><span style='font-family:仿宋;color:black'>7521</span><span
                            style='font-family:"Calibri",sans-serif;color:black'>&nbsp;</span><span
                            style='font-family:仿宋;color:black'>2210<span lang=ZH-CN>）</span></span><span
                            style='font-family:"Calibri",sans-serif;color:black'></span></p>

            </div>

        </div>

    </div>

    <p style='background:white'><span style='font-family:"Calibri",sans-serif;
color:black'>&nbsp;</span></p>

    <p style='background:white'><b><span lang=ZH-CN style='font-family:仿宋;
color:black'>本邮件（包括任何附件）含有专供特定的个人和目的而使用的机密信息。如果您不是此邮件的收件人或被授权收到此邮件，您不应使用，复制，披露或基于此邮件的信息采取任何行动。如果您不是此邮件的收件人，请以电邮通知发送方并删除此邮件，谢谢。</span></b><span
                style='font-family:"Calibri",sans-serif;color:black'></span></p>

</div>
</body>

</html>

