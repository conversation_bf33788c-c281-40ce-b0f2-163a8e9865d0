<html>
<head>
    <meta http-equiv="Content-Type" content="text/html" ; charset="utf-8">
    <style lang="css">
        .o-el-wrap {
            font-size: 12px;
        }
        .report-content {
            display: flex;
            justify-content: center;
        }

        .report-content .report-info {
            background-image: url("cid:bgDailyReport");
            background-size: 100% 100%;
            height: 430px;
            width: 580px;
            position: relative;
        }

        .report-content .report-info .info-left {
            position: absolute;
            top: 130px;
            left: 50px;
            line-height: 30px;
            text-align: center;
        }

        .report-content .report-info .info-left .header {
            font-size: 24px;
        }

        .report-content .report-info .info-left .date {
            font-size: 16px;
            text-shadow: 1px 1px 1px #666666;
        }

        .report-content .report-info .info-right {
            position: absolute;
            text-align: center;
            top: 80px;
            right: 20px;
            width: 350px;
        }

        .report-content .report-info .info-right .title {
            font-size: 18px;
            font-weight: bold;
        }

        .report-content .report-info .info-right .content {
            padding: 10px 20px;
            text-align: left;
            line-height: 20px;
        }

        .report-content .report-info .info-right .content .font-date {
            text-decoration: underline;
            font-size: 14px;
        }

        .report-content .report-info .info-right .content .font-date i {
            color: #FFA500;
        }

        .report-content .report-info .info-right .content .font-area i {
            color: #FFA500;
        }

        .report-content .report-info .info-right .content ul {
            margin: 0;
        }

        .report-content .report-info .info-right .content .num {
            display: inline-block;
            text-decoration: underline;
            font-weight: bold;
        }

        .report-content .report-info .info-right .content .total {
            color: #5B9BD5;
            font-weight: bold;
            font-style: italic;
            font-size: 16px;
        }
    </style>
</head>
<body>
<div class="o-el-wrap">
    <div class="report-content">
        <div class="report-info">
            <div class="info-left">
                <div class="header">温馨日报</div>
                <div class="date">${dailyData.month}月${dailyData.day}日（${dailyData.week}）</div>
            </div>
            <div class="info-right">
                <div class="title">xxx-电商平台业务线上化运营数据</div>
                <div class="content">
                    <div class="font-date">
                        <i class="el-icon-position"></i>
                        <span>${dailyData.month}月${dailyData.day}日小计：</span>
                    </div>
                    <div class="font-area">
                        <i class="el-icon-s-flag"></i>
                        <span>广西大区：订单数 <div class="num total">${dailyData.gxOrderCount}</div> 笔，发货量 <div class="num total">${dailyData.gxSendQuantity}</div> 吨</span>
                    </div>
                    <ul>
                        <li>袋装：订单数 <div class="num">${dailyData.gxBagOrderCount}</div> 笔，发货量 <div class="num">${dailyData.gxBagSendQuantity}</div> 吨</li>
                        <li>散装：订单数 <div class="num">${dailyData.gxBulkOrderCount}</div> 笔，发货量 <div class="num">${dailyData.gxBulkSendQuantity}</div> 吨</li>
                        <li>恒亮：订单数 <div class="num">${dailyData.gxHLOrderCount}</div> 笔，发货量 <div class="num">${dailyData.gxHLSendQuantity}</div> 吨</li>
                    </ul>
                    <div class="font-date margin-top-10">
                        <i class="el-icon-position"></i>
                        <span>截至当日24点累计：</span>
                    </div>
                    <div>
                        <span>${dailyData.month}月累计发货量 <div class="num total">${dailyData.monthSendQuantity}</div> 吨，年累计发货量 <div class="num total">${dailyData.yearSendQuantity}</div> 吨</span>
                    </div>
                    <ul>
                        <li>袋装：月发货量 <div class="num">${dailyData.monthBagSendQuantity}</div> 吨，年发货量 <div class="num">${dailyData.yearBagSendQuantity}</div> 吨</li>
                        <li>散装：月发货量 <div class="num">${dailyData.monthBulkSendQuantity}</div> 吨，年发货量 <div class="num">${dailyData.yearBulkSendQuantity}</div> 吨</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
</body>

</html>

