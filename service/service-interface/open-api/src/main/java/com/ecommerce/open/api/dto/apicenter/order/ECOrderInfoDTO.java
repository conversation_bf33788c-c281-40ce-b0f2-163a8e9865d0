package com.ecommerce.open.api.dto.apicenter.order;

import com.ecommerce.open.api.dto.apicenter.BaseClientRequestDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class ECOrderInfoDTO extends BaseClientRequestDTO {

	@Schema(description = "电商合同号")
	private String ecContractCode;

	@Schema(description = "erp合同号")
	private String erpContractCode;

	@Schema(description = "erp买家编号")
	private String mdmCode;

	@Schema(description = "是否ERP调度，根据业务情况决定，目前卖家配送就是ERP调度", required = true)
	private boolean ifERPDispatch;

	@Schema(description = "订单可以开始提货时间", required = false)
	private Date canTakeStartTime;

	@Schema(description = "订单最终可以提货时间", required = false)
	private Date canTakeEndTime;

	@Schema(description = "erp提货点编码")
	private String ERPPickupPointCode;

	@Schema(description = "提货点组织机构编码")
	private String pickupOrgCode;

	@Schema(description = "订单最终可以提货数量", required = true)
	private BigDecimal canTakeOverAmount;

	@Schema(description = "erp销售区域编码")
	private String erpOrgCode;

	private String saleBigAreaCode;//erp销售区域大区Code

	@Schema(description = "erp订单号")
	private String erpOrderCode;

	@Schema(description = "仓库id")
	private String storeId;

	@Schema(description = "订单id")
	private String orderId;

	@Schema(description = "订单编号")
	private String orderCode;

	@Schema(description = "订单版本")
	private Integer orderVersion;

	@Schema(description = "订单类型")
	private String orderType;

	@Schema(description = "订单状态")
	private String orderStatus;

	@Schema(description = "卖家id")
	private String sellerId;

	@Schema(description = "卖家名称")
	private String sellerName;

	@Schema(description = "卖家联系人")
	private String sellerContact;

	@Schema(description = "卖家联系方式")
	private String sellerContactWay;

	@Schema(description = "买家Id")
	private String buyerId;

	@Schema(description = "买家类型")
	private String buyerType;

	@Schema(description = "买家名称")
	private String buyerName;

	@Schema(description = "买家联系人")
	private String buyerContact;

	@Schema(description = "买家联系方式")
	private String buyerContactWay;

	@Schema(description = "是否已锁价")
	private Boolean ifLocked;

	@Schema(description = "锁价时间")
	private Date pricelockTime;

	@Schema(description = "锁价类型")
	private String pricelockType;

	@Schema(description = "协议Id")
	private String dealsId;

	@Schema(description = "协议名称")
	private String dealsName;

	@Schema(description = "业务员id")
	private String salesmanId;

	@Schema(description = "业务员姓名")
	private String salesmanName;

	@Schema(description = "业务员电话")
	private String salesmanMobile;

	@Schema(description = "工程名称")
	private String projectName;

	@Schema(description = "签约方量")
	private String signAmount;

	@Schema(description = "客户结算人姓名")
	private String 	clearName;

	@Schema(description = "是否修改卸货地")
	private String ifChangeAddress;

	@Schema(description = "运距")
	private String distance;

	@Schema(description = "是否为月结 Y: 是、N: 否")
	private String longFlag;

	@Schema(description = "组织结构id")
	private String orgId;

	@Schema(description = "组织机构名称")
	private String orgName;

	@Schema(description = "收货地址Id")
	private String addressId;

	@Schema(description = "渠道")
	private String createWay;

	@Schema(description = "是否线下支付")
	private Boolean ifUnderlinePay;

	@Schema(description = "是否买家可关闭")
	private Boolean ifBuyercanclose;

	@Schema(description = "是否卖家可关闭")
	private Boolean ifSellercanclose;

	@Schema(description = "销售区域详细路径")
	private String saleRegionPath;

	@Schema(description = "销售区域一级")
	private String saleRegion1;

	@Schema(description = "销售区域二级")
	private String saleRegion2;

	@Schema(description = "销售区域三级")
	private String saleRegion3;

	@Schema(description = "销售区域四级")
	private String saleRegion4;

	@Schema(description = "销售区域五级")
	private String saleRegion5;

	@Schema(description = "销售区域一级")
	private String saleRegionName1;

	@Schema(description = "销售区域二级")
	private String saleRegionName2;

	@Schema(description = "销售区域三级")
	private String saleRegionName3;

	@Schema(description = "销售区域四级")
	private String saleRegionName4;

	@Schema(description = "销售区域五级")
	private String saleRegionName5;

	@Schema(description = "订单卖家确认时间")
	private Date sellerConfirmTime;

	@Schema(description = "开始发货时间")
	private Date startDeliverTime;

	@Schema(description = "完成时间")
	private Date completeTime;

	@Schema(description = "订单关闭时间")
	private Date closeTime;

	@Schema(description = "门店代码")
	private String shopCode;

	@Schema(description = "门店名称")
	private String shopName;

	@Schema(description = "支付时间")
	private Date payTime;

	@Schema(description = "支付状态")
	private String payStatus;

	@Schema(description = "发货状态")
	private String deliverStatus;

	@Schema(description = "开票状态")
	private String invoiceStatus;

	@Schema(description = "退款状态")
	private String refundStatus;

	@Schema(description = "是否评论")
	private Boolean ifRemark;

	@Schema(description = "是否退款")
	private Boolean ifRefund;

	@Schema(description = "是否补款")
	private Boolean ifRepay;

	@Schema(description = "是否卖家关闭")
	private Boolean ifSellerClose;

	@Schema(description = "是否平台关闭")
	private Boolean ifPlatformClose;

	@Schema(description = "是否需要卖家确认")
	private Boolean ifNeedsellerconfirm;

	@Schema(description = "是否可以多次发货")
	private Boolean ifDeliverTimes;

	@Schema(description = "是否代客下单")
	private Boolean ifSellerCreate;

	@Schema(description = "买家备注")
	private String buyerMemo;

	@Schema(description = "卖家备注")
	private String sellerMemo;

	@Schema(description = "币种")
	private String currency;

	@Schema(description = "币种符号")
	private String currencySymbol;

	@Schema(description = "币种名称")
	private String currencyName;

	@Schema(description = "配送方式")
	private String deliverWay;

	@Schema(description = "支付期限")
	private Date payTimeLimit;

	@Schema(description = "提货期限")
	private Date takeTimeLimit;

	@Schema(description = "税率")
	private Byte taxRate;

	@Schema(description = "订单原总金额")
	private BigDecimal originOrderAmount;

	@Schema(description = "物流原总金额")
	private BigDecimal originLogisticAmount;

	@Schema(description = "资源原总金额")
	private BigDecimal originResourceAmount;

	@Schema(description = "其他原总金额")
	private BigDecimal originOthersAmount;

	@Schema(description = "订单实际总金额")
	private BigDecimal actualOrderAmount;

	@Schema(description = "物流实际总费用")
	private BigDecimal actualLogisticAmount;

	@Schema(description = "资源实际总金额")
	private BigDecimal actualResourceAmount;

	@Schema(description = "其他实际总金额")
	private BigDecimal actualOthersAmount;

	@Schema(description = "订单已执行总金额")
	private BigDecimal realtimeOrderAmount;

	@Schema(description = "物流已执行总金额")
	private BigDecimal realtimeLogisticAmount;

	@Schema(description = "货物已执行总金额")
	private BigDecimal realtimeResourceAmount;

	@Schema(description = "其他已执行总金额")
	private BigDecimal realtimeOthersAmount;

	@Schema(description = "已支付总金额")
	private BigDecimal orderPayedAmount;

	@Schema(description = "申请退款总金额")
	private BigDecimal requestRefundAmount;

	@Schema(description = "实际退款总金额")
	private BigDecimal realRefundAmount;

	@Schema(description = "补款金额")
	private BigDecimal supplementAmount;

	@Schema(description = "收货地址国家编码")
	private String countryCode;

	@Schema(description = "收货地址国家名称")
	private String countryName;

	@Schema(description = "收货地址省编码")
	private String provinceCode;

	@Schema(description = "收货地址省名称")
	private String provinceName;

	@Schema(description = "收货地址市/区编码")
	private String cityCode;

	@Schema(description = "收货地址市/区名称")
	private String cityName;

	@Schema(description = "收货地址区县编码")
	private String districtCode;

	@Schema(description = "收货地址区县名称")
	private String districtName;

	@Schema(description = "收货地址街道编码")
	private String streetCode;

	@Schema(description = "收货地址街道名称")
	private String streetName;

	@Schema(description = "详细收货地址")
	private String addressDetail;

	@Schema(description = "收货地经纬度")
	private String addressMap;

	@Schema(description = "收货地址编码")
	private String receiveAddressCode;

	@Schema(description = "卸货点编码")
	private String unloadingCode;

	@Schema(description = "收货人")
	private String receiver;

	@Schema(description = "收货人姓名")
	private String receiverName;

	@Schema(description = "收货人电话")
	private String receiverPhone;

	@Schema(description = "提货时间")
	private Date takeTime;

	@Schema(description = "提货时间段")
	private String takeTimeQuantum;

	@Schema(description = "买家取消原因")
	private String buyerCancelReason;

	@Schema(description = "线下支付凭证")
	private String underLinePic;

	@Schema(description = "线下支付备注")
	private String underLineMome;

	@Schema(description = "支付方式")
	private String payWay;

	@Schema(description = "支持的支付方式")
	private List<String> supportPayWay;

	@Schema(description = "搬运方式")
	private String carryWay;

	@Schema(description = "搬运楼层")
	private String carryFloor;

	@Schema(description = "搬运楼层字符串")
	private String carryFloorStr;

	@Schema(description = "买家删除")
	private Boolean buyerDel;

	@Schema(description = "卖家删除")
	private Boolean sellerDel;

	@Schema(description = "创建时间")
	private Date createTime;

	@Schema(description = "创建者")
	private String createUser;
	private String createUserNm;

	@Schema(description = "修改时间")
	private Date updateTime;

	@Schema(description = "修改者")
	private String updateUser;
	private String updateUserName;

	private boolean specPay;

	@Schema(description = "优惠码id")
	private String discountId;

	@Schema(description = "台班费")
	private BigDecimal machineShiftCost;

	@Schema(description = "搬运费")
	private BigDecimal truckage;

	@Schema(description = "其他费用")
	private BigDecimal othersAmount;

	@Schema(description = "项目信息")
	private String project;

	@Schema(description = "是否有搬运费规则")
	private boolean isHaveCartageRule;

	@Schema(description = "银行名称")
	private String bankName;

	@Schema(description = "银行编码")
	private String bankCode;

	@Schema(description = "支付渠道")
	private String paymentChannel;

	@Schema(description = "银行支付单号")
	private String bankBillNumber;

	@Schema(description = "计价方式")
	private String valuationWay;

	@Schema(description = "是否线下清算物流费")
	private boolean ifLogisticOffline;

	@Schema(description = "流向卸货点编码,ERP合同需要流向管控时必传")
	private String unloadingFlowCode;

	@Schema(description = "合同地址编码（卖家配送时必传）")
	private String contractAddressCode;

	@Schema(description = "订单子项")
	private List<ECOrderItemDTO> orderItemDTOList;

	@Schema(description = "订单加价项")
	private List<ECOrderItemAddDTO> orderItemAddDTOList;

	@Schema(description = "首车到位时间（计划开始时间）")
	private Date firstArriveTime;

	@Schema(description = "加价项总金额:包含润管砂浆商品金额，不包含加价项金额")
	private BigDecimal addItemAmount;

	@Schema(description = "施工部位")
	private String constructPlace;

	@Schema(description = "润管砂浆数量")
	private BigDecimal lubricityQuantity;

	@Schema(description = "润管砂浆单价")
	private BigDecimal lubricityPrice;

	@Schema(description = "卸料方式 00:自卸 01:泵送")
	private String unloadingWay;

	@Schema(description = "是否混合运输 1:是 0:否")
	private Integer ifMixedTransport;
}
