package com.ecommerce.open.api.dto.gnete;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;

@Data
public class BankAcctSignApplyResponseDTO implements IBusinessResponseDTO{

    @Serial
    private static final long serialVersionUID = 8749665941130485976L;
    @Schema(description = "应答码")
    private String rspCode;
    @Schema(description = "应答描述")
    private String rspResult;

    @Schema(description = "签约申请流水号\t32X\tC\t申请成功时返回")
    private String reqSn;
    @Schema(description = "是否授权方式\t1X\tC\t1表示授权，同一客户不同钱包已经和同个商户签约；空和其他正常签约流程")
    private String isAuth;
}
