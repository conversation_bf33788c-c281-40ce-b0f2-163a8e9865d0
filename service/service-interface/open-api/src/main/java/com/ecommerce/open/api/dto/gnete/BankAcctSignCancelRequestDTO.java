package com.ecommerce.open.api.dto.gnete;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;

@Data
public class BankAcctSignCancelRequestDTO implements IBusinessRequestDTO {
    @Serial
    private static final long serialVersionUID = 4146749929743292101L;

    @Schema(description = "商户订单号")
    private String mctOrderNo;
    @Schema(description = "钱包ID\t19N\tM\t电子钱包ID")
    private Long walletId;
    @Schema(description = "签约商户号\t32X\tO\t如果不填，默认使用钱包商户号进行解约")
    private String signMerNo;
    @Schema(description = "商户用户名称\t32X\tO\t如果不填写默认使用钱包的用户号解约")
    private String userName;
    @Schema(description = "签约业务类型\t6X\tO\t参考附录5.3 不填默认使用钱包默认的业务类型")
    private String businessCode;
    @Schema(description = "银行账户编号\t32 X\tM")
    private String accountNo;
    @Schema(description = "支付密码密文(使用encryptType指定的方式进行加密（最终密文是base64编码的字符串）。)")
    private String encryptPwd;
    @Schema(description = """
            加密类型(1：H5密码键盘加密（密码键盘先使用公钥加密，然后自身再加密）。
                2：非H5加密。（加密控件先使用公钥加密，然后控件自身再加密）。)""")
    private String encryptType;
    @Schema(description = "控件随机因子(加密随机因子，通过“获取控件随机因子”接口获取。)")
    private String plugRandomKey;
    @Schema(description = "证书签名密文(企业钱包ID所在集团的交易用户证书签名，签名方式见附录交易用户证书签名)")
    private String certSign;

}
