package com.ecommerce.open.api.dto.gnete;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;

@Data
public class BankAcctSignConfirmRequestDTO implements IBusinessRequestDTO {
    @Serial
    private static final long serialVersionUID = 8174404265715664445L;

    @Schema(description = "商户订单号")
    private String mctOrderNo;
    @Schema(description = "钱包ID\t19N\tM\t电子钱包ID")
    private Long walletId;
    @Schema(description = "原签约申请流水号\t1X\tM")
    private String oriReqSn;
    @Schema(description = "短信验证码\t6X\tM \t银行发送的签约验证码")
    private String verifyCode;

}
