package com.ecommerce.open.api.dto.gnete;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;

@Data
public class BankAcctSignConfirmResponseDTO implements IBusinessResponseDTO{

    @Serial
    private static final long serialVersionUID = -5788364849211379357L;
    @Schema(description = "应答码")
    private String rspCode;
    @Schema(description = "应答描述")
    private String rspResult;
    @Schema(description = "签约协议号\t60X\tC\t成功时返回")
    private String protocolNo;
}
