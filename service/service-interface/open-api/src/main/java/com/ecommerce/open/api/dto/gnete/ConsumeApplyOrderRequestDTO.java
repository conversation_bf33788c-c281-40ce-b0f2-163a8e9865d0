package com.ecommerce.open.api.dto.gnete;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;

@Data
public class ConsumeApplyOrderRequestDTO implements IBusinessRequestDTO {

    @Serial
    private static final long serialVersionUID = 7698090710202906497L;
    @Schema(description = "商户网站唯一订单号\t64X\tM\t支付下单时的商户网站唯一订单号")
    private String outTradeNo;
    @Schema(description = "钱包ID\t19N\tM\t电子钱包ID")
    private Long walletId;
    @Schema(description = "商品编号\t64X\tO\t商品编号")
    private String goodsId;
    @Schema(description = "商品名称\t128G\tO\t对一笔交易的具体描述信息。如果是多种商品，请将商品描述字符串累加传给goods_name")
    private String goodsName;
    @Schema(description = "商品摘要\t256G\tM\t商品的标题/交易标题/订单标题/订单关键字等:如‘大乐透’")
    private String subject;
    @Schema(description = "订单金额\t16N\tM\t单位：分。订单金额 ;优惠前的交易金额")
    private Long orderAmt;
    @Schema(description = "商户钱包ID\t19N\tM")
    private Long merWalletId;
    @Schema(description = "订单有效时间\t6G\tO\t该笔订单允许的最晚付款时间，逾期将关闭交易。取值范围：1m～15d。m-分钟，h-小时，d-天，1c-当天（1c-当天的情况下，无论交易何时创建，都在0点关闭）。 该参数数值不接受小数点， 如 1.5h，可转换为 90m。注：若为空，则默认为15d。")
    private String timeoutExpress;
    @Schema(description = "商户号\t32N\tO\t交易商户号")
    private Long merNo;
    @Schema(description = "商户名称\t100G\tO")
    private String merName;
    @Schema(description = "前端回调url\t255X\tO\t完成支付后前端跳转回商户的url。")
    private String frontCallbackUrl;
    @Schema(description = "消息推送url\t255X\tO\t服务器在消费/退货交易完成后主动通知商户服务器里指定的页面 http/https 路径。")
    private String notifyUrl;
    @Schema(description = "备注")
    private String remark;
}
