package com.ecommerce.open.api.dto.gnete;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;

@Data
public class ConsumeApplyOrderResponseDTO implements IBusinessResponseDTO{

    @Serial
    private static final long serialVersionUID = -7438933429682469651L;
        @Schema(description = "应答码")
        private String rspCode;
        @Schema(description = "应答描述")
        private String rspResult;
        @Schema(description = "商户订单号\t64X\tM\t服务端产生的消费类交易的商户订单号（或支付流水号），用于客户端唯一标记该笔交易。")
        private String mctOrderNo;

        @Schema(description = "消费类支付（H5）url\t255X\tM\t支付URL，跳转到支付插件。")
        private String payH5Url;
        @Schema(description = "优惠描述\t40X\tC\t有优惠时出现，优惠中文说明")
        private String disctDesc;
        @Schema(description = "优惠金额\t16N\tC\t有优惠时出现")
        private String disctAmt;
        @Schema(description = "实付金额\t16N\tC\t有优惠时出现。订单金额-优惠金额")
        private String actPayAmt;

}
