package com.ecommerce.open.api.dto.gnete;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class GetPlugRandomKeyItemResponseDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = -9166989098616917346L;
    @Schema(description = "控件随机因子集合")
    private String plugRandomKey;
}
