package com.ecommerce.open.api.dto.gnete;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.util.List;

@Data
public class GetPlugRandomKeyResponseDTO implements IBusinessResponseDTO{
    @Serial
    private static final long serialVersionUID = -2122524473160902815L;

    @Schema(description = "应答码")
    private String rspCode;
    @Schema(description = "应答描述")
    private String rspResult;

    @Schema(description = "控件随机因子集合")
    private List<GetPlugRandomKeyItemResponseDTO> list;
}
