package com.ecommerce.open.api.dto.gnete;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.util.List;

@Data
public class GuaranteedConfirmPayRequestDTO implements IBusinessRequestDTO {
    @Serial
    private static final long serialVersionUID = 3124780121901430157L;

    @Schema(description = "商户订单号\t64X\tM\t结算商户订单号，商户请求唯一")
    private String mctOrderNo;
    @Schema(description = "原交易订单号\t64X\tC\t原交易订单号/担保支付订单号。 业务类型为担保支付，必填，值为担保支付订单号。")
    private String oriTransOrderNo;
    @Schema(description = "系统参考号\t20X\tC\t业务类型为好支付时，必填；其他业务类型为空。")
    private String refNo;
    @Schema(description = "清算日期\t8N\tC\t业务类型为好支付时，必填；其他业务类型为空。")
    private String settDate;
    @Schema(description = "平台钱包ID")
    private Long platformWalletId;
    @Schema(description = "结算金额\t20N\tO\t结算金额，单位：分。结算金额=分账金额之和。")
    private Long settAmt;
    @Schema(description = "是否完结(:Y-是N-否，完结后不允许再调用担保支付确认接口)")
    private String isFinished;
    @Schema(description = "业务类型\t6X\tM\t参考附录5.3，目前仅支付（暂时只支持：担保支付、好支付）")
    private String bizType;
    @Schema(description = "通知url\t200X\tO")
    private String notifyUrl;
    @Schema(description = "随机校验码\t200X\tO\t客户系统上送")
    private String randomValidCode;
    @Schema(description = "外部索引号\t64X\tO\t客户系统上送")
    private String extIdxNo;
    @Schema(description = "分账规则ID\t20N\tO\t规则ID ,未上送分账规则ID时，分账方只能为平台钱包ID")
    private String profitSharingRuleId;
    @Schema(description = "分账单信息")
    private List<GuaranteedConfirmPayRequestItemDTO> profitSharingList;
    @Schema(description = "备注")
    private String remark;
    @Schema(description = "摘要")
    private String abst;


}
