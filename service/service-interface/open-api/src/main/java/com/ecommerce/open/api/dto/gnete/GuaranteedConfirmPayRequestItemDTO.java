package com.ecommerce.open.api.dto.gnete;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class GuaranteedConfirmPayRequestItemDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 5424212128253783537L;
    @Schema(description = "分账方钱包ID")
    private Long profitSharingWalletId;
    @Schema(description = "分账金额(金额单位：分。)")
    private Long profitSharingAmt;
    @Schema(description = "分账描述")
    private String profitSharingDesc;

}
