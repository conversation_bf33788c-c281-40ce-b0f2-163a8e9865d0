package com.ecommerce.open.api.dto.gnete;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;

@Data
public class GuaranteedConfirmPayResponseDTO implements IBusinessResponseDTO {
    @Serial
    private static final long serialVersionUID = -4366528554805522640L;

    @Schema(description = "应答码")
    private String rspCode;
    @Schema(description = "应答描述")
    private String rspResult;
    @Schema(description = "商户订单号")
    private String mctOrderNo;

    @Schema(description = "结算单号")
    private String settOrderNo;
    @Schema(description = "原交易订单号")
    private String oriTransOrderNo;

}
