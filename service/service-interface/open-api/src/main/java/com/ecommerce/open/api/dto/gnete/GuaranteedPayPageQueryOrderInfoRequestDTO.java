package com.ecommerce.open.api.dto.gnete;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;

@Data
public class GuaranteedPayPageQueryOrderInfoRequestDTO implements IBusinessRequestDTO {
    @Serial
    private static final long serialVersionUID = 8047784077880935743L;

    @Schema(description = "页面大小(M 不传默认为10（最大限制50）)")
    private Integer pageSize;
    @Schema(description = "页数(M 从1开始 不传默认为1。)")
    private Integer pageNo;
    @Schema(description = "平台钱包ID M ")
    private Long platformWalletId;
    @Schema(description = "下单开始时间(yyyy-MM-dd HH:mm:ss)")
    private String startOrderTime;
    @Schema(description = "下单结束时间(yyyy-MM-dd HH:mm:ss)")
    private String endOrderTime;
    @Schema(description = "商户订单号")
    private String mctOrderNo;
    @Schema(description = "担保支付订单号")
    private String guaranteedOrderNo;
    @Schema(description = "买家钱包ID")
    private Long buyerWalletId;
    @Schema(description = "卖家钱包ID")
    private Long merWalletId;
    @Schema(description = "订单状态(订单状态：00-未支付；01-支付处理中；02-待结算；03-支付失败；04-长时间未支付置为交易失败；05-部分结算；06-订单完成；07-订单撤销；08-订单部分退款")
    private String orderStatus;

}
