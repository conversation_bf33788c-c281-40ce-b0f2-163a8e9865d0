package com.ecommerce.open.api.dto.gnete;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.util.List;

@Data
public class GuaranteedPayPageQueryOrderInfoResponseDTO implements IBusinessResponseDTO {
    @Serial
    private static final long serialVersionUID = 650494387372811419L;

    @Schema(description = "应答码")
    private String rspCode;
    @Schema(description = "应答描述")
    private String rspResult;

    @Schema(description = "页面大小(不传默认为10（最大限制50）)")
    private String pageSize;
    @Schema(description = "页数(从1开始 不传默认为1。)")
    private String pageNo;
    @Schema(description = "记录总数")
    private Long total;

    private List<GuaranteedPayPageQueryOrderInfoResponseItemDTO> rowList;

}
