package com.ecommerce.open.api.dto.gnete;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class GuaranteedPayPageQueryOrderInfoResponseItemDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 7253576075914309736L;
    @Schema(description = "商户订单号")
    private String mctOrderNo;
    @Schema(description = "担保支付订单号")
    private String guaranteedOrderNo;
    @Schema(description = "买家钱包ID")
    private Long buyerWalletId;
    @Schema(description = "平台钱包ID")
    private Long platformWalletId;
    @Schema(description = "卖家钱包ID")
    private Long merWalletId;
    @Schema(description = "商品摘要")
    private String subject;
    @Schema(description = "下单金额(单位：分。)")
    private Long orderAmt;
    @Schema(description = "实际订单金额(单位：分。)")
    private Long actOrderAmt;
    @Schema(description = "已结算金额(单位：分。)")
    private Long settledAmt;
    @Schema(description = "结算中金额(单位：分。)")
    private Long settlingAmt;
    @Schema(description = "待结算金额(单位：分。)")
    private Long toSettleAmt;
    @Schema(description = "已退款金额(单位：分。)")
    private Long refundedAmt;
    @Schema(description = "退款中金额(单位：分。)")
    private Long refundingAmt;
    @Schema(description = "分账退款金额(单位：分。)")
    private Long refundedGoodsAmt;
    @Schema(description = "分账退款中金额(单位：分。)")
    private Long refundingGoodsAmt;
    @Schema(description = "是否完结(Y-是N-否，完结后不允许再调用担保支付确认接口)")
    private String isFinished;
    @Schema(description = "订单状态(00-未支付；01-支付成功；02-支付失败；03-长时间未支付置为交易失败；04-部分到货；05-订单完成；06-订单撤销)")
    private String orderStatus;
    @Schema(description = "订单支付失效时间(格式：yyyy-mm-dd hh:mm:ss)")
    private String payInvalidTime;
    @Schema(description = "订单结算失效日期(yyyyMMdd)")
    private String settInvalidDate ;
}
