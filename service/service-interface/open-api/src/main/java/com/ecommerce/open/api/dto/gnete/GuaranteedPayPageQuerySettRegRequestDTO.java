package com.ecommerce.open.api.dto.gnete;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;

@Data
public class GuaranteedPayPageQuerySettRegRequestDTO implements IBusinessRequestDTO {
    @Serial
    private static final long serialVersionUID = 541908351598585817L;

    @Schema(description = "页面大小(M 不传默认为10（最大限制50）)")
    private Integer pageSize;
    @Schema(description = "页数(M 从1开始 不传默认为1。)")
    private Integer pageNo;
    @Schema(description = "结算开始时间(yyyy-MM-dd HH:mm:ss)")
    private String startSettTime;
    @Schema(description = "结算结束时间(yyyy-MM-dd HH:mm:ss)")
    private String endSettTime;
    @Schema(description = "商户订单号")
    private String mctOrderNo;
    @Schema(description = "结算单号")
    private String settOrderNo;
    @Schema(description = "原交易订单号\t64X\tO\t原交易订单号/担保支付订单号。 业务类型为担保支付，必填，值为担保支付订单号。")
    private String oriTransOrderNo;
    @Schema(description = "平台钱包ID M ")
    private Long platformWalletId;
    @Schema(description = "分账方钱包ID\t19N\tO\t分账方钱包ID（分账方入金方）")
    private Long profitSharingWalletId;
    @Schema(description = "业务类型\t6X\tM\t参考附录5.3")
    private String bizType;
}
