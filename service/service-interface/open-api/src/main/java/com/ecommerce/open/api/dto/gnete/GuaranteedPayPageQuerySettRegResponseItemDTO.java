package com.ecommerce.open.api.dto.gnete;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Data
public class GuaranteedPayPageQuerySettRegResponseItemDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 7417079388305086458L;

    @Schema(description = "商户订单号")
    private String mctOrderNo;
    @Schema(description = "结算单号")
    private String settOrderNo;
    @Schema(description = "原交易订单号/担保支付订单号。 业务类型为担保支付，必填，值为担保支付订单号。")
    private String oriTransOrderNo;
    @Schema(description = "系统参考号\t20X\tC\t业务类型为好支付时，必填；其他业务类型为空。")
    private String refNo;
    @Schema(description = "清算日期\t8N\tC\t业务类型为好支付时，必填；其他业务类型为空。")
    private String settDate;
    @Schema(description = "平台钱包ID")
    private Long platformWalletId;
    @Schema(description = "卖家钱包ID")
    private Long merWalletId;
    @Schema(description = "结算金额\t20N\tO\t结算金额，单位：分。结算金额=分账金额之和。")
    private Long settAmt;
    @Schema(description = "成功金额\t20N\tO\t单位：分。已分账成功。")
    private Long successAmt;
    @Schema(description = "失败金额\t20N\tO\t单位：分。分账失败的金额。")
    private Long failAmt;
    @Schema(description = "处理中金额\t20N\tO\t单位：分。分账中的金额。")
    private Long dealingAmt;
    @Schema(description = "\t结算状态\t2X\tO\t分账状态：00-未处理；01-处理中；02-结算成功；03-部分结算成功；04-结算失败；")
    private String settStatus;
    @Schema(description = "业务类型\t6X\tM\t参考附录5.3，目前仅支付（暂时只支持：担保支付、好支付）")
    private String bizType;
    @Schema(description = "分账规则ID\t20N\tO\t规则ID ,未上送分账规则ID时，分账方只能为平台钱包ID")
    private String profitSharingRuleId;
    @Schema(description = "备注")
    private String remark;
    @Schema(description = "摘要")
    private String abst;
    @Schema(description = "分账单信息")
    private List<GuaranteedPayQuerySettRegDetailResponseItemDTO> profitSharingList;
}
