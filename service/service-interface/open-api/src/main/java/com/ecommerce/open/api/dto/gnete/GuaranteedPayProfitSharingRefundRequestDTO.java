package com.ecommerce.open.api.dto.gnete;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.util.List;

@Data
public class GuaranteedPayProfitSharingRefundRequestDTO implements IBusinessRequestDTO {

    @Serial
    private static final long serialVersionUID = 4752084974218818651L;
    @Schema(description = "退款商户订单号")
    private String mctOrderNo;
    @Schema(description = "担保支付订单号")
    private String guaranteedOrderNo;
    @Schema(description = "平台钱包ID")
    private Long platformWalletId;
    @Schema(description = "分账退款金额")
    private Long refundGoodsAmt;
    @Schema(description = "通知url\t200X\tO")
    private String notifyUrl;
    @Schema(description = "外部索引号\t64X\tO\t客户系统上送")
    private String extIdxNo;
    @Schema(description = "循环域(分账退款信息) ")
    private List<GuaranteedPayProfitSharingRefundRequestItemDTO> rowlist;
    @Schema(description = "备注")
    private String remark;
    @Schema(description = "摘要")
    private String abst;

}
