package com.ecommerce.open.api.dto.gnete;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class GuaranteedPayProfitSharingRefundRequestItemDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = -978287519168860683L;
    @Schema(description = "分账方钱包ID\t19N\tM")
    private String profitSharingWalletId;
    @Schema(description = "分账退款金额\t16N\tM\t金额单位：分。")
    private Long profitSharingRefundAmt;
    @Schema(description = "分账退款描述\t200X\tO")
    private String remark;

}
