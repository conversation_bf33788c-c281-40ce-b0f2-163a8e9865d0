package com.ecommerce.open.api.dto.gnete;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;

@Data
public class GuaranteedPayProfitSharingRefundResponseDTO implements IBusinessResponseDTO {

    @Serial
    private static final long serialVersionUID = 2138770172697441243L;
    @Schema(description = "应答码")
    private String rspCode;
    @Schema(description = "应答描述")
    private String rspResult;

    @Schema(description = "退款商户订单号")
    private String mctOrderNo;
    @Schema(description = "担保支付订单号")
    private String guaranteedOrderNo;

}
