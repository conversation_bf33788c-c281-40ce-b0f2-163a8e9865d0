package com.ecommerce.open.api.dto.gnete;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;

@Data
public class GuaranteedPayQueryOrderDetailRequestDTO implements IBusinessRequestDTO {
    @Serial
    private static final long serialVersionUID = 6093753183158035386L;

    @Schema(description = "商户下单时的订单号")
    private String mctOrderNo;
    @Schema(description = "担保支付订单号")
    private String guaranteedOrderNo;
    @Schema(description = "平台钱包ID")
    private Long platformWalletId;
    @Schema(description = "支付钱包ID")
    private Long buyerWalletId;
    @Schema(description = "卖家钱包ID")
    private Long merWalletId;

}
