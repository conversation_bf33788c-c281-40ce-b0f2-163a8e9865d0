package com.ecommerce.open.api.dto.gnete;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.util.List;

@Data
public class GuaranteedPayQueryOrderDetailResponseDTO implements IBusinessResponseDTO {
    @Serial
    private static final long serialVersionUID = 4715829123724615877L;

    @Schema(description = "响应代码")
    private String rspCode;
    @Schema(description = "应答描述")
    private String rspResult;
    @Schema(description = "商户订单号")
    private String mctOrderNo;
    @Schema(description = "担保支付订单号")
    private String guaranteedOrderNo;
    @Schema(description = "买家钱包ID")
    private Long buyerWalletId;
    @Schema(description = "平台钱包ID(平台可以是卖家钱包ID)")
    private Long platformWalletId;
    @Schema(description = "商户钱包ID(卖家钱包ID)")
    private Long merWalletId;
    @Schema(description = "下单金额(单位：分。)")
    private Long orderAmt;
    @Schema(description = "实付金额(单位：分。)")
    private Long actPayedAmt;
    @Schema(description = "已结算金额(单位：分。)")
    private Long settledAmt;
    @Schema(description = "结算中金额(单位：分。)")
    private Long settlingAmt;
    @Schema(description = "待结算金额(单位：分。)")
    private Long toSettleAmt;
    @Schema(description = "已退款金额(单位：分。)")
    private Long refundedAmt;
    @Schema(description = "退款中金额 单位：分。")
    private Long refundingAmt;
    @Schema(description = "分账退款金额(单位：分。)")
    private Long refundedGoodsAmt;
    @Schema(description = "分账退款中金额(单位：分。)")
    private Long refundingGoodsAmt;
    @Schema(description = "是否完成\t4X\tM\t是否完成:Y-是N-否，完结后不允许再调用担保支付确认接口")
    private String isFinished;
    @Schema(description = "订单状态\t2X\tM\t订单状态：00-未支付；01-支付处理中；02-待结算；03-支付失败；04-长时间未支付置为交易失败；05-部分结算；06-订单完成；07-订单撤销(全额退款)；08-订单已完成且部分退款")
    private String orderStatus;
    @Schema(description = "订单支付失效时间\t24X\tO\t格式：yyyy-mm-dd hh:mm:ss")
    private String payInvalidTime;
    @Schema(description = "订单结算失效日期\t8X\tO\t格式：yyyyMMdd")
    private String settInvalidDate;
    @Schema(description = "循环域(支付明细list)")
    private List<GuaranteedPayQueryOrderDetailResponsePayListDTO> payList;
    @Schema(description = "循环域(结算单信息)")
    private List<GuaranteedPayQueryOrderDetailResponseSettListDTO> settList;
}
