package com.ecommerce.open.api.dto.gnete;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;

@Schema(name = "可根据结算单号（settOrderNo）条件查询明细，或者根据mctOrderNo + platformWalletId + bizType查询查询明细。（两种方式二选一）")
@Data
public class GuaranteedPayQuerySettRegDetailRequestDTO implements IBusinessRequestDTO {
    @Serial
    private static final long serialVersionUID = -2783567832491481453L;

    @Schema(description = "商户订单号")
    private String mctOrderNo;
    @Schema(description = "结算单号")
    private String settOrderNo;
    @Schema(description = "平台钱包ID C ")
    private Long platformWalletId;
    @Schema(description = "业务类型")
    private String bizType;

}
