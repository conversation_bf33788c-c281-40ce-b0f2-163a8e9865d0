package com.ecommerce.open.api.dto.gnete;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;

@Data
public class GuaranteedPayRefundQueryRequestDTO implements IBusinessRequestDTO {
    @Serial
    private static final long serialVersionUID = 4979163832210871618L;

    @Schema(description = "退款商户订单号 C")
    private String mctOrderNo;
    @Schema(description = "交易订单号 C")
    private String transOrderNo;
    @Schema(description = "平台钱包ID M")
    private String platformWalletId;

}
