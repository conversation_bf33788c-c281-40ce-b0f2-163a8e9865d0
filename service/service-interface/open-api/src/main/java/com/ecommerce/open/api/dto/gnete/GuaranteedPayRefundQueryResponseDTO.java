package com.ecommerce.open.api.dto.gnete;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.util.List;

@Data
public class GuaranteedPayRefundQueryResponseDTO implements IBusinessResponseDTO {
    @Serial
    private static final long serialVersionUID = 3393718687902312446L;

    @Schema(description = "应答码")
    private String rspCode;
    @Schema(description = "应答描述")
    private String rspResult;

    @Schema(description = "退款商户订单号")
    private String mctOrderNo;
    @Schema(description = "交易订单号")
    private String transOrderNo;
    @Schema(description = "担保支付订单号")
    private String guaranteedOrderNo;
    @Schema(description = "实退金额 单位：分")
    private Long actRefundAmt;
    @Schema(description = "状态\t4X\tM\t状态：00-未处理，01-处理中；02-失败；03-成功")
    private String refundStatus;
    @Schema(description = "平台钱包ID")
    private Long platformWalletId;
    @Schema(description = "备注")
    private String remark;
    @Schema(description = "摘要")
    private String abst;
    @Schema(description = "循环域(分账退款信息) ")
    private List<GuaranteedPayRefundQueryResponseItemDTO> profitSharingRefundList;
}
