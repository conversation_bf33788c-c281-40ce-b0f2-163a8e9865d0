package com.ecommerce.open.api.dto.gnete;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;

@Data
public class GuaranteedPayRefundRequestDTO implements IBusinessRequestDTO {
    @Serial
    private static final long serialVersionUID = 740080837696050531L;

    @Schema(description = "退款商户订单号")
    private String mctOrderNo;
    @Schema(description = "担保支付订单号")
    private String guaranteedOrderNo;
    @Schema(description = "平台钱包ID")
    private Long platformWalletId;
    @Schema(description = "退款金额\t16N\tM\t金额单位：分，支持多次退货,支持部分退货。")
    private Long refundAmt;
    @Schema(description = "备注")
    private String remark;
    @Schema(description = "摘要")
    private String abst;

}
