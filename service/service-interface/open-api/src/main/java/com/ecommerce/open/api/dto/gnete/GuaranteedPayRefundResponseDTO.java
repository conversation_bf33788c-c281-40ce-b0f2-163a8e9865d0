package com.ecommerce.open.api.dto.gnete;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;

@Data
public class GuaranteedPayRefundResponseDTO implements IBusinessResponseDTO {
    @Serial
    private static final long serialVersionUID = -5977920273044301591L;

    @Schema(description = "应答码")
    private String rspCode;
    @Schema(description = "应答描述")
    private String rspResult;

    @Schema(description = "退款商户订单号")
    private String mctOrderNo;
    @Schema(description = "交易订单号")
    private String transOrderNo;
    @Schema(description = "担保支付订单号")
    private String guaranteedOrderNo;
    @Schema(description = "实退金额 单位：分")
    private Long actRefundAmt;

}
