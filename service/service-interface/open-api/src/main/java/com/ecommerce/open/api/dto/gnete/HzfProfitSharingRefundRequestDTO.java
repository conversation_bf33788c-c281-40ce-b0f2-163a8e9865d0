package com.ecommerce.open.api.dto.gnete;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.util.List;

@Data
public class HzfProfitSharingRefundRequestDTO implements IBusinessRequestDTO {
    @Serial
    private static final long serialVersionUID = -2071464125382286548L;

    @Schema(description = "商户退货订单号 64X M 商户退货订单号唯一")
    private String mctOrderNo;
    @Schema(description = "清算日期 8X M yyyyMMdd")
    private String settDate;
    @Schema(description = "系统参考号 20X M 系统参考号")
    private String refNo;
    @Schema(description = "平台钱包ID 19N M 平台钱包ID")
    private String platformWalletId;
    @Schema(description = "退款金额 16N M 退款金额单位：分，支持多次退货,支持部分退货。")
    private Long refundAmt;
    @Schema(description = "通知url 200X O")
    private String notifyUrl;
    @Schema(description = "外部索引号 64X O 客户系统上送")
    private String extIdxNo;
    @Schema(description = "交易类型 2X M 交易类型(1分账前退款、2分账后退款）")
    private String transType;
    @Schema(description = " 循环域(分账方退款信息)     当交易类型为：2分账后退款，必传。")
    private List<HzfProfitSharingRefundRequestItemDTO> profitSharingRefundList;
    @Schema(description = "摘要\t64X\tO")
    private String abst;
    @Schema(description = "备注\t128G\tO")
    private String remark;

}
