package com.ecommerce.open.api.dto.gnete;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class HzfProfitSharingRefundRequestItemDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 3455484174641117342L;

    @Schema(description = "分账方钱包ID\t19N\tM")
    private String profitSharingWalletId;
    @Schema(description = "分账退款金额\t16N\tM\t金额单位：分。")
    private Long profitSharingRefundAmt;
    @Schema(description = "分账退款描述\t128G\tO")
    private String profitSharingRefundDesc;
}
