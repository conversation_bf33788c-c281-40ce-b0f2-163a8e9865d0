package com.ecommerce.open.api.dto.gnete;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;

@Data
public class HzfProfitSharingRefundResponseDTO implements IBusinessResponseDTO{

    @Serial
    private static final long serialVersionUID = 6927596882369399443L;
    @Schema(description = "应答码")
    private String rspCode;
    @Schema(description = "应答描述")
    private String rspResult;

    @Schema(description = "商户退货订单号 64X M 商户退货订单号唯一")
    private String mctOrderNo;
    @Schema(description = "清算日期 8X M yyyyMMdd")
    private String settDate;
    @Schema(description = "系统参考号 20X M 系统参考号")
    private String refNo;
    @Schema(description = "平台钱包ID 19N M 平台钱包ID")
    private String platformWalletId;

}