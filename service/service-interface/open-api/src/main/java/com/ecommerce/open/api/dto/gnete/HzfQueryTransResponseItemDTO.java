package com.ecommerce.open.api.dto.gnete;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class HzfQueryTransResponseItemDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 2408470667846762068L;
    
    @Schema(description = "商户订单号  64X  O")
    private String mctOrderNo;
    @Schema(description = "系统参考号  20X  C")
    private String refNo;
    @Schema(description = "清算日期  8N  C")
    private String settDate;
    @Schema(description = "平台钱包ID  19N  O")
    private String platformWalletId;
    @Schema(description = "交易金额  20N  O  单位：分。")
    private Long trxAmount;
    @Schema(description = "清算金额  20N  O  单位：分。")
    private Long settAmount;
    @Schema(description = "（日结）手续费  20N  O  单位：分。")
    private Long feeAmount;
    @Schema(description = "总手续费  20N  O  单位：分。")
    private Long totalFeeAmoun;
    @Schema(description = "入账金额  20N  O  单位：分。")
    private Long payAmoun;
    @Schema(description = "已结算金额  20N  O  单位：分。")
    private Long settledAmt;
    @Schema(description = "结算中金额  20N  O  单位：分。")
    private Long settlingAmt;
    @Schema(description = "待结算金额  20N  O  单位：分。")
    private Long toSettleAmt;
    @Schema(description = "已退款金额  20N  O  单位：分。")
    private Long refundedAmt;
    @Schema(description = "退款中金额  20N  O  单位：分。")
    private Long refundingAmt;
    @Schema(description = "订单状态  2X  O   订单状态：00-待结算；01-部分结算；02-订单完成；03-订单撤销（全部退款）；04-订单部分退款）")
    private String orderStatus;
}
