package com.ecommerce.open.api.dto.gnete;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;

@Data
public class QueryAcctRelatedInfoRequestDTO implements IBusinessRequestDTO {
    @Serial
    private static final long serialVersionUID = 783036486680050889L;

    @Schema(description = "商户订单号(客户端唯一标记该笔交易,即: 请求流水号，我方定义的交易流水号)")
    private String mctOrderNo;

    @Schema(description = "钱包ID(只允许查询C端钱包用户信息)")
    private Long walletId;
    @Schema(description = "手机号")
    private String mobileNo;


}
