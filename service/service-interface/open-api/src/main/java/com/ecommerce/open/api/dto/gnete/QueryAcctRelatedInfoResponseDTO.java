package com.ecommerce.open.api.dto.gnete;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;

@Data
public class QueryAcctRelatedInfoResponseDTO implements IBusinessResponseDTO {
    @Serial
    private static final long serialVersionUID = -5876729591823450693L;

    @Schema(description = "应答码")
    private String rspCode;
    @Schema(description = "应答描述")
    private String rspResult;

    @Schema(description = "钱包ID")
    private Long walletId;
    @Schema(description = "钱包名称")
    private String walletName;
    @Schema(description = "用户姓名")
    private String userName;
    @Schema(description = "手机号")
    private String mobileNo;
    @Schema(description = "用户中心userUuid")
    private String userUuid;
    @Schema(description = "客商ID")
    private String custId;
    @Schema(description = "客商名称")
    private String custName;


}
