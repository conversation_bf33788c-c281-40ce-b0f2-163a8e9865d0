package com.ecommerce.open.api.dto.gnete;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;

@Data
public class QueryBatchTransResultRequestDTO implements IBusinessRequestDTO {

    @Serial
    private static final long serialVersionUID = -8218964244112564804L;

    @Schema(description = "原商户订单号\t64X\tC\t批量申请时的商户打单号")
    private String oriMctOrderNo;
    @Schema(description = "登记ID\t32X\tC\t接收成功时返回")
    private String regId;

    @Schema(description = """
            查询类型	1N	M	1：批量收款；
                        2：批量代发工资；
                        3：批量转账；
                        4：批量汇款
                        5：批量发券""")
    private Integer operType;
    @Schema(description = "明细流水号\t32X\tO\t如果只查批次中某条明细，请输入明细流水号")
    private String sn;

}
