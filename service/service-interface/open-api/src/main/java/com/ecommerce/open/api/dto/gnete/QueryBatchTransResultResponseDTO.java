package com.ecommerce.open.api.dto.gnete;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.util.List;

@Data
public class QueryBatchTransResultResponseDTO implements IBusinessResponseDTO {

    @Serial
    private static final long serialVersionUID = 3611536280092040635L;
    @Schema(description = "应答码")
    private String rspCode;
    @Schema(description = "应答描述")
    private String rspResult;

    @Schema(description = "商户订单号")
    private String oriMctOrderNo;
    @Schema(description = "登记ID\t32X\tM\t接收成功时返回")
    private String regId;
    @Schema(description = "交易预约时间\t14X\tO\t原交易预约时间 格式：yyyyMMddHHmmss")
    private String bookingTime;
    @Schema(description = "批次状态\t1N\tM\t1：待处理；2：处理中；3：处理完成")
    private Integer status;

    private List<QueryBatchTransResultResponseItemDTO> list;
}
