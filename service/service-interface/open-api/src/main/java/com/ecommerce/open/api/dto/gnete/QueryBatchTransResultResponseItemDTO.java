package com.ecommerce.open.api.dto.gnete;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class QueryBatchTransResultResponseItemDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = -8803595622508717623L;
    @Schema(description = "明细流水号\t32X\tM\t该批次明细唯一标记")
    private String sn;
    @Schema(description = "交易订单号\t64X\tO")
    private String transOrderNo;

    @Schema(description = "外部索引号\t64X\tO\t客户系统上送")
    private String extIdxNo;
    @Schema(description = "明细状态\t1N\tM\t1：待处理；2：处理中；3：成功；4：失败")
    private Integer status;
    @Schema(description = "明细响应码\t8N\tM")
    private String dtlRspCode;
    @Schema(description = "明细响应信息\t128G\tM\t结果描述，失败原因等。")
    private String dtlResultDscrb;
    @Schema(description = "手续费金额\t12N\tO\t手续费金额（单位：分）")
    private String feeAmt;
    @Schema(description = "备注\t128G\tO")
    private String remark;

}
