package com.ecommerce.open.api.dto.gnete;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;

import java.io.Serial;

@Data
public class QueryBindBankCardRequestDTO implements IBusinessRequestDTO {

    @Serial
    private static final long serialVersionUID = 39724239543369005L;
    @Schema(description = "钱包ID")
    @NotNull
    private Long walletId;

    @Schema(description = "银行账户")
    private String bankAcctNo;

    @Schema(description = "查询类型 O 查询绑定银行卡 1查询已签约银行卡")
    private Integer signBindType;

    @Schema(description = "签约商户号")
    private String signMerNo;

}
