package com.ecommerce.open.api.dto.gnete;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class QueryBindBankCardResponseItemDTO implements Serializable {


    @Serial
    private static final long serialVersionUID = -4781646175567729943L;

    @Schema(description = "银行账户")
    private String bankAcctNo;
    @Schema(description = "银行账户名称")
    private String bankAcctName;
    @Schema(description = "是否默认卡(0：非默认卡；1：默认卡；)")
    private Integer isDefault;

    @Schema(description = "开户行号")
    private Long bankNo;
    @Schema(description = "银行名称")
    private String bankName;
    @Schema(description = "电子联行号")
    private Long elecBankNo;
    @Schema(description = "银行账户类型(0：对公银行账户 1：对私银行卡)")
    private Integer bankAcctType;
    @Schema(description = "是否信用卡(0不是；1是)")
    private Integer creditMark;
    @Schema(description = "电子协议编号")
    private String protocolNo;
    @Schema(description = "签约商户号(签约的银行卡才返回)")
    private String signMerNo;
    @Schema(description = "业务类型(返回参考：银联网络电子钱包业务中心接口文档_v1.0.48(对外).docx 5.3章节)")
    private String businessCode;
    @Schema(description = "是否签约类型(0绑定银行卡 1签约银行卡 2协议支付签约银行卡)")
    private Integer signBindType;
    @Schema(description = "是否需要重新签约(0-不续约 1-需要重新签约)")
    private Integer isNeedReSign;

}
