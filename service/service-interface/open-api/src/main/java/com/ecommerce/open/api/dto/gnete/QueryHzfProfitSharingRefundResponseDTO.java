package com.ecommerce.open.api.dto.gnete;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.util.List;

@Data
public class QueryHzfProfitSharingRefundResponseDTO implements IBusinessResponseDTO{

    @Serial
    private static final long serialVersionUID = -1689329254240694226L;
    @Schema(description = "应答码")
    private String rspCode;
    @Schema(description = "应答描述")
    private String rspResult;
    @Schema(description = "商户退货订单号 64X M 商户退货订单号唯一")
    private String mctOrderNo;
    @Schema(description = "交易订单号\t64X\tM")
    private String transOrderNo;

    @Schema(description = "清算日期 8X M yyyyMMdd")
    private String settDate;
    @Schema(description = "系统参考号 20X O 系统参考号")
    private String refNo;
    @Schema(description = "退款金额\t16N\tO\t退款金额，金额单位：分")
    private Long refundAmt;
    @Schema(description = "退款状态\t4X\tM\t状态：00-未处理，01-处理中；02-失败；03-成功")
    private String refundStatus;
    @Schema(description = "平台钱包ID\t19N\tM\t平台钱包ID")
    private String platformWalletId;
    @Schema(description = "循环域(分账退款信息) \t\t\t分账退款返回该信息")
    private List<QueryHzfProfitSharingRefundResponseItemDTO> profitSharingRefundList;
    @Schema(description = "摘要\t64X\tO")
    private String abst;
    @Schema(description = "备注\t128G\tO")
    private String remark;

}
