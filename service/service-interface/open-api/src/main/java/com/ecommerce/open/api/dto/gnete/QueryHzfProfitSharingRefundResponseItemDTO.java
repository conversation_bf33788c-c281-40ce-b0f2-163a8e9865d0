package com.ecommerce.open.api.dto.gnete;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class QueryHzfProfitSharingRefundResponseItemDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = -7683696265391029875L;

    @Schema(description = "分账方钱包ID\t19N\tC")
    private String profitSharingWalletId;
    @Schema(description = "分账退款金额\t16N\tC\t金额单位：分。")
    private Long profitSharingRefundAmt;
    @Schema(description = "分账退款描述\t200X\tO")
    private String profitSharingRefundDesc;

}
