package com.ecommerce.open.api.dto.gnete;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.util.List;

@Data
public class QueryProfitSharingMerRelResponseDTO implements IBusinessResponseDTO{
    @Serial
    private static final long serialVersionUID = -1452298704151208280L;

    @Schema(description = "应答码")
    private String rspCode;
    @Schema(description = "应答描述")
    private String rspResult;

    private List<QueryProfitSharingMerRelResponseItemDTO> list;
}
