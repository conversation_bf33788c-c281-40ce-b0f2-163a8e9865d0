package com.ecommerce.open.api.dto.gnete;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class QueryProfitSharingMerRelResponseItemDTO implements Serializable {


    @Serial
    private static final long serialVersionUID = 4133895447420703447L;
    @Schema(description = "分账方关联ID\t20N\tO\t分账方关联的唯一标识")
    private String id;
    @Schema(description = "商户编号\t19N\tO")
    private String merNo;
    @Schema(description = "商户名称\t50X\tO")
    private String merName;
    @Schema(description = "平台钱包ID")
    private String platformWalletId;
    @Schema(description = "业务类型\t2N\tO\t12-好支付，04-担保支付")
    private String bizType;
    @Schema(description = "\t关联商户名称\t50X\tO")
    private String relMerName;
    @Schema(description = "关联商户钱包ID\t19N\tO")
    private String relMerWalletId;
    @Schema(description = "关联商户结算账户钱包ID\t19N")
    private String relSettAcctWalletId;
    @Schema(description = "状态\t2X\tO\t01新建（待提交）、02提交（待初审）、03生效、04失效")
    private String status;
    @Schema(description = "审批状态\t2X\tO\t01新建（待提交）、02提交（待初审）、03初审不通过、04待复审、05生效、06失效提交（待初审）、07失效初审不通过、08失效待复审、09失效")
    private String approveStatus;
    @Schema(description = "生效时间\t\tO\tyyyy-MM-dd HH:mm:ss")
    private String enableDatetime;
    @Schema(description = "失效时间\t\tO\tyyyy-MM-dd HH:mm:ss")
    private String disableDatetime;
    @Schema(description = "备注\t250X\tO")
    private String remark;

}
