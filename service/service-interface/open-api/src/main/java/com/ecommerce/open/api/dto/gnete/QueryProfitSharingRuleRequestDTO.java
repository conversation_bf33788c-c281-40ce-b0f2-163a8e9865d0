package com.ecommerce.open.api.dto.gnete;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;

@Data
public class QueryProfitSharingRuleRequestDTO implements IBusinessRequestDTO {
    @Serial
    private static final long serialVersionUID = -5308056877147881717L;

    @Schema(description = "分账规则id\t20N\tO\t该记录的唯一标识")
    private String id;
    @Schema(description = "商户号\t19N\tO\t")
    private String merNo;
    @Schema(description = "平台钱包ID")
    private String platformWalletId;
    @Schema(description = "业务类型\t2N\tO\t030002-好支付、030001-担保支付")
    private String bizType;
    @Schema(description = "状态\t2X\tO\t01新建（待提交）、02提交（待初审）、03生效、04失效")
    private String status;

}
