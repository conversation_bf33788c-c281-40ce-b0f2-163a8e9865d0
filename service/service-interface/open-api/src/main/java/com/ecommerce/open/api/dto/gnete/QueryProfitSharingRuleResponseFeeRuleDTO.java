package com.ecommerce.open.api.dto.gnete;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class QueryProfitSharingRuleResponseFeeRuleDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = -1005531814070122671L;
    @Schema(description = "分账清算规则id\t20N\tO")
    private String id;
    @Schema(description = "分账规则id\t20N\tO")
    private String profitSharingRuleId;
    @Schema(description = "分账钱包ID\t19N\tO")
    private String profitSharingWalletId;
    @Schema(description = "手续费比例\t20N\tO\t分账方式为01按比例分账，该字段才有意义")
    private String feeRate;
    @Schema(description = "单笔金额上限\t20N\tO\t单位：分，分账方式为01按比例分账，该字段才有意义")
    private Long rateMinFeeAmt;
    @Schema(description = "单笔金额下限\t20N\tO\t单位：分，分账方式为01按比例分账，该字段才有意义")
    private Long rateMaxFeeAmt;
    //文档写的是这样
    @Schema(description = "固定清算金额\t20N\tO\t单位：分，分账方式为02按金额分账，该字段才有意义")
    private Long fixedSettAmt;
    //文档不准确，可能是这样
    @Schema(description = "固定清算金额\t20N\tO\t单位：分，分账方式为02按金额分账，该字段才有意义")
    private Long fixedFeeAmt;
    @Schema(description = "手续费方式\t2X\tO\t01按比例分账、02按金额分账")
    private String feeRuleType;

}
