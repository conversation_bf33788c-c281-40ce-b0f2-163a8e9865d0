package com.ecommerce.open.api.dto.gnete;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Data
public class QueryProfitSharingRuleResponseRuleDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = -5486701975329326470L;
    @Schema(description = "分账规则ID\t20N\tO\t分账规则的唯一标识")
    private String id;
    @Schema(description = "商户编号\t19N\tO")
    private String merNo;
    @Schema(description = "商户名称\t50X\tO")
    private String merName;
    @Schema(description = "商户钱包ID\t19N\tO")
    private String platformWalletId;
    @Schema(description = "业务类型\t2N\tO\t12-好支付，04-担保支付")
    private String bizType;
    @Schema(description = "分账起始金额\t20N\tO\t单位：分")
    private Long startAmt;
    @Schema(description = "状态\t2X\tO\t01新建（待提交）、02提交（待初审）、03生效、04失效")
    private String status;
    @Schema(description = "审批状态\t2X\tO\t01新建（待提交）、02提交（待初审）、03初审不通过、04待复审、05生效、06失效提交（待初审）、07失效初审不通过、08失效待复审、09失效")
    private String approveStatus;
    @Schema(description = "分账资金清算周期\t6N\tO\tT+N日")
    private String settCyc;
    @Schema(description = "是否指令分账\t1X\tO\t0否（默认）、1是")
    private String isInstr;
    @Schema(description = "分账规则")
    private List<QueryProfitSharingRuleResponseSettRuleDTO> settRuleList;
    @Schema(description = "分账手续费规则")
    private List<QueryProfitSharingRuleResponseFeeRuleDTO> feeRuleList;
}
