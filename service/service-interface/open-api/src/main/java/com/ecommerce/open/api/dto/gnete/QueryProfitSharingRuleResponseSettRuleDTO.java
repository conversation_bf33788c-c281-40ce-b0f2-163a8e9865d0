package com.ecommerce.open.api.dto.gnete;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class QueryProfitSharingRuleResponseSettRuleDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = -5486701975329326470L;
    @Schema(description = "分账清算规则id\t20N\tO")
    private String id;
    @Schema(description = "分账规则id\t20N\tO")
    private String profitSharingRuleId;
    @Schema(description = "是否默认清算账户\t2X\tO\t0否、1是")
    private String isSettWalletId;
    @Schema(description = "分账钱包ID\t19N\tO")
    private String profitSharingWalletId;
    @Schema(description = "清算比例\t20N\tO\t分账方式为01按比例分账，该字段才有意义")
    private Long settRate;
    @Schema(description = "单笔金额上限\t20N\tO\t单位：分，分账方式为01按比例分账，该字段才有意义")
    private Long rateMinSettAmt;
    @Schema(description = "单笔金额下限\t20N\tO\t单位：分，分账方式为01按比例分账，该字段才有意义")
    private Long rateMaxSettAmt;
    @Schema(description = "固定清算金额\t20N\tO\t单位：分，分账方式为02按金额分账，该字段才有意义")
    private Long fixedSettAmt;
    @Schema(description = "清算方式\t2X\tO\t01按比例分账、02按金额分账")
    private String settRuleType;
    @Schema(description = "备注\t200X\tO")
    private String remark;
}
