package com.ecommerce.open.api.dto.gnete;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.util.List;

@Data
public class QueryTransResultResponseDTO implements IBusinessResponseDTO {
    @Serial
    private static final long serialVersionUID = -2675578496328544004L;

    @Schema(description = "应答码")
    private String rspCode;
    @Schema(description = "应答描述")
    private String rspResult;

    @Schema(description = "记录总数")
    private Long total;

    private List<QueryTransResultResponseItemDTO> rowList;

}
