package com.ecommerce.open.api.dto.gnete;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class QueryTransResultResponseItemDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 6434170712730937597L;
    @Schema(description = "交易订单号")
    private String transOrderNo;
    @Schema(description = "钱包ID")
    private Long walletId;
    @Schema(description = "转账交易的转入钱包ID(如果是转账，则表示转入钱包ID)")
    private Long otherWalletId;
    @Schema(description = "交易日期(yyyyMMdd)")
    private Integer transDate;
    @Schema(description = "交易时间(yyyyMMddHHmmss)")
    private Integer transTime;
    @Schema(description = "交易类型编码")
    private String transType;
    @Schema(description = "交易类型名称")
    private String transTypeName;
    @Schema(description = "商户号")
    private String merNo;
    @Schema(description = "商户简称")
    private String merName;
    @Schema(description = "交易金额(金额单位：分)")
    private String transAmt;
    @Schema(description = "清算金额(金额单位：分)")
    private Long settAmt;
    @Schema(description = "清算日期yyyyMMdd((未明确结果的交易，清算日为空))")
    private String settDate;
    @Schema(description = """
            处理状态( 0：已接收，
             1：成功，
             2：失败，
             3：已冲正，
             4：已撤销，
             5：处理中。)""")
    private String procStatus;
    @Schema(description = "状态描述")
    private String procStatusDscrb;
    @Schema(description = "处理结果描述(结果描述，失败原因等。)")
    private String procResultDscrb;
    @Schema(description = "调整标记(0：未调整，1：已全部退款，2：已部分退款)")
    private String adjustFlag;
    @Schema(description = "商户订单号(客户端唯一标记该笔交易)")
    private String mctOrderNo;
    @Schema(description = "批次号")
    private String regId;
    @Schema(description = "批次明细号")
    private String regDtlSn;
    @Schema(description = "付款码")
    private String payCode;
    @Schema(description = "摘要")
    private String abst;
    @Schema(description = "备注")
    private String remark;
    @Schema(description = "资金计划项目ID")
    private String cptlPlnPrjctId;
    @Schema(description = "资金计划项目编号")
    private String cptlPlnPrjctCode;
    @Schema(description = "资金计划项目名称")
    private String cptlPlnPrjctName;

}
