package com.ecommerce.open.api.dto.gnete;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.util.List;

@Data
public class QueryWithholdGrantRequestDTO implements IBusinessRequestDTO {


    @Serial
    private static final long serialVersionUID = 6923211237073199069L;

    @Schema(description = "被授权钱包ID列表\t19N列表\tC")
    private List<Long> grantWalletIdList;
    @Schema(description = "被授权钱包名称\t64G\tO\t（模糊查询）")
    private String grantWalletName;
    @Schema(description = "\t授权钱包ID列表\t19N列表\tC")
    private List<Long> walletIdList;
    @Schema(description = "授权钱包名称\t64G\tO\t（模糊查询）")
    private String walletName;
    @Schema(description = "电子协议编号\t64X\tC")
    private String protocolNo;
    @Schema(description = "授权状态\t1N\tO\t0取消授权，1已授权")
    private Integer grantStatus;
    @Schema(description = "授权操作时间（开始）\t16N\tO\tyyyy-MM-dd HH:mm:ss")
    private String grantOprtTimeStart;
    @Schema(description = "授权操作时间（结束）\t16N\tO\tyyyy-MM-dd HH:mm:ss")
    private String grantOprtTimeEnd;
    @Schema(description = "分页类型\t1N\tM\t0分页 1不分页（返回所有查询结果）")
    private Integer pageType=0;
    @Schema(description = "\t第几页\t6N\tC\t如果分页，此字段必填")
    private Integer pageNumber;
    @Schema(description = "每页显示数\t6N\tC\t如果分页，此字段必填")
    private Integer pageSize;

}
