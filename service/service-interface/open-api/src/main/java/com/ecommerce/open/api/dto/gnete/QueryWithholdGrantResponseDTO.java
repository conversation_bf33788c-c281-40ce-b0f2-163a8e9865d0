package com.ecommerce.open.api.dto.gnete;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.util.List;

@Data
public class QueryWithholdGrantResponseDTO implements IBusinessResponseDTO {

    @Serial
    private static final long serialVersionUID = 7656288230041641669L;
    @Schema(description = "应答码")
    private String rspCode;
    @Schema(description = "应答描述")
    private String rspResult;
    @Schema(description = "总条数\t6N\tM\t按查询条件查询到所有记录的总数")
    private Integer totalSize;
    @Schema(description = "第几页\t6N\tC\t如果分页，此字段必填")
    private Integer pageNumber;
    @Schema(description = "每页显示数\t6N\tC")
    private Integer pageSize;
    @Schema(description = "当前返回集合的元素数量\t6N\tC\tlist的size大小")
    private Integer contentSize;
    @Schema(description = "授权信息列表")
    private List<QueryWithholdGrantResponseItemDTO> list;

}
