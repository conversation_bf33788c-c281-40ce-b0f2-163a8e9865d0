package com.ecommerce.open.api.dto.gnete;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class QueryWithholdGrantResponseItemDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 3132009426213261683L;
    @Schema(description = "被授权钱包ID\t19N\tM")
    private Long grantWalletId;
    @Schema(description = "被授权钱包名称\t64G\tM")
    private String grantWalletName;
    @Schema(description = "授权钱包ID\t19N\tM")
    private Long walletId;
    @Schema(description = "授权钱包名称\t64G\tM")
    private String walletName;
    @Schema(description = "授权状态\t1N\tM\t0取消授权，1已授权")
    private Integer grantStatus;
    @Schema(description = "授权开始日期\t8N\tM\tyyyyMMdd。")
    private Integer grantBeginDate;
    @Schema(description = "授权结束日期\t8N\tM\tyyyyMMdd。")
    private Integer grantEndDate;
    @Schema(description = "电子协议编号\t64X\tM")
    private String protocolNo;
    @Schema(description = "电子协议名称\t64G\tM")
    private String protocolName;
    @Schema(description = "单笔最高金额\t15N\tM\t单位：分；")
    private Long limitAmt;
    @Schema(description = "汇总最高金额\t15N\tM\t单位：分；")
    private Long totalLimitAmt;
    @Schema(description = "已使用金额\t15\tM\t单位：分；")
    private Long usedAmt;
    @Schema(description = "授权操作时间\t16N\tC\tyyyy-MM-dd HH:mm:ss")
    private String grantOprtTime;
    @Schema(description = "取消授权操作时间\t16N\tC\tyyyy-MM-dd HH:mm:ss")
    private String cancelGrantOprtTime;

}
