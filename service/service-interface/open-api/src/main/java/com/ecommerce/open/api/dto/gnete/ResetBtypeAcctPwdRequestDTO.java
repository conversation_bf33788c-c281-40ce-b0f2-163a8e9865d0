package com.ecommerce.open.api.dto.gnete;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;

@Data
public class ResetBtypeAcctPwdRequestDTO implements IBusinessRequestDTO {

    @Serial
    private static final long serialVersionUID = 8346616524624056254L;
    @Schema(description = "商户订单号")
    private String mctOrderNo;
    @Schema(description = "钱包ID")
    private Long walletId;
    @Schema(description = "原支付密码\t1024X\tM\t密码使用encryptType指定的方式进行加密（最终密文是base64编码的字符串）。")
    private String encryptOrigPwd;
    @Schema(description = "新支付密码\t1024X\tM\t密码使用encryptType指定的方式进行加密（最终密文是base64编码的字符串）。")
    private String encryptNewPwd;
    @Schema(description = """
            加密类型(当IsActive（是否激活）为1时，该字段才有意义。
                支付密码加密类型：
                        1：H5密码键盘加密（密码键盘先使用公钥加密，然后自身再加密）。
                        2：非H5加密。（加密控件先使用公钥加密，然后控件自身再加密）。)""")
    private Integer encryptType;
    @Schema(description = "控件随机因子(加密随机因子，通过“获取控件随机因子”接口获取，有效期为24小时。)")
    private String plugRandomKey;
    @Schema(description = "法人代表姓名\t64G\tM\t与注册资料保持一致")
    private String legalName;
    @Schema(description = "法人代表身份证号码\t18X\tM\t与注册资料保持一致")
    private String legalIdCard;
    @Schema(description = "法人代表手机号码\t16N\tM\t可以和注册时的手机号不一致，但手机号必须为法人代表本人手机号")
    private Long legalPhoneNum;
    @Schema(description = "法人代表短信验证码\t6N\tM")
    private Long legalSmsAuthCode;
    @Schema(description = "代理人姓名\t64G\tC\t注册时申请人类型为“02-代理人”时必填 与注册资料保持一致")
    private String agentName;
    @Schema(description = "代理人身份证号码\t18X\tC\t注册时申请人类型为“02-代理人”时必填 与注册资料保持一致")
    private String agentIdCard;
    @Schema(description = "代理人手机号\t16N\tC\t注册时申请人类型为“02-代理人”时必填 可以和注册时的手机号不一致，但手机号必须为代理人本人手机号")
    private Long agentPhoneNum;
    @Schema(description = "代理人短信验证码\t6N\tC")
    private Long agentSmsAuthCode;
    @Schema(description = "备注\t128G\tO")
    private String remark;

}
