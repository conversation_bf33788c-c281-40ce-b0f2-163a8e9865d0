package com.ecommerce.open.api.dto.gnete;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;

@Data
public class ResetBtypeAcctPwdResponseDTO implements IBusinessResponseDTO{

    @Serial
    private static final long serialVersionUID = -5496210939940721872L;
        @Schema(description = "应答码")
        private String rspCode;
        @Schema(description = "应答描述")
        private String rspResult;
        @Schema(description = "交易订单号\t64X\tM")
        private String transOrderNo;

}
