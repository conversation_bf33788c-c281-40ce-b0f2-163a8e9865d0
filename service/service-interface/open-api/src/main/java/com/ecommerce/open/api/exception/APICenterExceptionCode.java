package com.ecommerce.open.api.exception;

import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.CodeMeta;

public class APICenterExceptionCode extends BasicCode {
	public static final CodeMeta CLIENT_DATA_NULL = new CodeMeta("EC2010001", "CLIENT_DATA_NULL",
			"L2 验证client 端数据验证未通过，{} 为空");

	public static final CodeMeta SERVER_RESPONSE_NULL = new CodeMeta("EC2020002", "SERVER_RESPONSE_NULL",
			"L2 接收到的服务端reponse为空");

	public static final CodeMeta CONFIG_NULL = new CodeMeta("EC2020001", "CONFIG_NULL", "L2 服务未配置   {} ");

	public static final CodeMeta DATA_NOT_VALIDATE = new CodeMeta("EC2010002", "DATA_NOT_VALIDATE", "请求数据非法 {}");

	public static final CodeMeta SERVER_RESPONSE_ERROR = new CodeMeta("EC2030002", "SERVER_RESPONSE_ERROR",
			"服务器响应解析失败 {}");

	public static final CodeMeta NETWORK_ERROR = new CodeMeta("EC20NET01", "NETWORK_ERROR", "ERP 调用失败： {}");

	public static final CodeMeta ERP_NETWORK_ERROR = new CodeMeta("EC2050001", "ERP_NETWORK_ERROR", "发送数据异常： {}");

	public static final CodeMeta RESPONSE_PARSE_ERROR = new CodeMeta("EC2060001", "RESPONSE_PARSE_ERROR",
			"服务器响应报文解析失败： {}");

}
