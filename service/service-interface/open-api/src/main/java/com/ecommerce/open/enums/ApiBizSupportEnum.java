package com.ecommerce.open.enums;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.ecommerce.common.utils.CsStringUtils;

public enum ApiBizSupportEnum {

	NOT_SUPPORT_EXCEPTION("NOT_SUPPORT_EXCEPTION", "不支持并抛出异常"), NOT_SUPPORT_IGNORE("NOT_SUPPORT",
			"不支持并忽略"), SUPPORT("SUPPORT", "支持"), NOT_SUPPORT_SUCCESS("NOT_SUPPORT_SUCCESS", "不支持并直接回调成功");

	private String code;

	private String message;

	ApiBizSupportEnum(String code, String message) {
		this.code = code;
		this.message = message;
	}

	public static ApiBizSupportEnum getByCode(String code) {
		for (ApiBizSupportEnum _enum : values()) {
            if (CsStringUtils.equals(code, _enum.code)) {
				return _enum;
			}
		}
		return null;
	}

	public static List<String> getAllCode() {
		return Stream.of(values()).map(ApiBizSupportEnum::code).toList();
	}

	public String code() {
		return code;
	}

	public String message() {
		return message;
	}

	public String getCode() {
		return code;
	}

	public String getMessage() {
		return message;
	}

}
