package com.ecommerce.open.enums;

import com.ecommerce.common.utils.CsStringUtils;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 接口方向:EC ERP
 *  ApiDirectEnum
 *
 * <AUTHOR>
 */
public enum ApiDirectEnum {

    EC_TO_ERP("EC_TO_ERP", "电商到外部"),

    ERP_TO_EC("ERP_TO_EC", "外部到电商")
    ;

    private String code;

    private String message;

    ApiDirectEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }
    public static ApiDirectEnum getByCode(String code) {
        for (ApiDirectEnum _enum : values()) {
            if (CsStringUtils.equals(code, _enum.code)) {
                return _enum;
            }
        }
        return null;
    }


    public static List<String> getAllCode() {
        return Stream.of(values()).map(ApiDirectEnum::code).toList();
    }


    public String code() {
        return code;
    }
    public String message() {
        return message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
    
}
