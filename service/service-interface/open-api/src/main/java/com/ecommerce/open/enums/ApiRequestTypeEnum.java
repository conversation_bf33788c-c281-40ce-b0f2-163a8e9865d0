package com.ecommerce.open.enums;

import com.ecommerce.common.utils.CsStringUtils;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 接口类型:同步异步
 *  ApiRequestTypeEnum
 *
 * <AUTHOR>
 */
public enum ApiRequestTypeEnum {

    REQUEST("REQUEST", "请求"),

    QUERY("QUERY", "查询"),

    CALLBACK("CALLBACK", "回调")
    ;

    private String code;

    private String message;

    ApiRequestTypeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }
    public static ApiRequestTypeEnum getByCode(String code) {
        for (ApiRequestTypeEnum _enum : values()) {
            if (CsStringUtils.equals(code, _enum.code)) {
                return _enum;
            }
        }
        return null;
    }


    public static List<String> getAllCode() {
        return Stream.of(values()).map(ApiRequestTypeEnum::code).toList();
    }


    public String code() {
        return code;
    }
    public String message() {
        return message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
    
}
