package com.ecommerce.open.enums;

import com.ecommerce.common.utils.CsStringUtils;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 接口类型:同步异步
 *  ApiTypeEnum
 *
 * <AUTHOR>
 */
public enum ApiTypeEnum {

    SYN("SYN", "同步"),

    ASYN("ASYN", "异步")
    ;

    private String code;

    private String message;

    ApiTypeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }
    public static ApiTypeEnum getByCode(String code) {
        for (ApiTypeEnum _enum : values()) {
            if (CsStringUtils.equals(code, _enum.code)) {
                return _enum;
            }
        }
        return null;
    }


    public static List<String> getAllCode() {
        return Stream.of(values()).map(ApiTypeEnum::code).toList();
    }


    public String code() {
        return code;
    }
    public String message() {
        return message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
    
}
