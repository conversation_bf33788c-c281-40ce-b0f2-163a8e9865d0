package com.ecommerce.open.enums;

import com.ecommerce.common.utils.CsStringUtils;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 *  BizCodeEnum
 *
 * <AUTHOR>
 */
public enum BizCodeEnum {

    EC_MEM_D0("EC-MEM-D0", "会员信息查询"),
    EC_MEM_A1("EC-MEM-A1", "会员注册变更通知"),
    EC_MEM_A2("EC-MEM-A2", "会员注册变更结果反馈"),
    EC_MEM_A3("EC-MEM-A3", "会员注册变更处理情况查询"),
    EC_GDS_C0("EC-GDS-C0", "物料属性查询"),
    EC_GDS_D0("EC-GDS-D0", "提货点信息查询"),
    EC_DEP_Q0("EC-DEP-Q0", "提货点信息查询"),
    EC_GDS_F0("EC-GDS-F0", "组织机构信息查询"),
    EC_CTS_C0("EC-CTS-C0", "查询合同"),
    EC_CTS_C2("EC-CTS-C2", "ERP系统合同信息变更后通知电商"),
    EC_FAP_A0("EC-FAP-A0", "客户余额（信用）信息查询"),
    EC_PLA_C0("EC-PLA-C0", "创建提货计划（把金额要传ERP，包含电商在线支付成功通知）"),
    EC_PLA_C1("EC-PLA-C1", "创建提货计划处理结果反馈"),
    EC_PLA_C2("EC-PLA-C2", "创建提货计划处理结果查询"),
    EC_PLA_D0("EC-PLA-D0", "作废提货计划"),
    EC_PLA_D1("EC-PLA-D1", "作废提货计划处理结果反馈"),
    EC_PLA_D2("EC-PLA-D2", "作废提货计划处理结果查询"),
    EC_PLA_F0("EC-PLA-F0", "完成提货计划"),
    EC_PLA_F1("EC-PLA-F1", "完成提货计划处理结果反馈"),
    EC_PLA_F2("EC-PLA-F2", "完成提货计划处理结果查询"),
    EC_PLA_N0("EC-PLA-N0", "ERP作废提货计划"),
    EC_PLA_N1("EC-PLA-N1", "电商反馈作废提货计划处理结果"),
    EC_PLA_N2("EC-PLA-N2", "ERP查询电商作废提货计划处理结果"),
    EC_PLA_K0("EC-PLA-K0", "ERP完成提货计划"),
    EC_PLA_K1("EC-PLA-K1", "电商反馈提货计划完成结果"),
    EC_PLA_K2("EC-PLA-K2", "ERP查询电商提货计划完成结果"),
    EC_REQ_C0("EC-REQ-C0","配送需求下发"),
    EC_REQ_D0("EC-REQ-D0","配送需求关闭"),
    EC_LOG_U0("EC-LOG-U0", "运单信息上传"),
    EC_LOG_U1("EC-LOG-U1", "运单信息上传处理结果反馈"),
    EC_LOG_U2("EC-LOG-U2", "运单信息上传处理结果查询"),
    EC_LOG_E0("EC-LOG-E0", "运单信息修改-ERP发起"),
    EC_LOG_E1("EC-LOG-E1", "运单信息修改处理结果反馈"),
    EC_LOG_E2("EC-LOG-E2", "运单信息修改处理结果查询"),
    EC_LOG_W0("EC-LOG-W0", "运单信息作废-ERP发起"),
    EC_LOG_W1("EC-LOG-W1", "运单信息作废处理结果反馈"),
    EC_LOG_W2("EC-LOG-W2", "运单信息作废处理结果查询"),
    EC_LOG_B0("EC-LOG-B0", "运单信息下发"),
    EC_LOG_B1("EC-LOG-B1", "运单信息下发反馈"),
    EC_LOG_B2("EC-LOG-B2", "运单信息下发处理结果查询"),
    EC_LOG_S0("EC-LOG-S0", "运单信息修改-电商发起"),
    EC_LOG_S1("EC-LOG-S1", "运单信息修改处理结果反馈"),
    EC_LOG_S2("EC-LOG-S2", "运单信息修改处理结果查询"),
    EC_LOG_Z0("EC-LOG-Z0", "运单作废-电商发起"),
    EC_LOG_Z1("EC-LOG-Z1", "运单作废处理结果反馈"),
    EC_LOG_Z2("EC-LOG-Z2", "运单作废处理结果查询"),
    EC_LOG_T0("EC-LOG-T0", "运单信息查询-ERP发起"),
    EC_LOG_T1("EC-LOG-T1", "运单信息查询-电商发起"),
    EC_LOG_F0("EC-LOG-F0", "ERP上传运单执行情况"),
    EC_LOG_F1("EC-LOG-F1", "EC反馈运单执行情况上传结果（包含通知资金划转结果）"),
    EC_LOG_Q0("EC-LOG-Q0", "超发验证"),
    EC_LOG_Q1("EC-LOG-Q1", "超发验证结果反馈"),
    EC_LOG_L0("EC-LOG-L0", "排队情况查询"),
    EC_QOG_K0("EC-QOG-K0", "运单开单-电商发起"),
    EC_QOG_C0("EC-QOG-C0", "运单发货-电商发起"),
    EC_QOG_Q0("EC-QOG-Q0", "运单发货-电商发起的ERP反馈"),
    EC_MON_T0("EC-MON-T0", "卖家提现通知"),
    EC_MON_T1("EC-MON-T1", "ERP反馈卖家提现通知处理结果"),
    EC_LOG_K0("EC-LOG-K0", "车辆轨迹信息传输"),
    EC_PUR_A0("EC-PUR-A0", "采购需求推送"),
    EC_HDD_S0("EC-HDD-S0", "加价项查询"),
    EC_HOG_E0("EC-HOG-E0", "电商签收交货单"),
    EC_HTB_C0("EC-HTB-C0","电商确认订单金额"),
    EC_HOG_Z0("EC-HOG-Z0", "交货单信息作废-电商发起"),
    EC_HOG_Z1("EC-HOG-Z1", "交货单信息作废-结果反馈"),
    EC_HLA_E0("EC-HLA-E0", "ERP关闭订单"),
    EC_CTS_C1("EC-CTS-C1", "查询合同列表"),
    EC_PLA_Y0("EC-PLA-Y0", "提货计划延期"),
    EC_SHP_M0("EC-SHP-M0", "ERP码头信息查询"),
    EC_SHP_L0("EC-SHP-L0", "电商同步ERP承运商信息"),
    EC_SHP_N0("EC-SHP-N0", "电商同步ERP承运商船舶信息"),
    EC_DEP_Q1("EC-DEP-Q1", "查询ERP运输路线信息"),
    EC_SHP_B0("EC-SHP-B0", "船运计划信息下发"),
    EC_SHP_B1("EC-SHP-B1", "船运计划信息下发反馈"),
    EC_SHP_S0("EC-SHP-S0", "船运计划信息修改-电商发起"),
    EC_SHP_S1("EC-SHP-S1", "船运计划信息修改处理结果反馈"),
    EC_SHP_Z0("EC-SHP-Z0", "船运计划作废-电商发起"),
    EC_SHP_Z1("EC-SHP-Z1", "船运计划作废处理结果反馈"),
    EC_SHP_F0("EC-SHP-F0", "ERP上传船运单调度信息"),
    EC_SHP_Q0("EC-SHP-Q0", "ERP通知电商扣款"),
    EC_SHP_G0("EC-SHP-G0", "电商通知ERP开仓卸货"),
    EC_SHP_G1("EC-SHP-G1", "ERP反馈开仓卸货结果"),
    EC_DEP_Q2("EC-DEP-Q2", "创建客户卸货地址"),
    EC_PRI_A1("EC-PRI-A1", "电商价格调整（事前调价）"),
    EC_PRI_A2("EC-PRI-A2", "电商价格调整（事前调价）反馈"),
    EC_PRI_B1("EC-PRI-B1", "电商价格调整（事后调价）"),
    EC_PRI_B2("EC-PRI-B2", "电商价格调整（事后调价）结果反馈"),
    EC_PRI_C1("EC-PRI-C1", "运单退货"),
    EC_PRI_C2("EC-PRI-C2", "运单退货结果反馈"),
    EC_HLA_F0("EC-HLA-F0", "电商通知ERP订单调价"),
    EC_HLA_F1("EC-HLA-F1", "订单调价结果反馈"),
    ;

    private String code;

    private String message;

    BizCodeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }
    public static BizCodeEnum getByCode(String code) {
        for (BizCodeEnum _enum : values()) {
            if (CsStringUtils.equals(code, _enum.code)) {
                return _enum;
            }
        }
        return null;
    }


    public static List<String> getAllCode() {
        return Stream.of(values()).map(BizCodeEnum::code).toList();
    }


    public String code() {
        return code;
    }
    public String message() {
        return message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

}
