package com.ecommerce.open.enums;

import com.ecommerce.common.utils.CsStringUtils;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 回调状态的枚举
 *  CallbackStatusEnum
 *
 * <AUTHOR>
 */
public enum CallbackStatusEnum {

    WAIT_CALLBACK("00", "未回调"),

    CALLBACK("01", "已回调")
    ;

    private String code;

    private String message;

    CallbackStatusEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }
    public static CallbackStatusEnum getByCode(String code) {
        for (CallbackStatusEnum _enum : values()) {
            if (CsStringUtils.equals(code, _enum.code)) {
                return _enum;
            }
        }
        return null;
    }


    public static List<String> getAllCode() {
        return Stream.of(values()).map(CallbackStatusEnum::code).toList();
    }


    public String code() {
        return code;
    }
    public String message() {
        return message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
