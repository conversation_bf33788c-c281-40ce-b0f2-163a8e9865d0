package com.ecommerce.open.enums;

import com.ecommerce.common.utils.CsStringUtils;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 *  DefaultErpCodeEnum
 *
 * <AUTHOR>
 */
public enum DefaultErpCodeEnum {

    DEFAULT_ERP_MEMBER_CODE("DEFAULT_ERP_MEMBER_CODE", "ERP会员默认返回编码"),

    DEFAULT_ERP_ORDER_CODE("DEFAULT_ERP_ORDER_CODE", "ERP订单默认返回编码"),

    DEFAULT_ERP_WAYBILL_CODE("DEFAULT_ERP_WAYBILL_CODE", "ERP运单默认返回编码");

    private String code;

    private String message;

    DefaultErpCodeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }
    public static DefaultErpCodeEnum getByCode(String code) {
        for (DefaultErpCodeEnum _enum : values()) {
            if (CsStringUtils.equals(code, _enum.code)) {
                return _enum;
            }
        }
        return null;
    }

    public static List<String> getAllCode() {
        return Stream.of(values()).map(DefaultErpCodeEnum::code).toList();
    }


    public String code() {
        return code;
    }
    public String message() {
        return message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
    
}
