package com.ecommerce.open.enums;

/**
 * 00：创建中，01：创建完成，02：创建失败，03执行中，04关闭中，05已关闭，06关闭失败，07已失效，08完成中，09已完成
 /**
 * OrderStatusEnum
 *
 * <AUTHOR>
 */
public enum ERPOrderStatusEnum {
	IN_CREATE("00", "创建中"),
    CREATE_COMPLETE("01", "创建完成"),
    CREATE_FAIL("02", "创建失败"),
    IN_EXECUTE("03", "执行中"),
    IN_CLOSE("04", "关闭中"),
    CLOSE_COMPLETE("05", "已关闭"),
    CLOSE_FAIL("06", "关闭失败"),
    OUT_OF_TIME("07", "已失效"),
    IN_COMPLETE("08", "完成中"),
    COMPLETED("09", "已完成"),
	;

    /** 枚举值 */
    private final String code;

    /** 枚举描述 */
    private final String message;


    /**
     * 构造一个<code>ResourceStatusEnum</code>枚举对象
     *
     * @param code
     * @param message
     */
    private ERPOrderStatusEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * @return Returns the code.
     */
    public String getCode() {
        return code;
    }

    /**
     * @return Returns the message.
     */
    public String getMessage() {
        return message;
    }

    /**
     * @return Returns the code.
     */
    public String code() {
        return code;
    }

    /**
     * @return Returns the message.
     */
    public String message() {
        return message;
    }


}
