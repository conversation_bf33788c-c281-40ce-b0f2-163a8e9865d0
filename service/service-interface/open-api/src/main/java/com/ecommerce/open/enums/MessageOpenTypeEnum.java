package com.ecommerce.open.enums;

/**
 * <AUTHOR>
 * @Description:
 * @Date: Create in 上午10:34 18/12/12
 */
public enum MessageOpenTypeEnum {

    GO_APP("go_app", "打开应用"),

    GO_URL("go_url", "跳转到URL"),

    GO_ACTIVITY("go_activity", "打开特定的activity"),

    GO_CUSTOM("go_custom", "用户自定义内容");

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String desc;

    MessageOpenTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static MessageOpenTypeEnum valueOfCode(String code) {
        MessageOpenTypeEnum[] enums = values();
        for (MessageOpenTypeEnum item : enums) {
            if (item.code.equals(code)) {
                return item;
            }
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}
