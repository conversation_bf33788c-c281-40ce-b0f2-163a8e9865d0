package com.ecommerce.open.enums;

/**
 * <AUTHOR>
 * Description:
 * Date: Create in 下午6:05 19/3/6
 */
public enum MessagePushAppNameEnum {

    BUYER_APP("buyerApp", "买家APP"),

    SELLER_APP("sellerApp", "卖家APP"),

    DRIVER_APP("driverApp", "司机APP");

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String desc;

    MessagePushAppNameEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static MessagePushAppNameEnum valueOfCode(String code) {
        MessagePushAppNameEnum[] enums = values();
        for (MessagePushAppNameEnum item : enums) {
            if (item.code.equals(code)) {
                return item;
            }
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}
