package com.ecommerce.open.enums;

/**
 * <AUTHOR>
 * Description:
 * Date: Create in 上午10:22 18/12/12
 */
public enum MessagePushTypeEnum {

    UNI_CAST("unicast", "单播，推送单个设备"),

    LIST_CAST("listcast", "列播，要求不超过500个device_token"),

    FILE_CAST("filecast", "文件播，多个device_token可通过文件形式批量发送"),

    BROAD_CAST("broadcast", "广播，向所有安装APP的用户推送"),

    GROUP_CAST("groupcast", "组播，按照filter筛选用户群, 请参照filter参数");

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String desc;

    MessagePushTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static MessagePushTypeEnum valueOfCode(String code) {
        MessagePushTypeEnum[] enums = values();
        for (MessagePushTypeEnum item : enums) {
            if (item.code.equals(code)) {
                return item;
            }
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}
