package com.ecommerce.open.enums;

/**
 * <AUTHOR>
 * @Description:
 * @Date: Create in 上午10:47 18/9/14
 */
public enum OpenMessageTypeEnum {

    ERP_SYNC_REQUEST("erp.sync.request", "erp异步请求");

    /**
     * 描述
     */
    private final String code;

    /**
     * 编码
     */
    private final String desc;

    OpenMessageTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static OpenMessageTypeEnum valueOfCode(String code) {
        OpenMessageTypeEnum[] enums = values();
        for (OpenMessageTypeEnum item : enums) {
            if (item.code.equals(code)) {
                return item;
            }
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}
