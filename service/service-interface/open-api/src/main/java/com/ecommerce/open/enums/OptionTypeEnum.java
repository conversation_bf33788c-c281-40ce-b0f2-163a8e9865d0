package com.ecommerce.open.enums;

import com.ecommerce.common.utils.CsStringUtils;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 操作类型的枚举
 *  OptionTypeEnum
 *
 * <AUTHOR>
 */
public enum OptionTypeEnum {

    CREATE("00", "创建"),

    MODIFY("01", "修改"),

    CLOSE("02", "关闭"),

    CANCEL("03", "取消"),

    EXECUTE("04", "执行"),

    SCHEDULE("05", "调度"),

    SETTLEMENT("06", "结算"),

    QUERY_INFO("07", "信息查询"),

    COMPLETE("08", "完成"),

    UPLOAD("09", "上报")
    ;

    private String code;

    private String message;

    OptionTypeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }
    public static OptionTypeEnum getByCode(String code) {
        for (OptionTypeEnum _enum : values()) {
            if (CsStringUtils.equals(code, _enum.code)) {
                return _enum;
            }
        }
        return null;
    }


    public static List<String> getAllCode() {
        return Stream.of(values()).map(OptionTypeEnum::code).toList();
    }


    public String code() {
        return code;
    }
    public String message() {
        return message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
