package com.ecommerce.open.enums;

import lombok.Getter;

@Getter
public enum ProcessResult {
	/**
	 * 处理成功
	 */
	SUCCESS("processSuccess", "业务处理成功"),

	/**
	 * 处理中
	 */
	PROCESSING("processing", "业务处理中"),

	/**
	 * 处理中
	 */
	NEEDRESEND("processNeedResend", "业务需要重发"),
	/**
	 * 处理失败
	 */
	FAIL("processFail", "业务处理失败"),
	/**
	 * 处理中
	 */
	CALLBACKPROCESSFAIL("callbackProcessFail", "业务处理端回调处理成功后，业务发起端处理失败");

	private String code;

	private String message;

	ProcessResult(String code, String message) {
		this.code = code;
		this.message = message;
	}

	public static String getMessageByCode(String code) {
		for (ProcessResult enums : values()) {
			if (enums.code.equals(code)) {
				return enums.message;
			}
		}
		return null;
	}
}
