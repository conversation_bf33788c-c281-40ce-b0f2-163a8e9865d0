package com.ecommerce.open.enums;

import com.ecommerce.common.utils.CsStringUtils;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 请求状态的枚举:
 * '00数据待补全，01待发送，02已发送成功，03已发送失败，03对方已处理成功，04对方已处理失败'
 *  RequestStatusEnum
 *
 * <AUTHOR>
 */
public enum RequestStatusEnum {

    DATA_WAIT_SUPP("requestVF", "请求数据验证失败"),

    WAIT_SEND("requestWaitSend", "请求待发送"),

    SEND_SUCCESS("requestSendSuccess", "请求发送成功"),

    SEND_FAIL("requestSendFail", "请求发送失败"),
    
    SEND_TIMEOUT("requestSendTimeOut", "请求发送超时"),

    RESPONSE_SUCCESS("responseParseSuccess", "响应处理成功"),

    RESPONSE_FAIL("responseParseFail", "响应处理失败")
    ;

    private String code;

    private String message;

    RequestStatusEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }
    public static RequestStatusEnum getByCode(String code) {
        for (RequestStatusEnum _enum : values()) {
            if (CsStringUtils.equals(code, _enum.code)) {
                return _enum;
            }
        }
        return null;
    }


    public static List<String> getAllCode() {
        return Stream.of(values()).map(RequestStatusEnum::code).toList();
    }


    public String code() {
        return code;
    }
    public String message() {
        return message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
