package com.ecommerce.open.enums;

import com.ecommerce.common.utils.CsStringUtils;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 路由记录状态
 *  RouteStatusEnum
 *
 * <AUTHOR>
 */
public enum RouteStatusEnum {

    ENABLE("ENABLE", "启用"),

    DISABLE("DISABLE", "禁用")
    ;

    private String code;

    private String message;

    RouteStatusEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }
    public static RouteStatusEnum getByCode(String code) {
        for (RouteStatusEnum _enum : values()) {
            if (CsStringUtils.equals(code, _enum.code)) {
                return _enum;
            }
        }
        return null;
    }


    public static List<String> getAllCode() {
        return Stream.of(values()).map(RouteStatusEnum::code).toList();
    }


    public String code() {
        return code;
    }
    public String message() {
        return message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
    
}
