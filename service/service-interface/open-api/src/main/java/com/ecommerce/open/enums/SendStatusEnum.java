package com.ecommerce.open.enums;

import com.ecommerce.common.utils.CsStringUtils;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 *  SendStatusEnum
 *
 * <AUTHOR>
 */
public enum SendStatusEnum {

    INIT("INIT", "初始化"),

    SEND_SUCCESS("SEND_SUCCESS", "发送成功"),

    SEND_FAIL("SEND_FAIL", "发送失败"),

    UNKNOWN("UNKNOWN", "发送失败")
    ;

    private String code;

    private String message;

    SendStatusEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }
    public static SendStatusEnum getByCode(String code) {
        for (SendStatusEnum _enum : values()) {
            if (CsStringUtils.equals(code, _enum.code)) {
                return _enum;
            }
        }
        return null;
    }


    public static List<String> getAllCode() {
        return Stream.of(values()).map(SendStatusEnum::code).toList();
    }


    public String code() {
        return code;
    }
    public String message() {
        return message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
    
}
