package com.ecommerce.open.enums;

import java.util.ArrayList;
import java.util.List;

/**
 * 提货时间段CODE
 /**
 * TakeTimeQuantumEnum
 *
 * <AUTHOR>
 */
public enum TakeTimeQuantumEnum {
    TTQ030020100("030020100", "凌晨(00:00-06:00)"),
    TTQ030020200("030020200", "上午(06:00-12:00)"),
    TTQ030020300("030020300", "下午(12:00-18:00)"),
    TTQ030020400("030020400", "晚上(18:00-24:00)"),
    TTQ030020500("030020500", "全天(00:00-24:00)"),
	TTQ030020101("030020101", "凌晨(00:00-00:30)"),
    TTQ030020102("030020102", "凌晨(00:30-01:00)"),
    TTQ030020103("030020103", "凌晨(01:00-01:30)"),
    TTQ030020104("030020104", "凌晨(01:30-02:00)"),
    TTQ030020105("030020105", "凌晨(02:00-02:30)"),
    TTQ030020106("030020106", "凌晨(02:30-03:00)"),
    TTQ030020107("030020107", "凌晨(03:00-03:30)"),
    TTQ030020108("030020108", "凌晨(03:30-04:00)"),
    TTQ030020109("030020109", "凌晨(04:00-04:30)"),
    TTQ030020110("030020110", "凌晨(04:30-05:00)"),
    TTQ030020111("030020111", "凌晨(05:00-05:30)"),
    TTQ030020112("030020112", "凌晨(05:30-06:00)"),
    TTQ030020201("030020201", "上午(06:00-06:30)"),
    TTQ030020202("030020202", "上午(06:30-07:00)"),
    TTQ030020203("030020203", "上午(07:00-07:30)"),
    TTQ030020204("030020204", "上午(07:30-08:00)"),
    TTQ030020205("030020205", "上午(08:00-08:30)"),
    TTQ030020206("030020206", "上午(08:30-09:00)"),
    TTQ030020207("030020207", "上午(09:00-09:30)"),
    TTQ030020208("030020208", "上午(09:30-10:00)"),
    TTQ030020209("030020209", "上午(10:00-10:30)"),
    TTQ030020210("030020210", "上午(10:30-11:00)"),
    TTQ030020211("030020211", "上午(11:00-11:30)"),
    TTQ030020212("030020212", "上午(11:30-12:00)"),
    TTQ030020301("030020301", "下午(12:00-12:30)"),
    TTQ030020302("030020302", "下午(12:30-13:00)"),
    TTQ030020303("030020303", "下午(13:00-13:30)"),
    TTQ030020304("030020304", "下午(13:30-14:00)"),
    TTQ030020305("030020305", "下午(14:00-14:30)"),
    TTQ030020306("030020306", "下午(14:30-15:00)"),
    TTQ030020307("030020307", "下午(15:00-15:30)"),
    TTQ030020308("030020308", "下午(15:30-16:00)"),
    TTQ030020309("030020309", "下午(16:00-16:30)"),
    TTQ030020310("030020310", "下午(16:30-17:00)"),
    TTQ030020311("030020311", "下午(17:00-17:30)"),
    TTQ030020312("030020312", "下午(17:30-18:00)"),
    TTQ030020401("030020401", "晚上(18:00-18:30)"),
    TTQ030020402("030020402", "晚上(18:30-19:00)"),
    TTQ030020403("030020403", "晚上(19:00-19:30)"),
    TTQ030020404("030020404", "晚上(19:30-20:00)"),
    TTQ030020405("030020405", "晚上(20:00-20:30)"),
    TTQ030020406("030020406", "晚上(20:30-21:00)"),
    TTQ030020407("030020407", "晚上(21:00-21:30)"),
    TTQ030020408("030020408", "晚上(21:30-22:00)"),
    TTQ030020409("030020409", "晚上(22:00-22:30)"),
    TTQ030020410("030020410", "晚上(22:30-23:00)"),
    TTQ030020411("030020411", "晚上(23:00-23:30)"),
    TTQ030020412("030020412", "晚上(23:30-24:00)");

    /** 枚举值 */
    private final String code;

    /** 枚举描述 */
    private final String message;


    /**
     * 构造一个<code>TakeTimeQuantumEnum</code>枚举对象
     *
     * @param code
     * @param message
     */
    private TakeTimeQuantumEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * @return Returns the code.
     */
    public String getCode() {
        return code;
    }

    /**
     * @return Returns the message.
     */
    public String getMessage() {
        return message;
    }

    /**
     * @return Returns the code.
     */
    public String code() {
        return code;
    }

    /**
     * @return Returns the message.
     */
    public String message() {
        return message;
    }

    /**
     * 通过枚举<code>code</code>获得枚举
     *
     * @param code
     * @return TakeTimeQuantumEnum
     */
    public static TakeTimeQuantumEnum getByCode(String code) {
        for (TakeTimeQuantumEnum _enum : values()) {
            if (_enum.getCode().equals(code)) {
                return _enum;
            }
        }
        return null;
    }

    /**
     * 获取全部枚举
     *
     * @return List<TakeTimeQuantumEnum>
     */
    public List<TakeTimeQuantumEnum> getAllEnum() {
        List<TakeTimeQuantumEnum> list = new ArrayList<TakeTimeQuantumEnum>();
        for (TakeTimeQuantumEnum _enum : values()) {
            list.add(_enum);
        }
        return list;
    }

    /**
     * 获取全部枚举值
     *
     * @return List<String>
     */
    public List<String> getAllEnumCode() {
        List<String> list = new ArrayList<String>();
        for (TakeTimeQuantumEnum _enum : values()) {
            list.add(_enum.code());
        }
        return list;
    }
}
