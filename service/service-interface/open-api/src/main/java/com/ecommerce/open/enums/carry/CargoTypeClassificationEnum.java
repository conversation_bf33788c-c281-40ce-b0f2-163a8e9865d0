package com.ecommerce.open.enums.carry;

import com.ecommerce.common.utils.CsStringUtils;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Auther: colu
 * @Date: 2020-05-29 16:12
 * @Description: CargoTypeClassificationEnum
 */
public enum CargoTypeClassificationEnum {

    CARGO_TYPE_100("100", "煤炭及制品"),
    CARGO_TYPE_200("200", "石油、天然气及制品"),
    CARGO_TYPE_300("300", "金属矿石"),
    CARGO_TYPE_400("400", "钢铁"),
    CARGO_TYPE_500("500", "矿建材料"),
    CARGO_TYPE_600("600", ""),
    CARGO_TYPE_700("700", "木材"),
    CARGO_TYPE_800("800", "非金属矿石"),
    CARGO_TYPE_900("900", "化肥及农药"),
    CARGO_TYPE_1000("1000", "盐"),
    CARGO_TYPE_1100("1100", "粮食"),
    CARGO_TYPE_1200("1200", "机械、设备、电器"),
    CARGO_TYPE_1300("1300", "轻工原料及制品"),
    CARGO_TYPE_1400("1400", "有色金属"),
    CARGO_TYPE_1500("1500", "轻工医药产品"),
    CARGO_TYPE_1601("1601", "鲜活农产品"),
    CARGO_TYPE_1602("1602", "冷藏冷冻货物"),
    CARGO_TYPE_1701("1701", "商品汽车"),
    CARGO_TYPE_1700("1700", "其他");

    private String code;

    private String message;

    CargoTypeClassificationEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }
    public static CargoTypeClassificationEnum getByCode(String code) {
        for (CargoTypeClassificationEnum _enum : values()) {
            if (CsStringUtils.equals(code, _enum.code)) {
                return _enum;
            }
        }
        return null;
    }


    public static List<String> getAllCode() {
        return Stream.of(values()).map(CargoTypeClassificationEnum::code).toList();
    }


    public String code() {
        return code;
    }
    public String message() {
        return message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

}
