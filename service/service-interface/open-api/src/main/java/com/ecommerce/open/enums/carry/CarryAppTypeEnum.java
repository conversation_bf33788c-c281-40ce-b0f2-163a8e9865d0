package com.ecommerce.open.enums.carry;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Auther: colu
 * @Date: 2020-06-01 14:51
 * @Description: CarryAppTypeEnum
 */
public enum CarryAppTypeEnum {

    ANDROID(1, "安卓"),
    
    IOS(2, "iOS");

    private Integer code;

    private String message;


    CarryAppTypeEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }
    public static CarryAppTypeEnum getByCode(Integer code) {
        for (CarryAppTypeEnum _enum : values()) {
            if (code.equals(_enum.code)) {
                return _enum;
            }
        }
        return null;
    }


    public static List<Integer> getAllCode() {
        return Stream.of(values()).map(CarryAppTypeEnum::code).toList();
    }


    public Integer code() {
        return code;
    }

    public String message() {
        return message;
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

}
