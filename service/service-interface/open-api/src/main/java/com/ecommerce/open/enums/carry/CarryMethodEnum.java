package com.ecommerce.open.enums.carry;

import com.ecommerce.common.utils.CsStringUtils;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Auther: colu
 * @Date: 2020-06-01 10:50
 * @Description: CarryMethodEnum
 */
public enum CarryMethodEnum {

    SUB_CARRIER("StShipperService.subCarrier", "提交网络货运企业信息接口"),

    SUB_WAYBILL("SyncStOrderService.add", "电子运单上传接口"),

    SUB_INTEGRITY("StShipperService.subIntegrity", "提交质量和信用数据接口"),

    SUB_CONTRACT("StShipperService.subMultiContract", "批量提交合同接口"),

    SUB_FINANCIAL("StFinancialService.subFinancial", "批量提交资金流水单接口"),

    SUB_VEHICLE("StVehicleService.subMultiVehicle", "批量提交车辆基本信息接口"),

    SUB_DRIVER("StDriverV2Service.subMultiDriver", "批量提交驾驶员基本信息接口")
    ;

    private String code;

    private String message;


    CarryMethodEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }
    public static CarryMethodEnum getByCode(String code) {
        for (CarryMethodEnum _enum : values()) {
            if (CsStringUtils.equals(code, _enum.code)) {
                return _enum;
            }
        }
        return null;
    }


    public static List<String> getAllCode() {
        return Stream.of(values()).map(CarryMethodEnum::code).toList();
    }


    public String code() {
        return code;
    }

    public String message() {
        return message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

}
