package com.ecommerce.open.enums.carry;

import com.ecommerce.common.utils.CsStringUtils;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Auther: colu
 * @Date: 2020-06-02 16:16
 * @Description: 无车承运人提交信息的状态
 */
public enum CarrySubmitStatusEnum {

    INIT("INIT", "初始化"),

    PROCESS("PROCESS", "处理中"),

    SUCCESS("SUCCESS", "成功"),

    FAIL("FAIL", "失败")

    ;

    private String code;

    private String message;

    CarrySubmitStatusEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }
    public static CarrySubmitStatusEnum getByCode(String code) {
        for (CarrySubmitStatusEnum _enum : values()) {
            if (CsStringUtils.equals(code, _enum.code)) {
                return _enum;
            }
        }
        return null;
    }


    public static List<String> getAllCode() {
        return Stream.of(values()).map(CarrySubmitStatusEnum::code).toList();
    }


    public String code() {
        return code;
    }
    public String message() {
        return message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
    
}
