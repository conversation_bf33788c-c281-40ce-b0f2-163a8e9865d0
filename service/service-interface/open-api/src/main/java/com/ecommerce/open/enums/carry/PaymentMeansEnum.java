package com.ecommerce.open.enums.carry;

import com.ecommerce.common.utils.CsStringUtils;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Auther: colu
 * @Date: 2020-05-29 16:29
 * @Description: PaymentMeansEnum
 */
public enum PaymentMeansEnum {

    PAYMENT_MEANS_1("1", "信用证"),
    PAYMENT_MEANS_2("2", "托收"),
    PAYMENT_MEANS_3("3", "汇付"),
    PAYMENT_MEANS_31("31", "银行汇票"),
    PAYMENT_MEANS_32("32", "银行转账"),
    PAYMENT_MEANS_4("4", "第三方支付平台"),
    PAYMENT_MEANS_41("41", "支付宝支付"),
    PAYMENT_MEANS_42("42", "微信支付"),
    PAYMENT_MEANS_9("9", "其他电子支付方式（不允许现金支付）");

    private String code;

    private String message;

    PaymentMeansEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }
    public static PaymentMeansEnum getByCode(String code) {
        for (PaymentMeansEnum _enum : values()) {
            if (CsStringUtils.equals(code, _enum.code)) {
                return _enum;
            }
        }
        return null;
    }


    public static List<String> getAllCode() {
        return Stream.of(values()).map(PaymentMeansEnum::code).toList();
    }


    public String code() {
        return code;
    }
    public String message() {
        return message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
    
}
