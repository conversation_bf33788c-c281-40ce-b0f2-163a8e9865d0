package com.ecommerce.open.enums.carry;

import com.ecommerce.common.utils.CsStringUtils;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Auther: colu
 * @Date: 2020-05-29 16:36
 * @Description: TransportTypeEnum
 */
public enum TransportTypeEnum {

    TRANSPORT_TYPE_1("1", "公路运输"),
    TRANSPORT_TYPE_2("2", "公铁联运"),
    TRANSPORT_TYPE_3("3", "公水联运"),
    TRANSPORT_TYPE_4("4", "公空联运"),
    TRANSPORT_TYPE_5("5", "公铁水联运"),
    TRANSPORT_TYPE_6("6", "公铁空联运"),
    TRANSPORT_TYPE_7("7", "公水空联运"),
    TRANSPORT_TYPE_8("8", "公铁水空联运");

    private String code;

    private String message;

    TransportTypeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }
    public static TransportTypeEnum getByCode(String code) {
        for (TransportTypeEnum _enum : values()) {
            if (CsStringUtils.equals(code, _enum.code)) {
                return _enum;
            }
        }
        return null;
    }


    public static List<String> getAllCode() {
        return Stream.of(values()).map(TransportTypeEnum::code).toList();
    }


    public String code() {
        return code;
    }
    public String message() {
        return message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
    
}
