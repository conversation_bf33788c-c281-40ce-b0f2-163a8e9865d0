package com.ecommerce.open.enums.pom.logistics;

/**
 * <AUTHOR>
 * @Description:
 * @Date: Create in 上午11:19 18/9/1
 */
public enum LogisticsValueSetEnum {

    CARRIER_TYPE("001", "承运商类型"),

    DELIVERY_TIME_RANGE("002", "配送时间范围"),

    DISPATCH_BILL_STATUS("003", "调度单状态"),

    PICKING_BILL_ASSIGN_MODE("004", "提货单指派模式"),

    PICKING_BILL_STATUS("005", "提货单状态"),

    PICKING_BILL_TYPE("006", "提货单类型"),

    WAYBILL_STATUS("007", "运单状态"),

    WAYBILL_TYPE("008", "运单类型"),

    VEHICLE_CERTIFICATION_STATUS("009", "车辆认证状态"),

    VEHICLE_TYPE_AXLES("010", "车型轴数"),

    VEHICLE_TYPE_CARRIAGE_TYPE("011", "车型车厢类型"),

    TRANSPORT_MODE("012", "运输方式"),

    PRODUCT_UNIT("013","商品单位"),

    WAREHOUSE_TYPE("014","仓库类型"),

    VEHICLE_LICENCE_PLATE_COLOR("015", "车牌颜色"),

    ATTACHMENT_TYPE("016", "附件类型"),

    PICKING_BILL_DATA_SOURCE("017", "提货单数据来源"),

    WAYBILL_OPERATION_TYPE("018", "运单操作类型"),

    METERING_UNIT("019", "计量单位类型"),

    TRANSPORT_UNIT("020", "运输单位类型"),

    TRANSPORT_CAPACITY_STATUS("021", "运力状态"),

    TRANSPORT_DEMAND_STATUS("022", "运输需求状态"),

    TRANSPORT_TOOL_TYPE("023", "运输工具类型"),

    TRANSPORT_ADDRESS_TYPE("024", "运输地址类型"),

    SHIPPING_TYPE("025", "船舶类型"),

    GPS_PROTOCOL_TYPE("026", "GPS协议类型"),

    CARRIAGE_SECTION_TYPE("027", "运价范围类型"),

    WAYBILL_EXTERNAL_STATUS("028", "运单外部状态"),

    EXTERNAL_SYNC_FLAG("029", "外部系统同步标识"),

    EXTERNAL_EXCEPTION_STATUS("030", "外部异常处理状态"),

    EXTERNAL_METHOD_TYPE("031", "外部方法类型"),

    CARRIAGE_PRICING_TYPE("032", "运费定价类型"),

    CARRIAGE_SETTLEMENT_TYPE("033", "运费结算类型"),

    DELIVERY_NOTE_STATUS("034", "送货单状态枚举")
    ;

    /**
     * 物流值集类型前缀
     */
    final static String LOGISTICS_PREFIX = "03";

    /**
     * 描述
     */
    private final String code;

    /**
     * 编码
     */
    private final String desc;

    LogisticsValueSetEnum(String code, String desc) {
        this.code = LOGISTICS_PREFIX + code;
        this.desc = desc;
    }

    public static LogisticsValueSetEnum valueOfCode(String code) {
        LogisticsValueSetEnum[] enums = values();
        for (LogisticsValueSetEnum item : enums) {
            if (item.code.equals(code)) {
                return item;
            }
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}
