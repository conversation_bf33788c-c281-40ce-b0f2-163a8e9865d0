package com.ecommerce.open.enums.pom.logistics;

/**
 * <AUTHOR>
 * @Description:
 * @Date: Create in 下午3:13 18/8/31
 */
public enum PickingBillTypeEnum {
    BUYER_TAKE("0100", "买家自提"),

    SELLER_DELIVERY("0200", "卖家配送"),

    PLATFORM_DELIVERY("0300", "平台配送"),

    VENDOR_ENTRUSTMENT("0400", "卖家委托"),

    STORE_DELIVERY("0500", "门店配送");

    /**
     * 描述
     */
    private final String code;

    /**
     * 编码
     */
    private final String desc;

    PickingBillTypeEnum(String code, String desc) {
        this.code = LogisticsValueSetEnum.PICKING_BILL_TYPE.getCode() + code;
        this.desc = desc;
    }

    public static PickingBillTypeEnum valueOfCode(String code) {
        PickingBillTypeEnum[] enums = values();
        for (PickingBillTypeEnum item : enums) {
            if (item.code.equals(code)) {
                return item;
            }
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}
