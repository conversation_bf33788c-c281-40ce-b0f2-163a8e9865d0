package com.ecommerce.open.enums.pom.logistics;

/**
 * <AUTHOR>
 * @Date: 22/08/2018 15:22
 * @Description:
 */
public enum VehicleTypeAxlesEnum {
    AXLES_TWO("0100", 2),

    AXLES_THREE("0200", 3),

    AXLES_FOUR("0300", 4),

    AXLES_FIVE("0500", 5),

    AXLES_SIX("0400", 6);

    /**
     * 描述
     */
    private final String code;

    /**
     * 编码
     */
    private final Integer desc;

    VehicleTypeAxlesEnum(String code, Integer desc) {
        this.code = LogisticsValueSetEnum.VEHICLE_TYPE_AXLES.getCode() + code;
        this.desc = desc;
    }

    public static VehicleTypeAxlesEnum valueOfCode(String code) {
        VehicleTypeAxlesEnum[] enums = values();
        for (VehicleTypeAxlesEnum item : enums) {
            if (item.code.equals(code)) {
                return item;
            }
        }
        return null;
    }
    public static String getCodeByDesc(int desc) {
        VehicleTypeAxlesEnum[] enums = values();
        for (VehicleTypeAxlesEnum item : enums) {
            if (item.desc == desc) {
                return item.code;
            }
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public Integer getDesc() {
        return this.desc;
    }

}
