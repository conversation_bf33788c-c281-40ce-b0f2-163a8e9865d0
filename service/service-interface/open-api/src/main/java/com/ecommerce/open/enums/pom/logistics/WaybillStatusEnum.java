package com.ecommerce.open.enums.pom.logistics;

/**
 * <AUTHOR>
 * @Description:
 * @Date: Create in 下午4:46 18/8/13
 */
public enum WaybillStatusEnum {
    WAIT_MERGER("0100", "待整合"),

    WAIT_AUDIT("0200", "待审核"),

    WAIT_PUBLISH("0300", "待发布"),

    WAIT_RECEIVE("0400", "待接单"),

    WAIT_DELIVERY("0500", "待配送"),

    DELIVERING("0600", "配送中"),

    COMPLETED("0700", "已完成"),

    CANCELED("0800", "已取消"),

    CLOSED("0900", "已关闭"),

    TIMEOUT("1000", "已超时"),
	
	WAIT_ASSIGN("1100", "待指派"),
	
	WAIT_CONFIRM("1200", "待确认"),

    /**运单,已完成之前的一个中间状态**/
    SIGNING("1300", "签收中")
    ;

    /**
     * 描述
     */
    private final String code;

    /**
     * 编码
     */
    private final String desc;

    WaybillStatusEnum(String code, String desc) {
        this.code = LogisticsValueSetEnum.WAYBILL_STATUS.getCode() + code;
        this.desc = desc;
    }

    public static WaybillStatusEnum valueOfCode(String code) {
        WaybillStatusEnum[] enums = values();
        for (WaybillStatusEnum item : enums) {
            if (item.code.equals(code)) {
                return item;
            }
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}
