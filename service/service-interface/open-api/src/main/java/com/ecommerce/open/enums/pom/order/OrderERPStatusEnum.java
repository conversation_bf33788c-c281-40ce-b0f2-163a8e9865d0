package com.ecommerce.open.enums.pom.order;

import java.util.ArrayList;
import java.util.List;

/**
 * 订单erp子状态
 /**
 * OrderStatusEnum
 *
 * <AUTHOR>
 */
public enum OrderERPStatusEnum {
	ORDER_TO_ERP_ERROR("order_to_erp_error", "订单传erp失败"),
    ORDER_TO_ERP_SUCCESS("order_to_erp_success", "订单传erp成功"),
    ORDER_CREATE_ERP_SUCCESS("order_create_erp_success", "订单创建erp执行成功"),
    ORDER_CREATE_ERP_ERROR("order_create_erp_error", "订单创建erp执行失败"),
    ORDER_CLOSE_ERP_SUCCESS("order_close_erp_success", "订单关闭erp执行成功"),
    ORDER_CLOSE_ERP_ERROR("order_close_erp_error", "订单关闭erp执行失败"),
    ORDER_COMPLETE_ERP_SUCCESS("order_complete_erp_success", "订单完成erp执行成功"),
    ORDER_COMPLETE_ERP_ERROR("order_complete_erp_error", "订单完成erp执行失败"),
	;

    /** 枚举值 */
    private final String code;

    /** 枚举描述 */
    private final String message;


    /**
     * 构造一个<code>ResourceStatusEnum</code>枚举对象
     *
     * @param code
     * @param message
     */
    private OrderERPStatusEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * @return Returns the code.
     */
    public String getCode() {
        return code;
    }

    /**
     * @return Returns the message.
     */
    public String getMessage() {
        return message;
    }

    /**
     * @return Returns the code.
     */
    public String code() {
        return code;
    }

    /**
     * @return Returns the message.
     */
    public String message() {
        return message;
    }

    /**
     * 通过枚举<code>code</code>获得枚举
     *
     * @param code
     * @return ResourceStatusEnum
     */
    public static OrderERPStatusEnum getByCode(String code) {
        for (OrderERPStatusEnum _enum : values()) {
            if (_enum.getCode().equals(code)) {
                return _enum;
            }
        }
        return null;
    }

    /**
     * 获取全部枚举
     *
     * @return List<ResourceStatusEnum>
     */
    public List<OrderERPStatusEnum> getAllEnum() {
        List<OrderERPStatusEnum> list = new ArrayList<OrderERPStatusEnum>();
        for (OrderERPStatusEnum _enum : values()) {
            list.add(_enum);
        }
        return list;
    }

    /**
     * 获取全部枚举值
     *
     * @return List<String>
     */
    public List<String> getAllEnumCode() {
        List<String> list = new ArrayList<String>();
        for (OrderERPStatusEnum _enum : values()) {
            list.add(_enum.code());
        }
        return list;
    }
}
