package com.ecommerce.open.redis.key;

/**
 * <AUTHOR>
 * @Date: 07/09/2018 09:05
 * @DESCRIPTION:
 */
public class OpenRedisKey {
    public static final String VERIFICATION_CODE = "open:VERIFICATION:CODE:";
    public static final String VERIFICATION_CERTIFICATE = "open:VERIFICATION:CERTIFICATE:";

    /** erp配置保存到缓存的前缀配置 **/
    public static final String SYS_CODE_CONFIG = "open:SYS_CODE_CONFIG:";

    /** 路由配置信息保存到缓存的前缀 **/
    public static final String SYS_ROUTE = "open:SYS_ROUTE:";

    /** api配置信息保存到缓存的前缀 **/
    public static final String API_CONFIG = "open:API_CONFIG:";

    /** api配置信息,基于bizCode维度保存 **/
    public static final String API_CONFIG_BIZ_CODE = "open:API_CONFIG:BIZ_CODE:";

    /** api配置信息,基于requestType维度保存 **/
    public static final String API_CONFIG_REQUEST_TYPE = "open:API_CONFIG:REQUEST_TYPE:";

    /** api参数配置 **/
    public static final String API_PARAM_CONFIG = "open:API_PARAM_CONFIG:";

    /** erp系统保存到缓存中**/
    public static final String ERP_SYS_CONFIG = "erp:SYS_CONFIG:";

}
