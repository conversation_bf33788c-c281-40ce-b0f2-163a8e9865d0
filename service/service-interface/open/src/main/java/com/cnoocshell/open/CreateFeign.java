
package com.ecommerce.open;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ecommerce.open.api.dto.pinganjz.OpenMemberChannelRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.query.BalanceQueryItemDTO;
import com.ecommerce.open.api.dto.pinganjz.query.BalanceQueryResponseDTO;
import com.ecommerce.plugin.GenerateUtil;
import org.springframework.util.Assert;

import java.io.File;
import java.io.FilenameFilter;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;


/**
 * @Author: <EMAIL>
 * @Description 此方法用于自动生成controller和feign
 * @Date 15/08/2018 16:03
 */

public class CreateFeign {

    public static void main(String[] args) throws Exception {
//        new GenerateUtil().controller("IApiConfigService");
        new GenerateUtil().feign("ApiConfigController");
//
//        Class<?> aClass = Class.forName("com.ecommerce.open.api.dto.pinganjz.OpenMemberChannelRequestDTO");
//
//        OpenMemberChannelRequestDTO obj = (OpenMemberChannelRequestDTO) aClass.newInstance();
//        obj.setCnsmrSeqNo("aaa");
//
//        Method method = aClass.getMethod("getCnsmrSeqNo");
//
//        Object invoke = method.invoke(obj);
//        System.out.println(invoke);

//        JSONArray jsonArray = new JSONArray();
//        JSONObject jsonObject = new JSONObject();
//        jsonObject.put("SubAcctNo",  "11111111111");
//        jsonObject.put("AcctAvailBal",  "2222222222222");
//        jsonObject.put("MaintenanceDate",  "33333333333333");
//        jsonObject.put("SubAcctName",  "4444444444444444444");
//        jsonArray.add(jsonObject);
//
//        BalanceQueryResponseDTO responseDTO = new BalanceQueryResponseDTO();
//        List<BalanceQueryItemDTO> acctArray = jsonArray.toJavaList(BalanceQueryItemDTO.class);
//        responseDTO.setAcctArray(acctArray);
//        String s = JSON.toJSONString(responseDTO);
//        System.out.println(s);
//
//
//        BalanceQueryResponseDTO parse = JSON.parseObject(s, BalanceQueryResponseDTO.class);
//        System.out.println(parse);
    }

}

