package com.ecommerce.open;

import com.ecommerce.base.api.config.BaseRibbonConfig;
import com.ecommerce.common.annotation.ExcludeFromComponetScan;
import com.ecommerce.information.api.config.InfoRibbonConfig;
import com.ecommerce.member.api.config.MemberRibbonConfig;
import com.ecommerce.pay.api.config.PayRibbonConfig;
import com.ecommerce.rabbitmq.annotation.EnableRabbitMq;
import com.ecommerce.report.api.config.ReportRibbonConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.cloud.netflix.ribbon.RibbonClient;
import org.springframework.cloud.netflix.ribbon.RibbonClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import tk.mybatis.spring.annotation.MapperScan;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

@EnableAsync
@SpringBootApplication
@EnableFeignClients({
		"com.ecommerce.base.api",
		"com.ecommerce.member.api",
		"com.ecommerce.pay.api",
		"com.ecommerce.information.api",
		"com.ecommerce.report.api",
})
@EnableDiscoveryClient
@EnableKafka
@ComponentScan(value = { "com.ecommerce.open",
		"com.ecommerce.base.api.service",
		"com.ecommerce.common.config",
		"com.ecommerce.common.service",
		"com.ecommerce.common.filter"
}, excludeFilters = {
		@ComponentScan.Filter(type = FilterType.ANNOTATION, value = ExcludeFromComponetScan.class)
})
@MapperScan("com.ecommerce.open.dao.mapper")
@RibbonClients(value = {
		@RibbonClient(name = "pay", configuration = PayRibbonConfig.class),
		@RibbonClient(name = "member", configuration = MemberRibbonConfig.class),
		@RibbonClient(name = "information", configuration = InfoRibbonConfig.class),
//		@RibbonClient(name = "order", configuration = OrderRibbonConfig.class),
		@RibbonClient(name = "base", configuration = BaseRibbonConfig.class),
		@RibbonClient(name = "report", configuration = ReportRibbonConfig.class),
})

public class OpenApplication {

	public static void main(String[] args) {
		SpringApplication.run(OpenApplication.class, args);
	}

	@Bean("myTaskAsyncPool")
	public Executor myTaskAsyncPool() {
		ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
		// 核心线程池大小
		executor.setCorePoolSize(20);
		// 最大线程数
		executor.setMaxPoolSize(40);
		// 队列容量
		executor.setQueueCapacity(300);
		// 活跃时间
		executor.setKeepAliveSeconds(50);
		// 线程名字前缀
		executor.setThreadNamePrefix("baseExecutor-");

		// setRejectedExecutionHandler：当pool已经达到max size的时候，如何处理新任务
		// CallerRunsPolicy：不在新线程中执行任务，而是由调用者所在的线程来执行
		executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
		executor.initialize();
		return executor;
	}
}
