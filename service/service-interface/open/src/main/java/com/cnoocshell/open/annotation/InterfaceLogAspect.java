package com.ecommerce.open.annotation;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.open.dao.vo.InterfaceLogInfo;
import com.ecommerce.open.disruptor.event.EventTypeEnum;
import com.ecommerce.open.disruptor.publisher.InterfaceEventPublisher;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Date;
import java.util.Map;

/**
 * Created by deguo on 29/09/2017.
 */
@Slf4j
@Aspect
@Component
public class InterfaceLogAspect {
    @Autowired
    private InterfaceEventPublisher publisher;

    @Around(value="execution(* com.ecommerce..*.*(..))&&@annotation(ifl)")
    public Object aroundMethod(ProceedingJoinPoint pjd, InterfaceLogger ifl) throws Throwable {
        ItemResult result0 = new ItemResult<>();
        String value = ifl.value();
        Object[] args = pjd.getArgs();
        InterfaceLogInfo interfaceLog = new InterfaceLogInfo();
        if (args.length > 0) {
            StringBuffer sf = new StringBuffer("");
            for(Object param:args){
                if (param != null) {
                    sf.append(JSONObject.toJSONString(param));
                }
            }
            interfaceLog.setParams(sf.toString());
            interfaceLog.setCreateTime(new Date());
            interfaceLog.setStartTime(new Date());
            interfaceLog.setInterfaceName(value);

//            String messageId = null;
//            try {
//                String field = "messageId";
//                if (!CsStringUtils.isEmpty(ifl.msgIdField())) {
//                    field = ifl.msgIdField();
//                }
//                Class<?> paramClass = param.getClass();
//                if (Map.class.isAssignableFrom(paramClass)) {
//                    messageId = (String) ((Map) param).get(field);
//                } else {
//                    String methodName = "get" + field.substring(0, 1).toUpperCase() + field.substring(1);
//                    Method method = paramClass.getMethod(methodName);
//                    try {
//                        messageId = (String) method.invoke(param);
//                    } catch (IllegalAccessException e) {
//                        log.info("getMessageId method error:" + e.getMessage());
//                    } catch (InvocationTargetException e) {
//                        log.info("getMessageId method error:" + e.getMessage());
//                    }
//                }
//            } catch (NoSuchMethodException e) {
//                log.info("getMessageId method error:" + e.getMessage());
//            }
//            interfaceLog.setMessageId(messageId);
        }

        Object result = null;
        try {
            result = pjd.proceed();
            ItemResult object = (ItemResult) result;
            interfaceLog.setReturnMessage(JSONObject.toJSONString(result));
            if (object.getCode() == null) {
                interfaceLog.setReturncode(object.isSuccess() ? "success" : "fail");
            } else {
                interfaceLog.setReturncode(object.getCode());
            }
        } catch (ClassCastException e) {
            interfaceLog.setReturnMessage(JSON.toJSONString(result));
        } catch (Throwable e) {
            interfaceLog.setErrorMessage(e.getMessage());
            throw e;
        }finally {
            interfaceLog.setEndTime(new Date());
            publisher.publishEvent(interfaceLog, EventTypeEnum.SAVE.getCode());
        }
        return result;
    }

}
