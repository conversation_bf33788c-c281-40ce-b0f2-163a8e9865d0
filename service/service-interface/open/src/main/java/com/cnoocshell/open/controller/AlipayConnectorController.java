package com.ecommerce.open.controller;

import com.alibaba.fastjson.JSON;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.open.api.dto.alipay.*;
import com.ecommerce.open.service.payment.IAlipayConnector;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * @Created: 10:49 11/12/2018
 * <AUTHOR>
 * @Description: TODO
 */
@RestController
@RequestMapping("/alipayConnector")
public class AlipayConnectorController {


    @Autowired
    private IAlipayConnector alipayConnector;

    /**
     * 退款
     * @param refundOpenApiStr
     * @return
     */
    @PostMapping("/refund")
    public AlipayRefundResultDTO refund(@RequestParam("refundOpenApiDTO") String refundOpenApiStr) {
        AlipayRefundOpenApiDTO alipayRefundOpenApiDTO = JSON.parseObject(refundOpenApiStr, AlipayRefundOpenApiDTO.class);
        return alipayConnector.refundRequest(alipayRefundOpenApiDTO);
    }

    /**
     * 支付宝异步通知接口
     * @param paramsMap
     * @return
     */
    @ApiOperation("异步回调")
    @PostMapping("/alipayNotify")
    public ItemResult<String> alipayNotify(@RequestBody Map<String, String> paramsMap) {
        return alipayConnector.alipayNotify(paramsMap);
    }

    /**
     * 查询订单
     * @param alipayQueryOpenApi
     * @return
     */
    @ApiOperation("查询订单")
    @PostMapping("/queryPayment")
    public AlipayQueryResultDTO queryPayment(@RequestParam("alipayQueryOpenApiDTO") String alipayQueryOpenApi) {
        AlipayQueryOpenApiDTO queryOpenApiDTO = JSON.parseObject(alipayQueryOpenApi, AlipayQueryOpenApiDTO.class);
        return alipayConnector.queryPayment(queryOpenApiDTO);
    }

    /**
     * 查询退款订单
     * @param alipayQueryOpenApi
     * @return
     */
    @ApiOperation("查询退款订单")
    @PostMapping("/queryRefund")
    public AlipayRefundQueryRestultDTO queryRefund(@RequestParam("alipayQueryOpenApiDTO") String alipayQueryOpenApi) {
        AlipayQueryOpenApiDTO queryOpenApiDTO = JSON.parseObject(alipayQueryOpenApi, AlipayQueryOpenApiDTO.class);
        return alipayConnector.queryRefund(queryOpenApiDTO);
    }

    /**
     * 关闭支付单
     * @param alipayQueryOpenApi
     * @return
     */
    @ApiOperation("关闭支付单")
    @PostMapping("/closeOrder")
    public AlipayCloseResultDTO closeOrder(@RequestParam("alipayQueryOpenApiDTO") String alipayQueryOpenApi) {
        AlipayQueryOpenApiDTO queryOpenApiDTO = JSON.parseObject(alipayQueryOpenApi, AlipayQueryOpenApiDTO.class);
        return alipayConnector.closeOrder(queryOpenApiDTO);
    }


    /**
     * 下载支付对账单
     * @param alipayDownloadStatementRequestDTO
     * @return
     */
    @ApiOperation("下载支付对账单")
    @PostMapping("/downloadStatement")
    public AlipayDownloadStatementDTO downloadStatement(@RequestParam("alipayDownloadStatementRequestDTO") String alipayDownloadStatementRequestDTO) {
        AlipayDownloadStatementRequestDTO requestDTO = JSON.parseObject(alipayDownloadStatementRequestDTO, AlipayDownloadStatementRequestDTO.class);
        return alipayConnector.downloadStatement(requestDTO);
    }

}
