package com.ecommerce.open.controller;

import com.ecommerce.common.service.common.BizRedisService;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;

@Slf4j
@RestController
@RequestMapping("/regionSync")
@Api(tags={"AmapRegionController"},description = "从高德地图获取行政区划(仅供base同步数据使用)")
public class AmapRegionController {

    @Autowired
    private BizRedisService bizRedisService;

    @Autowired
    @Qualifier("myTaskAsyncPool")
    private Executor executor;
    //与base工程RegionSyncBiz保持一致
    private static String key1 ="base:cache:region:sync:amapData";
    private static String key2 ="base:cache:region:sync:error";

    /**
     * 数据量大，响应时间长 所以存redis,由base主动查询redis
     * @param url
     * @return
     */
    @PostMapping
    public Boolean getRegionData(@RequestParam("url") String url){
        executor.execute(()->{
            try{
                Request request = (new Request.Builder()).url(url).build();
                OkHttpClient okHttpClient = new OkHttpClient().newBuilder()
                        .connectTimeout(1, TimeUnit.MINUTES)
                        .readTimeout(1, TimeUnit.MINUTES).build();
                Response response = null;
                try {
                    log.info("getRegionData request begin");
                    response = okHttpClient.newCall(request).execute();
                    if (response.isSuccessful()) {
                        String jsonStr = response.body().string();
                        log.info("get data success: {}",jsonStr);
                        bizRedisService.set(key1,jsonStr);
                        bizRedisService.setTimeout(key1,1,TimeUnit.HOURS);
                    }else{
                        log.info("get data fail: {}",response.code());
                        bizRedisService.set(key2,"获取接口数据出错:"+response.code());
                        bizRedisService.setTimeout(key2,1,TimeUnit.HOURS);
                    }
                } finally {
                    if (response != null) {
                        response.close();
                    }
                }
            }catch (Throwable e){
                bizRedisService.set(key2,"获取接口数据出错");
                bizRedisService.setTimeout(key2,1,TimeUnit.HOURS);
                log.error(e.getMessage(),e);
            }
        });
        log.info("invoke success");
        //调用成功
        return true;
    }
}
