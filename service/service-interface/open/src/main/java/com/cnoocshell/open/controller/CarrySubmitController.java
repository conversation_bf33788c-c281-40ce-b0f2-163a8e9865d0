package com.ecommerce.open.controller;

import com.ecommerce.open.api.dto.carry.carryContract.CarryContractDTO;
import com.ecommerce.open.api.dto.carry.carryContract.CarryContractListDTO;
import com.ecommerce.open.api.dto.carry.carryContract.CarryContractQueryDTO;
import com.ecommerce.open.api.dto.carry.carryDriver.CarryDriverDTO;
import com.ecommerce.open.api.dto.carry.carryDriver.CarryDriverListDTO;
import com.ecommerce.open.api.dto.carry.carryDriver.CarryDriverQueryDTO;
import com.ecommerce.open.api.dto.carry.carryEnterprise.CarryEnterpriseDTO;
import com.ecommerce.open.api.dto.carry.carryEnterprise.CarryEnterpriseListDTO;
import com.ecommerce.open.api.dto.carry.carryEnterprise.CarryEnterpriseQueryDTO;
import com.ecommerce.open.api.dto.carry.carryFinanceFlow.CarryFinanceFlowDTO;
import com.ecommerce.open.api.dto.carry.carryFinanceFlow.CarryFinanceFlowListDTO;
import com.ecommerce.open.api.dto.carry.carryFinanceFlow.CarryFinanceFlowQueryDTO;
import com.ecommerce.open.api.dto.carry.carryShipNote.CarryShipNoteQueryDTO;
import com.ecommerce.open.api.dto.carry.carryShipNote.CarryWaybillDTO;
import com.ecommerce.open.api.dto.carry.carryShipNote.CarryWaybillListDTO;
import com.ecommerce.open.api.dto.carry.carryVehicle.CarryVehicleDTO;
import com.ecommerce.open.api.dto.carry.carryVehicle.CarryVehicleListDTO;
import com.ecommerce.open.api.dto.carry.carryVehicle.CarryVehicleQueryDTO;
import com.ecommerce.open.service.carry.ICarryContractSubmitService;
import com.ecommerce.open.service.carry.ICarryDriverSubmitService;
import com.ecommerce.open.service.carry.ICarryEnterpriseSubmitService;
import com.ecommerce.open.service.carry.ICarryFinanceFlowSubmitService;
import com.ecommerce.open.service.carry.ICarryShipNoteSubmitService;
import com.ecommerce.open.service.carry.ICarryVehicleSubmitService;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.ApiOperation;
import com.ecommerce.open.service.carry.ICarryCreditSubmitService;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.open.api.dto.carry.carryMonthlyCredit.CarryMonthlyCreditDTO;
import java.lang.Void;
import com.ecommerce.open.api.dto.carry.carryMonthlyCredit.CarryMonthlyCreditQueryDTO;
import com.ecommerce.common.result.PageData;
import com.ecommerce.open.api.dto.carry.carryMonthlyCredit.CarryMonthlyCreditListDTO;
import com.ecommerce.open.api.dto.PageQuery;
import java.lang.String;
import java.util.List;

import com.ecommerce.open.api.dto.carry.carryMonthlyCredit.CarryMonthlyCreditAddDTO;


/**
 * 信息上报服务
 **/
@Api(tags={"CarrySubmitController"},description = ": 信息上报服务")
@RestController
@RequestMapping("/carrySubmit")
public class CarrySubmitController {

   @Autowired 
   private ICarryCreditSubmitService carryCreditSubmitService;
   @Autowired
   private ICarryShipNoteSubmitService carryShipNoteSubmitService;
   @Autowired
   private ICarryEnterpriseSubmitService carryEnterpriseSubmitService;
   @Autowired
   private ICarryVehicleSubmitService carryVehicleSubmitService;
   @Autowired
   private ICarryDriverSubmitService carryDriverSubmitService;
   @Autowired
   private ICarryContractSubmitService carryContractSubmitService;
   @Autowired
   private ICarryFinanceFlowSubmitService carryFinanceFlowSubmitService;

   @ApiOperation("编辑质量和信用数据")
   @PostMapping(value="/editCarryMonthlyCredit")
   public ItemResult<Void> editCarryMonthlyCredit(@RequestBody CarryMonthlyCreditDTO carryMonthlyCreditDTO)throws Exception{
      return carryCreditSubmitService.editCarryMonthlyCredit(carryMonthlyCreditDTO);
   }


   @ApiOperation("分页查询质量和信用数据")
   @PostMapping(value="/selectCarryMonthlyCreditList")
   public ItemResult<PageData<CarryMonthlyCreditListDTO>> selectCarryMonthlyCreditList(@RequestBody PageQuery<CarryMonthlyCreditQueryDTO> pageQuery)throws Exception{
      return carryCreditSubmitService.selectCarryMonthlyCreditList(pageQuery);
   }


   @ApiOperation("查询质量和信用数据详情")
   @PostMapping(value="/selectCarryMonthlyCreditDetail")
   public ItemResult<CarryMonthlyCreditDTO> selectCarryMonthlyCreditDetail(@RequestParam("creditRecordId") String creditRecordId)throws Exception{
      return carryCreditSubmitService.selectCarryMonthlyCreditDetail(creditRecordId);
   }


   @ApiOperation("创建质量和信用数据")
   @PostMapping(value="/saveCarryMonthlyCredit")
   public ItemResult<Void> saveCarryMonthlyCredit(@RequestBody CarryMonthlyCreditAddDTO carryMonthlyCreditAddDTO)throws Exception{
      return carryCreditSubmitService.saveCarryMonthlyCredit(carryMonthlyCreditAddDTO);
   }


   @ApiOperation("提交质量和信用数据")
   @PostMapping(value="/subCarryMonthlyCredit")
   public ItemResult<String> subCarryMonthlyCredit(@ApiParam("信用上报记录主键") @RequestParam("creditRecordId") String creditRecordId,@RequestParam("operatorId") String operatorId)throws Exception{
      return carryCreditSubmitService.subCarryMonthlyCredit(creditRecordId,operatorId);
   }

    @ApiOperation("删除质量和信用数据(状态不为已上报)")
    @PostMapping(value="/deleteCarryMonthlyCredit")
    public ItemResult<Void> deleteCarryMonthlyCredit(@RequestBody List<String> creditRecordIds,@ApiParam("操作人账号ID") @RequestParam("operatorId") String operatorId)throws Exception{
        return carryCreditSubmitService.deleteCarryMonthlyCredit(creditRecordIds,operatorId);
    }

   @ApiOperation("保存无车承运人运单数据")
   @PostMapping(value="/saveShipNote")
   public ItemResult<String> saveShipNote(@RequestBody CarryWaybillDTO carryWaybillDTO){
      return carryShipNoteSubmitService.saveShipNote(carryWaybillDTO);
   }

   @ApiOperation("分页查询电子运单数据")
   @PostMapping(value="/selectCarryShipNoteList")
   public ItemResult<PageData<CarryWaybillListDTO>> selectCarryShipNoteList(@RequestBody PageQuery<CarryShipNoteQueryDTO> pageQuery){
      return carryShipNoteSubmitService.selectCarryShipNoteList(pageQuery);
   }

    @ApiOperation("通过运单号查询上报运单信息")
    @PostMapping(value="/selectCarryShipNoteListByQuery")
    public ItemResult<CarryWaybillListDTO> selectCarryShipNoteListByQuery(@RequestParam("shippingNoteNumber") String shippingNoteNumber){
        return carryShipNoteSubmitService.selectCarryShipNoteListByQuery(shippingNoteNumber);
    }

   @ApiOperation("查询电子运单上报数据详情")
   @PostMapping(value="/selectCarryShipNoteDetail")
   public ItemResult<CarryWaybillDTO> selectCarryShipNoteDetail(@ApiParam("运输记录ID") @RequestParam("shipRecordId") String shipRecordId){
      return carryShipNoteSubmitService.selectCarryShipNoteDetail(shipRecordId);
   }

   @ApiOperation("电子运单信息上报")
   @PostMapping(value="/batchSubShipNoteByIds")
   public ItemResult<Void> batchSubShipNoteByIds(@RequestBody List<String> shipRecordIds)throws Exception{
      return carryShipNoteSubmitService.batchSubShipNoteByIds(shipRecordIds);
   }

    @ApiOperation("删除电子运单信息数据(状态不为已上报)")
    @PostMapping(value="/deleteShipNoteByIds")
    public ItemResult<Void> deleteShipNoteByIds(@RequestBody List<String> shipRecordIds,@ApiParam("操作人账号ID") @RequestParam("operatorId") String operatorId)throws Exception{
        return carryShipNoteSubmitService.deleteShipNoteByIds(shipRecordIds,operatorId);
    }

   @ApiOperation("网络货运企业信息上报")
   @PostMapping(value="/subCarryEnterprise")
   public ItemResult<String> subCarryEnterprise(@ApiParam("网络货运企业信息Id")  @RequestParam("enterpriseId") String enterpriseId)throws Exception{
      return carryEnterpriseSubmitService.subCarryEnterprise(enterpriseId);
   }

   @ApiOperation("保存网络货运企业信息")
   @PostMapping(value="/saveCarryEnterprise")
   public ItemResult<String> saveCarryEnterprise(@RequestBody CarryEnterpriseDTO carryEnterpriseDTO)throws Exception{
      return carryEnterpriseSubmitService.saveCarryEnterprise(carryEnterpriseDTO);
   }

   @ApiOperation("分页查询网络货运企业信息")
   @PostMapping(value="/selectCarryEnterpriseList")
   public ItemResult<PageData<CarryEnterpriseListDTO>> selectCarryEnterpriseList(@RequestBody PageQuery<CarryEnterpriseQueryDTO> pageQuery)throws Exception{
      return carryEnterpriseSubmitService.selectCarryEnterpriseList(pageQuery);
   }

   @ApiOperation("查询网络货运企业信息数据详情")
   @PostMapping(value="/selectCarryEnterpriseDetail")
   public ItemResult<CarryEnterpriseDTO> selectCarryEnterpriseDetail(@ApiParam("网络货运企业信息Id")  @RequestParam("enterpriseId") String enterpriseId)throws Exception{
      return carryEnterpriseSubmitService.selectCarryEnterpriseDetail(enterpriseId);
   }

    @ApiOperation("保存无车承运人车辆信息")
    @PostMapping(value="/saveVehicle")
    public ItemResult<String> saveVehicle(@RequestBody CarryVehicleDTO carryVehicleDTO)throws Exception{
        return carryVehicleSubmitService.saveVehicle(carryVehicleDTO);
    }

    @ApiOperation("批量提交车辆基本信息")
    @PostMapping(value="/batchSubVehicle")
    public ItemResult<Void> batchSubVehicle(@ApiParam("上传条数")  @RequestParam("size") int size)throws Exception{
        return carryVehicleSubmitService.batchSubVehicle(size);
    }

    @ApiOperation("分页查询车辆基本信息")
    @PostMapping(value="/selectCarryVehicleList")
    public ItemResult<PageData<CarryVehicleListDTO>> selectCarryVehicleList(@RequestBody PageQuery<CarryVehicleQueryDTO> pageQuery)throws Exception{
        return carryVehicleSubmitService.selectCarryVehicleList(pageQuery);
    }

    @ApiOperation("车辆基本信息上报")
    @PostMapping(value="/batchSubVehicleByIds")
    public ItemResult<Void> batchSubVehicleByIds(@RequestBody List<String> vehicleIds)throws Exception{
        return carryVehicleSubmitService.batchSubVehicleByIds(vehicleIds);
    }

    @ApiOperation("查询车辆基本信息详情")
    @PostMapping(value="/selectCarryVehicleDetail")
    public ItemResult<CarryVehicleDTO> selectCarryVehicleDetail(@ApiParam("车辆记录ID") @RequestParam("vehicleId")String vehicleId)throws Exception{
        return carryVehicleSubmitService.selectCarryVehicleDetail(vehicleId);
    }

    @ApiOperation("删除车辆基本信息数据(状态不为已上报)")
    @PostMapping(value="/deleteCarryVehicleByIds")
    public ItemResult<Void> deleteCarryVehicleByIds(@RequestBody List<String> vehicleIds,@ApiParam("操作人账号ID") @RequestParam("operatorId") String operatorId)throws Exception{
        return carryVehicleSubmitService.deleteCarryVehicleByIds(vehicleIds,operatorId);
    }

    @ApiOperation("保存无车承运人驾驶员信息")
    @PostMapping(value="/saveDriver")
    public ItemResult<String> saveDriver(@RequestBody CarryDriverDTO carryDriverDTO)throws Exception{
        return carryDriverSubmitService.saveDriver(carryDriverDTO);
    }

    @ApiOperation("批量提交驾驶员基本信息")
    @PostMapping(value="/batchSubDriver")
    public ItemResult<Void> batchSubDriver(@ApiParam("上传条数")  @RequestParam("size") int size)throws Exception{
        return carryDriverSubmitService.batchSubDriver(size);
    }

    @ApiOperation("分页查询驾驶员基本信息")
    @PostMapping(value="/selectCarryDriverList")
    public ItemResult<PageData<CarryDriverListDTO>> selectCarryDriverList(@RequestBody PageQuery<CarryDriverQueryDTO> pageQuery)throws Exception{
        return carryDriverSubmitService.selectCarryDriverList(pageQuery);
    }

    @ApiOperation("驾驶员基本信息上报")
    @PostMapping(value="/batchSubDriverByIds")
    public ItemResult<Void> batchSubDriverByIds(@RequestBody List<String> driverIds)throws Exception{
        return carryDriverSubmitService.batchSubDriverByIds(driverIds);
    }

    @ApiOperation("查询驾驶员信息详情")
    @PostMapping(value="/selectCarryDriverDetail")
    public ItemResult<CarryDriverDTO> selectCarryDriverDetail(@ApiParam("司机记录ID") @RequestParam("driverId") String driverId)throws Exception{
        return carryDriverSubmitService.selectCarryDriverDetail(driverId);
    }

    @ApiOperation("删除驾驶员基本信息数据(状态不为已上报)")
    @PostMapping(value="/deleteCarryDriverByIds")
    public ItemResult<Void> deleteCarryDriverByIds(@RequestBody List<String> driverIds,@ApiParam("操作人账号ID") @RequestParam("operatorId") String operatorId)throws Exception{
        return carryDriverSubmitService.deleteCarryDriverByIds(driverIds,operatorId);
    }

    @ApiOperation("保存无车承运人合同信息")
    @PostMapping(value="/saveContract")
    public ItemResult<String> saveContract(@RequestBody CarryContractDTO carryContractDTO)throws Exception{
        return carryContractSubmitService.saveContract(carryContractDTO);
    }

    @ApiOperation("分页查询合同信息")
    @PostMapping(value="/selectCarryContractList")
    public ItemResult<PageData<CarryContractListDTO>> selectCarryContractList(@RequestBody PageQuery<CarryContractQueryDTO> pageQuery)throws Exception{
        return carryContractSubmitService.selectCarryContractList(pageQuery);
    }

    @ApiOperation("合同信息批量上报")
    @PostMapping(value="/batchSubContractByIds")
    public ItemResult<Void> batchSubContractByIds(@RequestBody List<String> contractIds)throws Exception{
        return carryContractSubmitService.batchSubContractByIds(contractIds);
    }

    @ApiOperation("查询合同信息详情")
    @PostMapping(value="/selectCarryContractDetail")
    public ItemResult<CarryContractDTO> selectCarryContractDetail(@ApiParam("合同ID") @RequestParam("contractId") String contractId)throws Exception{
        return carryContractSubmitService.selectCarryContractDetail(contractId);
    }

    @ApiOperation("删除合同信息数据(状态不为已上报)")
    @PostMapping(value="/deleteCarryContractByIds")
    public ItemResult<Void> deleteCarryContractByIds(@RequestBody List<String> contractIds,@ApiParam("操作人账号ID") @RequestParam("operatorId") String operatorId)throws Exception{
        return carryContractSubmitService.deleteCarryContractByIds(contractIds,operatorId);
    }

    @ApiOperation("保存无车承运人资金流水信息")
    @PostMapping(value="/saveFinanceFlow")
    public ItemResult<String> saveFinanceFlow(@RequestBody CarryFinanceFlowDTO carryFinanceFlowDTO)throws Exception{
        return carryFinanceFlowSubmitService.saveFinanceFlow(carryFinanceFlowDTO);
    }

    @ApiOperation("分页查询资金流水信息")
    @PostMapping(value="/selectCarryFinanceFlowList")
    public ItemResult<PageData<CarryFinanceFlowListDTO>> selectCarryFinanceFlowList(@RequestBody PageQuery<CarryFinanceFlowQueryDTO> pageQuery)throws Exception{
        return carryFinanceFlowSubmitService.selectCarryFinanceFlowList(pageQuery);
    }

    @ApiOperation("资金流水信息上报")
    @PostMapping(value="/batchSubFinanceFlowByIds")
    public ItemResult<Void> batchSubFinanceFlowByIds(@RequestBody List<String> financeFlowIds)throws Exception{
        return carryFinanceFlowSubmitService.batchSubFinanceFlowByIds(financeFlowIds);
    }

    @ApiOperation("查询资金流水信息")
    @PostMapping(value="/selectCarryFinanceFlowDetail")
    public ItemResult<CarryFinanceFlowDTO> selectCarryFinanceFlowDetail(@ApiParam("资金流水单ID") @RequestParam("financeFlowId") String financeFlowId)throws Exception{
        return carryFinanceFlowSubmitService.selectCarryFinanceFlowDetail(financeFlowId);
    }

    @ApiOperation("删除资金流水信息数据(状态不为已上报)")
    @PostMapping(value="/deleteCarryFinanceFlowByIds")
    public ItemResult<Void> deleteCarryFinanceFlowByIds(@RequestBody List<String> financeFlowIds,@ApiParam("操作人账号ID") @RequestParam("operatorId") String operatorId)throws Exception{
        return carryFinanceFlowSubmitService.deleteCarryFinanceFlowByIds(financeFlowIds,operatorId);
    }
}
