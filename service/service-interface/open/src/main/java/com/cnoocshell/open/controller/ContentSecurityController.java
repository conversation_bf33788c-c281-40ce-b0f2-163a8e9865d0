package com.ecommerce.open.controller;

import com.ecommerce.open.service.IContentSecurityService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


/**
 * 内容安全
 **/

@Api(tags={"ContentSecurity"},description = "内容安全")
@RestController
@RequestMapping("/contentSecurity")
public class ContentSecurityController {

   @Autowired 
   private IContentSecurityService iContentSecurityService;

   @ApiOperation("替换文本中不安全的文本")
   @PostMapping(value="/greenText")
   public String greenText(@RequestParam("text") String text,@RequestParam(value = "replaceChar",defaultValue = "*") String replaceChar)throws Exception{
      return iContentSecurityService.greenText(text,replaceChar);
   }



}
