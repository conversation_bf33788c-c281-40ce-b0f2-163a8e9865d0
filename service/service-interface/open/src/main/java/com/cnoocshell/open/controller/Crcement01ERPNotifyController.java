package com.ecommerce.open.controller;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import io.swagger.annotations.ApiOperation;
import com.ecommerce.open.service.ICrcement01ERPNotifyService;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.open.api.dto.erp.crcement01.Crce01ERPResultDTO;

import java.lang.String;


/**
 * xxx-ERP的通知接收接口
 */

@Api(tags = {"Crcement01ERPNotify"}, description = "xxx-ERP的通知接收接口")
@RestController
@RequestMapping("/crcement01ERPNotify")
public class Crcement01ERPNotifyController {

    @Autowired
    private ICrcement01ERPNotifyService iCrcement01ERPNotifyService;

    @ApiOperation("接收通知接口的JSON并返回")
    @PostMapping(value = "/notify")
    public ItemResult<Crce01ERPResultDTO> notify(@RequestParam("arg0") String arg0) throws Exception {
        return iCrcement01ERPNotifyService.notify(arg0);
    }


}
