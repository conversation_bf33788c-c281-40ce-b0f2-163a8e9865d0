package com.ecommerce.open.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.open.api.dto.apicenter.base.ERPSaleRegionOptionDTO;
import com.ecommerce.open.api.dto.apicenter.base.ERPSaleRegionRequestDTO;
import com.ecommerce.open.dao.vo.ERPSaleRegionOption;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.ApiOperation;
import com.ecommerce.open.service.erp.IERPIntegrationService;
import com.ecommerce.open.api.dto.erp.ECMemberInfoDTO;
import com.ecommerce.open.api.dto.erp.ECBalanceDTO;
import com.ecommerce.open.api.dto.erp.ERPRequest;
import com.ecommerce.open.api.dto.erp.ERPResult;
import com.ecommerce.open.api.dto.erp.ECPickupPointDTO;
import com.ecommerce.open.api.dto.apicenter.logistic.ECWaybillDTO;
import com.ecommerce.open.api.dto.apicenter.order.ECOrderInfoDTO;
import java.util.List;
import com.ecommerce.open.api.dto.erp.ECContractDTO;
import com.ecommerce.open.api.dto.apicenter.logistic.ECWaybillQueryDTO;
import com.ecommerce.open.api.dto.erp.ECMaterialsDTO;
import com.ecommerce.open.api.dto.erp.ECOrgDTO;


/**
 *  ERP集成service
*/

@Api(tags={"ERPIntegration"},description = ": ERP集成service")
@RestController
@RequestMapping("/eRPIntegration")
public class ERPIntegrationController {

   @Autowired 
   private IERPIntegrationService iERPIntegrationService;

   @ApiOperation("获取ERP销售组织信息")
   @PostMapping(value="/getERPMemberOrg")
   public ERPResult<List<ECOrgDTO>> getERPMemberOrg(@RequestBody ERPRequest<ECOrgDTO> arg0)throws Exception{
      return iERPIntegrationService.getERPMemberOrg(arg0);
   }


   @ApiOperation("查询ERP会员编码")
   @PostMapping(value="/searchERPMember")
   public ERPResult<ECMemberInfoDTO> searchERPMember(@RequestBody ERPRequest<ECMemberInfoDTO> arg0)throws Exception{
      return iERPIntegrationService.searchERPMember(arg0);
   }


   @ApiOperation("完成ERP订单")
   @PostMapping(value="/notifyERPFinishOrder")
   public ERPResult<ECOrderInfoDTO> notifyERPFinishOrder(@RequestBody ERPRequest<ECOrderInfoDTO> arg0)throws Exception{
      return iERPIntegrationService.notifyERPFinishOrder(arg0);
   }


   @ApiOperation("创建ERP运单")
   @PostMapping(value="/notifyERPCreateWaybill")
   public ERPResult<ECWaybillDTO> notifyERPCreateWaybill(@RequestBody ERPRequest<ECWaybillDTO> arg0)throws Exception{
      return iERPIntegrationService.notifyERPCreateWaybill(arg0);
   }


   @ApiOperation("获取ERP会员创建结果")
   @PostMapping(value="/getERPMemberCreateResult")
   public ERPResult<ECMemberInfoDTO> getERPMemberCreateResult(@RequestBody ERPRequest<ECMemberInfoDTO> arg0)throws Exception{
      return iERPIntegrationService.getERPMemberCreateResult(arg0);
   }


   @ApiOperation("获取ERP物料编码")
   @PostMapping(value="/getERPMaterialsCode")
   public ERPResult<List<ECMaterialsDTO>> getERPMaterialsCode(@RequestBody ERPRequest<ECMaterialsDTO> arg0)throws Exception{
      return iERPIntegrationService.getERPMaterialsCode(arg0);
   }


   @ApiOperation("获取ERP提货点信息")
   @PostMapping(value="/getERPMemberPickupPoint")
   public ERPResult<List<ECPickupPointDTO>> getERPMemberPickupPoint(@RequestBody ERPRequest<ECPickupPointDTO> arg0)throws Exception{
      return iERPIntegrationService.getERPMemberPickupPoint(arg0);
   }


   @ApiOperation("获取会员ERP余额")
   @PostMapping(value="/getERPMemberBalance")
   public ERPResult<ECBalanceDTO> getERPMemberBalance(@RequestBody ERPRequest<ECBalanceDTO> arg0)throws Exception{
      return iERPIntegrationService.getERPMemberBalance(arg0);
   }


   @ApiOperation("获取ERP订单创建结果")
   @PostMapping(value="/getERPOrderResult")
   public ERPResult<ECOrderInfoDTO> getERPOrderResult(@RequestBody ERPRequest<ECOrderInfoDTO> arg0)throws Exception{
      return iERPIntegrationService.getERPOrderResult(arg0);
   }


   @ApiOperation("获取ERP会员修改结果")
   @PostMapping(value="/getERPMemberChangeResult")
   public ERPResult<ECMemberInfoDTO> getERPMemberChangeResult(@RequestBody ERPRequest<ECMemberInfoDTO> arg0)throws Exception{
      return iERPIntegrationService.getERPMemberChangeResult(arg0);
   }


   @ApiOperation("获取ERP合同")
   @PostMapping(value="/getERPMemberContract")
   public ERPResult<ECContractDTO> getERPMemberContract(@RequestBody ERPRequest<ECContractDTO> arg0)throws Exception{
      return iERPIntegrationService.getERPMemberContract(arg0);
   }


   @ApiOperation("获取ERP运单创建结果")
   @PostMapping(value="/getERPCreateWaybillResult")
   public ERPResult<ECWaybillDTO> getERPCreateWaybillResult(@RequestBody ERPRequest<ECWaybillDTO> arg0)throws Exception{
      return iERPIntegrationService.getERPCreateWaybillResult(arg0);
   }


   @ApiOperation("通知ERP修改会员信息")
   @PostMapping(value="/notifyERPMemberChange")
   public ERPResult<ECMemberInfoDTO> notifyERPMemberChange(@RequestBody ERPRequest<ECMemberInfoDTO> arg0)throws Exception{
      return iERPIntegrationService.notifyERPMemberChange(arg0);
   }


   @ApiOperation("查询运单排队情况")
   @PostMapping(value="/getERPWaybillQueryInfo")
   public ERPResult<ECWaybillQueryDTO> getERPWaybillQueryInfo(@RequestBody ERPRequest<ECWaybillQueryDTO> arg0)throws Exception{
      return iERPIntegrationService.getERPWaybillQueryInfo(arg0);
   }


   @ApiOperation("异步创建ERP会员")
   @PostMapping(value="/notifyERPMemberCreate")
   public ERPResult<ECMemberInfoDTO> notifyERPMemberCreate(@RequestBody ERPRequest<ECMemberInfoDTO> arg0)throws Exception{
      return iERPIntegrationService.notifyERPMemberCreate(arg0);
   }


   @ApiOperation("创建ERP订单")
   @PostMapping(value="/notifyERPCreateOrder")
   public ERPResult<ECOrderInfoDTO> notifyERPCreateOrder(@RequestBody ERPRequest<ECOrderInfoDTO> arg0)throws Exception{
      return iERPIntegrationService.notifyERPCreateOrder(arg0);
   }


   @ApiOperation("关闭ERP订单")
   @PostMapping(value="/notifyERPCloseOrder")
   public ERPResult<ECOrderInfoDTO> notifyERPCloseOrder(@RequestBody ERPRequest<ECOrderInfoDTO> arg0)throws Exception{
      return iERPIntegrationService.notifyERPCloseOrder(arg0);
   }


   @ApiOperation("通知ERP由ERP调度的运单已在电商修改成功")
   @PostMapping(value="/notifyECChangeWaybillCallback")
   public ERPResult<ECWaybillDTO> notifyECChangeWaybillCallback(@RequestBody ERPRequest<ECWaybillDTO> arg0)throws Exception{
      return iERPIntegrationService.notifyECChangeWaybillCallback(arg0);
   }


   @ApiOperation("ERP通知EC关闭运单")
   @PostMapping(value="/notifyECCloseWaybillCallback")
   public ERPResult<ECWaybillDTO> notifyECCloseWaybillCallback(@RequestBody ERPRequest<ECWaybillDTO> arg0)throws Exception{
      return iERPIntegrationService.notifyECCloseWaybillCallback(arg0);
   }


   @ApiOperation("通知ERP电商已完成运单二次支付")
   @PostMapping(value="/notifyECWaybillExcessCallback")
   public ERPResult<ECWaybillDTO> notifyECWaybillExcessCallback(@RequestBody ERPRequest<ECWaybillDTO> arg0)throws Exception{
      return iERPIntegrationService.notifyECWaybillExcessCallback(arg0);
   }


   @ApiOperation("通知ERP由ERP调度的运单已在电商创建成功")
   @PostMapping(value="/notifyECCreateWaybillCallback")
   public ERPResult<ECWaybillDTO> notifyECCreateWaybillCallback(@RequestBody ERPRequest<ECWaybillDTO> arg0)throws Exception{
      return iERPIntegrationService.notifyECCreateWaybillCallback(arg0);
   }


   @ApiOperation("关闭运单")
   @PostMapping(value="/notifyERPCloseWaybill")
   public ERPResult<ECWaybillDTO> notifyERPCloseWaybill(@RequestBody ERPRequest<ECWaybillDTO> arg0)throws Exception{
      return iERPIntegrationService.notifyERPCloseWaybill(arg0);
   }


   @ApiOperation("修改ERP运单")
   @PostMapping(value="/notifyERPChangeWaybill")
   public ERPResult<ECWaybillDTO> notifyERPChangeWaybill(@RequestBody ERPRequest<ECWaybillDTO> arg0)throws Exception{
      return iERPIntegrationService.notifyERPChangeWaybill(arg0);
   }

   @ApiOperation("获取ERP销售大区列表")
   @PostMapping(value="/queryErpSaleRegionList")
   public ItemResult<List<ERPSaleRegionOptionDTO>> queryErpSaleRegionList(@RequestBody ERPSaleRegionRequestDTO saleRegionRequestDTO)throws Exception{
      return iERPIntegrationService.queryErpSaleRegionList(saleRegionRequestDTO);
   }

}
