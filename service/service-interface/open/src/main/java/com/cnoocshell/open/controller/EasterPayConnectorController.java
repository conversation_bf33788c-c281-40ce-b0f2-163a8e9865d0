package com.ecommerce.open.controller;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.ApiOperation;
import com.ecommerce.open.service.payment.IEasterPayConnector;
import com.ecommerce.common.result.ItemResult;
import java.lang.String;
import com.ecommerce.open.api.dto.EasterPayNotifyResponseDTO;



@Api(tags={"easterPayConnector"},description = "TODO")
@RestController
@RequestMapping("/easterPayConnector")
public class EasterPayConnectorController {

   @Autowired 
   private IEasterPayConnector iEasterPayConnector;

   @ApiOperation("发送东方付通请求")
   @PostMapping(value="/easterPayPostRequest")
   public ItemResult<String> easterPayPostRequest(@RequestParam("jsonString") String jsonString,@RequestParam("paymentBillNo") String paymentBillNo) throws Exception{
      return iEasterPayConnector.easterPayPostRequest(jsonString,paymentBillNo);
   }

   @ApiOperation("获取东方付通回调结果通知")
   @PostMapping(value="/getEasterPayNotify")
   public ItemResult<EasterPayNotifyResponseDTO> getEasterPayNotify(@RequestParam("requestStr") String requestStr) throws Exception{
      return iEasterPayConnector.getEasterPayNotify(requestStr);
   }



}
