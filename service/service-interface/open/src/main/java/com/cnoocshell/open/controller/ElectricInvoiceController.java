package com.ecommerce.open.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.open.api.dto.invoice.AccountTokenDTO;
import com.ecommerce.open.api.dto.invoice.EnterpriseCardDTO;
import com.ecommerce.open.api.dto.invoice.InvoiceContentDTO;
import com.ecommerce.open.api.dto.invoice.InvoiceRequestDTO;
import com.ecommerce.open.api.dto.invoice.InvoiceRevokeDTO;
import com.ecommerce.open.api.dto.invoice.SendInvoiceDTO;
import com.ecommerce.open.api.dto.invoice.TaxCategoryDTO;
import com.ecommerce.open.api.dto.invoice.TokenApplyDTO;
import com.ecommerce.open.service.IElectricInvoiceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 电子发票服务
 */
@RestController
@RequestMapping("/electricInvoice")
@Api(tags={"ElectricInvoiceController"}, description = "电子发票服务")
public class ElectricInvoiceController {

    @Autowired
    private IElectricInvoiceService electricInvoiceService;

    @PostMapping(value="/applyToken")
    ItemResult<AccountTokenDTO> applyToken(@RequestBody TokenApplyDTO tokenApplyDTO) throws Exception{
        return electricInvoiceService.applyToken(tokenApplyDTO);
    }

    @PostMapping(value="/searchEnterpriseCardByKeyword")
    ItemResult<EnterpriseCardDTO> searchEnterpriseCardByKeyword(@RequestBody InvoiceRequestDTO<String> invoiceRequestDTO) throws Exception{
        return electricInvoiceService.searchEnterpriseCardByKeyword(invoiceRequestDTO);
    }

    @PostMapping(value="/queryTaxCategoryByName")
    ItemResult<TaxCategoryDTO> queryTaxCategoryByName(@RequestBody InvoiceRequestDTO<String> invoiceRequestDTO) throws Exception{
        return electricInvoiceService.queryTaxCategoryByName(invoiceRequestDTO);
    }

    @PostMapping(value="/directSendInvoice")
    ItemResult<InvoiceContentDTO> directSendInvoice(@RequestBody InvoiceRequestDTO<SendInvoiceDTO> invoiceRequestDTO) throws Exception{
        return electricInvoiceService.directSendInvoice(invoiceRequestDTO);
    }

    @PostMapping(value="/invoiceRevoke")
    ItemResult<Void> invoiceRevoke(@RequestBody InvoiceRequestDTO<InvoiceRevokeDTO> invoiceRequestDTO) throws Exception{
        return electricInvoiceService.invoiceRevoke(invoiceRequestDTO);
    }

}
