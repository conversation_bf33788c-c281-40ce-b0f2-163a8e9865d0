package com.ecommerce.open.controller;

import com.ecommerce.open.api.dto.email.EmailCodeSendDTO;
import com.ecommerce.open.service.IEmailSendService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 邮件发送服务
 *
 */
@Api(tags={"email"},description = "邮件发送")
@RestController
@RequestMapping("/email")
public class EmailSendController {

    @Autowired
    private IEmailSendService emailSendService;

    @ApiOperation("发送电子邮件验证码")
    @PostMapping(value="/sendEmailCode")
    public Boolean sendEmailCode(@RequestBody EmailCodeSendDTO dto){
        return emailSendService.sendEmailCode(dto);
    }

}
