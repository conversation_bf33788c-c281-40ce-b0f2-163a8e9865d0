package com.ecommerce.open.controller;

import com.ecommerce.open.service.IFilePutService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;


/**
 * 附件上传服务
 */

@Api(tags = { "FilePutController" }, description = "附件上传服务")
@RestController
@RequestMapping("/file")
@Slf4j
public class FilePutController {

	@Autowired
	private IFilePutService filePutService;

	@ApiOperation("文件上传")
	@PostMapping(value = "/put")
	public List<Map<String, String>> putFiles(
			@RequestParam(value = "fileName", required = false) String[] fileNames,
			@RequestParam(value = "accountId", required = true) String accountId,
			@RequestParam(value = "businessScenario", required = true) String businessScenario,
			@RequestParam(value = "bid", required = false) String bid,
			@RequestParam(value = "saveAttachmentInfo", defaultValue = "1" ) Boolean saveAttachmentInfo,
			@RequestParam(value = "ip", required = false) String ip,
			@RequestParam(value = "token", required = false) String token) throws Exception {
		return filePutService.putFiles(fileNames, accountId, businessScenario, bid, saveAttachmentInfo, ip,token);
	}
//
//	@Autowired
//	private MessageSendService messageSendService;
//
//	@ApiOperation("消息手工重试")
//	@GetMapping("/test")
//	public void test(@RequestParam("mqId") String mqId) throws Exception {
//		messageSendService.retryTest(mqId);
//	}
}
