package com.ecommerce.open.controller;

import com.ecommerce.open.api.dto.gnete.AcctBindBankCardRequestDTO;
import com.ecommerce.open.api.dto.gnete.AcctBindBankCardResponseDTO;
import com.ecommerce.open.api.dto.gnete.AcctCancelRequestDTO;
import com.ecommerce.open.api.dto.gnete.AcctCancelResponseDTO;
import com.ecommerce.open.api.dto.gnete.ApplyTicketRequestDTO;
import com.ecommerce.open.api.dto.gnete.ApplyTicketResponseDTO;
import com.ecommerce.open.api.dto.gnete.BankAcctSignApplyRequestDTO;
import com.ecommerce.open.api.dto.gnete.BankAcctSignApplyResponseDTO;
import com.ecommerce.open.api.dto.gnete.BankAcctSignCancelRequestDTO;
import com.ecommerce.open.api.dto.gnete.BankAcctSignCancelResponseDTO;
import com.ecommerce.open.api.dto.gnete.BankAcctSignConfirmRequestDTO;
import com.ecommerce.open.api.dto.gnete.BankAcctSignConfirmResponseDTO;
import com.ecommerce.open.api.dto.gnete.BatchTransferRequestDTO;
import com.ecommerce.open.api.dto.gnete.BatchTransferResponseDTO;
import com.ecommerce.open.api.dto.gnete.ConfigDTO;
import com.ecommerce.open.api.dto.gnete.ConsumeApplyOrderRequestDTO;
import com.ecommerce.open.api.dto.gnete.ConsumeApplyOrderResponseDTO;
import com.ecommerce.open.api.dto.gnete.ConsumePayRequestDTO;
import com.ecommerce.open.api.dto.gnete.ConsumePayResponseDTO;
import com.ecommerce.open.api.dto.gnete.ConsumeRefundRequestDTO;
import com.ecommerce.open.api.dto.gnete.ConsumeRefundResponseDTO;
import com.ecommerce.open.api.dto.gnete.CpsDsRechargeRequestDTO;
import com.ecommerce.open.api.dto.gnete.CpsDsRechargeResponseDTO;
import com.ecommerce.open.api.dto.gnete.GetPlugRandomKeyRequestDTO;
import com.ecommerce.open.api.dto.gnete.GetPlugRandomKeyResponseDTO;
import com.ecommerce.open.api.dto.gnete.GuaranteedPayNotifyDTO;
import com.ecommerce.open.api.dto.gnete.GuaranteedPayPageQuerySettRegRequestDTO;
import com.ecommerce.open.api.dto.gnete.GuaranteedPayPageQuerySettRegResponseDTO;
import com.ecommerce.open.api.dto.gnete.GuaranteedPayQuerySettRegDetailRequestDTO;
import com.ecommerce.open.api.dto.gnete.GuaranteedPayQuerySettRegDetailResponseDTO;
import com.ecommerce.open.api.dto.gnete.GuaranteedPayRefundQueryRequestDTO;
import com.ecommerce.open.api.dto.gnete.GuaranteedPayRefundQueryResponseDTO;
import com.ecommerce.open.api.dto.gnete.GuaranteedPayRefundRequestDTO;
import com.ecommerce.open.api.dto.gnete.GuaranteedPayRefundResponseDTO;
import com.ecommerce.open.api.dto.gnete.GuaranteedConfirmPayRequestDTO;
import com.ecommerce.open.api.dto.gnete.GuaranteedConfirmPayResponseDTO;
import com.ecommerce.open.api.dto.gnete.GuaranteedPayQueryOrderDetailRequestDTO;
import com.ecommerce.open.api.dto.gnete.GuaranteedPayQueryOrderDetailResponseDTO;
import com.ecommerce.open.api.dto.gnete.GuaranteedPayRequestDTO;
import com.ecommerce.open.api.dto.gnete.GuaranteedPayResponseDTO;
import com.ecommerce.open.api.dto.gnete.GuaranteedPayProfitSharingRefundRequestDTO;
import com.ecommerce.open.api.dto.gnete.GuaranteedPayProfitSharingRefundResponseDTO;
import com.ecommerce.open.api.dto.gnete.HzfProfitSharingRefundRequestDTO;
import com.ecommerce.open.api.dto.gnete.HzfProfitSharingRefundResponseDTO;
import com.ecommerce.open.api.dto.gnete.HzfQueryTransRequestDTO;
import com.ecommerce.open.api.dto.gnete.HzfQueryTransResponseDTO;
import com.ecommerce.open.api.dto.gnete.ModifyPwdRequestDTO;
import com.ecommerce.open.api.dto.gnete.ModifyPwdResponseDTO;
import com.ecommerce.open.api.dto.gnete.OpenAcctRequestDTO;
import com.ecommerce.open.api.dto.gnete.OpenAcctResponseDTO;
import com.ecommerce.open.api.dto.gnete.QueryAcctBalRequestDTO;
import com.ecommerce.open.api.dto.gnete.QueryAcctBalResponseDTO;
import com.ecommerce.open.api.dto.gnete.QueryAcctRelatedInfoRequestDTO;
import com.ecommerce.open.api.dto.gnete.QueryAcctRelatedInfoResponseDTO;
import com.ecommerce.open.api.dto.gnete.QueryBatchTransResultRequestDTO;
import com.ecommerce.open.api.dto.gnete.QueryBatchTransResultResponseDTO;
import com.ecommerce.open.api.dto.gnete.QueryBindBankCardRequestDTO;
import com.ecommerce.open.api.dto.gnete.QueryBindBankCardResponseDTO;
import com.ecommerce.open.api.dto.gnete.GuaranteedPayPageQueryOrderInfoRequestDTO;
import com.ecommerce.open.api.dto.gnete.GuaranteedPayPageQueryOrderInfoResponseDTO;
import com.ecommerce.open.api.dto.gnete.QueryHzfProfitSharingRefundRequestDTO;
import com.ecommerce.open.api.dto.gnete.QueryHzfProfitSharingRefundResponseDTO;
import com.ecommerce.open.api.dto.gnete.QueryProfitSharingMerRelRequestDTO;
import com.ecommerce.open.api.dto.gnete.QueryProfitSharingMerRelResponseDTO;
import com.ecommerce.open.api.dto.gnete.QueryProfitSharingRuleRequestDTO;
import com.ecommerce.open.api.dto.gnete.QueryProfitSharingRuleResponseDTO;
import com.ecommerce.open.api.dto.gnete.QueryRegInfRequestDTO;
import com.ecommerce.open.api.dto.gnete.QueryRegInfResponseDTO;
import com.ecommerce.open.api.dto.gnete.QueryTransResultRequestDTO;
import com.ecommerce.open.api.dto.gnete.QueryTransResultResponseDTO;
import com.ecommerce.open.api.dto.gnete.QueryWalleIdRequestDTO;
import com.ecommerce.open.api.dto.gnete.QueryWalleIdResponseDTO;
import com.ecommerce.open.api.dto.gnete.QueryWithholdGrantRequestDTO;
import com.ecommerce.open.api.dto.gnete.QueryWithholdGrantResponseDTO;
import com.ecommerce.open.api.dto.gnete.ReceivableRequestDTO;
import com.ecommerce.open.api.dto.gnete.ReceivableResponseDTO;
import com.ecommerce.open.api.dto.gnete.RefundTransRequestDTO;
import com.ecommerce.open.api.dto.gnete.RefundTransResponseDTO;
import com.ecommerce.open.api.dto.gnete.ResetBtypeAcctPwdRequestDTO;
import com.ecommerce.open.api.dto.gnete.ResetBtypeAcctPwdResponseDTO;
import com.ecommerce.open.api.dto.gnete.SendSmsAuthCodeRequestDTO;
import com.ecommerce.open.api.dto.gnete.SendSmsAuthCodeResponseDTO;
import com.ecommerce.open.api.dto.gnete.TransferRequestDTO;
import com.ecommerce.open.api.dto.gnete.TransferResponseDTO;
import com.ecommerce.open.api.dto.gnete.ValidSmsAuthCodeRequestDTO;
import com.ecommerce.open.api.dto.gnete.ValidSmsAuthCodeResponseDTO;
import com.ecommerce.open.api.dto.gnete.ValidateTicketRequestDTO;
import com.ecommerce.open.api.dto.gnete.ValidateTicketResponsetDTO;
import com.ecommerce.open.api.dto.gnete.WithdrawRequestDTO;
import com.ecommerce.open.api.dto.gnete.WithdrawResponseDTO;
import com.ecommerce.open.api.dto.gnete.WithholdGrantRequestDTO;
import com.ecommerce.open.api.dto.gnete.WithholdGrantResponseDTO;
import com.ecommerce.open.api.dto.gnete.aggregate.pay.ClosePayOrderRequestDTO;
import com.ecommerce.open.api.dto.gnete.aggregate.pay.ClosePayOrderResponseDTO;
import com.ecommerce.open.api.dto.gnete.aggregate.pay.PrePayOrderRequestDTO;
import com.ecommerce.open.api.dto.gnete.aggregate.pay.PrePayOrderResponseDTO;
import com.ecommerce.open.api.dto.gnete.aggregate.pay.QueryPayOrderRequestDTO;
import com.ecommerce.open.api.dto.gnete.aggregate.pay.QueryPayOrderResponseDTO;
import com.ecommerce.open.api.dto.gnete.aggregate.pay.RefundQueryRequestDTO;
import com.ecommerce.open.api.dto.gnete.aggregate.pay.RefundQueryResponseDTO;
import com.ecommerce.open.api.dto.gnete.aggregate.pay.RefundRequestDTO;
import com.ecommerce.open.api.dto.gnete.aggregate.pay.RefundResponseDTO;
import com.ecommerce.open.service.payment.IGneteAggregatePayConnector;
import com.ecommerce.open.service.payment.IGneteConnector;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;


/**
 * 银联支付接口
 **/
@Api(tags={"IGneteConnector"},description = "银联支付接口")
@RestController
@RequestMapping("/gneteConnector")
public class GneteConnectorController {

   @Autowired 
   private IGneteConnector gneteConnector;
   @Autowired
   private IGneteAggregatePayConnector gneteAggregateC2BConnector;

   @ApiOperation("4.1获取凭据接口")
   @PostMapping(value = "/applyTicket")
   public ApplyTicketResponseDTO applyTicket(@RequestBody ApplyTicketRequestDTO dto){
      return gneteConnector.applyTicket(dto);
   }

   @ApiOperation("4.2验证凭据接口")
   @PostMapping(value = "/validateTicket")
   public ValidateTicketResponsetDTO validateTicket(@RequestBody ValidateTicketRequestDTO dto){
      return gneteConnector.validateTicket(dto);
   }

   @ApiOperation("4.3外部用户查询钱包ID")
   @PostMapping(value = "/queryWalletId")
   public QueryWalleIdResponseDTO queryWalletId(@RequestBody QueryWalleIdRequestDTO dto){
      return gneteConnector.queryWalletId(dto);
   }

   @ApiOperation("4.4查询注册登记信息 主要应用于B端H5/PC开户后，使用登记手机号或注册号查询注册登记信息。")
   @PostMapping(value = "/queryRegInf")
   public QueryRegInfResponseDTO queryRegInf(@RequestBody QueryRegInfRequestDTO dto){
      return gneteConnector.queryRegInf(dto);
   }

   @ApiOperation("1003 查询账户关联信息,主要指用户相关信息,只允许查询C端钱包用户信息")
   @PostMapping(value = "/queryAcctRelatedInfo")
   public QueryAcctRelatedInfoResponseDTO queryAcctRelatedInfo(@RequestBody QueryAcctRelatedInfoRequestDTO dto){
      return gneteConnector.queryAcctRelatedInfo(dto);
   }

   @ApiOperation("2001 5.1开户（C端）")
   @PostMapping(value = "/openAcct")
   public OpenAcctResponseDTO openAcct(@RequestBody OpenAcctRequestDTO dto){
      return gneteConnector.openAcct(dto);
   }

   @ApiOperation("修改支付密码")
   @PostMapping(value = "/modifyPwd")
   public ModifyPwdResponseDTO modifyPwd(@RequestBody ModifyPwdRequestDTO dto){
      return gneteConnector.modifyPwd(dto);
   }

   @ApiOperation("2007 5.3提现")
   @PostMapping(value = "/withdraw")
   public WithdrawResponseDTO withdraw(@RequestBody WithdrawRequestDTO dto){
      return gneteConnector.withdraw(dto);
   }

   @ApiOperation("1004 5.4查询账户余额")
   @PostMapping(value = "/queryAcctBal")
   public QueryAcctBalResponseDTO queryAcctBal(@RequestBody QueryAcctBalRequestDTO dto){
      return gneteConnector.queryAcctBal(dto);
   }

   @ApiOperation("2103 担保支付-下单")
   @PostMapping(value = "/guaranteedPay")
   public GuaranteedPayResponseDTO guaranteedPay(@RequestBody GuaranteedPayRequestDTO dto){
      return gneteConnector.guaranteedPay(dto);
   }

   @ApiOperation("2107 担保支付-确认（分账）")
   @PostMapping(value = "/guaranteedConfirmPay")
   public GuaranteedConfirmPayResponseDTO guaranteedConfirmPay(@RequestBody GuaranteedConfirmPayRequestDTO dto){
      return gneteConnector.guaranteedConfirmPay(dto);
   }

   @ApiOperation("担保支付-统一回调处理入口")
   @PostMapping(value = "/guaranteedPayNotify")
   public String guaranteedPayNotify(@RequestBody GuaranteedPayNotifyDTO dto){
      return gneteConnector.guaranteedPayNotify(dto);
   }

   @ApiOperation("1051 担保支付详情查询")
   @PostMapping(value = "/guaranteedPayQueryOrderDetail")
   public GuaranteedPayQueryOrderDetailResponseDTO guaranteedPayQueryOrderDetail(@RequestBody GuaranteedPayQueryOrderDetailRequestDTO dto){
      return gneteConnector.guaranteedPayQueryOrderDetail(dto);
   }

   @ApiOperation("1052 担保支付-分页查询担保支付订单")
   @PostMapping(value = "/guaranteedPayPageQueryOrderInfo")
   public GuaranteedPayPageQueryOrderInfoResponseDTO guaranteedPayPageQueryOrderInfo(@RequestBody GuaranteedPayPageQueryOrderInfoRequestDTO dto){
      return gneteConnector.guaranteedPayPageQueryOrderInfo(dto);
   }

   @ApiOperation("2106 担保支付-退款(仅支持未分账的情况,相当于取消支付动作)")
   @PostMapping(value = "/guaranteedPayRefund")
   public GuaranteedPayRefundResponseDTO guaranteedPayRefund(@RequestBody GuaranteedPayRefundRequestDTO dto){
      return gneteConnector.guaranteedPayRefund(dto);
   }

   @ApiOperation("2110 担保支付-分账完结后的退款(订单结束后，退货产生的退款调用此方法)")
   @PostMapping(value = "/guaranteedPayProfitSharingRefund")
   public GuaranteedPayProfitSharingRefundResponseDTO guaranteedPayProfitSharingRefund(@RequestBody GuaranteedPayProfitSharingRefundRequestDTO dto){
      return gneteConnector.guaranteedPayProfitSharingRefund(dto);
   }

   @ApiOperation("1054 担保支付-退款查询")
   @PostMapping(value = "/guaranteedPayQueryRefundInfo")
   public GuaranteedPayRefundQueryResponseDTO guaranteedPayQueryRefundInfo(@RequestBody GuaranteedPayRefundQueryRequestDTO dto){
      return gneteConnector.guaranteedPayQueryRefundInfo(dto);
   }

   @ApiOperation("1055 担保支付-查询结算登记（结算单）明细（查询分账记录）(可根据结算单号（settOrderNo）条件查询明细，或者根据mctOrderNo + platformWalletId + bizType查询查询明细。（两种方式二选一）)")
   @PostMapping(value = "/guaranteedQuerySettRegDetail")
   public GuaranteedPayQuerySettRegDetailResponseDTO guaranteedQuerySettRegDetail(@RequestBody GuaranteedPayQuerySettRegDetailRequestDTO dto){
      return gneteConnector.guaranteedQuerySettRegDetail(dto);
   }

   @ApiOperation("1056 担保支付-分页查询结算登记（结算单）（分页查询分账记录））")
   @PostMapping(value = "/guaranteedPageQuerySettReg")
   public GuaranteedPayPageQuerySettRegResponseDTO guaranteedPageQuerySettReg(@RequestBody GuaranteedPayPageQuerySettRegRequestDTO dto){
      return gneteConnector.guaranteedPageQuerySettReg(dto);
   }

   @ApiOperation("1057 担保支付-查询分账规则")
   @PostMapping(value = "/queryProfitSharingRule")
   public QueryProfitSharingRuleResponseDTO queryProfitSharingRule(@RequestBody QueryProfitSharingRuleRequestDTO dto){
      return gneteConnector.queryProfitSharingRule(dto);
   }

   @ApiOperation("1058 担保支付-查询分账方")
   @PostMapping(value = "/queryProfitSharingMerRel")
   public QueryProfitSharingMerRelResponseDTO queryProfitSharingMerRel(@RequestBody QueryProfitSharingMerRelRequestDTO dto){
      return gneteConnector.queryProfitSharingMerRel(dto);
   }

   @ApiOperation("2015 5.8转账接口")
   @PostMapping(value = "/transfer")
   public TransferResponseDTO transfer(@RequestBody TransferRequestDTO dto){
      return gneteConnector.transfer(dto);
   }

   @ApiOperation("2027 批量转账接口(平台-->司机)")
   @PostMapping(value = "/batchTransfer")
   public BatchTransferResponseDTO batchTransfer(@RequestBody BatchTransferRequestDTO dto){
      return gneteConnector.batchTransfer(dto);
   }

   @ApiOperation("1010 批量转账结果查询")
   @PostMapping(value = "/queryBatchTransResult")
   public QueryBatchTransResultResponseDTO queryBatchTransResult(@RequestBody QueryBatchTransResultRequestDTO dto){
      return gneteConnector.queryBatchTransResult(dto);
   }

   @ApiOperation("2023 授权签约/解约 授权给企业根据协议定期从其钱包账户中扣取相应款项，满足C2B或B2B免密扣款。)")
   @PostMapping(value = "/withholdGrant")
   public WithholdGrantResponseDTO withholdGrant(@RequestBody WithholdGrantRequestDTO dto){
      return gneteConnector.withholdGrant(dto);
   }

   @ApiOperation("1009 查询授权签约详情")
   @PostMapping(value = "/queryWithholdGrant")
   public QueryWithholdGrantResponseDTO queryWithholdGrant(@RequestBody QueryWithholdGrantRequestDTO dto){
      return gneteConnector.queryWithholdGrant(dto);
   }

   @ApiOperation("2024 单笔收款(卖家-->平台) 授权给企业根据协议定期从其钱包账户中扣取相应款项，满足C2B或B2B免密扣款。企业可以对已经授权的账户批量发起收款。被授权方扣授权方的钱。")
   @PostMapping(value = "/receivable")
   public ReceivableResponseDTO receivable(@RequestBody ReceivableRequestDTO dto){
      return gneteConnector.receivable(dto);
   }

   @ApiOperation("1005 5.11查询交易结果")
   @PostMapping(value = "/queryTransResult")
   public QueryTransResultResponseDTO queryTransResult(@RequestBody QueryTransResultRequestDTO dto){
      return gneteConnector.queryTransResult(dto);
   }

   @ApiOperation("2046 获取随机控件因子(单次申请上限个数为100个，默认申请为1个，有效期为24小时,交易时，只能使用一次)")
   @PostMapping(value = "/getPlugRandomKey")
   public GetPlugRandomKeyResponseDTO getPlugRandomKey(@RequestBody GetPlugRandomKeyRequestDTO dto){
      return gneteConnector.getPlugRandomKey(dto);
   }

   @ApiOperation("2020 获取随机控件因子(单次申请上限个数为100个，默认申请为1个，有效期为24小时,交易时，只能使用一次)")
   @PostMapping(value = "/sendSmsAuthCode")
   public SendSmsAuthCodeResponseDTO sendSmsAuthCode(@RequestBody SendSmsAuthCodeRequestDTO dto){
      return gneteConnector.sendSmsAuthCode(dto);
   }

   @ApiOperation("2021 向电子钱包业务中心请求验证短信验证码")
   @PostMapping(value = "/validSmsAuthCode")
   public ValidSmsAuthCodeResponseDTO validSmsAuthCode(@RequestBody ValidSmsAuthCodeRequestDTO dto){
      return gneteConnector.validSmsAuthCode(dto);
   }

   @ApiOperation("2019 账户绑定/解绑/设置默认银行卡")
   @PostMapping(value = "/acctBindBankCard")
   public AcctBindBankCardResponseDTO acctBindBankCard(@RequestBody AcctBindBankCardRequestDTO dto){
      return gneteConnector.acctBindBankCard(dto);
   }

   @ApiOperation("1007 查询账户绑定的银行卡信息")
   @PostMapping(value = "/queryBindBankCard")
   public QueryBindBankCardResponseDTO queryBindBankCard(@RequestBody QueryBindBankCardRequestDTO dto){
      return gneteConnector.queryBindBankCard(dto);
   }

   @ApiOperation("2066 注销(只能注销个人钱包，且是在注册7天后且无交易)")
   @PostMapping(value = "/acctCancel")
   public AcctCancelResponseDTO acctCancel(@RequestBody AcctCancelRequestDTO dto){
      return gneteConnector.acctCancel(dto);
   }

   @ApiOperation("2039 退款")
   @PostMapping(value = "/refundTrans")
   public RefundTransResponseDTO refundTrans(@RequestBody RefundTransRequestDTO dto){
      return gneteConnector.refundTrans(dto);
   }

   @ApiOperation("4.4.2 消费类下单接口（报文编码2056）")
   @PostMapping(value = "/consumeApplyOrder")
   public ConsumeApplyOrderResponseDTO consumeApplyOrder(@RequestBody ConsumeApplyOrderRequestDTO dto){
      return gneteConnector.consumeApplyOrder(dto);
   }

   @ApiOperation("4.4.3 消费类支付（报文编码2057）(当前仅用于缴纳保证金)")
   @PostMapping(value = "/consumePay")
   public ConsumePayResponseDTO consumePay(@RequestBody ConsumePayRequestDTO dto){
      return gneteConnector.consumePay(dto);
   }

   @ApiOperation("4.4.4 消费类退款（报文编码2058）(当前仅用于退还保证金)")
   @PostMapping(value = "/consumeRefund")
   public ConsumeRefundResponseDTO consumeRefund(@RequestBody ConsumeRefundRequestDTO dto){
      return gneteConnector.consumeRefund(dto);
   }

   @ApiOperation("2045 B端钱包重置支付密码（报文编码2045）(C端用户只能调用银联的H5页面)")
   @PostMapping(value = "/resetBtypeAcctPwd")
   public ResetBtypeAcctPwdResponseDTO resetBtypeAcctPwd(@RequestBody ResetBtypeAcctPwdRequestDTO dto){
      return gneteConnector.resetBtypeAcctPwd(dto);
   }

   @ApiOperation("2034 充值-银行卡签约申请接口")
   @PostMapping(value = "/bankAcctSignApply")
   public BankAcctSignApplyResponseDTO bankAcctSignApply(@RequestBody BankAcctSignApplyRequestDTO dto){
      return gneteConnector.bankAcctSignApply(dto);
   }

   @ApiOperation("2035 充值-银行卡签约确认接口")
   @PostMapping(value = "/bankAcctSignConfirm")
   public BankAcctSignConfirmResponseDTO bankAcctSignConfirm(@RequestBody BankAcctSignConfirmRequestDTO dto){
      return gneteConnector.bankAcctSignConfirm(dto);
   }

   @ApiOperation("2036 充值-银行卡解约约接口")
   @PostMapping(value = "/bankAcctSignCancel")
   public BankAcctSignCancelResponseDTO bankAcctSignCancel(@RequestBody BankAcctSignCancelRequestDTO dto){
      return gneteConnector.bankAcctSignCancel(dto);
   }

   @ApiOperation("2037 充值-银行卡-代扣充值(从用户签约的银行卡扣款，充值到钱包余额)")
   @PostMapping(value = "/cpsDsRecharge")
   public CpsDsRechargeResponseDTO cpsDsRecharge(@RequestBody CpsDsRechargeRequestDTO dto){
      return gneteConnector.cpsDsRecharge(dto);
   }

   @ApiOperation("聚合支付-下单/二维码获取")
   @PostMapping(value = "/aggregatePay/prePayOrder")
   public PrePayOrderResponseDTO aggregatePayPrePayOrder(@RequestBody PrePayOrderRequestDTO dto){
      return gneteAggregateC2BConnector.prePayOrder(dto);
   }

   @ApiOperation("聚合支付-订单交易查询/账单状态查询")
   @PostMapping(value = "/aggregatePay/queryPayOrder")
   public QueryPayOrderResponseDTO aggregatePayQueryPayOrder(@RequestBody QueryPayOrderRequestDTO dto){
      return gneteAggregateC2BConnector.queryPayOrder(dto);
   }

   @ApiOperation("聚合支付-退款")
   @PostMapping(value = "/aggregatePay/refund")
   public RefundResponseDTO aggregatePayRefund(@RequestBody RefundRequestDTO dto){
      return gneteAggregateC2BConnector.refund(dto);
   }

   @ApiOperation("聚合支付-退款查询")
   @PostMapping(value = "/aggregatePay/refundQuery")
   public RefundQueryResponseDTO aggregatePayRefundQuery(@RequestBody RefundQueryRequestDTO dto){
      return gneteAggregateC2BConnector.refundQuery(dto);
   }

   @ApiOperation("聚合支付-二维码关闭")
   @PostMapping(value = "/aggregatePay/closePayOrder")
   public ClosePayOrderResponseDTO aggregatePayClosePayOrder(@RequestBody ClosePayOrderRequestDTO dto){
      return gneteAggregateC2BConnector.closePayOrder(dto);
   }

   @ApiOperation("聚合支付-支付结果通知")
   @PostMapping(value = "/aggregatePay/notify")
   public String aggregatePayNotify(@RequestBody Map<String, String> paramsMap){
      return gneteAggregateC2BConnector.payNotify(paramsMap);
   }

   @ApiOperation("2112 好支付分账退款接口(即银联聚合支付分账退款和退货退款)")
   @PostMapping(value = "/hzfProfitSharingRefund")
   public HzfProfitSharingRefundResponseDTO hzfProfitSharingRefund(@RequestBody HzfProfitSharingRefundRequestDTO dto){
      return gneteConnector.hzfProfitSharingRefund(dto);
   }

   @ApiOperation("1063 查询好支付分账退款(即银联聚合支付分账退款和退货退款)")
   @PostMapping(value = "/queryHzfProfitSharingRefund")
   public QueryHzfProfitSharingRefundResponseDTO queryHzfProfitSharingRefund(@RequestBody QueryHzfProfitSharingRefundRequestDTO dto){
      return gneteConnector.queryHzfProfitSharingRefund(dto);
   }

   @ApiOperation("1062 分页查询好支付原交易")
   @PostMapping(value = "/hzfQueryTrans")
   public HzfQueryTransResponseDTO hzfQueryTrans(@RequestBody HzfQueryTransRequestDTO dto){
      return gneteConnector.hzfQueryTrans(dto);
   }

   @ApiOperation("获取配置参数(供pay使用，银联支付配置相关参数统一设置到open)")
   @PostMapping(value = "/getConfig")
   public ConfigDTO getConfig(){
      return gneteConnector.getConfig();
   }

   @ApiOperation("打印配置参数")
   @PostMapping(value = "/printConfig")
   public void printConfig(){
      gneteConnector.printConfig();
   }
}
