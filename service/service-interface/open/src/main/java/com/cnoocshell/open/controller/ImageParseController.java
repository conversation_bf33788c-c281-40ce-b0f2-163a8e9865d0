package com.ecommerce.open.controller;

import com.ecommerce.open.api.dto.OcrDTO;
import com.ecommerce.open.service.IImageParseService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Set;


/**
 * 供base CloudService使用
*/
@Api(tags={"ImageParse"},description = "图片识别")
@RestController
@RequestMapping("/imageParse")
public class ImageParseController {

   @Autowired 
   private IImageParseService imageParseService;

   @ApiOperation("阿里云图片ocr识别")
   @PostMapping(value="/parseImageByAli")
   public String parseImageByAli(@RequestParam("url") String url,@RequestParam("appCode") String appCode,@RequestParam("type") Integer type,@RequestParam("methodName") String methodName){
      return imageParseService.parseImageByAli(url,appCode,type,methodName);
   }


   @ApiOperation("腾讯云图片ocr识别")
   @PostMapping(value="/parseImageTencent")
   public String parseImageTencent(@RequestBody OcrDTO ocrDTO){
      return imageParseService.parseImageTencent(ocrDTO.getBucketName(),ocrDTO.getUrl(),ocrDTO.getAppId(),ocrDTO.getSecretId(),ocrDTO.getSecretKey(),ocrDTO.getMethodName(),ocrDTO.getType());
   }

    @ApiOperation("华为云图片ocr识别")
    @PostMapping(value="/parseImageHuawei")
    public String parseImageHuawei(@RequestParam("accessKeyId") String accessKeyId,@RequestParam("accessSecretKey") String accessSecretKey,@RequestParam("regionName") String regionName,
                                   @RequestParam("httpUri") String httpUri, @RequestParam("imgPath") String imgPath,@RequestParam(value = "type",required = false) Integer type){
        return imageParseService.parseImageHuawei(accessKeyId,accessSecretKey,regionName, httpUri, imgPath,type);
    }

    /**
    * cdn文件刷新
    * @param urls
    */
    @ApiOperation("腾讯云cdn文件刷新")
    @PostMapping(value="/refreshUrl")
    public Boolean refreshUrl(@RequestBody  Set<String> urls,@RequestParam("secretId") String secretId,@RequestParam("secretKey") String secretKey,@RequestParam("cloudName") String cloudName){
       return imageParseService.refreshUrl(urls,secretId,secretKey,cloudName);
   }
}
