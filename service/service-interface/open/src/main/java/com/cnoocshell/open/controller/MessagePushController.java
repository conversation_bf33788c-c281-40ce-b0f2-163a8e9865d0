package com.ecommerce.open.controller;

import com.ecommerce.open.api.dto.MessagePushDTO;
import com.ecommerce.open.service.IMessagePushService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 消息推送服务
 */
@RestController
@RequestMapping("/messagePush")
@Api(tags={"MessagePushController"}, description = "消息推送服务")
public class MessagePushController {
    @Autowired
    private IMessagePushService messagePushService;

    @PostMapping(value="/pushAndriodMessage")
    public String pushAndriodMessage(@RequestBody MessagePushDTO messagePushDTO)throws Exception{
        return messagePushService.pushAndriodMessage(messagePushDTO);
    }

    @PostMapping(value="/pushIOSMessage")
    public String pushIOSMessage(@RequestBody MessagePushDTO messagePushDTO)throws Exception{
        return messagePushService.pushIOSMessage(messagePushDTO);
    }
}
