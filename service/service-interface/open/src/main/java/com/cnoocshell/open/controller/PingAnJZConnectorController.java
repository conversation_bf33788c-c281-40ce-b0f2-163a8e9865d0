package com.ecommerce.open.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.open.api.dto.pinganjz.AccountRegulationRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.AccountRegulationResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.ApplicationTextMsgDynamicCodeRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.ApplicationTextMsgDynamicCodeResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.ApplyForChangeOfCellPhoneNumRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.ApplyForChangeOfCellPhoneNumResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.BackfillDynamicPasswordRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.BackfillDynamicPasswordResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.BatChseqNoVerifiedTextMsgsRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.BatChseqNoVerifiedTextMsgsResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.BindBankRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.BindBankResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.BindRelateAccReUnionPayRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.BindRelateAccReUnionPayResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.ClearingCorpInfoCheckRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.ClearingCorpInfoSendRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.MbrPropertyReviseRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.MbrPropertyReviseResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.MbrTchFrzVerifiedTextMsgsRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.MbrTchFrzVerifiedTextMsgsResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.MbrTchPayVerifiedTextMsgsRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.MbrTchPayVerifiedTextMsgsResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.MemberTranVerifyTextMsgsRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.MemberTranVerifyTextMsgsResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.MemberTransactionRefundRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.MemberTransactionRefundResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.MemberTransactionRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.MemberTransactionResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.MemberTransactionsInspectionRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.MemberTransactionsInspectionResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.MemberWithdrawCashRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.MemberWithdrawCashResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.MemberWithdrawSprtFeePwdVerifyRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.MemberWithdrawSprtFeePwdVerifyResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.MntMbrBindRelateAcctBankCodeRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.MntMbrBindRelateAcctBankCodeResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.OpenMemberChannelRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.OpenMemberChannelResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.PlatformAccountSupplyRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.PlatformAccountSupplyResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.PlatformOrderManagementRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.PlatformOrderManagementResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.TrancheFreezeInspectionRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.TrancheFreezeInspectionResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.TranchePayInspectionRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.TranchePayInspectionResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.UnbindRelateAcctRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.UnbindRelateAcctResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.ValidateBindBankRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.ValidateBindBankResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.aggregate.DownloadBillRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.aggregate.PayCancelRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.aggregate.PayCancelResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.aggregate.PayOrderRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.aggregate.PayOrderResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.aggregate.PayRefundRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.aggregate.PayRefundResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.aggregate.PayStatusRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.aggregate.PayStatusResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.aggregate.ReconciliationBillDTO;
import com.ecommerce.open.api.dto.pinganjz.aggregate.RefundStatusRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.aggregate.RefundStatusResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.fastpay.RechargeRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.fastpay.RechargeResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.password.PasswordFormRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.password.PasswordFormResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.query.BalanceQueryRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.query.BalanceQueryResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.query.BankClearQueryRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.query.BankClearQueryResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.query.BankCostDsDealResultQueryRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.query.BankCostDsDealResultQueryResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.query.BankTransactionDetailsQueryRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.query.BankTransactionDetailsQueryResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.query.BankWithDrawCashBackQueryRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.query.BankWithDrawCashBackQueryResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.query.BankWithdrawCashDetailsQueryRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.query.BankWithdrawCashDetailsQueryResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.query.ChargeDetailQueryRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.query.ChargeDetailQueryResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.query.CommonTransferRechargeQueryRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.query.CommonTransferRechargeQueryResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.query.CustAcctIdHistoryBalanceQueryRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.query.CustAcctIdHistoryBalanceQueryResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.query.DetailVerifiedCodeQueryRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.query.DetailVerifiedCodeQueryResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.query.MemberAccountQueryRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.query.MemberAccountQueryResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.query.MemberBalanceQueryRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.query.MemberBalanceQueryResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.query.MemberBindQueryRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.query.MemberBindQueryResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.query.PlatFormBalanceQueryRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.query.PlatFormBalanceQueryResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.query.QueryCustAcctIdRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.query.QueryCustAcctIdResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.query.ReconciliationDocumentQueryRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.query.ReconciliationDocumentQueryResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.query.RegisterBehaviorRecordRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.query.SingleTransactionStatusQueryRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.query.SingleTransactionStatusQueryResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.query.SmallAmountTransferQueryRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.query.SmallAmountTransferQueryResponseDTO;
import com.ecommerce.open.service.payment.IPingAnJZConnector;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;



@Api(tags={"IPingAnJZConnector"},description = ": TODO")
@RestController
@RequestMapping("/pingAnJZConnector")
public class PingAnJZConnectorController {

   @Autowired
   private IPingAnJZConnector pingAnJZConnector;

   @ApiOperation("文件下载")
   @PostMapping(value="/fileDownload")
   public byte[] fileDownload(String filePath, String fileName, String drawCode, String randomPassword){
      return pingAnJZConnector.fileDownload(filePath, fileName, drawCode, randomPassword);
   }

   @ApiOperation("开通子账户 6000")
   @PostMapping(value="/openOrCloseMemberChannel")
   public OpenMemberChannelResponseDTO openOrCloseMemberChannel(@RequestBody OpenMemberChannelRequestDTO requestDTO){
      return pingAnJZConnector.openOrCloseMemberChannel(requestDTO);
   }

   @PostMapping(value="/findPlatformBalance")
   public PlatFormBalanceQueryResponseDTO findPlatformBalance(@RequestBody PlatFormBalanceQueryRequestDTO requestDTO){
      return pingAnJZConnector.findPlatformBalance(requestDTO);
   }

   @ApiOperation("查询资金总帐户余额 6011")
   @PostMapping(value="/findMemberAccount")
   public MemberAccountQueryResponseDTO findMemberAccount(@RequestBody MemberAccountQueryRequestDTO requestDTO){
      return pingAnJZConnector.findMemberAccount(requestDTO);
   }

   @ApiOperation("会员绑定提现账户-银联鉴权 6238")
   @PostMapping(value="/bindRelateAcctUnionPay")
   public BindBankResponseDTO bindRelateAcctUnionPay(@RequestBody BindBankRequestDTO bindBankRequestDTO){
      return pingAnJZConnector.bindRelateAcctUnionPay(bindBankRequestDTO);
   }

   @ApiOperation("会员绑定提现账户-回填银联鉴权短信码 6239")
   @PostMapping(value="/bindRelateAccReUnionPay")
   public BindRelateAccReUnionPayResponseDTO bindRelateAccReUnionPay(@RequestBody BindRelateAccReUnionPayRequestDTO requestDTO){
      return pingAnJZConnector.bindRelateAccReUnionPay(requestDTO);
   }

   @ApiOperation("会员绑定提现账户-小额鉴权 6240")
   @PostMapping(value="/bindBankWithSmallAmount")
   public BindBankResponseDTO bindBankWithSmallAmount(@RequestBody BindBankRequestDTO bindBankRequestDTO){
      return pingAnJZConnector.bindBankWithSmallAmount(bindBankRequestDTO);
   }

   @ApiOperation("验证鉴权金额 6241")
   @PostMapping(value="/validateBindBankWithSmallAmount")
   public ValidateBindBankResponseDTO validateBindBankWithSmallAmount(@RequestBody ValidateBindBankRequestDTO bindBankRequestDTO){
      return pingAnJZConnector.validateBindBankWithSmallAmount(bindBankRequestDTO);
   }

   @ApiOperation("查询会员子账号余额 6093")
   @PostMapping(value="/queryCustAcctIdBalance")
   public MemberBalanceQueryResponseDTO queryCustAcctIdBalance(@RequestBody MemberBalanceQueryRequestDTO requestDTO){
      return pingAnJZConnector.queryCustAcctIdBalance(requestDTO);
   }

   @ApiOperation("会员交易-验密 6006")
   @PostMapping(value="/memberTransactionsInspection")
   public MemberTransactionsInspectionResponseDTO memberTransactionsInspection(@RequestBody MemberTransactionsInspectionRequestDTO requestDTO){
      return pingAnJZConnector.memberTransactionsInspection(requestDTO);
   }

   @ApiOperation("会员交易-不验密 6034")
   @PostMapping(value="/memberTransaction")
   public MemberTransactionResponseDTO memberTransaction(@RequestBody MemberTransactionRequestDTO requestDTO){
      return pingAnJZConnector.memberTransaction(requestDTO);
   }

   @ApiOperation("会员资金冻结-支付密码验密 6134")
   @PostMapping(value="/mbrTrancheFreezeInspection")
   public TrancheFreezeInspectionResponseDTO mbrTrancheFreezeInspection(@RequestBody TrancheFreezeInspectionRequestDTO requestDTO){
      return pingAnJZConnector.mbrTrancheFreezeInspection(requestDTO);
   }

   @ApiOperation("会员资金冻结-不验密 6007")
   @PostMapping(value="/mbrTrancheFreeze")
   public TrancheFreezeInspectionResponseDTO mbrTrancheFreeze(@RequestBody TrancheFreezeInspectionRequestDTO requestDTO){
      return pingAnJZConnector.mbrTrancheFreeze(requestDTO);
   }

   @ApiOperation("会员资金支付-支付密码验密 6165")
   @PostMapping(value="/mbrTranchePayInspection")
   public TranchePayInspectionResponseDTO mbrTranchePayInspection(@RequestBody TranchePayInspectionRequestDTO requestDTO){
      return pingAnJZConnector.mbrTranchePayInspection(requestDTO);
   }

   @ApiOperation("会员资金支付-不验密 6163")
   @PostMapping(value="/membershipTranchePay")
   public TranchePayInspectionResponseDTO membershipTranchePay(@RequestBody TranchePayInspectionRequestDTO requestDTO){
      return pingAnJZConnector.membershipTranchePay(requestDTO);
   }

   @ApiOperation("申请修改手机号码 6083")
   @PostMapping(value="/applyForChangeOfCellPhoneNum")
   public ApplyForChangeOfCellPhoneNumResponseDTO applyForChangeOfCellPhoneNum(@RequestBody ApplyForChangeOfCellPhoneNumRequestDTO requestDTO){
      return pingAnJZConnector.applyForChangeOfCellPhoneNum(requestDTO);
   }

   @ApiOperation("回填动态码-修改手机 6084")
   @PostMapping(value="/backfillDynamicPassword")
   public BackfillDynamicPasswordResponseDTO backfillDynamicPassword(@RequestBody BackfillDynamicPasswordRequestDTO requestDTO){
      return pingAnJZConnector.backfillDynamicPassword(requestDTO);
   }

   @ApiOperation("查询对账文件信息 6103")
   @PostMapping(value="/reconciliationDocumentQuery")
   public ReconciliationDocumentQueryResponseDTO reconciliationDocumentQuery(@RequestBody ReconciliationDocumentQueryRequestDTO requestDTO){
      return pingAnJZConnector.reconciliationDocumentQuery(requestDTO);
   }

   @ApiOperation("查询会员子账号余额 6093")
   @PostMapping(value="/queryCustAcctId")
   public QueryCustAcctIdResponseDTO queryCustAcctId(@RequestBody QueryCustAcctIdRequestDTO requestDTO){
      return pingAnJZConnector.queryCustAcctId(requestDTO);
   }

   @ApiOperation("KFEJZB6145 调账-见证收单")
   @PostMapping(value="/accountRegulation")
   public AccountRegulationResponseDTO accountRegulation(@RequestBody AccountRegulationRequestDTO requestDTO){
      return pingAnJZConnector.accountRegulation(requestDTO);
   }

   @ApiOperation("KFEJZB6082   申请提现或支付短信动态码")
   @PostMapping(value="/applicationTextMsgDynamicCode")
   public ApplicationTextMsgDynamicCodeResponseDTO applicationTextMsgDynamicCode(@RequestBody ApplicationTextMsgDynamicCodeRequestDTO requestDTO){
      return pingAnJZConnector.applicationTextMsgDynamicCode(requestDTO);
   }

   @ApiOperation("KFEJZB6166 会员资金支付-验短信动态码")
   @PostMapping(value="/mbrTchPayVerifiedTextMsgs")
   public MbrTchPayVerifiedTextMsgsResponseDTO mbrTchPayVerifiedTextMsgs(@RequestBody MbrTchPayVerifiedTextMsgsRequestDTO requestDTO){
      return pingAnJZConnector.mbrTchPayVerifiedTextMsgs(requestDTO);
   }

   @ApiOperation("KFEJZB6101 会员间交易-验证短信动态码")
   @PostMapping(value="/memberTranVerifyTextMsgs")
   public MemberTranVerifyTextMsgsResponseDTO memberTranVerifyTextMsgs(@RequestBody MemberTranVerifyTextMsgsRequestDTO requestDTO){
      return pingAnJZConnector.memberTranVerifyTextMsgs(requestDTO);
   }

   @ApiOperation("KFEJZB6135 会员资金冻结-验证短信动态码")
   @PostMapping(value="/mbrTchFrzVerifiedTextMsgs")
   public MbrTchFrzVerifiedTextMsgsResponseDTO mbrTchFrzVerifiedTextMsgs(@RequestBody MbrTchFrzVerifiedTextMsgsRequestDTO requestDTO){
      return pingAnJZConnector.mbrTchFrzVerifiedTextMsgs(requestDTO);
   }

   @ApiOperation("KFEJZB6229 提现短信验证回填")
   @PostMapping(value="/batChseqNoVerifiedTextMsgs")
   public BatChseqNoVerifiedTextMsgsResponseDTO batChseqNoVerifiedTextMsgs(@RequestBody BatChseqNoVerifiedTextMsgsRequestDTO requestDTO){
      return pingAnJZConnector.batChseqNoVerifiedTextMsgs(requestDTO);
   }

   @ApiOperation("FEJZB6138 维护会员绑定提现账户联行号")
   @PostMapping(value="/mntMbrBindRelateAcctBankCode")
   public MntMbrBindRelateAcctBankCodeResponseDTO mntMbrBindRelateAcctBankCode(@RequestBody MntMbrBindRelateAcctBankCodeRequestDTO requestDTO){
      return pingAnJZConnector.mntMbrBindRelateAcctBankCode(requestDTO);
   }

   @ApiOperation("KFEJZB6085 会员提现-支持手续费")
   @PostMapping(value="/memberWithdrawCash")
   public MemberWithdrawCashResponseDTO memberWithdrawCash(@RequestBody MemberWithdrawCashRequestDTO requestDTO){
      return pingAnJZConnector.memberWithdrawCash(requestDTO);
   }

   @ApiOperation("生成充值表单")
   @PostMapping(value="/recharge")
   public RechargeResponseDTO recharge(@RequestBody RechargeRequestDTO rechargeRequestDTO){
      return pingAnJZConnector.recharge(rechargeRequestDTO);
   }

   @ApiOperation("跨行-对账查询")
   @PostMapping(value="/khQueryBill")
   public String khQueryBill(String date){
      return pingAnJZConnector.khQueryBill(date);
   }

   @ApiOperation("跨行-单笔查询")
   @PostMapping(value="/khSingleQuery")
   public String khSingleQuery(String orderId){
      return pingAnJZConnector.khSingleQuery(orderId);
   }

   @ApiOperation("生成密码框表单")
   @PostMapping(value="/password")
   public PasswordFormResponseDTO password(@RequestBody PasswordFormRequestDTO requestDTO){
      return pingAnJZConnector.password(requestDTO);
   }

   @ApiOperation("查询小额鉴权转账结果 6061")
   @PostMapping(value="/findSmallAmountTransfer")
   public SmallAmountTransferQueryResponseDTO findSmallAmountTransfer(@RequestBody SmallAmountTransferQueryRequestDTO requestDTO){
      return pingAnJZConnector.findSmallAmountTransfer(requestDTO);
   }

   @ApiOperation("查询银行单笔交易状态 6110")
   @PostMapping(value="/findSingleTransactionStatus")
   public SingleTransactionStatusQueryResponseDTO findSingleTransactionStatus(@RequestBody SingleTransactionStatusQueryRequestDTO requestDTO){
      return pingAnJZConnector.findSingleTransactionStatus(requestDTO);
   }

   @ApiOperation("平台订单管理 6031\n" +
           "平台代理会员进行相关的订单操作，如确认收货。该接口适用于需要验密的情况下使用。\n" +
           "平台代理确认收货（担保→收款方）\n" +
           "平台代理申请退款（担保→付款方）")
   @PostMapping(value="/platformOrderManagement")
   public PlatformOrderManagementResponseDTO platformOrderManagement(@RequestBody PlatformOrderManagementRequestDTO requestDTO){
      return pingAnJZConnector.platformOrderManagement(requestDTO);
   }

   @ApiOperation("查询普通转账充值明细 6050")
   @PostMapping(value="/findCommonTransferRecharge")
   public CommonTransferRechargeQueryResponseDTO findCommonTransferRecharge(@RequestBody CommonTransferRechargeQueryRequestDTO requestDTO){
      return pingAnJZConnector.findCommonTransferRecharge(requestDTO);
   }

   @ApiOperation("查询子帐号历史余额及待转可提现状态信息 6114 (只能查询10天内)")
   @PostMapping(value="/findCustAcctIdHistoryBalance")
   public CustAcctIdHistoryBalanceQueryResponseDTO findCustAcctIdHistoryBalance(@RequestBody CustAcctIdHistoryBalanceQueryRequestDTO requestDTO){
      return pingAnJZConnector.findCustAcctIdHistoryBalance(requestDTO);
   }

   @ApiOperation("会员间交易退款-不验证 6164")
   @PostMapping(value="/memberTransactionRefund")
   public MemberTransactionRefundResponseDTO memberTransactionRefund(@RequestBody MemberTransactionRefundRequestDTO requestDTO){
      return pingAnJZConnector.memberTransactionRefund(requestDTO);
   }

   @ApiOperation("修改会员属性-普通商户子账户 6162")
   @PostMapping(value="/mbrPropertyRevise")
   public MbrPropertyReviseResponseDTO mbrPropertyRevise(@RequestBody MbrPropertyReviseRequestDTO requestDTO){
      return pingAnJZConnector.mbrPropertyRevise(requestDTO);
   }

   @ApiOperation("查询银行时间段内清分提现明细 6073\n" +
           "查询银行时间段内清分提现明细接口：若为“见证+收单退款”“见证+收单充值”记录时备注Note为“见证+收单充值,\n" +
           "订单号”“见证+收单退款,订单号”，此接口可以查到T0/T1的充值明细和退款记录。\n" +
           "查询标志：充值记录仍用3清分选项查询，退款记录同提现用2选项查询。")
   @PostMapping(value="/findBankWithdrawCashDetails")
   public BankWithdrawCashDetailsQueryResponseDTO findBankWithdrawCashDetails(@RequestBody BankWithdrawCashDetailsQueryRequestDTO requestDTO){
      return pingAnJZConnector.findBankWithdrawCashDetails(requestDTO);
   }

   @ApiOperation("查询银行提现退单信息 6048\n" +
           "通过大小额支付系统进行的提现交易，会存在被收款行退票的情况。\n" +
           "本接口用于查询此类退票交易的记录，可以退票日期为区间查询该段时间区间内的退票记录。\n" +
           "退单说明：\n" +
           "若对方账户处于异常状态，不允许资金存入，对方行在收到平安银行资金后，发现入账失败，\n" +
           "会重新给平安银行转回来。这种情况我们称之为退单。\n" +
           "银行的原提现流水状态不会随着退单而修改，即支付成功1笔100元后，不幸发生退单，\n" +
           "原交易流水不会修改为失败，而是成功。这100元退回来后，系统当作普通会员充值进行入账，将资金重新清分到会员子账户。\n" +
           "（此规则对特殊市场不适用）")
   @PostMapping(value="/findBankWithdrawCashBack")
   public BankWithDrawCashBackQueryResponseDTO findBankWithdrawCashBack(@RequestBody BankWithDrawCashBackQueryRequestDTO requestDTO){
      return pingAnJZConnector.findBankWithdrawCashBack(requestDTO);
   }

   @ApiOperation("会员提现-支持手续费及支付密码验证 6111")
   @PostMapping(value="/memberWithdrawSprtFeePwdVerify")
   public MemberWithdrawSprtFeePwdVerifyResponseDTO memberWithdrawSprtFeePwdVerify(@RequestBody MemberWithdrawSprtFeePwdVerifyRequestDTO requestDTO){
      return pingAnJZConnector.memberWithdrawSprtFeePwdVerify(requestDTO);
   }

   @ApiOperation("查询时间段的会员成功交易 6072")
   @PostMapping(value="/findBankTransactionDetails")
   public BankTransactionDetailsQueryResponseDTO findBankTransactionDetails(@RequestBody BankTransactionDetailsQueryRequestDTO requestDTO){
      return pingAnJZConnector.findBankTransactionDetails(requestDTO);
   }

   @ApiOperation("平台补账-见证收单 6147")
   @PostMapping(value="/platformAccountSupply")
   public PlatformAccountSupplyResponseDTO platformAccountSupply(@RequestBody PlatformAccountSupplyRequestDTO requestDTO){
      return pingAnJZConnector.platformAccountSupply(requestDTO);
   }

   @ApiOperation("查询明细单验证码 6142")
   @PostMapping(value="/findDetailVerifiedCode")
   public DetailVerifiedCodeQueryResponseDTO findDetailVerifiedCode(@RequestBody DetailVerifiedCodeQueryRequestDTO requestDTO){
      return pingAnJZConnector.findDetailVerifiedCode(requestDTO);
   }

   @ApiOperation("查询银行费用扣收结果 6109\n查询时间段内银行扣收平台服务费（提现手续费、银联鉴权服务费）结果。")
   @PostMapping(value="/findBankCostDsDealResult")
   public BankCostDsDealResultQueryResponseDTO findBankCostDsDealResult(@RequestBody BankCostDsDealResultQueryRequestDTO requestDTO){
      return pingAnJZConnector.findBankCostDsDealResult(requestDTO);
   }

   @ApiOperation("查询银行在途清算结果 6108 查询时间段内交易网的在途清算结果。")
   @PostMapping(value="/findBankClear")
   public BankClearQueryResponseDTO findBankClear(@RequestBody BankClearQueryRequestDTO requestDTO){
      return pingAnJZConnector.findBankClear(requestDTO);
   }

   @ApiOperation("查询银行子账户余额 6010\n" +
           "查询会员子账户以及平台的功能子账户的余额。\n" +
           "可用金额与可提现金额关系：\n" +
           "会员在电商平台进行了充值100，此时因充值的真实资金未到账，次日才清算到银行，所以这100可以在平台交易，但不可提现。\n" +
           "当T+1日时，充值资金清算到银行，此时这100变为可提现。")
   @PostMapping(value="/findBalance")
   public BalanceQueryResponseDTO findBalance(@RequestBody BalanceQueryRequestDTO requestDTO){
      return pingAnJZConnector.findBalance(requestDTO);
   }

   @ApiOperation("会员绑定信息查询 6098")
   @PostMapping(value="/findMemberBind")
   public MemberBindQueryResponseDTO findMemberBind(@RequestBody MemberBindQueryRequestDTO requestDTO){
      return pingAnJZConnector.findMemberBind(requestDTO);
   }

   @ApiOperation("会员解绑提现账户 6065")
   @PostMapping(value="/unbindRelateAcct")
   public UnbindRelateAcctResponseDTO unbindRelateAcct(@RequestBody UnbindRelateAcctRequestDTO requestDTO){
      return pingAnJZConnector.unbindRelateAcct(requestDTO);
   }

   @ApiOperation("查询充值明细-见证收单 6146")
   @PostMapping(value="/findChargeDetail")
   public ChargeDetailQueryResponseDTO findChargeDetail(@RequestBody ChargeDetailQueryRequestDTO requestDTO){
      return pingAnJZConnector.findChargeDetail(requestDTO);
   }

   @ApiOperation("聚合支付")
   @PostMapping(value="/payOrder")
   public PayOrderResponseDTO payOrder(@RequestBody PayOrderRequestDTO requestDTO){
      return pingAnJZConnector.payOrder(requestDTO);
   }

   @ApiOperation("聚合支付 - 退款")
   @PostMapping(value="/payRefund")
   public PayRefundResponseDTO payRefund(@RequestBody PayRefundRequestDTO requestDTO){
      return pingAnJZConnector.payRefund(requestDTO);
   }

   @ApiOperation("聚合支付 - 取消支付")
   @PostMapping(value="/payCancel")
   public PayCancelResponseDTO payCancel(@RequestBody PayCancelRequestDTO requestDTO){
      return pingAnJZConnector.payCancel(requestDTO);
   }

   @ApiOperation("聚合支付 - 查询支付")
   @PostMapping(value="/payStatus")
   public PayStatusResponseDTO payStatus(@RequestBody PayStatusRequestDTO requestDTO){
      return pingAnJZConnector.payStatus(requestDTO);
   }

   /**
    * 聚合支付 - 退款查询
    * @param requestDTO
    * @return
    */
   @ApiOperation("聚合支付 - 查询退款")
   @PostMapping(value="/refundStatus")
   public RefundStatusResponseDTO refundStatus(@RequestBody RefundStatusRequestDTO requestDTO){
      return pingAnJZConnector.refundStatus(requestDTO);
   }

   /**
    * 跨行快付 - 充值回调
    * @param paramsMap
    * @return
    */
   @ApiOperation("跨行快付 - 充值回调")
   @PostMapping(value="/rechargeNotify")
   public ItemResult<Object> rechargeNotify(@RequestBody Map<String, String> paramsMap) {
      return pingAnJZConnector.rechargeNotify(paramsMap);
   }

   /**
    * 下载商户对账单
    * @param requestDTO
    * @return
    */
   @ApiOperation("下载商户对账单")
   @PostMapping(value="/searchAggregationBill")
   public List<ReconciliationBillDTO> searchAggregationBill(@RequestBody DownloadBillRequestDTO requestDTO) {
      return pingAnJZConnector.searchAggregationBill(requestDTO);
   }

   /**
    * 见证宝 - 密码回调
    * @param paramsMap
    * @return
    */
   @ApiOperation("见证宝 - 密码回调")
   @PostMapping(value="/pinganjzPasswordNotify")
   public ItemResult<Object> pinganjzPasswordNotify(@RequestBody Map<String, String> paramsMap){
      return pingAnJZConnector.pinganjzPasswordNotify(paramsMap);
   }

   /**
    * 见证宝 - 密码回调
    * @param paramsMap
    * @return
    */
   @ApiOperation("聚合支付 - 密码回调")
   @PostMapping(value="/pinganjzAggregationNotify")
   public ItemResult<Object> pinganjzAggregationNotify(@RequestBody Map<String, String> paramsMap){
      return pingAnJZConnector.pinganjzAggregationNotify(paramsMap);
   }

   @ApiOperation("会员补录法人信息-下发短信验证码[6242]")
   @PostMapping(value="/clearingCorpInfoSendMsgCode")
   public Map<String, String> clearingCorpInfoSendMsgCode(@RequestBody ClearingCorpInfoSendRequestDTO corpInfo)
   {
      return pingAnJZConnector.clearingCorpInfoSendMsgCode(corpInfo);
   }

   @ApiOperation("会员补录法人信息-回填短信验证码[6243]")
   @PostMapping(value="/clearingCorpInfoCheckMsgCode")
   public Map<String, String> clearingCorpInfoCheckMsgCode(@RequestBody ClearingCorpInfoCheckRequestDTO corpInfo)
   {
      return pingAnJZConnector.clearingCorpInfoCheckMsgCode(corpInfo);
   }

   @ApiOperation("登记行为记录信息[6244]")
   @PostMapping(value="/registerBehaviorRecordInfo")
   public Map<String, Object> registerBehaviorRecordInfo(@RequestBody RegisterBehaviorRecordRequestDTO record)
   {
      return pingAnJZConnector.registerBehaviorRecordInfo(record);
   }
}
