package com.ecommerce.open.controller;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import io.swagger.annotations.ApiOperation;
import com.ecommerce.open.service.IRefreshCacheService;



@Api(tags={"RefreshCache"},description = ": IRefreshCacheService")
@RestController
@RequestMapping("/refreshCache")
public class RefreshCacheController {

   @Autowired 
   private IRefreshCacheService iRefreshCacheService;

   @ApiOperation("刷新api所有缓存")
   @PostMapping(value="/refreshApiAll")
   public void refreshApiAll()throws Exception{
      iRefreshCacheService.refreshApiAll();

   }



}
