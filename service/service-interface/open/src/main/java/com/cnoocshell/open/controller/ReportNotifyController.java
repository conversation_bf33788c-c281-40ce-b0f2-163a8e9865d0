package com.ecommerce.open.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.open.service.IReportNotifyService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Api(tags={"ReportNotify"},description = "温馨日报发邮件")
@RestController
@RequestMapping("/reportNotify")
public class ReportNotifyController {

    @Autowired
    private IReportNotifyService mailService;

    /**
     * 温馨日报发送邮件
     * @return
     */
    @ApiOperation("温馨日报发送邮件")
    @PostMapping(value="/sendDailyReportAllMail")
    public ItemResult<Void> sendDailyReportAllMail() {
        try {
            mailService.sendDailyReportAllMail();
        } catch (Exception e) {
            ItemResult<Void> itemResult = new ItemResult<>();
            itemResult.setDescription(e.getMessage());
            return itemResult;
        }
        return new ItemResult<>(null);
    }

    /**
     * 发送模板ID的报表
     * @param reportTemplateId
     */
    @ApiOperation("发送模板ID的报表")
    @PostMapping(value="/sendDailyReportByTemplateId")
    public ItemResult<Void> sendDailyReportByTemplateId(@RequestParam("reportTemplateId") String reportTemplateId) {
        try {
            mailService.sendDailyReportByTemplateId(reportTemplateId);
        } catch (Exception e) {
            ItemResult<Void> itemResult = new ItemResult<>();
            itemResult.setDescription(e.getMessage());
            return itemResult;
        }
        return new ItemResult<>(null);
    }


}
