package com.ecommerce.open.controller;

import com.ecommerce.open.api.dto.SMSMessageSendRequestDTO;
import com.ecommerce.open.api.dto.SMSMessageSendResponseDTO;
import com.ecommerce.open.service.impl.SMSMessageTencentService;

import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@Api(tags={"SMSMessageController"},description = "短信服务")
@RestController
@RequestMapping("/message/sms")
public class SMSMessageController {

   @Autowired
   private SMSMessageTencentService smsMessageTencentService;

    @ApiOperation("发送国内短信")
    @PostMapping(value="/send")
    public SMSMessageSendResponseDTO sendMessage(@RequestBody SMSMessageSendRequestDTO smsMessageTencentDTO){
        return smsMessageTencentService.sendMessage(smsMessageTencentDTO);
    }
}
