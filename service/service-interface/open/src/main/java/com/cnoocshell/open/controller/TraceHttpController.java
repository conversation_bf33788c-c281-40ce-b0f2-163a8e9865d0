package com.ecommerce.open.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.open.api.dto.TraceHttpReqDTO;
import com.ecommerce.open.service.ITraceHttpService;

import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/traceHttp")
@Api(tags={"TraceHttpController"},description = "Trace Http 发送服务")
public class TraceHttpController {

    @Autowired
    private ITraceHttpService traceHttpService;

    /**
     * get请求
     * @param traceHttpReqDTO
     * @return
     */
    @ApiOperation("get 请求")
    @RequestMapping("/sendGet")
    public ItemResult<String> sendGet(@RequestBody TraceHttpReqDTO traceHttpReqDTO) {
        return traceHttpService.sendGet(traceHttpReqDTO);
    }

    /**
     * post json string
     * @param traceHttpReqDTO
     * @return
     */
    @ApiOperation("post json 请求")
    @RequestMapping("/postJson")
    public ItemResult<String> postJson(@RequestBody TraceHttpReqDTO traceHttpReqDTO) {
        return traceHttpService.postJson(traceHttpReqDTO);
    }

}
