package com.ecommerce.open.controller;

import cn.binarywang.wx.miniapp.api.WxMaService;
import com.ecommerce.open.api.dto.wechat.*;

import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Date: 04/09/2018 11:47
 * @DESCRIPTION:
 */
@Slf4j
@RestController
@RequestMapping("/wechatLoginInfo")
@Api(tags={"WechatLoginInfoController"},description = "web端控制器，微信登陆相关接口")
public class WechatLoginInfoController {

    @Autowired
    private WxMaService wxService;

    @ApiOperation("获取登录后的session信息")
    @PostMapping("/getSessionInfo")
    public WxMaJscode2SessionResult getSessionInfo(@RequestParam("code") String code) {
        try {
            cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult wmsr = wxService.getUserService().getSessionInfo(code);
            WxMaJscode2SessionResult wsr = new WxMaJscode2SessionResult();
            BeanUtils.copyProperties(wmsr,wsr);
            return wsr;
        }catch (WxErrorException e){
            log.error(e.getMessage(),e);
            return null;
        }
    }

    @ApiOperation("获取openId")
    @PostMapping("/getOpenId")
    public String getOpenId(@RequestParam("code") String code) {
        try {
            return wxService.getUserService().getSessionInfo(code).getOpenid();
        }catch (WxErrorException e){
            if( e != null && e.getError() != null ) {
                log.error("getOpenId error errorcode:{},errorMsg:{}",e.getError().getErrorCode(),e.getError().getErrorMsg());
                if( 40125 == e.getError().getErrorCode() ){
                    log.error("getOpenId error wxId:{},{}",wxService.getWxMaConfig().getAppid(),wxService.getWxMaConfig().getSecret());
                }
            }
            log.error(e.getMessage(),e);
            return null;
        }
    }

    @ApiOperation("获取用户信息")
    @PostMapping("/getUserInfo")
    public WxMaUserInfo getUserInfo(@RequestBody WxMaUserInfoQueryDTO dto) {
        try {
            if(CsStringUtils.isNotBlank(dto.getCode()) && CsStringUtils.isBlank(dto.getSessionKey()) ){
                cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult wmsr = wxService.getUserService().getSessionInfo(dto.getCode());
                dto.setSessionKey(wmsr.getSessionKey());
            }
            cn.binarywang.wx.miniapp.bean.WxMaUserInfo wxMaUserInfo = wxService.getUserService().getUserInfo(dto.getSessionKey(), dto.getEncryptedData(), dto.getIvStr());
            WxMaUserInfo wxMaUserInfo2 = new WxMaUserInfo();
            BeanUtils.copyProperties(wxMaUserInfo,wxMaUserInfo);
            return wxMaUserInfo2;
        }catch (WxErrorException e){
            log.error(e.getMessage(),e);
            return null;
        }
    }

    @ApiOperation("获取手机信息")
    @PostMapping("/getPhoneNoInfo")
    public WxMaPhoneNumberInfo getPhoneNoInfo(@RequestBody WxMaUserInfoQueryDTO dto) {
        try {
            if(CsStringUtils.isNotBlank(dto.getCode()) && CsStringUtils.isBlank(dto.getSessionKey()) ){
                cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult wmsr = wxService.getUserService().getSessionInfo(dto.getCode());
                dto.setSessionKey(wmsr.getSessionKey());
            }
            cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo wxMaPhoneNumberInfo = wxService.getUserService().getPhoneNoInfo(dto.getSessionKey(), dto.getEncryptedData(), dto.getIvStr());
            WxMaPhoneNumberInfo wxMaPhoneNumberInfo2 = new WxMaPhoneNumberInfo();
            BeanUtils.copyProperties(wxMaPhoneNumberInfo,wxMaPhoneNumberInfo2);
            return wxMaPhoneNumberInfo2;
        }catch (Exception e){
            log.error(e.getMessage(),e);
            return null;
        }
    }

    @ApiOperation("获取登录后的session信息、用户信息、手机信息")
    @PostMapping("/getWxInfo")
    public WxMaAllInfoDTO getWxInfo(@RequestBody WxMaAllInfoDTO dto) {
        try {
            WxMaAllInfoDTO info = new WxMaAllInfoDTO();

            cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult wmsr = wxService.getUserService().getSessionInfo(dto.getCode());
            WxMaJscode2SessionResult wsr = new WxMaJscode2SessionResult();
            BeanUtils.copyProperties(wmsr,wsr);
            info.setSessionInfo(wsr);
            //获取用户信息
            if( dto.getUserQueryDTO() != null && CsStringUtils.isNotBlank(dto.getUserQueryDTO().getEncryptedData()) ) {
                cn.binarywang.wx.miniapp.bean.WxMaUserInfo wxMaUserInfo = wxService.getUserService().getUserInfo(wmsr.getSessionKey(), dto.getUserQueryDTO().getEncryptedData(), dto.getUserQueryDTO().getIvStr());
                WxMaUserInfo wxMaUserInfo2 = new WxMaUserInfo();
                BeanUtils.copyProperties(wxMaUserInfo, wxMaUserInfo);
                info.setUserInfo(wxMaUserInfo2);
            }
            //获取手机信息
            if( dto.getPhoneQueryDTO() != null && CsStringUtils.isNotBlank(dto.getPhoneQueryDTO().getEncryptedData()) ) {
                cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo wxMaPhoneNumberInfo = wxService.getUserService().getPhoneNoInfo(wmsr.getSessionKey(), dto.getPhoneQueryDTO().getEncryptedData(), dto.getPhoneQueryDTO().getIvStr());
                WxMaPhoneNumberInfo wxMaPhoneNumberInfo2 = new WxMaPhoneNumberInfo();
                BeanUtils.copyProperties(wxMaPhoneNumberInfo, wxMaPhoneNumberInfo2);
                info.setPhoneInfo(wxMaPhoneNumberInfo2);
            }
            return info;
        }catch (WxErrorException e){
            log.error(e.getMessage(),e);
            return null;
        }
    }
}
