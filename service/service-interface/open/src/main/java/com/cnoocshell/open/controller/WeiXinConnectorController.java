package com.ecommerce.open.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import io.swagger.annotations.ApiOperation;
import com.ecommerce.open.service.payment.IWeiXinConnector;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.open.api.dto.WXNotifyResponseDTO;
import java.util.Map;
import  java.lang.String;


/**
 * @Created锛�Tue Dec 11 10:14:14 CST 2018
 * <AUTHOR>
 * @Version:2
 * @Description:null
*/

@Api(tags={"weiXinConnector"},description = "TODO")
@RestController
@RequestMapping("/weiXinConnector")
public class WeiXinConnectorController {

   @Autowired 
   private IWeiXinConnector iWeiXinConnector;

   @ApiOperation("获取微信支付结果通知")
   @PostMapping(value="/getWxPaymentNotify")
   public ItemResult<WXNotifyResponseDTO> getWxPaymentNotify(@RequestParam("requestStr") String requestStr)throws Exception{
      return iWeiXinConnector.getWxPaymentNotify(requestStr);
   }


   @ApiOperation("微信支付发送http post请求")
   @PostMapping(value="/wxPostRequest")
   public ItemResult<String> wxPostRequest(@RequestParam("xmlString") String xmlString,@RequestParam("url") String url,@RequestParam("paymentBillId") String paymentBillId)throws Exception{
      return iWeiXinConnector.wxPostRequest(xmlString,url,paymentBillId);
   }


   @ApiOperation("微信退款发送http post请求")
   @PostMapping(value="/wxRefundPostRequest")
   public ItemResult<String> wxRefundPostRequest(String xmlString, String url, String paymentBillNo,String mchNo)throws Exception{
      return iWeiXinConnector.wxRefundPostRequest(xmlString,url,paymentBillNo,mchNo);
   }

   @ApiOperation("获取微信退款结果通知")
   @PostMapping(value="/getWxRefundNotify")
   public ItemResult<WXNotifyResponseDTO> getWxRefundNotify(String requestStr)throws Exception{
      return iWeiXinConnector.getWxRefundNotify(requestStr);
   }

   @ApiOperation("微信支付发送http get请求")
   @PostMapping(value="/wxGetRequest")
   public ItemResult<String> wxGetRequest(@RequestParam("url") String url,@RequestBody Map<String,String> map)throws Exception{
      return iWeiXinConnector.wxGetRequest(url,map);
   }





}
