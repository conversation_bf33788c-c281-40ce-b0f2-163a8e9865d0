package com.ecommerce.open.controller.apicenter;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import io.swagger.annotations.ApiOperation;
import com.ecommerce.open.service.apicenter.IApiConfigService;
import com.ecommerce.common.result.ItemResult;

import java.lang.Integer;
import java.lang.Void;

import com.ecommerce.open.api.dto.apicenter.config.ApiConfigDelDTO;
import com.ecommerce.open.api.dto.apicenter.config.ApiConfigUpdateDTO;
import com.ecommerce.open.api.dto.apicenter.config.ApiConfigDetailDTO;
import com.ecommerce.open.api.dto.apicenter.config.ApiConfigAddDTO;
import com.ecommerce.common.result.PageData;
import com.ecommerce.open.api.dto.PageQuery;
import com.ecommerce.open.api.dto.apicenter.config.ApiConfigQueryDTO;
import com.ecommerce.open.api.dto.apicenter.config.ApiConfigListDTO;


/**
 * 协议报文参数管控 Author: colu Date: 2019-05-28 11:44:55
 */

@Api(tags = {"ApiConfig"}, description = "Description: 协议报文参数管控 Author: colu Date: 2019-05-28 11:44:55")
@RestController
@RequestMapping("/apiConfig")
public class ApiConfigController {

    @Autowired
    private IApiConfigService iApiConfigService;

    @ApiOperation("改")
    @PostMapping(value = "/update")
    public ItemResult<Void> update(@RequestBody ApiConfigUpdateDTO apiConfigUpdateDTO) throws Exception {
        return iApiConfigService.update(apiConfigUpdateDTO);
    }


    @ApiOperation("增")
    @PostMapping(value = "/create")
    public ItemResult<Void> create(@RequestBody ApiConfigAddDTO apiConfigAddDTO) throws Exception {
        return iApiConfigService.create(apiConfigAddDTO);
    }


    @ApiOperation("列表查")
    @PostMapping(value = "/queryList")
    public ItemResult<PageData<ApiConfigListDTO>> queryList(@RequestBody PageQuery<ApiConfigQueryDTO> pageQuery) throws Exception {
        return iApiConfigService.queryList(pageQuery);
    }


    @ApiOperation("ID查")
    @PostMapping(value = "/queryById")
    public ItemResult<ApiConfigDetailDTO> queryById(@RequestParam("defApiId") Integer defApiId) throws Exception {
        return iApiConfigService.queryById(defApiId);
    }


    @ApiOperation("删")
    @PostMapping(value = "/deleteByIds")
    public ItemResult<Void> deleteByIds(@RequestBody ApiConfigDelDTO apiConfigDelDTO) throws Exception {
        return iApiConfigService.deleteByIds(apiConfigDelDTO);
    }


}
