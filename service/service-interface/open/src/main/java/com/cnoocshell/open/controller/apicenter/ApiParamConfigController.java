package com.ecommerce.open.controller.apicenter;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import io.swagger.annotations.ApiOperation;
import com.ecommerce.open.service.apicenter.IApiParamConfigService;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.open.api.dto.apicenter.config.ApiParamConfigAddDTO;

import java.lang.Integer;
import java.lang.Void;

import com.ecommerce.open.api.dto.apicenter.config.ApiParamConfigUpdateDTO;
import com.ecommerce.open.api.dto.apicenter.config.ApiParamConfigDelDTO;
import com.ecommerce.open.api.dto.apicenter.config.ApiParamConfigResultDTO;
import com.ecommerce.common.result.PageData;
import com.ecommerce.open.api.dto.PageQuery;
import com.ecommerce.open.api.dto.apicenter.config.ApiParamConfigQueryDTO;


@Api(tags = {"ApiParamConfig"}, description = "null")
@RestController
@RequestMapping("/apiParamConfig")
public class ApiParamConfigController {

    @Autowired
    private IApiParamConfigService iApiParamConfigService;

    @ApiOperation("null")
    @PostMapping(value = "/update")
    public ItemResult<Void> update(@RequestBody ApiParamConfigUpdateDTO apiParamConfigUpdateDTO) throws Exception {
        return iApiParamConfigService.update(apiParamConfigUpdateDTO);
    }


    @ApiOperation("null")
    @PostMapping(value = "/create")
    public ItemResult<Void> create(@RequestBody ApiParamConfigAddDTO apiParamConfigAddDTO) throws Exception {
        return iApiParamConfigService.create(apiParamConfigAddDTO);
    }


    @ApiOperation("null")
    @PostMapping(value = "/queryList")
    public ItemResult<PageData<ApiParamConfigResultDTO>> queryList(@RequestBody PageQuery<ApiParamConfigQueryDTO> pageQuery) throws Exception {
        return iApiParamConfigService.queryList(pageQuery);
    }


    @ApiOperation("null")
    @PostMapping(value = "/deleteByIds")
    public ItemResult<Void> deleteByIds(@RequestBody ApiParamConfigDelDTO apiParamConfigDelDTO) throws Exception {
        return iApiParamConfigService.deleteByIds(apiParamConfigDelDTO);
    }


    @ApiOperation("null")
    @PostMapping(value = "/queryById")
    public ItemResult<ApiParamConfigResultDTO> queryById(@RequestParam("paramConfigId") Integer paramConfigId) throws Exception {
        return iApiParamConfigService.queryById(paramConfigId);
    }


}
