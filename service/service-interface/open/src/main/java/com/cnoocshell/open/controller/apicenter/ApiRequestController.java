package com.ecommerce.open.controller.apicenter;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import io.swagger.annotations.ApiOperation;
import com.ecommerce.open.service.apicenter.IApiRequestService;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.open.api.dto.apicenter.ApiRequestCondDTO;
import com.ecommerce.common.result.PageData;
import com.ecommerce.open.api.dto.PageQuery;
import com.ecommerce.open.api.dto.apicenter.ApiRequestListDTO;


/**
 * 的管理
 */

@Api(tags = {"ApiRequest"}, description = "op_api_request 的管理")
@RestController
@RequestMapping("/apiRequest")
public class ApiRequestController {

    @Autowired
    private IApiRequestService iApiRequestService;

    @ApiOperation("分页查询erp发送记录")
    @PostMapping(value = "/queryApiRequestByCond")
    public ItemResult<PageData<ApiRequestListDTO>> queryApiRequestByCond(@RequestBody PageQuery<ApiRequestCondDTO> arg0) throws Exception {
        return iApiRequestService.queryApiRequestByCond(arg0);
    }


}
