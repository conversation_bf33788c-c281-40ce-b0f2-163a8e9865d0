package com.ecommerce.open.controller.apicenter;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import io.swagger.annotations.ApiOperation;
import com.ecommerce.open.service.apicenter.IApiTaskService;


@Api(tags = {"ApiTask"}, description = ": IApiTaskService")
@RestController
@RequestMapping("/apiTask")
public class ApiTaskController {

    @Autowired
    private IApiTaskService iApiTaskService;

    @ApiOperation("删除过期的pendingMessage记录")
    @PostMapping(value = "/deleteOverduePendingMessage")
    public void deleteOverduePendingMessage() throws Exception {
        iApiTaskService.deleteOverduePendingMessage();

    }


}
