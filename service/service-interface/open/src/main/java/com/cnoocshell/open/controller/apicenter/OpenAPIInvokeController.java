package com.ecommerce.open.controller.apicenter;

import com.alibaba.fastjson.JSON;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.open.api.dto.apicenter.ApiResult;
import com.ecommerce.open.api.dto.apicenter.ERPRetryCallbackDTO;
import com.ecommerce.open.api.dto.apicenter.IdentityVerifyDTO;
import com.ecommerce.open.api.dto.apicenter.logistic.ECWaybillDTO;
import com.ecommerce.open.api.dto.apicenter.logistic.ECWaybillItemDTO;
import com.ecommerce.open.service.apicenter.IOpenAPIInvokeService;
import com.google.common.collect.Lists;

import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Created锛�Mon Apr 08 09:48:16 CST 2019
 * <AUTHOR>
 * @Version:2
 * @Description:: API 调用服务
 */

@Api(tags = { "OpenAPIInvoke" }, description = ": API 调用服务")
@RestController
@RequestMapping("/openAPIInvoke")
public class OpenAPIInvokeController {

	@Autowired
	private IOpenAPIInvokeService iOpenAPIInvokeService;

	@ApiOperation(": 调用接口中心方法")
	@PostMapping(value = "/invoke")
	public ApiResult invoke(@RequestParam("bizCode") String bizCode, @RequestParam("sellerId") String sellerId,
			@RequestBody String data) throws Exception {
		return iOpenAPIInvokeService.invoke(bizCode, sellerId, data);
	}

	@ApiOperation(": 数据验证服务")
	@PostMapping(value = "/validate")
	public ApiResult validate(@RequestParam("bizCode") String bizCode, @RequestParam("sellerId") String sellerId,
			@RequestBody String data) throws Exception {
		return iOpenAPIInvokeService.validate(bizCode, sellerId, data);
	}

	@ApiOperation(": 重发服务")
	@PostMapping(value = "/reSendRequest")
	public ItemResult<Object> validate(@RequestBody ERPRetryCallbackDTO retryCallbackDTO) throws Exception {
		return iOpenAPIInvokeService.reSendRequest(retryCallbackDTO);
	}

	@ApiOperation(": 测试")
	@PostMapping(value = "/test")
	public ApiResult test() throws Exception {
		ECWaybillDTO one = new ECWaybillDTO();
		List<ECWaybillItemDTO> itemDTOS = Lists.newArrayList();
		ECWaybillItemDTO i1 = new ECWaybillItemDTO();
		i1.setQrCode("000000");
		itemDTOS.add(i1);
		itemDTOS.add(new ECWaybillItemDTO());
		one.setWaybillItemList(itemDTOS);
		one.setReceiver("xxx");
		ECWaybillDTO two = new ECWaybillDTO();
		two.setWarehouseName("重庆中心仓");
		return new ApiResult(JSON.toJSONString(Lists.newArrayList(one, two)));
	}

	@ApiOperation("身份验证")
	@PostMapping(value = "/identityVerify")
	public ItemResult<Boolean> identityVerify(@RequestBody IdentityVerifyDTO identityVerifyDTO) throws Exception {
		return iOpenAPIInvokeService.identityVerify(identityVerifyDTO);
	}

}
