package com.ecommerce.open.controller.apicenter;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import io.swagger.annotations.ApiOperation;
import com.ecommerce.open.service.apicenter.ISysConfigService;
import com.ecommerce.open.api.dto.apicenter.config.SysConfigResultDTO;
import com.ecommerce.common.result.ItemResult;
import java.lang.Integer;
import com.ecommerce.open.api.dto.apicenter.config.SysConfigQueryDTO;
import java.lang.Void;
import java.util.List;

import com.ecommerce.open.api.dto.apicenter.config.SysConfigAddDTO;
import com.ecommerce.open.api.dto.apicenter.config.SysConfigDelDTO;
import com.ecommerce.common.result.PageData;
import com.ecommerce.open.api.dto.PageQuery;
import com.ecommerce.open.api.dto.apicenter.config.SysConfigUpdateDTO;


/**
 * @Created锛�Tue May 28 14:52:09 CST 2019
 * <AUTHOR>
 * @Version:2
 * @Description:Description: 系统定义管控 Author: colu Date: 2019-05-28 11:44:55
*/

@Api(tags={"SysConfig"},description = "Description: 系统定义管控 Author: colu Date: 2019-05-28 11:44:55")
@RestController
@RequestMapping("/sysConfig")
public class SysConfigController {

   @Autowired 
   private ISysConfigService iSysConfigService;

   @ApiOperation("改")
   @PostMapping(value="/update")
   public ItemResult<Void> update(@RequestBody SysConfigUpdateDTO sysConfigUpdateDTO)throws Exception{
      return iSysConfigService.update(sysConfigUpdateDTO);
   }


   @ApiOperation("增")
   @PostMapping(value="/create")
   public ItemResult<Void> create(@RequestBody SysConfigAddDTO sysConfigAddDTO)throws Exception{
      return iSysConfigService.create(sysConfigAddDTO);
   }


   @ApiOperation("删")
   @PostMapping(value="/deleteByIds")
   public ItemResult<Void> deleteByIds(@RequestBody SysConfigDelDTO sysConfigDelDTO)throws Exception{
      return iSysConfigService.deleteByIds(sysConfigDelDTO);
   }


   @ApiOperation("列表查")
   @PostMapping(value="/queryList")
   public ItemResult<PageData<SysConfigResultDTO>> queryList(@RequestBody PageQuery<SysConfigQueryDTO> pageQuery)throws Exception{
      return iSysConfigService.queryList(pageQuery);
   }


   @ApiOperation("ID查")
   @PostMapping(value="/queryById")
   public ItemResult<SysConfigResultDTO> queryById(@RequestParam("defSysId") Integer defSysId)throws Exception{
      return iSysConfigService.queryById(defSysId);
   }

   @ApiOperation("查询所有ERP系统")
   @PostMapping(value="/querySysConfig")
   public ItemResult<List<SysConfigResultDTO>> querySysConfig()throws Exception{
      return iSysConfigService.querySysConfig();
   }


}
