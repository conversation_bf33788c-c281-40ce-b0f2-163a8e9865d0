package com.ecommerce.open.controller.apicenter;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import io.swagger.annotations.ApiOperation;
import com.ecommerce.open.service.apicenter.ISysRouteService;
import com.ecommerce.common.result.ItemResult;
import java.lang.Integer;
import com.ecommerce.open.api.dto.apicenter.config.SysRouteResultDTO;
import com.ecommerce.open.api.dto.apicenter.config.SysRouteAddDTO;
import java.lang.Void;
import com.ecommerce.open.api.dto.apicenter.config.SysRouteQueryDTO;
import com.ecommerce.open.api.dto.apicenter.config.SysRouteDelDTO;
import com.ecommerce.open.api.dto.apicenter.config.SysRouteUpdateDTO;
import com.ecommerce.common.result.PageData;
import com.ecommerce.open.api.dto.PageQuery;


/**
 * 系统路由管控 Author: colu Date: 2019-05-28 11:44:55
*/

@Api(tags={"SysRoute"},description = "Description: 系统路由管控 Author: colu Date: 2019-05-28 11:44:55")
@RestController
@RequestMapping("/sysRoute")
public class SysRouteController {

   @Autowired 
   private ISysRouteService iSysRouteService;

   @ApiOperation("改")
   @PostMapping(value="/update")
   public ItemResult<Void> update(@RequestBody SysRouteUpdateDTO sysRouteUpdateDTO)throws Exception{
      return iSysRouteService.update(sysRouteUpdateDTO);
   }


   @ApiOperation("增")
   @PostMapping(value="/create")
   public ItemResult<Void> create(@RequestBody SysRouteAddDTO sysRouteAddDTO)throws Exception{
      return iSysRouteService.create(sysRouteAddDTO);
   }


   @ApiOperation("删")
   @PostMapping(value="/deleteByIds")
   public ItemResult<Void> deleteByIds(@RequestBody SysRouteDelDTO sysRouteDelDTO)throws Exception{
      return iSysRouteService.deleteByIds(sysRouteDelDTO);
   }


   @ApiOperation("ID查")
   @PostMapping(value="/queryById")
   public ItemResult<SysRouteResultDTO> queryById(@RequestParam("defSysId") Integer defSysId)throws Exception{
      return iSysRouteService.queryById(defSysId);
   }


   @ApiOperation("列表查")
   @PostMapping(value="/queryList")
   public ItemResult<PageData<SysRouteResultDTO>> queryList(@RequestBody PageQuery<SysRouteQueryDTO> pageQuery)throws Exception{
      return iSysRouteService.queryList(pageQuery);
   }



}
