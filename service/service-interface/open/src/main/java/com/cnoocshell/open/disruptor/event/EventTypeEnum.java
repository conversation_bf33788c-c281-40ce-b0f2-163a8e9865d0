package com.ecommerce.open.disruptor.event;

/**
 * <AUTHOR>
 * @Description:
 * @Date: Create in 15:27 06/09/2018
 */
public enum EventTypeEnum {
    /**
     * Save coordinator action enum.
     */
    SAVE(0, "保存");

    private int code;

    private String desc;

    EventTypeEnum(final int code, final String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * Gets code.
     *
     * @return the code
     */
    public int getCode() {
        return code;
    }

    /**
     * Sets code.
     *
     * @param code the code
     */
    public void setCode(final int code) {
        this.code = code;
    }

    /**
     * Gets desc.
     *
     * @return the desc
     */
    public String getDesc() {
        return desc;
    }

    /**
     * Sets desc.
     *
     * @param desc the desc
     */
    public void setDesc(final String desc) {
        this.desc = desc;
    }
}
