
package com.ecommerce.open.disruptor.handler;

import com.ecommerce.open.disruptor.event.EventTypeEnum;
import com.ecommerce.open.disruptor.event.InterfaceLogEvent;
import com.ecommerce.open.service.IInterfaceInfoService;
import com.lmax.disruptor.EventHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class InterfaceLogEventHandler implements EventHandler<InterfaceLogEvent> {
    private final static Logger interfaceLogger = LoggerFactory.getLogger("AsyncInterfaceLog");

    @Autowired
    private IInterfaceInfoService interfaceInfoService;

    @Override
    public void onEvent(final InterfaceLogEvent interfaceLogEvent, final long sequence, final boolean endOfBatch) {
        if (interfaceLogEvent.getType() == EventTypeEnum.SAVE.getCode()) {
//            interfaceInfoService.save(interfaceLogEvent.getInterfaceLogInfo());
            interfaceLogger.info(interfaceLogEvent.getInterfaceLogInfo().toString());
        }

        interfaceLogEvent.clear();
    }
}
