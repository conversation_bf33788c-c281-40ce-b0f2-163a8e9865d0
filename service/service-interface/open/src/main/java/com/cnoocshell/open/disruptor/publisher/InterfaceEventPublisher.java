
package com.ecommerce.open.disruptor.publisher;

import com.ecommerce.open.dao.vo.InterfaceLogInfo;
import com.ecommerce.open.disruptor.event.InterfaceLogEvent;
import com.ecommerce.open.disruptor.factory.InterfaceLogEventFactory;
import com.ecommerce.open.disruptor.handler.InterfaceLogEventHandler;
import com.ecommerce.open.disruptor.translator.InterfaceLogEventTranslator;
import com.lmax.disruptor.RingBuffer;
import com.lmax.disruptor.YieldingWaitStrategy;
import com.lmax.disruptor.dsl.Disruptor;
import com.lmax.disruptor.dsl.ProducerType;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.concurrent.atomic.AtomicInteger;

@Component
public class InterfaceEventPublisher implements DisposableBean {
    @Value("${open.disruptor.buffer.size:1024}")
    private String bufferSize;

    private Disruptor<InterfaceLogEvent> disruptor;

    @Autowired
    private InterfaceLogEventHandler interfaceLogEventHandler;

    /**
     * start disruptor.
     *
     */
    public void start() {
        disruptor = new Disruptor<>(new InterfaceLogEventFactory(), Integer.parseInt(bufferSize), r -> {
            AtomicInteger index = new AtomicInteger(1);
            return new Thread(null, r, "disruptor-thread-" + index.getAndIncrement());
        }, ProducerType.MULTI, new YieldingWaitStrategy());
        disruptor.handleEventsWith(interfaceLogEventHandler);
        disruptor.start();
    }


    /**
     * publish disruptor event.
     *
     */
    public void publishEvent(final InterfaceLogInfo interfaceLogInfo, final int type) {
        final RingBuffer<InterfaceLogEvent> ringBuffer = disruptor.getRingBuffer();
        ringBuffer.publishEvent(new InterfaceLogEventTranslator(type), interfaceLogInfo);
    }

    @Override
    public void destroy() {
        disruptor.shutdown();
    }
}
