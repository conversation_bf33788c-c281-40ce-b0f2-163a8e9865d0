
package com.ecommerce.open.disruptor.translator;

import com.ecommerce.open.dao.vo.InterfaceLogInfo;
import com.ecommerce.open.disruptor.event.InterfaceLogEvent;
import com.lmax.disruptor.EventTranslatorOneArg;

public class InterfaceLogEventTranslator implements EventTranslatorOneArg<InterfaceLogEvent, InterfaceLogInfo> {

    private int type;

    public InterfaceLogEventTranslator(final int type) {
        this.type = type;
    }

    @Override
    public void translateTo(final InterfaceLogEvent interfaceLogEvent, final long l, final InterfaceLogInfo interfaceLogInfo) {
        interfaceLogEvent.setInterfaceLogInfo(interfaceLogInfo);
        interfaceLogEvent.setType(type);
    }
}
