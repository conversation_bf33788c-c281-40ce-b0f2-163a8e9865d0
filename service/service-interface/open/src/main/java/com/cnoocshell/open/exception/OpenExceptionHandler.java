/*

package com.ecommerce.open.exception;

import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.result.ItemResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

*/
/**
 *
 * <AUTHOR>
 * Date: Create in 下午3:34 19/5/22
 *//*

@Slf4j
@ControllerAdvice
public class OpenExceptionHandler {

    @ResponseBody
    @ExceptionHandler(value = Exception.class)
    public ItemResult<Object> javaExceptionHandler(Exception ex) {
        log.error("接口中心接口异常", ex);
        ItemResult itemResult = new ItemResult<>();
        itemResult.setSuccess(Boolean.FALSE);
        itemResult.setDescription("接口中心接口异常");

        return itemResult;
    }

    @ResponseBody
    @ExceptionHandler(value = BizException.class)
    public ItemResult<Object> bizExceptionHandler(BizException ex) {
        log.error("接口中心业务异常", ex);
        ItemResult itemResult = new ItemResult<>();
        itemResult.setSuccess(Boolean.FALSE);
        itemResult.setDescription(ex.getMessage());
        itemResult.setCode(ex.getErrorCode().getCode());

        return itemResult;
    }
}
*/
