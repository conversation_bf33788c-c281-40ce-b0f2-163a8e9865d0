package com.ecommerce.open.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.open.api.dto.invoice.AccountTokenDTO;
import com.ecommerce.open.api.dto.invoice.EnterpriseCardDTO;
import com.ecommerce.open.api.dto.invoice.InvoiceContentDTO;
import com.ecommerce.open.api.dto.invoice.InvoiceRequestDTO;
import com.ecommerce.open.api.dto.invoice.InvoiceRevokeDTO;
import com.ecommerce.open.api.dto.invoice.SendInvoiceDTO;
import com.ecommerce.open.api.dto.invoice.TaxCategoryDTO;
import com.ecommerce.open.api.dto.invoice.TokenApplyDTO;

/**
 * <AUTHOR>
 * Description:
 * Date: Create in 下午2:50 19/4/25
 */
public interface IElectricInvoiceService {
    /**
     * 申请令牌
     */
    ItemResult<AccountTokenDTO> applyToken(TokenApplyDTO tokenApplyDTO);

    /**
     * 根据企业名称模糊查询企业名片
     * <T> 企业名称
     */
    ItemResult<EnterpriseCardDTO> searchEnterpriseCardByKeyword(InvoiceRequestDTO<String> invoiceRequestDTO);

    /**
     * 根据商品名称查询税收分类信息
     * <T> 商品名称
     */
    ItemResult<TaxCategoryDTO> queryTaxCategoryByName(InvoiceRequestDTO<String> invoiceRequestDTO);

    /**
     * 直接开票
     */
    ItemResult<InvoiceContentDTO> directSendInvoice(InvoiceRequestDTO<SendInvoiceDTO> invoiceRequestDTO);

    /**
     * 发票红冲
     */
    ItemResult<Void> invoiceRevoke(InvoiceRequestDTO<InvoiceRevokeDTO> invoiceRequestDTO);
}
