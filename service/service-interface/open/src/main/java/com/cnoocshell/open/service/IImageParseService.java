package com.ecommerce.open.service;

import java.util.Set;

/**
 * 图片识别
 */
public interface IImageParseService {
    /**
     * 阿里云图片ocr识别
     * @param url
     * @param appCode
     * @param type
     * @param methodName
     * @return
     */
    String parseImageByAli(String url,String appCode,Integer type,String methodName);

    /**
     * 腾讯云图片ocr识别
     * @param bucketName
     * @param url
     * @param appId
     * @param secretId
     * @param secretKey
     * @param methodName
     * @param type
     * @return
     */
    String parseImageTencent(String bucketName,String url,String appId,String secretId,String secretKey,String methodName,Integer type);

    /**
     * 华为图片识别
     * @param accessKeyId
     * @param accessSecretKey
     * @param regionName
     * @param httpUri
     * @param imgPath
     * @return
     */
    String parseImageHuawei(String accessKeyId,String accessSecretKey,String regionName,String httpUri,String imgPath,Integer type);
    /**
     * cdn文件刷新
     * @param urls
     */
    Boolean refreshUrl(Set<String> urls,String secretId,String secretKey,String cloudName);
}
