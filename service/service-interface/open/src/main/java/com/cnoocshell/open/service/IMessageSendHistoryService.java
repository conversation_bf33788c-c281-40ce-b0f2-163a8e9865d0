package com.ecommerce.open.service;

import com.ecommerce.base.api.dto.MessageConfigDTO;
import com.ecommerce.open.api.dto.MessageDTO;
import com.ecommerce.open.dao.vo.MessageHistoryMq;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 消息发送历史记录
 */
public interface IMessageSendHistoryService {

    /**
     * 保存队列消息
     */
    MessageHistoryMq saveMQMessage(MessageDTO messageDTO,List<MessageConfigDTO> messageConfigDTOList);
    /**
     * 更新
     * @param messageHistoryMq
     */
    void updateMQMessage(MessageHistoryMq messageHistoryMq);

    /**
     * 翻页查询失败的记录，用于重试
     * @param pageSize
     * @param pageNumber
     * @return
     */
    PageInfo<MessageHistoryMq> pageFail(int pageSize, int pageNumber);

    MessageHistoryMq findById(String messageHistoryMqId);
}
