package com.ecommerce.open.service;

import com.ecommerce.base.api.dto.MessageConfigDTO;
import com.ecommerce.member.api.dto.account.AccountDTO;
import com.ecommerce.open.api.dto.MessageDTO;
import com.ecommerce.open.dao.vo.MessageHistoryMq;

/**
 * <AUTHOR>
 * @Date: 08/01/2020 18:31
 * @DESCRIPTION:
 */
public interface IMessageSendSMSService {

    void sendMessage(MessageHistoryMq messageHistoryMq, MessageDTO dto, MessageConfigDTO messageConfigDTO, AccountDTO accountDTO);
}
