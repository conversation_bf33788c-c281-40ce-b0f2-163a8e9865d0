package com.ecommerce.open.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.open.api.dto.TraceHttpReqDTO;


public interface ITraceHttpService {

    /**
     * get请求
     * @param traceHttpReqDTO
     * @return
     */
    ItemResult<String> sendGet(TraceHttpReqDTO traceHttpReqDTO);

    /**
     * post json string
     * @param traceHttpReqDTO
     * @return
     */
    ItemResult<String> postJson(TraceHttpReqDTO traceHttpReqDTO);

}
