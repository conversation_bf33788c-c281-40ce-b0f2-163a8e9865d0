package com.ecommerce.open.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.open.api.dto.WXNotifyResponseDTO;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Map;


public interface IWechatPayHttpService {

    /**
     * 微信支付发送http post请求
     */
    ItemResult<String> wxPostRequest(String xmlString, String url);

    /**
     * 获取微信支付结果通知
     */
    ItemResult<WXNotifyResponseDTO> getWxPaymentNotify(String requestStr);

    /**
     * 微信支付发送http get请求
     */
    ItemResult<String> wxGetRequest(String url, Map<String,String> map);

}
