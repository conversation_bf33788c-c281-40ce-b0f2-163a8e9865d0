package com.ecommerce.open.service.apicenter;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.open.api.dto.PageQuery;
import com.ecommerce.open.api.dto.apicenter.config.ApiConfigAddDTO;
import com.ecommerce.open.api.dto.apicenter.config.ApiConfigDelDTO;
import com.ecommerce.open.api.dto.apicenter.config.ApiConfigDetailDTO;
import com.ecommerce.open.api.dto.apicenter.config.ApiConfigListDTO;
import com.ecommerce.open.api.dto.apicenter.config.ApiConfigQueryDTO;
import com.ecommerce.open.api.dto.apicenter.config.ApiConfigUpdateDTO;

/**
 * Description: 协议报文参数管控
 * Author: colu
 * Date: 2019-05-28 11:44:55
 */
public interface IApiConfigService {

    /**
     * 增
     * @param apiConfigAddDTO
     * @return
     */
    ItemResult<Void> create(ApiConfigAddDTO apiConfigAddDTO);

    /**
     * 删
     * @param apiConfigDelDTO
     * @return
     */
    ItemResult<Void> deleteByIds(ApiConfigDelDTO apiConfigDelDTO);

    /**
     * 列表查
     * @param pageQuery
     * @return
     */
    ItemResult<PageData<ApiConfigListDTO>> queryList(PageQuery<ApiConfigQueryDTO> pageQuery);

    /**
     * ID查
     * @param defApiId
     * @return
     */
    ItemResult<ApiConfigDetailDTO> queryById(Integer defApiId);

    /**
     * 改
     * @param apiConfigUpdateDTO
     * @return
     */
    ItemResult<Void> update(ApiConfigUpdateDTO apiConfigUpdateDTO);

}
