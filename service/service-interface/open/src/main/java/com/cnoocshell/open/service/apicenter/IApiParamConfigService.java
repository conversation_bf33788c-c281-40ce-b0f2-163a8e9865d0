package com.ecommerce.open.service.apicenter;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.open.api.dto.PageQuery;
import com.ecommerce.open.api.dto.apicenter.config.ApiParamConfigAddDTO;
import com.ecommerce.open.api.dto.apicenter.config.ApiParamConfigDelDTO;
import com.ecommerce.open.api.dto.apicenter.config.ApiParamConfigQueryDTO;
import com.ecommerce.open.api.dto.apicenter.config.ApiParamConfigResultDTO;
import com.ecommerce.open.api.dto.apicenter.config.ApiParamConfigUpdateDTO;

/**
 * Description: 协议报文参数管控
 * Author: colu
 * Date: 2019-05-28 11:44:55
 */
public interface IApiParamConfigService {

    /**
     * 增
     * @param apiParamConfigAddDTO
     * @return
     */
    ItemResult<Void> create(ApiParamConfigAddDTO apiParamConfigAddDTO);

    /**
     * 删
     * @param apiParamConfigDelDTO
     * @return
     */
    ItemResult<Void> deleteByIds(ApiParamConfigDelDTO apiParamConfigDelDTO);

    /**
     * 列表查
     * @param pageQuery
     * @return
     */
    ItemResult<PageData<ApiParamConfigResultDTO>> queryList(PageQuery<ApiParamConfigQueryDTO> pageQuery);

    /**
     * ID查
     * @param paramConfigId
     * @return
     */
    ItemResult<ApiParamConfigResultDTO> queryById(Integer paramConfigId);

    /**
     * 改
     * @param apiParamConfigUpdateDTO
     * @return
     */
    ItemResult<Void> update(ApiParamConfigUpdateDTO apiParamConfigUpdateDTO);

}
