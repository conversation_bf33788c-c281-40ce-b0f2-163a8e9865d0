package com.ecommerce.open.service.apicenter;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.open.api.dto.PageQuery;
import com.ecommerce.open.api.dto.apicenter.ApiRequestCondDTO;
import com.ecommerce.open.api.dto.apicenter.ApiRequestListDTO;

/**
 * op_api_request 的管理
 */
public interface IApiRequestService {

    /**
     * 分页查询erp发送记录
     * @param pageQuery
     * @return
     */
    ItemResult<PageData<ApiRequestListDTO>> queryApiRequestByCond(PageQuery<ApiRequestCondDTO> pageQuery);

}
