package com.ecommerce.open.service.apicenter;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.open.api.dto.apicenter.ApiResult;
import com.ecommerce.open.api.dto.apicenter.ERPRetryCallbackDTO;
import com.ecommerce.open.api.dto.apicenter.IdentityVerifyDTO;
import com.ecommerce.open.dao.vo.ApiPendingMessage;
import com.google.common.base.Verify;

/**
 * @ClassName: IOpenAPIInvokeService
 * @Description: API 调用服务
 * <AUTHOR>
 * @version 1.0
 */
public interface IOpenAPIInvokeService {
	/**
	 * @Title: invoke
	 * @Description: 调用接口中心方法
	 * <AUTHOR>
	 * @param bizCode
	 *            接口业务编码
	 * @param sellerId
	 *            卖家id
	 * @param data
	 *            业务数据
	 * @return
	 */
	public ApiResult invoke(String bizCode, String sellerId, String data);

	/**
	 * @Title: validate
	 * @Description: 数据验证服务
	 * <AUTHOR>
	 * @param bizCode
	 *            接口业务编码
	 * @param sellerId
	 *            卖家id
	 * @param data
	 *            业务数据
	 * @return
	 */
	public ApiResult validate(String bizCode, String sellerId, String data);

	/**
	 * @Title: reSendRequest
	 * @Description: 重试
	 * <AUTHOR>
	 * @param retryCallbackDTO
	 */
	public ItemResult<Object> reSendRequest(ERPRetryCallbackDTO retryCallbackDTO);

	/**
	 * @Title: failCallbackWhenRequestStatusException
	 * @Description:定时任务发现请求异常，超时时间超过3天发起自动回调，通知业务方处理失败
	 * <AUTHOR>
	 * @param apiPendingMessage
	 */
	public void failCallbackWhenRequestStatusException(ApiPendingMessage apiPendingMessage);


	/**
	 * 外部系统身份鉴权
	 * @param identityVerifyDTO 身份鉴权对象
	 * @return 通过标识
	 */
	public ItemResult<Boolean> identityVerify(IdentityVerifyDTO identityVerifyDTO);

}
