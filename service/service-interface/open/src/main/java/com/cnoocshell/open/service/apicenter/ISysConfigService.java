package com.ecommerce.open.service.apicenter;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.open.api.dto.PageQuery;
import com.ecommerce.open.api.dto.apicenter.config.SysConfigAddDTO;
import com.ecommerce.open.api.dto.apicenter.config.SysConfigDelDTO;
import com.ecommerce.open.api.dto.apicenter.config.SysConfigQueryDTO;
import com.ecommerce.open.api.dto.apicenter.config.SysConfigResultDTO;
import com.ecommerce.open.api.dto.apicenter.config.SysConfigUpdateDTO;

import java.util.List;

/**
 * Description: 系统定义管控
 * Author: colu
 * Date: 2019-05-28 11:44:55
 */
public interface ISysConfigService {

    /**
     * 增
     * @param sysConfigAddDTO
     * @return
     */
    ItemResult<Void> create(SysConfigAddDTO sysConfigAddDTO);

    /**
     * 删
     * @param sysConfigDelDTO
     * @return
     */
    ItemResult<Void> deleteByIds(SysConfigDelDTO sysConfigDelDTO);

    /**
     * 列表查
     * @param pageQuery
     * @return
     */
    ItemResult<PageData<SysConfigResultDTO>> queryList(PageQuery<SysConfigQueryDTO> pageQuery);

    /**
     * ID查
     * @param defSysId
     * @return
     */
    ItemResult<SysConfigResultDTO> queryById(Integer defSysId);

    /**
     * 改
     * @param sysConfigUpdateDTO
     * @return
     */
    ItemResult<Void> update(SysConfigUpdateDTO sysConfigUpdateDTO);

    /**
     * 查询所有ERP系统
     * @return
     */
    ItemResult<List<SysConfigResultDTO>> querySysConfig();
}
