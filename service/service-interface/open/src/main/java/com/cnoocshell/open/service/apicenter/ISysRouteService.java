package com.ecommerce.open.service.apicenter;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.open.api.dto.PageQuery;
import com.ecommerce.open.api.dto.apicenter.config.SysRouteAddDTO;
import com.ecommerce.open.api.dto.apicenter.config.SysRouteDelDTO;
import com.ecommerce.open.api.dto.apicenter.config.SysRouteQueryDTO;
import com.ecommerce.open.api.dto.apicenter.config.SysRouteResultDTO;
import com.ecommerce.open.api.dto.apicenter.config.SysRouteUpdateDTO;

/**
 * Description: 系统路由管控
 * Author: colu
 * Date: 2019-05-28 11:44:55
 */
public interface ISysRouteService {

    /**
     * 增
     * @param sysRouteAddDTO
     * @return
     */
    ItemResult<Void> create(SysRouteAddDTO sysRouteAddDTO);

    /**
     * 删
     * @param sysRouteDelDTO
     * @return
     */
    ItemResult<Void> deleteByIds(SysRouteDelDTO sysRouteDelDTO);

    /**
     * 列表查
     * @param pageQuery
     * @return
     */
    ItemResult<PageData<SysRouteResultDTO>> queryList(PageQuery<SysRouteQueryDTO> pageQuery);

    /**
     * ID查
     * @param defSysId
     * @return
     */
    ItemResult<SysRouteResultDTO> queryById(Integer defSysId);

    /**
     * 改
     * @param sysRouteUpdateDTO
     * @return
     */
    ItemResult<Void> update(SysRouteUpdateDTO sysRouteUpdateDTO);

}
