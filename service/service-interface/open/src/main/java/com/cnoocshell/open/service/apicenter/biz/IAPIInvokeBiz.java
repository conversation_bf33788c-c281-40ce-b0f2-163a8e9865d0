package com.ecommerce.open.service.apicenter.biz;

import com.ecommerce.open.api.dto.apicenter.ApiResult;
import com.ecommerce.open.service.apicenter.dto.RequestContext;

/**
 * @ClassName: IAPIInvokeBiz
 * @Description: API调用biz
 * <AUTHOR>
 * @version 1.0
 */
public interface IAPIInvokeBiz {
	/**
	 * @Title: invokeAPI
	 * @Description: 调用API
	 * <AUTHOR>
	 * @param ApiConfigBean
	 * @param SysConfig
	 * @param data
	 * @return
	 */
	public ApiResult invokeAPI(RequestContext requestContext);

	/**
	 * @Title: validate
	 * @Description: 验证请求
	 * <AUTHOR>
	 * @param sysRoute
	 * @param ApiConfigBean
	 * @param SysConfig
	 * @param data
	 * @return
	 */
	public ApiResult validate(RequestContext requestContext);
 

}
