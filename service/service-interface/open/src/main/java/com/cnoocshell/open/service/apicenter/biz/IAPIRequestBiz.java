package com.ecommerce.open.service.apicenter.biz;

import com.ecommerce.common.result.PageData;
import com.ecommerce.open.api.dto.PageQuery;
import com.ecommerce.open.api.dto.apicenter.ApiRequestCondDTO;
import com.ecommerce.open.api.dto.apicenter.ApiRequestListDTO;
import com.ecommerce.open.dao.vo.ApiRequest;

/**
 * @ClassName: IAPIRequestBiz
 * @Description: 接口请求记录服务
 * <AUTHOR>
 * @version 1.0
 */
public interface IAPIRequestBiz {

	/**
	 * 保存
	 * @param request
	 */
	void saveAPIRequest(ApiRequest request);

	/**
	 * 请求号更新
	 * @param request
	 */
	void updateAPIRequestByRequestNo(ApiRequest request);

	/**
	 * 回调请求号更新
	 * @param request
	 */
	void updateAPIRequestByCallbackRequestNo(ApiRequest request);

	/**
	 * 主键更新
	 * @param request
	 */
	void updateById(ApiRequest request);

	/**
	 * 主键查找
	 * @param requestLogId
	 * @return
	 */
	ApiRequest selectById(Integer requestLogId);

	/**
	 * 条件查询
	 * @param pageQuery
	 * @return
	 */
	PageData<ApiRequestListDTO> queryApiRequestByCond(PageQuery<ApiRequestCondDTO> pageQuery);

}
