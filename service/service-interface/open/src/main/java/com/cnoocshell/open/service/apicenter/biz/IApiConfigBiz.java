package com.ecommerce.open.service.apicenter.biz;

import com.ecommerce.common.result.PageData;
import com.ecommerce.open.api.dto.PageQuery;
import com.ecommerce.open.api.dto.apicenter.config.ApiConfigAddDTO;
import com.ecommerce.open.api.dto.apicenter.config.ApiConfigDelDTO;
import com.ecommerce.open.api.dto.apicenter.config.ApiConfigDetailDTO;
import com.ecommerce.open.api.dto.apicenter.config.ApiConfigListDTO;
import com.ecommerce.open.api.dto.apicenter.config.ApiConfigQueryDTO;
import com.ecommerce.open.api.dto.apicenter.config.ApiConfigUpdateDTO;
import com.ecommerce.open.dao.bean.ApiConfigBean;
import com.ecommerce.open.dao.vo.BizRoute;
import com.ecommerce.open.service.apicenter.dto.RouteConvert;


public interface IApiConfigBiz {

    /**
     * 查询业务对应的api
     * @param sysCode
     * @param bizCode
     * @return
     */
    ApiConfigBean queryApiByBizCode(String sysCode, String bizCode);

    /**
     * 根据请求类型查询Api
     * @param apiCode
     * @param apiRequestType
     * @return
     */
    ApiConfigBean queryApiByApiRequestType(String apiCode, String apiRequestType);

    /**
     * 增
     * @param apiConfigAddDTO
     */
    void create(ApiConfigAddDTO apiConfigAddDTO);

    /**
     * 删
     * @param apiConfigDelDTO
     */
    void deleteByIds(ApiConfigDelDTO apiConfigDelDTO);

    /**
     * 条件查
     * @param pageQuery
     * @return
     */
    PageData<ApiConfigListDTO> queryList(PageQuery<ApiConfigQueryDTO> pageQuery);

    /**
     * id查
     * @param defApiId
     * @return
     */
    ApiConfigDetailDTO queryById(Integer defApiId);

    /**
     * 改
     * @param apiConfigUpdateDTO
     */
    void update(ApiConfigUpdateDTO apiConfigUpdateDTO);

    void refreshCache();

    BizRoute queryBizRouteByCondition(RouteConvert routeConvert);
}
