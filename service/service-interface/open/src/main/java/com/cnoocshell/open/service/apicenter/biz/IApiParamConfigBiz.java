package com.ecommerce.open.service.apicenter.biz;

import com.ecommerce.common.result.PageData;
import com.ecommerce.open.api.dto.PageQuery;
import com.ecommerce.open.api.dto.apicenter.config.ApiParamConfigAddDTO;
import com.ecommerce.open.api.dto.apicenter.config.ApiParamConfigDelDTO;
import com.ecommerce.open.api.dto.apicenter.config.ApiParamConfigQueryDTO;
import com.ecommerce.open.api.dto.apicenter.config.ApiParamConfigResultDTO;
import com.ecommerce.open.api.dto.apicenter.config.ApiParamConfigUpdateDTO;

import java.util.Map;


public interface IApiParamConfigBiz {

    /**
     * 获取sysCode下bizCode的参数键值对
     * @param sysCode
     * @param bizCode
     * @return
     */
    Map<String, String> getByBizCodeFromCache(String sysCode, String bizCode);

    /**
     * 增
     * @param apiParamConfigAddDTO
     */
    void create(ApiParamConfigAddDTO apiParamConfigAddDTO);

    /**
     * 删
     * @param apiParamConfigDelDTO
     */
    void deleteByIds(ApiParamConfigDelDTO apiParamConfigDelDTO);

    /**
     * 条件查
     * @param pageQuery
     * @return
     */
    PageData<ApiParamConfigResultDTO> queryList(PageQuery<ApiParamConfigQueryDTO> pageQuery);

    /**
     * id查
     * @param paramConfigId
     * @return
     */
    ApiParamConfigResultDTO queryById(Integer paramConfigId);

    /**
     * 改
     * @param apiParamConfigUpdateDTO
     */
    void update(ApiParamConfigUpdateDTO apiParamConfigUpdateDTO);

    /**
     * 刷新缓存
     */
    void refreshCache();

}
