package com.ecommerce.open.service.apicenter.biz;

import com.ecommerce.open.dao.vo.ApiPendingMessage;

import java.util.Date;
import java.util.List;

public interface IApiPendingMessageBiz {

    /**
     * 保存
     * @param pendingMessage
     * @return
     */
    String savePending(ApiPendingMessage pendingMessage);

    /**
     * 主键更新
     * @param pendingMessage
     */
    void updatePendingById(ApiPendingMessage pendingMessage);

    /**
     * 主键删除
     * @param pendingId
     */
    void deletePendingById(Integer pendingId);

    /**
     * 删除早于指定时间的成功记录(硬删除
     * @param endTime
     * @param sendStatusList
     */
    void deleteBeforeDateAndStatus(Date endTime, List<String> sendStatusList);

    /**
     * 处理未能发送的报文记录
     * @param endTime
     */
    void handleNotSend(Date endTime);

    /**
     * 状态和截止时间查询
     * @param endTime
     * @param sendStatusList
     * @return
     */
    List<ApiPendingMessage> selectByTimeAndStatus(Date endTime, List<String> sendStatusList);

    /**
     * ids 删除
     * @param ids
     */
    void deleteByIds(String ids);

    /**
     * 请求号查询
     * @param requestNo
     * @return
     */
    ApiPendingMessage queryByRequestNo(String requestNo);

    /**
     * 请求号更新
     * @param apiPendingMessage
     */
    void updateByRequestNo(ApiPendingMessage apiPendingMessage);

}
