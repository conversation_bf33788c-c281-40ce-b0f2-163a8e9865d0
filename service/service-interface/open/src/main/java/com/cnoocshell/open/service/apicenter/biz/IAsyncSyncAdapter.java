package com.ecommerce.open.service.apicenter.biz;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.open.api.dto.apicenter.ApiResult;
import com.ecommerce.open.dao.bean.ApiConfigBean;
import com.ecommerce.open.dao.vo.SysConfig;
import com.ecommerce.open.dao.vo.SysRoute;
import com.ecommerce.open.service.apicenter.dto.RequestContext;

/**
 * @ClassName: IAsyncSyncAdapter
 * @Description: 同步异步适配器，用于转换client和server不同的同步异步情况
 * <AUTHOR>
 * @version 1.0
 */
public interface IAsyncSyncAdapter {

	/**
	 * @Title: invoke
	 * @Description: 调用api
	 * <AUTHOR>
	 * @param ApiConfigBean
	 * @param SysConfig
	 * @param dataTransfer
	 * @param data
	 * @return
	 */
	public ApiResult invoke(RequestContext requestContext);

	/**
	 * @Title: sendFailCallBackRequest
	 * @Description: 发起默认的failcallback
	 * <AUTHOR>
	 * @param requestContext
	 */
	public void sendFailCallBackRequest(RequestContext requestContext);
}
