package com.ecommerce.open.service.apicenter.biz;

import java.util.Map;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.open.enums.ApiBizSupportEnum;
import com.ecommerce.open.service.apicenter.dto.RequestContext;
import com.ecommerce.open.service.apicenter.dto.RequestInfo;
import com.ecommerce.open.service.apicenter.dto.ResponseInfo;

/**
 * @ClassName: IDataTransferBiz
 * @Description: 数据转换服务
 * <AUTHOR>
 * @version 1.0
 * @param <clientRequest>
 *            接收到的client端请求
 * @param <clientResponse>
 *            发送给client端的response
 * @param <serverRequest>
 *            发送给server端的请求
 * @param <serverResponse>
 *            接收到的server端的response
 */
public interface IDataTransfer<clientRequest, clientResponse, serverRequest, serverResponse> {

	public ApiBizSupportEnum checkSupport(RequestContext requestContext);
	
	/** 
	 * @Title: transferURL 
	 * @Description: 获取URL，根据具体的ERP需要进行加密 
	 * <AUTHOR>
	 * @param requestContext
	 * @return
	 */
	public String transferURL(RequestContext requestContext);
	
	
	/**
	 * @Title: getClientRequestDTO
	 * @Description: 获取DTO
	 * <AUTHOR>
	 * @param clientRequestString
	 * @return
	 */
	public clientRequest getClientDataDTO(String clientRequestString);

	/**
	 * @Title: transferToServerRequest
	 * @Description: 将接收到的客户端请求报文转换为发送给服务器端的报文
	 * <AUTHOR>
	 * @param clientRequest
	 * @param sysRoute
	 * @param SysConfig
	 * @param ApiConfigBean
	 * @return
	 */
	public void transferToServerRequest(RequestContext requestContext);

	/**
	 * @Title: transferToClientResponse
	 * @Description: 将服务端发过来的响应转换为发送给客户端的响应
	 * <AUTHOR>
	 * @param serverResponse
	 * @param sysRoute
	 * @param SysConfig
	 * @param ApiConfigBean
	 * @return
	 */
	public void transferToClientResponse(RequestContext requestContext);

	/**
	 * @Title: validateClientRequest
	 * @Description: 验证接收到的client端请求
	 * <AUTHOR>
	 * @param clientRequest
	 * @param SysConfig
	 * @param ApiConfigBean
	 * @return
	 */
	public ItemResult<Boolean> validateClientRequest(RequestContext requestContext);

	/**
	 * @Title: getRequestInfoDTO
	 * @Description: 从clientRequest中获取请求参数信息供接口中心处理
	 * <AUTHOR>
	 * @param clientRequest
	 * @param SysConfig
	 * @param ApiConfigBean
	 * @return
	 */
	public RequestInfo getRequestInfo(RequestContext requestContext);

//	/**
//	 * @Title: getResponseInfoDTO
//	 * @Description: 从ServerResponse中获取请求消息供接口中心处理
//	 * <AUTHOR>
//	 * @param serverResponse
//	 * @param SysConfig
//	 * @param ApiConfigBean
//	 * @return
//	 */
//	public ResponseInfo getResponseInfo(RequestContext requestContext);

	// /**
	// * @Title: transferURL
	// * @Description: 拼接动态url, 如添加签名等等。
	// * <AUTHOR>
	// * @param url
	// * @param clientRequest
	// * @param serverRequest
	// * @param SysConfig
	// * @param ApiConfigBean
	// * @return
	// */
	// public String transferURL(String url, clientRequest clientRequest,
	// serverRequest serverRequest, SysRoute sysRoute,
	// SysConfig SysConfig, ApiConfigBean ApiConfigBean);

	/**
	 * @Title: getHeaderParms
	 * @Description: 获取请求头参数
	 * <AUTHOR>
	 * @param clientRequest
	 * @param serverRequest
	 * @param SysConfig
	 * @param ApiConfigBean
	 * @return
	 */
	public Map<String, String> getHeaderParms(RequestContext requestContext);

//	/**
//	 * @Title: getAsyncResponseForBizRequest
//	 * @Description: 获取异步响应
//	 * <AUTHOR>
//	 * @param requestContext
//	 * @return
//	 */
//	public ItemResult<clientResponse> getAsyncResponseForBizRequest(RequestContext requestContext);

}
