package com.ecommerce.open.service.apicenter.biz;

import java.util.Map;


public interface IOpenRedisBiz {

    /**
     * 前缀清空缓存
     * @param keyPrefix
     */
    void clearRedisByKeyPrefix(String keyPrefix);

    /**
     * 单个删除
     * @param key
     */
    void deleteByKey(String key);

    /**
     * key查询
     * @param key
     * @param <T>
     * @return
     */
    <T> T getByKey(String key);

    /**
     * 键值对插入缓存
     * @param key
     * @param value
     * @param <T>
     */
    <T> void setByKey(String key, T value);

    /**
     * 获取hash表
     * @param key
     * @return
     */
    Map<String, String> entries(String key);

    /**
     * 添加hash表中的项,有则覆盖
     * @param key
     * @param hashTable
     */
    void putHashTable(String key, Map<String, String> hashTable);

    /**
     * 设置新的hash表,原先的删除
     * @param key
     * @param hashTable
     * @param needExpire
     */
    void setHashTable(String key, Map<String, String> hashTable, boolean needExpire);

    /**
     * 获取hash表中的值
     * @param key
     * @param hashKey
     * @return
     */
    String getByHashKey(String key, String hashKey);

    /**
     * 设置hash表中的值
     * @param key
     * @param hashKey
     * @param value
     */
    void setByHashKey(String key, String hashKey, String value);

    /**
     * 删除 hash key的值
     * @param key
     * @param hashKeys
     */
    void deleteHashKey(String key, String... hashKeys);

}
