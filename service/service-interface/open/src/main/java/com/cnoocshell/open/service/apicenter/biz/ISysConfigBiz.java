package com.ecommerce.open.service.apicenter.biz;

import com.ecommerce.common.result.PageData;
import com.ecommerce.open.api.dto.PageQuery;
import com.ecommerce.open.api.dto.apicenter.config.SysConfigAddDTO;
import com.ecommerce.open.api.dto.apicenter.config.SysConfigDelDTO;
import com.ecommerce.open.api.dto.apicenter.config.SysConfigQueryDTO;
import com.ecommerce.open.api.dto.apicenter.config.SysConfigResultDTO;
import com.ecommerce.open.api.dto.apicenter.config.SysConfigUpdateDTO;
import com.ecommerce.open.dao.vo.SysConfig;

import java.util.List;

public interface ISysConfigBiz {

    SysConfig getBySysCode(String sysCode);

    /**
     * 增
     * @param sysConfigAddDTO
     */
    void create(SysConfigAddDTO sysConfigAddDTO);

    /**
     * 删
     * @param sysConfigDelDTO
     */
    void deleteByIds(SysConfigDelDTO sysConfigDelDTO);

    /**
     * 条件查
     * @param pageQuery
     * @return
     */
    PageData<SysConfigResultDTO> queryList(PageQuery<SysConfigQueryDTO> pageQuery);

    /**
     * id查
     * @param defSysId
     * @return
     */
    SysConfigResultDTO queryById(Integer defSysId);

    /**
     * 改
     * @param sysConfigUpdateDTO
     */
    void update(SysConfigUpdateDTO sysConfigUpdateDTO);

    void refreshCache();

    /**
     * 查询所有ERP系统
     * @return
     */
    List<SysConfigResultDTO> querySysConfig();
}
