package com.ecommerce.open.service.apicenter.biz;

import com.ecommerce.common.result.PageData;
import com.ecommerce.open.api.dto.PageQuery;
import com.ecommerce.open.api.dto.apicenter.config.SysRouteAddDTO;
import com.ecommerce.open.api.dto.apicenter.config.SysRouteDelDTO;
import com.ecommerce.open.api.dto.apicenter.config.SysRouteQueryDTO;
import com.ecommerce.open.api.dto.apicenter.config.SysRouteResultDTO;
import com.ecommerce.open.api.dto.apicenter.config.SysRouteUpdateDTO;
import com.ecommerce.open.dao.vo.SysRoute;

/**
 * 系统路由业务接口
 */
public interface ISysRouteBiz {

    /**
     * 路由
     * @param invokeSellerId
     * @param bizCode
     * @return
     */
    SysRoute route(String invokeSellerId, String bizCode);

    /**
     * 启用禁用路由
     * @param invokeSellerId
     * @param bizCode
     * @param routeStatus
     * @param operId
     */
    void updateStatus(String invokeSellerId, String bizCode, String routeStatus, String operId);

    /**
     * 增
     * @param sysRouteAddDTO
     */
    void create(SysRouteAddDTO sysRouteAddDTO);

    /**
     * 删
     * @param sysRouteDelDTO
     */
    void deleteByIds(SysRouteDelDTO sysRouteDelDTO);

    /**
     * 条件查
     * @param pageQuery
     * @return
     */
    PageData<SysRouteResultDTO> queryList(PageQuery<SysRouteQueryDTO> pageQuery);

    /**
     * id查
     * @param defSysId
     * @return
     */
    SysRouteResultDTO queryById(Integer defSysId);

    /**
     * 改
     * @param sysRouteUpdateDTO
     */
    void update(SysRouteUpdateDTO sysRouteUpdateDTO);

    /**
     * 清空缓存
     */
    void refreshCache();

}
