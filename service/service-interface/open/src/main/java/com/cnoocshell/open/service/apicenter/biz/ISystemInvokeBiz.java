package com.ecommerce.open.service.apicenter.biz;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.open.api.dto.apicenter.ApiResult;
import com.ecommerce.open.dao.vo.SysConfig;
import com.ecommerce.open.dao.vo.SysRoute;
import com.ecommerce.open.service.apicenter.dto.RequestContext;

/**
 * @ClassName: ISystemInvokeBiz
 * @Description: system调用系统实现
 * <AUTHOR>
 * @version 1.0
 */
public interface ISystemInvokeBiz {

	/**
	 * @Title: invokeSystem
	 * @Description: 调用系统实现
	 * <AUTHOR>
	 * @param bizCode
	 *            业务编码
	 * @param data
	 *            业务数据
	 * @param SysConfig
	 *            系统配置DTO
	 * @return
	 */
	public ApiResult invokeSystem(RequestContext requestContext);

	/**
	 * @Title: validate
	 * @Description: 验证请求
	 * <AUTHOR>
	 * @param sysRoute
	 * @param bizCode
	 * @param data
	 * @param SysConfig
	 * @return
	 */
	public ApiResult validate(RequestContext requestContext);
}
