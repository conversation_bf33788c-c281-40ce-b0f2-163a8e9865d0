package com.ecommerce.open.service.apicenter.biz.connector;

import lombok.Data;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Data
public class HttpPoolProperties {
	@Value("${http.pool.conn.maxTotal:}")
	private Integer maxTotal;
	@Value("${http.pool.conn.defaultMaxPerRoute:}")
	private Integer defaultMaxPerRoute;
	@Value("${http.pool.conn.connectTimeout:}")
	private Integer connectTimeout;
	@Value("${http.pool.conn.connectionRequestTimeout:}")
	private Integer connectionRequestTimeout;
	@Value("${http.pool.conn.socketTimeout:}")
	private Integer socketTimeout;
	@Value("${http.pool.conn.validateAfterInactivity:}")
	private Integer validateAfterInactivity;
}
