package com.ecommerce.open.service.apicenter.biz.connector;

import com.ecommerce.open.service.apicenter.dto.RequestContext;
import com.ecommerce.open.service.apicenter.dto.ResponseMessage;

/**
 * @ClassName: IConnector
 * @Description: 连接器用于向服务器发送请求
 * <AUTHOR>
 * @version 1.0
 */
public interface IConnector {
	/**
	 * @Title: sendRequestToServer
	 * @Description: 发送请求到服务器
	 * <AUTHOR>
	 * @param ApiConfigBean
	 * @param SysConfig
	 * @param dataTransfer
	 * @param data
	 * @param serverRequest
	 * @return
	 */
	public ResponseMessage sendRequestToServer(RequestContext requestContext);
}
