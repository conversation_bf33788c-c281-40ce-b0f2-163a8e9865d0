package com.ecommerce.open.service.apicenter.biz.connector.impl;

import java.util.Map;

import com.ecommerce.common.utils.CsStringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestTemplate;

import com.alibaba.fastjson.JSON;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.open.api.exception.APICenterExceptionCode;
import com.ecommerce.open.dao.bean.ApiConfigBean;
import com.ecommerce.open.dao.vo.SysConfig;
import com.ecommerce.open.enums.RequestStatusEnum;
import com.ecommerce.open.service.apicenter.biz.connector.IConnector;
import com.ecommerce.open.service.apicenter.dto.RequestContext;
import com.ecommerce.open.service.apicenter.dto.RequestMessage;
import com.ecommerce.open.service.apicenter.dto.ResponseMessage;

import lombok.extern.slf4j.Slf4j;

import com.ecommerce.open.service.apicenter.biz.IDataTransfer;

@Slf4j
@Service("baseRestConnector")
public class BaseRestConnector implements IConnector {
	private final static Logger interfaceLogger = LoggerFactory.getLogger("AsyncInterfaceLog");
	
	@Autowired
	private RestTemplate restTemplate;

	@Value("${crc.erp.header.host:ssdpuat.crc.com.cn}")
	private String headerHost;

	@Override
	public ResponseMessage sendRequestToServer(RequestContext requestContext) {

		ApiConfigBean ApiConfigBean = requestContext.getApiConfigBean();
		SysConfig SysConfig = requestContext.getSysConfig();
		IDataTransfer dataTransfer = requestContext.getDataTransfer();
		RequestMessage serverRequest = requestContext.getRequestMessage();
		//// ResponseMessage
		////
		//// , SysConfig SysConfig,
		// IDataTransfer dataTransfer, Object data, ServerRequest serverRequest

		log.info(
				"apicenter connector layer: start send request to server ApiConfigBean {} \r\n, SysConfig {}, \r\n   IDataTransfer {} \r\n, data {} \r\n, ServerRequest {}",
				ApiConfigBean, SysConfig, dataTransfer, serverRequest.getBody(), serverRequest);
		long startSendTime = System.currentTimeMillis();

		Object message = serverRequest.getBody();

		HttpHeaders headers = new HttpHeaders();
		Map<String, String> headerMap = requestContext.getRequestMessage().getHeaderParms();
		//专网时,添加了host
		headers.add(HttpHeaders.HOST, headerHost);
		if (headerMap != null && !headerMap.isEmpty()) {
			headerMap.forEach((key, value) -> {
				headers.add(key, value);
			});
		}
		HttpEntity<Object> entity = new HttpEntity<>(message, headers);

		String url = serverRequest.getUrl();
		if (CsStringUtils.isEmpty(url)) {
			log.error("L3 BaseRestConnector sendRequestToServer error url is empty");
			throw new BizException(APICenterExceptionCode.CONFIG_NULL, "URL未配置");
		}
		log.info("L3 BaseRestConnector start send to url {}  , message {} ", url, JSON.toJSONString(message));
		HttpEntity<String> response = null;
		try {
			long s = System.currentTimeMillis();
			log.info("API Flow 开始发送请求 BaseRestConnector requestContext===>{}", requestContext);
			interfaceLogger.info("send request to url===>{},message====>{}" , url,message);
			response = restTemplate.postForEntity(url, entity, String.class);
			interfaceLogger.info("send request to url===>{},message====>{},response===>{}" , url,message,response);

			log.info("API Flow 发送请求完成,cost time :{}ms BaseRestConnector response===>{}",(System.currentTimeMillis()-s), response);
		} catch (ResourceAccessException e) {
			log.error("L3 BaseRestConnector send timeout error:{}====>{}", url, message, e);
			e.printStackTrace();
			long endSendTime = System.currentTimeMillis();
			long spendTime = endSendTime - startSendTime;
			ResponseMessage result = new ResponseMessage();
			result.setCostTime(spendTime);
			result.setRequestStatus(RequestStatusEnum.SEND_TIMEOUT);
			result.setCode(APICenterExceptionCode.NETWORK_ERROR.getCode());
			result.setMessage("发送请求超时");
			requestContext.setCode(APICenterExceptionCode.NETWORK_ERROR.getCode());
			requestContext.setResponseMessage(result);
			requestContext.setMsg("发送请求超时");
			log.info("API Flow 请求发送失败。BaseRestConnector 发送超时 构建ResponseMessage===>{}", result);
			return result;

		} catch (Exception e) {
			log.error("L3 BaseRestConnector send error:{}====>{}", url, message, e);
			e.printStackTrace();
			long endSendTime = System.currentTimeMillis();
			long spendTime = endSendTime - startSendTime;
			ResponseMessage result = new ResponseMessage();
			result.setCostTime(spendTime);
			result.setRequestStatus(RequestStatusEnum.SEND_FAIL);
			result.setCode(APICenterExceptionCode.NETWORK_ERROR.getCode());
			result.setMessage("发送请求失败");
			requestContext.setCode(APICenterExceptionCode.NETWORK_ERROR.getCode());
			requestContext.setResponseMessage(result);
			requestContext.setMsg("发送请求失败");
			log.info("API Flow 请求发送失败。BaseRestConnector 发请求失败 构建ResponseMessage===>{}", result);
			return result;
		}
		if (response == null) {
			log.error("L3 BaseRestConnector response is null");
			long endSendTime = System.currentTimeMillis();
			long spendTime = endSendTime - startSendTime;
			ResponseMessage result = new ResponseMessage();
			result.setCostTime(spendTime);
			result.setRequestStatus(RequestStatusEnum.SEND_FAIL);
			result.setCode(APICenterExceptionCode.NETWORK_ERROR.getCode());
			result.setMessage("发送请求失败收到的响应为空");
			requestContext.setCode(APICenterExceptionCode.NETWORK_ERROR.getCode());
			requestContext.setResponseMessage(result);
			requestContext.setMsg("发送请求失败收到的响应为空");
			log.info("API Flow 请求发送失败，收到的响应为空。BaseRestConnector 发请求失败 构建ResponseMessage===>{}", result);
			return result;
		}

		long endSendTime = System.currentTimeMillis();
		long spendTime = endSendTime - startSendTime;
		log.info("sendRequestToServer spend time =  {}ms responseHeader :{} ，responseBody :{}", spendTime,
				response.getHeaders(), response.getBody());
		ResponseMessage result = new ResponseMessage();
		result.setCostTime(spendTime);
		result.setBody(response.getBody());
		result.setHeaderParms(response.getHeaders().toSingleValueMap());
		result.setRequestStatus(RequestStatusEnum.SEND_SUCCESS);
		result.setCode("01");
		result.setMessage("请求发送成功");
		requestContext.setResponseMessage(result);
		log.info("API Flow 请求发送成功。BaseRestConnector 发请求成功 构建ResponseMessage===>{}", result);
		return result;
	}

}
