package com.ecommerce.open.service.apicenter.biz.transfer;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.alibaba.fastjson.JSON;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.exception.CodeMeta;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.open.api.exception.APICenterExceptionCode;
import com.ecommerce.open.dao.bean.ApiConfigBean;
import com.ecommerce.open.dao.vo.SysConfig;
import com.ecommerce.open.dao.vo.SysRoute;
import com.ecommerce.open.enums.ApiBizSupportEnum;
import com.ecommerce.open.enums.ProcessResult;
import com.ecommerce.open.enums.RequestStatusEnum;
import com.ecommerce.open.service.apicenter.biz.IDataTransfer;
import com.ecommerce.open.service.apicenter.dto.RequestContext;
import com.ecommerce.open.service.apicenter.dto.ResponseInfo;

import java.lang.reflect.ParameterizedType;
import lombok.extern.slf4j.Slf4j;
import com.ecommerce.common.utils.CsStringUtils;

@Slf4j
public abstract class BaseDataTransfer<clientRequest, clientResponse, serverRequest, serverResponse>
		implements IDataTransfer<clientRequest, clientResponse, serverRequest, serverResponse> {

	public clientRequest getClientDataDTO(String clientRequestString) {
		return JSON.parseObject(clientRequestString, getTClass());
	};
	
	public ApiBizSupportEnum checkSupport(RequestContext requestContext) {
		return ApiBizSupportEnum.SUPPORT;
	}
	
	public static boolean isNumeric(String str) {
        for (int i = 0; i < str.length(); i++) {
//            System.out.println(str.charAt(i));
            if (!Character.isDigit(str.charAt(i))) {
                return false;
            }
        }
        return true;
    }

	public String unicodeToString(String str) {
		Pattern pattern = Pattern.compile("(\\\\u(\\p{XDigit}{4}))");
		Matcher matcher = pattern.matcher(str);
		char ch;
		while (matcher.find()) {
			String group = matcher.group(2);
			ch = (char) Integer.parseInt(group, 16);
			String group1 = matcher.group(1);
			str = str.replace(group1, ch + "");
		}
		return str;
	}

	private Class<clientRequest> getTClass() {
		Class<clientRequest> tClass = (Class<clientRequest>) ((ParameterizedType) getClass().getGenericSuperclass())
				.getActualTypeArguments()[0];
		return tClass;
	}

	/**
	 * @Title: bizDataNotNull
	 * @Description: 获取数据并验证非空
	 * <AUTHOR>
	 * @param data
	 *            数据
	 * @param dataName
	 *            数据名称用于为空时的错误提示
	 * @return
	 */
	public String getDataNotNull(String data, String dataName) {
		if (CsStringUtils.isEmpty(data)) {
			log.error("L2 验证client 端数据验证未通过，{}  为空 ", dataName);
			throw new BizException(APICenterExceptionCode.CLIENT_DATA_NULL, dataName);
		}
		return data;
	}

	/**
	 * @Title: setNotNullClientParms
	 * @Description: 设置非空值
	 * <AUTHOR>
	 * @param clientParms<参数名称,
	 *            参数值>
	 */
	protected abstract void setNotNullClientParms(Map<String, String> clientParms, clientRequest clientRequest,
			SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean);

	public ItemResult<Boolean> validateClientRequest(RequestContext requestContext) {
		//log.info("L2 BaseDataTransfer validateClientRequest start requestContext===>{}", requestContext);
		ApiConfigBean ApiConfigBean = requestContext.getApiConfigBean();
		SysConfig SysConfig = requestContext.getSysConfig();
		SysRoute sysRoute = requestContext.getSysRoute();
		Map<String, String> clientParms = new HashMap<>();
		this.setNotNullClientParms(clientParms, (clientRequest) requestContext.getClientDataDTO(), sysRoute, SysConfig,
				ApiConfigBean);
		if (!clientParms.isEmpty()) {
			for (Map.Entry<String, String> entry : clientParms.entrySet()) {
				if (isEmpty(entry.getValue())) {
					ItemResult<Boolean> result = new ItemResult<Boolean>(Boolean.FALSE);
					result.setCode(APICenterExceptionCode.CLIENT_DATA_NULL.getCode());
					result.setSuccess(false);
					result.setDescription(
							new BizException(APICenterExceptionCode.CLIENT_DATA_NULL, entry.getKey()).getMessage());
					return result;
				}
			}
		}
		ItemResult<Boolean> result = new ItemResult<Boolean>(Boolean.TRUE);
		result.setSuccess(true);
		return result;

	}

	public String getDataNotNull(String data, CodeMeta errorCode) {
		if (CsStringUtils.isEmpty(data)) {
			log.error(errorCode.getMsg());
			throw new BizException(errorCode);
		}
		return data;
	}

	public boolean isEmpty(String data) {
		return CsStringUtils.isEmpty(data);
	}

	public boolean isNotEmpty(String data) {
		return CsStringUtils.isNotEmpty(data);
	}
	
	public String getEmptyString(String data) {
		if(isEmpty(data) || "null".equals(data)) {
			return "";
		}
		return data;
	}

	protected void createResponseInfoWhenResponseParseFail(ResponseInfo responseInfo, RequestContext requestContext) {
		log.error("L2 BaseCrcementTransfer transferToClientResponse 解析ERP报文错误");
		responseInfo.setProcessResult(ProcessResult.FAIL);
		requestContext.getResponseMessage().setRequestStatus(RequestStatusEnum.RESPONSE_FAIL);
		requestContext.setMsg(APICenterExceptionCode.RESPONSE_PARSE_ERROR.getMsg());
		requestContext.setCode(APICenterExceptionCode.RESPONSE_PARSE_ERROR.getCode());
		responseInfo.setProcessResult(ProcessResult.FAIL);
		responseInfo.setCode(APICenterExceptionCode.RESPONSE_PARSE_ERROR.getCode());
		responseInfo.setMessage(APICenterExceptionCode.RESPONSE_PARSE_ERROR.getMsg().replace("{}",
				String.valueOf(requestContext.getResponseMessage().getBody())));
		requestContext.setResponseInfo(responseInfo);
		//log.info("API Flow 请求发送完成，解析报文失败。构建responseInfo===>{}", responseInfo);
	}

}
