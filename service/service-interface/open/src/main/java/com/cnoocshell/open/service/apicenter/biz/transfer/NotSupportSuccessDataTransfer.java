package com.ecommerce.open.service.apicenter.biz.transfer;

import java.util.Map;

import org.springframework.stereotype.Service;

import com.ecommerce.open.api.dto.apicenter.BaseClientRequestDTO;
import com.ecommerce.open.api.dto.apicenter.BaseClientResponseDTO;
import com.ecommerce.open.dao.bean.ApiConfigBean;
import com.ecommerce.open.dao.vo.SysConfig;
import com.ecommerce.open.dao.vo.SysRoute;
import com.ecommerce.open.enums.ApiBizSupportEnum;
import com.ecommerce.open.service.apicenter.dto.RequestContext;
import com.ecommerce.open.service.apicenter.dto.RequestInfo;


@Service("notSupportSuccessDataTransfer")
public class NotSupportSuccessDataTransfer extends BaseDataTransfer<BaseClientRequestDTO, BaseClientResponseDTO, String, String>{

	public ApiBizSupportEnum checkSupport(RequestContext requestContext) {
		return ApiBizSupportEnum.NOT_SUPPORT_SUCCESS;
	}
	
	@Override
	public String transferURL(RequestContext requestContext) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public void transferToServerRequest(RequestContext requestContext) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void transferToClientResponse(RequestContext requestContext) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public RequestInfo getRequestInfo(RequestContext requestContext) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public Map<String, String> getHeaderParms(RequestContext requestContext) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	protected void setNotNullClientParms(Map<String, String> clientParms, BaseClientRequestDTO clientRequest,
			SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
		// TODO Auto-generated method stub
		
	}

}
