package com.ecommerce.open.service.apicenter.biz.transfer.crcement;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.open.api.dto.apicenter.BaseClientResponseDTO;
import com.ecommerce.open.api.dto.apicenter.logistic.ECShipBillCloseCallbackRequestDTO;
import com.ecommerce.open.api.dto.apicenter.logistic.ShipBillCloseERPCallbackDTO;
import com.ecommerce.open.dao.bean.ApiConfigBean;
import com.ecommerce.open.dao.vo.SysConfig;
import com.ecommerce.open.dao.vo.SysRoute;
import com.ecommerce.open.enums.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * EC-SHP-Z1
 * 船运计划作废处理结果反馈
 */
@Slf4j
@Service("cementECCloseShipBillCallbackTransfer")
public class CementECCloseShipBillCallbackTransfer extends BaseCrcementCallbackTransfer<ECShipBillCloseCallbackRequestDTO, BaseClientResponseDTO, ItemResult<ShipBillCloseERPCallbackDTO>, String> {

    @Override
    protected void setNotNullClientParms(Map<String, String> clientParms, ECShipBillCloseCallbackRequestDTO ecShipBillCloseCallbackRequestDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        clientParms.put("电商运单号", ecShipBillCloseCallbackRequestDTO.getEcBillCode());
        clientParms.put("外部船运计划ID", ecShipBillCloseCallbackRequestDTO.getExternalBillId());
    }

    @Override
    public ItemResult<ShipBillCloseERPCallbackDTO> createDefaultFailServerRequestData(JSONObject jsonmap, String serverBillNo) {
        ItemResult<ShipBillCloseERPCallbackDTO> itemResult = new ItemResult<>();
        ShipBillCloseERPCallbackDTO callbackDTO = new ShipBillCloseERPCallbackDTO();
        callbackDTO.setEcBillCode(serverBillNo);
        itemResult.setData(callbackDTO);
        itemResult.setSuccess(false);

        return itemResult;
    }

    @Override
    protected String getServerBillNo(ECShipBillCloseCallbackRequestDTO ecShipBillCloseCallbackRequestDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        return ecShipBillCloseCallbackRequestDTO.getEcBillCode();
    }

    @Override
    protected ProcessResult getProcessResult(ECShipBillCloseCallbackRequestDTO ecShipBillCloseCallbackRequestDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        if (ecShipBillCloseCallbackRequestDTO != null && CsStringUtils.equals("01", ecShipBillCloseCallbackRequestDTO.getCode())) {
            return ProcessResult.SUCCESS;
        }
        return ProcessResult.FAIL;
    }

    @Override
    protected ItemResult<ShipBillCloseERPCallbackDTO> fillServerRequestData(ECShipBillCloseCallbackRequestDTO ecShipBillCloseCallbackRequestDTO, JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        log.info("船运计划作废处理结果反馈 fillServerRequestData : {}", JSON.toJSONString(ecShipBillCloseCallbackRequestDTO));
        ItemResult<ShipBillCloseERPCallbackDTO> itemResult = new ItemResult<>();
        ShipBillCloseERPCallbackDTO callbackDTO = new ShipBillCloseERPCallbackDTO();
        if(ecShipBillCloseCallbackRequestDTO == null){
            itemResult.setSuccess(false);
            return itemResult;
        }

        if (CsStringUtils.equals("01", ecShipBillCloseCallbackRequestDTO.getCode())) {
            BeanUtils.copyProperties(ecShipBillCloseCallbackRequestDTO, callbackDTO);
            itemResult.setSuccess(true);
        } else {
            callbackDTO.setEcBillCode(ecShipBillCloseCallbackRequestDTO.getEcBillCode());
            itemResult.setDescription(ecShipBillCloseCallbackRequestDTO.getMessage());
            itemResult.setSuccess(false);
        }
        log.info("ShipBillCloseERPCallbackDTO : {}", JSON.toJSONString(callbackDTO));
        itemResult.setData(callbackDTO);

        return itemResult;
    }

    @Override
    protected String getClientBillNo(ECShipBillCloseCallbackRequestDTO ecShipBillCloseCallbackRequestDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        return ecShipBillCloseCallbackRequestDTO.getExternalBillId();
    }

    @Override
    protected BaseClientResponseDTO fillClientResponseData(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        return new BaseClientResponseDTO();
    }
}
