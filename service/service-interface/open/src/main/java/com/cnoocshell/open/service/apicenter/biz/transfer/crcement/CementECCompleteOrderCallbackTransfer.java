package com.ecommerce.open.service.apicenter.biz.transfer.crcement;

import com.alibaba.fastjson.JSONObject;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.open.api.dto.apicenter.BaseClientResponseDTO;
import com.ecommerce.open.api.dto.apicenter.cement.ECModifyOrderCBRequestDTO;
import com.ecommerce.open.dao.bean.ApiConfigBean;
import com.ecommerce.open.dao.vo.SysConfig;
import com.ecommerce.open.dao.vo.SysRoute;
import com.ecommerce.open.enums.ProcessResult;
import com.ecommerce.open.api.dto.pom.order.OrderCompleteERPCallback;
import com.ecommerce.open.enums.pom.order.OrderERPStatusEnum;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Map;


@Service("cementECCompleteOrderCallbackTransfer")
public class CementECCompleteOrderCallbackTransfer extends BaseCrcementCallbackTransfer<ECModifyOrderCBRequestDTO, BaseClientResponseDTO, OrderCompleteERPCallback, String> {

    @Override
    public OrderCompleteERPCallback createDefaultFailServerRequestData(JSONObject jsonmap, String serverBillNo) {
        OrderCompleteERPCallback callback = new OrderCompleteERPCallback();
        callback.setEcPlanBillId(serverBillNo);
        callback.setStatus("10");
        return callback;
    }

    @Override
    protected String getServerBillNo(ECModifyOrderCBRequestDTO ecModifyOrderCBRequestDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        return ecModifyOrderCBRequestDTO.getEcPlanBillId();
    }

    @Override
    protected ProcessResult getProcessResult(ECModifyOrderCBRequestDTO ecModifyOrderCBRequestDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        if (ecModifyOrderCBRequestDTO == null) {
            return ProcessResult.FAIL;
        }

        if (CsStringUtils.equals("01", ecModifyOrderCBRequestDTO.getCode())) {
            return ProcessResult.SUCCESS;
        }

        return ProcessResult.FAIL;
    }

    @Override
    protected OrderCompleteERPCallback fillServerRequestData(ECModifyOrderCBRequestDTO ecCreateOrderCBRequestDTO, JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        if (ecCreateOrderCBRequestDTO == null) {
            return null;
        }
        OrderCompleteERPCallback callback = new OrderCompleteERPCallback();
        BeanUtils.copyProperties(ecCreateOrderCBRequestDTO, callback);
        if("E0B02D15".equals(ecCreateOrderCBRequestDTO.getCode()) || "E0B02D15".equals(ecCreateOrderCBRequestDTO.getProcessCode())) {
        	callback.setStatus(OrderERPStatusEnum.ORDER_COMPLETE_ERP_ERROR.getCode());
		}else {
			callback.setStatus(OrderERPStatusEnum.ORDER_COMPLETE_ERP_SUCCESS.getCode());
		}
		if(!CsStringUtils.isNullOrBlank(ecCreateOrderCBRequestDTO.getProcessMessage())) {
			callback.setMessage(ecCreateOrderCBRequestDTO.getProcessMessage());
		}else {
			callback.setMessage(ecCreateOrderCBRequestDTO.getMessage());
		}
        
        
        return callback;
    }

    @Override
    protected String getClientBillNo(ECModifyOrderCBRequestDTO ecModifyOrderCBRequestDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        return ecModifyOrderCBRequestDTO.getERPDistributeCode();
    }

    @Override
    protected BaseClientResponseDTO fillClientResponseData(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        return new BaseClientResponseDTO();
    }

    @Override
    protected void setNotNullClientParms(Map<String, String> clientParms, ECModifyOrderCBRequestDTO ecModifyOrderCBRequestDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        clientParms.put("响应消息代码", ecModifyOrderCBRequestDTO.getCode());
        clientParms.put("响应消息具体内容", ecModifyOrderCBRequestDTO.getMessage());
        clientParms.put("电商单据编号", ecModifyOrderCBRequestDTO.getEcPlanBillId());
        clientParms.put("配送需求状态", ecModifyOrderCBRequestDTO.getStatus());
        clientParms.put("ERP提货计划编号", ecModifyOrderCBRequestDTO.getERPDistributeCode());
    }
}
