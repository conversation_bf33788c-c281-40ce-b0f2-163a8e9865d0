package com.ecommerce.open.service.apicenter.biz.transfer.crcement;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.open.api.dto.apicenter.BaseClientResponseDTO;
import com.ecommerce.open.api.dto.apicenter.logistic.ECShipBillCallbackRequestDTO;
import com.ecommerce.open.api.dto.apicenter.logistic.ShipBillERPCallbackDTO;
import com.ecommerce.open.dao.bean.ApiConfigBean;
import com.ecommerce.open.dao.vo.SysConfig;
import com.ecommerce.open.dao.vo.SysRoute;
import com.ecommerce.open.enums.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * EC-SHP-B1
 * 船运计划信息下发反馈
 */
@Slf4j
@Service("cementECCreateShipBillCallbackTransfer")
public class CementECCreateShipBillCallbackTransfer extends BaseCrcementCallbackTransfer<ECShipBillCallbackRequestDTO, BaseClientResponseDTO, ItemResult<ShipBillERPCallbackDTO>, String> {

    @Override
    protected void setNotNullClientParms(Map<String, String> clientParms, ECShipBillCallbackRequestDTO ecShipBillCallbackRequestDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        clientParms.put("电商运单号", ecShipBillCallbackRequestDTO.getEcBillCode());
        clientParms.put("外部船运计划ID", ecShipBillCallbackRequestDTO.getExternalBillId());
    }

    @Override
    public ItemResult<ShipBillERPCallbackDTO> createDefaultFailServerRequestData(JSONObject jsonmap, String serverBillNo) {
        ItemResult<ShipBillERPCallbackDTO> itemResult = new ItemResult<>();
        ShipBillERPCallbackDTO callbackDTO = new ShipBillERPCallbackDTO();
        callbackDTO.setEcBillCode(serverBillNo);
        itemResult.setData(callbackDTO);
        itemResult.setSuccess(false);

        return itemResult;
    }

    @Override
    protected String getServerBillNo(ECShipBillCallbackRequestDTO ecShipBillCallbackRequestDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        return ecShipBillCallbackRequestDTO.getEcBillCode();
    }

    @Override
    protected ProcessResult getProcessResult(ECShipBillCallbackRequestDTO ecShipBillCallbackRequestDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        if (ecShipBillCallbackRequestDTO != null && CsStringUtils.equals("01", ecShipBillCallbackRequestDTO.getCode())) {
            return ProcessResult.SUCCESS;
        }
        return ProcessResult.FAIL;
    }

    @Override
    protected ItemResult<ShipBillERPCallbackDTO> fillServerRequestData(ECShipBillCallbackRequestDTO ecShipBillCallbackRequestDTO, JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        log.info("船运计划信息下发反馈 fillServerRequestData : {}", JSON.toJSONString(ecShipBillCallbackRequestDTO));
        ItemResult<ShipBillERPCallbackDTO> itemResult = new ItemResult<>();
        ShipBillERPCallbackDTO callbackDTO = new ShipBillERPCallbackDTO();
        if(ecShipBillCallbackRequestDTO == null){
            itemResult.setSuccess(false);
            return itemResult;
        }

        if (CsStringUtils.equals("01", ecShipBillCallbackRequestDTO.getCode())) {
            BeanUtils.copyProperties(ecShipBillCallbackRequestDTO, callbackDTO);
            itemResult.setSuccess(true);
        } else {
            callbackDTO.setEcBillCode(ecShipBillCallbackRequestDTO.getEcBillCode());
            itemResult.setDescription(ecShipBillCallbackRequestDTO.getMessage());
            itemResult.setSuccess(false);
        }
        log.info("ShipBillERPCallbackDTO : {}", JSON.toJSONString(callbackDTO));
        itemResult.setData(callbackDTO);

        return itemResult;
    }

    @Override
    protected String getClientBillNo(ECShipBillCallbackRequestDTO ecShipBillCallbackRequestDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        return ecShipBillCallbackRequestDTO.getExternalBillId();
    }

    @Override
    protected BaseClientResponseDTO fillClientResponseData(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        return new BaseClientResponseDTO();
    }
}
