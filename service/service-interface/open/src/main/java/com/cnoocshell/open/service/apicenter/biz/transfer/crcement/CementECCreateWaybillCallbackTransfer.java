package com.ecommerce.open.service.apicenter.biz.transfer.crcement;

import com.alibaba.fastjson.JSONObject;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.open.api.dto.pom.logistics.ERPWaybillItemDTO;
import com.ecommerce.open.api.dto.apicenter.BaseClientResponseDTO;
import com.ecommerce.open.api.dto.apicenter.cement.logistics.ECCreateWaybillCBRequestDTO;
import com.ecommerce.open.api.dto.pom.logistics.ERPWaybillDTO;
import com.ecommerce.open.api.dto.apicenter.cement.logistics.ERPGoodsItem;
import com.ecommerce.open.dao.bean.ApiConfigBean;
import com.ecommerce.open.dao.vo.SysConfig;
import com.ecommerce.open.dao.vo.SysRoute;
import com.ecommerce.open.enums.ProcessResult;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Map;


@Slf4j
@Service("cementECCreateWaybillCallbackTransfer")
public class CementECCreateWaybillCallbackTransfer extends BaseCrcementCallbackTransfer<ECCreateWaybillCBRequestDTO, BaseClientResponseDTO, ItemResult<ERPWaybillDTO>, String>{
	@Override
	public ItemResult<ERPWaybillDTO> createDefaultFailServerRequestData(JSONObject jsonmap, String serverBillNo) {
		ItemResult<ERPWaybillDTO> request = new ItemResult<>();
		ERPWaybillDTO ecWaybillDTO = new ERPWaybillDTO();
//		ecWaybillDTO.setRequestNo(jsonmap.getString("requestNo"));
		ecWaybillDTO.setWaybillNum(serverBillNo);
		ecWaybillDTO.setStatus("02");
		request.setData(ecWaybillDTO);
		request.setSuccess(false);
		return request;
	}

	@Override
	protected String getServerBillNo(ECCreateWaybillCBRequestDTO ecCreateWaybillCBRequestDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
		return ecCreateWaybillCBRequestDTO.getEcBillCode();
	}

	@Override
	protected ProcessResult getProcessResult(ECCreateWaybillCBRequestDTO ecCreateWaybillCBRequestDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
		if (ecCreateWaybillCBRequestDTO != null && CsStringUtils.equals("01", ecCreateWaybillCBRequestDTO.getBillStatus())) {
			return ProcessResult.SUCCESS;
		}
		return ProcessResult.FAIL;
	}

	@Override
	protected ItemResult<ERPWaybillDTO> fillServerRequestData(ECCreateWaybillCBRequestDTO ecCreateWaybillCBRequestDTO, JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
		ItemResult<ERPWaybillDTO> request = new ItemResult<>();
		ERPWaybillDTO waybillDTO = new ERPWaybillDTO();
//		waybillDTO.setStatus(ecCreateWaybillCBRequestDTO.getBillStatus());
		waybillDTO.setWaybillNum(ecCreateWaybillCBRequestDTO.getEcBillCode());
//		waybillDTO.setRequestNo(ecCreateWaybillCBRequestDTO.getBaseRequestNo());
		waybillDTO.setErpWaybillNum(ecCreateWaybillCBRequestDTO.getExternalBillId());
		String createTimeStr = ecCreateWaybillCBRequestDTO.getExternalCreateDate();
		if (CsStringUtils.isNotBlank(createTimeStr)) {
			SimpleDateFormat sdf =new SimpleDateFormat("yyyyMMddHHmmss");
			try {
				waybillDTO.setCreateTime(sdf.parse(createTimeStr));
			} catch (ParseException e) {
				log.error("转换创建时间发生异常:{}", createTimeStr, e);
			}
		}

		List<ERPGoodsItem> goodsList = ecCreateWaybillCBRequestDTO.getGoodsList();
		if (CollectionUtils.isNotEmpty(goodsList)) {
			List<ERPWaybillItemDTO> waybillItemList = Lists.newArrayList();
			for (ERPGoodsItem item : goodsList) {
				if (item == null) {
					continue;
				}
				ERPWaybillItemDTO dto = new ERPWaybillItemDTO();
				dto.setCommodityCode(item.getCommodityCode());
				dto.setQrCode(item.getQrCode());
				waybillItemList.add(dto);
			}
			waybillDTO.setWaybillItemList(waybillItemList);
		}

		request.setData(waybillDTO);
		if (CsStringUtils.equals("E0B02D15", ecCreateWaybillCBRequestDTO.getCode()) || CsStringUtils.equals("E0B02D15", ecCreateWaybillCBRequestDTO.getProcessCode())) {
			request.setSuccess(false);
		} else if (CsStringUtils.equals("01", ecCreateWaybillCBRequestDTO.getBillStatus())) {
			request.setSuccess(true);
		} else {
			request.setSuccess(false);
		}
		String message = ecCreateWaybillCBRequestDTO.getMessage();
		if (CsStringUtils.isBlank(message)) {
			message = ecCreateWaybillCBRequestDTO.getProcessMessage();
		}
		request.setDescription(message);
		return request;
	}

	@Override
	protected String getClientBillNo(ECCreateWaybillCBRequestDTO ecCreateWaybillCBRequestDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
		return ecCreateWaybillCBRequestDTO.getExternalBillId();
	}

	@Override
	protected BaseClientResponseDTO fillClientResponseData(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
		return new BaseClientResponseDTO();
	}

	@Override
	protected void setNotNullClientParms(Map<String, String> clientParms, ECCreateWaybillCBRequestDTO ecCreateWaybillCBRequestDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
		clientParms.put("原请求号", ecCreateWaybillCBRequestDTO.getBaseRequestNo());
		clientParms.put("电商运单号", ecCreateWaybillCBRequestDTO.getEcBillCode());
		clientParms.put("外部运单号", ecCreateWaybillCBRequestDTO.getExternalBillId());
		clientParms.put("运单状态", ecCreateWaybillCBRequestDTO.getBillStatus());
		clientParms.put("运单创建时间", ecCreateWaybillCBRequestDTO.getExternalCreateDate());
		List<ERPGoodsItem> goodsList = ecCreateWaybillCBRequestDTO.getGoodsList();
		if (CollectionUtils.isEmpty(goodsList)) {
			clientParms.put("商品list", null);
			return;
		}
		for (int i = 0; i < goodsList.size(); i++) {
			ERPGoodsItem item = goodsList.get(i);
			clientParms.put("商品编号" + i, item.getCommodityCode());
			clientParms.put("开票二维码" + i, item.getQrCode());
		}
	}
}
