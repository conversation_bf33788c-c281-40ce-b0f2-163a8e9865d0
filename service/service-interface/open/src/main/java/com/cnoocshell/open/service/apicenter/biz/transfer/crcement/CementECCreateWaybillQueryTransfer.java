package com.ecommerce.open.service.apicenter.biz.transfer.crcement;

import com.alibaba.fastjson.JSONObject;
import com.ecommerce.open.api.dto.apicenter.logistic.ECWaybillDTO;
import com.ecommerce.open.dao.bean.ApiConfigBean;
import com.ecommerce.open.dao.vo.SysConfig;
import com.ecommerce.open.dao.vo.SysRoute;
import com.ecommerce.open.enums.ProcessResult;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;


@Service("cementECCreateWaybillQueryTransfer")
public class CementECCreateWaybillQueryTransfer extends BaseCrcementTransfer<ECWaybillDTO, ECWaybillDTO, String, String> {

    @Override
    protected String fillServerRequestData(ECWaybillDTO ecWaybillDTO, JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        //原请求号在外层添加
        jsonmap.put("ecBillCode",ecWaybillDTO.getWaybillNum());
        jsonmap.put("baseRequestNo",ecWaybillDTO.getBaseRequestNo());
        return jsonmap.toJSONString();
    }

    @Override
    protected String getClientBillNo(ECWaybillDTO ecWaybillDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        return ecWaybillDTO.getWaybillNum();
    }

    @Override
    protected String getServerBillNo(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        return null;
    }

    @Override
    protected ProcessResult getProcessResult(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        if (CsStringUtils.equals(jsonmap.getString("code"), "01")) {
            return ProcessResult.SUCCESS;
        }
        return ProcessResult.FAIL;
    }

    @Override
    protected ECWaybillDTO fillClientResponseData(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        ECWaybillDTO resultDTO = new ECWaybillDTO();
        resultDTO.setWaybillNum(jsonmap.getString("ecBillCode"));
        resultDTO.setStatus(jsonmap.getString("billStatus"));
        resultDTO.setMessage(jsonmap.getString("message"));

        if (CsStringUtils.isNotBlank(jsonmap.getString("externalBillId"))) {
            Date createTime = new Date();
            String createTimeStr = jsonmap.getString("externalCreateDate");
            if (CsStringUtils.isNotBlank(createTimeStr)) {
                SimpleDateFormat sdf =new SimpleDateFormat("yyyyMMddHHmmss");
                try {
                    createTime = sdf.parse(createTimeStr);
                } catch (ParseException e) {
                }
            }


            resultDTO.setErpWaybillNum(jsonmap.getString("externalBillId"));
            resultDTO.setQrCode(jsonmap.getString("qrCode"));
            resultDTO.setCreateTime(createTime);//外部运单创建时间
            resultDTO.setBillWeight(jsonmap.getBigDecimal("billWeight"));
            resultDTO.setCommodityCode(jsonmap.getString("commodityCode"));
            resultDTO.setRealBillWeight(jsonmap.getBigDecimal("realBillWeight"));
            resultDTO.setRealCostAmount(getYuan(jsonmap.getString("realCostAmount")).toPlainString());
            resultDTO.setRealUnitPrice(getYuan(jsonmap.getString("realUnitPrice")).toPlainString());
            resultDTO.setLogisticUnitPrice(getYuan(jsonmap.getString("logisticUnitPrice")).toPlainString());
            resultDTO.setFactoryNumber(jsonmap.getString("factoryNumber"));
            resultDTO.setSrcPlant(jsonmap.getString("srcPlant"));
            resultDTO.setExecuteStatus(jsonmap.getString("executeStatus"));
        }

        return resultDTO;
    }

    @Override
    protected void setNotNullClientParms(Map<String, String> clientParms, ECWaybillDTO ecWaybillDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        clientParms.put("电商运单号", ecWaybillDTO.getWaybillNum());
    }
}
