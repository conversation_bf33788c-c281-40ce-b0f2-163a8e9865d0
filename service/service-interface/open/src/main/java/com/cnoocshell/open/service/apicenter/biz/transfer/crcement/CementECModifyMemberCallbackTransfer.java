package com.ecommerce.open.service.apicenter.biz.transfer.crcement;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.ecommerce.open.api.dto.pom.member.ErpExecueResultDTO;
import com.ecommerce.open.api.dto.apicenter.BaseClientResponseDTO;
import com.ecommerce.open.api.dto.apicenter.cement.ECModifyMemberCBRequestDTO;
import com.ecommerce.open.dao.bean.ApiConfigBean;
import com.ecommerce.open.dao.vo.SysConfig;
import com.ecommerce.open.dao.vo.SysRoute;
import com.ecommerce.open.enums.ProcessResult;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Map;


@Service("cementECModifyMemberCallbackTransfer")
public class CementECModifyMemberCallbackTransfer extends BaseCrcementCallbackTransfer<ECModifyMemberCBRequestDTO, BaseClientResponseDTO, ErpExecueResultDTO, String> {

    /**
     * 请求失败,生成的默认的callback
     * @param jsonmap
     * @param serverBillNo : 表示 request请求的clientNo, 此次表示callback的serverNo
     * @return
     */
    @Override
    public ErpExecueResultDTO createDefaultFailServerRequestData(JSONObject jsonmap, String serverBillNo) {
        ErpExecueResultDTO resultDTO = new ErpExecueResultDTO();
        resultDTO.setMemberCode(serverBillNo);
        resultDTO.setProcResult("160");
        resultDTO.setProcMsg(jsonmap.getString("xmessage"));
        return resultDTO;
    }

    @Override
    protected String getServerBillNo(ECModifyMemberCBRequestDTO ecModifyMemberCBRequestDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        return ecModifyMemberCBRequestDTO.getMemberCode();
    }

    @Override
    protected ProcessResult getProcessResult(ECModifyMemberCBRequestDTO ecModifyMemberCBRequestDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        if (ecModifyMemberCBRequestDTO == null) {
            return ProcessResult.FAIL;
        }

        String procResult = ecModifyMemberCBRequestDTO.getProcResult();
        if (CsStringUtils.equals(procResult, "100") || CsStringUtils.equals(procResult, "200")) {
            return ProcessResult.SUCCESS;
        }

        return ProcessResult.FAIL;
    }

    /**
     * 构造发送ec的dto
     * @param ecModifyMemberCBRequestDTO
     * @param jsonmap
     * @param sysRoute
     * @param SysConfig
     * @param ApiConfigBean
     * @return
     */
    @Override
    protected ErpExecueResultDTO fillServerRequestData(ECModifyMemberCBRequestDTO ecModifyMemberCBRequestDTO, JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        //ecModifyMemberCBRequestDTO --> ErpExecueResultDTO
        ErpExecueResultDTO resultDTO = new ErpExecueResultDTO();
        if (ecModifyMemberCBRequestDTO == null) {
            return null;
        }
        BeanUtils.copyProperties(ecModifyMemberCBRequestDTO, resultDTO);
        return resultDTO;
    }

    @Override
    protected String getClientBillNo(ECModifyMemberCBRequestDTO ecModifyMemberCBRequestDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        return ecModifyMemberCBRequestDTO.getMdmCode();
    }

    @Override
    protected BaseClientResponseDTO fillClientResponseData(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        //todo 构造返回erp的报文
        return new BaseClientResponseDTO();
    }

    @Override
    protected void setNotNullClientParms(Map<String, String> clientParms, ECModifyMemberCBRequestDTO ecModifyMemberCBRequestDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        clientParms.put("请求号", ecModifyMemberCBRequestDTO.getBaseRequestNo());
        clientParms.put("电商编号", ecModifyMemberCBRequestDTO.getMemberCode());
        clientParms.put("处理结果", ecModifyMemberCBRequestDTO.getProcResult());
        clientParms.put("ERP客户编码", ecModifyMemberCBRequestDTO.getMdmCode());
        clientParms.put("客户状态", ecModifyMemberCBRequestDTO.getMemberStatus());
        clientParms.put("处理说明", ecModifyMemberCBRequestDTO.getMemberStatus());
    }
}
