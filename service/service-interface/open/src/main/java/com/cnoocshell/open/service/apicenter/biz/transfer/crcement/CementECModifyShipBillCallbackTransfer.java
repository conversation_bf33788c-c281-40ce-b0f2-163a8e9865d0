package com.ecommerce.open.service.apicenter.biz.transfer.crcement;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.open.api.dto.apicenter.BaseClientResponseDTO;
import com.ecommerce.open.api.dto.apicenter.logistic.ECShipBillModifyCallbackRequestDTO;
import com.ecommerce.open.api.dto.apicenter.logistic.ShipBillModifyERPCallbackDTO;
import com.ecommerce.open.dao.bean.ApiConfigBean;
import com.ecommerce.open.dao.vo.SysConfig;
import com.ecommerce.open.dao.vo.SysRoute;
import com.ecommerce.open.enums.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * EC-SHP-S1
 * 船运计划信息修改处理结果反馈
 */
@Slf4j
@Service("cementECModifyShipBillCallbackTransfer")
public class CementECModifyShipBillCallbackTransfer extends BaseCrcementCallbackTransfer<ECShipBillModifyCallbackRequestDTO, BaseClientResponseDTO, ItemResult<ShipBillModifyERPCallbackDTO>, String> {

    @Override
    protected void setNotNullClientParms(Map<String, String> clientParms, ECShipBillModifyCallbackRequestDTO ecShipBillModifyCallbackRequestDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        clientParms.put("电商运单号", ecShipBillModifyCallbackRequestDTO.getEcBillCode());
        clientParms.put("外部船运计划ID", ecShipBillModifyCallbackRequestDTO.getExternalBillId());
    }

    @Override
    public ItemResult<ShipBillModifyERPCallbackDTO> createDefaultFailServerRequestData(JSONObject jsonmap, String serverBillNo) {
        ItemResult<ShipBillModifyERPCallbackDTO> itemResult = new ItemResult<>();
        ShipBillModifyERPCallbackDTO callbackDTO = new ShipBillModifyERPCallbackDTO();
        callbackDTO.setEcBillCode(serverBillNo);
        itemResult.setData(callbackDTO);
        itemResult.setSuccess(false);

        return itemResult;
    }

    @Override
    protected String getServerBillNo(ECShipBillModifyCallbackRequestDTO ecShipBillModifyCallbackRequestDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        return ecShipBillModifyCallbackRequestDTO.getEcBillCode();
    }

    @Override
    protected ProcessResult getProcessResult(ECShipBillModifyCallbackRequestDTO ecShipBillModifyCallbackRequestDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        if (ecShipBillModifyCallbackRequestDTO != null && CsStringUtils.equals("01", ecShipBillModifyCallbackRequestDTO.getCode())) {
            return ProcessResult.SUCCESS;
        }
        return ProcessResult.FAIL;
    }

    @Override
    protected ItemResult<ShipBillModifyERPCallbackDTO> fillServerRequestData(ECShipBillModifyCallbackRequestDTO ecShipBillModifyCallbackRequestDTO, JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        log.info("船运计划信息修改处理结果反馈 fillServerRequestData : {}", JSON.toJSONString(ecShipBillModifyCallbackRequestDTO));
        ItemResult<ShipBillModifyERPCallbackDTO> itemResult = new ItemResult<>();
        ShipBillModifyERPCallbackDTO callbackDTO = new ShipBillModifyERPCallbackDTO();
        if(ecShipBillModifyCallbackRequestDTO == null){
            itemResult.setSuccess(false);
            return itemResult;
        }

        if (CsStringUtils.equals("01", ecShipBillModifyCallbackRequestDTO.getCode())) {
            BeanUtils.copyProperties(ecShipBillModifyCallbackRequestDTO, callbackDTO);
            itemResult.setSuccess(true);
        } else {
            callbackDTO.setEcBillCode(ecShipBillModifyCallbackRequestDTO.getEcBillCode());
            itemResult.setDescription(ecShipBillModifyCallbackRequestDTO.getMessage());
            itemResult.setSuccess(false);
        }
        log.info("ShipBillModifyERPCallbackDTO : {}", JSON.toJSONString(callbackDTO));
        itemResult.setData(callbackDTO);

        return itemResult;
    }

    @Override
    protected String getClientBillNo(ECShipBillModifyCallbackRequestDTO ecShipBillModifyCallbackRequestDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        return ecShipBillModifyCallbackRequestDTO.getExternalBillId();
    }

    @Override
    protected BaseClientResponseDTO fillClientResponseData(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        return new BaseClientResponseDTO();
    }
}
