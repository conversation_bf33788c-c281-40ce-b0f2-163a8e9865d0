package com.ecommerce.open.service.apicenter.biz.transfer.crcement;

import com.alibaba.fastjson.JSONObject;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.open.api.dto.apicenter.BaseClientResponseDTO;
import com.ecommerce.open.api.dto.apicenter.cement.logistics.ECModifyWaybillCBRequestDTO;
import com.ecommerce.open.api.dto.pom.logistics.ERPWaybillDTO;
import com.ecommerce.open.dao.bean.ApiConfigBean;
import com.ecommerce.open.dao.vo.SysConfig;
import com.ecommerce.open.dao.vo.SysRoute;
import com.ecommerce.open.enums.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Map;


@Slf4j
@Service("cementECModifyWaybillCallbackTransfer")
public class CementECModifyWaybillCallbackTransfer extends BaseCrcementCallbackTransfer<ECModifyWaybillCBRequestDTO, BaseClientResponseDTO, ItemResult<ERPWaybillDTO>, String> {

    @Override
    public ItemResult<ERPWaybillDTO> createDefaultFailServerRequestData(JSONObject jsonmap, String serverBillNo) {
        ItemResult<ERPWaybillDTO> result = new ItemResult<>();
        ERPWaybillDTO waybillDTO = new ERPWaybillDTO();
        waybillDTO.setWaybillNum(serverBillNo);
        result.setData(waybillDTO);
        result.setSuccess(false);
        return result;
    }

    @Override
    protected String getServerBillNo(ECModifyWaybillCBRequestDTO ecModifyWaybillCBRequestDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        return ecModifyWaybillCBRequestDTO.getEcBillCode();
    }

    @Override
    protected ProcessResult getProcessResult(ECModifyWaybillCBRequestDTO ecModifyWaybillCBRequestDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        if (ecModifyWaybillCBRequestDTO == null) {
            return ProcessResult.FAIL;
        }

        if (CsStringUtils.equals(ecModifyWaybillCBRequestDTO.getBillStatus(), "04")) {
            return ProcessResult.SUCCESS;
        }

        return ProcessResult.FAIL;
    }

    @Override
    protected ItemResult<ERPWaybillDTO> fillServerRequestData(ECModifyWaybillCBRequestDTO ecModifyWaybillCBRequestDTO, JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        ItemResult<ERPWaybillDTO> result = new ItemResult<>();
        ERPWaybillDTO waybillDTO = new ERPWaybillDTO();
        waybillDTO.setWaybillNum(ecModifyWaybillCBRequestDTO.getEcBillCode());
        waybillDTO.setQrCode(ecModifyWaybillCBRequestDTO.getQrCode());
//        waybillDTO.setRequestNo(ecModifyWaybillCBRequestDTO.getBaseRequestNo());
        waybillDTO.setErpWaybillNum(ecModifyWaybillCBRequestDTO.getExternalBillId());
        waybillDTO.setStatus(ecModifyWaybillCBRequestDTO.getBillStatus());
        String externalCreateDate = ecModifyWaybillCBRequestDTO.getExternalCreateDate();
        if (CsStringUtils.isNotBlank(externalCreateDate)) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            try {
                waybillDTO.setCreateTime(sdf.parse(externalCreateDate));
            } catch (ParseException e) {
                log.error("转换创建时间发生异常:{}", externalCreateDate, e);
            }
        }
        result.setData(waybillDTO);

        if (CsStringUtils.equals("E0B02D15", ecModifyWaybillCBRequestDTO.getCode()) || CsStringUtils.equals("E0B02D15", ecModifyWaybillCBRequestDTO.getProcessCode())) {
            result.setSuccess(false);
        } else if (CsStringUtils.equals(ecModifyWaybillCBRequestDTO.getBillStatus(), "04")) {
            result.setSuccess(true);
        } else {
            result.setSuccess(false);
        }
        String message = ecModifyWaybillCBRequestDTO.getMessage();
        if (CsStringUtils.isBlank(message)) {
            message = ecModifyWaybillCBRequestDTO.getProcessMessage();
        }
        result.setDescription(message);
        return result;
    }

    @Override
    protected String getClientBillNo(ECModifyWaybillCBRequestDTO ecModifyWaybillCBRequestDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        if (ecModifyWaybillCBRequestDTO == null) {
            return null;
        }
        return ecModifyWaybillCBRequestDTO.getExternalBillId();
    }

    @Override
    protected BaseClientResponseDTO fillClientResponseData(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        return new BaseClientResponseDTO();
    }

    @Override
    protected void setNotNullClientParms(Map<String, String> clientParms, ECModifyWaybillCBRequestDTO ecModifyWaybillCBRequestDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        clientParms.put("原请求号", ecModifyWaybillCBRequestDTO.getBaseRequestNo());
        clientParms.put("电商运单号", ecModifyWaybillCBRequestDTO.getEcBillCode());
        clientParms.put("外部运单号", ecModifyWaybillCBRequestDTO.getExternalBillId());
        clientParms.put("运单状态", ecModifyWaybillCBRequestDTO.getBillStatus());
        clientParms.put("运单创建时间", ecModifyWaybillCBRequestDTO.getExternalCreateDate());
    }
}
