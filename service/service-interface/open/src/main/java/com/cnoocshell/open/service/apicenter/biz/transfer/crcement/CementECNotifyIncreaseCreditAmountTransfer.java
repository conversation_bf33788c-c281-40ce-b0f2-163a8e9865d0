package com.ecommerce.open.service.apicenter.biz.transfer.crcement;

import com.alibaba.fastjson.JSONObject;
import com.ecommerce.open.api.dto.apicenter.BaseClientResponseDTO;
import com.ecommerce.open.api.dto.apicenter.pay.ERPCreditNotifyDTO;
import com.ecommerce.open.dao.bean.ApiConfigBean;
import com.ecommerce.open.dao.vo.SysConfig;
import com.ecommerce.open.dao.vo.SysRoute;
import com.ecommerce.open.enums.ProcessResult;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service("cementECNotifyIncreaseCreditAmountTransfer")
public class CementECNotifyIncreaseCreditAmountTransfer extends BaseCrcementTransfer<ERPCreditNotifyDTO, BaseClientResponseDTO, String, String> {

    @Override
    protected String fillServerRequestData(ERPCreditNotifyDTO erpCreditNotifyDTO, JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        jsonmap.put("ecBillCode", erpCreditNotifyDTO.getWaybillNum());
        jsonmap.put("externalBillId", erpCreditNotifyDTO.getExternalWaybillNum());
        jsonmap.put("ecPlanBillId", erpCreditNotifyDTO.getEcOrderCode());
        jsonmap.put("creditAmount", getCentAmount(erpCreditNotifyDTO.getCreditAmount()));

        return jsonmap.toJSONString();
    }

    @Override
    protected String getClientBillNo(ERPCreditNotifyDTO erpCreditNotifyDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        return null;
    }

    @Override
    protected String getServerBillNo(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        return null;
    }

    @Override
    protected ProcessResult getProcessResult(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        if (CsStringUtils.equals(jsonmap.getString("code"), "01")) {
            return ProcessResult.SUCCESS;
        }
        return ProcessResult.FAIL;
    }

    @Override
    protected BaseClientResponseDTO fillClientResponseData(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        return new BaseClientResponseDTO();
    }

    @Override
    protected void setNotNullClientParms(Map<String, String> clientParms, ERPCreditNotifyDTO erpCreditNotifyDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        clientParms.put("电商运单号", erpCreditNotifyDTO.getWaybillNum());
        clientParms.put("外部运单号", erpCreditNotifyDTO.getExternalWaybillNum());
        clientParms.put("电商订单号", erpCreditNotifyDTO.getEcOrderCode());
        clientParms.put("授信额度", getCentAmount(erpCreditNotifyDTO.getCreditAmount()));
    }
}
