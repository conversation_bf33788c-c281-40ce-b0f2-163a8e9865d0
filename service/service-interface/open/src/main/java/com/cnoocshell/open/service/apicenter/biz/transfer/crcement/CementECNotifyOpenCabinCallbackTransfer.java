package com.ecommerce.open.service.apicenter.biz.transfer.crcement;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.open.api.dto.apicenter.BaseClientResponseDTO;
import com.ecommerce.open.api.dto.apicenter.logistic.ECNotifyOpenCabinCallBackRequestDTO;
import com.ecommerce.open.api.dto.pom.logistics.ERPShipBillDTO;
import com.ecommerce.open.dao.bean.ApiConfigBean;
import com.ecommerce.open.dao.vo.SysConfig;
import com.ecommerce.open.dao.vo.SysRoute;
import com.ecommerce.open.enums.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;


@Slf4j
@Service("cementECNotifyOpenCabinCallbackTransfer")
public class CementECNotifyOpenCabinCallbackTransfer extends BaseCrcementCallbackTransfer<ECNotifyOpenCabinCallBackRequestDTO, BaseClientResponseDTO, ItemResult<ERPShipBillDTO> ,String>{

    @Override
    public ItemResult<ERPShipBillDTO> createDefaultFailServerRequestData(JSONObject jsonmap, String serverBillNo) {
        ERPShipBillDTO erpShipBillDTO = new ERPShipBillDTO();
        erpShipBillDTO.setWaybillNum(serverBillNo);
        ItemResult<ERPShipBillDTO> itemResult = new ItemResult<>();
        itemResult.setSuccess(false);
        itemResult.setData(erpShipBillDTO);
        return itemResult;
    }

    @Override
    protected String getServerBillNo(ECNotifyOpenCabinCallBackRequestDTO ecNotifyOpenCabinCallBackRequestDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        return ecNotifyOpenCabinCallBackRequestDTO.getEcBillCode();
    }

    @Override
    protected ProcessResult getProcessResult(ECNotifyOpenCabinCallBackRequestDTO ecNotifyOpenCabinCallBackRequestDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        String code = ecNotifyOpenCabinCallBackRequestDTO.getCode();
        if (CsStringUtils.equals(code, "01")) {
            return ProcessResult.SUCCESS;
        }
        return ProcessResult.FAIL;
    }

    @Override
    protected ItemResult<ERPShipBillDTO> fillServerRequestData(ECNotifyOpenCabinCallBackRequestDTO ecNotifyOpenCabinCallBackRequestDTO, JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        ItemResult<ERPShipBillDTO> itemResult = new ItemResult<>();
        ERPShipBillDTO erpShipBillDTO = new ERPShipBillDTO();
        erpShipBillDTO.setWaybillNum(ecNotifyOpenCabinCallBackRequestDTO.getEcBillCode());
        erpShipBillDTO.setExternalRequestNo(ecNotifyOpenCabinCallBackRequestDTO.getRequestNo());
        erpShipBillDTO.setExternalWaybillId(ecNotifyOpenCabinCallBackRequestDTO.getExternalBillId());
        erpShipBillDTO.setExternalWaybillNum(ecNotifyOpenCabinCallBackRequestDTO.getExternalBillNo());
        erpShipBillDTO.setOpenReult(CsStringUtils.equals(ecNotifyOpenCabinCallBackRequestDTO.getOpenReult(),"00"));
        erpShipBillDTO.setFailedMessage(ecNotifyOpenCabinCallBackRequestDTO.getFailedMessage());

        itemResult.setSuccess(CsStringUtils.equals(ecNotifyOpenCabinCallBackRequestDTO.getCode(), "S1A00000"));
        itemResult.setDescription(ecNotifyOpenCabinCallBackRequestDTO.getMessage());
        itemResult.setData(erpShipBillDTO);
        return itemResult;
    }

    @Override
    protected String getClientBillNo(ECNotifyOpenCabinCallBackRequestDTO ecNotifyOpenCabinCallBackRequestDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        if (ecNotifyOpenCabinCallBackRequestDTO == null) {
            return null;
        }
        return ecNotifyOpenCabinCallBackRequestDTO.getEcBillCode();
    }

    @Override
    protected BaseClientResponseDTO fillClientResponseData(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        return new BaseClientResponseDTO();
    }

    @Override
    protected void setNotNullClientParms(Map<String, String> clientParms, ECNotifyOpenCabinCallBackRequestDTO ecNotifyOpenCabinCallBackRequestDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {

    }
}
