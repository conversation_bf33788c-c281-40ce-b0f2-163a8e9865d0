package com.ecommerce.open.service.apicenter.biz.transfer.crcement;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.open.api.dto.apicenter.BaseClientResponseDTO;
import com.ecommerce.open.api.dto.apicenter.logistic.ECShipBillRefundCallbackRequestDTO;
import com.ecommerce.open.api.dto.apicenter.logistic.ShipBillRefundCallbackRequestDTO;
import com.ecommerce.open.dao.bean.ApiConfigBean;
import com.ecommerce.open.dao.vo.SysConfig;
import com.ecommerce.open.dao.vo.SysRoute;
import com.ecommerce.open.enums.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * ERP将运单退货结果反馈给电商,EC-PRI-C2
 * Created by hexinhui3 on 2021/5/31 20:05
 */
@Slf4j
@Service("cementECShipBillRefundCallbackTransfer")
public class CementECShipBillRefundCallbackTransfer extends BaseCrcementCallbackTransfer<ECShipBillRefundCallbackRequestDTO, BaseClientResponseDTO, ItemResult<ShipBillRefundCallbackRequestDTO>, String> {

    @Override
    protected void setNotNullClientParms(Map<String, String> clientParms, ECShipBillRefundCallbackRequestDTO ecShipBillRefundCallbackRequestDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        clientParms.put("外部运单号", ecShipBillRefundCallbackRequestDTO.getExternalBillId());
    }

    @Override
    public ItemResult<ShipBillRefundCallbackRequestDTO> createDefaultFailServerRequestData(JSONObject jsonmap, String serverBillNo) {
        ItemResult<ShipBillRefundCallbackRequestDTO> itemResult = new ItemResult<>();
        ShipBillRefundCallbackRequestDTO callbackDTO = new ShipBillRefundCallbackRequestDTO();
        callbackDTO.setEcBillCode(serverBillNo);
        itemResult.setData(callbackDTO);
        itemResult.setSuccess(false);

        return itemResult;
    }

    @Override
    protected String getServerBillNo(ECShipBillRefundCallbackRequestDTO ecShipBillRefundCallbackRequestDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        return ecShipBillRefundCallbackRequestDTO.getEcBillCode();
    }

    @Override
    protected ProcessResult getProcessResult(ECShipBillRefundCallbackRequestDTO ecShipBillRefundCallbackRequestDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        if (ecShipBillRefundCallbackRequestDTO != null && CsStringUtils.equals("01", ecShipBillRefundCallbackRequestDTO.getCode())) {
            return ProcessResult.SUCCESS;
        }
        return ProcessResult.FAIL;
    }


    @Override
    protected ItemResult<ShipBillRefundCallbackRequestDTO> fillServerRequestData(ECShipBillRefundCallbackRequestDTO ecShipBillRefundCallbackRequestDTO, JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        log.info("运单退货结果反馈fillServerRequestData : {}", JSON.toJSONString(ecShipBillRefundCallbackRequestDTO));
        ItemResult<ShipBillRefundCallbackRequestDTO> itemResult = new ItemResult<>();
        ShipBillRefundCallbackRequestDTO callbackDTO = new ShipBillRefundCallbackRequestDTO();
        if(ecShipBillRefundCallbackRequestDTO == null){
            itemResult.setSuccess(false);
            return itemResult;
        }

        if (CsStringUtils.equals("S1A00000", ecShipBillRefundCallbackRequestDTO.getCode()) ||
                CsStringUtils.equals("01", ecShipBillRefundCallbackRequestDTO.getCode())) {
            BeanUtils.copyProperties(ecShipBillRefundCallbackRequestDTO, callbackDTO);
            itemResult.setSuccess(true);
        } else {
            callbackDTO.setEcBillCode(ecShipBillRefundCallbackRequestDTO.getEcBillCode());
            callbackDTO.setExternalBillId(ecShipBillRefundCallbackRequestDTO.getEcBillCode() == null ? "" : ecShipBillRefundCallbackRequestDTO.getEcBillCode());
            itemResult.setDescription(ecShipBillRefundCallbackRequestDTO.getMessage());
            itemResult.setSuccess(false);
        }
        itemResult.setData(callbackDTO);

        return itemResult;
    }

    @Override
    protected String getClientBillNo(ECShipBillRefundCallbackRequestDTO ecShipBillRefundCallbackRequestDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        if (ecShipBillRefundCallbackRequestDTO == null) {
            return null;
        }
        return ecShipBillRefundCallbackRequestDTO.getExternalBillId();
    }

    @Override
    protected BaseClientResponseDTO fillClientResponseData(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        return new BaseClientResponseDTO();
    }
}
