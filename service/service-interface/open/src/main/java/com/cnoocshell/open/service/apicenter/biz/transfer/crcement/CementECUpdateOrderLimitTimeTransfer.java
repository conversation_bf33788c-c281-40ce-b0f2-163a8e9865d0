package com.ecommerce.open.service.apicenter.biz.transfer.crcement;

import java.util.Map;

import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.ecommerce.open.api.dto.apicenter.BaseClientResponseDTO;
import com.ecommerce.open.api.dto.apicenter.order.ECOrderLimitTimeDTO;
import com.ecommerce.open.dao.bean.ApiConfigBean;
import com.ecommerce.open.dao.vo.SysConfig;
import com.ecommerce.open.dao.vo.SysRoute;
import com.ecommerce.open.enums.ProcessResult;
@Service("cementECUpdateOrderLimitTimeTransfer")
public class CementECUpdateOrderLimitTimeTransfer
		extends BaseCrcementTransfer<ECOrderLimitTimeDTO, BaseClientResponseDTO, String, String> {

	@Override
	protected String fillServerRequestData(ECOrderLimitTimeDTO clientRequest, JSONObject jsonmap, SysRoute sysRoute,
			SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
		jsonmap.put("ERPDistributeCode", clientRequest.getERPDistributeCode());
		jsonmap.put("takeEndTime", formatCommDate(clientRequest.getTakeTimeLimit()));
		return jsonmap.toJSONString();
	}

	@Override
	protected String getClientBillNo(ECOrderLimitTimeDTO clientRequest, SysRoute sysRoute, SysConfig SysConfig,
			ApiConfigBean ApiConfigBean) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	protected String getServerBillNo(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig,
			ApiConfigBean ApiConfigBean) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	protected ProcessResult getProcessResult(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig,
			ApiConfigBean ApiConfigBean) {
		if ("01".equals(jsonmap.getString("code"))) {
			return ProcessResult.PROCESSING;
		}
		return ProcessResult.FAIL;
	}

	@Override
	protected BaseClientResponseDTO fillClientResponseData(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig,
			ApiConfigBean ApiConfigBean) {
		// TODO Auto-generated method stub
		return new BaseClientResponseDTO();
	}

	@Override
	protected void setNotNullClientParms(Map<String, String> clientParms, ECOrderLimitTimeDTO clientRequest,
			SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
		clientParms.put("ERP提货计划编号", clientRequest.getERPDistributeCode());
		clientParms.put("提货有效期", formatCommDate(clientRequest.getTakeTimeLimit()));
	}

}
