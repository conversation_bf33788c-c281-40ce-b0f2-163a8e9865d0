package com.ecommerce.open.service.apicenter.biz.transfer.crcement;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.open.api.dto.pom.logistics.ERPWaybillDTO;
import com.ecommerce.open.api.dto.apicenter.BaseClientResponseDTO;
import com.ecommerce.open.api.dto.apicenter.cement.logistics.ECSendOutWaybillCBRequestDTO;
import com.ecommerce.open.dao.bean.ApiConfigBean;
import com.ecommerce.open.dao.vo.SysConfig;
import com.ecommerce.open.dao.vo.SysRoute;
import com.ecommerce.open.enums.ProcessResult;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * @Auther: colu
 * @Date: 2019-07-19 11:08
 * @Description: CementECWaybillSendOutCallbackTransfer
 */
@Service("cementECWaybillSendOutCallbackTransfer")
public class CementECWaybillSendOutCallbackTransfer extends BaseCrcementCallbackTransfer<ECSendOutWaybillCBRequestDTO, BaseClientResponseDTO, ItemResult<ERPWaybillDTO>, String> {

    @Override
    public ItemResult<ERPWaybillDTO> createDefaultFailServerRequestData(JSONObject jsonmap, String serverBillNo) {
        ERPWaybillDTO erpWaybillDTO = new ERPWaybillDTO();
        erpWaybillDTO.setWaybillNum(serverBillNo);
        ItemResult<ERPWaybillDTO> itemResult = new ItemResult<>();
        itemResult.setSuccess(false);
        itemResult.setData(erpWaybillDTO);
        return itemResult;
    }

    @Override
    protected String getServerBillNo(ECSendOutWaybillCBRequestDTO ecSendOutWaybillCBRequestDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        return ecSendOutWaybillCBRequestDTO.getWaybillNum();
    }

    @Override
    protected ProcessResult getProcessResult(ECSendOutWaybillCBRequestDTO ecSendOutWaybillCBRequestDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        String code = ecSendOutWaybillCBRequestDTO.getCode();
        if (CsStringUtils.equals(code, "S1A00000")) {
            return ProcessResult.SUCCESS;
        }
        return ProcessResult.FAIL;
    }

    @Override
    protected ItemResult<ERPWaybillDTO> fillServerRequestData(ECSendOutWaybillCBRequestDTO ecSendOutWaybillCBRequestDTO, JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        ItemResult<ERPWaybillDTO> itemResult = new ItemResult<>();
        ERPWaybillDTO erpWaybillDTO = new ERPWaybillDTO();
        erpWaybillDTO.setWaybillId(ecSendOutWaybillCBRequestDTO.getWaybillId());
        erpWaybillDTO.setWaybillNum(ecSendOutWaybillCBRequestDTO.getWaybillNum());
        itemResult.setSuccess(CsStringUtils.equals(ecSendOutWaybillCBRequestDTO.getCode(), "S1A00000"));
        itemResult.setDescription(ecSendOutWaybillCBRequestDTO.getMessage());
        itemResult.setData(erpWaybillDTO);
        return itemResult;
    }

    @Override
    protected String getClientBillNo(ECSendOutWaybillCBRequestDTO ecSendOutWaybillCBRequestDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        if (ecSendOutWaybillCBRequestDTO == null) {
            return null;
        }
        return ecSendOutWaybillCBRequestDTO.getWaybillNum();
    }

    @Override
    protected BaseClientResponseDTO fillClientResponseData(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        return new BaseClientResponseDTO();
    }

    @Override
    protected void setNotNullClientParms(Map<String, String> clientParms, ECSendOutWaybillCBRequestDTO ecSendOutWaybillCBRequestDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        clientParms.put("运单号", ecSendOutWaybillCBRequestDTO.getWaybillNum());
        clientParms.put("运单ID", ecSendOutWaybillCBRequestDTO.getWaybillId());
        clientParms.put("code编码", ecSendOutWaybillCBRequestDTO.getCode());
    }
}
