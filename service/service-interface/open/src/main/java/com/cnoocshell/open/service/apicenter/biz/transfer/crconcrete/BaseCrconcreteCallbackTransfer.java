package com.ecommerce.open.service.apicenter.biz.transfer.crconcrete;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.open.api.dto.apicenter.BaseClientRequestDTO;
import com.ecommerce.open.api.dto.apicenter.BaseClientResponseDTO;
import com.ecommerce.open.api.exception.APICenterExceptionCode;
import com.ecommerce.open.dao.bean.ApiConfigBean;
import com.ecommerce.open.dao.vo.SysConfig;
import com.ecommerce.open.dao.vo.SysRoute;
import com.ecommerce.open.enums.ApiRequestTypeEnum;
import com.ecommerce.open.enums.ProcessResult;
import com.ecommerce.open.enums.RequestStatusEnum;
import com.ecommerce.open.service.apicenter.biz.IDataTransfer;
import com.ecommerce.open.service.apicenter.biz.transfer.crcement.BaseCrcementTransfer;
import com.ecommerce.open.service.apicenter.dto.RequestContext;
import com.ecommerce.open.service.apicenter.dto.RequestInfo;
import com.ecommerce.open.service.apicenter.dto.RequestMessage;
import com.ecommerce.open.service.apicenter.dto.ResponseInfo;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Slf4j
public abstract class BaseCrconcreteCallbackTransfer<clientRequest, clientResponse, serverRequest, serverResponse>
		extends BaseCrcementTransfer<clientRequest, clientResponse, serverRequest, serverResponse>
		implements IDataTransfer<clientRequest, clientResponse, serverRequest, serverResponse> {

	public abstract serverRequest createDefaultFailServerRequestData(JSONObject jsonmap, String serverBillNo);

	public serverRequest createDefaultSuccessServerRequestData(JSONObject jsonmap, RequestContext requestContext) {
		return null;
	}

	private final static Logger interfaceLogger = LoggerFactory.getLogger("AsyncInterfaceLog");
//	protected static final sun.misc.BASE64Encoder Encoder = new sun.misc.BASE64Encoder();
//	protected static final sun.misc.BASE64Decoder Decoder = new sun.misc.BASE64Decoder();

	public String transferURL(RequestContext requestContext) {
		ApiConfigBean ApiConfigBean = requestContext.getApiConfigBean();
		return ApiConfigBean.getUrl();
	}

	@Override
	public void transferToServerRequest(RequestContext requestContext) {
		log.info("API Flow 开始处理收到的回调请求，BaseCrcementCallbackTransfer start transferToServerRequest requestContext===>{}",
				requestContext);

		if (requestContext.getClientData() == null) {
			log.error("在处理回调时 BaseCrcementCallbackTransfer 接收到的回调数据为空");
		}
		if (!requestContext.isIfRequestFailCallBack()) {
			if (requestContext.getClientData() instanceof BaseClientRequestDTO) {
				BaseClientRequestDTO baseClientResponseDTO = (BaseClientRequestDTO) requestContext.getClientData();
				requestContext.setBaseRequestNo(baseClientResponseDTO.getBaseRequestNo());
				requestContext.setRequestNo(baseClientResponseDTO.getRequestNo());
				requestContext.setCode(CsStringUtils.isNullOrBlank(baseClientResponseDTO.getCode())
						? baseClientResponseDTO.getProcessCode()
						: baseClientResponseDTO.getCode());
				requestContext.setMsg(CsStringUtils.isNullOrBlank(baseClientResponseDTO.getMessage())
						? baseClientResponseDTO.getProcessMessage()
						: baseClientResponseDTO.getMessage());

			} else {
				log.error("在处理回调时 BaseCrcementCallbackTransfer 接收到的回调数据类型不正确，未继承至 BaseClientResponseDTO");
			}
		}

		JSONObject jsonmap = getBaseServerRequest(requestContext);
		jsonmap.put("baseRequestNo", requestContext.getBaseRequestNo());
		ApiConfigBean ApiConfigBean = requestContext.getApiConfigBean();
		SysRoute sysRoute = requestContext.getSysRoute();
		SysConfig sysConfig = requestContext.getSysConfig();
		serverRequest serverRequest = null;
		if (requestContext.isIfRequestFailCallBack()) {
			jsonmap.put("message", requestContext.getMsg());
			jsonmap.put("code", requestContext.getCode());
			jsonmap.put("xmessage", requestContext.getXmessage());
			serverRequest = this.createDefaultFailServerRequestData(jsonmap, requestContext.getClientBillNo());
			log.info("API Flow 构建默认失败回调，BaseCrcementCallbackTransfer serverRequest===>{}", serverRequest);
		} else if (requestContext.isIfNeedRedoCallback()) {
			jsonmap.put("message", requestContext.getMsg());
			jsonmap.put("code", requestContext.getCode());
			serverRequest = this.createDefaultSuccessServerRequestData(jsonmap, requestContext);
			log.info("API Flow 构建默认成功回调，BaseCrcementCallbackTransfer serverRequest===>{}", serverRequest);
		} else {
			serverRequest = this.fillServerRequestData((clientRequest) requestContext.getClientData(), jsonmap,
					sysRoute, sysConfig, ApiConfigBean);
			log.info("API Flow 构建发送给server的回调请求，BaseCrcementCallbackTransfer serverRequest===>{}", serverRequest);
			requestContext.setRequestInfo(this.getRequestInfo(requestContext));
			log.info("API Flow 构建发送给server的回调请求，BaseCrcementCallbackTransfer RequestInfo===>{}",
					requestContext.getRequestInfo());
		}
		RequestMessage requestMessage = new RequestMessage();
		requestMessage.setBody(serverRequest);
		requestMessage.setHeaderParms(this.getHeaderParms(requestContext));
		requestMessage.setUrl(this.transferURL(requestContext));
		requestContext.setRequestMessage(requestMessage);
		requestContext.setBody(String.valueOf(requestContext.getClientData()));
		log.info("API Flow 构建发送给server的回调requestMessage，BaseCrcementCallbackTransfer requestMessage===>{}",
				requestMessage);
		interfaceLogger.info("ERP callback send to EC===>{}", requestMessage);
		// String serverBillNo = this.getServerBillNo((clientRequest)
		// requestContext.getClientData(), sysRoute, sysConfig, ApiConfigBean);
		// String serverBillNo = this.getServerBillNo(requestContext.getClientData(),
		// sysRoute, sysConfig, ApiConfigBean);
		// responseInfo.setServerBillNo(serverBillNo);
		// log.info("L2 BaseCrcementCallbackTransfer transferToServerRequest
		// serverBillNo ===>{} ", serverBillNo);
		// requestContext.setResponseInfo(responseInfo);
		// requestContext.setServerBillNo(serverBillNo);

		// requestContext.setServerBillNo(serverBillNo);
		log.info("L2 BaseCrcementCallbackTransfer transferToServerRequest result :{} ", jsonmap);
		// return serverRequest;
	}

	protected JSONObject getBaseServerRequest(RequestContext requestContext) {
		ApiConfigBean ApiConfigBean = requestContext.getApiConfigBean();
		JSONObject jsonmap = new JSONObject(true);
		String requestNo = generateRequestNo();
		if (!ApiRequestTypeEnum.CALLBACK.getCode().equals(ApiConfigBean.getApiRequestType())) {
			requestContext.setRequestNo(requestNo);
		}
		jsonmap.put("custNo", getDataNotNull(ApiConfigBean.getParamMap().get("custNo"), "客户号"));
		jsonmap.put("requestNo", requestNo);
		jsonmap.put("eventCode", getDataNotNull(ApiConfigBean.getBizCode(), "消息号"));
		return jsonmap;
	}

	@Override
	public RequestInfo getRequestInfo(RequestContext requestContext) {
		log.info("L2 BaseCrcementTransfer getRequestInfo end requestContext===>{}", requestContext);
		RequestInfo result = new RequestInfo();
		result.setRequestNo(requestContext.getRequestNo());
		SysRoute sysRoute = requestContext.getSysRoute();
		SysConfig sysConfig = requestContext.getSysConfig();
		ApiConfigBean apiConfigBean = requestContext.getApiConfigBean();
		String clientBillNo = this.getClientBillNo((clientRequest) requestContext.getClientData(), sysRoute, sysConfig,
				apiConfigBean);
		String serverBillNo = this.getServerBillNo((clientRequest) requestContext.getClientData(), sysRoute, sysConfig,
				apiConfigBean);
		requestContext.setClientBillNo(clientBillNo);
		requestContext.setServerBillNo(serverBillNo);
		result.setClientBillNo(clientBillNo);
		result.setServerBillNo(serverBillNo);
		result.setOperator(requestContext.getOperator());
		result.setOperatorName(requestContext.getOperatorName());

		if (requestContext.getClientData() instanceof BaseClientRequestDTO) {
			BaseClientRequestDTO clientReqeust = (BaseClientRequestDTO) requestContext.getClientData();
			if ("E0B02D15".equals(clientReqeust.getCode()) || "E0B02D15".equals(clientReqeust.getProcessCode())) {
				result.setProcessResult(ProcessResult.FAIL);
			} else {
				result.setProcessResult(ProcessResult.SUCCESS);
			}
			if (!CsStringUtils.isNullOrBlank(clientReqeust.getProcessMessage())) {
				result.setMessage(clientReqeust.getProcessMessage());
			} else {
				result.setMessage(clientReqeust.getMessage());
			}
		} else {
			result.setProcessResult(this.getProcessResult((clientRequest) requestContext.getClientData(), sysRoute,
					sysConfig, apiConfigBean));
		}

		log.info("***ERP 回调处理结果：===>{}", result);

		if (requestContext.getClientData() instanceof BaseClientRequestDTO) {
			BaseClientRequestDTO clientReqeust = (BaseClientRequestDTO) requestContext.getClientData();
			result.setCode(clientReqeust.getCode());
			result.setMessage(clientReqeust.getMessage());
		}
		log.info("L2 BaseCrcementTransfer getRequestInfoDTO result :{} ", result);
		return result;
	}

	@Override
	public void transferToClientResponse(RequestContext requestContext) {
		log.info("L2 BaseCrcementCallbackTransfer transferToServerRequest start requestContext===>{} ", requestContext);
		SysRoute sysRoute = requestContext.getSysRoute();
		SysConfig SysConfig = requestContext.getSysConfig();
		ApiConfigBean ApiConfigBean = requestContext.getApiConfigBean();

		// 处理responseinfo
		ResponseInfo responseInfo = new ResponseInfo();
		String serverBillNo = null;

		// 先判断request发送情况，生成业务处理情况
		if (RequestStatusEnum.SEND_TIMEOUT.equals(requestContext.getResponseMessage().getRequestStatus())) {
			log.error(
					"L2 BaseCrcementCallbackTransfer transferToClientResponse SEND_TIMEOUT do not check response data");
			responseInfo.setCode(requestContext.getCode());
			responseInfo.setMessage(requestContext.getMsg());
			responseInfo.setProcessResult(ProcessResult.NEEDRESEND);
			requestContext.setResponseInfo(responseInfo);
			log.info("API Flow 请求发送完成，生成业务处理情况。transferToServerRequest 发请超时，构建responseInfo===>{}", responseInfo);
			return;
		}

		if (RequestStatusEnum.SEND_FAIL.equals(requestContext.getResponseMessage().getRequestStatus())) {
			log.error("L2 BaseCrcementCallbackTransfer transferToClientResponse SEND_FAIL do not check response data");
			responseInfo.setCode(requestContext.getCode());
			responseInfo.setMessage(requestContext.getMsg());
			requestContext.setResponseInfo(responseInfo);
			responseInfo.setProcessResult(ProcessResult.FAIL);
			log.info("API Flow 请求发送完成，生成业务处理情况。transferToServerRequest 发请失败，构建responseInfo===>{}", responseInfo);
			return;
		}

		// 处理responseData
		JSONObject responseJson = null;
		try {
			responseJson = JSON
					.parseObject(getDataNotNull(String.valueOf(requestContext.getResponseMessage().getBody()),
							APICenterExceptionCode.SERVER_RESPONSE_NULL));
			requestContext.setResponseBody(String.valueOf(requestContext.getResponseMessage().getBody()));
		} catch (Exception e) {
			e.printStackTrace();
			log.error("L2 BaseCrcementCallbackTransfer transferToClientResponse 解析Callback报文错误");
			this.createResponseInfoWhenResponseParseFail(responseInfo, requestContext);
			return;
		}
		JSONObject responseData = responseJson;
		log.info("L2 BaseCrcementCallbackTransfer *** server respose JSON===>{}", responseJson);
		interfaceLogger.info("ERP callback EC response send to ERP===>{}", responseJson);
		// responseInfo.setProcessResult(this.getProcessResult(responseJson, sysRoute,
		// SysConfig, ApiConfigBean));
		responseInfo.setCode(responseJson.getString("code"));
		responseInfo.setMessage(responseJson.getString("message"));
		if ("true".equals(responseJson.getString("success"))) {
			responseInfo.setProcessResult(ProcessResult.SUCCESS);
		} else {
			responseInfo.setProcessResult(ProcessResult.CALLBACKPROCESSFAIL);
		}

		responseInfo.setRequestNo(requestContext.getRequestNo());
		// serverBillNo = this.getServerBillNo(responseJson, sysRoute, SysConfig,
		// ApiConfigBean);
		// responseInfo.setServerBillNo(serverBillNo);
		log.info("L2 BaseCrcementCallbackTransfer getProcessResult 的处理结果 ===>{} ", responseInfo);
		log.info("API Flow 请求发送完成，解析response完成，构建responseInfo完成，BaseCrcementCallbackTransfer responseInfo===>{}",
				responseInfo);
		requestContext.setResponseInfo(responseInfo);
		// requestContext.setServerBillNo(serverBillNo);

		// 处理reponseData

		clientResponse clientResponse = this.fillClientResponseData(responseData, sysRoute, SysConfig, ApiConfigBean);
		BaseClientResponseDTO clientResult = (BaseClientResponseDTO) clientResponse;
		if (clientResult == null) {
			clientResult = new BaseClientResponseDTO();
		}

		// if(ApiRequestTypeEnum.CALLBACK.getCode().equals(ApiConfigBean.getApiRequestType()))
		// {
		//
		// clientResult.setCode("01");
		// clientResult.setRequestNo(requestContext.getRequestNo());
		// }

		if (CsStringUtils.isNullOrBlank(responseData.getString("code"))) {
			if ("true".equals(responseJson.getString("success"))) {
				if (CsStringUtils.isNullOrBlank(clientResult.getMessage())) {
					if (CsStringUtils.isNullOrBlank(responseData.getString("message"))) {
						clientResult.setMessage("电商接受到回调请求并处理成功");
					} else {
						clientResult.setMessage(responseData.getString("message"));
					}
				}

				if (ApiRequestTypeEnum.CALLBACK.getCode()
						.equals(requestContext.getApiConfigBean().getApiRequestType())) {
					clientResult.setCode("S1A00000");
				} else {
					clientResult.setCode("01");
				}
			} else {
				if (CsStringUtils.isNullOrBlank(clientResult.getMessage())) {
					if (CsStringUtils.isNullOrBlank(responseData.getString("message"))) {
						clientResult.setMessage("电商接受到回调请求但处理失败");
					} else {
						clientResult.setMessage(responseData.getString("message"));
					}
				}
				if (ApiRequestTypeEnum.CALLBACK.getCode()
						.equals(requestContext.getApiConfigBean().getApiRequestType())) {
					clientResult.setCode("E0B02D15");
				} else {
					clientResult.setCode("00");
				}
			}
		} else {
			clientResult.setCode(responseData.getString("code"));
			if (CsStringUtils.isNullOrBlank(clientResult.getMessage())) {

				String message = responseData.getString("message");

				if (CsStringUtils.isNullOrBlank(message)) {
					message = responseData.getString("description");
				}
				if (CsStringUtils.isNullOrBlank(message)) {
					message = "电商接受到回调请求";
				}
				clientResult.setMessage(message);
			}
		}

		clientResult.setRequestNo(requestContext.getRequestNo());
		// clientResult.setMessage(responseData.getString("message"));
		log.info("API Flow 请求发送完成，解析response完成，构建clientResult完成，BaseCrcementCallbackTransfer clientResult===>{}",
				clientResult);
		requestContext.setClientResponseDTO(clientResponse);
		requestContext.setCode(clientResult.getCode());
		requestContext.setMsg(clientResult.getMessage());
		log.info("L2 BaseCrcementCallbackTransfer end transferToClientResponse result :{} ", clientResponse);
		// return clientResponse;
	}

	protected ProcessResult getProcessResult(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig,
			ApiConfigBean ApiConfigBean) {
		return null;
	};

	/**
	 * @Title: getClientBillNo
	 * @Description: 获取客户端单据号
	 * <AUTHOR>
	 * @param clientRequest
	 * @param jsonmap
	 * @param sysRoute
	 * @param SysConfig
	 * @param ApiConfigBean
	 * @return
	 */
	protected String getServerBillNo(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig,
			ApiConfigBean ApiConfigBean) {
		return null;
	};

	/**
	 * @Title: getClientBillNo
	 * @Description: 获取客户端单据号
	 * <AUTHOR>
	 * @param clientRequest
	 * @param jsonmap
	 * @param sysRoute
	 * @param SysConfig
	 * @param ApiConfigBean
	 * @return
	 */
	protected abstract String getServerBillNo(clientRequest clientRequest, SysRoute sysRoute, SysConfig SysConfig,
			ApiConfigBean ApiConfigBean);

	/**
	 * @Title: getProcessResult
	 * @Description: 获取处理结果
	 * <AUTHOR>
	 * @param jsonmap
	 * @param sysRoute
	 * @param SysConfig
	 * @param ApiConfigBean
	 * @return
	 */
	protected abstract ProcessResult getProcessResult(clientRequest clientRequest, SysRoute sysRoute,
			SysConfig SysConfig, ApiConfigBean ApiConfigBean);

	public ItemResult<Boolean> validateClientRequest(RequestContext requestContext) {
		ItemResult<Boolean> result = new ItemResult<Boolean>(Boolean.TRUE);
		result.setSuccess(true);
		return result;
	}

}
