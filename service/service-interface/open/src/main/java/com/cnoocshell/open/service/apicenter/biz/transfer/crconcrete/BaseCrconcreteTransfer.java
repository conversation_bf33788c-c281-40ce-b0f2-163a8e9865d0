package com.ecommerce.open.service.apicenter.biz.transfer.crconcrete;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.NameFilter;
import com.alibaba.fastjson.serializer.ValueFilter;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.open.api.dto.apicenter.BaseClientResponseDTO;
import com.ecommerce.open.api.exception.APICenterExceptionCode;
import com.ecommerce.open.dao.bean.ApiConfigBean;
import com.ecommerce.open.dao.vo.SysConfig;
import com.ecommerce.open.dao.vo.SysRoute;
import com.ecommerce.open.enums.ApiDirectEnum;
import com.ecommerce.open.enums.ApiRequestTypeEnum;
import com.ecommerce.open.enums.ProcessResult;
import com.ecommerce.open.enums.RequestStatusEnum;
import com.ecommerce.open.service.apicenter.biz.IDataTransfer;
import com.ecommerce.open.service.apicenter.biz.transfer.BaseDataTransfer;
import com.ecommerce.open.service.apicenter.dto.RequestContext;
import com.ecommerce.open.service.apicenter.dto.RequestInfo;
import com.ecommerce.open.service.apicenter.dto.RequestMessage;
import com.ecommerce.open.service.apicenter.dto.ResponseInfo;
import lombok.extern.slf4j.Slf4j;
import com.ecommerce.common.utils.CsStringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
public abstract class BaseCrconcreteTransfer<clientRequest, clientResponse, serverRequest, serverResponse>
		extends BaseDataTransfer<clientRequest, clientResponse, serverRequest, serverResponse>
		implements IDataTransfer<clientRequest, clientResponse, serverRequest, serverResponse> {
	private final static Logger interfaceLogger = LoggerFactory.getLogger("AsyncInterfaceLog");
	protected final static ValueFilter valueFilter = new ValueFilter() {
		@Override
		public Object process(Object object, String name, Object value) {
			// System.out.println(name);
			if (value instanceof BigDecimal) {
				return ((BigDecimal) value).stripTrailingZeros().toString();
			}
			if ("ecOperatorName".equals(name)) {
				if (value != null && String.valueOf(value).length() > 30) {
					return String.valueOf(value).substring(0, 50);
				}
			}
			// if ("planBillWeight".equals(name)) {
			// if(value != null) {
			// return new BigDecimal(String.valueOf(value)).toPlainString();
			// }
			// }
			return value;
		}
	};

	protected final static NameFilter nameFilter = new NameFilter() {
		@Override
		public String process(Object object, String name, Object value) {
			if ("eRPDistributeCode".equals(name)) {
				return "ERPDistributeCode";
			}
			return name;
		}
	};
	protected static final SimpleDateFormat Time_Stamp_Format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss:SSS");
	protected static final Base64.Encoder Encoder = Base64.getEncoder();
	protected static final Base64.Decoder Decoder = Base64.getDecoder();
	protected static final String RETURN_CODE = "RETURN_CODE";
	protected static final String RETURN_DESC = "RETURN_DESC";
	protected static final String RETURN_CODE_FAIL = "E0B02D15";
	protected static final String RETURN_CODE_SUCCESS = "S1A00000";
	protected static final String RETURN_CODE_PROCESS = "E0B00007";
	protected static final String RETURN_CODE_KEY = "X_RETURN_CODE";

	protected static final Random r = new Random();
	/**
	 * 请求前缀
	 */
	protected static final String REQUEST_NO_PREX = "CRCERP";

	protected static final SimpleDateFormat REQUEST_NO_FORMART = new SimpleDateFormat("yyyyMMddHHmmss");

	/**
	 * erp response报文固定解析字段
	 */
	private static final String Output_Parameters_Key = "OutputParameters";

	/**
	 * erp response报文固定解析字段
	 */
	private static final String X_RESPONSE_DATA = "X_RESPONSE_DATA";
	private static final String X_RETURN_MESG = "X_RETURN_MESG";

	@Override
	public String transferURL(RequestContext requestContext) {
		ApiConfigBean ApiConfigBean = requestContext.getApiConfigBean();
		String url = ApiConfigBean.getUrl();
		if (!ApiConfigBean.getServerSign()) {
			return url;
		}
		log.info("L2 BaseCrcementTransfer start transferURL url {}, ApiConfigBean {}", url, ApiConfigBean);

		if (CsStringUtils.isEmpty(url)) {
			log.error(" xxx-ERP接口未配置URL ");
			throw new BizException(APICenterExceptionCode.PARAM_NULL, "URL 未配置");
		}

		String urlResult = "";
		String Api_ID = ApiConfigBean.getParamMap().get("Api_ID");

		String Api_Version = ApiConfigBean.getParamMap().get("Api_Version");

		String App_Sub_ID = ApiConfigBean.getParamMap().get("App_Sub_ID");

		String App_Token = ApiConfigBean.getParamMap().get("App_Token");

		String Time_Stamp = Time_Stamp_Format.format(new Date());

		String Partner_ID = ApiConfigBean.getParamMap().get("Partner_ID");

		String Sys_ID = ApiConfigBean.getParamMap().get("Sys_ID");

		String User_Token = ApiConfigBean.getParamMap().get("User_Token");

		if (CsStringUtils.isEmpty(Api_ID) || CsStringUtils.isEmpty(Api_Version) || CsStringUtils.isEmpty(App_Sub_ID)
				|| CsStringUtils.isEmpty(App_Token)
		// || CsStringUtils.isEmpty(User_Token)
		) {
			log.error(
					" xxx-ERP接口参数未配置全  Api_ID : {} ,  Api_Version : {} ,   App_Sub_ID : {} ,   App_Token : {} ,  User_Token : {}  ",
					Api_ID, Api_Version, App_Sub_ID, App_Token, User_Token);
			throw new BizException(APICenterExceptionCode.PARAM_NULL, "参数不全");
		}

		urlResult = "Api_ID=" + Api_ID + "&Api_Version=" + Api_Version + "&App_Sub_ID=" + App_Sub_ID + "&App_Token="
				+ App_Token + "&Partner_ID=" + Partner_ID + "&Sign=" + "&Sys_ID=" + Sys_ID + "&Time_Stamp=" + Time_Stamp
				+ "&User_Token=";

		log.info("L2 BaseCrcementTransfer transferURL before encode url====>{}", urlResult);
		String encodeResult = Encoder.encodeToString(urlResult.getBytes()).replaceAll("[\\s*\t\n\r]", "");
		urlResult = url + encodeResult;

		// // 要配置
		// String url = "http://ssdpuat.crc.com.cn/ssdp/soa/rf?ssdp=";
		// // 要配置
		// String sysId = "80000004";
		//
		// String restUrlSign =
		// "Api_ID=crcem.EC.app.crcementERP&Api_Version=dev&App_Sub_ID=8000000401NH&App_Token=16104e6ca5b645479bfe3fd4455bd444&Partner_ID=80000000&Sign=&Sys_ID=80000004&Time_Stamp=testtime&User_Token=";

		log.info("L2 BaseCrcementTransfer end transferURL result :{}", urlResult);
		return urlResult;
	}

	@Override
	public Map<String, String> getHeaderParms(RequestContext requestContext) {
		Map<String, String> head = new HashMap<>();
		// head.put("P_IFACE_CODE", requestContext.getBizCode());
		// head.put("P_BATCH_NUMBER", requestContext.getRequestNo());
		String username = requestContext.getApiConfigBean().getParamMap().get("username");
		String password = requestContext.getApiConfigBean().getParamMap().get("password");
		head.put("authorization", "Basic " + Encoder.encodeToString((username + ":" + password).getBytes()));
		head.put("content-type", "application/json;charset=UTF-8");
		head.put("soapaction", "PROCESS_SN_REQUEST");
		return head;
	}

	// /**
	// * @Title: baseConfigValidate
	// * @Description: 基本验证，对crcment的公共参数进行验证
	// * <AUTHOR>
	// * @param sysRoute
	// * @param SysConfig
	// * @param ApiConfigBean
	// */
	// protected void baseBizValidate(SysRoute sysRoute, SysConfig SysConfig,
	// ApiConfigBean ApiConfigBean) {
	// if (ApiConfigBean == null) {
	// log.error("L2 BaseCrcementTransfer 参数异常 ApiConfigBean 为空");
	// throw new BizException(APICenterExceptionCode.PARAM_NULL, "API未配置");
	// }
	//
	// // 验证server seller id是否有配置
	//
	// }

	/**
	 * @Title: generateRequestNo
	 * @Description: 生成请求号，格式为：前缀 + 秒数 + 随机6位
	 * <AUTHOR>
	 * @return
	 */
	protected String generateRequestNo() {
		double pross = (1 + r.nextDouble()) * Math.pow(10, 6);
		return REQUEST_NO_PREX + REQUEST_NO_FORMART.format(new Date()) + String.valueOf(pross).substring(1, 6 + 1);
	}

	/**
	 * @Title: getBaseServerRequest
	 * @Description: 获取基础server请求对象
	 * <AUTHOR>
	 * @param sysRoute
	 * @param SysConfig
	 * @param ApiConfigBean
	 * @return
	 */
	protected JSONObject getBaseServerRequest(RequestContext requestContext) {
		ApiConfigBean ApiConfigBean = requestContext.getApiConfigBean();
		SysRoute sysRoute = requestContext.getSysRoute();
		JSONObject jsonmap = new JSONObject(true);
		String requestNo = generateRequestNo();
		if (!ApiRequestTypeEnum.CALLBACK.getCode().equals(ApiConfigBean.getApiRequestType())) {
			requestContext.setRequestNo(requestNo);
		}
		jsonmap.put("custNo", getDataNotNull(ApiConfigBean.getParamMap().get("custNo"), "客户号"));
		jsonmap.put("requestNo", requestNo);
		String bizCode = getDataNotNull(ApiConfigBean.getBizCode(), "消息号");
		String bizCodeConfig = ApiConfigBean.getParamMap().get("bizCode");
		if (CsStringUtils.isNullOrBlank(bizCodeConfig)) {
			bizCodeConfig = bizCode;
		}
		jsonmap.put("eventCode", bizCodeConfig);
		requestContext.setOutBizCode(bizCodeConfig);
		return jsonmap;
	}

	@Override
	public void transferToServerRequest(RequestContext requestContext) {
		log.info("L2 BaseCrcementTransfer start transferToServerRequest result===>{} ", requestContext);

		// 处理发送给服务端的报文，如果是回调则需要解析业务处理结果
		JSONObject jsonmap = getBaseServerRequest(requestContext);
		ApiConfigBean ApiConfigBean = requestContext.getApiConfigBean();
		SysRoute sysRoute = requestContext.getSysRoute();
		SysConfig sysConfig = requestContext.getSysConfig();

		if (CsStringUtils.isNullOrBlank(jsonmap.getString("externalSellerId"))) {
			jsonmap.put("externalSellerId", getDataNotNull(sysRoute.getSendSellerId(), "外部系统卖家编号"));

		}
		// 处理requestMessage
		serverRequest serverRequest = this.fillServerRequestData((clientRequest) requestContext.getClientData(),
				jsonmap, sysRoute, sysConfig, ApiConfigBean);
		RequestMessage requestMessage = new RequestMessage();
		requestMessage.setBody(processERPRequestBody(requestContext, serverRequest));
		requestMessage.setHeaderParms(this.getHeaderParms(requestContext));
		requestMessage.setUrl(this.transferURL(requestContext));
		requestContext.setRequestMessage(requestMessage);
		log.info("API Flow create RequestMessage BaseCrcementTransfer===>{}", requestMessage);
		// 处理requestInfo
		requestContext.setRequestInfo(this.getRequestInfo(requestContext));
		log.info("API Flow create RequestInfo BaseCrcementTransfer===>{}", requestContext.getRequestInfo());
		// requestContext.setServerBillNo(serverBillNo);
		log.info("L2 BaseCrcementTransfer end transferToServerRequest result===>{} ", jsonmap);
		// return serverRequest;
	}

	private String processERPRequestBody(RequestContext requestContext, serverRequest serverRequest) {
		String result = "";
		String baseBody = String.valueOf(serverRequest);
		if (!(serverRequest instanceof String)) {
			baseBody = JSON.toJSONString(serverRequest);
		}

		ApiConfigBean ApiConfigBean = requestContext.getApiConfigBean();
		if (!ApiConfigBean.getServerEncrypt()) {
			return baseBody;
		}
		requestContext.setBody(baseBody);
		baseBody = "{\"request\":" + baseBody + "}";
		log.info("*** 加密前报文内容===>{}", baseBody);
		interfaceLogger.info("request send to ERP before encode ===>{}", baseBody);
		String baseBodyEncode = Encoder.encodeToString(baseBody.getBytes()).replaceAll("[\\s*\t\n\r]", "");
		JSONObject resultJSON = new JSONObject(true);
		JSONObject PROCESS_SN_REQUEST = new JSONObject(true);
		JSONObject RESTHeader = new JSONObject(true);
		JSONObject inputParameters = new JSONObject(true);
		inputParameters.put("P_BATCH_NUMBER", requestContext.getRequestNo());
		inputParameters.put("P_IFACE_CODE", requestContext.getOutBizCode());
		inputParameters.put("P_REQUEST_DATA", baseBodyEncode);
		PROCESS_SN_REQUEST.put("@xmlns", "http://xmlns.oracle.com/apps/cux/soaprovider/plsql/rest/EC_SN_processws/");

		RESTHeader.put("xmlns", "http://xmlns.oracle.com/apps/fnd/rest/header");
		RESTHeader.put("NLSLanguage", "SIMPLIFIED CHINESE");
		RESTHeader.put("RespApplication", "CUX");
		RESTHeader.put("Responsibility", "CRC_B09_SOA");
		RESTHeader.put("SecurityGroup", "STANDARD");
		PROCESS_SN_REQUEST.put("RESTHeader", RESTHeader);
		PROCESS_SN_REQUEST.put("InputParameters", inputParameters);
		resultJSON.put("PROCESS_SN_REQUEST", PROCESS_SN_REQUEST);
		result = resultJSON.toJSONString();
		log.info("API Flow BaseCrcementTransfer final request===>{} ", result);
		return result;
	}

	@Override
	public void transferToClientResponse(RequestContext requestContext) {
		log.info("L2 BaseCrcementTransfer transferToServerRequest start requestContext===>{} ", requestContext);
		SysRoute sysRoute = requestContext.getSysRoute();
		SysConfig SysConfig = requestContext.getSysConfig();
		ApiConfigBean ApiConfigBean = requestContext.getApiConfigBean();

		// 处理responseinfo
		ResponseInfo responseInfo = new ResponseInfo();
		String serverBillNo = null;

		// 先判断request发送情况，生成业务处理情况
		if (RequestStatusEnum.SEND_TIMEOUT.equals(requestContext.getResponseMessage().getRequestStatus())) {
			log.error("L2 BaseCrcementTransfer transferToClientResponse SEND_TIMEOUT do not check response data");
			responseInfo.setCode(requestContext.getCode());
			responseInfo.setMessage(requestContext.getMsg());
			responseInfo.setProcessResult(ProcessResult.NEEDRESEND);
			requestContext.setResponseInfo(responseInfo);
			log.info("API Flow 请求发送完成，生成业务处理情况。transferToServerRequest 发请超时，构建responseInfo===>{}", responseInfo);
			return;
		}

		if (RequestStatusEnum.SEND_FAIL.equals(requestContext.getResponseMessage().getRequestStatus())) {
			log.error("L2 BaseCrcementTransfer transferToClientResponse SEND_FAIL do not check response data");
			responseInfo.setCode(requestContext.getCode());
			responseInfo.setMessage(requestContext.getMsg());
			requestContext.setResponseInfo(responseInfo);
			responseInfo.setProcessResult(ProcessResult.FAIL);
			log.info("API Flow 请求发送完成，生成业务处理情况。transferToServerRequest 发请失败，构建responseInfo===>{}", responseInfo);
			return;
		}

		// 处理responseData
		JSONObject responseJson = null;
		try {
			responseJson = JSON
					.parseObject(getDataNotNull(String.valueOf(requestContext.getResponseMessage().getBody()),
							APICenterExceptionCode.SERVER_RESPONSE_NULL));
		} catch (Exception e) {
			e.printStackTrace();
			log.error("L2 BaseCrcementTransfer transferToClientResponse 解析ERP报文错误");
			this.createResponseInfoWhenResponseParseFail(responseInfo, requestContext);
			return;
		}
		JSONObject responseData = responseJson;
		log.info("L2 BaseCrcementTransfer *** server respose JSON===>{}", responseJson);

		if (ApiDirectEnum.EC_TO_ERP.getCode().equals(ApiConfigBean.getApiDirect())) {
			log.info("API Flow 请求发送完成，由电商发送给ERP的请求，开始解析response，BaseCrcementTransfer requestContext===>{}",
					requestContext);
			log.info("L2 BaseCrcementTransfer EC_TO_ERP check the ERP response");
			if (responseJson == null) {
				log.error("L2 BaseCrcementTransfer 无法获取server response responseJson is null===>{}", responseJson);
				this.createResponseInfoWhenResponseParseFail(responseInfo, requestContext);
				return;
			}
			JSONObject outputParameters = responseJson.getJSONObject(Output_Parameters_Key);
			if (outputParameters == null) {
				log.error("L2 BaseCrcementTransfer 无法获取server response outputParameters===>{}", responseJson);
				this.createResponseInfoWhenResponseParseFail(responseInfo, requestContext);
				return;
			}

			String data = outputParameters.getString(X_RESPONSE_DATA);
			String xmessage = outputParameters.getString(X_RETURN_MESG);
			requestContext.setXmessage(xmessage);
			if (data == null) {
				log.error("L2 BaseCrcementTransfer 无法获取server response responseDataA===>{}", responseJson);
				this.createResponseInfoWhenResponseParseFail(responseInfo, requestContext);
				return;
			}

			try {
				String dataDecode = new String(Decoder.decode(data));
				requestContext.setResponseBody(dataDecode);
				log.info("L2 BaseCrcementTransfer dataDecode===>{}", dataDecode);
				responseData = JSON.parseObject(dataDecode);

			} catch (Exception e) {
				e.printStackTrace();
				log.error("L2 BaseCrcementTransfer 无法获取server response responseDataB===>{}", responseJson);
				this.createResponseInfoWhenResponseParseFail(responseInfo, requestContext);
				return;
			}

			// responseData = outputParameters.getJSONObject(X_RESPONSE_DATA);
			if (responseData == null) {
				log.error("L2 BaseCrcementTransfer 无法获取server response responseDataC===>{}", responseJson);
				this.createResponseInfoWhenResponseParseFail(responseInfo, requestContext);
				return;
			}
			responseData = responseData.getJSONObject("response");
			if (responseData == null) {
				log.error("L2 BaseCrcementTransfer 无法获取server response responseDataD===>{}", responseJson);
				this.createResponseInfoWhenResponseParseFail(responseInfo, requestContext);
				return;
			}

			log.info("API Flow ***响应报文===>{}", responseData);
			interfaceLogger.info("response received from ERP after decode ===>{}", responseData);
			if (RETURN_CODE_SUCCESS.equals(outputParameters.getString(RETURN_CODE_KEY))
					|| RETURN_CODE_PROCESS.equals(outputParameters.getString(RETURN_CODE_KEY))) {
				if (ApiRequestTypeEnum.QUERY.getCode().equals(ApiConfigBean.getApiRequestType())
						|| (ApiRequestTypeEnum.REQUEST.getCode().equals(ApiConfigBean.getApiRequestType())
								&& (Boolean.TRUE.equals(ApiConfigBean.getClientSync())
										&& Boolean.TRUE.equals(ApiConfigBean.getServerSync())))) {
					responseInfo.setProcessResult(ProcessResult.SUCCESS);
					responseInfo.setCode("01");
					responseInfo.setMessage("ERP处理成功");
				} else {
					responseInfo.setProcessResult(ProcessResult.PROCESSING);
					responseInfo.setCode("01");
					responseInfo.setMessage("ERP处理中");
				}
			} else {
				responseInfo.setProcessResult(ProcessResult.FAIL);
				responseInfo.setCode("00");
				responseInfo.setMessage(CsStringUtils.isEmpty(responseData.getString("message")) ? "ERP处理失败" : responseData.getString("message"));
			}
			log.info("API Flow 请求发送完成，解析报文完成，BaseCrcementTransfer根据SSDP请求头判断业务处理结果===>{}", responseData);
			serverBillNo = this.getServerBillNo(responseData, sysRoute, SysConfig, ApiConfigBean);
			responseInfo.setServerBillNo(serverBillNo);
			log.info("API Flow 请求发送完成，解析报文获取serverBillNo，BaseCrcementTransfer serverBillNo===>{}", serverBillNo);
			log.info("L2 BaseCrcementTransfer getProcessResult 的处理结果 ===>{} ", responseInfo);

		} else {
			log.info("API Flow 请求发送完成，由ERP发送给电商的请求，开始解析response，BaseCrcementTransfer requestContext===>{}",
					requestContext);
			responseInfo.setProcessResult(this.getProcessResult(responseJson, sysRoute, SysConfig, ApiConfigBean));
			responseInfo.setCode(responseJson.getString("code"));
			responseInfo.setMessage(responseJson.getString("message"));
			if (CsStringUtils.isNullOrBlank(responseInfo.getMessage())) {
				responseInfo.setMessage(responseJson.getString("description"));
			}

			responseInfo.setRequestNo(requestContext.getRequestNo());
			serverBillNo = this.getServerBillNo(responseJson, sysRoute, SysConfig, ApiConfigBean);
			responseInfo.setServerBillNo(serverBillNo);
			log.info("API Flow 请求发送完成，解析报文完成，BaseCrcementTransfer根据SSDP请求头判断业务处理结果===>{}", responseData);
			log.info("API Flow 请求发送完成，解析报文获取serverBillNo，BaseCrcementTransfer serverBillNo===>{}", serverBillNo);
			log.info("L2 BaseCrcementTransfer getProcessResult 的处理结果 ===>{} ", responseInfo);
		}

		requestContext.setResponseInfo(responseInfo);
		requestContext.setServerBillNo(serverBillNo);

		// 处理reponseData
		log.info("API Flow 请求发送完成，解析response完成，开始构建clientResponse，BaseCrcementTransfer requestContext===>{}",
				requestContext);
		requestContext.getResponseMessage().setResponseJSON(responseData);
		clientResponse clientResponse = this.fillClientResponseData(responseData, sysRoute, SysConfig, ApiConfigBean);

		if (clientResponse instanceof BaseClientResponseDTO) {
			BaseClientResponseDTO clientResult = (BaseClientResponseDTO) clientResponse;
			if (CsStringUtils.isNullOrBlank(clientResult.getCode())) {
				clientResult.setCode(responseData.getString("code"));
			}
			if (CsStringUtils.isNullOrBlank(clientResult.getMessage())) {
				clientResult.setMessage(responseData.getString("message"));
			}
			clientResult.setRequestNo(requestContext.getRequestNo());

			requestContext.setIfNeedRedoCallback(clientResult.isIfNeedRedoCallback());
		}

		log.info("API Flow 请求发送完成，解析response完成，构建clientResponse完成，BaseCrcementTransfer clientResponse===>{}",
				clientResponse);

		requestContext.setClientResponseDTO(clientResponse);
		requestContext.setCode(responseInfo.getCode());
		requestContext.setMsg(responseInfo.getMessage());
		log.info("L2 BaseCrcementTransfer end transferToClientResponse result :{} ", clientResponse);
		// return clientResponse;
	}

	/**
	 * @Title: fillServerRequestData
	 * @Description: 填充发送给server端的请求内容
	 * <AUTHOR>
	 * @param clientRequest
	 * @param jsonmap
	 * @param sysRoute
	 * @param SysConfig
	 * @param ApiConfigBean
	 * @return
	 */
	protected abstract serverRequest fillServerRequestData(clientRequest clientRequest, JSONObject jsonmap,
			SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean);

	/**
	 * @Title: getClientBillNo
	 * @Description: 获取客户端单据号
	 * <AUTHOR>
	 * @param clientRequest
	 * @param jsonmap
	 * @param sysRoute
	 * @param SysConfig
	 * @param ApiConfigBean
	 * @return
	 */
	protected abstract String getClientBillNo(clientRequest clientRequest, SysRoute sysRoute, SysConfig SysConfig,
			ApiConfigBean ApiConfigBean);

	/**
	 * @Title: getClientBillNo
	 * @Description: 获取客户端单据号
	 * <AUTHOR>
	 * @param clientRequest
	 * @param jsonmap
	 * @param sysRoute
	 * @param SysConfig
	 * @param ApiConfigBean
	 * @return
	 */
	protected abstract String getServerBillNo(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig,
			ApiConfigBean ApiConfigBean);

	/**
	 * @Title: getProcessResult
	 * @Description: 获取处理结果
	 * <AUTHOR>
	 * @param jsonmap
	 * @param sysRoute
	 * @param SysConfig
	 * @param ApiConfigBean
	 * @return
	 */
	protected abstract ProcessResult getProcessResult(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig,
			ApiConfigBean ApiConfigBean);

	/**
	 * @Title: fillClientResponseData
	 * @Description: 填充发送给客户端的响应
	 * <AUTHOR>
	 * @param clientResponse
	 * @param jsonmap
	 * @param sysRoute
	 * @param SysConfig
	 * @param ApiConfigBean
	 * @return
	 */
	protected abstract clientResponse fillClientResponseData(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig,
			ApiConfigBean ApiConfigBean);

	@Override
	public RequestInfo getRequestInfo(RequestContext requestContext) {
		log.info("L2 BaseCrcementTransfer getRequestInfo end requestContext===>{}", requestContext);
		RequestInfo result = new RequestInfo();
		result.setRequestNo(requestContext.getRequestNo());
		SysRoute sysRoute = requestContext.getSysRoute();
		SysConfig sysConfig = requestContext.getSysConfig();
		ApiConfigBean apiConfigBean = requestContext.getApiConfigBean();
		String clientBillNo = this.getClientBillNo((clientRequest) requestContext.getClientData(), sysRoute, sysConfig,
				apiConfigBean);
		requestContext.setClientBillNo(clientBillNo);
		result.setClientBillNo(clientBillNo);
		result.setOperator(requestContext.getOperator());
		result.setOperatorName(requestContext.getOperatorName());
		log.info("L2 BaseCrcementTransfer getRequestInfoDTO result :{} ", result);
		return result;
	}

	// @Override
	// public ResponseInfo getResponseInfo(RequestContext requestContext) {
	// log.info("L2 BaseCrcementTransfer getResponseInfo start
	// requestContext===>{}", requestContext);
	// // ServerResponse serverResponse, String requestNo, SysRoute sysRoute,
	// // SysConfig SysConfig, ApiConfigBean ApiConfigBean
	// ApiConfigBean ApiConfigBean = requestContext.getApiConfigBean();
	//
	// ResponseInfo result = new ResponseInfo();
	// result.setRequestNo(requestContext.getRequestNo());
	// if
	// (requestContext.getResponseMessage().getRequestStatus().equals(RequestStatusEnum.SEND_FAIL))
	// {
	// result.setCode(requestContext.getCode());
	// result.setMessage(requestContext.getMsg());
	// result.setProcessResult(ProcessResult.FAIL);
	// requestContext.setResponseInfo(result);
	// log.info("L2 BaseCrcementTransfer getResponseInfo end result===>{}", result);
	// return result;
	// }
	// if
	// (requestContext.getResponseMessage().getRequestStatus().equals(RequestStatusEnum.SEND_TIMEOUT))
	// {
	// result.setCode(requestContext.getCode());
	// result.setMessage(requestContext.getMsg());
	// result.setProcessResult(ProcessResult.NEEDRESEND);
	// requestContext.setResponseInfo(result);
	// log.info("L2 BaseCrcementTransfer getResponseInfo end result===>{}", result);
	// return result;
	// }
	//
	// if (requestContext.getResponseMessage().getHeaderParms() != null) {
	// String returnCode = (String)
	// (requestContext.getResponseMessage().getHeaderParms().get(RETURN_CODE));
	// if (!CsStringUtils.isNullOrBlank(returnCode) && !returnCode.startsWith("S")) {
	// if (CsStringUtils.isNotEmpty(ApiConfigBean.getRcNeedRedo())) {
	// if (ApiConfigBean.getRcNeedRedo().contains(
	// String.valueOf(requestContext.getResponseMessage().getHeaderParms().get(RETURN_CODE))))
	// {
	// log.info("L2 getResponseInfoDTO check need redo");
	// result.setProcessResult(ProcessResult.NEEDRESEND);
	// }
	// } else {
	// result.setProcessResult(ProcessResult.FAIL);
	// }
	// result.setCode(String.valueOf(requestContext.getResponseMessage().getHeaderParms().get(RETURN_CODE)));
	// result.setMessage(
	// String.valueOf(requestContext.getResponseMessage().getHeaderParms().get(RETURN_DESC)));
	// requestContext.setCode(result.getCode());
	// requestContext.setMsg(result.getMessage());
	// log.info("L2 BaseCrcementTransfer getResponseInfo end result===>{}", result);
	// return result;
	// }
	// } else {
	// log.error("L2 BaseCrcementTransfer 无法解析获取请求头信息");
	// throw new BizException(APICenterExceptionCode.SERVER_RESPONSE_ERROR,
	// "响应请求头为空");
	// }
	//
	// SysRoute sysRoute = requestContext.getSysRoute();
	// SysConfig SysConfig = requestContext.getSysConfig();
	//
	// // requestContext.setCode(result.getCode());
	// // requestContext.setMsg(result.getMessage());
	// log.info("L2 BaseCrcementTransfer getResponseInfoDTO result :{} ", result);
	// return result;
	// }

	protected String formatCommDate(Date date) {
		if (date == null) {
			return null;
		}
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
		return sdf.format(date);
	}

	protected Date stringToDate(String dateString) throws ParseException {
		if (dateString == null || dateString.equals("")) {
			return null;
		}
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
		return sdf.parse(dateString);
	}

	protected String getCentAmount(BigDecimal bigDecimal) {
		if (bigDecimal == null) {
			return null;
		}
		return bigDecimal.multiply(new BigDecimal("100")).stripTrailingZeros().toPlainString();
	}

	protected String getCentAmount(String yuan) {
		if (CsStringUtils.isBlank(yuan)) {
			return null;
		}
		return getCentAmount(new BigDecimal(yuan));
	}

	protected String getYuanAmount(BigDecimal bigDecimal) {
		if (bigDecimal == null) {
			return null;
		}
		return bigDecimal.setScale(2, RoundingMode.HALF_UP).toString();
	}

	protected BigDecimal getYuan(String cent) {
		if (CsStringUtils.isBlank(cent)) {
			return null;
		}
		return new BigDecimal(cent).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
	}

}
