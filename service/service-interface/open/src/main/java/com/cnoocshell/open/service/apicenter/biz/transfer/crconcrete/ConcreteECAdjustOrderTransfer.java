package com.ecommerce.open.service.apicenter.biz.transfer.crconcrete;

import com.alibaba.fastjson.JSONObject;
import com.ecommerce.open.api.dto.apicenter.order.ECOrderAdjustDTO;
import com.ecommerce.open.api.dto.apicenter.order.ECOrderConfimDTO;
import com.ecommerce.open.dao.bean.ApiConfigBean;
import com.ecommerce.open.dao.vo.SysConfig;
import com.ecommerce.open.dao.vo.SysRoute;
import com.ecommerce.open.enums.ProcessResult;
import org.apache.commons.collections.CollectionUtils;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * Date: Create in 下午8:33 19/7/30
 */
@Deprecated(since = "2.1.4-RELEASE")
@Service("concreteECAdjustOrderTransfer")
public class ConcreteECAdjustOrderTransfer extends BaseCrconcreteTransfer<ECOrderConfimDTO, ECOrderConfimDTO, String, String> {

    @Override
    protected String fillServerRequestData(ECOrderConfimDTO ecOrderConfimDTO, JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        jsonmap.put("ecPlanBillId", ecOrderConfimDTO.getEcPlanBillId());
        jsonmap.put("ERPDistributeCode", ecOrderConfimDTO.getERPDistributeCode());
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        List<JSONObject> adjustList = new ArrayList<>();
        for (ECOrderAdjustDTO ecOrderAdjustDTO : ecOrderConfimDTO.getOrderAdjustDTOS()) {
            JSONObject adjustItem = new JSONObject();
            adjustItem.put("adjustTitle", ecOrderAdjustDTO.getAdjustTitle());
            adjustItem.put("amount", getCentAmount(ecOrderAdjustDTO.getAmount()));
            adjustItem.put("adjustTime", simpleDateFormat.format(ecOrderAdjustDTO.getAdjustTime()));
            adjustItem.put("memo", ecOrderAdjustDTO.getMemo());
            adjustItem.put("operator", ecOrderAdjustDTO.getOperator());
            adjustList.add(adjustItem);
        }
        jsonmap.put("changeList", adjustList);

        return jsonmap.toString();
    }

    @Override
    protected String getClientBillNo(ECOrderConfimDTO ecOrderConfimDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        return ecOrderConfimDTO.getEcPlanBillId();
    }

    @Override
    protected String getServerBillNo(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        return null;
    }

    @Override
    protected ProcessResult getProcessResult(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        if ("01".equals(jsonmap.getString("processCode"))) {
            return ProcessResult.PROCESSING;
        }
        return ProcessResult.FAIL;
    }

    @Override
    protected ECOrderConfimDTO fillClientResponseData(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        ECOrderConfimDTO ecOrderConfimDTO = new ECOrderConfimDTO();
        ecOrderConfimDTO.setEcPlanBillId(jsonmap.getString("ecPlanBillId"));
        return ecOrderConfimDTO;
    }

    @Override
    protected void setNotNullClientParms(Map<String, String> clientParms, ECOrderConfimDTO ecOrderConfimDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        clientParms.put("电商订单号", ecOrderConfimDTO.getEcPlanBillId());
        clientParms.put("ERP订单号", ecOrderConfimDTO.getERPDistributeCode());
        if (CollectionUtils.isEmpty(ecOrderConfimDTO.getOrderAdjustDTOS())) {
            clientParms.put("调价明细列表", null);
        }
        for (ECOrderAdjustDTO ecOrderAdjustDTO : ecOrderConfimDTO.getOrderAdjustDTOS()) {
            clientParms.put("调整类型", ecOrderAdjustDTO.getAdjustTitle());
            if (ecOrderAdjustDTO.getAdjustTime() == null) {
                clientParms.put("调整时间", null);
            }
            if (ecOrderAdjustDTO.getAmount() == null) {
                clientParms.put("调整金额", null);
            }
            clientParms.put("操作人", ecOrderAdjustDTO.getOperator());
            if ("00".equals(ecOrderAdjustDTO.getAdjustTitle()) && CsStringUtils.isEmpty(ecOrderAdjustDTO.getMemo())) {
                clientParms.put("操作说明", null);
            }
        }
    }
}
