package com.ecommerce.open.service.apicenter.biz.transfer.crconcrete;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ecommerce.open.api.dto.apicenter.contract.ECContractListRequestDTO;
import com.ecommerce.open.api.dto.apicenter.contract.ECContractListResultDTO;
import com.ecommerce.open.dao.bean.ApiConfigBean;
import com.ecommerce.open.dao.vo.SysConfig;
import com.ecommerce.open.dao.vo.SysRoute;
import com.ecommerce.open.enums.ProcessResult;
import com.ecommerce.open.enums.pom.logistics.PickingBillTypeEnum;
import com.ecommerce.open.service.apicenter.biz.transfer.crcement.BaseCrcementTransfer;
import lombok.extern.slf4j.Slf4j;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * Date: Create in 下午2:50 19/8/12
 */
@Slf4j
@Service("concreteECBatchQueryContractTransfer")
public class ConcreteECBatchQueryContractTransfer extends BaseCrcementTransfer<ECContractListRequestDTO, List<ECContractListResultDTO>, String, String> {

    @Override
    protected String fillServerRequestData(ECContractListRequestDTO ecContractListRequestDTO, JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        jsonmap.put("contractCode", ecContractListRequestDTO.getContractCode());
        jsonmap.put("externalSellerId", sysRoute.getSendSellerId());
        jsonmap.put("customerNumber", ecContractListRequestDTO.getCustomerNumber());
        jsonmap.put("erpContractCode", ecContractListRequestDTO.getErpContractCode());

        return jsonmap.toJSONString();
    }

    @Override
    protected String getClientBillNo(ECContractListRequestDTO ecContractListRequestDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        return null;
    }

    @Override
    protected String getServerBillNo(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        return null;
    }

    @Override
    protected ProcessResult getProcessResult(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        if (CsStringUtils.equals(jsonmap.getString("code"), "01")) {
            return ProcessResult.SUCCESS;
        }
        return ProcessResult.FAIL;
    }

    @Override
    protected List<ECContractListResultDTO> fillClientResponseData(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        log.info("concreteECBatchQueryContractTransfer_response:" + JSON.toJSONString(jsonmap));
        List<ECContractListResultDTO> contractList = new ArrayList<>();
        if (jsonmap.get("list") != null) {
            JSONArray contractArr = jsonmap.getJSONArray("list");
            contractList = contractArr.toJavaList(ECContractListResultDTO.class);
            contractList.stream().forEach(contractListResultDTO -> {
                if (CsStringUtils.isNotEmpty(contractListResultDTO.getDeliveryWay())) {
                    if ("1".equals(contractListResultDTO.getDeliveryWay()) || "2".equals(contractListResultDTO.getDeliveryWay())) {
                        contractListResultDTO.setDeliveryWay(PickingBillTypeEnum.BUYER_TAKE.getCode());
                    } else if ("4".equals(contractListResultDTO.getDeliveryWay())) {
                        contractListResultDTO.setDeliveryWay(PickingBillTypeEnum.SELLER_DELIVERY.getCode());
                    }
                }
                if (CsStringUtils.isNotBlank(contractListResultDTO.getProjectName())) {
                    contractListResultDTO.setContractCode(contractListResultDTO.getProjectName());
                }
            });
        }

        return contractList;
    }

    @Override
    protected void setNotNullClientParms(Map<String, String> clientParms, ECContractListRequestDTO ecContractListRequestDTO,
                                         SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        clientParms.put("外部系统卖家编号", ecContractListRequestDTO.getExternalSellerId());
        clientParms.put("ERP客户编号", ecContractListRequestDTO.getCustomerNumber());
    }
}
