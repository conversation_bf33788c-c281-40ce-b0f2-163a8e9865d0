package com.ecommerce.open.service.apicenter.biz.transfer.crconcrete;

import com.alibaba.fastjson.JSONObject;
import com.ecommerce.open.api.dto.apicenter.order.ECOrderCloseDTO;
import com.ecommerce.open.dao.bean.ApiConfigBean;
import com.ecommerce.open.dao.vo.SysConfig;
import com.ecommerce.open.dao.vo.SysRoute;
import com.ecommerce.open.enums.ProcessResult;
import com.ecommerce.open.service.apicenter.biz.transfer.crcement.BaseCrcementTransfer;
import com.ecommerce.open.api.dto.pom.order.OrderCloseERPCallback;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

@Deprecated(since = "2.1.4-RELEASE")
@Service("concreteECCloseOrderQueryTransfer")
public class ConcreteECCloseOrderQueryTransfer extends BaseCrcementTransfer<ECOrderCloseDTO, OrderCloseERPCallback, String, String> {

    @Override
    protected String fillServerRequestData(ECOrderCloseDTO ECOrderCloseDTO, JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        jsonmap.put("ecPlanBillId",ECOrderCloseDTO.getEcPlanBillId());
        jsonmap.put("baseRequestNo",ECOrderCloseDTO.getBaseRequestNo());
        return jsonmap.toJSONString();
    }

    @Override
    protected String getClientBillNo(ECOrderCloseDTO ECOrderCloseDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        return null;
    }

    @Override
    protected String getServerBillNo(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        return null;
    }

    @Override
    protected ProcessResult getProcessResult(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        if (CsStringUtils.equals(jsonmap.getString("code"), "01")) {
            return ProcessResult.SUCCESS;
        }
        return ProcessResult.FAIL;
    }

    @Override
    protected OrderCloseERPCallback fillClientResponseData(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
    	OrderCloseERPCallback resultDTO = new OrderCloseERPCallback();
//    	resultDTO.setStatus(status);
//        resultDTO.setWaybillNum(jsonmap.getString("ecBillCode"));
//        resultDTO.setErpWaybillNum(jsonmap.getString("externalBillId"));
//        resultDTO.setStatus(jsonmap.getString("billStatus"));
//        resultDTO.setCreateTime(jsonmap.getDate("externalChangeDate"));//外部运单修改时间：
    	resultDTO.setStatus(jsonmap.getString("status"));
    	resultDTO.setERPDistributeCode(jsonmap.getString("ERPDistributeCode"));
    	resultDTO.setEcPlanBillId(jsonmap.getString("ecPlanBillId"));
        return resultDTO;
    }

    @Override
    protected void setNotNullClientParms(Map<String, String> clientParms, ECOrderCloseDTO ECOrderCloseDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
//        clientParms.put("外部运单号", ECOrderCloseDTO.getWaybillNum());
    }
}
