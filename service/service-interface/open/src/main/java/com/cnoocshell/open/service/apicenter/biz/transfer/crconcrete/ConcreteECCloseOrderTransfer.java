package com.ecommerce.open.service.apicenter.biz.transfer.crconcrete;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ecommerce.open.api.dto.apicenter.BaseClientResponseDTO;
import com.ecommerce.open.api.dto.apicenter.order.ECOrderCloseDTO;
import com.ecommerce.open.dao.bean.ApiConfigBean;
import com.ecommerce.open.dao.vo.SysConfig;
import com.ecommerce.open.dao.vo.SysRoute;
import com.ecommerce.open.enums.ProcessResult;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Slf4j
@Service("concreteECCloseOrderTransfer")
public class ConcreteECCloseOrderTransfer extends BaseCrconcreteTransfer<ECOrderCloseDTO, BaseClientResponseDTO, String, String> {

    @Override
    protected String fillServerRequestData(ECOrderCloseDTO ecOrderCloseDTO, JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
		log.info("ConcreteECCloseOrderTransfer_ecOrderCloseDTO:" + JSON.toJSONString(ecOrderCloseDTO));
        jsonmap.put("ecOperatorId", ecOrderCloseDTO.getEcOperatorId());
		jsonmap.put("ecOperatorName", ecOrderCloseDTO.getEcOperatorName());
		jsonmap.put("ecPlanBillId", ecOrderCloseDTO.getEcPlanBillId());
		jsonmap.put("ERPDistributeCode", ecOrderCloseDTO.getERPDistributeCode());
		jsonmap.put("closeReason", ecOrderCloseDTO.getCloseReason());
		jsonmap.put("mome", ecOrderCloseDTO.getMemo());
		jsonmap.put("clearAmount", getCentAmount(ecOrderCloseDTO.getClearAmount()));
		//实际商品费用
		jsonmap.put("realtimeGoodsAmount", getCentAmount(ecOrderCloseDTO.getClearGoodsAmount()));
		//实际台班费
		jsonmap.put("realtimeClassAmount", getCentAmount(ecOrderCloseDTO.getClassCostRealAmount()));
		//实际空载费
		jsonmap.put("realtimeIdlingAmount", getCentAmount(ecOrderCloseDTO.getIdlingCostRealAmount()));
		//调整总金额
		jsonmap.put("adjustAmount", getCentAmount(ecOrderCloseDTO.getAdjustAmount()));
        //最后一车交货单号
        jsonmap.put("lastExternalBillId", ecOrderCloseDTO.getLastExternalBillId());
        //调整明细列表
        List<JSONObject> adjustList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(ecOrderCloseDTO.getAdjustList())) {
			ecOrderCloseDTO.getAdjustList().stream().forEach(orderAdjust -> {
				JSONObject orderAdjustObject = new JSONObject();
				orderAdjustObject.put("adjustTitle", orderAdjust.getAdjustTitle());
				orderAdjustObject.put("amount", getCentAmount(orderAdjust.getAmount()));
				orderAdjustObject.put("memo", orderAdjust.getMemo());
				orderAdjustObject.put("operator", orderAdjust.getOperator());
				orderAdjustObject.put("adjustTime", formatCommDate(orderAdjust.getAdjustTime()));
                adjustList.add(orderAdjustObject);
			});
		}
		jsonmap.put("list", adjustList);

        return jsonmap.toJSONString();
    }

    @Override
    protected String getClientBillNo(ECOrderCloseDTO ecOrderCloseDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        return ecOrderCloseDTO.getEcPlanBillId();
    }

    @Override
    protected String getServerBillNo(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        return null;
    }

    @Override
    protected ProcessResult getProcessResult(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        if ("01".equals(jsonmap.getString("processCode"))) {
            return ProcessResult.PROCESSING;
        }
        return ProcessResult.FAIL;
    }

    @Override
    protected BaseClientResponseDTO fillClientResponseData(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        return null;
    }

    @Override
    protected void setNotNullClientParms(Map<String, String> clientParms, ECOrderCloseDTO ecOrderCloseDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        clientParms.put("电商操作人id", ecOrderCloseDTO.getEcOperatorId());
        clientParms.put("电商操作人姓名", ecOrderCloseDTO.getEcOperatorName());
        clientParms.put("电商订单号", ecOrderCloseDTO.getEcPlanBillId());
		clientParms.put("ERP订单号", ecOrderCloseDTO.getERPDistributeCode());
        clientParms.put("关闭原因", ecOrderCloseDTO.getCloseReason());
        clientParms.put("结算总金额", getCentAmount(ecOrderCloseDTO.getClearAmount()));
		clientParms.put("商品费实际总金额", getCentAmount(ecOrderCloseDTO.getClearGoodsAmount()));
    }
}
