package com.ecommerce.open.service.apicenter.biz.transfer.crconcrete;

import com.alibaba.fastjson.JSONObject;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.open.api.dto.pom.logistics.ERPWaybillDTO;
import com.ecommerce.open.api.dto.apicenter.BaseClientResponseDTO;
import com.ecommerce.open.api.dto.apicenter.cement.logistics.ECCloseConcreteCBRequestDTO;
import com.ecommerce.open.dao.bean.ApiConfigBean;
import com.ecommerce.open.dao.vo.SysConfig;
import com.ecommerce.open.dao.vo.SysRoute;
import com.ecommerce.open.enums.ProcessResult;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

@Deprecated(since = "2.1.4-RELEASE")
@Service("concreteECCloseWaybillCallbackTransfer")
public class ConcreteECCloseWaybillCallbackTransfer extends BaseCrconcreteCallbackTransfer<ECCloseConcreteCBRequestDTO, BaseClientResponseDTO, ItemResult<ERPWaybillDTO>, String> {

    @Override
    public ItemResult<ERPWaybillDTO> createDefaultFailServerRequestData(JSONObject jsonmap, String serverBillNo) {
        ItemResult<ERPWaybillDTO> result = new ItemResult<>();
        ERPWaybillDTO waybillDTO = new ERPWaybillDTO();
        waybillDTO.setWaybillNum(serverBillNo);
        result.setData(waybillDTO);
        result.setSuccess(false);
        return result;
    }

    @Override
    protected String getServerBillNo(ECCloseConcreteCBRequestDTO ecCloseConcreteCBRequestDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        return ecCloseConcreteCBRequestDTO.getEcBillCode();
    }

    @Override
    protected ProcessResult getProcessResult(ECCloseConcreteCBRequestDTO ecCloseConcreteCBRequestDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        if (ecCloseConcreteCBRequestDTO == null) {
            return ProcessResult.FAIL;
        }

        if (CsStringUtils.equals(ecCloseConcreteCBRequestDTO.getBillResult(), "05")) {
            return ProcessResult.SUCCESS;
        }

        return ProcessResult.FAIL;
    }

    @Override
    protected ItemResult<ERPWaybillDTO> fillServerRequestData(ECCloseConcreteCBRequestDTO ecCloseConcreteCBRequestDTO, JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        ItemResult<ERPWaybillDTO> result = new ItemResult<>();
        ERPWaybillDTO waybillDTO = new ERPWaybillDTO();
        waybillDTO.setWaybillNum(ecCloseConcreteCBRequestDTO.getEcBillCode());
        waybillDTO.setErpWaybillNum(ecCloseConcreteCBRequestDTO.getExternalBillId());
        waybillDTO.setStatus(ecCloseConcreteCBRequestDTO.getBillResult());
        result.setData(waybillDTO);
        if (CsStringUtils.equals("E0B02D15", ecCloseConcreteCBRequestDTO.getCode()) || CsStringUtils.equals("E0B02D15", ecCloseConcreteCBRequestDTO.getProcessCode())) {
            result.setSuccess(false);
        } else if (CsStringUtils.equals(ecCloseConcreteCBRequestDTO.getBillResult(), "05")) {
            result.setSuccess(true);
        } else {
            result.setSuccess(false);
        }
        String message = ecCloseConcreteCBRequestDTO.getMessage();
        if (CsStringUtils.isBlank(message)) {
            message = ecCloseConcreteCBRequestDTO.getProcessMessage();
        }
        result.setDescription(message);
        return result;
    }

    @Override
    protected String getClientBillNo(ECCloseConcreteCBRequestDTO ecCloseConcreteCBRequestDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        if (ecCloseConcreteCBRequestDTO == null) {
            return null;
        }
        return ecCloseConcreteCBRequestDTO.getExternalBillId();
    }

    @Override
    protected BaseClientResponseDTO fillClientResponseData(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        BaseClientResponseDTO responseDTO =  new BaseClientResponseDTO();
        if (jsonmap != null && CsStringUtils.equals(jsonmap.getString("success"), "true")) {
            responseDTO.setCode("01");
            responseDTO.setMessage("接收成功");
        } else {
            responseDTO.setCode("00");
            responseDTO.setMessage("接收失败");
        }
        return responseDTO;
    }

    @Override
    protected void setNotNullClientParms(Map<String, String> clientParms, ECCloseConcreteCBRequestDTO ecCloseConcreteCBRequestDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        clientParms.put("原请求号", ecCloseConcreteCBRequestDTO.getBaseRequestNo());
        clientParms.put("电商运单号", ecCloseConcreteCBRequestDTO.getEcBillCode());
        clientParms.put("外部运单号", ecCloseConcreteCBRequestDTO.getExternalBillId());
        clientParms.put("运单状态", ecCloseConcreteCBRequestDTO.getBillResult());
    }
}
