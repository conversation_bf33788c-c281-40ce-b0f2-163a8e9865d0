package com.ecommerce.open.service.apicenter.biz.transfer.crconcrete;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.ecommerce.open.enums.pom.logistics.WaybillStatusEnum;
import com.ecommerce.open.api.dto.apicenter.logistic.ECWaybillDTO;
import com.ecommerce.open.api.dto.apicenter.logistic.ECWaybillItemDTO;
import com.ecommerce.open.api.dto.erp.ECCarInfoDTO;
import com.ecommerce.open.dao.bean.ApiConfigBean;
import com.ecommerce.open.dao.vo.SysConfig;
import com.ecommerce.open.dao.vo.SysRoute;
import com.ecommerce.open.enums.ProcessResult;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Deprecated(since = "2.1.4-RELEASE")
@Service("concreteECCloseWaybillTransfer")
public class ConcreteECCloseWaybillTransfer extends BaseCrconcreteTransfer<ECWaybillDTO, ECWaybillDTO, String, String> {

    @Override
    protected String fillServerRequestData(ECWaybillDTO ecWaybillDTO, JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        String erpStatus;
        String ecStatus = ecWaybillDTO.getStatus();
        if (CsStringUtils.equals(ecStatus, WaybillStatusEnum.COMPLETED.getCode())) {
            erpStatus = "04";
        } else if (CsStringUtils.equals(ecStatus, WaybillStatusEnum.SIGNING.getCode())) {
            erpStatus = "03";
        } else if (CsStringUtils.equals(ecStatus, WaybillStatusEnum.DELIVERING.getCode())) {
            erpStatus = "02";
        } else {
            erpStatus = "00";
        }
        jsonmap.put("status", erpStatus);
        jsonmap.put("ecBillCode", ecWaybillDTO.getWaybillNum());
        jsonmap.put("externalBillId", ecWaybillDTO.getErpWaybillNum());
        jsonmap.put("sendTime", formatCommDate(ecWaybillDTO.getDeliveryTime()));
        jsonmap.put("ecOrderCode", ecWaybillDTO.getOrderCode());
        jsonmap.put("erpOrderCode", ecWaybillDTO.getErpOrderCode());
        jsonmap.put("realAddress", ecWaybillDTO.getReceiveAddress());
        jsonmap.put("mome", ecWaybillDTO.getCloseReason());
        jsonmap.put("pumpNo", ecWaybillDTO.getPumpNo());
        jsonmap.put("pumperName", ecWaybillDTO.getPumperName());
        if (ecWaybillDTO.getCompleteTime() == null) {
            jsonmap.put("unloadingTime", formatCommDate(ecWaybillDTO.getCloseTime()));
        } else {
            jsonmap.put("unloadingTime", formatCommDate(ecWaybillDTO.getCompleteTime()));
        }
        ECCarInfoDTO carInfoDTO = ecWaybillDTO.getCarInfoDTO();
        if (carInfoDTO != null) {
            jsonmap.put("vehicleNo", carInfoDTO.getNumber());
            jsonmap.put("gpsManufacturerId", carInfoDTO.getGpsId());
            jsonmap.put("gpsDeviceNumber", carInfoDTO.getGpsDeviceNumber());
            jsonmap.put("driverName", carInfoDTO.getDriverName());
            jsonmap.put("driverCode", carInfoDTO.getBindDriverId());
        }
        List<ECWaybillItemDTO> goodsList = ecWaybillDTO.getWaybillItemList();
        if (!CollectionUtils.isEmpty(goodsList)) {
            ECWaybillItemDTO waybillItemDTO = goodsList.get(0);
            jsonmap.put("rowNo", "0");
            if (waybillItemDTO.getQuantity() != null) {
                jsonmap.put("billWeight", waybillItemDTO.getQuantity().setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
            }
            jsonmap.put("commodityCode", waybillItemDTO.getCommodityCode());
            jsonmap.put("contractCode", waybillItemDTO.getContractCode());
            jsonmap.put("sendAmount", getYuanAmount(waybillItemDTO.getActualQuantity()));
            jsonmap.put("signAmount", getYuanAmount(waybillItemDTO.getSignQuantity()));
            jsonmap.put("qrCode", waybillItemDTO.getQrCode());
            jsonmap.put("realCostAmount", getCentAmount(waybillItemDTO.getRealCostAmount()));
            jsonmap.put("realUnitPrice", getCentAmount(waybillItemDTO.getRealUnitPrice()));
        }
        return jsonmap.toJSONString();
    }

    @Override
    protected String getClientBillNo(ECWaybillDTO ecWaybillDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        return ecWaybillDTO.getWaybillNum();
    }

    @Override
    protected String getServerBillNo(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        return null;
    }

    @Override
    protected ProcessResult getProcessResult(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        if ("01".equals(jsonmap.getString("processCode"))) {
            return ProcessResult.PROCESSING;
        }
        return ProcessResult.FAIL;
    }

    @Override
    protected ECWaybillDTO fillClientResponseData(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        ECWaybillDTO resultDTO = new ECWaybillDTO();
        resultDTO.setRequestNo(jsonmap.getString("requestNo"));
        return resultDTO;
    }

    @Override
    protected void setNotNullClientParms(Map<String, String> clientParms, ECWaybillDTO ecWaybillDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        clientParms.put("送货单状态", ecWaybillDTO.getStatus());
        clientParms.put("电商运单号", ecWaybillDTO.getWaybillNum());
        clientParms.put("erp送货单号", ecWaybillDTO.getErpWaybillNum());
//        clientParms.put("送货日期",formatCommDate(ecWaybillDTO.getDeliveryTime()));
//        List<ECWaybillItemDTO> goodsList = ecWaybillDTO.getWaybillItemList();
//        if (CollectionUtils.isEmpty(goodsList)) {
//            clientParms.put("配送商品", null);
//        } else {
//            ECWaybillItemDTO waybillItemDTO = goodsList.get(0);
//            if (waybillItemDTO.getQuantity() == null) {
//                clientParms.put("开单量", null);
//            }
//            clientParms.put("商品编码", waybillItemDTO.getCommodityCode());
//            clientParms.put("合同号", waybillItemDTO.getContractCode());
//            if (waybillItemDTO.getActualQuantity() == null) {
//                clientParms.put("送货方量", null);
//            }
//        }
    }
}
