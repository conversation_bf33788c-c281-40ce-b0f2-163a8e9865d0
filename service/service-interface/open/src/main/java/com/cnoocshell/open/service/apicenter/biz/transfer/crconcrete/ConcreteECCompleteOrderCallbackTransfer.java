package com.ecommerce.open.service.apicenter.biz.transfer.crconcrete;

import com.alibaba.fastjson.JSONObject;
import com.ecommerce.open.api.dto.apicenter.BaseClientResponseDTO;
import com.ecommerce.open.api.dto.apicenter.order.ECOrderCloseDTO;
import com.ecommerce.open.dao.bean.ApiConfigBean;
import com.ecommerce.open.dao.vo.SysConfig;
import com.ecommerce.open.dao.vo.SysRoute;
import com.ecommerce.open.enums.ApiBizSupportEnum;
import com.ecommerce.open.enums.ProcessResult;
import com.ecommerce.open.service.apicenter.dto.RequestContext;
import com.ecommerce.open.api.dto.pom.order.OrderCompleteERPCallback;
import com.ecommerce.open.enums.pom.order.OrderERPStatusEnum;

import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

@Deprecated(since = "2.1.4-RELEASE")
@Service("ConcreteECCompleteOrderCallbackTransfer")
public class ConcreteECCompleteOrderCallbackTransfer extends
		BaseCrconcreteCallbackTransfer<ECOrderCloseDTO, BaseClientResponseDTO, OrderCompleteERPCallback, String> {

	public ApiBizSupportEnum checkSupport(RequestContext requestContext) {
		return ApiBizSupportEnum.NOT_SUPPORT_SUCCESS;
	}
	
	@Override
	public OrderCompleteERPCallback createDefaultFailServerRequestData(JSONObject jsonmap, String serverBillNo) {
		OrderCompleteERPCallback callback = new OrderCompleteERPCallback();
		callback.setEcPlanBillId(serverBillNo);
		callback.setStatus("10");
		return callback;
	}

	@Override
	protected String getServerBillNo(ECOrderCloseDTO ECOrderCloseDTO, SysRoute sysRoute, SysConfig SysConfig,
			ApiConfigBean ApiConfigBean) {
		return ECOrderCloseDTO.getEcPlanBillId();
	}

	@Override
	protected ProcessResult getProcessResult(ECOrderCloseDTO ECOrderCloseDTO, SysRoute sysRoute, SysConfig SysConfig,
			ApiConfigBean ApiConfigBean) {
		if (ECOrderCloseDTO == null) {
			return ProcessResult.FAIL;
		}

		if (CsStringUtils.equals("01", ECOrderCloseDTO.getCode())) {
			return ProcessResult.SUCCESS;
		}

		return ProcessResult.FAIL;
	}

	@Override
	protected OrderCompleteERPCallback fillServerRequestData(ECOrderCloseDTO ecCreateOrderCBRequestDTO,
			JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
		if (ecCreateOrderCBRequestDTO == null) {
			return null;
		}
		OrderCompleteERPCallback callback = new OrderCompleteERPCallback();
		BeanUtils.copyProperties(ecCreateOrderCBRequestDTO, callback);

		callback.setStatus(OrderERPStatusEnum.ORDER_COMPLETE_ERP_SUCCESS.getCode());

		return callback;
	}

	@Override
	protected String getClientBillNo(ECOrderCloseDTO ECOrderCloseDTO, SysRoute sysRoute, SysConfig SysConfig,
			ApiConfigBean ApiConfigBean) {
		return ECOrderCloseDTO.getERPDistributeCode();
	}

	@Override
	protected BaseClientResponseDTO fillClientResponseData(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig,
			ApiConfigBean ApiConfigBean) {
		return new BaseClientResponseDTO();
	}

	@Override
	protected void setNotNullClientParms(Map<String, String> clientParms, ECOrderCloseDTO ECOrderCloseDTO,
			SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
		clientParms.put("响应消息代码", ECOrderCloseDTO.getCode());
		clientParms.put("响应消息具体内容", ECOrderCloseDTO.getMessage());
		clientParms.put("电商单据编号", ECOrderCloseDTO.getEcPlanBillId());
		// clientParms.put("配送需求状态", ECOrderCloseDTO.getStatus());
		clientParms.put("ERP提货计划编号", ECOrderCloseDTO.getERPDistributeCode());
	}
}
