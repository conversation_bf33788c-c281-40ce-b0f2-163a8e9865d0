package com.ecommerce.open.service.apicenter.biz.transfer.crconcrete;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ecommerce.open.api.dto.apicenter.BaseClientResponseDTO;
import com.ecommerce.open.api.dto.apicenter.order.ECOrderCloseDTO;
import com.ecommerce.open.api.dto.apicenter.order.ECOrderConfimDTO;
import com.ecommerce.open.dao.bean.ApiConfigBean;
import com.ecommerce.open.dao.vo.SysConfig;
import com.ecommerce.open.dao.vo.SysRoute;
import com.ecommerce.open.enums.ProcessResult;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Map;

@Deprecated(since = "2.1.4-RELEASE")
@Service("concreteECConfimOrderTransfer")
public class ConcreteECConfimOrderTransfer extends BaseCrconcreteTransfer<ECOrderConfimDTO, BaseClientResponseDTO, String, String> {

    @Override
    protected String fillServerRequestData(ECOrderConfimDTO ecOrderConfimDTO, JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        jsonmap.put("ecOperatorId", ecOrderConfimDTO.getEcOperatorId());
        jsonmap.put("ecOperatorName", ecOrderConfimDTO.getEcOperatorName());
        jsonmap.put("ecPlanBillId", ecOrderConfimDTO.getEcPlanBillId());
        jsonmap.put("ERPDistributeCode", ecOrderConfimDTO.getERPDistributeCode());
        JSONArray jsonArray = new JSONArray();
        if(!CollectionUtils.isEmpty(ecOrderConfimDTO.getOrderAdjustDTOS())){
            ecOrderConfimDTO.getOrderAdjustDTOS().stream().forEach(orderAdjust ->{
                JSONObject jsonmapOrderAdjust = new JSONObject();
                jsonmapOrderAdjust.put("adjustTitle",orderAdjust.getAdjustTitle());
                jsonmapOrderAdjust.put("amount",getCentAmount(orderAdjust.getAmount()));
                jsonmapOrderAdjust.put("memo",orderAdjust.getMemo());
                jsonmapOrderAdjust.put("operator",orderAdjust.getOperator());
                jsonmapOrderAdjust.put("adjustTime",formatCommDate(orderAdjust.getAdjustTime()));
                jsonArray.add(jsonmapOrderAdjust);
            });
        }
        jsonmap.put("orderAdjustList", jsonArray.toJSONString());

        return jsonmap.toJSONString();
    }

    @Override
    protected String getClientBillNo(ECOrderConfimDTO ecOrderConfimDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        return ecOrderConfimDTO.getEcPlanBillId();
    }

    @Override
    protected String getServerBillNo(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        return null;
    }

    @Override
    protected ProcessResult getProcessResult(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        if ("01".equals(jsonmap.getString("processCode"))) {
            return ProcessResult.PROCESSING;
        }
        return ProcessResult.FAIL;
    }

    @Override
    protected BaseClientResponseDTO fillClientResponseData(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        return null;
    }

    @Override
    protected void setNotNullClientParms(Map<String, String> clientParms, ECOrderConfimDTO ecOrderConfimDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        clientParms.put("电商操作人id", ecOrderConfimDTO.getEcOperatorId());
        clientParms.put("电商操作人姓名", ecOrderConfimDTO.getEcOperatorName());
        clientParms.put("电商单据编号", ecOrderConfimDTO.getEcPlanBillId());
    }
}
