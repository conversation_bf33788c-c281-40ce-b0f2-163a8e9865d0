package com.ecommerce.open.service.apicenter.biz.transfer.crconcrete;

import com.alibaba.fastjson.JSONObject;
import com.ecommerce.open.api.dto.apicenter.BaseClientResponseDTO;
import com.ecommerce.open.api.dto.apicenter.cement.ECCreateOrderCBRequestDTO;
import com.ecommerce.open.api.dto.pom.order.OrderCreateERPCallback;
import com.ecommerce.open.dao.bean.ApiConfigBean;
import com.ecommerce.open.dao.vo.SysConfig;
import com.ecommerce.open.dao.vo.SysRoute;
import com.ecommerce.open.enums.ProcessResult;
import com.ecommerce.open.enums.pom.order.OrderERPStatusEnum;
import com.ecommerce.open.service.apicenter.dto.RequestContext;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Map;


@Service("concreteECCreateOrderCallbackTransfer")
public class ConcreteECCreateOrderCallbackTransfer extends
		BaseCrconcreteCallbackTransfer<ECCreateOrderCBRequestDTO, BaseClientResponseDTO, OrderCreateERPCallback, String> {

	@Override
	public OrderCreateERPCallback createDefaultFailServerRequestData(JSONObject jsonmap, String serverBillNo) {
		OrderCreateERPCallback callback = new OrderCreateERPCallback();
		callback.setStatus(OrderERPStatusEnum.ORDER_CREATE_ERP_ERROR.getCode());
		callback.setEcPlanBillId(serverBillNo);
		if (!CsStringUtils.isNullOrBlank(jsonmap.getString("xmessage"))) {
			callback.setMessage(jsonmap.getString("xmessage"));
		} else {
			callback.setMessage(jsonmap.getString("message"));
		}
		return callback;
	}

	@Override
	public OrderCreateERPCallback createDefaultSuccessServerRequestData(JSONObject jsonmap, RequestContext requestContext) {
		JSONObject responseData = requestContext.getResponseMessage().getResponseJSON();
		if (responseData != null) {
			OrderCreateERPCallback callback = new OrderCreateERPCallback();
			callback.setERPDistributeCode(responseData.getString("ERPDistributeCode"));
			callback.setEcPlanBillId(responseData.getString("ecPlanBillId"));
			callback.setFreezeAmount(responseData.getString("freezeAmount"));
			callback.setStatus(OrderERPStatusEnum.ORDER_CREATE_ERP_SUCCESS.getCode());
			return callback;
		}
		return null;
	}

	@Override
	protected String getServerBillNo(ECCreateOrderCBRequestDTO ecCreateOrderCBRequestDTO, SysRoute sysRoute,
			SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
		return ecCreateOrderCBRequestDTO.getEcPlanBillId();
	}

	@Override
	protected ProcessResult getProcessResult(ECCreateOrderCBRequestDTO ecCreateOrderCBRequestDTO, SysRoute sysRoute,
			SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
		if (ecCreateOrderCBRequestDTO == null) {
			return ProcessResult.FAIL;
		}

		if (CsStringUtils.equals(ecCreateOrderCBRequestDTO.getCode(), "01")) {
			return ProcessResult.SUCCESS;
		}

		return ProcessResult.FAIL;
	}

	@Override
	protected OrderCreateERPCallback fillServerRequestData(ECCreateOrderCBRequestDTO ecCreateOrderCBRequestDTO,
			JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
		if (ecCreateOrderCBRequestDTO == null) {
			return null;
		}
		OrderCreateERPCallback callback = new OrderCreateERPCallback();
		BeanUtils.copyProperties(ecCreateOrderCBRequestDTO, callback);
		if ("E0B02D15".equals(ecCreateOrderCBRequestDTO.getCode())
				|| "E0B02D15".equals(ecCreateOrderCBRequestDTO.getProcessCode())) {
			callback.setStatus(OrderERPStatusEnum.ORDER_CREATE_ERP_ERROR.getCode());
		} else {
			callback.setStatus(OrderERPStatusEnum.ORDER_CREATE_ERP_SUCCESS.getCode());
		}
		if (!CsStringUtils.isNullOrBlank(ecCreateOrderCBRequestDTO.getProcessMessage())) {
			callback.setMessage(ecCreateOrderCBRequestDTO.getProcessMessage());
		} else {
			callback.setMessage(ecCreateOrderCBRequestDTO.getMessage());
		}
		callback.setUnloadingCode(ecCreateOrderCBRequestDTO.getPickupPointCode());
		return callback;
	}

	@Override
	protected String getClientBillNo(ECCreateOrderCBRequestDTO ecCreateOrderCBRequestDTO, SysRoute sysRoute,
			SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
		return ecCreateOrderCBRequestDTO.getERPDistributeCode();
	}

	@Override
	protected BaseClientResponseDTO fillClientResponseData(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig,
			ApiConfigBean ApiConfigBean) {
		// BaseClientResponseDTO baseClientResponseDTO = new BaseClientResponseDTO();

		return new BaseClientResponseDTO();
	}

	@Override
	protected void setNotNullClientParms(Map<String, String> clientParms,
			ECCreateOrderCBRequestDTO ecCreateOrderCBRequestDTO, SysRoute sysRoute, SysConfig SysConfig,
			ApiConfigBean ApiConfigBean) {
		clientParms.put("响应消息代码", ecCreateOrderCBRequestDTO.getCode());
		clientParms.put("响应消息具体内容", ecCreateOrderCBRequestDTO.getMessage());
		clientParms.put("电商单据编号", ecCreateOrderCBRequestDTO.getEcPlanBillId());
//		clientParms.put("配送需求状态", ecCreateOrderCBRequestDTO.getStatus());
		clientParms.put("ERP提货计划编号", ecCreateOrderCBRequestDTO.getERPDistributeCode());
		clientParms.put("ERP合同号", ecCreateOrderCBRequestDTO.getERPContractCode());
	}
}
