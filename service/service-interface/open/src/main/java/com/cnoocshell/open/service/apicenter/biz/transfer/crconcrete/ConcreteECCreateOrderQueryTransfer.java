package com.ecommerce.open.service.apicenter.biz.transfer.crconcrete;

import com.alibaba.fastjson.JSONObject;
import com.ecommerce.open.api.dto.apicenter.cement.ECCreateOrderCBRequestDTO;
import com.ecommerce.open.api.dto.apicenter.order.ECOrderInfoDTO;
import com.ecommerce.open.dao.bean.ApiConfigBean;
import com.ecommerce.open.dao.vo.SysConfig;
import com.ecommerce.open.dao.vo.SysRoute;
import com.ecommerce.open.enums.ProcessResult;
import com.ecommerce.open.service.apicenter.biz.transfer.crcement.BaseCrcementTransfer;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

@Deprecated(since = "2.1.4-RELEASE")
@Service("concreteECCreateOrderQueryTransfer")
public class ConcreteECCreateOrderQueryTransfer
		extends BaseCrconcreteTransfer<ECOrderInfoDTO, ECCreateOrderCBRequestDTO, String, String> {

	@Override
	protected String fillServerRequestData(ECOrderInfoDTO ECOrderInfoDTO, JSONObject jsonmap, SysRoute sysRoute,
			SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
		jsonmap.put("ecPlanBillId", ECOrderInfoDTO.getOrderCode());
		jsonmap.put("baseRequestNo", ECOrderInfoDTO.getBaseRequestNo());
		return jsonmap.toJSONString();
	}

	@Override
	protected String getClientBillNo(ECOrderInfoDTO ECOrderInfoDTO, SysRoute sysRoute, SysConfig SysConfig,
			ApiConfigBean ApiConfigBean) {
		return null;
	}

	@Override
	protected String getServerBillNo(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig,
			ApiConfigBean ApiConfigBean) {
		return null;
	}

	@Override
	protected ProcessResult getProcessResult(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig,
			ApiConfigBean ApiConfigBean) {
		if (CsStringUtils.equals(jsonmap.getString("code"), "01")) {
			return ProcessResult.SUCCESS;
		}
		return ProcessResult.FAIL;
	}

	@Override
	protected ECCreateOrderCBRequestDTO fillClientResponseData(JSONObject jsonmap, SysRoute sysRoute,
			SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
		ECCreateOrderCBRequestDTO resultDTO = new ECCreateOrderCBRequestDTO();
		// resultDTO.setStatus(status);
		// resultDTO.setWaybillNum(jsonmap.getString("ecBillCode"));
		// resultDTO.setErpWaybillNum(jsonmap.getString("externalBillId"));
		// resultDTO.setStatus(jsonmap.getString("billStatus"));
		// resultDTO.setCreateTime(jsonmap.getDate("externalChangeDate"));//外部运单修改时间：
		resultDTO.setStatus(jsonmap.getString("status"));
		resultDTO.setERPDistributeCode(jsonmap.getString("ERPDistributeCode"));
		resultDTO.setEcPlanBillId(jsonmap.getString("ecPlanBillId"));
		return resultDTO;
	}

	@Override
	protected void setNotNullClientParms(Map<String, String> clientParms, ECOrderInfoDTO ECOrderInfoDTO,
			SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
		// clientParms.put("外部运单号", ECOrderInfoDTO.getWaybillNum());
	}
}
