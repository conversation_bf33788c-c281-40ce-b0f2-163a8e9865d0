package com.ecommerce.open.service.apicenter.biz.transfer.crconcrete;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.utils.ArithUtils;
import com.ecommerce.open.api.dto.apicenter.BaseClientResponseDTO;
import com.ecommerce.open.api.dto.apicenter.order.ECOrderInfoDTO;
import com.ecommerce.open.api.dto.apicenter.order.ECOrderItemAddDTO;
import com.ecommerce.open.api.dto.apicenter.order.ECOrderItemDTO;
import com.ecommerce.open.dao.bean.ApiConfigBean;
import com.ecommerce.open.dao.vo.SysConfig;
import com.ecommerce.open.dao.vo.SysRoute;
import com.ecommerce.open.enums.ProcessResult;
import com.ecommerce.open.enums.pom.logistics.PickingBillTypeEnum;
import com.ecommerce.pay.api.v2.enums.ChannelCodeEnum;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Service("concreteECCreateOrderTransfer")
public class ConcreteECCreateOrderTransfer
		extends BaseCrconcreteTransfer<ECOrderInfoDTO, BaseClientResponseDTO, String, String> {

	@Override
	protected String fillServerRequestData(ECOrderInfoDTO ecOrderInfoDTO, JSONObject jsonmap, SysRoute sysRoute,
			SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
		log.info("concreteECCreateOrderTransfer:" + JSON.toJSONString(ecOrderInfoDTO));
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
		// 测试用固定为C1140311900022
		jsonmap.put("contractCode", ecOrderInfoDTO.getErpContractCode());
//		jsonmap.put("contractCode", "C1140311900022");
		jsonmap.put("mdmCode", ecOrderInfoDTO.getMdmCode());
		// jsonmap.put("externalSellerId", ecOrderInfoDTO.getSellerId());
		if (ecOrderInfoDTO.isIfERPDispatch()) {
			jsonmap.put("dispatcher", "02");
		} else {
			jsonmap.put("dispatcher", "01");
		}
		jsonmap.put("ecPlanBillId", ecOrderInfoDTO.getOrderCode());
		//首车到位时间（计划开始时间）
		jsonmap.put("firstArriveTime", sdf.format(ecOrderInfoDTO.getFirstArriveTime()));
		jsonmap.put("shippingMethod", "01");
		if (PickingBillTypeEnum.SELLER_DELIVERY.getCode().equals(ecOrderInfoDTO.getDeliverWay())) {
			jsonmap.put("shippingMethod", "02");
		}
		if (PickingBillTypeEnum.PLATFORM_DELIVERY.getCode().equals(ecOrderInfoDTO.getDeliverWay())) {
			jsonmap.put("shippingMethod", "03");
		}
		jsonmap.put("salemanName", "");
		jsonmap.put("projectName", ecOrderInfoDTO.getProjectName());
		jsonmap.put("signAmount", ecOrderInfoDTO.getSignAmount());
		jsonmap.put("clearName", ecOrderInfoDTO.getClearName());
		//合同地址由电商管理，所以固定传 01:是
		jsonmap.put("ifChangeAddress", "01");
		jsonmap.put("distance", ecOrderInfoDTO.getDistance());
		jsonmap.put("memo", ecOrderInfoDTO.getBuyerMemo());
		//付款方式
		if (CsStringUtils.equals(ChannelCodeEnum.ERP.getCode(), ecOrderInfoDTO.getPayWay())) {
			jsonmap.put("paymentWay", "01");
		} else {
			jsonmap.put("paymentWay", "00");
			//如果是在线支付的平台配送订单，当作自提处理。
			if (PickingBillTypeEnum.PLATFORM_DELIVERY.getCode().equals(ecOrderInfoDTO.getDeliverWay())) {
				jsonmap.put("shippingMethod", "01");
			}
		}
		jsonmap.put("unloadingCountry", ecOrderInfoDTO.getCountryName());
		jsonmap.put("unloadingProvince", ecOrderInfoDTO.getProvinceName());
		jsonmap.put("unloadingCity", ecOrderInfoDTO.getCityName());
		jsonmap.put("unloadingDistrict", ecOrderInfoDTO.getDistrictName());
		jsonmap.put("unloadingStreet", CsStringUtils.isEmpty(ecOrderInfoDTO.getStreetName()) ?
				" ": ecOrderInfoDTO.getStreetName());
		jsonmap.put("unloadingAddress", ecOrderInfoDTO.getAddressDetail());
		jsonmap.put("unloadingCode", ecOrderInfoDTO.getReceiveAddressCode());
		jsonmap.put("contactName", ecOrderInfoDTO.getReceiverName());
		jsonmap.put("contactPhone", ecOrderInfoDTO.getReceiverPhone());
		jsonmap.put("ifMixedTransport", ecOrderInfoDTO.getIfMixedTransport());
		//台班费
		jsonmap.put("classCost", getCentAmount(ecOrderInfoDTO.getMachineShiftCost()));
		//空载费
		jsonmap.put("idlingCost", "0");
		//商品总金额
		jsonmap.put("goodsAmount", getCentAmount(ArithUtils.add(
				ecOrderInfoDTO.getActualResourceAmount(), ecOrderInfoDTO.getActualOthersAmount())));
		//加价项总金额
		jsonmap.put("addItemAmount", getCentAmount(ecOrderInfoDTO.getAddItemAmount()));
		//订单总金额
		jsonmap.put("orderAmount", getCentAmount(ecOrderInfoDTO.getActualOrderAmount()));
		if (CollectionUtils.isEmpty(ecOrderInfoDTO.getOrderItemDTOList())) {
			throw new BizException(BasicCode.PARAM_NULL, "orderItemDTOList");
		}
		//商品列表
		List<JSONObject> goodsList = new ArrayList<>();
		//坍落度从加价项中获取
		String caving = "";
		List<JSONObject> addItemList = new ArrayList<>();
		BigDecimal addItemAmount = BigDecimal.ZERO;
		BigDecimal addItemPrice = BigDecimal.ZERO;
		for (ECOrderItemDTO itemDTO : ecOrderInfoDTO.getOrderItemDTOList()) {
			JSONObject orderItem = new JSONObject();
			orderItem.put("rowNo", "1");
			orderItem.put("commodityCode", itemDTO.getCommodityCode());
			orderItem.put("planBillWeight", String.valueOf(itemDTO.getItemQuantity().setScale(2, BigDecimal.ROUND_DOWN)));
			//超拉数量
			orderItem.put("tolerance", "0");
			orderItem.put("price", getCentAmount(itemDTO.getActualUnitPrice()));
			orderItem.put("unloadingWay", ecOrderInfoDTO.getUnloadingWay());
			orderItem.put("clocation", ecOrderInfoDTO.getConstructPlace());
			orderItem.put("isPipe", "no");
			if (CollectionUtils.isEmpty(ecOrderInfoDTO.getOrderItemAddDTOList())) {
				orderItem.put("addItemList", Lists.newArrayList());
				continue;
			}
			for (ECOrderItemAddDTO itemAddDTO : ecOrderInfoDTO.getOrderItemAddDTOList()) {
				//处理坍落度 这个也是属于加价项
				if (itemAddDTO.getAddItemTypeName() != null && itemAddDTO.getAddItemTypeName().contains("落度")) {
					caving = itemAddDTO.getAddItemName();
					addItemPrice = ArithUtils.add(addItemPrice, new BigDecimal(itemAddDTO.getAddItemPrice()));
					continue;
				}
				if (itemAddDTO.getAddItemTypeName() != null && itemAddDTO.getAddItemTypeName().contains("润管")) {
					continue;
				}

				// 没有ERP加价项代码的不用传到ERP
				if(CsStringUtils.isBlank(itemAddDTO.getAddItemCode()))
				{
					continue;
				}

				JSONObject oItemAddDTO = new JSONObject();
				oItemAddDTO.put("addItemCode", itemAddDTO.getAddItemCode());
				oItemAddDTO.put("addItemName", itemAddDTO.getAddItemName());
				oItemAddDTO.put("addItemPrice", getCentAmount(new BigDecimal(itemAddDTO.getAddItemPrice())));
				oItemAddDTO.put("addItemTypeCode", "");
				oItemAddDTO.put("addItemTypeName", itemAddDTO.getAddItemTypeName());
				addItemList.add(oItemAddDTO);
				addItemPrice = ArithUtils.add(addItemPrice, new BigDecimal(itemAddDTO.getAddItemPrice()));
			}
			addItemAmount = ArithUtils.add(addItemAmount, ArithUtils.multiply(addItemPrice, itemDTO.getItemQuantity()));
			orderItem.put("addItemList", addItemList);
			goodsList.add(orderItem);
		}
		//包含润管砂浆

		if (ecOrderInfoDTO.getLubricityQuantity() != null && ecOrderInfoDTO.getLubricityQuantity().compareTo(BigDecimal.ZERO) == 1) {
			//润管砂浆的物料编码
			String pipeCode = ApiConfigBean.getParamMap().get("pipeCode");
			JSONObject oPipe = new JSONObject();
			oPipe.put("rowNo", "2");
			oPipe.put("relateRowNo", "1");
			oPipe.put("commodityCode", pipeCode);
			oPipe.put("planBillWeight", String.valueOf(ecOrderInfoDTO.getLubricityQuantity().setScale(2, BigDecimal.ROUND_DOWN)));
			oPipe.put("price", getCentAmount(ecOrderInfoDTO.getLubricityPrice()));
			oPipe.put("tolerance", "0");
			oPipe.put("unloadingWay", ecOrderInfoDTO.getUnloadingWay());
			oPipe.put("clocation", ecOrderInfoDTO.getConstructPlace());
			oPipe.put("isPipe", "yes");
			goodsList.add(oPipe);
			// addItemAmount = ArithUtils.add(addItemAmount, ArithUtils.multiply(addItemPrice, ecOrderInfoDTO.getLubricityQuantity()));
		}
		jsonmap.put("addItemAmount", getCentAmount(addItemAmount));
		//设置坍落度，并累加商品单价
		if (!CollectionUtils.isEmpty(goodsList)) {
			for (JSONObject goodsItem : goodsList) {
				goodsItem.put("caving", caving);
//				if (CsStringUtils.equals(goodsItem.getString("isPipe"), "yes")) {
//					goodsItem.put("price", getCentAmount(ArithUtils.add(ecOrderInfoDTO.getLubricityPrice(), cavingPrice)));
//				} else {
//					goodsItem.put("price", getCentAmount(ArithUtils.add(actualUnitPrice, cavingPrice)));
//				}
			}
		}

		jsonmap.put("goodsList", goodsList);

		return jsonmap.toJSONString();
	}

	@Override
	protected String getClientBillNo(ECOrderInfoDTO ecOrderInfoDTO, SysRoute sysRoute, SysConfig SysConfig,
			ApiConfigBean ApiConfigBean) {
		return ecOrderInfoDTO.getOrderCode();
	}

	@Override
	protected String getServerBillNo(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig,
			ApiConfigBean ApiConfigBean) {
		return null;
	}

	@Override
	protected ProcessResult getProcessResult(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig,
			ApiConfigBean ApiConfigBean) {
		if ("01".equals(jsonmap.getString("processCode"))) {
			return ProcessResult.PROCESSING;
		}
		return ProcessResult.FAIL;
	}

	@Override
	protected BaseClientResponseDTO fillClientResponseData(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig,
			ApiConfigBean ApiConfigBean) {

		log.info("创建订单响应: {}", jsonmap.toJSONString());

		if ("01".equals(jsonmap.getString("status")) || "03".equals(jsonmap.getString("status"))) {
			// 重复发起时 erp直接反馈创建结果
			BaseClientResponseDTO baseClientResponseDTO = new BaseClientResponseDTO();
			baseClientResponseDTO.setIfNeedRedoCallback(true);
			return baseClientResponseDTO;
		}

		return null;
	}

	@Override
	protected void setNotNullClientParms(Map<String, String> clientParms, ECOrderInfoDTO ecOrderInfoDTO,
			SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
		clientParms.put("ERP客户编号", ecOrderInfoDTO.getMdmCode());
		clientParms.put("电商单据编号", ecOrderInfoDTO.getOrderCode());
		clientParms.put("配送方式", ecOrderInfoDTO.getDeliverWay());
		clientParms.put("付款方式", ecOrderInfoDTO.getPayWay());
		clientParms.put("卸货地详细地址", ecOrderInfoDTO.getAddressDetail());
		clientParms.put("联系人姓名", ecOrderInfoDTO.getReceiverName());
		clientParms.put("联系人电话", ecOrderInfoDTO.getReceiverPhone());
		clientParms.put("首车到位时间", formatCommDate(ecOrderInfoDTO.getFirstArriveTime()));
		clientParms.put("计划总金额", getCentAmount(ecOrderInfoDTO.getActualOrderAmount()));
		clientParms.put("计划商品总费用", getCentAmount(ecOrderInfoDTO.getActualResourceAmount()));
		List<ECOrderItemDTO> itemDTOS = ecOrderInfoDTO.getOrderItemDTOList();
		if (CollectionUtils.isEmpty(itemDTOS)) {
			throw new BizException(BasicCode.PARAM_NULL, "orderItemDTOList");
		}
		for (int i = 0; i < itemDTOS.size(); i++) {
			ECOrderItemDTO dto = itemDTOS.get(i);
			clientParms.put("商品编码" + i, dto.getCommodityCode());
			if (dto.getItemQuantity() == null) {
				clientParms.put("计划提货量" + i, null);
			}
			if (dto.getActualResUnitPrice() == null) {
				clientParms.put("单价" + i, null);
			}
		}
	}
}
