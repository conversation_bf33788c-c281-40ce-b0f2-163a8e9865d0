package com.ecommerce.open.service.apicenter.biz.transfer.crconcrete;

import com.alibaba.fastjson.JSONObject;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.open.api.dto.apicenter.BaseClientResponseDTO;
import com.ecommerce.open.api.dto.apicenter.cement.ECOrderAdjustPriceCBRequestDTO;
import com.ecommerce.open.api.dto.apicenter.order.ECOrderAdjustPriceDTO;
import com.ecommerce.open.dao.bean.ApiConfigBean;
import com.ecommerce.open.dao.vo.SysConfig;
import com.ecommerce.open.dao.vo.SysRoute;
import com.ecommerce.open.enums.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 *
 * <AUTHOR>
 * Date: Create in 下午2:00 21/7/27
 */
@Slf4j
@Service("concreteECOrderAdjustPriceCallbackTransfer")
public class ConcreteECOrderAdjustPriceCallbackTransfer extends BaseCrconcreteCallbackTransfer<ECOrderAdjustPriceCBRequestDTO, BaseClientResponseDTO, ECOrderAdjustPriceDTO, String> {

    @Override
    public ECOrderAdjustPriceDTO createDefaultFailServerRequestData(JSONObject jsonmap, String serverBillNo) {
        ECOrderAdjustPriceDTO callback = new ECOrderAdjustPriceDTO();
        callback.setAdjustBatchNo(serverBillNo);
        callback.setExecuteStatus(Boolean.FALSE);
        callback.setErrorMessage("执行调价失败");

        return callback;
    }

    @Override
    protected String getServerBillNo(ECOrderAdjustPriceCBRequestDTO ecOrderAdjustPriceCBRequestDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        return ecOrderAdjustPriceCBRequestDTO.getAdjustBatchNo();
    }

    @Override
    protected ProcessResult getProcessResult(ECOrderAdjustPriceCBRequestDTO ecOrderAdjustPriceCBRequestDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        if (ecOrderAdjustPriceCBRequestDTO == null) {
            return ProcessResult.FAIL;
        }

        if (CsStringUtils.equals("01", ecOrderAdjustPriceCBRequestDTO.getCode())) {
            return ProcessResult.SUCCESS;
        }

        return ProcessResult.FAIL;
    }

    @Override
    protected ECOrderAdjustPriceDTO fillServerRequestData(ECOrderAdjustPriceCBRequestDTO ecOrderAdjustPriceCBRequestDTO, JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        if (ecOrderAdjustPriceCBRequestDTO == null) {
            return null;
        }
        ECOrderAdjustPriceDTO callback = new ECOrderAdjustPriceDTO();
        callback.setAdjustBatchNo(ecOrderAdjustPriceCBRequestDTO.getAdjustBatchNo());
        if ("E0B02D15".equals(ecOrderAdjustPriceCBRequestDTO.getCode()) || "E0B02D15".equals(ecOrderAdjustPriceCBRequestDTO.getProcessCode())) {
            if (!CsStringUtils.isNullOrBlank(ecOrderAdjustPriceCBRequestDTO.getProcessMessage())) {
                callback.setErrorMessage(ecOrderAdjustPriceCBRequestDTO.getProcessMessage());
            } else {
                callback.setErrorMessage(ecOrderAdjustPriceCBRequestDTO.getMessage());
            }
            callback.setExecuteStatus(Boolean.FALSE);
        } else {
            callback.setExecuteStatus(Boolean.TRUE);
        }
        return callback;
    }

    @Override
    protected String getClientBillNo(ECOrderAdjustPriceCBRequestDTO ecOrderAdjustPriceCBRequestDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        return ecOrderAdjustPriceCBRequestDTO.getAdjustBatchNo();
    }

    @Override
    protected BaseClientResponseDTO fillClientResponseData(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        return new BaseClientResponseDTO();
    }

    @Override
    protected void setNotNullClientParms(Map<String, String> clientParms, ECOrderAdjustPriceCBRequestDTO ecOrderAdjustPriceCBRequestDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        clientParms.put("响应消息代码", ecOrderAdjustPriceCBRequestDTO.getCode());
        clientParms.put("电商调价批次号", ecOrderAdjustPriceCBRequestDTO.getAdjustBatchNo());
    }
}
