package com.ecommerce.open.service.apicenter.biz.transfer.crconcrete;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.open.api.dto.apicenter.BaseClientResponseDTO;
import com.ecommerce.open.api.dto.apicenter.order.ECOrderAdjustPriceDTO;
import com.ecommerce.open.api.dto.apicenter.order.ECOrderAdjustPriceItemDTO;
import com.ecommerce.open.dao.bean.ApiConfigBean;
import com.ecommerce.open.dao.vo.SysConfig;
import com.ecommerce.open.dao.vo.SysRoute;
import com.ecommerce.open.enums.ProcessResult;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * Date: Create in 上午11:06 21/7/27
 */
@Slf4j
@Service("concreteECOrderAdjustPriceTransfer")
public class ConcreteECOrderAdjustPriceTransfer extends BaseCrconcreteTransfer<ECOrderAdjustPriceDTO, BaseClientResponseDTO, String, String> {

    @Override
    protected String fillServerRequestData(ECOrderAdjustPriceDTO ecOrderAdjustPriceDTO, JSONObject jsonMap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        log.info("ConcreteECOrderAdjustPriceTransfer_ecOrderAdjustPriceDTO:" + JSON.toJSONString(ecOrderAdjustPriceDTO));
        jsonMap.put("adjustBatchNo", ecOrderAdjustPriceDTO.getAdjustBatchNo());
        jsonMap.put("adjustPeriod", ecOrderAdjustPriceDTO.getAdjustPeriod());
        jsonMap.put("adjustExecuteTime", formatCommDate(ecOrderAdjustPriceDTO.getAdjustExecuteTime()));
        jsonMap.put("ecOperatorId", ecOrderAdjustPriceDTO.getEcOperatorId());
        jsonMap.put("ecOperatorName", CsStringUtils.isBlank(ecOrderAdjustPriceDTO.getEcOperatorName()) ? "ec" : ecOrderAdjustPriceDTO.getEcOperatorName());
        List<JSONObject> orderGoodsItemList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(ecOrderAdjustPriceDTO.getList())) {
            ecOrderAdjustPriceDTO.getList().stream().forEach(ecOrderAdjustPriceItemDTO -> {
                JSONObject orderGoodsItem = new JSONObject();
                orderGoodsItem.put("ERPDistributeCode", ecOrderAdjustPriceItemDTO.getERPDistributeCode());
                orderGoodsItem.put("ecPlanBillId", ecOrderAdjustPriceItemDTO.getEcPlanBillId());
                orderGoodsItem.put("commodityCode", ecOrderAdjustPriceItemDTO.getCommodityCode());
                if (ecOrderAdjustPriceItemDTO.getPrePrice() != null) {
                    orderGoodsItem.put("prePrice", getCentAmount(ecOrderAdjustPriceItemDTO.getPrePrice()));
                }
                if (ecOrderAdjustPriceItemDTO.getAfterPrice() != null) {
                    orderGoodsItem.put("afterPrice", getCentAmount(ecOrderAdjustPriceItemDTO.getAfterPrice()));
                }
                orderGoodsItemList.add(orderGoodsItem);
            });
        }
        jsonMap.put("list", orderGoodsItemList);

        log.info("ConcreteECOrderAdjustPriceTransfer_json:" + jsonMap.toJSONString());
        return jsonMap.toJSONString();
    }

    @Override
    protected String getClientBillNo(ECOrderAdjustPriceDTO ecOrderAdjustPriceDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        return ecOrderAdjustPriceDTO.getAdjustBatchNo();
    }

    @Override
    protected String getServerBillNo(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig,
                                     ApiConfigBean ApiConfigBean) {
        return null;
    }

    @Override
    protected ProcessResult getProcessResult(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig,
                                             ApiConfigBean ApiConfigBean) {
        if ("01".equals(jsonmap.getString("processCode"))) {
            return ProcessResult.PROCESSING;
        }
        return ProcessResult.FAIL;
    }

    @Override
    protected BaseClientResponseDTO fillClientResponseData(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig,
                                                           ApiConfigBean ApiConfigBean) {
        if ("01".equals(jsonmap.getString("status")) || "03".equals(jsonmap.getString("status"))) {
            // 重复发起时 erp直接反馈创建结果
            BaseClientResponseDTO baseClientResponseDTO = new BaseClientResponseDTO();
            baseClientResponseDTO.setIfNeedRedoCallback(true);
            return baseClientResponseDTO;
        }

        return null;
    }

    @Override
    protected void setNotNullClientParms(Map<String, String> clientParms, ECOrderAdjustPriceDTO ecOrderAdjustPriceDTO,
                                         SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        clientParms.put("调价计划编号", ecOrderAdjustPriceDTO.getAdjustBatchNo());
        clientParms.put("调价计划期间", ecOrderAdjustPriceDTO.getAdjustPeriod());
        clientParms.put("调价执行时间", formatCommDate(ecOrderAdjustPriceDTO.getAdjustExecuteTime()));
        clientParms.put("操作人ID", ecOrderAdjustPriceDTO.getEcOperatorId());
        List<ECOrderAdjustPriceItemDTO> itemList = ecOrderAdjustPriceDTO.getList();
        if (CollectionUtils.isEmpty(itemList)) {
            throw new BizException(BasicCode.PARAM_NULL, "itemList");
        }
        for (int i = 0; i < itemList.size(); i++) {
            ECOrderAdjustPriceItemDTO dto = itemList.get(i);
            clientParms.put("ERP订单编号" + i, dto.getERPDistributeCode());
            clientParms.put("电商订单编号" + i, dto.getEcPlanBillId());
            clientParms.put("商品编码" + i, dto.getCommodityCode());
            if (dto.getPrePrice() == null) {
                clientParms.put("调价前商品单价" + i, null);
            }
            if (dto.getAfterPrice() == null) {
                clientParms.put("调价后商品单价" + i, null);
            }
        }
    }
}
