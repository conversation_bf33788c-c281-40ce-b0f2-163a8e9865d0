package com.ecommerce.open.service.apicenter.biz.transfer.crconcrete;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ecommerce.open.api.dto.apicenter.goods.ERPGoodsAddItemRequestDTO;
import com.ecommerce.open.api.dto.apicenter.goods.ERPGoodsAdditemResponseDTO;
import com.ecommerce.open.dao.bean.ApiConfigBean;
import com.ecommerce.open.dao.vo.SysConfig;
import com.ecommerce.open.dao.vo.SysRoute;
import com.ecommerce.open.enums.ProcessResult;
import com.google.common.collect.Lists;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service("concreteECQueryAddItemTransfer")
public class ConcreteECQueryAddItemTransfer extends BaseCrconcreteTransfer<ERPGoodsAddItemRequestDTO, List<ERPGoodsAdditemResponseDTO>, String, String> {

    @Override
    protected String fillServerRequestData(ERPGoodsAddItemRequestDTO erpGoodsAddItemRequestDTO,
                                           JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
//        jsonmap.put("externalSellerId", erpGoodsAddItemRequestDTO.getExternalSellerId());
        jsonmap.put("lastUpdateTime", erpGoodsAddItemRequestDTO.getLastUpdateTime());
        return jsonmap.toJSONString(); 
    }

    @Override
    protected String getClientBillNo(ERPGoodsAddItemRequestDTO erpGoodsAddItemRequestDTO,
                                     SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        return null;
    }

    @Override
    protected String getServerBillNo(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig,
                                     ApiConfigBean ApiConfigBean) {
        return null;
    }

    @Override
    protected ProcessResult getProcessResult(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig,
                                             ApiConfigBean ApiConfigBean) {
        if (CsStringUtils.equals(jsonmap.getString("code"), "01")) {
            return ProcessResult.SUCCESS;
        }
        return ProcessResult.FAIL;
    }

    @Override
    protected List<ERPGoodsAdditemResponseDTO> fillClientResponseData(JSONObject jsonmap, SysRoute sysRoute,
                                                                      SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        JSONArray addItemList = jsonmap.getJSONArray("additem");
        if (addItemList == null || addItemList.isEmpty()) {
            return null;
        }
        List<ERPGoodsAdditemResponseDTO> responseDTOList = Lists.newArrayList();
        for (int i = 0; i < addItemList.size(); i++) {
            JSONObject goodsItem = addItemList.getJSONObject(i);
            if (goodsItem == null) {
                continue;
            }
            ERPGoodsAdditemResponseDTO responseDTO = new ERPGoodsAdditemResponseDTO();
            responseDTO.setAddItemCode(goodsItem.getString("addItemCode"));
            responseDTO.setAddItemName(goodsItem.getString("addItemName"));
            responseDTO.setAddItemPrice(goodsItem.getString("addItemPrice"));
            responseDTO.setAddItemTypeCode(goodsItem.getString("addItemTypeCode"));
            responseDTO.setAddItemTypeName(goodsItem.getString("addItemTypeName"));
            responseDTOList.add(responseDTO);
        }
        return responseDTOList;
    }

    @Override
    protected void setNotNullClientParms(Map<String, String> clientParms, ERPGoodsAddItemRequestDTO erpGoodsAddItemRequestDTO,
                                         SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
//        clientParms.put("外部系统卖家编号", erpGoodsMaterialsRequestDTO.getExternalSellerId());
    }
}
