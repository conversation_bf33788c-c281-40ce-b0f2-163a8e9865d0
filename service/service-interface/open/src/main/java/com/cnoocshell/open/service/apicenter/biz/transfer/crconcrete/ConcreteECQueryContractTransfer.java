package com.ecommerce.open.service.apicenter.biz.transfer.crconcrete;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.open.enums.pom.logistics.PickingBillTypeEnum;
import com.ecommerce.open.api.dto.apicenter.contract.*;
import com.ecommerce.open.dao.bean.ApiConfigBean;
import com.ecommerce.open.dao.vo.SysConfig;
import com.ecommerce.open.dao.vo.SysRoute;
import com.ecommerce.open.enums.ProcessResult;
import com.ecommerce.open.service.apicenter.biz.transfer.crcement.BaseCrcementTransfer;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service("concreteECQueryContractTransfer")
public class ConcreteECQueryContractTransfer extends BaseCrcementTransfer<ECContractRequestDTO, ECContractResultDTO, String, String> {

	@Override
	protected String fillServerRequestData(ECContractRequestDTO clientRequest, JSONObject jsonmap, SysRoute sysRoute,
			SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
		jsonmap.put("contractCode", clientRequest.getContractCode());
		return jsonmap.toJSONString();
	}

	@Override
	protected String getClientBillNo(ECContractRequestDTO clientRequest, SysRoute sysRoute, SysConfig SysConfig,
			ApiConfigBean ApiConfigBean) {
		return clientRequest.getContractCode();
	}

	@Override
	protected String getServerBillNo(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig,
			ApiConfigBean ApiConfigBean) {

		return jsonmap.getString("erpContractCode");
	}

	@Override
	protected ProcessResult getProcessResult(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig,
			ApiConfigBean ApiConfigBean) {
		// TODO Auto-generated method stub
		return ProcessResult.SUCCESS;
	}

	@Override
	protected ECContractResultDTO fillClientResponseData(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig,
			ApiConfigBean ApiConfigBean) {
		ECContractResultDTO contractResultDTO = new ECContractResultDTO();
		contractResultDTO = jsonmap.toJavaObject(ECContractResultDTO.class);
		// ECContractGoodsResultDTO
		if(!CsStringUtils.isNullOrBlank(contractResultDTO.getDeliveryWay())) {
			if("1".equals(contractResultDTO.getDeliveryWay()) || "2".equals(contractResultDTO.getDeliveryWay())) {
				contractResultDTO.setDeliveryWay(PickingBillTypeEnum.BUYER_TAKE.getCode());
			}

			if("4".equals(contractResultDTO.getDeliveryWay())) {
				contractResultDTO.setDeliveryWay(PickingBillTypeEnum.SELLER_DELIVERY.getCode());
			}
		}
		contractResultDTO.setOrgCode(contractResultDTO.getParentOrgCode() + "/" + contractResultDTO.getOrgCode());
		JSONArray goods = jsonmap.getJSONArray("goodsList");
		if (goods != null) {
			contractResultDTO.setContractGoodsList(goods.toJavaList(ECContractGoodsResultDTO.class));
		}
		JSONArray additem = jsonmap.getJSONArray("additemList");
		if (additem != null) {
			contractResultDTO.setContractAddItemList(additem.toJavaList(ECContractAddItemResultDTO.class));
		}
		JSONObject payInfo = jsonmap.getJSONObject("payInfo");
		if (payInfo != null) {
			ECContractPayInfoResultDTO ecContractPayInfoResultDTO = payInfo.toJavaObject(ECContractPayInfoResultDTO.class);
			JSONArray payInfoDetail = jsonmap.getJSONObject("payInfo").getJSONArray("payDtailList");
			if (payInfoDetail != null) {
				ecContractPayInfoResultDTO.setContractPayInfoDetail(payInfoDetail.toJavaList(ECContractPayInfoDetailResultDTO.class));
			}
			contractResultDTO.setContractPayInfo(ecContractPayInfoResultDTO);
		}
//		JSONObject advanceDetail = jsonmap.getJSONObject("dpayDtailList");
//		if (advanceDetail != null) {
//			contractResultDTO.setContractAdvanceDetail(advanceDetail.toJavaObject(ECContractAdvanceDetailResultDTO.class));
//		}
//		JSONObject creditClause = jsonmap.getJSONObject("creditList");
//		if (creditClause != null) {
//			contractResultDTO.setContractCreditClause(creditClause.toJavaObject(ECContractCreditClauseResultDTO.class));
//		}

		return contractResultDTO;
	}
 
	@Override
	protected void setNotNullClientParms(Map<String, String> clientParms, ECContractRequestDTO clientRequest,
			SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
		clientParms.put("ERP合同编号", clientRequest.getContractCode());

	}

}
