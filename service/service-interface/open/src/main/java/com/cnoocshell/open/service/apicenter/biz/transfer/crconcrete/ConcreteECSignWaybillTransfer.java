package com.ecommerce.open.service.apicenter.biz.transfer.crconcrete;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ecommerce.common.utils.ArithUtils;
import com.ecommerce.open.api.dto.apicenter.logistic.ECWaybillDTO;
import com.ecommerce.open.api.dto.apicenter.logistic.ECWaybillItemDTO;
import com.ecommerce.open.dao.bean.ApiConfigBean;
import com.ecommerce.open.dao.vo.SysConfig;
import com.ecommerce.open.dao.vo.SysRoute;
import com.ecommerce.open.enums.ProcessResult;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Slf4j
@Service("concreteECSignWaybillTransfer")
public class ConcreteECSignWaybillTransfer extends BaseCrconcreteTransfer<ECWaybillDTO, ECWaybillDTO, String, String> {

    @Override
    protected String fillServerRequestData(ECWaybillDTO ecWaybillDTO, JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        log.info("ConcreteECSignWaybillTransfer_ecWaybillDTO:" + JSON.toJSONString(ecWaybillDTO));
        jsonmap.put("ecBillCode", ecWaybillDTO.getWaybillNum());
        jsonmap.put("externalBillId", ecWaybillDTO.getErpWaybillNum());
        jsonmap.put("signer", ecWaybillDTO.getSigner());
        jsonmap.put("signerPhone", ecWaybillDTO.getSignerPhone());
        jsonmap.put("driverName", ecWaybillDTO.getDriverName());
        jsonmap.put("driverPhone", ecWaybillDTO.getDriverPhone());
        jsonmap.put("pumpNo", ecWaybillDTO.getPumpNo());
        jsonmap.put("pumperName", ecWaybillDTO.getPumperName());
        jsonmap.put("arriveTime", formatCommDate(ecWaybillDTO.getArriveTime()));
        jsonmap.put("unloadingTime", formatCommDate(ecWaybillDTO.getCompleteTime()));
        jsonmap.put("realDistance", ecWaybillDTO.getRealDistance());
        List<ECWaybillItemDTO> goodsList = ecWaybillDTO.getWaybillItemList();
        if (!CollectionUtils.isEmpty(goodsList)) {
            List<JSONObject> goodsItemList = Lists.newArrayList();
            JSONObject goodsItem = new JSONObject();
            ECWaybillItemDTO waybillItemDTO = goodsList.get(0);
            goodsItem.put("rowNo", "0");
            goodsItem.put("commodityCode", waybillItemDTO.getCommodityCode());
            if (waybillItemDTO.getQuantity() != null) {
                goodsItem.put("billWeight", waybillItemDTO.getQuantity().setScale(2, BigDecimal.ROUND_HALF_UP));
            }
            if (waybillItemDTO.getActualQuantity() != null) {
                goodsItem.put("leaveWeight", waybillItemDTO.getActualQuantity().setScale(2, BigDecimal.ROUND_HALF_UP));
            }
            if (waybillItemDTO.getSignQuantity() != null) {
                BigDecimal signQuantity =  ArithUtils.subtract(waybillItemDTO.getSignQuantity(), ecWaybillDTO.getLubricityQuantity());
                goodsItem.put("signWeight", signQuantity.toPlainString());
                goodsItem.put("realGoodsAmount", getCentAmount(
                        ArithUtils.multiply(signQuantity, new BigDecimal(waybillItemDTO.getRealUnitPrice()))));
            }
            goodsItem.put("realUnitPrice", getCentAmount(waybillItemDTO.getRealUnitPrice()));
            goodsItem.put("realIdlingAmount", getCentAmount(waybillItemDTO.getEmptyLoadCharge()));
            if (waybillItemDTO.getEmptyLoadQuantity() != null) {
                goodsItem.put("emptyLoadQuantity", waybillItemDTO.getEmptyLoadQuantity().toPlainString());
            }
            goodsItem.put("extend2", ecWaybillDTO.getSignRemark());
            goodsItemList.add(goodsItem);
            if (ecWaybillDTO.getLubricityQuantity() != null) {
                JSONObject pipeItem = new JSONObject();
                pipeItem.put("rowNo", "1");
                Map<String, String> paramMap = ApiConfigBean.getParamMap();
                pipeItem.put("commodityCode", paramMap == null ? "" : paramMap.get("pipeCode"));
                pipeItem.put("billWeight", ecWaybillDTO.getLubricityQuantity().toPlainString());
                pipeItem.put("leaveWeight", ecWaybillDTO.getLubricityQuantity().toPlainString());
                if (waybillItemDTO.getSignQuantity().compareTo(ecWaybillDTO.getLubricityQuantity()) < 1) {
                    pipeItem.put("signWeight", waybillItemDTO.getSignQuantity().toPlainString());
                    pipeItem.put("realGoodsAmount", getCentAmount(ArithUtils.multiply(
                            waybillItemDTO.getSignQuantity(), ecWaybillDTO.getLubricityPrice())));
                } else {
                    pipeItem.put("signWeight", ecWaybillDTO.getLubricityQuantity().toPlainString());
                    pipeItem.put("realGoodsAmount", getCentAmount(ArithUtils.multiply(
                            ecWaybillDTO.getLubricityQuantity(), ecWaybillDTO.getLubricityPrice())));
                }
                pipeItem.put("realUnitPrice", getCentAmount(ecWaybillDTO.getLubricityPrice()));
                pipeItem.put("realIdlingAmount", "0");
                //润管砂浆数量和运单数量相等(清除本身的签收量)
                if (waybillItemDTO.getQuantity().compareTo(ecWaybillDTO.getLubricityQuantity()) == 0) {
                    pipeItem.put("realIdlingAmount", getCentAmount(waybillItemDTO.getEmptyLoadCharge()));
                    if (waybillItemDTO.getEmptyLoadQuantity() != null) {
                        pipeItem.put("emptyLoadQuantity", waybillItemDTO.getEmptyLoadQuantity().toPlainString());
                    }
                    goodsItemList.clear();
                }
                pipeItem.put("extend2", ecWaybillDTO.getSignRemark());
                goodsItemList.add(pipeItem);
            }
            jsonmap.put("goodsList", goodsItemList);
        }

        log.info("ConcreteECSignWaybillTransfer_json:" + jsonmap.toJSONString());

        return jsonmap.toJSONString();
    }

    @Override
    protected String getClientBillNo(ECWaybillDTO ecWaybillDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        return ecWaybillDTO.getWaybillNum();
    }

    @Override
    protected String getServerBillNo(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        return null;
    }

    @Override
    protected ProcessResult getProcessResult(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        if ("01".equals(jsonmap.getString("processCode"))) {
            return ProcessResult.SUCCESS;
        }
        return ProcessResult.FAIL;
    }

    @Override
    protected ECWaybillDTO fillClientResponseData(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        ECWaybillDTO resultDTO = new ECWaybillDTO();
        resultDTO.setRequestNo(jsonmap.getString("requestNo"));
        return resultDTO;
    }

    @Override
    protected void setNotNullClientParms(Map<String, String> clientParms, ECWaybillDTO ecWaybillDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        clientParms.put("电商运单号", ecWaybillDTO.getWaybillNum());
        clientParms.put("erp送货单号", ecWaybillDTO.getErpWaybillNum());
        clientParms.put("签收人", ecWaybillDTO.getSigner());
        clientParms.put("签收电话", ecWaybillDTO.getSignerPhone());
        clientParms.put("司机名称", ecWaybillDTO.getDriverName());
//        clientParms.put("司机电话", ecWaybillDTO.getDriverPhone());
        clientParms.put("到达卸货点时间", formatCommDate(ecWaybillDTO.getArriveTime()));
        clientParms.put("卸货完成时间", formatCommDate(ecWaybillDTO.getCompleteTime()));
        clientParms.put("实际运距", ecWaybillDTO.getRealDistance());
        List<ECWaybillItemDTO> goodsList = ecWaybillDTO.getWaybillItemList();
        if (CollectionUtils.isEmpty(goodsList)) {
            clientParms.put("配送商品", null);
            return;
        }
        ECWaybillItemDTO waybillItemDTO = goodsList.get(0);
        clientParms.put("商品物料编码", waybillItemDTO.getCommodityCode());
        if (waybillItemDTO.getQuantity() == null) {
            clientParms.put("开单量", null);
        }
        if (waybillItemDTO.getActualQuantity() == null) {
            clientParms.put("出厂量", null);
        }
        if (waybillItemDTO.getSignQuantity() == null) {
            clientParms.put("签收量", null);
        }
        clientParms.put("实际商品金额", waybillItemDTO.getRealCostAmount());
        clientParms.put("执行商品单价", waybillItemDTO.getRealUnitPrice());
    }
}
