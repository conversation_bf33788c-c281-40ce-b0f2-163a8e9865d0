package com.ecommerce.open.service.apicenter.biz.transfer.crconcrete;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ecommerce.open.api.dto.apicenter.base.ERPSaleRegionOptionDTO;
import com.ecommerce.open.api.dto.apicenter.base.ERPSaleRegionRequestDTO;
import com.ecommerce.open.api.dto.apicenter.pay.ERPBalanceQueryDTO;
import com.ecommerce.open.api.dto.apicenter.pay.ERPBalanceResultDTO;
import com.ecommerce.open.dao.bean.ApiConfigBean;
import com.ecommerce.open.dao.vo.SysConfig;
import com.ecommerce.open.dao.vo.SysRoute;
import com.ecommerce.open.enums.ProcessResult;
import com.ecommerce.open.service.erp.biz.IERPSaleRegionBiz;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Slf4j
@Service("concreteERPBalanceQueryTransfer")
public class ConcreteERPBalanceQueryTransfer
		extends BaseCrconcreteTransfer<ERPBalanceQueryDTO, ERPBalanceResultDTO, String, String> {

	@Autowired
	private IERPSaleRegionBiz erpSaleRegionBiz;

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.ecommerce.open.service.apicenter.biz.transfer.BaseDataTransfer#
	 * setNotNullClientParms(java.util.Map, java.lang.Object,
	 * com.ecommerce.open.dao.vo.SysRoute, com.ecommerce.open.dao.vo.SysConfig,
	 * com.ecommerce.open.dao.bean.ApiConfigBean)
	 */
	@Override
	protected void setNotNullClientParms(Map<String, String> clientParms, ERPBalanceQueryDTO clientRequest,
			SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
		clientParms.put("会员编码", clientRequest.getMemberMDMCode());
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.ecommerce.open.service.apicenter.biz.transfer.crcement.BaseCrcementTransfer#
	 * fillServerRequestData(java.lang.Object, com.alibaba.fastjson.JSONObject,
	 * com.ecommerce.open.dao.vo.SysRoute, com.ecommerce.open.dao.vo.SysConfig,
	 * com.ecommerce.open.dao.bean.ApiConfigBean)
	 */
	@Override
	protected String fillServerRequestData(ERPBalanceQueryDTO clientRequest, JSONObject jsonmap, SysRoute sysRoute,
			SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
		//ERP余额不区分区域，授信方式为合同授信，所以只需ERP合同编号即可
		jsonmap.put("mdmCode", getDataNotNull(clientRequest.getMemberMDMCode(), "会员编码"));
		jsonmap.put("erpContractCode", clientRequest.getErpContractCode());
		return jsonmap.toJSONString();
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.ecommerce.open.service.apicenter.biz.transfer.crcement.BaseCrcementTransfer#
	 * fillClientResponseData(com.alibaba.fastjson.JSONObject,
	 * com.ecommerce.open.dao.vo.SysRoute, com.ecommerce.open.dao.vo.SysConfig,
	 * com.ecommerce.open.dao.bean.ApiConfigBean)
	 */
	@Override
	protected ERPBalanceResultDTO fillClientResponseData(JSONObject responseJson, SysRoute sysRoute,
			SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
		log.info("concreteERPBalanceQueryTransfer_responseJson:" + JSON.toJSONString(responseJson));
		JSONArray balanceList = responseJson.getJSONArray("balanceList");
		ERPBalanceResultDTO result = new ERPBalanceResultDTO();
		result.setBalance("0");
		result.setCredit("0");
		result.setBaceBalance("0");
		if(balanceList != null && !balanceList.isEmpty()) {
			for(Object balanceObj : balanceList.toArray()) {
				JSONObject balanceJson = JSONObject.parseObject(String.valueOf(balanceObj));
				String balance = balanceJson.getString("balance");
				String credit = balanceJson.getString("availableCredit");
				String baceBalance = balanceJson.getString("availableCredit");
				if (isEmpty(balance)) {
					balance = "0";
				}
				if (isEmpty(credit)) {
					credit = "0";
				}
				if (isEmpty(baceBalance)) {
					baceBalance = "0";
				}
				result.setBalance(new BigDecimal(result.getBalance()).add(this.getYuan(balance)).toPlainString());
				result.setCredit(new BigDecimal(result.getCredit()).add(this.getYuan(credit)).toPlainString());
				result.setBaceBalance(new BigDecimal(result.getBaceBalance()).add(this.getYuan(baceBalance)).toPlainString());
			}
		}
		return result;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.ecommerce.open.service.apicenter.biz.transfer.crcement.BaseCrcementTransfer#
	 * getClientBillNo(java.lang.Object, com.ecommerce.open.dao.vo.SysRoute,
	 */
	@Override
	protected String getClientBillNo(ERPBalanceQueryDTO clientRequest, SysRoute sysRoute, SysConfig SysConfig,
			ApiConfigBean ApiConfigBean) {
		return null;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.ecommerce.open.service.apicenter.biz.transfer.crcement.BaseCrcementTransfer#
	 * getServerBillNo(com.alibaba.fastjson.JSONObject,
	 * com.ecommerce.open.dao.vo.SysRoute, com.ecommerce.open.dao.vo.SysConfig,
	 * com.ecommerce.open.dao.bean.ApiConfigBean)
	 */
	@Override
	protected String getServerBillNo(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig,
			ApiConfigBean ApiConfigBean) {
		return null;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.ecommerce.open.service.apicenter.biz.transfer.crcement.BaseCrcementTransfer#
	 * getProcessResult(com.alibaba.fastjson.JSONObject,
	 * com.ecommerce.open.dao.vo.SysRoute, com.ecommerce.open.dao.vo.SysConfig,
	 * com.ecommerce.open.dao.bean.ApiConfigBean)
	 */
	@Override
	protected ProcessResult getProcessResult(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig,
			ApiConfigBean ApiConfigBean) {
		if ("01".equals(jsonmap.getString("processCode"))) {
			return ProcessResult.SUCCESS;
		}
		return ProcessResult.FAIL;
	}

}
