package com.ecommerce.open.service.apicenter.biz.transfer.crconcrete;

import com.alibaba.fastjson.JSONObject;
import com.ecommerce.open.api.dto.apicenter.BaseClientResponseDTO;
import com.ecommerce.open.api.dto.apicenter.cement.logistics.ERPCloseWaybillRequestDTO;
import com.ecommerce.open.api.dto.pom.logistics.ERPWaybillDTO;
import com.ecommerce.open.dao.bean.ApiConfigBean;
import com.ecommerce.open.dao.vo.SysConfig;
import com.ecommerce.open.dao.vo.SysRoute;
import com.ecommerce.open.enums.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

@Slf4j
@Service("concreteERPCloseWaybillTransfer")
public class ConcreteERPCloseWaybillTransfer extends BaseCrconcreteTransfer<ERPCloseWaybillRequestDTO, BaseClientResponseDTO, ERPWaybillDTO, String> {

    @Override
    protected ERPWaybillDTO fillServerRequestData(ERPCloseWaybillRequestDTO erpCloseWaybillRequestDTO, JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        ERPWaybillDTO waybillDTO = new ERPWaybillDTO();
        waybillDTO.setErpWaybillNum(erpCloseWaybillRequestDTO.getExternalBillId());
        waybillDTO.setWaybillNum(erpCloseWaybillRequestDTO.getEcBillCode());
        waybillDTO.setCloseReason(erpCloseWaybillRequestDTO.getMemo());

        return waybillDTO;
    }

    @Override
    protected String getClientBillNo(ERPCloseWaybillRequestDTO erpCloseWaybillRequestDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        return erpCloseWaybillRequestDTO.getExternalBillId();
    }

    @Override
    protected String getServerBillNo(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        return null;
    }

    @Override
    protected ProcessResult getProcessResult(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        if ("01".equals(jsonmap.getString("processCode"))) {
            return ProcessResult.PROCESSING;
        }
        return ProcessResult.FAIL;
    }

    @Override
    protected BaseClientResponseDTO fillClientResponseData(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        BaseClientResponseDTO responseDTO = new BaseClientResponseDTO();
        if (jsonmap != null && CsStringUtils.equals(jsonmap.getString("success"), "true")) {
            responseDTO.setCode("01");
            responseDTO.setMessage("电商处理成功");
        } else {
            responseDTO.setCode("00");
            responseDTO.setMessage("电商处理失败");
        }
        return responseDTO;
    }

    @Override
    protected void setNotNullClientParms(Map<String, String> clientParms, ERPCloseWaybillRequestDTO erpCloseWaybillRequestDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        clientParms.put("erp送货单号", erpCloseWaybillRequestDTO.getExternalBillId());
        clientParms.put("电商运单号", erpCloseWaybillRequestDTO.getEcBillCode());
    }
}
