package com.ecommerce.open.service.apicenter.biz.transfer.crconcrete;

import com.alibaba.fastjson.JSONObject;
import com.ecommerce.open.api.dto.apicenter.BaseClientResponseDTO;
import com.ecommerce.open.api.dto.apicenter.cement.ERPModifyOrderRequestDTO;
import com.ecommerce.open.dao.bean.ApiConfigBean;
import com.ecommerce.open.dao.vo.SysConfig;
import com.ecommerce.open.dao.vo.SysRoute;
import com.ecommerce.open.enums.ProcessResult;
import com.ecommerce.open.service.apicenter.biz.transfer.crcement.BaseCrcementTransfer;
import com.ecommerce.open.api.dto.pom.order.OrderCompleteERPCallback;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

@Deprecated(since = "2.1.4-RELEASE")
@Service("concreteERPCompleteOrderTransfer")
public class ConcreteERPCompleteOrderTransfer extends BaseCrconcreteTransfer<ERPModifyOrderRequestDTO, BaseClientResponseDTO, OrderCompleteERPCallback, String> {

    @Override
    protected OrderCompleteERPCallback fillServerRequestData(ERPModifyOrderRequestDTO erpModifyOrderRequestDTO, JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        OrderCompleteERPCallback callback = new OrderCompleteERPCallback();
        callback.setEcPlanBillId(erpModifyOrderRequestDTO.getEcPlanBillId());
        callback.setERPDistributeCode(erpModifyOrderRequestDTO.getERPDistributeCode());
        callback.setErrorMessage(erpModifyOrderRequestDTO.getCloseReason());
        return callback;
    }

    @Override
    protected String getClientBillNo(ERPModifyOrderRequestDTO erpModifyOrderRequestDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        return erpModifyOrderRequestDTO.getERPDistributeCode();
    }

    @Override
    protected String getServerBillNo(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        return null;
    }

    @Override
    protected ProcessResult getProcessResult(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        if (jsonmap != null && CsStringUtils.equals(jsonmap.getString("success"), "true")) {
            return ProcessResult.SUCCESS;
        }
        return ProcessResult.FAIL;
    }

    @Override
    protected BaseClientResponseDTO fillClientResponseData(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        BaseClientResponseDTO responseDTO = new BaseClientResponseDTO();
        if (jsonmap != null && CsStringUtils.equals(jsonmap.getString("success"), "true")) {
            responseDTO.setCode("01");
            responseDTO.setMessage("接收成功");
        } else {
            responseDTO.setCode("00");
            responseDTO.setMessage("接收失败");
        }
        return responseDTO;
    }

    @Override
    protected void setNotNullClientParms(Map<String, String> clientParms, ERPModifyOrderRequestDTO erpModifyOrderRequestDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        clientParms.put("电商单据编号", erpModifyOrderRequestDTO.getEcPlanBillId());
        clientParms.put("erp提货计划编号", erpModifyOrderRequestDTO.getERPDistributeCode());
    }
}
