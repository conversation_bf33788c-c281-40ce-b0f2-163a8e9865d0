package com.ecommerce.open.service.apicenter.biz.transfer.crconcrete;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.utils.ArithUtils;
import com.ecommerce.open.api.dto.apicenter.cement.logistics.ERPConcreteGoodsItem;
import com.ecommerce.open.api.dto.apicenter.cement.logistics.ERPCreateConcreteRequestDTO;
import com.ecommerce.open.api.dto.apicenter.logistic.ERPWayBillResponseDTO;
import com.ecommerce.open.api.dto.pom.logistics.ERPPumpItemDTO;
import com.ecommerce.open.api.dto.pom.logistics.ERPVehicleDTO;
import com.ecommerce.open.api.dto.pom.logistics.ERPWaybillDTO;
import com.ecommerce.open.api.dto.pom.logistics.ERPWaybillItemDTO;
import com.ecommerce.open.dao.bean.ApiConfigBean;
import com.ecommerce.open.dao.vo.SysConfig;
import com.ecommerce.open.dao.vo.SysRoute;
import com.ecommerce.open.enums.ProcessResult;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Slf4j
@Service("concreteERPCreateWaybillTransfer")
public class ConcreteERPCreateWaybillTransfer extends BaseCrconcreteTransfer<ERPCreateConcreteRequestDTO, ERPWayBillResponseDTO, ERPWaybillDTO, ItemResult<String>> {

    @Override
    protected ERPWaybillDTO fillServerRequestData(ERPCreateConcreteRequestDTO erpCreateConcreteRequestDTO, JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        log.info("ConcreteERPCreateWaybillTransfer_erpCreateConcreteRequestDTO:" + JSON.toJSONString(erpCreateConcreteRequestDTO));
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        ERPWaybillDTO waybillDTO = new ERPWaybillDTO();
        waybillDTO.setErpWaybillNum(erpCreateConcreteRequestDTO.getExternalBillId());
        waybillDTO.setOrderCode(erpCreateConcreteRequestDTO.getEcOrderCode());
        waybillDTO.setErpOrderCode(erpCreateConcreteRequestDTO.getErpOrderCode());
        waybillDTO.setOperateType(erpCreateConcreteRequestDTO.getOperateType());
        waybillDTO.setExternalStatus(erpCreateConcreteRequestDTO.getExternalStatus());
        if (CsStringUtils.isNotBlank(erpCreateConcreteRequestDTO.getSendTime())) {
            try {
                waybillDTO.setDeliveryTime(sdf.parse(erpCreateConcreteRequestDTO.getSendTime()));
            } catch (ParseException e) {
                log.error("转换发送时间异常:{}", erpCreateConcreteRequestDTO.getSendTime(), e);
            }
        }
        waybillDTO.setOrderCode(erpCreateConcreteRequestDTO.getEcOrderCode());
        waybillDTO.setErpOrderCode(erpCreateConcreteRequestDTO.getErpOrderCode());
        waybillDTO.setVehicleNum(erpCreateConcreteRequestDTO.getVehicleNo());
        if (CsStringUtils.equals("Y", erpCreateConcreteRequestDTO.getComputeEmpty())) {
            waybillDTO.setEmptyLoadFlag((byte) 1);
        } else {
            waybillDTO.setEmptyLoadFlag((byte) 0);
        }
        waybillDTO.setIfMixedTransport(CsStringUtils.equals("Y", erpCreateConcreteRequestDTO.getIfMixedTransport()) ?
                Boolean.TRUE : Boolean.FALSE);
        //车辆信息
        ERPVehicleDTO vehicleDTO = new ERPVehicleDTO();
        vehicleDTO.setNumber(erpCreateConcreteRequestDTO.getVehicleNo());
        vehicleDTO.setGpsId(erpCreateConcreteRequestDTO.getGpsManufacturerId());
        vehicleDTO.setGpsDeviceNumber(erpCreateConcreteRequestDTO.getGpsDeviceNumber());
        vehicleDTO.setDriverName(CsStringUtils.isBlank(erpCreateConcreteRequestDTO.getDriverName()) ?
                "司机" : erpCreateConcreteRequestDTO.getDriverName());
        vehicleDTO.setDriverPhone(erpCreateConcreteRequestDTO.getDriverPhone());
        waybillDTO.setErpVehicleDTO(vehicleDTO);
        //商品信息
        Map<String, String> paramMap = ApiConfigBean.getParamMap();
        List<ERPWaybillItemDTO> waybillItemList = Lists.newArrayList();
        for (ERPConcreteGoodsItem erpConcreteGoodsItem : erpCreateConcreteRequestDTO.getGoodsList()) {
            ERPWaybillItemDTO itemDTO = new ERPWaybillItemDTO();
            itemDTO.setCommodityCode(erpConcreteGoodsItem.getCommodityCode());
            itemDTO.setQuantity(erpConcreteGoodsItem.getPlanBillWeight());
            if (paramMap != null && CsStringUtils.equals(paramMap.get("pipeCode"), erpConcreteGoodsItem.getCommodityCode())) {
                waybillDTO.setLubricityQuantity(erpConcreteGoodsItem.getPlanBillWeight());
                waybillDTO.setLubricityPrice(getYuan(erpConcreteGoodsItem.getRealUnitPrice()));
                //如果是润管砂浆，则需要替换为物料编码
                itemDTO.setCommodityCode(erpConcreteGoodsItem.getGoodsCommodityCode());
            }
            waybillItemList.add(itemDTO);
        }
//        //混合运输(运单出厂量＝＋润管砂浆)
//        if (waybillDTO.getIfMixedTransport() && erpCreateConcreteRequestDTO.getGoodsList().size() > 1) {
//            waybillItemList.get(0).setQuantity(ArithUtils.add(waybillDTO.getLubricityQuantity(), waybillItemList.get(0).getQuantity()));
//        }
        waybillDTO.setWaybillItemList(waybillItemList);
        //泵送设备
        if (CollectionUtils.isNotEmpty(erpCreateConcreteRequestDTO.getPumpList())) {
            List<ERPPumpItemDTO> pumpItemList = Lists.newArrayList();
            erpCreateConcreteRequestDTO.getPumpList().stream().forEach(erpPumpItemDTO -> {
                ERPPumpItemDTO pumpItemDTO = new ERPPumpItemDTO();
                BeanUtils.copyProperties(erpPumpItemDTO, pumpItemDTO);
                pumpItemList.add(pumpItemDTO);
            });
            waybillDTO.setPumpList(pumpItemList);
        }

        return waybillDTO;
    }

    @Override
    protected String getClientBillNo(ERPCreateConcreteRequestDTO erpCreateConcreteRequestDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        return erpCreateConcreteRequestDTO.getExternalBillId();
    }

    @Override
    protected String getServerBillNo(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        return null;
    }

    @Override
    protected ProcessResult getProcessResult(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        if ("01".equals(jsonmap.getString("processCode"))) {
            return ProcessResult.PROCESSING;
        }
        return ProcessResult.FAIL;
    }

    @Override
    protected ERPWayBillResponseDTO fillClientResponseData(JSONObject jsonmap, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        ERPWayBillResponseDTO responseDTO = new ERPWayBillResponseDTO();
        if (jsonmap != null && CsStringUtils.equals(jsonmap.getString("success"), "true")) {
            responseDTO.setCode("01");
            responseDTO.setMessage("接收成功");
        } else {
            responseDTO.setCode("00");
            responseDTO.setMessage("接收失败");
        }
        responseDTO.setEcBillCode(jsonmap.getString("data"));
        responseDTO.setEcChangeDate(this.formatCommDate(new Date()));
        return responseDTO;
    }

    @Override
    protected void setNotNullClientParms(Map<String, String> clientParms, ERPCreateConcreteRequestDTO erpCreateConcreteRequestDTO, SysRoute sysRoute, SysConfig SysConfig, ApiConfigBean ApiConfigBean) {
        clientParms.put("ERP送货单号", erpCreateConcreteRequestDTO.getExternalBillId());
        clientParms.put("交货单状态", erpCreateConcreteRequestDTO.getExternalStatus());
        clientParms.put("操作类型", erpCreateConcreteRequestDTO.getOperateType());
        clientParms.put("送货日期", erpCreateConcreteRequestDTO.getSendTime());
        clientParms.put("电商订单号", erpCreateConcreteRequestDTO.getEcOrderCode());
        clientParms.put("ERP订单号", erpCreateConcreteRequestDTO.getErpOrderCode());
        clientParms.put("车牌号", erpCreateConcreteRequestDTO.getVehicleNo());
        clientParms.put("司机名称", erpCreateConcreteRequestDTO.getDriverName());
        clientParms.put("是否混合运输", erpCreateConcreteRequestDTO.getIfMixedTransport());
        clientParms.put("是否计算空载费", erpCreateConcreteRequestDTO.getComputeEmpty());
        if (CollectionUtils.isEmpty(erpCreateConcreteRequestDTO.getGoodsList())) {
            throw new BizException(BasicCode.PARAM_NULL, "goodsList");
        }
        for (ERPConcreteGoodsItem goodsItemDTO : erpCreateConcreteRequestDTO.getGoodsList()) {
            if (CsStringUtils.isBlank(goodsItemDTO.getCommodityCode())) {
                clientParms.put("物料编码", null);
            }
            if (goodsItemDTO.getPlanBillWeight() == null) {
                clientParms.put("送货数量", null);
            }
        }
    }
}
