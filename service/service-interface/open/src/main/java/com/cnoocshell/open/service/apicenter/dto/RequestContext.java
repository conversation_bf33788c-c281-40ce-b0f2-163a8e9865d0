package com.ecommerce.open.service.apicenter.dto;

import com.ecommerce.open.dao.bean.ApiConfigBean;
import com.ecommerce.open.dao.vo.SysConfig;
import com.ecommerce.open.dao.vo.SysRoute;
import com.ecommerce.open.service.apicenter.biz.IDataTransfer;

import lombok.Data;

/**
 * @ClassName: RequestContext
 * @Description: TODO(这里用一句话描述这个类的作用)
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class RequestContext {
	/**
	 * 请求信息
	 */
	private RequestInfo requestInfo;
	/**
	 * 请求报文
	 */
	private RequestMessage requestMessage;
	/**
	 * 响应信息
	 */
	private ResponseInfo responseInfo;
	/**
	 * 响应报文
	 */
	private ResponseMessage responseMessage;

	/**
	 * 路由配置
	 */
	private SysRoute sysRoute;
	/**
	 * api配置
	 */
	private ApiConfigBean ApiConfigBean;
	/**
	 * 系统配置
	 */
	private SysConfig SysConfig;

	/**
	 * 
	 */
	private String bizCode;

	/**
	 * 请求号
	 */
	private String requestNo;

	/**
	 * 原请求号
	 */
	private String baseRequestNo;

	/**
	 * 
	 */
	private String sellerId;

	/**
	 * 
	 */
	private Object clientData;

	/**
	 * 
	 */
	private Object clientDataDTO;

	/**
	 * 
	 */
	private IDataTransfer dataTransfer;

	/**
	 * 客户端单据号
	 */
	private String clientBillNo;

	/**
	 * 服务端单据号
	 */
	private String serverBillNo;

	/**
	 * 
	 */
	private Object clientRequestDTO;

	/**
	 * 
	 */
	private Object clientResponseDTO;

	private String operator;

	private String operatorName;

	/**
	 * 是否是请求失败后接口中心自动回调
	 */
	private boolean ifRequestFailCallBack = false;

	/**
	 * 接口中心是否直接回调成功
	 */
	private boolean ifRequestSuccessCallBack = false;
	/**
	 * 错误编码
	 */
	private String code;

	/**
	 * 错误信息
	 */
	private String msg;
	
	//标识在重复发起请求时，如果对方系统已经处理成功，则自动转为回调成功逻辑
	private boolean ifNeedRedoCallback;
	
	private String xmessage;
	
	private String outBizCode;

	/**
	 * 后续补充:发送报文体明文
	 */
	private String body;

	/**
	 * 后续补充:响应报文体明文
	 */
	private String responseBody;

}
