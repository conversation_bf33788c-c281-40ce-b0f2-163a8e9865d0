package com.ecommerce.open.service.apicenter.dto;

import java.util.Map;


import lombok.Data;

/**
 * @ClassName: ServerRequestInfo
 * @Description: 服务请求
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class RequestMessage {
	private Map<String, String> headerParms;
	private Object body;
	private String url;

	public Map<String, String> getHeaderParms() {
		return headerParms;
	}

	public void setHeaderParms(Map<String, String> headerParms) {
		this.headerParms = headerParms;
	}

	public Object getBody() {
		return body;
	}

	public void setBody(Object body) {
		this.body = body;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}


}
