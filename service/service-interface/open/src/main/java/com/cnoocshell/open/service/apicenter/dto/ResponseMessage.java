package com.ecommerce.open.service.apicenter.dto;

import java.util.Map;

import com.alibaba.fastjson.JSONObject;
import com.ecommerce.open.enums.RequestStatusEnum;

import lombok.Data;

/**
 * @ClassName: ServerRequestInfo
 * @Description: 服务请求
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class ResponseMessage {
	private Map<String, String> headerParms;
	private Object body;
	private String url;
	private long costTime;
	private String code; 
	private String message;
	private JSONObject responseJSON;

	/**
	 * 请求状态
	 */
	private RequestStatusEnum requestStatus;
	
	/**
	 * 是否发送超时
	 */
//	private boolean timeout = false;

}
