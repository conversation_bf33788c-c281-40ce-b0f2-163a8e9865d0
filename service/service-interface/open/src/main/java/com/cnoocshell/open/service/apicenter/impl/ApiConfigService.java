package com.ecommerce.open.service.apicenter.impl;

import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.open.api.dto.PageQuery;
import com.ecommerce.open.api.dto.apicenter.config.ApiConfigAddDTO;
import com.ecommerce.open.api.dto.apicenter.config.ApiConfigDelDTO;
import com.ecommerce.open.api.dto.apicenter.config.ApiConfigDetailDTO;
import com.ecommerce.open.api.dto.apicenter.config.ApiConfigListDTO;
import com.ecommerce.open.api.dto.apicenter.config.ApiConfigQueryDTO;
import com.ecommerce.open.api.dto.apicenter.config.ApiConfigUpdateDTO;
import com.ecommerce.open.service.apicenter.IApiConfigService;
import com.ecommerce.open.service.apicenter.biz.IApiConfigBiz;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service("apiConfigService")
public class ApiConfigService implements IApiConfigService {

    @Autowired
    private IApiConfigBiz apiConfigBiz;

    @Override
    public ItemResult<Void> create(ApiConfigAddDTO apiConfigAddDTO) {
        try {
            apiConfigBiz.create(apiConfigAddDTO);
        } catch (Exception e) {
            log.error("创建接口报文参数配置异常:{}", apiConfigAddDTO, e);
            ItemResult<Void> result = new ItemResult<>();
            result.setCode(BasicCode.UNKNOWN_ERROR.getCode());
            result.setDescription(e.getMessage());
            return result;
        }
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> deleteByIds(ApiConfigDelDTO apiConfigDelDTO) {
        try {
            apiConfigBiz.deleteByIds(apiConfigDelDTO);
        } catch (Exception e) {
            log.error("删除接口报文参数配置异常:{}", apiConfigDelDTO, e);
            ItemResult<Void> result = new ItemResult<>();
            result.setCode(BasicCode.UNKNOWN_ERROR.getCode());
            result.setDescription(e.getMessage());
            return result;
        }
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<PageData<ApiConfigListDTO>> queryList(PageQuery<ApiConfigQueryDTO> pageQuery) {
        PageData<ApiConfigListDTO> resultDTOPageQuery = null;
        try {
            resultDTOPageQuery = apiConfigBiz.queryList(pageQuery);
        } catch (Exception e) {
            log.error("列表查询接口报文参数配置异常:{}", pageQuery, e);
            ItemResult<PageData<ApiConfigListDTO>> result = new ItemResult<>();
            result.setCode(BasicCode.UNKNOWN_ERROR.getCode());
            result.setDescription(e.getMessage());
            return result;
        }
        return new ItemResult<>(resultDTOPageQuery);
    }

    @Override
    public ItemResult<ApiConfigDetailDTO> queryById(Integer defApiId) {
        ApiConfigDetailDTO resultDTO = null;
        try{
            resultDTO = apiConfigBiz.queryById(defApiId);
        } catch (Exception e) {
            log.error("单条查询接口报文参数配置异常:{}", defApiId, e);
            ItemResult<ApiConfigDetailDTO> result = new ItemResult<>();
            result.setCode(BasicCode.UNKNOWN_ERROR.getCode());
            result.setDescription(e.getMessage());
            return result;
        }
        return new ItemResult<>(resultDTO);
    }

    @Override
    public ItemResult<Void> update(ApiConfigUpdateDTO apiConfigUpdateDTO) {
        try {
            apiConfigBiz.update(apiConfigUpdateDTO);
        } catch (Exception e) {
            log.error("修改接口报文参数配置异常:{}", apiConfigUpdateDTO, e);
            ItemResult<Void> result = new ItemResult<>();
            result.setCode(BasicCode.UNKNOWN_ERROR.getCode());
            result.setDescription(e.getMessage());
            return result;
        }
        return new ItemResult<>(null);
    }
}
