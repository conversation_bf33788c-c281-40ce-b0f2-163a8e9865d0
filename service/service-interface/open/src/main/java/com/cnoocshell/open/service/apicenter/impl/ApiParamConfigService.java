package com.ecommerce.open.service.apicenter.impl;

import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.open.api.dto.PageQuery;
import com.ecommerce.open.api.dto.apicenter.config.ApiParamConfigAddDTO;
import com.ecommerce.open.api.dto.apicenter.config.ApiParamConfigDelDTO;
import com.ecommerce.open.api.dto.apicenter.config.ApiParamConfigQueryDTO;
import com.ecommerce.open.api.dto.apicenter.config.ApiParamConfigResultDTO;
import com.ecommerce.open.api.dto.apicenter.config.ApiParamConfigUpdateDTO;
import com.ecommerce.open.service.apicenter.IApiParamConfigService;
import com.ecommerce.open.service.apicenter.biz.IApiParamConfigBiz;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service("apiParamConfigService")
public class ApiParamConfigService implements IApiParamConfigService {

    @Autowired
    private IApiParamConfigBiz apiParamConfigBiz;

    @Override
    public ItemResult<Void> create(ApiParamConfigAddDTO apiParamConfigAddDTO) {
        try {
            apiParamConfigBiz.create(apiParamConfigAddDTO);
        } catch (Exception e) {
            log.error("创建接口报文参数配置异常:{}", apiParamConfigAddDTO, e);
            ItemResult<Void> result = new ItemResult<>();
            result.setCode(BasicCode.UNKNOWN_ERROR.getCode());
            result.setDescription(e.getMessage());
            return result;
        }
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> deleteByIds(ApiParamConfigDelDTO apiParamConfigDelDTO) {
        try {
            apiParamConfigBiz.deleteByIds(apiParamConfigDelDTO);
        } catch (Exception e) {
            log.error("删除接口报文参数配置异常:{}", apiParamConfigDelDTO, e);
            ItemResult<Void> result = new ItemResult<>();
            result.setCode(BasicCode.UNKNOWN_ERROR.getCode());
            result.setDescription(e.getMessage());
            return result;
        }
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<PageData<ApiParamConfigResultDTO>> queryList(PageQuery<ApiParamConfigQueryDTO> pageQuery) {
        PageData<ApiParamConfigResultDTO> resultDTOPageQuery = null;
        try {
            resultDTOPageQuery = apiParamConfigBiz.queryList(pageQuery);
        } catch (Exception e) {
            log.error("列表查询接口报文参数配置异常:{}", pageQuery, e);
            ItemResult<PageData<ApiParamConfigResultDTO>> result = new ItemResult<>();
            result.setCode(BasicCode.UNKNOWN_ERROR.getCode());
            result.setDescription(e.getMessage());
            return result;
        }
        return new ItemResult<>(resultDTOPageQuery);
    }

    @Override
    public ItemResult<ApiParamConfigResultDTO> queryById(Integer paramConfigId) {
        ApiParamConfigResultDTO resultDTO = null;
        try{
            resultDTO = apiParamConfigBiz.queryById(paramConfigId);
        } catch (Exception e) {
            log.error("单条查询接口报文参数配置异常:{}", paramConfigId, e);
            ItemResult<ApiParamConfigResultDTO> result = new ItemResult<>();
            result.setCode(BasicCode.UNKNOWN_ERROR.getCode());
            result.setDescription(e.getMessage());
            return result;
        }
        return new ItemResult<>(resultDTO);
    }

    @Override
    public ItemResult<Void> update(ApiParamConfigUpdateDTO apiParamConfigUpdateDTO) {
        try {
            apiParamConfigBiz.update(apiParamConfigUpdateDTO);
        } catch (Exception e) {
            log.error("修改接口报文参数配置异常:{}", apiParamConfigUpdateDTO, e);
            ItemResult<Void> result = new ItemResult<>();
            result.setCode(BasicCode.UNKNOWN_ERROR.getCode());
            result.setDescription(e.getMessage());
            return result;
        }
        return new ItemResult<>(null);
    }
}
