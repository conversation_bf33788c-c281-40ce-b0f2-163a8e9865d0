package com.ecommerce.open.service.apicenter.impl;

import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.open.api.dto.PageQuery;
import com.ecommerce.open.api.dto.apicenter.ApiRequestCondDTO;
import com.ecommerce.open.api.dto.apicenter.ApiRequestListDTO;
import com.ecommerce.open.service.apicenter.IApiRequestService;
import com.ecommerce.open.service.apicenter.biz.IAPIRequestBiz;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service("apiRequestService")
public class ApiRequestService implements IApiRequestService {

    @Autowired
    private IAPIRequestBiz apiRequestBiz;

    @Override
    public ItemResult<PageData<ApiRequestListDTO>> queryApiRequestByCond(PageQuery<ApiRequestCondDTO> pageQuery) {
        log.info("请求查询的条件为:{}", pageQuery);
        try {
            PageData<ApiRequestListDTO> resultPage = apiRequestBiz.queryApiRequestByCond(pageQuery);
            log.info("请求查询的结果为:{}", pageQuery);
            return new ItemResult<>(resultPage);
        } catch (Exception e) {
            log.error("请求查询发生异常", e);
            ItemResult<PageData<ApiRequestListDTO>> result = new ItemResult<>();
            result.setCode(BasicCode.UNKNOWN_ERROR.getCode());
            result.setDescription(e.getMessage());
            return result;
        }
    }
}
