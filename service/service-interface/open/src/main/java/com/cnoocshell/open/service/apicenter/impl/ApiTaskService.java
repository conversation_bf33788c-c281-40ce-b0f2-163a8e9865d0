package com.ecommerce.open.service.apicenter.impl;

import com.ecommerce.open.dao.vo.ApiPendingMessage;
import com.ecommerce.open.enums.RequestStatusEnum;
import com.ecommerce.open.enums.SendStatusEnum;
import com.ecommerce.open.service.apicenter.IApiTaskService;
import com.ecommerce.open.service.apicenter.IOpenAPIInvokeService;
import com.ecommerce.open.service.apicenter.biz.IApiPendingMessageBiz;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

@Service("apiTaskService")
public class ApiTaskService implements IApiTaskService {

    @Autowired
    private IApiPendingMessageBiz apiPendingMessageBiz;

    @Autowired
    private IOpenAPIInvokeService openAPIInvokeService;

    @Override
    public void deleteOverduePendingMessage() {
        LocalDateTime now = LocalDateTime.now();

        //删除三天成功的
        LocalDateTime successOverdueTime = now.minusDays(90);
        Date successEndTime = Date.from(successOverdueTime.atZone(ZoneId.systemDefault()).toInstant());
        //3天 requestSendSuccess，responseParseSuccess
        apiPendingMessageBiz.deleteBeforeDateAndStatus(
                successEndTime, Lists.newArrayList(RequestStatusEnum.RESPONSE_SUCCESS.code(), RequestStatusEnum.SEND_SUCCESS.code())
        );

        //删除七天失败的
        LocalDateTime failOverdueTime = now.minusDays(90);
        Date failEndTime = Date.from(failOverdueTime.atZone(ZoneId.systemDefault()).toInstant());
        //7天 responseParseFail，requestSendFail
        apiPendingMessageBiz.deleteBeforeDateAndStatus(
                failEndTime, Lists.newArrayList(RequestStatusEnum.RESPONSE_FAIL.code(), RequestStatusEnum.SEND_FAIL.code())
        );
        //处理并删除七天发失败的
        //7天 requestSendTimeOut， requestWaitSend
        List<ApiPendingMessage> failCallBackList = apiPendingMessageBiz.selectByTimeAndStatus(
                failEndTime, Lists.newArrayList(RequestStatusEnum.SEND_TIMEOUT.code(), RequestStatusEnum.WAIT_SEND.code())
        );
        if (CollectionUtils.isNotEmpty(failCallBackList)) {
            List<String> delIdList = Lists.newArrayList();
            for (ApiPendingMessage apiPendingMessage : failCallBackList) {
                delIdList.add(apiPendingMessage.getPendingId().toString());
                openAPIInvokeService.failCallbackWhenRequestStatusException(apiPendingMessage);
            }
            apiPendingMessageBiz.deleteByIds(String.join(",", delIdList));
        }
    }
}
