package com.ecommerce.open.service.apicenter.impl;

import com.alibaba.fastjson.JSON;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.service.common.SpringContextHolder;
import com.ecommerce.open.api.dto.apicenter.ApiResult;
import com.ecommerce.open.api.dto.apicenter.BaseClientRequestDTO;
import com.ecommerce.open.api.dto.apicenter.ERPRetryCallbackDTO;
import com.ecommerce.open.api.dto.apicenter.IdentityVerifyDTO;
import com.ecommerce.open.api.exception.APICenterExceptionCode;
import com.ecommerce.open.config.AuthConfig;
import com.ecommerce.open.dao.bean.ApiConfigBean;
import com.ecommerce.open.dao.vo.ApiPendingMessage;
import com.ecommerce.open.dao.vo.SysConfig;
import com.ecommerce.open.dao.vo.SysRoute;
import com.ecommerce.open.enums.RequestStatusEnum;
import com.ecommerce.open.service.apicenter.IOpenAPIInvokeService;
import com.ecommerce.open.service.apicenter.biz.IAPIInvokeBiz;
import com.ecommerce.open.service.apicenter.biz.IApiConfigBiz;
import com.ecommerce.open.service.apicenter.biz.IApiPendingMessageBiz;
import com.ecommerce.open.service.apicenter.biz.IAsyncSyncAdapter;
import com.ecommerce.open.service.apicenter.biz.IDataTransfer;
import com.ecommerce.open.service.apicenter.biz.ISysConfigBiz;
import com.ecommerce.open.service.apicenter.biz.ISysRouteBiz;
import com.ecommerce.open.service.apicenter.biz.ISystemInvokeBiz;
import com.ecommerce.open.service.apicenter.biz.connector.IConnector;
import com.ecommerce.open.service.apicenter.dto.RequestContext;
import com.ecommerce.open.service.apicenter.dto.RequestMessage;
import com.ecommerce.open.service.apicenter.dto.ResponseMessage;
import lombok.extern.slf4j.Slf4j;
import com.ecommerce.common.utils.CsStringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Base64;

@Slf4j
@Service("openAPIInvokeService")
public class OpenAPIInvokeService implements IOpenAPIInvokeService {
	private final static Logger interfaceLogger = LoggerFactory.getLogger("AsyncInterfaceLog");

	/**
	 *
	 */
	@Autowired
	private ISystemInvokeBiz systemInvokeBiz;

	@Autowired
	private ISysRouteBiz sysRouteBiz;

	@Autowired
	private ISysConfigBiz sysConfigBiz;

	@Autowired
	private IApiPendingMessageBiz apiPendingMessageBiz;

	@Autowired
	private IAPIInvokeBiz apiInvokeBiz;

	@Autowired
	private IApiConfigBiz apiConfigBiz;

	@Autowired
	private AuthConfig authConfig;

	@Override
	public ApiResult invoke(String bizCode, String sellerId, String data) {
		interfaceLogger.info("L1 OpenAPIInvokeService start invoke bizCode====>{}, sellerId====>{}, data====>{}",
				bizCode, sellerId, data);
		log.info("*** start ALL L1 OpenAPIInvokeService start invoke bizCode====>{}, sellerId====>{}, data====>{}", bizCode, sellerId,
				data);

		String apiPendingMessageRequestNo = "";

		boolean needUpdateApiPendingMessage = false;

		SysRoute sysRoute = this.getSysRoute(bizCode, sellerId);
		RequestContext requestContext = new RequestContext();
		requestContext.setSellerId(sellerId);
		requestContext.setBizCode(bizCode);
		requestContext.setClientData(data);
		requestContext.setSysRoute(sysRoute);
		requestContext.setSysConfig(this.getSysConfig(sysRoute.getSysCode()));

		ApiResult validateResult = this.validate(requestContext);

		if(requestContext.getClientDataDTO() != null && requestContext.getClientDataDTO()  instanceof BaseClientRequestDTO) {
			BaseClientRequestDTO reuqestDTO =  (BaseClientRequestDTO)requestContext.getClientDataDTO();
			if("1".equals(reuqestDTO.getIfFirstSaveClientRequest())){
				String requestNo = reuqestDTO.getRequestNo();
				ApiPendingMessage apiPendingMessage = new ApiPendingMessage();
				apiPendingMessage.setRequestNo(requestNo);
				apiPendingMessage.setBizCode(bizCode);
				if(data.length() <= 10000 ) {
					apiPendingMessage.setMessage(data);
				}else {
					log.info("*** request 过长不做入库只做归档");
				}
				apiPendingMessage.setSendStatus(RequestStatusEnum.SEND_SUCCESS.getCode());

				apiPendingMessage.setSendCount(1);
				apiPendingMessage.setSysCode(sysRoute.getSysCode());
				apiPendingMessage.setBody(data);
				apiPendingMessageRequestNo = apiPendingMessageBiz.savePending(apiPendingMessage);
				needUpdateApiPendingMessage = true;
				//log.info("更新请求pending message状态 apiPendingMessage===>{}", JSON.toJSON(apiPendingMessage));
			}
		}

		if (!(validateResult.isSuccess())) {
			log.error("L1 OpenAPIInvokeService invoke 数据验证未通过 {} ", validateResult.getDescription());
//			// throw new BizException(APICenterExceptionCode.DATA_NOT_VALIDATE,
//			// validateResult.getDescription());
//			ApiResult result = new ApiResult();
//			result.setSuccess(false);
//			result.setDescription("数据验证未通过 :" + validateResult.getDescription());
//			result.getCode()
			return validateResult;
		}
		//log.info("L1 OpenAPIInvokeService start invokeSystem ");

		ItemResult  result = null;//systemInvokeBiz.invokeSystem(requestContext);
		try {
			 result = systemInvokeBiz.invokeSystem(requestContext);
		}catch(Exception e) {
			log.error(e.getMessage(), e);
			interfaceLogger.error("L1 OpenAPIInvokeService end invokeSystem Exception====>{}", e.getMessage());
			result = new ItemResult<>();
			result.setSuccess(false);
			result.setDescription(e.getMessage());
			result.setData(e);
			//log.info("*** end ALL L1 OpenAPIInvokeService end invokeSystem result====>{}", result);
			return new ApiResult(result);
		}

		log.info("*** end ALL L1 OpenAPIInvokeService end invokeSystem result====>{}", result);
		interfaceLogger.info("L1 OpenAPIInvokeService end invokeSystem result====>{}", result);
		if (needUpdateApiPendingMessage && CsStringUtils.isNotBlank(apiPendingMessageRequestNo)) {
			ApiPendingMessage updateRecord = new ApiPendingMessage();
			updateRecord.setResponseMessage(String.valueOf(result.getData()));
			updateRecord.setRequestNo(apiPendingMessageRequestNo);
			apiPendingMessageBiz.updateByRequestNo(updateRecord);
		}
		return new ApiResult(result);
	}

	/**
	 * @Title: getSysRoute
	 * @Description: 获取系统路由
	 * <AUTHOR>
	 * @param bizCode
	 * @param sellerId
	 * @return
	 */
	private SysRoute getSysRoute(String bizCode, String sellerId) {
		//log.info("L1 OpenAPIInvokeService start getSysRoute");
		SysRoute sysRoute = sysRouteBiz.route(sellerId, bizCode);

		if (sysRoute == null) {
			log.error("L1 OpenAPIInvokeService invoke 未能路由到system 配置错误 bizCode====>{}, sellerId====>{} ", bizCode,
					sellerId);
			throw new BizException(APICenterExceptionCode.CONFIG_NULL, "路由配置错误");
		}
		//log.info("L1 OpenAPIInvokeService end find sysRoute===>{}", sysRoute);
		return sysRoute;
	}

	/**
	 * @Title: getSysConfig
	 * @Description: 获取系统配置
	 * <AUTHOR>
	 * @param sysCode
	 * @return
	 */
	private SysConfig getSysConfig(String sysCode) {
		//log.info("L1 OpenAPIInvokeService start getSysRoute");
		if (CsStringUtils.isEmpty(sysCode)) {
			log.error("L1 OpenAPIInvokeService getSysConfig sysCode未配置");
			throw new BizException(APICenterExceptionCode.CONFIG_NULL, "sysCode");
		}

		SysConfig sysConfig = null;
		try {
			sysConfig = sysConfigBiz.getBySysCode(sysCode);
		} catch (Exception e) {
			e.printStackTrace();
			log.error("L1 OpenAPIInvokeService getSysConfig sysConfig配置异常 sysCode====>{}", sysCode);
		}
		if (sysConfig == null) {
			log.error("L1 OpenAPIInvokeService getSysConfig sysConfig未配置 sysCode====>{}", sysCode);
			throw new BizException(APICenterExceptionCode.CONFIG_NULL, "未配置SysConfig");
		}
		//log.info("L1 OpenAPIInvokeService end getSysConfig sysConfig===>{}", sysConfig);
		return sysConfig;
	}

	@Override
	public ApiResult validate(String bizCode, String sellerId, String data) {
		//log.info("L1 OpenAPIInvokeService start validate ");
		SysRoute sysRoute = this.getSysRoute(bizCode, sellerId);
		RequestContext requestContext = new RequestContext();
		requestContext.setSellerId(sellerId);
		requestContext.setBizCode(bizCode);
		requestContext.setClientData(data);
		requestContext.setSysRoute(sysRoute);
		requestContext.setSysConfig(this.getSysConfig(sysRoute.getSysCode()));
		ApiResult result = validate(requestContext);
		//log.info("L1 OpenAPIInvokeService end validate result====>{}", result);
		return result;
	}

	public ApiResult validate(RequestContext requestContext) {
		//log.info("L1 OpenAPIInvokeService start validate ");
		ApiResult result = systemInvokeBiz.validate(requestContext);
		//log.info("L1 OpenAPIInvokeService end validate result====>{}", result);
		return result;
	}

	@Override
	public void failCallbackWhenRequestStatusException(ApiPendingMessage apiPendingMessage) {
		log.error("定时任务发现请求异常，超时时间超过3天发起自动回调，通知业务方处理失败apiPendingMessage===>{}", apiPendingMessage);
		String apiCode = apiPendingMessage.getApiCode();
		String systemCode = apiPendingMessage.getSysCode();
		String bizCode = apiPendingMessage.getBizCode();

		if (CsStringUtils.isNullOrBlank(apiCode)) {
			log.error("L1 OpenAPIInvokeService reSendRequest error, apiCode 为空");
			return;
		}
		if (CsStringUtils.isNullOrBlank(systemCode)) {
			log.error("L1 OpenAPIInvokeService reSendRequest error, systemCode 为空");
			return;
		}
		if (CsStringUtils.isNullOrBlank(bizCode)) {
			log.error("L1 OpenAPIInvokeService reSendRequest error, bizCode 为空");
			return;
		}

		SysConfig sysConfig = sysConfigBiz.getBySysCode(systemCode);

		if (sysConfig == null) {
			log.error("L1 OpenAPIInvokeService reSendRequest error, sysConfig 为空");
			throw new BizException(APICenterExceptionCode.DATA_NOT_VALIDATE);
		}

		ApiConfigBean apiConfigBean = apiConfigBiz.queryApiByBizCode(systemCode, bizCode);
		if (apiConfigBean == null) {
			log.error("L1 OpenAPIInvokeService reSendRequest error, apiConfigBean 为空");
			throw new BizException(APICenterExceptionCode.DATA_NOT_VALIDATE);
		}
		RequestMessage requestMessage = null;
		try {
			requestMessage = JSON.parseObject(apiPendingMessage.getMessage(), RequestMessage.class);
		} catch (Exception e) {
			e.printStackTrace();
			log.error("L1 OpenAPIInvokeService reSendRequest error, RequestMessage 解析失败");
			this.failCallbackWhenRequestStatusException(apiPendingMessage);
			this.updateRequestPendingMessage(apiPendingMessage.getRequestNo(), RequestStatusEnum.SEND_FAIL.getCode());
		}

		RequestContext requestContext = new RequestContext();

		requestContext.setApiConfigBean(apiConfigBean);
		requestContext.setSysConfig(sysConfig);
		requestContext.setRequestMessage(requestMessage);
		// ResponseMessage responseMessage = null;
		// if (retryCallbackDTO.isMaxRetry()) {
		log.error("API failCallbackWhenRequestStatusException, send fail callback  requestContext===>{}",
				requestContext);
		IAsyncSyncAdapter asyncSyncAdapter = this.getAsyncSyncAdapter(apiConfigBean.getClientSync(),
				apiConfigBean.getServerSync());
		asyncSyncAdapter.sendFailCallBackRequest(requestContext);
		this.updateRequestPendingMessage(apiPendingMessage.getRequestNo(), RequestStatusEnum.SEND_FAIL.getCode());
		// } else {
		// log.error("API Resend OpenAPIInvokeService start resend
		// requestContext===>{}", requestContext);
		// responseMessage = connector.sendRequestToServer(requestContext);
		// }

	}

	@Override
	public ItemResult<Boolean> identityVerify(IdentityVerifyDTO identityVerifyDTO) {
		Boolean verifyFlag = Boolean.FALSE;
		if (identityVerifyDTO == null || CsStringUtils.isEmpty(identityVerifyDTO.getAuthorization())) {
			return new ItemResult<>(verifyFlag);
		}
		Base64.Encoder encoder = Base64.getEncoder();
		//请求来源验证
		String username = authConfig.getHrERPUserName();
		String password = authConfig.getHrERPPassword();
		String authKey = "Basic " + encoder.encodeToString((username + ":" + password).getBytes());
		if (identityVerifyDTO.getAuthorization().equals(authKey)) {
			verifyFlag = Boolean.TRUE;
		}

		return new ItemResult<>(verifyFlag);
	}

	@Override
	@Transactional
	public ItemResult<Object> reSendRequest(ERPRetryCallbackDTO retryCallbackDTO) {
		//log.info("API Resend OpenAPIInvokeService start reSendRequest retryCallbackDTO===>{}", retryCallbackDTO);
		String requestNo = retryCallbackDTO.getRequestNo();
		if (CsStringUtils.isNullOrBlank(requestNo)) {
			log.error("API Resend OpenAPIInvokeService reSendRequest error, requestNo 为空");
			throw new BizException(APICenterExceptionCode.DATA_NOT_VALIDATE);
		}

		ApiPendingMessage apiPendingMessage = apiPendingMessageBiz.queryByRequestNo(requestNo);

		if (apiPendingMessage == null) {
			log.error("API Resend OpenAPIInvokeService reSendRequest error 未查询到请求记录 requestNo===>{}", requestNo);
		}

		String apiCode = apiPendingMessage.getApiCode();
		String systemCode = apiPendingMessage.getSysCode();
		String bizCode = apiPendingMessage.getBizCode();
		if (CsStringUtils.isNullOrBlank(apiCode)) {
			log.error("API Resend OpenAPIInvokeService reSendRequest error, apiCode 为空");
			throw new BizException(APICenterExceptionCode.DATA_NOT_VALIDATE);
		}
		if (CsStringUtils.isNullOrBlank(systemCode)) {
			log.error("API Resend OpenAPIInvokeService reSendRequest error, systemCode 为空");
			throw new BizException(APICenterExceptionCode.DATA_NOT_VALIDATE);
		}
		if (CsStringUtils.isNullOrBlank(bizCode)) {
			log.error("API Resend OpenAPIInvokeService reSendRequest error, bizCode 为空");
			throw new BizException(APICenterExceptionCode.DATA_NOT_VALIDATE);
		}

		SysConfig sysConfig = sysConfigBiz.getBySysCode(systemCode);

		if (sysConfig == null) {
			log.error("API Resend OpenAPIInvokeService reSendRequest error, sysConfig 为空");
			throw new BizException(APICenterExceptionCode.DATA_NOT_VALIDATE);
		}

		ApiConfigBean apiConfigBean = apiConfigBiz.queryApiByBizCode(systemCode, bizCode);
		if (apiConfigBean == null) {
			log.error("API Resend OpenAPIInvokeService reSendRequest error, apiConfigBean 为空");
			throw new BizException(APICenterExceptionCode.DATA_NOT_VALIDATE);
		}

		int sendTime = apiPendingMessage.getSendCount();
		int maxResendTime = sysConfig.getMaxTimes();
		// if (sendTime >= (maxResendTime + 1)) {
		// log.info("API Resend OpenAPIInvokeService request:{} 达到最大重试次数不再重试 ",
		// requestNo);
		// return;
		// }

		String transferName = apiConfigBean.getTransferName();

		if (CsStringUtils.isNullOrBlank(transferName)) {
			log.error("API Resend OpenAPIInvokeService reSendRequest error, transferName 为空");
			throw new BizException(APICenterExceptionCode.CONFIG_NULL, "transferName");
		}

		IConnector connector = this.getConnectorByProtocol(apiConfigBean.getProtocol());

		if (connector == null) {
			log.error("API Resend OpenAPIInvokeService reSendRequest error, connector 为空");
			throw new BizException(APICenterExceptionCode.CONFIG_NULL, "connector");
		}

		RequestMessage requestMessage = null;

		try {
			requestMessage = JSON.parseObject(apiPendingMessage.getMessage(), RequestMessage.class);
		} catch (Exception e) {
			e.printStackTrace();
			log.error("API Resend OpenAPIInvokeService reSendRequest error, RequestMessage 解析失败");
			this.failCallbackWhenRequestStatusException(apiPendingMessage);
			this.updateRequestPendingMessage(requestNo, RequestStatusEnum.SEND_FAIL.getCode());
			return new ItemResult<>(false);
		}
		IDataTransfer transfer = this.getTransfer(apiConfigBean);
		if (transfer == null) {
			log.error("API Resend OpenAPIInvokeService reSendRequest error, getTransfer 为空");
			throw new BizException(APICenterExceptionCode.CONFIG_NULL, "getTransfer");
		}

		RequestContext requestContext = new RequestContext();

		requestContext.setApiConfigBean(apiConfigBean);
		requestContext.setSysConfig(sysConfig);
		requestMessage.setUrl(transfer.transferURL(requestContext));
		requestContext.setRequestMessage(requestMessage);
		ResponseMessage responseMessage = null;
		if (retryCallbackDTO.isMaxRetry()) {
			log.error("API Resend OpenAPIInvokeService max retry, send fail callback  requestContext===>{}",
					requestContext);
			IAsyncSyncAdapter asyncSyncAdapter = this.getAsyncSyncAdapter(apiConfigBean.getClientSync(),
					apiConfigBean.getServerSync());
			asyncSyncAdapter.sendFailCallBackRequest(requestContext);
			this.updateRequestPendingMessage(requestNo, RequestStatusEnum.SEND_FAIL.getCode());
		} else {
			log.error("API Resend OpenAPIInvokeService start resend requestContext===>{}", requestContext);
			responseMessage = connector.sendRequestToServer(requestContext);
		}

		// if (responseMessage == null) {
		// log.info("API Resend OpenAPIInvokeService send fail responseMessage is null,
		// trigger resend");
		// this.triggerResend(requestContext);
		// }
		//
		// if (RequestStatusEnum.SEND_FAIL.equals(responseMessage.getRequestStatus())
		// || RequestStatusEnum.SEND_TIMEOUT.equals(responseMessage.getRequestStatus()))
		// {
		// this.triggerResend(requestContext);
		// }
		ItemResult result = new ItemResult<>();
		//log.info("API Resend OpenAPIInvokeService end reSendRequest ");
		if (responseMessage == null) {
			result.setSuccess(false);
			return result;
		}
		if (RequestStatusEnum.SEND_SUCCESS.equals(responseMessage.getRequestStatus())) {
			result.setSuccess(true);
			return result;
		}
		result.setSuccess(false);
		return result;
	}

	private IConnector getConnectorByProtocol(String protocol) {
		//log.info("L1 BaseAsyncSyncAdapter start getConnectorByProtocol");
		if (CsStringUtils.isEmpty(protocol)) {
			log.error("L1 SyncToSyncAdapter sendRequest cannot find protocol");
			throw new BizException(APICenterExceptionCode.CONFIG_NULL);
		}

		IConnector connector = (IConnector) SpringContextHolder.getBean(protocol);

		if (connector == null) {
			log.error("L1 SyncToSyncAdapter sendRequest cannot find connector");
			throw new BizException(APICenterExceptionCode.CONFIG_NULL);
		}
		//log.info("L1 BaseAsyncSyncAdapter end getConnectorByProtocol {}", connector);
		return connector;
	}

	// private void triggerResend(RequestContext requestContext) {
	// log.info("API Resend OpenAPIInvokeService resend fail trigger resend
	// requestContext====>{}", requestContext);
	//
	// }

	private void updateRequestPendingMessage(String requestNo, String status) {

		ApiPendingMessage apiPendingMessage = new ApiPendingMessage();
		apiPendingMessage.setRequestNo(requestNo);
		apiPendingMessage.setSendStatus(status);
		//log.info("更新请求pending message状态 apiPendingMessage===>{}", JSON.toJSON(apiPendingMessage));
		this.apiPendingMessageBiz.updateByRequestNo(apiPendingMessage);
	}

	/**
	 * @Title: getAsyncSyncAdapter
	 * @Description: 获取AsyncSyncAdapter
	 * <AUTHOR>
	 * @param clientSync
	 * @param serverSync
	 * @return
	 */
	private IAsyncSyncAdapter getAsyncSyncAdapter(boolean clientSync, boolean serverSync) {
		//log.info("L1 获取async adapter ：clientSync {} , serverSync {} ", clientSync, serverSync);
		String adapterName = "syncToSyncAdapter";
		if (!clientSync && serverSync) {
			adapterName = "asyncToSyncAdapter";
		}
		if (clientSync && !serverSync) {
			adapterName = "syncToAsyncAdapter";
		}
		if (!clientSync && !serverSync) {
			adapterName = "asyncToAsyncAdapter";
		}
		log.info("L1 获取async adapter ：adapterName {}  ", adapterName);

		try {
			return (IAsyncSyncAdapter) SpringContextHolder.getBean(adapterName);
		} catch (Exception e) {
			log.error("获取 AsyncSyncAdapter 失败, error: {}, message: {}", e.getClass().getName(), e.getMessage());
			throw new BizException(BasicCode.UNKNOWN_ERROR);
		}
	}

	private IDataTransfer getTransfer(ApiConfigBean apiConfigBean) {

		String transferName = apiConfigBean.getTransferName();
		if (CsStringUtils.isEmpty(transferName)) {
			log.error("L1 APIInvokeBiz TransferName 为空, 无法找到对应的转换类");
			throw new BizException(BasicCode.PARAM_NULL, "Transfer");
		}

		try {
			return (IDataTransfer) SpringContextHolder.getBean(transferName);
		} catch (Exception e) {
			log.error("获取 IDataTransfer 失败, error: {}, message: {}", e.getClass().getName(), e.getMessage());
			throw new BizException(APICenterExceptionCode.CONFIG_NULL, "Transfer未配置 :" + transferName);
		}
	}

}
