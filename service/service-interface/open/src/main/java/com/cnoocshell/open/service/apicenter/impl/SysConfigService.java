package com.ecommerce.open.service.apicenter.impl;

import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.open.api.dto.PageQuery;
import com.ecommerce.open.api.dto.apicenter.config.SysConfigAddDTO;
import com.ecommerce.open.api.dto.apicenter.config.SysConfigDelDTO;
import com.ecommerce.open.api.dto.apicenter.config.SysConfigQueryDTO;
import com.ecommerce.open.api.dto.apicenter.config.SysConfigResultDTO;
import com.ecommerce.open.api.dto.apicenter.config.SysConfigUpdateDTO;
import com.ecommerce.open.service.apicenter.ISysConfigService;
import com.ecommerce.open.service.apicenter.biz.ISysConfigBiz;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service("sysConfigService")
public class SysConfigService implements ISysConfigService {

    @Autowired
    private ISysConfigBiz sysConfigBiz;

    @Override
    public ItemResult<Void> create(SysConfigAddDTO sysConfigAddDTO) {
        try {
            sysConfigBiz.create(sysConfigAddDTO);
        } catch (Exception e) {
            log.error("创建系统定义配置异常:{}", sysConfigAddDTO, e);
            ItemResult<Void> result = new ItemResult<>();
            result.setCode(BasicCode.UNKNOWN_ERROR.getCode());
            result.setDescription(e.getMessage());
            return result;
        }
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> deleteByIds(SysConfigDelDTO sysConfigDelDTO) {
        try {
            sysConfigBiz.deleteByIds(sysConfigDelDTO);
        } catch (Exception e) {
            log.error("删除系统定义配置异常:{}", sysConfigDelDTO, e);
            ItemResult<Void> result = new ItemResult<>();
            result.setCode(BasicCode.UNKNOWN_ERROR.getCode());
            result.setDescription(e.getMessage());
            return result;
        }
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<PageData<SysConfigResultDTO>> queryList(PageQuery<SysConfigQueryDTO> pageQuery) {
        PageData<SysConfigResultDTO> resultDTOPageQuery = null;
        try {
            resultDTOPageQuery = sysConfigBiz.queryList(pageQuery);
        } catch (Exception e) {
            log.error("列表查询系统定义配置异常:{}", pageQuery, e);
            ItemResult<PageData<SysConfigResultDTO>> result = new ItemResult<>();
            result.setCode(BasicCode.UNKNOWN_ERROR.getCode());
            result.setDescription(e.getMessage());
            return result;
        }
        return new ItemResult<>(resultDTOPageQuery);
    }

    @Override
    public ItemResult<SysConfigResultDTO> queryById(Integer defSysId) {
        SysConfigResultDTO resultDTO = null;
        try{
            resultDTO = sysConfigBiz.queryById(defSysId);
        } catch (Exception e) {
            log.error("单条查询系统定义配置异常:{}", defSysId, e);
            ItemResult<SysConfigResultDTO> result = new ItemResult<>();
            result.setCode(BasicCode.UNKNOWN_ERROR.getCode());
            result.setDescription(e.getMessage());
            return result;
        }
        return new ItemResult<>(resultDTO);
    }

    @Override
    public ItemResult<Void> update(SysConfigUpdateDTO sysConfigUpdateDTO) {
        try {
            sysConfigBiz.update(sysConfigUpdateDTO);
        } catch (Exception e) {
            log.error("修改系统定义配置异常:{}", sysConfigUpdateDTO, e);
            ItemResult<Void> result = new ItemResult<>();
            result.setCode(BasicCode.UNKNOWN_ERROR.getCode());
            result.setDescription(e.getMessage());
            return result;
        }
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<List<SysConfigResultDTO>> querySysConfig() {
        try {
            return new ItemResult<>(sysConfigBiz.querySysConfig());
        } catch (Exception e) {
            log.error("querySysConfig异常:", e);
            ItemResult<List<SysConfigResultDTO>> result = new ItemResult<>();
            result.setCode(BasicCode.UNKNOWN_ERROR.getCode());
            result.setDescription(e.getMessage());
            result.setData(null);
            return result;
        }
    }
}
