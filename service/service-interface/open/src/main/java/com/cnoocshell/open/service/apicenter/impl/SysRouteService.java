package com.ecommerce.open.service.apicenter.impl;

import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.open.api.dto.PageQuery;
import com.ecommerce.open.api.dto.apicenter.config.SysRouteAddDTO;
import com.ecommerce.open.api.dto.apicenter.config.SysRouteDelDTO;
import com.ecommerce.open.api.dto.apicenter.config.SysRouteQueryDTO;
import com.ecommerce.open.api.dto.apicenter.config.SysRouteResultDTO;
import com.ecommerce.open.api.dto.apicenter.config.SysRouteUpdateDTO;
import com.ecommerce.open.service.apicenter.ISysRouteService;
import com.ecommerce.open.service.apicenter.biz.ISysRouteBiz;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service("sysRouteService")
public class SysRouteService implements ISysRouteService {

    @Autowired
    private ISysRouteBiz sysRouteBiz;

    @Override
    public ItemResult<Void> create(SysRouteAddDTO sysRouteAddDTO) {
        try {
            sysRouteBiz.create(sysRouteAddDTO);
        } catch (Exception e) {
            log.error("创建系统定义配置异常:{}", sysRouteAddDTO, e);
            ItemResult<Void> result = new ItemResult<>();
            result.setCode(BasicCode.UNKNOWN_ERROR.getCode());
            result.setDescription(e.getMessage());
            return result;
        }
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> deleteByIds(SysRouteDelDTO sysRouteDelDTO) {
        try {
            sysRouteBiz.deleteByIds(sysRouteDelDTO);
        } catch (Exception e) {
            log.error("删除系统定义配置异常:{}", sysRouteDelDTO, e);
            ItemResult<Void> result = new ItemResult<>();
            result.setCode(BasicCode.UNKNOWN_ERROR.getCode());
            result.setDescription(e.getMessage());
            return result;
        }
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<PageData<SysRouteResultDTO>> queryList(PageQuery<SysRouteQueryDTO> pageQuery) {
        PageData<SysRouteResultDTO> resultDTOPageQuery = null;
        try {
            resultDTOPageQuery = sysRouteBiz.queryList(pageQuery);
        } catch (Exception e) {
            log.error("列表查询系统定义配置异常:{}", pageQuery, e);
            ItemResult<PageData<SysRouteResultDTO>> result = new ItemResult<>();
            result.setCode(BasicCode.UNKNOWN_ERROR.getCode());
            result.setDescription(e.getMessage());
            return result;
        }
        return new ItemResult<>(resultDTOPageQuery);
    }

    @Override
    public ItemResult<SysRouteResultDTO> queryById(Integer defSysId) {
        SysRouteResultDTO resultDTO = null;
        try{
            resultDTO = sysRouteBiz.queryById(defSysId);
        } catch (Exception e) {
            log.error("单条查询系统定义配置异常:{}", defSysId, e);
            ItemResult<SysRouteResultDTO> result = new ItemResult<>();
            result.setCode(BasicCode.UNKNOWN_ERROR.getCode());
            result.setDescription(e.getMessage());
            return result;
        }
        return new ItemResult<>(resultDTO);
    }

    @Override
    public ItemResult<Void> update(SysRouteUpdateDTO sysRouteUpdateDTO) {
        try {
            sysRouteBiz.update(sysRouteUpdateDTO);
        } catch (Exception e) {
            log.error("修改系统定义配置异常:{}", sysRouteUpdateDTO, e);
            ItemResult<Void> result = new ItemResult<>();
            result.setCode(BasicCode.UNKNOWN_ERROR.getCode());
            result.setDescription(e.getMessage());
            return result;
        }
        return new ItemResult<>(null);
    }
}
