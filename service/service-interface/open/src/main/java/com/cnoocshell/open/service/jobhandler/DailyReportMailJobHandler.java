package com.ecommerce.open.service.jobhandler;

import com.ecommerce.common.utils.DateUtil;
import com.ecommerce.open.service.IReportNotifyService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.IJobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class DailyReportMailJobHandler extends IJobHandler {

    @Autowired
    private IReportNotifyService mailService;

    @Override
    public void execute() throws Exception {
        log.info("time job [dailyReportMailJobHandler] is called --->>>{}", s);
        XxlJobHelper.log("dailyReportMailJobHandler start time " + DateUtil.getCurrentDateStr("yyyy-MM-dd HH:mm:ss"));
        try {
            mailService.sendDailyReportAllMail();
        } catch (Exception e) {
            XxlJobHelper.log("dailyReportMailJobHandler excute fail reason:" + e.getMessage());
        }
        XxlJobHelper.log("dailyReportMailJobHandler end time " + DateUtil.getCurrentDateStr("yyyy-MM-dd HH:mm:ss"));
    }
}
