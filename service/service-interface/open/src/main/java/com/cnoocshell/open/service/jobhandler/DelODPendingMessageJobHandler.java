package com.ecommerce.open.service.jobhandler;

import com.ecommerce.common.utils.DateUtil;
import com.ecommerce.open.service.apicenter.IApiTaskService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class DelODPendingMessageJobHandler extends IJobHandler {

    @Autowired
    private IApiTaskService iApiTaskService;

    @Override
    public void execute() throws Exception {
        log.info("time job [delODPendingMessageJobHandler] is called --->>> ");
        XxlJobHelper.log("delODPendingMessageJobHandler start time " + DateUtil.getCurrentDateStr("yyyy-MM-dd HH:mm:ss"));
        try {
            iApiTaskService.deleteOverduePendingMessage();
        } catch (Exception e) {
            XxlJobHelper.log("delODPendingMessageJobHandler excute fail reason:" + e.getMessage());
        }
        XxlJobHelper.log("delODPendingMessageJobHandler end time " + DateUtil.getCurrentDateStr("yyyy-MM-dd HH:mm:ss"));
    }
}
