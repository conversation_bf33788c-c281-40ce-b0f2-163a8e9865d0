<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.open.dao.mapper.ApiConfigMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.open.dao.vo.ApiConfig">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="def_api_id" jdbcType="INTEGER" property="defApiId" />
    <result column="sys_code" jdbcType="VARCHAR" property="sysCode" />
    <result column="sys_name" jdbcType="VARCHAR" property="sysName" />
    <result column="api_code" jdbcType="VARCHAR" property="apiCode" />
    <result column="option_type" jdbcType="VARCHAR" property="optionType" />
    <result column="api_name" jdbcType="VARCHAR" property="apiName" />
    <result column="client_sync" jdbcType="BIT" property="clientSync" />
    <result column="server_sync" jdbcType="BIT" property="serverSync" />
    <result column="api_direct" jdbcType="VARCHAR" property="apiDirect" />
    <result column="save_request_flag" jdbcType="BIT" property="saveRequestFlag" />
    <result column="request_biz_code" jdbcType="VARCHAR" property="requestBizCode" />
    <result column="request_transfer_name" jdbcType="VARCHAR" property="requestTransferName" />
    <result column="request_protocol" jdbcType="VARCHAR" property="requestProtocol" />
    <result column="request_url" jdbcType="VARCHAR" property="requestUrl" />
    <result column="request_client_encrypt" jdbcType="BIT" property="requestClientEncrypt" />
    <result column="request_server_encrypt" jdbcType="BIT" property="requestServerEncrypt" />
    <result column="request_client_sign" jdbcType="BIT" property="requestClientSign" />
    <result column="request_server_sign" jdbcType="BIT" property="requestServerSign" />
    <result column="callback_biz_code" jdbcType="VARCHAR" property="callbackBizCode" />
    <result column="callback_transfer_name" jdbcType="VARCHAR" property="callbackTransferName" />
    <result column="callback_protocol" jdbcType="VARCHAR" property="callbackProtocol" />
    <result column="callback_url" jdbcType="VARCHAR" property="callbackUrl" />
    <result column="callback_client_encrypt" jdbcType="BIT" property="callbackClientEncrypt" />
    <result column="callback_server_encrypt" jdbcType="BIT" property="callbackServerEncrypt" />
    <result column="callback_client_sign" jdbcType="BIT" property="callbackClientSign" />
    <result column="callback_server_sign" jdbcType="BIT" property="callbackServerSign" />
    <result column="query_biz_code" jdbcType="VARCHAR" property="queryBizCode" />
    <result column="query_transfer_name" jdbcType="VARCHAR" property="queryTransferName" />
    <result column="query_protocol" jdbcType="VARCHAR" property="queryProtocol" />
    <result column="query_url" jdbcType="VARCHAR" property="queryUrl" />
    <result column="query_client_encrypt" jdbcType="BIT" property="queryClientEncrypt" />
    <result column="query_server_encrypt" jdbcType="BIT" property="queryServerEncrypt" />
    <result column="query_client_sign" jdbcType="BIT" property="queryClientSign" />
    <result column="query_server_sign" jdbcType="BIT" property="queryServerSign" />
    <result column="rc_normal" jdbcType="VARCHAR" property="rcNormal" />
    <result column="rc_need_redo" jdbcType="VARCHAR" property="rcNeedRedo" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>

  <select id="queryApiByBizCode" resultMap="BaseResultMap">
    select * from op_api_config
    where sys_code = #{sysCode} and del_flg = 0
    and (request_biz_code = #{bizCode} or query_biz_code = #{bizCode} or callback_biz_code = #{bizCode})
    order by update_time desc
    limit 1
  </select>

  <select id="queryList" parameterType="com.ecommerce.open.api.dto.apicenter.config.ApiConfigQueryDTO"
          resultType="com.ecommerce.open.api.dto.apicenter.config.ApiConfigListDTO">
    select
      def_api_id as defApiId,
      sys_code as sysCode,
      sys_name as sysName,
      api_code as apiCode,
      option_type as optionType,
      api_name as apiName,
      client_sync as clientSync,
      server_sync as serverSync,
      api_direct as apiDirect,
      save_request_flag as saveRequestFlag,
      request_biz_code as requestBizCode,
      callback_biz_code as callbackBizCode,
      query_biz_code as queryBizCode,
      rc_normal as rcNormal,
      rc_need_redo as rcNeedRedo
    from op_api_config
    where del_flg = 0
    <if test="sysCode != null and sysCode != ''">
      and sys_code = #{sysCode}
    </if>
    <if test="sysName != null and sysName != ''">
      and sys_name like concat('%', #{sysName}, '%')
    </if>
    <if test="apiCode != null and apiCode != ''">
      and api_code = #{apiCode}
    </if>
    <if test="optionType != null and optionType != ''">
      and option_type = #{optionType}
    </if>
    <if test="apiName != null and apiName != ''">
      and api_name like concat('%', #{apiName}, '%')
    </if>
    <if test="apiDirect != null and apiDirect != ''">
      and api_direct = #{apiDirect}
    </if>
    <if test="bizCode != null and bizCode != ''">
      and (request_biz_code = #{bizCode} or query_biz_code = #{bizCode} or callback_biz_code = #{bizCode})
    </if>
    order by update_time desc
  </select>

</mapper>