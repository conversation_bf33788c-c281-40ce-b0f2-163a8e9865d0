<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.open.dao.mapper.ApiParamConfigMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.open.dao.vo.ApiParamConfig">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="param_config_id" jdbcType="INTEGER" property="paramConfigId" />
    <result column="def_api_id" jdbcType="INTEGER" property="defApiId" />
    <result column="sys_code" jdbcType="VARCHAR" property="sysCode" />
    <result column="biz_code" jdbcType="VARCHAR" property="bizCode" />
    <result column="param_key" jdbcType="VARCHAR" property="paramKey" />
    <result column="param_value" jdbcType="VARCHAR" property="paramValue" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>

  <select id="selectBySysAndBizCode" resultMap="BaseResultMap">
    select * from op_api_param_config
    where sys_code = #{sysCode} and biz_code = #{bizCode} and del_flg = 0
  </select>

  <select id="queryList" resultType="com.ecommerce.open.api.dto.apicenter.config.ApiParamConfigResultDTO" parameterType="com.ecommerce.open.api.dto.apicenter.config.ApiParamConfigQueryDTO">
    select
      param_config_id as paramConfigId,
      sys_code as sysCode,
      biz_code as bizCode,
      param_key as paramKey,
      param_value as paramValue,
      memo
    from op_api_param_config
    where del_flg = 0
    <if test="sysCode != null and sysCode != ''">
      and sys_code = #{sysCode}
    </if>
    <if test="bizCode != null and bizCode != ''">
      and biz_code = #{bizCode}
    </if>
    <if test="paramKey != null and paramKey != ''">
      and param_key = #{paramKey}
    </if>
  </select>

</mapper>