<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.open.dao.mapper.ApiPendingMessageMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.open.dao.vo.ApiPendingMessage">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="pending_id" jdbcType="INTEGER" property="pendingId" />
    <result column="sys_code" jdbcType="VARCHAR" property="sysCode" />
    <result column="api_code" jdbcType="VARCHAR" property="apiCode" />
    <result column="biz_code" jdbcType="VARCHAR" property="bizCode" />
    <result column="request_no" jdbcType="VARCHAR" property="requestNo" />
    <result column="request_type" jdbcType="VARCHAR" property="requestType" />
    <result column="send_status" jdbcType="VARCHAR" property="sendStatus" />
    <result column="send_count" jdbcType="INTEGER" property="sendCount" />
    <result column="ext1" jdbcType="VARCHAR" property="ext1" />
    <result column="ext2" jdbcType="VARCHAR" property="ext2" />
    <result column="ext3" jdbcType="VARCHAR" property="ext3" />
    <result column="ext4" jdbcType="VARCHAR" property="ext4" />
    <result column="ext5" jdbcType="VARCHAR" property="ext5" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="message" jdbcType="LONGVARCHAR" property="message" />
    <result column="response_message" jdbcType="LONGVARCHAR" property="responseMessage" />
    <result column="body" jdbcType="LONGVARCHAR" property="body" />
    <result column="response_body" jdbcType="LONGVARCHAR" property="responseBody" />
  </resultMap>
</mapper>