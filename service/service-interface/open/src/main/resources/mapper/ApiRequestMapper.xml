<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.open.dao.mapper.ApiRequestMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.open.dao.vo.ApiRequest">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="request_log_id" jdbcType="INTEGER" property="requestLogId" />
    <result column="def_api_id" jdbcType="INTEGER" property="defApiId" />
    <result column="sys_code" jdbcType="VARCHAR" property="sysCode" />
    <result column="api_code" jdbcType="VARCHAR" property="apiCode" />
    <result column="biz_code" jdbcType="VARCHAR" property="bizCode" />
    <result column="request_type" jdbcType="VARCHAR" property="requestType" />
    <result column="request_start_time" jdbcType="TIMESTAMP" property="requestStartTime" />
    <result column="request_status" jdbcType="VARCHAR" property="requestStatus" />
    <result column="process_result" jdbcType="VARCHAR" property="processResult" />
    <result column="request_no" jdbcType="VARCHAR" property="requestNo" />
    <result column="event_code" jdbcType="VARCHAR" property="eventCode" />
    <result column="option_type" jdbcType="VARCHAR" property="optionType" />
    <result column="client_bill_code" jdbcType="VARCHAR" property="clientBillCode" />
    <result column="server_bill_code" jdbcType="VARCHAR" property="serverBillCode" />
    <result column="client_seller_id" jdbcType="VARCHAR" property="clientSellerId" />
    <result column="server_seller_id" jdbcType="VARCHAR" property="serverSellerId" />
    <result column="client_operator" jdbcType="VARCHAR" property="clientOperator" />
    <result column="client_operator_name" jdbcType="VARCHAR" property="clientOperatorName" />
    <result column="response_code" jdbcType="VARCHAR" property="responseCode" />
    <result column="response_message" jdbcType="VARCHAR" property="responseMessage" />
    <result column="response_time" jdbcType="TIMESTAMP" property="responseTime" />
    <result column="need_callback" jdbcType="BIT" property="needCallback" />
    <result column="callback_request_no" jdbcType="VARCHAR" property="callbackRequestNo" />
    <result column="callback_status" jdbcType="VARCHAR" property="callbackStatus" />
    <result column="callback_code" jdbcType="VARCHAR" property="callbackCode" />
    <result column="callback_time" jdbcType="TIMESTAMP" property="callbackTime" />
    <result column="callback_message" jdbcType="VARCHAR" property="callbackMessage" />
    <result column="retry_time" jdbcType="INTEGER" property="retryTime" />
    <result column="last_retry_time" jdbcType="TIMESTAMP" property="lastRetryTime" />
    <result column="member_id" jdbcType="VARCHAR" property="memberId" />
    <result column="syn_time" jdbcType="INTEGER" property="synTime" />
    <result column="asyn_time" jdbcType="INTEGER" property="asynTime" />
    <result column="ext1" jdbcType="VARCHAR" property="ext1" />
    <result column="ext3" jdbcType="VARCHAR" property="ext3" />
    <result column="ext4" jdbcType="VARCHAR" property="ext4" />
    <result column="ext5" jdbcType="VARCHAR" property="ext5" />
    <result column="ext2" jdbcType="VARCHAR" property="ext2" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="erp_operator" jdbcType="VARCHAR" property="erpOperator" />
    <result column="erp_operator_name" jdbcType="VARCHAR" property="erpOperatorName" />
  </resultMap>

  <select id="queryApiRequestByCond" parameterType="com.ecommerce.open.api.dto.apicenter.ApiRequestCondDTO"
          resultType="com.ecommerce.open.api.dto.apicenter.ApiRequestListDTO">
    SELECT
      biz_code AS bizCode,
      request_no AS requestNo,
      request_status AS requestStatus,
      request_start_time AS requestStartTime,
      response_time AS responseTime,
      response_code AS responseCode,
      option_type AS optionType,
      client_bill_code AS clientBillCode,
      callback_request_no AS callbackRequestNo,
      callback_time AS callbackTime,
      callback_code AS callbackCode,
      server_bill_code AS serverBillCode,
      callback_status AS callbackStatus,
      request_log_id AS requestLogId,
      sys_code AS sysCode,
      api_code AS apiCode
    FROM op_api_request
    WHERE
    del_flg = 0
    <if test="bizCode != null and bizCode != ''">
      AND biz_code = #{bizCode}
    </if>
    <if test="requestNo != null and requestNo != ''">
      AND request_no = #{requestNo}
    </if>
    <if test="callbackRequestNo != null and callbackRequestNo != ''">
      AND callback_request_no = #{callbackRequestNo}
    </if>
    <if test="clientBillCode != null and clientBillCode != ''">
      AND client_bill_code = #{clientBillCode}
    </if>
    <if test="serverBillCode != null and serverBillCode != ''">
      AND server_bill_code = #{serverBillCode}
    </if>
    <if test="requestStatus != null and requestStatus != ''">
      AND request_status = #{requestStatus}
    </if>
    <if test="callbackStatus != null and callbackStatus != ''">
      AND callback_status = #{callbackStatus}
    </if>
    ORDER BY update_time DESC
  </select>

</mapper>