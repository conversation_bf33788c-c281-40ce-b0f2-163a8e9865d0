<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.open.dao.mapper.ApiRequestResultMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.open.dao.vo.ApiRequestResult">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="result_id" jdbcType="INTEGER" property="resultId" />
    <result column="client_bill_code" jdbcType="VARCHAR" property="clientBillCode" />
    <result column="server_bill_code" jdbcType="VARCHAR" property="serverBillCode" />
    <result column="seller_id" jdbcType="VARCHAR" property="sellerId" />
    <result column="option_type" jdbcType="VARCHAR" property="optionType" />
    <result column="request_no" jdbcType="VARCHAR" property="requestNo" />
    <result column="first_request_time" jdbcType="TIMESTAMP" property="firstRequestTime" />
    <result column="final_time" jdbcType="TIMESTAMP" property="finalTime" />
    <result column="request_status" jdbcType="VARCHAR" property="requestStatus" />
    <result column="request_type" jdbcType="VARCHAR" property="requestType" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>
</mapper>