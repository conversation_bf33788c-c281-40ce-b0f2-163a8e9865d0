<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.open.dao.mapper.BizRouteMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.open.dao.vo.BizRoute">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="biz_route_id" jdbcType="VARCHAR" property="bizRouteId" />
    <result column="ec_biz_code" jdbcType="VARCHAR" property="ecBizCode" />
    <result column="external_sys_code" jdbcType="VARCHAR" property="externalSysCode" />
    <result column="external_biz_code" jdbcType="VARCHAR" property="externalBizCode" />
    <result column="note" jdbcType="VARCHAR" property="note" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>