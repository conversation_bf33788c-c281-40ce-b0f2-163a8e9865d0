<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.open.dao.mapper.CarryContractMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.open.dao.vo.CarryContract">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="contract_id" jdbcType="VARCHAR" property="contractId" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="carrier" jdbcType="VARCHAR" property="carrier" />
    <result column="date_signed" jdbcType="TIMESTAMP" property="dateSigned" />
    <result column="contract_period" jdbcType="INTEGER" property="contractPeriod" />
    <result column="goods_type" jdbcType="VARCHAR" property="goodsType" />
    <result column="cargo_freight" jdbcType="DECIMAL" property="cargoFreight" />
    <result column="place_of_loading_province" jdbcType="VARCHAR" property="placeOfLoadingProvince" />
    <result column="place_of_loading_city" jdbcType="VARCHAR" property="placeOfLoadingCity" />
    <result column="place_of_loading_district" jdbcType="VARCHAR" property="placeOfLoadingDistrict" />
    <result column="place_of_receipt_province" jdbcType="VARCHAR" property="placeOfReceiptProvince" />
    <result column="place_of_receipt_city" jdbcType="VARCHAR" property="placeOfReceiptCity" />
    <result column="place_of_receipt_district" jdbcType="VARCHAR" property="placeOfReceiptDistrict" />
    <result column="transport_distance" jdbcType="DECIMAL" property="transportDistance" />
    <result column="contract_amount" jdbcType="DECIMAL" property="contractAmount" />
    <result column="insurance_amount" jdbcType="DECIMAL" property="insuranceAmount" />
    <result column="business_type" jdbcType="VARCHAR" property="businessType" />
    <result column="type_of_shipping" jdbcType="VARCHAR" property="typeOfShipping" />
    <result column="transportation_comb" jdbcType="VARCHAR" property="transportationComb" />
    <result column="contract_no" jdbcType="VARCHAR" property="contractNo" />
    <result column="contract_type" jdbcType="INTEGER" property="contractType" />
    <result column="contract_sub_type" jdbcType="INTEGER" property="contractSubType" />
    <result column="contract_term" jdbcType="VARCHAR" property="contractTerm" />
    <result column="sender_code" jdbcType="VARCHAR" property="senderCode" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <select id="selectByStatus" resultMap="BaseResultMap">
    select occ.*
    from op_carry_contract occ
    where occ.del_flg = 0
    and occ.status in
    <foreach collection="statusList" item="sit" index="sin" open="(" separator="," close=")">
      #{sit}
    </foreach>
    order by occ.update_time
    limit #{size}
  </select>

  <update id="updateStatusForContract">
    update op_carry_contract
    set update_time = current_timestamp()
    ,status = #{newStatus}
    where del_flg = 0
    and status in
    <foreach collection="oldStatusList" item="oit" index="oin" open="(" separator="," close=")">
      #{oit}
    </foreach>
    and contract_id in
    <foreach collection="contractIdList" item="cit" index="cin" open="(" separator="," close=")">
      #{cit}
    </foreach>
  </update>

  <update id="updateResultForAll">
    update op_carry_contract
    set update_time = current_timestamp()
    , status = #{newStatus}
    <if test="code != null">
      ,code = #{code}
    </if>
    <if test="description != null">
      ,description = #{description}
    </if>
    where del_flg = 0
    and status = #{oldStatus}
    and contract_id in
    <foreach collection="contractIdList" item="sit" index="sin" open="(" separator="," close=")">
      #{sit}
    </foreach>
  </update>

  <update id="updateResultForOne">
    update op_carry_contract
    set update_time = current_timestamp()
    , status = #{newStatus}
    <if test="code != null">
      ,code = #{code}
    </if>
    <if test="description != null">
      ,description = #{description}
    </if>
    where del_flg = 0
    and status = #{oldStatus}
    and contract_id = #{contractId}
  </update>

  <select id="selectCarryContractList" parameterType="com.ecommerce.open.api.dto.carry.carryContract.CarryContractQueryDTO" resultType="com.ecommerce.open.api.dto.carry.carryContract.CarryContractListDTO">
    select
    contract_id as contractId,
    status as status,
    description as description,
    contract_no as contractNo,
    date_signed as dateSigned,
    contract_type as contractType,
    update_time as updateTime
    from op_carry_contract
    where del_flg = 0
    <if test="status != null and status != ''">
      and status = #{status}
    </if>
    <if test="contractNo != null and contractNo != ''">
      and contract_no like concat('%', #{contractNo}, '%')
    </if>
    order by update_time
  </select>
</mapper>