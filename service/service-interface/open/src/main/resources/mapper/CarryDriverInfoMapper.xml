<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.open.dao.mapper.CarryDriverInfoMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.open.dao.vo.CarryDriverInfo">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="driver_id" jdbcType="VARCHAR" property="driverId" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="name_of_person" jdbcType="VARCHAR" property="nameOfPerson" />
    <result column="driving_license" jdbcType="VARCHAR" property="drivingLicense" />
    <result column="vehicle_class" jdbcType="VARCHAR" property="vehicleClass" />
    <result column="issuing_organizations" jdbcType="VARCHAR" property="issuingOrganizations" />
    <result column="valid_period_from" jdbcType="DATE" property="validPeriodFrom" />
    <result column="valid_period_to" jdbcType="DATE" property="validPeriodTo" />
    <result column="qualification_certificate_number" jdbcType="VARCHAR" property="qualificationCertificateNumber" />
    <result column="telephone_number" jdbcType="VARCHAR" property="telephoneNumber" />
    <result column="qualification_certificate_type" jdbcType="VARCHAR" property="qualificationCertificateType" />
    <result column="aduit_time" jdbcType="TIMESTAMP" property="aduitTime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="carrier" jdbcType="VARCHAR" property="carrier" />
    <result column="sender_code" jdbcType="VARCHAR" property="senderCode" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <select id="selectByStatus" resultMap="BaseResultMap">
    select scdi.*
    from op_carry_driver_info scdi
    where scdi.del_flg = 0
    and scdi.status in
    <foreach collection="statusList" index="ssin" item="ssit" open="(" separator="," close=")">
      #{ssit}
    </foreach>
    order by update_time
    limit #{size}
  </select>

  <update id="updateStatusForDriver">
    update op_carry_driver_info
    set update_time = current_timestamp(),
    status = #{newStatus}
    where del_flg = 0
    and driver_id in
    <foreach collection="driverIdList" item="dit" index="din" open="(" separator="," close=")">
      #{dit}
    </foreach>
    and status in
    <foreach collection="oldStatusList" item="oit" index="oin" open="(" separator="," close=")">
      #{oit}
    </foreach>
  </update>

  <update id="updateResultForAll">
    update op_carry_driver_info
    set update_time = current_timestamp()
    , status = #{newStatus}
    <if test="code != null">
      ,code = #{code}
    </if>
    <if test="description != null">
      ,description = #{description}
    </if>
    where del_flg = 0
    and status = #{oldStatus}
    and driver_id in
    <foreach collection="driverIdList" item="dit" index="din" open="(" separator="," close=")">
      #{dit}
    </foreach>
  </update>

  <update id="updateResultForOne">
    update op_carry_driver_info
    set update_time = current_timestamp()
    , status = #{newStatus}
    <if test="code != null">
      ,code = #{code}
    </if>
    <if test="description != null">
      ,description = #{description}
    </if>
    where del_flg = 0
    and status = #{oldStatus}
    and driver_id = #{driverId}
  </update>

  <select id="selectCarryDriverList" parameterType="com.ecommerce.open.api.dto.carry.carryDriver.CarryDriverQueryDTO" resultType="com.ecommerce.open.api.dto.carry.carryDriver.CarryDriverListDTO" >
    select
    driver_id as driverId,
    status as status,
    description as description,
    name_of_person as nameOfPerson,
    driving_license as drivingLicense,
    telephone_number as telephoneNumber,
    aduit_time as aduitTime,
    update_time as updateTime,
    remark as remark
    from op_carry_driver_info
    where del_flg = 0
    <if test="status != null and status != ''">
      and status = #{status}
    </if>
    <if test="nameOfPerson != null and nameOfPerson != ''">
      and name_of_person like concat('%', #{nameOfPerson}, '%')
    </if>
    order by update_time
  </select>
</mapper>