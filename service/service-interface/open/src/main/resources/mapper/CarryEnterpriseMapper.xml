<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.open.dao.mapper.CarryEnterpriseMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.open.dao.vo.CarryEnterprise">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="enterprise_id" jdbcType="VARCHAR" property="enterpriseId" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="identify" jdbcType="VARCHAR" property="identify" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="sender_code" jdbcType="VARCHAR" property="senderCode" />
    <result column="carrier" jdbcType="VARCHAR" property="carrier" />
    <result column="credit_number" jdbcType="VARCHAR" property="creditNumber" />
    <result column="registered_place" jdbcType="VARCHAR" property="registeredPlace" />
    <result column="area_code" jdbcType="VARCHAR" property="areaCode" />
    <result column="registered_capital" jdbcType="DECIMAL" property="registeredCapital" />
    <result column="register_date" jdbcType="CHAR" property="registerDate" />
    <result column="applicant" jdbcType="VARCHAR" property="applicant" />
    <result column="run_scope" jdbcType="VARCHAR" property="runScope" />
    <result column="permit_number" jdbcType="VARCHAR" property="permitNumber" />
    <result column="fax_no" jdbcType="VARCHAR" property="faxNo" />
    <result column="legal_representative" jdbcType="VARCHAR" property="legalRepresentative" />
    <result column="legaler_tel" jdbcType="VARCHAR" property="legalerTel" />
    <result column="carrier_platform" jdbcType="VARCHAR" property="carrierPlatform" />
    <result column="business_type" jdbcType="VARCHAR" property="businessType" />
    <result column="cargo_type" jdbcType="VARCHAR" property="cargoType" />
    <result column="management_area" jdbcType="VARCHAR" property="managementArea" />
    <result column="branche_count" jdbcType="INTEGER" property="brancheCount" />
    <result column="report_count" jdbcType="INTEGER" property="reportCount" />
    <result column="actual_carrier_count" jdbcType="INTEGER" property="actualCarrierCount" />
    <result column="vehicle_count" jdbcType="INTEGER" property="vehicleCount" />
    <result column="driver_count" jdbcType="INTEGER" property="driverCount" />
    <result column="contacts" jdbcType="VARCHAR" property="contacts" />
    <result column="contact_phone" jdbcType="VARCHAR" property="contactPhone" />
    <result column="contact_mobile" jdbcType="VARCHAR" property="contactMobile" />
    <result column="enterprise_create_time" jdbcType="CHAR" property="enterpriseCreateTime" />
    <result column="ipcnum" jdbcType="VARCHAR" property="ipcnum" />
    <result column="psnrnum" jdbcType="VARCHAR" property="psnrnum" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <select id="selectCarryEnterpriseList" parameterType="com.ecommerce.open.api.dto.carry.carryEnterprise.CarryEnterpriseQueryDTO" resultType="com.ecommerce.open.api.dto.carry.carryEnterprise.CarryEnterpriseListDTO">
    select
    enterprise_id as enterpriseId,
    status as status,
    description as description,
    sender_code as senderCode,
    carrier as carrier,
    register_date as registerDate,
    legal_representative as legalRepresentative,
    update_time as updateTime
    from op_carry_enterprise
    where del_flg = 0
    <if test="status != null and status != ''">
      and status = #{status}
    </if>
    <if test="carrier != null and carrier != ''">
      and carrier like concat('%', #{carrier}, '%')
    </if>
    order by update_time
  </select>

</mapper>