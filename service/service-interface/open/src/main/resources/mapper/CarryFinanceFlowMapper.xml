<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.open.dao.mapper.CarryFinanceFlowMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.open.dao.vo.CarryFinanceFlow">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="finance_flow_id" jdbcType="VARCHAR" property="financeFlowId" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="carrier" jdbcType="VARCHAR" property="carrier" />
    <result column="actual_carrier" jdbcType="VARCHAR" property="actualCarrier" />
    <result column="actual_carrier_id" jdbcType="VARCHAR" property="actualCarrierId" />
    <result column="sender_code" jdbcType="VARCHAR" property="senderCode" />
    <result column="document_number" jdbcType="VARCHAR" property="documentNumber" />
    <result column="document_name" jdbcType="VARCHAR" property="documentName" />
    <result column="rec_carrier" jdbcType="VARCHAR" property="recCarrier" />
    <result column="vehicle_number" jdbcType="VARCHAR" property="vehicleNumber" />
    <result column="vehicle_plate_color_code" jdbcType="INTEGER" property="vehiclePlateColorCode" />
    <result column="pstatus" jdbcType="INTEGER" property="pstatus" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <select id="selectByStatus" resultMap="BaseResultMap">
    select ocff.*
    from op_carry_finance_flow ocff
    where ocff.del_flg = 0
    and ocff.status in
    <foreach collection="statusList" index="ssin" item="ssit" open="(" separator="," close=")">
      #{ssit}
    </foreach>
    order by update_time
    limit #{size}
  </select>

  <update id="updateStatusForFinanceFlow">
    update op_carry_finance_flow
    set update_time = current_timestamp(),
    status = #{newStatus}
    where del_flg = 0
    and finance_flow_id in
    <foreach collection="financeFlowIdList" item="fit" index="fin" open="(" separator="," close=")">
      #{fit}
    </foreach>
    and status in
    <foreach collection="oldStatusList" item="oit" index="oin" open="(" separator="," close=")">
      #{oit}
    </foreach>
  </update>

  <update id="updateResultForAll">
    update op_carry_finance_flow
    set update_time = current_timestamp()
    , status = #{newStatus}
    <if test="code != null">
      ,code = #{code}
    </if>
    <if test="description != null">
      ,description = #{description}
    </if>
    where del_flg = 0
    and status = #{oldStatus}
    and finance_flow_id in
    <foreach collection="financeFlowIdList" item="fit" index="fin" open="(" separator="," close=")">
      #{fit}
    </foreach>
  </update>

  <update id="updateResultForOne">
    update op_carry_finance_flow
    set update_time = current_timestamp()
    , status = #{newStatus}
    <if test="code != null">
      ,code = #{code}
    </if>
    <if test="description != null">
      ,description = #{description}
    </if>
    where del_flg = 0
    and status = #{oldStatus}
    and finance_flow_id = #{financeFlowId}
  </update>

  <select id="selectCarryFinanceFlowList" parameterType="com.ecommerce.open.api.dto.carry.carryFinanceFlow.CarryFinanceFlowQueryDTO" resultType="com.ecommerce.open.api.dto.carry.carryFinanceFlow.CarryFinanceFlowListDTO">
    select
    finance_flow_id as financeFlowId,
    status as status,
    description as description,
    document_number as documentNumber,
    rec_carrier as recCarrier,
    vehicle_number as vehicleNumber,
    update_time as updateTime
    from op_carry_finance_flow
    where del_flg = 0
    <if test="status != null and status != ''">
      and status = #{status}
    </if>
    <if test="documentNumber != null and documentNumber != ''">
      and document_number like concat('%', #{documentNumber}, '%')
    </if>
    <if test="recCarrier != null and recCarrier != ''">
      and rec_carrier like concat('%', #{recCarrier}, '%')
    </if>
    order by update_time
  </select>

</mapper>