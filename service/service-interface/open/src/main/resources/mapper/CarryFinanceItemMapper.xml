<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.open.dao.mapper.CarryFinanceItemMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.open.dao.vo.CarryFinanceItem">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="financial_item_id" jdbcType="VARCHAR" property="financialItemId" />
    <result column="finance_flow_id" jdbcType="VARCHAR" property="financeFlowId" />
    <result column="payment_means_code" jdbcType="VARCHAR" property="paymentMeansCode" />
    <result column="recipient" jdbcType="VARCHAR" property="recipient" />
    <result column="receipt_account" jdbcType="VARCHAR" property="receiptAccount" />
    <result column="bank_code" jdbcType="VARCHAR" property="bankCode" />
    <result column="sequence_code" jdbcType="VARCHAR" property="sequenceCode" />
    <result column="monetary_amount" jdbcType="DECIMAL" property="monetaryAmount" />
    <result column="date_time" jdbcType="TIMESTAMP" property="dateTime" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>