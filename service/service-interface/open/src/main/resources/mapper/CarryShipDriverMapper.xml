<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.open.dao.mapper.CarryShipDriverMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.open.dao.vo.CarryShipDriver">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="shipping_driver_id" jdbcType="VARCHAR" property="shippingDriverId" />
    <result column="ship_record_id" jdbcType="VARCHAR" property="shipRecordId" />
    <result column="name_of_person" jdbcType="VARCHAR" property="nameOfPerson" />
    <result column="driving_license" jdbcType="VARCHAR" property="drivingLicense" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>