<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.open.dao.mapper.CarryShipGoodsMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.open.dao.vo.CarryShipGoods">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="goods_record_id" jdbcType="VARCHAR" property="goodsRecordId" />
    <result column="ship_record_id" jdbcType="VARCHAR" property="shipRecordId" />
    <result column="description_of_goods" jdbcType="VARCHAR" property="descriptionOfGoods" />
    <result column="cargo_type_classification_code" jdbcType="VARCHAR" property="cargoTypeClassificationCode" />
    <result column="goods_item_gross_weight" jdbcType="DECIMAL" property="goodsItemGrossWeight" />
    <result column="cube" jdbcType="DECIMAL" property="cube" />
    <result column="total_number_of_packages" jdbcType="INTEGER" property="totalNumberOfPackages" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>