<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.open.dao.mapper.CarryShipNoteMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.open.dao.vo.CarryShipNote">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="ship_record_id" jdbcType="VARCHAR" property="shipRecordId" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="original_document_number" jdbcType="VARCHAR" property="originalDocumentNumber" />
    <result column="shipping_note_number" jdbcType="VARCHAR" property="shippingNoteNumber" />
    <result column="serial_number" jdbcType="VARCHAR" property="serialNumber" />
    <result column="vehicle_count" jdbcType="INTEGER" property="vehicleCount" />
    <result column="transport_type_code" jdbcType="INTEGER" property="transportTypeCode" />
    <result column="transportation_comb" jdbcType="VARCHAR" property="transportationComb" />
    <result column="carrier" jdbcType="VARCHAR" property="carrier" />
    <result column="unified_social_credit_identifier" jdbcType="VARCHAR" property="unifiedSocialCreditIdentifier" />
    <result column="permit_number" jdbcType="VARCHAR" property="permitNumber" />
    <result column="consignment_date_time" jdbcType="TIMESTAMP" property="consignmentDateTime" />
    <result column="business_type_code" jdbcType="VARCHAR" property="businessTypeCode" />
    <result column="despatch_actual_date_time" jdbcType="TIMESTAMP" property="despatchActualDateTime" />
    <result column="goods_receipt_date_time" jdbcType="TIMESTAMP" property="goodsReceiptDateTime" />
    <result column="consignor" jdbcType="VARCHAR" property="consignor" />
    <result column="consignor_id" jdbcType="VARCHAR" property="consignorId" />
    <result column="place_of_loading_deli_province" jdbcType="VARCHAR" property="placeOfLoadingDeliProvince" />
    <result column="place_of_loading_deli_city" jdbcType="VARCHAR" property="placeOfLoadingDeliCity" />
    <result column="place_of_loading_deli_district" jdbcType="VARCHAR" property="placeOfLoadingDeliDistrict" />
    <result column="place_of_loading_deli_address" jdbcType="VARCHAR" property="placeOfLoadingDeliAddress" />
    <result column="deli_country_subdivision_code" jdbcType="VARCHAR" property="deliCountrySubdivisionCode" />
    <result column="consignee" jdbcType="VARCHAR" property="consignee" />
    <result column="consignee_id" jdbcType="VARCHAR" property="consigneeId" />
    <result column="goods_receipt_place_province" jdbcType="VARCHAR" property="goodsReceiptPlaceProvince" />
    <result column="goods_receipt_place_city" jdbcType="VARCHAR" property="goodsReceiptPlaceCity" />
    <result column="goods_receipt_place_district" jdbcType="VARCHAR" property="goodsReceiptPlaceDistrict" />
    <result column="goods_receipt_place_address" jdbcType="VARCHAR" property="goodsReceiptPlaceAddress" />
    <result column="receipt_country_subdivision_code" jdbcType="VARCHAR" property="receiptCountrySubdivisionCode" />
    <result column="total_monetary_amount" jdbcType="VARCHAR" property="totalMonetaryAmount" />
    <result column="vehicle_number" jdbcType="VARCHAR" property="vehicleNumber" />
    <result column="vehicle_plate_color_code" jdbcType="VARCHAR" property="vehiclePlateColorCode" />
    <result column="vehicle_despatch_actual_date_time" jdbcType="TIMESTAMP" property="vehicleDespatchActualDateTime" />
    <result column="vehicle_goods_receipt_date_time" jdbcType="TIMESTAMP" property="vehicleGoodsReceiptDateTime" />
    <result column="vehicle_place_of_loading_deli_province" jdbcType="VARCHAR" property="vehiclePlaceOfLoadingDeliProvince" />
    <result column="vehicle_place_of_loading_deli_city" jdbcType="VARCHAR" property="vehiclePlaceOfLoadingDeliCity" />
    <result column="vehicle_place_of_loading_deli_district" jdbcType="VARCHAR" property="vehiclePlaceOfLoadingDeliDistrict" />
    <result column="vehicle_place_of_loading_deli_address" jdbcType="VARCHAR" property="vehiclePlaceOfLoadingDeliAddress" />
    <result column="vehicle_deli_country_subdivision_code" jdbcType="VARCHAR" property="vehicleDeliCountrySubdivisionCode" />
    <result column="actual_carrier" jdbcType="VARCHAR" property="actualCarrier" />
    <result column="actual_type" jdbcType="VARCHAR" property="actualType" />
    <result column="carrier_register_place" jdbcType="VARCHAR" property="carrierRegisterPlace" />
    <result column="carrier_permit_number" jdbcType="VARCHAR" property="carrierPermitNumber" />
    <result column="actual_carrier_id" jdbcType="VARCHAR" property="actualCarrierId" />
    <result column="carrier_business_range" jdbcType="VARCHAR" property="carrierBusinessRange" />
    <result column="carrier_aduit_time" jdbcType="TIMESTAMP" property="carrierAduitTime" />
    <result column="travel_distance" jdbcType="DECIMAL" property="travelDistance" />
    <result column="transport_distance" jdbcType="DECIMAL" property="transportDistance" />
    <result column="policy_number" jdbcType="VARCHAR" property="policyNumber" />
    <result column="insurance_company_code" jdbcType="VARCHAR" property="insuranceCompanyCode" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="pstatus" jdbcType="INTEGER" property="pstatus" />
    <result column="free_text" jdbcType="VARCHAR" property="freeText" />
    <result column="sender_code" jdbcType="VARCHAR" property="senderCode" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <select id="selectByStatus" resultMap="BaseResultMap">
    select ocsn.*
    from op_carry_ship_note ocsn
    where ocsn.del_flg = 0
      and ocsn.status in
        <foreach collection="statusList" index="ssin" item="ssit" open="(" separator="," close=")">
          #{ssit}
        </foreach>
    order by update_time
    limit #{size}
  </select>

  <update id="updateStatusForShipNote">
    update op_carry_ship_note
    set update_time = current_timestamp(),
        status = #{newStatus}
    where del_flg = 0
    and ship_record_id in
    <foreach collection="shipRecordIdList" item="sit" index="sin" open="(" separator="," close=")">
      #{sit}
    </foreach>
    and status in
    <foreach collection="oldStatusList" item="oit" index="oin" open="(" separator="," close=")">
      #{oit}
    </foreach>
  </update>

  <update id="updateResultForAll">
    update op_carry_ship_note
    set update_time = current_timestamp()
    , status = #{newStatus}
    <if test="code != null">
      ,code = #{code}
    </if>
    <if test="description != null">
      ,description = #{description}
    </if>
    where del_flg = 0
    and status = #{oldStatus}
    and ship_record_id in
    <foreach collection="shipRecordIdList" item="sit" index="sin" open="(" separator="," close=")">
      #{sit}
    </foreach>
  </update>

  <update id="updateResultForOne">
    update op_carry_ship_note
    set update_time = current_timestamp()
    , status = #{newStatus}
    <if test="code != null">
      ,code = #{code}
    </if>
    <if test="description != null">
      ,description = #{description}
    </if>
    where del_flg = 0
    and status = #{oldStatus}
    and ship_record_id = #{shipRecordId}
  </update>

  <select id="selectCarryShipNoteList" parameterType="com.ecommerce.open.api.dto.carry.carryShipNote.CarryShipNoteQueryDTO" resultType="com.ecommerce.open.api.dto.carry.carryShipNote.CarryWaybillListDTO">
    select
    ocsn.ship_record_id as shipRecordId,
    ocsn.status as status,
    ocsn.description as description,
    ocsn.shipping_note_number as shippingNoteNumber,
    ocsn.consignor as consignor,
    ocsn.consignee as consignee,
    ocsn.actual_carrier as actualCarrier,
    ocsn.total_monetary_amount as totalMonetaryAmount,
    ocsn.vehicle_number as vehicleNumber,
    ocsn.vehicle_plate_color_code as vehiclePlateColorCode,
    ocsn.serial_number as serialNumber,
    update_time as updateTime
    from op_carry_ship_note ocsn
    where ocsn.del_flg = 0
    <if test="status != null and status != ''">
      and ocsn.status = #{status}
    </if>
    <if test="shippingNoteNumber != null and shippingNoteNumber != ''">
      and ocsn.shipping_note_number like concat('%', #{shippingNoteNumber}, '%')
    </if>
    order by ocsn.update_time
  </select>
</mapper>