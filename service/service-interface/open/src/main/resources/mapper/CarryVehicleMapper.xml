<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.open.dao.mapper.CarryVehicleMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.open.dao.vo.CarryVehicle">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="vehicle_id" jdbcType="VARCHAR" property="vehicleId" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="vehicle_number" jdbcType="VARCHAR" property="vehicleNumber" />
    <result column="vehicle_plate_color_code" jdbcType="VARCHAR" property="vehiclePlateColorCode" />
    <result column="vehicle_type" jdbcType="VARCHAR" property="vehicleType" />
    <result column="owner" jdbcType="VARCHAR" property="owner" />
    <result column="use_character" jdbcType="VARCHAR" property="useCharacter" />
    <result column="vin" jdbcType="VARCHAR" property="vin" />
    <result column="issuing_organizations" jdbcType="VARCHAR" property="issuingOrganizations" />
    <result column="register_date" jdbcType="DATE" property="registerDate" />
    <result column="issue_date" jdbcType="DATE" property="issueDate" />
    <result column="vehicle_energy_type" jdbcType="VARCHAR" property="vehicleEnergyType" />
    <result column="vehicle_tonnage" jdbcType="DECIMAL" property="vehicleTonnage" />
    <result column="gross_mass" jdbcType="DECIMAL" property="grossMass" />
    <result column="road_transport_certificate_number" jdbcType="VARCHAR" property="roadTransportCertificateNumber" />
    <result column="operating_permit_number" jdbcType="VARCHAR" property="operatingPermitNumber" />
    <result column="trailer_vehicle_plate_number" jdbcType="VARCHAR" property="trailerVehiclePlateNumber" />
    <result column="vehicle_anchored" jdbcType="VARCHAR" property="vehicleAnchored" />
    <result column="vehicle_aduit_time" jdbcType="TIMESTAMP" property="vehicleAduitTime" />
    <result column="vehicle_is_accident" jdbcType="INTEGER" property="vehicleIsAccident" />
    <result column="vehicle_is_accident_finished" jdbcType="INTEGER" property="vehicleIsAccidentFinished" />
    <result column="vehicle_is_insurance" jdbcType="INTEGER" property="vehicleIsInsurance" />
    <result column="vehicle_is_insurance_amount" jdbcType="DECIMAL" property="vehicleIsInsuranceAmount" />
    <result column="vehicle_is_service" jdbcType="INTEGER" property="vehicleIsService" />
    <result column="vehicle_is_service_finished" jdbcType="INTEGER" property="vehicleIsServiceFinished" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="carrier" jdbcType="VARCHAR" property="carrier" />
    <result column="sender_code" jdbcType="VARCHAR" property="senderCode" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <select id="selectByStatus" resultMap="BaseResultMap">
    select ocv.*
    from op_carry_vehicle ocv
    where ocv.del_flg = 0
    and ocv.status in
    <foreach collection="statusList" index="ssin" item="ssit" open="(" separator="," close=")">
      #{ssit}
    </foreach>
    order by update_time
    limit #{size}
  </select>

  <update id="updateStatusForVehicle">
    update op_carry_vehicle
    set update_time = current_timestamp(),
    status = #{newStatus}
    where del_flg = 0
    and vehicle_id in
    <foreach collection="vehicleIdList" item="vit" index="vin" open="(" separator="," close=")">
      #{vit}
    </foreach>
    and status in
    <foreach collection="oldStatusList" item="oit" index="oin" open="(" separator="," close=")">
      #{oit}
    </foreach>
  </update>

  <update id="updateResultForAll">
    update op_carry_vehicle
    set update_time = current_timestamp()
    , status = #{newStatus}
    <if test="code != null">
      ,code = #{code}
    </if>
    <if test="description != null">
      ,description = #{description}
    </if>
    where del_flg = 0
    and status = #{oldStatus}
    and vehicle_id in
    <foreach collection="vehicleIdList" item="vit" index="vin" open="(" separator="," close=")">
      #{vit}
    </foreach>
  </update>

  <update id="updateResultForOne">
    update op_carry_vehicle
    set update_time = current_timestamp()
    , status = #{newStatus}
    <if test="code != null">
      ,code = #{code}
    </if>
    <if test="description != null">
      ,description = #{description}
    </if>
    where del_flg = 0
    and status = #{oldStatus}
    and vehicle_id = #{vehicleId}
  </update>

  <select id="selectCarryVehicleList" parameterType="com.ecommerce.open.api.dto.carry.carryVehicle.CarryVehicleQueryDTO" resultType="com.ecommerce.open.api.dto.carry.carryVehicle.CarryVehicleListDTO" >
    select
    vehicle_id as vehicleId,
    status as status,
    description as description,
    vehicle_number as vehicleNumber,
    vehicle_tonnage as vehicleTonnage,
    gross_mass as grossMass,
    vehicle_anchored as vehicleAnchored,
    vehicle_aduit_time as vehicleAduitTime,
    update_time as updateTime,
    remark as remark
    from op_carry_vehicle
    where del_flg = 0
    <if test="status != null and status != ''">
      and status = #{status}
    </if>
    <if test="vehicleNumber != null and vehicleNumber != ''">
      and vehicle_number like concat('%', #{vehicleNumber}, '%')
    </if>
    order by update_time
  </select>
</mapper>