<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.open.dao.mapper.ERPConfigMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.open.dao.vo.ERPConfig">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="erpconfig_id" jdbcType="VARCHAR" property="erpconfigId" />
    <result column="erp_name" jdbcType="VARCHAR" property="erpName" />
    <result column="member_id" jdbcType="VARCHAR" property="memberId" />
    <result column="member_name" jdbcType="VARCHAR" property="memberName" />
    <result column="erp_status" jdbcType="VARCHAR" property="erpStatus" />
    <result column="erp_code" jdbcType="VARCHAR" property="erpCode" />
    <result column="wsdl_path" jdbcType="VARCHAR" property="wsdlPath" />
    <result column="auth_name" jdbcType="VARCHAR" property="authName" />
    <result column="auth_password" jdbcType="VARCHAR" property="authPassword" />
    <result column="erp_seller_id" jdbcType="VARCHAR" property="erpSellerId" />
    <result column="max_times" jdbcType="INTEGER" property="maxTimes" />
    <result column="timeout_time" jdbcType="INTEGER" property="timeoutTime" />
    <result column="if_calculat" jdbcType="BIT" property="ifCalculat" />
    <result column="clear_way" jdbcType="VARCHAR" property="clearWay" />
    <result column="ext1" jdbcType="VARCHAR" property="ext1" />
    <result column="ext3" jdbcType="VARCHAR" property="ext3" />
    <result column="ext4" jdbcType="VARCHAR" property="ext4" />
    <result column="ext5" jdbcType="VARCHAR" property="ext5" />
    <result column="ext2" jdbcType="VARCHAR" property="ext2" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>
</mapper>