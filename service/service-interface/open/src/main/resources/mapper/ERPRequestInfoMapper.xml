<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.open.dao.mapper.ERPRequestInfoMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.open.dao.vo.ERPRequestInfo">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="request_log_id" jdbcType="VARCHAR" property="requestLogId" />
    <result column="request_type" jdbcType="VARCHAR" property="requestType" />
    <result column="request_start_time" jdbcType="TIMESTAMP" property="requestStartTime" />
    <result column="request_status" jdbcType="VARCHAR" property="requestStatus" />
    <result column="request_no" jdbcType="VARCHAR" property="requestNo" />
    <result column="event_code" jdbcType="VARCHAR" property="eventCode" />
    <result column="option_type" jdbcType="VARCHAR" property="optionType" />
    <result column="ec_bill_code" jdbcType="VARCHAR" property="ecBillCode" />
    <result column="erp_bill_code" jdbcType="VARCHAR" property="erpBillCode" />
    <result column="erp_seller_id" jdbcType="VARCHAR" property="erpSellerId" />
    <result column="ec_operator" jdbcType="VARCHAR" property="ecOperator" />
    <result column="ec_operator_name" jdbcType="VARCHAR" property="ecOperatorName" />
    <result column="response_status" jdbcType="VARCHAR" property="responseStatus" />
    <result column="response_message" jdbcType="VARCHAR" property="responseMessage" />
    <result column="response_time" jdbcType="TIMESTAMP" property="responseTime" />
    <result column="callback_request_no" jdbcType="VARCHAR" property="callbackRequestNo" />
    <result column="callback_status" jdbcType="VARCHAR" property="callbackStatus" />
    <result column="callback_code" jdbcType="VARCHAR" property="callbackCode" />
    <result column="callback_time" jdbcType="TIMESTAMP" property="callbackTime" />
    <result column="callback_message" jdbcType="VARCHAR" property="callbackMessage" />
    <result column="retry_time" jdbcType="INTEGER" property="retryTime" />
    <result column="last_retry_time" jdbcType="TIMESTAMP" property="lastRetryTime" />
    <result column="erp_code" jdbcType="VARCHAR" property="erpCode" />
    <result column="member_id" jdbcType="VARCHAR" property="memberId" />
    <result column="syn_time" jdbcType="INTEGER" property="synTime" />
    <result column="asyn_time" jdbcType="INTEGER" property="asynTime" />
    <result column="ext1" jdbcType="VARCHAR" property="ext1" />
    <result column="ext3" jdbcType="VARCHAR" property="ext3" />
    <result column="ext4" jdbcType="VARCHAR" property="ext4" />
    <result column="ext5" jdbcType="VARCHAR" property="ext5" />
    <result column="ext2" jdbcType="VARCHAR" property="ext2" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="erp_operator" jdbcType="VARCHAR" property="erpOperator" />
    <result column="erp_operator_name" jdbcType="VARCHAR" property="erpOperatorName" />
  </resultMap>
</mapper>