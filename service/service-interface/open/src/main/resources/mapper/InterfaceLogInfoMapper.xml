<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.open.dao.mapper.InterfaceLogInfoMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.open.dao.vo.InterfaceLogInfo">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="interface_id" jdbcType="VARCHAR" property="interfaceId" />
    <result column="message_id" jdbcType="VARCHAR" property="messageId" />
    <result column="interface_name" jdbcType="VARCHAR" property="interfaceName" />
    <result column="returnCode" jdbcType="VARCHAR" property="returncode" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="text1" jdbcType="VARCHAR" property="text1" />
    <result column="text2" jdbcType="VARCHAR" property="text2" />
    <result column="text3" jdbcType="VARCHAR" property="text3" />
    <result column="text4" jdbcType="VARCHAR" property="text4" />
    <result column="text5" jdbcType="VARCHAR" property="text5" />
    <result column="params" jdbcType="LONGVARCHAR" property="params" />
    <result column="return_message" jdbcType="LONGVARCHAR" property="returnMessage" />
    <result column="error_message" jdbcType="LONGVARCHAR" property="errorMessage" />
    <result column="log_info" jdbcType="LONGVARCHAR" property="logInfo" />
  </resultMap>
</mapper>