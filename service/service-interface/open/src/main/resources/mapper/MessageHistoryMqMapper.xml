<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.open.dao.mapper.MessageHistoryMqMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.open.dao.vo.MessageHistoryMq">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="message_history_mq_id" jdbcType="VARCHAR" property="messageHistoryMqId" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="customer_sms_flag" jdbcType="BIT" property="customerSmsFlag" />
    <result column="customer_app_flag" jdbcType="BIT" property="customerAppFlag" />
    <result column="customer_inner_flag" jdbcType="BIT" property="customerInnerFlag" />
    <result column="customer_mail_flag" jdbcType="BIT" property="customerMailFlag" />
    <result column="customer_sms_retry_count" jdbcType="BIT" property="customerSmsRetryCount" />
    <result column="customer_app_retry_count" jdbcType="BIT" property="customerAppRetryCount" />
    <result column="customer_inner_retry_count" jdbcType="BIT" property="customerInnerRetryCount" />
    <result column="customer_mail_retry_count" jdbcType="BIT" property="customerMailRetryCount" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="message" jdbcType="LONGVARCHAR" property="message" />
  </resultMap>
</mapper>