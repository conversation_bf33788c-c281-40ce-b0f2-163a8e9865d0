<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.open.dao.mapper.ReportItemMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.open.dao.vo.ReportItem">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="report_item_id" jdbcType="VARCHAR" property="reportItemId" />
    <result column="report_template_id" jdbcType="VARCHAR" property="reportTemplateId" />
    <result column="report_item_province_code" jdbcType="VARCHAR" property="reportItemProvinceCode" />
    <result column="report_item_province" jdbcType="VARCHAR" property="reportItemProvince" />
    <result column="report_item_member_id" jdbcType="VARCHAR" property="reportItemMemberId" />
    <result column="report_item_member_name" jdbcType="VARCHAR" property="reportItemMemberName" />
    <result column="report_item_order" jdbcType="INTEGER" property="reportItemOrder" />
    <result column="del_flg" jdbcType="INTEGER" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>