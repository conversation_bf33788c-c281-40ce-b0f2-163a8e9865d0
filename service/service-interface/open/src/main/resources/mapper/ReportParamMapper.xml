<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.open.dao.mapper.ReportParamMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.open.dao.vo.ReportParam">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="report_param_id" jdbcType="VARCHAR" property="reportParamId" />
    <result column="report_template_id" jdbcType="VARCHAR" property="reportTemplateId" />
    <result column="report_param_key" jdbcType="VARCHAR" property="reportParamKey" />
    <result column="report_param_value" jdbcType="VARCHAR" property="reportParamValue" />
    <result column="report_param_desc" jdbcType="VARCHAR" property="reportParamDesc" />
    <result column="del_flg" jdbcType="INTEGER" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <select id="selectByTemplateId" resultMap="BaseResultMap">
    select *
    from op_report_param
    where report_template_id = #{reportTemplateId} and del_flg = 0
    order by create_time
  </select>
</mapper>