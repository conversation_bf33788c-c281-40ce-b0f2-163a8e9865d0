<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.open.dao.mapper.ReportTemplateMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.open.dao.vo.ReportTemplate">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="report_template_id" jdbcType="VARCHAR" property="reportTemplateId" />
    <result column="report_template_desc" jdbcType="VARCHAR" property="reportTemplateDesc" />
    <result column="report_province_code" jdbcType="VARCHAR" property="reportProvinceCode" />
    <result column="notify_type" jdbcType="INTEGER" property="notifyType" />
    <result column="template_name" jdbcType="VARCHAR" property="templateName" />
    <result column="del_flg" jdbcType="INTEGER" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>