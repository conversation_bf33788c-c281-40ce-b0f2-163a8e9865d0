<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.open.dao.mapper.SysConfigMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.open.dao.vo.SysConfig">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="def_sys_id" jdbcType="INTEGER" property="defSysId" />
    <result column="sys_code" jdbcType="VARCHAR" property="sysCode" />
    <result column="sys_name" jdbcType="VARCHAR" property="sysName" />
    <result column="sys_type" jdbcType="VARCHAR" property="sysType" />
    <result column="sys_status" jdbcType="VARCHAR" property="sysStatus" />
    <result column="sys_url" jdbcType="VARCHAR" property="sysUrl" />
    <result column="auth_type" jdbcType="VARCHAR" property="authType" />
    <result column="auth_name" jdbcType="VARCHAR" property="authName" />
    <result column="auth_password" jdbcType="VARCHAR" property="authPassword" />
    <result column="auth_cert_path" jdbcType="VARCHAR" property="authCertPath" />
    <result column="dev_url" jdbcType="VARCHAR" property="devUrl" />
    <result column="test_url" jdbcType="VARCHAR" property="testUrl" />
    <result column="prod_url" jdbcType="VARCHAR" property="prodUrl" />
    <result column="mq_key_name" jdbcType="VARCHAR" property="mqKeyName" />
    <result column="max_times" jdbcType="INTEGER" property="maxTimes" />
    <result column="timeout_time" jdbcType="INTEGER" property="timeoutTime" />
    <result column="if_calculat" jdbcType="BIT" property="ifCalculat" />
    <result column="clear_way" jdbcType="VARCHAR" property="clearWay" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>

  <select id="queryList" parameterType="com.ecommerce.open.api.dto.apicenter.config.SysConfigQueryDTO" resultType="com.ecommerce.open.api.dto.apicenter.config.SysConfigResultDTO">
    select
      def_sys_id as defSysId,
      sys_code as sysCode,
      sys_name as sysName,
      sys_type as sysType,
      sys_status as sysStatus,
      max_times as maxTimes
    from op_sys_config
    where del_flg = 0
    <if test="sysCode != null and sysCode != ''">
      and sys_code = #{sysCode}
    </if>
    <if test="sysType != null and sysType != ''">
      and sys_type = #{sysType}
    </if>
    <if test="sysStatus != null and sysStatus != ''">
      and sys_status = #{sysStatus}
    </if>
    order by update_time desc
  </select>

  <!--查询所有ERP系统-->
  <select id="querySysConfig" resultType="com.ecommerce.open.api.dto.apicenter.config.SysConfigResultDTO">
    SELECT
        sys_code AS sysCode,
        sys_name AS sysName
    FROM op_sys_config
    WHERE
        sys_type = 'ERP'
    AND sys_status='ENABLE'
    AND del_flg = 0
  </select>
</mapper>