<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.open.dao.mapper.SysRouteMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.open.dao.vo.SysRoute">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="sys_route_id" jdbcType="INTEGER" property="sysRouteId" />
    <result column="sys_code" jdbcType="VARCHAR" property="sysCode" />
    <result column="sys_name" jdbcType="VARCHAR" property="sysName" />
    <result column="invoke_seller_id" jdbcType="VARCHAR" property="invokeSellerId" />
    <result column="invoke_seller_name" jdbcType="VARCHAR" property="invokeSellerName" />
    <result column="send_seller_id" jdbcType="VARCHAR" property="sendSellerId" />
    <result column="send_seller_name" jdbcType="VARCHAR" property="sendSellerName" />
    <result column="route_status" jdbcType="VARCHAR" property="routeStatus" />
    <result column="ext1" jdbcType="VARCHAR" property="ext1" />
    <result column="ext2" jdbcType="VARCHAR" property="ext2" />
    <result column="ext3" jdbcType="VARCHAR" property="ext3" />
    <result column="ext4" jdbcType="VARCHAR" property="ext4" />
    <result column="ext5" jdbcType="VARCHAR" property="ext5" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="biz_code" jdbcType="LONGVARCHAR" property="bizCode" />
  </resultMap>

  <resultMap id="SysRouteResultDTOMap" type="com.ecommerce.open.api.dto.apicenter.config.SysRouteResultDTO">
    <id column="sys_route_id" jdbcType="INTEGER" property="sysRouteId" />
    <result column="sys_code" jdbcType="VARCHAR" property="sysCode" />
    <result column="biz_code" jdbcType="LONGVARCHAR" property="bizCode" />
    <result column="sys_name" jdbcType="VARCHAR" property="sysName" />
    <result column="invoke_seller_id" jdbcType="VARCHAR" property="invokeSellerId" />
    <result column="invoke_seller_name" jdbcType="VARCHAR" property="invokeSellerName" />
    <result column="send_seller_id" jdbcType="VARCHAR" property="sendSellerId" />
    <result column="send_seller_name" jdbcType="VARCHAR" property="sendSellerName" />
    <result column="route_status" jdbcType="VARCHAR" property="routeStatus" />
  </resultMap>

  <select id="queryList" parameterType="com.ecommerce.open.api.dto.apicenter.config.SysRouteQueryDTO" resultMap="SysRouteResultDTOMap">
    select
      sys_route_id,
      sys_code,
      biz_code,
      sys_name,
      invoke_seller_id,
      invoke_seller_name,
      send_seller_id,
      send_seller_name,
      route_status
    from op_sys_route
    where del_flg = 0
    <if test="sysCode != null and sysCode != ''">
      and sys_code = #{sysCode}
    </if>
    <if test="bizCode != null and bizCode != ''">
      and biz_code like concat('%', #{bizCode}, '%')
    </if>
    <if test="invokeSellerId != null and invokeSellerId != ''">
      and invoke_seller_id = #{invokeSellerId}
    </if>
    <if test="invokeSellerName != null and invokeSellerName !=''">
      and invoke_seller_name like concat('%', #{invokeSellerName}, '%')
    </if>
    <if test="sendSellerId != null and sendSellerId != ''">
      and send_seller_id = #{sendSellerId}
    </if>
    <if test="sendSellerName != null and sendSellerName != ''">
      and send_seller_name like concat('%', #{sendSellerName}, '%')
    </if>
    <if test="routeStatus != null and routeStatus != ''">
      and route_status = #{routeStatus}
    </if>
    order by update_time desc
  </select>

</mapper>