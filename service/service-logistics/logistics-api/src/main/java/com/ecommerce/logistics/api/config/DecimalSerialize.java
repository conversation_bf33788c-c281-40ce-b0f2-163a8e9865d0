
package com.ecommerce.logistics.api.config;

import com.alibaba.fastjson.serializer.JSONSerializer;
import com.alibaba.fastjson.serializer.ObjectSerializer;
import com.alibaba.fastjson.serializer.SerializeWriter;
import org.springframework.stereotype.Component;

import java.lang.reflect.Type;
import java.math.BigDecimal;

/**
 * @Author: <EMAIL>
 * @Description:
 * @Date: Create in 下午7:03 18/9/6
 */
@Component
public class DecimalSerialize implements ObjectSerializer {

    @Override
    public void write(JSONSerializer serializer, Object object, Object fieldName, Type fieldType, int features) {
        SerializeWriter out = serializer.out;
        BigDecimal value = (BigDecimal)object;
        out.writeString(value.toPlainString());
    }
}

