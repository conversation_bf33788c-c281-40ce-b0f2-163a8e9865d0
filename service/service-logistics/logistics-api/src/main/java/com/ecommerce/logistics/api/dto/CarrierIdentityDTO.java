
package com.ecommerce.logistics.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import lombok.Data;

@Data
@Schema(name="承运商身份实体")
public class CarrierIdentityDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = 5977812116375721867L;
    @Schema(description="是否为船舶经纪人")
    private Boolean isCarrierShipowner;
    @Schema(description="是否为汽运经纪人")
    private Boolean isCarrierVehicler;
}

