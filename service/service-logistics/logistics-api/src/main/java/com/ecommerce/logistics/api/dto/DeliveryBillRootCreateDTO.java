
package com.ecommerce.logistics.api.dto;

import com.ecommerce.logistics.api.enums.BillProxyTypeEnum;
import com.ecommerce.logistics.api.enums.EntrustSourceEnum;
import com.ecommerce.logistics.api.enums.TransportToolTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(name="委托单创建实体")
public class DeliveryBillRootCreateDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = 5977812116375721896L;
    @Schema(description="配送信息ID")
    private String deliveryInfoId;
    @Schema(description="托运人ID")
    private String consignorId;
    @Schema(description="托运人名")
    private String consignorName;
    @Schema(description="承运人ID")
    private String carrierId;
    @Schema(description="承运人名")
    private String carrierName;
    @Schema(description="承运人角色类型")
    private String carrierRoleType;
    @Schema(description="卖家ID")
    private String sellerId;
    @Schema(description="背靠背单据层级")
    private String billProxyType = BillProxyTypeEnum.NORMAL.getCode();
    @Schema(description="运输工具类型")
    private String transportToolType = TransportToolTypeEnum.ROAD_TRANSPORT.getCode();
    @Schema(description="运输品类ID")
    private String transportCategoryId;
    @Schema(description="委托来源")
    private String entrustSource = EntrustSourceEnum.ORDER_CREATE.getCode();
    @Schema(description="是否为叶")
    private Byte leafFlag = 0;
    @Schema(description="商品数量")
    private BigDecimal quantity;
    @Schema(description="运费规则ID")
    private String carriageRuleId;
    @Schema(description="起运港ID")
    private String loadPortId;
    @Schema(description="目的港ID")
    private String unloadPortId;
    @Schema(description="仓库ID")
    private String warehouseId;
    @Schema(description="仓库名称")
    private String warehouseName;
    @Schema(description="仓库类型")
    private String warehouseType;
    @Schema(description="出货点省")
    private String warehouseProvince;
    @Schema(description="出货点省编码")
    private String warehouseProvinceCode;
    @Schema(description="出货点市")
    private String warehouseCity;
    @Schema(description="出货点市编码")
    private String warehouseCityCode;
    @Schema(description="出货点区")
    private String warehouseDistrict;
    @Schema(description="出货点区编码")
    private String warehouseDistrictCode;
    @Schema(description="出货点街道")
    private String warehouseStreet;
    @Schema(description="出货点省编码")
    private String warehouseStreetCode;
    @Schema(description="出货点地址")
    private String warehouseAddress;
    @Schema(description="出货点坐标")
    private String warehouseLocation;
    @Schema(description="收货地址ID")
    private String receiverAddressId;
    @Schema(description="卸货点坐标")
    private String location;
    @Schema(description="操作用户ID", required=false, example="10010")
    private String operationUserId;
    @Schema(description="操作用户名称", required=false, example="钱计算")
    private String operationUserName;
    @Schema(description="是否可操作:0-否,1-是")
    private Byte canOperate;
    @Schema(description="船运是否同意调配 0:不同意 1:同意")
    private Integer ifAgreeDispatch = 0;
    @Schema(description="提货类型")
    private String type;
    private BigDecimal payableCarriagePrice;
}

