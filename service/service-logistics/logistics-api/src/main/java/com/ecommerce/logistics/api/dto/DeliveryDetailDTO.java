
package com.ecommerce.logistics.api.dto;

import com.ecommerce.logistics.api.dto.operationrecord.OperationRecordDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

@Data
@Schema(name="委托详情查询DTO")
public class DeliveryDetailDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = 2567479178986625936L;
    @Schema(description="委托单主键")
    private String deliveryBillId;
    @Schema(description="委托单号")
    private String deliveryBillNum;
    @Schema(description="发货单号")
    private String takeCode;
    @Schema(description="承运人ID")
    private String carrierId;
    @Schema(description="承运人名称")
    private String carrierName;
    @Schema(description="承运人类型")
    private String carrierRoleType;
    @Schema(description="承运人联系电话")
    private String carrierPhone;
    @Schema(description="委托方ID")
    private String consignorId;
    @Schema(description="委托方名称")
    private String consignorName;
    @Schema(description="委托方联系电话")
    private String consignorPhone;
    @Schema(description="买家ID")
    private String buyerId;
    @Schema(description="买家名称")
    private String buyerName;
    @Schema(description="卖家ID")
    private String sellerId;
    @Schema(description="卖家名称")
    private String sellerName;
    @Schema(description="父级ID")
    private String parentId;
    @Schema(description="运输品类Id")
    private String transportCategoryId;
    @Schema(description="运输品类名称")
    private String transportCategoryName;
    @Schema(description="配送时间")
    private Date deliveryTime;
    @Schema(description="配送时间区间(期望)")
    private String deliveryTimeRange;
    @Schema(description="开始时间")
    private Date deliveryTimeStart;
    @Schema(description="结束时间")
    private Date deliveryTimeEnd;
    @Schema(description="仓库名称")
    private String warehouseName;
    @Schema(description="装货地址ID")
    private String warehouseId;
    @Schema(description="仓库类型")
    private String warehouseType;
    @Schema(description="出货点省")
    private String warehouseProvince;
    @Schema(description="出货点省编码")
    private String warehouseProvinceCode;
    @Schema(description="出货点市")
    private String warehouseCity;
    @Schema(description="出货点市编码")
    private String warehouseCityCode;
    @Schema(description="出货点区")
    private String warehouseDistrict;
    @Schema(description="出货点区编码")
    private String warehouseDistrictCode;
    @Schema(description="装货地址")
    private String warehouseAddress;
    @Schema(description="收货地址ID")
    private String receiveAddressId;
    @Schema(description="收货地址")
    private String receiveAddress;
    @Schema(description="收货地址省编码")
    private String receiveProvinceCode;
    @Schema(description="收货地址市编码")
    private String receiveCityCode;
    @Schema(description="收货地址市编码")
    private String receiveDistrictCode;
    @Schema(description="收货地址市编码")
    private String receiveStreetCode;
    @Schema(description="委托总量")
    private BigDecimal quantity;
    @Schema(description="单位")
    private String unit;
    @Schema(description="待安排数量")
    private BigDecimal unplannedQuantity;
    @Schema(description="已安排数量")
    private BigDecimal plannedQuantity;
    @Schema(description="已完成数量")
    private BigDecimal completeQuantity;
    @Schema(description="配送中数量")
    private BigDecimal sendQuantity;
    @Schema(description="运费单价")
    private BigDecimal carriageUnitPrice;
    @Schema(description="运费总价")
    private BigDecimal carriageAmount;
    @Schema(description="商品名称")
    private String goodsName;
    @Schema(description="收货人")
    private String receiver;
    @Schema(description="收货人电话")
    private String receiverPhone;
    @Schema(description="状态")
    private String status;
    @Schema(description="是否可操作:0-否,1-是")
    private Byte canOperate;
    @Schema(description="起点坐标")
    private String startLocation;
    @Schema(description="终点坐标")
    private String endLocation;
    @Schema(description="运输时间")
    private BigDecimal transportTime;
    @Schema(description="装货时间")
    private BigDecimal loadingTime;
    @Schema(description="卸货时间")
    private BigDecimal unloadingTime;
    @Schema(description="距离,单位:公里")
    private BigDecimal distance = BigDecimal.ZERO;
    @Schema(description="运输方式")
    private String transportToolType;
    @Schema(description="自己承运-计划量")
    private BigDecimal carryPlanQuantity;
    @Schema(description="自己承运-已发货数量")
    private BigDecimal carrySendQuantity;
    @Schema(description="自己承运-已发货运力数")
    private BigDecimal carrySendCount;
    @Schema(description="自己承运-已完成数量")
    private BigDecimal carryCompleteQuantity;
    @Schema(description="自己承运-已完成运力数")
    private BigDecimal carryCompleteCount;
    @Schema(description="委托承运-已委托数量")
    private BigDecimal consignQuantity;
    @Schema(description="委托承运-已接受委托数量")
    private BigDecimal consignAcceptQuantity;
    @Schema(description="委托承运-已发货数量")
    private BigDecimal consignSendQuantity;
    @Schema(description="委托承运-已发货运力数")
    private BigDecimal consignSendCount;
    @Schema(description="委托承运-已完成数量")
    private BigDecimal consignCompleteQuantity;
    @Schema(description="委托承运-已完成运力数")
    private BigDecimal consignCompleteCount;
    @Schema(description="禁用指派委托标识")
    private Integer bidAssignFlag = 0;
    @Schema(description="同步标识")
    private String syncFlag;
    @Schema(description="类型")
    private String type;
    @Schema(description="单据层级")
    private String billProxyType;
    @Schema(description="是否为根")
    private Byte rootFlag;
    @Schema(description="是否可以修改仓库")
    private Integer canChangeWarehouse = 0;
    @Schema(description="是否为厂商指派的船运")
    private Integer erpShipAssignFlag = 0;
    @Schema(description="委托记录DTO")
    private List<DeliveryDetailRecordsDTO> deliveryDetailRecordsDTOS;
    @Schema(description="委托运输信息DTO")
    private List<DeliveryDetailVehicleDTO> deliveryDetailVehicleDTOS;
    @Schema(description="委托过程记录DTO")
    private List<OperationRecordDTO> operationRecordDTOS;
}

