
package com.ecommerce.logistics.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(name="委托记录DTO")
public class DeliveryDetailRecordsDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = 4914790436236200776L;
    @Schema(description="委托单主键")
    private String deliveryBillId;
    @Schema(description="委托单号")
    private String deliveryBillNum;
    @Schema(description="承运人ID")
    private String carrierId;
    @Schema(description="承运人名称")
    private String carrierName;
    @Schema(description="托运人ID")
    private String consignorId;
    @Schema(description="托运人名称")
    private String consignorName;
    @Schema(description="委托总量")
    private BigDecimal quantity;
    @Schema(description="运费单价")
    private BigDecimal carriageUnitPrice;
    @Schema(description="状态")
    private String status;
    @Schema(description="待安排数量")
    private BigDecimal unplannedQuantity;
    @Schema(description="已发货数量")
    private BigDecimal sendQuantity;
    @Schema(description="已完成数量")
    private BigDecimal completeQuantity;
    @Schema(description="是否可操作:0-否,1-是")
    private Byte canOperate;
    @Schema(description="是否为叶子节点")
    private Byte leafFlag;
    @Schema(description="承运人类型")
    private String carrierRoleType;
}

