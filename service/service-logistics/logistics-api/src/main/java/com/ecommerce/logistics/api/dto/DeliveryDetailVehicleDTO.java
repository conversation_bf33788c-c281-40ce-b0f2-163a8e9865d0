
package com.ecommerce.logistics.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import lombok.Data;

@Data
@Schema(name="委托运输信息DTO")
public class DeliveryDetailVehicleDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = -7290809341588688264L;
    @Schema(description="真实委托单号")
    private String deliveryBillNum;
    @Schema(description="承运人名称")
    private String carrierName;
    @Schema(description="运单号")
    private String waybillNum;
    @Schema(description="车牌号")
    private String vehicleNum;
    @Schema(description="司机名称")
    private String driverName;
    @Schema(description="数量")
    private String quantity;
    @Schema(description="运输工具类型")
    private String transportToolType;
    @Schema(description="状态")
    private String status;
}

