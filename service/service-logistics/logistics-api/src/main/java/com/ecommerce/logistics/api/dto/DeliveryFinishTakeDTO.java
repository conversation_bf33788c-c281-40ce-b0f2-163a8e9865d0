
package com.ecommerce.logistics.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(name="委托单完成发货单信息DTO")
public class DeliveryFinishTakeDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = -6594035888400927864L;
    @Schema(description="发货单号")
    private String takeCode;
    @Schema(description="订单子项ID")
    private String orderItemId;
    @Schema(description="已完成数量")
    private BigDecimal completeQuantity;
    @Schema(description="运费")
    private BigDecimal carriageUnitPrice;
}

