
package com.ecommerce.logistics.api.dto;

import com.ecommerce.logistics.api.enums.BillProxyTypeEnum;
import com.ecommerce.logistics.api.enums.PickingBillDataSourceEnum;
import com.ecommerce.logistics.api.enums.TransportToolTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

@Data
@Schema(name="配送信息实体")
public class DeliveryInfoDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = -1017683981076183013L;
    @Schema(description="配送信息ID")
    private String deliveryInfoId;
    @Schema(description="父级信息ID")
    private String parentInfoId;
    @Schema(description="是否可操作")
    private Byte canOperate = 1;
    @Schema(description="船运是否同意调配 0:不同意 1:同意")
    private Integer ifAgreeDispatch = 0;
    @Schema(description="出厂基地偏好")
    private String basePreference;
    @Schema(description="关联提货类型")
    private String relationType;
    @Schema(description="单据背靠背层级")
    private String billProxyType = BillProxyTypeEnum.NORMAL.getCode();
    @Schema(description="买家ID")
    private String buyerId;
    @Schema(description="买家名称")
    private String buyerName;
    @Schema(description="卖家ID")
    private String sellerId;
    @Schema(description="卖家名称")
    private String sellerName;
    @Schema(description="erp支付账户编码")
    private String mdmCode;
    @Schema(description="发票类型 1：一票制、2：2票制")
    private Integer billType;
    @Schema(description="发货单编码")
    private String takeCode;
    @Schema(description="提货类型")
    private String type;
    @Schema(description="起运港ID")
    private String loadPortId;
    @Schema(description="起运港名")
    private String loadPortName;
    @Schema(description="目的港ID")
    private String unloadPortId;
    @Schema(description="目的港名")
    private String unloadPortName;
    @Schema(description="仓库ID")
    private String warehouseId;
    @Schema(description="仓库名称")
    private String warehouseName;
    @Schema(description="仓库类型")
    private String warehouseType;
    @Schema(description="出货点省")
    private String warehouseProvince;
    @Schema(description="出货点省编码")
    private String warehouseProvinceCode;
    @Schema(description="出货点市")
    private String warehouseCity;
    @Schema(description="出货点市编码")
    private String warehouseCityCode;
    @Schema(description="出货点区")
    private String warehouseDistrict;
    @Schema(description="出货点区编码")
    private String warehouseDistrictCode;
    @Schema(description="出货点街道")
    private String warehouseStreet;
    @Schema(description="出货点省编码")
    private String warehouseStreetCode;
    @Schema(description="出货点地址")
    private String warehouseAddress;
    @Schema(description="出货点坐标")
    private String warehouseLocation;
    @Schema(description="收货地址ID")
    private String receiverAddressId;
    @Schema(description="收货人名")
    private String receiver;
    @Schema(description="收货人电话")
    private String receiverPhone;
    @Schema(description="收货省级名")
    private String province;
    @Schema(description="收货省编码")
    private String provinceCode;
    @Schema(description="收货市级名")
    private String city;
    @Schema(description="收货市编码")
    private String cityCode;
    @Schema(description="收货区级名")
    private String district;
    @Schema(description="收货区编码")
    private String districtCode;
    @Schema(description="收货街道名")
    private String street;
    @Schema(description="收货街道编码")
    private String streetCode;
    @Schema(description="收货详细地址")
    private String address;
    @Schema(description="收货坐标")
    private String location;
    @Schema(description="配送日期(期望)")
    private Date deliveryTime;
    @Schema(description="配送时间区间(期望)")
    private String deliveryTimeRange;
    @Schema(description="同步标识")
    private String syncFlag;
    @Schema(description="运输工具类型")
    private String transportToolType = TransportToolTypeEnum.ROAD_TRANSPORT.getCode();
    @Schema(description="合同ID")
    private String dealsId;
    @Schema(description="合同编号")
    private String dealsName;
    @Schema(description="项目名称")
    private String projectName;
    @Schema(description="配送信息来源")
    private String dataSource = PickingBillDataSourceEnum.TRADE_SYNCHRONIZE.getCode();
    @Schema(description="搬运标识 0:否 1:是")
    private Byte carryFlag;
    @Schema(description="订单子项ID")
    private String orderItemId;
    @Schema(description="运输品类ID")
    private String transportCategoryId;
    @Schema(description="erp商品编码")
    private String commodityCode;
    @Schema(description="商品厂商")
    private String vendor;
    @Schema(description="商品图片url")
    private String productImg;
    @Schema(description="商品ID")
    private String goodsId;
    @Schema(description="商品描述")
    private String note;
    @Schema(description="商品单价")
    private BigDecimal price;
    @Schema(description="商品单位")
    private String unit;
    @Schema(description="运费单价")
    private BigDecimal carriageUnitPrice;
    @Schema(description="流向卸货点编码")
    private String unloadingFlowCode;
    @Schema(description="是否需要监控, 0-不需要,1-需要")
    private Integer needMonitor = 0;
    @Schema(description="签收类型")
    private String signType;
    @Schema(description="签收人")
    private String signer;
    @Schema(description="签收人电话")
    private String signerPhone;
    @Schema(description="特殊商品标识")
    private Byte specialFlag;
    @Schema(description="销售区域ID")
    private String saleRegionPath;
    @Schema(description="业务员ID")
    private String salesmanId;
    @Schema(description="业务员名称")
    private String salesmanName;
    @Schema(description="操作用户ID", required=false, example="10010")
    private String operationUserId;
    @Schema(description="操作用户名称", required=false, example="钱计算")
    private String operationUserName;
    @Schema(description="订单号")
    private String orderCode;
}

