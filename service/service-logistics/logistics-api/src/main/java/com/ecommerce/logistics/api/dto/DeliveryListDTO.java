
package com.ecommerce.logistics.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

@Data
@Schema(name="委托列表DTO")
public class DeliveryListDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = -7182799188629274659L;
    @Schema(description="委托单主键")
    private String deliveryBillId;
    @Schema(description="委托单号")
    private String deliveryBillNum;
    @Schema(description="发货单号")
    private String takeCode;
    @Schema(description="承运人ID")
    private String carrierId;
    @Schema(description="承运人名称")
    private String carrierName;
    @Schema(description="承运人联系电话")
    private String carrierPhone;
    @Schema(description="委托方ID")
    private String consignorId;
    @Schema(description="委托方名称")
    private String consignorName;
    @Schema(description="委托方联系电话")
    private String consignorPhone;
    @Schema(description="买家ID")
    private String buyerId;
    @Schema(description="卖家ID")
    private String sellerId;
    @Schema(description="运输品类Id")
    private String transportCategoryId;
    @Schema(description="运输品类名称")
    private String transportCategoryName;
    @Schema(description="运输方式")
    private String transportToolType;
    @Schema(description="委托量")
    private BigDecimal quantity;
    @Schema(description="单位")
    private String unit;
    @Schema(description="待安排数量")
    private BigDecimal unplannedQuantity;
    @Schema(description="已安排数量")
    private BigDecimal plannedQuantity;
    @Schema(description="已完成数量")
    private BigDecimal completeQuantity;
    @Schema(description="配送中数量")
    private BigDecimal sendQuantity;
    @Schema(description="装货地址Id")
    private String warehouseId;
    @Schema(description="装货地址")
    private String warehouseAddress;
    @Schema(description="收货地址")
    private String receiveAddress;
    @Schema(description="收货地址省编码")
    private String receiveProvinceCode;
    @Schema(description="收货地址市编码")
    private String receiveCityCode;
    @Schema(description="运费单价")
    private BigDecimal carriageUnitPrice;
    @Schema(description="配送时间")
    private Date deliveryTime;
    @Schema(description="配送时间区间(期望)")
    private String deliveryTimeRange;
    @Schema(description="状态")
    private String status;
    @Schema(description="是否可操作:0-否,1-是")
    private Byte canOperate;
    @Schema(description="起点坐标")
    private String startLocation;
    @Schema(description="终点坐标")
    private String endLocation;
    @Schema(description="距离,单位:公里")
    private BigDecimal distance = BigDecimal.ZERO;
    @Schema(description="禁用指派委托标识")
    private Integer bidAssignFlag = 0;
    @Schema(description="同步标识")
    private String syncFlag;
    @Schema(description="类型")
    private String type;
    @Schema(description="单据层级")
    private String billProxyType;
    @Schema(description="是否为根")
    private Byte rootFlag;
    @Schema(description="是否为厂商指派的船运")
    private Integer erpShipAssignFlag = 0;
    @Schema(description="商品描述")
    private String note;
}

