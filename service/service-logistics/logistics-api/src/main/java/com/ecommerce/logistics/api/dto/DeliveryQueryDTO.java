
package com.ecommerce.logistics.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

@Data
@Schema(name="委托列表查询DTO")
public class DeliveryQueryDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = 8302423406567809771L;
    @Schema(description="委托单号")
    private String deliveryBillNum;
    @Schema(description="发货单号")
    private String takeCode;
    @Schema(description="托运人ID")
    private String consignorId;
    @Schema(description="托运人名称")
    private String consignorName;
    @Schema(description="承运人ID")
    private String carrierId;
    @Schema(description="承运人名称")
    private String carrierName;
    @Schema(description="承运人角色类型")
    private String carrierRoleType;
    @Schema(description="装货地址省编码")
    private String warehouseProvinceCode;
    @Schema(description="装货地址市编码")
    private String warehouseCityCode;
    @Schema(description="装货地址区编码")
    private String warehouseDistrictCode;
    @Schema(description="收货地址省编码")
    private String receiveProvinceCode;
    @Schema(description="收货地址市编码")
    private String receiveCityCode;
    @Schema(description="收货地址区编码")
    private String receiveDistrictCode;
    @Schema(description="买家ID")
    private String buyerId;
    @Schema(description="买家名称")
    private String buyerName;
    @Schema(description="卖家ID")
    private String sellerId;
    @Schema(description="卖家名称")
    private String sellerName;
    @Schema(description="商品Id")
    private String goodsId;
    @Schema(description="商品名称")
    private String goodsName;
    @Schema(description="运输品类Id")
    private String transportCategoryId;
    @Schema(description="收货人名称")
    private String receiver;
    @Schema(description="配送开始时间")
    private String deliveryTimeStart;
    @Schema(description="配送结束时间")
    private String deliveryTimeEnd;
    @Schema(description="状态")
    private String status;
    @Schema(description="状态")
    private List<String> statusList;
    @Schema(description="运输方式")
    private String transportToolType;
    @Schema(description="是否返回待安排量为0的委托单（默认为返回）")
    private String isDispatchBill;
    @Schema(description="(当前查询人状态)：0 -> 委托人、 1 -> 承运人")
    private int isCarrier;
    @Schema(description="销售区域ID列表")
    private List<String> saleRegionIdList;
    @Schema(description="业务员ID")
    private String salesmanId;
}

