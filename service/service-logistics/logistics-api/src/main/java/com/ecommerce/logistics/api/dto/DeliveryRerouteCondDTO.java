
package com.ecommerce.logistics.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import lombok.Data;

@Data
@Schema(name="委托单发起改航的查询实体")
public class DeliveryRerouteCondDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = -3488025961541406010L;
    @Schema(description="卖家ID")
    private String sellerId;
    @Schema(description="委托单ID")
    private String deliveryBillId;
}

