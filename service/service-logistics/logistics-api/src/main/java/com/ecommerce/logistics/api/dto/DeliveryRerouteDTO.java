
package com.ecommerce.logistics.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
@Schema(name = "委托单改航实体")
public class DeliveryRerouteDTO
        implements Serializable {
    @Serial
    private static final long serialVersionUID = 2728829501351276414L;
    @Schema(description = "运单原先的叶子委托单ID")
    private String fromDeliveryBillId;
    @Schema(description = "原委托单号")
    private String fromDeliveryBillNum;
    @Schema(description = "需要改航到的根委托单ID")
    private String toDeliveryBillId;
    @Schema(description = "当前委托单号")
    private String toDeliveryBillNum;

    @Schema(description = "改航的运单ID")
    private String waybillId;
    @Schema(description = "改航的运单子项ID")
    private String waybillItemId;
    @Schema(description = "结算码头ID")
    private String unloadPortId;
    @Schema(description = "结算码头名称")
    private String unloadPortName;
    @Schema(description = "改航原因")
    private String rerouteReason;
    @Schema(description = "操作人ID")
    private String operationUserId;
    @Schema(description = "操作人名")
    private String operationUserName;

    @Schema(description = "运单号")
    private String waybillNum;

    @Schema(description = "当前委托单收货单位ID")
    private String currReceiveMemberId;

    @Schema(description = "当前委托单收货单位")
    private String currReceiveMemberName;


    @Schema(description = "当前卸货码头ID")
    private String currUnloadPortId;

    @Schema(description = "当前卸货码头名称")
    private String currUnloadPortName;

    @Schema(description = "原委托单收货单位ID")
    private String originalReceiveMemberId;

    @Schema(description = "原委托单收货单位")
    private String originalReceiveMemberName;


    @Schema(description = "原卸货码头ID")
    private String originalUnloadPortId;

    @Schema(description = "原卸货码头名称")
    private String originalUnloadPortName;


}

