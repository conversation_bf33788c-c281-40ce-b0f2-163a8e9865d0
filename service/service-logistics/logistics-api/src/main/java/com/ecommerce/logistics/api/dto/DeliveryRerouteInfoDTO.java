
package com.ecommerce.logistics.api.dto;

import com.ecommerce.logistics.api.dto.waybill.RerouteRecordLogDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import lombok.Data;

@Data
@Schema(name="委托信息改航信息对象")
public class DeliveryRerouteInfoDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = -2977020059949815339L;
    @Schema(description="发货单号")
    private String takeCode;
    @Schema(description="委托单ID")
    private String deliveryBillId;
    @Schema(description="委托单号")
    private String deliveryBillNum;
    @Schema(description="商品ID")
    private String goodsId;
    @Schema(description="商品")
    private String goodsName;
    @Schema(description="运输品类ID")
    private String transportCategoryId;
    @Schema(description="运输品类")
    private String transportCategoryName;
    @Schema(description="买家ID")
    private String buyerId;
    @Schema(description="买家名称")
    private String buyerName;
    @Schema(description="卖家ID")
    private String sellerId;
    @Schema(description="卖家名称")
    private String sellerName;
    @Schema(description="出货点ID")
    private String warehouseId;
    @Schema(description="出货点名称")
    private String warehouseName;
    @Schema(description="起运港ID")
    private String loadPortId;
    @Schema(description="起运港名")
    private String loadPortName;
    @Schema(description="目的港ID")
    private String unloadPortId;
    @Schema(description="目的港名")
    private String unloadPortName;
    @Schema(description="配送日期(期望)")
    private Date deliveryTime;
    @Schema(description="配送时间区间(期望)")
    private String deliveryTimeRange;
    @Schema(description="委托单状态")
    private String status;
    @Schema(description="申请提货数量")
    private BigDecimal quantity;
    @Schema(description="已安排量")
    private BigDecimal plannedQuantity;
    @Schema(description="待安排量")
    private BigDecimal unplannedQuantity;
    @Schema(description="单位")
    private String unit;
    @Schema(description="收货人")
    private String receiver;
    @Schema(description="收货电话")
    private String receiverPhone;
    @Schema(description="收货地址")
    private String receiverAddress;
    @Schema(description="是否同意调配")
    private Integer ifAgreeDispatch;
    @Schema(description="出厂基地偏好")
    private String basePreference;
    private List<RerouteRecordLogDTO> recordLogDTOList;
}

