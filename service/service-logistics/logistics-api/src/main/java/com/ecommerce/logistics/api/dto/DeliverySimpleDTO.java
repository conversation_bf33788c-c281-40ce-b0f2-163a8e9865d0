
package com.ecommerce.logistics.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="委托单会员信息对象")
public class DeliverySimpleDTO {
    @Schema(description="买家Id")
    private String buyerId;
    @Schema(description="买家名称")
    private String buyerName;
    @Schema(description="承运商Id")
    private String carrierId;
    @Schema(description="承运商名称")
    private String carrierName;
}

