
package com.ecommerce.logistics.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(name="委托统计DTO")
public class DeliveryStatisticsDTO
implements Serializable {
    @Schema(description="委托量")
    private BigDecimal quantity = BigDecimal.ZERO;
    @Schema(description="待安排数量")
    private BigDecimal unplannedQuantity = BigDecimal.ZERO;
    @Schema(description="待配送数量")
    private BigDecimal plannedQuantity = BigDecimal.ZERO;
    @Schema(description="待配送车次")
    private Integer plannedNum = 0;
    @Schema(description="已发货数量")
    private BigDecimal sendQuantity = BigDecimal.ZERO;
    @Schema(description="已发货车次")
    private Integer sendNum = 0;
    @Schema(description="已完成数量")
    private BigDecimal completeQuantity = BigDecimal.ZERO;
    @Schema(description="已完成车次")
    private Integer completeNum = 0;
    @Schema(description="接单率")
    private BigDecimal receivingRate = BigDecimal.ZERO;
    @Schema(description="总完成率")
    private BigDecimal totalCompletionRate = BigDecimal.ZERO;
}

