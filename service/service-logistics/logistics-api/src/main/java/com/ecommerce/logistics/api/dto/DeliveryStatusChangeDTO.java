
package com.ecommerce.logistics.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="委托单状态变更DTO")
public class DeliveryStatusChangeDTO {
    @Schema(description="变更类型")
    private Integer operationType;
    @Schema(description="委托单主键")
    private String deliveryBillId;
    @Schema(description="操作人账号Id")
    private String operationUserId;
    @Schema(description="操作人名称")
    private String operationUserName;
    @Schema(description="原因")
    private String reason;
}

