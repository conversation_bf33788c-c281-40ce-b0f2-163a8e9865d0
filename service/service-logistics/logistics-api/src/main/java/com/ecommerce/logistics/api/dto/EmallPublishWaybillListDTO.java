
package com.ecommerce.logistics.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(name="Emall发布运单对象")
public class EmallPublishWaybillListDTO {
    @Schema(description="运单Id", required=false)
    private String waybillId;
    @Schema(description="运单号", required=false)
    private String waybillNum;
    @Schema(description="配送车辆", required=false)
    private String vehicleNum;
    @Schema(description="提货点省份", required=false)
    private String warehouseProvince;
    @Schema(description="提货点城市", required=false)
    private String warehouseCity;
    @Schema(description="提货点区域", required=false)
    private String warehouseDistrict;
    @Schema(description="收货人省份编码", required=false)
    private String receiveProvince;
    @Schema(description="收货人城市编码", required=false)
    private String receiveCity;
    @Schema(description="收货人地区编码", required=false)
    private String district;
    @Schema(description="期望配送时间", required=false)
    private String deliveryTime;
    @Schema(description="期望配送时间段", required=false)
    private String deliveryTimeRange;
    @Schema(description="货物重量", required=false)
    private BigDecimal totalQuantity;
    @Schema(description="货物单位", required=false)
    private String productUnit;
    @Schema(description="商品描述", required=false)
    private String productDesc;
    @Schema(description="运输品类ID", required=false)
    private String transportCategoryId;
    @Schema(description="运输品类名称", required=false)
    private String transportCategoryName;
    @Schema(description="发布运价", required=false)
    private BigDecimal publishedCarriage;
    @Schema(description="预估里程", required=false)
    private BigDecimal estimateKm;
    @Schema(description="运单状态", required=false)
    private String status;
    @Schema(description="创建时间", required=false)
    private String createTime;
}

