
package com.ecommerce.logistics.api.dto;

import com.ecommerce.logistics.api.dto.assign.AssignShipDTO;
import com.ecommerce.logistics.api.dto.assign.AssignVehicleDTO;
import com.ecommerce.logistics.api.dto.pickingbill.AssignDetailDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

@Data
@Schema(name="录入普通委托单")
public class EnteringNormalDeliveryBillDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = -7602417691739193106L;
    @Schema(description="操作用户ID")
    private String operationUserId;
    @Schema(description="操作用户名称")
    private String operationUserName;
    @Schema(description="厂家级配送信息")
    private EnteringDeliveryInfoDTO primaryDeliveryInfo;
    @Schema(description="批量指派车辆列表")
    List<AssignVehicleDTO> assignVehicleDTOList;
    @Schema(description="批量指派船只列表")
    List<AssignShipDTO> assignShipDTOList;
    @Schema(description="指派承运商信息")
    List<AssignDetailDTO> assignCarrierDTOList;
}

