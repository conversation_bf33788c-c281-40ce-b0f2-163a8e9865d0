
package com.ecommerce.logistics.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

@Data
@Schema(name="预估距离查询对象")
public class EstimateKmQueryDTO {
    @NotBlank(message="收货地址Id不能为空")
    @Schema(description="收货地址Id")
    private String receiveAddressId;
    @NotBlank(message="仓库Id不能为空")
    @Schema(description="仓库Id")
    private String warehouseId;
    @Schema(description="运输品类Id")
    private String transportCategoryId;
    @Schema(description="车型ID")
    private String vehicleTypeId;
}

