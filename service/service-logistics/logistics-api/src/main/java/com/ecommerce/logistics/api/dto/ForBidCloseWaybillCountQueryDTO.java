
package com.ecommerce.logistics.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="发货单下不可以关闭的运单数量查询对象")
public class ForBidCloseWaybillCountQueryDTO {
    @Schema(description="发货单号")
    private String takeCode;
    @Schema(description="待配送状态是否可关闭,1-查询包含待配送状态的运单数量，0-不包含待配送状态")
    private Integer waitDeliveryFlag = 0;

    public String getTakeCode() {
        return this.takeCode;
    }

    public Integer getWaitDeliveryFlag() {
        return this.waitDeliveryFlag;
    }

    public void setTakeCode(String takeCode) {
        this.takeCode = takeCode;
    }

    public void setWaitDeliveryFlag(Integer waitDeliveryFlag) {
        this.waitDeliveryFlag = waitDeliveryFlag;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof ForBidCloseWaybillCountQueryDTO)) {
            return false;
        }
        ForBidCloseWaybillCountQueryDTO other = (ForBidCloseWaybillCountQueryDTO)o;
        if (!other.canEqual(this)) {
            return false;
        }
        String this$takeCode = this.getTakeCode();
        String other$takeCode = other.getTakeCode();
        if (this$takeCode == null ? other$takeCode != null : !this$takeCode.equals(other$takeCode)) {
            return false;
        }
        Integer this$waitDeliveryFlag = this.getWaitDeliveryFlag();
        Integer other$waitDeliveryFlag = other.getWaitDeliveryFlag();
        return !(this$waitDeliveryFlag == null ? other$waitDeliveryFlag != null : !((Object)this$waitDeliveryFlag).equals(other$waitDeliveryFlag));
    }

    protected boolean canEqual(Object other) {
        return other instanceof ForBidCloseWaybillCountQueryDTO;
    }

    public int hashCode() {
        int result = 1;
        String $takeCode = this.getTakeCode();
        result = result * 59 + ($takeCode == null ? 43 : $takeCode.hashCode());
        Integer $waitDeliveryFlag = this.getWaitDeliveryFlag();
        result = result * 59 + ($waitDeliveryFlag == null ? 43 : ((Object)$waitDeliveryFlag).hashCode());
        return result;
    }

    public String toString() {
        return "ForBidCloseWaybillCountQueryDTO(takeCode=" + this.getTakeCode() + ", waitDeliveryFlag=" + this.getWaitDeliveryFlag() + ")";
    }

    public ForBidCloseWaybillCountQueryDTO(String takeCode, Integer waitDeliveryFlag) {
        this.takeCode = takeCode;
        this.waitDeliveryFlag = waitDeliveryFlag;
    }

    public ForBidCloseWaybillCountQueryDTO() {
    }
}

