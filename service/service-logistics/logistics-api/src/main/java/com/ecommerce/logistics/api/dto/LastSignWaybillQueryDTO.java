
package com.ecommerce.logistics.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import lombok.Data;

@Data
@Schema(name="最新签收运单查询实体")
public class LastSignWaybillQueryDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = -1744821952590091990L;
    @Schema(description="运单子项ID")
    private String orderItemId;
}

