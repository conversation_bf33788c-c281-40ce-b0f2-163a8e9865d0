
package com.ecommerce.logistics.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

@Data
@Schema(name="物流调价用户")
public class LogisticsAdjustPriceDTO {
    @Schema(description="委托单ID")
    private String deliveryBillId;
    @Schema(description="委托人ID")
    private String entrustingSideId;
    @Schema(description="委托人名称")
    private String entrustingSideName;
    @Schema(description="承运人ID")
    private String entrustedSideId;
    @Schema(description="承运人名称")
    private String entrustedSideName;
    @Schema(description="运输工具类型")
    private String transportType;
    @Schema(description="出货点ID")
    private String warehouseId;
    @Schema(description="出货点名称")
    private String warehouseName;
    @Schema(description="运输量")
    private BigDecimal completeQuantity;
    @Schema(description="原运费单价")
    private BigDecimal payableCarriagePrice;
    @Schema(description="回调区间 开始时间")
    private Date shipStartTime;
    @Schema(description="回调区间 结束时间")
    private Date shipEndTime;
    @Schema(description="收货省级名")
    private String province;
    @Schema(description="收货省编码")
    private String provinceCode;
    @Schema(description="收货市级名")
    private String city;
    @Schema(description="收货市编码")
    private String cityCode;
    @Schema(description="收货区级名")
    private String district;
    @Schema(description="收货区编码")
    private String districtCode;
    @Schema(description="街道")
    private String street;
    @Schema(description="街道Code")
    private String streetCode;
    @Schema(description="收货详细地址")
    private String address;
    @Schema(description="运输品类ID")
    private String transportCategoryId;
    @Schema(description="商品ID")
    private String goodsId;
    @Schema(description="商品名称")
    private String goodsName;
    @Schema(description="商品单价")
    private BigDecimal goodsPrice;
    @Schema(description="调价类型 0：收费  1：付费   2：收付费")
    private Integer adjustType;
    @Schema(description="基础运费单价", required=true, example="23.10")
    private BigDecimal carriageUnitPrice;
    @Schema(description="辅助价目表价格", required=true, example="23.10")
    private BigDecimal auxiliaryPrice;
}

