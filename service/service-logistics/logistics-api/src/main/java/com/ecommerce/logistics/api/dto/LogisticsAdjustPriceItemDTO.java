
package com.ecommerce.logistics.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

@Data
@Schema(name="物流费回调详情")
public class LogisticsAdjustPriceItemDTO {
    @Schema(description="回调详情ID")
    private String id;
    @Schema(description="物流调价ID")
    private String adjustId;
    @Schema(description="委托单ID")
    private String deliveryBillId;
    @Schema(description="运单ID")
    private String waybillId;
    @Schema(description="运单itemID")
    private String waybillItemId;
    @Schema(description="运单号")
    private String waybillNum;
    @Schema(description="客户ERP编码")
    private String mdmCode;
    @Schema(description="商品ID")
    private String goodsId;
    @Schema(description="商品名称")
    private String goodsName;
    @Schema(description="商品单价")
    private BigDecimal goodsPrice;
    @Schema(description="运输类型 030230100:汽运 030230200:船运")
    private String transportType;
    @Schema(description="委托方ID")
    private String entrustingSideId;
    @Schema(description="委托方名称")
    private String entrustingSideName;
    @Schema(description="被委托方ID")
    private String entrustedSideId;
    @Schema(description="被委托方名称")
    private String entrustedSideName;
    @Schema(description="承运量")
    private BigDecimal actualQuantity;
    @Schema(description="出库仓库ID")
    private String warehouseId;
    @Schema(description="出库仓库名称")
    private String warehouseName;
    @Schema(description="原始物流单价")
    private BigDecimal originPrice;
    @Schema(description="原始物流金额")
    private BigDecimal originAmount;
    @Schema(description="最新物流单价")
    private BigDecimal newestPrice;
    @Schema(description="最新物流金额")
    private BigDecimal newestAmount;
    @Schema(description="调整前物流单价 单位：元/吨")
    private BigDecimal beforeAdjustPrice;
    @Schema(description="调整后物流金额")
    private BigDecimal adjustAmount;
    @Schema(description="价幅度 单位：元/吨")
    private BigDecimal adjustAddPrice;
    @Schema(description="调价次数")
    private Integer adjustNum;
    @Schema(description="调价类型 0：收费  1：付费   2：收付费")
    private Integer adjustType;
    @Schema(description="运单出厂时间")
    private Date leaveWarehouseTime;
    @Schema(description="运单完成时间")
    private Date completeTime;
    @Schema(description="状态 1：待回调、2：回调中、3：回调成功、4：回调失败")
    private Integer status;
}

