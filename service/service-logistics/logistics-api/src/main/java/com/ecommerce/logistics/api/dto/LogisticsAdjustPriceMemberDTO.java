
package com.ecommerce.logistics.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

@Data
@Schema(name="物流费回调用户")
public class LogisticsAdjustPriceMemberDTO {
    @Schema(description="ID")
    private String id;
    @Schema(description="物流调价ID")
    private String adjustPriceId;
    @Schema(description="委托方ID")
    private String entrustingSideId;
    @Schema(description="委托方名称")
    private String entrustingSideName;
    @Schema(description="被委托方ID")
    private String entrustedSideId;
    @Schema(description="被委托方名称")
    private String entrustedSideName;
    @Schema(description="运输量")
    private BigDecimal actualQuantity;

    @Schema(description="商品ID")
    private String goodsId;
    @Schema(description="商品名称")
    private String goodsName;
    @Schema(description="商品单价")
    private BigDecimal goodsPrice;
    @Schema(description="运输类型 030230100:汽运 030230200:船运")
    private String transportType;
    @Schema(description="调整前物流单价 单位：元/吨")
    private BigDecimal originPrice;
    @Schema(description="调整后物流单价 单位：元/吨")
    private BigDecimal newestPrice;
    @Schema(description="调整幅度 单位：元/吨")
    private BigDecimal adjustAddPrice;
    @Schema(description="物流费调增金额")
    private BigDecimal adjustAddAmount;
    @Schema(description="基础运费单价", required=true, example="23.10")
    private BigDecimal carriageUnitPrice;
    @Schema(description="辅助价目表价格", required=true, example="23.10")
    private BigDecimal auxiliaryPrice;
    @Schema(description="状态 1：待回调、2：回调中、3：回调成功、4：回调失败")
    private Byte status;
    @Schema(description="发货点ID")
    private String warehouseId;
    @Schema(description="发货点名称")
    private String warehouseName;
    @Schema(description="省")
    private String province;
    @Schema(description="省级编码")
    private String provinceCode;
    @Schema(description="城市")
    private String city;
    @Schema(description="城市编码")
    private String cityCode;
    @Schema(description="区域")
    private String district;
    @Schema(description="区域编码")
    private String districtCode;
    @Schema(description="街道")
    private String street;
    @Schema(description="街道Code")
    private String streetCode;
    @Schema(description="收货地址详情")
    private String address;
    @Schema(description="物流调价详情集合")
    private List<LogisticsAdjustPriceItemDTO> adjustPriceItemDTOs;
    private List<String> deliveryBillIds;
    @Schema(description = "回调区间 开始时间")
    private Date shipStartTime;
    @Schema(description = "回调区间 结束时间")
    private Date shipEndTime;
    @Schema(description = "运输品类ID")
    private String transportCategoryId;
    @Schema(description = "调价类型 0：收费  1：付费   2：收付费")
    private Integer adjustType;


}

