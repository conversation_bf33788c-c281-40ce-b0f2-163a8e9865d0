
package com.ecommerce.logistics.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

@Data
@Schema(name="整合运力指派实体")
public class MergeDeliveryAssignDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = -9151917710575754614L;
    @Schema(description="指派车辆ID")
    private String vehicleId;
    @Schema(description="车牌号")
    private String vehicleNum;
    @Schema(description="司机ID")
    private String driverId;
    @Schema(description="司机名")
    private String driverName;
    @Schema(description="司机电话")
    private String driverPhone;
    @Schema(description="整合的委托单列表")
    private List<MergeDeliveryItem> mergeDeliveryItemList;
    @Schema(description="操作人ID")
    private String operationUserId;
    @Schema(description="操作人名")
    private String operationUserName;
}

