
package com.ecommerce.logistics.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

@Schema(name="整合运力委托单子项")
public class MergeDeliveryItem
implements Serializable {
    @Serial
    private static final long serialVersionUID = -6906742005427053969L;
    @Schema(description="委托单ID")
    private String deliveryBillId;
    @Schema(description="整合数量")
    private BigDecimal quantity;

    public String getDeliveryBillId() {
        return this.deliveryBillId;
    }

    public BigDecimal getQuantity() {
        return this.quantity;
    }

    public void setDeliveryBillId(String deliveryBillId) {
        this.deliveryBillId = deliveryBillId;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof MergeDeliveryItem)) {
            return false;
        }
        MergeDeliveryItem other = (MergeDeliveryItem)o;
        if (!other.canEqual(this)) {
            return false;
        }
        String this$deliveryBillId = this.getDeliveryBillId();
        String other$deliveryBillId = other.getDeliveryBillId();
        if (this$deliveryBillId == null ? other$deliveryBillId != null : !this$deliveryBillId.equals(other$deliveryBillId)) {
            return false;
        }
        BigDecimal this$quantity = this.getQuantity();
        BigDecimal other$quantity = other.getQuantity();
        return !(this$quantity == null ? other$quantity != null : !((Object)this$quantity).equals(other$quantity));
    }

    protected boolean canEqual(Object other) {
        return other instanceof MergeDeliveryItem;
    }

    public int hashCode() {
        int result = 1;
        String $deliveryBillId = this.getDeliveryBillId();
        result = result * 59 + ($deliveryBillId == null ? 43 : $deliveryBillId.hashCode());
        BigDecimal $quantity = this.getQuantity();
        result = result * 59 + ($quantity == null ? 43 : ((Object)$quantity).hashCode());
        return result;
    }

    public String toString() {
        return "MergeDeliveryItem(deliveryBillId=" + this.getDeliveryBillId() + ", quantity=" + this.getQuantity() + ")";
    }
}

