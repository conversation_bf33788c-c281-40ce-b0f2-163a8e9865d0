
package com.ecommerce.logistics.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(name="运费单价查询DTO")
public class PayableCarriagePriceQueryDTO {
    @Schema(description="委托单Id")
    private String deliveryBillId;
    @Schema(description="承运人类型")
    private String carrierType;
    @Schema(description="提货类型")
    private String pickingBillType;
    @Schema(description="1-收费规则,2-航运付费规则")
    private int routeTariffType;
    @Schema(description="运费计算数量")
    private BigDecimal quantity;
}

