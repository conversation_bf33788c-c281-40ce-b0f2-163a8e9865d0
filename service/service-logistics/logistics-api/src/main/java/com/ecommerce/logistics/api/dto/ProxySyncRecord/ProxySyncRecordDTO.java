
package com.ecommerce.logistics.api.dto.ProxySyncRecord;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="背靠背单据同步记录DTO")
public class ProxySyncRecordDTO {
    @Schema(description="主键Id")
    private String proxySyncId;
    @Schema(description="单据类型")
    private String billType;
    @Schema(description="实际操作单据ID")
    private String operateBillId;
    @Schema(description="同步到的单据ID")
    private String relationBillId;
    @Schema(description="同步类型")
    private String syncType;
    @Schema(description="处理的外部状态")
    private String status;
    @Schema(description="结果描述")
    private String syncDesc;
    @Schema(description="尝试次数")
    private String tryCount;
    @Schema(description="参数json")
    private String paramText;
}

