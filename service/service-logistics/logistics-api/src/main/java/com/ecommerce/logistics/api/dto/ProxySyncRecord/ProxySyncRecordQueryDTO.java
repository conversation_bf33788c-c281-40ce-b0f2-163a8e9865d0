
package com.ecommerce.logistics.api.dto.ProxySyncRecord;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="背靠背单据同步记录查询DTO")
public class ProxySyncRecordQueryDTO {
    @Schema(description="实际操作单据ID")
    private String operateBillId;
    @Schema(description="实际操作单据Num")
    private String operateBillNum;
    @Schema(description="同步到的单据ID")
    private String relationBillId;
    @Schema(description="同步类型")
    private String syncType;
    @Schema(description="处理的外部状态")
    private String status;
}

