
package com.ecommerce.logistics.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="承运商APP紧急任务列表查询DTO")
public class QueryUrgentTaskDTO {
    @Schema(description="承运人ID")
    private String carrierId;
    @Schema(description="紧急任务时长")
    private Integer urgentTaskTime;
    @Schema(description="配送时间日期")
    private String deliveryTime;
    @Schema(description="配送时间段")
    private String deliveryTimeRange;
    @Schema(description="是否相等标识")
    private String equalFlag;
    @Schema(description="状态")
    private String status;
    @Schema(description="承运人角色类型")
    private String carrierRoleType;
}

