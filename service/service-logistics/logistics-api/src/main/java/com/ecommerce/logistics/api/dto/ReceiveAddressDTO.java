
package com.ecommerce.logistics.api.dto;

import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class ReceiveAddressDTO {
    @Schema(description="ID")
    private String id;
    @Schema(description="会员id")
    private String memberId;
    @Schema(description="收货人")
    private String consigneeName;
    @Schema(description="省份名称")
    private String provinceName;
    @Schema(description="省份编码")
    private String provinceCode;
    @Schema(description="城市名称")
    private String cityName;
    @Schema(description="城市编码")
    private String cityCode;
    @Schema(description="区县名称")
    private String districtName;
    @Schema(description="区县编码")
    private String districtCode;
    @Schema(description="街道名称")
    private String streetName;
    @Schema(description="街道编码")
    private String streetCode;
    @Schema(description="详细地址")
    private String address;
    @Schema(description="详细地址")
    private String addressDetail;
    @Schema(description="收货地别名")
    private String alias;
    @Schema(description="经纬度")
    private String coordinate;
    @Schema(description="手机号码")
    private String mobilePhone;
    @Schema(description="固定电话")
    private String phone;
    @Schema(description="卸货点地址ID")
    private Integer unloadType;
    @Schema(description="卸货点地址名称")
    private String unloadTypeName;
    @Schema(description="是否默认（1默认）")
    private Integer isDefault;
    @Schema(description="创建人")
    private String createUser;
    @Schema(description="创建时间")
    private Date createTime;
    @Schema(description="修改人")
    private String updateUser;
    @Schema(description="修改时间")
    private Date updateTime;
    @Schema(description="收货地址类型（不能为空）")
    private Integer type;
    @Schema(description="码头id")
    private String wharfId;
    @Schema(description="码头名称")
    private String wharfName;
}

