
package com.ecommerce.logistics.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(name="待改航运单列表信息")
public class ShipBillWaitRerouteListDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = 9204193717831944453L;
    @Schema(description="委托单ID")
    private String deliveryBillId;
    @Schema(description="运单ID")
    private String waybillId;
    @Schema(description="运单子项ID")
    private String waybillItemId;
    @Schema(description="运单号")
    private String waybillNum;
    @Schema(description="买家ID")
    private String buyerId;
    @Schema(description="买家名称")
    private String buyerName;
    @Schema(description="卖家ID")
    private String sellerId;
    @Schema(description="卖家名称")
    private String sellerName;
    @Schema(description="承运商名")
    private String carrierName;
    @Schema(description="船舶名称")
    private String shippingName;
    @Schema(description="船长")
    private String driverName;
    @Schema(description="船长电话")
    private String driverPhone;
    @Schema(description="商品ID")
    private String goodsId;
    @Schema(description="商品名称")
    private String goodsName;
    @Schema(description="仓库ID")
    private String warehouseId;
    @Schema(description="仓库名称")
    private String warehouseName;
    @Schema(description="仓库类型")
    private String warehouseType;
    @Schema(description="实际出厂量")
    private BigDecimal actualQuantity;
    @Schema(description="目的港ID")
    private String unloadPortId;
    @Schema(description="目的港名")
    private String unloadPortName;
    @Schema(description="实时位置")
    private String realTimeAddress;
}

