
package com.ecommerce.logistics.api.dto;

import com.ecommerce.logistics.api.enums.TransportToolTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

import lombok.Data;
import org.apache.commons.lang.StringUtils;

@Data
@Schema(name="待整合运力实体查询")
public class WaitMergeDeliveryQueryDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = 584159475867260956L;
    @Schema(description="承运人ID")
    private String carrierId;
    @Schema(description="运输类型")
    private String transportToolType = TransportToolTypeEnum.ROAD_TRANSPORT.getCode();
    @Schema(description="运输品类ID")
    private String transportCategoryId;
    @Schema(description="商品ID")
    private String goodsId;
    @Schema(description="仓库ID")
    private String warehouseId;
    @Schema(description="配送开始时间")
    private String deliveryTimeStart;
    @Schema(description="配送结束时间")
    private String deliveryTimeEnd;
    @Schema(description="是否升序排序")
    private Integer ascFlag = 1;

    public boolean validate() {
        return StringUtils.isNotBlank((String) this.carrierId) && StringUtils.isNotBlank((String) this.transportToolType) && StringUtils.isNotBlank((String) this.transportCategoryId) && StringUtils.isNotBlank((String) this.goodsId) && StringUtils.isNotBlank((String) this.warehouseId) && StringUtils.isNotBlank((String) this.deliveryTimeStart) && StringUtils.isNotBlank((String) this.deliveryTimeEnd);
    }

}

