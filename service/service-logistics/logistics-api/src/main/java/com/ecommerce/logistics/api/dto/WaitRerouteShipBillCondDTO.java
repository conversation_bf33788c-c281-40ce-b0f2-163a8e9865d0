
package com.ecommerce.logistics.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import lombok.Data;

@Data
@Schema(name="待改航运单的查询实体")
public class WaitRerouteShipBillCondDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = 4232184976519681854L;
    @Schema(description="卖家ID")
    private String sellerId;
    @Schema(description="改航到的买家ID")
    private String buyerId;
    @Schema(description="改航到的委托单ID")
    private String toDeliveryBillId;
    @Schema(description="商品ID")
    private String goodsId;
    @Schema(description="目的港")
    private String unloadPortId;
}

