
package com.ecommerce.logistics.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import lombok.Data;

@Data
@Schema(name="待同步到erp的运单实体")
public class WaitSyncErpShipBillDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = 1909790788267008167L;
    @Schema(description="委托单ID")
    private String deliveryBillId;
    @Schema(description="配送信息ID")
    private String deliveryInfoId;
    @Schema(description="运单ID")
    private String waybillId;
    @Schema(description="运单号")
    private String waybillNum;
    @Schema(description="运单状态")
    private String status;
    @Schema(description="同步标识")
    private String syncFlag;
    @Schema(description="运单的erp状态")
    private String externalWaybillStatus;
}

