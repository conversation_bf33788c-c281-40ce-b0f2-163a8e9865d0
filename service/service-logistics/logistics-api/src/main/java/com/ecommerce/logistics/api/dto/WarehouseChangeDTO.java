
package com.ecommerce.logistics.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import lombok.Data;

@Data
@Schema(name="修改仓库实体")
public class WarehouseChangeDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = -7803566736549417394L;
    @Schema(description="委托单ID")
    private String deliveryBillId;
    @Schema(description="仓库ID")
    private String warehouseId;
    @Schema(description="更新人ID")
    private String operateUserId;
    @Schema(description="更新人名称")
    private String operateUserName;
}

