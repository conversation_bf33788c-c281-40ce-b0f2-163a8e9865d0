
package com.ecommerce.logistics.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import lombok.Data;

@Data
@Schema(name="运单简要实体")
public class WaybillBriefDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = -5334319398005513881L;
    @Schema(description="运单ID")
    private String waybillId;
    @Schema(description="运单号")
    private String waybillNum;
    @Schema(description="外部运单号")
    private String externalWaybillNum;
}

