
package com.ecommerce.logistics.api.dto.adjust;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(name="价格回调 - 运单调整价格")
public class AdjustBillDTO {
    @Schema(description="运单ItemID")
    private String waybillItemId;
    @Schema(description="运单号")
    private String waybillNum;
    @Schema(description="最新成交金额")
    private BigDecimal newestPrice;
    @Schema(description="价幅度 单位：元/吨")
    private BigDecimal adjustAddPrice;
    @Schema(description="回调后成交单价 单位：元/吨")
    private BigDecimal adjustPrice;
}

