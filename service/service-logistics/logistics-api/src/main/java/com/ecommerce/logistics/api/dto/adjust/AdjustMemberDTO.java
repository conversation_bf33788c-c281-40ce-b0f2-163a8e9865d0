
package com.ecommerce.logistics.api.dto.adjust;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(name="价格回调对象查询")
public class AdjustMemberDTO {
    @Schema(description="卖家ID")
    private String sellerId;
    @Schema(description="卖家名称")
    private String sellerName;
    @Schema(description="买家ID")
    private String buyerId;
    @Schema(description="买家名称")
    private String buyerName;
    @Schema(description="合同编号")
    private String dealsName;
    @Schema(description="销售区域ID")
    private String saleRegionId;
    @Schema(description="商品ID")
    private String goodsId;
    @Schema(description="商品名称")
    private String goodsName;
    @Schema(description="仓库ID")
    private String warehouseId;
    @Schema(description="仓库名称")
    private String warehouseName;
    @Schema(description="成交量")
    private BigDecimal totalQuantity;
    @Schema(description="原始价格")
    private BigDecimal originAmount;
    @Schema(description="最新价格")
    private BigDecimal newestAmount;
    @Schema(description="订单Item ID")
    private String orderItemId;
    @Schema(description="影响运单数")
    private Integer affectNum;
}

