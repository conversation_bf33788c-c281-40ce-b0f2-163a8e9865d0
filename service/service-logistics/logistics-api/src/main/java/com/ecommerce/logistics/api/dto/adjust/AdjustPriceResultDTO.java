
package com.ecommerce.logistics.api.dto.adjust;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;
import lombok.Data;

@Data
@Schema(name="价格回调 - 调整价格")
public class AdjustPriceResultDTO {
    @Schema(description="调价编号")
    private String adjustNo;
    @Schema(description="状态 1：回调中、2：回调成功")
    private Integer adjustStatus;
    @Schema(description="运单列表")
    private List<AdjustBillDTO> billList;
    @Schema(description="操作人")
    private String operator;
}

