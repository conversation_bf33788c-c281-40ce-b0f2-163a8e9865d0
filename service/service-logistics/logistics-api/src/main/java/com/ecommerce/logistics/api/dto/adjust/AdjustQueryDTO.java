
package com.ecommerce.logistics.api.dto.adjust;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;
import java.util.List;
import java.util.Set;
import lombok.Data;

@Data
@Schema(name="价格回调对象查询")
public class AdjustQueryDTO {
    @Schema(description="卖家ID")
    private String sellerId;
    @Schema(description="销售区域IDs")
    private List<String> saleRegionIds;
    @Schema(description="商品IDs")
    private List<String> goodsIds;
    @Schema(description="买家IDs")
    private List<String> buyerIds;
    @Schema(description="合同编号")
    private String dealsName;
    @Schema(description="回调区间 开始时间")
    private Date shipStartTime;
    @Schema(description="回调区间 结束时间")
    private Date shipEndTime;
    @Schema(description="运输类型 030230100:汽运 030230200:船运 030230300:空运 030230400:铁运")
    private String transportType;
    @Schema(description="运输类型 030230100:汽运 030230200:船运 030230300:空运 030230400:铁运")
    private String transportTypes;
    @Schema(description="配送方式 030060100:买家自提 030060200:卖家配送 030060300:平台配送 030060400:卖家委托 030060500:门店配送")
    private String deliveryType;
    @Schema(description="配送方式 030060100:买家自提 030060200:卖家配送 030060300:平台配送 030060400:卖家委托 030060500:门店配送")
    private Set<String> deliveryTypes;
    @Schema(description="发票类型 1：一票制、2：2票制")
    private Integer billType;
    @Schema(description="出库仓库ID")
    private List<String> warehouseList;
    @Schema(description="要排除调价的买家ID")
    private List<String> excludeBuyerIds;
    @Schema(description="要调价的运单")
    private List<String> waybillNums;
}

