
package com.ecommerce.logistics.api.dto.adjust;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

@Data
@Schema(name="价格回调运单查询")
public class AdjustShipBillDTO {
    @Schema(description="运单ID")
    private String waybillItemId;
    @Schema(description="运单编号")
    private String waybillNum;
    @Schema(description="外部运单编号")
    private String externalWaybillNum;
    @Schema(description="卖家ID")
    private String sellerId;
    @Schema(description="卖家名称")
    private String sellerName;
    @Schema(description="买家ID")
    private String buyerId;
    @Schema(description="买家名称")
    private String buyerName;
    @Schema(description="合同编号")
    private String dealsName;
    @Schema(description="销售区域ID")
    private String saleRegionId;
    @Schema(description="商品ID")
    private String goodsId;
    @Schema(description="商品名称")
    private String goodsName;
    @Schema(description="仓库ID")
    private String warehouseId;
    @Schema(description="仓库")
    private String warehouseName;
    @Schema(description="成交量")
    private BigDecimal totalQuantity;
    @Schema(description="原始成交单价")
    private BigDecimal originPrice;
    @Schema(description="原始成交金额")
    private BigDecimal originAmount;
    @Schema(description="最新成交单价")
    private BigDecimal newestPrice;
    @Schema(description="物流单价")
    private BigDecimal logisticsPrice;
    @Schema(description="最新成交总额")
    private BigDecimal newestAmount;
    @Schema(description="订单Item ID")
    private String orderItemId;
    @Schema(description="配送方式")
    private String deliveryType;
    @Schema(description="运输方式")
    private String transportType;
    @Schema(description="MdmCode")
    private String mdmCode;
    @Schema(description="发票类型 1：一票制、2：2票制")
    private Integer billType;
    @Schema(description="调价次数")
    private Integer adjustNum;
    @Schema(description="运单出厂时间")
    private Date leaveWarehouseTime;
    @Schema(description="运单完成时间")
    private Date completeTime;
    @Schema(description="影响运单数")
    private Integer affectNum;
}

