
package com.ecommerce.logistics.api.dto.adjust;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(name="发货单调价实体")
public class AdjustTakeInfoDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = 530213247173919014L;
    @Schema(description="发货单号")
    private String takeCode;
    @Schema(description="最新单价")
    private BigDecimal newestPrice;
}

