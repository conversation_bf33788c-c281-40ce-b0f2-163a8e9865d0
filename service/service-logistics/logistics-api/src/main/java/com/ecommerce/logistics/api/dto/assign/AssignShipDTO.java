
package com.ecommerce.logistics.api.dto.assign;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

@Data
@Schema(name="船运指派信息实体")
public class AssignShipDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = 7537711955074287947L;
    @Schema(description="指派给船的数量,适用于自提在发货单批量指派")
    private BigDecimal quantity = BigDecimal.ZERO;
    @Schema(description="船只ID")
    private String shippingId;
    @Schema(description="船舶编号")
    private String shippingNo;
    @Schema(description="船舶名称")
    private String shippingName;
    @Schema(description="船长ID")
    private String driverId = "";
    @NotBlank(message="船长姓名")
    @Schema(description="船长姓名")
    private String driverName;
    @NotBlank(message="船长联系电话")
    @Schema(description="船长联系电话")
    private String driverPhone;
    @Schema(description="委托单ID")
    private String deliveryBillId;
    @Schema(description="期望配送时间")
    private Date deliveryTime;
    @Schema(description="计划到港时间")
    private Date planArriveTime;
    @Schema(description="是否内部调拨单,00-否，01-是")
    private String internal;
}

