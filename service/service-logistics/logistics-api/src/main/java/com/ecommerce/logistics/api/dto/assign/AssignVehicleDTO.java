
package com.ecommerce.logistics.api.dto.assign;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

@Data
@Schema(name="指派车辆对象")
public class AssignVehicleDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = -7625295415101208931L;
    @Schema(description="是否为社会运力抢单")
    private Integer snatchFlag = 0;
    @Schema(description="指派给车辆的数量,适用于自提在发货单批量指派")
    private BigDecimal quantity = BigDecimal.ZERO;
    @Schema(description="车辆ID", required=false)
    private String vehicleId;
    @Schema(description="车牌号码", required=false)
    private String number;
    @Schema(description="司机ID", required=false)
    private String driverId = "";
    @NotBlank(message="司机姓名")
    @Schema(description="司机姓名", required=false)
    private String driverName;
    @NotBlank(message="司机联系电话")
    @Schema(description="司机联系电话", required=false)
    private String driverPhone;
    @Schema(description="委托单ID")
    private String deliveryBillId;
    @Schema(description="期望配送时间")
    private String deliveryTime;
    @Schema(description="产地")
    private String originPlace;
    @Schema(description="是否计算空载费")
    private Byte emptyLoadFlag = 0;
    @Schema(description="外部运单号")
    private String externalWaybillNum;
    @Schema(description="二维码内容")
    private String qrCode;
    private Long version;
    @Schema(description="所有泵号列表,逗号分隔,与下方对应,会出现重复")
    private String allPumpNo;
    @Schema(description="泵手列表,逗号分隔,与上方对应,会出现重复")
    private String allPumperName;
}

