
package com.ecommerce.logistics.api.dto.attachment;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import lombok.Data;

@Data
@Schema(name="新增附件对象")
public class AttAddDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = 1291678722941702801L;
    @Schema(description="业务实体Id")
    private String entryId;
    @Schema(description="附件类型")
    private String type;
    @Schema(description="文件全名")
    private String fileName;
    @Schema(description="文件扩展名")
    private String fileExt;
    @Schema(description="附件url")
    private String fileUrl;
    @Schema(description="创建用户")
    private String createUser;
    @Schema(description="获取url时使用的bid")
    private String bid;
    @Schema(description="前端定义，图片位置序号，1,2,3...")
    private Integer serialNo;
}

