
package com.ecommerce.logistics.api.dto.attachment;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="附件列表对象")
public class AttListDTO {
    @Schema(description="附件Id")
    private String attachmentId;
    @Schema(description="业务实体Id")
    private String entryId;
    @Schema(description="附件类型")
    private String type;
    @Schema(description="文件全名")
    private String fileName;
    @Schema(description="文件扩展名")
    private String fileExt;
    @Schema(description="附件url")
    private String fileUrl;
    @Schema(description="附件bId")
    private String bid;
    @Schema(description="真实附件url")
    private String realFileUrl;
}

