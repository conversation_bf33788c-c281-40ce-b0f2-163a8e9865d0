
package com.ecommerce.logistics.api.dto.attachment;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="附件列表查询对象")
public class AttListQueryDTO {
    @Schema(description="附件Id", required=false)
    private String attachmentId;
    @Schema(description="实体Id", required=false)
    private String entryId;
    @Schema(description="附件类型", required=false)
    private String type;
}

