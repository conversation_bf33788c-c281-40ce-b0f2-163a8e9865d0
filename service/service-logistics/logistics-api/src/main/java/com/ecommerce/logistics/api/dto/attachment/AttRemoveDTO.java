
package com.ecommerce.logistics.api.dto.attachment;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="附件删除对象")
public class AttRemoveDTO {
    @Schema(description="附件Id", required=false)
    private String attachmentId;
    @Schema(description="实体Id", required=false)
    private String entryId;
    @Schema(description="更新用户", required=false)
    private String updateUser;
}

