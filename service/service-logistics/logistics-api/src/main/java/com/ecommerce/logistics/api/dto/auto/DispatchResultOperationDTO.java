
package com.ecommerce.logistics.api.dto.auto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

@Data
@Schema(name="智能调度操作实体")
public class DispatchResultOperationDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = 4617733023389320708L;
    @Schema(description="操作结果记录")
    private List<VehicleAutoAssignDTO> vehicleAutoAssignDTOList;
    @Schema(description="操作人ID")
    private String operateUserId;
    @Schema(description="操作人名")
    private String operateUserName;
}

