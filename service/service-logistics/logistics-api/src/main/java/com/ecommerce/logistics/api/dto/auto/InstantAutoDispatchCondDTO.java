
package com.ecommerce.logistics.api.dto.auto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(name="实时智能调度条件实体")
public class InstantAutoDispatchCondDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = 7661188589693681408L;
    @Schema(description="委托单ID")
    private String deliveryBillId;
    @Schema(description="是否整车")
    private boolean fullLoadFlag = true;
    @Schema(description="用户ID")
    private String userId;
    @Schema(description="用户类型")
    private Integer userType;
    @Schema(description="操作人ID")
    private String operateUserId;
    @Schema(description="操作人名")
    private String operateUserName;
    @Schema(description="车型ID")
    private String vehicleTypeId;
    @Schema(description="车辆数")
    private Integer vehicleCount;
    @Schema(description="距离")
    private BigDecimal kmDistance;
}

