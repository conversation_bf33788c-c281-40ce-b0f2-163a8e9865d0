
package com.ecommerce.logistics.api.dto.auto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import lombok.Data;

@Data
@Schema(name="最大运力查询对象")
public class TransportCapacityDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    @Schema(description="委托人ID")
    private String consignorId;
    @Schema(description="承运商ID")
    private String carrierId;
    @Schema(description="配送时间日期")
    private String deliveryTime;
    @Schema(description="承运人角色类型")
    private String carrierRoleType;
}

