
package com.ecommerce.logistics.api.dto.auto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(name="最大运力返回对象")
public class TransportCapacityResultDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    @Schema(description="每天可接单数（车次）")
    private Integer canTakeOrderNum;
    @Schema(description="委托数量")
    private BigDecimal quantity;
    @Schema(description="委托单ID")
    private String deliveryInfoId;
    @Schema(description="承运商ID")
    private String carrierId;
    @Schema(description="委托人ID")
    private String consignorId;
    @Schema(description="期望时间")
    private String deliveryTime;
}

