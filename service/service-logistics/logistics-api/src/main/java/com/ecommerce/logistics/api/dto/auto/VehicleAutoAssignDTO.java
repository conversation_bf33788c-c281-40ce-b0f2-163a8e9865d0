
package com.ecommerce.logistics.api.dto.auto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

@Data
@Schema(name="车辆调度实体")
public class VehicleAutoAssignDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = 362647759425268153L;
    @Schema(description="智能调度批次ID")
    private String batchId;
    @Schema(description="调度结果ID")
    private String dispatchResultId;
    @Schema(description="委托单ID")
    private String deliveryBillId;
    @Schema(description="委托单号")
    private String deliveryBillNum;
    @Schema(description="车辆ID")
    private String vehicleId;
    @Schema(description="车牌号")
    private String vehicleNum;
    @Schema(description="司机ID")
    private String driverId;
    @Schema(description="司机名称")
    private String driverName;
    @Schema(description="司机电话")
    private String driverPhone;
    @Schema(description="期望配送时间")
    private Date deliveryTime;
    @Schema(description="期望配送时间区间")
    private String deliveryTimeRange;
    @Schema(description="开始时间")
    private Date deliveryTimeStart;
    @Schema(description="结束时间")
    private Date deliveryTimeEnd;
    @Schema(description="距离")
    private BigDecimal kmDistance;
    @Schema(description="最大载重量")
    private BigDecimal maxLoadCapacity;
    @Schema(description="指派量")
    private BigDecimal quantity;
    @Schema(description="商品ID")
    private String goodsId;
    @Schema(description="商品名称")
    private String goodsName;
    @Schema(description="单位")
    private String unit;
    @Schema(description="车型ID")
    private String vehicleTypeId;
    @Schema(description="车型名称")
    private String vehicleTypeName;
    @Schema(description="仓库ID")
    private String warehouseId;
    @Schema(description="仓库名称")
    private String warehouseName;
    @Schema(description="仓库地址")
    private String warehouseAddress;
    @Schema(description="收货地址ID")
    private String receiverAddressId;
    @Schema(description="收货地址")
    private String address;
    @Schema(description="预估运距")
    private BigDecimal estimateKm;
    @Schema(description="预估时间")
    private BigDecimal estimateDuration;
    @Schema(description="空跑距离")
    private BigDecimal noLoadKm = BigDecimal.ZERO;
    @Schema(description="装货时间")
    private BigDecimal loadingTime;
    @Schema(description="卸货时间")
    private BigDecimal unloadingTime;
    @Schema(description="状态")
    private String status;
}

