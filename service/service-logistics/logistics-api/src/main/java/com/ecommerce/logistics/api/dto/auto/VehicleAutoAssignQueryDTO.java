
package com.ecommerce.logistics.api.dto.auto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import lombok.Data;

@Data
@Schema(name="智能调度条件查询实体")
public class VehicleAutoAssignQueryDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = -1031574134655153885L;
    @Schema(description="批次ID")
    private String batchId;
    @Schema(description="调度状态")
    private String status;
    @Schema(description="仓库ID")
    private String warehouseId;
    @Schema(description="收货地址ID")
    private String receiverAddressId;
    @Schema(description="期望配送日期开始")
    private String deliveryTimeStart;
    @Schema(description="期望配送日期结束")
    private String deliveryTimeEnd;
}

