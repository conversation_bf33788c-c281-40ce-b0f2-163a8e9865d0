
package com.ecommerce.logistics.api.dto.auto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
@Schema(name="待计划调度的委托单实体")
public class WaitPlanAutoDispatchDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = -4864870215172402552L;
    @Schema(description="待计划调度的委托单列表")
    private List<PlanDeliveryListDTO> planDeliveryListDTOList;
    private String unit = "吨";
    @Schema(description="委托单数量")
    private BigDecimal quantity;
    @Schema(description="待安排量")
    private BigDecimal unplannedQuantity;
}

