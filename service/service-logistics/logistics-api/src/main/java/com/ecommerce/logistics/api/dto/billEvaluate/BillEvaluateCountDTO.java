
package com.ecommerce.logistics.api.dto.billEvaluate;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="评价分数统计DTO")
public class BillEvaluateCountDTO {
    @Schema(description="运输效率评分")
    private int tcScore;
    @Schema(description="运输安全评分")
    private int tsScore;
    @Schema(description="服务质量评分")
    private int sqScore;
    @Schema(description="客户满意度评分")
    private int csScore;
    @Schema(description="托运方评分")
    private int shipperScore;
}

