
package com.ecommerce.logistics.api.dto.billEvaluate;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;
import lombok.Data;

@Data
@Schema(name="单据评价记录列表DTO")
public class BillEvaluateListDTO {
    @Schema(description="评价编号")
    private String evaluateNum;
    @Schema(description="评价类型")
    private String evaluateType;
    @Schema(description="关联单据类型")
    private String relationBillType;
    @Schema(description="关联单据编号")
    private String relationBillNum;
    @Schema(description="被评价主体类型")
    private String evaluatedPersonType;
    @Schema(description="被评价主体名称")
    private String evaluatedPersonName;
    @Schema(description="运输效率评分")
    private Byte tcScore;
    @Schema(description="运输安全评分")
    private Byte tsScore;
    @Schema(description="服务质量评分")
    private Byte sqScore;
    @Schema(description="客户满意度评分")
    private Byte csScore;
    @Schema(description="托运方评分")
    private Byte shipperScore;
    @Schema(description="评价人名称")
    private String evaluatorName;
    @Schema(description="评价人类型")
    private String evaluatorType;
    @Schema(description="评价时间")
    private Date createTime;
}

