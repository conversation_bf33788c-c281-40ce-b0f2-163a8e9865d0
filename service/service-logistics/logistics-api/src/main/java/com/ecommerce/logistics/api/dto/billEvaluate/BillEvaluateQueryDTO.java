
package com.ecommerce.logistics.api.dto.billEvaluate;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="单据评价记录查询DTO")
public class BillEvaluateQueryDTO {
    @Schema(description="评价维度（评价承运方/托运方）")
    private String type;
    @Schema(description="评价类型")
    private String evaluateType;
    @Schema(description="被评价主体ID")
    private String evaluatedPersonId;
    @Schema(description="被评价主体类型")
    private String evaluatedPersonType;
    @Schema(description="评价人ID")
    private String evaluatorId;
    @Schema(description="评价人类型")
    private String evaluatorType;
    @Schema(description="开始时间")
    private String beginTime;
    @Schema(description="结束时间")
    private String endTime;
}

