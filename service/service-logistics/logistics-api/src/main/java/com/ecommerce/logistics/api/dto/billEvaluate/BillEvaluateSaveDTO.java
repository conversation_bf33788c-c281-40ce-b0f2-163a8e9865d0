
package com.ecommerce.logistics.api.dto.billEvaluate;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

@Data
@Schema(name="单据评价记录保存DTO")
public class BillEvaluateSaveDTO {
    @Schema(description="评价类型")
    @NotBlank(message="评价类型不能为空")
    private String evaluateType;
    @Schema(description="评价维度（评价承运方/托运方）")
    @NotBlank(message="评价维度不能为空")
    private String type;
    @Schema(description="关联单据ID")
    @NotBlank(message="关联单据ID不能为空")
    private String relationBillId;
    @Schema(description="关联单据编号")
    @NotBlank(message="关联单据编号不能为空")
    private String relationBillNum;
    @Schema(description="关联单据类型")
    @NotBlank(message="关联单据类型不能为空")
    private String relationBillType;
    @Schema(description="被评价主体ID")
    @NotBlank(message="被评价主体ID不能为空")
    private String evaluatedPersonId;
    @Schema(description="被评价主体名称")
    @NotBlank(message="被评价主体名称不能为空")
    private String evaluatedPersonName;
    @Schema(description="被评价主体类型")
    @NotBlank(message="被评价主体类型不能为空")
    private String evaluatedPersonType;
    @Schema(description="评价人ID")
    @NotBlank(message="评价人ID不能为空")
    private String evaluatorId;
    @Schema(description="评价人名称")
    @NotBlank(message="评价人名称不能为空")
    private String evaluatorName;
    @Schema(description="评价人类型")
    @NotBlank(message="评价人类型不能为空")
    private String evaluatorType;
    @Schema(description="创建用户")
    @NotBlank(message="创建用户不能为空")
    private String createUser;
    @Schema(description="运输效率评分")
    private Byte tcScore;
    @Schema(description="运输安全评分")
    private Byte tsScore;
    @Schema(description="服务质量评分")
    private Byte sqScore;
    @Schema(description="客户满意度评分")
    private Byte csScore;
    @Schema(description="托运方评分")
    private Byte shipperScore;
}

