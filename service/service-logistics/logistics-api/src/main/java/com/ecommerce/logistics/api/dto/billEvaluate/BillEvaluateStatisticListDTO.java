
package com.ecommerce.logistics.api.dto.billEvaluate;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="评价统计列表DTO")
public class BillEvaluateStatisticListDTO {
    @Schema(description="被评价主体Id")
    private String evaluatedPersonId;
    @Schema(description="被评价主体名称")
    private String evaluatedPersonName;
    @Schema(description="被评价主体类型")
    private String evaluatedPersonType;
    @Schema(description="评价次数")
    private Integer evaluateCount;
    @Schema(description="平均运输效率评分")
    private Byte aveTcScore;
    @Schema(description="平均运输安全评分")
    private Byte aveTsScore;
    @Schema(description="平均服务质量评分")
    private Byte aveSqScore;
    @Schema(description="平均客户满意度评分")
    private Byte aveCsScore;
    @Schema(description="平均分")
    private Byte aveScore;
    @Schema(description="排名")
    private String rank;
}

