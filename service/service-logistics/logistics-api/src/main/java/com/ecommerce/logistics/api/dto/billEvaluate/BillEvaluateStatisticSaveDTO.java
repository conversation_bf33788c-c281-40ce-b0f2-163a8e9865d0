
package com.ecommerce.logistics.api.dto.billEvaluate;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="评价统计保存DTO")
public class BillEvaluateStatisticSaveDTO {
    @Schema(description="评价维度（评价承运方/托运方）")
    private String type;
    @Schema(description="被评价主体ID")
    private String evaluatedPersonId;
    @Schema(description="被评价主体名称")
    private String evaluatedPersonName;
    @Schema(description="被评价主体类型")
    private String evaluatedPersonType;
    @Schema(description="运输效率评分")
    private Byte tcScore;
    @Schema(description="运输安全评分")
    private Byte tsScore;
    @Schema(description="服务质量评分")
    private Byte sqScore;
    @Schema(description="客户满意度评分")
    private Byte csScore;
    @Schema(description="托运方评分")
    private Byte shipperScore;
    @Schema(description="操作人ID")
    private String operatorUserId;
}

