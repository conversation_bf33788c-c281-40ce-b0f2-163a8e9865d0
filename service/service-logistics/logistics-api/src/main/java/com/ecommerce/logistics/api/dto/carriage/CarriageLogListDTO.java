
package com.ecommerce.logistics.api.dto.carriage;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;
import jakarta.persistence.Column;
import lombok.Data;

@Data
@Schema(name="运费日志列表对象")
public class CarriageLogListDTO {
    @Schema(description="操作人ID")
    private String operatorId;
    @Column(name="operator_name")
    @Schema(description="操作人姓名")
    private String operatorName;
    @Schema(description="创建时间")
    private Date createTime;
    @Schema(description="前置规则")
    private String preRule;
    @Schema(description="变更后规则")
    private String nextRule;
    @Schema(description="类别 添加,修改,删除")
    private String categoryType;
    @Schema(description="附件url集合", required=false)
    private String attachmentUrl;
}

