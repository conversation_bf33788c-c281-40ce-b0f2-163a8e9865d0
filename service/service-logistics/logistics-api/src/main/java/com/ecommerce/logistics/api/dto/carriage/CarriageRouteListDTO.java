
package com.ecommerce.logistics.api.dto.carriage;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;
import java.util.List;
import lombok.Data;

@Data
@Schema(name="运费路线列表对象")
public class CarriageRouteListDTO {
    @Schema(description="运费路线ID", required=false, example="askdoamckasxxxx")
    private String carriageRouteId;
    @Schema(description="路线名称", required=false, example="askdoamckasxxxx")
    private String routeName;
    @Schema(description="定价类型", required=false, example="askdoamckasxxxx")
    private String pricingType;
    @Schema(description="提货点ID", required=false, example="askdoamckasxxxx")
    private String warehouseId;
    @Schema(description="提货点名称", required=false, example="askdoamckasxxxx")
    private String warehouseName;
    @Schema(description="提货点类型", required=false, example="askdoamckasxxxx")
    private String warehouseType;
    @Schema(description="省份", required=false, example="askdoamckasxxxx")
    private String warehouseProvince;
    @Schema(description="省份代码", required=false, example="askdoamckasxxxx")
    private String warehouseProvinceCode;
    @Schema(description="城市", required=false, example="askdoamckasxxxx")
    private String warehouseCity;
    @Schema(description="城市代码", required=false, example="askdoamckasxxxx")
    private String warehouseCityCode;
    @Schema(description="地区", required=false, example="askdoamckasxxxx")
    private String warehouseDistrict;
    @Schema(description="地区代码", required=false, example="askdoamckasxxxx")
    private String warehouseDistrictCode;
    @Schema(description="详细地址", required=false, example="askdoamckasxxxx")
    private String warehouseAddress;
    @Schema(description="提货点经纬度", required=false, example="askdoamckasxxxx")
    private String warehouseLocation;
    @Schema(description="收获地址ID", required=false, example="askdoamckasxxxx")
    private String receiveAddressId;
    @Schema(description="提货点详细地址", required=false, example="askdoamckasxxxx")
    private String receiveAddressDetail;
    @Schema(description="地址经纬度", required=false, example="askdoamckasxxxx")
    private String receiveAddressLocation;
    @Schema(description="运输品类ID", required=false, example="askdoamckasxxxx")
    private String transportCategoryId;
    @Schema(description="运输品类ID", required=false, example="askdoamckasxxxx")
    private String transportCategoryName;
    @Schema(description="路线配送区域列表", required=false)
    private List<CarriageZoneMapDTO> zoneMapList;
    @Schema(description="创建时间", required=false, example="askdoamckasxxxx")
    private Date createTime;
}

