
package com.ecommerce.logistics.api.dto.carriage;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;
import lombok.Data;

@Data
@Schema(name="运费路线规则保存对象")
public class CarriageRuleSaveDTO<T> {
    @Schema(description="运费路线所属的会员ID", required=false, example="askdoamckasxxxx")
    private String memberId;
    @Schema(description="运费路线ID", required=false, example="askdoamckasxxxx")
    private String carriageRouteId;
    @Schema(description="定价类型", required=false, example="030320100")
    private String pricingType;
    @Schema(description="结算类型", required=false, example="030330200")
    private String settlementType;
    @Schema(description="运费规则列表", required=false)
    private List<CarriageRuleDTO<T>> carriageRuleList;
    @Schema(description="附件url集合", required=false)
    private String attachmentUrl;
    @Schema(description="编辑标识", required=false)
    private Integer editFlag;
    @Schema(description="运输品类ID", required=false, example="askdoamckasxxxx")
    private String transportCategoryId;
    @Schema(description="提货点ID", required=false, example="askdoamckasxxxx")
    private String warehouseId;
    @Schema(description="收货地址ID")
    private String receiveAddressId;
    @Schema(description="操作人ID", required=true, example="10010")
    private String operatorUserId;
    @Schema(description="操作人姓名", required=true, example="王大锤")
    private String operatorUserName;
}

