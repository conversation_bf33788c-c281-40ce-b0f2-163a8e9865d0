
package com.ecommerce.logistics.api.dto.carriage;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="运费路线配送区域对象")
public class CarriageZoneMapDTO {
    @Schema(description="收货省份", required=false, example="askdoamckasxxxx")
    private String province;
    @Schema(description="省份编码", required=false, example="askdoamckasxxxx")
    private String provinceCode;
    @Schema(description="收货城市", required=false, example="askdoamckasxxxx")
    private String city;
    @Schema(description="城市编码", required=false, example="askdoamckasxxxx")
    private String cityCode;
    @Schema(description="收货地区", required=false, example="askdoamckasxxxx")
    private String district;
    @Schema(description="区域编码", required=false, example="askdoamckasxxxx")
    private String districtCode;
    @Schema(description="收货街道", required=false, example="askdoamckasxxxx")
    private String street;
    @Schema(description="收货街道编码", required=false, example="askdoamckasxxxx")
    private String streetCode;
}

