
package com.ecommerce.logistics.api.dto.carriage;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="ERP运输路线对象")
public class TransportRouteDTO {
    @Schema(description="运费路线ID", required=false, example="askdoamckasxxxx")
    private String carriageRouteId;
    @Schema(description="合同地址编号")
    private String contractAddressCode;
    @Schema(description="合同地址详细信息")
    private String contractAddressDetail;
    @Schema(description="ERP路线别名")
    private String contractAddressName;
    @Schema(description="提货点编码")
    private String pickupPointCode;
}

