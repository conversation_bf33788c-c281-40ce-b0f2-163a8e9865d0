
package com.ecommerce.logistics.api.dto.carriagerule;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(name="买家运费规则明细对象")
public class BuyerCarriageItemDTO {
    @Schema(description="运费规则明细ID", required=false, example="askdoamckasxxxx")
    private String carriageItemId;
    @Schema(description="运费规则ID", required=false, example="askdoamckasxxxx")
    private String carriageRuleId;
    @Schema(description="省份", required=false, example="1")
    private String province;
    @Schema(description="省份编码", required=false, example="1")
    private String provinceCode;
    @Schema(description="城市", required=false, example="2")
    private String city;
    @Schema(description="城市编码", required=false, example="2")
    private String cityCode;
    @Schema(description="地区", required=false, example="3")
    private String district;
    @Schema(description="地区编码", required=false, example="3")
    private String districtCode;
    @Schema(description="运输单价", required=false, example="10.00")
    private BigDecimal transportPrice;
    @Schema(description="装卸单价", required=false, example="10.00")
    private BigDecimal carryPrice;
}

