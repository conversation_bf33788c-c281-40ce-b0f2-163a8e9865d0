
package com.ecommerce.logistics.api.dto.carriagerule;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="买家运费规则删除DTO对象")
public class BuyerCarriageRuleDeleteDTO {
    @Schema(description="运费规则ID", required=false, example="askdoamckasxxxx")
    private String carriageRuleId;
    @Schema(description="操作人ID", required=true, example="10010")
    private String operatorUserId;
    @Schema(description="操作人姓名", required=true, example="王大锤")
    private String operatorUserName;
}

