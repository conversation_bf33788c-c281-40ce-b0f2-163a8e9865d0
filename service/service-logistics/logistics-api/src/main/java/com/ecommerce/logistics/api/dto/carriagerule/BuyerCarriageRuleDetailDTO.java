
package com.ecommerce.logistics.api.dto.carriagerule;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
@Schema(name="买家运费规则列表详情对象")
public class BuyerCarriageRuleDetailDTO {
    @Schema(description="运费规则ID", required=false, example="askdoamckasxxxx")
    private String carriageRuleId;
    @Schema(description="中心仓ID", required=false, example="askdoamckasxxxx")
    private String warehouseId;
    @Schema(description="中心仓名称", required=false, example="askdoamckasxxxx")
    private String warehouseName;
    @Schema(description="中心仓类型", required=false, example="askdoamckasxxxx")
    private String warehouseType;
    @Schema(description="运输品类ID", required=false, example="askdoamckasxxxx")
    private String transportCategoryId;
    @Schema(description="运输品类ID", required=false, example="aiijmmda")
    private String transportCategoryName;
    @Schema(description="运输单价", required=false, example="10.00")
    private BigDecimal transportPrice;
    @Schema(description="装卸单价", required=false, example="10.00")
    private BigDecimal carryPrice;
    @Schema(description="创建时间", required=false, example="2019-01-26 18:36:16")
    private String createTime;
    @Schema(description="买家运费规则明细列表")
    private List<BuyerCarriageItemDTO> carriageItemList;
}

