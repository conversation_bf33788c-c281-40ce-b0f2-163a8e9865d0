
package com.ecommerce.logistics.api.dto.carriagerule;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="买家运费规则查询对象")
public class BuyerCarriageRuleQueryDTO {
    @Schema(description="中心仓ID", required=false, example="askdoamckasxxxx")
    private String warehouseId;
    @Schema(description="运输品类ID", required=false, example="askdoamckasxxxx")
    private String transportCategoryId;
}

