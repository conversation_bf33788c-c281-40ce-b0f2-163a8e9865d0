
package com.ecommerce.logistics.api.dto.carriagerule;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
@Schema(name="买家运费规则保存对象")
public class BuyerCarriageRuleSaveDTO {
    @Schema(description="运费规则ID", required=false, example="askdoamckasxxxx")
    private String carriageRuleId;
    @Schema(description="中心仓ID", required=false, example="askdoamckasxxxx")
    private String warehouseId;
    @Schema(description="运输品类ID", required=false, example="askdoamckasxxxx")
    private String transportCategoryId;
    @Schema(description="运输单价", required=false, example="10.00")
    private BigDecimal transportPrice;
    @Schema(description="装卸单价", required=false, example="10.00")
    private BigDecimal carryPrice;
    @Schema(description="买家运费规则明细列表")
    private List<BuyerCarriageItemDTO> carriageItemList;
    @Schema(description="操作人ID", required=true, example="10010")
    private String operatorUserId;
    @Schema(description="操作人姓名", required=true, example="王大锤")
    private String operatorUserName;
}

