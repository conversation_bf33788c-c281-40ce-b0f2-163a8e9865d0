
package com.ecommerce.logistics.api.dto.carriagerule;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="承运商运费规则明细对象")
public class CarrierCarriageItemDTO {
    @Schema(description="运费规则明细ID", required=false, example="askdoamckasxxxx")
    private String carriageItemId;
    @Schema(description="运费规则ID", required=false, example="askdoamckasxxxx")
    private String carriageRuleId;
    @Schema(description="区间范围类型", required=false, example="030260100")
    private String sectionType;
    @Schema(description="最小范围", required=false, example="1")
    private Integer minSection;
    @Schema(description="最大范围", required=false, example="1")
    private Integer maxSection;
    @Schema(description="规则表达式", required=false, example="1")
    private String ruleExpression;
}

