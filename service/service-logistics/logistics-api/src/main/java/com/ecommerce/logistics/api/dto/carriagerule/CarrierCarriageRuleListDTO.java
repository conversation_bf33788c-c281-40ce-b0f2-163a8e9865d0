
package com.ecommerce.logistics.api.dto.carriagerule;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="承运商运费规则列表对象")
public class CarrierCarriageRuleListDTO {
    @Schema(description="运费规则ID", required=false, example="askdoamckasxxxx")
    private String carriageRuleId;
    @Schema(description="省份", required=false, example="1")
    private String province;
    @Schema(description="省份编码", required=false, example="1")
    private String provinceCode;
    @Schema(description="城市", required=false, example="2")
    private String city;
    @Schema(description="城市编码", required=false, example="2")
    private String cityCode;
    @Schema(description="地区", required=false, example="3")
    private String district;
    @Schema(description="地区编码", required=false, example="3")
    private String districtCode;
    @Schema(description="运输品类ID", required=false, example="aiijmmda")
    private String transportCategoryId;
    @Schema(description="运输品类ID", required=false, example="aiijmmda")
    private String transportCategoryName;
    @Schema(description="车型ID", required=false, example="ddd")
    private String vehicleTypeId;
    @Schema(description="车型名称", required=false, example="ddd")
    private String vehicleTypeName;
    @Schema(description="创建时间", required=false, example="2019-01-26")
    private String createTime;
}

