
package com.ecommerce.logistics.api.dto.carriagerule;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="承运商运费规则查询对象")
public class CarrierCarriageRuleQueryDTO {
    @Schema(description="省份编码", required=false, example="1")
    private String provinceCode;
    @Schema(description="城市编码", required=false, example="2")
    private String cityCode;
    @Schema(description="地区编码", required=false, example="3")
    private String districtCode;
    @Schema(description="运输品类ID", required=false, example="aiijmmda")
    private String transportCategoryId;
    @Schema(description="车型ID", required=false, example="aiijmmda")
    private String vehicleTypeId;
}

