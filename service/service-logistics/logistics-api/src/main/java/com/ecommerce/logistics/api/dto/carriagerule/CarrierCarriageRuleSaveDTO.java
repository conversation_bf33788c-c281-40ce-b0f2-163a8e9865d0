
package com.ecommerce.logistics.api.dto.carriagerule;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;
import lombok.Data;

@Data
@Schema(name="承运商运费规则保存对象")
public class CarrierCarriageRuleSaveDTO {
    @Schema(description="运费规则ID", required=false, example="askdoamckasxxxx")
    private String carriageRuleId;
    @Schema(description="省份", required=false, example="1")
    private String province;
    @Schema(description="省份编码", required=false, example="1")
    private String provinceCode;
    @Schema(description="城市", required=false, example="2")
    private String city;
    @Schema(description="城市编码", required=false, example="2")
    private String cityCode;
    @Schema(description="地区", required=false, example="3")
    private String district;
    @Schema(description="地区编码", required=false, example="3")
    private String districtCode;
    @Schema(description="运输品类ID", required=false, example="aiijmmda")
    private String transportCategoryId;
    @Schema(description="车型ID", required=false, example="ddd")
    private String vehicleTypeId;
    @Schema(description="承运商运费规则明细列表")
    private List<CarrierCarriageItemDTO> carriageItemList;
    @Schema(description="操作人ID", required=true, example="10010")
    private String operatorUserId;
    @Schema(description="操作人姓名", required=true, example="王大锤")
    private String operatorUserName;
}

