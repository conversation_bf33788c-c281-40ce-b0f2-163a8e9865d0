
package com.ecommerce.logistics.api.dto.complaint;

import com.ecommerce.logistics.api.dto.attachment.AttListDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

@Data
@Schema(name="投诉记录分页查询返回结果")
public class ComplaintDTO {
    @Schema(description="投诉记录ID")
    private String complaintId;
    @Schema(description="投诉编号")
    private String complaintNum;
    @Schema(description="投诉类型枚举[1.运输时效2.运输质量3.服务态度4.其他]")
    private String complaintType;
    @Schema(description="关联单据号")
    private String relationBillNum;
    @Schema(description="投诉方类型枚举")
    private String complaintInitRoleType;
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    @Schema(description="创建时间[提出时间]")
    private Date proposeTime;
    @Schema(description="投诉方名称")
    private String complaintInitRoleName;
    @Schema(description="投诉方ID")
    private String complaintInitRoleId;
    @Schema(description="投诉内容")
    private String complaintContent;
    @Schema(description="被投诉方类型枚举")
    private String complaintAcceptRoleType;
    @Schema(description="被投诉方ID")
    private String complaintAcceptRoleId;
    @Schema(description="被投诉方名称")
    private String complaintAcceptRoleName;
    @Schema(description="凭证附件列表")
    private List<AttListDTO> attListDTOList;
    @Schema(description="罚款金额")
    private BigDecimal fineAmount;
    @Schema(description="处理结果详情")
    private String dealResult;
    @Schema(description="处理状态")
    private String dealStatus;
    @Schema(description="是否罚款【-1:未罚款 1:罚款】")
    private Integer fineFlag;
    @Schema(description="责任归属类型枚举")
    private String responsibleRoleType;
    @Schema(description="责任归属名称(责任方名称)")
    private String responsibleRoleName;
    @Schema(description="责任归属备注")
    private String responsibleContent;
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    @Schema(description="处理完结时间")
    private Date dealFinishTime;
    @Schema(description="处理完结人用户ID")
    private String dealFinishUserId;
    @Schema(description="评价得分")
    private Float evaluationScore;
    @Schema(description="处理完结人用户名")
    private String dealFinishUserName;
    @Schema(description="投诉跟进记录列表")
    private List<ComplaintFollowDTO> complaintFollowDTOList;
}

