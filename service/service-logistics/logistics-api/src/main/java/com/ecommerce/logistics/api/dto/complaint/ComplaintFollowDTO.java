
package com.ecommerce.logistics.api.dto.complaint;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;
import lombok.Data;

@Data
@Schema(name="投诉记跟进记录返回结果")
public class ComplaintFollowDTO {
    @Schema(description="操作内容")
    private String content;
    @Schema(description="操作用户Id")
    private String operationUserId;
    @Schema(description="操作用户名称")
    private String operationUserName;
    @Schema(description="操作时间")
    private Date operationTime;
}

