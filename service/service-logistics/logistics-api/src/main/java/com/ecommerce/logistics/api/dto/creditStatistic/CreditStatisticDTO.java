
package com.ecommerce.logistics.api.dto.creditStatistic;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="信用统计DTO")
public class CreditStatisticDTO {
    @Schema(description="信用统计ID")
    private String creditStatisticId;
    @Schema(description="承运人ID")
    private String personId;
    @Schema(description="承运人名称")
    private String personName;
    @Schema(description="承运人类型")
    private String personType;
    @Schema(description="接单次数")
    private Integer billCount;
    @Schema(description="违约次数")
    private Integer breakCount;
    @Schema(description="客诉次数")
    private Integer complaintCount;
}

