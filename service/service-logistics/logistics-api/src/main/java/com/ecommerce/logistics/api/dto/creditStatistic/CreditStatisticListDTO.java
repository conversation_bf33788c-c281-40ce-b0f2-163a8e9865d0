
package com.ecommerce.logistics.api.dto.creditStatistic;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(name="信用统计列表DTO")
public class CreditStatisticListDTO {
    @Schema(description="承运人名称")
    private String personName;
    @Schema(description="承运人类型")
    private String personType;
    @Schema(description="接单次数")
    private Integer billCount;
    @Schema(description="违约次数")
    private Integer breakCount;
    @Schema(description="违约率")
    private BigDecimal breakRate;
    @Schema(description="客诉次数")
    private Integer complaintCount;
    @Schema(description="客诉率")
    private BigDecimal complaintRate;
    @Schema(description="服务评分")
    private Byte aveScore;
    @Schema(description="综合信用")
    private BigDecimal comprehensiveCredit;
    @Schema(description="信用等级")
    private String creditGrade;
}

