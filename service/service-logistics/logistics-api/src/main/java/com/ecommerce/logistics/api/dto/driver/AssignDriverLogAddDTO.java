
package com.ecommerce.logistics.api.dto.driver;

import com.ecommerce.logistics.api.enums.VehicleLicencePlateColorEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

@Data
@Schema(name="指派司机记录新增实体")
public class AssignDriverLogAddDTO {
    @Schema(description="归属人")
    private String userId;
    @Schema(description="归属人名")
    private String userName;
    @Schema(description="归属类型")
    private Integer userType;
    @Schema(description="车辆ID", required=false)
    private String vehicleId;
    @Schema(description="车牌号码", required=false)
    private String number;
    @Schema(description="司机ID", required=false)
    private String driverId = "";
    @NotBlank(message="司机姓名")
    private String driverName;
    @NotBlank(message="司机联系电话")
    private String driverPhone;
    @Schema(description="车牌颜色", required=false)
    private String color = VehicleLicencePlateColorEnum.YELLOW.getCode();
    @Schema(description="运输品类")
    private List<String> transportCategoryIdList;
}

