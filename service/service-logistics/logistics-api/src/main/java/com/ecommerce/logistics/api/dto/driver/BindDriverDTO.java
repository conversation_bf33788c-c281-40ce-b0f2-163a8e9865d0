
package com.ecommerce.logistics.api.dto.driver;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="绑定司机DTO对象")
public class BindDriverDTO {
    @Schema(description="车辆ID", required=false)
    private String vehicleId;
    @Schema(description="司机ID", required=false)
    private String driverId;
    @Schema(description="司机姓名", required=false)
    private String driverName;
    @Schema(description="司机联系电话", required=false)
    private String driverPhone;
    @Schema(description="操作人ID", example="10010")
    private String operatorId;
    @Schema(description="操作人姓名", example="王大锤")
    private String operatorName;
}

