
package com.ecommerce.logistics.api.dto.driver;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

@Data
@Schema(name="绑定司机记录DTO对象")
public class BindDriverLogDTO {
    @NotBlank(message="车辆ID不能为空")
    @Schema(description="车辆ID", required=false)
    private String vehicleId;
    @NotBlank(message="司机ID不能为空")
    @Schema(description="司机ID", required=false)
    private String driverId = "";
    @NotBlank(message="司机姓名不能为空")
    @Schema(description="司机姓名", required=false)
    private String driverName;
    @NotBlank(message="司机联系电话不能为空")
    @Schema(description="司机联系电话", required=false)
    private String driverPhone;
    @Schema(description="车辆承运商名称", required=false)
    private String driverMemberName;
    @Schema(description="操作人ID", example="10010")
    private String operatorId;
    @Schema(description="操作人姓名", example="王大锤")
    private String operatorName;
}

