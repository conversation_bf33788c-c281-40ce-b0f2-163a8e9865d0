
package com.ecommerce.logistics.api.dto.driver;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(name="个体车辆实体")
public class PersonVehicleDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = 3204953170256505364L;
    @Schema(description="车牌号码")
    private String vehicleNumber;
    @Schema(description="车辆类型Id")
    private String vehicleTypeId;
    @Schema(description="车辆类型")
    private String vehicleTypeName;
    @Schema(description="最大装载量")
    private BigDecimal maxLoadingCapacity;
    @Schema(description="车牌颜色")
    private String vehicleColor;
    @Schema(description="运输品类ID")
    private String transportCategoryId;
    @Schema(description="运输品类名称（显示使用）")
    private String transportCategoryName;
    @Schema(description="能源类型id(见EnergyTypeEnum)")
    private String energyTypeId;
    @Schema(description="显示使用")
    private String energyTypeName;
}

