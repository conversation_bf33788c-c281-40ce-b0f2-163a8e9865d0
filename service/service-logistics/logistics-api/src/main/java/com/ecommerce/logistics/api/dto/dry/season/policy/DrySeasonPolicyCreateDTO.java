
package com.ecommerce.logistics.api.dto.dry.season.policy;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

@Data
@Schema(name="枯水期策略添加对象")
public class DrySeasonPolicyCreateDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = 5819534139427201884L;
    @Schema(description="策略名称")
    private String policyName;
    @Schema(description="归属人memberId", hidden=true)
    private String belongerId;
    @Schema(description="支持的船形（存枚举ShippingTypeEnum的值,所有船型传all）")
    private String supportShippingType;
    @Schema(description="创建用户")
    private String createUser;
    @Schema(description="备注说明")
    private String remark;
    @Schema(description="水位区间集合")
    private List<DrySeasonPolicyWaterLevelDTO> waterLevelList;
    @Schema(description="策略选中的列")
    private String selectedColumn;
    @Schema(description="有效时间的集合")
    private List<DrySeasonPolicyTimeDTO> timeList;
    @Schema(description="关联航线信息")
    private List<DrySeasonPolicyShippingRouteRelationDTO> shippingRouteList;
}

