
package com.ecommerce.logistics.api.dto.dry.season.policy;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Data;

@Data
public class DrySeasonPolicyDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = -8682881745031930715L;
    @Schema(description="枯水期价格策略ID")
    private String policyId;
    @Schema(description="策略类型")
    private String policyType;
    @Schema(description="价格策略编码")
    private String policyCode;
    @Schema(description="策略名称")
    private String policyName;
    @Schema(description="归属人memberId", hidden=true)
    private String belongerId;
    @Schema(description="支持的船形（存枚举ShippingTypeEnum的值,所有船型传all）")
    private String supportShippingType;
    @Schema(description="创建用户")
    private String createUser;
    @Schema(description="更新用户")
    private String updateUser;
    @Schema(description="创建时间")
    private Date createTime;
    @Schema(description="更新时间")
    private Date updateTime;
    @Schema(description="备注说明")
    private String remark;
    @Schema(description="水位区间集合")
    private List<DrySeasonPolicyWaterLevelDTO> waterLevelList;
    @Schema(description="有效时间的集合")
    private List<DrySeasonPolicyTimeDTO> timeList;
    @Schema(description="关联航线信息")
    private List<DrySeasonPolicyShippingRouteRelationDTO> shippingRouteList;
}

