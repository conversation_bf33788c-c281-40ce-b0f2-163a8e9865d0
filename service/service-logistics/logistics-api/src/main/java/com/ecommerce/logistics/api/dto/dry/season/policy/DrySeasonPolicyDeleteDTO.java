
package com.ecommerce.logistics.api.dto.dry.season.policy;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import lombok.Data;

@Data
@Schema(name="枯水期策略删除对象")
public class DrySeasonPolicyDeleteDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = 3522626502571060369L;
    @Schema(description="来自哪个web层应用")
    private String appName;
    @Schema(description="操作人id")
    private String operator;
    @Schema(description="操作人会员id")
    private String operatorMemberId;
    @Schema(description="枯水期策略表id")
    private String policyId;
}

