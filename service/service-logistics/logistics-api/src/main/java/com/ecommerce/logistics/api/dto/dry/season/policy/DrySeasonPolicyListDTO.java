
package com.ecommerce.logistics.api.dto.dry.season.policy;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class DrySeasonPolicyListDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = -7695265127771605927L;
    @Schema(description="枯水期价格策略ID")
    private String policyId;
    @Schema(description="策略名称")
    private String policyName;
    @Schema(description="支持的船形（仅显示）")
    private String supportShippingType;
    @Schema(description="支持的船形（仅显示）")
    private String supportShippingTypeName;
    @Schema(description="航线编号")
    private String shippingRouteNo;
    @Schema(description="装货码头名称")
    private String pickingWharfName;
    @Schema(description="收货码头名称")
    private String unloadingWharfName;
    @Schema(description="开始时间")
    private Date startTime;
    @Schema(description="失效时间(为空表示一直有效)")
    private Date endTime;
    @Schema(description="水位")
    private String waterLevel;
    @Schema(description="策略选中的列")
    private String selectedColumn;
    @Schema(description="创建时间")
    private Date createTime;
}

