
package com.ecommerce.logistics.api.dto.dry.season.policy;

import java.io.Serial;
import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class DrySeasonPolicyQueryDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = -3108425302784643831L;
    @Schema(description="归属会员id")
    private String memberId;
    @Schema(description="策略名称")
    private String policyName;
    @Schema(description="生效开始时间 yyyy-MM-dd HH:ss:mm")
    private String startTime;
    @Schema(description="生效截至时间 yyyy-MM-dd HH:ss:mm")
    private String endTime;
    @Schema(description="创建开始时间 yyyy-MM-dd HH:ss:mm")
    private String createStartTime;
    @Schema(description="创建截至时间 yyyy-MM-dd HH:ss:mm")
    private String createEndTime;
    @Schema(description="支持的船形")
    private String supportShippingType;
    @Schema(description="水位列代码")
    private String waterColumnCode;
    @Schema(description="策略选中的列")
    private String selectedColumn;
}

