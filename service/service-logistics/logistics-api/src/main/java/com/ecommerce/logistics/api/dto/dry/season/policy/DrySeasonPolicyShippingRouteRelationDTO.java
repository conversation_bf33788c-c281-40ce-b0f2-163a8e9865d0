
package com.ecommerce.logistics.api.dto.dry.season.policy;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

@Data
@Schema(name="枯水期策略有效时间段对象")
public class DrySeasonPolicyShippingRouteRelationDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = 3387757896926611049L;
    @Schema(description="关系表Id")
    private String relationId;
    @Schema(description="枯水期价格策略ID")
    private String policyId;
    @Schema(description="主键ID")
    private String shippingRouteId;
    @Schema(description="航线编号")
    private String shippingRouteNo;
    @Schema(description="装货码头名称（仅查询有）")
    private String pickingWharfName;
    @Schema(description="收货码头名称（仅查询有）")
    private String unloadingWharfName;
    @Schema(description="创建用户")
    private String createUser;
    @Schema(description="更新用户")
    private String updateUser;
    @Schema(description="创建时间")
    private Date createTime;
    @Schema(description="更新时间")
    private Date updateTime;
}

