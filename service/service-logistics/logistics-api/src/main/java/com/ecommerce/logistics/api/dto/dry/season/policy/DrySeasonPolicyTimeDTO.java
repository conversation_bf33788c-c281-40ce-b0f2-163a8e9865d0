
package com.ecommerce.logistics.api.dto.dry.season.policy;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

@Data
@Schema(name="枯水期策略有效时间段对象")
public class DrySeasonPolicyTimeDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = -5838701279938863993L;
    @Schema(description="关系表Id")
    private String relationId;
    @Schema(description="枯水期价格策略ID")
    private String policyId;
    @Schema(description="开始时间")
    private Date startTime;
    @Schema(description="失效时间(为空表示一直有效)")
    private Date endTime;
    @Schema(description="创建用户")
    private String createUser;
    @Schema(description="更新用户")
    private String updateUser;
    @Schema(description="创建时间")
    private Date createTime;
    @Schema(description="更新时间")
    private Date updateTime;
}

