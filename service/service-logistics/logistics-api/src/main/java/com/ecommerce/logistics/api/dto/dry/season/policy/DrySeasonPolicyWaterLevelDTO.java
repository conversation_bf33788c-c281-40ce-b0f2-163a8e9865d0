
package com.ecommerce.logistics.api.dto.dry.season.policy;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

@Data
public class DrySeasonPolicyWaterLevelDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = 677939585756101062L;
    @Schema(description="价格策略ID")
    private String waterLevelId;
    @Schema(description="归属人memberId")
    private String belongerId;
    @Schema(description="是否策略模板 0否 1是")
    private Boolean templeteFlg;
    @Schema(description="归属枯水期策略id")
    private String policyId;
    @Schema(description="起始值，B级核载吨位（吨）")
    private BigDecimal minTonnage;
    @Schema(description="截止值，B级核载吨位（吨）")
    private BigDecimal maxTonnage;
    @Schema(description="列集合(查询时返回)")
    private List<DrySeasonPolicyWaterLevelItemDTO> columnList;
    @Schema(description="策略选中的列-动态列信息列ID")
    private String selectedColumnItemId;
    @Schema(description="策略选中的列-字段标识代码")
    private String selectedColumnCode;
    @Schema(description="策略选中的列-字段名（水位）")
    private String selectedColumnName;
    @Schema(description="策略选中的列-字段值(上浮比例)")
    private BigDecimal selectedColumnVal;
    @Schema(description="策略选中的列")
    private String selectedColumn;
    @Schema(description="策略选中的列的值")
    private BigDecimal selectValue;
    @Schema(description="floatingRatio15~floatingRatio40为水位1.5~4米对应的上浮比例")
    private BigDecimal floatingRatio15;
    private BigDecimal floatingRatio16;
    private BigDecimal floatingRatio17;
    private BigDecimal floatingRatio18;
    private BigDecimal floatingRatio19;
    private BigDecimal floatingRatio20;
    private BigDecimal floatingRatio21;
    private BigDecimal floatingRatio22;
    private BigDecimal floatingRatio23;
    private BigDecimal floatingRatio24;
    private BigDecimal floatingRatio25;
    private BigDecimal floatingRatio26;
    private BigDecimal floatingRatio27;
    private BigDecimal floatingRatio28;
    private BigDecimal floatingRatio29;
    private BigDecimal floatingRatio30;
    private BigDecimal floatingRatio31;
    private BigDecimal floatingRatio32;
    private BigDecimal floatingRatio33;
    private BigDecimal floatingRatio34;
    private BigDecimal floatingRatio35;
    private BigDecimal floatingRatio36;
    private BigDecimal floatingRatio37;
    private BigDecimal floatingRatio38;
    private BigDecimal floatingRatio39;
    private BigDecimal floatingRatio40;
    @Schema(description="创建用户")
    private String createUser;
    @Schema(description="更新用户")
    private String updateUser;
    @Schema(description="创建时间")
    private Date createTime;
    @Schema(description="更新时间")
    private Date updateTime;
}

