
package com.ecommerce.logistics.api.dto.dry.season.policy;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class DrySeasonPolicyWaterLevelItemDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = 677939585756101062L;
    @Schema(description="枯水期策略-水位价格模板表-动态列信息列ID")
    private String itemId;
    @Schema(description="枯水期策略-水位价格表ID")
    private String waterLevelId;
    @Schema(description="字段标识代码")
    private String code;
    @Schema(description="字段名（水位）")
    private String name;
    @Schema(description="字段值(上浮比例)")
    private BigDecimal val;
    @Schema(description="排序权重")
    private Integer weight;
}

