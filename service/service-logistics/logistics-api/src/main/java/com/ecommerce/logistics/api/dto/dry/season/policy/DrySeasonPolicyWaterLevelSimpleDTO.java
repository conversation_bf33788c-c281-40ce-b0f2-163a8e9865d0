package com.ecommerce.logistics.api.dto.dry.season.policy;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class DrySeasonPolicyWaterLevelSimpleDTO implements Serializable {

    private static final long serialVersionUID = 2583816440679153411L;
    /**
     * 归属枯水期策略id
     */
    @ApiModelProperty("归属枯水期策略id")
    private String policyId;

    /**
     * 起始值，B级核载吨位（吨）
     */
    @ApiModelProperty("起始值，B级核载吨位（吨）")
    private BigDecimal minTonnage;

    /**
     * 截止值，B级核载吨位（吨）
     */
    @ApiModelProperty("截止值，B级核载吨位（吨）")
    private BigDecimal maxTonnage;

    /**
     * 策略选中的列
     */
    @ApiModelProperty("策略选中的列")
    private String selectedColumn;

    @ApiModelProperty("策略选中的列的值")
    private BigDecimal selectValue;

}
