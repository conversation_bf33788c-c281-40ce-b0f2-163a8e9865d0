
package com.ecommerce.logistics.api.dto.dry.season.policy;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

@Data
public class DrySeasonPolicyWaterLevelTemplateDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = 677939585756101062L;
    @Schema(description="水位区间集合")
    private List<DrySeasonPolicyWaterLevelDTO> waterLevelList;
}

