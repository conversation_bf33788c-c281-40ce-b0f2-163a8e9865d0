
package com.ecommerce.logistics.api.dto.dry.season.policy;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

@Data
public class DrySeasonPolicyWaterLevelTemplateEditDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = 7829507165027825541L;
    @Schema(description="水位区间集合")
    private List<DrySeasonPolicyWaterLevelDTO> waterLevelList;
    @Schema(description="操作人", hidden=true)
    private String operator;
    @Schema(description="归属会员id")
    private String memberId;
}

