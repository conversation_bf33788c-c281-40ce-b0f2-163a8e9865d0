
package com.ecommerce.logistics.api.dto.dry.season.policy;

import java.io.Serial;
import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class PolicyShippingRouteRelationRemoveDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = 6673884117022974854L;
    @Schema(description="来自哪个web层应用")
    private String appName;
    @Schema(description="操作人id")
    private String operator;
    @Schema(description="操作人会员id")
    private String operatorMemberId;
    @Schema(description="枯水期策略表id")
    private String policyId;
    @Schema(description="航运线路id")
    private String shippingRouteId;
}

