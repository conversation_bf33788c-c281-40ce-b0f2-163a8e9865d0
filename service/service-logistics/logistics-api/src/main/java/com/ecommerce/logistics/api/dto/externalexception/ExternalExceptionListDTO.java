
package com.ecommerce.logistics.api.dto.externalexception;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;
import lombok.Data;

@Data
@Schema(name="外部异常处理列表对象")
public class ExternalExceptionListDTO {
    @Schema(description="异常处理Id", required=true, example="exceptionHandleId")
    private String exceptionHandleId;
    @Schema(description="关联运单ID", required=true, example="waybill_id")
    private String waybillId;
    @Schema(description="关联运单子项Id")
    private String waybillItemId;
    @Schema(description="运输工具类型")
    private String transportToolType;
    @Schema(description="关联运单号", required=true, example="waybillNum")
    private String waybillNum;
    @Schema(description="方法类型", required=true, example="methodType")
    private String methodType;
    @Schema(description="异常状态", required=true, example="methodType")
    private String status;
    @Schema(description="异常原因", required=true, example="exception_reason")
    private String exceptionReason;
    @Schema(description="处理时间", required=true, example="createTime")
    private Date createTime;
}

