
package com.ecommerce.logistics.api.dto.externalexception;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="外部异常处理查询对象")
public class ExternalExceptionQueryDTO {
    @Schema(description="关联运单号", required=true, example="waybillNum")
    private String waybillNum;
    @Schema(description="方法类型", required=true, example="methodType")
    private String methodType;
    @Schema(description="异常状态", required=true, example="status")
    private String status;
    @Schema(description="创建时间起始", required=true, example="createTime")
    private String createTimeStart;
    @Schema(description="创建时间截止", required=true, example="createTime")
    private String createTimeEnd;
}

