
package com.ecommerce.logistics.api.dto.externalexception;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="外部异常处理重试对象")
public class ExternalExceptionRetryDTO {
    @Schema(description="外部异常处理ID")
    private String externalExceptionHandleId;
    @Schema(description="操作人Id", required=true, example="xxx")
    private String operatorUserId;
    @Schema(description="操作人姓名", required=true, example="王大锤")
    private String operatorUserName;
}

