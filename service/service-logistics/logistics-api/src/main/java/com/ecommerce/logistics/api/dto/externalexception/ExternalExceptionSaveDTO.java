
package com.ecommerce.logistics.api.dto.externalexception;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="外部异常处理保存对象")
public class ExternalExceptionSaveDTO {
    @Schema(description="异常处理Id", required=true, example="exceptionHandleId")
    private String exceptionHandleId;
    @Schema(description="关联运单ID", required=true, example="waybill_id")
    private String waybillId;
    @Schema(description="关联运单号", required=true, example="waybillNum")
    private String waybillNum;
    @Schema(description="方法类型", required=true, example="methodType")
    private String methodType;
    @Schema(description="异常状态", required=true, example="methodType")
    private String status;
    @Schema(description="异常原因", required=true, example="exception_reason")
    private String exceptionReason;
    @Schema(description="操作人", required=true, example="operatorUserId")
    private String operatorUserId;
    @Schema(description="操作人名称", required=true, example="operatorUserName")
    private String operatorUserName;
}

