
package com.ecommerce.logistics.api.dto.gps;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

@Data
@Schema(name="GPS厂商新增实体")
public class GpsManufacturerAddDTO {
    @Schema(name="厂商名称", required=true)
    @NotBlank(message="厂商名称不能为空")
    private String gpsName;
    @Schema(description="厂商URL")
    private String url;
    @Schema(description="GPS协议类型")
    private String gpsProtocolType;
    @Schema(description="GPS用户名")
    private String gpsUserName;
    @Schema(description="GPS密码")
    private String gpsPassword;
    @Schema(description="创建人ID")
    private String createUser;
}

