
package com.ecommerce.logistics.api.dto.gps;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;
import lombok.Data;

@Data
@Schema(name="GPS厂商实体")
public class GpsManufacturerDTO {
    @Schema(description="主键")
    private String gpsManufacturerId;
    @Schema(description="厂商名称")
    private String gpsName;
    @Schema(description="厂商标识")
    private String number;
    @Schema(description="GPS协议类型")
    private String gpsProtocolType;
    @Schema(description="GPS用户名")
    private String gpsUserName;
    @Schema(description="GPS密码")
    private String gpsPassword;
    @Schema(description="厂商url")
    private String url;
    @Schema(description="创建人ID")
    private String createUser;
    @Schema(description="更新人ID")
    private String updateUser;
    @Schema(description="创建时间")
    private Date createTime;
    @Schema(description="修改时间")
    private Date updateTime;
}

