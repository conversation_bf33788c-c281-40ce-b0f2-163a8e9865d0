
package com.ecommerce.logistics.api.dto.gps;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

@Data
@Schema(name="GPS厂商删除实体")
public class GpsManufacturerDeleteDTO {
    @Schema(description="GPS厂商主键", required=true)
    @NotBlank(message="GPS厂商主键不能为空")
    private String gpsManufacturerId;
    @Schema(description="更新人ID")
    private String updateUser;
}

