
package com.ecommerce.logistics.api.dto.gps;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

@Data
@Schema(name="GPS厂商修改实体")
public class GpsManufacturerUpdateDTO {
    @Schema(name="GPS厂商主键", required=true)
    @NotBlank(message="GPS厂商主键不能为空")
    private String gpsManufacturerId;
    @Schema(name="厂商名称")
    private String gpsName;
    @Schema(description="GPS协议类型")
    private String gpsProtocolType;
    @Schema(description="GPS用户名")
    private String gpsUserName;
    @Schema(description="GPS密码")
    private String gpsPassword;
    @Schema(name="厂商URL")
    private String url;
    @Schema(name="更新人ID")
    private String updateUser;
}

