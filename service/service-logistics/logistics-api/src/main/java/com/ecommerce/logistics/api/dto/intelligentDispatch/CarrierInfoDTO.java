
package com.ecommerce.logistics.api.dto.intelligentDispatch;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(name="承运商信息DTO")
public class CarrierInfoDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    @Schema(description="承运商会员ID")
    private String carrierId;
    @Schema(description="承运商名称")
    private String carrierName;
    @Schema(description="承运商类型")
    private String carrierType;
    @Schema(description="单位运价", required=false, example="10.00")
    private BigDecimal unitPrice;
}

