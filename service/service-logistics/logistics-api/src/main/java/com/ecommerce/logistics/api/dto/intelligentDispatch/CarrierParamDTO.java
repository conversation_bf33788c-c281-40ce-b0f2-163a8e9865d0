
package com.ecommerce.logistics.api.dto.intelligentDispatch;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Schema(name="智能调度入参DTO")
public class CarrierParamDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    @Schema(description="服务方ID")
    private String consignorId;
    @Schema(description="服务方用户类型")
    private Integer consignorUserType;
    @Schema(description="运输品类ID", required=true)
    @NotBlank(message="运输品类ID不能为空")
    private String transportCategoryId;
    @Schema(description="收货地址省编码", required=true)
    private String provinceCode;
    @Schema(description="收货地址市编码", required=true)
    private String cityCode;
    @Schema(description="收货地址区编码", required=true)
    private String districtCode;
    @Schema(description="收货地址街道编码", required=true)
    private String streetCode;
    @Schema(description="经度", required=true)
    private BigDecimal longitude;
    @Schema(description="纬度", required=true)
    private BigDecimal latitude;
    @Schema(description="预估里程（公里）", required=true)
    private BigDecimal estimateKm;
    @Schema(description="收货地址ID", required=true, example="13niw824264inaxb2cxwu9a")
    private String receiveAddressId;
    @Schema(description="提货点ID", required=true, example="8a2c3cc75efdeqw727015affb0aad5ad")
    private String warehouseId;
    @Schema(description="收货地址位置", required=true, example="13niw824264inaxb2cxwu9a")
    private String receiveAddressLocation;
    @Schema(description="提货点位置坐标", required=true)
    private String warehouseLocation;
    @Schema(description="指派数量", required=true, example="200")
    @NotNull(message="指派数量不能为空")
    private @NotNull(message="指派数量不能为空") BigDecimal quantity;
    @Schema(description="委托单ID")
    private String deliveryBillId;
    @Schema(description="期望配送时间")
    private String deliveryTime;
}

