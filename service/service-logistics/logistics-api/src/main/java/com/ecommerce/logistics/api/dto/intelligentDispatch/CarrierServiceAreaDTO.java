
package com.ecommerce.logistics.api.dto.intelligentDispatch;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;
import lombok.Data;

@Data
@Schema(name="承运商服务范围DTO")
public class CarrierServiceAreaDTO {
    @Schema(description="承运商服务范围ID")
    private String serviceAreaId;
    @Schema(description="承运商会员ID")
    private String carrierId;
    @Schema(description="承运商会员代码")
    private String carrierCode;
    @Schema(description="承运商名称")
    private String carrierName;
    @Schema(description="服务方ID")
    private String consignorId;
    @Schema(description="服务方代码")
    private String consignorCode;
    @Schema(description="服务方名称")
    private String consignorName;
    @Schema(description="服务方用户类型")
    private Integer consignorUserType;
    @Schema(description="运输品类ID")
    private String transportCategoryId;
    @Schema(description="承运商服务范围映射DTO列表")
    private List<CarrierServiceAreaMapDTO> mapDTOList;
    @Schema(description="服务范围-前端展示")
    private String serviceArea;
    @Schema(description="操作人账号ID")
    private String operatorUserId;
    @Schema(description="操作人用户类型")
    private Integer operatorUserType;
    @Schema(description="操作人账号对应的会员ID")
    private String operatorMemberId;
}

