
package com.ecommerce.logistics.api.dto.intelligentDispatch;

import com.ecommerce.logistics.api.dto.warehouse.AddressOptionsDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;
import lombok.Data;

@Data
@Schema(name="承运商服务范围映射DTO")
public class CarrierServiceAreaMapDTO {
    @Schema(description="承运商服务范围映射ID")
    private String serviceAreaMapId;
    @Schema(description="承运商服务范围ID")
    private String serviceAreaId;
    @Schema(description="省名称")
    private String provinceName;
    @Schema(description="省编码")
    private String provinceCode;
    @Schema(description="市名称")
    private String cityName;
    @Schema(description="市编码")
    private String cityCode;
    @Schema(description="区名称")
    private String districtName;
    @Schema(description="区编码")
    private String districtCode;
    @Schema(description="街道名称")
    private String streetName;
    @Schema(description="街道编码")
    private String streetCode;
    @Schema(description="服务范围-拼接")
    private String serviceArea;
    @Schema(description="省下拉选项-前端编辑需要")
    private List<AddressOptionsDTO> provinceOptions;
    @Schema(description="市下拉选项-前端编辑需要")
    private List<AddressOptionsDTO> cityOptions;
    @Schema(description="区下拉选项-前端编辑需要")
    private List<AddressOptionsDTO> districtOptions;
    @Schema(description="街道下拉选项-前端编辑需要")
    private List<AddressOptionsDTO> streetOptions;
}

