
package com.ecommerce.logistics.api.dto.intelligentDispatch;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

@Data
@Schema(name="智能调度规则状态修改对象")
public class ChangeRuleStatusDTO {
    @NotBlank(message="规则映射Id不能为空")
    @Schema(description="规则映射Id")
    private String ruleMapId;
    @Schema(description="新状态")
    private Integer newStatus;
    @Schema(description="操作人账号Id")
    private String operationUserId;
}

