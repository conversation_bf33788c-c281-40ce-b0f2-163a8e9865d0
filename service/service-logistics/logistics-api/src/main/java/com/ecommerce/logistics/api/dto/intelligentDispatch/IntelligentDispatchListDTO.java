
package com.ecommerce.logistics.api.dto.intelligentDispatch;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="智能调度列表对象")
public class IntelligentDispatchListDTO {
    @Schema(description="规则Id")
    private String ruleId;
    @Schema(description="规则映射Id")
    private String ruleMapId;
    @Schema(description="规则适用对象")
    private Integer applicableObject;
    @Schema(description="规则类型")
    private Integer type;
    @Schema(description="规则代码")
    private String code;
    @Schema(description="规则内容")
    private String content;
    @Schema(description="适用范围")
    private Integer range;
    @Schema(description="规则算法")
    private String ruleAlgorithm;
    @Schema(description="状态")
    private Integer status;
    @Schema(description="优先级")
    private Integer seqNum;
}

