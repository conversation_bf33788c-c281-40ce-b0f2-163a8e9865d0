
package com.ecommerce.logistics.api.dto.intelligentDispatch;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;
import lombok.Data;

@Data
@Schema(name="智能调度列表查询对象")
public class IntelligentDispatchQueryDTO {
    @Schema(description="规则代码")
    private String ruleCode;
    @Schema(description="规则类型")
    private Integer ruleType;
    @Schema(description="规则适用对象")
    private Integer applicableObject;
    @Schema(description="适用范围")
    private List<Integer> applicableRange;
    @Schema(description="状态")
    private List<Integer> status;
    @Schema(description="会员Id")
    private String userId;
    @Schema(description="会员名称")
    private String userName;
    @Schema(description="会员类型")
    private Integer userType;
    @Schema(description="操作人账号Id")
    private String operationUserId;
}

