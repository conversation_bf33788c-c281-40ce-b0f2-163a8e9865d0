
package com.ecommerce.logistics.api.dto.intelligentDispatch;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import lombok.Data;

@Data
@Schema(name="返回智能调度规则集合DTO")
public class IntelligentDispatchRuleDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    @Schema(description="规则代码")
    private String ruleCode;
    @Schema(description="规则类型")
    private Integer ruleType;
    @Schema(description="规则适用对象")
    private Integer applicableObject;
    @Schema(description="适用范围")
    private Integer applicableRange;
    @Schema(description="适用范围")
    private String content;
    @Schema(description="规则算法")
    private String ruleAlgorithm;
    @Schema(description="优先级1开始，值越小优先级越高")
    private Integer seqNum;
    @Schema(description="归属用户ID")
    private String userId;
    @Schema(description="归属用户名称")
    private String userName;
    @Schema(description="归属用户类型(UserRoleEnum)")
    private String userType;
    @Schema(description="状态,0-未启用,1-启用,2-暂不支持")
    private Integer status;
}

