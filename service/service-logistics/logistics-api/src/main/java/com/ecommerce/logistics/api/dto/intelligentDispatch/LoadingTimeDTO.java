
package com.ecommerce.logistics.api.dto.intelligentDispatch;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(name="装货时间DTO")
public class LoadingTimeDTO {
    @Schema(description="装货时间ID")
    private String loadingTimeId;
    @Schema(description="装货点ID（仓库）")
    private String loadingId;
    @Schema(description="装货点名称（仓库）")
    private String loadingName;
    @Schema(description="卖家ID")
    private String sellerId;
    @Schema(description="卖家名称")
    private String sellerName;
    @Schema(description="归属用户ID")
    private String userId;
    @Schema(description="归属用户名称")
    private String userName;
    @Schema(description="归属用户类型")
    private Integer userType;
    @Schema(description="装货时间（分钟）")
    private Integer loadingTime;
    @Schema(description="装货里程（公里）")
    private BigDecimal loadingKm;
    @Schema(description="装货等待时间（分钟）")
    private Integer loadingWaitingTime;
    @Schema(description="装货等待里程（公里）")
    private BigDecimal loadingWaitingKm;
    @Schema(description="操作人账号ID")
    private String operatorUserId;
    @Schema(description="操作人账号对应的会员ID")
    private String operatorMemberId;
}

