
package com.ecommerce.logistics.api.dto.intelligentDispatch;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="搬运人员列表DTO")
public class PorterListDTO {
    @Schema(description="主键ID")
    private String porterId;
    @Schema(description="省名称")
    private String provinceName;
    @Schema(description="省编码")
    private String provinceCode;
    @Schema(description="市名称")
    private String cityName;
    @Schema(description="市编码")
    private String cityCode;
    @Schema(description="区名称")
    private String districtName;
    @Schema(description="区编码")
    private String districtCode;
    @Schema(description="姓名")
    private String porterName;
    @Schema(description="联系方式")
    private String porterPhone;
    @Schema(description="年龄")
    private String age;
    @Schema(description="性别,0-男,1-女")
    private Integer sex;
    @Schema(description="活动区域说明")
    private String content;
    @Schema(description="归属会员Id")
    private String userId;
    @Schema(description="是否可以删除或者编辑")
    private Integer flag = 0;
}

