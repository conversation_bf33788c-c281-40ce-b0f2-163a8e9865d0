
package com.ecommerce.logistics.api.dto.intelligentDispatch;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="搬运人员列表查询DTO")
public class PorterQueryDTO {
    @Schema(description="省编码")
    private String provinceCode;
    @Schema(description="市编码")
    private String cityCode;
    @Schema(description="区编码")
    private String districtCode;
    @Schema(description="活动区域说明")
    private String content;
    @Schema(description="操作人会员Id")
    private String operationMemberId;
}

