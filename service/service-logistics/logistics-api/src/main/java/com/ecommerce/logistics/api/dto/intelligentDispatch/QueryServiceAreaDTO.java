
package com.ecommerce.logistics.api.dto.intelligentDispatch;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

@Data
@Schema(name="查询承运商服务范围DTO")
public class QueryServiceAreaDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    @Schema(description="服务方ID")
    private String consignorId;
    @Schema(description="服务方用户类型")
    private Integer consignorUserType;
    @Schema(description="运输品类ID")
    private String transportCategoryId;
    @Schema(description="省编码")
    private String provinceCode;
    @Schema(description="市编码")
    private String cityCode;
    @Schema(description="区编码")
    private String districtCode;
    @Schema(description="街道编码")
    private String streetCode;
    @Schema(description="承运商会员ID集合")
    private List<String> carrierIdList;
}

