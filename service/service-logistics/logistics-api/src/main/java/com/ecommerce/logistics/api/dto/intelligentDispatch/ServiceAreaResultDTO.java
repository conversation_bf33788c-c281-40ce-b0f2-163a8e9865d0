
package com.ecommerce.logistics.api.dto.intelligentDispatch;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import lombok.Data;

@Data
@Schema(name="承运商服务范围返回结果DTO")
public class ServiceAreaResultDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    @Schema(description="承运商服务范围ID")
    private String serviceAreaId;
    @Schema(description="承运商会员ID")
    private String carrierId;
    @Schema(description="承运商会员代码")
    private String carrierCode;
    @Schema(description="承运商名称")
    private String carrierName;
    @Schema(description="服务方ID")
    private String consignorId;
    @Schema(description="服务方代码")
    private String consignorCode;
    @Schema(description="服务方名称")
    private String consignorName;
    @Schema(description="服务方用户类型")
    private Integer consignorUserType;
    @Schema(description="运输品类ID")
    private String transportCategoryId;
    @Schema(description="省名称")
    private String provinceName;
    @Schema(description="省编码")
    private String provinceCode;
    @Schema(description="市名称")
    private String cityName;
    @Schema(description="市编码")
    private String cityCode;
    @Schema(description="区名称")
    private String districtName;
    @Schema(description="区编码")
    private String districtCode;
    @Schema(description="街道名称")
    private String streetName;
    @Schema(description="街道编码")
    private String streetCode;
    @Schema(description="服务范围-前端展示")
    private String serviceArea;
    @Schema(description="操作人账号ID")
    private String operatorUserId;
    @Schema(description="操作人用户类型")
    private Integer operatorUserType;
    @Schema(description="操作人账号对应的会员ID")
    private String operatorMemberId;
}

