
package com.ecommerce.logistics.api.dto.intelligentDispatch;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(name="卸货时间DTO")
public class UnloadingTimeDTO {
    @Schema(description="卸货时间ID")
    private String unloadingTimeId;
    @Schema(description="卸货点ID（收货地址）")
    private String unloadingId;
    @Schema(description="卸货点别名（收货地址）")
    private String unloadingAlias;
    @Schema(description="卸货点详细地址（收货地址）")
    private String unloadingAddress;
    @Schema(description="卸货点其他信息（收货地址）")
    private String unloadingAddressDetail;
    @Schema(description="客户ID")
    private String customerId;
    @Schema(description="客户名称")
    private String customerName;
    @Schema(description="归属用户ID")
    private String userId;
    @Schema(description="归属用户名称")
    private String userName;
    @Schema(description="归属用户类型")
    private Integer userType;
    @Schema(description="卸货时间（分钟）")
    private Integer unloadingTime;
    @Schema(description="卸货里程（公里）")
    private BigDecimal unloadingKm;
    @Schema(description="卸货等待时间（分钟）")
    private Integer unloadingWaitingTime;
    @Schema(description="卸货等待里程（公里）")
    private BigDecimal unloadingWaitingKm;
    @Schema(description="操作人账号ID")
    private String operatorUserId;
    @Schema(description="操作人账号对应的会员ID")
    private String operatorMemberId;
}

