
package com.ecommerce.logistics.api.dto.intelligentDispatch;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import lombok.Data;

@Data
@Schema(name="车辆信息DTO")
public class VehicleInfoDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    @Schema(description="车辆ID")
    private String vehicleId;
    @Schema(description="车牌号")
    private String number;
    @Schema(description="车辆类型ID")
    private String vehicleTypeId;
    @Schema(description="绑定司机id")
    private String bindDriverId;
    @Schema(description="司机信息")
    private String driverName;
    @Schema(description="司机手机")
    private String driverPhone;
    @Schema(description="车辆状态(VehicleStatusEnum枚举)")
    private String status;
    @Schema(description="运输品类ID")
    private String transportCategoryId;
}

