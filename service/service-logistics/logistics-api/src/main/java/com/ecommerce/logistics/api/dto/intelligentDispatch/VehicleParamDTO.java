
package com.ecommerce.logistics.api.dto.intelligentDispatch;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

@Data
@Schema(name="智能调度车辆入参DTO")
public class VehicleParamDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    @Schema(description="会员ID", required=true, example="4cxf33j23q00jewipxvfv2xw")
    @NotBlank(message="会员ID不能为空")
    String memberId;
    @Schema(description="车辆类型ID", required=true, example="4cxf33j23q00jewipxvfv2xw")
    private String vehicleTypeId;
    @Schema(description="运输品类ID", required=true, example="4cxf33j23q00jewipxvfv2xw")
    @NotBlank(message="运输品类ID不能为空")
    private String transportCategoryId;
}

