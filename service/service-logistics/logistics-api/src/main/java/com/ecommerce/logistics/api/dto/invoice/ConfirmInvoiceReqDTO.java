
package com.ecommerce.logistics.api.dto.invoice;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;
import lombok.Data;

@Data
@Schema(name="确认开票请求实体")
public class ConfirmInvoiceReqDTO {
    @Schema(description="开票方用户ID")
    private String buyerId;
    @Schema(description="开票方用户名")
    private String buyerName;
    @Schema(description="开票方平台账户ID")
    private String sellerId;
    @Schema(description="开票方平台账户名")
    private String sellName;
    @Schema(description="发票抬头")
    private String invoiceTitle;
    @Schema(description="开票方类型 01:企业 02：机关事业单位 03:个人 04:其他")
    private String buyerType;
    @Schema(description="开票方税号")
    private String buyerTaxNo;
    @Schema(description="开票方手机号")
    private String buyerMobile;
    @Schema(description="开票方电话")
    private String buyerTelephone;
    @Schema(description="开票方邮箱")
    private String buyerEmail;
    @Schema(description="开票方地址")
    private String buyerAddress;
    @Schema(description="开票方省份")
    private String buyerProvince;
    @Schema(description="开票方开户行名称")
    private String buyerBankName;
    @Schema(description="开票方开户行账号")
    private String buyerBankNum;
    @Schema(description="分机号")
    private String machineNo;
    @Schema(description="创建人AccountId")
    private String createUser;
    @Schema(description="是否专票")
    private Byte isSpecial = 0;
    @Schema(description="选择的未开票运单IDs")
    private List<String> waybillIds;
}

