
package com.ecommerce.logistics.api.dto.invoice;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;
import lombok.Data;

@Data
@Schema(name="开票对应单据查询条件实体")
public class InvoiceBillCondDTO {
    @Schema(description="承运商ID")
    private String carrierId;
    @Schema(description="运单号")
    private String waybillNum;
    @Schema(description="开票对象类型")
    private String invoiceTargetType;
    @Schema(description="开票对象ID")
    private String invoiceUserId;
    @Schema(description="单据数据权限对应的销售区域编码")
    private List<String> regionCodeList;
}

