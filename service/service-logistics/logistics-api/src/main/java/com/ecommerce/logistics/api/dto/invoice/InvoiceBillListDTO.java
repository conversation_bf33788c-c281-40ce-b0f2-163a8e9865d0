
package com.ecommerce.logistics.api.dto.invoice;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

@Data
@Schema(name="开票单据列表实体")
public class InvoiceBillListDTO {
    @Schema(description="运单号")
    private String waybillId;
    @Schema(description="运单号")
    private String waybillNum;
    @Schema(description="承运商ID")
    private String carrierId;
    @Schema(description="承运商名称")
    private String carrierName;
    @Schema(description="单据开票申请状态")
    private String invoiceStatus;
    @Schema(description="运单创建时间")
    private Date createTime;
    @Schema(description="运单完成时间")
    private Date completeTime;
    @Schema(description="实际物流费")
    private BigDecimal actualCarriage;
}

