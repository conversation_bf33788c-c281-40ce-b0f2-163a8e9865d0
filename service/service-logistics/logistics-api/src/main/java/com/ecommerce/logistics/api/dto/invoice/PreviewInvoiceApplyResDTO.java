
package com.ecommerce.logistics.api.dto.invoice;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;
import lombok.Data;

@Data
@Schema(name="物流开票申请预览结果实体")
public class PreviewInvoiceApplyResDTO {
    @Schema(description="选择开票的运单数")
    private Integer waybillCount = 0;
    @Schema(description="选择开票的商品数")
    private Integer goodsCount = 0;
    @Schema(description="申请开票结算详情")
    private List<PreviewInvoiceItemDTO> list;
}

