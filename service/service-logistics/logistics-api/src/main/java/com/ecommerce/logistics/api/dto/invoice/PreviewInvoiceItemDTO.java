
package com.ecommerce.logistics.api.dto.invoice;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(name="申请开票结算详情项实体")
public class PreviewInvoiceItemDTO {
    @Schema(description="服务提供方ID")
    private String sellerId;
    @Schema(description="服务提供方名称")
    private String sellerName;
    @Schema(description="服务提供方简称")
    private String sellerNikeName;
    @Schema(description="开票类型")
    private String invoiceType;
    @Schema(description="开票金额")
    private BigDecimal invoiceAmount;
    @Schema(description="服务提供方税号")
    private String sellerTaxNo;
}

