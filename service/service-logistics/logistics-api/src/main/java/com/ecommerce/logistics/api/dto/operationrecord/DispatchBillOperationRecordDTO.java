
package com.ecommerce.logistics.api.dto.operationrecord;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import lombok.Data;

@Data
@Schema(name="调度单操作记录DTO")
public class DispatchBillOperationRecordDTO
implements Serializable {
    @Schema(description="动作(做了什么)")
    private String operation;
    @Schema(description="操作人")
    private String operationName;
    @Schema(description="创建时间(操作时间)")
    private String createTime;
}

