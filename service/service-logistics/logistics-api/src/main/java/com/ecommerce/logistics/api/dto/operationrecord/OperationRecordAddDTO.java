
package com.ecommerce.logistics.api.dto.operationrecord;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;
import lombok.Data;

@Data
@Schema(name="操作记录添加实体")
public class OperationRecordAddDTO {
    @Schema(description="业务实体ID")
    private String entryId;
    @Schema(description="业务类型 1:运单 2：提货单 3：调度单")
    private Integer type;
    @Schema(description="操作类型")
    private Integer operationType;
    @Schema(description="操作人ID")
    private String operatorId;
    @Schema(description="操作人姓名")
    private String operatorName;
    @Schema(description="内容")
    private String content;
    @Schema(description="创建时间")
    private Date createTime;
}

