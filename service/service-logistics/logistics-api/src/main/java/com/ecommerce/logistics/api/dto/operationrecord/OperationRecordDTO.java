
package com.ecommerce.logistics.api.dto.operationrecord;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;
import lombok.Data;

@Data
@Schema(name="操作记录列表对象")
public class OperationRecordDTO {
    @Schema(description="状态描述")
    private String statusDesc;
    @Schema(description="操作类型")
    private Integer operationType;
    @Schema(description="操作内容")
    private String content;
    @Schema(description="操作用户Id")
    private String operationUserId;
    @Schema(description="操作用户名称")
    private String operationUserName;
    @Schema(description="操作时间")
    private Date operationTime;
    @Schema(description="记录时间")
    private Date createTime;
}

