
package com.ecommerce.logistics.api.dto.operationrecord.shipping;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;
import lombok.Data;

@Data
@Schema(name="操作记录添加实体")
public class ShippingOperationLogAddDTO {
    @Schema(description="业务实体ID")
    private String entryId;
    @Schema(description="业务类型 1:航线运费，2:船舶")
    private Integer businessType;
    @Schema(description="操作类型")
    private String operationType;
    @Schema(description="操作人ID")
    private String operatorId;
    @Schema(description="操作人姓名")
    private String operatorName;
    @Schema(description="内容")
    private String content;
    @Schema(description="创建时间")
    private Date createTime;
}

