
package com.ecommerce.logistics.api.dto.operationrecord.shipping;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;
import lombok.Data;

@Data
@Schema(name="航运操作记录列表对象")
public class ShippingOperationLogDTO {
    @Schema(description="操作记录ID")
    private String operationLogId;
    @Schema(description="实体ID")
    private String entryId;
    @Schema(description="业务类型")
    private Integer businessType;
    @Schema(description="操作类型")
    private String operationType;
    @Schema(description="操作内容")
    private String content;
    @Schema(description="操作用户Id")
    private String operatorId;
    @Schema(description="操作用户名称")
    private String operatorName;
    @Schema(description="操作时间")
    private Date operationTime;
}

