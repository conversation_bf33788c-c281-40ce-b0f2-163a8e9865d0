
package com.ecommerce.logistics.api.dto.operationrecord.shipping;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="操作记录列表对象")
public class ShippingOperationLogListDTO {
    @Schema(description="操作人ID")
    private String operatorId;
    @Schema(description="操作人姓名")
    private String operatorName;
    @Schema(description="操作内容")
    private String content;
    @Schema(description="操作时间")
    private String operationTime;
}

