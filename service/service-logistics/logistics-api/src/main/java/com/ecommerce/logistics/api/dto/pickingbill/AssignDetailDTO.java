
package com.ecommerce.logistics.api.dto.pickingbill;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@Schema(name="指派调度单详情对象")
public class AssignDetailDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = 773006757747717850L;
    @Schema(description="承运商ID", required=true, example="4cxf33j23q00jewipxvfv2xw")
    @NotBlank(message="指派承运商ID不能为空")
    String carrierId;
    @Schema(description="承运商名称", required=true, example="深圳嘉禾物流运输有限公司")
    @NotBlank(message="指派承运商名称不能为空")
    String carrierName;
    @Schema(description="承运商类型", required=true, example="030010100")
    @NotNull(message="指派承运商类型不能为空")
    private @NotNull(message="指派承运商类型不能为空") String carrierType = "";
    @Schema(description="承运人角色类型")
    private String carrierRoleType;
    @Schema(description="指派数量", required=true, example="200")
    @NotNull(message="指派数量不能为空")
    private @NotNull(message="指派数量不能为空") BigDecimal quantity;
    @Schema(description="委托单ID")
    private String deliveryBillId;
    @Schema(description="仓库ID,基地发货修改仓库时传入")
    private String warehouseId;
}

