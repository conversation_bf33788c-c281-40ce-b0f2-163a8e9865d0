
package com.ecommerce.logistics.api.dto.pickingbill;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@Schema(name="指派调度单对象")
public class AssignDispatchBillDTO
implements Serializable {
    @Schema(description="提货单ID", required=true, example="4cxf33j23q00jpripxvfv2xw")
    @NotBlank(message="提货单ID不能为空")
    private String PickingBillId;
    @Schema(description="分配总数量", required=true, example="200")
    @NotNull(message="分配总数量不能为空")
    private @NotNull(message="分配总数量不能为空") BigDecimal totalQuantity;
    @Schema(description="提货时间", required=true, example="2018-10-12 13:10:08")
    @NotNull(message="提货时间不能为空")
    private @NotNull(message="提货时间不能为空") String pickingTime;
    @Schema(description="操作人ID", required=true, example="10010")
    @NotBlank(message="操作人ID不能为空")
    private String operatorUserId;
    @Schema(description="操作人姓名", required=true, example="王大锤")
    @NotBlank(message="操作人姓名不能为空")
    private String operatorUserName;
    @Schema(description="指派详情列表", required=true)
    @NotNull(message="指派详情列表不能为空")
    private @NotNull(message="指派详情列表不能为空") List<AssignDetailDTO> assignList;
    @Schema(description="指派单位", example="吨")
    private String productUnit;
}

