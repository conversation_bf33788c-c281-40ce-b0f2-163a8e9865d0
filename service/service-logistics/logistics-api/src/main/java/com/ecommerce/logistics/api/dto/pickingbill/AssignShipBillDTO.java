
package com.ecommerce.logistics.api.dto.pickingbill;

import com.ecommerce.logistics.api.enums.BillProxyTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

@Data
@Schema(name="指派运单对象")
public class AssignShipBillDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = 8855579585639278766L;
    @Schema(description="提货单ID", required=true, example="4cxf33j23q00jpripxvfv2xw")
    private String pickingBillId;
    @Schema(description="操作人ID", required=true, example="10010")
    private String operatorUserId;
    @Schema(description="操作人姓名", required=true, example="王大锤")
    private String operatorUserName;
    @Schema(description="会员Id")
    private String userId;
    @Schema(description="会员名称")
    private String userName;
    @Schema(description="外部调度", required=true, example="1")
    private Integer externalDispatch;
    @Schema(description="是否计算空载费")
    private Byte emptyLoadFlag;
    @Schema(description="是否管控商品")
    private Integer specialFlag;
    @Schema(description="指派详情列表")
    private List<AssignWaybillDetailDTO> assignWaybillDetailList;
    @Schema(description="单据类型")
    private String billProxyType = BillProxyTypeEnum.NORMAL.getCode();
    @Schema(description="是否可以调度")
    private Byte canOperate = 1;
    @Schema(description="源运单ID")
    private String sourceWaybillId;
    @Schema(description="源运单号")
    private String sourceWaybillNum;
    @Schema(description="源发货单")
    private String sourceDeliverySheetNum;
}

