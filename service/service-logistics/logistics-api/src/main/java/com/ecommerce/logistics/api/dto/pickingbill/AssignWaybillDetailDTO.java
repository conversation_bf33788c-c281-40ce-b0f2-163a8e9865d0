
package com.ecommerce.logistics.api.dto.pickingbill;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(name="指派运单详情对象")
public class AssignWaybillDetailDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = -6978181636879254657L;
    @Schema(description="车辆ID", required=true, example="44722qn0043vvrvfj99prwwi")
    private String vehicleId;
    @Schema(description="车牌号")
    private String number;
    @Schema(description="司机ID")
    private String driverId;
    @Schema(description="司机电话")
    private String driverPhone;
    @Schema(description="司机名称")
    private String driverName;
    @Schema(description="运输品类Id")
    private String transportCategoryId;
    @Schema(description="配送数量", required=true, example="30")
    private BigDecimal quantity;
    @Schema(description="外部运单号", required=true, example="30")
    private String externalWaybillNum;
    @Schema(description="二维码内容")
    private String qrCode;
    @Schema(description="产地")
    private String originPlace;
}

