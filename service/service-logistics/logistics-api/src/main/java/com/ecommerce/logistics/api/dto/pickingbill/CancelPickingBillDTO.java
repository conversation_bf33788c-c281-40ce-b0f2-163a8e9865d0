
package com.ecommerce.logistics.api.dto.pickingbill;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serializable;

@Data
@Schema(name="取消提货单对象")
public class CancelPickingBillDTO
implements Serializable {
    @Schema(description="提货单号", required=true, example="200893010041001")
    @NotBlank(message="提货单号不能为空")
    private String pickingBillNum;
    @Schema(description="操作人ID", required=true, example="10010")
    @NotBlank(message="操作人ID不能为空")
    private String operatorUserId;
    @Schema(description="操作人姓名", required=true, example="王大锤")
    @NotBlank(message="操作人姓名不能为空")
    private String operatorUserName;
}

