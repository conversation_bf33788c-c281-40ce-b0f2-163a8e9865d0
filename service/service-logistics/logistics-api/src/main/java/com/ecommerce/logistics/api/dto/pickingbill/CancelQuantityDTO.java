
package com.ecommerce.logistics.api.dto.pickingbill;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;
import lombok.Data;

@Data
@Schema(name="取消发货单结果对象")
public class CancelQuantityDTO
implements Serializable {
    @Schema(description="发货单号")
    private String deliverySheetNum;
    @Schema(description="取消总数量")
    private BigDecimal totalCancelQuantity;
    @Schema(description="取消发货商品数量清单")
    private Map<String, BigDecimal> cancelProductMap;
}

