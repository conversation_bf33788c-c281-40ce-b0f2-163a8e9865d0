
package com.ecommerce.logistics.api.dto.pickingbill;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="变更提货点对象")
public class ChangePickingBillWarehouseDTO {
    @Schema(description="提货单号", required=true)
    private String pickingBillId;
    @Schema(description="发货单号", required=true)
    private String deliverySheetNum;
    @Schema(description="仓库ID", required=true)
    private String warehouseId;
    @Schema(description="操作用户ID", required=false, example="10010")
    private String operationUserId;
    @Schema(description="操作用户名称", required=false, example="钱计算")
    private String operationUserName;
}

