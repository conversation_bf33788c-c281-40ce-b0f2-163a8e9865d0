
package com.ecommerce.logistics.api.dto.pickingbill;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;
import lombok.Data;

@Data
@Schema(name="关闭发货单对象")
public class CloseDeliverySheetDTO {
    @Schema(description="发货单号列表", required=true)
    private List<String> deliverySheetNumList;
    @Schema(description="操作用户ID", required=false, example="10010")
    private String operationUserId;
    @Schema(description="操作用户名称", required=false, example="钱计算")
    private String operationUserName;
}

