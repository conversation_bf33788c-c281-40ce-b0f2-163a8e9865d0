
package com.ecommerce.logistics.api.dto.pickingbill;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="关闭提货单对象")
public class ClosePickingBillDTO {
    @Schema(description="提货单号", required=true)
    private String pickingBillId;
    @Schema(description="操作用户ID", required=false, example="10010")
    private String operationUserId;
    @Schema(description="操作用户名称", required=false, example="钱计算")
    private String operationUserName;
}

