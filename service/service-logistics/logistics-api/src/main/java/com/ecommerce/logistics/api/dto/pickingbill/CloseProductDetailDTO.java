
package com.ecommerce.logistics.api.dto.pickingbill;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(name="关闭商品明细对象")
public class CloseProductDetailDTO
implements Serializable {
    @Schema(description="资源ID")
    private String resourceId;
    @Schema(description="完成数量")
    private BigDecimal completedQuantity;
    @Schema(description="实际物流费")
    private BigDecimal actualCarriage;
}

