
package com.ecommerce.logistics.api.dto.pickingbill;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

@Data
@Schema(name="关闭发货单结果对象")
public class CloseQuantityDTO
implements Serializable {
    @Schema(description="发货单号")
    private String deliverySheetNum;
    @Schema(description="取消发货商品数量清单")
    private List<CloseProductDetailDTO> closeProductDetailList;
}

