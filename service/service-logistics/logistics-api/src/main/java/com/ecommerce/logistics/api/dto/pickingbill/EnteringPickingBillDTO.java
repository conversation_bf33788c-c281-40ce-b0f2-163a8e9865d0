
package com.ecommerce.logistics.api.dto.pickingbill;

import com.ecommerce.logistics.api.enums.BillProxyTypeEnum;
import com.ecommerce.logistics.api.enums.TransportToolTypeEnum;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@Schema(name="录入提货单对象")
public class EnteringPickingBillDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = 1291136401208316082L;
    @Schema(description="发货单号", required=true, example="20180705124556")
    private String deliverySheetNum;
    @Schema(description="二级发货单号(背靠背场景下,创建一级提货单时传入)")
    private String secondaryDeliverySheetNum;
    @Schema(description="二级运单号(背靠背场景下,创建一级提货单时传入)")
    private String secondaryWaybillNum;
    @Schema(description="二级提货单ID")
    private String secondaryPickingBillId;
    @Schema(description="买家ID", required=true, example="8a2c3cc75eff5727015effb0aad5adaf")
    @NotBlank(message="买家ID不能为空")
    private String buyerId;
    @Schema(description="买家名称", required=true, example="李某某")
    @NotBlank(message="买家名称不能为空")
    private String buyerName;
    @Schema(description="卖家ID", required=true, example="ajkacc75eff5727015effb0aad5adaf")
    private String sellerId;
    @Schema(description="卖家名称", required=true)
    private String sellerName;
    @Schema(description="提货单类型：0100：买家自提   0200：卖家配送  0300：平台配送", required=true, example="030060300")
    @NotNull(message="提货单类型不能为空")
    private @NotNull(message="提货单类型不能为空") String type;
    @Schema(description="收货人", required=true, example="张某强")
    @NotBlank(message="收货人不能为空")
    private String receiver;
    @Schema(description="收货人电话", required=true, example="13618209318")
    @NotBlank(message="收货人电话不能为空")
    private String receiverPhone;
    @Schema(description="收货地址ID", required=true, example="8a2c3cc75efdeqw727015affb0aad5ad")
    @NotBlank(message="收货地址编号不能为空")
    private String receiveAddressId;
    @Schema(description="收货省份", required=true, example="广东省")
    @NotBlank(message="收货省份不能为空")
    private String province;
    @Schema(description="省份编码", required=true, example="S001")
    @NotBlank(message="省份编码不能为空")
    private String provinceCode;
    @Schema(description="收货城市", required=true, example="深圳市")
    @NotBlank(message="收货城市不能为空")
    private String city;
    @Schema(description="城市编码", required=true, example="C001")
    @NotBlank(message="城市编码不能为空")
    private String cityCode;
    @Schema(description="收货地区", required=true, example="罗湖区")
    @NotNull(message="收货地区不能为空")
    private @NotNull(message="收货地区不能为空") String district;
    @Schema(description="区域编码", required=true, example="D001")
    @NotNull(message="区域编码不能为空")
    private @NotNull(message="区域编码不能为空") String districtCode;
    @Schema(description="收货街道", required=true, example="深南街道")
    @NotNull(message="收货街道不能为空")
    private @NotNull(message="收货街道不能为空") String street;
    @Schema(description="收货详细地址", required=true, example="地王大厦14-9")
    @NotBlank(message="收货详细地址不能为空")
    private String address;
    @Schema(description="地址经纬度", required=true, example="109.899131,40.512316")
    private String location;
    @Schema(description="配送时间", required=true, example="2018-08-06")
    @NotNull(message="配送时间不能为空")
    private @NotNull(message="配送时间不能为空") Date deliveryTime;
    @Schema(description="配送时段", required=true, example="030020300")
    @NotNull(message="配送时段不能为空")
    private @NotNull(message="配送时段不能为空") String deliveryTimeRange;
    @Schema(description="操作用户ID", required=false, example="10010")
    private String operationUserId;
    @Schema(description="操作用户名称", required=false, example="钱计算")
    private String operationUserName;
    @Schema(description="仓库ID", required=true, example="13niw8u0pf264inaxb2cxwu9a")
    @NotBlank(message="仓库ID不能为空")
    private String warehouseId;
    @Schema(description="推荐ID,门店配送时传入", required=false, example="8a2c3cc75eff5727015effb0aad501a1")
    private String recommendId;
    @JsonIgnore
    private String recommendUserId;
    @Schema(description="外部同步标识", required=false, example="ExternalSyncFlagEnum")
    private String externalSyncFlag;
    @Schema(description="是否需要搬运", required=false, example="0:否 1:是")
    private Integer carryFlag;
    @Schema(description="商品详情列表", required=true)
    @NotNull(message="商品详情列表")
    private @NotNull(message="商品详情列表") List<ProductDetailDTO> productDetailList;
    @Schema(description="是否需要监控, 0-不需要,1-需要")
    private Integer needMonitor = 0;
    @Schema(description="关联的提货单类型")
    private String relationType;
    @Schema(description="单据代理类型")
    private String billProxyType = BillProxyTypeEnum.NORMAL.getCode();
    @Schema(description="是否指派承运商")
    private Boolean assignBuyerCarrierFlag = false;
    @Schema(description="erp支付账户编码")
    private String mdmCode;
    @Schema(description="运输工具类型")
    private String transportToolType = TransportToolTypeEnum.ROAD_TRANSPORT.getCode();
    @Schema(description="起运港ID")
    private String loadPortId;
    @Schema(description="起运港名")
    private String loadPortName;
    @Schema(description="目的港ID")
    private String unloadPortId;
    @Schema(description="目的港名")
    private String unloadPortName;
    @Schema(description="合同Id")
    private String dealsId;
    @Schema(description="合同序号")
    private String dealsName;
    @Schema(description="合同项目名称")
    private String projectName;
}