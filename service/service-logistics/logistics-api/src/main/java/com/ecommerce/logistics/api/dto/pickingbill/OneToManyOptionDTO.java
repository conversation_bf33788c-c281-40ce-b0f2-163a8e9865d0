
package com.ecommerce.logistics.api.dto.pickingbill;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

@Data
@Schema(name="一对多选项")
public class OneToManyOptionDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = 8180278268481013060L;
    @Schema(description="键")
    private String key;
    @Schema(description="值集合")
    private List<String> valueList;
}

