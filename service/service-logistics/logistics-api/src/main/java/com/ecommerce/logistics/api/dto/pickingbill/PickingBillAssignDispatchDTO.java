
package com.ecommerce.logistics.api.dto.pickingbill;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(name="提货单对应的调度单指派列表")
public class PickingBillAssignDispatchDTO
implements Serializable {
    @Schema(description="调度单Id")
    private String dispatchBillId;
    @Schema(description="承运商名称")
    private String carrierName;
    @Schema(description="承运商类型")
    private String carrierType;
    @Schema(description="承运商Id")
    private String carrierId;
    @Schema(description="调度单号")
    private String dispatchBillNum;
    @Schema(description="最大运力")
    private BigDecimal maxCapacity;
    @Schema(description="已指派运力")
    private BigDecimal assignCapacity;
    @Schema(description="剩余运力")
    private BigDecimal leaveCapacity;
    @Schema(description="分配数量")
    private BigDecimal deliveryQuantity;
    @Schema(description="状态")
    private String status;
    @Schema(description="商品单位")
    private String productUnit;
    @Serial
    private static final long serialVersionUID = 1L;
}

