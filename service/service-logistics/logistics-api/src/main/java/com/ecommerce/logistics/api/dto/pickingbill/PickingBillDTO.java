
package com.ecommerce.logistics.api.dto.pickingbill;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

@Data
@Schema(name="提货单详情结果对象")
public class PickingBillDTO
implements Serializable {
    @Deprecated(since = "2.1.4-RELEASE")
    @Schema(description="提货单ID")
    private String pickingBillId;
    @Deprecated(since = "2.1.4-RELEASE")
    @Schema(description="提货单号")
    private String pickingBillNum;
    @Deprecated(since = "2.1.4-RELEASE")
    @Schema(description="序列号")
    private Integer seqNum;
    @Schema(description="发货单号")
    private String deliverySheetNum;
    @Schema(description="商品详情")
    private String productDesc;
    @Schema(description="商品图片")
    private String productImg;
    @Schema(description="商品ID")
    private String productId;
    @Schema(description="是否管控商品")
    private Integer specialFlag;
    @Schema(description="厂商")
    private String vendor;
    @Schema(description="运输品类ID")
    private String transportCategoryId;
    @Deprecated(since = "2.1.4-RELEASE")
    @Schema(description="商品数量")
    private BigDecimal productQuantity;
    @Schema(description="商品单位 1-吨 2-升 3-平方米 4-立方米")
    private String productUnit;
    @Schema(description="签收类型")
    private String signType;
    @Schema(description="收货地址Id")
    private String receiveAddressId;
    @Schema(description="收货地址Id")
    private String receiveAddressLocation;
    @Schema(description="收货省份")
    private String receiveProvince;
    @Schema(description="收货省份编码")
    private String receiveProvinceCode;
    @Schema(description="收货城市")
    private String receiveCity;
    @Schema(description="收货城市编码")
    private String receiveCityCode;
    @Schema(description="收货区域")
    private String receiveDistrict;
    @Schema(description="收货区域编码")
    private String receiveDistrictCode;
    @Schema(description="收货街道")
    private String receiveStreet;
    @Schema(description="收货街道编码")
    private String receiveStreetCode;
    @Schema(description="收货人")
    private String receiver;
    @Schema(description="收货地址")
    private String receiveAddress;
    @Schema(description="收货人电话")
    private String receiverPhone;
    @Schema(description="提货地址Id")
    private String warehouseId;
    @Schema(description="买家ID")
    private String buyerId;
    @Schema(description="买家名称")
    private String buyerName;
    @Schema(description="买家ID")
    private String sellerId;
    @Schema(description="卖家名称")
    private String sellerName;
    @Schema(description="提货点")
    private String warehouseName;
    @Schema(description="提货点地址")
    private String warehouseAddress;
    @Schema(description="配送时间")
    private Date deliveryTime;
    @Schema(description="配送方式")
    private String type;
    @Deprecated(since = "2.1.4-RELEASE")
    @Schema(description="指派模式")
    private String assignMode;
    @Schema(description="配送时间范围")
    private String deliveryTimeRange;
    @Deprecated(since = "2.1.4-RELEASE")
    @Schema(description="提货单状态")
    private String status;
    @Schema(description="创建时间")
    private Date createTime;
    @Schema(description="资源ID")
    private String resourceId;
    @Deprecated(since = "2.1.4-RELEASE")
    @Schema(description="发货数量")
    private BigDecimal shippingQuantity;
    @Deprecated(since = "2.1.4-RELEASE")
    @Schema(description="签收数量")
    private BigDecimal completeQuantity;
    @Deprecated(since = "2.1.4-RELEASE")
    @Schema(description="指派数量")
    private BigDecimal deliveryQuantity;
    @Schema(description="单位运价")
    private BigDecimal unitCarriage;
    @Deprecated(since = "2.1.4-RELEASE")
    @Schema(description="提货时间")
    private Date pickingTime;
    @Schema(description="同步标识")
    private String syncFlag;
    @Schema(description="背靠背单据层级类型")
    private String billProxyType;
    @Schema(description="是否可操作")
    private Byte canOperate;
}

