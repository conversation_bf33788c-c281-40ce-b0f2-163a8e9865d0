
package com.ecommerce.logistics.api.dto.pickingbill;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

@Data
@Schema(name="提货单导出DTO")
public class PickingBillExportDTO {
    @Schema(description="提货单号")
    private String pickingBillNum;
    @Schema(description="提货单状态")
    private String status;
    @Schema(description="出货点")
    private String warehouseName;
    @Schema(description="买家名称")
    private String buyerName;
    @Schema(description="商品品类")
    private String goodsCategory;
    @Schema(description="运输品类Id")
    private String transportCategoryId;
    @Schema(description="运输品类")
    private String categoryName;
    @Schema(description="商品名称")
    private String goodsName;
    @Schema(description="配送数量（吨）")
    private BigDecimal productQuantity;
    @Schema(description="已指派数量（吨）")
    private BigDecimal deliveryQuantity;
    @Schema(description="已配送数量（吨）")
    private BigDecimal completeQuantity;
    @Schema(description="收货人")
    private String receiver;
    @Schema(description="省份名称")
    private String provinceName;
    @Schema(description="城市名称")
    private String cityName;
    @Schema(description="区县名称")
    private String districtName;
    @Schema(description="街道名称")
    private String streetName;
    @Schema(description="收货地址")
    private String receiveAddress;
    @Schema(description="配送时间")
    private Date deliveryTime;
    @Schema(description="配送时间区间")
    private String deliveryTimeRange;
    @Schema(description="订单号")
    private String orderCode;
    @Schema(description="发货单号")
    private String deliverySheetNum;
    @Schema(description="物流方式")
    private String logisticType;
    @Schema(description="指派模式")
    private String assignMode;
    @Schema(description="是否为管控商品")
    private Integer specialFlag;
}

