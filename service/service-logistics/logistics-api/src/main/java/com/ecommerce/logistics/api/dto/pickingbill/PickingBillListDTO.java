
package com.ecommerce.logistics.api.dto.pickingbill;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

@Data
@Schema(name="提货单列表结果对象")
public class PickingBillListDTO
implements Serializable {
    @Schema(description="提货单ID")
    private String pickingBillId;
    @Schema(description="提货单号")
    private String pickingBillNum;
    @Schema(description="发货单号")
    private String deliverySheetNum;
    @Schema(description="序列号")
    private Integer seqNum;
    @Schema(description="买家ID")
    private String buyerId;
    @Schema(description="买家名称")
    private String buyerName;
    @Schema(description="卖家ID")
    private String sellerId;
    @Schema(description="卖家名称")
    private String sellerName;
    @Schema(description="商品详情")
    private String productDesc;
    @Schema(description="是否管控商品")
    private Integer specialFlag;
    @Schema(description="商品数量")
    private BigDecimal productQuantity;
    @Schema(description="已指派数量")
    private BigDecimal deliveryQuantity;
    @Schema(description="运输品类ID")
    private String transportCategoryId;
    @Schema(description="运输品类名称")
    private String transportCategoryName;
    @Schema(description="待指派数量")
    private BigDecimal tbaQuantity;
    @Schema(description="已完成数量")
    private BigDecimal completeQuantity;
    @Schema(description="收货人")
    private String receiver;
    @Schema(description="收货人联系方式")
    private String receiverPhone;
    @Schema(description="省份编码")
    private String receiveProvinceCode;
    @Schema(description="城市编码")
    private String receiveCityCode;
    @Schema(description="区域编码")
    private String receiveDistrictCode;
    @Schema(description="收货地址")
    private String receiveAddress;
    @Schema(description="提货地址")
    private String warehouseName;
    @Schema(description="提货地址")
    private String warehouseAddress;
    @Schema(description="指派方式")
    private String assignMode;
    @Schema(description="配送方式")
    private String type;
    @Schema(description="配送时间")
    private Date deliveryTime;
    @Schema(description="提货单状态 1：新建  2：指派中  3：已指派  4：已完成  5：已取消")
    private String status;
    @Schema(description="创建时间")
    private Date createTime;
    @Schema(description="商品单位 1-吨 2-升 3-平方米 4-立方米")
    private String productUnit;
    @Schema(description="提货地址ID")
    private String warehouseId;
    @Schema(description="配送时间范围")
    private String deliveryTimeRange;
    @Schema(description="提货时间")
    private Date pickingTime;
    @Schema(description="同步标识")
    private String syncFlag;
    @Schema(description="是否可以调度")
    private Byte canOperate;
    @Schema(description="单据层级类型")
    private String billProxyType;
    @Schema(description="是否评价（0-未评价，1-已评价）")
    private Byte isEvaluate;
}

