
package com.ecommerce.logistics.api.dto.pickingbill;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

@Data
@Schema(name="提货单列表查询对象")
public class PickingBillListQueryDTO
implements Serializable {
    @Schema(description="发货单号", required=false)
    private String deliverySheetNum;
    @Schema(description="提货单号", required=false)
    private String pickingBillNum;
    @Schema(description="提货点ID", required=false)
    private String warehouseId;
    @Schema(description="配送开始时间", required=false)
    private String deliveryTimeStart;
    @Schema(description="配送结束时间", required=false)
    private String deliveryTimeEnd;
    @Schema(description="配送时间段", required=false)
    private Integer deliveryTimeRange;
    @Schema(description="收货省份Code", required=false)
    private String provinceCode;
    @Schema(description="收货城市Code", required=false)
    private String cityCode;
    @Schema(description="收货区域Code", required=false)
    private String districtCode;
    @Schema(description="买家ID", required=false)
    private String buyerId;
    @Schema(description="卖家ID", required=false)
    private String sellerId;
    @Schema(description="配送类型", required=false)
    private String type;
    @Schema(description="配送类型", required=false)
    private String assignMode;
    @Schema(description="提货单状态", required=false)
    private String status;
    @Schema(description="运输品类ID")
    private String transportCategoryId;
    @Schema(description="收货人")
    private String receiver;
    @Schema(description="区域权限列表", required=false)
    private List<String> regionCodeList;
}

