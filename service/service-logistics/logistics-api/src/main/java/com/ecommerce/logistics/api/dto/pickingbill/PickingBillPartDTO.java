
package com.ecommerce.logistics.api.dto.pickingbill;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import lombok.Data;

@Data
@Schema(name="提货单详情关联对象")
public class PickingBillPartDTO
implements Serializable {
    @Schema(description="提货单号", required=true, example="0100")
    private String pickingBillNum;
    @Schema(description="商品描述", required=true, example="0100")
    private String productDesc;
    @Schema(description="收货区域", required=true, example="0100")
    private String receiveDistrict;
    @Schema(description="收货详细地址", required=true, example="0100")
    private String receiveAddress;
    @Schema(description="期望配送时间", required=true, example="0100")
    private String deliveryTime;
    @Schema(description="提货点", required=true, example="0100")
    private String warehouseAddress;
    @Schema(description="期望配送时间段", required=true, example="0100")
    private Integer deliveryTimeRange;
}

