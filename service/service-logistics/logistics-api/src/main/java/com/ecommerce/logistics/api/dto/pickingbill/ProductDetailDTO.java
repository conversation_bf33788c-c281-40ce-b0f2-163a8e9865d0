
package com.ecommerce.logistics.api.dto.pickingbill;

import com.ecommerce.logistics.api.dto.vehicle.SelfPickingVehicleDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
@Schema(name="商品详情对象")
public class ProductDetailDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = -4036603740609695450L;
    @Schema(description="商品ID", required=true, example="8a2c3cc75efr5727015effb0aad501a1")
    private String productId;
    @Schema(description="运输品类ID", required=true, example="0100")
    private String transportCategoryId;
    @Schema(description="厂商", required=true)
    private String vendor;
    @Schema(description="商品ID")
    private String goodsId;
    @Schema(description="商品描述", required=true, example="王牌工匠PC2.5-强度等级:高")
    private String note;
    @Schema(description="商品单位", required=true, example="0100")
    private String unit;
    @Schema(description="商品数量", required=true, example="300")
    private BigDecimal quantity;
    @Schema(description="特殊商品标识", required=true, example="0")
    private Integer specialFlag;
    @Schema(description="商品单价", required=false)
    private BigDecimal price;
    @Schema(description="商品图片", required=false)
    private String productImg;
    @Schema(description="物料编码", required=true)
    private String commodityCode;
    @Schema(description="签收类型")
    private String signType;
    @Schema(description="搬运标识")
    private Byte carryFlag = 0;
    @Schema(description="是否计算运费", required=false, example="0:否 1:是")
    private Integer calculateCarriageFlag;
    @Schema(description="应收运费单价")
    private BigDecimal receivableCarriagePrice = BigDecimal.ZERO;
    @Schema(description="流向卸货点编码")
    private String unloadingFlowCode;
    @Schema(description="二级发货单商品ID(仅用于背靠背,创建一级提货单时传入)")
    private String secondaryGoodsId;
    @Schema(description="批量指派车辆列表")
    List<SelfPickingVehicleDTO> selfPickingVehicleDTOList;
    @Schema(description="指派承运商信息")
    List<AssignDetailDTO> selfPickingCarrierDTOList;
}