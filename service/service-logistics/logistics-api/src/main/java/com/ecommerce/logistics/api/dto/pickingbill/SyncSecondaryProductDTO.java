
package com.ecommerce.logistics.api.dto.pickingbill;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(name="同步用背靠背二级商品对象")
public class SyncSecondaryProductDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = 2504892567996294250L;
    @Schema(description="商品ID", required=true, example="8a2c3cc75efr5727015effb0aad501a1")
    private String productId;
    @Schema(description="运输品类ID", required=true, example="0100")
    private String transportCategoryId;
    @Schema(description="厂商", required=true)
    private String vendor;
    @Schema(description="商品ID")
    private String goodsId;
    @Schema(description="商品描述", required=true, example="王牌工匠PC2.5-强度等级:高")
    private String note;
    @Schema(description="商品单位", required=true, example="0100")
    private String unit;
    @Schema(description="商品数量", required=true, example="300")
    private BigDecimal quantity;
    @Schema(description="特殊商品标识", required=true, example="0")
    private Integer specialFlag;
    @Schema(description="商品单价", required=false)
    private BigDecimal price;
    @Schema(description="商品图片", required=false)
    private String productImg;
    @Schema(description="物料编码", required=true)
    private String commodityCode;
    @Schema(description="签收类型")
    private String signType;
    @Schema(description="搬运标识")
    private Byte carryFlag = 0;
    @Schema(description="是否计算运费", required=false, example="0:否 1:是")
    private Integer calculateCarriageFlag;
    @Schema(description="流向卸货点编码")
    private String unloadingFlowCode;
}