
package com.ecommerce.logistics.api.dto.pickingbill;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

@Data
@Schema(name="两层下拉列表实体")
public class TwoLeaveOptionDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = 8181453237244780627L;
    @Schema(description="第一级集合")
    private List<String> firstList;
    @Schema(description="第二级集合")
    private List<OneToManyOptionDTO> secondList;
}

