
package com.ecommerce.logistics.api.dto.pickingbill;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="委托平台配送对象")
public class VendorEntrustmentDTO {
    @Schema(description="提货单ID")
    private String pickingBillId;
    @Schema(description="操作人ID")
    private String updateUser;
    @Schema(description="操作类型")
    private String type;
    @Schema(description="操作人名称")
    private String updateUserName;
}

