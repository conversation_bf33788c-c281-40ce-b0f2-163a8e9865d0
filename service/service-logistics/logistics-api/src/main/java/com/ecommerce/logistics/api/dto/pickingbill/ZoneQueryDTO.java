
package com.ecommerce.logistics.api.dto.pickingbill;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

@Data
@Schema(name="区域查询承运商对象")
public class ZoneQueryDTO
implements Serializable {
    @Schema(description="省份编码")
    private String provinceCode;
    @Schema(description="城市编码")
    private String cityCode;
    @Schema(description="区域编码")
    private String districtCode;
    @Schema(description="运输品类")
    private String loadCategory;
    @Schema(description="查询承运商类型列表")
    private List<Integer> carrierTypeList;
}

