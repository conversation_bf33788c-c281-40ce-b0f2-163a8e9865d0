
package com.ecommerce.logistics.api.dto.pickingbill;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(name="区域查询承运商结果对象")
public class ZoneQueryResultDTO {
    @Schema(description="承运商ID")
    private String carrierId;
    @Schema(description="承运商名称")
    private String carrierName;
    @Schema(description="承运商类型")
    private String carrierType;
    @Schema(description="最大运力")
    private BigDecimal maxCapacity;
    @Schema(description="已指派运力")
    private BigDecimal deliveryCapacity;
    @Schema(description="剩余运力")
    private BigDecimal remainderCapacity;
}

