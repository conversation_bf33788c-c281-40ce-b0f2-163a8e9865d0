
package com.ecommerce.logistics.api.dto.porterage;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(name="搬运费计算对象")
public class PorterageComputeDTO {
    @Schema(description="规则Id", required=true, example="xxx")
    private String porterageRuleId;
    @Schema(description="卖家ID", required=true, example="xxx")
    private String sellerId;
    @Schema(description="配送方式", required=true, example="030060200")
    private String deliveryType;
    @Schema(description="省份编码", required=true, example="440000")
    private String provinceCode;
    @Schema(description="城市编码", required=true, example="441900")
    private String cityCode;
    @Schema(description="地区编码", required=true, example="441914")
    private String districtCode;
    @Schema(description="商品数量", required=true, example="5袋")
    private BigDecimal meteringQuantity;
    @Schema(description="单位运输数量", required=true, example="7层")
    private BigDecimal transportQuantity;
}

