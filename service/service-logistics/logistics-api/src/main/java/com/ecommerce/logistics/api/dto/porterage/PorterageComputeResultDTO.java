
package com.ecommerce.logistics.api.dto.porterage;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(name="搬运费计算结果对象")
public class PorterageComputeResultDTO {
    @Schema(description="规则Id", required=true, example="xxx")
    private String porterageRuleId;
    @Schema(description="规则明细Id", required=true, example="xxx")
    private String porterageRuleItemId;
    @Schema(description="订单搬运费", required=true, example="100.00")
    private BigDecimal orderPorterage;
}

