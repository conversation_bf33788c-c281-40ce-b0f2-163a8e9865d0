
package com.ecommerce.logistics.api.dto.porterage;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

@Data
@Schema(name="搬运费明细对象")
public class PorterageItemDTO {
    @Schema(description="搬运费规则明细id")
    private String porterageRuleItemId;
    @Schema(description="运费规则id")
    private String porterageRuleId;
    @Schema(description="计量单位", required=true, example="袋")
    private String meteringUnit;
    @Schema(description="计量范围最小值", required=true, example="1.00")
    private BigDecimal meteringNum;
    @Schema(description="运输单位", required=true, example="层")
    private String transportUnit;
    @Schema(description="运输范围最小值", required=true, example="1.00")
    private BigDecimal transportNum;
    @Schema(description="搬运费价格", required=true, example="2.00")
    private BigDecimal porteragePrice;
    @Schema(description="创建人ID")
    private String createUser;
    @Schema(description="创建时间")
    private Date createTime;
    @Schema(description="更新人ID")
    private String updateUser;
    @Schema(description="更新时间")
    private Date updateTime;
}

