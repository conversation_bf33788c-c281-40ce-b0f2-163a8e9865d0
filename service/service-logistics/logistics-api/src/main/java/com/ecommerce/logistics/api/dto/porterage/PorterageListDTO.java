
package com.ecommerce.logistics.api.dto.porterage;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;
import lombok.Data;

@Data
@Schema(name="搬运费列表对象")
public class PorterageListDTO {
    @Schema(description="规则Id")
    private String porterageRuleId;
    @Schema(description="规则名称", example="袋装水泥搬运费规则")
    private String ruleName;
    @Schema(description="归属用户ID", example="10086")
    private String userId;
    @Schema(description="归属用户名称", example="platform")
    private String userName;
    @Schema(description="归属用户类型", example="5")
    private Integer userType;
    @Schema(description="省份名称", example="广东省")
    private String province;
    @Schema(description="省份编码", example="440000")
    private String provinceCode;
    @Schema(description="城市名称", example="惠州市")
    private String city;
    @Schema(description="城市编码", example="441900")
    private String cityCode;
    @Schema(description="区域名称", example="中堂镇")
    private String district;
    @Schema(description="区域编码", example="441914")
    private String districtCode;
    @Schema(description="街道名称", example="中堂镇")
    private String street;
    @Schema(description="街道编码", example="441914")
    private String streetCode;
    @Schema(description="运输品类ID")
    private String transportCategoryId;
    @Schema(description="创建人id")
    private String createUser;
    @Schema(description="创建时间")
    private Date createTime;
    @Schema(description="更新人ID")
    private String updateUser;
    @Schema(description="更新时间")
    private Date updateTime;
}

