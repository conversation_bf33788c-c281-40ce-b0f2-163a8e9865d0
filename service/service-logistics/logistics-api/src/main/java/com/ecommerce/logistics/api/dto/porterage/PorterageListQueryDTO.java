
package com.ecommerce.logistics.api.dto.porterage;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="搬运费列表查询对象")
public class PorterageListQueryDTO {
    @Schema(description="用户Id")
    private String userId;
    @Schema(description="用户类型")
    private Integer userType;
    @Schema(description="规则名称")
    private String ruleName;
    @Schema(description="省份编码", example="440000")
    private String provinceCode;
    @Schema(description="城市编码", example="441900")
    private String cityCode;
    @Schema(description="区域编码", example="441914")
    private String districtCode;
    @Schema(description="街道编码", example="441914")
    private String streetCode;
    @Schema(description="创建时间start")
    private String createTimeStart;
    @Schema(description="创建时间end")
    private String createTimeEnd;
}

