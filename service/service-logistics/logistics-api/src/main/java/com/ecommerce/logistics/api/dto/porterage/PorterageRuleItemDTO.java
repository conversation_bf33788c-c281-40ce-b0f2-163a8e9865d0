
package com.ecommerce.logistics.api.dto.porterage;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(name="搬运费规则明细对象")
public class PorterageRuleItemDTO {
    @Schema(description="计量单位", required=true, example="袋")
    private String meteringUnit;
    @Schema(description="计量范围最小值", required=true, example="1")
    private BigDecimal meteringNum;
    @Schema(description="运输单位", required=true, example="层")
    private String transportUnit;
    @Schema(description="运输范围最小值", required=true, example="1")
    private BigDecimal transportNum;
    @Schema(description="搬运价格", required=true, example="10.00")
    private BigDecimal porteragePrice;
}

