
package com.ecommerce.logistics.api.dto.porterage;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="搬运费规则搜索结果对象")
public class PorterageSearchResultDTO {
    @Schema(description="规则Id", required=true, example="xxx")
    private String porterageRuleId;
    @Schema(description="规则名称", required=true, example="袋装水泥搬运费规则")
    private String ruleName;
    @Schema(description="计量单位", required=true, example="袋")
    private String meteringUnit;
    @Schema(description="运输单位", required=true, example="层")
    private String transportUnit;
}

