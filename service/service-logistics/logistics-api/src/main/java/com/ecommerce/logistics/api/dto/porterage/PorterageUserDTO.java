
package com.ecommerce.logistics.api.dto.porterage;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;
import lombok.Data;

@Data
@Schema(name="搬运费用户对象")
public class PorterageUserDTO {
    @Schema(description="归属用户ID", required=true, example="xgegt2qhi2ap5uqqqt7maqu6")
    private String userId;
    @Schema(description="运输品类ID列表", required=true)
    private List<String> transportCategoryIdList;
}

