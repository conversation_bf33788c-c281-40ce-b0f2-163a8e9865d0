
package com.ecommerce.logistics.api.dto.porterage;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;
import lombok.Data;

@Data
@Schema(name="区域范围搬运费列表对象")
public class PorterageZoneListDTO {
    @Schema(description="归属用户ID", required=true, example="xxx")
    private String userId;
    @Schema(description="运输品类ID列表", required=true, example="xxx")
    private String transportCategoryId;
    @Schema(description="运输品类ID列表", required=true, example="xxx")
    private String transportCategoryName;
    @Schema(description="搬运费规则ID", required=true, example="xxx")
    private String porterageRuleId;
    @Schema(description="搬运费规则明细列表")
    private List<PorterageRuleItemDTO> ruleItemList;
}

