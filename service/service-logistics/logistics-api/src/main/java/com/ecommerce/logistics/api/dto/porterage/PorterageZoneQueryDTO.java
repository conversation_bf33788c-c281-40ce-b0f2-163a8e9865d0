
package com.ecommerce.logistics.api.dto.porterage;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;
import lombok.Data;

@Data
@Schema(name="区域范围搬运费查询对象")
public class PorterageZoneQueryDTO {
    @Schema(description="提货类型", required=true, example="030060200")
    private String pickingBillType;
    @Schema(description="省份编码", required=true, example="110000")
    private String provinceCode;
    @Schema(description="城市编码", required=true, example="110100")
    private String cityCode;
    @Schema(description="地区编码", required=true, example="441914")
    private String districtCode;
    @Schema(description="街道编码", required=true, example="441914")
    private String streetCode;
    @Schema(description="归属用户列表", required=true)
    private List<PorterageUserDTO> porterageUserList;
}

