
package com.ecommerce.logistics.api.dto.porterage;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="范围明细查询对象")
public class ZoneQueryItemDTO {
    @Schema(description="省份名称", required=true, example="广东省")
    private String province;
    @Schema(description="省份编码", required=true, example="440000")
    private String provinceCode;
    @Schema(description="城市名称", required=true, example="惠州市")
    private String city;
    @Schema(description="城市编码", required=true, example="441900")
    private String cityCode;
    @Schema(description="地区名称", required=true, example="中堂镇")
    private String district;
    @Schema(description="地区编码", required=true, example="441914")
    private String districtCode;
    @Schema(description="街道", required=true, example="测试街道")
    private String street;
}

