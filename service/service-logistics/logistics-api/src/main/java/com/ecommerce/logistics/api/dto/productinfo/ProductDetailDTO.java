
package com.ecommerce.logistics.api.dto.productinfo;

import com.ecommerce.logistics.api.dto.porterage.PorterageZoneListDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
@Schema(name="商品详情DTO")
public class ProductDetailDTO
implements Serializable {
    @Schema(description="运单ID", required=true)
    private String waybillId;
    @Schema(description="运单号", required=true)
    private String waybillNum;
    @Schema(description="运单子项ID")
    private String waybillItemId;
    @Schema(description="父运单Id")
    private String parentId;
    @Schema(description="经纬度信息")
    private String location;
    @Schema(description="经度", required=true)
    private BigDecimal longitude;
    @Schema(description="纬度", required=true)
    private BigDecimal latitude;
    @Schema(description="买家ID", required=true)
    private String buyerId;
    @Schema(description="买家名称", required=true)
    private String buyerName;
    @Schema(description="卖家ID", required=true)
    private String sellerId;
    @Schema(description="卖家名称", required=true)
    private String sellerName;
    @Schema(description="商品ID", required=true)
    private String productId;
    @Schema(description="商品信息", required=true)
    private String productDesc;
    @Schema(description="管控商品", required=true)
    private Integer specialFlag;
    @Schema(description="商品数量", required=true)
    private BigDecimal quantity;
    @Schema(description="实际出厂量", required=true)
    private BigDecimal actualQuantity;
    @Schema(description="商品单位", required=true)
    private String productUnit;
    @Schema(description="发货单号", required=true)
    private String deliverySheetNum;
    @Schema(description="提货单号", required=true)
    private String pickingBillNum;
    @Schema(description="收货地址ID", required=true)
    private String receiveAddressId;
    @Schema(description="收货人", required=true)
    private String receiver;
    @Schema(description="收货人电话", required=true)
    private String receiverPhone;
    @Schema(description="收货地址", required=true)
    private String receiverAddress;
    @Schema(description="收货地区", required=true)
    private String receiverDistrict;
    @Schema(description="签收司机", required=true)
    private String signDriver;
    @Schema(description="签收时间", required=true)
    private String signTime;
    @Schema(description="签收类型")
    private String signType;
    @Schema(description="完成图片bid集合", required=true)
    private List<String> completeBids;
    @Schema(description="配送时间", required=true)
    private String deliveryTime;
    @Schema(description="配送时间段", required=true)
    private String deliveryTimeRange;
    @Schema(description="状态", required=true)
    private String status;
    @Schema(description="到达时间", required=true)
    private String arriveDestinationTime;
    @Schema(description="签收时间", required=true)
    private String completeTime;
    @Schema(description="提货时间", required=true)
    private String pickingTime;
    @Schema(description="是否达到目的地", required=true)
    private boolean alreadyArriveDestination;
    @Schema(description="是否开始搬运", required=true)
    private boolean alreadyBeginCarry;
    @Schema(description="是否已完成", required=true)
    private boolean alreadyComplete;
    @Schema(description="商品图片", required=true)
    private String productImg;
    @Schema(description="物料编码", required=true)
    private String commodityCode;
    @Schema(description="省份编码", required=true)
    private String provinceCode;
    @Schema(description="城市编码", required=true)
    private String cityCode;
    @Schema(description="区域编码", required=true)
    private String districtCode;
    @Schema(description="运费单价", required=true)
    private BigDecimal carriageUnitPrice;
    @Schema(description="同步标识", required=true)
    private String syncFlag;
    @Schema(description="外部运单状态", required=true)
    private String externalWaybillStatus;
    @Schema(description="外部运单号", required=true)
    private String externalWaybillNum;
    @Schema(description="同步失败原因", required=true)
    private String syncFailReason;
    @Schema(description="流向卸货点编码")
    private String unloadingFlowCode;
    @Schema(description="目的港名")
    private String unloadPortName;
    @Schema(description="搬运标识")
    private Byte carryFlag = 0;
    @Schema(description="搬运标识")
    private PorterageZoneListDTO porterageZoneListDTO;
    @Schema(description="订单ID")
    private String orderItemId;
}

