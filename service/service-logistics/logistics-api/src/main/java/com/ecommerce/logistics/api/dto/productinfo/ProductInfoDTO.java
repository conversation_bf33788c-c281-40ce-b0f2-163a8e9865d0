
package com.ecommerce.logistics.api.dto.productinfo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="商品信息DTO")
public class ProductInfoDTO {
    @Schema(description="商品ID", required=true)
    private String productInfoId;
    @Schema(description="资源ID", required=false)
    private String resourceId;
    @Schema(description="商品详情", required=true)
    private String note;
    @Schema(description="商品图片", required=true)
    private String productImg;
}

