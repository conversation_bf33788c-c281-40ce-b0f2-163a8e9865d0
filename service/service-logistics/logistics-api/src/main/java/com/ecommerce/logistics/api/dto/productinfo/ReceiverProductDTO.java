
package com.ecommerce.logistics.api.dto.productinfo;

import com.ecommerce.logistics.api.dto.porterage.PorterageZoneListDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(name="收货人商品详情DTO")
public class ReceiverProductDTO {
    @Schema(description="运单ID", required=true)
    private String waybillId;
    @Schema(description="买家ID")
    private String buyerId;
    @Schema(description="买家名称")
    private String buyerName;
    @Schema(description="卖家ID")
    private String sellerId;
    @Schema(description="卖家名称")
    private String sellerName;
    @Schema(description="商品信息", required=true)
    private String productDesc;
    @Schema(description="商品数量", required=true)
    private BigDecimal quantity;
    @Schema(description="实际出厂量", required=true)
    private BigDecimal actualQuantity;
    @Schema(description="商品id")
    private String productId;
    @Schema(description="商品单位", required=true)
    private String productUnit;
    @Schema(description="发货单号", required=true)
    private String deliverySheetNum;
    @Schema(description="提货单号", required=true)
    private String pickingBillNum;
    @Schema(description="提货时间", required=true)
    private String pickingTime;
    @Schema(description="商品图片", required=true)
    private String productImg;
    @Schema(description="管控商品", required=true)
    private Integer specialFlag;
    @Schema(description="开票二维码", required=true)
    private String qrCode;
    @Schema(description="关闭时间", required=true)
    private String closeTime;
    @Schema(description="关闭原因", required=true)
    private String closeReason;
    @Schema(description="搬运标识")
    private Integer carryFlag;
    @Schema(description="搬运标识")
    private PorterageZoneListDTO porterageZoneListDTO;
}

