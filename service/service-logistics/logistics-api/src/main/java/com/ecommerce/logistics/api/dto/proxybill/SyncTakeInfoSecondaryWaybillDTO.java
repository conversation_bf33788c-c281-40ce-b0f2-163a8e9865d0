
package com.ecommerce.logistics.api.dto.proxybill;

import com.ecommerce.logistics.api.dto.pickingbill.SyncSecondaryProductDTO;
import com.ecommerce.logistics.api.enums.TransportToolTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

@Data
@Schema(name="同步发货单用的二级运单信息")
public class SyncTakeInfoSecondaryWaybillDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = 1933684146995216305L;
    @Schema(description="二级运单ID")
    private String secondaryWaybillId;
    @Schema(description="二级运单号")
    private String secondaryWaybillNum;
    @Schema(description="二级提货单ID")
    private String secondaryPickingBillId;
    @Schema(description="二级发货单号")
    private String secondaryDeliverySheetNum;
    @Schema(description="运输工具类型")
    private String transportToolType = TransportToolTypeEnum.WATER_TRANSPORT.getCode();
    @Schema(description="车辆ID")
    private String vehicleId;
    @Schema(description="车牌号")
    private String vehicleNum;
    @Schema(description="船舶ID")
    private String shippingId;
    @Schema(description="船舶名")
    private String shipName;
    @Schema(description="司机/船长ID")
    private String driverId;
    @Schema(description="司机/船长名称")
    private String driverName;
    @Schema(description="司机/船长电话")
    private String driverPhone;
    @Schema(description="商品详情列表")
    private List<SyncSecondaryProductDTO> secondaryProductDetailList;
}

