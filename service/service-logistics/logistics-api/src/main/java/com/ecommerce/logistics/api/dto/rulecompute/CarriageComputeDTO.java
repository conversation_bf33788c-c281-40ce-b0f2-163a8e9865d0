
package com.ecommerce.logistics.api.dto.rulecompute;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

@Data
@Schema(name="运费计算对象")
public class CarriageComputeDTO
implements Serializable {
    @Schema(description="提货类型", required=true, example="8a2c3cc75efdeqw727015affb0aad5ad")
    private String pickingBillType;
    @Schema(description="规则对象ID", required=true, example="8a2c3cc75efdeqw727015affb0aad5ad")
    private String userId;
    @Schema(description="提货点ID", required=true, example="8a2c3cc75efdeqw727015affb0aad5ad")
    private String warehouseId;
    @Schema(description="收货地址ID", required=true, example="13niw824264inaxb2cxwu9a")
    private String receiveAddressId;
    @Schema(description="收货地址位置", required=true, example="13niw824264inaxb2cxwu9a")
    private String receiveAddressLocation;
    @Schema(description="收货地址省份编码", required=true, example="440000")
    private String provinceCode;
    @Schema(description="收货地址城市编码", required=true, example="441900")
    private String cityCode;
    @Schema(description="收货地址地区编码", required=true, example="")
    private String districtCode;
    @Schema(description="收货地址街道编码", required=true, example="")
    private String streetCode;
    @Schema(description="运输品类数量列表", required=true)
    private List<CategoryQuantityDTO> categoryQuantityList;
    @Schema(description="是否需要创建路书", required=true)
    private Integer saveRoadBook;
}

