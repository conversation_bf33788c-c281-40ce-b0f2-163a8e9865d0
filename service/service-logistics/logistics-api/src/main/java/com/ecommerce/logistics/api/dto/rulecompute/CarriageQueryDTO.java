
package com.ecommerce.logistics.api.dto.rulecompute;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

@Data
@Schema(name="运费查询对象")
public class CarriageQueryDTO
implements Serializable {
    @Schema(description="运输品类ID", required=true, example="15aaurg0ue0tm88a4ts3rma7b")
    private String transportCategoryId;
    @Schema(description="查询区域列表", required=true)
    private List<QueryZoneDTO> queryZoneList;
}

