
package com.ecommerce.logistics.api.dto.rulecompute;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(name="商品运费规则查询对象")
public class CarriageRuleResultDTO {
    @Schema(description="运输品类ID", required=true, example="2as8kcwx9digx166wnr52e67n")
    private String transportCategoryId;
    @Schema(description="商品数量", required=true, example="28.20")
    private BigDecimal productQuantity;
    @Schema(description="运费规则标识", required=true, example="true")
    private Boolean ruleFlag;
}

