
package com.ecommerce.logistics.api.dto.rulecompute;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(name="运输品类数量对象")
public class CategoryQuantityDTO
implements Serializable {
    @Schema(description="运输品类ID", required=true, example="2as8kcwx9digx166wnr52e67n")
    private String transportCategoryId;
    @Schema(description="商品数量", required=true, example="28.20")
    private BigDecimal productQuantity;
}

