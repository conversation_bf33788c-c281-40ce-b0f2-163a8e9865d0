
package com.ecommerce.logistics.api.dto.rulecompute;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

@Data
@Schema(name="运费价格计算对象")
public class ComputeCarriagePriceDTO {
    @Schema(description="结算类型", required=true, example="8a2c3cc75efdeqw727015affb0aad5ad")
    private String settlementType;
    @Schema(description="规则对象ID", required=true, example="8a2c3cc75efdeqw727015affb0aad5ad")
    private String userId;
    @Schema(description="提货点ID", required=true, example="8a2c3cc75efdeqw727015affb0aad5ad")
    private String warehouseId;
    @Schema(description="提货点位置坐标")
    private String warehouseLocation;
    @Schema(description="收货地址ID", required=true, example="13niw824264inaxb2cxwu9a")
    private String receiveAddressId;
    @Schema(description="收货地址位置", required=true, example="13niw824264inaxb2cxwu9a")
    private String receiveAddressLocation;
    @Schema(description="收货地址省份编码", required=true, example="440000")
    private String provinceCode;
    @Schema(description="收货地址城市编码", required=true, example="441900")
    private String cityCode;
    @Schema(description="收货地址地区编码", required=true, example="")
    private String districtCode;
    @Schema(description="收货地址街道编码", required=true, example="")
    private String streetCode;
    @Schema(description="运输品类ID", required=true, example="2as8kcwx9digx166wnr52e67n")
    private String transportCategoryId;
    @Schema(description="结算对象ID", required=true, example="8a2c3cc75efdeqw727015affb0aad5ad")
    private String settlementUserId;
    @Schema(description="车型ID", required=true, example="28.20")
    private String vehicleTypeId;
    @Schema(description="车型轴数")
    private String axles;
    @Schema(description="车厢类型")
    private String carriageType;
    @Schema(description="运输距离", required=true, example="28.20")
    private BigDecimal distance;
    @Schema(description="商品数量", required=true, example="28.20")
    private BigDecimal productQuantity;
    @Schema(description="生效日期", required=true)
    private Date effectiveDate;
}

