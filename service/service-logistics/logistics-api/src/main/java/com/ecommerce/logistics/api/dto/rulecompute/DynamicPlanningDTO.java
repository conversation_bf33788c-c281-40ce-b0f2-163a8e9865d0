
package com.ecommerce.logistics.api.dto.rulecompute;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(name="动态规划对象")
public class DynamicPlanningDTO
implements Comparable<DynamicPlanningDTO> {
    @Schema(description="车型类型Id", required=true, example="28.20")
    private String vehicleTypeId;
    @Schema(description="载重", required=true, example="28.20")
    private BigDecimal loadCapacity;
    @Schema(description="当前路线的车型运费", required=true, example="28.20")
    private BigDecimal vehicleTypeCarriage;

    @Override
    public int compareTo(DynamicPlanningDTO o) {
        return this.getLoadCapacity().compareTo(o.getLoadCapacity());
    }
}

