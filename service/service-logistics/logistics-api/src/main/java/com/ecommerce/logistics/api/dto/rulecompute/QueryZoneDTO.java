
package com.ecommerce.logistics.api.dto.rulecompute;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import lombok.Data;

@Data
@Schema(name="查询区域对象")
public class QueryZoneDTO
implements Serializable {
    @Schema(description="省份", required=true, example="重庆市")
    private String province;
    @Schema(description="省份编码", required=true, example="120100")
    private String provinceCode;
    @Schema(description="城市", required=true, example="重庆市")
    private String city;
    @Schema(description="城市编码", required=true, example="120100")
    private String cityCode;
    @Schema(description="区域", required=true, example="巴南区")
    private String district;
    @Schema(description="区域编码", required=true, example="120122")
    private String districtCode;
    @Schema(description="街道地址", required=true, example="鱼洞街道")
    private String street;
}

