
package com.ecommerce.logistics.api.dto.rulecompute;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(name="中心仓运费映射对象")
public class WarehouseCarriageDTO
implements Serializable {
    @Schema(description="仓库ID", required=true)
    private String warehouseId;
    @Schema(description="运费单价", required=true)
    private BigDecimal carriage;
}

