
package com.ecommerce.logistics.api.dto.rulecompute;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@Schema(name="运单合并对象")
public class WaybillMergeDTO
implements Serializable {
    @Schema(description="运单ID列表", required=true)
    private List<String> waybillIdList;
    @Schema(description="操作人ID", required=true, example="10010")
    @NotBlank(message="操作人ID不能为空")
    private String operationUserId;
    @Schema(description="操作人姓名", required=true, example="王大锤")
    @NotBlank(message="操作人姓名不能为空")
    private String operationUserName;
}

