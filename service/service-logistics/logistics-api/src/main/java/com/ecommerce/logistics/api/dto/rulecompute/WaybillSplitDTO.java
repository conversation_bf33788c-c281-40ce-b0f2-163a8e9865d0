
package com.ecommerce.logistics.api.dto.rulecompute;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Schema(name="运单拆分对象")
public class WaybillSplitDTO
implements Serializable {
    @NotBlank(message="提货单ID不能为空")
    @Schema(description="提货单ID", required=true)
    private String pickingBillId;
    @Schema(description="调度单ID", required=true)
    private String dispatchBillId;
    @NotBlank(message="运输品类ID不能为空")
    @Schema(description="运输品类ID", required=true)
    private String transportCategoryId;
    @NotBlank(message="拆单数量不能为空")
    @Schema(description="拆单数量", required=true)
    private BigDecimal productQuantity;
    @NotBlank(message="拆分运单类型不能为空")
    @Schema(description="运单类型", required=true)
    private String waybillType;
    @Schema(description="外部同步标识", required=true)
    private String externalSyncFlag;
    @Schema(description="操作人ID", required=true)
    private String operationUserId;
    @Schema(description="操作人姓名", required=true)
    private String operationUserName;
}

