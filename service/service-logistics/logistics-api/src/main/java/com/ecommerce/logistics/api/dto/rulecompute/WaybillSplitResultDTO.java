
package com.ecommerce.logistics.api.dto.rulecompute;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
@Schema(name="运单拆分结果对象")
public class WaybillSplitResultDTO {
    @Schema(description="拆分剩余数量", required=true)
    private BigDecimal remainingQuantity;
    @Schema(description="拆分数量列表", required=true)
    private List<BigDecimal> quantityList;
}

