
package com.ecommerce.logistics.api.dto.rulecompute;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(name="区域运费对象")
public class ZoneCarriageDTO
implements Serializable {
    @Schema(description="省份", required=true)
    private String province;
    @Schema(description="省份编码", required=true)
    private String provinceCode;
    @Schema(description="城市", required=true)
    private String city;
    @Schema(description="城市编码", required=true)
    private String cityCode;
    @Schema(description="区域", required=true)
    private String district;
    @Schema(description="区域编码", required=true)
    private String districtCode;
    @Schema(description="街道地址", required=true)
    private String street;
    @Schema(description="每公里运费", required=true)
    private BigDecimal carriage;
    @Schema(description="配送标识 0:不可配 1:可配 ", required=true)
    private String deliveryFlag;
}

