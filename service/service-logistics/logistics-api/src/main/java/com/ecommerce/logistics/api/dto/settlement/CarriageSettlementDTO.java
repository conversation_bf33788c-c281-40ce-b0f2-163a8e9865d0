
package com.ecommerce.logistics.api.dto.settlement;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
@Schema(name="用户运费结算对象")
public class CarriageSettlementDTO {
    @Schema(description="承运商ID", required=true, example="6o3jxtag96v5iqjwmx8stup4g")
    private String carrierId;
    @Schema(description="结算周期", required=false)
    private String settlementMonth;
    @Schema(description="结算运费", required=false)
    private BigDecimal carriage;
    @Schema(description="运单ID列表", required=false)
    private List<String> waybillIdList;
}

