
package com.ecommerce.logistics.api.dto.settlement;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="月度结算查询DTO对象")
public class MonthSettlementListQueryDTO {
    @Schema(description="承运商ID")
    private String carrierId;
    @Schema(description="结算周期开始")
    private String settlementPeriodA;
    @Schema(description="结算周期结束")
    private String settlementPeriodB;
    @Schema(description="结算状态1:未结算 2:已结算")
    private String settlementStatus;
}

