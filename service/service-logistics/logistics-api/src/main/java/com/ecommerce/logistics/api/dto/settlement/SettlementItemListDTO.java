
package com.ecommerce.logistics.api.dto.settlement;

import com.alibaba.fastjson.annotation.JSONField;
import com.ecommerce.logistics.api.config.DecimalSerialize;
import com.ecommerce.logistics.api.dto.waybill.WaybillReceiveAddressDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

@Data
@Schema(name="运费结算明细DTO对象")
public class SettlementItemListDTO {
    @Schema(description="运单ID")
    private String waybillId;
    @Schema(description="运单号")
    private String waybillNum;
    @Schema(description="提货点Id")
    private String warehouseId;
    @Schema(description="提货点名称")
    private String warehouseName;
    @Schema(description="提货点")
    private String warehouseAddress;
    @Schema(description="结算月份")
    private String settlementMonth;
    @Schema(description="司机ID")
    private String driverId;
    @Schema(description="司机名称")
    private String driverName;
    @Schema(description="司机电话")
    private String driverPhone;
    @Schema(description="车牌号")
    private String vehicleNum;
    @Schema(description="运输里程")
    @JSONField(serializeUsing=DecimalSerialize.class)
    private BigDecimal estimateKm;
    @Schema(description="发布运费")
    @JSONField(serializeUsing=DecimalSerialize.class)
    private BigDecimal publishedCarriage;
    @Schema(description="配送数量")
    @JSONField(serializeUsing=DecimalSerialize.class)
    private BigDecimal productQuantity;
    @Schema(description="运输里程")
    @JSONField(serializeUsing=DecimalSerialize.class)
    private BigDecimal actualKm;
    @Schema(description="发布运费")
    @JSONField(serializeUsing=DecimalSerialize.class)
    private BigDecimal actualCarriage;
    @Schema(description="实际出厂量")
    @JSONField(serializeUsing=DecimalSerialize.class)
    private BigDecimal actualQuantity;
    @Schema(description="结算状态")
    private Integer status;
    @Schema(description="完成时间")
    private Date completeTime;
    @Schema(description="出厂时间")
    private Date leaveWarehouseTime;
    @Schema(description="收货地址列表")
    private List<WaybillReceiveAddressDTO> receiveAddressList;
}

