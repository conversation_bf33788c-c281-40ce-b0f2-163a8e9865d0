
package com.ecommerce.logistics.api.dto.settlement;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="运费结算明细查询DTO对象")
public class SettlementItemListQueryDTO {
    @Schema(description="承运商Id", required=true, example="6o3jxtag96v5iqjwmx8stup4g")
    private String carrierId;
    @Schema(description="结算周期", required=true, example="2018-08")
    private String settlementMonth;
    @Schema(description="结算状态", required=true, example="2")
    private Integer settlementStatus;
    @Schema(description="运单号", required=true, example="2")
    private String waybillNum;
}

