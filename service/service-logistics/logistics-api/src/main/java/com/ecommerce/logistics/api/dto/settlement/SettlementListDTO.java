
package com.ecommerce.logistics.api.dto.settlement;

import com.alibaba.fastjson.annotation.JSONField;
import com.ecommerce.logistics.api.config.DecimalSerialize;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(name="运费结算列表DTO对象")
public class SettlementListDTO {
    @Schema(description="结算开始周期")
    private String settlementBeginPeriod;
    @Schema(description="结算结束周期")
    private String settlementEndPeriod;
    @Schema(description="承运商ID")
    private String carrierId;
    @Schema(description="承运商名称")
    private String carrierName;
    @Schema(description="承运商类型")
    private String carrierType;
    @Schema(description="联系电话")
    private String phone;
    @Schema(description="未结算里程")
    @JSONField(serializeUsing=DecimalSerialize.class)
    private BigDecimal unsettlementMileage;
    @Schema(description="已结算里程")
    @JSONField(serializeUsing=DecimalSerialize.class)
    private BigDecimal settlementedMileage;
    @Schema(description="总运输里程")
    @JSONField(serializeUsing=DecimalSerialize.class)
    private BigDecimal totalMileage;
    @Schema(description="未结算重量")
    @JSONField(serializeUsing=DecimalSerialize.class)
    private BigDecimal unsettlementWeight;
    @Schema(description="已结算重量")
    @JSONField(serializeUsing=DecimalSerialize.class)
    private BigDecimal settlementedWeight;
    @Schema(description="总运输重量")
    @JSONField(serializeUsing=DecimalSerialize.class)
    private BigDecimal totalWeight;
    @Schema(description="未结算运费")
    @JSONField(serializeUsing=DecimalSerialize.class)
    private BigDecimal unsettlementCarriage;
    @Schema(description="已结算运费")
    @JSONField(serializeUsing=DecimalSerialize.class)
    private BigDecimal settlementedCarriage;
}

