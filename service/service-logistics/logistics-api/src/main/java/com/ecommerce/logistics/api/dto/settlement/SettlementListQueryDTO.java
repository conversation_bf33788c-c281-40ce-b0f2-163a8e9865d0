
package com.ecommerce.logistics.api.dto.settlement;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="运费结算列表查询DTO对象")
public class SettlementListQueryDTO {
    @Schema(description="承运商ID", required=true, example="030010100")
    private String carrierType;
    @Schema(description="承运商ID", example="6o3jxtag96v5iqjwmx8stup4g")
    private String carrierId;
    @Schema(description="结算周期", example="2018-09")
    private String settlementPeriod;
    @Schema(description="结算状态1:未结算 2:已结算")
    private String settlementStatus;
}

