
package com.ecommerce.logistics.api.dto.settlement;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(name="用户结算金额对象")
public class UserSettlementAmountDTO {
    @Schema(description="未结算金额")
    private BigDecimal unsettlementAmount;
    @Schema(description="已结算金额")
    private BigDecimal settlementedAmount;
    @Schema(description="总金额")
    private BigDecimal totalAmount;
}

