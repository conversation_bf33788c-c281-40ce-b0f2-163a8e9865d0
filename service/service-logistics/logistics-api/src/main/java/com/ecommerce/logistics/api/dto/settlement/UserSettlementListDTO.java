
package com.ecommerce.logistics.api.dto.settlement;

import com.alibaba.fastjson.annotation.JSONField;
import com.ecommerce.logistics.api.config.DecimalSerialize;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

@Data
@Schema(name="用户结算列表对象")
public class UserSettlementListDTO {
    @Schema(description="运单ID")
    private String waybillId;
    @Schema(description="运单号")
    private String waybillNum;
    @Schema(description="完成时间")
    private Date completeTime;
    @Schema(description="提货点名称")
    private String warehouseName;
    @Schema(description="提货点地址")
    private String warehouseAddress;
    @Schema(description="运费")
    @JSONField(serializeUsing=DecimalSerialize.class)
    private BigDecimal carriage;
    @Schema(description="运输里程")
    @JSONField(serializeUsing=DecimalSerialize.class)
    private BigDecimal mileage;
}

