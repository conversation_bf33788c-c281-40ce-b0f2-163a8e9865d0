
package com.ecommerce.logistics.api.dto.shipbill;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="对账单-运单明细信息查询对象")
public class CheckWaybillQueryDTO {
    @Schema(description="托运人ID")
    private String consignorId;
    @Schema(description="承运人ID")
    private String carrierId;
    @Schema(description="司机ID")
    private String driverId;
    @Schema(description="运输方式")
    private String transportType;
    @Schema(description="完成开始时间")
    private String completeTimeStart;
    @Schema(description="完成结束时间")
    private String completeTimeEnd;
}

