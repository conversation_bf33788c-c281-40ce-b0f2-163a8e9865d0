
package com.ecommerce.logistics.api.dto.shipbill;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(name="混凝土运单信息实体")
public class ConcreteShipBillInfoDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = 8118187588081914973L;
    @Schema(description="运单ID")
    private String waybillId;
    @Schema(description="运单号")
    private String waybillNum;
    @Schema(description="是否计算空载费")
    private Byte emptyLoadFlag;
    @Schema(description="实际配送量")
    private BigDecimal actualQuantity;
    @Schema(description="合同ID")
    private String dealsId;
    @Schema(description="发货单号")
    private String takeCode;
    @Schema(description="订单子项ID")
    private String orderItemId;
    @Schema(description="商品ID")
    private String goodsId;
    @Schema(description="签收类型")
    private String signType;
}

