
package com.ecommerce.logistics.api.dto.shipbill;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import lombok.Data;

@Data
@Schema(name="指派取消运单实体")
public class ReassignShipBillDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = 1650116000357729080L;
    @Schema(description="运单ID")
    private String waybillId;
    @Schema(description="车辆ID", required=false)
    private String vehicleId;
    @Schema(description="车牌号码", required=false)
    private String number;
    @Schema(description="船只ID")
    private String shippingId;
    @Schema(description="船舶编号")
    private String shippingNo;
    @Schema(description="船舶名称")
    private String shippingName;
    @Schema(description="船长ID")
    private String driverId = "";
    @Schema(description="船长姓名")
    private String driverName;
    @Schema(description="船长联系电话")
    private String driverPhone;
    @Schema(description="操作人ID")
    private String operateUserId;
    @Schema(description="操作人名")
    private String operateUserName;
}

