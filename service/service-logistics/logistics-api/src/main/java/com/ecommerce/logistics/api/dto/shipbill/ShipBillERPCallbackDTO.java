
package com.ecommerce.logistics.api.dto.shipbill;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="船运计划信息下发反馈DTO")
public class ShipBillERPCallbackDTO {
    @Schema(description="电商船运单号")
    private String ecBillCode;
    @Schema(description="外部船运计划ID-自提：船运委托函ID；配送：船运计划ID")
    private String externalBillId;
    @Schema(description="外部提货计划号-自提用，提货计划号")
    private String externalPlanCode;
}

