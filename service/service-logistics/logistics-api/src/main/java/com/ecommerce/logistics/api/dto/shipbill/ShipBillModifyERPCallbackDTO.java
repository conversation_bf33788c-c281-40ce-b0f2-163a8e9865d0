
package com.ecommerce.logistics.api.dto.shipbill;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="船运计划信息修改处理结果反馈DTO")
public class ShipBillModifyERPCallbackDTO {
    @Schema(description="电商船运单号")
    private String ecBillCode;
    @Schema(description="外部船运计划ID-自提：船运委托函ID；配送：船运计划ID")
    private String externalBillId;
    @Schema(description="外部计划状态-ERP船运计划的当前状态，修改失败时返回")
    private String externalBillStatus;
}

