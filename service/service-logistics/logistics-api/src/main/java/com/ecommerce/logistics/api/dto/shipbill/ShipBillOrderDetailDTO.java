
package com.ecommerce.logistics.api.dto.shipbill;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(name="运单列表查询DTO")
public class ShipBillOrderDetailDTO {
    @Schema(description="运单子项ID")
    private String waybillItemId;
    @Schema(description="运单ID")
    private String waybillId;
    @Schema(description="运单号")
    private String waybillNum;
    @Schema(description="商品ID")
    private String goodsId;
    @Schema(description="商品名称")
    private String goodsName;
    @Schema(description="商品图片")
    private String goodsImg;
    @Schema(description="单位")
    private String unit;
    @Schema(description="出站时间")
    private String leaveWarehouseTime;
    @Schema(description="实际出厂量")
    private BigDecimal actualQuantity;
    @Schema(description="物流提货时间")
    private String logisticsTakeTime;
    @Schema(description="物流提货量")
    private BigDecimal logisticsTakeQuantity;
    @Schema(description="商品单价")
    private BigDecimal goodsUnitPrice;
    @Schema(description="下单单价")
    private BigDecimal orderPrice;
    @Schema(description="出厂单价")
    private BigDecimal originPrice;
    @Schema(description="最新单价")
    private BigDecimal newstPrice;
    @Schema(description="物流单价")
    private BigDecimal logisticsUnitPrice;
    @Schema(description="运力名称")
    private String transportToolName;
    @Schema(description="运力ID")
    private String transportToolId;
    @Schema(description="司机ID")
    private String driverId;
    @Schema(description="司机电话")
    private String driverPhone;
    @Schema(description="司机名称")
    private String driverName;
    @Schema(description="船舶ID")
    private String shippingId;
    @Schema(description="船舶名称")
    private String shippingName;
    @Schema(description="船舶编号")
    private String shippingNo;
}

