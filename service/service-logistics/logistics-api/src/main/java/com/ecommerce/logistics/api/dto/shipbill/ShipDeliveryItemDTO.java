
package com.ecommerce.logistics.api.dto.shipbill;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

@Data
@Schema(name="船运运单交易明细对象")
public class ShipDeliveryItemDTO {
    @Schema(description="子运单ID")
    private String parentId;
    @Schema(description="子运单ID")
    private String waybillId;
    @Schema(description="子运单编号")
    private String waybillNum;
    @Schema(description="二维码")
    private String qrCode;
    @Schema(description="计划提货量")
    private BigDecimal totalQuantity;
    @Schema(description="实际出厂量")
    private BigDecimal actualQuantity;
    @Schema(description="货物所有人")
    private String ownerId;
    @Schema(description="外部运单编号")
    private String externalWaybillNum;
    @Schema(description="外部运单状态")
    private String externalWaybillStatus;
    @Schema(description="运单子项ID")
    private String waybillItemId;
    @Schema(description="委托单Id")
    private String deliveryBillId;
    @Schema(description="委托单号")
    private String deliveryBillNum;
    @Schema(description="运单子项类型")
    private String itemType;
    @Schema(description="运单子项交易状态")
    private String itemStatus;
    @Schema(description="创建时间")
    private Date createTime;
    @Schema(description="是否可操作")
    private Byte canOperate;
    @Schema(description="买家Id")
    private String buyerId;
    @Schema(description="买家名称")
    private String buyerName;
    @Schema(description="卖家Id")
    private String sellerId;
    @Schema(description="卖家名称")
    private String sellerName;
    @Schema(description="商品Id")
    private String goodsId;
    @Schema(description="商品名称")
    private String goodsName;
    @Schema(description="运输品类ID")
    private String transportCategoryId;
    @Schema(description="运输品类名称")
    private String transportCategoryName;
    @Schema(description="商品单位")
    private String unit;
    @Schema(description="收货地址Id")
    private String receiveAddressId;
    @Schema(description="收货人名称")
    private String receiver;
    @Schema(description="收货人电话")
    private String receiverPhone;
    @Schema(description="收货地址省份")
    private String receiveProvince;
    @Schema(description="收货地址省份编码")
    private String receiveProvinceCode;
    @Schema(description="收货地址城市")
    private String receiveCity;
    @Schema(description="收货地址城市编码")
    private String receiveCityCode;
    @Schema(description="收货地址区域")
    private String receiveDistrict;
    @Schema(description="收货地址区域编码")
    private String receiveDistrictCode;
    @Schema(description="收货地址街道")
    private String receiveStreet;
    @Schema(description="收货地址街道编码")
    private String receiveStreetCode;
    @Schema(description="收货地址详情")
    private String receiveAddress;
    @Schema(description="收货地址坐标")
    private String receiveLocation;
    @Schema(description="配送时间")
    private String deliveryTimeStr;
    @Schema(description="配送日期(期望)")
    private Date deliveryTime;
    @Schema(description="配送时间区间(期望)")
    private String deliveryTimeRange;
    @Schema(description="签收类型")
    private String signType;
    @Schema(description="同步标识")
    private String syncFlag;
    @Schema(description="是否需要监控")
    private Byte needMonitor;
    @Schema(description="单据背靠背层级")
    private String billProxyType;
    @Schema(description="运输工具类型")
    private String transportToolType;
    @Schema(description="送达时间")
    private String arriveDestinationTimeStr;
    @Schema(description="运力名称")
    private String transportToolName;
}

