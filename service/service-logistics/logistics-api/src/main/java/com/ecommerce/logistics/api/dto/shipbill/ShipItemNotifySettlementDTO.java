
package com.ecommerce.logistics.api.dto.shipbill;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import lombok.Data;

@Data
@Schema(name="运单子项结算通知处理实体")
public class ShipItemNotifySettlementDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = -51630963523932214L;
    @Schema(description="运单号")
    private String waybillNum;
    @Schema(description="运单子项ID")
    private String waybillItemId;
    @Schema(description="运输工具类型")
    private String transportToolType;
    @Schema(description="中心仓标识 1：是", required=true)
    private Integer centralWarehouseFlag;
}

