
package com.ecommerce.logistics.api.dto.shipping;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@Schema(name="一艘船授权批量卖家DTO对象")
public class AuthorizeSellerBatchDTO {
    @NotBlank(message="授权船舶ID不能为空")
    @Schema(description="授权船舶ID", required=true)
    private String shippingId;
    @NotNull(message="授权卖家对象不能为空")
    @Schema(description="授权卖家", required=true)
    private @NotNull(message="授权卖家对象不能为空") List<SellerDTO> sellerList;
    @Schema(description="操作人ID", example="askdoamckas")
    private String operatorId;
    @Schema(description="操作人名称", example="李四")
    private String operatorName;
}

