
package com.ecommerce.logistics.api.dto.shipping;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="卖家DTO对象")
public class AuthorizeSellerDTO {
    @Schema(description="主键ID", required=true)
    private String authorizeSellerId;
    @Schema(description="授权卖家ID", required=true)
    private String authorizeUserId;
    @Schema(description="授权卖家名称", example="dsfgdsws")
    private String authorizeUserName;
    @Schema(description="服务类型,0-不可用，1-固定，2-临时", required=false, example="0-不可用，1-固定，2-临时")
    private Integer serviceType;
    @Schema(description="授权状态,0-未授权，1-已授权", required=false, example="0-未授权，1-已授权")
    private Integer authorizeStatus;
}

