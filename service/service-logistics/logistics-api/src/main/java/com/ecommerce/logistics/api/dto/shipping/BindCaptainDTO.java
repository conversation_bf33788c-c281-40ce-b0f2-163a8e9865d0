
package com.ecommerce.logistics.api.dto.shipping;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

@Data
@Schema(name="绑定船长DTO对象")
public class BindCaptainDTO {
    @NotBlank(message="船舶ID不能为空")
    @Schema(description="船舶ID", required=true, example="sdfgds")
    private String shippingId;
    @NotBlank(message="船长ID不能为空")
    @Schema(description="船长ID", required=true, example="dfssdf")
    private String captainAccountId;
    @NotBlank(message="船长账号不能为空")
    @Schema(description="船长账号", required=true, example="dfssdf")
    private String captainAccountName;
    @NotBlank(message="船长名称不能为空")
    @Schema(description="船长名称", required=true, example="幅度达到")
    private String captainName;
    @NotBlank(message="船长联系方式不能为空")
    @Schema(description="船长联系方式", required=true, example="sdfgds")
    private String captainPhone;
    @Schema(description="操作人ID", example="10010")
    private String operatorId;
    @Schema(description="操作人姓名", example="王大锤")
    private String operatorName;
}

