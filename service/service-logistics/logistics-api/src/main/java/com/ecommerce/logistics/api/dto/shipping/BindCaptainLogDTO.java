
package com.ecommerce.logistics.api.dto.shipping;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;
import lombok.Data;

@Data
@Schema(name="绑定船长记录DTO对象")
public class BindCaptainLogDTO {
    @Schema(description="绑定船长记录主键", required=true, example="sdfgds")
    private String bindCaptainId;
    @Schema(description="船舶主键ID", required=true, example="gfhgfsgjgh")
    private String shippingId;
    @Schema(description="船长账号", required=true, example="xcvasds")
    private String captainAccountName;
    @Schema(description="船长姓名", required=true, example="杰克")
    private String captainName;
    @Schema(description="船长联系电话", required=true, example="***********")
    private String captainPhone;
    @Schema(description="船舶名称", required=true, example="乘风破浪号")
    private String shippingName;
    @Schema(description="船舶承运商名称", required=true, example="船舶承运商名称")
    private String managerMemberName;
    @Schema(description="类型,0-解绑，1-绑定", required=true, example="0")
    private Integer type;
    @Schema(description="创建用户")
    private String createUser;
    @Schema(description="创建时间")
    private Date createTime;
}

