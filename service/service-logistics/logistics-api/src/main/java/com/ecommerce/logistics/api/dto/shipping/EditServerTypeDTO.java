
package com.ecommerce.logistics.api.dto.shipping;

import io.swagger.v3.oas.annotations.media.Schema;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@Schema(name="修改服务类型DTO对象")
public class EditServerTypeDTO {
    @Schema(description="授权卖家ID", required=true, example="sdfgds")
    private String authorizeUserId;
    @NotBlank(message="船舶ID不能为空")
    @Schema(description="船舶ID", required=true, example="sdfgds")
    private String shippingId;
    @NotNull(message="服务类型不能为空")
    @Schema(description="服务类型,0-不可用，1-固定，2-临时", required=true, example="sdfgds")
    private @NotNull(message="服务类型不能为空") Integer serviceType;
    @Schema(description="操作人ID", example="10010")
    private String operatorId;
    @Schema(description="操作人姓名", example="王大锤")
    private String operatorName;
}

