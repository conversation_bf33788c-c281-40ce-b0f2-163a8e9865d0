
package com.ecommerce.logistics.api.dto.shipping;

import io.swagger.v3.oas.annotations.media.Schema;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
@Schema(name="卖家DTO对象")
public class SellerDTO {
    @NotBlank(message="授权卖家ID不能为空")
    @Schema(description="授权卖家ID", required=true)
    private @NotBlank(message="授权卖家ID不能为空") String authorizeUserId;
    @NotBlank(message="授权卖家名称不能为空")
    @Schema(description="授权卖家名称", example="dsfgdsws")
    private @NotBlank(message="授权卖家名称不能为空") String authorizeUserName;
}

