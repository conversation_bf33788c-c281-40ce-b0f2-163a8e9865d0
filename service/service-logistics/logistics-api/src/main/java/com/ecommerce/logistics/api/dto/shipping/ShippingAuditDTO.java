
package com.ecommerce.logistics.api.dto.shipping;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="船舶审核对象")
public class ShippingAuditDTO {
    @Schema(description="船舶ID", required=false, example="askdoamckasxxxx")
    private String shippingId;
    @Schema(description="是否通过审核", required=false, example="1")
    private Integer isPassAudit;
    @Schema(description="审核失败原因", required=false, example="虚假信息")
    private String failReason;
    @Schema(description="操作人ID", required=true, example="10010")
    private String operatorUserId;
    @Schema(description="操作人姓名", required=true, example="王大锤")
    private String operatorUserName;
}

