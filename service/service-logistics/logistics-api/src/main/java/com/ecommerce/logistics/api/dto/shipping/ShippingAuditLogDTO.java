
package com.ecommerce.logistics.api.dto.shipping;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;
import lombok.Data;

@Data
@Schema(name="船舶审核记录DTO对象")
public class ShippingAuditLogDTO {
    @Schema(description="审核记录主键ID", example="askdoamckasxxxx")
    private Long shippingAuditId;
    @Schema(description="表船舶ID", example="askdoamckasxxxx")
    private String shippingId;
    @Schema(description="审核状态", example="驳回")
    private int auditStatus;
    @Schema(description="驳回原因", example="信息不完整")
    private String rejectionReasons;
    @Schema(description="审核人名称", example="李四")
    private String createUserName;
    @Schema(description="操作类型", example="同意")
    private String operationType;
    @Schema(description="审核时间", example="2020-11-20 10:12:21")
    private Date createTime;
}

