
package com.ecommerce.logistics.api.dto.shipping;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@Schema(name="批量授权DTO对象")
public class ShippingBatchAuthorizeDTO {
    @NotNull(message="授权船舶ID集合不能为空")
    @Schema(description="授权船舶ID集合", required=true)
    private @NotNull(message="授权船舶ID集合不能为空") List<String> shippingIdList;
    @NotNull(message="授权卖家ID不能为空")
    @Schema(description="授权卖家ID", required=true)
    private @NotNull(message="授权卖家ID不能为空") String authorizeUserId;
    @Schema(description="卖家名称", example="dsfgdsws")
    private String authorizeUserName;
    @Schema(description="操作人ID", example="askdoamckas")
    private String operatorId;
    @Schema(description="操作人名称", example="李四")
    private String operatorName;
}

