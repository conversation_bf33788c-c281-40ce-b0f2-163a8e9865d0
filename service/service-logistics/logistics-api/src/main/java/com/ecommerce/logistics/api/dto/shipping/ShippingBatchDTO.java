
package com.ecommerce.logistics.api.dto.shipping;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;
import lombok.Data;

@Data
@Schema(name="船舶批量操作DTO对象")
public class ShippingBatchDTO {
    @Schema(description="船舶ID集合", example="gfdsads")
    private List<String> shippingIds;
    @Schema(description="经纪人ID", example="wefgdsavcx")
    private String managerMemberId;
    @Schema(description="操作人ID", example="askdoamckas")
    private String operatorId;
    @Schema(description="操作人名称", example="李四")
    private String operatorName;
    private String authorizeUserId;
    @Schema(description="服务类型,0-不可用，1-固定，2-临时", example="2")
    private Integer serviceType;
}

