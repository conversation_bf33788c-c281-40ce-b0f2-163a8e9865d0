
package com.ecommerce.logistics.api.dto.shipping;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="船舶删除DTO对象")
public class ShippingDeleteDTO {
    @Schema(description="船舶ID", required=false, example="askdoamckasxxxx")
    private String shippingId;
    @Schema(description="操作人ID", required=true, example="10010")
    private String operatorUserId;
    @Schema(description="操作人姓名", required=true, example="王大锤")
    private String operatorUserName;
}

