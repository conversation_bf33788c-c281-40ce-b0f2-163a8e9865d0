
package com.ecommerce.logistics.api.dto.shipping;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(name="船舶详情DTO对象")
public class ShippingDetailDTO {
    @Schema(description="船舶ID", required=false, example="askdoamckasxxxx")
    private String shippingId;
    @Schema(description="归属用户id", required=false, example="askdoamckasxxxx")
    private String userId;
    @Schema(description="归属用户名称", required=false, example="askdoamckasxxxx")
    private String userName;
    @Schema(description="用户类型", required=false, example="askdoamckasxxxx")
    private Integer userType;
    @Schema(description="船舶编码", required=false, example="askdoamckasxxxx")
    private String number;
    @Schema(description="船舶类型", required=false, example="askdoamckasxxxx")
    private String shippingType;
    @Schema(description="船舶名称", required=false, example="askdoamckasxxxx")
    private String shippingName;
    @Schema(description="运输品类id", required=false, example="askdoamckasxxxx")
    private String transportCategoryId;
    @Schema(description="载重", required=false, example="askdoamckasxxxx")
    private BigDecimal loadCapacity;
    @Schema(description="排水量", required=false, example="askdoamckasxxxx")
    private BigDecimal displacement;
    @Schema(description="船舶全长", required=false, example="askdoamckasxxxx")
    private BigDecimal overallLength;
    @Schema(description="最大宽度", required=false, example="askdoamckasxxxx")
    private BigDecimal extremeWidth;
    @Schema(description="最大高度", required=false, example="askdoamckasxxxx")
    private BigDecimal extremeHeight;
    @Schema(description="认证状态", required=false, example="askdoamckasxxxx")
    private String certificationStatus;
    @Schema(description="认证失败原因", required=false, example="askdoamckasxxxx")
    private String failReason;
    @Schema(description="默认船长id", required=false, example="askdoamckasxxxx")
    private String bindDriverId;
    @Schema(description="船长名称", required=false, example="askdoamckasxxxx")
    private String driverName;
    @Schema(description="船长电话", required=false, example="askdoamckasxxxx")
    private String driverPhone;
}

