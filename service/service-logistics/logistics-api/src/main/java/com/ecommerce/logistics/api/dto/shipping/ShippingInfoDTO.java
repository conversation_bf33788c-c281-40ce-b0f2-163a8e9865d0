
package com.ecommerce.logistics.api.dto.shipping;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.Date;
import jakarta.persistence.Column;
import lombok.Data;

@Data
@Schema(name="船舶DTO对象")
public class ShippingInfoDTO {
    @Schema(description="船舶ID")
    private String shippingId;
    @Schema(description="船舶编号")
    private String shippingNo;
    @Schema(description="船舶名称")
    private String shippingName;
    @Schema(description="经纪人类型,10-企业承运商，11-个体船东承运商，20-卖家，30-买家")
    private String managerMemberType;
    @Schema(description="经纪人ID，即添加这艘船的用户，对该船拥有管理授权权限")
    private String managerMemberId;
    @Schema(description="经纪人名称")
    private String managerMemberName;
    @Schema(description="船主账号Id")
    private String belongerId;
    @Schema(description="船主名称")
    private String belongerName;
    @Schema(description="船主手机号码")
    private String belongerPhone;
    @Schema(description="船舶类型")
    private String shippingType;
    @Schema(description="船务公司")
    private String shippingCompany;
    @Schema(description="所有权归属")
    private String ownership;
    @Schema(description="起运港ID，多个时以“，”隔开")
    private String departureWharfId;
    @Schema(description="目的地港ID，多个时以“，”隔开")
    private String destinationWharfId;
    @Schema(description="A级核载吨位(吨)")
    private BigDecimal alevelPayload;
    @Schema(description="B级核载吨位(吨)", required=false, example="45.00")
    private BigDecimal blevelPayload;
    @Schema(description="净重(吨)")
    private BigDecimal selfPayload;
    @Schema(description="总吨位(吨)")
    private BigDecimal totalPayload;
    @Column(name="total_length")
    @Schema(description="总长度(米)")
    private BigDecimal totalLength;
    @Column(name="max_width")
    @Schema(description="最大宽度(米)")
    private BigDecimal maxWidth;
    @Schema(description="船舶的型深(米)")
    private BigDecimal mouldedDepth;
    @Schema(description="满载吃水(米)")
    private BigDecimal fullDraft;
    @Schema(description="空载吃水(米)")
    private BigDecimal emptyDraft;
    @Schema(description="是否支持管控，0-不管控，1-管控")
    private Integer whetherControl;
    @Schema(description="是否绑定GPS终端，0-无，1-有")
    private Integer gpsDevice;
    @Schema(description="GPS设备号")
    private String gpsDeviceNumber;
    @Schema(description="船主账号Id")
    private String captainId;
    @Schema(description="船长姓名，冗余字段，方便展示", required=false, example="发的xxxx")
    private String captainName;
    @Schema(description="船长手机号码，冗余字段，方便展示", required=false, example="32456786xxxx")
    private String captainPhone;
    @Schema(description="船舶状态,1-吉船,2-待装货,3-重船,4-检修中,5-已禁用", required=false, example="1-吉船,2-待装货,3-重船,4-检修中,5-已禁用")
    private Integer shippingStatus;
    @Schema(description="审核状态,1-待提交,2-待审核,3-已通过,4-已驳回,5-更新中,6-更新失败（已审核更新信息被驳回）", required=false, example="1-待提交,2-待审核,3-已通过,4-已驳回,5-更新中")
    private Integer auditStatus;
    @Schema(description="服务类型,0-不可用，1-固定，2-临时", required=false, example="0-不可用，1-固定，2-临时")
    private Integer serviceType;
    @Schema(description="授权状态,0-未授权，1-已授权", required=false, example="0-未授权，1-已授权")
    private Integer authorizeStatus;
    @Schema(description="驳回原因", required=false, example="开心")
    private String rejectionReasons;
    @Schema(description="授权数量，大于0说明有授权卖家", required=false, example="2")
    private String authorizeTotal;
    @Schema(description="创建用户")
    private String createUser;
    @Schema(description="更新用户")
    private String updateUser;
    @Schema(description="创建时间")
    private Date createTime;
    @Schema(description="更新时间")
    private Date updateTime;
}

