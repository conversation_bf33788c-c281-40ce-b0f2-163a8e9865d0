
package com.ecommerce.logistics.api.dto.shipping;

import com.ecommerce.logistics.api.dto.attachment.AttListDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
@Schema(name="船舶详情DTO对象")
public class ShippingInfoDetailsDTO {
    @Schema(description="船舶ID", example="askdoamckasxxxx")
    private String shippingId;
    @Schema(description="船舶编号", example="askdoamckasxxxx")
    private String shippingNo;
    @Schema(description="船舶名称，需保证未删除的数据名称唯一", example="askdoamckasxxxx")
    private String shippingName;
    @Schema(description="经纪人类型,10-企业承运商，11-个体船东承运商，20-卖家，30-买家", example="askdoamckasxxxx")
    private String managerMemberType;
    @Schema(description="经纪人ID，即添加这艘船的用户，对该船拥有管理授权权限", example="askdoamckasxxxx")
    private String managerMemberId;
    @Schema(description="经纪人名称", example="askdoamckasxxxx")
    private String managerMemberName;
    @Schema(description="船舶类型", example="askdoamckasxxxx")
    private String shippingType;
    @Schema(description="船舶所有人名称", example="askdoamckasxxxx")
    private String belongerName;
    @Schema(description="船舶所有人手机号码", example="askdoamckasxxxx")
    private String belongerPhone;
    @Schema(description="船务公司", example="askdoamckasxxxx")
    private String shippingCompany;
    @Schema(description="所有权归属", example="askdoamckasxxxx")
    private String ownership;
    @Schema(description="起运港,省份，城市code")
    private List<List<String>> departureWharf;
    @Schema(description="起运港ID，多个时以“，”隔开", example="askdoamckasxxxx")
    private String departureWharfId;
    @Schema(description="目的地港,省份，城市code")
    private List<List<String>> destinationWharf;
    @Schema(description="目的地港ID，多个时以“，”隔开", example="askdoamckasxxxx")
    private String destinationWharfId;
    @Schema(description="A级核载吨位(吨)", example="askdoamckasxxxx")
    private BigDecimal alevelPayload;
    @Schema(description="B级核载吨位(吨)", example="askdoamckasxxxx")
    private BigDecimal blevelPayload;
    @Schema(description="净重(吨)", example="askdoamckasxxxx")
    private BigDecimal selfPayload;
    @Schema(description="总吨位(吨)", example="askdoamckasxxxx")
    private BigDecimal totalPayload;
    @Schema(description="总长度(米)", example="askdoamckasxxxx")
    private BigDecimal totalLength;
    @Schema(description="最大宽度(米)", example="askdoamckasxxxx")
    private BigDecimal maxWidth;
    @Schema(description="船舶的型深(米)", example="askdoamckasxxxx")
    private BigDecimal mouldedDepth;
    @Schema(description="满载吃水(米)", example="askdoamckasxxxx")
    private BigDecimal fullDraft;
    @Schema(description="空载吃水(米)", example="askdoamckasxxxx")
    private BigDecimal emptyDraft;
    @Schema(description="最大装载量(吨)", example="12.32")
    private BigDecimal maxLoadingCapacity;
    @Schema(description="最大船高(米)", example="2.32")
    private BigDecimal maxHeight;
    @Schema(description="是否支持管控，0-不管控，1-管控", example="1")
    private Integer whetherControl;
    @Schema(description="是否绑定GPS终端，0-无，1-有", example="0")
    private Integer gpsDevice;
    @Schema(description="GPS设备号", example="68464514")
    private String gpsDeviceNumber;
    @Schema(description="船长姓名，冗余字段，方便展示", example="askdoamckasxxxx")
    private String captainName;
    @Schema(description="船长手机号码，冗余字段，方便展示", example="askdoamckasxxxx")
    private String captainPhone;
    @Schema(description="船舶状态,1-吉船,2-待装货,3-重船,4-检修中,5-已禁用", example="askdoamckasxxxx")
    private Integer shippingStatus;
    @Schema(description="审核状态,1-待提交,2-待审核,3-已通过,4-已驳回,5-更新中", example="askdoamckasxxxx")
    private Integer auditStatus;
    @Schema(description="驳回原因", example="askdoamckasxxxx")
    private String rejectionReasons;
    @Schema(description="船舶审核记录集合", example="askdoamckasxxxx")
    private List<ShippingAuditLogDTO> shippingAuditLogDTOList;
    @Schema(description="船舶证书文件对象")
    private List<AttListDTO> attListDTOS;
    @Schema(description="绑定船长记录集合")
    private List<BindCaptainLogDTO> bindCaptainLogList;
}

