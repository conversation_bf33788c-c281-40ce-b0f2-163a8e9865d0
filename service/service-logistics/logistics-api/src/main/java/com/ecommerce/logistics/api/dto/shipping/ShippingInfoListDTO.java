
package com.ecommerce.logistics.api.dto.shipping;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

@Data
@Schema(name="船舶列表DTO对象")
public class ShippingInfoListDTO {
    @Schema(description="船舶ID", required=false, example="askdoamckasxxxx")
    private String shippingId;
    @Schema(description="船舶编号", required=false, example="32456786xxxx")
    private String shippingNo;
    @Schema(description="船舶名称", required=false, example="的健康xxxx")
    private String shippingName;
    @Schema(description="经纪人ID，即添加这艘船的用户，对该船拥有管理授权权限", required=false, example="32456786xxxx")
    private String managerMemberId;
    @Schema(description="经纪人类型", required=false, example="11")
    private String managerMemberType;
    @Schema(description="经纪人名称", required=false, example="浮动空间xxxx")
    private String managerMemberName;
    @Schema(description="船舶类型", required=false, example="32456786xxxx")
    private String shippingType;
    @Schema(description="船务公司", required=false, example="反倒是公司")
    private String shippingCompany;
    @Schema(description="所有权归属", required=false, example="点三公分")
    private String ownership;
    @Schema(description="A级核载吨位(吨)", required=false, example="45.00")
    private BigDecimal alevelPayload;
    @Schema(description="B级核载吨位(吨)", required=false, example="45.00")
    private BigDecimal blevelPayload;
    @Schema(description="船主账号Id")
    private String captainId;
    @Schema(description="船长姓名，冗余字段，方便展示", required=false, example="发的xxxx")
    private String captainName;
    @Schema(description="船长手机号码，冗余字段，方便展示", required=false, example="32456786xxxx")
    private String captainPhone;
    @Schema(description="船舶状态,1-吉船,2-待装货,3-重船,4-检修中,5-已禁用", required=false, example="1-吉船,2-待装货,3-重船,4-检修中,5-已禁用")
    private Integer shippingStatus;
    @Schema(description="审核状态,1-待提交,2-待审核,3-已通过,4-已驳回,5-更新中,6-更新失败（已审核更新信息被驳回）", required=false, example="1-待提交,2-待审核,3-已通过,4-已驳回,5-更新中")
    private Integer auditStatus;
    @Schema(description="服务类型,0-不可用，1-固定，2-临时", required=false, example="0-不可用，1-固定，2-临时")
    private Integer serviceType;
    @Schema(description="授权状态,0-未授权，1-已授权", required=false, example="0-未授权，1-已授权")
    private Integer authorizeStatus;
    @Schema(description="驳回原因", required=false, example="开心")
    private String rejectionReasons;
    @Schema(description="授权数量，大于0说明有授权卖家", required=false, example="2")
    private Integer authorizeTotal;
    @Schema(description="承运商名称", required=false, example="发的")
    private String carrierName;
    private Date updateTime;
    @Schema(description="最大装载量(吨)")
    private BigDecimal maxLoadingCapacity;
    @Schema(description="ERP承运商编号", required=false, example="456465")
    private String erpCarrierNo;
    @Schema(description="ERP承运商名称", required=false, example="地方发的")
    private String erpCarrierName;
    @Schema(description="ERP船舶编码", required=false, example="dsdfg")
    private String erpShippingCode;
    @Schema(description="ERP船舶名称", required=false, example="多少个发的")
    private String erpShippingName;
    @Schema(description="绑定数量，大于0说明已绑定过", required=false, example="1")
    private Integer bindTotal;
}

