
package com.ecommerce.logistics.api.dto.shipping;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
@Schema(name="船舶查询DTO对象")
public class ShippingInfoListQueryDTO {
    @Schema(description="船舶编号", example="辽宁舰")
    private String shippingNo;
    @Schema(description="船务公司", example="xxx公司")
    private String shippingCompany;
    @Schema(description="船舶类型", example="0567850")
    private String shippingType;
    @Schema(description="船舶名称", example="长江一号")
    private String shippingName;
    @Schema(description="所有权归属", example="fgfdsadf")
    private String ownership;
    @Schema(description="最小B级核载吨位(吨)", example="1")
    private BigDecimal blevelPayloadMin;
    @Schema(description="最大B级核载吨位(吨)", example="1000")
    private BigDecimal blevelPayloadMax;
    @Schema(description="审核状态,1-待提交,2-待审核,3-已通过,4-已驳回,5-更新中", example="1")
    private Integer auditStatus;
    @Schema(description="当前登录用户所属会员ID", example="fd1sgfg")
    private String memberId;
    @Schema(description="经纪人类型", example="fd1sgfg")
    private String managerMemberType;
    @Schema(description="船舶状态,1-吉船,2-待装货,3-重船,4-检修中,5-已禁用", required=false, example="1-吉船,2-待装货,3-重船,4-检修中,5-已禁用")
    private Integer shippingStatus;
    @Schema(description="是否筛掉未绑定船长的船舶")
    private Boolean hasCaptain = false;
    @Schema(description="审核状态集合")
    private List<Integer> auditStatusList;
    @Schema(description="是否为ERP船舶，0-非erp船舶，1-erp船舶")
    private int erpValue;
    @Schema(description="厂商ID", example="fd1sgfg")
    private String manufacturerId;
    @Schema(description="是否绑定了船长0:否,1:是")
    private Integer hasBindCaptain;
}