
package com.ecommerce.logistics.api.dto.shipping;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="船舶查询DTO对象")
public class ShippingInfoQueryDTO {
    @Schema(description="当前登录用户所属会员ID", example="fd1sgfg")
    private String memberId;
    @Schema(description="归属人类型", example="fd1sgfg")
    private int userRole;
    @Schema(description="经纪人类型", example="fd1sgfg")
    private String managerMemberType;
    @Schema(description="船舶状态,1-吉船,2-待装货,3-重船,4-检修中,5-已禁用", example="fd1sgfg")
    private int shippingStatus;
    @Schema(description="船舶名称 like")
    private String shippingName;
    @Schema(description="承运商ID", example="fd1sgfg")
    private String carrierId;
}

