
package com.ecommerce.logistics.api.dto.shipping;

import com.ecommerce.logistics.api.dto.attachment.AttListDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;


import java.math.BigDecimal;
import java.util.List;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

@Data
@Schema(name="船舶保存DTO对象")
public class ShippingInfoSaveDTO {
    @Schema(description="保存类型,1-新增保存，2-更改保存", example="1")
    private Integer saveType;
    @Schema(description="提交类型,1-新增提交，2-更改提交,3-更新已审核提交", example="1")
    private Integer submitType;
    @NotNull(message="船舶证书文件对象不能为空")
    @Schema(description="船舶证书文件对象")
    private @NotNull(message="船舶证书文件对象不能为空") List<AttListDTO> attListDTOS;
    @Schema(description="主键ID", example="df1gf")
    private String shippingId;
    @Schema(description="船舶编号", example="432123456")
    private String shippingNo;
    @NotBlank(message="船舶名称不能为空")
    @Schema(description="船舶名称，需保证未删除的数据名称唯一", example="发热三个发")
    private String shippingName;
    @Schema(description="经纪人类型,10-企业承运商，11-个体船东承运商，20-卖家，30-买家", example="11")
    private String managerMemberType;
    @Schema(description="经纪人ID，即添加这艘船的用户，对该船拥有管理授权权限", example="df1gf")
    private String managerMemberId;
    @Schema(description="经纪人名称", example="第三方的")
    private String managerMemberName;
    @NotBlank(message="船舶类型不能为空")
    @Schema(description="船舶类型", example="09230")
    private String shippingType;
    @Schema(description="船长ID", example="dfssdf")
    private String captainAccountId;
    @Schema(description="船长姓名", example="dfssdf")
    private String captainName;
    @Schema(description="船长电话", example="dfssdf")
    private String captainPhone;
    @NotBlank(message="船舶所有人不能为空")
    @Schema(description="船舶所有人名称", example="啊的空间")
    private String belongerName;
    @NotBlank(message="船舶所有人联系方式不能为空")
    @Length(min=11, max=12, message="手机号码只能为11或12位")
    @Schema(description="船舶所有人手机号码", example="*********")
    private String belongerPhone;
    @Schema(description="船务公司", example="xx公司")
    private String shippingCompany;
    @Schema(description="所有权归属", example="fdwafe")
    private String ownership;
    @Schema(description="起运港ID，多个时以“，”隔开", example="gfhghg")
    private String departureWharfId;
    @Schema(description="目的地港ID，多个时以“，”隔开", example="jhgfd")
    private String destinationWharfId;
    @NotNull(message="A级核载吨位不能为空")
    @Schema(description="A级核载吨位(吨)", example="45")
    private @NotNull(message="A级核载吨位不能为空") BigDecimal alevelPayload;
    @NotNull(message="B级核载吨位不能为空")
    @Schema(description="B级核载吨位(吨)", example="12")
    private @NotNull(message="B级核载吨位不能为空") BigDecimal blevelPayload;
    @NotNull(message="净重吨位不能为空")
    @Schema(description="净重(吨)", example="21.9")
    private @NotNull(message="净重吨位不能为空") BigDecimal selfPayload;
    @NotNull(message="总吨位不能为空")
    @Schema(description="总吨位(吨)", example="32.59")
    private @NotNull(message="总吨位不能为空") BigDecimal totalPayload;
    @NotNull(message="总长度不能为空")
    @Schema(description="总长度(米)", example="33.54")
    private @NotNull(message="总长度不能为空") BigDecimal totalLength;
    @NotNull(message="最大宽度不能为空")
    @Schema(description="最大宽度(米)", example="18.54")
    private @NotNull(message="最大宽度不能为空") BigDecimal maxWidth;
    @NotNull(message="船舶的型深不能为空")
    @Schema(description="船舶的型深(米)", example="22.76")
    private @NotNull(message="船舶的型深不能为空") BigDecimal mouldedDepth;
    @NotNull(message="满载吃水不能为空")
    @Schema(description="满载吃水(米)", example="12.87")
    private @NotNull(message="满载吃水不能为空") BigDecimal fullDraft;
    @NotNull(message="空载吃水不能为空")
    @Schema(description="空载吃水(米)", example="2.32")
    private @NotNull(message="空载吃水不能为空") BigDecimal emptyDraft;
    @Schema(description="最大装载量(吨)", example="12.32")
    private BigDecimal maxLoadingCapacity;
    @Schema(description="最大船高(米)", example="2.32")
    private BigDecimal maxHeight;
    @NotNull(message="是否绑定GPS终端不能为空")
    @Schema(description="是否绑定GPS终端，0-无，1-有", example="1")
    private @NotNull(message="是否绑定GPS终端不能为空") Integer gpsDevice;
    @Schema(description="GPS设备号", example="68464514")
    private String gpsDeviceNumber;
    @Schema(description="操作人ID", example="10010")
    private String operatorId;
    @Schema(description="操作人姓名", example="王大锤")
    private String operatorName;
}

