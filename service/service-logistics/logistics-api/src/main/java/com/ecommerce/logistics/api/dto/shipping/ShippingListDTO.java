
package com.ecommerce.logistics.api.dto.shipping;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(name="船舶列表DTO对象")
public class ShippingListDTO {
    @Schema(description="船舶ID", required=false, example="askdoamckasxxxx")
    private String shippingId;
    @Schema(description="归属用户id", required=false, example="askdoamckasxxxx")
    private String userId;
    @Schema(description="归属用户名称", required=false, example="测试船运公司")
    private String userName;
    @Schema(description="用户类型", required=false, example="1")
    private Integer userType;
    @Schema(description="船舶编码", required=false, example="43100231")
    private String number;
    @Schema(description="船舶类型", required=false, example="030250100")
    private String shippingType;
    @Schema(description="船舶名称", required=false, example="华夏7号")
    private String shippingName;
    @Schema(description="运输品类id", required=false, example="2eur6trcwaukfsafcaqxu84wb")
    private String transportCategoryId;
    @Schema(description="运输品类名称", required=false, example="2eur6trcwaukfsafcaqxu84wb")
    private String transportCategoryName;
    @Schema(description="载重", required=false, example="10000.00")
    private BigDecimal loadCapacity;
    @Schema(description="排水量", required=false, example="16000.00")
    private BigDecimal displacement;
    @Schema(description="船舶全长", required=false, example="438.00")
    private BigDecimal overallLength;
    @Schema(description="最大宽度", required=false, example="128.00")
    private BigDecimal extremeWidth;
    @Schema(description="最大高度", required=false, example="68.00")
    private BigDecimal extremeHeight;
    @Schema(description="默认船长id", required=false, example="")
    private String bindDriverId;
    @Schema(description="船长名称", required=false, example="")
    private String driverName;
    @Schema(description="船长电话", required=false, example="")
    private String driverPhone;
    @Schema(description="认证状态", required=false, example="0300100")
    private String certificationStatus;
    @Schema(description="创建时间", required=false, example="2018-12-30 18:21:01")
    private String createTime;
    @Schema(description="认证失败原因", required=false, example="askdoamckasxxxx")
    private String failReason;
}

