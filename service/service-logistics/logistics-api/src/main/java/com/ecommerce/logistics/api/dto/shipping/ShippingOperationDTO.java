
package com.ecommerce.logistics.api.dto.shipping;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

@Data
@Schema(name="操作船舶DTO对象")
public class ShippingOperationDTO {
    @NotBlank(message="船舶ID不能为空")
    @Schema(description="船舶ID", required=true, example="sdfgds")
    private String shippingId;
    @Schema(description="经纪人ID", example="sdfgds")
    private String managerMemberId;
    @Schema(description="操作人ID", example="10010")
    private String operatorId;
    @Schema(description="操作人姓名", example="王大锤")
    private String operatorName;
    @Schema(description="船舶状态,1-吉船,2-待装货,3-重船,4-检修中,5-已禁用", required=false, example="1")
    private Integer shippingStatus;
    @Schema(description="驳回原因", example="深刻的")
    private String rejectionReasons;
}

