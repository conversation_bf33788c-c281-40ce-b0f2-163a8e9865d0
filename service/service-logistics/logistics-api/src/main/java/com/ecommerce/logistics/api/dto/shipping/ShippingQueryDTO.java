
package com.ecommerce.logistics.api.dto.shipping;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;
import lombok.Data;

@Data
@Schema(name="船舶查询DTO对象")
public class ShippingQueryDTO {
    @Schema(description="归属用户id", required=false, example="askdoamckas21xx")
    private String userId;
    @Schema(description="归属用户类型", required=false, example="1")
    private Integer userType;
    @Schema(description="船舶编号", required=false, example="askdoamckas21xx")
    private String number;
    @Schema(description="船舶类型", required=false, example="askdoamckasxxxx")
    private String shippingType;
    @Schema(description="船舶名称", required=false, example="askdoamckasxxxx")
    private String shippingName;
    @Schema(description="运输品类id", required=false, example="askdoamckasxxxx")
    private String transportCategoryId;
    @Schema(description="认证状态列表", required=false, example="askdoamckasxxxx")
    private List<String> certificationStatusList;
}

