
package com.ecommerce.logistics.api.dto.shipping.erp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

@Data
public class BindErpShippingDTO {
    @Schema(description="电商中的厂商会员ID")
    private String ecMemberId;
    @Schema(description="电商中的厂商会员名称")
    private String ecMemberName;
    @Schema(description="电商承运商ID")
    private String ecCarrierId;
    @NotBlank(message="电商船舶ID不能为空")
    @Schema(description="电商船舶ID")
    private String ecShippingId;
    @NotBlank(message="电商船舶名称不能为空")
    @Schema(description="电商船舶名称")
    private String ecShippingName;
    @NotBlank(message="ERP船舶编号不能为空")
    @Schema(description="ERP船舶编号")
    private String erpShippingCode;
    @Schema(description="ERP船舶名称")
    private String erpShippingName;
    @Schema(description="ERP承运商编号")
    private String erpCarrierNo;
    @Schema(description="ERP承运商名称")
    private String erpCarrierName;
    @Schema(description="操作人")
    private String operatorId;
}

