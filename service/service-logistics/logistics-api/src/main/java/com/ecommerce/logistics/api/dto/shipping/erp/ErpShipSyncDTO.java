
package com.ecommerce.logistics.api.dto.shipping.erp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

@Data
public class ErpShipSyncDTO {
    @NotBlank(message="厂商会员ID不能为空")
    @Schema(description="电商中的厂商会员ID")
    private String ecMemberId;
    @Schema(description="电商承运商会员ID")
    private String memberId;
    @Schema(description="ERP承运商编号(必填)")
    private String erpCarrierNo;
    @Schema(description="船舶名称(前缀模糊匹配)")
    private String shipName;
    @Schema(description="船舶编号")
    private String shipCode;
    @Schema(description="操作者id")
    private String operatorId;
}

