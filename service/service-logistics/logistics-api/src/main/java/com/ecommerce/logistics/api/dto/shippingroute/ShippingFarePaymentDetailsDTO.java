
package com.ecommerce.logistics.api.dto.shippingroute;

import com.ecommerce.logistics.api.valid.EnumValidator;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@Schema(name="查询运价付费对象详情")
public class ShippingFarePaymentDetailsDTO {
    @Schema(description="航线运价付费ID")
    private Long shippingFarePaymentId;
    @Schema(description="辅助价目表NO")
    private String auxiliaryPriceNo;
    @Schema(description="辅助价目表名称")
    private String auxiliaryPriceName;
    @Schema(description="船舶类型")
    @NotBlank(message="航线运价类型不能为空")
    private @NotBlank(message="航线运价类型不能为空") String shipType;
    @Schema(description="航线运价类型：0-其他，1-买家付费，2-承运商收费")
    private Integer routeTariffType;
    @Schema(description="是否区分核载吨位：0-不区分，1-区分")
    @EnumValidator(value={0, 1})
    @NotNull(message="航线运价类型不能为空")
    private @NotNull(message="航线运价类型不能为空") Integer distinguish;
    @Schema(description="单位运价对象集合")
    private List<ShippingUnitFareDTO> shippingUnitFareList;

    public String fetchGroupKey(ShippingFarePaymentDetailsDTO dto) {
        return dto.getRouteTariffType() + "#" + dto.getShipType() + "#" + dto.getDistinguish();
    }
}

