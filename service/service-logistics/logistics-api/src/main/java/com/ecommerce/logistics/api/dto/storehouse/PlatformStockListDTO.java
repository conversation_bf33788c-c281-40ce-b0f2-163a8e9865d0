
package com.ecommerce.logistics.api.dto.storehouse;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
@Schema(name="平台占用库存列表实体")
public class PlatformStockListDTO {
    @Schema(description="归属人memberId")
    private String userId;
    @Schema(description="归属人名")
    private String userName;
    @Schema(description="总库存")
    private BigDecimal totalUsedStock = BigDecimal.ZERO;
    @Schema(description="总在途库存")
    private BigDecimal totalLockedStock = BigDecimal.ZERO;
    @Schema(description="商品分别库存项列表")
    private List<ProductStockItem> productStockItemList;
}

