
package com.ecommerce.logistics.api.dto.storehouse;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;

@Schema(name="商品库存项")
public class ProductStockItem {
    @Schema(description="商品ID")
    private String productId;
    @Schema(description="商品名")
    private String note;
    @Schema(description="当期库存")
    private BigDecimal usedStock = BigDecimal.ZERO;
    @Schema(description="在途库存")
    private BigDecimal lockedStock = BigDecimal.ZERO;

    public String getProductId() {
        return this.productId;
    }

    public String getNote() {
        return this.note;
    }

    public BigDecimal getUsedStock() {
        return this.usedStock;
    }

    public BigDecimal getLockedStock() {
        return this.lockedStock;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public void setUsedStock(BigDecimal usedStock) {
        this.usedStock = usedStock;
    }

    public void setLockedStock(BigDecimal lockedStock) {
        this.lockedStock = lockedStock;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof ProductStockItem)) {
            return false;
        }
        ProductStockItem other = (ProductStockItem)o;
        if (!other.canEqual(this)) {
            return false;
        }
        String this$productId = this.getProductId();
        String other$productId = other.getProductId();
        if (this$productId == null ? other$productId != null : !this$productId.equals(other$productId)) {
            return false;
        }
        String this$note = this.getNote();
        String other$note = other.getNote();
        if (this$note == null ? other$note != null : !this$note.equals(other$note)) {
            return false;
        }
        BigDecimal this$usedStock = this.getUsedStock();
        BigDecimal other$usedStock = other.getUsedStock();
        if (this$usedStock == null ? other$usedStock != null : !((Object)this$usedStock).equals(other$usedStock)) {
            return false;
        }
        BigDecimal this$lockedStock = this.getLockedStock();
        BigDecimal other$lockedStock = other.getLockedStock();
        return !(this$lockedStock == null ? other$lockedStock != null : !((Object)this$lockedStock).equals(other$lockedStock));
    }

    protected boolean canEqual(Object other) {
        return other instanceof ProductStockItem;
    }

    public int hashCode() {
        int result = 1;
        String $productId = this.getProductId();
        result = result * 59 + ($productId == null ? 43 : $productId.hashCode());
        String $note = this.getNote();
        result = result * 59 + ($note == null ? 43 : $note.hashCode());
        BigDecimal $usedStock = this.getUsedStock();
        result = result * 59 + ($usedStock == null ? 43 : ((Object)$usedStock).hashCode());
        BigDecimal $lockedStock = this.getLockedStock();
        result = result * 59 + ($lockedStock == null ? 43 : ((Object)$lockedStock).hashCode());
        return result;
    }

    public String toString() {
        return "ProductStockItem(productId=" + this.getProductId() + ", note=" + this.getNote() + ", usedStock=" + this.getUsedStock() + ", lockedStock=" + this.getLockedStock() + ")";
    }
}

