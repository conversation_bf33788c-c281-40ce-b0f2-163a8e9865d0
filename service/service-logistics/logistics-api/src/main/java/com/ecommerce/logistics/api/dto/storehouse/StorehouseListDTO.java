
package com.ecommerce.logistics.api.dto.storehouse;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(name="仓库存储列表实体")
public class StorehouseListDTO {
    @Schema(description="仓库ID")
    private String warehouseId;
    @Schema(description="仓库容量")
    private BigDecimal warehouseCapacity = BigDecimal.ZERO;
    @Schema(description="当前库存")
    private BigDecimal usedStock = BigDecimal.ZERO;
    @Schema(description="在途库存")
    private BigDecimal lockedStock = BigDecimal.ZERO;
    @Schema(description="可用库存")
    private BigDecimal avaStock = BigDecimal.ZERO;
}

