
package com.ecommerce.logistics.api.dto.storehouse;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;

@Schema(name="周转量商品视角项")
public class TSProductViewItem {
    @Schema(description="卖家ID")
    private String sellerId;
    @Schema(description="卖家名")
    private String sellerName;
    @Schema(description="开始日期")
    private String startDateStr;
    @Schema(description="截止日期")
    private String endDateStr;
    @Schema(description="累计入库量")
    private BigDecimal totalInQuantity = BigDecimal.ZERO;
    @Schema(description="累计出库量")
    private BigDecimal totalOutQuantity = BigDecimal.ZERO;

    public String getSellerId() {
        return this.sellerId;
    }

    public String getSellerName() {
        return this.sellerName;
    }

    public String getStartDateStr() {
        return this.startDateStr;
    }

    public String getEndDateStr() {
        return this.endDateStr;
    }

    public BigDecimal getTotalInQuantity() {
        return this.totalInQuantity;
    }

    public BigDecimal getTotalOutQuantity() {
        return this.totalOutQuantity;
    }

    public void setSellerId(String sellerId) {
        this.sellerId = sellerId;
    }

    public void setSellerName(String sellerName) {
        this.sellerName = sellerName;
    }

    public void setStartDateStr(String startDateStr) {
        this.startDateStr = startDateStr;
    }

    public void setEndDateStr(String endDateStr) {
        this.endDateStr = endDateStr;
    }

    public void setTotalInQuantity(BigDecimal totalInQuantity) {
        this.totalInQuantity = totalInQuantity;
    }

    public void setTotalOutQuantity(BigDecimal totalOutQuantity) {
        this.totalOutQuantity = totalOutQuantity;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof TSProductViewItem)) {
            return false;
        }
        TSProductViewItem other = (TSProductViewItem)o;
        if (!other.canEqual(this)) {
            return false;
        }
        String this$sellerId = this.getSellerId();
        String other$sellerId = other.getSellerId();
        if (this$sellerId == null ? other$sellerId != null : !this$sellerId.equals(other$sellerId)) {
            return false;
        }
        String this$sellerName = this.getSellerName();
        String other$sellerName = other.getSellerName();
        if (this$sellerName == null ? other$sellerName != null : !this$sellerName.equals(other$sellerName)) {
            return false;
        }
        String this$startDateStr = this.getStartDateStr();
        String other$startDateStr = other.getStartDateStr();
        if (this$startDateStr == null ? other$startDateStr != null : !this$startDateStr.equals(other$startDateStr)) {
            return false;
        }
        String this$endDateStr = this.getEndDateStr();
        String other$endDateStr = other.getEndDateStr();
        if (this$endDateStr == null ? other$endDateStr != null : !this$endDateStr.equals(other$endDateStr)) {
            return false;
        }
        BigDecimal this$totalInQuantity = this.getTotalInQuantity();
        BigDecimal other$totalInQuantity = other.getTotalInQuantity();
        if (this$totalInQuantity == null ? other$totalInQuantity != null : !((Object)this$totalInQuantity).equals(other$totalInQuantity)) {
            return false;
        }
        BigDecimal this$totalOutQuantity = this.getTotalOutQuantity();
        BigDecimal other$totalOutQuantity = other.getTotalOutQuantity();
        return !(this$totalOutQuantity == null ? other$totalOutQuantity != null : !((Object)this$totalOutQuantity).equals(other$totalOutQuantity));
    }

    protected boolean canEqual(Object other) {
        return other instanceof TSProductViewItem;
    }

    public int hashCode() {
        int result = 1;
        String $sellerId = this.getSellerId();
        result = result * 59 + ($sellerId == null ? 43 : $sellerId.hashCode());
        String $sellerName = this.getSellerName();
        result = result * 59 + ($sellerName == null ? 43 : $sellerName.hashCode());
        String $startDateStr = this.getStartDateStr();
        result = result * 59 + ($startDateStr == null ? 43 : $startDateStr.hashCode());
        String $endDateStr = this.getEndDateStr();
        result = result * 59 + ($endDateStr == null ? 43 : $endDateStr.hashCode());
        BigDecimal $totalInQuantity = this.getTotalInQuantity();
        result = result * 59 + ($totalInQuantity == null ? 43 : ((Object)$totalInQuantity).hashCode());
        BigDecimal $totalOutQuantity = this.getTotalOutQuantity();
        result = result * 59 + ($totalOutQuantity == null ? 43 : ((Object)$totalOutQuantity).hashCode());
        return result;
    }

    public String toString() {
        return "TSProductViewItem(sellerId=" + this.getSellerId() + ", sellerName=" + this.getSellerName() + ", startDateStr=" + this.getStartDateStr() + ", endDateStr=" + this.getEndDateStr() + ", totalInQuantity=" + this.getTotalInQuantity() + ", totalOutQuantity=" + this.getTotalOutQuantity() + ")";
    }
}

