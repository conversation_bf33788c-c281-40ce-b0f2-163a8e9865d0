
package com.ecommerce.logistics.api.dto.storehouse;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;

@Schema(name="周转量卖家视角项")
public class TSSellerViewItem {
    @Schema(description="商品ID")
    private String productId;
    @Schema(description="商品名")
    private String productName;
    @Schema(description="开始日期")
    private String startDateStr;
    @Schema(description="截止日期")
    private String endDateStr;
    @Schema(description="累计入库量")
    private BigDecimal totalInQuantity = BigDecimal.ZERO;
    @Schema(description="累计出库量")
    private BigDecimal totalOutQuantity = BigDecimal.ZERO;

    public String getProductId() {
        return this.productId;
    }

    public String getProductName() {
        return this.productName;
    }

    public String getStartDateStr() {
        return this.startDateStr;
    }

    public String getEndDateStr() {
        return this.endDateStr;
    }

    public BigDecimal getTotalInQuantity() {
        return this.totalInQuantity;
    }

    public BigDecimal getTotalOutQuantity() {
        return this.totalOutQuantity;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public void setStartDateStr(String startDateStr) {
        this.startDateStr = startDateStr;
    }

    public void setEndDateStr(String endDateStr) {
        this.endDateStr = endDateStr;
    }

    public void setTotalInQuantity(BigDecimal totalInQuantity) {
        this.totalInQuantity = totalInQuantity;
    }

    public void setTotalOutQuantity(BigDecimal totalOutQuantity) {
        this.totalOutQuantity = totalOutQuantity;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof TSSellerViewItem)) {
            return false;
        }
        TSSellerViewItem other = (TSSellerViewItem)o;
        if (!other.canEqual(this)) {
            return false;
        }
        String this$productId = this.getProductId();
        String other$productId = other.getProductId();
        if (this$productId == null ? other$productId != null : !this$productId.equals(other$productId)) {
            return false;
        }
        String this$productName = this.getProductName();
        String other$productName = other.getProductName();
        if (this$productName == null ? other$productName != null : !this$productName.equals(other$productName)) {
            return false;
        }
        String this$startDateStr = this.getStartDateStr();
        String other$startDateStr = other.getStartDateStr();
        if (this$startDateStr == null ? other$startDateStr != null : !this$startDateStr.equals(other$startDateStr)) {
            return false;
        }
        String this$endDateStr = this.getEndDateStr();
        String other$endDateStr = other.getEndDateStr();
        if (this$endDateStr == null ? other$endDateStr != null : !this$endDateStr.equals(other$endDateStr)) {
            return false;
        }
        BigDecimal this$totalInQuantity = this.getTotalInQuantity();
        BigDecimal other$totalInQuantity = other.getTotalInQuantity();
        if (this$totalInQuantity == null ? other$totalInQuantity != null : !((Object)this$totalInQuantity).equals(other$totalInQuantity)) {
            return false;
        }
        BigDecimal this$totalOutQuantity = this.getTotalOutQuantity();
        BigDecimal other$totalOutQuantity = other.getTotalOutQuantity();
        return !(this$totalOutQuantity == null ? other$totalOutQuantity != null : !((Object)this$totalOutQuantity).equals(other$totalOutQuantity));
    }

    protected boolean canEqual(Object other) {
        return other instanceof TSSellerViewItem;
    }

    public int hashCode() {
        int result = 1;
        String $productId = this.getProductId();
        result = result * 59 + ($productId == null ? 43 : $productId.hashCode());
        String $productName = this.getProductName();
        result = result * 59 + ($productName == null ? 43 : $productName.hashCode());
        String $startDateStr = this.getStartDateStr();
        result = result * 59 + ($startDateStr == null ? 43 : $startDateStr.hashCode());
        String $endDateStr = this.getEndDateStr();
        result = result * 59 + ($endDateStr == null ? 43 : $endDateStr.hashCode());
        BigDecimal $totalInQuantity = this.getTotalInQuantity();
        result = result * 59 + ($totalInQuantity == null ? 43 : ((Object)$totalInQuantity).hashCode());
        BigDecimal $totalOutQuantity = this.getTotalOutQuantity();
        result = result * 59 + ($totalOutQuantity == null ? 43 : ((Object)$totalOutQuantity).hashCode());
        return result;
    }

    public String toString() {
        return "TSSellerViewItem(productId=" + this.getProductId() + ", productName=" + this.getProductName() + ", startDateStr=" + this.getStartDateStr() + ", endDateStr=" + this.getEndDateStr() + ", totalInQuantity=" + this.getTotalInQuantity() + ", totalOutQuantity=" + this.getTotalOutQuantity() + ")";
    }
}

