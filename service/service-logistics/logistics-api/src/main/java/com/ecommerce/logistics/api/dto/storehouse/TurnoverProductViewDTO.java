
package com.ecommerce.logistics.api.dto.storehouse;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;
import lombok.Data;

@Data
@Schema(name="周转量商品视角")
public class TurnoverProductViewDTO {
    @Schema(description="商品ID")
    private String productId;
    @Schema(description="商品名")
    private String productName;
    @Schema(description="周转量子项")
    private List<TSProductViewItem> tsProductViewItems;
}

