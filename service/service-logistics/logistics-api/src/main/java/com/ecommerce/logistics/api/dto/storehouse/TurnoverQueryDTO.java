
package com.ecommerce.logistics.api.dto.storehouse;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="周转量查询条件类")
public class TurnoverQueryDTO {
    @Schema(description="仓库ID")
    private String warehouseId;
    @Schema(description="卖家ID")
    private String sellerId;
    @Schema(description="卖家名关键字")
    private String sellerNameLike;
    @Schema(description="商品名关键字")
    private String productNameLike;
    @Schema(description="开始日期")
    private String startDateStr;
    @Schema(description="截止日期")
    private String endDateStr;
}

