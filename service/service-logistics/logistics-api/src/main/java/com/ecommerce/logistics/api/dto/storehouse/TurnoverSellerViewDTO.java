
package com.ecommerce.logistics.api.dto.storehouse;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;
import lombok.Data;

@Data
@Schema(name="周转量卖家视角")
public class TurnoverSellerViewDTO {
    @Schema(description="卖家ID")
    private String sellerId;
    @Schema(description="卖家名")
    private String sellerName;
    @Schema(description="周转量子项")
    private List<TSSellerViewItem> tsSellerViewItems;
}

