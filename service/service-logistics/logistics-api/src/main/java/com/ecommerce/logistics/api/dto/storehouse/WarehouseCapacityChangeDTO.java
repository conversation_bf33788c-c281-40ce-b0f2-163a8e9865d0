
package com.ecommerce.logistics.api.dto.storehouse;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(name="仓库容量修改实体")
public class WarehouseCapacityChangeDTO {
    @Schema(description="仓库ID")
    private String warehouseId;
    @Schema(description="仓库名")
    private String warehouseName;
    @Schema(description="仓库类型")
    private String warehouseType;
    @Schema(description="容量")
    private BigDecimal capacity = BigDecimal.ZERO;
    @Schema(description="更新人ID")
    private String updateUser;
    @Schema(description="更新人名")
    private String updateUserName;
}

