
package com.ecommerce.logistics.api.dto.storehouse;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(name="仓库存储列表实体")
public class WarehouseStorageListDTO {
    @Schema(description="仓库ID")
    private String warehouseId;
    @Schema(description="仓库名称")
    private String warehouseName;
    @Schema(description="仓库类型")
    private String type;
    @Schema(description="仓库地址")
    private String address;
    @Schema(description="单位")
    private String unit;
    @Schema(description="仓库容量")
    private BigDecimal warehouseCapacity;
    @Schema(description="当前库存")
    private BigDecimal usedStock;
    @Schema(description="在途库存")
    private BigDecimal lockedStock;
    @Schema(description="可用库存")
    private BigDecimal avaStock;
}

