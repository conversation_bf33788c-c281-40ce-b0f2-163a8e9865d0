
package com.ecommerce.logistics.api.dto.storehouse;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="仓库存储查询条件实体")
public class WarehouseStorageQueryDTO {
    @Schema(description="仓库ID")
    private String warehouseId;
    @Schema(description="归属人")
    private String userId;
    @Schema(description="仓库名模糊关键字")
    private String warehouseNameLike;
    @Schema(description="仓库类型")
    private String type;
}

