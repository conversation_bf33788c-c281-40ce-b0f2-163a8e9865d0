
package com.ecommerce.logistics.api.dto.transportaddress;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="运输地址删除DTO对象")
public class TransportAddressDeleteDTO {
    @Schema(description="运输地址ID", required=false, example="askdoamckasxxxx")
    private String transportAddressId;
    @Schema(description="操作人ID", required=true, example="10010")
    private String operatorUserId;
    @Schema(description="操作人姓名", required=true, example="王大锤")
    private String operatorUserName;
}

