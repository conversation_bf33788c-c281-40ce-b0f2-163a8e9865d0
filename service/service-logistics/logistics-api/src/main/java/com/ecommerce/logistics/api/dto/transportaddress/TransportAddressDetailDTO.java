
package com.ecommerce.logistics.api.dto.transportaddress;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="运输地址详情DTO对象")
public class TransportAddressDetailDTO {
    @Schema(description="运输地址ID", required=false, example="askdoamckasxxxx")
    private String transportAddressId;
    @Schema(description="地址类型", required=false, example="030240100")
    private String addressType;
    @Schema(description="地址名称", required=false, example="测试提货点")
    private String addressName;
    @Schema(description="归属用户id", required=false, example="askdoamckas21xx")
    private String userId;
    @Schema(description="归属用户名称", required=false, example="测试用户")
    private String userName;
    @Schema(description="归属用户类型", required=false, example="1")
    private Integer userType;
    @Schema(description="省份", required=false, example="广东省")
    private String province;
    @Schema(description="省份代码", required=false, example="410000")
    private String provinceCode;
    @Schema(description="城市", required=false, example="深圳市")
    private String city;
    @Schema(description="城市代码", required=false, example="419000")
    private String cityCode;
    @Schema(description="地区", required=false, example="罗湖区")
    private String district;
    @Schema(description="地区代码", required=false, example="419300")
    private String districtCode;
    @Schema(description="详细地址", required=false, example="地王大厦19楼")
    private String address;
    @Schema(description="地址经纬度", required=false, example="103.0900901,43.100012")
    private String location;
}

