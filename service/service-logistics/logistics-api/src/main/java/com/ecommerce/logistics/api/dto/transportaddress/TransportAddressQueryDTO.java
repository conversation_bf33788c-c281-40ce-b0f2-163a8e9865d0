
package com.ecommerce.logistics.api.dto.transportaddress;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="运输地址查询DTO对象")
public class TransportAddressQueryDTO {
    @Schema(description="地址类型", required=false, example="030240100")
    private String addressType;
    @Schema(description="地址名称", required=false, example="测试提货点")
    private String addressName;
    @Schema(description="归属用户id", required=false, example="askdoamckas21xx")
    private String userId;
    @Schema(description="归属用户类型", required=false, example="1")
    private Integer userType;
    @Schema(description="省份代码", required=false, example="410000")
    private String provinceCode;
    @Schema(description="城市代码", required=false, example="419000")
    private String cityCode;
    @Schema(description="地区代码", required=false, example="419300")
    private String districtCode;
}

