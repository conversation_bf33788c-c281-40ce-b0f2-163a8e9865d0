
package com.ecommerce.logistics.api.dto.transportcapacity;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
@Schema(name="运力列表DTO对象")
public class TransportCapacityListDTO {
    @Schema(description="运力ID", required=false, example="askdoamckasxxxx")
    private String transportCapacityId;
    @Schema(description="发布人ID", required=false, example="askdoamckas")
    private String publisherId;
    @Schema(description="发布人姓名", required=false, example="测试承运商")
    private String publisherName;
    @Schema(description="发布人类型", required=false, example="承运商")
    private Integer publisherType;
    @Schema(description="驾驶员ID", required=false, example="xxx")
    private String driverId;
    @Schema(description="驾驶员ID", required=false, example="张某某")
    private String driverName;
    @Schema(description="驾驶员电话", required=false, example="12345678900")
    private String transportToolPhone;
    @Schema(description="运输工具类型", required=false, example="03150100")
    private String transportToolType;
    @Schema(description="运输工具ID", required=false, example="张某某")
    private String transportToolId;
    @Schema(description="工具编号", required=false, example="渝B909031")
    private String toolNumber;
    @Schema(description="装载容量", required=false, example="20.00吨")
    private BigDecimal loadCapacity;
    @Schema(description="运输品类Id", required=false, example="xnmldo2001kdoam321kd")
    private String transportCategoryId;
    @Schema(description="运输品类名称", required=false, example="xnmldo2001kdoam321kd")
    private String transportCategoryName;
    @Schema(description="起始运输时间范围", required=false, example="2018-12-30")
    private String deliveryTimeStart;
    @Schema(description="结束运输时间范围", required=false, example="2018-12-31")
    private String deliveryTimeEnd;
    @Schema(description="状态", required=false, example="0300123")
    private String status;
    @Schema(description="创建时间", required=false, example="2018-12-30 12:48:19")
    private String createTime;
    @Schema(description="配送区域列表")
    private List<TransportZoneMapDTO> zoneMapList;
}

