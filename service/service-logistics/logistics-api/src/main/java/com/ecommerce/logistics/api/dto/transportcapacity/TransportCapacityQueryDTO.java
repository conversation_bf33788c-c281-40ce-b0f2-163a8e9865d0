
package com.ecommerce.logistics.api.dto.transportcapacity;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import lombok.Data;

@Data
@Schema(name="运力查询DTO对象")
public class TransportCapacityQueryDTO {
    @Schema(description="发布人ID", required=false, example="xxx")
    private String publisherId;
    @Schema(description="发布人ID列表", required=false, example="xxx")
    private List<String> publisherIdList;
    @Schema(description="发布人类型", required=false, example="xxx")
    private Integer publisherType;
    @Schema(description="驾驶员ID", required=false, example="xxx")
    private String driverId;
    @Schema(description="运输工具类型", required=false, example="xxx")
    private String transportToolType;
    @Schema(description="运输品类Id", required=false, example="xxx")
    private String transportCategoryId;
    @Schema(description="运输时间范围起始日期", required=false, example="2018-12-30")
    private String deliveryTimeStart;
    @Schema(description="运输时间范围结束日期", required=false, example="xxx")
    private String deliveryTimeEnd;
    @Schema(description="载重最小值", required=false, example="20")
    private BigDecimal loadCapacityMin;
    @Schema(description="载重最大值", required=false, example="30")
    private BigDecimal loadCapacityMax;
    @Schema(description="配送区域省份编码", required=false, example="500000")
    private String provinceCode;
    @Schema(description="配送区域城市编码", required=false, example="510000")
    private String cityCode;
    @Schema(description="配送区域区域编码", required=false, example="xxx")
    private String districtCode;
    @Schema(description="工具编号", required=false)
    private String toolNumber;
    @Schema(description="排序集合", required=false)
    private Map<String, String> orderMap;
    private Integer needJoinVOrS = 0;
}

