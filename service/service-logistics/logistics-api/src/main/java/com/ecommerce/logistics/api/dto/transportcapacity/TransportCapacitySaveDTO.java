
package com.ecommerce.logistics.api.dto.transportcapacity;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
@Schema(name="运力保存DTO对象")
public class TransportCapacitySaveDTO {
    @Schema(description="运力ID", required=false, example="askdoamckasxxxx")
    private String transportCapacityId;
    @Schema(description="发布人ID", required=true, example="askdoamckas")
    private String publisherId;
    @Schema(description="发布人姓名", required=true, example="测试承运商")
    private String publisherName;
    @Schema(description="发布人类型", required=true, example="1")
    private Integer publisherType;
    @Schema(description="驾驶员ID", required=true, example="xxx")
    private String driverId;
    @Schema(description="驾驶员名称", required=true, example="张某某")
    private String driverName;
    @Schema(description="运输工具类型", required=true, example="03150100")
    private String transportToolType;
    @Schema(description="运输工具ID", required=true, example="张某某")
    private String transportToolId;
    @Schema(description="装载容量", required=true, example="20.00")
    private BigDecimal loadCapacity;
    @Schema(description="运输品类Id", required=true, example="xnmldo2001kdoam321kd")
    private String transportCategoryId;
    @Schema(description="运力提供起始时间", required=true, example="2018-12-30")
    private String deliveryTimeStart;
    @Schema(description="运力提供结束时间", required=true, example="2018-12-30")
    private String deliveryTimeEnd;
    @Schema(description="运输区域", required=true)
    private List<TransportZoneMapDTO> transportZoneMapList;
    @Schema(description="操作人ID", required=true, example="10010")
    private String operatorUserId;
    @Schema(description="操作人姓名", required=true, example="王大锤")
    private String operatorUserName;
}

