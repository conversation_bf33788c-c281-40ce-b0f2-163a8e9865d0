
package com.ecommerce.logistics.api.dto.transportcapacity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="运力浏览记录查询DTO对象")
public class TransportCapacityViewQueryDTO {
    @Schema(description="查询用户id", required=false, example="askdoamckdaasxxxx")
    private String userId;
    @Schema(description="运输工具类型", required=false, example="0300210100")
    private String transportToolType;
    @Schema(description="查询记录大小", required=false, example="10")
    private Integer size;
}

