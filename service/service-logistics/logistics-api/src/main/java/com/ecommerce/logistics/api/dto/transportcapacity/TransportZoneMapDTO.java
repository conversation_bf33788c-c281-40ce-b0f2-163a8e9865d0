
package com.ecommerce.logistics.api.dto.transportcapacity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="运输区域映射DTO对象")
public class TransportZoneMapDTO {
    @Schema(description="配送省份", required=true, example="广东省")
    private String province;
    @Schema(description="配送省份编码", required=true, example="400000")
    private String provinceCode;
    @Schema(description="配送城市", required=false, example="深圳市")
    private String city;
    @Schema(description="配送城市编码", required=false, example="410000")
    private String cityCode;
    @Schema(description="配送地区", required=false, example="宝安区")
    private String district;
    @Schema(description="配送地区编码", required=false, example="419000")
    private String districtCode;
    @Schema(description="配送街道", required=false, example="宝安街道")
    private String street;
    @Schema(description="配送街道编码", required=false, example="419001")
    private String streetCode;
}

