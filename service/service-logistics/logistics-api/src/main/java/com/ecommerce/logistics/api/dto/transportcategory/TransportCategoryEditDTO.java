
package com.ecommerce.logistics.api.dto.transportcategory;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="运输品类编辑DTO对象")
public class TransportCategoryEditDTO {
    @Schema(description="运输品类ID", required=true)
    private String transportCategoryId;
    @Schema(description="运输品类名称", required=true, example="混凝土")
    private String categoryName;
    @Schema(description="是否可整合运输，0-否 1-是", required=true, example="0")
    private Integer isIntegrable;
}

