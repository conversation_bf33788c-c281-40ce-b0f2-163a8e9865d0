
package com.ecommerce.logistics.api.dto.transportcategory;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name="运输品类选项对象")
public class TransportOption {
    @Schema(description="运输品类名称", required=true)
    private String categoryName;
    @Schema(description="运输品类ID", required=true)
    private String transportCategoryId;

    public String getCategoryName() {
        return this.categoryName;
    }

    public String getTransportCategoryId() {
        return this.transportCategoryId;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public void setTransportCategoryId(String transportCategoryId) {
        this.transportCategoryId = transportCategoryId;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof TransportOption)) {
            return false;
        }
        TransportOption other = (TransportOption)o;
        if (!other.canEqual(this)) {
            return false;
        }
        String this$categoryName = this.getCategoryName();
        String other$categoryName = other.getCategoryName();
        if (this$categoryName == null ? other$categoryName != null : !this$categoryName.equals(other$categoryName)) {
            return false;
        }
        String this$transportCategoryId = this.getTransportCategoryId();
        String other$transportCategoryId = other.getTransportCategoryId();
        return !(this$transportCategoryId == null ? other$transportCategoryId != null : !this$transportCategoryId.equals(other$transportCategoryId));
    }

    protected boolean canEqual(Object other) {
        return other instanceof TransportOption;
    }

    public int hashCode() {
        int result = 1;
        String $categoryName = this.getCategoryName();
        result = result * 59 + ($categoryName == null ? 43 : $categoryName.hashCode());
        String $transportCategoryId = this.getTransportCategoryId();
        result = result * 59 + ($transportCategoryId == null ? 43 : $transportCategoryId.hashCode());
        return result;
    }

    public String toString() {
        return "TransportOption(categoryName=" + this.getCategoryName() + ", transportCategoryId=" + this.getTransportCategoryId() + ")";
    }
}

