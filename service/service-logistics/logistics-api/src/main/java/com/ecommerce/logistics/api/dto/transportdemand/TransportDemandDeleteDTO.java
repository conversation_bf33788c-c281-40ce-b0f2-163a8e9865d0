
package com.ecommerce.logistics.api.dto.transportdemand;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="运输需求删除DTO对象")
public class TransportDemandDeleteDTO {
    @Schema(description="运输需求ID", required=false, example="askdoamckasxxxx")
    private String transportDemandId;
    @Schema(description="操作人ID", required=true, example="10010")
    private String operatorUserId;
    @Schema(description="操作人姓名", required=true, example="王大锤")
    private String operatorUserName;
}

