
package com.ecommerce.logistics.api.dto.transportdemand;

import com.ecommerce.logistics.api.dto.transportaddress.TransportAddressDetailDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(name="运输需求详情DTO对象")
public class TransportDemandDetailDTO {
    @Schema(description="运输需求ID", required=false, example="askdoamckasxxxx")
    private String transportDemandId;
    @Schema(description="归属用户id", required=false, example="askdoamckdaasxxxx")
    private String userId;
    @Schema(description="归属用户名称", required=false, example="admin")
    private String userName;
    @Schema(description="归属用户类型", required=false, example="1")
    private Integer userType;
    @Schema(description="联系人姓名", required=true, example="联系人姓名")
    private String contactName;
    @Schema(description="联系人电话", required=true, example="联系人电话")
    private String contactPhone;
    @Schema(description="货物描述", required=false, example="袋装水泥")
    private String freightDesc;
    @Schema(description="货物数量", required=false, example="20.00")
    private BigDecimal freightQuantity;
    @Schema(description="货物单位", required=false, example="吨")
    private String freightUnit;
    @Schema(description="运输品类id", required=false, example="xxx")
    private String transportCategoryId;
    @Schema(description="运输工具类型", required=false, example="030210100")
    private String transportToolType;
    @Schema(description="配送时间", required=false, example="2018-12-30")
    private String deliveryTime;
    @Schema(description="配送时间范围", required=false, example="03020100")
    private String deliveryTimeRange;
    @Schema(description="提货地址详情")
    private TransportAddressDetailDTO pickingAddress;
    @Schema(description="卸货地址详情")
    private TransportAddressDetailDTO unloadAddress;
    @Schema(description="需求状态", required=false, example="030220100")
    private String status;
    @Schema(description="审核失败原因", required=false, example="虚假信息")
    private String failReason;
}

