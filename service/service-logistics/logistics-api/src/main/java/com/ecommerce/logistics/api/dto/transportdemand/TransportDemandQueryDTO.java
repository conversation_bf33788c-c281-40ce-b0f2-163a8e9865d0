
package com.ecommerce.logistics.api.dto.transportdemand;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;
import java.util.Map;
import lombok.Data;

@Data
@Schema(name="运输需求查询DTO对象")
public class TransportDemandQueryDTO {
    @Schema(description="归属用户id", required=false, example="askdoamckdaasxxxx")
    private String userId;
    @Schema(description="归属用户类型", required=false, example="askdoamckdaasxxxx")
    private Integer userType;
    @Schema(description="运输品类id", required=false, example="xxx")
    private String transportCategoryId;
    @Schema(description="运输工具类型", required=false, example="030210100")
    private String transportToolType;
    @Schema(description="配送时间范围起始日期", required=false, example="2018-12-30")
    private String deliveryTimeStart;
    @Schema(description="配送时间范围结束日期", required=false, example="2019-01-01")
    private String deliveryTimeEnd;
    @Schema(description="提货地址id", required=false, example="askdoamckdaasx1xxx")
    private String pickingAddressId;
    @Schema(description="卸货点址省份编码", required=false, example="askdoamckdaasxx2xx")
    private String unloadProvinceCode;
    @Schema(description="卸货点址城市编码", required=false, example="askdoamckdaasxx2xx")
    private String unloadCityCode;
    @Schema(description="卸货点址区域编码", required=false, example="askdoamckdaasxx2xx")
    private String unloadDistrictCode;
    @Schema(description="卸货地址id", required=false, example="askdoamckdaasxx2xx")
    private String unloadAddressId;
    @Schema(description="需求状态列表", required=false, example="030220100")
    private List<String> statusList;
    @Schema(description="排序集合", required=false)
    private Map<String, String> orderMap;
}

