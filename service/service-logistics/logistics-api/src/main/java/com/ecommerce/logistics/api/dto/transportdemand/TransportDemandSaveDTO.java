
package com.ecommerce.logistics.api.dto.transportdemand;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(name="运输需求保存DTO对象")
public class TransportDemandSaveDTO {
    @Schema(description="运输需求ID", required=false, example="askdoamckasxxxx")
    private String transportDemandId;
    @Schema(description="归属用户id", required=false, example="askdoamckdaasxxxx")
    private String userId;
    @Schema(description="归属用户名称", required=false, example="admin")
    private String userName;
    @Schema(description="归属用户类型", required=false, example="1")
    private Integer userType;
    @Schema(description="货物描述", required=false, example="袋装水泥")
    private String freightDesc;
    @Schema(description="货物数量", required=false, example="20.00")
    private BigDecimal freightQuantity;
    @Schema(description="货物单位", required=false, example="吨")
    private String freightUnit;
    @Schema(description="运输品类id", required=false, example="xxx")
    private String transportCategoryId;
    @Schema(description="运输工具类型", required=false, example="030210100")
    private String transportToolType;
    @Schema(description="配送时间", required=false, example="2018-12-30")
    private String deliveryTime;
    @Schema(description="配送时间范围", required=false, example="03020100")
    private String deliveryTimeRange;
    @Schema(description="提货地址id", required=false, example="askdoamckdaasx1xxx")
    private String pickingAddressId;
    @Schema(description="卸货地址id", required=false, example="askdoamckdaasxx2xx")
    private String unloadAddressId;
    @Schema(description="操作人ID", required=true, example="10010")
    private String operatorUserId;
    @Schema(description="操作人姓名", required=true, example="王大锤")
    private String operatorUserName;
}

