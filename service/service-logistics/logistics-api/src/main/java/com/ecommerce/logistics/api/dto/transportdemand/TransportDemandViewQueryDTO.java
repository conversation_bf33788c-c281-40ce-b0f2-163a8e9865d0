
package com.ecommerce.logistics.api.dto.transportdemand;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="运输需求浏览记录查询DTO对象")
public class TransportDemandViewQueryDTO {
    @Schema(description="查询用户id", required=false, example="askdoamckdaasxxxx")
    private String userId;
    @Schema(description="查询记录大小", required=false, example="10")
    private Integer size;
}

