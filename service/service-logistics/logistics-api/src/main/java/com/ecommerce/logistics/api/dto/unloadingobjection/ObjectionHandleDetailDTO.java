
package com.ecommerce.logistics.api.dto.unloadingobjection;

import com.ecommerce.logistics.api.dto.operationrecord.OperationRecordListDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;
import lombok.Data;

@Data
@Schema(name="卸货异议处理详情DTO")
public class ObjectionHandleDetailDTO {
    @Schema(description="卸货异议ID")
    private String objectionId;
    @Schema(description="发起方角色类型")
    private String proposerType;
    @Schema(description="发起方名称")
    private String proposerName;
    @Schema(description="状态")
    private String status;
    @Schema(description="操作记录")
    private List<OperationRecordListDTO> operationRecordListDTOS;
}

