
package com.ecommerce.logistics.api.dto.unloadingobjection;

import com.ecommerce.logistics.api.dto.attachment.AttListAddDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(name="卸货异议新增DTO")
public class UnloadingObjectionAddDTO {
    @Schema(description="发起方角色类型")
    private String proposerType;
    @Schema(description="发起方账号ID")
    private String proposerId;
    @Schema(description="发起方名称")
    private String proposerName;
    @Schema(description="关联运单ID")
    private String relationBillId;
    @Schema(description="关联运单号")
    private String relationBillNum;
    @Schema(description="商品ID")
    private String goodsId;
    @Schema(description="商品名称")
    private String goodsName;
    @Schema(description="车牌号")
    private String vehicleNum;
    @Schema(description="司机ID")
    private String driverId;
    @Schema(description="司机姓名")
    private String driverName;
    @Schema(description="买家ID")
    private String buyerId;
    @Schema(description="买家名称")
    private String buyerName;
    @Schema(description="卖家ID")
    private String sellerId;
    @Schema(description="卖家名称")
    private String sellerName;
    @Schema(description="承运商ID")
    private String carrierId;
    @Schema(description="承运商名称")
    private String carrierName;
    @Schema(description="出货点ID")
    private String warehouseId;
    @Schema(description="出货点名称")
    private String warehouseName;
    @Schema(description="收货地址ID")
    private String addressId;
    @Schema(description="详细收货地址")
    private String address;
    @Schema(description="异议说明")
    private String content;
    @Schema(description="配送数量")
    private BigDecimal quantity;
    @Schema(description="单位")
    private String unit;
    @Schema(description="附件批量新增对象")
    private AttListAddDTO attListAddDTO;
    @Schema(description="操作用户ID")
    private String operationUserId;
}

