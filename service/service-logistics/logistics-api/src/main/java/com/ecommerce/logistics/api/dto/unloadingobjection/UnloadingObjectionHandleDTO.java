
package com.ecommerce.logistics.api.dto.unloadingobjection;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="卸货异议处理DTO")
public class UnloadingObjectionHandleDTO {
    @Schema(description="卸货异议ID")
    private String objectionId;
    @Schema(description="处理内容")
    private String content;
    @Schema(description="操作用户ID")
    private String operationUserId;
    @Schema(description="操作用户名称")
    private String operationUserName;
}

