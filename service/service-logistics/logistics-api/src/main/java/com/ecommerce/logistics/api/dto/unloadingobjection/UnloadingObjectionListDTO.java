
package com.ecommerce.logistics.api.dto.unloadingobjection;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

@Data
@Schema(name="卸货异议列表DTO")
public class UnloadingObjectionListDTO {
    @Schema(description="卸货异议ID")
    private String objectionId;
    @Schema(description="卸货异议编号")
    private String objectionNum;
    @Schema(description="发起方角色类型")
    private String proposerType;
    @Schema(description="发起方名称")
    private String proposerName;
    @Schema(description="关联运单Id")
    private String relationBillId;
    @Schema(description="关联运单号")
    private String relationBillNum;
    @Schema(description="车牌号")
    private String vehicleNum;
    @Schema(description="配送数量")
    private BigDecimal quantity;
    @Schema(description="单位")
    private String unit;
    @Schema(description="详细收货地址")
    private String address;
    @Schema(description="完成时间")
    private Date completeTime;
    @Schema(description="创建时间")
    private Date createTime;
    @Schema(description="状态")
    private String status;
}

