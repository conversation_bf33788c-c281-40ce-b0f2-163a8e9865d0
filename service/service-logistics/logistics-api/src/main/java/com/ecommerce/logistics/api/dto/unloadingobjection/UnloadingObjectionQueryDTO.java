
package com.ecommerce.logistics.api.dto.unloadingobjection;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;
import lombok.Data;

@Data
@Schema(name="卸货异议新增DTO")
public class UnloadingObjectionQueryDTO {
    @Schema(description="卸货异议编号")
    private String objectionNum;
    @Schema(description="发起方角色类型")
    private String proposerType;
    @Schema(description="发起方名称")
    private String proposerName;
    @Schema(description="关联运单号")
    private String relationBillNum;
    @Schema(description="车牌号")
    private String vehicleNum;
    @Schema(description="状态")
    private String status;
    @Schema(description="开始创建时间")
    private Date beginTime;
    @Schema(description="结束创建时间")
    private Date endTime;
}

