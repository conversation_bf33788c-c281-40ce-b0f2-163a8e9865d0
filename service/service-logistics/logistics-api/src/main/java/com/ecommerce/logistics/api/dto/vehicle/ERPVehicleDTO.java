
package com.ecommerce.logistics.api.dto.vehicle;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(name="ERP车辆对象")
public class ERPVehicleDTO {
    @Schema(description="车辆ID")
    private String vehicleId;
    @Schema(description="归属人ID")
    private String userId;
    @Schema(description="归属人名")
    private String userName;
    @Schema(description="用户类型")
    private Integer userType;
    @Schema(description="车牌颜色")
    private String color;
    @Schema(description="车牌号")
    private String number;
    @Schema(description="运输类型")
    private String loadCategory;
    @Schema(description="车辆类型ID")
    private String vehicleTypeId;
    @Schema(description="核定载重")
    private BigDecimal loadCapacity;
    @Schema(description="车长")
    private BigDecimal length;
    @Schema(description="车宽")
    private BigDecimal width;
    @Schema(description="车高")
    private BigDecimal height;
    @Schema(description="车轴数枚举")
    private String axles;
    @Schema(description="认证状态")
    private Integer certificationStatus;
    @Schema(description="绑定司机accountId")
    private String bindDriverId;
    @Schema(description="司机名")
    private String driverName;
    @Schema(description="司机手机号码")
    private String driverPhone;
    @Schema(description="是否有GPS设备")
    private Integer isGpsDevice;
    @Schema(description="是否有GPS信号")
    private Integer signalFlag;
    @Schema(description="GPS厂商")
    private String gpsInfo;
    @Schema(description="GPSID")
    private String gpsId;
    @Schema(description="GPS设备号")
    private String gpsDeviceNumber;
    @Schema(description="SIM卡号")
    private String simNumber;
    @Schema(description="创建人ID")
    private String createUser;
    @Schema(description="自重")
    private BigDecimal selfCapacity;
}

