
package com.ecommerce.logistics.api.dto.vehicle;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@Schema(name="APP ERP车辆对象")
public class ExternalVehicleAppDTO {
    @Schema(description="车牌号码", required=true)
    @NotNull(message="车牌号码不能为空!")
    private @NotNull(message="车牌号码不能为空!") String number;
    @Schema(description="车辆ID")
    private String vehicleId;
    @Schema(description="车辆类型")
    private String type;
    @Schema(description="车牌颜色")
    private String color;
    @Schema(description="运输品类Id", required=true)
    private String transportCategoryId;
    @Schema(description="车长（米）")
    private BigDecimal length;
    @Schema(description="车宽（米）")
    private BigDecimal width;
    @Schema(description="车高（米）")
    private BigDecimal height;
    @Schema(description="司机姓名")
    private String driverName;
    @Schema(description="绑定司机id，是memberId")
    private String bindDriverId;
    @Schema(description="司机手机号")
    private String driverPhone;
    @Schema(description="司机身份证号")
    private String idNumber;
    @Schema(description="用户类型")
    private Integer userType;
    @Schema(description="品类名称")
    private String categoryName;
    @Schema(description="最大装载量")
    private BigDecimal maxLoadingCapacity;
    @Schema(description="车辆状态")
    private String status;
    @Schema(description="装卸能力")
    private Integer unloadAbility;
    @Schema(description="搬运能力")
    private Integer carryAbility;
}

