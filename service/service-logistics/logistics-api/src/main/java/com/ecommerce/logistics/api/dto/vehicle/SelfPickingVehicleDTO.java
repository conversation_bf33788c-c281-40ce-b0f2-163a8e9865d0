
package com.ecommerce.logistics.api.dto.vehicle;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

@Data
@Schema(name="自提车辆对象")
public class SelfPickingVehicleDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = -1005987331647045248L;
    @Schema(description="指派给车辆的数量,适用于自提在发货单批量指派")
    private BigDecimal quantity = BigDecimal.ZERO;
    @Schema(description="车辆ID", required=false)
    private String vehicleId;
    @Schema(description="车牌号码", required=false)
    private String number;
    @Schema(description="司机ID", required=false)
    private String driverId = "";
    @NotBlank(message="司机姓名")
    @Schema(description="司机姓名", required=false)
    private String driverName;
    @NotBlank(message="司机联系电话")
    @Schema(description="司机联系电话", required=false)
    private String driverPhone;
}

