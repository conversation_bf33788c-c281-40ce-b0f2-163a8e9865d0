
package com.ecommerce.logistics.api.dto.vehicle;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

@Data
@Schema(name="用户车辆列表查询对象")
public class UserVehicleListQueryDTO {
    @Schema(description="用户Id", required=true)
    private String userId;
    @NotBlank(message="用户类型不能为空")
    @Schema(description="用户类型(卖家,买家,承运商)", required=true)
    private Integer userType;
    @Schema(description="运单ID")
    private String waybillId;
    @Schema(description="调度单ID")
    private String dispatchBillId;
    @Schema(description="提货单ID")
    private String pickingBillId;
    @Schema(description="是否有GPS信号的标识")
    private Byte signalFlag;
    @Schema(description="是否有GPS设备")
    private Integer isGpsDevice;
    @Schema(description="运输品类ID")
    private String transportCategoryId;
    @Schema(description="是否禁用")
    private Integer disableFlg = 0;
    @Schema(description="车牌号码", required=false)
    private String number;
}

