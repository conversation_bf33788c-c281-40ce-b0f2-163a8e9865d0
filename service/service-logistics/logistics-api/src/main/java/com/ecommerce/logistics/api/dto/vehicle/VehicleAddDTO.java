
package com.ecommerce.logistics.api.dto.vehicle;

import com.ecommerce.logistics.api.dto.attachment.AttAddDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.List;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@Schema(name="车辆新增DTO对象")
public class VehicleAddDTO {
    @Schema(description="车辆Id")
    private String vehicleId;
    @Schema(description="车牌颜色", required=true)
    @NotBlank(message="车牌颜色不能为空!")
    private @NotBlank(message="车牌颜色不能为空!") String color;
    @Schema(description="车牌号码", required=true)
    @NotBlank(message="车牌号码不能为空!")
    private @NotBlank(message="车牌号码不能为空!") String number;
    @Schema(description="运输品类Id", required=true)
    @NotBlank(message="运输品类不能为空!")
    private @NotBlank(message="运输品类不能为空!") String transportCategoryId;
    @Schema(description="能源类型Id", required=true)
    @NotBlank(message="能源类型不能为空!")
    private @NotBlank(message="能源类型不能为空!") String energyTypeId;
    @Schema(description="载重", required=true)
    @NotNull(message="载重不能为空!")
    private @NotNull(message="载重不能为空!") BigDecimal loadCapacity;
    @Schema(description="宽度", required=true)
    @NotNull(message="宽度不能为空!")
    private @NotNull(message="宽度不能为空!") BigDecimal width;
    @Schema(description="高度", required=true)
    @NotNull(message="高度不能为空!")
    private @NotNull(message="高度不能为空!") BigDecimal height;
    @Schema(description="长度", required=true)
    @NotNull(message="长度不能为空!")
    private @NotNull(message="长度不能为空!") BigDecimal length;
    @Schema(description="轴数", required=true)
    private String axles;
    @Schema(description="绑定司机ID", required=true)
    private String bindDriverId;
    @Schema(description="司机名字", required=true)
    private String driverName;
    @Schema(description="司机手机号", required=true)
    private String driverPhone;
    @Schema(description="创建用户")
    @NotBlank(message="创建用户不能为空!")
    private @NotBlank(message="创建用户不能为空!") String createUser;
    @Schema(description="归属用户ID")
    @NotBlank(message="归属用户ID不能为空!")
    private @NotBlank(message="归属用户ID不能为空!") String userId;
    @Schema(description="用户类型")
    private Integer userType;
    @Schema(description="归属用户名字")
    private String userName;
    @Schema(description="SIM卡号")
    private String simNumber;
    @Schema(description="是否有GPS设备")
    private Integer isGpsDevice;
    @Schema(description="是否为默认车辆")
    private Integer isDefault;
    @Schema(description="车载设备号")
    private String gpsDeviceNumber;
    @Schema(description="gps厂商Id")
    private String gpsManufacturerId;
    @Schema(description="车辆类型ID", required=true)
    @NotBlank(message="车辆类型不能为空!")
    private @NotBlank(message="车辆类型不能为空!") String vehicleTypeId;
    @Schema(description="车辆自重", required=true)
    private BigDecimal selfCapacity;
    @Schema(description="车辆附件对象")
    private List<AttAddDTO> attListAddDTO;
    @Schema(description="操作用户ID", required=true)
    @NotBlank(message="操作用户不能为空!")
    private @NotBlank(message="操作用户不能为空!") String operationUserId;
    @Schema(description="操作用户名", required=true)
    private String operationUserName;
    @Schema(description="是否为司机App")
    private Integer isDriverApp = 0;
    @Schema(description="更新用户", required=true)
    private String updateUser;
    @Schema(description="最大装载量")
    private BigDecimal maxLoadingCapacity;
    @Schema(description="车辆状态")
    private String status;
    @Schema(description="装卸能力")
    private Integer unloadAbility;
    @Schema(description="搬运能力")
    private Integer carryAbility;
}

