
package com.ecommerce.logistics.api.dto.vehicle;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

@Data
@Schema(name="非管控车辆添加实体")
public class VehicleAppAddDTO {
    @Schema(description="司机名")
    private String driverName;
    @Schema(description="司机手机号码")
    private String driverPhone;
    @Schema(description="车辆类型ID")
    private String vehicleTypeId;
    @Schema(description="车牌号")
    private String number;
    @Schema(description="归属人名")
    private String userName;
    @Schema(description="归属人类型")
    private Integer userType;
    @Schema(description="归属人memberId")
    private String userId;
    @Schema(description="核定载重")
    private BigDecimal loadCapacity;
    @Schema(description="创建人ID")
    private String createUser;
    @Schema(description="是否监控")
    private Integer isMonitor;
    @Schema(description="是否默认车辆")
    private Integer isDefault;
    @Schema(description="是否收到gps信号")
    private Integer isGpsDevice;
    @Schema(description="车辆ID")
    private String vehicleId;
    @Schema(description="创建时间")
    private Date createTime;
    @Schema(description="审核状态")
    private String certificationStatus;
    @Schema(description="车牌颜色枚举编码")
    private String color;
    @Schema(description="操作用户ID", required=true)
    private String operationUserId;
    @Schema(description="操作用户名", required=true)
    private String operationUserName;
    @Schema(description="运输品类Id")
    private String transportCategoryId;
}

