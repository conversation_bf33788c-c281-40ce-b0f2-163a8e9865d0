
package com.ecommerce.logistics.api.dto.vehicle;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(name="承运商APP安排运力车辆信息")
public class VehicleAppCarrierDTO {
    @Schema(description="车牌号")
    private String number;
    @Schema(description="最大装载量(吨)")
    private BigDecimal maxLoadingCapacity;
    @Schema(description="车辆来源，1-自有车辆，2-外部车辆", required=true)
    private String vehicleSource;
}

