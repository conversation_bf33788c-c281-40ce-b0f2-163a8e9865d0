
package com.ecommerce.logistics.api.dto.vehicle;

import com.ecommerce.logistics.api.dto.attachment.AttListDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
@Schema(name="小程序查询车辆结果实体")
public class VehicleAppDataDTO {
    @Schema(description="车牌颜色")
    private String color;
    @Schema(description="车牌号")
    private String number;
    @Schema(description="能源类型Id")
    private String energyTypeId;
    @Schema(description="运输品类ID")
    private String transportCategoryId;
    @Schema(description="运输品类名")
    private String transportCategory;
    @Schema(description="核定载重")
    private BigDecimal loadCapacity;
    @Schema(description="车辆类型ID")
    private String vehicleTypeId;
    @Schema(description="车辆类型名")
    private String vehicleType;
    @Schema(description="是否有GPS设备")
    private Integer isGpsDevice;
    @Schema(description="是否有GPS信号")
    private Integer signalFlag;
    @Schema(description="GPS厂商ID")
    private String gpsManufacturerId;
    @Schema(description="GPS厂商名")
    private String gpsManufacturer;
    @Schema(description="终端设备号")
    private String gpsDeviceNumber;
    @Schema(description="SIM卡号")
    private String simNumber;
    @Schema(description="车辆ID")
    private String vehicleId;
    @Schema(description="绑定司机AccountId")
    private String bindDriverId;
    @Schema(description="司机名")
    private String driverName;
    @Schema(description="司机手机号码")
    private String driverPhone;
    @Schema(description="附件列表")
    private List<AttListDTO> attListDTOS;
    @Schema(description="车长")
    private BigDecimal length;
    @Schema(description="车宽")
    private BigDecimal width;
    @Schema(description="车高")
    private BigDecimal height;
    @Schema(description="自重")
    private BigDecimal selfCapacity;
    @Schema(description="用户类型 UserRoleEnum 1：买家 2：卖家 3：承运商 4: 个人司机 5: 平台")
    private Integer userType;
    @Schema(description="认证状态")
    private String certificationStatus;
    @Schema(description="认证失败原因")
    private String certificationFailReason = "";
    @Schema(description="是否禁用")
    private Integer disableFlg;
    @Schema(description="最大装载量")
    private BigDecimal maxLoadingCapacity;
    @Schema(description="车辆状态")
    private String status;
    @Schema(description="装卸能力")
    private Integer unloadAbility;
    @Schema(description="搬运能力")
    private Integer carryAbility;
}

