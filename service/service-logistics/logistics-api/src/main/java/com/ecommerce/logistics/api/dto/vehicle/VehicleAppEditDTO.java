
package com.ecommerce.logistics.api.dto.vehicle;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="更新非管控车辆实体")
public class VehicleAppEditDTO {
    @Schema(description="司机名")
    private String driverName;
    @Schema(description="司机手机号码")
    private String driverPhone;
    @Schema(description="车辆类型ID")
    private String vehicleTypeId;
    @Schema(description="车牌号")
    private String number;
    @Schema(description="车辆ID")
    private String vehicleId;
    @Schema(description="核定载重")
    private String loadCapacity;
    @Schema(description="车牌颜色")
    private String color;
    @Schema(description="更新人ID")
    private String updateUser;
    @Schema(description="是否有GPS设备")
    private Integer isGpsDevice;
    @Schema(description="是否为默认车辆")
    private Integer isDefault;
    @Schema(description="操作用户ID", required=true)
    private String operationUserId;
    @Schema(description="操作用户名", required=true)
    private String operationUserName;
}

