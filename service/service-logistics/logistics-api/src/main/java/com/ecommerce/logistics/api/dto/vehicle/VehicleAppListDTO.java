
package com.ecommerce.logistics.api.dto.vehicle;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(name="App车辆列表")
public class VehicleAppListDTO {
    @Schema(description="绑定司机AccountId")
    private String bindDriverId;
    @Schema(description="司机名")
    private String driverName;
    @Schema(description="司机手机号码")
    private String driverPhone;
    @Schema(description="车辆类型ID")
    private String vehicleTypeId;
    @Schema(description="车辆类型名")
    private String vehicleType;
    @Schema(description="车牌号")
    private String number;
    @Schema(description="核定载重")
    private BigDecimal loadCapacity;
    @Schema(description="是否为默认车辆")
    private Integer isDefault;
    @Schema(description="是否有GPS设备")
    private Integer isGpsDevice;
    @Schema(description="是否有GPS信号")
    private Integer signalFlag;
    @Schema(description="车辆ID")
    private String vehicleId;
    @Schema(description="车牌颜色枚举code")
    private String color;
    @Schema(description="是否禁用")
    private Integer disableFlg;
}

