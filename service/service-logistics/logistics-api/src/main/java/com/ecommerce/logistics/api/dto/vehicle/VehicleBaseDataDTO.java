
package com.ecommerce.logistics.api.dto.vehicle;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;
import lombok.Data;

@Data
@Schema(name="车辆基础数据实体")
public class VehicleBaseDataDTO {
    @Schema(description="车辆ID")
    private String vehicleId;
    @Schema(description="车辆类型ID")
    private String vehicleTypeId;
    @Schema(description="车辆类型名")
    private String vehicleType;
    @Schema(description="车牌号")
    private String number;
    @Schema(description="司机名")
    private String driverName;
    @Schema(description="司机手机号码")
    private String driverPhone;
    @Schema(description="认证状态")
    private String certificationStatus;
    @Schema(description="是否认证")
    private Boolean status;
    @Schema(description="是否监控")
    private Boolean isMonitor;
    @Schema(description="是否有GPS设备")
    private Integer isGpsDevice;
    @Schema(description="是否有GPS信号")
    private Integer signalFlag;
    @Schema(description="运输品类ID列表")
    private List<String> transportCategoryIdList;
    @Schema(description="归属人类型,见UserRoleEnum: 1.买家,2.卖家,3.承运商,4.个人司机,5.平台")
    private Integer userType;
    @Schema(description="是否禁用")
    private Integer disableFlg;
    @Schema(description="车牌颜色")
    private String color;
    @Schema(description="是否为默认车辆")
    private Integer isDefault;

    private String driverId;
}

