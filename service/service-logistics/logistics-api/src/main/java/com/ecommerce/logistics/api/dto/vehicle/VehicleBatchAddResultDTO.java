
package com.ecommerce.logistics.api.dto.vehicle;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Schema(name="批量添加车辆结果DTO")
@AllArgsConstructor
@NoArgsConstructor
public class VehicleBatchAddResultDTO {
    @Schema(description="校验未通过列表")
    private List<String> checkFailList;
    @Schema(description="新增失败映射 : 失败原因 -> 新增失败列表")
    private Map<String, List<String>> insertFailMap;
    @Schema(description="新增成功列表")
    private List<String> insertSuccessList;
    @Schema(description="覆盖失败映射 : 失败原因 -> 覆盖失败列表")
    private Map<String, List<String>> updateFailMap;
    @Schema(description="覆盖成功列表")
    private List<String> updateSuccessList;
}

