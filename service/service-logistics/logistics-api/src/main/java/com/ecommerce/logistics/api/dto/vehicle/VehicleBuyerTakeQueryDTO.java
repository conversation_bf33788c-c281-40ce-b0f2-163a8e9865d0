
package com.ecommerce.logistics.api.dto.vehicle;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="买家自提车辆列表查询对象")
public class VehicleBuyerTakeQueryDTO {
    @Schema(description="归属人ID")
    private String userId;
    @Schema(description="用户类型")
    private Integer userType;
    @Schema(description="车牌号")
    private String number;
    @Schema(description="运输品类ID")
    private String transportCategoryId;
    @Schema(description="是否管控")
    private boolean needMonitor = false;
}

