
package com.ecommerce.logistics.api.dto.vehicle;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

@Data
@Schema(name="车辆请求更改状态对象")
public class VehicleCertificationStatusDTO {
    @NotBlank(message="车辆Id不能为空")
    @Schema(description="车俩Id", required=true)
    private String vehicleId;
    @NotBlank(message="更新用户不能为空")
    @Schema(description="更新用户", required=true)
    private String updateUser;
    @Schema(description="驳回原因", required=false)
    private String certificationFailReason;
    @Schema(description="认证状态")
    private String certificationStatus;
    @Schema(description="操作用户ID", required=true)
    private String operationUserId;
    @Schema(description="操作用户名", required=true)
    private String operationUserName;
}

