package com.ecommerce.logistics.api.dto.vehicle;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("车辆新增DTO对象 管控和非管控")
public class VehicleCommonDTO
{
    /** 非管控车辆 start */
    @ApiModelProperty("车辆Id")
    private String vehicleId;

    @ApiModelProperty("是否为管控车辆 1：是、0：否")
    private Integer isMonitor;

    @ApiModelProperty("归属用户ID")
    private String userId;

    @ApiModelProperty("归属用户名字")
    private String userName;

    @ApiModelProperty("用户类型")
    private Integer userType;

    @ApiModelProperty("司机名字")
    private String driverName;

    @ApiModelProperty("司机手机号")
    private String driverPhone;

    @ApiModelProperty("载重")
    private BigDecimal loadCapacity;

    @ApiModelProperty("车牌颜色")
    private String color;

    @ApiModelProperty("车牌号码")
    private String number;

    @ApiModelProperty("车辆类型ID")
    private String vehicleTypeId;
    /** 非管控车辆 end */


    /** 管控车辆 start */
    @ApiModelProperty("运输品类ID")
    private String transportCategoryId;

    @ApiModelProperty("GPS厂商Id")
    private String gpsManufacturerId;

    @ApiModelProperty("SIM卡号")
    private String simNumber;

    @ApiModelProperty("终端设备号")
    private String gpsDeviceNumber;

    @ApiModelProperty("长度")
    private BigDecimal length;

    @ApiModelProperty("宽度")
    private BigDecimal width;

    @ApiModelProperty("高度")
    private BigDecimal height;

    @ApiModelProperty("车辆自重")
    private BigDecimal selfCapacity;
    /** 管控车辆 end */

    @ApiModelProperty("操作用户ID")
    private String operationUserId;

    @ApiModelProperty("操作用户名")
    private String operationUserName;
}
