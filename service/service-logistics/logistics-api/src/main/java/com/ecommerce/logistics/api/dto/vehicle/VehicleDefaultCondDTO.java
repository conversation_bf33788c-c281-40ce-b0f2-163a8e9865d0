
package com.ecommerce.logistics.api.dto.vehicle;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;
import lombok.Data;

@Data
@Schema(name="车辆默认信息查询条件")
public class VehicleDefaultCondDTO {
    @Schema(description="归属人ID")
    private String userId;
    @Schema(description="归属人类型")
    private Integer userType;
    @Schema(description="运输品类集合")
    private List<String> transportCategoryIdList;
    @Schema(description="是否有GPS设备")
    private Integer isGpsDevice;
    @Schema(description="是否收到信号")
    private Integer signalFlag;
    @Schema(description="最小的version值")
    private Long minVersion = 0L;
    @Schema(description="是否必须有司机ID")
    private Integer mustDriverId;
}

