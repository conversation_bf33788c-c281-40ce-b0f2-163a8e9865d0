
package com.ecommerce.logistics.api.dto.vehicle;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="默认指派车辆的信息")
public class VehicleDefaultResDTO {
    @Schema(description="车辆ID")
    private String vehicleId;
    @Schema(description="车型名")
    private String vehicleType;
    @Schema(description="车牌号")
    private String number;
    @Schema(description="司机ID")
    private String driverId;
    @Schema(description="司机名")
    private String driverName;
    @Schema(description="司机手机号")
    private String driverPhone;
    @Schema(description="运输品类ID")
    private String transportCategoryId;
}

