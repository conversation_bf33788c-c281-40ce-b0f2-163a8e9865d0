
package com.ecommerce.logistics.api.dto.vehicle;

import com.ecommerce.logistics.api.dto.attachment.AttListDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

@Data
@Schema(name="车辆详情实体")
public class VehicleDetailDTO {
    @Schema(description="车辆ID", required=false)
    private String vehicleId;
    @Schema(description="车牌颜色", required=true)
    private String color;
    @Schema(description="车牌号码", required=true)
    private String number;
    @Schema(description="运输品类ID", required=true)
    private String transportCategoryId;
    @Schema(description="运输品类名称", required=true)
    private String categoryName;
    @Schema(description="车辆类型", required=true)
    private String type;
    @Schema(description="车辆类型Id", required=true)
    private String vehicleTypeId;
    @Schema(description="车辆类型", required=true)
    private String vehicleType;
    @Schema(description="载重", required=true)
    private BigDecimal loadCapacity;
    @Schema(description="自重")
    private BigDecimal selfCapacity;
    @Schema(description="宽度", required=true)
    private BigDecimal width;
    @Schema(description="高度", required=true)
    private BigDecimal height;
    @Schema(description="长度", required=true)
    private BigDecimal length;
    @Schema(description="轴数", required=true)
    private String axles;
    @Schema(description="绑定司机ID", required=true)
    private String bindDriverId;
    @Schema(description="司机信息", required=true)
    private String driverName;
    @Schema(description="司机手机号", required=true)
    private String driverPhone;
    @Schema(description="SIM卡号")
    private String simNumber;
    @Schema(description="车载设备号")
    private String gpsDeviceNumber;
    @Schema(description="创建用户")
    private String createUser;
    @Schema(description="归属用户ID")
    private String userId;
    @Schema(description="用户类型")
    private Integer userType;
    @Schema(description="更新用户")
    private String updateUser;
    @Schema(description="创建时间")
    private Date createTime;
    @Schema(description="gps厂商Id")
    private String gpsManufacturerId;
    @Schema(description="gps厂商名称")
    private String gpsManufacturer;
    @Schema(description="车辆附件信息")
    private List<AttListDTO> attListDTOS;
    @Schema(description="是否有GPS设备")
    private Integer isGpsDevice;
    @Schema(description="是否有GPS信号")
    private Integer signalFlag;
    @Schema(description="是否为默认车辆")
    private Integer isDefault;
    @Schema(description="是否监控")
    private Integer isMonitor;
    @Schema(description="能源类型Id", required=true)
    private String energyTypeId;
    @Schema(description="最大装载量")
    private BigDecimal maxLoadingCapacity;
    @Schema(description="车辆状态")
    private String status;
    @Schema(description="装卸能力")
    private Integer unloadAbility;
    @Schema(description="搬运能力")
    private Integer carryAbility;
    @Schema(description="是否支付了保证金")
    private Integer depositPayedFlag;
}

