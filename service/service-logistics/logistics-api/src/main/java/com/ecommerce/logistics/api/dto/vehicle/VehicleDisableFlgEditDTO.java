
package com.ecommerce.logistics.api.dto.vehicle;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

@Data
@Schema(name="修改车辆是否禁用DTO对象")
public class VehicleDisableFlgEditDTO {
    @NotBlank(message="车辆ID不能为空")
    @Schema(description="车辆ID", required=true)
    private String vehicleId;
    @Schema(description="是否禁用", required=true)
    private Integer disableFlg = 0;
    @Schema(description="更新人ID")
    private String updateUser;
    @Schema(description="操作用户ID", required=true)
    private String operationUserId;
    @Schema(description="操作用户名", required=true)
    private String operationUserName;
}

