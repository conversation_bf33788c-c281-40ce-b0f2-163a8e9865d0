
package com.ecommerce.logistics.api.dto.vehicle;

import com.ecommerce.logistics.api.dto.attachment.AttAddDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.List;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@Schema(name="车辆编辑DTO对象")
public class VehicleEditDTO {
    @NotBlank(message="车辆ID不能为空")
    @Schema(description="车辆ID", required=true)
    private String vehicleId;
    @NotBlank(message="车牌颜色不能为空")
    @Schema(description="车牌颜色", required=true)
    private String color;
    @NotBlank(message="车牌号码不能为空")
    @Schema(description="车牌号码", required=true)
    private String number;
    @NotBlank(message="运输品类Id不能为空")
    @Schema(description="运输品类Id", required=true)
    private String transportCategoryId;
    @Schema(description="能源类型Id", required=true)
    private String energyTypeId;
    @NotBlank(message="车辆类型Id不能为空")
    @Schema(description="车辆类型Id", required=true)
    private String vehicleTypeId;
    @NotNull(message="载重不能为空")
    @Schema(description="载重", required=true)
    private @NotNull(message="载重不能为空") BigDecimal loadCapacity;
    @NotNull(message="宽度不能为空")
    @Schema(description="宽度", required=true)
    private @NotNull(message="宽度不能为空") BigDecimal width;
    @NotNull(message="高度不能为空")
    @Schema(description="高度", required=true)
    private @NotNull(message="高度不能为空") BigDecimal height;
    @NotNull(message="长度不能为空")
    @Schema(description="长度", required=true)
    private @NotNull(message="长度不能为空") BigDecimal length;
    @NotNull(message="轴数不能为空")
    @Schema(description="轴数", required=true)
    private @NotNull(message="轴数不能为空") String axles;
    @NotBlank(message="SIM卡号不能为空")
    @Schema(description="sim卡号", required=true)
    private String simNumber;
    @NotBlank(message="更新用户不能为空")
    @Schema(description="更新用户", required=true)
    private String updateUser;
    @NotBlank(message="车辆自重不能为空")
    @Schema(description="车辆自重", required=true)
    private BigDecimal selfCapacity;
    @NotBlank(message="gps厂商Id不能为空")
    @Schema(description="gps厂商Id", required=true)
    private String gpsManufacturerId;
    @NotBlank(message="车载设备号不能为空")
    @Schema(description="车载设备号", required=true)
    private String gpsDeviceNumber;
    @Schema(description="是否有GPS设备")
    private Integer isGpsDevice;
    @Schema(description="是否为默认车辆")
    private Integer isDefault;
    @Schema(description="绑定司机ID", required=false)
    private String bindDriverId;
    @Schema(description="司机名字", required=false)
    private String driverName;
    @Schema(description="司机手机号")
    private String driverPhone;
    @Schema(description="车辆附件对象")
    private List<AttAddDTO> attListAddDTO;
    @Schema(description="操作用户ID", required=true)
    private String operationUserId;
    @Schema(description="操作用户名", required=true)
    private String operationUserName;
    @Schema(description="是否为司机App")
    private Integer isDriverApp = 0;
    @Schema(description="最大装载量")
    private BigDecimal maxLoadingCapacity;
    @Schema(description="车辆状态")
    private String status;
    @Schema(description="装卸能力")
    private Integer unloadAbility;
    @Schema(description="搬运能力")
    private Integer carryAbility;
}

