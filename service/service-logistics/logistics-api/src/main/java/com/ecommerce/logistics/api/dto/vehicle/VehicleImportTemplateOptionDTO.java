
package com.ecommerce.logistics.api.dto.vehicle;

import com.ecommerce.logistics.api.dto.vehicletype.VehicleTypeOptionDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Map;
import lombok.Data;

@Data
@Schema(name="车辆导入模板下拉列表DTO")
public class VehicleImportTemplateOptionDTO {
    @Schema(description="车牌颜色下拉列表")
    private Map<String, String> plateColorMap;
    @Schema(description="车辆类型下拉列表")
    private Map<String, VehicleTypeOptionDTO> vehicleTypeMap;
    @Schema(description="运输品类下拉列表")
    private Map<String, String> transportCategoryMap;
    @Schema(description="GPS厂商下拉列表")
    private Map<String, String> gpsManufacturerMap;
}

