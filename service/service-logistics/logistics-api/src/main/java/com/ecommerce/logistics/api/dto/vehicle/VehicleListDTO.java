
package com.ecommerce.logistics.api.dto.vehicle;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@NoArgsConstructor
@Schema(name = "车辆列表对象")
public class VehicleListDTO implements Serializable {
    @Schema(description = "车辆ID", required = true)
    private String vehicleId;
    @Schema(description = "车牌颜色", required = true)
    private String color;
    @Schema(description = "车牌号码", required = true)
    private String number;
    @Schema(description = "运输品类", required = true)
    private String categoryName;
    @Schema(description = "运输品类Id")
    private String transportCategoryId;
    @Schema(description = "车辆类型名称", required = true)
    private String typeName;
    @Schema(description = "载重(核定载质量)", required = true)
    private BigDecimal loadCapacity;
    @Schema(description = "车宽", required = true)
    private BigDecimal width;
    @Schema(description = "车高", required = true)
    private BigDecimal height;
    @Schema(description = "长度", required = true)
    private BigDecimal length;
    @Schema(description = "gps厂商名称", required = true)
    private String gpsName;
    @Schema(description = "车载设备号")
    private String gpsDeviceNumber;
    @Schema(description = "SIM卡号")
    private String simNumber;
    @Schema(description = "归属人memberId")
    private String userId;
    @Schema(description = "归属用户名")
    private String userName;
    @Schema(description = "归属用户类型")
    private String userType;
    @Schema(description = "车辆类型Id")
    private String vehicleTypeId;
    @Schema(description = "认证状态（审核状态）")
    private String certificationStatus;
    @Schema(description = "gps厂商Id")
    private String gpsManufacturerId;
    @Schema(description = "车辆自重")
    private BigDecimal selfCapacity;
    @Schema(description = "车辆轴数")
    private String axles;
    @Schema(description = "车辆绑定的AccountId")
    private String bindDriverId;
    @Schema(description = "绑定司机名字")
    private String driverName;
    @Schema(description = "绑定司机电话")
    private String driverPhone;
    @Schema(description = "驳回原因")
    private String certificationFailReason;
    @Schema(description = "是否为管控车辆")
    private Integer isMonitor;
    @Schema(description = "是否可以收到GPS信号（车载定位信号）")
    private Integer signalFlag;
    @Schema(description = "是否有GPS设备")
    private Integer isGpsDevice;
    @Schema(description = "是否为默认车辆")
    private Integer isDefault;
    @Schema(description = "车辆来源,0-其他，1-自有车辆，2-外部车辆")
    private String vehicleSource;
    @Schema(description = "是否绑定")
    private String bindStatus;
    @Schema(description = "是否禁用")
    private Integer disableFlg;
    @Schema(description = "身份证号码")
    private String idNumber;
    @Schema(description = "最大装载量")
    private BigDecimal maxLoadingCapacity;
    @Schema(description = "车辆状态")
    private String status;
    @Schema(description = "装卸能力")
    private Integer unloadAbility;
    @Schema(description = "搬运能力")
    private Integer carryAbility;
}

