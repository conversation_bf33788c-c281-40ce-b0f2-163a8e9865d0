
package com.ecommerce.logistics.api.dto.vehicle;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;
import lombok.Data;

@Data
@Schema(name="车辆列表查询对象")
public class VehicleListQueryDTO {
    @Schema(description="车牌颜色", required=false)
    private String color;
    @Schema(description="车牌号码", required=false)
    private String number;
    @Schema(description="运输品类Id", required=false)
    private String transportCategoryId;
    @Schema(description="车辆类型Id", required=false)
    private String vehicleTypeId;
    @Schema(description="认证状态", required=false)
    private List<String> statusList;
    @Schema(description="归属用户ID", required=false)
    private String userId;
    @Schema(description="归属用户类型", required=false)
    private Integer userType;
    @Schema(description="管控状态", required=false)
    private Integer isMonitor;
    @Schema(description="GPS信号")
    private Integer signalFlag;
    @Schema(description="是否有GPS设备")
    private Integer isGpsDevice;
    @Schema(description="司机名字", required=false)
    private String driverName;
    @Schema(description="车辆来源,0-其他，1-自有车辆，2-外部车辆")
    private String vehicleSource;
    @Schema(description="是否绑定，0-解绑，1-绑定")
    private String bindStatus;
    @Schema(description="是否禁用 0否 1是")
    private Integer disableFlg;
    @Schema(description="是否绑定了司机0:否,1:是")
    private Integer hasBindDriver;
}

