
package com.ecommerce.logistics.api.dto.vehicle;

import com.ecommerce.logistics.api.dto.attachment.AttAddDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
@Schema(name="手机添加司机实体")
public class VehicleMobileAppAddDTO {
    @Schema(description="车牌颜色")
    private String color;
    @Schema(description="车牌号")
    private String number;
    @Schema(description="运输品类ID")
    private String transportCategoryId;
    @Schema(description="运输品类")
    private BigDecimal loadCapacity;
    @Schema(description="车辆类型ID")
    private String vehicleTypeId;
    @Schema(description="GPS厂商ID")
    private String gpsManufacturerId;
    @Schema(description="GPS终端设备号")
    private String gpsDeviceNumber;
    @Schema(description="SIM卡号")
    private String simNumber;
    @Schema(description="司机名")
    private String driverName;
    @Schema(description="司机手机号")
    private String driverPhone;
    @Schema(description="归属人ID")
    private String userId;
    @Schema(description="附件列表")
    private List<AttAddDTO> attList;
}

