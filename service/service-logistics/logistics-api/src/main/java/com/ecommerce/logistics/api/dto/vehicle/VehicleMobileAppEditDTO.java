
package com.ecommerce.logistics.api.dto.vehicle;

import com.ecommerce.logistics.api.dto.attachment.AttAddDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
@Schema(name="司机App编辑车辆实体")
public class VehicleMobileAppEditDTO {
    @Schema(description="车辆ID")
    private String vehicleId;
    @Schema(description="车牌颜色")
    private String color;
    @Schema(description="车牌号")
    private String number;
    @Schema(description="运输品类ID")
    private String transportCategoryId;
    @Schema(description="核定载重")
    private BigDecimal loadCapacity;
    @Schema(description="车型ID")
    private String vehicleTypeId;
    @Schema(description="GPS厂商ID")
    private String gpsManufacturerId;
    @Schema(description="终端设备号")
    private String gpsDeviceNumber;
    @Schema(description="SIM卡号")
    private String simNumber;
    @Schema(description="附件列表")
    private List<AttAddDTO> attList;
}

