
package com.ecommerce.logistics.api.dto.vehicle;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

@Data
@Schema(name="车辆修改默认实体")
public class VehicleModifyIsDefaultDTO {
    @NotBlank(message="userId不能为空")
    private String userId;
    @NotBlank(message="vehicleId不能为空")
    private String vehicleId;
    @Schema(description="操作用户ID", required=true)
    private String operationUserId;
    @Schema(description="操作用户名", required=true)
    private String operationUserName;
}

