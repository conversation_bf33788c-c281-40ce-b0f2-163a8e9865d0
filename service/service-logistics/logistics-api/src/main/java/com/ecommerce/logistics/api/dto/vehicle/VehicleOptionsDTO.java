
package com.ecommerce.logistics.api.dto.vehicle;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="车辆下拉列表实体")
public class VehicleOptionsDTO {
    @Schema(description="列表后台传入值")
    private String value;
    @Schema(description="列表显示标签")
    private String label;
    @Schema(description="归属人类型")
    private Integer userType;
    @Schema(description="归属人ID")
    private String userId;
    @Schema(description="归属人名称")
    private String userName;
}

