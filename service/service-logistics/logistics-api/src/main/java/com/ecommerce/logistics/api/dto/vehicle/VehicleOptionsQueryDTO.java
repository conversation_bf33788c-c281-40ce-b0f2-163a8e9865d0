
package com.ecommerce.logistics.api.dto.vehicle;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;
import lombok.Data;

@Data
@Schema(name="车辆下拉列表查询条件实体")
public class VehicleOptionsQueryDTO {
    @Schema(description="归属人ID")
    private String userId;
    @Schema(description="用户类型")
    private Integer userType;
    @Schema(description="包含的用户类型")
    private List<Integer> includeUserTypeList;
    @Schema(description="排除的用户类型")
    private List<Integer> excludeUserTypeList;
    @Schema(description="查询关键字")
    private String keyword;
    @Schema(description="认证状态")
    private String certificationStatus;
    @Schema(description="运单ID")
    private String waybillId;
    @Schema(description="调度单ID")
    private String dispatchBillId;
    @Schema(description="提货单ID")
    private String pickingBillId;
    @Schema(description="运输品类")
    private String transportCategoryId;
    @Schema(description="是否有GPS信号的标识")
    private Byte signalFlag;
    private Integer limitSize;
    private Integer ignoreIdFlag = 0;
    @Schema(description="车牌号列表")
    private List<String> vehicleNumList;
}

