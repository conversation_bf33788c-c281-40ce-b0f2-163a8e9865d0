
package com.ecommerce.logistics.api.dto.vehicle;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="车辆查询条件实体")
public class VehicleQueryConditionDTO {
    @Schema(description="车牌号")
    private String vehicleNum;
    @Schema(description="认证状态")
    private String certificationStatus;
    @Schema(description="归属人")
    private String userId;
    @Schema(description="归属人类型")
    private Integer userType;
}

