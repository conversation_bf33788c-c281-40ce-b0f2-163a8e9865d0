
package com.ecommerce.logistics.api.dto.vehicle;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="车辆删除实体")
public class VehicleRemoveDTO {
    @Schema(description="车辆ID")
    private String vehicleId;
    @Schema(description="更新人ID")
    private String updateUser;
    @Schema(description="操作用户ID", required=true)
    private String operationUserId;
    @Schema(description="操作用户名", required=true)
    private String operationUserName;
}

