
package com.ecommerce.logistics.api.dto.vehicle;

import io.swagger.v3.oas.annotations.media.Schema;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@Schema(name="修改车辆是否收到信号实体")
public class VehicleSignalEditDTO {
    @NotBlank(message="车牌号不能为空")
    @Schema(description="车牌号")
    String plateNumber;
    @NotNull(message="信号标识不能为空")
    @Schema(description="信号标识")
    private @NotNull(message="信号标识不能为空") Byte signalFlag = 0;
}

