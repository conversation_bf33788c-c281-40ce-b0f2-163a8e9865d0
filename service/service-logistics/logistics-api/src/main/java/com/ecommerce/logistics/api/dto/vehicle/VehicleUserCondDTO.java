
package com.ecommerce.logistics.api.dto.vehicle;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;
import lombok.Data;

@Data
@Schema(name="车辆用户条件实体")
public class VehicleUserCondDTO {
    @Schema(description="归属人MemberId")
    private String userId;
    @Schema(description="归属人类型")
    private Integer userType;
    @Schema(description="管控商品标识")
    private Integer specialFlag;
    @Schema(description="运输品类ID集合")
    private List<String> transportIdList;
    @Schema(description="是否禁用 0否 1是")
    private Integer disableFlg;
}

