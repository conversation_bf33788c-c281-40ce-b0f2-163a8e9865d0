
package com.ecommerce.logistics.api.dto.vehiclecert;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="行驶证信息返回结果")
public class DrivingLicenseDTO {
    @Schema(description="资质id")
    private String certId;
    @Schema(description="行驶证档案编号")
    private String drivingLicenseNum;
    @Schema(description="车辆类型（行驶证OCR识别返回数据）")
    private String carType;
    @Schema(description="使用性质（行驶证OCR识别返回数据）")
    private String useCharacter;
    @Schema(description="车辆识别代码（行驶证OCR识别返回数据）")
    private String vin;
    @Schema(description="核定载质量(吨)")
    private String approvedLoadingQuality;
    @Schema(description="总质量(吨)")
    private String totalQuality;
    @Schema(description="车辆载重大于4.5吨则需要道路运输经营许可证(0-不需要上传，1-需要上传)")
    private Integer needCert;
    @Schema(description="所有人")
    private String owner;
    @Schema(description="注册日期")
    private String registTime;
    @Schema(description="发证日期")
    private String issuanceTime;
    @Schema(description="检验有效期截止时间")
    private String testEffectiveEndTime;
    @Schema(description="发证机关")
    private String issuingAuthority;
}

