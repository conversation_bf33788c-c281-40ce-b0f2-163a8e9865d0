
package com.ecommerce.logistics.api.dto.vehiclecert;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="行驶证信息DTO")
public class DrivingLicenseInfoDTO {
    @Schema(description="车辆Id")
    private String vehicleId;
    @Schema(description="归属会员Id")
    private String userId;
    @Schema(description="行驶证信息")
    private String certJson;
    @Schema(description="行驶证DTO")
    private DrivingLicenseDTO drivingLicenseDTO;
}

