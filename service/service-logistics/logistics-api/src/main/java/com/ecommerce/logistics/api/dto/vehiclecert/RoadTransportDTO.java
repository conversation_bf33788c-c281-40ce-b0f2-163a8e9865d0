
package com.ecommerce.logistics.api.dto.vehiclecert;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="道路运输证(营运证)信息返回结果")
public class RoadTransportDTO {
    @Schema(description="资质id")
    private String certId;
    @Schema(description="车辆载重大于4.5吨则需要道路运输经营许可证(0-不需要上传，1-需要上传)")
    private Integer needCert;
    @Schema(description="道路运输证号")
    private String roadTransportNo;
    @Schema(description="经营许可证号")
    private String roadManagementNo;
    @Schema(description="有效期截止时间")
    private String effectiveEndTime;
}

