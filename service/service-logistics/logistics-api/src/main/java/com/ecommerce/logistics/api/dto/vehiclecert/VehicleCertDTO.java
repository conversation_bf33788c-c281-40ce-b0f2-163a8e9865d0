
package com.ecommerce.logistics.api.dto.vehiclecert;

import com.ecommerce.logistics.api.dto.vehicle.VehicleAppDataDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="车辆认证信息返回结果")
public class VehicleCertDTO {
    @Schema(description="车辆信息返货结果")
    private VehicleAppDataDTO vehicleAppDataDTO;
    @Schema(description="行驶证信息返回结果")
    private DrivingLicenseDTO drivingLicenseDTO;
    @Schema(description="道路运输证(营运证)信息返回结果")
    private RoadTransportDTO roadTransportDTO;
}

