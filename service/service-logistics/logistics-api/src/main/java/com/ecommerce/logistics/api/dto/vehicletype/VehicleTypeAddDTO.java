
package com.ecommerce.logistics.api.dto.vehicletype;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(name="车辆类型新增DTO对象")
public class VehicleTypeAddDTO {
    @Schema(description="类型名称", required=true, example="6轴罐车")
    private String typeName;
    @Schema(description="类型备注", required=true, example="6轴罐车")
    private String note;
    @Schema(description="轴数", required=true, example="031001")
    private String axles;
    @Schema(description="车厢类型", required=true, example="031101")
    private String carriageType;
    @Schema(description="最大载重", required=true, example="32")
    private BigDecimal maxLoadCapacity;
    @Schema(description="最大自重", required=true, example="17")
    private BigDecimal maxSelfCapacity;
    @Schema(description="最大方量", required=true, example="6")
    private BigDecimal maxVolume;
    @Schema(description="最大长度", required=true, example="12")
    private BigDecimal maxLength;
    @Schema(description="最大宽度", required=true, example="2.55")
    private BigDecimal maxWidth;
    @Schema(description="最大高度", required=true, example="3.45")
    private BigDecimal maxHeight;
    @Schema(description="操作人ID", required=true, example="10010")
    private String operatorUserId;
    @Schema(description="操作人姓名", required=true, example="王大锤")
    private String operatorUserName;
    @Schema(description="配送类型ID")
    private String transportCategoryId;
    @Schema(description="保证金")
    private BigDecimal depositAmount;
    @Schema(description="是否需要校验车牌0-不校验，1-校验")
    private Integer verificationNumber;
}

