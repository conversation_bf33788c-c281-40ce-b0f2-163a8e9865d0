
package com.ecommerce.logistics.api.dto.vehicletype;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(name="车辆类型详情DTO对象")
public class VehicleTypeDetailDTO {
    @Schema(description="车辆类型ID", required=false)
    private String vehicleTypeId;
    @Schema(description="类型名称", required=true)
    private String typeName;
    @Schema(description="类型备注", required=true)
    private String note;
    @Schema(description="轴数", required=true)
    private String axles;
    @Schema(description="车厢类型", required=true)
    private String carriageType;
    @Schema(description="最大载重", required=true)
    private BigDecimal maxLoadCapacity;
    @Schema(description="最大自重", required=true)
    private BigDecimal maxSelfCapacity;
    @Schema(description="最大方量", required=true)
    private BigDecimal maxVolume;
    @Schema(description="最大长度", required=true)
    private BigDecimal maxLength;
    @Schema(description="最大宽度", required=true)
    private BigDecimal maxWidth;
    @Schema(description="最大高度", required=true)
    private BigDecimal maxHeight;
    @Schema(description="配送类型ID")
    private String transportCategoryId;
    @Schema(description="是否不限车型，0-不是，1-不限车型")
    private Integer unlimiteType;
    @Schema(description="保证金")
    private BigDecimal depositAmount;
}

