
package com.ecommerce.logistics.api.dto.vehicletype;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(name="车辆类型列表对象")
public class VehicleTypeListDTO {
    @Schema(description="车辆类型ID", required=true)
    private String vehicleTypeId;
    @Schema(description="类型名称", required=true)
    private String typeName;
    @Schema(description="类型备注", required=true)
    private String note;
    @Schema(description="轴数", required=true)
    private String axles;
    @Schema(description="车厢类型", required=true)
    private String carriageType;
    @Schema(description="最大载重", required=true)
    private BigDecimal maxLoadCapacity;
    @Schema(description="最大自重", required=true)
    private BigDecimal maxSelfCapacity;
    @Schema(description="最大方量", required=true)
    private BigDecimal maxVolume;
    @Schema(description="最大长度", required=true)
    private BigDecimal maxLength;
    @Schema(description="最大宽度", required=true)
    private BigDecimal maxWidth;
    @Schema(description="最大高度", required=true)
    private BigDecimal maxHeight;
    @Schema(description="运输品类Id", required=true)
    private String transportCategoryId;
    @Schema(description="保证金")
    private BigDecimal depositAmount;
    @Schema(description="是否需要校验车牌0-不校验，1-校验")
    private Integer verificationNumber;
}

