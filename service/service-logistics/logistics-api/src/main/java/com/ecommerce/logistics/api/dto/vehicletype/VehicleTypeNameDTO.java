
package com.ecommerce.logistics.api.dto.vehicletype;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="车型名实体")
public class VehicleTypeNameDTO {
    @Schema(description="车型名")
    private String typeName;
    @Schema(description="车型ID")
    private String vehicleTypeId;
    @Schema(description="轴数")
    private String axles;
    @Schema(description="车厢类型")
    private String carriageType;
}

