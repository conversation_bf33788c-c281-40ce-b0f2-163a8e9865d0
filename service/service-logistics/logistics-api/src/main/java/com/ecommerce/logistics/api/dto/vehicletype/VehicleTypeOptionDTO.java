
package com.ecommerce.logistics.api.dto.vehicletype;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(name="车型下拉列表实体")
public class VehicleTypeOptionDTO {
    @Schema(description="车型ID")
    private String vehicleTypeId;
    @Schema(description="车型名")
    private String typeName;
    @Schema(description="车轴数枚举code")
    private String axles;
    @Schema(description="运输品类ID")
    private String transportCategoryId;
    @Schema(description="最大载重")
    private BigDecimal maxLoadCapacity;
    @Schema(description="最大自重")
    private BigDecimal maxSelfCapacity;
    @Schema(description="最大长度")
    private BigDecimal maxLength;
    @Schema(description="最大宽度")
    private BigDecimal maxWidth;
    @Schema(description="最大高度")
    private BigDecimal maxHeight;
    @Schema(description="保证金")
    private BigDecimal depositAmount;
    @Schema(description="是否不限车型，0-不是，1-不限车型")
    private Integer unlimiteType;
    @Schema(description="是否需要校验车牌0-不校验，1-校验")
    private Integer verificationNumber;
}

