
package com.ecommerce.logistics.api.dto.warehouse;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="地址列表实体")
public class AddressOptionsDTO {
    @Schema(description="标签")
    private String label;
    @Schema(description="值")
    private String value;
    @Schema(description="键")
    private Integer key;
    @Schema(description="中心")
    private String center;
}

