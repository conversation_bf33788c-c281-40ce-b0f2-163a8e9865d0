
package com.ecommerce.logistics.api.dto.warehouse;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="区域中心仓查询对象")
public class CentralWarehouseQueryDTO {
    @Schema(description="省code")
    private String provinceCode;
    @Schema(description="城市Code")
    private String cityCode;
    @Schema(description="地区Code")
    private String districtCode;
    @Schema(description="卖家ID")
    private String buyerId;
}

