
package com.ecommerce.logistics.api.dto.warehouse;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="门店查询对象")
public class StoreQueryDTO {
    @Schema(description="省code")
    private String provinceCode;
    @Schema(description="城市Code")
    private String cityCode;
    @Schema(description="地区Code")
    private String districtCode;
    @Schema(description="门店编码")
    private String storeCode;
}

