
package com.ecommerce.logistics.api.dto.warehouse;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@Schema(name="交易仓库对象")
public class TradeWarehouseDTO
implements Serializable {
    @NotBlank(message="仓库名字不能为空")
    @Schema(description="仓库名字", required=true)
    private String name;
    @NotNull(message="仓库类型不能为空")
    @Schema(description="仓库类型", required=true)
    private @NotNull(message="仓库类型不能为空") String type;
    @NotBlank(message="省不能为空")
    @Schema(description="省", required=true)
    private String province;
    @NotBlank(message="省份代码不能为空")
    @Schema(description="省份代码", required=true)
    private String provinceCode;
    @NotBlank(message="市不能为空")
    @Schema(description="市", required=true)
    private String city;
    @NotBlank(message="城市代码不能为空")
    @Schema(description="城市代码", required=true)
    private String cityCode;
    @NotBlank(message="区不能为空")
    @Schema(description="区", required=true)
    private String district;
    @NotBlank(message="地区代码不能为空")
    @Schema(description="地区代码", required=true)
    private String districtCode;
    @NotBlank(message="街道地址不能为空")
    @Schema(description="街道地址", required=true)
    private String street;
    @NotBlank(message="街道Code不能为空")
    @Schema(description="街道Code", required=true)
    private String streetCode;
    @NotBlank(message="定位地址不能为空")
    @Schema(description="定位地址")
    private String locationAddress;
    @Schema(description="详细地址", required=true)
    private String address;
    @Schema(description="地址经纬度", required=true)
    private String location;
}

