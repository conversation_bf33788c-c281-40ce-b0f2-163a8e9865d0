
package com.ecommerce.logistics.api.dto.warehouse;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="仓库基础实体")
public class WarehouseBaseDataDTO {
    @Schema(description="仓库ID")
    private String warehouseId;
    @Schema(description="仓库编码")
    private String number;
    @Schema(description="仓库名称")
    private String name;
    @Schema(description="省")
    private String province;
    @Schema(description="市")
    private String city;
    @Schema(description="区")
    private String district;
    @Schema(description="地址")
    private String address;
    @Schema(description="管理员名")
    private String administrator;
    @Schema(description="管理员电话")
    private String administratorPhone;
    @Schema(description="管理员ID")
    private String administratorId;
}

