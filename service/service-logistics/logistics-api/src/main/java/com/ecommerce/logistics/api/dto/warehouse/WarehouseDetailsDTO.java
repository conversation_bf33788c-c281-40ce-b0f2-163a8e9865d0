
package com.ecommerce.logistics.api.dto.warehouse;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;
import lombok.Data;

@Data
public class WarehouseDetailsDTO {
    @Schema(description="仓库Id", required=true)
    private String warehouseId;
    @Schema(description="仓库名字", required=true)
    private String name;
    @Schema(description="仓库类型", required=true)
    private String type;
    @Schema(description="省", required=true)
    private String province;
    @Schema(description="省份代码", required=true)
    private String provinceCode;
    @Schema(description="市", required=true)
    private String city;
    @Schema(description="城市代码", required=true)
    private String cityCode;
    @Schema(description="区", required=true)
    private String district;
    @Schema(description="地区代码", required=true)
    private String districtCode;
    @Schema(description="详细地址", required=true)
    private String address;
    @Schema(description="地址经纬度", required=true)
    private String location;
    @Schema(description="管理员", required=true)
    private String administrator;
    @Schema(description="管理员电话", required=true)
    private String administratorPhone;
    @Schema(description="仓库面积", required=true)
    private Integer area;
    @Schema(description="归属用户Id", required=true)
    private String userId;
    @Schema(description="归属用户类型", required=true)
    private Integer userType;
    @Schema(description="归属用户名称", required=true)
    private String userName;
    @Schema(description="描述", required=true)
    private String note;
    @Schema(description="仓库编号")
    private String number;
    @Schema(description="配送区域")
    private List<WarehouseZoneMapDTO> deliveryAreas;
    @Schema(description="省列表")
    private List<AddressOptionsDTO> provinceOptions;
    @Schema(description="市列表")
    private List<AddressOptionsDTO> cityOptions;
    @Schema(description="区列表")
    private List<AddressOptionsDTO> districtOptions;
}

