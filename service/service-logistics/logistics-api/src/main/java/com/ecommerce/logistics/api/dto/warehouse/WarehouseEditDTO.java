
package com.ecommerce.logistics.api.dto.warehouse;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@Schema(name="仓库编辑DTO对象")
public class WarehouseEditDTO {
    @NotBlank(message="仓库ID不能为空")
    @Schema(description="仓库ID", required=true)
    private String warehouseId;
    @NotBlank(message="仓库名字不能为空")
    @Schema(description="仓库名字", required=true)
    private String name;
    @NotNull(message="仓库类型不能为空")
    @Schema(description="仓库类型", required=true)
    private @NotNull(message="仓库类型不能为空") String type;
    @NotBlank(message="省不能为空")
    @Schema(description="省", required=true)
    private String province;
    @NotBlank(message="省份代码不能为空")
    @Schema(description="省份代码", required=true)
    private String provinceCode;
    @NotBlank(message="市不能为空")
    @Schema(description="市", required=true)
    private String city;
    @NotBlank(message="城市代码不能为空")
    @Schema(description="城市代码", required=true)
    private String cityCode;
    @NotBlank(message="区不能为空")
    @Schema(description="区", required=true)
    private String district;
    @NotBlank(message="地区代码不能为空")
    @Schema(description="地区代码", required=true)
    private String districtCode;
    @NotBlank(message="详细地址不能为空")
    @Schema(description="详细地址", required=true)
    private String address;
    @Schema(description="备注", required=true)
    private String note;
    @NotBlank(message="地址经纬度不能为空")
    @Schema(description="地址经纬度", required=true)
    private String location;
    @NotBlank(message="管理员不能为空")
    @Schema(description="管理员", required=true)
    private String administrator;
    @NotBlank(message="管理员电话不能为空")
    @Schema(description="管理员电话", required=true)
    private String administratorPhone;
    @NotNull(message="仓库面积不能为空")
    @Schema(description="仓库面积", required=true)
    private @NotNull(message="仓库面积不能为空") Integer area;
    @NotBlank(message="更新用户不能为空")
    @Schema(description="更新用户", required=true)
    private String updateUser;
    @Schema(description="仓库编号")
    private String number;
    @NotNull(message="配送区域不能为空")
    private @NotNull(message="配送区域不能为空") List<WarehouseZoneMapDTO> deliveryAreas;
}

