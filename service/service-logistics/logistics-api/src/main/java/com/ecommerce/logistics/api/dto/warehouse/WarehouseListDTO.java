
package com.ecommerce.logistics.api.dto.warehouse;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="仓库列表DTO对象")
public class WarehouseListDTO {
    @Schema(description="仓库ID", required=true)
    private String warehouseId;
    @Schema(description="仓库编号", required=true)
    private String number;
    @Schema(description="仓库名字", required=true)
    private String name;
    @Schema(description="仓库类型", required=true)
    private String type;
    @Schema(description="省", required=true)
    private String province;
    @Schema(description="省份代码", required=true)
    private String provinceCode;
    @Schema(description="市", required=true)
    private String city;
    @Schema(description="城市代码", required=true)
    private String cityCode;
    @Schema(description="区", required=true)
    private String district;
    @Schema(description="地区代码", required=true)
    private String districtCode;
    @Schema(description="详细地址", required=true)
    private String address;
    @Schema(description="地址经纬度", required=true)
    private String location;
    @Schema(description="管理员", required=true)
    private String administrator;
    @Schema(description="管理员电话", required=true)
    private String administratorPhone;
    @Schema(description="仓库面积", required=true)
    private Integer area;
    @Schema(description="归属用户名称", required=true)
    private String userName;
    @Schema(description="备注", required=true)
    private String note;
    @Schema(description="归属用户Id")
    private String userId;
}

