
package com.ecommerce.logistics.api.dto.warehouse;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="仓库列表查询对象")
public class WarehouseListQueryDTO {
    @Schema(description="仓库类型")
    private Integer type;
    @Schema(description="仓库名字")
    private String name;
    @Schema(description="归属用户Id")
    private String userId;
    @Schema(description="归属用户类型")
    private Integer userType;
    @Schema(description="省code")
    private String provinceCode;
    @Schema(description="城市Code")
    private String cityCode;
    @Schema(description="地区Code")
    private String districtCode;
}

