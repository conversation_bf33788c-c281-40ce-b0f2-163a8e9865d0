
package com.ecommerce.logistics.api.dto.warehouse;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="仓库下拉列表查询对象")
public class WarehouseOptionQueryDTO {
    @Schema(description="仓库名称[模糊查询]", required=true)
    private String name;
    @Schema(description="归属用户Id", required=true)
    private String userId;
    @Schema(description="仓库类型", required=true)
    private String type;
}

