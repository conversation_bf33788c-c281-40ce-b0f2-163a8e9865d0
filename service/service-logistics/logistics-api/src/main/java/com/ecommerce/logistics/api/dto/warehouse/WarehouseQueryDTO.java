
package com.ecommerce.logistics.api.dto.warehouse;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serializable;

@Data
@Schema(name="仓库查询对象")
public class WarehouseQueryDTO
implements Serializable {
    @Schema(description="省份代码", required=true)
    private String provinceCode;
    @NotBlank(message="城市代码不能为空")
    @Schema(description="城市代码", required=true)
    private String cityCode;
    @NotBlank(message="地区代码不能为空")
    @Schema(description="地区代码", required=true)
    private String districtCode;
    @NotBlank(message="推荐人ID不能为空")
    @Schema(description="推荐人ID", required=true)
    private String recommendUserId;
}

