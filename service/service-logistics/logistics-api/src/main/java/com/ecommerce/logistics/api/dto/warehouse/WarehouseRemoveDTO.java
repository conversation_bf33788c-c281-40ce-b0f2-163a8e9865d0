
package com.ecommerce.logistics.api.dto.warehouse;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

@Data
@Schema(name="仓库删除DTO对象")
public class WarehouseRemoveDTO {
    @NotBlank(message="仓库ID不能为空")
    @Schema(description="仓库ID")
    private String WarehouseId;
    @NotBlank(message="更新用户不能为空")
    @Schema(description="更新用户")
    private String updateUser;
}

