
package com.ecommerce.logistics.api.dto.warehouse;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.List;

@Data
@Schema(name="新增仓库配送区域DTO")
public class WarehouseZoneMapDTO {
    @Schema(description="省", required=true)
    @NotBlank(message="省不能为空")
    private String province;
    @Schema(description="省Code", required=true)
    @NotBlank(message="省Code不能为空")
    private String provinceCode;
    @Schema(description="市", required=false)
    @NotBlank(message="市不能为空")
    private String city;
    @Schema(description="市Code", required=false)
    @NotBlank(message="市Code不能为空")
    private String cityCode;
    @Schema(description="地区", required=false)
    @NotBlank(message="地区C不能为空")
    private String district;
    @Schema(description="地区Code", required=false)
    @NotBlank(message="地区Code不能为空")
    private String districtCode;
    @Schema(description="街道", required=false)
    private String street;
    @Schema(description="街道Code", required=false)
    private String streetCode;
    @Schema(description="省列表")
    private List<AddressOptionsDTO> provinceOptions;
    @Schema(description="市列表")
    private List<AddressOptionsDTO> cityOptions;
    @Schema(description="区列表")
    private List<AddressOptionsDTO> districtOptions;
    @Schema(description="街道列表")
    private List<AddressOptionsDTO> streetOptions;
}

