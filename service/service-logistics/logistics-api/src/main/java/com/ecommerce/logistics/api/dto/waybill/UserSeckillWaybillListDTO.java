
package com.ecommerce.logistics.api.dto.waybill;

import com.ecommerce.logistics.api.dto.productinfo.ProductInfoDTO;
import com.ecommerce.logistics.api.dto.transportcategory.CategoryNameDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
@Schema(name="用户抢单列表对象")
public class UserSeckillWaybillListDTO {
    @Schema(description="运单ID", required=false)
    private String waybillId;
    @Schema(description="运单子项ID", required=false)
    private String waybillItemId;
    @Schema(description="运单号", required=false)
    private String waybillNum;
    @Schema(description="运单状态", required=false)
    private String status;
    @Schema(description="已发布运费", required=false)
    private BigDecimal publishedCarriage;
    @Schema(description="总重量", required=false)
    private BigDecimal totalQuantity;
    @Schema(description="配送距离", required=false)
    private BigDecimal estimateKm;
    @Schema(description="仓库ID", required=false)
    private String warehouseId;
    @Schema(description="仓库信息", required=false)
    private WarehouseAddressDTO warehouse;
    @Schema(description="单位", required=false)
    private String productUnit;
    @Schema(description="司机姓名", required=false)
    private String driverName;
    @Schema(description="司机电话", required=false)
    private String driverPhone;
    @Schema(description="车牌号", required=false)
    private String vehicleNum;
    @Schema(description="子运单信息", required=false)
    private List<SeckillAddressDTO> waybills;
    @Schema(description="运输品类列表", required=false)
    private List<CategoryNameDTO> transportCategoryList;
    @Schema(description="商品列表", required=false)
    private List<ProductInfoDTO> productList;
    @Schema(description="货物是否已准备")
    private Byte readyFlag = 1;
    @Schema(description="运输工具类型")
    private String transportToolType;
}

