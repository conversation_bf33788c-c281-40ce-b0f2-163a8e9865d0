
package com.ecommerce.logistics.api.dto.waybill;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;
import lombok.Data;

@Data
@Schema(name="管理员待处理运单查询对象")
public class WarehouseAdminWaitDeliveryQueryDTO {
    @Schema(description="仓库ID列表", required=false)
    private List<String> warehouseIdList;
    @Schema(description="运单号", required=false)
    private String waybillNum;
    @Schema(description="车牌号", required=false)
    private String vehicleNum;
    @Schema(description="状态,默认待处理")
    private String status;
    @Schema(description="管理员用户ID")
    private String userId;
    @Schema(description="买家Id")
    private String buyerId;
    @Schema(description="是否进厂")
    private Boolean isEntered;
    @Schema(description="区域权限列表")
    private List<String> regionCodeList;
}

