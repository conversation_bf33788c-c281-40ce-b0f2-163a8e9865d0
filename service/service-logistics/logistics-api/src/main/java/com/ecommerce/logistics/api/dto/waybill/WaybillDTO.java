
package com.ecommerce.logistics.api.dto.waybill;

import com.ecommerce.logistics.api.dto.pickingbill.PickingBillDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

@Data
@Schema(name="运单对象")
public class WaybillDTO
implements Serializable,
Comparable<WaybillDTO> {
    @Schema(description="运单ID", required=false)
    private String waybillId;
    @Schema(description="运单号", required=false)
    private String waybillNum;
    @Schema(description="父运单号", required=false)
    private String parentWaybillNum;
    @Schema(description="提货单ID", required=true)
    private String pickingBillId;
    @Schema(description="调度单ID", required=false)
    private String dispatchBillId;
    @Schema(description="调度单号", required=false)
    private String dispatchBillNum;
    @Schema(description="承运商ID", required=true)
    private String carrierId;
    @Schema(description="承运商名称", required=true)
    private String carrierName;
    @Schema(description="配送时间", required=true)
    private Date deliveryTime;
    @Schema(description="配送时间范围", required=true)
    private String deliveryTimeRange;
    @Schema(description="配送司机ID", required=false)
    private String driverId;
    @Schema(description="配送司机名称", required=false)
    private String driverName;
    @Schema(description="司机电话", required=false)
    private String driverPhone;
    @Schema(description="配送车辆", required=false)
    private String vehicleNum;
    @Schema(description="商品数量", required=false)
    private BigDecimal quantity;
    @Schema(description="商品图片", required=false)
    private String productImg;
    @Schema(description="数量单位", required=false)
    private String unit;
    @Schema(description="运单状态", required=false)
    private String status;
    @Schema(description="运单类型", required=false)
    private String type;
    @Schema(description="仓库Id", required=false)
    private String warehouseId;
    @Schema(description="预估配送里程", required=false)
    private BigDecimal estimateKm;
    @Schema(description="预估配送时长", required=false)
    private BigDecimal estimateDuration;
    @Schema(description="发布运费", required=false)
    private BigDecimal publishedCarriage;
    @Schema(description="出站时间", required=false)
    private Date leaveWarehouseTime;
    @Schema(description="完成时间", required=false)
    private Date completeTime;
    @Schema(description="运单创建时间", required=false)
    private Date createTime;
    @Schema(description="商品数量", required=false)
    private Integer pruductCount;
    @Schema(description="提货点", required=false)
    private String warehouseAddress;
    @Schema(description="发货单列表", required=false)
    private List<PickingBillDTO> pickingBills;
    @Schema(description="父运单ID", required=false)
    private String parentId;
    @Schema(description="是否主运单", required=false)
    private Integer isMainWaybill;
    @Schema(description="出厂量", required=false)
    private BigDecimal actualQuantity;
    @Schema(description="签收数量", required=false)
    private BigDecimal signQuantity;
    @Schema(description="外部运单状态", required=false)
    private String externalWaybillStatus;

    @Override
    public int compareTo(WaybillDTO waybillDTO) {
        return waybillDTO.getWaybillNum().compareTo(this.getWaybillNum());
    }
}

