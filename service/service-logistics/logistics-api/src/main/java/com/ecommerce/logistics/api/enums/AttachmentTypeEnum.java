
package com.ecommerce.logistics.api.enums;

public enum AttachmentTypeEnum {
    IMG("030160100", "图片"),
    VIDEO("030160200", "视频"),
    OTHER_TYPES("030160300", "其它类型"),
    DELIVERY_CERTIFICATE("030160400", "支付凭证");

    private final String code;
    private final String desc;

    private AttachmentTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static AttachmentTypeEnum valueOfCode(String code) {
        for (AttachmentTypeEnum item : AttachmentTypeEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

