
package com.ecommerce.logistics.api.enums;

import com.ecommerce.common.utils.CsStringUtils;

public enum BillProxyTypeEnum {
    NORMAL("030400100", "常规单据"),
    PRIMARY("030400200", "一级"),
    SECONDARY("030400300", "二级");

    private final String code;
    private final String desc;

    private BillProxyTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static BillProxyTypeEnum valueOfCode(String code) {
        for (BillProxyTypeEnum item : BillProxyTypeEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public static String parseToName(String code) {
        BillProxyTypeEnum billProxyTypeEnum = BillProxyTypeEnum.valueOfCode(code);
        if (billProxyTypeEnum == null) {
            return null;
        }
        return billProxyTypeEnum.desc;
    }

    public static boolean isFirstLevel(String billProxyType) {
        return CsStringUtils.equals((String) billProxyType, (String) NORMAL.getCode()) || CsStringUtils.equals((String) billProxyType, (String) PRIMARY.getCode());
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

