
package com.ecommerce.logistics.api.enums;

public enum BizUidTypeEnum {
    PICKING_BILL_NUM("20", "提货单号"),
    DISPATCH_BILL_NUM("21", "调度单号"),
    WAYBILL_NUM("22", "运单号");

    private final String code;
    private final String desc;

    private BizUidTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static BizUidTypeEnum valueOfCode(String code) {
        for (BizUidTypeEnum item : BizUidTypeEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

