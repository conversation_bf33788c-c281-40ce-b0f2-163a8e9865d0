
package com.ecommerce.logistics.api.enums;

public enum BreakDealStatusEnum {
    APPLYING("030530100", "申请中"),
    APPROVED("030530200", "已同意"),
    REJECTED("030530300", "已拒绝"),
    CANCELLED("030530400", "已取消");

    private final String code;
    private final String desc;

    private BreakDealStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static BreakDealStatusEnum valueOfCode(String code) {
        for (BreakDealStatusEnum item : BreakDealStatusEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

