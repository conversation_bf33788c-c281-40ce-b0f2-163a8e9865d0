
package com.ecommerce.logistics.api.enums;

public enum CarriageLogCategoryTypeEnum {
    ADD("030390100", "添加"),
    UPDATE("030390200", "修改"),
    DELETE("030390300", "删除");

    private final String code;
    private final String desc;

    private CarriageLogCategoryTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static CarriageLogCategoryTypeEnum valueOfCode(String code) {
        for (CarriageLogCategoryTypeEnum item : CarriageLogCategoryTypeEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

