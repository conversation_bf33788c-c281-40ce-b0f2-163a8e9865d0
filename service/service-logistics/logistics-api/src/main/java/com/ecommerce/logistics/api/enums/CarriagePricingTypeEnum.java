
package com.ecommerce.logistics.api.enums;

public enum CarriagePricingTypeEnum {
    POINT_TO_POINT("030320100", "点对点定价"),
    ZONE_INTEGRATE("030320200", "区域统一定价"),
    ZONE_EXPRESSION("030320300", "区域公式定价");

    private final String code;
    private final String desc;

    private CarriagePricingTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static CarriagePricingTypeEnum valueOfCode(String code) {
        for (CarriagePricingTypeEnum item : CarriagePricingTypeEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

