
package com.ecommerce.logistics.api.enums;

public enum CarriageSectionTypeEnum {
    KILOMETRE("030270100", "公里");

    private final String code;
    private final String desc;

    private CarriageSectionTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static CarriageSectionTypeEnum valueOfCode(String code) {
        for (CarriageSectionTypeEnum item : CarriageSectionTypeEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

