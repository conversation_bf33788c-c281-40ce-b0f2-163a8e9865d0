
package com.ecommerce.logistics.api.enums;

public enum CarriageSettlementTypeEnum {
    COLLECT("030330100", "收费"),
    PAY("030330200", "付费"),
    ENTRUST("030330300", "委托");

    private final String code;
    private final String desc;

    private CarriageSettlementTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static CarriageSettlementTypeEnum valueOfCode(String code) {
        for (CarriageSettlementTypeEnum item : CarriageSettlementTypeEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

