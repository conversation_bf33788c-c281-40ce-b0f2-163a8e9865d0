
package com.ecommerce.logistics.api.enums;

public enum CarrierTypeEnum {
    SOCIETY_TRANSPORT("030010100", "社会运力"),
    PLATFORM_SIGN("030010200", "平台签约"),
    PLATFORM_SELF("030010300", "平台自有"),
    PLATFORM_STORE("030010400", "平台门店"),
    PERSONAL_SHIPOWNER("030010500", "个体船东");

    private final String code;
    private final String desc;

    private CarrierTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static CarrierTypeEnum valueOfCode(String code) {
        for (CarrierTypeEnum item :  CarrierTypeEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

