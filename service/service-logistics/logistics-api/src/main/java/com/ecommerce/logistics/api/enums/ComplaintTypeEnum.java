
package com.ecommerce.logistics.api.enums;

public enum ComplaintTypeEnum {
    TRANSPORTATION_TIME_LIMIT("045510100", "运输时效"),
    TRANSPORT_QUALITY("045510200", "运输质量"),
    SERVICE_ATTITUDE("045510300", "服务态度"),
    OTHER("045510400", "其它");

    private final String code;
    private final String desc;

    private ComplaintTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ComplaintTypeEnum valueOfCode(String code) {
        for (ComplaintTypeEnum item : ComplaintTypeEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

