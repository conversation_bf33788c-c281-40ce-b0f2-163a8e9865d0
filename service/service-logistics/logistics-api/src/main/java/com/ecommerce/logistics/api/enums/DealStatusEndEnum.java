
package com.ecommerce.logistics.api.enums;

public enum DealStatusEndEnum {
    NOT_HANDLE("043510100", "待处理"),
    ACCEPTING("043510200", "已受理"),
    REJECTED("043510300", "驳回"),
    FINISHED("043510400", "处理完成");

    private final String code;
    private final String desc;

    private DealStatusEndEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static DealStatusEndEnum valueOfCode(String code) {

        for (DealStatusEndEnum item :  DealStatusEndEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

