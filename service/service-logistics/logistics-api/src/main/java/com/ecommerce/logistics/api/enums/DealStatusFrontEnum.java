
package com.ecommerce.logistics.api.enums;

public enum DealStatusFrontEnum {
    NOT_HANDLE("042510100", "待处理"),
    HANDLING("042510200", "处理中"),
    HANDLED("042510300", "已处理"),
    CANCEL("042510400", "已取消");

    private final String code;
    private final String desc;

    private DealStatusFrontEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static DealStatusFrontEnum valueOfCode(String code) {
        for (DealStatusFrontEnum item : DealStatusFrontEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

