
package com.ecommerce.logistics.api.enums;

import com.google.common.collect.Lists;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public enum DeliveryBillStatusEnum {
    DRAFT("030590100", "草稿"),
    CREATED("030590200", "新建"),
    UNCONFIRMED("030590300", "待确认"),
    CONFIRMED("030590400", "待安排"),
    CANCELED("030590500", "已取消"),
    REJECT("030590600", "已拒绝"),
    CLOSED("030590700", " 已中止"),
    COMPLETE("030590800", "已完成");

    private final String code;
    private final String desc;

    private DeliveryBillStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static DeliveryBillStatusEnum valueOfCode(String code) {
        for (DeliveryBillStatusEnum item :  DeliveryBillStatusEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public static List<String> getAllCodes() {
        return Stream.of(DeliveryBillStatusEnum.values()).map(DeliveryBillStatusEnum::getCode).toList();
    }

    public static List<String> getFinalCodesList() {
        return Lists.newArrayList(new String[]{DeliveryBillStatusEnum.CANCELED.code, DeliveryBillStatusEnum.REJECT.code, DeliveryBillStatusEnum.CLOSED.code, DeliveryBillStatusEnum.COMPLETE.code});
    }

    public static boolean isFinalStatus(String code) {
        return DeliveryBillStatusEnum.getFinalCodesList().contains(code);
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

