
package com.ecommerce.logistics.api.enums;

public enum DeliveryNoteStatusEnum {
    PLANING("030340100", "计划"),
    LEAVED("030340200", "已出厂"),
    SIGNED("030340300", "已签收"),
    UNLOAD("030340400", "已入库"),
    CANCELED("030340500", "已取消");

    private final String code;
    private final String desc;

    private DeliveryNoteStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static DeliveryNoteStatusEnum valueOfCode(String code) {
        for (DeliveryNoteStatusEnum item : DeliveryNoteStatusEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

