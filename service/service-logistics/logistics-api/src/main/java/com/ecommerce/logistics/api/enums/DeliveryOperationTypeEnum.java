
package com.ecommerce.logistics.api.enums;

public enum DeliveryOperationTypeEnum {
    DELIVERY_NEW(1, "创建委托单"),
    DELIVERY_CONSIGN(2, "委托"),
    DELIVERY_ASSIGN(3, "指派运力"),
    DELIVERY_ACCEPT(4, "接受委托"),
    DELIVERY_REJECT(5, "拒绝委托"),
    DELIVERY_CANCEL(6, "取消委托"),
    DELIVERY_STOP(7, "中止委托"),
    DELIVERY_COMPLETE(8, "完成委托"),
    DELIVERY_CHANGE_WAREHOUSE(9, "修改出货点");

    private final Integer code;
    private final String desc;

    private DeliveryOperationTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static DeliveryOperationTypeEnum valueOfCode(Integer code) {
        for (DeliveryOperationTypeEnum item : DeliveryOperationTypeEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

