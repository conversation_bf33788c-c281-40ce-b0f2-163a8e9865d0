
package com.ecommerce.logistics.api.enums;

import com.ecommerce.logistics.api.constant.StringConstant;

import java.util.HashMap;
import java.util.Map;

public enum DeliveryTimeRangeEnum {
    WEE_HOURS("030020100", "凌晨(00:00-06:00)", StringConstant.START_OF_DAY_TIME),
    FORENOON("030020200", "上午(06:00-12:00)", "06:00:00"),
    AFTERNOON("030020300", "下午(12:00-18:00)", "12:00:00"),
    EVENING("030020400", "晚上(18:00-24:00)", "18:00:00"),
    WHOLE_DAY("030020500", "全天(00:00-24:00)", StringConstant.START_OF_DAY_TIME),
    TTQ030020101("030020101", "凌晨(00:00-00:30)", StringConstant.START_OF_DAY_TIME),
    TTQ030020102("030020102", "凌晨(00:30-01:00)", "00:30:00"),
    TTQ030020103("030020103", "凌晨(01:00-01:30)", "01:00:00"),
    TTQ030020104("030020104", "凌晨(01:30-02:00)", "01:30:00"),
    TTQ030020105("030020105", "凌晨(02:00-02:30)", "02:00:00"),
    TTQ030020106("030020106", "凌晨(02:30-03:00)", "02:30:00"),
    TTQ030020107("030020107", "凌晨(03:00-03:30)", "03:00:00"),
    TTQ030020108("030020108", "凌晨(03:30-04:00)", "03:30:00"),
    TTQ030020109("030020109", "凌晨(04:00-04:30)", "04:00:00"),
    TTQ030020110("030020110", "凌晨(04:30-05:00)", "04:30:00"),
    TTQ030020111("030020111", "凌晨(05:00-05:30)", "05:00:00"),
    TTQ030020112("030020112", "凌晨(05:30-06:00)", "05:30:00"),
    TTQ030020201("030020201", "上午(06:00-06:30)", "06:00:00"),
    TTQ030020202("030020202", "上午(06:30-07:00)", "06:30:00"),
    TTQ030020203("030020203", "上午(07:00-07:30)", "07:00:00"),
    TTQ030020204("030020204", "上午(07:30-08:00)", "07:30:00"),
    TTQ030020205("030020205", "上午(08:00-08:30)", "08:00:00"),
    TTQ030020206("030020206", "上午(08:30-09:00)", "08:30:00"),
    TTQ030020207("030020207", "上午(09:00-09:30)", "09:00:00"),
    TTQ030020208("030020208", "上午(09:30-10:00)", "09:30:00"),
    TTQ030020209("030020209", "上午(10:00-10:30)", "10:00:00"),
    TTQ030020210("030020210", "上午(10:30-11:00)", "10:30:00"),
    TTQ030020211("030020211", "上午(11:00-11:30)", "11:00:00"),
    TTQ030020212("030020212", "上午(11:30-12:00)", "11:30:00"),
    TTQ030020301("030020301", "下午(12:00-12:30)", "12:00:00"),
    TTQ030020302("030020302", "下午(12:30-13:00)", "12:30:00"),
    TTQ030020303("030020303", "下午(13:00-13:30)", "13:00:00"),
    TTQ030020304("030020304", "下午(13:30-14:00)", "13:30:00"),
    TTQ030020305("030020305", "下午(14:00-14:30)", "14:00:00"),
    TTQ030020306("030020306", "下午(14:30-15:00)", "14:30:00"),
    TTQ030020307("030020307", "下午(15:00-15:30)", "15:00:00"),
    TTQ030020308("030020308", "下午(15:30-16:00)", "15:30:00"),
    TTQ030020309("030020309", "下午(16:00-16:30)", "16:00:00"),
    TTQ030020310("030020310", "下午(16:30-17:00)", "16:30:00"),
    TTQ030020311("030020311", "下午(17:00-17:30)", "17:00:00"),
    TTQ030020312("030020312", "下午(17:30-18:00)", "17:30:00"),
    TTQ030020401("030020401", "晚上(18:00-18:30)", "18:00:00"),
    TTQ030020402("030020402", "晚上(18:30-19:00)", "18:30:00"),
    TTQ030020403("030020403", "晚上(19:00-19:30)", "19:00:00"),
    TTQ030020404("030020404", "晚上(19:30-20:00)", "19:30:00"),
    TTQ030020405("030020405", "晚上(20:00-20:30)", "20:00:00"),
    TTQ030020406("030020406", "晚上(20:30-21:00)", "20:30:00"),
    TTQ030020407("030020407", "晚上(21:00-21:30)", "21:00:00"),
    TTQ030020408("030020408", "晚上(21:30-22:00)", "21:30:00"),
    TTQ030020409("030020409", "晚上(22:00-22:30)", "22:00:00"),
    TTQ030020410("030020410", "晚上(22:30-23:00)", "22:30:00"),
    TTQ030020411("030020411", "晚上(23:00-23:30)", "23:00:00"),
    TTQ030020412("030020412", "晚上(23:30-24:00)", "23:30:00");

    private final String code;
    private final String desc;
    private final String defaultTime;

    private DeliveryTimeRangeEnum(String code, String desc, String defaultTime) {
        this.code = code;
        this.desc = desc;
        this.defaultTime = defaultTime;
    }

    public static DeliveryTimeRangeEnum valueOfCode(String code) {
        for (DeliveryTimeRangeEnum item : DeliveryTimeRangeEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }

    public String getDefaultTime() {
        return this.defaultTime;
    }

    public static Map<String, String> getCode(String time) {
        HashMap<String, String> map = new HashMap<String, String>();
        for (DeliveryTimeRangeEnum item :  DeliveryTimeRangeEnum.values()) {
            if (time.compareTo(DeliveryTimeRangeEnum.EVENING.defaultTime) >= 0) {
                map.put("code", DeliveryTimeRangeEnum.WHOLE_DAY.code);
                map.put(StringConstant.EQUAL_FLAG, "1");
                return map;
            }
            if (item.defaultTime.compareTo(time) == 0) {
                map.put("code", item.code);
                map.put(StringConstant.EQUAL_FLAG, "1");
                return map;
            }
            if (item.defaultTime.compareTo(time) <= 0) continue;
            map.put("code", item.code);
            map.put(StringConstant.EQUAL_FLAG, "0");
            return map;
        }
        return map;
    }

    public static String getDateCode(String time) {
        if (time.compareTo(DeliveryTimeRangeEnum.EVENING.defaultTime) >= 0) {
            return DeliveryTimeRangeEnum.EVENING.code;
        }
        if (time.compareTo(DeliveryTimeRangeEnum.AFTERNOON.defaultTime) >= 0) {
            return DeliveryTimeRangeEnum.AFTERNOON.code;
        }
        if (time.compareTo(DeliveryTimeRangeEnum.FORENOON.defaultTime) >= 0) {
            return DeliveryTimeRangeEnum.FORENOON.code;
        }
        if (time.compareTo(DeliveryTimeRangeEnum.WEE_HOURS.defaultTime) >= 0) {
            return DeliveryTimeRangeEnum.WEE_HOURS.code;
        }
        return null;
    }
}

