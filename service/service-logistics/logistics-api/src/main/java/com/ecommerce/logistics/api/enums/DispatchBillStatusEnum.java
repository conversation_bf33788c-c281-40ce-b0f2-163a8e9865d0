
package com.ecommerce.logistics.api.enums;

public enum DispatchBillStatusEnum {
    UNCONFIRMED("030030100", "待确认"),
    ASSIGNING("030030200", "指派中"),
    ASSIGNED("030030300", "已指派"),
    COMPLETED("030030400", "已完成"),
    CANCELED("030030500", "已取消"),
    CLOSED("030030600", "已关闭");

    private final String code;
    private final String desc;

    private DispatchBillStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static DispatchBillStatusEnum valueOfCode(String code) {
        for (DispatchBillStatusEnum item :  DispatchBillStatusEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

