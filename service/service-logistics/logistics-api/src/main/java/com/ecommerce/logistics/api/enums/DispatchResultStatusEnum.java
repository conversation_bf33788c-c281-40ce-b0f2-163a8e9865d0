
package com.ecommerce.logistics.api.enums;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public enum DispatchResultStatusEnum {
    WAIT_CONFIRM("030640100", "待确认"),
    CONFIRMED("030640200", "已确认"),
    REJECTED("030640300", "已拒绝");

    private final String code;
    private final String desc;

    private DispatchResultStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static DispatchResultStatusEnum valueOfCode(String code) {
        for (DispatchResultStatusEnum item : DispatchResultStatusEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public static List<String> getAllCodes() {
        return Stream.of(DispatchResultStatusEnum.values()).map(DispatchResultStatusEnum::getCode).toList();
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

