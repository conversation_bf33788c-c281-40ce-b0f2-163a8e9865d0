
package com.ecommerce.logistics.api.enums;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public enum DispatchRuleEnum {
    TRANSPORT_CAPACITY_LIMIT("A001", "每天指派运力不超过承运商运力", 0, "transportCapacityLimitDRProcessor"),
    CARRIER_SERVICE_AREA_LIMIT("A002", "在承运商的服务范围内", 0, "carrierServiceAreaLimitDRProcessor"),
    CARRIER_FREIGHT_LIMIT("A101", "运费最低", 1, "carrierFreightLimitDRProcess"),
    CARRIER_PARKING_LIMIT("A102", "承运商停车场离发货点最近", 1, "carrierParkingLimitDRProcessor"),
    VEHICLE_STATUS_LIMIT("B001", "车辆处于可用状态", 0, "vehicleStatusLimitDRProcess"),
    VEHICLE_TYPE_LIMIT("B002", "车型满足运输品类要求", 0, "vehicleTypeLimitDRProcess"),
    VEHICLE_HEIGHT_LIMIT("B003", "车辆满足限高需求", 0, "vehicleHeightLimitDRProcess"),
    VEHICLE_LOAD_LIMIT("B004", "车辆满足装卸、搬运要求", 0, "vehicleLoadAndUnloadLimitDRProcess"),
    INSTANT_FULL_COST_LOWEST("B101", "整车即时成本最低,最大车型距离最近", 1, "instantCostLowestProcess"),
    PLAN_COST_LOWEST("B102", "计划成本最低,最大车型距离最近", 1, "planCostLowestProcess"),
    INSTANT_ZERO_COST_LOWEST("B103", "零单即时成本最低,最大车型距离最近", 1, "instantCostLowestProcess");

    private String code;
    private String desc;
    private Integer ruleType;
    private String processor;

    private DispatchRuleEnum(String code, String desc, Integer ruleType, String processor) {
        this.code = code;
        this.desc = desc;
        this.ruleType = ruleType;
        this.processor = processor;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }

    public Integer getRuleType() {
        return this.ruleType;
    }

    public String getProcessor() {
        return this.processor;
    }

    public static boolean containForceRule(List<String> ruleCodeList) {
        if (ruleCodeList == null || ruleCodeList.isEmpty()) {
            return false;
        }
        return ruleCodeList.stream().map(DispatchRuleEnum::valueOfCode).filter(Objects::nonNull).anyMatch(item -> item.ruleType != null && item.ruleType == 0);
    }

    public static DispatchRuleEnum valueOfCode(String code) {
        for (DispatchRuleEnum item : DispatchRuleEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public static List<String> getAllCode() {
        return Stream.of(DispatchRuleEnum.values()).map(DispatchRuleEnum::getCode).toList();
    }
}

