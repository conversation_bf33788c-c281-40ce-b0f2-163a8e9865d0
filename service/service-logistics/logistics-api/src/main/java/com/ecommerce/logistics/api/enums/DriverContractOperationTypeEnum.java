
package com.ecommerce.logistics.api.enums;

public enum DriverContractOperationTypeEnum {
    DRIVER_CONTRACT_NEW(1, "添加司机合同"),
    DRIVER_CONTRACT_REPORTE(2, "上报司机合同");

    private final Integer code;
    private final String desc;

    private DriverContractOperationTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static DriverContractOperationTypeEnum valueOfCode(Integer code) {
        for (DriverContractOperationTypeEnum item : DriverContractOperationTypeEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

