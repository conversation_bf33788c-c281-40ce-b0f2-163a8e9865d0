
package com.ecommerce.logistics.api.enums;

public enum EndReasonEnum {
    NORMAL("030360100", "正常流程"),
    EXPIRE("030360200", "延时");

    private final String code;
    private final String desc;

    private EndReasonEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static EndReasonEnum valueOfCode(String code) {
        for (EndReasonEnum item :  EndReasonEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

