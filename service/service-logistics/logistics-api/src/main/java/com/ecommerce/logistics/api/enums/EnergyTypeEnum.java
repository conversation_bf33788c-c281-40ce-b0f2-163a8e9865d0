
package com.ecommerce.logistics.api.enums;

public enum EnergyTypeEnum {
    GASOLINE("030510100", "汽油"),
    DIESEL("030510200", "柴油"),
    ELECTRICITY("030510300", "电"),
    MIXED_OIL("030510400", "混合油"),
    NATURAL_GAS("030510500", "天然气"),
    LIQUEFIED_PETROLEUM_GAS("030510600", "液化石油气"),
    METHANOL("030510700", "甲醇"),
    ETHANOL("030510800", "乙醇"),
    SOLAR_ENERGY("030510900", "太阳能"),
    HYBRID("030511000", "混合动力"),
    OTHER("030511010", "其他");

    private final String code;
    private final String desc;

    private EnergyTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static EnergyTypeEnum valueOfCode(String code) {
        for (EnergyTypeEnum item :  EnergyTypeEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

