
package com.ecommerce.logistics.api.enums;

public enum EntrustSourceEnum {
    ORDER_CREATE("030580100", "交易创建"),
    MANUAL_CREATE("030580200", "手动创建"),
    REROUTE_CREATE("030580300", "改航创建"),
    OUTER_ENTRUST("030580400", "他人委托"),
    INNER_ASSIGN("030580500", "内部指派"),
    PROXY_SYNC("030580600", "背靠背同步");

    private final String code;
    private final String desc;

    private EntrustSourceEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static EntrustSourceEnum valueOfCode(String code) {
        for (EntrustSourceEnum item :  EntrustSourceEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

