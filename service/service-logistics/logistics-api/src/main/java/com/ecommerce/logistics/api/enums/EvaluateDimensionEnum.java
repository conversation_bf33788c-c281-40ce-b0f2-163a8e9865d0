
package com.ecommerce.logistics.api.enums;

public enum EvaluateDimensionEnum {
    CARRIERS("030480100", "评价承运方"),
    SHIPPERS("030480200", "评价托运方");

    private final String code;
    private final String desc;

    private EvaluateDimensionEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static EvaluateDimensionEnum valueOfCode(String code) {
        for (EvaluateDimensionEnum item :  EvaluateDimensionEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

