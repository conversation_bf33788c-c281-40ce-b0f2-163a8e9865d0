
package com.ecommerce.logistics.api.enums;

public enum EvaluateTypeEnum {
    CARRIERS_PLATFORMCARRIER("030470100", "评价承运方-平台承运商"),
    CARRIERS_SOCIETY("030470200", "评价承运方-社会承运商"),
    CARRIERS_PERSONAL("030470300", "评价承运方-个体车主"),
    CARRIERS_PLATFORM("030470400", "评价承运方-平台"),
    SHIPPERS_PLATFORM("030470500", "评价托运方-平台");

    private final String code;
    private final String desc;

    private EvaluateTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static EvaluateTypeEnum valueOfCode(String code) {
        EvaluateTypeEnum[] enums;
        for (EvaluateTypeEnum item : enums = EvaluateTypeEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

