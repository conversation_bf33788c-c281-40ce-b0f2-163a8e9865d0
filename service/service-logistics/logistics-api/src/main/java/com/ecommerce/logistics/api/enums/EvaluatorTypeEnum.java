
package com.ecommerce.logistics.api.enums;

public enum EvaluatorTypeEnum {
    CARRIER("030490100", "承运商"),
    PERSONAL_DRIVER("030490200", "个体车主"),
    PLATFORM("030490300", "平台"),
    BUYER("030490400", "买家"),
    SELLER("030490500", "卖家");

    private final String code;
    private final String desc;

    private EvaluatorTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static EvaluatorTypeEnum valueOfCode(String code) {
        for (EvaluatorTypeEnum item : EvaluatorTypeEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

