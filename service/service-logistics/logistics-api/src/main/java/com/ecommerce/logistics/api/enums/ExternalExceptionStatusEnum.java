
package com.ecommerce.logistics.api.enums;

public enum ExternalExceptionStatusEnum {
    WAIT_HANDLE("030300100", "待处理"),
    HANDLING("030300200", "处理中"),
    HAN<PERSON>LE_SUCCESS("030300300", "处理成功"),
    HANDLE_FAIL("030300400", "处理失败");

    private final String code;
    private final String desc;

    private ExternalExceptionStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ExternalExceptionStatusEnum valueOfCode(String code) {
        for (ExternalExceptionStatusEnum item : ExternalExceptionStatusEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

