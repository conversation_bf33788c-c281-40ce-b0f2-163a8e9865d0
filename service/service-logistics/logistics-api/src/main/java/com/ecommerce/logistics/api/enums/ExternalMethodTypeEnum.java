
package com.ecommerce.logistics.api.enums;

public enum ExternalMethodTypeEnum {
    ERP_WAYBILL_CREATE("030310100", "汽运-ERP运单创建"),
    ERP_WAYBILL_CHANGE("030310200", "汽运-ERP运单修改"),
    ERP_WAYBILL_CLOSE("030310300", "汽运-ERP运单作废"),
    ERP_CONCRETE_COMPLETE("030310400", "汽运-ERP混凝土完成"),
    ERP_WAYBILL_OPEN("030310500", "汽运-ERP运单开单"),
    ERP_WAYBILL_SEND("030310600", "汽运-ERP运单发货"),
    ERP_SHIPBILL_SEND("030310700", "船运-计划信息下发"),
    ERP_SHIPBILL_CHANGE("030310800", "船运-计划信息修改"),
    ERP_SHIPBILL_CLOSE("030310900", "船运-计划作废"),
    ERP_SHIPBILL_OPENCABIN("030311000", "船运-通知ERP开仓卸货"),
    ERP_SHIPBILL_REFUND("030312000", "汽运-通知ERP退货"),
    TRACE_SHIPBILL_REFUND("030313000", "汽运-通知交易退货");

    private final String code;
    private final String desc;

    private ExternalMethodTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ExternalMethodTypeEnum valueOfCode(String code) {
        for (ExternalMethodTypeEnum item :  ExternalMethodTypeEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

