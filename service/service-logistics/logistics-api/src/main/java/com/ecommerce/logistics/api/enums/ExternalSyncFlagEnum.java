
package com.ecommerce.logistics.api.enums;

public enum ExternalSyncFlagEnum {
    NO_SYNC("030290100", "未同步"),
    FORWARD_SYNC("030290200", "正向同步"),
    BACKWARD_SYNC("030290300", "反向同步");

    private final String code;
    private final String desc;

    private ExternalSyncFlagEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ExternalSyncFlagEnum valueOfCode(String code) {
        for (ExternalSyncFlagEnum item : ExternalSyncFlagEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

