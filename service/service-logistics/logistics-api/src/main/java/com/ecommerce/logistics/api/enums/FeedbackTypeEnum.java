
package com.ecommerce.logistics.api.enums;

public enum FeedbackTypeEnum {
    SUGGEST("044510100", "建议"),
    COMPLAINT("044510200", "投诉"),
    UNLOAD_TYPE("044510300", "卸货类型");

    private final String code;
    private final String desc;

    private FeedbackTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static FeedbackTypeEnum valueOfCode(String code) {
        for (FeedbackTypeEnum item : FeedbackTypeEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

