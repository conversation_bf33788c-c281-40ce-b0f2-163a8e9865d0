
package com.ecommerce.logistics.api.enums;

public enum GpsProtocolTypeEnum {
    GPS_808("030260100", "808协议"),
    GPS_809("030260200", "809协议");

    private final String code;
    private final String desc;

    private GpsProtocolTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static GpsProtocolTypeEnum valueOfCode(String code) {
        for (GpsProtocolTypeEnum item : GpsProtocolTypeEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

