
package com.ecommerce.logistics.api.enums;

public enum InvoiceTargetTypeEnum {
    NO_INV("030380100", "不开票"),
    SELLER_INV("030380200", "卖家开票"),
    PLATFORM_INV("030380300", "平台开票");

    private String code;
    private String desc;

    private InvoiceTargetTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static InvoiceTargetTypeEnum valueOfCode(String code) {
        for (InvoiceTargetTypeEnum item :  InvoiceTargetTypeEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

