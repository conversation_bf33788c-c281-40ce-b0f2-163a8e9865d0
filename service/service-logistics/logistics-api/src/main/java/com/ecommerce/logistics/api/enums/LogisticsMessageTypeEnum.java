
package com.ecommerce.logistics.api.enums;

public enum LogisticsMessageTypeEnum {
    WAYBILL_DRAFT_CREATE("com.ecommerce.logistics.waybill.draftCreate", "运单草稿创建"),
    WAYBILL_MONITOR_PREPARE("com.ecommerce.logistics.waybill.monitorPrepare", "运单监控准备"),
    WAYBILL_MONITOR_PREPARE_MERGER("com.ecommerce.logistics.waybill.monitorPrepareForMerge", "合并运单监控准备"),
    WAYBILL_MONITOR_PUBLISH("com.ecommerce.logistics.waybill.monitorPublish", "运单监控发布"),
    WAYBILL_STATUS_NOTIFY("com.ecommerce.logistics.waybill.statusNotify", "运单状态通知"),
    WAYBILL_ARRIVE_WAREHOUSE("com.ecommerce.logistics.waybill.arriveWarehouse", "运单到达提货点"),
    WAYBILL_SNATCH_SMS("com.ecommerce.logistics.waybill.snatchSMS", "抢单成功通知司机"),
    WAYBILL_SNATCH_MESSAGE_PUSH("com.ecommerce.logistics.waybill.snatchMessage.push", "运单抢单消息推送"),
    WAYBILL_CONFIRM_MESSAGE_PUSH("com.ecommerce.logistics.waybill.confirmMessage.push", "运单确认消息推送"),
    NOTIFY_ERP_CREATE_WAYBILL("erp.logistics.notifyERPCreateWaybill", "通知ERP创建运单"),
    NOTIFY_ERP_CHANGE_WAYBILL("erp.logistics.notifyERPChangeWaybill", "通知ERP修改运单"),
    NOTIFY_ERP_CLOSE_WAYBILL("erp.logistics.notifyERPCloseWaybill", "通知ERP作废运单"),
    NOTIFY_EC_CREATE_WAYBILL_CALLBACK("erp.logistics.notifyECCreateWaybill.callback", "通知电商创建运单回调"),
    NOTIFY_EC_CHANGE_WAYBILL_CALLBACK("erp.logistics.notifyECChangeWaybill.callback", "通知电商修改运单回调"),
    NOTIFY_EC_CLOSE_WAYBILL_CALLBACK("erp.logistics.notifyECCloseWaybill.callback", "通知电商作废运单回调"),
    NOTIFY_ERP_COMPLETE_CONCRETE("erp.logistics.notifyCompleteConcrete", "通知ERP完成混凝土运单"),
    DELIVERY_NOTE_SYN("com.ecommerce.logistics.deliveryNote.syn", "送货单同步到trace"),
    SAVE_SELF_TAKE_WAYBILL("com.ecommerce.logistics.waybill.saveSelfTakeWaybill", "自提运单保存trace"),
    BKB_WAYBILL_LEAVE_WAREHOUSE("com.ecommerce.logistics.waybill.leaveWarehouse", "背靠背运单出站后关联运单出站"),
    BKB_WAYBILL_RELATION_CLOSE("com.ecommerce.logistics.waybill.relation.close", "背靠背运单出站后关联运单关闭"),
    BKB_WAYBILL_RELATION_DRIVER_CANCLE("com.ecommerce.logistics.waybill.relation.driverCancle", "背靠背运单司机取消后关联运单取消"),
    BKB_WAYBILL_RELATION_PLATFORM_CANCLE("com.ecommerce.logistics.waybill.relation.platformCancle", "背靠背运单平台取消后关联运单取消"),
    BKB_WAYBILL_RELATION_COMPLETE("com.ecommerce.logistics.waybill.relation.complete", "背靠背运单完成后关联运单完成"),
    BKB_SYNC_CREATE_PRIMARY_TAKE_INFO("com.ecommerce.logistics.sync.createPrimaryTakeInfo", "背靠背二级运单确认时同步生成一级发货单"),
    BKB_SYNC_CREATE_RELATION_WAYBILL("com.ecommerce.logistics.sync.createRelationWaybill", "背靠背调度运单确认时同步生成关联运单"),
    BKB_SYNC_REASSIGNMENT_VEHICLE("com.ecommerce.logistics.sync.reassignmentVehicle", "背靠背二级买家自提运单重新指派车辆时同步一级运单"),
    BKB_SYNC_ASSIGN_WAYBILL("com.ecommerce.logistics.sync.assignWaybill", "背靠背同步关联的二级卖家配送运单-取消后重新指派"),
    BKB_SYNC_PICKING_BILL_RELATION_CLOSE("com.ecommerce.logistics.sync.closePickingBill", "背靠背一级提货单关闭同步到二级提货单"),
    BKB_WAYBILL_RELATION_OPEN_CABIN("com.ecommerce.logistics.sync.openCabinWaybill", "背靠背一级开仓同步到二级"),
    SEND_CHECK_WAYBILL("com.ecommerce.logistics.send.checkWaybill", "发送对账单数据"),
    SEND_SOCIAL_DRIVER_CARRIAGE("com.ecommerce.logistics.send.socialDriver.carriage", "社会运力运费通知"),
    CARRY_SUBMIT_WAYBILL_FINISH("com.ecommerce.logistics.carry.submit.waybillFinish", "运单完成时无车承运人的处理"),
    CARRY_SUBMIT_DELIVERY_END("com.ecommerce.logistics.carry.submit.deliveryEnd", "委托结束时无车承运人的处理"),
    CARRY_SUBMIT_DRIVER_SETTLEMENT_FINISH("com.ecommerce.logistics.carry.submit.driverSettlementFinish", "司机结算完成时无车承运人的处理"),
    REFUND_NOTIFY_RECONCILIATION("com.ecommerce.logistics.refund.externalRefundCallBack", "退货通知对账处理");

    private final String code;
    private final String desc;

    private LogisticsMessageTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static LogisticsMessageTypeEnum valueOfCode(String code) {
        for (LogisticsMessageTypeEnum item : LogisticsMessageTypeEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

