
package com.ecommerce.logistics.api.enums;

public enum LogisticsValueSetEnum {
    CARRIER_TYPE("03001", "承运商类型"),
    DELIVERY_TIME_RANGE("03002", "配送时间范围"),
    DISPATCH_BILL_STATUS("03003", "调度单状态"),
    PICKING_BILL_ASSIGN_MODE("03004", "提货单指派模式"),
    PICKING_BILL_STATUS("03005", "提货单状态"),
    PICKING_BILL_TYPE("03006", "提货单类型"),
    WAYBILL_STATUS("03007", "运单状态"),
    WAYBILL_TYPE("03008", "运单类型"),
    VEHICLE_CERTIFICATION_STATUS("03009", "车辆认证状态"),
    VEHICLE_TYPE_AXLES("03010", "车型轴数"),
    VEHICLE_TYPE_CARRIAGE_TYPE("03011", "车型车厢类型"),
    TRANSPORT_MODE("03012", "运输方式"),
    PRODUCT_UNIT("03013", "商品单位"),
    WAREHOUSE_TYPE("03014", "仓库类型"),
    VEHICLE_LICENCE_PLATE_COLOR("03015", "车牌颜色"),
    ATTACHMENT_TYPE("03016", "附件类型"),
    PICKING_BILL_DATA_SOURCE("03017", "提货单数据来源"),
    WAYBILL_OPERATION_TYPE("03018", "运单操作类型"),
    METERING_UNIT("03019", "计量单位类型"),
    TRANSPORT_UNIT("03020", "运输单位类型"),
    TRANSPORT_CAPACITY_STATUS("03021", "运力状态"),
    TRANSPORT_DEMAND_STATUS("03022", "运输需求状态"),
    TRANSPORT_TOOL_TYPE("03023", "运输工具类型"),
    TRANSPORT_ADDRESS_TYPE("03024", "运输地址类型"),
    SHIPPING_TYPE("03025", "船舶类型"),
    GPS_PROTOCOL_TYPE("03026", "GPS协议类型"),
    CARRIAGE_SECTION_TYPE("03027", "运价范围类型"),
    WAYBILL_EXTERNAL_STATUS("03028", "运单外部状态"),
    EXTERNAL_SYNC_FLAG("03029", "外部系统同步标识"),
    EXTERNAL_EXCEPTION_STATUS("03030", "外部异常处理状态"),
    EXTERNAL_METHOD_TYPE("03031", "外部方法类型"),
    CARRIAGE_PRICING_TYPE("03032", "运费定价类型"),
    CARRIAGE_SETTLEMENT_TYPE("03033", "运费结算类型"),
    DELIVERY_NOTE_STATUS("03034", "送货单状态枚举"),
    STOCK_DIRECT("03035", "库存方向枚举"),
    END_REASON("03036", "结束原因枚举"),
    SOURCE_TYPE("03037", "单据来源类型"),
    INVOICE_TARGET_TYPE("03038", "开票对象类型"),
    CARRIAGE_LOG_CATEGORY_TYPE("03039", "运费规则日志类别"),
    BILL_PROXY_TYPE("03040", "单据代理类型"),
    PROXY_WAYBILL_SYNC_TYPE("03041", "背靠背运单同步类型"),
    DEAL_STATUS_FRONT("03042", "投诉与建议状态处理枚举"),
    DEAL_STATUS_END("03043", "投诉与建议状态处理枚举"),
    FEEDBACK_TYPE("03044", "反馈类型枚举"),
    COMPLAINT_TYPE("03045", "投诉类型枚举"),
    RELATION_BILL_TYPE("03046", "关联单据类型枚举"),
    EVALUATE_TYPE("03047", "评价类型枚举"),
    EVALUATE_DIMENSION("03048", "评价维度枚举"),
    EVALUATOR_TYPE("03049", "评价人类型枚举"),
    VEHICLE_AUDIT_STATUS("03050", "车辆审核状态"),
    ENERGY_TYPE("03051", "能源类型"),
    SETTLEMENT_CYCLE("03052", "结算周期枚举"),
    TRANSPORT_CATEGORY("03053", "运输品类枚举"),
    BREAK_DEAL_STATUS("03054", "违约处理状态枚举"),
    UNLOADING_OBJECTION_STATUS("03055", "卸货异议状态枚举"),
    MEMBER_ROLE_TYPE("03056", "会员Member的角色类型"),
    PAY_STATUS("03057", "支付状态"),
    ENTRUST_SOURCE("03058", "委托来源"),
    DELIVERY_BILL_STATUS("03059", "委托单状态"),
    SHIP_BILL_STATUS("03060", "运单状态"),
    SHIP_BILL_ITEM_STATUS("03061", "运单子项状态"),
    VEHICLE_STATUS("03062", "车辆状态"),
    DISPATCH_RULE("03063", "调度规则"),
    DISPATCH_RESULT_STATUS("03064", "调度结果确认状态");

    static final String LOGISTICS_PREFIX = "03";
    private final String code;
    private final String desc;

    private LogisticsValueSetEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static LogisticsValueSetEnum valueOfCode(String code) {
        for (LogisticsValueSetEnum item : LogisticsValueSetEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

