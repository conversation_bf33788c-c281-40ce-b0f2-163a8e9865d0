
package com.ecommerce.logistics.api.enums;

public enum MemberRoleTypeEnum {
    BUYER("030560100", "买家", 1),
    SE<PERSON><PERSON>("030560200", "卖家", 2),
    CAR<PERSON><PERSON>("030560300", "承运商", 3),
    VIRTUAL("030560400", "虚拟", 0),
    PLATFORM("030560500", "平台", 5),
    SOCIEY("030560600", "社会", 3);

    private final String code;
    private final String desc;
    private final Integer userRole;

    private MemberRoleTypeEnum(String code, String desc, Integer userRole) {
        this.code = code;
        this.desc = desc;
        this.userRole = userRole;
    }

    public static MemberRoleTypeEnum valueOfCode(String code) {
        for (MemberRoleTypeEnum item : MemberRoleTypeEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }

    public Integer getUserRole() {
        return this.userRole;
    }
}

