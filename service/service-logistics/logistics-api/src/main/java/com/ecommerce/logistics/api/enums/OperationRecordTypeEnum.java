
package com.ecommerce.logistics.api.enums;

public enum OperationRecordTypeEnum {
    WAYBILL(1, "运单"),
    PICKING_BILL(2, "提货单"),
    DISPATCH_BILL(3, "调度单"),
    VEHICLE(4, "车辆"),
    DELIVERY_NOTE(5, "送货单"),
    COMPLAINT_FOLLOW(6, "投诉跟进记录"),
    TRUSTEE_CONTRACT(7, "托运合同"),
    UNLOADING_OBJECTION(8, "卸货异议"),
    BREAK_FOLLOW(9, "违约跟进记录"),
    DRIVER_CONTRACT(10, "司机合同"),
    DELIVERY_BILL(11, "委托单");

    private final Integer code;
    private final String desc;

    private OperationRecordTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static OperationRecordTypeEnum valueOfCode(Integer code) {
        for (OperationRecordTypeEnum item : OperationRecordTypeEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

