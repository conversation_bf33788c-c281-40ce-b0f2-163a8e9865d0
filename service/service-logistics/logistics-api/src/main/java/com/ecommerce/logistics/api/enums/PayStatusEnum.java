
package com.ecommerce.logistics.api.enums;

public enum PayStatusEnum {
    WAIT_PAY("030570100", "待支付"),
    IN_PAY("030570200", "支付中"),
    SUCCESS_PAY("030570300", "支付成功"),
    FAIL_PAY("030570400", "支付失败");

    private final String code;
    private final String desc;

    private PayStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PayStatusEnum valueOfCode(String code) {
        for (PayStatusEnum item : PayStatusEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

