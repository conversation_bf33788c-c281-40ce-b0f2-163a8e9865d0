
package com.ecommerce.logistics.api.enums;

public enum PickingBillAssignModeEnum {
    AUTO("030040100", "自动"),
    MANUAL("030040200", "手动");

    private final String code;
    private final String desc;

    private PickingBillAssignModeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PickingBillAssignModeEnum valueOfCode(String code) {
        for (PickingBillAssignModeEnum item : PickingBillAssignModeEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

