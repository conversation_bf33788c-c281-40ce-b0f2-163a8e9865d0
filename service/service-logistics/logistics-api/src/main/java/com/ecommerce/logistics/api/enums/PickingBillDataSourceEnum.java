
package com.ecommerce.logistics.api.enums;

public enum PickingBillDataSourceEnum {
    TRADE_SYNCHRONIZE("030170100", "交易同步"),
    MANUAL_ENTERING("030170200", "手动录入"),
    INTERNAL_ALLOCATION("030170300", "内部调拨");

    private final String code;
    private final String desc;

    private PickingBillDataSourceEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PickingBillDataSourceEnum valueOfCode(String code) {
        for (PickingBillDataSourceEnum item : PickingBillDataSourceEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

