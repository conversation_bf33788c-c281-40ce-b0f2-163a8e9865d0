
package com.ecommerce.logistics.api.enums;

public enum PickingBillStatusEnum {
    NEW("030050100", "新建"),
    ASSIGNING("030050200", "指派中"),
    ASSIGNED("030050300", "已指派"),
    COMPLETED("030050400", "已完成"),
    CLOSED("030050500", "已关闭"),
    TIMEOUT("030050600", "已超时"),
    REROUTE("030050700", "已改航");

    private final String code;
    private final String desc;

    private PickingBillStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PickingBillStatusEnum valueOfCode(String code) {
        for (PickingBillStatusEnum item : PickingBillStatusEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

