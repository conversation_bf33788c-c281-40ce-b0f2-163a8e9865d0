
package com.ecommerce.logistics.api.enums;

public enum PickingBillTypeEnum {
    BUYER_TAKE("030060100", "买家自提"),
    SELLER_DELIVERY("030060200", "卖家配送"),
    PLATFORM_DELIVERY("030060300", "平台配送"),
    VENDOR_ENTRUSTMENT("030060400", "卖家委托"),
    STORE_DELIVERY("030060500", "门店配送");

    private final String code;
    private final String desc;

    private PickingBillTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PickingBillTypeEnum valueOfCode(String code) {
        for (PickingBillTypeEnum item : PickingBillTypeEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

