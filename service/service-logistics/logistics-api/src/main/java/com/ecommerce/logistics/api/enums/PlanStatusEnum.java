
package com.ecommerce.logistics.api.enums;

public enum PlanStatusEnum {
    WAIT_CONFIRM(0, "待确认"),
    WAIT_DELIVER(1, "待发货"),
    DELIVERING(2, "发货中"),
    CANCELED(3, "已取消"),
    REASSIGNED(4, "已改派"),
    COMPLETED(5, "已完成");

    private final int code;
    private final String desc;

    private PlanStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PlanStatusEnum valueOfCode(int code) {
        for (PlanStatusEnum item : PlanStatusEnum.values()) {
            if (item.code != code) continue;
            return item;
        }
        return null;
    }

    public int getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

