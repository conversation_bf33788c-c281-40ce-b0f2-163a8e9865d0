
package com.ecommerce.logistics.api.enums;

public enum PorterageMeteringUnitEnum {
    METERING_BAG("030190100", "袋"),
    METERING_TON("030190200", "吨");

    private final String code;
    private final String desc;

    private PorterageMeteringUnitEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PorterageMeteringUnitEnum valueOfCode(String code) {
        for (PorterageMeteringUnitEnum item : PorterageMeteringUnitEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

