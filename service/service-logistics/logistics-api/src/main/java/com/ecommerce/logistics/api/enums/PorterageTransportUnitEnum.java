
package com.ecommerce.logistics.api.enums;

public enum PorterageTransportUnitEnum {
    TRANSPORT_FLOOR("030200100", "层"),
    TRANSPORT_METRE("030200200", "米");

    private final String code;
    private final String desc;

    private PorterageTransportUnitEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PorterageTransportUnitEnum valueOfCode(String code) {
        for (PorterageTransportUnitEnum item : PorterageTransportUnitEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

