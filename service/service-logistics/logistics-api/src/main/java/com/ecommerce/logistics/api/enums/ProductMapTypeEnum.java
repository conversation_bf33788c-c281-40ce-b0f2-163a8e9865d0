
package com.ecommerce.logistics.api.enums;

public enum ProductMapTypeEnum {
    PICKING_BILL(1, "提货单"),
    DISPATCH_BILL(2, "调度单"),
    WAYBILL(3, "运单");

    private final Integer code;
    private final String desc;

    private ProductMapTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ProductMapTypeEnum valueOfCode(Integer code) {
        for (ProductMapTypeEnum item :  ProductMapTypeEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

