
package com.ecommerce.logistics.api.enums;

public enum ProductUnitTypeEnum {
    TON("030130100", "吨"),
    LITER("030130200", "升"),
    SQUARE_METRE("030130300", "平方米"),
    CUBIC_METRE("030130400", "立方米");

    private final String code;
    private final String desc;

    private ProductUnitTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ProductUnitTypeEnum valueOfCode(String code) {
        for (ProductUnitTypeEnum item :  ProductUnitTypeEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

