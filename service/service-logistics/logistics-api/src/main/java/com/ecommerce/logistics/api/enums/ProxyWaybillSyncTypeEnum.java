
package com.ecommerce.logistics.api.enums;

public enum ProxyWaybillSyncTypeEnum {
    PUBLISH_WAYBILL("030410100", "发布运单"),
    LEAVE_WAYBILL("030410200", "出站"),
    PLATFORM_CANCEL_WAYBILL("030410300", "平台取消运单"),
    DRIVER_CANCEL_WAYBILL("030410400", "司机取消运单"),
    CLOSE_WAYBILL("030410500", "关闭运单"),
    COMPLETE_WAYBILL("030410600", "完成运单"),
    SELF_TAKE_RE_ASSIGN("030410700", "自提单重新指派"),
    CANCELED_RE_ASSIGN("030410800", "指派已取消的运单"),
    CLOSE_CLOSE_PICKING_BILL("030410900", "关闭提货单"),
    OPEN_CABIN("030411000", "开仓");

    private final String code;
    private final String desc;

    private ProxyWaybillSyncTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ProxyWaybillSyncTypeEnum valueOfCode(String code) {
        for (ProxyWaybillSyncTypeEnum item :  ProxyWaybillSyncTypeEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

