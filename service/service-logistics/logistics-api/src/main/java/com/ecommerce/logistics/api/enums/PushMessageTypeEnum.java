
package com.ecommerce.logistics.api.enums;

public enum PushMessageTypeEnum {
    SECKILL_BILL("driver/snatchHall/details/", "司机/承运商抢单推送跳转uri"),
    ASSIGN_DRIVER("driver/waybillMana/details/", "承运商指派司机推送跳转uri"),
    PAY_CALLBACK("backToBack/submit", "二级订单支付回调时通知卖家");

    private final String code;
    private final String desc;

    private PushMessageTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PushMessageTypeEnum valueOfCode(String code) {
        for (PushMessageTypeEnum item :  PushMessageTypeEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

