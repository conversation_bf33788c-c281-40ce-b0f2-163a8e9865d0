
package com.ecommerce.logistics.api.enums;

public enum QueryWaybillTypeEnum {
    DIRECT_QUERY(1, "直接sql查询"),
    CONDITION_QUERY(2, "条件sql查询");

    private final Integer code;
    private final String desc;

    private QueryWaybillTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static QueryWaybillTypeEnum valueOfCode(Integer code) {
        for (QueryWaybillTypeEnum item :  QueryWaybillTypeEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

