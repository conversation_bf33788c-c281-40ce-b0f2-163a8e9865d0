
package com.ecommerce.logistics.api.enums;

public enum RefundReasonEnum {
    MODIFY_CATEGORY("1", "同一客户更改商品品种", "修改品种"),
    MODIFY_ADDRESS("2", "同一客户更改收货地址", "修改地址"),
    MODIFY_SELLER("3", "更改客户", "改卖家"),
    OTHER("4", "其他", "其他");

    private final String code;
    private final String value;
    private final String desc;

    private RefundReasonEnum(String code, String value, String desc) {
        this.code = code;
        this.value = value;
        this.desc = desc;
    }

    public static RefundReasonEnum valueOfCode(String code) {
        for (RefundReasonEnum item :  RefundReasonEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }

    public String getValue() {
        return this.value;
    }
}

