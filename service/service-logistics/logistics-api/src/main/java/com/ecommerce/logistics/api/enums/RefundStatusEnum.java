
package com.ecommerce.logistics.api.enums;

public enum RefundStatusEnum {
    APPLIED("030690001", "已申请"),
    ERP_PROCESSING("030690002", "ERP处理中"),
    REFUND_COMPLETED("030690003", "退货完成");

    private final String code;
    private final String desc;

    private RefundStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static RefundStatusEnum valueOfCode(String code) {
        for (RefundStatusEnum item :  RefundStatusEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

