
package com.ecommerce.logistics.api.enums;

public enum RelationBillTypeEnum {
    PICK_BILL("046510100", "提货单"),
    DISPATCH_BILL("046510200", "调度单"),
    WAY_BILL("046510300", "运单");

    private final String code;
    private final String desc;

    private RelationBillTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static RelationBillTypeEnum valueOfCode(String code) {
        for (RelationBillTypeEnum item : RelationBillTypeEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

