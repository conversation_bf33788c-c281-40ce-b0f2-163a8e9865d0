
package com.ecommerce.logistics.api.enums;

import com.google.common.collect.Lists;
import java.util.ArrayList;

public enum ShipBillItemStatusEnum {
    WAIT_DELIVERY("030610100", "待配送"),
    LEAVE_WAREHOUSE("030610200", "已出厂"),
    WAREHOUSE_WAIT_SUPPLEMENT("030610300", "出厂待补款"),
    OPEN_CABIN("030610400", "已开仓"),
    CABIN_WAIT_SUPPLEMENT("030610500", "开仓待补款"),
    COMPLETE("030610600", "已完成"),
    CLOSE("030610700", "已关闭"),
    REFUND("030610800", "已退货");

    private final String code;
    private final String desc;

    private ShipBillItemStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ShipBillItemStatusEnum valueOfCode(String code) {
        for (ShipBillItemStatusEnum item :  ShipBillItemStatusEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public static boolean needSupple(String code) {
        ArrayList<String> suppleStatusList = Lists.newArrayList(new String[]{ShipBillItemStatusEnum.WAREHOUSE_WAIT_SUPPLEMENT.code, ShipBillItemStatusEnum.CABIN_WAIT_SUPPLEMENT.code});
        return suppleStatusList.contains(code);
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

