
package com.ecommerce.logistics.api.enums;

public enum ShipBillOperateEnum {
    CANCEL("cancel", "取消"),
    QR_CODE("qrCode", "查看二维码"),
    CLOSE("close", "关闭运单"),
    ASSIGN("assign", "指派"),
    LEAVE_WAREHOUSE("leave_warehouse", "出站"),
    OPEN_CABIN("open_cabin", "开仓"),
    SIGN("sign", "签收"),
    COMPLETE("complete", "完成"),
    UPLOAD_CERTIFICATE("uploadCertificate", "上传支付凭证"),
    UNLOADING_REMIND("unloading_remind", "卸货提醒"),
    OPEN_CABIN_REMIND("open_cabin_remind", "开仓提醒"),
    SHIP_ARRIVE("ship_arrive", "船已到港"),
    TRACK_PLAYBACK("track_playback", "轨迹回放"),
    WARNING_CHECK("warning_check", "报警查看"),
    REASSIGNMENT("reassignment", "重新指派");

    private final String code;
    private final String message;

    private ShipBillOperateEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return this.code;
    }

    public String getMessage() {
        return this.message;
    }

    public static ShipBillOperateEnum getByCode(String code) {
        for (ShipBillOperateEnum _enum : ShipBillOperateEnum.values()) {
            if (!_enum.getCode().equals(code)) continue;
            return _enum;
        }
        return null;
    }
}

