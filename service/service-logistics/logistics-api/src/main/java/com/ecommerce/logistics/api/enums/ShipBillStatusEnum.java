
package com.ecommerce.logistics.api.enums;

import com.google.common.collect.Lists;
import java.util.ArrayList;

public enum ShipBillStatusEnum {
    WAIT_AUDIT("030600100", "待审核"),
    WAIT_RECEIVE("030600200", "待接单"),
    WAIT_ASSIGN("030600210", "待指派"),
    SNATCHED("030600300", "已抢单"),
    CANCELED("030600400", "已取消"),
    WAIT_CONFIRM("030600500", "待确认"),
    WAIT_DELIVERY("030600600", "待配送"),
    DELIVERING("030600700", "配送中"),
    SIGNED("030600800", "已送达"),
    COMPLETE("030600900", "已完成"),
    CLOSED("030601000", "已关闭"),
    REFUND("030601100", "已退货");

    private final String code;
    private final String desc;

    private ShipBillStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ShipBillStatusEnum valueOfCode(String code) {
        for (ShipBillStatusEnum item :  ShipBillStatusEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public static boolean isFinalStatus(String code) {
        ArrayList<String> finalStatusList = Lists.newArrayList(new String[]{ShipBillStatusEnum.COMPLETE.code, ShipBillStatusEnum.CLOSED.code, ShipBillStatusEnum.REFUND.code});
        return finalStatusList.contains(code);
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

