
package com.ecommerce.logistics.api.enums;

public enum ShippingBusinessTypeEnum {
    SHIPPING_ROUTE(1, "航线运费"),
    SHIPPING_INFO(2, "船舶");

    private final int code;
    private final String desc;

    private ShippingBusinessTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ShippingBusinessTypeEnum valueOfCode(int code) {
        for (ShippingBusinessTypeEnum item :  ShippingBusinessTypeEnum.values()) {
            if (item.code != code) continue;
            return item;
        }
        return null;
    }

    public int getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

