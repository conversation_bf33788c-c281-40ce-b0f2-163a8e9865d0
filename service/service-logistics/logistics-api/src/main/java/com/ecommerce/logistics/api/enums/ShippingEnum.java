
package com.ecommerce.logistics.api.enums;

public class ShippingEnum {

    public static enum OperationTypeEnum {
        SAVE(1, "保存"),
        SUBMIT(2, "提交"),
        PASS(3, "通过"),
        REJECTE(4, "驳回"),
        UPDAT_INFO(5, "更新信息"),
        UPDATE_FAILED(6, "更新驳回");

        private final int code;
        private final String desc;

        private OperationTypeEnum(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static String valueOfCode(int code) {
            for (OperationTypeEnum item :  OperationTypeEnum.values()) {
                if (item.code != code) continue;
                return item.getDesc();
            }
            return null;
        }

        public int getCode() {
            return this.code;
        }

        public String getDesc() {
            return this.desc;
        }
    }

    public static enum OwnershipEnum {
        HAVE(1, "自有(控股50%及以上)"),
        SHARE(2, "参股(控股50%以下)"),
        RENT_ONLY(3, "光租"),
        AFFILIATION(4, "挂靠"),
        BUYER_IMPORT(5, "买家引入"),
        SELLER_IMPORT(6, "卖家引入");

        private final int code;
        private final String desc;

        private OwnershipEnum(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static OwnershipEnum valueOfCode(int code) {
            OwnershipEnum[] enums;
            for (OwnershipEnum item : enums = OwnershipEnum.values()) {
                if (item.code != code) continue;
                return item;
            }
            return null;
        }

        public int getCode() {
            return this.code;
        }

        public String getDesc() {
            return this.desc;
        }
    }

    public static enum ShippingStatusEnum {
        EMPTY_SHIP(1, "吉船"),
        WAITE_LOAD(2, "待装货"),
        HEAVY_SHIP(3, "重船"),
        OVERHAULING(4, "检修中"),
        DISABLED(5, "已禁用");

        private final int code;
        private final String desc;

        private ShippingStatusEnum(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static ShippingStatusEnum valueOfCode(int code) {
            ShippingStatusEnum[] enums;
            for (ShippingStatusEnum item : enums = ShippingStatusEnum.values()) {
                if (item.code != code) continue;
                return item;
            }
            return null;
        }

        public int getCode() {
            return this.code;
        }

        public String getDesc() {
            return this.desc;
        }
    }

    public static enum ServerTypeEnum {
        DISABLE(0, "不可用"),
        FIXED(1, "固定"),
        TEMPORARY(2, "临时");

        private final int code;
        private final String desc;

        private ServerTypeEnum(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static ServerTypeEnum valueOfCode(int code) {
            ServerTypeEnum[] enums;
            for (ServerTypeEnum item : enums = ServerTypeEnum.values()) {
                if (item.code != code) continue;
                return item;
            }
            return null;
        }

        public int getCode() {
            return this.code;
        }

        public String getDesc() {
            return this.desc;
        }
    }

    public static enum AuditStatusEnum {
        WAIT_SUBMIT(1, "待提交"),
        WAIT_AUDIT(2, "待审核"),
        PASSED(3, "已通过"),
        REJECTED(4, "已驳回"),
        UPDATING(5, "更新中"),
        UPDATE_FAILED(6, "更新失败");

        private final int code;
        private final String desc;

        private AuditStatusEnum(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static AuditStatusEnum valueOfCode(int code) {
            AuditStatusEnum[] enums;
            for (AuditStatusEnum item : enums = AuditStatusEnum.values()) {
                if (item.code != code) continue;
                return item;
            }
            return null;
        }

        public int getCode() {
            return this.code;
        }

        public String getDesc() {
            return this.desc;
        }
    }
}

