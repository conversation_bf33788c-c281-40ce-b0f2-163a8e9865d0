
package com.ecommerce.logistics.api.enums;

public class ShippingOperateEnum {

    private ShippingOperateEnum(){
        throw new IllegalStateException("ShippingOperateEnum class");
    }

    public static final String INSERT = "新增";
    public static final String UPDATE = "修改";
    public static final String DELETE = "删除";
    public static final String SUBMIT = "提交";
    public static final String PASSE = "通过";
    public static final String REJECTE = "驳回";
    public static final String BIND_CAPTAIN = "绑定船长";
    public static final String UN_BIND_CAPTAIN = "解绑船长";
    public static final String DISABLE_SHIPPING = "禁用船舶";
    public static final String SERVERTYPE = "修改服务类型";
    public static final String AUTHORIZE_SELLER = "批量授权卖家";
    public static final String CANCEL_AUTHORIZE_SELLER = "批量授权卖家";
    public static final String BATCH_OVERHAULE = "批量船舶检修";
    public static final String UPDATE_SHIPPING = "修改船舶状态";
}

