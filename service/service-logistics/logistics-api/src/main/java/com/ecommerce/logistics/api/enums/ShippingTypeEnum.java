
package com.ecommerce.logistics.api.enums;

public enum ShippingTypeEnum {
    OPEN_SHIPPING("030250100", "开仓船"),
    TANKER("030250200", "罐船"),
    SELF_DUMPING_SHIPPING("030250300", "皮带自卸船"),
    RIVER_SHIP("030250400", "江海直达船"),
    SEA_SHIPPINGF("030250500", "海船"),
    OTHER_SHIPPING("030251000", "其他船只"),
    UNLIMITED_SHIPPING("030252000", "不限船型"),
    RIVERS_SHIPPING("030253000", "江船");

    private final String code;
    private final String desc;

    private ShippingTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ShippingTypeEnum valueOfCode(String code) {
        for (ShippingTypeEnum item :  ShippingTypeEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

