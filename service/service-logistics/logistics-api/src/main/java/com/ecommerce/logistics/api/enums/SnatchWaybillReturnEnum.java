
package com.ecommerce.logistics.api.enums;

public enum SnatchWaybillReturnEnum {
    SUCCESS("0001", "抢单成功"),
    NOT_EXIST("0002", "运单不存在"),
    BEEN_SECKILLED("0003", "运单已被抢"),
    OVER_SECKILL_COUNT("0004", "抢单数量已达到上限"),
    ROLE_ERROR("0005", "认证后才能抢单"),
    NO_VEHICLE("0006", "请先添加车辆"),
    NO_DEPOSIT("0007", "请完善保证金");

    private final String code;
    private final String desc;

    private SnatchWaybillReturnEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static SnatchWaybillReturnEnum valueOfCode(String code) {
        for (SnatchWaybillReturnEnum item : SnatchWaybillReturnEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

