
package com.ecommerce.logistics.api.enums;

public enum SourceTypeEnum {
    FROM_BUYER("030370100", "来自买家"),
    FROM_SELLER("030370200", "来自卖家"),
    FROM_PLATFORM("030370300", "来自平台");

    private String code;
    private String desc;

    private SourceTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static SourceTypeEnum valueOfCode(String code) {
        for (SourceTypeEnum item :  SourceTypeEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

