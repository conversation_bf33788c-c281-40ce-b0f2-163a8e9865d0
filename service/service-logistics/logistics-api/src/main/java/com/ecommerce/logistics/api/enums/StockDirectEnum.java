
package com.ecommerce.logistics.api.enums;

public enum StockDirectEnum {
    PUT_IN("030350100", "入库"),
    CHECK_OUT("030350200", "出库");

    private String code;
    private String desc;

    private StockDirectEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static StockDirectEnum valueOfCode(String code) {
        for (StockDirectEnum item :  StockDirectEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

