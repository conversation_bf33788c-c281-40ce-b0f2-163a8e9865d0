
package com.ecommerce.logistics.api.enums;

public enum TransportAddressTypeEnum {
    PICKING_ADDRESS("030240100", "提货地址"),
    UNLOAD_ADDRESS("030240200", "卸货地址");

    private final String code;
    private final String desc;

    private TransportAddressTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static TransportAddressTypeEnum valueOfCode(String code) {
        for (TransportAddressTypeEnum item : TransportAddressTypeEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

