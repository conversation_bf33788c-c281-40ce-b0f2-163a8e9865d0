
package com.ecommerce.logistics.api.enums;

public enum TransportCapacityStatusEnum {
    WAIT_AUDIT("030210100", "待审核"),
    AUDIT_FAIL("030210200", "审核失败"),
    AUDIT_PASS("030210300", "审核通过");

    private final String code;
    private final String desc;

    private TransportCapacityStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static TransportCapacityStatusEnum valueOfCode(String code) {
        for (TransportCapacityStatusEnum item : TransportCapacityStatusEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

