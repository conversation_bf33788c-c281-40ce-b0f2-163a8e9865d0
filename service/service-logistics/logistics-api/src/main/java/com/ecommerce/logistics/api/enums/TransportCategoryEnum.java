
package com.ecommerce.logistics.api.enums;

@Deprecated(since = "2.1.4-RELEASE")
public enum TransportCategoryEnum {
    BAGGED_CEMENT("030520100", "袋装水泥"),
    BULK_CEMENT("030520200", "散装水泥"),
    MINERAL_POWDER("030520300", "矿粉"),
    OTHER("030520500", "其他");

    private final String code;
    private final String desc;

    private TransportCategoryEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static TransportCategoryEnum valueOfCode(String code) {
        for (TransportCategoryEnum item :TransportCategoryEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

