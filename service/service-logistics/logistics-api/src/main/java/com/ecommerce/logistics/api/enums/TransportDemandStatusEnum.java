
package com.ecommerce.logistics.api.enums;

public enum TransportDemandStatusEnum {
    WAIT_AUDIT("030220100", "待审核"),
    AUDIT_FAIL("030220200", "审核失败"),
    AUDIT_PASS("030220300", "审核通过");

    private final String code;
    private final String desc;

    private TransportDemandStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static TransportDemandStatusEnum valueOfCode(String code) {
        for (TransportDemandStatusEnum item : TransportDemandStatusEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

