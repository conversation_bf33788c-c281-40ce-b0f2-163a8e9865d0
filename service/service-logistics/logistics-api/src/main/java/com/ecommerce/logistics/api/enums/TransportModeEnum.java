
package com.ecommerce.logistics.api.enums;

public enum TransportModeEnum {
    TRUCK("030120100", "汽运"),
    STEAMER("030120200", "船运"),
    TRAIN("030120300", "铁运");

    private final String code;
    private final String desc;

    private TransportModeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static TransportModeEnum valueOfCode(String code) {
        for (TransportModeEnum item : TransportModeEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

