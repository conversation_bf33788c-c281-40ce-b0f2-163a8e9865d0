
package com.ecommerce.logistics.api.enums;

public enum TransportToolTypeEnum {
    ROAD_TRANSPORT("030230100", "路运"),
    WATER_TRANSPORT("030230200", "水运"),
    AIR_TRANSPORT("030230300", "空运"),
    RAIL_TRANSPORT("030230400", "铁运");

    private final String code;
    private final String desc;

    private TransportToolTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static TransportToolTypeEnum valueOfCode(String code) {
        for (TransportToolTypeEnum item : TransportToolTypeEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

