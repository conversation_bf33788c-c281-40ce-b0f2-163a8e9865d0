
package com.ecommerce.logistics.api.enums;

public class TrusteeContractEnum {

    public static enum SettlementCycleEnum {
        WEEK_SETTLEMENT("030520100", "按周结"),
        TWO_WEEK_SETTLEMENT("030520200", "按半月结"),
        month_SETTLEMENT("030520300", "按月结"),
        QUARTER_SETTLEMENT("030520400", "按季结"),
        OTHER("030520500", "其他");

        private final String code;
        private final String desc;

        private SettlementCycleEnum(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static SettlementCycleEnum valueOfCode(String code) {
            for (SettlementCycleEnum item : SettlementCycleEnum.values()) {
                if (!item.code.equals(code)) continue;
                return item;
            }
            return null;
        }

        public String getCode() {
            return this.code;
        }

        public String getDesc() {
            return this.desc;
        }
    }

    public static enum InvoiceTypeEnum {
        OTHER(0, "其他"),
        SPECIAL_INVOICE(1, "增值税专用发票"),
        ORDINARY_INVOICE(2, "增值税普通发票");

        private final Integer code;
        private final String desc;

        private InvoiceTypeEnum(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static InvoiceTypeEnum valueOfCode(Integer code) {
            InvoiceTypeEnum[] enums;
            for (InvoiceTypeEnum item : enums = InvoiceTypeEnum.values()) {
                if (!item.code.equals(code)) continue;
                return item;
            }
            return null;
        }

        public Integer getCode() {
            return this.code;
        }

        public String getDesc() {
            return this.desc;
        }
    }

    public static enum ContractTypeEnum {
        OTHER(0, "其他"),
        MAIN_CONTRACT(1, "主合同"),
        SUPPLEMENT_CONTRACT(2, "补充合同");

        private final Integer code;
        private final String desc;

        private ContractTypeEnum(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static ContractTypeEnum valueOfCode(Integer code) {
            ContractTypeEnum[] enums;
            for (ContractTypeEnum item : enums = ContractTypeEnum.values()) {
                if (!item.code.equals(code)) continue;
                return item;
            }
            return null;
        }

        public Integer getCode() {
            return this.code;
        }

        public String getDesc() {
            return this.desc;
        }
    }
}

