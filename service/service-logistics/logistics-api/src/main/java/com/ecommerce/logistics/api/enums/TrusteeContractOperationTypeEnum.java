
package com.ecommerce.logistics.api.enums;

public enum TrusteeContractOperationTypeEnum {
    TRUSTEE_CONTRACT_NEW(1, "添加托运合同"),
    TRUSTEE_CONTRACT_MODIFY(2, "修改托运合同"),
    TRUSTEE_CONTRACT_REMOVE(3, "删除托运合同");

    private final Integer code;
    private final String desc;

    private TrusteeContractOperationTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static TrusteeContractOperationTypeEnum valueOfCode(Integer code) {
        for (TrusteeContractOperationTypeEnum item : TrusteeContractOperationTypeEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

