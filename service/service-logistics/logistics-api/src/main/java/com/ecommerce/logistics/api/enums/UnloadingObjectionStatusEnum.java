
package com.ecommerce.logistics.api.enums;

public enum UnloadingObjectionStatusEnum {
    WAIT_HANDLE("030550100", "待处理"),
    HANDLING("030550200", "处理中"),
    HANDLED("030550300", "已处理"),
    CANCEL("030550400", "已取消");

    private final String code;
    private final String desc;

    private UnloadingObjectionStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static UnloadingObjectionStatusEnum valueOfCode(String code) {
        for (UnloadingObjectionStatusEnum item : UnloadingObjectionStatusEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

