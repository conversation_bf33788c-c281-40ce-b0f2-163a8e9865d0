
package com.ecommerce.logistics.api.enums;

public enum UserRoleEnum {
    BUYER(1, "买家"),
    SELLER(2, "卖家"),
    CARRIER(3, "承运商"),
    PERSONAL_DRIVER(4, "个人司机"),
    PLATFORM(5, "平台");

    private final Integer code;
    private final String desc;

    private UserRoleEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static UserRoleEnum valueOfCode(Integer code) {
        for (UserRoleEnum item : UserRoleEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

