
package com.ecommerce.logistics.api.enums;

public enum VehicleCertTypeEnum {
    DRIVING_LICENSE("047610100", "行驶证"),
    ROAD_TRANSPORT("047610200", "道路运输证（营运证）");

    private final String code;
    private final String desc;

    private VehicleCertTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static VehicleCertTypeEnum valueOfCode(String code) {
        for (VehicleCertTypeEnum item :  VehicleCertTypeEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

