
package com.ecommerce.logistics.api.enums;

public enum VehicleCertificationStatusEnum {
    NO_AUTHENTICATION("030090100", "未认证"),
    AUTHENTICATION_OK("030090200", "认证成功"),
    AUTHENTICATION_FAILURE("030090300", "认证失败"),
    NEW_STATE("030090400", "新建状态");

    private final String code;
    private final String desc;

    private VehicleCertificationStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static VehicleCertificationStatusEnum valueOfCode(String code) {
        for (VehicleCertificationStatusEnum item :  VehicleCertificationStatusEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

