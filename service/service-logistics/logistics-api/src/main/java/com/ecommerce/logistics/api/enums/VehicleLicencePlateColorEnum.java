
package com.ecommerce.logistics.api.enums;

public enum VehicleLicencePlateColorEnum {
    BLUE("030150100", "蓝色车牌"),
    GREEN_WHITE("030150200", "绿白车牌"),
    GREEN_YELLOW("030150300", "绿黄车牌"),
    YELLOW("030150400", "黄色车牌"),
    AQUA_BOTTOM_WHITE_CHARACTER("030150500", "浅绿底白字");

    private final String code;
    private final String desc;

    private VehicleLicencePlateColorEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static VehicleLicencePlateColorEnum valueOfCode(String code) {
        for (VehicleLicencePlateColorEnum item :  VehicleLicencePlateColorEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

