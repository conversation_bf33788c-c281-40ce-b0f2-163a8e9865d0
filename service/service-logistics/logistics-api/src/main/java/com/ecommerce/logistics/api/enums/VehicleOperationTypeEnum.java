
package com.ecommerce.logistics.api.enums;

public enum VehicleOperationTypeEnum {
    VEHICLE_NEW(1, "添加车辆"),
    VEHICLE_MODIFY(2, "修改车辆"),
    VEHICLE_REMOVE(3, "删除车辆"),
    VEHICLE_AUTH(4, "认证通过"),
    VEHICLE_REJECT(5, "认证驳回"),
    VEHICLE_DEFAULT(6, "设为默认车辆"),
    VEHICLE_NO_DEFAULT(7, "取消默认车辆"),
    VEHICLE_SUBMIT_AUT(8, "提交审核"),
    VEHICLE_BIND_DRITER(9, "车辆绑定司机");

    private final Integer code;
    private final String desc;

    private VehicleOperationTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static VehicleOperationTypeEnum valueOfCode(Integer code) {
        for (VehicleOperationTypeEnum item :  VehicleOperationTypeEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

