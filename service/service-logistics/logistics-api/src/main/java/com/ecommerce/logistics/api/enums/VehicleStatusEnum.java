
package com.ecommerce.logistics.api.enums;

public enum VehicleStatusEnum {
    CAN_DISPATCH("030620001", "可调度"),
    IN_TRANSIT("030620002", "运输中"),
    UNDER_REPAIR("030620003", "检修中");

    private final String code;
    private final String desc;

    private VehicleStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static VehicleStatusEnum valueOfCode(String code) {
        for (VehicleStatusEnum item : VehicleStatusEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

