
package com.ecommerce.logistics.api.enums;

public enum VehicleTypeAxlesEnum {
    AXLES_ZERO("030100000", 0),
    AXLES_TWO("030100100", 2),
    AXLES_THREE("030100200", 3),
    AXLES_FOUR("030100300", 4),
    AXLES_FIVE("030100500", 5),
    AXLES_SIX("030100400", 6);

    private final String code;
    private final Integer desc;

    private VehicleTypeAxlesEnum(String code, Integer desc) {
        this.code = code;
        this.desc = desc;
    }

    public static VehicleTypeAxlesEnum valueOfCode(String code) {
        for (VehicleTypeAxlesEnum item : VehicleTypeAxlesEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public static String getCodeByDesc(int desc) {
        for (VehicleTypeAxlesEnum item :  VehicleTypeAxlesEnum.values()) {
            if (item.desc != desc) continue;
            return item.code;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public Integer getDesc() {
        return this.desc;
    }
}

