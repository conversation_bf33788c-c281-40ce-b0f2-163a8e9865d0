
package com.ecommerce.logistics.api.enums;

public enum VehicleTypeCarriageTypeEnum {
    LOW_BAR_CAR("030110100", "低栏车"),
    HIGH_BAR_CAR("030110200", "高栏车"),
    VAN_CAR("030110300", "厢式车"),
    TANK_CAR("030110400", "罐式专用车"),
    FLATBED_CAR("030110500", "平板车"),
    UNLIMITED("030110600", "不限");

    private final String code;
    private final String desc;

    private VehicleTypeCarriageTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static VehicleTypeCarriageTypeEnum valueOfCode(String code) {
        for (VehicleTypeCarriageTypeEnum item : VehicleTypeCarriageTypeEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

