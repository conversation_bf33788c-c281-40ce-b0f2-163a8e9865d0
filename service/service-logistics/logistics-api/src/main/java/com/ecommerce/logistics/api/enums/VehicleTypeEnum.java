
package com.ecommerce.logistics.api.enums;

public enum VehicleTypeEnum {
    H11("重型普通货车", "H11"),
    H112("重型栏板货车", "H11"),
    H12("重型厢式货车", "H12"),
    H13("重型封闭货车", "H13"),
    H132("重型封闭式货车", "H13"),
    H14("重型罐式货车", "H14"),
    H15("重型平板货车", "H15"),
    H16("重型集装厢车", "H16"),
    H162("重型集装箱车", "H16"),
    H17("重型自卸货车", "H17"),
    H18("重型特殊结构货车", "H18"),
    H19("重型仓栅式货车", "H19"),
    H21("中型普通货车", "H21"),
    H212("中型栏板货车", "H21"),
    H22("中型厢式货车", "H22"),
    H23("中型封闭货车", "H23"),
    H232("中型封闭式货车", "H23"),
    H24("中型罐式货车", "H24"),
    H25("中型平板货车", "H25"),
    H26("中型集装厢车", "H26"),
    H262("中型集装箱车", "H26"),
    H27("中型自卸货车", "H27"),
    H28("中型特殊结构货车", "H28"),
    H29("中型仓栅式货车", "H29"),
    H31("轻型普通货车", "H31"),
    H312("轻型栏板货车", "H31"),
    H32("轻型厢式货车", "H32"),
    H33("轻型封闭货车", "H33"),
    H332("轻型封闭式货车", "H33"),
    H34("轻型罐式货车", "H34"),
    H35("轻型平板货车", "H35"),
    H37("轻型自卸货车", "H37"),
    H38("轻型特殊结构货车", "H38"),
    H39("轻型仓栅式货车", "H39"),
    H41("微型普通货车", "H41"),
    H412("微型栏板货车", "H41"),
    H42("微型厢式货车", "H42"),
    H43("微型封闭货车", "H43"),
    H432("微型封闭式货车", "H43"),
    H44("微型罐式货车", "H44"),
    H45("微型自卸货车", "H45"),
    H46("微型特殊结构货车", "H46"),
    H47("微型仓栅式货车", "H47"),
    H51("普通低速货车", "H51"),
    H512("栏板低速货车", "H51"),
    H52("厢式低速货车", "H52"),
    H53("罐式低速货车", "H53"),
    H54("自卸低速货车", "H54"),
    H55("仓栅式低速货车", "H55"),
    Q11("重型半挂牵引车", "Q11"),
    Q12("重型全挂牵引车", "Q12"),
    Q21("中型半挂牵引车", "Q21"),
    Q22("中型全挂牵引车", "Q22"),
    Q31("轻型半挂牵引车", "Q31"),
    Q32("轻型全挂牵引车", "Q32"),
    Z11("大型专项作业车", "Z11"),
    Z21("中型专项作业车", "Z21"),
    Z31("小型专项作业车", "Z31"),
    Z41("微型专项作业车", "Z41"),
    Z51("重型专项作业车", "Z51"),
    Z71("轻型专项作业车", "Z71"),
    D11("无轨电车", "D11"),
    D12("有轨电车", "D12"),
    M11("普通正三轮摩托车", "M11"),
    M12("轻便正三轮摩托车", "M12"),
    M13("正三轮载客摩托车", "M13"),
    M14("正三轮载货摩托车", "M14"),
    M15("侧三轮摩托车", "M15"),
    M21("普通二轮摩托车", "M21"),
    M22("轻便二轮摩托车", "M22"),
    N11("三轮汽车", "N11"),
    T11("大型轮式拖拉机", "T11"),
    T21("小型轮式拖拉机", "T21"),
    T22("手扶拖拉机", "T22"),
    T23("手扶变形运输机", "T23"),
    J11("轮式装载机械", "J11"),
    J12("轮式挖掘机械", "J12"),
    J13("轮式平地机械", "J13"),
    G11("重型普通全挂车", "G11"),
    G112("重型栏板全挂车", "G11"),
    G12("重型厢式全挂车", "G12"),
    G13("重型罐式全挂车", "G13"),
    G14("重型平板全挂车", "G14"),
    G15("重型集装箱全挂车", "G15"),
    G16("重型自卸全挂车", "G16"),
    G17("重型仓栅式全挂车", "G17"),
    G18("重型旅居全挂车", "G18"),
    G19("重型专项作业全挂车", "G19"),
    G192("重型特殊用途全挂车", "G19"),
    G21("中型普通全挂车", "G21"),
    G212("中型栏板全挂车", "G21"),
    G22("中型厢式全挂车", "G22"),
    G23("中型罐式全挂车", "G23"),
    G24("中型平板全挂车", "G24"),
    G25("中型集装箱全挂车", "G25"),
    G26("中型自卸全挂车", "G26"),
    G27("中型仓栅式全挂车", "G27"),
    G28("中型旅居全挂车", "G28"),
    G29("中型专项作业全挂车", "G29"),
    G292("中型特殊用途全挂车", "G29"),
    G31("轻型普通全挂车", "G31"),
    G312("轻型栏板全挂车", "G31"),
    G32("轻型厢式全挂车", "G32"),
    G33("轻型罐式全挂车", "G33"),
    G34("轻型平板全挂车", "G34"),
    G35("轻型自卸全挂车", "G35"),
    G36("轻型仓栅式全挂车", "G36"),
    G37("轻型旅居全挂车", "G37"),
    G38("轻型专项作业全挂车", "G38"),
    G382("轻型特殊用途全挂车", "G38"),
    B11("重型普通半挂车", "B11"),
    B112("重型栏板半挂车", "B11"),
    B12("重型厢式半挂车", "B12"),
    B13("重型罐式半挂车", "B13"),
    B14("重型平板半挂车", "B14"),
    B15("重型集装箱半挂车", "B15"),
    B16("重型自卸半挂车", "B16"),
    B17("重型特殊结构半挂车", "B17"),
    B18("重型仓栅式半挂车", "B18"),
    B19("重型旅居半挂车", "B19"),
    B1A("重型专项作业半挂车", "B1A"),
    B1B("重型低平板半挂车", "B1B"),
    B21("中型普通半挂车", "B21"),
    B212("中型栏板半挂车", "B21"),
    B22("中型厢式半挂车", "B22"),
    B23("中型罐式半挂车", "B23"),
    B24("中型平板半挂车", "B24"),
    B25("中型集装箱半挂车", "B25"),
    B26("中型自卸半挂车", "B26"),
    B27("中型特殊结构半挂车", "B27"),
    B28("中型仓栅式半挂车", "B28"),
    B29("中型旅居半挂车", "B29"),
    B2A("中型专项作业半挂车", "B2A"),
    B2B("中型低平板半挂车", "B2B"),
    B31("轻型普通半挂车", "B31"),
    B312("轻型栏板半挂车", "B31"),
    B32("轻型厢式半挂车", "B32"),
    B33("轻型罐式半挂车", "B33"),
    B34("轻型平板半挂车", "B34"),
    B35("轻型自卸半挂车", "B35"),
    B36("轻型仓栅式半挂车", "B36"),
    B37("轻型旅居半挂车", "B37"),
    B38("轻型专项作业半挂车", "B38"),
    B39("轻型低平板半挂车", "B39"),
    X99("其他", "X99");

    private final String code;
    private final String desc;

    private VehicleTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static VehicleTypeEnum valueOfCode(String code) {
        for (VehicleTypeEnum item : VehicleTypeEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

