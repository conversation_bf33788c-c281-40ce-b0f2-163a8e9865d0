
package com.ecommerce.logistics.api.enums;

public enum VehicleUserTypeEnum {
    BUYER(1, "买家"),
    SELLER(2, "卖家"),
    CARRIER(3, "承运商");

    private final Integer code;
    private final String desc;

    private VehicleUserTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static VehicleUserTypeEnum valueOfCode(Integer code) {
        for (VehicleUserTypeEnum item : VehicleUserTypeEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

