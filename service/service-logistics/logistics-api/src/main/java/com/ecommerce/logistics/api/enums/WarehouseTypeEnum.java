
package com.ecommerce.logistics.api.enums;

public enum WarehouseTypeEnum {
    CENTRAL_WAREHOUSE("030140100", "中心仓"),
    STORE("030140200", "门店"),
    TRANSFER_WAREHOUSE("030140300", "中转库"),
    FACTORY_BASE("030140400", "厂家基地");

    private final String code;
    private final String desc;

    private WarehouseTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static WarehouseTypeEnum valueOfCode(String code) {
        for (WarehouseTypeEnum item : WarehouseTypeEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

