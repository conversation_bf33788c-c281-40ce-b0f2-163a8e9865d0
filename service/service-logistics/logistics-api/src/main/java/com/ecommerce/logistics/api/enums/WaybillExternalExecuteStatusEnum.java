
package com.ecommerce.logistics.api.enums;

public enum WaybillExternalExecuteStatusEnum {
    OPEN_BILL("00", "已开单"),
    ENTERED("01", "已进站"),
    PASS_TARE("02", "已过皮重"),
    PASS_ROUGH("03", "已过毛重"),
    LEAVE_WAREHOUSE("04", "已出站"),
    SIGNED("05", "已签收");

    private final String code;
    private final String desc;

    private WaybillExternalExecuteStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static WaybillExternalExecuteStatusEnum valueOfCode(String code) {
        for (WaybillExternalExecuteStatusEnum item :  WaybillExternalExecuteStatusEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

