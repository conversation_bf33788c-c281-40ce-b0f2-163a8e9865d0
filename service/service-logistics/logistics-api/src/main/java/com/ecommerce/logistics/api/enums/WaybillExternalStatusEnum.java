
package com.ecommerce.logistics.api.enums;

public enum WaybillExternalStatusEnum {
    ERP_CREATING("030280100", "创建中"),
    ERP_CREATE_SUCCESS("030280200", "创建成功"),
    ERP_CREATE_FAIL("030280210", "创建失败"),
    ERP_CHANGING("030280300", "修改中"),
    ERP_CHANGE_SUCCESS("030280400", "修改成功"),
    ERP_CLOSING("030280500", "作废中"),
    ERP_CLOSE_SUCCESS("030280600", "作废成功"),
    PASS_TARE("030280700", "已过皮重"),
    ENTER_WAREHOUSE("030280800", "已进站"),
    LEAVE_WAREHOUSE("030280900", "已出站"),
    COMPLETE_SETTLEMENT("030281000", "已结算"),
    SETTLEMENT_ING("030281100", "结算中"),
    ERP_SHIPPING("030281200", "发货中"),
    PASS_ROUGH("030281300", "已过毛重"),
    MATCH_PLAN("030281400", "已匹配计划"),
    ERP_DISPATCH("030281500", "已调度"),
    START_SHIPMENT("030281600", "开始装船"),
    COMPLETE_SHIPMENT("030281700", "完成装船"),
    OPEN_CABIN_ING("030281800", "开仓中"),
    OPEN_CABIN_FAIL("030281900", "开仓失败"),
    OPEN_CABIN_SUCCESS("030282000", "开仓成功"),
    ERP_PROCESSING("030282100", "ERP处理中"),
    REFUND_COMPLETED("030282200", "退货完成"),
    ERP_REFUND_FAIL("030282300", "ERP退货失败"),
    TRACE_REFUND_FAIL("030282400", "交易退货失败");

    private final String code;
    private final String desc;

    private WaybillExternalStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static WaybillExternalStatusEnum valueOfCode(String code) {
        for (WaybillExternalStatusEnum item : WaybillExternalStatusEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

