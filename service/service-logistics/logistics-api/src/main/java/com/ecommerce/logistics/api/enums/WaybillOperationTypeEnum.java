
package com.ecommerce.logistics.api.enums;

public enum WaybillOperationTypeEnum {
    WAYBILL_NEW(1, "运单新建"),
    WAYBILL_MERGE(2, "运单整合"),
    WAYBILL_AUDIT(3, "运单审核"),
    WAYBILL_SNATCH(4, "运单抢单"),
    WAYBILL_ASSIGN(5, "运单指派"),
    WAYBILL_CONFIRM(6, "运单确认"),
    ASSIGN_CARRIER(7, "指派承运商"),
    ASSIGN_DRIVER(8, "指派司机"),
    WAYBILL_REPUBLISH(9, "重新发布"),
    VEHICLE_ENTER(10, "车辆进站"),
    VEHICLE_LEAVE(11, "车辆出站"),
    ARRIVE_DESTINATION(12, "到达目的地"),
    BEGIN_CARRY(13, "开始搬运"),
    WAYBILL_COMPLETE(14, "运单完成"),
    WAYBILL_CANCEL(15, "运单取消"),
    WAYBILL_CLOSED(16, "运单关闭"),
    PASS_TARE(17, "已过皮重"),
    PASS_ROUGH(18, "已过毛重"),
    OPEN_BILL(19, "已开单"),
    WAYBILL_SIGN(20, "运单签收"),
    ENTER_FACTORY(21, "进厂"),
    WAYBILL_DISCARD(22, "作废"),
    MATCH_PLAN(23, "已匹配计划"),
    ERP_DISPATCH(24, "已调度"),
    START_SHIPMENT(25, "开始装船"),
    COMPLETE_SHIPMENT(26, "完成装船"),
    WAYBILL_REJECT(27, "运单拒绝"),
    OPEN_CABIN(28, "开仓"),
    REROUTE(29, "改航"),
    WAYBILL_REFUND(30, "运单退货"),
    WAYBILL_MODIFY(31, "运单修改");

    private final Integer code;
    private final String desc;

    private WaybillOperationTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static WaybillOperationTypeEnum valueOfCode(Integer code) {
        for (WaybillOperationTypeEnum item : WaybillOperationTypeEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

