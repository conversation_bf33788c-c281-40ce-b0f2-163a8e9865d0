
package com.ecommerce.logistics.api.enums;

public enum WaybillStatusEnum {
    WAIT_MERGER("030070100", "待整合"),
    WAIT_AUDIT("030070200", "待审核"),
    WAIT_PUBLISH("030070300", "待发布"),
    WAIT_RECEIVE("030070400", "待接单"),
    WAIT_DELIVERY("030070500", "待配送"),
    DELIVERING("030070600", "配送中"),
    COMPLETED("030070700", "已完成"),
    CANCELED("030070800", "已取消"),
    DISCARD("030070810", "已作废"),
    CLOSED("030070900", "已关闭"),
    TIMEOUT("030071000", "已超时"),
    WAIT_ASSIGN("030071100", "待指派"),
    WAIT_CONFIRM("030071200", "待确认"),
    SIGNING("030071300", "签收中");

    private final String code;
    private final String desc;

    private WaybillStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static WaybillStatusEnum valueOfCode(String code) {
        for (WaybillStatusEnum item : WaybillStatusEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

