
package com.ecommerce.logistics.api.enums;

public enum WaybillTypeEnum {
    SOCIETY_SNATCH("030080100", "社会运力抢单"),
    PLATFORM_ASSIGNING("030080110", "平台指派"),
    CARRIER_ASSIGNING("030080200", "承运商指派"),
    BUYER_PICK("030080300", "买家自提"),
    SELLER_DELIVERY("030080400", "卖家配送"),
    STORE_DELIVERY("030080500", "门店配送");

    private final String code;
    private final String desc;

    private WaybillTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static WaybillTypeEnum valueOfCode(String code) {
        for (WaybillTypeEnum item : WaybillTypeEnum.values()) {
            if (!item.code.equals(code)) continue;
            return item;
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}

