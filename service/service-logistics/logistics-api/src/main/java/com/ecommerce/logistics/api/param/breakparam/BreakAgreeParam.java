
package com.ecommerce.logistics.api.param.breakparam;

import io.swagger.v3.oas.annotations.media.Schema;

import jakarta.validation.constraints.NotBlank;
import java.math.BigDecimal;

import jakarta.validation.constraints.NotBlank;
import java.util.Date;

@Schema(name="同意违约记录入参")
public class BreakAgreeParam {
    @Schema(description="违约ID")
    @NotBlank(message="违约ID不能为空！")
    private String breakId;
    @Schema(description="违约金金额")
    private BigDecimal breakAmount;
    @Schema(description="备注")
    private String dealResult;
    @Schema(description="是否有违约金【-1:否 1:是】")
    private Integer breakFlag;
    @Schema(description="违约数量")
    private String breakQuantity;
    private String dealFinishUserName;
    private String dealFinishUserId;
    private Date dealFinishTime;
    String operateUserId;

    public String getBreakId() {
        return this.breakId;
    }

    public BigDecimal getBreakAmount() {
        return this.breakAmount;
    }

    public String getDealResult() {
        return this.dealResult;
    }

    public Integer getBreakFlag() {
        return this.breakFlag;
    }

    public String getBreakQuantity() {
        return this.breakQuantity;
    }

    public String getDealFinishUserName() {
        return this.dealFinishUserName;
    }

    public String getDealFinishUserId() {
        return this.dealFinishUserId;
    }

    public Date getDealFinishTime() {
        return this.dealFinishTime;
    }

    public String getOperateUserId() {
        return this.operateUserId;
    }

    public void setBreakId(String breakId) {
        this.breakId = breakId;
    }

    public void setBreakAmount(BigDecimal breakAmount) {
        this.breakAmount = breakAmount;
    }

    public void setDealResult(String dealResult) {
        this.dealResult = dealResult;
    }

    public void setBreakFlag(Integer breakFlag) {
        this.breakFlag = breakFlag;
    }

    public void setBreakQuantity(String breakQuantity) {
        this.breakQuantity = breakQuantity;
    }

    public void setDealFinishUserName(String dealFinishUserName) {
        this.dealFinishUserName = dealFinishUserName;
    }

    public void setDealFinishUserId(String dealFinishUserId) {
        this.dealFinishUserId = dealFinishUserId;
    }

    public void setDealFinishTime(Date dealFinishTime) {
        this.dealFinishTime = dealFinishTime;
    }

    public void setOperateUserId(String operateUserId) {
        this.operateUserId = operateUserId;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof BreakAgreeParam)) {
            return false;
        }
        BreakAgreeParam other = (BreakAgreeParam)o;
        if (!other.canEqual(this)) {
            return false;
        }
        String this$breakId = this.getBreakId();
        String other$breakId = other.getBreakId();
        if (this$breakId == null ? other$breakId != null : !this$breakId.equals(other$breakId)) {
            return false;
        }
        BigDecimal this$breakAmount = this.getBreakAmount();
        BigDecimal other$breakAmount = other.getBreakAmount();
        if (this$breakAmount == null ? other$breakAmount != null : !((Object)this$breakAmount).equals(other$breakAmount)) {
            return false;
        }
        String this$dealResult = this.getDealResult();
        String other$dealResult = other.getDealResult();
        if (this$dealResult == null ? other$dealResult != null : !this$dealResult.equals(other$dealResult)) {
            return false;
        }
        Integer this$breakFlag = this.getBreakFlag();
        Integer other$breakFlag = other.getBreakFlag();
        if (this$breakFlag == null ? other$breakFlag != null : !((Object)this$breakFlag).equals(other$breakFlag)) {
            return false;
        }
        String this$breakQuantity = this.getBreakQuantity();
        String other$breakQuantity = other.getBreakQuantity();
        if (this$breakQuantity == null ? other$breakQuantity != null : !this$breakQuantity.equals(other$breakQuantity)) {
            return false;
        }
        String this$dealFinishUserName = this.getDealFinishUserName();
        String other$dealFinishUserName = other.getDealFinishUserName();
        if (this$dealFinishUserName == null ? other$dealFinishUserName != null : !this$dealFinishUserName.equals(other$dealFinishUserName)) {
            return false;
        }
        String this$dealFinishUserId = this.getDealFinishUserId();
        String other$dealFinishUserId = other.getDealFinishUserId();
        if (this$dealFinishUserId == null ? other$dealFinishUserId != null : !this$dealFinishUserId.equals(other$dealFinishUserId)) {
            return false;
        }
        Date this$dealFinishTime = this.getDealFinishTime();
        Date other$dealFinishTime = other.getDealFinishTime();
        if (this$dealFinishTime == null ? other$dealFinishTime != null : !((Object)this$dealFinishTime).equals(other$dealFinishTime)) {
            return false;
        }
        String this$operateUserId = this.getOperateUserId();
        String other$operateUserId = other.getOperateUserId();
        return !(this$operateUserId == null ? other$operateUserId != null : !this$operateUserId.equals(other$operateUserId));
    }

    protected boolean canEqual(Object other) {
        return other instanceof BreakAgreeParam;
    }

    public int hashCode() {
        int result = 1;
        String $breakId = this.getBreakId();
        result = result * 59 + ($breakId == null ? 43 : $breakId.hashCode());
        BigDecimal $breakAmount = this.getBreakAmount();
        result = result * 59 + ($breakAmount == null ? 43 : ((Object)$breakAmount).hashCode());
        String $dealResult = this.getDealResult();
        result = result * 59 + ($dealResult == null ? 43 : $dealResult.hashCode());
        Integer $breakFlag = this.getBreakFlag();
        result = result * 59 + ($breakFlag == null ? 43 : ((Object)$breakFlag).hashCode());
        String $breakQuantity = this.getBreakQuantity();
        result = result * 59 + ($breakQuantity == null ? 43 : $breakQuantity.hashCode());
        String $dealFinishUserName = this.getDealFinishUserName();
        result = result * 59 + ($dealFinishUserName == null ? 43 : $dealFinishUserName.hashCode());
        String $dealFinishUserId = this.getDealFinishUserId();
        result = result * 59 + ($dealFinishUserId == null ? 43 : $dealFinishUserId.hashCode());
        Date $dealFinishTime = this.getDealFinishTime();
        result = result * 59 + ($dealFinishTime == null ? 43 : ((Object)$dealFinishTime).hashCode());
        String $operateUserId = this.getOperateUserId();
        result = result * 59 + ($operateUserId == null ? 43 : $operateUserId.hashCode());
        return result;
    }

    public String toString() {
        return "BreakAgreeParam(breakId=" + this.getBreakId() + ", breakAmount=" + this.getBreakAmount() + ", dealResult=" + this.getDealResult() + ", breakFlag=" + this.getBreakFlag() + ", breakQuantity=" + this.getBreakQuantity() + ", dealFinishUserName=" + this.getDealFinishUserName() + ", dealFinishUserId=" + this.getDealFinishUserId() + ", dealFinishTime=" + this.getDealFinishTime() + ", operateUserId=" + this.getOperateUserId() + ")";
    }
}

