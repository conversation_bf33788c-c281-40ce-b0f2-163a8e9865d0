
package com.ecommerce.logistics.api.param.breakparam;

import io.swagger.v3.oas.annotations.media.Schema;

import jakarta.validation.constraints.NotEmpty;
import java.util.List;

@Schema(name="批量取消违约记录入参")
public class BreakBatchCancelParam {
    @Schema(description="违约ID列表")
    @NotEmpty(message="违违约ID列表不能为空！")
    private List<String> breakIdList;
    String operateUserId;

    public List<String> getBreakIdList() {
        return this.breakIdList;
    }

    public String getOperateUserId() {
        return this.operateUserId;
    }

    public void setBreakIdList(List<String> breakIdList) {
        this.breakIdList = breakIdList;
    }

    public void setOperateUserId(String operateUserId) {
        this.operateUserId = operateUserId;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof BreakBatchCancelParam)) {
            return false;
        }
        BreakBatchCancelParam other = (BreakBatchCancelParam)o;
        if (!other.canEqual(this)) {
            return false;
        }
        List<String> this$breakIdList = this.getBreakIdList();
        List<String> other$breakIdList = other.getBreakIdList();
        if (this$breakIdList == null ? other$breakIdList != null : !((Object)this$breakIdList).equals(other$breakIdList)) {
            return false;
        }
        String this$operateUserId = this.getOperateUserId();
        String other$operateUserId = other.getOperateUserId();
        return !(this$operateUserId == null ? other$operateUserId != null : !this$operateUserId.equals(other$operateUserId));
    }

    protected boolean canEqual(Object other) {
        return other instanceof BreakBatchCancelParam;
    }

    public int hashCode() {
        int result = 1;
        List<String> $breakIdList = this.getBreakIdList();
        result = result * 59 + ($breakIdList == null ? 43 : ((Object)$breakIdList).hashCode());
        String $operateUserId = this.getOperateUserId();
        result = result * 59 + ($operateUserId == null ? 43 : $operateUserId.hashCode());
        return result;
    }

    public String toString() {
        return "BreakBatchCancelParam(breakIdList=" + this.getBreakIdList() + ", operateUserId=" + this.getOperateUserId() + ")";
    }
}

