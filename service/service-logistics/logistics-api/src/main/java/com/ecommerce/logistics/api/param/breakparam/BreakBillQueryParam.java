
package com.ecommerce.logistics.api.param.breakparam;

import io.swagger.v3.oas.annotations.media.Schema;

import jakarta.validation.constraints.NotBlank;

@Schema(name="违约关联单据入参")
public class BreakBillQueryParam {
    @Schema(description="关联单据号")
    @NotBlank(message="关联单据号不能为空！")
    private String relationBillNum;

    public String getRelationBillNum() {
        return this.relationBillNum;
    }

    public void setRelationBillNum(String relationBillNum) {
        this.relationBillNum = relationBillNum;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof BreakBillQueryParam)) {
            return false;
        }
        BreakBillQueryParam other = (BreakBillQueryParam)o;
        if (!other.canEqual(this)) {
            return false;
        }
        String this$relationBillNum = this.getRelationBillNum();
        String other$relationBillNum = other.getRelationBillNum();
        return !(this$relationBillNum == null ? other$relationBillNum != null : !this$relationBillNum.equals(other$relationBillNum));
    }

    protected boolean canEqual(Object other) {
        return other instanceof BreakBillQueryParam;
    }

    public int hashCode() {
        int result = 1;
        String $relationBillNum = this.getRelationBillNum();
        result = result * 59 + ($relationBillNum == null ? 43 : $relationBillNum.hashCode());
        return result;
    }

    public String toString() {
        return "BreakBillQueryParam(relationBillNum=" + this.getRelationBillNum() + ")";
    }
}

