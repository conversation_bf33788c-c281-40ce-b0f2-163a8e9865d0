
package com.ecommerce.logistics.api.param.breakparam;

import io.swagger.v3.oas.annotations.media.Schema;

import jakarta.validation.constraints.NotBlank;
import java.util.Date;

import jakarta.validation.constraints.NotBlank;

@Schema(name="违约记录跟进处理入参")
public class BreakFollowParam {
    @Schema(description="违约ID")
    @NotBlank(message="违约ID不能为空！")
    private String breakId;
    @Schema(description="操作内容")
    private String content;
    @Schema(description="操作用户Id")
    private String operationUserId;
    @Schema(description="操作用户名称")
    private String operationUserName;
    @Schema(description="操作时间")
    private Date operationTime;

    public String getBreakId() {
        return this.breakId;
    }

    public String getContent() {
        return this.content;
    }

    public String getOperationUserId() {
        return this.operationUserId;
    }

    public String getOperationUserName() {
        return this.operationUserName;
    }

    public Date getOperationTime() {
        return this.operationTime;
    }

    public void setBreakId(String breakId) {
        this.breakId = breakId;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public void setOperationUserId(String operationUserId) {
        this.operationUserId = operationUserId;
    }

    public void setOperationUserName(String operationUserName) {
        this.operationUserName = operationUserName;
    }

    public void setOperationTime(Date operationTime) {
        this.operationTime = operationTime;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof BreakFollowParam)) {
            return false;
        }
        BreakFollowParam other = (BreakFollowParam)o;
        if (!other.canEqual(this)) {
            return false;
        }
        String this$breakId = this.getBreakId();
        String other$breakId = other.getBreakId();
        if (this$breakId == null ? other$breakId != null : !this$breakId.equals(other$breakId)) {
            return false;
        }
        String this$content = this.getContent();
        String other$content = other.getContent();
        if (this$content == null ? other$content != null : !this$content.equals(other$content)) {
            return false;
        }
        String this$operationUserId = this.getOperationUserId();
        String other$operationUserId = other.getOperationUserId();
        if (this$operationUserId == null ? other$operationUserId != null : !this$operationUserId.equals(other$operationUserId)) {
            return false;
        }
        String this$operationUserName = this.getOperationUserName();
        String other$operationUserName = other.getOperationUserName();
        if (this$operationUserName == null ? other$operationUserName != null : !this$operationUserName.equals(other$operationUserName)) {
            return false;
        }
        Date this$operationTime = this.getOperationTime();
        Date other$operationTime = other.getOperationTime();
        return !(this$operationTime == null ? other$operationTime != null : !((Object)this$operationTime).equals(other$operationTime));
    }

    protected boolean canEqual(Object other) {
        return other instanceof BreakFollowParam;
    }

    public int hashCode() {
        int result = 1;
        String $breakId = this.getBreakId();
        result = result * 59 + ($breakId == null ? 43 : $breakId.hashCode());
        String $content = this.getContent();
        result = result * 59 + ($content == null ? 43 : $content.hashCode());
        String $operationUserId = this.getOperationUserId();
        result = result * 59 + ($operationUserId == null ? 43 : $operationUserId.hashCode());
        String $operationUserName = this.getOperationUserName();
        result = result * 59 + ($operationUserName == null ? 43 : $operationUserName.hashCode());
        Date $operationTime = this.getOperationTime();
        result = result * 59 + ($operationTime == null ? 43 : ((Object)$operationTime).hashCode());
        return result;
    }

    public String toString() {
        return "BreakFollowParam(breakId=" + this.getBreakId() + ", content=" + this.getContent() + ", operationUserId=" + this.getOperationUserId() + ", operationUserName=" + this.getOperationUserName() + ", operationTime=" + this.getOperationTime() + ")";
    }
}

