
package com.ecommerce.logistics.api.param.breakparam;

import com.ecommerce.logistics.api.dto.attachment.AttAddDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;

import java.util.List;

@Schema(name="违约记录入参")
public class BreakParam {
    @Schema(description="违约ID")
    private String breakId;
    @Schema(description="违约编号")
    private String breakNum;
    @Schema(description="关联单据ID")
    private String relationBillId;
    @Schema(description="关联单据号")
    @NotBlank(message="关联单据号不能为空")
    private String relationBillNum;
    @Schema(description="关联单据类型")
    private String relationBillType;
    @Schema(description="违约发起方类型枚举")
    private String breakInitRoleType;
    @Schema(description="违约发起方名称")
    @NotBlank(message="违约发起方名称不能为空")
    private String breakInitRoleName;
    @Schema(description="违约发起方ID")
    @NotBlank(message="违约发起方角色不能为空")
    private String breakInitRoleId;
    @Schema(description="违约说明")
    private String breakContent;
    @Schema(description="配送数量")
    private String deliveryQuantity;
    @Schema(description="违约数量")
    @NotBlank(message="违约数量不能为空")
    private String breakQuantity;
    @Schema(description="凭证附件对象")
    private List<AttAddDTO> attListAddDTO;
    String operateUserId;

    public String getBreakId() {
        return this.breakId;
    }

    public String getBreakNum() {
        return this.breakNum;
    }

    public String getRelationBillId() {
        return this.relationBillId;
    }

    public String getRelationBillNum() {
        return this.relationBillNum;
    }

    public String getRelationBillType() {
        return this.relationBillType;
    }

    public String getBreakInitRoleType() {
        return this.breakInitRoleType;
    }

    public String getBreakInitRoleName() {
        return this.breakInitRoleName;
    }

    public String getBreakInitRoleId() {
        return this.breakInitRoleId;
    }

    public String getBreakContent() {
        return this.breakContent;
    }

    public String getDeliveryQuantity() {
        return this.deliveryQuantity;
    }

    public String getBreakQuantity() {
        return this.breakQuantity;
    }

    public List<AttAddDTO> getAttListAddDTO() {
        return this.attListAddDTO;
    }

    public String getOperateUserId() {
        return this.operateUserId;
    }

    public void setBreakId(String breakId) {
        this.breakId = breakId;
    }

    public void setBreakNum(String breakNum) {
        this.breakNum = breakNum;
    }

    public void setRelationBillId(String relationBillId) {
        this.relationBillId = relationBillId;
    }

    public void setRelationBillNum(String relationBillNum) {
        this.relationBillNum = relationBillNum;
    }

    public void setRelationBillType(String relationBillType) {
        this.relationBillType = relationBillType;
    }

    public void setBreakInitRoleType(String breakInitRoleType) {
        this.breakInitRoleType = breakInitRoleType;
    }

    public void setBreakInitRoleName(String breakInitRoleName) {
        this.breakInitRoleName = breakInitRoleName;
    }

    public void setBreakInitRoleId(String breakInitRoleId) {
        this.breakInitRoleId = breakInitRoleId;
    }

    public void setBreakContent(String breakContent) {
        this.breakContent = breakContent;
    }

    public void setDeliveryQuantity(String deliveryQuantity) {
        this.deliveryQuantity = deliveryQuantity;
    }

    public void setBreakQuantity(String breakQuantity) {
        this.breakQuantity = breakQuantity;
    }

    public void setAttListAddDTO(List<AttAddDTO> attListAddDTO) {
        this.attListAddDTO = attListAddDTO;
    }

    public void setOperateUserId(String operateUserId) {
        this.operateUserId = operateUserId;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof BreakParam)) {
            return false;
        }
        BreakParam other = (BreakParam)o;
        if (!other.canEqual(this)) {
            return false;
        }
        String this$breakId = this.getBreakId();
        String other$breakId = other.getBreakId();
        if (this$breakId == null ? other$breakId != null : !this$breakId.equals(other$breakId)) {
            return false;
        }
        String this$breakNum = this.getBreakNum();
        String other$breakNum = other.getBreakNum();
        if (this$breakNum == null ? other$breakNum != null : !this$breakNum.equals(other$breakNum)) {
            return false;
        }
        String this$relationBillId = this.getRelationBillId();
        String other$relationBillId = other.getRelationBillId();
        if (this$relationBillId == null ? other$relationBillId != null : !this$relationBillId.equals(other$relationBillId)) {
            return false;
        }
        String this$relationBillNum = this.getRelationBillNum();
        String other$relationBillNum = other.getRelationBillNum();
        if (this$relationBillNum == null ? other$relationBillNum != null : !this$relationBillNum.equals(other$relationBillNum)) {
            return false;
        }
        String this$relationBillType = this.getRelationBillType();
        String other$relationBillType = other.getRelationBillType();
        if (this$relationBillType == null ? other$relationBillType != null : !this$relationBillType.equals(other$relationBillType)) {
            return false;
        }
        String this$breakInitRoleType = this.getBreakInitRoleType();
        String other$breakInitRoleType = other.getBreakInitRoleType();
        if (this$breakInitRoleType == null ? other$breakInitRoleType != null : !this$breakInitRoleType.equals(other$breakInitRoleType)) {
            return false;
        }
        String this$breakInitRoleName = this.getBreakInitRoleName();
        String other$breakInitRoleName = other.getBreakInitRoleName();
        if (this$breakInitRoleName == null ? other$breakInitRoleName != null : !this$breakInitRoleName.equals(other$breakInitRoleName)) {
            return false;
        }
        String this$breakInitRoleId = this.getBreakInitRoleId();
        String other$breakInitRoleId = other.getBreakInitRoleId();
        if (this$breakInitRoleId == null ? other$breakInitRoleId != null : !this$breakInitRoleId.equals(other$breakInitRoleId)) {
            return false;
        }
        String this$breakContent = this.getBreakContent();
        String other$breakContent = other.getBreakContent();
        if (this$breakContent == null ? other$breakContent != null : !this$breakContent.equals(other$breakContent)) {
            return false;
        }
        String this$deliveryQuantity = this.getDeliveryQuantity();
        String other$deliveryQuantity = other.getDeliveryQuantity();
        if (this$deliveryQuantity == null ? other$deliveryQuantity != null : !this$deliveryQuantity.equals(other$deliveryQuantity)) {
            return false;
        }
        String this$breakQuantity = this.getBreakQuantity();
        String other$breakQuantity = other.getBreakQuantity();
        if (this$breakQuantity == null ? other$breakQuantity != null : !this$breakQuantity.equals(other$breakQuantity)) {
            return false;
        }
        List<AttAddDTO> this$attListAddDTO = this.getAttListAddDTO();
        List<AttAddDTO> other$attListAddDTO = other.getAttListAddDTO();
        if (this$attListAddDTO == null ? other$attListAddDTO != null : !((Object)this$attListAddDTO).equals(other$attListAddDTO)) {
            return false;
        }
        String this$operateUserId = this.getOperateUserId();
        String other$operateUserId = other.getOperateUserId();
        return !(this$operateUserId == null ? other$operateUserId != null : !this$operateUserId.equals(other$operateUserId));
    }

    protected boolean canEqual(Object other) {
        return other instanceof BreakParam;
    }

    public int hashCode() {
        int result = 1;
        String $breakId = this.getBreakId();
        result = result * 59 + ($breakId == null ? 43 : $breakId.hashCode());
        String $breakNum = this.getBreakNum();
        result = result * 59 + ($breakNum == null ? 43 : $breakNum.hashCode());
        String $relationBillId = this.getRelationBillId();
        result = result * 59 + ($relationBillId == null ? 43 : $relationBillId.hashCode());
        String $relationBillNum = this.getRelationBillNum();
        result = result * 59 + ($relationBillNum == null ? 43 : $relationBillNum.hashCode());
        String $relationBillType = this.getRelationBillType();
        result = result * 59 + ($relationBillType == null ? 43 : $relationBillType.hashCode());
        String $breakInitRoleType = this.getBreakInitRoleType();
        result = result * 59 + ($breakInitRoleType == null ? 43 : $breakInitRoleType.hashCode());
        String $breakInitRoleName = this.getBreakInitRoleName();
        result = result * 59 + ($breakInitRoleName == null ? 43 : $breakInitRoleName.hashCode());
        String $breakInitRoleId = this.getBreakInitRoleId();
        result = result * 59 + ($breakInitRoleId == null ? 43 : $breakInitRoleId.hashCode());
        String $breakContent = this.getBreakContent();
        result = result * 59 + ($breakContent == null ? 43 : $breakContent.hashCode());
        String $deliveryQuantity = this.getDeliveryQuantity();
        result = result * 59 + ($deliveryQuantity == null ? 43 : $deliveryQuantity.hashCode());
        String $breakQuantity = this.getBreakQuantity();
        result = result * 59 + ($breakQuantity == null ? 43 : $breakQuantity.hashCode());
        List<AttAddDTO> $attListAddDTO = this.getAttListAddDTO();
        result = result * 59 + ($attListAddDTO == null ? 43 : ((Object)$attListAddDTO).hashCode());
        String $operateUserId = this.getOperateUserId();
        result = result * 59 + ($operateUserId == null ? 43 : $operateUserId.hashCode());
        return result;
    }

    public String toString() {
        return "BreakParam(breakId=" + this.getBreakId() + ", breakNum=" + this.getBreakNum() + ", relationBillId=" + this.getRelationBillId() + ", relationBillNum=" + this.getRelationBillNum() + ", relationBillType=" + this.getRelationBillType() + ", breakInitRoleType=" + this.getBreakInitRoleType() + ", breakInitRoleName=" + this.getBreakInitRoleName() + ", breakInitRoleId=" + this.getBreakInitRoleId() + ", breakContent=" + this.getBreakContent() + ", deliveryQuantity=" + this.getDeliveryQuantity() + ", breakQuantity=" + this.getBreakQuantity() + ", attListAddDTO=" + this.getAttListAddDTO() + ", operateUserId=" + this.getOperateUserId() + ")";
    }
}

