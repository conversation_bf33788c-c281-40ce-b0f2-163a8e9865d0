
package com.ecommerce.logistics.api.param.breakparam;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

@Schema(name="违约记录分页查询条件")
public class BreakQueryParam {
    @Schema(description="违约编号")
    private String breakNum;
    @Schema(description="关联单据号")
    private String relationBillNum;
    @Schema(description="处理状态")
    private String dealStatus;
    @Schema(description="违约发起方类型枚举")
    private String breakInitRoleType;
    @Schema(description="违约发起方名称")
    private String breakInitRoleName;
    @Schema(description="违约发起方ID")
    private String breakInitRoleId;
    @Schema(description="提出时间【创建时间】-开始")
    private Date proposeTimeStart;
    @Schema(description="提出时间【创建时间】-结束")
    private Date proposeTimeEnd;

    public String getBreakNum() {
        return this.breakNum;
    }

    public String getRelationBillNum() {
        return this.relationBillNum;
    }

    public String getDealStatus() {
        return this.dealStatus;
    }

    public String getBreakInitRoleType() {
        return this.breakInitRoleType;
    }

    public String getBreakInitRoleName() {
        return this.breakInitRoleName;
    }

    public String getBreakInitRoleId() {
        return this.breakInitRoleId;
    }

    public Date getProposeTimeStart() {
        return this.proposeTimeStart;
    }

    public Date getProposeTimeEnd() {
        return this.proposeTimeEnd;
    }

    public void setBreakNum(String breakNum) {
        this.breakNum = breakNum;
    }

    public void setRelationBillNum(String relationBillNum) {
        this.relationBillNum = relationBillNum;
    }

    public void setDealStatus(String dealStatus) {
        this.dealStatus = dealStatus;
    }

    public void setBreakInitRoleType(String breakInitRoleType) {
        this.breakInitRoleType = breakInitRoleType;
    }

    public void setBreakInitRoleName(String breakInitRoleName) {
        this.breakInitRoleName = breakInitRoleName;
    }

    public void setBreakInitRoleId(String breakInitRoleId) {
        this.breakInitRoleId = breakInitRoleId;
    }

    public void setProposeTimeStart(Date proposeTimeStart) {
        this.proposeTimeStart = proposeTimeStart;
    }

    public void setProposeTimeEnd(Date proposeTimeEnd) {
        this.proposeTimeEnd = proposeTimeEnd;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof BreakQueryParam)) {
            return false;
        }
        BreakQueryParam other = (BreakQueryParam)o;
        if (!other.canEqual(this)) {
            return false;
        }
        String this$breakNum = this.getBreakNum();
        String other$breakNum = other.getBreakNum();
        if (this$breakNum == null ? other$breakNum != null : !this$breakNum.equals(other$breakNum)) {
            return false;
        }
        String this$relationBillNum = this.getRelationBillNum();
        String other$relationBillNum = other.getRelationBillNum();
        if (this$relationBillNum == null ? other$relationBillNum != null : !this$relationBillNum.equals(other$relationBillNum)) {
            return false;
        }
        String this$dealStatus = this.getDealStatus();
        String other$dealStatus = other.getDealStatus();
        if (this$dealStatus == null ? other$dealStatus != null : !this$dealStatus.equals(other$dealStatus)) {
            return false;
        }
        String this$breakInitRoleType = this.getBreakInitRoleType();
        String other$breakInitRoleType = other.getBreakInitRoleType();
        if (this$breakInitRoleType == null ? other$breakInitRoleType != null : !this$breakInitRoleType.equals(other$breakInitRoleType)) {
            return false;
        }
        String this$breakInitRoleName = this.getBreakInitRoleName();
        String other$breakInitRoleName = other.getBreakInitRoleName();
        if (this$breakInitRoleName == null ? other$breakInitRoleName != null : !this$breakInitRoleName.equals(other$breakInitRoleName)) {
            return false;
        }
        String this$breakInitRoleId = this.getBreakInitRoleId();
        String other$breakInitRoleId = other.getBreakInitRoleId();
        if (this$breakInitRoleId == null ? other$breakInitRoleId != null : !this$breakInitRoleId.equals(other$breakInitRoleId)) {
            return false;
        }
        Date this$proposeTimeStart = this.getProposeTimeStart();
        Date other$proposeTimeStart = other.getProposeTimeStart();
        if (this$proposeTimeStart == null ? other$proposeTimeStart != null : !((Object)this$proposeTimeStart).equals(other$proposeTimeStart)) {
            return false;
        }
        Date this$proposeTimeEnd = this.getProposeTimeEnd();
        Date other$proposeTimeEnd = other.getProposeTimeEnd();
        return !(this$proposeTimeEnd == null ? other$proposeTimeEnd != null : !((Object)this$proposeTimeEnd).equals(other$proposeTimeEnd));
    }

    protected boolean canEqual(Object other) {
        return other instanceof BreakQueryParam;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        String $breakNum = this.getBreakNum();
        result = result * 59 + ($breakNum == null ? 43 : $breakNum.hashCode());
        String $relationBillNum = this.getRelationBillNum();
        result = result * 59 + ($relationBillNum == null ? 43 : $relationBillNum.hashCode());
        String $dealStatus = this.getDealStatus();
        result = result * 59 + ($dealStatus == null ? 43 : $dealStatus.hashCode());
        String $breakInitRoleType = this.getBreakInitRoleType();
        result = result * 59 + ($breakInitRoleType == null ? 43 : $breakInitRoleType.hashCode());
        String $breakInitRoleName = this.getBreakInitRoleName();
        result = result * 59 + ($breakInitRoleName == null ? 43 : $breakInitRoleName.hashCode());
        String $breakInitRoleId = this.getBreakInitRoleId();
        result = result * 59 + ($breakInitRoleId == null ? 43 : $breakInitRoleId.hashCode());
        Date $proposeTimeStart = this.getProposeTimeStart();
        result = result * 59 + ($proposeTimeStart == null ? 43 : ((Object)$proposeTimeStart).hashCode());
        Date $proposeTimeEnd = this.getProposeTimeEnd();
        result = result * 59 + ($proposeTimeEnd == null ? 43 : ((Object)$proposeTimeEnd).hashCode());
        return result;
    }

    public String toString() {
        return "BreakQueryParam(breakNum=" + this.getBreakNum() + ", relationBillNum=" + this.getRelationBillNum() + ", dealStatus=" + this.getDealStatus() + ", breakInitRoleType=" + this.getBreakInitRoleType() + ", breakInitRoleName=" + this.getBreakInitRoleName() + ", breakInitRoleId=" + this.getBreakInitRoleId() + ", proposeTimeStart=" + this.getProposeTimeStart() + ", proposeTimeEnd=" + this.getProposeTimeEnd() + ")";
    }
}

