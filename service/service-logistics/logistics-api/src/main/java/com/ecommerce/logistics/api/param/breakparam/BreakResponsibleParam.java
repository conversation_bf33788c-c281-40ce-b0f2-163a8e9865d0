
package com.ecommerce.logistics.api.param.breakparam;

import io.swagger.v3.oas.annotations.media.Schema;

import jakarta.validation.constraints.NotBlank;

@Schema(name="违约记录责任归属入参")
public class BreakResponsibleParam {
    @Schema(description="违约ID")
    @NotBlank(message="违约ID不能为空！")
    private String breakId;
    @Schema(description="责任归属方名称")
    private String responsibleRoleName;
    @Schema(description="责任归属方ID")
    private String responsibleRoleId;
    @Schema(description="责任归属类型枚举")
    private String responsibleRoleType;
    @Schema(description="责任归属备注")
    private String responsibleContent;
    String operateUserId;

    public String getBreakId() {
        return this.breakId;
    }

    public String getResponsibleRoleName() {
        return this.responsibleRoleName;
    }

    public String getResponsibleRoleId() {
        return this.responsibleRoleId;
    }

    public String getResponsibleRoleType() {
        return this.responsibleRoleType;
    }

    public String getResponsibleContent() {
        return this.responsibleContent;
    }

    public String getOperateUserId() {
        return this.operateUserId;
    }

    public void setBreakId(String breakId) {
        this.breakId = breakId;
    }

    public void setResponsibleRoleName(String responsibleRoleName) {
        this.responsibleRoleName = responsibleRoleName;
    }

    public void setResponsibleRoleId(String responsibleRoleId) {
        this.responsibleRoleId = responsibleRoleId;
    }

    public void setResponsibleRoleType(String responsibleRoleType) {
        this.responsibleRoleType = responsibleRoleType;
    }

    public void setResponsibleContent(String responsibleContent) {
        this.responsibleContent = responsibleContent;
    }

    public void setOperateUserId(String operateUserId) {
        this.operateUserId = operateUserId;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof BreakResponsibleParam)) {
            return false;
        }
        BreakResponsibleParam other = (BreakResponsibleParam)o;
        if (!other.canEqual(this)) {
            return false;
        }
        String this$breakId = this.getBreakId();
        String other$breakId = other.getBreakId();
        if (this$breakId == null ? other$breakId != null : !this$breakId.equals(other$breakId)) {
            return false;
        }
        String this$responsibleRoleName = this.getResponsibleRoleName();
        String other$responsibleRoleName = other.getResponsibleRoleName();
        if (this$responsibleRoleName == null ? other$responsibleRoleName != null : !this$responsibleRoleName.equals(other$responsibleRoleName)) {
            return false;
        }
        String this$responsibleRoleId = this.getResponsibleRoleId();
        String other$responsibleRoleId = other.getResponsibleRoleId();
        if (this$responsibleRoleId == null ? other$responsibleRoleId != null : !this$responsibleRoleId.equals(other$responsibleRoleId)) {
            return false;
        }
        String this$responsibleRoleType = this.getResponsibleRoleType();
        String other$responsibleRoleType = other.getResponsibleRoleType();
        if (this$responsibleRoleType == null ? other$responsibleRoleType != null : !this$responsibleRoleType.equals(other$responsibleRoleType)) {
            return false;
        }
        String this$responsibleContent = this.getResponsibleContent();
        String other$responsibleContent = other.getResponsibleContent();
        if (this$responsibleContent == null ? other$responsibleContent != null : !this$responsibleContent.equals(other$responsibleContent)) {
            return false;
        }
        String this$operateUserId = this.getOperateUserId();
        String other$operateUserId = other.getOperateUserId();
        return !(this$operateUserId == null ? other$operateUserId != null : !this$operateUserId.equals(other$operateUserId));
    }

    protected boolean canEqual(Object other) {
        return other instanceof BreakResponsibleParam;
    }

    public int hashCode() {
        int result = 1;
        String $breakId = this.getBreakId();
        result = result * 59 + ($breakId == null ? 43 : $breakId.hashCode());
        String $responsibleRoleName = this.getResponsibleRoleName();
        result = result * 59 + ($responsibleRoleName == null ? 43 : $responsibleRoleName.hashCode());
        String $responsibleRoleId = this.getResponsibleRoleId();
        result = result * 59 + ($responsibleRoleId == null ? 43 : $responsibleRoleId.hashCode());
        String $responsibleRoleType = this.getResponsibleRoleType();
        result = result * 59 + ($responsibleRoleType == null ? 43 : $responsibleRoleType.hashCode());
        String $responsibleContent = this.getResponsibleContent();
        result = result * 59 + ($responsibleContent == null ? 43 : $responsibleContent.hashCode());
        String $operateUserId = this.getOperateUserId();
        result = result * 59 + ($operateUserId == null ? 43 : $operateUserId.hashCode());
        return result;
    }

    public String toString() {
        return "BreakResponsibleParam(breakId=" + this.getBreakId() + ", responsibleRoleName=" + this.getResponsibleRoleName() + ", responsibleRoleId=" + this.getResponsibleRoleId() + ", responsibleRoleType=" + this.getResponsibleRoleType() + ", responsibleContent=" + this.getResponsibleContent() + ", operateUserId=" + this.getOperateUserId() + ")";
    }
}

