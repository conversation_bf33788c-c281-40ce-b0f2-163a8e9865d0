
package com.ecommerce.logistics.api.param.complaint;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(name="批量删除投入记录入参")
public class ComplaintBatchDelParam {
    @Schema(description="投诉ID列表")
    private List<String> complaintIdList;
    private String updateUser;

    public List<String> getComplaintIdList() {
        return this.complaintIdList;
    }

    public String getUpdateUser() {
        return this.updateUser;
    }

    public void setComplaintIdList(List<String> complaintIdList) {
        this.complaintIdList = complaintIdList;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof ComplaintBatchDelParam)) {
            return false;
        }
        ComplaintBatchDelParam other = (ComplaintBatchDelParam)o;
        if (!other.canEqual(this)) {
            return false;
        }
        List<String> this$complaintIdList = this.getComplaintIdList();
        List<String> other$complaintIdList = other.getComplaintIdList();
        if (this$complaintIdList == null ? other$complaintIdList != null : !((Object)this$complaintIdList).equals(other$complaintIdList)) {
            return false;
        }
        String this$updateUser = this.getUpdateUser();
        String other$updateUser = other.getUpdateUser();
        return !(this$updateUser == null ? other$updateUser != null : !this$updateUser.equals(other$updateUser));
    }

    protected boolean canEqual(Object other) {
        return other instanceof ComplaintBatchDelParam;
    }

    public int hashCode() {
        int result = 1;
        List<String> $complaintIdList = this.getComplaintIdList();
        result = result * 59 + ($complaintIdList == null ? 43 : ((Object)$complaintIdList).hashCode());
        String $updateUser = this.getUpdateUser();
        result = result * 59 + ($updateUser == null ? 43 : $updateUser.hashCode());
        return result;
    }

    public String toString() {
        return "ComplaintBatchDelParam(complaintIdList=" + this.getComplaintIdList() + ", updateUser=" + this.getUpdateUser() + ")";
    }
}

