
package com.ecommerce.logistics.api.param.complaint;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;

@Schema(name="投诉记录处理完成入参")
public class ComplaintDealFinishParam {
    @Schema(description="投诉Id")
    private String complaintId;
    @Schema(description="罚款金额")
    private BigDecimal fineAmount;
    @Schema(description="处理结果详情")
    private String dealResult;
    @Schema(description="是否罚款【0:未罚款 1:罚款】")
    private Integer fineFlag;
    private String dealFinishUserId;
    private String dealFinishUserName;
    private String updateUser;

    public String getComplaintId() {
        return this.complaintId;
    }

    public BigDecimal getFineAmount() {
        return this.fineAmount;
    }

    public String getDealResult() {
        return this.dealResult;
    }

    public Integer getFineFlag() {
        return this.fineFlag;
    }

    public String getDealFinishUserId() {
        return this.dealFinishUserId;
    }

    public String getDealFinishUserName() {
        return this.dealFinishUserName;
    }

    public String getUpdateUser() {
        return this.updateUser;
    }

    public void setComplaintId(String complaintId) {
        this.complaintId = complaintId;
    }

    public void setFineAmount(BigDecimal fineAmount) {
        this.fineAmount = fineAmount;
    }

    public void setDealResult(String dealResult) {
        this.dealResult = dealResult;
    }

    public void setFineFlag(Integer fineFlag) {
        this.fineFlag = fineFlag;
    }

    public void setDealFinishUserId(String dealFinishUserId) {
        this.dealFinishUserId = dealFinishUserId;
    }

    public void setDealFinishUserName(String dealFinishUserName) {
        this.dealFinishUserName = dealFinishUserName;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof ComplaintDealFinishParam)) {
            return false;
        }
        ComplaintDealFinishParam other = (ComplaintDealFinishParam)o;
        if (!other.canEqual(this)) {
            return false;
        }
        String this$complaintId = this.getComplaintId();
        String other$complaintId = other.getComplaintId();
        if (this$complaintId == null ? other$complaintId != null : !this$complaintId.equals(other$complaintId)) {
            return false;
        }
        BigDecimal this$fineAmount = this.getFineAmount();
        BigDecimal other$fineAmount = other.getFineAmount();
        if (this$fineAmount == null ? other$fineAmount != null : !((Object)this$fineAmount).equals(other$fineAmount)) {
            return false;
        }
        String this$dealResult = this.getDealResult();
        String other$dealResult = other.getDealResult();
        if (this$dealResult == null ? other$dealResult != null : !this$dealResult.equals(other$dealResult)) {
            return false;
        }
        Integer this$fineFlag = this.getFineFlag();
        Integer other$fineFlag = other.getFineFlag();
        if (this$fineFlag == null ? other$fineFlag != null : !((Object)this$fineFlag).equals(other$fineFlag)) {
            return false;
        }
        String this$dealFinishUserId = this.getDealFinishUserId();
        String other$dealFinishUserId = other.getDealFinishUserId();
        if (this$dealFinishUserId == null ? other$dealFinishUserId != null : !this$dealFinishUserId.equals(other$dealFinishUserId)) {
            return false;
        }
        String this$dealFinishUserName = this.getDealFinishUserName();
        String other$dealFinishUserName = other.getDealFinishUserName();
        if (this$dealFinishUserName == null ? other$dealFinishUserName != null : !this$dealFinishUserName.equals(other$dealFinishUserName)) {
            return false;
        }
        String this$updateUser = this.getUpdateUser();
        String other$updateUser = other.getUpdateUser();
        return !(this$updateUser == null ? other$updateUser != null : !this$updateUser.equals(other$updateUser));
    }

    protected boolean canEqual(Object other) {
        return other instanceof ComplaintDealFinishParam;
    }

    public int hashCode() {
        int result = 1;
        String $complaintId = this.getComplaintId();
        result = result * 59 + ($complaintId == null ? 43 : $complaintId.hashCode());
        BigDecimal $fineAmount = this.getFineAmount();
        result = result * 59 + ($fineAmount == null ? 43 : ((Object)$fineAmount).hashCode());
        String $dealResult = this.getDealResult();
        result = result * 59 + ($dealResult == null ? 43 : $dealResult.hashCode());
        Integer $fineFlag = this.getFineFlag();
        result = result * 59 + ($fineFlag == null ? 43 : ((Object)$fineFlag).hashCode());
        String $dealFinishUserId = this.getDealFinishUserId();
        result = result * 59 + ($dealFinishUserId == null ? 43 : $dealFinishUserId.hashCode());
        String $dealFinishUserName = this.getDealFinishUserName();
        result = result * 59 + ($dealFinishUserName == null ? 43 : $dealFinishUserName.hashCode());
        String $updateUser = this.getUpdateUser();
        result = result * 59 + ($updateUser == null ? 43 : $updateUser.hashCode());
        return result;
    }

    public String toString() {
        return "ComplaintDealFinishParam(complaintId=" + this.getComplaintId() + ", fineAmount=" + this.getFineAmount() + ", dealResult=" + this.getDealResult() + ", fineFlag=" + this.getFineFlag() + ", dealFinishUserId=" + this.getDealFinishUserId() + ", dealFinishUserName=" + this.getDealFinishUserName() + ", updateUser=" + this.getUpdateUser() + ")";
    }
}

