
package com.ecommerce.logistics.api.param.complaint;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name="投诉记录完成后满意度评价入参")
public class ComplaintEvaluationParam {
    @Schema(description="投诉ID")
    private String complaintId;
    @Schema(description="投诉满意度评价得分")
    private Float evaluationScore;
    private String updateUser;

    public String getComplaintId() {
        return this.complaintId;
    }

    public Float getEvaluationScore() {
        return this.evaluationScore;
    }

    public String getUpdateUser() {
        return this.updateUser;
    }

    public void setComplaintId(String complaintId) {
        this.complaintId = complaintId;
    }

    public void setEvaluationScore(Float evaluationScore) {
        this.evaluationScore = evaluationScore;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof ComplaintEvaluationParam)) {
            return false;
        }
        ComplaintEvaluationParam other = (ComplaintEvaluationParam)o;
        if (!other.canEqual(this)) {
            return false;
        }
        String this$complaintId = this.getComplaintId();
        String other$complaintId = other.getComplaintId();
        if (this$complaintId == null ? other$complaintId != null : !this$complaintId.equals(other$complaintId)) {
            return false;
        }
        Float this$evaluationScore = this.getEvaluationScore();
        Float other$evaluationScore = other.getEvaluationScore();
        if (this$evaluationScore == null ? other$evaluationScore != null : !((Object)this$evaluationScore).equals(other$evaluationScore)) {
            return false;
        }
        String this$updateUser = this.getUpdateUser();
        String other$updateUser = other.getUpdateUser();
        return !(this$updateUser == null ? other$updateUser != null : !this$updateUser.equals(other$updateUser));
    }

    protected boolean canEqual(Object other) {
        return other instanceof ComplaintEvaluationParam;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        String $complaintId = this.getComplaintId();
        result = result * 59 + ($complaintId == null ? 43 : $complaintId.hashCode());
        Float $evaluationScore = this.getEvaluationScore();
        result = result * 59 + ($evaluationScore == null ? 43 : ((Object)$evaluationScore).hashCode());
        String $updateUser = this.getUpdateUser();
        result = result * 59 + ($updateUser == null ? 43 : $updateUser.hashCode());
        return result;
    }

    public String toString() {
        return "ComplaintEvaluationParam(complaintId=" + this.getComplaintId() + ", evaluationScore=" + this.getEvaluationScore() + ", updateUser=" + this.getUpdateUser() + ")";
    }
}

