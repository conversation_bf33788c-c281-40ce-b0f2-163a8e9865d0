
package com.ecommerce.logistics.api.param.complaint;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

@Schema(name="投诉记录跟进处理入参")
public class ComplaintFollowParam {
    @Schema(description="投诉记录ID")
    private String complaintId;
    @Schema(description="操作内容")
    private String content;
    @Schema(description="操作用户Id")
    private String operationUserId;
    @Schema(description="操作用户名称")
    private String operationUserName;
    @Schema(description="操作时间")
    private Date operationTime;

    public String getComplaintId() {
        return this.complaintId;
    }

    public String getContent() {
        return this.content;
    }

    public String getOperationUserId() {
        return this.operationUserId;
    }

    public String getOperationUserName() {
        return this.operationUserName;
    }

    public Date getOperationTime() {
        return this.operationTime;
    }

    public void setComplaintId(String complaintId) {
        this.complaintId = complaintId;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public void setOperationUserId(String operationUserId) {
        this.operationUserId = operationUserId;
    }

    public void setOperationUserName(String operationUserName) {
        this.operationUserName = operationUserName;
    }

    public void setOperationTime(Date operationTime) {
        this.operationTime = operationTime;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof ComplaintFollowParam)) {
            return false;
        }
        ComplaintFollowParam other = (ComplaintFollowParam)o;
        if (!other.canEqual(this)) {
            return false;
        }
        String this$complaintId = this.getComplaintId();
        String other$complaintId = other.getComplaintId();
        if (this$complaintId == null ? other$complaintId != null : !this$complaintId.equals(other$complaintId)) {
            return false;
        }
        String this$content = this.getContent();
        String other$content = other.getContent();
        if (this$content == null ? other$content != null : !this$content.equals(other$content)) {
            return false;
        }
        String this$operationUserId = this.getOperationUserId();
        String other$operationUserId = other.getOperationUserId();
        if (this$operationUserId == null ? other$operationUserId != null : !this$operationUserId.equals(other$operationUserId)) {
            return false;
        }
        String this$operationUserName = this.getOperationUserName();
        String other$operationUserName = other.getOperationUserName();
        if (this$operationUserName == null ? other$operationUserName != null : !this$operationUserName.equals(other$operationUserName)) {
            return false;
        }
        Date this$operationTime = this.getOperationTime();
        Date other$operationTime = other.getOperationTime();
        return !(this$operationTime == null ? other$operationTime != null : !((Object)this$operationTime).equals(other$operationTime));
    }

    protected boolean canEqual(Object other) {
        return other instanceof ComplaintFollowParam;
    }

    public int hashCode() {
        int result = 1;
        String $complaintId = this.getComplaintId();
        result = result * 59 + ($complaintId == null ? 43 : $complaintId.hashCode());
        String $content = this.getContent();
        result = result * 59 + ($content == null ? 43 : $content.hashCode());
        String $operationUserId = this.getOperationUserId();
        result = result * 59 + ($operationUserId == null ? 43 : $operationUserId.hashCode());
        String $operationUserName = this.getOperationUserName();
        result = result * 59 + ($operationUserName == null ? 43 : $operationUserName.hashCode());
        Date $operationTime = this.getOperationTime();
        result = result * 59 + ($operationTime == null ? 43 : ((Object)$operationTime).hashCode());
        return result;
    }

    public String toString() {
        return "ComplaintFollowParam(complaintId=" + this.getComplaintId() + ", content=" + this.getContent() + ", operationUserId=" + this.getOperationUserId() + ", operationUserName=" + this.getOperationUserName() + ", operationTime=" + this.getOperationTime() + ")";
    }
}

