
package com.ecommerce.logistics.api.param.complaint;

import com.ecommerce.logistics.api.dto.attachment.AttAddDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(name="投诉记录入参")
public class ComplaintParam {
    @Schema(description="投诉记录ID")
    private String complaintId;
    @Schema(description="投诉编号")
    private String complaintNum;
    @Schema(description="投诉类型枚举[1.运输时效2.运输质量3.服务态度4.其他]")
    private String complaintType;
    @Schema(description="关联单据号")
    private String relationBillNum;
    @Schema(description="投诉方类型枚举")
    private String complaintInitRoleType;
    @Schema(description="投诉方名称")
    private String complaintInitRoleName;
    @Schema(description="投诉方ID")
    private String complaintInitRoleId;
    @Schema(description="投诉内容")
    private String complaintContent;
    @Schema(description="被投诉方类型枚举")
    private String complaintAcceptRoleType;
    @Schema(description="被投诉方ID")
    private String complaintAcceptRoleId;
    @Schema(description="被投诉方名称")
    private String complaintAcceptRoleName;
    @Schema(description="凭证附件对象")
    private List<AttAddDTO> attListAddDTO;
    private String updateUser;
    private String createUser;

    public String getComplaintId() {
        return this.complaintId;
    }

    public String getComplaintNum() {
        return this.complaintNum;
    }

    public String getComplaintType() {
        return this.complaintType;
    }

    public String getRelationBillNum() {
        return this.relationBillNum;
    }

    public String getComplaintInitRoleType() {
        return this.complaintInitRoleType;
    }

    public String getComplaintInitRoleName() {
        return this.complaintInitRoleName;
    }

    public String getComplaintInitRoleId() {
        return this.complaintInitRoleId;
    }

    public String getComplaintContent() {
        return this.complaintContent;
    }

    public String getComplaintAcceptRoleType() {
        return this.complaintAcceptRoleType;
    }

    public String getComplaintAcceptRoleId() {
        return this.complaintAcceptRoleId;
    }

    public String getComplaintAcceptRoleName() {
        return this.complaintAcceptRoleName;
    }

    public List<AttAddDTO> getAttListAddDTO() {
        return this.attListAddDTO;
    }

    public String getUpdateUser() {
        return this.updateUser;
    }

    public String getCreateUser() {
        return this.createUser;
    }

    public void setComplaintId(String complaintId) {
        this.complaintId = complaintId;
    }

    public void setComplaintNum(String complaintNum) {
        this.complaintNum = complaintNum;
    }

    public void setComplaintType(String complaintType) {
        this.complaintType = complaintType;
    }

    public void setRelationBillNum(String relationBillNum) {
        this.relationBillNum = relationBillNum;
    }

    public void setComplaintInitRoleType(String complaintInitRoleType) {
        this.complaintInitRoleType = complaintInitRoleType;
    }

    public void setComplaintInitRoleName(String complaintInitRoleName) {
        this.complaintInitRoleName = complaintInitRoleName;
    }

    public void setComplaintInitRoleId(String complaintInitRoleId) {
        this.complaintInitRoleId = complaintInitRoleId;
    }

    public void setComplaintContent(String complaintContent) {
        this.complaintContent = complaintContent;
    }

    public void setComplaintAcceptRoleType(String complaintAcceptRoleType) {
        this.complaintAcceptRoleType = complaintAcceptRoleType;
    }

    public void setComplaintAcceptRoleId(String complaintAcceptRoleId) {
        this.complaintAcceptRoleId = complaintAcceptRoleId;
    }

    public void setComplaintAcceptRoleName(String complaintAcceptRoleName) {
        this.complaintAcceptRoleName = complaintAcceptRoleName;
    }

    public void setAttListAddDTO(List<AttAddDTO> attListAddDTO) {
        this.attListAddDTO = attListAddDTO;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof ComplaintParam)) {
            return false;
        }
        ComplaintParam other = (ComplaintParam)o;
        if (!other.canEqual(this)) {
            return false;
        }
        String this$complaintId = this.getComplaintId();
        String other$complaintId = other.getComplaintId();
        if (this$complaintId == null ? other$complaintId != null : !this$complaintId.equals(other$complaintId)) {
            return false;
        }
        String this$complaintNum = this.getComplaintNum();
        String other$complaintNum = other.getComplaintNum();
        if (this$complaintNum == null ? other$complaintNum != null : !this$complaintNum.equals(other$complaintNum)) {
            return false;
        }
        String this$complaintType = this.getComplaintType();
        String other$complaintType = other.getComplaintType();
        if (this$complaintType == null ? other$complaintType != null : !this$complaintType.equals(other$complaintType)) {
            return false;
        }
        String this$relationBillNum = this.getRelationBillNum();
        String other$relationBillNum = other.getRelationBillNum();
        if (this$relationBillNum == null ? other$relationBillNum != null : !this$relationBillNum.equals(other$relationBillNum)) {
            return false;
        }
        String this$complaintInitRoleType = this.getComplaintInitRoleType();
        String other$complaintInitRoleType = other.getComplaintInitRoleType();
        if (this$complaintInitRoleType == null ? other$complaintInitRoleType != null : !this$complaintInitRoleType.equals(other$complaintInitRoleType)) {
            return false;
        }
        String this$complaintInitRoleName = this.getComplaintInitRoleName();
        String other$complaintInitRoleName = other.getComplaintInitRoleName();
        if (this$complaintInitRoleName == null ? other$complaintInitRoleName != null : !this$complaintInitRoleName.equals(other$complaintInitRoleName)) {
            return false;
        }
        String this$complaintInitRoleId = this.getComplaintInitRoleId();
        String other$complaintInitRoleId = other.getComplaintInitRoleId();
        if (this$complaintInitRoleId == null ? other$complaintInitRoleId != null : !this$complaintInitRoleId.equals(other$complaintInitRoleId)) {
            return false;
        }
        String this$complaintContent = this.getComplaintContent();
        String other$complaintContent = other.getComplaintContent();
        if (this$complaintContent == null ? other$complaintContent != null : !this$complaintContent.equals(other$complaintContent)) {
            return false;
        }
        String this$complaintAcceptRoleType = this.getComplaintAcceptRoleType();
        String other$complaintAcceptRoleType = other.getComplaintAcceptRoleType();
        if (this$complaintAcceptRoleType == null ? other$complaintAcceptRoleType != null : !this$complaintAcceptRoleType.equals(other$complaintAcceptRoleType)) {
            return false;
        }
        String this$complaintAcceptRoleId = this.getComplaintAcceptRoleId();
        String other$complaintAcceptRoleId = other.getComplaintAcceptRoleId();
        if (this$complaintAcceptRoleId == null ? other$complaintAcceptRoleId != null : !this$complaintAcceptRoleId.equals(other$complaintAcceptRoleId)) {
            return false;
        }
        String this$complaintAcceptRoleName = this.getComplaintAcceptRoleName();
        String other$complaintAcceptRoleName = other.getComplaintAcceptRoleName();
        if (this$complaintAcceptRoleName == null ? other$complaintAcceptRoleName != null : !this$complaintAcceptRoleName.equals(other$complaintAcceptRoleName)) {
            return false;
        }
        List<AttAddDTO> this$attListAddDTO = this.getAttListAddDTO();
        List<AttAddDTO> other$attListAddDTO = other.getAttListAddDTO();
        if (this$attListAddDTO == null ? other$attListAddDTO != null : !((Object)this$attListAddDTO).equals(other$attListAddDTO)) {
            return false;
        }
        String this$updateUser = this.getUpdateUser();
        String other$updateUser = other.getUpdateUser();
        if (this$updateUser == null ? other$updateUser != null : !this$updateUser.equals(other$updateUser)) {
            return false;
        }
        String this$createUser = this.getCreateUser();
        String other$createUser = other.getCreateUser();
        return !(this$createUser == null ? other$createUser != null : !this$createUser.equals(other$createUser));
    }

    protected boolean canEqual(Object other) {
        return other instanceof ComplaintParam;
    }

    public int hashCode() {
        int result = 1;
        String $complaintId = this.getComplaintId();
        result = result * 59 + ($complaintId == null ? 43 : $complaintId.hashCode());
        String $complaintNum = this.getComplaintNum();
        result = result * 59 + ($complaintNum == null ? 43 : $complaintNum.hashCode());
        String $complaintType = this.getComplaintType();
        result = result * 59 + ($complaintType == null ? 43 : $complaintType.hashCode());
        String $relationBillNum = this.getRelationBillNum();
        result = result * 59 + ($relationBillNum == null ? 43 : $relationBillNum.hashCode());
        String $complaintInitRoleType = this.getComplaintInitRoleType();
        result = result * 59 + ($complaintInitRoleType == null ? 43 : $complaintInitRoleType.hashCode());
        String $complaintInitRoleName = this.getComplaintInitRoleName();
        result = result * 59 + ($complaintInitRoleName == null ? 43 : $complaintInitRoleName.hashCode());
        String $complaintInitRoleId = this.getComplaintInitRoleId();
        result = result * 59 + ($complaintInitRoleId == null ? 43 : $complaintInitRoleId.hashCode());
        String $complaintContent = this.getComplaintContent();
        result = result * 59 + ($complaintContent == null ? 43 : $complaintContent.hashCode());
        String $complaintAcceptRoleType = this.getComplaintAcceptRoleType();
        result = result * 59 + ($complaintAcceptRoleType == null ? 43 : $complaintAcceptRoleType.hashCode());
        String $complaintAcceptRoleId = this.getComplaintAcceptRoleId();
        result = result * 59 + ($complaintAcceptRoleId == null ? 43 : $complaintAcceptRoleId.hashCode());
        String $complaintAcceptRoleName = this.getComplaintAcceptRoleName();
        result = result * 59 + ($complaintAcceptRoleName == null ? 43 : $complaintAcceptRoleName.hashCode());
        List<AttAddDTO> $attListAddDTO = this.getAttListAddDTO();
        result = result * 59 + ($attListAddDTO == null ? 43 : ((Object)$attListAddDTO).hashCode());
        String $updateUser = this.getUpdateUser();
        result = result * 59 + ($updateUser == null ? 43 : $updateUser.hashCode());
        String $createUser = this.getCreateUser();
        result = result * 59 + ($createUser == null ? 43 : $createUser.hashCode());
        return result;
    }

    public String toString() {
        return "ComplaintParam(complaintId=" + this.getComplaintId() + ", complaintNum=" + this.getComplaintNum() + ", complaintType=" + this.getComplaintType() + ", relationBillNum=" + this.getRelationBillNum() + ", complaintInitRoleType=" + this.getComplaintInitRoleType() + ", complaintInitRoleName=" + this.getComplaintInitRoleName() + ", complaintInitRoleId=" + this.getComplaintInitRoleId() + ", complaintContent=" + this.getComplaintContent() + ", complaintAcceptRoleType=" + this.getComplaintAcceptRoleType() + ", complaintAcceptRoleId=" + this.getComplaintAcceptRoleId() + ", complaintAcceptRoleName=" + this.getComplaintAcceptRoleName() + ", attListAddDTO=" + this.getAttListAddDTO() + ", updateUser=" + this.getUpdateUser() + ", createUser=" + this.getCreateUser() + ")";
    }
}

