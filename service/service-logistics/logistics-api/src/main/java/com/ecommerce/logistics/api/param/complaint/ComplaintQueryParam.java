
package com.ecommerce.logistics.api.param.complaint;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

@Schema(name="投诉记录查询入参")
public class ComplaintQueryParam {
    @Schema(description="投诉编号")
    private String complaintNum;
    @Schema(description="处理状态")
    private String dealStatus;
    @Schema(description="投诉类型枚举[1.运输时效2.运输质量3.服务态度4.其他]")
    private String complaintType;
    @Schema(description="投诉方类型枚举")
    private String complaintInitRoleType;
    @Schema(description="投诉方名称")
    private String complaintInitRoleName;
    @Schema(description="投诉方ID")
    private String complaintInitRoleId;
    @Schema(description="被投诉方类型枚举")
    private String complaintAcceptRoleType;
    @Schema(description="被投诉方ID")
    private String complaintAcceptRoleId;
    @Schema(description="被投诉方名称")
    private String complaintAcceptRoleName;
    @Schema(description="提出时间【创建时间】-开始")
    private Date proposeTimeStart;
    @Schema(description="提出时间【创建时间】-结束")
    private Date proposeTimeEnd;

    public String getComplaintNum() {
        return this.complaintNum;
    }

    public String getDealStatus() {
        return this.dealStatus;
    }

    public String getComplaintType() {
        return this.complaintType;
    }

    public String getComplaintInitRoleType() {
        return this.complaintInitRoleType;
    }

    public String getComplaintInitRoleName() {
        return this.complaintInitRoleName;
    }

    public String getComplaintInitRoleId() {
        return this.complaintInitRoleId;
    }

    public String getComplaintAcceptRoleType() {
        return this.complaintAcceptRoleType;
    }

    public String getComplaintAcceptRoleId() {
        return this.complaintAcceptRoleId;
    }

    public String getComplaintAcceptRoleName() {
        return this.complaintAcceptRoleName;
    }

    public Date getProposeTimeStart() {
        return this.proposeTimeStart;
    }

    public Date getProposeTimeEnd() {
        return this.proposeTimeEnd;
    }

    public void setComplaintNum(String complaintNum) {
        this.complaintNum = complaintNum;
    }

    public void setDealStatus(String dealStatus) {
        this.dealStatus = dealStatus;
    }

    public void setComplaintType(String complaintType) {
        this.complaintType = complaintType;
    }

    public void setComplaintInitRoleType(String complaintInitRoleType) {
        this.complaintInitRoleType = complaintInitRoleType;
    }

    public void setComplaintInitRoleName(String complaintInitRoleName) {
        this.complaintInitRoleName = complaintInitRoleName;
    }

    public void setComplaintInitRoleId(String complaintInitRoleId) {
        this.complaintInitRoleId = complaintInitRoleId;
    }

    public void setComplaintAcceptRoleType(String complaintAcceptRoleType) {
        this.complaintAcceptRoleType = complaintAcceptRoleType;
    }

    public void setComplaintAcceptRoleId(String complaintAcceptRoleId) {
        this.complaintAcceptRoleId = complaintAcceptRoleId;
    }

    public void setComplaintAcceptRoleName(String complaintAcceptRoleName) {
        this.complaintAcceptRoleName = complaintAcceptRoleName;
    }

    public void setProposeTimeStart(Date proposeTimeStart) {
        this.proposeTimeStart = proposeTimeStart;
    }

    public void setProposeTimeEnd(Date proposeTimeEnd) {
        this.proposeTimeEnd = proposeTimeEnd;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof ComplaintQueryParam)) {
            return false;
        }
        ComplaintQueryParam other = (ComplaintQueryParam)o;
        if (!other.canEqual(this)) {
            return false;
        }
        String this$complaintNum = this.getComplaintNum();
        String other$complaintNum = other.getComplaintNum();
        if (this$complaintNum == null ? other$complaintNum != null : !this$complaintNum.equals(other$complaintNum)) {
            return false;
        }
        String this$dealStatus = this.getDealStatus();
        String other$dealStatus = other.getDealStatus();
        if (this$dealStatus == null ? other$dealStatus != null : !this$dealStatus.equals(other$dealStatus)) {
            return false;
        }
        String this$complaintType = this.getComplaintType();
        String other$complaintType = other.getComplaintType();
        if (this$complaintType == null ? other$complaintType != null : !this$complaintType.equals(other$complaintType)) {
            return false;
        }
        String this$complaintInitRoleType = this.getComplaintInitRoleType();
        String other$complaintInitRoleType = other.getComplaintInitRoleType();
        if (this$complaintInitRoleType == null ? other$complaintInitRoleType != null : !this$complaintInitRoleType.equals(other$complaintInitRoleType)) {
            return false;
        }
        String this$complaintInitRoleName = this.getComplaintInitRoleName();
        String other$complaintInitRoleName = other.getComplaintInitRoleName();
        if (this$complaintInitRoleName == null ? other$complaintInitRoleName != null : !this$complaintInitRoleName.equals(other$complaintInitRoleName)) {
            return false;
        }
        String this$complaintInitRoleId = this.getComplaintInitRoleId();
        String other$complaintInitRoleId = other.getComplaintInitRoleId();
        if (this$complaintInitRoleId == null ? other$complaintInitRoleId != null : !this$complaintInitRoleId.equals(other$complaintInitRoleId)) {
            return false;
        }
        String this$complaintAcceptRoleType = this.getComplaintAcceptRoleType();
        String other$complaintAcceptRoleType = other.getComplaintAcceptRoleType();
        if (this$complaintAcceptRoleType == null ? other$complaintAcceptRoleType != null : !this$complaintAcceptRoleType.equals(other$complaintAcceptRoleType)) {
            return false;
        }
        String this$complaintAcceptRoleId = this.getComplaintAcceptRoleId();
        String other$complaintAcceptRoleId = other.getComplaintAcceptRoleId();
        if (this$complaintAcceptRoleId == null ? other$complaintAcceptRoleId != null : !this$complaintAcceptRoleId.equals(other$complaintAcceptRoleId)) {
            return false;
        }
        String this$complaintAcceptRoleName = this.getComplaintAcceptRoleName();
        String other$complaintAcceptRoleName = other.getComplaintAcceptRoleName();
        if (this$complaintAcceptRoleName == null ? other$complaintAcceptRoleName != null : !this$complaintAcceptRoleName.equals(other$complaintAcceptRoleName)) {
            return false;
        }
        Date this$proposeTimeStart = this.getProposeTimeStart();
        Date other$proposeTimeStart = other.getProposeTimeStart();
        if (this$proposeTimeStart == null ? other$proposeTimeStart != null : !((Object)this$proposeTimeStart).equals(other$proposeTimeStart)) {
            return false;
        }
        Date this$proposeTimeEnd = this.getProposeTimeEnd();
        Date other$proposeTimeEnd = other.getProposeTimeEnd();
        return !(this$proposeTimeEnd == null ? other$proposeTimeEnd != null : !((Object)this$proposeTimeEnd).equals(other$proposeTimeEnd));
    }

    protected boolean canEqual(Object other) {
        return other instanceof ComplaintQueryParam;
    }

    public int hashCode() {
        int result = 1;
        String $complaintNum = this.getComplaintNum();
        result = result * 59 + ($complaintNum == null ? 43 : $complaintNum.hashCode());
        String $dealStatus = this.getDealStatus();
        result = result * 59 + ($dealStatus == null ? 43 : $dealStatus.hashCode());
        String $complaintType = this.getComplaintType();
        result = result * 59 + ($complaintType == null ? 43 : $complaintType.hashCode());
        String $complaintInitRoleType = this.getComplaintInitRoleType();
        result = result * 59 + ($complaintInitRoleType == null ? 43 : $complaintInitRoleType.hashCode());
        String $complaintInitRoleName = this.getComplaintInitRoleName();
        result = result * 59 + ($complaintInitRoleName == null ? 43 : $complaintInitRoleName.hashCode());
        String $complaintInitRoleId = this.getComplaintInitRoleId();
        result = result * 59 + ($complaintInitRoleId == null ? 43 : $complaintInitRoleId.hashCode());
        String $complaintAcceptRoleType = this.getComplaintAcceptRoleType();
        result = result * 59 + ($complaintAcceptRoleType == null ? 43 : $complaintAcceptRoleType.hashCode());
        String $complaintAcceptRoleId = this.getComplaintAcceptRoleId();
        result = result * 59 + ($complaintAcceptRoleId == null ? 43 : $complaintAcceptRoleId.hashCode());
        String $complaintAcceptRoleName = this.getComplaintAcceptRoleName();
        result = result * 59 + ($complaintAcceptRoleName == null ? 43 : $complaintAcceptRoleName.hashCode());
        Date $proposeTimeStart = this.getProposeTimeStart();
        result = result * 59 + ($proposeTimeStart == null ? 43 : ((Object)$proposeTimeStart).hashCode());
        Date $proposeTimeEnd = this.getProposeTimeEnd();
        result = result * 59 + ($proposeTimeEnd == null ? 43 : ((Object)$proposeTimeEnd).hashCode());
        return result;
    }

    public String toString() {
        return "ComplaintQueryParam(complaintNum=" + this.getComplaintNum() + ", dealStatus=" + this.getDealStatus() + ", complaintType=" + this.getComplaintType() + ", complaintInitRoleType=" + this.getComplaintInitRoleType() + ", complaintInitRoleName=" + this.getComplaintInitRoleName() + ", complaintInitRoleId=" + this.getComplaintInitRoleId() + ", complaintAcceptRoleType=" + this.getComplaintAcceptRoleType() + ", complaintAcceptRoleId=" + this.getComplaintAcceptRoleId() + ", complaintAcceptRoleName=" + this.getComplaintAcceptRoleName() + ", proposeTimeStart=" + this.getProposeTimeStart() + ", proposeTimeEnd=" + this.getProposeTimeEnd() + ")";
    }
}

