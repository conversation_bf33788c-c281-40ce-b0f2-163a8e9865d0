
package com.ecommerce.logistics.api.param.complaint;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name="投诉记录责任归属入参")
public class ComplaintResponsibleParam {
    @Schema(description="投诉ID")
    private String complaintId;
    @Schema(description="责任归属类型枚举")
    private String responsibleRoleType;
    @Schema(description="责任归属名称(责任方名称)")
    private String responsibleRoleName;
    @Schema(description="责任归属人ID")
    private String responsibleRoleId;
    @Schema(description="责任归属备注")
    private String responsibleContent;
    private String updateUser;

    public String getComplaintId() {
        return this.complaintId;
    }

    public String getResponsibleRoleType() {
        return this.responsibleRoleType;
    }

    public String getResponsibleRoleName() {
        return this.responsibleRoleName;
    }

    public String getResponsibleRoleId() {
        return this.responsibleRoleId;
    }

    public String getResponsibleContent() {
        return this.responsibleContent;
    }

    public String getUpdateUser() {
        return this.updateUser;
    }

    public void setComplaintId(String complaintId) {
        this.complaintId = complaintId;
    }

    public void setResponsibleRoleType(String responsibleRoleType) {
        this.responsibleRoleType = responsibleRoleType;
    }

    public void setResponsibleRoleName(String responsibleRoleName) {
        this.responsibleRoleName = responsibleRoleName;
    }

    public void setResponsibleRoleId(String responsibleRoleId) {
        this.responsibleRoleId = responsibleRoleId;
    }

    public void setResponsibleContent(String responsibleContent) {
        this.responsibleContent = responsibleContent;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof ComplaintResponsibleParam)) {
            return false;
        }
        ComplaintResponsibleParam other = (ComplaintResponsibleParam)o;
        if (!other.canEqual(this)) {
            return false;
        }
        String this$complaintId = this.getComplaintId();
        String other$complaintId = other.getComplaintId();
        if (this$complaintId == null ? other$complaintId != null : !this$complaintId.equals(other$complaintId)) {
            return false;
        }
        String this$responsibleRoleType = this.getResponsibleRoleType();
        String other$responsibleRoleType = other.getResponsibleRoleType();
        if (this$responsibleRoleType == null ? other$responsibleRoleType != null : !this$responsibleRoleType.equals(other$responsibleRoleType)) {
            return false;
        }
        String this$responsibleRoleName = this.getResponsibleRoleName();
        String other$responsibleRoleName = other.getResponsibleRoleName();
        if (this$responsibleRoleName == null ? other$responsibleRoleName != null : !this$responsibleRoleName.equals(other$responsibleRoleName)) {
            return false;
        }
        String this$responsibleRoleId = this.getResponsibleRoleId();
        String other$responsibleRoleId = other.getResponsibleRoleId();
        if (this$responsibleRoleId == null ? other$responsibleRoleId != null : !this$responsibleRoleId.equals(other$responsibleRoleId)) {
            return false;
        }
        String this$responsibleContent = this.getResponsibleContent();
        String other$responsibleContent = other.getResponsibleContent();
        if (this$responsibleContent == null ? other$responsibleContent != null : !this$responsibleContent.equals(other$responsibleContent)) {
            return false;
        }
        String this$updateUser = this.getUpdateUser();
        String other$updateUser = other.getUpdateUser();
        return !(this$updateUser == null ? other$updateUser != null : !this$updateUser.equals(other$updateUser));
    }

    protected boolean canEqual(Object other) {
        return other instanceof ComplaintResponsibleParam;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        String $complaintId = this.getComplaintId();
        result = result * 59 + ($complaintId == null ? 43 : $complaintId.hashCode());
        String $responsibleRoleType = this.getResponsibleRoleType();
        result = result * 59 + ($responsibleRoleType == null ? 43 : $responsibleRoleType.hashCode());
        String $responsibleRoleName = this.getResponsibleRoleName();
        result = result * 59 + ($responsibleRoleName == null ? 43 : $responsibleRoleName.hashCode());
        String $responsibleRoleId = this.getResponsibleRoleId();
        result = result * 59 + ($responsibleRoleId == null ? 43 : $responsibleRoleId.hashCode());
        String $responsibleContent = this.getResponsibleContent();
        result = result * 59 + ($responsibleContent == null ? 43 : $responsibleContent.hashCode());
        String $updateUser = this.getUpdateUser();
        result = result * 59 + ($updateUser == null ? 43 : $updateUser.hashCode());
        return result;
    }

    public String toString() {
        return "ComplaintResponsibleParam(complaintId=" + this.getComplaintId() + ", responsibleRoleType=" + this.getResponsibleRoleType() + ", responsibleRoleName=" + this.getResponsibleRoleName() + ", responsibleRoleId=" + this.getResponsibleRoleId() + ", responsibleContent=" + this.getResponsibleContent() + ", updateUser=" + this.getUpdateUser() + ")";
    }
}

