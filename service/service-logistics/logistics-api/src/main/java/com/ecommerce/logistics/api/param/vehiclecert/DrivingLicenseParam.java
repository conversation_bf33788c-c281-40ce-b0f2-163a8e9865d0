
package com.ecommerce.logistics.api.param.vehiclecert;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name="行驶证信息入参")
public class DrivingLicenseParam {
    @Schema(description="资质id")
    private String certId;
    @Schema(description="行驶证档案编号")
    private String drivingLicenseNum;
    @Schema(description="核定载质量(吨)")
    private String approvedLoadingQuality;
    @Schema(description="总质量(吨)")
    private String totalQuality;
    @Schema(description="车辆类型（行驶证OCR识别返回数据）")
    private String carType;
    @Schema(description="使用性质（行驶证OCR识别返回数据）")
    private String useCharacter;
    @Schema(description="车辆识别代码（行驶证OCR识别返回数据）")
    private String vin;
    @Schema(description="车辆载重大于4.5吨则需要道路运输经营许可证(0-不需要上传，1-需要上传)")
    private Integer needCert;
    @Schema(description="所有人")
    private String owner;
    @Schema(description="注册日期")
    private String registTime;
    @Schema(description="发证日期")
    private String issuanceTime;
    @Schema(description="检验有效期截止时间")
    private String testEffectiveEndTime;
    @Schema(description="发证机关")
    private String issuingAuthority;

    public String getCertId() {
        return this.certId;
    }

    public String getDrivingLicenseNum() {
        return this.drivingLicenseNum;
    }

    public String getApprovedLoadingQuality() {
        return this.approvedLoadingQuality;
    }

    public String getTotalQuality() {
        return this.totalQuality;
    }

    public String getCarType() {
        return this.carType;
    }

    public String getUseCharacter() {
        return this.useCharacter;
    }

    public String getVin() {
        return this.vin;
    }

    public Integer getNeedCert() {
        return this.needCert;
    }

    public String getOwner() {
        return this.owner;
    }

    public String getRegistTime() {
        return this.registTime;
    }

    public String getIssuanceTime() {
        return this.issuanceTime;
    }

    public String getTestEffectiveEndTime() {
        return this.testEffectiveEndTime;
    }

    public String getIssuingAuthority() {
        return this.issuingAuthority;
    }

    public void setCertId(String certId) {
        this.certId = certId;
    }

    public void setDrivingLicenseNum(String drivingLicenseNum) {
        this.drivingLicenseNum = drivingLicenseNum;
    }

    public void setApprovedLoadingQuality(String approvedLoadingQuality) {
        this.approvedLoadingQuality = approvedLoadingQuality;
    }

    public void setTotalQuality(String totalQuality) {
        this.totalQuality = totalQuality;
    }

    public void setCarType(String carType) {
        this.carType = carType;
    }

    public void setUseCharacter(String useCharacter) {
        this.useCharacter = useCharacter;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public void setNeedCert(Integer needCert) {
        this.needCert = needCert;
    }

    public void setOwner(String owner) {
        this.owner = owner;
    }

    public void setRegistTime(String registTime) {
        this.registTime = registTime;
    }

    public void setIssuanceTime(String issuanceTime) {
        this.issuanceTime = issuanceTime;
    }

    public void setTestEffectiveEndTime(String testEffectiveEndTime) {
        this.testEffectiveEndTime = testEffectiveEndTime;
    }

    public void setIssuingAuthority(String issuingAuthority) {
        this.issuingAuthority = issuingAuthority;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof DrivingLicenseParam)) {
            return false;
        }
        DrivingLicenseParam other = (DrivingLicenseParam)o;
        if (!other.canEqual(this)) {
            return false;
        }
        String this$certId = this.getCertId();
        String other$certId = other.getCertId();
        if (this$certId == null ? other$certId != null : !this$certId.equals(other$certId)) {
            return false;
        }
        String this$drivingLicenseNum = this.getDrivingLicenseNum();
        String other$drivingLicenseNum = other.getDrivingLicenseNum();
        if (this$drivingLicenseNum == null ? other$drivingLicenseNum != null : !this$drivingLicenseNum.equals(other$drivingLicenseNum)) {
            return false;
        }
        String this$approvedLoadingQuality = this.getApprovedLoadingQuality();
        String other$approvedLoadingQuality = other.getApprovedLoadingQuality();
        if (this$approvedLoadingQuality == null ? other$approvedLoadingQuality != null : !this$approvedLoadingQuality.equals(other$approvedLoadingQuality)) {
            return false;
        }
        String this$totalQuality = this.getTotalQuality();
        String other$totalQuality = other.getTotalQuality();
        if (this$totalQuality == null ? other$totalQuality != null : !this$totalQuality.equals(other$totalQuality)) {
            return false;
        }
        String this$carType = this.getCarType();
        String other$carType = other.getCarType();
        if (this$carType == null ? other$carType != null : !this$carType.equals(other$carType)) {
            return false;
        }
        String this$useCharacter = this.getUseCharacter();
        String other$useCharacter = other.getUseCharacter();
        if (this$useCharacter == null ? other$useCharacter != null : !this$useCharacter.equals(other$useCharacter)) {
            return false;
        }
        String this$vin = this.getVin();
        String other$vin = other.getVin();
        if (this$vin == null ? other$vin != null : !this$vin.equals(other$vin)) {
            return false;
        }
        Integer this$needCert = this.getNeedCert();
        Integer other$needCert = other.getNeedCert();
        if (this$needCert == null ? other$needCert != null : !((Object)this$needCert).equals(other$needCert)) {
            return false;
        }
        String this$owner = this.getOwner();
        String other$owner = other.getOwner();
        if (this$owner == null ? other$owner != null : !this$owner.equals(other$owner)) {
            return false;
        }
        String this$registTime = this.getRegistTime();
        String other$registTime = other.getRegistTime();
        if (this$registTime == null ? other$registTime != null : !this$registTime.equals(other$registTime)) {
            return false;
        }
        String this$issuanceTime = this.getIssuanceTime();
        String other$issuanceTime = other.getIssuanceTime();
        if (this$issuanceTime == null ? other$issuanceTime != null : !this$issuanceTime.equals(other$issuanceTime)) {
            return false;
        }
        String this$testEffectiveEndTime = this.getTestEffectiveEndTime();
        String other$testEffectiveEndTime = other.getTestEffectiveEndTime();
        if (this$testEffectiveEndTime == null ? other$testEffectiveEndTime != null : !this$testEffectiveEndTime.equals(other$testEffectiveEndTime)) {
            return false;
        }
        String this$issuingAuthority = this.getIssuingAuthority();
        String other$issuingAuthority = other.getIssuingAuthority();
        return !(this$issuingAuthority == null ? other$issuingAuthority != null : !this$issuingAuthority.equals(other$issuingAuthority));
    }

    protected boolean canEqual(Object other) {
        return other instanceof DrivingLicenseParam;
    }

    public int hashCode() {
        int result = 1;
        String $certId = this.getCertId();
        result = result * 59 + ($certId == null ? 43 : $certId.hashCode());
        String $drivingLicenseNum = this.getDrivingLicenseNum();
        result = result * 59 + ($drivingLicenseNum == null ? 43 : $drivingLicenseNum.hashCode());
        String $approvedLoadingQuality = this.getApprovedLoadingQuality();
        result = result * 59 + ($approvedLoadingQuality == null ? 43 : $approvedLoadingQuality.hashCode());
        String $totalQuality = this.getTotalQuality();
        result = result * 59 + ($totalQuality == null ? 43 : $totalQuality.hashCode());
        String $carType = this.getCarType();
        result = result * 59 + ($carType == null ? 43 : $carType.hashCode());
        String $useCharacter = this.getUseCharacter();
        result = result * 59 + ($useCharacter == null ? 43 : $useCharacter.hashCode());
        String $vin = this.getVin();
        result = result * 59 + ($vin == null ? 43 : $vin.hashCode());
        Integer $needCert = this.getNeedCert();
        result = result * 59 + ($needCert == null ? 43 : ((Object)$needCert).hashCode());
        String $owner = this.getOwner();
        result = result * 59 + ($owner == null ? 43 : $owner.hashCode());
        String $registTime = this.getRegistTime();
        result = result * 59 + ($registTime == null ? 43 : $registTime.hashCode());
        String $issuanceTime = this.getIssuanceTime();
        result = result * 59 + ($issuanceTime == null ? 43 : $issuanceTime.hashCode());
        String $testEffectiveEndTime = this.getTestEffectiveEndTime();
        result = result * 59 + ($testEffectiveEndTime == null ? 43 : $testEffectiveEndTime.hashCode());
        String $issuingAuthority = this.getIssuingAuthority();
        result = result * 59 + ($issuingAuthority == null ? 43 : $issuingAuthority.hashCode());
        return result;
    }

    public String toString() {
        return "DrivingLicenseParam(certId=" + this.getCertId() + ", drivingLicenseNum=" + this.getDrivingLicenseNum() + ", approvedLoadingQuality=" + this.getApprovedLoadingQuality() + ", totalQuality=" + this.getTotalQuality() + ", carType=" + this.getCarType() + ", useCharacter=" + this.getUseCharacter() + ", vin=" + this.getVin() + ", needCert=" + this.getNeedCert() + ", owner=" + this.getOwner() + ", registTime=" + this.getRegistTime() + ", issuanceTime=" + this.getIssuanceTime() + ", testEffectiveEndTime=" + this.getTestEffectiveEndTime() + ", issuingAuthority=" + this.getIssuingAuthority() + ")";
    }
}

