
package com.ecommerce.logistics.api.param.vehiclecert;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name="道路运输证(营运证)信息入参")
public class RoadTransportParam {
    @Schema(description="资质id")
    private String certId;
    @Schema(description="车辆载重大于4.5吨则需要道路运输经营许可证(0-不需要上传，1-需要上传)")
    private Integer needCert;
    @Schema(description="道路运输证号")
    private String roadTransportNo;
    @Schema(description="经营许可证号")
    private String roadManagementNo;
    @Schema(description="有效期截止时间")
    private String effectiveEndTime;

    public String getCertId() {
        return this.certId;
    }

    public Integer getNeedCert() {
        return this.needCert;
    }

    public String getRoadTransportNo() {
        return this.roadTransportNo;
    }

    public String getRoadManagementNo() {
        return this.roadManagementNo;
    }

    public String getEffectiveEndTime() {
        return this.effectiveEndTime;
    }

    public void setCertId(String certId) {
        this.certId = certId;
    }

    public void setNeedCert(Integer needCert) {
        this.needCert = needCert;
    }

    public void setRoadTransportNo(String roadTransportNo) {
        this.roadTransportNo = roadTransportNo;
    }

    public void setRoadManagementNo(String roadManagementNo) {
        this.roadManagementNo = roadManagementNo;
    }

    public void setEffectiveEndTime(String effectiveEndTime) {
        this.effectiveEndTime = effectiveEndTime;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof RoadTransportParam)) {
            return false;
        }
        RoadTransportParam other = (RoadTransportParam)o;
        if (!other.canEqual(this)) {
            return false;
        }
        String this$certId = this.getCertId();
        String other$certId = other.getCertId();
        if (this$certId == null ? other$certId != null : !this$certId.equals(other$certId)) {
            return false;
        }
        Integer this$needCert = this.getNeedCert();
        Integer other$needCert = other.getNeedCert();
        if (this$needCert == null ? other$needCert != null : !((Object)this$needCert).equals(other$needCert)) {
            return false;
        }
        String this$roadTransportNo = this.getRoadTransportNo();
        String other$roadTransportNo = other.getRoadTransportNo();
        if (this$roadTransportNo == null ? other$roadTransportNo != null : !this$roadTransportNo.equals(other$roadTransportNo)) {
            return false;
        }
        String this$roadManagementNo = this.getRoadManagementNo();
        String other$roadManagementNo = other.getRoadManagementNo();
        if (this$roadManagementNo == null ? other$roadManagementNo != null : !this$roadManagementNo.equals(other$roadManagementNo)) {
            return false;
        }
        String this$effectiveEndTime = this.getEffectiveEndTime();
        String other$effectiveEndTime = other.getEffectiveEndTime();
        return !(this$effectiveEndTime == null ? other$effectiveEndTime != null : !this$effectiveEndTime.equals(other$effectiveEndTime));
    }

    protected boolean canEqual(Object other) {
        return other instanceof RoadTransportParam;
    }

    public int hashCode() {
        int result = 1;
        String $certId = this.getCertId();
        result = result * 59 + ($certId == null ? 43 : $certId.hashCode());
        Integer $needCert = this.getNeedCert();
        result = result * 59 + ($needCert == null ? 43 : ((Object)$needCert).hashCode());
        String $roadTransportNo = this.getRoadTransportNo();
        result = result * 59 + ($roadTransportNo == null ? 43 : $roadTransportNo.hashCode());
        String $roadManagementNo = this.getRoadManagementNo();
        result = result * 59 + ($roadManagementNo == null ? 43 : $roadManagementNo.hashCode());
        String $effectiveEndTime = this.getEffectiveEndTime();
        result = result * 59 + ($effectiveEndTime == null ? 43 : $effectiveEndTime.hashCode());
        return result;
    }

    public String toString() {
        return "RoadTransportParam(certId=" + this.getCertId() + ", needCert=" + this.getNeedCert() + ", roadTransportNo=" + this.getRoadTransportNo() + ", roadManagementNo=" + this.getRoadManagementNo() + ", effectiveEndTime=" + this.getEffectiveEndTime() + ")";
    }
}

