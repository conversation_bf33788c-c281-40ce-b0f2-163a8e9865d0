
package com.ecommerce.logistics.api.param.vehiclecert;

import com.ecommerce.logistics.api.dto.vehicle.VehicleAddDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@Schema(name="添加车辆信息入参")
public class VehicleAddParamDTO {
    @Schema(description="车辆基本信息和车载定位设备信息")
    @NotNull(message="车辆基本信息不能为空!")
    private @NotNull(message="车辆基本信息不能为空!") VehicleAddDTO vehicleAddDTO;
    @Schema(description="行驶证入参信息")
    private DrivingLicenseParam drivingLicenseParam;
    @Schema(description="道路运输证入参信息")
    private RoadTransportParam roadTransportParam;
    @Schema(description="操作人id")
    private String operateUserId;
    @Schema(description="承运商ID")
    private String carrierId;
    @Schema(description="承运商名称")
    private String carrierName;
    @Schema(description="车辆来源,1-自有车辆，2-外部车辆")
    @NotNull(message="车辆来源不能为空!")
    private @NotNull(message="车辆来源不能为空!") Integer vehicleSource;
}

