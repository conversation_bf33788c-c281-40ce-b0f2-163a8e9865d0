
package com.ecommerce.logistics.api.param.vehiclecert;

import com.ecommerce.logistics.api.dto.vehicle.VehicleAddDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import jakarta.validation.constraints.NotNull;

@Schema(name="车辆认证信息新增入参")
public class VehicleCertSaveParam {
    @Schema(description="车辆基本信息和车载定位设备信息")
    private VehicleAddDTO vehicleAddDTO;
    @Schema(description="行驶证入参信息")
    @NotNull(message="行驶证入参信息不能为空!")
    private @NotNull(message="行驶证入参信息不能为空!") DrivingLicenseParam drivingLicenseParam;
    @Schema(description="道路运输证入参信息")
    @NotNull(message="道路运输证入参信息不能为空!")
    private @NotNull(message="道路运输证入参信息不能为空!") RoadTransportParam roadTransportParam;
    private String operateUserId;

    public VehicleAddDTO getVehicleAddDTO() {
        return this.vehicleAddDTO;
    }

    public DrivingLicenseParam getDrivingLicenseParam() {
        return this.drivingLicenseParam;
    }

    public RoadTransportParam getRoadTransportParam() {
        return this.roadTransportParam;
    }

    public String getOperateUserId() {
        return this.operateUserId;
    }

    public void setVehicleAddDTO(VehicleAddDTO vehicleAddDTO) {
        this.vehicleAddDTO = vehicleAddDTO;
    }

    public void setDrivingLicenseParam(DrivingLicenseParam drivingLicenseParam) {
        this.drivingLicenseParam = drivingLicenseParam;
    }

    public void setRoadTransportParam(RoadTransportParam roadTransportParam) {
        this.roadTransportParam = roadTransportParam;
    }

    public void setOperateUserId(String operateUserId) {
        this.operateUserId = operateUserId;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof VehicleCertSaveParam)) {
            return false;
        }
        VehicleCertSaveParam other = (VehicleCertSaveParam)o;
        if (!other.canEqual(this)) {
            return false;
        }
        VehicleAddDTO this$vehicleAddDTO = this.getVehicleAddDTO();
        VehicleAddDTO other$vehicleAddDTO = other.getVehicleAddDTO();
        if (this$vehicleAddDTO == null ? other$vehicleAddDTO != null : !((Object)this$vehicleAddDTO).equals(other$vehicleAddDTO)) {
            return false;
        }
        DrivingLicenseParam this$drivingLicenseParam = this.getDrivingLicenseParam();
        DrivingLicenseParam other$drivingLicenseParam = other.getDrivingLicenseParam();
        if (this$drivingLicenseParam == null ? other$drivingLicenseParam != null : !((Object)this$drivingLicenseParam).equals(other$drivingLicenseParam)) {
            return false;
        }
        RoadTransportParam this$roadTransportParam = this.getRoadTransportParam();
        RoadTransportParam other$roadTransportParam = other.getRoadTransportParam();
        if (this$roadTransportParam == null ? other$roadTransportParam != null : !((Object)this$roadTransportParam).equals(other$roadTransportParam)) {
            return false;
        }
        String this$operateUserId = this.getOperateUserId();
        String other$operateUserId = other.getOperateUserId();
        return !(this$operateUserId == null ? other$operateUserId != null : !this$operateUserId.equals(other$operateUserId));
    }

    protected boolean canEqual(Object other) {
        return other instanceof VehicleCertSaveParam;
    }

    public int hashCode() {
        int result = 1;
        VehicleAddDTO $vehicleAddDTO = this.getVehicleAddDTO();
        result = result * 59 + ($vehicleAddDTO == null ? 43 : ((Object)$vehicleAddDTO).hashCode());
        DrivingLicenseParam $drivingLicenseParam = this.getDrivingLicenseParam();
        result = result * 59 + ($drivingLicenseParam == null ? 43 : ((Object)$drivingLicenseParam).hashCode());
        RoadTransportParam $roadTransportParam = this.getRoadTransportParam();
        result = result * 59 + ($roadTransportParam == null ? 43 : ((Object)$roadTransportParam).hashCode());
        String $operateUserId = this.getOperateUserId();
        result = result * 59 + ($operateUserId == null ? 43 : $operateUserId.hashCode());
        return result;
    }

    public String toString() {
        return "VehicleCertSaveParam(vehicleAddDTO=" + this.getVehicleAddDTO() + ", drivingLicenseParam=" + this.getDrivingLicenseParam() + ", roadTransportParam=" + this.getRoadTransportParam() + ", operateUserId=" + this.getOperateUserId() + ")";
    }
}

