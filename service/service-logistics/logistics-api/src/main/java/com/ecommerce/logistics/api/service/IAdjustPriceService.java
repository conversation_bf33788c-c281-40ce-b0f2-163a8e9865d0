
package com.ecommerce.logistics.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.logistics.api.dto.adjust.AdjustMemberDTO;
import com.ecommerce.logistics.api.dto.adjust.AdjustPriceResultDTO;
import com.ecommerce.logistics.api.dto.adjust.AdjustQueryDTO;
import com.ecommerce.logistics.api.dto.adjust.AdjustShipBillDTO;
import com.ecommerce.logistics.api.dto.adjust.AdjustTakeInfoDTO;

import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name ="logistics")
public interface IAdjustPriceService
{
    @PostMapping(value = "/adjustPrice/memberQuery")
    ItemResult<List<AdjustMemberDTO>> memberQuery(@RequestBody AdjustQueryDTO query);

    @PostMapping({"/adjustPrice/shipBillList"})
    ItemResult<List<AdjustShipBillDTO>> shipBillList(@RequestBody AdjustQueryDTO var1);

    @PostMapping({"/adjustPrice/saveAdjustPriceResult"})
    void saveAdjustPriceResult(@RequestBody AdjustPriceResultDTO var1);

    @PostMapping({"/adjustPrice/adjustConcreteByTakeCode"})
    ItemResult<List<String>> adjustConcreteByTakeCode(@RequestBody List<AdjustTakeInfoDTO> var1);
}

