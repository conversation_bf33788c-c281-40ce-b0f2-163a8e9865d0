
package com.ecommerce.logistics.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.logistics.api.dto.driver.AssignDriverLogAddDTO;
import com.ecommerce.logistics.api.dto.driver.AssignDriverLogCondDTO;
import com.ecommerce.logistics.api.dto.driver.AssignDriverLogResDTO;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "logistics")
public interface IAssignDriverLogService {


    @PostMapping("/assignDriverLog/queryByCond")
    public ItemResult<List<AssignDriverLogResDTO>> queryByCond(@RequestBody AssignDriverLogCondDTO assignDriverLogCondDTO);

    @PostMapping("/assignDriverLog/updateSelfPickAssignLog")
    public ItemResult<Void> updateSelfPickAssignLog(@RequestBody AssignDriverLogAddDTO assignDriverLogAddDTO);
}
