
package com.ecommerce.logistics.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.logistics.api.dto.attachment.AttListAddDTO;
import com.ecommerce.logistics.api.dto.attachment.AttListDTO;
import com.ecommerce.logistics.api.dto.attachment.AttListQueryDTO;
import com.ecommerce.logistics.api.dto.attachment.AttRemoveDTO;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "logistics")
public interface IAttachmentService {
    @PostMapping("/attachment/removeAtt")
    public ItemResult<Void> removeAtt(@RequestBody AttRemoveDTO var1);

    @PostMapping("/attachment/queryAttList")
    public ItemResult<List<AttListDTO>> queryAttList(@RequestBody AttListQueryDTO var1);

    @PostMapping("/attachment/addAtt")
    public ItemResult<Void> addAtt(@RequestBody AttListAddDTO var1);
}

