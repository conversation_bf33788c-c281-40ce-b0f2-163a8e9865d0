
package com.ecommerce.logistics.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.shippingroute.AuxiliaryPriceDTO;
import com.ecommerce.logistics.api.dto.shippingroute.AuxiliaryPriceListDTO;
import io.swagger.v3.oas.annotations.Operation;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "logistics")
public interface IAuxiliaryPriceService {
    @PostMapping("/auxiliaryPrice/addAuxiliaryPrice")
    ItemResult<Void> addAuxiliaryPrice(@RequestBody List<AuxiliaryPriceDTO> var1);

    @PostMapping("/auxiliaryPrice/delAuxiliaryPriceByNo")
    ItemResult<Void> delAuxiliaryPriceByNo(@RequestParam(value = "belongerId") String var1, @RequestParam(value = "auxiliaryPriceNo") String var2, @RequestParam(value = "operatorUserId") String var3);

    @PostMapping("/auxiliaryPrice/delAuxiliaryPrice")
    ItemResult<Void> delAuxiliaryPrice(@RequestParam(value = "auxiliaryPriceNo") String var1, @RequestParam(value = "belongerId") String var2, @RequestParam(value = "auxiliaryPriceId") String var3, @RequestParam(value = "operatorUserId") String var4);

    @PostMapping("/auxiliaryPrice/editAuxiliaryPrice")
    ItemResult<Void> editAuxiliaryPrice(@RequestBody List<AuxiliaryPriceDTO> var1);

    @Operation(summary = "通过辅助价目表编号查看辅助价目表详情(queryAuxiliaryPriceDetails 是否计算枯水期补助)")
    @PostMapping("/auxiliaryPrice/queryAuxiliaryPriceDetails")
    ItemResult<List<AuxiliaryPriceDTO>> queryAuxiliaryPriceDetails(@RequestParam(value = "belongerId") String var1, @RequestParam(value = "auxiliaryPriceNo") String var2, @RequestParam(value = "drySeasonPolicyAffected", defaultValue = "false") Boolean var3);

    @PostMapping("/auxiliaryPrice/queryAuxiliaryPriceList")
    ItemResult<PageData<AuxiliaryPriceListDTO>> queryAuxiliaryPriceList(@RequestBody PageQuery<AuxiliaryPriceDTO> var1);
}

