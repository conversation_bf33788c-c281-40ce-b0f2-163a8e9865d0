
package com.ecommerce.logistics.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.billEvaluate.BillEvaluateListDTO;
import com.ecommerce.logistics.api.dto.billEvaluate.BillEvaluateQueryDTO;
import com.ecommerce.logistics.api.dto.billEvaluate.BillEvaluateSaveDTO;
import com.ecommerce.logistics.api.dto.billEvaluate.BillEvaluateStatisticListDTO;
import com.ecommerce.logistics.api.dto.billEvaluate.BillEvaluateStatisticListQueryDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "logistics")
public interface IBillEvaluateService {
    @PostMapping("/billEvaluate/addBillEvaluate")
    public ItemResult<String> addBillEvaluate(@RequestBody BillEvaluateSaveDTO var1);

    @PostMapping("/billEvaluate/updateAllRankCache")
    public ItemResult<Void> updateAllRankCache();

    @PostMapping("/billEvaluate/selectEvaluateStatisticList")
    public ItemResult<PageData<BillEvaluateStatisticListDTO>> selectEvaluateStatisticList(@RequestBody PageQuery<BillEvaluateStatisticListQueryDTO> var1);

    @PostMapping("/billEvaluate/queryBillEvaluateList")
    public ItemResult<PageData<BillEvaluateListDTO>> queryBillEvaluateList(@RequestBody PageQuery<BillEvaluateQueryDTO> var1);
}

