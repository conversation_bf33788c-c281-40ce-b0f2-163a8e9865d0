
package com.ecommerce.logistics.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.breakdto.BreakBillDTO;
import com.ecommerce.logistics.api.dto.breakdto.BreakDTO;
import com.ecommerce.logistics.api.param.breakparam.BreakAgreeParam;
import com.ecommerce.logistics.api.param.breakparam.BreakBatchCancelParam;
import com.ecommerce.logistics.api.param.breakparam.BreakBillQueryParam;
import com.ecommerce.logistics.api.param.breakparam.BreakFollowParam;
import com.ecommerce.logistics.api.param.breakparam.BreakParam;
import com.ecommerce.logistics.api.param.breakparam.BreakQueryParam;
import com.ecommerce.logistics.api.param.breakparam.BreakRejectParam;
import com.ecommerce.logistics.api.param.breakparam.BreakResponsibleParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "logistics")
public interface IBreakService {
    @PostMapping("/break/pageByQueryOption")
    public ItemResult<PageData<BreakDTO>> pageByQueryOption(@RequestBody PageQuery<BreakQueryParam> var1);

    @PostMapping("/break/batchCancelBreak")
    public ItemResult<Boolean> batchCancelBreak(@RequestBody BreakBatchCancelParam var1);

    @PostMapping("/break/rejectBreak")
    public ItemResult<Boolean> rejectBreak(@RequestBody BreakRejectParam var1);

    @PostMapping("/break/addOrUpdateBreak")
    public ItemResult<Boolean> addOrUpdateBreak(@RequestBody BreakParam var1);

    @PostMapping("/break/responsibleBreak")
    public ItemResult<Boolean> responsibleBreak(@RequestBody BreakResponsibleParam var1);

    @PostMapping("/break/agreeBreak")
    public ItemResult<Boolean> agreeBreak(@RequestBody BreakAgreeParam var1);

    @PostMapping("/break/followBreak")
    public ItemResult<Boolean> followBreak(@RequestBody BreakFollowParam var1);

    @PostMapping("/break/queryBreakBill")
    public ItemResult<BreakBillDTO> queryBreakBill(BreakBillQueryParam var1);
}

