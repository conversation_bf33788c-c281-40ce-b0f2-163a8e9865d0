
package com.ecommerce.logistics.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.carriage.CarriageRouteDeleteDTO;
import com.ecommerce.logistics.api.dto.carriage.CarriageRouteDetailDTO;
import com.ecommerce.logistics.api.dto.carriage.CarriageRouteListDTO;
import com.ecommerce.logistics.api.dto.carriage.CarriageRouteQueryDTO;
import com.ecommerce.logistics.api.dto.carriage.CarriageRouteSaveDTO;
import com.ecommerce.logistics.api.dto.carriage.TransportRouteDTO;
import com.ecommerce.logistics.api.dto.carriage.TransportRouteQueryDTO;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "logistics")
public interface ICarriageRouteService {
    @PostMapping("/carriageRoute/enteringCarriageRoute")
    public ItemResult<String> enteringCarriageRoute(@RequestBody CarriageRouteSaveDTO var1);

    @PostMapping("/carriageRoute/deleteCarriageRoute")
    public ItemResult<Void> deleteCarriageRoute(@RequestBody CarriageRouteDeleteDTO var1);

    @PostMapping("/carriageRoute/editCarriageRoute")
    public ItemResult<String> editCarriageRoute(@RequestBody CarriageRouteSaveDTO var1);

    @PostMapping("/carriageRoute/queryCarriageRouteDetail")
    public ItemResult<CarriageRouteDetailDTO> queryCarriageRouteDetail(@RequestParam(value = "carriageRouteId") String var1);

    @PostMapping("/carriageRoute/queryCarriageRouteList")
    public ItemResult<PageData<CarriageRouteListDTO>> queryCarriageRouteList(@RequestBody PageQuery<CarriageRouteQueryDTO> var1);

    @PostMapping("/carriageRoute/queryERPTransportRoute")
    public ItemResult<List<TransportRouteDTO>> queryERPTransportRoute(@RequestBody TransportRouteQueryDTO var1);

    @PostMapping("/carriageRoute/hasERPContractAddress")
    public boolean hasERPContractAddress(@RequestBody CarriageRouteQueryDTO var1);

    @PostMapping("/carriageRoute/getERPContractAddress")
    public TransportRouteDTO getERPContractAddress(@RequestBody CarriageRouteQueryDTO var1);

    @PostMapping("/carriageRoute/hasCarriageRoute")
    public boolean hasCarriageRoute(@RequestBody CarriageRouteQueryDTO var1);
}

