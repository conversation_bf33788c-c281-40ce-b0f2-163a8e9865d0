
package com.ecommerce.logistics.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.carriage.CarriageLogListDTO;
import com.ecommerce.logistics.api.dto.carriage.CarriageRuleDeleteDTO;
import com.ecommerce.logistics.api.dto.carriage.CarriageRuleQueryDTO;
import com.ecommerce.logistics.api.dto.carriage.CarriageRuleSaveDTO;
import com.ecommerce.logistics.api.dto.carriagerule.BuyerCarriageRuleDeleteDTO;
import com.ecommerce.logistics.api.dto.carriagerule.BuyerCarriageRuleDetailDTO;
import com.ecommerce.logistics.api.dto.carriagerule.BuyerCarriageRuleListDTO;
import com.ecommerce.logistics.api.dto.carriagerule.BuyerCarriageRuleQueryDTO;
import com.ecommerce.logistics.api.dto.carriagerule.BuyerCarriageRuleSaveDTO;
import com.ecommerce.logistics.api.dto.carriagerule.CarrierCarriageRuleDeleteDTO;
import com.ecommerce.logistics.api.dto.carriagerule.CarrierCarriageRuleDetailDTO;
import com.ecommerce.logistics.api.dto.carriagerule.CarrierCarriageRuleListDTO;
import com.ecommerce.logistics.api.dto.carriagerule.CarrierCarriageRuleQueryDTO;
import com.ecommerce.logistics.api.dto.carriagerule.CarrierCarriageRuleSaveDTO;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name="logistics")
public interface ICarriageRuleService {
    @PostMapping( path={"/carriageRule/addCarrierCarriageRule"}, consumes={"application/json"})
    public ItemResult<String> addCarrierCarriageRule(@RequestBody CarrierCarriageRuleSaveDTO var1);

    @PostMapping( path={"/carriageRule/deleteCarrierCarriageRule"}, consumes={"application/json"})
    public ItemResult<Void> deleteCarrierCarriageRule(@RequestBody CarrierCarriageRuleDeleteDTO var1);

    @PostMapping( path={"/carriageRule/editCarrierCarriageRule"}, consumes={"application/json"})
    public ItemResult<Void> editCarrierCarriageRule(@RequestBody CarrierCarriageRuleSaveDTO var1);

    @PostMapping( path={"/carriageRule/queryCarrierCarriageRuleDetail"}, consumes={"application/json"})
    public ItemResult<CarrierCarriageRuleDetailDTO> queryCarrierCarriageRuleDetail(@RequestParam(value="carriageRuleId") String var1);

    @PostMapping( path={"/carriageRule/queryCarrierCarriageRuleList"}, consumes={"application/json"})
    public ItemResult<PageData<CarrierCarriageRuleListDTO>> queryCarrierCarriageRuleList(@RequestBody PageQuery<CarrierCarriageRuleQueryDTO> var1);

    @PostMapping( path={"/carriageRule/addBuyerCarriageRule"}, consumes={"application/json"})
    public ItemResult<String> addBuyerCarriageRule(@RequestBody BuyerCarriageRuleSaveDTO var1);

    @PostMapping( path={"/carriageRule/deleteBuyerCarriageRule"}, consumes={"application/json"})
    public ItemResult<Void> deleteBuyerCarriageRule(@RequestBody BuyerCarriageRuleDeleteDTO var1);

    @PostMapping( path={"/carriageRule/editBuyerCarriageRule"}, consumes={"application/json"})
    public ItemResult<Void> editBuyerCarriageRule(@RequestBody BuyerCarriageRuleSaveDTO var1);

    @PostMapping( path={"/carriageRule/queryBuyerCarriageRuleDetail"}, consumes={"application/json"})
    public ItemResult<BuyerCarriageRuleDetailDTO> queryBuyerCarriageRuleDetail(@RequestParam(value="carriageRuleId") String var1);

    @PostMapping( path={"/carriageRule/queryBuyerCarriageRuleList"}, consumes={"application/json"})
    public ItemResult<PageData<BuyerCarriageRuleListDTO>> queryBuyerCarriageRuleList(@RequestBody PageQuery<BuyerCarriageRuleQueryDTO> var1);

    @PostMapping( path={"/carriageRule/enterCarriageRule"}, consumes={"application/json"})
    public ItemResult<List<String>> enterCarriageRule(@RequestBody CarriageRuleSaveDTO<Object> var1);

    @PostMapping( path={"/carriageRule/deleteCarriageRule"}, consumes={"application/json"})
    public ItemResult<Void> deleteCarriageRule(@RequestBody CarriageRuleDeleteDTO var1);

    @PostMapping( path={"/carriageRule/editCarriageRule"}, consumes={"application/json"})
    public ItemResult<Void> editCarriageRule(@RequestBody CarriageRuleSaveDTO<Object> var1);

    @PostMapping( path={"/carriageRule/queryCarriageRuleList"}, consumes={"application/json"})
    public ItemResult<List<Object>> queryCarriageRuleList(@RequestBody CarriageRuleQueryDTO var1);

    @PostMapping( path={"/carriageRule/queryCarriageLogList"}, consumes={"application/json"})
    public ItemResult<PageData<CarriageLogListDTO>> queryCarriageLogList(@RequestBody PageQuery<CarriageRuleQueryDTO> var1);

    @PostMapping( path={"/carriageRule/queryCarriageLogListOneYear"}, consumes={"application/json"})
    public ItemResult<List<CarriageLogListDTO>> queryCarriageLogListOneYear(@RequestBody CarriageRuleQueryDTO var1);
}

