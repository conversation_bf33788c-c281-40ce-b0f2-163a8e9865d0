
package com.ecommerce.logistics.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.intelligentDispatch.CarrierParkingLotDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name="logistics")
public interface ICarrierParkingLotService {
    @PostMapping( path={"/carrierParkingLot/add"}, consumes={"application/json"})
    public ItemResult<Boolean> add(@RequestBody CarrierParkingLotDTO var1);

    @PostMapping( path={"/carrierParkingLot/edit"}, consumes={"application/json"})
    public ItemResult<Boolean> edit(@RequestBody CarrierParkingLotDTO var1);

    @PostMapping( path={"/carrierParkingLot/delete"}, consumes={"application/json"})
    public ItemResult<Boolean> delete(@RequestBody CarrierParkingLotDTO var1);

    @PostMapping( path={"/carrierParkingLot/pageCarrierParkingLot"}, consumes={"application/json"})
    public ItemResult<PageData<CarrierParkingLotDTO>> pageCarrierParkingLot(@RequestBody PageQuery<CarrierParkingLotDTO> var1);
}

