
package com.ecommerce.logistics.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.intelligentDispatch.CarrierServiceAreaDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name="logistics")
public interface ICarrierServiceAreaService {
    @PostMapping( path={"/carrierServiceArea/add"}, consumes={"application/json"})
    public ItemResult<Boolean> add(@RequestBody CarrierServiceAreaDTO var1);

    @PostMapping( path={"/carrierServiceArea/edit"}, consumes={"application/json"})
    public ItemResult<Boolean> edit(@RequestBody CarrierServiceAreaDTO var1);

    @PostMapping( path={"/carrierServiceArea/delete"}, consumes={"application/json"})
    public ItemResult<Boolean> delete(@RequestBody CarrierServiceAreaDTO var1);

    @PostMapping( path={"/carrierServiceArea/pageCarrierServiceArea"}, consumes={"application/json"})
    public ItemResult<PageData<CarrierServiceAreaDTO>> pageCarrierServiceArea(@RequestBody PageQuery<CarrierServiceAreaDTO> var1);

    @PostMapping( path={"/carrierServiceArea/queryDetailById"}, consumes={"application/json"})
    public ItemResult<CarrierServiceAreaDTO> queryDetailById(@RequestParam(value="serviceAreaId") String var1);
}

