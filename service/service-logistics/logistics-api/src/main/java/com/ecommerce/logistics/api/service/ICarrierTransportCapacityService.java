
package com.ecommerce.logistics.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.intelligentDispatch.CarrierTransportCapacityDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name="logistics")
public interface ICarrierTransportCapacityService {
    @PostMapping( path={"/carrierTransportCapacity/add"}, consumes={"application/json"})
    public ItemResult<Boolean> add(@RequestBody CarrierTransportCapacityDTO var1);

    @PostMapping( path={"/carrierTransportCapacity/edit"}, consumes={"application/json"})
    public ItemResult<Boolean> edit(@RequestBody CarrierTransportCapacityDTO var1);

    @PostMapping( path={"/carrierTransportCapacity/delete"}, consumes={"application/json"})
    public ItemResult<Boolean> delete(@RequestBody CarrierTransportCapacityDTO var1);

    @PostMapping( path={"/carrierTransportCapacity/pageCarrierTransportCapacity"}, consumes={"application/json"})
    public ItemResult<PageData<CarrierTransportCapacityDTO>> pageCarrierTransportCapacity(@RequestBody PageQuery<CarrierTransportCapacityDTO> var1);
}

