
package com.ecommerce.logistics.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.complaint.ComplaintDTO;
import com.ecommerce.logistics.api.param.complaint.ComplaintBatchCancelParam;
import com.ecommerce.logistics.api.param.complaint.ComplaintBatchDelParam;
import com.ecommerce.logistics.api.param.complaint.ComplaintDealFinishParam;
import com.ecommerce.logistics.api.param.complaint.ComplaintEvaluationParam;
import com.ecommerce.logistics.api.param.complaint.ComplaintFollowParam;
import com.ecommerce.logistics.api.param.complaint.ComplaintParam;
import com.ecommerce.logistics.api.param.complaint.ComplaintQueryParam;
import com.ecommerce.logistics.api.param.complaint.ComplaintResponsibleParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name="logistics")
public interface IComplaintService {
    @PostMapping( path={"/complaint/followComplaint"}, consumes={"application/json"})
    public ItemResult<Boolean> followComplaint(@RequestBody ComplaintFollowParam var1);

    @PostMapping( path={"/complaint/addOrUpdateComplaint"}, consumes={"application/json"})
    public ItemResult<Boolean> addOrUpdateComplaint(@RequestBody ComplaintParam var1);

    @PostMapping( path={"/complaint/batchDelComplaint"}, consumes={"application/json"})
    public ItemResult<Boolean> batchDelComplaint(@RequestBody ComplaintBatchDelParam var1);

    @PostMapping( path={"/complaint/batchCancelComplaint"}, consumes={"application/json"})
    public ItemResult<Boolean> batchCancelComplaint(@RequestBody ComplaintBatchCancelParam var1);

    @PostMapping( path={"/complaint/dealFinishComplaint"}, consumes={"application/json"})
    public ItemResult<Boolean> dealFinishComplaint(@RequestBody ComplaintDealFinishParam var1);

    @PostMapping( path={"/complaint/responsibleComplaint"}, consumes={"application/json"})
    public ItemResult<Boolean> responsibleComplaint(@RequestBody ComplaintResponsibleParam var1);

    @PostMapping( path={"/complaint/pageByQueryOption"}, consumes={"application/json"})
    public ItemResult<PageData<ComplaintDTO>> pageByQueryOption(@RequestBody PageQuery<ComplaintQueryParam> var1);

    @PostMapping( path={"/complaint/evaluationComplaint"}, consumes={"application/json"})
    public ItemResult<Boolean> evaluationComplaint(@RequestBody ComplaintEvaluationParam var1);
}