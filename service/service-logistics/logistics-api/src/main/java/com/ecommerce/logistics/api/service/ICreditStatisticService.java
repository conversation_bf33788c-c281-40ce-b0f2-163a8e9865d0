
package com.ecommerce.logistics.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.creditStatistic.CreditStatisticAddDTO;
import com.ecommerce.logistics.api.dto.creditStatistic.CreditStatisticDTO;
import com.ecommerce.logistics.api.dto.creditStatistic.CreditStatisticListDTO;
import com.ecommerce.logistics.api.dto.creditStatistic.CreditStatisticQueryDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name="logistics")
public interface ICreditStatisticService {
    @PostMapping( path={"/creditStatistic/addCreditStatistic"}, consumes={"application/json"})
    public ItemResult<String> addCreditStatistic(@RequestBody CreditStatisticAddDTO var1);

    @PostMapping( path={"/creditStatistic/updateCreditStatisticCount"}, consumes={"application/json"})
    public ItemResult<Void> updateCreditStatisticCount(@RequestParam(value="personId") String var1, @RequestParam(value="billCount") Boolean var2, @RequestParam(value="breakCount") Boolean var3, @RequestParam(value="complaintCount") Boolean var4, @RequestParam(value="operatorUserId") String var5);

    @PostMapping( path={"/creditStatistic/findCreditStatisticById"}, consumes={"application/json"})
    public ItemResult<CreditStatisticDTO> findCreditStatisticById(@RequestParam(value="personId") String var1, @RequestParam(value="personType") String var2);

    @PostMapping( path={"/creditStatistic/queryCreditStatisticList"}, consumes={"application/json"})
    public ItemResult<PageData<CreditStatisticListDTO>> queryCreditStatisticList(@RequestBody PageQuery<CreditStatisticQueryDTO> var1);
}

