
package com.ecommerce.logistics.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.DeliveryDetailDTO;
import com.ecommerce.logistics.api.dto.DeliveryListDTO;
import com.ecommerce.logistics.api.dto.DeliveryQueryDTO;
import com.ecommerce.logistics.api.dto.DeliveryRerouteCondDTO;
import com.ecommerce.logistics.api.dto.DeliveryRerouteDTO;
import com.ecommerce.logistics.api.dto.DeliveryRerouteInfoDTO;
import com.ecommerce.logistics.api.dto.DeliverySimpleDTO;
import com.ecommerce.logistics.api.dto.DeliverySimpleQueryDTO;
import com.ecommerce.logistics.api.dto.DeliveryStatisticsDTO;
import com.ecommerce.logistics.api.dto.DeliveryStatusChangeDTO;
import com.ecommerce.logistics.api.dto.EnteringNormalDeliveryBillDTO;
import com.ecommerce.logistics.api.dto.EnteringProxyDeliveryBillDTO;
import com.ecommerce.logistics.api.dto.MergeDeliveryAssignDTO;
import com.ecommerce.logistics.api.dto.PayableCarriagePriceQueryDTO;
import com.ecommerce.logistics.api.dto.QueryUrgentTaskDTO;
import com.ecommerce.logistics.api.dto.WaitMergeDeliveryQueryDTO;
import com.ecommerce.logistics.api.dto.WarehouseChangeDTO;
import com.ecommerce.logistics.api.dto.assign.AssignShipDTO;
import com.ecommerce.logistics.api.dto.assign.AssignVehicleDTO;
import com.ecommerce.logistics.api.dto.auto.DispatchResultOperationDTO;
import com.ecommerce.logistics.api.dto.auto.InstantAutoDispatchCondDTO;
import com.ecommerce.logistics.api.dto.auto.PlanAutoDispatchCondDTO;
import com.ecommerce.logistics.api.dto.auto.VehicleAutoAssignDTO;
import com.ecommerce.logistics.api.dto.auto.VehicleAutoAssignQueryDTO;
import com.ecommerce.logistics.api.dto.auto.WaitPlanAutoDispatchDTO;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.pickingbill.AssignDetailDTO;
import com.ecommerce.logistics.api.dto.pickingbill.CloseDeliverySheetDTO;
import com.ecommerce.logistics.api.dto.pickingbill.OptionDTO;
import com.ecommerce.logistics.api.dto.waybill.ContractSubmitInfoDTO;
import java.math.BigDecimal;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
@FeignClient(name ="logistics")
public interface IDeliveryBillService {


    @PostMapping(path = "/deliveryBill/assignVehicle", consumes = "application/json")
    public ItemResult<Void> assignVehicle(@RequestBody List<AssignVehicleDTO> assignVehicleDTOList,@RequestParam("deliveryBillId") String deliveryBillId,@RequestParam("operationUserId") String operationUserId,@RequestParam("operationUserName") String operationUserName);


    @PostMapping(path = "/deliveryBill/assignShip", consumes = "application/json")
    public ItemResult<Void> assignShip(@RequestBody List<AssignShipDTO> assignShipDTOList,@RequestParam("deliveryBillId") String deliveryBillId,@RequestParam("operationUserId") String operationUserId,@RequestParam("operationUserName") String operationUserName);


    @PostMapping(path = "/deliveryBill/assignCarrier", consumes = "application/json")
    public ItemResult<Void> assignCarrier(@RequestBody List<AssignDetailDTO> assignCarrierDTOList,@RequestParam("deliveryBillId") String deliveryBillId,@RequestParam("operationUserId") String operationUserId,@RequestParam("operationUserName") String operationUserName);


    @PostMapping(path = "/deliveryBill/enteringNormalDeliveryBill", consumes = "application/json")
    public ItemResult<Void> enteringNormalDeliveryBill(@RequestBody EnteringNormalDeliveryBillDTO enteringNormalDeliveryBillDTO);


    @PostMapping(path = "/deliveryBill/enteringProxyDeliveryBill", consumes = "application/json")
    public ItemResult<Void> enteringProxyDeliveryBill(@RequestBody EnteringProxyDeliveryBillDTO enteringProxyDeliveryBillDTO);


    @PostMapping(path = "/deliveryBill/queryDeliveryList", consumes = "application/json")
    public ItemResult<PageData<DeliveryListDTO>> queryDeliveryList(@RequestBody PageQuery<DeliveryQueryDTO> pageQuery);

    @PostMapping(path = "/deliveryBill/queryWaitMergeDeliveryList", consumes = "application/json")
    public ItemResult<PageData<DeliveryListDTO>> queryWaitMergeDeliveryList(@RequestBody PageQuery<WaitMergeDeliveryQueryDTO> pageQuery);

    @PostMapping(path = "/deliveryBill/mergeAssign", consumes = "application/json")
    public ItemResult<Void> mergeAssign(@RequestBody MergeDeliveryAssignDTO mergeDeliveryAssignDTO);


    @PostMapping(path = "/deliveryBill/queryDeliveryDetailById", consumes = "application/json")
    public ItemResult<DeliveryDetailDTO> queryDeliveryDetailById(@RequestParam("deliveryBillId") String deliveryBillId);


    @PostMapping(path = "/deliveryBill/changeDeliveryStatus", consumes = "application/json")
    public ItemResult<Void> changeDeliveryStatus(@RequestBody DeliveryStatusChangeDTO deliveryStatusChangeDTO);

    @PostMapping(path = "/deliveryBill/batchCloseDeliverySheet", consumes = "application/json")
    public ItemResult<Void> batchCloseDeliverySheet(@RequestBody CloseDeliverySheetDTO closeDeliverySheetDTO);

    @PostMapping(path = "/deliveryBill/queryPayableCarriagePrice", consumes = "application/json")
    public ItemResult<BigDecimal> queryPayableCarriagePrice(@RequestBody PayableCarriagePriceQueryDTO queryDTO);

    @PostMapping(path = "/deliveryBill/reroute", consumes = "application/json")
    public ItemResult<Void> reroute(@RequestBody DeliveryRerouteDTO deliveryRerouteDTO);

    @PostMapping(path = "/deliveryBill/queryCanRerouteInfo", consumes = "application/json")
    public ItemResult<DeliveryRerouteInfoDTO> queryCanRerouteInfo(@RequestBody DeliveryRerouteCondDTO deliveryRerouteCondDTO);

    @PostMapping(path = "/deliveryBill/queryMemberInfoForCheckRule", consumes = "application/json")
    public ItemResult<List<DeliverySimpleDTO>> queryMemberInfoForCheckRule(@RequestBody DeliverySimpleQueryDTO deliverySimpleQueryDTO);

    @PostMapping(path = "/deliveryBill/statisticsDeliveryForTheSomeDay", consumes = "application/json")
    public ItemResult<DeliveryStatisticsDTO> statisticsDeliveryForTheSomeDay(DeliveryQueryDTO dto);

    @PostMapping(path = "/deliveryBill/statisticsDeliveryForTheSomeMonth", consumes = "application/json")
    public ItemResult<DeliveryStatisticsDTO> statisticsDeliveryForTheSomeMonth(DeliveryQueryDTO dto);

    @PostMapping(path = "/deliveryBill/queryUrgentTaskList", consumes = "application/json")
    public ItemResult<List<DeliveryListDTO>> queryUrgentTaskList(@RequestBody QueryUrgentTaskDTO query);

    @PostMapping(path = "/deliveryBill/instantVehicleDispatch", consumes = "application/json")
    public ItemResult<List<VehicleAutoAssignDTO>> instantVehicleDispatch(@RequestBody InstantAutoDispatchCondDTO instantAutoDispatchCondDTO);

    @PostMapping(path = "/deliveryBill/createAutoDispatchBatchId", consumes = "application/json")
    public ItemResult<String> createAutoDispatchBatchId(@RequestBody List<String> deliveryBillIdList);

    @PostMapping(path = "/deliveryBill/queryWaitAutoDispatchByBatchId", consumes = "application/json")
    public ItemResult<WaitPlanAutoDispatchDTO> queryWaitAutoDispatchByBatchId(@RequestParam("batchId") String batchId);

    @PostMapping(path = "/deliveryBill/planVehicleDispatch", consumes = "application/json")
    public ItemResult<Void> planVehicleDispatch(@RequestBody PlanAutoDispatchCondDTO planAutoDispatchCondDTO);

    @PostMapping(path = "/deliveryBill/queryAutoWaitAssignList", consumes = "application/json")
    public ItemResult<PageData<VehicleAutoAssignDTO>> queryAutoWaitAssignList(@RequestBody PageQuery<VehicleAutoAssignQueryDTO> vehicleAutoAssignQueryDTO);

    @PostMapping(path = "/deliveryBill/warehouseOptionsByBatchId", consumes = "application/json")
    public ItemResult<List<OptionDTO>> warehouseOptionsByBatchId(@RequestParam("batchId") String batchId);

    @PostMapping(path = "/deliveryBill/receiverAddressOptionsByBatchId", consumes = "application/json")
    public ItemResult<List<OptionDTO>> receiverAddressOptionsByBatchId(@RequestParam("batchId") String batchId);

    @PostMapping(path = "/deliveryBill/confirmAutoDispatch", consumes = "application/json")
    public ItemResult<Void> confirmAutoDispatch(@RequestBody DispatchResultOperationDTO dispatchResultOperationDTO);

    @PostMapping(path = "/deliveryBill/rejectAutoDispatch", consumes = "application/json")
    public ItemResult<Void> rejectAutoDispatch(@RequestBody DispatchResultOperationDTO dispatchResultOperationDTO);

    @PostMapping(path = "/deliveryBill/queryContractInfo", consumes = "application/json")
    public ItemResult<ContractSubmitInfoDTO> queryContractInfo(@RequestParam("deliveryBillNum") String deliveryBillNum);

    @PostMapping(path = "/deliveryBill/changeDeliveryWarehouse", consumes = "application/json")
    public ItemResult<Void> changeDeliveryWarehouse(@RequestBody WarehouseChangeDTO warehouseChangeDTO);
}