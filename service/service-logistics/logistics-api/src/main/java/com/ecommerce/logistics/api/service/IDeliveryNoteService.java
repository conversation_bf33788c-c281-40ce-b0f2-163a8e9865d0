
package com.ecommerce.logistics.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.delivery.DeliveryNoteAddDTO;
import com.ecommerce.logistics.api.dto.delivery.DeliveryNoteDetailDTO;
import com.ecommerce.logistics.api.dto.delivery.DeliveryNoteDetailQueryDTO;
import com.ecommerce.logistics.api.dto.delivery.DeliveryNoteUnloadDTO;
import com.ecommerce.logistics.api.dto.delivery.DeliveryNoteLeaveDTO;
import com.ecommerce.logistics.api.dto.delivery.DeliveryNoteEditDTO;
import com.ecommerce.logistics.api.dto.delivery.DeliveryNoteListDTO;
import com.ecommerce.logistics.api.dto.delivery.DeliveryNoteQueryDTO;
import com.ecommerce.logistics.api.dto.delivery.DeliveryNoteSignDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @Version:2
 * @Description::送货单服务
*/

@FeignClient(name="logistics")
public interface IDeliveryNoteService {
    @PostMapping( path={"/deliveryNote/leaveWarehouse"}, consumes={"application/json"})
    public ItemResult<Void> leaveWarehouse(@RequestBody DeliveryNoteLeaveDTO var1);

    @PostMapping( path={"/deliveryNote/editDeliveryNote"}, consumes={"application/json"})
    public ItemResult<Void> editDeliveryNote(@RequestBody DeliveryNoteEditDTO var1);

    @PostMapping( path={"/deliveryNote/queryDetail"}, consumes={"application/json"})
    public ItemResult<DeliveryNoteDetailDTO> queryDetail(@RequestBody DeliveryNoteDetailQueryDTO var1);

    @PostMapping( path={"/deliveryNote/queryList"}, consumes={"application/json"})
    public ItemResult<PageData<DeliveryNoteListDTO>> queryList(@RequestBody PageQuery<DeliveryNoteQueryDTO> var1);

    @PostMapping( path={"/deliveryNote/signDeliveryNote"}, consumes={"application/json"})
    public ItemResult<Void> signDeliveryNote(@RequestBody DeliveryNoteSignDTO var1);

    @PostMapping( path={"/deliveryNote/createDeliveryNote"}, consumes={"application/json"})
    public ItemResult<String> createDeliveryNote(@RequestBody DeliveryNoteAddDTO var1);

    @PostMapping( path={"/deliveryNote/unloadDeliveryNote"}, consumes={"application/json"})
    public ItemResult<Void> unloadDeliveryNote(@RequestBody DeliveryNoteUnloadDTO var1);
}

