
package com.ecommerce.logistics.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.dispatchbill.CarrierListDTO;
import com.ecommerce.logistics.api.dto.dispatchbill.DispatchAssignDetailDTO;
import com.ecommerce.logistics.api.dto.dispatchbill.DispatchBillAssignAddDTO;
import com.ecommerce.logistics.api.dto.dispatchbill.DispatchBillDTO;
import com.ecommerce.logistics.api.dto.dispatchbill.DispatchBillListDTO;
import com.ecommerce.logistics.api.dto.dispatchbill.DispatchBillListQueryDTO;
import com.ecommerce.logistics.api.dto.dispatchbill.ExportDispatchBillDTO;
import com.ecommerce.logistics.api.dto.dispatchbill.TransportCapacityDTO;
import com.ecommerce.logistics.api.dto.dispatchbill.TransportCapacityQueryDTO;
import com.ecommerce.logistics.api.dto.pickingbill.PickingBillAssignDispatchDTO;
import com.ecommerce.logistics.api.dto.waybill.CarrierDispatchBillListDTO;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name="logistics")
public interface IDispatchBillService {
    @PostMapping( path={"/dispatchBill/getDispatchBillDetail"}, consumes={"application/json"})
    public ItemResult<DispatchBillDTO> getDispatchBillDetail(@RequestParam(value="dispatchBillId") String var1);

    @PostMapping( path={"/dispatchBill/cancelDispatchBill"}, consumes={"application/json"})
    public ItemResult<Void> cancelDispatchBill(@RequestParam(value="dispatchBillId") String var1, @RequestParam(value="operatorId") String var2, @RequestParam(value="operatorName") String var3);

    @PostMapping( path={"/dispatchBill/queryDispatchBillList"}, consumes={"application/json"})
    public ItemResult<PageData<DispatchBillListDTO>> queryDispatchBillList(@RequestBody PageQuery<DispatchBillListQueryDTO> var1);

    @PostMapping( path={"/dispatchBill/assignDispatchBill"}, consumes={"application/json"})
    public ItemResult<Void> assignDispatchBill(@RequestBody DispatchBillAssignAddDTO var1);

    @PostMapping( path={"/dispatchBill/batchAssignDispatchBill"}, consumes={"application/json"})
    public ItemResult<Void> batchAssignDispatchBill(@RequestBody List<DispatchBillAssignAddDTO> var1);

    @PostMapping( path={"/dispatchBill/queryCarrierTransportCapacity"}, consumes={"application/json"})
    public ItemResult<List<TransportCapacityDTO>> queryCarrierTransportCapacity(@RequestBody TransportCapacityQueryDTO var1);

    @PostMapping( path={"/dispatchBill/queryListByPickingBillId"}, consumes={"application/json"})
    public ItemResult<List<PickingBillAssignDispatchDTO>> queryListByPickingBillId(@RequestParam(value="pickingBillId") String var1);

    @PostMapping( path={"/dispatchBill/getDispatchAssignDetail"}, consumes={"application/json"})
    public ItemResult<DispatchAssignDetailDTO> getDispatchAssignDetail(@RequestParam(value="dispatchBillId") String var1);

    @PostMapping( path={"/dispatchBill/queryCarrierDispatchBillList"}, consumes={"application/json"})
    public ItemResult<PageData<CarrierDispatchBillListDTO>> queryCarrierDispatchBillList(@RequestBody PageQuery<DispatchBillListQueryDTO> var1);

    @PostMapping( path={"/dispatchBill/queryCarrierListLikeName"}, consumes={"application/json"})
    public ItemResult<List<CarrierListDTO>> queryCarrierListLikeName(@RequestParam(value="name") String var1);

    @PostMapping( path={"/dispatchBill/downloadDispatchBillList"}, consumes={"application/json"})
    public ItemResult<List<ExportDispatchBillDTO>> downloadDispatchBillList(@RequestBody DispatchBillListQueryDTO var1);
}

