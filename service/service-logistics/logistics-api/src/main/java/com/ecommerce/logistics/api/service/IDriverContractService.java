
package com.ecommerce.logistics.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.contract.DriverContractAddDTO;
import com.ecommerce.logistics.api.dto.contract.DriverContractListDTO;
import com.ecommerce.logistics.api.dto.contract.DriverContractListQueryDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name="logistics")
public interface IDriverContractService {
    @PostMapping( path={"/driverContract/addDriverContract"}, consumes={"application/json"})
    public ItemResult<String> addDriverContract(@RequestBody DriverContractAddDTO var1);

    @PostMapping( path={"/driverContract/queryDriverContractList"}, consumes={"application/json"})
    public ItemResult<PageData<DriverContractListDTO>> queryDriverContractList(@RequestBody PageQuery<DriverContractListQueryDTO> var1);

    @PostMapping( path={"/driverContract/reportDriverContract"}, consumes={"application/json"})
    public ItemResult<Void> reportDriverContract(@RequestParam(value="contractIds") String var1, @RequestParam(value="operatorUserId") String var2, @RequestParam(value="operatorUserName") String var3);
}

