
package com.ecommerce.logistics.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.dry.season.policy.DrySeasonPolicyCreateDTO;
import com.ecommerce.logistics.api.dto.dry.season.policy.DrySeasonPolicyDTO;
import com.ecommerce.logistics.api.dto.dry.season.policy.DrySeasonPolicyDeleteDTO;
import com.ecommerce.logistics.api.dto.dry.season.policy.DrySeasonPolicyListDTO;
import com.ecommerce.logistics.api.dto.dry.season.policy.DrySeasonPolicyQueryDTO;
import com.ecommerce.logistics.api.dto.dry.season.policy.DrySeasonPolicyUpdateDTO;
import com.ecommerce.logistics.api.dto.dry.season.policy.DrySeasonPolicyWaterLevelTemplateDTO;
import com.ecommerce.logistics.api.dto.dry.season.policy.DrySeasonPolicyWaterLevelTemplateEditDTO;
import com.ecommerce.logistics.api.dto.dry.season.policy.DrySeasonPolicyWaterLevelTemplateQueryDTO;
import com.ecommerce.logistics.api.dto.dry.season.policy.PolicyShippingRouteRelationAddDTO;
import com.ecommerce.logistics.api.dto.dry.season.policy.PolicyShippingRouteRelationRemoveDTO;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = "logistics")
public interface IDrySeasonPolicyService {
    @Operation(summary="添加策略")
    @PostMapping( path={"/drySeasonPolicy/addPolicy"}, consumes={"application/json"})
    public ItemResult<Boolean> addPolicy(@RequestBody DrySeasonPolicyCreateDTO var1);

    @Operation(summary="更新策略(含枯水期信息、有效时间段、航运线路信息)")
    @PostMapping( path={"/drySeasonPolicy/updatePolicy"}, consumes={"application/json"})
    public ItemResult<Boolean> updatePolicy(@RequestBody DrySeasonPolicyUpdateDTO var1);

    @Operation(summary="添加策略-线路绑定(航运线路编辑使用)")
    @PostMapping( path={"/drySeasonPolicy/updatePolicyShippingRouteRelation"}, consumes={"application/json"})
    public ItemResult<Boolean> updatePolicyShippingRouteRelation(@RequestBody PolicyShippingRouteRelationAddDTO var1);

    @PostMapping( path={"/drySeasonPolicy/addPolicyShippingRouteRelation"}, consumes={"application/json"})
    public ItemResult<Boolean> addPolicyShippingRouteRelation(@RequestBody PolicyShippingRouteRelationAddDTO var1);

    @Operation(summary="移除策略-线路绑定(航运线路编辑使用)")
    @PostMapping( path={"/drySeasonPolicy/removePolicyShippingRouteRelation"}, consumes={"application/json"})
    public ItemResult<Boolean> removePolicyShippingRouteRelation(@RequestBody PolicyShippingRouteRelationRemoveDTO var1);

    @Operation(summary="删除策略")
    @PostMapping( path={"/drySeasonPolicy/deletePolicy"}, consumes={"application/json"})
    public ItemResult<Boolean> deletePolicy(@RequestBody DrySeasonPolicyDeleteDTO var1);

    @Operation(summary="翻页查询策略")
    @PostMapping( path={"/drySeasonPolicy/pageInfoPolicy"}, consumes={"application/json"})
    public ItemResult<PageInfo<DrySeasonPolicyListDTO>> pageInfoPolicy(@RequestBody PageQuery<DrySeasonPolicyQueryDTO> var1);

    @Operation(summary="根据id查询策略")
    @PostMapping( path={"/drySeasonPolicy/findById"}, consumes={"application/json"})
    public ItemResult<DrySeasonPolicyDTO> findById(@RequestParam(value="id") String var1);

    @Operation(summary="根据航运线路id查询策略")
    @PostMapping( path={"/drySeasonPolicy/findByShippingRouteId"}, consumes={"application/json"})
    public ItemResult<List<DrySeasonPolicyDTO>> findByShippingRouteId(@RequestParam(value="shippingRouteId") String var1);

    @Operation(summary="查询水位系数表母版")
    @PostMapping( path={"/drySeasonPolicy/queryWaterLevelTemplate"}, consumes={"application/json"})
    public ItemResult<DrySeasonPolicyWaterLevelTemplateDTO> queryWaterLevelTemplate(@RequestBody DrySeasonPolicyWaterLevelTemplateQueryDTO var1);

    @Operation(summary="编辑修改水位系数表母版")
    @PostMapping( path={"/drySeasonPolicy/editWaterLevelTemplate"}, consumes={"application/json"})
    public ItemResult<Boolean> editWaterLevelTemplate(@RequestBody DrySeasonPolicyWaterLevelTemplateEditDTO var1);
}

