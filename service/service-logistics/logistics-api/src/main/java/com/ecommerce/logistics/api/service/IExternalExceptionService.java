
package com.ecommerce.logistics.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.externalexception.ExternalExceptionListDTO;
import com.ecommerce.logistics.api.dto.externalexception.ExternalExceptionQueryDTO;
import com.ecommerce.logistics.api.dto.externalexception.ExternalExceptionRetryDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name="logistics")
public interface IExternalExceptionService {
    @PostMapping( path={"/externalException/queryExternalExceptionList"}, consumes={"application/json"})
    public ItemResult<PageData<ExternalExceptionListDTO>> queryExternalExceptionList(@RequestBody PageQuery<ExternalExceptionQueryDTO> var1);

    @PostMapping( path={"/externalException/externalExceptionRetryHandler"}, consumes={"application/json"})
    public ItemResult<Void> externalExceptionRetryHandler(@RequestBody ExternalExceptionRetryDTO var1);
}

