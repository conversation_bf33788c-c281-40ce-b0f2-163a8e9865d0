
package com.ecommerce.logistics.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.gps.GpsManCondDTO;
import com.ecommerce.logistics.api.dto.gps.GpsManufacturerAddDTO;
import com.ecommerce.logistics.api.dto.gps.GpsManufacturerDTO;
import com.ecommerce.logistics.api.dto.gps.GpsManufacturerDeleteDTO;
import com.ecommerce.logistics.api.dto.gps.GpsManufacturerOptionDTO;
import com.ecommerce.logistics.api.dto.gps.GpsManufacturerUpdateDTO;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name="logistics")
public interface IGpsManufacturerService {
    @PostMapping( path={"/gpsManufacturer/queryGpsOptions"}, consumes={"application/json"})
    public ItemResult<List<GpsManufacturerOptionDTO>> queryGpsOptions();

    @PostMapping( path={"/gpsManufacturer/addGpsManufacturer"}, consumes={"application/json"})
    public ItemResult<String> addGpsManufacturer(@RequestBody GpsManufacturerAddDTO var1);

    @PostMapping( path={"/gpsManufacturer/removeGpsManufacturerById"}, consumes={"application/json"})
    public ItemResult<Void> removeGpsManufacturerById(@RequestBody GpsManufacturerDeleteDTO var1);

    @PostMapping( path={"/gpsManufacturer/queryAllGpsMan"}, consumes={"application/json"})
    public ItemResult<List<GpsManufacturerDTO>> queryAllGpsMan();

    @PostMapping( path={"/gpsManufacturer/queryGpsManufacturerById"}, consumes={"application/json"})
    public ItemResult<GpsManufacturerDTO> queryGpsManufacturerById(@RequestParam(value="gpsManufacturerId") String var1);

    @PostMapping( path={"/gpsManufacturer/queryGpsManufacturerByNumber"}, consumes={"application/json"})
    public ItemResult<GpsManufacturerDTO> queryGpsManufacturerByNumber(@RequestParam(value="number") String var1);

    @PostMapping( path={"/gpsManufacturer/modifyGpsManById"}, consumes={"application/json"})
    public ItemResult<Void> modifyGpsManById(@RequestBody GpsManufacturerUpdateDTO var1);

    @PostMapping( path={"/gpsManufacturer/queryGpsManByCond"}, consumes={"application/json"})
    public ItemResult<PageData<GpsManufacturerDTO>> queryGpsManByCond(@RequestBody PageQuery<GpsManCondDTO> var1);
}

