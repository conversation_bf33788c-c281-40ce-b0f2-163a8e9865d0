
package com.ecommerce.logistics.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.logistics.api.dto.intelligentDispatch.CarrierInfoDTO;
import com.ecommerce.logistics.api.dto.intelligentDispatch.CarrierParamDTO;
import com.ecommerce.logistics.api.dto.intelligentDispatch.ChangeRuleStatusDTO;
import com.ecommerce.logistics.api.dto.intelligentDispatch.IntelligentDispatchListDTO;
import com.ecommerce.logistics.api.dto.intelligentDispatch.IntelligentDispatchQueryDTO;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name="logistics")
public interface IIntelligentDispatchService {
    @PostMapping( path={"/intelligentDispatch/queryRuleList"}, consumes={"application/json"})
    public ItemResult<List<IntelligentDispatchListDTO>> queryRuleList(@RequestBody IntelligentDispatchQueryDTO var1);

    @PostMapping( path={"/intelligentDispatch/disableRuleStatus"}, consumes={"application/json"})
    public ItemResult<Void> disableRuleStatus(@RequestBody ChangeRuleStatusDTO var1);

    @PostMapping( path={"/intelligentDispatch/enableRuleStatus"}, consumes={"application/json"})
    public ItemResult<Void> enableRuleStatus(@RequestBody ChangeRuleStatusDTO var1);

    @PostMapping( path={"/intelligentDispatch/entrustCarrier"}, consumes={"application/json"})
    public ItemResult<CarrierInfoDTO> entrustCarrier(@RequestBody CarrierParamDTO var1);
}

