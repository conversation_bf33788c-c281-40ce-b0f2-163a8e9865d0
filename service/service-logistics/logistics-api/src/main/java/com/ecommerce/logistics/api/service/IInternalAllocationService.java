
package com.ecommerce.logistics.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.ReceiveAddressDTO;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.delivery.InternalAllocationDTO;
import com.ecommerce.logistics.api.dto.waybill.AllocationListQueryDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name="logistics")
public interface IInternalAllocationService {
    @PostMapping( path={"/internalAllocation/queryInternalAllocationList"}, consumes={"application/json"})
    public ItemResult<PageData<InternalAllocationDTO>> queryInternalAllocationList(@RequestBody PageQuery<AllocationListQueryDTO> var1);

    @PostMapping( path={"/internalAllocation/getInternalAllocation"}, consumes={"application/json"})
    public ItemResult<InternalAllocationDTO> getInternalAllocation(InternalAllocationDTO var1);

    @PostMapping( path={"/internalAllocation/saveInternalAllocation"}, consumes={"application/json"})
    public ItemResult<ReceiveAddressDTO> saveInternalAllocation(InternalAllocationDTO var1);

    @PostMapping( path={"/internalAllocation/completeShipBill"}, consumes={"application/json"})
    public ItemResult<Void> completeShipBill(@RequestBody InternalAllocationDTO var1);

    @PostMapping( path={"/internalAllocation/cancelShipBill"}, consumes={"application/json"})
    public ItemResult<Void> cancelShipBill(@RequestBody InternalAllocationDTO var1);
}

