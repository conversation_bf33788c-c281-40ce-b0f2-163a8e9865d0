
package com.ecommerce.logistics.api.service;

import com.ecommerce.common.result.ItemResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name="logistics")
public interface ILgsCarrySubmitService {
    @PostMapping( path={"/lgsCarrySubmit/generateCarryShipNote"}, consumes={"application/json"})
    public ItemResult<String> generateCarryShipNote(@RequestParam(value="waybillNum") String var1, @RequestParam(value="operatorId") String var2);

    @PostMapping( path={"/lgsCarrySubmit/generateFinanceFlow"}, consumes={"application/json"})
    public ItemResult<String> generateFinanceFlow(@RequestParam(value="waybillNum") String var1, @RequestParam(value="operatorId") String var2);

    @PostMapping( path={"/lgsCarrySubmit/generateContract"}, consumes={"application/json"})
    public ItemResult<String> generateContract(@RequestParam(value="contractType") Integer var1, @RequestParam(value="number") String var2, @RequestParam(value="operatorId") String var3);
}

