
package com.ecommerce.logistics.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.invoice.ConfirmInvoiceReqDTO;
import com.ecommerce.logistics.api.dto.invoice.InvoiceBillCondDTO;
import com.ecommerce.logistics.api.dto.invoice.InvoiceBillListDTO;
import com.ecommerce.logistics.api.dto.invoice.PreviewInvoiceApplyReqDTO;
import com.ecommerce.logistics.api.dto.invoice.PreviewInvoiceApplyResDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name="logistics")
public interface ILgsElectricInvoiceService {
    @PostMapping( path={"/lgsElectricInvoice/queryLgsWaybillsForInvoice"}, consumes={"application/json"})
    public ItemResult<PageData<InvoiceBillListDTO>> queryLgsWaybillsForInvoice(@RequestBody PageQuery<InvoiceBillCondDTO> var1);

    @PostMapping( path={"/lgsElectricInvoice/applyLgsWaybillsInvoice"}, consumes={"application/json"})
    public ItemResult<PreviewInvoiceApplyResDTO> applyLgsWaybillsInvoice(@RequestBody PreviewInvoiceApplyReqDTO var1);

    @PostMapping( path={"/lgsElectricInvoice/batchConfirmInvoiceApply"}, consumes={"application/json"})
    public ItemResult<Void> batchConfirmInvoiceApply(@RequestBody ConfirmInvoiceReqDTO var1);
}

