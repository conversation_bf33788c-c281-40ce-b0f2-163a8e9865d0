
package com.ecommerce.logistics.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.intelligentDispatch.LoadingTimeDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name="logistics")
public interface ILoadingTimeService {
    @PostMapping( path={"/loadingTime/add"}, consumes={"application/json"})
    public ItemResult<Boolean> add(@RequestBody LoadingTimeDTO var1);

    @PostMapping( path={"/loadingTime/edit"}, consumes={"application/json"})
    public ItemResult<Boolean> edit(@RequestBody LoadingTimeDTO var1);

    @PostMapping( path={"/loadingTime/delete"}, consumes={"application/json"})
    public ItemResult<Boolean> delete(@RequestBody LoadingTimeDTO var1);

    @PostMapping( path={"/loadingTime/pageLoadingTime"}, consumes={"application/json"})
    public ItemResult<PageData<LoadingTimeDTO>> pageLoadingTime(@RequestBody PageQuery<LoadingTimeDTO> var1);
}

