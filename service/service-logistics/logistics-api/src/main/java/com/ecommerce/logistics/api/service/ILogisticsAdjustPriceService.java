
package com.ecommerce.logistics.api.service;

import com.ecommerce.logistics.api.dto.LogisticsAdjustPriceDTO;
import com.ecommerce.logistics.api.dto.LogisticsAdjustPriceMemberDTO;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name="logistics")
public interface ILogisticsAdjustPriceService {
    @PostMapping( path={"/logisticsAdjustPrice/queryLogisticsAdjustPriceMemberList"}, consumes={"application/json"})
    public List<LogisticsAdjustPriceMemberDTO> queryLogisticsAdjustPriceMemberList(@RequestBody LogisticsAdjustPriceDTO var1);
}

