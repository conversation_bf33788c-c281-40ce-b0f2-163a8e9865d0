
package com.ecommerce.logistics.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.logistics.api.dto.operationrecord.OperationRecordAddDTO;
import com.ecommerce.logistics.api.dto.operationrecord.OperationRecordDTO;
import com.ecommerce.logistics.api.dto.operationrecord.OperationRecordListDTO;
import com.ecommerce.logistics.api.dto.operationrecord.QueryWaybillOperationDTO;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 操作记录服务
 */

@FeignClient(name = "logistics")
public interface IOperationRecordService {
    @PostMapping(path = {"/operationRecord/queryWaybillOperation"}, consumes = {"application/json"})
    public ItemResult<List<OperationRecordDTO>> queryWaybillOperation(@RequestBody QueryWaybillOperationDTO var1);

    @PostMapping(path = {"/operationRecord/addOperationRecord"}, consumes = {"application/json"})
    public ItemResult<Void> addOperationRecord(@RequestBody OperationRecordAddDTO var1);

    @PostMapping(path = {"/operationRecord/queryOperationRecordList"}, consumes = {"application/json"})
    public ItemResult<List<OperationRecordListDTO>> queryOperationRecordList(@RequestParam(value = "entryId") String var1);
}

