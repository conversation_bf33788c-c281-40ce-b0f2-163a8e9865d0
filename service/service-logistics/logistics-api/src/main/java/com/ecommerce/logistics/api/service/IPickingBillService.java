
package com.ecommerce.logistics.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.pickingbill.AssignDispatchBillDTO;
import com.ecommerce.logistics.api.dto.pickingbill.AssignWaybillDTO;
import com.ecommerce.logistics.api.dto.pickingbill.CancelDeliverySheetDTO;
import com.ecommerce.logistics.api.dto.pickingbill.CancelPickingBillDTO;
import com.ecommerce.logistics.api.dto.pickingbill.CancelQuantityDTO;
import com.ecommerce.logistics.api.dto.pickingbill.ChangePickingBillWarehouseDTO;
import com.ecommerce.logistics.api.dto.pickingbill.CloseDeliverySheetDTO;
import com.ecommerce.logistics.api.dto.pickingbill.ClosePickingBillDTO;
import com.ecommerce.logistics.api.dto.pickingbill.EnteringPickingBillDTO;
import com.ecommerce.logistics.api.dto.pickingbill.ExpirePickingBillDTO;
import com.ecommerce.logistics.api.dto.pickingbill.OptionDTO;
import com.ecommerce.logistics.api.dto.pickingbill.PickingBillDTO;
import com.ecommerce.logistics.api.dto.pickingbill.PickingBillExportDTO;
import com.ecommerce.logistics.api.dto.pickingbill.PickingBillListDTO;
import com.ecommerce.logistics.api.dto.pickingbill.PickingBillListQueryDTO;
import com.ecommerce.logistics.api.dto.pickingbill.StatisticsPickingBillStatusDTO;
import com.ecommerce.logistics.api.dto.pickingbill.VendorEntrustmentDTO;
import java.util.List;
import java.util.Map;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name="logistics")
public interface IPickingBillService {
    @PostMapping( path={"/pickingBill/assignCreateWaybill"}, consumes={"application/json"})
    public ItemResult<Void> assignCreateWaybill(@RequestBody AssignWaybillDTO var1);

    @PostMapping( path={"/pickingBill/assignVehicleCreateWaybill"}, consumes={"application/json"})
    public ItemResult<Void> assignVehicleCreateWaybill(@RequestBody AssignWaybillDTO var1);

    @PostMapping( path={"/pickingBill/changePickingBillWarehouse"}, consumes={"application/json"})
    public ItemResult<Void> changePickingBillWarehouse(@RequestBody ChangePickingBillWarehouseDTO var1);

    @PostMapping( path={"/pickingBill/enteringPickingBill"}, consumes={"application/json"})
    public ItemResult<String> enteringPickingBill(@RequestBody EnteringPickingBillDTO var1);

    @PostMapping( path={"/pickingBill/vendorEntrustmentPickingBill"}, consumes={"application/json"})
    public ItemResult<Void> vendorEntrustmentPickingBill(@RequestBody VendorEntrustmentDTO var1);

    @PostMapping( path={"/pickingBill/cancelPickingBill"}, consumes={"application/json"})
    public ItemResult<CancelQuantityDTO> cancelPickingBill(@RequestBody CancelPickingBillDTO var1);

    @PostMapping( path={"/pickingBill/batchCloseDeliverySheet"}, consumes={"application/json"})
    public ItemResult<Void> batchCloseDeliverySheet(@RequestBody CloseDeliverySheetDTO var1);

    @PostMapping( path={"/pickingBill/assignCreateDispatchBill"}, consumes={"application/json"})
    public ItemResult<Void> assignCreateDispatchBill(@RequestBody AssignDispatchBillDTO var1);

    @PostMapping( path={"/pickingBill/queryPickingBillList"}, consumes={"application/json"})
    public ItemResult<PageData<PickingBillListDTO>> queryPickingBillList(@RequestBody PageQuery<PickingBillListQueryDTO> var1);

    @PostMapping( path={"/pickingBill/getPickingBillDetail"}, consumes={"application/json"})
    public ItemResult<PickingBillDTO> getPickingBillDetail(@RequestParam(value="pickingBillId") String var1);

    @PostMapping( path={"/pickingBill/cancelDeliverySheet"}, consumes={"application/json"})
    public ItemResult<CancelQuantityDTO> cancelDeliverySheet(@RequestBody CancelDeliverySheetDTO var1);

    @PostMapping( path={"/pickingBill/getSellerOptions"}, consumes={"application/json"})
    public ItemResult<List<OptionDTO>> getSellerOptions(@RequestParam(value="name") String var1);

    @PostMapping( path={"/pickingBill/closePickingBill"}, consumes={"application/json"})
    public ItemResult<Void> closePickingBill(@RequestBody ClosePickingBillDTO var1);

    @PostMapping( path={"/pickingBill/getBuyerOptions"}, consumes={"application/json"})
    public ItemResult<List<OptionDTO>> getBuyerOptions(@RequestParam(value="name") String var1);

    @PostMapping( path={"/pickingBill/statisticsPickingBillStatus"}, consumes={"application/json"})
    public ItemResult<Map<String, StatisticsPickingBillStatusDTO>> statisticsPickingBillStatus(@RequestBody PickingBillListQueryDTO var1);

    @PostMapping( path={"/pickingBill/expirePickingBill"}, consumes={"application/json"})
    public ItemResult<Void> expirePickingBill(@RequestBody ExpirePickingBillDTO var1);

    @PostMapping( path={"/pickingBill/exportPickingBill"}, consumes={"application/json"})
    public List<PickingBillExportDTO> exportPickingBill(@RequestBody PickingBillListQueryDTO var1);

    @PostMapping( path={"/pickingBill/enteringSelfTakePickingBill"}, consumes={"application/json"})
    public ItemResult<String> enteringSelfTakePickingBill(@RequestBody EnteringPickingBillDTO var1);
}

