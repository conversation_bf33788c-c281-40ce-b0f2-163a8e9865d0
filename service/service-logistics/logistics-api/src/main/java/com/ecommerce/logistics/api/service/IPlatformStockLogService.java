
package com.ecommerce.logistics.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.storehouse.TSSellerViewItem;
import com.ecommerce.logistics.api.dto.storehouse.TurnoverProductViewDTO;
import com.ecommerce.logistics.api.dto.storehouse.TurnoverQueryDTO;
import com.ecommerce.logistics.api.dto.storehouse.TurnoverSellerViewDTO;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name="logistics")
public interface IPlatformStockLogService {
    @PostMapping( path={"/platformStockLog/sellerViewTurnover"}, consumes={"application/json"})
    public ItemResult<PageData<TurnoverSellerViewDTO>> sellerViewTurnover(@RequestBody PageQuery<TurnoverQueryDTO> var1);

    @PostMapping( path={"/platformStockLog/productViewTurnover"}, consumes={"application/json"})
    public ItemResult<PageData<TurnoverProductViewDTO>> productViewTurnover(@RequestBody PageQuery<TurnoverQueryDTO> var1);

    @PostMapping( path={"/platformStockLog/turnoverForSeller"}, consumes={"application/json"})
    public ItemResult<PageData<TSSellerViewItem>> turnoverForSeller(@RequestBody PageQuery<TurnoverQueryDTO> var1);

    @PostMapping( path={"/platformStockLog/queryTurnoverForSeller"}, consumes={"application/json"})
    public ItemResult<List<TSSellerViewItem>> queryTurnoverForSeller(@RequestBody TurnoverQueryDTO var1);

    @PostMapping( path={"/platformStockLog/queryTurnoverForPlatform"}, consumes={"application/json"})
    public ItemResult<List<TurnoverSellerViewDTO>> queryTurnoverForPlatform(@RequestBody TurnoverQueryDTO var1);
}

