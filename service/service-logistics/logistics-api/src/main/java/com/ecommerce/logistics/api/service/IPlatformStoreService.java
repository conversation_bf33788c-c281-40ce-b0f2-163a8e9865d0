
package com.ecommerce.logistics.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.storehouse.PlatformStockListDTO;
import com.ecommerce.logistics.api.dto.storehouse.PlatformStockQueryDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name="logistics")
public interface IPlatformStoreService {
    @PostMapping( path={"/platformStore/queryPlatformStockList"}, consumes={"application/json"})
    public ItemResult<PageData<PlatformStockListDTO>> queryPlatformStockList(@RequestBody PageQuery<PlatformStockQueryDTO> var1);
}

