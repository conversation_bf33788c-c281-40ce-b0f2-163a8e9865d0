
package com.ecommerce.logistics.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.intelligentDispatch.PorterEditDTO;
import com.ecommerce.logistics.api.dto.intelligentDispatch.PorterListDTO;
import com.ecommerce.logistics.api.dto.intelligentDispatch.PorterQueryDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name="logistics")
public interface IPorterService {
    @PostMapping( path={"/porter/addPorter"}, consumes={"application/json"})
    public ItemResult<Void> addPorter(@RequestBody PorterEditDTO var1);

    @PostMapping( path={"/porter/delPorter"}, consumes={"application/json"})
    public ItemResult<Void> delPorter(@RequestBody PorterEditDTO var1);

    @PostMapping( path={"/porter/queryPorterList"}, consumes={"application/json"})
    public ItemResult<PageData<PorterListDTO>> queryPorterList(@RequestBody PageQuery<PorterQueryDTO> var1);

    @PostMapping( path={"/porter/editPorter"}, consumes={"application/json"})
    public ItemResult<Void> editPorter(@RequestBody PorterEditDTO var1);
}

