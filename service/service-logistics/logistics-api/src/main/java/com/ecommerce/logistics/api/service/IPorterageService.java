
package com.ecommerce.logistics.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.porterage.PorterageComputeDTO;
import com.ecommerce.logistics.api.dto.porterage.PorterageComputeResultDTO;
import com.ecommerce.logistics.api.dto.porterage.PorterageDetailDTO;
import com.ecommerce.logistics.api.dto.porterage.PorterageDetailQueryDTO;
import com.ecommerce.logistics.api.dto.porterage.PorterageListDTO;
import com.ecommerce.logistics.api.dto.porterage.PorterageListQueryDTO;
import com.ecommerce.logistics.api.dto.porterage.PorterageSaveDTO;
import com.ecommerce.logistics.api.dto.porterage.PorterageSearchDTO;
import com.ecommerce.logistics.api.dto.porterage.PorterageSearchResultDTO;
import com.ecommerce.logistics.api.dto.porterage.PorterageZoneListDTO;
import com.ecommerce.logistics.api.dto.porterage.PorterageZoneQueryDTO;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name="logistics")
public interface IPorterageService {
    @PostMapping( path={"/porterage/addPorterageRule"}, consumes={"application/json"})
    public ItemResult<String> addPorterageRule(@RequestBody PorterageSaveDTO var1);

    @PostMapping( path={"/porterage/computePorterage"}, consumes={"application/json"})
    public ItemResult<PorterageComputeResultDTO> computePorterage(@RequestBody PorterageComputeDTO var1);

    @PostMapping( path={"/porterage/deletePorterageRule"}, consumes={"application/json"})
    public ItemResult<String> deletePorterageRule(@RequestParam(value="porterageRuleId") String var1);

    @PostMapping( path={"/porterage/queryPorterageRuleList"}, consumes={"application/json"})
    public ItemResult<PageData<PorterageListDTO>> queryPorterageRuleList(@RequestBody PageQuery<PorterageListQueryDTO> var1);

    @PostMapping( path={"/porterage/queryZonePorterageList"}, consumes={"application/json"})
    public ItemResult<List<PorterageZoneListDTO>> queryZonePorterageList(@RequestBody PorterageZoneQueryDTO var1);

    @PostMapping( path={"/porterage/editPorterageRule"}, consumes={"application/json"})
    public ItemResult<Void> editPorterageRule(@RequestBody PorterageSaveDTO var1);

    @PostMapping( path={"/porterage/searchPorterageRuleList"}, consumes={"application/json"})
    public ItemResult<List<PorterageSearchResultDTO>> searchPorterageRuleList(@RequestBody PorterageSearchDTO var1);

    @PostMapping( path={"/porterage/queryPorterageDetail"}, consumes={"application/json"})
    public ItemResult<PorterageDetailDTO> queryPorterageDetail(@RequestBody PorterageDetailQueryDTO var1);
}

