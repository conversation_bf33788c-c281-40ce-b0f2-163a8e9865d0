
package com.ecommerce.logistics.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.logistics.api.dto.pickingbill.OptionDTO;
import com.ecommerce.logistics.api.dto.productinfo.ProductInfoDTO;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 商品信息服务
 */
@FeignClient(name="logistics")
public interface IProductInfoService {
    @PostMapping( path={"/productInfo/queryInfoLikeNote"}, consumes={"application/json"})
    public ItemResult<List<ProductInfoDTO>> queryInfoLikeNote(@RequestParam(value="note") String var1);

    @PostMapping( path={"/productInfo/queryOptionsByKeyWord"}, consumes={"application/json"})
    public ItemResult<List<OptionDTO>> queryOptionsByKeyWord(@RequestParam(value="keyWord") String var1);
}

