
package com.ecommerce.logistics.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.ProxySyncRecord.ProxySyncRecordListDTO;
import com.ecommerce.logistics.api.dto.ProxySyncRecord.ProxySyncRecordQueryDTO;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name="logistics")
public interface IProxySyncRecordService {
    @PostMapping( path={"/proxySyncRecord/syncByProxySyncIds"}, consumes={"application/json"})
    public ItemResult<Void> syncByProxySyncIds(@RequestBody List<String> var1);

    @PostMapping( path={"/proxySyncRecord/queryProxySyncRecordList"}, consumes={"application/json"})
    public ItemResult<PageData<ProxySyncRecordListDTO>> queryProxySyncRecordList(@RequestBody PageQuery<ProxySyncRecordQueryDTO> var1);
}

