
package com.ecommerce.logistics.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.logistics.api.dto.rulecompute.CarriageComputeDTO;
import com.ecommerce.logistics.api.dto.rulecompute.CarriageComputeResultDTO;
import com.ecommerce.logistics.api.dto.rulecompute.CarriageRuleResultDTO;
import com.ecommerce.logistics.api.dto.rulecompute.WaybillMergeDTO;
import java.math.BigDecimal;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name="logistics")
public interface IRuleComputeService {
    @PostMapping( path={"/ruleCompute/orderCarriageCompute"}, consumes={"application/json"})
    public ItemResult<List<CarriageComputeResultDTO>> orderCarriageCompute(@RequestBody CarriageComputeDTO var1);

    @PostMapping( path={"/ruleCompute/waybillCarriageCompute"}, consumes={"application/json"})
    public ItemResult<BigDecimal> waybillCarriageCompute(@RequestBody CarriageComputeDTO var1);

    @PostMapping( path={"/ruleCompute/waybillMerge"}, consumes={"application/json"})
    public ItemResult<String> waybillMerge(@RequestBody WaybillMergeDTO var1);

    @PostMapping( path={"/ruleCompute/queryCarriageRule"}, consumes={"application/json"})
    public ItemResult<List<CarriageRuleResultDTO>> queryCarriageRule(@RequestBody CarriageComputeDTO var1);
}

