
package com.ecommerce.logistics.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.logistics.api.dto.rule.CarriageRuleDTO;
import com.ecommerce.logistics.api.dto.rule.CarriageRuleListDTO;
import com.ecommerce.logistics.api.dto.rule.CarriageRuleListQueryDTO;
import com.ecommerce.logistics.api.dto.rule.MatchCarriageRuleDTO;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 规则服务
 */
@FeignClient(name="logistics")
public interface IRuleService {
    @PostMapping( path={"/rule/matchCarriageRule"}, consumes={"application/json"})
    public ItemResult<CarriageRuleDTO> matchCarriageRule(@RequestBody MatchCarriageRuleDTO var1);

    @PostMapping( path={"/rule/queryCarriageRuleList"}, consumes={"application/json"})
    public ItemResult<List<CarriageRuleListDTO>> queryCarriageRuleList(@RequestBody CarriageRuleListQueryDTO var1);

    @PostMapping( path={"/rule/removeCarriageRule"}, consumes={"application/json"})
    public ItemResult<Void> removeCarriageRule(@RequestParam(value="carriageRuleId") String var1);

    @PostMapping( path={"/rule/editCarriageRule"}, consumes={"application/json"})
    public ItemResult<Void> editCarriageRule(@RequestBody CarriageRuleDTO var1);

    @PostMapping( path={"/rule/addCarriageRule"}, consumes={"application/json"})
    public ItemResult<Void> addCarriageRule(@RequestBody CarriageRuleDTO var1);
}

