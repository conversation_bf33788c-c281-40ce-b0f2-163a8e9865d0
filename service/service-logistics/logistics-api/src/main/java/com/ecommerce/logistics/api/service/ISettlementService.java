
package com.ecommerce.logistics.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.settlement.CarriageSettlementDTO;
import com.ecommerce.logistics.api.dto.settlement.MonthSettlementListDTO;
import com.ecommerce.logistics.api.dto.settlement.MonthSettlementListQueryDTO;
import com.ecommerce.logistics.api.dto.settlement.SettlementItemListDTO;
import com.ecommerce.logistics.api.dto.settlement.SettlementItemListQueryDTO;
import com.ecommerce.logistics.api.dto.settlement.SettlementListDTO;
import com.ecommerce.logistics.api.dto.settlement.SettlementListQueryDTO;
import com.ecommerce.logistics.api.dto.settlement.UserSettlementAmountDTO;
import com.ecommerce.logistics.api.dto.settlement.UserSettlementListDTO;
import com.ecommerce.logistics.api.dto.settlement.UserSettlementListQueryDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 结算服务
 */
@FeignClient(name="logistics")
public interface ISettlementService {
    @PostMapping( path={"/settlement/userCarriageSettlement"}, consumes={"application/json"})
    public ItemResult<Void> userCarriageSettlement(@RequestBody CarriageSettlementDTO var1);

    @PostMapping( path={"/settlement/queryMonthSettlementList"}, consumes={"application/json"})
    public ItemResult<PageData<MonthSettlementListDTO>> queryMonthSettlementList(@RequestBody PageQuery<MonthSettlementListQueryDTO> var1);

    @PostMapping( path={"/settlement/querySettlementItemList"}, consumes={"application/json"})
    public ItemResult<PageData<SettlementItemListDTO>> querySettlementItemList(@RequestBody PageQuery<SettlementItemListQueryDTO> var1);

    @PostMapping( path={"/settlement/waybillCarriageSettlement"}, consumes={"application/json"})
    public ItemResult<Void> waybillCarriageSettlement(@RequestBody CarriageSettlementDTO var1);

    @PostMapping( path={"/settlement/queryUserSettlementAmount"}, consumes={"application/json"})
    public ItemResult<UserSettlementAmountDTO> queryUserSettlementAmount(@RequestParam(value="userId") String var1);

    @PostMapping( path={"/settlement/querySettlementList"}, consumes={"application/json"})
    public ItemResult<PageData<SettlementListDTO>> querySettlementList(@RequestBody PageQuery<SettlementListQueryDTO> var1);

    @PostMapping( path={"/settlement/queryUserSettlementList"}, consumes={"application/json"})
    public ItemResult<PageData<UserSettlementListDTO>> queryUserSettlementList(@RequestBody PageQuery<UserSettlementListQueryDTO> var1);
}

