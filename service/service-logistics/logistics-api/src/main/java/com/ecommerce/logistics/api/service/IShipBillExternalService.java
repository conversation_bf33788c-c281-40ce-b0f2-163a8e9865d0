
package com.ecommerce.logistics.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.logistics.api.dto.shipbill.ShipBillCloseERPCallbackDTO;
import com.ecommerce.logistics.api.dto.shipbill.ShipBillERPCallbackDTO;
import com.ecommerce.logistics.api.dto.shipbill.ShipBillModifyERPCallbackDTO;
import com.ecommerce.logistics.api.dto.waybill.ERPShipBillDTO;
import com.ecommerce.logistics.api.dto.waybill.ExternalWaybillRequestDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name="logistics")
public interface IShipBillExternalService {
    @PostMapping( path={"/shipBillExternal/erpNotifyPay"}, consumes={"application/json"})
    public ItemResult<ERPShipBillDTO> erpNotifyPay(@RequestBody ERPShipBillDTO var1);

    @PostMapping( path={"/shipBillExternal/updateECShipBill"}, consumes={"application/json"})
    public ItemResult<Void> updateECShipBill(@RequestBody ERPShipBillDTO var1);

    @PostMapping( path={"/shipBillExternal/notifyOpenCabin"}, consumes={"application/json"})
    public ItemResult<Void> notifyOpenCabin(@RequestParam(value="waybillId") String var1);

    @PostMapping( path={"/shipBillExternal/closeExternalShipBillCallback"}, consumes={"application/json"})
    public ItemResult<Void> closeExternalShipBillCallback(@RequestBody ItemResult<ShipBillCloseERPCallbackDTO> var1);

    @PostMapping( path={"/shipBillExternal/createExternalShipBillCallback"}, consumes={"application/json"})
    public ItemResult<Void> createExternalShipBillCallback(@RequestBody ItemResult<ShipBillERPCallbackDTO> var1);

    @PostMapping( path={"/shipBillExternal/closeExternalShipBill"}, consumes={"application/json"})
    public void closeExternalShipBill(@RequestBody ExternalWaybillRequestDTO var1);

    @PostMapping( path={"/shipBillExternal/modifyExternalShipBill"}, consumes={"application/json"})
    public void modifyExternalShipBill(@RequestBody ExternalWaybillRequestDTO var1);

    @PostMapping( path={"/shipBillExternal/openCabinCallback"}, consumes={"application/json"})
    public ItemResult<Void> openCabinCallback(@RequestBody ItemResult<ERPShipBillDTO> var1);

    @PostMapping( path={"/shipBillExternal/modifyExternalShipBillCallback"}, consumes={"application/json"})
    public ItemResult<Void> modifyExternalShipBillCallback(@RequestBody ItemResult<ShipBillModifyERPCallbackDTO> var1);

    @PostMapping( path={"/shipBillExternal/createExternalShipBill"}, consumes={"application/json"})
    public void createExternalShipBill(@RequestBody ExternalWaybillRequestDTO var1);
}

