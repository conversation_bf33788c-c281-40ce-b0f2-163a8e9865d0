
package com.ecommerce.logistics.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.DeliveryDetailVehicleDTO;
import com.ecommerce.logistics.api.dto.EmallPublishWaybillListDTO;
import com.ecommerce.logistics.api.dto.EmallPublishWaybillQueryDTO;
import com.ecommerce.logistics.api.dto.EstimateKmQueryDTO;
import com.ecommerce.logistics.api.dto.ForBidCloseWaybillCountQueryDTO;
import com.ecommerce.logistics.api.dto.GoodsInfoDTO;
import com.ecommerce.logistics.api.dto.LastSignWaybillQueryDTO;
import com.ecommerce.logistics.api.dto.ShipBillWaitRerouteListDTO;
import com.ecommerce.logistics.api.dto.WaitRerouteShipBillCondDTO;
import com.ecommerce.logistics.api.dto.WaybillBriefDTO;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.pickingbill.PickingBillDTO;
import com.ecommerce.logistics.api.dto.pickingbill.TwoLeaveOptionDTO;
import com.ecommerce.logistics.api.dto.shipbill.CancelShipBillDTO;
import com.ecommerce.logistics.api.dto.shipbill.CheckWaybillInfoDTO;
import com.ecommerce.logistics.api.dto.shipbill.CheckWaybillQueryDTO;
import com.ecommerce.logistics.api.dto.shipbill.CloseShipBillDTO;
import com.ecommerce.logistics.api.dto.shipbill.ConcreteShipBillInfoDTO;
import com.ecommerce.logistics.api.dto.shipbill.QrCodeDTO;
import com.ecommerce.logistics.api.dto.shipbill.ReassignShipBillDTO;
import com.ecommerce.logistics.api.dto.shipbill.ShipBillBriCondDTO;
import com.ecommerce.logistics.api.dto.shipbill.ShipBillBriResDTO;
import com.ecommerce.logistics.api.dto.shipbill.ShipBillDTO;
import com.ecommerce.logistics.api.dto.shipbill.ShipBillDetailDTO;
import com.ecommerce.logistics.api.dto.shipbill.ShipBillListDTO;
import com.ecommerce.logistics.api.dto.shipbill.ShipBillOrderDetailDTO;
import com.ecommerce.logistics.api.dto.shipbill.ShipBillQueryDTO;
import com.ecommerce.logistics.api.dto.shipbill.ShipWaybillListDTO;
import com.ecommerce.logistics.api.dto.shipbill.ShipWaybillQueryDTO;
import com.ecommerce.logistics.api.dto.shipbill.TransportInfoDTO;
import com.ecommerce.logistics.api.dto.warehouse.WarehouseOptionDTO;
import com.ecommerce.logistics.api.dto.waybill.AppWaybillQueryConditionsMapDTO;
import com.ecommerce.logistics.api.dto.waybill.AppWaybillStatisticsDTO;
import com.ecommerce.logistics.api.dto.waybill.ArriveWarehouseDTO;
import com.ecommerce.logistics.api.dto.waybill.BatchPassCheckDTO;
import com.ecommerce.logistics.api.dto.waybill.CarryWaybillSubmitDTO;
import com.ecommerce.logistics.api.dto.waybill.CheckWaybillListQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.CompleteShipBillDTO;
import com.ecommerce.logistics.api.dto.waybill.ContractSubmitInfoDTO;
import com.ecommerce.logistics.api.dto.waybill.DistrictListDTO;
import com.ecommerce.logistics.api.dto.waybill.DriverConfirmDTO;
import com.ecommerce.logistics.api.dto.waybill.DriverDeliveryDTO;
import com.ecommerce.logistics.api.dto.waybill.DriverWaybillAssignDTO;
import com.ecommerce.logistics.api.dto.waybill.DriverWaybillListQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.EnterFactoryDTO;
import com.ecommerce.logistics.api.dto.waybill.EvaluateWaybillListDTO;
import com.ecommerce.logistics.api.dto.waybill.LeaveWarehouseDTO;
import com.ecommerce.logistics.api.dto.waybill.OpenCabinShipBillDTO;
import com.ecommerce.logistics.api.dto.waybill.PassCheckDTO;
import com.ecommerce.logistics.api.dto.waybill.PublishWaybillListDTO;
import com.ecommerce.logistics.api.dto.waybill.QueryGoodsDTO;
import com.ecommerce.logistics.api.dto.waybill.RefundShipBillDTO;
import com.ecommerce.logistics.api.dto.waybill.SnatchWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.SnatchedWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.SnatchedWaybillQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.TradeWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.TradeWaybillDetailDTO;
import com.ecommerce.logistics.api.dto.waybill.UploadCertificateDTO;
import com.ecommerce.logistics.api.dto.waybill.UserSeckillWaybillListDTO;
import com.ecommerce.logistics.api.dto.waybill.UserSeckillWaybillQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.WaitCheckWaybillListDTO;
import com.ecommerce.logistics.api.dto.waybill.WarehouseAdminProccessedDTO;
import com.ecommerce.logistics.api.dto.waybill.WarehouseAdminProccessedQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.WarehouseAdminWaitDeliveryDTO;
import com.ecommerce.logistics.api.dto.waybill.WarehouseAdminWaitDeliveryQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillAssignDTO;
import io.swagger.v3.oas.annotations.Parameter;

import java.math.BigDecimal;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name="logistics")
public interface IShipBillService {
    @PostMapping( path={"/shipBill/queryShipBillList"}, consumes={"application/json"})
    public ItemResult<PageData<ShipBillListDTO>> queryShipBillList(@RequestBody PageQuery<ShipBillQueryDTO> var1);

    @PostMapping( path={"/shipBill/queryShipBillByDeliveryBillId"}, consumes={"application/json"})
    public ItemResult<List<DeliveryDetailVehicleDTO>> queryShipBillByDeliveryBillId(@RequestParam(value="deliveryBillId") String var1);

    @PostMapping( path={"/shipBill/inputLeaveWarehouse"}, consumes={"application/json"})
    public ItemResult<Void> inputLeaveWarehouse(@RequestBody LeaveWarehouseDTO var1);

    @PostMapping( path={"/shipBill/openCabin"}, consumes={"application/json"})
    public ItemResult<Boolean> openCabin(@RequestBody OpenCabinShipBillDTO var1);

    @PostMapping( path={"/shipBill/completeShipBill"}, consumes={"application/json"})
    public ItemResult<Void> completeShipBill(@RequestBody CompleteShipBillDTO var1);

    @GetMapping( path={"/shipBill/getWaybillDetail"}, consumes={"application/json"})
    public ItemResult<ShipBillDetailDTO> getWaybillDetail(@Parameter(description="运单子项ID") @RequestParam(value="waybillItemId") String var1);

    @GetMapping( path={"/shipBill/getWaybillDetailForDriverApp"}, consumes={"application/json"})
    public ItemResult<ShipBillDetailDTO> getWaybillDetailForDriverApp(@Parameter(description="运单Id") @RequestParam(value="waybillId") String var1);

    @PostMapping( path={"/shipBill/queryWaybillListByTakeCode"}, consumes={"application/json"})
    public ItemResult<List<ShipBillOrderDetailDTO>> queryWaybillListByTakeCode(@RequestParam(value="takeCodeList") List<String> var1);

    @PostMapping( path={"/shipBill/queryWaybillListByDeliveryNum"}, consumes={"application/json"})
    public ItemResult<List<TradeWaybillDTO>> queryWaybillListByDeliveryNum(@Parameter(description="发货单号") @RequestParam(value="deliverySheetNum") String var1);

    @PostMapping( path={"/shipBill/queryWaitRerouteShipBills"}, consumes={"application/json"})
    public ItemResult<PageData<ShipBillWaitRerouteListDTO>> queryWaitRerouteShipBills(@RequestBody PageQuery<WaitRerouteShipBillCondDTO> var1);

    @PostMapping( path={"/shipBill/cancelShipBill"}, consumes={"application/json"})
    public ItemResult<Void> cancelShipBill(@RequestBody CancelShipBillDTO var1);

    @PostMapping( path={"/shipBill/assignCanceledShipBill"}, consumes={"application/json"})
    public ItemResult<Void> assignCanceledShipBill(@RequestBody ReassignShipBillDTO var1);

    @PostMapping( path={"/shipBill/discardShipBill"}, consumes={"application/json"})
    public ItemResult<Void> discardShipBill(@RequestBody CloseShipBillDTO var1);

    @PostMapping( path={"/shipBill/statisticsShipBill"}, consumes={"application/json"})
    public ItemResult<AppWaybillStatisticsDTO> statisticsShipBill(@RequestBody ShipBillQueryDTO var1);

    @PostMapping( path={"/shipBill/selectShipBillQueryConditions"}, consumes={"application/json"})
    public ItemResult<AppWaybillQueryConditionsMapDTO> selectShipBillQueryConditions(@RequestBody ShipBillQueryDTO var1);

    @PostMapping( path={"/shipBill/uploadCertificate"}, consumes={"application/json"})
    public ItemResult<Void> uploadCertificate(@RequestBody UploadCertificateDTO var1);

    @PostMapping( path={"/shipBill/queryForbidCloseWaybillCountByTakeCode"}, consumes={"application/json"})
    public ItemResult<Integer> queryForbidCloseWaybillCountByTakeCode(@RequestBody ForBidCloseWaybillCountQueryDTO var1);

    @PostMapping({"/shipBill/queryForbidCloseWaybillListByTakeCode"})
    public ItemResult<List<ShipBillDTO>> queryForbidCloseWaybillListByTakeCode(@RequestBody ForBidCloseWaybillCountQueryDTO var1);

    @PostMapping( path={"/shipBill/queryTradeWaybillDetail"}, consumes={"application/json"})
    public ItemResult<TradeWaybillDetailDTO> queryTradeWaybillDetail(@RequestParam(value="waybillId") String var1);

    @PostMapping( path={"/shipBill/queryTradeWaybillDetailByNum"}, consumes={"application/json"})
    public ItemResult<TradeWaybillDetailDTO> queryTradeWaybillDetailByNum(@RequestParam(value="waybillNum") String var1);

    @PostMapping( path={"/shipBill/passCheck"}, consumes={"application/json"})
    public ItemResult<Void> passCheck(@RequestBody PassCheckDTO var1);

    @PostMapping( path={"/shipBill/batchPassCheck"}, consumes={"application/json"})
    public ItemResult<Void> batchPassCheck(@RequestBody BatchPassCheckDTO var1);

    @PostMapping( path={"/shipBill/queryCheckWaybillList"}, consumes={"application/json"})
    public ItemResult<PageData<WaitCheckWaybillListDTO>> queryCheckWaybillList(@RequestBody PageQuery<CheckWaybillListQueryDTO> var1);

    @PostMapping( path={"/shipBill/getWarehouseAdminWaitDelivery"}, consumes={"application/json"})
    public ItemResult<PageData<WarehouseAdminWaitDeliveryDTO>> getWarehouseAdminWaitDelivery(@RequestBody PageQuery<WarehouseAdminWaitDeliveryQueryDTO> var1);

    @PostMapping( path={"/shipBill/getWarehouseAdminProccessed"}, consumes={"application/json"})
    public ItemResult<PageData<WarehouseAdminProccessedDTO>> getWarehouseAdminProccessed(@RequestBody PageQuery<WarehouseAdminProccessedQueryDTO> var1);

    @PostMapping( path={"/shipBill/enterFactory"}, consumes={"application/json"})
    public ItemResult<Void> enterFactory(@RequestBody EnterFactoryDTO var1);

    @PostMapping( path={"/shipBill/withdrawEnterFactory"}, consumes={"application/json"})
    public ItemResult<Void> withdrawEnterFactory(@RequestBody EnterFactoryDTO var1);

    @PostMapping( path={"/shipBill/confirmWaybillByDriver"}, consumes={"application/json"})
    public ItemResult<Void> confirmWaybillByDriver(@RequestBody DriverConfirmDTO var1);

    @PostMapping( path={"/shipBill/rejectWaybillByDriver"}, consumes={"application/json"})
    public ItemResult<Void> rejectWaybillByDriver(@RequestBody DriverConfirmDTO var1);

    @PostMapping( path={"/shipBill/selectSeckillWarehouseByUserId"}, consumes={"application/json"})
    public ItemResult<List<WarehouseOptionDTO>> selectSeckillWarehouseByUserId(@RequestParam(value="userId") String var1);

    @PostMapping( path={"/shipBill/selectSeckillDistrictsByUserId"}, consumes={"application/json"})
    public ItemResult<List<DistrictListDTO>> selectSeckillDistrictsByUserId(@RequestParam(value="userId") String var1);

    @PostMapping( path={"/shipBill/selectUserSeckillWaybillList"}, consumes={"application/json"})
    public ItemResult<PageData<UserSeckillWaybillListDTO>> selectUserSeckillWaybillList(@RequestBody PageQuery<UserSeckillWaybillQueryDTO> var1);

    @PostMapping( path={"/shipBill/selectUserSeckilledWaybillList"}, consumes={"application/json"})
    public ItemResult<PageData<UserSeckillWaybillListDTO>> selectUserSeckilledWaybillList(@RequestBody PageQuery<DriverWaybillListQueryDTO> var1);

    @PostMapping( path={"/shipBill/snatchWaybill"}, consumes={"application/json"})
    public ItemResult<Void> snatchWaybill(@RequestBody SnatchWaybillDTO var1);

    @PostMapping( path={"/shipBill/changeArriveDestinationTime"}, consumes={"application/json"})
    public ItemResult<Void> changeArriveDestinationTime(@RequestParam(value="waybillId") String var1);

    @PostMapping( path={"/shipBill/arriveWarehouse"}, consumes={"application/json"})
    public ItemResult<Void> arriveWarehouse(@RequestBody ArriveWarehouseDTO var1);

    @PostMapping( path={"/shipBill/queryDriverDeliveryStatus"}, consumes={"application/json"})
    public ItemResult<DriverDeliveryDTO> queryDriverDeliveryStatus(@RequestParam(value="driverId") String var1);

    @PostMapping( path={"/shipBill/queryShipWaybillList"}, consumes={"application/json"})
    public ItemResult<PageData<ShipWaybillListDTO>> queryShipWaybillList(@RequestBody PageQuery<ShipWaybillQueryDTO> var1);

    @PostMapping( path={"/shipBill/assignWaybill"}, consumes={"application/json"})
    public ItemResult<Void> assignWaybill(@RequestBody WaybillAssignDTO var1);

    @PostMapping( path={"/shipBill/carrierAssignWaybillToDriver"}, consumes={"application/json"})
    public ItemResult<Void> carrierAssignWaybillToDriver(@RequestBody DriverWaybillAssignDTO var1);

    @PostMapping( path={"/shipBill/openCabinRemind"}, consumes={"application/json"})
    public ItemResult<Void> openCabinRemind(@RequestParam(value="waybillItemId") String var1);

    @PostMapping( path={"/shipBill/unloadingRemind"}, consumes={"application/json"})
    public ItemResult<Void> unloadingRemind(@RequestParam(value="waybillItemId") String var1);

    @PostMapping( path={"/shipBill/queryPickingListByWaybillNum"}, consumes={"application/json"})
    public ItemResult<List<PickingBillDTO>> queryPickingListByWaybillNum(@RequestBody List<String> var1);

    @PostMapping( path={"/shipBill/queryEvaluateWaybillList"}, consumes={"application/json"})
    public ItemResult<List<EvaluateWaybillListDTO>> queryEvaluateWaybillList(@Parameter(description="发货单号集合") @RequestBody List<String> var1);

    @PostMapping( path={"/shipBill/querySnatchedWaybillList"}, consumes={"application/json"})
    public ItemResult<List<SnatchedWaybillDTO>> querySnatchedWaybillList(@RequestBody SnatchedWaybillQueryDTO var1);

    @PostMapping( path={"/shipBill/selectWaybillForEmall"}, consumes={"application/json"})
    public ItemResult<List<EmallPublishWaybillListDTO>> selectWaybillForEmall(@RequestBody EmallPublishWaybillQueryDTO var1);

    @PostMapping( path={"/shipBill/selectWaybillForNum"}, consumes={"application/json"})
    public ItemResult<PublishWaybillListDTO> selectWaybillForNum(@Parameter(description="运单号") @RequestParam(value="waybillNum") String var1);

    @PostMapping( path={"/shipBill/estimateKm"}, consumes={"application/json"})
    public ItemResult<BigDecimal> estimateKm(@RequestBody EstimateKmQueryDTO var1);

    @PostMapping( path={"/shipBill/queryBillCheckWaybillInfoList"}, consumes={"application/json"})
    public ItemResult<PageData<CheckWaybillInfoDTO>> queryBillCheckWaybillInfoList(@RequestBody PageQuery<CheckWaybillQueryDTO> var1);

    @PostMapping( path={"/shipBill/queryConcreteInfoByWaybillId"}, consumes={"application/json"})
    public ConcreteShipBillInfoDTO queryConcreteInfoByWaybillId(@Parameter(description="运单ID") @RequestParam(value="waybillId") String var1);

    @PostMapping( path={"/shipBill/generateBillCheckWaybillInfoList"}, consumes={"application/json"})
    public ItemResult<Void> generateBillCheckWaybillInfoList(@Parameter(description="运单ID") @RequestParam(value="waybillId") String var1);

    @PostMapping( path={"/shipBill/againCreateExternalWaybill"}, consumes={"application/json"})
    public ItemResult<ShipBillListDTO> againCreateExternalWaybill(@Parameter(description="运单ID") @RequestParam(value="waybillId") String var1, @RequestParam(value="operationUserId") String var2, @RequestParam(value="operationUserName") String var3);

    @PostMapping( path={"/shipBill/selectCarryWaybill"}, consumes={"application/json"})
    public ItemResult<List<CarryWaybillSubmitDTO>> selectCarryWaybill(@Parameter(description="运单号") @RequestParam(value="waybillNum") String var1);

    @PostMapping( path={"/shipBill/queryPumpListByWaybillId"}, consumes={"application/json"})
    public ItemResult<TwoLeaveOptionDTO> queryPumpListByWaybillId(@Parameter(description="运单ID") @RequestParam(value="waybillId") String var1);

    @PostMapping( path={"/shipBill/queryPersonalDriverHalfWayWaybillNums"}, consumes={"application/json"})
    public ItemResult<List<String>> queryPersonalDriverHalfWayWaybillNums(@Parameter(description="个体司机MemberID") @RequestParam(value="personalDriverMemberId") String var1);

    @PostMapping( path={"/shipBill/queryContractInfo"}, consumes={"application/json"})
    public ItemResult<ContractSubmitInfoDTO> queryContractInfo(@RequestParam(value="waybillNum") String var1);

    @PostMapping( path={"/shipBill/refundShipBill"}, consumes={"application/json"})
    public ItemResult<Void> refundShipBill(@RequestBody RefundShipBillDTO var1);

    @PostMapping( path={"/shipBill/queryLastSignWaybill"}, consumes={"application/json"})
    public ItemResult<WaybillBriefDTO> queryLastSignWaybill(@RequestBody LastSignWaybillQueryDTO var1);

    @PostMapping( path={"/shipBill/queryGoodsNameListDropDownBox"}, consumes={"application/json"})
    public ItemResult<List<GoodsInfoDTO>> queryGoodsNameListDropDownBox(@RequestBody QueryGoodsDTO var1);

    @PostMapping( path={"/shipBill/queryTransportByWaybillNum"}, consumes={"application/json"})
    public ItemResult<TransportInfoDTO> queryTransportByWaybillNum(@RequestParam(value="waybillNum") String var1);

    @PostMapping( path={"/shipBill/queryShipBillBriByCond"}, consumes={"application/json"})
    public ShipBillBriResDTO queryShipBillBriByCond(@RequestBody ShipBillBriCondDTO var1);

    @PostMapping( path={"/shipBill/queryVehicleOrShippingListByName"}, consumes={"application/json"})
    public ItemResult<List<String>> queryVehicleOrShippingListByName(@RequestBody ShipBillQueryDTO var1);

    @PostMapping( path={"/shipBill/queryWDQrCodeByOrderCode"}, consumes={"application/json"})
    public ItemResult<List<QrCodeDTO>> queryWDQrCodeByOrderCode(@RequestParam(value="orderCode") String var1);
}

