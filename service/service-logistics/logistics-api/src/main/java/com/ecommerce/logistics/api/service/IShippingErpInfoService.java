
package com.ecommerce.logistics.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.logistics.api.dto.SysConfigDTO;
import com.ecommerce.logistics.api.dto.shipping.erp.BindErpShippingDTO;
import com.ecommerce.logistics.api.dto.shipping.erp.ErpShipSyncDTO;
import com.ecommerce.logistics.api.dto.shipping.erp.ErpShippingInfoDTO;
import io.swagger.v3.oas.annotations.Operation;

import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name="logistics")
public interface IShippingErpInfoService {
    @Operation(summary="查找ERP船舶列表")
    @PostMapping( path={"/shippingErpInfo/findErpShippingByErpCarrierNo"}, consumes={"application/json"})
    public ItemResult<List<ErpShippingInfoDTO>> findErpShippingByErpCarrierNo(@RequestParam(value="erpCarrierNo") String var1, @RequestParam(value="manufacturerMemberId") String var2);

    @Operation(summary="承运商船舶信息同步")
    @PostMapping( path={"/shippingErpInfo/syncErpInfo"}, consumes={"application/json"})
    public ItemResult<Boolean> syncErpInfo(ErpShipSyncDTO var1);

    @Operation(summary="电商和ERP船舶绑定")
    @PostMapping( path={"/shippingErpInfo/bindErpShippingInfo"}, consumes={"application/json"})
    public ItemResult<Boolean> bindErpShippingInfo(@RequestBody BindErpShippingDTO var1);

    @Operation(summary="查询所有ERP系统")
    @PostMapping( path={"/shippingErpInfo/querySysConfig"}, consumes={"application/json"})
    public ItemResult<List<SysConfigDTO>> querySysConfig();
}

