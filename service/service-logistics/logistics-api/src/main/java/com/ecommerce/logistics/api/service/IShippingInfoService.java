
package com.ecommerce.logistics.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.shipping.AuthorizeSellerBatchDTO;
import com.ecommerce.logistics.api.dto.shipping.AuthorizeSellerDTO;
import com.ecommerce.logistics.api.dto.shipping.BindCaptainDTO;
import com.ecommerce.logistics.api.dto.shipping.BindCaptainLogDTO;
import com.ecommerce.logistics.api.dto.shipping.EditServerTypeDTO;
import com.ecommerce.logistics.api.dto.shipping.ShippingBatchAuthorizeDTO;
import com.ecommerce.logistics.api.dto.shipping.ShippingBatchDTO;
import com.ecommerce.logistics.api.dto.shipping.ShippingInfoDTO;
import com.ecommerce.logistics.api.dto.shipping.ShippingInfoDetailsDTO;
import com.ecommerce.logistics.api.dto.shipping.ShippingInfoListDTO;
import com.ecommerce.logistics.api.dto.shipping.ShippingInfoListQueryDTO;
import com.ecommerce.logistics.api.dto.shipping.ShippingInfoQueryDTO;
import com.ecommerce.logistics.api.dto.shipping.ShippingInfoSaveDTO;
import com.ecommerce.logistics.api.dto.shipping.ShippingOperationDTO;
import com.ecommerce.logistics.api.dto.shipping.erp.BindErpShippingDTO;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name="logistics")
public interface IShippingInfoService {
    @PostMapping( path={"/shippingInfo/saveShippingInfo"}, consumes={"application/json"})
    public ItemResult<String> saveShippingInfo(@RequestBody ShippingInfoSaveDTO var1);

    @PostMapping( path={"/shippingInfo/delShippingInfo"}, consumes={"application/json"})
    public ItemResult<Void> delShippingInfo(@RequestBody ShippingBatchDTO var1);

    @PostMapping( path={"/shippingInfo/submitShippingInfo"}, consumes={"application/json"})
    public ItemResult<Void> submitShippingInfo(@RequestBody ShippingInfoSaveDTO var1);

    @PostMapping( path={"/shippingInfo/passShippingInfo"}, consumes={"application/json"})
    public ItemResult<Void> passShippingInfo(@RequestBody ShippingOperationDTO var1);

    @PostMapping( path={"/shippingInfo/rejectShippingInfo"}, consumes={"application/json"})
    public ItemResult<Void> rejectShippingInfo(@RequestBody ShippingOperationDTO var1);

    @PostMapping( path={"/shippingInfo/queryShippingInfoListByPlatform"}, consumes={"application/json"})
    public ItemResult<PageData<ShippingInfoListDTO>> queryShippingInfoListByPlatform(@RequestBody PageQuery<ShippingInfoListQueryDTO> var1);

    @PostMapping( path={"/shippingInfo/queryShippingInfoListByCarrier"}, consumes={"application/json"})
    public ItemResult<PageData<ShippingInfoListDTO>> queryShippingInfoListByCarrier(@RequestBody PageQuery<ShippingInfoListQueryDTO> var1);

    @PostMapping( path={"/shippingInfo/queryShippingInfoListBySeller"}, consumes={"application/json"})
    public ItemResult<PageData<ShippingInfoListDTO>> queryShippingInfoListBySeller(@RequestBody PageQuery<ShippingInfoListQueryDTO> var1);

    @PostMapping( path={"/shippingInfo/queryShippingInfoListBySelf"}, consumes={"application/json"})
    public ItemResult<PageData<ShippingInfoDTO>> queryShippingInfoListBySelf(@RequestBody PageQuery<ShippingInfoQueryDTO> var1);

    @PostMapping( path={"/shippingInfo/queryShippingInfoDetailsByBuyer"}, consumes={"application/json"})
    public ItemResult<ShippingInfoDetailsDTO> queryShippingInfoDetailsByBuyer(@RequestParam(value="shippingId") String var1);

    @PostMapping( path={"/shippingInfo/queryShippingInfoDetails"}, consumes={"application/json"})
    public ItemResult<ShippingInfoDetailsDTO> queryShippingInfoDetails(@RequestParam(value="shippingId") String var1);

    @PostMapping( path={"/shippingInfo/batchAuthorizeSeller"}, consumes={"application/json"})
    public ItemResult<Void> batchAuthorizeSeller(@RequestBody ShippingBatchAuthorizeDTO var1);

    @PostMapping( path={"/shippingInfo/batchCancelAuthorizeSeller"}, consumes={"application/json"})
    public ItemResult<Void> batchCancelAuthorizeSeller(@RequestBody ShippingBatchAuthorizeDTO var1);

    @PostMapping( path={"/shippingInfo/shippingInfoOverhaul"}, consumes={"application/json"})
    public ItemResult<Void> shippingInfoOverhaul(@RequestBody ShippingBatchDTO var1);

    @PostMapping( path={"/shippingInfo/bindCaptain"}, consumes={"application/json"})
    public ItemResult<Void> bindCaptain(@RequestBody BindCaptainDTO var1);

    @PostMapping( path={"/shippingInfo/shippingNameValid"}, consumes={"application/json"})
    public ItemResult<Boolean> shippingNameValid(@RequestParam(value="shippingName") String var1);

    @PostMapping( path={"/shippingInfo/disableShippingInfo"}, consumes={"application/json"})
    public ItemResult<Void> disableShippingInfo(@RequestBody ShippingOperationDTO var1);

    @PostMapping( path={"/shippingInfo/updateServerType"}, consumes={"application/json"})
    public ItemResult<Void> updateServerType(@RequestBody EditServerTypeDTO var1);

    @PostMapping( path={"/shippingInfo/batchFixedTemporary"}, consumes={"application/json"})
    public ItemResult<Void> batchFixedTemporary(@RequestBody ShippingBatchDTO var1);

    @PostMapping( path={"/shippingInfo/authorizeSellerList"}, consumes={"application/json"})
    public ItemResult<Void> authorizeSellerList(@RequestBody AuthorizeSellerBatchDTO var1);

    @PostMapping( path={"/shippingInfo/getAuthorizeSellerList"}, consumes={"application/json"})
    public ItemResult<List<AuthorizeSellerDTO>> getAuthorizeSellerList(@RequestParam(value="shippingId") String var1);

    @PostMapping( path={"/shippingInfo/updateShippingStatus"}, consumes={"application/json"})
    public ItemResult<Void> updateShippingStatus(@RequestBody ShippingOperationDTO var1);

    @PostMapping( path={"/shippingInfo/queryBindCaptainLog"}, consumes={"application/json"})
    public ItemResult<List<BindCaptainLogDTO>> queryBindCaptainLog(@RequestParam(value="captainAccountName") String var1);

    @PostMapping( path={"/shippingInfo/getShippingCompanyList"}, consumes={"application/json"})
    public ItemResult<List<String>> getShippingCompanyList(@RequestParam(value="shippingCompany", required=false) String var1);

    @PostMapping( path={"/shippingInfo/batchRecoveryTransport"}, consumes={"application/json"})
    public ItemResult<Void> batchRecoveryTransport(@RequestBody ShippingBatchDTO var1);

    @PostMapping( path={"/shippingInfo/queryShippingInfoByMemberId"}, consumes={"application/json"})
    public ItemResult<ShippingInfoDetailsDTO> queryShippingInfoByMemberId(@RequestParam(value="memberId") String var1);

    @PostMapping( path={"/shippingInfo/isPersonalShipowner"}, consumes={"application/json"})
    public ItemResult<Boolean> isPersonalShipowner(@RequestParam(value="memberId") String var1);

    @PostMapping( path={"/shippingInfo/queryPersonalShippingInfo"}, consumes={"application/json"})
    public ItemResult<List<ShippingInfoDTO>> queryPersonalShippingInfo(@RequestParam(value="shippingName") String var1);

    @PostMapping( path={"/shippingInfo/queryPlatformShippingInfo"}, consumes={"application/json"})
    public ItemResult<List<ShippingInfoDTO>> queryPlatformShippingInfo(@RequestParam(value="shippingName") String var1);

    @PostMapping( path={"/shippingInfo/queryErpShippingBindList"}, consumes={"application/json"})
    public List<BindErpShippingDTO> queryErpShippingBindList(@RequestParam(value="shippingId") String var1);

    @PostMapping( path={"/shippingInfo/selectShippingInfoName"}, consumes={"application/json"})
    public ItemResult<List<String>> selectShippingInfoName(@RequestBody ShippingInfoListQueryDTO var1);

    @PostMapping( path={"/shippingInfo/queryAllocationShippingInfo"}, consumes={"application/json"})
    public ItemResult<List<ShippingInfoListDTO>> queryAllocationShippingInfo(@RequestParam(value="memberId") String var1, @RequestParam(value="carrierId") String var2, @RequestParam(value="shippingName") String var3);
}

