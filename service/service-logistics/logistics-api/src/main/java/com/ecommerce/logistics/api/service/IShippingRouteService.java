
package com.ecommerce.logistics.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.shippingroute.ShippingAuxiliaryOperateLogDTO;
import com.ecommerce.logistics.api.dto.shippingroute.ShippingRouteAddDTO;
import com.ecommerce.logistics.api.dto.shippingroute.ShippingRouteDetailsDTO;
import com.ecommerce.logistics.api.dto.shippingroute.ShippingRouteEditDTO;
import com.ecommerce.logistics.api.dto.shippingroute.ShippingRouteListDTO;
import com.ecommerce.logistics.api.dto.shippingroute.ShippingRouteQueryDTO;
import com.ecommerce.logistics.api.dto.shippingrule.QueryShippingRuleDTO;
import com.ecommerce.logistics.api.dto.shippingrule.ShippingRuleResultDTO;
import java.util.List;
import java.util.Map;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name="logistics")
public interface IShippingRouteService {
    @PostMapping( path={"/shippingRoute/addShippingRoute"}, consumes={"application/json"})
    public ItemResult<String> addShippingRoute(@RequestBody ShippingRouteAddDTO var1);

    @PostMapping( path={"/shippingRoute/delShippingRoute"}, consumes={"application/json"})
    public ItemResult<Void> delShippingRoute(@RequestParam(value="belongerId") String var1, @RequestParam(value="shippingRouteId") String var2, @RequestParam(value="operatorUserId") String var3);

    @PostMapping( path={"/shippingRoute/editShippingRoute"}, consumes={"application/json"})
    public ItemResult<Void> editShippingRoute(@RequestBody ShippingRouteEditDTO var1);

    @PostMapping( path={"/shippingRoute/queryShippingRouteDetails"}, consumes={"application/json"})
    public ItemResult<ShippingRouteDetailsDTO> queryShippingRouteDetails(@RequestParam(value="belongerId") String var1, @RequestParam(value="shippingRouteId") String var2);

    @PostMapping( path={"/shippingRoute/queryShippingRouteList"}, consumes={"application/json"})
    public ItemResult<PageData<ShippingRouteListDTO>> queryShippingRouteList(@RequestBody PageQuery<ShippingRouteQueryDTO> var1);

    @PostMapping( path={"/shippingRoute/delFarePayment"}, consumes={"application/json"})
    public ItemResult<Void> delFarePayment(@RequestParam(value="shippingRouteId") String var1, @RequestParam(value="shippingFarePaymentId") String var2, @RequestParam(value="operatorUserId") String var3, @RequestParam(value="operatorUserName") String var4);

    @PostMapping( path={"/shippingRoute/downloadShippingRouteOperateLog"}, consumes={"application/json"})
    public ItemResult<List<ShippingAuxiliaryOperateLogDTO>> downloadShippingRouteOperateLog(@RequestParam(value="shippingRouteId") String var1);

    @PostMapping( path={"/shippingRoute/queryShippingRule"}, consumes={"application/json"})
    public ItemResult<List<ShippingRuleResultDTO>> queryShippingRule(@RequestBody QueryShippingRuleDTO var1);

    @PostMapping( path={"/shippingRoute/queryShippingPaymentRule"}, consumes={"application/json"})
    public ItemResult<List<ShippingRuleResultDTO>> queryShippingPaymentRule(@RequestBody QueryShippingRuleDTO var1);

    @PostMapping( path={"/shippingRoute/queryUnloadingWharf"}, consumes={"application/json"})
    public ItemResult<Map<String, String>> queryUnloadingWharf(@RequestParam(value="belongerId") String var1);

    @PostMapping( path={"/shippingRoute/queryPickingWharf"}, consumes={"application/json"})
    public ItemResult<Map<String, String>> queryPickingWharf(@RequestParam(value="belongerId") String var1);
}

