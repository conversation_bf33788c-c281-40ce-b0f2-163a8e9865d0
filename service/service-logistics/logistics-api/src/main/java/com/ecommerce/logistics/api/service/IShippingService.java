
package com.ecommerce.logistics.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.shipping.ShippingAuditDTO;
import com.ecommerce.logistics.api.dto.shipping.ShippingDeleteDTO;
import com.ecommerce.logistics.api.dto.shipping.ShippingDetailDTO;
import com.ecommerce.logistics.api.dto.shipping.ShippingListDTO;
import com.ecommerce.logistics.api.dto.shipping.ShippingQueryDTO;
import com.ecommerce.logistics.api.dto.shipping.ShippingSaveDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name="logistics")
public interface IShippingService {
    @PostMapping( path={"/shipping/addShipping"}, consumes={"application/json"})
    public ItemResult<String> addShipping(@RequestBody ShippingSaveDTO var1);

    @PostMapping( path={"/shipping/deleteShipping"}, consumes={"application/json"})
    public ItemResult<Void> deleteShipping(@RequestBody ShippingDeleteDTO var1);

    @PostMapping( path={"/shipping/editShipping"}, consumes={"application/json"})
    public ItemResult<Void> editShipping(@RequestBody ShippingSaveDTO var1);

    @PostMapping( path={"/shipping/queryShippingDetail"}, consumes={"application/json"})
    public ItemResult<ShippingDetailDTO> queryShippingDetail(@RequestParam(value="shippingId") String var1);

    @PostMapping( path={"/shipping/queryShippingList"}, consumes={"application/json"})
    public ItemResult<PageData<ShippingListDTO>> queryShippingList(@RequestBody PageQuery<ShippingQueryDTO> var1);

    @PostMapping( path={"/shipping/shippingAudit"}, consumes={"application/json"})
    public ItemResult<Void> shippingAudit(@RequestBody ShippingAuditDTO var1);
}

