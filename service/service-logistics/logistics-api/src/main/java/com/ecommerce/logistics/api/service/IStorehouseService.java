
package com.ecommerce.logistics.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.storehouse.StorehouseListDTO;
import com.ecommerce.logistics.api.dto.storehouse.StorehouseQueryDTO;
import com.ecommerce.logistics.api.dto.storehouse.WarehouseCapacityChangeDTO;
import com.ecommerce.logistics.api.dto.storehouse.WarehouseStorageListDTO;
import com.ecommerce.logistics.api.dto.storehouse.WarehouseStorageQueryDTO;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name="logistics")
public interface IStorehouseService {
    @PostMapping( path={"/storehouse/changeWarehouseCapacity"}, consumes={"application/json"})
    public ItemResult<Void> changeWarehouseCapacity(@RequestBody WarehouseCapacityChangeDTO var1);

    @PostMapping( path={"/storehouse/queryPlatformStorageBySeller"}, consumes={"application/json"})
    public ItemResult<PageData<WarehouseStorageListDTO>> queryPlatformStorageBySeller(@RequestBody PageQuery<WarehouseStorageQueryDTO> var1);

    @PostMapping( path={"/storehouse/fetchStockList"}, consumes={"application/json"})
    public List<StorehouseListDTO> fetchStockList(@RequestBody StorehouseQueryDTO var1);
}

