
package com.ecommerce.logistics.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.transportaddress.TransportAddressDeleteDTO;
import com.ecommerce.logistics.api.dto.transportaddress.TransportAddressDetailDTO;
import com.ecommerce.logistics.api.dto.transportaddress.TransportAddressListDTO;
import com.ecommerce.logistics.api.dto.transportaddress.TransportAddressQueryDTO;
import com.ecommerce.logistics.api.dto.transportaddress.TransportAddressSaveDTO;
import com.ecommerce.logistics.api.dto.transportaddress.TransportAddressSearchDTO;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name="logistics")
public interface ITransportAddressService {
    @PostMapping( path={"/transportAddress/addTransportAddress"}, consumes={"application/json"})
    public ItemResult<String> addTransportAddress(@RequestBody TransportAddressSaveDTO var1);

    @PostMapping( path={"/transportAddress/deleteTransportAddress"}, consumes={"application/json"})
    public ItemResult<Void> deleteTransportAddress(@RequestBody TransportAddressDeleteDTO var1);

    @PostMapping( path={"/transportAddress/editTransportAddress"}, consumes={"application/json"})
    public ItemResult<Void> editTransportAddress(@RequestBody TransportAddressSaveDTO var1);

    @PostMapping( path={"/transportAddress/queryTransportAddressDetail"}, consumes={"application/json"})
    public ItemResult<TransportAddressDetailDTO> queryTransportAddressDetail(@RequestParam(value="transportAddressId") String var1);

    @PostMapping( path={"/transportAddress/queryTransportAddressList"}, consumes={"application/json"})
    public ItemResult<PageData<TransportAddressListDTO>> queryTransportAddressList(@RequestBody PageQuery<TransportAddressQueryDTO> var1);

    @PostMapping( path={"/transportAddress/searchTransportAddressList"}, consumes={"application/json"})
    public ItemResult<List<TransportAddressListDTO>> searchTransportAddressList(@RequestBody TransportAddressSearchDTO var1);
}

