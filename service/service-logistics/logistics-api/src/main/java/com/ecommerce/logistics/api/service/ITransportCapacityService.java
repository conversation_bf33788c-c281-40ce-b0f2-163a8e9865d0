
package com.ecommerce.logistics.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.transportcapacity.TransportCapacityDeleteDTO;
import com.ecommerce.logistics.api.dto.transportcapacity.TransportCapacityDetailDTO;
import com.ecommerce.logistics.api.dto.transportcapacity.TransportCapacityListDTO;
import com.ecommerce.logistics.api.dto.transportcapacity.TransportCapacityQueryDTO;
import com.ecommerce.logistics.api.dto.transportcapacity.TransportCapacitySaveDTO;
import com.ecommerce.logistics.api.dto.transportcapacity.TransportCapacityViewQueryDTO;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name="logistics")
public interface ITransportCapacityService {
    @PostMapping( path={"/transportCapacity/addTransportCapacity"}, consumes={"application/json"})
    public ItemResult<String> addTransportCapacity(@RequestBody TransportCapacitySaveDTO var1);

    @PostMapping( path={"/transportCapacity/deleteTransportCapacity"}, consumes={"application/json"})
    public ItemResult<Void> deleteTransportCapacity(@RequestBody TransportCapacityDeleteDTO var1);

    @PostMapping( path={"/transportCapacity/editTransportCapacity"}, consumes={"application/json"})
    public ItemResult<Void> editTransportCapacity(@RequestBody TransportCapacitySaveDTO var1);

    @PostMapping( path={"/transportCapacity/queryTransportCapacityDetail"}, consumes={"application/json"})
    public ItemResult<TransportCapacityDetailDTO> queryTransportCapacityDetail(@RequestParam(value="transportCapacityId") String var1);

    @PostMapping( path={"/transportCapacity/queryTransportCapacityList"}, consumes={"application/json"})
    public ItemResult<PageData<TransportCapacityListDTO>> queryTransportCapacityList(@RequestBody PageQuery<TransportCapacityQueryDTO> var1);

    @PostMapping( path={"/transportCapacity/queryTransportCapacityViewRecord"}, consumes={"application/json"})
    public ItemResult<List<TransportCapacityListDTO>> queryTransportCapacityViewRecord(@RequestBody TransportCapacityViewQueryDTO var1);
}

