
package com.ecommerce.logistics.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.transportcategory.CategoryColDTO;
import com.ecommerce.logistics.api.dto.transportcategory.TransportCategoryAddDTO;
import com.ecommerce.logistics.api.dto.transportcategory.TransportCategoryDTO;
import com.ecommerce.logistics.api.dto.transportcategory.TransportCategoryDetailDTO;
import com.ecommerce.logistics.api.dto.transportcategory.TransportCategoryEditDTO;
import com.ecommerce.logistics.api.dto.transportcategory.TransportCategoryListDTO;
import com.ecommerce.logistics.api.dto.transportcategory.TransportCategoryListQueryDTO;
import com.ecommerce.logistics.api.dto.transportcategory.TransportCategoryRemoveDTO;
import com.ecommerce.logistics.api.dto.transportcategory.TransportOption;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 运输品类
*/

@FeignClient(name="logistics")
public interface ITransportCategoryService {
    @PostMapping( path={"/transportCategory/queryAll"}, consumes={"application/json"})
    public ItemResult<List<TransportCategoryDTO>> queryAll();

    @PostMapping( path={"/transportCategory/queryOptions"}, consumes={"application/json"})
    public ItemResult<List<TransportOption>> queryOptions();

    @PostMapping( path={"/transportCategory/queryTransportCategoryListById"}, consumes={"application/json"})
    public ItemResult<List<TransportCategoryDTO>> queryTransportCategoryListById(@RequestBody List<String> var1);

    @PostMapping( path={"/transportCategory/addTransportCategory"}, consumes={"application/json"})
    public ItemResult<Void> addTransportCategory(@RequestBody TransportCategoryAddDTO var1);

    @PostMapping( path={"/transportCategory/transportCategoryMergeFilter"}, consumes={"application/json"})
    public ItemResult<List<String>> transportCategoryMergeFilter(@RequestBody List<String> var1);

    @PostMapping( path={"/transportCategory/modifyTransportCategory"}, consumes={"application/json"})
    public ItemResult<Void> modifyTransportCategory(@RequestBody TransportCategoryEditDTO var1);

    @PostMapping( path={"/transportCategory/removeTransportCategory"}, consumes={"application/json"})
    public ItemResult<Void> removeTransportCategory(@RequestBody TransportCategoryRemoveDTO var1);

    @PostMapping( path={"/transportCategory/queryTransportCategory"}, consumes={"application/json"})
    public ItemResult<TransportCategoryDetailDTO> queryTransportCategory(@RequestParam(value="transportCategoryId") String var1);

    @PostMapping( path={"/transportCategory/queryTransportCategoryList"}, consumes={"application/json"})
    public ItemResult<PageData<TransportCategoryListDTO>> queryTransportCategoryList(@RequestBody PageQuery<TransportCategoryListQueryDTO> var1);

    @PostMapping( path={"/transportCategory/queryWaybillTransportCategoryMap"}, consumes={"application/json"})
    public ItemResult<List<CategoryColDTO>> queryWaybillTransportCategoryMap(@RequestBody List<String> var1);
}

