
package com.ecommerce.logistics.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.transportdemand.TransportDemandAuditDTO;
import com.ecommerce.logistics.api.dto.transportdemand.TransportDemandDeleteDTO;
import com.ecommerce.logistics.api.dto.transportdemand.TransportDemandDetailDTO;
import com.ecommerce.logistics.api.dto.transportdemand.TransportDemandListDTO;
import com.ecommerce.logistics.api.dto.transportdemand.TransportDemandQueryDTO;
import com.ecommerce.logistics.api.dto.transportdemand.TransportDemandSaveDTO;
import com.ecommerce.logistics.api.dto.transportdemand.TransportDemandViewQueryDTO;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name="logistics")
public interface ITransportDemandService {
    @PostMapping( path={"/transportDemand/addTransportDemand"}, consumes={"application/json"})
    public ItemResult<String> addTransportDemand(@RequestBody TransportDemandSaveDTO var1);

    @PostMapping( path={"/transportDemand/deleteTransportDemand"}, consumes={"application/json"})
    public ItemResult<Void> deleteTransportDemand(@RequestBody TransportDemandDeleteDTO var1);

    @PostMapping( path={"/transportDemand/editTransportDemand"}, consumes={"application/json"})
    public ItemResult<Void> editTransportDemand(@RequestBody TransportDemandSaveDTO var1);

    @PostMapping( path={"/transportDemand/queryTransportDemandDetail"}, consumes={"application/json"})
    public ItemResult<TransportDemandDetailDTO> queryTransportDemandDetail(@RequestParam(value="transportDemandId") String var1);

    @PostMapping( path={"/transportDemand/queryTransportDemandList"}, consumes={"application/json"})
    public ItemResult<PageData<TransportDemandListDTO>> queryTransportDemandList(@RequestBody PageQuery<TransportDemandQueryDTO> var1);

    @PostMapping( path={"/transportDemand/transportDemandAudit"}, consumes={"application/json"})
    public ItemResult<Void> transportDemandAudit(@RequestBody TransportDemandAuditDTO var1);

    @PostMapping( path={"/transportDemand/queryTransportDemandViewRecord"}, consumes={"application/json"})
    public ItemResult<List<TransportDemandListDTO>> queryTransportDemandViewRecord(@RequestBody TransportDemandViewQueryDTO var1);
}

