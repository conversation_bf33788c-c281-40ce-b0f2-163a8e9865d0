
package com.ecommerce.logistics.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.contract.TrusteeContractAddDTO;
import com.ecommerce.logistics.api.dto.contract.TrusteeContractDetailsDTO;
import com.ecommerce.logistics.api.dto.contract.TrusteeContractEditDTO;
import com.ecommerce.logistics.api.dto.contract.TrusteeContractListDTO;
import com.ecommerce.logistics.api.dto.contract.TrusteeContractListQueryDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name="logistics")
public interface ITrusteeContractService {
    @PostMapping( path={"/trusteeContract/addTrusteeContractInfo"}, consumes={"application/json"})
    public ItemResult<String> addTrusteeContractInfo(@RequestBody TrusteeContractAddDTO var1);

    @PostMapping( path={"/trusteeContract/delTrusteeContractInfo"}, consumes={"application/json"})
    public ItemResult<Void> delTrusteeContractInfo(@RequestParam(value="contractIds") String var1, @RequestParam(value="operatorUserId") String var2, @RequestParam(value="operatorUserName") String var3);

    @PostMapping( path={"/trusteeContract/editTrusteeContractInfo"}, consumes={"application/json"})
    public ItemResult<Void> editTrusteeContractInfo(@RequestBody TrusteeContractEditDTO var1);

    @PostMapping( path={"/trusteeContract/findTrusteeContractInfo"}, consumes={"application/json"})
    public ItemResult<TrusteeContractDetailsDTO> findTrusteeContractInfo(@RequestParam(value="contractId") String var1);

    @PostMapping( path={"/trusteeContract/queryTrusteeContractList"}, consumes={"application/json"})
    public ItemResult<PageData<TrusteeContractListDTO>> queryTrusteeContractList(@RequestBody PageQuery<TrusteeContractListQueryDTO> var1);
}

