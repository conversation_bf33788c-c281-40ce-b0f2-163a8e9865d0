
package com.ecommerce.logistics.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.unloadingobjection.ObjectionHandleDetailDTO;
import com.ecommerce.logistics.api.dto.unloadingobjection.UnloadingObjectionAddDTO;
import com.ecommerce.logistics.api.dto.unloadingobjection.UnloadingObjectionDetailDTO;
import com.ecommerce.logistics.api.dto.unloadingobjection.UnloadingObjectionHandleDTO;
import com.ecommerce.logistics.api.dto.unloadingobjection.UnloadingObjectionListDTO;
import com.ecommerce.logistics.api.dto.unloadingobjection.UnloadingObjectionQueryDTO;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name="logistics")
public interface IUnloadingObjectionService {
    @PostMapping( path={"/unloadingObjection/addObjection"}, consumes={"application/json"})
    public ItemResult<Void> addObjection(@RequestBody UnloadingObjectionAddDTO var1);

    @PostMapping( path={"/unloadingObjection/handleObjection"}, consumes={"application/json"})
    public ItemResult<Void> handleObjection(@RequestBody UnloadingObjectionHandleDTO var1);

    @PostMapping( path={"/unloadingObjection/batchCancelObjectionByIds"}, consumes={"application/json"})
    public ItemResult<Void> batchCancelObjectionByIds(@RequestBody List<String> var1, @RequestParam(value="operator") String var2);

    @PostMapping( path={"/unloadingObjection/findHandleDetailById"}, consumes={"application/json"})
    public ItemResult<ObjectionHandleDetailDTO> findHandleDetailById(@RequestParam(value="objectionId") String var1);

    @PostMapping( path={"/unloadingObjection/selectObjectionList"}, consumes={"application/json"})
    public ItemResult<PageData<UnloadingObjectionListDTO>> selectObjectionList(@RequestBody PageQuery<UnloadingObjectionQueryDTO> var1);

    @PostMapping( path={"/unloadingObjection/findObjectionDetailById"}, consumes={"application/json"})
    public ItemResult<UnloadingObjectionDetailDTO> findObjectionDetailById(@RequestParam(value="objectionId") String var1);

    @PostMapping( path={"/unloadingObjection/completeObjection"}, consumes={"application/json"})
    public ItemResult<Void> completeObjection(@RequestBody UnloadingObjectionHandleDTO var1);
}

