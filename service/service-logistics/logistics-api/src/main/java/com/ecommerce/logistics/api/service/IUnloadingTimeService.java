
package com.ecommerce.logistics.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.intelligentDispatch.UnloadingTimeDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name="logistics")
public interface IUnloadingTimeService {
    @PostMapping( path={"/unloadingTime/add"}, consumes={"application/json"})
    public ItemResult<Boolean> add(@RequestBody UnloadingTimeDTO var1);

    @PostMapping( path={"/unloadingTime/edit"}, consumes={"application/json"})
    public ItemResult<Boolean> edit(@RequestBody UnloadingTimeDTO var1);

    @PostMapping( path={"/unloadingTime/delete"}, consumes={"application/json"})
    public ItemResult<Boolean> delete(@RequestBody UnloadingTimeDTO var1);

    @PostMapping( path={"/unloadingTime/pageUnloadingTime"}, consumes={"application/json"})
    public ItemResult<PageData<UnloadingTimeDTO>> pageUnloadingTime(@RequestBody PageQuery<UnloadingTimeDTO> var1);
}

