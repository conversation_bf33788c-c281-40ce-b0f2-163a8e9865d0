
package com.ecommerce.logistics.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.logistics.api.dto.vehiclecert.VehicleCertDTO;
import com.ecommerce.logistics.api.param.vehiclecert.VehicleCertEditParam;
import com.ecommerce.logistics.api.param.vehiclecert.VehicleCertSaveParam;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name="logistics")
public interface IVehicleCertService {
    @PostMapping( path={"/vehicleCert/saveVehicleCertAndSubmitAuth"}, consumes={"application/json"})
    public ItemResult<String> saveVehicleCertAndSubmitAuth(VehicleCertSaveParam var1);

    @PostMapping( path={"/vehicleCert/queryVehicleCertByUserId"}, consumes={"application/json"})
    public ItemResult<VehicleCertDTO> queryVehicleCertByUserId(@Parameter(description="用户ID") @RequestParam(value="userId") String var1);

    @PostMapping( path={"/vehicleCert/modifyVehicleCert"}, consumes={"application/json"})
    public ItemResult<String> modifyVehicleCert(VehicleCertEditParam var1);

    @PostMapping( path={"/vehicleCert/queryVehicleCertByVehicleId"}, consumes={"application/json"})
    public ItemResult<VehicleCertDTO> queryVehicleCertByVehicleId(@Parameter(description="车辆ID") @RequestParam(value="vehicleId") String var1);
}

