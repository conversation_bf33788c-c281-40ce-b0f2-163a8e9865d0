
package com.ecommerce.logistics.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.logistics.api.dto.realtime.VehicleRelationCondDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name="logistics")
public interface IVehicleRealtimeService {
    @PostMapping( path={"/vehicleRealtime/filterVehicleCond"}, consumes={"application/json"})
    public ItemResult<VehicleRelationCondDTO> filterVehicleCond(@RequestBody VehicleRelationCondDTO var1);
}

