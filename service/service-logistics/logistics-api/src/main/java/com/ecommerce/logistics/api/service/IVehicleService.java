
package com.ecommerce.logistics.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.dispatchbill.DriverListDTO;
import com.ecommerce.logistics.api.dto.driver.BindDriverDTO;
import com.ecommerce.logistics.api.dto.driver.PersonVehicleDTO;
import com.ecommerce.logistics.api.dto.vehicle.ExternalVehicleAppDTO;
import com.ecommerce.logistics.api.dto.vehicle.ExternalVehicleDTO;
import com.ecommerce.logistics.api.dto.vehicle.UserVehicleListDTO;
import com.ecommerce.logistics.api.dto.vehicle.UserVehicleListQueryDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleAddDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleAppAddDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleAppDataDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleAppEditDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleAppListDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleBaseDataDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleBatchAddResultDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleBuyerTakeQueryDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleCertificationStatusDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleDefaultCondDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleDefaultResDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleDetailDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleDisableFlgEditDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleEditDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleImportTemplateOptionDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleListDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleListQueryDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleModifyIsDefaultDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleOptionsDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleOptionsQueryDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleRemoveDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleSignalEditDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleUserCondDTO;
import com.ecommerce.logistics.api.param.vehiclecert.VehicleAddParamDTO;
import java.util.List;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name="logistics")
public interface IVehicleService {
    @PostMapping( path={"/vehicle/queryVehicleInfoByNumber"}, consumes={"application/json"})
    public ItemResult<ExternalVehicleDTO> queryVehicleInfoByNumber(@RequestParam(value="number") String var1);

    @PostMapping( path={"/vehicle/modifyVehicle"}, consumes={"application/json"})
    public ItemResult<Void> modifyVehicle(@RequestBody VehicleEditDTO var1);

    @PostMapping( path={"/vehicle/queryVehicle"}, consumes={"application/json"})
    public ItemResult<VehicleDetailDTO> queryVehicle(@RequestParam(value="vehicleId") String var1);

    @PostMapping( path={"/vehicle/modifyAppVehicle"}, consumes={"application/json"})
    public ItemResult<Void> modifyAppVehicle(@RequestBody VehicleAppEditDTO var1);

    @PostMapping( path={"/vehicle/addAppVehicle"}, consumes={"application/json"})
    public ItemResult<String> addAppVehicle(@RequestBody VehicleAppAddDTO var1);

    @PostMapping( path={"/vehicle/addVehicle"}, consumes={"application/json"})
    public ItemResult<String> addVehicle(@RequestBody VehicleAddDTO var1);

    @PostMapping( path={"/vehicle/addVehicleV2"}, consumes={"application/json"})
    public ItemResult<String> addVehicleV2(@RequestBody VehicleAddParamDTO var1);

    @PostMapping( path={"/vehicle/queryVehicleList"}, consumes={"application/json"})
    public ItemResult<PageData<VehicleListDTO>> queryVehicleList(@RequestBody PageQuery<VehicleListQueryDTO> var1);

    @PostMapping( path={"/vehicle/queryVehicleListPlatform"}, consumes={"application/json"})
    public ItemResult<PageData<VehicleListDTO>> queryVehicleListPlatform(@RequestBody PageQuery<VehicleListQueryDTO> var1);

    @PostMapping( path={"/vehicle/removeVehicle"}, consumes={"application/json"})
    public ItemResult<Void> removeVehicle(@RequestBody List<VehicleRemoveDTO> var1);

    @PostMapping( path={"/vehicle/modifyCertificationStatusToFailure"}, consumes={"application/json"})
    public ItemResult<Void> modifyCertificationStatusToFailure(@RequestBody VehicleCertificationStatusDTO var1);

    @PostMapping( path={"/vehicle/addVehicleAndSubmitAuth"}, consumes={"application/json"})
    public ItemResult<String> addVehicleAndSubmitAuth(@RequestBody VehicleAddDTO var1);

    @PostMapping( path={"/vehicle/queryAppVehicleList"}, consumes={"application/json"})
    public ItemResult<List<VehicleAppListDTO>> queryAppVehicleList(@RequestParam(value="userId") String var1);

    @PostMapping( path={"/vehicle/queryDriverListByUserId"}, consumes={"application/json"})
    public ItemResult<PageData<DriverListDTO>> queryDriverListByUserId(@RequestBody PageQuery<String> var1);

    @PostMapping( path={"/vehicle/selectVehicleById"}, consumes={"application/json"})
    public ItemResult<VehicleDTO> selectVehicleById(@RequestParam(value="vehicleId") String var1);

    @PostMapping( path={"/vehicle/queryAppVehicleByUserId"}, consumes={"application/json"})
    public ItemResult<List<VehicleAppDataDTO>> queryAppVehicleByUserId(@RequestParam(value="userId") String var1);

    @PostMapping( path={"/vehicle/queryVehicleListByUserId"}, consumes={"application/json"})
    public ItemResult<PageData<UserVehicleListDTO>> queryVehicleListByUserId(@RequestBody PageQuery<UserVehicleListQueryDTO> var1);

    @PostMapping( path={"/vehicle/queryVehicleOptions"}, consumes={"application/json"})
    public ItemResult<List<VehicleOptionsDTO>> queryVehicleOptions(@RequestBody VehicleOptionsQueryDTO var1);

    @PostMapping( path={"/vehicle/modifyCertificationStatusToOk"}, consumes={"application/json"})
    public ItemResult<Void> modifyCertificationStatusToOk(@RequestBody VehicleCertificationStatusDTO var1);

    @PostMapping( path={"/vehicle/modifyAppIsDefault"}, consumes={"application/json"})
    public ItemResult<Void> modifyAppIsDefault(@RequestBody VehicleModifyIsDefaultDTO var1);

    @PostMapping( path={"/vehicle/updateVehicleSignFlag"}, consumes={"application/json"})
    public ItemResult<Void> updateVehicleSignFlag(@RequestBody VehicleSignalEditDTO var1);

    @PostMapping( path={"/vehicle/getCertificationStatus"}, consumes={"application/json"})
    public ItemResult<String> getCertificationStatus(@RequestParam(value="userId") String var1);

    @PostMapping( path={"/vehicle/submitAuthentication"}, consumes={"application/json"})
    public ItemResult<Void> submitAuthentication(@RequestBody VehicleCertificationStatusDTO var1);

    @PostMapping( path={"/vehicle/queryVehicleBaseData"}, consumes={"application/json"})
    public ItemResult<List<VehicleBaseDataDTO>> queryVehicleBaseData(@RequestParam(value="userId") String var1);

    @PostMapping( path={"/vehicle/queryAppVehicleListByUserDTO"}, consumes={"application/json"})
    public ItemResult<List<VehicleAppListDTO>> queryAppVehicleListByUserDTO(@RequestBody VehicleUserCondDTO var1);

    @PostMapping( path={"/vehicle/findDefaultAssignVehicle"}, consumes={"application/json"})
    public ItemResult<VehicleDefaultResDTO> findDefaultAssignVehicle(@RequestBody VehicleDefaultCondDTO var1);

    @PostMapping( path={"/vehicle/batchAddVehicle"}, consumes={"application/json"})
    public ItemResult<VehicleBatchAddResultDTO> batchAddVehicle(@RequestBody List<VehicleAddDTO> var1);

    @PostMapping( path={"/vehicle/batchAddAppVehicle"}, consumes={"application/json"})
    public ItemResult<VehicleBatchAddResultDTO> batchAddAppVehicle(@RequestBody List<VehicleAppAddDTO> var1);

    @PostMapping( path={"/vehicle/queryVehicleImportTemplateOptions"}, consumes={"application/json"})
    public ItemResult<VehicleImportTemplateOptionDTO> queryVehicleImportTemplateOptions();

    @PostMapping( path={"/vehicle/bindingVehicle"}, consumes={"application/json"})
    public ItemResult<Void> bindingVehicle(@RequestParam(value="operateUserId") String var1, @RequestParam(value="number") String var2, @RequestParam(value="carrierId") String var3);

    @PostMapping( path={"/vehicle/unBindingVehicle"}, consumes={"application/json"})
    public ItemResult<Void> unBindingVehicle(@RequestParam(value="operateUserId") String var1, @RequestParam(value="number") String var2, @RequestParam(value="carrierId") String var3);

    @PostMapping( path={"/vehicle/updateVehicleDisableFlg"}, consumes={"application/json"})
    public ItemResult<Void> updateVehicleDisableFlg(@RequestBody List<VehicleDisableFlgEditDTO> var1);

    @PostMapping( path={"/vehicle/queryVehicleByNumber"}, consumes={"application/json"})
    public ItemResult<VehicleDTO> queryVehicleByNumber(@RequestParam(value="number") String var1);

    @PostMapping( path={"/vehicle/getBuyerTakeCars"}, consumes={"application/json"})
    public ItemResult<List<VehicleBaseDataDTO>> getBuyerTakeCars(@RequestBody VehicleBuyerTakeQueryDTO var1);

    @PostMapping( path={"/vehicle/queryPersonalVehicle"}, consumes={"application/json"})
    public ItemResult<List<VehicleDetailDTO>> queryPersonalVehicle(@RequestBody VehicleListQueryDTO var1);

    @PostMapping( path={"/vehicle/bindDriver"}, consumes={"application/json"})
    public ItemResult<Void> bindDriver(@Valid BindDriverDTO var1);

    @PostMapping( path={"/vehicle/selectVehicleListByNumber"}, consumes={"application/json"})
    public ItemResult<ExternalVehicleAppDTO> selectVehicleListByNumber(@RequestParam(value="number") String var1);

    @PostMapping( path={"/vehicle/queryPersonVehicleByUserId"}, consumes={"application/json"})
    public ItemResult<PersonVehicleDTO> queryPersonVehicleByUserId(@RequestParam(value="userId") String var1);

    @PostMapping( path={"/vehicle/queryExportVehicleList"}, consumes={"application/json"})
    public ItemResult<List<VehicleListDTO>> queryExportVehicleList(@RequestBody VehicleListQueryDTO var1);

    @PostMapping( path={"/vehicle/queryVehicleListDropDownBox"}, consumes={"application/json"})
    public ItemResult<List<UserVehicleListDTO>> queryVehicleListDropDownBox(@RequestBody UserVehicleListQueryDTO var1);

    @PostMapping( path={"/vehicle/selectVehicleNumber"}, consumes={"application/json"})
    public ItemResult<List<String>> selectVehicleNumber(@RequestBody VehicleListQueryDTO var1);
}

