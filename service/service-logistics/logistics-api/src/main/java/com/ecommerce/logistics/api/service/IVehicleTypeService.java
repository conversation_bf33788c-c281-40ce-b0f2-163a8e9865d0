
package com.ecommerce.logistics.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.vehicletype.VehicleTypeAddDTO;
import com.ecommerce.logistics.api.dto.vehicletype.VehicleTypeDetailDTO;
import com.ecommerce.logistics.api.dto.vehicletype.VehicleTypeEditDTO;
import com.ecommerce.logistics.api.dto.vehicletype.VehicleTypeListDTO;
import com.ecommerce.logistics.api.dto.vehicletype.VehicleTypeListQueryDTO;
import com.ecommerce.logistics.api.dto.vehicletype.VehicleTypeNameDTO;
import com.ecommerce.logistics.api.dto.vehicletype.VehicleTypeOptionDTO;
import com.ecommerce.logistics.api.dto.vehicletype.VehicleTypeRemoveDTO;
import java.math.BigDecimal;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @Description::: 车辆类型
*/

@FeignClient(name="logistics")
public interface IVehicleTypeService {
    @PostMapping( path={"/vehicleType/queryOptions"}, consumes={"application/json"})
    public ItemResult<List<VehicleTypeOptionDTO>> queryOptions();

    @PostMapping( path={"/vehicleType/queryVehicleTypes"}, consumes={"application/json"})
    public List<VehicleTypeListDTO> queryVehicleTypes(@RequestBody List<String> var1);

    @PostMapping( path={"/vehicleType/removeVehicleType"}, consumes={"application/json"})
    public ItemResult<Void> removeVehicleType(@RequestBody VehicleTypeRemoveDTO var1);

    @PostMapping( path={"/vehicleType/queryVehicleTypeList"}, consumes={"application/json"})
    public ItemResult<PageData<VehicleTypeListDTO>> queryVehicleTypeList(@RequestBody PageQuery<VehicleTypeListQueryDTO> var1);

    @PostMapping( path={"/vehicleType/modifyVehicleType"}, consumes={"application/json"})
    public ItemResult<Void> modifyVehicleType(@RequestBody VehicleTypeEditDTO var1);

    @PostMapping( path={"/vehicleType/addVehicleType"}, consumes={"application/json"})
    public ItemResult<Void> addVehicleType(@RequestBody VehicleTypeAddDTO var1);

    @PostMapping( path={"/vehicleType/queryVehicleType"}, consumes={"application/json"})
    public ItemResult<VehicleTypeDetailDTO> queryVehicleType(@RequestParam(value="vehicleTypeId") String var1);

    @PostMapping( path={"/vehicleType/querytUnlimiteType"}, consumes={"application/json"})
    public ItemResult<VehicleTypeNameDTO> querytUnlimiteType();

    @PostMapping( path={"/vehicleType/queryDepositAmountByDriverUserId"}, consumes={"application/json"})
    public ItemResult<BigDecimal> queryDepositAmountByDriverUserId(@RequestParam(value="userId") String var1);
}

