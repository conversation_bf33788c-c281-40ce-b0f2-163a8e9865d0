
package com.ecommerce.logistics.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.warehouse.CentralWarehouseQueryDTO;
import com.ecommerce.logistics.api.dto.warehouse.SearchAddressDTO;
import com.ecommerce.logistics.api.dto.warehouse.StoreQueryDTO;
import com.ecommerce.logistics.api.dto.warehouse.TradeWarehouseDTO;
import com.ecommerce.logistics.api.dto.warehouse.WarehouseAddDTO;
import com.ecommerce.logistics.api.dto.warehouse.WarehouseBaseDataDTO;
import com.ecommerce.logistics.api.dto.warehouse.WarehouseDetailsDTO;
import com.ecommerce.logistics.api.dto.warehouse.WarehouseEditDTO;
import com.ecommerce.logistics.api.dto.warehouse.WarehouseInsertReturnDTO;
import com.ecommerce.logistics.api.dto.warehouse.WarehouseListDTO;
import com.ecommerce.logistics.api.dto.warehouse.WarehouseListQueryDTO;
import com.ecommerce.logistics.api.dto.warehouse.WarehouseOptionDTO;
import com.ecommerce.logistics.api.dto.warehouse.WarehouseOptionQueryDTO;
import com.ecommerce.logistics.api.dto.warehouse.WarehouseQueryDTO;
import com.ecommerce.logistics.api.dto.warehouse.WarehouseRemoveDTO;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 仓库服务
*/

@FeignClient(name="logistics")
public interface IWarehouseService {
    @PostMapping( path={"/warehouse/selectSeckillWarehouseByUserId"}, consumes={"application/json"})
    public ItemResult<List<WarehouseOptionDTO>> selectSeckillWarehouseByUserId(@RequestParam(value="userId") String var1);

    @PostMapping( path={"/warehouse/queryWarehouseIdAndName"}, consumes={"application/json"})
    public ItemResult<List<WarehouseOptionDTO>> queryWarehouseIdAndName(@RequestBody WarehouseOptionQueryDTO var1);

    @PostMapping( path={"/warehouse/queryZoneWarehouse"}, consumes={"application/json"})
    public ItemResult<TradeWarehouseDTO> queryZoneWarehouse(@RequestBody WarehouseQueryDTO var1);

    @PostMapping( path={"/warehouse/queryWarehouseDetails"}, consumes={"application/json"})
    public ItemResult<WarehouseDetailsDTO> queryWarehouseDetails(@RequestParam(value="warehouseId") String var1);

    @PostMapping( path={"/warehouse/queryCentralWarehouseByArea"}, consumes={"application/json"})
    public ItemResult<WarehouseDetailsDTO> queryCentralWarehouseByArea(@RequestBody CentralWarehouseQueryDTO var1);

    @PostMapping( path={"/warehouse/selectWarehouseBaseData"}, consumes={"application/json"})
    public ItemResult<WarehouseBaseDataDTO> selectWarehouseBaseData(@RequestParam(value="warehouseId") String var1);

    @PostMapping( path={"/warehouse/queryWarehouseList"}, consumes={"application/json"})
    public ItemResult<PageData<WarehouseListDTO>> queryWarehouseList(@RequestBody PageQuery<WarehouseListQueryDTO> var1);

    @PostMapping( path={"/warehouse/searchAddress"}, consumes={"application/json"})
    public ItemResult<String> searchAddress(@RequestBody SearchAddressDTO var1);

    @PostMapping( path={"/warehouse/removeWarehouse"}, consumes={"application/json"})
    public ItemResult<Void> removeWarehouse(@RequestBody WarehouseRemoveDTO var1);

    @PostMapping( path={"/warehouse/updateWarehouse"}, consumes={"application/json"})
    public ItemResult<Void> updateWarehouse(@RequestBody WarehouseEditDTO var1);

    @PostMapping( path={"/warehouse/queryStoreByCode"}, consumes={"application/json"})
    public ItemResult<WarehouseDetailsDTO> queryStoreByCode(@RequestBody StoreQueryDTO var1);

    @PostMapping( path={"/warehouse/saveWarehouse"}, consumes={"application/json"})
    public ItemResult<WarehouseInsertReturnDTO> saveWarehouse(@RequestBody WarehouseAddDTO var1);
}

