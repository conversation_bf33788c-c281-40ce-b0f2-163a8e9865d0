
package com.ecommerce.logistics.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.logistics.api.dto.waybill.ERPWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.ERPWaybillExecuteDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillRetryHandlerDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name="logistics")
public interface IWaybillExternalService {
    @PostMapping( path={"/waybillExternal/waybillRetryHandler"}, consumes={"application/json"})
    public ItemResult<Void> waybillRetryHandler(@RequestBody WaybillRetryHandlerDTO var1);

    @PostMapping( path={"/waybillExternal/createExternalWaybillCallBack"}, consumes={"application/json"})
    public ItemResult<Void> createExternalWaybillCallBack(@RequestBody ItemResult<ERPWaybillDTO> var1);

    @PostMapping( path={"/waybillExternal/updateExternalWaybillCallBack"}, consumes={"application/json"})
    public ItemResult<Void> updateExternalWaybillCallBack(@RequestBody ItemResult<ERPWaybillDTO> var1);

    @PostMapping( path={"/waybillExternal/closeExternalWaybillCallBack"}, consumes={"application/json"})
    public ItemResult<Void> closeExternalWaybillCallBack(@RequestBody ItemResult<ERPWaybillDTO> var1);

    @PostMapping( path={"/waybillExternal/createECWaybill"}, consumes={"application/json"})
    public ItemResult<String> createECWaybill(@RequestBody ERPWaybillDTO var1);

    @PostMapping( path={"/waybillExternal/updateECWaybill"}, consumes={"application/json"})
    public ItemResult<Void> updateECWaybill(@RequestBody ERPWaybillDTO var1);

    @PostMapping( path={"/waybillExternal/closeECWaybill"}, consumes={"application/json"})
    public ItemResult<Void> closeECWaybill(@RequestBody ERPWaybillDTO var1);

    @PostMapping( path={"/waybillExternal/handlerExternalExecuteStatus"}, consumes={"application/json"})
    public ItemResult<Void> handlerExternalExecuteStatus(@RequestBody ERPWaybillExecuteDTO var1);

    @PostMapping( path={"/waybillExternal/externalSendOutCallBack"}, consumes={"application/json"})
    public ItemResult<Void> externalSendOutCallBack(@RequestBody ItemResult<ERPWaybillDTO> var1);
}

