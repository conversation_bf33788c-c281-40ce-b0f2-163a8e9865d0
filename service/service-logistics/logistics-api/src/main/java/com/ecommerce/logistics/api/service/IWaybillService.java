
package com.ecommerce.logistics.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.attachment.AttListDTO;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.pickingbill.PickingBillDTO;
import com.ecommerce.logistics.api.dto.warehouse.WarehouseOptionDTO;
import com.ecommerce.logistics.api.dto.waybill.AppWaybillListQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.AppWaybillQueryConditionsMapDTO;
import com.ecommerce.logistics.api.dto.waybill.AppWaybillStatisticsDTO;
import com.ecommerce.logistics.api.dto.waybill.ArriveWarehouseDTO;
import com.ecommerce.logistics.api.dto.waybill.BatchPassCheckDTO;
import com.ecommerce.logistics.api.dto.waybill.CancelReasonDTO;
import com.ecommerce.logistics.api.dto.waybill.CancelWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.CarrierWaybillListDTO;
import com.ecommerce.logistics.api.dto.waybill.CarrierWaybillListQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.CarryWaybillSubmitDTO;
import com.ecommerce.logistics.api.dto.waybill.ChangeCarriageDTO;
import com.ecommerce.logistics.api.dto.waybill.ChangePriceDTO;
import com.ecommerce.logistics.api.dto.waybill.CheckWaybillListQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.CloseWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.CompleteWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.DiscardWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.DistrictListDTO;
import com.ecommerce.logistics.api.dto.waybill.DriverCancelWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.DriverConfirmDTO;
import com.ecommerce.logistics.api.dto.waybill.DriverDeliveryDTO;
import com.ecommerce.logistics.api.dto.waybill.DriverWaybillAssignDTO;
import com.ecommerce.logistics.api.dto.waybill.DriverWaybillListQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.EnterFactoryDTO;
import com.ecommerce.logistics.api.dto.waybill.EvaluateWaybillListDTO;
import com.ecommerce.logistics.api.dto.waybill.LeaveWarehouseDTO;
import com.ecommerce.logistics.api.dto.waybill.LocationDTO;
import com.ecommerce.logistics.api.dto.waybill.MergeWaybillListDTO;
import com.ecommerce.logistics.api.dto.waybill.MergeWaybillListQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.MonitorTimeDTO;
import com.ecommerce.logistics.api.dto.waybill.NotifyLeaveWarehouseDTO;
import com.ecommerce.logistics.api.dto.waybill.PassCheckDTO;
import com.ecommerce.logistics.api.dto.waybill.PublishWaybillListDTO;
import com.ecommerce.logistics.api.dto.waybill.PublishWaybillListQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.ReassignmentVehicleDTO;
import com.ecommerce.logistics.api.dto.waybill.ReceiverInfoDTO;
import com.ecommerce.logistics.api.dto.waybill.RepublishWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.SeckillScanQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.SellerAssignWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.SnatchWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.SnatchedWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.SnatchedWaybillQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.TradeWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.TradeWaybillDetailDTO;
import com.ecommerce.logistics.api.dto.waybill.UploadCertificateDTO;
import com.ecommerce.logistics.api.dto.waybill.UserSeckillWaybillListDTO;
import com.ecommerce.logistics.api.dto.waybill.UserSeckillWaybillQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.WaitCheckWaybillListDTO;
import com.ecommerce.logistics.api.dto.waybill.WarehouseAdminProccessedDTO;
import com.ecommerce.logistics.api.dto.waybill.WarehouseAdminProccessedQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.WarehouseAdminWaitDeliveryDTO;
import com.ecommerce.logistics.api.dto.waybill.WarehouseAdminWaitDeliveryQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillAssignDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillAssignListDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillAssignListQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillCodeDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillDetailDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillLocationDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillLocationQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillMonitorStatusNotifyDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillStatusOption;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name="logistics")
public interface IWaybillService {
    @PostMapping( path={"/waybill/getLocation"}, consumes={"application/json"})
    public ItemResult<WaybillLocationDTO> getLocation(@RequestParam(value="waybillId") String var1);

    @PostMapping( path={"/waybill/changeCarriage"}, consumes={"application/json"})
    public ItemResult<Void> changeCarriage(@RequestBody ChangeCarriageDTO var1);

    @PostMapping( path={"/waybill/completeWaybill"}, consumes={"application/json"})
    public ItemResult<Void> completeWaybill(@RequestBody CompleteWaybillDTO var1);

    @PostMapping( path={"/waybill/assignWaybill"}, consumes={"application/json"})
    public ItemResult<Void> assignWaybill(@RequestBody WaybillAssignDTO var1);

    @PostMapping( path={"/waybill/passCheck"}, consumes={"application/json"})
    public ItemResult<Void> passCheck(@RequestBody PassCheckDTO var1);

    @PostMapping( path={"/waybill/batchPassCheck"}, consumes={"application/json"})
    public ItemResult<Void> batchPassCheck(@RequestBody BatchPassCheckDTO var1);

    @PostMapping( path={"/waybill/snatchWaybill"}, consumes={"application/json"})
    public ItemResult<Void> snatchWaybill(@RequestBody SnatchWaybillDTO var1);

    @PostMapping( path={"/waybill/arriveWarehouse"}, consumes={"application/json"})
    public ItemResult<Void> arriveWarehouse(@RequestBody ArriveWarehouseDTO var1);

    @PostMapping( path={"/waybill/getWaybillDetail"}, consumes={"application/json"})
    public ItemResult<WaybillDetailDTO> getWaybillDetail(@RequestParam(value="waybillId") String var1);

    @PostMapping( path={"/waybill/getWaybillDetailByNum"}, consumes={"application/json"})
    public ItemResult<WaybillDetailDTO> getWaybillDetailByNum(@RequestParam(value="waybillNum") String var1);

    @PostMapping( path={"/waybill/closeWaybill"}, consumes={"application/json"})
    public ItemResult<Void> closeWaybill(@RequestBody CloseWaybillDTO var1);

    @PostMapping( path={"/waybill/removeWaybill"}, consumes={"application/json"})
    public ItemResult<Void> removeWaybill(@RequestBody DriverCancelWaybillDTO var1);

    @PostMapping( path={"/waybill/changePrice"}, consumes={"application/json"})
    public ItemResult<Void> changePrice(@RequestBody ChangePriceDTO var1);

    @PostMapping( path={"/waybill/selectMainWaybillInfoByMainWaybillId"}, consumes={"application/json"})
    public ItemResult<WaybillDetailDTO> selectMainWaybillInfoByMainWaybillId(@RequestParam(value="waybillId") String var1);

    @PostMapping( path={"/waybill/selectReceiverInfoByMainWaybillId"}, consumes={"application/json"})
    public ItemResult<List<ReceiverInfoDTO>> selectReceiverInfoByMainWaybillId(@RequestParam(value="waybillId") String var1);

    @PostMapping( path={"/waybill/selectSeckillWarehouseByUserId"}, consumes={"application/json"})
    public ItemResult<List<WarehouseOptionDTO>> selectSeckillWarehouseByUserId(@RequestParam(value="userId") String var1);

    @PostMapping( path={"/waybill/queryWaybillAssignList"}, consumes={"application/json"})
    public ItemResult<PageData<WaybillAssignListDTO>> queryWaybillAssignList(@RequestBody PageQuery<WaybillAssignListQueryDTO> var1);

    @PostMapping( path={"/waybill/queryCheckWaybillList"}, consumes={"application/json"})
    public ItemResult<PageData<WaitCheckWaybillListDTO>> queryCheckWaybillList(@RequestBody PageQuery<CheckWaybillListQueryDTO> var1);

    @PostMapping( path={"/waybill/queryWaybillListByDeliveryNum"}, consumes={"application/json"})
    public ItemResult<List<TradeWaybillDTO>> queryWaybillListByDeliveryNum(@RequestParam(value="deliverySheetNum") String var1);

    @PostMapping( path={"/waybill/inputLeaveWarehouseQuantity"}, consumes={"application/json"})
    public ItemResult<Void> inputLeaveWarehouseQuantity(@RequestBody LeaveWarehouseDTO var1);

    @PostMapping( path={"/waybill/queryWaybillsByPickingBillIds"}, consumes={"application/json"})
    public ItemResult<List<WaybillDTO>> queryWaybillsByPickingBillIds(@RequestBody List<String> var1);

    @PostMapping( path={"/waybill/getWaybillLocationByCon"}, consumes={"application/json"})
    public ItemResult<List<LocationDTO>> getWaybillLocationByCon(@RequestBody WaybillLocationQueryDTO var1);

    @PostMapping( path={"/waybill/changeCancelReason"}, consumes={"application/json"})
    public ItemResult<Void> changeCancelReason(@RequestBody CancelReasonDTO var1);

    @PostMapping( path={"/waybill/selectSeckillScanWaybillList"}, consumes={"application/json"})
    public ItemResult<PageData<UserSeckillWaybillListDTO>> selectSeckillScanWaybillList(@RequestBody PageQuery<SeckillScanQueryDTO> var1);

    @PostMapping( path={"/waybill/changeStatusToWaitReceiveById"}, consumes={"application/json"})
    public ItemResult<Void> changeStatusToWaitReceiveById(@RequestParam(value="waybillId") String var1);

    @PostMapping( path={"/waybill/completeWaybillBySubWaybillIds"}, consumes={"application/json"})
    public ItemResult<Void> completeWaybillBySubWaybillIds(@RequestBody CompleteWaybillDTO var1);

    @PostMapping( path={"/waybill/queryPublishWaybillList"}, consumes={"application/json"})
    public ItemResult<PageData<PublishWaybillListDTO>> queryPublishWaybillList(@RequestBody PageQuery<PublishWaybillListQueryDTO> var1);

    @PostMapping( path={"/waybill/queryDriverDeliveryStatus"}, consumes={"application/json"})
    public ItemResult<DriverDeliveryDTO> queryDriverDeliveryStatus(@RequestParam(value="driverId") String var1);

    @PostMapping( path={"/waybill/queryWaybillsByWaybillIds"}, consumes={"application/json"})
    public ItemResult<List<WaybillDTO>> queryWaybillsByWaybillIds(@RequestBody List<String> var1);

    @PostMapping( path={"/waybill/getWarehouseAdminWaitDelivery"}, consumes={"application/json"})
    public ItemResult<PageData<WarehouseAdminWaitDeliveryDTO>> getWarehouseAdminWaitDelivery(@RequestBody PageQuery<WarehouseAdminWaitDeliveryQueryDTO> var1);

    @PostMapping( path={"/waybill/changeArriveDestinationTime"}, consumes={"application/json"})
    public ItemResult<Void> changeArriveDestinationTime(@RequestParam(value="waybillId") String var1);

    @PostMapping( path={"/waybill/republishWaybillByPlatform"}, consumes={"application/json"})
    public ItemResult<Void> republishWaybillByPlatform(@RequestBody RepublishWaybillDTO var1);

    @PostMapping( path={"/waybill/queryMergeWaybillList"}, consumes={"application/json"})
    public ItemResult<PageData<MergeWaybillListDTO>> queryMergeWaybillList(@RequestBody PageQuery<MergeWaybillListQueryDTO> var1);

    @PostMapping( path={"/waybill/getCarriersByType"}, consumes={"application/json"})
    public ItemResult<Void> getCarriersByType(@RequestParam(value="carrierType") String var1);

    @PostMapping( path={"/waybill/selectUserSeckillWaybillList"}, consumes={"application/json"})
    public ItemResult<PageData<UserSeckillWaybillListDTO>> selectUserSeckillWaybillList(@RequestBody PageQuery<UserSeckillWaybillQueryDTO> var1);

    @PostMapping( path={"/waybill/cancelWaybillByPlatform"}, consumes={"application/json"})
    public ItemResult<Void> cancelWaybillByPlatform(@RequestBody CancelWaybillDTO var1);

    @PostMapping( path={"/waybill/querySellerWaybillList"}, consumes={"application/json"})
    public ItemResult<PageData<PublishWaybillListDTO>> querySellerWaybillList(@RequestBody PageQuery<PublishWaybillListQueryDTO> var1);

    @PostMapping( path={"/waybill/queryAppWaybillList"}, consumes={"application/json"})
    public ItemResult<PageData<PublishWaybillListDTO>> queryAppWaybillList(@RequestBody PageQuery<AppWaybillListQueryDTO> var1);

    @PostMapping( path={"/waybill/statisticsAppWaybill"}, consumes={"application/json"})
    public ItemResult<AppWaybillStatisticsDTO> statisticsAppWaybill(@RequestBody AppWaybillListQueryDTO var1);

    @PostMapping( path={"/waybill/selectAppWaybillQueryConditions"}, consumes={"application/json"})
    public ItemResult<AppWaybillQueryConditionsMapDTO> selectAppWaybillQueryConditions(@RequestBody AppWaybillListQueryDTO var1);

    @PostMapping( path={"/waybill/queryWaybillsByDispatchIds"}, consumes={"application/json"})
    public ItemResult<List<WaybillDTO>> queryWaybillsByDispatchIds(@RequestBody List<String> var1);

    @PostMapping( path={"/waybill/changeStatusToCanceledById"}, consumes={"application/json"})
    public ItemResult<Void> changeStatusToCanceledById(@RequestParam(value="waybillId") String var1);

    @PostMapping( path={"/waybill/assignWaybillBySeller"}, consumes={"application/json"})
    public ItemResult<Void> assignWaybillBySeller(@RequestBody SellerAssignWaybillDTO var1);

    @PostMapping( path={"/waybill/selectUserSeckilledWaybillList"}, consumes={"application/json"})
    public ItemResult<PageData<UserSeckillWaybillListDTO>> selectUserSeckilledWaybillList(@RequestBody PageQuery<DriverWaybillListQueryDTO> var1);

    @PostMapping( path={"/waybill/changeBeginCarryTime"}, consumes={"application/json"})
    public ItemResult<Void> changeBeginCarryTime(@RequestParam(value="waybillId") String var1);

    @PostMapping( path={"/waybill/changeMonitorArriveTime"}, consumes={"application/json"})
    public ItemResult<Void> changeMonitorArriveTime(@RequestBody MonitorTimeDTO var1);

    @PostMapping( path={"/waybill/waybillMonitorStatusNotify"}, consumes={"application/json"})
    public ItemResult<Void> waybillMonitorStatusNotify(@RequestBody WaybillMonitorStatusNotifyDTO var1);

    @PostMapping( path={"/waybill/selectSeckillDistrictsByUserId"}, consumes={"application/json"})
    public ItemResult<List<DistrictListDTO>> selectSeckillDistrictsByUserId(@RequestParam(value="userId") String var1);

    @PostMapping( path={"/waybill/getWarehouseAdminProccessed"}, consumes={"application/json"})
    public ItemResult<PageData<WarehouseAdminProccessedDTO>> getWarehouseAdminProccessed(@RequestBody PageQuery<WarehouseAdminProccessedQueryDTO> var1);

    @PostMapping( path={"/waybill/carrierAssignWaybillToDriver"}, consumes={"application/json"})
    public ItemResult<Void> carrierAssignWaybillToDriver(@RequestBody DriverWaybillAssignDTO var1);

    @PostMapping( path={"/waybill/confirmWaybillByDriver"}, consumes={"application/json"})
    public ItemResult<Void> confirmWaybillByDriver(@RequestBody DriverConfirmDTO var1);

    @PostMapping( path={"/waybill/queryCarrierWaybillList"}, consumes={"application/json"})
    public ItemResult<PageData<CarrierWaybillListDTO>> queryCarrierWaybillList(@RequestBody PageQuery<CarrierWaybillListQueryDTO> var1);

    @PostMapping( path={"/waybill/queryTradeWaybillDetail"}, consumes={"application/json"})
    public ItemResult<TradeWaybillDetailDTO> queryTradeWaybillDetail(@RequestParam(value="waybillId") String var1);

    @PostMapping( path={"/waybill/queryTradeWaybillDetailByNum"}, consumes={"application/json"})
    public ItemResult<TradeWaybillDetailDTO> queryTradeWaybillDetailByNum(@RequestParam(value="waybillNum") String var1);

    @PostMapping( path={"/waybill/querySnatchedWaybillList"}, consumes={"application/json"})
    public ItemResult<List<SnatchedWaybillDTO>> querySnatchedWaybillList(@RequestBody SnatchedWaybillQueryDTO var1);

    @PostMapping( path={"/waybill/queryEvaluateWaybillList"}, consumes={"application/json"})
    public ItemResult<List<EvaluateWaybillListDTO>> queryEvaluateWaybillList(@RequestBody List<String> var1);

    @PostMapping( path={"/waybill/queryPickingListByWaybillNum"}, consumes={"application/json"})
    public ItemResult<List<PickingBillDTO>> queryPickingListByWaybillNum(@RequestBody List<String> var1);

    @PostMapping( path={"/waybill/queryDeliverySheetNumByWaybillId"}, consumes={"application/json"})
    public ItemResult<List<WaybillCodeDTO>> queryDeliverySheetNumByWaybillId(@RequestBody List<String> var1);

    @PostMapping( path={"/waybill/notifyLeaveWarehouse"}, consumes={"application/json"})
    public ItemResult<Void> notifyLeaveWarehouse(@RequestBody NotifyLeaveWarehouseDTO var1);

    @PostMapping( path={"/waybill/queryWaybillStatusByNum"}, consumes={"application/json"})
    public ItemResult<List<WaybillStatusOption>> queryWaybillStatusByNum(@RequestBody List<String> var1);

    @PostMapping( path={"/waybill/enterFactory"}, consumes={"application/json"})
    public ItemResult<Void> enterFactory(@RequestBody EnterFactoryDTO var1);

    @PostMapping( path={"/waybill/withdrawEnterFactory"}, consumes={"application/json"})
    public ItemResult<Void> withdrawEnterFactory(@RequestBody EnterFactoryDTO var1);

    @PostMapping( path={"/waybill/queryWarehouseIdByBuyerId"}, consumes={"application/json"})
    public ItemResult<List<String>> queryWarehouseIdByBuyerId(@RequestParam(value="buyerId") String var1);

    @PostMapping( path={"/waybill/reassignmentVehicle"}, consumes={"application/json"})
    public ItemResult<Void> reassignmentVehicle(@RequestBody ReassignmentVehicleDTO var1);

    @PostMapping( path={"/waybill/discardWaybill"}, consumes={"application/json"})
    public ItemResult<Void> discardWaybill(@RequestBody DiscardWaybillDTO var1);

    @PostMapping( path={"/waybill/uploadCertificate"}, consumes={"application/json"})
    public ItemResult<Void> uploadCertificate(@RequestBody UploadCertificateDTO var1);

    @PostMapping( path={"/waybill/findWaybillCertificate"}, consumes={"application/json"})
    public ItemResult<List<AttListDTO>> findWaybillCertificate(@RequestParam(value="waybillId") String var1);

    @PostMapping( path={"/waybill/selectCarryWaybill"}, consumes={"application/json"})
    public ItemResult<CarryWaybillSubmitDTO> selectCarryWaybill(@RequestParam(value="waybillNum") String var1);
}

