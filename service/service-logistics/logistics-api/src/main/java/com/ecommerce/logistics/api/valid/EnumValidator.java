
package com.ecommerce.logistics.api.valid;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import jakarta.validation.Constraint;
import jakarta.validation.Payload;

@Documented
@Retention(value=RetentionPolicy.RUNTIME)
@Target(value={ElementType.FIELD, ElementType.PARAMETER, ElementType.METHOD})
@Constraint(validatedBy={EnumValidatorService.class})
public @interface EnumValidator {
    public int[] value() default {};

    public String message() default "参数检验失败";

    public Class<?>[] groups() default {};

    public Class<? extends Payload>[] payload() default {};
}

