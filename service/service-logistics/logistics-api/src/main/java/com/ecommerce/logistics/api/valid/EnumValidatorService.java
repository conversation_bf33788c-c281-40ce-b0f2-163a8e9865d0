
package com.ecommerce.logistics.api.valid;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class EnumValidatorService
implements ConstraintValidator<EnumValidator, Integer> {
    private int[] values;

    public void initialize(EnumValidator enumValidator) {
        this.values = enumValidator.value();
    }

    public boolean isValid(Integer value, ConstraintValidatorContext constraintValidatorContext) {
        boolean isValid = false;
        if (value == null) {
            return true;
        }
        for (int i = 0; i < this.values.length; ++i) {
            if (this.values[i] != value) continue;
            isValid = true;
            break;
        }
        return isValid;
    }
}

