<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.ecommerce</groupId>
		<artifactId>service-logistics</artifactId>
		<version>2.1.4-RELEASE</version>
	</parent>
	<artifactId>logistics</artifactId>
	<packaging>jar</packaging>
	<version>2.1.4-RELEASE</version>
	<name>logistics</name>
	<description>Logistics Service</description>
	
	<properties>
        <docker.deploy.version>${project.version}</docker.deploy.version>
    </properties>

	<dependencies>
		<dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-config</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-eureka-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bus-amqp</artifactId>
        </dependency>
        <dependency>
			<groupId>com.ecommerce</groupId>
			<artifactId>service-common</artifactId>
		</dependency>
		<dependency>

			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-aop</artifactId>
		</dependency>

		<!--session同步-->
		<dependency>
			<groupId>org.springframework.session</groupId>
			<artifactId>spring-session-data-redis</artifactId>
		</dependency>


		<dependency>
			<groupId>ch.qos.logback</groupId>
			<artifactId>logback-classic</artifactId>
			<scope>runtime</scope>
		</dependency>

		<dependency>
			<groupId>ch.qos.logback</groupId>
			<artifactId>logback-core</artifactId>
			<scope>runtime</scope>
		</dependency>

		<dependency>
			<groupId>com.xuxueli</groupId>
			<artifactId>xxl-job-core</artifactId>
		</dependency>



		<dependency>
			<groupId>com.ecommerce</groupId>
			<artifactId>logistics-api</artifactId>
		</dependency>
		<dependency>
			<groupId>com.ecommerce</groupId>
			<artifactId>member-api</artifactId>
		</dependency>

		<dependency>
			<groupId>com.ecommerce</groupId>
			<artifactId>open-api</artifactId>
		</dependency>

		<dependency>
			<groupId>com.ecommerce</groupId>
			<artifactId>pay-api</artifactId>
		</dependency>
		
		<dependency>
			<groupId>com.ecommerce</groupId>
			<artifactId>goods-api</artifactId>
		</dependency>
		<dependency>
			<groupId>org.powermock</groupId>
			<artifactId>powermock-module-junit4</artifactId>
            <version>${powermock.version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.powermock</groupId>
			<artifactId>powermock-api-mockito</artifactId>
            <version>${powermock.version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.powermock</groupId>
			<artifactId>powermock-module-junit4-rule-agent</artifactId>
            <version>${powermock.version}</version>
		</dependency>
		<dependency>
			<groupId>com.ecommerce</groupId>
			<artifactId>generator</artifactId>
            <version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.squareup.okhttp3</groupId>
			<artifactId>okhttp</artifactId>
		</dependency>
		<!-- httpclient -->
		<dependency>
            <groupId>org.apache.httpcomponents.client5</groupId>
            <artifactId>httpclient5</artifactId>
        </dependency>
		<dependency>
			<groupId>com.ecommerce</groupId>
			<artifactId>kafka-spring-boot-starter</artifactId>
		</dependency>
		<dependency>
			<artifactId>order-api</artifactId>
			<groupId>com.ecommerce</groupId>
		</dependency>
		<dependency>
			<groupId>com.ecommerce</groupId>
			<artifactId>base-api</artifactId>
		</dependency>
		<dependency>
            <groupId>com.ecommerce</groupId>
            <artifactId>storage-api</artifactId>
		</dependency>
		<dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
		</dependency>
		<dependency>
			<groupId>tk.mybatis</groupId>
			<artifactId>mapper</artifactId>

        </dependency>
		<dependency>
			<groupId>org.eweb4j</groupId>
			<artifactId>fel</artifactId>
			<version>0.8</version>
		</dependency>
	</dependencies>
	<build>
		<finalName>logistics</finalName>
		<resources>
			<resource>
				<directory>src/main/resources</directory>
			</resource>
		</resources>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
		</plugins>
	</build>

</project>
