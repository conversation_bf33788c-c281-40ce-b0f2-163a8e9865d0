package com.ecommerce.logistics;

import com.ecommerce.common.annotation.ExcludeFromComponetScan;
import com.ecommerce.kafka.annotation.EnableKafka;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import tk.mybatis.spring.annotation.MapperScan;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

@SpringBootApplication
@EnableFeignClients(value = {
		"com.ecommerce.member.api.service",
		"com.ecommerce.order.api.service",
		"com.ecommerce.base.api.service",
		"com.ecommerce.open.api.service",
        "com.ecommerce.pay.api.v2.service",
        "com.ecommerce.storage.api.service",
		"com.ecommerce.goods.api"
})
@ComponentScan(value = {"com.ecommerce.logistics",
        "com.ecommerce.common.config",
		"com.ecommerce.common.service",
        "com.ecommerce.common.filter"
},
		excludeFilters = {@ComponentScan.Filter(type = FilterType.ANNOTATION, value = ExcludeFromComponetScan.class)})
@MapperScan("com.ecommerce.logistics.dao")
@EnableTransactionManagement
@EnableKafka
@EnableDiscoveryClient
public class LogisticsApplication {

	public static void main(String[] args) {
		SpringApplication.run(LogisticsApplication.class, args);
	}


	@Bean(name = "firstParamKeyGenerator")
    public KeyGenerator firstParamKeyGenerator(){
        return (target, method, params) -> {
            StringBuilder sb = new StringBuilder();
            sb.append(params[0].toString());
            return sb.toString();
        };
	}

	@Bean("myTaskAsyncPool")
	public Executor myTaskAsyncPool() {
		ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
		//核心线程池大小
		executor.setCorePoolSize(20);
		//最大线程数
		executor.setMaxPoolSize(40);
		//队列容量
		executor.setQueueCapacity(300);
		//活跃时间
		executor.setKeepAliveSeconds(50);
		//线程名字前缀
		executor.setThreadNamePrefix("logisticsExecutor-");

		// setRejectedExecutionHandler：当pool已经达到max size的时候，如何处理新任务
		// CallerRunsPolicy：不在新线程中执行任务，而是由调用者所在的线程来执行
		executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
		executor.initialize();
		return executor;
	}
}
