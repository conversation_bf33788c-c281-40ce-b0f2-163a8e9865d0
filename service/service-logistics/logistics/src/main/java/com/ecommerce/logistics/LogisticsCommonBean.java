package com.ecommerce.logistics;

/**
 * @Auther: colu
 * @Date: 2021-01-05 15:09
 * @Description: 物流通用实例
 */
public class LogisticsCommonBean {

    private LogisticsCommonBean(){
        throw new IllegalStateException("LogisticsCommonBean class");
    }
    /**
     * 任意承运商
     */
    public static final String ANY_CARRIER = "ANY_CARRIER";

    // 背靠背委托单的承运方
    public static final String PROXY_DELIVERY = "VIRTUAL_PROXY_CARRIER";

    // 平台配送的承运方
    public static final String PLATFORM_DELIVERY = "PLATFORM_ID";

    public static final String PLATFORM_ID = "PLATFORM_ID";

    public static final String SOCIETY_CARRIER_ID = "SOCIETY_CARRIER_ID";

    public static final String UN_FOUND_CARRIAGE_RULE_ID = "UN_FOUND_CARRIAGE_RULE_ID";

    // 平台配送的承运方名称
    public static final String PLATFORM_DELIVERY_NAME = "平台";

    //委托单的前缀
    public static final String DELIVERY_NUM_PRE = "W";

    //委托单层级分割符
    public static final String DELIVERY_LEVEL_SEP = ",";

    //泵信息分隔符
    public static final String PUMP_SEP = ",";

    //程序替代的操作人
    public static final String SYSTEM_ID = "system";

    //填充的车型
    public static final String FILL_VEHICLE_TYPE = "999999999";

    // 运单单一报警延时队列
    public static final String UNI_WARN_DELAYED_QUEUE = "logistics:LOGISTICS_UNI_WARN_DELAYED_QUEUE";

    //运单异常延时队列
    public static final String ERP_EXCEPTION_QUEUE = "logistics:ERP_EXCEPTION_WAYBILL_QUEUE";

    //自动完成方法名
    public static final String AUTO_COMPLETE_METHOD = "autoComplete";

    /**
     * 待自动完成的集合
     */
    public static final String WAIT_AUTO_COMPLETE_SET = "logistics:LOGISTICS_WAIT_AUTO_COMPLETE_SET";

    /**
     * 待智能调度的委托单ID集合的key前缀
     */
    public static final String WAIT_AUTO_DISPATCH_SET_PREFIX = "logistics:LOGISTICS_WAIT_AUTO_DISPATCH_SET_PREFIX:";

    /**
     * 需要船长接单: need_ship_confirm: 0-不需要, 1-需要; 默认需要
     */
    public static final String MEMBER_FLAG_NEED_SHIP_CONFIRM = "need_ship_confirm";

    /**
     * 需要船长到港: need_ship_arrival: 0-需要, 1-不需要; 默认不需要
     */
    public static final String MEMBER_FLAG_NEED_SHIP_ARRIVAL = "need_ship_arrival";

}
