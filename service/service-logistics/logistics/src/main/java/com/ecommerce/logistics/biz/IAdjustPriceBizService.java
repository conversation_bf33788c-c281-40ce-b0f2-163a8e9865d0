package com.ecommerce.logistics.biz;

import com.ecommerce.logistics.api.dto.adjust.AdjustMemberDTO;
import com.ecommerce.logistics.api.dto.adjust.AdjustPriceResultDTO;
import com.ecommerce.logistics.api.dto.adjust.AdjustQueryDTO;
import com.ecommerce.logistics.api.dto.adjust.AdjustShipBillDTO;
import com.ecommerce.logistics.api.dto.adjust.AdjustTakeInfoDTO;

import java.util.List;

public interface IAdjustPriceBizService
{
    public List<AdjustMemberDTO> getAdjustMemberList(AdjustQueryDTO query);

    public List<AdjustShipBillDTO> getAdjustPriceShipBillList(AdjustQueryDTO query);

    public boolean saveAdjustPriceResult(AdjustPriceResultDTO dto);

    /**
     * 基于发货单的混凝土单价修改
     * @param adjustTakeInfoDTOList
     * @return
     */
    List<String> adjustConcreteByTakeCode(List<AdjustTakeInfoDTO> adjustTakeInfoDTOList);
}
