package com.ecommerce.logistics.biz;

import com.ecommerce.logistics.api.dto.driver.AssignDriverLogAddDTO;
import com.ecommerce.logistics.api.dto.driver.AssignDriverLogCondDTO;
import com.ecommerce.logistics.api.dto.driver.AssignDriverLogResDTO;
import com.ecommerce.logistics.dao.vo.AssignDriverLog;
import com.ecommerce.logistics.dao.vo.Vehicle;

import java.util.List;

/**
 * @Auther: colu
 * @Date: 2020-05-19 17:36
 * @Description: IAssignDriverLogBizService
 */
public interface IAssignDriverLogBizService {

    /**
     * 条件查询指派司机列表
     * @param assignDriverLogCondDTO
     * @return
     */
    List<AssignDriverLogResDTO> queryByCond(AssignDriverLogCondDTO assignDriverLogCondDTO);

    /**
     * 自提单车辆和司机的指派记录更新
     * @param assignDriverLogAddDTO
     */
    String updateSelfPickAssignLog(AssignDriverLogAddDTO assignDriverLogAddDTO);

    /**
     * 自提单指派的车辆更新(无则添加,有则更新)
     * @param assignDriverLogAddDTO
     */
    Vehicle updateSelfAssignVehicleInfo(AssignDriverLogAddDTO assignDriverLogAddDTO);

    /**
     * 自提单指派的司机记录更新(无则添加,有则更新)
     * @param assignDriverLogAddDTO
     */
    AssignDriverLog updateSelfAssignDriverInfo(AssignDriverLogAddDTO assignDriverLogAddDTO);
}
