package com.ecommerce.logistics.biz;

import com.ecommerce.logistics.api.dto.attachment.AttListAddDTO;
import com.ecommerce.logistics.api.dto.attachment.AttListDTO;
import com.ecommerce.logistics.api.dto.attachment.AttListQueryDTO;
import com.ecommerce.logistics.api.dto.attachment.AttRemoveDTO;

import java.util.List;

/**
 * @Auther: <EMAIL>
 * @Date: 20/08/2018 10:24
 * @Description:
 */
public interface IAttachmentBizService {
    void saveAttList(AttListAddDTO attListAddDTO);

    void deleteAtt(AttRemoveDTO attRemoveDTO);

    List<AttListDTO> getAttList(AttListQueryDTO attListQueryDTO);
    
    void saveAttachmentRecord(String entryId,String type, String fileName,String fileExt,String bid,String userId);
}
