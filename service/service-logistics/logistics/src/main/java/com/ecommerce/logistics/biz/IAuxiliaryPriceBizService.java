package com.ecommerce.logistics.biz;


import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.shippingroute.AuxiliaryPriceDTO;
import com.ecommerce.logistics.api.dto.shippingroute.AuxiliaryPriceListDTO;
import com.ecommerce.logistics.dao.vo.AuxiliaryPriceInfo;

import java.util.List;

/**
 * 辅助价目表服务
 * Created by hexinhui on 2020/11/4 16:36
 */
public interface IAuxiliaryPriceBizService {

    /**
     * 添加辅助价目表
     * @param dtoList
     * @return
     */
    void addAuxiliaryPrice(List<AuxiliaryPriceDTO> dtoList) throws BizException;

    /**
     * 删除一个辅助价目表的所有运价差数据
     * @param auxiliaryPriceNo
     * @param operatorUserId
     */
    void delAuxiliaryPriceByNo(String belongerId, String auxiliaryPriceNo, String operatorUserId) throws BizException;

    /**
     * 删除辅助价目表里面的某一个运价差
     * @param auxiliaryPriceId
     */
    void delAuxiliaryPrice(String auxiliaryPriceNo, String belongerId, String auxiliaryPriceId, String operatorUserId) throws BizException;

    /**
     * 编辑辅助价目表
     * @param dtoList
     */
    void editAuxiliaryPrice(List<AuxiliaryPriceDTO> dtoList) throws BizException;

    /**
     * 通过辅助价目表编号查看辅助价目表详情
     * @param auxiliaryPriceNo 辅助价目表编号
     * @return
     */
    List<AuxiliaryPriceDTO> queryAuxiliaryPriceDetails(String belongerId, String auxiliaryPriceNo) throws BizException;

    /**
     * 查询辅助价目列表
     * @param pageQuery
     * @return
     */
    PageData<AuxiliaryPriceListDTO> queryAuxiliaryPriceList(PageQuery<AuxiliaryPriceDTO> pageQuery) throws BizException;

    /**
     * 根据编号查询出辅助价目表
     * @param belongerId 所属用户ID
     * @param auxiliaryPriceNo 辅助价目表编号
     * @return
     * @throws BizException
     */
    List<AuxiliaryPriceDTO> queryAuxiliaryByNo(String belongerId, String auxiliaryPriceNo) throws BizException;

    List<AuxiliaryPriceInfo> queryAuxiliaryByNo(String auxiliaryPriceNo);
}
