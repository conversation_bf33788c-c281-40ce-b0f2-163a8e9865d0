package com.ecommerce.logistics.biz;

import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.billEvaluate.BillEvaluateListDTO;
import com.ecommerce.logistics.api.dto.billEvaluate.BillEvaluateQueryDTO;
import com.ecommerce.logistics.api.dto.billEvaluate.BillEvaluateSaveDTO;

/**
 * 物流单据评价服务
 * @Author: <EMAIL>
 * @Date: 2020-09-22 17:57
 */
public interface IBillEvaluateBizService {

    /**
     * 分页查询单据评价记录
     * @param pageQuery 单据分页查询对象
     * @return PageData<BillEvaluateListDTO>
     */
    PageData<BillEvaluateListDTO> queryBillEvaluateList(PageQuery<BillEvaluateQueryDTO> pageQuery);

    /**
     * 添加单据评价记录
     * @param billEvaluateSaveDTO 单据评价记录保存对象
     * @return String
     */
    String addBillEvaluate(BillEvaluateSaveDTO billEvaluateSaveDTO);
}
