package com.ecommerce.logistics.biz;

import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.billEvaluate.BillEvaluateStatisticListDTO;
import com.ecommerce.logistics.api.dto.billEvaluate.BillEvaluateStatisticListQueryDTO;
import com.ecommerce.logistics.api.dto.billEvaluate.BillEvaluateStatisticSaveDTO;
import com.ecommerce.logistics.dao.vo.BillEvaluateStatistic;

import java.util.List;

/**
 * 物流单据评价服务
 * @Author: <EMAIL>
 * @Date: 2020-10-28 14:00
 */
public interface IBillEvaluateStatisticBizService {

    /**
     * 添加评价统计(评价承运商才会添加统计)
     * @param evaluateStatisticSaveDTO
     * @return String
     */
    BillEvaluateStatistic addOrUpdateBillEvaluate(BillEvaluateStatisticSaveDTO evaluateStatisticSaveDTO,int addCountFlag);

    /**
     * 查询评价统计排名
     * @param pageQuery
     * @return String
     */
    PageData<BillEvaluateStatisticListDTO> selectEvaluateStatisticList(PageQuery<BillEvaluateStatisticListQueryDTO> pageQuery);

    /**
     * 根据被评价主体类型查询评分
     * @param evaluatedPersonType
     * @return String
     */
    List<BillEvaluateStatisticListDTO> selectPersonIdAndScoreByType(String evaluatedPersonType);
}
