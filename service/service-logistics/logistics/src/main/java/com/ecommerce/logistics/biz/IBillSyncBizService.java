package com.ecommerce.logistics.biz;

import com.ecommerce.logistics.api.dto.proxybill.SyncTakeInfoSecondaryWaybillDTO;
import com.ecommerce.logistics.dao.vo.Waybill;

/**
 * @Auther: colu
 * @Date: 2020-08-12 17:12
 * @Description: 单据同步服务
 */
public interface IBillSyncBizService {

    /**
     * 二级运单同步生成发货单
     * @param syncTakeInfoSecondaryWaybillDTO
     */
    void syncPrimaryTakeInfoByWaybill(SyncTakeInfoSecondaryWaybillDTO syncTakeInfoSecondaryWaybillDTO);

    /**
     *背靠背 平台: 司机确认/司机抢单时调用
     * 二级的运单确认时通知交易生成一级发货单
     */
     void syncPublishPrimaryWaybill(Waybill waybill);
}
