package com.ecommerce.logistics.biz;

import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.breakdto.BreakBillDTO;
import com.ecommerce.logistics.api.dto.breakdto.BreakDTO;
import com.ecommerce.logistics.api.param.breakparam.BreakAgreeParam;
import com.ecommerce.logistics.api.param.breakparam.BreakBatchCancelParam;
import com.ecommerce.logistics.api.param.breakparam.BreakBillQueryParam;
import com.ecommerce.logistics.api.param.breakparam.BreakFollowParam;
import com.ecommerce.logistics.api.param.breakparam.BreakParam;
import com.ecommerce.logistics.api.param.breakparam.BreakQueryParam;
import com.ecommerce.logistics.api.param.breakparam.BreakRejectParam;
import com.ecommerce.logistics.api.param.breakparam.BreakResponsibleParam;

/**
 * Copyright (C),2020
 *
 * @className: IBreakBizService
 * @author: <EMAIL>
 * @Date: 2020/10/21 3:29 下午
 * @Description:
 */
public interface IBreakBizService {

    /**
     * 分页查询违约列表
     *
     * @param param 入参
     * @return
     */
    PageData<BreakDTO> pageByQueryOption(PageQuery<BreakQueryParam> param);

    /**
     * 新增或编辑违约记录
     *
     * @param breakParam 入参
     * @return
     */
    Boolean addOrUpdateBreak(BreakParam breakParam);


    /**
     * 批量取消违约记录
     *
     * @param breakBatchCancelParam 入参
     * @return
     */
    Boolean batchCancelBreak(BreakBatchCancelParam breakBatchCancelParam);


    /**
     * 违约记录责任归属
     *
     * @param breakResponsibleParam 入参
     * @return
     */
    Boolean responsibleBreak(BreakResponsibleParam breakResponsibleParam);

    /**
     * 违约记录跟进
     *
     * @param breakFollowParam 入参
     * @return
     */
    Boolean followBreak(BreakFollowParam breakFollowParam);

    /**
     * 同意违约申请
     *
     * @param breakAgreeParam 入参
     * @return
     */
    Boolean agreeBreak(BreakAgreeParam breakAgreeParam);


    /**
     * 拒绝违约申请
     *
     * @param breakRejectParam 入参
     * @return
     */
    Boolean rejectBreak(BreakRejectParam breakRejectParam);


    /**
     * 查询违约关联单据数据
     *
     * @param breakBillQueryParam 违约关联单据查询条件
     * @return
     */
    BreakBillDTO queryBreakBill(BreakBillQueryParam breakBillQueryParam);
}
