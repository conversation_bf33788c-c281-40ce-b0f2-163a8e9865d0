package com.ecommerce.logistics.biz;

import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.carriage.CarriageRouteDeleteDTO;
import com.ecommerce.logistics.api.dto.carriage.CarriageRouteDetailDTO;
import com.ecommerce.logistics.api.dto.carriage.CarriageRouteListDTO;
import com.ecommerce.logistics.api.dto.carriage.CarriageRouteQueryDTO;
import com.ecommerce.logistics.api.dto.carriage.CarriageRouteSaveDTO;

import java.util.List;

/**
 * Author: lehu
 * Description:
 * Date: Create in 下午3:18 19/5/29
 */
public interface ICarriageRouteBizService {

    /**
     * 录入运费路线
     * @param  carriageRouteSaveDTO 运费路线保存对象
     * @return String
     */
    String enteringCarriageRoute(CarriageRouteSaveDTO carriageRouteSaveDTO);


    /**
     * 删除运费路线
     * @param carriageRouteDeleteDTO 运费路线删除对象
     */
    void deleteCarriageRoute(CarriageRouteDeleteDTO carriageRouteDeleteDTO);


    /**
     * 编辑运费路线
     * @param carriageRouteSaveDTO 运费路线保存对象
     */
    void editCarriageRoute(CarriageRouteSaveDTO carriageRouteSaveDTO);


    /**
     * 查询运费路线详情
     * @param carriageRouteId 运费路线ID
     */
    CarriageRouteDetailDTO queryCarriageRouteDetail(String carriageRouteId);


    /**
     * 查询运费路线列表
     * @param  pageQuery 运费路线分页查询对象
     * @return List<CarriageRouteListDTO>
     */
    PageData<CarriageRouteListDTO> queryCarriageRouteList(PageQuery<CarriageRouteQueryDTO> pageQuery);

    /**
     * 查询点对点运费路线详情
     * @param queryDTO 运费路线查询对象
     */
    CarriageRouteDetailDTO queryPointCarriageRouteDetail(CarriageRouteQueryDTO queryDTO);

    /**
     * 查询运费路线列表
     */
    List<CarriageRouteListDTO> queryCarriageRouteList(CarriageRouteQueryDTO queryDTO);
}
