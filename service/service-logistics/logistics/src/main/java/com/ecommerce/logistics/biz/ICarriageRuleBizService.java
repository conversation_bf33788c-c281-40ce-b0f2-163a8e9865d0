package com.ecommerce.logistics.biz;

import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.carriagerule.BuyerCarriageRuleDeleteDTO;
import com.ecommerce.logistics.api.dto.carriagerule.BuyerCarriageRuleDetailDTO;
import com.ecommerce.logistics.api.dto.carriagerule.BuyerCarriageRuleListDTO;
import com.ecommerce.logistics.api.dto.carriagerule.BuyerCarriageRuleQueryDTO;
import com.ecommerce.logistics.api.dto.carriagerule.BuyerCarriageRuleSaveDTO;
import com.ecommerce.logistics.api.dto.carriagerule.CarrierCarriageRuleDeleteDTO;
import com.ecommerce.logistics.api.dto.carriagerule.CarrierCarriageRuleDetailDTO;
import com.ecommerce.logistics.api.dto.carriagerule.CarrierCarriageRuleListDTO;
import com.ecommerce.logistics.api.dto.carriagerule.CarrierCarriageRuleQueryDTO;
import com.ecommerce.logistics.api.dto.carriagerule.CarrierCarriageRuleSaveDTO;
import com.ecommerce.logistics.dao.dto.carriage.BuyerCarriageQueryDO;
import com.ecommerce.logistics.dao.dto.carriage.CarrierCarriageQueryDO;
import com.ecommerce.logistics.dao.vo.BuyerCarriageRule;

/**
 * Author: <EMAIL>
 * Description:
 * Date: Create in 上午10:45 18/9/22
 */
public interface ICarriageRuleBizService {

    /**
     * 条件查询承运商运费规则
     * @param carrierCarriageQueryDO 承运商运费规则查询对象
     * @return List<CarrierCarriageRule>
     */
     String queryCarrierCarriageRuleByCondition(CarrierCarriageQueryDO carrierCarriageQueryDO);

    /**
     * 条件查询买家运费规则
     * @param buyerCarriageQueryDO 买家运费规则查询对象
     * @return BuyerCarriageRule
     */
    BuyerCarriageRule queryBuyerCarriageRuleByCondition(BuyerCarriageQueryDO buyerCarriageQueryDO);

    /**
     * 添加承运商运费规则
     * @param carrierCarriageRuleSaveDTO 承运商运费规则保存对象
     * @return String 运费规则ID
     */
    String addCarrierCarriageRule(CarrierCarriageRuleSaveDTO carrierCarriageRuleSaveDTO);

    /**
     * 删除承运商运费规则
     * @param carrierCarriageRuleDeleteDTO 承运商运费规则删除对象
     */
    void deleteCarrierCarriageRule(CarrierCarriageRuleDeleteDTO carrierCarriageRuleDeleteDTO);

    /**
     * 编辑承运商运费规则
     * @param carrierCarriageRuleSaveDTO 承运商运费规则保存对象
     */
    void editCarrierCarriageRule(CarrierCarriageRuleSaveDTO carrierCarriageRuleSaveDTO);

    /**
     * 查询承运商运费规则
     * @param carriageRuleId 承运商运费规则ID
     * @return CarrierCarriageRuleDetailDTO 承运商运费规则详情对象
     */
    CarrierCarriageRuleDetailDTO queryCarrierCarriageRuleDetail(String carriageRuleId);

    /**
     * 查询承运商运费规则列表
     * @param pageQuery 承运商运费规则查询对象
     * @return PageData<CarrierCarriageRuleListDTO> 承运商运费规则列表
     */
    PageData<CarrierCarriageRuleListDTO> queryCarrierCarriageRuleList(PageQuery<CarrierCarriageRuleQueryDTO> pageQuery);

    /**
     * 添加买家运费规则
     * @param buyerCarriageRuleSaveDTO 买家运费规则保存对象
     * @return String 运费规则ID
     */
    String addBuyerCarriageRule(BuyerCarriageRuleSaveDTO buyerCarriageRuleSaveDTO);

    /**
     * 删除买家运费规则
     * @param buyerCarriageRuleDeleteDTO 买家运费规则删除对象
     */
    void deleteBuyerCarriageRule(BuyerCarriageRuleDeleteDTO buyerCarriageRuleDeleteDTO);

    /**
     * 编辑买家运费规则
     * @param buyerCarriageRuleSaveDTO 买家运费规则保存对象
     */
    void editBuyerCarriageRule(BuyerCarriageRuleSaveDTO buyerCarriageRuleSaveDTO);

    /**
     * 查询买家运费规则
     * @param carriageRuleId 买家运费规则ID
     * @return buyerCarriageRuleDetailDTO 买家运费规则详情对象
     */
    BuyerCarriageRuleDetailDTO queryBuyerCarriageRuleDetail(String carriageRuleId);

    /**
     * 查询买家运费规则列表
     * @param pageQuery 买家运费规则查询对象
     * @return PageData<BuyerCarriageRuleListDTO> 买家运费规则列表
     */
    PageData<BuyerCarriageRuleListDTO> queryBuyerCarriageRuleList(PageQuery<BuyerCarriageRuleQueryDTO> pageQuery);
}
