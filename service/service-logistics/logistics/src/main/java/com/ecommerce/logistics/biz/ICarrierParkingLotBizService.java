package com.ecommerce.logistics.biz;

import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.intelligentDispatch.CarrierParkingLotDTO;

public interface ICarrierParkingLotBizService {

    /**
     * 新增承运商停车点
     */
    Boolean add(CarrierParkingLotDTO createDTO);

    /**
     * 编辑承运商停车点
     */
    Boolean edit(CarrierParkingLotDTO updateDTO);

    /**
     * 删除承运商停车点
     */
    Boolean delete(CarrierParkingLotDTO deleteDTO);

    /**
     * 分页查询承运商停车点
     */
    PageData<CarrierParkingLotDTO> pageCarrierParkingLot(PageQuery<CarrierParkingLotDTO> pageQuery);
}
