package com.ecommerce.logistics.biz;

import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.intelligentDispatch.CarrierServiceAreaDTO;

public interface ICarrierServiceAreaBizService {

    /**
     * 新增承运商服务范围
     */
    Boolean add(CarrierServiceAreaDTO createDTO);

    /**
     * 编辑承运商服务范围
     */
    Boolean edit(CarrierServiceAreaDTO updateDTO);

    /**
     * 删除承运商服务范围
     */
    Boolean delete(CarrierServiceAreaDTO deleteDTO);

    /**
     * 分页查询承运商服务范围
     */
    PageData<CarrierServiceAreaDTO> pageCarrierServiceArea(PageQuery<CarrierServiceAreaDTO> pageQuery);

    /**
     * 根据承运商服务范围ID查询详情
     */
    CarrierServiceAreaDTO queryDetailById(String serviceAreaId);
}
