package com.ecommerce.logistics.biz;

import com.ecommerce.logistics.api.dto.intelligentDispatch.CarrierServiceAreaMapDTO;

import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface ICarrierServiceAreaMapBizService {

    /**
     * 根据服务范围ID查询映射DTO列表
     */
    List<CarrierServiceAreaMapDTO> queryMapDTOListByServiceAreaId(String serviceAreaId);

    /**
     * 根据服务范围ID集合查询ID和列表映射
     */
    Map<String, List<CarrierServiceAreaMapDTO>> queryMapByServiceAreaIds(Collection<String> serviceAreaIds);

    Boolean insertList(List<CarrierServiceAreaMapDTO> mapDTOList, String serviceAreaId, String operatorUserId);

    Boolean deleteList(String serviceAreaId, String operatorUserId);
}
