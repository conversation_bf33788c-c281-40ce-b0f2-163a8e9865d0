package com.ecommerce.logistics.biz;

import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.intelligentDispatch.CarrierTransportCapacityDTO;

public interface ICarrierTransportCapacityBizService {

    /**
     * 新增承运商运力
     */
    Boolean add(CarrierTransportCapacityDTO createDTO);

    /**
     * 编辑承运商运力
     */
    Boolean edit(CarrierTransportCapacityDTO updateDTO);

    /**
     * 删除承运商运力
     */
    Boolean delete(CarrierTransportCapacityDTO deleteDTO);

    /**
     * 分页查询承运商运力
     */
    PageData<CarrierTransportCapacityDTO> pageCarrierTransportCapacity(PageQuery<CarrierTransportCapacityDTO> pageQuery);
}
