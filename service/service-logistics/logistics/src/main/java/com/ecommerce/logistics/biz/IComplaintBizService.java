package com.ecommerce.logistics.biz;

import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.complaint.ComplaintDTO;
import com.ecommerce.logistics.api.param.complaint.*;

/**
 * Copyright (C),2020
 *
 * @ClassName: IComplaintBizService
 * @author: <EMAIL>
 * @Date: 25/09/2020 16:35
 * @Description:
 */
public interface IComplaintBizService {


    PageData<ComplaintDTO> pageByQueryOption(PageQuery<ComplaintQueryParam> param);

    Boolean addOrUpdateComplaint(ComplaintParam complaintParam);

    Boolean batchDelComplaint(ComplaintBatchDelParam complaintBatchDelParam);

    Boolean batchCancelComplaint(ComplaintBatchCancelParam complaintBatchCancelParam);

    Boolean responsibleComplaint(ComplaintResponsibleParam complaintResponsibleParam);

    Boolean followComplaint(ComplaintFollowParam complaintFollowParam);

    Boolean dealFinishComplaint(ComplaintDealFinishParam complaintDealFinishParam);

    Boolean evaluationComplaint(ComplaintEvaluationParam complaintEvaluationParam);
}
