package com.ecommerce.logistics.biz;

import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.creditStatistic.CreditStatisticAddDTO;
import com.ecommerce.logistics.api.dto.creditStatistic.CreditStatisticListDTO;
import com.ecommerce.logistics.api.dto.creditStatistic.CreditStatisticQueryDTO;
import com.ecommerce.logistics.dao.vo.CreditStatistic;

/**
 * @Description: ICreditStatisticBizService
 * @Author: <EMAIL>
 * @Date: 06/11/2020 10:04
 */
public interface ICreditStatisticBizService {

    /**
     * 添加信用统计
     * @param addDTO
     * @return String
     */
    String addCreditStatistic(CreditStatisticAddDTO addDTO);

    /**
     * 查找信用统计记录
     * @param personId
     * @param personType
     * @return CreditStatisticDTO
     */
    CreditStatistic findCreditStatisticById(String personId,String personType);

    /**
     * 更新次数
     */
    void updateCreditStatisticCount(String personId,Boolean billCount,Boolean breakCount,Boolean complaintCount,String operatorUserId);

    /**
     * 分页查询信用统计记录
     */
    PageData<CreditStatisticListDTO> queryCreditStatisticList(PageQuery<CreditStatisticQueryDTO> pageQuery);
}
