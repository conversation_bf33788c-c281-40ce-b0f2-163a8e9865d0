package com.ecommerce.logistics.biz;

import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.dispatchbill.DispatchAssignDetailDTO;
import com.ecommerce.logistics.api.dto.dispatchbill.DispatchBillAssignAddDTO;
import com.ecommerce.logistics.api.dto.dispatchbill.DispatchBillDTO;
import com.ecommerce.logistics.api.dto.dispatchbill.DispatchBillListDTO;
import com.ecommerce.logistics.api.dto.dispatchbill.DispatchBillListQueryDTO;
import com.ecommerce.logistics.api.dto.dispatchbill.ExportDispatchBillDTO;
import com.ecommerce.logistics.api.dto.dispatchbill.TransportCapacityDTO;
import com.ecommerce.logistics.api.dto.dispatchbill.TransportCapacityQueryDTO;
import com.ecommerce.logistics.api.dto.pickingbill.PickingBillAssignDispatchDTO;
import com.ecommerce.logistics.api.dto.waybill.CarrierDispatchBillListDTO;
import com.ecommerce.logistics.dao.vo.DispatchBill;

import java.math.BigDecimal;
import java.util.List;

/**
 * 
 * @Auther: <EMAIL>
 * @Date: 2018年8月9日 上午11:41:21
 * @Description:
 */
public interface IDispatchBillBizService {
	
	/**
	 * 分页查询调度单列表
	 * @param pageQuery
	 * @return
	 */
	PageData<DispatchBillListDTO> queryDispatchBillList(PageQuery<DispatchBillListQueryDTO> pageQuery);

	/**
	 * 调度单导出
	 * @param dispatchBillListQueryDTO
	 * @return
	 */
	List<ExportDispatchBillDTO> downloadDispatchBillList(DispatchBillListQueryDTO dispatchBillListQueryDTO);
	
	/**
	 * 分页查询调度单列表
	 * @param pageQuery
	 * @return
	 */
	PageData<CarrierDispatchBillListDTO> queryCarrierDispatchBillList(PageQuery<DispatchBillListQueryDTO> pageQuery);

	/**
	 *通过pk查询调度单详情
	 * @param dispatchBillId
	 * @return
	 */
	DispatchBillDTO queryDispatchBillDetail(String dispatchBillId);
	
	/**
	 * 指派调度单(相当于新增运单)
	 * @param assignAddDTO
	 */
	void assignDispatchBill(DispatchBillAssignAddDTO assignAddDTO);
	
	/**
     * 取消调度单
     * @param dispatchBillId 调度单ID
     */
    void cancelDispatchBill(String dispatchBillId, String operatorId,String operatorName);

	/**
	 * 提货单ID查询调度单列表
	 * @param pickingBillId 提货单集合
	 * @return List<DispatchBillDO>
	 */
	List<PickingBillAssignDispatchDTO> selectDispatchBillByPickingBillId(String pickingBillId);

	/**
	 * 提货单ID批量查询调度单列表
	 * @param pickingBillIds 提货单集合
	 * @return List<DispatchBillDO>
	 */
	List<DispatchBillListDTO> selectDispatchBillByPickingBillIds(List<String> pickingBillIds);

	/**
	 * 获取承运商运力信息（最大运力,已指派运力,剩余运力）
	 * @Auther: <EMAIL>
	 * @Date: 2018年8月29日 下午4:57:38
	 * @param carrierId
	 * @param transportCapacityQueryDTO 运力查询对象
	 * @return List<TransportCapacityDTO>
	 *
	 */
	List<TransportCapacityDTO> getCarrierCapacity(TransportCapacityQueryDTO transportCapacityQueryDTO);
	
	/**
	 * 查询调度单指派详情
	 * @Auther: <EMAIL>
	 * @Date: 2018年9月3日 下午8:02:32
	 * @param dispatchBillId
	 * @return
	 */
	DispatchAssignDetailDTO queryDispatchAssignDetail(String dispatchBillId);

	/**
	 * 运单完成后修改调度单完成数量
	 * @param dispatchBillId 调度单ID
	 * @param quantity 数量
	 * @param operatorId 操作人ID
	 * @param operatorName 操作人姓名
	 */
	Boolean updateDispatchCompleteQuantity(String dispatchBillId, BigDecimal quantity, String operatorId, String operatorName);

	/**
	 * 调度单ID查询调度单列表
	 * @param dispatchBillId 调度单号
	 * @return List<DispatchBillDO>
	 */
	DispatchBill selectDispatchBillById(String dispatchBillId);

	/**
	 * 关闭调度单
	 * @Auther: <EMAIL>
	 * @Date: 2018年10月9日 下午2:01:05
	 * @param dispatchBillId
	 * @return
	 */
	Boolean closeDispatchBill(String dispatchBillId,BigDecimal quantity, String operatorId,
			   String operatorName);

	/**
	 * 关闭调度单(原子操作)
	 * @Auther: <EMAIL>
	 * @Date: 2018年10月9日 下午2:01:05
	 * @param dispatchBillId
	 * @return
	 */
	void atomCloseDispatchBill(String dispatchBillId, String operatorId, String operatorName);

	/**
	 * 更新调度单
	 *
	 */
	void updateDispatchBill(DispatchBill dispatchBill);
}
