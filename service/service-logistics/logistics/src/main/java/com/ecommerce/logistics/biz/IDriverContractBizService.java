package com.ecommerce.logistics.biz;

import com.ecommerce.base.api.dto.warehouse.PageQuery;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.contract.*;

/**
 * 司机合同信息服务
 * Created by hexinhui on 2020/10/27 17:35
 */
public interface IDriverContractBizService {

    /**
     * 新增司机合同
     * @param driverContractAddDTO
     * @return 合同主键
     */
    String addDriverContract(DriverContractAddDTO driverContractAddDTO) throws BizException;

    /**
     * 查询司机合同列表
     * @param pageQuery 查询对象
     * @return
     */
    PageData<DriverContractListDTO> queryDriverContractList(PageQuery<DriverContractListQueryDTO> pageQuery) throws BizException;

    /**
     * 上报司机合同
     * @return
     * @throws Exception
     */
    void reportDriverContract(String contractIds, String operatorUserId, String operatorUserName) throws BizException;

}
