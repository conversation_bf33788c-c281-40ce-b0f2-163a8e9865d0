package com.ecommerce.logistics.biz;

import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.externalexception.ExternalExceptionListDTO;
import com.ecommerce.logistics.api.dto.externalexception.ExternalExceptionQueryDTO;
import com.ecommerce.logistics.api.dto.externalexception.ExternalExceptionSaveDTO;
import com.ecommerce.logistics.dao.vo.ExternalExceptionHandle;

import java.util.List;

/**
 * Author: <EMAIL>
 * Description:
 * Date: Create in 下午2:26 19/4/26
 */
public interface IExternalExceptionBizService {

    /**
     * 添加外部异常处理
     * @param externalExceptionSaveDTO 外部异常保存对象
     * @return String 异常处理ID
     */
    ExternalExceptionHandle addExternalExceptionHandle(ExternalExceptionSaveDTO externalExceptionSaveDTO);

    /**
     * 编辑外部异常处理
     * @param externalExceptionHandle 外部异常保存对象
     */
    void editExternalExceptionHandle(ExternalExceptionHandle externalExceptionHandle);

    /**
     * 查询外部异常处理
     * @param externalExceptionHandleId 外部异常处理ID
     */
    ExternalExceptionHandle queryExternalExceptionHandleById(String externalExceptionHandleId);

    /**
     * 查询外部异常处理列表
     * @param pageQuery 分页查询对象
     * @return PageData<ExternalExceptionListDTO>
     */
    PageData<ExternalExceptionListDTO> queryExternalExceptionList(PageQuery<ExternalExceptionQueryDTO> pageQuery);

    /**
     * 查询外部异常处理
     * @param waybillId 运单ID
     * @param methodType 方法类型
     * @return ExternalExceptionHandle
     */
    ExternalExceptionHandle queryExternalExceptionHandleByCondition(String waybillId, String methodType);

    /**
     * 根据发货单号查询创建运单失败的异常记录ID
     * @param deliverySheetNumList
     * @return
     */
    List<String> queryExceptionIdsForCreateWaybillFail(List<String> deliverySheetNumList);

}
