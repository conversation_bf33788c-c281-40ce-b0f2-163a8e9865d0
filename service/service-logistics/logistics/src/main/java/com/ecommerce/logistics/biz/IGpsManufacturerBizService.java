package com.ecommerce.logistics.biz;

import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.gps.GpsManCondDTO;
import com.ecommerce.logistics.api.dto.gps.GpsManufacturerAddDTO;
import com.ecommerce.logistics.api.dto.gps.GpsManufacturerDTO;
import com.ecommerce.logistics.api.dto.gps.GpsManufacturerDeleteDTO;
import com.ecommerce.logistics.api.dto.gps.GpsManufacturerOptionDTO;
import com.ecommerce.logistics.api.dto.gps.GpsManufacturerUpdateDTO;

import java.util.List;

/**
 * @Auther: <EMAIL>
 * @Date: 12/09/2018 11:35
 * @Description:
 */
public interface IGpsManufacturerBizService {

    /**
     * 添加GPS厂商,并返回主键
     * @param gpsManufacturerAddDTO
     * @return
     */
    String addGpsManufacturer(GpsManufacturerAddDTO gpsManufacturerAddDTO);

    /**
     * 所有GPS厂商下拉列表
     * @return
     */
    List<GpsManufacturerOptionDTO> getOptions();

    /**
     * 获取所有的GPS厂商
     * @return
     */
    List<GpsManufacturerDTO> queryAllGpsMan();

    /**
     * 条件查询GPS厂商
     * @param pageQuery
     * @return
     */
    PageData<GpsManufacturerDTO> queryGpsManByCond(PageQuery<GpsManCondDTO> pageQuery);

    /**
     * 主键删除GPS厂商
     * @param gpsManufacturerDeleteDTO
     */
    void removeGpsManufacturerById(GpsManufacturerDeleteDTO gpsManufacturerDeleteDTO);

    /**
     * 主键查询GPS厂商
     * @param gpsManufacturerId
     * @return
     */
    GpsManufacturerDTO queryGpsManufacturerById(String gpsManufacturerId);

    /**
     * 编号查询GPS厂商
     * @param number
     * @return
     */
    GpsManufacturerDTO queryGpsManufacturerByNumber(String number);

    /**
     * 主键修改GPS厂商信息
     * @param gpsManufacturerUpdateDTO
     */
    void modifyGpsManById(GpsManufacturerUpdateDTO gpsManufacturerUpdateDTO);

}
