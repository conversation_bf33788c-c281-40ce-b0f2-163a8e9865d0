package com.ecommerce.logistics.biz;

import com.ecommerce.logistics.api.dto.intelligentDispatch.IntelligentDispatchListDTO;
import com.ecommerce.logistics.api.dto.intelligentDispatch.IntelligentDispatchQueryDTO;
import com.ecommerce.logistics.api.dto.intelligentDispatch.ChangeRuleStatusDTO;

import java.util.List;

/**
 * @Description: IIntelligentDispatchService
 * @Author: <EMAIL>
 * @Date: 2021-04-09 15:18
 */
public interface IIntelligentDispatchBizService {

    /**
     * 查询智能调度规则列表（仅页面列表查询时使用）
     * @param queryDTO
     * @return
     */
    List<IntelligentDispatchListDTO> queryRuleList(IntelligentDispatchQueryDTO queryDTO);

    /**
     * 修改智能调度规则状态
     * @param changeRuleStatusDTO
     */
    void changeRuleStatus(ChangeRuleStatusDTO changeRuleStatusDTO);
}
