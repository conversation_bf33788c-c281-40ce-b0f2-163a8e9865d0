package com.ecommerce.logistics.biz;

import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.ReceiveAddressDTO;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.delivery.InternalAllocationDTO;
import com.ecommerce.logistics.api.dto.waybill.AllocationListQueryDTO;

/**
 * 内部调拨服务
 * Created by hexinhui3 on 2021/8/10 12:00
 */
public interface IInternalAllocationBizService {

    /**
     * 获取调拨信息列表
     * @param pageQuery
     * @return
     */
    PageData<InternalAllocationDTO> queryInternalAllocationList(PageQuery<AllocationListQueryDTO> pageQuery) throws BizException;

    /**
     * 获取调拨信息
     * @param dto
     * @return
     */
    InternalAllocationDTO getInternalAllocation(InternalAllocationDTO dto) throws BizException;

    /**
     * 获取调拨信息
     * @param deliveryBillNum
     * @return
     */
    InternalAllocationDTO getInternalByBillId(String deliveryBillNum) throws BizException;

    /**
     * 保存调拨信息
     * @param dto
     * @return
     */
    ReceiveAddressDTO saveInternalAllocation(InternalAllocationDTO dto) throws BizException;

    /**
     * 修改内部调拨信息
     * @param dto
     * @throws BizException
     */
    void updateAllocation(InternalAllocationDTO dto) throws BizException;

    /**
     * 修改内部调拨的状态
     * @param dto
     * @throws BizException
     */
    void updateInternalPlanStatus(InternalAllocationDTO dto) throws BizException;

}
