package com.ecommerce.logistics.biz;

import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.invoice.ConfirmInvoiceReqDTO;
import com.ecommerce.logistics.api.dto.invoice.FinishInvoiceDTO;
import com.ecommerce.logistics.api.dto.invoice.InvoiceBillCondDTO;
import com.ecommerce.logistics.api.dto.invoice.InvoiceBillListDTO;
import com.ecommerce.logistics.api.dto.invoice.PreviewInvoiceApplyReqDTO;
import com.ecommerce.logistics.api.dto.invoice.PreviewInvoiceApplyResDTO;

/**
 * @Auther: colu
 * @Date: 2020-04-20 15:27
 * @Description: ILgsElectricInvoiceBizService
 */
public interface ILgsElectricInvoiceBizService {

    /**
     * 查询可开物流费发票的运单列表
     * @param pageQuery
     * @return
     */
    PageData<InvoiceBillListDTO> queryLgsWaybillsForInvoice(PageQuery<InvoiceBillCondDTO> pageQuery);

    /**
     * 批量开票申请预览请求
     * @param previewInvoiceApplyReqDTO
     * @return
     */
    PreviewInvoiceApplyResDTO applyLgsWaybillsInvoice(PreviewInvoiceApplyReqDTO previewInvoiceApplyReqDTO);

    /**
     * 批量开票申请确认请求
     * @param confirmInvoiceReqDTO
     * @return
     */
    void batchConfirmInvoiceApply(ConfirmInvoiceReqDTO confirmInvoiceReqDTO);

    /**
     * 修改运单开票结果
     * @param finishInvoiceDTO
     */
    void updateInvoiceResult(FinishInvoiceDTO finishInvoiceDTO);

}
