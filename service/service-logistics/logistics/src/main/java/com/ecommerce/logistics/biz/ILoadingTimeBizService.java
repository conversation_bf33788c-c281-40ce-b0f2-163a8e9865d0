package com.ecommerce.logistics.biz;

import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.intelligentDispatch.LoadingTimeDTO;

public interface ILoadingTimeBizService {

    /**
     * 新增装货时间
     */
    Boolean add(LoadingTimeDTO createDTO);

    /**
     * 编辑装货时间
     */
    Boolean edit(LoadingTimeDTO updateDTO);

    /**
     * 删除装货时间
     */
    Boolean delete(LoadingTimeDTO deleteDTO);

    /**
     * 分页查询装货时间
     */
    PageData<LoadingTimeDTO> pageLoadingTime(PageQuery<LoadingTimeDTO> pageQuery);

    /**
     * 根据条件查询装货时间
     */
    LoadingTimeDTO findByCondition(String warehouseId, String userId, Integer userType);
}
