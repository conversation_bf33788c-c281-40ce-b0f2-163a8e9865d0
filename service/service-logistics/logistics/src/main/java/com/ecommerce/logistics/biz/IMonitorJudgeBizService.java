package com.ecommerce.logistics.biz;

import com.ecommerce.logistics.api.dto.vehicle.SelfPickingVehicleDTO;
import com.ecommerce.logistics.dao.dto.proxybill.PickingBillTypeDO;

/**
 * @Auther: colu
 * @Date: 2020-03-12 16:09
 * @Description: 监控判断biz
 */
public interface IMonitorJudgeBizService {

    /**
     * 判断录入的自提车辆对象是否满足监控的条件
     * @param selfPickingVehicleDTO
     * @return
     */
    Boolean canMonitor(SelfPickingVehicleDTO selfPickingVehicleDTO);

    /**
     * 判断是否满足监控条件
     * @param shippingId
     * @param driverId
     * @param driverPhone
     * @return
     */
    Boolean canMonitor(String shippingId, String driverId, String driverPhone);

    /**
     * 判断提货单是否可以调度/指派(适用于背靠背逻辑的一二级单据)
     * @param pickingBillTypeDO
     * @return
     */
    Boolean canOperate(PickingBillTypeDO pickingBillTypeDO);

    /**
     * 是否需要同步到另一级单据
     * @param billProxyType
     * @param canOperate
     * @return
     */
    Boolean needSyncBill(String billProxyType, Byte canOperate);

    /**
     * 是否需要同步完成操作到映射的运单上
     * @param billProxyType
     * @param canOperate
     * @param waybillId
     * @return
     */
    Boolean needSyncComplete(String billProxyType, Byte canOperate, String waybillId);

    /**
     * 是否需要出站同步本运单调用完成
     * 出现原因:二级单,之前未补款,但是一级为操作方已经完成了.现在二级单补款,然后会自动调用出站,此时补充自动完成(其实是填补之前完成的同步操作)
     * 满足条件: 1.canOperate为0; 2.billProxyType是二级; 3.relationWaybill已经完成了
     * 试用场景: 一级是 卖家/平台(出站方+调度方);二级是卖家,且此时为二级
     * @param billProxyType
     * @param canOperate
     * @param waybillId
     * @return
     */
    Boolean needSynBKBCompleteByLeaveWarehouse(String billProxyType, Byte canOperate, String waybillId);

    /**
     * 判断运单的状态是否可以更改(主要判断背靠背运单是否状态已经相同,不同不能更改)
     * @param billProxyType
     * @param waybillId
     * @param status
     * @param canOperate
     * @return
     */
    Boolean canChangeStatus (String billProxyType, String waybillId, String status, Byte canOperate);

    /**
     * 判断运单当前状态是否可以关闭
     * @param billProxyType
     * @param waybillId
     * @param status
     * @param externalWaybillStatus
     * @param canOperate
     * @return
     */
    Boolean canCloseStatus(String billProxyType, String waybillId, String status, String externalWaybillStatus, Byte canOperate);

    /**
     * 判断是否需要创建一级发货单
     * @param billProxyType
     * @param canOperate
     * @return
     */
    Boolean needCreatePrimaryTakeInfo(String billProxyType, Byte canOperate);

    /**
     * 分析运单在插入时是否卖家已经备好货
     * @param billProxyType
     * @param canOperate
     * @return
     */
    Byte analysisWaybillIsReady(String billProxyType, Byte canOperate);



}
