package com.ecommerce.logistics.biz;

import com.ecommerce.logistics.api.dto.operationrecord.OperationRecordAddDTO;
import com.ecommerce.logistics.api.dto.operationrecord.OperationRecordDTO;
import com.ecommerce.logistics.api.dto.operationrecord.OperationRecordListDTO;
import com.ecommerce.logistics.api.dto.operationrecord.OperationRecordQueryDTO;
import com.ecommerce.logistics.api.dto.operationrecord.QueryWaybillOperationDTO;

import java.util.Date;
import java.util.List;

/**
 * @Auther: <EMAIL>
 * @Date: 20/08/2018 16:40
 * @Description:
 */
public interface IOperationRecordBizService {
    List<OperationRecordListDTO> getOperationRecordList(String entryId);

    void saveOperationRecord(OperationRecordAddDTO operationRecordAddDTO);

    void saveOperationRecord(OperationRecordAddDTO operationRecordAddDTO, Date operateTime);
    
    void saveOperationRecord(String entryId, Byte type,String operatorId, String operatorName, String content);

    /**
     * 查询运单操作
     * @param queryWaybillOperationDTO 查询运单操作对象
     * @return  List<OperationRecordDTO> 运单操作记录
     */
    List<OperationRecordDTO> queryWaybillOperation(QueryWaybillOperationDTO queryWaybillOperationDTO);

    /**
     * 批量保存操作记录
     * @param operationRecordAddDTOList 操作记录列表
     */
    void batchSaveOperationRecord(List<OperationRecordAddDTO> operationRecordAddDTOList);

    /**
     * 批量保存单据簇操作记录
     * @param entryIdList
     * @param type
     * @param operatorId
     * @param operatorName
     * @param content
     */
    void batchSaveBillCluster(List<String> entryIdList, Byte type,String operatorId, String operatorName, String content);

    /**
     * 查询操作
     * @param queryDTO 查询操作对象
     * @return  List<OperationRecordDTO> 操作记录
     */
    List<OperationRecordDTO> queryOperation(OperationRecordQueryDTO queryDTO);
}
