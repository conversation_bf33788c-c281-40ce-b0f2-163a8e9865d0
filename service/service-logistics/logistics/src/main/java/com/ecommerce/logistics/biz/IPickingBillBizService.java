package com.ecommerce.logistics.biz;

import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.pickingbill.*;
import com.ecommerce.logistics.api.dto.proxybill.SyncTakeInfoSecondaryWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillInfoDTO;
import com.ecommerce.logistics.dao.dto.waybill.UpdatePickingQuantityResultDO;
import com.ecommerce.logistics.dao.vo.PickingBill;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @Author: <EMAIL>
 * @Date: 2018-08-08 10:03
 * @Description:
 */
@Deprecated(since = "2.1.4-RELEASE")
public interface IPickingBillBizService {
    /**
     * 获取提货单列表
     * @param pageQuery 提货单查询对象
     * @return PageData<PickingBillListDTO>
     */
    PageData<PickingBillListDTO> queryPickingBillList(PageQuery<PickingBillListQueryDTO> pageQuery);

    /**
     * 录入发货单
     * @param enteringPickingBillDTO 发货单录入对象
     * @return 发货单号
     */
    String addPickingBill(EnteringPickingBillDTO enteringPickingBillDTO);

    /**
     * 录入自提发货单时,创建物流提货信息
     * @param enteringPickingBillDTO
     * @return
     */
    List<SyncTakeInfoSecondaryWaybillDTO> addSelfTakePickingBill(EnteringPickingBillDTO enteringPickingBillDTO);

    /**
     * 指派生成运单
     * @param assignWaybillDTO 指派运单对象
     * @param waybillInfoList 运单信息对象列表
     */
    void assignCreateWaybill(AssignWaybillDTO assignWaybillDTO, List<WaybillInfoDTO> waybillInfoList);

    /**
     * 指派生成调度单
     * @param dispatchBillAssignDTO 指派调度单对象
     */
    void assignCreateDispatchBill(AssignDispatchBillDTO dispatchBillAssignDTO);

    /**
     * 获取提货单详情
     * @param pickingBillId 提货单ID
     * @return EnteringPickingBillDTO
     */
    PickingBillDTO getPickingBillDetail(String pickingBillId);

    /**
     * 取消发货单
     * @param cancelDeliverySheetDTO 取消发货单对象
     * @return CancelQuantityDTO 实际取消商品数量对象
     */
    CancelQuantityDTO cancelDeliverySheet(CancelDeliverySheetDTO cancelDeliverySheetDTO);

    /**
     * 取消提货单
     * @param cancelPickingBillDTO 取消提货单对象
     * @return CancelQuantityDTO
     */
    CancelQuantityDTO cancelPickingBill(CancelPickingBillDTO cancelPickingBillDTO);

    List<OptionDTO> getBuyerOptions(String name);

    List<OptionDTO> getSellerOptions(String name);

    /**
     * 关闭发货单
     * @param closeDeliverySheetDTO 关闭发货单对象
     */
    void batchCloseDeliverySheet(CloseDeliverySheetDTO closeDeliverySheetDTO);

    /**
     * 关闭提货单
     * @param closePickingBillDTO 关闭提货单对象
     */
    void closePickingBill(ClosePickingBillDTO closePickingBillDTO);
    
    /**
     * 修改调度单完成数量
     * @Auther: <EMAIL>
     * @Date: 2018年9月19日 下午8:18:03
     * @param pickingBillId
     * @param quantity
     * @param operatorId
     * @param operatorName
     */
    UpdatePickingQuantityResultDO updatePickingCompleteQuantity(String pickingBillId,
                                                                BigDecimal quantity,
                                                                String operatorId,
                                                                String operatorName,
                                                                Boolean lockFlag);

    /**
     * 卖家提货单委托平台配送
     * @param vendorEntrustmentDTO 委托对象
     */
    void vendorEntrustmentPickingBill(VendorEntrustmentDTO vendorEntrustmentDTO);

    /**
     * 尝试关闭提货单
     * @param pickingBillId 提货单号
     * @param operatorId 操作人ID
     * @param operatorName 操作人名称
     * @return 是否关闭
     */
    Boolean tryClosePickingBill(String pickingBillId, String operatorId, String operatorName);

    /**
     * 更新提货单发货数量
     * @param pickingBillId
     * @param quantity
     * @param operatorId
     * @param operatorName
     * @return Boolean
     */
    Boolean updatePickingShippingQuantity(String pickingBillId, BigDecimal quantity, String operatorId, String operatorName);

    /**
     * 查询提货单对象
     * @param pickingBillId 提货单ID
     * @return PickingBill
     */
    PickingBill selectPickingBillById(String pickingBillId);

    /**
     * 变更提货单提货点
     * @param changePickingBillWarehouseDTO 变更提货单提货点对象
     */
    void changePickingBillWarehouse(ChangePickingBillWarehouseDTO changePickingBillWarehouseDTO, String storeCarrierId);

    /**
     * 更新提货单对象
     * @param pickingBill 提货单对象
     */
    void updatePickingBill(PickingBill pickingBill);

    /**
     * 统计提货单状态
     * @param pickingBillListQueryDTO 提货单查询对象
     * @return Map<String, StatisticsPickingBillStatusDTO>
     */
    Map<String, StatisticsPickingBillStatusDTO> statisticsPickingBillStatus(PickingBillListQueryDTO pickingBillListQueryDTO);

    /**
     * 查询提货单信息
     * @param pickingBillQueryDTO 提货单详情查询对象
     * @return PickingBillDTO
     */
    PickingBillDTO queryPickingBillByCondition(PickingBillQueryDTO pickingBillQueryDTO);

    /**
     * 导出提货单
     * @param queryDTO 提货单查询对象
     * @return List<PickingBillExportDTO>
     */
    List<PickingBillExportDTO> exportPickingBill(PickingBillListQueryDTO queryDTO);
}