package com.ecommerce.logistics.biz;

import com.ecommerce.logistics.dao.dto.pickingbill.PickingMapDTO;
import com.ecommerce.logistics.dao.vo.PickingMap;

import java.util.List;

/**
 * 提货单映射
 */
public interface IPickingMapBizService {

    String addPickingMap(PickingMapDTO pickingMapDTO);

    void updatePickingMap(PickingMapDTO pickingMapDTO);

    /**
     * 根据单据bkb类型找到映射的提货单id
     * @param pickingBillId
     * @param billProxyType
     * @return
     */
    String findRelationPickingBillId(String pickingBillId, String billProxyType);

    /**
     * 根据单据bkb类型找到映射的提货单集合
     * @param pickingBillId
     * @param billProxyType
     * @return
     */
    List<PickingMap> findRelationPickingBillList(String pickingBillId, String billProxyType);
}
