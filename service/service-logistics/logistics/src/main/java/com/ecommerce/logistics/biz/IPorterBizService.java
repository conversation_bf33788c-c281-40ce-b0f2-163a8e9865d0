package com.ecommerce.logistics.biz;

import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.intelligentDispatch.PorterEditDTO;
import com.ecommerce.logistics.api.dto.intelligentDispatch.PorterListDTO;
import com.ecommerce.logistics.api.dto.intelligentDispatch.PorterQueryDTO;

/**
 * @Description: IPorterBizService
 * @Author: <EMAIL>
 * @Date: 2021-04-13 14:40
 */
public interface IPorterBizService {

    /**
     * 分页查询搬运人员列表
     * @param pageQuery
     * @return
     */
    PageData<PorterListDTO> queryPorterList(PageQuery<PorterQueryDTO> pageQuery);

    /**
     * 编辑搬运人员
     * @param porterEditDTO
     */
    void editPorter(PorterEditDTO porterEditDTO);

    /**
     * 新增搬运人员
     * @param porterEditDTO
     */
    void addPorter(PorterEditDTO porterEditDTO);

    /**
     * 删除搬运人员
     * @param porterEditDTO
     */
    void delPorter(PorterEditDTO porterEditDTO);
}
