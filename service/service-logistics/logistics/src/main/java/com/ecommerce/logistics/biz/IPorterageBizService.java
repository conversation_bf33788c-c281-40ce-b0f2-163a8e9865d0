package com.ecommerce.logistics.biz;

import com.ecommerce.common.result.PageData;
import com.ecommerce.common.service.IBaseBiz;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.porterage.*;
import com.ecommerce.logistics.dao.vo.PorterageRule;

import java.util.List;

/**
 * Author: <EMAIL>
 * Description:
 * Date: Create in 上午10:58 18/11/26
 */
public interface IPorterageBizService extends IBaseBiz<PorterageRule> {
    /**
     * 新增搬运费规则
     * @param porterageSaveDTO 搬运费规则保存对象
     * @return String 搬运费规则ID
     */
    String addPorterageRule(PorterageSaveDTO porterageSaveDTO);

    /**
     * 删除搬运费规则
     * @param porterageRuleId 搬运费规则Id
     * @param operator 操作人Id
     * @return String 搬运费规则ID
     */
    String deletePorterageRule(String porterageRuleId,String operator);

    /**
     * 编辑搬运费规则
     * @param porterageSaveDTO 搬运费规则保存对象
     */
    void editPorterageRule(PorterageSaveDTO porterageSaveDTO);

    /**
     * 分页查询搬运费规则列表
     * @param pageQuery 搬运费规则查询对象
     * @return PageData<PorterageListDTO>
     */
    PageData<PorterageListDTO> queryPorterageRuleList(PageQuery<PorterageListQueryDTO> pageQuery);

    /**
     * 查询搬运费规则详情
     * @param porterageDetailQueryDTO 搬运费详情查询对象
     * @return PorterageDetailDTO
     */
    PorterageDetailDTO queryPorterageDetail(PorterageDetailQueryDTO porterageDetailQueryDTO);

    /**
     * 搜索搬运费规则列表
     * @param porterageSearchDTO 搬运费规则搜索对象
     * @return List<PorterageSearchResultDTO>
     */
    List<PorterageSearchResultDTO> searchPorterageRuleList(PorterageSearchDTO porterageSearchDTO);

    /**
     * 计算订单搬运费
     * @param porterageComputeDTO 搬运费计算对象
     * @return PorterageComputeResultDTO
     */
    PorterageComputeResultDTO computePorterage(PorterageComputeDTO porterageComputeDTO);

    /**
     * 查询区域范围内搬运费列表
     * @param porterageZoneQueryDTO 区域范围搬运费查询对象
     * @return List<PorterageZoneListDTO>
     */
    List<PorterageZoneListDTO> queryZonePorterageList(PorterageZoneQueryDTO porterageZoneQueryDTO);
}
