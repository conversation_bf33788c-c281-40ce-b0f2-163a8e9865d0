package com.ecommerce.logistics.biz;

import java.util.List;

import com.ecommerce.logistics.api.dto.pickingbill.OptionDTO;
import com.ecommerce.logistics.api.dto.productinfo.ProductInfoDTO;
import com.ecommerce.logistics.dao.vo.ProductInfo;

/**
 * 
 * @Auther: <EMAIL>
 * @Date: 2018年8月31日 上午10:21:46
 * @Description:
 */
public interface IProductInfoBizService {
	
	List<ProductInfoDTO> queryInfoLikeNote(String note);

	void insertListFillDefault(List<ProductInfo> productInfoList);

	/**
	 * 关键字查询商品下拉列表
	 * @param keyWord
	 * @return
	 */
	List<OptionDTO> queryOptionsByKeyWord(String keyWord);

}
