package com.ecommerce.logistics.biz;

import com.ecommerce.logistics.api.dto.pickingbill.ProductDetailDTO;
import com.ecommerce.logistics.dao.vo.ProductQuantityMap;

import java.math.BigDecimal;
import java.util.List;

/**
 * 商品数量映射表service
 * @Auther: <EMAIL>
 * @Date: 2018年8月25日 下午5:51:40
 * @Description:
 */
public interface IProductQuantityMapBizService {
	
	/**
	 * 添加记录
	 * @Auther: <EMAIL>
	 * @Date: 2018年8月25日 下午5:57:00
	 */
	void saveRecord(String mapId,Byte mapType, String productInfoId,BigDecimal quantity,BigDecimal deliveryQuantity,
			String memberId);

	/**
	 * 运单ID查询商品详情
	 * @param waybillId
	 * @return
	 */
	List<ProductDetailDTO> selectProductByWaybillId(String waybillId);

	/**
	 * 根据父运单ID查询运单的配送map
	 * @param parentWaybillId
	 * @return
	 */
	List<ProductQuantityMap> selectMapByParentWaybillId(String parentWaybillId);

}
