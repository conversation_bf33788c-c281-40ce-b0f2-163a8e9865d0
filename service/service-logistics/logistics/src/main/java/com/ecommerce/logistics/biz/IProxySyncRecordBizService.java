package com.ecommerce.logistics.biz;

import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.ProxySyncRecord.ProxySyncRecordListDTO;
import com.ecommerce.logistics.api.dto.ProxySyncRecord.ProxySyncRecordQueryDTO;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.pickingbill.AssignWaybillDTO;
import com.ecommerce.logistics.api.dto.pickingbill.ClosePickingBillDTO;
import com.ecommerce.logistics.api.dto.waybill.*;
import com.ecommerce.logistics.dao.vo.ProxySyncRecord;

import java.util.List;

/**
 * @Auther: colu
 * @Date: 2020-08-20 15:20
 * @Description: 背靠背同级单据同步记录服务
 */
public interface IProxySyncRecordBizService {

    /**
     * 保存运单同步记录
     * @param syncType
     * @param operateBillId
     * @param relationBillId
     * @param param
     * @param <T>
     * @return
     */
    <T>String saveSyncWaybillRecord(String syncType, String operateBillId, String relationBillId, T param);

    /**
     * 保存单据同步记录
     * @param billType
     * @param syncType
     * @param operateBillId
     * @param relationBillId
     * @param param
     * @param <T>
     * @return
     */
    <T>String saveSyncBillRecord(String billType, String syncType, String operateBillId, String relationBillId, T param);

    /**
     * 获取一条运单操作记录
     * @param relationWaybillId
     * @param syncType
     * @return
     */
    ProxySyncRecord selectByRelation(String relationWaybillId, String syncType);

    /**
     * 获取一条单据操作记录
     * @param billType
     * @param relationWaybillId
     * @param syncType
     * @return
     */
    ProxySyncRecord selectByRelation(String billType, String relationWaybillId, String syncType);

    /**
     * 根据操作id和同步id获取一条记录
     * @param operateBillId
     * @param relationBillId
     * @param syncType
     * @return
     */
    ProxySyncRecord selectByOperateAndRelation(String operateBillId, String relationBillId, String syncType);

    /**
     * 主键更新操作状态
     * @param proxySyncId
     * @param syncStatus
     * @param syncDesc
     * @return
     */
    int updateStatusById(String proxySyncId, String syncStatus, String syncDesc);

    /**
     * 同步创建运单:
     *  一级运单:司机枪单/司机确认/提货单指派生成运单
     * @param assignWaybillDTO
     */
    void syncPublishSecondaryWaybill(AssignWaybillDTO assignWaybillDTO);

    /**
     * 同步出站
     * @param operateWaybillId
     * @param relationLeaveWarehouseDTO
     */
    void syncLeaveWarehouse(String operateWaybillId, LeaveWarehouseDTO relationLeaveWarehouseDTO);

    /**
     * 同步关闭运单
     * @param operateWaybillId
     * @param relationCloseWaybillDTO
     */
    void syncCloseWaybill(String operateWaybillId, CloseWaybillDTO relationCloseWaybillDTO);

    /**
     * 同步取消运单（司机）
     * @param operateWaybillId
     * @param driverCancelWaybillDTO
     */
    void syncCancelWaybillByDriver(String operateWaybillId, DriverCancelWaybillDTO driverCancelWaybillDTO);

    /**
     * 同步取消运单（平台）
     * @param operateWaybillId
     * @param cancelWaybillDTO
     */
    void syncCancelWaybillByPlatform(String operateWaybillId, CancelWaybillDTO cancelWaybillDTO);

    /**
     * 同步完成运单
     * @param operateWaybillId
     * @param relationCompleteWaybillDTO
     */
    void syncCompleteWaybill(String operateWaybillId, CompleteWaybillDTO relationCompleteWaybillDTO);

    /**
     * 条件查询背靠背单据同步记录
     * @param proxySyncRecordQueryDTO
     */
    PageData<ProxySyncRecordListDTO> queryProxySyncRecordList(PageQuery<ProxySyncRecordQueryDTO> proxySyncRecordQueryDTO);

    /**
     * 同步自提重新指派车辆
     * @param operateWaybillId
     * @param reassignmentVehicleDTO
     */
    void syncReassignmentVehicle(String operateWaybillId, ReassignmentVehicleDTO reassignmentVehicleDTO);

    /**
     * 同步卖家配送 取消后重新指派
     * @param operateWaybillId
     * @param sellerAssignWaybillDTO
     */
    void syncAssignWaybill(String operateWaybillId, SellerAssignWaybillDTO sellerAssignWaybillDTO);

    /**
     * 同步运单重新指派
     * @param operateWaybillId
     * @param sellerAssignWaybillDTO
     */
    void syncReassignmentWaybill(String operateWaybillId, SellerAssignWaybillDTO sellerAssignWaybillDTO);

    /**
     * 同步关闭提货单
     * @param operatePickingBillId
     * @param closePickingBillDTO
     */
    void syncClosePickingBill(String operatePickingBillId, ClosePickingBillDTO closePickingBillDTO);

    /**
     * 同步开仓运单
     * @param operateWaybillId
     * @param openCabinShipBillDTO
     */
    void syncOpenCabinWaybill(String operateWaybillId, OpenCabinShipBillDTO openCabinShipBillDTO);

    /**
     * 根据背靠背单据同步记录id 重新发消息同步单据信息
     * @param proxySyncIds
     */
    void syncByProxySyncIds(List<String> proxySyncIds);
}
