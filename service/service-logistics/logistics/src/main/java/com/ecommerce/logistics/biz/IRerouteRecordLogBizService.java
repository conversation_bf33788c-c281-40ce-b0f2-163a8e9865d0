package com.ecommerce.logistics.biz;

import com.ecommerce.logistics.api.dto.waybill.RerouteRecordLogDTO;

import java.util.List;

/**
 * 改航记录日志服务
 * Created by hexinhui3 on 2021/9/15 16:39
 */
public interface IRerouteRecordLogBizService {

    /**
     * 新增操作记录
     * @param recordLogDTO
     * @return
     */
    int addRerouteRecordLog(RerouteRecordLogDTO recordLogDTO);

    /**
     * 获取改航记录
     * @param recordLogDTO
     * @return
     */
    List<RerouteRecordLogDTO> queryRerouteLogList(RerouteRecordLogDTO recordLogDTO);

}
