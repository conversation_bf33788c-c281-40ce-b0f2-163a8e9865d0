package com.ecommerce.logistics.biz;

import com.ecommerce.logistics.api.dto.carriage.CarriageRuleDTO;
import com.ecommerce.logistics.api.dto.rulecompute.ComputeCarriagePriceDTO;
import com.ecommerce.logistics.api.dto.rulecompute.WaybillMergeDTO;
import com.ecommerce.logistics.api.dto.rulecompute.WaybillSplitDTO;
import com.ecommerce.logistics.api.dto.waybill.CreateWaybillDTO;
import com.ecommerce.logistics.dao.dto.carriage.AssignDirectDO;
import com.ecommerce.logistics.dao.dto.pickingbill.PickingBillListDO;
import com.ecommerce.logistics.dao.vo.DeliveryBill;
import com.ecommerce.logistics.dao.vo.DeliveryInfo;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: <EMAIL>
 * @Description: 规则运算服务接口
 * @Date: Create in 下午5:02 18/8/15
 */
public interface IRuleComputeBizService {
    /**
     * 运单拆分
     * @param waybillSplitDTO 运单拆分对象
     * @return List<CreateWaybillDTO> 订单创建列表
     */
    List<CreateWaybillDTO> waybillSplit(WaybillSplitDTO waybillSplitDTO);

    /**
     * 获取合并信息
     * @param waybillMergeDTO 运单合并对象
     * @return List<PickingBillListDO> 合并运单号
     */
    List<PickingBillListDO> getMergeInfo(WaybillMergeDTO waybillMergeDTO);

    /**
     * 计算运费价格
     * @param computeCarriagePriceDTO 运费价格计算对象
     * @return BigDecimal
     */
    BigDecimal computeCarriagePrice(ComputeCarriagePriceDTO computeCarriagePriceDTO);

    /**
     * 收费场景
     * @param computeCarriagePriceDTO
     * @return
     */
    CarriageRuleDTO computeCollectCarriage(ComputeCarriagePriceDTO computeCarriagePriceDTO);

    /**
     * 付费场景，不限车型时计算运单运费
     * @param computeCarriagePriceDTO
     * @return
     */
    CarriageRuleDTO computeUnlimiteTypeCarriage(ComputeCarriagePriceDTO computeCarriagePriceDTO);

    /**
     * 委托单查询运费规则
     * @param deliveryBill
     * @param deliveryInfo
     * @param assignDirectDO
     * @return
     */
    CarriageRuleDTO computeDeliveryCarriage(DeliveryBill deliveryBill,
                                            DeliveryInfo deliveryInfo,
                                            AssignDirectDO assignDirectDO);

}
