package com.ecommerce.logistics.biz;

import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.settlement.MonthSettlementListDTO;
import com.ecommerce.logistics.api.dto.settlement.MonthSettlementListQueryDTO;
import com.ecommerce.logistics.api.dto.settlement.SettlementItemListDTO;
import com.ecommerce.logistics.api.dto.settlement.SettlementItemListQueryDTO;
import com.ecommerce.logistics.api.dto.settlement.SettlementListDTO;
import com.ecommerce.logistics.api.dto.settlement.SettlementListQueryDTO;
import com.ecommerce.logistics.api.dto.settlement.CarriageSettlementDTO;
import com.ecommerce.logistics.api.dto.settlement.UserSettlementAmountDTO;
import com.ecommerce.logistics.api.dto.settlement.UserSettlementListDTO;
import com.ecommerce.logistics.api.dto.settlement.UserSettlementListQueryDTO;

import java.math.BigDecimal;
import java.util.Map;

/**
 * @Auther: <EMAIL>
 * @Date: 03/09/2018 20:30
 * @Description:
 */
public interface ISettlementBizService {

    /**
     * 获取结算列表
     * @param pageQuery 查询条件对象
     * @return PageData<SettlementListDTO>
     */
    PageData<SettlementListDTO> querySettlementList(PageQuery<SettlementListQueryDTO> pageQuery);

    /**
     * 获取月度结算列表
     * @param pageQuery 查询条件对象
     * @return PageData<MonthSettlementListDTO>
     */
    PageData<MonthSettlementListDTO> queryMonthSettlementList(PageQuery<MonthSettlementListQueryDTO> pageQuery);

    /**
     * 获取结算明细列表
     * @param pageQuery 查询条件对象
     * @return PageData<SettlementItemListDTO>
     */
    PageData<SettlementItemListDTO> querySettlementItemList(PageQuery<SettlementItemListQueryDTO> pageQuery);

    /**
     * 用户运费结算
     * @param carriageSettlementDTO  运费结算对象
     */
    void userCarriageSettlement(CarriageSettlementDTO carriageSettlementDTO);

    /**
     * 运单运费结算
     * @param carriageSettlementDTO 运费结算对象
     */
    void waybillCarriageSettlement(CarriageSettlementDTO carriageSettlementDTO);

    /**
     * 新增运单结算记录
     * @param waybillId 运单ID
     * @param actualMap 实际数据map
     */
    void addWaybillSettlement(String waybillId, Map<String, BigDecimal> actualMap);

    /**
     * 查询用户结算金额
     * @param userId 用户ID
     */
    UserSettlementAmountDTO queryUserSettlementAmount(String userId);

    /**
     * 查询用户结算信息列表
     * @param pageQuery 用户结算信息查询对象
     * @return PageData<UserSettlementListDTO>
     */
    PageData<UserSettlementListDTO> queryUserSettlementList(PageQuery<UserSettlementListQueryDTO> pageQuery);
}
