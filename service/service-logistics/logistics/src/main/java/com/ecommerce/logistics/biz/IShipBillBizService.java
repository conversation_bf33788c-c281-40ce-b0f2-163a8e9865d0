package com.ecommerce.logistics.biz;

import com.ecommerce.common.exception.BizException;
import com.ecommerce.logistics.api.dto.GoodsInfoDTO;
import com.ecommerce.logistics.api.dto.shipbill.CancelShipBillDTO;
import com.ecommerce.logistics.api.dto.shipbill.CloseShipBillDTO;
import com.ecommerce.logistics.api.dto.shipbill.ReassignShipBillDTO;
import com.ecommerce.logistics.api.dto.shipbill.ShipBillQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.*;
import com.ecommerce.logistics.dao.dto.waybill.ConfirmWaybillDO;
import com.ecommerce.logistics.dao.vo.ShipBill;

import java.util.List;

/**
 * @Auther: colu
 * @Date: 2020-11-30 17:36
 * @Description: 船运单(船运计划)业务接口
 */
public interface IShipBillBizService {

    /**
     * 添加船运单到数据,只能由提货单分配时使用
     * NOTE: 不适用于改航,不适用整合运单的主运单
     * @param createShipBillDTO
     * @return
     */
    ShipBill addShipBill(CreateShipBillDTO createShipBillDTO);

    /**
     * 取消运单
     * @param cancelShipBillDTO
     */
    ShipBill cancelShipBill(CancelShipBillDTO cancelShipBillDTO);

    /**
     * 取消整合运单
     * @param cancelShipBillDTO
     */
    void cancelMergeShipBill(CancelShipBillDTO cancelShipBillDTO);

    /**
     * 关闭运单
     * @param closeShipBillDTO
     */
    void discardShipBill(CloseShipBillDTO closeShipBillDTO);

    /**
     * 关闭整合运单的主运单
     * @param closeShipBillDTO
     */
    void discardMergeMainShipBill(CloseShipBillDTO closeShipBillDTO);

    /**
     * 已取消运单的重新指派
     * @param reassignShipBillDTO
     */
    void assignCanceledShipBill(ReassignShipBillDTO reassignShipBillDTO);

    /**
     * 已取消整合运单的重新指派
     * @param reassignShipBillDTO
     */
    void assignCanceledMergeShipBill(ReassignShipBillDTO reassignShipBillDTO);

    /**
     * 审核运单
     * @param passCheckDTO
     */
    void passCheck(PassCheckDTO passCheckDTO);

    /**
     * 批量审核
     * @param batchPassCheckDTO
     */
    void batchPassCheck(BatchPassCheckDTO batchPassCheckDTO);

    /**
     * 进厂
     * @param dto
     */
    void enterFactory(EnterFactoryDTO dto);

    /**
     * 撤回进厂
     * @param dto 运单ID
     */
    void withdrawEnterFactory(EnterFactoryDTO dto);

    /**
     * 指派运单到承运商
     * @param waybillAssignDTO
     */
    void assignWaybill(WaybillAssignDTO waybillAssignDTO);

    /**
     * 承运商指派运单到司机
     * @param dto
     */
    void carrierAssignWaybillToDriver(DriverWaybillAssignDTO dto);

    /**
     * 司机确认运单
     * @param confirmWaybillDO 确认对象
     */
    void confirmWaybillByDriver(ConfirmWaybillDO confirmWaybillDO);

    /**
     * 司机拒绝运单
     * @param driverConfirmDTO 拒绝对象
     */
    void rejectWaybillByDriver(DriverConfirmDTO driverConfirmDTO);

    /**
     * 抢单,返回抢单的结果
     * @param snatchWaybillDTO
     * @return
     */
    String snatchWaybill(SnatchWaybillDTO snatchWaybillDTO);

    /**
     * 重新下发创建ERP运单
     * @param waybillId
     * @param operationUserId
     * @param operationUserName
     * @return
     */
    ShipBill againCreateExternalWaybill(String waybillId, String operationUserId, String operationUserName) throws BizException;


    List<GoodsInfoDTO> queryGoodsNameListDropDownBox(QueryGoodsDTO queryGoodsDTO);

	List<String> queryVehicleOrShippingListByName(ShipBillQueryDTO queryDTO);

    int updateCarrierInfo(String waybillId, String carrierId, String carrierName, String internal);

}
