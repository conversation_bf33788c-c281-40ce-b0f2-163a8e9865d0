package com.ecommerce.logistics.biz;

import com.ecommerce.logistics.api.dto.waybill.CompleteShipBillDTO;
import com.ecommerce.logistics.api.dto.waybill.OpenCabinShipBillDTO;
import com.ecommerce.logistics.api.dto.waybill.RefundShipBillDTO;

/**
 * @Auther: colu
 * @Date: 2021-01-19 17:53
 * @Description: 运单完成业务接口
 */
public interface IShipBillCompleteBizService {

    /**
     * 完成运单
     * @param completeShipBillDTO
     */
    void completeShipBill(CompleteShipBillDTO completeShipBillDTO);

    /**
     * 完成混凝土
     * @param completeShipBillDTO
     */
    void completeConcrete(CompleteShipBillDTO completeShipBillDTO);

    /**
     * 开仓船运运单
     * @param openCabinShipBillDTO
     */
    String openCabin(OpenCabinShipBillDTO openCabinShipBillDTO);

    /**
     * 完成混凝土
     * @param completeShipBillDTO
     */
    void completeVehicleConcrete(CompleteShipBillDTO completeShipBillDTO);


    /**
     * 运单退货
     * @param refundShipBillDTO
     */
    void refundShipBill(RefundShipBillDTO refundShipBillDTO);



}
