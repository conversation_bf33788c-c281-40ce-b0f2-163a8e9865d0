package com.ecommerce.logistics.biz;

import com.ecommerce.logistics.api.dto.shipbill.ShipItemNotifySettlementDTO;
import com.ecommerce.logistics.api.dto.waybill.LeaveWarehouseDTO;

/**
 * @Auther: colu
 * @Date: 2021-01-12 11:33
 * @Description: 运单操作业务接口
 * NOTE:
 *      出站始终由卖家发起
 *      如果一级(或者非背靠背)需要补款,则运单不可出站.必须等待补款完成才能补款
 */
public interface IShipBillLeaveWarehouseBizService {

    /**
     * 电商汽运的出站操作i
     * @param leaveWarehouseDTO
     */
    Boolean ecVehicleLeaveWarehouse(LeaveWarehouseDTO leaveWarehouseDTO);

    /**
     * 电商背靠背同步汽运出站
     * @param leaveWarehouseDTO
     */
    Boolean ecProxyVehicleLeaveWarehouse(LeaveWarehouseDTO leaveWarehouseDTO);

    /**
     * 电商船运的出站操作
     * @param leaveWarehouseDTO
     */
    Boolean ecShipLeaveWarehouse(LeaveWarehouseDTO leaveWarehouseDTO);

    /**
     * 电商背靠背同步船运出站操作
     * @param leaveWarehouseDTO
     */
    Boolean ecProxyShipLeaveWarehouse(LeaveWarehouseDTO leaveWarehouseDTO);

    /**
     * erp的出站操作
     * @param leaveWarehouseDTO
     */
    void erpLeaveWarehouse(LeaveWarehouseDTO leaveWarehouseDTO);

    /**
     * 出站通知erp之后,erp回调的处理
     * @param leaveWarehouseDTO
     */
    void leaveToERPCallBack(LeaveWarehouseDTO leaveWarehouseDTO);

    /**
     * 出站发送到交易,补款完成后的回调
     * @param shipItemNotifySettlementDTO
     */
    void leaveSupplementCallBack(ShipItemNotifySettlementDTO shipItemNotifySettlementDTO);

}
