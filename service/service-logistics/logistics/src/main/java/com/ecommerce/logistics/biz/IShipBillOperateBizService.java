package com.ecommerce.logistics.biz;

import com.ecommerce.logistics.api.dto.DeliveryInfoDTO;
import com.ecommerce.logistics.api.dto.waybill.ArriveWarehouseDTO;
import com.ecommerce.logistics.dao.dto.delivery.DeliveryRerouteResult;
import com.ecommerce.logistics.dao.dto.shipbill.ShipBillLeaveDTO;
import com.ecommerce.logistics.dao.dto.shipbill.ShipBillLeaveResDTO;
import com.ecommerce.logistics.dao.vo.DeliveryInfo;

import java.math.BigDecimal;

/**
 * @Auther: colu
 * @Date: 2021-01-14 17:26
 * @Description: 运单操作业务接口
 */
public interface IShipBillOperateBizService {

    /**
     * 填充运单的出站数量,金额信息
     * @param waybillId 运单ID
     * @param actualQuantity 实际出厂量
     * @param updateUser 操作人
     */
    void fillLeaveWarehouseQuantity(String waybillId, BigDecimal actualQuantity, String updateUser);

    /**
     * 汽运子项出站操作
     * @param shipBillLeaveDTO
     */
    ShipBillLeaveResDTO vehicleItemLeaveWarehouse(ShipBillLeaveDTO shipBillLeaveDTO);

    /**
     * 船运模式的子项出站操作
     * @param shipBillLeaveDTO
     */
    ShipBillLeaveResDTO shipItemLeaveWarehouse(ShipBillLeaveDTO shipBillLeaveDTO);

    /**
     * 运单子项出站,通知交易结算
     * @param waybillNum
     * @param waybillItemId
     * @param transportToolType
     * @param operateUserName
     * @param deliveryInfoDTO
     */
    void itemSendQuantityToOrder(String waybillNum, String waybillItemId, String transportToolType, String operateUserName, DeliveryInfoDTO deliveryInfoDTO);

    /**
     * 改航
     * @param result
     * @return 返回运单得新配送信息
     */
    DeliveryInfo rerouteShipBill(DeliveryRerouteResult result);

    /**
     * 更新运单送达时间
     * @param waybillId
     */
    void changeArriveDestinationTime(String waybillId);

    /**
     * 车辆进站
     * @param dto
     */
    void arriveWarehouse(ArriveWarehouseDTO dto);

    /**
     * 获取卖家参数的开关值
     * @param memberId
     * @param keyCode
     * @param defFlag
     * @return
     */
    boolean memberConfigFlag(String memberId, String keyCode, boolean defFlag);
}
