package com.ecommerce.logistics.biz;

import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.DeliveryDetailVehicleDTO;
import com.ecommerce.logistics.api.dto.EmallPublishWaybillListDTO;
import com.ecommerce.logistics.api.dto.EmallPublishWaybillQueryDTO;
import com.ecommerce.logistics.api.dto.ForBidCloseWaybillCountQueryDTO;
import com.ecommerce.logistics.api.dto.LastSignWaybillQueryDTO;
import com.ecommerce.logistics.api.dto.ShipBillWaitRerouteListDTO;
import com.ecommerce.logistics.api.dto.WaitRerouteShipBillCondDTO;
import com.ecommerce.logistics.api.dto.WaybillBriefDTO;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.pickingbill.PickingBillDTO;
import com.ecommerce.logistics.api.dto.shipbill.CheckWaybillInfoDTO;
import com.ecommerce.logistics.api.dto.shipbill.ConcreteShipBillInfoDTO;
import com.ecommerce.logistics.api.dto.shipbill.ShipBillDetailDTO;
import com.ecommerce.logistics.api.dto.shipbill.ShipBillListDTO;
import com.ecommerce.logistics.api.dto.shipbill.ShipBillOrderDetailDTO;
import com.ecommerce.logistics.api.dto.shipbill.ShipBillQueryDTO;
import com.ecommerce.logistics.api.dto.shipbill.ShipWaybillListDTO;
import com.ecommerce.logistics.api.dto.shipbill.ShipWaybillQueryDTO;
import com.ecommerce.logistics.api.dto.warehouse.WarehouseOptionDTO;
import com.ecommerce.logistics.api.dto.waybill.AppWaybillQueryConditionsMapDTO;
import com.ecommerce.logistics.api.dto.waybill.AppWaybillStatisticsDTO;
import com.ecommerce.logistics.api.dto.waybill.CarryWaybillSubmitDTO;
import com.ecommerce.logistics.api.dto.waybill.CheckWaybillListQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.ContractSubmitInfoDTO;
import com.ecommerce.logistics.api.dto.waybill.DistrictListDTO;
import com.ecommerce.logistics.api.dto.waybill.DriverWaybillListQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.EvaluateWaybillListDTO;
import com.ecommerce.logistics.api.dto.waybill.PublishWaybillListDTO;
import com.ecommerce.logistics.api.dto.waybill.SnatchedWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.SnatchedWaybillQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.TradeWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.UserSeckillWaybillListDTO;
import com.ecommerce.logistics.api.dto.waybill.UserSeckillWaybillQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.WaitCheckWaybillListDTO;
import com.ecommerce.logistics.api.dto.waybill.WarehouseAdminProccessedDTO;
import com.ecommerce.logistics.api.dto.waybill.WarehouseAdminProccessedQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.WarehouseAdminWaitDeliveryDTO;
import com.ecommerce.logistics.api.dto.waybill.WarehouseAdminWaitDeliveryQueryDTO;
import com.ecommerce.logistics.biz.message.dto.ShipBillSMSDTO;
import com.ecommerce.logistics.biz.message.dto.WaybillCreatedSMSDTO;
import com.ecommerce.logistics.dao.dto.shipbill.ParentInfo;
import com.ecommerce.logistics.dao.dto.shipbill.ShipBillRecInfo;
import com.ecommerce.logistics.dao.dto.waybill.SubWaybillDO;
import com.ecommerce.logistics.dao.dto.waybill.TradeWaybillDetailDO;
import com.ecommerce.logistics.dao.vo.ShipBill;

import java.util.List;

/**
 * @Auther: colu
 * @Date: 2021-01-13 10:59
 * @Description: 新.运单查询服务接口
 */
public interface IShipBillQueryBizService {

    PageData<ShipBillListDTO> queryShipBillList(PageQuery<ShipBillQueryDTO> pageQuery);

    AppWaybillStatisticsDTO statisticsShipBill(ShipBillQueryDTO queryDTO);

    AppWaybillQueryConditionsMapDTO selectShipBillQueryConditions(ShipBillQueryDTO queryDTO);

    /**
     * 根据委托单Id查询所有运单信息
     * @param deliveryBillId
     * @return
     */
    List<DeliveryDetailVehicleDTO> queryAllShipBillByDeliveryBillId(String deliveryBillId);

    /**
     * 根据委托单Id查询该委托单自己承运的运单
     * @param deliveryBillId
     * @return
     */
    List<DeliveryDetailVehicleDTO> queryShipBillByDeliveryBillId(String deliveryBillId);

    /**
     * 查询委托单下不可以关闭的运单数量
     * @param deliveryBillId
     * @return
     */
    Integer queryForbidCloseWaybillCount(String deliveryBillId);

    /**
     * 查询发货单下不可以关闭的运单数量
     * @param forBidCloseWaybillCountQueryDTO
     * @return
     */
    Integer queryForbidCloseWaybillCountByTakeCode(ForBidCloseWaybillCountQueryDTO forBidCloseWaybillCountQueryDTO);

    List<ShipBill> queryForbidCloseWaybillListByTakeCode(ForBidCloseWaybillCountQueryDTO forBidCloseWaybillCountQueryDTO);

    ShipBillDetailDTO selectWaybillDetailByWaybillItemId(String waybillItemId);

    /**
     * 获取运单详情-司机APP调用
     * @param waybillId
     * @return
     */
    ShipBillDetailDTO getWaybillDetailForDriverApp(String waybillId);

    /**
     * 根据发货单号查询运单信息
     * @param takeCodeList
     * @return
     */
    List<ShipBillOrderDetailDTO> queryWaybillListByTakeCode(List<String> takeCodeList);

    List<TradeWaybillDTO> queryWaybillListByDeliveryNum(String deliverySheetNum);

    /**
     * 查询可改航的运单列表
     * @param pageQuery
     * @return
     */
    PageData<ShipBillWaitRerouteListDTO> queryWaitRerouteShipBills(PageQuery<WaitRerouteShipBillCondDTO> pageQuery);

    ShipBill selectShipBillById(String waybillId);

    ShipBillDetailDTO selectShipBillDetailByWaybillId(String waybillId);

    /**
     * 根据外部运单号查询运单
     * @param externalWaybillNum
     * @return
     */
    ShipBill queryWaybillByExternalWaybillNum(String externalWaybillNum);

    /**
     * 根据运单号查询运单
     * @param WaybillNum
     * @return
     */
    ShipBill queryWaybillByWaybillNum(String WaybillNum);

    TradeWaybillDetailDO queryTradeWaybillDetail(String waybillId);

    /**
     * 待审核运单列表查询
     * @param pageQuery
     * @return
     */
    PageData<WaitCheckWaybillListDTO> queryCheckWaybillList(PageQuery<CheckWaybillListQueryDTO> pageQuery);

    /**
     * 分页查询仓库管理员待配送运单列表
     * @param pageQuery
     * @return
     */
    PageData<WarehouseAdminWaitDeliveryDTO> queryWarehouseAdminWaitDelivery(PageQuery<WarehouseAdminWaitDeliveryQueryDTO> pageQuery);

    /**
     * 分页查询仓库管理员已处理运单列表
     * @param pageQuery
     * @return
     */
    PageData<WarehouseAdminProccessedDTO> queryWarehouseAdminProccessed(PageQuery<WarehouseAdminProccessedQueryDTO> pageQuery);

    /**
     * 运单是否为平台配送
     * @param waybillId
     * @return
     */
    boolean isPlatformDelivery(String waybillId);

    /**
     * 通过父运单id列表获取子运单对象列表
     * @param parentIdList 父运单id列表
     * @return List<SubWaybillDO>
     */
    List<SubWaybillDO> selectSubWaybillListByParentId(List<String> parentIdList);

    /**
     * 根据真实委托单号查询发送短信需要的信息
     * @param realDeliveryBilNum
     * @return
     */
    List<WaybillCreatedSMSDTO> selectWaybillForSMS(String realDeliveryBilNum);

    /**
     * 查询用户抢单仓库列表
     * @param userId 用户Id
     * @return
     */
    List<WarehouseOptionDTO> selectSeckillWarehouseByUserId(String userId);

    /**
     * 获取用户可抢单区域列表
     * @param userId
     * @return
     */
    List<DistrictListDTO> selectSeckillDistrictsByUserId(String userId);


    /**
     * 查询用户可抢运单集合
     * @param pageQuery
     * @return
     */
    PageData<UserSeckillWaybillListDTO> selectUserSeckillWaybillList(PageQuery<UserSeckillWaybillQueryDTO> pageQuery);

    /**
     * 查询用户已抢到的运单集合
     * @param pageQuery
     * @return
     */
    PageData<UserSeckillWaybillListDTO> selectUserSeckilledWaybillList(PageQuery<DriverWaybillListQueryDTO> pageQuery);

    /**
     * 运单查询配送信息
     * @param waybillId
     * @return
     */
    List<ShipBillRecInfo> selectRecInfoByWaybillId(String waybillId);

    /**
     * 查询司机当前配送状态
     * @param driverId 司机Id
     * @return
     */
    Integer queryDriverDeliveryStatus(String driverId);

    /**
     * 查询司机在途的车辆
     * @param driverId
     * @return
     */
    List<String> queryHalfWayVehicleNum(String driverId);

    /**
     * 分页查询船运单列表
     * @param pageQuery 分页查询对象
     * @return PageData<ShipWaybillListDTO>
     */
    PageData<ShipWaybillListDTO> queryShipWaybillList(PageQuery<ShipWaybillQueryDTO> pageQuery);

    /**
     * 根据运单子项Id查询运单通知实体
     * @param waybillItemId
     * @return
     */
    ShipBillSMSDTO queryShipBillSMSByItemId(String waybillItemId);

    /**
     * 根据运单号查询提货信息(无提货单和数量)
     * @param waybillNumList 运单号列表
     * @return ItemResult<List<PickingBillDTO>>
     */
    List<PickingBillDTO> queryPickingListByWaybillNum(List<String> waybillNumList);

    /**
     * 获取评价运单列表
     */
    List<EvaluateWaybillListDTO> queryEvaluateWaybillList(List<String> deliverySheetNumList);

    /**
     * 判断运单是否为整合运单的主运单
     * @param waybillId
     * @return
     */
    ParentInfo getParent(String waybillId);

    /**
     * 查询被抢运单列表
     * @param snatchedWaybillQueryDTO 被抢运单查询对象
     * @return List<SnatchedWaybillDTO>
     */
    List<SnatchedWaybillDTO> querySnatchedWaybillList(SnatchedWaybillQueryDTO snatchedWaybillQueryDTO);

    /**
     * 根据运单状态和运单类型查询指定条数的发布运单信息
     * @param emallPublishWaybillQueryDTO
     * @return
     */
    List<EmallPublishWaybillListDTO> selectWaybillForEmall(EmallPublishWaybillQueryDTO emallPublishWaybillQueryDTO);

    /**
     * 运单号查询运单信息(为轨迹回放使用)
     * @param waybillNum
     * @return
     */
    PublishWaybillListDTO selectWaybillForNum(String waybillNum);

    /**
     * 运单ID查询混凝土的信息
     * @param waybillId
     * @return
     */
    ConcreteShipBillInfoDTO queryConcreteInfoByWaybillId(String waybillId);

    /**
     * 运单完成时，查询出需要生成的对账单数据
     * @param waybillId
     * @return
     */
    List<CheckWaybillInfoDTO> queryBillCheckWaybillInfoList(String waybillId);

    /**
     * 根据运单号查询已完成运单详情（信息上报调用）
     */
    List<CarryWaybillSubmitDTO> selectCarryWaybill(String waybillNum);

    /**
     * 无车承运人 -- 实际运输合同上报信息查询
     * @param waybillNum
     * @return
     */
    ContractSubmitInfoDTO queryContractInfo(String waybillNum);

    /**
     * 查询运单子项对应的运单ID集合
     * @param waybillItemIdList
     * @return
     */
    List<String> queryWaybillIdByItemIdList(List<String> waybillItemIdList);

    /**
     * 查询最新签收的运单信息
     * @param lastSignWaybillQueryDTO
     * @return
     */
    WaybillBriefDTO queryLastSignWaybill(LastSignWaybillQueryDTO lastSignWaybillQueryDTO);
}
