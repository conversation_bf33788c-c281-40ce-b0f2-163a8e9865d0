package com.ecommerce.logistics.biz;

import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.shipping.ShippingAuditDTO;
import com.ecommerce.logistics.api.dto.shipping.ShippingDeleteDTO;
import com.ecommerce.logistics.api.dto.shipping.ShippingDetailDTO;
import com.ecommerce.logistics.api.dto.shipping.ShippingListDTO;
import com.ecommerce.logistics.api.dto.shipping.ShippingQueryDTO;
import com.ecommerce.logistics.api.dto.shipping.ShippingSaveDTO;

/**
 * Author: <EMAIL>
 * Description:
 * Date: Create in 下午3:52 18/12/25
 */
public interface IShippingBizService {
    /**
     * 新增船舶
     * @param shippingSaveDTO 船舶保存对象
     * @return String 船舶ID
     */
    String addShipping(ShippingSaveDTO shippingSaveDTO);

    /**
     * 删除船舶
     * @param shippingDeleteDTO 船舶删除对象
     */
    void deleteShipping(ShippingDeleteDTO shippingDeleteDTO);

    /**
     * 编辑船舶
     * @param shippingSaveDTO 船舶保存对象
     */
    void editShipping(ShippingSaveDTO shippingSaveDTO);

    /**
     * 查询船舶详情
     * @param shippingId 船舶ID
     * @return ShippingDetailDTO 船舶详情对象
     */
    ShippingDetailDTO queryShippingDetail(String shippingId);

    /**
     * 查询船舶列表
     * @param pageQuery 船舶列表查询对象
     * @return PageData<ShippingListDTO> 船舶列表
     */
    PageData<ShippingListDTO> queryShippingList(PageQuery<ShippingQueryDTO> pageQuery);

    /**
     * 船舶审核
     * @param shippingAuditDTO 船舶审核对象
     */
    void shippingAudit(ShippingAuditDTO shippingAuditDTO);
}
