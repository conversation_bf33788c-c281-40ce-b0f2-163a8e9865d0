package com.ecommerce.logistics.biz;

import com.ecommerce.common.exception.BizException;
import com.ecommerce.logistics.api.dto.shipping.erp.BindErpShippingDTO;
import com.ecommerce.logistics.api.dto.shipping.erp.ErpShipSyncDTO;
import com.ecommerce.logistics.api.dto.shipping.erp.ErpShippingInfoDTO;

import java.util.List;

/**
 * 承运商船舶同步服务
 * <AUTHOR>
 * @Date 2021/1/11 15:56
 * @Version 1.0
 */
public interface IShippingErpInfoBizService {

    /**
     * 根据ERP承运商编号查询ERP的船舶
     * @param erpCarrierNo
     * @return
     */
    List<ErpShippingInfoDTO> findErpShippingByErpCarrierNo(String erpCarrierNo, String manufacturerMemberId);

    /**
     * 有承运商船舶信息同步,则更新，无则添加
     * @return 是否同步仓库erp信息表成功
     */
    Boolean syncErpInfo(ErpShipSyncDTO erpShipSyncDTO) throws BizException;

    /**
     * 电商和ERP船舶绑定
     * @param bindErpShippingDTO
     * @return
     */
    Boolean bindErpShippingInfo(BindErpShippingDTO bindErpShippingDTO) throws BizException;

}
