package com.ecommerce.logistics.biz;

import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.shipping.AuthorizeSellerBatchDTO;
import com.ecommerce.logistics.api.dto.shipping.AuthorizeSellerDTO;
import com.ecommerce.logistics.api.dto.shipping.BindCaptainDTO;
import com.ecommerce.logistics.api.dto.shipping.BindCaptainLogDTO;
import com.ecommerce.logistics.api.dto.shipping.EditServerTypeDTO;
import com.ecommerce.logistics.api.dto.shipping.ShippingBatchAuthorizeDTO;
import com.ecommerce.logistics.api.dto.shipping.ShippingBatchDTO;
import com.ecommerce.logistics.api.dto.shipping.ShippingInfoDTO;
import com.ecommerce.logistics.api.dto.shipping.ShippingInfoDetailsDTO;
import com.ecommerce.logistics.api.dto.shipping.ShippingInfoListDTO;
import com.ecommerce.logistics.api.dto.shipping.ShippingInfoListQueryDTO;
import com.ecommerce.logistics.api.dto.shipping.ShippingInfoQueryDTO;
import com.ecommerce.logistics.api.dto.shipping.ShippingInfoSaveDTO;
import com.ecommerce.logistics.api.dto.shipping.ShippingOperationDTO;
import com.ecommerce.logistics.api.dto.shipping.erp.BindErpShippingDTO;

import java.util.List;

/**
 * 船舶管理服务
 * <AUTHOR>
 * Created on 2020/11/23 11:27
 */
public interface IShippingInfoBizService {

    /**
     * 保存，不需要做数据必填校验
     * @param dto
     * @throws BizException
     */
    String saveShippingInfo(ShippingInfoSaveDTO dto) throws BizException;

    /**
     * 删除船舶
     * @param dto
     * @return
     * @throws BizException
     */
    void delShippingInfo(ShippingBatchDTO dto) throws BizException;

    /**
     * 提交，需要做数据必填校验
     * @param dto
     * @throws BizException
     */
    String submitShippingInfo(ShippingInfoSaveDTO dto) throws BizException;

    /**
     * 审核通过
     * @param dto
     * @throws BizException
     */
    void passShippingInfo(ShippingOperationDTO dto) throws BizException;

    /**
     * 审核驳回
     * @param dto
     * @throws BizException
     */
    void rejectShippingInfo(ShippingOperationDTO dto) throws BizException;

    /**
     * 平台查询船舶列表
     * @param pageQuery
     * @return
     * @throws BizException
     */
    PageData<ShippingInfoListDTO> queryShippingInfoListByPlatform(PageQuery<ShippingInfoListQueryDTO> pageQuery) throws BizException;

    /**
     * 查询当前用户名下船舶列表
     * @param pageQuery
     * @return
     * @throws BizException
     */
    PageData<ShippingInfoListDTO> queryShippingInfoListByCarrier(PageQuery<ShippingInfoListQueryDTO> pageQuery) throws BizException;

    /**
     * 买家，卖家查询我名下和授权给我的船舶列表
     * @param pageQuery
     * @return
     * @throws BizException
     */
    PageData<ShippingInfoListDTO> queryShippingInfoListBySeller(PageQuery<ShippingInfoListQueryDTO> pageQuery) throws BizException;

    /**
     * 查询我名下和授权给我的船舶列表
     * @param pageQuery
     * @return
     * @throws BizException
     */
    PageData<ShippingInfoDTO> queryShippingInfoListBySelf(PageQuery<ShippingInfoQueryDTO> pageQuery) throws BizException;

    /**
     * 查询船舶详情
     * @param shippingId id
     * @return
     * @throws BizException
     */
    ShippingInfoDetailsDTO queryShippingInfoDetails(String shippingId) throws BizException;

    /**
     * 多艘船舶授权一个卖家接口
     * @param dto
     * @throws BizException
     */
    void batchAuthorizeSeller(ShippingBatchAuthorizeDTO dto) throws BizException;

    /**
     * 多艘船舶取消授权一个卖家接口
     * @param dto
     * @throws BizException
     */
    void batchCancelAuthorizeSeller(ShippingBatchAuthorizeDTO dto) throws BizException;

    /**
     * 批量船舶检修
     * @param dto
     * @throws BizException
     */
    void shippingInfoOverhaul(ShippingBatchDTO dto) throws BizException;

    /**
     * 绑定船长,如果已绑定船长则先解绑
     * @param dto
     * @throws BizException
     */
    void bindCaptain(BindCaptainDTO dto) throws BizException;

    /**
     * 船舶名称校验
     * @param shippingName
     * @return true-存在，false-不存在
     * @throws BizException
     */
    boolean shippingNameValid(String shippingName) throws BizException;

    /**
     * 禁用，启用船舶
     * @param dto
     */
    void disableShippingInfo(ShippingOperationDTO dto);

    /**
     * 修改单个服务类型
     * @param dto
     * @throws BizException
     */
    void updateServerType(EditServerTypeDTO dto) throws BizException;

    /**
     * 批量修改服务类型
     * @param dto
     */
    void batchFixedTemporary(ShippingBatchDTO dto);


    /**
     * 一艘船授权批量卖家接口
     * @param dto
     * @throws BizException
     */
    void authorizeSellerList(AuthorizeSellerBatchDTO dto) throws BizException;


    /**
     * 获取授权卖家列表
     * @param shippingId
     * @throws BizException
     */
    List<AuthorizeSellerDTO> authorizeSellerListInfo(String shippingId) throws BizException;

    /**
     * 修改船舶状态
     * @param dto
     * @throws BizException
     */
    void updateShippingStatus(ShippingOperationDTO dto) throws BizException;

    /**
     * 船长绑定船舶记录
     * @param captainAccountName
     * @return
     * @throws BizException
     */
    List<BindCaptainLogDTO> queryBindCaptainLog(String captainAccountName) throws BizException;

    /**
     * 获取船务公司集合
     * @return
     */
    List<String> getShippingCompanyList(String shippingCompany);

    /**
     * 批量恢复运输
     * @param dto
     * @throws BizException
     */
    void batchRecoveryTransport(ShippingBatchDTO dto) throws BizException;

    /**
     * 会员Id查询船舶信息（APP专用）
     */
    ShippingInfoDetailsDTO queryShippingInfoByMemberId(String memberId);

    /**
     * 查询所有审核通过个体船东信息
     */
    List<ShippingInfoDTO> queryPersonalShippingInfo(String shippingName);

    /**
     * 查询所有审核通过的承运商授权给平台的船舶
     */
    List<ShippingInfoDTO> queryPlatformShippingInfo(String shippingName);

    /**
     * 获取ERP船舶绑定列表
     * @param shippingId
     * @return
     */
    List<BindErpShippingDTO> queryErpShippingBindList(String shippingId);

	/**
	 * 模糊查询船名
	 * @param queryDTO
	 * @return
	 */
	List<String> selectShippingInfoName(ShippingInfoListQueryDTO queryDTO);

    /**
     * 内部调拨船舶列表接口
     * @param memberId
     * @param carrierId
     * @param shippingName
     * @return
     */
    List<ShippingInfoListDTO> queryAllocationShippingInfo(String memberId, String carrierId, String shippingName);

}
