package com.ecommerce.logistics.biz;

import com.ecommerce.common.exception.BizException;
import com.ecommerce.logistics.api.dto.operationrecord.shipping.ShippingOperationLogAddDTO;
import com.ecommerce.logistics.api.dto.operationrecord.shipping.ShippingOperationLogDTO;

import java.util.List;

/**
 * <AUTHOR>
 * Created on 2020/11/24 15:30
 */
public interface IShippingOperationLogBizService {

    /**
     * 查询最近半年的操作记录
     * @param entryId
     * @return
     */
    List<ShippingOperationLogDTO> getOperationRecordList(String entryId);

    void saveOperationRecord(ShippingOperationLogAddDTO operationLogAddDTO) throws BizException;

    void saveOperationRecord(String entryId, int businessType,String operatorId, String operatorName, String content) throws BizException;

    void saveOperationRecord(String entryId, int businessType, String operationType, String operatorId, String operatorName, String content) throws BizException;


    /**
     * 批量保存操作记录
     * @param operationLogAddDTOList 操作记录列表
     */
    void batchSaveOperationLog(List<ShippingOperationLogAddDTO> operationLogAddDTOList) throws BizException;

    /**
     * 批量保存单据簇操作记录
     * @param entryIdList
     * @param businessType
     * @param operatorId
     * @param operatorName
     * @param content
     */
    void batchSaveBillCluster(List<String> entryIdList, int businessType,String operatorId, String operatorName, String content) throws BizException;

}
