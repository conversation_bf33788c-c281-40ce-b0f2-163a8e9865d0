package com.ecommerce.logistics.biz;

import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.shippingroute.*;
import com.ecommerce.logistics.api.dto.shippingrule.QueryShippingRuleDTO;
import com.ecommerce.logistics.api.dto.shippingrule.ShippingRuleResultDTO;

import java.util.List;
import java.util.Map;

/**
 * 航线运费定价服务
 * Created by hexin<PERSON> on 2020/11/4 16:19
 */
public interface IShippingRouteBizService {

   /**
    * 添加航线运费定价
    * @param dto
    * @return
    */
   String addShippingRoute(ShippingRouteAddDTO dto) throws BizException;

   /**
    * 删除航线运费定价
    * @param shippingRouteId 主键id
    * @param operatorUserId 操作人
    */
   void delShippingRoute(String belongerId, String shippingRouteId, String operatorUserId) throws BizException;

   /**
    * 编辑航线运费定价
    * @param dto
    */
   void editShippingRoute(ShippingRouteEditDTO dto) throws BizException;

   /**
    * 查询航线运费定价详情
    * @param shippingRouteId
    * @return
    */
   ShippingRouteDetailsDTO queryShippingRouteDetails(String belongerId, String shippingRouteId) throws BizException;

   /**
    * 查询航线运费定价列表
    * @param pageQuery
    * @return
    */
   PageData<ShippingRouteListDTO> queryShippingRouteList(PageQuery<ShippingRouteQueryDTO> pageQuery) throws BizException;

   /**
    * 删除航线运价付费
    * @param shippingRouteId 航线ID
    * @param shippingFarePaymentId 主键id
    * @param operatorUserId 操作人
    */
   void delFarePayment(String shippingRouteId, String shippingFarePaymentId, String operatorUserId, String operatorUserName) throws BizException;


   /**
    * 下载操作记录
    * @param shippingRouteId 航线ID
    * @return
    */
   List<ShippingAuxiliaryOperateLogDTO> downloadShippingRouteOperateLog(String shippingRouteId) throws BizException;


   /**
    * 交易航运运费规则查询
    * @param queryShippingRuleDTO 航运运费规则查询对象
    * @return List<ShippingRuleResultDTO>
    */
   List<ShippingRuleResultDTO> queryShippingRule(QueryShippingRuleDTO queryShippingRuleDTO);


   /**
    * 查询航运付费规则
    * @param queryShippingRuleDTO 航运运费规则查询对象
    * @return List<ShippingRuleResultDTO>
    */
   List<ShippingRuleResultDTO> queryShippingPaymentRule(QueryShippingRuleDTO queryShippingRuleDTO);


   /**
    * 获取当前卖家的客户收货码头地址列表
    * @param belongerId
    * @return
    */
   Map<String, String> queryUnloadingWharf(String belongerId);

   /**
    * 获取当前卖家的客户装货码头地址列表
    * @param belongerId
    * @return
    */
   Map<String, String> queryPickingWharf(String belongerId);
}
