package com.ecommerce.logistics.biz;

import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.transportaddress.TransportAddressDeleteDTO;
import com.ecommerce.logistics.api.dto.transportaddress.TransportAddressDetailDTO;
import com.ecommerce.logistics.api.dto.transportaddress.TransportAddressListDTO;
import com.ecommerce.logistics.api.dto.transportaddress.TransportAddressQueryDTO;
import com.ecommerce.logistics.api.dto.transportaddress.TransportAddressSaveDTO;
import com.ecommerce.logistics.api.dto.transportaddress.TransportAddressSearchDTO;
import com.ecommerce.logistics.dao.vo.TransportAddress;

import java.util.List;

/**
 * Author: <EMAIL>
 * Description:
 * Date: Create in 上午10:12 18/12/21
 */
public interface ITransportAddressBizService {

    /**
     * 添加运输地址
     * @param transportAddressSaveDTO 运输地址保存对象
     * @return String 运输地址ID
     */
    String addTransportAddress(TransportAddressSaveDTO transportAddressSaveDTO);

    /**
     * 删除运输地址
     * @param transportAddressDeleteDTO 运输地址删除对象
     */
    void deleteTransportAddress(TransportAddressDeleteDTO transportAddressDeleteDTO);

    /**
     * 编辑运输地址
     * @param transportAddressSaveDTO 运输地址保存对象
     */
    void editTransportAddress(TransportAddressSaveDTO transportAddressSaveDTO);

    /**
     * 查询运输地址详情
     * @param transportAddressId 运输地址ID
     * @return TransportAddressDetailDTO 运输地址详情对象
     */
    TransportAddressDetailDTO queryTransportAddressDetail(String transportAddressId);

    /**
     * 查询运输地址列表
     * @param pageQuery 运输地址列表查询对象
     * @return PageData<TransportAddressListDTO> 运输地址列表
     */
    PageData<TransportAddressListDTO> queryTransportAddressList(PageQuery<TransportAddressQueryDTO> pageQuery);

    /**
     * 批量查询运输地址列表
     * @param transportAddressIdList 运输地址Id列表
     * @return List<TransportAddress>
     */
    List<TransportAddress> queryTransportAddressListByIds(List<String> transportAddressIdList);

    /**
     * 搜索运输地址列表
     * @param transportAddressSearchDTO 运输地址列表搜索对象
     * @return List<TransportAddressListDTO> 运输地址列表
     */
    List<TransportAddressListDTO> searchTransportAddressList(TransportAddressSearchDTO transportAddressSearchDTO);
}
