package com.ecommerce.logistics.biz;

import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.transportcapacity.TransportCapacityDeleteDTO;
import com.ecommerce.logistics.api.dto.transportcapacity.TransportCapacityDetailDTO;
import com.ecommerce.logistics.api.dto.transportcapacity.TransportCapacityListDTO;
import com.ecommerce.logistics.api.dto.transportcapacity.TransportCapacityQueryDTO;
import com.ecommerce.logistics.api.dto.transportcapacity.TransportCapacitySaveDTO;
import com.ecommerce.logistics.api.dto.transportcapacity.TransportCapacityViewQueryDTO;

import java.util.List;

/**
 * Author: <EMAIL>
 * Description:
 * Date: Create in 上午11:43 18/12/19
 */
public interface ITransportCapacityBizService {
    /**
     * 发布运力
     * @param transportCapacitySaveDTO 运力保存对象
     * @return String 运力ID
     */
    String addTransportCapacity(TransportCapacitySaveDTO transportCapacitySaveDTO);

    /**
     * 删除运力
     * @param transportCapacityDeleteDTO 运力删除对象
     */
    void deleteTransportCapacity(TransportCapacityDeleteDTO transportCapacityDeleteDTO);

    /**
     * 编辑运力
     * @param transportCapacitySaveDTO 运力保存对象
     */
    void editTransportCapacity(TransportCapacitySaveDTO transportCapacitySaveDTO);

    /**
     *  查询运力详情
     * @param transportCapacityId 运力ID
     * @return TransportCapacityDetailDTO 运力详情对象
     */
    TransportCapacityDetailDTO queryTransportCapacityDetail(String transportCapacityId);

    /**
     *  查询运力列表
     * @param pageQuery 运力列表查询对象
     * @return List<TransportCapacityListDTO> 运力列表
     */
    PageData<TransportCapacityListDTO> queryTransportCapacityList(PageQuery<TransportCapacityQueryDTO> pageQuery);

    /**
     * 查询运力浏览记录
     * @param transportCapacityViewQueryDTO 运力浏览记录查询对象
     * @return List<TransportCapacityListDTO> 运力列表
     */
    List<TransportCapacityListDTO> queryTransportCapacityViewRecord(TransportCapacityViewQueryDTO transportCapacityViewQueryDTO);
}
