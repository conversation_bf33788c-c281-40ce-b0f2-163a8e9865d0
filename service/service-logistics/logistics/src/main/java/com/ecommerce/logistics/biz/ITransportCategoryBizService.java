package com.ecommerce.logistics.biz;

import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.transportcategory.TransportCategoryAddDTO;
import com.ecommerce.logistics.api.dto.transportcategory.TransportCategoryDTO;
import com.ecommerce.logistics.api.dto.transportcategory.TransportCategoryDetailDTO;
import com.ecommerce.logistics.api.dto.transportcategory.TransportCategoryEditDTO;
import com.ecommerce.logistics.api.dto.transportcategory.TransportCategoryListDTO;
import com.ecommerce.logistics.api.dto.transportcategory.TransportCategoryListQueryDTO;
import com.ecommerce.logistics.api.dto.transportcategory.TransportCategoryRemoveDTO;
import com.ecommerce.logistics.api.dto.transportcategory.*;
import com.ecommerce.logistics.dao.dto.transport.WaybillIdCategoryMappingDO;

import java.util.List;

/**
 * @Auther: <EMAIL>
 * @Date: 30/08/2018 10:47
 * @Description:
 */
public interface ITransportCategoryBizService {

    /**
     * 获取运输品类列表
     * @param pageQuery
     * @return
     */
    PageData<TransportCategoryListDTO> queryTransportCategoryList(PageQuery<TransportCategoryListQueryDTO> pageQuery);

    /**
     * 获取运输品类详情
     * @param transportCategoryId
     * @return TransportCategoryDetailDTO
     */
    TransportCategoryDetailDTO queryTransportCategory(String transportCategoryId);

    /**
     * 新增运输品类
     * @param transportCategoryAddDTO
     * @return void
     */
    void saveTransportCategory(TransportCategoryAddDTO transportCategoryAddDTO);

    /**
     * 逻辑删除运输品类
     * @param transportCategoryRemoveDTO
     */
    void removeTransportCategory(TransportCategoryRemoveDTO transportCategoryRemoveDTO);

    /**
     * 修改运输品类
     * @param transportCategoryEditDTO
     */
    void modifyTransportCategory(TransportCategoryEditDTO transportCategoryEditDTO);

    /**
     *  查询所有
     * @return
     */
    List<TransportCategoryDTO> queryAll();

    List<TransportOption> queryOptions();

    /**
     * 运输品类合并过滤
     * @param transportCategoryIdList 品类Id列表
     * @return List<String> 可以合并的品类Id列表
     */
    List<String> transportCategoryMergeFilter(List<String> transportCategoryIdList);

    /**
     * 运输品类列表
     * @param transportCategoryIdList 运输品类ID列表
     * @return List<TransportCategoryDTO>
     */
    List<TransportCategoryDTO> queryTransportCategoryListById(List<String> transportCategoryIdList);

    /**
     * 查询运单与运输品类的映射
     * @param waybillIdList
     * @return
     */
    List<WaybillIdCategoryMappingDO> queryTransportCategoryByWaybillIdList(List<String> waybillIdList);

    List<WaybillIdCategoryMappingDO> queryTransportCategoryByParentIdList(List<String> parentIdList);

    /**
     * 多条运输品类的ID(可能为空)查询品类名,并逗号联合
     * @param transportCategoryIds
     * @return
     */
    String joinNameByIds(String transportCategoryIds);

}
