package com.ecommerce.logistics.biz;

import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.transportdemand.TransportDemandAuditDTO;
import com.ecommerce.logistics.api.dto.transportdemand.TransportDemandDeleteDTO;
import com.ecommerce.logistics.api.dto.transportdemand.TransportDemandDetailDTO;
import com.ecommerce.logistics.api.dto.transportdemand.TransportDemandListDTO;
import com.ecommerce.logistics.api.dto.transportdemand.TransportDemandQueryDTO;
import com.ecommerce.logistics.api.dto.transportdemand.TransportDemandSaveDTO;
import com.ecommerce.logistics.api.dto.transportdemand.TransportDemandViewQueryDTO;

import java.util.List;

/**
 * Author: <EMAIL>
 * Description:
 * Date: Create in 上午10:29 18/12/20
 */
public interface ITransportDemandBizService {
    /**
     * 发布运输需求
     * @param transportDemandSaveDTO 运输需求保存对象
     * @return String 运输需求ID
     */
    String addTransportDemand(TransportDemandSaveDTO transportDemandSaveDTO);

    /**
     * 删除运输需求
     * @param transportDemandDeleteDTO 运输需求删除对象
     */
    void deleteTransportDemand(TransportDemandDeleteDTO transportDemandDeleteDTO);

    /**
     * 编辑运输需求
     * @param transportDemandSaveDTO 运输需求保存对象
     */
    void editTransportDemand(TransportDemandSaveDTO transportDemandSaveDTO);

    /**
     * 查询运输需求详情
     * @param transportDemandId 运输需求ID
     * @return TransportDemandDetailDTO 运输需求详情对象
     */
    TransportDemandDetailDTO queryTransportDemandDetail(String transportDemandId);

    /**
     * 查询运输需求列表
     * @param pageQuery 运输需求列表查询对象
     * @return PageData<TransportDemandListDTO> 运输需求列表
     */
    PageData<TransportDemandListDTO> queryTransportDemandList(PageQuery<TransportDemandQueryDTO> pageQuery);

    /**
     * 审核运输需求
     * @param transportDemandAuditDTO 运输需求审核对象
     */
    void transportDemandAudit(TransportDemandAuditDTO transportDemandAuditDTO);

    /**
     * 查询运输需求浏览列表
     * @param transportDemandViewQueryDTO 运输需求浏览记录查询对象
     * @return List<TransportDemandListDTO> 运输需求列表
     */
    List<TransportDemandListDTO> queryTransportDemandViewRecord(TransportDemandViewQueryDTO transportDemandViewQueryDTO);
}
