package com.ecommerce.logistics.biz;

import com.ecommerce.base.api.dto.warehouse.PageQuery;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.contract.*;

/**
 * 托运合同服务
 * Created by hexinhui on 2020/10/20 15:04
 */
public interface ITrusteeContractBizService {

    /**
     * 新增托运合同
     * @param trusteeContractAddDTO
     * @return 合同主键
     */
    String addTrusteeContractInfo(TrusteeContractAddDTO trusteeContractAddDTO) throws BizException;

    /**
     * 删除托运合同
     * @param contractIds 主键
     */
    void delTrusteeContractInfo(String contractIds, String operatorUserId, String operatorUserName) throws BizException;

    /**
     * 修改托运合同
     * @param trusteeContractEditDTO
     */
    void editTrusteeContractInfo(TrusteeContractEditDTO trusteeContractEditDTO) throws BizException;

    /**
     * 查看托运合同详情
     * @param contractId 主键
     */
    TrusteeContractDetailsDTO findTrusteeContractDetails(String contractId) throws BizException;

    /**
     * 查询托运合同列表
     * @param pageQuery 查询对象
     * @return
     */
    PageData<TrusteeContractListDTO> queryTrusteeContractList(PageQuery<TrusteeContractListQueryDTO> pageQuery) throws BizException;

}
