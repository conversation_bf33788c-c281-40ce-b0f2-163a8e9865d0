package com.ecommerce.logistics.biz;

import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.unloadingobjection.ObjectionHandleDetailDTO;
import com.ecommerce.logistics.api.dto.unloadingobjection.UnloadingObjectionAddDTO;
import com.ecommerce.logistics.api.dto.unloadingobjection.UnloadingObjectionDetailDTO;
import com.ecommerce.logistics.api.dto.unloadingobjection.UnloadingObjectionHandleDTO;
import com.ecommerce.logistics.api.dto.unloadingobjection.UnloadingObjectionListDTO;
import com.ecommerce.logistics.api.dto.unloadingobjection.UnloadingObjectionQueryDTO;
import com.ecommerce.logistics.dao.vo.UnloadingObjection;

import java.util.List;

/**
 * @Description:
 * @Author: <EMAIL>
 * @Date: 21/10/2020 17:25
 */
public interface IUnloadingObjectionBizService {

    /**
     * 添加卸货异议
     * @param unloadingObjectionAddDTO
     * @return
     */
    String addObjection(UnloadingObjectionAddDTO unloadingObjectionAddDTO);

    /**
     * 查询卸货异议列表
     * @param pageQuery
     * @return
     */
    PageData<UnloadingObjectionListDTO> selectObjectionList(PageQuery<UnloadingObjectionQueryDTO> pageQuery);

    /**
     * 查询卸货异议处理记录
     * @param objectionId
     */
    ObjectionHandleDetailDTO findHandleDetailById(String objectionId);

    /**
     * 通过Id查询卸货异议
     * @param ids
     * @return
     */
    List<UnloadingObjection> findByIds(List<String> ids);

    /**
     * 批量取消卸货异议
     * @param ids
     * @param operator
     */
    void batchCancelObjectionByIds(List<String> ids,String operator);

    /**
     * 处理卸货异议
     * @param handleDTO
     */
    String handleObjection(UnloadingObjectionHandleDTO handleDTO);

    /**
     * 完成卸货异议
     * @param handleDTO
     */
    String completeObjection(UnloadingObjectionHandleDTO handleDTO);

    /**
     * 查询卸货异议详情
     * @param objectionId
     */
    UnloadingObjectionDetailDTO findObjectionDetailById(String objectionId);
}
