package com.ecommerce.logistics.biz;

import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.intelligentDispatch.UnloadingTimeDTO;

public interface IUnloadingTimeBizService {

    /**
     * 新增卸货时间
     */
    Boolean add(UnloadingTimeDTO createDTO);

    /**
     * 编辑卸货时间
     */
    Boolean edit(UnloadingTimeDTO updateDTO);

    /**
     * 删除卸货时间
     */
    Boolean delete(UnloadingTimeDTO deleteDTO);

    /**
     * 分页查询卸货时间
     */
    PageData<UnloadingTimeDTO> pageUnloadingTime(PageQuery<UnloadingTimeDTO> pageQuery);

    /**
     * 根据条件查询卸货时间
     */
    UnloadingTimeDTO findByCondition(String addressId, String userId, Integer userType);
}
