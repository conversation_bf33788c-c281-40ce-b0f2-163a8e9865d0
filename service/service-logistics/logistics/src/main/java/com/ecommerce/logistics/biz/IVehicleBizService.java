package com.ecommerce.logistics.biz;

import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.DeliveryDetailDTO;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.driver.BindDriverDTO;
import com.ecommerce.logistics.api.dto.driver.PersonVehicleDTO;
import com.ecommerce.logistics.api.dto.pickingbill.PickingBillDTO;
import com.ecommerce.logistics.api.dto.vehicle.ERPVehicleDTO;
import com.ecommerce.logistics.api.dto.vehicle.ExternalVehicleAppDTO;
import com.ecommerce.logistics.api.dto.vehicle.ExternalVehicleDTO;
import com.ecommerce.logistics.api.dto.vehicle.UserVehicleListDTO;
import com.ecommerce.logistics.api.dto.vehicle.UserVehicleListQueryDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleAddDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleAppAddDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleAppDataDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleAppEditDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleAppListDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleBaseDataDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleBatchAddResultDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleBuyerTakeQueryDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleCertificationStatusDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleDefaultCondDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleDefaultResDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleDetailDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleDisableFlgEditDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleEditDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleImportTemplateOptionDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleListDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleListQueryDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleModifyIsDefaultDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleOptionsDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleOptionsQueryDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleQueryConditionDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleRemoveDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleSignalEditDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleUserCondDTO;
import com.ecommerce.logistics.api.param.vehiclecert.VehicleAddParamDTO;
import com.ecommerce.logistics.dao.vo.Vehicle;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Auther: <EMAIL>
 * @Date: 07/08/2018 16:20
 * @Description:
 */
public interface IVehicleBizService{

    /**
     * 根据车牌查询车辆信息
     * @param vehicleNumber
     * @return
     */
    ExternalVehicleDTO queryVehicleInfoByNumber(String vehicleNumber);

    /**
     * 寻找默认指派车辆
     * @param vehicleDefaultCondDTO
     * @return
     */
    VehicleDefaultResDTO findDefaultAssignVehicle(VehicleDefaultCondDTO vehicleDefaultCondDTO);

    PageData<VehicleListDTO> queryVehicleList(PageQuery<VehicleListQueryDTO> pageQuery) ;

    /**
     * 平台获取车辆列表
     * @param pageQuery
     * @return
     */
    PageData<VehicleListDTO> queryVehicleListPlatform(PageQuery<VehicleListQueryDTO> pageQuery);

    @Deprecated(since = "2.1.4-RELEASE")
    String saveVehicle(VehicleAddDTO vehicleAddDTO);

    String saveVehicleV2(VehicleAddParamDTO vehicleAddParamDTO);

    void removeVehicle(List<VehicleRemoveDTO> vehicleRemoveDTOS);

    void modifyVehicle(VehicleEditDTO vehicle);

    VehicleDetailDTO queryVehicle(String vehicleId);

    void modifyCertificationStatusToOk(VehicleCertificationStatusDTO vehicleCertificationStatusDTO);

    void modifyCertificationStatusToFailure(VehicleCertificationStatusDTO vehicleCertificationStatusDTO);

    List<Vehicle> selectAll();

    VehicleDTO selectVehicleById(String vehicleId);

    PageData<UserVehicleListDTO> queryVehicleListByUserId(PageQuery<UserVehicleListQueryDTO> pageQuery);

    Vehicle selectVehicleByCondition(VehicleQueryConditionDTO vehicleQueryConditionDTO);

    /**
     *
     * @Auther: <EMAIL>
     * @Date: 2018年9月12日 下午3:59:43
     * @param userIds
     * @param quantity
     * @return
     */
    List<String> selectUserIdsByOverCapacity(List<String> userIds, BigDecimal quantity);

    List<VehicleAppDataDTO> queryVehicleByUserId(String userId);

    List<VehicleAppListDTO> queryAppVehicleList(String userId);

    /**
     * 用户类型+用户ID查询App车辆列表
     * @param vehicleUserCondDTO
     * @return
     */
    List<VehicleAppListDTO> queryAppVehicleListByUserDTO(VehicleUserCondDTO vehicleUserCondDTO);

    String addAppVehicle(VehicleAppAddDTO vehicleAppAddDTO);

    void modifyAppVehicle(VehicleAppEditDTO vehicleAppEditDTO);

    void modifyAppIsDefault(VehicleModifyIsDefaultDTO vehicleModifyIsDefaultDTO);

    List<VehicleBaseDataDTO> queryVehicleBaseData(String userId);

    List<VehicleOptionsDTO> queryVehicleOptions(VehicleOptionsQueryDTO vehicleOptionsQueryDTO);

    void submitAuthentication(VehicleCertificationStatusDTO vehicleCertificationStatusDTO);

    String getCertificationStatus(String userId);

    void updateVehicleSignFlag(VehicleSignalEditDTO vehicleSignalEditDTO);

    /**
     * 添加车辆并直接提交审核
     *  操作日志修改为添加车辆
     * @param vehicleAddDTO
     * @return
     */
    String addVehicleAndSubmitAuth(VehicleAddDTO vehicleAddDTO);

    /**
     * 条件查询车辆
     * @param userId 归属用户Id
     * @param vehicleNum 车牌号
     * @return List<Vehicle>
     */
    List<Vehicle> queryVehicleByCondition(String userId, String vehicleNum);

    /**
     * 添加erp车辆
     * @param erpVehicleDTO erp车辆对象
     * @param pickingBillDTO 提货单对象
     * @param loadCapacity 车辆载重
     * @return String 车辆ID
     */
    public String addErpVehicle(ERPVehicleDTO erpVehicleDTO,
                                PickingBillDTO pickingBillDTO,
                                BigDecimal loadCapacity);

    /**
     * 添加erp车辆
     * @param erpVehicleDTO erp车辆对象
     * @param deliveryDetailDTO 委托单对象
     * @param loadCapacity 车辆载重
     * @return String 车辆ID
     */
    public String addErpVehicle(ERPVehicleDTO erpVehicleDTO,
                                DeliveryDetailDTO deliveryDetailDTO,
                                BigDecimal loadCapacity);

    /**
     * 查询车辆是否有GPS设备
     * @param vehicleNum
     * @return
     */
    Integer vehicleIsGpsDevice(String vehicleNum);

    /**
     * 批量添加车辆
     * @param vehicleAddDTOList
     */
    VehicleBatchAddResultDTO batchSaveVehicle(List<VehicleAddDTO> vehicleAddDTOList);

    /**
     * 批量添加非管控车辆
     * @param vehicleAppAddDTOList
     */
    VehicleBatchAddResultDTO batchAddAppVehicle(List<VehicleAppAddDTO> vehicleAppAddDTOList);

    /**
     * 查询生成批量导入车辆模板需要的下拉列表
     * @return
     */
    VehicleImportTemplateOptionDTO queryVehicleImportTemplateOptions();

   /**
     * 绑定外协车辆
     * @param operateUserId 操作人id
     * @param number 车牌号
     * @param carrierId 承运商id
     */
    void bindingVehicle(String operateUserId, String number, String carrierId);

    /**
     * 解绑外协车辆
     * @param operateUserId 操作人id
     * @param number 车牌号
     * @param carrierId 承运商id
     */
    void unBindingVehicle(String operateUserId, String number,String carrierId);

    /**
     * 修改车辆是否禁用
     * @param list
     */
    void updateVehicleDisableFlg(List<VehicleDisableFlgEditDTO> list);

    /**
     * 根据归属人,类型和车牌号,找到最新一条的车辆ID[主要用于自提]
     * @param userId
     * @param userType
     * @param number
     * @return
     */
    String selectOneIdByUserAndNumber(String userId, Integer userType, String number);

    /**
     * 根据车辆Id查询归属承运商名称
     */
    String selectCarrierNameByVehicle(String Vehicle);

    /**
     * 获取买家自提车辆
     * @param queryDTO VehicleBuyerTakeQueryDTO
     * @return List<VehicleBaseDataDTO>
     */
    List<VehicleBaseDataDTO> getBuyerTakeCars(VehicleBuyerTakeQueryDTO queryDTO);

    /**
     * 根据车牌号条件查询所有认证成功的个体车辆(去掉绑定了承运商的车)
     */
    List<VehicleDetailDTO> queryPersonalVehicle(VehicleListQueryDTO queryDTO);

    /**
     * 绑定司机
     * @param dto
     */
	void bindDriver(BindDriverDTO dto);

	/**
     * 根据车牌查询车辆信息 APP
     * @param vehicleNumber
     * @return
     */
	ExternalVehicleAppDTO selectVehicleListByNumber(String number);

    /**
     * 根据会员id查询已经绑定承运商的个体司机
     */
    List<String> queryPersonUserId(List<String> memberIds);

    /**
     * 通过车牌号获取GPS设备信息
     * @param vehicleNum
     * @return
     */
    Vehicle getVehicleGpsDeviceByNum(String vehicleNum);

    /**
     * 查询用户ID的个体车辆信息
     * @param userId
     * @return
     */
    PersonVehicleDTO queryPersonVehicleByUserId(String userId);

    /**
     * 车辆导出EXCEL表格
     * @param vehicleListQueryDTO
     * @return
     */
    List<VehicleListDTO> queryExportVehicleList(VehicleListQueryDTO vehicleListQueryDTO);

    /**
     * 通过用户ID查询车辆下拉列表
     * @param queryDTO
     * @return
     */
    List<UserVehicleListDTO> queryVehicleListDropDownBox(UserVehicleListQueryDTO queryDTO);

	/**
	 * 模糊查询车牌号
	 * @param queryDTO
	 * @return
	 */
	List<String> selectVehicleNumber(VehicleListQueryDTO queryDTO);

}
