package com.ecommerce.logistics.biz;

import com.ecommerce.logistics.api.dto.vehiclecert.VehicleCertDTO;
import com.ecommerce.logistics.api.dto.vehiclecert.DrivingLicenseInfoDTO;
import com.ecommerce.logistics.api.param.vehiclecert.VehicleCertEditParam;
import com.ecommerce.logistics.api.param.vehiclecert.VehicleCertSaveParam;

import java.util.List;

/**
 * Copyright (C),2020
 *
 * @className: IVehicleCertBizService
 * @author: <EMAIL>
 * @Date: 2020/10/12 11:35 上午
 * @Description:
 */
public interface IVehicleCertBizService {


    /**
     * 根据用户ID查询车辆认证信息
     *
     * @param userId
     * @return 车辆认证信息
     */
    VehicleCertDTO queryVehicleCertByUserId(String userId);

    /**
     * 根据车辆ID查询车辆和认证信息
     * @param vehicleId
     * @return
     */
    VehicleCertDTO queryVehicleCertByVehicleId(String vehicleId);


    /**
     * 新增车辆认证信息
     *
     * @param VehicleCertSaveParam 车辆认证信息新增入参
     * @return 车辆ID
     */
    String saveVehicleCertAndSubmitAuth(VehicleCertSaveParam VehicleCertSaveParam);


    /**
     * 编辑车辆认证信息
     *
     * @param vehicleCertEditParam 车辆认证信息编辑入参
     * @return 车辆ID
     */
    String modifyVehicleCert(VehicleCertEditParam vehicleCertEditParam);

    /**
     * 查询所有行驶证及其归属人
     */
    List<DrivingLicenseInfoDTO> selectAllDrivingLicense();
}
