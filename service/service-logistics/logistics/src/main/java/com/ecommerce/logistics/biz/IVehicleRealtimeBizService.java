package com.ecommerce.logistics.biz;

import com.ecommerce.logistics.api.dto.realtime.VehicleRelationCondDTO;

/**
 * @Auther: colu
 * @Date: 2020-07-23 14:45
 * @Description: 车辆实时处理的服务
 */
public interface IVehicleRealtimeBizService {

    /**
     * 车辆实时查询条件的过滤处理方法
     * @param vehicleRelationCondDTO
     * @return
     */
    VehicleRelationCondDTO filterVehicleCond(VehicleRelationCondDTO vehicleRelationCondDTO);

}
