package com.ecommerce.logistics.biz;

import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.vehicletype.VehicleTypeAddDTO;
import com.ecommerce.logistics.api.dto.vehicletype.VehicleTypeDetailDTO;
import com.ecommerce.logistics.api.dto.vehicletype.VehicleTypeEditDTO;
import com.ecommerce.logistics.api.dto.vehicletype.VehicleTypeListDTO;
import com.ecommerce.logistics.api.dto.vehicletype.VehicleTypeListQueryDTO;
import com.ecommerce.logistics.api.dto.vehicletype.VehicleTypeNameDTO;
import com.ecommerce.logistics.api.dto.vehicletype.VehicleTypeOptionDTO;
import com.ecommerce.logistics.api.dto.vehicletype.VehicleTypeRemoveDTO;
import com.ecommerce.logistics.dao.vo.VehicleType;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

/**
 * @Auther: <EMAIL>
 * @Date: 21/08/2018 16:24
 * @Description:
 */
public interface IVehicleTypeBizService {

    /**
     * 获取车辆类型列表
     * @param pageQuery
     * @return PageData<VehicleTypeListDTO>
     */
    PageData<VehicleTypeListDTO> queryVehicleTypeList(PageQuery<VehicleTypeListQueryDTO> pageQuery);

    /**
     * 新增车辆类型
     * @param vehicleTypeAddDTO
     * @return void
     */
    void saveVehicleType(VehicleTypeAddDTO vehicleTypeAddDTO);

    /**
     * 逻辑删除车辆类型
     * @param vehicleTypeRemoveDTO
     * @return void
     */
    void removeVehicleType(VehicleTypeRemoveDTO vehicleTypeRemoveDTO);

    /**
     * 修改车辆类型
     * @param vehicleTypeEditDTO
     * @return void
     */
    void modifyVehicleType(VehicleTypeEditDTO vehicleTypeEditDTO);

    /**
     * 车辆类型的详细数据
     * @param vehicleTypeId
     * @return VehicleTypeDetailDTO
     */
    VehicleTypeDetailDTO queryVehicleType(String vehicleTypeId);

    /**
     * 根据配送数量查询车型
     * @param quantity 配送数量
     * @return  List<VehicleType> 车型列表
     */
    List<VehicleType> queryVehicleTypeByQuantity(BigDecimal quantity, Collection<String> transportCategoryIdCollection);

    /**
     * 条件查询车型列表
     * @param transportCategoryId 运输品类Id
     * @return List<VehicleType>
     */
    List<VehicleType> queryVehicleTypeByCondition(String transportCategoryId);

    List<VehicleTypeOptionDTO> queryOptions();

	List<VehicleTypeListDTO> queryVehicleTypes(List<String> vehicleTypeIdList);

    /**
     * 查询车型对应的运输品类
     * @param vehicleTypeId
     * @return
     */
	String queryTransportCategoryIdByVehicleType(String vehicleTypeId);

    /**
     * 查询出不限车型
     * @return
     */
    VehicleTypeNameDTO querytUnlimiteType();

    /**
     * 查询个体司机的保证金应缴额度
     * @param userId
     * @return
     */
    BigDecimal queryDepositAmountByDriverUserId(String userId);

}
