package com.ecommerce.logistics.biz;

import java.util.List;

import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.rulecompute.QueryZoneDTO;
import com.ecommerce.logistics.api.dto.warehouse.*;
import com.ecommerce.logistics.dao.vo.Warehouse;
import com.ecommerce.logistics.dao.vo.WarehouseZoneMap;

/**
 * @Description:
 * @Author: <EMAIL>
 * @Date: 2018-08-09 11:54
 */
@Deprecated(since = "2.1.4-RELEASE")
public interface IWarehouseBizService {
    /**
     * 根据仓库id批量查询仓库列表
     * @param warehouseIdList 仓库ID列表
     * @return List<Warehouse>
     */
    List<Warehouse> queryWarehouseListByIds(List<String> warehouseIdList);

    void saveWarehouse(Warehouse warehouse ,List<WarehouseZoneMap> list);

    void removeWarehouse(WarehouseRemoveDTO warehouseRemoveDTO);

    PageData<WarehouseListDTO> getWarehouseList(PageQuery<WarehouseListQueryDTO> pageQuery);

    void modifyWarehouse(WarehouseEditDTO warehouseEditDTO, List<WarehouseZoneMap> maps);

    List<WarehouseOptionDTO> queryWarehouseOption(WarehouseOptionQueryDTO warehouseOptionQueryDTO);
    /**
     * 获取省范围内的配送地址
     * @param queryZoneList 查询区域列表
     * @return List<WarehouseZoneMap>
     */
    List<WarehouseZoneMap> queryProvinceWarehouse(List<String> typeList, List<QueryZoneDTO> queryZoneList);

    /**
     * 获取市范围内的配送地址
     * @param queryZoneList 查询区域列表
     * @return List<WarehouseZoneMap>
     */
    List<WarehouseZoneMap> queryCityWarehouse(List<String> typeList, List<QueryZoneDTO> queryZoneList);

    /**
     * 获取区域范围内的配送地址
     * @param queryZoneList 查询区域列表
     * @return List<WarehouseZoneMap>
     */
    List<WarehouseZoneMap> queryDistrictWarehouse(List<String> typeList, List<QueryZoneDTO> queryZoneList);

    /**
     * 获取仓库详情
     * @param warehouseId
     * @return
     */
    WarehouseDetailsDTO queryWarehouseDetails(String warehouseId);

    String searchAddress(SearchAddressDTO searchAddressDTO);

    /**
     * 获取用户可抢单仓库列表
     * @Auther: <EMAIL>
     * @Date: 2018年9月11日 下午5:24:34
     * @param info
     * @return
     */
	List<WarehouseOptionDTO> selectSeckillWarehouseByUserId(String userId);

	WarehouseBaseDataDTO selectWarehouseBaseData(String warehouseId);

    /**
     * 查询仓库信息
     * @param warehouseId 仓库Id
     * @return Warehouse
     */
    Warehouse selectWarehouseById(String warehouseId);

    /**
     * 查询区域内中心仓
     * @param centralWarehouseQueryDTO 区域中心仓查询对象
     * @return WarehouseDetailsDTO
     */
    WarehouseDetailsDTO queryCentralWarehouseByArea(CentralWarehouseQueryDTO centralWarehouseQueryDTO);

    /**
     * 获取仓库详情
     * @param warehouseId 仓库ID
     * @return
     */
    WarehouseDetailsDTO queryWarehouseDetail(String warehouseId);
}
