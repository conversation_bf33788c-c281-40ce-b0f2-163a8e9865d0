package com.ecommerce.logistics.biz;

import java.math.BigDecimal;

/**
 * @Auther: colu
 * @Date: 2020-11-26 16:03
 * @Description: 运单价格信息业务接口
 */
public interface IWaybillAmtBizService {

    /**
     * 初始化运单价格信息
     * @param waybillId
     * @param waybillNum
     * @param parentId
     * @param pickingBillId
     * @param operatorId
     * @param operatorName
     * @return
     */
    String initWaybillAmt(String waybillId, String waybillNum, String parentId, String pickingBillId, String operatorId, String operatorName);

    /**
     * 实际出厂时更新运单的价格明细
     * @param waybillId
     * @param goodsPrice
     * @param actualQuantity
     * @param operatorId
     * @param operatorName
     */
    void updateByActualQuantity(String waybillId, BigDecimal goodsPrice, BigDecimal actualQuantity, String operatorId, String operatorName);

}
