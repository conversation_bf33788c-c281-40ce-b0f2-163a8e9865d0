package com.ecommerce.logistics.biz;

import com.ecommerce.logistics.api.dto.rulecompute.WaybillMergeDTO;
import com.ecommerce.logistics.api.dto.waybill.CreateWaybillDTO;
import com.ecommerce.logistics.dao.vo.Waybill;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: <EMAIL>
 * @Description:
 * @Date: Create in 上午9:41 18/8/14
 */
public interface IWaybillCompositeBizService {
    /**
     * 创建运单
     * @param createWaybillDTO 创建运单对象
     * @return String 运单ID
     */
    String createWaybill(CreateWaybillDTO createWaybillDTO);

    /**
     * 创建运单, 同createWaybill,返回运单全信息
     * @param createWaybillDTO
     * @return
     */
    Waybill createWaybillAndReturn(CreateWaybillDTO createWaybillDTO);

    /**
     * 添加提货单商品数量信息
     * @param quantity 商品数量
     * @param mapId 映射ID
     * @param mapType 映射类型
     * @param relatedId 商品信息关联ID
     */
    void addProductQuantity(BigDecimal quantity,
                            String mapId,
                            Integer mapType,
                            String relatedId);

    /**
     * 合并子运单
     *
     * @param createWaybillDTO 运单创建对象
     * @param waybillMergeDTO 运单合并对象
     * @return String 主运单号
     */
    String mergeSubWaybill(CreateWaybillDTO createWaybillDTO,
                           WaybillMergeDTO waybillMergeDTO,
                           List<String> orderWaybillList);

    /**
     * 批量修改子运单状态
     * @param waybillIdList
     * @param status
     * @param parentWaybillId
     * @param isMainWaybill 是否主运单
     */
    void updateSubWaybillStatus(List<String> waybillIdList,
                                String status,
                                String parentWaybillId,
                                Integer isMainWaybill);

    /**
     * 批量修改子运单状态
     * @param status 更新状态
     * @param parentWaybillId 父运单ID
     * @param currentStatus 当前的运单状态
     */
    void updateSubWaybillStatus(String status, String parentWaybillId, String currentStatus);

    /**
     * 添加运单信息记录
     * @param createWaybillDTO 运单创建对象
     * @param waybillId 运单ID
     */
    void addWaybillInfo(CreateWaybillDTO createWaybillDTO, String waybillId);

    /**
     * 修改运单承运商
     * @param carrierId 承运商ID
     * @param parentId 父运单ID
     */
    void updateWaybillCarrier(String carrierId, String parentId);
}
