package com.ecommerce.logistics.biz;

import com.ecommerce.logistics.dao.dto.waybill.WaybillMapDTO;
import com.ecommerce.logistics.dao.dto.waybill.WaybillMapQueryDTO;
import com.ecommerce.logistics.dao.dto.waybillmap.PrimaryWaybillErpInfo;
import com.ecommerce.logistics.dao.vo.WaybillMap;

import java.util.List;

/**
 * 运单映射
 */
public interface IWaybillMapBizService {

    /**
     * 添加运单映射
     * @param waybillMapDTO
     * @return
     */
    String addWaybillMap(WaybillMapDTO waybillMapDTO);

    void updateWaybillMap(WaybillMapDTO waybillMapDTO);

    WaybillMap findWaybillMapByQueryDTO(WaybillMapQueryDTO waybillMapQueryDTO);

    /**
     * 寻找关联的运单ID
     * @param waybillId
     * @param billProxyType
     * @return
     */
    String findRelationWaybillId(String waybillId, String billProxyType);

    /**
     * 根据一级运单ID寻找记录
     * @param primaryWaybillId
     * @return
     */
    WaybillMap findByPrimaryWaybillId(String primaryWaybillId);

    /**
     * 二级运单ID查询一级运单的erp信息
     * @param secondaryWaybillId
     * @return
     */
    PrimaryWaybillErpInfo findPrimaryERPBySecondaryWaybillId(String secondaryWaybillId);

    /**
     * 二级运单ID列表查询一级运单的erp信息
     * @param secondaryWaybillIdList
     * @return
     */
    List<PrimaryWaybillErpInfo> findPrimaryERPBySecondaryWaybillIdList(List<String> secondaryWaybillIdList);
}
