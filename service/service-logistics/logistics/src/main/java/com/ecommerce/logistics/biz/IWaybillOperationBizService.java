package com.ecommerce.logistics.biz;

import com.ecommerce.logistics.api.dto.waybill.ArriveWarehouseDTO;
import com.ecommerce.logistics.api.dto.waybill.BatchPassCheckDTO;
import com.ecommerce.logistics.api.dto.waybill.CancelReasonDTO;
import com.ecommerce.logistics.api.dto.waybill.CancelWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.ChangeCarriageDTO;
import com.ecommerce.logistics.api.dto.waybill.CloseWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.CompleteWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.DiscardWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.DriverCancelWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.DriverWaybillAssignDTO;
import com.ecommerce.logistics.api.dto.waybill.LeaveWarehouseDTO;
import com.ecommerce.logistics.api.dto.waybill.PassCheckDTO;
import com.ecommerce.logistics.api.dto.waybill.RepublishWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.SellerAssignWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.SnatchWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillAssignDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillMonitorStatusNotifyDTO;
import com.ecommerce.logistics.api.dto.waybill.EnterFactoryDTO;
import com.ecommerce.logistics.dao.dto.storehouse.PlatformStoreChangeBean;
import com.ecommerce.logistics.dao.dto.waybill.CompleteWaybillResultDO;
import com.ecommerce.logistics.dao.dto.waybill.ConfirmWaybillDO;
import com.ecommerce.logistics.dao.dto.waybill.WaybillTemporaryDO;
import com.ecommerce.logistics.dao.vo.Waybill;

import java.util.Date;
import java.util.List;

/**
 * @Author: <EMAIL>
 * @Description:
 * @Date: Create in 上午9:33 18/8/14
 */
public interface IWaybillOperationBizService {
    void changeCarriage(ChangeCarriageDTO changeCarriageDTO);

    void changeStatusToWaitReceiveById(String waybillId);

    void waybillAssign(WaybillAssignDTO waybillAssignDTO);

    void arriveWarehouse(ArriveWarehouseDTO dtos);

    boolean inputLeaveWarehouseQuantity(LeaveWarehouseDTO dto, List<PlatformStoreChangeBean> platformStoreChangeBeanList);

    void changeStatusToCanceledById(String waybillId);

    void changeCancelReason(CancelReasonDTO cancelReasonDTO);

    void changeArriveDestinationTime(String waybillId);

    void changeMonitorArriveTime(String waybillId, Date date);

    void changeBeginCarryTime(String waybillId);

    CompleteWaybillResultDO completeWaybill(CompleteWaybillDTO completeWaybillDTO);

    CompleteWaybillResultDO completeWaybillBySubWaybillIds(CompleteWaybillDTO completeWaybillDTO);

    String snatchWaybill(SnatchWaybillDTO snatchWaybillDTO);

    void removeWaybillAndRestoreDeliveryQuantity(DriverCancelWaybillDTO driverCancelWaybillDTO);
    
    void passCheck(PassCheckDTO dto);

    /**
     * 运单批量审核
     * @param batchPassCheckDTO
     */
    void batchPassCheck(BatchPassCheckDTO batchPassCheckDTO);
    
    /**
     * 平台取消运单
     * @Auther: <EMAIL>
     * @Date: 2018年9月6日 下午3:09:15
     * @param dto
     */
    void cancelWaybillByPlatform(CancelWaybillDTO dto);
    
    /**
     * 平台重新发布已取消运单
     * @Auther: <EMAIL>
     * @Date: 2018年9月6日 下午3:55:12
     * @param republish
     */
    void republishWaybillByPlatform(RepublishWaybillDTO republish);

    /**
     * 
     * @Auther: <EMAIL>
     * @Date: 2018年9月10日 下午3:40:05
     * @param dto
     */
    void assignWaybill(SellerAssignWaybillDTO dto);

    /**
     * 司机确认运单
     * @param confirmWaybillDO 确认对象
     */
    void confirmWaybillByDriver(ConfirmWaybillDO confirmWaybillDO);
    
    void carrierAssignWaybillToDriver(DriverWaybillAssignDTO dto);

    /**
     * 运单监控状态通知
     * @param waybillMonitorStatusNotifyDTO 运单监控状态通知对象
     */
    void waybillMonitorStatusNotify(WaybillMonitorStatusNotifyDTO waybillMonitorStatusNotifyDTO);

    /**
     * 关闭运单
     * @param closeWaybillDTO 关闭运单对象
     */
    Boolean closeWaybill(CloseWaybillDTO closeWaybillDTO);


    WaybillTemporaryDO oldWaybillInfo(String waybillId);

    /**
     * 惠州 or 珠海 试点发送到材料员[收货人]
     * @param temporaryDO
     */
    void smsToMaterial(WaybillTemporaryDO temporaryDO);

    /**
     * 填充出场商品数量
     */
    void fillLeaveWarehouseQuantity(LeaveWarehouseDTO leaveWarehouseDTO);

    /**
     * 将运单的监控完成时间设置为当前,
     * 仅用于自提运单已完成,但用监控完成时间的有无来判断是否完成监控的情况
     * @param subWaybillIdList
     */
    void updateMonitorCompleteTimeForCurrent(List<String> subWaybillIdList);

    /**
     * 更新自提运单的完成时间为当前时间
     * @param waybill
     * @return
     */
    Boolean updateMCTForBuyerPickCurr(Waybill waybill);

    void enterFactory(EnterFactoryDTO dto);

    void withdrawEnterFactory(EnterFactoryDTO dto);

    /**
     * 作废运单
     * @param discardWaybillDTO
     * @return
     */
    void discardWaybill(DiscardWaybillDTO discardWaybillDTO);
}
