package com.ecommerce.logistics.biz;

import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.pickingbill.PickingBillDTO;
import com.ecommerce.logistics.api.dto.warehouse.WarehouseOptionDTO;
import com.ecommerce.logistics.api.dto.waybill.AppWaybillListQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.AppWaybillQueryConditionsMapDTO;
import com.ecommerce.logistics.api.dto.waybill.AppWaybillStatisticsDTO;
import com.ecommerce.logistics.api.dto.waybill.CarrierWaybillListDTO;
import com.ecommerce.logistics.api.dto.waybill.CarrierWaybillListQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.CarryWaybillSubmitDTO;
import com.ecommerce.logistics.api.dto.waybill.CheckWaybillListQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.DistrictListDTO;
import com.ecommerce.logistics.api.dto.waybill.DriverWaybillListQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.EvaluateWaybillListDTO;
import com.ecommerce.logistics.api.dto.waybill.LocationDTO;
import com.ecommerce.logistics.api.dto.waybill.MergeWaybillListDTO;
import com.ecommerce.logistics.api.dto.waybill.MergeWaybillListQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.PublishWaybillListDTO;
import com.ecommerce.logistics.api.dto.waybill.PublishWaybillListQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.ReceiverInfoDTO;
import com.ecommerce.logistics.api.dto.waybill.SeckillScanQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.SnatchedWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.SnatchedWaybillQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.TradeWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.UserSeckillWaybillListDTO;
import com.ecommerce.logistics.api.dto.waybill.UserSeckillWaybillQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.WaitCheckWaybillListDTO;
import com.ecommerce.logistics.api.dto.waybill.WarehouseAdminProccessedDTO;
import com.ecommerce.logistics.api.dto.waybill.WarehouseAdminProccessedQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.WarehouseAdminWaitDeliveryDTO;
import com.ecommerce.logistics.api.dto.waybill.WarehouseAdminWaitDeliveryQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillAssignListDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillAssignListQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillCodeDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillDetailDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillDraftDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillLocationDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillLocationQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillMonitorPrepareDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillQuantityDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillStatusOption;
import com.ecommerce.logistics.biz.message.dto.WaybillCreatedSMSDTO;
import com.ecommerce.logistics.dao.dto.waybill.SubWaybillDO;
import com.ecommerce.logistics.dao.dto.waybill.TradeWaybillDetailDO;
import com.ecommerce.logistics.dao.dto.waybill.WaybillAmtDO;
import com.ecommerce.logistics.dao.dto.waybill.WaybillQuantityDO;
import com.ecommerce.logistics.dao.dto.waybill.WaybillUserInfoDTO;
import com.ecommerce.logistics.dao.vo.Waybill;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description: 运单服务
 * <AUTHOR>
 * @date 18/7/27
 */
public interface IWaybillQueryBizService {
    /**
     * 获取已发布运单列表
     * @param pageQuery 已发布运单列表查询对象
     * @return PageData<PublishWaybillListDTO>
     */
    PageData<PublishWaybillListDTO> selectPublishWaybillList(PageQuery<PublishWaybillListQueryDTO> pageQuery);
    
   /**
    * 查询卖家运单(已发布)列表
    * @Auther: <EMAIL>
    * @Date: 2018年9月10日 下午2:14:56
    * @param pageQuery
    * @return
    */
    PageData<PublishWaybillListDTO> selectBuyerSellerWaybillList(PageQuery<PublishWaybillListQueryDTO> pageQuery);

    /**
     * 查询APP运单列表
     * @Auther: ding
     * @Date: 2020年7月21日 上午11:17
     * @param pageQuery
     * @return
     */
    PageData<PublishWaybillListDTO> selectAppWaybillList(PageQuery<AppWaybillListQueryDTO> pageQuery);

    /**
     * 买家APP运单统计（计划提货量，实际出场量）
     * @Auther: ding
     * @Date: 2020年7月22日 上午10:00
     * @param queryDTO
     * @return
     */
    AppWaybillStatisticsDTO statisticsAppWaybill(AppWaybillListQueryDTO queryDTO);

    /**
     * APP运单列表筛选条件查询(买家，卖家，商品)
     * @param queryDTO
     * @return
     */
    AppWaybillQueryConditionsMapDTO selectAppWaybillQueryConditions(AppWaybillListQueryDTO queryDTO);

    /**
     * 查询待整合运单列表
     * @Auther: <EMAIL>
     * @Date: 2018年8月14日 下午3:25:44
     * @Description:
     * @param pageQuery
     * @return
     */
    PageData<MergeWaybillListDTO> selectMergeWaybillList(PageQuery<MergeWaybillListQueryDTO> pageQuery);

    /**
     *   查询待审核运单列表
     * @Auther: <EMAIL>
     * @Date: 2018年8月15日 上午10:22:30
     * @Description:
     * @param pageQuery
     * @return
     */
    PageData<WaitCheckWaybillListDTO> selectCheckWaybillList(PageQuery<CheckWaybillListQueryDTO> pageQuery);

    /**
     *  查询承运商运单指派列表
     * @Auther: <EMAIL>
     * @Date: 2018年8月16日 上午9:56:00
     * @param pageQuery
     * @return
     */
    PageData<WaybillAssignListDTO> selectWaybillAssignList(PageQuery<WaybillAssignListQueryDTO> pageQuery);

    /**
     * 通过调度单查询相应运单列表
     * @Auther: <EMAIL>
     * @Date: 2018年8月16日 下午3:21:30
     * @param dispatchIds
     * @return
     */
    List<WaybillDTO> selectWaybillsByDispatchIds(List<String> dispatchIds);
    
    /**
     * 通过提货单查询相应运单列表
     * @Auther: <EMAIL>
     * @Date: 2018年8月16日 下午3:21:30
     * @param pickingBillIds
     * @return
     */
    List<WaybillDTO> selectWaybillsByPickingBillIds(List<String> pickingBillIds);
    
    /**
     * 通过运单ID集合查询相应运单列表
     * @Auther: <EMAIL>
     * @Date: 2018年8月16日 下午3:21:30
     * @param waybillIds
     * @return
     */
    List<WaybillDTO> selectWaybillsByWaybillIds(List<String> waybillIds);
    
    /**
     * 
     * @Auther: <EMAIL>
     * @Date: 2018年8月17日 上午11:55:17
     * @param waybillId
     * @return
     */
    WaybillDetailDTO selectWaybillDetailByWaybillId(String waybillId);

    WaybillDetailDTO selectWaybillDetailByWaybillNum(String waybillNum);

    /**
     * @Auther: <EMAIL>
     * 批量提货单关联的运单数量集合
     * @param pickingBillIds
     * @return
     */
    List<WaybillQuantityDTO> selectWaybillQuantityByPickingBillIds(List<String> pickingBillIds);

    /**
     * 查询运单相关的位置信息
     * @Auther: <EMAIL>
     * @Date: 2018年9月4日 下午6:56:13
     * @param waybillId
     * @return
     */
    WaybillLocationDTO selectWaybillLocation(String waybillId);
    /**
     * 运单ID查询运单草稿信息
     * @param waybillId 运单ID
     * @return WaybillDraftDTO 运单草稿对象
     */
    WaybillDraftDTO selectWaybillDraftInfoByWaybillId(String waybillId);

    /**
     * 运单ID列表查询运单监控准备数据
     * @param waybillIdList 运单ID列表
     * @return List<WaybillMonitorPrepareDTO> 运单草稿对象
     */
    List<WaybillMonitorPrepareDTO> selectWaybillMonitorPrepareData(List<String> waybillIdList);

    /**
     * 查询子运单监控准备数据
     * @param parentId 父运单ID
     * @return List<WaybillMonitorPrepareDTO>
     */
    List<WaybillMonitorPrepareDTO> selectWaybillMonitorPrepareForMergeData(String parentId);

    /**
     * 查询父运单监控准备数据
     * @param waybillId 运单ID
     * @return WaybillMonitorPrepareDTO 父运单监控准备对象
     */
    WaybillMonitorPrepareDTO selectWaybillMonitorPrepareParentData(String waybillId);

    /**
     * 查询子运单ID列表
     * @param parentId 父运单ID
     * @return List<String>
     */
    List<Waybill> querySubWaybillByParentId(String parentId);
    
    /**
     * 
     * @param waybillIds
     * @return
     */
    List<Waybill> selectWaybillByWaybillIds(List<String> waybillIds);
    
    /**
     * 
     * @Auther: <EMAIL>
     * @Date: 2018年9月6日 上午10:47:53
     * @param dto
     * @return
     */
    List<LocationDTO> queryWaybillLocationByCon(WaybillLocationQueryDTO dto);
    
    /**
     * 查询经纬度
     * 	
     * @Auther: <EMAIL>
     * @Date: 2018年9月6日 下午12:02:26
     * @param waybillId
     * @return
     * 		BigDecimal[0] 经度
     * 		BigDecimal[1]纬度
     */
    BigDecimal[] queryLocationByWaybillId(String waybillId);
    
    /**
     * 分页查询仓库管理员待配送运单列表
     * @Auther: <EMAIL>
     * @Date: 2018年9月7日 下午7:45:06
     * @param pageQuery
     * @return
     */
    PageData<WarehouseAdminWaitDeliveryDTO> queryWarehouseAdminWaitDelivery(PageQuery<WarehouseAdminWaitDeliveryQueryDTO> pageQuery);

    /**
     * 分页查询仓库管理员已处理运单列表
     * @Auther: <EMAIL>
     * @Date: 2018年9月8日 上午10:35:46
     * @param pageQuery
     * @return
     */
    PageData<WarehouseAdminProccessedDTO> queryWarehouseAdminProccessed(PageQuery<WarehouseAdminProccessedQueryDTO> pageQuery);

    /**
     *
     * @Auther: <EMAIL>
     * @Date: 2018年9月11日 下午5:52:55
     * @param info
     * @return
     */
    List<DistrictListDTO> selectSeckillDistrictsByUserId(String userId);
    
    /**
     * 
     * @Auther: <EMAIL>
     * @Date: 2018年9月11日 下午9:54:55
     * @param pageQuery
     * @return
     */
	PageData<UserSeckillWaybillListDTO> selectUserSeckillWaybillList(
			PageQuery<UserSeckillWaybillQueryDTO> pageQuery);
	
	/**
	 * 多条件查询待接单运单列表
	 * @Auther: <EMAIL>
	 * @Date: 2018年10月6日 下午4:34:42
	 * @param pageQuery
	 * @return
	 */
	PageData<UserSeckillWaybillListDTO> selectSeckillScanWaybillList(
			PageQuery<SeckillScanQueryDTO> pageQuery);

	/**
	 * 
	 * @Auther: <EMAIL>
	 * @Date: 2018年9月11日 下午9:55:16
	 * @param pageQuery
	 * @return
	 */
	PageData<UserSeckillWaybillListDTO> selectUserSeckilledWaybillList(
			PageQuery<DriverWaybillListQueryDTO> pageQuery);

  /**
   * 发货单查询交易运单
   * @param deliverySheetNum 发货单号
   * @return List<TradeWaybillDTO>
   */
    List<TradeWaybillDTO> queryWaybillListByDeliveryNum(String deliverySheetNum);

    Waybill selectWaybillById(String waybillId);

    PageData<CarrierWaybillListDTO> queryCarrierWaybillList(PageQuery<CarrierWaybillListQueryDTO> arg0);

   /**
    * 获取合并运单总重量
    * @param waybillIdList
    * @return
    */
   WaybillQuantityDO queryWaybillTotalQuantity(List<String> waybillIdList);

   /**
    * 查询子运单收获地址列表
    * @param waybillId 运单ID
    * @return TradeWaybillDetailDTO
    */
    TradeWaybillDetailDO queryTradeWaybillDetail(String waybillId);
    
    /**
     * 通过主运单ID查询主运单信息
     * @Auther: <EMAIL>
     * @Date: 2018年10月24日 上午10:55:16
     * @param waybillId
     * @return
     */
    WaybillDetailDTO selectMainWaybillInfoByMainWaybillId(String waybillId);
    
    /**
     * 通过主运单号查询收货人商品详情
     * @Auther: <EMAIL>
     * @Date: 2018年10月24日 上午11:54:35
     * @param waybillId
     * @return
     */
    List<ReceiverInfoDTO> selectReceiverInfoByMainWaybillId(String waybillId);

   /**
    * 主运单ID批量查询运单数量
    * @param waybillIdList 主运单ID列表
    * @return List<WaybillQuantityDO>
    */
    List<WaybillQuantityDO> queryWaybillTotalQuantityByParentId(List<String> waybillIdList);

    /**
     * 运单ID查询运单数量
     * @param waybillId 运单ID
     * @return List<WaybillQuantityDO>
     */
    WaybillQuantityDO queryWaybillTotalQuantityByWaybillId(String waybillId);

   /**
    * 通过父运单id列表获取子运单对象列表
    * @param parentIdList 父运单id列表
    * @return List<SubWaybillDO>
    */
    List<SubWaybillDO> selectSubWaybillListByParentId(List<String> parentIdList);

    /**
     * 通过父运单id获取子运单ID列表
     * @param parentId 父运单id
     * @return List<String>
     */
    List<String> selectSubWaybillIdListByParentId(String parentId);

   /**
    * 查询用户抢单仓库列表
    * @param userId 用户Id
    * @return
    */
    List<WarehouseOptionDTO> selectSeckillWarehouseByUserId(String userId);

    /**
     * 查询司机当前配送状态
     * @param driverId 司机Id
     * @return
     */
    Integer queryDriverDeliveryStatus(String driverId);

 /**
  * 查询司机在途的车辆
  * @param driverId
  * @return
  */
 List<String> queryHalfWayVehicleNum(String driverId);

    /**
     * 根据运单号查询运单对象
     * @param waybillNum  运单号
     * @return Waybill
     */
    Waybill queryWaybillByWaybillNum(String waybillNum);

    /**
     * 查询被抢运单列表
     * @param snatchedWaybillQueryDTO 被抢运单查询对象
     * @return List<SnatchedWaybillDTO>
     */
    List<SnatchedWaybillDTO> querySnatchedWaybillList(SnatchedWaybillQueryDTO snatchedWaybillQueryDTO);

    /**
     * 根据外部运单号查询运单
     * @param externalWaybillNum 外部运单号
     * @return
     */
    Waybill queryWaybillByExternalWaybillNum(String externalWaybillNum);

    /**
     * 查询评价运单列表
     * @param deliverySheetNumList 发货单号
     * @return List<EvaluateWaybillListDTO>
     */
    List<EvaluateWaybillListDTO> queryEvaluateWaybillList(List<String> deliverySheetNumList);

    /**
     * 根据运单号查询提货单信息
     * @param waybillNumList 运单号列表
     * @return List<PickingBillDTO>
     */
    List<PickingBillDTO> queryPickingListByWaybillNum(List<String> waybillNumList);

    /**
     * 运单ID查询单据信息
     * @param waybillIdList
     * @return
     */
    List<WaybillCodeDTO> queryDeliverySheetNumByWaybillId(List<String> waybillIdList);

    /**
     * 运单号查询运单
     * @param waybillNumList
     * @return
     */
    List<Waybill> queryByWaybillNumList(List<String> waybillNumList);

    /**
     * 统计提货单实际发货数量
     * @param pickingBillId 提货单ID
     * @return 实际发货数量(运单签收前以出厂量累计,签收后以签收量累计)
     */
    BigDecimal countActualSendQuantity(String pickingBillId);

    /**
     * 运单是否为平台配送
     * @param waybillId
     * @return
     */
    boolean isPlatformDelivery(String waybillId);

    /**
     * 运单号查询运单状态
     * @param waybillNumList
     * @return
     */
    List<WaybillStatusOption> queryWaybillStatusByNum(List<String> waybillNumList);

    /**
     * 运单id构建发送创建信息的内容
     * @param waybillId
     * @return
     */
    WaybillCreatedSMSDTO buildCreateMessageInfo(String waybillId);

    /**
     * 查询买家运单仓库Id合集
     */
    List<String> queryWarehouseIdByBuyerId(String buyerId);

    /**
     * 通过运单号查询运单实体
     * @param waybillNum
     * @return
     */
    Waybill selectWaybillByWaybillNum(String waybillNum);

    /**
     * 通过车牌号和主运单Id查询运单的车辆Id
     */
    WaybillUserInfoDTO selectWaybillUserInfoById(String waybillId);

    /**
     * 更新运单对象
     * @param waybill 提货单对象
     */
    void updateWaybill(Waybill waybill);

    /**
     * 运单ID查询运单初始化的价格信息
     * @param waybillId
     * @return
     */
    WaybillAmtDO queryInitWaybillAmtInfo(String waybillId);

    /**
     * 根据运单号查询已完成运单详情（信息上报调用）
     */
    CarryWaybillSubmitDTO selectCarryWaybill(String waybillNum);
}
