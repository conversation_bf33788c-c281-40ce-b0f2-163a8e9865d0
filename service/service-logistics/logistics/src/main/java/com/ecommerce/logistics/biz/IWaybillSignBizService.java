package com.ecommerce.logistics.biz;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.logistics.api.dto.waybill.CompleteWaybillDTO;
import com.ecommerce.logistics.dao.dto.waybill.CompleteWaybillResultDO;
import com.ecommerce.logistics.dao.vo.ShipBill;
import com.ecommerce.logistics.dao.vo.Waybill;

/**
 * 签收相关业务处理
 */
public interface IWaybillSignBizService {

    /**
     * 处理签收运单
     * @param waybill
     * @param completeWaybillDTO
     * @return
     */
    CompleteWaybillResultDO signWaybill(Waybill waybill, CompleteWaybillDTO completeWaybillDTO);

    /**
     * 签收运单完成时通知erp
     * @param waybill
     */
    void sendSignedWaybillToErp(ShipBill waybill);

    /**
     * 重发运单完成时通知erp
     * @param waybill
     * @return
     */
    ItemResult reSendSignedWaybillToErp(ShipBill waybill);

    /**
     * 签收运单完成时通知交易签收数量
     * @param waybill
     */
    void notifyTradeSignQuantity(Waybill waybill);

}
