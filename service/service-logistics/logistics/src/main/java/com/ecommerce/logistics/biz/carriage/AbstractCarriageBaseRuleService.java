package com.ecommerce.logistics.biz.carriage;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.ecommerce.base.api.dto.cloud.AttachmentinfoDTO;
import com.ecommerce.base.api.service.IAttachmentService;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.result.PageData;
import com.ecommerce.common.service.common.BaseBiz;
import com.ecommerce.common.service.common.uuid.UUIDGenerator;
import com.ecommerce.common.utils.DateUtil;
import com.ecommerce.logistics.api.constant.NumberConstant;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.carriage.CarriageLogListDTO;
import com.ecommerce.logistics.api.dto.carriage.CarriageRuleDTO;
import com.ecommerce.logistics.api.dto.carriage.CarriageRuleQueryDTO;
import com.ecommerce.logistics.api.dto.carriage.CarriageRuleSaveDTO;
import com.ecommerce.logistics.api.dto.vehicletype.VehicleTypeDetailDTO;
import com.ecommerce.logistics.api.enums.CarriageLogCategoryTypeEnum;
import com.ecommerce.logistics.api.enums.CarriageSettlementTypeEnum;
import com.ecommerce.logistics.api.enums.UserRoleEnum;
import com.ecommerce.logistics.biz.IVehicleTypeBizService;
import com.ecommerce.logistics.dao.mapper.CarriageLogMapper;
import com.ecommerce.logistics.dao.mapper.CarriageRouteMapper;
import com.ecommerce.logistics.dao.mapper.CarriageRuleMapper;
import com.ecommerce.logistics.dao.vo.CarriageLog;
import com.ecommerce.logistics.dao.vo.CarriageRule;
import com.ecommerce.logistics.exception.LogisticsErrorCode;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Author: lehu
 * Description:
 * Date: Create in 下午2:35 19/6/4
 */
@Slf4j
public abstract class AbstractCarriageBaseRuleService implements ICarriageBaseRuleService{
    /**
     * 日期格式
     */
    private static final  String YYYYMMDD_FORMAT = "yyyy-MM-dd";
    private static final String VERIFY_PARAM_MESSAGE_CARRIAGE_ROTE_ID = "运费路线ID";

    @Autowired
    public CarriageRouteMapper carriageRouteMapper;

    @Autowired
    public CarriageRuleMapper carriageRuleMapper;

    @Autowired
    public CarriageLogMapper carriageLogMapper;

    @Autowired
    public UUIDGenerator uuidGenerator;

    @Autowired
    public IVehicleTypeBizService vehicleTypeBizService;
    @Autowired
    private IAttachmentService attachmentService;

    /**
     * 规则数据验证
     * @param carriageRuleSaveDTO 规则保存对象
     */
    public void ruleDataValid(CarriageRuleSaveDTO<?> carriageRuleSaveDTO) {
        if (CsStringUtils.isEmpty(carriageRuleSaveDTO.getCarriageRouteId())) {
            throw new BizException(BasicCode.INVALID_PARAM, VERIFY_PARAM_MESSAGE_CARRIAGE_ROTE_ID);
        }
        if (CsStringUtils.isEmpty(carriageRuleSaveDTO.getPricingType())) {
            throw new BizException(BasicCode.INVALID_PARAM, "定价类型");
        }
        if (CsStringUtils.isEmpty(carriageRuleSaveDTO.getSettlementType())) {
            throw new BizException(BasicCode.INVALID_PARAM, "结算类型");
        }
        if (CsStringUtils.isEmpty(carriageRuleSaveDTO.getOperatorUserId())) {
            throw new BizException(BasicCode.INVALID_PARAM, "操作用户Id");
        }
        if (CsStringUtils.isEmpty(carriageRuleSaveDTO.getOperatorUserName())) {
            throw new BizException(BasicCode.INVALID_PARAM, "操作用户名称");
        }
        if (CollectionUtils.isEmpty(carriageRuleSaveDTO.getCarriageRuleList())) {
            throw new BizException(BasicCode.INVALID_PARAM, "运费规则列表");
        }
    }

    /**
     * 转换运费规则key
     * @param carriageRuleDTO 运费规则对象
     * @return String
     */
    public String convertCarriageRuleKey(CarriageRuleDTO<?> carriageRuleDTO) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(carriageRuleDTO.getSettlementUserType());
        stringBuilder.append(carriageRuleDTO.getSettlementUserId());
        stringBuilder.append(carriageRuleDTO.getVehicleTypeId());
        return stringBuilder.toString();
    }

    /**
     * 规则有效期重复验证
     * @param carriageRuleSaveDTO 运费规则保存对象
     * @param carriageRuleMap 运费规则map
     */
    public void repeatEffectiveTimeValid(CarriageRuleSaveDTO<?> carriageRuleSaveDTO,
                                         Map<String, List<CarriageRuleDTO>> carriageRuleMap) {
        log.info("运费规则映射carriageRuleMap:" + JSON.toJSONString(carriageRuleMap));
        if (carriageRuleMap.size() < 1) {
            return;
        }
        for (List<CarriageRuleDTO> carriageRuleList : carriageRuleMap.values()) {
            Collections.sort(carriageRuleList, Comparator.comparing(CarriageRuleDTO::getBeginEffectiveTime));
            CarriageRuleDTO preCarriageRuleDTO = null;
            for (CarriageRuleDTO carriageRuleDTO :carriageRuleList) {
                if (preCarriageRuleDTO == null) {
                    preCarriageRuleDTO = carriageRuleDTO;
                    continue;
                }
                //有效区间重复
                if (preCarriageRuleDTO.getEndEffectiveTime().compareTo(carriageRuleDTO.getBeginEffectiveTime()) > -1) {
                    throw CarriageSettlementTypeEnum.PAY.getCode().equals(carriageRuleSaveDTO.getSettlementType()) ?
                            new BizException(LogisticsErrorCode.PAY_EFFECTIVE_TIME_REPEAT,
                                    carriageRuleDTO.getSettlementUserName(), carriageRuleDTO.getVehicleTypeName()) :
                            new BizException(LogisticsErrorCode.COLLECT_EFFECTIVE_TIME_REPEAT);
                }
                //替换比较标量
                preCarriageRuleDTO = carriageRuleDTO;
            }
        }
    }

    /**
     * 转换运费规则
     * @param carriageRuleDTO 运费规则对象
     * @param carriageRuleSaveDTO 运费规则保存对象
     * @return CarriageRule
     */
    public CarriageRule convertCarriageRule(CarriageRuleDTO<?> carriageRuleDTO,
                                            CarriageRuleSaveDTO<?> carriageRuleSaveDTO,
                                            Map<String, List<CarriageRuleDTO>> carriageRuleMap) {
        if (CarriageSettlementTypeEnum.COLLECT.getCode().equals(carriageRuleSaveDTO.getSettlementType())) {
            carriageRuleDTO.setSettlementUserId("");
            carriageRuleDTO.setSettlementUserName("");
            carriageRuleDTO.setSettlementUserType(UserRoleEnum.BUYER.getCode());
            carriageRuleDTO.setVehicleTypeId("");
            carriageRuleDTO.setVehicleTypeName("");
        } else if (CarriageSettlementTypeEnum.PAY.getCode().equals(carriageRuleSaveDTO.getSettlementType())) {
            carriageRuleDTO.setSettlementUserType(UserRoleEnum.CARRIER.getCode());
            if (CsStringUtils.isEmpty(carriageRuleDTO.getSettlementUserId())) {
                throw new BizException(BasicCode.INVALID_PARAM, "结算对象ID");
            }
            if (CsStringUtils.isEmpty(carriageRuleDTO.getSettlementUserName())) {
                throw new BizException(BasicCode.INVALID_PARAM, "结算对象名称");
            }
            if (CsStringUtils.isEmpty(carriageRuleDTO.getVehicleTypeId())) {
                throw new BizException(BasicCode.INVALID_PARAM, "车型ID");
            }
            VehicleTypeDetailDTO vehicleTypeDetailDTO = vehicleTypeBizService.queryVehicleType(carriageRuleDTO.getVehicleTypeId());
            if (vehicleTypeDetailDTO == null) {
                throw new BizException(BasicCode.DATA_NOT_EXIST, "车型");
            }
            carriageRuleDTO.setVehicleTypeName(vehicleTypeDetailDTO.getTypeName());
        }
        if (CsStringUtils.isEmpty(carriageRuleDTO.getBeginEffectiveTime())) {
            throw new BizException(BasicCode.INVALID_PARAM, "开始生效时间");
        }
        if (CsStringUtils.isEmpty(carriageRuleDTO.getEndEffectiveTime())) {
            throw new BizException(BasicCode.INVALID_PARAM, "结束生效时间");
        }
        CarriageRule carriageRule = new CarriageRule();
        BeanUtils.copyProperties(carriageRuleDTO, carriageRule);
        carriageRule.setCarriageRuleId(uuidGenerator.gain());
        carriageRule.setCarriageRouteId(carriageRuleSaveDTO.getCarriageRouteId());
        carriageRule.setSettlementType(carriageRuleSaveDTO.getSettlementType());
        carriageRule.setBeginEffectiveTime(DateUtil.parse(carriageRuleDTO.getBeginEffectiveTime(), YYYYMMDD_FORMAT));
        Date endDate = DateUtil.parse(carriageRuleDTO.getEndEffectiveTime(), YYYYMMDD_FORMAT);
        carriageRule.setEndEffectiveTime(new Date(endDate.getTime() + 86399000));
        BaseBiz.setOperInfo(carriageRule, carriageRuleSaveDTO.getOperatorUserId(), Boolean.TRUE);
        //添加规则有效期校验Map
        String key = convertCarriageRuleKey(carriageRuleDTO);
        if (carriageRuleMap.get(key) == null) {
            carriageRuleMap.put(key, Lists.newArrayList(carriageRuleDTO));
        } else {
            carriageRuleMap.get(key).add(carriageRuleDTO);
        }

        return carriageRule;
    }

    @Override
    public <K> List<K> queryCarriageRuleList(CarriageRuleQueryDTO carriageRuleQueryDTO) {
        if (CsStringUtils.isEmpty(carriageRuleQueryDTO.getCarriageRouteId())) {
            throw new BizException(BasicCode.INVALID_PARAM, VERIFY_PARAM_MESSAGE_CARRIAGE_ROTE_ID);
        }
        if (CsStringUtils.isEmpty(carriageRuleQueryDTO.getSettlementType())) {
            throw new BizException(BasicCode.INVALID_PARAM, "结算类型");
        }
        Condition condition = new Condition(CarriageRule.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("carriageRouteId", carriageRuleQueryDTO.getCarriageRouteId());
        criteria.andEqualTo("settlementType", carriageRuleQueryDTO.getSettlementType());
        List<CarriageRule> ruleList = carriageRuleMapper.selectByCondition(condition);
        if (CollectionUtils.isEmpty(ruleList)) {
            return new ArrayList<>();
        }
        Map<String, CarriageRuleDTO<Object>> carriageRuleMap = new HashMap<>();
        for (CarriageRule carriageRule : ruleList) {
            CarriageRuleDTO<Object> carriageRuleDTO = new CarriageRuleDTO<>();
            BeanUtils.copyProperties(carriageRule, carriageRuleDTO);
            carriageRuleDTO.setBeginEffectiveTime(DateUtil.format(carriageRule.getBeginEffectiveTime(), YYYYMMDD_FORMAT));
            carriageRuleDTO.setEndEffectiveTime(DateUtil.format(carriageRule.getEndEffectiveTime(), YYYYMMDD_FORMAT));
            carriageRuleMap.put(carriageRule.getCarriageRuleId(), carriageRuleDTO);
        }
        queryRuleItemList(carriageRuleMap);
        //按开始生效时间对运费规则进行排序
        List<CarriageRuleDTO<?>> carriageRuleList = new ArrayList<>(carriageRuleMap.values());
        Collections.sort(carriageRuleList, (o1, o2)
                -> o1.getBeginEffectiveTime().compareTo(o2.getBeginEffectiveTime()));

        return JSON.parseObject(JSON.toJSONString(carriageRuleList), new TypeReference<List<K>>() {});
    }

    /**
     * 记录运费操作日志
     */
    public <T>void recordCarriageOperateLog(CarriageRuleSaveDTO<T> carriageRuleSaveDTO) {
        //查询编辑前规则
        CarriageRuleQueryDTO carriageRuleQueryDTO = new CarriageRuleQueryDTO();
        carriageRuleQueryDTO.setCarriageRouteId(carriageRuleSaveDTO.getCarriageRouteId());
        carriageRuleQueryDTO.setPricingType(carriageRuleSaveDTO.getPricingType());
        carriageRuleQueryDTO.setSettlementType(carriageRuleSaveDTO.getSettlementType());
        List<CarriageRuleDTO> oldCarriageRuleList = BeanUtil.copyToList(queryCarriageRuleList(carriageRuleQueryDTO), CarriageRuleDTO.class);

        List<String> oldRuleIds = Lists.newArrayList();
        List<String> newRuleIds = Lists.newArrayList();
        Map<String,CarriageRuleDTO<?>> ruleId2DTO = new HashMap<>();
        if(CollectionUtils.isNotEmpty(oldCarriageRuleList)){
            for (CarriageRuleDTO<?> carriageRuleDTO : oldCarriageRuleList){
                oldRuleIds.add(carriageRuleDTO.getCarriageRuleId());
                ruleId2DTO.put(carriageRuleDTO.getCarriageRuleId(),carriageRuleDTO);
            }
        }

        //判断前后运费规则是否变化并生成日志
        for (CarriageRuleDTO<?> carriageRuleDTO : carriageRuleSaveDTO.getCarriageRuleList()){
            newRuleIds.add(carriageRuleDTO.getCarriageRuleId());
            CarriageLog carriageLog = new CarriageLog();
            carriageLog.setCarriageLogId(uuidGenerator.gain());
            carriageLog.setCategoryType(CarriageLogCategoryTypeEnum.UPDATE.getCode());
            carriageLog.setCarriageRouteId(carriageRuleSaveDTO.getCarriageRouteId());
            carriageLog.setSettlementType(carriageRuleSaveDTO.getSettlementType());
            carriageLog.setNextRule(JSON.toJSONString(carriageRuleDTO));
            carriageLog.setOperatorId(carriageRuleSaveDTO.getOperatorUserId());
            carriageLog.setOperatorName(carriageRuleSaveDTO.getOperatorUserName());

            //如果有附件，存入附件url
            if (CsStringUtils.isNotEmpty(carriageRuleDTO.getBid())) {
                List<AttachmentinfoDTO> attachmentinfoDTOS = attachmentService.getAttachmentByBID(carriageRuleDTO.getBid());
                List<String> urlList = attachmentinfoDTOS.stream().map(item -> item.getAttcPath().substring(0,item.getAttcPath().indexOf("?"))).toList();
                carriageLog.setAttachmentUrl(JSON.toJSONString(urlList));
            }

            if (CsStringUtils.isEmpty(carriageRuleDTO.getCarriageRuleId())) {//新增规则
                carriageLog.setCategoryType(CarriageLogCategoryTypeEnum.ADD.getCode());
            }else if( oldRuleIds.contains(carriageRuleDTO.getCarriageRuleId()) ){  //修改规则
                carriageLog.setPreRule(JSON.toJSONString( ruleId2DTO.get(carriageRuleDTO.getCarriageRuleId()) ));
            }
            BaseBiz.setOperInfo(carriageLog, carriageRuleSaveDTO.getOperatorUserId(), Boolean.TRUE);
            carriageLogMapper.insert(carriageLog);
        }
        //添加删除运费规则日志
        oldRuleIds.forEach(item -> {
            if( !newRuleIds.contains(item) ){
                CarriageLog carriageLog = new CarriageLog();
                carriageLog.setCarriageLogId(uuidGenerator.gain());
                carriageLog.setCarriageRouteId(carriageRuleSaveDTO.getCarriageRouteId());
                carriageLog.setSettlementType(carriageRuleSaveDTO.getSettlementType());
                carriageLog.setOperatorId(carriageRuleSaveDTO.getOperatorUserId());
                carriageLog.setOperatorName(carriageRuleSaveDTO.getOperatorUserName());
                carriageLog.setNextRule(JSON.toJSONString( ruleId2DTO.get(item) ));
                carriageLog.setCategoryType(CarriageLogCategoryTypeEnum.DELETE.getCode());
                BaseBiz.setOperInfo(carriageLog, carriageRuleSaveDTO.getOperatorUserId(), Boolean.TRUE);
                carriageLogMapper.insert(carriageLog);
            }
        });



    }

    /**
     * 分页查询运费操作日志列表
     * @param pageQuery 查询对象
     * @return PageData<CarriageRouteListDTO>
     */
    @Override
    public PageData<CarriageLogListDTO> queryCarriageLogList(PageQuery<CarriageRuleQueryDTO> pageQuery) {
        CarriageRuleQueryDTO queryDTO = pageQuery.getQueryDTO();
        if (CsStringUtils.isEmpty(queryDTO.getCarriageRouteId())) {
            throw new BizException(BasicCode.INVALID_PARAM, VERIFY_PARAM_MESSAGE_CARRIAGE_ROTE_ID);
        }
        if (CsStringUtils.isEmpty(queryDTO.getSettlementType())) {
            throw new BizException(BasicCode.INVALID_PARAM, "结算类型");
        }
        PageInfo<CarriageLogListDTO> pageInfo = PageMethod
                .startPage(ObjectUtil.defaultIfNull(pageQuery.getPageNum(), NumberConstant.DEFAULT_PAGE_NUM),
                        ObjectUtil.defaultIfNull(pageQuery.getPageSize(),NumberConstant.FIVE_INTEGER))
                .doSelectPageInfo(
                () -> carriageLogMapper.selectCarriageLogList(queryDTO));

        return new PageData<>(pageInfo);
    }

    /**
     * 查询过去一年运费日志
     * @param carriageRuleQueryDTO 查询对象
     * @return List<CarriageLogListDTO>
     */
    @Override
    public List<CarriageLogListDTO> queryCarriageLogListOneYear(CarriageRuleQueryDTO carriageRuleQueryDTO){
        //得到一年前时间
        Calendar ca = Calendar.getInstance();
        ca.setTime(new Date());
        ca.add(Calendar.YEAR, -1);
        Date startDate = ca.getTime();

        Condition condition = new Condition(CarriageLog.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("carriageRouteId", carriageRuleQueryDTO.getCarriageRouteId());
        criteria.andEqualTo("settlementType", carriageRuleQueryDTO.getSettlementType());
        criteria.andGreaterThanOrEqualTo("updateTime", startDate);
        criteria.andEqualTo("delFlg",0);
        condition.orderBy("createTime").desc();
        List<CarriageLog> carriageLogs = carriageLogMapper.selectByCondition(condition);
        List<CarriageLogListDTO> carriageLogListDTOS = new ArrayList<>();
        if(CollectionUtils.isEmpty(carriageLogs)){
            return carriageLogListDTOS;
        }

        carriageLogs.forEach(carriageLog -> {
            CarriageLogListDTO carriageLogListDTO = new CarriageLogListDTO();
            BeanUtils.copyProperties(carriageLog,carriageLogListDTO);
            carriageLogListDTOS.add(carriageLogListDTO);
        });

        return carriageLogListDTOS;
    }

    /**
     * 获取运费规则明细
     */
    public abstract void queryRuleItemList(Map<String, CarriageRuleDTO<Object>> carriageRuleMap);
}
