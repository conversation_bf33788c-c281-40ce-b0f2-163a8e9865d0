package com.ecommerce.logistics.biz.carriage;

import com.alibaba.fastjson.JSON;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.carriage.CarriageLogListDTO;
import com.ecommerce.logistics.api.dto.carriage.CarriageRuleDTO;
import com.ecommerce.logistics.api.dto.carriage.CarriageRuleDeleteDTO;
import com.ecommerce.logistics.api.dto.carriage.CarriageRuleQueryDTO;
import com.ecommerce.logistics.api.dto.carriage.CarriageRuleSaveDTO;
import com.ecommerce.logistics.dao.dto.carriage.CarriageRuleQueryDO;
import com.ecommerce.logistics.exception.LogisticsErrorCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Author: lehu
 * Description:
 * Date: Create in 下午3:52 19/5/29
 */
@Slf4j
@Component
public class CarriageFactoryContext implements ApplicationContextAware {

    public static final String PLATFORM_ID = "PLATFORM_ID";

    public static final String SOCIETY_CARRIER_ID = "SOCIETY_CARRIER_ID";

    protected static final Map<String, ICarriageBaseRuleService> CARRIAGE_MODE_HASH_MAP = new HashMap<>();

    public static ICarriageBaseRuleService instance(String pricingType) {
        if (CARRIAGE_MODE_HASH_MAP.get(pricingType) == null) {
            throw new BizException(LogisticsErrorCode.DATA_EXCEPTION, "无效的定价类型");
        }
        return CARRIAGE_MODE_HASH_MAP.get(pricingType);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        String[] beanNamesForType = applicationContext.getBeanNamesForType(ICarriageBaseRuleService.class);
        for (String beanName : beanNamesForType) {
            ICarriageBaseRuleService bean = (ICarriageBaseRuleService) applicationContext.getBean(beanName);
            CARRIAGE_MODE_HASH_MAP.put(bean.carriagePricingType(), bean);
        }
    }

    /**
     * 录入运费规则
     * @param  carriageRuleSaveDTO 运费规则保存对象
     * @return String
     */
    public <T> List<String> enteringCarriageRule(CarriageRuleSaveDTO<T> carriageRuleSaveDTO) {
        log.info("enteringCarriageRule:" + JSON.toJSONString(carriageRuleSaveDTO));
        ICarriageBaseRuleService carriageRuleService = CarriageFactoryContext.instance(carriageRuleSaveDTO.getPricingType());
        return carriageRuleService.enteringCarriageRule(carriageRuleSaveDTO);
    }

    /**
     * 删除运费规则
     * @param  carriageRuleDeleteDTO 运费规则删除对象
     */
    public void deleteCarriageRule(CarriageRuleDeleteDTO carriageRuleDeleteDTO) {
        log.info("deleteCarriageRule:" + JSON.toJSONString(carriageRuleDeleteDTO));
        ICarriageBaseRuleService carriageRuleService = CarriageFactoryContext.instance(carriageRuleDeleteDTO.getPricingType());
        carriageRuleService.deleteCarriageRule(carriageRuleDeleteDTO);
    }

    /**
     * 编辑运费规则
     * @param  carriageRuleSaveDTO 运费规则保存对象
     */
    public <T> void editCarriageRule(CarriageRuleSaveDTO<T> carriageRuleSaveDTO) {
        log.info("editCarriageRule:" + JSON.toJSONString(carriageRuleSaveDTO));
        ICarriageBaseRuleService carriageRuleService = CarriageFactoryContext.instance(carriageRuleSaveDTO.getPricingType());
        carriageRuleService.editCarriageRule(carriageRuleSaveDTO);
    }

    /**
     * 查询运费规则列表
     * @param  carriageRuleQueryDTO 运费规则查询对象
     * @return List<K>
     */
    public <K> List<K> queryCarriageRuleList(CarriageRuleQueryDTO carriageRuleQueryDTO) {
        log.info("queryCarriageRuleList:" + JSON.toJSONString(carriageRuleQueryDTO));
        ICarriageBaseRuleService carriageRuleService = CarriageFactoryContext.instance(carriageRuleQueryDTO.getPricingType());
        return carriageRuleService.queryCarriageRuleList(carriageRuleQueryDTO);
    }

    /**
     * 条件查询运费规则
     * @param carriageRuleQueryDO 运费规则查询对象
     * @return List<K>
     */
    public CarriageRuleDTO queryCarriageRuleByCondition(CarriageRuleQueryDO carriageRuleQueryDO) {
        //根据计算规则依次查找点对点规则
        log.info("queryCarriageRuleList:" + JSON.toJSONString(carriageRuleQueryDO));
        ICarriageBaseRuleService carriageRuleService = CarriageFactoryContext.instance(carriageRuleQueryDO.getPricingType());
        return carriageRuleService.queryCarriageRuleByCondition(carriageRuleQueryDO);
    }
    /**
     *  查询运费日志列表
     * @param pageQuery 查询对象
     * @return PageData<CarriageRouteListDTO>
     */
    public PageData<CarriageLogListDTO> queryCarriageLogList(PageQuery<CarriageRuleQueryDTO> pageQuery) {
        log.info("queryCarriageLogList:" + JSON.toJSONString(pageQuery));
        ICarriageBaseRuleService carriageRuleService = CarriageFactoryContext.instance(pageQuery.getQueryDTO().getPricingType());
        return carriageRuleService.queryCarriageLogList(pageQuery);
    }

    /**
     *  查询过去一年运费日志
     * @param carriageRuleQueryDTO 查询对象
     * @return List<CarriageLogListDTO>
     */
    public List<CarriageLogListDTO> queryCarriageLogListOneYear(CarriageRuleQueryDTO carriageRuleQueryDTO) {
        ICarriageBaseRuleService carriageRuleService = CarriageFactoryContext.instance(carriageRuleQueryDTO.getPricingType());
        return carriageRuleService.queryCarriageLogListOneYear(carriageRuleQueryDTO);
    }


    /**
     * 条件查询不限车辆，不限承运商运费规则
     * @param carriageRuleQueryDO 运费规则查询对象
     * @return List<K>
     */
    public CarriageRuleDTO queryUnlimiteCarriageRuleByCondition(CarriageRuleQueryDO carriageRuleQueryDO) {
        //根据计算规则依次查找1.点对点规则，2.区域统一定价，3.区域公式定价
        log.info("queryUnlimiteCarriageRuleByCondition:" + JSON.toJSONString(carriageRuleQueryDO));
        ICarriageBaseRuleService carriageRuleService = CarriageFactoryContext.instance(carriageRuleQueryDO.getPricingType());
        return carriageRuleService.queryUnlimiteCarriageRuleByCondition(carriageRuleQueryDO);
    }

}
