package com.ecommerce.logistics.biz.carriage;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.service.common.BaseBiz;
import com.ecommerce.logistics.LogisticsCommonBean;
import com.ecommerce.logistics.api.dto.carriage.CarriageRuleDTO;
import com.ecommerce.logistics.api.dto.carriage.CarriageRuleDeleteDTO;
import com.ecommerce.logistics.api.dto.carriage.CarriageRuleSaveDTO;
import com.ecommerce.logistics.api.dto.carriage.IntegrateCarriageRuleItemDTO;
import com.ecommerce.logistics.api.dto.vehicletype.VehicleTypeNameDTO;
import com.ecommerce.logistics.api.enums.CarriagePricingTypeEnum;
import com.ecommerce.logistics.api.enums.CarriageSettlementTypeEnum;
import com.ecommerce.logistics.api.enums.VehicleTypeAxlesEnum;
import com.ecommerce.logistics.dao.dto.carriage.CarriageRouteListDO;
import com.ecommerce.logistics.dao.dto.carriage.CarriageRouteQueryDO;
import com.ecommerce.logistics.dao.dto.carriage.CarriageRuleQueryDO;
import com.ecommerce.logistics.dao.mapper.CarriageIntegrateItemMapper;
import com.ecommerce.logistics.dao.vo.CarriageIntegrateItem;
import com.ecommerce.logistics.dao.vo.CarriageRule;
import com.ecommerce.logistics.util.LogisticsUtils;
import com.github.pagehelper.util.StringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Author: lehu
 * Description:
 * Date: Create in 下午9:17 19/5/29
 */
@Slf4j
@Service
public class CarriageIntegrateRuleService extends AbstractCarriageBaseRuleService{

    public static final String SETTLEMENT_TYPE = "settlementType";
    public static final String SETTLEMENT_USER_ID = "settlementUserId";
    public static final String VEHICLE_TYPE_ID = "vehicleTypeId";
    public static final String CARRIAGE_ROUTE_ID = "carriageRouteId";
    public static final String CARRIAGE_RULE_ID = "carriageRuleId";
    public static final String DEL_FLG = "delFlg";
    public static final String BEGIN_EFFECTIVE_TIME = "beginEffectiveTime";
    public static final String END_EFFECTIVE_TIME = "endEffectiveTime";
    @Autowired
    private CarriageIntegrateItemMapper carriageIntegrateItemMapper;

    @Override
    public String carriagePricingType() {
        return CarriagePricingTypeEnum.ZONE_INTEGRATE.getCode();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public <T> List<String> enteringCarriageRule(CarriageRuleSaveDTO<T> carriageRuleSaveDTO) {
        //数据验证
        ruleDataValid(carriageRuleSaveDTO);
        //添加操作记录
        if (carriageRuleSaveDTO.getEditFlag() == null) {
            recordCarriageOperateLog(carriageRuleSaveDTO);
        }
        //添加规则
        List<CarriageRule> carriageRuleList = Lists.newArrayList();
        List<String> carriageRuleIdList = Lists.newArrayList();
        List<CarriageIntegrateItem> integrateItemList = Lists.newArrayList();
        Map<String, List<CarriageRuleDTO>> carriageRuleMap = new HashMap<>();
        for (CarriageRuleDTO carriageRuleDTO : carriageRuleSaveDTO.getCarriageRuleList()) {
            CarriageRule carriageRule = convertCarriageRule(carriageRuleDTO, carriageRuleSaveDTO, carriageRuleMap);
            carriageRule.setUnitPrice(BigDecimal.ZERO);
            carriageRuleDTO.setCarriageRuleId(carriageRule.getCarriageRuleId());
            carriageRuleList.add(carriageRule);
            carriageRuleIdList.add(carriageRule.getCarriageRuleId());
            if (CollectionUtils.isEmpty(carriageRuleDTO.getRuleItemList())) continue;
            convertItemList(integrateItemList, carriageRuleDTO, carriageRuleSaveDTO.getOperatorUserId());
        }
        //有效期重复验证
        repeatEffectiveTimeValid(carriageRuleSaveDTO, carriageRuleMap);

        if (CarriageSettlementTypeEnum.PAY.getCode().equals(carriageRuleSaveDTO.getSettlementType()) &&
                CollectionUtils.isNotEmpty(carriageRuleList)) {
            verifyRule(carriageRuleSaveDTO, carriageRuleList);
        }

        if (CollectionUtils.isNotEmpty(carriageRuleList)) {
            carriageRuleMapper.insertList(carriageRuleList);
            carriageIntegrateItemMapper.insertList(integrateItemList);
        }

        return carriageRuleIdList;
    }

    private <T> void verifyRule(CarriageRuleSaveDTO<T> carriageRuleSaveDTO, List<CarriageRule> carriageRuleList) {
        for (CarriageRule carriageRule : carriageRuleList) {
            if (CsStringUtils.equals(LogisticsCommonBean.ANY_CARRIER, carriageRule.getSettlementUserId())) {
                VehicleTypeNameDTO vehicleTypeNameDTO = vehicleTypeBizService.querytUnlimiteType();
                if (vehicleTypeNameDTO == null || CsStringUtils.isEmpty(vehicleTypeNameDTO.getVehicleTypeId())) {
                    throw new BizException(BasicCode.CUSTOM_ERROR, "找不到不限车型，无法添加不限承运商运费规则");
                }
                HashMap<String, Object> map = new HashMap<>();
                map.put(SETTLEMENT_TYPE, carriageRuleSaveDTO.getSettlementType());
                map.put(SETTLEMENT_USER_ID, LogisticsCommonBean.ANY_CARRIER);
                map.put(VEHICLE_TYPE_ID, vehicleTypeNameDTO.getVehicleTypeId());
                map.put("userId", carriageRuleSaveDTO.getMemberId());
                map.put("pricingType", carriageRuleSaveDTO.getPricingType());
                map.put("transportCategoryId", carriageRuleSaveDTO.getTransportCategoryId());
                map.put("warehouseId", carriageRuleSaveDTO.getWarehouseId());
                map.put("receiveAddressId", carriageRuleSaveDTO.getReceiveAddressId());
                //查询不限承运商的运费规则
                List<CarriageRule> dataCarriageRuleList = carriageRuleMapper.queryUnlimiteCarrier(map);
                if (CollectionUtils.isNotEmpty(dataCarriageRuleList)) {
                    throw new BizException(BasicCode.CUSTOM_ERROR, "已存在不限承运商的运费规则");
                }
            }
        }
    }

    /**
     * 转换明细列表
     * @param integrateItemList 明细列表
     * @param carriageRuleDTO 运费规则对象
     * @param operatorUserId 操作人ID
     */
    private void convertItemList(List<CarriageIntegrateItem> integrateItemList,
                                 CarriageRuleDTO carriageRuleDTO,
                                 String operatorUserId) {
        //添加规则明细
        List<IntegrateCarriageRuleItemDTO> itemList = JSON.parseObject(JSON.toJSONString(carriageRuleDTO.getRuleItemList()),
                new TypeReference<List<IntegrateCarriageRuleItemDTO>>() {});
        for (IntegrateCarriageRuleItemDTO item : itemList) {
            verifyItem(item);
            CarriageIntegrateItem carriageIntegrateItem = new CarriageIntegrateItem();
            BeanUtils.copyProperties(item, carriageIntegrateItem);
            carriageIntegrateItem.setRoundingFlag(item.getRoundingFlag().byteValue());
            carriageIntegrateItem.setCarriageRuleId(carriageRuleDTO.getCarriageRuleId());
            carriageIntegrateItem.setIntegrateItemId(uuidGenerator.gain());
            BaseBiz.setOperInfo(carriageIntegrateItem, operatorUserId, Boolean.TRUE);
            integrateItemList.add(carriageIntegrateItem);
        }
    }

    private static void verifyItem(IntegrateCarriageRuleItemDTO item) {
        if (item.getMinSection() == null) {
            throw new BizException(BasicCode.INVALID_PARAM, "最小重量范围");
        }
        if (item.getMaxSection() == null) {
            throw new BizException(BasicCode.INVALID_PARAM, "最大重量范围");
        }
        if (item.getRoundingFlag() == null) {
            throw new BizException(BasicCode.INVALID_PARAM, "凑整标识");
        }
        if (item.getRoundingFlag() == 1) {
            if (item.getRoundingCarriage() == null) {
                throw new BizException(BasicCode.INVALID_PARAM, "凑整运费");
            }
            item.setUnitPrice(item.getUnitPrice() == null ? BigDecimal.ZERO : item.getUnitPrice());
        } else {
            if (item.getUnitPrice() == null) {
                throw new BizException(BasicCode.INVALID_PARAM, "单位运价");
            }
            item.setRoundingCarriage(item.getRoundingCarriage() == null ? BigDecimal.ZERO : item.getRoundingCarriage());
        }
    }

    @Override
    public void deleteCarriageRule(CarriageRuleDeleteDTO carriageRuleDeleteDTO) {
        //sonar 修复添加备注
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public <T> void editCarriageRule(CarriageRuleSaveDTO<T> carriageRuleSaveDTO) {
        if (CsStringUtils.isEmpty(carriageRuleSaveDTO.getCarriageRouteId())) {
            throw new BizException(BasicCode.INVALID_PARAM, "运费路线ID");
        }
        if (CsStringUtils.isEmpty(carriageRuleSaveDTO.getSettlementType())) {
            throw new BizException(BasicCode.INVALID_PARAM, "结算类型");
        }
        //添加操作记录
        recordCarriageOperateLog(carriageRuleSaveDTO);
        //删除当前路线规则
        Condition condition = new Condition(CarriageRule.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(CARRIAGE_ROUTE_ID, carriageRuleSaveDTO.getCarriageRouteId());
        criteria.andEqualTo(SETTLEMENT_TYPE, carriageRuleSaveDTO.getSettlementType());
        List<CarriageRule> ruleList = carriageRuleMapper.selectByCondition(condition);
        List<String> carriageRuleIdList = ruleList.stream().map(CarriageRule::getCarriageRuleId).toList();
        carriageRuleMapper.deleteByCondition(condition);
        if (CollectionUtils.isNotEmpty(carriageRuleIdList)) {
            //删除当前路线规则明细
            Condition itemCondition = new Condition(CarriageRule.class);
            Example.Criteria itemCriteria = itemCondition.createCriteria();
            itemCriteria.andIn(CARRIAGE_RULE_ID, carriageRuleIdList);
            carriageIntegrateItemMapper.deleteByCondition(itemCondition);
        }
        //添加规则
        carriageRuleSaveDTO.setEditFlag(1);
        enteringCarriageRule(carriageRuleSaveDTO);
    }

    @Override
    public CarriageRuleDTO queryCarriageRuleByCondition(CarriageRuleQueryDO carriageRuleQueryDO) {
        verifyParam(carriageRuleQueryDO);
        if (carriageRuleQueryDO.getDistrictCode() == null) {
            carriageRuleQueryDO.setDistrictCode("");
        }
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(carriageRuleQueryDO.getProvinceCode())
                .append(carriageRuleQueryDO.getCityCode())
                .append(carriageRuleQueryDO.getDistrictCode());
        stringBuilder.append(LogisticsUtils.defaultIfCondition(StringUtil.isNotEmpty(carriageRuleQueryDO.getStreetCode()),carriageRuleQueryDO.getStreetCode(),""));
        CarriageRouteQueryDO carriageRouteQueryDO = new CarriageRouteQueryDO();
        BeanUtils.copyProperties(carriageRuleQueryDO, carriageRouteQueryDO);
        carriageRouteQueryDO.setZoneCode(stringBuilder.toString());
        List<CarriageRouteListDO> carriageRouteList = carriageRouteMapper.queryCarriageRouteByCondition(carriageRouteQueryDO);
        if (CollectionUtils.isEmpty(carriageRouteList)) {
            return null;
        }
        //获取规则
        List<CarriageRule> carriageRuleList = null;
        for (CarriageRouteListDO carriageRouteListDO : carriageRouteList) {
            Condition ruleCondition = new Condition(CarriageRule.class);
            Example.Criteria ruleCriteria = ruleCondition.createCriteria();
            ruleCriteria.andEqualTo(DEL_FLG, Boolean.FALSE);
            ruleCriteria.andEqualTo(CARRIAGE_ROUTE_ID, carriageRouteListDO.getCarriageRouteId());
            ruleCriteria.andEqualTo(SETTLEMENT_TYPE, carriageRuleQueryDO.getSettlementType());
            ruleCriteria.andLessThanOrEqualTo(BEGIN_EFFECTIVE_TIME, carriageRuleQueryDO.getEffectiveDate());
            ruleCriteria.andGreaterThanOrEqualTo(END_EFFECTIVE_TIME, carriageRuleQueryDO.getEffectiveDate());
            if (CarriageSettlementTypeEnum.PAY.getCode().equals(carriageRuleQueryDO.getSettlementType())) {
                verifyQueryParam(carriageRuleQueryDO);

                ruleCriteria.andEqualTo(SETTLEMENT_USER_ID, carriageRuleQueryDO.getSettlementUserId());
                ruleCriteria.andEqualTo(VEHICLE_TYPE_ID, carriageRuleQueryDO.getVehicleTypeId());
            }
            carriageRuleList = carriageRuleMapper.selectByCondition(ruleCondition);
            if (CollectionUtils.isNotEmpty(carriageRuleList)) {
                //第一次找到时间符合的
                break;
            }
        }

        if (CollectionUtils.isEmpty(carriageRuleList)) {
            return null;
        }
        CarriageRuleDTO<IntegrateCarriageRuleItemDTO> carriageRuleDTO = new CarriageRuleDTO<>();
        BeanUtils.copyProperties(carriageRuleList.get(0), carriageRuleDTO);
        //获取规则明细
        Condition itemCondition = new Condition(CarriageIntegrateItem.class);
        Example.Criteria itemCriteria = itemCondition.createCriteria();
        itemCriteria.andEqualTo(CARRIAGE_RULE_ID, carriageRuleDTO.getCarriageRuleId());
        List<CarriageIntegrateItem> itemList = carriageIntegrateItemMapper.selectByCondition(itemCondition);
        if (CollectionUtils.isEmpty(itemList)) {
            return null;
        }
        List<IntegrateCarriageRuleItemDTO> ruleItemList = new ArrayList<>();
        for (CarriageIntegrateItem carriageIntegrateItem : itemList) {
            IntegrateCarriageRuleItemDTO item = new IntegrateCarriageRuleItemDTO();
            BeanUtils.copyProperties(carriageIntegrateItem, item);
            item.setRoundingFlag(carriageIntegrateItem.getRoundingFlag().intValue());
            ruleItemList.add(item);
        }
        //按最大max_section排序
        ruleItemList.sort(Comparator.comparing(IntegrateCarriageRuleItemDTO::getMaxSection));

        carriageRuleDTO.setRuleItemList(ruleItemList);

        return carriageRuleDTO;
    }

    private static void verifyQueryParam(CarriageRuleQueryDO carriageRuleQueryDO) {
        if (CsStringUtils.isEmpty(carriageRuleQueryDO.getSettlementUserId())) {
            throw new BizException(BasicCode.INVALID_PARAM, "结算对象ID");
        }
        if (CsStringUtils.isEmpty(carriageRuleQueryDO.getVehicleTypeId())) {
            throw new BizException(BasicCode.INVALID_PARAM, "车型ID");
        }
    }

    private static void verifyParam(CarriageRuleQueryDO carriageRuleQueryDO) {
        if (StringUtil.isEmpty(carriageRuleQueryDO.getWarehouseId())) {
            throw new BizException(BasicCode.INVALID_PARAM, "仓库ID");
        }
        if (StringUtil.isEmpty(carriageRuleQueryDO.getPricingType())) {
            throw new BizException(BasicCode.INVALID_PARAM, "定价类型");
        }
        if (StringUtil.isEmpty(carriageRuleQueryDO.getTransportCategoryId())) {
            throw new BizException(BasicCode.INVALID_PARAM, "运输品类");
        }
        if (StringUtil.isEmpty(carriageRuleQueryDO.getProvinceCode())) {
            throw new BizException(BasicCode.INVALID_PARAM, "省份编码");
        }
        if (StringUtil.isEmpty(carriageRuleQueryDO.getCityCode())) {
            throw new BizException(BasicCode.INVALID_PARAM, "城市编码");
        }
    }

    @Override
    public void queryRuleItemList(Map<String, CarriageRuleDTO<Object>> carriageRuleMap) {
        List<CarriageRuleDTO<Object>> carriageRuleList = Lists.newArrayList(carriageRuleMap.values());
        //获取规则明细
        if (CollectionUtils.isEmpty(carriageRuleMap.keySet())) {
            return;
        }
        Condition itemCondition = new Condition(CarriageRule.class);
        Example.Criteria itemCriteria = itemCondition.createCriteria();
        itemCriteria.andIn(CARRIAGE_RULE_ID, carriageRuleMap.keySet());
        List<CarriageIntegrateItem> itemList = carriageIntegrateItemMapper.selectByCondition(itemCondition);
        Map<String, TreeMap<BigDecimal, Object>> itemMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(itemList)) {
            for (CarriageIntegrateItem item : itemList) {
                IntegrateCarriageRuleItemDTO integrateCarriageRuleItemDTO = new IntegrateCarriageRuleItemDTO();
                BeanUtils.copyProperties(item, integrateCarriageRuleItemDTO);
                integrateCarriageRuleItemDTO.setRoundingFlag(item.getRoundingFlag().intValue());
                if (itemMap.get(item.getCarriageRuleId()) != null) {
                    itemMap.get(item.getCarriageRuleId()).put(integrateCarriageRuleItemDTO.getMaxSection(), integrateCarriageRuleItemDTO);
                } else {
                    TreeMap<BigDecimal, Object> treeMap = new TreeMap<>();
                    treeMap.put(integrateCarriageRuleItemDTO.getMaxSection(), integrateCarriageRuleItemDTO);
                    itemMap.put(item.getCarriageRuleId(), treeMap);
                }
            }
        }
        for (CarriageRuleDTO<Object> carriageRuleDTO : carriageRuleList) {
            if (itemMap.get(carriageRuleDTO.getCarriageRuleId()) != null) {
                carriageRuleDTO.setRuleItemList(Lists.newArrayList(itemMap.get(carriageRuleDTO.getCarriageRuleId()).values()));
            } else {
                carriageRuleDTO.setRuleItemList(new ArrayList<>());
            }
        }
    }


    @Override
    public CarriageRuleDTO queryUnlimiteCarriageRuleByCondition(CarriageRuleQueryDO carriageRuleQueryDO) {
        verifyParam(carriageRuleQueryDO);
        carriageRuleQueryDO.setDistrictCode(LogisticsUtils.defaultIfNull(carriageRuleQueryDO.getDistrictCode(),""));

        verifyQueryParam(carriageRuleQueryDO);

        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(carriageRuleQueryDO.getProvinceCode())
                .append(carriageRuleQueryDO.getCityCode())
                .append(carriageRuleQueryDO.getDistrictCode());
        stringBuilder.append(LogisticsUtils.defaultIfCondition(StringUtil.isNotEmpty(carriageRuleQueryDO.getStreetCode()),carriageRuleQueryDO.getStreetCode(),""));
        CarriageRouteQueryDO carriageRouteQueryDO = new CarriageRouteQueryDO();
        BeanUtils.copyProperties(carriageRuleQueryDO, carriageRouteQueryDO);
        carriageRouteQueryDO.setZoneCode(stringBuilder.toString());
        List<CarriageRouteListDO> carriageRouteList = carriageRouteMapper.queryCarriageRouteByCondition(carriageRouteQueryDO);
        if (CollectionUtils.isEmpty(carriageRouteList)) {
            return null;
        }
        //获取规则
        List<CarriageRule> carriageRuleList = null;
        for (CarriageRouteListDO carriageRouteListDO : carriageRouteList) {
            Condition ruleCondition = new Condition(CarriageRule.class);
            Example.Criteria ruleCriteria = ruleCondition.createCriteria();
            ruleCriteria.andEqualTo(DEL_FLG, Boolean.FALSE);
            ruleCriteria.andEqualTo(CARRIAGE_ROUTE_ID, carriageRouteListDO.getCarriageRouteId());
            ruleCriteria.andEqualTo(SETTLEMENT_TYPE, carriageRuleQueryDO.getSettlementType());
            ruleCriteria.andLessThanOrEqualTo(BEGIN_EFFECTIVE_TIME, carriageRuleQueryDO.getEffectiveDate());
            ruleCriteria.andGreaterThanOrEqualTo(END_EFFECTIVE_TIME, carriageRuleQueryDO.getEffectiveDate());
            ruleCriteria.andEqualTo(SETTLEMENT_USER_ID, carriageRuleQueryDO.getSettlementUserId());
            ruleCriteria.andEqualTo(VEHICLE_TYPE_ID, carriageRuleQueryDO.getVehicleTypeId());

            carriageRuleList = carriageRuleMapper.selectByCondition(ruleCondition);
            log.info("carriageRuleList:" + JSON.toJSONString(carriageRuleList));

            //第一次找到时间符合的
            if (CollectionUtils.isNotEmpty(carriageRuleList))
                break;

            VehicleTypeNameDTO vehicleTypeNameDTO = null;
            //处理逻辑：1.先看是否满足承运商ID+车型ID,有则返回。 2.再看是否满足承运商ID+不限车型ID,有则返回。 3.再看是否满足不限承运商+不限车型ID,有则返回。
            //如果当前车型是非不限车型,//编码对应-VehicleTypeCarriageTypeEnum.UNLIMITED.getCode()，但是数据库中存的是6
            if (!CsStringUtils.equals(carriageRuleQueryDO.getAxles(), VehicleTypeAxlesEnum.AXLES_ZERO.getCode())
                    && !CsStringUtils.equals(carriageRuleQueryDO.getCarriageType(), "6")) {
                //查询出不限车型的车型ID
                vehicleTypeNameDTO = vehicleTypeBizService.querytUnlimiteType();
                verifyNotNull(vehicleTypeNameDTO);

                Condition ruleCondition2 = new Condition(CarriageRule.class);
                Example.Criteria ruleCriteria2 = ruleCondition2.createCriteria();
                ruleCriteria2.andEqualTo(DEL_FLG, Boolean.FALSE);
                ruleCriteria2.andEqualTo(CARRIAGE_ROUTE_ID, carriageRouteListDO.getCarriageRouteId());
                ruleCriteria2.andEqualTo(SETTLEMENT_TYPE, carriageRuleQueryDO.getSettlementType());
                ruleCriteria2.andLessThanOrEqualTo(BEGIN_EFFECTIVE_TIME, carriageRuleQueryDO.getEffectiveDate());
                ruleCriteria2.andGreaterThanOrEqualTo(END_EFFECTIVE_TIME, carriageRuleQueryDO.getEffectiveDate());
                ruleCriteria2.andEqualTo(SETTLEMENT_USER_ID, carriageRuleQueryDO.getSettlementUserId());
                //设置为不限车型
                ruleCriteria2.andEqualTo(VEHICLE_TYPE_ID, vehicleTypeNameDTO.getVehicleTypeId());
                carriageRuleList = carriageRuleMapper.selectByCondition(ruleCondition2);
            }
            if (CollectionUtils.isNotEmpty(carriageRuleList))
                break;

            Condition ruleCondition3 = new Condition(CarriageRule.class);
            Example.Criteria ruleCriteria3 = ruleCondition3.createCriteria();
            ruleCriteria3.andEqualTo(DEL_FLG, Boolean.FALSE);
            ruleCriteria3.andEqualTo(CARRIAGE_ROUTE_ID, carriageRouteListDO.getCarriageRouteId());
            ruleCriteria3.andEqualTo(SETTLEMENT_TYPE, carriageRuleQueryDO.getSettlementType());
            ruleCriteria3.andLessThanOrEqualTo(BEGIN_EFFECTIVE_TIME, carriageRuleQueryDO.getEffectiveDate());
            ruleCriteria3.andGreaterThanOrEqualTo(END_EFFECTIVE_TIME, carriageRuleQueryDO.getEffectiveDate());
            //设置为不限车型
            String vehicleTypeId = getId(carriageRuleQueryDO, vehicleTypeNameDTO);
            ruleCriteria3.andEqualTo(VEHICLE_TYPE_ID, vehicleTypeId);
            //设置为不限承运商
            ruleCriteria3.andEqualTo(SETTLEMENT_USER_ID, LogisticsCommonBean.ANY_CARRIER);
            carriageRuleList = carriageRuleMapper.selectByCondition(ruleCondition3);

            if (CollectionUtils.isNotEmpty(carriageRuleList)) {
                break;
            }
        }

        if (CollectionUtils.isEmpty(carriageRuleList)) {
            return null;
        }
        CarriageRuleDTO<IntegrateCarriageRuleItemDTO> carriageRuleDTO = new CarriageRuleDTO<>();
        BeanUtils.copyProperties(carriageRuleList.get(0), carriageRuleDTO);
        //获取规则明细
        Condition itemCondition = new Condition(CarriageIntegrateItem.class);
        Example.Criteria itemCriteria = itemCondition.createCriteria();
        itemCriteria.andEqualTo(CARRIAGE_RULE_ID, carriageRuleDTO.getCarriageRuleId());
        List<CarriageIntegrateItem> itemList = carriageIntegrateItemMapper.selectByCondition(itemCondition);
        if (CollectionUtils.isEmpty(itemList)) {
            return null;
        }
        List<IntegrateCarriageRuleItemDTO> ruleItemList = new ArrayList<>();
        for (CarriageIntegrateItem carriageIntegrateItem : itemList) {
            IntegrateCarriageRuleItemDTO item = new IntegrateCarriageRuleItemDTO();
            BeanUtils.copyProperties(carriageIntegrateItem, item);
            item.setRoundingFlag(carriageIntegrateItem.getRoundingFlag().intValue());
            ruleItemList.add(item);
        }
        //按最大max_section排序
        ruleItemList.sort(Comparator.comparing(IntegrateCarriageRuleItemDTO::getMaxSection));

        carriageRuleDTO.setRuleItemList(ruleItemList);

        return carriageRuleDTO;
    }

    private static String getId(CarriageRuleQueryDO carriageRuleQueryDO, VehicleTypeNameDTO vehicleTypeNameDTO) {
        String vehicleTypeId = vehicleTypeNameDTO == null ? carriageRuleQueryDO.getVehicleTypeId() : vehicleTypeNameDTO.getVehicleTypeId();
        return vehicleTypeId;
    }

    private static void verifyNotNull(VehicleTypeNameDTO vehicleTypeNameDTO) {
        if (vehicleTypeNameDTO == null) {
            throw new BizException(BasicCode.DB_DATA_NOT_FOUND, "不限车型");
        }
    }

}
