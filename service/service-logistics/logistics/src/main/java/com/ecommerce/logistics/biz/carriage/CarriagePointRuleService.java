package com.ecommerce.logistics.biz.carriage;

import com.alibaba.fastjson.JSON;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.logistics.LogisticsCommonBean;
import com.ecommerce.logistics.api.dto.carriage.CarriageRuleDTO;
import com.ecommerce.logistics.api.dto.carriage.CarriageRuleDeleteDTO;
import com.ecommerce.logistics.api.dto.carriage.CarriageRuleSaveDTO;
import com.ecommerce.logistics.api.dto.vehicletype.VehicleTypeNameDTO;
import com.ecommerce.logistics.api.enums.CarriagePricingTypeEnum;
import com.ecommerce.logistics.api.enums.CarriageSettlementTypeEnum;
import com.ecommerce.logistics.api.enums.VehicleTypeAxlesEnum;
import com.ecommerce.logistics.dao.dto.carriage.CarriageRuleQueryDO;
import com.ecommerce.logistics.dao.vo.CarriageRoute;
import com.ecommerce.logistics.dao.vo.CarriageRule;
import com.github.pagehelper.util.StringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Author: lehu
 * Description:
 * Date: Create in 下午5:46 19/5/29
 */
@Slf4j
@Service
public class CarriagePointRuleService extends AbstractCarriageBaseRuleService {

    private static final String SETTLEMENT_TYPE = "settlementType";
    private static final String SETTLEMENT_USER_ID = "settlementUserId";
    private static final String VEHICLE_TYPE_ID = "vehicleTypeId";
    private static final String USER_ID = "userId";
    private static final String TRANSPORT_CATEGORY_ID = "transportCategoryId";
    private static final String WAREHOUSE_ID = "warehouseId";
    private static final String RECEIVE_ADDRESS_ID = "receiveAddressId";
    private static final String CARRIAGE_ROUTE_ID = "carriageRouteId";
    private static final String DEL_FLG = "delFlg";
    private static final String BEGIN_EFFECTIVE_TIME = "beginEffectiveTime";
    private static final String END_EFFECTIVE_TIME = "endEffectiveTime";

    @Override
    public String carriagePricingType() {
        return CarriagePricingTypeEnum.POINT_TO_POINT.getCode();
    }

    @Override
    public <T> List<String> enteringCarriageRule(CarriageRuleSaveDTO<T> carriageRuleSaveDTO) {
        //数据验证
        ruleDataValid(carriageRuleSaveDTO);
        //添加操作记录
        if (carriageRuleSaveDTO.getEditFlag() == null) {
            recordCarriageOperateLog(carriageRuleSaveDTO);
        }
        //添加规则
        List<CarriageRule> carriageRuleList = Lists.newArrayList();
        List<String> carriageRuleIdList = Lists.newArrayList();
        Map<String, List<CarriageRuleDTO>> carriageRuleMap = new HashMap<>();

        handleCarriageRule(carriageRuleSaveDTO, carriageRuleMap, carriageRuleList, carriageRuleIdList);
        //有效期重复验证
        repeatEffectiveTimeValid(carriageRuleSaveDTO, carriageRuleMap);

        if (CarriageSettlementTypeEnum.PAY.getCode().equals(carriageRuleSaveDTO.getSettlementType()) &&
                CollectionUtils.isNotEmpty(carriageRuleList)) {
            for (CarriageRule carriageRule : carriageRuleList) {
                if (CsStringUtils.equals(LogisticsCommonBean.ANY_CARRIER, carriageRule.getSettlementUserId())) {
                    VehicleTypeNameDTO vehicleTypeNameDTO = vehicleTypeBizService.querytUnlimiteType();

                    verifyVehicleTypeId(vehicleTypeNameDTO);
                    HashMap<String, Object> map = new HashMap<>();
                    map.put(SETTLEMENT_TYPE, carriageRuleSaveDTO.getSettlementType());
                    map.put(SETTLEMENT_USER_ID, LogisticsCommonBean.ANY_CARRIER);
                    map.put(VEHICLE_TYPE_ID, vehicleTypeNameDTO.getVehicleTypeId());
                    map.put(USER_ID, carriageRuleSaveDTO.getMemberId());
                    map.put("pricingType", carriageRuleSaveDTO.getPricingType());
                    map.put(TRANSPORT_CATEGORY_ID, carriageRuleSaveDTO.getTransportCategoryId());
                    map.put(WAREHOUSE_ID, carriageRuleSaveDTO.getWarehouseId());
                    map.put(RECEIVE_ADDRESS_ID, carriageRuleSaveDTO.getReceiveAddressId());
                    //查询不限承运商的运费规则
                    List<CarriageRule> dataCarriageRuleList = carriageRuleMapper.queryUnlimiteCarrier(map);
                    if (CollectionUtils.isNotEmpty(dataCarriageRuleList)) {
                        throw new BizException(BasicCode.CUSTOM_ERROR, "已存在不限承运商的运费规则");
                    }
                }
            }
        }

        if (CollectionUtils.isNotEmpty(carriageRuleList)) {
            carriageRuleMapper.insertList(carriageRuleList);
        }

        return carriageRuleIdList;
    }

    private static void verifyVehicleTypeId(VehicleTypeNameDTO vehicleTypeNameDTO) {
        if (vehicleTypeNameDTO == null || CsStringUtils.isEmpty(vehicleTypeNameDTO.getVehicleTypeId())) {
            throw new BizException(BasicCode.CUSTOM_ERROR, "找不到不限车型，无法添加不限承运商运费规则");
        }
    }

    private <T> void handleCarriageRule(CarriageRuleSaveDTO<T> carriageRuleSaveDTO,
                                        Map<String, List<CarriageRuleDTO>> carriageRuleMap,
                                        List<CarriageRule> carriageRuleList, List<String> carriageRuleIdList) {
        for (CarriageRuleDTO carriageRuleDTO : carriageRuleSaveDTO.getCarriageRuleList()) {
            if (carriageRuleDTO.getUnitPrice() == null) {
                throw new BizException(BasicCode.INVALID_PARAM, "单位运价");
            }
            CarriageRule carriageRule = convertCarriageRule(carriageRuleDTO, carriageRuleSaveDTO, carriageRuleMap);
            carriageRuleList.add(carriageRule);
            carriageRuleIdList.add(carriageRule.getCarriageRuleId());
        }
    }

    @Override
    public void deleteCarriageRule(CarriageRuleDeleteDTO carriageRuleDeleteDTO) {
        //sonar 添加备注
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public <T> void editCarriageRule(CarriageRuleSaveDTO<T> carriageRuleSaveDTO) {
        //添加操作记录
        recordCarriageOperateLog(carriageRuleSaveDTO);
        //数据验证
        ruleDataValid(carriageRuleSaveDTO);
        //删除当前路线规则
        Condition condition = new Condition(CarriageRule.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(CARRIAGE_ROUTE_ID, carriageRuleSaveDTO.getCarriageRouteId());
        criteria.andEqualTo(SETTLEMENT_TYPE, carriageRuleSaveDTO.getSettlementType());
        carriageRuleMapper.deleteByCondition(condition);
        //添加规则
        carriageRuleSaveDTO.setEditFlag(1);
        enteringCarriageRule(carriageRuleSaveDTO);
    }

    @Override
    public CarriageRuleDTO queryCarriageRuleByCondition(CarriageRuleQueryDO carriageRuleQueryDO) {
        verifyParam(carriageRuleQueryDO);

        Condition condition = new Condition(CarriageRoute.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(DEL_FLG, Boolean.FALSE);
        criteria.andEqualTo(USER_ID, carriageRuleQueryDO.getUserId());
        criteria.andEqualTo(WAREHOUSE_ID, carriageRuleQueryDO.getWarehouseId());
        criteria.andEqualTo(RECEIVE_ADDRESS_ID, carriageRuleQueryDO.getReceiveAddressId());
        criteria.andEqualTo("receiveAddressLocation", carriageRuleQueryDO.getReceiveAddressLocation());
        criteria.andEqualTo(TRANSPORT_CATEGORY_ID, carriageRuleQueryDO.getTransportCategoryId());
        List<CarriageRoute> carriageRouteList = carriageRouteMapper.selectByCondition(condition);
        if (CollectionUtils.isEmpty(carriageRouteList)) {
            return null;
        }
        //获取运费规则
        List<CarriageRule> carriageRuleList = null;
        for (CarriageRoute carriageRoute : carriageRouteList) {
            Condition ruleCondition = new Condition(CarriageRule.class);
            Example.Criteria ruleCriteria = ruleCondition.createCriteria();
            ruleCriteria.andEqualTo(DEL_FLG, Boolean.FALSE);
            ruleCriteria.andEqualTo(CARRIAGE_ROUTE_ID, carriageRoute.getCarriageRouteId());
            ruleCriteria.andEqualTo(SETTLEMENT_TYPE, carriageRuleQueryDO.getSettlementType());
            ruleCriteria.andLessThanOrEqualTo(BEGIN_EFFECTIVE_TIME, carriageRuleQueryDO.getEffectiveDate());
            ruleCriteria.andGreaterThanOrEqualTo(END_EFFECTIVE_TIME, carriageRuleQueryDO.getEffectiveDate());
            if (CarriageSettlementTypeEnum.PAY.getCode().equals(carriageRuleQueryDO.getSettlementType())) {
                ruleCriteria.andEqualTo(SETTLEMENT_USER_ID, carriageRuleQueryDO.getSettlementUserId());
                ruleCriteria.andEqualTo(VEHICLE_TYPE_ID, carriageRuleQueryDO.getVehicleTypeId());
            }
             carriageRuleList = carriageRuleMapper.selectByCondition(ruleCondition);
            if (CollectionUtils.isNotEmpty(carriageRuleList)) {
                break;
            }
        }
        if (CollectionUtils.isEmpty(carriageRuleList)) {
            return null;
        }
        CarriageRuleDTO carriageRuleDTO = new CarriageRuleDTO();
        BeanUtils.copyProperties(carriageRuleList.get(0), carriageRuleDTO);

        return carriageRuleDTO;
    }

    @Override
    public void queryRuleItemList(Map<String, CarriageRuleDTO<Object>> carriageRuleMap) {
        //sonar 添加备注
    }


    @Override
    public CarriageRuleDTO queryUnlimiteCarriageRuleByCondition(CarriageRuleQueryDO carriageRuleQueryDO) {
        verifyParam(carriageRuleQueryDO);

        Condition condition = new Condition(CarriageRoute.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(DEL_FLG, Boolean.FALSE);
        criteria.andEqualTo(USER_ID, carriageRuleQueryDO.getUserId());
        criteria.andEqualTo("pricingType", carriageRuleQueryDO.getPricingType());
        criteria.andEqualTo(WAREHOUSE_ID, carriageRuleQueryDO.getWarehouseId());
        criteria.andEqualTo(RECEIVE_ADDRESS_ID, carriageRuleQueryDO.getReceiveAddressId());
        criteria.andEqualTo("receiveAddressLocation", carriageRuleQueryDO.getReceiveAddressLocation());
        criteria.andEqualTo(TRANSPORT_CATEGORY_ID, carriageRuleQueryDO.getTransportCategoryId());
        List<CarriageRoute> carriageRouteList = carriageRouteMapper.selectByCondition(condition);
        if (CollectionUtils.isEmpty(carriageRouteList)) {
            return null;
        }
        //获取运费规则，只适用于付费规则
        List<CarriageRule> carriageRuleList = null;
        for (CarriageRoute carriageRoute : carriageRouteList) {
            Condition ruleCondition = new Condition(CarriageRule.class);
            Example.Criteria ruleCriteria = ruleCondition.createCriteria();
            ruleCriteria.andEqualTo(DEL_FLG, Boolean.FALSE);
            ruleCriteria.andEqualTo(CARRIAGE_ROUTE_ID, carriageRoute.getCarriageRouteId());
            ruleCriteria.andEqualTo(SETTLEMENT_TYPE, carriageRuleQueryDO.getSettlementType());
            ruleCriteria.andLessThanOrEqualTo(BEGIN_EFFECTIVE_TIME, carriageRuleQueryDO.getEffectiveDate());
            ruleCriteria.andGreaterThanOrEqualTo(END_EFFECTIVE_TIME, carriageRuleQueryDO.getEffectiveDate());
            ruleCriteria.andEqualTo(SETTLEMENT_USER_ID, carriageRuleQueryDO.getSettlementUserId());
            ruleCriteria.andEqualTo(VEHICLE_TYPE_ID, carriageRuleQueryDO.getVehicleTypeId());
            carriageRuleList = carriageRuleMapper.selectByCondition(ruleCondition);
            log.info("carriageRuleList:" + JSON.toJSONString(carriageRuleList));
            if (CollectionUtils.isNotEmpty(carriageRuleList))
                break;

            VehicleTypeNameDTO vehicleTypeNameDTO = null;
            //处理逻辑：1.先看是否满足承运商ID+车型ID,有则返回。 2.再看是否满足承运商ID+不限车型ID,有则返回。 3.再看是否满足不限承运商+不限车型ID,有则返回。
            //如果当前车型是非不限车型,//编码对应-VehicleTypeCarriageTypeEnum.UNLIMITED.getCode()，但是数据库中存的是6
            if (!CsStringUtils.equals(carriageRuleQueryDO.getAxles(), VehicleTypeAxlesEnum.AXLES_ZERO.getCode())
                    && !CsStringUtils.equals(carriageRuleQueryDO.getCarriageType(), "6")) {
                //查询出不限车型的车型ID
                vehicleTypeNameDTO = vehicleTypeBizService.querytUnlimiteType();
                verifyVehicleTypeNotNull(vehicleTypeNameDTO);

                Condition ruleCondition2 = new Condition(CarriageRule.class);
                Example.Criteria ruleCriteria2 = ruleCondition2.createCriteria();
                ruleCriteria2.andEqualTo(DEL_FLG, Boolean.FALSE);
                ruleCriteria2.andEqualTo(CARRIAGE_ROUTE_ID, carriageRoute.getCarriageRouteId());
                ruleCriteria2.andEqualTo(SETTLEMENT_TYPE, carriageRuleQueryDO.getSettlementType());
                ruleCriteria2.andLessThanOrEqualTo(BEGIN_EFFECTIVE_TIME, carriageRuleQueryDO.getEffectiveDate());
                ruleCriteria2.andGreaterThanOrEqualTo(END_EFFECTIVE_TIME, carriageRuleQueryDO.getEffectiveDate());
                ruleCriteria2.andEqualTo(SETTLEMENT_USER_ID, carriageRuleQueryDO.getSettlementUserId());
                //设置为不限车型
                ruleCriteria2.andEqualTo(VEHICLE_TYPE_ID, vehicleTypeNameDTO.getVehicleTypeId());
                carriageRuleList = carriageRuleMapper.selectByCondition(ruleCondition2);
            }
            if (CollectionUtils.isNotEmpty(carriageRuleList))
                break;

            Condition ruleCondition3 = new Condition(CarriageRule.class);
            Example.Criteria ruleCriteria3 = ruleCondition3.createCriteria();
            ruleCriteria3.andEqualTo(DEL_FLG, Boolean.FALSE);
            ruleCriteria3.andEqualTo(CARRIAGE_ROUTE_ID, carriageRoute.getCarriageRouteId());
            ruleCriteria3.andEqualTo(SETTLEMENT_TYPE, carriageRuleQueryDO.getSettlementType());
            ruleCriteria3.andLessThanOrEqualTo(BEGIN_EFFECTIVE_TIME, carriageRuleQueryDO.getEffectiveDate());
            ruleCriteria3.andGreaterThanOrEqualTo(END_EFFECTIVE_TIME, carriageRuleQueryDO.getEffectiveDate());
            //设置为不限车型
            String vehicleTypeId = vehicleTypeNameDTO == null ? carriageRuleQueryDO.getVehicleTypeId() : vehicleTypeNameDTO.getVehicleTypeId();
            ruleCriteria3.andEqualTo(VEHICLE_TYPE_ID, vehicleTypeId);
            //设置为不限承运商
            ruleCriteria.andEqualTo(SETTLEMENT_USER_ID, LogisticsCommonBean.ANY_CARRIER);
            carriageRuleList = carriageRuleMapper.selectByCondition(ruleCondition3);

            if (CollectionUtils.isNotEmpty(carriageRuleList)) {
                break;
            }
        }
        if (CollectionUtils.isEmpty(carriageRuleList)) {
            return null;
        }
        CarriageRuleDTO carriageRuleDTO = new CarriageRuleDTO();
        BeanUtils.copyProperties(carriageRuleList.get(0), carriageRuleDTO);
        return carriageRuleDTO;
    }

    private static void verifyVehicleTypeNotNull(VehicleTypeNameDTO vehicleTypeNameDTO) {
        if (vehicleTypeNameDTO == null) {
            throw new BizException(BasicCode.DB_DATA_NOT_FOUND, "不限车型");
        }
    }

    private static void verifyParam(CarriageRuleQueryDO carriageRuleQueryDO) {
        if (StringUtil.isEmpty(carriageRuleQueryDO.getWarehouseId())) {
            throw new BizException(BasicCode.INVALID_PARAM, "仓库ID");
        }
        if (StringUtil.isEmpty(carriageRuleQueryDO.getReceiveAddressId())) {
            throw new BizException(BasicCode.INVALID_PARAM, "收货地址ID");
        }
        if (StringUtil.isEmpty(carriageRuleQueryDO.getReceiveAddressLocation())) {
            throw new BizException(BasicCode.INVALID_PARAM, "收货地址位置");
        }
    }


}
