package com.ecommerce.logistics.biz.carriage;

import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.carriage.*;
import com.ecommerce.logistics.dao.dto.carriage.CarriageRuleQueryDO;

import java.util.List;

/**
 * Author: lehu
 * Description:
 * Date: Create in 下午4:24 19/5/29
 */
public interface ICarriageBaseRuleService {

    /**
     * 运费定价适配器
     */
    String carriagePricingType();

    /**
     * 录入运费规则
     * @param  carriageRuleSaveDTO 运费规则保存对象
     * @return String
     */
    <T> List<String> enteringCarriageRule(CarriageRuleSaveDTO<T> carriageRuleSaveDTO);

    /**
     * 删除运费规则
     * @param  carriageRuleDeleteDTO 运费规则删除对象
     */
    void deleteCarriageRule(CarriageRuleDeleteDTO carriageRuleDeleteDTO);

    /**
     * 编辑运费规则
     * @param  carriageRuleSaveDTO 运费规则保存对象
     */
    <T> void editCarriageRule(CarriageRuleSaveDTO<T> carriageRuleSaveDTO);

    /**
     * 查询运费规则列表
     * @param  carriageRuleQueryDTO 运费规则查询对象
     * @return  List<K>
     */
    <K> List<K> queryCarriageRuleList(CarriageRuleQueryDTO carriageRuleQueryDTO);

    /**
     * 条件查询运费规则
     * @param carriageRuleQueryDO 运费规则查询对象
     * @return CarriageRuleDTO<K>
     */
    CarriageRuleDTO queryCarriageRuleByCondition(CarriageRuleQueryDO carriageRuleQueryDO);
    /**
     * 查询运费日志列表
     * @param pageQuery 查询对象
     * @return PageData<CarriageRouteListDTO>
     */
    PageData<CarriageLogListDTO> queryCarriageLogList(PageQuery<CarriageRuleQueryDTO> pageQuery);

    /**
     * 查询过去一年运费日志
     * @param carriageRuleQueryDTO 查询对象
     * @return List<CarriageLogListDTO>
     */
    List<CarriageLogListDTO> queryCarriageLogListOneYear(CarriageRuleQueryDTO carriageRuleQueryDTO);

    /**
     * 条件查询不限车辆，不限承运商运费规则
     * @param carriageRuleQueryDO 运费规则查询对象
     * @return CarriageRuleDTO<K>
     */
    CarriageRuleDTO queryUnlimiteCarriageRuleByCondition(CarriageRuleQueryDO carriageRuleQueryDO);

}
