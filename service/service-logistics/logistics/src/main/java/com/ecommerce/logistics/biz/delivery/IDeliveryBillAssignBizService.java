package com.ecommerce.logistics.biz.delivery;

import com.ecommerce.logistics.api.dto.DeliveryBillRootCreateDTO;
import com.ecommerce.logistics.api.dto.assign.AssignShipDTO;
import com.ecommerce.logistics.api.dto.assign.AssignVehicleDTO;
import com.ecommerce.logistics.api.dto.pickingbill.AssignDetailDTO;
import com.ecommerce.logistics.api.dto.shipping.ShippingInfoDTO;
import com.ecommerce.logistics.api.dto.waybill.CreateShipBillDTO;
import com.ecommerce.logistics.dao.dto.delivery.DeliveryRerouteResult;
import com.ecommerce.logistics.dao.vo.DeliveryBill;
import com.ecommerce.logistics.dao.vo.ShipBill;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Auther: colu
 * @Date: 2020-12-11 10:53
 * @Description: 委托单指派业务接口
 */
public interface IDeliveryBillAssignBizService {

    /**
     * 创建第一级的委托单
     * @param deliveryBillRootCreateDTO
     * @return
     */
    String createRootDeliveryBill(DeliveryBillRootCreateDTO deliveryBillRootCreateDTO);

    /**
     * 背靠背创建第一级委托单
     * @param canOperateRootCreateDTO
     * @param unCanOperateRootCreateDTO
     * @return
     */
    String createProxyGroupRootDeliveryBill(DeliveryBillRootCreateDTO canOperateRootCreateDTO, DeliveryBillRootCreateDTO unCanOperateRootCreateDTO);

    /**
     * 在委托单上指派承运商(兼容背靠背逻辑)
     * @param deliveryBillId
     * @param assignCarrierDTOList
     * @param operationUserId
     * @param operationUserName
     */
    List<DeliveryRerouteResult> assignCarrier(String deliveryBillId, List<AssignDetailDTO> assignCarrierDTOList, String operationUserId, String operationUserName);

    /**
     * 在(非虚拟)委托单上指派船,背靠背需要把数量修改到背靠背的根级委托单
     * @param deliveryBillId
     * @param assignShipDTOList
     * @param operationUserId
     * @param operationUserName
     */
    List<CreateShipBillDTO>  assignShip(String deliveryBillId, List<AssignShipDTO> assignShipDTOList, String operationUserId, String operationUserName);

    /**
     * 在(非虚拟)委托单上指派车辆,背靠背需要把数量修改到背靠背的根级委托单
     * @param deliveryBillId
     * @param assignVehicleDTOList
     * @param operationUserId
     * @param operationUserName
     */
    List<ShipBill> assignVehicle(String deliveryBillId, List<AssignVehicleDTO> assignVehicleDTOList, String operationUserId, String operationUserName);

    /**
     * 判断当前时刻的委托单是否可以指派或委托
     * @param deliveryBill
     * @param assignQuantity
     * @return
     */
    boolean canAssign(DeliveryBill deliveryBill, BigDecimal assignQuantity);

    /**
     * 改航到新的委托单
     * @param fromLeafDeliveryBillId 运单原先的叶子节点委托单ID
     * @param toSellerDeliveryBillId 操作改航操作的委托单
     * @param rollBackPlanQuantity 改航运单返还计划数量
     * @param rerouteQuantity 改航数量
     * @param operationUserId 操作人ID
     * @param operationUserName 操作人名
     * @return 新叶子节点的委托单信息
     */
    DeliveryRerouteResult rerouteChangeDelivery(String fromLeafDeliveryBillId,
                                                String toSellerDeliveryBillId,
                                                BigDecimal rollBackPlanQuantity,
                                                BigDecimal rerouteQuantity,
                                                String operationUserId,
                                                String operationUserName);

    /**
     * 查询或生成委托单的叶子节点
     * @param deliveryBill
     * @param shippingInfoDTO
     * @param operationUserId
     * @param quantity
     * @param shippingId
     * @return
     */
    DeliveryBill childLeaf(DeliveryBill deliveryBill, ShippingInfoDTO shippingInfoDTO, String operationUserId, BigDecimal quantity, String shippingId);

}
