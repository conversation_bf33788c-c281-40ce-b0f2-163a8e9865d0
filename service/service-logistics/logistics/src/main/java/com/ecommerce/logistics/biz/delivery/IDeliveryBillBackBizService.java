package com.ecommerce.logistics.biz.delivery;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Auther: colu
 * @Date: 2021-01-12 17:32
 * @Description: 委托单反向修改业务接口
 */
public interface IDeliveryBillBackBizService {

    /**
     * 一笔运单出站,修改委托单的发货数量
     * @param deliveryBillId
     * @param actualLeaveQuantity
     * @param operateUserId
     */
    void addSendQuantity(String deliveryBillId, BigDecimal actualLeaveQuantity, String operateUserId);

    /**
     * 减少承运计划量
     * @param deliveryBillIdList
     * @param carryPlanQuantity
     * @param operateUserId
     * @param isSelfTake
     */
    void subTractCarryPlanQuantity(List<String> deliveryBillIdList, BigDecimal carryPlanQuantity, String operateUserId, Boolean isSelfTake);

    /**
     * 尝试关闭委托单
     * @param deliveryBillId
     */
    void tryCloseDelivery(String deliveryBillId);

    /**
     * 一笔运单完成,修改委托单的完成数量
     * @param deliveryBillId
     * @param carryCompleteQuantity
     * @param carryPlanQuantity
     * @param completeTime
     * @param operateUserId
     * @param operateUserName
     */
    void addCompleteQuantityFromShipBill(String deliveryBillId,
                                         BigDecimal carryCompleteQuantity,
                                         BigDecimal carryPlanQuantity,
                                         Date completeTime,
                                         String operateUserId,
                                         String operateUserName);

    /**
     * 通知交易发货单完成
     */
    void finishTakeCode(String takeCode,String operatorName);

    /**
     * 通知交易发货单关闭
     */
    void closeTakeCode(String takeCode,String operatorName);

    /**
     * 重审委托单状态
     * @param leafDeliveryBillId
     * @param operateUserId
     */
    void retrialDeliveryStatusAfterReroute(String leafDeliveryBillId, String operateUserId);
}
