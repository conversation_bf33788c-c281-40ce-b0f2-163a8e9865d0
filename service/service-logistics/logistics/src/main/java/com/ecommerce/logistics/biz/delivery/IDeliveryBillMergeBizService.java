package com.ecommerce.logistics.biz.delivery;

import com.ecommerce.logistics.api.dto.MergeDeliveryAssignDTO;
import com.ecommerce.logistics.dao.vo.ShipBill;

import java.util.List;

/**
 * @Auther: colu
 * @Date: 2021-02-23 20:57
 * @Description: 委托单发起整合运力的业务接口
 */
public interface IDeliveryBillMergeBizService {

    /**
     * 整合运力并指派
     * @param mergeDeliveryAssignDTO
     * @return
     */
    List<ShipBill> mergeAssign(MergeDeliveryAssignDTO mergeDeliveryAssignDTO);

}
