package com.ecommerce.logistics.biz.delivery;

import com.ecommerce.logistics.api.dto.WarehouseChangeDTO;
import com.ecommerce.logistics.dao.vo.InternalAllocation;
import com.ecommerce.logistics.dao.vo.ShipBill;

import java.util.List;

/**
 * @Auther: colu
 * @Date: 2021-01-06 15:04
 * @Description: 委托单操作业务接口
 * NOTE:
 * 1.叶子委托单用户不能操作,列表不能查看
 */
public interface IDeliveryBillOperateBizService {

    /**
     * 接受委托
     * 条件：委托单状态为待确认时可以调用此方法
     * 业务逻辑
     * 1.修改当前委托单状态为待安排
     * 2.更新上级委托单委托接受量字段(把当前委托单的商品数量加上去)
     * 3.记录日志
     */
    void acceptDelivery(String deliveryBillId,String operationUserId, String operationUserName);

    /**
     * 拒绝委托
     * 条件：委托单状态为待确认时可以调用此方法
     * 业务逻辑
     * 1.修改当前委托单状态为已拒绝
     * 2.更新上级委托单委托总量字段(上级委托单委托总量减去当前委托单商品数量)
     * 3.记录日志
     */
    void rejectDelivery(String deliveryBillId,String reason,String operationUserId, String operationUserName);

    /**
     * 取消委托
     * 条件：委托单状态为待确认时可以调用此方法
     * 业务逻辑
     * 1.修改当前委托单状态为已取消
     * 2.更新上级委托单委托总量字段(上级委托单委托总量减去当前委托单商品数量)
     * 3.记录日志
     */
    void cancelDelivery(String deliveryBillId,String operationUserId, String operationUserName);

    /**
     * 中止委托(已弃用)
     * 条件：当前委托单状态为待安排、所有子委托单都为最终状态
     * 业务逻辑
     * 1.修改当前委托单状态为已中止
     * 2.更新当前委托单商品数量（修改为已完成数量加改派掉的数量，记录未完成数量）---- 新需求改为不更新
     * 3.更新父委托单的委托总量（减去之前记录的未完成数量）
     * 4.更新父委托单的委托接受量（减去之前记录的未完成数量）
     * 4.记录日志
     * 5.如果是背靠背委托单，则调用方法，中止其背靠背委托单
     */
    @Deprecated(since = "2.1.4-RELEASE")
    void stopDelivery(String deliveryBillId,String reason,String operationUserId, String operationUserName);

    /**
     * 中止委托(支持上级关下级)
     * 条件：该委托单下所有运单都是终止状态
     * 业务逻辑
     * 1.修改当前委托单状态为已中止
     * 2.修改下层所有委托单为已中止
     * 3.更新父委托单的委托总量（减去当前委托单的未完成数量）
     * 4.记录日志
     * 5.如果是背靠背委托单，则调用方法，中止其背靠背委托单
     */
    void stopDeliveryTree(String deliveryBillId,String reason,String operationUserId, String operationUserName);

    /**
     * 中止委托(背靠背关联委托单专用)
     * 条件：当前委托单状态为待安排、所有子委托单都为最终状态
     * 业务逻辑
     * 1.修改当前委托单状态为已中止
     * 2.更新当前委托单商品数量（修改为已完成数量加改派掉的数量，记录未完成数量）---- 新需求改为不更新
     * 3.记录日志
     */
    void stopDeliveryForBKB(String deliveryBillId,String reason,String operationUserId, String operationUserName);

    /**
     * 交易关闭订单和发货单通知物流时调用
     * 业务逻辑
     * 1.修改所有未完成运单的状态为已关闭
     * 2.修改所有不是终态的委托单状态为已中止
     */
    List<ShipBill> stopDeliveryByOrder(List<String> takeCodeList, String reason);

    /**
     * 修改委托单的仓库
     * @param warehouseChangeDTO
     * @return
     */
    void changeDeliveryWarehouse(WarehouseChangeDTO warehouseChangeDTO);

    /**
     * 修改委托单承运商信息
     * @param allocation
     * @return
     */
    void changeDeliveryInfoCarrier(InternalAllocation allocation);

}
