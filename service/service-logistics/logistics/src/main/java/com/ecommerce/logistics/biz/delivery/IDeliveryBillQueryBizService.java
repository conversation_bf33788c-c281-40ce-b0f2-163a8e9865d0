package com.ecommerce.logistics.biz.delivery;

import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.*;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.waybill.ContractSubmitInfoDTO;
import com.ecommerce.logistics.dao.dto.delivery.DeliveryBillPathItem;
import com.ecommerce.logistics.dao.vo.DeliveryBill;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Auther: colu
 * @Date: 2021-01-06 15:05
 * @Description: 委托单查询业务接口
 */
public interface IDeliveryBillQueryBizService {

    /**
     * 获取委托单的叶子子节点
     * @param deliveryBillId
     * @param actualCarrierId
     * @return
     */
    DeliveryBill queryChildLeaf(String deliveryBillId, String actualCarrierId);

    /**
     * 获取委托单的叶子节点
     * @param deliveryBillId
     * @return
     */
    List<DeliveryBill> queryChild(String deliveryBillId);

    /**
     * 获取指定配送信息的根级委托单
     * @param deliveryInfoId
     * @return
     * NOTE: 背靠背产生的虚拟委托单既是根节点,也是叶子节点
     */
    DeliveryBill queryRootBill(String deliveryInfoId);

    /**
     * 委托单列表分页查询
     */
    PageData<DeliveryListDTO> queryDeliveryList(PageQuery<DeliveryQueryDTO> pageQuery);
    /**
     * 待整合的委托单查询
     */
    PageData<DeliveryListDTO> queryWaitMergeDeliveryList(PageQuery<WaitMergeDeliveryQueryDTO> pageQuery);

    /**
     * 委托单详情查询
     */
    DeliveryDetailDTO queryDeliveryDetailById(String deliveryBillId);

    /**
     * 判断委托单所在发货单的其他根级委托单是否都是终态
     */
    Boolean canCompleteTake(String deliveryBillId);

    /**
     * 查询配送信息的根级委托单
     * @param
     * @return
     */
    DeliveryBill queryRootBillByInfoId(String deliveryInfoId);

    /**
     * 获取叶子节点的委托单的路径信息
     * @param leafDeliveryBillId
     * @return
     */
    DeliveryBillPathItem extractBillPathForLeaf(String leafDeliveryBillId);

    /**
     * 获取普通委托单的路径信息
     * @param deliveryBillId
     * @return
     */
    List<String> extractBillPath(String deliveryBillId);

    /**
     * 委托时查询运费单价
     * @param queryDTO
     * @return
     */
    BigDecimal queryPayableCarriagePrice(PayableCarriagePriceQueryDTO queryDTO);

    /**
     * 委托单ID查询委托单的改航相关信息
     * @param deliveryBillId
     * @return
     */
    DeliveryRerouteInfoDTO queryDeliveryRerouteInfoById(String deliveryBillId);

    /**
     * 委托单的直线距离
     * @param deliveryBillId
     * @return
     */
    BigDecimal lineDistanceForDeliveryBillId(String deliveryBillId);


    /**
     * 查询对账规则需要的委托单会员信息
     * @param deliverySimpleQueryDTO
     * @return
     */
    List<DeliverySimpleDTO> queryMemberInfoForCheckRule(DeliverySimpleQueryDTO deliverySimpleQueryDTO);
    
    /**
     * 委托单当天完成情况统计
     * @param dto
     * @return
     */
    DeliveryStatisticsDTO statisticsDeliveryForTheSomeDay(DeliveryQueryDTO dto);
    
    /**
     * 委托单当月完成情况统计
     * @param dto
     * @return
     */
    DeliveryStatisticsDTO statisticsDeliveryForTheSomeMonth(DeliveryQueryDTO dto);

    /**
     * 紧急任务列表分页查询
     */
    List<DeliveryListDTO> queryUrgentTaskList(QueryUrgentTaskDTO query);

    /**
     * 无车承运人 -- 托运合同上报信息查询
     * @param deliveryBillNum
     * @return
     */
    ContractSubmitInfoDTO queryContractInfo(String deliveryBillNum);
    
    /**
     * 查询物流调价用户
     * @param queryDTO
     * @return
     */
    List<LogisticsAdjustPriceMemberDTO> queryLogisticsAdjustPriceMemberList(LogisticsAdjustPriceDTO queryDTO);

    /**
     * 根据委托单号获取委托单
     * @param deliveryBillNum
     * @return
     */
    DeliveryBill queryBillByBillNum(String deliveryBillNum);

    /**
     * 查询物流调价运单列表
     * @param queryDTO
     * @return
     */
    List<LogisticsAdjustPriceItemDTO> queryLogisticsAdjustPriceItemList(LogisticsAdjustPriceMemberDTO queryDTO);
}
