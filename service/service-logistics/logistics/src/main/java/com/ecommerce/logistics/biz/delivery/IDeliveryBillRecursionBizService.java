package com.ecommerce.logistics.biz.delivery;

import com.ecommerce.logistics.dao.vo.DeliveryBill;

/**
 * @Auther: colu
 * @Date: 2021-03-05 18:25
 * @Description: 委托单递归服务
 */
public interface IDeliveryBillRecursionBizService {

    /**
     * 递归中止委托下级
     * 条件：该委托单下所有运单都是终止状态
     * 业务逻辑
     * 1.修改当前委托单状态为已中止
     * 2.修改下层所有委托单为已中止
     * 3.更新父委托单的委托总量（减去当前委托单的未完成数量）
     * 4.记录日志
     */
    void stopDeliveryToSubTreeREC(DeliveryBill deliveryBill, String reason, String operationUserId, String operationUserName);


}
