package com.ecommerce.logistics.biz.delivery;

import com.ecommerce.logistics.api.dto.DeliveryBillRootCreateDTO;
import com.ecommerce.logistics.api.dto.DeliveryInfoDTO;
import com.ecommerce.logistics.api.dto.waybill.DeliveryInfoProxyDTO;
import com.ecommerce.logistics.dao.vo.DeliveryInfo;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Auther: colu
 * @Date: 2020-12-11 11:02
 * @Description: 配送消息业务接口
 */
public interface IDeliveryInfoBizService {

    /**
     * 保存配送信息
     * @param deliveryInfoDTO
     * @return 返回主键
     */
    String createDeliveryInfo(DeliveryInfoDTO deliveryInfoDTO);

    /**
     * 主键查询配送信息
     * @param deliveryInfoId
     * @return
     */
    DeliveryInfoDTO queryByInfoId(String deliveryInfoId);

    /**
     * 主键列表查询信息
     * @param deliveryInfoIdList
     * @return
     */
    List<DeliveryInfoDTO> queryByInfoIdList(List<String> deliveryInfoIdList);

    /**
     * 添加背靠背配送信息组(先保存不可指派端,再保存可指派端)
     * @param primaryInfoDTO
     * @param secondaryInfoDTO
     * NOTE: 不可指派端的parentInfoId 与 deliveryInfoId相同
     *       可指派端的parentInfoId为不可指派端的deliveryInfoId
     * @return 返回可操作端
     */
    DeliveryInfoDTO createProxyGroupDeliveryInfo(DeliveryInfoDTO primaryInfoDTO, DeliveryInfoDTO secondaryInfoDTO);

    /**
     * 获取当前Info的背靠背对应ID
     * @param deliveryInfoId
     * @return 返回背靠背对应的InfoId
     */
    String queryProxyInfoId(String deliveryInfoId);

    /**
     * 按照配送信息组装顶级委托单
     * @param deliveryInfoDTO
     * @param quantity
     * @return
     */
    DeliveryBillRootCreateDTO covertInfoToRootBill(DeliveryInfoDTO deliveryInfoDTO, BigDecimal quantity);

    /**
     * 填充仓库信息到配送信息实体中
     * @param deliveryInfoDTO
     */
    void fillWarehouseInfo(DeliveryInfoDTO deliveryInfoDTO);

    /**
     * 运单ID查找可操作方的配送信息
     * @param waybillId
     * @return
     */
    DeliveryInfo selectCanOperateByWaybillId(String waybillId);

    /**
     * 统计配送信息下签收数量
     * @param deliveryInfoId
     * @return
     */
    BigDecimal sumSignQuantity(String deliveryInfoId);


    /**
     * 通过parentInfoId获取背靠背配送信息集合
     * @param parentInfoId
     * @return 返回背靠背对应的配送信息集合
     */
    List<DeliveryInfoProxyDTO> queryProxyInfoList(String parentInfoId);

    /**
     * 通过运单ID获取配送信息
     * @param waybillId
     * @return
     */
    DeliveryInfo selectDeliveryInfoByWaybillId(String waybillId);

    /**
     * 查询运单的终端买家配送信息
     * @param waybillId
     * @return
     */
    DeliveryInfo selectRealBuyerDeliveryInfoByWaybillId(String waybillId);


}
