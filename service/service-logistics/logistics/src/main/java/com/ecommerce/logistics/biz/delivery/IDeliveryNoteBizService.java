package com.ecommerce.logistics.biz.delivery;

import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.delivery.DeliveryNoteAddDTO;
import com.ecommerce.logistics.api.dto.delivery.DeliveryNoteDetailDTO;
import com.ecommerce.logistics.api.dto.delivery.DeliveryNoteDetailQueryDTO;
import com.ecommerce.logistics.api.dto.delivery.DeliveryNoteEditDTO;
import com.ecommerce.logistics.api.dto.delivery.DeliveryNoteListDTO;
import com.ecommerce.logistics.api.dto.delivery.DeliveryNoteQueryDTO;

/**
 * @Auther: colu
 * @Date: 2019-06-18 16:18
 * @Description: 送货单业务接口
 */
public interface IDeliveryNoteBizService {

    /**
     * 创建送货单
     * @param deliveryNoteAddDTO
     * @return
     */
    String createDeliveryNote(DeliveryNoteAddDTO deliveryNoteAddDTO);

    /**
     * 编辑送货单
     * @param deliveryNoteEditDTO
     * @return
     */
    void editDeliveryNote(DeliveryNoteEditDTO deliveryNoteEditDTO);

    /**
     * 送货单明细查询
     * @param deliveryNoteDetailQueryDTO
     * @return
     */
    DeliveryNoteDetailDTO queryDetail(DeliveryNoteDetailQueryDTO deliveryNoteDetailQueryDTO);

    /**
     * 送货单列表查询
     * @param pageQuery
     * @return
     */
    PageData<DeliveryNoteListDTO> queryList(PageQuery<DeliveryNoteQueryDTO> pageQuery);

}
