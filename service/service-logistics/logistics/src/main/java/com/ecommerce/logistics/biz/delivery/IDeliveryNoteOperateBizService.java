package com.ecommerce.logistics.biz.delivery;

import com.ecommerce.logistics.api.dto.delivery.DeliveryNoteLeaveDTO;
import com.ecommerce.logistics.api.dto.delivery.DeliveryNoteSignDTO;

/**
 * @Auther: colu
 * @Date: 2019-06-27 11:23
 * @Description: 送货单操作业务层接口
 */
public interface IDeliveryNoteOperateBizService {

    /**
     * 送货单出厂
     * @param deliveryNoteLeaveDTO
     */
    void deliveryNoteLeave(DeliveryNoteLeaveDTO deliveryNoteLeaveDTO);

    /**
     * 送货单签收
     * @param deliveryNoteSignDTO
     */
    void deliveryNoteSign(DeliveryNoteSignDTO deliveryNoteSignDTO);
}
