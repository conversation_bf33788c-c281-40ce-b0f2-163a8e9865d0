package com.ecommerce.logistics.biz.delivery;

import com.ecommerce.logistics.api.dto.delivery.item.DeliveryNoteEditItem;
import com.ecommerce.logistics.dao.dto.delivery.DeliveryNoteProductItem;
import com.ecommerce.logistics.dao.dto.delivery.DeliverySendBean;
import com.ecommerce.logistics.dao.vo.DeliverySendMap;

import java.util.Date;
import java.util.List;

/**
 * @Auther: colu
 * @Date: 2019-06-18 19:04
 * @Description: 送货单商品映射业务服务
 */
public interface IDeliverySendMapBizService {

    /**
     * 保存新的送货单商品映射
     * @param deliveryNoteEditItemList
     * @param deliveryNoteId
     * @param createUser
     * @param updateUser
     */
    void saveDeliverySendMapList(List<DeliveryNoteEditItem> deliveryNoteEditItemList, String deliveryNoteId, String createUser, String updateUser);

    /**
     * 解绑送货单商品映射
     * @param deliveryNoteId
     * @param updateUser
     * @param updateTime
     */
    void deleteByDeliveryNoteId(String deliveryNoteId, String updateUser, Date updateTime);

    /**
     * 获取送货单的映射记录
     * @param deliveryNoteId
     * @return
     */
    List<DeliverySendMap> selectByDeliveryNoteId(String deliveryNoteId);

    /**
     * 送货单和子状态查询
     * @param deliveryNoteId
     * @param statusList
     * @return
     */
    List<DeliverySendBean> selectByDeliveryNoteIdAndSubStatusList(String deliveryNoteId, String... statusList);

    /**
     * 查询送货单下满足条件的商品名集合
     * @param deliveryNoteIdList
     * @param productNameLike
     * @return
     */
    List<DeliveryNoteProductItem> selectProductNameByNoteId(List<String> deliveryNoteIdList, String productNameLike);

    /**
     * 查询送货单下的deliverySendBean
     * @param deliveryNoteId
     * @return
     */
    List<DeliverySendBean> selectSendBeanByDeliveryNote(String deliveryNoteId);

}
