package com.ecommerce.logistics.biz.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import cn.hutool.core.util.ObjectUtil;
import com.ecommerce.base.api.dto.warehouse.WarehouseListDTO;
import com.ecommerce.logistics.api.constant.NumberConstant;
import com.github.pagehelper.page.PageMethod;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.logistics.api.dto.waybill.WaybillReceiveAddressDTO;
import com.ecommerce.logistics.dao.dto.waybill.WaybillQuantityDO;
import com.ecommerce.logistics.util.LogisticsUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.ibatis.session.RowBounds;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.result.PageData;
import com.ecommerce.common.service.common.BaseBiz;
import com.ecommerce.common.service.common.uuid.UUIDGenerator;
import com.ecommerce.common.utils.DateUtil;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.settlement.CarriageSettlementDTO;
import com.ecommerce.logistics.api.dto.settlement.MonthSettlementListDTO;
import com.ecommerce.logistics.api.dto.settlement.MonthSettlementListQueryDTO;
import com.ecommerce.logistics.api.dto.settlement.SettlementItemListDTO;
import com.ecommerce.logistics.api.dto.settlement.SettlementItemListQueryDTO;
import com.ecommerce.logistics.api.dto.settlement.SettlementListDTO;
import com.ecommerce.logistics.api.dto.settlement.SettlementListQueryDTO;
import com.ecommerce.logistics.api.dto.settlement.UserSettlementAmountDTO;
import com.ecommerce.logistics.api.dto.settlement.UserSettlementListDTO;
import com.ecommerce.logistics.api.dto.settlement.UserSettlementListQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillDTO;
import com.ecommerce.logistics.api.enums.CarrierTypeEnum;
import com.ecommerce.logistics.api.enums.WaybillTypeEnum;
import com.ecommerce.logistics.biz.IDispatchBillBizService;
import com.ecommerce.logistics.biz.ISettlementBizService;
import com.ecommerce.logistics.dao.dto.settlement.SettlementItemListDO;
import com.ecommerce.logistics.dao.dto.settlement.SettlementPeriodDO;
import com.ecommerce.logistics.dao.dto.settlement.SettlementUpdateDO;
import com.ecommerce.logistics.dao.dto.waybill.SubWaybillDO;
import com.ecommerce.logistics.dao.mapper.ProductQuantityMapMapper;
import com.ecommerce.logistics.dao.mapper.UserSettlementMapper;
import com.ecommerce.logistics.dao.mapper.WaybillMapper;
import com.ecommerce.logistics.dao.mapper.WaybillSettlementMapper;
import com.ecommerce.logistics.dao.vo.DispatchBill;
import com.ecommerce.logistics.dao.vo.UserSettlement;
import com.ecommerce.logistics.dao.vo.Waybill;
import com.ecommerce.logistics.dao.vo.WaybillSettlement;
import com.github.pagehelper.PageInfo;

import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

/**
 * @Auther: <EMAIL>
 * @Date: 03/09/2018 20:30
 * @Description:
 */
@Service("settlementBizService")
public class SettlementBizService implements ISettlementBizService {

    public static final String CARRIER_ID_NULL = "carrierId=null";
    public static final String CARRIER_ID = "carrierId";
    public static final String SETTLEMENT_PERIOD = "settlementPeriod";
    public static final String ACTUAL_KM = "actualKm";
    public static final String ACTUAL_QUANTITY = "actualQuantity";
    @Autowired
    private UserSettlementMapper userSettlementMapper;

    @Autowired
    private WaybillSettlementMapper waybillSettlementMapper;

    @Autowired
    private WaybillQueryBizService waybillQueryBizService;

    @Autowired
    private UUIDGenerator uuidGenerator;

    @Autowired
    private IDispatchBillBizService dispatchBillBizService;
    
    @Autowired
    private ProductQuantityMapMapper productQuantityMapMapper;
    
    @Autowired
    private WaybillMapper waybillMapper;

    @Override
    public PageData<SettlementListDTO> querySettlementList(PageQuery<SettlementListQueryDTO> pageQuery) {
        SettlementListQueryDTO settlementListQueryDTO = pageQuery.getQueryDTO() == null ?
                new SettlementListQueryDTO() : pageQuery.getQueryDTO();
        PageInfo<SettlementListDTO> pageInfo = PageMethod
                .startPage(ObjectUtil.defaultIfNull(pageQuery.getPageNum(), NumberConstant.DEFAULT_PAGE_NUM),
                        ObjectUtil.defaultIfNull(pageQuery.getPageSize(), NumberConstant.DEFAULT_PAGE_SIZE))
                .doSelectPageInfo(
                        () -> userSettlementMapper.querySettlementList(settlementListQueryDTO));
        if (CollectionUtils.isEmpty(pageInfo.getList())) {
            return new PageData<>(new ArrayList<>());
        }
        List<String> carrierIdList = pageInfo.getList().stream().map(SettlementListDTO::getCarrierId).toList();
        //获取运单详细信息
        List<SettlementPeriodDO> settlementPeriodList = userSettlementMapper.querySettlementListPeriod(carrierIdList);
        Map<String, SettlementPeriodDO> settlementMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(settlementPeriodList)) {
            for (SettlementPeriodDO settlementPeriodDO : settlementPeriodList) {
                settlementMap.put(settlementPeriodDO.getCarrierId(), settlementPeriodDO);
            }
        }
        /*pageInfo.getList().stream().filter(settlementListDTO -> settlementMap.get(settlementListDTO) != null).forEach(settlementListDTO -> {
            settlementListDTO.setSettlementBeginPeriod(settlementMap.get(settlementListDTO).getSettlementBeginPeriod());
            settlementListDTO.setSettlementEndPeriod(settlementMap.get(settlementListDTO).getSettlementEndPeriod());
        });*/

        return new PageData<>(pageInfo);
    }

    @Override
    public PageData<MonthSettlementListDTO> queryMonthSettlementList(PageQuery<MonthSettlementListQueryDTO> pageQuery) {
        MonthSettlementListQueryDTO monthSettlementListQueryDTO = pageQuery.getQueryDTO() == null ?
                new MonthSettlementListQueryDTO() : pageQuery.getQueryDTO();
        if (CsStringUtils.isEmpty(monthSettlementListQueryDTO.getCarrierId())) {
            throw new BizException(BasicCode.INVALID_PARAM, CARRIER_ID_NULL);
        }
        PageInfo<MonthSettlementListDTO> pageInfo = PageMethod
                .startPage(ObjectUtil.defaultIfNull(pageQuery.getPageNum(), NumberConstant.DEFAULT_PAGE_NUM),
                        ObjectUtil.defaultIfNull(pageQuery.getPageSize(),NumberConstant.DEFAULT_PAGE_SIZE))
                .doSelectPageInfo(
                () -> userSettlementMapper.queryMonthSettlementList(monthSettlementListQueryDTO));
        return new PageData<>(pageInfo);
    }

    @Override
    public PageData<SettlementItemListDTO> querySettlementItemList(PageQuery<SettlementItemListQueryDTO> pageQuery) {
        SettlementItemListQueryDTO settlementItemListQueryDTO = LogisticsUtils.defaultIfNull(pageQuery.getQueryDTO(),new SettlementItemListQueryDTO());
        if (CsStringUtils.isEmpty(settlementItemListQueryDTO.getCarrierId())) {
            throw new BizException(BasicCode.INVALID_PARAM, CARRIER_ID_NULL);
        }
        PageInfo<SettlementItemListDO> pageInfo = PageMethod
                .startPage(ObjectUtil.defaultIfNull(pageQuery.getPageNum(), NumberConstant.DEFAULT_PAGE_NUM),
                        ObjectUtil.defaultIfNull(pageQuery.getPageSize(),NumberConstant.DEFAULT_PAGE_SIZE))
                .doSelectPageInfo(
                () -> waybillSettlementMapper.querySettlementItemList(settlementItemListQueryDTO));
        List<SettlementItemListDTO> settlementItemList = new ArrayList<>();
        PageData<SettlementItemListDTO> pageData = new PageData<>(settlementItemList);
        if (CollectionUtils.isEmpty(pageInfo.getList())) {
            return pageData;
        }
        List<String> waybillIdList = new ArrayList<>();
        for (SettlementItemListDO settlementItemListDO : pageInfo.getList()) {
            waybillIdList.add(settlementItemListDO.getWaybillId());
            SettlementItemListDTO settlementItemListDTO = new SettlementItemListDTO();
            BeanUtils.copyProperties(settlementItemListDO, settlementItemListDTO);
            settlementItemListDTO.setActualKm(settlementItemListDTO.getActualKm() == null ?
                    BigDecimal.ZERO : settlementItemListDTO.getActualKm());
            settlementItemList.add(settlementItemListDTO);
        }
        //获取运单数量数据
        List<WaybillQuantityDO> waybillQuantityDOList = waybillQueryBizService.queryWaybillTotalQuantityByParentId(waybillIdList);
        Map<String, WaybillQuantityDO> quantityMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(waybillQuantityDOList)) {
            for (WaybillQuantityDO waybillQuantityDO : waybillQuantityDOList) {
                quantityMap.put(waybillQuantityDO.getWaybillId(), waybillQuantityDO);
            }
        }
        //获取运单详细信息
        List<SubWaybillDO> waybillList = waybillQueryBizService.selectSubWaybillListByParentId(waybillIdList);
        if (CollectionUtils.isEmpty(waybillList)) {
            throw new BizException(BasicCode.DATA_NOT_EXIST, "waybillList=" + waybillList);
        }
        List<String> warehouseIdList = new ArrayList<>();
        Map<String, WarehouseListDTO> subWaybillMap = new HashMap<>();
        warehouseIdList.addAll(waybillList.stream().map(SubWaybillDO::getWarehouseId).toList());
        Map<String, WarehouseListDTO> warehouseMap = waybillQueryBizService.getWarehouseMap(warehouseIdList);
        Map<String, List<WaybillReceiveAddressDTO>> receiveAddressMap = new HashMap<>();
        for (SubWaybillDO subWaybillDO : waybillList) {
            subWaybillMap.put(subWaybillDO.getParentId(), warehouseMap.get(subWaybillDO.getWarehouseId()));
            WaybillReceiveAddressDTO waybillReceiveAddressDTO = new WaybillReceiveAddressDTO();
            BeanUtils.copyProperties(subWaybillDO, waybillReceiveAddressDTO);
            if (receiveAddressMap.get(subWaybillDO.getParentId()) == null) {
                receiveAddressMap.put(subWaybillDO.getParentId(), Lists.newArrayList(waybillReceiveAddressDTO));
            } else {
                receiveAddressMap.get(subWaybillDO.getParentId()).add(waybillReceiveAddressDTO);
            }
        }
        for (SettlementItemListDTO settlementItemListDTO :settlementItemList) {
            handleSettlementItem(settlementItemListDTO, subWaybillMap, quantityMap, receiveAddressMap);
        }
        pageData.setList(settlementItemList);
        pageData.setPageSize(pageInfo.getPageSize());
        pageData.setPageNum(pageInfo.getPageNum());
        pageData.setPages(pageInfo.getPages());
        pageData.setTotal(pageInfo.getTotal());

        return pageData;
    }

    private static void handleSettlementItem(SettlementItemListDTO settlementItemListDTO,
                                             Map<String, WarehouseListDTO> subWaybillMap,
                                             Map<String, WaybillQuantityDO> quantityMap,
                                             Map<String, List<WaybillReceiveAddressDTO>> receiveAddressMap) {
        if (subWaybillMap.get(settlementItemListDTO.getWaybillId()) != null) {
            WarehouseListDTO warehouseListDTO = subWaybillMap.get(settlementItemListDTO.getWaybillId());
            settlementItemListDTO.setWarehouseId(warehouseListDTO.getWarehouseId());
            settlementItemListDTO.setWarehouseName(warehouseListDTO.getName());
            settlementItemListDTO.setWarehouseAddress(warehouseListDTO.getProvince() + warehouseListDTO.getCity()
                    + warehouseListDTO.getDistrict() + warehouseListDTO.getAddress());
        }
        if (quantityMap.get(settlementItemListDTO.getWaybillId()) != null) {
            settlementItemListDTO.setProductQuantity(quantityMap.get(settlementItemListDTO.getWaybillId()).getQuantity());
            settlementItemListDTO.setActualQuantity(quantityMap.get(settlementItemListDTO.getWaybillId()).getActualQuantity());
        }
        if (receiveAddressMap.get(settlementItemListDTO.getWaybillId()) != null) {
            settlementItemListDTO.setReceiveAddressList(receiveAddressMap.get(settlementItemListDTO.getWaybillId()));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void userCarriageSettlement(CarriageSettlementDTO carriageSettlementDTO) {
        Condition condition = new Condition(UserSettlement.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(CARRIER_ID, carriageSettlementDTO.getCarrierId());
        if (CsStringUtils.isNotEmpty(carriageSettlementDTO.getSettlementMonth())) {
            criteria.andEqualTo(SETTLEMENT_PERIOD, carriageSettlementDTO.getSettlementMonth());
        }
        RowBounds rowBounds = new RowBounds(0, 1);
        List<UserSettlement> userSettlementList = userSettlementMapper.selectByExampleAndRowBounds(condition, rowBounds);
        if (CollectionUtils.isEmpty(userSettlementList)) {
            throw new BizException(BasicCode.DATA_NOT_EXIST, "找不到对应的用户结算数据！");
        }
        String carrierType = userSettlementList.get(0).getCarrierType();
        //非社会运力抢单需要输入结算金额
        if (!CarrierTypeEnum.SOCIETY_TRANSPORT.getCode().equals(carrierType)) {
            if (carriageSettlementDTO.getCarriage() == null ||
                carriageSettlementDTO.getCarriage().compareTo(BigDecimal.ZERO) < 1) {
                throw new BizException(BasicCode.INVALID_PARAM, "请输入正确的结算金额！");
            }
            if (CsStringUtils.isEmpty(carriageSettlementDTO.getSettlementMonth())) {
                throw new BizException(BasicCode.INVALID_PARAM, "请输入正确的结算周期！");
            }
            UserSettlement userSettlement = userSettlementList.get(0);
            userSettlement.setSettlementedWeight(userSettlement.getSettlementedWeight().add(userSettlement.getUnsettlementWeight()));
            userSettlement.setSettlementedMileage(userSettlement.getSettlementedMileage().add(userSettlement.getUnsettlementMileage()));
            userSettlement.setSettlementedCarriage(userSettlement.getSettlementedCarriage().add(carriageSettlementDTO.getCarriage()));
            userSettlement.setUnsettlementWeight(BigDecimal.ZERO);
            userSettlement.setUnsettlementMileage(BigDecimal.ZERO);
            userSettlement.setUnsettlementCarriage(BigDecimal.ZERO);
            userSettlementMapper.updateByPrimaryKeySelective(userSettlement);
        } else {
            userSettlementMapper.userCarriageSettlement(carriageSettlementDTO);
        }
        waybillSettlementMapper.userCarriageSettlement(carriageSettlementDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void waybillCarriageSettlement(CarriageSettlementDTO carriageSettlementDTO) {
        SettlementUpdateDO settlementUpdateDO = waybillSettlementMapper.queryWaybillSettlementData(carriageSettlementDTO.getWaybillIdList());
        if (settlementUpdateDO == null) {
            throw new BizException(BasicCode.INVALID_PARAM, "找不到运单对应的结算数据！");
        }
        if (settlementUpdateDO.getPublishedCarriage() == null ||
            settlementUpdateDO.getPublishedCarriage().compareTo(BigDecimal.ZERO) < 1) {
            throw new BizException(BasicCode.INVALID_PARAM, "结算运费不能为空！");
        }
        waybillSettlementMapper.waybillCarriageSettlement(carriageSettlementDTO);
        //获取所有运单的实际结算数量
        Condition condition = new Condition(UserSettlement.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(CARRIER_ID, carriageSettlementDTO.getCarrierId());
        criteria.andEqualTo(SETTLEMENT_PERIOD, carriageSettlementDTO.getSettlementMonth());
        criteria.andEqualTo("delFlg", 0);
        List<UserSettlement> userSettlementList = userSettlementMapper.selectByCondition(condition);
        if (CollectionUtils.isEmpty(userSettlementList)) {
            throw new BizException(BasicCode.DATA_NOT_EXIST, "queryCondition=" + condition);
        }
        UserSettlement userSettlement = userSettlementList.get(0);
        userSettlementMapper.selectSettlementForLock(userSettlement.getUserSettlementId());
        BigDecimal unsettlementCarriage = userSettlement.getUnsettlementCarriage().subtract(settlementUpdateDO.getPublishedCarriage());
        userSettlement.setUnsettlementCarriage(BigDecimal.ZERO.compareTo(unsettlementCarriage) < 1 ? unsettlementCarriage : BigDecimal.ZERO);
        BigDecimal unsettlementMileage = userSettlement.getUnsettlementMileage().subtract(settlementUpdateDO.getEstimateKm());
        userSettlement.setUnsettlementMileage(BigDecimal.ZERO.compareTo(unsettlementMileage) < 1 ? unsettlementMileage : BigDecimal.ZERO);
        BigDecimal unsettlementWeight = userSettlement.getUnsettlementWeight().subtract(settlementUpdateDO.getQuantity());
        userSettlement.setUnsettlementWeight(BigDecimal.ZERO.compareTo(unsettlementWeight) < 1 ? unsettlementWeight : BigDecimal.ZERO);
        userSettlement.setSettlementedCarriage(userSettlement.getSettlementedCarriage().add(settlementUpdateDO.getPublishedCarriage()));
        userSettlement.setSettlementedMileage(userSettlement.getSettlementedMileage().add(settlementUpdateDO.getEstimateKm()));
        userSettlement.setSettlementedWeight(userSettlement.getSettlementedWeight().add(settlementUpdateDO.getQuantity()));
        userSettlementMapper.updateByPrimaryKeySelective(userSettlement);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addWaybillSettlement(String waybillId, Map<String, BigDecimal> actualMap) {
        //获取运单详情信息
        List<WaybillDTO> waybillList = waybillQueryBizService.selectWaybillsByWaybillIds(Collections.singletonList(waybillId));
        WaybillDTO waybillDTO = waybillList.get(0);
        Set<String> filterType = Sets.newHashSet(
                WaybillTypeEnum.SELLER_DELIVERY.getCode(),
                WaybillTypeEnum.STORE_DELIVERY.getCode());
        //卖家配送、门店配送不需要结算运费
        if (filterType.contains(waybillDTO.getType())) return;
        if (null == waybillDTO.getQuantity()) {//多个子运单
        	waybillDTO.setQuantity(productQuantityMapMapper.selectTotalQuantityByMainWaybillId(waybillId));
        }
        String carrierType = "";
        if (waybillDTO.getType().equals(WaybillTypeEnum.SOCIETY_SNATCH.getCode())) {
            carrierType = CarrierTypeEnum.SOCIETY_TRANSPORT.getCode();
        } else if (waybillDTO.getType().equals(WaybillTypeEnum.CARRIER_ASSIGNING.getCode())) {
            if (CsStringUtils.isNotBlank(waybillDTO.getDispatchBillId())) {
        		//获取具体的承运商类型
        		DispatchBill dispatchBill = dispatchBillBizService.selectDispatchBillById(waybillDTO.getDispatchBillId());
                carrierType = getCarriageType(dispatchBill);
            } else {
        		Waybill waybill = waybillMapper.selectByPrimaryKey(waybillId);
        		if (null != waybill) {
        			carrierType = waybill.getCarrierType();
        		}
        	}
        }
        //创建结算数据
        WaybillSettlement waybillSettlement = new WaybillSettlement();
        waybillSettlement.setWaybillSettlementId(uuidGenerator.gain());
        waybillSettlement.setCarrierId(waybillDTO.getCarrierId());
        waybillSettlement.setWaybillId(waybillDTO.getWaybillId());
        waybillSettlement.setStatus(Byte.valueOf("1"));
        String settlementMonth = DateUtil.format(new Date(), "yyyy-MM");
        int currentDay = cn.hutool.core.date.DateUtil.dayOfMonth(new Date());
        if (currentDay <= 15) {
            settlementMonth = settlementMonth + "A";
        } else {
            settlementMonth = settlementMonth + "B";
        }
        waybillSettlement.setSettlementMonth(settlementMonth);
        waybillSettlement.setSettlementDate(DateUtil.convertDateToString(new Date()));
        waybillSettlement.setSettlementTime(new Date());
        waybillSettlement.setVersion(0L);
        BaseBiz.setOperInfo(waybillSettlement, 0, true);
        waybillSettlementMapper.insert(waybillSettlement);
        //更新用户结算表数据
        Condition condition = new Condition(UserSettlement.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(CARRIER_ID, waybillDTO.getCarrierId());
        criteria.andEqualTo(SETTLEMENT_PERIOD, waybillSettlement.getSettlementMonth());
        List<UserSettlement> userSettlementList = userSettlementMapper.selectByCondition(condition);
        //如果实际路径为空则设置为规划路径
        if (actualMap.get(ACTUAL_KM).compareTo(BigDecimal.ZERO) == 0) {
            actualMap.put(ACTUAL_KM, waybillDTO.getEstimateKm());
        }
        if (CollectionUtils.isNotEmpty(userSettlementList)) {
            SettlementUpdateDO settlementUpdateDO = new SettlementUpdateDO();
            settlementUpdateDO.setUserSettlementId(userSettlementList.get(0).getUserSettlementId());
            settlementUpdateDO.setQuantity(actualMap.get(ACTUAL_QUANTITY));
            settlementUpdateDO.setEstimateKm(actualMap.get(ACTUAL_KM));
            settlementUpdateDO.setPublishedCarriage(actualMap.get("actualCarriage"));
            //for update修改防止并发
            userSettlementMapper.selectSettlementForLock(userSettlementList.get(0).getUserSettlementId());
            userSettlementMapper.updateSettlementForLock(settlementUpdateDO);
        } else {
            //创建当前月份的新记录
            UserSettlement userSettlement = new UserSettlement();
            userSettlement.setUserSettlementId(uuidGenerator.gain());
            userSettlement.setSettlementPeriod(waybillSettlement.getSettlementMonth());
            userSettlement.setCarrierId(waybillDTO.getCarrierId());
            userSettlement.setCarrierName("");
            userSettlement.setCarrierType(carrierType);
            userSettlement.setUnsettlementCarriage(actualMap.get("actualCarriage"));
            userSettlement.setSettlementedCarriage(BigDecimal.ZERO);
            userSettlement.setUnsettlementMileage(actualMap.get(ACTUAL_KM));
            userSettlement.setSettlementedMileage(BigDecimal.ZERO);
            userSettlement.setTotalMileage(actualMap.get(ACTUAL_KM));
            userSettlement.setUnsettlementWeight(actualMap.get(ACTUAL_QUANTITY));
            userSettlement.setSettlementedWeight(BigDecimal.ZERO);
            userSettlement.setTotalWeight(actualMap.get(ACTUAL_QUANTITY));
            userSettlement.setVersion(0L);
            BaseBiz.setOperInfo(userSettlement, 0, true);
            userSettlementMapper.insertSelective(userSettlement);
        }
    }

    private static String getCarriageType(DispatchBill dispatchBill) {
        String carrierType;
        if (dispatchBill.getCarrierType().equals(CarrierTypeEnum.PLATFORM_SIGN.getCode())) {
            carrierType = CarrierTypeEnum.PLATFORM_SIGN.getCode();
        } else {
            carrierType = CarrierTypeEnum.PLATFORM_SELF.getCode();
        }
        return carrierType;
    }

    @Override
    public UserSettlementAmountDTO queryUserSettlementAmount(String userId) {
        if (CsStringUtils.isEmpty(userId)) {
            throw new BizException(BasicCode.INVALID_PARAM, "用户ID为空");
        }
        UserSettlementAmountDTO userSettlementAmountDTO =  userSettlementMapper.queryUserSettlementAmount(userId);
        if (userSettlementAmountDTO == null) {
            userSettlementAmountDTO = new UserSettlementAmountDTO();
            userSettlementAmountDTO.setUnsettlementAmount(BigDecimal.ZERO);
            userSettlementAmountDTO.setSettlementedAmount(BigDecimal.ZERO);
            userSettlementAmountDTO.setTotalAmount(BigDecimal.ZERO);
        } else {
            userSettlementAmountDTO.setTotalAmount(userSettlementAmountDTO.getUnsettlementAmount()
                    .add(userSettlementAmountDTO.getSettlementedAmount()));
        }
        return userSettlementAmountDTO;
    }

    @Override
    public PageData<UserSettlementListDTO> queryUserSettlementList(PageQuery<UserSettlementListQueryDTO> pageQuery) {
        UserSettlementListQueryDTO userSettlementListQueryDTO = pageQuery.getQueryDTO() == null ?
                new UserSettlementListQueryDTO() : pageQuery.getQueryDTO();
        if (CsStringUtils.isEmpty(userSettlementListQueryDTO.getCarrierId())) {
            throw new BizException(BasicCode.INVALID_PARAM, CARRIER_ID_NULL);
        }
        PageInfo<UserSettlementListDTO> pageInfo = PageMethod
                .startPage(ObjectUtil.defaultIfNull(pageQuery.getPageNum(), NumberConstant.DEFAULT_PAGE_NUM),
                        ObjectUtil.defaultIfNull(pageQuery.getPageSize(),NumberConstant.DEFAULT_PAGE_SIZE))
                .doSelectPageInfo(
                () -> waybillSettlementMapper.queryUserSettlementList(userSettlementListQueryDTO));
        PageData<UserSettlementListDTO> pageData = new PageData<>(pageInfo);
        if (CollectionUtils.isEmpty(pageInfo.getList())) {
            return pageData;
        }
        Map<String, UserSettlementListDTO> userSettlementMap = new HashMap<>();
        List<String> waybillIdList = new ArrayList<>();
        for (UserSettlementListDTO userSettlementListDTO : pageInfo.getList()) {
            waybillIdList.add(userSettlementListDTO.getWaybillId());
            userSettlementMap.put(userSettlementListDTO.getWaybillId(), userSettlementListDTO);
        }
        //获取运单提货点信息
        List<SubWaybillDO> waybillDOList = waybillQueryBizService.selectSubWaybillListByParentId(waybillIdList);
        Set<String> warehouseIdSet = new HashSet<>();
        if (CollectionUtils.isNotEmpty(waybillDOList)) {
            waybillDOList.stream().filter(subWaybillDO -> !warehouseIdSet.contains(subWaybillDO.getWarehouseId()))
                    .forEach(subWaybillDO -> warehouseIdSet.add(subWaybillDO.getWarehouseId()));
        }
        //获取提货点
        Map<String, WarehouseListDTO> warehouseMap = waybillQueryBizService.getWarehouseMap(new ArrayList<>(warehouseIdSet));
        if (CollectionUtils.isNotEmpty(waybillDOList)) {
            waybillDOList.stream().filter(subWaybillDO -> warehouseMap.get(subWaybillDO.getWarehouseId()) != null).forEach(subWaybillDO -> {
                WarehouseListDTO warehouseListDTO = warehouseMap.get(subWaybillDO.getWarehouseId());
                userSettlementMap.get(subWaybillDO.getParentId()).setWarehouseName(warehouseListDTO.getName());
                userSettlementMap.get(subWaybillDO.getParentId()).setWarehouseAddress(warehouseListDTO.getProvince()
                        + warehouseListDTO.getCity() + warehouseListDTO.getDistrict() + warehouseListDTO.getAddress());
            });
        }

        return pageData;
    }
}
