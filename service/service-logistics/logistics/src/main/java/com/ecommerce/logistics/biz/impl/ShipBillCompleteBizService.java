package com.ecommerce.logistics.biz.impl;

import com.alibaba.fastjson.JSON;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.exception.DistributeLockException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.service.ILockService;
import com.ecommerce.common.utils.ArithUtils;
import com.ecommerce.logistics.LogisticsCommonBean;
import com.ecommerce.logistics.api.dto.DeliveryInfoDTO;
import com.ecommerce.logistics.api.dto.operationrecord.OperationRecordAddDTO;
import com.ecommerce.logistics.api.dto.shipping.ShippingOperationDTO;
import com.ecommerce.logistics.api.dto.waybill.*;
import com.ecommerce.logistics.api.enums.*;
import com.ecommerce.logistics.biz.IOperationRecordBizService;
import com.ecommerce.logistics.biz.IProxySyncRecordBizService;
import com.ecommerce.logistics.biz.IShipBillCompleteBizService;
import com.ecommerce.logistics.biz.IShipBillOperateBizService;
import com.ecommerce.logistics.biz.IShippingInfoBizService;
import com.ecommerce.logistics.biz.delivery.IDeliveryBillBackBizService;
import com.ecommerce.logistics.biz.delivery.IDeliveryInfoBizService;
import com.ecommerce.logistics.biz.message.MessageQueueService;
import com.ecommerce.logistics.dao.mapper.ShipBillItemMapper;
import com.ecommerce.logistics.dao.mapper.ShipBillMapper;
import com.ecommerce.logistics.dao.vo.ShipBill;
import com.ecommerce.logistics.dao.vo.ShipBillItem;
import com.ecommerce.logistics.service.IShipBillExternalService;
import com.ecommerce.logistics.service.IWaybillNewExternalService;
import com.ecommerce.logistics.util.LogisticsDateUtils;
import com.ecommerce.logistics.util.DigestUtils;
import com.ecommerce.logistics.util.LogisticsUtils;
import com.ecommerce.open.api.service.apicenter.IOpenAPIInvokeService;
import com.ecommerce.order.api.dto.UpdateTakeInfoSignDTO;
import com.ecommerce.order.api.dto.UpdateTakeItemSignDTO;
import com.ecommerce.order.api.dto.logistics.OverSendOutVerifyDTO;
import com.ecommerce.order.api.dto.logistics.OverSendOutVerifyResponseDTO;
import com.ecommerce.order.api.dto.logistics.ShipBillRefundDTO;
import com.ecommerce.order.api.enums.AdjustAddWayEnum;
import com.ecommerce.order.api.service.ITakeUpDataService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import com.ecommerce.common.utils.CsStringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/**
 * NOTE: 完成子运单
 * 1.普通商品:
 *      配送中->已完成;
 * 2.混凝土:
 *   卖家直接完成:
 *       配送中 -> 已完成;
 *       已签收 -> 已完成; 此时不再通知trace完成,其余时候都需要通知
 *   司机完成:
 *       配送中 -> 已签收;
 * @Auther: colu
 * @Date: 2021-01-19 18:29
 * @Description: 运单完成操作
 */
@Slf4j
@Service("shipBillCompleteBizService")
public class ShipBillCompleteBizService implements IShipBillCompleteBizService {

    private static final String REPEAT_OPERATION = "您点击太快了，请勿重复操作！";
    @Autowired(required = false)
    private ShipBillMapper shipBillMapper;

    @Autowired(required = false)
    private ShipBillItemMapper shipBillItemMapper;

    @Autowired
    private IShipBillOperateBizService shipBillOperateBizService;

    @Autowired
    private IDeliveryInfoBizService deliveryInfoBizService;

    @Autowired
    private IDeliveryBillBackBizService deliveryBillBackBizService;

    @Autowired
    private IProxySyncRecordBizService proxySyncRecordBizService;

    @Autowired
    private IOperationRecordBizService operationRecordBizService;

    @Autowired
    private ILockService redisLockService;

    @Autowired
    private ITakeUpDataService takeUpDataService;

    @Autowired
    private IShipBillExternalService shipBillExternalService;

    @Autowired
    private MessageQueueService messageQueueService;

    @Autowired
    private IShippingInfoBizService shippingInfoBizService;

    @Autowired
    private IOpenAPIInvokeService openAPIInvokeService;

    @Autowired
    private IWaybillNewExternalService waybillNewExternalService;

    /**
     * 完成运单
     * @param completeShipBillDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void completeShipBill(CompleteShipBillDTO completeShipBillDTO) {
        log.info("用户发起完成运单操作:{}", completeShipBillDTO);

        boolean isMainComplete = false;

        if (CsStringUtils.equals(completeShipBillDTO.getSignType(), AdjustAddWayEnum.SELLER_SIGIN.getCode())) {
            throw new BizException(BasicCode.UN_SUPPORT, "混凝土的完成");
        }

        String identifier = "";
        String bizResource = DigestUtils.md5Digest(completeShipBillDTO.toString());
        try {
            identifier = redisLockService.lockFast(bizResource);

            String waybillId = completeShipBillDTO.getWaybillId();
            ShipBill shipBill = shipBillMapper.selectByPrimaryKey(waybillId);
            List<ShipBillItem> shipBillItemList = shipBillItemMapper.selectByWaybillIdAndSeq(waybillId, shipBill.getMaxSeqNum());

            BigDecimal completeQuantity = shipBill.getActualQuantity();

            Date completeTime = completeShipBillDTO.getCompleteTime();
            if (completeTime == null) {
                completeTime = new Date();
            }

            BigDecimal actualKm = BigDecimal.ZERO;

            //完成运单, 添加操作日志
            if (!ShipBillStatusEnum.isFinalStatus(shipBill.getStatus())) {
                shipBillMapper.completeShipBill(waybillId,
                        ShipBillStatusEnum.COMPLETE.getCode(),
                        completeQuantity,
                        actualKm,
                        completeTime,
                        completeShipBillDTO.getOperationUserId());
                if (!LogisticsUtils.flagParse(shipBill.getChildFlag())) {
                    isMainComplete = true;
                }
                log.info("运单完成:{}-{}", shipBill.getWaybillId(), shipBill.getStatus());
                OperationRecordAddDTO operationRecordAddDTO = new OperationRecordAddDTO();
                operationRecordAddDTO.setEntryId(waybillId);
                operationRecordAddDTO.setType(OperationRecordTypeEnum.WAYBILL.getCode());
                operationRecordAddDTO.setOperationType(WaybillOperationTypeEnum.WAYBILL_COMPLETE.getCode());
                operationRecordAddDTO.setContent("完成运单");
                operationRecordAddDTO.setCreateTime(completeTime);
                operationRecordAddDTO.setOperatorId(completeShipBillDTO.getOperationUserId());
                operationRecordAddDTO.setOperatorName(completeShipBillDTO.getOperationUserName());
                operationRecordBizService.saveOperationRecord(operationRecordAddDTO);

            }
            List<ShipBillItem> needUpdateItemList = Lists.newArrayList();
            //判断需要完成的子项
            handleShipBillItem(shipBillItemList, shipBill, needUpdateItemList);

            for (ShipBillItem item : needUpdateItemList) {
                String waybillItemId = item.getWaybillItemId();
                String itemStatus = item.getStatus();
                log.info("运单子项完成:{}-{}", waybillItemId, itemStatus);
                int updateItemCount = shipBillItemMapper.updateWaybillItemStatus(
                        waybillItemId,
                        ShipBillItemStatusEnum.COMPLETE.getCode(),
                        Lists.newArrayList(itemStatus),
                        completeShipBillDTO.getOperationUserId());
                if (updateItemCount > 0) {
                    deliveryBillBackBizService.addCompleteQuantityFromShipBill(
                            item.getDeliveryBillId(),
                            completeQuantity, shipBill.getAssignQuantity(),
                            completeTime,
                            completeShipBillDTO.getOperationUserId(), completeShipBillDTO.getOperationUserName());
                }
            }


            if (LogisticsUtils.flagParse(shipBill.getChildFlag())) {
                //如果是子运单
                int doingSubBillCount = shipBillMapper.countSubNotFinal(shipBill.getParentId());
                if (doingSubBillCount == 0) {
                    shipBillMapper.updateShipBillStatusById(
                            shipBill.getParentId(),
                            ShipBillStatusEnum.COMPLETE.getCode(),
                            ShipBillStatusEnum.DELIVERING.getCode(),
                            completeShipBillDTO.getOperationUserId());
                    isMainComplete = true;
                }
            }

            if (isMainComplete && CsStringUtils.equals(shipBill.getTransportToolType(), TransportToolTypeEnum.WATER_TRANSPORT.getCode())) {
                //修改船舶状态为吉船
                ShippingOperationDTO shippingOperationDTO = new ShippingOperationDTO();
                shippingOperationDTO.setShippingId(shipBill.getShippingId());
                shippingOperationDTO.setShippingStatus(1);
                shippingOperationDTO.setOperatorId(completeShipBillDTO.getOperationUserId());
                shippingOperationDTO.setOperatorName(completeShipBillDTO.getOperationUserName());
                shippingInfoBizService.updateShippingStatus(shippingOperationDTO);
            }

        } catch (DistributeLockException e) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, REPEAT_OPERATION);
        } finally {
            if (CsStringUtils.isNotBlank(identifier)) {
                redisLockService.unlock(bizResource, identifier);
            }
        }
    }

    private static void handleShipBillItem(List<ShipBillItem> shipBillItemList,
                                           ShipBill shipBill,
                                           List<ShipBillItem> needUpdateItemList) {
        for (ShipBillItem item : shipBillItemList) {
            String itemStatus = item.getStatus();
            String waybillItemId = item.getWaybillItemId();

            if (CsStringUtils.equals(itemStatus, ShipBillItemStatusEnum.COMPLETE.getCode())) {
                log.info("运单子项已完成:{}", waybillItemId);
                continue;
            }

            String proxyTypeName = BillProxyTypeEnum.parseToName(item.getBillProxyType());
            if (CsStringUtils.equals(proxyTypeName, BillProxyTypeEnum.NORMAL.getDesc())) {
                proxyTypeName = "";
            }
            //判断子项是否可以完成
            verifyShipBillAndItem(item, shipBill, itemStatus, waybillItemId, proxyTypeName);

            needUpdateItemList.add(item);

        }
    }

    private static void verifyShipBillAndItem(ShipBillItem item,
                                              ShipBill shipBill,
                                              String itemStatus,
                                              String waybillItemId,
                                              String proxyTypeName) {
        if (CsStringUtils.equals(shipBill.getTransportToolType(), TransportToolTypeEnum.ROAD_TRANSPORT.getCode())) {
            if (!CsStringUtils.equals(itemStatus, ShipBillItemStatusEnum.LEAVE_WAREHOUSE.getCode())) {
                log.info("汽运子项状态还不能完成:{}->{}", waybillItemId, itemStatus);
                throw new BizException(BasicCode.UNDEFINED_ERROR, "汽运" + proxyTypeName + "未出站,不能完成!");
            }
        } else {
            veryShipBillByRoadTransport(item, itemStatus, waybillItemId, proxyTypeName);
        }

        if (ShipBillItemStatusEnum.needSupple(itemStatus)) {
            log.info("运单子项待补款:{}", item);
            throw new BizException(BasicCode.UNDEFINED_ERROR, "运单" + proxyTypeName + "未补款,不能完成!");
        }
    }

    private static void veryShipBillByRoadTransport(ShipBillItem item, String itemStatus, String waybillItemId, String proxyTypeName) {
        if (CsStringUtils.equals(item.getType(), PickingBillTypeEnum.SELLER_DELIVERY.getCode())) {
            //卖家配送:
            if (!CsStringUtils.equals(itemStatus, ShipBillItemStatusEnum.OPEN_CABIN.getCode())) {
                log.info("船运卖家配送需要先开仓:{}", waybillItemId);
                throw new BizException(BasicCode.UNDEFINED_ERROR, "船运" + proxyTypeName + "未开仓,不能完成!");
            }
        } else {
            if (!CsStringUtils.equals(itemStatus, ShipBillItemStatusEnum.LEAVE_WAREHOUSE.getCode())) {
                log.info("船运自提或平台需要先出站:{}", waybillItemId);
                throw new BizException(BasicCode.UNDEFINED_ERROR, "船运" + proxyTypeName +"未出站,不能完成!");
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void completeConcrete(CompleteShipBillDTO completeShipBillDTO) {
        log.info("用户发起完成混凝土操作:{}", completeShipBillDTO);
        if (!CsStringUtils.equals(completeShipBillDTO.getSignType(), AdjustAddWayEnum.SELLER_SIGIN.getCode())) {
            throw new BizException(BasicCode.UN_SUPPORT, "非混凝土的完成");
        }

        String identifier = "";
        String bizResource = DigestUtils.md5Digest(completeShipBillDTO.toString());
        try {
            ShipBill shipBill = shipBillMapper.selectByPrimaryKey(completeShipBillDTO.getWaybillId());
            String oldStatus = shipBill.getStatus();
            //是否为卖家操作
            boolean sellerCompleteFlag =  LogisticsUtils.flagParse(completeShipBillDTO.getSellerDirectCompleteFlag());
            if (!sellerCompleteFlag) {
                //司机
                if (CsStringUtils.equals(oldStatus, ShipBillStatusEnum.COMPLETE.getCode())) {
                    log.info("运单已完成,跳过司机完成:{}", completeShipBillDTO);
                    return;
                }

                if (!CsStringUtils.equals(oldStatus, ShipBillStatusEnum.DELIVERING.getCode())) {
                    throw new BizException(BasicCode.UNDEFINED_ERROR, "运单当前状态不支持司机完成");
                }
                //司机改为已送达
                shipBillMapper.updateShipBillStatusById(
                        completeShipBillDTO.getWaybillId(),
                        ShipBillStatusEnum.SIGNED.getCode(),
                        oldStatus,
                        completeShipBillDTO.getOperationUserId());

            } else {
                //卖家点击
                //1. 到达卸货点时间在(出厂时间, 出厂时间+2天)
                //2. 卸货完成时间(完成时间)在(到达卸货点时间, 出厂时间+2天)

                Date leaveWarehouseTime = shipBill.getLeaveWarehouseTime();
                long leaveTwoDayMS = leaveWarehouseTime.getTime() + 2L * 24 * 60 * 60 * 1000;
                Date leaveTwoDayTime = new Date(leaveTwoDayMS);

                verifyStatusAndTime(completeShipBillDTO, leaveWarehouseTime, leaveTwoDayTime, oldStatus);

                List<ShipBillItem> shipBillItems = shipBillItemMapper.selectByWaybillIdAndSeq(completeShipBillDTO.getWaybillId(), shipBill.getMaxSeqNum());
                if (CollectionUtils.isEmpty(shipBillItems)) {
                    throw new BizException(BasicCode.UNDEFINED_ERROR, "运单信息不存在");
                }

                if (completeShipBillDTO.getSignQuantity() != null &&
                        completeShipBillDTO.getSignQuantity().compareTo(shipBill.getActualQuantity()) < 0 &&
                        CsStringUtils.isBlank(completeShipBillDTO.getSignRemark())) {
                    //签收量小于出厂量时,签收备注必填
                    throw new BizException(BasicCode.UNDEFINED_ERROR, "签收量小于出厂量,请补充签收备注!");
                }

                completeShipBillDTO.setWaybillOldStatus(oldStatus);

                signConcrete(completeShipBillDTO);

                //签收:

                ShipBillItem shipBillItem = shipBillItems.get(0);

                UpdateTakeInfoSignDTO updateTakeInfoSignDTO = new UpdateTakeInfoSignDTO();

                String deliveryInfoId = shipBillItem.getDeliveryInfoId();

                DeliveryInfoDTO deliveryInfoDTO = deliveryInfoBizService.queryByInfoId(deliveryInfoId);

                updateTakeInfoSignDTO.setTakeCode(deliveryInfoDTO.getTakeCode());
                updateTakeInfoSignDTO.setOperator(completeShipBillDTO.getOperationUserId());
                updateTakeInfoSignDTO.setAdjustAddWay(AdjustAddWayEnum.SELLER_SIGIN.getCode());

                UpdateTakeItemSignDTO updateTakeItemSignDTO = new UpdateTakeItemSignDTO();
                updateTakeItemSignDTO.setWaybillNum(shipBill.getWaybillNum() + "#" + shipBillItem.getWaybillItemId() + "#" + shipBill.getTransportToolType());
                updateTakeItemSignDTO.setEmptyLoadCost(LogisticsUtils.defaultIfNull(completeShipBillDTO.getEmptyLoadCharge(),BigDecimal.ZERO));
                updateTakeItemSignDTO.setResourceId(deliveryInfoDTO.getOrderItemId());
                updateTakeItemSignDTO.setSellerSignQuantity(completeShipBillDTO.getSignQuantity());
                updateTakeItemSignDTO.setAdjustQuantity(LogisticsUtils.subtract(completeShipBillDTO.getSignQuantity(), shipBill.getActualQuantity()));
                //获取提货单的实际发货总量
                BigDecimal actualSignQuantity = shipBillMapper.concreteTotalSignQuantity(deliveryInfoId);
                updateTakeItemSignDTO.setItemActualQuantity(shipBillMapper.concreteTotalSignQuantityByOrderItemId(deliveryInfoDTO.getOrderItemId()));
                updateTakeItemSignDTO.setTotalActualQuantity(actualSignQuantity);
                updateTakeInfoSignDTO.setUpdateTakeItemSignDTOs(Lists.newArrayList(updateTakeItemSignDTO));
                log.info("notifyTradeSignQuantity:" + JSON.toJSONString(updateTakeInfoSignDTO));

                doLogisticUpdateSignQuantity(updateTakeInfoSignDTO);

                //添加完成日志
                OperationRecordAddDTO operationRecordAddDTO = new OperationRecordAddDTO();
                operationRecordAddDTO.setEntryId(completeShipBillDTO.getWaybillId());
                operationRecordAddDTO.setType(OperationRecordTypeEnum.WAYBILL.getCode());
                operationRecordAddDTO.setOperationType(WaybillOperationTypeEnum.WAYBILL_COMPLETE.getCode());
                operationRecordAddDTO.setOperatorId(completeShipBillDTO.getOperationUserId());
                operationRecordAddDTO.setOperatorName(completeShipBillDTO.getOperationUserName());
                operationRecordAddDTO.setContent("已签收 实际签收量:" + completeShipBillDTO.getSignQuantity() + "立方");
                operationRecordBizService.saveOperationRecord(operationRecordAddDTO, completeShipBillDTO.getCompleteTime());

                deliveryBillBackBizService.addCompleteQuantityFromShipBill(
                        shipBillItem.getDeliveryBillId(),
                        shipBill.getActualQuantity(), shipBill.getAssignQuantity(),
                        completeShipBillDTO.getCompleteTime(),
                        completeShipBillDTO.getOperationUserId(), completeShipBillDTO.getOperationUserName());


            }

        } catch (DistributeLockException e) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, REPEAT_OPERATION);
        } finally {
            if (CsStringUtils.isNotBlank(identifier)) {
                redisLockService.unlock(bizResource, identifier);
            }
        }
    }

    private void signConcrete(CompleteShipBillDTO completeShipBillDTO) {
        int updateCount = shipBillMapper.signConcrete(completeShipBillDTO);
        if (updateCount <= 0) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "卖家签收混凝土失败");
        }
    }

    private static void verifyStatusAndTime(CompleteShipBillDTO completeShipBillDTO,
                                            Date leaveWarehouseTime,
                                            Date leaveTwoDayTime,
                                            String oldStatus) {
        if (completeShipBillDTO.getArriveDestinationTime().before(leaveWarehouseTime) ||
                completeShipBillDTO.getArriveDestinationTime().after(leaveTwoDayTime)) {
            throw new BizException(BasicCode.INVALID_PARAM, "到达卸货点时间应在出厂时间之后两天内");
        }

        if (completeShipBillDTO.getCompleteTime().before(completeShipBillDTO.getArriveDestinationTime()) ||
                completeShipBillDTO.getCompleteTime().after(leaveTwoDayTime)) {
            throw new BizException(BasicCode.INVALID_PARAM, "完成时间应在到达卸货点时间之后,出厂时间之后两天内");
        }

        if (!CsStringUtils.equals(oldStatus, ShipBillStatusEnum.DELIVERING.getCode()) &&
                !CsStringUtils.equals(oldStatus, ShipBillStatusEnum.SIGNED.getCode())) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "运单当前状态不支持卖家签收");
        }
    }

    private void doLogisticUpdateSignQuantity(UpdateTakeInfoSignDTO updateTakeInfoSignDTO) {
        ItemResult<Boolean> signResult = takeUpDataService.doLogisticUpdateSignQuantity(updateTakeInfoSignDTO);
        if (signResult == null || !signResult.isSuccess()) {
            log.error("交易签收失败: {}", JSON.toJSONString(signResult));
            throw new BizException(BasicCode.UNKNOWN_ERROR, "交易系统签收异常" );
        }
    }

    /**
     * 开仓船运
     * @param openCabinShipBillDTO
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public String openCabin(OpenCabinShipBillDTO openCabinShipBillDTO) {
        log.info("开仓操作:{}", openCabinShipBillDTO);
        verifyIdNotEmpty(openCabinShipBillDTO);
        String identifier = "";
        String bizResource = DigestUtils.md5Digest(openCabinShipBillDTO.getWaybillItemId());
        try {
            identifier = redisLockService.lockFast(bizResource);
            String waybillItemId = openCabinShipBillDTO.getWaybillItemId();
            ShipBill shipBill = shipBillMapper.selectByPrimaryKey(openCabinShipBillDTO.getWaybillId());
            ShipBillItem billItem = shipBillItemMapper.selectByPrimaryKey(waybillItemId);
            DeliveryInfoDTO deliveryInfoDTO = deliveryInfoBizService.queryByInfoId(billItem.getDeliveryInfoId());

            verifyShipBillAndItem(billItem, deliveryInfoDTO, shipBill);

            boolean needVerify = CsStringUtils.equals(billItem.getType(), PickingBillTypeEnum.SELLER_DELIVERY.getCode());
            //交易校验,不通过则失败

            List<String> oldStatusList = Lists.newArrayList(
                    ShipBillItemStatusEnum.LEAVE_WAREHOUSE.getCode(),
                    ShipBillItemStatusEnum.CABIN_WAIT_SUPPLEMENT.getCode()
            );

            if (needVerify) {
                OverSendOutVerifyDTO overSendOutVerifyDTO = new OverSendOutVerifyDTO();
                overSendOutVerifyDTO.setTakeCode(deliveryInfoDTO.getTakeCode());
                overSendOutVerifyDTO.setWaybillNum(shipBill.getWaybillNum() + "#" + waybillItemId + "#" + shipBill.getTransportToolType());
                overSendOutVerifyDTO.setOrderItemId(deliveryInfoDTO.getOrderItemId());
                overSendOutVerifyDTO.setQuantity(shipBill.getAssignQuantity());
                overSendOutVerifyDTO.setActualQuantity(shipBill.getActualQuantity());
                overSendOutVerifyDTO.setOperator(openCabinShipBillDTO.getOperationUserId());
                overSendOutVerifyDTO.setLogisticsAmount(billItem.getReceivableCarriagePrice()
                        .multiply(shipBill.getActualQuantity()));
                overSendOutVerifyDTO.setLogisticsPrice(billItem.getReceivableCarriagePrice());

                //如果是内部调拨，无需校验超发验证
                if (CsStringUtils.equals("01", shipBill.getInternal())) {
                    //如果同步标识为200，不是是二级单，且外部运单状态不为开仓成功 则通知erp开仓
                    if (CsStringUtils.equals(shipBill.getSyncFlag(), ExternalSyncFlagEnum.FORWARD_SYNC.getCode())
                            && !CsStringUtils.equals(shipBill.getExternalWaybillStatus(), WaybillExternalStatusEnum.OPEN_CABIN_SUCCESS.getCode())
                            && BillProxyTypeEnum.isFirstLevel(billItem.getBillProxyType())){
                        asyncNotifyOpenCabin(shipBill);

                        //更新shipBill的外部运单状态
                        ShipBill newShipBill = new ShipBill();
                        newShipBill.setWaybillId(shipBill.getWaybillId());
                        newShipBill.setExternalWaybillStatus(WaybillExternalStatusEnum.OPEN_CABIN_ING.getCode());
                        shipBillMapper.updateByPrimaryKeySelective(newShipBill);
                        return "ERP开仓中";
                    }
                } else {
                    //1.商品单价, 2.是否结算校验成功
                    ItemResult<OverSendOutVerifyResponseDTO> verifyResult = takeUpDataService.overSendOutVerify(overSendOutVerifyDTO);
                    log.info("开仓校验:{}=>{}", JSON.toJSONString(overSendOutVerifyDTO), JSON.toJSONString(verifyResult));
                    verifyResult(verifyResult);

                    OverSendOutVerifyResponseDTO verifyDTO = verifyResult.getData();

                    BigDecimal goodsPrice = verifyDTO.getPrice();
                    BigDecimal payQuantity = shipBill.getActualQuantity();
                    BigDecimal settlementAmount = ArithUtils.multiply(payQuantity, (LogisticsUtils.add(goodsPrice, billItem.getReceivableCarriagePrice())));

                    shipBillItemMapper.updateWaybillItemQuantity(
                            billItem.getWaybillItemId(),
                            payQuantity,
                            goodsPrice,
                            settlementAmount,
                            openCabinShipBillDTO.getOperationUserId());
                    String message = updateShipBill(openCabinShipBillDTO, verifyDTO, waybillItemId, oldStatusList, shipBill, billItem);
                    if(Objects.nonNull(message)) return message;
                }
            }

            //成功
            //转移货权
            shipBillMapper.normalChangeOwnerId(waybillItemId, openCabinShipBillDTO.getOperationUserId());
            //状态修改
            shipBillItemMapper.updateWaybillItemStatus(
                    waybillItemId,
                    ShipBillItemStatusEnum.OPEN_CABIN.getCode(),
                    oldStatusList,
                    openCabinShipBillDTO.getOperationUserId()
                    );
            //添加操作日志
            OperationRecordAddDTO firstRecordAddDTO = new OperationRecordAddDTO();
            firstRecordAddDTO.setEntryId(shipBill.getWaybillId());
            firstRecordAddDTO.setType(OperationRecordTypeEnum.WAYBILL.getCode());
            firstRecordAddDTO.setOperationType(WaybillOperationTypeEnum.VEHICLE_LEAVE.getCode());
            firstRecordAddDTO.setContent("开仓成功");
            firstRecordAddDTO.setCreateTime(new Date());
            firstRecordAddDTO.setOperatorId(openCabinShipBillDTO.getOperationUserId());
            firstRecordAddDTO.setOperatorName(openCabinShipBillDTO.getOperationUserName());
            operationRecordBizService.saveOperationRecord(firstRecordAddDTO);

            shipBillOperateBizService.itemSendQuantityToOrder(
                    shipBill.getWaybillNum(), billItem.getWaybillItemId(), shipBill.getTransportToolType(),
                    openCabinShipBillDTO.getOperationUserId(), deliveryInfoDTO);

            if (CsStringUtils.equals(billItem.getBillProxyType(), BillProxyTypeEnum.PRIMARY.getCode())) {
                //同步尝试开仓另一级
                OpenCabinShipBillDTO relationOpenCabinShipBillDTO = new OpenCabinShipBillDTO();
                BeanUtils.copyProperties(openCabinShipBillDTO, relationOpenCabinShipBillDTO);
                String relationItemId = shipBillItemMapper.selectItemIdByWaybillProxy(
                        openCabinShipBillDTO.getWaybillId(),
                        BillProxyTypeEnum.SECONDARY.getCode(),
                        shipBill.getMaxSeqNum());
                relationOpenCabinShipBillDTO.setWaybillItemId(relationItemId);
                proxySyncRecordBizService.syncOpenCabinWaybill(relationItemId, relationOpenCabinShipBillDTO);
            }

        } catch (DistributeLockException e) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, REPEAT_OPERATION);
        } finally {
            if (CsStringUtils.isNotBlank(identifier)) {
                redisLockService.unlock(bizResource, identifier);
            }
        }
        return "开仓成功";
    }

    private static void verifyResult(ItemResult<OverSendOutVerifyResponseDTO> verifyResult) {
        if (!verifyResult.isSuccess() || verifyResult.getData() == null) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "调用交易超发验证接口异常:" + verifyResult.getDescription());
        }
    }

    @Nullable
    private String updateShipBill(OpenCabinShipBillDTO openCabinShipBillDTO,
                                  OverSendOutVerifyResponseDTO verifyDTO,
                                  String waybillItemId,
                                  List<String> oldStatusList,
                                  ShipBill shipBill,
                                  ShipBillItem billItem) {
        if (BooleanUtils.isTrue(!verifyDTO.getCanOut())) {
            //状态修改
            shipBillItemMapper.updateWaybillItemStatus(
                    waybillItemId,
                    ShipBillItemStatusEnum.CABIN_WAIT_SUPPLEMENT.getCode(),
                    oldStatusList,
                    openCabinShipBillDTO.getOperationUserId()
            );
            return "预留资金不足,请补款后重试";
        }else {
            //如果同步标识为200，不是是二级单，且外部运单状态不为开仓成功 则通知erp开仓
            if (CsStringUtils.equals(shipBill.getSyncFlag(), ExternalSyncFlagEnum.FORWARD_SYNC.getCode())
                    && !CsStringUtils.equals(shipBill.getExternalWaybillStatus(), WaybillExternalStatusEnum.OPEN_CABIN_SUCCESS.getCode())
                    && BillProxyTypeEnum.isFirstLevel(billItem.getBillProxyType())){
                asyncNotifyOpenCabin(shipBill);

                //更新shipBill的外部运单状态
                ShipBill newShipBill = new ShipBill();
                newShipBill.setWaybillId(shipBill.getWaybillId());
                newShipBill.setExternalWaybillStatus(WaybillExternalStatusEnum.OPEN_CABIN_ING.getCode());
                shipBillMapper.updateByPrimaryKeySelective(newShipBill);
                return "ERP开仓中";
            }
        }
        return null;
    }

    private static void verifyIdNotEmpty(OpenCabinShipBillDTO openCabinShipBillDTO) {
        if (CsStringUtils.isEmpty(openCabinShipBillDTO.getWaybillId()) || CsStringUtils.isEmpty(openCabinShipBillDTO.getWaybillItemId())) {
            throw new BizException(BasicCode.INVALID_PARAM, "运单ID或运单子项ID不能为空" );
        }
    }

    private void asyncNotifyOpenCabin(ShipBill shipBill) {
        CompletableFuture.runAsync(() -> {
            try {
                shipBillExternalService.notifyOpenCabin(shipBill.getWaybillId());
            } catch (Exception e) {
                log.info("外部接口异常, {}", e);
            }
        });
    }

    private void verifyShipBillAndItem(ShipBillItem billItem, DeliveryInfoDTO deliveryInfoDTO, ShipBill shipBill) {
        if (CsStringUtils.equals(billItem.getBillProxyType(), BillProxyTypeEnum.PRIMARY.getCode()) ||
                CsStringUtils.equals(billItem.getBillProxyType(), BillProxyTypeEnum.NORMAL.getCode())) {
            //校验是否必须到达目的地才能开仓:
            boolean needArrival = shipBillOperateBizService.memberConfigFlag(deliveryInfoDTO.getSellerId(), LogisticsCommonBean.MEMBER_FLAG_NEED_SHIP_ARRIVAL, false);
            if (needArrival && shipBill.getArriveDestinationTime() == null) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "请等待船长到港后才能开仓!");
            }
        }

        if (!CsStringUtils.equals(deliveryInfoDTO.getSellerId(), shipBill.getOwnerId())) {
            //货权已经转移,此级不需要开仓操作
            throw new BizException(BasicCode.UNDEFINED_ERROR, "此级运单已开仓,请勿重复操作");
        }
    }

    /**
     * 完成混凝土
     * @param completeShipBillDTO
     */
    @Override
    public void completeVehicleConcrete(CompleteShipBillDTO completeShipBillDTO) {
        //卖家完成: 配送中 -> 已完成 / 已签收 -> 已完成
        //非卖家完成: 配送中 -> 已签收
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void refundShipBill(RefundShipBillDTO refundShipBillDTO) {
        //运单退货-->运单状态判断是否可退货-->卖家是否有权退货-->一二级运单退货状态修改-->检查配送单是否可以完成关闭-->通知交易
        log.info("运单退货操作:{}", refundShipBillDTO);
        verifyIdNotNull(refundShipBillDTO);
        boolean isMainComplete = false;

        String identifier = "";
        String bizResource = DigestUtils.md5Digest(refundShipBillDTO.toString());
        try {
            identifier = redisLockService.lockFast(bizResource);
            //出站后的状态才可以退货
            List<String> waybillStatus = Lists.newArrayList(
                    ShipBillStatusEnum.DELIVERING.getCode(),
                    ShipBillStatusEnum.SIGNED.getCode(),
                    ShipBillStatusEnum.COMPLETE.getCode());

            ShipBill shipBill = shipBillMapper.selectByPrimaryKey(refundShipBillDTO.getWaybillId());
            verifyShipBill(shipBill, waybillStatus);

            ShipBillItem billItem = shipBillItemMapper.selectByPrimaryKey(refundShipBillDTO.getWaybillItemId());
            if (billItem == null ) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "运单子项不存在！" );
            }
            DeliveryInfoDTO deliveryInfoDTO = deliveryInfoBizService.queryByInfoId(billItem.getDeliveryInfoId());
            verifyDeliveryInfo(refundShipBillDTO, deliveryInfoDTO);

            //退货数量
            BigDecimal refundQuantity = shipBill.getActualQuantity();

            String refundReasonCode = refundShipBillDTO.getRefundReason();
            //运单退货, 添加操作日志
            refundShipBillDTO.setRefundQuantity(refundQuantity);
            refundShipBillDTO.setStatus(ShipBillStatusEnum.REFUND.getCode());
            refundShipBillDTO.setRefundStatus(RefundStatusEnum.APPLIED.getCode());
            refundShipBillDTO.setRefundReason(RefundReasonEnum.valueOfCode(refundReasonCode).getValue());
            refundShipBillDTO.setCompleteTime(new Date());
            //运单退货
            shipBillMapper.refundShipBill(refundShipBillDTO);

            if (!LogisticsUtils.flagParse(shipBill.getChildFlag())) {
                isMainComplete = true;
            }
            log.info("运单退货处理:{}_{}", shipBill.getWaybillId(), shipBill.getStatus());
            OperationRecordAddDTO operationRecordAddDTO = new OperationRecordAddDTO();
            operationRecordAddDTO.setEntryId(shipBill.getWaybillId());
            operationRecordAddDTO.setType(OperationRecordTypeEnum.WAYBILL.getCode());
            operationRecordAddDTO.setOperationType(WaybillOperationTypeEnum.WAYBILL_REFUND.getCode());
            operationRecordAddDTO.setContent("运单退货");
            //退货时间
            operationRecordAddDTO.setCreateTime(new Date());
            operationRecordAddDTO.setOperatorId(refundShipBillDTO.getOperationUserId());
            operationRecordAddDTO.setOperatorName(refundShipBillDTO.getOperationUserName());
            operationRecordBizService.saveOperationRecord(operationRecordAddDTO);

            List<ShipBillItem> shipBillItemList = shipBillItemMapper.selectByWaybillIdAndSeq(shipBill.getWaybillId(), shipBill.getMaxSeqNum());

            //判断需要完成的子项
            for (ShipBillItem item : shipBillItemList) {
                String itemStatus = item.getStatus();
                String waybillItemNum = item.getWaybillNum();

                if (waybillStatus.contains(itemStatus)) {
                    log.info("运单子项状态不符合退货操作:{}_{}", waybillItemNum, ShipBillStatusEnum.valueOfCode(itemStatus).getDesc());
                    continue;
                }
                log.info("运单子项状态改为已退货:{}-{}", waybillItemNum, ShipBillItemStatusEnum.REFUND.getCode());
                int updateItemCount = shipBillItemMapper.updateWaybillItemStatusByNum(
                        waybillItemNum,
                        ShipBillItemStatusEnum.REFUND.getCode(),
                        Lists.newArrayList(itemStatus),
                        refundShipBillDTO.getOperationUserId());

                if (updateItemCount > 0) {
                    //1.修改运单已完成数量（包含已退货数量），2.修改运单已退货数量
                    deliveryBillBackBizService.addCompleteQuantityFromShipBill(
                            item.getDeliveryBillId(),
                            shipBill.getActualQuantity(), shipBill.getAssignQuantity(),
                            new Date(),
                            refundShipBillDTO.getOperationUserId(), refundShipBillDTO.getOperationUserName());
                }
            }

            /**
             * 订单一共有四种场景：
             * 1、终端买家-->经销商（ExternalSyncFlagEnum.NO_SYNC）
             * 2、终端买家-->经销商-->电商厂家（ExternalSyncFlagEnum.NO_SYNC）
             * 3、终端买家-->ERP厂家（正向或方向同步）
             * 4、终端买家-->经销商-->ERP厂家（正向或方向同步）
             * 1、2不需要同步到ERP系统
             */
            //如果是未同步的，说明是纯电商单据，不需要ERP处理
            if (CsStringUtils.equals(ExternalSyncFlagEnum.NO_SYNC.getCode(), deliveryInfoDTO.getSyncFlag())) {
                //如果是普通订单则直接退货
                RefundShipBillDTO refundDTO = new RefundShipBillDTO();
                refundDTO.setWaybillId(shipBill.getWaybillId());
                refundDTO.setOperationUserId(refundShipBillDTO.getOperationUserId());
                refundDTO.setRefundStatus(RefundStatusEnum.REFUND_COMPLETED.getCode());
                refundDTO.setRefundReason(refundShipBillDTO.getRefundReason());
                refundDTO.setRefundRemark(refundShipBillDTO.getRefundRemark());
                //运单退货
                shipBillMapper.refundShipBill(refundDTO);

                if (isMainComplete) {

                    List<DeliveryInfoProxyDTO> deliveryInfoList = deliveryInfoBizService.queryProxyInfoList(deliveryInfoDTO.getParentInfoId());
                    verifyIsEmpty(deliveryInfoList, deliveryInfoDTO);

                    doLogisticRefund(refundShipBillDTO, deliveryInfoList, shipBill, refundDTO);
                }
            } else if (CsStringUtils.equals(ExternalSyncFlagEnum.FORWARD_SYNC.getCode(), deliveryInfoDTO.getSyncFlag())
                    || CsStringUtils.equals(ExternalSyncFlagEnum.BACKWARD_SYNC.getCode(), deliveryInfoDTO.getSyncFlag())) {
                waybillNewExternalService.externalWaybillRefund(refundShipBillDTO, shipBill);
            }
        } catch (DistributeLockException e) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, REPEAT_OPERATION);
        } finally {
            if (CsStringUtils.isNotBlank(identifier)) {
                redisLockService.unlock(bizResource, identifier);
            }
        }
    }

    private static void verifyDeliveryInfo(RefundShipBillDTO refundShipBillDTO, DeliveryInfoDTO deliveryInfoDTO) {
        if (deliveryInfoDTO == null ) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "运单配送信息不存在！" );
        }
        if (CsStringUtils.equals(deliveryInfoDTO.getBillProxyType(), BillProxyTypeEnum.SECONDARY.getCode())
                || !CsStringUtils.equals(deliveryInfoDTO.getSellerId(), refundShipBillDTO.getMemberId())) {
            //背靠背二级运单不能退货/运单不是当前会员的，用户没有权限操作该运单
            throw new BizException(BasicCode.UNDEFINED_ERROR, "您无权操作该运单！");
        }
    }

    private static void verifyShipBill(ShipBill shipBill, List<String> waybillStatus) {
        if (shipBill == null ) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "运单不存在！" );
        }
        if (!CsStringUtils.equals(shipBill.getTransportToolType(), TransportToolTypeEnum.ROAD_TRANSPORT.getCode())) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "当前仅支持汽运退货！" );
        }
        if (!waybillStatus.contains(shipBill.getStatus())) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "运单状态不支持退货操作！" );
        }
    }

    private static void verifyIdNotNull(RefundShipBillDTO refundShipBillDTO) {
        if (CsStringUtils.isEmpty(refundShipBillDTO.getWaybillId()) || CsStringUtils.isEmpty(refundShipBillDTO.getWaybillItemId())) {
            throw new BizException(BasicCode.INVALID_PARAM, "运单ID或运单子项ID不能为空" );
        }
    }

    private static void verifyIsEmpty(List<DeliveryInfoProxyDTO> deliveryInfoList, DeliveryInfoDTO deliveryInfoDTO) {
        if (CollectionUtils.isEmpty(deliveryInfoList)) {
            log.error("运单退货异常，找不到配送信息：{}", deliveryInfoDTO.getParentInfoId());
            throw new BizException(BasicCode.UNDEFINED_ERROR, "运单退货异常，找不到配送信息！");
        }
    }

    private void doLogisticRefund(RefundShipBillDTO refundShipBillDTO,
                                  List<DeliveryInfoProxyDTO> deliveryInfoList,
                                  ShipBill shipBill,
                                  RefundShipBillDTO refundDTO) {
        try {
            //通知交易可以完成订单了
            ShipBillRefundDTO shipBillRefundDTO = handleShipBillRefundDTO(deliveryInfoList, shipBill.getActualQuantity(), refundShipBillDTO.getRefundReason());
            log.info("交易shipBillRefundDTO:{}", shipBillRefundDTO);
            ItemResult<Boolean> itemResult = takeUpDataService.doLogisticRefund(shipBillRefundDTO);
            if (itemResult.isSuccess()) {
                refundDTO.setWaybillNum(shipBill.getWaybillNum());
                refundDTO.setTakeCode(shipBillRefundDTO.getTakeCode());
                //同一个运单的goodsId是一样的
                refundDTO.setGoodsId(deliveryInfoList.get(0).getGoodsId());
                //退货时间
                refundDTO.setRefundTime(LogisticsDateUtils.getStandardTime(new Date()));
                log.info("电商退货处理完毕通知对账：{}", refundDTO);
                messageQueueService.sendMQ(refundDTO, LogisticsMessageTypeEnum.REFUND_NOTIFY_RECONCILIATION.getCode());
            } else {
                // 添加到异常列表,重试处理
                log.error("运单退货异常:{}_{}",itemResult.getCode(), itemResult.getDescription());
                throw new BizException(BasicCode.UNDEFINED_ERROR, "运单退货异常！");
            }
        } catch (Exception e) {
            log.error("运单退货异常:{}", e);
            throw new BizException(BasicCode.UNDEFINED_ERROR, "运单退货异常！");
        }
    }

    private ShipBillRefundDTO handleShipBillRefundDTO(List<DeliveryInfoProxyDTO> deliveryInfoList, BigDecimal refundQuantity, String refundReason) {

        ShipBillRefundDTO shipBillRefundDTO = new ShipBillRefundDTO();
        ShipBillRefundDTO secondRefundDTO = new ShipBillRefundDTO();

        deliveryInfoList.forEach(item -> {
            //如果不是二级单据则不需要传子单据信息
            if (!CsStringUtils.equals(item.getBillProxyType(), BillProxyTypeEnum.SECONDARY.getCode())) {
                //发货单号
                shipBillRefundDTO.setTakeCode(item.getTakeCode());
                //订单明细项ID
                shipBillRefundDTO.setOrderItemId(item.getOrderItemId());
                //退货数量
                shipBillRefundDTO.setRefundQuantity(refundQuantity);
                if (refundQuantity == null || refundQuantity.compareTo(BigDecimal.ZERO) == 0) {
                    //退货金额（商品费用）
                    shipBillRefundDTO.setRefundGoodsAmount(BigDecimal.ZERO);
                } else {
                    //退货金额（商品费用），退货金额 = 商品数量*商品单价,使用原始价格(合同可能调价)
                    shipBillRefundDTO.setRefundGoodsAmount(refundQuantity.multiply(item.getOriginPrice()));
                }
                //退货金额（物流总费用）
                shipBillRefundDTO.setRefundLogisticsAmount(item.getReceivableCarriagePrice());
                shipBillRefundDTO.setRefundReason(refundReason);
            } else if (CsStringUtils.equals(item.getBillProxyType(), BillProxyTypeEnum.SECONDARY.getCode())) {
                //发货单号
                secondRefundDTO.setTakeCode(item.getTakeCode());
                //订单明细项ID
                secondRefundDTO.setOrderItemId(item.getOrderItemId());
                //退货数量
                secondRefundDTO.setRefundQuantity(refundQuantity);
                if (refundQuantity == null || refundQuantity.compareTo(BigDecimal.ZERO) == 0) {
                    //退货金额（商品费用）
                    secondRefundDTO.setRefundGoodsAmount(BigDecimal.ZERO);
                } else {
                    //退货金额（商品费用），退货金额 = 商品数量*商品单价,使用原始价格(合同可能调价)
                    secondRefundDTO.setRefundGoodsAmount(refundQuantity.multiply(item.getOriginPrice()));
                }
                //退货金额（物流总费用）
                secondRefundDTO.setRefundLogisticsAmount(item.getReceivableCarriagePrice());
                secondRefundDTO.setRefundReason(refundReason);
            }
        });
        shipBillRefundDTO.setSecondRefundDTO(secondRefundDTO);
        return shipBillRefundDTO;
    }

}
