package com.ecommerce.logistics.biz.message;

/**
 * @Author: <EMAIL>
 * @Description:
 * @Date: Create in 下午4:44 18/8/30
 */
public interface IMessageBizService {
    /**
     * 发送消息
     * @param data
     * @return
     */
    <T> void sendMQ(T data, String routeKey);

    /**
     * 发送消息
     * @param data
     * @param routeKey
     * @param exchange
     * @param <T>
     * @throws Exception
     */
    <T> void sendMQWithExchange(T data, String routeKey, String exchange);

    /**
     * 无视环境变量发送消息队列
     * @param data
     * @param routeKey
     * @param <T>
     * @throws Exception
     */
    <T> void sendMQWithoutProfile(T data, String routeKey);

    /**
     * 发送消息
     * @param data
     * @return
     */
    <T> void sendSimpleMQ(T data, String routeKey);
}
