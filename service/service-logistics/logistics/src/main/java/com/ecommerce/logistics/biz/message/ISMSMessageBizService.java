package com.ecommerce.logistics.biz.message;

import com.ecommerce.logistics.biz.message.dto.AssignDispatcherSMSDTO;
import com.ecommerce.logistics.biz.message.dto.DeliveryWaybillSMSDTO;
import com.ecommerce.logistics.biz.message.dto.NoticeMaterialSMSDTO;
import com.ecommerce.logistics.biz.message.dto.SnatchWaybillSMSDTO;
import com.ecommerce.logistics.biz.message.dto.StoreWaitDeliverySMSDTO;
import com.ecommerce.logistics.biz.message.dto.WaybillCreatedSMSDTO;

import java.util.List;

/**
 * 短信发送
 * @Auther: <EMAIL>
 * @Date: 11/10/2018 15:31
 * @Description: ISMSMessageBizService
 */
public interface ISMSMessageBizService {

    /**
     * 客户确认收货
     * @param waybillNum : 运单号
     * @param notifyAccountIdList 通知人: 运单对应的买家下单人员、平台物流人员 [不发]
     */
    @Deprecated(since = "2.1.4-RELEASE")
    void confirmReceiptSMS(String waybillNum, List<String> notifyAccountIdList);

    /**
     * 车辆出厂
     * @param waybillNum : 运单号
     * @param notifyAccountIdList 通知人: 运单对应的买家下单人员、平台物流人员 [不发]
     */
    @Deprecated(since = "2.1.4-RELEASE")
    void leaveWarehouseSMS(String waybillNum, List<String> notifyAccountIdList);

    /**
     * 配送运单
     * @param deliveryWaybillSMSDTO
     * 通知人: 运单对应的买家下单人员、平台物流人员 [发给承运商]
     */
    void deliveryWaybillSMS(DeliveryWaybillSMSDTO deliveryWaybillSMSDTO);

    /**
     * 抢单
     * @param snatchWaybillSMSDTO
     * 通知人: 抢单成功账号主体: [抢单的用户(承运商或者司机)]
     */
    void snatchWaybillSMS(SnatchWaybillSMSDTO snatchWaybillSMSDTO);

    /**
     * 门店待配送
     * @param storeWaitDeliverySMSDTO
     */
    void storeWaitDelivery(StoreWaitDeliverySMSDTO storeWaitDeliverySMSDTO);

    /**
     * 生成提货单并通知调度员
     * @param provinceCode
     * @param cityCode
     */
    void dispatchNoticeSMS(String provinceCode, String cityCode);

    /**
     * 指派提货单后通知调度人员
     * @param assignDispatcherSMSDTO
     */
    void assignDispatcherSMS(AssignDispatcherSMSDTO assignDispatcherSMSDTO);

    /**
     * 承运商指派车辆后通知材料员
     * @param noticeMaterialSMSDTO
     */
    void firstNoticeMaterial(NoticeMaterialSMSDTO noticeMaterialSMSDTO);

    /**
     * 承运商重新指派车辆后通知材料员
     * @param noticeMaterialSMSDTO
     */
    void secondNoticeMaterial(NoticeMaterialSMSDTO noticeMaterialSMSDTO);

    /**
     * 车辆审核通过
     * @param driverPhone
     */
    void vehicleAuthOkSMS(String driverPhone);

    /**
     * 车辆认证失败
     * @param driverPhone
     * @param failReason
     */
    void vehicleAuthFailSMS(String driverPhone, String failReason);
    /**
     * 承运商指派司机消息推送
     */
    void pushDeviceDriverAssign(String waybillNum, String waybillId, String accountId);

    /**
     * 推送抢单信息
     * @param waybillNum
     * @param accountIdList
     */
    void pushSocialSnatch(String waybillNum, String waybillId, List<String> accountIdList);

    // 提货单接收(卖家 => 平台)
    void sendSellerConfirmTakeInfo(String pickingBillId);

    // 调度单接收(平台管理员 => 平台承运商)
    void sendPlatformAssign(String dispatchBillId, String carrierId, String quantity);

    // 调度单取消(平台管理员 => 平台承运商)
    void sendPlatformAssignCancel(String dispatchBillId);

    // 运单接收(承运商 => 平台承运商司机)
    void sendWaybillAssign(String dispatchBillId, String driverId);

    // 运单确认(司机 => 平台承运商)
    void sendWaybillConfirm(String waybillId);

    // 运单取消(承运商 => 平台承运商司机)
    void sendWaybillPlatformCancel(String waybillId);

    // 运单取消(司机 => 平台承运商)
    void sendWaybillDriverCancel(String waybillId);

    // 车辆出厂(卖家 => 买家)
    void sendVehicleFactoryBuyer(String waybillId);

    // 车辆出厂(卖家 => 平台承运商)
    void sendVehicleFactoryCarrier(String waybillId);

    // 运单完成(司机 => 平台承运商)
    void sendWaybillDriverCompleted(String waybillId);

    // 运单完成(承运商或司机 => 买家)
    void sendWaybillCarrierCompleted(String waybillId);

    /**
     * 运单创建时,通知承运的司机(只针对卖家配送和买家自提)
     * @param waybillId
     */
    void createWaybillNotifyDriver(String waybillId);

    /**
     * 运单创建时,通知承运的司机(只针对卖家配送和买家自提)
     * @param waybillCreatedSMSDTO
     */
    void createWaybillNotifyDriver(WaybillCreatedSMSDTO waybillCreatedSMSDTO);

    /**
     * 船运单开仓提醒
     * @param waybillItemId
     */
    void openCabinRemind(String waybillItemId);

    /**
     * 船运单卸货提醒
     * @param waybillItemId
     */
    void unloadingRemind(String waybillItemId);
}
