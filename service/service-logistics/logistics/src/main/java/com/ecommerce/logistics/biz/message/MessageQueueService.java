package com.ecommerce.logistics.biz.message;

import com.ecommerce.mq.core.MQMessage;
import com.ecommerce.mq.core.service.IMQProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * Author: <EMAIL>
 * Description:
 * Date: Create in 下午7:51 18/8/30
 */
@Service("messageBizService")
@Slf4j
public class MessageQueueService implements IMessageBizService {

    @Value("${rabbitmq.profile}")
    private String profile;

    @Autowired
    private IMQProducer producer;

    @Override
    public <T> void sendMQ(T data, String routeKey) {
        log.info(data.toString());
        MQMessage<T> message = new MQMessage<>();
        message.setData(data);
        message.setKey(routeKey + '.' + profile);
        try {
            producer.send(message);
        } catch (Exception e) {
            log.error("sendMessageError:[{}]",routeKey,e);
        }
    }

    @Override
    public <T> void sendMQWithExchange(T data, String routeKey, String exchange){
        log.info("sendMQWithExchange:::{}", data.toString());
        MQMessage<T> message = new MQMessage<>();
        message.setData(data);
        message.setKey(routeKey + '.' + profile);
        message.setExchange(exchange);
        try {
            producer.send(message);
        } catch (Exception e) {
            log.error("sendMessageError:[{}], [{}]", routeKey, exchange, e);
        }
    }

    @Override
    public <T> void sendMQWithoutProfile(T data, String routeKey){
        log.info(data.toString());
        MQMessage<T> message = new MQMessage<>();
        message.setData(data);
        message.setKey(routeKey);
        try {
            producer.send(message);
        } catch (Exception e) {
            log.error("sendMessageError:[" + routeKey + "]" + e.toString());
        }
    }

    @Override
    public <T> void sendSimpleMQ(T data, String routeKey) {
        log.info(data.toString());
        MQMessage<T> message = new MQMessage<>();
        message.setExchange("erp_exchange");
        message.setData(data);
        message.setKey(routeKey + '.' + profile);
        try {
            producer.send(message);
        } catch (Exception e) {
            log.error("sendMessageError:[" + routeKey + "]" + e.toString());
        }
    }
}
