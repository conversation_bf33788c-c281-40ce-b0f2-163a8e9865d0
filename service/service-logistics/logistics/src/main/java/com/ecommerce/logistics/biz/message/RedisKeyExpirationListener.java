package com.ecommerce.logistics.biz.message;

import com.ecommerce.common.exception.DistributeLockException;
import com.ecommerce.common.service.ILockService;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.logistics.api.dto.externalexception.ExternalExceptionRetryDTO;
import com.ecommerce.logistics.service.IExternalExceptionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.listener.KeyExpirationEventMessageListener;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.stereotype.Component;

/**
 * Author: lehu
 * Description:
 * Date: Create in 下午8:57 20/11/4
 */
@Slf4j
@Component
public class RedisKeyExpirationListener extends KeyExpirationEventMessageListener {

    public static final String ERP_EXCEPTION = "ERP_EXCEPTION_WAYBILL";

    @Autowired
    private IExternalExceptionService externalExceptionService;

    @Autowired
    private ILockService redisLockService;

    public RedisKeyExpirationListener(@Qualifier("expiredRedisMessageListenerContainer") RedisMessageListenerContainer listenerContainer) {
        super(listenerContainer);
    }

    @Override
    public void onMessage(Message message, byte[] pattern) {
        //此处业务处理即可,注意message.toString()可以获取失效的key
        String expiredKey = message.toString();
        if (expiredKey.startsWith(ERP_EXCEPTION)) {
            String identifier = null;
            try {
                identifier = redisLockService.lockFast(expiredKey);
                log.info("发起异常重试：" + expiredKey);
                String[] keyStr = CsStringUtils.split(expiredKey, ":");
                if (keyStr != null && keyStr.length > 1) {
                    ExternalExceptionRetryDTO externalExceptionRetryDTO = new ExternalExceptionRetryDTO();
                    externalExceptionRetryDTO.setExternalExceptionHandleId(keyStr[1]);
                    externalExceptionRetryDTO.setOperatorUserId("system");
                    externalExceptionRetryDTO.setOperatorUserName("system");
                    externalExceptionService.externalExceptionRetryHandler(externalExceptionRetryDTO);
                }
            } catch (DistributeLockException e) {
                log.info("自动异常重试已在另一台负载发起,此次忽略:{},{}", expiredKey, e.getMessage());
            } finally {
                if (CsStringUtils.isNotBlank(identifier)) {
                    redisLockService.unlock(expiredKey, identifier);
                }
            }
        }
    }
}
