package com.ecommerce.logistics.biz.message.dto;

import com.google.common.collect.Maps;
import lombok.Data;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 指派提货单后通知调度人员，详情如下：
   调度单号：{dispatchNumber}
   出货点：{warehouseAddress}
   收货地址：{receiverAddress}
   期望收货时间：{deliveryTime}
   联系电话：{mobile}
 * 待配送短信通知实体
 * @Auther: <EMAIL>
 * @Date: 06/09/2019 11:22
 * @Description: AssignDispatcherSMSDTO
 */
@Data
public class AssignDispatcherSMSDTO {

    private String sign = "畅行物流";

    /**
     * 收信人手机号码列表
     */
    List<String> storeCarrierMobile;

    /**
     * 调度单号
     */
    private String dispatchNumber;

    /**
     * 出货点
     */
    private String warehouseAddress;

    /**
     * 收货地址
     */
    private String receiverAddress;

    /**
     * 联系电话
     */
    private String mobile;

    /**
     * 期望收货时间
     */
    private Date deliveryTime;

    public Map<String, String> templateParams() {
        Map<String, String> smsParams = Maps.newHashMap();
        smsParams.put("sign", sign);
        smsParams.put("dispatchNumber", dispatchNumber);
        smsParams.put("warehouseAddress", warehouseAddress);
        smsParams.put("receiverAddress", receiverAddress);
        smsParams.put("deliveryTime", new SimpleDateFormat("yyyy-MM-dd").format(deliveryTime));
        smsParams.put("mobile", mobile);
        return smsParams;
    }

}
