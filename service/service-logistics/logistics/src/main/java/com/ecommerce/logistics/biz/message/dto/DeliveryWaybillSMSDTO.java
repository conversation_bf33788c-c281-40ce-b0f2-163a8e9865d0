package com.ecommerce.logistics.biz.message.dto;

import com.google.common.collect.Maps;
import lombok.Data;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 配单成功短信DTO
 * matchSuccessTime / waybillNumber / orderDriverName / plateNumber / contactNumber
 * @Auther: <EMAIL>
 * @Date: 11/10/2018 20:39
 * @Description: DeliveryWaybillSMSDTO
 */
@Data
public class DeliveryWaybillSMSDTO {

    private String sign = "畅行物流";

    /**
     * 收信人ID列表
     */
    List<String> notifyAccountIdList;

    /**
     * 匹配时间
     */
    private Date matchSuccessTime;

    /**
     * 运单号
     */
    private String waybillNumber;

    /**
     * 司机名
     */
    private String driverName;

    /**
     * 车牌号
     */
    private String plateNumber;

    /**
     * 司机联系电话
     */
    private String driverPhone;

    public Map<String, String> templateParams() {
        Map<String, String> smsParams = Maps.newHashMap();
        smsParams.put("sign", sign);
        smsParams.put("matchSuccessTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(matchSuccessTime));
        smsParams.put("waybillNumber", waybillNumber);
        smsParams.put("orderDriverName", driverName);
        smsParams.put("plateNumber", plateNumber);
        smsParams.put("contactNumber", driverPhone);
        return smsParams;
    }

}
