package com.ecommerce.logistics.biz.message.dto;

import com.ecommerce.common.utils.CsStringUtils;
import com.google.common.collect.Maps;
import lombok.Data;

import java.util.Map;

/**
 * 通知材料员
 * @Auther: <EMAIL>
 * @Date: 29/10/2018 18:31
 * @Description: NoticeMaterialSMSDTO
 */
@Data
public class NoticeMaterialSMSDTO {

    private String sign = "畅行物流";

    //收货人手机号
    private String receivePhone;

    //运单号
    private String waybillNumber;

    //原车牌号
    private String numberPlate;

    //新车牌号
    private String numberPlate2;


    public Map<String, String> templateParams() {
        Map<String, String> smsParams = Maps.newHashMap();
        smsParams.put("sign", sign);
        smsParams.put("waybillNumber", waybillNumber);
        smsParams.put("numberPlate", numberPlate);
        if (CsStringUtils.isNotBlank(numberPlate2)) {
            smsParams.put("numberPlate2", numberPlate2);
        }
        return smsParams;
    }

}
