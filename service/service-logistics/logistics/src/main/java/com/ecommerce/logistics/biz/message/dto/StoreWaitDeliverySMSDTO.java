package com.ecommerce.logistics.biz.message.dto;

import com.google.common.collect.Maps;
import lombok.Data;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

/**
 * 您有一笔新订单待配送，详情如下：
   运单号：{waybillNumber}
   商品：{goodsInfo}
   收货地址：{recipientAddress}
   收货人电话：{consigneeCall}
   配送时间：{deliveryTime}
   请按要求安排配送。如有疑问，请联系平台客服（{consumerHotline}，工作时间：09:00-17:00）。
 * 待配送短信通知实体
 * @Auther: <EMAIL>
 * @Date: 25/10/2018 19:20
 * @Description: StoreWaitDeliverySMSDTO
 */
@Data
public class StoreWaitDeliverySMSDTO {

    private String sign = "畅行物流";

    /**
     * 收信人手机号码列表
     */
    String storeCarrierMobile;

    /**
     * 运单号
     */
    private String waybillNum;

    /**
     * 商品名称
     */
    private String goodsInfo;

    /**
     * 卸货点地址
     */
    private String unloadPointAddress;

    /**
     * 收货人电话
     */
    private String unloadPhone;

    /**
     * 配送时间
     */
    private Date deliveryTime;

    public Map<String, String> templateParams() {
        Map<String, String> smsParams = Maps.newHashMap();
        smsParams.put("sign", sign);
        smsParams.put("waybillNumber", waybillNum);
        smsParams.put("goodsInfo", goodsInfo);
        smsParams.put("recipientAddress", unloadPointAddress);
        smsParams.put("consigneeCall", unloadPhone);
        smsParams.put("deliveryTime", new SimpleDateFormat("yyyy-MM-dd").format(deliveryTime));
        return smsParams;
    }

}
