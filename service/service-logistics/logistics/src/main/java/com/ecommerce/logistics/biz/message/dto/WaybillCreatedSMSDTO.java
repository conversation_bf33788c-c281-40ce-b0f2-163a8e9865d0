package com.ecommerce.logistics.biz.message.dto;

import com.google.common.collect.Maps;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 您有新的运单，请及时提货。
 * 运单号：{waybillCode}；
 * 出货点：{exportName}；
 * 商品：{productName}；
 * 运单数量：{number}吨；
 * 买家：{buyerName}；
 * 收货地址：{receiverAddress}
 *
 * @Auther: colu
 * @Date: 2020-06-17 15:31
 * @Description: WaybillCreatedSMSDTO
 */
@Data
@ApiModel("运单生成时通知实体")
public class WaybillCreatedSMSDTO {

    @ApiModelProperty("运单号")
    private String waybillNum;

    @ApiModelProperty("出货点ID")
    private String warehouseId;

    @ApiModelProperty("司机ID")
    private String driverId;

    @ApiModelProperty("司机电话")
    private String driverPhone;

    @ApiModelProperty("出货点")
    private String warehouseName;

    @ApiModelProperty("商品名")
    private String productName;

    @ApiModelProperty("数量")
    private BigDecimal quantity;

    @ApiModelProperty("买家")
    private String buyerName;

    @ApiModelProperty("收货地址")
    private String receiverAddress;


    public Map<String, String> templateParams() {
        Map<String, String> smsParams = Maps.newHashMap();
        smsParams.put("waybillCode", waybillNum);
        smsParams.put("exportName", warehouseName);
        smsParams.put("productName", productName);
        smsParams.put("number", quantity == null ? "0.00" : quantity.toString());
        smsParams.put("buyerName", buyerName);
        smsParams.put("receiverAddress", receiverAddress);
        return smsParams;
    }

}
