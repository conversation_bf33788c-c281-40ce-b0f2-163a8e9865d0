package com.ecommerce.logistics.config;

import com.ecommerce.logistics.LogisticsCommonBean;
import com.ecommerce.logistics.biz.delayed.TaskConsumer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * @Auther: colu
 * @Date: 2020-11-30 11:02
 * @Description: LgsContainerListener
 */
@Slf4j
@Service("lgsContainerListener")
public class LgsContainerListener implements ApplicationRunner, DisposableBean {

    @Resource(name = "bizRedisTemplate")
    private RedisTemplate bizRedisTemplate;

    //运单单一报警延时队列:

    /**
     * 线程
     */
    private Thread uwtThread = null;

    /**
     * 消费者
     */
    private TaskConsumer uwtTaskConsumer = null;

    //运单同步erp失败之后重试延时队列:

    private Thread retryErpThread = null;

    private TaskConsumer retryErpTaskConsumer = null;

    @Override
    public void destroy() throws Exception {

        uwtTaskConsumer.setSignal(false);
        uwtThread.interrupt();

    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("启动完毕,开始初始化单一预警延时队列");
        uwtTaskConsumer = new TaskConsumer(bizRedisTemplate, LogisticsCommonBean.UNI_WARN_DELAYED_QUEUE, "uniWarnTask");
        uwtThread = new Thread(uwtTaskConsumer);
        uwtThread.start();
        log.info("单一预警延时队列初始化完毕");
        log.info("开始初始化erp异常重试延时队列");
        retryErpTaskConsumer = new TaskConsumer(bizRedisTemplate, LogisticsCommonBean.ERP_EXCEPTION_QUEUE, "retryErpTask");
        retryErpThread = new Thread(retryErpTaskConsumer);
        retryErpThread.start();
        log.info("erp异常重试延时队列初始化完毕");
    }
}
