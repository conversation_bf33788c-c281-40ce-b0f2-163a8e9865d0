package com.ecommerce.logistics.controller;

import com.ecommerce.logistics.api.dto.driver.AssignDriverLogAddDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.ecommerce.logistics.service.IAssignDriverLogService;

import java.util.List;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.logistics.api.dto.driver.AssignDriverLogCondDTO;
import com.ecommerce.logistics.api.dto.driver.AssignDriverLogResDTO;


/**
 * 指派司机日志记录服务
 */

@Api(tags = {"AssignDriverLog"})
@RestController
@RequestMapping("/assignDriverLog")
public class AssignDriverLogController {

    @Autowired
    private IAssignDriverLogService iAssignDriverLogService;

    @ApiOperation("条件查询指派司机列表")
    @PostMapping(value = "/queryByCond")
    public ItemResult<List<AssignDriverLogResDTO>> queryByCond(@RequestBody AssignDriverLogCondDTO assignDriverLogCondDTO) {
        return iAssignDriverLogService.queryByCond(assignDriverLogCondDTO);
    }

    /**
     * 自提单车辆和司机的指派记录更新
     *
     * @param assignDriverLogAddDTO
     */
    @ApiOperation("自提单车辆和司机的指派记录更新")
    @PostMapping(value = "/updateSelfPickAssignLog")
    public ItemResult<Void> updateSelfPickAssignLog(@RequestBody AssignDriverLogAddDTO assignDriverLogAddDTO) {
        return iAssignDriverLogService.updateSelfPickAssignLog(assignDriverLogAddDTO);
    }

}
