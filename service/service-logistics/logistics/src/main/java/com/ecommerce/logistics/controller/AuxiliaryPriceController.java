package com.ecommerce.logistics.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.shippingroute.AuxiliaryPriceDTO;
import com.ecommerce.logistics.api.dto.shippingroute.AuxiliaryPriceListDTO;
import com.ecommerce.logistics.service.IAuxiliaryPriceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


import java.util.List;

/**
 * 辅助价目表服务
 * Created by <PERSON><PERSON><PERSON> on 2020/11/9 10:23
 */
@Slf4j
@Api(tags={"AuxiliaryPrice"})
@RestController
@RequestMapping("/auxiliaryPrice")
public class AuxiliaryPriceController {

    @Autowired
    private IAuxiliaryPriceService auxiliaryPriceService;


    @ApiOperation("添加辅助价目表")
    @PostMapping(value="/addAuxiliaryPrice")
    public ItemResult<Void> addAuxiliaryPrice(@Valid @RequestBody List<AuxiliaryPriceDTO> dtoList) {
        return auxiliaryPriceService.addAuxiliaryPrice(dtoList);
    }


    @ApiOperation("删除一个辅助价目表的所有运价差数据")
    @PostMapping(value="/delAuxiliaryPriceByNo")
    public ItemResult<Void> delAuxiliaryPriceByNo(@RequestParam("belongerId")String belongerId,
                                                  @RequestParam("auxiliaryPriceNo")String auxiliaryPriceNo,
                                                  @RequestParam("operatorUserId")String operatorUserId) {
        return auxiliaryPriceService.delAuxiliaryPriceByNo(belongerId, auxiliaryPriceNo, operatorUserId);
    }


    @ApiOperation("删除辅助价目表里面的某一个运价差")
    @PostMapping(value="/delAuxiliaryPrice")
    public ItemResult<Void> delAuxiliaryPrice(@RequestParam("auxiliaryPriceNo")String auxiliaryPriceNo,
                                              @RequestParam("belongerId") String belongerId,
                                              @RequestParam("auxiliaryPriceId")String auxiliaryPriceId,
                                              @RequestParam("operatorUserId")String operatorUserId) {
        return auxiliaryPriceService.delAuxiliaryPrice(auxiliaryPriceNo, belongerId, auxiliaryPriceId, operatorUserId);
    }


    @ApiOperation("编辑辅助价目表")
    @PostMapping(value="/editAuxiliaryPrice")
    public ItemResult<Void> editAuxiliaryPrice(@Valid @RequestBody List<AuxiliaryPriceDTO> dtoList) {
        return auxiliaryPriceService.editAuxiliaryPrice(dtoList);
    }


    @ApiOperation("通过辅助价目表编号查看辅助价目表详情")
    @PostMapping(value="/queryAuxiliaryPriceDetails")
    public ItemResult<List<AuxiliaryPriceDTO>> queryAuxiliaryPriceDetails(@RequestParam("belongerId") String belongerId,
                                                                           @RequestParam("auxiliaryPriceNo") String auxiliaryPriceNo,
                                                                          @RequestParam(value = "drySeasonPolicyAffected",defaultValue = "false") Boolean drySeasonPolicyAffected) {
        return auxiliaryPriceService.queryAuxiliaryPriceDetails(belongerId, auxiliaryPriceNo,drySeasonPolicyAffected);
    }


    @ApiOperation("分页查询辅助价目列表记录")
    @PostMapping(value="/queryAuxiliaryPriceList")
    public ItemResult<PageData<AuxiliaryPriceListDTO>> queryAuxiliaryPriceList(@RequestBody PageQuery<AuxiliaryPriceDTO> pageQuery) {
        return auxiliaryPriceService.queryAuxiliaryPriceList(pageQuery);
    }

}
