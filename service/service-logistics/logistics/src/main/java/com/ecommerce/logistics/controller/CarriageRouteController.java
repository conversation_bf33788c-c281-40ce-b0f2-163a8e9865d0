package com.ecommerce.logistics.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.carriage.*;
import com.ecommerce.logistics.service.ICarriageRouteService;
import com.ecommerce.logistics.service.IRouteContractService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Author: lehu
 * Description:
 * Date: Create in 下午4:22 19/6/3
 * 运费定价路线服务
 */
@Api(tags={"CarriageRoute"})
@RestController
@RequestMapping("/carriageRoute")
public class CarriageRouteController {

    @Autowired
    private ICarriageRouteService carriageRouteService;
    @Autowired
    private IRouteContractService routeContractService;

    @ApiOperation("录入运费路线")
    @PostMapping(value="/enteringCarriageRoute")
    public ItemResult<String> enteringCarriageRoute(@RequestBody CarriageRouteSaveDTO carriageRouteSaveDTO) {
        return carriageRouteService.enteringCarriageRoute(carriageRouteSaveDTO);
    }

    @ApiOperation("删除运费路线")
    @PostMapping(value="/deleteCarriageRoute")
    public ItemResult<Void> deleteCarriageRoute(@RequestBody CarriageRouteDeleteDTO carriageRouteDeleteDTO) {
        return carriageRouteService.deleteCarriageRoute(carriageRouteDeleteDTO);
    }

    @ApiOperation("编辑运费路线")
    @PostMapping(value="/editCarriageRoute")
    public ItemResult<String> editCarriageRoute(@RequestBody CarriageRouteSaveDTO carriageRouteSaveDTO) {
        return carriageRouteService.editCarriageRoute(carriageRouteSaveDTO);
    }

    @ApiOperation("查询运费路线详情")
    @PostMapping(value="/queryCarriageRouteDetail")
    public ItemResult<CarriageRouteDetailDTO> queryCarriageRouteDetail(@ApiParam("运费路线ID") @RequestParam("carriageRouteId") String carriageRouteId) {
        return carriageRouteService.queryCarriageRouteDetail(carriageRouteId);
    }

    @ApiOperation("查询运费路线列表")
    @PostMapping(value="/queryCarriageRouteList")
    public ItemResult<PageData<CarriageRouteListDTO>> queryCarriageRouteList(@RequestBody PageQuery<CarriageRouteQueryDTO> pageQuery) {
        return carriageRouteService.queryCarriageRouteList(pageQuery);
    }

    @ApiOperation("查询ERP运输路线信息")
    @PostMapping(value="/queryERPTransportRoute")
    public ItemResult<List<TransportRouteDTO>> queryERPTransportRoute(@RequestBody TransportRouteQueryDTO dto) {
        return new ItemResult<>(routeContractService.queryERPTransportRoute(dto));
    }

    @ApiOperation("查询点对点运费规则是否绑定ERP合同地址")
    @PostMapping(value="/hasERPContractAddress")
    public boolean hasERPContractAddress(@RequestBody CarriageRouteQueryDTO queryDTO) {
        return carriageRouteService.hasERPContractAddress(queryDTO);
    }


    @ApiOperation("查询点对点运费规则绑定的ERP合同地址信息")
    @PostMapping(value="/getERPContractAddress")
    public TransportRouteDTO getERPContractAddress(@RequestBody CarriageRouteQueryDTO queryDTO) {
        return carriageRouteService.getERPContractAddress(queryDTO);
    }

    @ApiOperation("是否存在运费路线")
    @PostMapping(value="/hasCarriageRoute")
    public boolean hasCarriageRoute(@RequestBody CarriageRouteQueryDTO queryDTO) {
        return carriageRouteService.hasCarriageRoute(queryDTO);
    }
}
