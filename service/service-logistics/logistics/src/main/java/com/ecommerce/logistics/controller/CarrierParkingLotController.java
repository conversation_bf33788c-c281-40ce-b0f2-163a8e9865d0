package com.ecommerce.logistics.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.intelligentDispatch.CarrierParkingLotDTO;
import com.ecommerce.logistics.service.ICarrierParkingLotService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 *承运商停车点管理
 */
@Slf4j
@Api(tags = {"CarrierParkingLotController"})
@RestController
@RequestMapping("/carrierParkingLot")
public class CarrierParkingLotController {

    @Autowired
    private ICarrierParkingLotService carrierParkingLotService;

    @ApiOperation("新增承运商停车点")
    @PostMapping(value = "/add")
    public ItemResult<Boolean> add(@RequestBody CarrierParkingLotDTO dto) {
        return carrierParkingLotService.add(dto);
    }

    @ApiOperation("编辑承运商停车点")
    @PostMapping(value = "/edit")
    public ItemResult<Boolean> edit(@RequestBody CarrierParkingLotDTO dto) {
        return carrierParkingLotService.edit(dto);
    }

    @ApiOperation("删除承运商停车点")
    @PostMapping(value = "/delete")
    public ItemResult<Boolean> delete(@RequestBody CarrierParkingLotDTO dto) {
        return carrierParkingLotService.delete(dto);
    }

    @ApiOperation("分页查询承运商停车点")
    @PostMapping(value = "/pageCarrierParkingLot")
    public ItemResult<PageData<CarrierParkingLotDTO>> pageCarrierParkingLot(@RequestBody PageQuery<CarrierParkingLotDTO> pageQuery) {
        return carrierParkingLotService.pageCarrierParkingLot(pageQuery);
    }
}
