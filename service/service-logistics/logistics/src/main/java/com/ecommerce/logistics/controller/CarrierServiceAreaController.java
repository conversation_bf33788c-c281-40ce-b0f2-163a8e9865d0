package com.ecommerce.logistics.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.intelligentDispatch.CarrierServiceAreaDTO;
import com.ecommerce.logistics.service.ICarrierServiceAreaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


/**
 *承运商服务范围
 */
@Slf4j
@Api(tags = {"CarrierServiceAreaController"})
@RestController
@RequestMapping("/carrierServiceArea")
public class CarrierServiceAreaController {

    @Autowired
    private ICarrierServiceAreaService carrierServiceAreaService;

    @ApiOperation("新增承运商服务范围")
    @PostMapping(value = "/add")
    public ItemResult<Boolean> add(@RequestBody CarrierServiceAreaDTO dto) {
        return carrierServiceAreaService.add(dto);
    }

    @ApiOperation("编辑承运商服务范围")
    @PostMapping(value = "/edit")
    public ItemResult<Boolean> edit(@RequestBody CarrierServiceAreaDTO dto) {
        return carrierServiceAreaService.edit(dto);
    }

    @ApiOperation("删除承运商服务范围")
    @PostMapping(value = "/delete")
    public ItemResult<Boolean> delete(@RequestBody CarrierServiceAreaDTO dto) {
        return carrierServiceAreaService.delete(dto);
    }

    @ApiOperation("分页查询承运商服务范围")
    @PostMapping(value = "/pageCarrierServiceArea")
    public ItemResult<PageData<CarrierServiceAreaDTO>> pageCarrierServiceArea(@RequestBody PageQuery<CarrierServiceAreaDTO> pageQuery) {
        return carrierServiceAreaService.pageCarrierServiceArea(pageQuery);
    }

    @ApiOperation("根据承运商服务范围ID查询详情")
    @PostMapping(value = "/queryDetailById")
    public ItemResult<CarrierServiceAreaDTO> queryDetailById(@RequestParam("serviceAreaId") String serviceAreaId) {
        return carrierServiceAreaService.queryDetailById(serviceAreaId);
    }
}
