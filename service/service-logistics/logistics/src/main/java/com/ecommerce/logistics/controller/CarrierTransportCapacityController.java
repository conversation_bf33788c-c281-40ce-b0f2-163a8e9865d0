package com.ecommerce.logistics.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.intelligentDispatch.CarrierTransportCapacityDTO;
import com.ecommerce.logistics.service.ICarrierTransportCapacityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 *承运商运力管理
 */
@Slf4j
@Api(tags = {"CarrierTransportCapacityController"})
@RestController
@RequestMapping("/carrierTransportCapacity")
public class CarrierTransportCapacityController {

    @Autowired
    private ICarrierTransportCapacityService carrierTransportCapacityService;

    @ApiOperation("新增承运商运力")
    @PostMapping(value = "/add")
    public ItemResult<Boolean> add(@RequestBody CarrierTransportCapacityDTO dto) {
        return carrierTransportCapacityService.add(dto);
    }

    @ApiOperation("编辑承运商运力")
    @PostMapping(value = "/edit")
    public ItemResult<Boolean> edit(@RequestBody CarrierTransportCapacityDTO dto) {
        return carrierTransportCapacityService.edit(dto);
    }

    @ApiOperation("删除承运商运力")
    @PostMapping(value = "/delete")
    public ItemResult<Boolean> delete(@RequestBody CarrierTransportCapacityDTO dto) {
        return carrierTransportCapacityService.delete(dto);
    }

    @ApiOperation("分页查询承运商运力")
    @PostMapping(value = "/pageCarrierTransportCapacity")
    public ItemResult<PageData<CarrierTransportCapacityDTO>> pageCarrierTransportCapacity(@RequestBody PageQuery<CarrierTransportCapacityDTO> pageQuery) {
        return carrierTransportCapacityService.pageCarrierTransportCapacity(pageQuery);
    }
}
