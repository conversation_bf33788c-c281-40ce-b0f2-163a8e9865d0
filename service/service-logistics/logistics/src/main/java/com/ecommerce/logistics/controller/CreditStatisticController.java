package com.ecommerce.logistics.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.creditStatistic.CreditStatisticAddDTO;
import com.ecommerce.logistics.api.dto.creditStatistic.CreditStatisticDTO;
import com.ecommerce.logistics.api.dto.creditStatistic.CreditStatisticListDTO;
import com.ecommerce.logistics.api.dto.creditStatistic.CreditStatisticQueryDTO;
import com.ecommerce.logistics.service.ICreditStatisticService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


/**
 * @Created：Tue Nov 17 19:09:26 CST 2020
 * <AUTHOR>
 * @Version:2
 * @Description:: ICreditStatisticService
 * 信用统计服务
*/

@Api(tags={"CreditStatistic"})
@RestController
@RequestMapping("/creditStatistic")
public class CreditStatisticController {

   @Autowired 
   private ICreditStatisticService iCreditStatisticService;

   @ApiOperation("分页查询信用统计记录")
   @PostMapping(value="/queryCreditStatisticList")
   public ItemResult<PageData<CreditStatisticListDTO>> queryCreditStatisticList(@RequestBody PageQuery<CreditStatisticQueryDTO> pageQuery){
      return iCreditStatisticService.queryCreditStatisticList(pageQuery);
   }


   @ApiOperation("查找信用统计记录")
   @PostMapping(value="/findCreditStatisticById")
   public ItemResult<CreditStatisticDTO> findCreditStatisticById(@RequestParam("personId") String personId,@RequestParam("personType") String personType){
      return iCreditStatisticService.findCreditStatisticById(personId,personType);
   }


   @ApiOperation("添加信用统计")
   @PostMapping(value="/addCreditStatistic")
   public ItemResult<String> addCreditStatistic(@RequestBody CreditStatisticAddDTO addDTO){
      return iCreditStatisticService.addCreditStatistic(addDTO);
   }


   @ApiOperation("更新次数")
   @PostMapping(value="/updateCreditStatisticCount")
   public ItemResult<Void> updateCreditStatisticCount(@RequestParam("personId") String personId,@RequestParam("billCount") Boolean billCount,@RequestParam("breakCount") Boolean breakCount,@RequestParam("complaintCount") Boolean complaintCount,@RequestParam("operatorUserId") String operatorUserId){
      return iCreditStatisticService.updateCreditStatisticCount(personId,billCount,breakCount,complaintCount,operatorUserId);
   }



}
