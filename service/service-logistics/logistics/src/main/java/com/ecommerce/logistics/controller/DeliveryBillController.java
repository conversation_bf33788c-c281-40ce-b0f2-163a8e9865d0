package com.ecommerce.logistics.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.DeliveryDetailDTO;
import com.ecommerce.logistics.api.dto.DeliveryListDTO;
import com.ecommerce.logistics.api.dto.DeliveryQueryDTO;
import com.ecommerce.logistics.api.dto.DeliveryRerouteCondDTO;
import com.ecommerce.logistics.api.dto.DeliveryRerouteDTO;
import com.ecommerce.logistics.api.dto.DeliveryRerouteInfoDTO;
import com.ecommerce.logistics.api.dto.DeliverySimpleDTO;
import com.ecommerce.logistics.api.dto.DeliverySimpleQueryDTO;
import com.ecommerce.logistics.api.dto.DeliveryStatisticsDTO;
import com.ecommerce.logistics.api.dto.DeliveryStatusChangeDTO;
import com.ecommerce.logistics.api.dto.EnteringNormalDeliveryBillDTO;
import com.ecommerce.logistics.api.dto.EnteringProxyDeliveryBillDTO;
import com.ecommerce.logistics.api.dto.MergeDeliveryAssignDTO;
import com.ecommerce.logistics.api.dto.PayableCarriagePriceQueryDTO;
import com.ecommerce.logistics.api.dto.QueryUrgentTaskDTO;
import com.ecommerce.logistics.api.dto.WaitMergeDeliveryQueryDTO;
import com.ecommerce.logistics.api.dto.WarehouseChangeDTO;
import com.ecommerce.logistics.api.dto.assign.AssignShipDTO;
import com.ecommerce.logistics.api.dto.assign.AssignVehicleDTO;
import com.ecommerce.logistics.api.dto.auto.DispatchResultOperationDTO;
import com.ecommerce.logistics.api.dto.auto.InstantAutoDispatchCondDTO;
import com.ecommerce.logistics.api.dto.auto.PlanAutoDispatchCondDTO;
import com.ecommerce.logistics.api.dto.auto.VehicleAutoAssignDTO;
import com.ecommerce.logistics.api.dto.auto.VehicleAutoAssignQueryDTO;
import com.ecommerce.logistics.api.dto.auto.WaitPlanAutoDispatchDTO;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.pickingbill.AssignDetailDTO;
import com.ecommerce.logistics.api.dto.pickingbill.CloseDeliverySheetDTO;
import com.ecommerce.logistics.api.dto.pickingbill.OptionDTO;
import com.ecommerce.logistics.api.dto.waybill.ContractSubmitInfoDTO;
import com.ecommerce.logistics.api.enums.DeliveryOperationTypeEnum;
import com.ecommerce.logistics.service.IDeliveryBillService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Created：Wed Jan 13 10:13:36 CST 2021
 * <AUTHOR>
 * @Version:2
 * @Description:: 委托单服务接口
 */
@Slf4j
@Api(tags = { "DeliveryBill" })
@RestController
@RequestMapping("/deliveryBill")
public class DeliveryBillController {

	@Autowired
	private IDeliveryBillService iDeliveryBillService;

	@ApiOperation("录入一个商品的背靠背委托单信息,请在一级发货单确认时调用")
	@PostMapping(value = "/enteringProxyDeliveryBill")
	public ItemResult<Void> enteringProxyDeliveryBill(@RequestBody EnteringProxyDeliveryBillDTO enteringProxyDeliveryBillDTO) {
		return iDeliveryBillService.enteringProxyDeliveryBill(enteringProxyDeliveryBillDTO);
	}

	@ApiOperation("录入一个普通的委托单信息")
	@PostMapping(value = "/enteringNormalDeliveryBill")
	public ItemResult<Void> enteringNormalDeliveryBill(@RequestBody EnteringNormalDeliveryBillDTO enteringNormalDeliveryBillDTO) {
		iDeliveryBillService.enteringNormalDeliveryBill(enteringNormalDeliveryBillDTO);
		return new ItemResult<>(null);
	}

	@ApiOperation("委托单列表分页查询")
	@PostMapping(value = "/queryDeliveryList")
	public ItemResult<PageData<DeliveryListDTO>> queryDeliveryList(@RequestBody PageQuery<DeliveryQueryDTO> pageQuery) {
		return iDeliveryBillService.queryDeliveryList(pageQuery);
	}

	@ApiOperation("待整合委托单列表分页查询")
	@PostMapping(value = "/queryWaitMergeDeliveryList")
	public ItemResult<PageData<DeliveryListDTO>> queryWaitMergeDeliveryList(@RequestBody PageQuery<WaitMergeDeliveryQueryDTO> pageQuery) {
		return iDeliveryBillService.queryWaitMergeDeliveryList(pageQuery);
	}

	@ApiOperation("整合运力")
	@PostMapping(value = "/mergeAssign")
	public ItemResult<Void> mergeAssign(@RequestBody MergeDeliveryAssignDTO mergeDeliveryAssignDTO) {
		return iDeliveryBillService.mergeAssign(mergeDeliveryAssignDTO);
	}

	@ApiOperation("委托单详情查询")
	@PostMapping(value = "/queryDeliveryDetailById")
	public ItemResult<DeliveryDetailDTO> queryDeliveryDetailById(@RequestParam("deliveryBillId") String deliveryBillId){
		return iDeliveryBillService.queryDeliveryDetailById(deliveryBillId);
	}

	@ApiOperation("在委托单上指派承运商")
	@PostMapping(value = "/assignCarrier")
	public ItemResult<Void> assignCarrier(@RequestBody List<AssignDetailDTO> assignCarrierDTOList,
			@ApiParam("委托单Id") @RequestParam("deliveryBillId") String deliveryBillId,
			@ApiParam("操作人账号Id") @RequestParam("operationUserId") String operationUserId,
			@ApiParam("操作人名称") @RequestParam("operationUserName") String operationUserName) {
		return iDeliveryBillService.assignCarrier(deliveryBillId, assignCarrierDTOList, operationUserId,
				operationUserName);
	}

	@ApiOperation("安排运力-委托单上指派船")
	@PostMapping(value = "/assignShip")
	public ItemResult<Void> assignShip(@RequestBody List<AssignShipDTO> assignShipDTOList,
			@ApiParam("委托单Id") @RequestParam("deliveryBillId") String deliveryBillId,
			@ApiParam("操作人账号Id") @RequestParam("operationUserId") String operationUserId,
			@ApiParam("操作人名称") @RequestParam("operationUserName") String operationUserName) {
		 iDeliveryBillService.assignShip(deliveryBillId, assignShipDTOList, operationUserId, operationUserName);
		return new ItemResult<>(null);
	}

	@ApiOperation("安排运力-委托单上指派车辆")
	@PostMapping(value = "/assignVehicle")
	public ItemResult<Void> assignVehicle(@RequestBody List<AssignVehicleDTO> assignVehicleDTOList,
			@ApiParam("委托单Id") @RequestParam("deliveryBillId") String deliveryBillId,
			@ApiParam("操作人账号Id") @RequestParam("operationUserId") String operationUserId,
			@ApiParam("操作人名称") @RequestParam("operationUserName") String operationUserName) {
		return iDeliveryBillService.assignVehicle(deliveryBillId, assignVehicleDTOList, operationUserId,
				operationUserName);
	}

	@ApiOperation("操作委托单（接受委托,拒绝委托,取消委托,中止委托）")
	@PostMapping(value = "/changeDeliveryStatus")
	public ItemResult<Void> changeDeliveryStatus(@RequestBody DeliveryStatusChangeDTO deliveryStatusChangeDTO)
			 {
		DeliveryOperationTypeEnum deliveryOperationTypeEnum = DeliveryOperationTypeEnum
				.valueOfCode(deliveryStatusChangeDTO.getOperationType());
		if (deliveryOperationTypeEnum == null) {
			throw new BizException(BasicCode.UNDEFINED_ERROR, "操作失败");
		}
		switch (deliveryOperationTypeEnum) {
		case DELIVERY_ACCEPT:
			iDeliveryBillService.acceptDelivery(deliveryStatusChangeDTO.getDeliveryBillId(),
					deliveryStatusChangeDTO.getOperationUserId(), deliveryStatusChangeDTO.getOperationUserName());
			break;
		case DELIVERY_REJECT:
			iDeliveryBillService.rejectDelivery(deliveryStatusChangeDTO.getDeliveryBillId(),
					deliveryStatusChangeDTO.getReason(), deliveryStatusChangeDTO.getOperationUserId(),
					deliveryStatusChangeDTO.getOperationUserName());
			break;
		case DELIVERY_CANCEL:
			iDeliveryBillService.cancelDelivery(deliveryStatusChangeDTO.getDeliveryBillId(),
					deliveryStatusChangeDTO.getOperationUserId(), deliveryStatusChangeDTO.getOperationUserName());
			break;
		case DELIVERY_STOP:
			iDeliveryBillService.stopDelivery(deliveryStatusChangeDTO.getDeliveryBillId(),
					deliveryStatusChangeDTO.getReason(), deliveryStatusChangeDTO.getOperationUserId(),
					deliveryStatusChangeDTO.getOperationUserName());
			break;
		default:
			throw new BizException(BasicCode.UNDEFINED_ERROR, "操作失败");
		}
		return new ItemResult<>(null);
	}

	@ApiOperation("委托时查询运费单价")
	@PostMapping(value = "/queryPayableCarriagePrice")
	public ItemResult<BigDecimal> queryPayableCarriagePrice(@RequestBody PayableCarriagePriceQueryDTO queryDTO)
			 {
		return iDeliveryBillService.queryPayableCarriagePrice(queryDTO);
	}

	@ApiOperation("买家、卖家订单关闭触发批量关闭")
	@PostMapping(value = "/batchCloseDeliverySheet")
	public ItemResult<Void> batchCloseDeliverySheet(@RequestBody CloseDeliverySheetDTO closeDeliverySheetDTO){
		return new ItemResult<>(null);
	}

	/**
	 * 改航
	 *
	 * @param deliveryRerouteDTO
	 * @return
	 */
	@ApiOperation("买家、卖家订单关闭触发批量关闭")
	@PostMapping(value = "/reroute")
	public ItemResult<Void> reroute(@RequestBody DeliveryRerouteDTO deliveryRerouteDTO) {
		return iDeliveryBillService.reroute(deliveryRerouteDTO);
	}

	/**
	 * 委托单分状态统计
	 *
	 * @param dto
	 * @return
	 */
	@ApiOperation("委托单当天完成情况统计")
	@PostMapping(value = "/statisticsDeliveryForTheSomeDay")
	public ItemResult<DeliveryStatisticsDTO> statisticsDeliveryForTheSomeDay(@RequestBody DeliveryQueryDTO dto) {
		return iDeliveryBillService.statisticsDeliveryForTheSomeDay(dto);
	}

	@Deprecated(since = "2.1.4-RELEASE")
	@ApiOperation("委托单当月完成情况统计")
	@PostMapping(value = "/statisticsDeliveryForTheSomeMonth")
	public ItemResult<DeliveryStatisticsDTO> statisticsDeliveryForTheSomeMonth(@RequestBody DeliveryQueryDTO dto) {
		return iDeliveryBillService.statisticsDeliveryForTheSomeMonth(dto);
	}

   @ApiOperation("紧急任务列表分页查询")
   @PostMapping(value="/queryUrgentTaskList")
   public ItemResult<List<DeliveryListDTO>> queryUrgentTaskList(@RequestBody QueryUrgentTaskDTO query){
      return iDeliveryBillService.queryUrgentTaskList(query);
   }

   /**
    * 改航页面的委托单详情
    * @param deliveryRerouteCondDTO
    * @return
    * @throws Exception
    */
   @ApiOperation("改航页面的委托单详情")
   @PostMapping(value="/queryCanRerouteInfo")
   public ItemResult<DeliveryRerouteInfoDTO> queryCanRerouteInfo(@RequestBody DeliveryRerouteCondDTO deliveryRerouteCondDTO) {
      return iDeliveryBillService.queryCanRerouteInfo(deliveryRerouteCondDTO);
   }

   @ApiOperation("查询对账规则需要的委托单会员信息")
   @PostMapping(value="/queryMemberInfoForCheckRule")
   public ItemResult<List<DeliverySimpleDTO>> queryMemberInfoForCheckRule(@RequestBody DeliverySimpleQueryDTO deliverySimpleQueryDTO) {
      return iDeliveryBillService.queryMemberInfoForCheckRule(deliverySimpleQueryDTO);
   }

   @ApiOperation("自动完成发货单")
   @PostMapping(value="/autoFinishTakeCode")
   public ItemResult<Void> autoFinishTakeCode(@ApiParam("发货单号") @RequestParam("takeCode") String takeCode) {
      try {
         iDeliveryBillService.finishTakeCode(takeCode, "system");
      } catch (Exception e) {
         log.error("自动完成发货单发生异常:{},", takeCode, e);
         ItemResult<Void> result = new ItemResult<>();
         result.setSuccess(false);
         result.setDescription(takeCode + e.getMessage());
         return result;
      }
      return new ItemResult<>(null);
   }

   @ApiOperation("自动关闭发货单")
   @PostMapping(value="/autoCloseTakeCode")
   public ItemResult<Void> autoCloseTakeCode(@ApiParam("发货单号") @RequestParam("takeCode") String takeCode) {
      try {
         iDeliveryBillService.closeTakeCode(takeCode, "system");
      } catch (Exception e) {
         log.error("自动关闭发货单发生异常:{},", takeCode, e);
         ItemResult<Void> result = new ItemResult<>();
         result.setSuccess(false);
         result.setDescription(takeCode + e.getMessage());
         return result;
      }
      return new ItemResult<>(null);
   }

	@ApiOperation("即时车辆智能调度")
	@PostMapping(value="/instantVehicleDispatch")
	public ItemResult<List<VehicleAutoAssignDTO>> instantVehicleDispatch(@RequestBody InstantAutoDispatchCondDTO instantAutoDispatchCondDTO) {
		return iDeliveryBillService.instantVehicleDispatch(instantAutoDispatchCondDTO);
	}

	@ApiOperation("智能调度,通过委托单列表创建批次ID")
	@PostMapping(value="/createAutoDispatchBatchId")
	public ItemResult<String> createAutoDispatchBatchId(@RequestBody List<String> deliveryBillIdList) {
		return iDeliveryBillService.createAutoDispatchBatchId(deliveryBillIdList);
	}

	/**
	 * batchId 查询待计划调度的委托单信息
	 * @param batchId
	 * @return
	 */
	@ApiOperation("查询待计划调度的委托单信息")
	@PostMapping(value="/queryWaitAutoDispatchByBatchId")
	public ItemResult<WaitPlanAutoDispatchDTO> queryWaitAutoDispatchByBatchId(@RequestParam("batchId") String batchId) {
		return iDeliveryBillService.queryWaitAutoDispatchByBatchId(batchId);
	}

	/**
	 * 智能调度,生成批次内的所有调度运单项
	 * @param planAutoDispatchCondDTO
	 * @return
	 */
	@ApiOperation("智能调度,生成批次内的所有调度运单项")
	@PostMapping(value="/planVehicleDispatch")
	public ItemResult<Void> planVehicleDispatch(@RequestBody PlanAutoDispatchCondDTO planAutoDispatchCondDTO) {
		return iDeliveryBillService.planVehicleDispatch(planAutoDispatchCondDTO);
	}

	/**
	 * 计划智能调度结果的分页查询
	 * @return
	 */
	@ApiOperation("计划智能调度结果的分页查询")
	@PostMapping(value="/queryAutoWaitAssignList")
	public ItemResult<PageData<VehicleAutoAssignDTO>> queryAutoWaitAssignList(@RequestBody PageQuery<VehicleAutoAssignQueryDTO> vehicleAutoAssignQueryDTO) {
		return iDeliveryBillService.queryAutoWaitAssignList(vehicleAutoAssignQueryDTO);
	}

	/**
	 * 批次ID查询仓库下拉列表
	 * @param batchId
	 * @return
	 */
	@ApiOperation("批次ID查询仓库下拉列表")
	@PostMapping(value="/warehouseOptionsByBatchId")
	public ItemResult<List<OptionDTO>> warehouseOptionsByBatchId(@RequestParam("batchId") String batchId) {
		return iDeliveryBillService.warehouseOptionsByBatchId(batchId);
	}

	/**
	 * 批次ID查询收货地址下拉列表
	 * @param batchId
	 * @return
	 */
	@ApiOperation("批次ID查询收货地址下拉列表")
	@PostMapping(value="/receiverAddressOptionsByBatchId")
	public ItemResult<List<OptionDTO>> receiverAddressOptionsByBatchId(@RequestParam("batchId") String batchId) {
		return iDeliveryBillService.receiverAddressOptionsByBatchId(batchId);
	}

	/**
	 * 确认智能调度
	 * @param dispatchResultOperationDTO
	 * @return
	 */
	@ApiOperation("确认智能调度")
	@PostMapping(value="/confirmAutoDispatch")
	public ItemResult<Void> confirmAutoDispatch(@RequestBody DispatchResultOperationDTO dispatchResultOperationDTO) {
		return iDeliveryBillService.confirmAutoDispatch(dispatchResultOperationDTO);
	}

	/**
	 * 拒绝智能调度
	 * @param dispatchResultOperationDTO
	 * @return
	 */
	@ApiOperation("拒绝智能调度")
	@PostMapping(value="/rejectAutoDispatch")
	public ItemResult<Void> rejectAutoDispatch(@RequestBody DispatchResultOperationDTO dispatchResultOperationDTO) {
		return iDeliveryBillService.rejectAutoDispatch(dispatchResultOperationDTO);
	}

	@ApiOperation("无车承运人 -- 托运合同上报信息查询")
	@PostMapping(value="/queryContractInfo")
	public ItemResult<ContractSubmitInfoDTO> queryContractInfo(@RequestParam("deliveryBillNum") String deliveryBillNum) {
		return iDeliveryBillService.queryContractInfo(deliveryBillNum);
	}

	/**
	 * 修改委托单的仓库
	 * @param warehouseChangeDTO
	 * @return
	 */
	@ApiOperation("修改委托单的仓库")
	@PostMapping(value="/changeDeliveryWarehouse")
	public ItemResult<Void> changeDeliveryWarehouse(@RequestBody WarehouseChangeDTO warehouseChangeDTO) {
		return iDeliveryBillService.changeDeliveryWarehouse(warehouseChangeDTO);
	}
}
