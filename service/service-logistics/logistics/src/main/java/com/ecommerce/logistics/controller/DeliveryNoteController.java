package com.ecommerce.logistics.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.ecommerce.logistics.service.IDeliveryNoteService;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.logistics.api.dto.delivery.DeliveryNoteQueryDTO;
import com.ecommerce.logistics.api.dto.delivery.DeliveryNoteSignDTO;
import com.ecommerce.logistics.api.dto.delivery.DeliveryNoteDetailQueryDTO;
import com.ecommerce.logistics.api.dto.delivery.DeliveryNoteUnloadDTO;
import com.ecommerce.logistics.api.dto.delivery.DeliveryNoteLeaveDTO;
import com.ecommerce.logistics.api.dto.delivery.DeliveryNoteEditDTO;
import com.ecommerce.logistics.api.dto.delivery.DeliveryNoteAddDTO;
import com.ecommerce.logistics.api.dto.delivery.DeliveryNoteListDTO;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.delivery.DeliveryNoteDetailDTO;
import com.ecommerce.logistics.api.dto.base.PageQuery;


/**
 * @Created锛�Tue Jun 18 16:04:04 CST 2019
 * <AUTHOR>
 * @Version:2
 * @Description:送货单服务
 * 目前没有入库,签收时修改库存信息,但是把送货单状态设置为已签收(不设置为已入库)
*/

@Api(tags={"DeliveryNote"})
@RestController
@RequestMapping("/deliveryNote")
public class DeliveryNoteController {

   @Autowired 
   private IDeliveryNoteService iDeliveryNoteService;

   @ApiOperation("入库")
   @PostMapping(value="/unloadDeliveryNote")
   public ItemResult<Void> unloadDeliveryNote(@RequestBody DeliveryNoteUnloadDTO deliveryNoteUnloadDTO){
      return iDeliveryNoteService.unloadDeliveryNote(deliveryNoteUnloadDTO);
   }


   @ApiOperation("创建送货单")
   @PostMapping(value="/createDeliveryNote")
   public ItemResult<String> createDeliveryNote(@RequestBody DeliveryNoteAddDTO deliveryNoteAddDTO){
      return iDeliveryNoteService.createDeliveryNote(deliveryNoteAddDTO);
   }


   @ApiOperation("编辑送货单")
   @PostMapping(value="/editDeliveryNote")
   public ItemResult<Void> editDeliveryNote(@RequestBody DeliveryNoteEditDTO deliveryNoteEditDTO){
      return iDeliveryNoteService.editDeliveryNote(deliveryNoteEditDTO);
   }


   @ApiOperation("详情查询")
   @PostMapping(value="/queryDetail")
   public ItemResult<DeliveryNoteDetailDTO> queryDetail(@RequestBody DeliveryNoteDetailQueryDTO deliveryNoteDetailQueryDTO){
      return iDeliveryNoteService.queryDetail(deliveryNoteDetailQueryDTO);
   }


   @ApiOperation("出厂")
   @PostMapping(value="/leaveWarehouse")
   public ItemResult<Void> leaveWarehouse(@RequestBody DeliveryNoteLeaveDTO deliveryNoteLeaveDTO){
      return iDeliveryNoteService.leaveWarehouse(deliveryNoteLeaveDTO);
   }


   @ApiOperation("分页条件查询")
   @PostMapping(value="/queryList")
   public ItemResult<PageData<DeliveryNoteListDTO>> queryList(@RequestBody PageQuery<DeliveryNoteQueryDTO> pageQuery){
      return iDeliveryNoteService.queryList(pageQuery);
   }


   @ApiOperation("签收")
   @PostMapping(value="/signDeliveryNote")
   public ItemResult<Void> signDeliveryNote(@RequestBody DeliveryNoteSignDTO deliveryNoteSignDTO){
      return iDeliveryNoteService.signDeliveryNote(deliveryNoteSignDTO);
   }



}
