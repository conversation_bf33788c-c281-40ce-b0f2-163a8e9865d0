package com.ecommerce.logistics.controller;

import com.ecommerce.base.api.dto.warehouse.PageQuery;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.contract.DriverContractAddDTO;
import com.ecommerce.logistics.api.dto.contract.DriverContractListDTO;
import com.ecommerce.logistics.api.dto.contract.DriverContractListQueryDTO;
import com.ecommerce.logistics.service.IDriverContractService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 司机合同管理服务
 */
@Api(tags={"DriverContract"})
@RestController
@RequestMapping("/driverContract")
public class DriverContractController {

    @Autowired
    private IDriverContractService driverContractService;

    @ApiOperation("新增司机合同")
    @PostMapping(value="/addDriverContract")
    public ItemResult<String> addDriverContract(@Valid @RequestBody DriverContractAddDTO driverContractAddDTO)  {
        return driverContractService.addDriverContract(driverContractAddDTO);
    }

    @ApiOperation("查询司机合同列表")
    @PostMapping(value="/queryDriverContractList")
    public ItemResult<PageData<DriverContractListDTO>> queryDriverContractList(@Valid @RequestBody PageQuery<DriverContractListQueryDTO> pageQuery)  {
        return driverContractService.queryDriverContractList(pageQuery);
    }

    @ApiOperation("上报司机合同")
    @PostMapping(value="/reportDriverContract")
    public ItemResult<Void> reportDriverContract(@RequestParam("contractIds") String contractIds,
                                                   @RequestParam("operatorUserId") String operatorUserId,
                                                   @RequestParam("operatorUserName") String operatorUserName)  {
        return driverContractService.reportDriverContract(contractIds, operatorUserId, operatorUserName);
    }


}
