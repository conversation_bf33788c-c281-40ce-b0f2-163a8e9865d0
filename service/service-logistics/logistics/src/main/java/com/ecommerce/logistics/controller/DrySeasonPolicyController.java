package com.ecommerce.logistics.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.dry.season.policy.DrySeasonPolicyCreateDTO;
import com.ecommerce.logistics.api.dto.dry.season.policy.DrySeasonPolicyDTO;
import com.ecommerce.logistics.api.dto.dry.season.policy.DrySeasonPolicyDeleteDTO;
import com.ecommerce.logistics.api.dto.dry.season.policy.DrySeasonPolicyListDTO;
import com.ecommerce.logistics.api.dto.dry.season.policy.DrySeasonPolicyQueryDTO;
import com.ecommerce.logistics.api.dto.dry.season.policy.DrySeasonPolicyUpdateDTO;
import com.ecommerce.logistics.api.dto.dry.season.policy.DrySeasonPolicyWaterLevelTemplateDTO;
import com.ecommerce.logistics.api.dto.dry.season.policy.DrySeasonPolicyWaterLevelTemplateEditDTO;
import com.ecommerce.logistics.api.dto.dry.season.policy.DrySeasonPolicyWaterLevelTemplateQueryDTO;
import com.ecommerce.logistics.api.dto.dry.season.policy.PolicyShippingRouteRelationAddDTO;
import com.ecommerce.logistics.api.dto.dry.season.policy.PolicyShippingRouteRelationRemoveDTO;
import com.ecommerce.logistics.service.IDrySeasonPolicyService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 *枯水期策略服务
 */
@Slf4j
@Api(tags={"DrySeasonPolicyController"})
@RestController
@RequestMapping("/drySeasonPolicy")
public class DrySeasonPolicyController {

    @Autowired
    private IDrySeasonPolicyService drySeasonPolicyService;

    @ApiOperation("添加策略")
    @PostMapping(value="/addPolicy")
    public ItemResult<Boolean> addPolicy(@RequestBody DrySeasonPolicyCreateDTO dto){
        return drySeasonPolicyService.addPolicy(dto);
    }

    @ApiOperation("更新策略")
    @PostMapping(value="/updatePolicy")
    public ItemResult<Boolean> updatePolicy(@RequestBody DrySeasonPolicyUpdateDTO dto){
        return drySeasonPolicyService.updatePolicy(dto);
    }

    @ApiOperation("添加策略-线路绑定(航运线路编辑使用)")
    @PostMapping(value="/addPolicyShippingRouteRelation")
    public ItemResult<Boolean> addPolicyShippingRouteRelation(@RequestBody PolicyShippingRouteRelationAddDTO dto){
        return drySeasonPolicyService.addPolicyShippingRouteRelation(dto);
    }

    @ApiOperation("移除策略-线路绑定(航运线路编辑使用)")
    @PostMapping(value="/removePolicyShippingRouteRelation")
    public ItemResult<Boolean> removePolicyShippingRouteRelation(@RequestBody PolicyShippingRouteRelationRemoveDTO dto){
        return drySeasonPolicyService.removePolicyShippingRouteRelation(dto);
    }

    @ApiOperation("删除策略")
    @PostMapping(value="/deletePolicy")
    public ItemResult<Boolean> deletePolicy(@RequestBody DrySeasonPolicyDeleteDTO dto){
        return drySeasonPolicyService.deletePolicy(dto);
    }

    @ApiOperation("翻页查询策略")
    @PostMapping(value="/pageInfoPolicy")
    public ItemResult<PageInfo<DrySeasonPolicyListDTO>> pageInfoPolicy(@RequestBody PageQuery<DrySeasonPolicyQueryDTO> dto){
        return drySeasonPolicyService.pageInfoPolicy(dto);
    }

    @ApiOperation("根据id查询策略")
    @PostMapping(value="/findById")
    public ItemResult<DrySeasonPolicyDTO> findById(@RequestParam("id") String id){
        return drySeasonPolicyService.findById(id);
    }

    @ApiOperation("根据航运线路id查询策略")
    @PostMapping(value="/findByShippingRouteId")
    public ItemResult<List<DrySeasonPolicyDTO>> findByShippingRouteId(@RequestParam("shippingRouteId") String shippingRouteId){
        return drySeasonPolicyService.findByShippingRouteId(shippingRouteId);
    }

    @ApiOperation("查询水位系数表母版")
    @PostMapping(value="/queryWaterLevelTemplate")
    public ItemResult<DrySeasonPolicyWaterLevelTemplateDTO> queryWaterLevelTemplate(@RequestBody DrySeasonPolicyWaterLevelTemplateQueryDTO dto){
        return drySeasonPolicyService.queryWaterLevelTemplate(dto);
    }

    @ApiOperation("编辑修改水位系数表母版")
    @PostMapping(value="/editWaterLevelTemplate")
    public ItemResult<Boolean> editWaterLevelTemplate(@RequestBody DrySeasonPolicyWaterLevelTemplateEditDTO dto){
        return drySeasonPolicyService.editWaterLevelTemplate(dto);
    }
}
