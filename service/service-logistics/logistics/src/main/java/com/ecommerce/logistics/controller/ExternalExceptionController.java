package com.ecommerce.logistics.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.externalexception.ExternalExceptionListDTO;
import com.ecommerce.logistics.api.dto.externalexception.ExternalExceptionQueryDTO;
import com.ecommerce.logistics.api.dto.externalexception.ExternalExceptionRetryDTO;
import com.ecommerce.logistics.biz.message.RedisKeyExpirationListener;
import com.ecommerce.logistics.service.IExternalExceptionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.concurrent.TimeUnit;

/**
 * Author: <EMAIL>
 * Description:
 * Date: Create in 下午3:43 19/4/26
 * 外部异常处理服务
 */
@Api(tags={"ExternalException"})
@RestController
@RequestMapping("/externalException")
public class ExternalExceptionController {

    @Autowired
    @Qualifier("bizRedisTemplate")
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private IExternalExceptionService externalExceptionService;

    @ApiOperation("查询外部异常处理列表")
    @PostMapping(value="/queryExternalExceptionList")
    public ItemResult<PageData<ExternalExceptionListDTO>> queryExternalExceptionList(@RequestBody PageQuery<ExternalExceptionQueryDTO> pageQuery){
        return externalExceptionService.queryExternalExceptionList(pageQuery);
    }

    @ApiOperation("外部异常重试处理")
    @PostMapping(value="/externalExceptionRetryHandler")
    public ItemResult<Void> externalExceptionRetryHandler(@RequestBody ExternalExceptionRetryDTO externalExceptionRetryDTO){
        return externalExceptionService.externalExceptionRetryHandler(externalExceptionRetryDTO);
    }

    @ApiOperation("异常自动重试机制")
    @PostMapping(value = "/autoRetryHandler")
    public ItemResult<String> autoRetryHandler(@RequestParam("exceptionId") String exceptionId) {
        String cacheKey = RedisKeyExpirationListener.ERP_EXCEPTION + ":" + exceptionId;
        //设置5秒过期时间
        redisTemplate.opsForValue().set(cacheKey, "", 5, TimeUnit.SECONDS);
        return new ItemResult<>("OK");
    }
}
