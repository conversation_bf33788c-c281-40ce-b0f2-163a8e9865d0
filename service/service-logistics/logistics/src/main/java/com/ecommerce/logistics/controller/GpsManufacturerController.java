package com.ecommerce.logistics.controller;

import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.gps.GpsManCondDTO;
import com.ecommerce.logistics.api.dto.gps.GpsManufacturerAddDTO;
import com.ecommerce.logistics.api.dto.gps.GpsManufacturerDTO;
import com.ecommerce.logistics.api.dto.gps.GpsManufacturerDeleteDTO;
import com.ecommerce.logistics.api.dto.gps.GpsManufacturerUpdateDTO;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.ecommerce.logistics.service.IGpsManufacturerService;
import java.util.List;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.logistics.api.dto.gps.GpsManufacturerOptionDTO;


/**
 * @Created锛�Mon Nov 26 21:18:41 CST 2018
 * <AUTHOR>
 * @Version:2
 * @Description::
*/

@Api(tags={"GpsManufacturer"})
@RestController
@RequestMapping("/gpsManufacturer")
public class GpsManufacturerController {

   @Autowired 
   private IGpsManufacturerService iGpsManufacturerService;

   @ApiOperation("查询gps厂商下拉列表")
   @PostMapping(value="/queryGpsOptions")
   public ItemResult<List<GpsManufacturerOptionDTO>> queryGpsOptions(){
      return iGpsManufacturerService.queryGpsOptions();
   }

   @ApiOperation("添加GPS厂商")
   @PostMapping(value = "/addGpsManufacturer")
   public ItemResult<String> addGpsManufacturer(@RequestBody GpsManufacturerAddDTO gpsManufacturerAddDTO)  {
      return iGpsManufacturerService.addGpsManufacturer(gpsManufacturerAddDTO);
   }

   @ApiOperation("主键删除GPS厂商")
   @PostMapping(value = "/removeGpsManufacturerById")
   public ItemResult<Void> removeGpsManufacturerById(@RequestBody GpsManufacturerDeleteDTO gpsManufacturerDeleteDTO)  {
      return iGpsManufacturerService.removeGpsManufacturerById(gpsManufacturerDeleteDTO);
   }

   @ApiOperation("获取所有GPS厂商的列表")
   @PostMapping(value = "/queryAllGpsMan")
   public ItemResult<List<GpsManufacturerDTO>> queryAllGpsMan()  {
      return iGpsManufacturerService.queryAllGpsMan();
   }

   @ApiOperation("获取单个GPS厂商的详情")
   @PostMapping(value = "/queryGpsManufacturerById")
   public ItemResult<GpsManufacturerDTO> queryGpsManufacturerById(@ApiParam("GPS厂商ID") @RequestParam("gpsManufacturerId") String gpsManufacturerId)  {
      return iGpsManufacturerService.queryGpsManufacturerById(gpsManufacturerId);
   }

   @ApiOperation("根据编号查询GPS厂商的详情")
   @PostMapping(value = "/queryGpsManufacturerByNumber")
   public ItemResult<GpsManufacturerDTO> queryGpsManufacturerByNumber(@ApiParam("GPS厂商编号") @RequestParam("number") String number)  {
      return iGpsManufacturerService.queryGpsManufacturerByNumber(number);
   }

   @ApiOperation("主键修改GPS厂商信息")
   @PostMapping(value = "/modifyGpsManById")
   public ItemResult<Void> modifyGpsManById(@RequestBody GpsManufacturerUpdateDTO gpsManufacturerUpdateDTO)  {
      return iGpsManufacturerService.modifyGpsManById(gpsManufacturerUpdateDTO);
   }

   @ApiOperation("条件查询GPS厂商信息")
   @PostMapping(value = "/queryGpsManByCond")
   public ItemResult<PageData<GpsManufacturerDTO>> queryGpsManByCond(@RequestBody PageQuery<GpsManCondDTO> pageQuery)  {
      return iGpsManufacturerService.queryGpsManByCond(pageQuery);
   }

}
