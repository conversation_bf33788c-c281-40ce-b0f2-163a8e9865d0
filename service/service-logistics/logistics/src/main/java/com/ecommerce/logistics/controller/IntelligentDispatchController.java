package com.ecommerce.logistics.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.logistics.api.dto.intelligentDispatch.*;
import com.ecommerce.logistics.service.IIntelligentDispatchService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * @Created：Mon Apr 12 11:44:17 CST 2021
 * <AUTHOR>
 * @Version:2
 * @Description:: 智能调度规则服务
*/

@Api(tags={"IntelligentDispatch"})
@RestController
@RequestMapping("/intelligentDispatch")
public class IntelligentDispatchController {

   @Autowired 
   private IIntelligentDispatchService iIntelligentDispatchService;

   @ApiOperation("查询智能调度规则列表")
   @PostMapping(value="/queryRuleList")
   public ItemResult<List<IntelligentDispatchListDTO>> queryRuleList(@RequestBody IntelligentDispatchQueryDTO queryDTO){
      return iIntelligentDispatchService.queryRuleList(queryDTO);
   }

   @ApiOperation("禁用智能调度规则")
   @PostMapping(value="/disableRuleStatus")
   public ItemResult<Void> disableRuleStatus(@RequestBody ChangeRuleStatusDTO changeRuleStatusDTO){
      return iIntelligentDispatchService.disableRuleStatus(changeRuleStatusDTO);
   }

   @ApiOperation("启用智能调度规则")
   @PostMapping(value="/enableRuleStatus")
   public ItemResult<Void> enableRuleStatus(@RequestBody ChangeRuleStatusDTO changeRuleStatusDTO){
      return iIntelligentDispatchService.enableRuleStatus(changeRuleStatusDTO);
   }

   @ApiOperation("承运商智能委托调度")
   @PostMapping(value="/entrustCarrier")
   public ItemResult<CarrierInfoDTO> entrustCarrier(@RequestBody CarrierParamDTO dispatchParamDTO){
      return iIntelligentDispatchService.entrustCarrier(dispatchParamDTO);
   }

}
