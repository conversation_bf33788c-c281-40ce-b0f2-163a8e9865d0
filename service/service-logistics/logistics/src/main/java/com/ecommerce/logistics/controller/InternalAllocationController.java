package com.ecommerce.logistics.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.ReceiveAddressDTO;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.delivery.InternalAllocationDTO;
import com.ecommerce.logistics.api.dto.waybill.AllocationListQueryDTO;
import com.ecommerce.logistics.service.IInternalAllocationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

/**
 * Created by hexinhui3 on 2021/8/11 11:53
 * 内部调拨服务
 */
@Api(tags={"InternalAllocation"})
@RestController
@RequestMapping("/internalAllocation")
public class InternalAllocationController {

    @Autowired
    private IInternalAllocationService allocationService;


    @ApiOperation("获取内部调拨列表")
    @PostMapping(value="/queryInternalAllocationList")
    public ItemResult<PageData<InternalAllocationDTO>> queryInternalAllocationList(@RequestBody PageQuery<AllocationListQueryDTO> pageQuery){
        return allocationService.queryInternalAllocationList(pageQuery);
    }

    @ApiOperation("获取内部调拨详情")
    @PostMapping(value="/getInternalAllocation")
    public ItemResult<InternalAllocationDTO> getInternalAllocation(@RequestBody InternalAllocationDTO dto){
        return allocationService.getInternalAllocation(dto);
    }

    @ApiOperation("保存内部调拨")
    @PostMapping(value="/saveInternalAllocation")
    public ItemResult<ReceiveAddressDTO> saveInternalAllocation(@RequestBody @Valid InternalAllocationDTO dto){
        return allocationService.saveInternalAllocation(dto);
    }


    @ApiOperation("完成内部调拨运单")
    @PostMapping(value = "/completeShipBill")
    public ItemResult<Void> completeShipBill(@RequestBody InternalAllocationDTO dto)  {
        return allocationService.completeShipBill(dto);
    }

    @ApiOperation("取消内部调拨运单")
    @PostMapping(value = "/cancelShipBill")
    public ItemResult<Void> cancelShipBill(@RequestBody InternalAllocationDTO dto)  {
        return allocationService.cancelShipBill(dto);
    }


}
