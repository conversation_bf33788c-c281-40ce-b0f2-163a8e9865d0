package com.ecommerce.logistics.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.logistics.service.ILgsCarrySubmitService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description: CarrySubmitController
 * @Author: <EMAIL>
 * @Date: 2021-05-25 11:27
 * 信息上报服务
 */
@Api(tags={"LgsCarrySubmitController"})
@RestController
@RequestMapping("/lgsCarrySubmit")
public class LgsCarrySubmitController {

    @Autowired
    private ILgsCarrySubmitService lgsCarrySubmitService;

    @ApiOperation("生成电子运单数据")
    @PostMapping(value = "/generateCarryShipNote")
    public ItemResult<String> generateCarryShipNote(@ApiParam("运单号") @RequestParam("waybillNum") String waybillNum,@ApiParam("操作人Id") @RequestParam("operatorId") String operatorId){
        return lgsCarrySubmitService.generateCarryShipNote(waybillNum,operatorId);
    }

    @ApiOperation("生成无车承运人合同信息")
    @PostMapping(value="/generateContract")
    public ItemResult<String> generateContract(@ApiParam("合同类型") @RequestParam("contractType") Integer contractType, @ApiParam("单据号") @RequestParam("number") String number,@ApiParam("操作人Id") @RequestParam("operatorId") String operatorId){
        return lgsCarrySubmitService.generateContract(contractType,number,operatorId);
    }

    @ApiOperation("生成无车承运人资金流水信息")
    @PostMapping(value="/generateFinanceFlow")
    public ItemResult<String> generateFinanceFlow(@ApiParam("运单号") @RequestParam("waybillNum") String waybillNum,@ApiParam("操作人Id") @RequestParam("operatorId") String operatorId){
        return lgsCarrySubmitService.generateFinanceFlow(waybillNum,operatorId);
    }

}
