package com.ecommerce.logistics.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.ecommerce.logistics.service.ILgsElectricInvoiceService;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.logistics.api.dto.invoice.InvoiceBillListDTO;
import com.ecommerce.logistics.api.dto.invoice.ConfirmInvoiceReqDTO;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.invoice.InvoiceBillCondDTO;
import com.ecommerce.logistics.api.dto.invoice.PreviewInvoiceApplyReqDTO;
import com.ecommerce.logistics.api.dto.invoice.PreviewInvoiceApplyResDTO;
import com.ecommerce.logistics.api.dto.base.PageQuery;


/**
 * @Created：Mon Apr 20 14:34:03 CST 2020
 * <AUTHOR>
 * @Version:2
 * @Description:: 物流费电子发票服务
*/

@Api(tags={"LgsElectricInvoice"})
@RestController
@RequestMapping("/lgsElectricInvoice")
public class LgsElectricInvoiceController {

   @Autowired 
   private ILgsElectricInvoiceService iLgsElectricInvoiceService;

   @ApiOperation("批量开票申请预览请求")
   @PostMapping(value="/applyLgsWaybillsInvoice")
   public ItemResult<PreviewInvoiceApplyResDTO> applyLgsWaybillsInvoice(@RequestBody PreviewInvoiceApplyReqDTO previewInvoiceApplyReqDTO){
      return iLgsElectricInvoiceService.applyLgsWaybillsInvoice(previewInvoiceApplyReqDTO);
   }


   @ApiOperation("查询可开物流费发票的运单列表")
   @PostMapping(value="/queryLgsWaybillsForInvoice")
   public ItemResult<PageData<InvoiceBillListDTO>> queryLgsWaybillsForInvoice(@RequestBody PageQuery<InvoiceBillCondDTO> pageQuery){
      return iLgsElectricInvoiceService.queryLgsWaybillsForInvoice(pageQuery);
   }


   @ApiOperation("批量开票申请确认请求")
   @PostMapping(value="/batchConfirmInvoiceApply")
   public ItemResult<Void> batchConfirmInvoiceApply(@RequestBody ConfirmInvoiceReqDTO confirmInvoiceReqDTO){
      return iLgsElectricInvoiceService.batchConfirmInvoiceApply(confirmInvoiceReqDTO);
   }



}
