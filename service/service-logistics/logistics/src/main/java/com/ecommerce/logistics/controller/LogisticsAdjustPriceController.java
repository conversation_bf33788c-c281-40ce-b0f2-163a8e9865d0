package com.ecommerce.logistics.controller;

import java.util.List;

import com.ecommerce.logistics.api.dto.LogisticsAdjustPriceItemDTO;
import com.ecommerce.logistics.service.IShipBillService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.ecommerce.logistics.api.dto.LogisticsAdjustPriceDTO;
import com.ecommerce.logistics.api.dto.LogisticsAdjustPriceMemberDTO;
import com.ecommerce.logistics.service.IDeliveryBillService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 *物流运费调价服务
 */
@Api(tags = {"LogisticsAdjustPriceController"})
@Slf4j
@RestController
@RequestMapping("/logisticsAdjustPrice")
public class LogisticsAdjustPriceController {
	
	@Autowired
	private IDeliveryBillService iDeliveryBillService;

	@Autowired
	private IShipBillService shipBillService;
	
	@ApiOperation("物流调价用户列表")
    @PostMapping(value = "/queryLogisticsAdjustPriceMemberList")
	public List<LogisticsAdjustPriceMemberDTO> queryLogisticsAdjustPriceMemberList(@RequestBody LogisticsAdjustPriceDTO queryDTO){
		return iDeliveryBillService.queryLogisticsAdjustPriceMemberList(queryDTO);
	}

	@ApiOperation("物流调价运单明细列表")
	@PostMapping(value = "/queryLogisticsAdjustPriceItemList")
	public List<LogisticsAdjustPriceItemDTO> queryLogisticsAdjustPriceItemList(@RequestBody LogisticsAdjustPriceMemberDTO queryDTO){
		return iDeliveryBillService.queryLogisticsAdjustPriceItemList(queryDTO);
	}

	@ApiOperation("触发运单对账单")
	@PostMapping(value = "/triggerWaybillStatement")
	public void triggerWaybillStatement(@RequestBody LogisticsAdjustPriceMemberDTO queryDTO){
		List<LogisticsAdjustPriceItemDTO> adjustPriceItemDTOS = queryDTO.getAdjustPriceItemDTOs();
		for (LogisticsAdjustPriceItemDTO adjustPriceItemDTO : adjustPriceItemDTOS) {
			shipBillService.generateBillCheckWaybillInfoList(adjustPriceItemDTO.getWaybillId());
		}
	}
}
