package com.ecommerce.logistics.controller;

import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.ecommerce.logistics.service.IOperationRecordService;
import java.util.List;
import com.ecommerce.logistics.api.dto.operationrecord.OperationRecordListDTO;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.logistics.api.dto.operationrecord.QueryWaybillOperationDTO;
import com.ecommerce.logistics.api.dto.operationrecord.OperationRecordAddDTO;
import com.ecommerce.logistics.api.dto.operationrecord.OperationRecordDTO;


/**
 * @Created锛�Mon Nov 26 21:18:41 CST 2018
 * <AUTHOR>
 * @Version:2
 * @Description:操作记录服务
*/

@Api(tags={"OperationRecord"})
@RestController
@RequestMapping("/operationRecord")
public class OperationRecordController {

   @Autowired 
   private IOperationRecordService iOperationRecordService;

   @ApiOperation("查询运单操作记录")
   @PostMapping(value="/queryWaybillOperation")
   public ItemResult<List<OperationRecordDTO>> queryWaybillOperation(@RequestBody QueryWaybillOperationDTO queryWaybillOperationDTO){
      return iOperationRecordService.queryWaybillOperation(queryWaybillOperationDTO);
   }


   @ApiOperation("获取操作记录列表")
   @PostMapping(value="/queryOperationRecordList")
   public ItemResult<List<OperationRecordListDTO>> queryOperationRecordList(@ApiParam("业务实体ID") @RequestParam("entryId") String entryId){
      return iOperationRecordService.queryOperationRecordList(entryId);
   }


   @ApiOperation("新增操作记录")
   @PostMapping(value="/addOperationRecord")
   public ItemResult<Void> addOperationRecord(@RequestBody OperationRecordAddDTO operationRecordAddDTO){
      return iOperationRecordService.addOperationRecord(operationRecordAddDTO);
   }



}
