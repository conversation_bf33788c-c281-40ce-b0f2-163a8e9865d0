package com.ecommerce.logistics.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.logistics.api.dto.pickingbill.ExpirePickingBillDTO;
import com.ecommerce.logistics.api.dto.pickingbill.StatisticsPickingBillStatusDTO;
import com.ecommerce.logistics.api.dto.pickingbill.PickingBillExportDTO;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.ecommerce.logistics.service.IPickingBillService;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.logistics.api.dto.pickingbill.AssignDispatchBillDTO;
import com.ecommerce.logistics.api.dto.pickingbill.CancelDeliverySheetDTO;
import java.util.List;
import com.ecommerce.logistics.api.dto.pickingbill.PickingBillListQueryDTO;
import com.ecommerce.logistics.api.dto.pickingbill.CancelPickingBillDTO;
import com.ecommerce.logistics.api.dto.pickingbill.ClosePickingBillDTO;
import com.ecommerce.logistics.api.dto.pickingbill.PickingBillListDTO;
import java.lang.String;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.pickingbill.VendorEntrustmentDTO;
import java.lang.Void;
import java.util.Map;

import com.ecommerce.logistics.api.dto.pickingbill.ChangePickingBillWarehouseDTO;
import com.ecommerce.logistics.api.dto.pickingbill.AssignWaybillDTO;
import com.ecommerce.logistics.api.dto.pickingbill.CloseDeliverySheetDTO;
import com.ecommerce.logistics.api.dto.pickingbill.OptionDTO;
import com.ecommerce.logistics.api.dto.pickingbill.CancelQuantityDTO;
import com.ecommerce.logistics.api.dto.pickingbill.PickingBillDTO;
import com.ecommerce.logistics.api.dto.pickingbill.EnteringPickingBillDTO;


/**
 * @Created锛�Mon Nov 26 21:18:42 CST 2018
 * <AUTHOR>
 * @Version:2
 * @Description:提货单服务
*/
@Deprecated(since = "2.1.4-RELEASE")
@Api(tags={"PickingBill"},description = "提货单服务")
@RestController
@RequestMapping("/pickingBill")
public class PickingBillController {

   @Autowired 
   private IPickingBillService iPickingBillService;

   @ApiOperation("获取买家列表")
   @PostMapping(value="/getSellerOptions")
   public ItemResult<List<OptionDTO>> getSellerOptions(@ApiParam("买家名称") @RequestParam("name") String name){
      return iPickingBillService.getSellerOptions(name);
   }


   @ApiOperation("单个关闭提货单")
   @PostMapping(value="/closePickingBill")
   public ItemResult<Void> closePickingBill(@RequestBody ClosePickingBillDTO closePickingBillDTO){
      return iPickingBillService.closePickingBill(closePickingBillDTO);
   }


   @ApiOperation("获取买家列表")
   @PostMapping(value="/getBuyerOptions")
   public ItemResult<List<OptionDTO>> getBuyerOptions(@ApiParam("买家名称") @RequestParam("name") String name){
      return iPickingBillService.getBuyerOptions(name);
   }


   @ApiOperation("买家委托平台配送")
   @PostMapping(value="/vendorEntrustmentPickingBill")
   public ItemResult<Void> vendorEntrustmentPickingBill(@RequestBody VendorEntrustmentDTO vendorEntrustmentDTO){
      return iPickingBillService.vendorEntrustmentPickingBill(vendorEntrustmentDTO);
   }


   @ApiOperation("取消提货单")
   @PostMapping(value="/cancelPickingBill")
   public ItemResult<CancelQuantityDTO> cancelPickingBill(@RequestBody CancelPickingBillDTO cancelPickingBillDTO){
      return iPickingBillService.cancelPickingBill(cancelPickingBillDTO);
   }


   @ApiOperation("录入提货单")
   @PostMapping(value="/enteringPickingBill")
   public ItemResult<String> enteringPickingBill(@RequestBody EnteringPickingBillDTO enteringPickingBillDTO){
      return iPickingBillService.enteringPickingBill(enteringPickingBillDTO);
   }


   @ApiOperation("买家、卖家订单关闭触发批量关闭")
   @PostMapping(value="/batchCloseDeliverySheet")
   public ItemResult<Void> batchCloseDeliverySheet(@RequestBody CloseDeliverySheetDTO closePickingBillDTO){
      return iPickingBillService.batchCloseDeliverySheet(closePickingBillDTO);
   }


   @ApiOperation("修改提货单提货点")
   @PostMapping(value="/changePickingBillWarehouse")
   public ItemResult<Void> changePickingBillWarehouse(@RequestBody ChangePickingBillWarehouseDTO changePickingBillWarehouseDTO){
      return iPickingBillService.changePickingBillWarehouse(changePickingBillWarehouseDTO);
   }


   @ApiOperation("获取提货单列表")
   @PostMapping(value="/queryPickingBillList")
   public ItemResult<PageData<PickingBillListDTO>> queryPickingBillList(@RequestBody PageQuery<PickingBillListQueryDTO> pageQuery){
      return iPickingBillService.queryPickingBillList(pageQuery);
   }


   @ApiOperation("指派生成调度单")
   @PostMapping(value="/assignCreateDispatchBill")
   public ItemResult<Void> assignCreateDispatchBill(@RequestBody AssignDispatchBillDTO assignDispatchBillDTO){
      return iPickingBillService.assignCreateDispatchBill(assignDispatchBillDTO);
   }


   @ApiOperation("获取提货单详情")
   @PostMapping(value="/getPickingBillDetail")
   public ItemResult<PickingBillDTO> getPickingBillDetail(@ApiParam("提货单ID") @RequestParam("pickingBillId") String pickingBillId){
      return iPickingBillService.getPickingBillDetail(pickingBillId);
   }


   @ApiOperation("指派生成运单")
   @PostMapping(value="/assignCreateWaybill")
   public ItemResult<Void> assignCreateWaybill(@RequestBody AssignWaybillDTO assignWaybillDTO){
      return iPickingBillService.assignCreateWaybill(assignWaybillDTO);
   }

    @ApiOperation("买家指派车辆生成运单")
    @PostMapping(value="/assignVehicleCreateWaybill")
    public ItemResult<Void> assignVehicleCreateWaybill(@RequestBody AssignWaybillDTO assignWaybillDTO){
        return iPickingBillService.assignVehicleCreateWaybill(assignWaybillDTO);
    }

   @ApiOperation("取消发货单")
   @PostMapping(value="/cancelDeliverySheet")
   public ItemResult<CancelQuantityDTO> cancelDeliverySheet(@RequestBody CancelDeliverySheetDTO cancelDeliverySheetDTO){
      return iPickingBillService.cancelDeliverySheet(cancelDeliverySheetDTO);
   }


   @ApiOperation("统计提货单状态")
   @PostMapping(value="/statisticsPickingBillStatus")
   public ItemResult<Map<String, StatisticsPickingBillStatusDTO>> statisticsPickingBillStatus(@RequestBody PickingBillListQueryDTO pickingBillListQueryDTO){
      return iPickingBillService.statisticsPickingBillStatus(pickingBillListQueryDTO);
   }

   @ApiOperation("提货单超时关闭")
   @PostMapping(value="/expirePickingBill")
   public ItemResult<Void> expirePickingBill(@RequestBody ExpirePickingBillDTO expirePickingBillDTO)   {
      return iPickingBillService.expirePickingBill(expirePickingBillDTO);
   }

   @ApiOperation("导出提货单")
   @PostMapping(value="/exportPickingBill")
   public List<PickingBillExportDTO> exportPickingBill(@RequestBody PickingBillListQueryDTO queryDTO)  {
      return iPickingBillService.exportPickingBill(queryDTO);
   }

   /**
    * 自提单录入提货单(可以批量指派车辆,也可以直接生成提货单后返回)
    * @param enteringPickingBillDTO
    * @return
    */
   @ApiOperation("自提单录入提货单(可以批量指派车辆,也可以直接生成提货单后返回)")
   @PostMapping(value="/enteringSelfTakePickingBill")
   public ItemResult<String> enteringSelfTakePickingBill(@RequestBody EnteringPickingBillDTO enteringPickingBillDTO)  {
      return iPickingBillService.enteringSelfTakePickingBill(enteringPickingBillDTO);
   }
}
