package com.ecommerce.logistics.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.storehouse.TSSellerViewItem;
import com.ecommerce.logistics.api.dto.storehouse.TurnoverProductViewDTO;
import com.ecommerce.logistics.api.dto.storehouse.TurnoverQueryDTO;
import com.ecommerce.logistics.api.dto.storehouse.TurnoverSellerViewDTO;
import com.ecommerce.logistics.service.IPlatformStockLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Auther: colu
 * @Date: 2019-09-16 15:20
 * @Description: PlatformStockLogController
 * 周转量服务
 */
@Api(tags={"PlatformStockLog"})
@RestController
@RequestMapping("/platformStockLog")
public class PlatformStockLogController {

    @Autowired
    private IPlatformStockLogService platformStockLogService;

    /**
     * 卖家视角查看周转量
     * @param pageQuery
     * @return
     */
    @ApiOperation("平台查看卖家视角周转量")
    @PostMapping("/sellerViewTurnover")
    public ItemResult<PageData<TurnoverSellerViewDTO>> sellerViewTurnover(@RequestBody PageQuery<TurnoverQueryDTO> pageQuery) {
        return platformStockLogService.sellerViewTurnover(pageQuery);
    }

    /**
     * 商品视角查看周转量
     * @param pageQuery
     * @return
     */
    @ApiOperation("平台查看产品视角周转量")
    @PostMapping("/productViewTurnover")
    public ItemResult<PageData<TurnoverProductViewDTO>> productViewTurnover(@RequestBody PageQuery<TurnoverQueryDTO> pageQuery) {
        return platformStockLogService.productViewTurnover(pageQuery);
    }

    /**
     * 为卖家提供查询周转量
     * @param pageQuery
     * @return
     */
    @ApiOperation("卖家查看周转量")
    @PostMapping("/turnoverForSeller")
    public ItemResult<PageData<TSSellerViewItem>> turnoverForSeller(@RequestBody PageQuery<TurnoverQueryDTO> pageQuery) {
        return platformStockLogService.turnoverForSeller(pageQuery);
    }

    /**
     * 卖家查询周转量
     * @param turnoverQueryDTO
     * @return
     */
    @ApiOperation("卖家查询周转量")
    @PostMapping("/queryTurnoverForSeller")
    public ItemResult<List<TSSellerViewItem>> queryTurnoverForSeller(@RequestBody TurnoverQueryDTO turnoverQueryDTO) {
        return platformStockLogService.queryTurnoverForSeller(turnoverQueryDTO);
    }

    /**
     * 平台查询周转量
     * @param turnoverQueryDTO
     * @return
     */
    @ApiOperation("平台查询周转量")
    @PostMapping("/queryTurnoverForPlatform")
    public ItemResult<List<TurnoverSellerViewDTO>> queryTurnoverForPlatform(@RequestBody TurnoverQueryDTO turnoverQueryDTO) {
        return platformStockLogService.queryTurnoverForPlatform(turnoverQueryDTO);
    }

}
