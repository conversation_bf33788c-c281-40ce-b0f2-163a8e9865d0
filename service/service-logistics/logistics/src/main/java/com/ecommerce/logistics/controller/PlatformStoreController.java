package com.ecommerce.logistics.controller;


import com.ecommerce.common.exception.BusinessOperationException;import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.ecommerce.logistics.service.IPlatformStoreService;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.logistics.api.dto.storehouse.PlatformStockListDTO;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.storehouse.PlatformStockQueryDTO;
import com.ecommerce.logistics.api.dto.base.PageQuery;


/**
 * @Created锛�Thu Jul 11 16:59:20 CST 2019
 * <AUTHOR>
 * @Version:2
 * @Description:: 卖家占用平台库存服务接口
*/

@Api(tags={"PlatformStore"})
@RestController
@RequestMapping("/platformStore")
public class PlatformStoreController {

   @Autowired 
   private IPlatformStoreService iPlatformStoreService;

   @ApiOperation("查询卖家占用的平台库存情况列表")
   @PostMapping(value="/queryPlatformStockList")
   public ItemResult<PageData<PlatformStockListDTO>> queryPlatformStockList(@RequestBody PageQuery<PlatformStockQueryDTO> pageQuery)throws Exception{
      return iPlatformStoreService.queryPlatformStockList(pageQuery);
   }



}
