package com.ecommerce.logistics.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.intelligentDispatch.PorterEditDTO;
import com.ecommerce.logistics.api.dto.intelligentDispatch.PorterListDTO;
import com.ecommerce.logistics.api.dto.intelligentDispatch.PorterQueryDTO;
import com.ecommerce.logistics.service.IPorterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * @Created：Tue Apr 13 15:45:58 CST 2021
 * <AUTHOR>
 * @Version:2
 * @Description:: IPorterService
 * 搬运人员服务
*/

@Api(tags={"Porter"})
@RestController
@RequestMapping("/porter")
public class PorterController {

   @Autowired 
   private IPorterService iPorterService;

   @ApiOperation("分页查询搬运人员列表")
   @PostMapping(value="/queryPorterList")
   public ItemResult<PageData<PorterListDTO>> queryPorterList(@RequestBody PageQuery<PorterQueryDTO> pageQuery){
      return iPorterService.queryPorterList(pageQuery);
   }

   @ApiOperation("新增搬运人员")
   @PostMapping(value="/addPorter")
   public ItemResult<Void> addPorter(@RequestBody PorterEditDTO porterEditDTO){
      return iPorterService.addPorter(porterEditDTO);
   }

   @ApiOperation("删除搬运人员")
   @PostMapping(value="/delPorter")
   public ItemResult<Void> delPorter(@RequestBody PorterEditDTO porterEditDTO){
      return iPorterService.delPorter(porterEditDTO);
   }

   @ApiOperation("编辑搬运人员")
   @PostMapping(value="/editPorter")
   public ItemResult<Void> editPorter(@RequestBody PorterEditDTO porterEditDTO){
      return iPorterService.editPorter(porterEditDTO);
   }
}
