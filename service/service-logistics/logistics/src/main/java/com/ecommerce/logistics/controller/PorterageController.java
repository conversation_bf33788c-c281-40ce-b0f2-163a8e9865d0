package com.ecommerce.logistics.controller;


import com.ecommerce.common.exception.BusinessOperationException;import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.ecommerce.logistics.service.IPorterageService;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.logistics.api.dto.porterage.PorterageListDTO;
import com.ecommerce.logistics.api.dto.porterage.PorterageZoneListDTO;
import com.ecommerce.logistics.api.dto.porterage.PorterageDetailDTO;
import java.util.List;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.porterage.PorterageSaveDTO;
import com.ecommerce.logistics.api.dto.porterage.PorterageSearchDTO;
import com.ecommerce.logistics.api.dto.porterage.PorterageComputeDTO;
import com.ecommerce.logistics.api.dto.porterage.PorterageSearchResultDTO;
import com.ecommerce.logistics.api.dto.porterage.PorterageDetailQueryDTO;
import com.ecommerce.logistics.api.dto.porterage.PorterageZoneQueryDTO;
import com.ecommerce.logistics.api.dto.porterage.PorterageComputeResultDTO;
import com.ecommerce.logistics.api.dto.porterage.PorterageListQueryDTO;


/**
 * @Created锛�Mon Nov 26 21:18:42 CST 2018
 * <AUTHOR>
 * @Version:2
 * @Description::
 * 搬运费服务
*/

@Api(tags={"Porterage"})
@RestController
@RequestMapping("/porterage")
public class PorterageController {

   @Autowired 
   private IPorterageService iPorterageService;

   @ApiOperation("新增搬运费规则")
   @PostMapping(value="/addPorterageRule")
   public ItemResult<String> addPorterageRule(@RequestBody PorterageSaveDTO porterageSaveDTO){
      return iPorterageService.addPorterageRule(porterageSaveDTO);
   }


   @ApiOperation("计算订单搬运费")
   @PostMapping(value="/computePorterage")
   public ItemResult<PorterageComputeResultDTO> computePorterage(@RequestBody PorterageComputeDTO porterageComputeDTO){
      return iPorterageService.computePorterage(porterageComputeDTO);
   }


   @ApiOperation("删除搬运费规则")
   @PostMapping(value="/deletePorterageRule")
   public ItemResult<String> deletePorterageRule(@ApiParam("搬运费规则ID") @RequestParam("porterageRuleId") String porterageRuleId){
      return iPorterageService.deletePorterageRule(porterageRuleId);
   }


   @ApiOperation("编辑搬运费规则")
   @PostMapping(value="/editPorterageRule")
   public ItemResult<Void> editPorterageRule(@RequestBody PorterageSaveDTO porterageSaveDTO){
      return iPorterageService.editPorterageRule(porterageSaveDTO);
   }


   @ApiOperation("查询搬运费规则详情")
   @PostMapping(value="/queryPorterageDetail")
   public ItemResult<PorterageDetailDTO> queryPorterageDetail(@RequestBody PorterageDetailQueryDTO porterageDetailQueryDTO){
      return iPorterageService.queryPorterageDetail(porterageDetailQueryDTO);
   }


   @ApiOperation("分页查询搬运费规则列表")
   @PostMapping(value="/queryPorterageRuleList")
   public ItemResult<PageData<PorterageListDTO>> queryPorterageRuleList(@RequestBody PageQuery<PorterageListQueryDTO> pageQuery){
      return iPorterageService.queryPorterageRuleList(pageQuery);
   }


   @ApiOperation("查询区域范围内搬运费列表")
   @PostMapping(value="/queryZonePorterageList")
   public ItemResult<List<PorterageZoneListDTO>> queryZonePorterageList(@RequestBody PorterageZoneQueryDTO porterageZoneQueryDTO){
      return iPorterageService.queryZonePorterageList(porterageZoneQueryDTO);
   }


   @ApiOperation("搜索搬运费规则列表")
   @PostMapping(value="/searchPorterageRuleList")
   public ItemResult<List<PorterageSearchResultDTO>> searchPorterageRuleList(@RequestBody PorterageSearchDTO porterageSearchDTO){
      return iPorterageService.searchPorterageRuleList(porterageSearchDTO);
   }



}
