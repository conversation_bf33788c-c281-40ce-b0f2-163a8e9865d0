package com.ecommerce.logistics.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.logistics.api.dto.pickingbill.OptionDTO;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.ecommerce.logistics.service.IProductInfoService;
import java.util.List;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.logistics.api.dto.productinfo.ProductInfoDTO;


/**
 * @Created锛�Mon Nov 26 21:18:43 CST 2018
 * <AUTHOR>
 * @Version:2
 * @Description:商品信息服务
*/

@Api(tags={"ProductInfo"})
@RestController
@RequestMapping("/productInfo")
public class ProductInfoController {

   @Autowired 
   private IProductInfoService iProductInfoService;

   @ApiOperation("null")
   @PostMapping(value="/queryInfoLikeNote")
   public ItemResult<List<ProductInfoDTO>> queryInfoLikeNote(@ApiParam("商品名") @RequestParam("note") String note){
      return iProductInfoService.queryInfoLikeNote(note);
   }

   @ApiOperation("关键字查询商品下拉列表")
   @PostMapping(value="/queryOptionsByKeyWord")
   public ItemResult<List<OptionDTO>> queryOptionsByKeyWord(@ApiParam("商品名") @RequestParam("keyWord") String keyWord)  {
      return iProductInfoService.queryOptionsByKeyWord(keyWord);
   }

}
