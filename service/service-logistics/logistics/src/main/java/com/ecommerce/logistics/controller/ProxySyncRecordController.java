package com.ecommerce.logistics.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.ProxySyncRecord.ProxySyncRecordListDTO;
import com.ecommerce.logistics.api.dto.ProxySyncRecord.ProxySyncRecordQueryDTO;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.service.IProxySyncRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * @Created：Fri Aug 28 16:48:06 CST 2020
 * <AUTHOR>
 * @Version:2
 * @Description:: 背靠背单据同步记录服务
*/

@Api(tags={"ProxySyncRecord"})
@RestController
@RequestMapping("/proxySyncRecord")
public class ProxySyncRecordController {

   @Autowired 
   private IProxySyncRecordService iProxySyncRecordService;

   @ApiOperation("批量重试背靠背运单同步")
   @PostMapping(value="/syncByProxySyncIds")
   public ItemResult<Void> syncByProxySyncIds(@RequestBody List<String> proxySyncIds)throws Exception{
      return iProxySyncRecordService.syncByProxySyncIds(proxySyncIds);
   }


   @ApiOperation("条件查询背靠背单据同步记录")
   @PostMapping(value="/queryProxySyncRecordList")
   public ItemResult<PageData<ProxySyncRecordListDTO>> queryProxySyncRecordList(@RequestBody PageQuery<ProxySyncRecordQueryDTO> pageQuery)throws Exception{
      return iProxySyncRecordService.queryProxySyncRecordList(pageQuery);
   }



}
