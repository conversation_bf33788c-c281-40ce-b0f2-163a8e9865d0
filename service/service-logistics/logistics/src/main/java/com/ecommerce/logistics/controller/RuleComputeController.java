package com.ecommerce.logistics.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.common.result.ItemResult;
import com.ecommerce.logistics.api.dto.rulecompute.CarriageComputeDTO;
import com.ecommerce.logistics.api.dto.rulecompute.CarriageComputeResultDTO;
import com.ecommerce.logistics.api.dto.rulecompute.CarriageRuleResultDTO;
import com.ecommerce.logistics.api.dto.rulecompute.WaybillMergeDTO;
import com.ecommerce.logistics.service.IRuleComputeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.List;


/**
 * @Created锛�Mon Nov 26 21:18:43 CST 2018
 * <AUTHOR>
 * @Version:2
 * @Description:规则计算服务
*/

@Api(tags={"RuleCompute"})
@RestController
@RequestMapping("/ruleCompute")
public class RuleComputeController {

   @Autowired 
   private IRuleComputeService iRuleComputeService;

   @ApiOperation("运单整合")
   @PostMapping(value="/waybillMerge")
   public ItemResult<String> waybillMerge(@RequestBody WaybillMergeDTO waybillMergeDTO){
      return iRuleComputeService.waybillMerge(waybillMergeDTO);
   }


   @ApiOperation("订单运费计算")
   @PostMapping(value="/orderCarriageCompute")
   public ItemResult<List<CarriageComputeResultDTO>> orderCarriageCompute(@RequestBody CarriageComputeDTO carriageComputeDTO){
      carriageComputeDTO.setSaveRoadBook(1);
      return iRuleComputeService.orderCarriageCompute(carriageComputeDTO);
   }


   @ApiOperation("运单运费计算")
   @PostMapping(value="/waybillCarriageCompute")
   public ItemResult<BigDecimal> waybillCarriageCompute(@RequestBody CarriageComputeDTO carriageComputeDTO){
      return iRuleComputeService.waybillCarriageCompute(carriageComputeDTO);
   }

   @ApiOperation("查询运费规则")
   @PostMapping(value="/queryCarriageRule")
   public ItemResult<List<CarriageRuleResultDTO>> queryCarriageRule(@RequestBody CarriageComputeDTO carriageComputeDTO){
      return iRuleComputeService.queryCarriageRule(carriageComputeDTO);
   }
}
