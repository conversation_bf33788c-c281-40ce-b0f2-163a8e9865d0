package com.ecommerce.logistics.controller;


import com.ecommerce.common.exception.BusinessOperationException;import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.ecommerce.logistics.service.IRuleService;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.logistics.api.dto.rule.CarriageRuleDTO;
import com.ecommerce.logistics.api.dto.rule.CarriageRuleListQueryDTO;
import java.util.List;
import com.ecommerce.logistics.api.dto.rule.MatchCarriageRuleDTO;
import com.ecommerce.logistics.api.dto.rule.CarriageRuleListDTO;


/**
 * @Created锛�Mon Nov 26 21:18:44 CST 2018
 * <AUTHOR>
 * @Version:2
 * @Description:规则服务
*/

@Api(tags={"Rule"})
@RestController
@RequestMapping("/rule")
public class RuleController {

   @Autowired 
   private IRuleService iRuleService;

   @ApiOperation("编辑运费规则")
   @PostMapping(value="/editCarriageRule")
   public ItemResult<Void> editCarriageRule(@RequestBody CarriageRuleDTO carriageRuleDTO){
      return iRuleService.editCarriageRule(carriageRuleDTO);
   }


   @ApiOperation("添加运费规则")
   @PostMapping(value="/addCarriageRule")
   public ItemResult<Void> addCarriageRule(@RequestBody CarriageRuleDTO carriageRuleDTO){
      return iRuleService.addCarriageRule(carriageRuleDTO);
   }


   @ApiOperation("查询运费规则列表")
   @PostMapping(value="/queryCarriageRuleList")
   public ItemResult<List<CarriageRuleListDTO>> queryCarriageRuleList(@RequestBody CarriageRuleListQueryDTO carriageRuleListQueryDTO){
      return iRuleService.queryCarriageRuleList(carriageRuleListQueryDTO);
   }


   @ApiOperation("删除运费规则")
   @PostMapping(value="/removeCarriageRule")
   public ItemResult<Void> removeCarriageRule(@ApiParam("运费规则ID") @RequestParam("carriageRuleId") String carriageRuleId){
      return iRuleService.removeCarriageRule(carriageRuleId);
   }


   @ApiOperation("匹配运费规则")
   @PostMapping(value="/matchCarriageRule")
   public ItemResult<CarriageRuleDTO> matchCarriageRule(@RequestBody MatchCarriageRuleDTO matchCarriageRuleDTO){
      return iRuleService.matchCarriageRule(matchCarriageRuleDTO);
   }



}
