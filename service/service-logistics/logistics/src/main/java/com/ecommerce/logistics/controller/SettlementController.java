package com.ecommerce.logistics.controller;


import com.ecommerce.common.exception.BusinessOperationException;import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.ecommerce.logistics.service.ISettlementService;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.logistics.api.dto.settlement.CarriageSettlementDTO;
import com.ecommerce.logistics.api.dto.settlement.SettlementListQueryDTO;
import com.ecommerce.logistics.api.dto.settlement.UserSettlementListDTO;
import com.ecommerce.logistics.api.dto.settlement.MonthSettlementListQueryDTO;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.settlement.SettlementItemListQueryDTO;
import com.ecommerce.logistics.api.dto.settlement.UserSettlementAmountDTO;
import com.ecommerce.logistics.api.dto.settlement.SettlementItemListDTO;
import com.ecommerce.logistics.api.dto.settlement.UserSettlementListQueryDTO;
import com.ecommerce.logistics.api.dto.settlement.SettlementListDTO;
import com.ecommerce.logistics.api.dto.settlement.MonthSettlementListDTO;


/**
 * @Created锛�Mon Nov 26 21:18:44 CST 2018
 * <AUTHOR>
 * @Version:2
 * @Description:结算服务
*/

@Api(tags={"Settlement"})
@RestController
@RequestMapping("/settlement")
public class SettlementController {

   @Autowired 
   private ISettlementService iSettlementService;

   @ApiOperation("获取月度结算列表")
   @PostMapping(value="/queryMonthSettlementList")
   public ItemResult<PageData<MonthSettlementListDTO>> queryMonthSettlementList(@RequestBody PageQuery<MonthSettlementListQueryDTO> pageQuery){
      return iSettlementService.queryMonthSettlementList(pageQuery);
   }


   @ApiOperation("用户运费结算")
   @PostMapping(value="/userCarriageSettlement")
   public ItemResult<Void> userCarriageSettlement(@RequestBody CarriageSettlementDTO carriageSettlementDTO){
      return iSettlementService.userCarriageSettlement(carriageSettlementDTO);
   }


   @ApiOperation("运单运费结算")
   @PostMapping(value="/waybillCarriageSettlement")
   public ItemResult<Void> waybillCarriageSettlement(@RequestBody CarriageSettlementDTO carriageSettlementDTO){
      return iSettlementService.waybillCarriageSettlement(carriageSettlementDTO);
   }


   @ApiOperation("查询用户结算金额")
   @PostMapping(value="/queryUserSettlementAmount")
   public ItemResult<UserSettlementAmountDTO> queryUserSettlementAmount(@ApiParam("用户ID") @RequestParam("userId") String userId){
      return iSettlementService.queryUserSettlementAmount(userId);
   }


   @ApiOperation("获取结算明细列表")
   @PostMapping(value="/querySettlementItemList")
   public ItemResult<PageData<SettlementItemListDTO>> querySettlementItemList(@RequestBody PageQuery<SettlementItemListQueryDTO> pageQuery){
      return iSettlementService.querySettlementItemList(pageQuery);
   }


   @ApiOperation("查询用户结算信息列表")
   @PostMapping(value="/queryUserSettlementList")
   public ItemResult<PageData<UserSettlementListDTO>> queryUserSettlementList(@RequestBody PageQuery<UserSettlementListQueryDTO> pageQuery){
      return iSettlementService.queryUserSettlementList(pageQuery);
   }


   @ApiOperation("获取结算列表")
   @PostMapping(value="/querySettlementList")
   public ItemResult<PageData<SettlementListDTO>> querySettlementList(@RequestBody PageQuery<SettlementListQueryDTO> pageQuery){
      return iSettlementService.querySettlementList(pageQuery);
   }



}
