package com.ecommerce.logistics.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.DeliveryDetailVehicleDTO;
import com.ecommerce.logistics.api.dto.EmallPublishWaybillListDTO;
import com.ecommerce.logistics.api.dto.EmallPublishWaybillQueryDTO;
import com.ecommerce.logistics.api.dto.EstimateKmQueryDTO;
import com.ecommerce.logistics.api.dto.ForBidCloseWaybillCountQueryDTO;
import com.ecommerce.logistics.api.dto.GoodsInfoDTO;
import com.ecommerce.logistics.api.dto.LastSignWaybillQueryDTO;
import com.ecommerce.logistics.api.dto.ShipBillWaitRerouteListDTO;
import com.ecommerce.logistics.api.dto.WaitRerouteShipBillCondDTO;
import com.ecommerce.logistics.api.dto.WaybillBriefDTO;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.pickingbill.PickingBillDTO;
import com.ecommerce.logistics.api.dto.pickingbill.TwoLeaveOptionDTO;
import com.ecommerce.logistics.api.dto.shipbill.CancelShipBillDTO;
import com.ecommerce.logistics.api.dto.shipbill.CheckWaybillInfoDTO;
import com.ecommerce.logistics.api.dto.shipbill.CheckWaybillQueryDTO;
import com.ecommerce.logistics.api.dto.shipbill.CloseShipBillDTO;
import com.ecommerce.logistics.api.dto.shipbill.ConcreteShipBillInfoDTO;
import com.ecommerce.logistics.api.dto.shipbill.QrCodeDTO;
import com.ecommerce.logistics.api.dto.shipbill.ReassignShipBillDTO;
import com.ecommerce.logistics.api.dto.shipbill.ShipBillBriCondDTO;
import com.ecommerce.logistics.api.dto.shipbill.ShipBillBriResDTO;
import com.ecommerce.logistics.api.dto.shipbill.ShipBillDTO;
import com.ecommerce.logistics.api.dto.shipbill.ShipBillDetailDTO;
import com.ecommerce.logistics.api.dto.shipbill.ShipBillListDTO;
import com.ecommerce.logistics.api.dto.shipbill.ShipBillOrderDetailDTO;
import com.ecommerce.logistics.api.dto.shipbill.ShipBillQueryDTO;
import com.ecommerce.logistics.api.dto.shipbill.ShipWaybillListDTO;
import com.ecommerce.logistics.api.dto.shipbill.ShipWaybillQueryDTO;
import com.ecommerce.logistics.api.dto.shipbill.TransportInfoDTO;
import com.ecommerce.logistics.api.dto.warehouse.WarehouseOptionDTO;
import com.ecommerce.logistics.api.dto.waybill.AppWaybillQueryConditionsMapDTO;
import com.ecommerce.logistics.api.dto.waybill.AppWaybillStatisticsDTO;
import com.ecommerce.logistics.api.dto.waybill.ArriveWarehouseDTO;
import com.ecommerce.logistics.api.dto.waybill.BatchPassCheckDTO;
import com.ecommerce.logistics.api.dto.waybill.CarryWaybillSubmitDTO;
import com.ecommerce.logistics.api.dto.waybill.CheckWaybillListQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.CompleteShipBillDTO;
import com.ecommerce.logistics.api.dto.waybill.ContractSubmitInfoDTO;
import com.ecommerce.logistics.api.dto.waybill.DistrictListDTO;
import com.ecommerce.logistics.api.dto.waybill.DriverConfirmDTO;
import com.ecommerce.logistics.api.dto.waybill.DriverDeliveryDTO;
import com.ecommerce.logistics.api.dto.waybill.DriverWaybillAssignDTO;
import com.ecommerce.logistics.api.dto.waybill.DriverWaybillListQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.EnterFactoryDTO;
import com.ecommerce.logistics.api.dto.waybill.EvaluateWaybillListDTO;
import com.ecommerce.logistics.api.dto.waybill.LeaveWarehouseDTO;
import com.ecommerce.logistics.api.dto.waybill.OpenCabinShipBillDTO;
import com.ecommerce.logistics.api.dto.waybill.PassCheckDTO;
import com.ecommerce.logistics.api.dto.waybill.PublishWaybillListDTO;
import com.ecommerce.logistics.api.dto.waybill.QueryGoodsDTO;
import com.ecommerce.logistics.api.dto.waybill.RefundShipBillDTO;
import com.ecommerce.logistics.api.dto.waybill.SnatchWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.SnatchedWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.SnatchedWaybillQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.TradeWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.TradeWaybillDetailDTO;
import com.ecommerce.logistics.api.dto.waybill.UploadCertificateDTO;
import com.ecommerce.logistics.api.dto.waybill.UserSeckillWaybillListDTO;
import com.ecommerce.logistics.api.dto.waybill.UserSeckillWaybillQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.WaitCheckWaybillListDTO;
import com.ecommerce.logistics.api.dto.waybill.WarehouseAdminProccessedDTO;
import com.ecommerce.logistics.api.dto.waybill.WarehouseAdminProccessedQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.WarehouseAdminWaitDeliveryDTO;
import com.ecommerce.logistics.api.dto.waybill.WarehouseAdminWaitDeliveryQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillAssignDTO;
import com.ecommerce.logistics.biz.message.IMessageBizService;
import com.ecommerce.logistics.service.IShipBillService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.List;

/**
 *运单服务
 */
@Api(tags = {"ShipBillController"})
@Slf4j
@RestController
@RequestMapping("/shipBill")
public class ShipBillController {

    @Autowired
    private IShipBillService shipBillService;

    @Autowired
    private IMessageBizService messageBizService;

    @ApiOperation("查询运单列表")
    @PostMapping(value = "/queryShipBillList")
    public ItemResult<PageData<ShipBillListDTO>> queryShipBillList(@RequestBody PageQuery<ShipBillQueryDTO> pageQuery)  {
        return shipBillService.queryShipBillList(pageQuery);
    }

    @ApiOperation("运单统计(计划提货量，实际出场量)")
    @PostMapping(value = "/statisticsShipBill")
    public ItemResult<AppWaybillStatisticsDTO> statisticsShipBill(@RequestBody ShipBillQueryDTO queryDTO)  {
        return shipBillService.statisticsShipBill(queryDTO);
    }

    @ApiOperation("运单列表筛选条件查询(买家，卖家，商品)")
    @PostMapping(value = "/selectShipBillQueryConditions")
    public ItemResult<AppWaybillQueryConditionsMapDTO> selectShipBillQueryConditions(@RequestBody ShipBillQueryDTO queryDTO)  {
        return shipBillService.selectShipBillQueryConditions(queryDTO);
    }

    @ApiOperation("根据委托单Id查询该委托单自己承运的运单")
    @PostMapping(value = "/queryShipBillByDeliveryBillId")
    public ItemResult<List<DeliveryDetailVehicleDTO>> queryShipBillByDeliveryBillId(@ApiParam("委托单Id") @RequestParam("deliveryBillId") String deliveryBillId)  {
        return shipBillService.queryShipBillByDeliveryBillId(deliveryBillId);
    }

    @ApiOperation("出站")
    @PostMapping(value = "/inputLeaveWarehouse")
    public ItemResult<Void> inputLeaveWarehouse(@RequestBody LeaveWarehouseDTO leaveWarehouseDTO)  {
        return shipBillService.inputLeaveWarehouse(leaveWarehouseDTO);
    }

    @ApiOperation("开仓(如果是一级开仓,将尝试开仓二级)")
    @PostMapping(value = "/openCabin")
    public ItemResult<Boolean> openCabin(@RequestBody OpenCabinShipBillDTO openCabinShipBillDTO)  {
        return shipBillService.openCabin(openCabinShipBillDTO);
    }

    @ApiOperation("完成运单")
    @PostMapping(value = "/completeShipBill")
    public ItemResult<Void> completeShipBill(@RequestBody CompleteShipBillDTO completeShipBillDTO)  {
        return shipBillService.completeShipBill(completeShipBillDTO);
    }

    @ApiOperation("查询个体司机的未完成运单号")
    @PostMapping(value = "/queryPersonalDriverHalfWayWaybillNums")
    public ItemResult<List<String>> queryPersonalDriverHalfWayWaybillNums(@ApiParam("个体司机MemberID") @RequestParam("personalDriverMemberId") String personalDriverMemberId)  {
        return shipBillService.queryPersonalDriverHalfWayWaybillNums(personalDriverMemberId);
    }

    @ApiOperation("获取运单详情")
    @GetMapping(value = "/getWaybillDetail")
    public ItemResult<ShipBillDetailDTO> getWaybillDetail(@ApiParam("运单子项ID") @RequestParam("waybillItemId") String waybillItemId)  {
        return shipBillService.getWaybillDetail(waybillItemId);
    }

    @ApiOperation("获取运单详情-司机APP调用")
    @GetMapping(value = "/getWaybillDetailForDriverApp")
    public ItemResult<ShipBillDetailDTO> getWaybillDetailForDriverApp(@ApiParam("运单Id") @RequestParam("waybillId") String waybillId) {
        return shipBillService.getWaybillDetailForDriverApp(waybillId);
    }

    @ApiOperation("根据发货单号查询运单信息")
    @PostMapping(value = "/queryWaybillListByTakeCode")
    public ItemResult<List<ShipBillOrderDetailDTO>> queryWaybillListByTakeCode(@ApiParam("发货单号集合") @RequestParam("takeCodeList") List<String> takeCodeList)  {
        return shipBillService.queryWaybillListByTakeCode(takeCodeList);
    }

    @ApiOperation("根据发货单号查询运单列表")
    @PostMapping(value = "/queryWaybillListByDeliveryNum")
    public ItemResult<List<TradeWaybillDTO>> queryWaybillListByDeliveryNum(@ApiParam("发货单号") @RequestParam("deliverySheetNum") String deliverySheetNum)  {
        return shipBillService.queryWaybillListByDeliveryNum(deliverySheetNum);
    }

    @ApiOperation("查询可改航运单列表")
    @PostMapping(value = "/queryWaitRerouteShipBills")
    public ItemResult<PageData<ShipBillWaitRerouteListDTO>> queryWaitRerouteShipBills(@RequestBody PageQuery<WaitRerouteShipBillCondDTO> pageQuery) {
        return shipBillService.queryWaitRerouteShipBills(pageQuery);
    }

    @ApiOperation("取消运单")
    @PostMapping(value = "/cancelShipBill")
    public ItemResult<Void> cancelShipBill(@RequestBody CancelShipBillDTO cancelShipBillDTO) {
        return shipBillService.cancelShipBill(cancelShipBillDTO);
    }

    @ApiOperation("取消运单的重新指派")
    @PostMapping(value = "/assignCanceledShipBill")
    public ItemResult<Void> assignCanceledShipBill(@RequestBody ReassignShipBillDTO reassignShipBillDTO) {
        return shipBillService.assignCanceledShipBill(reassignShipBillDTO);
    }

    @ApiOperation("关闭运单")
    @PostMapping(value = "/discardShipBill")
    public ItemResult<Void> discardShipBill(@RequestBody CloseShipBillDTO closeShipBillDTO) {
        return shipBillService.discardShipBill(closeShipBillDTO);
    }

    @ApiOperation("运单上传支付凭证")
    @PostMapping(value = "/uploadCertificate")
    public ItemResult<Void> uploadCertificate(@RequestBody UploadCertificateDTO uploadCertificateDTO) {
        return shipBillService.uploadCertificate(uploadCertificateDTO);
    }

    @ApiOperation("查询发货单下不可以关闭的运单数量")
    @PostMapping(value = "/queryForbidCloseWaybillCountByTakeCode")
    public ItemResult<Integer> queryForbidCloseWaybillCountByTakeCode(@RequestBody ForBidCloseWaybillCountQueryDTO forBidCloseWaybillCountQueryDTO){
        return shipBillService.queryForbidCloseWaybillCountByTakeCode(forBidCloseWaybillCountQueryDTO);
    }

    @ApiOperation("查询发货单下不可以关闭的运单列表")
    @PostMapping(value = "/queryForbidCloseWaybillListByTakeCode")
    public ItemResult<List<ShipBillDTO>> queryForbidCloseWaybillListByTakeCode(@RequestBody ForBidCloseWaybillCountQueryDTO forBidCloseWaybillCountQueryDTO)
    {
        return shipBillService.queryForbidCloseWaybillListByTakeCode(forBidCloseWaybillCountQueryDTO);
    }

    @ApiOperation("APP查看物流")
    @PostMapping(value = "/queryTradeWaybillDetail")
    public ItemResult<TradeWaybillDetailDTO> queryTradeWaybillDetail(@ApiParam("运单ID") @RequestParam("waybillId") String waybillId) {
        return shipBillService.queryTradeWaybillDetail(waybillId);
    }

    @ApiOperation("根据运单号获取APP展示的运单详情")
    @PostMapping(value="/queryTradeWaybillDetailByNum")
    public ItemResult<TradeWaybillDetailDTO> queryTradeWaybillDetailByNum(@ApiParam("运单号") @RequestParam("waybillNum") String waybillNum)  {
        return shipBillService.queryTradeWaybillDetailByNum(waybillNum);
    }

    @ApiOperation("审核运单")
    @PostMapping(value = "/passCheck")
    public ItemResult<Void> passCheck(@RequestBody PassCheckDTO passCheckDTO) {
        return shipBillService.passCheck(passCheckDTO);
    }

    @ApiOperation("批量审核运单")
    @PostMapping(value = "/batchPassCheck")
    public ItemResult<Void> batchPassCheck(@RequestBody BatchPassCheckDTO batchPassCheckDTO) {
        return shipBillService.batchPassCheck(batchPassCheckDTO);
    }

    @ApiOperation("待审核运单列表查询")
    @PostMapping(value = "/queryCheckWaybillList")
    public ItemResult<PageData<WaitCheckWaybillListDTO>> queryCheckWaybillList(@RequestBody PageQuery<CheckWaybillListQueryDTO> pageQuery) {
        return shipBillService.queryCheckWaybillList(pageQuery);
    }

    @ApiOperation("查询仓库管理员待配送运单列表")
    @PostMapping(value="/getWarehouseAdminWaitDelivery")
    public ItemResult<PageData<WarehouseAdminWaitDeliveryDTO>> getWarehouseAdminWaitDelivery(@RequestBody PageQuery<WarehouseAdminWaitDeliveryQueryDTO> pageQuery) {
        return shipBillService.getWarehouseAdminWaitDelivery(pageQuery);
    }

    @ApiOperation("查询仓库管理员已处理运单列表")
    @PostMapping(value="/getWarehouseAdminProccessed")
    public ItemResult<PageData<WarehouseAdminProccessedDTO>> getWarehouseAdminProccessed(@RequestBody PageQuery<WarehouseAdminProccessedQueryDTO> pageQuery) {
        return shipBillService.getWarehouseAdminProccessed(pageQuery);
    }

    @ApiOperation("进厂")
    @PostMapping(value="/enterFactory")
    public ItemResult<Void> enterFactory(@RequestBody EnterFactoryDTO dto)  {
        return shipBillService.enterFactory(dto);
    }

    @ApiOperation("撤回进厂")
    @PostMapping(value="/withdrawEnterFactory")
    public ItemResult<Void> withdrawEnterFactory(@RequestBody EnterFactoryDTO dto)  {
        return shipBillService.withdrawEnterFactory(dto);
    }

    @ApiOperation("司机确认运单(承运商指派)")
    @PostMapping(value="/confirmWaybillByDriver")
    public ItemResult<Void> confirmWaybillByDriver(@RequestBody DriverConfirmDTO driverConfirmDTO) {
        return shipBillService.confirmWaybillByDriver(driverConfirmDTO);
    }

    @ApiOperation("司机拒绝运单(承运商指派或平台指派个体司机)")
    @PostMapping(value="/rejectWaybillByDriver")
    public ItemResult<Void> rejectWaybillByDriver(@RequestBody DriverConfirmDTO driverConfirmDTO) {
        return shipBillService.rejectWaybillByDriver(driverConfirmDTO);
    }

    @ApiOperation("获取抢单用户的查询仓库列表")
    @PostMapping(value="/selectSeckillWarehouseByUserId")
    public ItemResult<List<WarehouseOptionDTO>> selectSeckillWarehouseByUserId(@ApiParam("抢单用户ID") @RequestParam("userId") String userId){
        return shipBillService.selectSeckillWarehouseByUserId(userId);
    }

    @ApiOperation("获取用户可抢单区域列表")
    @PostMapping(value="/selectSeckillDistrictsByUserId")
    public ItemResult<List<DistrictListDTO>> selectSeckillDistrictsByUserId(@ApiParam("用户ID") @RequestParam("userId") String userId){
        return shipBillService.selectSeckillDistrictsByUserId(userId);
    }

    @ApiOperation("查询用户可抢运单集合")
    @PostMapping(value="/selectUserSeckillWaybillList")
    public ItemResult<PageData<UserSeckillWaybillListDTO>> selectUserSeckillWaybillList(@RequestBody PageQuery<UserSeckillWaybillQueryDTO> pageQuery){
        return shipBillService.selectUserSeckillWaybillList(pageQuery);
    }

    @ApiOperation("查询用户已抢运单集合")
    @PostMapping(value="/selectUserSeckilledWaybillList")
    public ItemResult<PageData<UserSeckillWaybillListDTO>> selectUserSeckilledWaybillList(@RequestBody PageQuery<DriverWaybillListQueryDTO> pageQuery){
        return shipBillService.selectUserSeckilledWaybillList(pageQuery);
    }

    @ApiOperation("司机抢单")
    @PostMapping(value="/snatchWaybill")
    public ItemResult<Void> snatchWaybill(@RequestBody SnatchWaybillDTO snatchWaybillDTO){
        return shipBillService.snatchWaybill(snatchWaybillDTO);
    }

    @ApiOperation("更新送达时间")
    @PostMapping(value="/changeArriveDestinationTime")
    public ItemResult<Void> changeArriveDestinationTime(@ApiParam("运单ID") @RequestParam("waybillId") String waybillId){
        return shipBillService.changeArriveDestinationTime(waybillId);
    }

    @ApiOperation("车辆进站")
    @PostMapping(value="/arriveWarehouse")
    public ItemResult<Void> arriveWarehouse(@RequestBody ArriveWarehouseDTO dto){
        return shipBillService.arriveWarehouse(dto);
    }

    @ApiOperation("查询司机当前配送状态")
    @PostMapping(value="/queryDriverDeliveryStatus")
    public ItemResult<DriverDeliveryDTO> queryDriverDeliveryStatus(@ApiParam("司机ID") @RequestParam("driverId") String driverId){
        return shipBillService.queryDriverDeliveryStatus(driverId);
    }

    @ApiOperation("查询主运单列表")
    @PostMapping(value = "/queryShipWaybillList")
    public ItemResult<PageData<ShipWaybillListDTO>> queryShipWaybillList(@RequestBody PageQuery<ShipWaybillQueryDTO> pageQuery)  {
        return shipBillService.queryShipWaybillList(pageQuery);
    }

    @ApiOperation("指派运单到承运商")
    @PostMapping(value="/assignWaybill")
    public ItemResult<Void> assignWaybill(@RequestBody WaybillAssignDTO waybillAssignDTO){
        return shipBillService.assignWaybill(waybillAssignDTO);
    }

    @ApiOperation("承运商指派运单到司机")
    @PostMapping(value="/carrierAssignWaybillToDriver")
    public ItemResult<Void> carrierAssignWaybillToDriver(@RequestBody DriverWaybillAssignDTO driverWaybillAssignDTO){
        return shipBillService.carrierAssignWaybillToDriver(driverWaybillAssignDTO);
    }

    @ApiOperation("船运单开仓提醒")
    @PostMapping(value="/openCabinRemind")
    public ItemResult<Void> openCabinRemind(@ApiParam("运单子项Id") @RequestParam("waybillItemId") String waybillItemId){
        return shipBillService.openCabinRemind(waybillItemId);
    }

    @ApiOperation("船运单卸货提醒")
    @PostMapping(value="/unloadingRemind")
    public ItemResult<Void> unloadingRemind(@ApiParam("运单子项Id") @RequestParam("waybillItemId") String waybillItemId){
        return shipBillService.unloadingRemind(waybillItemId);
    }

    @ApiOperation("获取评价运单列表")
    @PostMapping(value="/queryEvaluateWaybillList")
    public ItemResult<List<EvaluateWaybillListDTO>> queryEvaluateWaybillList(@ApiParam("发货单号集合") @RequestBody List<String> deliverySheetNumList)  {
        return shipBillService.queryEvaluateWaybillList(deliverySheetNumList);
    }

    @ApiOperation("获取运单的一级配送信息")
    @PostMapping(value="/queryPickingListByWaybillNum")
    public ItemResult<List<PickingBillDTO>> queryPickingListByWaybillNum(@ApiParam("运单号集合") @RequestBody List<String> waybillNumList)  {
        return shipBillService.queryPickingListByWaybillNum(waybillNumList);
    }

    @ApiOperation("获取已抢运单列表--Emall调用")
    @PostMapping(value="/querySnatchedWaybillList")
    public ItemResult<List<SnatchedWaybillDTO>> querySnatchedWaybillList(@RequestBody SnatchedWaybillQueryDTO snatchedWaybillQueryDTO) {
        return shipBillService.querySnatchedWaybillList(snatchedWaybillQueryDTO);
    }

    @ApiOperation("根据运单状态和运单类型查询指定条数的发布运单信息--Emall调用")
    @PostMapping(value="/selectWaybillForEmall")
    public ItemResult<List<EmallPublishWaybillListDTO>> selectWaybillForEmall(@RequestBody EmallPublishWaybillQueryDTO emallPublishWaybillQueryDTO) {
        return shipBillService.selectWaybillForEmall(emallPublishWaybillQueryDTO);
    }

    @ApiOperation("运单号查询轨迹回放页面运单信息")
    @PostMapping(value="/selectWaybillForNum")
    public ItemResult<PublishWaybillListDTO> selectWaybillForNum(@ApiParam("运单号") @RequestParam("waybillNum") String waybillNum) {
        return shipBillService.selectWaybillForNum(waybillNum);
    }

    @ApiOperation("添加主运单ID到自动完成的队列中")
    @PostMapping(value="/addParentIdToAutoComplete")
    public ItemResult<Void> addParentIdToAutoComplete(@ApiParam("主运单ID") @RequestParam("parentId") String parentId) {
        //通知物流的自动完成功能
        try {
            log.info("addParentIdToAutoComplete parentId:{}",parentId);
        } catch (Exception e) {
            log.error("添加自动完成发生异常:{},", parentId, e);
            ItemResult<Void> result = new ItemResult<>();
            result.setSuccess(false);
            result.setDescription(e.getMessage());
            return result;
        }
        return new ItemResult<>(null);
    }

    @ApiOperation("预估距离")
    @PostMapping(value="/estimateKm")
    public ItemResult<BigDecimal> estimateKm(@RequestBody EstimateKmQueryDTO queryDTO) {
        return shipBillService.estimateKm(queryDTO);
    }

    @ApiOperation("查询对账单-运单明细信息")
    @PostMapping(value="/queryBillCheckWaybillInfoList")
    public ItemResult<PageData<CheckWaybillInfoDTO>> queryBillCheckWaybillInfoList(@RequestBody PageQuery<CheckWaybillQueryDTO> pageQuery) {
        return shipBillService.queryBillCheckWaybillInfoList(pageQuery);
    }

    /**
     * 运单ID查询混凝土的信息
     * @param waybillId
     * @return
     */
    @ApiOperation("运单ID查询混凝土的信息")
    @PostMapping(value="/queryConcreteInfoByWaybillId")
    public ConcreteShipBillInfoDTO queryConcreteInfoByWaybillId(@ApiParam("运单ID") @RequestParam("waybillId") String waybillId) {
        return shipBillService.queryConcreteInfoByWaybillId(waybillId);
    }

    @ApiOperation("根据运单号查询已完成运单详情（信息上报调用）")
    @PostMapping(value = "/selectCarryWaybill")
    public ItemResult<List<CarryWaybillSubmitDTO>> selectCarryWaybill(@ApiParam("运单号") @RequestParam("waybillNum") String waybillNum){
        return shipBillService.selectCarryWaybill(waybillNum);
    }

    /**
     * 根据运单id生成对账单数据
     * @param waybillId
     * @return
     */
    @ApiOperation("根据运单id生成对账单数据")
    @PostMapping(value="/generateBillCheckWaybillInfoList")
    public ItemResult<Void> generateBillCheckWaybillInfoList(@ApiParam("运单ID") @RequestParam("waybillId") String waybillId) {
        return shipBillService.generateBillCheckWaybillInfoList(waybillId);
    }

    @ApiOperation("洗数据")
    @PostMapping(value="/generateBillCheckWaybill")
    public ItemResult<Void> generateBillCheckWaybill() {
        return shipBillService.generateBillCheckWaybill();
    }


    @ApiOperation("重新创建ERP运单")
    @PostMapping(value="/againCreateExternalWaybill")
    public ItemResult<ShipBillListDTO> againCreateExternalWaybill(@ApiParam("运单ID") @RequestParam("waybillId") String waybillId,
                                                           @RequestParam("operationUserId") String operationUserId,
                                                           @RequestParam("operationUserName") String operationUserName) throws BizException {
        return shipBillService.againCreateExternalWaybill(waybillId, operationUserId, operationUserName);
    }


    /**
     * 查询运单的泵候选信息
     * @param waybillId
     * @return
     */
    @ApiOperation("查询运单的泵候选信息")
    @PostMapping(value="/queryPumpListByWaybillId")
    public ItemResult<TwoLeaveOptionDTO> queryPumpListByWaybillId(@ApiParam("运单ID") @RequestParam("waybillId") String waybillId) {
        return shipBillService.queryPumpListByWaybillId(waybillId);
    }

    @ApiOperation("无车承运人 -- 实际运输合同上报信息查询")
    @PostMapping(value="/queryContractInfo")
    public ItemResult<ContractSubmitInfoDTO> queryContractInfo(@RequestParam("waybillNum") String waybillNum) {
        return shipBillService.queryContractInfo(waybillNum);
    }


    @ApiOperation("运单退货")
    @PostMapping(value="/refundShipBill")
    public ItemResult<Void> refundShipBill(@RequestBody RefundShipBillDTO refundShipBillDTO) {
        return shipBillService.refundShipBill(refundShipBillDTO);
    }

    /**
     * 查询最新签收的运单信息
     * @param lastSignWaybillQueryDTO
     * @return
     */
    @ApiOperation("查询最新签收的运单信息")
    @PostMapping(value="/queryLastSignWaybill")
    public ItemResult<WaybillBriefDTO> queryLastSignWaybill(@RequestBody LastSignWaybillQueryDTO lastSignWaybillQueryDTO) {
        return shipBillService.queryLastSignWaybill(lastSignWaybillQueryDTO);
    }

    @ApiOperation("获取商品种类下拉列表")
    @PostMapping(value = "/queryGoodsNameListDropDownBox")
    public ItemResult<List<GoodsInfoDTO>> queryGoodsNameListDropDownBox(@RequestBody QueryGoodsDTO queryGoodsDTO){
        return new ItemResult<>(shipBillService.queryGoodsNameListDropDownBox(queryGoodsDTO));
    }

    @ApiOperation("通过车牌号、船名查询")
    @PostMapping(value="/queryVehicleOrShippingListByName")
    public ItemResult<List<String>> queryVehicleOrShippingListByName(@RequestBody ShipBillQueryDTO queryDTO){
        return new ItemResult<>(shipBillService.queryVehicleOrShippingListByName(queryDTO));
    }
    /**
     * 运单号查询运输工具
     * @param waybillNum
     * @return
     */
    @ApiOperation("运单号查询运输工具")
    @PostMapping(value = "/queryTransportByWaybillNum")
    public ItemResult<TransportInfoDTO> queryTransportByWaybillNum(@RequestParam("waybillNum") String waybillNum) {
        return new ItemResult<>(shipBillService.queryTransportByWaybillNum(waybillNum));
    }

    /**
     * 查询订单下待发货的二维码信息
     * @param orderCode
     * @return
     */
    @ApiOperation("查询订单下待发货的二维码信息")
    @PostMapping(value="/queryWDQrCodeByOrderCode")
    public ItemResult<List<QrCodeDTO>> queryWDQrCodeByOrderCode(@RequestParam("orderCode") String orderCode)  {
        return shipBillService.queryWDQrCodeByOrderCode(orderCode);
    }
    /**
     * 运单简要信息条件查询
     * @param shipBillBriCondDTO
     * @return
     */
    @ApiOperation("运单简要信息条件查询")
    @PostMapping(value = "/queryShipBillBriByCond")
    public ShipBillBriResDTO queryShipBillBriByCond(@RequestBody ShipBillBriCondDTO shipBillBriCondDTO) {
        return shipBillService.queryShipBillBriByCond(shipBillBriCondDTO);
    }

}
