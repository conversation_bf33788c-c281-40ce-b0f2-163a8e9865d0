package com.ecommerce.logistics.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.common.result.ItemResult;
import com.ecommerce.logistics.api.dto.shipbill.ShipBillCloseERPCallbackDTO;
import com.ecommerce.logistics.api.dto.shipbill.ShipBillERPCallbackDTO;
import com.ecommerce.logistics.api.dto.shipbill.ShipBillModifyERPCallbackDTO;
import com.ecommerce.logistics.api.dto.waybill.ERPShipBillDTO;
import com.ecommerce.logistics.api.dto.waybill.ExternalWaybillRequestDTO;
import com.ecommerce.logistics.service.IShipBillExternalService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


/**
 * @Created：Wed Feb 03 21:05:19 CST 2021
 * <AUTHOR>
 * @Version:2
 * @Description:: IShipBillExternalService
*/

@Api(tags={"ShipBillExternal"})
@RestController
@RequestMapping("/shipBillExternal")
public class ShipBillExternalController {

   @Autowired 
   private IShipBillExternalService iShipBillExternalService;

   @ApiOperation("null")
   @PostMapping(value="/createExternalShipBill")
   public void createExternalShipBill(@RequestBody ExternalWaybillRequestDTO requestDTO){
      iShipBillExternalService.createExternalShipBill(requestDTO);

   }


   @ApiOperation("null")
   @PostMapping(value="/closeExternalShipBill")
   public void closeExternalShipBill(@RequestBody ExternalWaybillRequestDTO requestDTO){
      iShipBillExternalService.closeExternalShipBill(requestDTO);

   }


   @ApiOperation("ERP反馈开仓卸货结果")
   @PostMapping(value="/openCabinCallback")
   public ItemResult<Void> openCabinCallback(@RequestBody ItemResult<ERPShipBillDTO> itemResult){
      return iShipBillExternalService.openCabinCallback(itemResult);
   }


   @ApiOperation("null")
   @PostMapping(value="/closeExternalShipBillCallback")
   public ItemResult<Void> closeExternalShipBillCallback(@RequestBody ItemResult<ShipBillCloseERPCallbackDTO> itemResult){
      return iShipBillExternalService.closeExternalShipBillCallback(itemResult);
   }


   @ApiOperation("null")
   @PostMapping(value="/createExternalShipBillCallback")
   public ItemResult<Void> createExternalShipBillCallback(@RequestBody ItemResult<ShipBillERPCallbackDTO> itemResult){
      return iShipBillExternalService.createExternalShipBillCallback(itemResult);
   }


   @ApiOperation("null")
   @PostMapping(value="/modifyExternalShipBill")
   public void modifyExternalShipBill(@RequestBody ExternalWaybillRequestDTO requestDTO){
      iShipBillExternalService.modifyExternalShipBill(requestDTO);

   }


   @ApiOperation("null")
   @PostMapping(value="/modifyExternalShipBillCallback")
   public ItemResult<Void> modifyExternalShipBillCallback(@RequestBody ItemResult<ShipBillModifyERPCallbackDTO> itemResult){
      return iShipBillExternalService.modifyExternalShipBillCallback(itemResult);
   }


   @ApiOperation("ERP通知电商扣款")
   @PostMapping(value="/erpNotifyPay")
   public ItemResult<ERPShipBillDTO> erpNotifyPay(@RequestBody ERPShipBillDTO erpShipBillDTO){
      return iShipBillExternalService.erpNotifyPay(erpShipBillDTO);
   }


   @ApiOperation("电商通知ERP开仓卸货")
   @PostMapping(value="/notifyOpenCabin")
   public ItemResult<Void> notifyOpenCabin(@RequestParam("waybillId") String waybillId){
      return iShipBillExternalService.notifyOpenCabin(waybillId);
   }


   @ApiOperation("修改电商船运单数据")
   @PostMapping(value="/updateECShipBill")
   public ItemResult<Void> updateECShipBill(@RequestBody ERPShipBillDTO erpShipBillDTO){
      return iShipBillExternalService.updateECShipBill(erpShipBillDTO);
   }



}
