package com.ecommerce.logistics.controller;

import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.logistics.api.dto.SysConfigDTO;
import com.ecommerce.logistics.api.dto.shipping.erp.BindErpShippingDTO;
import com.ecommerce.logistics.api.dto.shipping.erp.ErpShipSyncDTO;
import com.ecommerce.logistics.api.dto.shipping.erp.ErpShippingInfoDTO;
import com.ecommerce.logistics.service.IShippingErpInfoService;
import com.ecommerce.open.api.dto.apicenter.config.SysConfigResultDTO;
import com.ecommerce.open.api.service.apicenter.ISysConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/1/11 17:55
 * @Version 1.0
 * ERP承运商船舶同步服务
 */
@Slf4j
@Api(tags={"ShippingErpInfo"})
@RestController
@RequestMapping("/shippingErpInfo")
public class ShippingErpInfoController {

    @Autowired
    private IShippingErpInfoService shippingErpInfoService;

    @Autowired
    private ISysConfigService iSysConfigService;

    @ApiOperation("查找ERP船舶列表")
    @PostMapping(value="/findErpShippingByErpCarrierNo")
    public ItemResult<List<ErpShippingInfoDTO>> findErpShippingByErpCarrierNo(@RequestParam("erpCarrierNo") String erpCarrierNo,
                                                                              @RequestParam("manufacturerMemberId") String manufacturerMemberId) {
        return shippingErpInfoService.findErpShippingByErpCarrierNo(erpCarrierNo,manufacturerMemberId);
    }

    @ApiOperation("承运商船舶信息同步")
    @PostMapping(value="/syncErpInfo")
    public ItemResult<Boolean> syncErpInfo(@RequestBody ErpShipSyncDTO erpShipSyncDTO) throws BizException {
        return shippingErpInfoService.syncErpInfo(erpShipSyncDTO);
    }

    @ApiOperation("电商船舶和ERP船舶绑定")
    @PostMapping(value="/bindErpShippingInfo")
    public ItemResult<Boolean> bindErpShippingInfo(@RequestBody BindErpShippingDTO bindErpShippingDTO) throws BizException {
        return shippingErpInfoService.bindErpShippingInfo(bindErpShippingDTO);
    }

    @ApiOperation("查询所有ERP系统")
    @PostMapping(value="/querySysConfig")
    public ItemResult<List<SysConfigDTO>> querySysConfig() throws BizException{
        List<SysConfigDTO> sysConfigDTOS = new LinkedList<>();
        ItemResult<List<SysConfigResultDTO>> itemResult = iSysConfigService.querySysConfig();
        log.info("返回的结果是：{}", itemResult);
        if (itemResult != null && CollectionUtils.isNotEmpty(itemResult.getData())) {
            List<SysConfigResultDTO> list = itemResult.getData();
            list.forEach(item -> {
                SysConfigDTO dto = new SysConfigDTO();
                BeanUtils.copyProperties(item, dto);
                sysConfigDTOS.add(dto);
            });
        }
        return new ItemResult<>(sysConfigDTOS);
    }

}
