package com.ecommerce.logistics.controller;

import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.shipping.*;
import com.ecommerce.logistics.api.dto.shipping.erp.BindErpShippingDTO;
import com.ecommerce.logistics.service.IShippingInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 船舶管理服务
 * Created by he<PERSON><PERSON> on 2020/11/23 11:25
 */
@Slf4j
@Api(tags={"ShippingInfo"})
@RestController
@RequestMapping("/shippingInfo")
public class ShippingInfoController {

    @Autowired
    private IShippingInfoService shippingInfoService;

    @ApiOperation("保存，不需要做数据必填校验")
    @PostMapping(value="/saveShippingInfo")
    public ItemResult<String> saveShippingInfo(@RequestBody ShippingInfoSaveDTO dto) throws BizException{
        return shippingInfoService.saveShippingInfo(dto);
    }

    @ApiOperation("删除船舶")
    @PostMapping(value="/delShippingInfo")
    public ItemResult<Void> delShippingInfo(@RequestBody ShippingBatchDTO dto) throws BizException{
        return shippingInfoService.delShippingInfo(dto);
    }

    @ApiOperation("提交，需要做数据必填校验")
    @PostMapping(value="/submitShippingInfo")
    public ItemResult<String> submitShippingInfo(@RequestBody @Valid ShippingInfoSaveDTO dto) throws BizException {
        return shippingInfoService.submitShippingInfo(dto);
    }

    @ApiOperation("审核通过")
    @PostMapping(value="/passShippingInfo")
    public ItemResult<Void> passShippingInfo(@RequestBody @Valid ShippingOperationDTO dto) throws BizException {
        return shippingInfoService.passShippingInfo(dto);
    }

    @ApiOperation("审核驳回")
    @PostMapping(value="/rejectShippingInfo")
    public ItemResult<Void> rejectShippingInfo(@RequestBody @Valid ShippingOperationDTO dto) throws BizException {
        return shippingInfoService.rejectShippingInfo(dto);
    }

    @ApiOperation("平台查询船舶列表")
    @PostMapping(value="/queryShippingInfoListByPlatform")
    public ItemResult<PageData<ShippingInfoListDTO>> queryShippingInfoListByPlatform(@RequestBody PageQuery<ShippingInfoListQueryDTO> pageQuery) throws BizException {
        return shippingInfoService.queryShippingInfoListByPlatform(pageQuery);
    }

    @ApiOperation("查询当前用户名下船舶列表")
    @PostMapping(value="/queryShippingInfoListByCarrier")
    public ItemResult<PageData<ShippingInfoListDTO>> queryShippingInfoListByCarrier(@RequestBody PageQuery<ShippingInfoListQueryDTO> pageQuery) throws BizException {
        return shippingInfoService.queryShippingInfoListByCarrier(pageQuery);
    }

    @ApiOperation("买家，卖家查询我名下和授权给我的船舶列表")
    @PostMapping(value="/queryShippingInfoListBySeller")
    public ItemResult<PageData<ShippingInfoListDTO>> queryShippingInfoListBySeller(@RequestBody PageQuery<ShippingInfoListQueryDTO> pageQuery) throws BizException {
        return shippingInfoService.queryShippingInfoListBySeller(pageQuery);
    }

    @ApiOperation("查询我名下和授权给我的可指派船舶列表")
    @PostMapping(value="/queryShippingInfoListBySelf")
    public ItemResult<PageData<ShippingInfoDTO>> queryShippingInfoListBySelf(@RequestBody PageQuery<ShippingInfoQueryDTO> pageQuery) throws BizException {
        return shippingInfoService.queryShippingInfoListBySelf(pageQuery);
    }

    @ApiOperation("买家查询船舶详情")
    @PostMapping(value="/queryShippingInfoDetailsByBuyer")
    public ItemResult<ShippingInfoDetailsDTO> queryShippingInfoDetailsByBuyer(@RequestParam("shippingId") String shippingId) throws BizException {
        return shippingInfoService.queryShippingInfoDetailsByBuyer(shippingId);
    }

    @ApiOperation("查询船舶详情")
    @PostMapping(value="/queryShippingInfoDetails")
    public ItemResult<ShippingInfoDetailsDTO> queryShippingInfoDetails(@RequestParam("shippingId") String shippingId) throws BizException {
        return shippingInfoService.queryShippingInfoDetails(shippingId);
    }

    @ApiOperation("多艘船舶授权一个卖家接口")
    @PostMapping(value="/batchAuthorizeSeller")
    public ItemResult<Void> batchAuthorizeSeller(@RequestBody @Valid ShippingBatchAuthorizeDTO dto) throws BizException {
        return shippingInfoService.batchAuthorizeSeller(dto);
    }

    @ApiOperation("批量取消授权")
    @PostMapping(value="/batchCancelAuthorizeSeller")
    public ItemResult<Void> batchCancelAuthorizeSeller(@RequestBody @Valid ShippingBatchAuthorizeDTO dto) throws BizException {
        return shippingInfoService.batchCancelAuthorizeSeller(dto);
    }

    @ApiOperation("批量船舶检修")
    @PostMapping(value="/shippingInfoOverhaul")
    public ItemResult<Void> shippingInfoOverhaul(@RequestBody @Valid ShippingBatchDTO dto) throws BizException {
        return shippingInfoService.shippingInfoOverhaul(dto);
    }

    @ApiOperation("绑定船长,如果已绑定船长则先解绑")
    @PostMapping(value="/bindCaptain")
    public ItemResult<Void> bindCaptain(@RequestBody @Valid BindCaptainDTO dto) throws BizException {
        return shippingInfoService.bindCaptain(dto);
    }

    @ApiOperation("船舶名称校验")
    @PostMapping(value="/shippingNameValid")
    public ItemResult<Boolean> shippingNameValid(@RequestParam("shippingName") String shippingName) throws BizException {
        return shippingInfoService.shippingNameValid(shippingName);
    }

    @ApiOperation("禁用或启用船舶")
    @PostMapping(value="/disableShippingInfo")
    public ItemResult<Void> disableShippingInfo(@RequestBody @Valid ShippingOperationDTO dto) {
        return shippingInfoService.disableShippingInfo(dto);
    }
    @ApiOperation("修改单个服务类型")
    @PostMapping(value="/updateServerType")
    public ItemResult<Void> updateServerType(@RequestBody @Valid EditServerTypeDTO dto)
            throws BizException {
        return shippingInfoService.updateServerType(dto);
    }

    @ApiOperation("批量修改服务类型")
    @PostMapping(value="/batchFixedTemporary")
    public ItemResult<Void> batchFixedTemporary(@RequestBody @Valid ShippingBatchDTO dto) {
        return shippingInfoService.batchFixedTemporary(dto);
    }

    @ApiOperation("一艘船授权批量卖家接口")
    @PostMapping(value="/authorizeSellerList")
    public ItemResult<Void> authorizeSellerList(@RequestBody @Valid AuthorizeSellerBatchDTO dto) {
        return shippingInfoService.authorizeSellerList(dto);
    }

    @ApiOperation("查询已经授权的卖家列表")
    @PostMapping(value="/getAuthorizeSellerList")
    public ItemResult<List<AuthorizeSellerDTO>> authorizeSellerListInfo(@RequestParam("shippingId") String shippingId) {
        return shippingInfoService.authorizeSellerListInfo(shippingId);
    }


    @ApiOperation("修改船舶状态")
    @PostMapping(value="/updateShippingStatus")
    public ItemResult<Void> updateShippingStatus(@RequestBody @Valid ShippingOperationDTO dto) {
        return shippingInfoService.updateShippingStatus(dto);
    }

    @ApiOperation("通过账号名查询船长绑定记录列表")
    @PostMapping(value="/queryBindCaptainLog")
    public ItemResult<List<BindCaptainLogDTO>> queryBindCaptainLog(@RequestParam("captainAccountName") String captainAccountName){
        return shippingInfoService.queryBindCaptainLog(captainAccountName);
    }

    @ApiOperation("获取船务公司集合")
    @PostMapping(value="/getShippingCompanyList")
    public ItemResult<List<String>> getShippingCompanyList(@RequestParam(value = "shippingCompany",required = false) String shippingCompany){
        return shippingInfoService.getShippingCompanyList(shippingCompany);
    }

    @ApiOperation("批量恢复运输")
    @PostMapping(value="/batchRecoveryTransport")
    public ItemResult<Void> batchRecoveryTransport(@RequestBody ShippingBatchDTO dto) throws BizException {
        return shippingInfoService.batchRecoveryTransport(dto);
    }

    @ApiOperation("会员Id查询船舶信息（APP专用）")
    @PostMapping(value="/queryShippingInfoByMemberId")
    public ItemResult<ShippingInfoDetailsDTO> queryShippingInfoByMemberId(@ApiParam("个体船东会员Id") @RequestParam("memberId") String memberId) throws BizException {
        return shippingInfoService.queryShippingInfoByMemberId(memberId);
    }

    @ApiOperation("根据会员Id判断是否为个体船东")
    @PostMapping(value="/isPersonalShipowner")
    public ItemResult<Boolean> isPersonalShipowner(@ApiParam("个体船东会员Id") @RequestParam("memberId") String memberId) throws BizException {
        return shippingInfoService.isPersonalShipowner(memberId);
    }

    @ApiOperation("查询所有审核通过的个体船东信息")
    @PostMapping(value="/queryPersonalShippingInfo")
    public ItemResult<List<ShippingInfoDTO>> queryPersonalShippingInfo(@ApiParam("船舶名称") @RequestParam("shippingName") String shippingName) throws BizException {
        return shippingInfoService.queryPersonalShippingInfo(shippingName);
    }

    @ApiOperation("查询所有审核通过的承运商授权给平台的船舶")
    @PostMapping(value="/queryPlatformShippingInfo")
    public ItemResult<List<ShippingInfoDTO>> queryPlatformShippingInfo(@ApiParam("船舶名称") @RequestParam("shippingName") String shippingName) throws BizException {
        return shippingInfoService.queryPlatformShippingInfo(shippingName);
    }


    @ApiOperation("获取ERP船舶绑定列表")
    @PostMapping(value="/queryErpShippingBindList")
    public List<BindErpShippingDTO> queryErpShippingBindList(@ApiParam("船舶ID") @RequestParam("shippingId") String shippingId) throws BizException {
        return shippingInfoService.queryErpShippingBindList(shippingId);
    }

    @ApiOperation("模糊查询船名")
    @PostMapping(value="/selectShippingInfoName")
    public ItemResult<List<String>> selectShippingInfoName(@RequestBody ShippingInfoListQueryDTO queryDTO){
		return shippingInfoService.selectShippingInfoName(queryDTO);
    }


    @ApiOperation("内部调拨时的船舶列表")
    @PostMapping(value="/queryAllocationShippingInfo")
    public ItemResult<List<ShippingInfoListDTO>> queryAllocationShippingInfo(@RequestParam("memberId") String memberId,
                                                                             @RequestParam("carrierId")String carrierId,
                                                                             @RequestParam(value ="shippingName",defaultValue = "false")String shippingName){
		return shippingInfoService.queryAllocationShippingInfo(memberId, carrierId, shippingName);
    }
}
