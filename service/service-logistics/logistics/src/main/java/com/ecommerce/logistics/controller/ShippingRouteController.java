package com.ecommerce.logistics.controller;

import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.shippingroute.*;
import com.ecommerce.logistics.api.dto.shippingrule.QueryShippingRuleDTO;
import com.ecommerce.logistics.api.dto.shippingrule.ShippingRuleResultDTO;
import com.ecommerce.logistics.service.IShippingRouteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 航线运费定价服务
 * Created by he<PERSON><PERSON> on 2020/11/9 10:30
 */
@Slf4j
@Api(tags={"ShippingRoute"})
@RestController
@RequestMapping("/shippingRoute")
public class ShippingRouteController {
    
    @Autowired
    private IShippingRouteService shippingRouteService;

    @ApiOperation("添加航线运费定价")
    @PostMapping(value="/addShippingRoute")
    public ItemResult<String> addShippingRoute(@Valid @RequestBody ShippingRouteAddDTO dto) throws BizException {
        return shippingRouteService.addShippingRoute(dto);
    }

    @ApiOperation("删除航线运费定价")
    @PostMapping(value="/delShippingRoute")
    public ItemResult<Void> delShippingRoute(@RequestParam("belongerId") String belongerId,
                                             @RequestParam("shippingRouteId") String shippingRouteId,
                                             @RequestParam("operatorUserId") String operatorUserId) throws BizException {
        return shippingRouteService.delShippingRoute(belongerId, shippingRouteId, operatorUserId);
    }

    @ApiOperation("编辑航线运费定价")
    @PostMapping(value="/editShippingRoute")
    public ItemResult<Void> editShippingRoute(@Valid @RequestBody ShippingRouteEditDTO dto) throws BizException {
        return shippingRouteService.editShippingRoute(dto);
    }

    @ApiOperation("查询航线运费定价详情")
    @PostMapping(value="/queryShippingRouteDetails")
    public ItemResult<ShippingRouteDetailsDTO> queryShippingRouteDetails(@RequestParam("belongerId") String belongerId,
                                                                         @RequestParam("shippingRouteId")String shippingRouteId) throws BizException {
        return shippingRouteService.queryShippingRouteDetails(belongerId, shippingRouteId);
    }

    @ApiOperation("查询航线运费定价列表")
    @PostMapping(value="/queryShippingRouteList")
    public ItemResult<PageData<ShippingRouteListDTO>> queryShippingRouteList(@RequestBody PageQuery<ShippingRouteQueryDTO> pageQuery) throws BizException {
        return shippingRouteService.queryShippingRouteList(pageQuery);
    }

    @ApiOperation("删除航线运价付费")
    @PostMapping(value="/delFarePayment")
    public ItemResult<Void> delFarePayment(@RequestParam("shippingRouteId")String shippingRouteId,
                                           @RequestParam("shippingFarePaymentId")String shippingFarePaymentId,
                                           @RequestParam("operatorUserId")String operatorUserId,
                                           @RequestParam("operatorUserName") String operatorUserName) throws BizException {
        return shippingRouteService.delFarePayment(shippingRouteId, shippingFarePaymentId, operatorUserId, operatorUserName);
    }


    @ApiOperation("下载操作记录")
    @PostMapping(value="/downloadShippingRouteOperateLog")
    public ItemResult<List<ShippingAuxiliaryOperateLogDTO>> downloadShippingRouteOperateLog(@RequestParam("shippingRouteId")String shippingRouteId) throws BizException {
        return shippingRouteService.downloadShippingRouteOperateLog(shippingRouteId);
    }


    @ApiOperation("交易航运运费规则查询")
    @PostMapping(value="/queryShippingRule")
    public ItemResult<List<ShippingRuleResultDTO>> queryShippingRule(@RequestBody @Valid QueryShippingRuleDTO queryShippingRuleDTO)
            throws BizException {
        return shippingRouteService.queryShippingRule(queryShippingRuleDTO);
    }


    @ApiOperation("查询航运付费规则")
    @PostMapping(value="/queryShippingPaymentRule")
    public ItemResult<List<ShippingRuleResultDTO>> queryShippingPaymentRule(@RequestBody @Valid QueryShippingRuleDTO queryShippingRuleDTO)
            throws BizException {
        return shippingRouteService.queryShippingPaymentRule(queryShippingRuleDTO);
    }


    @ApiOperation("获取当前卖家的客户收货码头地址列表")
    @PostMapping(value="/queryUnloadingWharf")
    public ItemResult<Map<String, String>> queryUnloadingWharf(@RequestParam("belongerId") String belongerId) throws BizException {
        return shippingRouteService.queryUnloadingWharf(belongerId);
    }

    @ApiOperation("获取当前卖家的客户装货码头地址列表")
    @PostMapping(value="/queryPickingWharf")
    public ItemResult<Map<String, String>> queryPickingWharf(@RequestParam("belongerId") String belongerId) throws BizException {
        return shippingRouteService.queryPickingWharf(belongerId);
    }
}
