package com.ecommerce.logistics.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.transportaddress.TransportAddressDeleteDTO;
import com.ecommerce.logistics.api.dto.transportaddress.TransportAddressDetailDTO;
import com.ecommerce.logistics.api.dto.transportaddress.TransportAddressListDTO;
import com.ecommerce.logistics.api.dto.transportaddress.TransportAddressQueryDTO;
import com.ecommerce.logistics.api.dto.transportaddress.TransportAddressSaveDTO;
import com.ecommerce.logistics.api.dto.transportaddress.TransportAddressSearchDTO;
import com.ecommerce.logistics.service.ITransportAddressService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Author: <EMAIL>
 * Description:
 * Date: Create in 下午2:23 18/12/21
 * 运输地址服务
 */
@Api(tags={"TransportAddress"})
@RestController
@RequestMapping("/transportAddress")
public class TransportAddressController {
    @Autowired
    private ITransportAddressService transportAddressService;

    @ApiOperation("添加运输地址")
    @PostMapping(value="/addTransportAddress")
    public ItemResult<String> addTransportAddress(@RequestBody TransportAddressSaveDTO transportAddressSaveDTO) {
        return transportAddressService.addTransportAddress(transportAddressSaveDTO);
    }

    @ApiOperation("删除运输地址")
    @PostMapping(value="/deleteTransportAddress")
    public ItemResult<Void> deleteTransportAddress(@RequestBody TransportAddressDeleteDTO transportAddressDeleteDTO) {
        return transportAddressService.deleteTransportAddress(transportAddressDeleteDTO);
    }

    @ApiOperation("编辑运输地址")
    @PostMapping(value="/editTransportAddress")
    public ItemResult<Void> editTransportAddress(@RequestBody TransportAddressSaveDTO transportAddressSaveDTO) {
        return transportAddressService.editTransportAddress(transportAddressSaveDTO);
    }

    @ApiOperation("查询运输地址详情")
    @PostMapping(value="/queryTransportAddressDetail")
    public ItemResult<TransportAddressDetailDTO> queryTransportAddressDetail(@ApiParam("运输地址ID") @RequestParam("transportAddressId") String transportAddressId) {
        return transportAddressService.queryTransportAddressDetail(transportAddressId);
    }

    @ApiOperation("查询运输地址列表")
    @PostMapping(value="/queryTransportAddressList")
    public ItemResult<PageData<TransportAddressListDTO>> queryTransportAddressList(@RequestBody PageQuery<TransportAddressQueryDTO> pageQuery) {
        return transportAddressService.queryTransportAddressList(pageQuery);
    }

    @ApiOperation("搜索运输地址列表")
    @PostMapping(value="/searchTransportAddressList")
    public ItemResult<List<TransportAddressListDTO>> searchTransportAddressList(@RequestBody TransportAddressSearchDTO transportAddressSearchDTO) {
        return transportAddressService.searchTransportAddressList(transportAddressSearchDTO);
    }
}
