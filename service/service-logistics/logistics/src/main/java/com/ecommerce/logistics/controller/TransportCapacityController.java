package com.ecommerce.logistics.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.transportcapacity.TransportCapacityDeleteDTO;
import com.ecommerce.logistics.api.dto.transportcapacity.TransportCapacityDetailDTO;
import com.ecommerce.logistics.api.dto.transportcapacity.TransportCapacityListDTO;
import com.ecommerce.logistics.api.dto.transportcapacity.TransportCapacityQueryDTO;
import com.ecommerce.logistics.api.dto.transportcapacity.TransportCapacitySaveDTO;
import com.ecommerce.logistics.api.dto.transportcapacity.TransportCapacityViewQueryDTO;
import com.ecommerce.logistics.service.ITransportCapacityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Author: <EMAIL>
 * Description:
 * Date: Create in 下午5:17 18/12/19
 * 运力服务
 */
@Api(tags={"TransportCapacity"})
@RestController
@RequestMapping("/transportCapacity")
public class TransportCapacityController {

    @Autowired
    private ITransportCapacityService transportCapacityService;

    @ApiOperation("发布运力")
    @PostMapping(value="/addTransportCapacity")
    public ItemResult<String> addTransportCapacity(@RequestBody TransportCapacitySaveDTO transportCapacitySaveDTO){
        return transportCapacityService.addTransportCapacity(transportCapacitySaveDTO);
    }

    @ApiOperation("删除运力")
    @PostMapping(value="/deleteTransportCapacity")
    public ItemResult<Void> deleteTransportCapacity(@RequestBody TransportCapacityDeleteDTO transportCapacityDeleteDTO){
        return transportCapacityService.deleteTransportCapacity(transportCapacityDeleteDTO);
    }

    @ApiOperation("编辑运力")
    @PostMapping(value="/editTransportCapacity")
    public ItemResult<Void> editTransportCapacity(@RequestBody TransportCapacitySaveDTO transportCapacitySaveDTO ){
        return transportCapacityService.editTransportCapacity(transportCapacitySaveDTO);
    }

    @ApiOperation("查询运力详情")
    @PostMapping(value="/queryTransportCapacityDetail")
    public ItemResult<TransportCapacityDetailDTO> queryTransportCapacityDetail(@ApiParam("运力ID") @RequestParam("transportCapacityId") String transportCapacityId){
        return transportCapacityService.queryTransportCapacityDetail(transportCapacityId);
    }

    @ApiOperation("查询运力列表")
    @PostMapping(value="/queryTransportCapacityList")
    public ItemResult<PageData<TransportCapacityListDTO>> queryTransportCapacityList(@RequestBody PageQuery<TransportCapacityQueryDTO> pageQuery){
        return transportCapacityService.queryTransportCapacityList(pageQuery);
    }

    @ApiOperation("查询运力浏览记录")
    @PostMapping(value="/queryTransportCapacityViewRecord")
    public ItemResult<List<TransportCapacityListDTO>> queryTransportCapacityViewRecord(@RequestBody TransportCapacityViewQueryDTO transportCapacityViewQueryDTO){
        return transportCapacityService.queryTransportCapacityViewRecord(transportCapacityViewQueryDTO);
    }
}
