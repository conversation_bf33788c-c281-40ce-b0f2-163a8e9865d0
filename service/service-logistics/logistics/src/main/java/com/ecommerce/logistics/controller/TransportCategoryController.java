package com.ecommerce.logistics.controller;

import com.ecommerce.logistics.api.dto.transportcategory.CategoryColDTO;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.ecommerce.logistics.service.ITransportCategoryService;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.logistics.api.dto.transportcategory.TransportCategoryListDTO;
import com.ecommerce.logistics.api.dto.transportcategory.TransportCategoryListQueryDTO;
import java.util.List;
import com.ecommerce.logistics.api.dto.transportcategory.TransportOption;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.transportcategory.TransportCategoryDTO;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.transportcategory.TransportCategoryEditDTO;
import com.ecommerce.logistics.api.dto.transportcategory.TransportCategoryRemoveDTO;
import com.ecommerce.logistics.api.dto.transportcategory.TransportCategoryAddDTO;
import com.ecommerce.logistics.api.dto.transportcategory.TransportCategoryDetailDTO;


/**
 * @Created锛�Mon Nov 26 21:18:45 CST 2018
 * <AUTHOR>
 * @Version:2
 * @Description:: 运输品类
*/

@Api(tags={"TransportCategory"})
@RestController
@RequestMapping("/transportCategory")
public class TransportCategoryController {

   @Autowired 
   private ITransportCategoryService iTransportCategoryService;

   @ApiOperation("查询所有品类")
   @PostMapping(value="/queryAll")
   public ItemResult<List<TransportCategoryDTO>> queryAll(){
      return iTransportCategoryService.queryAll();
   }


   @ApiOperation("查询运输品类 下拉列表")
   @PostMapping(value="/queryOptions")
   public ItemResult<List<TransportOption>> queryOptions(){
      return iTransportCategoryService.queryOptions();
   }


   @ApiOperation("查询运输品类列表")
   @PostMapping(value="/queryTransportCategoryListById")
   public ItemResult<List<TransportCategoryDTO>> queryTransportCategoryListById(@ApiParam("运输品类ID列表") @RequestBody List<String> transportCategoryIdList){
      return iTransportCategoryService.queryTransportCategoryListById(transportCategoryIdList);
   }


   @ApiOperation("通过运输品类Id查询 运输品类")
   @PostMapping(value="/queryTransportCategory")
   public ItemResult<TransportCategoryDetailDTO> queryTransportCategory(@ApiParam("运输品类ID") @RequestParam("transportCategoryId") String transportCategoryId){
      return iTransportCategoryService.queryTransportCategory(transportCategoryId);
   }


   @ApiOperation("新增运输品类")
   @PostMapping(value="/addTransportCategory")
   public ItemResult<Void> addTransportCategory(@RequestBody TransportCategoryAddDTO transportCategoryAddDTO){
      return iTransportCategoryService.addTransportCategory(transportCategoryAddDTO);
   }


   @ApiOperation("逻辑删除运输品类")
   @PostMapping(value="/removeTransportCategory")
   public ItemResult<Void> removeTransportCategory(@RequestBody TransportCategoryRemoveDTO transportCategoryRemoveDTO){
      return iTransportCategoryService.removeTransportCategory(transportCategoryRemoveDTO);
   }


   @ApiOperation("运输品类合并过滤")
   @PostMapping(value="/transportCategoryMergeFilter")
   public ItemResult<List<String>> transportCategoryMergeFilter(@ApiParam("运输品类ID集合") @RequestBody List<String> transportCategoryIdList){
      return iTransportCategoryService.transportCategoryMergeFilter(transportCategoryIdList);
   }


   @ApiOperation("修改运输品类")
   @PostMapping(value="/modifyTransportCategory")
   public ItemResult<Void> modifyTransportCategory(@RequestBody TransportCategoryEditDTO transportCategoryEditDTO){
      return iTransportCategoryService.modifyTransportCategory(transportCategoryEditDTO);
   }


   @ApiOperation("获取运输品类列表")
   @PostMapping(value="/queryTransportCategoryList")
   public ItemResult<PageData<TransportCategoryListDTO>> queryTransportCategoryList(@RequestBody PageQuery<TransportCategoryListQueryDTO> pageQuery){
      return iTransportCategoryService.queryTransportCategoryList(pageQuery);
   }

   @ApiOperation("查询运单号对应运输品类")
   @PostMapping(value = "/queryWaybillTransportCategoryMap")
   public ItemResult<List<CategoryColDTO>> queryWaybillTransportCategoryMap(@ApiParam("运单号集合") @RequestBody List<String> waybillNumList)  {
      return iTransportCategoryService.queryWaybillTransportCategoryMap(waybillNumList);
   }

}
