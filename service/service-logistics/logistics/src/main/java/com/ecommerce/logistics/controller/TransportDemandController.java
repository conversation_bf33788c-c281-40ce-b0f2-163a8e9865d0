package com.ecommerce.logistics.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.transportdemand.TransportDemandAuditDTO;
import com.ecommerce.logistics.api.dto.transportdemand.TransportDemandDeleteDTO;
import com.ecommerce.logistics.api.dto.transportdemand.TransportDemandDetailDTO;
import com.ecommerce.logistics.api.dto.transportdemand.TransportDemandListDTO;
import com.ecommerce.logistics.api.dto.transportdemand.TransportDemandQueryDTO;
import com.ecommerce.logistics.api.dto.transportdemand.TransportDemandSaveDTO;
import com.ecommerce.logistics.api.dto.transportdemand.TransportDemandViewQueryDTO;
import com.ecommerce.logistics.service.ITransportDemandService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Author: <EMAIL>
 * Description:
 * Date: Create in 上午11:29 18/12/21
 * 运输需求服务
 */
@Api(tags={"TransportDemand"})
@RestController
@RequestMapping("/transportDemand")
public class TransportDemandController {

    @Autowired
    private ITransportDemandService transportDemandService;

    @ApiOperation("发布运输需求")
    @PostMapping(value="/addTransportDemand")
    public ItemResult<String> addTransportDemand(@RequestBody TransportDemandSaveDTO transportDemandSaveDTO) {
        return transportDemandService.addTransportDemand(transportDemandSaveDTO);
    }

    @ApiOperation("删除运输需求")
    @PostMapping(value="/deleteTransportDemand")
    public ItemResult<Void> deleteTransportDemand(@RequestBody TransportDemandDeleteDTO transportDemandDeleteDTO) {
        return transportDemandService.deleteTransportDemand(transportDemandDeleteDTO);
    }

    @ApiOperation("编辑运输需求")
    @PostMapping(value="/editTransportDemand")
    public ItemResult<Void> editTransportDemand(@RequestBody TransportDemandSaveDTO transportDemandSaveDTO) {
        return transportDemandService.editTransportDemand(transportDemandSaveDTO);
    }

    @ApiOperation("查询运输需求详情")
    @PostMapping(value="/queryTransportDemandDetail")
    public ItemResult<TransportDemandDetailDTO> queryTransportDemandDetail(@ApiParam("运输需求ID") @RequestParam("transportDemandId") String transportDemandId) {
        return transportDemandService.queryTransportDemandDetail(transportDemandId);
    }

    @ApiOperation("查询运输需求列表")
    @PostMapping(value="/queryTransportDemandList")
    public ItemResult<PageData<TransportDemandListDTO>> queryTransportDemandList(@RequestBody PageQuery<TransportDemandQueryDTO> pageQuery) {
        return transportDemandService.queryTransportDemandList(pageQuery);
    }

    @ApiOperation("运输需求审核")
    @PostMapping(value="/transportDemandAudit")
    public ItemResult<Void> transportDemandAudit(@RequestBody TransportDemandAuditDTO transportDemandAuditDTO) {
        return transportDemandService.transportDemandAudit(transportDemandAuditDTO);
    }

    @ApiOperation("查询运输需求浏览记录")
    @PostMapping(value="/queryTransportDemandViewRecord")
    public ItemResult<List<TransportDemandListDTO>> queryTransportDemandViewRecord(@RequestBody TransportDemandViewQueryDTO transportDemandViewQueryDTO) {
        return transportDemandService.queryTransportDemandViewRecord(transportDemandViewQueryDTO);
    }
}
