package com.ecommerce.logistics.controller;

import com.ecommerce.base.api.dto.warehouse.PageQuery;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.contract.*;
import com.ecommerce.logistics.service.ITrusteeContractInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 托运合同服务
 * Created by hexinhui on 2020/10/20 14:51
 */
@Api(tags = {"TrusteeContract"})
@RestController
@RequestMapping("/trusteeContract")
public class TrusteeContractInfoController {

    @Autowired
    private ITrusteeContractInfoService contractInfoService;

    @ApiOperation("新增托运合同")
    @PostMapping(value = "/addTrusteeContractInfo")
    public ItemResult<String> addTrusteeContractInfo(@Valid @RequestBody TrusteeContractAddDTO contractAddDTO) {
        return contractInfoService.addTrusteeContractInfo(contractAddDTO);
    }

    @ApiOperation("删除托运合同")
    @PostMapping(value = "/delTrusteeContractInfo")
    public ItemResult<Void> delTrusteeContractInfo(@ApiParam("合同主键数组") @RequestParam("contractIds") String contractIds,
                                                   @ApiParam("操作人id") @RequestParam("operatorUserId") String operatorUserId,
                                                   @ApiParam("操作人姓名") @RequestParam("operatorUserName") String operatorUserName) {
        return contractInfoService.delTrusteeContractInfo(contractIds, operatorUserId, operatorUserName);
    }

    @ApiOperation("修改托运合同")
    @PostMapping(value = "/editTrusteeContractInfo")
    public ItemResult<Void> editTrusteeContractInfo(@Valid @RequestBody TrusteeContractEditDTO contractEditDTO) {
        return contractInfoService.editTrusteeContractInfo(contractEditDTO);
    }

    @ApiOperation("查看托运合同详情")
    @PostMapping(value = "/findTrusteeContractInfo")
    public ItemResult<TrusteeContractDetailsDTO> findTrusteeContractInfo(@ApiParam("合同主键") @RequestParam("contractId") String contractId) {
        return contractInfoService.findTrusteeContractDetails(contractId);
    }

    @ApiOperation("查询托运合同列表")
    @PostMapping(value = "/queryTrusteeContractList")
    public ItemResult<PageData<TrusteeContractListDTO>> queryTrusteeContractList(@RequestBody PageQuery<TrusteeContractListQueryDTO> pageQuery) {
        return contractInfoService.queryTrusteeContractList(pageQuery);
    }


}
