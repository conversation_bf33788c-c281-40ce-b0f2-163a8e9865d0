package com.ecommerce.logistics.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.unloadingobjection.ObjectionHandleDetailDTO;
import com.ecommerce.logistics.api.dto.unloadingobjection.UnloadingObjectionAddDTO;
import com.ecommerce.logistics.api.dto.unloadingobjection.UnloadingObjectionDetailDTO;
import com.ecommerce.logistics.api.dto.unloadingobjection.UnloadingObjectionHandleDTO;
import com.ecommerce.logistics.api.dto.unloadingobjection.UnloadingObjectionListDTO;
import com.ecommerce.logistics.api.dto.unloadingobjection.UnloadingObjectionQueryDTO;
import com.ecommerce.logistics.service.IUnloadingObjectionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * @Created：Fri Oct 30 14:44:17 CST 2020
 * <AUTHOR>
 * @Version:2
 * @Description::
 * 卸货异议服务
*/

@Api(tags={"UnloadingObjection"})
@RestController
@RequestMapping("/unloadingObjection")
public class UnloadingObjectionController {

   @Autowired 
   private IUnloadingObjectionService iUnloadingObjectionService;

   @ApiOperation("查询卸货异议详情")
   @PostMapping(value="/findObjectionDetailById")
   public ItemResult<UnloadingObjectionDetailDTO> findObjectionDetailById(@ApiParam("卸货异议Id") @RequestParam("objectionId") String objectionId){
      return iUnloadingObjectionService.findObjectionDetailById(objectionId);
   }


   @ApiOperation("查询卸货异议列表")
   @PostMapping(value="/selectObjectionList")
   public ItemResult<PageData<UnloadingObjectionListDTO>> selectObjectionList(@RequestBody PageQuery<UnloadingObjectionQueryDTO> pageQuery){
      return iUnloadingObjectionService.selectObjectionList(pageQuery);
   }


   @ApiOperation("查询卸货异议处理记录")
   @PostMapping(value="/findHandleDetailById")
   public ItemResult<ObjectionHandleDetailDTO> findHandleDetailById(@ApiParam("卸货异议Id") @RequestParam("objectionId") String objectionId){
      return iUnloadingObjectionService.findHandleDetailById(objectionId);
   }


   @ApiOperation("批量取消卸货异议")
   @PostMapping(value="/batchCancelObjectionByIds")
   public ItemResult<Void> batchCancelObjectionByIds(@RequestBody List<String> ids,@ApiParam("操作人Id") @RequestParam("operator") String operator){
      return iUnloadingObjectionService.batchCancelObjectionByIds(ids,operator);
   }


   @ApiOperation("完成卸货异议")
   @PostMapping(value="/completeObjection")
   public ItemResult<Void> completeObjection(@RequestBody UnloadingObjectionHandleDTO handleDTO){
      return iUnloadingObjectionService.completeObjection(handleDTO);
   }


   @ApiOperation("处理卸货异议")
   @PostMapping(value="/handleObjection")
   public ItemResult<Void> handleObjection(@RequestBody UnloadingObjectionHandleDTO handleDTO){
      return iUnloadingObjectionService.handleObjection(handleDTO);
   }


   @ApiOperation("添加卸货异议")
   @PostMapping(value="/addObjection")
   public ItemResult<Void> addObjection(@RequestBody UnloadingObjectionAddDTO unloadingObjectionAddDTO){
      return iUnloadingObjectionService.addObjection(unloadingObjectionAddDTO);
   }



}
