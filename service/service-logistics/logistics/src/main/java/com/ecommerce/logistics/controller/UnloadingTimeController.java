package com.ecommerce.logistics.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.intelligentDispatch.UnloadingTimeDTO;
import com.ecommerce.logistics.service.IUnloadingTimeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 *卸货时间管理（收货地址）
 */
@Slf4j
@Api(tags = {"UnloadingTimeController"})
@RestController
@RequestMapping("/unloadingTime")
public class UnloadingTimeController {

    @Autowired
    private IUnloadingTimeService unloadingTimeService;

    @ApiOperation("新增卸货时间")
    @PostMapping(value = "/add")
    public ItemResult<Boolean> add(@RequestBody UnloadingTimeDTO dto) {
        return unloadingTimeService.add(dto);
    }

    @ApiOperation("编辑卸货时间")
    @PostMapping(value = "/edit")
    public ItemResult<Boolean> edit(@RequestBody UnloadingTimeDTO dto) {
        return unloadingTimeService.edit(dto);
    }

    @ApiOperation("删除卸货时间")
    @PostMapping(value = "/delete")
    public ItemResult<Boolean> delete(@RequestBody UnloadingTimeDTO dto) {
        return unloadingTimeService.delete(dto);
    }

    @ApiOperation("分页查询卸货时间")
    @PostMapping(value = "/pageUnloadingTime")
    public ItemResult<PageData<UnloadingTimeDTO>> pageUnloadingTime(@RequestBody PageQuery<UnloadingTimeDTO> pageQuery) {
        return unloadingTimeService.pageUnloadingTime(pageQuery);
    }
}
