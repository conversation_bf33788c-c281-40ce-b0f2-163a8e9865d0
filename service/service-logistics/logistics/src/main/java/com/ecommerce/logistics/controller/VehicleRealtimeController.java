package com.ecommerce.logistics.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.ecommerce.logistics.service.IVehicleRealtimeService;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.logistics.api.dto.realtime.VehicleRelationCondDTO;


/**
 * @Created：Wed Jul 22 14:53:55 CST 2020
 * <AUTHOR>
 * @Version:2
 * @Description:: 车辆实时处理的服务
*/

@Api(tags={"VehicleRealtime"})
@RestController
@RequestMapping("/vehicleRealtime")
public class VehicleRealtimeController {

   @Autowired 
   private IVehicleRealtimeService iVehicleRealtimeService;

   @ApiOperation("车辆实时查询条件的过滤处理方法")
   @PostMapping(value="/filterVehicleCond")
   public ItemResult<VehicleRelationCondDTO> filterVehicleCond(@RequestBody VehicleRelationCondDTO vehicleRelationCondDTO){
      return iVehicleRealtimeService.filterVehicleCond(vehicleRelationCondDTO);
   }



}
