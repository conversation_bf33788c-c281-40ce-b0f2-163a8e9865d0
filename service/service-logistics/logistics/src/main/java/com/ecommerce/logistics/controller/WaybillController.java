package com.ecommerce.logistics.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.attachment.AttListDTO;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.pickingbill.PickingBillDTO;
import com.ecommerce.logistics.api.dto.warehouse.WarehouseOptionDTO;
import com.ecommerce.logistics.api.dto.waybill.AppWaybillListQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.AppWaybillQueryConditionsMapDTO;
import com.ecommerce.logistics.api.dto.waybill.AppWaybillStatisticsDTO;
import com.ecommerce.logistics.api.dto.waybill.ArriveWarehouseDTO;
import com.ecommerce.logistics.api.dto.waybill.BatchPassCheckDTO;
import com.ecommerce.logistics.api.dto.waybill.CancelReasonDTO;
import com.ecommerce.logistics.api.dto.waybill.CancelWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.CarrierWaybillListDTO;
import com.ecommerce.logistics.api.dto.waybill.CarrierWaybillListQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.CarryWaybillSubmitDTO;
import com.ecommerce.logistics.api.dto.waybill.ChangeCarriageDTO;
import com.ecommerce.logistics.api.dto.waybill.ChangePriceDTO;
import com.ecommerce.logistics.api.dto.waybill.CheckWaybillListQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.CloseWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.CompleteWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.DiscardWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.DistrictListDTO;
import com.ecommerce.logistics.api.dto.waybill.DriverCancelWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.DriverConfirmDTO;
import com.ecommerce.logistics.api.dto.waybill.DriverDeliveryDTO;
import com.ecommerce.logistics.api.dto.waybill.DriverWaybillAssignDTO;
import com.ecommerce.logistics.api.dto.waybill.DriverWaybillListQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.EnterFactoryDTO;
import com.ecommerce.logistics.api.dto.waybill.EvaluateWaybillListDTO;
import com.ecommerce.logistics.api.dto.waybill.LeaveWarehouseDTO;
import com.ecommerce.logistics.api.dto.waybill.LocationDTO;
import com.ecommerce.logistics.api.dto.waybill.MergeWaybillListDTO;
import com.ecommerce.logistics.api.dto.waybill.MergeWaybillListQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.MonitorTimeDTO;
import com.ecommerce.logistics.api.dto.waybill.NotifyLeaveWarehouseDTO;
import com.ecommerce.logistics.api.dto.waybill.PassCheckDTO;
import com.ecommerce.logistics.api.dto.waybill.PublishWaybillListDTO;
import com.ecommerce.logistics.api.dto.waybill.PublishWaybillListQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.ReassignmentVehicleDTO;
import com.ecommerce.logistics.api.dto.waybill.ReceiverInfoDTO;
import com.ecommerce.logistics.api.dto.waybill.RepublishWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.SeckillScanQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.SellerAssignWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.SnatchWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.SnatchedWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.SnatchedWaybillQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.TradeWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.TradeWaybillDetailDTO;
import com.ecommerce.logistics.api.dto.waybill.UploadCertificateDTO;
import com.ecommerce.logistics.api.dto.waybill.UserSeckillWaybillListDTO;
import com.ecommerce.logistics.api.dto.waybill.UserSeckillWaybillQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.WaitCheckWaybillListDTO;
import com.ecommerce.logistics.api.dto.waybill.WarehouseAdminProccessedDTO;
import com.ecommerce.logistics.api.dto.waybill.WarehouseAdminProccessedQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.WarehouseAdminWaitDeliveryDTO;
import com.ecommerce.logistics.api.dto.waybill.WarehouseAdminWaitDeliveryQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillAssignDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillAssignListDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillAssignListQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillCodeDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillDetailDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillLocationDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillLocationQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillMonitorStatusNotifyDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillStatusOption;
import com.ecommerce.logistics.service.IShipBillService;
import com.ecommerce.logistics.service.IWaybillService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * @Created锛�Mon Nov 26 21:18:47 CST 2018
 * <AUTHOR>
 * @Version:2
 * @Description:运单服务
*/

@Api(tags={"Waybill"})
@RestController
@RequestMapping("/waybill")
public class WaybillController {

   @Autowired
   private IWaybillService iWaybillService;

   @Autowired
   private IShipBillService shipBillService;

   @ApiOperation("获取运单以及与运单相同城市区域的经纬度信息")
   @PostMapping(value="/getLocation")
   public ItemResult<WaybillLocationDTO> getLocation(@ApiParam("运单ID") @RequestParam("waybillId") String waybillId){
      return iWaybillService.getLocation(waybillId);
   }


   @ApiOperation("通过主运单ID查询主运单信息")
   @PostMapping(value="/selectMainWaybillInfoByMainWaybillId")
   public ItemResult<WaybillDetailDTO> selectMainWaybillInfoByMainWaybillId(@ApiParam("运单ID") @RequestParam("waybillId") String waybillId){
      return iWaybillService.selectMainWaybillInfoByMainWaybillId(waybillId);
   }


   @ApiOperation("通过主运单号查询收货人商品详情")
   @PostMapping(value="/selectReceiverInfoByMainWaybillId")
   public ItemResult<List<ReceiverInfoDTO>> selectReceiverInfoByMainWaybillId(@ApiParam("运单ID") @RequestParam("waybillId") String waybillId){
      return iWaybillService.selectReceiverInfoByMainWaybillId(waybillId);
   }


   @ApiOperation("根据发货单号查询运单列表")
   @PostMapping(value="/queryWaybillListByDeliveryNum")
   public ItemResult<List<TradeWaybillDTO>> queryWaybillListByDeliveryNum(@ApiParam("发货单号") @RequestParam("deliverySheetNum") String deliverySheetNum){
      return iWaybillService.queryWaybillListByDeliveryNum(deliverySheetNum);
   }


   @ApiOperation("指派运单")
   @PostMapping(value="/assignWaybill")
   public ItemResult<Void> assignWaybill(@RequestBody WaybillAssignDTO waybillAssignDTO){
      return iWaybillService.assignWaybill(waybillAssignDTO);
   }


   @ApiOperation("司机抢单")
   @PostMapping(value="/snatchWaybill")
   public ItemResult<Void> snatchWaybill(@RequestBody SnatchWaybillDTO snatchWaybillDTO){
      return iWaybillService.snatchWaybill(snatchWaybillDTO);
   }


   @ApiOperation("通过审核")
   @PostMapping(value="/passCheck")
   public ItemResult<Void> passCheck(@RequestBody PassCheckDTO dto){
      return iWaybillService.passCheck(dto);
   }

   @ApiOperation("批量通过审核")
   @PostMapping(value="/batchPassCheck")
   public ItemResult<Void> batchPassCheck(@RequestBody BatchPassCheckDTO batchPassCheckDTO)  {
      return iWaybillService.batchPassCheck(batchPassCheckDTO);
   }


   @ApiOperation("关闭运单")
   @PostMapping(value="/closeWaybill")
   public ItemResult<Void> closeWaybill(@RequestBody CloseWaybillDTO closeWaybillDTO){
      return iWaybillService.closeWaybill(closeWaybillDTO);
   }


   @ApiOperation("修改运单价格")
   @PostMapping(value="/changeCarriage")
   public ItemResult<Void> changeCarriage(@RequestBody ChangeCarriageDTO changeCarriageDTO){
      return iWaybillService.changeCarriage(changeCarriageDTO);
   }


   @ApiOperation("功能描述: 订单取消时，删除运单恢复提货单或调度单里面的商品已分配数量，若其运单有父运单则将其相父运单 删除并且将其关联的运单（即父运单Id相同的运单）状态修改为待整合。")
   @PostMapping(value="/removeWaybill")
   public ItemResult<Void> removeWaybill(@RequestBody DriverCancelWaybillDTO driverCancelWaybillDTO){
      return iWaybillService.removeWaybill(driverCancelWaybillDTO);
   }


   @ApiOperation("获取运单详情")
   @PostMapping(value="/getWaybillDetail")
   public ItemResult<WaybillDetailDTO> getWaybillDetail(@ApiParam("运单ID") @RequestParam("waybillId") String waybillId){
      return iWaybillService.getWaybillDetail(waybillId);
   }

   @ApiOperation("获取运单详情")
   @PostMapping(value="/getWaybillDetailByNum")
   public ItemResult<WaybillDetailDTO> getWaybillDetailByNum(@ApiParam("运单号") @RequestParam("waybillNum") String waybillNum){
      return iWaybillService.getWaybillDetailByNum(waybillNum);
   }

   @ApiOperation("完成运单")
   @PostMapping(value="/completeWaybill")
   public ItemResult<Void> completeWaybill(@RequestBody CompleteWaybillDTO completeWaybillDTO){
      return iWaybillService.completeWaybill(completeWaybillDTO);
   }


   @ApiOperation("修改运费")
   @PostMapping(value="/changePrice")
   public ItemResult<Void> changePrice(@RequestBody ChangePriceDTO dto){
      return iWaybillService.changePrice(dto);
   }


   @ApiOperation("车辆进站")
   @PostMapping(value="/arriveWarehouse")
   public ItemResult<Void> arriveWarehouse(@RequestBody ArriveWarehouseDTO dtos){
      return iWaybillService.arriveWarehouse(dtos);
   }


   @ApiOperation("获取抢单用户的查询仓库列表")
   @PostMapping(value="/selectSeckillWarehouseByUserId")
   public ItemResult<List<WarehouseOptionDTO>> selectSeckillWarehouseByUserId(@ApiParam("抢单用户ID") @RequestParam("userId") String userId){
      return iWaybillService.selectSeckillWarehouseByUserId(userId);
   }


   @ApiOperation("获取送达日期前后三天的运单指派列表")
   @PostMapping(value="/queryWaybillAssignList")
   public ItemResult<PageData<WaybillAssignListDTO>> queryWaybillAssignList(@RequestBody PageQuery<WaybillAssignListQueryDTO> pageQuery){
      return iWaybillService.queryWaybillAssignList(pageQuery);
   }


   @ApiOperation("输入出厂数量")
   @PostMapping(value="/inputLeaveWarehouseQuantity")
   public ItemResult<Void> inputLeaveWarehouseQuantity(@RequestBody LeaveWarehouseDTO dto){
      return iWaybillService.inputLeaveWarehouseQuantity(dto);
   }


   @ApiOperation("通过提货单查询相应运单列表")
   @PostMapping(value="/queryWaybillsByPickingBillIds")
   public ItemResult<List<WaybillDTO>> queryWaybillsByPickingBillIds(@ApiParam("提货单ID集合") @RequestBody List<String> pickingBillIds){
      return iWaybillService.queryWaybillsByPickingBillIds(pickingBillIds);
   }


   @ApiOperation("多条件查询运单经纬度信息")
   @PostMapping(value="/getWaybillLocationByCon")
   public ItemResult<List<LocationDTO>> getWaybillLocationByCon(@RequestBody WaybillLocationQueryDTO dto){
      return iWaybillService.getWaybillLocationByCon(dto);
   }


   @ApiOperation("将取消的运单的取消原因添加进去")
   @PostMapping(value="/changeCancelReason")
   public ItemResult<Void> changeCancelReason(@RequestBody CancelReasonDTO cancelReasonDTO){
      return iWaybillService.changeCancelReason(cancelReasonDTO);
   }


   @ApiOperation("待审核运单列表")
   @PostMapping(value="/queryCheckWaybillList")
   public ItemResult<PageData<WaitCheckWaybillListDTO>> queryCheckWaybillList(@RequestBody PageQuery<CheckWaybillListQueryDTO> pageQuery){
      return iWaybillService.queryCheckWaybillList(pageQuery);
   }


   @ApiOperation("平台取消运单")
   @PostMapping(value="/cancelWaybillByPlatform")
   public ItemResult<Void> cancelWaybillByPlatform(@RequestBody CancelWaybillDTO dto){
      return iWaybillService.cancelWaybillByPlatform(dto);
   }


   @ApiOperation("查询卖家运单列表")
   @PostMapping(value="/querySellerWaybillList")
   public ItemResult<PageData<PublishWaybillListDTO>> querySellerWaybillList(@RequestBody PageQuery<PublishWaybillListQueryDTO> pageQuery){
      return iWaybillService.querySellerWaybillList(pageQuery);
   }

   @ApiOperation("作废运单")
   @PostMapping(value="/discardWaybill")
   public ItemResult<Void> discardWaybill(@RequestBody DiscardWaybillDTO discardWaybillDTO) {
      iWaybillService.discardWaybill(discardWaybillDTO);
      return new ItemResult<>(null);
   }

   @ApiOperation("卖家指派运单(车辆)")
   @PostMapping(value="/assignWaybillBySeller")
   public ItemResult<Void> assignWaybillBySeller(@RequestBody SellerAssignWaybillDTO dto){
      return iWaybillService.assignWaybillBySeller(dto);
   }


   @ApiOperation("获取用户可抢单区域列表")
   @PostMapping(value="/selectSeckillDistrictsByUserId")
   public ItemResult<List<DistrictListDTO>> selectSeckillDistrictsByUserId(@ApiParam("用户ID") @RequestParam("userId") String userId){
      return iWaybillService.selectSeckillDistrictsByUserId(userId);
   }


   @ApiOperation("查询用户可抢运单集合")
   @PostMapping(value="/selectUserSeckillWaybillList")
   public ItemResult<PageData<UserSeckillWaybillListDTO>> selectUserSeckillWaybillList(@RequestBody PageQuery<UserSeckillWaybillQueryDTO> pageQuery){
      return iWaybillService.selectUserSeckillWaybillList(pageQuery);
   }


   @ApiOperation("多条件查询待接单运单列表")
   @PostMapping(value="/selectSeckillScanWaybillList")
   public ItemResult<PageData<UserSeckillWaybillListDTO>> selectSeckillScanWaybillList(@RequestBody PageQuery<SeckillScanQueryDTO> pageQuery){
      return iWaybillService.selectSeckillScanWaybillList(pageQuery);
   }


   @ApiOperation("修改运单状态为待接单")
   @PostMapping(value="/changeStatusToWaitReceiveById")
   public ItemResult<Void> changeStatusToWaitReceiveById(@ApiParam("运单ID") @RequestParam("waybillId") String waybillId){
      return iWaybillService.changeStatusToWaitReceiveById(waybillId);
   }


   @ApiOperation("查询用户已强到的运单集合")
   @PostMapping(value="/selectUserSeckilledWaybillList")
   public ItemResult<PageData<UserSeckillWaybillListDTO>> selectUserSeckilledWaybillList(@RequestBody PageQuery<DriverWaybillListQueryDTO> pageQuery){
      return iWaybillService.selectUserSeckilledWaybillList(pageQuery);
   }


   @ApiOperation("通过运单查询相应运单列表")
   @PostMapping(value="/queryWaybillsByWaybillIds")
   public ItemResult<List<WaybillDTO>> queryWaybillsByWaybillIds(@ApiParam("运单ID集合") @RequestBody List<String> waybillIds){
      return iWaybillService.queryWaybillsByWaybillIds(waybillIds);
   }


   @ApiOperation("查询仓库管理员已处理运单列表")
   @PostMapping(value="/getWarehouseAdminProccessed")
   public ItemResult<PageData<WarehouseAdminProccessedDTO>> getWarehouseAdminProccessed(@RequestBody PageQuery<WarehouseAdminProccessedQueryDTO> pageQuery){
      return iWaybillService.getWarehouseAdminProccessed(pageQuery);
   }


   @ApiOperation("完成多个子运单")
   @PostMapping(value="/completeWaybillBySubWaybillIds")
   public ItemResult<Void> completeWaybillBySubWaybillIds(@RequestBody CompleteWaybillDTO completeWaybillDTO){
      return iWaybillService.completeWaybillBySubWaybillIds(completeWaybillDTO);
   }


   @ApiOperation("查询仓库管理员待配送运单列表")
   @PostMapping(value="/getWarehouseAdminWaitDelivery")
   public ItemResult<PageData<WarehouseAdminWaitDeliveryDTO>> getWarehouseAdminWaitDelivery(@RequestBody PageQuery<WarehouseAdminWaitDeliveryQueryDTO> pageQuery){
      return iWaybillService.getWarehouseAdminWaitDelivery(pageQuery);
   }


   @ApiOperation("通过调度单查询相应运单列表")
   @PostMapping(value="/queryWaybillsByDispatchIds")
   public ItemResult<List<WaybillDTO>> queryWaybillsByDispatchIds(@ApiParam("调度单ID集合") @RequestBody List<String> dispatchIds){
      return iWaybillService.queryWaybillsByDispatchIds(dispatchIds);
   }


   @ApiOperation("重新发布运单")
   @PostMapping(value="/republishWaybillByPlatform")
   public ItemResult<Void> republishWaybillByPlatform(@RequestBody RepublishWaybillDTO republish){
      return iWaybillService.republishWaybillByPlatform(republish);
   }


   @ApiOperation("待整合运单列表")
   @PostMapping(value="/queryMergeWaybillList")
   public ItemResult<PageData<MergeWaybillListDTO>> queryMergeWaybillList(@RequestBody PageQuery<MergeWaybillListQueryDTO> pageQuery){
      return iWaybillService.queryMergeWaybillList(pageQuery);
   }


   @ApiOperation("修改运单状态为已取消")
   @PostMapping(value="/changeStatusToCanceledById")
   public ItemResult<Void> changeStatusToCanceledById(@ApiParam("运单ID") @RequestParam("waybillId") String waybillId){
      return iWaybillService.changeStatusToCanceledById(waybillId);
   }


   @ApiOperation("更新监控送达时间")
   @PostMapping(value="/changeMonitorArriveTime")
   public ItemResult<Void> changeMonitorArriveTime(@RequestBody MonitorTimeDTO dto){
      return iWaybillService.changeMonitorArriveTime(dto);
   }


   @ApiOperation("更新送达时间")
   @PostMapping(value="/changeArriveDestinationTime")
   public ItemResult<Void> changeArriveDestinationTime(@ApiParam("运单ID") @RequestParam("waybillId") String waybillId){
      return iWaybillService.changeArriveDestinationTime(waybillId);
   }


   @ApiOperation("更新开始搬运时间")
   @PostMapping(value="/changeBeginCarryTime")
   public ItemResult<Void> changeBeginCarryTime(@ApiParam("运单ID") @RequestParam("waybillId") String waybillId){
      return iWaybillService.changeBeginCarryTime(waybillId);
   }

   @Deprecated(since = "2.1.4-RELEASE")
   @ApiOperation("承运商类型查询所有承运商集合")
   @PostMapping(value="/getCarriersByType")
   public ItemResult<Void> getCarriersByType(@ApiParam("承运商类型") @RequestParam("carrierType") String carrierType){
      return iWaybillService.getCarriersByType(carrierType);
   }


   @ApiOperation("发布运单列表")
   @PostMapping(value="/queryPublishWaybillList")
   public ItemResult<PageData<PublishWaybillListDTO>> queryPublishWaybillList(@RequestBody PageQuery<PublishWaybillListQueryDTO> pageQuery){
      return iWaybillService.queryPublishWaybillList(pageQuery);
   }


   @ApiOperation("承运商指派运单给司机")
   @PostMapping(value="/carrierAssignWaybillToDriver")
   public ItemResult<Void> carrierAssignWaybillToDriver(@RequestBody DriverWaybillAssignDTO dto){
      return iWaybillService.carrierAssignWaybillToDriver(dto);
   }


   @ApiOperation("运单监控状态通知")
   @PostMapping(value="/waybillMonitorStatusNotify")
   public ItemResult<Void> waybillMonitorStatusNotify(@RequestBody WaybillMonitorStatusNotifyDTO waybillMonitorStatusNotifyDTO){
      return iWaybillService.waybillMonitorStatusNotify(waybillMonitorStatusNotifyDTO);
   }


   @ApiOperation("查询司机当前配送状态")
   @PostMapping(value="/queryDriverDeliveryStatus")
   public ItemResult<DriverDeliveryDTO> queryDriverDeliveryStatus(@ApiParam("司机ID") @RequestParam("driverId") String driverId){
      return iWaybillService.queryDriverDeliveryStatus(driverId);
   }


   @ApiOperation("司机确认运单(承运商指派)")
   @PostMapping(value="/confirmWaybillByDriver")
   public ItemResult<Void> confirmWaybillByDriver(@RequestBody DriverConfirmDTO driverConfirmDTO){
      return iWaybillService.confirmWaybillByDriver(driverConfirmDTO);
   }


   @ApiOperation("承运商运单列表")
   @PostMapping(value="/queryCarrierWaybillList")
   public ItemResult<PageData<CarrierWaybillListDTO>> queryCarrierWaybillList(@RequestBody PageQuery<CarrierWaybillListQueryDTO> arg0){
      return iWaybillService.queryCarrierWaybillList(arg0);
   }


   @ApiOperation("获取买家APP展示的运单详情")
   @PostMapping(value="/queryTradeWaybillDetail")
   public ItemResult<TradeWaybillDetailDTO> queryTradeWaybillDetail(@ApiParam("运单ID") @RequestParam("waybillId") String waybillId){
      return iWaybillService.queryTradeWaybillDetail(waybillId);
   }

   @ApiOperation("根据运单号获取APP展示的运单详情")
   @PostMapping(value="/queryTradeWaybillDetailByNum")
   public ItemResult<TradeWaybillDetailDTO> queryTradeWaybillDetailByNum(@ApiParam("运单号") @RequestParam("waybillNum") String waybillNum){
      return shipBillService.queryTradeWaybillDetailByNum(waybillNum);
   }

   @ApiOperation("获取已抢运单列表")
   @PostMapping(value="/querySnatchedWaybillList")
   public ItemResult<List<SnatchedWaybillDTO>> querySnatchedWaybillList(@RequestBody SnatchedWaybillQueryDTO snatchedWaybillQueryDTO) {
      return iWaybillService.querySnatchedWaybillList(snatchedWaybillQueryDTO);
   }

   @ApiOperation("获取评价运单列表")
   @PostMapping(value="/queryEvaluateWaybillList")
   public ItemResult<List<EvaluateWaybillListDTO>> queryEvaluateWaybillList(@ApiParam("发货单号集合") @RequestBody List<String> deliverySheetNumList) {
      return iWaybillService.queryEvaluateWaybillList(deliverySheetNumList);
   }

   @ApiOperation("根据运单号获取提货单列表")
   @PostMapping(value="/queryPickingListByWaybillNum")
   public ItemResult<List<PickingBillDTO>> queryPickingListByWaybillNum(@ApiParam("运单号集合") @RequestBody List<String> waybillNumList) {
      return iWaybillService.queryPickingListByWaybillNum(waybillNumList);
   }

   /**
    * 运单ID查询单号实体
    * @param waybillIdList
    * @return
    */
   @ApiOperation("运单ID查询单号实体")
   @PostMapping(value="/queryDeliverySheetNumByWaybillId")
   public ItemResult<List<WaybillCodeDTO>> queryDeliverySheetNumByWaybillId(@ApiParam("运单ID集合") @RequestBody List<String> waybillIdList) {
      return iWaybillService.queryDeliverySheetNumByWaybillId(waybillIdList);
   }

   @ApiOperation("交易补款完成后通知物流出站")
   @PostMapping(value="/notifyLeaveWarehouse")
   public ItemResult<Void> notifyLeaveWarehouse(@RequestBody NotifyLeaveWarehouseDTO notifyLeaveWarehouseDTO) {
      return iWaybillService.notifyLeaveWarehouse(notifyLeaveWarehouseDTO);
   }

   /**
    * 运单号查询运单状态
    * @param waybillNumList
    * @return
    */
   @ApiOperation("运单号查询运单状态")
   @PostMapping(value="/queryWaybillStatusByNum")
   public ItemResult<List<WaybillStatusOption>> queryWaybillStatusByNum(@RequestBody List<String> waybillNumList)  {
      return iWaybillService.queryWaybillStatusByNum(waybillNumList);
   }

   @ApiOperation("进厂")
   @PostMapping(value="/enterFactory")
   public ItemResult<Void> enterFactory(@RequestBody EnterFactoryDTO dto)  {
      return iWaybillService.enterFactory(dto);
   }

   @ApiOperation("撤回进厂")
   @PostMapping(value="/withdrawEnterFactory")
   public ItemResult<Void> withdrawEnterFactory(@RequestBody EnterFactoryDTO dto)  {
      return iWaybillService.withdrawEnterFactory(dto);
   }

   @ApiOperation("APP查询运单列表")
   @PostMapping(value="/queryAppWaybillList")
   public ItemResult<PageData<PublishWaybillListDTO>> queryAppWaybillList(@RequestBody PageQuery<AppWaybillListQueryDTO> pageQuery){
      return iWaybillService.queryAppWaybillList(pageQuery);
   }

   @ApiOperation("APP运单统计（计划提货量，实际出场量）")
   @PostMapping(value="/statisticsAppWaybill")
   public ItemResult<AppWaybillStatisticsDTO> statisticsAppWaybill(@RequestBody AppWaybillListQueryDTO queryDTO){
      return iWaybillService.statisticsAppWaybill(queryDTO);
   }

   @ApiOperation("APP运单列表筛选条件查询(买家，卖家，商品)")
   @PostMapping(value="/selectAppWaybillQueryConditions")
   public ItemResult<AppWaybillQueryConditionsMapDTO> selectAppWaybillQueryConditions(@RequestBody AppWaybillListQueryDTO queryDTO){
      return iWaybillService.selectAppWaybillQueryConditions(queryDTO);
   }

   @ApiOperation("查询买家运单仓库Id合集")
   @PostMapping(value="/queryWarehouseIdByBuyerId")
   public ItemResult<List<String>> queryWarehouseIdByBuyerId(@RequestParam("buyerId") String buyerId){
      return iWaybillService.queryWarehouseIdByBuyerId(buyerId);
   }
   
   @ApiOperation("买家待配送运单重新指派车辆接口")
   @PostMapping(value="/reassignmentVehicle")
   public ItemResult<Void> reassignmentVehicle(@RequestBody ReassignmentVehicleDTO queryDTO){
      return iWaybillService.reassignmentVehicle(queryDTO);
   }

   @ApiOperation("运单上传支付凭证")
   @PostMapping(value = "/uploadCertificate")
   public ItemResult<Void> uploadCertificate(@RequestBody UploadCertificateDTO uploadCertificateDTO){
      return iWaybillService.uploadCertificate(uploadCertificateDTO);
   }

   @ApiOperation("查看运单支付凭证")
   @PostMapping(value = "/findWaybillCertificate")
   public ItemResult<List<AttListDTO>> findWaybillCertificate(@ApiParam("运单ID") @RequestParam("waybillId") String waybillId){
      return iWaybillService.findWaybillCertificate(waybillId);
   }

   @ApiOperation("根据运单号查询已完成运单详情（信息上报调用）")
   @PostMapping(value = "/selectCarryWaybill")
   public ItemResult<CarryWaybillSubmitDTO> selectCarryWaybill(@ApiParam("运单号") @RequestParam("waybillNum") String waybillNum){
      return iWaybillService.selectCarryWaybill(waybillNum);
   }
}
