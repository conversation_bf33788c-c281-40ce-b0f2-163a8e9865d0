package com.ecommerce.logistics.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.logistics.api.dto.waybill.ERPWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.ERPWaybillExecuteDTO;
import com.ecommerce.logistics.api.dto.waybill.ERPWaybillRefundDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillRetryHandlerDTO;
import com.ecommerce.logistics.api.enums.TransportToolTypeEnum;
import com.ecommerce.logistics.service.IShipBillExternalService;
import com.ecommerce.logistics.service.IWaybillNewExternalService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * Author: <EMAIL>
 * Description:
 * Date: Create in 下午8:05 19/4/9
 */
@Api(tags={"WaybillExternal"},description = "外部运单服务")
@RestController
@RequestMapping("/waybillExternal")
public class WaybillExternalController {

    @Autowired
    private IWaybillNewExternalService waybillExternalService;

    @Autowired
    private IShipBillExternalService shipBillExternalService;

    @ApiOperation("重试运单外部系统请求")
    @PostMapping(value="/waybillRetryHandler")
    public ItemResult<Void> waybillRetryHandler(@RequestBody WaybillRetryHandlerDTO waybillRetryHandlerDTO)throws Exception{
        //区分汽运船运
        if (CsStringUtils.equals(TransportToolTypeEnum.WATER_TRANSPORT.getCode(), waybillRetryHandlerDTO.getTransportToolType())) {
            return shipBillExternalService.waybillRetryHandler(waybillRetryHandlerDTO);
        }
        return waybillExternalService.waybillRetryHandler(waybillRetryHandlerDTO);
    }

    @ApiOperation("创建外部运单回调")
    @PostMapping(value="/createExternalWaybillCallBack")
    public ItemResult<Void> createExternalWaybillCallBack(@RequestBody ItemResult<ERPWaybillDTO> itemResult)throws Exception{
        return waybillExternalService.createExternalWaybillCallBack(itemResult);
    }

    @ApiOperation("运单信息下发处理结果查询")
    @GetMapping(value="/queryWaybillCreateResult")
    public ItemResult<Object> queryWaybillCreateResult(@RequestParam("waybillNum") String waybillNum)throws Exception{
        return waybillExternalService.queryWaybillCreateResult(waybillNum);
    }

    @ApiOperation("修改外部运单回调")
    @PostMapping(value="/updateExternalWaybillCallBack")
    public ItemResult<Void> updateExternalWaybillCallBack(@RequestBody ItemResult<ERPWaybillDTO> itemResult)throws Exception{
        return waybillExternalService.updateExternalWaybillCallBack(itemResult);
    }

    @ApiOperation("关闭外部运单回调")
    @PostMapping(value="/closeExternalWaybillCallBack")
    public ItemResult<Void> closeExternalWaybillCallBack(@RequestBody ItemResult<ERPWaybillDTO> itemResult)throws Exception{
        return waybillExternalService.closeExternalWaybillCallBack(itemResult);
    }

    @ApiOperation("创建电商运单数据")
    @PostMapping(value="/createECWaybill")
    public ItemResult<String> createECWaybill(@RequestBody ERPWaybillDTO ecWaybillDTO)throws Exception{
        return waybillExternalService.createECWaybill(ecWaybillDTO);
    }

/*    @ApiOperation("修改电商运单数据")
    @PostMapping(value="/updateECWaybill")
    public ItemResult<Void> updateECWaybill(@RequestBody ERPWaybillDTO ecWaybillDTO)throws Exception{
        return waybillExternalService.updateECWaybill(ecWaybillDTO);
    }*/

    @ApiOperation("关闭电商运单数据")
    @PostMapping(value="/closeECWaybill")
    public ItemResult<Void> closeECWaybill(@RequestBody ERPWaybillDTO ecWaybillDTO)throws Exception{
        return waybillExternalService.closeECWaybill(ecWaybillDTO);
    }

    @ApiOperation("处理外部系统执行状态")
    @PostMapping(value="/handlerExternalExecuteStatus")
    public ItemResult<Void> handlerExternalExecuteStatus(@RequestBody ERPWaybillExecuteDTO erpWaybillExecuteDTO)throws Exception{
        return waybillExternalService.handlerExternalExecuteStatus(erpWaybillExecuteDTO);
    }

    @ApiOperation("外部运单发货回调")
    @PostMapping(value="/externalSendOutCallBack")
    public ItemResult<Void> externalSendOutCallBack(@RequestBody ItemResult<ERPWaybillDTO> itemResult)throws Exception{
        return waybillExternalService.externalSendOutCallBack(itemResult);
    }


    @ApiOperation("外部运单退货回调")
    @PostMapping(value="/externalRefundCallBack")
    public ItemResult<Void> externalRefundCallBack(@RequestBody ItemResult<ERPWaybillRefundDTO> itemResult)throws Exception {
        return waybillExternalService.externalRefundCallBack( itemResult);
    }

}
