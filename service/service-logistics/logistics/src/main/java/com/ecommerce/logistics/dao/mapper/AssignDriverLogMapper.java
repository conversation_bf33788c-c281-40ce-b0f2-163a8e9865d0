package com.ecommerce.logistics.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.api.dto.driver.AssignDriverLogAddDTO;
import com.ecommerce.logistics.api.dto.driver.AssignDriverLogCondDTO;
import com.ecommerce.logistics.api.dto.driver.AssignDriverLogResDTO;
import com.ecommerce.logistics.dao.vo.AssignDriverLog;

import java.util.List;

public interface AssignDriverLogMapper extends IBaseMapper<AssignDriverLog> {

    /**
     * 条件查询可指派的司机
     * @param assignDriverLogCondDTO
     * @return
     */
    List<AssignDriverLogResDTO> queryByCond(AssignDriverLogCondDTO assignDriverLogCondDTO);

    /**
     * 按照指派的司机查询记录
     * @param assignDriverLogAddDTO
     * @return
     */
    AssignDriverLog findByAssignDriver(AssignDriverLogAddDTO assignDriverLogAddDTO);

}