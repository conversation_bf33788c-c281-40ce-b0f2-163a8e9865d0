package com.ecommerce.logistics.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.api.dto.attachment.AttListDTO;
import com.ecommerce.logistics.api.dto.attachment.AttListQueryDTO;
import com.ecommerce.logistics.api.dto.attachment.AttRemoveDTO;
import com.ecommerce.logistics.dao.vo.Attachment;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AttachmentMapper extends IBaseMapper<Attachment> {
    List<AttListDTO> selectAttList(AttListQueryDTO attListQueryDTO);

    Integer deleteAtt(AttRemoveDTO attRemoveDTO);
    
    /**
     * 
     * @Auther: <EMAIL>
     * @Date: 2018年9月7日 上午11:56:16
     * @param entryId
     * @return
     */
    List<String> selectUrlByEntryId(@Param("entryId")String entryId);

    Integer insertAttachmentList(List<Attachment> list);

    List<AttListDTO> selectAttListByIds(List<String> ids);

    List<AttListDTO> selectAttListById(@Param("entryId") String entryId);
    
    List<String> selectBidsByFileExtAndEntryId(@Param("entryId")String entryId,@Param("fileExt")String fileExt);

    List<String> selectBidsByFileExtAndEntryIds(@Param("entryIds")List<String> entryIds,@Param("fileExt")String fileExt);

    Integer deleteAttachmentByEntryIds(@Param("entryId") String entryId, @Param("operatorId")String operatorId);
}