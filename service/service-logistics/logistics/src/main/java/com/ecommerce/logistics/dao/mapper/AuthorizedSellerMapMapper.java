package com.ecommerce.logistics.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.api.dto.shipping.AuthorizeSellerDTO;
import com.ecommerce.logistics.dao.vo.AuthorizedSellerMap;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface AuthorizedSellerMapMapper extends IBaseMapper<AuthorizedSellerMap> {

    /**
     * 批量授权卖家
     * @param list
     */
    int addOrUpdateBatchAuthorize(List<AuthorizedSellerMap> list);


    void batchInsertAuthorize(List<AuthorizedSellerMap> list);

    /**
     * 批量取消授权卖家
     * @param map
     * @return
     */
    int batchCancelAuthorize(Map<String, Object> map);

    /**
     * 批量修改服务类型
     * @param map
     * @return
     */
    int batchFixedTemporary(Map<String, Object> map);

    /**
     * 查询在数据库中是否存在，存在则返回船舶ID
     * @param map
     * @return
     */
    List<AuthorizedSellerMap> queryExistShippingIds(Map<String, Object> map);

    /**
     * 已授权卖家列表
     * @param shippingId
     * @return
     */
    List<AuthorizeSellerDTO> authorizeSellerListInfo(@Param("shippingId") String shippingId);

    /**
     * 查找是否存在固定服务类型的卖家
     * @param shippingId
     * @return
     */
    int queryExistFixed(@Param("shippingId") String shippingId);
}