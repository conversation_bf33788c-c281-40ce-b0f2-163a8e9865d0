package com.ecommerce.logistics.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.api.dto.shippingroute.AuxiliaryPriceDTO;
import com.ecommerce.logistics.api.dto.shippingroute.AuxiliaryPriceListDTO;
import com.ecommerce.logistics.dao.vo.AuxiliaryPriceInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AuxiliaryPriceInfoMapper extends IBaseMapper<AuxiliaryPriceInfo> {

    int insertAuxiliaryPriceBatch(List<AuxiliaryPriceInfo> auxiliaryPriceList);

    List<AuxiliaryPriceDTO> queryAuxiliaryPriceDetails(@Param("belongerId") String belongerId,
                                                       @Param("auxiliaryPriceNo") String auxiliaryPriceNo);

    List<AuxiliaryPriceListDTO> queryAuxiliaryPriceList(AuxiliaryPriceDTO auxiliaryPriceDTO);

    List<AuxiliaryPriceDTO> queryAuxiliaryDetailList(@Param("belongerId") String belongerId,
                                                     @Param("auxiliaryPriceNo") String auxiliaryPriceNo,
                                                     @Param("auxiliaryPriceIdList") List<Long> auxiliaryPriceIdList);

    /**
     * 根据编号查询出辅助价目表
     * @param belongerId
     * @param auxiliaryPriceNo
     * @return
     */
    List<AuxiliaryPriceDTO> queryAuxiliaryByNo(@Param("belongerId")String belongerId, @Param("auxiliaryPriceNo")String auxiliaryPriceNo);

    /**
     * 查询辅助价目表名称是否存在
     * @param auxiliaryPriceName
     * @return
     */
    int queryAuxiliaryByName(@Param("auxiliaryPriceName")String auxiliaryPriceName);

}