package com.ecommerce.logistics.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.dao.vo.BaSaleRegionAll;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BaSaleRegionAllMapper extends IBaseMapper<BaSaleRegionAll> {

    List<String> queryRegionSonIdByRegionId(@Param("sellerId") String sellerId,
                                            @Param("saleRegionId") String saleRegionId,
                                            @Param("saleRegionIdList") List<String> saleRegionIdList);

}