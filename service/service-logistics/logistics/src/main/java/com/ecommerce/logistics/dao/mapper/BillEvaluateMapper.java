package com.ecommerce.logistics.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.api.dto.billEvaluate.BillEvaluateCountDTO;
import com.ecommerce.logistics.api.dto.billEvaluate.BillEvaluateListDTO;
import com.ecommerce.logistics.api.dto.billEvaluate.BillEvaluateQueryDTO;
import com.ecommerce.logistics.dao.vo.BillEvaluate;

import java.util.List;

public interface BillEvaluateMapper extends IBaseMapper<BillEvaluate> {

    /**
     * 查询单据评价记录
     */
    List<BillEvaluateListDTO> selectBillEvaluateListByCondition(BillEvaluateQueryDTO billEvaluateQueryDTO);

    /**
     * 根据被评价人Id查询评价总分数
     */
    BillEvaluateCountDTO selectCountByEvaluatedPersonId(String evaluatedPersonId);
}