package com.ecommerce.logistics.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.api.dto.billEvaluate.BillEvaluateStatisticListDTO;
import com.ecommerce.logistics.api.dto.billEvaluate.BillEvaluateStatisticListQueryDTO;
import com.ecommerce.logistics.dao.vo.BillEvaluateStatistic;

import java.util.List;

public interface BillEvaluateStatisticMapper extends IBaseMapper<BillEvaluateStatistic> {

    List<BillEvaluateStatisticListDTO> selectEvaluateStatisticList(BillEvaluateStatisticListQueryDTO queryDTO);

    List<BillEvaluateStatisticListDTO> selectPersonIdAndScoreByType(String evaluatedPersonType);
}