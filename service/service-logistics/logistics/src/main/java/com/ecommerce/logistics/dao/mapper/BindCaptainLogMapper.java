package com.ecommerce.logistics.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.api.dto.shipping.BindCaptainLogDTO;
import com.ecommerce.logistics.dao.vo.BindCaptainLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BindCaptainLogMapper extends IBaseMapper<BindCaptainLog> {

    List<BindCaptainLogDTO> queryBindCaptainLog(@Param("captainAccountName") String captainAccountName);

    List<BindCaptainLogDTO> queryBindCaptainLogByShippingId(@Param("shippingId") String shippingId);

    BindCaptainLogDTO getBindCaptainByShippingId(@Param("shippingId") String shippingId);

}