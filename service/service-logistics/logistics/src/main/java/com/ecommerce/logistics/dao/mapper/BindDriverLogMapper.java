package com.ecommerce.logistics.dao.mapper;

import org.apache.ibatis.annotations.Param;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.api.dto.driver.BindDriverLogDTO;
import com.ecommerce.logistics.dao.vo.BindDriverLog;

public interface BindDriverLogMapper extends IBaseMapper<BindDriverLog> {

	BindDriverLogDTO getBindCaptainByVehicleId(@Param("vehicleId") String vehicleId);
}