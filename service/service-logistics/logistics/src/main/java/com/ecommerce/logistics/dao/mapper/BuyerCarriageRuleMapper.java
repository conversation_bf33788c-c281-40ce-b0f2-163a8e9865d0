package com.ecommerce.logistics.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.api.dto.carriagerule.BuyerCarriageRuleQueryDTO;
import com.ecommerce.logistics.dao.dto.carriage.BuyerCarriageRuleListDO;
import com.ecommerce.logistics.dao.vo.BuyerCarriageRule;

import java.util.List;

public interface BuyerCarriageRuleMapper extends IBaseMapper<BuyerCarriageRule> {

    /**
     * 查询买家运费规则列表
     * @param buyerCarriageRuleQueryDTO 买家运费规则查询对象
     * @return List<BuyerCarriageRuleListDO>
     */
    List<BuyerCarriageRuleListDO> queryBuyerCarriageRuleList(BuyerCarriageRuleQueryDTO buyerCarriageRuleQueryDTO);
}