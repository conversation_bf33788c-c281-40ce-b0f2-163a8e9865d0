package com.ecommerce.logistics.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.dao.dto.carriage.BuyerCarriageQueryDO;
import com.ecommerce.logistics.dao.vo.BuyerRuleItem;

import java.util.List;

public interface BuyerRuleItemMapper extends IBaseMapper<BuyerRuleItem> {

    /**
     * 查询买家运费明细列表
     * @param buyerCarriageQueryDO 买家运费查询对象
     * @return List<BuyerRuleItem>
     */
    List<BuyerRuleItem> selectBuyerCarriageItemList(BuyerCarriageQueryDO buyerCarriageQueryDO);
}