package com.ecommerce.logistics.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.api.dto.carriage.CarriageRouteListDTO;
import com.ecommerce.logistics.api.dto.carriage.CarriageRouteQueryDTO;
import com.ecommerce.logistics.api.dto.carriage.CarriageRouteSaveDTO;
import com.ecommerce.logistics.dao.dto.carriage.CarriageRouteListDO;
import com.ecommerce.logistics.dao.dto.carriage.CarriageRouteQueryDO;
import com.ecommerce.logistics.dao.vo.CarriageRoute;

import java.util.List;

public interface CarriageRouteMapper extends IBaseMapper<CarriageRoute> {

    /**
     * 查询运费路线列表
     * @param carriageRouteQueryDTO 运费路线查询对象
     * @return List<CarriageRouteListDTO>
     */
    List<CarriageRouteListDTO>  queryCarriageRouteList(CarriageRouteQueryDTO carriageRouteQueryDTO);

    /**
     * 条件查询运费路线列表
     * @param carriageRouteQueryDO 运费路线查询对象
     * @return List<CarriageRouteListDTO>
     */
    List<CarriageRouteListDO> queryCarriageRouteByCondition(CarriageRouteQueryDO carriageRouteQueryDO);

    /**
     * 计算重复的地方,返回主键
     * @param carriageRouteSaveDTO
     * @return
     */
    List<String> selectDuplicateId(CarriageRouteSaveDTO carriageRouteSaveDTO);

}