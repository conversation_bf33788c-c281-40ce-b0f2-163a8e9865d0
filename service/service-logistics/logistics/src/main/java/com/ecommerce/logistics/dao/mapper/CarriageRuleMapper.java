package com.ecommerce.logistics.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.dao.vo.CarriageRule;

import java.util.HashMap;
import java.util.List;

public interface CarriageRuleMapper extends IBaseMapper<CarriageRule> {

    /**
     * 查询不限承运商的运费规则
     * @param map
     * @return
     */
    List<CarriageRule> queryUnlimiteCarrier(HashMap<String,Object> map);

}