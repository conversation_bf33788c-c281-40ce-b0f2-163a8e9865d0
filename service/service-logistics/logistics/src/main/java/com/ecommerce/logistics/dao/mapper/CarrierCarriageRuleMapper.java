package com.ecommerce.logistics.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.api.dto.carriagerule.CarrierCarriageRuleQueryDTO;
import com.ecommerce.logistics.dao.dto.carriage.CarrierCarriageQueryDO;
import com.ecommerce.logistics.dao.dto.carriage.CarrierCarriageRuleListDO;
import com.ecommerce.logistics.dao.vo.CarrierCarriageRule;

import java.util.List;

public interface CarrierCarriageRuleMapper extends IBaseMapper<CarrierCarriageRule> {

    /**
     * 查询承运商运费规则列表
     * @param carrierCarriageQueryDO 运费规则查询对象
     * @return List<CarrierCarriageRule>
     */
    List<CarrierCarriageRule> selectCarrierCarriageRuleList(CarrierCarriageQueryDO carrierCarriageQueryDO);

    /**
     * 查询承运商运费规则列表
     * @param carrierCarriageRuleQueryDTO 承运商运费规则查询对象
     * @return List<CarrierCarriageRuleListDO>
     */
    List<CarrierCarriageRuleListDO> queryCarrierCarriageRuleList(CarrierCarriageRuleQueryDTO carrierCarriageRuleQueryDTO);
}