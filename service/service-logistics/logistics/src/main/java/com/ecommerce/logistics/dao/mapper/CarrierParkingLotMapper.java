package com.ecommerce.logistics.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.api.dto.intelligentDispatch.CarrierParkingLotDTO;
import com.ecommerce.logistics.dao.vo.CarrierParkingLot;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CarrierParkingLotMapper extends IBaseMapper<CarrierParkingLot> {

    List<CarrierParkingLotDTO> queryCarrierParkingLotList(CarrierParkingLotDTO queryDTO);

    List<CarrierParkingLotDTO> queryCarrierParkingListByCarriers(@Param("consignorId") String consignorId,
                                                                 @Param("list") List<String> carrierIdList,
                                                                 @Param("transportCategoryId") String transportCategoryId);

}