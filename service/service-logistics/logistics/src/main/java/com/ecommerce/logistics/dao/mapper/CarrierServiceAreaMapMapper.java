package com.ecommerce.logistics.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.api.dto.intelligentDispatch.CarrierServiceAreaMapDTO;
import com.ecommerce.logistics.dao.vo.CarrierServiceAreaMap;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface CarrierServiceAreaMapMapper extends IBaseMapper<CarrierServiceAreaMap> {

    List<CarrierServiceAreaMapDTO> queryMapDTOListByServiceAreaId(@Param("id") String serviceAreaId);

    List<CarrierServiceAreaMapDTO> queryMapDTOListByServiceAreaIds(@Param("ids") Collection<String> serviceAreaIds);
}