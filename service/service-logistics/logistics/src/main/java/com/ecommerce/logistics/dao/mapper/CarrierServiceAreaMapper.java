package com.ecommerce.logistics.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.api.dto.intelligentDispatch.CarrierServiceAreaDTO;
import com.ecommerce.logistics.api.dto.intelligentDispatch.QueryServiceAreaDTO;
import com.ecommerce.logistics.api.dto.intelligentDispatch.ServiceAreaResultDTO;
import com.ecommerce.logistics.dao.vo.CarrierServiceArea;

import java.util.List;

public interface CarrierServiceAreaMapper extends IBaseMapper<CarrierServiceArea> {

    List<CarrierServiceAreaDTO> queryCarrierServiceAreaList(CarrierServiceAreaDTO queryDTO);

    List<ServiceAreaResultDTO> queryCarrierServiceAreaByConsignor(QueryServiceAreaDTO queryDTO);
}