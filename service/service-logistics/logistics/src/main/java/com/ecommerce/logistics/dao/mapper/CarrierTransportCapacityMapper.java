package com.ecommerce.logistics.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.api.dto.intelligentDispatch.CarrierTransportCapacityDTO;
import com.ecommerce.logistics.dao.vo.CarrierTransportCapacity;

import java.util.List;

public interface CarrierTransportCapacityMapper extends IBaseMapper<CarrierTransportCapacity> {

    List<CarrierTransportCapacityDTO> queryCarrierTransportCapacityList(CarrierTransportCapacityDTO queryDTO);

}