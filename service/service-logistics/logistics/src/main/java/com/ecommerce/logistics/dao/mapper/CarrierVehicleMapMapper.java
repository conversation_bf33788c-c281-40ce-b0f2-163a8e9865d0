package com.ecommerce.logistics.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.dao.vo.CarrierVehicleMap;
import org.apache.ibatis.annotations.Param;

public interface CarrierVehicleMapMapper extends IBaseMapper<CarrierVehicleMap> {

    /**
     * 查询承运商+车辆的关联关系
     * @param carrierId
     * @param vehicleId
     * @return
     */
    CarrierVehicleMap selectByCarrierAndVehicle(@Param("carrierId") String carrierId,
                                                @Param("vehicleId") String vehicleId);

    /**
     * 统计承运商的车辆数
     * @param carrierId
     * @return
     */
    int countVehicleByCarrierId(@Param("carrierId") String carrierId);

}