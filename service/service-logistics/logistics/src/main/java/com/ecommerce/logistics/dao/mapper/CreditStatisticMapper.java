package com.ecommerce.logistics.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.api.dto.creditStatistic.CreditStatisticListDTO;
import com.ecommerce.logistics.api.dto.creditStatistic.CreditStatisticQueryDTO;
import com.ecommerce.logistics.dao.vo.CreditStatistic;

import java.util.List;

public interface CreditStatisticMapper extends IBaseMapper<CreditStatistic> {

    /**
     * 查询信用统计记录
     */
    List<CreditStatisticListDTO> queryCreditStatisticList(CreditStatisticQueryDTO queryDTO);
}