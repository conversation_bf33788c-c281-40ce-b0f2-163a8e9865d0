package com.ecommerce.logistics.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.api.dto.*;
import com.ecommerce.logistics.api.dto.auto.PlanDeliveryListDTO;
import com.ecommerce.logistics.api.dto.auto.TransportCapacityDTO;
import com.ecommerce.logistics.api.dto.auto.TransportCapacityResultDTO;
import com.ecommerce.logistics.api.dto.waybill.ContractSubmitInfoDTO;
import com.ecommerce.logistics.dao.dto.BillLocationDO;
import com.ecommerce.logistics.dao.dto.delivery.DeliveryBillChangeDTO;
import com.ecommerce.logistics.dao.dto.delivery.DeliveryChainBriDTO;
import com.ecommerce.logistics.dao.dto.shipbill.ShipBillQuantityDTO;
import com.ecommerce.logistics.dao.vo.DeliveryBill;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

public interface DeliveryBillMapper extends IBaseMapper<DeliveryBill> {

    /**
     * 委托单向下级委托数量时,修改当前委托单的委托量
     * @param deliveryBillId
     * @param consignQuantity
     * @return
     */
    int consignCarrierQuantity(@Param("deliveryBillId") String deliveryBillId,
                               @Param("consignQuantity") BigDecimal consignQuantity);

    /**
     * 获取下一级的叶子子节点
     * @param deliveryBillId
     * @return
     */
    DeliveryBill queryChildLeafById(@Param("deliveryBillId") String deliveryBillId, @Param("actualCarrierId") String actualCarrierId);

    /**
     * 获取委托单的叶子节点
     * @param deliveryBillId
     * @return
     */
    List<DeliveryBill> queryChildById(@Param("deliveryBillId") String deliveryBillId);

    /**
     * 单号查询
     * @param deliveryBillNum
     * @return
     */
    DeliveryBill queryByNum(@Param("deliveryBillNum") String deliveryBillNum);

    /**
     * 指派运力
     * @param deliveryBillId
     * @param assignQuantity
     * @param assignCount
     * @return
     */
    int assignTransportUpdate(@Param("deliveryBillId") String deliveryBillId,
                        @Param("assignQuantity") BigDecimal assignQuantity,
                        @Param("assignCount") Integer assignCount);

    /**
     * 获取配送信息的根委托单
     * @param deliveryInfoId
     * @return
     */
    DeliveryBill queryRootBillByInfoId(@Param("deliveryInfoId") String deliveryInfoId);

    /**
     * 反向增加承运发货量
     * @param deliveryBillIdList
     * @param sendQuantity
     * @param updateUser
     * @return
     */
    int backAddCarrySend(@Param("deliveryBillIdList") List<String> deliveryBillIdList,
                         @Param("sendQuantity") BigDecimal sendQuantity,
                         @Param("updateUser") String updateUser);

    /**
     * 反向增加委托发货量
     * @param deliveryBillIdList
     * @param sendQuantity
     * @param updateUser
     * @return
     */
    int backAddConsignSend(@Param("deliveryBillIdList") List<String> deliveryBillIdList,
                           @Param("sendQuantity") BigDecimal sendQuantity,
                           @Param("updateUser") String updateUser);

    /**
     * 反向增加承运完成量
     * @param deliveryBillIdList
     * @param carryCompleteQuantity
     * @param updateUser
     * @return
     */
    int backAddCarryComplete(@Param("deliveryBillIdList") List<String> deliveryBillIdList,
                             @Param("carryCompleteQuantity") BigDecimal carryCompleteQuantity,
                             @Param("carryPlanQuantity") BigDecimal carryPlanQuantity,
                             @Param("updateUser") String updateUser);

    /**
     * 反向增加委托完成量
     * @param deliveryBillIdList
     * @param consignCompleteQuantity
     * @param updateUser
     * @return
     */
    int backAddConsignComplete(@Param("deliveryBillIdList") List<String> deliveryBillIdList,
                             @Param("consignCompleteQuantity") BigDecimal consignCompleteQuantity,
                             @Param("updateUser") String updateUser);

    /**
     * 委托单列表分页查询
     */
    List<DeliveryListDTO> queryDeliveryList(DeliveryQueryDTO deliveryQueryDTO);
    /**
     * 委托单详情查询
     */
    DeliveryDetailDTO queryDeliveryDetailById(@Param("deliveryBillId") String deliveryBillId);

    /**
     * 根据委托单Id查询其下一层级所有子委托单信息
     */
    List<DeliveryDetailRecordsDTO> queryDeliveryByParentId(@Param("parentId") String parentId);

    /**
     * 根据委托单Id查询所有层级子委托单信息
     */
    List<DeliveryDetailRecordsDTO> queryAllDeliveryByParentId(@Param("parentId") String parentId);

    /**
     * 根据委托单Id查询其所有子委托单Id(包含自己)
     */
    List<String> queryDeliveryIdByParentId(@Param("parentId") String parentId);

    /**
     * 根据发货单号查询委托单信息
     */
    List<DeliveryBill> queryDeliveryByTakeCode(@Param("takeCode") String takeCode);

    /**
     * 根据发货单号查询处于指定状态的非虚拟委托单列表
     */
    List<DeliveryBill> queryByTakeCodeAndStatus(@Param("takeCodeList") List<String> takeCodeList, @Param("statusList") List<String> statusList);

    /**
     * 行锁查询
     * @param deliveryBillId 主键ID
     * @return
     */
    DeliveryBill selectByIdForUpdate(@Param("deliveryBillId")String deliveryBillId);

    /**
     * 更新委托单的数量
     * @param deliveryBillId
     * @param newStatus
     * @param oldStatus
     * @param updateUser
     * @return
     */
    int updateStatus(@Param("deliveryBillId")String deliveryBillId,
                     @Param("newStatus")String newStatus,
                     @Param("oldStatus")String oldStatus,
                     @Param("updateUser") String updateUser);

    /**
     * 委托量变更量修改
     * @param deliveryBillId
     * @param addConsignQuantity
     * @param updateUser
     * @return
     */
    int addConsignQuantity(@Param("deliveryBillId")String deliveryBillId,
                          @Param("addConsignQuantity") BigDecimal addConsignQuantity,
                          @Param("updateUser") String updateUser);

    /**
     * 添加改航数量
     * @param deliveryBillIdList
     * @param rerouteQuantity
     * @param updateUser
     * @return
     */
    int addRerouteQuantity(@Param("deliveryBillIdList")List<String> deliveryBillIdList,
                           @Param("rerouteQuantity") BigDecimal rerouteQuantity,
                           @Param("updateUser") String updateUser);

    /**
     * 改航修改委托单carry部分
     * @param deliveryBillId
     * @param reroutePlanQuantity
     * @param rerouteActualQuantity
     * @param updateUser
     * @return
     */
    int updateCarryForReroute(@Param("deliveryBillId") String deliveryBillId,
                              @Param("reroutePlanQuantity") BigDecimal reroutePlanQuantity,
                              @Param("rerouteActualQuantity") BigDecimal rerouteActualQuantity,
                              @Param("updateUser") String updateUser);

    /**
     * 改航修改委托单consign部分
     * @param deliveryBillId
     * @param reroutePlanQuantity
     * @param rerouteActualQuantity
     * @param updateUser
     * @return
     */
    int updateConsignForReroute(@Param("deliveryBillId") String deliveryBillId,
                               @Param("reroutePlanQuantity") BigDecimal reroutePlanQuantity,
                               @Param("rerouteActualQuantity") BigDecimal rerouteActualQuantity,
                               @Param("updateUser") String updateUser);

    /**
     * 委托单数量变更实体
     * @param deliveryBillChangeDTO
     * @return
     */
    int updateByQuantityAndCountChange(DeliveryBillChangeDTO deliveryBillChangeDTO);

    /**
     * 查询路径的委托单
     * @param billPathIds
     * @return
     */
    List<DeliveryChainBriDTO> queryBillChainByPathIds(@Param("billPathIds") List<String> billPathIds);

    /**
     * 改航后,反向更新委托单的状态
     * @param deliveryBillId
     * @param status
     * @param consignQuantityChange
     * @param updateUser
     * @return
     */
    int updateStatusBackAfterReroute(@Param("deliveryBillId") String deliveryBillId,
                                     @Param("status") String status,
                                     @Param("consignQuantityChange") BigDecimal consignQuantityChange,
                                     @Param("updateUser") String updateUser);

    /**
     * 根据委托单Id查询其非终止状态的子委托单Id(不包括虚拟委托单)
     * @param deliveryBillId
     * @return
     */
    List<String> getSubDoingBillIdList(@Param("deliveryBillId") String deliveryBillId);

    /**
     * 减少承运计划量
     * @param deliveryBillIdList
     * @param carryPlanQuantity
     * @param updateUser
     * @return
     */
    int reduceCarryPlanQuantity(@Param("deliveryBillIdList") List<String> deliveryBillIdList,
                                @Param("carryPlanQuantity") BigDecimal carryPlanQuantity,
                                @Param("updateUser") String updateUser);

    /**
     * 通过发货单号和商品物料编码查询委托单详情
     * @param takeCode
     * @param commodityCode
     * @return
     */
    DeliveryDetailDTO queryDeliveryBillByTakeCodeAndCommodityCode(@Param("takeCode") String takeCode,
                                                                  @Param("commodityCode") String commodityCode);

    /**
     * 通过订单号和商品物料编码查询委托单详情
     * @param orderCode
     * @param commodityCode
     * @return
     */
    DeliveryDetailDTO queryDeliveryBillByOrderCodeAndCommodityCode(@Param("orderCode") String orderCode, @Param("commodityCode") String commodityCode);

    /**
     * 查询混凝土的委托单详情
     * @param waybillId
     * @return
     */
    DeliveryDetailDTO queryDeliveryBillByConcreteWaybillId(@Param("waybillId") String waybillId);

    /**
     * 待整合委托单列表查询
     * @param waitMergeDeliveryQueryDTO
     * @return
     */
    List<DeliveryListDTO> queryWaitMergeDeliveryList(WaitMergeDeliveryQueryDTO waitMergeDeliveryQueryDTO);

    /**
     * 统计委托单承运人的承运运单未完成的数量
     * @param deliveryBillNum
     * @return
     */
    int countDeliveryCarryNotFinal(@Param("deliveryBillNum") String deliveryBillNum);

    /**
     * 查询委托单的起始点坐标
     * @param deliveryBillId
     * @return
     */
    BillLocationDO queryDeliveryLocation(@Param("deliveryBillId") String deliveryBillId);

    /**
     * 查询对账规则需要的委托单会员信息
     * @param deliverySimpleQueryDTO
     * @return
     */
    List<DeliverySimpleDTO> queryMemberInfoForCheckRule(DeliverySimpleQueryDTO deliverySimpleQueryDTO);

    /**
     * 委托单分状态统计
     * @param dto
     * @return
     */
    DeliveryStatisticsDTO statisticsDeliveryByStatusQuantity(DeliveryQueryDTO dto);

    /**
     * 紧急任务列表查询,只需查询出当天待安排的数据
     */
    List<DeliveryListDTO> queryUrgentTaskList(QueryUrgentTaskDTO queryDTO);

    /**
     * 紧急任务列表分页查询,只需查询出历史待安排的数据
     */
    List<DeliveryListDTO> queryUrgentTaskHistoryList(QueryUrgentTaskDTO queryDTO);

    /**
     * 查询出每天运力总和
     * @param dto
     * @return
     */
    TransportCapacityResultDTO queryCapacityCount(TransportCapacityDTO dto);

    /**
     * 查询出承运商安排车辆次数
     * @param dto
     * @return
     */
    TransportCapacityResultDTO getShipBillNum(TransportCapacityDTO dto);

    /**
     * 根据委托单ID集合查询待智能调度的委托信息列表
     * @param deliveryBillIdList
     * @return
     */
    List<PlanDeliveryListDTO> selectPlanDeliveryByIdList(@Param("deliveryBillIdList") List<String> deliveryBillIdList);

    /**
     * 根据委托单id查询出所有委托单信息
     * @param pathBillIdList
     * @return
     */
    List<DeliveryBill> queryDeliveryListById(@Param("pathBillIdList") List<String> pathBillIdList);

    /**
     * 获取已配送和已完成的数量
     * @param dto
     * @return
     */
    List<ShipBillQuantityDTO> getSameDayQuantity(DeliveryQueryDTO dto);

    /**
     * 无车承运人 -- 托运合同上报信息查询
     * @param deliveryBillNum
     * @return
     */
    ContractSubmitInfoDTO queryContractInfo(String deliveryBillNum);

    /**
     * 混凝土数量变化时,减少承运相关量
     * @param carryDeliveryBillIdList
     * @param reduceQuantity
     * @param updateUser
     * @return
     */
    int reduceCarryForConcrete(@Param("carryDeliveryBillIdList") List<String> carryDeliveryBillIdList,
                               @Param("reduceQuantity") BigDecimal reduceQuantity,
                               @Param("updateUser") String updateUser);

    /**
     * 混凝土数量变化时,减少委托相关量
     * @param consignDeliveryBillIdList
     * @param reduceQuantity
     * @param updateUser
     * @return
     */
    int reduceConsignForConcrete(@Param("consignDeliveryBillIdList") List<String> consignDeliveryBillIdList,
                                 @Param("reduceQuantity") BigDecimal reduceQuantity,
                                 @Param("updateUser") String updateUser);

    /**
     * 计划量改为完成量
     * @param deliveryBillIdList
     * @param updateUser
     * @return
     */
    int updatePlanToQuantity(@Param("deliveryBillIdList") List<String> deliveryBillIdList,
                             @Param("updateUser") String updateUser);

    /**
     * 新增委托单数量
     * @param addQuantity
     * @param deliveryBillIdList
     * @param updateUser
     * @return
     */
    int addQuantity(@Param("addQuantity") BigDecimal addQuantity,
                    @Param("deliveryBillIdList") List<String> deliveryBillIdList,
                    @Param("updateUser") String updateUser);

	List<LogisticsAdjustPriceMemberDTO> queryDeliverBillId(LogisticsAdjustPriceDTO queryDTO);

	List<String> queryLeafFlagDeliverBillId(@Param("deliveryBillId") String deliveryBillId);

    /**
     * 新增委托单数量
     * @param deliveryInfoId
     * @param carrierId
     * @param carrierName
     * @return
     */
    int updateDeliveryInfoCarrier(@Param("deliveryInfoId") String deliveryInfoId,
                    @Param("carrierId") String carrierId,
                    @Param("carrierName") String carrierName);

    DeliveryBill queryBillByBillNum(@Param("deliveryBillNum") String deliveryBillNum);

    /**
     * 获取委托单ID集合
     * @param adjustPriceMemberDTO
     * @return
     */
    List<String> getDeliverBillId(LogisticsAdjustPriceMemberDTO adjustPriceMemberDTO);
}
