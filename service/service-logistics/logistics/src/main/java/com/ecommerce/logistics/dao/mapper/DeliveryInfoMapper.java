package com.ecommerce.logistics.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.api.dto.DeliveryFinishTakeDTO;
import com.ecommerce.logistics.api.dto.adjust.AdjustTakeInfoDTO;
import com.ecommerce.logistics.api.dto.waybill.DeliveryInfoProxyDTO;
import com.ecommerce.logistics.dao.vo.DeliveryInfo;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

public interface DeliveryInfoMapper extends IBaseMapper<DeliveryInfo> {

    /**
     * 获取当前Info的父级InfoId
     * @param deliveryInfoId
     * @return
     */
    String selectParentInfoId(@Param("deliveryInfoId") String deliveryInfoId);

    /**
     * 获取背靠背的另一级InfoId
     * @param parentInfoId
     * @param deliveryInfoId
     * @return
     */
    String selectProxyInfoId(@Param("parentInfoId") String parentInfoId, @Param("deliveryInfoId") String deliveryInfoId);

    /**
     * 获取背靠背的另一级takeCode
     * @param parentInfoId
     * @param deliveryInfoId
     * @return
     */
    List<String> selectProxyTakeCode(@Param("parentInfoId") String parentInfoId, @Param("deliveryInfoId") String deliveryInfoId);

    /**
     * 通过发货单号查询其发货单号下所有订单子项ID
     */
    List<String> queryOrderItemIdByTakeCode(@Param("takeCode") String takeCode);

    /**
     * 通过发货单号查询所有委托单信息
     * @param takeCode
     * @return
     */
    List<DeliveryInfo> queryInfoByTakeCode(@Param("takeCode") String takeCode);

    /**
     * 根据发货单号和订单子项Id查询委托单信息
     */
    List<DeliveryFinishTakeDTO> queryDeliveryByTakeCodeAndOrderItemId(@Param("takeCode") String takeCode,@Param("orderItemId") String orderItemId);

    /**
     * 根据deliveryBillId查询发货单号
     * @param deliveryBillId
     * @return
     */
    String queryTakeCodeByDeliveryBillId(@Param("deliveryBillId") String deliveryBillId);

    /**
     * 根据deliveryBillId查询委托单信息
     * @param deliveryBillId
     * @return
     */
    DeliveryInfo queryInfoByDeliveryBillId(@Param("deliveryBillId") String deliveryBillId);

    /**
     * 配送信息主键查询发货单号集合
     * @param deliveryInfoIdList
     * @return
     */
    List<String> selectTakeCodeByInfoList(@Param("deliveryInfoIdList") List<String> deliveryInfoIdList);

    /**
     * 运单ID查找可操作方的配送信息
     * @param waybillId
     * @return
     */
    DeliveryInfo selectCanOperateByWaybillId(@Param("waybillId") String waybillId);

    /**
     * 统计配送信息下,已经签收的数量(仅用于混凝土)
     * @param deliveryInfoId
     * @return
     */
    BigDecimal sumSignQuantity(@Param("deliveryInfoId") String deliveryInfoId);


    DeliveryInfo selectDeliveryInfo(@Param("deliveryBillId") String deliveryBillId);

    List<DeliveryInfoProxyDTO> queryProxyInfoList(@Param("parentInfoId") String parentInfoId);

    DeliveryInfo selectDeliveryInfoByWaybillId(@Param("waybillId") String waybillId);

    DeliveryInfo selectRealBuyerDeliveryInfoByWaybillId(@Param("waybillId") String waybillId);

    /**
     * 运单ID查询厂家
     * @param waybillId
     * @return
     */
    String selectRealSellerByWaybillId(@Param("waybillId") String waybillId);

    /**
     * 基于发货单的调价(只能支持一个发货单一个价)
     * @param adjustTakeInfoDTO
     * @return
     */
    int updatePriceByTakeCode(AdjustTakeInfoDTO adjustTakeInfoDTO);
}