package com.ecommerce.logistics.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.api.dto.delivery.DeliveryNoteDetailDTO;
import com.ecommerce.logistics.api.dto.delivery.DeliveryNoteDetailQueryDTO;
import com.ecommerce.logistics.api.dto.delivery.DeliveryNoteListDTO;
import com.ecommerce.logistics.api.dto.delivery.DeliveryNoteQueryDTO;
import com.ecommerce.logistics.dao.vo.DeliveryNote;

import java.util.List;

public interface DeliveryNoteMapper extends IBaseMapper<DeliveryNote> {

    /**
     * 送货单详情查询
     * @param deliveryNoteDetailQueryDTO
     * @return
     */
    DeliveryNoteDetailDTO queryDetail(DeliveryNoteDetailQueryDTO deliveryNoteDetailQueryDTO);

    /**
     * 送货单列表查询[返回值不带商品名]
     * @param deliveryNoteQueryDTO
     * @return
     */
    List<DeliveryNoteListDTO> queryMainList(DeliveryNoteQueryDTO deliveryNoteQueryDTO);

}