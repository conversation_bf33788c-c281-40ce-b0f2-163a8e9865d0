package com.ecommerce.logistics.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.dao.dto.delivery.DeliveryNoteProductItem;
import com.ecommerce.logistics.dao.dto.delivery.DeliverySendBean;
import com.ecommerce.logistics.dao.vo.DeliverySendMap;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DeliverySendMapMapper extends IBaseMapper<DeliverySendMap> {

    /**
     * 查询送货单下的商品名
     * @param deliveryNoteIdList
     * @param productNameLike
     * @return
     */
    List<DeliveryNoteProductItem> selectProductNameByNoteId(@Param("deliveryNoteIdList") List<String> deliveryNoteIdList, @Param("productNameLike") String productNameLike);

    List<DeliverySendBean> selectSendBeanByDeliveryNote(@Param("deliveryNoteId") String deliveryNoteId);

    List<DeliverySendBean> selectByDeliveryNoteIdAndSubStatusList(@Param("deliveryNoteId") String deliveryNoteId, @Param("subStatusList") List<String> subStatusList);

}