package com.ecommerce.logistics.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.api.dto.dispatchbill.DispatchBillListDTO;
import com.ecommerce.logistics.api.dto.dispatchbill.DispatchBillListQueryDTO;
import com.ecommerce.logistics.api.dto.dispatchbill.TransportCapacityQueryDTO;
import com.ecommerce.logistics.api.dto.pickingbill.PickingBillAssignDispatchDTO;
import com.ecommerce.logistics.dao.dto.dispatchbill.CapacityDO;
import com.ecommerce.logistics.dao.dto.dispatchbill.DispatchBillDO;
import com.ecommerce.logistics.dao.dto.dispatchbill.ExportDispatchBillDO;
import com.ecommerce.logistics.dao.vo.DispatchBill;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 * @Auther: <EMAIL>
 * @Date: 2018年8月9日 下午6:21:19
 * @Description:
 */
public interface DispatchBillMapper extends IBaseMapper<DispatchBill> {

	/**
	 * 分页查询调度单列表
	 * @param dispatchBillListQueryDTO
	 * @return
	 */
	List<DispatchBillDO> selectDispatchBillList(DispatchBillListQueryDTO dispatchBillListQueryDTO);


	/**
	 * 通过dispatch_bill_id查询调度单详情
	 * @param dispatchBillId
	 * @return
	 */
	DispatchBillDO selectDispatchBillDetail(@Param("dispatchBillId")String dispatchBillId);

	String selectProductInfoIdByDispatchBillId(@Param("dispatchBillId")String dispatchBillId);

	/**
	 * 根据dispatch_bill_id查询picking_bill_id以及quantity
	 * @Auther: <EMAIL>
	 * @Description:
	 * @param dispatchBillId
	 * @return
	 */
	DispatchBillDO selectDispatchBillPartInfo(@Param("dispatchBillId")String dispatchBillId);


	void updateDelFlgByKey(@Param("dispatchBillId")String dispatchBillId, @Param("operatorId")String operatorId);

	
	/**
	 * 提货单ID批量查询调度单列表
	 * @param pickingBillIds
	 * @return
	 */
	List<DispatchBillListDTO> selectDispatchBillByPickingBillIds(List<String> pickingBillIds);

	
	/**
	 * 提货单ID批量查询调度单列表
	 * @param pickingBillIds
	 * @return
	 */
	List<PickingBillAssignDispatchDTO> selectPickingBillAssigns(@Param("pickingBillId")String pickingBillId);
	
	/**
	 * 查询承运商已指派运力(即状态为(已指派和指派中)的总量-总完成量)
	 * @Auther: <EMAIL>
	 * @Date: 2018年8月29日 下午5:06:35
	 * @return Map<String, BigDecimal>
	 */
	List<CapacityDO> selectAssignCapacityByCarrierId(@Param("capacityQueryDTO")TransportCapacityQueryDTO transportCapacityQueryDTO,
											   				@Param("statusList")List<String> statusList);

	/**
	 * 查询调度单部分信息
	 * @Auther: <EMAIL>
	 * @Date: 2018年9月3日 下午8:08:25
	 * @param pickingBillId
	 * @return
	 */
	DispatchBillDO selectPartInfoById(@Param("dispatchBillId")String dispatchBillId);

	/**
	 * 关闭超时的可关闭调度单
	 * @param dispatchBillIdList
	 */
	void closeExpireDispatchBillList(@Param("dispatchBillIdList") List<String> dispatchBillIdList);

	List<ExportDispatchBillDO> selectDispatchBillForDownload(DispatchBillListQueryDTO dispatchBillListQueryDTO);

	/**
	 * 发货单号查询处于指定状态的调度单列表
	 * @param deliverySheetNumList
	 * @param statusList
	 * @return
	 */
	List<DispatchBill> selectByDeliverySheetNumAndStatus(@Param("deliverySheetNumList") List<String> deliverySheetNumList, @Param("statusList") List<String> statusList);


}