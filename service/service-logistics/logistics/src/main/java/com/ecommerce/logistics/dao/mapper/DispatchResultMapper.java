package com.ecommerce.logistics.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.api.dto.auto.VehicleAutoAssignDTO;
import com.ecommerce.logistics.api.dto.auto.VehicleAutoAssignQueryDTO;
import com.ecommerce.logistics.api.dto.pickingbill.OptionDTO;
import com.ecommerce.logistics.dao.vo.DispatchResult;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DispatchResultMapper extends IBaseMapper<DispatchResult> {

    /**
     * 批次ID查询仓库下拉列表
     * @param batchId
     * @return
     */
    List<OptionDTO> warehouseOptionsByBatchId(@Param("batchId") String batchId);

    /**
     * 批次ID查询收货地址下拉列表
     * @param batchId
     * @return
     */
    List<OptionDTO> receiverAddressOptionsByBatchId(@Param("batchId") String batchId);

    /**
     * 更新状态
     * @param dispatchResultIdList
     * @param oldStatus
     * @param newStatus
     * @param operateUserId
     * @return
     */
    int updateStatus(@Param("dispatchResultIdList") List<String> dispatchResultIdList,
                     @Param("oldStatus") String oldStatus,
                     @Param("newStatus") String newStatus,
                     @Param("operateUserId") String operateUserId);

    /**
     * 更新司机信息
     * @param vehicleAutoAssignDTO
     * @return
     */
    int updateDriverInfo(VehicleAutoAssignDTO vehicleAutoAssignDTO);

    /**
     * 分页查询
     * @param queryDTO
     * @return
     */
    List<VehicleAutoAssignDTO> queryAutoWaitAssignList(VehicleAutoAssignQueryDTO queryDTO);

}