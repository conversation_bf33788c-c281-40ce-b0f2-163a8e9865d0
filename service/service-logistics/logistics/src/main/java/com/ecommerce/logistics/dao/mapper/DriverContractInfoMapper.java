package com.ecommerce.logistics.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.api.dto.contract.DriverContractListDTO;
import com.ecommerce.logistics.api.dto.contract.DriverContractListQueryDTO;
import com.ecommerce.logistics.dao.vo.DriverContractInfo;

import java.util.List;

public interface DriverContractInfoMapper extends IBaseMapper<DriverContractInfo> {


    /**
     * 查询司机合同列表
     * @param contractListQueryDTO
     * @return
     */
    List<DriverContractListDTO> queryDriverContractList(DriverContractListQueryDTO contractListQueryDTO);
}