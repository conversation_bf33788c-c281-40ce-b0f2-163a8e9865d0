package com.ecommerce.logistics.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.api.dto.dry.season.policy.DrySeasonPolicyListDTO;
import com.ecommerce.logistics.api.dto.dry.season.policy.DrySeasonPolicyQueryDTO;
import com.ecommerce.logistics.dao.vo.DrySeasonPricePolicy;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

public interface DrySeasonPricePolicyMapper extends IBaseMapper<DrySeasonPricePolicy> {

    List<DrySeasonPolicyListDTO> pageInfoPolicy(@RequestParam("policyIds") String policyIds);

    List<String> findPolicyId(DrySeasonPolicyQueryDTO dto);
}