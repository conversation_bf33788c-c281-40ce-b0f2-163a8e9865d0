package com.ecommerce.logistics.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.dao.vo.DrySeasonPricePolicyTime;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface DrySeasonPricePolicyTimeMapper extends IBaseMapper<DrySeasonPricePolicyTime> {

    @Select("select distinct policy_id from lgs_dry_season_price_policy_time where start_time<=NOW() and end_time>=NOW() and del_flg=0")
    List<String> findEffectivePolicyId();
}