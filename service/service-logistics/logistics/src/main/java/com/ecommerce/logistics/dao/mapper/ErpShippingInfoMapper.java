package com.ecommerce.logistics.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.api.dto.shipping.erp.ErpShippingInfoDTO;
import com.ecommerce.logistics.dao.vo.ErpShippingInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ErpShippingInfoMapper extends IBaseMapper<ErpShippingInfo> {


    /**
     * 根据ERP承运商编号查询ERP的船舶
     * @param erpCarrierNo ERP承运商编号
     * @return
     */
    List<ErpShippingInfoDTO> findErpShippingByErpCarrierNo(@Param("erpCarrierNo")String erpCarrierNo, @Param("manufacturerMemberId")String manufacturerMemberId);

    ErpShippingInfo findErpInfo(@Param("memberId") String memberId, @Param("erpShipName") String erpShipName,
                                @Param("erpShipCode") String erpShipCode);

}