package com.ecommerce.logistics.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.api.dto.externalexception.ExternalExceptionListDTO;
import com.ecommerce.logistics.api.dto.externalexception.ExternalExceptionQueryDTO;
import com.ecommerce.logistics.dao.vo.ExternalExceptionHandle;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ExternalExceptionHandleMapper extends IBaseMapper<ExternalExceptionHandle> {

    /**
     * 查询外部异常列表
     * @param externalExceptionQueryDTO 外部异常列表查询对象
     * @return ExternalExceptionListDTO
     */
     List<ExternalExceptionListDTO> queryExternalExceptionList(ExternalExceptionQueryDTO externalExceptionQueryDTO);

    /**
     * 发货单下,创建失败的运单,对应的异常记录表主键ID的查询
     * @param deliverySheetNumList
     * @return
     */
     List<String> queryExceptionIdsForCreateWaybillFail(@Param("deliverySheetNumList") List<String> deliverySheetNumList);

}