package com.ecommerce.logistics.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.api.dto.gps.GpsManCondDTO;
import com.ecommerce.logistics.api.dto.gps.GpsManufacturerDTO;
import com.ecommerce.logistics.api.dto.gps.GpsManufacturerOptionDTO;
import com.ecommerce.logistics.api.dto.gpsmanufacturer.GpsNameDTO;
import com.ecommerce.logistics.dao.vo.GpsManufacturer;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface GpsManufacturerMapper extends IBaseMapper<GpsManufacturer> {
    List<GpsNameDTO>queryGpsNameByIds(@Param("ids")List<String> ids);

    List<GpsManufacturerOptionDTO> queryOptions();

    String queryGpsNameById(@Param("gpsManufacturerId") String gpsManufacturerId);

    List<GpsManufacturerDTO> queryGpsManByCond(GpsManCondDTO gpsManCondDTO);
}