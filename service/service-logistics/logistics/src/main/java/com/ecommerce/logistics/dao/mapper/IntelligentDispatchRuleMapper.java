package com.ecommerce.logistics.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.api.dto.intelligentDispatch.IntelligentDispatchListDTO;
import com.ecommerce.logistics.api.dto.intelligentDispatch.IntelligentDispatchQueryDTO;
import com.ecommerce.logistics.api.dto.intelligentDispatch.IntelligentDispatchRuleDTO;
import com.ecommerce.logistics.dao.vo.IntelligentDispatchRule;

import java.util.List;

public interface IntelligentDispatchRuleMapper extends IBaseMapper<IntelligentDispatchRule> {

    /**
     * 查询智能调度规则列表
     * @param queryDTO
     * @return
     */
    List<IntelligentDispatchListDTO> queryRuleList(IntelligentDispatchQueryDTO queryDTO);

    /**
     * 查询所有规则
     * @return
     */
    List<IntelligentDispatchListDTO> queryAllRule();

    /**
     * 查询出用户下面的规则并按照约束，优先规则排序
     * @param queryDTO
     * @return
     */
    List<IntelligentDispatchRuleDTO> queryIntelligentDispatchRule(IntelligentDispatchQueryDTO queryDTO);

}