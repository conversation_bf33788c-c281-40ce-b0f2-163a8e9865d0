package com.ecommerce.logistics.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.api.dto.delivery.InternalAllocationDTO;
import com.ecommerce.logistics.api.dto.waybill.AllocationListQueryDTO;
import com.ecommerce.logistics.dao.vo.InternalAllocation;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface InternalAllocationMapper extends IBaseMapper<InternalAllocation> {

    int delInternalAllocation(InternalAllocationDTO dto);

    int updateInternalAllocation(InternalAllocation allocation);

    List<InternalAllocationDTO> queryInternalAllocationList(AllocationListQueryDTO queryDTO);

    InternalAllocation getInternalAllocation(InternalAllocationDTO dto);

    InternalAllocation getInternalByBillId(@Param("deliveryBillId") String deliveryBillId);

    int updateInternalPlanStatus(InternalAllocationDTO dto);

}
