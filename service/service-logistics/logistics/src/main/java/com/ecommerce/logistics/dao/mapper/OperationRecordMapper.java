package com.ecommerce.logistics.dao.mapper;

import java.util.List;

import com.ecommerce.logistics.api.dto.operationrecord.OperationRecordDTO;
import com.ecommerce.logistics.api.dto.operationrecord.QueryWaybillOperationDTO;
import org.apache.ibatis.annotations.Param;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.dao.vo.OperationRecord;

public interface OperationRecordMapper extends IBaseMapper<OperationRecord> {
    /**
     * 获取操作记录列表
     * @param entryId
     * @return
     */
    List<OperationRecord> selectOperationRecordListDTO(@Param("entryId")String entryId);

    /**
     * 查询运单操作记录
     * @param queryWaybillOperationDTO 查询操作记录对象
     * @return List<OperationRecordDTO>
     */
    List<OperationRecordDTO> queryWaybillOperation(QueryWaybillOperationDTO queryWaybillOperationDTO);
}