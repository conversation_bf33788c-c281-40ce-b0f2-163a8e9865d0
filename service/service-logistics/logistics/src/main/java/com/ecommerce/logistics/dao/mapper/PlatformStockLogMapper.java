package com.ecommerce.logistics.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.api.dto.storehouse.TurnoverQueryDTO;
import com.ecommerce.logistics.dao.dto.storehouse.TurnoverCondBean;
import com.ecommerce.logistics.dao.vo.PlatformStockLog;

import java.util.List;

public interface PlatformStockLogMapper extends IBaseMapper<PlatformStockLog> {

    List<String> querySellerIdByTurnoverCond(TurnoverQueryDTO turnoverQueryDTO);

    List<String> queryProductIdByTurnoverCond(TurnoverQueryDTO turnoverQueryDTO);

    List<PlatformStockLog> queryLogByTurnoverCond(TurnoverCondBean turnoverCondBean);

}