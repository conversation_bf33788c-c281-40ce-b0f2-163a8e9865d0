package com.ecommerce.logistics.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.api.dto.storehouse.PlatformStockListDTO;
import com.ecommerce.logistics.dao.dto.storehouse.PlatformStoreChangeBean;
import com.ecommerce.logistics.dao.vo.PlatformStoreMap;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PlatformStoreMapMapper extends IBaseMapper<PlatformStoreMap> {

    /**
     * 修改平台仓库占用库存
     * @param platformStoreChangeBean
     * @return
     */
    int changeStock(PlatformStoreChangeBean platformStoreChangeBean);

    /**
     * 修改平台仓库占用库存,不校验存量
     * @param platformStoreChangeBean
     * @return
     */
    int changeStockWithoutCheck(PlatformStoreChangeBean platformStoreChangeBean);

    /**
     * 检验出厂量是否合法
     * @param platformStoreChangeBean
     * @return
     */
    int checkForLeaveCenterWarehouse(PlatformStoreChangeBean platformStoreChangeBean);

    /**
     *
     * @param userId
     * @param noteLike
     * @return
     */
    PlatformStockListDTO selectSellerPlatformStock(@Param("userId") String userId, @Param("noteLike") String noteLike, @Param("warehouseId") String warehouseId);

    /**
     * 获取占用平台仓库的买家信息
     * @param noteLike
     * @return
     */
    List<PlatformStockListDTO> selectPlatformStockSellerInfo(@Param("noteLike") String noteLike, @Param("warehouseId") String warehouseId);

    /**
     * 查询卖家商品库存
     * @param userIdList
     * @param noteLike
     * @return
     */
    List<PlatformStockListDTO> selectProductStockList(@Param("userIdList") List<String> userIdList, @Param("noteLike") String noteLike, @Param("warehouseId") String warehouseId);

}