package com.ecommerce.logistics.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.dao.dto.porterage.ZoneRuleQueryDO;
import com.ecommerce.logistics.dao.vo.PorterageRule;

import java.util.List;

public interface PorterageRuleMapper extends IBaseMapper<PorterageRule> {

    /**
     * 查询区域规则列表
     * @param zoneRuleQueryDO 区域规则查询对象
     * @return List<PorterageRule>
     */
    List<PorterageRule> selectZoneRuleList(ZoneRuleQueryDO zoneRuleQueryDO);
}