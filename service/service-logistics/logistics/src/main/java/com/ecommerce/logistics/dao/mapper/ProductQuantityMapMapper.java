package com.ecommerce.logistics.dao.mapper;

import java.math.BigDecimal;
import java.util.List;

import com.ecommerce.logistics.api.dto.pickingbill.ProductDetailDTO;
import org.apache.ibatis.annotations.Param;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.dao.dto.pickingbill.ProductQuantityDO;
import com.ecommerce.logistics.dao.vo.ProductQuantityMap;

public interface ProductQuantityMapMapper extends IBaseMapper<ProductQuantityMap> {
	
	/**
	 * 根据map_id建设商品分配数量
	 * @param mapId
	 * @param quantity 减少的数量
	 * @param operatorId
	 */
	public void reduceDeliveryQuantityByMapId(@Param("mapId")String mapId, @Param("deliveryQuantity")BigDecimal quantity,
			@Param("operatorId")String operatorId);
	
	/**
	 * 将del_flg置为删除(1)
	 * @Auther: <EMAIL>
	 * @Description:
	 * @param operatorId
	 * @param mapIds
	 */
	public void updateDelFlgByMapIds(@Param("mapIds")List<String> mapIds,@Param("operatorId")String operatorId);

	/**
	 * 根据map_id增加商品分配数量
	 * @param mapId
	 * @param quantity 增加的数量
	 * @param operatorId
	 */
	public void increaseDeliveryQuantityByMapId(@Param("mapId")String mapId, @Param("deliveryQuantity")BigDecimal quantity,
			@Param("operatorId")String operatorId);
	
	/**
	 * 根据map_id查询商品数量与商品已分配数量
	 * @Auther: <EMAIL>
	 * @Date: 2018年8月25日 下午12:16:41
	 * @param mapId
	 * @return
	 */
	public ProductQuantityMap selectPartQuantityInfoByMapId(@Param("mapId")String mapId);
	
	public ProductQuantityDO selectProductInfoByMapId(@Param("mapId")String mapId);
	
	/**
	 * 增加商品完成数量
	 * @Auther: <EMAIL>
	 * @Date: 2018年9月19日 下午7:54:22
	 * @param mapId
	 * @param quantity
	 * @param operatorId
	 */
	public void increaseCompleteQuantityByMapId(@Param("mapId")String mapId, @Param("completeQuantity")BigDecimal quantity,
			@Param("operatorId")String operatorId);

	/**
	 * 更新已发货数量
	 * @param mapId
	 * @param quantity
	 * @param operatorId
	 */
	public void increaseShippingQuantityByMapId(@Param("mapId")String mapId,
												@Param("shippingQuantity")BigDecimal quantity,
												@Param("operatorId")String operatorId);

	/**
	 * 单据数量更新悲观锁
	 * @param mapId 单据ID
	 * @return String
	 */
	String queryProductQuantityForUpdate(String mapId);
	
	/**
	 * 
	 * @Auther: <EMAIL>
	 * @Date: 2018年10月9日 上午10:36:00
	 * @param mapIds
	 * @return
	 */
	BigDecimal selectTotalQuantityByMapIds(@Param("mapIds")List<String> mapIds);
	
	/**
	 * 通过主运单ID查找总重量
	 * @Auther: <EMAIL>
	 * @Date: 2018年10月12日 下午3:57:22
	 * @param mainWaybillId
	 * @return
	 */
	BigDecimal selectTotalQuantityByMainWaybillId(@Param("waybillId")String mainWaybillId);

	/**
	 * 父运单ID查询配送map
	 * @param parentWaybillId
	 * @return
	 */
	List<ProductQuantityMap> selectMapByParentWaybillId(@Param("parentWaybillId") String parentWaybillId);

	/**
	 * 退回数量
	 * @param mapId
	 * @param productInfoId
	 * @param deltaQuantity
	 */
	int revertQuantity(@Param("mapId") String mapId,
						@Param("productInfoId") String productInfoId,
						@Param("deltaQuantity") BigDecimal deltaQuantity,
					   @Param("updateUser") String updateUser);


	/**
	 * 查询map的映射商品信息
	 * @param mapId
	 * @param mapType
	 * @return
	 */
	List<ProductDetailDTO> selectByMapId(@Param("mapId") String mapId, @Param("mapType") Integer mapType);

}