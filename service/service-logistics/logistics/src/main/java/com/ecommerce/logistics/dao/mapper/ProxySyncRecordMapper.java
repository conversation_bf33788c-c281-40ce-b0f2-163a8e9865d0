package com.ecommerce.logistics.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.api.dto.ProxySyncRecord.ProxySyncRecordListDTO;
import com.ecommerce.logistics.api.dto.ProxySyncRecord.ProxySyncRecordQueryDTO;
import com.ecommerce.logistics.dao.vo.ProxySyncRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ProxySyncRecordMapper extends IBaseMapper<ProxySyncRecord> {

    /**
     * 操作记录的状态更新
     * @param proxySyncId
     * @param syncStatus
     * @param syncDesc
     * @return
     */
    int updateStatusById(@Param("proxySyncId") String proxySyncId,
                         @Param("syncStatus") String syncStatus,
                         @Param("syncDesc") String syncDesc);

    /**
     * 条件查询背靠背单据同步记录
     * @param proxySyncRecordQueryDTO
     * @return List<ProxySyncRecordListDTO>
     */
    List<ProxySyncRecordListDTO> queryProxySyncRecordList(ProxySyncRecordQueryDTO proxySyncRecordQueryDTO);
}