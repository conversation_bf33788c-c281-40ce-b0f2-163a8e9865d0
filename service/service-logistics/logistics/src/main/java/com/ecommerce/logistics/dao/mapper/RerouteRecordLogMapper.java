package com.ecommerce.logistics.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.api.dto.waybill.RerouteRecordLogDTO;
import com.ecommerce.logistics.dao.vo.RerouteRecordLog;

import java.util.List;

public interface RerouteRecordLogMapper extends IBaseMapper<RerouteRecordLog> {

    List<RerouteRecordLogDTO> queryRerouteLogList(RerouteRecordLogDTO recordLogDTO);

}