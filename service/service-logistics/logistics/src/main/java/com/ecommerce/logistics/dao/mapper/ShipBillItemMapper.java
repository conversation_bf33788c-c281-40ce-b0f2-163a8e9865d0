package com.ecommerce.logistics.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.api.dto.ShipBillWaitRerouteListDTO;
import com.ecommerce.logistics.api.dto.shipbill.CheckWaybillInfoDTO;
import com.ecommerce.logistics.api.dto.shipbill.CheckWaybillQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.CarryWaybillSubmitDTO;
import com.ecommerce.logistics.biz.message.dto.ShipBillSMSDTO;
import com.ecommerce.logistics.dao.dto.pickingbill.WaybillDO;
import com.ecommerce.logistics.dao.vo.ShipBillItem;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

public interface ShipBillItemMapper extends IBaseMapper<ShipBillItem> {

    /**
     * 查询指定批次的运单子项
     * @param waybillId
     * @param seqNum
     * @return
     */
    List<ShipBillItem> selectByWaybillIdAndSeq(@Param("waybillId") String waybillId, @Param("seqNum") Integer seqNum);

    /**
     * 获取运单集合的子项集合
     * @param waybillIdList
     * @return
     */
    List<ShipBillItem> selectItemsByWaybillIdList(@Param("waybillIdList") List<String> waybillIdList);

    /**
     * 修改运单子项
     * @param waybillItemId
     * @param newStatus
     * @param oldStatusList
     * @param updateUser
     * @return
     */
    int updateWaybillItemStatus(@Param("waybillItemId") String waybillItemId,
                                @Param("newStatus") String newStatus,
                                @Param("oldStatusList") List<String> oldStatusList,
                                @Param("updateUser") String updateUser);

    /**
     * 修改运单子项金额
     * @param waybillItemId
     * @param payQuantity
     * @param goodsPrice
     * @param settlementAmount
     * @param updateUser
     * @return
     */
    int updateWaybillItemQuantity(@Param("waybillItemId") String waybillItemId,
                                  @Param("payQuantity") BigDecimal payQuantity,
                                  @Param("goodsPrice") BigDecimal goodsPrice,
                                  @Param("settlementAmount") BigDecimal settlementAmount,
                                  @Param("updateUser") String updateUser);

    /**
     * 查询运单指定层级的运单子项ID
     * @param waybillId
     * @param billProxyType
     * @param seqNum
     * @return
     */
    String selectItemIdByWaybillProxy(@Param("waybillId") String waybillId,
                                      @Param("billProxyType") String billProxyType,
                                      @Param("seqNum") Integer seqNum);

    /**
     * 删除运单指定序列号的子项
     * @param waybillId
     * @param seqNum
     * @param updateUser
     * @return
     */
    int deleteItemByWaybillAndSeq(@Param("waybillId") String waybillId,
                                  @Param("seqNum") Integer seqNum,
                                  @Param("updateUser") String updateUser);

    /**
     * 查询卖家指定商品的可改航运单列表信息
     * @param sellerId
     * @param excludeUnloadPortId
     * @param goodsId
     * @param pickingBillType
     * @param billProxyTypeList
     * @param shipBillStatus
     * @param transportToolType
     * @return
     */
    List<ShipBillWaitRerouteListDTO> selectWaitRerouteShipBillList(@Param("sellerId") String sellerId,
                                                                   @Param("excludeUnloadPortId") String excludeUnloadPortId,
                                                                   @Param("goodsId") String goodsId,
                                                                   @Param("pickingBillType") String pickingBillType,
                                                                   @Param("billProxyTypeList") List<String> billProxyTypeList,
                                                                   @Param("shipBillStatus") String shipBillStatus,
                                                                   @Param("transportToolType") String transportToolType);


    /**
     * 计算委托单下指定状态集合的item数量
     * @param realDeliveryBillNum
     * @param itemStatusList
     * @return
     */
    int countByRealDeliveryNumAndStatusList(@Param("realDeliveryBillNum") String realDeliveryBillNum,
                                            @Param("itemStatusList") List<String> itemStatusList);

    /**
     * 通过运单号查询erp运单子项
     * @param waybillId
     * @return
     */
    ShipBillItem selectErpShipBillItem(@Param("waybillId") String waybillId);

    /**
     * 通过运单号查询所有运单子项
     * @param waybillId
     * @return
     */
    List<ShipBillItem> selectShipBillItemByWaybillId(@Param("waybillId") String waybillId);

    /**
     *
     * @param waybillId
     * @param seqNum
     * @param oldStatus
     * @param newStatus
     * @param updateUser
     * @return
     */
    int closeItemByWaybillId(@Param("waybillId") String waybillId,
                             @Param("seqNum") Integer seqNum,
                             @Param("oldStatus") String oldStatus,
                             @Param("newStatus") String newStatus,
                             @Param("updateUser") String updateUser);

    /**
     * 查新一个当前可以出站的子项ID
     * @param waybillId
     * @return
     */
    String selectCurrentItemForLeaveWarehouse(@Param("waybillId") String waybillId);

    /**
     * 根据运单子项Id查询运单通知实体
     * @param waybillItemId
     * @return
     */
    ShipBillSMSDTO queryShipBillSMSByItemId(@Param("waybillItemId") String waybillItemId);

    /**
     * 查询对账单-运单明细信息
     * @param queryDTO
     * @return
     */
    List<CheckWaybillInfoDTO> queryBillCheckWaybillInfoList(CheckWaybillQueryDTO queryDTO);

    /**
     * 查询对账单-运单明细信息
     * @param waybillItemId
     * @return
     */
    CheckWaybillInfoDTO queryBillCheckWaybillInfo(String waybillItemId);

    /**
     * 根据运单号查询已完成运单详情（信息上报调用）
     */
    List<CarryWaybillSubmitDTO> selectCarryWaybill(String waybillNum);


    int updateShipBillItemByWaybillItemId(@Param("waybillItemId") String waybillItemId, @Param("newestPrice") BigDecimal newestPrice);

    /**
     * 查询运单子项的运单ID集合
     * @param waybillItemIdList
     * @return
     */
    List<String> queryWaybillIdByItemIdList(@Param("waybillItemIdList") List<String> waybillItemIdList);

    /**
     * 通过运单号修改运单子项
     * @param waybillNum
     * @param newStatus
     * @param oldStatusList
     * @param updateUser
     * @return
     */
    int updateWaybillItemStatusByNum(@Param("waybillNum") String waybillNum,
                                @Param("newStatus") String newStatus,
                                @Param("oldStatusList") List<String> oldStatusList,
                                @Param("updateUser") String updateUser);


    ShipBillItem queryWaybillItemByNum(@Param("waybillNum") String waybillNum);


    WaybillDO selectSellerIdByWaybillNum(@Param("waybillNum") String waybillNum);

    /**
     * 运单子项基于发货单调价
     * @param takeCode
     * @param newestPrice
     * @return
     */
    int updatePriceByTakeCode(@Param("takeCode") String takeCode, @Param("newestPrice") BigDecimal newestPrice);

    List<ShipBillItem> getWaybillIdListByOrderCode(@Param("orderCode") String orderCode);
}
