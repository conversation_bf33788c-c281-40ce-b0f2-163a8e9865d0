package com.ecommerce.logistics.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.api.dto.*;
import com.ecommerce.logistics.api.dto.adjust.AdjustMemberDTO;
import com.ecommerce.logistics.api.dto.adjust.AdjustQueryDTO;
import com.ecommerce.logistics.api.dto.adjust.AdjustShipBillDTO;
import com.ecommerce.logistics.api.dto.pickingbill.PickingBillDTO;
import com.ecommerce.logistics.api.dto.productinfo.ProductDetailDTO;
import com.ecommerce.logistics.api.dto.shipbill.*;
import com.ecommerce.logistics.api.dto.waybill.*;
import com.ecommerce.logistics.biz.message.dto.WaybillCreatedSMSDTO;
import com.ecommerce.logistics.dao.dto.pickingbill.WaybillDO;
import com.ecommerce.logistics.dao.dto.shipbill.ShipBillMergeInfo;
import com.ecommerce.logistics.dao.dto.shipbill.ShipBillRecInfo;
import com.ecommerce.logistics.dao.dto.waybill.*;
import com.ecommerce.logistics.dao.vo.ShipBill;
import com.ecommerce.logistics.push.vo.SeckillWaybill;

import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public interface ShipBillMapper extends IBaseMapper<ShipBill> {

    /**
     * 待配送->配送中,更新运单的实际出厂量
     * @param waybillId
     * @param actualQuantity
     * @param updateUser
     * @return
     */
    int updateActualQuantity(@Param("waybillId") String waybillId,
                             @Param("actualQuantity") BigDecimal actualQuantity,
                             @Param("updateUser") String updateUser);

    /**
     * Id修改运单主状态
     * @param waybillId
     * @param newStatus
     * @param oldStatus
     * @param updateUser
     * @return
     */
    int updateShipBillStatusById(@Param("waybillId") String waybillId,
                                 @Param("newStatus") String newStatus,
                                 @Param("oldStatus") String oldStatus,
                                 @Param("updateUser") String updateUser);

    /**
     * 运单号查询运单
     * @param waybillNum
     * @return
     */
    ShipBill selectByWaybillNum(@Param("waybillNum") String waybillNum);

    /**
     * 正常操作(出站/开仓)导致的货权转移
     * @param waybillItemId
     * @param updateUser
     * @return
     */
    int normalChangeOwnerId(@Param("waybillItemId") String waybillItemId, @Param("updateUser") String updateUser);

    List<ShipBillListDTO> queryShipBillList(ShipBillQueryDTO queryDTO);

    /**
     * 完成运单
     * @param waybillId
     * @param status
     * @param completeQuantity
     * @param actualKm
     * @param completeTime
     * @param updateUser
     * @return
     */
    int completeShipBill(@Param("waybillId") String waybillId,
                         @Param("status") String status,
                         @Param("completeQuantity") BigDecimal completeQuantity,
                         @Param("actualKm") BigDecimal actualKm,
                         @Param("completeTime") Date completeTime,
                         @Param("updateUser") String updateUser);

    /**
     * 根据委托单Id查询所有运单信息
     * @param deliveryBillIds
     * @return
     */
    List<DeliveryDetailVehicleDTO> queryAllShipBillByDeliveryBillId(@Param("deliveryBillIds") List<String> deliveryBillIds);

    /**
     * 根据委托单Id查询该委托单自己承运的运单
     * @param deliveryBillId
     * @return
     */
    List<DeliveryDetailVehicleDTO> queryShipBillByDeliveryBillId(@Param("deliveryBillId") String deliveryBillId);

    /**
     * 查询委托单下不可以关闭的运单数量
     * @param deliveryBillId
     * @return
     */
    Integer queryForbidCloseWaybillCount(@Param("deliveryBillId") String deliveryBillId);

    /**
     * 查询发货单下不可以关闭的运单数量
     * @param forBidCloseWaybillCountQueryDTO
     * @return
     */
    Integer queryForbidCloseWaybillCountByTakeCode(ForBidCloseWaybillCountQueryDTO forBidCloseWaybillCountQueryDTO);

    List<ShipBill> queryForbidCloseWaybillListByTakeCode(ForBidCloseWaybillCountQueryDTO forBidCloseWaybillCountQueryDTO);

    /**
     * 发货单号查询处于指定状态的运单列表
     * @param takeCodeList
     * @param statusList
     * @return
     */
    List<ShipBill> selectByTakeCodeAndStatus(@Param("takeCodeList") List<String> takeCodeList, @Param("statusList") List<String> statusList);

    /**
     * 关闭超时运单
     * @param shipBillList
     */
    void closeExpireWaybillList(@Param("shipBillList") List<String> shipBillList);

    ShipBillDetailDTO selectWaybillDetailsByWaybillItemId(String waybillItemId);

    List<ProductDetailDTO> selectProductDetailByWaybillItemId(String waybillItemId);

    /**
     * 通过运单Id查询商品信息(包含整合运单的其他商品信息-ERP接口调用)
     * @param waybillId
     * @return
     */
    List<ProductDetailDTO> selectProductDetailByWaybillId(@Param("waybillId") String waybillId);

    /**
     * 通过运单Id查询商品信息(包含整合运单的其他商品信息-页面展示详情时调用)
     * @param waybillId
     * @return
     */
    List<ProductDetailDTO> selectProductDetailByWaybillIdForApp(@Param("waybillId") String waybillId);

    /**
     * 运单Item序列号加一
     * @param waybillId
     * @param updateUser
     * @return
     */
    int increaseMaxSeq(@Param("waybillId") String waybillId, @Param("updateUser") String updateUser);

    /**
     * 改航修改运单信息
     * @param waybillId
     * @param unloadPortId
     * @param unloadPortName
     * @param updateUser
     * @return
     */
    int updateForReroute(@Param("waybillId") String waybillId,
                         @Param("unloadPortId") String unloadPortId,
                         @Param("unloadPortName") String unloadPortName,
                         @Param("internal") String internal,
                         @Param("updateUser") String updateUser);

    /**
     * 根据发货单号查询运单信息
     * @param takeCodeList
     * @return
     */
    List<ShipBillOrderDetailDTO> queryWaybillListByTakeCode(@Param("takeCodeList") List<String> takeCodeList);

    List<TradeWaybillDTO> queryTradeWaybillList(@Param("deliverySheetNum") String deliverySheetNum, @Param("statusList") List<String> statusList);

    ShipBillDetailDTO selectShipBillDetailByWaybillId(String waybillId);

    /**
     * 获取运单详情-司机APP调用
     * @param waybillId
     * @return
     */
    ShipBillDetailDTO selectShipBillDetailForDriverApp(String waybillId);

    /**
     * 取消运单
     * @param waybillId
     * @param newStatus
     * @param oldStatus
     * @param updateUser
     * @return
     */
    int cancelShipBill(@Param("waybillId") String waybillId,
                       @Param("newStatus") String newStatus,
                       @Param("oldStatus") String oldStatus,
                       @Param("updateUser") String updateUser);

    /**
     * 作废运单
     * @param waybillId
     * @param newStatus
     * @param oldStatus
     * @param closeReason
     * @param closeTime
     * @param externalWaybillStatus
     * @param updateUser
     * @return
     */
    int discardShipBill(@Param("waybillId") String waybillId,
                       @Param("newStatus") String newStatus,
                       @Param("oldStatus") String oldStatus,
                       @Param("closeReason") String closeReason,
                       @Param("closeTime") Date closeTime,
                       @Param("externalWaybillStatus") String externalWaybillStatus,
                       @Param("updateUser") String updateUser);

    List<AppWaybillQueryConditionsDTO> selectShipBillQueryConditions(ShipBillQueryDTO queryDTO);

    TradeWaybillDetailDO queryTradeWaybillDetail(String waybillId);

    /**
     * 审核通过
     * @param passCheckDTO
     * @return
     */
    int updateShipBillByPassCheckDTO(PassCheckDTO passCheckDTO);

    WaybillDO selectCityAndQuantityByWaybillId(@Param("waybillId")String waybillId);

    int batchPassCheckMainWaybill(@Param("waybillIdList") List<String> waybillIdList, @Param("updateUser") String updateUser);

    /**
     * 查询出运单抢单
     * @param waybillIdList
     * @return
     */
    List<SeckillWaybill> selectSeckillByParentIdList(@Param("waybillIdList") List<String> waybillIdList);

    /**
     * 待审核运单列表查询
     * @param queryDTO
     * @return
     */
    List<WaitCheckWaybillListDTO> queryCheckWaybillList(CheckWaybillListQueryDTO queryDTO);

    /**
     * 平台待处理汽运运单
     * @param queryDTO
     * @return
     */
    List<WarehouseAdminWaitDeliveryDTO> queryWarehouseAdminWaitDelivery(WarehouseAdminWaitDeliveryQueryDTO queryDTO);

    /**
     * 平台已处理汽运运单
     * @param queryDTO
     * @return
     */
    List<WarehouseAdminProccessedDTO> queryWarehouseAdminProccessed(WarehouseAdminProccessedQueryDTO queryDTO);

    /**
     * 进厂
     * @param waybillId
     * @param enterFactoryTime
     * @param updateUser
     * @return
     */
    int enterFactory(@Param("waybillId") String waybillId,
                     @Param("enterFactoryTime") Date enterFactoryTime,
                     @Param("updateUser") String updateUser);

    /**
     * 撤销进厂
     * @param waybillId
     * @param updateUser
     * @return
     */
    int withdrawEnterFactory(@Param("waybillId") String waybillId,
                             @Param("updateUser") String updateUser);


    /**
     * 按照运单提货类型计数
     * @param waybillId
     * @return
     */
    int countByDeliveryInfoType(@Param("waybillId") String waybillId, @Param("pickingBillType") String pickingBillType);

    /**
     * 更新erp运单信息
     * @param updateExternalWaybillDO erp运单更新对象
     */
    void updateExternalWaybillInfo(UpdateExternalWaybillDO updateExternalWaybillDO);

    /**
     * 根据父运单ID查询子运单信息
     * @param parentIdList
     * @return
     */
    List<SubWaybillDO> selectSubWaybillListByParentId(@Param("list")List<String> parentIdList,
                                                      @Param("statusList")List<String> statusList);

    /**
     * 根据真实委托单号查询发送短信需要的信息
     * @param realDeliveryBilNum
     * @return
     */
    List<WaybillCreatedSMSDTO> selectWaybillForSMS(@Param("realDeliveryBilNum") String realDeliveryBilNum);
    /**
     * 指派运单到承运商
     * @param waybillAssignDTO
     * @return
     */
    int updateCarrierIdByWaybillId(WaybillAssignDTO waybillAssignDTO);

    /**
     * 修改子运单,指派到承运商
     * @param waybillAssignDTO
     * @return
     */
    int updateCarrierIdByParentId(WaybillAssignDTO waybillAssignDTO);

    /**
     * 更新运单的指派信息
     * @param driverWaybillAssignDTO
     * @return
     */
    int updateAssignToDriverByWaybillId(DriverWaybillAssignDTO driverWaybillAssignDTO);

    /**
     * 更新运单的指派信息
     * @param driverWaybillAssignDTO
     * @return
     */
    int updateAssignToDriverByParentId(DriverWaybillAssignDTO driverWaybillAssignDTO);

    /**
     * 司机确认运单
     * @param confirmWaybillDO 确认对象
     */
    int confirmWaybillByDriver(ConfirmWaybillDO confirmWaybillDO);

    /**
     * 批量更新子运单状态
     * @param parentWaybillId 主运单Id
     */
    void updateSubWaybillStatusByParentId(@Param("status") String status,
                                          @Param("parentId") String parentWaybillId,
                                          @Param("currentStatus") String currentStatus);


    /**
     * 查询需要同步到erp创建的运单ID
     * @param leafDeliveryBillId
     * @return
     */
    List<String> selectWaybillIdForCreateErp(@Param("leafDeliveryBillId") String leafDeliveryBillId);

    /**
     * 获取用户抢单仓库ID列表
     * @param userId
     * @param status
     * @return
     */
    List<String> selectSeckillWarehouseIdByUserId(@Param("userId")String userId, @Param("status")String status);

    /**
     * 获取用户可抢单区域列表
     * @param userId
     * @param status
     * @return
     */
    List<DistrictListDTO> selectSeckillDistrictsByUserId(@Param("userId")String userId, @Param("status")String status);


    /**
     * 查询用户可抢单ID列表
     * @param dto
     * @return
     */
    List<String> selectSeckillWaybillIdsByCon(UserSeckillWaybillQueryDTO dto);

    /**
     * 查询用户已抢到的运单Id列表
     * @param dto
     * @return
     */
    List<String> selectSeckilledWaybillIdsByCon(DriverWaybillListQueryDTO dto);

    /**
     * 通过父运单Id查询运单信息
     * @param waybillIds
     * @return
     */
    List<ShipBillDetailDTO> selectSeckillWaybillByParentIds(@Param("waybillIds")List<String> waybillIds);

    /**
     * 查询用户指定日期未完成的运单数量
     * @param dto
     * @return
     */
    int selectWaybillSnatchCountByCon(SnatchCountQueryDO dto);

    /**
     * 抢单
     * @param snatchWaybillDO
     * @return
     */
    int snatchShipBill(SnatchWaybillDO snatchWaybillDO);

    /**
     * 更新主运单抢单信息
     * @param snatchWaybillDO
     * @return
     */
    int updateSnatchInfoByParentId(SnatchWaybillDO snatchWaybillDO);

    /**
     *
     * @param waybillId
     * @return
     */
    List<ShipBillRecInfo> selectRecInfoByWaybillId(@Param("waybillId") String waybillId);

    /**
     * 更新送达时间
     * @param waybillId
     */
    void updateArriveDestinationTime(@Param("waybillId") String waybillId,@Param("date") Date date);

    /**
     * 更新进站时间
     * @param waybillId
     * @param arriveWarehouseTime
     * @return
     */
    int updateArriveWarehouseTimeById(@Param("waybillId")String waybillId, @Param("arriveWarehouseTime") Date arriveWarehouseTime);

    /**
     * 统计司机在途运单数量
     * @param halfwayWaybillDO 在途运单查询对象
     * @return Integer 在途运单数量
     */
    Integer countDriverHalfwayWaybill(HalfwayWaybillDO halfwayWaybillDO);

    /**
     * 查询司机在途的车辆列表
     * @param driverId
     * @return
     */
    List<String> queryHalfWayVehicleNum(@Param("driverId") String driverId);

    /**
     * 修改子运单的整合信息
     * @param childWaybillIdList
     * @param parentId
     * @return
     */
    int updateChildMergeInfo(@Param("childWaybillIdList") List<String> childWaybillIdList, @Param("parentId") String parentId);

    /**
     * 查询船运主运单ID列表
     * @param shipWaybillQueryDTO 查询对象
     * @return List<String>
     */
    List<String> queryShipWaybillParentIdList(ShipWaybillQueryDTO shipWaybillQueryDTO);

    /**
     * 查询船运主运单明细列表
     * @param shipWaybillQueryDTO 查询对象
     * @return List<String>
     */
    List<ShipDeliveryItemDTO> queryShipWaybillItemList(ShipWaybillQueryDTO shipWaybillQueryDTO);

    /**
     * 运单id列表查询
     * @param waybillIds
     * @return
     */
    List<ShipBillMergeInfo> selectWaitMergeByWaybillIds(@Param("waybillIds") List<String> waybillIds);

    /**
     * 计数子运单不是该状态的数量
     * @param waybillId
     * @param parentId
     * @param status
     * @return
     */
    int countSubNotStatus(@Param("waybillId") String waybillId,@Param("parentId") String parentId,@Param("status") String status);

    /**
     * 急速子运单没有终结的数量
     * @param parentId
     * @return
     */
    int countSubNotFinal(@Param("parentId") String parentId);

    /**
     * 根据发货单号列表和运单状态列表查询船运主运单ID列表
     * @param deliverySheetNumList 发货单号列表
     * @param shipBillStatusList   运单状态列表
     * @return List<String>
     */
    List<String> queryShipWaybillParentIdListByTakeCodesAndStatusList(@Param("deliverySheetNumList") List<String> deliverySheetNumList,
                                                                      @Param("shipBillStatusList") List<String> shipBillStatusList);

    /**
     * 查询主运单对象列表
     * @param parentIdList 主运单ID列表
     * @return List<MainWaybillDO>
     */
    List<MainWaybillDO> selectMainWaybillListByParentId(@Param("parentIdList") List<String> parentIdList);
    /**
     * 查询新结构的提货信息(无提货单和数量)
     * @param waybillNumList
     * @return
     */
    List<PickingBillDTO> queryPickingListByWaybillNum(@Param("waybillNumList") List<String> waybillNumList);

    /**
     * 查询运单的整合运单父运单标识
     * @param waybillId
     * @return
     */
    Byte queryParentFlagById(@Param("waybillId") String waybillId);

    /**
     * 查询整合运单的子运单列表
     * @param parentId
     * @return
     */
    List<ShipBill> queryNoFinalSubShipBillListByParentId(@Param("parentId") String parentId);

    /**
     * 更新运达的出战时间
     * @param waybillId
     * @param leaveWarehouseTime
     * @return
     */
    int updateLeaveWarehouseTimeByWaybillId(@Param("waybillId") String waybillId, @Param("leaveWarehouseTime") Date leaveWarehouseTime);

    /**
     * 查询已抢运单列表--只查询两条数据
     * @return List<SnatchedWaybillDO>
     */
    List<SnatchedWaybillDTO> selectSnatchedWaybillList();

    /**
     * 根据运单状态和运单类型查询指定条数的发布运单信息
     * @param emallPublishWaybillQueryDTO
     * @return
     */
    List<EmallPublishWaybillListDTO> selectWaybillForEmall(EmallPublishWaybillQueryDTO emallPublishWaybillQueryDTO);

    /**
     * 查询轨迹回放的数据
     * @param waybillNum
     * @return
     */
    PublishWaybillListDTO selectTrackWaybillForNum(@Param("waybillNum") String waybillNum);

    /**
     * 查询运单的混凝土信息
     * @param waybillId
     * @return
     */
    ConcreteShipBillInfoDTO queryConcreteInfoByWaybillId(@Param("waybillId") String waybillId);

    /**
     * 签收混凝土
     * @param completeShipBillDTO
     * @return
     */
    int signConcrete(CompleteShipBillDTO completeShipBillDTO);

    /**
     * 更新运单外部状态
     * @param waybillId
     * @param externalWaybillStatus
     * @return
     */
    int updateExternalStatus(@Param("waybillId") String waybillId, @Param("externalWaybillStatus") String externalWaybillStatus);

    /**
     * 配送信息下混凝土的签收量之和
     * @param deliveryInfoId
     * @return
     */
    BigDecimal concreteTotalSignQuantity(@Param("deliveryInfoId") String deliveryInfoId);

    /**
     * 订单子项查询签收量之和
     * @param orderItemId
     * @return
     */
    BigDecimal concreteTotalSignQuantityByOrderItemId(@Param("orderItemId") String orderItemId);

    /**
     * 更新运单上车辆的状态
     * @param waybillId
     * @param vehicleStatus
     * @param updateUserId
     * @return
     */
    int updateVehicleStatus(@Param("waybillId") String waybillId,
                            @Param("vehicleStatus") String vehicleStatus,
                            @Param("updateUserId") String updateUserId);

    /**
     * 计算车辆在途运单数
     * @param vehicleId
     * @return
     */
    int countDeliveringWaybill(@Param("vehicleId") String vehicleId);

    /**
     * 查询所有抢单的社会承运商
     * @return
     */
    List<String> queryAllSnatchCarrierId();


    ShipBill queryExternalWaybillStatus(@Param("waybillId") String waybillId);

    public List<AdjustMemberDTO> getAdjustPriceMemberList(AdjustQueryDTO adjustQueryDTO);

    public List<AdjustShipBillDTO> getAdjustPriceShipBillList(AdjustQueryDTO adjustQueryDTO);

    /**
     * 查询车辆和指定运单状态集合内的运单号
     * @param vehicleId
     * @param statusList
     * @return
     */
    List<String> queryWaybillNumByVehicleIdAndStatus(@Param("vehicleId") String vehicleId,
                                                     @Param("statusList") List<String> statusList);

    /**
     * 无车承运人 -- 实际运输合同上报信息查询
     * @param waybillNum
     * @return
     */
    ContractSubmitInfoDTO queryContractInfo(@Param("waybillNum") String waybillNum);

    /**
     * 更新润管砂浆
     * @param waybillId
     * @param lubricityQuantity
     * @param lubricityPrice
     * @return
     */
    int updateLubricity(@Param("waybillId") String waybillId,
                        @Param("lubricityQuantity") BigDecimal lubricityQuantity,
                        @Param("lubricityPrice") BigDecimal lubricityPrice);


    /**
     * 更新为退货状态
     * @return
     */
    int refundShipBill(RefundShipBillDTO refundShipBillDTO);

    /**
     * 通过运单号和外部运单号获取运单信息
     * @param waybillNum
     * @return
     */
    ShipBill queryShipBillInfoByNum(@Param("waybillNum") String waybillNum, @Param("externalWaybillNum") String externalWaybillNum);

    /**
     * 混凝土数据量变化时修改
     * @param waybillId
     * @param quantity
     * @param updateUser
     * @return
     */
    int updateQuantityForConcrete(@Param("waybillId") String waybillId,
                                  @Param("quantity") BigDecimal quantity,
                                  @Param("updateUser") String updateUser);

    /**
     * 查询订单下,最新签收的运单信息(混凝土签收之后查询最后一单用)
     * @param lastSignWaybillQueryDTO
     * @return
     */
    WaybillBriefDTO queryLastSignWaybill(LastSignWaybillQueryDTO lastSignWaybillQueryDTO);

    /**
     * 获取商品种类下拉列表
     * @param queryGoodsDTO
     * @return
     */
    List<GoodsInfoDTO> queryGoodsNameListDropDownBox(QueryGoodsDTO queryGoodsDTO);

	List<String> queryVehicleOrShippingListByName(ShipBillQueryDTO queryDTO);
	
	List<LogisticsAdjustPriceItemDTO> queryShipBillByDeliveryBillIds(ShipBillQueryDTO shipBillQueryDTO);

    /**
     * 订单下待发货的二维码信息查询
     * @param orderCode
     * @return
     */
    List<QrCodeDTO> queryWDQrCodeByOrderCode(@Param("orderCode") String orderCode);

    int updateCarrierInfo(@Param("waybillId") String waybillId, @Param("carrierId") String carrierId,
                          @Param("carrierName") String carrierName, @Param("internal") String internal);

    /**
     * 交易视角,运单简要信息查询
     * @param shipBillBriCondDTO
     * @return
     */
    ShipBillBriResDTO queryTradeWaybillBri(ShipBillBriCondDTO shipBillBriCondDTO);

    /**
     * 承运视角,运单简要信息查询
     * @param shipBillBriCondDTO
     * @return
     */
    ShipBillBriResDTO queryCarryWaybillBri(ShipBillBriCondDTO shipBillBriCondDTO);

    /**
     * 统计运单数
     * @param shipBillQueryDTO
     * @return
     */
    int countShipBillByDeliveryBillIds(ShipBillQueryDTO shipBillQueryDTO);
}

