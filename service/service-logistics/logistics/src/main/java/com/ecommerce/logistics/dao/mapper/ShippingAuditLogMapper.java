package com.ecommerce.logistics.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.api.dto.shipping.ShippingAuditLogDTO;
import com.ecommerce.logistics.dao.vo.ShippingAuditLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ShippingAuditLogMapper extends IBaseMapper<ShippingAuditLog> {

    /**
     * 根据船舶ID查询审核日志
     * @param ShippingId
     * @return
     */
    List<ShippingAuditLogDTO> selectAuditLogByShippingId(@Param("shippingId") String ShippingId);


}