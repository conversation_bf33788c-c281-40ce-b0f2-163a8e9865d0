package com.ecommerce.logistics.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.api.dto.shippingroute.ShippingUnitFareDTO;
import com.ecommerce.logistics.dao.vo.ShippingFarePayment;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ShippingFarePaymentMapper extends IBaseMapper<ShippingFarePayment> {

    void insertBatch(List<ShippingFarePayment> list);

    void updateShippingFarePayment(ShippingFarePayment farePayment);

    /**
     * 修改辅助价目表
     * @param farePayment
     */
    void updateAuxiliaryPriceNo(ShippingFarePayment farePayment);

    /**
     * 获取单位运价信息
     * @param shippingRouteId
     */
    List<ShippingUnitFareDTO> getShippingFarePayment(@Param("shippingRouteId") String shippingRouteId);
}