package com.ecommerce.logistics.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.api.dto.shipping.*;
import com.ecommerce.logistics.api.dto.shipping.erp.BindErpShippingDTO;
import com.ecommerce.logistics.dao.vo.ShippingInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface ShippingInfoMapper extends IBaseMapper<ShippingInfo> {

    /**
     * 平台查看船舶列表
     * @param dto
     * @return
     */
    List<ShippingInfoListDTO> queryShippingInfoListByPlatform(ShippingInfoListQueryDTO dto);

    /**
     * 查询当前用户名下船舶列表
     * @param dto
     * @return
     */
    List<ShippingInfoListDTO> queryShippingInfoListByCarrier(ShippingInfoListQueryDTO dto);

    /**
     * 买家，卖家查询我名下和授权给我的船舶列表
     * @param dto
     * @return
     */
    List<ShippingInfoListDTO> queryShippingInfoListBySeller(ShippingInfoListQueryDTO dto);
    /**
     * 查询我名下和授权给我的可指派船舶列表
     * @param dto
     * @return
     */
    List<ShippingInfoDTO> queryShippingInfoListBySelf(ShippingInfoQueryDTO dto);

    /**
     * 批量修改船舶状态
     * @param map
     * @return
     */
    int batchUpdateShippingStatus(Map<String, Object> map);

    /**
     * 批量检修船舶
     * @param map
     * @return
     */
    int batchDelShippingInfo(Map<String, Object> map);

    /**
     * 获取船务公司集合
     * @return
     */
    List<String> getShippingCompanyList(@Param("shippingCompany") String shippingCompany);

    /**
     * 买家，卖家查询我名下和授权给我的绑定了ERP的船舶列表
     * @param dto
     * @return
     */
    List<ShippingInfoListDTO> queryERPShippingInfoListBySeller(ShippingInfoListQueryDTO dto);

    /**
     * 主键查询船舶的实际归属人
     * @param shippingId
     * @return
     */
    ShippingInfoDTO findManagerMemberIdByPK(@Param("shippingId") String shippingId);

    /**
     * 获取ERP船舶绑定列表
     * @param shippingId
     * @return
     */
    List<BindErpShippingDTO> queryErpShippingBindList(@Param("shippingId") String shippingId);

	/**
	 * 模糊查询船名
	 * @param queryDTO
	 * @return
	 */
	List<String> selectShippingInfoName(ShippingInfoListQueryDTO queryDTO);

    List<ShippingInfoListDTO> queryAllocationShippingInfo(@Param("memberId")String memberId, @Param("carrierId")String carrierId, @Param("shippingName")String shippingName);

}