package com.ecommerce.logistics.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.api.dto.operationrecord.shipping.ShippingOperationLogDTO;
import com.ecommerce.logistics.dao.vo.ShippingOperationLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ShippingOperationLogMapper extends IBaseMapper<ShippingOperationLog> {

    /**
     * 查询最近半年的操作记录
     * @param entryId
     * @return
     */
    List<ShippingOperationLogDTO> selectOperationLogListDTO(@Param("entryId")String entryId);


}