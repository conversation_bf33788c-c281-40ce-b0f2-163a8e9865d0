package com.ecommerce.logistics.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.api.dto.shippingroute.ShippingRouteListDTO;
import com.ecommerce.logistics.api.dto.shippingroute.ShippingRouteQueryDTO;
import com.ecommerce.logistics.api.dto.shippingrule.ShippingRuleDTO;
import com.ecommerce.logistics.api.dto.shippingrule.ShippingUnitDTO;
import com.ecommerce.logistics.dao.vo.ShippingRouteInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface ShippingRouteInfoMapper extends IBaseMapper<ShippingRouteInfo> {

    /**
     * 查询航线运费定价列表
     * @param routeQueryDTO
     * @return
     */
    List<ShippingRouteListDTO> queryShippingRouteList(ShippingRouteQueryDTO routeQueryDTO);

    /**
     * 起运港、目的港、运输品类不能重复设置
     * @param shippingRouteInfo
     * @return
     */
    String checkData(ShippingRouteInfo shippingRouteInfo);

    Map<String, String> queryUnloadingWharf(@Param("belongerId")String belongerId);

    List<ShippingUnitDTO> getShippingRoute(ShippingRuleDTO shippingRuleDTO);

}