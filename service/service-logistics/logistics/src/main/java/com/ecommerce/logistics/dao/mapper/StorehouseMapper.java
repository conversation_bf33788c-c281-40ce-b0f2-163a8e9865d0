package com.ecommerce.logistics.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.api.dto.storehouse.StorehouseListDTO;
import com.ecommerce.logistics.api.dto.storehouse.StorehouseQueryDTO;
import com.ecommerce.logistics.api.dto.storehouse.WarehouseStorageListDTO;
import com.ecommerce.logistics.api.dto.storehouse.WarehouseStorageQueryDTO;
import com.ecommerce.logistics.dao.dto.storehouse.StockChangeBean;
import com.ecommerce.logistics.dao.vo.Storehouse;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface StorehouseMapper extends IBaseMapper<Storehouse> {

    List<StorehouseListDTO> fetchStockList(StorehouseQueryDTO storehouseQueryDTO);

    List<String> queryPlatformWarehouseIdForSeller(WarehouseStorageQueryDTO warehouseStorageQueryDTO);

    List<WarehouseStorageListDTO> queryPlatformStoreForSeller(@Param("userId") String userId, @Param("warehouseIdList") List<String> warehouseIdList);

    int updateStoreStockChange(StockChangeBean stockChangeBean);

}