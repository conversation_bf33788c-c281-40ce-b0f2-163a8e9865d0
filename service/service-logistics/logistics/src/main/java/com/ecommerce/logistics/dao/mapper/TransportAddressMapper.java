package com.ecommerce.logistics.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.api.dto.transportaddress.TransportAddressQueryDTO;
import com.ecommerce.logistics.dao.vo.TransportAddress;

import java.util.List;

public interface TransportAddressMapper extends IBaseMapper<TransportAddress> {

    /**
     * 查询运输需求列表
     * @param transportAddressQueryDTO 运输需求查询对象
     * @return List<TransportAddress>
     */
    List<TransportAddress> queryTransportAddressList(TransportAddressQueryDTO transportAddressQueryDTO);
}