package com.ecommerce.logistics.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.api.dto.transportcapacity.TransportCapacityQueryDTO;
import com.ecommerce.logistics.dao.dto.transport.TransportCapacityListDO;
import com.ecommerce.logistics.dao.vo.TransportCapacity;

import java.util.List;

public interface TransportCapacityMapper extends IBaseMapper<TransportCapacity> {

    /**
     * 查询运力列表
     * @param transportCapacityQueryDTO 运力列表查询对象
     * @return List<TransportCapacity>
     */
    List<TransportCapacityListDO> queryTransportCapacityList(TransportCapacityQueryDTO transportCapacityQueryDTO);

    /**
     * 查询运力浏览记录
     * @param transportCapacityIdList 运力ID列表
     * @return List<TransportCapacityListDO>
     */
    List<TransportCapacityListDO> queryTransportCapacityViewRecord(List<String> transportCapacityIdList);

    /**
     * 查询运力ID列表
     * @param transportCapacityQueryDTO 运力列表查询对象
     * @return List<String>
     */
    List<String> queryTransportCapacityIdList(TransportCapacityQueryDTO transportCapacityQueryDTO);
}