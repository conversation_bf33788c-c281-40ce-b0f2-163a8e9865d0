package com.ecommerce.logistics.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.api.dto.transportcategory.*;
import com.ecommerce.logistics.dao.dto.transport.WaybillIdCategoryMappingDO;
import com.ecommerce.logistics.dao.vo.TransportCategory;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TransportCategoryMapper extends IBaseMapper<TransportCategory> {

    List<TransportCategoryListDTO> selectTransportCategoryList(TransportCategoryListQueryDTO transportCategoryListQueryDTO);

    void updateTransportCategory(TransportCategoryEditDTO transportCategoryEditDTO);

    List<TransportOption> queryOptions();

    List<CategoryNameDTO> queryCategoryNameByIds(@Param("ids")List<String> ids);

    String queryCategoryNameById(@Param("transportCategoryId")String transportCategoryId);

    List<WaybillIdCategoryMappingDO> queryTransportCategoryByWaybillIdList(@Param("waybillIdList") List<String> waybillIdList);

    List<WaybillIdCategoryMappingDO> queryTransportCategoryByParentIdList(@Param("parentIdList") List<String> parentIdList);
}