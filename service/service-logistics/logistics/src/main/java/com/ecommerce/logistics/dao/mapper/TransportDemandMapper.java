package com.ecommerce.logistics.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.api.dto.transportdemand.TransportDemandQueryDTO;
import com.ecommerce.logistics.dao.dto.transport.TransportDemandListDO;
import com.ecommerce.logistics.dao.vo.TransportDemand;

import java.util.List;

public interface TransportDemandMapper extends IBaseMapper<TransportDemand> {

    /**
     * 查询运输需求列表
     * @param transportDemandQueryDTO 运输需求查询对象
     * @return List<TransportDemandListDO>
     */
    List<TransportDemandListDO> queryTransportDemandList(TransportDemandQueryDTO transportDemandQueryDTO);

    /**
     * 查询运输需求浏览记录
     * @param transportDemandIdList 运输需求id列表
     * @return List<TransportDemandListDO>
     */
    List<TransportDemandListDO> queryTransportDemandViewRecord(List<String> transportDemandIdList);
}