package com.ecommerce.logistics.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.api.dto.contract.TrusteeContractListDTO;
import com.ecommerce.logistics.api.dto.contract.TrusteeContractListQueryDTO;
import com.ecommerce.logistics.dao.vo.TrusteeContractInfo;

import java.util.List;

public interface TrusteeContractInfoMapper extends IBaseMapper<TrusteeContractInfo> {

    /**
     * 查询托运合同列表
     * @param contractListQueryDTO
     * @return
     */
    List<TrusteeContractListDTO> queryTrusteeContractList(TrusteeContractListQueryDTO contractListQueryDTO);

}