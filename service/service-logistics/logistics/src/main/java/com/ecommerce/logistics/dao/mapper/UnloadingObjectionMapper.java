package com.ecommerce.logistics.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.api.dto.unloadingobjection.UnloadingObjectionListDTO;
import com.ecommerce.logistics.api.dto.unloadingobjection.UnloadingObjectionQueryDTO;
import com.ecommerce.logistics.dao.vo.UnloadingObjection;

import java.util.List;

public interface UnloadingObjectionMapper extends IBaseMapper<UnloadingObjection> {

    List<UnloadingObjectionListDTO> selectUnloadingObjectionList(UnloadingObjectionQueryDTO queryDTO);

}