package com.ecommerce.logistics.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.api.dto.intelligentDispatch.UnloadingTimeDTO;
import com.ecommerce.logistics.dao.vo.UnloadingTime;

import java.util.List;

public interface UnloadingTimeMapper extends IB<PERSON><PERSON>apper<UnloadingTime> {

    List<UnloadingTimeDTO> queryUnloadingTimeList(UnloadingTimeDTO queryDTO);
}