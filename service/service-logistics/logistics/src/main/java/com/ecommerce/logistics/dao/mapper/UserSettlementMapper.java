package com.ecommerce.logistics.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.api.dto.settlement.MonthSettlementListDTO;
import com.ecommerce.logistics.api.dto.settlement.MonthSettlementListQueryDTO;
import com.ecommerce.logistics.api.dto.settlement.SettlementListDTO;
import com.ecommerce.logistics.api.dto.settlement.SettlementListQueryDTO;
import com.ecommerce.logistics.api.dto.settlement.CarriageSettlementDTO;
import com.ecommerce.logistics.api.dto.settlement.UserSettlementAmountDTO;
import com.ecommerce.logistics.dao.dto.settlement.SettlementPeriodDO;
import com.ecommerce.logistics.dao.dto.settlement.SettlementUpdateDO;
import com.ecommerce.logistics.dao.vo.UserSettlement;

import java.util.List;

public interface UserSettlementMapper extends IBaseMapper<UserSettlement> {

    List<SettlementListDTO> querySettlementList(SettlementListQueryDTO settlementListQueryDTO);

    List<MonthSettlementListDTO> queryMonthSettlementList(MonthSettlementListQueryDTO monthSettlementListQueryDTO);

    List<SettlementPeriodDO> querySettlementListPeriod(List<String> carrierIdList);

    void userCarriageSettlement(CarriageSettlementDTO carriageSettlementDTO);

    String selectSettlementForLock(String userSettlementId);

    void updateSettlementForLock(SettlementUpdateDO settlementUpdateDO);

    /**
     * 查询用户结算金额
     * @param userId 用户ID
     */
    UserSettlementAmountDTO queryUserSettlementAmount(String userId);
}