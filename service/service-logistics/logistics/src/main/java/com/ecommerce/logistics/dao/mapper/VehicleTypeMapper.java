package com.ecommerce.logistics.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.api.dto.vehicletype.*;
import com.ecommerce.logistics.dao.vo.VehicleType;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

public interface VehicleTypeMapper extends IBaseMapper<VehicleType> {
    List<VehicleTypeListDTO> selectVehicleTypeList(VehicleTypeListQueryDTO vehicleTypeListQueryDTO);

    void updateVehicleType(VehicleTypeEditDTO vehicleTypeEditDTO);

    List<VehicleTypeOptionDTO> queryOptions();

    List<VehicleTypeNameDTO> queryTypeNameByIds(@Param("ids")List<String> ids);

    String queryTypeNameById(@Param("vehicleTypeId") String id);

    /**
     * 查询是否存在不限车型
     * @param axles
     * @param carriageType
     * @return
     */
    int querytVehicleType(@Param("axles") String axles, @Param("carriageType") String carriageType);


    /**
     * 查询出不限车型
     * @param axles
     * @param carriageType
     * @return
     */
    VehicleTypeNameDTO querytUnlimiteType(@Param("axles") String axles, @Param("carriageType") String carriageType);

    /**
     * 个体司机,查询保证金
     * @param userId
     * @return
     */
    BigDecimal queryDepositAmountByDriverUserId(@Param("userId") String userId);
}