package com.ecommerce.logistics.dao.mapper;

import java.util.List;

import com.ecommerce.logistics.api.dto.warehouse.*;
import org.apache.ibatis.annotations.Param;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.dao.vo.Warehouse;

public interface WarehouseMapper extends IBaseMapper<Warehouse> {
    List<WarehouseListDTO> selectWarehouseList(WarehouseListQueryDTO queryDTO);

    /**
     * 通过仓库ID查询仓库名称
     * @Auther: <EMAIL>
     * @Date: 2018年8月14日 下午7:03:05
     * @Description:
     * @param warehouseId
     * @return
     */
    //@Cacheable(value=CacheableValue.WAREHOUSE_NAME, keyGenerator="firstParamKeyGenerator")
    String selectWarehouseNameByPK(@Param("warehouseId")String warehouseId);

    /**
     * 通过仓库名称模糊查询所有匹配的仓库ID
     * @Auther: <EMAIL>
     * @Date: 2018年8月24日 下午3:20:24
     * @param warehouseName
     * @return
     */
    List<String> selectWarehouseIdLikeName(@Param("warehouseName")String warehouseName);

    /**
     *
     * 功能描述: 通过名字模糊查询下拉列表
     *
     * @param:
     * @return:
     */
    List<WarehouseOptionDTO> selectWarehouseIdAndName(WarehouseOptionQueryDTO warehouseOptionQueryDTO);

    /**
     * 通过仓库Id查询仓库
     * @param warehouseId
     * @return
     */
    WarehouseDetailsDTO queryWarehouseDetails(@Param("warehouseId") String warehouseId);

    /**
     * 通过仓库管理员ID查询仓库ID
     * @Auther: <EMAIL>
     * @Date: 2018年9月8日 下午4:02:58
     * @param administrator
     * @return
     */
    String selectWarehouseIdByAdministrator(@Param("administratorId") String administratorId);
     /** 更新仓库
     * @param warehouseEditDTO
     * @return
     */
    Integer updateWarehouse(WarehouseEditDTO warehouseEditDTO);

    Integer deleteWarehouseById(WarehouseRemoveDTO warehouseRemoveDTO);
    
    List<WarehouseOptionDTO> selectWarehouseNameAndIDByWarehouseIds(@Param("warehouseIds")List<String> warehouseIds);

    WarehouseBaseDataDTO selectWarehouseBaseData(@Param("warehouseId") String warehouseId);

    /**
     * 通过仓库Id查询仓库
     * @param warehouseId
     * @return
     */
    WarehouseDetailsDTO queryWarehouseDetail(@Param("warehouseId") String warehouseId);
    
    /**
     * 通过主运单查询仓库信息
     * @Auther: <EMAIL>
     * @Date: 2018年10月20日 下午6:40:04
     * @param mainWaybillId
     * @return
     */
    Warehouse selectWarehouseByMainWaybillId(@Param("mainWaybillId")String mainWaybillId);
    
}