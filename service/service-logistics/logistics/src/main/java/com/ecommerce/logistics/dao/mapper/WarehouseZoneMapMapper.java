package com.ecommerce.logistics.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.api.dto.rulecompute.QueryZoneDTO;
import com.ecommerce.logistics.dao.vo.WarehouseZoneMap;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface WarehouseZoneMapMapper extends IBaseMapper<WarehouseZoneMap> {

    List<WarehouseZoneMap> queryProvinceWarehouse(@Param("typeList")List<String> typeList, @Param("list")List<QueryZoneDTO> queryZoneList);

    List<WarehouseZoneMap> queryCityWarehouse(@Param("typeList")List<String> typeList, @Param("list")List<QueryZoneDTO> queryZoneList);

    List<WarehouseZoneMap> queryDistrictWarehouse(@Param("typeList")List<String> typeList, @Param("list")List<QueryZoneDTO> queryZoneList);

    Integer deleteZoneMap(String warehouseId);

    Integer insertZoneMapList(List<WarehouseZoneMap> warehouseZoneMaps);

}