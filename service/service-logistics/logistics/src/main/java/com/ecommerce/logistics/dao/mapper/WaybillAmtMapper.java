package com.ecommerce.logistics.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.dao.vo.WaybillAmt;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface WaybillAmtMapper extends IBaseMapper<WaybillAmt> {

    int updateParentIdByWaybillIdList(@Param("waybillIdList") List<String> waybillIdList,
                                      @Param("mainWaybillId") String mainWaybillId);

    WaybillAmt queryByWaybillId(@Param("waybillId") String waybillId);

}