package com.ecommerce.logistics.dao.mapper;


import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface WaybillCompositeMapper {
    /**
     * 批量更新子运单状态
     * @param waybillIdList 子运单id列表
     * @param status 更新状态
     * @param parentWaybillId 父运单ID
     * @param isMainWaybill 是否主运单
     */
    void updateSubWaybillStatus(@Param("waybillIdList")List<String> waybillIdList,
                                @Param("status")String status,
                                @Param("parentId") String parentWaybillId,
                                @Param("isMainWaybill") Integer isMainWaybill);

    /**
     * 批量更新子运单状态
     * @param parentWaybillId 主运单Id
     */
    void updateSubWaybillStatusByParentId(@Param("status") String status,
                                          @Param("parentId") String parentWaybillId,
                                          @Param("currentStatus") String currentStatus);
    
    void updateStatusByWaybillIds(@Param("status")String status, @Param("waybillIds")List<String> waybillIds,
    		@Param("userId")String userId);
    
    void updateStatusToCancelByWaybillIds(@Param("status")String status, @Param("waybillIds")List<String> waybillIds,
    		@Param("userId")String userId);
    
    void updateStatusToCompleteByWaybillIds(@Param("status")String status, @Param("waybillIds")List<String> waybillIds,
    		@Param("userId")String userId);
    
    
    void updateSubWaybillByParentId(@Param("status")String status,
            @Param("parentId") String parentWaybillId, 
            @Param("carrierId")String carrierId,
            @Param("currentStatus") String currentStatus);

    void updateWaybillCarrier(@Param("carrierId")String carrierId, @Param("parentId")String parentId);
}