package com.ecommerce.logistics.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.api.dto.waybill.SellerAssignWaybillDTO;
import com.ecommerce.logistics.dao.vo.WaybillInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface WaybillInfoMapper extends IBaseMapper<WaybillInfo> {
	
	/**
	 * 根据waybill_id设置del_flg为删除
	 * @param operatorId
	 * @param waybillId
	 */
	void updateDelFlgByWaybillIds(@Param("operatorId")String operatorId,@Param("waybillIds")List<String> waybillId);

	/**
	 * 通过运单查询运单详情
	 * @Auther: <EMAIL>
	 * @Date: 2018年9月3日 下午4:21:07
	 * @param waybillId
	 */
	WaybillInfo selectWaybillInfoByWaybillId(@Param("waybillId")String waybillId);

	int updateVehicleNumByWaybillId(@Param("operatorId")String operatorId,@Param("dto")SellerAssignWaybillDTO dto);
}