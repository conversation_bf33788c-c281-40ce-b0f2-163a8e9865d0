package com.ecommerce.logistics.dao.mapper;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.ecommerce.logistics.api.dto.waybill.CancelReasonDTO;
import com.ecommerce.logistics.api.dto.waybill.CancelWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.ChangeCarriageDTO;
import com.ecommerce.logistics.api.dto.waybill.PassCheckDTO;
import com.ecommerce.logistics.api.dto.waybill.ProductQuantityDTO;
import com.ecommerce.logistics.api.dto.waybill.SubWaybillMonitorStatusNotifyDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillAssignDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillMonitorStatusNotifyDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillRemoveDTO;
import com.ecommerce.logistics.dao.dto.waybill.ConfirmWaybillDO;
import com.ecommerce.logistics.dao.dto.waybill.SnatchWaybillDO;
import com.ecommerce.logistics.dao.dto.waybill.UpdateStatusDTO;

public interface WaybillOperationMapper {
    void updateCarriage(ChangeCarriageDTO changeCarriageDTO);
	/**
     * 根据dispatch_bill_id更新删除标志位
     * @Auther: <EMAIL>
     * @Description:
     * @param dispatchBillId
     * @param operatorId
     */
    void updateDelFlgByDispatchBillId(@Param("dispatchBillId")String dispatchBillId,
    		 @Param("operatorId")String operatorId);

    void updateStatusToWaitReceiveById(String waybillId);

    int updateCarrierIdById(WaybillAssignDTO waybillAssignDTO);

    int updateArriveWarehouseTimeById(@Param("waybillId")String waybillId, @Param("arriveWarehouseTime") Date arriveWarehouseTime);

    int updateEnterFactoryTimeById(@Param("waybillId")String waybillId, @Param("enterFactoryTime") Date enterFactoryTime);

    int updateActualQuantity(@Param("actualQuantity")BigDecimal actualQuantity,
                              @Param("waybillId")String waybillId,@Param("status")String status);

    void updateStatusToCanceledById(@Param("waybillId") String waybillId);

    void updateWaybillCancelReason(CancelReasonDTO cancelReasonDTO);

    /**
     * 暂时不做，属于抢单消息推送
     * 查询配送时间
     * @param waybillId
     * @return
     */
    Date selectDeliveryTime(@Param("waybillId")String waybillId);

    /**
     * 暂时不做，属于抢单消息推送
     * 查询商品种类
     * @param productInfoId
     * @return
     */
    String selectProductCategory(@Param("productInfoId") String productInfoId);

    /**
     * 暂时不做，属于抢单消息推送
     * 查询商品数量
     * @param waybillId
     * @return
     */
    ProductQuantityDTO selectProductQuantity(@Param("waybillId")String waybillId);

    /**
     * 更新送达时间
     * @param waybillId
     */
    void updateArriveDestinationTime(@Param("waybillId") String waybillId,@Param("date") Date date);

    /**
     * 更新监控送达时间
     * @param waybillId
     * @param date
     */
    void updateMonitorArriveTime(@Param("waybillId") String waybillId,@Param("date") Date date);

    /**
     * 更新开始搬运时间
     * @param waybillId
     * @param date
     */
    void updateBeginCarryTime(@Param("waybillId")String waybillId);

    /**
     * 更新完成时间与运单状态
     * @param waybillId 运单ID
     * @param status 状态
     */
    void updateCompleteTimeAndStatus(@Param("waybillId")String waybillId,
                                     @Param("status")String status,
                                     @Param("completeTime")Date completeTime);

    /**
     * 更新监控完成时间
     */
    void updateMonitorCompleteTime(SubWaybillMonitorStatusNotifyDTO subWaybillMonitorStatusNotifyDTO);

    /**
     * monitor_complete_time 改为当前时间
     * @param waybillIdList
     */
    void updateMonitorCompleteTimeForCurrent(@Param("waybillIdList") List<String> waybillIdList);

    /**
     * 司机抢单
     * @param snatchWaybillDO
     * @return
     */
    Integer snatchWaybill(SnatchWaybillDO snatchWaybillDO);

    WaybillRemoveDTO selectCancelObj(@Param("waybillId") String waybillId );

    BigDecimal selectProductDeliveryQuantity(@Param("waybillId")String waybillId);

    void updateWaybillStatusToWaitMerger(@Param("parentId") String parentId,@Param("status") String code);

    void driverCancelWaybill(@Param("waybillId") String waybillId, @Param("status") String status);

    void updateProductQuantityMap(@Param("mapId") String dispatchBillId, @Param("mapType") Integer code, @Param("quantity") BigDecimal quantity);
    
    /**
     * 更新状态成待配送
     * @Auther: <EMAIL>
     * @Date: 2018年9月3日 下午4:13:28
     * @param dto
     */
    void updateStatusToWaitDelivery(PassCheckDTO dto);
    
    /**
     * 通过waybill_id更新运单状态
     * @Auther: <EMAIL>
     * @Date: 2018年9月10日 下午3:57:41
     * @param waybillId
     * @param status
     * @param operatorId
     * @return
     */
    int updateStatusByWaybillId(@Param("waybillId")String waybillId, @Param("status")String status,
    		@Param("operatorId")String operatorId);
    
    /**
     * 通过审核更新数据库
     * 
     * @Auther: <EMAIL>
     * @Date: 2018年9月12日 下午9:14:23
     * @param dto
     * @return
     */
    int updateWaybillByPassCheckDTO(PassCheckDTO dto);
    
    /**
     * 更新运单状态为取消
     * @Auther: <EMAIL>
     * @Date: 2018年9月17日 上午11:14:08
     * @param dto
     * @return
     */
    int updateWaybillStatusToCancel(CancelWaybillDTO dto);

    /**
     * 司机确认运单
     * @param confirmWaybillDO 确认对象
     */
    int confirmWaybillByDriver(ConfirmWaybillDO confirmWaybillDO);

    /**
     * 更新主运单监控状态
     * @param waybillMonitorStatusNotifyDTO
     */
    void updateMainWaybillMonitorStatus(WaybillMonitorStatusNotifyDTO waybillMonitorStatusNotifyDTO);

    /**
     * 更新子运单排序状态
     * @param waybillId 子运单ID
     * @param sequence 顺序值
     */
    void updateSubWaybillSequence(@Param("waybillId")String waybillId, @Param("sequence")Integer sequence);
    
    /**
     * 更新主运单状态
     * @Auther: <EMAIL>
     * @Date: 2018年10月22日 下午3:44:26
     * @param dto
     * @return
     */
    int updateMainWaybillStatusByCon(UpdateStatusDTO dto);
    
    /**
     * 更新子运单状态
     * @Auther: <EMAIL>
     * @Date: 2018年10月22日 下午3:52:32
     * @param dto
     * @return
     */
    int updateSubWaybillStatusByCon(UpdateStatusDTO dto);
    
    /**
     * 
     * @Auther: <EMAIL>
     * @Date: 2018年10月22日 下午11:02:43
     * @param dto
     * @return
     */
    int updateSubWaybillStatusByRepublishCon(UpdateStatusDTO dto);

    /**
     * 批量审核运单(主)
     * @param waybillIdList
     * @return
     */
    int batchPassCheckMainWaybill(@Param("waybillIdList") List<String> waybillIdList,
                                  @Param("operatorId") String operatorId);

    /**
     * 批量审核子运单(子)
     * @param parentIdList
     * @param operatorId
     * @return
     */
    int batchPassCheckSubWaybill(@Param("parentIdList") List<String> parentIdList,
                                 @Param("operatorId") String operatorId);

    /**
     * 设置二级运单为已准备就绪
     * @param secondaryWaybillId
     * @return
     */
    int markSecondaryWaybillReady(@Param("secondaryWaybillId") String secondaryWaybillId);
}