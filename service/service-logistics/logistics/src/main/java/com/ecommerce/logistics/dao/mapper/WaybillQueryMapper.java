package com.ecommerce.logistics.dao.mapper;


import com.ecommerce.logistics.api.dto.dispatchbill.DispatchBillAssignDTO;
import com.ecommerce.logistics.api.dto.invoice.InvoiceBillCondDTO;
import com.ecommerce.logistics.api.dto.invoice.InvoiceBillListDTO;
import com.ecommerce.logistics.api.dto.pickingbill.PickingBillDTO;
import com.ecommerce.logistics.api.dto.realtime.VehicleRelationCondDTO;
import com.ecommerce.logistics.api.dto.waybill.AppWaybillListQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.AppWaybillQueryConditionsDTO;
import com.ecommerce.logistics.api.dto.waybill.AppWaybillStatisticsDTO;
import com.ecommerce.logistics.api.dto.waybill.CarrierWaybillListDTO;
import com.ecommerce.logistics.api.dto.waybill.CarrierWaybillListQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.CarryWaybillSubmitDTO;
import com.ecommerce.logistics.api.dto.waybill.CheckWaybillListQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.DistrictListDTO;
import com.ecommerce.logistics.api.dto.waybill.DriverWaybillListQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.MergeWaybillListQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.PublishWaybillListDTO;
import com.ecommerce.logistics.api.dto.waybill.PublishWaybillListQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.SeckillScanQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.TradeWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.UserSeckillWaybillQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.WarehouseAdminProccessedQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.WarehouseAdminWaitDeliveryQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillAssignListQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillCodeDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillDraftDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillLocationQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillMonitorPrepareDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillQuantityDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillStatusOption;
import com.ecommerce.logistics.biz.message.dto.WaybillCreatedSMSDTO;
import com.ecommerce.logistics.dao.dto.pickingbill.WaybillDO;
import com.ecommerce.logistics.dao.dto.waybill.HalfwayWaybillDO;
import com.ecommerce.logistics.dao.dto.waybill.MainWaybillDO;
import com.ecommerce.logistics.dao.dto.waybill.SnatchCountQueryDO;
import com.ecommerce.logistics.dao.dto.waybill.SubWaybillDO;
import com.ecommerce.logistics.dao.dto.waybill.TradeWaybillDetailDO;
import com.ecommerce.logistics.dao.dto.waybill.WaybillAmtDO;
import com.ecommerce.logistics.dao.dto.waybill.WaybillQuantityDO;
import com.ecommerce.logistics.dao.dto.waybill.WaybillQueryDO;
import com.ecommerce.logistics.dao.dto.waybill.WaybillSimpleDO;
import com.ecommerce.logistics.dao.dto.waybill.WaybillUserInfoDTO;
import com.ecommerce.logistics.dao.vo.Vehicle;
import com.ecommerce.logistics.dao.vo.Waybill;
import com.ecommerce.logistics.push.vo.SeckillWaybill;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface WaybillQueryMapper {
    /**
     *
     * @Auther: <EMAIL>
     * @Description: 根据dispatch_bill_id查询相关指派列表详情
     * @param dispatchBillId
     * @return
     */
    List<DispatchBillAssignDTO> selectBillAssignByDispatchBillId(@Param("dispatchBillId")String dispatchBillId);

    /**
     *	 根据dispatch_bill_id查询所有的waybill_id
     * @Auther: <EMAIL>
     * @Description:
     * @param dispatchBillId
     * @return
     */
    List<String> selectWaybillIdsByDispatchBillId(@Param("dispatchBillId")String dispatchBillId);

    /**
     * 	多条件查询待整合运单集合(lgs_waybill中status为1)
     * 	运单状态 1：待整合 2：待审核 3：待发布 4：待接单 5：待配送 6：配送中 7：已完成 8：已取消
     * @Auther: <EMAIL>
     * @Date: 2018年8月14日 下午4:49:35
     * @param mergeWaybillListQueryDTO
     * @return
     */
    List<WaybillDO> selectMergeWayBillByCondition(MergeWaybillListQueryDTO mergeWaybillListQueryDTO);

    /**
     * 通过运单ID集合查询运单详情（parent_id in）
     * @Auther: <EMAIL>
     * @Date: 2018年8月15日 上午11:04:18
     * @Description:
     * @param waybillIds
     * @return
     */
    List<WaybillDO> selectCheckWayBillByWayBillIds(List<String> waybillIds);

    /**
     * 条件查询待审核的运单ID列表
     * @Auther: <EMAIL>
     * @Date: 2018年8月25日 下午8:56:13
     * @param dto
     * @return
     */
    List<String> selectCheckWayBillIdsByCondition(CheckWaybillListQueryDTO dto);

    /**
     * 买家/卖家运单集合列表
     * @Auther: <EMAIL>
     * @Date: 2018年8月15日 下午4:54:03
     * @param dto
     * @return
     */
    List<WaybillDO> selectBuyerSellerWaybillByCondition(PublishWaybillListQueryDTO dto);

    /**
     * App运单集合列表
     * @Auther: ding
     * @Date: 2020年7月21日 上午11:17
     * @param dto
     * @return
     */
    List<PublishWaybillListDTO> selectAppWaybillByCondition(AppWaybillListQueryDTO dto);

    /**
     * APP运单统计（计划提货量，实际出场量）
     * @Auther: ding
     * @Date: 2020年7月22日 上午10:00
     * @param dto
     * @return
     */
    AppWaybillStatisticsDTO statisticsAppWaybill(AppWaybillListQueryDTO dto);

    /**
     * APP运单列表筛选条件查询(买家，卖家，商品)
     * @param queryDTO
     * @return
     */
    List<AppWaybillQueryConditionsDTO> selectAppWaybillQueryConditions(AppWaybillListQueryDTO queryDTO);

    /**
     *
     * @Auther: <EMAIL>
     * @Date: 2018年9月13日 下午3:56:41
     * @param dto
     * @return
     */
    List<CarrierWaybillListDTO> selectMainCarrierWaybillByCondition(CarrierWaybillListQueryDTO dto);

    /**
     * @Auther: <EMAIL>
     * @Date: 2018年10月8日 下午7:36:08
     * @param waybillIds
     * @return
     */
    List<CarrierWaybillListDTO> selectMainCarrierWaybillByMainWaybillIds(@Param("waybillIds")List<String> waybillIds);

    /**
     * 多条件查询承运商主运单ID
     * @Auther: <EMAIL>
     * @Date: 2018年10月8日 下午5:36:11
     * @param dto
     * @return
     */
    List<String> selectMainWaybillIdByCarrierCon(CarrierWaybillListQueryDTO dto);

    /**
     * 根据查询条件去重后得到车两信息
     * @Auther: <EMAIL>
     * @Date: 2018年8月16日 上午11:00:08
     * @param dto
     * @return
     */
    List<Vehicle> selectVehicleInfoByCondition(WaybillAssignListQueryDTO dto);

    /**
     * 多条件查询运单指派集合
     * @Auther: <EMAIL>
     * @Date: 2018年8月16日 上午10:00:58
     * @param dto
     * @return
     */
    List<WaybillDO> selectWaybillAssignByCondition(WaybillAssignListQueryDTO dto);

    /**
     *
     * @Auther: <EMAIL>
     * @Date: 2018年8月16日 下午3:30:08
     * @param dispatchBillIds
     * @return
     */
    List<WaybillDTO> selectWaybillsByDispatchIds(List<String> dispatchBillIds);

    /**
     *
     * @Auther: <EMAIL>
     * @Date: 2018年8月16日 下午4:21:55
     * @param pickingBillIds
     * @return
     */
    List<WaybillDTO> selectWaybillsByPickingBillIds(List<String> pickingBillIds);

    /**
     *
     * @Auther: <EMAIL>
     * @Date: 2018年8月16日 下午4:51:03
     * @param waybillIds
     * @return
     */
    List<WaybillDTO> selectWaybillsByWaybillIds(List<String> waybillIds);

    /**
     * 通过运单查询运单详情
     * @Auther: <EMAIL>
     * @Date: 2018年8月17日 上午11:01:07
     * @param waybillId
     * @return
     */
    WaybillDO selectWaybillDetailsBywaybillId(@Param("waybillId")String waybillId);

    WaybillDO selectWaybillDetailsByWaybillNum(@Param("waybillNum")String waybillNum);

    /**
     * 通过运单号查询运单实体
     * @Auther: <EMAIL>
     * @Date: 2018年8月17日 下午3:30:16
     * @param waybillNum
     * @return
     */
    Waybill selectWaybillByWaybillNum(@Param("waybillNum")String waybillNum);

    /**
     * 提货单ID批量查询运单商品数量对象
     * @param pickingBillIds
     * @return
     */
    List<WaybillQuantityDTO> selectWaybillQuantityByPickingBillIds(List<String> pickingBillIds);

    /**
     * 通过主运单Id查询所有运单号集合(包括所有次运单)
     * @Auther: <EMAIL>
     * @Date: 2018年9月3日 下午4:06:25
     * @param parentId
     * @return
     */
    List<String> selectWaybillIdsByParentId(@Param("parentId")String parentId);

    List<WaybillDO> selectDispatchWaybillAssign(@Param("dispatchBillId")String dispatchBillId);

    /**
     * 运单ID查询运单草稿信息
     * @param waybillId 运单ID
     * @return WaybillDraftDTO 运单草稿对象
     */
    WaybillDraftDTO selectWaybillDraftInfoByWaybillId(String waybillId);

    WaybillDO selectWaybillLocationInfo(@Param("waybillId")String waybillId);

    /**
     * 查询省市区且运单状态为待整合的运单经纬度集合
     * @Auther: <EMAIL>
     * @Date: 2018年9月4日 下午8:08:34
     * @param map
     * 			provinceCode  	省份
     * 			cityCode		城市
     * 			districtCode	区县
     * 			waybillStatus	运单状态   required
     * @return
     */
    List<WaybillDO> selectWaybillLocationByDistrict(Map<String,String> map);

    /**
     * 运单ID查询运单监控准备数据
     * @param waybillIdList 运单ID列表
     * @return List<WaybillMonitorPrepareDTO> 运单监控数据对象列表
     */
    List<WaybillMonitorPrepareDTO> selectWaybillMonitorPrepareData(List<String> waybillIdList);

    /**
     * 父运单ID查询运单监控准备数据
     * @param parentId 父运单ID
     * @return List<WaybillMonitorPrepareDTO>
     */
    List<WaybillMonitorPrepareDTO> selectWaybillMonitorPrepareForMergeData(String parentId);

    /**
     * 查询父运单监控准备数据
     * @param waybillId 运单ID
     * @return WaybillMonitorPrepareDTO 父运单监控准备对象
     */
    WaybillMonitorPrepareDTO selectWaybillMonitorPrepareParentData(String waybillId);

    /**
     *
     * @Auther: <EMAIL>
     * @Date: 2018年9月6日 上午10:53:53
     * @param dto
     * @return
     */
    List<WaybillDO> selectWaybillLocationByCon(WaybillLocationQueryDTO dto);

    /**
     * 查询运单配送信息(包括子运单)
     * @Auther: <EMAIL>
     * @Date: 2018年9月7日 上午11:45:44
     * @param parentId
     * @return
     */
    List<WaybillDO> selectWaybillProductDetailByParentId(@Param("parentId")String parentId);

    /**
     * 根据运单号查询运单商品配送信息
     * @Auther: <EMAIL>
     * @Date: 2018年9月7日 下午2:57:50
     * @param waybillId
     * @return
     */
    WaybillDO selectWaybillProductDetailByWaybillId(@Param("waybillId")String waybillId);

    /**
     * 查询运单的完成时间以及司机信息
     * @Auther: <EMAIL>
     * @Date: 2018年9月7日 下午3:10:14
     * @param waybillId
     * @return
     */
    WaybillDO selectCompleteInfoBywaybillId(@Param("waybillId")String waybillId);

    /**
     *
     * @Auther: <EMAIL>
     * @Date: 2018年9月7日 下午7:53:40
     * @param dto
     * @return
     */
    List<String> selectWaybillIdsByWarehouseAdminWaitDeliveryCon(WarehouseAdminWaitDeliveryQueryDTO dto);

    /**
     *
     * @Auther: <EMAIL>
     * @Date: 2018年9月8日 上午10:39:19
     * @param dto
     * @return
     */
    List<String> selectMainWaybillIdByWarehouseAdminProccessedCon(WarehouseAdminProccessedQueryDTO dto);

    /**
     * 查询Id为parentid
     * @Auther: <EMAIL>
     * @Date: 2018年9月7日 下午8:20:10
     * @param waybillIds
     * @return
     */
    List<WaybillDO> selectByWarehouseAdminWaitDeliveryByWaybillIds(@Param("waybillIds")List<String> waybillIds);

    /**
     * 查询ID为parentId
     * @Auther: <EMAIL>
     * @Date: 2018年9月8日 上午10:49:48
     * @param waybillIds
     * @return
     */
    List<WaybillDO> selectByWarehouseAdminProccessedByMainWaybillIds(@Param("waybillIds")List<String> waybillIds);

    /**
     * 查询朱运单下所有次运单的状态
     * @Auther: <EMAIL>
     * @Date: 2018年9月8日 下午12:11:50
     * @param parentId
     * @return
     */
    List<String> selectSecondaryStatusByParentId(@Param("parentId")String parentId);

    /**
     * 查询所有子运单运单号
     * @Auther: <EMAIL>
     * @Date: 2018年9月8日 下午2:35:38
     * @param parentId
     * @return
     */
    List<String> selectSubWaybillIdListByParentId(@Param("parentId")String parentId);

    /**
     * 获取用户抢单仓库ID列表
     * @Auther: <EMAIL>
     * @Date: 2018年9月11日 下午5:29:00
     * @param userId
     * @param status
     * @return
     */
    List<String> selectSeckillWarehouseIdByUserId(@Param("userId")String userId, @Param("status")String status);

    /**
     * 获取用户可抢单区域列表
     * @Auther: <EMAIL>
     * @Date: 2018年9月11日 下午5:55:52
     * @param userId
     * @param status
     * @return
     */
    List<DistrictListDTO> selectSeckillDistrictsByUserId(@Param("userId")String userId, @Param("status")String status);

    /**
     * 查询用户可抢单ID列表
     * @Auther: <EMAIL>
     * @Date: 2018年9月11日 下午8:14:45
     * @param dto
     * @return
     */
    List<String> selectSeckillWaybillIdsByCon(UserSeckillWaybillQueryDTO dto);

    /**
     * 多条件查询可抢运单
     * @Auther: <EMAIL>
     * @Date: 2018年10月6日 下午4:49:41
     * @param pageQuery
     * @return
     */
    List<String> selectSeckillScanWaybillIdsByCon(SeckillScanQueryDTO pageQuery);

    /**
     *  司机APP端已处理运单ID查询
     * @Auther: <EMAIL>
     * @Date: 2018年9月11日 下午10:03:46
     * @param dto
     * @return
     */
    List<String> selectDriverWaybillIdsByCon(DriverWaybillListQueryDTO dto);

    /**
     * 承运商APP端已处理运单ID查询
     * @Auther: <EMAIL>
     * @Date: 2018年9月11日 下午10:03:46
     * @param dto
     * @return
     */
    List<String> selectCarrierWaybillIdsByCon(DriverWaybillListQueryDTO dto);

    /**
     * 通过parentID查询运单信息
     * @Auther: <EMAIL>
     * @Date: 2018年9月11日 下午8:24:51
     * @param waybillIds
     * @return
     */
    List<WaybillDO> selectSeckillWaybillByParentIds(@Param("waybillIds")List<String> waybillIds);

    /**
     * 查询交易运单列表
      * @param pickingBillIdList 提货单Id列表
     * @return List<TradeWaybillDTO>
     */
    List<TradeWaybillDTO> queryTradeWaybillList(@Param("pickingBillIdList")List<String> pickingBillIdList,
                                                @Param("statusList")List<String> statusList);

    WaybillDO selectCityAndQuantityByWaybilId(@Param("waybillId")String waybillId);

    /**
     * 查询合并运单总运输重量
     * @param waybillIdList 运单ID列表
     * @return 总重量
     */
    WaybillQuantityDO queryWaybillTotalQuantity(List<String> waybillIdList);

    /**
     * 查询主运单列表
     * @param waybillQueryDO 运单查询对象
     * @return List<MainWaybillDO>
     */
    List<MainWaybillDO> selectMainWaybillList(WaybillQueryDO waybillQueryDO);

    /**
     * 根据父运单ID查询子运单信息
     * @param parentIdList
     * @return
     */
    List<SubWaybillDO> selectSubWaybillListByParentId(@Param("list")List<String> parentIdList,
                                                      @Param("statusList")List<String> statusList);

    /**
     * 条件查询主运单ID列表
     * @param waybillQueryDO 运单查询对象
     * @return List<String>
     */
    List<String> selectMainWaybillIdByCondition(WaybillQueryDO waybillQueryDO);

    /**
     *
     * @Auther: <EMAIL>
     * @Date: 2018年9月28日 下午10:17:17
     * @param waybillQueryDO
     * @return
     */
    List<String> selectWaitCheckMainIdByCon(WaybillQueryDO waybillQueryDO);

    /**
     * 查询主运单对象列表
     * @param parentIdList 主运单ID列表
     * @return List<MainWaybillDO>
     */
    List<MainWaybillDO> selectMainWaybillListById(List<String> parentIdList);

    List<MainWaybillDO> selectMainWaybillInfoByMainIds(List<String> parentIdList);

    /**
     * 查询所有子运单
     * @Auther: <EMAIL>
     * @Date: 2018年10月9日 上午10:18:51
     * @param parentId
     * @return
     */
    List<Waybill> selectSubWaybillByParentId(@Param("parentId")String parentId);

    /**
     * 查询待处理主运单ID集合
     * @Auther: <EMAIL>
     * @Date: 2018年10月15日 下午2:31:39
     * @param waybillQueryDO
     * @return
     */
    List<String> selectWaitDeliveryMainWaybillIdByCondition(WaybillQueryDO waybillQueryDO);

    /**
     * 查询交易运单
     * @param waybillId
     * @return
     */
    TradeWaybillDetailDO queryTradeWaybillDetail(String waybillId);

    /**
     * 查询用户指定日期未完成的运单数量
     * @Auther: <EMAIL>
     * @Date: 2018年10月20日 下午3:47:39
     * @param dto
     * @return
     */
    int selectWaybillSnatchCountByCon(SnatchCountQueryDO dto);

    /**
     * 通过主运单号查询子运单信息
     * @Auther: <EMAIL>
     * @Date: 2018年10月24日 下午2:08:42
     * @param parentId
     * @return
     */
    List<WaybillDO> selectSubWaybillInfoByParentId(@Param("parentId")String parentId);

    /**
     * 通过子运单ID集合查询子运单详情
     * @param subWaybillIds
     * @return
     */
    List<WaybillDO> selectSubWaybillInfoByWaybillIds(@Param("subWaybillIds")List<String> subWaybillIds);

    /**
     * 主运单ID批量查询运单数量
     * @param waybillIdList 主运单ID列表
     * @return List<WaybillQuantityDO>
     */
    List<WaybillQuantityDO> queryWaybillTotalQuantityByParentId(List<String> waybillIdList);

    /**
     * 运单ID查询运单数量
     * @param waybillId 运单ID
     * @return List<WaybillQuantityDO>
     */
    WaybillQuantityDO queryWaybillTotalQuantityByWaybillId(String waybillId);

    /**
     * 统计司机在途运单数量
     * @param halfwayWaybillDO 在途运单查询对象
     * @return Integer 在途运单数量
     */
    Integer countDriverHalfwayWaybill(HalfwayWaybillDO halfwayWaybillDO);

    /**
     * 查询司机在途的车辆列表
     * @param driverId
     * @return
     */
    List<String> queryHalfWayVehicleNum(@Param("driverId") String driverId);

    /**
     * 查询主运单ID列表
     * @param deliverySheetNumList 发货单号列表
     * @return List<String>
     */
    List<String> queryMainWaybillIdList(@Param("deliverySheetNumList") List<String> deliverySheetNumList,
                                        @Param("waybillStatusList") List<String> waybillStatusList);

    /**
     * 根据运单号查询提货单信息
     * @param waybillNumList 运单号列表
     * @return List<PickingBillDTO>
     */
    List<PickingBillDTO> queryPickingListByWaybillNum(List<String> waybillNumList);

    /**
     * 查询运单发货单号
     * @param waybillIdList
     * @return
     */
    List<WaybillCodeDTO> queryDeliverySheetNumByWaybillId(List<String> waybillIdList);

    /**
     * 获取待开票的运单父运单ID
     * @param invoiceBillCondDTO
     * @return
     */
    List<String> queryWaitInvoiceWaybillParentIdList(InvoiceBillCondDTO invoiceBillCondDTO);

    /**
     * 运单ID集合查询待开票运单列表
     * @param waybillIdList
     * @return
     */
    List<InvoiceBillListDTO> queryWaitInvoiceWaybillByIds(@Param("waybillIdList") List<String> waybillIdList);

    /**
     * 更新开票状态
     * @param waybillIdList
     * @param invoiceStatus
     * @param invoiceMessage
     * @return
     */
    int updateInvoiceStatus(@Param("waybillIdList") List<String> waybillIdList,
                            @Param("invoiceStatus") String invoiceStatus,
                            @Param("invoiceMessage") String invoiceMessage);

    /**
     * 更新通知来的开票信息
     * @param parentId
     * @param invoiceStatus
     * @param invoiceMessage
     * @return
     */
    int updateInvoiceForNotify(@Param("parentId") String parentId,
                               @Param("invoiceStatus") String invoiceStatus,
                               @Param("invoiceMessage") String invoiceMessage);


    /**
     * 计数平台配送的运单
     * @param waybillId
     * @return
     */
    int countByPickingBillType(@Param("waybillId") String waybillId, @Param("pickingBillType") String pickingBillType);

    /**
     * 查询运单最新状态
     * @param waybillNumList
     * @return
     */
    List<WaybillStatusOption> queryWaybillStatusByNum(@Param("waybillNumList") List<String> waybillNumList);

    /**
     * 为创建的运单查询短信相关消息
     * @param waybillId
     * @return
     */
    WaybillCreatedSMSDTO queryWaybillForCreatedSMS(@Param("waybillId") String waybillId);

    /**
     * 查询出运单信息为合并抢单
     * @param parentIdList
     * @return
     */
    List<SeckillWaybill> selectSeckillByParentIdList(@Param("parentIdList") List<String> parentIdList);

    /**
     * 根据parentId查找所有运单ID,包括parentId
     * @param parentIdList
     * @return
     */
    List<String> selectWaybillIdByParentIdList(@Param("parentIdList") List<String> parentIdList);

    List<WaybillSimpleDO> selectSimpleByVehicleRelationCond(VehicleRelationCondDTO vehicleRelationCondDTO);

    WaybillUserInfoDTO selectWaybillUserInfo(@Param("waybillId") String waybillId);


    WaybillAmtDO queryInitWaybillAmtInfo(@Param("waybillId") String waybillId);

    /**
     * 发货单号查询处于指定状态的运单列表
     * @param deliverySheetNumList
     * @param statusList
     * @return
     */
    List<Waybill> selectByDeliverySheetNumAndStatus(@Param("deliverySheetNumList") List<String> deliverySheetNumList, @Param("statusList") List<String> statusList);

    /**
     * 根据运单号查询已完成运单详情（信息上报调用）
     */
    CarryWaybillSubmitDTO selectCarryWaybill(String waybillNum);
}
