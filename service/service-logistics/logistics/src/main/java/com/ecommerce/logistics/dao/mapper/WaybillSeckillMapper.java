package com.ecommerce.logistics.dao.mapper;

import org.apache.ibatis.annotations.Param;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.dao.vo.WaybillSeckill;

/**
 * 
 * @Auther: <EMAIL>
 * @Date: 2018年9月12日 下午7:32:38
 * @Description:
 */
public interface WaybillSeckillMapper extends IBaseMapper<WaybillSeckill> {
	
	/**
	 * 
	 * @Auther: <EMAIL>
	 * @Date: 2018年9月12日 下午7:33:24
	 * @param waybillId
	 */
	void deleteByWaybillId(@Param("waybillId")String waybillId);
}