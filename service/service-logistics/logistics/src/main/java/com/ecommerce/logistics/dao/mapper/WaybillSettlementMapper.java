package com.ecommerce.logistics.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.api.dto.settlement.CarriageSettlementDTO;
import com.ecommerce.logistics.api.dto.settlement.SettlementItemListQueryDTO;
import com.ecommerce.logistics.api.dto.settlement.UserSettlementListDTO;
import com.ecommerce.logistics.api.dto.settlement.UserSettlementListQueryDTO;
import com.ecommerce.logistics.dao.dto.settlement.SettlementItemListDO;
import com.ecommerce.logistics.dao.dto.settlement.SettlementUpdateDO;
import com.ecommerce.logistics.dao.vo.WaybillSettlement;

import java.util.List;

public interface WaybillSettlementMapper extends IBaseMapper<WaybillSettlement> {
    /**
     * 查询结算子项列表
     * @param settlementItemListQueryDTO 条件查询对象
     * @return  List<SettlementItemListDTO>
     */
    List<SettlementItemListDO> querySettlementItemList(SettlementItemListQueryDTO settlementItemListQueryDTO);

    /**
     * 运单运费结算
     * @param carriageSettlementDTO 运单结算对象
     */
    void waybillCarriageSettlement(CarriageSettlementDTO carriageSettlementDTO);

    /**
     * 查询用户结算信息列表
     * @param userSettlementListQueryDTO 用户结算信息查询对象
     * @return UserSettlementListDTO
     */
    List<UserSettlementListDTO> queryUserSettlementList(UserSettlementListQueryDTO userSettlementListQueryDTO);

    /**
     * 用户运费结算
     * @param settlementListRemoveDTO 结算对象
     */
    void userCarriageSettlement(CarriageSettlementDTO settlementListRemoveDTO);

    /**
     * 统计运单结算数据
     * @param waybillIdList 运单ID列表
     * @return SettlementUpdateDO
     */
    SettlementUpdateDO queryWaybillSettlementData(List<String> waybillIdList);
}