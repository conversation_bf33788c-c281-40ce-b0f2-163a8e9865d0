package com.ecommerce.logistics.dao.vo;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

@Table(name = "lgs_assign_driver_log")
public class AssignDriverLog implements Serializable {
    /**
     * 车辆指派记录主键
     */
    @Id
    @Column(name = "assign_driver_id")
    private String assignDriverId;

    /**
     * 归属人MemberID
     */
    @Column(name = "user_id")
    private String userId;

    /**
     * 归属用户名称
     */
    @Column(name = "user_name")
    private String userName;

    /**
     * 用户类型 1：买家 2：卖家 3：承运商
     */
    @Column(name = "user_type")
    private Integer userType;

    /**
     * 绑定司机id
     */
    @Column(name = "driver_id")
    private String driverId;

    /**
     * 司机信息
     */
    @Column(name = "driver_name")
    private String driverName;

    /**
     * 司机手机
     */
    @Column(name = "driver_phone")
    private String driverPhone;

    /**
     * 删除标识
     */
    @Column(name = "del_flg")
    private Byte delFlg;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 数据版本
     */
    private Long version;

    private static final long serialVersionUID = 1L;

    /**
     * 获取车辆指派记录主键
     *
     * @return assign_driver_id - 车辆指派记录主键
     */
    public String getAssignDriverId() {
        return assignDriverId;
    }

    /**
     * 设置车辆指派记录主键
     *
     * @param assignDriverId 车辆指派记录主键
     */
    public void setAssignDriverId(String assignDriverId) {
        this.assignDriverId = assignDriverId == null ? null : assignDriverId.trim();
    }

    /**
     * 获取归属人MemberID
     *
     * @return user_id - 归属人MemberID
     */
    public String getUserId() {
        return userId;
    }

    /**
     * 设置归属人MemberID
     *
     * @param userId 归属人MemberID
     */
    public void setUserId(String userId) {
        this.userId = userId == null ? null : userId.trim();
    }

    /**
     * 获取归属用户名称
     *
     * @return user_name - 归属用户名称
     */
    public String getUserName() {
        return userName;
    }

    /**
     * 设置归属用户名称
     *
     * @param userName 归属用户名称
     */
    public void setUserName(String userName) {
        this.userName = userName == null ? null : userName.trim();
    }

    /**
     * 获取用户类型 1：买家 2：卖家 3：承运商
     *
     * @return user_type - 用户类型 1：买家 2：卖家 3：承运商
     */
    public Integer getUserType() {
        return userType;
    }

    /**
     * 设置用户类型 1：买家 2：卖家 3：承运商
     *
     * @param userType 用户类型 1：买家 2：卖家 3：承运商
     */
    public void setUserType(Integer userType) {
        this.userType = userType;
    }

    /**
     * 获取绑定司机id
     *
     * @return driver_id - 绑定司机id
     */
    public String getDriverId() {
        return driverId;
    }

    /**
     * 设置绑定司机id
     *
     * @param driverId 绑定司机id
     */
    public void setDriverId(String driverId) {
        this.driverId = driverId == null ? null : driverId.trim();
    }

    /**
     * 获取司机信息
     *
     * @return driver_name - 司机信息
     */
    public String getDriverName() {
        return driverName;
    }

    /**
     * 设置司机信息
     *
     * @param driverName 司机信息
     */
    public void setDriverName(String driverName) {
        this.driverName = driverName == null ? null : driverName.trim();
    }

    /**
     * 获取司机手机
     *
     * @return driver_phone - 司机手机
     */
    public String getDriverPhone() {
        return driverPhone;
    }

    /**
     * 设置司机手机
     *
     * @param driverPhone 司机手机
     */
    public void setDriverPhone(String driverPhone) {
        this.driverPhone = driverPhone == null ? null : driverPhone.trim();
    }

    /**
     * 获取删除标识
     *
     * @return del_flg - 删除标识
     */
    public Byte getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标识
     *
     * @param delFlg 删除标识
     */
    public void setDelFlg(Byte delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取数据版本
     *
     * @return version - 数据版本
     */
    public Long getVersion() {
        return version;
    }

    /**
     * 设置数据版本
     *
     * @param version 数据版本
     */
    public void setVersion(Long version) {
        this.version = version;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", assignDriverId=").append(assignDriverId);
        sb.append(", userId=").append(userId);
        sb.append(", userName=").append(userName);
        sb.append(", userType=").append(userType);
        sb.append(", driverId=").append(driverId);
        sb.append(", driverName=").append(driverName);
        sb.append(", driverPhone=").append(driverPhone);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", version=").append(version);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}