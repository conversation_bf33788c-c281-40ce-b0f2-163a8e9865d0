package com.ecommerce.logistics.dao.vo;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

@Table(name = "lgs_attachment")
public class Attachment implements Serializable {
    @Id
    @Column(name = "attachment_id")
    private String attachmentId;

    /**
     * 文件的唯一标识
     */
    @Column(name = "bid")
    private String bid;

    /**
     * 业务实体ID
     */
    @Column(name = "entry_id")
    private String entryId;

    /**
     * 附件类型 1：图片  2：视频 3：文件
     */
    private String type;

    /**
     * 文件全名
     */
    @Column(name = "file_name")
    private String fileName;

    /**
     * 文件扩展名
     */
    @Column(name = "file_ext")
    private String fileExt;

    /**
     * 附件url
     */
    @Column(name = "file_url")
    private String fileUrl;

    /**
     * 前端定义，图片位置序号，1,2,3...
     */
    @Column(name = "serial_no")
    private Integer serialNo;

    /**
     * 删除标识
     */
    @Column(name = "del_flg")
    private Integer delFlg;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 数据版本
     */
    private Long version;

    private static final long serialVersionUID = 1L;

    /**
     * @return attachment_id
     */
    public String getAttachmentId() {
        return attachmentId;
    }

    /**
     * @param attachmentId
     */
    public void setAttachmentId(String attachmentId) {
        this.attachmentId = attachmentId == null ? null : attachmentId.trim();
    }

    /**
     * 获取文件的唯一标识
     *
     * @return b_id - 文件的唯一标识
     */
    public String getBid() {
        return bid;
    }

    /**
     * 设置文件的唯一标识
     *
     * @param bid 文件的唯一标识
     */
    public void setBid(String bid) {
        this.bid = bid == null ? null : bid.trim();
    }

    /**
     * 获取业务实体ID
     *
     * @return entry_id - 业务实体ID
     */
    public String getEntryId() {
        return entryId;
    }

    /**
     * 设置业务实体ID
     *
     * @param entryId 业务实体ID
     */
    public void setEntryId(String entryId) {
        this.entryId = entryId == null ? null : entryId.trim();
    }

    /**
     * 获取附件类型 1：图片  2：视频 3：文件
     *
     * @return type - 附件类型 1：图片  2：视频 3：文件
     */
    public String getType() {
        return type;
    }

    /**
     * 设置附件类型 1：图片  2：视频 3：文件
     *
     * @param type 附件类型 1：图片  2：视频 3：文件
     */
    public void setType(String type) {
        this.type = type;
    }

    /**
     * 获取文件全名
     *
     * @return file_name - 文件全名
     */
    public String getFileName() {
        return fileName;
    }

    /**
     * 设置文件全名
     *
     * @param fileName 文件全名
     */
    public void setFileName(String fileName) {
        this.fileName = fileName == null ? null : fileName.trim();
    }

    /**
     * 获取文件扩展名
     *
     * @return file_ext - 文件扩展名
     */
    public String getFileExt() {
        return fileExt;
    }

    /**
     * 设置文件扩展名
     *
     * @param fileExt 文件扩展名
     */
    public void setFileExt(String fileExt) {
        this.fileExt = fileExt == null ? null : fileExt.trim();
    }

    /**
     * 获取附件url
     *
     * @return file_url - 附件url
     */
    public String getFileUrl() {
        return fileUrl;
    }

    /**
     * 设置附件url
     *
     * @param fileUrl 附件url
     */
    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl == null ? null : fileUrl.trim();
    }

    /**
     * 获取前端定义，图片位置序号，1,2,3...
     *
     * @return serial_no - 前端定义，图片位置序号，1,2,3...
     */
    public Integer getSerialNo() {
        return serialNo;
    }

    /**
     * 设置前端定义，图片位置序号，1,2,3...
     *
     * @param serialNo 前端定义，图片位置序号，1,2,3...
     */
    public void setSerialNo(Integer serialNo) {
        this.serialNo = serialNo;
    }

    /**
     * 获取删除标识
     *
     * @return del_flg - 删除标识
     */
    public Integer getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标识
     *
     * @param delFlg 删除标识
     */
    public void setDelFlg(Integer delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取数据版本
     *
     * @return version - 数据版本
     */
    public Long getVersion() {
        return version;
    }

    /**
     * 设置数据版本
     *
     * @param version 数据版本
     */
    public void setVersion(Long version) {
        this.version = version;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", attachmentId=").append(attachmentId);
        sb.append(", bid=").append(bid);
        sb.append(", entryId=").append(entryId);
        sb.append(", type=").append(type);
        sb.append(", fileName=").append(fileName);
        sb.append(", fileExt=").append(fileExt);
        sb.append(", fileUrl=").append(fileUrl);
        sb.append(", serialNo=").append(serialNo);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", version=").append(version);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}