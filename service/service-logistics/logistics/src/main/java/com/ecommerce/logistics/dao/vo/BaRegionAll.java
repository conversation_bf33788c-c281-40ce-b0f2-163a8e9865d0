package com.ecommerce.logistics.dao.vo;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

@Table(name = "ba_region_all")
public class BaRegionAll implements Serializable {
    /**
     * 主键
     */
    @Id
    private String id;

    /**
     * 全关系主Code
     */
    @Column(name = "region_code")
    private String regionCode;

    /**
     * 全关系子code
     */
    @Column(name = "region_son_code")
    private String regionSonCode;

    private String depth;

    /**
     * 根code
     */
    @Column(name = "root_code")
    private String rootCode;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;

    /**
     * 获取主键
     *
     * @return id - 主键
     */
    public String getId() {
        return id;
    }

    /**
     * 设置主键
     *
     * @param id 主键
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * 获取全关系主Code
     *
     * @return region_code - 全关系主Code
     */
    public String getRegionCode() {
        return regionCode;
    }

    /**
     * 设置全关系主Code
     *
     * @param regionCode 全关系主Code
     */
    public void setRegionCode(String regionCode) {
        this.regionCode = regionCode == null ? null : regionCode.trim();
    }

    /**
     * 获取全关系子code
     *
     * @return region_son_code - 全关系子code
     */
    public String getRegionSonCode() {
        return regionSonCode;
    }

    /**
     * 设置全关系子code
     *
     * @param regionSonCode 全关系子code
     */
    public void setRegionSonCode(String regionSonCode) {
        this.regionSonCode = regionSonCode == null ? null : regionSonCode.trim();
    }

    /**
     * @return depth
     */
    public String getDepth() {
        return depth;
    }

    /**
     * @param depth
     */
    public void setDepth(String depth) {
        this.depth = depth == null ? null : depth.trim();
    }

    /**
     * 获取根code
     *
     * @return root_code - 根code
     */
    public String getRootCode() {
        return rootCode;
    }

    /**
     * 设置根code
     *
     * @param rootCode 根code
     */
    public void setRootCode(String rootCode) {
        this.rootCode = rootCode == null ? null : rootCode.trim();
    }

    /**
     * @return create_time
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * @param createTime
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * @return update_time
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * @param updateTime
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", regionCode=").append(regionCode);
        sb.append(", regionSonCode=").append(regionSonCode);
        sb.append(", depth=").append(depth);
        sb.append(", rootCode=").append(rootCode);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}