package com.ecommerce.logistics.dao.vo;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

@Table(name = "ba_sale_region_All")
public class BaSaleRegionAll implements Serializable {
    /**
     * 主键
     */
    @Id
    private String id;

    /**
     * 销售区域父ID
     */
    @Column(name = "region_id")
    private String regionId;

    /**
     * 销售区域叶子ID
     */
    @Column(name = "region_son_id")
    private String regionSonId;

    /**
     * root_id
     */
    @Column(name = "root_id")
    private String rootId;

    /**
     * 层级
     */
    private Integer depth;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 卖家id
     */
    @Column(name = "seller_id")
    private String sellerId;

    private static final long serialVersionUID = 1L;

    /**
     * 获取主键
     *
     * @return id - 主键
     */
    public String getId() {
        return id;
    }

    /**
     * 设置主键
     *
     * @param id 主键
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * 获取销售区域父ID
     *
     * @return region_id - 销售区域父ID
     */
    public String getRegionId() {
        return regionId;
    }

    /**
     * 设置销售区域父ID
     *
     * @param regionId 销售区域父ID
     */
    public void setRegionId(String regionId) {
        this.regionId = regionId == null ? null : regionId.trim();
    }

    /**
     * 获取销售区域叶子ID
     *
     * @return region_son_id - 销售区域叶子ID
     */
    public String getRegionSonId() {
        return regionSonId;
    }

    /**
     * 设置销售区域叶子ID
     *
     * @param regionSonId 销售区域叶子ID
     */
    public void setRegionSonId(String regionSonId) {
        this.regionSonId = regionSonId == null ? null : regionSonId.trim();
    }

    /**
     * 获取root_id
     *
     * @return root_id - root_id
     */
    public String getRootId() {
        return rootId;
    }

    /**
     * 设置root_id
     *
     * @param rootId root_id
     */
    public void setRootId(String rootId) {
        this.rootId = rootId == null ? null : rootId.trim();
    }

    /**
     * 获取层级
     *
     * @return depth - 层级
     */
    public Integer getDepth() {
        return depth;
    }

    /**
     * 设置层级
     *
     * @param depth 层级
     */
    public void setDepth(Integer depth) {
        this.depth = depth;
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取卖家id
     *
     * @return seller_id - 卖家id
     */
    public String getSellerId() {
        return sellerId;
    }

    /**
     * 设置卖家id
     *
     * @param sellerId 卖家id
     */
    public void setSellerId(String sellerId) {
        this.sellerId = sellerId == null ? null : sellerId.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", regionId=").append(regionId);
        sb.append(", regionSonId=").append(regionSonId);
        sb.append(", rootId=").append(rootId);
        sb.append(", depth=").append(depth);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", sellerId=").append(sellerId);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}