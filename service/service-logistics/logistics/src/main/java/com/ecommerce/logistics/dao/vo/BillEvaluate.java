package com.ecommerce.logistics.dao.vo;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

@Table(name = "lgs_bill_evaluate")
public class BillEvaluate implements Serializable {
    /**
     * 评价ID
     */
    @Id
    @Column(name = "evaluate_id")
    private String evaluateId;

    /**
     * 评价编号
     */
    @Column(name = "evaluate_num")
    private String evaluateNum;

    /**
     * 评价类型
     */
    @Column(name = "evaluate_type")
    private String evaluateType;

    /**
     * 评价维度（评价承运方/托运方）
     */
    private String type;

    /**
     * 关联单据ID
     */
    @Column(name = "relation_bill_id")
    private String relationBillId;

    /**
     * 关联单据编号
     */
    @Column(name = "relation_bill_num")
    private String relationBillNum;

    /**
     * 关联单据类型
     */
    @Column(name = "relation_bill_type")
    private String relationBillType;

    /**
     * 被评价主体ID
     */
    @Column(name = "evaluated_person_id")
    private String evaluatedPersonId;

    /**
     * 被评价主体名称
     */
    @Column(name = "evaluated_person_name")
    private String evaluatedPersonName;

    /**
     * 被评价主体类型
     */
    @Column(name = "evaluated_person_type")
    private String evaluatedPersonType;

    /**
     * 评价人ID
     */
    @Column(name = "evaluator_id")
    private String evaluatorId;

    /**
     * 评价人名称
     */
    @Column(name = "evaluator_name")
    private String evaluatorName;

    /**
     * 评价人类型
     */
    @Column(name = "evaluator_type")
    private String evaluatorType;

    /**
     * 运输效率评分
     */
    @Column(name = "tc_score")
    private Byte tcScore;

    /**
     * 运输安全评分
     */
    @Column(name = "ts_score")
    private Byte tsScore;

    /**
     * 服务质量评分
     */
    @Column(name = "sq_score")
    private Byte sqScore;

    /**
     * 客户满意度评分
     */
    @Column(name = "cs_score")
    private Byte csScore;

    /**
     * 托运方评分
     */
    @Column(name = "shipper_score")
    private Byte shipperScore;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 删除标识
     */
    @Column(name = "del_flg")
    private Byte delFlg;

    private static final long serialVersionUID = 1L;

    /**
     * 获取评价ID
     *
     * @return evaluate_id - 评价ID
     */
    public String getEvaluateId() {
        return evaluateId;
    }

    /**
     * 设置评价ID
     *
     * @param evaluateId 评价ID
     */
    public void setEvaluateId(String evaluateId) {
        this.evaluateId = evaluateId == null ? null : evaluateId.trim();
    }

    /**
     * 获取评价编号
     *
     * @return evaluate_num - 评价编号
     */
    public String getEvaluateNum() {
        return evaluateNum;
    }

    /**
     * 设置评价编号
     *
     * @param evaluateNum 评价编号
     */
    public void setEvaluateNum(String evaluateNum) {
        this.evaluateNum = evaluateNum == null ? null : evaluateNum.trim();
    }

    /**
     * 获取评价类型
     *
     * @return evaluate_type - 评价类型
     */
    public String getEvaluateType() {
        return evaluateType;
    }

    /**
     * 设置评价类型
     *
     * @param evaluateType 评价类型
     */
    public void setEvaluateType(String evaluateType) {
        this.evaluateType = evaluateType == null ? null : evaluateType.trim();
    }

    /**
     * 获取评价维度（评价承运方/托运方）
     *
     * @return type - 评价维度（评价承运方/托运方）
     */
    public String getType() {
        return type;
    }

    /**
     * 设置评价维度（评价承运方/托运方）
     *
     * @param type 评价维度（评价承运方/托运方）
     */
    public void setType(String type) {
        this.type = type == null ? null : type.trim();
    }

    /**
     * 获取关联单据ID
     *
     * @return relation_bill_id - 关联单据ID
     */
    public String getRelationBillId() {
        return relationBillId;
    }

    /**
     * 设置关联单据ID
     *
     * @param relationBillId 关联单据ID
     */
    public void setRelationBillId(String relationBillId) {
        this.relationBillId = relationBillId == null ? null : relationBillId.trim();
    }

    /**
     * 获取关联单据编号
     *
     * @return relation_bill_num - 关联单据编号
     */
    public String getRelationBillNum() {
        return relationBillNum;
    }

    /**
     * 设置关联单据编号
     *
     * @param relationBillNum 关联单据编号
     */
    public void setRelationBillNum(String relationBillNum) {
        this.relationBillNum = relationBillNum == null ? null : relationBillNum.trim();
    }

    /**
     * 获取关联单据类型
     *
     * @return relation_bill_type - 关联单据类型
     */
    public String getRelationBillType() {
        return relationBillType;
    }

    /**
     * 设置关联单据类型
     *
     * @param relationBillType 关联单据类型
     */
    public void setRelationBillType(String relationBillType) {
        this.relationBillType = relationBillType == null ? null : relationBillType.trim();
    }

    /**
     * 获取被评价主体ID
     *
     * @return evaluated_person_id - 被评价主体ID
     */
    public String getEvaluatedPersonId() {
        return evaluatedPersonId;
    }

    /**
     * 设置被评价主体ID
     *
     * @param evaluatedPersonId 被评价主体ID
     */
    public void setEvaluatedPersonId(String evaluatedPersonId) {
        this.evaluatedPersonId = evaluatedPersonId == null ? null : evaluatedPersonId.trim();
    }

    /**
     * 获取被评价主体名称
     *
     * @return evaluated_person_name - 被评价主体名称
     */
    public String getEvaluatedPersonName() {
        return evaluatedPersonName;
    }

    /**
     * 设置被评价主体名称
     *
     * @param evaluatedPersonName 被评价主体名称
     */
    public void setEvaluatedPersonName(String evaluatedPersonName) {
        this.evaluatedPersonName = evaluatedPersonName == null ? null : evaluatedPersonName.trim();
    }

    /**
     * 获取被评价主体类型
     *
     * @return evaluated_person_type - 被评价主体类型
     */
    public String getEvaluatedPersonType() {
        return evaluatedPersonType;
    }

    /**
     * 设置被评价主体类型
     *
     * @param evaluatedPersonType 被评价主体类型
     */
    public void setEvaluatedPersonType(String evaluatedPersonType) {
        this.evaluatedPersonType = evaluatedPersonType == null ? null : evaluatedPersonType.trim();
    }

    /**
     * 获取评价人ID
     *
     * @return evaluator_id - 评价人ID
     */
    public String getEvaluatorId() {
        return evaluatorId;
    }

    /**
     * 设置评价人ID
     *
     * @param evaluatorId 评价人ID
     */
    public void setEvaluatorId(String evaluatorId) {
        this.evaluatorId = evaluatorId == null ? null : evaluatorId.trim();
    }

    /**
     * 获取评价人名称
     *
     * @return evaluator_name - 评价人名称
     */
    public String getEvaluatorName() {
        return evaluatorName;
    }

    /**
     * 设置评价人名称
     *
     * @param evaluatorName 评价人名称
     */
    public void setEvaluatorName(String evaluatorName) {
        this.evaluatorName = evaluatorName == null ? null : evaluatorName.trim();
    }

    /**
     * 获取评价人类型
     *
     * @return evaluator_type - 评价人类型
     */
    public String getEvaluatorType() {
        return evaluatorType;
    }

    /**
     * 设置评价人类型
     *
     * @param evaluatorType 评价人类型
     */
    public void setEvaluatorType(String evaluatorType) {
        this.evaluatorType = evaluatorType == null ? null : evaluatorType.trim();
    }

    /**
     * 获取运输效率评分
     *
     * @return tc_score - 运输效率评分
     */
    public Byte getTcScore() {
        return tcScore;
    }

    /**
     * 设置运输效率评分
     *
     * @param tcScore 运输效率评分
     */
    public void setTcScore(Byte tcScore) {
        this.tcScore = tcScore;
    }

    /**
     * 获取运输安全评分
     *
     * @return ts_score - 运输安全评分
     */
    public Byte getTsScore() {
        return tsScore;
    }

    /**
     * 设置运输安全评分
     *
     * @param tsScore 运输安全评分
     */
    public void setTsScore(Byte tsScore) {
        this.tsScore = tsScore;
    }

    /**
     * 获取服务质量评分
     *
     * @return sq_score - 服务质量评分
     */
    public Byte getSqScore() {
        return sqScore;
    }

    /**
     * 设置服务质量评分
     *
     * @param sqScore 服务质量评分
     */
    public void setSqScore(Byte sqScore) {
        this.sqScore = sqScore;
    }

    /**
     * 获取客户满意度评分
     *
     * @return cs_score - 客户满意度评分
     */
    public Byte getCsScore() {
        return csScore;
    }

    /**
     * 设置客户满意度评分
     *
     * @param csScore 客户满意度评分
     */
    public void setCsScore(Byte csScore) {
        this.csScore = csScore;
    }

    /**
     * 获取托运方评分
     *
     * @return shipper_score - 托运方评分
     */
    public Byte getShipperScore() {
        return shipperScore;
    }

    /**
     * 设置托运方评分
     *
     * @param shipperScore 托运方评分
     */
    public void setShipperScore(Byte shipperScore) {
        this.shipperScore = shipperScore;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取删除标识
     *
     * @return del_flg - 删除标识
     */
    public Byte getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标识
     *
     * @param delFlg 删除标识
     */
    public void setDelFlg(Byte delFlg) {
        this.delFlg = delFlg;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", evaluateId=").append(evaluateId);
        sb.append(", evaluateNum=").append(evaluateNum);
        sb.append(", evaluateType=").append(evaluateType);
        sb.append(", type=").append(type);
        sb.append(", relationBillId=").append(relationBillId);
        sb.append(", relationBillNum=").append(relationBillNum);
        sb.append(", relationBillType=").append(relationBillType);
        sb.append(", evaluatedPersonId=").append(evaluatedPersonId);
        sb.append(", evaluatedPersonName=").append(evaluatedPersonName);
        sb.append(", evaluatedPersonType=").append(evaluatedPersonType);
        sb.append(", evaluatorId=").append(evaluatorId);
        sb.append(", evaluatorName=").append(evaluatorName);
        sb.append(", evaluatorType=").append(evaluatorType);
        sb.append(", tcScore=").append(tcScore);
        sb.append(", tsScore=").append(tsScore);
        sb.append(", sqScore=").append(sqScore);
        sb.append(", csScore=").append(csScore);
        sb.append(", shipperScore=").append(shipperScore);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}