package com.ecommerce.logistics.dao.vo;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

@Table(name = "lgs_bind_captain_log")
public class BindCaptainLog implements Serializable {
    /**
     * 绑定船长记录主键
     */
    @Id
    @Column(name = "bind_captain_id")
    private String bindCaptainId;

    /**
     * 船舶主键ID
     */
    @Column(name = "shipping_id")
    private String shippingId;

    /**
     * 船长账号
     */
    @Column(name = "captain_account_name")
    private String captainAccountName;

    /**
     * 船长姓名
     */
    @Column(name = "captain_name")
    private String captainName;

    /**
     * 船长联系电话
     */
    @Column(name = "captain_phone")
    private String captainPhone;

    /**
     * 船舶名称
     */
    @Column(name = "shipping_name")
    private String shippingName;

    /**
     * 船舶承运商名称
     */
    @Column(name = "manager_member_name")
    private String managerMemberName;

    /**
     * 类型,0-解绑，1-绑定
     */
    @Column(name = "type")
    private Integer type;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;

    /**
     * 获取绑定船长记录主键
     *
     * @return bind_captain_id - 绑定船长记录主键
     */
    public String getBindCaptainId() {
        return bindCaptainId;
    }

    /**
     * 设置绑定船长记录主键
     *
     * @param bindCaptainId 绑定船长记录主键
     */
    public void setBindCaptainId(String bindCaptainId) {
        this.bindCaptainId = bindCaptainId == null ? null : bindCaptainId.trim();
    }

    /**
     * 获取船舶主键ID
     *
     * @return shipping_id - 船舶主键ID
     */
    public String getShippingId() {
        return shippingId;
    }

    /**
     * 设置船舶主键ID
     *
     * @param shippingId 船舶主键ID
     */
    public void setShippingId(String shippingId) {
        this.shippingId = shippingId == null ? null : shippingId.trim();
    }

    /**
     * 获取船长账号
     *
     * @return captainAccountName - 船长账号
     */
    public String getCaptainAccountName() {
        return captainAccountName;
    }

    /**
     * 设置船长账号
     *
     * @param captainAccountName 船长账号
     */
    public void setCaptainAccountName(String captainAccountName) {
        this.captainAccountName = captainAccountName == null ? null : captainAccountName.trim();
    }

    /**
     * 获取船长姓名
     *
     * @return captain_name - 船长姓名
     */
    public String getCaptainName() {
        return captainName;
    }

    /**
     * 设置船长姓名
     *
     * @param captainName 船长姓名
     */
    public void setCaptainName(String captainName) {
        this.captainName = captainName == null ? null : captainName.trim();
    }

    /**
     * 获取船长联系电话
     *
     * @return captain_phone - 船长联系电话
     */
    public String getCaptainPhone() {
        return captainPhone;
    }

    /**
     * 设置船长联系电话
     *
     * @param captainPhone 船长联系电话
     */
    public void setCaptainPhone(String captainPhone) {
        this.captainPhone = captainPhone == null ? null : captainPhone.trim();
    }

    /**
     * 获取船舶名称
     *
     * @return shipping_name - 船舶名称
     */
    public String getShippingName() {
        return shippingName;
    }

    /**
     * 设置船舶名称
     *
     * @param shippingName 船舶名称
     */
    public void setShippingName(String shippingName) {
        this.shippingName = shippingName == null ? null : shippingName.trim();
    }

    /**
     * 获取船舶承运商名称
     *
     * @return manager_member_name - 船舶承运商名称
     */
    public String getManagerMemberName() {
        return managerMemberName;
    }

    /**
     * 设置船舶承运商名称
     *
     * @param managerMemberName 船舶承运商名称
     */
    public void setManagerMemberName(String managerMemberName) {
        this.managerMemberName = managerMemberName == null ? null : managerMemberName.trim();
    }

    /**
     * 获取类型,0-解绑，1-绑定
     *
     * @return type - 类型,0-解绑，1-绑定
     */
    public Integer getType() {
        return type;
    }

    /**
     * 设置类型,0-解绑，1-绑定
     *
     * @param type 类型,0-解绑，1-绑定
     */
    public void setType(Integer type) {
        this.type = type;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", bindCaptainId=").append(bindCaptainId);
        sb.append(", shippingId=").append(shippingId);
        sb.append(", captainAccountName=").append(captainAccountName);
        sb.append(", captainName=").append(captainName);
        sb.append(", captainPhone=").append(captainPhone);
        sb.append(", shippingName=").append(shippingName);
        sb.append(", managerMemberName=").append(managerMemberName);
        sb.append(", type=").append(type);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}