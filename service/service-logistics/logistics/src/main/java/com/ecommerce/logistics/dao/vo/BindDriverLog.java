package com.ecommerce.logistics.dao.vo;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

@Table(name = "lgs_bind_driver_log")
public class BindDriverLog implements Serializable {
    /**
     * 绑定船长记录主键
     */
    @Id
    @Column(name = "bind_driver_id")
    private String bindDriverId;

    /**
     * 车辆主键ID
     */
    @Column(name = "vehicle_id")
    private String vehicleId;

    /**
     * 司机账号
     */
    @Column(name = "driver_account_name")
    private String driverAccountName;

    /**
     * 司机姓名
     */
    @Column(name = "driver_name")
    private String driverName;

    /**
     * 司机联系电话
     */
    @Column(name = "driver_phone")
    private String driverPhone;

    /**
     * 车辆承运商名称
     */
    @Column(name = "driver_member_name")
    private String driverMemberName;

    /**
     * 类型,0-解绑，1-绑定
     */
    private Integer type;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;

    /**
     * 获取绑定船长记录主键
     *
     * @return bind_driver_id - 绑定船长记录主键
     */
    public String getBindDriverId() {
        return bindDriverId;
    }

    /**
     * 设置绑定船长记录主键
     *
     * @param bindDriverId 绑定船长记录主键
     */
    public void setBindDriverId(String bindDriverId) {
        this.bindDriverId = bindDriverId == null ? null : bindDriverId.trim();
    }

    /**
     * 获取车辆主键ID
     *
     * @return vehicle_id - 车辆主键ID
     */
    public String getVehicleId() {
        return vehicleId;
    }

    /**
     * 设置车辆主键ID
     *
     * @param vehicleId 车辆主键ID
     */
    public void setVehicleId(String vehicleId) {
        this.vehicleId = vehicleId == null ? null : vehicleId.trim();
    }

    /**
     * 获取司机账号
     *
     * @return driver_account_name - 司机账号
     */
    public String getDriverAccountName() {
        return driverAccountName;
    }

    /**
     * 设置司机账号
     *
     * @param driverAccountName 司机账号
     */
    public void setDriverAccountName(String driverAccountName) {
        this.driverAccountName = driverAccountName == null ? null : driverAccountName.trim();
    }

    /**
     * 获取司机姓名
     *
     * @return driver_name - 司机姓名
     */
    public String getDriverName() {
        return driverName;
    }

    /**
     * 设置司机姓名
     *
     * @param driverName 司机姓名
     */
    public void setDriverName(String driverName) {
        this.driverName = driverName == null ? null : driverName.trim();
    }

    /**
     * 获取司机联系电话
     *
     * @return driver_phone - 司机联系电话
     */
    public String getDriverPhone() {
        return driverPhone;
    }

    /**
     * 设置司机联系电话
     *
     * @param driverPhone 司机联系电话
     */
    public void setDriverPhone(String driverPhone) {
        this.driverPhone = driverPhone == null ? null : driverPhone.trim();
    }

    /**
     * 获取车辆承运商名称
     *
     * @return driver_member_name - 车辆承运商名称
     */
    public String getDriverMemberName() {
        return driverMemberName;
    }

    /**
     * 设置车辆承运商名称
     *
     * @param driverMemberName 车辆承运商名称
     */
    public void setDriverMemberName(String driverMemberName) {
        this.driverMemberName = driverMemberName == null ? null : driverMemberName.trim();
    }

    /**
     * 获取类型,0-解绑，1-绑定
     *
     * @return type - 类型,0-解绑，1-绑定
     */
    public Integer getType() {
        return type;
    }

    /**
     * 设置类型,0-解绑，1-绑定
     *
     * @param type 类型,0-解绑，1-绑定
     */
    public void setType(Integer type) {
        this.type = type;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", bindDriverId=").append(bindDriverId);
        sb.append(", vehicleId=").append(vehicleId);
        sb.append(", driverAccountName=").append(driverAccountName);
        sb.append(", driverName=").append(driverName);
        sb.append(", driverPhone=").append(driverPhone);
        sb.append(", driverMemberName=").append(driverMemberName);
        sb.append(", type=").append(type);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}