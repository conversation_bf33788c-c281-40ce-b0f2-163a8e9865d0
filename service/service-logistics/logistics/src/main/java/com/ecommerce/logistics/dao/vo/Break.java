package com.ecommerce.logistics.dao.vo;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Table(name = "lgs_break")
public class Break implements Serializable {
    /**
     * 违约ID
     */
    @Id
    @Column(name = "break_id")
    private String breakId;

    /**
     * 违约编号
     */
    @Column(name = "break_num")
    private String breakNum;

    /**
     * 关联单据类型枚举[1.提货单2.调度单3.运单]
     */
    @Column(name = "relation_bill_type")
    private String relationBillType;

    @Column(name = "relation_bill_id")
    private String relationBillId;

    /**
     * 关联单据号
     */
    @Column(name = "relation_bill_num")
    private String relationBillNum;

    /**
     * 违约发起方类型枚举
     */
    @Column(name = "break_init_role_type")
    private String breakInitRoleType;

    /**
     * 违约发起方名称
     */
    @Column(name = "break_init_role_name")
    private String breakInitRoleName;

    /**
     * 违约发起方ID
     */
    @Column(name = "break_init_role_id")
    private String breakInitRoleId;

    /**
     * 违约说明
     */
    @Column(name = "break_content")
    private String breakContent;

    /**
     * 配送数量
     */
    @Column(name = "delivery_quantity")
    private String deliveryQuantity;

    /**
     * 违约数量
     */
    @Column(name = "break_quantity")
    private String breakQuantity;

    /**
     * 【创建时间】
     */
    @Column(name = "propose_time")
    private Date proposeTime;

    /**
     * 处理完成人名称
     */
    @Column(name = "deal_finish_user_name")
    private String dealFinishUserName;

    /**
     * 处理完成人ID
     */
    @Column(name = "deal_finish_user_id")
    private String dealFinishUserId;

    /**
     * 处理完结时间
     */
    @Column(name = "deal_finish_time")
    private Date dealFinishTime;

    /**
     * 处理状态[申请中 已同意 已拒绝]
     */
    @Column(name = "deal_status")
    private String dealStatus;

    /**
     * 责任归属方名称
     */
    @Column(name = "responsible_role_name")
    private String responsibleRoleName;

    /**
     * 责任归属方ID
     */
    @Column(name = "responsible_role_id")
    private String responsibleRoleId;

    /**
     * 责任归属类型枚举
     */
    @Column(name = "responsible_role_type")
    private String responsibleRoleType;

    /**
     * 责任归属备注
     */
    @Column(name = "responsible_content")
    private String responsibleContent;

    /**
     * 违约金金额
     */
    @Column(name = "break_amount")
    private BigDecimal breakAmount;

    /**
     * 处理结果说明[已同意状态下为备注，已拒绝状态下为拒绝理由]
     */
    @Column(name = "deal_result")
    private String dealResult;

    /**
     * 是否有违约金【-1:否 1:是】
     */
    @Column(name = "break_flag")
    private Integer breakFlag;

    /**
     * 删除标识
     */
    @Column(name = "del_flg")
    private Integer delFlg;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 版本号
     */
    private Long version;

    private static final long serialVersionUID = 1L;

    public String getRelationBillId() {
        return relationBillId;
    }

    public void setRelationBillId(String relationBillId) {
        this.relationBillId = relationBillId;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    /**
     * 获取违约ID
     *
     * @return break_id - 违约ID
     */
    public String getBreakId() {
        return breakId;
    }

    /**
     * 设置违约ID
     *
     * @param breakId 违约ID
     */
    public void setBreakId(String breakId) {
        this.breakId = breakId == null ? null : breakId.trim();
    }

    /**
     * 获取违约编号
     *
     * @return break_num - 违约编号
     */
    public String getBreakNum() {
        return breakNum;
    }

    /**
     * 设置违约编号
     *
     * @param breakNum 违约编号
     */
    public void setBreakNum(String breakNum) {
        this.breakNum = breakNum == null ? null : breakNum.trim();
    }

    /**
     * 获取关联单据类型枚举[1.提货单2.调度单3.运单]
     *
     * @return relation_bill_type - 关联单据类型枚举[1.提货单2.调度单3.运单]
     */
    public String getRelationBillType() {
        return relationBillType;
    }

    /**
     * 设置关联单据类型枚举[1.提货单2.调度单3.运单]
     *
     * @param relationBillType 关联单据类型枚举[1.提货单2.调度单3.运单]
     */
    public void setRelationBillType(String relationBillType) {
        this.relationBillType = relationBillType == null ? null : relationBillType.trim();
    }

    /**
     * 获取关联单据号
     *
     * @return relation_bill_num - 关联单据号
     */
    public String getRelationBillNum() {
        return relationBillNum;
    }

    /**
     * 设置关联单据号
     *
     * @param relationBillNum 关联单据号
     */
    public void setRelationBillNum(String relationBillNum) {
        this.relationBillNum = relationBillNum == null ? null : relationBillNum.trim();
    }

    /**
     * 获取违约发起方类型枚举
     *
     * @return break_init_role_type - 违约发起方类型枚举
     */
    public String getBreakInitRoleType() {
        return breakInitRoleType;
    }

    /**
     * 设置违约发起方类型枚举
     *
     * @param breakInitRoleType 违约发起方类型枚举
     */
    public void setBreakInitRoleType(String breakInitRoleType) {
        this.breakInitRoleType = breakInitRoleType == null ? null : breakInitRoleType.trim();
    }

    /**
     * 获取违约发起方名称
     *
     * @return break_init_role_name - 违约发起方名称
     */
    public String getBreakInitRoleName() {
        return breakInitRoleName;
    }

    /**
     * 设置违约发起方名称
     *
     * @param breakInitRoleName 违约发起方名称
     */
    public void setBreakInitRoleName(String breakInitRoleName) {
        this.breakInitRoleName = breakInitRoleName == null ? null : breakInitRoleName.trim();
    }

    /**
     * 获取违约发起方ID
     *
     * @return break_init_role_id - 违约发起方ID
     */
    public String getBreakInitRoleId() {
        return breakInitRoleId;
    }

    /**
     * 设置违约发起方ID
     *
     * @param breakInitRoleId 违约发起方ID
     */
    public void setBreakInitRoleId(String breakInitRoleId) {
        this.breakInitRoleId = breakInitRoleId == null ? null : breakInitRoleId.trim();
    }

    /**
     * 获取违约说明
     *
     * @return break_content - 违约说明
     */
    public String getBreakContent() {
        return breakContent;
    }

    /**
     * 设置违约说明
     *
     * @param breakContent 违约说明
     */
    public void setBreakContent(String breakContent) {
        this.breakContent = breakContent == null ? null : breakContent.trim();
    }

    /**
     * 获取配送数量
     *
     * @return delivery_quantity - 配送数量
     */
    public String getDeliveryQuantity() {
        return deliveryQuantity;
    }

    /**
     * 设置配送数量
     *
     * @param deliveryQuantity 配送数量
     */
    public void setDeliveryQuantity(String deliveryQuantity) {
        this.deliveryQuantity = deliveryQuantity == null ? null : deliveryQuantity.trim();
    }

    /**
     * 获取违约数量
     *
     * @return break_quantity - 违约数量
     */
    public String getBreakQuantity() {
        return breakQuantity;
    }

    /**
     * 设置违约数量
     *
     * @param breakQuantity 违约数量
     */
    public void setBreakQuantity(String breakQuantity) {
        this.breakQuantity = breakQuantity == null ? null : breakQuantity.trim();
    }

    /**
     * 获取【创建时间】
     *
     * @return propose_time - 【创建时间】
     */
    public Date getProposeTime() {
        return proposeTime;
    }

    /**
     * 设置【创建时间】
     *
     * @param proposeTime 【创建时间】
     */
    public void setProposeTime(Date proposeTime) {
        this.proposeTime = proposeTime;
    }

    /**
     * 获取处理完成人名称
     *
     * @return deal_finish_user_name - 处理完成人名称
     */
    public String getDealFinishUserName() {
        return dealFinishUserName;
    }

    /**
     * 设置处理完成人名称
     *
     * @param dealFinishUserName 处理完成人名称
     */
    public void setDealFinishUserName(String dealFinishUserName) {
        this.dealFinishUserName = dealFinishUserName == null ? null : dealFinishUserName.trim();
    }

    /**
     * 获取处理完成人ID
     *
     * @return deal_finish_user_id - 处理完成人ID
     */
    public String getDealFinishUserId() {
        return dealFinishUserId;
    }

    /**
     * 设置处理完成人ID
     *
     * @param dealFinishUserId 处理完成人ID
     */
    public void setDealFinishUserId(String dealFinishUserId) {
        this.dealFinishUserId = dealFinishUserId == null ? null : dealFinishUserId.trim();
    }

    /**
     * 获取处理完结时间
     *
     * @return deal_finish_time - 处理完结时间
     */
    public Date getDealFinishTime() {
        return dealFinishTime;
    }

    /**
     * 设置处理完结时间
     *
     * @param dealFinishTime 处理完结时间
     */
    public void setDealFinishTime(Date dealFinishTime) {
        this.dealFinishTime = dealFinishTime;
    }

    /**
     * 获取处理状态[申请中 已同意 已拒绝]
     *
     * @return deal_status - 处理状态[申请中 已同意 已拒绝]
     */
    public String getDealStatus() {
        return dealStatus;
    }

    /**
     * 设置处理状态[申请中 已同意 已拒绝]
     *
     * @param dealStatus 处理状态[申请中 已同意 已拒绝]
     */
    public void setDealStatus(String dealStatus) {
        this.dealStatus = dealStatus == null ? null : dealStatus.trim();
    }

    /**
     * 获取责任归属方名称
     *
     * @return responsible_role_name - 责任归属方名称
     */
    public String getResponsibleRoleName() {
        return responsibleRoleName;
    }

    /**
     * 设置责任归属方名称
     *
     * @param responsibleRoleName 责任归属方名称
     */
    public void setResponsibleRoleName(String responsibleRoleName) {
        this.responsibleRoleName = responsibleRoleName == null ? null : responsibleRoleName.trim();
    }

    /**
     * 获取责任归属方ID
     *
     * @return responsible_role_id - 责任归属方ID
     */
    public String getResponsibleRoleId() {
        return responsibleRoleId;
    }

    /**
     * 设置责任归属方ID
     *
     * @param responsibleRoleId 责任归属方ID
     */
    public void setResponsibleRoleId(String responsibleRoleId) {
        this.responsibleRoleId = responsibleRoleId == null ? null : responsibleRoleId.trim();
    }

    /**
     * 获取责任归属类型枚举
     *
     * @return responsible_role_type - 责任归属类型枚举
     */
    public String getResponsibleRoleType() {
        return responsibleRoleType;
    }

    /**
     * 设置责任归属类型枚举
     *
     * @param responsibleRoleType 责任归属类型枚举
     */
    public void setResponsibleRoleType(String responsibleRoleType) {
        this.responsibleRoleType = responsibleRoleType == null ? null : responsibleRoleType.trim();
    }

    /**
     * 获取责任归属备注
     *
     * @return responsible_content - 责任归属备注
     */
    public String getResponsibleContent() {
        return responsibleContent;
    }

    /**
     * 设置责任归属备注
     *
     * @param responsibleContent 责任归属备注
     */
    public void setResponsibleContent(String responsibleContent) {
        this.responsibleContent = responsibleContent == null ? null : responsibleContent.trim();
    }

    /**
     * 获取违约金金额
     *
     * @return break_amount - 违约金金额
     */
    public BigDecimal getBreakAmount() {
        return breakAmount;
    }

    /**
     * 设置违约金金额
     *
     * @param breakAmount 违约金金额
     */
    public void setBreakAmount(BigDecimal breakAmount) {
        this.breakAmount = breakAmount;
    }

    /**
     * 获取处理结果说明[已同意状态下为备注，已拒绝状态下为拒绝理由]
     *
     * @return deal_result - 处理结果说明[已同意状态下为备注，已拒绝状态下为拒绝理由]
     */
    public String getDealResult() {
        return dealResult;
    }

    /**
     * 设置处理结果说明[已同意状态下为备注，已拒绝状态下为拒绝理由]
     *
     * @param dealResult 处理结果说明[已同意状态下为备注，已拒绝状态下为拒绝理由]
     */
    public void setDealResult(String dealResult) {
        this.dealResult = dealResult == null ? null : dealResult.trim();
    }

    /**
     * 获取是否有违约金【-1:否 1:是】
     *
     * @return break_flag - 是否有违约金【-1:否 1:是】
     */
    public Integer getBreakFlag() {
        return breakFlag;
    }

    /**
     * 设置是否有违约金【-1:否 1:是】
     *
     * @param breakFlag 是否有违约金【-1:否 1:是】
     */
    public void setBreakFlag(Integer breakFlag) {
        this.breakFlag = breakFlag;
    }

    /**
     * 获取删除标识
     *
     * @return del_flg - 删除标识
     */
    public Integer getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标识
     *
     * @param delFlg 删除标识
     */
    public void setDelFlg(Integer delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取版本号
     *
     * @return version - 版本号
     */
    public Long getVersion() {
        return version;
    }

    /**
     * 设置版本号
     *
     * @param version 版本号
     */
    public void setVersion(Long version) {
        this.version = version;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", breakId=").append(breakId);
        sb.append(", breakNum=").append(breakNum);
        sb.append(", relationBillType=").append(relationBillType);
        sb.append(", relationBillNum=").append(relationBillNum);
        sb.append(", breakInitRoleType=").append(breakInitRoleType);
        sb.append(", breakInitRoleName=").append(breakInitRoleName);
        sb.append(", breakInitRoleId=").append(breakInitRoleId);
        sb.append(", breakContent=").append(breakContent);
        sb.append(", deliveryQuantity=").append(deliveryQuantity);
        sb.append(", breakQuantity=").append(breakQuantity);
        sb.append(", proposeTime=").append(proposeTime);
        sb.append(", dealFinishUserName=").append(dealFinishUserName);
        sb.append(", dealFinishUserId=").append(dealFinishUserId);
        sb.append(", dealFinishTime=").append(dealFinishTime);
        sb.append(", dealStatus=").append(dealStatus);
        sb.append(", responsibleRoleName=").append(responsibleRoleName);
        sb.append(", responsibleRoleId=").append(responsibleRoleId);
        sb.append(", responsibleRoleType=").append(responsibleRoleType);
        sb.append(", responsibleContent=").append(responsibleContent);
        sb.append(", breakAmount=").append(breakAmount);
        sb.append(", dealResult=").append(dealResult);
        sb.append(", breakFlag=").append(breakFlag);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", version=").append(version);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}