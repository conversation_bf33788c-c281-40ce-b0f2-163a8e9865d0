package com.ecommerce.logistics.dao.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;

@Table(name = "lgs_buyer_carriage_rule")
public class BuyerCarriageRule implements Serializable {
    @Id
    @Column(name = "carriage_rule_id")
    private String carriageRuleId;

    /**
     * 品类名称
     */
    @Column(name = "warehouse_id")
    private String warehouseId;

    /**
     * 是否可整合运输
     */
    @Column(name = "transport_category_id")
    private String transportCategoryId;

    /**
     * 删除标识
     */
    @Column(name = "vehicle_type_id")
    private String vehicleTypeId;

    /**
     * 运输单价
     */
    @Column(name = "transport_price")
    private BigDecimal transportPrice;

    /**
     * 装卸单价
     */
    @Column(name = "carry_price")
    private BigDecimal carryPrice;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 数据版本
     */
    private Long version;

    @Column(name = "del_flg")
    private Byte delFlg;

    private static final long serialVersionUID = 1L;

    /**
     * @return carriage_rule_id
     */
    public String getCarriageRuleId() {
        return carriageRuleId;
    }

    /**
     * @param carriageRuleId
     */
    public void setCarriageRuleId(String carriageRuleId) {
        this.carriageRuleId = carriageRuleId == null ? null : carriageRuleId.trim();
    }

    /**
     * 获取品类名称
     *
     * @return warehouse_id - 品类名称
     */
    public String getWarehouseId() {
        return warehouseId;
    }

    /**
     * 设置品类名称
     *
     * @param warehouseId 品类名称
     */
    public void setWarehouseId(String warehouseId) {
        this.warehouseId = warehouseId == null ? null : warehouseId.trim();
    }

    /**
     * 获取是否可整合运输
     *
     * @return transport_category_id - 是否可整合运输
     */
    public String getTransportCategoryId() {
        return transportCategoryId;
    }

    /**
     * 设置是否可整合运输
     *
     * @param transportCategoryId 是否可整合运输
     */
    public void setTransportCategoryId(String transportCategoryId) {
        this.transportCategoryId = transportCategoryId == null ? null : transportCategoryId.trim();
    }

    /**
     * 获取删除标识
     *
     * @return vehicle_type_id - 删除标识
     */
    public String getVehicleTypeId() {
        return vehicleTypeId;
    }

    /**
     * 设置删除标识
     *
     * @param vehicleTypeId 删除标识
     */
    public void setVehicleTypeId(String vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId == null ? null : vehicleTypeId.trim();
    }

    /**
     * 获取运输单价
     *
     * @return transport_price - 运输单价
     */
    public BigDecimal getTransportPrice() {
        return transportPrice;
    }

    /**
     * 设置运输单价
     *
     * @param transportPrice 运输单价
     */
    public void setTransportPrice(BigDecimal transportPrice) {
        this.transportPrice = transportPrice;
    }

    /**
     * 获取装卸单价
     *
     * @return carry_price - 装卸单价
     */
    public BigDecimal getCarryPrice() {
        return carryPrice;
    }

    /**
     * 设置装卸单价
     *
     * @param carryPrice 装卸单价
     */
    public void setCarryPrice(BigDecimal carryPrice) {
        this.carryPrice = carryPrice;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取数据版本
     *
     * @return version - 数据版本
     */
    public Long getVersion() {
        return version;
    }

    /**
     * 设置数据版本
     *
     * @param version 数据版本
     */
    public void setVersion(Long version) {
        this.version = version;
    }

    /**
     * @return del_flg
     */
    public Byte getDelFlg() {
        return delFlg;
    }

    /**
     * @param delFlg
     */
    public void setDelFlg(Byte delFlg) {
        this.delFlg = delFlg;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", carriageRuleId=").append(carriageRuleId);
        sb.append(", warehouseId=").append(warehouseId);
        sb.append(", transportCategoryId=").append(transportCategoryId);
        sb.append(", vehicleTypeId=").append(vehicleTypeId);
        sb.append(", transportPrice=").append(transportPrice);
        sb.append(", carryPrice=").append(carryPrice);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", version=").append(version);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}