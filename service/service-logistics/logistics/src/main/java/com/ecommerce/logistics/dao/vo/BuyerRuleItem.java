package com.ecommerce.logistics.dao.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;

@Table(name = "lgs_buyer_carriage_item")
public class BuyerRuleItem implements Serializable {
    /**
     * 省份
     */
    @Id
    @Column(name = "carriage_rule_id")
    private String carriageRuleId;

    /**
     * 运费规则id
     */
    @Column(name = "carriage_item_id")
    private String carriageItemId;

    /**
     * 省份
     */
    private String province;

    /**
     * 省份编码
     */
    @Column(name = "province_code")
    private String provinceCode;

    /**
     * 城市
     */
    private String city;

    /**
     * 城市编码
     */
    @Column(name = "city_code")
    private String cityCode;

    /**
     * 区域
     */
    private String district;

    /**
     * 地区编码
     */
    @Column(name = "district_code")
    private String districtCode;

    /**
     * 运输单价
     */
    @Column(name = "transport_price")
    private BigDecimal transportPrice;

    /**
     * 装卸单价
     */
    @Column(name = "carry_price")
    private BigDecimal carryPrice;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 数据版本
     */
    private Long version;

    /**
     * del_flg
     */
    @Column(name = "del_flg")
    private Byte delFlg;

    private static final long serialVersionUID = 1L;

    /**
     * 获取省份
     *
     * @return carriage_rule_id - 省份
     */
    public String getCarriageRuleId() {
        return carriageRuleId;
    }

    /**
     * 设置省份
     *
     * @param carriageRuleId 省份
     */
    public void setCarriageRuleId(String carriageRuleId) {
        this.carriageRuleId = carriageRuleId == null ? null : carriageRuleId.trim();
    }

    /**
     * 获取运费规则id
     *
     * @return carriage_item_id - 运费规则id
     */
    public String getCarriageItemId() {
        return carriageItemId;
    }

    /**
     * 设置运费规则id
     *
     * @param carriageItemId 运费规则id
     */
    public void setCarriageItemId(String carriageItemId) {
        this.carriageItemId = carriageItemId == null ? null : carriageItemId.trim();
    }


    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    /**
     * 获取省份编码
     *
     * @return province_code - 省份编码
     */
    public String getProvinceCode() {
        return provinceCode;
    }

    /**
     * 设置省份编码
     *
     * @param provinceCode 省份编码
     */
    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode == null ? null : provinceCode.trim();
    }

    /**
     * 获取城市
     *
     * @return city - 城市
     */
    public String getCity() {
        return city;
    }

    /**
     * 设置城市
     *
     * @param city 城市
     */
    public void setCity(String city) {
        this.city = city == null ? null : city.trim();
    }

    /**
     * 获取城市编码
     *
     * @return city_code - 城市编码
     */
    public String getCityCode() {
        return cityCode;
    }

    /**
     * 设置城市编码
     *
     * @param cityCode 城市编码
     */
    public void setCityCode(String cityCode) {
        this.cityCode = cityCode == null ? null : cityCode.trim();
    }

    /**
     * 获取区域
     *
     * @return district - 区域
     */
    public String getDistrict() {
        return district;
    }

    /**
     * 设置区域
     *
     * @param district 区域
     */
    public void setDistrict(String district) {
        this.district = district == null ? null : district.trim();
    }

    /**
     * 获取地区编码
     *
     * @return district_code - 地区编码
     */
    public String getDistrictCode() {
        return districtCode;
    }

    /**
     * 设置地区编码
     *
     * @param districtCode 地区编码
     */
    public void setDistrictCode(String districtCode) {
        this.districtCode = districtCode == null ? null : districtCode.trim();
    }

    /**
     * 获取运输单价
     *
     * @return transport_price - 运输单价
     */
    public BigDecimal getTransportPrice() {
        return transportPrice;
    }

    /**
     * 设置运输单价
     *
     * @param transportPrice 运输单价
     */
    public void setTransportPrice(BigDecimal transportPrice) {
        this.transportPrice = transportPrice;
    }

    /**
     * 获取装卸单价
     *
     * @return carry_price - 装卸单价
     */
    public BigDecimal getCarryPrice() {
        return carryPrice;
    }

    /**
     * 设置装卸单价
     *
     * @param carryPrice 装卸单价
     */
    public void setCarryPrice(BigDecimal carryPrice) {
        this.carryPrice = carryPrice;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取数据版本
     *
     * @return version - 数据版本
     */
    public Long getVersion() {
        return version;
    }

    /**
     * 设置数据版本
     *
     * @param version 数据版本
     */
    public void setVersion(Long version) {
        this.version = version;
    }

    /**
     * 获取del_flg
     *
     * @return del_flg - del_flg
     */
    public Byte getDelFlg() {
        return delFlg;
    }

    /**
     * 设置del_flg
     *
     * @param delFlg del_flg
     */
    public void setDelFlg(Byte delFlg) {
        this.delFlg = delFlg;
    }

    @Override
    public String toString() {
        return "BuyerRuleItem{" +
                "carriageRuleId='" + carriageRuleId + '\'' +
                ", carriageItemId='" + carriageItemId + '\'' +
                ", province='" + province + '\'' +
                ", provinceCode='" + provinceCode + '\'' +
                ", city='" + city + '\'' +
                ", cityCode='" + cityCode + '\'' +
                ", district='" + district + '\'' +
                ", districtCode='" + districtCode + '\'' +
                ", transportPrice=" + transportPrice +
                ", carryPrice=" + carryPrice +
                ", createUser='" + createUser + '\'' +
                ", updateUser='" + updateUser + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", version=" + version +
                ", delFlg=" + delFlg +
                '}';
    }
}