package com.ecommerce.logistics.dao.vo;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

@Table(name = "lgs_carriage_log")
public class CarriageLog implements Serializable {
    /**
     * 运费规则日志ID
     */
    @Id
    @Column(name = "carriage_log_id")
    private String carriageLogId;

    /**
     * 运费路线ID
     */
    @Column(name = "carriage_route_id")
    private String carriageRouteId;

    /**
     * 结算类型 1收费 2付费 3委托
     */
    @Column(name = "settlement_type")
    private String settlementType;

    /**
     * 附件url集合
     */
    @Column(name = "attachment_url")
    private String attachmentUrl;

    /**
     * 类别 添加,修改,删除
     */
    @Column(name = "category_type")
    private String categoryType;

    /**
     * 操作人ID
     */
    @Column(name = "operator_id")
    private String operatorId;

    /**
     * 操作人姓名
     */
    @Column(name = "operator_name")
    private String operatorName;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * del_flg
     */
    @Column(name = "del_flg")
    private Boolean delFlg;

    /**
     * 前置规则
     */
    @Column(name = "pre_rule")
    private String preRule;

    /**
     * 变更后规则
     */
    @Column(name = "next_rule")
    private String nextRule;

    private static final long serialVersionUID = 1L;

    /**
     * 获取运费规则日志ID
     *
     * @return carriage_log_id - 运费规则日志ID
     */
    public String getCarriageLogId() {
        return carriageLogId;
    }

    /**
     * 设置运费规则日志ID
     *
     * @param carriageLogId 运费规则日志ID
     */
    public void setCarriageLogId(String carriageLogId) {
        this.carriageLogId = carriageLogId == null ? null : carriageLogId.trim();
    }

    /**
     * 获取运费路线ID
     *
     * @return carriage_route_id - 运费路线ID
     */
    public String getCarriageRouteId() {
        return carriageRouteId;
    }

    /**
     * 设置运费路线ID
     *
     * @param carriageRouteId 运费路线ID
     */
    public void setCarriageRouteId(String carriageRouteId) {
        this.carriageRouteId = carriageRouteId == null ? null : carriageRouteId.trim();
    }

    /**
     * 获取结算类型 1收费 2付费 3委托
     *
     * @return settlement_type - 结算类型 1收费 2付费 3委托
     */
    public String getSettlementType() {
        return settlementType;
    }

    /**
     * 设置结算类型 1收费 2付费 3委托
     *
     * @param settlementType 结算类型 1收费 2付费 3委托
     */
    public void setSettlementType(String settlementType) {
        this.settlementType = settlementType == null ? null : settlementType.trim();
    }

    /**
     * 获取附件url集合
     *
     * @return attachment_url - 附件url集合
     */
    public String getAttachmentUrl() {
        return attachmentUrl;
    }

    /**
     * 设置附件url集合
     *
     * @param attachmentUrl 附件url集合
     */
    public void setAttachmentUrl(String attachmentUrl) {
        this.attachmentUrl = attachmentUrl == null ? null : attachmentUrl.trim();
    }

    /**
     * 获取类别 添加,修改,删除
     *
     * @return category_type - 类别 添加,修改,删除
     */
    public String getCategoryType() {
        return categoryType;
    }

    /**
     * 设置类别 添加,修改,删除
     *
     * @param categoryType 类别 添加,修改,删除
     */
    public void setCategoryType(String categoryType) {
        this.categoryType = categoryType == null ? null : categoryType.trim();
    }

    /**
     * 获取操作人ID
     *
     * @return operator_id - 操作人ID
     */
    public String getOperatorId() {
        return operatorId;
    }

    /**
     * 设置操作人ID
     *
     * @param operatorId 操作人ID
     */
    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId == null ? null : operatorId.trim();
    }

    /**
     * 获取操作人姓名
     *
     * @return operator_name - 操作人姓名
     */
    public String getOperatorName() {
        return operatorName;
    }

    /**
     * 设置操作人姓名
     *
     * @param operatorName 操作人姓名
     */
    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName == null ? null : operatorName.trim();
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取del_flg
     *
     * @return del_flg - del_flg
     */
    public Boolean getDelFlg() {
        return delFlg;
    }

    /**
     * 设置del_flg
     *
     * @param delFlg del_flg
     */
    public void setDelFlg(Boolean delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取前置规则
     *
     * @return pre_rule - 前置规则
     */
    public String getPreRule() {
        return preRule;
    }

    /**
     * 设置前置规则
     *
     * @param preRule 前置规则
     */
    public void setPreRule(String preRule) {
        this.preRule = preRule == null ? null : preRule.trim();
    }

    /**
     * 获取变更后规则
     *
     * @return next_rule - 变更后规则
     */
    public String getNextRule() {
        return nextRule;
    }

    /**
     * 设置变更后规则
     *
     * @param nextRule 变更后规则
     */
    public void setNextRule(String nextRule) {
        this.nextRule = nextRule == null ? null : nextRule.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", carriageLogId=").append(carriageLogId);
        sb.append(", carriageRouteId=").append(carriageRouteId);
        sb.append(", settlementType=").append(settlementType);
        sb.append(", attachmentUrl=").append(attachmentUrl);
        sb.append(", categoryType=").append(categoryType);
        sb.append(", operatorId=").append(operatorId);
        sb.append(", operatorName=").append(operatorName);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", preRule=").append(preRule);
        sb.append(", nextRule=").append(nextRule);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}