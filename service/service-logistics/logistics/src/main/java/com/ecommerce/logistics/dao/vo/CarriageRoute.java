package com.ecommerce.logistics.dao.vo;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

@Table(name = "lgs_carriage_route")
public class CarriageRoute implements Serializable {
    /**
     * 运费路线ID
     */
    @Id
    @Column(name = "carriage_route_id")
    private String carriageRouteId;

    /**
     * 路线名称
     */
    @Column(name = "route_name")
    private String routeName;

    /**
     * 路线归属的用户ID
     */
    @Column(name = "user_id")
    private String userId;

    /**
     * 路线归属用户名称
     */
    @Column(name = "user_name")
    private String userName;

    /**
     * 路线归属用户类型
     */
    @Column(name = "user_type")
    private Integer userType;

    /**
     * 定价类型 1:点对点 2:区域统一定价 3:区域公式定价
     */
    @Column(name = "pricing_type")
    private String pricingType;

    /**
     * 提货点ID
     */
    @Column(name = "warehouse_id")
    private String warehouseId;

    /**
     * 提货点名称
     */
    @Column(name = "warehouse_name")
    private String warehouseName;

    /**
     * 提货点类型
     */
    @Column(name = "warehouse_type")
    private String warehouseType;

    /**
     * 提货点省份
     */
    @Column(name = "warehouse_province")
    private String warehouseProvince;

    /**
     * 提货点省份代码
     */
    @Column(name = "warehouse_province_code")
    private String warehouseProvinceCode;

    /**
     * 提货点城市
     */
    @Column(name = "warehouse_city")
    private String warehouseCity;

    /**
     * 提货点城市代码
     */
    @Column(name = "warehouse_city_code")
    private String warehouseCityCode;

    /**
     * 提货点地区
     */
    @Column(name = "warehouse_district")
    private String warehouseDistrict;

    /**
     * 提货点地区代码
     */
    @Column(name = "warehouse_district_code")
    private String warehouseDistrictCode;

    /**
     * 提货点详细地址
     */
    @Column(name = "warehouse_address")
    private String warehouseAddress;

    /**
     * 提货点经纬度
     */
    @Column(name = "warehouse_location")
    private String warehouseLocation;

    /**
     * 收货用户ID
     */
    @Column(name = "receive_user_id")
    private String receiveUserId;

    /**
     * 收货用户名
     */
    @Column(name = "receive_user_name")
    private String receiveUserName;

    /**
     * 收获地址ID
     */
    @Column(name = "receive_address_id")
    private String receiveAddressId;

    /**
     * 提货点详细地址
     */
    @Column(name = "receive_address_detail")
    private String receiveAddressDetail;

    /**
     * 收获地址地址经纬度
     */
    @Column(name = "receive_address_location")
    private String receiveAddressLocation;

    /**
     * 运输品类ID
     */
    @Column(name = "transport_category_id")
    private String transportCategoryId;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * del_flg
     */
    @Column(name = "del_flg")
    private Boolean delFlg;

    private static final long serialVersionUID = 1L;

    /**
     * 获取运费路线ID
     *
     * @return carriage_route_id - 运费路线ID
     */
    public String getCarriageRouteId() {
        return carriageRouteId;
    }

    /**
     * 设置运费路线ID
     *
     * @param carriageRouteId 运费路线ID
     */
    public void setCarriageRouteId(String carriageRouteId) {
        this.carriageRouteId = carriageRouteId == null ? null : carriageRouteId.trim();
    }

    /**
     * 获取路线名称
     *
     * @return route_name - 路线名称
     */
    public String getRouteName() {
        return routeName;
    }

    /**
     * 设置路线名称
     *
     * @param routeName 路线名称
     */
    public void setRouteName(String routeName) {
        this.routeName = routeName == null ? null : routeName.trim();
    }

    /**
     * 获取路线归属的用户ID
     *
     * @return user_id - 路线归属的用户ID
     */
    public String getUserId() {
        return userId;
    }

    /**
     * 设置路线归属的用户ID
     *
     * @param userId 路线归属的用户ID
     */
    public void setUserId(String userId) {
        this.userId = userId == null ? null : userId.trim();
    }

    /**
     * 获取路线归属用户名称
     *
     * @return user_name - 路线归属用户名称
     */
    public String getUserName() {
        return userName;
    }

    /**
     * 设置路线归属用户名称
     *
     * @param userName 路线归属用户名称
     */
    public void setUserName(String userName) {
        this.userName = userName == null ? null : userName.trim();
    }

    /**
     * 获取路线归属用户类型
     *
     * @return user_type - 路线归属用户类型
     */
    public Integer getUserType() {
        return userType;
    }

    /**
     * 设置路线归属用户类型
     *
     * @param userType 路线归属用户类型
     */
    public void setUserType(Integer userType) {
        this.userType = userType;
    }

    /**
     * 获取定价类型 1:点对点 2:区域统一定价 3:区域公式定价
     *
     * @return pricing_type - 定价类型 1:点对点 2:区域统一定价 3:区域公式定价
     */
    public String getPricingType() {
        return pricingType;
    }

    /**
     * 设置定价类型 1:点对点 2:区域统一定价 3:区域公式定价
     *
     * @param pricingType 定价类型 1:点对点 2:区域统一定价 3:区域公式定价
     */
    public void setPricingType(String pricingType) {
        this.pricingType = pricingType == null ? null : pricingType.trim();
    }

    /**
     * 获取提货点ID
     *
     * @return warehouse_id - 提货点ID
     */
    public String getWarehouseId() {
        return warehouseId;
    }

    /**
     * 设置提货点ID
     *
     * @param warehouseId 提货点ID
     */
    public void setWarehouseId(String warehouseId) {
        this.warehouseId = warehouseId == null ? null : warehouseId.trim();
    }

    /**
     * 获取提货点名称
     *
     * @return warehouse_name - 提货点名称
     */
    public String getWarehouseName() {
        return warehouseName;
    }

    /**
     * 设置提货点名称
     *
     * @param warehouseName 提货点名称
     */
    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName == null ? null : warehouseName.trim();
    }

    /**
     * 获取提货点类型
     *
     * @return warehouse_type - 提货点类型
     */
    public String getWarehouseType() {
        return warehouseType;
    }

    /**
     * 设置提货点类型
     *
     * @param warehouseType 提货点类型
     */
    public void setWarehouseType(String warehouseType) {
        this.warehouseType = warehouseType == null ? null : warehouseType.trim();
    }

    /**
     * 获取提货点省份
     *
     * @return warehouse_province - 提货点省份
     */
    public String getWarehouseProvince() {
        return warehouseProvince;
    }

    /**
     * 设置提货点省份
     *
     * @param warehouseProvince 提货点省份
     */
    public void setWarehouseProvince(String warehouseProvince) {
        this.warehouseProvince = warehouseProvince == null ? null : warehouseProvince.trim();
    }

    /**
     * 获取提货点省份代码
     *
     * @return warehouse_province_code - 提货点省份代码
     */
    public String getWarehouseProvinceCode() {
        return warehouseProvinceCode;
    }

    /**
     * 设置提货点省份代码
     *
     * @param warehouseProvinceCode 提货点省份代码
     */
    public void setWarehouseProvinceCode(String warehouseProvinceCode) {
        this.warehouseProvinceCode = warehouseProvinceCode == null ? null : warehouseProvinceCode.trim();
    }

    /**
     * 获取提货点城市
     *
     * @return warehouse_city - 提货点城市
     */
    public String getWarehouseCity() {
        return warehouseCity;
    }

    /**
     * 设置提货点城市
     *
     * @param warehouseCity 提货点城市
     */
    public void setWarehouseCity(String warehouseCity) {
        this.warehouseCity = warehouseCity == null ? null : warehouseCity.trim();
    }

    /**
     * 获取提货点城市代码
     *
     * @return warehouse_city_code - 提货点城市代码
     */
    public String getWarehouseCityCode() {
        return warehouseCityCode;
    }

    /**
     * 设置提货点城市代码
     *
     * @param warehouseCityCode 提货点城市代码
     */
    public void setWarehouseCityCode(String warehouseCityCode) {
        this.warehouseCityCode = warehouseCityCode == null ? null : warehouseCityCode.trim();
    }

    /**
     * 获取提货点地区
     *
     * @return warehouse_district - 提货点地区
     */
    public String getWarehouseDistrict() {
        return warehouseDistrict;
    }

    /**
     * 设置提货点地区
     *
     * @param warehouseDistrict 提货点地区
     */
    public void setWarehouseDistrict(String warehouseDistrict) {
        this.warehouseDistrict = warehouseDistrict == null ? null : warehouseDistrict.trim();
    }

    /**
     * 获取提货点地区代码
     *
     * @return warehouse_district_code - 提货点地区代码
     */
    public String getWarehouseDistrictCode() {
        return warehouseDistrictCode;
    }

    /**
     * 设置提货点地区代码
     *
     * @param warehouseDistrictCode 提货点地区代码
     */
    public void setWarehouseDistrictCode(String warehouseDistrictCode) {
        this.warehouseDistrictCode = warehouseDistrictCode == null ? null : warehouseDistrictCode.trim();
    }

    /**
     * 获取提货点详细地址
     *
     * @return warehouse_address - 提货点详细地址
     */
    public String getWarehouseAddress() {
        return warehouseAddress;
    }

    /**
     * 设置提货点详细地址
     *
     * @param warehouseAddress 提货点详细地址
     */
    public void setWarehouseAddress(String warehouseAddress) {
        this.warehouseAddress = warehouseAddress == null ? null : warehouseAddress.trim();
    }

    /**
     * 获取提货点经纬度
     *
     * @return warehouse_location - 提货点经纬度
     */
    public String getWarehouseLocation() {
        return warehouseLocation;
    }

    /**
     * 设置提货点经纬度
     *
     * @param warehouseLocation 提货点经纬度
     */
    public void setWarehouseLocation(String warehouseLocation) {
        this.warehouseLocation = warehouseLocation == null ? null : warehouseLocation.trim();
    }

    /**
     * 获取收货用户ID
     *
     * @return receive_user_id - 收货用户ID
     */
    public String getReceiveUserId() {
        return receiveUserId;
    }

    /**
     * 设置收货用户ID
     *
     * @param receiveUserId 收货用户ID
     */
    public void setReceiveUserId(String receiveUserId) {
        this.receiveUserId = receiveUserId == null ? null : receiveUserId.trim();
    }

    /**
     * 获取收货用户名
     *
     * @return receive_user_name - 收货用户名
     */
    public String getReceiveUserName() {
        return receiveUserName;
    }

    /**
     * 设置收货用户名
     *
     * @param receiveUserName 收货用户名
     */
    public void setReceiveUserName(String receiveUserName) {
        this.receiveUserName = receiveUserName == null ? null : receiveUserName.trim();
    }

    /**
     * 获取收获地址ID
     *
     * @return receive_address_id - 收获地址ID
     */
    public String getReceiveAddressId() {
        return receiveAddressId;
    }

    /**
     * 设置收获地址ID
     *
     * @param receiveAddressId 收获地址ID
     */
    public void setReceiveAddressId(String receiveAddressId) {
        this.receiveAddressId = receiveAddressId == null ? null : receiveAddressId.trim();
    }

    /**
     * 获取提货点详细地址
     *
     * @return receive_address_detail - 提货点详细地址
     */
    public String getReceiveAddressDetail() {
        return receiveAddressDetail;
    }

    /**
     * 设置提货点详细地址
     *
     * @param receiveAddressDetail 提货点详细地址
     */
    public void setReceiveAddressDetail(String receiveAddressDetail) {
        this.receiveAddressDetail = receiveAddressDetail == null ? null : receiveAddressDetail.trim();
    }

    /**
     * 获取收获地址地址经纬度
     *
     * @return receive_address_location - 收获地址地址经纬度
     */
    public String getReceiveAddressLocation() {
        return receiveAddressLocation;
    }

    /**
     * 设置收获地址地址经纬度
     *
     * @param receiveAddressLocation 收获地址地址经纬度
     */
    public void setReceiveAddressLocation(String receiveAddressLocation) {
        this.receiveAddressLocation = receiveAddressLocation == null ? null : receiveAddressLocation.trim();
    }

    /**
     * 获取运输品类ID
     *
     * @return transport_category_id - 运输品类ID
     */
    public String getTransportCategoryId() {
        return transportCategoryId;
    }

    /**
     * 设置运输品类ID
     *
     * @param transportCategoryId 运输品类ID
     */
    public void setTransportCategoryId(String transportCategoryId) {
        this.transportCategoryId = transportCategoryId == null ? null : transportCategoryId.trim();
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取del_flg
     *
     * @return del_flg - del_flg
     */
    public Boolean getDelFlg() {
        return delFlg;
    }

    /**
     * 设置del_flg
     *
     * @param delFlg del_flg
     */
    public void setDelFlg(Boolean delFlg) {
        this.delFlg = delFlg;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", carriageRouteId=").append(carriageRouteId);
        sb.append(", routeName=").append(routeName);
        sb.append(", userId=").append(userId);
        sb.append(", userName=").append(userName);
        sb.append(", userType=").append(userType);
        sb.append(", pricingType=").append(pricingType);
        sb.append(", warehouseId=").append(warehouseId);
        sb.append(", warehouseName=").append(warehouseName);
        sb.append(", warehouseType=").append(warehouseType);
        sb.append(", warehouseProvince=").append(warehouseProvince);
        sb.append(", warehouseProvinceCode=").append(warehouseProvinceCode);
        sb.append(", warehouseCity=").append(warehouseCity);
        sb.append(", warehouseCityCode=").append(warehouseCityCode);
        sb.append(", warehouseDistrict=").append(warehouseDistrict);
        sb.append(", warehouseDistrictCode=").append(warehouseDistrictCode);
        sb.append(", warehouseAddress=").append(warehouseAddress);
        sb.append(", warehouseLocation=").append(warehouseLocation);
        sb.append(", receiveUserId=").append(receiveUserId);
        sb.append(", receiveUserName=").append(receiveUserName);
        sb.append(", receiveAddressId=").append(receiveAddressId);
        sb.append(", receiveAddressDetail=").append(receiveAddressDetail);
        sb.append(", receiveAddressLocation=").append(receiveAddressLocation);
        sb.append(", transportCategoryId=").append(transportCategoryId);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}