package com.ecommerce.logistics.dao.vo;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Table(name = "lgs_carriage_rule")
public class CarriageRule implements Serializable {
    /**
     * 运费规则ID
     */
    @Id
    @Column(name = "carriage_rule_id")
    private String carriageRuleId;

    /**
     * 运费路线ID
     */
    @Column(name = "carriage_route_id")
    private String carriageRouteId;

    /**
     * 附件业务ID
     */
    private String bid;

    /**
     * 结算类型 1:收费 2:付费 3:委托
     */
    @Column(name = "settlement_type")
    private String settlementType;

    /**
     * 结算对象ID
     */
    @Column(name = "settlement_user_id")
    private String settlementUserId;

    /**
     * 结算对象名称
     */
    @Column(name = "settlement_user_name")
    private String settlementUserName;

    /**
     * 结算对象类型
     */
    @Column(name = "settlement_user_type")
    private Integer settlementUserType;

    /**
     * 车型ID
     */
    @Column(name = "vehicle_type_id")
    private String vehicleTypeId;

    /**
     * 单位运价
     */
    @Column(name = "unit_price")
    private BigDecimal unitPrice;

    /**
     * 开始生效时间
     */
    @Column(name = "begin_effective_time")
    private Date beginEffectiveTime;

    /**
     * 结束生效时间
     */
    @Column(name = "end_effective_time")
    private Date endEffectiveTime;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * del_flg
     */
    @Column(name = "del_flg")
    private Boolean delFlg;

    private static final long serialVersionUID = 1L;

    /**
     * 获取运费规则ID
     *
     * @return carriage_rule_id - 运费规则ID
     */
    public String getCarriageRuleId() {
        return carriageRuleId;
    }

    /**
     * 设置运费规则ID
     *
     * @param carriageRuleId 运费规则ID
     */
    public void setCarriageRuleId(String carriageRuleId) {
        this.carriageRuleId = carriageRuleId == null ? null : carriageRuleId.trim();
    }

    /**
     * 获取运费路线ID
     *
     * @return carriage_route_id - 运费路线ID
     */
    public String getCarriageRouteId() {
        return carriageRouteId;
    }

    /**
     * 设置运费路线ID
     *
     * @param carriageRouteId 运费路线ID
     */
    public void setCarriageRouteId(String carriageRouteId) {
        this.carriageRouteId = carriageRouteId == null ? null : carriageRouteId.trim();
    }

    /**
     * 获取附件业务ID
     *
     * @return bid - 附件业务ID
     */
    public String getBid() {
        return bid;
    }

    /**
     * 设置附件业务ID
     *
     * @param bid 附件业务ID
     */
    public void setBid(String bid) {
        this.bid = bid == null ? null : bid.trim();
    }

    /**
     * 获取结算类型 1:收费 2:付费 3:委托
     *
     * @return settlement_type - 结算类型 1:收费 2:付费 3:委托
     */
    public String getSettlementType() {
        return settlementType;
    }

    /**
     * 设置结算类型 1:收费 2:付费 3:委托
     *
     * @param settlementType 结算类型 1:收费 2:付费 3:委托
     */
    public void setSettlementType(String settlementType) {
        this.settlementType = settlementType == null ? null : settlementType.trim();
    }

    /**
     * 获取结算对象ID
     *
     * @return settlement_user_id - 结算对象ID
     */
    public String getSettlementUserId() {
        return settlementUserId;
    }

    /**
     * 设置结算对象ID
     *
     * @param settlementUserId 结算对象ID
     */
    public void setSettlementUserId(String settlementUserId) {
        this.settlementUserId = settlementUserId == null ? null : settlementUserId.trim();
    }

    /**
     * 获取结算对象名称
     *
     * @return settlement_user_name - 结算对象名称
     */
    public String getSettlementUserName() {
        return settlementUserName;
    }

    /**
     * 设置结算对象名称
     *
     * @param settlementUserName 结算对象名称
     */
    public void setSettlementUserName(String settlementUserName) {
        this.settlementUserName = settlementUserName == null ? null : settlementUserName.trim();
    }

    /**
     * 获取结算对象类型
     *
     * @return settlement_user_type - 结算对象类型
     */
    public Integer getSettlementUserType() {
        return settlementUserType;
    }

    /**
     * 设置结算对象类型
     *
     * @param settlementUserType 结算对象类型
     */
    public void setSettlementUserType(Integer settlementUserType) {
        this.settlementUserType = settlementUserType;
    }

    /**
     * 获取车型ID
     *
     * @return vehicle_type_id - 车型ID
     */
    public String getVehicleTypeId() {
        return vehicleTypeId;
    }

    /**
     * 设置车型ID
     *
     * @param vehicleTypeId 车型ID
     */
    public void setVehicleTypeId(String vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId == null ? null : vehicleTypeId.trim();
    }

    /**
     * 获取单位运价
     *
     * @return unit_price - 单位运价
     */
    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    /**
     * 设置单位运价
     *
     * @param unitPrice 单位运价
     */
    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    /**
     * 获取开始生效时间
     *
     * @return begin_effective_time - 开始生效时间
     */
    public Date getBeginEffectiveTime() {
        return beginEffectiveTime;
    }

    /**
     * 设置开始生效时间
     *
     * @param beginEffectiveTime 开始生效时间
     */
    public void setBeginEffectiveTime(Date beginEffectiveTime) {
        this.beginEffectiveTime = beginEffectiveTime;
    }

    /**
     * 获取结束生效时间
     *
     * @return end_effective_time - 结束生效时间
     */
    public Date getEndEffectiveTime() {
        return endEffectiveTime;
    }

    /**
     * 设置结束生效时间
     *
     * @param endEffectiveTime 结束生效时间
     */
    public void setEndEffectiveTime(Date endEffectiveTime) {
        this.endEffectiveTime = endEffectiveTime;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取del_flg
     *
     * @return del_flg - del_flg
     */
    public Boolean getDelFlg() {
        return delFlg;
    }

    /**
     * 设置del_flg
     *
     * @param delFlg del_flg
     */
    public void setDelFlg(Boolean delFlg) {
        this.delFlg = delFlg;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", carriageRuleId=").append(carriageRuleId);
        sb.append(", carriageRouteId=").append(carriageRouteId);
        sb.append(", bid=").append(bid);
        sb.append(", settlementType=").append(settlementType);
        sb.append(", settlementUserId=").append(settlementUserId);
        sb.append(", settlementUserName=").append(settlementUserName);
        sb.append(", settlementUserType=").append(settlementUserType);
        sb.append(", vehicleTypeId=").append(vehicleTypeId);
        sb.append(", unitPrice=").append(unitPrice);
        sb.append(", beginEffectiveTime=").append(beginEffectiveTime);
        sb.append(", endEffectiveTime=").append(endEffectiveTime);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}