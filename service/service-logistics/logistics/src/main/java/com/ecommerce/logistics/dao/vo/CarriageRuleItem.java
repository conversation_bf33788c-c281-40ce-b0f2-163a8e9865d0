package com.ecommerce.logistics.dao.vo;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

@Table(name = "lgs_carrier_carriage_item")
public class CarriageRuleItem implements Serializable {
    /**
     * 运费规则明细ID
     */
    @Id
    @Column(name = "carriage_item_id")
    private String carriageItemId;

    /**
     * 运费规则ID
     */
    @Column(name = "carriage_rule_id")
    private String carriageRuleId;

    /**
     * 区间范围类型
     */
    @Column(name = "section_type")
    private String sectionType;

    /**
     * 区间最小范围
     */
    @Column(name = "min_section")
    private Integer minSection;

    /**
     * 区间最大范围
     */
    @Column(name = "max_section")
    private Integer maxSection;

    /**
     * 规则表达式
     */
    @Column(name = "rule_expression")
    private String ruleExpression;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 数据版本
     */
    private Long version;

    /**
     * del_flg
     */
    @Column(name = "del_flg")
    private Boolean delFlg;

    private static final long serialVersionUID = 1L;

    /**
     * 获取运费规则明细ID
     *
     * @return carriage_item_id - 运费规则明细ID
     */
    public String getCarriageItemId() {
        return carriageItemId;
    }

    /**
     * 设置运费规则明细ID
     *
     * @param carriageItemId 运费规则明细ID
     */
    public void setCarriageItemId(String carriageItemId) {
        this.carriageItemId = carriageItemId == null ? null : carriageItemId.trim();
    }

    /**
     * 获取运费规则ID
     *
     * @return carriage_rule_id - 运费规则ID
     */
    public String getCarriageRuleId() {
        return carriageRuleId;
    }

    /**
     * 设置运费规则ID
     *
     * @param carriageRuleId 运费规则ID
     */
    public void setCarriageRuleId(String carriageRuleId) {
        this.carriageRuleId = carriageRuleId == null ? null : carriageRuleId.trim();
    }

    /**
     * 获取区间范围类型
     *
     * @return section_type - 区间范围类型
     */
    public String getSectionType() {
        return sectionType;
    }

    /**
     * 设置区间范围类型
     *
     * @param sectionType 区间范围类型
     */
    public void setSectionType(String sectionType) {
        this.sectionType = sectionType == null ? null : sectionType.trim();
    }

    /**
     * 获取区间最小范围
     *
     * @return min_section - 区间最小范围
     */
    public Integer getMinSection() {
        return minSection;
    }

    /**
     * 设置区间最小范围
     *
     * @param minSection 区间最小范围
     */
    public void setMinSection(Integer minSection) {
        this.minSection = minSection;
    }

    /**
     * 获取区间最大范围
     *
     * @return max_section - 区间最大范围
     */
    public Integer getMaxSection() {
        return maxSection;
    }

    /**
     * 设置区间最大范围
     *
     * @param maxSection 区间最大范围
     */
    public void setMaxSection(Integer maxSection) {
        this.maxSection = maxSection;
    }

    /**
     * 获取规则表达式
     *
     * @return rule_expression - 规则表达式
     */
    public String getRuleExpression() {
        return ruleExpression;
    }

    /**
     * 设置规则表达式
     *
     * @param ruleExpression 规则表达式
     */
    public void setRuleExpression(String ruleExpression) {
        this.ruleExpression = ruleExpression == null ? null : ruleExpression.trim();
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取数据版本
     *
     * @return version - 数据版本
     */
    public Long getVersion() {
        return version;
    }

    /**
     * 设置数据版本
     *
     * @param version 数据版本
     */
    public void setVersion(Long version) {
        this.version = version;
    }

    /**
     * 获取del_flg
     *
     * @return del_flg - del_flg
     */
    public Boolean getDelFlg() {
        return delFlg;
    }

    /**
     * 设置del_flg
     *
     * @param delFlg del_flg
     */
    public void setDelFlg(Boolean delFlg) {
        this.delFlg = delFlg;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", carriageItemId=").append(carriageItemId);
        sb.append(", carriageRuleId=").append(carriageRuleId);
        sb.append(", sectionType=").append(sectionType);
        sb.append(", minSection=").append(minSection);
        sb.append(", maxSection=").append(maxSection);
        sb.append(", ruleExpression=").append(ruleExpression);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", version=").append(version);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}