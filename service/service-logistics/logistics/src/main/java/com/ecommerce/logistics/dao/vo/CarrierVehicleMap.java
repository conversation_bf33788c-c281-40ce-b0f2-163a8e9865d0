package com.ecommerce.logistics.dao.vo;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

@Table(name = "lgs_carrier_vehicle_map")
public class CarrierVehicleMap implements Serializable {
    @Id
    @Column(name = "carrier_vehicle_id")
    private String carrierVehicleId;

    /**
     * 车辆ID
     */
    @Column(name = "vehicle_id")
    private String vehicleId;

    /**
     * 车牌号码
     */
    private String number;

    /**
     * 承运商id
     */
    @Column(name = "carrier_id")
    private String carrierId;

    /**
     * 承运商名称
     */
    @Column(name = "carrier_name")
    private String carrierName;

    /**
     * 所属人id
     */
    @Column(name = "belonger_id")
    private String belongerId;

    /**
     * 所属人名称
     */
    @Column(name = "belonger_name")
    private String belongerName;

    /**
     * 绑定状态（0-解绑，1绑定）
     */
    @Column(name = "bind_status")
    private Integer bindStatus;

    /**
     * 车辆来源（0-其他，1-自有车辆，2-外部车辆）
     */
    @Column(name = "vehicle_source")
    private Integer vehicleSource;

    /**
     * 删除标识
     */
    @Column(name = "del_flg")
    private Byte delFlg;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 创建人id
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 修改人id
     */
    @Column(name = "update_user")
    private String updateUser;

    private static final long serialVersionUID = 1L;

    /**
     * @return carrier_vehicle_id
     */
    public String getCarrierVehicleId() {
        return carrierVehicleId;
    }

    /**
     * @param carrierVehicleId
     */
    public void setCarrierVehicleId(String carrierVehicleId) {
        this.carrierVehicleId = carrierVehicleId == null ? null : carrierVehicleId.trim();
    }

    /**
     * 获取车辆ID
     *
     * @return vehicle_id - 车辆ID
     */
    public String getVehicleId() {
        return vehicleId;
    }

    /**
     * 设置车辆ID
     *
     * @param vehicleId 车辆ID
     */
    public void setVehicleId(String vehicleId) {
        this.vehicleId = vehicleId == null ? null : vehicleId.trim();
    }

    /**
     * 获取车牌号码
     *
     * @return number - 车牌号码
     */
    public String getNumber() {
        return number;
    }

    /**
     * 设置车牌号码
     *
     * @param number 车牌号码
     */
    public void setNumber(String number) {
        this.number = number == null ? null : number.trim();
    }

    /**
     * 获取承运商id
     *
     * @return carrier_id - 承运商id
     */
    public String getCarrierId() {
        return carrierId;
    }

    /**
     * 设置承运商id
     *
     * @param carrierId 承运商id
     */
    public void setCarrierId(String carrierId) {
        this.carrierId = carrierId == null ? null : carrierId.trim();
    }

    /**
     * 获取承运商名称
     *
     * @return carrier_name - 承运商名称
     */
    public String getCarrierName() {
        return carrierName;
    }

    /**
     * 设置承运商名称
     *
     * @param carrierName 承运商名称
     */
    public void setCarrierName(String carrierName) {
        this.carrierName = carrierName == null ? null : carrierName.trim();
    }

    /**
     * 获取所属人id
     *
     * @return belonger_id - 所属人id
     */
    public String getBelongerId() {
        return belongerId;
    }

    /**
     * 设置所属人id
     *
     * @param belongerId 所属人id
     */
    public void setBelongerId(String belongerId) {
        this.belongerId = belongerId == null ? null : belongerId.trim();
    }

    /**
     * 获取所属人名称
     *
     * @return belonger_name - 所属人名称
     */
    public String getBelongerName() {
        return belongerName;
    }

    /**
     * 设置所属人名称
     *
     * @param belongerName 所属人名称
     */
    public void setBelongerName(String belongerName) {
        this.belongerName = belongerName == null ? null : belongerName.trim();
    }

    /**
     * 获取绑定状态（0-解绑，1绑定）
     *
     * @return bind_status - 绑定状态（0-解绑，1绑定）
     */
    public Integer getBindStatus() {
        return bindStatus;
    }

    /**
     * 设置绑定状态（0-解绑，1绑定）
     *
     * @param bindStatus 绑定状态（0-解绑，1绑定）
     */
    public void setBindStatus(Integer bindStatus) {
        this.bindStatus = bindStatus;
    }

    /**
     * 获取车辆来源（0-其他，1-自有车辆，2-外部车辆）
     *
     * @return vehicle_source - 车辆来源（0-其他，1-自有车辆，2-外部车辆）
     */
    public Integer getVehicleSource() {
        return vehicleSource;
    }

    /**
     * 设置车辆来源（0-其他，1-自有车辆，2-外部车辆）
     *
     * @param vehicleSource 车辆来源（0-其他，1-自有车辆，2-外部车辆）
     */
    public void setVehicleSource(Integer vehicleSource) {
        this.vehicleSource = vehicleSource;
    }

    /**
     * 获取删除标识
     *
     * @return del_flg - 删除标识
     */
    public Byte getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标识
     *
     * @param delFlg 删除标识
     */
    public void setDelFlg(Byte delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取创建人id
     *
     * @return create_user - 创建人id
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建人id
     *
     * @param createUser 创建人id
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取修改时间
     *
     * @return update_time - 修改时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置修改时间
     *
     * @param updateTime 修改时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取修改人id
     *
     * @return update_user - 修改人id
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置修改人id
     *
     * @param updateUser 修改人id
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", carrierVehicleId=").append(carrierVehicleId);
        sb.append(", vehicleId=").append(vehicleId);
        sb.append(", number=").append(number);
        sb.append(", carrierId=").append(carrierId);
        sb.append(", carrierName=").append(carrierName);
        sb.append(", belongerId=").append(belongerId);
        sb.append(", belongerName=").append(belongerName);
        sb.append(", bindStatus=").append(bindStatus);
        sb.append(", vehicleSource=").append(vehicleSource);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createTime=").append(createTime);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}