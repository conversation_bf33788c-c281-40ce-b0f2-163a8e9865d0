package com.ecommerce.logistics.dao.vo;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Table(name = "lgs_complaint")
public class Complaint implements Serializable {
    /**
     * 投诉ID
     */
    @Id
    @Column(name = "complaint_id")
    private String complaintId;

    /**
     * 投诉编号
     */
    @Column(name = "complaint_num")
    private String complaintNum;

    /**
     * 投诉类型枚举[1.运输时效2.运输质量3.服务态度4.其他]
     */
    @Column(name = "complaint_type")
    private String complaintType;

    /**
     * 关联单据类型枚举[1.提货单2.调度单3.运单]
     */
    @Column(name = "relation_bill_type")
    private String relationBillType;

    /**
     * 关联单据号
     */
    @Column(name = "relation_bill_num")
    private String relationBillNum;

    /**
     * 投诉方类型枚举
     */
    @Column(name = "complaint_init_role_type")
    private String complaintInitRoleType;

    /**
     * 投诉方名称
     */
    @Column(name = "complaint_init_role_name")
    private String complaintInitRoleName;

    /**
     * 投诉方ID
     */
    @Column(name = "complaint_init_role_id")
    private String complaintInitRoleId;

    /**
     * 投诉内容
     */
    @Column(name = "complaint_content")
    private String complaintContent;

    /**
     * 被投诉方类型枚举
     */
    @Column(name = "complaint_accept_role_type")
    private String complaintAcceptRoleType;

    /**
     * 被投诉方ID
     */
    @Column(name = "complaint_accept_role_id")
    private String complaintAcceptRoleId;

    /**
     * 被投诉方名称
     */
    @Column(name = "complaint_accept_role_name")
    private String complaintAcceptRoleName;

    /**
     * 提出时间【创建时间】
     */
    @Column(name = "propose_time")
    private Date proposeTime;

    /**
     * 处理完成人名称
     */
    @Column(name = "deal_finish_user_name")
    private String dealFinishUserName;

    /**
     * 处理完成人ID
     */
    @Column(name = "deal_finish_user_id")
    private String dealFinishUserId;

    /**
     * 处理完结时间
     */
    @Column(name = "deal_finish_time")
    private Date dealFinishTime;

    /**
     * 处理状态[1.待处理 2.处理中 3.已处理]
     */
    @Column(name = "deal_status")
    private String dealStatus;

    /**
     * 投诉满意度评价得分
     */
    @Column(name = "evaluation_score")
    private Float evaluationScore;

    /**
     * 评价时间
     */
    @Column(name = "evaluation_time")
    private Date evaluationTime;

    /**
     * 责任归属方名称
     */
    @Column(name = "responsible_role_name")
    private String responsibleRoleName;

    /**
     * 责任归属方ID
     */
    @Column(name = "responsible_role_id")
    private String responsibleRoleId;

    /**
     * 责任归属类型枚举
     */
    @Column(name = "responsible_role_type")
    private String responsibleRoleType;

    /**
     * 责任归属备注
     */
    @Column(name = "responsible_content")
    private String responsibleContent;

    /**
     * 罚款金额
     */
    @Column(name = "fine_amount")
    private BigDecimal fineAmount;

    /**
     * 处理结果详情
     */
    @Column(name = "deal_result")
    private String dealResult;

    /**
     * 是否罚款【0:未罚款 1:罚款】
     */
    @Column(name = "fine_flag")
    private Integer fineFlag;

    /**
     * 删除标识
     */
    @Column(name = "del_flg")
    private Integer delFlg;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 版本号
     */
    private Long version;

    private static final long serialVersionUID = 1L;

    /**
     * 获取投诉ID
     *
     * @return complaint__id - 投诉ID
     */
    public String getComplaintId() {
        return complaintId;
    }

    /**
     * 设置投诉ID
     *
     * @param complaintId 投诉ID
     */
    public void setComplaintId(String complaintId) {
        this.complaintId = complaintId == null ? null : complaintId.trim();
    }

    /**
     * 获取投诉编号
     *
     * @return complaint_num - 投诉编号
     */
    public String getComplaintNum() {
        return complaintNum;
    }

    /**
     * 设置投诉编号
     *
     * @param complaintNum 投诉编号
     */
    public void setComplaintNum(String complaintNum) {
        this.complaintNum = complaintNum == null ? null : complaintNum.trim();
    }

    /**
     * 获取投诉类型枚举[1.运输时效2.运输质量3.服务态度4.其他]
     *
     * @return complaint_type - 投诉类型枚举[1.运输时效2.运输质量3.服务态度4.其他]
     */
    public String getComplaintType() {
        return complaintType;
    }

    /**
     * 设置投诉类型枚举[1.运输时效2.运输质量3.服务态度4.其他]
     *
     * @param complaintType 投诉类型枚举[1.运输时效2.运输质量3.服务态度4.其他]
     */
    public void setComplaintType(String complaintType) {
        this.complaintType = complaintType == null ? null : complaintType.trim();
    }

    /**
     * 获取关联单据类型枚举[1.提货单2.调度单3.运单]
     *
     * @return relation_bill_type - 关联单据类型枚举[1.提货单2.调度单3.运单]
     */
    public String getRelationBillType() {
        return relationBillType;
    }

    /**
     * 设置关联单据类型枚举[1.提货单2.调度单3.运单]
     *
     * @param relationBillType 关联单据类型枚举[1.提货单2.调度单3.运单]
     */
    public void setRelationBillType(String relationBillType) {
        this.relationBillType = relationBillType == null ? null : relationBillType.trim();
    }

    /**
     * 获取关联单据号
     *
     * @return relation_bill_num - 关联单据号
     */
    public String getRelationBillNum() {
        return relationBillNum;
    }

    /**
     * 设置关联单据号
     *
     * @param relationBillNum 关联单据号
     */
    public void setRelationBillNum(String relationBillNum) {
        this.relationBillNum = relationBillNum == null ? null : relationBillNum.trim();
    }

    /**
     * 获取投诉方类型枚举
     *
     * @return complaint_init_role_type - 投诉方类型枚举
     */
    public String getComplaintInitRoleType() {
        return complaintInitRoleType;
    }

    /**
     * 设置投诉方类型枚举
     *
     * @param complaintInitRoleType 投诉方类型枚举
     */
    public void setComplaintInitRoleType(String complaintInitRoleType) {
        this.complaintInitRoleType = complaintInitRoleType == null ? null : complaintInitRoleType.trim();
    }

    /**
     * 获取投诉方名称
     *
     * @return complaint_init_role_name - 投诉方名称
     */
    public String getComplaintInitRoleName() {
        return complaintInitRoleName;
    }

    /**
     * 设置投诉方名称
     *
     * @param complaintInitRoleName 投诉方名称
     */
    public void setComplaintInitRoleName(String complaintInitRoleName) {
        this.complaintInitRoleName = complaintInitRoleName == null ? null : complaintInitRoleName.trim();
    }

    /**
     * 获取投诉方ID
     *
     * @return complaint_init_role_id - 投诉方ID
     */
    public String getComplaintInitRoleId() {
        return complaintInitRoleId;
    }

    /**
     * 设置投诉方ID
     *
     * @param complaintInitRoleId 投诉方ID
     */
    public void setComplaintInitRoleId(String complaintInitRoleId) {
        this.complaintInitRoleId = complaintInitRoleId == null ? null : complaintInitRoleId.trim();
    }

    /**
     * 获取投诉内容
     *
     * @return complaint_content - 投诉内容
     */
    public String getComplaintContent() {
        return complaintContent;
    }

    /**
     * 设置投诉内容
     *
     * @param complaintContent 投诉内容
     */
    public void setComplaintContent(String complaintContent) {
        this.complaintContent = complaintContent == null ? null : complaintContent.trim();
    }

    /**
     * 获取被投诉方类型枚举
     *
     * @return complaint_accept_role_type - 被投诉方类型枚举
     */
    public String getComplaintAcceptRoleType() {
        return complaintAcceptRoleType;
    }

    /**
     * 设置被投诉方类型枚举
     *
     * @param complaintAcceptRoleType 被投诉方类型枚举
     */
    public void setComplaintAcceptRoleType(String complaintAcceptRoleType) {
        this.complaintAcceptRoleType = complaintAcceptRoleType == null ? null : complaintAcceptRoleType.trim();
    }

    /**
     * 获取被投诉方ID
     *
     * @return complaint_accept_role_id - 被投诉方ID
     */
    public String getComplaintAcceptRoleId() {
        return complaintAcceptRoleId;
    }

    /**
     * 设置被投诉方ID
     *
     * @param complaintAcceptRoleId 被投诉方ID
     */
    public void setComplaintAcceptRoleId(String complaintAcceptRoleId) {
        this.complaintAcceptRoleId = complaintAcceptRoleId == null ? null : complaintAcceptRoleId.trim();
    }

    /**
     * 获取被投诉方名称
     *
     * @return complaint_accept_role_name - 被投诉方名称
     */
    public String getComplaintAcceptRoleName() {
        return complaintAcceptRoleName;
    }

    /**
     * 设置被投诉方名称
     *
     * @param complaintAcceptRoleName 被投诉方名称
     */
    public void setComplaintAcceptRoleName(String complaintAcceptRoleName) {
        this.complaintAcceptRoleName = complaintAcceptRoleName == null ? null : complaintAcceptRoleName.trim();
    }

    /**
     * 获取提出时间【创建时间】
     *
     * @return propose_time - 提出时间【创建时间】
     */
    public Date getProposeTime() {
        return proposeTime;
    }

    /**
     * 设置提出时间【创建时间】
     *
     * @param proposeTime 提出时间【创建时间】
     */
    public void setProposeTime(Date proposeTime) {
        this.proposeTime = proposeTime;
    }

    /**
     * 获取处理完成人名称
     *
     * @return deal_finish_user_name - 处理完成人名称
     */
    public String getDealFinishUserName() {
        return dealFinishUserName;
    }

    /**
     * 设置处理完成人名称
     *
     * @param dealFinishUserName 处理完成人名称
     */
    public void setDealFinishUserName(String dealFinishUserName) {
        this.dealFinishUserName = dealFinishUserName == null ? null : dealFinishUserName.trim();
    }

    /**
     * 获取处理完成人ID
     *
     * @return deal_finish_user_id - 处理完成人ID
     */
    public String getDealFinishUserId() {
        return dealFinishUserId;
    }

    /**
     * 设置处理完成人ID
     *
     * @param dealFinishUserId 处理完成人ID
     */
    public void setDealFinishUserId(String dealFinishUserId) {
        this.dealFinishUserId = dealFinishUserId == null ? null : dealFinishUserId.trim();
    }

    /**
     * 获取处理完结时间
     *
     * @return deal_finish_time - 处理完结时间
     */
    public Date getDealFinishTime() {
        return dealFinishTime;
    }

    /**
     * 设置处理完结时间
     *
     * @param dealFinishTime 处理完结时间
     */
    public void setDealFinishTime(Date dealFinishTime) {
        this.dealFinishTime = dealFinishTime;
    }

    /**
     * 获取处理状态[1.待处理 2.处理中 3.已处理]
     *
     * @return deal_status - 处理状态[1.待处理 2.处理中 3.已处理]
     */
    public String getDealStatus() {
        return dealStatus;
    }

    /**
     * 设置处理状态[1.待处理 2.处理中 3.已处理]
     *
     * @param dealStatus 处理状态[1.待处理 2.处理中 3.已处理]
     */
    public void setDealStatus(String dealStatus) {
        this.dealStatus = dealStatus == null ? null : dealStatus.trim();
    }

    /**
     * 获取投诉满意度评价得分
     *
     * @return evaluation_score - 投诉满意度评价得分
     */
    public Float getEvaluationScore() {
        return evaluationScore;
    }

    /**
     * 设置投诉满意度评价得分
     *
     * @param evaluationScore 投诉满意度评价得分
     */
    public void setEvaluationScore(Float evaluationScore) {
        this.evaluationScore = evaluationScore;
    }

    /**
     * 获取评价时间
     *
     * @return evaluation_time - 评价时间
     */
    public Date getEvaluationTime() {
        return evaluationTime;
    }

    /**
     * 设置评价时间
     *
     * @param evaluationTime 评价时间
     */
    public void setEvaluationTime(Date evaluationTime) {
        this.evaluationTime = evaluationTime;
    }

    /**
     * 获取责任归属方名称
     *
     * @return responsible_role_name - 责任归属方名称
     */
    public String getResponsibleRoleName() {
        return responsibleRoleName;
    }

    /**
     * 设置责任归属方名称
     *
     * @param responsibleRoleName 责任归属方名称
     */
    public void setResponsibleRoleName(String responsibleRoleName) {
        this.responsibleRoleName = responsibleRoleName == null ? null : responsibleRoleName.trim();
    }

    /**
     * 获取责任归属方ID
     *
     * @return responsible_role_id - 责任归属方ID
     */
    public String getResponsibleRoleId() {
        return responsibleRoleId;
    }

    /**
     * 设置责任归属方ID
     *
     * @param responsibleRoleId 责任归属方ID
     */
    public void setResponsibleRoleId(String responsibleRoleId) {
        this.responsibleRoleId = responsibleRoleId == null ? null : responsibleRoleId.trim();
    }

    /**
     * 获取责任归属类型枚举
     *
     * @return responsible_role_type - 责任归属类型枚举
     */
    public String getResponsibleRoleType() {
        return responsibleRoleType;
    }

    /**
     * 设置责任归属类型枚举
     *
     * @param responsibleRoleType 责任归属类型枚举
     */
    public void setResponsibleRoleType(String responsibleRoleType) {
        this.responsibleRoleType = responsibleRoleType == null ? null : responsibleRoleType.trim();
    }

    /**
     * 获取责任归属备注
     *
     * @return responsible_content - 责任归属备注
     */
    public String getResponsibleContent() {
        return responsibleContent;
    }

    /**
     * 设置责任归属备注
     *
     * @param responsibleContent 责任归属备注
     */
    public void setResponsibleContent(String responsibleContent) {
        this.responsibleContent = responsibleContent == null ? null : responsibleContent.trim();
    }

    /**
     * 获取罚款金额
     *
     * @return fine_amount - 罚款金额
     */
    public BigDecimal getFineAmount() {
        return fineAmount;
    }

    /**
     * 设置罚款金额
     *
     * @param fineAmount 罚款金额
     */
    public void setFineAmount(BigDecimal fineAmount) {
        this.fineAmount = fineAmount;
    }

    /**
     * 获取处理结果详情
     *
     * @return deal_result - 处理结果详情
     */
    public String getDealResult() {
        return dealResult;
    }

    /**
     * 设置处理结果详情
     *
     * @param dealResult 处理结果详情
     */
    public void setDealResult(String dealResult) {
        this.dealResult = dealResult == null ? null : dealResult.trim();
    }

    /**
     * 获取是否罚款【0:未罚款 1:罚款】
     *
     * @return fine_flag - 是否罚款【0:未罚款 1:罚款】
     */
    public Integer getFineFlag() {
        return fineFlag;
    }

    /**
     * 设置是否罚款【0:未罚款 1:罚款】
     *
     * @param fineFlag 是否罚款【0:未罚款 1:罚款】
     */
    public void setFineFlag(Integer fineFlag) {
        this.fineFlag = fineFlag;
    }

    /**
     * 获取删除标识
     *
     * @return del_flg - 删除标识
     */
    public Integer getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标识
     *
     * @param delFlg 删除标识
     */
    public void setDelFlg(Integer delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * @return update_time
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * @param updateTime
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取版本号
     *
     * @return version - 版本号
     */
    public Long getVersion() {
        return version;
    }

    /**
     * 设置版本号
     *
     * @param version 版本号
     */
    public void setVersion(Long version) {
        this.version = version;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", complaintId=").append(complaintId);
        sb.append(", complaintNum=").append(complaintNum);
        sb.append(", complaintType=").append(complaintType);
        sb.append(", relationBillType=").append(relationBillType);
        sb.append(", relationBillNum=").append(relationBillNum);
        sb.append(", complaintInitRoleType=").append(complaintInitRoleType);
        sb.append(", complaintInitRoleName=").append(complaintInitRoleName);
        sb.append(", complaintInitRoleId=").append(complaintInitRoleId);
        sb.append(", complaintContent=").append(complaintContent);
        sb.append(", complaintAcceptRoleType=").append(complaintAcceptRoleType);
        sb.append(", complaintAcceptRoleId=").append(complaintAcceptRoleId);
        sb.append(", complaintAcceptRoleName=").append(complaintAcceptRoleName);
        sb.append(", proposeTime=").append(proposeTime);
        sb.append(", dealFinishUserName=").append(dealFinishUserName);
        sb.append(", dealFinishUserId=").append(dealFinishUserId);
        sb.append(", dealFinishTime=").append(dealFinishTime);
        sb.append(", dealStatus=").append(dealStatus);
        sb.append(", evaluationScore=").append(evaluationScore);
        sb.append(", evaluationTime=").append(evaluationTime);
        sb.append(", responsibleRoleName=").append(responsibleRoleName);
        sb.append(", responsibleRoleId=").append(responsibleRoleId);
        sb.append(", responsibleRoleType=").append(responsibleRoleType);
        sb.append(", responsibleContent=").append(responsibleContent);
        sb.append(", fineAmount=").append(fineAmount);
        sb.append(", dealResult=").append(dealResult);
        sb.append(", fineFlag=").append(fineFlag);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", version=").append(version);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}