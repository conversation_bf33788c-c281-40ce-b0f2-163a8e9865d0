package com.ecommerce.logistics.dao.vo;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

@Table(name = "lgs_credit_statistic")
public class CreditStatistic implements Serializable {
    /**
     * 信用统计ID
     */
    @Id
    @Column(name = "credit_statistic_id")
    private String creditStatisticId;

    /**
     * 承运人ID
     */
    @Column(name = "person_id")
    private String personId;

    /**
     * 承运人名称
     */
    @Column(name = "person_name")
    private String personName;

    /**
     * 承运人类型
     */
    @Column(name = "person_type")
    private String personType;

    /**
     * 接单次数
     */
    @Column(name = "bill_count")
    private Integer billCount;

    /**
     * 违约次数
     */
    @Column(name = "break_count")
    private Integer breakCount;

    /**
     * 客诉次数
     */
    @Column(name = "complaint_count")
    private Integer complaintCount;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 删除标识
     */
    @Column(name = "del_flg")
    private Byte delFlg;

    private static final long serialVersionUID = 1L;

    /**
     * 获取信用统计ID
     *
     * @return credit_statistic_id - 信用统计ID
     */
    public String getCreditStatisticId() {
        return creditStatisticId;
    }

    /**
     * 设置信用统计ID
     *
     * @param creditStatisticId 信用统计ID
     */
    public void setCreditStatisticId(String creditStatisticId) {
        this.creditStatisticId = creditStatisticId == null ? null : creditStatisticId.trim();
    }

    /**
     * 获取承运人ID
     *
     * @return person_id - 承运人ID
     */
    public String getPersonId() {
        return personId;
    }

    /**
     * 设置承运人ID
     *
     * @param personId 承运人ID
     */
    public void setPersonId(String personId) {
        this.personId = personId == null ? null : personId.trim();
    }

    /**
     * 获取承运人名称
     *
     * @return person_name - 承运人名称
     */
    public String getPersonName() {
        return personName;
    }

    /**
     * 设置承运人名称
     *
     * @param personName 承运人名称
     */
    public void setPersonName(String personName) {
        this.personName = personName == null ? null : personName.trim();
    }

    /**
     * 获取承运人类型
     *
     * @return person_type - 承运人类型
     */
    public String getPersonType() {
        return personType;
    }

    /**
     * 设置承运人类型
     *
     * @param personType 承运人类型
     */
    public void setPersonType(String personType) {
        this.personType = personType == null ? null : personType.trim();
    }

    /**
     * 获取接单次数
     *
     * @return bill_count - 接单次数
     */
    public Integer getBillCount() {
        return billCount;
    }

    /**
     * 设置接单次数
     *
     * @param billCount 接单次数
     */
    public void setBillCount(Integer billCount) {
        this.billCount = billCount;
    }

    /**
     * 获取违约次数
     *
     * @return break_count - 违约次数
     */
    public Integer getBreakCount() {
        return breakCount;
    }

    /**
     * 设置违约次数
     *
     * @param breakCount 违约次数
     */
    public void setBreakCount(Integer breakCount) {
        this.breakCount = breakCount;
    }

    /**
     * 获取客诉次数
     *
     * @return complaint_count - 客诉次数
     */
    public Integer getComplaintCount() {
        return complaintCount;
    }

    /**
     * 设置客诉次数
     *
     * @param complaintCount 客诉次数
     */
    public void setComplaintCount(Integer complaintCount) {
        this.complaintCount = complaintCount;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取删除标识
     *
     * @return del_flg - 删除标识
     */
    public Byte getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标识
     *
     * @param delFlg 删除标识
     */
    public void setDelFlg(Byte delFlg) {
        this.delFlg = delFlg;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", creditStatisticId=").append(creditStatisticId);
        sb.append(", personId=").append(personId);
        sb.append(", personName=").append(personName);
        sb.append(", personType=").append(personType);
        sb.append(", billCount=").append(billCount);
        sb.append(", breakCount=").append(breakCount);
        sb.append(", complaintCount=").append(complaintCount);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}