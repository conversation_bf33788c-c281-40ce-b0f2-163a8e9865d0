package com.ecommerce.logistics.dao.vo;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Table(name = "lgs_delivery_bill")
public class DeliveryBill implements Serializable {
    /**
     * 委托单主键
     */
    @Id
    @Column(name = "delivery_bill_id")
    private String deliveryBillId;

    /**
     * 委托单号
     */
    @Column(name = "delivery_bill_num")
    private String deliveryBillNum;

    /**
     * 配送信息ID
     */
    @Column(name = "delivery_info_id")
    private String deliveryInfoId;

    /**
     * 托运人ID
     */
    @Column(name = "consignor_id")
    private String consignorId;

    /**
     * 托运人名
     */
    @Column(name = "consignor_name")
    private String consignorName;

    /**
     * 承运人ID
     */
    @Column(name = "carrier_id")
    private String carrierId;

    /**
     * 承运人名
     */
    @Column(name = "carrier_name")
    private String carrierName;

    /**
     * 承运商类型(仅限承运商)
     */
    @Column(name = "carrier_type")
    private String carrierType;

    /**
     * 承运人角色类型
     */
    @Column(name = "carrier_role_type")
    private String carrierRoleType;

    /**
     * 背靠背单据层级
     */
    @Column(name = "bill_proxy_type")
    private String billProxyType;

    /**
     * 运输工具类型
     */
    @Column(name = "transport_tool_type")
    private String transportToolType;

    /**
     * 委托单状态
     */
    private String status;

    /**
     * 结算状态
     */
    @Column(name = "pay_status")
    private String payStatus;

    /**
     * 是否为根
     */
    @Column(name = "root_flag")
    private Byte rootFlag;

    /**
     * 是否为叶
     */
    @Column(name = "leaf_flag")
    private Byte leafFlag;

    /**
     * 父级ID,根的父级为自身主键
     */
    @Column(name = "parent_id")
    private String parentId;

    /**
     * 当前层级
     */
    @Column(name = "tree_level")
    private Integer treeLevel;

    /**
     * 指派ID路径
     */
    @Column(name = "node_id_path")
    private String nodeIdPath;

    /**
     * 指派编号路径
     */
    @Column(name = "node_num_path")
    private String nodeNumPath;

    /**
     * 委托来源
     */
    @Column(name = "entrust_source")
    private String entrustSource;

    /**
     * 商品数量
     */
    private BigDecimal quantity;

    /**
     * 已完成数量
     */
    @Column(name = "complete_quantity")
    private BigDecimal completeQuantity;

    /**
     * 改派掉的数量
     */
    @Column(name = "reroute_quantity")
    private BigDecimal rerouteQuantity;

    /**
     * 改航运力数
     */
    @Column(name = "reroute_count")
    private Integer rerouteCount;

    /**
     * 承运计划量
     */
    @Column(name = "carry_plan_quantity")
    private BigDecimal carryPlanQuantity;

    /**
     * 承运指派运力数
     */
    @Column(name = "carry_plan_count")
    private Integer carryPlanCount;

    /**
     * 承运发货量
     */
    @Column(name = "carry_send_quantity")
    private BigDecimal carrySendQuantity;

    /**
     * 承运发货运力数
     */
    @Column(name = "carry_send_count")
    private Integer carrySendCount;

    /**
     * 承运完成量
     */
    @Column(name = "carry_complete_quantity")
    private BigDecimal carryCompleteQuantity;

    /**
     * 承运完成运力数
     */
    @Column(name = "carry_complete_count")
    private Integer carryCompleteCount;

    /**
     * 委托总量
     */
    @Column(name = "consign_quantity")
    private BigDecimal consignQuantity;

    /**
     * 委托接受量
     */
    @Column(name = "consign_accept_quantity")
    private BigDecimal consignAcceptQuantity;

    /**
     * 委托发货量
     */
    @Column(name = "consign_send_quantity")
    private BigDecimal consignSendQuantity;

    /**
     * 委托发货运力数
     */
    @Column(name = "consign_send_count")
    private Integer consignSendCount;

    /**
     * 委托完成量
     */
    @Column(name = "consign_complete_quantity")
    private BigDecimal consignCompleteQuantity;

    /**
     * 委托完成运力数
     */
    @Column(name = "consign_complete_count")
    private Integer consignCompleteCount;

    /**
     * 应付运费单价
     */
    @Column(name = "payable_carriage_price")
    private BigDecimal payableCarriagePrice;

    /**
     * 应付运费总价
     */
    @Column(name = "payable_carriage_amount")
    private BigDecimal payableCarriageAmount;

    /**
     * 运费规则ID
     */
    @Column(name = "carriage_rule_id")
    private String carriageRuleId;

    /**
     * 预估配送里程
     */
    @Column(name = "estimate_km")
    private BigDecimal estimateKm;

    /**
     * 预估配送分钟数
     */
    @Column(name = "estimate_duration")
    private BigDecimal estimateDuration;

    /**
     * 出货点ID
     */
    @Column(name = "warehouse_id")
    private String warehouseId;

    /**
     * 仓库类型
     */
    @Column(name = "warehouse_type")
    private String warehouseType;

    /**
     * 仓库名称
     */
    @Column(name = "warehouse_name")
    private String warehouseName;

    /**
     * 出货点省
     */
    @Column(name = "warehouse_province")
    private String warehouseProvince;

    /**
     * 出货点省编码
     */
    @Column(name = "warehouse_province_code")
    private String warehouseProvinceCode;

    /**
     * 出货点市
     */
    @Column(name = "warehouse_city")
    private String warehouseCity;

    /**
     * 出货点市编码
     */
    @Column(name = "warehouse_city_code")
    private String warehouseCityCode;

    /**
     * 出货点区
     */
    @Column(name = "warehouse_district")
    private String warehouseDistrict;

    /**
     * 出货点区编码
     */
    @Column(name = "warehouse_district_code")
    private String warehouseDistrictCode;

    /**
     * 出货点街道
     */
    @Column(name = "warehouse_street")
    private String warehouseStreet;

    /**
     * 出货点街道编码
     */
    @Column(name = "warehouse_street_code")
    private String warehouseStreetCode;

    /**
     * 仓库地址
     */
    @Column(name = "warehouse_address")
    private String warehouseAddress;

    /**
     * 仓库坐标
     */
    @Column(name = "warehouse_location")
    private String warehouseLocation;

    /**
     * 删除标识
     */
    @Column(name = "del_flg")
    private Byte delFlg;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 数据版本
     */
    private Long version;
    
    /**
     * 基础运费单价
     */
    @Column(name = "carriage_unit_price")
    private BigDecimal carriageUnitPrice;

    /**
     * 辅助价目表价格
     */
    @Column(name = "auxiliary_price")
    private BigDecimal auxiliaryPrice;

    private static final long serialVersionUID = 1L;

    /**
     * 获取委托单主键
     *
     * @return delivery_bill_id - 委托单主键
     */
    public String getDeliveryBillId() {
        return deliveryBillId;
    }

    /**
     * 设置委托单主键
     *
     * @param deliveryBillId 委托单主键
     */
    public void setDeliveryBillId(String deliveryBillId) {
        this.deliveryBillId = deliveryBillId == null ? null : deliveryBillId.trim();
    }

    /**
     * 获取委托单号
     *
     * @return delivery_bill_num - 委托单号
     */
    public String getDeliveryBillNum() {
        return deliveryBillNum;
    }

    /**
     * 设置委托单号
     *
     * @param deliveryBillNum 委托单号
     */
    public void setDeliveryBillNum(String deliveryBillNum) {
        this.deliveryBillNum = deliveryBillNum == null ? null : deliveryBillNum.trim();
    }

    /**
     * 获取配送信息ID
     *
     * @return delivery_info_id - 配送信息ID
     */
    public String getDeliveryInfoId() {
        return deliveryInfoId;
    }

    /**
     * 设置配送信息ID
     *
     * @param deliveryInfoId 配送信息ID
     */
    public void setDeliveryInfoId(String deliveryInfoId) {
        this.deliveryInfoId = deliveryInfoId == null ? null : deliveryInfoId.trim();
    }

    /**
     * 获取托运人ID
     *
     * @return consignor_id - 托运人ID
     */
    public String getConsignorId() {
        return consignorId;
    }

    /**
     * 设置托运人ID
     *
     * @param consignorId 托运人ID
     */
    public void setConsignorId(String consignorId) {
        this.consignorId = consignorId == null ? null : consignorId.trim();
    }

    /**
     * 获取托运人名
     *
     * @return consignor_name - 托运人名
     */
    public String getConsignorName() {
        return consignorName;
    }

    /**
     * 设置托运人名
     *
     * @param consignorName 托运人名
     */
    public void setConsignorName(String consignorName) {
        this.consignorName = consignorName == null ? null : consignorName.trim();
    }

    /**
     * 获取承运人ID
     *
     * @return carrier_id - 承运人ID
     */
    public String getCarrierId() {
        return carrierId;
    }

    /**
     * 设置承运人ID
     *
     * @param carrierId 承运人ID
     */
    public void setCarrierId(String carrierId) {
        this.carrierId = carrierId == null ? null : carrierId.trim();
    }

    /**
     * 获取承运人名
     *
     * @return carrier_name - 承运人名
     */
    public String getCarrierName() {
        return carrierName;
    }

    /**
     * 设置承运人名
     *
     * @param carrierName 承运人名
     */
    public void setCarrierName(String carrierName) {
        this.carrierName = carrierName == null ? null : carrierName.trim();
    }

    /**
     * 获取承运商类型
     *
     * @return carrier_type - 承运承运商类型
     */
    public String getCarrierType() {
        return carrierType;
    }

    /**
     * 设置承运商类型
     *
     * @param carrierType 承运商类型
     */
    public void setCarrierType(String carrierType) {
        this.carrierType = carrierType == null ? null : carrierType.trim();
    }

    /**
     * 获取承运人角色类型
     *
     * @return carrier_role_type - 承运人角色类型
     */
    public String getCarrierRoleType() {
        return carrierRoleType;
    }

    /**
     * 设置承运人角色类型
     *
     * @param carrierRoleType 承运人角色类型
     */
    public void setCarrierRoleType(String carrierRoleType) {
        this.carrierRoleType = carrierRoleType == null ? null : carrierRoleType.trim();
    }

    /**
     * 获取背靠背单据层级
     *
     * @return bill_proxy_type - 背靠背单据层级
     */
    public String getBillProxyType() {
        return billProxyType;
    }

    /**
     * 设置背靠背单据层级
     *
     * @param billProxyType 背靠背单据层级
     */
    public void setBillProxyType(String billProxyType) {
        this.billProxyType = billProxyType == null ? null : billProxyType.trim();
    }
    /**
     * 获取运输工具类型
     *
     * @return transport_tool_type - 运输工具类型
     */
    public String getTransportToolType() {
        return transportToolType;
    }

    /**
     * 设置运输工具类型
     *
     * @param transportToolType 运输工具类型
     */
    public void setTransportToolType(String transportToolType) {
        this.transportToolType = transportToolType == null ? null : transportToolType.trim();
    }

    /**
     * 获取委托单状态
     *
     * @return status - 委托单状态
     */
    public String getStatus() {
        return status;
    }

    /**
     * 设置委托单状态
     *
     * @param status 委托单状态
     */
    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    /**
     * 获取结算状态
     *
     * @return pay_status - 结算状态
     */
    public String getPayStatus() {
        return payStatus;
    }

    /**
     * 设置结算状态
     *
     * @param payStatus 结算状态
     */
    public void setPayStatus(String payStatus) {
        this.payStatus = payStatus == null ? null : payStatus.trim();
    }

    /**
     * 获取是否为根
     *
     * @return root_flag - 是否为根
     */
    public Byte getRootFlag() {
        return rootFlag;
    }

    /**
     * 设置是否为根
     *
     * @param rootFlag 是否为根
     */
    public void setRootFlag(Byte rootFlag) {
        this.rootFlag = rootFlag;
    }

    /**
     * 获取是否为叶
     *
     * @return leaf_flag - 是否为叶
     */
    public Byte getLeafFlag() {
        return leafFlag;
    }

    /**
     * 设置是否为叶
     *
     * @param leafFlag 是否为叶
     */
    public void setLeafFlag(Byte leafFlag) {
        this.leafFlag = leafFlag;
    }

    /**
     * 获取父级ID,根的父级为自身主键
     *
     * @return parent_id - 父级ID,根的父级为自身主键
     */
    public String getParentId() {
        return parentId;
    }

    /**
     * 设置父级ID,根的父级为自身主键
     *
     * @param parentId 父级ID,根的父级为自身主键
     */
    public void setParentId(String parentId) {
        this.parentId = parentId == null ? null : parentId.trim();
    }

    /**
     * 获取当前层级
     *
     * @return tree_level - 当前层级
     */
    public Integer getTreeLevel() {
        return treeLevel;
    }

    /**
     * 设置当前层级
     *
     * @param treeLevel 当前层级
     */
    public void setTreeLevel(Integer treeLevel) {
        this.treeLevel = treeLevel;
    }

    /**
     * 获取指派ID路径
     *
     * @return node_id_path - 指派ID路径
     */
    public String getNodeIdPath() {
        return nodeIdPath;
    }

    /**
     * 设置指派ID路径
     *
     * @param nodeIdPath 指派ID路径
     */
    public void setNodeIdPath(String nodeIdPath) {
        this.nodeIdPath = nodeIdPath == null ? null : nodeIdPath.trim();
    }

    /**
     * 获取指派编号路径
     *
     * @return node_num_path - 指派编号路径
     */
    public String getNodeNumPath() {
        return nodeNumPath;
    }

    /**
     * 设置指派编号路径
     *
     * @param nodeNumPath 指派编号路径
     */
    public void setNodeNumPath(String nodeNumPath) {
        this.nodeNumPath = nodeNumPath == null ? null : nodeNumPath.trim();
    }

    /**
     * 获取委托来源
     *
     * @return entrust_source - 委托来源
     */
    public String getEntrustSource() {
        return entrustSource;
    }

    /**
     * 设置委托来源
     *
     * @param entrustSource 委托来源
     */
    public void setEntrustSource(String entrustSource) {
        this.entrustSource = entrustSource == null ? null : entrustSource.trim();
    }

    /**
     * 获取商品数量
     *
     * @return quantity - 商品数量
     */
    public BigDecimal getQuantity() {
        return quantity;
    }

    /**
     * 设置商品数量
     *
     * @param quantity 商品数量
     */
    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    /**
     * 获取已完成数量
     *
     * @return complete_quantity - 已完成数量
     */
    public BigDecimal getCompleteQuantity() {
        return completeQuantity;
    }

    /**
     * 设置已完成数量
     *
     * @param completeQuantity 已完成数量
     */
    public void setCompleteQuantity(BigDecimal completeQuantity) {
        this.completeQuantity = completeQuantity;
    }

    /**
     * 获取改派掉的数量
     *
     * @return reroute_quantity - 改派掉的数量
     */
    public BigDecimal getRerouteQuantity() {
        return rerouteQuantity;
    }

    /**
     * 设置改派掉的数量
     *
     * @param rerouteQuantity 改派掉的数量
     */
    public void setRerouteQuantity(BigDecimal rerouteQuantity) {
        this.rerouteQuantity = rerouteQuantity;
    }

    /**
     * 获取改航运力数
     *
     * @return reroute_count - 改航运力数
     */
    public Integer getRerouteCount() {
        return rerouteCount;
    }

    /**
     * 设置改航运力数
     *
     * @param rerouteCount 改航运力数
     */
    public void setRerouteCount(Integer rerouteCount) {
        this.rerouteCount = rerouteCount;
    }

    /**
     * 获取承运计划量
     *
     * @return carry_plan_quantity - 承运计划量
     */
    public BigDecimal getCarryPlanQuantity() {
        return carryPlanQuantity;
    }

    /**
     * 设置承运计划量
     *
     * @param carryPlanQuantity 承运计划量
     */
    public void setCarryPlanQuantity(BigDecimal carryPlanQuantity) {
        this.carryPlanQuantity = carryPlanQuantity;
    }

    /**
     * 获取承运指派运力数
     *
     * @return carry_plan_count - 承运指派运力数
     */
    public Integer getCarryPlanCount() {
        return carryPlanCount;
    }

    /**
     * 设置承运指派运力数
     *
     * @param carryPlanCount 承运指派运力数
     */
    public void setCarryPlanCount(Integer carryPlanCount) {
        this.carryPlanCount = carryPlanCount;
    }

    /**
     * 获取承运发货量
     *
     * @return carry_send_quantity - 承运发货量
     */
    public BigDecimal getCarrySendQuantity() {
        return carrySendQuantity;
    }

    /**
     * 设置承运发货量
     *
     * @param carrySendQuantity 承运发货量
     */
    public void setCarrySendQuantity(BigDecimal carrySendQuantity) {
        this.carrySendQuantity = carrySendQuantity;
    }

    /**
     * 获取承运发货运力数
     *
     * @return carry_send_count - 承运发货运力数
     */
    public Integer getCarrySendCount() {
        return carrySendCount;
    }

    /**
     * 设置承运发货运力数
     *
     * @param carrySendCount 承运发货运力数
     */
    public void setCarrySendCount(Integer carrySendCount) {
        this.carrySendCount = carrySendCount;
    }

    /**
     * 获取承运完成量
     *
     * @return carry_complete_quantity - 承运完成量
     */
    public BigDecimal getCarryCompleteQuantity() {
        return carryCompleteQuantity;
    }

    /**
     * 设置承运完成量
     *
     * @param carryCompleteQuantity 承运完成量
     */
    public void setCarryCompleteQuantity(BigDecimal carryCompleteQuantity) {
        this.carryCompleteQuantity = carryCompleteQuantity;
    }

    /**
     * 获取承运完成运力数
     *
     * @return carry_complete_count - 承运完成运力数
     */
    public Integer getCarryCompleteCount() {
        return carryCompleteCount;
    }

    /**
     * 设置承运完成运力数
     *
     * @param carryCompleteCount 承运完成运力数
     */
    public void setCarryCompleteCount(Integer carryCompleteCount) {
        this.carryCompleteCount = carryCompleteCount;
    }

    /**
     * 获取委托总量
     *
     * @return consign_quantity - 委托总量
     */
    public BigDecimal getConsignQuantity() {
        return consignQuantity;
    }

    /**
     * 设置委托总量
     *
     * @param consignQuantity 委托总量
     */
    public void setConsignQuantity(BigDecimal consignQuantity) {
        this.consignQuantity = consignQuantity;
    }

    /**
     * 获取委托接受量
     *
     * @return consign_accept_quantity - 委托接受量
     */
    public BigDecimal getConsignAcceptQuantity() {
        return consignAcceptQuantity;
    }

    /**
     * 设置委托接受量
     *
     * @param consignAcceptQuantity 委托接受量
     */
    public void setConsignAcceptQuantity(BigDecimal consignAcceptQuantity) {
        this.consignAcceptQuantity = consignAcceptQuantity;
    }

    /**
     * 获取委托发货量
     *
     * @return consign_send_quantity - 委托发货量
     */
    public BigDecimal getConsignSendQuantity() {
        return consignSendQuantity;
    }

    /**
     * 设置委托发货量
     *
     * @param consignSendQuantity 委托发货量
     */
    public void setConsignSendQuantity(BigDecimal consignSendQuantity) {
        this.consignSendQuantity = consignSendQuantity;
    }

    /**
     * 获取委托发货运力数
     *
     * @return consign_send_count - 委托发货运力数
     */
    public Integer getConsignSendCount() {
        return consignSendCount;
    }

    /**
     * 设置委托发货运力数
     *
     * @param consignSendCount 委托发货运力数
     */
    public void setConsignSendCount(Integer consignSendCount) {
        this.consignSendCount = consignSendCount;
    }

    /**
     * 获取委托完成量
     *
     * @return consign_complete_quantity - 委托完成量
     */
    public BigDecimal getConsignCompleteQuantity() {
        return consignCompleteQuantity;
    }

    /**
     * 设置委托完成量
     *
     * @param consignCompleteQuantity 委托完成量
     */
    public void setConsignCompleteQuantity(BigDecimal consignCompleteQuantity) {
        this.consignCompleteQuantity = consignCompleteQuantity;
    }

    /**
     * 获取委托完成运力数
     *
     * @return consign_complete_count - 委托完成运力数
     */
    public Integer getConsignCompleteCount() {
        return consignCompleteCount;
    }

    /**
     * 设置委托完成运力数
     *
     * @param consignCompleteCount 委托完成运力数
     */
    public void setConsignCompleteCount(Integer consignCompleteCount) {
        this.consignCompleteCount = consignCompleteCount;
    }

    /**
     * 获取应付运费单价
     *
     * @return payable_carriage_price - 应付运费单价
     */
    public BigDecimal getPayableCarriagePrice() {
        return payableCarriagePrice;
    }

    /**
     * 设置应付运费单价
     *
     * @param payableCarriagePrice 应付运费单价
     */
    public void setPayableCarriagePrice(BigDecimal payableCarriagePrice) {
        this.payableCarriagePrice = payableCarriagePrice;
    }

    /**
     * 获取应付运费总价
     *
     * @return payable_carriage_amount - 应付运费总价
     */
    public BigDecimal getPayableCarriageAmount() {
        return payableCarriageAmount;
    }

    /**
     * 设置应付运费总价
     *
     * @param payableCarriageAmount 应付运费总价
     */
    public void setPayableCarriageAmount(BigDecimal payableCarriageAmount) {
        this.payableCarriageAmount = payableCarriageAmount;
    }

    /**
     * 获取运费规则ID
     *
     * @return carriage_rule_id - 运费规则ID
     */
    public String getCarriageRuleId() {
        return carriageRuleId;
    }

    /**
     * 设置运费规则ID
     *
     * @param carriageRuleId 运费规则ID
     */
    public void setCarriageRuleId(String carriageRuleId) {
        this.carriageRuleId = carriageRuleId == null ? null : carriageRuleId.trim();
    }

    /**
     * 获取预估配送里程
     *
     * @return estimate_km - 预估配送里程
     */
    public BigDecimal getEstimateKm() {
        return estimateKm;
    }

    /**
     * 设置预估配送里程
     *
     * @param estimateKm 预估配送里程
     */
    public void setEstimateKm(BigDecimal estimateKm) {
        this.estimateKm = estimateKm;
    }

    public BigDecimal getEstimateDuration() {
        return estimateDuration;
    }

    public void setEstimateDuration(BigDecimal estimateDuration) {
        this.estimateDuration = estimateDuration;
    }

    /**
     * 获取出货点ID
     *
     * @return warehouse_id - 出货点ID
     */
    public String getWarehouseId() {
        return warehouseId;
    }

    /**
     * 设置出货点ID
     *
     * @param warehouseId 出货点ID
     */
    public void setWarehouseId(String warehouseId) {
        this.warehouseId = warehouseId == null ? null : warehouseId.trim();
    }

    /**
     * 获取仓库类型
     *
     * @return warehouse_type - 仓库类型
     */
    public String getWarehouseType() {
        return warehouseType;
    }

    /**
     * 设置仓库类型
     *
     * @param warehouseType 仓库类型
     */
    public void setWarehouseType(String warehouseType) {
        this.warehouseType = warehouseType == null ? null : warehouseType.trim();
    }

    /**
     * 获取仓库名称
     *
     * @return warehouse_name - 仓库名称
     */
    public String getWarehouseName() {
        return warehouseName;
    }

    /**
     * 设置仓库名称
     *
     * @param warehouseName 仓库名称
     */
    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName == null ? null : warehouseName.trim();
    }

    /**
     * 获取出货点省
     *
     * @return warehouse_province - 出货点省
     */
    public String getWarehouseProvince() {
        return warehouseProvince;
    }

    /**
     * 设置出货点省
     *
     * @param warehouseProvince 出货点省
     */
    public void setWarehouseProvince(String warehouseProvince) {
        this.warehouseProvince = warehouseProvince == null ? null : warehouseProvince.trim();
    }

    /**
     * 获取出货点省编码
     *
     * @return warehouse_province_code - 出货点省编码
     */
    public String getWarehouseProvinceCode() {
        return warehouseProvinceCode;
    }

    /**
     * 设置出货点省编码
     *
     * @param warehouseProvinceCode 出货点省编码
     */
    public void setWarehouseProvinceCode(String warehouseProvinceCode) {
        this.warehouseProvinceCode = warehouseProvinceCode == null ? null : warehouseProvinceCode.trim();
    }

    /**
     * 获取出货点市
     *
     * @return warehouse_city - 出货点市
     */
    public String getWarehouseCity() {
        return warehouseCity;
    }

    /**
     * 设置出货点市
     *
     * @param warehouseCity 出货点市
     */
    public void setWarehouseCity(String warehouseCity) {
        this.warehouseCity = warehouseCity == null ? null : warehouseCity.trim();
    }

    /**
     * 获取出货点市编码
     *
     * @return warehouse_city_code - 出货点市编码
     */
    public String getWarehouseCityCode() {
        return warehouseCityCode;
    }

    /**
     * 设置出货点市编码
     *
     * @param warehouseCityCode 出货点市编码
     */
    public void setWarehouseCityCode(String warehouseCityCode) {
        this.warehouseCityCode = warehouseCityCode == null ? null : warehouseCityCode.trim();
    }

    /**
     * 获取出货点区
     *
     * @return warehouse_district - 出货点区
     */
    public String getWarehouseDistrict() {
        return warehouseDistrict;
    }

    /**
     * 设置出货点区
     *
     * @param warehouseDistrict 出货点区
     */
    public void setWarehouseDistrict(String warehouseDistrict) {
        this.warehouseDistrict = warehouseDistrict == null ? null : warehouseDistrict.trim();
    }

    /**
     * 获取出货点区编码
     *
     * @return warehouse_district_code - 出货点区编码
     */
    public String getWarehouseDistrictCode() {
        return warehouseDistrictCode;
    }

    /**
     * 设置出货点区编码
     *
     * @param warehouseDistrictCode 出货点区编码
     */
    public void setWarehouseDistrictCode(String warehouseDistrictCode) {
        this.warehouseDistrictCode = warehouseDistrictCode == null ? null : warehouseDistrictCode.trim();
    }

    /**
     * 获取出货点街道
     *
     * @return warehouse_street - 出货点街道
     */
    public String getWarehouseStreet() {
        return warehouseStreet;
    }

    /**
     * 设置出货点街道
     *
     * @param warehouseStreet 出货点街道
     */
    public void setWarehouseStreet(String warehouseStreet) {
        this.warehouseStreet = warehouseStreet == null ? null : warehouseStreet.trim();
    }

    /**
     * 获取出货点街道编码
     *
     * @return warehouse_street_code - 出货点街道编码
     */
    public String getWarehouseStreetCode() {
        return warehouseStreetCode;
    }

    /**
     * 设置出货点街道编码
     *
     * @param warehouseStreetCode 出货点街道编码
     */
    public void setWarehouseStreetCode(String warehouseStreetCode) {
        this.warehouseStreetCode = warehouseStreetCode == null ? null : warehouseStreetCode.trim();
    }

    /**
     * 获取仓库地址
     *
     * @return warehouse_address - 仓库地址
     */
    public String getWarehouseAddress() {
        return warehouseAddress;
    }

    /**
     * 设置仓库地址
     *
     * @param warehouseAddress 仓库地址
     */
    public void setWarehouseAddress(String warehouseAddress) {
        this.warehouseAddress = warehouseAddress == null ? null : warehouseAddress.trim();
    }

    /**
     * 获取仓库坐标
     *
     * @return warehouse_location - 仓库坐标
     */
    public String getWarehouseLocation() {
        return warehouseLocation;
    }

    /**
     * 设置仓库坐标
     *
     * @param warehouseLocation 仓库坐标
     */
    public void setWarehouseLocation(String warehouseLocation) {
        this.warehouseLocation = warehouseLocation == null ? null : warehouseLocation.trim();
    }

    /**
     * 获取删除标识
     *
     * @return del_flg - 删除标识
     */
    public Byte getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标识
     *
     * @param delFlg 删除标识
     */
    public void setDelFlg(Byte delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建人
     *
     * @return create_user - 创建人
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建人
     *
     * @param createUser 创建人
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新人
     *
     * @return update_user - 更新人
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新人
     *
     * @param updateUser 更新人
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取数据版本
     *
     * @return version - 数据版本
     */
    public Long getVersion() {
        return version;
    }

    /**
     * 设置数据版本
     *
     * @param version 数据版本
     */
    public void setVersion(Long version) {
        this.version = version;
    }

    
    
    public BigDecimal getCarriageUnitPrice() {
		return carriageUnitPrice;
	}

	public void setCarriageUnitPrice(BigDecimal carriageUnitPrice) {
		this.carriageUnitPrice = carriageUnitPrice;
	}

	public BigDecimal getAuxiliaryPrice() {
		return auxiliaryPrice;
	}

	public void setAuxiliaryPrice(BigDecimal auxiliaryPrice) {
		this.auxiliaryPrice = auxiliaryPrice;
	}

	@Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", deliveryBillId=").append(deliveryBillId);
        sb.append(", deliveryBillNum=").append(deliveryBillNum);
        sb.append(", deliveryInfoId=").append(deliveryInfoId);
        sb.append(", consignorId=").append(consignorId);
        sb.append(", consignorName=").append(consignorName);
        sb.append(", carrierId=").append(carrierId);
        sb.append(", carrierName=").append(carrierName);
        sb.append(", carrierType=").append(carrierType);
        sb.append(", carrierRoleType=").append(carrierRoleType);
        sb.append(", billProxyType=").append(billProxyType);
        sb.append(", transportToolType=").append(transportToolType);
        sb.append(", status=").append(status);
        sb.append(", payStatus=").append(payStatus);
        sb.append(", rootFlag=").append(rootFlag);
        sb.append(", leafFlag=").append(leafFlag);
        sb.append(", parentId=").append(parentId);
        sb.append(", treeLevel=").append(treeLevel);
        sb.append(", nodeIdPath=").append(nodeIdPath);
        sb.append(", nodeNumPath=").append(nodeNumPath);
        sb.append(", entrustSource=").append(entrustSource);
        sb.append(", quantity=").append(quantity);
        sb.append(", completeQuantity=").append(completeQuantity);
        sb.append(", rerouteQuantity=").append(rerouteQuantity);
        sb.append(", rerouteCount=").append(rerouteCount);
        sb.append(", carryPlanQuantity=").append(carryPlanQuantity);
        sb.append(", carryPlanCount=").append(carryPlanCount);
        sb.append(", carrySendQuantity=").append(carrySendQuantity);
        sb.append(", carrySendCount=").append(carrySendCount);
        sb.append(", carryCompleteQuantity=").append(carryCompleteQuantity);
        sb.append(", carryCompleteCount=").append(carryCompleteCount);
        sb.append(", consignQuantity=").append(consignQuantity);
        sb.append(", consignAcceptQuantity=").append(consignAcceptQuantity);
        sb.append(", consignSendQuantity=").append(consignSendQuantity);
        sb.append(", consignSendCount=").append(consignSendCount);
        sb.append(", consignCompleteQuantity=").append(consignCompleteQuantity);
        sb.append(", consignCompleteCount=").append(consignCompleteCount);
        sb.append(", payableCarriagePrice=").append(payableCarriagePrice);
        sb.append(", payableCarriageAmount=").append(payableCarriageAmount);
        sb.append(", carriageRuleId=").append(carriageRuleId);
        sb.append(", estimateKm=").append(estimateKm);
        sb.append(", estimateDuration=").append(estimateDuration);
        sb.append(", warehouseId=").append(warehouseId);
        sb.append(", warehouseType=").append(warehouseType);
        sb.append(", warehouseName=").append(warehouseName);
        sb.append(", warehouseProvince=").append(warehouseProvince);
        sb.append(", warehouseProvinceCode=").append(warehouseProvinceCode);
        sb.append(", warehouseCity=").append(warehouseCity);
        sb.append(", warehouseCityCode=").append(warehouseCityCode);
        sb.append(", warehouseDistrict=").append(warehouseDistrict);
        sb.append(", warehouseDistrictCode=").append(warehouseDistrictCode);
        sb.append(", warehouseStreet=").append(warehouseStreet);
        sb.append(", warehouseStreetCode=").append(warehouseStreetCode);
        sb.append(", warehouseAddress=").append(warehouseAddress);
        sb.append(", warehouseLocation=").append(warehouseLocation);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", version=").append(version);
        sb.append(", carriageUnitPrice=").append(carriageUnitPrice);
        sb.append(", auxiliaryPrice=").append(auxiliaryPrice);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}