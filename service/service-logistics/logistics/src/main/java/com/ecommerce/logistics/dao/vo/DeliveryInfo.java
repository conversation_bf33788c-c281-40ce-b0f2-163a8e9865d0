package com.ecommerce.logistics.dao.vo;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Table(name = "lgs_delivery_info")
public class DeliveryInfo implements Serializable {
    /**
     * 配送信息ID
     */
    @Id
    @Column(name = "delivery_info_id")
    private String deliveryInfoId;

    /**
     * 父级配送信息ID
     */
    @Column(name = "parent_info_id")
    private String parentInfoId;

    /**
     * 买家ID
     */
    @Column(name = "buyer_id")
    private String buyerId;

    /**
     * 买家名称
     */
    @Column(name = "buyer_name")
    private String buyerName;

    /**
     * 卖家ID
     */
    @Column(name = "seller_id")
    private String sellerId;

    /**
     * 卖家名称
     */
    @Column(name = "seller_name")
    private String sellerName;

    /**
     * erp支付账户编码
     */
    @Column(name = "mdm_code")
    private String mdmCode;

    /**
     * 发票类型 1：一票制、2：2票制
     */
    @Column(name = "bill_type")
    private Integer billType;

    /**
     * 发货单号
     */
    @Column(name = "take_code")
    private String takeCode;

    /**
     * 订单号
     */
    @Column(name = "order_code")
    private String orderCode;

    /**
     * 订单子项ID
     */
    @Column(name = "order_item_id")
    private String orderItemId;

    /**
     * 提货类型
     */
    private String type;

    /**
     * 关联类型
     */
    @Column(name = "relation_type")
    private String relationType;

    /**
     * 单据背靠背层级
     */
    @Column(name = "bill_proxy_type")
    private String billProxyType;

    /**
     * 是否可操作
     */
    @Column(name = "can_operate")
    private Byte canOperate;

    /**
     * 是否同意调配
     */
    @Column(name = "if_agree_dispatch")
    private Integer ifAgreeDispatch;

    /**
     * 出厂基地偏好
     */
    @Column(name = "base_preference")
    private String basePreference;

    /**
     * 起运港ID
     */
    @Column(name = "load_port_id")
    private String loadPortId;

    /**
     * 起运港名
     */
    @Column(name = "load_port_name")
    private String loadPortName;

    /**
     * 目的港ID
     */
    @Column(name = "unload_port_id")
    private String unloadPortId;

    /**
     * 目的港名
     */
    @Column(name = "unload_port_name")
    private String unloadPortName;

    /**
     * 出货点ID
     */
    @Column(name = "warehouse_id")
    private String warehouseId;

    /**
     * 仓库类型
     */
    @Column(name = "warehouse_type")
    private String warehouseType;

    /**
     * 仓库名称
     */
    @Column(name = "warehouse_name")
    private String warehouseName;

    /**
     * 出货点省
     */
    @Column(name = "warehouse_province")
    private String warehouseProvince;

    /**
     * 出货点省编码
     */
    @Column(name = "warehouse_province_code")
    private String warehouseProvinceCode;

    /**
     * 出货点市
     */
    @Column(name = "warehouse_city")
    private String warehouseCity;

    /**
     * 出货点市编码
     */
    @Column(name = "warehouse_city_code")
    private String warehouseCityCode;

    /**
     * 出货点区
     */
    @Column(name = "warehouse_district")
    private String warehouseDistrict;

    /**
     * 出货点区编码
     */
    @Column(name = "warehouse_district_code")
    private String warehouseDistrictCode;

    /**
     * 出货点街道
     */
    @Column(name = "warehouse_street")
    private String warehouseStreet;

    /**
     * 出货点街道编码
     */
    @Column(name = "warehouse_street_code")
    private String warehouseStreetCode;

    /**
     * 仓库地址
     */
    @Column(name = "warehouse_address")
    private String warehouseAddress;

    /**
     * 仓库坐标
     */
    @Column(name = "warehouse_location")
    private String warehouseLocation;

    /**
     * 收货地址ID
     */
    @Column(name = "receiver_address_id")
    private String receiverAddressId;

    /**
     * 收货人
     */
    private String receiver;

    /**
     * 收货电话
     */
    @Column(name = "receiver_phone")
    private String receiverPhone;

    /**
     * 收货省级名
     */
    private String province;

    /**
     * 收货省编码
     */
    @Column(name = "province_code")
    private String provinceCode;

    /**
     * 收货市级名
     */
    private String city;

    /**
     * 收货市编码
     */
    @Column(name = "city_code")
    private String cityCode;

    /**
     * 收货区级名
     */
    private String district;

    /**
     * 收货区编码
     */
    @Column(name = "district_code")
    private String districtCode;

    /**
     * 收货街道名
     */
    private String street;

    /**
     * 收货街道编码
     */
    @Column(name = "street_code")
    private String streetCode;

    /**
     * 收货详细地址
     */
    private String address;

    /**
     * 收货坐标
     */
    private String location;

    /**
     * 配送日期(期望)
     */
    @Column(name = "delivery_time")
    private Date deliveryTime;

    /**
     * 配送时间区间(期望)
     */
    @Column(name = "delivery_time_range")
    private String deliveryTimeRange;

    /**
     * 同步标识
     */
    @Column(name = "sync_flag")
    private String syncFlag;

    /**
     * 运输工具类型
     */
    @Column(name = "transport_tool_type")
    private String transportToolType;

    /**
     * 合同ID
     */
    @Column(name = "deals_id")
    private String dealsId;

    /**
     * 合同编号
     */
    @Column(name = "deals_name")
    private String dealsName;

    /**
     * 项目名称
     */
    @Column(name = "project_name")
    private String projectName;

    /**
     * 配送信息来源
     */
    @Column(name = "data_source")
    private String dataSource;

    /**
     * 搬运标识 0:否 1:是
     */
    @Column(name = "carry_flag")
    private Byte carryFlag;

    /**
     * 车辆限高(米)
     */
    @Column(name = "height_limit")
    private BigDecimal heightLimit;

    /**
     * 装卸能力要求0-无,1-要求
     */
    @Column(name = "unload_require_flag")
    private Integer unloadRequireFlag;

    /**
     * 搬运能力要求0-无,1-要求
     */
    @Column(name = "carry_require_flag")
    private Integer carryRequireFlag;

    /**
     * 运输品类ID
     */
    @Column(name = "transport_category_id")
    private String transportCategoryId;

    /**
     * erp商品编码
     */
    @Column(name = "commodity_code")
    private String commodityCode;

    /**
     * 商品厂商
     */
    private String vendor;

    /**
     * 商品图片url
     */
    @Column(name = "product_img")
    private String productImg;

    /**
     * 商品ID
     */
    @Column(name = "goods_id")
    private String goodsId;

    /**
     * 商品描述
     */
    private String note;

    /**
     * 商品单价
     */
    private BigDecimal price;

    /**
     * 商品单位
     */
    private String unit;

    /**
     * 运费单价
     */
    @Column(name = "carriage_unit_price")
    private BigDecimal carriageUnitPrice;

    /**
     * 流向卸货点编码
     */
    @Column(name = "unloading_flow_code")
    private String unloadingFlowCode;

    /**
     * 需要监控
     */
    @Column(name = "need_monitor")
    private Byte needMonitor;

    /**
     * 签收类型
     */
    @Column(name = "sign_type")
    private String signType;

    /**
     * 签收人
     */
    @Column(name = "signer")
    private String signer;

    /**
     * 签收人电话
     */
    @Column(name = "signer_phone")
    private String signerPhone;

    /**
     * 销售区域
     */
    @Column(name = "sale_region_path")
    private String saleRegionPath;

    /**
     * 业务员ID
     */
    @Column(name = "salesman_id")
    private String salesmanId;

    /**
     * 业务员名称
     */
    @Column(name = "salesman_name")
    private String salesmanName;

    /**
     * 特殊商品标识
     */
    @Column(name = "special_flag")
    private Byte specialFlag;

    /**
     * 删除标识
     */
    @Column(name = "del_flg")
    private Byte delFlg;

    /**
     * 创建人ID
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新人ID
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 数据版本
     */
    private Long version;

    private static final long serialVersionUID = 1L;

    /**
     * 获取配送信息ID
     *
     * @return delivery_info_id - 配送信息ID
     */
    public String getDeliveryInfoId() {
        return deliveryInfoId;
    }

    /**
     * 设置配送信息ID
     *
     * @param deliveryInfoId 配送信息ID
     */
    public void setDeliveryInfoId(String deliveryInfoId) {
        this.deliveryInfoId = deliveryInfoId == null ? null : deliveryInfoId.trim();
    }

    /**
     * 获取父级配送信息ID
     *
     * @return parent_info_id - 父级配送信息ID
     */
    public String getParentInfoId() {
        return parentInfoId;
    }

    /**
     * 设置父级配送信息ID
     *
     * @param parentInfoId 父级配送信息ID
     */
    public void setParentInfoId(String parentInfoId) {
        this.parentInfoId = parentInfoId == null ? null : parentInfoId.trim();
    }

    /**
     * 获取买家ID
     *
     * @return buyer_id - 买家ID
     */
    public String getBuyerId() {
        return buyerId;
    }

    /**
     * 设置买家ID
     *
     * @param buyerId 买家ID
     */
    public void setBuyerId(String buyerId) {
        this.buyerId = buyerId == null ? null : buyerId.trim();
    }

    /**
     * 获取买家名称
     *
     * @return buyer_name - 买家名称
     */
    public String getBuyerName() {
        return buyerName;
    }

    /**
     * 设置买家名称
     *
     * @param buyerName 买家名称
     */
    public void setBuyerName(String buyerName) {
        this.buyerName = buyerName == null ? null : buyerName.trim();
    }

    /**
     * 获取卖家ID
     *
     * @return seller_id - 卖家ID
     */
    public String getSellerId() {
        return sellerId;
    }

    /**
     * 设置卖家ID
     *
     * @param sellerId 卖家ID
     */
    public void setSellerId(String sellerId) {
        this.sellerId = sellerId == null ? null : sellerId.trim();
    }

    /**
     * 获取卖家名称
     *
     * @return seller_name - 卖家名称
     */
    public String getSellerName() {
        return sellerName;
    }

    /**
     * 设置卖家名称
     *
     * @param sellerName 卖家名称
     */
    public void setSellerName(String sellerName) {
        this.sellerName = sellerName == null ? null : sellerName.trim();
    }

    /**
     * 获取erp支付账户编码
     *
     * @return mdm_code - erp支付账户编码
     */
    public String getMdmCode() {
        return mdmCode;
    }

    /**
     * 设置erp支付账户编码
     *
     * @param mdmCode erp支付账户编码
     */
    public void setMdmCode(String mdmCode) {
        this.mdmCode = mdmCode == null ? null : mdmCode.trim();
    }

    public Integer getBillType()
    {
        return billType;
    }

    public void setBillType(Integer billType)
    {
        this.billType = billType;
    }

    /**
     * 获取发货单号
     *
     * @return take_code - 发货单号
     */
    public String getTakeCode() {
        return takeCode;
    }

    /**
     * 设置发货单号
     *
     * @param takeCode 发货单号
     */
    public void setTakeCode(String takeCode) {
        this.takeCode = takeCode == null ? null : takeCode.trim();
    }

    /**
     * 获取订单号
     *
     * @return order_code - 订单号
     */
    public String getOrderCode() {
        return orderCode;
    }

    /**
     * 设置订单号
     *
     * @param orderCode 订单号
     */
    public void setOrderCode(String orderCode) {
        this.orderCode = orderCode == null ? null : orderCode.trim();
    }

    /**
     * 获取订单子项ID
     *
     * @return order_item_id - 订单子项ID
     */
    public String getOrderItemId() {
        return orderItemId;
    }

    /**
     * 设置订单子项ID
     *
     * @param orderItemId 订单子项ID
     */
    public void setOrderItemId(String orderItemId) {
        this.orderItemId = orderItemId == null ? null : orderItemId.trim();
    }

    /**
     * 获取提货类型
     *
     * @return type - 提货类型
     */
    public String getType() {
        return type;
    }

    /**
     * 设置提货类型
     *
     * @param type 提货类型
     */
    public void setType(String type) {
        this.type = type == null ? null : type.trim();
    }

    /**
     * 获取关联类型
     *
     * @return relation_type - 关联类型
     */
    public String getRelationType() {
        return relationType;
    }

    /**
     * 设置关联类型
     *
     * @param relationType 关联类型
     */
    public void setRelationType(String relationType) {
        this.relationType = relationType == null ? null : relationType.trim();
    }

    /**
     * 获取单据背靠背层级
     *
     * @return bill_proxy_type - 单据背靠背层级
     */
    public String getBillProxyType() {
        return billProxyType;
    }

    /**
     * 设置单据背靠背层级
     *
     * @param billProxyType 单据背靠背层级
     */
    public void setBillProxyType(String billProxyType) {
        this.billProxyType = billProxyType == null ? null : billProxyType.trim();
    }

    /**
     * 获取是否可操作
     *
     * @return can_operate - 是否可操作
     */
    public Byte getCanOperate() {
        return canOperate;
    }

    /**
     * 设置是否可操作
     *
     * @param canOperate 是否可操作
     */
    public void setCanOperate(Byte canOperate) {
        this.canOperate = canOperate;
    }

    /**
     * 获取是否同意调配
     *
     * @return if_agree_dispatch - 是否同意调配
     */
    public Integer getIfAgreeDispatch() {
        return ifAgreeDispatch;
    }

    /**
     * 设置是否同意调配
     * @param ifAgreeDispatch 是否同意调配
     */
    public void setIfAgreeDispatch(Integer ifAgreeDispatch) {
        this.ifAgreeDispatch = ifAgreeDispatch;
    }

    public String getBasePreference() {
        return basePreference;
    }

    public void setBasePreference(String basePreference) {
        this.basePreference = basePreference == null ? null : basePreference.trim();
    }

    /**
     * 获取起运港ID
     *
     * @return load_port_id - 起运港ID
     */
    public String getLoadPortId() {
        return loadPortId;
    }

    /**
     * 设置起运港ID
     *
     * @param loadPortId 起运港ID
     */
    public void setLoadPortId(String loadPortId) {
        this.loadPortId = loadPortId == null ? null : loadPortId.trim();
    }

    /**
     * 获取起运港名
     *
     * @return load_port_name - 起运港名
     */
    public String getLoadPortName() {
        return loadPortName;
    }

    /**
     * 设置起运港名
     *
     * @param loadPortName 起运港名
     */
    public void setLoadPortName(String loadPortName) {
        this.loadPortName = loadPortName == null ? null : loadPortName.trim();
    }

    /**
     * 获取目的港ID
     *
     * @return unload_port_id - 目的港ID
     */
    public String getUnloadPortId() {
        return unloadPortId;
    }

    /**
     * 设置目的港ID
     *
     * @param unloadPortId 目的港ID
     */
    public void setUnloadPortId(String unloadPortId) {
        this.unloadPortId = unloadPortId == null ? null : unloadPortId.trim();
    }

    /**
     * 获取目的港名
     *
     * @return unload_port_name - 目的港名
     */
    public String getUnloadPortName() {
        return unloadPortName;
    }

    /**
     * 设置目的港名
     *
     * @param unloadPortName 目的港名
     */
    public void setUnloadPortName(String unloadPortName) {
        this.unloadPortName = unloadPortName == null ? null : unloadPortName.trim();
    }

    /**
     * 获取出货点ID
     *
     * @return warehouse_id - 出货点ID
     */
    public String getWarehouseId() {
        return warehouseId;
    }

    /**
     * 设置出货点ID
     *
     * @param warehouseId 出货点ID
     */
    public void setWarehouseId(String warehouseId) {
        this.warehouseId = warehouseId == null ? null : warehouseId.trim();
    }

    /**
     * 获取仓库类型
     *
     * @return warehouse_type - 仓库类型
     */
    public String getWarehouseType() {
        return warehouseType;
    }

    /**
     * 设置仓库类型
     *
     * @param warehouseType 仓库类型
     */
    public void setWarehouseType(String warehouseType) {
        this.warehouseType = warehouseType == null ? null : warehouseType.trim();
    }

    /**
     * 获取仓库名称
     *
     * @return warehouse_name - 仓库名称
     */
    public String getWarehouseName() {
        return warehouseName;
    }

    /**
     * 设置仓库名称
     *
     * @param warehouseName 仓库名称
     */
    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName == null ? null : warehouseName.trim();
    }

    /**
     * 获取出货点省
     *
     * @return warehouse_province - 出货点省
     */
    public String getWarehouseProvince() {
        return warehouseProvince;
    }

    /**
     * 设置出货点省
     *
     * @param warehouseProvince 出货点省
     */
    public void setWarehouseProvince(String warehouseProvince) {
        this.warehouseProvince = warehouseProvince == null ? null : warehouseProvince.trim();
    }

    /**
     * 获取出货点省编码
     *
     * @return warehouse_province_code - 出货点省编码
     */
    public String getWarehouseProvinceCode() {
        return warehouseProvinceCode;
    }

    /**
     * 设置出货点省编码
     *
     * @param warehouseProvinceCode 出货点省编码
     */
    public void setWarehouseProvinceCode(String warehouseProvinceCode) {
        this.warehouseProvinceCode = warehouseProvinceCode == null ? null : warehouseProvinceCode.trim();
    }

    /**
     * 获取出货点市
     *
     * @return warehouse_city - 出货点市
     */
    public String getWarehouseCity() {
        return warehouseCity;
    }

    /**
     * 设置出货点市
     *
     * @param warehouseCity 出货点市
     */
    public void setWarehouseCity(String warehouseCity) {
        this.warehouseCity = warehouseCity == null ? null : warehouseCity.trim();
    }

    /**
     * 获取出货点市编码
     *
     * @return warehouse_city_code - 出货点市编码
     */
    public String getWarehouseCityCode() {
        return warehouseCityCode;
    }

    /**
     * 设置出货点市编码
     *
     * @param warehouseCityCode 出货点市编码
     */
    public void setWarehouseCityCode(String warehouseCityCode) {
        this.warehouseCityCode = warehouseCityCode == null ? null : warehouseCityCode.trim();
    }

    /**
     * 获取出货点区
     *
     * @return warehouse_district - 出货点区
     */
    public String getWarehouseDistrict() {
        return warehouseDistrict;
    }

    /**
     * 设置出货点区
     *
     * @param warehouseDistrict 出货点区
     */
    public void setWarehouseDistrict(String warehouseDistrict) {
        this.warehouseDistrict = warehouseDistrict == null ? null : warehouseDistrict.trim();
    }

    /**
     * 获取出货点区编码
     *
     * @return warehouse_district_code - 出货点区编码
     */
    public String getWarehouseDistrictCode() {
        return warehouseDistrictCode;
    }

    /**
     * 设置出货点区编码
     *
     * @param warehouseDistrictCode 出货点区编码
     */
    public void setWarehouseDistrictCode(String warehouseDistrictCode) {
        this.warehouseDistrictCode = warehouseDistrictCode == null ? null : warehouseDistrictCode.trim();
    }

    /**
     * 获取出货点街道
     *
     * @return warehouse_street - 出货点街道
     */
    public String getWarehouseStreet() {
        return warehouseStreet;
    }

    /**
     * 设置出货点街道
     *
     * @param warehouseStreet 出货点街道
     */
    public void setWarehouseStreet(String warehouseStreet) {
        this.warehouseStreet = warehouseStreet == null ? null : warehouseStreet.trim();
    }

    /**
     * 获取出货点街道编码
     *
     * @return warehouse_street_code - 出货点街道编码
     */
    public String getWarehouseStreetCode() {
        return warehouseStreetCode;
    }

    /**
     * 设置出货点街道编码
     *
     * @param warehouseStreetCode 出货点街道编码
     */
    public void setWarehouseStreetCode(String warehouseStreetCode) {
        this.warehouseStreetCode = warehouseStreetCode == null ? null : warehouseStreetCode.trim();
    }

    /**
     * 获取仓库地址
     *
     * @return warehouse_address - 仓库地址
     */
    public String getWarehouseAddress() {
        return warehouseAddress;
    }

    /**
     * 设置仓库地址
     *
     * @param warehouseAddress 仓库地址
     */
    public void setWarehouseAddress(String warehouseAddress) {
        this.warehouseAddress = warehouseAddress == null ? null : warehouseAddress.trim();
    }

    /**
     * 获取仓库坐标
     *
     * @return warehouse_location - 仓库坐标
     */
    public String getWarehouseLocation() {
        return warehouseLocation;
    }

    /**
     * 设置仓库坐标
     *
     * @param warehouseLocation 仓库坐标
     */
    public void setWarehouseLocation(String warehouseLocation) {
        this.warehouseLocation = warehouseLocation == null ? null : warehouseLocation.trim();
    }

    /**
     * 获取收货地址ID
     *
     * @return receiver_address_id - 收货地址ID
     */
    public String getReceiverAddressId() {
        return receiverAddressId;
    }

    /**
     * 设置收货地址ID
     *
     * @param receiverAddressId 收货地址ID
     */
    public void setReceiverAddressId(String receiverAddressId) {
        this.receiverAddressId = receiverAddressId == null ? null : receiverAddressId.trim();
    }

    /**
     * 获取收货人
     *
     * @return receiver - 收货人
     */
    public String getReceiver() {
        return receiver;
    }

    /**
     * 设置收货人
     *
     * @param receiver 收货人
     */
    public void setReceiver(String receiver) {
        this.receiver = receiver == null ? null : receiver.trim();
    }

    /**
     * 获取收货电话
     *
     * @return receiver_phone - 收货电话
     */
    public String getReceiverPhone() {
        return receiverPhone;
    }

    /**
     * 设置收货电话
     *
     * @param receiverPhone 收货电话
     */
    public void setReceiverPhone(String receiverPhone) {
        this.receiverPhone = receiverPhone == null ? null : receiverPhone.trim();
    }

    /**
     * 获取收货省级名
     *
     * @return province - 收货省级名
     */
    public String getProvince() {
        return province;
    }

    /**
     * 设置收货省级名
     *
     * @param province 收货省级名
     */
    public void setProvince(String province) {
        this.province = province == null ? null : province.trim();
    }

    /**
     * 获取收货省编码
     *
     * @return province_code - 收货省编码
     */
    public String getProvinceCode() {
        return provinceCode;
    }

    /**
     * 设置收货省编码
     *
     * @param provinceCode 收货省编码
     */
    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode == null ? null : provinceCode.trim();
    }

    /**
     * 获取收货市级名
     *
     * @return city - 收货市级名
     */
    public String getCity() {
        return city;
    }

    /**
     * 设置收货市级名
     *
     * @param city 收货市级名
     */
    public void setCity(String city) {
        this.city = city == null ? null : city.trim();
    }

    /**
     * 获取收货市编码
     *
     * @return city_code - 收货市编码
     */
    public String getCityCode() {
        return cityCode;
    }

    /**
     * 设置收货市编码
     *
     * @param cityCode 收货市编码
     */
    public void setCityCode(String cityCode) {
        this.cityCode = cityCode == null ? null : cityCode.trim();
    }

    /**
     * 获取收货区级名
     *
     * @return district - 收货区级名
     */
    public String getDistrict() {
        return district;
    }

    /**
     * 设置收货区级名
     *
     * @param district 收货区级名
     */
    public void setDistrict(String district) {
        this.district = district == null ? null : district.trim();
    }

    /**
     * 获取收货区编码
     *
     * @return district_code - 收货区编码
     */
    public String getDistrictCode() {
        return districtCode;
    }

    /**
     * 设置收货区编码
     *
     * @param districtCode 收货区编码
     */
    public void setDistrictCode(String districtCode) {
        this.districtCode = districtCode == null ? null : districtCode.trim();
    }

    /**
     * 获取收货街道名
     *
     * @return street - 收货街道名
     */
    public String getStreet() {
        return street;
    }

    /**
     * 设置收货街道名
     *
     * @param street 收货街道名
     */
    public void setStreet(String street) {
        this.street = street == null ? null : street.trim();
    }

    /**
     * 获取收货街道编码
     *
     * @return street_code - 收货街道编码
     */
    public String getStreetCode() {
        return streetCode;
    }

    /**
     * 设置收货街道编码
     *
     * @param streetCode 收货街道编码
     */
    public void setStreetCode(String streetCode) {
        this.streetCode = streetCode == null ? null : streetCode.trim();
    }

    /**
     * 获取收货详细地址
     *
     * @return address - 收货详细地址
     */
    public String getAddress() {
        return address;
    }

    /**
     * 设置收货详细地址
     *
     * @param address 收货详细地址
     */
    public void setAddress(String address) {
        this.address = address == null ? null : address.trim();
    }

    /**
     * 获取收货坐标
     *
     * @return location - 收货坐标
     */
    public String getLocation() {
        return location;
    }

    /**
     * 设置收货坐标
     *
     * @param location 收货坐标
     */
    public void setLocation(String location) {
        this.location = location == null ? null : location.trim();
    }

    /**
     * 获取配送日期(期望)
     *
     * @return delivery_time - 配送日期(期望)
     */
    public Date getDeliveryTime() {
        return deliveryTime;
    }

    /**
     * 设置配送日期(期望)
     *
     * @param deliveryTime 配送日期(期望)
     */
    public void setDeliveryTime(Date deliveryTime) {
        this.deliveryTime = deliveryTime;
    }

    /**
     * 获取配送时间区间(期望)
     *
     * @return delivery_time_range - 配送时间区间(期望)
     */
    public String getDeliveryTimeRange() {
        return deliveryTimeRange;
    }

    /**
     * 设置配送时间区间(期望)
     *
     * @param deliveryTimeRange 配送时间区间(期望)
     */
    public void setDeliveryTimeRange(String deliveryTimeRange) {
        this.deliveryTimeRange = deliveryTimeRange == null ? null : deliveryTimeRange.trim();
    }

    /**
     * 获取同步标识
     *
     * @return sync_flag - 同步标识
     */
    public String getSyncFlag() {
        return syncFlag;
    }

    /**
     * 设置同步标识
     *
     * @param syncFlag 同步标识
     */
    public void setSyncFlag(String syncFlag) {
        this.syncFlag = syncFlag == null ? null : syncFlag.trim();
    }

    /**
     * 获取运输工具类型
     *
     * @return transport_tool_type - 运输工具类型
     */
    public String getTransportToolType() {
        return transportToolType;
    }

    /**
     * 设置运输工具类型
     *
     * @param transportToolType 运输工具类型
     */
    public void setTransportToolType(String transportToolType) {
        this.transportToolType = transportToolType == null ? null : transportToolType.trim();
    }

    /**
     * 获取合同ID
     *
     * @return deals_id - 合同ID
     */
    public String getDealsId() {
        return dealsId;
    }

    /**
     * 设置合同ID
     *
     * @param dealsId 合同ID
     */
    public void setDealsId(String dealsId) {
        this.dealsId = dealsId == null ? null : dealsId.trim();
    }

    /**
     * 获取合同编号
     *
     * @return deals_name - 合同编号
     */
    public String getDealsName() {
        return dealsName;
    }

    /**
     * 设置合同编号
     *
     * @param dealsName 合同编号
     */
    public void setDealsName(String dealsName) {
        this.dealsName = dealsName == null ? null : dealsName.trim();
    }

    /**
     * 获取项目名称
     *
     * @return project_name - 项目名称
     */
    public String getProjectName() {
        return projectName;
    }

    /**
     * 设置项目名称
     *
     * @param projectName 项目名称
     */
    public void setProjectName(String projectName) {
        this.projectName = projectName == null ? null : projectName.trim();
    }

    /**
     * 获取配送信息来源
     *
     * @return data_source - 配送信息来源
     */
    public String getDataSource() {
        return dataSource;
    }

    /**
     * 设置配送信息来源
     *
     * @param dataSource 配送信息来源
     */
    public void setDataSource(String dataSource) {
        this.dataSource = dataSource == null ? null : dataSource.trim();
    }

    /**
     * 获取搬运标识 0:否 1:是
     *
     * @return carry_flag - 搬运标识 0:否 1:是
     */
    public Byte getCarryFlag() {
        return carryFlag;
    }

    /**
     * 设置搬运标识 0:否 1:是
     *
     * @param carryFlag 搬运标识 0:否 1:是
     */
    public void setCarryFlag(Byte carryFlag) {
        this.carryFlag = carryFlag;
    }

    /**
     * 获取车辆限高(米)
     *
     * @return height_limit - 车辆限高(米)
     */
    public BigDecimal getHeightLimit() {
        return heightLimit;
    }

    /**
     * 设置车辆限高(米)
     *
     * @param heightLimit 车辆限高(米)
     */
    public void setHeightLimit(BigDecimal heightLimit) {
        this.heightLimit = heightLimit;
    }

    /**
     * 获取装卸能力要求0-无,1-要求
     *
     * @return unload_require_flag - 装卸能力要求0-无,1-要求
     */
    public Integer getUnloadRequireFlag() {
        return unloadRequireFlag;
    }

    /**
     * 设置装卸能力要求0-无,1-要求
     *
     * @param unloadRequireFlag 装卸能力要求0-无,1-要求
     */
    public void setUnloadRequireFlag(Integer unloadRequireFlag) {
        this.unloadRequireFlag = unloadRequireFlag;
    }

    /**
     * 获取搬运能力要求0-无,1-要求
     *
     * @return carry_require_flag - 搬运能力要求0-无,1-要求
     */
    public Integer getCarryRequireFlag() {
        return carryRequireFlag;
    }

    /**
     * 设置搬运能力要求0-无,1-要求
     *
     * @param carryRequireFlag 搬运能力要求0-无,1-要求
     */
    public void setCarryRequireFlag(Integer carryRequireFlag) {
        this.carryRequireFlag = carryRequireFlag;
    }

    /**
     * 获取运输品类ID
     *
     * @return transport_category_id - 运输品类ID
     */
    public String getTransportCategoryId() {
        return transportCategoryId;
    }

    /**
     * 设置运输品类ID
     *
     * @param transportCategoryId 运输品类ID
     */
    public void setTransportCategoryId(String transportCategoryId) {
        this.transportCategoryId = transportCategoryId == null ? null : transportCategoryId.trim();
    }

    /**
     * 获取erp商品编码
     *
     * @return commodity_code - erp商品编码
     */
    public String getCommodityCode() {
        return commodityCode;
    }

    /**
     * 设置erp商品编码
     *
     * @param commodityCode erp商品编码
     */
    public void setCommodityCode(String commodityCode) {
        this.commodityCode = commodityCode == null ? null : commodityCode.trim();
    }

    /**
     * 获取商品厂商
     *
     * @return vendor - 商品厂商
     */
    public String getVendor() {
        return vendor;
    }

    /**
     * 设置商品厂商
     *
     * @param vendor 商品厂商
     */
    public void setVendor(String vendor) {
        this.vendor = vendor == null ? null : vendor.trim();
    }

    /**
     * 获取商品图片url
     *
     * @return product_img - 商品图片url
     */
    public String getProductImg() {
        return productImg;
    }

    /**
     * 设置商品图片url
     *
     * @param productImg 商品图片url
     */
    public void setProductImg(String productImg) {
        this.productImg = productImg == null ? null : productImg.trim();
    }

    /**
     * 获取商品ID
     *
     * @return goods_id - 商品ID
     */
    public String getGoodsId() {
        return goodsId;
    }

    /**
     * 设置商品ID
     *
     * @param goodsId 商品ID
     */
    public void setGoodsId(String goodsId) {
        this.goodsId = goodsId == null ? null : goodsId.trim();
    }

    /**
     * 获取商品描述
     *
     * @return note - 商品描述
     */
    public String getNote() {
        return note;
    }

    /**
     * 设置商品描述
     *
     * @param note 商品描述
     */
    public void setNote(String note) {
        this.note = note == null ? null : note.trim();
    }

    /**
     * 获取商品单价
     *
     * @return price - 商品单价
     */
    public BigDecimal getPrice() {
        return price;
    }

    /**
     * 设置商品单价
     *
     * @param price 商品单价
     */
    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    /**
     * 获取商品单位
     *
     * @return unit - 商品单位
     */
    public String getUnit() {
        return unit;
    }

    /**
     * 设置商品单位
     *
     * @param unit 商品单位
     */
    public void setUnit(String unit) {
        this.unit = unit == null ? null : unit.trim();
    }

    /**
     * 获取运费单价
     *
     * @return carriage_unit_price - 运费单价
     */
    public BigDecimal getCarriageUnitPrice() {
        return carriageUnitPrice;
    }

    /**
     * 设置运费单价
     *
     * @param carriageUnitPrice 运费单价
     */
    public void setCarriageUnitPrice(BigDecimal carriageUnitPrice) {
        this.carriageUnitPrice = carriageUnitPrice;
    }

    /**
     * 获取流向卸货点编码
     *
     * @return unloading_flow_code - 流向卸货点编码
     */
    public String getUnloadingFlowCode() {
        return unloadingFlowCode;
    }

    /**
     * 设置流向卸货点编码
     *
     * @param unloadingFlowCode 流向卸货点编码
     */
    public void setUnloadingFlowCode(String unloadingFlowCode) {
        this.unloadingFlowCode = unloadingFlowCode == null ? null : unloadingFlowCode.trim();
    }

    /**
     * 获取需要监控
     *
     * @return need_monitor - 需要监控
     */
    public Byte getNeedMonitor() {
        return needMonitor;
    }

    /**
     * 设置需要监控
     *
     * @param needMonitor 需要监控
     */
    public void setNeedMonitor(Byte needMonitor) {
        this.needMonitor = needMonitor;
    }

    /**
     * 获取签收类型
     *
     * @return sign_type - 签收类型
     */
    public String getSignType() {
        return signType;
    }

    /**
     * 设置签收类型
     *
     * @param signType 签收类型
     */
    public void setSignType(String signType) {
        this.signType = signType == null ? null : signType.trim();
    }

    public String getSigner() {
        return signer;
    }

    public void setSigner(String signer) {
        this.signer = signer == null ? null : signer.trim();
    }

    public String getSignerPhone() {
        return signerPhone;
    }

    public void setSignerPhone(String signerPhone) {
        this.signerPhone = signerPhone == null ? null : signerPhone.trim();
    }

    public String getSaleRegionPath() {
        return saleRegionPath;
    }

    public void setSaleRegionPath(String saleRegionPath) {
        this.saleRegionPath = saleRegionPath == null ? null : saleRegionPath.trim();
    }

    public String getSalesmanId() {
        return salesmanId;
    }

    public void setSalesmanId(String salesmanId) {
        this.salesmanId = salesmanId == null ? null : salesmanId.trim();
    }

    public String getSalesmanName() {
        return salesmanName;
    }

    public void setSalesmanName(String salesmanName) {
        this.salesmanName = salesmanName == null ? null : salesmanName.trim();
    }

    /**
     * 获取特殊商品标识
     *
     * @return special_flag - 特殊商品标识
     */
    public Byte getSpecialFlag() {
        return specialFlag;
    }

    /**
     * 设置特殊商品标识
     *
     * @param specialFlag 特殊商品标识
     */
    public void setSpecialFlag(Byte specialFlag) {
        this.specialFlag = specialFlag;
    }

    /**
     * 获取删除标识
     *
     * @return del_flg - 删除标识
     */
    public Byte getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标识
     *
     * @param delFlg 删除标识
     */
    public void setDelFlg(Byte delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建人ID
     *
     * @return create_user - 创建人ID
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建人ID
     *
     * @param createUser 创建人ID
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新人ID
     *
     * @return update_user - 更新人ID
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新人ID
     *
     * @param updateUser 更新人ID
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
    /**
     * 获取数据版本
     *
     * @return version - 数据版本
     */
    public Long getVersion() {
        return version;
    }

    /**
     * 设置数据版本
     *
     * @param version 数据版本
     */
    public void setVersion(Long version) {
        this.version = version;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", deliveryInfoId=").append(deliveryInfoId);
        sb.append(", parentInfoId=").append(parentInfoId);
        sb.append(", buyerId=").append(buyerId);
        sb.append(", buyerName=").append(buyerName);
        sb.append(", sellerId=").append(sellerId);
        sb.append(", sellerName=").append(sellerName);
        sb.append(", mdmCode=").append(mdmCode);
        sb.append(", takeCode=").append(takeCode);
        sb.append(", orderCode=").append(orderCode);
        sb.append(", orderItemId=").append(orderItemId);
        sb.append(", type=").append(type);
        sb.append(", relationType=").append(relationType);
        sb.append(", billProxyType=").append(billProxyType);
        sb.append(", canOperate=").append(canOperate);
        sb.append(", ifAgreeDispatch=").append(ifAgreeDispatch);
        sb.append(", basePreference=").append(basePreference);
        sb.append(", loadPortId=").append(loadPortId);
        sb.append(", loadPortName=").append(loadPortName);
        sb.append(", unloadPortId=").append(unloadPortId);
        sb.append(", unloadPortName=").append(unloadPortName);
        sb.append(", warehouseId=").append(warehouseId);
        sb.append(", warehouseType=").append(warehouseType);
        sb.append(", warehouseName=").append(warehouseName);
        sb.append(", warehouseProvince=").append(warehouseProvince);
        sb.append(", warehouseProvinceCode=").append(warehouseProvinceCode);
        sb.append(", warehouseCity=").append(warehouseCity);
        sb.append(", warehouseCityCode=").append(warehouseCityCode);
        sb.append(", warehouseDistrict=").append(warehouseDistrict);
        sb.append(", warehouseDistrictCode=").append(warehouseDistrictCode);
        sb.append(", warehouseStreet=").append(warehouseStreet);
        sb.append(", warehouseStreetCode=").append(warehouseStreetCode);
        sb.append(", warehouseAddress=").append(warehouseAddress);
        sb.append(", warehouseLocation=").append(warehouseLocation);
        sb.append(", receiverAddressId=").append(receiverAddressId);
        sb.append(", receiver=").append(receiver);
        sb.append(", receiverPhone=").append(receiverPhone);
        sb.append(", province=").append(province);
        sb.append(", provinceCode=").append(provinceCode);
        sb.append(", city=").append(city);
        sb.append(", cityCode=").append(cityCode);
        sb.append(", district=").append(district);
        sb.append(", districtCode=").append(districtCode);
        sb.append(", street=").append(street);
        sb.append(", streetCode=").append(streetCode);
        sb.append(", address=").append(address);
        sb.append(", location=").append(location);
        sb.append(", deliveryTime=").append(deliveryTime);
        sb.append(", deliveryTimeRange=").append(deliveryTimeRange);
        sb.append(", syncFlag=").append(syncFlag);
        sb.append(", transportToolType=").append(transportToolType);
        sb.append(", dealsId=").append(dealsId);
        sb.append(", dealsName=").append(dealsName);
        sb.append(", projectName=").append(projectName);
        sb.append(", dataSource=").append(dataSource);
        sb.append(", carryFlag=").append(carryFlag);
        sb.append(", heightLimit=").append(heightLimit);
        sb.append(", unloadRequireFlag=").append(unloadRequireFlag);
        sb.append(", carryRequireFlag=").append(carryRequireFlag);
        sb.append(", transportCategoryId=").append(transportCategoryId);
        sb.append(", commodityCode=").append(commodityCode);
        sb.append(", vendor=").append(vendor);
        sb.append(", productImg=").append(productImg);
        sb.append(", goodsId=").append(goodsId);
        sb.append(", note=").append(note);
        sb.append(", price=").append(price);
        sb.append(", unit=").append(unit);
        sb.append(", carriageUnitPrice=").append(carriageUnitPrice);
        sb.append(", unloadingFlowCode=").append(unloadingFlowCode);
        sb.append(", needMonitor=").append(needMonitor);
        sb.append(", signType=").append(signType);
        sb.append(", signer=").append(signer);
        sb.append(", signerPhone=").append(signerPhone);
        sb.append(", saleRegionPath=").append(saleRegionPath);
        sb.append(", salesmanId=").append(salesmanId);
        sb.append(", salesmanName=").append(salesmanName);
        sb.append(", specialFlag=").append(specialFlag);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", version=").append(version);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}
