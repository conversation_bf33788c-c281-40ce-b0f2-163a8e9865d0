package com.ecommerce.logistics.dao.vo;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

@Table(name = "lgs_delivery_note")
public class DeliveryNote implements Serializable {
    /**
     * 送货单主键
     */
    @Id
    @Column(name = "delivery_note_id")
    private String deliveryNoteId;

    /**
     * 送货单号
     */
    @Column(name = "delivery_seq")
    private String deliverySeq;

    /**
     * 卖家ID
     */
    @Column(name = "seller_id")
    private String sellerId;

    /**
     * 卖家名称
     */
    @Column(name = "seller_name")
    private String sellerName;

    /**
     * 状态
     */
    private String status;

    /**
     * 收货仓库
     */
    @Column(name = "receive_warehouse_id")
    private String receiveWarehouseId;

    /**
     * 收货仓库名
     */
    @Column(name = "receive_warehouse_name")
    private String receiveWarehouseName;

    /**
     * 收货仓位编号
     */
    @Column(name = "receive_storehouse_number")
    private String receiveStorehouseNumber;

    /**
     * 配送日期
     */
    @Column(name = "delivery_time")
    private Date deliveryTime;

    /**
     * 配送时段
     */
    @Column(name = "delivery_time_range")
    private String deliveryTimeRange;

    /**
     * 车辆ID
     */
    @Column(name = "vehicle_id")
    private String vehicleId;

    /**
     * 车牌号
     */
    @Column(name = "vehicle_number")
    private String vehicleNumber;

    /**
     * 配送人名
     */
    @Column(name = "delivery_name")
    private String deliveryName;

    /**
     * 配送人电话
     */
    @Column(name = "delivery_phone")
    private String deliveryPhone;

    /**
     * 签收时间
     */
    @Column(name = "sign_time")
    private Date signTime;

    /**
     * 删除标识
     */
    @Column(name = "del_flg")
    private Byte delFlg;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 数据版本
     */
    private Long version;

    private static final long serialVersionUID = 1L;

    /**
     * 获取送货单主键
     *
     * @return delivery_note_id - 送货单主键
     */
    public String getDeliveryNoteId() {
        return deliveryNoteId;
    }

    /**
     * 设置送货单主键
     *
     * @param deliveryNoteId 送货单主键
     */
    public void setDeliveryNoteId(String deliveryNoteId) {
        this.deliveryNoteId = deliveryNoteId == null ? null : deliveryNoteId.trim();
    }

    /**
     * 获取送货单号
     *
     * @return delivery_seq - 送货单号
     */
    public String getDeliverySeq() {
        return deliverySeq;
    }

    /**
     * 设置送货单号
     *
     * @param deliverySeq 送货单号
     */
    public void setDeliverySeq(String deliverySeq) {
        this.deliverySeq = deliverySeq == null ? null : deliverySeq.trim();
    }

    /**
     * 获取卖家ID
     *
     * @return seller_id - 卖家ID
     */
    public String getSellerId() {
        return sellerId;
    }

    /**
     * 设置卖家ID
     *
     * @param sellerId 卖家ID
     */
    public void setSellerId(String sellerId) {
        this.sellerId = sellerId == null ? null : sellerId.trim();
    }

    /**
     * 获取卖家名称
     *
     * @return seller_name - 卖家名称
     */
    public String getSellerName() {
        return sellerName;
    }

    /**
     * 设置卖家名称
     *
     * @param sellerName 卖家名称
     */
    public void setSellerName(String sellerName) {
        this.sellerName = sellerName == null ? null : sellerName.trim();
    }

    /**
     * 获取状态
     *
     * @return status - 状态
     */
    public String getStatus() {
        return status;
    }

    /**
     * 设置状态
     *
     * @param status 状态
     */
    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    /**
     * 获取收货仓库
     *
     * @return receive_warehouse_id - 收货仓库
     */
    public String getReceiveWarehouseId() {
        return receiveWarehouseId;
    }

    /**
     * 设置收货仓库
     *
     * @param receiveWarehouseId 收货仓库
     */
    public void setReceiveWarehouseId(String receiveWarehouseId) {
        this.receiveWarehouseId = receiveWarehouseId == null ? null : receiveWarehouseId.trim();
    }

    /**
     * 获取收货仓库名
     *
     * @return receive_warehouse_name - 收货仓库名
     */
    public String getReceiveWarehouseName() {
        return receiveWarehouseName;
    }

    /**
     * 设置收货仓库名
     *
     * @param receiveWarehouseName 收货仓库名
     */
    public void setReceiveWarehouseName(String receiveWarehouseName) {
        this.receiveWarehouseName = receiveWarehouseName == null ? null : receiveWarehouseName.trim();
    }

    /**
     * 获取收货仓位编号
     *
     * @return receive_storehouse_number - 收货仓位编号
     */
    public String getReceiveStorehouseNumber() {
        return receiveStorehouseNumber;
    }

    /**
     * 设置收货仓位编号
     *
     * @param receiveStorehouseNumber 收货仓位编号
     */
    public void setReceiveStorehouseNumber(String receiveStorehouseNumber) {
        this.receiveStorehouseNumber = receiveStorehouseNumber == null ? null : receiveStorehouseNumber.trim();
    }

    /**
     * 获取配送日期
     *
     * @return delivery_time - 配送日期
     */
    public Date getDeliveryTime() {
        return deliveryTime;
    }

    /**
     * 设置配送日期
     *
     * @param deliveryTime 配送日期
     */
    public void setDeliveryTime(Date deliveryTime) {
        this.deliveryTime = deliveryTime;
    }

    /**
     * 获取配送时段
     *
     * @return delivery_time_range - 配送时段
     */
    public String getDeliveryTimeRange() {
        return deliveryTimeRange;
    }

    /**
     * 设置配送时段
     *
     * @param deliveryTimeRange 配送时段
     */
    public void setDeliveryTimeRange(String deliveryTimeRange) {
        this.deliveryTimeRange = deliveryTimeRange == null ? null : deliveryTimeRange.trim();
    }

    /**
     * 获取车辆ID
     *
     * @return vehicle_id - 车辆ID
     */
    public String getVehicleId() {
        return vehicleId;
    }

    /**
     * 设置车辆ID
     *
     * @param vehicleId 车辆ID
     */
    public void setVehicleId(String vehicleId) {
        this.vehicleId = vehicleId == null ? null : vehicleId.trim();
    }

    /**
     * 获取车牌号
     *
     * @return vehicle_number - 车牌号
     */
    public String getVehicleNumber() {
        return vehicleNumber;
    }

    /**
     * 设置车牌号
     *
     * @param vehicleNumber 车牌号
     */
    public void setVehicleNumber(String vehicleNumber) {
        this.vehicleNumber = vehicleNumber == null ? null : vehicleNumber.trim();
    }

    /**
     * 获取配送人名
     *
     * @return delivery_name - 配送人名
     */
    public String getDeliveryName() {
        return deliveryName;
    }

    /**
     * 设置配送人名
     *
     * @param deliveryName 配送人名
     */
    public void setDeliveryName(String deliveryName) {
        this.deliveryName = deliveryName == null ? null : deliveryName.trim();
    }

    /**
     * 获取配送人电话
     *
     * @return delivery_phone - 配送人电话
     */
    public String getDeliveryPhone() {
        return deliveryPhone;
    }

    /**
     * 设置配送人电话
     *
     * @param deliveryPhone 配送人电话
     */
    public void setDeliveryPhone(String deliveryPhone) {
        this.deliveryPhone = deliveryPhone == null ? null : deliveryPhone.trim();
    }

    public Date getSignTime() {
        return signTime;
    }

    public void setSignTime(Date signTime) {
        this.signTime = signTime;
    }

    /**
     * 获取删除标识
     *
     * @return del_flg - 删除标识
     */
    public Byte getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标识
     *
     * @param delFlg 删除标识
     */
    public void setDelFlg(Byte delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取数据版本
     *
     * @return version - 数据版本
     */
    public Long getVersion() {
        return version;
    }

    /**
     * 设置数据版本
     *
     * @param version 数据版本
     */
    public void setVersion(Long version) {
        this.version = version;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", deliveryNoteId=").append(deliveryNoteId);
        sb.append(", deliverySeq=").append(deliverySeq);
        sb.append(", sellerId=").append(sellerId);
        sb.append(", sellerName=").append(sellerName);
        sb.append(", status=").append(status);
        sb.append(", receiveWarehouseId=").append(receiveWarehouseId);
        sb.append(", receiveWarehouseName=").append(receiveWarehouseName);
        sb.append(", receiveStorehouseNumber=").append(receiveStorehouseNumber);
        sb.append(", deliveryTime=").append(deliveryTime);
        sb.append(", deliveryTimeRange=").append(deliveryTimeRange);
        sb.append(", vehicleId=").append(vehicleId);
        sb.append(", vehicleNumber=").append(vehicleNumber);
        sb.append(", deliveryName=").append(deliveryName);
        sb.append(", deliveryPhone=").append(deliveryPhone);
        sb.append(", signTime=").append(signTime);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", version=").append(version);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}