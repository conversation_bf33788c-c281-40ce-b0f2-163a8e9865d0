package com.ecommerce.logistics.dao.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;

@Table(name = "lgs_delivery_send_map")
public class DeliverySendMap implements Serializable {
    /**
     * 主键
     */
    @Id
    @Column(name = "map_id")
    private String mapId;

    /**
     * 商品信息ID
     */
    @Column(name = "product_info_id")
    private String productInfoId;

    /**
     * 发货单ID
     */
    @Column(name = "delivery_note_id")
    private String deliveryNoteId;

    /**
     * 发送仓库ID
     */
    @Column(name = "send_warehouse_id")
    private String sendWarehouseId;

    /**
     * 发送仓库名
     */
    @Column(name = "send_warehouse_name")
    private String sendWarehouseName;

    /**
     * 发送仓位编码
     */
    @Column(name = "send_storehouse_number")
    private String sendStorehouseNumber;

    /**
     * 出厂顺序
     */
    @Column(name = "send_order")
    private Integer sendOrder;

    /**
     * 发送数量
     */
    @Column(name = "send_quantity")
    private BigDecimal sendQuantity;

    /**
     * 出厂数量
     */
    @Column(name = "leave_quantity")
    private BigDecimal leaveQuantity;

    /**
     * 签收数量
     */
    @Column(name = "sign_quantity")
    private BigDecimal signQuantity;

    /**
     * 入库数量
     */
    @Column(name = "unload_quantity")
    private BigDecimal unloadQuantity;

    /**
     * 是否签收
     */
    private String status;

    /**
     * 删除标识
     */
    @Column(name = "del_flg")
    private Byte delFlg;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 数据版本
     */
    private Long version;

    private static final long serialVersionUID = 1L;

    /**
     * 获取主键
     *
     * @return map_id - 主键
     */
    public String getMapId() {
        return mapId;
    }

    /**
     * 设置主键
     *
     * @param mapId 主键
     */
    public void setMapId(String mapId) {
        this.mapId = mapId == null ? null : mapId.trim();
    }

    /**
     * 获取商品信息ID
     *
     * @return product_info_id - 商品信息ID
     */
    public String getProductInfoId() {
        return productInfoId;
    }

    /**
     * 设置商品信息ID
     *
     * @param productInfoId 商品信息ID
     */
    public void setProductInfoId(String productInfoId) {
        this.productInfoId = productInfoId == null ? null : productInfoId.trim();
    }

    /**
     * 获取发货单ID
     *
     * @return delivery_note_id - 发货单ID
     */
    public String getDeliveryNoteId() {
        return deliveryNoteId;
    }

    /**
     * 设置发货单ID
     *
     * @param deliveryNoteId 发货单ID
     */
    public void setDeliveryNoteId(String deliveryNoteId) {
        this.deliveryNoteId = deliveryNoteId == null ? null : deliveryNoteId.trim();
    }

    /**
     * 获取发送仓库ID
     *
     * @return send_warehouse_id - 发送仓库ID
     */
    public String getSendWarehouseId() {
        return sendWarehouseId;
    }

    /**
     * 设置发送仓库ID
     *
     * @param sendWarehouseId 发送仓库ID
     */
    public void setSendWarehouseId(String sendWarehouseId) {
        this.sendWarehouseId = sendWarehouseId == null ? null : sendWarehouseId.trim();
    }

    /**
     * 获取发送仓库名
     *
     * @return send_warehouse_name - 发送仓库名
     */
    public String getSendWarehouseName() {
        return sendWarehouseName;
    }

    /**
     * 设置发送仓库名
     *
     * @param sendWarehouseName 发送仓库名
     */
    public void setSendWarehouseName(String sendWarehouseName) {
        this.sendWarehouseName = sendWarehouseName == null ? null : sendWarehouseName.trim();
    }

    /**
     * 获取发送仓位编码
     *
     * @return send_storehouse_number - 发送仓位编码
     */
    public String getSendStorehouseNumber() {
        return sendStorehouseNumber;
    }

    /**
     * 设置发送仓位编码
     *
     * @param sendStorehouseNumber 发送仓位编码
     */
    public void setSendStorehouseNumber(String sendStorehouseNumber) {
        this.sendStorehouseNumber = sendStorehouseNumber == null ? null : sendStorehouseNumber.trim();
    }

    /**
     * 获取出厂顺序
     *
     * @return send_order - 出厂顺序
     */
    public Integer getSendOrder() {
        return sendOrder;
    }

    /**
     * 设置出厂顺序
     *
     * @param sendOrder 出厂顺序
     */
    public void setSendOrder(Integer sendOrder) {
        this.sendOrder = sendOrder;
    }

    /**
     * 获取发送数量
     *
     * @return send_quantity - 发送数量
     */
    public BigDecimal getSendQuantity() {
        return sendQuantity;
    }

    /**
     * 设置发送数量
     *
     * @param sendQuantity 发送数量
     */
    public void setSendQuantity(BigDecimal sendQuantity) {
        this.sendQuantity = sendQuantity;
    }

    public BigDecimal getLeaveQuantity() {
        return leaveQuantity;
    }

    public void setLeaveQuantity(BigDecimal leaveQuantity) {
        this.leaveQuantity = leaveQuantity;
    }

    public BigDecimal getSignQuantity() {
        return signQuantity;
    }

    public void setSignQuantity(BigDecimal signQuantity) {
        this.signQuantity = signQuantity;
    }

    public BigDecimal getUnloadQuantity() {
        return unloadQuantity;
    }

    public void setUnloadQuantity(BigDecimal unloadQuantity) {
        this.unloadQuantity = unloadQuantity;
    }

    /**
     * 获取删除标识
     *
     * @return del_flg - 删除标识
     */
    public Byte getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标识
     *
     * @param delFlg 删除标识
     */
    public void setDelFlg(Byte delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取数据版本
     *
     * @return version - 数据版本
     */
    public Long getVersion() {
        return version;
    }


    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status == null ? "" : status.trim();
    }

    /**
     * 设置数据版本
     *
     * @param version 数据版本
     */
    public void setVersion(Long version) {
        this.version = version;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", mapId=").append(mapId);
        sb.append(", productInfoId=").append(productInfoId);
        sb.append(", deliveryNoteId=").append(deliveryNoteId);
        sb.append(", sendWarehouseId=").append(sendWarehouseId);
        sb.append(", sendWarehouseName=").append(sendWarehouseName);
        sb.append(", sendStorehouseNumber=").append(sendStorehouseNumber);
        sb.append(", sendOrder=").append(sendOrder);
        sb.append(", sendQuantity=").append(sendQuantity);
        sb.append(", leaveQuantity=").append(leaveQuantity);
        sb.append(", signQuantity=").append(signQuantity);
        sb.append(", unloadQuantity=").append(unloadQuantity);
        sb.append(", status=").append(status);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", version=").append(version);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}