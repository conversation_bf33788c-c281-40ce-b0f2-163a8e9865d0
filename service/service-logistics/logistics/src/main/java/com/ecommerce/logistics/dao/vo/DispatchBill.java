package com.ecommerce.logistics.dao.vo;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Table(name = "lgs_dispatch_bill")
public class DispatchBill implements Serializable {
    /**
     * 调度单ID
     */
    @Id
    @Column(name = "dispatch_bill_id")
    private String dispatchBillId;

    /**
     * 调度单号
     */
    @Column(name = "dispatch_bill_num")
    private String dispatchBillNum;

    /**
     * 提货单ID
     */
    @Column(name = "picking_bill_id")
    private String pickingBillId;

    /**
     * 状态
     */
    private String status;

    /**
     * 结束原因
     */
    @Column(name = "end_reason")
    private String endReason;

    /**
     * 承运商ID
     */
    @Column(name = "carrier_id")
    private String carrierId;

    /**
     * 承运商名称
     */
    @Column(name = "carrier_name")
    private String carrierName;

    /**
     * 承运商类型
     */
    @Column(name = "carrier_type")
    private String carrierType;

    /**
     * 收入运费
     */
    @Column(name = "income_carriage")
    private BigDecimal incomeCarriage;

    /**
     * 提货时间
     */
    @Column(name = "picking_time")
    private Date pickingTime;

    /**
     * 支出运费
     */
    @Column(name = "expend_carriage")
    private BigDecimal expendCarriage;

    /**
     * 同步标识:1未同步 2: 正向同步 3:反向同步
     */
    @Column(name = "sync_flag")
    private String syncFlag;

    /**
     * 票据来源类型: 买家; 卖家; 平台
     */
    @Column(name = "source_type")
    private String sourceType;

    /**
     * 票据来源用户ID
     */
    @Column(name = "source_user_id")
    private String sourceUserId;

    /**
     * 平台是否评价（0-未评价，1-已评价）
     */
    @Column(name = "platform_evaluate_flag")
    private Byte platformEvaluateFlag;

    /**
     * 承运商是否评价（0-未评价，1-已评价）
     */
    @Column(name = "carrier_evaluate_flag")
    private Byte carrierEvaluateFlag;

    /**
     * 删除标识
     */
    @Column(name = "del_flg")
    private Byte delFlg;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 数据版本
     */
    private Long version;

    private static final long serialVersionUID = 1L;

    /**
     * 获取调度单ID
     *
     * @return dispatch_bill_id - 调度单ID
     */
    public String getDispatchBillId() {
        return dispatchBillId;
    }

    /**
     * 设置调度单ID
     *
     * @param dispatchBillId 调度单ID
     */
    public void setDispatchBillId(String dispatchBillId) {
        this.dispatchBillId = dispatchBillId == null ? null : dispatchBillId.trim();
    }

    /**
     * 获取调度单号
     *
     * @return dispatch_bill_num - 调度单号
     */
    public String getDispatchBillNum() {
        return dispatchBillNum;
    }

    /**
     * 设置调度单号
     *
     * @param dispatchBillNum 调度单号
     */
    public void setDispatchBillNum(String dispatchBillNum) {
        this.dispatchBillNum = dispatchBillNum == null ? null : dispatchBillNum.trim();
    }

    /**
     * 获取提货单ID
     *
     * @return picking_bill_id - 提货单ID
     */
    public String getPickingBillId() {
        return pickingBillId;
    }

    /**
     * 设置提货单ID
     *
     * @param pickingBillId 提货单ID
     */
    public void setPickingBillId(String pickingBillId) {
        this.pickingBillId = pickingBillId == null ? null : pickingBillId.trim();
    }

    /**
     * 获取状态
     *
     * @return status - 状态
     */
    public String getStatus() {
        return status;
    }

    /**
     * 设置状态
     *
     * @param status 状态
     */
    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    public String getEndReason() {
        return endReason;
    }

    public void setEndReason(String endReason) {
        this.endReason = endReason == null ? null : endReason.trim();
    }

    /**
     * 获取承运商ID
     *
     * @return carrier_id - 承运商ID
     */
    public String getCarrierId() {
        return carrierId;
    }

    /**
     * 设置承运商ID
     *
     * @param carrierId 承运商ID
     */
    public void setCarrierId(String carrierId) {
        this.carrierId = carrierId == null ? null : carrierId.trim();
    }

    /**
     * 获取承运商名称
     *
     * @return carrier_name - 承运商名称
     */
    public String getCarrierName() {
        return carrierName;
    }

    /**
     * 设置承运商名称
     *
     * @param carrierName 承运商名称
     */
    public void setCarrierName(String carrierName) {
        this.carrierName = carrierName == null ? null : carrierName.trim();
    }

    /**
     * 获取承运商类型
     *
     * @return carrier_type - 承运商类型
     */
    public String getCarrierType() {
        return carrierType;
    }

    /**
     * 设置承运商类型
     *
     * @param carrierType 承运商类型
     */
    public void setCarrierType(String carrierType) {
        this.carrierType = carrierType == null ? null : carrierType.trim();
    }

    /**
     * 获取收入运费
     *
     * @return income_carriage - 收入运费
     */
    public BigDecimal getIncomeCarriage() {
        return incomeCarriage;
    }

    /**
     * 设置收入运费
     *
     * @param incomeCarriage 收入运费
     */
    public void setIncomeCarriage(BigDecimal incomeCarriage) {
        this.incomeCarriage = incomeCarriage;
    }

    /**
     * 获取提货时间
     *
     * @return picking_time - 提货时间
     */
    public Date getPickingTime() {
        return pickingTime;
    }

    /**
     * 设置提货时间
     *
     * @param pickingTime 提货时间
     */
    public void setPickingTime(Date pickingTime) {
        this.pickingTime = pickingTime;
    }

    /**
     * 获取支出运费
     *
     * @return expend_carriage - 支出运费
     */
    public BigDecimal getExpendCarriage() {
        return expendCarriage;
    }

    /**
     * 设置支出运费
     *
     * @param expendCarriage 支出运费
     */
    public void setExpendCarriage(BigDecimal expendCarriage) {
        this.expendCarriage = expendCarriage;
    }

    /**
     * 获取同步标识:1未同步 2: 正向同步 3:反向同步
     *
     * @return sync_flag - 同步标识:1未同步 2: 正向同步 3:反向同步
     */
    public String getSyncFlag() {
        return syncFlag;
    }

    /**
     * 设置同步标识:1未同步 2: 正向同步 3:反向同步
     *
     * @param syncFlag 同步标识:1未同步 2: 正向同步 3:反向同步
     */
    public void setSyncFlag(String syncFlag) {
        this.syncFlag = syncFlag == null ? null : syncFlag.trim();
    }

    public String getSourceType() {
        return sourceType;
    }

    public void setSourceType(String sourceType) {
        this.sourceType = sourceType == null ? null : sourceType.trim();
    }

    public String getSourceUserId() {
        return sourceUserId;
    }

    public void setSourceUserId(String sourceUserId) {
        this.sourceUserId = sourceUserId == null ? null : sourceUserId.trim();
    }

    public Byte getPlatformEvaluateFlag() {
        return platformEvaluateFlag;
    }

    public void setPlatformEvaluateFlag(Byte platformEvaluateFlag) {
        this.platformEvaluateFlag = platformEvaluateFlag;
    }

    public Byte getCarrierEvaluateFlag() {
        return carrierEvaluateFlag;
    }

    public void setCarrierEvaluateFlag(Byte carrierEvaluateFlag) {
        this.carrierEvaluateFlag = carrierEvaluateFlag;
    }

    /**
     * 获取删除标识
     *
     * @return del_flg - 删除标识
     */
    public Byte getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标识
     *
     * @param delFlg 删除标识
     */
    public void setDelFlg(Byte delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取数据版本
     *
     * @return version - 数据版本
     */
    public Long getVersion() {
        return version;
    }

    /**
     * 设置数据版本
     *
     * @param version 数据版本
     */
    public void setVersion(Long version) {
        this.version = version;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", dispatchBillId=").append(dispatchBillId);
        sb.append(", dispatchBillNum=").append(dispatchBillNum);
        sb.append(", pickingBillId=").append(pickingBillId);
        sb.append(", status=").append(status);
        sb.append(", endReason=").append(endReason);
        sb.append(", carrierId=").append(carrierId);
        sb.append(", carrierName=").append(carrierName);
        sb.append(", carrierType=").append(carrierType);
        sb.append(", incomeCarriage=").append(incomeCarriage);
        sb.append(", pickingTime=").append(pickingTime);
        sb.append(", expendCarriage=").append(expendCarriage);
        sb.append(", syncFlag=").append(syncFlag);
        sb.append(", sourceType=").append(sourceType);
        sb.append(", sourceUserId=").append(sourceUserId);
        sb.append(", platformEvaluateFlag=").append(platformEvaluateFlag);
        sb.append(", carrierEvaluateFlag=").append(carrierEvaluateFlag);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", version=").append(version);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}