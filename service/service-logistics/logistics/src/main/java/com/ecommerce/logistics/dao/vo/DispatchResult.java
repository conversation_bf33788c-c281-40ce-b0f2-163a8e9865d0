package com.ecommerce.logistics.dao.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;

@Table(name = "lgs_dispatch_result")
public class DispatchResult implements Serializable {
    /**
     * 调度结果ID
     */
    @Id
    @Column(name = "dispatch_result_id")
    private String dispatchResultId;

    /**
     * 批次ID
     */
    @Column(name = "batch_id")
    private String batchId;

    /**
     * 委托单ID
     */
    @Column(name = "delivery_bill_id")
    private String deliveryBillId;

    /**
     * 委托单号
     */
    @Column(name = "delivery_bill_num")
    private String deliveryBillNum;

    /**
     * 车辆ID
     */
    @Column(name = "vehicle_id")
    private String vehicleId;

    /**
     * 车牌号
     */
    @Column(name = "vehicle_num")
    private String vehicleNum;

    /**
     * 司机ID
     */
    @Column(name = "driver_id")
    private String driverId;

    /**
     * 司机名
     */
    @Column(name = "driver_name")
    private String driverName;

    @Column(name = "driver_phone")
    private String driverPhone;

    /**
     * 期望配送时间
     */
    @Column(name = "delivery_time")
    private Date deliveryTime;

    /**
     * 开始时间
     */
    @Column(name = "delivery_time_start")
    private Date deliveryTimeStart;

    /**
     * 结束时间
     */
    @Column(name = "delivery_time_end")
    private Date deliveryTimeEnd;

    /**
     * 距离
     */
    @Column(name = "km_distance")
    private BigDecimal kmDistance;

    /**
     * 最大载重
     */
    @Column(name = "max_load_capacity")
    private BigDecimal maxLoadCapacity;

    /**
     * 指派量
     */
    private BigDecimal quantity;

    /**
     * 商品ID
     */
    @Column(name = "goods_id")
    private String goodsId;

    /**
     * 商品名称
     */
    @Column(name = "goods_name")
    private String goodsName;

    /**
     * 单位
     */
    private String unit;

    /**
     * 车型ID
     */
    @Column(name = "vehicle_type_id")
    private String vehicleTypeId;

    /**
     * 车型名称
     */
    @Column(name = "vehicle_type_name")
    private String vehicleTypeName;

    /**
     * 仓库ID
     */
    @Column(name = "warehouse_id")
    private String warehouseId;

    /**
     * 仓库名称
     */
    @Column(name = "warehouse_name")
    private String warehouseName;

    /**
     * 仓库地址
     */
    @Column(name = "warehouse_address")
    private String warehouseAddress;

    /**
     * 收货地址ID
     */
    @Column(name = "receiver_address_id")
    private String receiverAddressId;

    /**
     * 收货地址
     */
    private String address;

    /**
     * 预估运距
     */
    @Column(name = "estimate_km")
    private BigDecimal estimateKm;

    /**
     * 预估时间
     */
    @Column(name = "estimate_duration")
    private BigDecimal estimateDuration;

    /**
     * 空跑距离
     */
    @Column(name = "no_load_km")
    private BigDecimal noLoadKm;

    /**
     * 装货时间
     */
    @Column(name = "loading_time")
    private BigDecimal loadingTime;

    /**
     * 卸货时间
     */
    @Column(name = "unloading_time")
    private BigDecimal unloadingTime;

    /**
     * 调度结果状态
     */
    private String status;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * del_flg
     */
    @Column(name = "del_flg")
    private Byte delFlg;

    private static final long serialVersionUID = 1L;

    /**
     * 获取调度结果ID
     *
     * @return dispatch_result_id - 调度结果ID
     */
    public String getDispatchResultId() {
        return dispatchResultId;
    }

    /**
     * 设置调度结果ID
     *
     * @param dispatchResultId 调度结果ID
     */
    public void setDispatchResultId(String dispatchResultId) {
        this.dispatchResultId = dispatchResultId == null ? null : dispatchResultId.trim();
    }

    /**
     * 获取批次ID
     *
     * @return batch_id - 批次ID
     */
    public String getBatchId() {
        return batchId;
    }

    /**
     * 设置批次ID
     *
     * @param batchId 批次ID
     */
    public void setBatchId(String batchId) {
        this.batchId = batchId == null ? null : batchId.trim();
    }

    /**
     * 获取委托单ID
     *
     * @return delivery_bill_id - 委托单ID
     */
    public String getDeliveryBillId() {
        return deliveryBillId;
    }

    /**
     * 设置委托单ID
     *
     * @param deliveryBillId 委托单ID
     */
    public void setDeliveryBillId(String deliveryBillId) {
        this.deliveryBillId = deliveryBillId == null ? null : deliveryBillId.trim();
    }

    /**
     * 获取委托单号
     *
     * @return delivery_bill_num - 委托单号
     */
    public String getDeliveryBillNum() {
        return deliveryBillNum;
    }

    /**
     * 设置委托单号
     *
     * @param deliveryBillNum 委托单号
     */
    public void setDeliveryBillNum(String deliveryBillNum) {
        this.deliveryBillNum = deliveryBillNum == null ? null : deliveryBillNum.trim();
    }

    /**
     * 获取车辆ID
     *
     * @return vehicle_id - 车辆ID
     */
    public String getVehicleId() {
        return vehicleId;
    }

    /**
     * 设置车辆ID
     *
     * @param vehicleId 车辆ID
     */
    public void setVehicleId(String vehicleId) {
        this.vehicleId = vehicleId == null ? null : vehicleId.trim();
    }

    /**
     * 获取车牌号
     *
     * @return vehicle_num - 车牌号
     */
    public String getVehicleNum() {
        return vehicleNum;
    }

    /**
     * 设置车牌号
     *
     * @param vehicleNum 车牌号
     */
    public void setVehicleNum(String vehicleNum) {
        this.vehicleNum = vehicleNum == null ? null : vehicleNum.trim();
    }

    /**
     * 获取司机ID
     *
     * @return driver_id - 司机ID
     */
    public String getDriverId() {
        return driverId;
    }

    /**
     * 设置司机ID
     *
     * @param driverId 司机ID
     */
    public void setDriverId(String driverId) {
        this.driverId = driverId == null ? null : driverId.trim();
    }

    /**
     * 获取司机名
     *
     * @return driver_name - 司机名
     */
    public String getDriverName() {
        return driverName;
    }

    /**
     * 设置司机名
     *
     * @param driverName 司机名
     */
    public void setDriverName(String driverName) {
        this.driverName = driverName == null ? null : driverName.trim();
    }

    /**
     * @return driver_phone
     */
    public String getDriverPhone() {
        return driverPhone;
    }

    /**
     * @param driverPhone
     */
    public void setDriverPhone(String driverPhone) {
        this.driverPhone = driverPhone == null ? null : driverPhone.trim();
    }

    /**
     * 获取期望配送时间
     *
     * @return delivery_time - 期望配送时间
     */
    public Date getDeliveryTime() {
        return deliveryTime;
    }

    /**
     * 设置期望配送时间
     *
     * @param deliveryTime 期望配送时间
     */
    public void setDeliveryTime(Date deliveryTime) {
        this.deliveryTime = deliveryTime;
    }

    /**
     * 获取开始时间
     *
     * @return delivery_time_start - 开始时间
     */
    public Date getDeliveryTimeStart() {
        return deliveryTimeStart;
    }

    /**
     * 设置开始时间
     *
     * @param deliveryTimeStart 开始时间
     */
    public void setDeliveryTimeStart(Date deliveryTimeStart) {
        this.deliveryTimeStart = deliveryTimeStart;
    }

    /**
     * 获取结束时间
     *
     * @return delivery_time_end - 结束时间
     */
    public Date getDeliveryTimeEnd() {
        return deliveryTimeEnd;
    }

    /**
     * 设置结束时间
     *
     * @param deliveryTimeEnd 结束时间
     */
    public void setDeliveryTimeEnd(Date deliveryTimeEnd) {
        this.deliveryTimeEnd = deliveryTimeEnd;
    }

    /**
     * 获取距离
     *
     * @return km_distance - 距离
     */
    public BigDecimal getKmDistance() {
        return kmDistance;
    }

    /**
     * 设置距离
     *
     * @param kmDistance 距离
     */
    public void setKmDistance(BigDecimal kmDistance) {
        this.kmDistance = kmDistance;
    }

    /**
     * 获取最大载重
     *
     * @return max_load_capacity - 最大载重
     */
    public BigDecimal getMaxLoadCapacity() {
        return maxLoadCapacity;
    }

    /**
     * 设置最大载重
     *
     * @param maxLoadCapacity 最大载重
     */
    public void setMaxLoadCapacity(BigDecimal maxLoadCapacity) {
        this.maxLoadCapacity = maxLoadCapacity;
    }

    /**
     * 获取指派量
     *
     * @return quantity - 指派量
     */
    public BigDecimal getQuantity() {
        return quantity;
    }

    /**
     * 设置指派量
     *
     * @param quantity 指派量
     */
    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    /**
     * 获取商品ID
     *
     * @return goods_id - 商品ID
     */
    public String getGoodsId() {
        return goodsId;
    }

    /**
     * 设置商品ID
     *
     * @param goodsId 商品ID
     */
    public void setGoodsId(String goodsId) {
        this.goodsId = goodsId == null ? null : goodsId.trim();
    }

    /**
     * 获取商品名称
     *
     * @return goods_name - 商品名称
     */
    public String getGoodsName() {
        return goodsName;
    }

    /**
     * 设置商品名称
     *
     * @param goodsName 商品名称
     */
    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName == null ? null : goodsName.trim();
    }

    /**
     * 获取单位
     *
     * @return unit - 单位
     */
    public String getUnit() {
        return unit;
    }

    /**
     * 设置单位
     *
     * @param unit 单位
     */
    public void setUnit(String unit) {
        this.unit = unit == null ? null : unit.trim();
    }

    /**
     * 获取车型ID
     *
     * @return vehicle_type_id - 车型ID
     */
    public String getVehicleTypeId() {
        return vehicleTypeId;
    }

    /**
     * 设置车型ID
     *
     * @param vehicleTypeId 车型ID
     */
    public void setVehicleTypeId(String vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId == null ? null : vehicleTypeId.trim();
    }

    /**
     * 获取车型名称
     *
     * @return vehicle_type_name - 车型名称
     */
    public String getVehicleTypeName() {
        return vehicleTypeName;
    }

    /**
     * 设置车型名称
     *
     * @param vehicleTypeName 车型名称
     */
    public void setVehicleTypeName(String vehicleTypeName) {
        this.vehicleTypeName = vehicleTypeName == null ? null : vehicleTypeName.trim();
    }

    /**
     * 获取仓库ID
     *
     * @return warehouse_id - 仓库ID
     */
    public String getWarehouseId() {
        return warehouseId;
    }

    /**
     * 设置仓库ID
     *
     * @param warehouseId 仓库ID
     */
    public void setWarehouseId(String warehouseId) {
        this.warehouseId = warehouseId == null ? null : warehouseId.trim();
    }

    /**
     * 获取仓库名称
     *
     * @return warehouse_name - 仓库名称
     */
    public String getWarehouseName() {
        return warehouseName;
    }

    /**
     * 设置仓库名称
     *
     * @param warehouseName 仓库名称
     */
    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName == null ? null : warehouseName.trim();
    }

    /**
     * 获取仓库地址
     *
     * @return warehouse_address - 仓库地址
     */
    public String getWarehouseAddress() {
        return warehouseAddress;
    }

    /**
     * 设置仓库地址
     *
     * @param warehouseAddress 仓库地址
     */
    public void setWarehouseAddress(String warehouseAddress) {
        this.warehouseAddress = warehouseAddress == null ? null : warehouseAddress.trim();
    }

    /**
     * 获取收货地址ID
     *
     * @return receiver_address_id - 收货地址ID
     */
    public String getReceiverAddressId() {
        return receiverAddressId;
    }

    /**
     * 设置收货地址ID
     *
     * @param receiverAddressId 收货地址ID
     */
    public void setReceiverAddressId(String receiverAddressId) {
        this.receiverAddressId = receiverAddressId == null ? null : receiverAddressId.trim();
    }

    /**
     * 获取收货地址
     *
     * @return address - 收货地址
     */
    public String getAddress() {
        return address;
    }

    /**
     * 设置收货地址
     *
     * @param address 收货地址
     */
    public void setAddress(String address) {
        this.address = address == null ? null : address.trim();
    }

    /**
     * 获取预估运距
     *
     * @return estimate_km - 预估运距
     */
    public BigDecimal getEstimateKm() {
        return estimateKm;
    }

    /**
     * 设置预估运距
     *
     * @param estimateKm 预估运距
     */
    public void setEstimateKm(BigDecimal estimateKm) {
        this.estimateKm = estimateKm;
    }

    /**
     * 获取预估时间
     *
     * @return estimate_duration - 预估时间
     */
    public BigDecimal getEstimateDuration() {
        return estimateDuration;
    }

    /**
     * 设置预估时间
     *
     * @param estimateDuration 预估时间
     */
    public void setEstimateDuration(BigDecimal estimateDuration) {
        this.estimateDuration = estimateDuration;
    }

    /**
     * 获取空跑距离
     *
     * @return no_load_km - 空跑距离
     */
    public BigDecimal getNoLoadKm() {
        return noLoadKm;
    }

    /**
     * 设置空跑距离
     *
     * @param noLoadKm 空跑距离
     */
    public void setNoLoadKm(BigDecimal noLoadKm) {
        this.noLoadKm = noLoadKm;
    }

    /**
     * 获取装货时间
     *
     * @return loading_time - 装货时间
     */
    public BigDecimal getLoadingTime() {
        return loadingTime;
    }

    /**
     * 设置装货时间
     *
     * @param loadingTime 装货时间
     */
    public void setLoadingTime(BigDecimal loadingTime) {
        this.loadingTime = loadingTime;
    }

    /**
     * 获取卸货时间
     *
     * @return unloading_time - 卸货时间
     */
    public BigDecimal getUnloadingTime() {
        return unloadingTime;
    }

    /**
     * 设置卸货时间
     *
     * @param unloadingTime 卸货时间
     */
    public void setUnloadingTime(BigDecimal unloadingTime) {
        this.unloadingTime = unloadingTime;
    }

    /**
     * 获取调度结果状态
     *
     * @return status - 调度结果状态
     */
    public String getStatus() {
        return status;
    }

    /**
     * 设置调度结果状态
     *
     * @param status 调度结果状态
     */
    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取del_flg
     *
     * @return del_flg - del_flg
     */
    public Byte getDelFlg() {
        return delFlg;
    }

    /**
     * 设置del_flg
     *
     * @param delFlg del_flg
     */
    public void setDelFlg(Byte delFlg) {
        this.delFlg = delFlg;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", dispatchResultId=").append(dispatchResultId);
        sb.append(", batchId=").append(batchId);
        sb.append(", deliveryBillId=").append(deliveryBillId);
        sb.append(", deliveryBillNum=").append(deliveryBillNum);
        sb.append(", vehicleId=").append(vehicleId);
        sb.append(", vehicleNum=").append(vehicleNum);
        sb.append(", driverId=").append(driverId);
        sb.append(", driverName=").append(driverName);
        sb.append(", driverPhone=").append(driverPhone);
        sb.append(", deliveryTime=").append(deliveryTime);
        sb.append(", deliveryTimeStart=").append(deliveryTimeStart);
        sb.append(", deliveryTimeEnd=").append(deliveryTimeEnd);
        sb.append(", kmDistance=").append(kmDistance);
        sb.append(", maxLoadCapacity=").append(maxLoadCapacity);
        sb.append(", quantity=").append(quantity);
        sb.append(", goodsId=").append(goodsId);
        sb.append(", goodsName=").append(goodsName);
        sb.append(", unit=").append(unit);
        sb.append(", vehicleTypeId=").append(vehicleTypeId);
        sb.append(", vehicleTypeName=").append(vehicleTypeName);
        sb.append(", warehouseId=").append(warehouseId);
        sb.append(", warehouseName=").append(warehouseName);
        sb.append(", warehouseAddress=").append(warehouseAddress);
        sb.append(", receiverAddressId=").append(receiverAddressId);
        sb.append(", address=").append(address);
        sb.append(", estimateKm=").append(estimateKm);
        sb.append(", estimateDuration=").append(estimateDuration);
        sb.append(", noLoadKm=").append(noLoadKm);
        sb.append(", loadingTime=").append(loadingTime);
        sb.append(", unloadingTime=").append(unloadingTime);
        sb.append(", status=").append(status);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}