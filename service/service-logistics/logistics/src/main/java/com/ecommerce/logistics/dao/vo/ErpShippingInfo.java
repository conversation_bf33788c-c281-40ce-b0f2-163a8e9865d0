package com.ecommerce.logistics.dao.vo;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

@Table(name = "lgs_erp_shipping_info")
public class ErpShippingInfo implements Serializable {
    /**
     * 表id
     */
    @Id
    private String id;

    /**
     * 会员ID
     */
    @Column(name = "member_id")
    private String memberId;

    /**
     * ERP承运商编号
     */
    @Column(name = "erp_carrier_no")
    private String erpCarrierNo;

    /**
     * ERP船舶名称
     */
    @Column(name = "erp_ship_name")
    private String erpShipName;

    /**
     * ERP船舶编号
     */
    @Column(name = "erp_ship_code")
    private String erpShipCode;

    /**
     * 预留字段1
     */
    private String extend1;

    /**
     * 预留字段2
     */
    private String extend2;

    /**
     * 预留字段3
     */
    private String extend3;

    /**
     * 预留字段4
     */
    private String extend4;

    /**
     * 预留字段5
     */
    private String extend5;

    /**
     * 删除标识,0-未删除，1-删除
     */
    @Column(name = "del_flg")
    private Integer delFlg;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 上次同步时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;

    /**
     * 获取表id
     *
     * @return id - 表id
     */
    public String getId() {
        return id;
    }

    /**
     * 设置表id
     *
     * @param id 表id
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * 获取会员ID
     *
     * @return member_id - 会员ID
     */
    public String getMemberId() {
        return memberId;
    }

    /**
     * 设置会员ID
     *
     * @param memberId 会员ID
     */
    public void setMemberId(String memberId) {
        this.memberId = memberId == null ? null : memberId.trim();
    }

    /**
     * 获取ERP承运商编号
     *
     * @return erp_carrier_no - ERP承运商编号
     */
    public String getErpCarrierNo() {
        return erpCarrierNo;
    }

    /**
     * 设置ERP承运商编号
     *
     * @param erpCarrierNo ERP承运商编号
     */
    public void setErpCarrierNo(String erpCarrierNo) {
        this.erpCarrierNo = erpCarrierNo == null ? null : erpCarrierNo.trim();
    }

    /**
     * 获取ERP船舶名称
     *
     * @return erp_ship_name - ERP船舶名称
     */
    public String getErpShipName() {
        return erpShipName;
    }

    /**
     * 设置ERP船舶名称
     *
     * @param erpShipName ERP船舶名称
     */
    public void setErpShipName(String erpShipName) {
        this.erpShipName = erpShipName == null ? null : erpShipName.trim();
    }

    /**
     * 获取ERP船舶编号
     *
     * @return erp_ship_code - ERP船舶编号
     */
    public String getErpShipCode() {
        return erpShipCode;
    }

    /**
     * 设置ERP船舶编号
     *
     * @param erpShipCode ERP船舶编号
     */
    public void setErpShipCode(String erpShipCode) {
        this.erpShipCode = erpShipCode == null ? null : erpShipCode.trim();
    }

    /**
     * 获取预留字段1
     *
     * @return extend1 - 预留字段1
     */
    public String getExtend1() {
        return extend1;
    }

    /**
     * 设置预留字段1
     *
     * @param extend1 预留字段1
     */
    public void setExtend1(String extend1) {
        this.extend1 = extend1 == null ? null : extend1.trim();
    }

    /**
     * 获取预留字段2
     *
     * @return extend2 - 预留字段2
     */
    public String getExtend2() {
        return extend2;
    }

    /**
     * 设置预留字段2
     *
     * @param extend2 预留字段2
     */
    public void setExtend2(String extend2) {
        this.extend2 = extend2 == null ? null : extend2.trim();
    }

    /**
     * 获取预留字段3
     *
     * @return extend3 - 预留字段3
     */
    public String getExtend3() {
        return extend3;
    }

    /**
     * 设置预留字段3
     *
     * @param extend3 预留字段3
     */
    public void setExtend3(String extend3) {
        this.extend3 = extend3 == null ? null : extend3.trim();
    }

    /**
     * 获取预留字段4
     *
     * @return extend4 - 预留字段4
     */
    public String getExtend4() {
        return extend4;
    }

    /**
     * 设置预留字段4
     *
     * @param extend4 预留字段4
     */
    public void setExtend4(String extend4) {
        this.extend4 = extend4 == null ? null : extend4.trim();
    }

    /**
     * 获取预留字段5
     *
     * @return extend5 - 预留字段5
     */
    public String getExtend5() {
        return extend5;
    }

    /**
     * 设置预留字段5
     *
     * @param extend5 预留字段5
     */
    public void setExtend5(String extend5) {
        this.extend5 = extend5 == null ? null : extend5.trim();
    }

    /**
     * 获取删除标识,0-未删除，1-删除
     *
     * @return del_flg - 删除标识,0-未删除，1-删除
     */
    public Integer getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标识,0-未删除，1-删除
     *
     * @param delFlg 删除标识,0-未删除，1-删除
     */
    public void setDelFlg(Integer delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取上次同步时间
     *
     * @return update_time - 上次同步时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置上次同步时间
     *
     * @param updateTime 上次同步时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", memberId=").append(memberId);
        sb.append(", erpCarrierNo=").append(erpCarrierNo);
        sb.append(", erpShipName=").append(erpShipName);
        sb.append(", erpShipCode=").append(erpShipCode);
        sb.append(", extend1=").append(extend1);
        sb.append(", extend2=").append(extend2);
        sb.append(", extend3=").append(extend3);
        sb.append(", extend4=").append(extend4);
        sb.append(", extend5=").append(extend5);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}