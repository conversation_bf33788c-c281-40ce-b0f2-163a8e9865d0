package com.ecommerce.logistics.dao.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

@Data
@Table(name = "lgs_erp_shipping_map")
public class ErpShippingMap implements Serializable {
    /**
     * 主键ID
     */
    @Id
    private String id;

    /**
     * 电商中的厂商会员ID
     */
    @Column(name = "ec_member_id")
    private String ecMemberId;

    /**
     * 电商中的厂商会员名称
     */
    @Column(name = "ec_member_name")
    private String ecMemberName;

    /**
     * 电商承运商会员ID
     */
    @Column(name = "ec_carrier_id")
    private String ecCarrierId;

    /**
     * 电商船舶ID
     */
    @Column(name = "ec_shipping_id")
    private String ecShippingId;

    /**
     * 电商船舶名称
     */
    @Column(name = "ec_shipping_name")
    private String ecShippingName;

    /**
     * erp承运商编号
     */
    @Column(name = "erp_carrier_no")
    private String erpCarrierNo;

    /**
     * erp承运商名称
     */
    @Column(name = "erp_carrier_name")
    private String erpCarrierName;

    /**
     * erp船舶编码
     */
    @Column(name = "erp_shipping_code")
    private String erpShippingCode;

    /**
     * erp船舶名称
     */
    @Column(name = "erp_shipping_name")
    private String erpShippingName;

    /**
     * 删除，0-未删除，1-已删除
     */
    @Column(name = "del_flg")
    private Integer delFlg;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;

}