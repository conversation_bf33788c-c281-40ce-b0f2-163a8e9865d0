package com.ecommerce.logistics.dao.vo;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

@Table(name = "lgs_gps_manufacturer")
public class GpsManufacturer implements Serializable {
    /**
     * gps厂商ID
     */
    @Id
    @Column(name = "gps_manufacturer_id")
    private String gpsManufacturerId;

    /**
     * 厂商名称
     */
    @Column(name = "gps_name")
    private String gpsName;

    /**
     * 厂商标识
     */
    private String number;

    /**
     * GPS协议类型
     */
    @Column(name = "gps_protocol_type")
    private String gpsProtocolType;

    /**
     * GPS登录名
     */
    @Column(name = "gps_user_name")
    private String gpsUserName;

    /**
     * GPS登录密码
     */
    @Column(name = "gps_password")
    private String gpsPassword;

    /**
     * 厂商url
     */
    private String url;

    /**
     * 删除标识
     */
    @Column(name = "del_flg")
    private Byte delFlg;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 数据版本
     */
    private Long version;

    private static final long serialVersionUID = 1L;

    /**
     * 获取gps厂商ID
     *
     * @return gps_manufacturer_id - gps厂商ID
     */
    public String getGpsManufacturerId() {
        return gpsManufacturerId;
    }

    /**
     * 设置gps厂商ID
     *
     * @param gpsManufacturerId gps厂商ID
     */
    public void setGpsManufacturerId(String gpsManufacturerId) {
        this.gpsManufacturerId = gpsManufacturerId == null ? null : gpsManufacturerId.trim();
    }

    /**
     * 获取厂商名称
     *
     * @return gps_name - 厂商名称
     */
    public String getGpsName() {
        return gpsName;
    }

    /**
     * 设置厂商名称
     *
     * @param gpsName 厂商名称
     */
    public void setGpsName(String gpsName) {
        this.gpsName = gpsName == null ? null : gpsName.trim();
    }

    /**
     * 获取厂商标识
     *
     * @return number - 厂商标识
     */
    public String getNumber() {
        return number;
    }

    /**
     * 设置厂商标识
     *
     * @param number 厂商标识
     */
    public void setNumber(String number) {
        this.number = number == null ? null : number.trim();
    }

    public String getGpsProtocolType() {
        return gpsProtocolType;
    }

    public void setGpsProtocolType(String gpsProtocolType) {
        this.gpsProtocolType = gpsProtocolType == null ? null : gpsProtocolType.trim();
    }

    public String getGpsUserName() {
        return gpsUserName;
    }

    public void setGpsUserName(String gpsUserName) {
        this.gpsUserName = gpsUserName == null ? null : gpsUserName.trim();
    }

    public String getGpsPassword() {
        return gpsPassword;
    }

    public void setGpsPassword(String gpsPassword) {
        this.gpsPassword = gpsPassword == null ? null : gpsPassword.trim();
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    /**
     * 获取厂商url
     *
     * @return url - 厂商url
     */
    public String getUrl() {
        return url;
    }

    /**
     * 设置厂商url
     *
     * @param url 厂商url
     */
    public void setUrl(String url) {
        this.url = url == null ? null : url.trim();
    }

    /**
     * 获取删除标识
     *
     * @return del_flg - 删除标识
     */
    public Byte getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标识
     *
     * @param delFlg 删除标识
     */
    public void setDelFlg(Byte delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * @return create_time
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * @param createTime
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * @return update_time
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * @param updateTime
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取数据版本
     *
     * @return version - 数据版本
     */
    public Long getVersion() {
        return version;
    }

    /**
     * 设置数据版本
     *
     * @param version 数据版本
     */
    public void setVersion(Long version) {
        this.version = version;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", gpsManufacturerId=").append(gpsManufacturerId);
        sb.append(", gpsName=").append(gpsName);
        sb.append(", number=").append(number);
        sb.append(", url=").append(url);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", version=").append(version);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}