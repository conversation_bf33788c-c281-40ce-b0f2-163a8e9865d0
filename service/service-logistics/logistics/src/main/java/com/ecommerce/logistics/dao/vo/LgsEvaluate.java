package com.ecommerce.logistics.dao.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;

@Table(name = "lgs_evaluate")
public class LgsEvaluate implements Serializable {
    /**
     * 主键-评价id
     */
    @Id
    @Column(name = "evaluate_id")
    private String evaluateId;

    /**
     * 订单id
     */
    @Column(name = "order_id")
    private String orderId;

    /**
     * 买家id
     */
    @Column(name = "buyer_id")
    private String buyerId;

    /**
     * 买家名称
     */
    @Column(name = "buyer_name")
    private String buyerName;

    /**
     * 承运商id
     */
    @Column(name = "carrier_id")
    private String carrierId;

    /**
     * 承运商名称
     */
    @Column(name = "carrier_name")
    private String carrierName;

    /**
     * 司机id
     */
    @Column(name = "driver_id")
    private String driverId;

    /**
     * 司机名称
     */
    @Column(name = "driver_name")
    private String driverName;

    /**
     * 卖家id
     */
    @Column(name = "seller_id")
    private String sellerId;

    /**
     * 卖家名称
     */
    @Column(name = "seller_name")
    private String sellerName;

    /**
     * 评分
     */
    private BigDecimal score;

    /**
     * 评价内容
     */
    private String content;

    /**
     * 买家印象
     */
    private String impression;

    /**
     * 评价图片
     */
    private String img;

    /**
     * 评价来源(0:web 1:app 2:小程序)
     */
    private Integer origin;

    /**
     * 是否系统自动评价 0:否 1:是
     */
    @Column(name = "is_auto")
    private Integer isAuto;

    /**
     * 是否匿名评价 0:否 1:是
     */
    @Column(name = "is_anonymous")
    private Integer isAnonymous;

    /**
     * 评价是否展示 0:否 1:是
     */
    @Column(name = "is_show")
    private Integer isShow;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    private String updateUser;

    private static final long serialVersionUID = 1L;

    /**
     * 获取主键-评价id
     *
     * @return evaluate_id - 主键-评价id
     */
    public String getEvaluateId() {
        return evaluateId;
    }

    /**
     * 设置主键-评价id
     *
     * @param evaluateId 主键-评价id
     */
    public void setEvaluateId(String evaluateId) {
        this.evaluateId = evaluateId == null ? null : evaluateId.trim();
    }

    /**
     * 获取订单id
     *
     * @return order_id - 订单id
     */
    public String getOrderId() {
        return orderId;
    }

    /**
     * 设置订单id
     *
     * @param orderId 订单id
     */
    public void setOrderId(String orderId) {
        this.orderId = orderId == null ? null : orderId.trim();
    }

    /**
     * 获取买家id
     *
     * @return buyer_id - 买家id
     */
    public String getBuyerId() {
        return buyerId;
    }

    /**
     * 设置买家id
     *
     * @param buyerId 买家id
     */
    public void setBuyerId(String buyerId) {
        this.buyerId = buyerId == null ? null : buyerId.trim();
    }

    /**
     * 获取买家名称
     *
     * @return buyer_name - 买家名称
     */
    public String getBuyerName() {
        return buyerName;
    }

    /**
     * 设置买家名称
     *
     * @param buyerName 买家名称
     */
    public void setBuyerName(String buyerName) {
        this.buyerName = buyerName == null ? null : buyerName.trim();
    }

    /**
     * 获取承运商id
     *
     * @return carrier_id - 承运商id
     */
    public String getCarrierId() {
        return carrierId;
    }

    /**
     * 设置承运商id
     *
     * @param carrierId 承运商id
     */
    public void setCarrierId(String carrierId) {
        this.carrierId = carrierId == null ? null : carrierId.trim();
    }

    /**
     * 获取承运商名称
     *
     * @return carrier_name - 承运商名称
     */
    public String getCarrierName() {
        return carrierName;
    }

    /**
     * 设置承运商名称
     *
     * @param carrierName 承运商名称
     */
    public void setCarrierName(String carrierName) {
        this.carrierName = carrierName == null ? null : carrierName.trim();
    }

    /**
     * 获取司机id
     *
     * @return driver_id - 司机id
     */
    public String getDriverId() {
        return driverId;
    }

    /**
     * 设置司机id
     *
     * @param driverId 司机id
     */
    public void setDriverId(String driverId) {
        this.driverId = driverId == null ? null : driverId.trim();
    }

    /**
     * 获取司机名称
     *
     * @return driver_name - 司机名称
     */
    public String getDriverName() {
        return driverName;
    }

    /**
     * 设置司机名称
     *
     * @param driverName 司机名称
     */
    public void setDriverName(String driverName) {
        this.driverName = driverName == null ? null : driverName.trim();
    }

    /**
     * 获取卖家id
     *
     * @return seller_id - 卖家id
     */
    public String getSellerId() {
        return sellerId;
    }

    /**
     * 设置卖家id
     *
     * @param sellerId 卖家id
     */
    public void setSellerId(String sellerId) {
        this.sellerId = sellerId == null ? null : sellerId.trim();
    }

    /**
     * 获取卖家名称
     *
     * @return seller_name - 卖家名称
     */
    public String getSellerName() {
        return sellerName;
    }

    /**
     * 设置卖家名称
     *
     * @param sellerName 卖家名称
     */
    public void setSellerName(String sellerName) {
        this.sellerName = sellerName == null ? null : sellerName.trim();
    }

    /**
     * 获取评分
     *
     * @return score - 评分
     */
    public BigDecimal getScore() {
        return score;
    }

    /**
     * 设置评分
     *
     * @param score 评分
     */
    public void setScore(BigDecimal score) {
        this.score = score;
    }

    /**
     * 获取评价内容
     *
     * @return content - 评价内容
     */
    public String getContent() {
        return content;
    }

    /**
     * 设置评价内容
     *
     * @param content 评价内容
     */
    public void setContent(String content) {
        this.content = content == null ? null : content.trim();
    }

    /**
     * 获取买家印象
     *
     * @return impression - 买家印象
     */
    public String getImpression() {
        return impression;
    }

    /**
     * 设置买家印象
     *
     * @param impression 买家印象
     */
    public void setImpression(String impression) {
        this.impression = impression == null ? null : impression.trim();
    }

    /**
     * 获取评价图片
     *
     * @return img - 评价图片
     */
    public String getImg() {
        return img;
    }

    /**
     * 设置评价图片
     *
     * @param img 评价图片
     */
    public void setImg(String img) {
        this.img = img == null ? null : img.trim();
    }

    /**
     * 获取评价来源(0:web 1:app 2:小程序)
     *
     * @return origin - 评价来源(0:web 1:app 2:小程序)
     */
    public Integer getOrigin() {
        return origin;
    }

    /**
     * 设置评价来源(0:web 1:app 2:小程序)
     *
     * @param origin 评价来源(0:web 1:app 2:小程序)
     */
    public void setOrigin(Integer origin) {
        this.origin = origin;
    }

    /**
     * 获取是否系统自动评价 0:否 1:是
     *
     * @return is_auto - 是否系统自动评价 0:否 1:是
     */
    public Integer getIsAuto() {
        return isAuto;
    }

    /**
     * 设置是否系统自动评价 0:否 1:是
     *
     * @param isAuto 是否系统自动评价 0:否 1:是
     */
    public void setIsAuto(Integer isAuto) {
        this.isAuto = isAuto;
    }

    /**
     * 获取是否匿名评价 0:否 1:是
     *
     * @return is_anonymous - 是否匿名评价 0:否 1:是
     */
    public Integer getIsAnonymous() {
        return isAnonymous;
    }

    /**
     * 设置是否匿名评价 0:否 1:是
     *
     * @param isAnonymous 是否匿名评价 0:否 1:是
     */
    public void setIsAnonymous(Integer isAnonymous) {
        this.isAnonymous = isAnonymous;
    }

    /**
     * 获取评价是否展示 0:否 1:是
     *
     * @return is_show - 评价是否展示 0:否 1:是
     */
    public Integer getIsShow() {
        return isShow;
    }

    /**
     * 设置评价是否展示 0:否 1:是
     *
     * @param isShow 评价是否展示 0:否 1:是
     */
    public void setIsShow(Integer isShow) {
        this.isShow = isShow;
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取创建人
     *
     * @return create_user - 创建人
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建人
     *
     * @param createUser 创建人
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取更新人
     *
     * @return update_user - 更新人
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新人
     *
     * @param updateUser 更新人
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", evaluateId=").append(evaluateId);
        sb.append(", orderId=").append(orderId);
        sb.append(", buyerId=").append(buyerId);
        sb.append(", buyerName=").append(buyerName);
        sb.append(", carrierId=").append(carrierId);
        sb.append(", carrierName=").append(carrierName);
        sb.append(", driverId=").append(driverId);
        sb.append(", driverName=").append(driverName);
        sb.append(", sellerId=").append(sellerId);
        sb.append(", sellerName=").append(sellerName);
        sb.append(", score=").append(score);
        sb.append(", content=").append(content);
        sb.append(", impression=").append(impression);
        sb.append(", img=").append(img);
        sb.append(", origin=").append(origin);
        sb.append(", isAuto=").append(isAuto);
        sb.append(", isAnonymous=").append(isAnonymous);
        sb.append(", isShow=").append(isShow);
        sb.append(", createTime=").append(createTime);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}