package com.ecommerce.logistics.dao.vo;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Table(name = "lgs_loading_time")
public class LoadingTime implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 装货时间ID
     */
    @Id
    @Column(name = "loading_time_id")
    private String loadingTimeId;

    /**
     * 装货点ID（仓库）
     */
    @Column(name = "loading_id")
    private String loadingId;

    /**
     * 卖家ID
     */
    @Column(name = "seller_id")
    private String sellerId;

    /**
     * 卖家名称
     */
    @Column(name = "seller_name")
    private String sellerName;

    /**
     * 归属用户ID
     */
    @Column(name = "user_id")
    private String userId;

    /**
     * 归属用户名称
     */
    @Column(name = "user_name")
    private String userName;

    /**
     * 归属用户类型
     */
    @Column(name = "user_type")
    private Integer userType;

    /**
     * 装货时间（分钟）
     */
    @Column(name = "loading_time")
    private Integer loadingTime;

    /**
     * 装货里程（公里）
     */
    @Column(name = "loading_km")
    private BigDecimal loadingKm;

    /**
     * 装货等待时间（分钟）
     */
    @Column(name = "loading_waiting_time")
    private Integer loadingWaitingTime;

    /**
     * 装货等待里程（公里）
     */
    @Column(name = "loading_waiting_km")
    private BigDecimal loadingWaitingKm;

    /**
     * 删除标识
     */
    @Column(name = "del_flg")
    private Boolean delFlg;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;
}