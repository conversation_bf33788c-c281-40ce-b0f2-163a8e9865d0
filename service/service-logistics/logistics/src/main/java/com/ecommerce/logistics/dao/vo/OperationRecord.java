package com.ecommerce.logistics.dao.vo;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

@Table(name = "lgs_operation_record")
public class OperationRecord implements Serializable {
    /**
     * 操作记录ID
     */
    @Id
    @Column(name = "operation_record_id")
    private String operationRecordId;

    /**
     * 业务实体ID
     */
    @Column(name = "entry_id")
    private String entryId;

    /**
     * 业务类型
     */
    private Byte type;

    /**
     * 操作类型
     */
    @Column(name = "operation_type")
    private Integer operationType;

    /**
     * 操作人ID
     */
    @Column(name = "operator_id")
    private String operatorId;

    /**
     * 操作用户名称
     */
    @Column(name = "operator_name")
    private String operatorName;

    /**
     * 内容
     */
    private String content;

    /**
     * 删除标识
     */
    @Column(name = "del_flg")
    private Byte delFlg;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 数据版本
     */
    private Long version;

    private static final long serialVersionUID = 1L;

    /**
     * 获取操作记录ID
     *
     * @return operation_record_id - 操作记录ID
     */
    public String getOperationRecordId() {
        return operationRecordId;
    }

    /**
     * 设置操作记录ID
     *
     * @param operationRecordId 操作记录ID
     */
    public void setOperationRecordId(String operationRecordId) {
        this.operationRecordId = operationRecordId == null ? null : operationRecordId.trim();
    }

    /**
     * 获取业务实体ID
     *
     * @return entry_id - 业务实体ID
     */
    public String getEntryId() {
        return entryId;
    }

    /**
     * 设置业务实体ID
     *
     * @param entryId 业务实体ID
     */
    public void setEntryId(String entryId) {
        this.entryId = entryId == null ? null : entryId.trim();
    }

    /**
     * 获取业务类型
     *
     * @return type - 业务类型
     */
    public Byte getType() {
        return type;
    }

    /**
     * 设置业务类型
     *
     * @param type 业务类型
     */
    public void setType(Byte type) {
        this.type = type;
    }

    /**
     * 获取操作类型
     *
     * @return operation_type - 操作类型
     */
    public Integer getOperationType() {
        return operationType;
    }

    /**
     * 设置操作类型
     *
     * @param operationType 操作类型
     */
    public void setOperationType(Integer operationType) {
        this.operationType = operationType;
    }

    /**
     * 获取操作人ID
     *
     * @return operator_id - 操作人ID
     */
    public String getOperatorId() {
        return operatorId;
    }

    /**
     * 设置操作人ID
     *
     * @param operatorId 操作人ID
     */
    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId == null ? null : operatorId.trim();
    }

    /**
     * 获取操作用户名称
     *
     * @return operator_name - 操作用户名称
     */
    public String getOperatorName() {
        return operatorName;
    }

    /**
     * 设置操作用户名称
     *
     * @param operatorName 操作用户名称
     */
    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName == null ? null : operatorName.trim();
    }

    /**
     * 获取内容
     *
     * @return content - 内容
     */
    public String getContent() {
        return content;
    }

    /**
     * 设置内容
     *
     * @param content 内容
     */
    public void setContent(String content) {
        this.content = content == null ? null : content.trim();
    }

    /**
     * 获取删除标识
     *
     * @return del_flg - 删除标识
     */
    public Byte getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标识
     *
     * @param delFlg 删除标识
     */
    public void setDelFlg(Byte delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取数据版本
     *
     * @return version - 数据版本
     */
    public Long getVersion() {
        return version;
    }

    /**
     * 设置数据版本
     *
     * @param version 数据版本
     */
    public void setVersion(Long version) {
        this.version = version;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", operationRecordId=").append(operationRecordId);
        sb.append(", entryId=").append(entryId);
        sb.append(", type=").append(type);
        sb.append(", operationType=").append(operationType);
        sb.append(", operatorId=").append(operatorId);
        sb.append(", operatorName=").append(operatorName);
        sb.append(", content=").append(content);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", version=").append(version);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}