package com.ecommerce.logistics.dao.vo;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Table(name = "lgs_picking_bill")
public class PickingBill implements Serializable {
    /**
     * 提货单ID
     */
    @Id
    @Column(name = "picking_bill_id")
    private String pickingBillId;

    /**
     * 发货单号
     */
    @Column(name = "delivery_sheet_num")
    private String deliverySheetNum;

    /**
     * 提货单号
     */
    @Column(name = "picking_bill_num")
    private String pickingBillNum;

    /**
     * 序列号
     */
    @Column(name = "seq_num")
    private Long seqNum;

    /**
     * 仓库ID
     */
    @Column(name = "warehouse_id")
    private String warehouseId;

    /**
     * 买家ID
     */
    @Column(name = "buyer_id")
    private String buyerId;

    /**
     * 买家名称
     */
    @Column(name = "buyer_name")
    private String buyerName;

    /**
     * erp支付账户编码
     */
    @Column(name = "mdm_code")
    private String mdmCode;

    /**
     * 卖家ID
     */
    @Column(name = "seller_id")
    private String sellerId;

    /**
     * 卖家名称
     */
    @Column(name = "seller_name")
    private String sellerName;

    /**
     * 提货单类型
     */
    private String type;

    /**
     * 关联的提货单类型
     */
    @Column(name = "relation_type")
    private String relationType;

    /**
     * 单据代理类型
     */
    @Column(name = "bill_proxy_type")
    private String billProxyType;

    /**
     * 是否可操作:0-否,1-是
     */
    @Column(name = "can_operate")
    private Byte canOperate;

    /**
     * 状态
     */
    private String status;

    /**
     * 结束原因
     */
    @Column(name = "end_reason")
    private String endReason;

    /**
     * 指派模式
     */
    @Column(name = "assign_mode")
    private String assignMode;

    /**
     * 运输方式
     */
    @Column(name = "transport_mode")
    private String transportMode;

    /**
     * 数据来源
     */
    @Column(name = "data_source")
    private String dataSource;

    /**
     * 收货人
     */
    private String receiver;

    /**
     * 收货人电话
     */
    @Column(name = "receiver_phone")
    private String receiverPhone;

    /**
     * 收货地址ID
     */
    @Column(name = "receive_address_id")
    private String receiveAddressId;

    /**
     * 收货省份
     */
    private String province;

    /**
     * 省份编码
     */
    @Column(name = "province_code")
    private String provinceCode;

    /**
     * 收货城市
     */
    private String city;

    /**
     * 城市编码
     */
    @Column(name = "city_code")
    private String cityCode;

    /**
     * 收货地区
     */
    private String district;

    /**
     * 区域编码
     */
    @Column(name = "district_code")
    private String districtCode;

    /**
     * 收货街道
     */
    private String street;

    /**
     * 收货详细地址
     */
    private String address;

    /**
     * 地址经纬度
     */
    private String location;

    /**
     * 配送时间
     */
    @Column(name = "delivery_time")
    private Date deliveryTime;

    /**
     * 配送时段
     */
    @Column(name = "delivery_time_range")
    private String deliveryTimeRange;

    /**
     * 收入运费
     */
    @Column(name = "income_carriage")
    private BigDecimal incomeCarriage;

    /**
     * 是否评价（0-未评价，1-已评价）
     */
    @Column(name = "is_evaluate")
    private Byte isEvaluate;

    /**
     * 合同Id
     */
    @Column(name = "deals_id")
    private String dealsId;

    /**
     * 合同序号
     */
    @Column(name = "deals_name")
    private String dealsName;

    /**
     * 合同项目名称
     */
    @Column(name = "project_name")
    private String projectName;

    /**
     * 提货时间
     */
    @Column(name = "picking_time")
    private Date pickingTime;

    /**
     * 支出运费
     */
    @Column(name = "expend_carriage")
    private BigDecimal expendCarriage;

    /**
     * 同步标识:1未同步 2: 正向同步 3:反向同步
     */
    @Column(name = "sync_flag")
    private String syncFlag;

    /**
     * 删除标识
     */
    @Column(name = "del_flg")
    private Byte delFlg;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 数据版本
     */
    private Long version;

    private static final long serialVersionUID = 1L;

    /**
     * 获取提货单ID
     *
     * @return picking_bill_id - 提货单ID
     */
    public String getPickingBillId() {
        return pickingBillId;
    }

    /**
     * 设置提货单ID
     *
     * @param pickingBillId 提货单ID
     */
    public void setPickingBillId(String pickingBillId) {
        this.pickingBillId = pickingBillId == null ? null : pickingBillId.trim();
    }

    /**
     * 获取发货单号
     *
     * @return delivery_sheet_num - 发货单号
     */
    public String getDeliverySheetNum() {
        return deliverySheetNum;
    }

    /**
     * 设置发货单号
     *
     * @param deliverySheetNum 发货单号
     */
    public void setDeliverySheetNum(String deliverySheetNum) {
        this.deliverySheetNum = deliverySheetNum == null ? null : deliverySheetNum.trim();
    }

    public String getRelationType() {
        return relationType;
    }

    public void setRelationType(String relationType) {
        this.relationType = relationType == null ? null : relationType.trim();
    }

    public String getBillProxyType() {
        return billProxyType;
    }

    public void setBillProxyType(String billProxyType) {
        this.billProxyType = billProxyType  == null ? null : billProxyType.trim();
    }

    public Byte getCanOperate() {
        return canOperate;
    }

    public void setCanOperate(Byte canOperate) {
        this.canOperate = canOperate;
    }

    /**
     * 获取提货单号
     *
     * @return picking_bill_num - 提货单号
     */
    public String getPickingBillNum() {
        return pickingBillNum;
    }

    /**
     * 设置提货单号
     *
     * @param pickingBillNum 提货单号
     */
    public void setPickingBillNum(String pickingBillNum) {
        this.pickingBillNum = pickingBillNum == null ? null : pickingBillNum.trim();
    }

    /**
     * 获取序列号
     *
     * @return seq_num - 序列号
     */
    public Long getSeqNum() {
        return seqNum;
    }

    /**
     * 设置序列号
     *
     * @param seqNum 序列号
     */
    public void setSeqNum(Long seqNum) {
        this.seqNum = seqNum;
    }

    /**
     * 获取仓库ID
     *
     * @return warehouse_id - 仓库ID
     */
    public String getWarehouseId() {
        return warehouseId;
    }

    /**
     * 设置仓库ID
     *
     * @param warehouseId 仓库ID
     */
    public void setWarehouseId(String warehouseId) {
        this.warehouseId = warehouseId == null ? null : warehouseId.trim();
    }

    /**
     * 获取买家ID
     *
     * @return buyer_id - 买家ID
     */
    public String getBuyerId() {
        return buyerId;
    }

    /**
     * 设置买家ID
     *
     * @param buyerId 买家ID
     */
    public void setBuyerId(String buyerId) {
        this.buyerId = buyerId == null ? null : buyerId.trim();
    }

    /**
     * 获取买家名称
     *
     * @return buyer_name - 买家名称
     */
    public String getBuyerName() {
        return buyerName;
    }

    /**
     * 设置买家名称
     *
     * @param buyerName 买家名称
     */
    public void setBuyerName(String buyerName) {
        this.buyerName = buyerName == null ? null : buyerName.trim();
    }

    public String getMdmCode() {
        return mdmCode;
    }

    public void setMdmCode(String mdmCode) {
        this.mdmCode = mdmCode == null ? null : mdmCode.trim();
    }

    /**
     * 获取卖家ID
     *
     * @return seller_id - 卖家ID
     */
    public String getSellerId() {
        return sellerId;
    }

    /**
     * 设置卖家ID
     *
     * @param sellerId 卖家ID
     */
    public void setSellerId(String sellerId) {
        this.sellerId = sellerId == null ? null : sellerId.trim();
    }

    /**
     * 获取卖家名称
     *
     * @return seller_name - 卖家名称
     */
    public String getSellerName() {
        return sellerName;
    }

    /**
     * 设置卖家名称
     *
     * @param sellerName 卖家名称
     */
    public void setSellerName(String sellerName) {
        this.sellerName = sellerName == null ? null : sellerName.trim();
    }

    /**
     * 获取提货单类型
     *
     * @return type - 提货单类型
     */
    public String getType() {
        return type;
    }

    /**
     * 设置提货单类型
     *
     * @param type 提货单类型
     */
    public void setType(String type) {
        this.type = type == null ? null : type.trim();
    }

    /**
     * 获取状态
     *
     * @return status - 状态
     */
    public String getStatus() {
        return status;
    }

    /**
     * 设置状态
     *
     * @param status 状态
     */
    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    public String getEndReason() {
        return endReason;
    }

    public void setEndReason(String endReason) {
        this.endReason = endReason == null ? null : endReason.trim();
    }

    /**
     * 获取指派模式
     *
     * @return assign_mode - 指派模式
     */
    public String getAssignMode() {
        return assignMode;
    }

    /**
     * 设置指派模式
     *
     * @param assignMode 指派模式
     */
    public void setAssignMode(String assignMode) {
        this.assignMode = assignMode == null ? null : assignMode.trim();
    }

    /**
     * 获取运输方式
     *
     * @return transport_mode - 运输方式
     */
    public String getTransportMode() {
        return transportMode;
    }

    /**
     * 设置运输方式
     *
     * @param transportMode 运输方式
     */
    public void setTransportMode(String transportMode) {
        this.transportMode = transportMode == null ? null : transportMode.trim();
    }

    /**
     * 获取数据来源
     *
     * @return data_source - 数据来源
     */
    public String getDataSource() {
        return dataSource;
    }

    /**
     * 设置数据来源
     *
     * @param dataSource 数据来源
     */
    public void setDataSource(String dataSource) {
        this.dataSource = dataSource == null ? null : dataSource.trim();
    }

    /**
     * 获取收货人
     *
     * @return receiver - 收货人
     */
    public String getReceiver() {
        return receiver;
    }

    /**
     * 设置收货人
     *
     * @param receiver 收货人
     */
    public void setReceiver(String receiver) {
        this.receiver = receiver == null ? null : receiver.trim();
    }

    /**
     * 获取收货人电话
     *
     * @return receiver_phone - 收货人电话
     */
    public String getReceiverPhone() {
        return receiverPhone;
    }

    /**
     * 设置收货人电话
     *
     * @param receiverPhone 收货人电话
     */
    public void setReceiverPhone(String receiverPhone) {
        this.receiverPhone = receiverPhone == null ? null : receiverPhone.trim();
    }

    /**
     * 获取收货地址ID
     *
     * @return receive_address_id - 收货地址ID
     */
    public String getReceiveAddressId() {
        return receiveAddressId;
    }

    /**
     * 设置收货地址ID
     *
     * @param receiveAddressId 收货地址ID
     */
    public void setReceiveAddressId(String receiveAddressId) {
        this.receiveAddressId = receiveAddressId == null ? null : receiveAddressId.trim();
    }

    /**
     * 获取收货省份
     *
     * @return province - 收货省份
     */
    public String getProvince() {
        return province;
    }

    /**
     * 设置收货省份
     *
     * @param province 收货省份
     */
    public void setProvince(String province) {
        this.province = province == null ? null : province.trim();
    }

    /**
     * 获取省份编码
     *
     * @return province_code - 省份编码
     */
    public String getProvinceCode() {
        return provinceCode;
    }

    /**
     * 设置省份编码
     *
     * @param provinceCode 省份编码
     */
    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode == null ? null : provinceCode.trim();
    }

    /**
     * 获取收货城市
     *
     * @return city - 收货城市
     */
    public String getCity() {
        return city;
    }

    /**
     * 设置收货城市
     *
     * @param city 收货城市
     */
    public void setCity(String city) {
        this.city = city == null ? null : city.trim();
    }

    /**
     * 获取城市编码
     *
     * @return city_code - 城市编码
     */
    public String getCityCode() {
        return cityCode;
    }

    /**
     * 设置城市编码
     *
     * @param cityCode 城市编码
     */
    public void setCityCode(String cityCode) {
        this.cityCode = cityCode == null ? null : cityCode.trim();
    }

    /**
     * 获取收货地区
     *
     * @return district - 收货地区
     */
    public String getDistrict() {
        return district;
    }

    /**
     * 设置收货地区
     *
     * @param district 收货地区
     */
    public void setDistrict(String district) {
        this.district = district == null ? null : district.trim();
    }

    /**
     * 获取区域编码
     *
     * @return district_code - 区域编码
     */
    public String getDistrictCode() {
        return districtCode;
    }

    /**
     * 设置区域编码
     *
     * @param districtCode 区域编码
     */
    public void setDistrictCode(String districtCode) {
        this.districtCode = districtCode == null ? null : districtCode.trim();
    }

    /**
     * 获取收货街道
     *
     * @return street - 收货街道
     */
    public String getStreet() {
        return street;
    }

    /**
     * 设置收货街道
     *
     * @param street 收货街道
     */
    public void setStreet(String street) {
        this.street = street == null ? null : street.trim();
    }

    /**
     * 获取收货详细地址
     *
     * @return address - 收货详细地址
     */
    public String getAddress() {
        return address;
    }

    /**
     * 设置收货详细地址
     *
     * @param address 收货详细地址
     */
    public void setAddress(String address) {
        this.address = address == null ? null : address.trim();
    }

    /**
     * 获取地址经纬度
     *
     * @return location - 地址经纬度
     */
    public String getLocation() {
        return location;
    }

    /**
     * 设置地址经纬度
     *
     * @param location 地址经纬度
     */
    public void setLocation(String location) {
        this.location = location == null ? null : location.trim();
    }

    /**
     * 获取配送时间
     *
     * @return delivery_time - 配送时间
     */
    public Date getDeliveryTime() {
        return deliveryTime;
    }

    /**
     * 设置配送时间
     *
     * @param deliveryTime 配送时间
     */
    public void setDeliveryTime(Date deliveryTime) {
        this.deliveryTime = deliveryTime;
    }

    /**
     * 获取配送时段
     *
     * @return delivery_time_range - 配送时段
     */
    public String getDeliveryTimeRange() {
        return deliveryTimeRange;
    }

    /**
     * 设置配送时段
     *
     * @param deliveryTimeRange 配送时段
     */
    public void setDeliveryTimeRange(String deliveryTimeRange) {
        this.deliveryTimeRange = deliveryTimeRange == null ? null : deliveryTimeRange.trim();
    }

    /**
     * 获取收入运费
     *
     * @return income_carriage - 收入运费
     */
    public BigDecimal getIncomeCarriage() {
        return incomeCarriage;
    }

    /**
     * 设置收入运费
     *
     * @param incomeCarriage 收入运费
     */
    public void setIncomeCarriage(BigDecimal incomeCarriage) {
        this.incomeCarriage = incomeCarriage;
    }

    /**
     * 获取是否评价
     *
     * @return is_evaluate - 是否评价
     */
    public Byte getIsEvaluate() {
        return isEvaluate;
    }

    /**
     * 设置是否评价
     *
     * @param isEvaluate 是否评价
     */
    public void setIsEvaluate(Byte isEvaluate) {
        this.isEvaluate = isEvaluate;
    }

    /**
     * 获取协议Id
     *
     * @return deals_id - 协议Id
     */
    public String getDealsId() {
        return dealsId;
    }

    /**
     * 设置协议Id
     *
     * @param dealsId 协议Id
     */
    public void setDealsId(String dealsId) {
        this.dealsId = dealsId == null ? null : dealsId.trim();
    }

    /**
     * 获取协议名称
     *
     * @return deals_name - 协议名称
     */
    public String getDealsName() {
        return dealsName;
    }

    /**
     * 设置协议名称
     *
     * @param dealsName 协议名称
     */
    public void setDealsName(String dealsName) {
        this.dealsName = dealsName == null ? null : dealsName.trim();
    }

    /**
     * 获取项目名称
     *
     * @return project_name - 项目名称
     */
    public String getProjectName() {
        return projectName;
    }

    /**
     * 设置项目名称
     *
     * @param projectName 项目名称
     */
    public void setProjectName(String projectName) {
        this.projectName = projectName == null ? null : projectName.trim();
    }


    /**
     * 获取提货时间
     *
     * @return picking_time - 提货时间
     */
    public Date getPickingTime() {
        return pickingTime;
    }

    /**
     * 设置提货时间
     *
     * @param pickingTime 提货时间
     */
    public void setPickingTime(Date pickingTime) {
        this.pickingTime = pickingTime;
    }

    /**
     * 获取支出运费
     *
     * @return expend_carriage - 支出运费
     */
    public BigDecimal getExpendCarriage() {
        return expendCarriage;
    }

    /**
     * 设置支出运费
     *
     * @param expendCarriage 支出运费
     */
    public void setExpendCarriage(BigDecimal expendCarriage) {
        this.expendCarriage = expendCarriage;
    }

    /**
     * 获取同步标识:1未同步 2: 正向同步 3:反向同步
     *
     * @return sync_flag - 同步标识:1未同步 2: 正向同步 3:反向同步
     */
    public String getSyncFlag() {
        return syncFlag;
    }

    /**
     * 设置同步标识:1未同步 2: 正向同步 3:反向同步
     *
     * @param syncFlag 同步标识:1未同步 2: 正向同步 3:反向同步
     */
    public void setSyncFlag(String syncFlag) {
        this.syncFlag = syncFlag == null ? null : syncFlag.trim();
    }

    /**
     * 获取删除标识
     *
     * @return del_flg - 删除标识
     */
    public Byte getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标识
     *
     * @param delFlg 删除标识
     */
    public void setDelFlg(Byte delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取数据版本
     *
     * @return version - 数据版本
     */
    public Long getVersion() {
        return version;
    }

    /**
     * 设置数据版本
     *
     * @param version 数据版本
     */
    public void setVersion(Long version) {
        this.version = version;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", pickingBillId=").append(pickingBillId);
        sb.append(", deliverySheetNum=").append(deliverySheetNum);
        sb.append(", pickingBillNum=").append(pickingBillNum);
        sb.append(", seqNum=").append(seqNum);
        sb.append(", warehouseId=").append(warehouseId);
        sb.append(", buyerId=").append(buyerId);
        sb.append(", buyerName=").append(buyerName);
        sb.append(", mdmCode=").append(mdmCode);
        sb.append(", sellerId=").append(sellerId);
        sb.append(", sellerName=").append(sellerName);
        sb.append(", type=").append(type);
        sb.append(", status=").append(status);
        sb.append(", endReason=").append(endReason);
        sb.append(", assignMode=").append(assignMode);
        sb.append(", transportMode=").append(transportMode);
        sb.append(", dataSource=").append(dataSource);
        sb.append(", receiver=").append(receiver);
        sb.append(", receiverPhone=").append(receiverPhone);
        sb.append(", receiveAddressId=").append(receiveAddressId);
        sb.append(", province=").append(province);
        sb.append(", provinceCode=").append(provinceCode);
        sb.append(", city=").append(city);
        sb.append(", cityCode=").append(cityCode);
        sb.append(", district=").append(district);
        sb.append(", districtCode=").append(districtCode);
        sb.append(", street=").append(street);
        sb.append(", address=").append(address);
        sb.append(", location=").append(location);
        sb.append(", deliveryTime=").append(deliveryTime);
        sb.append(", deliveryTimeRange=").append(deliveryTimeRange);
        sb.append(", incomeCarriage=").append(incomeCarriage);
        sb.append(", isEvaluate=").append(isEvaluate);
        sb.append(", dealsId=").append(dealsId);
        sb.append(", dealsName=").append(dealsName);
        sb.append(", projectName=").append(projectName);
        sb.append(", pickingTime=").append(pickingTime);
        sb.append(", expendCarriage=").append(expendCarriage);
        sb.append(", syncFlag=").append(syncFlag);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", version=").append(version);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}