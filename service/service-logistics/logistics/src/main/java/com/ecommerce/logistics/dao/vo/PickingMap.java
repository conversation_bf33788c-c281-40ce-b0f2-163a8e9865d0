package com.ecommerce.logistics.dao.vo;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

@Table(name = "lgs_picking_map")
public class PickingMap implements Serializable {
    /**
     * 提货单映射ID
     */
    @Id
    @Column(name = "picking_map_id")
    private String pickingMapId;

    /**
     * 一级提货单ID
     */
    @Column(name = "primary_picking_bill_id")
    private String primaryPickingBillId;

    /**
     * 一级提货单号
     */
    @Column(name = "primary_picking_bill_num")
    private String primaryPickingBillNum;

    /**
     * 一级发货单号
     */
    @Column(name = "primary_delivery_sheet_num")
    private String primaryDeliverySheetNum;

    /**
     * 二级提货单ID
     */
    @Column(name = "secondary_picking_bill_id")
    private String secondaryPickingBillId;

    /**
     * 二级提货单号
     */
    @Column(name = "secondary_picking_bill_num")
    private String secondaryPickingBillNum;

    /**
     * 二级发货单号
     */
    @Column(name = "secondary_delivery_sheet_num")
    private String secondaryDeliverySheetNum;

    /**
     * 删除标识
     */
    @Column(name = "del_flg")
    private Byte delFlg;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 数据版本
     */
    private Long version;

    private static final long serialVersionUID = 1L;

    /**
     * 获取提货单映射ID
     *
     * @return picking_map_id - 提货单映射ID
     */
    public String getPickingMapId() {
        return pickingMapId;
    }

    /**
     * 设置提货单映射ID
     *
     * @param pickingMapId 提货单映射ID
     */
    public void setPickingMapId(String pickingMapId) {
        this.pickingMapId = pickingMapId == null ? null : pickingMapId.trim();
    }

    /**
     * 获取一级提货单ID
     *
     * @return primary_picking_bill_id - 一级提货单ID
     */
    public String getPrimaryPickingBillId() {
        return primaryPickingBillId;
    }

    /**
     * 设置一级提货单ID
     *
     * @param primaryPickingBillId 一级提货单ID
     */
    public void setPrimaryPickingBillId(String primaryPickingBillId) {
        this.primaryPickingBillId = primaryPickingBillId == null ? null : primaryPickingBillId.trim();
    }

    /**
     * 获取一级提货单号
     *
     * @return primary_picking_bill_num - 一级提货单号
     */
    public String getPrimaryPickingBillNum() {
        return primaryPickingBillNum;
    }

    /**
     * 设置一级提货单号
     *
     * @param primaryPickingBillNum 一级提货单号
     */
    public void setPrimaryPickingBillNum(String primaryPickingBillNum) {
        this.primaryPickingBillNum = primaryPickingBillNum == null ? null : primaryPickingBillNum.trim();
    }

    /**
     * 获取一级发货单号
     *
     * @return primary_delivery_sheet_num - 一级发货单号
     */
    public String getPrimaryDeliverySheetNum() {
        return primaryDeliverySheetNum;
    }

    /**
     * 设置一级发货单号
     *
     * @param primaryDeliverySheetNum 一级发货单号
     */
    public void setPrimaryDeliverySheetNum(String primaryDeliverySheetNum) {
        this.primaryDeliverySheetNum = primaryDeliverySheetNum == null ? null : primaryDeliverySheetNum.trim();
    }

    /**
     * 获取二级提货单ID
     *
     * @return secondary_picking_bill_id - 二级提货单ID
     */
    public String getSecondaryPickingBillId() {
        return secondaryPickingBillId;
    }

    /**
     * 设置二级提货单ID
     *
     * @param secondaryPickingBillId 二级提货单ID
     */
    public void setSecondaryPickingBillId(String secondaryPickingBillId) {
        this.secondaryPickingBillId = secondaryPickingBillId == null ? null : secondaryPickingBillId.trim();
    }

    /**
     * 获取二级提货单号
     *
     * @return secondary_picking_bill_num - 二级提货单号
     */
    public String getSecondaryPickingBillNum() {
        return secondaryPickingBillNum;
    }

    /**
     * 设置二级提货单号
     *
     * @param secondaryPickingBillNum 二级提货单号
     */
    public void setSecondaryPickingBillNum(String secondaryPickingBillNum) {
        this.secondaryPickingBillNum = secondaryPickingBillNum == null ? null : secondaryPickingBillNum.trim();
    }

    /**
     * 获取二级发货单号
     *
     * @return secondary_delivery_sheet_num - 二级发货单号
     */
    public String getSecondaryDeliverySheetNum() {
        return secondaryDeliverySheetNum;
    }

    /**
     * 设置二级发货单号
     *
     * @param secondaryDeliverySheetNum 二级发货单号
     */
    public void setSecondaryDeliverySheetNum(String secondaryDeliverySheetNum) {
        this.secondaryDeliverySheetNum = secondaryDeliverySheetNum == null ? null : secondaryDeliverySheetNum.trim();
    }

    /**
     * 获取删除标识
     *
     * @return del_flg - 删除标识
     */
    public Byte getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标识
     *
     * @param delFlg 删除标识
     */
    public void setDelFlg(Byte delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取数据版本
     *
     * @return version - 数据版本
     */
    public Long getVersion() {
        return version;
    }

    /**
     * 设置数据版本
     *
     * @param version 数据版本
     */
    public void setVersion(Long version) {
        this.version = version;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", pickingMapId=").append(pickingMapId);
        sb.append(", primaryPickingBillId=").append(primaryPickingBillId);
        sb.append(", primaryPickingBillNum=").append(primaryPickingBillNum);
        sb.append(", primaryDeliverySheetNum=").append(primaryDeliverySheetNum);
        sb.append(", secondaryPickingBillId=").append(secondaryPickingBillId);
        sb.append(", secondaryPickingBillNum=").append(secondaryPickingBillNum);
        sb.append(", secondaryDeliverySheetNum=").append(secondaryDeliverySheetNum);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", version=").append(version);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}