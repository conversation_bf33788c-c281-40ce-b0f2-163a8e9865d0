package com.ecommerce.logistics.dao.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;

@Table(name = "lgs_platform_stock_log")
public class PlatformStockLog implements Serializable {
    /**
     * 主键ID
     */
    @Id
    @Column(name = "log_id")
    private String logId;

    /**
     * 卖家ID
     */
    @Column(name = "seller_id")
    private String sellerId;

    /**
     * 卖家名
     */
    @Column(name = "seller_name")
    private String sellerName;

    /**
     * 仓库ID
     */
    @Column(name = "warehouse_id")
    private String warehouseId;

    /**
     * 库位编号
     */
    @Column(name = "storehouse_number")
    private String storehouseNumber;

    /**
     * 商品ID
     */
    @Column(name = "product_id")
    private String productId;

    /**
     * 商品名
     */
    @Column(name = "product_name")
    private String productName;

    /**
     * 库存变动方向
     */
    @Column(name = "stock_direct")
    private String stockDirect;

    /**
     * 入库量
     */
    @Column(name = "in_quantity")
    private BigDecimal inQuantity;

    /**
     * 出库量
     */
    @Column(name = "out_quantity")
    private BigDecimal outQuantity;

    /**
     * 删除标识
     */
    @Column(name = "del_flg")
    private Byte delFlg;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;

    /**
     * 获取主键ID
     *
     * @return log_id - 主键ID
     */
    public String getLogId() {
        return logId;
    }

    /**
     * 设置主键ID
     *
     * @param logId 主键ID
     */
    public void setLogId(String logId) {
        this.logId = logId == null ? null : logId.trim();
    }

    /**
     * 获取卖家ID
     *
     * @return seller_id - 卖家ID
     */
    public String getSellerId() {
        return sellerId;
    }

    /**
     * 设置卖家ID
     *
     * @param sellerId 卖家ID
     */
    public void setSellerId(String sellerId) {
        this.sellerId = sellerId == null ? null : sellerId.trim();
    }

    /**
     * 获取卖家名
     *
     * @return seller_name - 卖家名
     */
    public String getSellerName() {
        return sellerName;
    }

    /**
     * 设置卖家名
     *
     * @param sellerName 卖家名
     */
    public void setSellerName(String sellerName) {
        this.sellerName = sellerName == null ? null : sellerName.trim();
    }

    /**
     * 获取仓库ID
     *
     * @return warehouse_id - 仓库ID
     */
    public String getWarehouseId() {
        return warehouseId;
    }

    /**
     * 设置仓库ID
     *
     * @param warehouseId 仓库ID
     */
    public void setWarehouseId(String warehouseId) {
        this.warehouseId = warehouseId == null ? null : warehouseId.trim();
    }

    /**
     * 获取库位编号
     *
     * @return storehouse_id - 库位编号
     */
    public String getStorehouseNumber() {
        return storehouseNumber;
    }

    /**
     * 设置库位编号
     *
     * @param storehouseNumber 库位编号
     */
    public void setStorehouseNumber(String storehouseNumber) {
        this.storehouseNumber = storehouseNumber == null ? null : storehouseNumber.trim();
    }

    /**
     * 获取商品ID
     *
     * @return product_id - 商品ID
     */
    public String getProductId() {
        return productId;
    }

    /**
     * 设置商品ID
     *
     * @param productId 商品ID
     */
    public void setProductId(String productId) {
        this.productId = productId == null ? null : productId.trim();
    }

    /**
     * 获取商品名
     *
     * @return product_name - 商品名
     */
    public String getProductName() {
        return productName;
    }

    /**
     * 设置商品名
     *
     * @param productName 商品名
     */
    public void setProductName(String productName) {
        this.productName = productName == null ? null : productName.trim();
    }

    /**
     * 获取库存变动方向
     *
     * @return stock_direct - 库存变动方向
     */
    public String getStockDirect() {
        return stockDirect;
    }

    /**
     * 设置库存变动方向
     *
     * @param stockDirect 库存变动方向
     */
    public void setStockDirect(String stockDirect) {
        this.stockDirect = stockDirect == null ? null : stockDirect.trim();
    }

    /**
     * 获取入库量
     *
     * @return in_quantity - 入库量
     */
    public BigDecimal getInQuantity() {
        return inQuantity;
    }

    /**
     * 设置入库量
     *
     * @param inQuantity 入库量
     */
    public void setInQuantity(BigDecimal inQuantity) {
        this.inQuantity = inQuantity;
    }

    /**
     * 获取出库量
     *
     * @return out_quantity - 出库量
     */
    public BigDecimal getOutQuantity() {
        return outQuantity;
    }

    /**
     * 设置出库量
     *
     * @param outQuantity 出库量
     */
    public void setOutQuantity(BigDecimal outQuantity) {
        this.outQuantity = outQuantity;
    }

    /**
     * 获取删除标识
     *
     * @return del_flg - 删除标识
     */
    public Byte getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标识
     *
     * @param delFlg 删除标识
     */
    public void setDelFlg(Byte delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", logId=").append(logId);
        sb.append(", sellerId=").append(sellerId);
        sb.append(", sellerName=").append(sellerName);
        sb.append(", warehouseId=").append(warehouseId);
        sb.append(", storehouseNumber=").append(storehouseNumber);
        sb.append(", productId=").append(productId);
        sb.append(", productName=").append(productName);
        sb.append(", stockDirect=").append(stockDirect);
        sb.append(", inQuantity=").append(inQuantity);
        sb.append(", outQuantity=").append(outQuantity);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}