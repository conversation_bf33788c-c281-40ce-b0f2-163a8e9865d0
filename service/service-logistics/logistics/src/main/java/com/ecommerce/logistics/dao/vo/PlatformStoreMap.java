package com.ecommerce.logistics.dao.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;

@Table(name = "lgs_platform_store_map")
public class PlatformStoreMap implements Serializable {
    /**
     * 存储映射ID
     */
    @Id
    @Column(name = "store_map_id")
    private String storeMapId;

    /**
     * 归属人ID
     */
    @Column(name = "user_id")
    private String userId;

    /**
     * 归属人名
     */
    @Column(name = "user_name")
    private String userName;

    /**
     * 仓库ID
     */
    @Column(name = "warehouse_id")
    private String warehouseId;

    /**
     * 库位ID
     */
    @Column(name = "storehouse_id")
    private String storehouseId;

    /**
     * 仓位编号
     */
    @Column(name = "storehouse_number")
    private String storehouseNumber;

    /**
     * 商品ID
     */
    @Column(name = "product_id")
    private String productId;

    /**
     * 商品描述
     */
    private String note;

    /**
     * 使用库存
     */
    @Column(name = "used_stock")
    private BigDecimal usedStock;

    /**
     * 在途库存
     */
    @Column(name = "locked_stock")
    private BigDecimal lockedStock;

    /**
     * 删除标识
     */
    @Column(name = "del_flg")
    private Byte delFlg;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 数据版本
     */
    private Long version;

    private static final long serialVersionUID = 1L;

    /**
     * 获取存储映射ID
     *
     * @return store_map_id - 存储映射ID
     */
    public String getStoreMapId() {
        return storeMapId;
    }

    /**
     * 设置存储映射ID
     *
     * @param storeMapId 存储映射ID
     */
    public void setStoreMapId(String storeMapId) {
        this.storeMapId = storeMapId == null ? null : storeMapId.trim();
    }

    /**
     * 获取归属人ID
     *
     * @return user_id - 归属人ID
     */
    public String getUserId() {
        return userId;
    }

    /**
     * 设置归属人ID
     *
     * @param userId 归属人ID
     */
    public void setUserId(String userId) {
        this.userId = userId == null ? null : userId.trim();
    }

    /**
     * 获取归属人名
     *
     * @return user_name - 归属人名
     */
    public String getUserName() {
        return userName;
    }

    /**
     * 设置归属人名
     *
     * @param userName 归属人名
     */
    public void setUserName(String userName) {
        this.userName = userName == null ? null : userName.trim();
    }

    /**
     * 获取仓库ID
     *
     * @return warehouse_id - 仓库ID
     */
    public String getWarehouseId() {
        return warehouseId;
    }

    /**
     * 设置仓库ID
     *
     * @param warehouseId 仓库ID
     */
    public void setWarehouseId(String warehouseId) {
        this.warehouseId = warehouseId == null ? null : warehouseId.trim();
    }

    /**
     * 获取库位ID
     *
     * @return storehouse_id - 库位ID
     */
    public String getStorehouseId() {
        return storehouseId;
    }

    /**
     * 设置库位ID
     *
     * @param storehouseId 库位ID
     */
    public void setStorehouseId(String storehouseId) {
        this.storehouseId = storehouseId == null ? null : storehouseId.trim();
    }

    /**
     * 获取仓位编号
     *
     * @return storehouse_number - 仓位编号
     */
    public String getStorehouseNumber() {
        return storehouseNumber;
    }

    /**
     * 设置仓位编号
     *
     * @param storehouseNumber 仓位编号
     */
    public void setStorehouseNumber(String storehouseNumber) {
        this.storehouseNumber = storehouseNumber == null ? null : storehouseNumber.trim();
    }

    /**
     * 获取商品ID
     *
     * @return product_id - 商品ID
     */
    public String getProductId() {
        return productId;
    }

    /**
     * 设置商品ID
     *
     * @param productId 商品ID
     */
    public void setProductId(String productId) {
        this.productId = productId == null ? null : productId.trim();
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note == null ? null : note.trim();
    }

    /**
     * 获取使用库存
     *
     * @return used_stock - 使用库存
     */
    public BigDecimal getUsedStock() {
        return usedStock;
    }

    /**
     * 设置使用库存
     *
     * @param usedStock 使用库存
     */
    public void setUsedStock(BigDecimal usedStock) {
        this.usedStock = usedStock;
    }

    /**
     * 获取在途库存
     *
     * @return locked_stock - 在途库存
     */
    public BigDecimal getLockedStock() {
        return lockedStock;
    }

    /**
     * 设置在途库存
     *
     * @param lockedStock 在途库存
     */
    public void setLockedStock(BigDecimal lockedStock) {
        this.lockedStock = lockedStock;
    }

    /**
     * 获取删除标识
     *
     * @return del_flg - 删除标识
     */
    public Byte getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标识
     *
     * @param delFlg 删除标识
     */
    public void setDelFlg(Byte delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取数据版本
     *
     * @return version - 数据版本
     */
    public Long getVersion() {
        return version;
    }

    /**
     * 设置数据版本
     *
     * @param version 数据版本
     */
    public void setVersion(Long version) {
        this.version = version;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", storeMapId=").append(storeMapId);
        sb.append(", userId=").append(userId);
        sb.append(", userName=").append(userName);
        sb.append(", warehouseId=").append(warehouseId);
        sb.append(", storehouseId=").append(storehouseId);
        sb.append(", storehouseNumber=").append(storehouseNumber);
        sb.append(", productId=").append(productId);
        sb.append(", note=").append(note);
        sb.append(", usedStock=").append(usedStock);
        sb.append(", lockedStock=").append(lockedStock);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", version=").append(version);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}