package com.ecommerce.logistics.dao.vo;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

@Table(name = "lgs_porter")
public class Porter implements Serializable {
    /**
     * 主键Id
     */
    @Id
    @Column(name = "porter_id")
    private String porterId;

    /**
     * 归属用户ID
     */
    @Column(name = "user_id")
    private String userId;

    /**
     * 归属用户名称
     */
    @Column(name = "user_name")
    private String userName;

    /**
     * 归属用户类型(UserRoleEnum)
     */
    @Column(name = "user_type")
    private String userType;

    /**
     * 省名称
     */
    @Column(name = "province_name")
    private String provinceName;

    /**
     * 省编码
     */
    @Column(name = "province_code")
    private String provinceCode;

    /**
     * 市名称
     */
    @Column(name = "city_name")
    private String cityName;

    /**
     * 市编码
     */
    @Column(name = "city_code")
    private String cityCode;

    /**
     * 区名称
     */
    @Column(name = "district_name")
    private String districtName;

    /**
     * 区编码
     */
    @Column(name = "district_code")
    private String districtCode;

    /**
     * 姓名
     */
    @Column(name = "porter_name")
    private String porterName;

    /**
     * 联系方式
     */
    @Column(name = "porter_phone")
    private String porterPhone;

    /**
     * 年龄
     */
    private String age;

    /**
     * 性别,0-男,1-女
     */
    private Integer sex;

    /**
     * 说明
     */
    private String content;

    /**
     * 删除标识
     */
    @Column(name = "del_flg")
    private Byte delFlg;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;

    /**
     * 获取主键Id
     *
     * @return porter_id - 主键Id
     */
    public String getPorterId() {
        return porterId;
    }

    /**
     * 设置主键Id
     *
     * @param porterId 主键Id
     */
    public void setPorterId(String porterId) {
        this.porterId = porterId == null ? null : porterId.trim();
    }

    /**
     * 获取归属用户ID
     *
     * @return user_id - 归属用户ID
     */
    public String getUserId() {
        return userId;
    }

    /**
     * 设置归属用户ID
     *
     * @param userId 归属用户ID
     */
    public void setUserId(String userId) {
        this.userId = userId == null ? null : userId.trim();
    }

    /**
     * 获取归属用户名称
     *
     * @return user_name - 归属用户名称
     */
    public String getUserName() {
        return userName;
    }

    /**
     * 设置归属用户名称
     *
     * @param userName 归属用户名称
     */
    public void setUserName(String userName) {
        this.userName = userName == null ? null : userName.trim();
    }

    /**
     * 获取归属用户类型(UserRoleEnum)
     *
     * @return user_type - 归属用户类型(UserRoleEnum)
     */
    public String getUserType() {
        return userType;
    }

    /**
     * 设置归属用户类型(UserRoleEnum)
     *
     * @param userType 归属用户类型(UserRoleEnum)
     */
    public void setUserType(String userType) {
        this.userType = userType == null ? null : userType.trim();
    }

    /**
     * 获取省名称
     *
     * @return province_name - 省名称
     */
    public String getProvinceName() {
        return provinceName;
    }

    /**
     * 设置省名称
     *
     * @param provinceName 省名称
     */
    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName == null ? null : provinceName.trim();
    }

    /**
     * 获取省编码
     *
     * @return province_code - 省编码
     */
    public String getProvinceCode() {
        return provinceCode;
    }

    /**
     * 设置省编码
     *
     * @param provinceCode 省编码
     */
    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode == null ? null : provinceCode.trim();
    }

    /**
     * 获取市名称
     *
     * @return city_name - 市名称
     */
    public String getCityName() {
        return cityName;
    }

    /**
     * 设置市名称
     *
     * @param cityName 市名称
     */
    public void setCityName(String cityName) {
        this.cityName = cityName == null ? null : cityName.trim();
    }

    /**
     * 获取市编码
     *
     * @return city_code - 市编码
     */
    public String getCityCode() {
        return cityCode;
    }

    /**
     * 设置市编码
     *
     * @param cityCode 市编码
     */
    public void setCityCode(String cityCode) {
        this.cityCode = cityCode == null ? null : cityCode.trim();
    }

    /**
     * 获取区名称
     *
     * @return district_name - 区名称
     */
    public String getDistrictName() {
        return districtName;
    }

    /**
     * 设置区名称
     *
     * @param districtName 区名称
     */
    public void setDistrictName(String districtName) {
        this.districtName = districtName == null ? null : districtName.trim();
    }

    /**
     * 获取区编码
     *
     * @return district_code - 区编码
     */
    public String getDistrictCode() {
        return districtCode;
    }

    /**
     * 设置区编码
     *
     * @param districtCode 区编码
     */
    public void setDistrictCode(String districtCode) {
        this.districtCode = districtCode == null ? null : districtCode.trim();
    }

    /**
     * 获取姓名
     *
     * @return porter_name - 姓名
     */
    public String getPorterName() {
        return porterName;
    }

    /**
     * 设置姓名
     *
     * @param porterName 姓名
     */
    public void setPorterName(String porterName) {
        this.porterName = porterName == null ? null : porterName.trim();
    }

    /**
     * 获取联系方式
     *
     * @return porter_phone - 联系方式
     */
    public String getPorterPhone() {
        return porterPhone;
    }

    /**
     * 设置联系方式
     *
     * @param porterPhone 联系方式
     */
    public void setPorterPhone(String porterPhone) {
        this.porterPhone = porterPhone == null ? null : porterPhone.trim();
    }

    /**
     * 获取年龄
     *
     * @return age - 年龄
     */
    public String getAge() {
        return age;
    }

    /**
     * 设置年龄
     *
     * @param age 年龄
     */
    public void setAge(String age) {
        this.age = age == null ? null : age.trim();
    }

    /**
     * 获取性别,0-男,1-女
     *
     * @return sex - 性别,0-男,1-女
     */
    public Integer getSex() {
        return sex;
    }

    /**
     * 设置性别,0-男,1-女
     *
     * @param sex 性别,0-男,1-女
     */
    public void setSex(Integer sex) {
        this.sex = sex;
    }

    /**
     * 获取说明
     *
     * @return content - 说明
     */
    public String getContent() {
        return content;
    }

    /**
     * 设置说明
     *
     * @param content 说明
     */
    public void setContent(String content) {
        this.content = content == null ? null : content.trim();
    }

    /**
     * 获取删除标识
     *
     * @return del_flg - 删除标识
     */
    public Byte getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标识
     *
     * @param delFlg 删除标识
     */
    public void setDelFlg(Byte delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", porterId=").append(porterId);
        sb.append(", userId=").append(userId);
        sb.append(", userName=").append(userName);
        sb.append(", userType=").append(userType);
        sb.append(", provinceName=").append(provinceName);
        sb.append(", provinceCode=").append(provinceCode);
        sb.append(", cityName=").append(cityName);
        sb.append(", cityCode=").append(cityCode);
        sb.append(", districtName=").append(districtName);
        sb.append(", districtCode=").append(districtCode);
        sb.append(", porterName=").append(porterName);
        sb.append(", porterPhone=").append(porterPhone);
        sb.append(", age=").append(age);
        sb.append(", sex=").append(sex);
        sb.append(", content=").append(content);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}