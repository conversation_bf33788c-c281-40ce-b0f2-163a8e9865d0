package com.ecommerce.logistics.dao.vo;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

@Table(name = "lgs_porterage_rule")
public class PorterageRule implements Serializable {
    /**
     * 搬运费规则id
     */
    @Id
    @Column(name = "porterage_rule_id")
    private String porterageRuleId;

    /**
     * 规则名称
     */
    @Column(name = "rule_name")
    private String ruleName;

    /**
     * 归属用户id
     */
    @Column(name = "user_id")
    private String userId;

    /**
     * 归属用户名称
     */
    @Column(name = "user_name")
    private String userName;

    /**
     * 归属用户类型
     */
    @Column(name = "user_type")
    private Integer userType;

    /**
     * 省份名称
     */
    private String province;

    /**
     * 省份code
     */
    @Column(name = "province_code")
    private String provinceCode;

    /**
     * 城市名称
     */
    private String city;

    /**
     * 城市code
     */
    @Column(name = "city_code")
    private String cityCode;

    /**
     * 地区名称
     */
    private String district;

    /**
     * 地区code
     */
    @Column(name = "district_code")
    private String districtCode;

    /**
     * 街道名称
     */
    private String street;

    /**
     * 街道code
     */
    @Column(name = "street_code")
    private String streetCode;

    /**
     * 运输品类ID
     */
    @Column(name = "transport_category_id")
    private String transportCategoryId;

    /**
     * 删除标识
     */
    @Column(name = "del_flg")
    private Boolean delFlg;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;

    /**
     * 获取搬运费规则id
     *
     * @return porterage_rule_id - 搬运费规则id
     */
    public String getPorterageRuleId() {
        return porterageRuleId;
    }

    /**
     * 设置搬运费规则id
     *
     * @param porterageRuleId 搬运费规则id
     */
    public void setPorterageRuleId(String porterageRuleId) {
        this.porterageRuleId = porterageRuleId == null ? null : porterageRuleId.trim();
    }

    /**
     * 获取规则名称
     *
     * @return rule_name - 规则名称
     */
    public String getRuleName() {
        return ruleName;
    }

    /**
     * 设置规则名称
     *
     * @param ruleName 规则名称
     */
    public void setRuleName(String ruleName) {
        this.ruleName = ruleName == null ? null : ruleName.trim();
    }

    /**
     * 获取归属用户id
     *
     * @return user_id - 归属用户id
     */
    public String getUserId() {
        return userId;
    }

    /**
     * 设置归属用户id
     *
     * @param userId 归属用户id
     */
    public void setUserId(String userId) {
        this.userId = userId == null ? null : userId.trim();
    }

    /**
     * 获取归属用户名称
     *
     * @return user_name - 归属用户名称
     */
    public String getUserName() {
        return userName;
    }

    /**
     * 设置归属用户名称
     *
     * @param userName 归属用户名称
     */
    public void setUserName(String userName) {
        this.userName = userName == null ? null : userName.trim();
    }

    /**
     * 获取归属用户类型
     *
     * @return user_type - 归属用户类型
     */
    public Integer getUserType() {
        return userType;
    }

    /**
     * 设置归属用户类型
     *
     * @param userType 归属用户类型
     */
    public void setUserType(Integer userType) {
        this.userType = userType;
    }

    /**
     * 获取省份名称
     *
     * @return province - 省份名称
     */
    public String getProvince() {
        return province;
    }

    /**
     * 设置省份名称
     *
     * @param province 省份名称
     */
    public void setProvince(String province) {
        this.province = province == null ? null : province.trim();
    }

    /**
     * 获取省份code
     *
     * @return province_code - 省份code
     */
    public String getProvinceCode() {
        return provinceCode;
    }

    /**
     * 设置省份code
     *
     * @param provinceCode 省份code
     */
    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode == null ? null : provinceCode.trim();
    }

    /**
     * 获取城市名称
     *
     * @return city - 城市名称
     */
    public String getCity() {
        return city;
    }

    /**
     * 设置城市名称
     *
     * @param city 城市名称
     */
    public void setCity(String city) {
        this.city = city == null ? null : city.trim();
    }

    /**
     * 获取城市code
     *
     * @return city_code - 城市code
     */
    public String getCityCode() {
        return cityCode;
    }

    /**
     * 设置城市code
     *
     * @param cityCode 城市code
     */
    public void setCityCode(String cityCode) {
        this.cityCode = cityCode == null ? null : cityCode.trim();
    }

    /**
     * 获取地区名称
     *
     * @return district - 地区名称
     */
    public String getDistrict() {
        return district;
    }

    /**
     * 设置地区名称
     *
     * @param district 地区名称
     */
    public void setDistrict(String district) {
        this.district = district == null ? null : district.trim();
    }

    /**
     * 获取地区code
     *
     * @return district_code - 地区code
     */
    public String getDistrictCode() {
        return districtCode;
    }

    /**
     * 设置地区code
     *
     * @param districtCode 地区code
     */
    public void setDistrictCode(String districtCode) {
        this.districtCode = districtCode == null ? null : districtCode.trim();
    }

    /**
     * 获取运输品类ID
     *
     * @return transport_category_id - 运输品类ID
     */
    public String getTransportCategoryId() {
        return transportCategoryId;
    }

    /**
     * 设置运输品类ID
     *
     * @param transportCategoryId 运输品类ID
     */
    public void setTransportCategoryId(String transportCategoryId) {
        this.transportCategoryId = transportCategoryId == null ? null : transportCategoryId.trim();
    }

    /**
     * 获取删除标识
     *
     * @return del_flg - 删除标识
     */
    public Boolean getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标识
     *
     * @param delFlg 删除标识
     */
    public void setDelFlg(Boolean delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getStreet() {
        return street;
    }

    public void setStreet(String street) {
        this.street = street;
    }

    public String getStreetCode() {
        return streetCode;
    }

    public void setStreetCode(String streetCode) {
        this.streetCode = streetCode;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("PorterageRule{");
        sb.append("porterageRuleId='").append(porterageRuleId).append('\'');
        sb.append(", ruleName='").append(ruleName).append('\'');
        sb.append(", userId='").append(userId).append('\'');
        sb.append(", userName='").append(userName).append('\'');
        sb.append(", userType=").append(userType);
        sb.append(", province='").append(province).append('\'');
        sb.append(", provinceCode='").append(provinceCode).append('\'');
        sb.append(", city='").append(city).append('\'');
        sb.append(", cityCode='").append(cityCode).append('\'');
        sb.append(", district='").append(district).append('\'');
        sb.append(", districtCode='").append(districtCode).append('\'');
        sb.append(", street='").append(street).append('\'');
        sb.append(", streetCode='").append(streetCode).append('\'');
        sb.append(", transportCategoryId='").append(transportCategoryId).append('\'');
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser='").append(createUser).append('\'');
        sb.append(", updateUser='").append(updateUser).append('\'');
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append('}');
        return sb.toString();
    }
}