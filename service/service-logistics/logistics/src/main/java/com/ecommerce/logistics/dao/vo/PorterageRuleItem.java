package com.ecommerce.logistics.dao.vo;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Table(name = "lgs_porterage_rule_item")
public class PorterageRuleItem implements Serializable {
    /**
     * 搬运费规则明细id
     */
    @Id
    @Column(name = "porterage_rule_item_id")
    private String porterageRuleItemId;

    /**
     * 运费规则id
     */
    @Column(name = "porterage_rule_id")
    private String porterageRuleId;

    /**
     * 计量单位
     */
    @Column(name = "metering_unit")
    private String meteringUnit;

    /**
     * 计量范围最小值
     */
    @Column(name = "metering_num")
    private BigDecimal meteringNum;

    /**
     * 运输单位
     */
    @Column(name = "transport_unit")
    private String transportUnit;

    /**
     * 运输范围最小值
     */
    @Column(name = "transport_num")
    private BigDecimal transportNum;

    /**
     * 搬运价格
     */
    @Column(name = "porterage_price")
    private BigDecimal porteragePrice;

    /**
     * 删除标识
     */
    @Column(name = "del_flg")
    private Boolean delFlg;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;

    /**
     * 获取搬运费规则明细id
     *
     * @return porterage_rule_item_id - 搬运费规则明细id
     */
    public String getPorterageRuleItemId() {
        return porterageRuleItemId;
    }

    /**
     * 设置搬运费规则明细id
     *
     * @param porterageRuleItemId 搬运费规则明细id
     */
    public void setPorterageRuleItemId(String porterageRuleItemId) {
        this.porterageRuleItemId = porterageRuleItemId == null ? null : porterageRuleItemId.trim();
    }

    /**
     * 获取运费规则id
     *
     * @return porterage_rule_id - 运费规则id
     */
    public String getPorterageRuleId() {
        return porterageRuleId;
    }

    /**
     * 设置运费规则id
     *
     * @param porterageRuleId 运费规则id
     */
    public void setPorterageRuleId(String porterageRuleId) {
        this.porterageRuleId = porterageRuleId == null ? null : porterageRuleId.trim();
    }

    /**
     * 获取计量单位
     *
     * @return metering_unit - 计量单位
     */
    public String getMeteringUnit() {
        return meteringUnit;
    }

    /**
     * 设置计量单位
     *
     * @param meteringUnit 计量单位
     */
    public void setMeteringUnit(String meteringUnit) {
        this.meteringUnit = meteringUnit == null ? null : meteringUnit.trim();
    }

    /**
     * 获取计量范围最小值
     *
     * @return metering_num - 计量范围最小值
     */
    public BigDecimal getMeteringNum() {
        return meteringNum;
    }

    /**
     * 设置计量范围最小值
     *
     * @param meteringNum 计量范围最小值
     */
    public void setMeteringNum(BigDecimal meteringNum) {
        this.meteringNum = meteringNum;
    }

    /**
     * 获取运输单位
     *
     * @return transport_unit - 运输单位
     */
    public String getTransportUnit() {
        return transportUnit;
    }

    /**
     * 设置运输单位
     *
     * @param transportUnit 运输单位
     */
    public void setTransportUnit(String transportUnit) {
        this.transportUnit = transportUnit == null ? null : transportUnit.trim();
    }

    /**
     * 获取运输范围最小值
     *
     * @return transport_num - 运输范围最小值
     */
    public BigDecimal getTransportNum() {
        return transportNum;
    }

    /**
     * 设置运输范围最小值
     *
     * @param transportNum 运输范围最小值
     */
    public void setTransportNum(BigDecimal transportNum) {
        this.transportNum = transportNum;
    }

    /**
     * 获取搬运价格
     *
     * @return porterage_price - 搬运价格
     */
    public BigDecimal getPorteragePrice() {
        return porteragePrice;
    }

    /**
     * 设置搬运价格
     *
     * @param porteragePrice 搬运价格
     */
    public void setPorteragePrice(BigDecimal porteragePrice) {
        this.porteragePrice = porteragePrice;
    }

    /**
     * 获取删除标识
     *
     * @return del_flg - 删除标识
     */
    public Boolean getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标识
     *
     * @param delFlg 删除标识
     */
    public void setDelFlg(Boolean delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", porterageRuleItemId=").append(porterageRuleItemId);
        sb.append(", porterageRuleId=").append(porterageRuleId);
        sb.append(", meteringUnit=").append(meteringUnit);
        sb.append(", meteringNum=").append(meteringNum);
        sb.append(", transportUnit=").append(transportUnit);
        sb.append(", transportNum=").append(transportNum);
        sb.append(", porteragePrice=").append(porteragePrice);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}