package com.ecommerce.logistics.dao.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;

@Table(name = "lgs_product_info")
public class ProductInfo implements Serializable {
    /**
     * 商品信息ID
     */
    @Id
    @Column(name = "product_info_id")
    private String productInfoId;

    /**
     * 运输品类ID
     */
    @Column(name = "transport_category_id")
    private String transportCategoryId;

    /**
     * 商品ID
     */
    @Column(name = "product_id")
    private String productId;

    /**
     * erp商品编码
     */
    @Column(name = "commodity_code")
    private String commodityCode;

    /**
     * 开票二维码字符串
     */
    @Column(name = "qr_code")
    private String qrCode;

    /**
     * 厂商
     */
    private String vendor;

    /**
     * 商品图片
     */
    @Column(name = "product_img")
    private String productImg;

    /**
     * 商品ID
     */
    @Column(name = "goods_id")
    private String goodsId;

    /**
     * 商品描述
     */
    private String note;

    /**
     * 商品单位
     */
    private String unit;

    /**
     * 商品单价
     */
    private BigDecimal price;

    /**
     * 运费单价
     */
    @Column(name = "carriage_unit_price")
    private BigDecimal carriageUnitPrice;

    /**
     * 特殊商品标识
     */
    @Column(name = "special_flag")
    private Byte specialFlag;

    /**
     * 流向卸货点编码
     */
    @Column(name = "unloading_flow_code")
    private String unloadingFlowCode;

    /**
     * 删除标识
     */
    @Column(name = "del_flg")
    private Byte delFlg;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 数据版本
     */
    private Long version;

    /**
     * 签收类型
     */
    @Column(name = "sign_type")
    private String signType;

    /**
     * 搬运标识
     */
    @Column(name = "carry_flag")
    private Byte carryFlag;

    private static final long serialVersionUID = 1L;

    /**
     * 获取商品信息ID
     *
     * @return product_info_id - 商品信息ID
     */
    public String getProductInfoId() {
        return productInfoId;
    }

    /**
     * 设置商品信息ID
     *
     * @param productInfoId 商品信息ID
     */
    public void setProductInfoId(String productInfoId) {
        this.productInfoId = productInfoId == null ? null : productInfoId.trim();
    }

    /**
     * 获取运输品类ID
     *
     * @return transport_category_id - 运输品类ID
     */
    public String getTransportCategoryId() {
        return transportCategoryId;
    }

    /**
     * 设置运输品类ID
     *
     * @param transportCategoryId 运输品类ID
     */
    public void setTransportCategoryId(String transportCategoryId) {
        this.transportCategoryId = transportCategoryId == null ? null : transportCategoryId.trim();
    }

    /**
     * 获取商品ID
     *
     * @return product_id - 商品ID
     */
    public String getProductId() {
        return productId;
    }

    /**
     * 设置商品ID
     *
     * @param productId 商品ID
     */
    public void setProductId(String productId) {
        this.productId = productId == null ? null : productId.trim();
    }

    /**
     * 获取erp商品编码
     *
     * @return commodity_code - erp商品编码
     */
    public String getCommodityCode() {
        return commodityCode;
    }

    /**
     * 设置erp商品编码
     *
     * @param commodityCode erp商品编码
     */
    public void setCommodityCode(String commodityCode) {
        this.commodityCode = commodityCode == null ? null : commodityCode.trim();
    }

    /**
     * 获取开票二维码字符串
     *
     * @return qr_code - 开票二维码字符串
     */
    public String getQrCode() {
        return qrCode;
    }

    /**
     * 设置开票二维码字符串
     *
     * @param qrCode 开票二维码字符串
     */
    public void setQrCode(String qrCode) {
        this.qrCode = qrCode == null ? null : qrCode.trim();
    }

    /**
     * 获取厂商
     *
     * @return vendor - 厂商
     */
    public String getVendor() {
        return vendor;
    }

    /**
     * 设置厂商
     *
     * @param vendor 厂商
     */
    public void setVendor(String vendor) {
        this.vendor = vendor == null ? null : vendor.trim();
    }

    /**
     * 获取商品图片
     *
     * @return product_img - 商品图片
     */
    public String getProductImg() {
        return productImg;
    }

    /**
     * 设置商品图片
     *
     * @param productImg 商品图片
     */
    public void setProductImg(String productImg) {
        this.productImg = productImg == null ? null : productImg.trim();
    }

    public String getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(String goodsId) {
        this.goodsId = goodsId == null ? null : goodsId.trim();
    }

    /**
     * 获取商品描述
     *
     * @return note - 商品描述
     */
    public String getNote() {
        return note;
    }

    /**
     * 设置商品描述
     *
     * @param note 商品描述
     */
    public void setNote(String note) {
        this.note = note == null ? null : note.trim();
    }

    /**
     * 获取商品单位
     *
     * @return unit - 商品单位
     */
    public String getUnit() {
        return unit;
    }

    /**
     * 设置商品单位
     *
     * @param unit 商品单位
     */
    public void setUnit(String unit) {
        this.unit = unit == null ? null : unit.trim();
    }

    /**
     * 获取商品单价
     *
     * @return price - 商品单价
     */
    public BigDecimal getPrice() {
        return price;
    }

    /**
     * 设置商品单价
     *
     * @param price 商品单价
     */
    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    /**
     * 获取运费单价
     *
     * @return carriage_unit_price - 运费单价
     */
    public BigDecimal getCarriageUnitPrice() {
        return carriageUnitPrice;
    }

    /**
     * 设置运费单价
     *
     * @param carriageUnitPrice 运费单价
     */
    public void setCarriageUnitPrice(BigDecimal carriageUnitPrice) {
        this.carriageUnitPrice = carriageUnitPrice;
    }

    /**
     * 获取特殊商品标识
     *
     * @return special_flag - 特殊商品标识
     */
    public Byte getSpecialFlag() {
        return specialFlag;
    }

    /**
     * 设置特殊商品标识
     *
     * @param specialFlag 特殊商品标识
     */
    public void setSpecialFlag(Byte specialFlag) {
        this.specialFlag = specialFlag;
    }

    public String getUnloadingFlowCode() {
        return unloadingFlowCode;
    }

    public void setUnloadingFlowCode(String unloadingFlowCode) {
        this.unloadingFlowCode = unloadingFlowCode == null ? null : unloadingFlowCode.trim();
    }

    /**
     * 获取删除标识
     *
     * @return del_flg - 删除标识
     */
    public Byte getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标识
     *
     * @param delFlg 删除标识
     */
    public void setDelFlg(Byte delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取数据版本
     *
     * @return version - 数据版本
     */
    public Long getVersion() {
        return version;
    }

    /**
     * 设置数据版本
     *
     * @param version 数据版本
     */
    public void setVersion(Long version) {
        this.version = version;
    }

    public String getSignType() {
        return signType;
    }

    public void setSignType(String signType) {
        this.signType = signType == null ? null : signType.trim();
    }

    public Byte getCarryFlag() {
        return carryFlag;
    }

    public void setCarryFlag(Byte carryFlag) {
        this.carryFlag = carryFlag;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", productInfoId=").append(productInfoId);
        sb.append(", transportCategoryId=").append(transportCategoryId);
        sb.append(", productId=").append(productId);
        sb.append(", commodityCode=").append(commodityCode);
        sb.append(", qrCode=").append(qrCode);
        sb.append(", vendor=").append(vendor);
        sb.append(", productImg=").append(productImg);
        sb.append(", goodsId=").append(goodsId);
        sb.append(", note=").append(note);
        sb.append(", unit=").append(unit);
        sb.append(", price=").append(price);
        sb.append(", carriageUnitPrice=").append(carriageUnitPrice);
        sb.append(", specialFlag=").append(specialFlag);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", version=").append(version);
        sb.append(", signType=").append(signType);
        sb.append(", carryFlag=").append(carryFlag);
        sb.append(", unloadingFlowCode=").append(unloadingFlowCode);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}