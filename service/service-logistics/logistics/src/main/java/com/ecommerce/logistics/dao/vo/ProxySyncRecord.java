package com.ecommerce.logistics.dao.vo;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

@Table(name = "lgs_proxy_sync_record")
public class ProxySyncRecord implements Serializable {
    /**
     * 背靠背单据同步记录主键
     */
    @Id
    @Column(name = "proxy_sync_id")
    private String proxySyncId;

    /**
     * 单据类型
     */
    @Column(name = "bill_type")
    private String billType;

    /**
     * 实际操作单据ID
     */
    @Column(name = "operate_bill_id")
    private String operateBillId;

    /**
     * 同步到的单据ID
     */
    @Column(name = "relation_bill_id")
    private String relationBillId;

    /**
     * 同步类型
     */
    @Column(name = "sync_type")
    private String syncType;

    /**
     * 处理的外部状态
     */
    private String status;

    /**
     * 结果描述
     */
    @Column(name = "sync_desc")
    private String syncDesc;

    /**
     * 尝试次数
     */
    @Column(name = "try_count")
    private Integer tryCount;

    /**
     * 删除标识
     */
    @Column(name = "del_flg")
    private Byte delFlg;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 数据版本
     */
    private Long version;

    /**
     * 参数json
     */
    @Column(name = "param_text")
    private String paramText;

    private static final long serialVersionUID = 1L;

    /**
     * 获取背靠背单据同步记录主键
     *
     * @return proxy_sync_id - 背靠背单据同步记录主键
     */
    public String getProxySyncId() {
        return proxySyncId;
    }

    /**
     * 设置背靠背单据同步记录主键
     *
     * @param proxySyncId 背靠背单据同步记录主键
     */
    public void setProxySyncId(String proxySyncId) {
        this.proxySyncId = proxySyncId == null ? null : proxySyncId.trim();
    }

    /**
     * 获取单据类型
     *
     * @return bill_type - 单据类型
     */
    public String getBillType() {
        return billType;
    }

    /**
     * 设置单据类型
     *
     * @param billType 单据类型
     */
    public void setBillType(String billType) {
        this.billType = billType == null ? null : billType.trim();
    }

    /**
     * 获取实际操作单据ID
     *
     * @return operate_bill_id - 实际操作单据ID
     */
    public String getOperateBillId() {
        return operateBillId;
    }

    /**
     * 设置实际操作单据ID
     *
     * @param operateBillId 实际操作单据ID
     */
    public void setOperateBillId(String operateBillId) {
        this.operateBillId = operateBillId == null ? null : operateBillId.trim();
    }

    /**
     * 获取同步到的单据ID
     *
     * @return relation_bill_id - 同步到的单据ID
     */
    public String getRelationBillId() {
        return relationBillId;
    }

    /**
     * 设置同步到的单据ID
     *
     * @param relationBillId 同步到的单据ID
     */
    public void setRelationBillId(String relationBillId) {
        this.relationBillId = relationBillId == null ? null : relationBillId.trim();
    }

    /**
     * 获取同步类型
     *
     * @return sync_type - 同步类型
     */
    public String getSyncType() {
        return syncType;
    }

    /**
     * 设置同步类型
     *
     * @param syncType 同步类型
     */
    public void setSyncType(String syncType) {
        this.syncType = syncType == null ? null : syncType.trim();
    }

    /**
     * 获取处理的外部状态
     *
     * @return status - 处理的外部状态
     */
    public String getStatus() {
        return status;
    }

    /**
     * 设置处理的外部状态
     *
     * @param status 处理的外部状态
     */
    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    /**
     * 获取结果描述
     *
     * @return syncDesc - 结果描述
     */
    public String getSyncDesc() {
        return syncDesc;
    }

    /**
     * 设置结果描述
     *
     * @param syncDesc 结果描述
     */
    public void setSyncDesc(String syncDesc) {
        this.syncDesc = syncDesc == null ? null : syncDesc.trim();
    }

    /**
     * 获取尝试次数
     *
     * @return try_count - 尝试次数
     */
    public Integer getTryCount() {
        return tryCount;
    }

    /**
     * 设置尝试次数
     *
     * @param tryCount 尝试次数
     */
    public void setTryCount(Integer tryCount) {
        this.tryCount = tryCount;
    }

    /**
     * 获取删除标识
     *
     * @return del_flg - 删除标识
     */
    public Byte getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标识
     *
     * @param delFlg 删除标识
     */
    public void setDelFlg(Byte delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取数据版本
     *
     * @return version - 数据版本
     */
    public Long getVersion() {
        return version;
    }

    /**
     * 设置数据版本
     *
     * @param version 数据版本
     */
    public void setVersion(Long version) {
        this.version = version;
    }

    /**
     * 获取参数json
     *
     * @return param_text - 参数json
     */
    public String getParamText() {
        return paramText;
    }

    /**
     * 设置参数json
     *
     * @param paramText 参数json
     */
    public void setParamText(String paramText) {
        this.paramText = paramText == null ? null : paramText.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", proxySyncId=").append(proxySyncId);
        sb.append(", billType=").append(billType);
        sb.append(", operateBillId=").append(operateBillId);
        sb.append(", relationBillId=").append(relationBillId);
        sb.append(", syncType=").append(syncType);
        sb.append(", status=").append(status);
        sb.append(", syncDesc=").append(syncDesc);
        sb.append(", tryCount=").append(tryCount);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", version=").append(version);
        sb.append(", paramText=").append(paramText);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}