package com.ecommerce.logistics.dao.vo;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;


@Data
@ToString
@Table(name = "lgs_reroute_record_log")
public class RerouteRecordLog implements Serializable {
    /**
     * 主键ID
     */
    @Id
    @Column(name = "reroute_record_id")
    private String rerouteRecordId;

    /**
     * 运单ID
     */
    @Column(name = "waybill_id")
    private String waybillId;

    /**
     * 运单号
     */
    @Column(name = "waybill_num")
    private String waybillNum;

    /**
     * 当前委托单收货单位ID
     */
    @Column(name = "curr_receive_member_id")
    private String currReceiveMemberId;

    /**
     * 当前委托单收货单位
     */
    @Column(name = "curr_receive_member_name")
    private String currReceiveMemberName;
    /**
     * 当前委托单ID
     */
    @Column(name = "curr_delivery_bill_id")
    private String currDeliveryBillId;

    /**
     * 当前委托单号
     */
    @Column(name = "curr_delivery_bill_num")
    private String currDeliveryBillNum;

    /**
     * 当前卸货码头ID
     */
    @Column(name = "curr_unload_port_id")
    private String currUnloadPortId;

    /**
     * 当前卸货码头名称
     */
    @Column(name = "curr_unload_port_name")
    private String currUnloadPortName;

    /**
     * 原委托单收货单位ID
     */
    @Column(name = "original_receive_member_id")
    private String originalReceiveMemberId;

    /**
     * 原委托单收货单位
     */
    @Column(name = "original_receive_member_name")
    private String originalReceiveMemberName;

    /**
     * 原委托单ID
     */
    @Column(name = "original_delivery_bill_id")
    private String originalDeliveryBillId;

    /**
     * 原委托单号
     */
    @Column(name = "original_delivery_bill_num")
    private String originalDeliveryBillNum;

    /**
     * 原卸货码头ID
     */
    @Column(name = "original_unload_port_id")
    private String originalUnloadPortId;

    /**
     * 原卸货码头名称
     */
    @Column(name = "original_unload_port_name")
    private String originalUnloadPortName;

    /**
     * 创建人ID
     */
    @Column(name = "create_user_id")
    private String createUserId;

    /**
     * 创建人姓名
     */
    @Column(name = "create_user_name")
    private String createUserName;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    private static final long serialVersionUID = 1L;
}