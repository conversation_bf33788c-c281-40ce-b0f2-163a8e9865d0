package com.ecommerce.logistics.dao.vo;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

@Table(name = "lgs_route_contract_map")
public class RouteContractMap implements Serializable {
    /**
     * 主键ID
     */
    @Id
    private String id;

    /**
     * 运费路线ID
     */
    @Column(name = "carriage_route_id")
    private String carriageRouteId;

    /**
     * 合同地址编号
     */
    @Column(name = "contract_address_code")
    private String contractAddressCode;

    /**
     * 合同地址详细信息
     */
    @Column(name = "contract_address_detail")
    private String contractAddressDetail;

    /**
     * ERP路线别名
     */
    @Column(name = "contract_address_name")
    private String contractAddressName;

    /**
     * ERP提货点编码
     */
    @Column(name = "pickup_point_code")
    private String pickupPointCode;

    /**
     * 删除标识，0-未删除，1-已删除
     */
    @Column(name = "del_flg")
    private Integer delFlg;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;

    /**
     * 获取主键ID
     *
     * @return id - 主键ID
     */
    public String getId() {
        return id;
    }

    /**
     * 设置主键ID
     *
     * @param id 主键ID
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * 获取运费路线ID
     *
     * @return carriage_route_id - 运费路线ID
     */
    public String getCarriageRouteId() {
        return carriageRouteId;
    }

    /**
     * 设置运费路线ID
     *
     * @param carriageRouteId 运费路线ID
     */
    public void setCarriageRouteId(String carriageRouteId) {
        this.carriageRouteId = carriageRouteId == null ? null : carriageRouteId.trim();
    }

    /**
     * 获取合同地址编号
     *
     * @return contract_address_code - 合同地址编号
     */
    public String getContractAddressCode() {
        return contractAddressCode;
    }

    /**
     * 设置合同地址编号
     *
     * @param contractAddressCode 合同地址编号
     */
    public void setContractAddressCode(String contractAddressCode) {
        this.contractAddressCode = contractAddressCode == null ? null : contractAddressCode.trim();
    }

    /**
     * 获取合同地址详细信息
     *
     * @return contract_address_detail - 合同地址详细信息
     */
    public String getContractAddressDetail() {
        return contractAddressDetail;
    }

    /**
     * 设置合同地址详细信息
     *
     * @param contractAddressDetail 合同地址详细信息
     */
    public void setContractAddressDetail(String contractAddressDetail) {
        this.contractAddressDetail = contractAddressDetail == null ? null : contractAddressDetail.trim();
    }

    /**
     * 获取ERP路线别名
     *
     * @return contract_address_name - ERP路线别名
     */
    public String getContractAddressName() {
        return contractAddressName;
    }

    /**
     * 设置ERP路线别名
     *
     * @param contractAddressName ERP路线别名
     */
    public void setContractAddressName(String contractAddressName) {
        this.contractAddressName = contractAddressName == null ? null : contractAddressName.trim();
    }

    /**
     * 获取ERP提货点编码
     *
     * @return pickup_point_code - ERP提货点编码
     */
    public String getPickupPointCode() {
        return pickupPointCode;
    }

    /**
     * 设置ERP提货点编码
     *
     * @param pickupPointCode ERP提货点编码
     */
    public void setPickupPointCode(String pickupPointCode) {
        this.pickupPointCode = pickupPointCode == null ? null : pickupPointCode.trim();
    }

    /**
     * 获取删除标识，0-未删除，1-已删除
     *
     * @return del_flg - 删除标识，0-未删除，1-已删除
     */
    public Integer getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标识，0-未删除，1-已删除
     *
     * @param delFlg 删除标识，0-未删除，1-已删除
     */
    public void setDelFlg(Integer delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", carriageRouteId=").append(carriageRouteId);
        sb.append(", contractAddressCode=").append(contractAddressCode);
        sb.append(", contractAddressDetail=").append(contractAddressDetail);
        sb.append(", contractAddressName=").append(contractAddressName);
        sb.append(", pickupPointCode=").append(pickupPointCode);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}