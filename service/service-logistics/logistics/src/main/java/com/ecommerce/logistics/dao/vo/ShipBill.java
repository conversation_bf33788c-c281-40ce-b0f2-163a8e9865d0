package com.ecommerce.logistics.dao.vo;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Table(name = "lgs_ship_bill")
public class ShipBill implements Serializable {
    /**
     * 运单主键
     */
    @Id
    @Column(name = "waybill_id")
    private String waybillId;

    /**
     * 运单编号
     */
    @Column(name = "waybill_num")
    private String waybillNum;

    /**
     * 是否为整合运单的父级运单
     */
    @Column(name = "parent_flag")
    private Byte parentFlag;

    /**
     * 是否为整合运单的子运单
     */
    @Column(name = "child_flag")
    private Byte childFlag;

    /**
     * 货物所有人
     */
    @Column(name = "owner_id")
    private String ownerId;

    /**
     * 父运单ID,没有存当前主键
     */
    @Column(name = "parent_id")
    private String parentId;

    /**
     * 承运商id
     */
    @Column(name = "carrier_id")
    private String carrierId;

    /**
     * 承运人名
     */
    @Column(name = "carrier_name")
    private String carrierName;

    /**
     * 承运商类型
     */
    @Column(name = "carrier_type")
    private String carrierType;

    /**
     * 运输工具类型
     */
    @Column(name = "transport_tool_type")
    private String transportToolType;

    /**
     * 驾驶员id
     */
    @Column(name = "driver_id")
    private String driverId;

    /**
     * 驾驶员姓名
     */
    @Column(name = "driver_name")
    private String driverName;

    /**
     * 驾驶员电话
     */
    @Column(name = "driver_phone")
    private String driverPhone;

    /**
     * 船舶ID
     */
    @Column(name = "shipping_id")
    private String shippingId;

    /**
     * 船舶名称
     */
    @Column(name = "shipping_name")
    private String shippingName;

    /**
     * 船舶编号
     */
    @Column(name = "shipping_no")
    private String shippingNo;

    /**
     * 车辆ID
     */
    @Column(name = "vehicle_id")
    private String vehicleId;

    /**
     * 车牌号
     */
    @Column(name = "vehicle_num")
    private String vehicleNum;

    /**
     * 运单类型
     */
    private String type;

    /**
     * 是否内部调拨单,00-否，01-是
     */
    @Column(name = "internal")
    private String internal;

    /**
     * 运单状态
     */
    private String status;

    /**
     * 二维码内容
     */
    @Column(name = "qr_code")
    private String qrCode;

    /**
     * 指派数量
     */
    @Column(name = "assign_quantity")
    private BigDecimal assignQuantity;

    /**
     * 实际出厂量
     */
    @Column(name = "actual_quantity")
    private BigDecimal actualQuantity;

    /**
     * 完成数量
     */
    @Column(name = "complete_quantity")
    private BigDecimal completeQuantity;

    /**
     * 签收数量
     */
    @Column(name = "sign_quantity")
    private BigDecimal signQuantity;

    /**
     * 签收备注
     */
    @Column(name = "sign_remark")
    private String signRemark;

    /**
     * 应收运费单价
     */
    @Column(name = "receivable_carriage_price")
    private BigDecimal receivableCarriagePrice;

    /**
     * 应收运费总额
     */
    @Column(name = "receivable_carriage_amount")
    private BigDecimal receivableCarriageAmount;

    /**
     * 目前最大序列号
     */
    @Column(name = "max_seq_num")
    private Integer maxSeqNum;

    /**
     * 是否需要监控
     */
    @Column(name = "need_monitor")
    private Byte needMonitor;

    /**
     * 是否可监控
     */
    @Column(name = "can_monitor")
    private Byte canMonitor;

    /**
     * 取消理由
     */
    @Column(name = "cancel_reason")
    private String cancelReason;

    /**
     * 关闭理由
     */
    @Column(name = "close_reason")
    private String closeReason;

    /**
     * 关闭时间
     */
    @Column(name = "close_time")
    private Date closeTime;

    /**
     * 进站时间
     */
    @Column(name = "arrive_warehouse_time")
    private Date arriveWarehouseTime;

    /**
     * 送达时间
     */
    @Column(name = "arrive_destination_time")
    private Date arriveDestinationTime;

    /**
     * 进厂时间
     */
    @Column(name = "enter_factory_time")
    private Date enterFactoryTime;

    /**
     * 出站时间
     */
    @Column(name = "leave_warehouse_time")
    private Date leaveWarehouseTime;

    /**
     * 完成时间
     */
    @Column(name = "complete_time")
    private Date completeTime;

    /**
     * 同步标识:1未同步 2: 正向同步 3:反向同步
     */
    @Column(name = "sync_flag")
    private String syncFlag;

    /**
     * 同步失败原因
     */
    @Column(name = "sync_fail_reason")
    private String syncFailReason;

    /**
     * 外部船运计划ID
     */
    @Column(name = "external_waybill_id")
    private String externalWaybillId;

    /**
     * 外部运单号
     */
    @Column(name = "external_waybill_num")
    private String externalWaybillNum;

    /**
     * 外部运单状态
     */
    @Column(name = "external_waybill_status")
    private String externalWaybillStatus;

    /**
     * ERP出厂编号
     */
    @Column(name = "factory_number")
    private String factoryNumber;

    /**
     * 外部请求号
     */
    @Column(name = "external_request_no")
    private String externalRequestNo;

    /**
     * 产地
     */
    @Column(name = "origin_place")
    private String originPlace;

    /**
     * 预估配送里程
     */
    @Column(name = "estimate_km")
    private BigDecimal estimateKm;

    /**
     * 实际配送里程
     */
    @Column(name = "actual_km")
    private BigDecimal actualKm;

    /**
     * 预估配送时长
     */
    @Column(name = "estimate_duration")
    private BigDecimal estimateDuration;

    /**
     * 预估运费
     */
    @Column(name = "estimate_carriage")
    private BigDecimal estimateCarriage;

    /**
     * 发布运费
     */
    @Column(name = "published_carriage")
    private BigDecimal publishedCarriage;

    /**
     * 审核通过时间
     */
    @Column(name = "approval_time")
    private Date approvalTime;

    /**
     * 接收运单时间
     */
    @Column(name = "receive_time")
    private Date receiveTime;

    /**
     * 指派时间
     */
    @Column(name = "assign_time")
    private Date assignTime;

    /**
     * 监控进站时间
     */
    @Column(name = "monitor_enter_time")
    private Date monitorEnterTime;

    /**
     * 监控出站时间
     */
    @Column(name = "monitor_outer_time")
    private Date monitorOuterTime;

    /**
     * 期望配送时间
     */
    @Column(name = "delivery_time")
    private Date deliveryTime;

    /**
     * 期望开始时间
     */
    @Column(name = "delivery_time_start")
    private Date deliveryTimeStart;

    /**
     * 期望结束时间
     */
    @Column(name = "delivery_time_end")
    private Date deliveryTimeEnd;

    /**
     * 计划到港时间
     */
    @Column(name = "plan_arrive_time")
    private Date planArriveTime;

    /**
     * 出货点ID
     */
    @Column(name = "warehouse_id")
    private String warehouseId;

    /**
     * 仓库类型
     */
    @Column(name = "warehouse_type")
    private String warehouseType;

    /**
     * 仓库名称
     */
    @Column(name = "warehouse_name")
    private String warehouseName;

    /**
     * 出货点省
     */
    @Column(name = "warehouse_province")
    private String warehouseProvince;

    /**
     * 出货点省编码
     */
    @Column(name = "warehouse_province_code")
    private String warehouseProvinceCode;

    /**
     * 出货点市
     */
    @Column(name = "warehouse_city")
    private String warehouseCity;

    /**
     * 出货点市编码
     */
    @Column(name = "warehouse_city_code")
    private String warehouseCityCode;

    /**
     * 出货点区
     */
    @Column(name = "warehouse_district")
    private String warehouseDistrict;

    /**
     * 出货点区编码
     */
    @Column(name = "warehouse_district_code")
    private String warehouseDistrictCode;

    /**
     * 出货点街道
     */
    @Column(name = "warehouse_street")
    private String warehouseStreet;

    /**
     * 出货点街道编码
     */
    @Column(name = "warehouse_street_code")
    private String warehouseStreetCode;

    /**
     * 仓库地址
     */
    @Column(name = "warehouse_address")
    private String warehouseAddress;

    /**
     * 仓库坐标
     */
    @Column(name = "warehouse_location")
    private String warehouseLocation;

    /**
     * 起运港ID
     */
    @Column(name = "load_port_id")
    private String loadPortId;

    /**
     * 起运港名
     */
    @Column(name = "load_port_name")
    private String loadPortName;

    /**
     * 结算目标港ID
     */
    @Column(name = "unload_port_id")
    private String unloadPortId;

    /**
     * 结算目标港名
     */
    @Column(name = "unload_port_name")
    private String unloadPortName;

    /**
     * 选择泵号
     */
    @Column(name = "pump_no")
    private String pumpNo;

    /**
     * 泵手名称
     */
    @Column(name = "pumper_name")
    private String pumperName;

    /**
     * 所有泵号
     */
    @Column(name = "all_pump_no")
    private String allPumpNo;

    /**
     * 所有泵手名称
     */
    @Column(name = "all_pumper_name")
    private String allPumperName;

    /**
     * 润管砂浆数量
     */
    @Column(name = "lubricity_quantity")
    private BigDecimal lubricityQuantity;

    /**
     * 润管砂浆单价
     */
    @Column(name = "lubricity_price")
    private BigDecimal lubricityPrice;

    /**
     * 退货状态
     */
    @Column(name = "refund_status")
    private String refundStatus;

    /**
     * 退货原因
     */
    @Column(name = "refund_reason")
    private String refundReason;

    /**
     * 退货说明
     */
    @Column(name = "refund_remark")
    private String refundRemark;

    /**
     * 删除标识
     */
    @Column(name = "del_flg")
    private Byte delFlg;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 数据版本
     */
    private Long version;

    private static final long serialVersionUID = 1L;

    /**
     * 获取运单主键
     *
     * @return waybill_id - 运单主键
     */
    public String getWaybillId() {
        return waybillId;
    }

    /**
     * 设置运单主键
     *
     * @param waybillId 运单主键
     */
    public void setWaybillId(String waybillId) {
        this.waybillId = waybillId == null ? null : waybillId.trim();
    }

    /**
     * 获取运单编号
     *
     * @return waybill_num - 运单编号
     */
    public String getWaybillNum() {
        return waybillNum;
    }

    /**
     * 设置运单编号
     *
     * @param waybillNum 运单编号
     */
    public void setWaybillNum(String waybillNum) {
        this.waybillNum = waybillNum == null ? null : waybillNum.trim();
    }

    /**
     * 获取是否为整合运单的父级运单
     *
     * @return parent_flag - 是否为整合运单的父级运单
     */
    public Byte getParentFlag() {
        return parentFlag;
    }

    /**
     * 设置是否为整合运单的父级运单
     *
     * @param parentFlag 是否为整合运单的父级运单
     */
    public void setParentFlag(Byte parentFlag) {
        this.parentFlag = parentFlag;
    }

    /**
     * 获取是否为整合运单的子运单
     *
     * @return child_flag - 是否为整合运单的子运单
     */
    public Byte getChildFlag() {
        return childFlag;
    }

    /**
     * 设置是否为整合运单的子运单
     *
     * @param childFlag 是否为整合运单的子运单
     */
    public void setChildFlag(Byte childFlag) {
        this.childFlag = childFlag;
    }

    /**
     * 获取父运单ID,没有存当前主键
     *
     * @return parent_id - 父运单ID,没有存当前主键
     */
    public String getParentId() {
        return parentId;
    }

    /**
     * 设置父运单ID,没有存当前主键
     *
     * @param parentId 父运单ID,没有存当前主键
     */
    public void setParentId(String parentId) {
        this.parentId = parentId == null ? null : parentId.trim();
    }

    /**
     * 获取承运商id
     *
     * @return carrier_id - 承运商id
     */
    public String getCarrierId() {
        return carrierId;
    }

    /**
     * 设置承运商id
     *
     * @param carrierId 承运商id
     */
    public void setCarrierId(String carrierId) {
        this.carrierId = carrierId == null ? null : carrierId.trim();
    }

    /**
     * 获取承运人名
     *
     * @return carrier_name - 承运人名
     */
    public String getCarrierName() {
        return carrierName;
    }

    /**
     * 设置承运人名
     *
     * @param carrierName 承运人名
     */
    public void setCarrierName(String carrierName) {
        this.carrierName = carrierName == null ? null : carrierName.trim();
    }

    /**
     * 获取承运商类型
     *
     * @return carrier_type - 承运商类型
     */
    public String getCarrierType() {
        return carrierType;
    }

    /**
     * 设置承运商类型
     *
     * @param carrierType 承运商类型
     */
    public void setCarrierType(String carrierType) {
        this.carrierType = carrierType == null ? null : carrierType.trim();
    }

    /**
     * 获取运输工具类型
     *
     * @return transport_tool_type - 运输工具类型
     */
    public String getTransportToolType() {
        return transportToolType;
    }

    /**
     * 设置运输工具类型
     *
     * @param transportToolType 运输工具类型
     */
    public void setTransportToolType(String transportToolType) {
        this.transportToolType = transportToolType == null ? null : transportToolType.trim();
    }

    /**
     * 获取驾驶员id
     *
     * @return driver_id - 驾驶员id
     */
    public String getDriverId() {
        return driverId;
    }

    /**
     * 设置驾驶员id
     *
     * @param driverId 驾驶员id
     */
    public void setDriverId(String driverId) {
        this.driverId = driverId == null ? null : driverId.trim();
    }

    /**
     * 获取驾驶员姓名
     *
     * @return driver_name - 驾驶员姓名
     */
    public String getDriverName() {
        return driverName;
    }

    /**
     * 设置驾驶员姓名
     *
     * @param driverName 驾驶员姓名
     */
    public void setDriverName(String driverName) {
        this.driverName = driverName == null ? null : driverName.trim();
    }

    /**
     * 获取驾驶员电话
     *
     * @return driver_phone - 驾驶员电话
     */
    public String getDriverPhone() {
        return driverPhone;
    }

    /**
     * 设置驾驶员电话
     *
     * @param driverPhone 驾驶员电话
     */
    public void setDriverPhone(String driverPhone) {
        this.driverPhone = driverPhone == null ? null : driverPhone.trim();
    }

    /**
     * 获取船舶ID
     *
     * @return shipping_id - 船舶ID
     */
    public String getShippingId() {
        return shippingId;
    }

    /**
     * 设置船舶ID
     *
     * @param shippingId 船舶ID
     */
    public void setShippingId(String shippingId) {
        this.shippingId = shippingId == null ? null : shippingId.trim();
    }

    /**
     * 获取船舶名称
     *
     * @return shipping_name - 船舶名称
     */
    public String getShippingName() {
        return shippingName;
    }

    /**
     * 设置船舶名称
     *
     * @param shippingName 船舶名称
     */
    public void setShippingName(String shippingName) {
        this.shippingName = shippingName == null ? null : shippingName.trim();
    }

    /**
     * 获取船舶编号
     *
     * @return shipping_no - 船舶编号
     */
    public String getShippingNo() {
        return shippingNo;
    }

    /**
     * 设置船舶编号
     *
     * @param shippingNo 船舶编号
     */
    public void setShippingNo(String shippingNo) {
        this.shippingNo = shippingNo == null ? null : shippingNo.trim();
    }

    /**
     * 获取车辆ID
     *
     * @return vehicle_id - 车辆ID
     */
    public String getVehicleId() {
        return vehicleId;
    }

    /**
     * 设置车辆ID
     *
     * @param vehicleId 车辆ID
     */
    public void setVehicleId(String vehicleId) {
        this.vehicleId = vehicleId == null ? null : vehicleId.trim();
    }

    /**
     * 获取车牌号
     *
     * @return vehicle_num - 车牌号
     */
    public String getVehicleNum() {
        return vehicleNum;
    }

    /**
     * 设置车牌号
     *
     * @param vehicleNum 车牌号
     */
    public void setVehicleNum(String vehicleNum) {
        this.vehicleNum = vehicleNum == null ? null : vehicleNum.trim();
    }

    /**
     * 获取运单类型
     *
     * @return type - 运单类型
     */
    public String getType() {
        return type;
    }

    /**
     * 设置运单类型
     *
     * @param type 运单类型
     */
    public void setType(String type) {
        this.type = type == null ? null : type.trim();
    }

    /**
     * 获取是否内部调拨单,00-否，01-是
     *
     * @return internal - 是否内部调拨单,00-否，01-是
     */
    public String getInternal() {
        return internal;
    }

    /**
     * 设置是否内部调拨单,00-否，01-是
     *
     * @param internal 是否内部调拨单,00-否，01-是
     */
    public void setInternal(String internal) {
        this.internal = internal == null ? null : internal.trim();
    }

    /**
     * 获取运单状态
     *
     * @return status - 运单状态
     */
    public String getStatus() {
        return status;
    }

    /**
     * 设置运单状态
     *
     * @param status 运单状态
     */
    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    /**
     * 获取二维码内容
     *
     * @return qr_code - 二维码内容
     */
    public String getQrCode() {
        return qrCode;
    }

    /**
     * 设置二维码内容
     *
     * @param qrCode 二维码内容
     */
    public void setQrCode(String qrCode) {
        this.qrCode = qrCode == null ? null : qrCode.trim();
    }

    /**
     * 获取指派数量
     *
     * @return assign_quantity - 指派数量
     */
    public BigDecimal getAssignQuantity() {
        return assignQuantity;
    }

    /**
     * 设置指派数量
     *
     * @param assignQuantity 指派数量
     */
    public void setAssignQuantity(BigDecimal assignQuantity) {
        this.assignQuantity = assignQuantity;
    }

    /**
     * 获取实际出厂量
     *
     * @return actual_quantity - 实际出厂量
     */
    public BigDecimal getActualQuantity() {
        return actualQuantity;
    }

    /**
     * 设置实际出厂量
     *
     * @param actualQuantity 实际出厂量
     */
    public void setActualQuantity(BigDecimal actualQuantity) {
        this.actualQuantity = actualQuantity;
    }

    /**
     * 获取完成数量
     *
     * @return complete_quantity - 完成数量
     */
    public BigDecimal getCompleteQuantity() {
        return completeQuantity;
    }

    /**
     * 设置完成数量
     *
     * @param completeQuantity 完成数量
     */
    public void setCompleteQuantity(BigDecimal completeQuantity) {
        this.completeQuantity = completeQuantity;
    }

    /**
     * 获取签收数量
     *
     * @return sign_quantity - 签收数量
     */
    public BigDecimal getSignQuantity() {
        return signQuantity;
    }

    /**
     * 设置签收数量
     *
     * @param signQuantity 签收数量
     */
    public void setSignQuantity(BigDecimal signQuantity) {
        this.signQuantity = signQuantity;
    }

    /**
     * 获取签收备注
     *
     * @return sign_remark - 签收备注
     */
    public String getSignRemark() {
        return signRemark;
    }

    /**
     * 设置签收备注
     *
     * @param signRemark 签收备注
     */
    public void setSignRemark(String signRemark) {
        this.signRemark = signRemark == null ? null : signRemark.trim();
    }

    /**
     * 获取应收运费单价
     *
     * @return receivable_carriage_price - 应收运费单价
     */
    public BigDecimal getReceivableCarriagePrice() {
        return receivableCarriagePrice;
    }

    /**
     * 设置应收运费单价
     *
     * @param receivableCarriagePrice 应收运费单价
     */
    public void setReceivableCarriagePrice(BigDecimal receivableCarriagePrice) {
        this.receivableCarriagePrice = receivableCarriagePrice;
    }

    /**
     * 获取应收运费总额
     *
     * @return receivable_carriage_amount - 应收运费总额
     */
    public BigDecimal getReceivableCarriageAmount() {
        return receivableCarriageAmount;
    }

    /**
     * 设置应收运费总额
     *
     * @param receivableCarriageAmount 应收运费总额
     */
    public void setReceivableCarriageAmount(BigDecimal receivableCarriageAmount) {
        this.receivableCarriageAmount = receivableCarriageAmount;
    }

    /**
     * 获取目前最大序列号
     *
     * @return max_seq_num - 目前最大序列号
     */
    public Integer getMaxSeqNum() {
        return maxSeqNum;
    }

    /**
     * 设置目前最大序列号
     *
     * @param maxSeqNum 目前最大序列号
     */
    public void setMaxSeqNum(Integer maxSeqNum) {
        this.maxSeqNum = maxSeqNum;
    }

    /**
     * 获取是否需要监控
     *
     * @return need_monitor - 是否需要监控
     */
    public Byte getNeedMonitor() {
        return needMonitor;
    }

    /**
     * 设置是否需要监控
     *
     * @param needMonitor 是否需要监控
     */
    public void setNeedMonitor(Byte needMonitor) {
        this.needMonitor = needMonitor;
    }

    /**
     * 获取是否可监控
     *
     * @return can_monitor - 是否可监控
     */
    public Byte getCanMonitor() {
        return canMonitor;
    }

    /**
     * 设置是否可监控
     *
     * @param canMonitor 是否可监控
     */
    public void setCanMonitor(Byte canMonitor) {
        this.canMonitor = canMonitor;
    }

    /**
     * 获取取消理由
     *
     * @return cancel_reason - 取消理由
     */
    public String getCancelReason() {
        return cancelReason;
    }

    /**
     * 设置取消理由
     *
     * @param cancelReason 取消理由
     */
    public void setCancelReason(String cancelReason) {
        this.cancelReason = cancelReason == null ? null : cancelReason.trim();
    }

    /**
     * 获取关闭理由
     *
     * @return close_reason - 关闭理由
     */
    public String getCloseReason() {
        return closeReason;
    }

    /**
     * 设置关闭理由
     *
     * @param closeReason 关闭理由
     */
    public void setCloseReason(String closeReason) {
        this.closeReason = closeReason == null ? null : closeReason.trim();
    }

    /**
     * 获取关闭时间
     *
     * @return close_time - 关闭时间
     */
    public Date getCloseTime() {
        return closeTime;
    }

    /**
     * 设置关闭时间
     *
     * @param closeTime 关闭时间
     */
    public void setCloseTime(Date closeTime) {
        this.closeTime = closeTime;
    }

    /**
     * 获取进站时间
     *
     * @return arrive_warehouse_time - 进站时间
     */
    public Date getArriveWarehouseTime() {
        return arriveWarehouseTime;
    }

    /**
     * 设置进站时间
     *
     * @param arriveWarehouseTime 进站时间
     */
    public void setArriveWarehouseTime(Date arriveWarehouseTime) {
        this.arriveWarehouseTime = arriveWarehouseTime;
    }

    /**
     * 获取送达时间
     *
     * @return arrive_destination_time - 送达时间
     */
    public Date getArriveDestinationTime() {
        return arriveDestinationTime;
    }

    /**
     * 设置送达时间
     *
     * @param arriveDestinationTime 送达时间
     */
    public void setArriveDestinationTime(Date arriveDestinationTime) {
        this.arriveDestinationTime = arriveDestinationTime;
    }

    /**
     * 获取进厂时间
     *
     * @return enter_factory_time - 进厂时间
     */
    public Date getEnterFactoryTime() {
        return enterFactoryTime;
    }

    /**
     * 设置进厂时间
     *
     * @param enterFactoryTime 进厂时间
     */
    public void setEnterFactoryTime(Date enterFactoryTime) {
        this.enterFactoryTime = enterFactoryTime;
    }

    /**
     * 获取出站时间
     *
     * @return leave_warehouse_time - 出站时间
     */
    public Date getLeaveWarehouseTime() {
        return leaveWarehouseTime;
    }

    /**
     * 设置出站时间
     *
     * @param leaveWarehouseTime 出站时间
     */
    public void setLeaveWarehouseTime(Date leaveWarehouseTime) {
        this.leaveWarehouseTime = leaveWarehouseTime;
    }

    /**
     * 获取完成时间
     *
     * @return complete_time - 完成时间
     */
    public Date getCompleteTime() {
        return completeTime;
    }

    /**
     * 设置完成时间
     *
     * @param completeTime 完成时间
     */
    public void setCompleteTime(Date completeTime) {
        this.completeTime = completeTime;
    }

    /**
     * 获取同步标识:1未同步 2: 正向同步 3:反向同步
     *
     * @return sync_flag - 同步标识:1未同步 2: 正向同步 3:反向同步
     */
    public String getSyncFlag() {
        return syncFlag;
    }

    /**
     * 设置同步标识:1未同步 2: 正向同步 3:反向同步
     *
     * @param syncFlag 同步标识:1未同步 2: 正向同步 3:反向同步
     */
    public void setSyncFlag(String syncFlag) {
        this.syncFlag = syncFlag == null ? null : syncFlag.trim();
    }

    /**
     * 获取同步失败原因
     *
     * @return sync_fail_reason - 同步失败原因
     */
    public String getSyncFailReason() {
        return syncFailReason;
    }

    /**
     * 设置同步失败原因
     *
     * @param syncFailReason 同步失败原因
     */
    public void setSyncFailReason(String syncFailReason) {
        this.syncFailReason = syncFailReason == null ? null : syncFailReason.trim();
    }

    /**
     * 获取外部船运计划ID
     *
     * @return external_waybill_id - 外部船运计划ID
     */
    public String getExternalWaybillId() {
        return externalWaybillId;
    }

    /**
     * 设置外部船运计划ID
     *
     * @param externalWaybillId 外部船运计划ID
     */
    public void setExternalWaybillId(String externalWaybillId) {
        this.externalWaybillId = externalWaybillId == null ? null : externalWaybillId.trim();
    }

    /**
     * 获取外部运单号
     *
     * @return external_waybill_num - 外部运单号
     */
    public String getExternalWaybillNum() {
        return externalWaybillNum;
    }

    /**
     * 设置外部运单号
     *
     * @param externalWaybillNum 外部运单号
     */
    public void setExternalWaybillNum(String externalWaybillNum) {
        this.externalWaybillNum = externalWaybillNum == null ? null : externalWaybillNum.trim();
    }

    /**
     * 获取外部运单状态
     *
     * @return external_waybill_status - 外部运单状态
     */
    public String getExternalWaybillStatus() {
        return externalWaybillStatus;
    }

    /**
     * 设置外部运单状态
     *
     * @param externalWaybillStatus 外部运单状态
     */
    public void setExternalWaybillStatus(String externalWaybillStatus) {
        this.externalWaybillStatus = externalWaybillStatus == null ? null : externalWaybillStatus.trim();
    }

    /**
     * 获取ERP出厂编号
     *
     * @return factory_nNumber - ERP出厂编号
     */
    public String getFactoryNumber() {
        return factoryNumber;
    }

    /**
     * 设置ERP出厂编号
     *
     * @param factoryNumber ERP出厂编号
     */
    public void setFactoryNumber(String factoryNumber) {
        this.factoryNumber = factoryNumber == null ? null : factoryNumber.trim();
    }

    /**
     * 获取外部请求号
     *
     * @return external_request_no - 外部请求号
     */
    public String getExternalRequestNo() {
        return externalRequestNo;
    }

    /**
     * 设置外部请求号
     *
     * @param externalRequestNo 外部请求号
     */
    public void setExternalRequestNo(String externalRequestNo) {
        this.externalRequestNo = externalRequestNo == null ? null : externalRequestNo.trim();
    }

    public String getOriginPlace() {
        return originPlace;
    }

    public void setOriginPlace(String originPlace) {
        this.originPlace = originPlace;
    }

    /**
     * 获取预估配送里程
     *
     * @return estimate_km - 预估配送里程
     */
    public BigDecimal getEstimateKm() {
        return estimateKm;
    }

    /**
     * 设置预估配送里程
     *
     * @param estimateKm 预估配送里程
     */
    public void setEstimateKm(BigDecimal estimateKm) {
        this.estimateKm = estimateKm;
    }

    /**
     * 获取实际配送里程
     *
     * @return actual_km - 实际配送里程
     */
    public BigDecimal getActualKm() {
        return actualKm;
    }

    /**
     * 设置实际配送里程
     *
     * @param actualKm 实际配送里程
     */
    public void setActualKm(BigDecimal actualKm) {
        this.actualKm = actualKm;
    }

    /**
     * 获取预估配送时长
     *
     * @return estimate_duration - 预估配送时长
     */
    public BigDecimal getEstimateDuration() {
        return estimateDuration;
    }

    /**
     * 设置预估配送时长
     *
     * @param estimateDuration 预估配送时长
     */
    public void setEstimateDuration(BigDecimal estimateDuration) {
        this.estimateDuration = estimateDuration;
    }

    /**
     * 获取预估运费
     *
     * @return estimate_carriage - 预估运费
     */
    public BigDecimal getEstimateCarriage() {
        return estimateCarriage;
    }

    /**
     * 设置预估运费
     *
     * @param estimateCarriage 预估运费
     */
    public void setEstimateCarriage(BigDecimal estimateCarriage) {
        this.estimateCarriage = estimateCarriage;
    }

    /**
     * 获取发布运费
     *
     * @return published_carriage - 发布运费
     */
    public BigDecimal getPublishedCarriage() {
        return publishedCarriage;
    }

    /**
     * 设置发布运费
     *
     * @param publishedCarriage 发布运费
     */
    public void setPublishedCarriage(BigDecimal publishedCarriage) {
        this.publishedCarriage = publishedCarriage;
    }

    /**
     * 获取审核通过时间
     *
     * @return approval_time - 审核通过时间
     */
    public Date getApprovalTime() {
        return approvalTime;
    }

    /**
     * 设置审核通过时间
     *
     * @param approvalTime 审核通过时间
     */
    public void setApprovalTime(Date approvalTime) {
        this.approvalTime = approvalTime;
    }

    /**
     * 获取接收运单时间
     *
     * @return receive_time - 接收运单时间
     */
    public Date getReceiveTime() {
        return receiveTime;
    }

    /**
     * 设置接收运单时间
     *
     * @param receiveTime 接收运单时间
     */
    public void setReceiveTime(Date receiveTime) {
        this.receiveTime = receiveTime;
    }

    /**
     * 获取指派时间
     *
     * @return assign_time - 指派时间
     */
    public Date getAssignTime() {
        return assignTime;
    }

    /**
     * 设置指派时间
     *
     * @param assignTime 指派时间
     */
    public void setAssignTime(Date assignTime) {
        this.assignTime = assignTime;
    }

    /**
     * 获取监控进站时间
     *
     * @return monitor_enter_time - 监控进站时间
     */
    public Date getMonitorEnterTime() {
        return monitorEnterTime;
    }

    /**
     * 设置监控进站时间
     *
     * @param monitorEnterTime 监控进站时间
     */
    public void setMonitorEnterTime(Date monitorEnterTime) {
        this.monitorEnterTime = monitorEnterTime;
    }

    /**
     * 获取监控出站时间
     *
     * @return monitor_outer_time - 监控出站时间
     */
    public Date getMonitorOuterTime() {
        return monitorOuterTime;
    }

    /**
     * 设置监控出站时间
     *
     * @param monitorOuterTime 监控出站时间
     */
    public void setMonitorOuterTime(Date monitorOuterTime) {
        this.monitorOuterTime = monitorOuterTime;
    }

    /**
     * 获取出货点ID
     *
     * @return warehouse_id - 出货点ID
     */
    public String getWarehouseId() {
        return warehouseId;
    }

    /**
     * 设置出货点ID
     *
     * @param warehouseId 出货点ID
     */
    public void setWarehouseId(String warehouseId) {
        this.warehouseId = warehouseId == null ? null : warehouseId.trim();
    }

    /**
     * 获取仓库类型
     *
     * @return warehouse_type - 仓库类型
     */
    public String getWarehouseType() {
        return warehouseType;
    }

    /**
     * 设置仓库类型
     *
     * @param warehouseType 仓库类型
     */
    public void setWarehouseType(String warehouseType) {
        this.warehouseType = warehouseType == null ? null : warehouseType.trim();
    }

    /**
     * 获取仓库名称
     *
     * @return warehouse_name - 仓库名称
     */
    public String getWarehouseName() {
        return warehouseName;
    }

    /**
     * 设置仓库名称
     *
     * @param warehouseName 仓库名称
     */
    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName == null ? null : warehouseName.trim();
    }

    /**
     * 获取出货点省
     *
     * @return warehouse_province - 出货点省
     */
    public String getWarehouseProvince() {
        return warehouseProvince;
    }

    /**
     * 设置出货点省
     *
     * @param warehouseProvince 出货点省
     */
    public void setWarehouseProvince(String warehouseProvince) {
        this.warehouseProvince = warehouseProvince == null ? null : warehouseProvince.trim();
    }

    /**
     * 获取出货点省编码
     *
     * @return warehouse_province_code - 出货点省编码
     */
    public String getWarehouseProvinceCode() {
        return warehouseProvinceCode;
    }

    /**
     * 设置出货点省编码
     *
     * @param warehouseProvinceCode 出货点省编码
     */
    public void setWarehouseProvinceCode(String warehouseProvinceCode) {
        this.warehouseProvinceCode = warehouseProvinceCode == null ? null : warehouseProvinceCode.trim();
    }

    /**
     * 获取出货点市
     *
     * @return warehouse_city - 出货点市
     */
    public String getWarehouseCity() {
        return warehouseCity;
    }

    /**
     * 设置出货点市
     *
     * @param warehouseCity 出货点市
     */
    public void setWarehouseCity(String warehouseCity) {
        this.warehouseCity = warehouseCity == null ? null : warehouseCity.trim();
    }

    /**
     * 获取出货点市编码
     *
     * @return warehouse_city_code - 出货点市编码
     */
    public String getWarehouseCityCode() {
        return warehouseCityCode;
    }

    /**
     * 设置出货点市编码
     *
     * @param warehouseCityCode 出货点市编码
     */
    public void setWarehouseCityCode(String warehouseCityCode) {
        this.warehouseCityCode = warehouseCityCode == null ? null : warehouseCityCode.trim();
    }

    /**
     * 获取出货点区
     *
     * @return warehouse_district - 出货点区
     */
    public String getWarehouseDistrict() {
        return warehouseDistrict;
    }

    /**
     * 设置出货点区
     *
     * @param warehouseDistrict 出货点区
     */
    public void setWarehouseDistrict(String warehouseDistrict) {
        this.warehouseDistrict = warehouseDistrict == null ? null : warehouseDistrict.trim();
    }

    /**
     * 获取出货点区编码
     *
     * @return warehouse_district_code - 出货点区编码
     */
    public String getWarehouseDistrictCode() {
        return warehouseDistrictCode;
    }

    /**
     * 设置出货点区编码
     *
     * @param warehouseDistrictCode 出货点区编码
     */
    public void setWarehouseDistrictCode(String warehouseDistrictCode) {
        this.warehouseDistrictCode = warehouseDistrictCode == null ? null : warehouseDistrictCode.trim();
    }

    /**
     * 获取出货点街道
     *
     * @return warehouse_street - 出货点街道
     */
    public String getWarehouseStreet() {
        return warehouseStreet;
    }

    /**
     * 设置出货点街道
     *
     * @param warehouseStreet 出货点街道
     */
    public void setWarehouseStreet(String warehouseStreet) {
        this.warehouseStreet = warehouseStreet == null ? null : warehouseStreet.trim();
    }

    /**
     * 获取出货点街道编码
     *
     * @return warehouse_street_code - 出货点街道编码
     */
    public String getWarehouseStreetCode() {
        return warehouseStreetCode;
    }

    /**
     * 设置出货点街道编码
     *
     * @param warehouseStreetCode 出货点街道编码
     */
    public void setWarehouseStreetCode(String warehouseStreetCode) {
        this.warehouseStreetCode = warehouseStreetCode == null ? null : warehouseStreetCode.trim();
    }

    /**
     * 获取仓库地址
     *
     * @return warehouse_address - 仓库地址
     */
    public String getWarehouseAddress() {
        return warehouseAddress;
    }

    /**
     * 设置仓库地址
     *
     * @param warehouseAddress 仓库地址
     */
    public void setWarehouseAddress(String warehouseAddress) {
        this.warehouseAddress = warehouseAddress == null ? null : warehouseAddress.trim();
    }

    /**
     * 获取仓库坐标
     *
     * @return warehouse_location - 仓库坐标
     */
    public String getWarehouseLocation() {
        return warehouseLocation;
    }

    /**
     * 设置仓库坐标
     *
     * @param warehouseLocation 仓库坐标
     */
    public void setWarehouseLocation(String warehouseLocation) {
        this.warehouseLocation = warehouseLocation == null ? null : warehouseLocation.trim();
    }

    public String getLoadPortId() {
        return loadPortId;
    }

    public void setLoadPortId(String loadPortId) {
        this.loadPortId = loadPortId;
    }

    public String getLoadPortName() {
        return loadPortName;
    }

    public void setLoadPortName(String loadPortName) {
        this.loadPortName = loadPortName;
    }

    public String getUnloadPortId() {
        return unloadPortId;
    }

    public void setUnloadPortId(String unloadPortId) {
        this.unloadPortId = unloadPortId;
    }

    public String getUnloadPortName() {
        return unloadPortName;
    }

    public void setUnloadPortName(String unloadPortName) {
        this.unloadPortName = unloadPortName;
    }

    public String getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(String ownerId) {
        this.ownerId = ownerId == null ? null : ownerId.trim();
    }

    public String getPumpNo() {
        return pumpNo;
    }

    public void setPumpNo(String pumpNo) {
        this.pumpNo = pumpNo == null ? null : pumpNo.trim();
    }

    public String getPumperName() {
        return pumperName;
    }

    public void setPumperName(String pumperName) {
        this.pumperName = pumperName == null ? null : pumperName.trim();
    }

    public String getAllPumpNo() {
        return allPumpNo;
    }

    public void setAllPumpNo(String allPumpNo) {
        this.allPumpNo = allPumpNo == null ? null : allPumpNo.trim();
    }

    public String getAllPumperName() {
        return allPumperName;
    }

    public void setAllPumperName(String allPumperName) {
        this.allPumperName = allPumperName == null ? null : allPumperName.trim();
    }

    public BigDecimal getLubricityQuantity() {
        return lubricityQuantity;
    }

    public void setLubricityQuantity(BigDecimal lubricityQuantity) {
        this.lubricityQuantity = lubricityQuantity;
    }

    public BigDecimal getLubricityPrice() {
        return lubricityPrice;
    }

    public void setLubricityPrice(BigDecimal lubricityPrice) {
        this.lubricityPrice = lubricityPrice;
    }

    public Date getDeliveryTime() {
        return deliveryTime;
    }

    public void setDeliveryTime(Date deliveryTime) {
        this.deliveryTime = deliveryTime;
    }

    public Date getDeliveryTimeStart() {
        return deliveryTimeStart;
    }

    public void setDeliveryTimeStart(Date deliveryTimeStart) {
        this.deliveryTimeStart = deliveryTimeStart;
    }

    public Date getDeliveryTimeEnd() {
        return deliveryTimeEnd;
    }

    public void setDeliveryTimeEnd(Date deliveryTimeEnd) {
        this.deliveryTimeEnd = deliveryTimeEnd;
    }

    public Date getPlanArriveTime() {
        return planArriveTime;
    }

    public void setPlanArriveTime(Date planArriveTime) {
        this.planArriveTime = planArriveTime;
    }


    public String setRefundStatus(String refundStatus) {
        return this.refundStatus = refundStatus;
    }

    public String getRefundStatus() {
        return refundStatus;
    }

    public String setRefundReason(String refundReason) {
        return this.refundReason = refundReason;
    }

    public String getRefundReason() {
        return refundReason;
    }

    public String setRefundRemark(String refundRemark) {
        return this.refundRemark = refundRemark;
    }

    public String getRefundRemark() {
        return refundRemark;
    }

    /**
     * 获取删除标识
     *
     * @return del_flg - 删除标识
     */
    public Byte getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标识
     *
     * @param delFlg 删除标识
     */
    public void setDelFlg(Byte delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取数据版本
     *
     * @return version - 数据版本
     */
    public Long getVersion() {
        return version;
    }

    /**
     * 设置数据版本
     *
     * @param version 数据版本
     */
    public void setVersion(Long version) {
        this.version = version;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", waybillId=").append(waybillId);
        sb.append(", waybillNum=").append(waybillNum);
        sb.append(", parentFlag=").append(parentFlag);
        sb.append(", childFlag=").append(childFlag);
        sb.append(", ownerId=").append(ownerId);
        sb.append(", parentId=").append(parentId);
        sb.append(", carrierId=").append(carrierId);
        sb.append(", carrierName=").append(carrierName);
        sb.append(", carrierType=").append(carrierType);
        sb.append(", transportToolType=").append(transportToolType);
        sb.append(", driverId=").append(driverId);
        sb.append(", driverName=").append(driverName);
        sb.append(", driverPhone=").append(driverPhone);
        sb.append(", shippingId=").append(shippingId);
        sb.append(", shippingName=").append(shippingName);
        sb.append(", shippingNo=").append(shippingNo);
        sb.append(", vehicleId=").append(vehicleId);
        sb.append(", vehicleNum=").append(vehicleNum);
        sb.append(", type=").append(type);
        sb.append(", internal=").append(internal);
        sb.append(", status=").append(status);
        sb.append(", qrCode=").append(qrCode);
        sb.append(", assignQuantity=").append(assignQuantity);
        sb.append(", actualQuantity=").append(actualQuantity);
        sb.append(", completeQuantity=").append(completeQuantity);
        sb.append(", signQuantity=").append(signQuantity);
        sb.append(", signRemark=").append(signRemark);
        sb.append(", receivableCarriagePrice=").append(receivableCarriagePrice);
        sb.append(", receivableCarriageAmount=").append(receivableCarriageAmount);
        sb.append(", maxSeqNum=").append(maxSeqNum);
        sb.append(", needMonitor=").append(needMonitor);
        sb.append(", canMonitor=").append(canMonitor);
        sb.append(", cancelReason=").append(cancelReason);
        sb.append(", closeReason=").append(closeReason);
        sb.append(", arriveWarehouseTime=").append(arriveWarehouseTime);
        sb.append(", arriveDestinationTime=").append(arriveDestinationTime);
        sb.append(", closeTime=").append(closeTime);
        sb.append(", enterFactoryTime=").append(enterFactoryTime);
        sb.append(", leaveWarehouseTime=").append(leaveWarehouseTime);
        sb.append(", completeTime=").append(completeTime);
        sb.append(", syncFlag=").append(syncFlag);
        sb.append(", syncFailReason=").append(syncFailReason);
        sb.append(", externalWaybillId=").append(externalWaybillId);
        sb.append(", externalWaybillNum=").append(externalWaybillNum);
        sb.append(", externalWaybillStatus=").append(externalWaybillStatus);
        sb.append(", factoryNumber=").append(factoryNumber);
        sb.append(", externalRequestNo=").append(externalRequestNo);
        sb.append(", originPlace=").append(originPlace);
        sb.append(", estimateKm=").append(estimateKm);
        sb.append(", actualKm=").append(actualKm);
        sb.append(", estimateDuration=").append(estimateDuration);
        sb.append(", estimateCarriage=").append(estimateCarriage);
        sb.append(", publishedCarriage=").append(publishedCarriage);
        sb.append(", approvalTime=").append(approvalTime);
        sb.append(", receiveTime=").append(receiveTime);
        sb.append(", assignTime=").append(assignTime);
        sb.append(", monitorEnterTime=").append(monitorEnterTime);
        sb.append(", monitorOuterTime=").append(monitorOuterTime);
        sb.append(", deliveryTime=").append(deliveryTime);
        sb.append(", deliveryTimeStart=").append(deliveryTimeStart);
        sb.append(", deliveryTimeEnd=").append(deliveryTimeEnd);
        sb.append(", planArriveTime=").append(planArriveTime);
        sb.append(", warehouseId=").append(warehouseId);
        sb.append(", warehouseType=").append(warehouseType);
        sb.append(", warehouseName=").append(warehouseName);
        sb.append(", warehouseProvince=").append(warehouseProvince);
        sb.append(", warehouseProvinceCode=").append(warehouseProvinceCode);
        sb.append(", warehouseCity=").append(warehouseCity);
        sb.append(", warehouseCityCode=").append(warehouseCityCode);
        sb.append(", warehouseDistrict=").append(warehouseDistrict);
        sb.append(", warehouseDistrictCode=").append(warehouseDistrictCode);
        sb.append(", warehouseStreet=").append(warehouseStreet);
        sb.append(", warehouseStreetCode=").append(warehouseStreetCode);
        sb.append(", warehouseAddress=").append(warehouseAddress);
        sb.append(", warehouseLocation=").append(warehouseLocation);
        sb.append(", loadPortId=").append(loadPortId);
        sb.append(", loadPortName=").append(loadPortName);
        sb.append(", unloadPortId=").append(unloadPortId);
        sb.append(", unloadPortName=").append(unloadPortName);
        sb.append(", pumpNo=").append(pumpNo);
        sb.append(", pumperName=").append(pumperName);
        sb.append(", allPumpNo=").append(allPumpNo);
        sb.append(", allPumperName=").append(allPumperName);
        sb.append(", lubricityQuantity=").append(lubricityQuantity);
        sb.append(", lubricityPrice=").append(lubricityPrice);
        sb.append(", refundStatus=").append(refundStatus);
        sb.append(", refundReason=").append(refundReason);
        sb.append(", refundRemark=").append(refundRemark);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", version=").append(version);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}