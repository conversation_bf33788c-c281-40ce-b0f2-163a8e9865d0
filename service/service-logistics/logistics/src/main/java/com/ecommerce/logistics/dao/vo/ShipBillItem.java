package com.ecommerce.logistics.dao.vo;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Table(name = "lgs_ship_bill_item")
public class ShipBillItem implements Serializable {
    /**
     * 运单子项ID
     */
    @Id
    @Column(name = "waybill_item_id")
    private String waybillItemId;

    /**
     * 运单ID
     */
    @Column(name = "waybill_id")
    private String waybillId;

    /**
     * 运单编号
     */
    @Column(name = "waybill_num")
    private String waybillNum;

    /**
     * 配送信息ID
     */
    @Column(name = "delivery_info_id")
    private String deliveryInfoId;

    /**
     * 委托单ID
     */
    @Column(name = "delivery_bill_id")
    private String deliveryBillId;

    /**
     * 委托单ID
     */
    @Column(name = "real_delivery_bill_num")
    private String realDeliveryBillNum;

    /**
     * 子项添加序列号
     */
    @Column(name = "seq_num")
    private Integer seqNum;

    /**
     * 运单类型 1：社会运力抢单  2：承运商指派 3：买家自提
     */
    private String type;

    /**
     * 运单子项交易状态
     */
    private String status;

    /**
     * 支付状态
     */
    @Column(name = "pay_status")
    private String payStatus;

    /**
     * 单据背靠背层级
     */
    @Column(name = "bill_proxy_type")
    private String billProxyType;

    /**
     * 是否可操作
     */
    @Column(name = "can_operate")
    private Byte canOperate;

    /**
     * 是否为数量计算层级
     */
    @Column(name = "calc_flag")
    private Byte calcFlag;

    /**
     * 完成量
     */
    @Column(name = "complete_quantity")
    private BigDecimal completeQuantity;

    /**
     * 完成时间
     */
    @Column(name = "complete_time")
    private Date completeTime;

    /**
     * 支付数量adjust_num
     */
    @Column(name = "pay_quantity")
    private BigDecimal payQuantity;

    /**
     * 商品单价
     */
    @Column(name = "goods_price")
    private BigDecimal goodsPrice;

    /**
     * 结算总金额
     */
    @Column(name = "settlement_amount")
    private BigDecimal settlementAmount;

    /**
     * 应收运费单价
     */
    @Column(name = "receivable_carriage_price")
    private BigDecimal receivableCarriagePrice;

    /**
     * 应收运费总额
     */
    @Column(name = "receivable_carriage_amount")
    private BigDecimal receivableCarriageAmount;

    @Column(name = "origin_price")
    private BigDecimal originPrice;

    @Column(name = "adjust_num")
    private Integer adjustNum;

    @Column(name = "adjust_status")
    private Integer adjustStatus;

    /**
     * 是否计算空载费
     */
    @Column(name = "empty_load_flag")
    private Byte emptyLoadFlag;

    /**
     * 空载费
     */
    @Column(name = "empty_load_charge")
    private BigDecimal emptyLoadCharge;

    /**
     * 空载方量
     */
    @Column(name = "empty_load_quantity")
    private BigDecimal emptyLoadQuantity;

    /**
     * 删除标识
     */
    @Column(name = "del_flg")
    private Byte delFlg;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 数据版本
     */
    private Long version;

    private static final long serialVersionUID = 1L;

    /**
     * 获取运单子项ID
     *
     * @return waybill_item_id - 运单子项ID
     */
    public String getWaybillItemId() {
        return waybillItemId;
    }

    /**
     * 设置运单子项ID
     *
     * @param waybillItemId 运单子项ID
     */
    public void setWaybillItemId(String waybillItemId) {
        this.waybillItemId = waybillItemId == null ? null : waybillItemId.trim();
    }

    /**
     * 获取运单ID
     *
     * @return waybill_id - 运单ID
     */
    public String getWaybillId() {
        return waybillId;
    }

    /**
     * 设置运单ID
     *
     * @param waybillId 运单ID
     */
    public void setWaybillId(String waybillId) {
        this.waybillId = waybillId == null ? null : waybillId.trim();
    }

    /**
     * 获取运单编号
     *
     * @return waybill_num - 运单编号
     */
    public String getWaybillNum() {
        return waybillNum;
    }

    /**
     * 设置运单编号
     *
     * @param waybillNum 运单编号
     */
    public void setWaybillNum(String waybillNum) {
        this.waybillNum = waybillNum == null ? null : waybillNum.trim();
    }

    /**
     * 获取配送信息ID
     *
     * @return delivery_info_id - 配送信息ID
     */
    public String getDeliveryInfoId() {
        return deliveryInfoId;
    }

    /**
     * 设置配送信息ID
     *
     * @param deliveryInfoId 配送信息ID
     */
    public void setDeliveryInfoId(String deliveryInfoId) {
        this.deliveryInfoId = deliveryInfoId == null ? null : deliveryInfoId.trim();
    }

    /**
     * 获取委托单ID
     *
     * @return delivery_bill_id - 委托单ID
     */
    public String getDeliveryBillId() {
        return deliveryBillId;
    }

    /**
     * 设置委托单ID
     *
     * @param deliveryBillId 委托单ID
     */
    public void setDeliveryBillId(String deliveryBillId) {
        this.deliveryBillId = deliveryBillId == null ? null : deliveryBillId.trim();
    }

    public String getRealDeliveryBillNum() {
        return realDeliveryBillNum;
    }

    public void setRealDeliveryBillNum(String realDeliveryBillNum) {
        this.realDeliveryBillNum = realDeliveryBillNum;
    }

    /**
     * 获取子项添加序列号
     *
     * @return seq_num - 子项添加序列号
     */
    public Integer getSeqNum() {
        return seqNum;
    }

    /**
     * 设置子项添加序列号
     *
     * @param seqNum 子项添加序列号
     */
    public void setSeqNum(Integer seqNum) {
        this.seqNum = seqNum;
    }

    /**
     * 获取运单类型 1：社会运力抢单  2：承运商指派 3：买家自提
     *
     * @return type - 运单类型 1：社会运力抢单  2：承运商指派 3：买家自提
     */
    public String getType() {
        return type;
    }

    /**
     * 设置运单类型 1：社会运力抢单  2：承运商指派 3：买家自提
     *
     * @param type 运单类型 1：社会运力抢单  2：承运商指派 3：买家自提
     */
    public void setType(String type) {
        this.type = type == null ? null : type.trim();
    }

    /**
     * 获取运单子项交易状态
     *
     * @return status - 运单子项交易状态
     */
    public String getStatus() {
        return status;
    }

    /**
     * 设置运单子项交易状态
     *
     * @param status 运单子项交易状态
     */
    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    /**
     * 获取支付状态
     *
     * @return pay_status - 支付状态
     */
    public String getPayStatus() {
        return payStatus;
    }

    /**
     * 设置支付状态
     *
     * @param payStatus 支付状态
     */
    public void setPayStatus(String payStatus) {
        this.payStatus = payStatus == null ? null : payStatus.trim();
    }

    /**
     * 获取单据背靠背层级
     *
     * @return bill_proxy_type - 单据背靠背层级
     */
    public String getBillProxyType() {
        return billProxyType;
    }

    /**
     * 设置单据背靠背层级
     *
     * @param billProxyType 单据背靠背层级
     */
    public void setBillProxyType(String billProxyType) {
        this.billProxyType = billProxyType == null ? null : billProxyType.trim();
    }

    /**
     * 获取是否可操作
     *
     * @return can_operate - 是否可操作
     */
    public Byte getCanOperate() {
        return canOperate;
    }

    /**
     * 设置是否可操作
     *
     * @param canOperate 是否可操作
     */
    public void setCanOperate(Byte canOperate) {
        this.canOperate = canOperate;
    }

    /**
     * 获取是否为数量计算层级
     *
     * @return calc_flag - 是否为数量计算层级
     */
    public Byte getCalcFlag() {
        return calcFlag;
    }

    /**
     * 设置是否为数量计算层级
     *
     * @param calcFlag 是否为数量计算层级
     */
    public void setCalcFlag(Byte calcFlag) {
        this.calcFlag = calcFlag;
    }

    /**
     * 获取完成量
     *
     * @return complete_quantity - 完成量
     */
    public BigDecimal getCompleteQuantity() {
        return completeQuantity;
    }

    /**
     * 设置完成量
     *
     * @param completeQuantity 完成量
     */
    public void setCompleteQuantity(BigDecimal completeQuantity) {
        this.completeQuantity = completeQuantity;
    }

    /**
     * 获取完成时间
     *
     * @return complete_time - 完成时间
     */
    public Date getCompleteTime() {
        return completeTime;
    }

    /**
     * 设置完成时间
     *
     * @param completeTime 完成时间
     */
    public void setCompleteTime(Date completeTime) {
        this.completeTime = completeTime;
    }

    /**
     * 获取支付数量
     *
     * @return pay_quantity - 支付数量
     */
    public BigDecimal getPayQuantity() {
        return payQuantity;
    }

    /**
     * 设置支付数量
     *
     * @param payQuantity 支付数量
     */
    public void setPayQuantity(BigDecimal payQuantity) {
        this.payQuantity = payQuantity;
    }

    /**
     * 获取商品单价
     *
     * @return goods_price - 商品单价
     */
    public BigDecimal getGoodsPrice() {
        return goodsPrice;
    }

    /**
     * 设置商品单价
     *
     * @param goodsPrice 商品单价
     */
    public void setGoodsPrice(BigDecimal goodsPrice) {
        this.goodsPrice = goodsPrice;
    }

    /**
     * 获取结算总金额
     *
     * @return settlement_amount - 结算总金额
     */
    public BigDecimal getSettlementAmount() {
        return settlementAmount;
    }

    /**
     * 设置结算总金额
     *
     * @param settlementAmount 结算总金额
     */
    public void setSettlementAmount(BigDecimal settlementAmount) {
        this.settlementAmount = settlementAmount;
    }

    /**
     * 获取应收运费单价
     *
     * @return receivable_carriage_price - 应收运费单价
     */
    public BigDecimal getReceivableCarriagePrice() {
        return receivableCarriagePrice;
    }

    /**
     * 设置应收运费单价
     *
     * @param receivableCarriagePrice 应收运费单价
     */
    public void setReceivableCarriagePrice(BigDecimal receivableCarriagePrice) {
        this.receivableCarriagePrice = receivableCarriagePrice;
    }

    /**
     * 获取应收运费总额
     *
     * @return receivable_carriage_amount - 应收运费总额
     */
    public BigDecimal getReceivableCarriageAmount() {
        return receivableCarriageAmount;
    }

    /**
     * 设置应收运费总额
     *
     * @param receivableCarriageAmount 应收运费总额
     */
    public void setReceivableCarriageAmount(BigDecimal receivableCarriageAmount) {
        this.receivableCarriageAmount = receivableCarriageAmount;
    }

    public BigDecimal getOriginPrice()
    {
        return originPrice;
    }

    public void setOriginPrice(BigDecimal originPrice)
    {
        this.originPrice = originPrice;
    }

    public Integer getAdjustNum()
    {
        return adjustNum;
    }

    public void setAdjustNum(Integer adjustNum)
    {
        this.adjustNum = adjustNum;
    }

    public Integer getAdjustStatus()
    {
        return adjustStatus;
    }

    public void setAdjustStatus(Integer adjustStatus)
    {
        this.adjustStatus = adjustStatus;
    }

    /**
     * 获取是否计算空载费
     *
     * @return empty_load_flag - 是否计算空载费
     */
    public Byte getEmptyLoadFlag() {
        return emptyLoadFlag;
    }

    /**
     * 设置是否计算空载费
     *
     * @param emptyLoadFlag 是否计算空载费
     */
    public void setEmptyLoadFlag(Byte emptyLoadFlag) {
        this.emptyLoadFlag = emptyLoadFlag;
    }

    /**
     * 获取空载费
     *
     * @return empty_load_charge - 空载费
     */
    public BigDecimal getEmptyLoadCharge() {
        return emptyLoadCharge;
    }

    /**
     * 设置空载费
     *
     * @param emptyLoadCharge 空载费
     */
    public void setEmptyLoadCharge(BigDecimal emptyLoadCharge) {
        this.emptyLoadCharge = emptyLoadCharge;
    }

    /**
     * 获取空载方量
     *
     * @return empty_load_quantity - 空载方量
     */
    public BigDecimal getEmptyLoadQuantity() {
        return emptyLoadQuantity;
    }

    /**
     * 设置空载方量
     *
     * @param emptyLoadQuantity 空载方量
     */
    public void setEmptyLoadQuantity(BigDecimal emptyLoadQuantity) {
        this.emptyLoadQuantity = emptyLoadQuantity;
    }

    /**
     * 获取删除标识
     *
     * @return del_flg - 删除标识
     */
    public Byte getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标识
     *
     * @param delFlg 删除标识
     */
    public void setDelFlg(Byte delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取数据版本
     *
     * @return version - 数据版本
     */
    public Long getVersion() {
        return version;
    }

    /**
     * 设置数据版本
     *
     * @param version 数据版本
     */
    public void setVersion(Long version) {
        this.version = version;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", waybillItemId=").append(waybillItemId);
        sb.append(", waybillId=").append(waybillId);
        sb.append(", waybillNum=").append(waybillNum);
        sb.append(", deliveryInfoId=").append(deliveryInfoId);
        sb.append(", deliveryBillId=").append(deliveryBillId);
        sb.append(", realDeliveryBillNum=").append(realDeliveryBillNum);
        sb.append(", seqNum=").append(seqNum);
        sb.append(", type=").append(type);
        sb.append(", status=").append(status);
        sb.append(", payStatus=").append(payStatus);
        sb.append(", billProxyType=").append(billProxyType);
        sb.append(", canOperate=").append(canOperate);
        sb.append(", calcFlag=").append(calcFlag);
        sb.append(", completeQuantity=").append(completeQuantity);
        sb.append(", completeTime=").append(completeTime);
        sb.append(", payQuantity=").append(payQuantity);
        sb.append(", goodsPrice=").append(goodsPrice);
        sb.append(", settlementAmount=").append(settlementAmount);
        sb.append(", receivableCarriagePrice=").append(receivableCarriagePrice);
        sb.append(", receivableCarriageAmount=").append(receivableCarriageAmount);
        sb.append(", emptyLoadFlag=").append(emptyLoadFlag);
        sb.append(", emptyLoadCharge=").append(emptyLoadCharge);
        sb.append(", emptyLoadQuantity=").append(emptyLoadQuantity);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", version=").append(version);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}
