package com.ecommerce.logistics.dao.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;

@Table(name = "lgs_shipping")
public class Shipping implements Serializable {
    /**
     * 船舶id
     */
    @Id
    @Column(name = "shipping_id")
    private String shippingId;

    /**
     * 归属用户id
     */
    @Column(name = "user_id")
    private String userId;

    /**
     * 归属用户名称
     */
    @Column(name = "user_name")
    private String userName;

    /**
     * 用户类型 1：买家 2：卖家 3：承运商
     */
    @Column(name = "user_type")
    private Byte userType;

    /**
     * 船舶编码
     */
    private String number;

    /**
     * 船舶类型
     */
    @Column(name = "shipping_type")
    private String shippingType;

    /**
     * 船舶名称
     */
    @Column(name = "shipping_name")
    private String shippingName;

    /**
     * 运输品类id
     */
    @Column(name = "transport_category_id")
    private String transportCategoryId;

    /**
     * 载重
     */
    @Column(name = "load_capacity")
    private BigDecimal loadCapacity;

    /**
     * 排水量
     */
    private BigDecimal displacement;

    /**
     * 船舶全长
     */
    @Column(name = "overall_length")
    private BigDecimal overallLength;

    /**
     * 最大宽度
     */
    @Column(name = "extreme_width")
    private BigDecimal extremeWidth;

    /**
     * 最大高度
     */
    @Column(name = "extreme_height")
    private BigDecimal extremeHeight;

    /**
     * 认证状态 1：未认证 2：认证中 3：已认证
     */
    @Column(name = "certification_status")
    private String certificationStatus;

    /**
     * 认证失败原因
     */
    @Column(name = "fail_reason")
    private String failReason;

    /**
     * 默认船长id
     */
    @Column(name = "bind_driver_id")
    private String bindDriverId;

    /**
     * 船长名称
     */
    @Column(name = "driver_name")
    private String driverName;

    /**
     * 船长电话
     */
    @Column(name = "driver_phone")
    private String driverPhone;

    /**
     * 删除标识
     */
    @Column(name = "del_flg")
    private Byte delFlg;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 数据版本
     */
    private Long version;

    private static final long serialVersionUID = 1L;

    /**
     * 获取船舶id
     *
     * @return shipping_id - 船舶id
     */
    public String getShippingId() {
        return shippingId;
    }

    /**
     * 设置船舶id
     *
     * @param shippingId 船舶id
     */
    public void setShippingId(String shippingId) {
        this.shippingId = shippingId == null ? null : shippingId.trim();
    }

    /**
     * 获取归属用户id
     *
     * @return user_id - 归属用户id
     */
    public String getUserId() {
        return userId;
    }

    /**
     * 设置归属用户id
     *
     * @param userId 归属用户id
     */
    public void setUserId(String userId) {
        this.userId = userId == null ? null : userId.trim();
    }

    /**
     * 获取归属用户名称
     *
     * @return user_name - 归属用户名称
     */
    public String getUserName() {
        return userName;
    }

    /**
     * 设置归属用户名称
     *
     * @param userName 归属用户名称
     */
    public void setUserName(String userName) {
        this.userName = userName == null ? null : userName.trim();
    }

    /**
     * 获取用户类型 1：买家 2：卖家 3：承运商
     *
     * @return user_type - 用户类型 1：买家 2：卖家 3：承运商
     */
    public Byte getUserType() {
        return userType;
    }

    /**
     * 设置用户类型 1：买家 2：卖家 3：承运商
     *
     * @param userType 用户类型 1：买家 2：卖家 3：承运商
     */
    public void setUserType(Byte userType) {
        this.userType = userType;
    }

    /**
     * 获取船舶编码
     *
     * @return number - 船舶编码
     */
    public String getNumber() {
        return number;
    }

    /**
     * 设置船舶编码
     *
     * @param number 船舶编码
     */
    public void setNumber(String number) {
        this.number = number == null ? null : number.trim();
    }

    /**
     * 获取船舶类型
     *
     * @return shipping_type - 船舶类型
     */
    public String getShippingType() {
        return shippingType;
    }

    /**
     * 设置船舶类型
     *
     * @param shippingType 船舶类型
     */
    public void setShippingType(String shippingType) {
        this.shippingType = shippingType == null ? null : shippingType.trim();
    }

    /**
     * 获取船舶名称
     *
     * @return shipping_name - 船舶名称
     */
    public String getShippingName() {
        return shippingName;
    }

    /**
     * 设置船舶名称
     *
     * @param shippingName 船舶名称
     */
    public void setShippingName(String shippingName) {
        this.shippingName = shippingName == null ? null : shippingName.trim();
    }

    /**
     * 获取运输品类id
     *
     * @return transport_category_id - 运输品类id
     */
    public String getTransportCategoryId() {
        return transportCategoryId;
    }

    /**
     * 设置运输品类id
     *
     * @param transportCategoryId 运输品类id
     */
    public void setTransportCategoryId(String transportCategoryId) {
        this.transportCategoryId = transportCategoryId == null ? null : transportCategoryId.trim();
    }

    /**
     * 获取载重
     *
     * @return load_capacity - 载重
     */
    public BigDecimal getLoadCapacity() {
        return loadCapacity;
    }

    /**
     * 设置载重
     *
     * @param loadCapacity 载重
     */
    public void setLoadCapacity(BigDecimal loadCapacity) {
        this.loadCapacity = loadCapacity;
    }

    /**
     * 获取排水量
     *
     * @return displacement - 排水量
     */
    public BigDecimal getDisplacement() {
        return displacement;
    }

    /**
     * 设置排水量
     *
     * @param displacement 排水量
     */
    public void setDisplacement(BigDecimal displacement) {
        this.displacement = displacement;
    }

    /**
     * 获取船舶全长
     *
     * @return overall_length - 船舶全长
     */
    public BigDecimal getOverallLength() {
        return overallLength;
    }

    /**
     * 设置船舶全长
     *
     * @param overallLength 船舶全长
     */
    public void setOverallLength(BigDecimal overallLength) {
        this.overallLength = overallLength;
    }

    /**
     * 获取最大宽度
     *
     * @return extreme_width - 最大宽度
     */
    public BigDecimal getExtremeWidth() {
        return extremeWidth;
    }

    /**
     * 设置最大宽度
     *
     * @param extremeWidth 最大宽度
     */
    public void setExtremeWidth(BigDecimal extremeWidth) {
        this.extremeWidth = extremeWidth;
    }

    /**
     * 获取最大高度
     *
     * @return extreme_height - 最大高度
     */
    public BigDecimal getExtremeHeight() {
        return extremeHeight;
    }

    /**
     * 设置最大高度
     *
     * @param extremeHeight 最大高度
     */
    public void setExtremeHeight(BigDecimal extremeHeight) {
        this.extremeHeight = extremeHeight;
    }

    /**
     * 获取认证状态 1：未认证 2：认证中 3：已认证
     *
     * @return certification_status - 认证状态 1：未认证 2：认证中 3：已认证
     */
    public String getCertificationStatus() {
        return certificationStatus;
    }

    /**
     * 设置认证状态 1：未认证 2：认证中 3：已认证
     *
     * @param certificationStatus 认证状态 1：未认证 2：认证中 3：已认证
     */
    public void setCertificationStatus(String certificationStatus) {
        this.certificationStatus = certificationStatus == null ? null : certificationStatus.trim();
    }

    /**
     * 获取认证失败原因
     *
     * @return fail_reason - 认证失败原因
     */
    public String getFailReason() {
        return failReason;
    }

    /**
     * 设置认证失败原因
     *
     * @param failReason 认证失败原因
     */
    public void setFailReason(String failReason) {
        this.failReason = failReason == null ? null : failReason.trim();
    }

    /**
     * 获取默认船长id
     *
     * @return bind_driver_id - 默认船长id
     */
    public String getBindDriverId() {
        return bindDriverId;
    }

    /**
     * 设置默认船长id
     *
     * @param bindDriverId 默认船长id
     */
    public void setBindDriverId(String bindDriverId) {
        this.bindDriverId = bindDriverId == null ? null : bindDriverId.trim();
    }

    /**
     * 获取船长名称
     *
     * @return driver_name - 船长名称
     */
    public String getDriverName() {
        return driverName;
    }

    /**
     * 设置船长名称
     *
     * @param driverName 船长名称
     */
    public void setDriverName(String driverName) {
        this.driverName = driverName == null ? null : driverName.trim();
    }

    /**
     * 获取船长电话
     *
     * @return driver_phone - 船长电话
     */
    public String getDriverPhone() {
        return driverPhone;
    }

    /**
     * 设置船长电话
     *
     * @param driverPhone 船长电话
     */
    public void setDriverPhone(String driverPhone) {
        this.driverPhone = driverPhone == null ? null : driverPhone.trim();
    }

    /**
     * 获取删除标识
     *
     * @return del_flg - 删除标识
     */
    public Byte getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标识
     *
     * @param delFlg 删除标识
     */
    public void setDelFlg(Byte delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取数据版本
     *
     * @return version - 数据版本
     */
    public Long getVersion() {
        return version;
    }

    /**
     * 设置数据版本
     *
     * @param version 数据版本
     */
    public void setVersion(Long version) {
        this.version = version;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", shippingId=").append(shippingId);
        sb.append(", userId=").append(userId);
        sb.append(", userName=").append(userName);
        sb.append(", userType=").append(userType);
        sb.append(", number=").append(number);
        sb.append(", shippingType=").append(shippingType);
        sb.append(", shippingName=").append(shippingName);
        sb.append(", transportCategoryId=").append(transportCategoryId);
        sb.append(", loadCapacity=").append(loadCapacity);
        sb.append(", displacement=").append(displacement);
        sb.append(", overallLength=").append(overallLength);
        sb.append(", extremeWidth=").append(extremeWidth);
        sb.append(", extremeHeight=").append(extremeHeight);
        sb.append(", certificationStatus=").append(certificationStatus);
        sb.append(", failReason=").append(failReason);
        sb.append(", bindDriverId=").append(bindDriverId);
        sb.append(", driverName=").append(driverName);
        sb.append(", driverPhone=").append(driverPhone);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", version=").append(version);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}