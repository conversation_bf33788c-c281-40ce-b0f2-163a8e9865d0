package com.ecommerce.logistics.dao.vo;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

@Table(name = "lgs_shipping_audit_log")
public class ShippingAuditLog implements Serializable {
    /**
     * 审核记录主键ID
     */
    @Id
    @Column(name = "shipping_audit_id")
    private Long shippingAuditId;

    /**
     * lgs_shipping_info表船舶ID
     */
    @Column(name = "shipping_id")
    private String shippingId;

    /**
     * 审核状态
     */
    @Column(name = "audit_status")
    private Integer auditStatus;

    /**
     * 驳回原因
     */
    @Column(name = "rejection_reasons")
    private String rejectionReasons;

    /**
     * 删除标识,0-未删除，1-已删除
     */
    @Column(name = "del_flg")
    private Integer delFlg;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 创建用户名称
     */
    @Column(name = "create_user_name")
    private String createUserName;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;

    /**
     * 获取审核记录主键ID
     *
     * @return shipping_audit_id - 审核记录主键ID
     */
    public Long getShippingAuditId() {
        return shippingAuditId;
    }

    /**
     * 设置审核记录主键ID
     *
     * @param shippingAuditId 审核记录主键ID
     */
    public void setShippingAuditId(Long shippingAuditId) {
        this.shippingAuditId = shippingAuditId;
    }

    /**
     * 获取lgs_shipping_info表船舶ID
     *
     * @return shipping_id - lgs_shipping_info表船舶ID
     */
    public String getShippingId() {
        return shippingId;
    }

    /**
     * 设置lgs_shipping_info表船舶ID
     *
     * @param shippingId lgs_shipping_info表船舶ID
     */
    public void setShippingId(String shippingId) {
        this.shippingId = shippingId == null ? null : shippingId.trim();
    }

    /**
     * 获取审核状态
     *
     * @return audit_status - 审核状态
     */
    public Integer getAuditStatus() {
        return auditStatus;
    }

    /**
     * 设置审核状态
     *
     * @param auditStatus 审核状态
     */
    public void setAuditStatus(Integer auditStatus) {
        this.auditStatus = auditStatus;
    }

    /**
     * 获取驳回原因
     *
     * @return rejection_reasons - 驳回原因
     */
    public String getRejectionReasons() {
        return rejectionReasons;
    }

    /**
     * 设置驳回原因
     *
     * @param rejectionReasons 驳回原因
     */
    public void setRejectionReasons(String rejectionReasons) {
        this.rejectionReasons = rejectionReasons == null ? null : rejectionReasons.trim();
    }

    /**
     * 获取删除标识,0-未删除，1-已删除
     *
     * @return del_flg - 删除标识,0-未删除，1-已删除
     */
    public Integer getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标识,0-未删除，1-已删除
     *
     * @param delFlg 删除标识,0-未删除，1-已删除
     */
    public void setDelFlg(Integer delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取创建用户名称
     *
     * @return create_user_name - 创建用户名称
     */
    public String getCreateUserName() {
        return createUserName;
    }

    /**
     * 设置创建用户名称
     *
     * @param createUserName 创建用户名称
     */
    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName == null ? null : createUserName.trim();
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", shippingAuditId=").append(shippingAuditId);
        sb.append(", shippingId=").append(shippingId);
        sb.append(", auditStatus=").append(auditStatus);
        sb.append(", rejectionReasons=").append(rejectionReasons);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", createUserName=").append(createUserName);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}