package com.ecommerce.logistics.dao.vo;


import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;

@Table(name = "lgs_shipping_info")
public class ShippingInfo implements Serializable {
    /**
     * 主键ID
     */
    @Id
    @Column(name = "shipping_id")
    private String shippingId;

    /**
     * 船舶编号
     */
    @Column(name = "shipping_no")
    private String shippingNo;

    /**
     * 船舶名称，需保证未删除的数据名称唯一
     */
    @Column(name = "shipping_name")
    private String shippingName;

    /**
     * 经纪人类型,10-企业承运商，11-个体船东承运商，20-卖家，30-买家
     */
    @Column(name = "manager_member_type")
    private String managerMemberType;

    /**
     * 经纪人ID，即添加这艘船的用户，对该船拥有管理授权权限
     */
    @Column(name = "manager_member_id")
    private String managerMemberId;

    /**
     * 经纪人名称
     */
    @Column(name = "manager_member_name")
    private String managerMemberName;

    /**
     * 船舶类型
     */
    @Column(name = "shipping_type")
    private String shippingType;

    /**
     * 船主名称
     */
    @Column(name = "belonger_name")
    private String belongerName;

    /**
     * 船主手机号码
     */
    @Column(name = "belonger_phone")
    private String belongerPhone;

    /**
     * 船务公司
     */
    @Column(name = "shipping_company")
    private String shippingCompany;

    /**
     * 所有权归属
     */
    @Column(name = "ownership")
    private String ownership;

    /**
     * 起运港ID，多个时以“，”隔开
     */
    @Column(name = "departure_wharf_id")
    private String departureWharfId;

    /**
     * 目的地港ID，多个时以“，”隔开
     */
    @Column(name = "destination_wharf_id")
    private String destinationWharfId;

    /**
     * A级核载吨位(吨)
     */
    @Column(name = "alevel_payload")
    private BigDecimal alevelPayload;

    /**
     * B级核载吨位(吨)
     */
    @Column(name = "blevel_payload")
    private BigDecimal blevelPayload;

    /**
     * 净重(吨)
     */
    @Column(name = "self_payload")
    private BigDecimal selfPayload;

    /**
     * 总吨位(吨)
     */
    @Column(name = "total_payload")
    private BigDecimal totalPayload;

    /**
     * 总长度(米)
     */
    @Column(name = "total_length")
    private BigDecimal totalLength;

    /**
     * 最大宽度(米)
     */
    @Column(name = "max_width")
    private BigDecimal maxWidth;

    /**
     * 船舶的型深(米)
     */
    @Column(name = "moulded_depth")
    private BigDecimal mouldedDepth;

    /**
     * 满载吃水(米)
     */
    @Column(name = "full_draft")
    private BigDecimal fullDraft;

    /**
     * 空载吃水(米)
     */
    @Column(name = "empty_draft")
    private BigDecimal emptyDraft;

    /**
     * 最大装载量
     */
    @Column(name = "max_loading_capacity")
    private BigDecimal maxLoadingCapacity;

    /**
     * 最大船高
     */
    @Column(name = "max_height")
    private BigDecimal maxHeight;

    /**
     * 是否支持管控，0-不管控，1-管控
     */
    @Column(name = "whether_control")
    private Integer whetherControl;

    /**
     * 是否绑定GPS终端，0-无，1-有
     */
    @Column(name = "gps_device")
    private Integer gpsDevice;

    /**
     * GPS设备号
     */
    @Column(name = "gps_device_number")
    private String gpsDeviceNumber;


    /**
     * 船长ID，冗余字段，方便展示
     */
    @Column(name = "captain_account_id")
    private String captainAccountId;

    /**
     * 船长姓名，冗余字段，方便展示
     */
    @Column(name = "captain_name")
    private String captainName;

    /**
     * 船长手机号码，冗余字段，方便展示
     */
    @Column(name = "captain_phone")
    private String captainPhone;

    /**
     * 船舶状态,1-吉船,2-待装货,3-重船,4-检修中,5-已禁用
     */
    @Column(name = "shipping_status")
    private Integer shippingStatus;

    /**
     * 审核状态,1-待提交,2-待审核,3-已通过,4-已驳回,5-更新中,6-更新失败（已审核更新信息被驳回）
     */
    @Column(name = "audit_status")
    private Integer auditStatus;

    /**
     * 驳回原因
     */
    @Column(name = "rejection_reasons")
    private String rejectionReasons;

    /**
     * 数据版本
     */
    @Column(name = "version")
    private Long version;

    /**
     * 删除标识，0-未删除，1-已删除
     */
    @Column(name = "del_flg")
    private Integer delFlg;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;

    /**
     * 获取主键ID
     *
     * @return shipping_id - 主键ID
     */
    public String getShippingId() {
        return shippingId;
    }

    /**
     * 设置主键ID
     *
     * @param shippingId 主键ID
     */
    public void setShippingId(String shippingId) {
        this.shippingId = shippingId == null ? null : shippingId.trim();
    }

    /**
     * 获取船舶编号
     *
     * @return shipping_no - 船舶编号
     */
    public String getShippingNo() {
        return shippingNo;
    }

    /**
     * 设置船舶编号
     *
     * @param shippingNo 船舶编号
     */
    public void setShippingNo(String shippingNo) {
        this.shippingNo = shippingNo == null ? null : shippingNo.trim();
    }

    /**
     * 获取船舶名称，需保证未删除的数据名称唯一
     *
     * @return shipping_name - 船舶名称，需保证未删除的数据名称唯一
     */
    public String getShippingName() {
        return shippingName;
    }

    /**
     * 设置船舶名称，需保证未删除的数据名称唯一
     *
     * @param shippingName 船舶名称，需保证未删除的数据名称唯一
     */
    public void setShippingName(String shippingName) {
        this.shippingName = shippingName == null ? null : shippingName.trim();
    }

    /**
     * 获取经纪人类型,10-企业承运商，11-个体船东承运商，20-卖家，30-买家
     *
     * @return manager_member_type - 经纪人类型,10-企业承运商，11-个体船东承运商，20-卖家，30-买家
     */
    public String getManagerMemberType() {
        return managerMemberType;
    }

    /**
     * 设置经纪人类型,10-企业承运商，11-个体船东承运商，20-卖家，30-买家
     *
     * @param managerMemberType 经纪人类型,10-企业承运商，11-个体船东承运商，20-卖家，30-买家
     */
    public void setManagerMemberType(String managerMemberType) {
        this.managerMemberType = managerMemberType == null ? null : managerMemberType.trim();
    }

    /**
     * 获取经纪人ID，即添加这艘船的用户，对该船拥有管理授权权限
     *
     * @return manager_member_id - 经纪人ID，即添加这艘船的用户，对该船拥有管理授权权限
     */
    public String getManagerMemberId() {
        return managerMemberId;
    }

    /**
     * 设置经纪人ID，即添加这艘船的用户，对该船拥有管理授权权限
     *
     * @param managerMemberId 经纪人ID，即添加这艘船的用户，对该船拥有管理授权权限
     */
    public void setManagerMemberId(String managerMemberId) {
        this.managerMemberId = managerMemberId == null ? null : managerMemberId.trim();
    }

    /**
     * 获取经纪人名称
     *
     * @return manager_member_name - 经纪人名称
     */
    public String getManagerMemberName() {
        return managerMemberName;
    }

    /**
     * 设置经纪人名称
     *
     * @param managerMemberName 经纪人名称
     */
    public void setManagerMemberName(String managerMemberName) {
        this.managerMemberName = managerMemberName == null ? null : managerMemberName.trim();
    }

    /**
     * 获取船舶类型
     *
     * @return shipping_type - 船舶类型
     */
    public String getShippingType() {
        return shippingType;
    }

    /**
     * 设置船舶类型
     *
     * @param shippingType 船舶类型
     */
    public void setShippingType(String shippingType) {
        this.shippingType = shippingType == null ? null : shippingType.trim();
    }

    /**
     * 获取船主名称
     *
     * @return belonger_name - 船主名称
     */
    public String getBelongerName() {
        return belongerName;
    }

    /**
     * 设置船主名称
     *
     * @param belongerName 船主名称
     */
    public void setBelongerName(String belongerName) {
        this.belongerName = belongerName == null ? null : belongerName.trim();
    }

    /**
     * 获取船主手机号码
     *
     * @return belonger_phone - 船主手机号码
     */
    public String getBelongerPhone() {
        return belongerPhone;
    }

    /**
     * 设置船主手机号码
     *
     * @param belongerPhone 船主手机号码
     */
    public void setBelongerPhone(String belongerPhone) {
        this.belongerPhone = belongerPhone == null ? null : belongerPhone.trim();
    }

    /**
     * 获取船务公司
     *
     * @return shipping_company - 船务公司
     */
    public String getShippingCompany() {
        return shippingCompany;
    }

    /**
     * 设置船务公司
     *
     * @param shippingCompany 船务公司
     */
    public void setShippingCompany(String shippingCompany) {
        this.shippingCompany = shippingCompany == null ? null : shippingCompany.trim();
    }

    /**
     * 获取所有权归属
     *
     * @return ownership - 所有权归属
     */
    public String getOwnership() {
        return ownership;
    }

    /**
     * 设置所有权归属
     *
     * @param ownership 所有权归属
     */
    public void setOwnership(String ownership) {
        this.ownership = ownership == null ? null : ownership.trim();
    }

    /**
     * 获取起运港ID，多个时以“，”隔开
     *
     * @return departure_wharf_id - 起运港ID，多个时以“，”隔开
     */
    public String getDepartureWharfId() {
        return departureWharfId;
    }

    /**
     * 设置起运港ID，多个时以“，”隔开
     *
     * @param departureWharfId 起运港ID，多个时以“，”隔开
     */
    public void setDepartureWharfId(String departureWharfId) {
        this.departureWharfId = departureWharfId == null ? null : departureWharfId.trim();
    }

    /**
     * 获取目的地港ID，多个时以“，”隔开
     *
     * @return destination_wharf_id - 目的地港ID，多个时以“，”隔开
     */
    public String getDestinationWharfId() {
        return destinationWharfId;
    }

    /**
     * 设置目的地港ID，多个时以“，”隔开
     *
     * @param destinationWharfId 目的地港ID，多个时以“，”隔开
     */
    public void setDestinationWharfId(String destinationWharfId) {
        this.destinationWharfId = destinationWharfId == null ? null : destinationWharfId.trim();
    }

    /**
     * 获取A级核载吨位(吨)
     *
     * @return alevel_payload - A级核载吨位(吨)
     */
    public BigDecimal getAlevelPayload() {
        return alevelPayload;
    }

    /**
     * 设置A级核载吨位(吨)
     *
     * @param alevelPayload A级核载吨位(吨)
     */
    public void setAlevelPayload(BigDecimal alevelPayload) {
        this.alevelPayload = alevelPayload;
    }

    /**
     * 获取B级核载吨位(吨)
     *
     * @return blevel_payload - B级核载吨位(吨)
     */
    public BigDecimal getBlevelPayload() {
        return blevelPayload;
    }

    /**
     * 设置B级核载吨位(吨)
     *
     * @param blevelPayload B级核载吨位(吨)
     */
    public void setBlevelPayload(BigDecimal blevelPayload) {
        this.blevelPayload = blevelPayload;
    }

    /**
     * 获取净重(吨)
     *
     * @return self_payload - 净重(吨)
     */
    public BigDecimal getSelfPayload() {
        return selfPayload;
    }

    /**
     * 设置净重(吨)
     *
     * @param selfPayload 净重(吨)
     */
    public void setSelfPayload(BigDecimal selfPayload) {
        this.selfPayload = selfPayload;
    }

    /**
     * 获取总吨位(吨)
     *
     * @return total_payload - 总吨位(吨)
     */
    public BigDecimal getTotalPayload() {
        return totalPayload;
    }

    /**
     * 设置总吨位(吨)
     *
     * @param totalPayload 总吨位(吨)
     */
    public void setTotalPayload(BigDecimal totalPayload) {
        this.totalPayload = totalPayload;
    }

    /**
     * 获取总长度(米)
     *
     * @return total_length - 总长度(米)
     */
    public BigDecimal getTotalLength() {
        return totalLength;
    }

    /**
     * 设置总长度(米)
     *
     * @param totalLength 总长度(米)
     */
    public void setTotalLength(BigDecimal totalLength) {
        this.totalLength = totalLength;
    }

    /**
     * 获取最大宽度(米)
     *
     * @return max_width - 最大宽度(米)
     */
    public BigDecimal getMaxWidth() {
        return maxWidth;
    }

    /**
     * 设置最大宽度(米)
     *
     * @param maxWidth 最大宽度(米)
     */
    public void setMaxWidth(BigDecimal maxWidth) {
        this.maxWidth = maxWidth;
    }

    /**
     * 获取船舶的型深(米)
     *
     * @return moulded_depth - 船舶的型深(米)
     */
    public BigDecimal getMouldedDepth() {
        return mouldedDepth;
    }

    /**
     * 设置船舶的型深(米)
     *
     * @param mouldedDepth 船舶的型深(米)
     */
    public void setMouldedDepth(BigDecimal mouldedDepth) {
        this.mouldedDepth = mouldedDepth;
    }

    /**
     * 获取满载吃水(米)
     *
     * @return full_draft - 满载吃水(米)
     */
    public BigDecimal getFullDraft() {
        return fullDraft;
    }

    /**
     * 设置满载吃水(米)
     *
     * @param fullDraft 满载吃水(米)
     */
    public void setFullDraft(BigDecimal fullDraft) {
        this.fullDraft = fullDraft;
    }

    /**
     * 获取空载吃水(米)
     *
     * @return empty_draft - 空载吃水(米)
     */
    public BigDecimal getEmptyDraft() {
        return emptyDraft;
    }

    /**
     * 设置空载吃水(米)
     *
     * @param emptyDraft 空载吃水(米)
     */
    public void setEmptyDraft(BigDecimal emptyDraft) {
        this.emptyDraft = emptyDraft;
    }

    /**
     * 获取最大装载量(米)
     *
     * @return max_loading_capacity - 最大装载量(米)
     */
    public BigDecimal getMaxLoadingCapacity() {
        return maxLoadingCapacity;
    }

    /**
     * 设置最大装载量(米)
     *
     * @param maxLoadingCapacity 最大装载量(米)
     */
    public void setMaxLoadingCapacity(BigDecimal maxLoadingCapacity) {
        this.maxLoadingCapacity = maxLoadingCapacity;
    }

    /**
     * 获取最大船高(米)
     *
     * @return max_height - 最大船高(米)
     */
    public BigDecimal getMaxHeight() {
        return maxHeight;
    }

    /**
     * 设置最大船高(米)
     *
     * @param maxHeight 最大船高(米)
     */
    public void setMaxHeight(BigDecimal maxHeight) {
        this.maxHeight = maxHeight;
    }

    /**
     * 获取是否支持管控，0-不管控，1-管控
     *
     * @return whether_control - 是否支持管控，0-不管控，1-管控
     */
    public Integer getWhetherControl() {
        return whetherControl;
    }

    /**
     * 设置是否支持管控，0-不管控，1-管控
     *
     * @param whetherControl 是否支持管控，0-不管控，1-管控
     */
    public void setWhetherControl(Integer whetherControl) {
        this.whetherControl = whetherControl;
    }

    /**
     * 获取是否绑定GPS终端，0-无，1-有
     *
     * @return gps_device - 是否绑定GPS终端，0-无，1-有
     */
    public Integer getGpsDevice() {
        return gpsDevice;
    }

    /**
     * 设置是否绑定GPS终端，0-无，1-有
     *
     * @param gpsDevice 是否绑定GPS终端，0-无，1-有
     */
    public void setGpsDevice(Integer gpsDevice) {
        this.gpsDevice = gpsDevice;
    }

    /**
     * 获取GPS设备号
     *
     * @return gps_device_number - GPS设备号
     */
    public String getGpsDeviceNumber() {
        return gpsDeviceNumber;
    }

    /**
     * 设置GPS设备号
     *
     * @param gpsDeviceNumber GPS设备号
     */
    public void setGpsDeviceNumber(String gpsDeviceNumber) {
        this.gpsDeviceNumber = gpsDeviceNumber == null ? null : gpsDeviceNumber.trim();
    }


    /**
     * 获取船长ID，冗余字段，方便展示
     *
     * @return captain_account_id - 船长ID，冗余字段，方便展示
     */
    public String getCaptainAccountId() {
        return captainAccountId;
    }

    /**
     * 设置船长ID，冗余字段，方便展示
     *
     * @return captainAccountId - 船长ID，冗余字段，方便展示
     */
    public void setCaptainAccountId(String captainAccountId) {
        this.captainAccountId = captainAccountId;
    }

    /**
     * 获取船长姓名，冗余字段，方便展示
     *
     * @return captain_name - 船长姓名，冗余字段，方便展示
     */
    public String getCaptainName() {
        return captainName;
    }

    /**
     * 设置船长姓名，冗余字段，方便展示
     *
     * @param captainName 船长姓名，冗余字段，方便展示
     */
    public void setCaptainName(String captainName) {
        this.captainName = captainName == null ? null : captainName.trim();
    }

    /**
     * 获取船长手机号码，冗余字段，方便展示
     *
     * @return captain_phone - 船长手机号码，冗余字段，方便展示
     */
    public String getCaptainPhone() {
        return captainPhone;
    }

    /**
     * 设置船长手机号码，冗余字段，方便展示
     *
     * @param captainPhone 船长手机号码，冗余字段，方便展示
     */
    public void setCaptainPhone(String captainPhone) {
        this.captainPhone = captainPhone == null ? null : captainPhone.trim();
    }

    /**
     * 获取船舶状态,1-吉船,2-待装货,3-重船,4-检修中,5-已禁用
     *
     * @return shipping_status - 船舶状态,1-吉船,2-待装货,3-重船,4-检修中,5-已禁用
     */
    public Integer getShippingStatus() {
        return shippingStatus;
    }

    /**
     * 设置船舶状态,1-吉船,2-待装货,3-重船,4-检修中,5-已禁用
     *
     * @param shippingStatus 船舶状态,1-吉船,2-待装货,3-重船,4-检修中,5-已禁用
     */
    public void setShippingStatus(Integer shippingStatus) {
        this.shippingStatus = shippingStatus;
    }

    /**
     * 获取审核状态,1-待提交,2-待审核,3-已通过,4-已驳回,5-更新中
     *
     * @return audit_status - 审核状态,1-待提交,2-待审核,3-已通过,4-已驳回,5-更新中
     */
    public Integer getAuditStatus() {
        return auditStatus;
    }

    /**
     * 设置审核状态,1-待提交,2-待审核,3-已通过,4-已驳回,5-更新中
     *
     * @param auditStatus 审核状态,1-待提交,2-待审核,3-已通过,4-已驳回,5-更新中
     */
    public void setAuditStatus(Integer auditStatus) {
        this.auditStatus = auditStatus;
    }

    /**
     * 获取驳回原因
     *
     * @return rejection_reasons - 驳回原因
     */
    public String getRejectionReasons() {
        return rejectionReasons;
    }

    /**
     * 设置驳回原因
     *
     * @param rejectionReasons 驳回原因
     */
    public void setRejectionReasons(String rejectionReasons) {
        this.rejectionReasons = rejectionReasons == null ? null : rejectionReasons.trim();
    }

    /**
     * 数据版本
     *
     * @return del_flg - 数据版本
     */
    public Long getVersion() {
        return version;
    }

    /**
     * 设置数据版本
     *
     * @param version 数据版本
     */
    public void setVersion(Long version) {
        this.version = version;
    }

    /**
     * 获取删除标识，0-未删除，1-已删除
     *
     * @return del_flg - 删除标识，0-未删除，1-已删除
     */
    public Integer getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标识，0-未删除，1-已删除
     *
     * @param delFlg 删除标识，0-未删除，1-已删除
     */
    public void setDelFlg(Integer delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", shippingId=").append(shippingId);
        sb.append(", shippingNo=").append(shippingNo);
        sb.append(", shippingName=").append(shippingName);
        sb.append(", managerMemberType=").append(managerMemberType);
        sb.append(", managerMemberId=").append(managerMemberId);
        sb.append(", managerMemberName=").append(managerMemberName);
        sb.append(", shippingType=").append(shippingType);
        sb.append(", belongerName=").append(belongerName);
        sb.append(", belongerPhone=").append(belongerPhone);
        sb.append(", shippingCompany=").append(shippingCompany);
        sb.append(", ownership=").append(ownership);
        sb.append(", departureWharfId=").append(departureWharfId);
        sb.append(", destinationWharfId=").append(destinationWharfId);
        sb.append(", alevelPayload=").append(alevelPayload);
        sb.append(", blevelPayload=").append(blevelPayload);
        sb.append(", selfPayload=").append(selfPayload);
        sb.append(", totalPayload=").append(totalPayload);
        sb.append(", totalLength=").append(totalLength);
        sb.append(", maxWidth=").append(maxWidth);
        sb.append(", mouldedDepth=").append(mouldedDepth);
        sb.append(", fullDraft=").append(fullDraft);
        sb.append(", emptyDraft=").append(emptyDraft);
        sb.append(", maxLoadingCapacity=").append(maxLoadingCapacity);
        sb.append(", maxHeight=").append(maxHeight);
        sb.append(", whetherControl=").append(whetherControl);
        sb.append(", gpsDevice=").append(gpsDevice);
        sb.append(", gpsDeviceNumber=").append(gpsDeviceNumber);
        sb.append(", captainAccountId=").append(captainAccountId);
        sb.append(", captainName=").append(captainName);
        sb.append(", captainPhone=").append(captainPhone);
        sb.append(", shippingStatus=").append(shippingStatus);
        sb.append(", auditStatus=").append(auditStatus);
        sb.append(", rejectionReasons=").append(rejectionReasons);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}