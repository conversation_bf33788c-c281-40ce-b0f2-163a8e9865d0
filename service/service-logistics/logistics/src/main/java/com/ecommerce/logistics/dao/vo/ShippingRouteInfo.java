package com.ecommerce.logistics.dao.vo;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

@Table(name = "lgs_shipping_route_info")
public class ShippingRouteInfo implements Serializable {
    /**
     * 航线ID
     */
    @Id
    @Column(name = "shipping_route_id")
    private String shippingRouteId;

    /**
     * 航线编号
     */
    @Column(name = "shipping_route_no")
    private String shippingRouteNo;

    /**
     * 装货码头编号
     */
    @Column(name = "picking_wharf_code")
    private String pickingWharfCode;

    /**
     * 装货码头名称
     */
    @Column(name = "picking_wharf_name")
    private String pickingWharfName;

    /**
     * 收货码头编号
     */
    @Column(name = "unloading_wharf_code")
    private String unloadingWharfCode;

    /**
     * 收货码头名称
     */
    @Column(name = "unloading_wharf_name")
    private String unloadingWharfName;

    /**
     * 运输品类ID，多个以","号隔开
     */
    @Column(name = "transport_category_id")
    private String transportCategoryId;

    /**
     * 所属用户ID，memberID
     */
    @Column(name = "belonger_id")
    private String belongerId;

    /**
     * 所属用户类型
     */
    @Column(name = "user_type")
    private Integer userType;

    /**
     * 卖家ID，memberID
     */
    @Column(name = "seller_id")
    private String sellerId;

    /**
     * 买家ID,memberID
     */
    @Column(name = "buyer_id")
    private String buyerId;

    /**
     * 买家名称
     */
    @Column(name = "buyer_name")
    private String buyerName;

    /**
     * 删除标识：0-未删除，1-已删除
     */
    @Column(name = "del_flg")
    private Integer delFlg;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;

    /**
     * 获取航线ID
     *
     * @return shipping_route_id - 航线ID
     */
    public String getShippingRouteId() {
        return shippingRouteId;
    }

    /**
     * 设置航线ID
     *
     * @param shippingRouteId 航线ID
     */
    public void setShippingRouteId(String shippingRouteId) {
        this.shippingRouteId = shippingRouteId == null ? null : shippingRouteId.trim();
    }

    /**
     * 获取定价编号
     *
     * @return shipping_route_no - 定价编号
     */
    public String getShippingRouteNo() {
        return shippingRouteNo;
    }

    /**
     * 设置定价编号
     *
     * @param shippingRouteNo 定价编号
     */
    public void setShippingRouteNo(String shippingRouteNo) {
        this.shippingRouteNo = shippingRouteNo == null ? null : shippingRouteNo.trim();
    }

    /**
     * 获取装货码头编号
     *
     * @return picking_wharf_code - 装货码头编号
     */
    public String getPickingWharfCode() {
        return pickingWharfCode;
    }

    /**
     * 设置装货码头编号
     *
     * @param pickingWharfCode 装货码头编号
     */
    public void setPickingWharfCode(String pickingWharfCode) {
        this.pickingWharfCode = pickingWharfCode == null ? null : pickingWharfCode.trim();
    }

    /**
     * 获取装货码头名称
     *
     * @return picking_wharf_name - 装货码头名称
     */
    public String getPickingWharfName() {
        return pickingWharfName;
    }

    /**
     * 设置起运港Id
     *
     * @param pickingWharfName 装货码头名称
     */
    public void setPickingWharfName(String pickingWharfName) {
        this.pickingWharfName = pickingWharfName == null ? null : pickingWharfName.trim();
    }

    /**
     * 获取收货码头编号
     *
     * @return unloading_wharf_code - 收货码头编号
     */
    public String getUnloadingWharfCode() {
        return unloadingWharfCode;
    }

    /**
     * 设置收货码头编号
     *
     * @param unloadingWharfCode 收货码头编号
     */
    public void setUnloadingWharfCode(String unloadingWharfCode) {
        this.unloadingWharfCode = unloadingWharfCode == null ? null : unloadingWharfCode.trim();
    }

    /**
     * 获取收货码头名称
     *
     * @return unloading_wharf_name - 收货码头名称
     */
    public String getUnloadingWharfName() {
        return unloadingWharfName;
    }

    /**
     * 设置收货码头名称
     *
     * @param unloadingWharfName 收货码头名称
     */
    public void setUnloadingWharfName(String unloadingWharfName) {
        this.unloadingWharfName = unloadingWharfName == null ? null : unloadingWharfName.trim();
    }

    /**
     * 获取运输品类ID，多个以","号隔开
     *
     * @return transport_category_id - 运输品类ID，多个以","号隔开
     */
    public String getTransportCategoryId() {
        return transportCategoryId;
    }

    /**
     * 设置运输品类ID，多个以","号隔开
     *
     * @param transportCategoryId 运输品类ID，多个以","号隔开
     */
    public void setTransportCategoryId(String transportCategoryId) {
        this.transportCategoryId = transportCategoryId == null ? null : transportCategoryId.trim();
    }

    /**
     * 获取所属用户ID，memberID
     *
     * @return belonger_id - 所属用户ID，memberID
     */
    public String getBelongerId() {
        return belongerId;
    }

    /**
     * 设置所属用户ID，memberID
     *
     * @param belongerId 所属用户ID，memberID
     */
    public void setBelongerId(String belongerId) {
        this.belongerId = belongerId == null ? null : belongerId.trim();
    }

    /**
     * 获取所属用户类型
     *
     * @return user_type - 所属用户类型
     */
    public Integer getUserType() {
        return userType;
    }

    /**
     * 设置所属用户类型
     *
     * @param userType 所属用户类型
     */
    public void setUserType(Integer userType) {
        this.userType = userType;
    }

    /**
     * 获取卖家ID，memberID
     *
     * @return belonger_id - 卖家ID，memberID
     */
    public String getSellerId() {
        return sellerId;
    }

    /**
     * 设置卖家ID，memberID
     *
     * @param sellerId 卖家ID，memberID
     */
    public void setSellerId(String sellerId) {
        this.sellerId = sellerId == null ? null : sellerId.trim();
    }

    /**
     * 获取买家ID，memberID
     *
     * @return buyer_id - 买家ID，memberID
     */
    public String getBuyerId() {
        return buyerId;
    }

    /**
     * 设置买家ID，memberID
     *
     * @param buyerId 买家ID，memberID
     */
    public void setBuyerId(String buyerId) {
        this.buyerId = buyerId == null ? null : buyerId.trim();
    }

    /**
     * 获取买家名称
     *
     * @return buyer_name - 买家名称
     */
    public String getBuyerName() {
        return buyerName;
    }

    /**
     * 设置买家名称
     *
     * @param buyerName 买家名称
     */
    public void setBuyerName(String buyerName) {
        this.buyerName = buyerName == null ? null : buyerName.trim();
    }

    /**
     * 获取删除标识：0-未删除，1-已删除
     *
     * @return del_flg - 删除标识：0-未删除，1-已删除
     */
    public Integer getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标识：0-未删除，1-已删除
     *
     * @param delFlg 删除标识：0-未删除，1-已删除
     */
    public void setDelFlg(Integer delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", shippingRouteId=").append(shippingRouteId);
        sb.append(", shippingRouteNo=").append(shippingRouteNo);
        sb.append(", pickingWharfCode=").append(pickingWharfCode);
        sb.append(", pickingWharfName=").append(pickingWharfName);
        sb.append(", unloadingWharfCode=").append(unloadingWharfCode);
        sb.append(", unloadingWharfName=").append(unloadingWharfName);
        sb.append(", transportCategoryId=").append(transportCategoryId);
        sb.append(", belongerId=").append(belongerId);
        sb.append(", userType=").append(userType);
        sb.append(", sellerId=").append(sellerId);
        sb.append(", buyerId=").append(buyerId);
        sb.append(", buyerName=").append(buyerName);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}