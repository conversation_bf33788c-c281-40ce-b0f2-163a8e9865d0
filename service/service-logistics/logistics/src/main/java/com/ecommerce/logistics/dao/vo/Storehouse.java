package com.ecommerce.logistics.dao.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;

@Table(name = "lgs_storehouse")
public class Storehouse implements Serializable {
    /**
     * 主键
     */
    @Id
    @Column(name = "storehouse_id")
    private String storehouseId;

    /**
     * 库位编号
     */
    @Column(name = "storehouse_number")
    private String storehouseNumber;

    /**
     * 库位名
     */
    @Column(name = "storehouse_name")
    private String storehouseName;

    /**
     * 库位容量
     */
    @Column(name = "storehouse_capacity")
    private BigDecimal storehouseCapacity;

    /**
     * 可用库存
     */
    @Column(name = "ava_stock")
    private BigDecimal avaStock;

    /**
     * 当前库存
     */
    @Column(name = "used_stock")
    private BigDecimal usedStock;

    /**
     * 在途库存
     */
    @Column(name = "locked_stock")
    private BigDecimal lockedStock;

    /**
     * 仓库主键
     */
    @Column(name = "warehouse_id")
    private String warehouseId;

    /**
     * 仓库名
     */
    @Column(name = "warehouse_name")
    private String warehouseName;

    @Column(name = "warehouse_type")
    private String warehouseType;

    /**
     * 管理员
     */
    private String administrator;

    /**
     * 管理员电话
     */
    @Column(name = "administrator_phone")
    private String administratorPhone;

    /**
     * 库位状态
     */
    @Column(name = "storehouse_status")
    private String storehouseStatus;

    /**
     * 删除标识
     */
    @Column(name = "del_flg")
    private Byte delFlg;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 数据版本
     */
    private Long version;

    private static final long serialVersionUID = 1L;

    /**
     * 获取主键
     *
     * @return storehouse_id - 主键
     */
    public String getStorehouseId() {
        return storehouseId;
    }

    /**
     * 设置主键
     *
     * @param storehouseId 主键
     */
    public void setStorehouseId(String storehouseId) {
        this.storehouseId = storehouseId == null ? null : storehouseId.trim();
    }

    /**
     * 获取库位编号
     *
     * @return storehouse_number - 库位编号
     */
    public String getStorehouseNumber() {
        return storehouseNumber;
    }

    /**
     * 设置库位编号
     *
     * @param storehouseNumber 库位编号
     */
    public void setStorehouseNumber(String storehouseNumber) {
        this.storehouseNumber = storehouseNumber == null ? null : storehouseNumber.trim();
    }

    /**
     * 获取库位名
     *
     * @return storehouse_name - 库位名
     */
    public String getStorehouseName() {
        return storehouseName;
    }

    /**
     * 设置库位名
     *
     * @param storehouseName 库位名
     */
    public void setStorehouseName(String storehouseName) {
        this.storehouseName = storehouseName == null ? null : storehouseName.trim();
    }

    /**
     * 获取库位容量
     *
     * @return storehouse_capacity - 库位容量
     */
    public BigDecimal getStorehouseCapacity() {
        return storehouseCapacity;
    }

    /**
     * 设置库位容量
     *
     * @param storehouseCapacity 库位容量
     */
    public void setStorehouseCapacity(BigDecimal storehouseCapacity) {
        this.storehouseCapacity = storehouseCapacity;
    }

    /**
     * 获取可用库存
     *
     * @return ava_stock - 可用库存
     */
    public BigDecimal getAvaStock() {
        return avaStock;
    }

    /**
     * 设置可用库存
     *
     * @param avaStock 可用库存
     */
    public void setAvaStock(BigDecimal avaStock) {
        this.avaStock = avaStock;
    }

    /**
     * 获取当前库存
     *
     * @return used_stock - 当前库存
     */
    public BigDecimal getUsedStock() {
        return usedStock;
    }

    /**
     * 设置当前库存
     *
     * @param usedStock 当前库存
     */
    public void setUsedStock(BigDecimal usedStock) {
        this.usedStock = usedStock;
    }

    /**
     * 获取在途库存
     *
     * @return locked_stock - 在途库存
     */
    public BigDecimal getLockedStock() {
        return lockedStock;
    }

    /**
     * 设置在途库存
     *
     * @param lockedStock 在途库存
     */
    public void setLockedStock(BigDecimal lockedStock) {
        this.lockedStock = lockedStock;
    }

    /**
     * 获取仓库主键
     *
     * @return warehouse_id - 仓库主键
     */
    public String getWarehouseId() {
        return warehouseId;
    }

    /**
     * 设置仓库主键
     *
     * @param warehouseId 仓库主键
     */
    public void setWarehouseId(String warehouseId) {
        this.warehouseId = warehouseId == null ? null : warehouseId.trim();
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName == null ? null : warehouseName.trim();
    }

    public String getWarehouseType() {
        return warehouseType;
    }

    public void setWarehouseType(String warehouseType) {
        this.warehouseType = warehouseType == null ? null : warehouseType.trim();
    }

    /**
     * 获取管理员
     *
     * @return administrator - 管理员
     */
    public String getAdministrator() {
        return administrator;
    }

    /**
     * 设置管理员
     *
     * @param administrator 管理员
     */
    public void setAdministrator(String administrator) {
        this.administrator = administrator == null ? null : administrator.trim();
    }

    /**
     * 获取管理员电话
     *
     * @return administrator_phone - 管理员电话
     */
    public String getAdministratorPhone() {
        return administratorPhone;
    }

    /**
     * 设置管理员电话
     *
     * @param administratorPhone 管理员电话
     */
    public void setAdministratorPhone(String administratorPhone) {
        this.administratorPhone = administratorPhone == null ? null : administratorPhone.trim();
    }

    /**
     * 获取库位状态
     *
     * @return storehouse_status - 库位状态
     */
    public String getStorehouseStatus() {
        return storehouseStatus;
    }

    /**
     * 设置库位状态
     *
     * @param storehouseStatus 库位状态
     */
    public void setStorehouseStatus(String storehouseStatus) {
        this.storehouseStatus = storehouseStatus == null ? null : storehouseStatus.trim();
    }

    /**
     * 获取删除标识
     *
     * @return del_flg - 删除标识
     */
    public Byte getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标识
     *
     * @param delFlg 删除标识
     */
    public void setDelFlg(Byte delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取数据版本
     *
     * @return version - 数据版本
     */
    public Long getVersion() {
        return version;
    }

    /**
     * 设置数据版本
     *
     * @param version 数据版本
     */
    public void setVersion(Long version) {
        this.version = version;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", storehouseId=").append(storehouseId);
        sb.append(", storehouseNumber=").append(storehouseNumber);
        sb.append(", storehouseName=").append(storehouseName);
        sb.append(", storehouseCapacity=").append(storehouseCapacity);
        sb.append(", avaStock=").append(avaStock);
        sb.append(", usedStock=").append(usedStock);
        sb.append(", lockedStock=").append(lockedStock);
        sb.append(", warehouseId=").append(warehouseId);
        sb.append(", warehouseName=").append(warehouseName);
        sb.append(", warehouseType=").append(warehouseType);
        sb.append(", administrator=").append(administrator);
        sb.append(", administratorPhone=").append(administratorPhone);
        sb.append(", storehouseStatus=").append(storehouseStatus);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", version=").append(version);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}