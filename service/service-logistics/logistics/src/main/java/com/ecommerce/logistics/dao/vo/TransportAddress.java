package com.ecommerce.logistics.dao.vo;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

@Table(name = "lgs_transport_address")
public class TransportAddress implements Serializable {
    /**
     * 运输地址id
     */
    @Id
    @Column(name = "transport_address_id")
    private String transportAddressId;

    /**
     * 地址类型 1：提货点 2：卸货点
     */
    @Column(name = "address_type")
    private String addressType;

    /**
     * 地址名称
     */
    @Column(name = "address_name")
    private String addressName;

    /**
     * 归属用户id
     */
    @Column(name = "user_id")
    private String userId;

    /**
     * 归属用户名称
     */
    @Column(name = "user_name")
    private String userName;

    /**
     * 归属用户类型 1：卖家 2:：平台
     */
    @Column(name = "user_type")
    private Byte userType;

    /**
     * 省份
     */
    private String province;

    /**
     * 省份代码
     */
    @Column(name = "province_code")
    private String provinceCode;

    /**
     * 城市
     */
    private String city;

    /**
     * 城市代码
     */
    @Column(name = "city_code")
    private String cityCode;

    /**
     * 地区
     */
    private String district;

    /**
     * 地区代码
     */
    @Column(name = "district_code")
    private String districtCode;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 地址经纬度
     */
    private String location;

    /**
     * 删除标识
     */
    @Column(name = "del_flg")
    private Byte delFlg;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 数据版本
     */
    private Long version;

    private static final long serialVersionUID = 1L;

    /**
     * 获取运输地址id
     *
     * @return transport_address_id - 运输地址id
     */
    public String getTransportAddressId() {
        return transportAddressId;
    }

    /**
     * 设置运输地址id
     *
     * @param transportAddressId 运输地址id
     */
    public void setTransportAddressId(String transportAddressId) {
        this.transportAddressId = transportAddressId == null ? null : transportAddressId.trim();
    }

    /**
     * 获取地址类型 1：提货点 2：卸货点
     *
     * @return address_type - 地址类型 1：提货点 2：卸货点
     */
    public String getAddressType() {
        return addressType;
    }

    /**
     * 设置地址类型 1：提货点 2：卸货点
     *
     * @param addressType 地址类型 1：提货点 2：卸货点
     */
    public void setAddressType(String addressType) {
        this.addressType = addressType == null ? null : addressType.trim();
    }

    /**
     * 获取地址名称
     *
     * @return address_name - 地址名称
     */
    public String getAddressName() {
        return addressName;
    }

    /**
     * 设置地址名称
     *
     * @param addressName 地址名称
     */
    public void setAddressName(String addressName) {
        this.addressName = addressName == null ? null : addressName.trim();
    }

    /**
     * 获取归属用户id
     *
     * @return user_id - 归属用户id
     */
    public String getUserId() {
        return userId;
    }

    /**
     * 设置归属用户id
     *
     * @param userId 归属用户id
     */
    public void setUserId(String userId) {
        this.userId = userId == null ? null : userId.trim();
    }

    /**
     * 获取归属用户名称
     *
     * @return user_name - 归属用户名称
     */
    public String getUserName() {
        return userName;
    }

    /**
     * 设置归属用户名称
     *
     * @param userName 归属用户名称
     */
    public void setUserName(String userName) {
        this.userName = userName == null ? null : userName.trim();
    }

    /**
     * 获取归属用户类型 1：卖家 2:：平台
     *
     * @return user_type - 归属用户类型 1：卖家 2:：平台
     */
    public Byte getUserType() {
        return userType;
    }

    /**
     * 设置归属用户类型 1：卖家 2:：平台
     *
     * @param userType 归属用户类型 1：卖家 2:：平台
     */
    public void setUserType(Byte userType) {
        this.userType = userType;
    }

    /**
     * 获取省份
     *
     * @return province - 省份
     */
    public String getProvince() {
        return province;
    }

    /**
     * 设置省份
     *
     * @param province 省份
     */
    public void setProvince(String province) {
        this.province = province == null ? null : province.trim();
    }

    /**
     * 获取省份代码
     *
     * @return province_code - 省份代码
     */
    public String getProvinceCode() {
        return provinceCode;
    }

    /**
     * 设置省份代码
     *
     * @param provinceCode 省份代码
     */
    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode == null ? null : provinceCode.trim();
    }

    /**
     * 获取城市
     *
     * @return city - 城市
     */
    public String getCity() {
        return city;
    }

    /**
     * 设置城市
     *
     * @param city 城市
     */
    public void setCity(String city) {
        this.city = city == null ? null : city.trim();
    }

    /**
     * 获取城市代码
     *
     * @return city_code - 城市代码
     */
    public String getCityCode() {
        return cityCode;
    }

    /**
     * 设置城市代码
     *
     * @param cityCode 城市代码
     */
    public void setCityCode(String cityCode) {
        this.cityCode = cityCode == null ? null : cityCode.trim();
    }

    /**
     * 获取地区
     *
     * @return district - 地区
     */
    public String getDistrict() {
        return district;
    }

    /**
     * 设置地区
     *
     * @param district 地区
     */
    public void setDistrict(String district) {
        this.district = district == null ? null : district.trim();
    }

    /**
     * 获取地区代码
     *
     * @return district_code - 地区代码
     */
    public String getDistrictCode() {
        return districtCode;
    }

    /**
     * 设置地区代码
     *
     * @param districtCode 地区代码
     */
    public void setDistrictCode(String districtCode) {
        this.districtCode = districtCode == null ? null : districtCode.trim();
    }

    /**
     * 获取详细地址
     *
     * @return address - 详细地址
     */
    public String getAddress() {
        return address;
    }

    /**
     * 设置详细地址
     *
     * @param address 详细地址
     */
    public void setAddress(String address) {
        this.address = address == null ? null : address.trim();
    }

    /**
     * 获取地址经纬度
     *
     * @return location - 地址经纬度
     */
    public String getLocation() {
        return location;
    }

    /**
     * 设置地址经纬度
     *
     * @param location 地址经纬度
     */
    public void setLocation(String location) {
        this.location = location == null ? null : location.trim();
    }

    /**
     * 获取删除标识
     *
     * @return del_flg - 删除标识
     */
    public Byte getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标识
     *
     * @param delFlg 删除标识
     */
    public void setDelFlg(Byte delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取数据版本
     *
     * @return version - 数据版本
     */
    public Long getVersion() {
        return version;
    }

    /**
     * 设置数据版本
     *
     * @param version 数据版本
     */
    public void setVersion(Long version) {
        this.version = version;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", transportAddressId=").append(transportAddressId);
        sb.append(", addressType=").append(addressType);
        sb.append(", addressName=").append(addressName);
        sb.append(", userId=").append(userId);
        sb.append(", userName=").append(userName);
        sb.append(", userType=").append(userType);
        sb.append(", province=").append(province);
        sb.append(", provinceCode=").append(provinceCode);
        sb.append(", city=").append(city);
        sb.append(", cityCode=").append(cityCode);
        sb.append(", district=").append(district);
        sb.append(", districtCode=").append(districtCode);
        sb.append(", address=").append(address);
        sb.append(", location=").append(location);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", version=").append(version);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}