package com.ecommerce.logistics.dao.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;

@Table(name = "lgs_transport_capacity")
public class TransportCapacity implements Serializable {
    /**
     * 运力id
     */
    @Id
    @Column(name = "transport_capacity_id")
    private String transportCapacityId;

    /**
     * 发布人id
     */
    @Column(name = "publisher_id")
    private String publisherId;

    /**
     * 发布人名称
     */
    @Column(name = "publisher_name")
    private String publisherName;

    /**
     * 发布人类型
     */
    @Column(name = "publisher_type")
    private Byte publisherType;

    /**
     * 司机id
     */
    @Column(name = "driver_id")
    private String driverId;

    /**
     * 司机名称
     */
    @Column(name = "driver_name")
    private String driverName;

    /**
     * 运输工具id
     */
    @Column(name = "transport_tool_id")
    private String transportToolId;

    /**
     * 装载容量
     */
    @Column(name = "load_capacity")
    private BigDecimal loadCapacity;

    /**
     * 运输工具类型
     */
    @Column(name = "transport_tool_type")
    private String transportToolType;

    /**
     * 运力提供起始时间
     */
    @Column(name = "delivery_time_start")
    private Date deliveryTimeStart;

    /**
     * 运力提供结束时间
     */
    @Column(name = "delivery_time_end")
    private Date deliveryTimeEnd;

    /**
     * 发布状态
     */
    private String status;

    /**
     * 审核失败原因
     */
    @Column(name = "fail_reason")
    private String failReason;

    /**
     * 删除标识
     */
    @Column(name = "del_flg")
    private Byte delFlg;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 数据版本
     */
    private Long version;

    private static final long serialVersionUID = 1L;

    /**
     * 获取运力id
     *
     * @return transport_capacity_id - 运力id
     */
    public String getTransportCapacityId() {
        return transportCapacityId;
    }

    /**
     * 设置运力id
     *
     * @param transportCapacityId 运力id
     */
    public void setTransportCapacityId(String transportCapacityId) {
        this.transportCapacityId = transportCapacityId == null ? null : transportCapacityId.trim();
    }

    /**
     * 获取发布人id
     *
     * @return publisher_id - 发布人id
     */
    public String getPublisherId() {
        return publisherId;
    }

    /**
     * 设置发布人id
     *
     * @param publisherId 发布人id
     */
    public void setPublisherId(String publisherId) {
        this.publisherId = publisherId == null ? null : publisherId.trim();
    }

    /**
     * 获取发布人名称
     *
     * @return publisher_name - 发布人名称
     */
    public String getPublisherName() {
        return publisherName;
    }

    /**
     * 设置发布人名称
     *
     * @param publisherName 发布人名称
     */
    public void setPublisherName(String publisherName) {
        this.publisherName = publisherName == null ? null : publisherName.trim();
    }

    /**
     * 获取发布人类型
     *
     * @return publisher_type - 发布人类型
     */
    public Byte getPublisherType() {
        return publisherType;
    }

    /**
     * 设置发布人类型
     *
     * @param publisherType 发布人类型
     */
    public void setPublisherType(Byte publisherType) {
        this.publisherType = publisherType;
    }

    /**
     * 获取司机id
     *
     * @return driver_id - 司机id
     */
    public String getDriverId() {
        return driverId;
    }

    /**
     * 设置司机id
     *
     * @param driverId 司机id
     */
    public void setDriverId(String driverId) {
        this.driverId = driverId == null ? null : driverId.trim();
    }

    /**
     * 获取司机名称
     *
     * @return driver_name - 司机名称
     */
    public String getDriverName() {
        return driverName;
    }

    /**
     * 设置司机名称
     *
     * @param driverName 司机名称
     */
    public void setDriverName(String driverName) {
        this.driverName = driverName == null ? null : driverName.trim();
    }

    /**
     * 获取运输工具id
     *
     * @return transport_tool_id - 运输工具id
     */
    public String getTransportToolId() {
        return transportToolId;
    }

    /**
     * 设置运输工具id
     *
     * @param transportToolId 运输工具id
     */
    public void setTransportToolId(String transportToolId) {
        this.transportToolId = transportToolId == null ? null : transportToolId.trim();
    }

    /**
     * 获取装载容量
     *
     * @return load_capacity - 装载容量
     */
    public BigDecimal getLoadCapacity() {
        return loadCapacity;
    }

    /**
     * 设置装载容量
     *
     * @param loadCapacity 装载容量
     */
    public void setLoadCapacity(BigDecimal loadCapacity) {
        this.loadCapacity = loadCapacity;
    }

    /**
     * 获取运输工具类型
     *
     * @return transport_tool_type - 运输工具类型
     */
    public String getTransportToolType() {
        return transportToolType;
    }

    /**
     * 设置运输工具类型
     *
     * @param transportToolType 运输工具类型
     */
    public void setTransportToolType(String transportToolType) {
        this.transportToolType = transportToolType == null ? null : transportToolType.trim();
    }

    /**
     * 获取运力提供起始时间
     *
     * @return delivery_time_start - 运力提供起始时间
     */
    public Date getDeliveryTimeStart() {
        return deliveryTimeStart;
    }

    /**
     * 设置运力提供起始时间
     *
     * @param deliveryTimeStart 运力提供起始时间
     */
    public void setDeliveryTimeStart(Date deliveryTimeStart) {
        this.deliveryTimeStart = deliveryTimeStart;
    }

    /**
     * 获取运力提供结束时间
     *
     * @return delivery_time_end - 运力提供结束时间
     */
    public Date getDeliveryTimeEnd() {
        return deliveryTimeEnd;
    }

    /**
     * 设置运力提供结束时间
     *
     * @param deliveryTimeEnd 运力提供结束时间
     */
    public void setDeliveryTimeEnd(Date deliveryTimeEnd) {
        this.deliveryTimeEnd = deliveryTimeEnd;
    }

    /**
     * 获取发布状态
     *
     * @return status - 发布状态
     */
    public String getStatus() {
        return status;
    }

    /**
     * 设置发布状态
     *
     * @param status 发布状态
     */
    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    /**
     * 获取审核失败原因
     *
     * @return fail_reason - 审核失败原因
     */
    public String getFailReason() {
        return failReason;
    }

    /**
     * 设置审核失败原因
     *
     * @param failReason 审核失败原因
     */
    public void setFailReason(String failReason) {
        this.failReason = failReason == null ? null : failReason.trim();
    }

    /**
     * 获取删除标识
     *
     * @return del_flg - 删除标识
     */
    public Byte getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标识
     *
     * @param delFlg 删除标识
     */
    public void setDelFlg(Byte delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取数据版本
     *
     * @return version - 数据版本
     */
    public Long getVersion() {
        return version;
    }

    /**
     * 设置数据版本
     *
     * @param version 数据版本
     */
    public void setVersion(Long version) {
        this.version = version;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", transportCapacityId=").append(transportCapacityId);
        sb.append(", publisherId=").append(publisherId);
        sb.append(", publisherName=").append(publisherName);
        sb.append(", publisherType=").append(publisherType);
        sb.append(", driverId=").append(driverId);
        sb.append(", driverName=").append(driverName);
        sb.append(", transportToolId=").append(transportToolId);
        sb.append(", loadCapacity=").append(loadCapacity);
        sb.append(", transportToolType=").append(transportToolType);
        sb.append(", deliveryTimeStart=").append(deliveryTimeStart);
        sb.append(", deliveryTimeEnd=").append(deliveryTimeEnd);
        sb.append(", status=").append(status);
        sb.append(", failReason=").append(failReason);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", version=").append(version);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}