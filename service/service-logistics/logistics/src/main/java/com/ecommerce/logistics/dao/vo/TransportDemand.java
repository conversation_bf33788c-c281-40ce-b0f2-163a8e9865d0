package com.ecommerce.logistics.dao.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;

@Table(name = "lgs_transport_demand")
public class TransportDemand implements Serializable {
    /**
     * 运输需求id
     */
    @Id
    @Column(name = "transport_demand_id")
    private String transportDemandId;

    /**
     * 归属用户id
     */
    @Column(name = "user_id")
    private String userId;

    /**
     * 归属用户名称
     */
    @Column(name = "user_name")
    private String userName;

    /**
     * 归属用户类型
     */
    @Column(name = "user_type")
    private Byte userType;

    /**
     * 货物描述
     */
    @Column(name = "freight_desc")
    private String freightDesc;

    /**
     * 货物数量
     */
    @Column(name = "freight_quantity")
    private BigDecimal freightQuantity;

    /**
     * 货物单位
     */
    @Column(name = "freight_unit")
    private String freightUnit;

    /**
     * 运输品类id
     */
    @Column(name = "transport_category_id")
    private String transportCategoryId;

    /**
     * 运输工具类型
     */
    @Column(name = "transport_tool_type")
    private String transportToolType;

    @Column(name = "delivery_time")
    private Date deliveryTime;

    /**
     * 配送时间范围
     */
    @Column(name = "delivery_time_range")
    private String deliveryTimeRange;

    /**
     * 提货地址id
     */
    @Column(name = "picking_address_id")
    private String pickingAddressId;

    /**
     * 卸货地址id
     */
    @Column(name = "unload_address_id")
    private String unloadAddressId;

    /**
     * 需求状态
     */
    private String status;

    /**
     * 审核失败原因
     */
    @Column(name = "fail_reason")
    private String failReason;

    /**
     * 删除标识
     */
    @Column(name = "del_flg")
    private Byte delFlg;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 数据版本
     */
    private Long version;

    private static final long serialVersionUID = 1L;

    /**
     * 获取运输需求id
     *
     * @return transport_demand_id - 运输需求id
     */
    public String getTransportDemandId() {
        return transportDemandId;
    }

    /**
     * 设置运输需求id
     *
     * @param transportDemandId 运输需求id
     */
    public void setTransportDemandId(String transportDemandId) {
        this.transportDemandId = transportDemandId == null ? null : transportDemandId.trim();
    }

    /**
     * 获取归属用户id
     *
     * @return user_id - 归属用户id
     */
    public String getUserId() {
        return userId;
    }

    /**
     * 设置归属用户id
     *
     * @param userId 归属用户id
     */
    public void setUserId(String userId) {
        this.userId = userId == null ? null : userId.trim();
    }

    /**
     * 获取归属用户名称
     *
     * @return user_name - 归属用户名称
     */
    public String getUserName() {
        return userName;
    }

    /**
     * 设置归属用户名称
     *
     * @param userName 归属用户名称
     */
    public void setUserName(String userName) {
        this.userName = userName == null ? null : userName.trim();
    }

    /**
     * 获取归属用户类型
     *
     * @return user_type - 归属用户类型
     */
    public Byte getUserType() {
        return userType;
    }

    /**
     * 设置归属用户类型
     *
     * @param userType 归属用户类型
     */
    public void setUserType(Byte userType) {
        this.userType = userType;
    }

    /**
     * 获取货物描述
     *
     * @return freight_desc - 货物描述
     */
    public String getFreightDesc() {
        return freightDesc;
    }

    /**
     * 设置货物描述
     *
     * @param freightDesc 货物描述
     */
    public void setFreightDesc(String freightDesc) {
        this.freightDesc = freightDesc == null ? null : freightDesc.trim();
    }

    /**
     * 获取货物数量
     *
     * @return freight_quantity - 货物数量
     */
    public BigDecimal getFreightQuantity() {
        return freightQuantity;
    }

    /**
     * 设置货物数量
     *
     * @param freightQuantity 货物数量
     */
    public void setFreightQuantity(BigDecimal freightQuantity) {
        this.freightQuantity = freightQuantity;
    }

    /**
     * 获取货物单位
     *
     * @return freight_unit - 货物单位
     */
    public String getFreightUnit() {
        return freightUnit;
    }

    /**
     * 设置货物单位
     *
     * @param freightUnit 货物单位
     */
    public void setFreightUnit(String freightUnit) {
        this.freightUnit = freightUnit == null ? null : freightUnit.trim();
    }

    /**
     * 获取运输品类id
     *
     * @return transport_category_id - 运输品类id
     */
    public String getTransportCategoryId() {
        return transportCategoryId;
    }

    /**
     * 设置运输品类id
     *
     * @param transportCategoryId 运输品类id
     */
    public void setTransportCategoryId(String transportCategoryId) {
        this.transportCategoryId = transportCategoryId == null ? null : transportCategoryId.trim();
    }

    /**
     * 获取运输工具类型
     *
     * @return transport_tool_type - 运输工具类型
     */
    public String getTransportToolType() {
        return transportToolType;
    }

    /**
     * 设置运输工具类型
     *
     * @param transportToolType 运输工具类型
     */
    public void setTransportToolType(String transportToolType) {
        this.transportToolType = transportToolType == null ? null : transportToolType.trim();
    }

    /**
     * @return delivery_time
     */
    public Date getDeliveryTime() {
        return deliveryTime;
    }

    /**
     * @param deliveryTime
     */
    public void setDeliveryTime(Date deliveryTime) {
        this.deliveryTime = deliveryTime;
    }

    /**
     * 获取配送时间范围
     *
     * @return delivery_time_range - 配送时间范围
     */
    public String getDeliveryTimeRange() {
        return deliveryTimeRange;
    }

    /**
     * 设置配送时间范围
     *
     * @param deliveryTimeRange 配送时间范围
     */
    public void setDeliveryTimeRange(String deliveryTimeRange) {
        this.deliveryTimeRange = deliveryTimeRange == null ? null : deliveryTimeRange.trim();
    }

    /**
     * 获取提货地址id
     *
     * @return picking_address_id - 提货地址id
     */
    public String getPickingAddressId() {
        return pickingAddressId;
    }

    /**
     * 设置提货地址id
     *
     * @param pickingAddressId 提货地址id
     */
    public void setPickingAddressId(String pickingAddressId) {
        this.pickingAddressId = pickingAddressId == null ? null : pickingAddressId.trim();
    }

    /**
     * 获取卸货地址id
     *
     * @return unload_address_id - 卸货地址id
     */
    public String getUnloadAddressId() {
        return unloadAddressId;
    }

    /**
     * 设置卸货地址id
     *
     * @param unloadAddressId 卸货地址id
     */
    public void setUnloadAddressId(String unloadAddressId) {
        this.unloadAddressId = unloadAddressId == null ? null : unloadAddressId.trim();
    }

    /**
     * 获取需求状态
     *
     * @return status - 需求状态
     */
    public String getStatus() {
        return status;
    }

    /**
     * 设置需求状态
     *
     * @param status 需求状态
     */
    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    /**
     * 获取审核失败原因
     *
     * @return fail_reason - 审核失败原因
     */
    public String getFailReason() {
        return failReason;
    }

    /**
     * 设置审核失败原因
     *
     * @param failReason 审核失败原因
     */
    public void setFailReason(String failReason) {
        this.failReason = failReason == null ? null : failReason.trim();
    }

    /**
     * 获取删除标识
     *
     * @return del_flg - 删除标识
     */
    public Byte getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标识
     *
     * @param delFlg 删除标识
     */
    public void setDelFlg(Byte delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取数据版本
     *
     * @return version - 数据版本
     */
    public Long getVersion() {
        return version;
    }

    /**
     * 设置数据版本
     *
     * @param version 数据版本
     */
    public void setVersion(Long version) {
        this.version = version;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", transportDemandId=").append(transportDemandId);
        sb.append(", userId=").append(userId);
        sb.append(", userName=").append(userName);
        sb.append(", userType=").append(userType);
        sb.append(", freightDesc=").append(freightDesc);
        sb.append(", freightQuantity=").append(freightQuantity);
        sb.append(", freightUnit=").append(freightUnit);
        sb.append(", transportCategoryId=").append(transportCategoryId);
        sb.append(", transportToolType=").append(transportToolType);
        sb.append(", deliveryTime=").append(deliveryTime);
        sb.append(", deliveryTimeRange=").append(deliveryTimeRange);
        sb.append(", pickingAddressId=").append(pickingAddressId);
        sb.append(", unloadAddressId=").append(unloadAddressId);
        sb.append(", status=").append(status);
        sb.append(", failReason=").append(failReason);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", version=").append(version);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}