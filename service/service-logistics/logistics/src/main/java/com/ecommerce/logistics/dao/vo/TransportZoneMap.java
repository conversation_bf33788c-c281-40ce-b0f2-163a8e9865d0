package com.ecommerce.logistics.dao.vo;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

@Table(name = "lgs_transport_zone_map")
public class TransportZoneMap implements Serializable {
    /**
     * 运力区域映射id
     */
    @Id
    @Column(name = "transport_zone_map_id")
    private String transportZoneMapId;

    /**
     * 运力发布id
     */
    @Column(name = "transport_capacity_id")
    private String transportCapacityId;

    /**
     * 配送区域省份
     */
    private String province;

    /**
     * 配送区域省份编码
     */
    @Column(name = "province_code")
    private String provinceCode;

    /**
     * 配送区域城市
     */
    private String city;

    /**
     * 配送区域城市编码
     */
    @Column(name = "city_code")
    private String cityCode;

    /**
     * 配送区域地区
     */
    private String district;

    /**
     * 配送区域地区编码
     */
    @Column(name = "district_code")
    private String districtCode;

    /**
     * 配送区域街道
     */
    private String street;

    /**
     * 配送区域街道编码
     */
    @Column(name = "street_code")
    private String streetCode;

    /**
     * 删除标识
     */
    @Column(name = "del_flg")
    private Byte delFlg;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 数据版本
     */
    private Long version;

    private static final long serialVersionUID = 1L;

    /**
     * 获取运力区域映射id
     *
     * @return transport_zone_map_id - 运力区域映射id
     */
    public String getTransportZoneMapId() {
        return transportZoneMapId;
    }

    /**
     * 设置运力区域映射id
     *
     * @param transportZoneMapId 运力区域映射id
     */
    public void setTransportZoneMapId(String transportZoneMapId) {
        this.transportZoneMapId = transportZoneMapId == null ? null : transportZoneMapId.trim();
    }

    /**
     * 获取运力发布id
     *
     * @return transport_capacity_id - 运力发布id
     */
    public String getTransportCapacityId() {
        return transportCapacityId;
    }

    /**
     * 设置运力发布id
     *
     * @param transportCapacityId 运力发布id
     */
    public void setTransportCapacityId(String transportCapacityId) {
        this.transportCapacityId = transportCapacityId == null ? null : transportCapacityId.trim();
    }

    /**
     * 获取配送区域省份
     *
     * @return province - 配送区域省份
     */
    public String getProvince() {
        return province;
    }

    /**
     * 设置配送区域省份
     *
     * @param province 配送区域省份
     */
    public void setProvince(String province) {
        this.province = province == null ? null : province.trim();
    }

    /**
     * 获取配送区域省份编码
     *
     * @return province_code - 配送区域省份编码
     */
    public String getProvinceCode() {
        return provinceCode;
    }

    /**
     * 设置配送区域省份编码
     *
     * @param provinceCode 配送区域省份编码
     */
    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode == null ? null : provinceCode.trim();
    }

    /**
     * 获取配送区域城市
     *
     * @return city - 配送区域城市
     */
    public String getCity() {
        return city;
    }

    /**
     * 设置配送区域城市
     *
     * @param city 配送区域城市
     */
    public void setCity(String city) {
        this.city = city == null ? null : city.trim();
    }

    /**
     * 获取配送区域城市编码
     *
     * @return city_code - 配送区域城市编码
     */
    public String getCityCode() {
        return cityCode;
    }

    /**
     * 设置配送区域城市编码
     *
     * @param cityCode 配送区域城市编码
     */
    public void setCityCode(String cityCode) {
        this.cityCode = cityCode == null ? null : cityCode.trim();
    }

    /**
     * 获取配送区域地区
     *
     * @return district - 配送区域地区
     */
    public String getDistrict() {
        return district;
    }

    /**
     * 设置配送区域地区
     *
     * @param district 配送区域地区
     */
    public void setDistrict(String district) {
        this.district = district == null ? null : district.trim();
    }

    /**
     * 获取配送区域地区编码
     *
     * @return district_code - 配送区域地区编码
     */
    public String getDistrictCode() {
        return districtCode;
    }

    /**
     * 设置配送区域地区编码
     *
     * @param districtCode 配送区域地区编码
     */
    public void setDistrictCode(String districtCode) {
        this.districtCode = districtCode == null ? null : districtCode.trim();
    }

    /**
     * 获取配送区域街道
     *
     * @return street - 配送区域街道
     */
    public String getStreet() {
        return street;
    }

    /**
     * 设置配送区域街道
     *
     * @param street 配送区域街道
     */
    public void setStreet(String street) {
        this.street = street == null ? null : street.trim();
    }

    /**
     * 获取配送区域街道编码
     *
     * @return street_code - 配送区域街道编码
     */
    public String getStreetCode() {
        return streetCode;
    }

    /**
     * 设置配送区域街道编码
     *
     * @param streetCode 配送区域街道编码
     */
    public void setStreetCode(String streetCode) {
        this.streetCode = streetCode == null ? null : streetCode.trim();
    }

    /**
     * 获取删除标识
     *
     * @return del_flg - 删除标识
     */
    public Byte getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标识
     *
     * @param delFlg 删除标识
     */
    public void setDelFlg(Byte delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取数据版本
     *
     * @return version - 数据版本
     */
    public Long getVersion() {
        return version;
    }

    /**
     * 设置数据版本
     *
     * @param version 数据版本
     */
    public void setVersion(Long version) {
        this.version = version;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", transportZoneMapId=").append(transportZoneMapId);
        sb.append(", transportCapacityId=").append(transportCapacityId);
        sb.append(", province=").append(province);
        sb.append(", provinceCode=").append(provinceCode);
        sb.append(", city=").append(city);
        sb.append(", cityCode=").append(cityCode);
        sb.append(", district=").append(district);
        sb.append(", districtCode=").append(districtCode);
        sb.append(", street=").append(street);
        sb.append(", streetCode=").append(streetCode);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", version=").append(version);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}