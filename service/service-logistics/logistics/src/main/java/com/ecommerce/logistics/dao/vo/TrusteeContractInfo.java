package com.ecommerce.logistics.dao.vo;


import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;

/**
 * 托管合同信息表
 */
@Table(name = "lgs_trustee_contract_info")
public class TrusteeContractInfo implements Serializable {
    /**
     * 合同ID,系统主键
     */
    @Id
    @Column(name = "contract_id")
    private String contractId;

    /**
     * 合同编号
     */
    @Column(name = "contract_number")
    private String contractNumber;

    /**
     * 托运方memberId
     */
    @Column(name = "trustee_id")
    private String trusteeId;

    /**
     * 托运方用户类型：0-其他，1-卖家，2-买家
     */
    @Column(name = "trustee_type")
    private Integer trusteeType;

    /**
     * 托运方名称
     */
    @Column(name = "trustee_name")
    private String trusteeName;

    /**
     * 合同类型：0-其他，1-主合同，2-补充合同
     */
    @Column(name = "contract_type")
    private String contractType;

    /**
     * 运输品类ID
     */
    @Column(name = "transport_category_id")
    private String transportCategoryId;

    /**
     * 运输商品ID
     */
    @Column(name = "goods_id")
    private String goodsId;

    /**
     * 商品名称
     */
    @Column(name = "goods_name")
    private String goodsName;

    /**
     * 运输数量
     */
    @Column(name = "quantity")
    private BigDecimal quantity;

    /**
     * 是否定量:0-不定量,1定量
     */
    @Column(name = "quantity_fixed_flag")
    private Integer quantityFixedFlag;

    /**
     * 运输单价
     */
    @Column(name = "transport_price")
    private BigDecimal transportPrice;

    /**
     * 是否固定价:0-浮动价,1固定价
     */
    @Column(name = "price_fixed_flag")
    private Integer priceFixedFlag;

    /**
     * 签约日期
     */
    @Column(name = "sign_date")
    private Date signDate;

    /**
     * 有效日期开始
     */
    @Column(name = "start_date")
    private Date startDate;

    /**
     * 有效日期截止
     */
    @Column(name = "end_date")
    private Date endDate;

    /**
     * 发票类型：0-其他，1-增值税专用发票，2-增值税普通发票
     */
    @Column(name = "invoice_type")
    private String invoiceType;

    /**
     * 税率
     */
    @Column(name = "invoice_rate")
    private BigDecimal invoiceRate;

    /**
     * 结算周期
     */
    @Column(name = "settlement_period_type")
    private String settlementPeriodType;

    /**
     * 联系人名
     */
    @Column(name = "contact_name")
    private String contactName;

    /**
     * 联系人电话
     */
    @Column(name = "contact_phone")
    private String contactPhone;

    /**
     * 删除标识：0-否,1-删除
     */
    @Column(name = "del_flg")
    private Integer delFlg;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    @Column(name = "version")
    private Integer version;
    /**
     * 备注
     */
    @Column(name = "note")
    private String note;

    private static final long serialVersionUID = 1L;

    /**
     * 获取合同ID,系统主键
     *
     * @return contract_id - 合同ID,系统主键
     */
    public String getContractId() {
        return contractId;
    }

    /**
     * 设置合同ID,系统主键
     *
     * @param contractId 合同ID,系统主键
     */
    public void setContractId(String contractId) {
        this.contractId = contractId == null ? null : contractId.trim();
    }

    /**
     * 获取合同编号
     *
     * @return contract_number - 合同编号
     */
    public String getContractNumber() {
        return contractNumber;
    }

    /**
     * 设置合同编号
     *
     * @param contractNumber 合同编号
     */
    public void setContractNumber(String contractNumber) {
        this.contractNumber = contractNumber == null ? null : contractNumber.trim();
    }

    /**
     * 获取托运方memberId
     *
     * @return trustee_id - 托运方memberId
     */
    public String getTrusteeId() {
        return trusteeId;
    }

    /**
     * 设置托运方memberId
     *
     * @param trusteeId 托运方memberId
     */
    public void setTrusteeId(String trusteeId) {
        this.trusteeId = trusteeId == null ? null : trusteeId.trim();
    }

    /**
     * 获取托运方用户类型：0-其他，1-卖家，2-买家
     *
     * @return trustee_type - 托运方用户类型：0-其他，1-卖家，2-买家
     */
    public Integer getTrusteeType() {
        return trusteeType;
    }

    /**
     * 设置托运方用户类型：0-其他，1-卖家，2-买家
     *
     * @param trusteeType 托运方用户类型：0-其他，1-卖家，2-买家
     */
    public void setTrusteeType(Integer trusteeType) {
        this.trusteeType = trusteeType;
    }

    /**
     * 获取托运方名称
     *
     * @return trustee_name - 托运方名称
     */
    public String getTrusteeName() {
        return trusteeName;
    }

    /**
     * 设置托运方名称
     *
     * @param trusteeName 托运方名称
     */
    public void setTrusteeName(String trusteeName) {
        this.trusteeName = trusteeName == null ? null : trusteeName.trim();
    }

    /**
     * 获取合同类型：0-其他，1-主合同，2-补充合同
     *
     * @return contract_type - 合同类型：0-其他，1-主合同，2-补充合同
     */
    public String getContractType() {
        return contractType;
    }

    /**
     * 设置合同类型：0-其他，1-主合同，2-补充合同
     *
     * @param contractType 合同类型：0-其他，1-主合同，2-补充合同
     */
    public void setContractType(String contractType) {
        this.contractType = contractType == null ? null : contractType.trim();
    }

    /**
     * 获取运输品类ID
     *
     * @return transport_category_id - 运输品类ID
     */
    public String getTransportCategoryId() {
        return transportCategoryId;
    }

    /**
     * 设置运输品类ID
     *
     * @param transportCategoryId 运输品类ID
     */
    public void setTransportCategoryId(String transportCategoryId) {
        this.transportCategoryId = transportCategoryId == null ? null : transportCategoryId.trim();
    }

    /**
     * 获取运输商品ID
     *
     * @return goods_id - 运输商品ID
     */
    public String getGoodsId() {
        return goodsId;
    }

    /**
     * 设置运输商品ID
     *
     * @param goodsId 运输商品ID
     */
    public void setGoodsId(String goodsId) {
        this.goodsId = goodsId == null ? null : goodsId.trim();
    }

    /**
     * 获取商品名称
     *
     * @return goods_name - 商品名称
     */
    public String getGoodsName() {
        return goodsName;
    }

    /**
     * 设置商品名称
     *
     * @param goodsName 商品名称
     */
    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName == null ? null : goodsName.trim();
    }

    /**
     * 获取运输数量
     *
     * @return quantity - 运输数量
     */
    public BigDecimal getQuantity() {
        return quantity;
    }

    /**
     * 设置运输数量
     *
     * @param quantity 运输数量
     */
    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    /**
     * 获取是否定量:0-不定量,1定量
     *
     * @return quantity_fixed_flag - 是否定量:0-不定量,1定量
     */
    public Integer getQuantityFixedFlag() {
        return quantityFixedFlag;
    }

    /**
     * 设置是否定量:0-不定量,1定量
     *
     * @param quantityFixedFlag 是否定量:0-不定量,1定量
     */
    public void setQuantityFixedFlag(Integer quantityFixedFlag) {
        this.quantityFixedFlag = quantityFixedFlag;
    }

    /**
     * 获取运输单价
     *
     * @return transport_price - 运输单价
     */
    public BigDecimal getTransportPrice() {
        return transportPrice;
    }

    /**
     * 设置运输单价
     *
     * @param transportPrice 运输单价
     */
    public void setTransportPrice(BigDecimal transportPrice) {
        this.transportPrice = transportPrice;
    }

    /**
     * 获取是否固定价:0-浮动价,1固定价
     *
     * @return price_fixed_flag - 是否固定价:0-浮动价,1固定价
     */
    public Integer getPriceFixedFlag() {
        return priceFixedFlag;
    }

    /**
     * 设置是否固定价:0-浮动价,1固定价
     *
     * @param priceFixedFlag 是否固定价:0-浮动价,1固定价
     */
    public void setPriceFixedFlag(Integer priceFixedFlag) {
        this.priceFixedFlag = priceFixedFlag;
    }

    /**
     * 获取签约日期
     *
     * @return sign_date - 签约日期
     */
    public Date getSignDate() {
        return signDate;
    }

    /**
     * 设置签约日期
     *
     * @param signDate 签约日期
     */
    public void setSignDate(Date signDate) {
        this.signDate = signDate;
    }

    /**
     * 获取有效日期开始
     *
     * @return start_date - 有效日期开始
     */
    public Date getStartDate() {
        return startDate;
    }

    /**
     * 设置有效日期开始
     *
     * @param startDate 有效日期开始
     */
    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    /**
     * 获取有效日期截止
     *
     * @return end_date - 有效日期截止
     */
    public Date getEndDate() {
        return endDate;
    }

    /**
     * 设置有效日期截止
     *
     * @param endDate 有效日期截止
     */
    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    /**
     * 获取发票类型：0-其他，1-增值税专用发票，2-增值税普通发票
     *
     * @return invoice_type - 发票类型：0-其他，1-增值税专用发票，2-增值税普通发票
     */
    public String getInvoiceType() {
        return invoiceType;
    }

    /**
     * 设置发票类型：0-其他，1-增值税专用发票，2-增值税普通发票
     *
     * @param invoiceType 发票类型：0-其他，1-增值税专用发票，2-增值税普通发票
     */
    public void setInvoiceType(String invoiceType) {
        this.invoiceType = invoiceType == null ? null : invoiceType.trim();
    }

    /**
     * 获取税率
     *
     * @return invoice_rate - 税率
     */
    public BigDecimal getInvoiceRate() {
        return invoiceRate;
    }

    /**
     * 设置税率
     *
     * @param invoiceRate 税率
     */
    public void setInvoiceRate(BigDecimal invoiceRate) {
        this.invoiceRate = invoiceRate;
    }

    /**
     * 获取结算周期
     *
     * @return settlement_period_type - 结算周期
     */
    public String getSettlementPeriodType() {
        return settlementPeriodType;
    }

    /**
     * 设置结算周期
     *
     * @param settlementPeriodType 结算周期
     */
    public void setSettlementPeriodType(String settlementPeriodType) {
        this.settlementPeriodType = settlementPeriodType == null ? null : settlementPeriodType.trim();
    }

    /**
     * 获取联系人名
     *
     * @return contact_name - 联系人名
     */
    public String getContactName() {
        return contactName;
    }

    /**
     * 设置联系人名
     *
     * @param contactName 联系人名
     */
    public void setContactName(String contactName) {
        this.contactName = contactName == null ? null : contactName.trim();
    }

    /**
     * 获取联系人电话
     *
     * @return contact_phone - 联系人电话
     */
    public String getContactPhone() {
        return contactPhone;
    }

    /**
     * 设置联系人电话
     *
     * @param contactPhone 联系人电话
     */
    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone == null ? null : contactPhone.trim();
    }

    /**
     * 获取删除标识：0-否,1-删除
     *
     * @return del_flg - 删除标识：0-否,1-删除
     */
    public Integer getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标识：0-否,1-删除
     *
     * @param delFlg 删除标识：0-否,1-删除
     */
    public void setDelFlg(Integer delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取备注
     *
     * @return note - 备注
     */
    public String getNote() {
        return note;
    }

    /**
     * 设置备注
     *
     * @param note 备注
     */
    public void setNote(String note) {
        this.note = note == null ? null : note.trim();
    }

    /**
     * 数据版本
     *
     * @return version - 数据版本
     */
    public Integer getVersion() {
        return version;
    }

    /**
     * 数据版本
     *
     * @param version 数据版本
     */
    public void setVersion(Integer version) {
        this.version = version;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", contractId=").append(contractId);
        sb.append(", contractNumber=").append(contractNumber);
        sb.append(", trusteeId=").append(trusteeId);
        sb.append(", trusteeType=").append(trusteeType);
        sb.append(", trusteeName=").append(trusteeName);
        sb.append(", contractType=").append(contractType);
        sb.append(", transportCategoryId=").append(transportCategoryId);
        sb.append(", goodsId=").append(goodsId);
        sb.append(", goodsName=").append(goodsName);
        sb.append(", quantity=").append(quantity);
        sb.append(", quantityFixedFlag=").append(quantityFixedFlag);
        sb.append(", transportPrice=").append(transportPrice);
        sb.append(", priceFixedFlag=").append(priceFixedFlag);
        sb.append(", signDate=").append(signDate);
        sb.append(", startDate=").append(startDate);
        sb.append(", endDate=").append(endDate);
        sb.append(", invoiceType=").append(invoiceType);
        sb.append(", invoiceRate=").append(invoiceRate);
        sb.append(", settlementPeriodType=").append(settlementPeriodType);
        sb.append(", contactName=").append(contactName);
        sb.append(", contactPhone=").append(contactPhone);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", note=").append(note);
        sb.append(", version=").append(version);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}