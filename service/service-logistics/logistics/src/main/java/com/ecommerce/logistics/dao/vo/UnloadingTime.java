package com.ecommerce.logistics.dao.vo;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Table(name = "lgs_unloading_time")
public class UnloadingTime implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 卸货时间ID
     */
    @Id
    @Column(name = "unloading_time_id")
    private String unloadingTimeId;

    /**
     * 卸货点ID（收货地址）
     */
    @Column(name = "unloading_id")
    private String unloadingId;

    /**
     * 客户ID
     */
    @Column(name = "customer_id")
    private String customerId;

    /**
     * 客户名称
     */
    @Column(name = "customer_name")
    private String customerName;

    /**
     * 归属用户ID
     */
    @Column(name = "user_id")
    private String userId;

    /**
     * 归属用户名称
     */
    @Column(name = "user_name")
    private String userName;

    /**
     * 归属用户类型
     */
    @Column(name = "user_type")
    private Integer userType;

    /**
     * 卸货时间（分钟）
     */
    @Column(name = "unloading_time")
    private Integer unloadingTime;

    /**
     * 卸货里程（公里）
     */
    @Column(name = "unloading_km")
    private BigDecimal unloadingKm;

    /**
     * 卸货等待时间（分钟）
     */
    @Column(name = "unloading_waiting_time")
    private Integer unloadingWaitingTime;

    /**
     * 卸货等待里程（公里）
     */
    @Column(name = "unloading_waiting_km")
    private BigDecimal unloadingWaitingKm;

    /**
     * 删除标识
     */
    @Column(name = "del_flg")
    private Boolean delFlg;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;
}