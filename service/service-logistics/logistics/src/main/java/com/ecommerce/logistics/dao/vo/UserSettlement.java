package com.ecommerce.logistics.dao.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;

@Table(name = "lgs_user_settlement")
public class UserSettlement implements Serializable {
    @Id
    @Column(name = "user_settlement_id")
    private String userSettlementId;

    /**
     * 承运商ID
     */
    @Column(name = "carrier_id")
    private String carrierId;

    /**
     * 未结算里程
     */
    @Column(name = "unsettlement_mileage")
    private BigDecimal unsettlementMileage;

    /**
     * 已结算里程
     */
    @Column(name = "settlemented_mileage")
    private BigDecimal settlementedMileage;

    /**
     * 总运输里程
     */
    @Column(name = "total_mileage")
    private BigDecimal totalMileage;

    /**
     * 未结算重量
     */
    @Column(name = "unsettlement_weight")
    private BigDecimal unsettlementWeight;

    /**
     * 已结算重量
     */
    @Column(name = "settlemented_weight")
    private BigDecimal settlementedWeight;

    /**
     * 总运输重量
     */
    @Column(name = "total_weight")
    private BigDecimal totalWeight;

    /**
     * 结算周期
     */
    @Column(name = "settlement_period")
    private String settlementPeriod;

    /**
     * 删除标识
     */
    @Column(name = "del_flg")
    private Byte delFlg;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 数据版本
     */
    private Long version;

    /**
     * 承运商名称
     */
    @Column(name = "carrier_name")
    private String carrierName;

    /**
     * 承运商类型
     */
    @Column(name = "carrier_type")
    private String carrierType;

    /**
     * 未结算运费
     */
    @Column(name = "unsettlement_carriage")
    private BigDecimal unsettlementCarriage;

    /**
     * 已结算运费
     */
    @Column(name = "settlemented_carriage")
    private BigDecimal settlementedCarriage;

    private static final long serialVersionUID = 1L;

    /**
     * @return user_settlement_id
     */
    public String getUserSettlementId() {
        return userSettlementId;
    }

    /**
     * @param userSettlementId
     */
    public void setUserSettlementId(String userSettlementId) {
        this.userSettlementId = userSettlementId == null ? null : userSettlementId.trim();
    }

    /**
     * 获取承运商ID
     *
     * @return carrier_id - 承运商ID
     */
    public String getCarrierId() {
        return carrierId;
    }

    /**
     * 设置承运商ID
     *
     * @param carrierId 承运商ID
     */
    public void setCarrierId(String carrierId) {
        this.carrierId = carrierId == null ? null : carrierId.trim();
    }

    /**
     * 获取未结算里程
     *
     * @return unsettlement_mileage - 未结算里程
     */
    public BigDecimal getUnsettlementMileage() {
        return unsettlementMileage;
    }

    /**
     * 设置未结算里程
     *
     * @param unsettlementMileage 未结算里程
     */
    public void setUnsettlementMileage(BigDecimal unsettlementMileage) {
        this.unsettlementMileage = unsettlementMileage;
    }

    /**
     * 获取已结算里程
     *
     * @return settlemented_mileage - 已结算里程
     */
    public BigDecimal getSettlementedMileage() {
        return settlementedMileage;
    }

    /**
     * 设置已结算里程
     *
     * @param settlementedMileage 已结算里程
     */
    public void setSettlementedMileage(BigDecimal settlementedMileage) {
        this.settlementedMileage = settlementedMileage;
    }

    /**
     * 获取总运输里程
     *
     * @return total_mileage - 总运输里程
     */
    public BigDecimal getTotalMileage() {
        return totalMileage;
    }

    /**
     * 设置总运输里程
     *
     * @param totalMileage 总运输里程
     */
    public void setTotalMileage(BigDecimal totalMileage) {
        this.totalMileage = totalMileage;
    }

    /**
     * 获取未结算重量
     *
     * @return unsettlement_weight - 未结算重量
     */
    public BigDecimal getUnsettlementWeight() {
        return unsettlementWeight;
    }

    /**
     * 设置未结算重量
     *
     * @param unsettlementWeight 未结算重量
     */
    public void setUnsettlementWeight(BigDecimal unsettlementWeight) {
        this.unsettlementWeight = unsettlementWeight;
    }

    /**
     * 获取已结算重量
     *
     * @return settlemented_weight - 已结算重量
     */
    public BigDecimal getSettlementedWeight() {
        return settlementedWeight;
    }

    /**
     * 设置已结算重量
     *
     * @param settlementedWeight 已结算重量
     */
    public void setSettlementedWeight(BigDecimal settlementedWeight) {
        this.settlementedWeight = settlementedWeight;
    }

    /**
     * 获取总运输重量
     *
     * @return total_weight - 总运输重量
     */
    public BigDecimal getTotalWeight() {
        return totalWeight;
    }

    /**
     * 设置总运输重量
     *
     * @param totalWeight 总运输重量
     */
    public void setTotalWeight(BigDecimal totalWeight) {
        this.totalWeight = totalWeight;
    }

    /**
     * 获取结算周期
     *
     * @return settlement_period - 结算周期
     */
    public String getSettlementPeriod() {
        return settlementPeriod;
    }

    /**
     * 设置结算周期
     *
     * @param settlementPeriod 结算周期
     */
    public void setSettlementPeriod(String settlementPeriod) {
        this.settlementPeriod = settlementPeriod;
    }

    /**
     * 获取删除标识
     *
     * @return del_flg - 删除标识
     */
    public Byte getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标识
     *
     * @param delFlg 删除标识
     */
    public void setDelFlg(Byte delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取数据版本
     *
     * @return version - 数据版本
     */
    public Long getVersion() {
        return version;
    }

    /**
     * 设置数据版本
     *
     * @param version 数据版本
     */
    public void setVersion(Long version) {
        this.version = version;
    }

    /**
     * 获取承运商名称
     *
     * @return carrier_name - 承运商名称
     */
    public String getCarrierName() {
        return carrierName;
    }

    /**
     * 设置承运商名称
     *
     * @param carrierName 承运商名称
     */
    public void setCarrierName(String carrierName) {
        this.carrierName = carrierName == null ? null : carrierName.trim();
    }

    /**
     * 获取承运商类型
     *
     * @return carrier_type - 承运商类型
     */
    public String getCarrierType() {
        return carrierType;
    }

    /**
     * 设置承运商类型
     *
     * @param carrierType 承运商类型
     */
    public void setCarrierType(String carrierType) {
        this.carrierType = carrierType == null ? null : carrierType.trim();
    }

    /**
     * 获取未结算运费
     *
     * @return unsettlement_carriage - 未结算运费
     */
    public BigDecimal getUnsettlementCarriage() {
        return unsettlementCarriage;
    }

    /**
     * 设置未结算运费
     *
     * @param unsettlementCarriage 未结算运费
     */
    public void setUnsettlementCarriage(BigDecimal unsettlementCarriage) {
        this.unsettlementCarriage = unsettlementCarriage;
    }

    /**
     * 获取已结算运费
     *
     * @return settlemented_carriage - 已结算运费
     */
    public BigDecimal getSettlementedCarriage() {
        return settlementedCarriage;
    }

    /**
     * 设置已结算运费
     *
     * @param settlementedCarriage 已结算运费
     */
    public void setSettlementedCarriage(BigDecimal settlementedCarriage) {
        this.settlementedCarriage = settlementedCarriage;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", userSettlementId=").append(userSettlementId);
        sb.append(", carrierId=").append(carrierId);
        sb.append(", unsettlementMileage=").append(unsettlementMileage);
        sb.append(", settlementedMileage=").append(settlementedMileage);
        sb.append(", totalMileage=").append(totalMileage);
        sb.append(", unsettlementWeight=").append(unsettlementWeight);
        sb.append(", settlementedWeight=").append(settlementedWeight);
        sb.append(", totalWeight=").append(totalWeight);
        sb.append(", settlementPeriod=").append(settlementPeriod);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", version=").append(version);
        sb.append(", carrierName=").append(carrierName);
        sb.append(", carrierType=").append(carrierType);
        sb.append(", unsettlementCarriage=").append(unsettlementCarriage);
        sb.append(", settlementedCarriage=").append(settlementedCarriage);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}