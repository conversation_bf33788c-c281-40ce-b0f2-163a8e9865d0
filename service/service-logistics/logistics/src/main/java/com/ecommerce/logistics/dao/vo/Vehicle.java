package com.ecommerce.logistics.dao.vo;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Table(name = "lgs_vehicle")
public class Vehicle implements Serializable {
    @Id
    @Column(name = "vehicle_id")
    private String vehicleId;

    /**
     * 归属用户ID
     */
    @Column(name = "user_id")
    private String userId;

    /**
     * 用户类型 1：买家 2：卖家 3：承运商
     */
    @Column(name = "user_type")
    private Integer userType;

    /**
     * 车辆状态
     */
    private String status;

    /**
     * 车牌颜色
     */
    private String color;

    /**
     * 车牌号码
     */
    private String number;

    /**
     * 能源类型id
     */
    @Column(name = "energy_type_id")
    private String energyTypeId;

    /**
     * 运输品类
     */
    @Column(name = "transport_category_id")
    private String transportCategoryId;

    /**
     * 车辆类型ID
     */
    @Column(name = "vehicle_type_id")
    private String vehicleTypeId;

    /**
     * 载重
     */
    @Column(name = "load_capacity")
    private BigDecimal loadCapacity;

    /**
     * 最大装载量
     */
    @Column(name = "max_loading_capacity")
    private BigDecimal maxLoadingCapacity;


    /**
     * 宽度
     */
    private BigDecimal width;

    /**
     * 长度
     */
    private BigDecimal length;

    /**
     * 高度
     */
    private BigDecimal height;

    /**
     * 轴数
     */
    private String axles;

    /**
     * 认证状态 1：未认证 2：认证成功 3：认证失败
     */
    @Column(name = "certification_status")
    private String certificationStatus;

    /**
     * 绑定司机ID
     */
    @Column(name = "bind_driver_id")
    private String bindDriverId;

    /**
     * 司机姓名
     */
    @Column(name = "driver_name")
    private String driverName;

    /**
     * sim卡号
     */
    @Column(name = "sim_number")
    private String simNumber;

    /**
     * 删除标识
     */
    @Column(name = "del_flg")
    private Integer delFlg;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 数据版本
     */
    private Long version;

    /**
     * 自重
     */
    @Column(name = "self_capacity")
    private BigDecimal selfCapacity;

    /**
     * 归属用户名称
     */
    @Column(name = "user_name")
    private String userName;

    /**
     * 认证失败原因
     */
    @Column(name = "certification_fail_reason")
    private String certificationFailReason;

    /**
     * gps厂商Id
     */
    @Column(name = "gps_manufacturer_id")
    private String gpsManufacturerId;

    /**
     * 车载设备号
     */
    @Column(name = "gps_device_number")
    private String gpsDeviceNumber;

    /**
     * 司机手机号
     */
    @Column(name = "driver_phone")
    private String driverPhone;

    /**
     * 是否监控车辆 0:否 1:是
     */
    @Column(name = "is_monitor")
    private Integer isMonitor;

    /**
     * 是否默认车辆 0:否 1:是
     */
    @Column(name = "is_default")
    private Integer isDefault;

    /**
     * 装卸能力,0:否 1:是
     */
    @Column(name = "unload_ability")
    private Integer unloadAbility;

    /**
     * 搬运能力,0:否 1:是
     */
    @Column(name = "carry_ability")
    private Integer carryAbility;

    /**
     * 是否有GPS
     */
    @Column(name = "is_gps_device")
    private Integer isGpsDevice;

    /**
     * 是否能收到GPS信号 0:否 1:是
     */
    @Column(name = "signal_flag")
    private Integer signalFlag;

    /**
     * 是否禁用 0:否 1:是
     */
    @Column(name = "disable_flg")
    private Integer disableFlg;

    private static final long serialVersionUID = 1L;

    public String getEnergyTypeId() {
        return energyTypeId;
    }

    public void setEnergyTypeId(String energyTypeId) {
        this.energyTypeId = energyTypeId;
    }

    /**
     * @return vehicle_id
     */
    public String getVehicleId() {
        return vehicleId;
    }

    /**
     * @param vehicleId
     */
    public void setVehicleId(String vehicleId) {
        this.vehicleId = vehicleId == null ? null : vehicleId.trim();
    }

    /**
     * 获取归属用户ID
     *
     * @return user_id - 归属用户ID
     */
    public String getUserId() {
        return userId;
    }

    /**
     * 设置归属用户ID
     *
     * @param userId 归属用户ID
     */
    public void setUserId(String userId) {
        this.userId = userId == null ? null : userId.trim();
    }

    /**
     * 获取用户类型 1：买家 2：卖家 3：承运商
     *
     * @return user_type - 用户类型 1：买家 2：卖家 3：承运商
     */
    public Integer getUserType() {
        return userType;
    }

    /**
     * 设置用户类型 1：买家 2：卖家 3：承运商
     *
     * @param userType 用户类型 1：买家 2：卖家 3：承运商
     */
    public void setUserType(Integer userType) {
        this.userType = userType;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    public Integer getUnloadAbility() {
        return unloadAbility;
    }

    public void setUnloadAbility(Integer unloadAbility) {
        this.unloadAbility = unloadAbility;
    }

    public Integer getCarryAbility() {
        return carryAbility;
    }

    public void setCarryAbility(Integer carryAbility) {
        this.carryAbility = carryAbility;
    }

    /**
     * 获取车牌颜色
     *
     * @return color - 车牌颜色
     */
    public String getColor() {
        return color;
    }

    /**
     * 设置车牌颜色
     *
     * @param color 车牌颜色
     */
    public void setColor(String color) {
        this.color = color == null ? null : color.trim();
    }

    /**
     * 获取车牌号码
     *
     * @return number - 车牌号码
     */
    public String getNumber() {
        return number;
    }

    /**
     * 设置车牌号码
     *
     * @param number 车牌号码
     */
    public void setNumber(String number) {
        this.number = number == null ? null : number.trim();
    }

    /**
     * 获取运输品类
     *
     * @return transport_category_id - 运输品类
     */
    public String getTransportCategoryId() {
        return transportCategoryId;
    }

    /**
     * 设置运输品类
     *
     * @param transportCategoryId 运输品类
     */
    public void setTransportCategoryId(String transportCategoryId) {
        this.transportCategoryId = transportCategoryId == null ? null : transportCategoryId.trim();
    }

    /**
     * 获取车辆类型ID
     *
     * @return vehicle_type_id - 车辆类型ID
     */
    public String getVehicleTypeId() {
        return vehicleTypeId;
    }

    /**
     * 设置车辆类型ID
     *
     * @param vehicleTypeId 车辆类型ID
     */
    public void setVehicleTypeId(String vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId == null ? null : vehicleTypeId.trim();
    }

    /**
     * 获取载重
     *
     * @return load_capacity - 载重
     */
    public BigDecimal getLoadCapacity() {
        return loadCapacity;
    }

    /**
     * 设置载重
     *
     * @param loadCapacity 载重
     */
    public void setLoadCapacity(BigDecimal loadCapacity) {
        this.loadCapacity = loadCapacity;
    }

    /**
     * 获取最大装载量
     *
     * @return max_loading_capacity - 最大装载量
     */
    public BigDecimal getMaxLoadingCapacity() {
        return maxLoadingCapacity;
    }

    /**
     * 设置最大装载量
     *
     * @param maxLoadingCapacity 最大装载量
     */
    public void setMaxLoadingCapacity(BigDecimal maxLoadingCapacity) {
        this.maxLoadingCapacity = maxLoadingCapacity;
    }

    /**
     * 获取宽度
     *
     * @return width - 宽度
     */
    public BigDecimal getWidth() {
        return width;
    }

    /**
     * 设置宽度
     *
     * @param width 宽度
     */
    public void setWidth(BigDecimal width) {
        this.width = width;
    }

    /**
     * 获取长度
     *
     * @return length - 长度
     */
    public BigDecimal getLength() {
        return length;
    }

    /**
     * 设置长度
     *
     * @param length 长度
     */
    public void setLength(BigDecimal length) {
        this.length = length;
    }

    /**
     * 获取高度
     *
     * @return height - 高度
     */
    public BigDecimal getHeight() {
        return height;
    }

    /**
     * 设置高度
     *
     * @param height 高度
     */
    public void setHeight(BigDecimal height) {
        this.height = height;
    }

    /**
     * 获取轴数
     *
     * @return axles - 轴数
     */
    public String getAxles() {
        return axles;
    }

    /**
     * 设置轴数
     *
     * @param axles 轴数
     */
    public void setAxles(String axles) {
        this.axles = axles == null ? null : axles.trim();
    }

    /**
     * 获取认证状态 1：未认证 2：认证成功 3：认证失败
     *
     * @return certification_status - 认证状态 1：未认证 2：认证成功 3：认证失败
     */
    public String getCertificationStatus() {
        return certificationStatus;
    }

    /**
     * 设置认证状态 1：未认证 2：认证成功 3：认证失败
     *
     * @param certificationStatus 认证状态 1：未认证 2：认证成功 3：认证失败
     */
    public void setCertificationStatus(String certificationStatus) {
        this.certificationStatus = certificationStatus == null ? null : certificationStatus.trim();
    }

    /**
     * 获取绑定司机ID
     *
     * @return bind_driver_id - 绑定司机ID
     */
    public String getBindDriverId() {
        return bindDriverId;
    }

    /**
     * 设置绑定司机ID
     *
     * @param bindDriverId 绑定司机ID
     */
    public void setBindDriverId(String bindDriverId) {
        this.bindDriverId = bindDriverId == null ? null : bindDriverId.trim();
    }

    /**
     * 获取司机姓名
     *
     * @return driver_name - 司机姓名
     */
    public String getDriverName() {
        return driverName;
    }

    /**
     * 设置司机姓名
     *
     * @param driverName 司机姓名
     */
    public void setDriverName(String driverName) {
        this.driverName = driverName == null ? null : driverName.trim();
    }

    /**
     * 获取sim卡号
     *
     * @return sim_number - sim卡号
     */
    public String getSimNumber() {
        return simNumber;
    }

    /**
     * 设置sim卡号
     *
     * @param simNumber sim卡号
     */
    public void setSimNumber(String simNumber) {
        this.simNumber = simNumber == null ? null : simNumber.trim();
    }

    /**
     * 获取删除标识
     *
     * @return del_flg - 删除标识
     */
    public Integer getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标识
     *
     * @param delFlg 删除标识
     */
    public void setDelFlg(Integer delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取数据版本
     *
     * @return version - 数据版本
     */
    public Long getVersion() {
        return version;
    }

    /**
     * 设置数据版本
     *
     * @param version 数据版本
     */
    public void setVersion(Long version) {
        this.version = version;
    }

    /**
     * 获取自重
     *
     * @return self_capacity - 自重
     */
    public BigDecimal getSelfCapacity() {
        return selfCapacity;
    }

    /**
     * 设置自重
     *
     * @param selfCapacity 自重
     */
    public void setSelfCapacity(BigDecimal selfCapacity) {
        this.selfCapacity = selfCapacity;
    }

    /**
     * 获取归属用户名称
     *
     * @return user_name - 归属用户名称
     */
    public String getUserName() {
        return userName;
    }

    /**
     * 设置归属用户名称
     *
     * @param userName 归属用户名称
     */
    public void setUserName(String userName) {
        this.userName = userName == null ? null : userName.trim();
    }

    /**
     * 获取认证失败原因
     *
     * @return certification_fail_reason - 认证失败原因
     */
    public String getCertificationFailReason() {
        return certificationFailReason;
    }

    /**
     * 设置认证失败原因
     *
     * @param certificationFailReason 认证失败原因
     */
    public void setCertificationFailReason(String certificationFailReason) {
        this.certificationFailReason = certificationFailReason == null ? null : certificationFailReason.trim();
    }

    /**
     * 获取gps厂商Id
     *
     * @return gps_manufacturer_id - gps厂商Id
     */
    public String getGpsManufacturerId() {
        return gpsManufacturerId;
    }

    /**
     * 设置gps厂商Id
     *
     * @param gpsManufacturerId gps厂商Id
     */
    public void setGpsManufacturerId(String gpsManufacturerId) {
        this.gpsManufacturerId = gpsManufacturerId == null ? null : gpsManufacturerId.trim();
    }

    /**
     * 获取车载设备号
     *
     * @return gps_device_number - 车载设备号
     */
    public String getGpsDeviceNumber() {
        return gpsDeviceNumber;
    }

    /**
     * 设置车载设备号
     *
     * @param gpsDeviceNumber 车载设备号
     */
    public void setGpsDeviceNumber(String gpsDeviceNumber) {
        this.gpsDeviceNumber = gpsDeviceNumber == null ? null : gpsDeviceNumber.trim();
    }

    /**
     * 获取司机手机号
     *
     * @return driver_phone - 司机手机号
     */
    public String getDriverPhone() {
        return driverPhone;
    }

    /**
     * 设置司机手机号
     *
     * @param driverPhone 司机手机号
     */
    public void setDriverPhone(String driverPhone) {
        this.driverPhone = driverPhone == null ? null : driverPhone.trim();
    }

    /**
     * 获取是否监控车辆 0:否 1:是
     *
     * @return is_monitor - 是否监控车辆 0:否 1:是
     */
    public Integer getIsMonitor() {
        return isMonitor;
    }

    /**
     * 设置是否监控车辆 0:否 1:是
     *
     * @param isMonitor 是否监控车辆 0:否 1:是
     */
    public void setIsMonitor(Integer isMonitor) {
        this.isMonitor = isMonitor;
    }

    /**
     * 获取是否默认车辆 0:否 1:是
     *
     * @return is_default - 是否默认车辆 0:否 1:是
     */
    public Integer getIsDefault() {
        return isDefault;
    }

    /**
     * 设置是否默认车辆 0:否 1:是
     *
     * @param isDefault 是否默认车辆 0:否 1:是
     */
    public void setIsDefault(Integer isDefault) {
        this.isDefault = isDefault;
    }

    public Integer getIsGpsDevice() {
        return isGpsDevice;
    }

    public void setIsGpsDevice(Integer isGpsDevice) {
        this.isGpsDevice = isGpsDevice;
    }

    public Integer getSignalFlag() {
        return signalFlag;
    }

    public void setSignalFlag(Integer signalFlag) {
        this.signalFlag = signalFlag;
    }

    public Integer getDisableFlg() {
        return disableFlg;
    }

    public void setDisableFlg(Integer disableFlg) {
        this.disableFlg = disableFlg;
    }

    @Override
    public String toString() {
        final StringBuffer sb = new StringBuffer("Vehicle{");
        sb.append("vehicleId='").append(vehicleId);
        sb.append(", userId='").append(userId);
        sb.append(", userType=").append(userType);
        sb.append(", status='").append(status);
        sb.append(", color='").append(color);
        sb.append(", number='").append(number);
        sb.append(", energyTypeId='").append(energyTypeId);
        sb.append(", transportCategoryId='").append(transportCategoryId);
        sb.append(", vehicleTypeId='").append(vehicleTypeId);
        sb.append(", loadCapacity=").append(loadCapacity);
        sb.append(", maxLoadingCapacity=").append(maxLoadingCapacity);
        sb.append(", width=").append(width);
        sb.append(", length=").append(length);
        sb.append(", height=").append(height);
        sb.append(", axles='").append(axles);
        sb.append(", certificationStatus='").append(certificationStatus);
        sb.append(", bindDriverId='").append(bindDriverId);
        sb.append(", driverName='").append(driverName);
        sb.append(", simNumber='").append(simNumber);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser='").append(createUser);
        sb.append(", updateUser='").append(updateUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", version=").append(version);
        sb.append(", selfCapacity=").append(selfCapacity);
        sb.append(", userName='").append(userName);
        sb.append(", certificationFailReason='").append(certificationFailReason);
        sb.append(", gpsManufacturerId='").append(gpsManufacturerId);
        sb.append(", gpsDeviceNumber='").append(gpsDeviceNumber);
        sb.append(", driverPhone='").append(driverPhone);
        sb.append(", isMonitor=").append(isMonitor);
        sb.append(", isDefault=").append(isDefault);
        sb.append(", unloadAbility=").append(unloadAbility);
        sb.append(", carryAbility=").append(carryAbility);
        sb.append(", isGpsDevice=").append(isGpsDevice);
        sb.append(", signalFlag=").append(signalFlag);
        sb.append(", disableFlg=").append(disableFlg);
        sb.append('}');
        return sb.toString();
    }
}