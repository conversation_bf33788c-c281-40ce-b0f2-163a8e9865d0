package com.ecommerce.logistics.dao.vo;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

@Table(name = "lgs_vehicle_cert")
public class VehicleCert implements Serializable {
    /**
     * 车辆资质ID
     */
    @Id
    @Column(name = "cert_id")
    private String certId;

    /**
     * 车辆资质编号
     */
    @Column(name = "cert_num")
    private String certNum;

    /**
     * 关联的车辆ID
     */
    @Column(name = "vehicle_id")
    private String vehicleId;

    /**
     * 车辆资质类型枚举
     */
    @Column(name = "cert_type")
    private String certType;

    /**
     * 车辆资质数据JSON
     */
    @Column(name = "cert_json")
    private String certJson;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 删除标识
     */
    @Column(name = "del_flg")
    private Integer delFlg;

    public Integer getDelFlg() {
        return delFlg;
    }

    public void setDelFlg(Integer delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 版本号
     */
    private Long version;

    private static final long serialVersionUID = 1L;

    /**
     * 获取车辆资质ID
     *
     * @return cert_id - 车辆资质ID
     */
    public String getCertId() {
        return certId;
    }

    /**
     * 设置车辆资质ID
     *
     * @param certId 车辆资质ID
     */
    public void setCertId(String certId) {
        this.certId = certId == null ? null : certId.trim();
    }

    /**
     * 获取车辆资质编号
     *
     * @return cert_num - 车辆资质编号
     */
    public String getCertNum() {
        return certNum;
    }

    /**
     * 设置车辆资质编号
     *
     * @param certNum 车辆资质编号
     */
    public void setCertNum(String certNum) {
        this.certNum = certNum == null ? null : certNum.trim();
    }

    /**
     * 获取关联的车辆ID
     *
     * @return vehicle_id - 关联的车辆ID
     */
    public String getVehicleId() {
        return vehicleId;
    }

    /**
     * 设置关联的车辆ID
     *
     * @param vehicleId 关联的车辆ID
     */
    public void setVehicleId(String vehicleId) {
        this.vehicleId = vehicleId == null ? null : vehicleId.trim();
    }

    /**
     * 获取车辆资质类型枚举
     *
     * @return cert_type - 车辆资质类型枚举
     */
    public String getCertType() {
        return certType;
    }

    /**
     * 设置车辆资质类型枚举
     *
     * @param certType 车辆资质类型枚举
     */
    public void setCertType(String certType) {
        this.certType = certType == null ? null : certType.trim();
    }

    /**
     * 获取车辆资质数据JSON
     *
     * @return cert_json - 车辆资质数据JSON
     */
    public String getCertJson() {
        return certJson;
    }

    /**
     * 设置车辆资质数据JSON
     *
     * @param certJson 车辆资质数据JSON
     */
    public void setCertJson(String certJson) {
        this.certJson = certJson == null ? null : certJson.trim();
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * @return update_time
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * @param updateTime
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取版本号
     *
     * @return version - 版本号
     */
    public Long getVersion() {
        return version;
    }

    /**
     * 设置版本号
     *
     * @param version 版本号
     */
    public void setVersion(Long version) {
        this.version = version;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", certId=").append(certId);
        sb.append(", certNum=").append(certNum);
        sb.append(", vehicleId=").append(vehicleId);
        sb.append(", certType=").append(certType);
        sb.append(", certJson=").append(certJson);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", version=").append(version);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}