package com.ecommerce.logistics.dao.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;

@Table(name = "lgs_vehicle_type")
public class VehicleType implements Serializable {
    @Id
    @Column(name = "vehicle_type_id")
    private String vehicleTypeId;

    /**
     * 类型名称
     */
    @Column(name = "type_name")
    private String typeName;

    /**
     * 类型备注
     */
    private String note;

    /**
     * 轴数
     */
    private String axles;

    /**
     * 车厢类型
     */
    @Column(name = "carriage_type")
    private String carriageType;

    /**
     * 最大载重
     */
    @Column(name = "max_load_capacity")
    private BigDecimal maxLoadCapacity;

    /**
     * 最大自重
     */
    @Column(name = "max_self_capacity")
    private BigDecimal maxSelfCapacity;

    /**
     * 方量
     */
    @Column(name = "max_volume")
    private BigDecimal maxVolume;

    /**
     * 最大长度
     */
    @Column(name = "max_length")
    private BigDecimal maxLength;

    /**
     * 最大宽度
     */
    @Column(name = "max_width")
    private BigDecimal maxWidth;

    /**
     * 最大高度
     */
    @Column(name = "max_height")
    private BigDecimal maxHeight;

    /**
     * 是否不限车型，0-不是，1-不限车型
     */
    @Column(name = "unlimite_type")
    private Integer unlimiteType;

    /**
     * 是否需要校验车牌0-不校验，1-校验
     */
    @Column(name = "verification_number")
    private Integer verificationNumber;

    @Column(name = "deposit_amount")
    private BigDecimal depositAmount;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 删除标识
     */
    @Column(name = "del_flg")
    private Byte delFlg;

    /**
     * 数据版本
     */
    private Long version;

    /**
     * 关联运输品类ID
     */
    @Column(name = "transport_category_id")
    private String transportCategoryId;

    private static final long serialVersionUID = 1L;

    /**
     * @return vehicle_type_id
     */
    public String getVehicleTypeId() {
        return vehicleTypeId;
    }

    /**
     * @param vehicleTypeId
     */
    public void setVehicleTypeId(String vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId == null ? null : vehicleTypeId.trim();
    }

    /**
     * 获取类型名称
     *
     * @return type_name - 类型名称
     */
    public String getTypeName() {
        return typeName;
    }

    /**
     * 设置类型名称
     *
     * @param typeName 类型名称
     */
    public void setTypeName(String typeName) {
        this.typeName = typeName == null ? null : typeName.trim();
    }

    /**
     * 获取类型备注
     *
     * @return note - 类型备注
     */
    public String getNote() {
        return note;
    }

    /**
     * 设置类型备注
     *
     * @param note 类型备注
     */
    public void setNote(String note) {
        this.note = note == null ? null : note.trim();
    }

    /**
     * 获取轴数
     *
     * @return axles - 轴数
     */
    public String getAxles() {
        return axles;
    }

    /**
     * 设置轴数
     *
     * @param axles 轴数
     */
    public void setAxles(String axles) {
        this.axles = axles == null ? null : axles.trim();
    }

    /**
     * 获取车厢类型
     *
     * @return carriage_type - 车厢类型
     */
    public String getCarriageType() {
        return carriageType;
    }

    /**
     * 设置车厢类型
     *
     * @param carriageType 车厢类型
     */
    public void setCarriageType(String carriageType) {
        this.carriageType = carriageType == null ? null : carriageType.trim();
    }

    /**
     * 获取最大载重
     *
     * @return max_load_capacity - 最大载重
     */
    public BigDecimal getMaxLoadCapacity() {
        return maxLoadCapacity;
    }

    /**
     * 设置最大载重
     *
     * @param maxLoadCapacity 最大载重
     */
    public void setMaxLoadCapacity(BigDecimal maxLoadCapacity) {
        this.maxLoadCapacity = maxLoadCapacity;
    }

    /**
     * 获取最大自重
     *
     * @return max_self_capacity - 最大自重
     */
    public BigDecimal getMaxSelfCapacity() {
        return maxSelfCapacity;
    }

    /**
     * 设置最大自重
     *
     * @param maxSelfCapacity 最大自重
     */
    public void setMaxSelfCapacity(BigDecimal maxSelfCapacity) {
        this.maxSelfCapacity = maxSelfCapacity;
    }

    /**
     * 获取方量
     *
     * @return max_volume - 方量
     */
    public BigDecimal getMaxVolume() {
        return maxVolume;
    }

    /**
     * 设置方量
     *
     * @param maxVolume 方量
     */
    public void setMaxVolume(BigDecimal maxVolume) {
        this.maxVolume = maxVolume;
    }

    /**
     * 获取最大长度
     *
     * @return maxLength - 最大长度
     */
    public BigDecimal getMaxLength() {
        return maxLength;
    }

    /**
     * 设置最大长度
     *
     * @param maxLength 最大长度
     */
    public void setMaxLength(BigDecimal maxLength) {
        this.maxLength = maxLength;
    }

    /**
     * 获取最大宽度
     *
     * @return max_width - 最大宽度
     */
    public BigDecimal getMaxWidth() {
        return maxWidth;
    }

    /**
     * 设置最大宽度
     *
     * @param maxWidth 最大宽度
     */
    public void setMaxWidth(BigDecimal maxWidth) {
        this.maxWidth = maxWidth;
    }

    /**
     * 获取最大高度
     *
     * @return max_height - 最大高度
     */
    public BigDecimal getMaxHeight() {
        return maxHeight;
    }

    /**
     * 设置最大高度
     *
     * @param maxHeight 最大高度
     */
    public void setMaxHeight(BigDecimal maxHeight) {
        this.maxHeight = maxHeight;
    }

    /**
     * 获取是否不限车型，0-不是，1-不限车型
     *
     * @return unlimite_type - 是否不限车型，0-不是，1-不限车型
     */
    public Integer getUnlimiteType() {
        return unlimiteType;
    }

    /**
     * 设置是否不限车型，0-不是，1-不限车型
     *
     * @param unlimiteType 是否不限车型，0-不是，1-不限车型
     */
    public void setUnlimiteType(Integer unlimiteType) {
        this.unlimiteType = unlimiteType;
    }

    /**
     * 获取是否需要校验车牌0-不校验，1-校验
     *
     * @return verification_number - 是否需要校验车牌0-不校验，1-校验
     */
    public Integer getVerificationNumber() {
        return verificationNumber;
    }

    /**
     * 设置是否需要校验车牌0-不校验，1-校验
     *
     * @param verificationNumber 是否需要校验车牌0-不校验，1-校验
     */
    public void setVerificationNumber(Integer verificationNumber) {
        this.verificationNumber = verificationNumber;
    }

    /**
     * 获取创建人
     *
     * @return create_user - 创建人
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建人
     *
     * @param createUser 创建人
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新人
     *
     * @return update_user - 更新人
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新人
     *
     * @param updateUser 更新人
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取删除标识
     *
     * @return del_flg - 删除标识
     */
    public Byte getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标识
     *
     * @param delFlg 删除标识
     */
    public void setDelFlg(Byte delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取数据版本
     *
     * @return version - 数据版本
     */
    public Long getVersion() {
        return version;
    }

    /**
     * 设置数据版本
     *
     * @param version 数据版本
     */
    public void setVersion(Long version) {
        this.version = version;
    }

    /**
     * 获取关联运输品类ID
     *
     * @return transport_category_id - 关联运输品类ID
     */
    public String getTransportCategoryId() {
        return transportCategoryId;
    }

    /**
     * 设置关联运输品类ID
     *
     * @param transportCategoryId 关联运输品类ID
     */
    public void setTransportCategoryId(String transportCategoryId) {
        this.transportCategoryId = transportCategoryId == null ? null : transportCategoryId.trim();
    }

    public BigDecimal getDepositAmount() {
        return depositAmount;
    }

    public void setDepositAmount(BigDecimal depositAmount) {
        this.depositAmount = depositAmount;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", vehicleTypeId=").append(vehicleTypeId);
        sb.append(", typeName=").append(typeName);
        sb.append(", note=").append(note);
        sb.append(", axles=").append(axles);
        sb.append(", carriageType=").append(carriageType);
        sb.append(", maxLoadCapacity=").append(maxLoadCapacity);
        sb.append(", maxSelfCapacity=").append(maxSelfCapacity);
        sb.append(", maxVolume=").append(maxVolume);
        sb.append(", maxLength=").append(maxLength);
        sb.append(", maxWidth=").append(maxWidth);
        sb.append(", maxHeight=").append(maxHeight);
        sb.append(", unlimiteType=").append(unlimiteType);
        sb.append(", verificationNumber=").append(verificationNumber);
        sb.append(", depositAmount=").append(depositAmount);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", version=").append(version);
        sb.append(", transportCategoryId=").append(transportCategoryId);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}