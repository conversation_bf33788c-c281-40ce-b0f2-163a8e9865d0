package com.ecommerce.logistics.dao.vo;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

@Table(name = "lgs_warehouse_zone_map")
public class WarehouseZoneMap implements Serializable {
    @Id
    @Column(name = "address_map_id")
    private String addressMapId;

    /**
     * 仓库ID
     */
    @Column(name = "warehouse_id")
    private String warehouseId;

    /**
     * 仓库类型
     */
    private String type;

    /**
     * 收货省份
     */
    private String province;

    /**
     * 省份编码
     */
    @Column(name = "province_code")
    private String provinceCode;

    /**
     * 收货城市
     */
    private String city;

    /**
     * 城市编码
     */
    @Column(name = "city_code")
    private String cityCode;

    /**
     * 收货地区
     */
    private String district;

    /**
     * 区域编码
     */
    @Column(name = "district_code")
    private String districtCode;

    /**
     * 删除标识
     */
    @Column(name = "del_flg")
    private Integer delFlg;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 数据版本
     */
    private Long version;

    private String street;

    private String streetCode;

    private static final long serialVersionUID = 1L;

    /**
     * @return address_map_id
     */
    public String getAddressMapId() {
        return addressMapId;
    }

    /**
     * @param addressMapId
     */
    public void setAddressMapId(String addressMapId) {
        this.addressMapId = addressMapId == null ? null : addressMapId.trim();
    }

    /**
     * 获取仓库ID
     *
     * @return warehouse_id - 仓库ID
     */
    public String getWarehouseId() {
        return warehouseId;
    }

    /**
     * 设置仓库ID
     *
     * @param warehouseId 仓库ID
     */
    public void setWarehouseId(String warehouseId) {
        this.warehouseId = warehouseId == null ? null : warehouseId.trim();
    }

    /**
     * 获取仓库类型
     *
     * @return type - 仓库类型
     */
    public String getType() {
        return type;
    }

    /**
     * 设置仓库类型
     *
     * @param type 仓库类型
     */
    public void setType(String type) {
        this.type = type == null ? null : type.trim();
    }

    /**
     * 获取收货省份
     *
     * @return province - 收货省份
     */
    public String getProvince() {
        return province;
    }

    /**
     * 设置收货省份
     *
     * @param province 收货省份
     */
    public void setProvince(String province) {
        this.province = province == null ? null : province.trim();
    }

    /**
     * 获取省份编码
     *
     * @return province_code - 省份编码
     */
    public String getProvinceCode() {
        return provinceCode;
    }

    /**
     * 设置省份编码
     *
     * @param provinceCode 省份编码
     */
    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode == null ? null : provinceCode.trim();
    }

    public void setStreetCode(String streetCode) {
        this.streetCode = streetCode == null ? null :streetCode.trim();
    }

    public String getStreetCode() {
        return streetCode;
    }
    /**
     * 获取收货城市
     *
     * @return city - 收货城市
     */
    public String getCity() {
        return city;
    }

    /**
     * 设置收货城市
     *
     * @param city 收货城市
     */
    public void setCity(String city) {
        this.city = city == null ? null : city.trim();
    }

    /**
     * 获取城市编码
     *
     * @return city_code - 城市编码
     */
    public String getCityCode() {
        return cityCode;
    }

    /**
     * 设置城市编码
     *
     * @param cityCode 城市编码
     */
    public void setCityCode(String cityCode) {
        this.cityCode = cityCode == null ? null : cityCode.trim();
    }

    /**
     * 获取收货地区
     *
     * @return district - 收货地区
     */
    public String getDistrict() {
        return district;
    }

    /**
     * 设置收货地区
     *
     * @param district 收货地区
     */
    public void setDistrict(String district) {
        this.district = district == null ? null : district.trim();
    }

    /**
     * 获取区域编码
     *
     * @return district_code - 区域编码
     */
    public String getDistrictCode() {
        return districtCode;
    }

    /**
     * 设置区域编码
     *
     * @param districtCode 区域编码
     */
    public void setDistrictCode(String districtCode) {
        this.districtCode = districtCode == null ? null : districtCode.trim();
    }

    /**
     * 获取删除标识
     *
     * @return del_flg - 删除标识
     */
    public Integer getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标识
     *
     * @param delFlg 删除标识
     */
    public void setDelFlg(Integer delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取数据版本
     *
     * @return version - 数据版本
     */
    public Long getVersion() {
        return version;
    }

    /**
     * 设置数据版本
     *
     * @param version 数据版本
     */
    public void setVersion(Long version) {
        this.version = version;
    }

    /**
     * @return street
     */
    public String getStreet() {
        return street;
    }

    /**
     * @param street
     */
    public void setStreet(String street) {
        this.street = street == null ? null : street.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", addressMapId=").append(addressMapId);
        sb.append(", warehouseId=").append(warehouseId);
        sb.append(", type=").append(type);
        sb.append(", province=").append(province);
        sb.append(", provinceCode=").append(provinceCode);
        sb.append(", city=").append(city);
        sb.append(", cityCode=").append(cityCode);
        sb.append(", district=").append(district);
        sb.append(", districtCode=").append(districtCode);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", version=").append(version);
        sb.append(", street=").append(street);
        sb.append(", streetCode=").append(streetCode);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}