package com.ecommerce.logistics.dao.vo;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Table(name = "lgs_waybill")
public class Waybill implements Serializable {
    /**
     * 运单id
     */
    @Id
    @Column(name = "waybill_id")
    private String waybillId;

    /**
     * 运单编号
     */
    @Column(name = "waybill_num")
    private String waybillNum;

    /**
     * 调度单id
     */
    @Column(name = "dispatch_bill_id")
    private String dispatchBillId;

    /**
     * 承运商id
     */
    @Column(name = "carrier_id")
    private String carrierId;

    /**
     * 承运商类型
     */
    @Column(name = "carrier_type")
    private String carrierType;

    /**
     * 提货单id
     */
    @Column(name = "picking_bill_id")
    private String pickingBillId;

    /**
     * 是否为主运单 0：否 1：是
     */
    @Column(name = "is_main_waybill")
    private Byte isMainWaybill;

    /**
     * 父运单id
     */
    @Column(name = "parent_id")
    private String parentId;

    /**
     * 特殊业务分组ID
     */
    @Column(name = "special_id")
    private String specialId;

    /**
     * 运单类型 1：社会运力抢单  2：承运商指派 3：买家自提
     */
    private String type;

    /**
     * 单据代理类型
     */
    @Column(name = "bill_proxy_type")
    private String billProxyType;

    /**
     * 是否可操作:0-否,1-是
     */
    @Column(name = "can_operate")
    private Byte canOperate;

    /**
     * 运单状态 1：待整合 2：待审核 3：待发布 4：待接单 5：待配送 6：配送中 7：已完成 8：已取消
     */
    private String status;

    /**
     * 二维码内容
     */
    @Column(name = "qr_code")
    private String qrCode;

    /**
     * 结束原因
     */
    @Column(name = "end_reason")
    private String endReason;

    /**
     * 配送时间
     */
    @Column(name = "delivery_time")
    private Date deliveryTime;

    /**
     * 配送时段
     */
    @Column(name = "delivery_time_range")
    private String deliveryTimeRange;

    /**
     * 提货时间
     */
    @Column(name = "picking_time")
    private Date pickingTime;

    /**
     * 收入运费
     */
    @Column(name = "income_carriage")
    private BigDecimal incomeCarriage;

    /**
     * 预警状态
     */
    @Column(name = "sla_status")
    private String slaStatus;

    /**
     * 送达时间
     */
    @Column(name = "arrive_destination_time")
    private Date arriveDestinationTime;

    /**
     * 监控送达时间
     */
    @Column(name = "monitor_arrive_time")
    private Date monitorArriveTime;

    /**
     * 开始搬运时间
     */
    @Column(name = "begin_carry_time")
    private Date beginCarryTime;

    /**
     * 完成时间
     */
    @Column(name = "complete_time")
    private Date completeTime;

    /**
     * 监控完成时间
     */
    @Column(name = "monitor_complete_time")
    private Date monitorCompleteTime;

    /**
     * 取消时间
     */
    @Column(name = "cancel_time")
    private Date cancelTime;

    /**
     * 实际出场量
     */
    @Column(name = "actual_quantity")
    private BigDecimal actualQuantity;

    /**
     * 签收数量
     */
    @Column(name = "sign_quantity")
    private BigDecimal signQuantity;

    /**
     * 签收备注
     */
    @Column(name = "sign_remark")
    private String signRemark;

    /**
     * 空载费
     */
    @Column(name = "empty_load_charge")
    private BigDecimal emptyLoadCharge;

    /**
     * 是否计算空载费
     */
    @Column(name = "empty_load_flag")
    private Byte emptyLoadFlag;

    /**
     * 空载方量
     */
    @Column(name = "empty_load_quantity")
    private BigDecimal emptyLoadQuantity;

    /**
     * 子运单顺序
     */
    @Column(name = "waybill_sequence")
    private Integer waybillSequence;

    /**
     * 是否需要监控
     */
    @Column(name = "need_monitor")
    private Byte needMonitor;

    /**
     * 是否可监控
     */
    @Column(name = "can_monitor")
    private Byte canMonitor;

    /**
     * 平台是否评价（0-未评价，1-已评价）
     */
    @Column(name = "platform_evaluate_flag")
    private Byte platformEvaluateFlag;

    /**
     * 司机是否评价（0-未评价，1-已评价）
     */
    @Column(name = "driver_evaluate_flag")
    private Byte driverEvaluateFlag;

    /**
     * 是否上传过交付凭证（0-未上传，1-已上传）
     */
    @Column(name = "delivery_certificate_flag")
    private Byte deliveryCertificateFlag;

    /**
     * 是否提交过卸货异议(0-未提交，1-已提交)
     */
    @Column(name = "objection_flag")
    private Byte objectionFlag;

    /**
     * 厂家货物是否准备就绪
     */
    @Column(name = "ready_flag")
    private Byte readyFlag;

    /**
     * 合同Id
     */
    @Column(name = "deals_id")
    private String dealsId;

    /**
     * 合同序号
     */
    @Column(name = "deals_name")
    private String dealsName;

    /**
     * 合同项目名称
     */
    @Column(name = "project_name")
    private String projectName;

    /**
     * 开票对象类型
     */
    @Column(name = "invoice_target_type")
    private String invoiceTargetType;

    /**
     * 开票会员ID
     */
    @Column(name = "invoice_user_id")
    private String invoiceUserId;

    /**
     * 开票状态
     */
    @Column(name = "invoice_status")
    private String invoiceStatus;

    /**
     * 开票结果描述
     */
    @Column(name = "invoice_message")
    private String invoiceMessage;

    /**
     * 删除标识
     */
    @Column(name = "del_flg")
    private Byte delFlg;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 数据版本
     */
    private Long version;

    @Column(name = "close_reason")
    private String closeReason;

    @Column(name = "close_time")
    private Date closeTime;

    /**
     * 同步标识:1未同步 2: 正向同步 3:反向同步
     */
    @Column(name = "sync_flag")
    private String syncFlag;

    /**
     * 同步失败原因
     */
    @Column(name = "sync_fail_reason")
    private String syncFailReason;

    /**
     * 外部运单号
     */
    @Column(name = "external_waybill_num")
    private String externalWaybillNum;

    /**
     * 外部运单状态
     */
    @Column(name = "external_waybill_status")
    private String externalWaybillStatus;

    /**
     * 外部请求号
     */
    @Column(name = "external_request_no")
    private String externalRequestNo;

    /**
     * 产地
     */
    @Column(name = "origin_place")
    private String originPlace;

    /**
     * 进厂时间
     */
    @Column(name = "enter_factory_time")
    private Date enterFactoryTime;

    private static final long serialVersionUID = 1L;

    /**
     * 获取运单id
     *
     * @return waybill_id - 运单id
     */
    public String getWaybillId() {
        return waybillId;
    }

    /**
     * 设置运单id
     *
     * @param waybillId 运单id
     */
    public void setWaybillId(String waybillId) {
        this.waybillId = waybillId == null ? null : waybillId.trim();
    }

    /**
     * 获取运单编号
     *
     * @return waybill_num - 运单编号
     */
    public String getWaybillNum() {
        return waybillNum;
    }

    /**
     * 设置运单编号
     *
     * @param waybillNum 运单编号
     */
    public void setWaybillNum(String waybillNum) {
        this.waybillNum = waybillNum == null ? null : waybillNum.trim();
    }

    /**
     * 获取调度单id
     *
     * @return dispatch_bill_id - 调度单id
     */
    public String getDispatchBillId() {
        return dispatchBillId;
    }

    /**
     * 设置调度单id
     *
     * @param dispatchBillId 调度单id
     */
    public void setDispatchBillId(String dispatchBillId) {
        this.dispatchBillId = dispatchBillId == null ? null : dispatchBillId.trim();
    }

    /**
     * 获取承运商id
     *
     * @return carrier_id - 承运商id
     */
    public String getCarrierId() {
        return carrierId;
    }

    /**
     * 设置承运商id
     *
     * @param carrierId 承运商id
     */
    public void setCarrierId(String carrierId) {
        this.carrierId = carrierId == null ? null : carrierId.trim();
    }

    /**
     * 获取承运商类型
     *
     * @return carrier_type - 承运商类型
     */
    public String getCarrierType() {
        return carrierType;
    }

    /**
     * 设置承运商类型
     *
     * @param carrierType 承运商类型
     */
    public void setCarrierType(String carrierType) {
        this.carrierType = carrierType == null ? null : carrierType.trim();
    }

    /**
     * 获取提货单id
     *
     * @return picking_bill_id - 提货单id
     */
    public String getPickingBillId() {
        return pickingBillId;
    }

    /**
     * 设置提货单id
     *
     * @param pickingBillId 提货单id
     */
    public void setPickingBillId(String pickingBillId) {
        this.pickingBillId = pickingBillId == null ? null : pickingBillId.trim();
    }

    /**
     * 获取是否为主运单 0：否 1：是
     *
     * @return is_main_waybill - 是否为主运单 0：否 1：是
     */
    public Byte getIsMainWaybill() {
        return isMainWaybill;
    }

    /**
     * 设置是否为主运单 0：否 1：是
     *
     * @param isMainWaybill 是否为主运单 0：否 1：是
     */
    public void setIsMainWaybill(Byte isMainWaybill) {
        this.isMainWaybill = isMainWaybill;
    }

    /**
     * 获取父运单id
     *
     * @return parent_id - 父运单id
     */
    public String getParentId() {
        return parentId;
    }

    /**
     * 设置父运单id
     *
     * @param parentId 父运单id
     */
    public void setParentId(String parentId) {
        this.parentId = parentId == null ? null : parentId.trim();
    }

    /**
     * 获取特殊业务分组ID
     *
     * @return special_id - 特殊业务分组ID
     */
    public String getSpecialId() {
        return specialId;
    }

    /**
     * 设置特殊业务分组ID
     *
     * @param specialId 特殊业务分组ID
     */
    public void setSpecialId(String specialId) {
        this.specialId = specialId == null ? null : specialId.trim();
    }

    /**
     * 获取运单类型 1：社会运力抢单  2：承运商指派 3：买家自提
     *
     * @return type - 运单类型 1：社会运力抢单  2：承运商指派 3：买家自提
     */
    public String getType() {
        return type;
    }

    /**
     * 设置运单类型 1：社会运力抢单  2：承运商指派 3：买家自提
     *
     * @param type 运单类型 1：社会运力抢单  2：承运商指派 3：买家自提
     */
    public void setType(String type) {
        this.type = type == null ? null : type.trim();
    }

    /**
     * 获取运单状态 1：待整合 2：待审核 3：待发布 4：待接单 5：待配送 6：配送中 7：已完成 8：已取消
     *
     * @return status - 运单状态 1：待整合 2：待审核 3：待发布 4：待接单 5：待配送 6：配送中 7：已完成 8：已取消
     */
    public String getStatus() {
        return status;
    }

    /**
     * 设置运单状态 1：待整合 2：待审核 3：待发布 4：待接单 5：待配送 6：配送中 7：已完成 8：已取消
     *
     * @param status 运单状态 1：待整合 2：待审核 3：待发布 4：待接单 5：待配送 6：配送中 7：已完成 8：已取消
     */
    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    /**
     * 获取二维码内容
     *
     * @return qr_code - 二维码内容
     */
    public String getQrCode() {
        return qrCode;
    }

    /**
     * 设置二维码内容
     *
     * @param qrCode 二维码内容
     */
    public void setQrCode(String qrCode) {
        this.qrCode = qrCode == null ? null : qrCode.trim();
    }

    /**
     * 获取end_reason
     *
     * @return end_reason - end_reason
     */
    public String getEndReason() {
        return endReason;
    }

    /**
     * 设置end_reason
     *
     * @param endReason end_reason
     */
    public void setEndReason(String endReason) {
        this.endReason = endReason == null ? null : endReason.trim();
    }

    /**
     * 获取配送时间
     *
     * @return delivery_time - 配送时间
     */
    public Date getDeliveryTime() {
        return deliveryTime;
    }

    /**
     * 设置配送时间
     *
     * @param deliveryTime 配送时间
     */
    public void setDeliveryTime(Date deliveryTime) {
        this.deliveryTime = deliveryTime;
    }

    /**
     * 获取配送时段
     *
     * @return delivery_time_range - 配送时段
     */
    public String getDeliveryTimeRange() {
        return deliveryTimeRange;
    }

    /**
     * 设置配送时段
     *
     * @param deliveryTimeRange 配送时段
     */
    public void setDeliveryTimeRange(String deliveryTimeRange) {
        this.deliveryTimeRange = deliveryTimeRange == null ? null : deliveryTimeRange.trim();
    }

    /**
     * 获取提货时间
     *
     * @return picking_time - 提货时间
     */
    public Date getPickingTime() {
        return pickingTime;
    }

    /**
     * 设置提货时间
     *
     * @param pickingTime 提货时间
     */
    public void setPickingTime(Date pickingTime) {
        this.pickingTime = pickingTime;
    }

    /**
     * 获取收入运费
     *
     * @return income_carriage - 收入运费
     */
    public BigDecimal getIncomeCarriage() {
        return incomeCarriage;
    }

    /**
     * 设置收入运费
     *
     * @param incomeCarriage 收入运费
     */
    public void setIncomeCarriage(BigDecimal incomeCarriage) {
        this.incomeCarriage = incomeCarriage;
    }

    /**
     * 获取预警状态
     *
     * @return sla_status - 预警状态
     */
    public String getSlaStatus() {
        return slaStatus;
    }

    /**
     * 设置预警状态
     *
     * @param slaStatus 预警状态
     */
    public void setSlaStatus(String slaStatus) {
        this.slaStatus = slaStatus == null ? null : slaStatus.trim();
    }

    /**
     * 获取送达时间
     *
     * @return arrive_destination_time - 送达时间
     */
    public Date getArriveDestinationTime() {
        return arriveDestinationTime;
    }

    /**
     * 设置送达时间
     *
     * @param arriveDestinationTime 送达时间
     */
    public void setArriveDestinationTime(Date arriveDestinationTime) {
        this.arriveDestinationTime = arriveDestinationTime;
    }

    /**
     * 获取监控送达时间
     *
     * @return monitor_arrive_time - 监控送达时间
     */
    public Date getMonitorArriveTime() {
        return monitorArriveTime;
    }

    /**
     * 设置监控送达时间
     *
     * @param monitorArriveTime 监控送达时间
     */
    public void setMonitorArriveTime(Date monitorArriveTime) {
        this.monitorArriveTime = monitorArriveTime;
    }

    /**
     * 获取开始搬运时间
     *
     * @return begin_carry_time - 开始搬运时间
     */
    public Date getBeginCarryTime() {
        return beginCarryTime;
    }

    /**
     * 设置开始搬运时间
     *
     * @param beginCarryTime 开始搬运时间
     */
    public void setBeginCarryTime(Date beginCarryTime) {
        this.beginCarryTime = beginCarryTime;
    }

    /**
     * 获取完成时间
     *
     * @return complete_time - 完成时间
     */
    public Date getCompleteTime() {
        return completeTime;
    }

    /**
     * 设置完成时间
     *
     * @param completeTime 完成时间
     */
    public void setCompleteTime(Date completeTime) {
        this.completeTime = completeTime;
    }

    /**
     * 获取监控完成时间
     *
     * @return monitor_complete_time - 监控完成时间
     */
    public Date getMonitorCompleteTime() {
        return monitorCompleteTime;
    }

    /**
     * 设置监控完成时间
     *
     * @param monitorCompleteTime 监控完成时间
     */
    public void setMonitorCompleteTime(Date monitorCompleteTime) {
        this.monitorCompleteTime = monitorCompleteTime;
    }

    /**
     * 获取取消时间
     *
     * @return cancel_time - 取消时间
     */
    public Date getCancelTime() {
        return cancelTime;
    }

    /**
     * 设置取消时间
     *
     * @param cancelTime 取消时间
     */
    public void setCancelTime(Date cancelTime) {
        this.cancelTime = cancelTime;
    }

    /**
     * 获取实际出场量
     *
     * @return actual_quantity - 实际出场量
     */
    public BigDecimal getActualQuantity() {
        return actualQuantity;
    }

    /**
     * 设置实际出场量
     *
     * @param actualQuantity 实际出场量
     */
    public void setActualQuantity(BigDecimal actualQuantity) {
        this.actualQuantity = actualQuantity;
    }

    /**
     * 获取签收数量
     *
     * @return sign_quantity - 签收数量
     */
    public BigDecimal getSignQuantity() {
        return signQuantity;
    }

    /**
     * 设置签收数量
     *
     * @param signQuantity 签收数量
     */
    public void setSignQuantity(BigDecimal signQuantity) {
        this.signQuantity = signQuantity;
    }

    /**
     * 获取签收备注
     *
     * @return sign_remark - 签收备注
     */
    public String getSignRemark() {
        return signRemark;
    }

    /**
     * 设置签收备注
     *
     * @param signRemark 签收备注
     */
    public void setSignRemark(String signRemark) {
        this.signRemark = signRemark == null ? null : signRemark.trim();
    }

    /**
     * 获取空载费
     *
     * @return empty_load_charge - 空载费
     */
    public BigDecimal getEmptyLoadCharge() {
        return emptyLoadCharge;
    }

    /**
     * 设置空载费
     *
     * @param emptyLoadCharge 空载费
     */
    public void setEmptyLoadCharge(BigDecimal emptyLoadCharge) {
        this.emptyLoadCharge = emptyLoadCharge;
    }

    /**
     * 获取是否计算空载费
     *
     * @return empty_load_flag - 是否计算空载费
     */
    public Byte getEmptyLoadFlag() {
        return emptyLoadFlag;
    }

    /**
     * 设置是否计算空载费
     *
     * @param emptyLoadFlag 是否计算空载费
     */
    public void setEmptyLoadFlag(Byte emptyLoadFlag) {
        this.emptyLoadFlag = emptyLoadFlag;
    }

    /**
     * 获取空载方量
     *
     * @return empty_load_quantity - 空载方量
     */
    public BigDecimal getEmptyLoadQuantity() {
        return emptyLoadQuantity;
    }

    /**
     * 设置空载方量
     *
     * @param emptyLoadQuantity 空载方量
     */
    public void setEmptyLoadQuantity(BigDecimal emptyLoadQuantity) {
        this.emptyLoadQuantity = emptyLoadQuantity;
    }

    /**
     * 获取子运单顺序
     *
     * @return waybill_sequence - 子运单顺序
     */
    public Integer getWaybillSequence() {
        return waybillSequence;
    }

    /**
     * 设置子运单顺序
     *
     * @param waybillSequence 子运单顺序
     */
    public void setWaybillSequence(Integer waybillSequence) {
        this.waybillSequence = waybillSequence;
    }

    /**
     * 获取是否需要监控
     *
     * @return need_monitor - 是否需要监控
     */
    public Byte getNeedMonitor() {
        return needMonitor;
    }

    /**
     * 设置是否需要监控
     *
     * @param needMonitor 是否需要监控
     */
    public void setNeedMonitor(Byte needMonitor) {
        this.needMonitor = needMonitor;
    }

    /**
     * 获取是否可监控
     *
     * @return can_monitor - 是否可监控
     */
    public Byte getCanMonitor() {
        return canMonitor;
    }

    /**
     * 设置是否可监控
     *
     * @param canMonitor 是否可监控
     */
    public void setCanMonitor(Byte canMonitor) {
        this.canMonitor = canMonitor;
    }

    public Byte getPlatformEvaluateFlag() {
        return platformEvaluateFlag;
    }

    public void setPlatformEvaluateFlag(Byte platformEvaluateFlag) {
        this.platformEvaluateFlag = platformEvaluateFlag;
    }

    public Byte getDriverEvaluateFlag() {
        return driverEvaluateFlag;
    }

    public void setDriverEvaluateFlag(Byte driverEvaluateFlag) {
        this.driverEvaluateFlag = driverEvaluateFlag;
    }

    public Byte getDeliveryCertificateFlag() {
        return deliveryCertificateFlag;
    }

    public void setDeliveryCertificateFlag(Byte deliveryCertificateFlag) {
        this.deliveryCertificateFlag = deliveryCertificateFlag;
    }

    public Byte getObjectionFlag() {
        return objectionFlag;
    }

    public void setObjectionFlag(Byte objectionFlag) {
        this.objectionFlag = objectionFlag;
    }

    public Byte getReadyFlag() {
        return readyFlag;
    }

    public void setReadyFlag(Byte readyFlag) {
        this.readyFlag = readyFlag;
    }

    /**
     * 获取协议Id
     *
     * @return deals_id - 协议Id
     */
    public String getDealsId() {
        return dealsId;
    }

    /**
     * 设置协议Id
     *
     * @param dealsId 协议Id
     */
    public void setDealsId(String dealsId) {
        this.dealsId = dealsId == null ? null : dealsId.trim();
    }

    /**
     * 获取协议名称
     *
     * @return deals_name - 协议名称
     */
    public String getDealsName() {
        return dealsName;
    }

    /**
     * 设置协议名称
     *
     * @param dealsName 协议名称
     */
    public void setDealsName(String dealsName) {
        this.dealsName = dealsName == null ? null : dealsName.trim();
    }

    /**
     * 获取项目名称
     *
     * @return project_name - 项目名称
     */
    public String getProjectName() {
        return projectName;
    }

    /**
     * 设置项目名称
     *
     * @param projectName 项目名称
     */
    public void setProjectName(String projectName) {
        this.projectName = projectName == null ? null : projectName.trim();
    }

    /**
     * 获取开票对象类型
     *
     * @return invoice_target_type - 开票对象类型
     */
    public String getInvoiceTargetType() {
        return invoiceTargetType;
    }

    /**
     * 设置开票对象类型
     *
     * @param invoiceTargetType 开票对象类型
     */
    public void setInvoiceTargetType(String invoiceTargetType) {
        this.invoiceTargetType = invoiceTargetType == null ? null : invoiceTargetType.trim();
    }

    /**
     * 获取开票会员ID
     *
     * @return invoice_user_id - 开票会员ID
     */
    public String getInvoiceUserId() {
        return invoiceUserId;
    }

    /**
     * 设置开票会员ID
     *
     * @param invoiceUserId 开票会员ID
     */
    public void setInvoiceUserId(String invoiceUserId) {
        this.invoiceUserId = invoiceUserId == null ? null : invoiceUserId.trim();
    }

    /**
     * 获取开票状态
     *
     * @return invoice_status - 开票状态
     */
    public String getInvoiceStatus() {
        return invoiceStatus;
    }

    /**
     * 设置开票状态
     *
     * @param invoiceStatus 开票状态
     */
    public void setInvoiceStatus(String invoiceStatus) {
        this.invoiceStatus = invoiceStatus == null ? null : invoiceStatus.trim();
    }

    /**
     * 获取开票结果描述
     *
     * @return invoice_message - 开票结果描述
     */
    public String getInvoiceMessage() {
        return invoiceMessage;
    }

    /**
     * 设置开票结果描述
     *
     * @param invoiceMessage 开票结果描述
     */
    public void setInvoiceMessage(String invoiceMessage) {
        this.invoiceMessage = invoiceMessage == null ? null : invoiceMessage.trim();
    }

    /**
     * 获取删除标识
     *
     * @return del_flg - 删除标识
     */
    public Byte getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标识
     *
     * @param delFlg 删除标识
     */
    public void setDelFlg(Byte delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    public String getBillProxyType() {
        return billProxyType;
    }

    public void setBillProxyType(String billProxyType) {
        this.billProxyType = billProxyType == null ? null : billProxyType.trim();
    }

    public Byte getCanOperate() {
        return canOperate;
    }

    public void setCanOperate(Byte canOperate) {
        this.canOperate = canOperate;
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取数据版本
     *
     * @return version - 数据版本
     */
    public Long getVersion() {
        return version;
    }

    /**
     * 设置数据版本
     *
     * @param version 数据版本
     */
    public void setVersion(Long version) {
        this.version = version;
    }

    /**
     * @return close_reason
     */
    public String getCloseReason() {
        return closeReason;
    }

    /**
     * @param closeReason
     */
    public void setCloseReason(String closeReason) {
        this.closeReason = closeReason == null ? null : closeReason.trim();
    }

    /**
     * @return close_time
     */
    public Date getCloseTime() {
        return closeTime;
    }

    /**
     * @param closeTime
     */
    public void setCloseTime(Date closeTime) {
        this.closeTime = closeTime;
    }

    /**
     * 获取同步标识:1未同步 2: 正向同步 3:反向同步
     *
     * @return sync_flag - 同步标识:1未同步 2: 正向同步 3:反向同步
     */
    public String getSyncFlag() {
        return syncFlag;
    }

    /**
     * 设置同步标识:1未同步 2: 正向同步 3:反向同步
     *
     * @param syncFlag 同步标识:1未同步 2: 正向同步 3:反向同步
     */
    public void setSyncFlag(String syncFlag) {
        this.syncFlag = syncFlag == null ? null : syncFlag.trim();
    }

    /**
     * 获取同步失败原因
     *
     * @return sync_fail_reason - 同步失败原因
     */
    public String getSyncFailReason() {
        return syncFailReason;
    }

    /**
     * 设置同步失败原因
     *
     * @param syncFailReason 同步失败原因
     */
    public void setSyncFailReason(String syncFailReason) {
        this.syncFailReason = syncFailReason == null ? null : syncFailReason.trim();
    }

    /**
     * 获取外部运单号
     *
     * @return external_waybill_num - 外部运单号
     */
    public String getExternalWaybillNum() {
        return externalWaybillNum;
    }

    /**
     * 设置外部运单号
     *
     * @param externalWaybillNum 外部运单号
     */
    public void setExternalWaybillNum(String externalWaybillNum) {
        this.externalWaybillNum = externalWaybillNum == null ? null : externalWaybillNum.trim();
    }

    /**
     * 获取外部运单状态
     *
     * @return external_waybill_status - 外部运单状态
     */
    public String getExternalWaybillStatus() {
        return externalWaybillStatus;
    }

    /**
     * 设置外部运单状态
     *
     * @param externalWaybillStatus 外部运单状态
     */
    public void setExternalWaybillStatus(String externalWaybillStatus) {
        this.externalWaybillStatus = externalWaybillStatus == null ? null : externalWaybillStatus.trim();
    }

    /**
     * 获取外部请求号
     *
     * @return external_request_no - 外部请求号
     */
    public String getExternalRequestNo() {
        return externalRequestNo;
    }

    /**
     * 设置外部请求号
     *
     * @param externalRequestNo 外部请求号
     */
    public void setExternalRequestNo(String externalRequestNo) {
        this.externalRequestNo = externalRequestNo == null ? null : externalRequestNo.trim();
    }

    public String getOriginPlace() {
        return originPlace;
    }

    public void setOriginPlace(String originPlace) {
        this.originPlace = originPlace;
    }

    /**
     * 获取进厂时间
     *
     * @return enter_factory_time - 进厂时间
     */
    public Date getEnterFactoryTime() {
        return enterFactoryTime;
    }

    /**
     * 设置进厂时间
     *
     * @param enterFactoryTime 进厂时间
     */
    public void setEnterFactoryTime(Date enterFactoryTime) {
        this.enterFactoryTime = enterFactoryTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", waybillId=").append(waybillId);
        sb.append(", waybillNum=").append(waybillNum);
        sb.append(", dispatchBillId=").append(dispatchBillId);
        sb.append(", carrierId=").append(carrierId);
        sb.append(", carrierType=").append(carrierType);
        sb.append(", pickingBillId=").append(pickingBillId);
        sb.append(", isMainWaybill=").append(isMainWaybill);
        sb.append(", parentId=").append(parentId);
        sb.append(", specialId=").append(specialId);
        sb.append(", type=").append(type);
        sb.append(", status=").append(status);
        sb.append(", qrCode=").append(qrCode);
        sb.append(", endReason=").append(endReason);
        sb.append(", deliveryTime=").append(deliveryTime);
        sb.append(", deliveryTimeRange=").append(deliveryTimeRange);
        sb.append(", pickingTime=").append(pickingTime);
        sb.append(", incomeCarriage=").append(incomeCarriage);
        sb.append(", slaStatus=").append(slaStatus);
        sb.append(", arriveDestinationTime=").append(arriveDestinationTime);
        sb.append(", monitorArriveTime=").append(monitorArriveTime);
        sb.append(", beginCarryTime=").append(beginCarryTime);
        sb.append(", completeTime=").append(completeTime);
        sb.append(", monitorCompleteTime=").append(monitorCompleteTime);
        sb.append(", cancelTime=").append(cancelTime);
        sb.append(", actualQuantity=").append(actualQuantity);
        sb.append(", signQuantity=").append(signQuantity);
        sb.append(", signRemark=").append(signRemark);
        sb.append(", emptyLoadCharge=").append(emptyLoadCharge);
        sb.append(", emptyLoadFlag=").append(emptyLoadFlag);
        sb.append(", emptyLoadQuantity=").append(emptyLoadQuantity);
        sb.append(", waybillSequence=").append(waybillSequence);
        sb.append(", needMonitor=").append(needMonitor);
        sb.append(", canMonitor=").append(canMonitor);
        sb.append(", platformEvaluateFlag=").append(platformEvaluateFlag);
        sb.append(", driverEvaluateFlag=").append(driverEvaluateFlag);
        sb.append(", deliveryCertificateFlag=").append(deliveryCertificateFlag);
        sb.append(", objectionFlag=").append(objectionFlag);
        sb.append(", readyFlag=").append(readyFlag);
        sb.append(", dealsId=").append(dealsId);
        sb.append(", dealsName=").append(dealsName);
        sb.append(", projectName=").append(projectName);
        sb.append(", invoiceTargetType=").append(invoiceTargetType);
        sb.append(", invoiceUserId=").append(invoiceUserId);
        sb.append(", invoiceStatus=").append(invoiceStatus);
        sb.append(", invoiceMessage=").append(invoiceMessage);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", version=").append(version);
        sb.append(", closeReason=").append(closeReason);
        sb.append(", closeTime=").append(closeTime);
        sb.append(", syncFlag=").append(syncFlag);
        sb.append(", syncFailReason=").append(syncFailReason);
        sb.append(", externalWaybillNum=").append(externalWaybillNum);
        sb.append(", externalWaybillStatus=").append(externalWaybillStatus);
        sb.append(", externalRequestNo=").append(externalRequestNo);
        sb.append(", originPlace=").append(originPlace);
        sb.append(", enterFactoryTime=").append(enterFactoryTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}