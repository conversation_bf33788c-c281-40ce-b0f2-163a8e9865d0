package com.ecommerce.logistics.dao.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;

@Table(name = "lgs_waybill_amt")
public class WaybillAmt implements Serializable {
    /**
     * 运单金额信息主键
     */
    @Id
    @Column(name = "waybill_amt_id")
    private String waybillAmtId;

    /**
     * 运单ID
     */
    @Column(name = "waybill_id")
    private String waybillId;

    /**
     * 主运单ID
     */
    @Column(name = "main_waybill_id")
    private String mainWaybillId;

    /**
     * 运单号
     */
    @Column(name = "waybill_num")
    private String waybillNum;

    /**
     * 卖家ID
     */
    @Column(name = "seller_id")
    private String sellerId;

    /**
     * 卖家名称
     */
    @Column(name = "seller_name")
    private String sellerName;

    /**
     * 买家ID
     */
    @Column(name = "buyer_id")
    private String buyerId;

    /**
     * 买家名称
     */
    @Column(name = "buyer_name")
    private String buyerName;

    /**
     * 承运人ID
     */
    @Column(name = "carrier_id")
    private String carrierId;

    /**
     * 承运人名
     */
    @Column(name = "carrier_name")
    private String carrierName;

    /**
     * 数量
     */
    @Column(name = "quantity")
    private BigDecimal quantity;

    /**
     * 商品单价
     */
    @Column(name = "goods_price")
    private BigDecimal goodsPrice;

    /**
     * 商品货款
     */
    @Column(name = "goods_amount")
    private BigDecimal goodsAmount;

    /**
     * 应收运费单价
     */
    @Column(name = "receivable_carriage_price")
    private BigDecimal receivableCarriagePrice;

    /**
     * 应收运费总额
     */
    @Column(name = "receivable_carriage_amount")
    private BigDecimal receivableCarriageAmount;

    /**
     * 应付运费总额
     */
    @Column(name = "payable_carriage_amount")
    private BigDecimal payableCarriageAmount;

    /**
     * 删除标识
     */
    @Column(name = "del_flg")
    private Byte delFlg;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 数据版本
     */
    private Long version;

    private static final long serialVersionUID = 1L;

    /**
     * 获取运单金额信息主键
     *
     * @return waybill_amt_id - 运单金额信息主键
     */
    public String getWaybillAmtId() {
        return waybillAmtId;
    }

    /**
     * 设置运单金额信息主键
     *
     * @param waybillAmtId 运单金额信息主键
     */
    public void setWaybillAmtId(String waybillAmtId) {
        this.waybillAmtId = waybillAmtId == null ? null : waybillAmtId.trim();
    }

    /**
     * 获取运单ID
     *
     * @return waybill_id - 运单ID
     */
    public String getWaybillId() {
        return waybillId;
    }

    /**
     * 设置运单ID
     *
     * @param waybillId 运单ID
     */
    public void setWaybillId(String waybillId) {
        this.waybillId = waybillId == null ? null : waybillId.trim();
    }

    /**
     * 获取主运单ID
     *
     * @return main_waybill_id - 主运单ID
     */
    public String getMainWaybillId() {
        return mainWaybillId;
    }

    /**
     * 设置主运单ID
     *
     * @param mainWaybillId 主运单ID
     */
    public void setMainWaybillId(String mainWaybillId) {
        this.mainWaybillId = mainWaybillId == null ? null : mainWaybillId.trim();
    }

    /**
     * 获取运单号
     *
     * @return waybill_num - 运单号
     */
    public String getWaybillNum() {
        return waybillNum;
    }

    /**
     * 设置运单号
     *
     * @param waybillNum 运单号
     */
    public void setWaybillNum(String waybillNum) {
        this.waybillNum = waybillNum == null ? null : waybillNum.trim();
    }

    /**
     * 获取卖家ID
     *
     * @return seller_id - 卖家ID
     */
    public String getSellerId() {
        return sellerId;
    }

    /**
     * 设置卖家ID
     *
     * @param sellerId 卖家ID
     */
    public void setSellerId(String sellerId) {
        this.sellerId = sellerId == null ? null : sellerId.trim();
    }

    /**
     * 获取卖家名称
     *
     * @return seller_name - 卖家名称
     */
    public String getSellerName() {
        return sellerName;
    }

    /**
     * 设置卖家名称
     *
     * @param sellerName 卖家名称
     */
    public void setSellerName(String sellerName) {
        this.sellerName = sellerName == null ? null : sellerName.trim();
    }

    /**
     * 获取买家ID
     *
     * @return buyer_id - 买家ID
     */
    public String getBuyerId() {
        return buyerId;
    }

    /**
     * 设置买家ID
     *
     * @param buyerId 买家ID
     */
    public void setBuyerId(String buyerId) {
        this.buyerId = buyerId == null ? null : buyerId.trim();
    }

    /**
     * 获取买家名称
     *
     * @return buyer_name - 买家名称
     */
    public String getBuyerName() {
        return buyerName;
    }

    /**
     * 设置买家名称
     *
     * @param buyerName 买家名称
     */
    public void setBuyerName(String buyerName) {
        this.buyerName = buyerName == null ? null : buyerName.trim();
    }

    /**
     * 获取承运人ID
     *
     * @return carrier_id - 承运人ID
     */
    public String getCarrierId() {
        return carrierId;
    }

    /**
     * 设置承运人ID
     *
     * @param carrierId 承运人ID
     */
    public void setCarrierId(String carrierId) {
        this.carrierId = carrierId == null ? null : carrierId.trim();
    }

    /**
     * 获取承运人名
     *
     * @return carrier_name - 承运人名
     */
    public String getCarrierName() {
        return carrierName;
    }

    /**
     * 设置承运人名
     *
     * @param carrierName 承运人名
     */
    public void setCarrierName(String carrierName) {
        this.carrierName = carrierName == null ? null : carrierName.trim();
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    /**
     * 获取商品单价
     *
     * @return goods_price - 商品单价
     */
    public BigDecimal getGoodsPrice() {
        return goodsPrice;
    }

    /**
     * 设置商品单价
     *
     * @param goodsPrice 商品单价
     */
    public void setGoodsPrice(BigDecimal goodsPrice) {
        this.goodsPrice = goodsPrice;
    }

    /**
     * 获取商品货款
     *
     * @return goods_amount - 商品货款
     */
    public BigDecimal getGoodsAmount() {
        return goodsAmount;
    }

    /**
     * 设置商品货款
     *
     * @param goodsAmount 商品货款
     */
    public void setGoodsAmount(BigDecimal goodsAmount) {
        this.goodsAmount = goodsAmount;
    }

    /**
     * 获取应收运费单价
     *
     * @return receivable_carriage_price - 应收运费单价
     */
    public BigDecimal getReceivableCarriagePrice() {
        return receivableCarriagePrice;
    }

    /**
     * 设置应收运费单价
     *
     * @param receivableCarriagePrice 应收运费单价
     */
    public void setReceivableCarriagePrice(BigDecimal receivableCarriagePrice) {
        this.receivableCarriagePrice = receivableCarriagePrice;
    }

    /**
     * 获取应收运费总额
     *
     * @return receivable_carriage_amount - 应收运费总额
     */
    public BigDecimal getReceivableCarriageAmount() {
        return receivableCarriageAmount;
    }

    /**
     * 设置应收运费总额
     *
     * @param receivableCarriageAmount 应收运费总额
     */
    public void setReceivableCarriageAmount(BigDecimal receivableCarriageAmount) {
        this.receivableCarriageAmount = receivableCarriageAmount;
    }

    /**
     * 获取应付运费总额
     *
     * @return payable_carriage_amount - 应付运费总额
     */
    public BigDecimal getPayableCarriageAmount() {
        return payableCarriageAmount;
    }

    /**
     * 设置应付运费总额
     *
     * @param payableCarriageAmount 应付运费总额
     */
    public void setPayableCarriageAmount(BigDecimal payableCarriageAmount) {
        this.payableCarriageAmount = payableCarriageAmount;
    }

    /**
     * 获取删除标识
     *
     * @return del_flg - 删除标识
     */
    public Byte getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标识
     *
     * @param delFlg 删除标识
     */
    public void setDelFlg(Byte delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取数据版本
     *
     * @return version - 数据版本
     */
    public Long getVersion() {
        return version;
    }

    /**
     * 设置数据版本
     *
     * @param version 数据版本
     */
    public void setVersion(Long version) {
        this.version = version;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", waybillAmtId=").append(waybillAmtId);
        sb.append(", waybillId=").append(waybillId);
        sb.append(", mainWaybillId=").append(mainWaybillId);
        sb.append(", waybillNum=").append(waybillNum);
        sb.append(", sellerId=").append(sellerId);
        sb.append(", sellerName=").append(sellerName);
        sb.append(", buyerId=").append(buyerId);
        sb.append(", buyerName=").append(buyerName);
        sb.append(", carrierId=").append(carrierId);
        sb.append(", carrierName=").append(carrierName);
        sb.append(", quantity=").append(quantity);
        sb.append(", goodsPrice=").append(goodsPrice);
        sb.append(", goodsAmount=").append(goodsAmount);
        sb.append(", receivableCarriagePrice=").append(receivableCarriagePrice);
        sb.append(", receivableCarriageAmount=").append(receivableCarriageAmount);
        sb.append(", payableCarriageAmount=").append(payableCarriageAmount);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", version=").append(version);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}