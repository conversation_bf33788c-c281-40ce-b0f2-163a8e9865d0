package com.ecommerce.logistics.dao.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;

@Table(name = "lgs_waybill_info")
public class WaybillInfo implements Serializable {
    /**
     * 运单信息ID
     */
    @Id
    @Column(name = "waybill_info_id")
    private String waybillInfoId;

    /**
     * 运单ID
     */
    @Column(name = "waybill_id")
    private String waybillId;

    /**
     * 司机ID
     */
    @Column(name = "driver_id")
    private String driverId;

    /**
     * 司机姓名
     */
    @Column(name = "driver_name")
    private String driverName;

    /**
     * 司机电话
     */
    @Column(name = "driver_phone")
    private String driverPhone;

    /**
     * 配送车辆
     */
    @Column(name = "vehicle_num")
    private String vehicleNum;

    /**
     * 预估配送里程
     */
    @Column(name = "estimate_km")
    private BigDecimal estimateKm;

    /**
     * 实际配送里程
     */
    @Column(name = "actual_km")
    private BigDecimal actualKm;

    /**
     * 预估配送时长
     */
    @Column(name = "estimate_duration")
    private BigDecimal estimateDuration;

    /**
     * 预估运费
     */
    @Column(name = "estimate_carriage")
    private BigDecimal estimateCarriage;

    /**
     * 发布运费
     */
    @Column(name = "published_carriage")
    private BigDecimal publishedCarriage;

    /**
     * 审核通过时间
     */
    @Column(name = "approval_time")
    private Date approvalTime;

    @Column(name = "receive_time")
    private Date receiveTime;

    /**
     * 指派时间
     */
    @Column(name = "assign_time")
    private Date assignTime;

    /**
     * 进站时间
     */
    @Column(name = "arrive_warehouse_time")
    private Date arriveWarehouseTime;

    /**
     * 监控进站时间
     */
    @Column(name = "monitor_enter_time")
    private Date monitorEnterTime;

    /**
     * 出站时间
     */
    @Column(name = "leave_warehouse_time")
    private Date leaveWarehouseTime;

    /**
     * 出厂数量
     */
    @Column(name = "actual_quantity")
    private BigDecimal actualQuantity;

    /**
     * 监控出站时间
     */
    @Column(name = "monitor_outer_time")
    private Date monitorOuterTime;

    /**
     * 取消原因
     */
    @Column(name = "cancel_reason")
    private String cancelReason;

    /**
     * 删除标识
     */
    @Column(name = "del_flg")
    private Byte delFlg;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 数据版本
     */
    private Long version;

    /**
     * 实际运费
     */
    @Column(name = "actual_carriage")
    private BigDecimal actualCarriage;

    private static final long serialVersionUID = 1L;

    /**
     * 获取运单信息ID
     *
     * @return waybill_info_id - 运单信息ID
     */
    public String getWaybillInfoId() {
        return waybillInfoId;
    }

    /**
     * 设置运单信息ID
     *
     * @param waybillInfoId 运单信息ID
     */
    public void setWaybillInfoId(String waybillInfoId) {
        this.waybillInfoId = waybillInfoId == null ? null : waybillInfoId.trim();
    }

    /**
     * 获取运单ID
     *
     * @return waybill_id - 运单ID
     */
    public String getWaybillId() {
        return waybillId;
    }

    /**
     * 设置运单ID
     *
     * @param waybillId 运单ID
     */
    public void setWaybillId(String waybillId) {
        this.waybillId = waybillId == null ? null : waybillId.trim();
    }

    /**
     * 获取司机ID
     *
     * @return driver_id - 司机ID
     */
    public String getDriverId() {
        return driverId;
    }

    /**
     * 设置司机ID
     *
     * @param driverId 司机ID
     */
    public void setDriverId(String driverId) {
        this.driverId = driverId == null ? null : driverId.trim();
    }

    /**
     * 获取司机姓名
     *
     * @return driver_name - 司机姓名
     */
    public String getDriverName() {
        return driverName;
    }

    /**
     * 设置司机姓名
     *
     * @param driverName 司机姓名
     */
    public void setDriverName(String driverName) {
        this.driverName = driverName == null ? null : driverName.trim();
    }

    /**
     * 获取司机电话
     *
     * @return driver_phone - 司机电话
     */
    public String getDriverPhone() {
        return driverPhone;
    }

    /**
     * 设置司机电话
     *
     * @param driverPhone 司机电话
     */
    public void setDriverPhone(String driverPhone) {
        this.driverPhone = driverPhone == null ? null : driverPhone.trim();
    }

    /**
     * 获取配送车辆
     *
     * @return vehicle_num - 配送车辆
     */
    public String getVehicleNum() {
        return vehicleNum;
    }

    /**
     * 设置配送车辆
     *
     * @param vehicleNum 配送车辆
     */
    public void setVehicleNum(String vehicleNum) {
        this.vehicleNum = vehicleNum == null ? null : vehicleNum.trim();
    }

    /**
     * 获取预估配送里程
     *
     * @return estimate_km - 预估配送里程
     */
    public BigDecimal getEstimateKm() {
        return estimateKm;
    }

    /**
     * 设置预估配送里程
     *
     * @param estimateKm 预估配送里程
     */
    public void setEstimateKm(BigDecimal estimateKm) {
        this.estimateKm = estimateKm;
    }

    /**
     * 获取实际配送里程
     *
     * @return actual_km - 实际配送里程
     */
    public BigDecimal getActualKm() {
        return actualKm;
    }

    /**
     * 设置实际配送里程
     *
     * @param actualKm 实际配送里程
     */
    public void setActualKm(BigDecimal actualKm) {
        this.actualKm = actualKm;
    }

    /**
     * 获取预估配送时长
     *
     * @return estimate_duration - 预估配送时长
     */
    public BigDecimal getEstimateDuration() {
        return estimateDuration;
    }

    /**
     * 设置预估配送时长
     *
     * @param estimateDuration 预估配送时长
     */
    public void setEstimateDuration(BigDecimal estimateDuration) {
        this.estimateDuration = estimateDuration;
    }

    /**
     * 获取预估运费
     *
     * @return estimate_carriage - 预估运费
     */
    public BigDecimal getEstimateCarriage() {
        return estimateCarriage;
    }

    /**
     * 设置预估运费
     *
     * @param estimateCarriage 预估运费
     */
    public void setEstimateCarriage(BigDecimal estimateCarriage) {
        this.estimateCarriage = estimateCarriage;
    }

    /**
     * 获取发布运费
     *
     * @return published_carriage - 发布运费
     */
    public BigDecimal getPublishedCarriage() {
        return publishedCarriage;
    }

    /**
     * 设置发布运费
     *
     * @param publishedCarriage 发布运费
     */
    public void setPublishedCarriage(BigDecimal publishedCarriage) {
        this.publishedCarriage = publishedCarriage;
    }

    /**
     * 获取审核通过时间
     *
     * @return approval_time - 审核通过时间
     */
    public Date getApprovalTime() {
        return approvalTime;
    }

    /**
     * 设置审核通过时间
     *
     * @param approvalTime 审核通过时间
     */
    public void setApprovalTime(Date approvalTime) {
        this.approvalTime = approvalTime;
    }

    /**
     * @return receive_time
     */
    public Date getReceiveTime() {
        return receiveTime;
    }

    /**
     * @param receiveTime
     */
    public void setReceiveTime(Date receiveTime) {
        this.receiveTime = receiveTime;
    }

    /**
     * 获取指派时间
     *
     * @return assign_time - 指派时间
     */
    public Date getAssignTime() {
        return assignTime;
    }

    /**
     * 设置指派时间
     *
     * @param assignTime 指派时间
     */
    public void setAssignTime(Date assignTime) {
        this.assignTime = assignTime;
    }

    /**
     * 获取进站时间
     *
     * @return arrive_warehouse_time - 进站时间
     */
    public Date getArriveWarehouseTime() {
        return arriveWarehouseTime;
    }

    /**
     * 设置进站时间
     *
     * @param arriveWarehouseTime 进站时间
     */
    public void setArriveWarehouseTime(Date arriveWarehouseTime) {
        this.arriveWarehouseTime = arriveWarehouseTime;
    }

    /**
     * 获取监控进站时间
     *
     * @return monitor_enter_time - 监控进站时间
     */
    public Date getMonitorEnterTime() {
        return monitorEnterTime;
    }

    /**
     * 设置监控进站时间
     *
     * @param monitorEnterTime 监控进站时间
     */
    public void setMonitorEnterTime(Date monitorEnterTime) {
        this.monitorEnterTime = monitorEnterTime;
    }

    /**
     * 获取出站时间
     *
     * @return leave_warehouse_time - 出站时间
     */
    public Date getLeaveWarehouseTime() {
        return leaveWarehouseTime;
    }

    /**
     * 设置出站时间
     *
     * @param leaveWarehouseTime 出站时间
     */
    public void setLeaveWarehouseTime(Date leaveWarehouseTime) {
        this.leaveWarehouseTime = leaveWarehouseTime;
    }

    /**
     * 获取出厂数量
     *
     * @return actual_quantity - 出厂数量
     */
    public BigDecimal getActualQuantity() {
        return actualQuantity;
    }

    /**
     * 设置出厂数量
     *
     * @param actualQuantity 出厂数量
     */
    public void setActualQuantity(BigDecimal actualQuantity) {
        this.actualQuantity = actualQuantity;
    }

    /**
     * 获取监控出站时间
     *
     * @return monitor_outer_time - 监控出站时间
     */
    public Date getMonitorOuterTime() {
        return monitorOuterTime;
    }

    /**
     * 设置监控出站时间
     *
     * @param monitorOuterTime 监控出站时间
     */
    public void setMonitorOuterTime(Date monitorOuterTime) {
        this.monitorOuterTime = monitorOuterTime;
    }

    /**
     * 获取取消原因
     *
     * @return cancel_reason - 取消原因
     */
    public String getCancelReason() {
        return cancelReason;
    }

    /**
     * 设置取消原因
     *
     * @param cancelReason 取消原因
     */
    public void setCancelReason(String cancelReason) {
        this.cancelReason = cancelReason == null ? null : cancelReason.trim();
    }

    /**
     * 获取删除标识
     *
     * @return del_flg - 删除标识
     */
    public Byte getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标识
     *
     * @param delFlg 删除标识
     */
    public void setDelFlg(Byte delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取数据版本
     *
     * @return version - 数据版本
     */
    public Long getVersion() {
        return version;
    }

    /**
     * 设置数据版本
     *
     * @param version 数据版本
     */
    public void setVersion(Long version) {
        this.version = version;
    }

    /**
     * 获取实际运费
     *
     * @return actual_carriage - 实际运费
     */
    public BigDecimal getActualCarriage() {
        return actualCarriage;
    }

    /**
     * 设置实际运费
     *
     * @param actualCarriage 实际运费
     */
    public void setActualCarriage(BigDecimal actualCarriage) {
        this.actualCarriage = actualCarriage;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", waybillInfoId=").append(waybillInfoId);
        sb.append(", waybillId=").append(waybillId);
        sb.append(", driverId=").append(driverId);
        sb.append(", driverName=").append(driverName);
        sb.append(", driverPhone=").append(driverPhone);
        sb.append(", vehicleNum=").append(vehicleNum);
        sb.append(", estimateKm=").append(estimateKm);
        sb.append(", actualKm=").append(actualKm);
        sb.append(", estimateDuration=").append(estimateDuration);
        sb.append(", estimateCarriage=").append(estimateCarriage);
        sb.append(", publishedCarriage=").append(publishedCarriage);
        sb.append(", approvalTime=").append(approvalTime);
        sb.append(", receiveTime=").append(receiveTime);
        sb.append(", assignTime=").append(assignTime);
        sb.append(", arriveWarehouseTime=").append(arriveWarehouseTime);
        sb.append(", monitorEnterTime=").append(monitorEnterTime);
        sb.append(", leaveWarehouseTime=").append(leaveWarehouseTime);
        sb.append(", actualQuantity=").append(actualQuantity);
        sb.append(", monitorOuterTime=").append(monitorOuterTime);
        sb.append(", cancelReason=").append(cancelReason);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", version=").append(version);
        sb.append(", actualCarriage=").append(actualCarriage);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}