package com.ecommerce.logistics.dao.vo;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

@Table(name = "lgs_waybill_map")
public class WaybillMap implements Serializable {
    /**
     * 运单映射ID
     */
    @Id
    @Column(name = "waybill_map_id")
    private String waybillMapId;

    /**
     * 一级运单ID
     */
    @Column(name = "primary_waybill_id")
    private String primaryWaybillId;

    /**
     * 一级运单号
     */
    @Column(name = "primary_waybill_num")
    private String primaryWaybillNum;

    /**
     * 一级发货单号
     */
    @Column(name = "primary_delivery_sheet_num")
    private String primaryDeliverySheetNum;

    /**
     * 二级运单ID
     */
    @Column(name = "secondary_waybill_id")
    private String secondaryWaybillId;

    /**
     * 二级运单号
     */
    @Column(name = "secondary_waybill_num")
    private String secondaryWaybillNum;

    /**
     * 二级发货单号
     */
    @Column(name = "secondary_delivery_sheet_num")
    private String secondaryDeliverySheetNum;

    /**
     * 删除标识
     */
    @Column(name = "del_flg")
    private Byte delFlg;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 数据版本
     */
    private Long version;

    private static final long serialVersionUID = 1L;

    /**
     * 获取运单映射ID
     *
     * @return waybill_map_id - 运单映射ID
     */
    public String getWaybillMapId() {
        return waybillMapId;
    }

    /**
     * 设置运单映射ID
     *
     * @param waybillMapId 运单映射ID
     */
    public void setWaybillMapId(String waybillMapId) {
        this.waybillMapId = waybillMapId == null ? null : waybillMapId.trim();
    }

    /**
     * 获取一级运单ID
     *
     * @return primary_waybill_id - 一级运单ID
     */
    public String getPrimaryWaybillId() {
        return primaryWaybillId;
    }

    /**
     * 设置一级运单ID
     *
     * @param primaryWaybillId 一级运单ID
     */
    public void setPrimaryWaybillId(String primaryWaybillId) {
        this.primaryWaybillId = primaryWaybillId == null ? null : primaryWaybillId.trim();
    }

    /**
     * 获取一级运单号
     *
     * @return primary_waybill_num - 一级运单号
     */
    public String getPrimaryWaybillNum() {
        return primaryWaybillNum;
    }

    /**
     * 设置一级运单号
     *
     * @param primaryWaybillNum 一级运单号
     */
    public void setPrimaryWaybillNum(String primaryWaybillNum) {
        this.primaryWaybillNum = primaryWaybillNum == null ? null : primaryWaybillNum.trim();
    }

    /**
     * 获取一级发货单号
     *
     * @return primary_delivery_sheet_num - 一级发货单号
     */
    public String getPrimaryDeliverySheetNum() {
        return primaryDeliverySheetNum;
    }

    /**
     * 设置一级发货单号
     *
     * @param primaryDeliverySheetNum 一级发货单号
     */
    public void setPrimaryDeliverySheetNum(String primaryDeliverySheetNum) {
        this.primaryDeliverySheetNum = primaryDeliverySheetNum == null ? null : primaryDeliverySheetNum.trim();
    }

    /**
     * 获取二级运单ID
     *
     * @return secondary_waybill_id - 二级运单ID
     */
    public String getSecondaryWaybillId() {
        return secondaryWaybillId;
    }

    /**
     * 设置二级运单ID
     *
     * @param secondaryWaybillId 二级运单ID
     */
    public void setSecondaryWaybillId(String secondaryWaybillId) {
        this.secondaryWaybillId = secondaryWaybillId == null ? null : secondaryWaybillId.trim();
    }

    /**
     * 获取二级运单号
     *
     * @return secondary_waybill_num - 二级运单号
     */
    public String getSecondaryWaybillNum() {
        return secondaryWaybillNum;
    }

    /**
     * 设置二级运单号
     *
     * @param secondaryWaybillNum 二级运单号
     */
    public void setSecondaryWaybillNum(String secondaryWaybillNum) {
        this.secondaryWaybillNum = secondaryWaybillNum == null ? null : secondaryWaybillNum.trim();
    }

    /**
     * 获取二级发货单号
     *
     * @return secondary_delivery_sheet_num - 二级发货单号
     */
    public String getSecondaryDeliverySheetNum() {
        return secondaryDeliverySheetNum;
    }

    /**
     * 设置二级发货单号
     *
     * @param secondaryDeliverySheetNum 二级发货单号
     */
    public void setSecondaryDeliverySheetNum(String secondaryDeliverySheetNum) {
        this.secondaryDeliverySheetNum = secondaryDeliverySheetNum == null ? null : secondaryDeliverySheetNum.trim();
    }

    /**
     * 获取删除标识
     *
     * @return del_flg - 删除标识
     */
    public Byte getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标识
     *
     * @param delFlg 删除标识
     */
    public void setDelFlg(Byte delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取数据版本
     *
     * @return version - 数据版本
     */
    public Long getVersion() {
        return version;
    }

    /**
     * 设置数据版本
     *
     * @param version 数据版本
     */
    public void setVersion(Long version) {
        this.version = version;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", waybillMapId=").append(waybillMapId);
        sb.append(", primaryWaybillId=").append(primaryWaybillId);
        sb.append(", primaryWaybillNum=").append(primaryWaybillNum);
        sb.append(", primaryDeliverySheetNum=").append(primaryDeliverySheetNum);
        sb.append(", secondaryWaybillId=").append(secondaryWaybillId);
        sb.append(", secondaryWaybillNum=").append(secondaryWaybillNum);
        sb.append(", secondaryDeliverySheetNum=").append(secondaryDeliverySheetNum);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", version=").append(version);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}