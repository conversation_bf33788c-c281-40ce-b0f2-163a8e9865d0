package com.ecommerce.logistics.dao.vo;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

@Table(name = "lgs_waybill_seckill")
public class WaybillSeckill implements Serializable {
    /**
	 * 表PK
	 */
	@Id
	@Column(name = "waybill_seckill_id")
	private String waybillSeckillId;

	/**
	 * 运单ID
	 */
	@Column(name = "waybill_id")
	private String waybillId;

	/**
	 * 用户ID
	 */
	@Column(name = "user_id")
	private String userId;

	/**
	 * 0代表未删除,1代表已删除
	 */
	@Column(name = "del_flg")
	private Byte delFlg;

	/**
	 * 创建用户
	 */
	@Column(name = "create_user")
	private String createUser;

	/**
	 * 创建时间
	 */
	@Column(name = "create_time")
	private Date createTime;

	/**
	 * 更新用户
	 */
	@Column(name = "update_user")
	private String updateUser;

	/**
	 * 更新时间
	 */
	@Column(name = "update_time")
	private Date updateTime;

	/**
	 * 数据版本
	 */
	private Long version;

	private static final long serialVersionUID = 1L;

	/**
	 * 获取表PK
	 * @return  waybill_seckill_id - 表PK
	 */
	public String getWaybillSeckillId() {
		return waybillSeckillId;
	}

	/**
	 * 设置表PK
	 * @param waybillSeckillId  表PK
	 */
	public void setWaybillSeckillId(String waybillSeckillId) {
		this.waybillSeckillId = waybillSeckillId == null ? null : waybillSeckillId.trim();
	}

	/**
	 * 获取运单ID
	 * @return  waybill_id - 运单ID
	 */
	public String getWaybillId() {
		return waybillId;
	}

	/**
	 * 设置运单ID
	 * @param waybillId  运单ID
	 */
	public void setWaybillId(String waybillId) {
		this.waybillId = waybillId == null ? null : waybillId.trim();
	}

	/**
	 * 获取用户ID
	 * @return  user_id - 用户ID
	 */
	public String getUserId() {
		return userId;
	}

	/**
	 * 设置用户ID
	 * @param userId  用户ID
	 */
	public void setUserId(String userId) {
		this.userId = userId == null ? null : userId.trim();
	}

	/**
	 * 获取0代表未删除,1代表已删除
	 * @return  del_flg - 0代表未删除,1代表已删除
	 */
	public Byte getDelFlg() {
		return delFlg;
	}

	/**
	 * 设置0代表未删除,1代表已删除
	 * @param delFlg  0代表未删除,1代表已删除
	 */
	public void setDelFlg(Byte delFlg) {
		this.delFlg = delFlg;
	}

	/**
	 * 获取创建用户
	 * @return  create_user - 创建用户
	 */
	public String getCreateUser() {
		return createUser;
	}

	/**
	 * 设置创建用户
	 * @param createUser  创建用户
	 */
	public void setCreateUser(String createUser) {
		this.createUser = createUser == null ? null : createUser.trim();
	}

	/**
	 * 获取创建时间
	 * @return  create_time - 创建时间
	 */
	public Date getCreateTime() {
		return createTime;
	}

	/**
	 * 设置创建时间
	 * @param createTime  创建时间
	 */
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	/**
	 * 获取更新用户
	 * @return  update_user - 更新用户
	 */
	public String getUpdateUser() {
		return updateUser;
	}

	/**
	 * 设置更新用户
	 * @param updateUser  更新用户
	 */
	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser == null ? null : updateUser.trim();
	}

	/**
	 * 获取更新时间
	 * @return  update_time - 更新时间
	 */
	public Date getUpdateTime() {
		return updateTime;
	}

	/**
	 * 设置更新时间
	 * @param updateTime  更新时间
	 */
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	/**
	 * 获取数据版本
	 * @return  version - 数据版本
	 */
	public Long getVersion() {
		return version;
	}

	/**
	 * 设置数据版本
	 * @param version  数据版本
	 */
	public void setVersion(Long version) {
		this.version = version;
	}

	@Override
	public String toString() {
		StringBuilder sb = new StringBuilder();
		sb.append(getClass().getSimpleName());
		sb.append(" [");
		sb.append("Hash = ").append(hashCode());
		sb.append(", waybillSeckillId=").append(waybillSeckillId);
		sb.append(", waybillId=").append(waybillId);
		sb.append(", userId=").append(userId);
		sb.append(", delFlg=").append(delFlg);
		sb.append(", createUser=").append(createUser);
		sb.append(", createTime=").append(createTime);
		sb.append(", updateUser=").append(updateUser);
		sb.append(", updateTime=").append(updateTime);
		sb.append(", version=").append(version);
		sb.append(", serialVersionUID=").append(serialVersionUID);
		sb.append("]");
		return sb.toString();
	}

}