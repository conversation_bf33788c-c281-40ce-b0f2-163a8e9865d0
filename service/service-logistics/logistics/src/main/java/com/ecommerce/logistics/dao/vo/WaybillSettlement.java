package com.ecommerce.logistics.dao.vo;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

@Table(name = "lgs_waybill_settlement")
public class WaybillSettlement implements Serializable {
    @Id
    @Column(name = "waybill_settlement_id")
    private String waybillSettlementId;

    /**
     * 运单ID
     */
    @Column(name = "waybill_id")
    private String waybillId;

    /**
     * 结算状态 1：未结算 2：已结算
     */
    private Byte status;

    /**
     * 结算月份
     */
    @Column(name = "settlement_month")
    private String settlementMonth;

    /**
     * 结算日期
     */
    @Column(name = "settlement_date")
    private String settlementDate;

    /**
     * 结算时间
     */
    @Column(name = "settlement_time")
    private Date settlementTime;

    /**
     * 删除标识
     */
    @Column(name = "del_flg")
    private Byte delFlg;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 数据版本
     */
    private Long version;

    /**
     * 承运商ID
     */
    @Column(name = "carrier_id")
    private String carrierId;

    private static final long serialVersionUID = 1L;

    /**
     * @return waybill_settlement_id
     */
    public String getWaybillSettlementId() {
        return waybillSettlementId;
    }

    /**
     * @param waybillSettlementId
     */
    public void setWaybillSettlementId(String waybillSettlementId) {
        this.waybillSettlementId = waybillSettlementId == null ? null : waybillSettlementId.trim();
    }

    /**
     * 获取运单ID
     *
     * @return waybill_id - 运单ID
     */
    public String getWaybillId() {
        return waybillId;
    }

    /**
     * 设置运单ID
     *
     * @param waybillId 运单ID
     */
    public void setWaybillId(String waybillId) {
        this.waybillId = waybillId == null ? null : waybillId.trim();
    }

    /**
     * 获取结算状态 1：未结算 2：已结算
     *
     * @return status - 结算状态 1：未结算 2：已结算
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * 设置结算状态 1：未结算 2：已结算
     *
     * @param status 结算状态 1：未结算 2：已结算
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * 获取结算月份
     *
     * @return settlement_month - 结算月份
     */
    public String getSettlementMonth() {
        return settlementMonth;
    }

    /**
     * 设置结算月份
     *
     * @param settlementMonth 结算月份
     */
    public void setSettlementMonth(String settlementMonth) {
        this.settlementMonth = settlementMonth == null ? null : settlementMonth.trim();
    }

    /**
     * 获取结算日期
     *
     * @return settlement_date - 结算日期
     */
    public String getSettlementDate() {
        return settlementDate;
    }

    /**
     * 设置结算日期
     *
     * @param settlementDate 结算日期
     */
    public void setSettlementDate(String settlementDate) {
        this.settlementDate = settlementDate;
    }

    /**
     * 获取结算时间
     *
     * @return settlement_time - 结算时间
     */
    public Date getSettlementTime() {
        return settlementTime;
    }

    /**
     * 设置结算时间
     *
     * @param settlementTime 结算时间
     */
    public void setSettlementTime(Date settlementTime) {
        this.settlementTime = settlementTime;
    }

    /**
     * 获取删除标识
     *
     * @return del_flg - 删除标识
     */
    public Byte getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标识
     *
     * @param delFlg 删除标识
     */
    public void setDelFlg(Byte delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取数据版本
     *
     * @return version - 数据版本
     */
    public Long getVersion() {
        return version;
    }

    /**
     * 设置数据版本
     *
     * @param version 数据版本
     */
    public void setVersion(Long version) {
        this.version = version;
    }

    /**
     * 获取承运商ID
     *
     * @return carrier_id - 承运商ID
     */
    public String getCarrierId() {
        return carrierId;
    }

    /**
     * 设置承运商ID
     *
     * @param carrierId 承运商ID
     */
    public void setCarrierId(String carrierId) {
        this.carrierId = carrierId == null ? null : carrierId.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", waybillSettlementId=").append(waybillSettlementId);
        sb.append(", waybillId=").append(waybillId);
        sb.append(", status=").append(status);
        sb.append(", settlementMonth=").append(settlementMonth);
        sb.append(", settlementDate=").append(settlementDate);
        sb.append(", settlementTime=").append(settlementTime);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", version=").append(version);
        sb.append(", carrierId=").append(carrierId);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}