package com.ecommerce.logistics.innit;

import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

/**
 * 该类实现CommandLineRunner接口用于项目启动时bean初始化完成以后执行run方法完成一些数据初始化动作
 * @Auther: <EMAIL>
 * @Date: 2018年8月22日 上午11:31:06
 * @Description:
 */
@Slf4j
@Component
public class InnitCacheRunner implements CommandLineRunner{@Override
	
	public void run(String... args) throws Exception {
		// sonar 添加备注
		
	}
	

}
