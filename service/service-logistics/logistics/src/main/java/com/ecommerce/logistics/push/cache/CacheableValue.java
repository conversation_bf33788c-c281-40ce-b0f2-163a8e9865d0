package com.ecommerce.logistics.push.cache;

/**
 * 缓存的value值(即哪个缓存)
 * @Auther: <EMAIL>
 * @Date: 2018年8月25日 下午9:57:53
 * @Description:
 */
public class CacheableValue {

	private CacheableValue(){
	    throw new IllegalStateException("CacheableValue class");
	}
	
	/**
	 * 商品信息表商品描述缓存
	 */
	public static final String PRODUCT_INFO_DESC = "product_info_desc";
	
	/**
	 * 商品信息表商品信息缓存
	 */
	public static final String PRODUCT_INFO_UNIT = "product_info_unit";
	
	/**
	 * 仓库名称
	 */
	public static final String WAREHOUSE_NAME = "warehouse_name";
	
	

}
