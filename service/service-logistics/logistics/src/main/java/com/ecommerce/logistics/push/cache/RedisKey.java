package com.ecommerce.logistics.push.cache;

public class RedisKey {

	private RedisKey(){
	    throw new IllegalStateException("RedisKey class");
	}
	/**
	 * 车载重与绑定司机ID对应集合(score为车载重,value为司机ID)
	 */
	public static final String HUARUN_VEHICLE_CAPACITY_KEY = "HUARUN_VEHICLE_CAPACITY_KEY";
	
	/**
	 * 司机ID与所在城市对应集合(score为城市code,value为司机ID)
	 */
	public static final String HUARUN_MEMBER_CITY_KEY = "HUARUN_DRIVER_CITY_KEY";
	
	/**
	 * 司机ID与司机信息(hash类型,field为司机ID,value为司机信息JSON数据)
	 */
	public static final String HUARUN_MEMBER_INFO_KEY = "HUARUN_DRIVER_INFO_KEY";
	
	/**
	 * 抢单(订单)ID与订单接收司机(set集合类型,key为HUARUN_WAYBILL_+运单ID,值为能接收到推送的司机ID集合)
	 */
	public static final String HUARUN_WAYBILL_PREFIX = "HUARUN_WAYBILL_";


}
