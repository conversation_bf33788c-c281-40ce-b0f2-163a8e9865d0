package com.ecommerce.logistics.push.service;

import java.util.ArrayList;

/**
 * 消息推送服务
 * @Auther: <EMAIL>
 * @Date: 2018年9月17日 下午4:09:11
 * @Description:
 */
public interface IPushMessageService {
	
	/**
	 * 
	 * @Auther: <EMAIL>
	 * @Date: 2018年9月18日 下午6:54:04
	 * @param title
	 * @param message
	 * @param tokenList
	 * @return
	 */
	int pushIOSMessage(String title, String message, ArrayList<String> tokenList);
	
	/**
	 * 
	 * @Auther: <EMAIL>
	 * @Date: 2018年9月18日 下午6:54:11
	 * @param title
	 * @param message
	 * @param tokenList
	 * @return
	 */
	int pushAndriodMessage(String title, String message, ArrayList<String> tokenList);

}
