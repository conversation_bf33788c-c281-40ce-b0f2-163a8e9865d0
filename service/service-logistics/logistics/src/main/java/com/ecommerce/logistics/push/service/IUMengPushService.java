package com.ecommerce.logistics.push.service;

import java.util.List;

/**
 * 友盟推送
 * Auther: <EMAIL>
 * Date: 2018年9月27日 下午5:41:21
 * Description:
 */
public interface IUMengPushService {
	
	/**
	 * 推送andriod消息
	 * Auther: <EMAIL>
	 * Date: 2018年9月27日 下午5:43:17
	 * @param ticker
	 * @param tittle
	 * @param text
	 * @param deviceTokens
	 */
	void pushAndriodMessage(String waybillId,
								   String ticker,
								   String tittle,
								   String text,
								   List<String> deviceTokens,
								   String messagePushType,
								   String urlPath);
	
	/**
	 * ios消息推送
	 * Auther: <EMAIL>
	 * Date: 2018年9月27日 下午7:29:47
	 * @param tittle
	 * @param text
	 * @param deviceTokens
	 */
	void pushIOSMessage(String waybillId,
						String tittle,
						String text,
						List<String> deviceTokens,
						String messagePushType,
						String urlPath);
}
