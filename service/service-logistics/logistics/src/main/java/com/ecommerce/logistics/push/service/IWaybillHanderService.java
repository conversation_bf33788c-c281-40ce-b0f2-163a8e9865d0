package com.ecommerce.logistics.push.service;

import com.ecommerce.logistics.push.vo.AssignDriver;
import com.ecommerce.logistics.push.vo.SeckillWaybill;

/**
 * 运单推送业务处理类
 * Auther: <EMAIL>
 * Date: 2018年9月17日 下午4:13:38
 * Description:
 */
public interface IWaybillHanderService {

	/**
	 * 推送运单确认消息
	 * @param assignDriver 指派参数对象
	 */
	void pushAssignDriver(AssignDriver assignDriver);

	/**
	 * 推送抢单消息
	 * @param seckillWaybill 抢单参数对象
	 */
	void pushSeckillBill(SeckillWaybill seckillWaybill);

}
