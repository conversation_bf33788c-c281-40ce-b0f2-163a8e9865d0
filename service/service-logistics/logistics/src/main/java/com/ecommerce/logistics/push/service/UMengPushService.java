package com.ecommerce.logistics.push.service;

import com.ecommerce.logistics.push.umeng.UMengNotificationBean;
import com.ecommerce.open.api.dto.MessagePushDTO;
import com.ecommerce.open.api.service.IMessagePushService;
import com.ecommerce.open.enums.MessageOpenTypeEnum;
import com.ecommerce.open.enums.MessagePushAppNameEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * post请求服务类
 * Auther: <EMAIL>
 * Date: 2018年9月27日 下午2:25:27
 * Description
 */
@Service
@Slf4j
public class UMengPushService implements IUMengPushService{

	@Autowired
	private UMengNotificationBean uMengNotificationBean;

	@Autowired
	private IMessagePushService messagePushService;
	
	@Override
	public void pushAndriodMessage(String waybillId,
								   String ticker,
								   String tittle,
								   String text,
								   List<String> deviceTokens,
								   String messagePushType,
								   String urlPath) {
		MessagePushDTO messagePushDTO = new MessagePushDTO();
		messagePushDTO.setApplicationName(MessagePushAppNameEnum.DRIVER_APP.getCode());
		messagePushDTO.setTicker(ticker);
		messagePushDTO.setTittle(tittle);
		messagePushDTO.setText(text);
		messagePushDTO.setMessagePushType(messagePushType);
		messagePushDTO.setDeviceTokens(deviceTokens);
		messagePushDTO.setMessageOpenType(MessageOpenTypeEnum.GO_ACTIVITY.getCode());
		messagePushDTO.setActivity(uMengNotificationBean.getAndriodActivity());
		messagePushDTO.setMiActivity(uMengNotificationBean.getMiActivity());
		Map<String, String> extraMap = new HashMap<>(); // extraMap#key
		extraMap.put(uMengNotificationBean.getAndriodUrlKey(), convertGoUrl(waybillId, urlPath));
		messagePushDTO.setExtraMap(extraMap);
		Map<String, Object> custom = new HashMap<>();
		custom.put(uMengNotificationBean.getAndriodUrlKey(), convertGoUrl(waybillId, urlPath));
		messagePushDTO.setCustom(custom);//custom#key

		messagePushService.pushAndriodMessage(messagePushDTO);
    }
	
	@Override
	public void pushIOSMessage(String waybillId,
							   String tittle,
							   String text,
							   List<String> deviceTokens,
							   String messagePushType,
							   String urlPath) {
		MessagePushDTO messagePushDTO = new MessagePushDTO();
		messagePushDTO.setApplicationName(MessagePushAppNameEnum.DRIVER_APP.getCode());
		messagePushDTO.setTittle(tittle);
		messagePushDTO.setText(text);
		messagePushDTO.setMessagePushType(messagePushType);
		messagePushDTO.setDeviceTokens(deviceTokens);
		Map<String, String> extraMap = new HashMap<>();
		extraMap.put(uMengNotificationBean.getIosUrlKey(), convertGoUrl(waybillId, urlPath));
		messagePushDTO.setExtraMap(extraMap);

		messagePushService.pushIOSMessage(messagePushDTO);
    }

	private String convertGoUrl(String waybillId, String urlPath) {
		return uMengNotificationBean.getDominAddress() + UMengNotificationBean.URL_PREFIX + urlPath + waybillId;
	}
}
