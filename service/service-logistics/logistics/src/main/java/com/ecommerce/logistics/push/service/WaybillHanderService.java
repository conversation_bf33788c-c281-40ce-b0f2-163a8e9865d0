package com.ecommerce.logistics.push.service;

import com.ecommerce.logistics.biz.IVehicleBizService;
import com.ecommerce.logistics.biz.IWaybillSeckillBizService;
import com.ecommerce.logistics.biz.message.ISMSMessageBizService;
import com.ecommerce.logistics.push.vo.AssignDriver;
import com.ecommerce.logistics.push.vo.SeckillWaybill;
import com.ecommerce.member.api.dto.account.AccountDTO;
import com.ecommerce.member.api.dto.account.SearchTokenDTO;
import com.ecommerce.member.api.dto.servicearea.MemberServiceAreaItemDTO;
import com.ecommerce.member.api.service.IAccountService;
import com.ecommerce.member.api.service.IMemberAreaService;
import com.ecommerce.pay.api.v2.service.IDriverService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 抢单运单处理类
 * Auther: <EMAIL>
 * Date: 2018年9月12日 下午12:57:26
 * Description:
 */
@Slf4j
@Service
public class WaybillHanderService implements IWaybillHanderService{
	@Autowired
	private IVehicleBizService vehicleBizService;
	
	@Autowired
	private IAccountService accountService;
	
	@Autowired
	private IMemberAreaService memberAreaService;
	

	@Autowired
	private ISMSMessageBizService smsMessageBizService;
	
	@Autowired
	private IWaybillSeckillBizService waybillSeckillBizService;

	@Autowired
	private IDriverService driverService;
	
	private static final String ANDROID_PLANTFORM = "android";
	
	private static final String IOS_PLANTFORM = "ios";
	
	private static final String SECKILL_CONTENT = "您有新的待抢运单，赶紧去抢单吧！";
	
	private static final String SECKILL_TITLE = "快来抢我呀";
	
	private static final String ASSIGN_DRIVER_TITLE = "运单确认";
	
	/**
	 * 承运商指派司机消息推送
	 * Author: <EMAIL>
	 * Date: 2018年9月18日 下午5:31:09
	 */
	public void pushAssignDriver(AssignDriver assignDriver) {
		AccountDTO accountDTO = accountService.findById(assignDriver.getDriverId());
		log.info("accountDTO:" + accountDTO);
		smsMessageBizService.pushDeviceDriverAssign(assignDriver.getWaybillNum(), assignDriver.getWaybillId(), accountDTO.getAccountId());
	}
	
	/**
	 * 承运商/司机抢单推送
	 * Auther: <EMAIL>
	 * Date: 2018年9月18日 下午5:22:07
	 */
	public void pushSeckillBill(SeckillWaybill seckillWaybill) {
		String waybillId = seckillWaybill.getWaybillId();
		String cityCode = seckillWaybill.getCityCode();
		String provinceCode = seckillWaybill.getProvinceCode();
		MemberServiceAreaItemDTO memberServiceAreaItemDTO = new MemberServiceAreaItemDTO();
		memberServiceAreaItemDTO.setCityCode(cityCode);
		memberServiceAreaItemDTO.setProvinceCode(provinceCode);
		List<String> cityMemberIds = memberAreaService.findSociologyPersonIdOrCarrierIdByRegionCode(memberServiceAreaItemDTO);
		log.info("cityMemberIds:" + cityMemberIds);
		if (CollectionUtils.isEmpty(cityMemberIds)) {
			log.info(">>>运单ID:{}, 城市code:{}, 从会员端获取司机或承运商列表为空", waybillId, cityCode);
			return;
		}
		//去重
		cityMemberIds = cityMemberIds.parallelStream().distinct().toList();
		List<String> strings = vehicleBizService.queryPersonUserId(cityMemberIds);
		cityMemberIds.removeAll(strings);

		cityMemberIds = driverService.findDepositPayedDriver(cityMemberIds);
		if (CollectionUtils.isEmpty(cityMemberIds)) {
			log.info("区域内司机都没有缴纳保证金,推送结束");
			return;
		}

		waybillSeckillBizService.seveRecords(waybillId, cityMemberIds);
		SearchTokenDTO dto = new SearchTokenDTO();
		dto.setAccountIdList(cityMemberIds);
		List<Map<String, String>> terminalAccounts = accountService.getMobileTerminalToken(dto);
		if (CollectionUtils.isEmpty(terminalAccounts)) {
			log.info(">>>运单Id:{}, 会员ID集合:{}, 获取设备列表为空.", waybillId, cityMemberIds);
			return;
		}
		Set<String> deviceAccountIdSet = new HashSet<>();
		terminalAccounts.forEach(item -> deviceAccountIdSet.add(item.get("account_id")));
		smsMessageBizService.pushSocialSnatch(seckillWaybill.getWaybillNum(), seckillWaybill.getWaybillId(), Lists.newArrayList(deviceAccountIdSet));

		log.info(">>>>运单ID:{}, 推送司机抢单成功, 推送数量:{}", seckillWaybill.getWaybillId(), cityMemberIds.size());
	}
}
