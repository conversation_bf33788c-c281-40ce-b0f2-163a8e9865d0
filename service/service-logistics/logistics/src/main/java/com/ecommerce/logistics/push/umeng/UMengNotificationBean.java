package com.ecommerce.logistics.push.umeng;

import java.util.List;
import java.util.Map;

import com.ecommerce.logistics.api.enums.PushMessageTypeEnum;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import org.json.JSONObject;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.logistics.exception.LogisticsErrorCode;
import com.ecommerce.logistics.push.umeng.andriod.AndroidBroadcast;
import com.ecommerce.logistics.push.umeng.andriod.AndroidListcast;
import com.ecommerce.logistics.push.umeng.andriod.AndroidNotification;
import com.ecommerce.logistics.push.umeng.andriod.AndroidUnicast;
import com.ecommerce.logistics.push.umeng.ios.IOSBroadcast;
import com.ecommerce.logistics.push.umeng.ios.IOSListcast;
import com.ecommerce.logistics.push.umeng.ios.IOSUnicast;

import lombok.extern.slf4j.Slf4j;

/**
 * 
 * @Auther: <EMAIL>
 * @Date: 2018年9月27日 下午3:40:58
 * @Description:
 */
@Slf4j
@Setter
@Getter
@Configuration
@ConfigurationProperties(prefix="umeng.push.config")
public class UMengNotificationBean {

	private static final String DEFAULT = "default";
	/**
	 * andriod app key
	 */
	private String andriodAppKey;

	/**
	 * andriod master secret
	 */
	private String andriodMasterSecret;

	/**
	 * ios app key
	 */
	private String iosAppKey;

	/**
	 * ios master secret
	 */
	private String iosMasterSecret;

	/**
	 * andriod open activity when click notification
	 */
	private String andriodActivity;

	/**
	 * ios环境
	 */
	private String iosEvn;

	/**
	 * dominAddress
	 */
	private String dominAddress;
	
	/**
	 * andriod custom params
	 */
	private Map<String,String> andriodExtra;
	
	/**
	 * ios custom params
	 */
	private Map<String,String> iosExtra;

	private String andriodUrlKey;

	private String iosUrlKey;

	private String miActivity;

	private PushMessageTypeEnum pushMessageType;

	public static final String URL_PREFIX = "/#/";

	public void setAndriodAppKey(String andriodAppKey) {
		this.andriodAppKey = andriodAppKey;
	}

	public void setAndriodMasterSecret(String andriodMasterSecret) {
		this.andriodMasterSecret = andriodMasterSecret;
	}

	public void setIosAppKey(String iosAppKey) {
		this.iosAppKey = iosAppKey;
	}

	public void setIosMasterSecret(String iosMasterSecret) {
		this.iosMasterSecret = iosMasterSecret;
	}

	public void setAndriodActivity(String andriodActivity) {
		this.andriodActivity = andriodActivity;
	}

	public void setAndriodExtra(Map<String, String> andriodExtra) {
		this.andriodExtra = andriodExtra;
	}

	public Map<String, String> getAndriodExtra() {
		return andriodExtra;
	}

	public Map<String, String> getIosExtra() {
		return iosExtra;
	}

	public void setIosExtra(Map<String, String> iosExtra) {
		this.iosExtra = iosExtra;
	}

	public void setIosEvn(String iosEvn) {
		this.iosEvn = iosEvn;
	}

	public void setDominAddress(String dominAddress) {
		this.dominAddress = dominAddress;
	}

	public String getAndriodUrlKey() {
		return andriodUrlKey;
	}

	public void setAndriodUrlKey(String andriodUrlKey) {
		this.andriodUrlKey = andriodUrlKey;
	}

	public String getIosUrlKey() {
		return iosUrlKey;
	}

	public void setIosUrlKey(String iosUrlKey) {
		this.iosUrlKey = iosUrlKey;
	}

	public PushMessageTypeEnum getPushMessageType() {
		return pushMessageType;
	}

	public void setPushMessageType(PushMessageTypeEnum pushMessageType) {
		this.pushMessageType = pushMessageType;
	}

	private AndroidBroadcast andriodBroadcast;

	private AndroidUnicast andriodUnicast;

	private AndroidListcast andriodListcast;

	private IOSBroadcast iosBroadcast;

	private IOSUnicast iosUnicast;

	private IOSListcast iosListcast;

	/**
	 * 获取andriod群推实例
	 * @Auther: <EMAIL>
	 * @Date: 2018年9月27日 下午5:20:45
	 * @param ticker
	 * @param tittle
	 * @param text
	 * @return
	 */
	public UmengNotification getAndriodBroadcast(String waybillId, String ticker, String tittle, String text) {
		AndroidBroadcast broadcast;
		try {
			broadcast = getAndriodBroadcast();
			broadcast.setTicker(ticker);
			broadcast.setTitle(tittle);
			broadcast.setText(text);
			if(null != andriodActivity) {
				broadcast.goActivityAfterOpen(andriodActivity);
			}else {
				broadcast.goAppAfterOpen();
			}
			broadcast.setDisplayType(AndroidNotification.DisplayType.NOTIFICATION);
			if(andriodExtra != null) {
				JSONObject obj = new JSONObject();
				obj.put(this.andriodUrlKey, this.genAndriodPushUrl(waybillId));
				broadcast.setExtraField(this.andriodUrlKey, this.genAndriodPushUrl(waybillId));
				broadcast.setCustomField(obj);
			}
		} catch (Exception e) {
			throw new BizException(LogisticsErrorCode.UMENG_PUSH, "获取andriod broadcast推送实体");
		}
		return broadcast;
	}

	private AndroidBroadcast getAndriodBroadcast() throws Exception {
		if( null == andriodBroadcast) {
			andriodBroadcast = new AndroidBroadcast(andriodAppKey,andriodMasterSecret);
		}
		return andriodBroadcast;
	}

	private IOSBroadcast getIOSBroadcast() throws Exception {
		if( null == iosBroadcast) {
			iosBroadcast = new IOSBroadcast(iosAppKey,iosMasterSecret);
		}
		return iosBroadcast;
	}

	/**
	 * ios 群推
	 * @Auther: <EMAIL>
	 * @Date: 2018年9月27日 下午7:12:40
	 * @param tittle
	 * @param body
	 * @return
	 */
	public UmengNotification getIOSBroadcast(String waybillId, String tittle, String body) {
		IOSBroadcast broadcast;
		try {
			broadcast = getIOSBroadcast();
			broadcast.setTitle(tittle);
			broadcast.setSubtitle(tittle);
			broadcast.setBody(body);
			broadcast.setBadge(0);
			broadcast.setSound(DEFAULT);
			if(this.iosEvn != null && this.iosEvn.equals("dev")) {
				broadcast.setTestMode();
			}else {
				broadcast.setProductionMode();
			}
			if(iosExtra != null) {
				broadcast.setCustomizedField(this.iosUrlKey, this.genIosPushUrl(waybillId));
			}
		} catch (Exception e) {
			throw new BizException(LogisticsErrorCode.UMENG_PUSH, "获取ios broadcast推送实体");
		}
		return broadcast;
	}

	/**
	 * 获取andriod单推实例
	 * @Auther: <EMAIL>
	 * @Date: 2018年9月27日 下午3:55:34
	 * @param ticker
	 * @param tittle
	 * @param text
	 * @param deviceToken
	 * @return
	 */
	public UmengNotification getAndriodUnicast(String waybillId, String ticker, String tittle, String text, String deviceToken) {
		AndroidUnicast unicast;
		try {
			unicast = getAndriodUnicast();
			unicast.setDeviceToken(deviceToken);
			unicast.setTicker(ticker);
			unicast.setTitle(tittle);
			unicast.setText(text);
			if(null != andriodActivity) {
				unicast.goActivityAfterOpen(andriodActivity);
			}else {
				unicast.goAppAfterOpen();
			}
			unicast.setDisplayType(AndroidNotification.DisplayType.NOTIFICATION);
			if(andriodExtra != null) {
				JSONObject obj = new JSONObject();
				obj.put(this.andriodUrlKey, this.genAndriodPushUrl(waybillId));
				unicast.setExtraField(this.andriodUrlKey, this.genAndriodPushUrl(waybillId));
				unicast.setCustomField(obj);
			}
		} catch (Exception e) {
			throw new BizException(LogisticsErrorCode.UMENG_PUSH, "获取andriod unicast推送实体");
		}
		return unicast;
	}

	private AndroidUnicast getAndriodUnicast() throws Exception {
		if(null == andriodUnicast) {
			andriodUnicast = new AndroidUnicast(andriodAppKey,andriodMasterSecret);
		}
		return andriodUnicast;
	}

	private IOSUnicast getIOSUnicast() throws Exception {
		if(null == iosUnicast) {
			iosUnicast = new IOSUnicast(iosAppKey,iosMasterSecret);
		}
		return iosUnicast;
	}

	/**
	 * ios单推
	 * @Auther: <EMAIL>
	 * @Date: 2018年9月27日 下午7:12:18
	 * @param ticker
	 * @param tittle
	 * @param text
	 * @param deviceToken
	 * @return
	 */
	public UmengNotification getIOSUnicast(String waybillId, String tittle, String body, String deviceToken) {
		IOSUnicast unicast;
		try {
			unicast = getIOSUnicast();
			unicast.setDeviceToken(deviceToken);
			unicast.setTitle(tittle);
			unicast.setSubtitle(tittle);
			unicast.setBody(body);
			unicast.setBadge(0);
			unicast.setSound(DEFAULT);
			if(this.iosEvn != null && this.iosEvn.equals("dev")) {
				unicast.setTestMode();
			}else {
				unicast.setProductionMode();
			}
			if(iosExtra != null) {
				unicast.setCustomizedField(this.iosUrlKey, this.genIosPushUrl(waybillId));
			}
		} catch (Exception e) {
			throw new BizException(LogisticsErrorCode.UMENG_PUSH, "获取ios unicast推送实体");
		}
		return unicast;
	}

	/**
	 * 获取andriod umeng多个设备推送实例
	 * @Auther: <EMAIL>
	 * @Date: 2018年9月27日 下午5:05:13
	 * @param ticker
	 * @param tittle
	 * @param text
	 * @param deviceTokens
	 * @return
	 */
	public UmengNotification getAndriodListcast(String waybillId, String ticker, String tittle, String text, List<String> deviceTokens) {
		AndroidListcast listcast;
		try {
			listcast = getAndriodListcast();
			listcast.setDeviceToken(converListToString(deviceTokens));
			listcast.setTicker(ticker);
			listcast.setTitle(tittle);
			listcast.setText(text);
			if(null != andriodActivity) {
				listcast.goActivityAfterOpen(andriodActivity);
			}else {
				listcast.goAppAfterOpen();
			}
			listcast.setDisplayType(AndroidNotification.DisplayType.NOTIFICATION);
			if(andriodExtra != null) {
				JSONObject obj = new JSONObject();
				obj.put(this.andriodUrlKey, this.genAndriodPushUrl(waybillId));
				listcast.setExtraField(this.andriodUrlKey, this.genAndriodPushUrl(waybillId));
				listcast.setCustomField(obj);
			}
		} catch (Exception e) {
			throw new BizException(LogisticsErrorCode.UMENG_PUSH, "获取andriod listcast推送实体");
		}
		return listcast;
	}

	/**
	 * ios群推实例
	 * @Auther: <EMAIL>
	 * @Date: 2018年9月27日 下午7:22:07
	 * @param tittle
	 * @param body
	 * @param deviceTokens
	 * @return
	 */
	public UmengNotification getIOSListcast(String waybillId, String tittle, String body, List<String> deviceTokens) {
		IOSListcast listcast;
		try {
			listcast = getIOSListcast();
			listcast.setDeviceToken(converListToString(deviceTokens));
			listcast.setTitle(tittle);
			listcast.setBody(body);
			listcast.setSubtitle(tittle);
			listcast.setBadge(0);
			listcast.setSound(DEFAULT);
			if(this.iosEvn != null && this.iosEvn.equals("dev")) {
				listcast.setTestMode();
			}else {
				listcast.setProductionMode();
			}
			if(iosExtra != null) {
				listcast.setCustomizedField(this.iosUrlKey, this.genIosPushUrl(waybillId));
			}
		} catch (Exception e) {
			throw new BizException(LogisticsErrorCode.UMENG_PUSH, "获取ios listcast推送实体");
		}
		return listcast;
	}

	private AndroidListcast getAndriodListcast() throws Exception {
		if(null == andriodListcast) {
			andriodListcast = new AndroidListcast(andriodAppKey,andriodMasterSecret);
		}
		return andriodListcast;
	}

	private IOSListcast getIOSListcast() throws Exception {
		if(null == iosListcast) {
			iosListcast = new IOSListcast(iosAppKey,iosMasterSecret);
		}
		return iosListcast;
	}

	/**
	 * 将list转换成以逗号分隔的字符串
	 * @Auther: <EMAIL>
	 * @Date: 2018年9月27日 下午5:08:22
	 * @param strs
	 * @return
	 */
	private String converListToString(List<String> strs) {
		StringBuffer result = new StringBuffer();
		for (String str : strs) {
			result.append(str).append(",");
		}
		return result.toString();
	}

	private String genAndriodPushUrl(String waybillId) {
		return this.dominAddress + URL_PREFIX + this.andriodExtra.get(this.pushMessageType.getCode())+ waybillId;
	}

	private String genIosPushUrl(String waybillId) {
		return this.dominAddress + URL_PREFIX + this.iosExtra.get(this.pushMessageType.getCode())+ waybillId;
	}
}
