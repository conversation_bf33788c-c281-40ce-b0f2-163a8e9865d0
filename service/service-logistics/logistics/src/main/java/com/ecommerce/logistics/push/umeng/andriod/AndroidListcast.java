package com.ecommerce.logistics.push.umeng.andriod;

/**
 * 
 * @Auther: <EMAIL>
 * @Date: 2018年9月27日 下午2:05:27
 * @Description:
 * 			listcast-列播，要求不超过500个device_token
 * 			device_tokens之间用英文逗号隔开
 */
public class AndroidListcast extends AndroidNotification {
	
	public AndroidListcast(String appkey,String appMasterSecret) throws Exception {
		setAppMasterSecret(appMasterSecret);
		setPredefinedKeyValue("appkey", appkey);
		this.setPredefinedKeyValue("type", "listcast");	
	}
	
	/**
	 * 
	 * @Auther: <EMAIL>
	 * @Date: 2018年9月27日 下午2:05:55
	 * @param token
	 * @throws Exception
	 */
	public void setDeviceToken(String token) throws Exception {
		setPredefinedKeyValue("device_tokens", token);
	}

}
