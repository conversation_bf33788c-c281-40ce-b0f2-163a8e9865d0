package com.ecommerce.logistics.push.umeng.andriod;

/**
 * 
 * @Auther: <EMAIL>
 * @Date: 2018年9月27日 下午2:02:28
 * @Description:
 * 		unicast-单播  对指定device_token推送
 */
public class AndroidUnicast extends AndroidNotification {
	public AndroidUnicast(String appkey,String appMasterSecret) throws Exception {
			setAppMasterSecret(appMasterSecret);
			setPredefinedKeyValue("appkey", appkey);
			this.setPredefinedKeyValue("type", "unicast");	
	}
	
	public void setDeviceToken(String token) throws Exception {
    	setPredefinedKeyValue("device_tokens", token);
    }

}