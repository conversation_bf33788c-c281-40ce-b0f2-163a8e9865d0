package com.ecommerce.logistics.push.umeng.ios;

/**
 *  IOS指定多个设备推送,一次最多只能推送500个
 * @Auther: <EMAIL>
 * @Date: 2018年9月27日 下午2:07:40
 * @Description:
 */
public class IOSListcast extends IOSNotification {
	public IOSListcast(String appkey,String appMasterSecret) throws Exception{
		setAppMasterSecret(appMasterSecret);
		setPredefinedKeyValue("appkey", appkey);
		this.setPredefinedKeyValue("type", "listcast");	
	}
	
	/**
	 * 多个device_token之间用逗号隔开
	 * @Auther: <EMAIL>
	 * @Date: 2018年9月27日 下午2:08:35
	 * @param token
	 * @throws Exception
	 */
	public void setDeviceToken(String token) throws Exception {
		setPredefinedKeyValue("device_tokens", token);
	}
}
