package com.ecommerce.logistics.push.umeng.ios;


import com.ecommerce.logistics.push.umeng.UmengNotification;
import org.json.JSONObject;

import java.util.Arrays;
import java.util.HashSet;

/**
 * ios通知消息体
 * @Auther: <EMAIL>
 * @Date: 2018年9月27日 下午1:58:08
 * @Description:
 */
public abstract class IOSNotification extends UmengNotification {

	private static final String ALERT = "alert";
	private static final String PAYLOAD = "payload";
	private static final String POLICY = "policy";
	// Keys can be set in the aps level
	protected static final HashSet<String> APS_KEYS = new HashSet<String>(Arrays.asList(new String[]{
			ALERT, "badge", "sound", "content-available"
	}));
	
	protected static final HashSet<String> ALERT_KEYS = new HashSet<String>(Arrays.asList(new String[]{
			"title", "subtitle", "body"
	}));


	@Override
	public boolean setPredefinedKeyValue(String key, Object value) throws Exception {
		if (ROOT_KEYS.contains(key)) {
			// This key should be in the root level
			rootJson.put(key, value);
		} else if (APS_KEYS.contains(key)) {
			// This key should be in the aps level
			JSONObject payloadJson = getJson(rootJson,PAYLOAD);
			JSONObject apsJson = getJson(payloadJson,"aps");

			apsJson.put(key, value);
		} else if (POLICY_KEYS.contains(key)) {
			// This key should be in the body level
			JSONObject policyJson = getJson(rootJson,POLICY);
			policyJson.put(key, value);
		} else if(ALERT_KEYS.contains(key)) {
			JSONObject payloadJson = getJson(rootJson,PAYLOAD);
			JSONObject apsJson = getJson(payloadJson,"aps");
			JSONObject alertJson = getJson(apsJson,ALERT);

			alertJson.put(key, value);
		} else {
			if (PAYLOAD.equals(key) || "aps".equals(key) || POLICY.equals(key) || ALERT.equals(key)) {
				throw new Exception("You don't need to set value for " + key + ", just set values for the sub keys in it.");
			} else {
				throw new Exception("Unknown key: " + key);
			}
		}
		
		return true;
	}

	private static  JSONObject getJson(JSONObject json,String key){
		JSONObject jsonObject = null;
		if(json.has(key)){
			jsonObject = json.getJSONObject(key);
		} else {
			jsonObject = new JSONObject();
			json.put(key, jsonObject);
		}
		return jsonObject;
	}
	// Set customized key/value for IOS notification
	public boolean setCustomizedField(String key, String value) throws Exception {
		JSONObject payloadJson = null;
		if (rootJson.has(PAYLOAD)) {
			payloadJson = rootJson.getJSONObject(PAYLOAD);
		} else {
			payloadJson = new JSONObject();
			rootJson.put(PAYLOAD, payloadJson);
		}
		payloadJson.put(key, value);
		return true;
	}

	public void setAlert(String token) throws Exception {
    	setPredefinedKeyValue(ALERT, token);
    }
	
	public void setTitle(String token) throws Exception {
    	setPredefinedKeyValue("title", token);
    }
	
	public void setSubtitle(String token) throws Exception {
    	setPredefinedKeyValue("subtitle", token);
    }
	
	public void setBody(String token) throws Exception {
    	setPredefinedKeyValue("body", token);
    }
	
	public void setBadge(Integer badge) throws Exception {
    	setPredefinedKeyValue("badge", badge);
    }
	
	public void setSound(String sound) throws Exception {
    	setPredefinedKeyValue("sound", sound);
    }
	
	public void setContentAvailable(Integer contentAvailable) throws Exception {
    	setPredefinedKeyValue("content-available", contentAvailable);
    }
}
