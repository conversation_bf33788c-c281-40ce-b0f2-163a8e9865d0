package com.ecommerce.logistics.push.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 承运商指派司机
 * @Auther: <EMAIL>
 * @Date: 2018年9月18日 下午5:27:02
 * @Description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AssignDriver {
	
	/**
	 * 司机ID
	 */
	private String driverId;
	
	/**
	 * 运单ID
	 */
	private String waybillId;
	
	/**
	 * 运单号
	 */
	private String waybillNum;
}
