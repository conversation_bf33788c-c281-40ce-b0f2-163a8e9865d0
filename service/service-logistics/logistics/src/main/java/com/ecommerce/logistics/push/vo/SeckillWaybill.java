package com.ecommerce.logistics.push.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 司机/承运商抢单消息实体
 * @Auther: <EMAIL>
 * @Date: 2018年9月12日 下午12:53:04
 * @Description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SeckillWaybill {
	
	/**
	 * 运单ID
	 */
	private String waybillId;
	
	/**
	 * 运单号
	 */
	private String waybillNum;
	
	/**
	 * 城市
	 */
	private String cityCode;
	
	/**
	 * 省code
	 */
	private String provinceCode;
	
	/**
	 * 重量
	 */
	private BigDecimal quantity;
}
