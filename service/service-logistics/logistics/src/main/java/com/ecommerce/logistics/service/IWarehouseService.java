package com.ecommerce.logistics.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.warehouse.CentralWarehouseQueryDTO;
import com.ecommerce.logistics.api.dto.warehouse.SearchAddressDTO;
import com.ecommerce.logistics.api.dto.warehouse.StoreQueryDTO;
import com.ecommerce.logistics.api.dto.warehouse.TradeWarehouseDTO;
import com.ecommerce.logistics.api.dto.warehouse.WarehouseAddDTO;
import com.ecommerce.logistics.api.dto.warehouse.WarehouseBaseDataDTO;
import com.ecommerce.logistics.api.dto.warehouse.WarehouseDetailsDTO;
import com.ecommerce.logistics.api.dto.warehouse.WarehouseEditDTO;
import com.ecommerce.logistics.api.dto.warehouse.WarehouseInsertReturnDTO;
import com.ecommerce.logistics.api.dto.warehouse.WarehouseListDTO;
import com.ecommerce.logistics.api.dto.warehouse.WarehouseListQueryDTO;
import com.ecommerce.logistics.api.dto.warehouse.WarehouseOptionDTO;
import com.ecommerce.logistics.api.dto.warehouse.WarehouseOptionQueryDTO;
import com.ecommerce.logistics.api.dto.warehouse.WarehouseQueryDTO;
import com.ecommerce.logistics.api.dto.warehouse.WarehouseRemoveDTO;

import java.util.List;

/**
 * 仓库服务
 * <AUTHOR>
 * @date 18/7/27
 */
@Deprecated(since = "2.1.4-RELEASE")
public interface IWarehouseService {
    /**
     *
     * 获取仓库列表
     *
     * @param: warehouseListQueryDTO
     * @return:
     */
    ItemResult<PageData<WarehouseListDTO>> queryWarehouseList(PageQuery<WarehouseListQueryDTO> pageQuery);

    /**
     *
     * 删除仓库
     *
     * @param: WarehouseId
     * @return:
     */
    ItemResult<Void> removeWarehouse(WarehouseRemoveDTO warehouseRemoveDTO);

    /**
     *
     * 新增仓库
     *
     * @param: warehouseDTO
     * @return: 
     */
    ItemResult<WarehouseInsertReturnDTO> saveWarehouse(WarehouseAddDTO warehouseAddDTO);

    /**
     *
     * 编辑仓库
     *
     * @param:warehouseEditDTO
     * @return:
     */
    ItemResult<Void> updateWarehouse(WarehouseEditDTO warehouseEditDTO);

    /**
     * 查询仓库下拉列表
     * @param warehouseOptionQueryDTO
     * @return
     */
    ItemResult<List<WarehouseOptionDTO>>queryWarehouseIdAndName(WarehouseOptionQueryDTO warehouseOptionQueryDTO);

    /**
     * 查询仓库详情
     * @param warehouseId
     * @return
     */
    ItemResult<WarehouseDetailsDTO> queryWarehouseDetails(String warehouseId);

    /**
     * 条件模糊查询仓库
     * @param searchAddressDTO
     * @return
     */
    ItemResult<String> searchAddress(SearchAddressDTO searchAddressDTO);

    /**
     * 通过用户ID查询用户可抢运单的仓库list
     * @Auther: <EMAIL>
     * @Date: 2018年9月11日 下午5:21:23
     * @param info
     * @return
     */
	ItemResult<List<WarehouseOptionDTO>> selectSeckillWarehouseByUserId(String userId);

    /**
     * 查询区域出货的中心仓或门店
     *
     * @param warehouseQueryDTO 仓库查询对象
     * @return List<CreateWaybillDTO> 订单创建列表
     */
    ItemResult<TradeWarehouseDTO> queryZoneWarehouse(WarehouseQueryDTO warehouseQueryDTO);

    /**
     * 查询仓库的基础数据
     * @param warehouseId
     * @return
     */
    ItemResult<WarehouseBaseDataDTO> selectWarehouseBaseData(String warehouseId);

    /**
     * 根据区域查询中心仓
     * @param centralWarehouseQueryDTO  中心仓查询对象
     * @return WarehouseDetailsDTO
     */
    ItemResult<WarehouseDetailsDTO> queryCentralWarehouseByArea(CentralWarehouseQueryDTO centralWarehouseQueryDTO);

    /**
     * 根据门店Code区域查询门店
     * @param storeQueryDTO  门店查询对象
     * @return WarehouseDetailsDTO
     */
    ItemResult<WarehouseDetailsDTO> queryStoreByCode(StoreQueryDTO storeQueryDTO);

}
