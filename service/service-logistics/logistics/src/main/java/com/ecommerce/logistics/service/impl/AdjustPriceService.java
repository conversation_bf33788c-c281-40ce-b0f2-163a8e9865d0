package com.ecommerce.logistics.service.impl;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.logistics.api.dto.adjust.AdjustMemberDTO;
import com.ecommerce.logistics.api.dto.adjust.AdjustPriceResultDTO;
import com.ecommerce.logistics.api.dto.adjust.AdjustQueryDTO;
import com.ecommerce.logistics.api.dto.adjust.AdjustShipBillDTO;
import com.ecommerce.logistics.api.dto.adjust.AdjustTakeInfoDTO;
import com.ecommerce.logistics.biz.IAdjustPriceBizService;
import com.ecommerce.logistics.service.IAdjustPriceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class AdjustPriceService implements IAdjustPriceService
{
    @Autowired
    private IAdjustPriceBizService adjustPriceBizService;

    @Override
    public List<AdjustMemberDTO> getAdjustMemberList(AdjustQueryDTO query)
    {
        return adjustPriceBizService.getAdjustMemberList(query);
    }

    @Override
    public List<AdjustShipBillDTO> getAdjustPriceShipBillList(AdjustQueryDTO query)
    {
        return adjustPriceBizService.getAdjustPriceShipBillList(query);
    }

    @Override
    public boolean saveAdjustPriceResult(AdjustPriceResultDTO dto)
    {
        return adjustPriceBizService.saveAdjustPriceResult(dto);
    }

    @Override
    public ItemResult<List<String>> adjustConcreteByTakeCode(List<AdjustTakeInfoDTO> adjustTakeInfoDTOList) {
        return new ItemResult<>(adjustPriceBizService.adjustConcreteByTakeCode(adjustTakeInfoDTOList));
    }
}
