package com.ecommerce.logistics.service.impl;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.logistics.api.dto.driver.AssignDriverLogAddDTO;
import com.ecommerce.logistics.api.dto.driver.AssignDriverLogCondDTO;
import com.ecommerce.logistics.api.dto.driver.AssignDriverLogResDTO;
import com.ecommerce.logistics.biz.IAssignDriverLogBizService;
import com.ecommerce.logistics.service.IAssignDriverLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Auther: colu
 * @Date: 2020-05-19 17:51
 * @Description: 指派司机日志记录服务
 */
@Service("assignDriverLogService")
public class AssignDriverLogService implements IAssignDriverLogService {

    @Autowired
    private IAssignDriverLogBizService assignDriverLogBizService;

    @Override
    public ItemResult<List<AssignDriverLogResDTO>> queryByCond(AssignDriverLogCondDTO assignDriverLogCondDTO) {
        return new ItemResult<>(assignDriverLogBizService.queryByCond(assignDriverLogCondDTO));
    }

    @Override
    public ItemResult<Void> updateSelfPickAssignLog(AssignDriverLogAddDTO assignDriverLogAddDTO) {
        assignDriverLogBizService.updateSelfPickAssignLog(assignDriverLogAddDTO);
        return new ItemResult<>(null);
    }

}
