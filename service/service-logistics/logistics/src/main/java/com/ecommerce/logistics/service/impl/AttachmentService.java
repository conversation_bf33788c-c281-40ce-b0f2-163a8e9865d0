package com.ecommerce.logistics.service.impl;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.logistics.api.dto.attachment.AttListAddDTO;
import com.ecommerce.logistics.api.dto.attachment.AttListDTO;
import com.ecommerce.logistics.api.dto.attachment.AttListQueryDTO;
import com.ecommerce.logistics.api.dto.attachment.AttRemoveDTO;
import com.ecommerce.logistics.biz.impl.AttachmentBizService;
import com.ecommerce.logistics.service.IAttachmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Description:
 * @Date: Create in 上午11:06 18/8/15
 */
@Service
public class AttachmentService implements IAttachmentService{
    @Autowired
    private AttachmentBizService attachmentBizService;

    @Override
    public ItemResult<List<AttListDTO>> queryAttList(AttListQueryDTO attListQueryDTO) {
        return new ItemResult<>(attachmentBizService.getAttList(attListQueryDTO));
    }

    @Override
    public ItemResult<Void> addAtt(AttListAddDTO attListAddDTO) {
        attachmentBizService.saveAttList(attListAddDTO);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> removeAtt(AttRemoveDTO attRemoveDTO) {
        attachmentBizService.deleteAtt(attRemoveDTO);
        return new ItemResult<>(null);
    }
}
