package com.ecommerce.logistics.service.impl;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.shippingroute.AuxiliaryPriceDTO;
import com.ecommerce.logistics.api.dto.shippingroute.AuxiliaryPriceListDTO;
import com.ecommerce.logistics.biz.IAuxiliaryPriceBizService;
import com.ecommerce.logistics.service.IAuxiliaryPriceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 辅助价目表服务
 * Created by hexinhui on 2020/11/9 10:18
 */
@Slf4j
@Service
public class AuxiliaryPriceService implements IAuxiliaryPriceService {

    @Autowired
    private IAuxiliaryPriceBizService auxiliaryPriceBizService;


    @Override
    public ItemResult<Void> addAuxiliaryPrice(List<AuxiliaryPriceDTO> dtoList) {
        auxiliaryPriceBizService.addAuxiliaryPrice(dtoList);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> delAuxiliaryPriceByNo(String belongerId,String auxiliaryPriceNo, String operatorUserId) {
        auxiliaryPriceBizService.delAuxiliaryPriceByNo(belongerId,auxiliaryPriceNo, operatorUserId);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> delAuxiliaryPrice(String auxiliaryPriceNo, String belongerId, String auxiliaryPriceId, String operatorUserId) {
        auxiliaryPriceBizService.delAuxiliaryPrice(auxiliaryPriceNo, belongerId, auxiliaryPriceId, operatorUserId);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> editAuxiliaryPrice(List<AuxiliaryPriceDTO> dtoList) {
        auxiliaryPriceBizService.editAuxiliaryPrice(dtoList);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<List<AuxiliaryPriceDTO>> queryAuxiliaryPriceDetails(String belongerId, String auxiliaryPriceNo,Boolean drySeasonPolicyAffected ) {
        return new ItemResult<>(auxiliaryPriceBizService.queryAuxiliaryPriceDetails(belongerId, auxiliaryPriceNo));
    }

    @Override
    public ItemResult<PageData<AuxiliaryPriceListDTO>> queryAuxiliaryPriceList(PageQuery<AuxiliaryPriceDTO> pageQuery) {
        return new ItemResult<>(auxiliaryPriceBizService.queryAuxiliaryPriceList(pageQuery));
    }
}
