package com.ecommerce.logistics.service.impl;

import com.alibaba.fastjson.JSON;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.billEvaluate.BillEvaluateListDTO;
import com.ecommerce.logistics.api.dto.billEvaluate.BillEvaluateQueryDTO;
import com.ecommerce.logistics.api.dto.billEvaluate.BillEvaluateSaveDTO;
import com.ecommerce.logistics.api.dto.billEvaluate.BillEvaluateStatisticListDTO;
import com.ecommerce.logistics.api.dto.billEvaluate.BillEvaluateStatisticListQueryDTO;
import com.ecommerce.logistics.api.dto.billEvaluate.BillEvaluateStatisticSaveDTO;
import com.ecommerce.logistics.api.dto.pickingbill.PickingBillAssignDispatchDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillDTO;
import com.ecommerce.logistics.api.enums.EvaluateDimensionEnum;
import com.ecommerce.logistics.api.enums.EvaluatorTypeEnum;
import com.ecommerce.logistics.api.enums.RelationBillTypeEnum;
import com.ecommerce.logistics.api.enums.WaybillTypeEnum;
import com.ecommerce.logistics.biz.IBillEvaluateBizService;
import com.ecommerce.logistics.biz.IBillEvaluateStatisticBizService;
import com.ecommerce.logistics.biz.IDispatchBillBizService;
import com.ecommerce.logistics.biz.IPickingBillBizService;
import com.ecommerce.logistics.biz.IWaybillQueryBizService;
import com.ecommerce.logistics.dao.vo.BillEvaluateStatistic;
import com.ecommerce.logistics.dao.vo.DispatchBill;
import com.ecommerce.logistics.dao.vo.PickingBill;
import com.ecommerce.logistics.dao.vo.Waybill;
import com.ecommerce.logistics.service.IBillEvaluateService;
import com.ecommerce.logistics.util.LogisticsUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * 物流单据评价服务
 * @Author: <EMAIL>
 * @Date: 2020-09-22 17:55
 */
@Service
@Slf4j
public class BillEvaluateService implements IBillEvaluateService {

    @Autowired
    private IBillEvaluateBizService billEvaluateBizService;

    @Autowired
    private IBillEvaluateStatisticBizService billEvaluateStatisticBizService;

    @Autowired
    private IPickingBillBizService pickingBillBizService;

    @Autowired
    private IDispatchBillBizService dispatchBillBizService;

    @Autowired
    private IWaybillQueryBizService waybillBizService;

    @Resource(name = "bizRedisTemplate")
    private RedisTemplate bizRedisTemplate;

    private static final String EVALUATE_CARRIER = "logistics:cache:evaluate_statistic:carrier";
    private static final String EVALUATE_DRIVER = "logistics:cache:evaluate_statistic:driver";
    private static final String EVALUATE_PLATFORM = "logistics:cache:evaluate_statistic:platform";

    @Override
    public ItemResult<PageData<BillEvaluateListDTO>> queryBillEvaluateList(PageQuery<BillEvaluateQueryDTO> pageQuery) {
        return new ItemResult<>(billEvaluateBizService.queryBillEvaluateList(pageQuery));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ItemResult<String> addBillEvaluate(BillEvaluateSaveDTO billEvaluateSaveDTO) {
        //添加评价记录
        billEvaluateBizService.addBillEvaluate(billEvaluateSaveDTO);

        //如果是评价托运方-平台
        if (CsStringUtils.equals(billEvaluateSaveDTO.getType(), EvaluateDimensionEnum.SHIPPERS.getCode())) {
            //不需要更新平台的评价统计
            //更新对应单据表是否已评价字段
            RelationBillTypeEnum relationBillTypeEnum = RelationBillTypeEnum.valueOfCode(billEvaluateSaveDTO.getRelationBillType());
            if(relationBillTypeEnum == null){
                throw new BizException(BasicCode.UNDEFINED_ERROR, "未找到对应关联单据类型");
            }
            switch (relationBillTypeEnum){
                case PICK_BILL:
                    PickingBill pickingBill = new PickingBill();
                    pickingBill.setPickingBillId(billEvaluateSaveDTO.getRelationBillId());
                    pickingBill.setIsEvaluate((byte) 1);
                    pickingBillBizService.updatePickingBill(pickingBill);
                    break;
                case DISPATCH_BILL:
                    DispatchBill dispatchBill = new DispatchBill();
                    dispatchBill.setDispatchBillId(billEvaluateSaveDTO.getRelationBillId());
                    dispatchBill.setCarrierEvaluateFlag((byte) 1);
                    dispatchBillBizService.updateDispatchBill(dispatchBill);
                    break;
                case WAY_BILL:
                    Waybill waybill = new Waybill();
                    waybill.setWaybillId(billEvaluateSaveDTO.getRelationBillId());
                    waybill.setDriverEvaluateFlag((byte) 1);
                    waybillBizService.updateWaybill(waybill);
                    break;
                default:
                    break;
            }
        }

        //如果是评价承运方
        if (CsStringUtils.equals(billEvaluateSaveDTO.getType(), EvaluateDimensionEnum.CARRIERS.getCode())) {
            //如果被评价主体为类型为平台,且类型为评价承运方,则为卖家评价平台,关联单据为提货单
            //需要去更新司机和承运商的评价
            if (CsStringUtils.equals(EvaluatorTypeEnum.PLATFORM.getCode(), billEvaluateSaveDTO.getEvaluatedPersonType())) {
                //更新司机评价
                //通过提货单号查询关联运单
                List<WaybillDTO> waybillDTOS = waybillBizService.selectWaybillsByPickingBillIds(Lists.newArrayList(billEvaluateSaveDTO.getRelationBillId()));
                handleWaybill(billEvaluateSaveDTO, waybillDTOS);

                //更新承运商评价
                //通过提货单号查询关联调度单
                List<PickingBillAssignDispatchDTO> pickingBillAssignDispatchDTOS = dispatchBillBizService.selectDispatchBillByPickingBillId(billEvaluateSaveDTO.getRelationBillId());
                handlePickingBillAssignDispatch(billEvaluateSaveDTO, pickingBillAssignDispatchDTOS);
            }

            BillEvaluateStatisticSaveDTO evaluateStatisticSaveDTO = new BillEvaluateStatisticSaveDTO();
            BeanUtils.copyProperties(billEvaluateSaveDTO,evaluateStatisticSaveDTO);
            evaluateStatisticSaveDTO.setOperatorUserId(billEvaluateSaveDTO.getCreateUser());
            saveBillEvaluateStatistic(evaluateStatisticSaveDTO,1);

            //更新对应单据表是否已评价字段
            RelationBillTypeEnum relationBillTypeEnum = RelationBillTypeEnum.valueOfCode(billEvaluateSaveDTO.getRelationBillType());
            if(relationBillTypeEnum == null){
                throw new BizException(BasicCode.UNDEFINED_ERROR, "未找到对应关联单据类型");
            }
            switch (relationBillTypeEnum){
                case PICK_BILL:
                    PickingBill pickingBill = new PickingBill();
                    pickingBill.setPickingBillId(billEvaluateSaveDTO.getRelationBillId());
                    pickingBill.setIsEvaluate((byte) 1);
                    pickingBillBizService.updatePickingBill(pickingBill);
                    break;
                case DISPATCH_BILL:
                    DispatchBill dispatchBill = new DispatchBill();
                    dispatchBill.setDispatchBillId(billEvaluateSaveDTO.getRelationBillId());
                    dispatchBill.setPlatformEvaluateFlag((byte) 1);
                    dispatchBillBizService.updateDispatchBill(dispatchBill);
                    break;
                case WAY_BILL:
                    Waybill waybill = new Waybill();
                    waybill.setWaybillId(billEvaluateSaveDTO.getRelationBillId());
                    waybill.setPlatformEvaluateFlag((byte) 1);
                    waybillBizService.updateWaybill(waybill);
                    break;
                default:
                    break;
            }
        }
        return new ItemResult<>(null);
    }

    private void handlePickingBillAssignDispatch(BillEvaluateSaveDTO billEvaluateSaveDTO, List<PickingBillAssignDispatchDTO> pickingBillAssignDispatchDTOS) {
        if(CollectionUtils.isNotEmpty(pickingBillAssignDispatchDTOS)){
            for(PickingBillAssignDispatchDTO pickingBillAssignDispatchDTO : pickingBillAssignDispatchDTOS){
                //去评价统计表里更新承运商的平均分，但是不加次数
                BillEvaluateStatisticSaveDTO saveDTO = new BillEvaluateStatisticSaveDTO();
                BeanUtils.copyProperties(billEvaluateSaveDTO,saveDTO);
                saveDTO.setEvaluatedPersonId(pickingBillAssignDispatchDTO.getCarrierId());
                saveDTO.setEvaluatedPersonName(pickingBillAssignDispatchDTO.getCarrierName());
                saveDTO.setEvaluatedPersonType(EvaluatorTypeEnum.CARRIER.getCode());
                saveDTO.setOperatorUserId(billEvaluateSaveDTO.getCreateUser());
                saveBillEvaluateStatistic(saveDTO,0);

                DispatchBill dispatchBill = new DispatchBill();
                dispatchBill.setDispatchBillId(pickingBillAssignDispatchDTO.getDispatchBillId());
                dispatchBill.setCarrierEvaluateFlag((byte) 1);
                dispatchBillBizService.updateDispatchBill(dispatchBill);
            }
        }
    }

    private void handleWaybill(BillEvaluateSaveDTO billEvaluateSaveDTO, List<WaybillDTO> waybillDTOS) {
        if(CollectionUtils.isNotEmpty(waybillDTOS)){
            for(WaybillDTO waybillDTO : waybillDTOS){
                //社会运力抢单，评价司机
                if(LogisticsUtils.isSame(WaybillTypeEnum.SOCIETY_SNATCH.getCode(),waybillDTO.getCarrierId())){
                    //去评价统计表里更新该司机的平均分，但是不加次数
                    BillEvaluateStatisticSaveDTO saveDTO = new BillEvaluateStatisticSaveDTO();
                    BeanUtils.copyProperties(billEvaluateSaveDTO,saveDTO);
                    saveDTO.setEvaluatedPersonId(waybillDTO.getCarrierId());
                    saveDTO.setEvaluatedPersonName(waybillDTO.getCarrierName());
                    saveDTO.setEvaluatedPersonType(EvaluatorTypeEnum.PERSONAL_DRIVER.getCode());
                    saveDTO.setOperatorUserId(billEvaluateSaveDTO.getCreateUser());
                    saveBillEvaluateStatistic(saveDTO,0);

                    Waybill waybill = new Waybill();
                    waybill.setWaybillId(waybillDTO.getWaybillId());
                    waybill.setDriverEvaluateFlag((byte) 1);
                    waybillBizService.updateWaybill(waybill);
                }
            }
        }
    }

    @Override
    public ItemResult<PageData<BillEvaluateStatisticListDTO>> selectEvaluateStatisticList(PageQuery<BillEvaluateStatisticListQueryDTO> pageQuery) {
        PageData<BillEvaluateStatisticListDTO> pageData = billEvaluateStatisticBizService.selectEvaluateStatisticList(pageQuery);
        List<BillEvaluateStatisticListDTO> evaluateStatisticListDTOS = pageData.getList();

        if(CollectionUtils.isEmpty(evaluateStatisticListDTOS))
            return new ItemResult<>(pageData);

        evaluateStatisticListDTOS.forEach(item -> {
            String evaluatedPersonType = item.getEvaluatedPersonType();
            String queueName;
            if (LogisticsUtils.isSame(evaluatedPersonType, EvaluatorTypeEnum.CARRIER.getCode())) {
                queueName = EVALUATE_CARRIER;
            } else if (LogisticsUtils.isSame(evaluatedPersonType, EvaluatorTypeEnum.PERSONAL_DRIVER.getCode())) {
                queueName = EVALUATE_DRIVER;
            } else if (LogisticsUtils.isSame(evaluatedPersonType, EvaluatorTypeEnum.PLATFORM.getCode())) {
                queueName = EVALUATE_PLATFORM;
            } else {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "评价人类型不存在");
            }
            //平台只有一个账号，不需要排位
            if (LogisticsUtils.isSame(evaluatedPersonType, EvaluatorTypeEnum.PLATFORM.getCode())) {
                item.setRank("-");
            } else {
                Long size = bizRedisTemplate.opsForZSet().size(queueName);
                if (size == null || size == 0) {
                    updateAllRankCache();
                    size = bizRedisTemplate.opsForZSet().size(queueName);
                }
                Long rank = size - bizRedisTemplate.opsForZSet().rank(queueName, item.getEvaluatedPersonId());
                item.setRank(rank.toString() + "/" + size.toString());
            }
        });
        return new ItemResult<>(pageData);
    }

    //刷新redis排名缓存
    private void updateRankCache(String queueName,BillEvaluateStatistic evaluateStatistic){
        log.info("BillEvaluateStatistic=============={}", JSON.toJSONString(evaluateStatistic));
        bizRedisTemplate.opsForZSet().add(queueName,evaluateStatistic.getEvaluatedPersonId(),generateScore(evaluateStatistic.getAveScore()));
    }

    //清空所有排位缓存后重新生成排位数据
    public ItemResult<Void> updateAllRankCache(){
        log.info("开始清空所有排位缓存数据=====");
        bizRedisTemplate.delete(Lists.newArrayList(EVALUATE_CARRIER,EVALUATE_DRIVER));
        //更新司机排名
        List<BillEvaluateStatisticListDTO> billEvaluateDTOS = billEvaluateStatisticBizService.selectPersonIdAndScoreByType(EvaluatorTypeEnum.PERSONAL_DRIVER.getCode());
        if(CollectionUtils.isNotEmpty(billEvaluateDTOS)){
            billEvaluateDTOS.forEach(item ->
                bizRedisTemplate.opsForZSet().add(EVALUATE_DRIVER,item.getEvaluatedPersonId(),generateScore(item.getAveScore()))
            );
        }
        //更新承运商排名
        List<BillEvaluateStatisticListDTO> carrierBillEvaluateDTOS = billEvaluateStatisticBizService.selectPersonIdAndScoreByType(EvaluatorTypeEnum.CARRIER.getCode());
        if(CollectionUtils.isNotEmpty(carrierBillEvaluateDTOS)){
            carrierBillEvaluateDTOS.forEach(item ->
                bizRedisTemplate.opsForZSet().add(EVALUATE_CARRIER,item.getEvaluatedPersonId(),generateScore(item.getAveScore()))
            );
        }

        return new ItemResult<>(null);
    }

    //更新评价统计
    private void saveBillEvaluateStatistic(BillEvaluateStatisticSaveDTO evaluateStatisticSaveDTO,int addCountFlag){
        BillEvaluateStatistic evaluateStatistic = billEvaluateStatisticBizService.addOrUpdateBillEvaluate(evaluateStatisticSaveDTO,addCountFlag);
        //刷新redis排名缓存
        String queueName;
        if(LogisticsUtils.isSame(evaluateStatisticSaveDTO.getEvaluatedPersonType(), EvaluatorTypeEnum.CARRIER.getCode())){
            queueName = EVALUATE_CARRIER;
        }else if(LogisticsUtils.isSame(evaluateStatisticSaveDTO.getEvaluatedPersonType(), EvaluatorTypeEnum.PERSONAL_DRIVER.getCode())){
            queueName = EVALUATE_DRIVER;
        }else if (LogisticsUtils.isSame(evaluateStatisticSaveDTO.getEvaluatedPersonType(), EvaluatorTypeEnum.PLATFORM.getCode())) {
            //平台只有一个，不参与排名
            return;
        }else {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "被评价主体类型不存在");
        }
        updateRankCache(queueName,evaluateStatistic);
    }

    //评分拼接时间
    private double generateScore(int aveScore){
        SimpleDateFormat timeFormat = new SimpleDateFormat("yyMMddHHmmss");
        aveScore = aveScore + 1;
        String Date = aveScore + timeFormat.format(new Date());
        return Double.parseDouble(Date);
    }
}
