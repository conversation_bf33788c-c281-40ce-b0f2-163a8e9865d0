package com.ecommerce.logistics.service.impl;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.breakdto.BreakBillDTO;
import com.ecommerce.logistics.api.dto.breakdto.BreakDTO;
import com.ecommerce.logistics.api.param.breakparam.BreakAgreeParam;
import com.ecommerce.logistics.api.param.breakparam.BreakBatchCancelParam;
import com.ecommerce.logistics.api.param.breakparam.BreakBillQueryParam;
import com.ecommerce.logistics.api.param.breakparam.BreakFollowParam;
import com.ecommerce.logistics.api.param.breakparam.BreakParam;
import com.ecommerce.logistics.api.param.breakparam.BreakQueryParam;
import com.ecommerce.logistics.api.param.breakparam.BreakRejectParam;
import com.ecommerce.logistics.api.param.breakparam.BreakResponsibleParam;
import com.ecommerce.logistics.biz.IBreakBizService;
import com.ecommerce.logistics.biz.ICreditStatisticBizService;
import com.ecommerce.logistics.dao.vo.CreditStatistic;
import com.ecommerce.logistics.service.IBreakService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Copyright (C),2020
 *
 * @className: BreakService
 * @author: <EMAIL>
 * @Date: 2020/10/21 4:34 下午
 * @Description:
 */
@Service
public class BreakService implements IBreakService {

    @Autowired
    private IBreakBizService breakBizService;

    @Autowired
    private ICreditStatisticBizService creditStatisticBizService;

    @Override
    public ItemResult<PageData<BreakDTO>> pageByQueryOption(PageQuery<BreakQueryParam> param) {
        return new ItemResult<>(breakBizService.pageByQueryOption(param));
    }

    @Override
    public ItemResult<Boolean> addOrUpdateBreak(BreakParam breakParam) {
        return new ItemResult<>(breakBizService.addOrUpdateBreak(breakParam));
    }

    @Override
    public ItemResult<Boolean> batchCancelBreak(BreakBatchCancelParam breakBatchCancelParam) {
        return new ItemResult<>(breakBizService.batchCancelBreak(breakBatchCancelParam));
    }

    @Override
    public ItemResult<Boolean> responsibleBreak(BreakResponsibleParam breakResponsibleParam) {
        //违约归责时，如果责任方为承运商或者司机，则添加其违约次数
        CreditStatistic creditStatistic = creditStatisticBizService.findCreditStatisticById(breakResponsibleParam.getResponsibleRoleId(),breakResponsibleParam.getResponsibleRoleType());
        //如果存在记录，则其违约次数+1
        if (creditStatistic != null && CsStringUtils.isNotBlank(creditStatistic.getPersonId())) {
            creditStatisticBizService.updateCreditStatisticCount(creditStatistic.getPersonId(),false,true,false,breakResponsibleParam.getOperateUserId());
        }
        return new ItemResult<>(breakBizService.responsibleBreak(breakResponsibleParam));
    }

    @Override
    public ItemResult<Boolean> followBreak(BreakFollowParam breakFollowParam) {
        return new ItemResult<>(breakBizService.followBreak(breakFollowParam));
    }

    @Override
    public ItemResult<Boolean> agreeBreak(BreakAgreeParam breakAgreeParam) {
        return new ItemResult<>(breakBizService.agreeBreak(breakAgreeParam));
    }

    @Override
    public ItemResult<Boolean> rejectBreak(BreakRejectParam breakRejectParam) {
        return new ItemResult<>(breakBizService.rejectBreak(breakRejectParam));
    }

    @Override
    public ItemResult<BreakBillDTO> queryBreakBill(BreakBillQueryParam breakBillQueryParam) {
        return new ItemResult<>(breakBizService.queryBreakBill(breakBillQueryParam));
    }
}
