package com.ecommerce.logistics.service.impl;

import com.alibaba.fastjson.JSON;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.carriage.*;
import com.ecommerce.logistics.biz.ICarriageRouteBizService;
import com.ecommerce.logistics.dao.mapper.RouteContractMapMapper;
import com.ecommerce.logistics.dao.vo.RouteContractMap;
import com.ecommerce.logistics.exception.LogisticsErrorCode;
import com.ecommerce.logistics.service.ICarriageRouteService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.List;
import java.util.Objects;

/**
 * Author: lehu
 * Description:
 * Date: Create in 下午4:17 19/6/3
 */
@Slf4j
@Service
public class CarriageRouteService implements ICarriageRouteService {

    @Autowired
    private ICarriageRouteBizService carriageRouteBizService;

    @Autowired(required = false)
    private RouteContractMapMapper routeContractMapMapper;

    @Override
    public ItemResult<String> enteringCarriageRoute(CarriageRouteSaveDTO carriageRouteSaveDTO) {
        try {
            return new ItemResult<>(carriageRouteBizService.enteringCarriageRoute(carriageRouteSaveDTO));
        } catch (BizException e) {
            //特殊逻辑:返回code: 030034,和重复ID
            return getEditExceptionItemResult(e);
        }

    }

    @Override
    public ItemResult<Void> deleteCarriageRoute(CarriageRouteDeleteDTO carriageRouteDeleteDTO) {
        carriageRouteBizService.deleteCarriageRoute(carriageRouteDeleteDTO);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<String> editCarriageRoute(CarriageRouteSaveDTO carriageRouteSaveDTO) {
        try {
            carriageRouteBizService.editCarriageRoute(carriageRouteSaveDTO);
        }  catch (BizException e) {
            //特殊逻辑:返回code: 030034,和重复ID
            return getEditExceptionItemResult(e);
        }
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<CarriageRouteDetailDTO> queryCarriageRouteDetail(String carriageRouteId) {
        return new ItemResult<>(carriageRouteBizService.queryCarriageRouteDetail(carriageRouteId));
    }

    @Override
    public ItemResult<PageData<CarriageRouteListDTO>> queryCarriageRouteList(PageQuery<CarriageRouteQueryDTO> pageQuery) {
        return new ItemResult<>(carriageRouteBizService.queryCarriageRouteList(pageQuery));
    }

    @Override
    public boolean hasERPContractAddress(CarriageRouteQueryDTO queryDTO) {
        CarriageRouteDetailDTO detailDTO = carriageRouteBizService.queryPointCarriageRouteDetail(queryDTO);
        if (detailDTO != null && CsStringUtils.isNotBlank(detailDTO.getCarriageRouteId())) {
            Condition condition = new Condition(RouteContractMap.class);
            Example.Criteria criteria = condition.createCriteria();
            criteria.andEqualTo("delFlg", Boolean.FALSE);
            criteria.andEqualTo("carriageRouteId", detailDTO.getCarriageRouteId());
            List<RouteContractMap> routeContractMapList = routeContractMapMapper.selectByCondition(condition);
            log.info("routeContractMapList : {}", JSON.toJSONString(routeContractMapList));
            return CollectionUtils.isNotEmpty(routeContractMapList) &&
                    routeContractMapList.stream().filter(Objects::nonNull).allMatch(i -> CsStringUtils.isNotBlank(i.getContractAddressCode()));
        }
        return false;
    }

    @Override
    public TransportRouteDTO getERPContractAddress(CarriageRouteQueryDTO queryDTO) {
        CarriageRouteDetailDTO detailDTO = carriageRouteBizService.queryPointCarriageRouteDetail(queryDTO);
        if (detailDTO != null && CsStringUtils.isNotBlank(detailDTO.getCarriageRouteId())) {
            Condition condition = new Condition(RouteContractMap.class);
            Example.Criteria criteria = condition.createCriteria();
            criteria.andEqualTo("delFlg", Boolean.FALSE);
            criteria.andEqualTo("carriageRouteId", detailDTO.getCarriageRouteId());
            List<RouteContractMap> routeContractMapList = routeContractMapMapper.selectByCondition(condition);
            log.info("routeContractMapList : {}", JSON.toJSONString(routeContractMapList));
            TransportRouteDTO dto = new TransportRouteDTO();
            if (CollectionUtils.isNotEmpty(routeContractMapList)) {
                RouteContractMap routeContractMap = routeContractMapList.get(0);
                BeanUtils.copyProperties(routeContractMap, dto);
            }
            log.info("TransportRouteDTO : {}", JSON.toJSONString(dto));
            return dto;
        }
        return null;
    }

    @Override
    public boolean hasCarriageRoute(CarriageRouteQueryDTO queryDTO) {
        log.info("hasCarriageRoute_queryDTO : {}", JSON.toJSONString(queryDTO));
        List<CarriageRouteListDTO> carriageRouteListDTOS = carriageRouteBizService.queryCarriageRouteList(queryDTO);
        log.info("carriageRouteListDTOS : {}", JSON.toJSONString(carriageRouteListDTOS));
        return CollectionUtils.isNotEmpty(carriageRouteListDTOS);
    }


    private ItemResult<String> getEditExceptionItemResult(BizException e) {
        //特殊逻辑:返回code: 030034,和重复ID
        ItemResult<String> errorResult = new ItemResult<>();
        if (e.getErrorCode() != null) {
            String errorCode = e.getErrorCode().getCode();
            String name = e.getErrorCode().getName();
            String message = e.getMessage();
            errorResult.setSuccess(true);
            if (CsStringUtils.equals(name, LogisticsErrorCode.CARRIAGE_ROUTE_P2P_DUPLICATE_ID.getName())) {
                errorResult.setCode(errorCode);
                errorResult.setData(message);
                errorResult.setDescription("该路线已存在相同类型的运费定价规则");
                return errorResult;
            }
            if (CsStringUtils.equals(name, LogisticsErrorCode.CARRIAGE_ROUTE_REGION_DUPLICATE_ID.getName())) {
                errorResult.setCode(errorCode);
                errorResult.setData(message);
                errorResult.setDescription("该区域已存在相同类型的运费定价规则");
                return errorResult;
            }
        }
        errorResult.setSuccess(false);
        errorResult.setCode(e.getErrorCode().getCode());
        errorResult.setDescription(e.getMessage());
        return errorResult;
    }

}
