package com.ecommerce.logistics.service.impl;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.carriage.CarriageLogListDTO;
import com.ecommerce.logistics.api.dto.carriage.CarriageRuleDeleteDTO;
import com.ecommerce.logistics.api.dto.carriage.CarriageRuleQueryDTO;
import com.ecommerce.logistics.api.dto.carriage.CarriageRuleSaveDTO;
import com.ecommerce.logistics.api.dto.carriagerule.*;
import com.ecommerce.logistics.biz.ICarriageRuleBizService;
import com.ecommerce.logistics.biz.carriage.CarriageFactoryContext;
import com.ecommerce.logistics.service.ICarriageRuleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Author: <EMAIL>
 * Description:
 * Date: Create in 上午11:09 19/1/8
 */
@Service
public class CarriageRuleService implements ICarriageRuleService {

    @Autowired
    private ICarriageRuleBizService carriageRuleBizService;

    @Autowired
    private CarriageFactoryContext carriageFactoryContext;

    @Override
    public ItemResult<String> addCarrierCarriageRule(CarrierCarriageRuleSaveDTO carrierCarriageRuleSaveDTO) {
        return new ItemResult<>(carriageRuleBizService.addCarrierCarriageRule(carrierCarriageRuleSaveDTO));
    }

    @Override
    public ItemResult<Void> deleteCarrierCarriageRule(CarrierCarriageRuleDeleteDTO carrierCarriageRuleDeleteDTO) {
        carriageRuleBizService.deleteCarrierCarriageRule(carrierCarriageRuleDeleteDTO);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> editCarrierCarriageRule(CarrierCarriageRuleSaveDTO carrierCarriageRuleSaveDTO) {
        carriageRuleBizService.editCarrierCarriageRule(carrierCarriageRuleSaveDTO);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<CarrierCarriageRuleDetailDTO> queryCarrierCarriageRuleDetail(String carriageRuleId) {
        return new ItemResult<>(carriageRuleBizService.queryCarrierCarriageRuleDetail(carriageRuleId));
    }

    @Override
    public ItemResult<PageData<CarrierCarriageRuleListDTO>> queryCarrierCarriageRuleList(PageQuery<CarrierCarriageRuleQueryDTO> pageQuery) {
        return new ItemResult<>(carriageRuleBizService.queryCarrierCarriageRuleList(pageQuery));
    }

    @Override
    public ItemResult<String> addBuyerCarriageRule(BuyerCarriageRuleSaveDTO buyerCarriageRuleSaveDTO) {
        return new ItemResult<>(carriageRuleBizService.addBuyerCarriageRule(buyerCarriageRuleSaveDTO));
    }

    @Override
    public ItemResult<Void> deleteBuyerCarriageRule(BuyerCarriageRuleDeleteDTO buyerCarriageRuleDeleteDTO) {
        carriageRuleBizService.deleteBuyerCarriageRule(buyerCarriageRuleDeleteDTO);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> editBuyerCarriageRule(BuyerCarriageRuleSaveDTO buyerCarriageRuleSaveDTO) {
        carriageRuleBizService.editBuyerCarriageRule(buyerCarriageRuleSaveDTO);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<BuyerCarriageRuleDetailDTO> queryBuyerCarriageRuleDetail(String carriageRuleId) {
        return new ItemResult<>(carriageRuleBizService.queryBuyerCarriageRuleDetail(carriageRuleId));
    }

    @Override
    public ItemResult<PageData<BuyerCarriageRuleListDTO>> queryBuyerCarriageRuleList(PageQuery<BuyerCarriageRuleQueryDTO> pageQuery) {
        return new ItemResult<>(carriageRuleBizService.queryBuyerCarriageRuleList(pageQuery));
    }

    @Override
    public <T> ItemResult<List<String>> enteringCarriageRule(CarriageRuleSaveDTO<T> carriageRuleSaveDTO) {
        return new ItemResult<>(carriageFactoryContext.enteringCarriageRule(carriageRuleSaveDTO));
    }

    @Override
    public ItemResult<Void> deleteCarriageRule(CarriageRuleDeleteDTO carriageRuleDeleteDTO) {
        carriageFactoryContext.deleteCarriageRule(carriageRuleDeleteDTO);
        return new ItemResult<>(null);
    }

    @Override
    public <T> ItemResult<Void> editCarriageRule(CarriageRuleSaveDTO<T> carriageRuleSaveDTO) {
        carriageFactoryContext.editCarriageRule(carriageRuleSaveDTO);
        return new ItemResult<>(null);
    }

    @Override
    public <K> ItemResult<List<K>> queryCarriageRuleList(CarriageRuleQueryDTO carriageRuleQueryDTO) {
        return new ItemResult<>(carriageFactoryContext.queryCarriageRuleList(carriageRuleQueryDTO));
    }

    @Override
    public ItemResult<PageData<CarriageLogListDTO>> queryCarriageLogList(PageQuery<CarriageRuleQueryDTO> pageQuery) {
        return new ItemResult<>(carriageFactoryContext.queryCarriageLogList(pageQuery));
    }

    @Override
    public ItemResult<List<CarriageLogListDTO>> queryCarriageLogListOneYear(CarriageRuleQueryDTO carriageRuleQueryDTO) {
        return new ItemResult<>(carriageFactoryContext.queryCarriageLogListOneYear(carriageRuleQueryDTO));
    }
}
