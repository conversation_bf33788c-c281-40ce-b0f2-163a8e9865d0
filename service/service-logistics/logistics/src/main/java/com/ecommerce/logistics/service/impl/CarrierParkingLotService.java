package com.ecommerce.logistics.service.impl;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.intelligentDispatch.CarrierParkingLotDTO;
import com.ecommerce.logistics.biz.ICarrierParkingLotBizService;
import com.ecommerce.logistics.service.ICarrierParkingLotService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CarrierParkingLotService implements ICarrierParkingLotService {

    @Autowired
    private ICarrierParkingLotBizService carrierParkingLotBizService;

    @Override
    public ItemResult<Boolean> add(CarrierParkingLotDTO createDTO) {
        return new ItemResult<>(carrierParkingLotBizService.add(createDTO));
    }

    @Override
    public ItemResult<Boolean> edit(CarrierParkingLotDTO updateDTO) {
        return new ItemResult<>(carrierParkingLotBizService.edit(updateDTO));
    }

    @Override
    public ItemResult<Boolean> delete(CarrierParkingLotDTO deleteDTO) {
        return new ItemResult<>(carrierParkingLotBizService.delete(deleteDTO));
    }

    @Override
    public ItemResult<PageData<CarrierParkingLotDTO>> pageCarrierParkingLot(PageQuery<CarrierParkingLotDTO> pageQuery) {
        return new ItemResult<>(carrierParkingLotBizService.pageCarrierParkingLot(pageQuery));
    }
}
