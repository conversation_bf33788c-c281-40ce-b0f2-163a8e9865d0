package com.ecommerce.logistics.service.impl;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.intelligentDispatch.CarrierServiceAreaDTO;
import com.ecommerce.logistics.biz.ICarrierServiceAreaBizService;
import com.ecommerce.logistics.service.ICarrierServiceAreaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CarrierServiceAreaService implements ICarrierServiceAreaService {

    @Autowired
    private ICarrierServiceAreaBizService carrierServiceAreaBizService;

    @Override
    public ItemResult<Boolean> add(CarrierServiceAreaDTO createDTO) {
        return new ItemResult<>(carrierServiceAreaBizService.add(createDTO));
    }

    @Override
    public ItemResult<Boolean> edit(CarrierServiceAreaDTO updateDTO) {
        return new ItemResult<>(carrierServiceAreaBizService.edit(updateDTO));
    }

    @Override
    public ItemResult<Boolean> delete(CarrierServiceAreaDTO deleteDTO) {
        return new ItemResult<>(carrierServiceAreaBizService.delete(deleteDTO));
    }

    @Override
    public ItemResult<PageData<CarrierServiceAreaDTO>> pageCarrierServiceArea(PageQuery<CarrierServiceAreaDTO> pageQuery) {
        return new ItemResult<>(carrierServiceAreaBizService.pageCarrierServiceArea(pageQuery));
    }

    @Override
    public ItemResult<CarrierServiceAreaDTO> queryDetailById(String serviceAreaId) {
        return new ItemResult<>(carrierServiceAreaBizService.queryDetailById(serviceAreaId));
    }
}
