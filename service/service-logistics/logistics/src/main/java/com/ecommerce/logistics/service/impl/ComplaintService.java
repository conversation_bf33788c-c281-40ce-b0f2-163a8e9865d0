package com.ecommerce.logistics.service.impl;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.complaint.ComplaintDTO;
import com.ecommerce.logistics.api.param.complaint.ComplaintBatchCancelParam;
import com.ecommerce.logistics.api.param.complaint.ComplaintBatchDelParam;
import com.ecommerce.logistics.api.param.complaint.ComplaintDealFinishParam;
import com.ecommerce.logistics.api.param.complaint.ComplaintEvaluationParam;
import com.ecommerce.logistics.api.param.complaint.ComplaintFollowParam;
import com.ecommerce.logistics.api.param.complaint.ComplaintParam;
import com.ecommerce.logistics.api.param.complaint.ComplaintQueryParam;
import com.ecommerce.logistics.api.param.complaint.ComplaintResponsibleParam;
import com.ecommerce.logistics.biz.IComplaintBizService;
import com.ecommerce.logistics.biz.ICreditStatisticBizService;
import com.ecommerce.logistics.dao.vo.CreditStatistic;
import com.ecommerce.logistics.service.IComplaintService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Copyright (C),2020
 *
 * @ClassName: ComplaintService
 * @author: <EMAIL>
 * @Date: 25/09/2020 16:07
 * @Description:
 */
@Service
public class ComplaintService implements IComplaintService {

    @Autowired
    private IComplaintBizService complaintBizService;

    @Autowired
    private ICreditStatisticBizService creditStatisticBizService;

    /**
     * <方法描述>
     *
     * @param <入参> [param]
     * @return <出参> com.ecommerce.common.result.ItemResult<com.ecommerce.common.result.PageData<com.ecommerce.logistics.api.dto
     * .complaint.ComplaintDTO>>
     * @title <方法描述> pageByQueryOption
     * @tables <涉及表说明，便于调用方调试>
     * @special <特殊说明>
     * <AUTHOR>
     * @date 25/09/2020 16:07
     * @since <版本号>
     */
    @Override
    public ItemResult<PageData<ComplaintDTO>> pageByQueryOption(PageQuery<ComplaintQueryParam> param) {
        return new ItemResult<>(complaintBizService.pageByQueryOption(param));
    }


    /**
     * <方法描述>
     *
     * @param <入参> [complaintParam]
     * @return <出参> com.ecommerce.common.result.ItemResult<java.lang.Boolean>
     * @title <方法描述> addOrUpdateComplaint
     * @tables <涉及表说明，便于调用方调试>
     * @special <特殊说明>
     * <AUTHOR>
     * @date 25/09/2020 16:08
     * @since <版本号>
     */
    @Override
    public ItemResult<Boolean> addOrUpdateComplaint(ComplaintParam complaintParam) {
        return new ItemResult<>(complaintBizService.addOrUpdateComplaint(complaintParam));
    }

    @Override
    public ItemResult<Boolean> batchDelComplaint(ComplaintBatchDelParam complaintBatchDelParam) {
        return new ItemResult<>(complaintBizService.batchDelComplaint(complaintBatchDelParam));
    }

    @Override
    public ItemResult<Boolean> batchCancelComplaint(ComplaintBatchCancelParam complaintBatchCancelParam) {
        return new ItemResult<>(complaintBizService.batchCancelComplaint(complaintBatchCancelParam));
    }

    @Override
    public ItemResult<Boolean> responsibleComplaint(ComplaintResponsibleParam complaintResponsibleParam) {
        //投诉归责时，如果责任方为承运商或者司机，则添加其客诉次数
        CreditStatistic creditStatistic = creditStatisticBizService.findCreditStatisticById(complaintResponsibleParam.getResponsibleRoleId(),complaintResponsibleParam.getResponsibleRoleType());
        //如果存在记录，则其客诉次数+1
        if (creditStatistic != null && CsStringUtils.isNotBlank(creditStatistic.getPersonId())) {
            creditStatisticBizService.updateCreditStatisticCount(creditStatistic.getPersonId(),false,false,true,complaintResponsibleParam.getUpdateUser());
        }
        return new ItemResult<>(complaintBizService.responsibleComplaint(complaintResponsibleParam));
    }

    @Override
    public ItemResult<Boolean> followComplaint(ComplaintFollowParam complaintFollowParam) {
        return new ItemResult<>(complaintBizService.followComplaint(complaintFollowParam));
    }

    @Override
    public ItemResult<Boolean> dealFinishComplaint(ComplaintDealFinishParam complaintDealFinishParam) {
        return new ItemResult<>(complaintBizService.dealFinishComplaint(complaintDealFinishParam));
    }

    @Override
    public ItemResult<Boolean> evaluationComplaint(ComplaintEvaluationParam complaintEvaluationParam) {
        return new ItemResult<>(complaintBizService.evaluationComplaint(complaintEvaluationParam));
    }
}
