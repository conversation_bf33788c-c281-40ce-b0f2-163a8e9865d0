package com.ecommerce.logistics.service.impl;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.creditStatistic.CreditStatisticAddDTO;
import com.ecommerce.logistics.api.dto.creditStatistic.CreditStatisticDTO;
import com.ecommerce.logistics.api.dto.creditStatistic.CreditStatisticListDTO;
import com.ecommerce.logistics.api.dto.creditStatistic.CreditStatisticQueryDTO;
import com.ecommerce.logistics.biz.ICreditStatisticBizService;
import com.ecommerce.logistics.dao.vo.CreditStatistic;
import com.ecommerce.logistics.service.ICreditStatisticService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Description: CreditStatisticService
 * @Author: <EMAIL>
 * @Date: 06/11/2020 14:22
 */
@Service
public class CreditStatisticService implements ICreditStatisticService {

    @Autowired
    private ICreditStatisticBizService creditStatisticBizService;

    @Override
    public ItemResult<String> addCreditStatistic(CreditStatisticAddDTO addDTO) {
        return new ItemResult<>(creditStatisticBizService.addCreditStatistic(addDTO));
    }

    @Override
    public ItemResult<Void> updateCreditStatisticCount(String personId, Boolean billCount, Boolean breakCount, Boolean complaintCount, String operatorUserId) {
        creditStatisticBizService.updateCreditStatisticCount(personId, billCount, breakCount, complaintCount, operatorUserId);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<PageData<CreditStatisticListDTO>> queryCreditStatisticList(PageQuery<CreditStatisticQueryDTO> pageQuery) {
        return new ItemResult<>(creditStatisticBizService.queryCreditStatisticList(pageQuery));
    }

    @Override
    public ItemResult<CreditStatisticDTO> findCreditStatisticById(String personId, String personType) {
        CreditStatistic creditStatistic = creditStatisticBizService.findCreditStatisticById(personId, personType);
        CreditStatisticDTO creditStatisticDTO = new CreditStatisticDTO();
        if(creditStatistic != null){
            BeanUtils.copyProperties(creditStatistic,creditStatisticDTO);
        }
        return new ItemResult<>(creditStatisticDTO);
    }
}
