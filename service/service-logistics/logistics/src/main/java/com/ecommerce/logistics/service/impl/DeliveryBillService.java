package com.ecommerce.logistics.service.impl;

import com.alibaba.fastjson.JSON;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.exception.DistributeLockException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.common.service.ILockService;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.logistics.api.dto.*;
import com.ecommerce.logistics.api.dto.assign.AssignShipDTO;
import com.ecommerce.logistics.api.dto.assign.AssignVehicleDTO;
import com.ecommerce.logistics.api.dto.auto.DispatchResultOperationDTO;
import com.ecommerce.logistics.api.dto.auto.InstantAutoDispatchCondDTO;
import com.ecommerce.logistics.api.dto.auto.PlanAutoDispatchCondDTO;
import com.ecommerce.logistics.api.dto.auto.VehicleAutoAssignDTO;
import com.ecommerce.logistics.api.dto.auto.VehicleAutoAssignQueryDTO;
import com.ecommerce.logistics.api.dto.auto.WaitPlanAutoDispatchDTO;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.delivery.InternalAllocationDTO;
import com.ecommerce.logistics.api.dto.intelligentDispatch.IntelligentDispatchQueryDTO;
import com.ecommerce.logistics.api.dto.intelligentDispatch.IntelligentDispatchRuleDTO;
import com.ecommerce.logistics.api.dto.operationrecord.OperationRecordDTO;
import com.ecommerce.logistics.api.dto.operationrecord.OperationRecordQueryDTO;
import com.ecommerce.logistics.api.dto.pickingbill.AssignDetailDTO;
import com.ecommerce.logistics.api.dto.pickingbill.OptionDTO;
import com.ecommerce.logistics.api.dto.productinfo.ProductDetailDTO;
import com.ecommerce.logistics.api.dto.shipbill.ShipBillDetailDTO;
import com.ecommerce.logistics.api.dto.waybill.ContractSubmitInfoDTO;
import com.ecommerce.logistics.api.dto.waybill.CreateShipBillDTO;
import com.ecommerce.logistics.api.dto.waybill.ExternalWaybillRequestDTO;
import com.ecommerce.logistics.api.dto.waybill.RerouteRecordLogDTO;
import com.ecommerce.logistics.api.enums.*;
import com.ecommerce.logistics.biz.*;
import com.ecommerce.logistics.biz.auto.IAutoDispatchBizService;
import com.ecommerce.logistics.biz.auto.IDispatchResultBizService;
import com.ecommerce.logistics.biz.auto.IDispatchRuleOperateBizService;
import com.ecommerce.logistics.biz.delivery.IDeliveryBillAssignBizService;
import com.ecommerce.logistics.biz.delivery.IDeliveryBillBackBizService;
import com.ecommerce.logistics.biz.delivery.IDeliveryBillMergeBizService;
import com.ecommerce.logistics.biz.delivery.IDeliveryBillOperateBizService;
import com.ecommerce.logistics.biz.delivery.IDeliveryBillQueryBizService;
import com.ecommerce.logistics.biz.delivery.IDeliveryInfoBizService;
import com.ecommerce.logistics.dao.dto.auto.DispatchRuleContext;
import com.ecommerce.logistics.dao.dto.auto.VehiclePlanParam;
import com.ecommerce.logistics.dao.dto.delivery.DeliveryRerouteResult;
import com.ecommerce.logistics.dao.dto.delivery.InternalAllocationBillDTO;
import com.ecommerce.logistics.dao.mapper.IntelligentDispatchRuleMapper;
import com.ecommerce.logistics.dao.vo.DeliveryBill;
import com.ecommerce.logistics.dao.vo.DeliveryInfo;
import com.ecommerce.logistics.dao.vo.InternalAllocation;
import com.ecommerce.logistics.dao.vo.ShipBill;
import com.ecommerce.logistics.service.IDeliveryBillService;
import com.ecommerce.logistics.service.IWaybillNewExternalService;
import com.ecommerce.logistics.util.DigestUtils;
import com.ecommerce.logistics.util.LogisticsUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Auther: colu
 * @Date: 2020-12-11 15:52
 * @Description: 委托单服务接口
 */
@Slf4j
@Service("deliveryBillService")
public class DeliveryBillService implements IDeliveryBillService {

    @Autowired
    private IAutoDispatchBizService autoDispatchBizService;

    @Autowired
    private IDeliveryInfoBizService deliveryInfoBizService;

    @Autowired
    private IDeliveryBillAssignBizService deliveryBillAssignBizService;

    @Autowired
    private IDeliveryBillMergeBizService deliveryBillMergeBizService;

    @Autowired
    private IWaybillNewExternalService waybillNewExternalService;

    @Autowired
    private ILockService redisLockService;

    @Autowired
    private IDeliveryBillQueryBizService deliveryBillQueryBizService;

    @Autowired
    private IOperationRecordBizService operationRecordBizService;

    @Autowired
    private IDeliveryBillOperateBizService deliveryBillOperateBizService;

    @Autowired
    private IDeliveryBillBackBizService deliveryBillBackBizService;

    @Autowired
    private IShipBillQueryBizService shipBillQueryBizService;

    @Autowired
    private IShipBillOperateBizService shipBillOperateBizService;

    @Autowired
    private IDispatchRuleOperateBizService dispatchRuleOperateBizService;

    @Autowired(required = false)
    private IntelligentDispatchRuleMapper intelligentDispatchRuleMapper;

    @Autowired
    private IDispatchResultBizService dispatchResultBizService;

    @Autowired
    private IInternalAllocationBizService allocationBizService;

    @Autowired
    private IRerouteRecordLogBizService rerouteRecordLogBizService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ItemResult<Void> enteringProxyDeliveryBill(EnteringProxyDeliveryBillDTO enteringProxyDeliveryBillDTO) {
        ItemResult<Void> itemResult = new ItemResult<>();
        //1.校验合法性
        log.info("enteringProxyDeliveryBillDTO:{}", enteringProxyDeliveryBillDTO);
        if (enteringProxyDeliveryBillDTO == null ||
                enteringProxyDeliveryBillDTO.getPrimaryDeliveryInfo() == null ||
                enteringProxyDeliveryBillDTO.getProxyDeliveryInfo() == null) {
            throw new BizException(BasicCode.PARAM_NULL, "配送信息");
        }

        String identifier = "";
        String bizResource = DigestUtils.md5Digest(enteringProxyDeliveryBillDTO.toString());
        try {
            identifier = redisLockService.lockFast(bizResource);

            DeliveryInfoDTO primaryInfoDTO = new DeliveryInfoDTO();
            DeliveryInfoDTO secondaryInfoDTO = new DeliveryInfoDTO();

            BeanUtils.copyProperties(enteringProxyDeliveryBillDTO.getPrimaryDeliveryInfo(), primaryInfoDTO);
            BeanUtils.copyProperties(enteringProxyDeliveryBillDTO.getProxyDeliveryInfo(), secondaryInfoDTO);
            primaryInfoDTO.setBillProxyType(BillProxyTypeEnum.PRIMARY.getCode());
            primaryInfoDTO.setCarriageUnitPrice(enteringProxyDeliveryBillDTO.getPrimaryDeliveryInfo().getReceivableCarriagePrice());
            primaryInfoDTO.setOperationUserId(enteringProxyDeliveryBillDTO.getOperationUserId());
            primaryInfoDTO.setOperationUserName(enteringProxyDeliveryBillDTO.getOperationUserName());

            secondaryInfoDTO.setBillProxyType(BillProxyTypeEnum.SECONDARY.getCode());
            secondaryInfoDTO.setCarriageUnitPrice(enteringProxyDeliveryBillDTO.getProxyDeliveryInfo().getReceivableCarriagePrice());
            secondaryInfoDTO.setOperationUserId(enteringProxyDeliveryBillDTO.getOperationUserId());
            secondaryInfoDTO.setOperationUserName(enteringProxyDeliveryBillDTO.getOperationUserName());

            deliveryInfoBizService.fillWarehouseInfo(primaryInfoDTO);
            deliveryInfoBizService.fillWarehouseInfo(secondaryInfoDTO);

            //保存两级的交易信息:deliveryInfo
            deliveryInfoBizService.createProxyGroupDeliveryInfo(primaryInfoDTO, secondaryInfoDTO);

            BigDecimal quantity = enteringProxyDeliveryBillDTO.getPrimaryDeliveryInfo().getQuantity();
            //创建根级deliveryBill
            String canOperateBillId;

            DeliveryBillRootCreateDTO primaryRootCreateDTO = deliveryInfoBizService.covertInfoToRootBill(primaryInfoDTO, quantity);
            DeliveryBillRootCreateDTO secondaryRootCreateDTO = deliveryInfoBizService.covertInfoToRootBill(secondaryInfoDTO, quantity);

            secondaryRootCreateDTO.setWarehouseType(primaryInfoDTO.getWarehouseType());
            secondaryRootCreateDTO.setWarehouseName(primaryInfoDTO.getWarehouseName());
            secondaryRootCreateDTO.setWarehouseProvince(primaryInfoDTO.getWarehouseProvince());
            secondaryRootCreateDTO.setWarehouseProvinceCode(primaryInfoDTO.getWarehouseProvinceCode());
            secondaryRootCreateDTO.setWarehouseCity(primaryInfoDTO.getWarehouseCity());
            secondaryRootCreateDTO.setWarehouseCityCode(primaryInfoDTO.getWarehouseCityCode());
            secondaryRootCreateDTO.setWarehouseDistrict(primaryInfoDTO.getWarehouseDistrict());
            secondaryRootCreateDTO.setWarehouseDistrictCode(primaryInfoDTO.getWarehouseDistrictCode());
            secondaryRootCreateDTO.setWarehouseAddress(primaryInfoDTO.getWarehouseAddress());
            secondaryRootCreateDTO.setWarehouseLocation(primaryInfoDTO.getWarehouseLocation());

            if(LogisticsUtils.isSame(primaryInfoDTO.getCanOperate(),(byte)1)){
                canOperateBillId = deliveryBillAssignBizService.createProxyGroupRootDeliveryBill(
                        primaryRootCreateDTO,
                        secondaryRootCreateDTO
                );
            }else {
                canOperateBillId = deliveryBillAssignBizService.createProxyGroupRootDeliveryBill(
                        secondaryRootCreateDTO,
                        primaryRootCreateDTO
                );
            }

            //指派
            if (CollectionUtils.isNotEmpty(enteringProxyDeliveryBillDTO.getAssignCarrierDTOList())) {
                //指派承运商
                deliveryBillAssignBizService.assignCarrier(canOperateBillId,
                        enteringProxyDeliveryBillDTO.getAssignCarrierDTOList(),
                        enteringProxyDeliveryBillDTO.getOperationUserId(),
                        enteringProxyDeliveryBillDTO.getOperationUserName());

                //交易传物流指派承运商后承运商直接接受委托
                List<DeliveryBill> deliveryBills = deliveryBillQueryBizService.queryChild(canOperateBillId);
                if(CollectionUtils.isNotEmpty(deliveryBills)){
                    deliveryBills.forEach(deliveryBill ->
                        deliveryBillOperateBizService.acceptDelivery(deliveryBill.getDeliveryBillId(),enteringProxyDeliveryBillDTO.getOperationUserId(), enteringProxyDeliveryBillDTO.getOperationUserName())
                    );
                }

            }
            if (CollectionUtils.isNotEmpty(enteringProxyDeliveryBillDTO.getAssignShipDTOList())) {
                //指派船舶
                deliveryBillAssignBizService.assignShip(canOperateBillId,
                        enteringProxyDeliveryBillDTO.getAssignShipDTOList(),
                        enteringProxyDeliveryBillDTO.getOperationUserId(),
                        enteringProxyDeliveryBillDTO.getOperationUserName());
            }
            if (CollectionUtils.isNotEmpty(enteringProxyDeliveryBillDTO.getAssignVehicleDTOList())) {
                //指派车辆
                deliveryBillAssignBizService.assignVehicle(canOperateBillId,
                        enteringProxyDeliveryBillDTO.getAssignVehicleDTOList(),
                        enteringProxyDeliveryBillDTO.getOperationUserId(),
                        enteringProxyDeliveryBillDTO.getOperationUserName());
            }
        } catch (DistributeLockException e) {
            log.info("重复操作: enteringProxyDeliveryBillDTO={}", enteringProxyDeliveryBillDTO);
            throw new BizException(BasicCode.UNDEFINED_ERROR, "有其他用户正在操作，请刷新页面后重试");
        } finally {
            if (CsStringUtils.isNotEmpty(identifier)) {
                redisLockService.unlock(bizResource, identifier);
            }
        }

        itemResult.setSuccess(true);
        return itemResult;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ItemResult<InternalAllocationBillDTO> enteringNormalDeliveryBill(EnteringNormalDeliveryBillDTO enteringNormalDeliveryBillDTO) {
        InternalAllocationBillDTO allocationBillDTO = new InternalAllocationBillDTO();
        //1.校验合法性
        log.info("enteringNormalDeliveryBillDTO:{}", JSON.toJSONString(enteringNormalDeliveryBillDTO));
        if (enteringNormalDeliveryBillDTO == null ||
                enteringNormalDeliveryBillDTO.getPrimaryDeliveryInfo() == null) {
            throw new BizException(BasicCode.PARAM_NULL, "配送信息");
        }

        String identifier = "";
        String bizResource = DigestUtils.md5Digest(enteringNormalDeliveryBillDTO.toString());
        try {
            identifier = redisLockService.lockFast(bizResource);

            DeliveryInfoDTO normalInfoDTO = new DeliveryInfoDTO();
            BeanUtils.copyProperties(enteringNormalDeliveryBillDTO.getPrimaryDeliveryInfo(), normalInfoDTO);
            normalInfoDTO.setCarriageUnitPrice(enteringNormalDeliveryBillDTO.getPrimaryDeliveryInfo().getReceivableCarriagePrice());
            normalInfoDTO.setOperationUserId(enteringNormalDeliveryBillDTO.getOperationUserId());
            normalInfoDTO.setOperationUserName(enteringNormalDeliveryBillDTO.getOperationUserName());
            deliveryInfoBizService.fillWarehouseInfo(normalInfoDTO);
            //保存交易信息:deliveryInfo
            String deliveryInfoId = deliveryInfoBizService.createDeliveryInfo(normalInfoDTO);
            allocationBillDTO.setDeliveryInfoId(deliveryInfoId);
            normalInfoDTO.setDeliveryInfoId(CsStringUtils.isBlank(normalInfoDTO.getDeliveryInfoId()) ? deliveryInfoId : normalInfoDTO.getDeliveryInfoId());

            //创建根级deliveryBill
            DeliveryBillRootCreateDTO rootCreateDTO = deliveryInfoBizService.covertInfoToRootBill(normalInfoDTO,
                    enteringNormalDeliveryBillDTO.getPrimaryDeliveryInfo().getQuantity());
            String deliveryBillId = deliveryBillAssignBizService.createRootDeliveryBill(rootCreateDTO);
            allocationBillDTO.setDeliveryBillId(deliveryBillId);

            //指派
            if (CollectionUtils.isNotEmpty(enteringNormalDeliveryBillDTO.getAssignCarrierDTOList())) {
                //指派承运商
                deliveryBillAssignBizService.assignCarrier(deliveryBillId,
                        enteringNormalDeliveryBillDTO.getAssignCarrierDTOList(),
                        enteringNormalDeliveryBillDTO.getOperationUserId(),
                        enteringNormalDeliveryBillDTO.getOperationUserName());

                //交易传物流指派承运商后承运商直接接受委托
                List<DeliveryBill> deliveryBills = deliveryBillQueryBizService.queryChild(deliveryBillId);
                if(CollectionUtils.isNotEmpty(deliveryBills)){
                    log.info("自动接受委托：{}", JSON.toJSONString(deliveryBills));
                    deliveryBills.forEach(deliveryBill ->
                        deliveryBillOperateBizService.acceptDelivery(deliveryBill.getDeliveryBillId(),enteringNormalDeliveryBillDTO.getOperationUserId(), enteringNormalDeliveryBillDTO.getOperationUserName())
                    );
                }

            }
            if (CollectionUtils.isNotEmpty(enteringNormalDeliveryBillDTO.getAssignShipDTOList())) {
                //指派船舶
                List<CreateShipBillDTO> createShipBillDTOList = deliveryBillAssignBizService.assignShip(deliveryBillId,
                        enteringNormalDeliveryBillDTO.getAssignShipDTOList(),
                        enteringNormalDeliveryBillDTO.getOperationUserId(),
                        enteringNormalDeliveryBillDTO.getOperationUserName());
                allocationBillDTO.setShipBillList(createShipBillDTOList);
            }
            if (CollectionUtils.isNotEmpty(enteringNormalDeliveryBillDTO.getAssignVehicleDTOList())) {
                //指派车辆
                deliveryBillAssignBizService.assignVehicle(deliveryBillId,
                        enteringNormalDeliveryBillDTO.getAssignVehicleDTOList(),
                        enteringNormalDeliveryBillDTO.getOperationUserId(),
                        enteringNormalDeliveryBillDTO.getOperationUserName());
            }
        } catch (DistributeLockException e) {
            log.info("重复操作: enteringNormalDeliveryBillDTO={}", enteringNormalDeliveryBillDTO);
            throw new BizException(BasicCode.UNDEFINED_ERROR, "有其他用户正在操作，请刷新页面后重试");
        } finally {
            if (CsStringUtils.isNotEmpty(identifier)) {
                redisLockService.unlock(bizResource, identifier);
            }
        }
        return new ItemResult<>(allocationBillDTO);
    }

    @Override
    public ItemResult<Void> assignCarrier(String deliveryBillId, List<AssignDetailDTO> assignCarrierDTOList, String operationUserId, String operationUserName) {
        deliveryBillAssignBizService.assignCarrier(deliveryBillId, assignCarrierDTOList, operationUserId, operationUserName);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<List<CreateShipBillDTO>> assignShip(String deliveryBillId, List<AssignShipDTO> assignShipDTOList, String operationUserId, String operationUserName) {
        return new ItemResult<>(deliveryBillAssignBizService.assignShip(deliveryBillId, assignShipDTOList, operationUserId, operationUserName));
    }

    @Override
    public ItemResult<Void> assignVehicle(String deliveryBillId, List<AssignVehicleDTO> assignVehicleDTOList, String operationUserId, String operationUserName) {
        deliveryBillAssignBizService.assignVehicle(deliveryBillId, assignVehicleDTOList, operationUserId, operationUserName);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<PageData<DeliveryListDTO>> queryDeliveryList(PageQuery<DeliveryQueryDTO> pageQuery) {
        return new ItemResult<>(deliveryBillQueryBizService.queryDeliveryList(pageQuery));
    }

    @Override
    public ItemResult<PageData<DeliveryListDTO>> queryWaitMergeDeliveryList(PageQuery<WaitMergeDeliveryQueryDTO> pageQuery) {
        return new ItemResult<>(deliveryBillQueryBizService.queryWaitMergeDeliveryList(pageQuery));
    }

    @Override
    public ItemResult<Void> mergeAssign(MergeDeliveryAssignDTO mergeDeliveryAssignDTO) {
        List<ShipBill> shipBillList = deliveryBillMergeBizService.mergeAssign(mergeDeliveryAssignDTO);
        if (CollectionUtils.isNotEmpty(shipBillList)) {
            for (ShipBill shipBill : shipBillList) {
                if (CsStringUtils.equals(shipBill.getSyncFlag(), ExternalSyncFlagEnum.FORWARD_SYNC.getCode())) {
                    ExternalWaybillRequestDTO externalWaybillRequestDTO = new ExternalWaybillRequestDTO();
                    externalWaybillRequestDTO.setWaybillId(shipBill.getWaybillId());
                    externalWaybillRequestDTO.setOperatorUserId(mergeDeliveryAssignDTO.getOperationUserId());
                    externalWaybillRequestDTO.setOperatorUserName(mergeDeliveryAssignDTO.getOperationUserName());
                    waybillNewExternalService.createExternalWaybill(externalWaybillRequestDTO);
                }
            }
        }
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<DeliveryDetailDTO> queryDeliveryDetailById(String deliveryBillId) {
        DeliveryDetailDTO deliveryDetailDTO = deliveryBillQueryBizService.queryDeliveryDetailById(deliveryBillId);
        if(deliveryDetailDTO != null){
            if (CsStringUtils.equals(deliveryDetailDTO.getType(), PickingBillTypeEnum.SELLER_DELIVERY.getCode()) &&
                    !CsStringUtils.equals(deliveryDetailDTO.getSyncFlag(), ExternalSyncFlagEnum.NO_SYNC.getCode()) &&
                    CsStringUtils.equals(deliveryDetailDTO.getTransportToolType(), TransportToolTypeEnum.ROAD_TRANSPORT.getCode()))
                deliveryDetailDTO.setBidAssignFlag(1);
            //设置委托过程记录DTO
            OperationRecordQueryDTO queryDTO = new OperationRecordQueryDTO();
            queryDTO.setEntryId(deliveryBillId);
            queryDTO.setType(OperationRecordTypeEnum.DELIVERY_BILL.getCode());
            List<OperationRecordDTO> operationRecordList = operationRecordBizService.queryOperation(queryDTO);
            deliveryDetailDTO.setOperationRecordDTOS(operationRecordList);

            //设置委托运输信息DTO
            List<DeliveryDetailVehicleDTO> deliveryDetailVehicleDTOS = shipBillQueryBizService.queryAllShipBillByDeliveryBillId(deliveryBillId);
            deliveryDetailDTO.setDeliveryDetailVehicleDTOS(deliveryDetailVehicleDTOS);
        }
        return new ItemResult<>(deliveryDetailDTO);
    }

    @Override
    public ItemResult<Void> acceptDelivery(String deliveryBillId, String operationUserId, String operationUserName) {
        deliveryBillOperateBizService.acceptDelivery(deliveryBillId, operationUserId, operationUserName);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> rejectDelivery(String deliveryBillId, String reason, String operationUserId, String operationUserName) {
        deliveryBillOperateBizService.rejectDelivery(deliveryBillId, reason, operationUserId, operationUserName);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> cancelDelivery(String deliveryBillId, String operationUserId, String operationUserName) {
        deliveryBillOperateBizService.cancelDelivery(deliveryBillId, operationUserId, operationUserName);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> stopDelivery(String deliveryBillId,String reason, String operationUserId, String operationUserName) {
        deliveryBillOperateBizService.stopDeliveryTree(deliveryBillId, reason, operationUserId, operationUserName);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<BigDecimal> queryPayableCarriagePrice(PayableCarriagePriceQueryDTO queryDTO) {
        return new ItemResult<>(deliveryBillQueryBizService.queryPayableCarriagePrice(queryDTO));
    }

    @Override
    public ItemResult<DeliveryRerouteInfoDTO> queryCanRerouteInfo(DeliveryRerouteCondDTO deliveryRerouteCondDTO) {
        log.info("查询可改航的运单:{}", deliveryRerouteCondDTO);
        return new ItemResult<>(deliveryBillQueryBizService.queryDeliveryRerouteInfoById(deliveryRerouteCondDTO.getDeliveryBillId()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ItemResult<Void> reroute(DeliveryRerouteDTO deliveryRerouteDTO) {
        log.info("开始改航:{}", deliveryRerouteDTO);
        ShipBillDetailDTO shipBillDetailDTO = shipBillQueryBizService.selectWaybillDetailByWaybillItemId(deliveryRerouteDTO.getWaybillItemId());
        ProductDetailDTO productDetail = shipBillDetailDTO.getProductDetails().get(0);
        if (productDetail == null) {
            throw new BizException(BasicCode.PARAM_NULL, "商品详情");
        }
        DeliveryRerouteResult result = deliveryBillAssignBizService.rerouteChangeDelivery(deliveryRerouteDTO.getFromDeliveryBillId(),
                deliveryRerouteDTO.getToDeliveryBillId(),
                productDetail.getQuantity(),
                productDetail.getActualQuantity(),
                deliveryRerouteDTO.getOperationUserId(),
                deliveryRerouteDTO.getOperationUserName());

        result.setWaybillId(deliveryRerouteDTO.getWaybillId());
        result.setWaybillItemId(deliveryRerouteDTO.getWaybillItemId());
        result.setUnloadPortId(deliveryRerouteDTO.getUnloadPortId());
        result.setUnloadPortName(deliveryRerouteDTO.getUnloadPortName());
        //改航后的委托单ID
        result.setToDeliveryBillId(deliveryRerouteDTO.getToDeliveryBillId());
        DeliveryInfo newDeliveryInfo = shipBillOperateBizService.rerouteShipBill(result);
        //如果是内部调拨单，则同步完成内部调拨单据
        if (CsStringUtils.equals("01", shipBillDetailDTO.getInternal())) {
            //修改运单对应得原内部调拨单得状态为已改派
            InternalAllocationDTO oldInternalDTO = new InternalAllocationDTO();
            oldInternalDTO.setWaybillId(shipBillDetailDTO.getWaybillId());
            //1待发货,2发货中,3已取消4,已改派,5已完成
            oldInternalDTO.setPlanStatus(PlanStatusEnum.REASSIGNED.getCode());
            oldInternalDTO.setActualQuantity(BigDecimal.ZERO);
            oldInternalDTO.setUserId(deliveryRerouteDTO.getOperationUserId());
            allocationBizService.updateInternalPlanStatus(oldInternalDTO);

            InternalAllocationDTO newInternalDTO = new InternalAllocationDTO();
            newInternalDTO.setDeliveryInfoId(newDeliveryInfo.getDeliveryInfoId());
            //修改内部调拨单得实际提货量
            newInternalDTO.setActualQuantity(shipBillDetailDTO.getActualQuantity());
            newInternalDTO.setPlanStatus(PlanStatusEnum.DELIVERING.getCode());
            newInternalDTO.setUserId(deliveryRerouteDTO.getOperationUserId());
            allocationBizService.updateInternalPlanStatus(newInternalDTO);
        }

        RerouteRecordLogDTO recordLogDTO = RerouteRecordLogDTO.builder()
                .waybillId(deliveryRerouteDTO.getWaybillId())
                .waybillNum(deliveryRerouteDTO.getWaybillNum())
                .currReceiveMemberId(deliveryRerouteDTO.getCurrReceiveMemberId())
                .currReceiveMemberName(deliveryRerouteDTO.getCurrReceiveMemberName())
                .currDeliveryBillId(deliveryRerouteDTO.getToDeliveryBillId())
                .currDeliveryBillNum(deliveryRerouteDTO.getToDeliveryBillNum())
                .currUnloadPortId(deliveryRerouteDTO.getCurrUnloadPortId())
                .currUnloadPortName(deliveryRerouteDTO.getCurrUnloadPortName())
                .originalReceiveMemberId(deliveryRerouteDTO.getOriginalReceiveMemberId())
                .originalReceiveMemberName(deliveryRerouteDTO.getOriginalReceiveMemberName())
                .originalDeliveryBillId(deliveryRerouteDTO.getFromDeliveryBillId())
                .originalDeliveryBillNum(deliveryRerouteDTO.getFromDeliveryBillNum())
                .originalUnloadPortId(deliveryRerouteDTO.getOriginalUnloadPortId())
                .originalUnloadPortName(deliveryRerouteDTO.getOriginalUnloadPortName())
                .createUserId(deliveryRerouteDTO.getOperationUserId())
                .createUserName(deliveryRerouteDTO.getOperationUserName())
                .build();
        rerouteRecordLogBizService.addRerouteRecordLog(recordLogDTO);

        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<List<DeliverySimpleDTO>> queryMemberInfoForCheckRule(DeliverySimpleQueryDTO deliverySimpleQueryDTO) {
        return new ItemResult<>(deliveryBillQueryBizService.queryMemberInfoForCheckRule(deliverySimpleQueryDTO));
    }

    @Override
    public void finishTakeCode(String takeCode, String operatorName) {
        deliveryBillBackBizService.finishTakeCode(takeCode, operatorName);
    }

    @Override
    public void closeTakeCode(String takeCode, String operatorName) {
        deliveryBillBackBizService.closeTakeCode(takeCode, operatorName);
    }
	@Override
	public ItemResult<DeliveryStatisticsDTO> statisticsDeliveryForTheSomeDay(DeliveryQueryDTO dto) {
		return new ItemResult<>(deliveryBillQueryBizService.statisticsDeliveryForTheSomeDay(dto));
	}

	@Override
	public ItemResult<DeliveryStatisticsDTO> statisticsDeliveryForTheSomeMonth(DeliveryQueryDTO dto) {
		return new ItemResult<>(deliveryBillQueryBizService.statisticsDeliveryForTheSomeMonth(dto));
	}

    /**
     * 紧急任务列表分页查询
     */
    @Override
    public ItemResult<List<DeliveryListDTO>> queryUrgentTaskList(QueryUrgentTaskDTO query){
        List<DeliveryListDTO> deliveryListDTOS = deliveryBillQueryBizService.queryUrgentTaskList(query);
        ItemResult<List<DeliveryListDTO>> itemResult = new ItemResult<>(deliveryListDTOS);
        itemResult.setSid(String.valueOf(deliveryListDTOS.size()));
        return itemResult;
    }

    @Override
    public ItemResult<List<VehicleAutoAssignDTO>> instantVehicleDispatch(InstantAutoDispatchCondDTO instantAutoDispatchCondDTO) {
        log.info("开始即时调度:{}", instantAutoDispatchCondDTO);

        String deliveryBillId = instantAutoDispatchCondDTO.getDeliveryBillId();

        List<String> vehicleIdList = autoDispatchBizService.canAssignVehicleIdList(instantAutoDispatchCondDTO.getUserId(),
                instantAutoDispatchCondDTO.getUserType(), instantAutoDispatchCondDTO.getVehicleTypeId());

        IntelligentDispatchQueryDTO queryDTO = new IntelligentDispatchQueryDTO();
        //服务方ID
        queryDTO.setUserId(instantAutoDispatchCondDTO.getUserId());

        queryDTO.setUserType(instantAutoDispatchCondDTO.getUserType());
        //规则适用对象,0-承运商,1-整车车辆物流,2-零单车辆物
        if(instantAutoDispatchCondDTO.isFullLoadFlag()) {
            queryDTO.setApplicableObject(1);
        } else {
            queryDTO.setApplicableObject(2);
        }
        //适用范围,0-全部,1-即时调度,2-计划调度
        queryDTO.setApplicableRange(Lists.newArrayList(0, 1));
        //默认状态,0-未启用,1-启用,2-暂不支持
        queryDTO.setStatus(Lists.newArrayList(1));
        //查询出卖家用户下面的规则并按照约束，优先规则排序
        List<IntelligentDispatchRuleDTO> dispatchRuleDTOS = intelligentDispatchRuleMapper.queryIntelligentDispatchRule(queryDTO);

        List<String> ruleCodeList = Lists.newLinkedList();

        if (CollectionUtils.isNotEmpty(dispatchRuleDTOS)) {
            ruleCodeList = dispatchRuleDTOS.stream()
                    .sorted((o1, o2) -> {
                        if (o1.getRuleType() > o2.getRuleType()) {
                            return 1;
                        } else if (LogisticsUtils.isSame(o1.getRuleType(), o2.getRuleType())) {
                            Integer o1SN =  LogisticsUtils.defaultIfNull(o1.getSeqNum(),0);
                            Integer o2SN = LogisticsUtils.defaultIfNull(o2.getSeqNum(),0);
                            return o1SN.compareTo(o2SN);
                        } else {
                            return -1;
                        }
                    })
                    .map(IntelligentDispatchRuleDTO::getRuleCode)
                    .toList();
        }

        VehiclePlanParam param = new VehiclePlanParam();
        BeanUtils.copyProperties(instantAutoDispatchCondDTO, param);
        param.setDeliveryBillIdList(Lists.newArrayList(deliveryBillId));
        param.setInstanceFlag(true);
        param.setVehicleIdList(vehicleIdList);

        DispatchRuleContext<String, VehicleAutoAssignDTO, VehiclePlanParam> dispatchRuleContext = new DispatchRuleContext<>();
        dispatchRuleContext.init(ruleCodeList, vehicleIdList, param);
        dispatchRuleOperateBizService.processContext(dispatchRuleContext);
        ItemResult<List<VehicleAutoAssignDTO>> result = new ItemResult<>();
        result.setSuccess(dispatchRuleContext.isStepSuccess());
        result.setDescription(dispatchRuleContext.getStepDescription());
        if (dispatchRuleContext.getStepCount() == 0) {
            List<VehicleAutoAssignDTO> vehicleAutoAssignDTOS = autoDispatchBizService.assignForInstantLastRule(
                    deliveryBillId, dispatchRuleContext.getRemainDispatchList(), instantAutoDispatchCondDTO.getVehicleCount());
            dispatchRuleContext.setRemainResultList(vehicleAutoAssignDTOS);
        }
        result.setData(dispatchRuleContext.getRemainResultList());
        log.info("即时调度,需要执行规则:{},已执行规则总数:{}", dispatchRuleDTOS, dispatchRuleContext.getStepCount());
        log.info("执行结果为:{}", JSON.toJSONString(dispatchRuleContext));
        return result;
    }

    @Override
    public ItemResult<String> createAutoDispatchBatchId(List<String> deliveryBillIdList) {
        return new ItemResult<>(autoDispatchBizService.createAutoDispatchBatchId(deliveryBillIdList));
    }

    @Override
    public ItemResult<Void> planVehicleDispatch(PlanAutoDispatchCondDTO planAutoDispatchCondDTO) {
        log.info("开始计划调度:{}", planAutoDispatchCondDTO);
        String batchId = planAutoDispatchCondDTO.getBatchId();
        if (dispatchResultBizService.batchExist(batchId)) {
            log.info("该计划调度已经执行,计划结束");
            return new ItemResult<>(null);
        }
        List<String> deliveryBillIdList = autoDispatchBizService.queryDeliveryBillIdListByBatchId(batchId);

        List<String> vehicleIdList = autoDispatchBizService.canAssignVehicleIdList(planAutoDispatchCondDTO.getUserId(),
                planAutoDispatchCondDTO.getUserType(), planAutoDispatchCondDTO.getVehicleTypeId());

        if (CollectionUtils.isEmpty(vehicleIdList)) {
            log.info("该计划调度已经执行,没有可调度的车辆");
            throw new BizException(BasicCode.CUSTOM_ERROR, "没有可调度的车辆");
        }
        IntelligentDispatchQueryDTO queryDTO = new IntelligentDispatchQueryDTO();
        //服务方ID
        queryDTO.setUserId(planAutoDispatchCondDTO.getUserId());

        queryDTO.setUserType(planAutoDispatchCondDTO.getUserType());
        //规则适用对象,0-承运商,1-整车车辆物流,2-零单车辆物
        if(planAutoDispatchCondDTO.isFullLoadFlag()) {
            queryDTO.setApplicableObject(1);
        } else {
            queryDTO.setApplicableObject(2);
        }
        //适用范围,0-全部,1-即时调度,2-计划调度
        queryDTO.setApplicableRange(Lists.newArrayList(0, 2));
        //默认状态,0-未启用,1-启用,2-暂不支持
        queryDTO.setStatus(Lists.newArrayList(1));
        //查询出卖家用户下面的规则并按照约束，优先规则排序
        List<IntelligentDispatchRuleDTO> dispatchRuleDTOS = intelligentDispatchRuleMapper.queryIntelligentDispatchRule(queryDTO);

        List<String> ruleCodeList = Lists.newLinkedList();

        if (CollectionUtils.isNotEmpty(dispatchRuleDTOS)) {
            ruleCodeList = dispatchRuleDTOS.stream()
                    .sorted((o1, o2) -> {
                        if (o1.getRuleType() > o2.getRuleType()) {
                            return 1;
                        } else if (LogisticsUtils.isSame(o1.getRuleType(), o2.getRuleType())) {
                            Integer o1SN = LogisticsUtils.defaultIfNull(o1.getSeqNum(),0);
                            Integer o2SN = LogisticsUtils.defaultIfNull(o2.getSeqNum(),0);
                            return o1SN.compareTo(o2SN);
                        } else {
                            return -1;
                        }
                    })
                    .map(IntelligentDispatchRuleDTO::getRuleCode)
                    .toList();
        }

        VehiclePlanParam param = new VehiclePlanParam();
        BeanUtils.copyProperties(planAutoDispatchCondDTO, param);
        param.setDeliveryBillIdList(deliveryBillIdList);
        param.setInstanceFlag(false);
        param.setVehicleIdList(vehicleIdList);

        DispatchRuleContext<String, VehicleAutoAssignDTO, VehiclePlanParam> dispatchRuleContext = new DispatchRuleContext<>();
        dispatchRuleContext.init(ruleCodeList, vehicleIdList, param);
        dispatchRuleOperateBizService.processContext(dispatchRuleContext);
        if (dispatchRuleContext.getStepCount() == 0) {
            List<VehicleAutoAssignDTO> vehicleAutoAssignDTOS = autoDispatchBizService.assignForDefaultLastRule(
                    deliveryBillIdList, dispatchRuleContext.getRemainDispatchList(), planAutoDispatchCondDTO.getVehicleCount());
            dispatchRuleContext.setRemainResultList(vehicleAutoAssignDTOS);
        }
        if (dispatchRuleContext.isStepSuccess()) {
            //保存到数据库
            List<VehicleAutoAssignDTO> remainResultList = dispatchRuleContext.getRemainResultList();
            dispatchResultBizService.saveResult(batchId, remainResultList, planAutoDispatchCondDTO.getOperateUserId());
        }
        ItemResult<Void> result = new ItemResult<>();
        result.setSuccess(dispatchRuleContext.isStepSuccess());
        result.setDescription(dispatchRuleContext.getStepDescription());
        log.info("计划调度,需要执行规则:{},已执行规则总数:{}", dispatchRuleDTOS, dispatchRuleContext.getStepCount());
        log.info("执行结果为:{}", JSON.toJSONString(dispatchRuleContext));
        return result;
    }

    @Override
    public ItemResult<WaitPlanAutoDispatchDTO> queryWaitAutoDispatchByBatchId(String batchId) {
        return new ItemResult<>(autoDispatchBizService.queryWaitAutoDispatchByBatchId(batchId));
    }

    @Override
    public ItemResult<PageData<VehicleAutoAssignDTO>> queryAutoWaitAssignList(PageQuery<VehicleAutoAssignQueryDTO> vehicleAutoAssignQueryDTO) {
        return new ItemResult<>(dispatchResultBizService.queryAutoWaitAssignList(vehicleAutoAssignQueryDTO));
    }

    @Override
    public ItemResult<List<OptionDTO>> warehouseOptionsByBatchId(String batchId) {
        return new ItemResult<>(dispatchResultBizService.warehouseOptionsByBatchId(batchId));
    }

    @Override
    public ItemResult<List<OptionDTO>> receiverAddressOptionsByBatchId(String batchId) {
        return new ItemResult<>(dispatchResultBizService.receiverAddressOptionsByBatchId(batchId));
    }

    @Override
    public ItemResult<Void> confirmAutoDispatch(DispatchResultOperationDTO dispatchResultOperationDTO) {
        dispatchResultBizService.confirmAutoDispatch(dispatchResultOperationDTO);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> rejectAutoDispatch(DispatchResultOperationDTO dispatchResultOperationDTO) {
        dispatchResultBizService.rejectAutoDispatch(dispatchResultOperationDTO);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<ContractSubmitInfoDTO> queryContractInfo(String deliveryBillNum) {
        return new ItemResult<>(deliveryBillQueryBizService.queryContractInfo(deliveryBillNum));
    }

    @Override
    public ItemResult<Void> changeDeliveryWarehouse(WarehouseChangeDTO warehouseChangeDTO) {
        deliveryBillOperateBizService.changeDeliveryWarehouse(warehouseChangeDTO);
        return new ItemResult<>(null);
    }

	@Override
	public List<LogisticsAdjustPriceMemberDTO> queryLogisticsAdjustPriceMemberList(
			LogisticsAdjustPriceDTO queryDTO) {
		return deliveryBillQueryBizService.queryLogisticsAdjustPriceMemberList(queryDTO);
	}

    @Override
    public ItemResult<Void> changeDeliveryInfoCarrier(InternalAllocation allocation) {
        deliveryBillOperateBizService.changeDeliveryInfoCarrier(allocation);
        return new ItemResult<>(null);
    }

    @Override
    public List<LogisticsAdjustPriceItemDTO> queryLogisticsAdjustPriceItemList(LogisticsAdjustPriceMemberDTO queryDTO) {
        return deliveryBillQueryBizService.queryLogisticsAdjustPriceItemList(queryDTO);
    }
}
