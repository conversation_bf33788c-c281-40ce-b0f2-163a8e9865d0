package com.ecommerce.logistics.service.impl;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.delivery.DeliveryNoteAddDTO;
import com.ecommerce.logistics.api.dto.delivery.DeliveryNoteDetailDTO;
import com.ecommerce.logistics.api.dto.delivery.DeliveryNoteDetailQueryDTO;
import com.ecommerce.logistics.api.dto.delivery.DeliveryNoteEditDTO;
import com.ecommerce.logistics.api.dto.delivery.DeliveryNoteLeaveDTO;
import com.ecommerce.logistics.api.dto.delivery.DeliveryNoteListDTO;
import com.ecommerce.logistics.api.dto.delivery.DeliveryNoteQueryDTO;
import com.ecommerce.logistics.api.dto.delivery.DeliveryNoteSignDTO;
import com.ecommerce.logistics.api.dto.delivery.DeliveryNoteUnloadDTO;
import com.ecommerce.logistics.biz.delivery.IDeliveryNoteBizService;
import com.ecommerce.logistics.biz.delivery.IDeliveryNoteOperateBizService;
import com.ecommerce.logistics.service.IDeliveryNoteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Auther: <EMAIL>
 * @Date: 2019-06-17 17:42
 * @Description: DeliveryNoteService
 */
@Slf4j
@Service("deliveryNoteService")
public class DeliveryNoteService implements IDeliveryNoteService {

    @Autowired
    private IDeliveryNoteBizService deliveryNoteBizService;

    @Autowired
    private IDeliveryNoteOperateBizService deliveryNoteOperateBizService;

    @Override
    public ItemResult<String> createDeliveryNote(DeliveryNoteAddDTO deliveryNoteAddDTO) {
        String deliveryNoteId = null;
        try {
            deliveryNoteId = deliveryNoteBizService.createDeliveryNote(deliveryNoteAddDTO);
        } catch (Exception e) {
            log.error("创建送货单发生异常:{}", deliveryNoteAddDTO, e);
            ItemResult<String> itemResult = new ItemResult<>();
            itemResult.setSuccess(false);
            itemResult.setDescription(e.getMessage());
            return itemResult;
        }
        return new ItemResult<>(deliveryNoteId);
    }

    @Override
    public ItemResult<Void> editDeliveryNote(DeliveryNoteEditDTO deliveryNoteEditDTO) {
        try {
            deliveryNoteBizService.editDeliveryNote(deliveryNoteEditDTO);
        } catch (Exception e) {
            log.error("编辑送货单发生异常:{}", deliveryNoteEditDTO, e);
            ItemResult<Void> itemResult = new ItemResult<>();
            itemResult.setSuccess(false);
            itemResult.setDescription(e.getMessage());
            return itemResult;
        }
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<DeliveryNoteDetailDTO> queryDetail(DeliveryNoteDetailQueryDTO deliveryNoteDetailQueryDTO) {
        DeliveryNoteDetailDTO deliveryNoteDetailDTO = null;
        try {
            deliveryNoteDetailDTO = deliveryNoteBizService.queryDetail(deliveryNoteDetailQueryDTO);
        } catch (Exception e) {
            log.error("查询送货单详情发生异常:{}", deliveryNoteDetailQueryDTO, e);
            ItemResult<DeliveryNoteDetailDTO> itemResult = new ItemResult<>();
            itemResult.setSuccess(false);
            itemResult.setDescription(e.getMessage());
            return itemResult;
        }
        return new ItemResult<>(deliveryNoteDetailDTO);
    }

    @Override
    public ItemResult<PageData<DeliveryNoteListDTO>> queryList(PageQuery<DeliveryNoteQueryDTO> pageQuery) {
        PageData<DeliveryNoteListDTO> pageData = null;
        try {
            pageData = deliveryNoteBizService.queryList(pageQuery);
        } catch (Exception e) {
            log.error("查询送货单列表发生异常:{}", pageQuery, e);
            ItemResult<PageData<DeliveryNoteListDTO>> itemResult = new ItemResult<>();
            itemResult.setSuccess(false);
            itemResult.setDescription(e.getMessage());
            return itemResult;
        }
        return new ItemResult<>(pageData);
    }

    @Override
    public ItemResult<Void> leaveWarehouse(DeliveryNoteLeaveDTO deliveryNoteLeaveDTO) {
        try {
            deliveryNoteOperateBizService.deliveryNoteLeave(deliveryNoteLeaveDTO);
        } catch (Exception e) {
            log.error("送货单出厂发生异常:{}", deliveryNoteLeaveDTO, e);
            ItemResult<Void> result = new ItemResult<>();
            result.setSuccess(false);
            result.setDescription(e.getMessage());
            return result;
        }
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> signDeliveryNote(DeliveryNoteSignDTO deliveryNoteSignDTO) {
        try {
            deliveryNoteOperateBizService.deliveryNoteSign(deliveryNoteSignDTO);
        } catch (Exception e) {
            log.error("送货单签收发生异常:{}", deliveryNoteSignDTO, e);
            ItemResult<Void> result = new ItemResult<>();
            result.setSuccess(false);
            result.setDescription(e.getMessage());
            return result;
        }
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> unloadDeliveryNote(DeliveryNoteUnloadDTO deliveryNoteUnloadDTO) {
        return null;
    }
}
