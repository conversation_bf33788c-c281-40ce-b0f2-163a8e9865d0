package com.ecommerce.logistics.service.impl;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.creditStatistic.CreditStatisticAddDTO;
import com.ecommerce.logistics.api.dto.dispatchbill.CarrierListDTO;
import com.ecommerce.logistics.api.dto.dispatchbill.DispatchAssignDetailDTO;
import com.ecommerce.logistics.api.dto.dispatchbill.DispatchBillAssignAddDTO;
import com.ecommerce.logistics.api.dto.dispatchbill.DispatchBillDTO;
import com.ecommerce.logistics.api.dto.dispatchbill.DispatchBillListDTO;
import com.ecommerce.logistics.api.dto.dispatchbill.DispatchBillListQueryDTO;
import com.ecommerce.logistics.api.dto.dispatchbill.ExportDispatchBillDTO;
import com.ecommerce.logistics.api.dto.dispatchbill.TransportCapacityDTO;
import com.ecommerce.logistics.api.dto.dispatchbill.TransportCapacityQueryDTO;
import com.ecommerce.logistics.api.dto.pickingbill.PickingBillAssignDispatchDTO;
import com.ecommerce.logistics.api.dto.waybill.CarrierDispatchBillListDTO;
import com.ecommerce.logistics.api.enums.UserRoleEnum;
import com.ecommerce.logistics.biz.ICreditStatisticBizService;
import com.ecommerce.logistics.biz.IDispatchBillBizService;
import com.ecommerce.logistics.biz.message.ISMSMessageBizService;
import com.ecommerce.logistics.dao.vo.CreditStatistic;
import com.ecommerce.logistics.service.IDispatchBillService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class DispatchBillService implements IDispatchBillService {
	
	@Autowired
	private IDispatchBillBizService dispatchBillBizService;

	@Autowired
	private ISMSMessageBizService smsMessageBizService;

	@Autowired
	private ICreditStatisticBizService creditStatisticBizService;

	@Override
	public ItemResult<PageData<DispatchBillListDTO>> queryDispatchBillList(PageQuery<DispatchBillListQueryDTO> pageQuery) {
		return new ItemResult<>(dispatchBillBizService.queryDispatchBillList(pageQuery));
	}

	@Override
	public ItemResult<Void> assignDispatchBill(DispatchBillAssignAddDTO dispatchBillAssignAddDTO) {
		dispatchBillBizService.assignDispatchBill(dispatchBillAssignAddDTO);
		// 发送消息
		smsMessageBizService.sendWaybillAssign(dispatchBillAssignAddDTO.getDispatchBillId(), dispatchBillAssignAddDTO.getDriverId());

		//如果抢单成功，则添加对应司机的接单次数
        if (CsStringUtils.isNotBlank(dispatchBillAssignAddDTO.getDriverId())) {
			String personType = UserRoleEnum.PERSONAL_DRIVER.getCode().toString();
			CreditStatistic creditStatistic = creditStatisticBizService.findCreditStatisticById(dispatchBillAssignAddDTO.getDriverId(),personType);
			//如果不存在记录，则新增
            if (creditStatistic == null || CsStringUtils.isBlank(creditStatistic.getCreditStatisticId())) {
				CreditStatisticAddDTO creditStatisticAddDTO = new CreditStatisticAddDTO();
				creditStatisticAddDTO.setPersonId(dispatchBillAssignAddDTO.getDriverId());
				creditStatisticAddDTO.setPersonName(dispatchBillAssignAddDTO.getDriverName());
				creditStatisticAddDTO.setPersonType(personType);
				creditStatisticAddDTO.setBillCount(1);
				creditStatisticAddDTO.setBreakCount(0);
				creditStatisticAddDTO.setComplaintCount(0);
				creditStatisticAddDTO.setOperatorUserId(dispatchBillAssignAddDTO.getOperatorId());
				creditStatisticBizService.addCreditStatistic(creditStatisticAddDTO);
			}else {
				creditStatisticBizService.updateCreditStatisticCount(creditStatistic.getPersonId(),true,false,false,dispatchBillAssignAddDTO.getOperatorId());
			}
		}
		return new ItemResult<>(null);
	}

	@Override
	public ItemResult<Void> batchAssignDispatchBill(List<DispatchBillAssignAddDTO> dispatchBillAssignAddDTOS) {
		if(CollectionUtils.isNotEmpty(dispatchBillAssignAddDTOS)){
			dispatchBillAssignAddDTOS.forEach(dispatchBillAssignAddDTO -> {
				dispatchBillBizService.assignDispatchBill(dispatchBillAssignAddDTO);
				// 发送消息
				smsMessageBizService.sendWaybillAssign(dispatchBillAssignAddDTO.getDispatchBillId(), dispatchBillAssignAddDTO.getDriverId());
			});
		}
		return new ItemResult<>(null);
	}

	@Override
	public ItemResult<DispatchBillDTO> getDispatchBillDetail(String dispatchBillId) {
		return new ItemResult<>(dispatchBillBizService.queryDispatchBillDetail(dispatchBillId));
	}

	@Override
	public ItemResult<List<PickingBillAssignDispatchDTO>> queryListByPickingBillId(String pickingBillId) {
		return new ItemResult<>(dispatchBillBizService.selectDispatchBillByPickingBillId(pickingBillId));
	}

	@Override
	public ItemResult<Void> cancelDispatchBill(String dispatchBillId,String operatorId,String operatorName) {
		dispatchBillBizService.cancelDispatchBill(dispatchBillId, operatorId, operatorName);
		return new ItemResult<>(null);
	}

	@Override
	public ItemResult<PageData<CarrierDispatchBillListDTO>> queryCarrierDispatchBillList(
			PageQuery<DispatchBillListQueryDTO> pageQuery) {
		return new ItemResult<>(dispatchBillBizService.queryCarrierDispatchBillList(pageQuery));
	}

	@Override
	public ItemResult<DispatchAssignDetailDTO> getDispatchAssignDetail(String dispatchBillId) {
		return new ItemResult<>(dispatchBillBizService.queryDispatchAssignDetail(dispatchBillId));
	}

	@Override
	public ItemResult<List<TransportCapacityDTO>> queryCarrierTransportCapacity(TransportCapacityQueryDTO transportCapacityQueryDTO) {
		return new ItemResult<>(dispatchBillBizService.getCarrierCapacity(transportCapacityQueryDTO));
	}

	@Override
	public ItemResult<List<CarrierListDTO>> queryCarrierListLikeName(String name) {
		//  待从会员端获取
		List<CarrierListDTO> list = new ArrayList<>();
		return new ItemResult<>(list);
	}

	@Override
	public ItemResult<List<ExportDispatchBillDTO>> downloadDispatchBillList(DispatchBillListQueryDTO dispatchBillListQueryDTO) {
		return new ItemResult<>(dispatchBillBizService.downloadDispatchBillList(dispatchBillListQueryDTO));
	}
}
