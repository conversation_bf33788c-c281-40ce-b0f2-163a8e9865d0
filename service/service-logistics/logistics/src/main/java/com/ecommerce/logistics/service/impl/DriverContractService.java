package com.ecommerce.logistics.service.impl;

import com.ecommerce.base.api.dto.warehouse.PageQuery;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.contract.*;
import com.ecommerce.logistics.biz.IDriverContractBizService;
import com.ecommerce.logistics.service.IDriverContractService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 司机合同信息服务
 */
@Service
@Slf4j
public class DriverContractService implements IDriverContractService {

    @Autowired
    private IDriverContractBizService driverContractBizService;

    /**
     * 新增司机合同
     * @param driverContractAddDTO
     * @return 合同主键
     */
    @Override
    public ItemResult<String> addDriverContract(DriverContractAddDTO driverContractAddDTO) throws BizException {
        return new ItemResult<>(driverContractBizService.addDriverContract(driverContractAddDTO));
    }

    /**
     * 查询司机合同列表
     * @param pageQuery 查询对象
     * @return
     */
    @Override
    public ItemResult<PageData<DriverContractListDTO>> queryDriverContractList(PageQuery<DriverContractListQueryDTO> pageQuery) throws BizException {
        return new ItemResult<>(driverContractBizService.queryDriverContractList(pageQuery));
    }

    /**
     * 上报司机合同
     * @return
     * @throws Exception
     */
    @Override
    public ItemResult<Void> reportDriverContract(String contractIds, String operatorUserId, String operatorUserName) throws BizException {
        driverContractBizService.reportDriverContract(contractIds, operatorUserId, operatorUserName);
        return new ItemResult<>(null);
    }

}
