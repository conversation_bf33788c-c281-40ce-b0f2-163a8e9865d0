package com.ecommerce.logistics.service.impl;

import com.alibaba.fastjson.JSON;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.service.common.uuid.UUIDGenerator;
import com.ecommerce.common.utils.BeanConvertUtils;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.dry.season.policy.*;
import com.ecommerce.logistics.biz.dry.season.price.policy.IDrySeasonPricePolicyBizService;
import com.ecommerce.logistics.biz.dry.season.price.policy.IDrySeasonPricePolicyShippingRouteRelationBizService;
import com.ecommerce.logistics.biz.dry.season.price.policy.IDrySeasonPricePolicyTimeBizService;
import com.ecommerce.logistics.biz.dry.season.price.policy.IDrySeasonPricePolicyWaterLevelBizService;
import com.ecommerce.logistics.dao.vo.DrySeasonPricePolicy;
import com.ecommerce.logistics.dao.vo.DrySeasonPricePolicyShippingRouteRelation;
import com.ecommerce.logistics.dao.vo.DrySeasonPricePolicyWaterLevel;
import com.ecommerce.logistics.service.IDrySeasonPolicyService;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DrySeasonPolicyService implements IDrySeasonPolicyService {

    @Autowired
    private IDrySeasonPricePolicyBizService drySeasonPricePolicyBizService;
    @Autowired
    private IDrySeasonPricePolicyTimeBizService drySeasonPricePolicyTimeBizService;
    @Autowired
    private IDrySeasonPricePolicyShippingRouteRelationBizService drySeasonPricePolicyShippingRouteRelationBizService;
    @Autowired
    private IDrySeasonPricePolicyWaterLevelBizService drySeasonPricePolicyWaterLevelBizService;
    @Autowired
    private UUIDGenerator uuidGenerator;

    @Transactional
    @Override
    public ItemResult<Boolean> addPolicy(DrySeasonPolicyCreateDTO dto) {
        log.info("addPolicy: {}", JSON.toJSONString(dto));
        //添加策略
        DrySeasonPricePolicy policy = drySeasonPricePolicyBizService.add(dto);
        //添加时间段
        drySeasonPricePolicyTimeBizService.add(policy,dto);
        //添加水位信息
        drySeasonPricePolicyWaterLevelBizService.add(policy,dto);
        //添加航线关系
        drySeasonPricePolicyShippingRouteRelationBizService.add(policy,dto);
        return new ItemResult<>(true);
    }

    @Transactional
    @Override
    public ItemResult<Boolean> updatePolicy(DrySeasonPolicyUpdateDTO dto) {
        log.info("updatePolicy: {}", JSON.toJSONString(dto));
        if (CsStringUtils.isBlank(dto.getPolicyId())) {
            throw new BizException(BasicCode.CUSTOM_ERROR,"Id不可为空");
        }
        DrySeasonPricePolicy policy = drySeasonPricePolicyBizService.findById(dto.getPolicyId());
        if(policy == null || BooleanUtils.isTrue(policy.getDelFlg()) ){
            throw new BizException(BasicCode.CUSTOM_ERROR,"更新对象不存在或已被删除");
        }
        DrySeasonPricePolicy up = new DrySeasonPricePolicy();
        up.setPolicyId(dto.getPolicyId());
        up.setUpdateTime(new Date());
        up.setUpdateUser(dto.getOperator());
        up.setPolicyName(dto.getPolicyName());
        up.setSupportShippingType(dto.getSupportShippingType());
        up.setRemark(dto.getRemark());
        policy.setUpdateTime(up.getUpdateTime());
        policy.setUpdateUser(dto.getOperator());
        policy.setPolicyName(dto.getPolicyName());
        policy.setSupportShippingType(dto.getSupportShippingType());
        policy.setRemark(dto.getRemark());
        //更新策略
        drySeasonPricePolicyBizService.updateSelective(up);
        //逻辑删除时间段
        drySeasonPricePolicyTimeBizService.deleteByPolicyId(dto.getPolicyId(),dto.getOperator());
        //逻辑删除水位信息
        drySeasonPricePolicyWaterLevelBizService.deleteByPolicyId(dto.getPolicyId(),dto.getOperator());
        //逻辑删除航线关系
        drySeasonPricePolicyShippingRouteRelationBizService.deleteByPolicyId(dto.getPolicyId(),dto.getOperator());
        DrySeasonPolicyCreateDTO createDTO = new DrySeasonPolicyCreateDTO();
        //添加时间段
        createDTO.setTimeList(dto.getTimeList());
        drySeasonPricePolicyTimeBizService.add(policy,createDTO);
        //添加水位信息
        createDTO.setSelectedColumn(dto.getSelectedColumn());
        drySeasonPricePolicyWaterLevelBizService.add(policy,createDTO);
        //添加航线关系
        createDTO.setShippingRouteList(dto.getShippingRouteList());
        drySeasonPricePolicyShippingRouteRelationBizService.add(policy,createDTO);
        return new ItemResult<>(true);
    }

    @Transactional
    @Override
    public ItemResult<Boolean> addPolicyShippingRouteRelation(PolicyShippingRouteRelationAddDTO dto) {
        drySeasonPricePolicyShippingRouteRelationBizService.addRelation(dto);
        return new ItemResult<>(true);
    }

    @Override
    public ItemResult<Boolean> removePolicyShippingRouteRelation(PolicyShippingRouteRelationRemoveDTO dto) {
        drySeasonPricePolicyShippingRouteRelationBizService.removeRelation(dto);
        return null;
    }

    @Transactional
    @Override
    public ItemResult<Boolean> deletePolicy(DrySeasonPolicyDeleteDTO dto) {
        if (CsStringUtils.isBlank(dto.getPolicyId()) || CsStringUtils.isBlank(dto.getOperatorMemberId())) {
            return new ItemResult<>(false);
        }
        //逻辑删除策略
        drySeasonPricePolicyBizService.delete(dto);
        //逻辑删除时间段
        drySeasonPricePolicyTimeBizService.deleteByPolicyId(dto.getPolicyId(),dto.getOperator());
        //逻辑删除水位信息
        drySeasonPricePolicyWaterLevelBizService.deleteByPolicyId(dto.getPolicyId(),dto.getOperator());
        //逻辑删除航线关系
        drySeasonPricePolicyShippingRouteRelationBizService.deleteByPolicyId(dto.getPolicyId(),dto.getOperator());
        return new ItemResult<>(true);
    }

    @Override
    public ItemResult<PageInfo<DrySeasonPolicyListDTO>> pageInfoPolicy(PageQuery<DrySeasonPolicyQueryDTO> dto) {
        return new ItemResult<>(drySeasonPricePolicyBizService.pageInfoPolicy(dto));
    }

    @Override
    public ItemResult<DrySeasonPolicyDTO> findById(String id) {
        DrySeasonPricePolicy policy = drySeasonPricePolicyBizService.findById(id);
        if( policy != null && BooleanUtils.isNotTrue(policy.getDelFlg())) {
            DrySeasonPolicyDTO result = BeanConvertUtils.convert(policy,DrySeasonPolicyDTO.class);
            //查询水位信息
            result.setWaterLevelList(drySeasonPricePolicyWaterLevelBizService.findByPolicyId(id));
            //查询时间
            result.setTimeList(drySeasonPricePolicyTimeBizService.findByPolicyId(id));
            //查询航线关系
            result.setShippingRouteList(drySeasonPricePolicyShippingRouteRelationBizService.findByPolicyId(id));
            return new ItemResult<>(result);
        }
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<List<DrySeasonPolicyDTO>> findByShippingRouteId(String shippingRouteId) {
        List<DrySeasonPolicyShippingRouteRelationDTO> shippingRouteRelationDTOList = drySeasonPricePolicyShippingRouteRelationBizService.findByShippingRouteId(shippingRouteId);
        if(CollectionUtils.isEmpty(shippingRouteRelationDTOList)){
            return new ItemResult<>(null);
        }
        List<DrySeasonPolicyDTO> result = Lists.newArrayList();
        List<String> policyIdList = shippingRouteRelationDTOList.stream().map(DrySeasonPolicyShippingRouteRelationDTO::getPolicyId).distinct().toList();
        for (String policyId : policyIdList) {
            DrySeasonPricePolicy policy = drySeasonPricePolicyBizService.findById(policyId);
            if( policy != null && BooleanUtils.isNotTrue(policy.getDelFlg())) {
                DrySeasonPolicyDTO dto = BeanConvertUtils.convert(policy,DrySeasonPolicyDTO.class);
                //查询水位信息
                dto.setWaterLevelList(drySeasonPricePolicyWaterLevelBizService.findByPolicyId(policyId));
                //查询时间
                dto.setTimeList(drySeasonPricePolicyTimeBizService.findByPolicyId(policyId));
                //查询航线关系
                dto.setShippingRouteList(shippingRouteRelationDTOList);
                result.add(dto);
            }
        }

        return new ItemResult<>(result);
    }

    //查询模板
    @Override
    public ItemResult<DrySeasonPolicyWaterLevelTemplateDTO> queryWaterLevelTemplate(DrySeasonPolicyWaterLevelTemplateQueryDTO dto) {
        DrySeasonPolicyWaterLevelTemplateDTO result = new DrySeasonPolicyWaterLevelTemplateDTO();
        //查询会员模板 一个会员仅有一套模板 一行一个吨位  按水位升序排列
        List<DrySeasonPricePolicyWaterLevel> waterLevelList = drySeasonPricePolicyWaterLevelBizService.findTemplateByBelongerId(dto.getBelongerId());
        result.setWaterLevelList(BeanConvertUtils.convertList(waterLevelList,DrySeasonPolicyWaterLevelDTO.class));
        return new ItemResult<>(result);
    }

    @Transactional
    @Override
    public ItemResult<Boolean> editWaterLevelTemplate(DrySeasonPolicyWaterLevelTemplateEditDTO dto) {
        //先删除(更新标记)，再插入  更新drySeasonPricePolicyWaterLevel表删除标记，
        drySeasonPricePolicyWaterLevelBizService.deleteTemplateByBelongerId(dto.getMemberId(),dto.getOperator());
        Date now = new Date();
        List<DrySeasonPricePolicyWaterLevel> list = Lists.newArrayList();
        for (DrySeasonPolicyWaterLevelDTO dsppwlDTO : dto.getWaterLevelList()) {
            DrySeasonPricePolicyWaterLevel dsppwl = new DrySeasonPricePolicyWaterLevel();
            BeanUtils.copyProperties(dsppwlDTO,dsppwl);
            dsppwl.setBelongerId(dto.getMemberId());
            dsppwl.setWaterLevelId(uuidGenerator.gain());
            dsppwl.setCreateUser(dto.getOperator());
            dsppwl.setTempleteFlg(true);
            dsppwl.setDelFlg(false);
            dsppwl.setCreateTime(now);
            dsppwl.setUpdateUser(dto.getOperator());
            dsppwl.setUpdateTime(now);
            list.add(dsppwl);
        }
        int insertCount1 = drySeasonPricePolicyWaterLevelBizService.insertTemplate(list);
        log.info("editWaterLevelTemplate insertCount: {}",insertCount1);
        return new ItemResult<>(true);
    }

    @Override
    public Map<String, List<DrySeasonPolicyWaterLevelSimpleDTO>> findByShippingRouteIds(Collection<String> shippingRouteIds) {
        Map<String, List<DrySeasonPolicyWaterLevelSimpleDTO>> result = Maps.newHashMap();
        if(CollectionUtils.isEmpty(shippingRouteIds)){
            return result;
        }
        //查询有效的枯水期策略
        List<String> effectivePolicyIds = drySeasonPricePolicyTimeBizService.findEffectivePolicyId();
        if(CollectionUtils.isEmpty(effectivePolicyIds)){
            return result;
        }
        //根据 有效的枯水期策略ID 和航线Id 查询关系表数据
        List<DrySeasonPricePolicyShippingRouteRelation> relations = drySeasonPricePolicyShippingRouteRelationBizService.findByShippingRouteIds(shippingRouteIds, effectivePolicyIds);
        if(CollectionUtils.isEmpty(relations)){
            return result;
        }
        //更新 有效的枯水期策略ID
        Map<String, Set<String>> group = relations.stream().collect(Collectors.groupingBy(DrySeasonPricePolicyShippingRouteRelation::getShippingRouteId, Collectors.mapping(DrySeasonPricePolicyShippingRouteRelation::getPolicyId, Collectors.toSet())));
        Set<String> effectivePolicyIdSet = Sets.newHashSet();
        if (group != null) {
            for (Set<String> set : group.values()) {
                effectivePolicyIdSet.addAll(set);
            }
        }
        if( group == null || group.size() == 0 || effectivePolicyIdSet.isEmpty()){
            return result;
        }
        //查询策略水位字段
        List<DrySeasonPolicyWaterLevelSimpleDTO> waterLevelDTOList = drySeasonPricePolicyWaterLevelBizService.findByPolicyIds(effectivePolicyIdSet);
        if(CollectionUtils.isEmpty(waterLevelDTOList)){
            return result;
        }
        //按policyId分组
        Map<String, List<DrySeasonPolicyWaterLevelSimpleDTO>> listMap = waterLevelDTOList.stream().collect(Collectors.groupingBy(DrySeasonPolicyWaterLevelSimpleDTO::getPolicyId));

        //shippingRouteId --> policyId --> List<DrySeasonPolicyWaterLevelSimpleDTO>
        if(group != null) {
            for (Map.Entry<String, Set<String>> entry : group.entrySet()) {//key: shippingRouteId,value: Set<policyId>
                String shippingRouteId = entry.getKey();
                List<DrySeasonPolicyWaterLevelSimpleDTO> value = result.getOrDefault(shippingRouteId, Lists.newArrayList());
                for (String policyId : entry.getValue()) {
                    List<DrySeasonPolicyWaterLevelSimpleDTO> list = listMap.get(policyId);
                    if (CollectionUtils.isNotEmpty(list)) {
                        value.addAll(list);
                    }
                }
                result.put(shippingRouteId, value);
            }
        }
        log.info("findByShippingRouteIds shippingRouteIds: {},result: {}",shippingRouteIds,JSON.toJSONString(result));
        return result;
    }
}
