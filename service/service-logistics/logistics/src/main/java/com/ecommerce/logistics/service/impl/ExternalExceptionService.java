package com.ecommerce.logistics.service.impl;

import com.alibaba.fastjson.JSON;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.externalexception.ExternalExceptionListDTO;
import com.ecommerce.logistics.api.dto.externalexception.ExternalExceptionQueryDTO;
import com.ecommerce.logistics.api.dto.externalexception.ExternalExceptionRetryDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillRetryHandlerDTO;
import com.ecommerce.logistics.api.enums.ExternalExceptionStatusEnum;
import com.ecommerce.logistics.api.enums.TransportToolTypeEnum;
import com.ecommerce.logistics.biz.IExternalExceptionBizService;
import com.ecommerce.logistics.biz.IShipBillQueryBizService;
import com.ecommerce.logistics.dao.vo.ExternalExceptionHandle;
import com.ecommerce.logistics.dao.vo.ShipBill;
import com.ecommerce.logistics.service.IExternalExceptionService;
import com.ecommerce.logistics.service.IShipBillExternalService;
import com.ecommerce.logistics.service.IWaybillNewExternalService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Author: <EMAIL>
 * Description:
 * Date: Create in 下午3:39 19/4/26
 */
@Service
@Slf4j
public class ExternalExceptionService implements IExternalExceptionService{

    @Autowired
    private IExternalExceptionBizService externalExceptionBizService;

    @Autowired
    private IWaybillNewExternalService waybillExternalService;

    @Autowired
    private IShipBillExternalService shipBillExternalService;

    @Autowired
    private IShipBillQueryBizService shipBillQueryBizService;

    @Override
    public ItemResult<PageData<ExternalExceptionListDTO>> queryExternalExceptionList(PageQuery<ExternalExceptionQueryDTO> pageQuery) {
        return new ItemResult<>(externalExceptionBizService.queryExternalExceptionList(pageQuery));
    }

    @Override
    public ItemResult<Void> externalExceptionRetryHandler(ExternalExceptionRetryDTO externalExceptionRetryDTO) {
        log.info("externalExceptionRetryHandler:{}", JSON.toJSONString(externalExceptionRetryDTO));
        ExternalExceptionHandle externalExceptionHandle = externalExceptionBizService
                .queryExternalExceptionHandleById(externalExceptionRetryDTO.getExternalExceptionHandleId());
        if (externalExceptionHandle == null) {
            throw new BizException(BasicCode.DATA_NOT_EXIST, "异常处理对象");
        }
        //判断处理状态，防止重复执行
        if (CsStringUtils.equals(externalExceptionHandle.getStatus(), ExternalExceptionStatusEnum.HANDLE_SUCCESS.getCode())) {
            return new ItemResult<>(null);
        }
        //判断运单类型
        ShipBill shipBill = shipBillQueryBizService.selectShipBillById(externalExceptionHandle.getWaybillId());
        if (shipBill == null) {
            throw new BizException(BasicCode.DATA_NOT_EXIST, "运单");
        }

        WaybillRetryHandlerDTO waybillRetryHandlerDTO = new WaybillRetryHandlerDTO();
        waybillRetryHandlerDTO.setWaybillId(externalExceptionHandle.getWaybillId());
        waybillRetryHandlerDTO.setOperatorUserId(externalExceptionRetryDTO.getOperatorUserId());
        waybillRetryHandlerDTO.setOperatorUserName(externalExceptionRetryDTO.getOperatorUserName());
        try {
            if (CsStringUtils.equals(TransportToolTypeEnum.ROAD_TRANSPORT.getCode(), shipBill.getTransportToolType())) {
                waybillExternalService.waybillRetryHandler(waybillRetryHandlerDTO);
            }else {
                shipBillExternalService.waybillRetryHandler(waybillRetryHandlerDTO);
            }

        } catch (BizException e) {
            log.info("业务处理失败:" + e.getMessage());
        }

        return new ItemResult<>(null);
    }
}
