package com.ecommerce.logistics.service.impl;

import com.alibaba.fastjson.JSON;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.exception.CodeMeta;
import com.ecommerce.common.exception.DistributeLockException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.service.ILockService;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.common.utils.DateUtil;
import com.ecommerce.logistics.LogisticsCommonBean;
import com.ecommerce.logistics.api.dto.externalexception.ExternalExceptionSaveDTO;
import com.ecommerce.logistics.api.dto.operationrecord.OperationRecordAddDTO;
import com.ecommerce.logistics.api.dto.pickingbill.AssignWaybillDTO;
import com.ecommerce.logistics.api.dto.pickingbill.AssignWaybillDetailDTO;
import com.ecommerce.logistics.api.dto.pickingbill.PickingBillDTO;
import com.ecommerce.logistics.api.dto.pickingbill.PickingBillQueryDTO;
import com.ecommerce.logistics.api.dto.productinfo.ProductDetailDTO;
import com.ecommerce.logistics.api.dto.vehicle.ERPVehicleDTO;
import com.ecommerce.logistics.api.dto.waybill.DiscardWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.ERPWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.ERPWaybillExecuteDTO;
import com.ecommerce.logistics.api.dto.waybill.ERPWaybillExecuteStatusDTO;
import com.ecommerce.logistics.api.dto.waybill.ERPWaybillItemDTO;
import com.ecommerce.logistics.api.dto.waybill.ExternalWaybillRequestDTO;
import com.ecommerce.logistics.api.dto.waybill.LeaveWarehouseDTO;
import com.ecommerce.logistics.api.dto.waybill.SellerAssignWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillDetailDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillRetryHandlerDTO;
import com.ecommerce.logistics.api.enums.BillProxyTypeEnum;
import com.ecommerce.logistics.api.enums.ExternalExceptionStatusEnum;
import com.ecommerce.logistics.api.enums.ExternalMethodTypeEnum;
import com.ecommerce.logistics.api.enums.ExternalSyncFlagEnum;
import com.ecommerce.logistics.api.enums.OperationRecordTypeEnum;
import com.ecommerce.logistics.api.enums.PickingBillTypeEnum;
import com.ecommerce.logistics.api.enums.WaybillExternalExecuteStatusEnum;
import com.ecommerce.logistics.api.enums.WaybillExternalStatusEnum;
import com.ecommerce.logistics.api.enums.WaybillOperationTypeEnum;
import com.ecommerce.logistics.api.enums.WaybillStatusEnum;
import com.ecommerce.logistics.biz.IExternalExceptionBizService;
import com.ecommerce.logistics.biz.IOperationRecordBizService;
import com.ecommerce.logistics.biz.IPickingBillBizService;
import com.ecommerce.logistics.biz.IVehicleBizService;
import com.ecommerce.logistics.biz.IWaybillMapBizService;
import com.ecommerce.logistics.biz.IWaybillOperationBizService;
import com.ecommerce.logistics.biz.IWaybillQueryBizService;
import com.ecommerce.logistics.biz.IWaybillSignBizService;
import com.ecommerce.logistics.biz.delayed.RetryErpTaskManager;
import com.ecommerce.logistics.biz.delayed.TaskMessage;
import com.ecommerce.logistics.biz.message.ISMSMessageBizService;
import com.ecommerce.logistics.dao.dto.pickingbill.WaybillDO;
import com.ecommerce.logistics.dao.dto.waybill.SubWaybillDO;
import com.ecommerce.logistics.dao.dto.waybill.UpdateExternalWaybillDO;
import com.ecommerce.logistics.dao.mapper.ProductInfoMapper;
import com.ecommerce.logistics.dao.mapper.WaybillMapper;
import com.ecommerce.logistics.dao.vo.ExternalExceptionHandle;
import com.ecommerce.logistics.dao.vo.ProductInfo;
import com.ecommerce.logistics.dao.vo.Vehicle;
import com.ecommerce.logistics.dao.vo.Waybill;
import com.ecommerce.logistics.dao.vo.WaybillMap;
import com.ecommerce.logistics.exception.LogisticsErrorCode;
import com.ecommerce.logistics.service.IPickingBillService;
import com.ecommerce.logistics.service.IWaybillExternalService;
import com.ecommerce.logistics.service.IWaybillService;
import com.ecommerce.logistics.util.LogisticsUtils;
import com.ecommerce.logistics.util.generator.CarriageRouteBizUidGenerator;
import com.ecommerce.member.api.dto.account.AccountDTO;
import com.ecommerce.member.api.dto.account.DriverRegisterByMasterDTO;
import com.ecommerce.member.api.service.IAccountService;
import com.ecommerce.open.api.dto.apicenter.ApiResult;
import com.ecommerce.open.api.dto.apicenter.logistic.ECWaybillDTO;
import com.ecommerce.open.api.dto.apicenter.logistic.ECWaybillItemDTO;
import com.ecommerce.open.api.dto.apicenter.logistic.ECWaybillOpenBillDTO;
import com.ecommerce.open.api.dto.apicenter.logistic.ECWaybillSendOutDTO;
import com.ecommerce.open.api.dto.erp.ECCarInfoDTO;
import com.ecommerce.open.api.service.apicenter.IOpenAPIInvokeService;
import com.ecommerce.open.enums.BizCodeEnum;
import com.ecommerce.open.enums.DefaultErpCodeEnum;
import com.ecommerce.order.api.dto.OrderDTO;
import com.ecommerce.order.api.dto.TakeInfoDTO;
import com.ecommerce.order.api.dto.TakeInfoSearchDTO;
import com.ecommerce.order.api.dto.UnitConversionDTO;
import com.ecommerce.order.api.enums.AdjustAddWayEnum;
import com.ecommerce.order.api.enums.OrderExceptionStatusEnum;
import com.ecommerce.order.api.service.IOrderService;
import com.ecommerce.order.api.service.ITakeInfoService;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Author: <EMAIL>
 * Description:
 * Date: Create in 下午12:23 19/3/2
 */

@Deprecated(since = "2.1.4-RELEASE")
@Service
@Slf4j
public class ExternalWaybillService implements IWaybillExternalService {

    public final static String ERP_OPERATE_USER_ID = "erp_system";

    public final static String ERP_OPERATE_USER_NAME = "erp_system";

    public final static String DATE_PATTERN = "yyyy-MM-dd HH:mm:ss";
    private static final String EXTERNAL_WAYBILL_NO = "外部运单号";
    private static final String WAYBILL_STATUS_EXCEPTION = "运单状态异常";

    @Value("${waybill.exception.retryCount:3}")
    private int retryCount;

    @Autowired
    @Qualifier("bizRedisTemplate")
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private IWaybillQueryBizService waybillQueryBizService;

    @Autowired
    private IWaybillOperationBizService waybillOperationBizService;

    @Autowired(required = false)
    private WaybillMapper waybillMapper;

    @Autowired
    private IOpenAPIInvokeService openAPIInvokeService;

    @Autowired
    private IWaybillService waybillService;

    @Autowired
    private IPickingBillService pickingBillService;

    @Autowired
    private IPickingBillBizService pickingBillBizService;

    @Autowired
    private IVehicleBizService vehicleBizService;

    @Autowired
    private IWaybillSignBizService waybillSignBizService;

    @Autowired
    private IOrderService orderService;

    @Autowired
    private ITakeInfoService takeInfoService;

    @Autowired
    private IOperationRecordBizService operationRecordBizService;

    @Autowired
    private IExternalExceptionBizService externalExceptionBizService;

    @Autowired
    private IAccountService accountService;

    @Autowired
    private CarriageRouteBizUidGenerator carriageRouteBizUidGenerator;

    @Autowired
    private ISMSMessageBizService smsMessageBizService;

    @Autowired(required = false)
    private ProductInfoMapper productInfoMapper;

    @Autowired
    private ILockService redisLockService;

    @Autowired
    private IWaybillMapBizService waybillMapBizService;

    @Autowired
    private RetryErpTaskManager retryErpTaskManager;

    private List<ECWaybillDTO> convertExternalObject(ExternalWaybillRequestDTO externalWaybillRequestDTO) {
        String mainWaybillId = externalWaybillRequestDTO.getWaybillId();
        Boolean subWaybillFlag = Boolean.FALSE;
        if (CsStringUtils.isNotEmpty(externalWaybillRequestDTO.getParentId()) &&
                !externalWaybillRequestDTO.getParentId().equals(externalWaybillRequestDTO.getWaybillId())) {
            mainWaybillId = externalWaybillRequestDTO.getParentId();
            subWaybillFlag = Boolean.TRUE;
        }
        WaybillDetailDTO waybillDetailDTO = waybillQueryBizService.selectWaybillDetailByWaybillId(mainWaybillId);
        List<ECWaybillDTO> ecWaybillList = new ArrayList<>();
        ECWaybillDTO ecWaybillDTO = new ECWaybillDTO();
        BeanUtils.copyProperties(waybillDetailDTO, ecWaybillDTO);
        ecWaybillDTO.setWarehouseCode(LogisticsUtils.defaultIfCondition(CsStringUtils.isEmpty(ecWaybillDTO.getWarehouseCode()), "warehouseCode", ecWaybillDTO.getWarehouseCode()));
        ecWaybillDTO.setWarehouseOrgCode(CsStringUtils.isEmpty(ecWaybillDTO.getWarehouseOrgCode()) ? "warehouseOrgCode" : ecWaybillDTO.getWarehouseOrgCode());
        ecWaybillDTO.setOperator(externalWaybillRequestDTO.getOperatorUserId());
        ecWaybillDTO.setOperatorName(externalWaybillRequestDTO.getOperatorUserName());
        ecWaybillDTO.setErpWaybillNum(waybillDetailDTO.getExternalWaybillNum());
        if (CsStringUtils.isNotEmpty(externalWaybillRequestDTO.getVehicleNum())) {
            ecWaybillDTO.setVehicleNum(externalWaybillRequestDTO.getVehicleNum());
        }
        ecWaybillDTO.setCreateTime(DateUtil.parse(waybillDetailDTO.getCreateTime(), DATE_PATTERN));
        //车辆信息
        ECCarInfoDTO carInfoDTO = new ECCarInfoDTO();
        carInfoDTO.setNumber(waybillDetailDTO.getVehicleNum());
        carInfoDTO.setDriverName(waybillDetailDTO.getDriverName());
        carInfoDTO.setDriverPhone(waybillDetailDTO.getDriverPhone());

        carInfoDTO.setIsGpsDevice(vehicleBizService.vehicleIsGpsDevice(waybillDetailDTO.getVehicleNum()));

        ecWaybillDTO.setCarInfoDTO(carInfoDTO);

        Set<String> deliverySheetNumSet = new HashSet<>();
        if (CollectionUtils.isEmpty(waybillDetailDTO.getProductDetails())) {
            return Collections.emptyList();
        }
        if (subWaybillFlag) {
            handleWayBill(externalWaybillRequestDTO, waybillDetailDTO, ecWaybillDTO);
        }
        waybillDetailDTO.getProductDetails().stream().filter(productDetailDTO
                -> !deliverySheetNumSet.contains(productDetailDTO.getDeliverySheetNum())).forEach(productDetailDTO
                -> deliverySheetNumSet.add(productDetailDTO.getDeliverySheetNum()));
        //批量获取订单ERP信息
        ItemResult<List<OrderDTO>> orderListResult = orderService.getOrderByTakeCodes(new ArrayList<>(deliverySheetNumSet));
        if (!orderListResult.isSuccess()) {
            throw new BizException(BasicCode.UNKNOWN_ERROR, orderListResult.getDescription());
        }
        if (CollectionUtils.isEmpty(orderListResult.getData())) {
            throw new BizException(BasicCode.DATA_NOT_EXIST, "发货单关联的运单");
        }
        Map<String, OrderDTO> orderMap = orderListResult.getData().stream().collect(Collectors.toMap(
                orderDTO -> orderDTO.getTakeInfos().get(0).getTakeCode(), orderDTO -> orderDTO));
        for (ProductDetailDTO productDetailDTO : waybillDetailDTO.getProductDetails()) {
            ecWaybillDTO.setSellerId(productDetailDTO.getSellerId());
            if (orderMap.get(productDetailDTO.getDeliverySheetNum()) != null) {
                OrderDTO orderDTO = orderMap.get(productDetailDTO.getDeliverySheetNum());
                ecWaybillDTO.setErpOrderCode(orderDTO.getErporderCode());
                ecWaybillDTO.setOrderCode(orderDTO.getOrderCode());
                ecWaybillDTO.setUnloadingPlaceCode(orderDTO.getUnloadingCode());
            }
            BeanUtils.copyProperties(productDetailDTO, ecWaybillDTO);
            ecWaybillDTO.setReceiveAddress(productDetailDTO.getReceiverDistrict() + productDetailDTO.getReceiverAddress());
            ecWaybillDTO.setDeliveryTime(DateUtil.parse(productDetailDTO.getDeliveryTime(), "yyyy-MM-dd"));
            ecWaybillDTO.setTakeInfoCode(productDetailDTO.getDeliverySheetNum());
            ECWaybillItemDTO ecWaybillItemDTO = new ECWaybillItemDTO();
            ecWaybillItemDTO.setCommodityCode(productDetailDTO.getCommodityCode());
            ecWaybillItemDTO.setQuantity(productDetailDTO.getQuantity());
            ecWaybillItemDTO.setUnit(productDetailDTO.getProductUnit());
            ecWaybillItemDTO.setLogisticsUnitPrice(productDetailDTO.getCarriageUnitPrice());
            ecWaybillDTO.setWaybillItemList(Collections.singletonList(ecWaybillItemDTO));
            //整合运单
            if (waybillDetailDTO.getProductDetails().size() > 1 &&
                !WaybillStatusEnum.CLOSED.getCode().equals(productDetailDTO.getStatus())) {
                ECWaybillDTO subECWaybill = new ECWaybillDTO();
                BeanUtils.copyProperties(ecWaybillDTO, subECWaybill);
                subECWaybill.setWaybillId(productDetailDTO.getWaybillId());
                subECWaybill.setWaybillNum(productDetailDTO.getWaybillNum());
                subECWaybill.setErpWaybillNum(productDetailDTO.getExternalWaybillNum());
                ecWaybillList.add(subECWaybill);
            } else {
                ecWaybillList.add(ecWaybillDTO);
            }
        }

        return ecWaybillList;
    }

    private static void handleWayBill(ExternalWaybillRequestDTO externalWaybillRequestDTO, WaybillDetailDTO waybillDetailDTO, ECWaybillDTO ecWaybillDTO) {
        ProductDetailDTO subProductDetailDTO = null;
        for (ProductDetailDTO productDetailDTO : waybillDetailDTO.getProductDetails()) {
            if (productDetailDTO.getWaybillId().equals(externalWaybillRequestDTO.getWaybillId())) {
                subProductDetailDTO = productDetailDTO;
                ecWaybillDTO.setWaybillId(productDetailDTO.getWaybillId());
                ecWaybillDTO.setWaybillNum(productDetailDTO.getWaybillNum());
                ecWaybillDTO.setErpWaybillNum(productDetailDTO.getExternalWaybillNum());
                break;
            }
        }
        waybillDetailDTO.getProductDetails().clear();
        waybillDetailDTO.getProductDetails().add(subProductDetailDTO);
    }

    @Override
    public void createExternalWaybill(ExternalWaybillRequestDTO externalWaybillRequestDTO) {
        Waybill waybill = waybillQueryBizService.selectWaybillById(externalWaybillRequestDTO.getWaybillId());
        Waybill failWaybill = null;
        try {
            externalWaybillRequestDTO.setParentId(waybill.getParentId());
            List<ECWaybillDTO> ecWaybillList = convertExternalObject(externalWaybillRequestDTO);
            if (CollectionUtils.isEmpty(ecWaybillList)) {
                throw new BizException(BasicCode.DATA_NOT_EXIST, "运单信息");
            }
            log.info("createExternalWaybill:" + JSON.toJSONString(ecWaybillList));
            //整合运单锁定主运单
            if (!CsStringUtils.equals(waybill.getWaybillId(), waybill.getParentId())) {
                waybill.setSyncFlag(ExternalSyncFlagEnum.FORWARD_SYNC.getCode());
                waybill.setExternalWaybillStatus(WaybillExternalStatusEnum.ERP_CREATING.getCode());
                waybill.setUpdateTime(new Date());
            }
            for (ECWaybillDTO ecWaybillDTO : ecWaybillList) {
                Waybill subWaybill = new Waybill();
                subWaybill.setWaybillId(ecWaybillDTO.getWaybillId());
                subWaybill.setSyncFlag(ExternalSyncFlagEnum.FORWARD_SYNC.getCode());
                subWaybill.setExternalWaybillStatus(WaybillExternalStatusEnum.ERP_CREATING.getCode());

                //传入值:是否为平台配送
                boolean platformDeliveryFlag = waybillQueryBizService.isPlatformDelivery(ecWaybillDTO.getWaybillId());
                ecWaybillDTO.setIfPlatformDelivery(platformDeliveryFlag);

                subWaybill.setUpdateTime(new Date());
                failWaybill = subWaybill;
                ApiResult apiResult = openAPIInvokeService.invoke(BizCodeEnum.EC_LOG_B0.code(),
                        ecWaybillDTO.getSellerId(), JSON.toJSONString(ecWaybillDTO));
                subWaybill.setSyncFailReason(apiResult.isSuccess() ? "" : apiResult.getDescription());
                waybillMapper.updateByPrimaryKeySelective(subWaybill);
                if (!apiResult.isSuccess()) {
                    throw new BizException(BasicCode.UNDEFINED_ERROR, apiResult.getDescription());
                }
                //发送短信
            }
        } catch (Exception e) {
            log.error("运单上传ERP异常:", e);
            String description = "运单上传ERP异常";
            CodeMeta errorCode = BasicCode.UNKNOWN_ERROR;
            if (e instanceof BizException) {
                BizException be = (BizException)e;
                errorCode = be.getErrorCode();
                description = description + "," + be.getMessage();
            }
            ItemResult itemResult = new ItemResult<>(errorCode.getCode(), description);
            externalExceptionHandle(itemResult, waybill, ExternalMethodTypeEnum.ERP_WAYBILL_CREATE.getCode());
            if (failWaybill != null) {
                log.info("创建erp运单失败:{}", failWaybill);
                failWaybill.setUpdateTime(new Date());
                failWaybill.setExternalWaybillStatus(WaybillExternalStatusEnum.ERP_CREATE_FAIL.getCode());
                waybillMapper.updateByPrimaryKeySelective(failWaybill);
            }
            throw new BizException(errorCode, description);
        }
    }

    @Override
    public void updateExternalWaybill(ExternalWaybillRequestDTO externalWaybillRequestDTO) {
        Waybill waybill = waybillQueryBizService.selectWaybillById(externalWaybillRequestDTO.getWaybillId());
        try {
            externalWaybillRequestDTO.setParentId(waybill.getParentId());
            List<ECWaybillDTO> ecWaybillList = convertExternalObject(externalWaybillRequestDTO);
            if (CollectionUtils.isEmpty(ecWaybillList)) {
                throw new BizException(BasicCode.DATA_NOT_EXIST, "运单信息");
            }
            log.info("updateExternalWaybill:" + JSON.toJSONString(ecWaybillList));
            //整合运单锁定主运单
            if (!CsStringUtils.equals(waybill.getWaybillId(), waybill.getParentId())) {
                waybill.setExternalWaybillStatus(WaybillExternalStatusEnum.ERP_CHANGING.getCode());
                waybill.setUpdateTime(new Date());
            }
            for (ECWaybillDTO ecWaybillDTO : ecWaybillList) {
                Waybill subWaybill = new Waybill();
                subWaybill.setWaybillId(ecWaybillDTO.getWaybillId());
                subWaybill.setExternalWaybillStatus(WaybillExternalStatusEnum.ERP_CHANGING.getCode());
                subWaybill.setUpdateTime(new Date());

                //传入值:是否为平台配送
                ecWaybillDTO.setIfPlatformDelivery(waybillQueryBizService.isPlatformDelivery(ecWaybillDTO.getWaybillId()));

                ApiResult apiResult = openAPIInvokeService.invoke(BizCodeEnum.EC_LOG_S0.code(),
                        ecWaybillDTO.getSellerId(), JSON.toJSONString(ecWaybillDTO));
                subWaybill.setSyncFailReason(apiResult.isSuccess() ? "" : apiResult.getDescription());
                waybillMapper.updateByPrimaryKeySelective(subWaybill);
                if (!apiResult.isSuccess()) {
                    throw new BizException(BasicCode.UNDEFINED_ERROR, apiResult.getDescription());
                }
            }
        } catch (Exception e) {
            log.error("接口内部异常:" + e);
            String description = "接口内部异常";
            CodeMeta errorCode = BasicCode.UNKNOWN_ERROR;
            if (e instanceof BizException) {
                BizException be = (BizException)e;
                errorCode = be.getErrorCode();
                description = description + "," + be.getMessage();
            }
            ItemResult itemResult = new ItemResult<>(errorCode.getCode(), description);
            externalExceptionHandle(itemResult, waybill, ExternalMethodTypeEnum.ERP_WAYBILL_CHANGE.getCode());
            throw new BizException(errorCode, description);
        }
    }

    @Override
    public ItemResult<Void> createExternalWaybillCallBack(ItemResult<ERPWaybillDTO> itemResult) {
        log.info("createExternalWaybillCallBack:" + JSON.toJSONString(itemResult));
        Waybill waybill = waybillQueryBizService.queryWaybillByWaybillNum(itemResult.getData().getWaybillNum());
        //修改当前运单外部执行状态
        if (itemResult.isSuccess()) {
            ERPWaybillDTO erpWaybillDTO = itemResult.getData();
            if (CsStringUtils.isEmpty(erpWaybillDTO.getErpWaybillNum())) {
                throw new BizException(BasicCode.INVALID_PARAM, EXTERNAL_WAYBILL_NO);
            }
            if (CollectionUtils.isEmpty(erpWaybillDTO.getWaybillItemList())) {
                throw new BizException(BasicCode.INVALID_PARAM, "商品明细");
            }
            if (!WaybillExternalStatusEnum.ERP_CREATE_FAIL.getCode().equals(waybill.getExternalWaybillStatus()) &&
                !WaybillExternalStatusEnum.ERP_CREATING.getCode().equals(waybill.getExternalWaybillStatus()) &&
                !WaybillExternalStatusEnum.ERP_CREATE_SUCCESS.getCode().equals(waybill.getExternalWaybillStatus())) {
                throw new BizException(BasicCode.INVALID_PARAM, WAYBILL_STATUS_EXCEPTION);
            }
            if (!DefaultErpCodeEnum.DEFAULT_ERP_WAYBILL_CODE.getCode().equals(itemResult.getData().getErpWaybillNum())) {
                UpdateExternalWaybillDO updateExternalWaybillDO = new UpdateExternalWaybillDO();
                updateExternalWaybillDO.setWaybillId(waybill.getWaybillId());
                updateExternalWaybillDO.setExternalWaybillNum(erpWaybillDTO.getErpWaybillNum());
                updateExternalWaybillDO.setExternalWaybillStatus(WaybillExternalStatusEnum.ERP_CREATE_SUCCESS.getCode());
                updateExternalWaybillDO.setQrCode(erpWaybillDTO.getWaybillItemList().get(0).getQrCode());
                updateExternalWaybillDO.setOriginPlace(erpWaybillDTO.getOriginPlace());
                waybillMapper.updateExternalWaybillInfo(updateExternalWaybillDO);
            }
            //整合运单
            if (!CsStringUtils.equals(waybill.getWaybillId(), waybill.getParentId())) {
                List<SubWaybillDO> subWaybillList = waybillQueryBizService
                        .selectSubWaybillListByParentId(Lists.newArrayList(waybill.getParentId()));
                updateWayBill(subWaybillList, waybill);
            }
        } else {
            waybill.setSyncFailReason(itemResult.getDescription());
            if (CsStringUtils.isNotEmpty(itemResult.getData().getErpWaybillNum())) {
                waybill.setExternalWaybillNum(itemResult.getData().getErpWaybillNum());
            }
            waybill.setUpdateTime(new Date());
            waybillMapper.updateByPrimaryKeySelective(waybill);
            itemResult.setCode(LogisticsErrorCode.ERP_CALLBACK_ERROR.getCode());
        }
        externalExceptionHandle(itemResult, waybill, ExternalMethodTypeEnum.ERP_WAYBILL_CREATE.getCode());

        return new ItemResult<>(null);
    }

    private void updateWayBill(List<SubWaybillDO> subWaybillList, Waybill waybill) {
        if (CollectionUtils.isNotEmpty(subWaybillList)) {
            Boolean mainWaybillFlag = Boolean.TRUE;
            for (SubWaybillDO subWaybillDO : subWaybillList) {
                if (!subWaybillDO.getWaybillId().equals(waybill.getWaybillId())) {
                    if (!WaybillStatusEnum.CLOSED.getCode().equals(subWaybillDO.getStatus()) &&
                            !WaybillExternalStatusEnum.ERP_CREATE_SUCCESS.getCode().equals(subWaybillDO.getExternalWaybillNum())) {
                        mainWaybillFlag = Boolean.FALSE;
                        break;
                    }
                }
            }
            if (mainWaybillFlag) {
                Waybill mainWaybill = new Waybill();
                mainWaybill.setWaybillId(waybill.getParentId());
                mainWaybill.setExternalWaybillStatus(WaybillExternalStatusEnum.ERP_CREATE_SUCCESS.getCode());
                mainWaybill.setUpdateTime(new Date());
                waybillMapper.updateByPrimaryKeySelective(mainWaybill);
            }
        }
    }

    @Override
    public void closeExternalWaybill(ExternalWaybillRequestDTO externalWaybillRequestDTO) {
        Waybill waybill = waybillQueryBizService.selectWaybillById(externalWaybillRequestDTO.getWaybillId());
        Waybill needUpdateDBWaybill = new Waybill();
        needUpdateDBWaybill.setWaybillId(waybill.getWaybillId());
        needUpdateDBWaybill.setWaybillNum(waybill.getWaybillNum());
        try {
            if (CsStringUtils.equals(waybill.getExternalWaybillStatus(), WaybillExternalStatusEnum.ERP_CREATE_FAIL.getCode()) ||
                    CsStringUtils.equals(waybill.getExternalWaybillStatus(), WaybillExternalStatusEnum.ERP_CLOSE_SUCCESS.getCode())
            ) {
                //原先erp就创建失败,直接返回
                return;
            }
            externalWaybillRequestDTO.setParentId(waybill.getParentId());
            List<ECWaybillDTO> ecWaybillList = convertExternalObject(externalWaybillRequestDTO);
            if (CollectionUtils.isEmpty(ecWaybillList)) {
                throw new BizException(BasicCode.DATA_NOT_EXIST, "运单信息");
            }
            log.info("closeExternalWaybill:" + JSON.toJSONString(ecWaybillList));
            //整合运单锁定主运单
            if (!CsStringUtils.equals(waybill.getWaybillId(), waybill.getParentId())) {
                Waybill mainWaybill = new Waybill();
                mainWaybill.setWaybillId(waybill.getParentId());
                mainWaybill.setExternalWaybillStatus(WaybillExternalStatusEnum.ERP_CLOSING.getCode());
                mainWaybill.setUpdateTime(new Date());
                waybillMapper.updateByPrimaryKeySelective(mainWaybill);
            }
            ECWaybillDTO ecWaybillDTO = ecWaybillList.get(0);
            needUpdateDBWaybill.setExternalWaybillStatus(WaybillExternalStatusEnum.ERP_CLOSING.getCode());
            needUpdateDBWaybill.setUpdateTime(new Date());
            String bizCode = BizCodeEnum.EC_LOG_Z0.code();
            List<ProductInfo> productInfoList = productInfoMapper.selectProductByWaybillId(waybill.getWaybillId());
            if (!CollectionUtils.isEmpty(productInfoList) && AdjustAddWayEnum.SELLER_SIGIN.getCode().equals(
                    productInfoList.get(0).getSignType())) {
                bizCode = BizCodeEnum.EC_HOG_Z0.code();
            }
            ApiResult apiResult = openAPIInvokeService.invoke(bizCode, ecWaybillDTO.getSellerId(), JSON.toJSONString(ecWaybillDTO));
            if (!apiResult.isSuccess()) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, apiResult.getDescription());
            }
            //正常调用下更新运单状态为ERP关闭中
            waybillMapper.updateByPrimaryKeySelective(needUpdateDBWaybill);
        } catch (Exception e) {
            log.error("关闭ERP交货单异常, {}", e);
            String description = "关闭ERP交货单异常";
            CodeMeta errorCode = BasicCode.UNKNOWN_ERROR;
            if (e instanceof BizException) {
                BizException be = (BizException)e;
                errorCode = be.getErrorCode();
                description = be.getMessage();
            }
            //更新运单状态
            needUpdateDBWaybill.setSyncFailReason(description);
            waybillMapper.updateByPrimaryKeySelective(needUpdateDBWaybill);
            ItemResult itemResult = new ItemResult<>(errorCode.getCode(), description);
            externalExceptionHandle(itemResult, waybill, ExternalMethodTypeEnum.ERP_WAYBILL_CLOSE.getCode());
            throw new BizException(errorCode, description);
        }
    }

    @Override
    public ItemResult<Void> updateExternalWaybillCallBack(ItemResult<ERPWaybillDTO> itemResult) {
        log.info("updateExternalWaybillCallBack:" + JSON.toJSONString(itemResult));
        Waybill waybill = waybillQueryBizService.queryWaybillByWaybillNum(itemResult.getData().getWaybillNum());
        //修改当前运单外部执行状态
        if (itemResult.isSuccess()) {
            if (!WaybillExternalStatusEnum.ERP_CHANGING.getCode().equals(waybill.getExternalWaybillStatus()) &&
                !WaybillExternalStatusEnum.ERP_CHANGE_SUCCESS.getCode().equals(waybill.getExternalWaybillStatus())) {
                throw new BizException(BasicCode.INVALID_PARAM, WAYBILL_STATUS_EXCEPTION);
            }
            waybill.setExternalWaybillStatus(WaybillExternalStatusEnum.ERP_CHANGE_SUCCESS.getCode());
            waybill.setUpdateTime(new Date());
            //整合运单
            updateWayBill(waybill);
        } else {
            waybill.setSyncFailReason(itemResult.getDescription());
            waybill.setUpdateTime(new Date());
            itemResult.setCode(LogisticsErrorCode.ERP_CALLBACK_ERROR.getCode());
        }
        waybillMapper.updateByPrimaryKeySelective(waybill);
        externalExceptionHandle(itemResult, waybill, ExternalMethodTypeEnum.ERP_WAYBILL_CHANGE.getCode());

        return new ItemResult<>(null);
    }

    private void updateWayBill(Waybill waybill) {
        if (!CsStringUtils.equals(waybill.getWaybillId(), waybill.getParentId())) {
            List<SubWaybillDO> subWaybillList = waybillQueryBizService
                    .selectSubWaybillListByParentId(Lists.newArrayList(waybill.getParentId()));
            if (CollectionUtils.isEmpty(subWaybillList)) {
                return;
            }

            Boolean mainWaybillFlag = Boolean.TRUE;
            for (SubWaybillDO subWaybillDO : subWaybillList) {
                if (!subWaybillDO.getWaybillId().equals(waybill.getWaybillId())) {
                    if (!WaybillStatusEnum.CLOSED.getCode().equals(subWaybillDO.getStatus()) &&
                            !WaybillExternalStatusEnum.ERP_CHANGE_SUCCESS.getCode().equals(subWaybillDO.getExternalWaybillNum())) {
                        mainWaybillFlag = Boolean.FALSE;
                        break;
                    }
                }
            }
            if (mainWaybillFlag) {
                Waybill mainWaybill = new Waybill();
                mainWaybill.setWaybillId(waybill.getParentId());
                mainWaybill.setExternalWaybillStatus(WaybillExternalStatusEnum.ERP_CHANGE_SUCCESS.getCode());
                mainWaybill.setUpdateTime(new Date());
                waybillMapper.updateByPrimaryKeySelective(mainWaybill);
            }
        }
    }

    @Override
    public ItemResult<Void> closeExternalWaybillCallBack(ItemResult<ERPWaybillDTO> itemResult) {
        log.info("closeExternalWaybillCallBack:" + JSON.toJSONString(itemResult));
        Waybill waybill = waybillQueryBizService.queryWaybillByWaybillNum(itemResult.getData().getWaybillNum());
        Waybill needUpdateDBWaybill = new Waybill();
        needUpdateDBWaybill.setWaybillNum(waybill.getWaybillNum());
        needUpdateDBWaybill.setWaybillId(waybill.getWaybillId());
        //修改当前运单外部执行状态
        if (itemResult.isSuccess()) {
            if (!WaybillExternalStatusEnum.ERP_CLOSING.getCode().equals(waybill.getExternalWaybillStatus()) &&
                !WaybillExternalStatusEnum.ERP_CLOSE_SUCCESS.getCode().equals(waybill.getExternalWaybillStatus())) {
                throw new BizException(BasicCode.INVALID_PARAM, WAYBILL_STATUS_EXCEPTION);
            }
            needUpdateDBWaybill.setExternalWaybillStatus(WaybillExternalStatusEnum.ERP_CLOSE_SUCCESS.getCode());
            needUpdateDBWaybill.setUpdateTime(new Date());
            //整合运单
            if (!CsStringUtils.equals(waybill.getWaybillId(), waybill.getParentId())) {
                List<SubWaybillDO> subWaybillList = waybillQueryBizService
                        .selectSubWaybillListByParentId(Lists.newArrayList(waybill.getParentId()));
                updateSubWayBill(subWaybillList, waybill);
            }
        } else {
            needUpdateDBWaybill.setSyncFailReason(itemResult.getDescription());
            needUpdateDBWaybill.setUpdateTime(new Date());
            itemResult.setCode(LogisticsErrorCode.ERP_CALLBACK_ERROR.getCode());
        }
        waybillMapper.updateByPrimaryKeySelective(needUpdateDBWaybill);
        externalExceptionHandle(itemResult, needUpdateDBWaybill, ExternalMethodTypeEnum.ERP_WAYBILL_CLOSE.getCode());

        return new ItemResult<>(null);
    }

    private void updateSubWayBill(List<SubWaybillDO> subWaybillList, Waybill waybill) {
        if (CollectionUtils.isNotEmpty(subWaybillList)) {
            Boolean mainWaybillFlag = Boolean.TRUE;
            for (SubWaybillDO subWaybillDO : subWaybillList) {
                if (!subWaybillDO.getWaybillId().equals(waybill.getWaybillId())) {
                    if (!WaybillStatusEnum.CLOSED.getCode().equals(subWaybillDO.getStatus()) &&
                            !WaybillExternalStatusEnum.ERP_CLOSE_SUCCESS.getCode().equals(subWaybillDO.getExternalWaybillNum())) {
                        mainWaybillFlag = Boolean.FALSE;
                        break;
                    }
                }
            }
            if (mainWaybillFlag) {
                Waybill mainWaybill = new Waybill();
                mainWaybill.setWaybillId(waybill.getParentId());
                mainWaybill.setExternalWaybillStatus(WaybillExternalStatusEnum.ERP_CLOSE_SUCCESS.getCode());
                mainWaybill.setUpdateTime(new Date());
                waybillMapper.updateByPrimaryKeySelective(mainWaybill);
            }
        }
    }

    @Override
    public ItemResult<String> createECWaybill(ERPWaybillDTO erpWaybillDTO) {
        String identifier = null;
        String lockResource = erpWaybillDTO.getErpWaybillNum();
        try {
            identifier = redisLockService.lock(lockResource);
            log.info("createECWaybill:" + JSON.toJSONString(erpWaybillDTO));
            boolean isConcrete = false;
            if (CsStringUtils.isEmpty(erpWaybillDTO.getErpWaybillNum())) {
                throw new BizException(BasicCode.INVALID_PARAM, EXTERNAL_WAYBILL_NO);
            }
            //业务幂等校验
            try {
                Waybill dbWaybill = waybillQueryBizService.queryWaybillByExternalWaybillNum(erpWaybillDTO.getErpWaybillNum());
                if (dbWaybill != null) {
                    //已存在,直接返回:
                    return new ItemResult<>(dbWaybill.getWaybillNum());
                }
            } catch (BizException e) {
                log.info("数据验证通过");
            }
            verifyErpWayBill(erpWaybillDTO);

            List<ERPWaybillItemDTO> waybillItemList = erpWaybillDTO.getWaybillItemList();
            ERPWaybillItemDTO waybillItemDTO =  waybillItemList.get(0);
            PickingBillQueryDTO pickingBillQueryDTO = new PickingBillQueryDTO();
            isConcrete = handleTakeInfo(erpWaybillDTO, isConcrete);
            pickingBillQueryDTO.setDeliverySheetNum(erpWaybillDTO.getDeliverySheetNum());
            pickingBillQueryDTO.setCommodityCode(waybillItemDTO.getCommodityCode());
            //根据提货单号查询提货单信息
            PickingBillDTO pickingBillDTO = pickingBillBizService.queryPickingBillByCondition(pickingBillQueryDTO);
            if (pickingBillDTO == null) {
                throw new BizException(LogisticsErrorCode.DATA_EXCEPTION, "发货单号查询不到对应的提货单:deliverySheetNum=" + erpWaybillDTO.getDeliverySheetNum());
            }
            //查询当前车辆是否存在
            ERPVehicleDTO erpVehicleDTO = erpWaybillDTO.getErpVehicleDTO();
            List<Vehicle> vehicleList = vehicleBizService.queryVehicleByCondition(pickingBillDTO.getSellerId(), erpVehicleDTO.getNumber());
            String vehicleId = CollectionUtils.isNotEmpty(vehicleList) ? vehicleList.get(0).getVehicleId() :
                    vehicleBizService.addErpVehicle(erpVehicleDTO, pickingBillDTO, waybillItemDTO.getQuantity());

            AssignWaybillDTO assignWaybillDTO = new AssignWaybillDTO();
            AccountDTO driverAccount = getDriverAccount(isConcrete, erpVehicleDTO, pickingBillDTO);

            assignWaybillDTO.setPickingBillId(pickingBillDTO.getPickingBillId());
            assignWaybillDTO.setExternalDispatch(1);
            assignWaybillDTO.setOperatorUserId(pickingBillDTO.getSellerId());
            assignWaybillDTO.setOperatorUserName(pickingBillDTO.getSellerName());
            List<AssignWaybillDetailDTO> assignWaybillDetailList = Lists.newArrayList();
            AssignWaybillDetailDTO assignWaybillDetailDTO = new AssignWaybillDetailDTO();
            assignWaybillDetailDTO.setVehicleId(vehicleId);
            assignWaybillDetailDTO.setQuantity(waybillItemDTO.getQuantity());
            assignWaybillDetailDTO.setQrCode(waybillItemDTO.getQrCode());
            assignWaybillDetailDTO.setExternalWaybillNum(erpWaybillDTO.getErpWaybillNum());
            assignWaybillDetailDTO.setOriginPlace(erpWaybillDTO.getOriginPlace());

            if (driverAccount != null) {
                assignWaybillDetailDTO.setDriverId(driverAccount.getAccountId());
                assignWaybillDetailDTO.setDriverName(driverAccount.getAccountName());
                assignWaybillDetailDTO.setDriverPhone(driverAccount.getMobile());
            }

            assignWaybillDetailList.add(assignWaybillDetailDTO);
            assignWaybillDTO.setAssignWaybillDetailList(assignWaybillDetailList);
            assignWaybillDTO.setEmptyLoadFlag(erpWaybillDTO.getEmptyLoadFlag());
            pickingBillService.assignCreateWaybill(assignWaybillDTO);
            //获取运单信息
            List<WaybillDTO> waybillList = waybillQueryBizService.selectWaybillsByPickingBillIds(Lists.newArrayList(pickingBillDTO.getPickingBillId()));
            if (CollectionUtils.isEmpty(waybillList)) {
                throw new BizException(LogisticsErrorCode.DATA_EXCEPTION, "查询运单失败");
            }

            WaybillDTO oneWaybillDTO = waybillList.get(0);

            if (isConcrete) {
                //如果是混凝土,直接出站
                LeaveWarehouseDTO leaveWarehouseDTO = new LeaveWarehouseDTO();
                leaveWarehouseDTO.setWaybillId(oneWaybillDTO.getWaybillId());
                if (waybillItemList.get(0).getQuantity() == null) {
                    throw new BizException(BasicCode.INVALID_PARAM, "实际出场数量");
                }
                leaveWarehouseDTO.setActualQuantity(waybillItemList.get(0).getQuantity());
                leaveWarehouseDTO.setUserId(ERP_OPERATE_USER_ID);
                leaveWarehouseDTO.setUserName(ERP_OPERATE_USER_NAME);
                leaveWarehouseDTO.setExternalFlag(1);
                leaveWarehouseDTO.setLogisticsUnitPrice(BigDecimal.ZERO);
                waybillService.inputLeaveWarehouseQuantity(leaveWarehouseDTO);
            }

            if (!CsStringUtils.equals(pickingBillDTO.getType(), PickingBillTypeEnum.PLATFORM_DELIVERY.getCode())) {
                smsMessageBizService.createWaybillNotifyDriver(oneWaybillDTO.getWaybillId());
            }
            return new ItemResult<>(oneWaybillDTO.getWaybillNum());
        } catch (DistributeLockException e) {
            throw new BizException(BasicCode.INVALID_PARAM, "重复操作：ERPWaybillDTO=" + erpWaybillDTO);
        } finally {
            if (CsStringUtils.isNotEmpty(identifier)) {
                redisLockService.unlock(lockResource, identifier);
            }
        }
    }

    private AccountDTO getDriverAccount(boolean isConcrete,
                                        ERPVehicleDTO erpVehicleDTO,
                                        PickingBillDTO pickingBillDTO) {
        AccountDTO driverAccount = null;
        if (isConcrete) {
            //查询司机:
            List<AccountDTO> driverAccountList = accountService.findSellerDriverByMobilePhoneAndMemberId(erpVehicleDTO.getDriverPhone(), pickingBillDTO.getSellerId());
            if (CollectionUtils.isEmpty(driverAccountList)) {
                //自动创建卖家司机
                DriverRegisterByMasterDTO driverRegisterByMasterDTO = new DriverRegisterByMasterDTO();
                driverRegisterByMasterDTO.setMemberId(pickingBillDTO.getSellerId());
                driverRegisterByMasterDTO.setAccountName(erpVehicleDTO.getDriverName());
                driverRegisterByMasterDTO.setMobile(erpVehicleDTO.getDriverPhone());
                driverRegisterByMasterDTO.setPassword("123456");
                driverAccount = accountService.registerDriverByMaster(driverRegisterByMasterDTO);
            } else {
                driverAccount = driverAccountList.get(0);
            }
        }
        return driverAccount;
    }

    private boolean handleTakeInfo(ERPWaybillDTO erpWaybillDTO, boolean isConcrete) {
        if (CsStringUtils.isBlank(erpWaybillDTO.getDeliverySheetNum())) {
            //混凝土没有发货单号
            isConcrete = true;
            TakeInfoSearchDTO takeInfoSearchDTO = new TakeInfoSearchDTO();
            takeInfoSearchDTO.setOrderCode(erpWaybillDTO.getOrderCode());
            takeInfoSearchDTO.setPageNumber(1);
            takeInfoSearchDTO.setPageSize(1);
            PageInfo<TakeInfoDTO> takeInfoDTOPageInfo = takeInfoService.pagePlatformTakeInfo(takeInfoSearchDTO);
            if (takeInfoDTOPageInfo != null && !CollectionUtils.isEmpty(takeInfoDTOPageInfo.getList())) {
                erpWaybillDTO.setDeliverySheetNum(takeInfoDTOPageInfo.getList().get(0).getTakeCode());
            } else {
                throw new BizException(BasicCode.INVALID_PARAM, "发货单号");
            }
        }
        return isConcrete;
    }

    private static void verifyErpWayBill(ERPWaybillDTO erpWaybillDTO) {
        if (CollectionUtils.isEmpty(erpWaybillDTO.getWaybillItemList())) {
            throw new BizException(BasicCode.INVALID_PARAM, "商品信息列表");
        }
        if (erpWaybillDTO.getErpVehicleDTO() == null) {
            throw new BizException(BasicCode.INVALID_PARAM, "车辆信息");
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public ItemResult<Void> updateECWaybill(ERPWaybillDTO ecWaybillDTO) {
        log.info("updateECWaybill:" + JSON.toJSONString(ecWaybillDTO));
        if (CsStringUtils.isEmpty(ecWaybillDTO.getWaybillNum())) {
            throw new BizException(LogisticsErrorCode.DATA_EXCEPTION, "erp回调数据异常");
        }
        Waybill waybill = waybillQueryBizService.queryWaybillByExternalWaybillNum(ecWaybillDTO.getErpWaybillNum());
        List<WaybillDTO> waybillList = waybillQueryBizService.selectWaybillsByWaybillIds(Lists.newArrayList(waybill.getWaybillId()));
        if (CollectionUtils.isEmpty(waybillList)) {
            throw new BizException(BasicCode.DATA_NOT_EXIST, "运单信息");
        }
        //修改运单外部状态
        waybill.setExternalWaybillStatus(WaybillExternalStatusEnum.ERP_CHANGE_SUCCESS.getCode());
        waybillMapper.updateByPrimaryKeySelective(waybill);
        SellerAssignWaybillDTO sellerAssignWaybillDTO = new SellerAssignWaybillDTO();
        sellerAssignWaybillDTO.setWaybillId(waybill.getWaybillId());
        sellerAssignWaybillDTO.setVehicleNum(ecWaybillDTO.getVehicleNum());
        sellerAssignWaybillDTO.setUserId(ERP_OPERATE_USER_ID);
        sellerAssignWaybillDTO.setUserName(ERP_OPERATE_USER_NAME);
        waybillOperationBizService.assignWaybill(sellerAssignWaybillDTO);

//        //修改EC运单数据(车辆和装载数量)

        return new ItemResult<>(null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ItemResult<Void> closeECWaybill(ERPWaybillDTO ecWaybillDTO) {
        log.info("closeECWaybill:" + JSON.toJSONString(ecWaybillDTO));
        if (CsStringUtils.isEmpty(ecWaybillDTO.getErpWaybillNum())) {
            throw new BizException(BasicCode.INVALID_PARAM, EXTERNAL_WAYBILL_NO);
        }
        Waybill waybill = waybillQueryBizService.queryWaybillByExternalWaybillNum(ecWaybillDTO.getErpWaybillNum());
        DiscardWaybillDTO primaryDiscardWaybillDTO = new DiscardWaybillDTO();
        primaryDiscardWaybillDTO.setWaybillNum(waybill.getWaybillNum());
        primaryDiscardWaybillDTO.setOperatorId(ERP_OPERATE_USER_ID);
        primaryDiscardWaybillDTO.setOperatorName(ERP_OPERATE_USER_NAME);
        waybillService.discardWaybill(primaryDiscardWaybillDTO);

        if (!CsStringUtils.equals(waybill.getBillProxyType(), BillProxyTypeEnum.NORMAL.getCode())) {
            //背靠背运单,还有二级运单的数量回滚
            WaybillMap waybillMap = waybillMapBizService.findByPrimaryWaybillId(waybill.getWaybillId());
            DiscardWaybillDTO secondaryDiscardWaybillDTO = new DiscardWaybillDTO();
            secondaryDiscardWaybillDTO.setWaybillNum(waybillMap.getSecondaryWaybillNum());
            secondaryDiscardWaybillDTO.setOperatorId(ERP_OPERATE_USER_ID);
            secondaryDiscardWaybillDTO.setOperatorName(ERP_OPERATE_USER_NAME);
            waybillService.discardWaybill(secondaryDiscardWaybillDTO);
        }


        /****** ERP 的关闭对应电商的作废 ***/
//
//        //修改运单外部状态
//        //关闭运单

        return new ItemResult<>(null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ItemResult<Void> handlerExternalExecuteStatus(ERPWaybillExecuteDTO erpWaybillExecuteDTO) {
        String identifier = null;
        String lockResource = erpWaybillExecuteDTO.getExternalWaybillNum();
        try {
            identifier = redisLockService.lock(lockResource);
            log.info("handlerExternalExecuteStatus:" + JSON.toJSONString(erpWaybillExecuteDTO));
            if (CsStringUtils.isEmpty(erpWaybillExecuteDTO.getExternalWaybillNum())) {
                throw new BizException(BasicCode.INVALID_PARAM, EXTERNAL_WAYBILL_NO);
            }
            List<ERPWaybillItemDTO> waybillItemList = erpWaybillExecuteDTO.getWaybillItemList();
            if (CollectionUtils.isEmpty(waybillItemList)) {
                throw new BizException(BasicCode.INVALID_PARAM, "运单明细列表");
            }
            Waybill waybill = waybillQueryBizService.queryWaybillByExternalWaybillNum(erpWaybillExecuteDTO.getExternalWaybillNum());
            List<ERPWaybillExecuteStatusDTO> waybillExecuteList = erpWaybillExecuteDTO.getWaybillExecuteList();
            if (CollectionUtils.isEmpty(waybillExecuteList)) {
                throw new BizException(LogisticsErrorCode.DATA_EXCEPTION, "无效的运单执行状态");
            }
            ERPWaybillExecuteStatusDTO waybillExecuteStatusDTO = waybillExecuteList.get(0);
            OperationRecordAddDTO operationRecordAddDTO = new OperationRecordAddDTO();
            operationRecordAddDTO.setEntryId(waybill.getWaybillId());
            operationRecordAddDTO.setType(OperationRecordTypeEnum.WAYBILL.getCode());
            operationRecordAddDTO.setOperatorId(ERP_OPERATE_USER_ID);
            operationRecordAddDTO.setOperatorName(ERP_OPERATE_USER_NAME);
            operationRecordAddDTO.setCreateTime(DateUtil.parse(waybillExecuteStatusDTO.getExecuteTime(), DATE_PATTERN));
            WaybillExternalExecuteStatusEnum waybillExternalExecuteStatusEnum =
                    WaybillExternalExecuteStatusEnum.valueOfCode(waybillExecuteStatusDTO.getExecuteStatus());
            if (waybillExternalExecuteStatusEnum == null) {
                throw new BizException(BasicCode.INVALID_PARAM, "找不到对应的运单执行状态");
            }
            switch (waybillExternalExecuteStatusEnum) {
//            //已开单
//            case OPEN_BILL:
                //车辆已进站
                case ENTERED:
                    //状态校验
                    List<String> validStatusList = Lists.newArrayList(
                            WaybillExternalStatusEnum.ERP_CREATE_SUCCESS.getCode(),
                            WaybillExternalStatusEnum.ERP_CHANGE_SUCCESS.getCode());
                    if (!validStatusList.contains(waybill.getExternalWaybillStatus())) {
                        throw new BizException(LogisticsErrorCode.DATA_EXCEPTION, "无效的执行状态");
                    }
                    waybill.setExternalWaybillStatus(WaybillExternalStatusEnum.ENTER_WAREHOUSE.getCode());
                    waybillMapper.updateByPrimaryKeySelective(waybill);
                    operationRecordAddDTO.setOperationType(WaybillOperationTypeEnum.VEHICLE_ENTER.getCode());
                    operationRecordAddDTO.setContent("车辆已进站");
                    operationRecordBizService.saveOperationRecord(operationRecordAddDTO, operationRecordAddDTO.getCreateTime());
                    break;
                case PASS_TARE:
                    waybill.setExternalWaybillStatus(WaybillExternalStatusEnum.PASS_TARE.getCode());
                    waybillMapper.updateByPrimaryKeySelective(waybill);
                    operationRecordAddDTO.setOperationType(WaybillOperationTypeEnum.PASS_TARE.getCode());
                    operationRecordAddDTO.setContent("车辆已过皮重");
                    operationRecordBizService.saveOperationRecord(operationRecordAddDTO, operationRecordAddDTO.getCreateTime());
                    break;
                case PASS_ROUGH:
                    waybill.setExternalWaybillStatus(WaybillExternalStatusEnum.PASS_ROUGH.getCode());
                    waybillMapper.updateByPrimaryKeySelective(waybill);
                    operationRecordAddDTO.setOperationType(WaybillOperationTypeEnum.PASS_ROUGH.getCode());
                    operationRecordAddDTO.setContent("车辆已过毛重");
                    operationRecordBizService.saveOperationRecord(operationRecordAddDTO, operationRecordAddDTO.getCreateTime());
                    break;
                case LEAVE_WAREHOUSE:
                    //状态幂等性校验
                    if (WaybillExternalStatusEnum.LEAVE_WAREHOUSE.getCode().equals(waybill.getExternalWaybillStatus())) {
                        return new ItemResult<>(null);
                    }
                    waybill.setExternalWaybillStatus(WaybillExternalStatusEnum.LEAVE_WAREHOUSE.getCode());
                    waybillMapper.updateByPrimaryKeySelective(waybill);
                    LeaveWarehouseDTO leaveWarehouseDTO = new LeaveWarehouseDTO();
                    leaveWarehouseDTO.setWaybillId(waybill.getWaybillId());
                    if (erpWaybillExecuteDTO.getWaybillItemList().get(0).getActualQuantity() == null) {
                        throw new BizException(BasicCode.INVALID_PARAM, "实际出场数量");
                    }
                    leaveWarehouseDTO.setActualQuantity(erpWaybillExecuteDTO.getWaybillItemList().get(0).getActualQuantity());
                    leaveWarehouseDTO.setLeaveWarehouseTime(waybillExecuteStatusDTO.getExecuteTime());
                    leaveWarehouseDTO.setUserId(ERP_OPERATE_USER_ID);
                    leaveWarehouseDTO.setUserName(ERP_OPERATE_USER_NAME);
                    leaveWarehouseDTO.setExternalFlag(1);
                    if (waybillItemList.get(0).getLogisticsUnitPrice() == null) {
                        throw new BizException(BasicCode.INVALID_PARAM, "物流运费单价");
                    }
                    leaveWarehouseDTO.setLogisticsUnitPrice(waybillItemList.get(0).getLogisticsUnitPrice());
                    leaveWarehouseDTO.setOriginPlace(waybillItemList.get(0).getOriginPlace());
                    waybillService.inputLeaveWarehouseQuantity(leaveWarehouseDTO);
                    break;
                default:
                    break;
            }

            return new ItemResult<>(null);
        } catch (DistributeLockException e) {
            throw new BizException(BasicCode.INVALID_PARAM, "重复操作：ERPWaybillExecuteDTO=" + erpWaybillExecuteDTO);
        } finally {
            if (CsStringUtils.isNotEmpty(identifier)) {
                redisLockService.unlock(lockResource, identifier);
            }
        }
    }

    @Override
    public ItemResult<Void> waybillRetryHandler(WaybillRetryHandlerDTO waybillRetryHandlerDTO) {
        //获取当前运单外部状态
        Waybill waybill = waybillQueryBizService.selectWaybillById(waybillRetryHandlerDTO.getWaybillId());
        WaybillExternalStatusEnum waybillExternalStatusEnum = WaybillExternalStatusEnum.valueOfCode(waybill.getExternalWaybillStatus());
        if (waybillExternalStatusEnum == null) {
            throw new BizException(LogisticsErrorCode.DATA_EXCEPTION, "无效的外部运单状态");
        }
        ExternalWaybillRequestDTO externalWaybillRequestDTO = new ExternalWaybillRequestDTO();
        BeanUtils.copyProperties(waybillRetryHandlerDTO, externalWaybillRequestDTO);
        switch (waybillExternalStatusEnum) {
            case ERP_CREATING:
            case ERP_CREATE_FAIL:
                createExternalWaybill(externalWaybillRequestDTO);
                break;
            case ERP_CHANGING:
                //获取当前车辆信息
                updateExternalWaybill(externalWaybillRequestDTO);
                break;
            case ERP_CLOSING:
                closeExternalWaybill(externalWaybillRequestDTO);
                break;
            case SETTLEMENT_ING:
                break;
            case ERP_CREATE_SUCCESS:
            case ERP_SHIPPING:
                externalOpenBillAndSendOut(externalWaybillRequestDTO.getWaybillId());
                break;
            default:
                break;
        }

        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> externalOpenBillAndSendOut(String waybillId) {
        WaybillDO waybillDO = waybillMapper.selectWaybillProductDetailByWaybillId(waybillId);
        if (waybillDO == null) {
            throw new BizException(BasicCode.DATA_NOT_EXIST, "运单信息");
        }
        try {
            Date date = new Date();
            ECWaybillOpenBillDTO ecWaybillOpenBillDTO = new ECWaybillOpenBillDTO();
            ecWaybillOpenBillDTO.setWaybillId(waybillDO.getWaybillId());
            ecWaybillOpenBillDTO.setWaybillNum(waybillDO.getWaybillNum());
            ecWaybillOpenBillDTO.setExternalWaybillNum(waybillDO.getExternalWaybillNum());
            ecWaybillOpenBillDTO.setMeteringQuantity(waybillDO.getQuantity());
            ItemResult<UnitConversionDTO> conversionResult = orderService.unitConversion(waybillDO.getProductId()
                    , waybillDO.getQuantity());
            if (!conversionResult.isSuccess()) {
                throw new BizException(LogisticsErrorCode.EXTERNAL_EXCEPTION, "单位转换异常:" + conversionResult.getDescription());
            }
            ecWaybillOpenBillDTO.setSalesQuantity(conversionResult.getData().getAmount());
            ecWaybillOpenBillDTO.setBillDate(date);
            ecWaybillOpenBillDTO.setBillNote("中心仓出货开单");
            ecWaybillOpenBillDTO.setTareWeight(BigDecimal.ZERO);
            ecWaybillOpenBillDTO.setTaredTime(date);
            ecWaybillOpenBillDTO.setGrossWeight(waybillDO.getActualQuantity());
            ecWaybillOpenBillDTO.setGrossedTime(date);
            ecWaybillOpenBillDTO.setNetWeight(waybillDO.getActualQuantity());
            ecWaybillOpenBillDTO.setShippedQuantity(waybillDO.getActualQuantity());
            ecWaybillOpenBillDTO.setVehicleNum(waybillDO.getVehicleNum());
            //开单
            ApiResult openBillResult = openAPIInvokeService.invoke(BizCodeEnum.EC_QOG_K0.code(),
                    waybillDO.getSellerId(), JSON.toJSONString(ecWaybillOpenBillDTO));
            log.info("openBill:" + openBillResult.getData());
            if (!openBillResult.isSuccess()) {
                throw new BizException(LogisticsErrorCode.EXTERNAL_EXCEPTION, "调用ERP开单接口失败：" + openBillResult.getDescription());
            }
            //发货
            Waybill waybill = new Waybill();
            waybill.setWaybillId(waybillDO.getWaybillId());
            //锁定运单
            if (!CsStringUtils.equals(waybillDO.getWaybillId(), waybillDO.getParentId())) {
                waybill.setExternalWaybillStatus(WaybillExternalStatusEnum.ERP_SHIPPING.getCode());
                waybill.setUpdateTime(new Date());
            }
            ECWaybillSendOutDTO ecWaybillSendOutDTO = new ECWaybillSendOutDTO();
            ecWaybillSendOutDTO.setWaybillId(waybillDO.getWaybillId());
            ecWaybillSendOutDTO.setWaybillNum(waybillDO.getWaybillNum());
            ecWaybillSendOutDTO.setExternalWaybillNum(waybillDO.getExternalWaybillNum());
            ecWaybillSendOutDTO.setTareWeight(BigDecimal.ZERO);
            ecWaybillSendOutDTO.setTaredTime(date);
            ecWaybillSendOutDTO.setGrossWeight(waybillDO.getActualQuantity());
            ecWaybillSendOutDTO.setGrossedTime(date);
            ecWaybillSendOutDTO.setNetWeight(waybillDO.getActualQuantity());
            ecWaybillSendOutDTO.setShippedQuantity(waybillDO.getActualQuantity());
            ecWaybillSendOutDTO.setShippedDate(date);
            ecWaybillSendOutDTO.setCertNumber(carriageRouteBizUidGenerator.erpCertNumber());
            ecWaybillSendOutDTO.setCarBoxStartDate(date);
            ecWaybillSendOutDTO.setCarBoxEndDate(date);
            log.info("ecWaybillSendOutDTO:" + JSON.toJSONString(ecWaybillSendOutDTO));
            ApiResult sendOutResult = openAPIInvokeService.invoke(BizCodeEnum.EC_QOG_C0.code(),
                    waybillDO.getSellerId(), JSON.toJSONString(ecWaybillSendOutDTO));
            waybill.setSyncFailReason(sendOutResult.isSuccess() ? "" : sendOutResult.getDescription());
            waybillMapper.updateByPrimaryKeySelective(waybill);
            if (!sendOutResult.isSuccess()) {
                throw new BizException(LogisticsErrorCode.EXTERNAL_EXCEPTION, "调用ERP发货接口失败：" + sendOutResult.getDescription());
            }
        } catch (Exception e) {
            log.error("接口内部异常:" + e);
            String description = "接口内部异常";
            CodeMeta errorCode = BasicCode.UNKNOWN_ERROR;
            if (e instanceof BizException) {
                BizException be = (BizException)e;
                errorCode = be.getErrorCode();
                description = be.getMessage();
            }
            ItemResult itemResult = new ItemResult<>(errorCode.getCode(), description);
            Waybill waybill = new Waybill();
            waybill.setWaybillId(waybillDO.getWaybillId());
            waybill.setWaybillNum(waybillDO.getWaybillNum());
            externalExceptionHandle(itemResult, waybill, ExternalMethodTypeEnum.ERP_WAYBILL_SEND.getCode());
            throw new BizException(errorCode, description);
        }

        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> externalSendOutCallBack(ItemResult<ERPWaybillDTO> itemResult) {
        log.info("externalSendOutCallBack:" + JSON.toJSONString(itemResult));
        Waybill waybill = waybillQueryBizService.queryWaybillByWaybillNum(itemResult.getData().getWaybillNum());
        //修改当前运单外部执行状态
        if (itemResult.isSuccess()) {
            if (!WaybillExternalStatusEnum.ERP_SHIPPING.getCode().equals(waybill.getExternalWaybillStatus()) &&
                    !WaybillExternalStatusEnum.LEAVE_WAREHOUSE.getCode().equals(waybill.getExternalWaybillStatus())) {
                throw new BizException(BasicCode.INVALID_PARAM, WAYBILL_STATUS_EXCEPTION);
            }
            waybill.setExternalWaybillStatus(WaybillExternalStatusEnum.LEAVE_WAREHOUSE.getCode());
            waybill.setUpdateTime(new Date());
            //整合运单
            Boolean mainWaybillFlag = Boolean.TRUE;
            changeWayBillStatusLeaveWarehouse(waybill, mainWaybillFlag);
            //通知交易发货数量
            try {
            } catch (Exception e) {
                log.error("通知交易发货失败：" + e);
            }
        } else {
            waybill.setSyncFailReason(itemResult.getDescription());
            waybill.setUpdateTime(new Date());
        }
        waybillMapper.updateByPrimaryKeySelective(waybill);

        externalExceptionHandle(itemResult, waybill, ExternalMethodTypeEnum.ERP_WAYBILL_SEND.getCode());

        return new ItemResult<>(null);
    }

    private void changeWayBillStatusLeaveWarehouse(Waybill waybill, Boolean mainWaybillFlag) {
        if (CsStringUtils.equals(waybill.getWaybillId(), waybill.getParentId())) {
            return;
        }
        List<SubWaybillDO> subWaybillList = waybillQueryBizService
                .selectSubWaybillListByParentId(Lists.newArrayList(waybill.getParentId()));
        if (CollectionUtils.isNotEmpty(subWaybillList)) {
            for (SubWaybillDO subWaybillDO : subWaybillList) {
                if (!subWaybillDO.getWaybillId().equals(waybill.getWaybillId())) {
                    if (!WaybillStatusEnum.DELIVERING.getCode().equals(subWaybillDO.getStatus()) &&
                            !WaybillExternalStatusEnum.LEAVE_WAREHOUSE.getCode().equals(subWaybillDO.getExternalWaybillNum())) {
                        mainWaybillFlag = Boolean.FALSE;
                        break;
                    }
                }
            }
            if (mainWaybillFlag) {
                Waybill mainWaybill = new Waybill();
                mainWaybill.setWaybillId(waybill.getParentId());
                mainWaybill.setExternalWaybillStatus(WaybillExternalStatusEnum.LEAVE_WAREHOUSE.getCode());
                mainWaybill.setUpdateTime(new Date());
                waybillMapper.updateByPrimaryKeySelective(mainWaybill);
            }
        }
    }

    @Override
    public ItemResult<ECWaybillDTO> queryExternalWaybill(String waybillId) {
        WaybillDetailDTO waybillDetailDTO = waybillQueryBizService.selectWaybillDetailByWaybillId(waybillId);
        if (waybillDetailDTO == null) {
            throw new BizException(BasicCode.DATA_NOT_EXIST, "运单信息");
        }
        ECWaybillDTO ecWaybillDTO = new ECWaybillDTO();
        ecWaybillDTO.setWaybillNum(waybillDetailDTO.getWaybillNum());
        ApiResult apiResult = openAPIInvokeService.invoke(BizCodeEnum.EC_LOG_T1.code(),
                waybillDetailDTO.getProductDetails().get(0).getSellerId(), JSON.toJSONString(ecWaybillDTO));
        log.info("queryExternalWaybill:" + apiResult.getData());
        ECWaybillDTO waybillDTO = apiResult.getDTO(ECWaybillDTO.class);

        return new ItemResult<>(waybillDTO);
    }

    /**
     * 处理异常记录
     * @param itemResult 调用结果对象
     * @param waybill 运单对象
     * @param methodType 方法类型
     */
    public void externalExceptionHandle(ItemResult itemResult, Waybill waybill, String methodType) {
        ExternalExceptionHandle externalExceptionHandle = externalExceptionBizService
                .queryExternalExceptionHandleByCondition(waybill.getWaybillId(), methodType);
        if (externalExceptionHandle == null) {
            if (!itemResult.isSuccess()) {
                ExternalExceptionSaveDTO externalExceptionSaveDTO = new ExternalExceptionSaveDTO();
                externalExceptionSaveDTO.setWaybillId(waybill.getWaybillId());
                externalExceptionSaveDTO.setWaybillNum(waybill.getWaybillNum());
                externalExceptionSaveDTO.setMethodType(methodType);
                externalExceptionSaveDTO.setExceptionReason(itemResult.getDescription());
                externalExceptionSaveDTO.setOperatorUserId(ERP_OPERATE_USER_ID);
                externalExceptionSaveDTO.setOperatorUserName(ERP_OPERATE_USER_NAME);
                externalExceptionHandle = externalExceptionBizService.addExternalExceptionHandle(externalExceptionSaveDTO);
            }
        } else {
            if (itemResult.isSuccess()) {
                externalExceptionHandle.setStatus(ExternalExceptionStatusEnum.HANDLE_SUCCESS.getCode());
                externalExceptionHandle.setExceptionReason("");
            } else {
                externalExceptionHandle.setStatus(ExternalExceptionStatusEnum.HANDLE_FAIL.getCode());
                externalExceptionHandle.setExceptionReason(itemResult.getDescription());
            }
            if (externalExceptionHandle.getRetryCount() == null) {
                externalExceptionHandle.setRetryCount(0);
            }
            externalExceptionHandle.setRetryCount(externalExceptionHandle.getRetryCount() + 1);
            externalExceptionBizService.editExternalExceptionHandle(externalExceptionHandle);
        }
        if (externalExceptionHandle == null) {
            return;
        }
        //重试策略
        List<String> operationTypeList = Lists.newArrayList(
                ExternalMethodTypeEnum.ERP_WAYBILL_CREATE.getCode(),
                ExternalMethodTypeEnum.ERP_WAYBILL_CHANGE.getCode(),
                ExternalMethodTypeEnum.ERP_WAYBILL_CLOSE.getCode()
        );
        if (externalExceptionHandle.getRetryCount() < retryCount && operationTypeList.contains(methodType)
                && !CsStringUtils.equals(externalExceptionHandle.getStatus(), OrderExceptionStatusEnum.HANDLE_SUCCESS.getCode())
                && !CsStringUtils.equals(itemResult.getCode(), LogisticsErrorCode.ERP_CALLBACK_ERROR.getCode())) {
            //重试等待分钟数为以2为底数的重试次数幂
            long waitMinutes = (long)Math.pow(2, externalExceptionHandle.getRetryCount());
            TaskMessage taskMessage = new TaskMessage(1000L * 60 * waitMinutes, externalExceptionHandle.getExceptionHandleId(), LogisticsCommonBean.ERP_EXCEPTION_QUEUE);
            retryErpTaskManager.addToQueue(taskMessage);

        }
    }
}
