package com.ecommerce.logistics.service.impl;

import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.gps.GpsManCondDTO;
import com.ecommerce.logistics.api.dto.gps.GpsManufacturerAddDTO;
import com.ecommerce.logistics.api.dto.gps.GpsManufacturerDTO;
import com.ecommerce.logistics.api.dto.gps.GpsManufacturerDeleteDTO;
import com.ecommerce.logistics.api.dto.gps.GpsManufacturerOptionDTO;
import com.ecommerce.logistics.api.dto.gps.GpsManufacturerUpdateDTO;
import com.ecommerce.logistics.biz.IGpsManufacturerBizService;
import com.ecommerce.logistics.service.IGpsManufacturerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Auther: <EMAIL>
 * @Date: 12/09/2018 11:41
 * @Description:
 */
@Slf4j
@Service
public class GpsManufacturerService implements IGpsManufacturerService {

    @Autowired
    private IGpsManufacturerBizService gpsManufacturerBizService;

    @Override
    public ItemResult<String> addGpsManufacturer(GpsManufacturerAddDTO gpsManufacturerAddDTO) {
        log.info("添加GPS厂商:{}", gpsManufacturerAddDTO);
        try {
            String gpsManufacturerId = gpsManufacturerBizService.addGpsManufacturer(gpsManufacturerAddDTO);
            log.info("GPS厂商的主键为:{}", gpsManufacturerId);
            return new ItemResult<>(gpsManufacturerId);
        } catch (Exception e) {
            log.error("添加GPS厂商发生异常:{}", gpsManufacturerAddDTO, e);
            ItemResult<String> result = new ItemResult<>();
            result.setSuccess(false);
            result.setCode(BasicCode.DB_INSERT_FAILED.getCode());
            result.setDescription(e.getMessage());
            return result;
        }
    }

    @Override
    public ItemResult<Void> removeGpsManufacturerById(GpsManufacturerDeleteDTO gpsManufacturerDeleteDTO) {
        log.info("删除GPS厂商:{}", gpsManufacturerDeleteDTO);
        try {
            gpsManufacturerBizService.removeGpsManufacturerById(gpsManufacturerDeleteDTO);
            return new ItemResult<>(null);
        } catch (Exception e) {
            log.error("删除GPS厂商发生异常:{}", gpsManufacturerDeleteDTO, e);
            ItemResult<Void> result = new ItemResult<>();
            result.setSuccess(false);
            result.setCode(BasicCode.DB_DELETE_FAILED.getCode());
            result.setDescription(e.getMessage());
            return result;
        }
    }

    @Override
    public ItemResult<List<GpsManufacturerOptionDTO>> queryGpsOptions() {
        return new ItemResult<>(gpsManufacturerBizService.getOptions());
    }

    @Override
    public ItemResult<List<GpsManufacturerDTO>> queryAllGpsMan() {
        try {
            List<GpsManufacturerDTO> dbList = gpsManufacturerBizService.queryAllGpsMan();
            log.info("查询全部GPS厂商:{}", dbList);
            return new ItemResult<>(dbList);
        } catch (Exception e) {
            log.error("查询全部GPS厂商发生异常:{}", e);
            ItemResult<List<GpsManufacturerDTO>> result = new ItemResult<>();
            result.setSuccess(false);
            result.setCode(BasicCode.UNKNOWN_ERROR.getCode());
            result.setDescription(e.getMessage());
            return result;
        }
    }

    @Override
    public ItemResult<PageData<GpsManufacturerDTO>> queryGpsManByCond(PageQuery<GpsManCondDTO> pageQuery) {
        try {
            PageData<GpsManufacturerDTO> dbDto = gpsManufacturerBizService.queryGpsManByCond(pageQuery);
            log.info("条件查询GPS厂商:{}===>{}", pageQuery, dbDto);
            return new ItemResult<>(dbDto);
        } catch (Exception e) {
            log.error("条件查询GPS厂商发生异常:{}", pageQuery, e);
            ItemResult<PageData<GpsManufacturerDTO>> result = new ItemResult<>();
            result.setSuccess(false);
            result.setCode(BasicCode.UNKNOWN_ERROR.getCode());
            result.setDescription(e.getMessage());
            return result;
        }
    }

    @Override
    public ItemResult<GpsManufacturerDTO> queryGpsManufacturerById(String gpsManufacturerId) {
        try {
            GpsManufacturerDTO dbDto = gpsManufacturerBizService.queryGpsManufacturerById(gpsManufacturerId);
            log.info("主键查询GPS厂商:{}===>{}", gpsManufacturerId, dbDto);
            return new ItemResult<>(dbDto);
        } catch (Exception e) {
            log.error("主键查询GPS厂商发生异常:{}", gpsManufacturerId, e);
            ItemResult<GpsManufacturerDTO> result = new ItemResult<>();
            result.setSuccess(false);
            result.setCode(BasicCode.UNKNOWN_ERROR.getCode());
            result.setDescription(e.getMessage());
            return result;
        }
    }

    @Override
    public ItemResult<GpsManufacturerDTO> queryGpsManufacturerByNumber(String number) {
        try {
            GpsManufacturerDTO dbDto = gpsManufacturerBizService.queryGpsManufacturerByNumber(number);
            log.info("编号查询GPS厂商:{}===>{}", number, dbDto);
            return new ItemResult<>(dbDto);
        } catch (Exception e) {
            log.error("编号查询GPS厂商发生异常:{}", number, e);
            ItemResult<GpsManufacturerDTO> result = new ItemResult<>();
            result.setSuccess(false);
            result.setCode(BasicCode.UNKNOWN_ERROR.getCode());
            result.setDescription(e.getMessage());
            return result;
        }
    }

    @Override
    public ItemResult<Void> modifyGpsManById(GpsManufacturerUpdateDTO gpsManufacturerUpdateDTO) {
        log.info("更新GPS厂商:{}", gpsManufacturerUpdateDTO);
        try {
            gpsManufacturerBizService.modifyGpsManById(gpsManufacturerUpdateDTO);
            return new ItemResult<>(null);
        } catch (Exception e) {
            log.error("更新GPS厂商异常:{}", gpsManufacturerUpdateDTO, e);
            ItemResult<Void> result = new ItemResult<>();
            result.setSuccess(false);
            result.setCode(BasicCode.UNKNOWN_ERROR.getCode());
            result.setDescription(e.getMessage());
            return result;
        }
    }
}
