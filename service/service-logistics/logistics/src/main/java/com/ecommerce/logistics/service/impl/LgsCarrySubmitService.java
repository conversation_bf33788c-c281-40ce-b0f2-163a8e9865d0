package com.ecommerce.logistics.service.impl;

import com.alibaba.fastjson.JSON;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.service.common.bsid.CommonBusinessIdGenerator;
import com.ecommerce.logistics.api.dto.vehicle.VehicleDTO;
import com.ecommerce.logistics.api.dto.waybill.CarryWaybillSubmitDTO;
import com.ecommerce.logistics.api.dto.waybill.ContractSubmitInfoDTO;
import com.ecommerce.logistics.service.IDeliveryBillService;
import com.ecommerce.logistics.service.ILgsCarrySubmitService;
import com.ecommerce.logistics.service.IShipBillService;
import com.ecommerce.logistics.service.IVehicleService;
import com.ecommerce.logistics.util.LogisticsUtils;
import com.ecommerce.member.api.dto.member.CertQueryDTO;
import com.ecommerce.member.api.dto.member.MemberCertDTO;
import com.ecommerce.member.api.dto.member.MemberDetailDTO;
import com.ecommerce.member.api.dto.member.enums.AdvertStatusEnum;
import com.ecommerce.member.api.dto.member.enums.MemberCertTypeEnum;
import com.ecommerce.member.api.dto.member.enums.MemberTypeEnum;
import com.ecommerce.member.api.service.IMemberService;
import com.ecommerce.open.api.dto.carry.carryContract.CarryContractDTO;
import com.ecommerce.open.api.dto.carry.carryFinanceFlow.CarryFinanceFlowDTO;
import com.ecommerce.open.api.dto.carry.carryFinanceFlow.FinanceItemDTO;
import com.ecommerce.open.api.dto.carry.carryFinanceFlow.FinanceShippingNoteDTO;
import com.ecommerce.open.api.dto.carry.carryShipNote.CarryWaybillDTO;
import com.ecommerce.open.api.dto.carry.carryShipNote.CarryWaybillListDTO;
import com.ecommerce.open.api.dto.carry.carryShipNote.ShipDriverItemDTO;
import com.ecommerce.open.api.dto.carry.carryShipNote.ShipGoodsItemDTO;
import com.ecommerce.open.api.service.ICarrySubmitService;
import com.ecommerce.pay.api.v2.dto.driver.DriverWaybillPayDTO;
import com.ecommerce.pay.api.v2.service.IDriverLogisticsCostService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: LgsCarrySubmitService
 * @Author: <EMAIL>
 * @Date: 2021-05-25 17:42
 */
@Slf4j
@Service("lgsCarrySubmitService")
public class LgsCarrySubmitService implements ILgsCarrySubmitService {

    private static final String NOT_FOUND_CONSIGNEE = "未找到运单对应收货人";
    @Autowired
    private ICarrySubmitService carrySubmitService;

    @Autowired
    private IDeliveryBillService deliveryBillService;

    @Autowired
    private IShipBillService shipBillService;

    @Autowired
    private IMemberService memberService;

    @Autowired
    private IVehicleService vehicleService;

    @Autowired
    private IDriverLogisticsCostService driverLogisticsCostService;

    @Autowired
    private CommonBusinessIdGenerator commonBusinessIdGenerator;

    //网络货运经营者名称
    @Value("${waybill.carrySubmit.carrier:广东省爱泥电子商务有限公司}")
    private String carrier;

    //统一社会信用代码
    @Value("${waybill.carrySubmit.unifiedSocialCreditIdentifier:91440300MA5FE3CHXQ}")
    private String unifiedSocialCreditIdentifier;

    //道路运输经营许可证编号
    @Value("${waybill.carrySubmit.permitNumber:123}")
    private String permitNumber;

    //物流交换代码
    @Value("${waybill.carrySubmit.senderCode:36274}")
    private String senderCode;

    @Override
    public ItemResult<String> generateCarryShipNote(String waybillNum, String operatorId) {
        List<CarryWaybillSubmitDTO> carryWaybillSubmitDTOList = shipBillService.selectCarryWaybill(waybillNum).getData();
        if(CollectionUtils.isEmpty(carryWaybillSubmitDTOList)){
            throw new BizException(BasicCode.UNDEFINED_ERROR, "未找到运单号" + waybillNum + "对应运单");
        }
        log.info("carryWaybillSubmitDTO:{}", JSON.toJSONString(carryWaybillSubmitDTOList));
        carryWaybillSubmitDTOList.forEach(carryWaybillSubmitDTO -> {
            CarryWaybillDTO carryWaybillDTO = new CarryWaybillDTO();
            BeanUtils.copyProperties(carryWaybillSubmitDTO,carryWaybillDTO);
            carryWaybillDTO.setTravelDistance(carryWaybillSubmitDTO.getTransportDistance());

            //设置发货人信息
            MemberDetailDTO sellerDetail = verifySeller(carryWaybillSubmitDTO);
            carryWaybillDTO.setConsignor(sellerDetail.getMemberName());
            carryWaybillDTO.setConsignorId(CsStringUtils.isNotBlank(sellerDetail.getBusinessLicenseCode()) ? sellerDetail.getBusinessLicenseCode() : getIdNumber(carryWaybillSubmitDTO.getSellerId()).getIdNumber());

            //设置收货人信息
            MemberDetailDTO buyerDetail = verifyBuyer(carryWaybillSubmitDTO);
            carryWaybillDTO.setConsignee(buyerDetail.getMemberName());
            carryWaybillDTO.setConsigneeId(CsStringUtils.isNotBlank(buyerDetail.getBusinessLicenseCode()) ? buyerDetail.getBusinessLicenseCode() : getIdNumber(carryWaybillSubmitDTO.getBuyerId()).getIdNumber());

            //设置驾驶员信息
            if (CsStringUtils.isBlank(carryWaybillSubmitDTO.getEcDriverId())) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "未找到运单对应驾驶员");
            }
            ShipDriverItemDTO shipDriverItemDTO = new ShipDriverItemDTO();
            shipDriverItemDTO.setNameOfPerson(getIdentityLicense(carryWaybillSubmitDTO.getEcDriverId()).getRealName());
            shipDriverItemDTO.setDrivingLicense(getIdentityLicense(carryWaybillSubmitDTO.getEcDriverId()).getIdNumber());
            carryWaybillDTO.setDriverItemDTOList(Lists.newArrayList(shipDriverItemDTO));

            //设置货物信息
            ShipGoodsItemDTO shipGoodsItemDTO = new ShipGoodsItemDTO();
            shipGoodsItemDTO.setDescriptionOfGoods(carryWaybillSubmitDTO.getDescriptionOfGoods());
            shipGoodsItemDTO.setCargoTypeClassificationCode(carryWaybillSubmitDTO.getCargoTypeClassificationCode());
            shipGoodsItemDTO.setGoodsItemGrossWeight(carryWaybillSubmitDTO.getGoodsItemGrossWeight());
            carryWaybillDTO.setGoodsItemDTOList(Lists.newArrayList(shipGoodsItemDTO));

            //设置承运人信息
            MemberDetailDTO carrierDetail = verifyCarrier(carryWaybillSubmitDTO);
            MemberCertDTO roadTransportLicenseCertDTO = getRoadTransportLicense(carryWaybillSubmitDTO.getVehicleUserId());

            MemberCertDTO idCardCertDTO = getMemberCertDTO(carrierDetail);
            carryWaybillDTO.setActualCarrier(CsStringUtils.equals(carrierDetail.getMemberType(), MemberTypeEnum.PERSON_DRIVER.getCode()) ?
                    shipDriverItemDTO.getNameOfPerson() : carrierDetail.getMemberName());
            carryWaybillDTO.setActualType(CsStringUtils.equals(carrierDetail.getMemberType(), MemberTypeEnum.PERSON_DRIVER.getCode()) ? "个人" : "企业");
            carryWaybillDTO.setCarrierRegisterPlace(idCardCertDTO.getNativePlace());
            if (roadTransportLicenseCertDTO != null) {
                carryWaybillDTO.setCarrierPermitNumber(roadTransportLicenseCertDTO.getRoadLicenseNo());
                carryWaybillDTO.setCarrierBusinessRange(roadTransportLicenseCertDTO.getRoadBusinessScope());
            }
            carryWaybillDTO.setActualCarrierId(CsStringUtils.isNotBlank(carrierDetail.getBusinessLicenseCode()) ? carrierDetail.getBusinessLicenseCode() : idCardCertDTO.getIdNumber());
            carryWaybillDTO.setCarrierAduitTime(idCardCertDTO.getUpdateTime());

            VehicleDTO vehicleDTO = vehicleService.queryVehicleByNumber(carryWaybillSubmitDTO.getVehicleNumber()).getData();
            carryWaybillDTO.setVehiclePlateColorCode(getVehicleColor(vehicleDTO));
            carryWaybillDTO.setCarrier(carrier);
            carryWaybillDTO.setUnifiedSocialCreditIdentifier(unifiedSocialCreditIdentifier);
            carryWaybillDTO.setPermitNumber(permitNumber);
            carryWaybillDTO.setSenderCode(senderCode);
            carryWaybillDTO.setOperatorId(operatorId);
            carrySubmitService.saveShipNote(carryWaybillDTO);
        });
        return new ItemResult<>("Success");
    }

    private MemberCertDTO getMemberCertDTO(MemberDetailDTO carrierDetail) {
        MemberCertDTO idCardCertDTO;
        if (CsStringUtils.equals(carrierDetail.getMemberType(), MemberTypeEnum.PERSON_DRIVER.getCode())) {
            idCardCertDTO = getIdNumber(carrierDetail.getMemberId());
        }else {
            idCardCertDTO = getIdentityLicense(carrierDetail.getMainAccountId());
        }
        return idCardCertDTO;
    }

    private MemberDetailDTO verifyCarrier(CarryWaybillSubmitDTO carryWaybillSubmitDTO) {
        if (CsStringUtils.isBlank(carryWaybillSubmitDTO.getEcDriverId())) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "未找到运单对承运人");
        }
        MemberDetailDTO carrierDetail = memberService.findMemberById(carryWaybillSubmitDTO.getVehicleUserId());
        if(carrierDetail == null){
            throw new BizException(BasicCode.UNDEFINED_ERROR, NOT_FOUND_CONSIGNEE);
        }
        return carrierDetail;
    }

    private MemberDetailDTO verifyBuyer(CarryWaybillSubmitDTO carryWaybillSubmitDTO) {
        if (CsStringUtils.isBlank(carryWaybillSubmitDTO.getBuyerId())) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, NOT_FOUND_CONSIGNEE);
        }
        MemberDetailDTO buyerDetail = memberService.findMemberById(carryWaybillSubmitDTO.getBuyerId());
        if(buyerDetail == null){
            throw new BizException(BasicCode.UNDEFINED_ERROR, NOT_FOUND_CONSIGNEE);
        }
        return buyerDetail;
    }

    private MemberDetailDTO verifySeller(CarryWaybillSubmitDTO carryWaybillSubmitDTO) {
        if (CsStringUtils.isBlank(carryWaybillSubmitDTO.getSellerId())) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "未找到运单对应托运人");
        }
        MemberDetailDTO sellerDetail = memberService.findMemberById(carryWaybillSubmitDTO.getSellerId());
        if(sellerDetail == null){
            throw new BizException(BasicCode.UNDEFINED_ERROR, "未找到运单对应托运人");
        }
        return sellerDetail;
    }

    @Override
    public ItemResult<String> generateContract(Integer contractType, String number, String operatorId) {
        CarryContractDTO carryContractDTO = new CarryContractDTO();

        ContractSubmitInfoDTO contractSubmitInfoDTO;
        //托运合同 -- 查询委托单
        if (contractType == 1){
            contractSubmitInfoDTO = deliveryBillService.queryContractInfo(number).getData();
            if (contractSubmitInfoDTO == null){
                throw new BizException(BasicCode.UNDEFINED_ERROR, "未找到对应委托单信息");
            }
        } else if (contractType == 2){ //实际运输合同 -- 查询运单
            contractSubmitInfoDTO = shipBillService.queryContractInfo(number).getData();
            if (contractSubmitInfoDTO == null){
                throw new BizException(BasicCode.UNDEFINED_ERROR, "未找到对应运单信息");
            }
        }else {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "合同类型错误");
        }

        BeanUtils.copyProperties(contractSubmitInfoDTO,carryContractDTO);
        carryContractDTO.setContractPeriod(1);
        carryContractDTO.setGoodsType("其他");
        carryContractDTO.setInsuranceAmount(BigDecimal.ZERO.setScale(3,RoundingMode.DOWN));
        carryContractDTO.setBusinessType("城市配送");
        carryContractDTO.setTypeOfShipping("公路运输");
        carryContractDTO.setTransportationComb("传统运输");
        carryContractDTO.setContractNo(number);
        carryContractDTO.setContractType(contractType);
        carryContractDTO.setContractSubType(1);
        String contractTerm = new SimpleDateFormat("yyyyMM").format(contractSubmitInfoDTO.getDateSigned());
        carryContractDTO.setContractTerm(contractTerm + "-" + contractTerm);
        carryContractDTO.setSenderCode(senderCode);
        carryContractDTO.setOperatorId(operatorId);
        carryContractDTO.setCarrier(carrier);

        return carrySubmitService.saveContract(carryContractDTO);
    }

    @Override
    public ItemResult<String> generateFinanceFlow(String waybillNum, String operatorId) {
        CarryFinanceFlowDTO carryFinanceFlowDTO = new CarryFinanceFlowDTO();
        List<DriverWaybillPayDTO> transOrderNoByWaybill = driverLogisticsCostService.findTransOrderNoByWaybill(Lists.newArrayList(waybillNum));
        log.info("运单资金流水为:{}==>{}", waybillNum, transOrderNoByWaybill);
        if (CollectionUtils.isEmpty(transOrderNoByWaybill)){
            throw new BizException(BasicCode.UNDEFINED_ERROR, "未找到资金流水单信息");
        }
        DriverWaybillPayDTO driverWaybillPayDTO = transOrderNoByWaybill.get(0);
        if (!LogisticsUtils.isGtZero(driverWaybillPayDTO.getMonetaryAmount())) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "支付金额为0,无法上报");
        }
        CarryWaybillListDTO carryWaybillListDTO = carrySubmitService.selectCarryShipNoteListByQuery(waybillNum).getData();
        if(carryWaybillListDTO == null){
            throw new BizException(BasicCode.UNDEFINED_ERROR, "未找到运单号对应上报信息");
        }
        MemberDetailDTO memberDetailDTO = memberService.findMemberById(driverWaybillPayDTO.getMemberId());
        if(memberDetailDTO == null){
            throw new BizException(BasicCode.UNDEFINED_ERROR, "未找到运单对应承运人");
        }
        MemberCertDTO idCardCertDTO = getMemberCertDTO(memberDetailDTO);

        carryFinanceFlowDTO.setCarrier(carrier);
        carryFinanceFlowDTO.setActualCarrier(driverWaybillPayDTO.getRecipient());
        carryFinanceFlowDTO.setActualCarrierId(CsStringUtils.isNotBlank(memberDetailDTO.getBusinessLicenseCode()) ? memberDetailDTO.getBusinessLicenseCode() : idCardCertDTO.getIdNumber());
        carryFinanceFlowDTO.setSenderCode(senderCode);
        carryFinanceFlowDTO.setDocumentName("资金流水单");
        carryFinanceFlowDTO.setDocumentNumber(waybillNum);
        carryFinanceFlowDTO.setRecCarrier(driverWaybillPayDTO.getRecipient());
        carryFinanceFlowDTO.setVehicleNumber(carryWaybillListDTO.getVehicleNumber());
        carryFinanceFlowDTO.setVehiclePlateColorCode(Integer.parseInt(carryWaybillListDTO.getVehiclePlateColorCode()));
        carryFinanceFlowDTO.setPstatus(0);
        carryFinanceFlowDTO.setOperatorId(operatorId);

        //设置运单列表
        FinanceShippingNoteDTO financeShippingNoteDTO = new FinanceShippingNoteDTO();
        financeShippingNoteDTO.setShippingNoteNumber(carryWaybillListDTO.getShippingNoteNumber());
        financeShippingNoteDTO.setSerialNumber(carryWaybillListDTO.getSerialNumber());
        financeShippingNoteDTO.setTotalMonetaryAmount(new BigDecimal(carryWaybillListDTO.getTotalMonetaryAmount()).setScale(3, RoundingMode.HALF_UP));
        carryFinanceFlowDTO.setShippingNoteItemList(Lists.newArrayList(financeShippingNoteDTO));

        //设置财务列表
        FinanceItemDTO financeItemDTO = new FinanceItemDTO();
        financeItemDTO.setPaymentMeansCode("4");
        financeItemDTO.setRecipient(driverWaybillPayDTO.getRecipient());
        financeItemDTO.setReceiptAccount(driverWaybillPayDTO.getReceiptAccount());
        financeItemDTO.setBankCode("9999");
        financeItemDTO.setSequenceCode(driverWaybillPayDTO.getSequenceCode());
        financeItemDTO.setMonetaryAmount(driverWaybillPayDTO.getMonetaryAmount().setScale(3,RoundingMode.HALF_UP));
        financeItemDTO.setDateTime(driverWaybillPayDTO.getDateTime());
        carryFinanceFlowDTO.setFinancialItemList(Lists.newArrayList(financeItemDTO));
        log.info("无车承运人-司机结算完成时上报:{}", JSON.toJSONString(carryFinanceFlowDTO));
        return carrySubmitService.saveFinanceFlow(carryFinanceFlowDTO);
    }


    private MemberCertDTO getIdentityLicense(String accountId){
        CertQueryDTO queryDTO = new CertQueryDTO();
        queryDTO.setAccountId(accountId);
        queryDTO.setCertType(MemberCertTypeEnum.IDENTITY_LICENSE.getCode());
        List<MemberCertDTO> memberCertDTOList = memberService.selectMemberCertList(queryDTO);
        if(CollectionUtils.isEmpty(memberCertDTOList)){
            throw new BizException(BasicCode.UNDEFINED_ERROR, "未找到账号对应身份证");
        }
        List<String> statusList = Lists.newArrayList(AdvertStatusEnum.APPROVED.getCode(),AdvertStatusEnum.LOADING.getCode(),AdvertStatusEnum.OVERTIME.getCode(),AdvertStatusEnum.OVERDUE.getCode());
        List<MemberCertDTO> memberCertDTOS = memberCertDTOList.stream()
                .filter(item -> CsStringUtils.isNotBlank(item.getIdNumber()) && statusList.contains(item.getStatus()))
                .toList();
        if(CollectionUtils.isEmpty(memberCertDTOS)){
            throw new BizException(BasicCode.UNDEFINED_ERROR, "未找到账号对应身份证");
        }
        return memberCertDTOS.get(0);
    }

    private MemberCertDTO getRoadTransportLicense(String memberId){
        CertQueryDTO queryDTO = new CertQueryDTO();
        queryDTO.setMemberId(memberId);
        queryDTO.setCertType(MemberCertTypeEnum.ROAD_TRANSPORT_LICENSE.getCode());
        List<MemberCertDTO> memberCertDTOList = memberService.selectMemberCertList(queryDTO);
        if(CollectionUtils.isEmpty(memberCertDTOList)){
            throw new BizException(BasicCode.UNDEFINED_ERROR, "未找到对应会员道路运输许可证");
        }
        List<String> statusList = Lists.newArrayList(AdvertStatusEnum.APPROVED.getCode(),AdvertStatusEnum.LOADING.getCode(),AdvertStatusEnum.OVERTIME.getCode(),AdvertStatusEnum.OVERDUE.getCode());
        List<MemberCertDTO> memberCertDTOS = memberCertDTOList.stream()
                .filter(item -> CsStringUtils.isNotBlank(item.getRoadLicenseNo()) && statusList.contains(item.getStatus()))
                .toList();
        if(CollectionUtils.isEmpty(memberCertDTOS)){
            return null;
        }
        return memberCertDTOS.get(0);
    }

    private MemberCertDTO getIdNumber(String memberId){
        CertQueryDTO queryDTO = new CertQueryDTO();
        queryDTO.setMemberId(memberId);
        queryDTO.setCertType(MemberCertTypeEnum.IDENTITY_LICENSE.getCode());
        List<MemberCertDTO> memberCertDTOList = memberService.selectMemberCertList(queryDTO);
        if(CollectionUtils.isEmpty(memberCertDTOList)){
            throw new BizException(BasicCode.UNDEFINED_ERROR, "未找到对应会员个人证件号码");
        }
        List<String> statusList = Lists.newArrayList(AdvertStatusEnum.APPROVED.getCode(),AdvertStatusEnum.LOADING.getCode(),AdvertStatusEnum.OVERTIME.getCode(),AdvertStatusEnum.OVERDUE.getCode());
        List<MemberCertDTO> memberCertDTOS = memberCertDTOList.stream()
                .filter(item -> CsStringUtils.isNotBlank(item.getIdNumber()) && statusList.contains(item.getStatus()))
                .toList();
        if(CollectionUtils.isEmpty(memberCertDTOS)){
            throw new BizException(BasicCode.UNDEFINED_ERROR, "未找到对应会员个人证件号码");
        }
        return memberCertDTOS.get(0);
    }

    private String getVehicleColor(VehicleDTO vehicleDTO){
        if (vehicleDTO == null || CsStringUtils.isBlank(vehicleDTO.getColor())) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "未找到对应车牌颜色");
        }
        String Color;
        switch (vehicleDTO.getColor()){
            case "030150100":{Color = "1";break;}
            case "030150200":{Color = "4";break;}
            case "030150300":{Color = "93";break;}
            case "030150400":{Color = "2";break;}
            case "030150500":{Color = "5";break;}
            default:{Color = "1";}
        }
        return Color;
    }

}
