package com.ecommerce.logistics.service.impl;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.invoice.ConfirmInvoiceReqDTO;
import com.ecommerce.logistics.api.dto.invoice.InvoiceBillCondDTO;
import com.ecommerce.logistics.api.dto.invoice.InvoiceBillListDTO;
import com.ecommerce.logistics.api.dto.invoice.PreviewInvoiceApplyReqDTO;
import com.ecommerce.logistics.api.dto.invoice.PreviewInvoiceApplyResDTO;
import com.ecommerce.logistics.biz.ILgsElectricInvoiceBizService;
import com.ecommerce.logistics.service.ILgsElectricInvoiceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Auther: colu
 * @Date: 2020-04-20 14:26
 * @Description: 物流费电子发票服务
 */
@Slf4j
@Service("lgsElectricInvoiceService")
public class LgsElectricInvoiceService implements ILgsElectricInvoiceService {

    @Autowired
    private ILgsElectricInvoiceBizService lgsElectricInvoiceBizService;

    @Override
    public ItemResult<PageData<InvoiceBillListDTO>> queryLgsWaybillsForInvoice(PageQuery<InvoiceBillCondDTO> pageQuery) {
        return new ItemResult<>(lgsElectricInvoiceBizService.queryLgsWaybillsForInvoice(pageQuery));
    }

    @Override
    public ItemResult<PreviewInvoiceApplyResDTO> applyLgsWaybillsInvoice(PreviewInvoiceApplyReqDTO previewInvoiceApplyReqDTO) {
        PreviewInvoiceApplyResDTO previewInvoiceApplyResDTO = null;
        try {
            previewInvoiceApplyResDTO = lgsElectricInvoiceBizService.applyLgsWaybillsInvoice(previewInvoiceApplyReqDTO);
        } catch (Exception e) {
            log.info("申请电子发票预览,发生异常:{}", previewInvoiceApplyReqDTO, e);
            ItemResult<PreviewInvoiceApplyReqDTO> errorResult = new ItemResult<>();
            errorResult.setSuccess(false);
            errorResult.setDescription(e.getMessage());
        }
        return new ItemResult<>(previewInvoiceApplyResDTO);
    }

    @Override
    public ItemResult<Void> batchConfirmInvoiceApply(ConfirmInvoiceReqDTO confirmInvoiceReqDTO) {
        try {
            lgsElectricInvoiceBizService.batchConfirmInvoiceApply(confirmInvoiceReqDTO);
        } catch (Exception e) {
            log.info("开票确认发生异常:{}", confirmInvoiceReqDTO, e);
        }
        return new ItemResult<>(null);
    }
}
