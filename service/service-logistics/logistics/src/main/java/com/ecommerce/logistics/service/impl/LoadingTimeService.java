package com.ecommerce.logistics.service.impl;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.intelligentDispatch.LoadingTimeDTO;
import com.ecommerce.logistics.biz.ILoadingTimeBizService;
import com.ecommerce.logistics.service.ILoadingTimeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class LoadingTimeService implements ILoadingTimeService {

    @Autowired
    private ILoadingTimeBizService loadingTimeBizService;

    @Override
    public ItemResult<Boolean> add(LoadingTimeDTO createDTO) {
        return new ItemResult<>(loadingTimeBizService.add(createDTO));
    }

    @Override
    public ItemResult<Boolean> edit(LoadingTimeDTO updateDTO) {
        return new ItemResult<>(loadingTimeBizService.edit(updateDTO));
    }

    @Override
    public ItemResult<Boolean> delete(LoadingTimeDTO deleteDTO) {
        return new ItemResult<>(loadingTimeBizService.delete(deleteDTO));
    }

    @Override
    public ItemResult<PageData<LoadingTimeDTO>> pageLoadingTime(PageQuery<LoadingTimeDTO> pageQuery) {
        return new ItemResult<>(loadingTimeBizService.pageLoadingTime(pageQuery));
    }
}
