package com.ecommerce.logistics.service.impl;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.logistics.api.dto.operationrecord.OperationRecordAddDTO;
import com.ecommerce.logistics.api.dto.operationrecord.OperationRecordDTO;
import com.ecommerce.logistics.api.dto.operationrecord.OperationRecordListDTO;
import com.ecommerce.logistics.api.dto.operationrecord.QueryWaybillOperationDTO;
import com.ecommerce.logistics.biz.IOperationRecordBizService;
import com.ecommerce.logistics.service.IOperationRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Description:
 * @Date: Create in 上午11:06 18/8/15
 */
@Service
public class OperationRecordService implements IOperationRecordService{
    @Autowired
    IOperationRecordBizService operationRecordBizService;

    @Override
    public ItemResult<List<OperationRecordListDTO>> queryOperationRecordList(String entryId) {
        return new ItemResult<>(operationRecordBizService.getOperationRecordList(entryId));
    }

    @Override
    public ItemResult<Void> addOperationRecord(OperationRecordAddDTO operationRecordAddDTO) {
        operationRecordBizService.saveOperationRecord(operationRecordAddDTO);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<List<OperationRecordDTO>> queryWaybillOperation(QueryWaybillOperationDTO queryWaybillOperationDTO) {
        return new ItemResult<>(operationRecordBizService.queryWaybillOperation(queryWaybillOperationDTO));
    }
}
