package com.ecommerce.logistics.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.ecommerce.base.api.dto.warehouse.WarehouseDetailsDTO;
import com.ecommerce.base.api.service.IWarehouseService;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.exception.DistributeLockException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.common.service.ILockService;
import com.ecommerce.common.service.common.bsid.CommonBusinessIdGenerator;
import com.ecommerce.common.service.common.uuid.UUIDGenerator;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.creditStatistic.CreditStatisticAddDTO;
import com.ecommerce.logistics.api.dto.driver.AssignDriverLogAddDTO;
import com.ecommerce.logistics.api.dto.pickingbill.AssignDetailDTO;
import com.ecommerce.logistics.api.dto.pickingbill.AssignDispatchBillDTO;
import com.ecommerce.logistics.api.dto.pickingbill.AssignWaybillDTO;
import com.ecommerce.logistics.api.dto.pickingbill.AssignWaybillDetailDTO;
import com.ecommerce.logistics.api.dto.pickingbill.CancelDeliverySheetDTO;
import com.ecommerce.logistics.api.dto.pickingbill.CancelPickingBillDTO;
import com.ecommerce.logistics.api.dto.pickingbill.CancelQuantityDTO;
import com.ecommerce.logistics.api.dto.pickingbill.ChangePickingBillWarehouseDTO;
import com.ecommerce.logistics.api.dto.pickingbill.CloseDeliverySheetDTO;
import com.ecommerce.logistics.api.dto.pickingbill.ClosePickingBillDTO;
import com.ecommerce.logistics.api.dto.pickingbill.EnteringPickingBillDTO;
import com.ecommerce.logistics.api.dto.pickingbill.ExpirePickingBillDTO;
import com.ecommerce.logistics.api.dto.pickingbill.OptionDTO;
import com.ecommerce.logistics.api.dto.pickingbill.PickingBillDTO;
import com.ecommerce.logistics.api.dto.pickingbill.PickingBillExportDTO;
import com.ecommerce.logistics.api.dto.pickingbill.PickingBillListDTO;
import com.ecommerce.logistics.api.dto.pickingbill.PickingBillListQueryDTO;
import com.ecommerce.logistics.api.dto.pickingbill.StatisticsPickingBillStatusDTO;
import com.ecommerce.logistics.api.dto.pickingbill.VendorEntrustmentDTO;
import com.ecommerce.logistics.api.dto.proxybill.SyncTakeInfoSecondaryWaybillDTO;
import com.ecommerce.logistics.api.dto.vehicle.SelfPickingVehicleDTO;
import com.ecommerce.logistics.api.dto.waybill.ExternalWaybillRequestDTO;
import com.ecommerce.logistics.api.dto.waybill.TradeWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillInfoDTO;
import com.ecommerce.logistics.api.enums.BillProxyTypeEnum;
import com.ecommerce.logistics.api.enums.ExternalSyncFlagEnum;
import com.ecommerce.logistics.api.enums.PickingBillTypeEnum;
import com.ecommerce.logistics.api.enums.UserRoleEnum;
import com.ecommerce.logistics.api.enums.WarehouseTypeEnum;
import com.ecommerce.logistics.biz.IAssignDriverLogBizService;
import com.ecommerce.logistics.biz.IBillSyncBizService;
import com.ecommerce.logistics.biz.ICreditStatisticBizService;
import com.ecommerce.logistics.biz.IMonitorJudgeBizService;
import com.ecommerce.logistics.biz.IPickingBillBizService;
import com.ecommerce.logistics.biz.IVehicleBizService;
import com.ecommerce.logistics.biz.IWaybillQueryBizService;
import com.ecommerce.logistics.biz.expire.IExpirePickingBillBizService;
import com.ecommerce.logistics.dao.vo.AssignDriverLog;
import com.ecommerce.logistics.dao.vo.CreditStatistic;
import com.ecommerce.logistics.dao.vo.Vehicle;
import com.ecommerce.logistics.exception.LogisticsErrorCode;
import com.ecommerce.logistics.service.IPickingBillService;
import com.ecommerce.logistics.service.IRuleComputeService;
import com.ecommerce.logistics.service.IWaybillExternalService;
import com.ecommerce.logistics.util.DigestUtils;
import com.ecommerce.member.api.dto.member.config.MemberConfigEnum;
import com.ecommerce.member.api.dto.referrer.ReferrerInfoDTO;
import com.ecommerce.member.api.dto.referrer.ReferrerInfoQueryDTO;
import com.ecommerce.member.api.service.IBuyerAndReferrerService;
import com.ecommerce.member.api.service.IMemberConfigService;
import com.ecommerce.order.api.enums.AdjustAddWayEnum;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Description: 提货单服务
 * @Author: <EMAIL>
 * @Date: 2018-08-08 16:33
 */
@Deprecated(since = "2.1.4-RELEASE")
@Service
@Setter
@Slf4j
public class PickingBillService implements IPickingBillService{

    private static final Integer ZERO_INTEGER = 0;
    @Autowired
    private IPickingBillBizService pickingBillBizService;

    @Autowired
    private IVehicleBizService vehicleBizService;

    @Autowired
    private ILockService redisLockService;

    @Autowired
    private UUIDGenerator uuidGenerator;

    @Autowired
    private CommonBusinessIdGenerator commonBusinessIdGenerator;

    @Autowired
    private IBuyerAndReferrerService buyerAndReferrerService;

    @Autowired
    private IRuleComputeService ruleComputeService;

    @Autowired
    private IWarehouseService warehouseService;

    @Autowired
    private IWaybillQueryBizService waybillQueryBizService;

    @Autowired
    private IWaybillExternalService waybillExternalService;

    @Autowired
    private IMemberConfigService memberConfigService;

    @Autowired
    private IExpirePickingBillBizService expirePickingBillBizService;

    @Autowired
    private IBillSyncBizService billSyncBizService;

    @Autowired
    private IAssignDriverLogBizService assignDriverLogBizService;

    @Autowired
    private IMonitorJudgeBizService monitorJudgeBizService;

    @Autowired
    private ICreditStatisticBizService creditStatisticBizService;

    @Override
    public ItemResult<PageData<PickingBillListDTO>> queryPickingBillList(PageQuery<PickingBillListQueryDTO> pageQuery) {
        return new ItemResult<>(pickingBillBizService.queryPickingBillList(pageQuery));
    }

    @Override
    public ItemResult<String> enteringPickingBill(EnteringPickingBillDTO enteringPickingBillDTO) {
        log.info("enteringPickingBill:" + enteringPickingBillDTO);

        verifyParam(enteringPickingBillDTO);

        //分布式锁控制重复操作问题
        String identifier = "";
        String bizResource = DigestUtils.md5Digest(enteringPickingBillDTO.toString());
        try {
            identifier = redisLockService.lockFast(bizResource);
            //需要同步erp不支持手动录入发货单
            if (memberConfigService.hasErpFlg(enteringPickingBillDTO.getSellerId(), MemberConfigEnum.ERP_LOGISTICS_FLG.getKeyCode()) &&
                    CsStringUtils.isEmpty(enteringPickingBillDTO.getDeliverySheetNum())) {
                throw new BizException(LogisticsErrorCode.BIZ_FORBIDDING, "卖家已接入ERP系统不支持手动录入发货单");
            }
            //门店配送发货单
            Set<String> storeTypeSet = new HashSet<>();
            storeTypeSet.add(PickingBillTypeEnum.PLATFORM_DELIVERY.getCode());
            storeTypeSet.add(PickingBillTypeEnum.SELLER_DELIVERY.getCode());
            if (storeTypeSet.contains(enteringPickingBillDTO.getType()) &&
                    CsStringUtils.isNotEmpty(enteringPickingBillDTO.getRecommendId())) {
                ReferrerInfoDTO referrerInfoDTO = buyerAndReferrerService.findById(enteringPickingBillDTO.getRecommendId());
                if (referrerInfoDTO.getReferrerType().equals(1)) {
                    enteringPickingBillDTO.setRecommendUserId(referrerInfoDTO.getReferrerAccountId());
                    enteringPickingBillDTO.setType(PickingBillTypeEnum.STORE_DELIVERY.getCode());
                }
            }
            if (CollectionUtils.isEmpty(enteringPickingBillDTO.getProductDetailList())) {
                throw new BizException(LogisticsErrorCode.DATA_VALID_FAIL, "商品列表不能为空");
            }

            //创建提货单
            String deliverSheetNum = pickingBillBizService.addPickingBill(enteringPickingBillDTO);
            //如果是指派承运商,不会生成运单,没有后续操作了
            if (enteringPickingBillDTO.getAssignBuyerCarrierFlag()) {
                return new ItemResult<>(enteringPickingBillDTO.getDeliverySheetNum());
            }
            //买家自提(同步外部系统运单)
            if (PickingBillTypeEnum.BUYER_TAKE.getCode().equals(enteringPickingBillDTO.getType()) &&
                ExternalSyncFlagEnum.FORWARD_SYNC.getCode().equals(enteringPickingBillDTO.getExternalSyncFlag())) {
                //获取当前发货单下的所有运单
                List<TradeWaybillDTO> waybillList = waybillQueryBizService
                        .queryWaybillListByDeliveryNum(enteringPickingBillDTO.getDeliverySheetNum());
                if (CollectionUtils.isEmpty(waybillList)) {
                    throw new BizException(LogisticsErrorCode.DATA_EXCEPTION, "自提运单列表");
                }
                createWayBill(enteringPickingBillDTO, waybillList);
            }

            return new ItemResult<>(deliverSheetNum);
        } catch (DistributeLockException e) {
            log.info("重复操作: enteringPickingBillDTO={}", enteringPickingBillDTO);
            throw new BizException(BasicCode.UNDEFINED_ERROR, "有其他用户正在操作，请刷新页面后重试");
        } finally {
            if (CsStringUtils.isNotEmpty(identifier)) {
                redisLockService.unlock(bizResource, identifier);
            }
        }
    }

    private static void verifyParam(EnteringPickingBillDTO enteringPickingBillDTO) {
        if (CsStringUtils.equals(enteringPickingBillDTO.getBillProxyType(), BillProxyTypeEnum.PRIMARY.getCode()) ||
                CsStringUtils.equals(enteringPickingBillDTO.getBillProxyType(), BillProxyTypeEnum.SECONDARY.getCode())) {
            if (CsStringUtils.isBlank(enteringPickingBillDTO.getType()) || CsStringUtils.isBlank(enteringPickingBillDTO.getRelationType())) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "发货单传入一二级提货类型不能为空");
            }
        }
    }

    private void createWayBill(EnteringPickingBillDTO enteringPickingBillDTO, List<TradeWaybillDTO> waybillList) {
        for (TradeWaybillDTO tradeWaybillDTO : waybillList) {
            ExternalWaybillRequestDTO externalWaybillRequestDTO = new ExternalWaybillRequestDTO();
            externalWaybillRequestDTO.setWaybillId(tradeWaybillDTO.getWaybillId());
            externalWaybillRequestDTO.setOperatorUserId(enteringPickingBillDTO.getOperationUserId());
            externalWaybillRequestDTO.setOperatorUserName(enteringPickingBillDTO.getOperationUserName());
            try {
                waybillExternalService.createExternalWaybill(externalWaybillRequestDTO);
            } catch (Exception e) {
                log.error("自提运单同步异常:" + tradeWaybillDTO);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ItemResult<String> enteringSelfTakePickingBill(EnteringPickingBillDTO enteringPickingBillDTO) {
        log.info("录入发货单,自提enteringPickingBillDTO:{}", enteringPickingBillDTO);
        verifyParam(enteringPickingBillDTO);
        //分布式锁控制重复操作问题
        String identifier = "";
        String bizResource = DigestUtils.md5Digest(enteringPickingBillDTO.toString());

        try {
            identifier = redisLockService.lockFast(bizResource);
            if (CsStringUtils.isBlank(enteringPickingBillDTO.getDeliverySheetNum())) {
                throw new BizException(LogisticsErrorCode.BIZ_FORBIDDING, "不支持手动录入发货单");
            }
            if (CollectionUtils.isEmpty(enteringPickingBillDTO.getProductDetailList())) {
                throw new BizException(LogisticsErrorCode.DATA_VALID_FAIL, "商品列表不能为空");
            }
            //批量创建发货单
            //场景1:如果是指派承运商,不会生成运单,没有后续操作了
            //场景2:指派车辆,需要生成运单并完成所有操作

            List<SyncTakeInfoSecondaryWaybillDTO> syncTakeInfoSecondaryWaybillDTOS = pickingBillBizService.addSelfTakePickingBill(enteringPickingBillDTO);

            //买家自提(同步外部系统运单)
            if (!enteringPickingBillDTO.getAssignBuyerCarrierFlag() && CsStringUtils.equals(enteringPickingBillDTO.getExternalSyncFlag(), ExternalSyncFlagEnum.FORWARD_SYNC.getCode())) {
                //获取当前发货单下的所有运单
                List<TradeWaybillDTO> waybillList = waybillQueryBizService
                        .queryWaybillListByDeliveryNum(enteringPickingBillDTO.getDeliverySheetNum());
                if (CollectionUtils.isEmpty(waybillList)) {
                    throw new BizException(LogisticsErrorCode.DATA_EXCEPTION, "自提运单列表");
                }
                createWayBill(enteringPickingBillDTO, waybillList);
            }

            if (CollectionUtils.isNotEmpty(syncTakeInfoSecondaryWaybillDTOS)) {
                for(SyncTakeInfoSecondaryWaybillDTO syncTakeInfoSecondaryWaybillDTO : syncTakeInfoSecondaryWaybillDTOS) {
                    billSyncBizService.syncPrimaryTakeInfoByWaybill(syncTakeInfoSecondaryWaybillDTO);
                }
            }

        } catch (DistributeLockException e) {
            log.info("重复操作: enteringPickingBillDTO={}", enteringPickingBillDTO);
            throw new BizException(BasicCode.UNDEFINED_ERROR, "有其他用户正在操作，请刷新页面后重试");
        } finally {
            //背靠背通知交易创建一级发货单!
            if (CsStringUtils.isNotEmpty(identifier)) {
                redisLockService.unlock(bizResource, identifier);
            }
        }
        return new ItemResult<>(enteringPickingBillDTO.getDeliverySheetNum());
    }

    @Override
    public ItemResult<Void> assignCreateDispatchBill(AssignDispatchBillDTO dispatchBillAssignDTO) {
        //分布式锁控制重复操作问题
        String identifier = "";
        String bizResource = DigestUtils.md5Digest(dispatchBillAssignDTO.toString());
        try {
            identifier = redisLockService.lockFast(bizResource);
            pickingBillBizService.assignCreateDispatchBill(dispatchBillAssignDTO);

            //如果抢单成功，则添加对应承运商的接单次数
            List<AssignDetailDTO> assignList = dispatchBillAssignDTO.getAssignList();
            if(CollectionUtils.isNotEmpty(assignList)){
                for (AssignDetailDTO assignDetailDTO : assignList){
                    if (CsStringUtils.isNotBlank(assignDetailDTO.getCarrierId())) {
                        log.info("添加对应承运商的接单次数====AssignDetailDTO{}",JSON.toJSONString(assignDetailDTO));
                        String personType = UserRoleEnum.CARRIER.getCode().toString();
                        CreditStatistic creditStatistic = creditStatisticBizService.findCreditStatisticById(assignDetailDTO.getCarrierId(),personType);
                        log.info("添加对应承运商的接单次数====CreditStatistic{}",JSON.toJSONString(creditStatistic));
                        //如果不存在记录，则新增
                        if (creditStatistic == null || CsStringUtils.isBlank(creditStatistic.getCreditStatisticId())) {
                            CreditStatisticAddDTO creditStatisticAddDTO = new CreditStatisticAddDTO();
                            creditStatisticAddDTO.setPersonId(assignDetailDTO.getCarrierId());
                            creditStatisticAddDTO.setPersonName(assignDetailDTO.getCarrierName());
                            creditStatisticAddDTO.setPersonType(personType);
                            creditStatisticAddDTO.setBillCount(1);
                            creditStatisticAddDTO.setBreakCount(0);
                            creditStatisticAddDTO.setComplaintCount(0);
                            creditStatisticAddDTO.setOperatorUserId(dispatchBillAssignDTO.getOperatorUserId());
                            creditStatisticBizService.addCreditStatistic(creditStatisticAddDTO);
                        }else {
                            creditStatisticBizService.updateCreditStatisticCount(creditStatistic.getPersonId(),true,false,false,dispatchBillAssignDTO.getOperatorUserId());
                        }
                    }
                }
            }

            return new ItemResult<>(null);
        } catch (DistributeLockException e) {
            throw new BizException(BasicCode.INVALID_PARAM, "重复操作：dispatchBillAssignDTO=" + dispatchBillAssignDTO);
        } finally {
            if (CsStringUtils.isNotEmpty(identifier)) {
                redisLockService.unlock(bizResource, identifier);
            }
        }
    }

    @Override
    public ItemResult<Void> assignCreateWaybill(AssignWaybillDTO assignWaybillDTO) {
        //分布式锁控制重复操作问题
        String identifier = "";
        String bizResource = DigestUtils.md5Digest(assignWaybillDTO.toString());
        try {
            identifier = redisLockService.lockFast(bizResource);
            //发送监控准备信息
            //提货单信息查询
            PickingBillDTO pickingBillDTO = pickingBillBizService.getPickingBillDetail(assignWaybillDTO.getPickingBillId());
            //
            assignWaybillDTO.setExternalDispatch(ObjectUtil.defaultIfNull(assignWaybillDTO.getExternalDispatch(), ZERO_INTEGER));
            //非外部调且为卖家签收时计算空载费
            if (CsStringUtils.equals(pickingBillDTO.getSignType(), AdjustAddWayEnum.SELLER_SIGIN.getCode()) &&
                    assignWaybillDTO.getExternalDispatch() != 1) {
                assignWaybillDTO.setEmptyLoadFlag(Byte.valueOf("1"));
            }

            if (CollectionUtils.isEmpty(assignWaybillDTO.getAssignWaybillDetailList()))     {
                throw new BizException(BasicCode.DATA_NOT_EXIST, "assignWaybillDetailList=null");
            }
            List<WaybillInfoDTO> waybillInfoList = new ArrayList<>();

            pickingBillBizService.assignCreateWaybill(assignWaybillDTO, waybillInfoList);
            return new ItemResult<>(null);
        } catch (DistributeLockException e) {
            throw new BizException(BasicCode.INVALID_PARAM, "重复操作：assignWaybillDTO=" + assignWaybillDTO);
        } finally {
            if (CsStringUtils.isNotEmpty(identifier)) {
                redisLockService.unlock(bizResource, identifier);
            }
        }
    }

    @Override
    public ItemResult<Void> assignVehicleCreateWaybill(AssignWaybillDTO assignWaybillDTO) {
        //分布式锁控制重复操作问题
        String identifier = "";
        String bizResource = DigestUtils.md5Digest(assignWaybillDTO.toString());
        try{
            identifier = redisLockService.lockFast(bizResource);

            List<AssignWaybillDetailDTO> assignWaybillDetailDTOS = assignWaybillDTO.getAssignWaybillDetailList();
            if (CollectionUtils.isEmpty(assignWaybillDetailDTOS))     {
                throw new BizException(BasicCode.DATA_NOT_EXIST, "assignWaybillDetailList");
            }
            //判断是否为外部调度
            assignWaybillDTO.setExternalDispatch(ObjectUtil.defaultIfNull(assignWaybillDTO.getExternalDispatch(),ZERO_INTEGER));
            //提货单信息查询
            PickingBillDTO pickingBillDTO = pickingBillBizService.getPickingBillDetail(assignWaybillDTO.getPickingBillId());


            List<WaybillInfoDTO> waybillInfoList = new ArrayList<>();
             assignWaybillDetailDTOS.forEach(assignWaybillDetailDTO -> {
                 //更新司机和车辆
                AssignDriverLogAddDTO assignDriverLogAddDTO = new AssignDriverLogAddDTO();
                BeanUtils.copyProperties(assignWaybillDetailDTO,assignDriverLogAddDTO);
                assignDriverLogAddDTO.setUserId(assignWaybillDTO.getUserId());
                assignDriverLogAddDTO.setUserName(assignWaybillDTO.getUserName());
                assignDriverLogAddDTO.setUserType(UserRoleEnum.BUYER.getCode());
                assignDriverLogAddDTO.setTransportCategoryIdList(Lists.newArrayList(assignWaybillDetailDTO.getTransportCategoryId()));
                Vehicle vehicle = assignDriverLogBizService.updateSelfAssignVehicleInfo(assignDriverLogAddDTO);
                AssignDriverLog assignDriverLog = assignDriverLogBizService.updateSelfAssignDriverInfo(assignDriverLogAddDTO);
                if (vehicle == null) {
                     throw new BizException(BasicCode.DATA_NOT_EXIST, "vehicleId=" + assignWaybillDetailDTO.getVehicleId());
                 }

                //判断是否可以监控
                 SelfPickingVehicleDTO selfPickingVehicleDTO = new SelfPickingVehicleDTO();
                 BeanUtils.copyProperties(assignWaybillDetailDTO,selfPickingVehicleDTO);
                 Boolean canMonitor = monitorJudgeBizService.canMonitor(selfPickingVehicleDTO);

                 String waybillId = uuidGenerator.gain();
                 String waybillNum = commonBusinessIdGenerator.incrementTransportCode();

                 //创建指派运单信息对象
                 WaybillInfoDTO waybillInfoDTO = new WaybillInfoDTO();
                 waybillInfoDTO.setWaybillId(waybillId);
                 waybillInfoDTO.setWaybillNum(waybillNum);
                 waybillInfoDTO.setVehicleNum(vehicle.getNumber());
                 waybillInfoDTO.setQuantity(assignWaybillDetailDTO.getQuantity());
                 waybillInfoDTO.setExternalWaybillNum(assignWaybillDetailDTO.getExternalWaybillNum());
                 waybillInfoDTO.setDriverId(assignDriverLog.getDriverId());
                 waybillInfoDTO.setDriverName(assignDriverLog.getDriverName());
                 waybillInfoDTO.setDriverPhone(assignDriverLog.getDriverPhone());
                 waybillInfoDTO.setQrCode(assignWaybillDetailDTO.getQrCode());
                 waybillInfoDTO.setCanMonitor(canMonitor ? (byte)1 : (byte)0);
                 waybillInfoDTO.setNeedMonitor(assignWaybillDTO.getSpecialFlag().byteValue());
                 waybillInfoList.add(waybillInfoDTO);
            });
            pickingBillBizService.assignCreateWaybill(assignWaybillDTO, waybillInfoList);
            return new ItemResult<>(null);
        } catch (DistributeLockException e) {
            throw new BizException(BasicCode.INVALID_PARAM, "重复操作：assignWaybillDTO=" + assignWaybillDTO);
        } finally {
            if (CsStringUtils.isNotEmpty(identifier)) {
                redisLockService.unlock(bizResource, identifier);
            }
        }
    }

    @Override
    public ItemResult<PickingBillDTO> getPickingBillDetail(String pickingBillId) {
        return new ItemResult<>(pickingBillBizService.getPickingBillDetail(pickingBillId));
    }

    @Override
    public ItemResult<CancelQuantityDTO> cancelDeliverySheet(CancelDeliverySheetDTO cancelDeliverySheetDTO) {
        return new ItemResult<>(pickingBillBizService.cancelDeliverySheet(cancelDeliverySheetDTO));
    }

    @Override
    public ItemResult<CancelQuantityDTO> cancelPickingBill(CancelPickingBillDTO cancelPickingBillDTO) {
        return new ItemResult<>(pickingBillBizService.cancelPickingBill(cancelPickingBillDTO));
    }

    @Override
    public ItemResult<List<OptionDTO>> getBuyerOptions(String name) {
        return new ItemResult<>(pickingBillBizService.getBuyerOptions(name));
    }

    @Override
    public ItemResult<List<OptionDTO>> getSellerOptions(String name) {
        return new ItemResult<>(pickingBillBizService.getSellerOptions(name));
    }

    @Override
    public ItemResult<Void> batchCloseDeliverySheet(CloseDeliverySheetDTO closePickingBillDTO) {
        pickingBillBizService.batchCloseDeliverySheet(closePickingBillDTO);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> closePickingBill(ClosePickingBillDTO closePickingBillDTO) {
        pickingBillBizService.closePickingBill(closePickingBillDTO);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> vendorEntrustmentPickingBill(VendorEntrustmentDTO vendorEntrustmentDTO) {
        pickingBillBizService.vendorEntrustmentPickingBill(vendorEntrustmentDTO);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> changePickingBillWarehouse(ChangePickingBillWarehouseDTO changePickingBillWarehouseDTO) {
        String storeCarrierId = "";
        if (CsStringUtils.isEmpty(changePickingBillWarehouseDTO.getWarehouseId())) {
            throw new BizException(BasicCode.INVALID_PARAM, "changePickingBillWarehouseDTO=" + changePickingBillWarehouseDTO);
        }
        WarehouseDetailsDTO warehouse = warehouseService.queryWarehouseDetails(changePickingBillWarehouseDTO.getWarehouseId());
        //查询门店承运商
        if (WarehouseTypeEnum.STORE.getCode().equals(warehouse.getType())) {
            ReferrerInfoQueryDTO referrerInfoQueryDTO = new ReferrerInfoQueryDTO();
            referrerInfoQueryDTO.setReferrerStoreId(changePickingBillWarehouseDTO.getWarehouseId());
            PageInfo<ReferrerInfoDTO> pageInfo = buyerAndReferrerService.findAllReferrerInfoByCondition(referrerInfoQueryDTO);
            if (pageInfo == null || CollectionUtils.isEmpty(pageInfo.getList()) ||
                    CsStringUtils.isEmpty(pageInfo.getList().get(0).getReferrerAccountId())) {
                throw new BizException(LogisticsErrorCode.DATA_EXCEPTION, "查找门店和承运商绑定关系失败");
            }
            storeCarrierId = pageInfo.getList().get(0).getReferrerAccountId();
        }
        pickingBillBizService.changePickingBillWarehouse(changePickingBillWarehouseDTO, storeCarrierId);

        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Map<String, StatisticsPickingBillStatusDTO>> statisticsPickingBillStatus(PickingBillListQueryDTO pickingBillListQueryDTO) {
        return new ItemResult<>(pickingBillBizService.statisticsPickingBillStatus(pickingBillListQueryDTO));
    }

    @Override
    public ItemResult<Void> expirePickingBill(ExpirePickingBillDTO expirePickingBillDTO) {
        log.info("发货单超时处理:" + JSON.toJSONString(expirePickingBillDTO));
        ItemResult<Void> dealResult = new ItemResult<>();
        dealResult.setSuccess(true);
        try {
            expirePickingBillBizService.expirePickingBill(expirePickingBillDTO);
        } catch (Exception e) {
            log.error("发货单超时处理失败:{}", expirePickingBillDTO, e);
            dealResult.setDescription("发货单超时处理失败");
            dealResult.setSuccess(false);
            return dealResult;
        }
        StringBuilder desc = new StringBuilder("");
        if (CollectionUtils.isNotEmpty(expirePickingBillDTO.getDeliverySheetNumList())) {
            for (String deliverySheetNum : expirePickingBillDTO.getDeliverySheetNumList()) {
                try {
                    expirePickingBillBizService.closeDeliverySheet(deliverySheetNum, "system");
                } catch (Exception e) {
                    log.error("超时单据通知交易发生异常:{}", deliverySheetNum, e);
                    desc.append(deliverySheetNum + ";");
                    dealResult.setSuccess(false);
                }
            }
        }
        dealResult.setDescription(desc.toString());

        return dealResult;
    }

    @Override
    public List<PickingBillExportDTO> exportPickingBill(PickingBillListQueryDTO queryDTO) {
        return pickingBillBizService.exportPickingBill(queryDTO);
    }
}
