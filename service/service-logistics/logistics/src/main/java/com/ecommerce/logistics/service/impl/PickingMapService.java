package com.ecommerce.logistics.service.impl;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.logistics.biz.IPickingMapBizService;
import com.ecommerce.logistics.dao.dto.pickingbill.PickingMapDTO;
import com.ecommerce.logistics.service.IPickingMapService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * @Auther: <EMAIL>
 * @Date: 12/08/2020 17:44
 * @Description:
 */
@Slf4j
@Service
public class PickingMapService implements IPickingMapService {

    @Autowired
    private IPickingMapBizService pickingMapBizService;


    @Override
    public ItemResult<String> addPickingMap(PickingMapDTO pickingMapDTO) {
        return new ItemResult<>(pickingMapBizService.addPickingMap(pickingMapDTO));
    }

    @Override
    public ItemResult<Void> updatePickingMap(PickingMapDTO pickingMapDTO) {
        pickingMapBizService.updatePickingMap(pickingMapDTO);
        return new ItemResult<>(null);
    }
}
