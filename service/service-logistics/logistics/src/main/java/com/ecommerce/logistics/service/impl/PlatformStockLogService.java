package com.ecommerce.logistics.service.impl;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.storehouse.TSSellerViewItem;
import com.ecommerce.logistics.api.dto.storehouse.TurnoverProductViewDTO;
import com.ecommerce.logistics.api.dto.storehouse.TurnoverQueryDTO;
import com.ecommerce.logistics.api.dto.storehouse.TurnoverSellerViewDTO;
import com.ecommerce.logistics.biz.stock.IPlatformStockLogBizService;
import com.ecommerce.logistics.service.IPlatformStockLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Auther: colu
 * @Date: 2019-09-16 11:50
 * @Description: PlatformStockLogService
 */
@Service("platformStockLogService")
public class PlatformStockLogService implements IPlatformStockLogService {

    @Autowired
    private IPlatformStockLogBizService platformStockLogBizService;

    @Override
    public ItemResult<PageData<TurnoverSellerViewDTO>> sellerViewTurnover(PageQuery<TurnoverQueryDTO> pageQuery) {
        return new ItemResult<>(platformStockLogBizService.sellerViewTurnover(pageQuery));
    }

    @Override
    public ItemResult<PageData<TurnoverProductViewDTO>> productViewTurnover(PageQuery<TurnoverQueryDTO> pageQuery) {
        return new ItemResult<>(platformStockLogBizService.productViewTurnover(pageQuery));
    }

    @Override
    public ItemResult<PageData<TSSellerViewItem>> turnoverForSeller(PageQuery<TurnoverQueryDTO> pageQuery) {
        return new ItemResult<>(platformStockLogBizService.turnoverForSeller(pageQuery));
    }

    @Override
    public ItemResult<List<TSSellerViewItem>> queryTurnoverForSeller(TurnoverQueryDTO turnoverQueryDTO) {
        return new ItemResult<>(platformStockLogBizService.queryTurnoverForSeller(turnoverQueryDTO));
    }

    @Override
    public ItemResult<List<TurnoverSellerViewDTO>> queryTurnoverForPlatform(TurnoverQueryDTO turnoverQueryDTO) {
        return new ItemResult<>(platformStockLogBizService.queryTurnoverForPlatform(turnoverQueryDTO));
    }
}
