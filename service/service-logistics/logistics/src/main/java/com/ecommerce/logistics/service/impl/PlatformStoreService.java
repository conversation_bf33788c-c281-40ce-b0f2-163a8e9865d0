package com.ecommerce.logistics.service.impl;

import com.alibaba.fastjson.JSON;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.storehouse.PlatformStockListDTO;
import com.ecommerce.logistics.api.dto.storehouse.PlatformStockQueryDTO;
import com.ecommerce.logistics.biz.stock.IPlatformStoreMapBizService;
import com.ecommerce.logistics.service.IPlatformStoreService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Auther: colu
 * @Date: 2019-07-11 11:47
 * @Description: 卖家占用平台库存服务
 */

@Slf4j
@Service("platformStoreService")
public class PlatformStoreService implements IPlatformStoreService {

    @Autowired
    private IPlatformStoreMapBizService platformStoreMapBizService;

    @Override
    public ItemResult<PageData<PlatformStockListDTO>> queryPlatformStockList(PageQuery<PlatformStockQueryDTO> pageQuery) {
        PageData<PlatformStockListDTO> platformStockListDTOPageData = platformStoreMapBizService.queryPlatformStockList(pageQuery);
        log.info("查询卖家占用的平台库存情况列表:{}==>{}", JSON.toJSONString(pageQuery), JSON.toJSONString(platformStockListDTOPageData));
        return new ItemResult<>(platformStockListDTOPageData);
    }

}
