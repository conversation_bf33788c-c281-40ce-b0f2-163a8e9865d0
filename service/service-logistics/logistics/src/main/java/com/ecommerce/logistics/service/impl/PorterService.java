package com.ecommerce.logistics.service.impl;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.intelligentDispatch.PorterEditDTO;
import com.ecommerce.logistics.api.dto.intelligentDispatch.PorterListDTO;
import com.ecommerce.logistics.api.dto.intelligentDispatch.PorterQueryDTO;
import com.ecommerce.logistics.biz.IPorterBizService;
import com.ecommerce.logistics.service.IPorterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Description: PorterService
 * @Author: <EMAIL>
 * @Date: 2021-04-13 15:42
 */
@Service
public class PorterService implements IPorterService {

    @Autowired
    private IPorterBizService porterBizService;

    @Override
    public ItemResult<PageData<PorterListDTO>> queryPorterList(PageQuery<PorterQueryDTO> pageQuery) {
        return new ItemResult<>(porterBizService.queryPorterList(pageQuery));
    }

    @Override
    public ItemResult<Void> editPorter(PorterEditDTO porterEditDTO) {
        porterBizService.editPorter(porterEditDTO);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> addPorter(PorterEditDTO porterEditDTO) {
        porterBizService.addPorter(porterEditDTO);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> delPorter(PorterEditDTO porterEditDTO) {
        porterBizService.delPorter(porterEditDTO);
        return new ItemResult<>(null);
    }
}
