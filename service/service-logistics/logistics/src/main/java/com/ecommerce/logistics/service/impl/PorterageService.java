package com.ecommerce.logistics.service.impl;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.porterage.PorterageComputeDTO;
import com.ecommerce.logistics.api.dto.porterage.PorterageComputeResultDTO;
import com.ecommerce.logistics.api.dto.porterage.PorterageDetailDTO;
import com.ecommerce.logistics.api.dto.porterage.PorterageDetailQueryDTO;
import com.ecommerce.logistics.api.dto.porterage.PorterageListDTO;
import com.ecommerce.logistics.api.dto.porterage.PorterageListQueryDTO;
import com.ecommerce.logistics.api.dto.porterage.PorterageSaveDTO;
import com.ecommerce.logistics.api.dto.porterage.PorterageSearchDTO;
import com.ecommerce.logistics.api.dto.porterage.PorterageSearchResultDTO;
import com.ecommerce.logistics.api.dto.porterage.PorterageZoneListDTO;
import com.ecommerce.logistics.api.dto.porterage.PorterageZoneQueryDTO;
import com.ecommerce.logistics.biz.IPorterageBizService;
import com.ecommerce.logistics.service.IPorterageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Description:
 * @Date: Create in 上午11:08 18/11/26
 */
@Service
public class PorterageService implements IPorterageService{

    @Autowired
    private IPorterageBizService porterageBizService;

    @Override
    public ItemResult<String> addPorterageRule(PorterageSaveDTO porterageSaveDTO) {
        return new ItemResult<>(porterageBizService.addPorterageRule(porterageSaveDTO));
    }

    @Override
    public ItemResult<String> deletePorterageRule(String porterageRuleId) {
        return new ItemResult<>(porterageBizService.deletePorterageRule(porterageRuleId,null));
    }

    @Override
    public ItemResult<Void> editPorterageRule(PorterageSaveDTO porterageSaveDTO) {
        porterageBizService.editPorterageRule(porterageSaveDTO);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<PageData<PorterageListDTO>> queryPorterageRuleList(PageQuery<PorterageListQueryDTO> pageQuery) {
        return new ItemResult<>(porterageBizService.queryPorterageRuleList(pageQuery));
    }

    @Override
    public ItemResult<PorterageDetailDTO> queryPorterageDetail(PorterageDetailQueryDTO porterageDetailQueryDTO) {
        return new ItemResult<>(porterageBizService.queryPorterageDetail(porterageDetailQueryDTO));
    }

    @Override
    public ItemResult<List<PorterageSearchResultDTO>> searchPorterageRuleList(PorterageSearchDTO porterageSearchDTO) {
        return new ItemResult<>(porterageBizService.searchPorterageRuleList(porterageSearchDTO));
    }

    @Override
    public ItemResult<PorterageComputeResultDTO> computePorterage(PorterageComputeDTO porterageComputeDTO) {
        return new ItemResult<>(porterageBizService.computePorterage(porterageComputeDTO));
    }

    @Override
    public ItemResult<List<PorterageZoneListDTO>> queryZonePorterageList(PorterageZoneQueryDTO porterageZoneQueryDTO) {
        return new ItemResult<>(porterageBizService.queryZonePorterageList(porterageZoneQueryDTO));
    }
}
