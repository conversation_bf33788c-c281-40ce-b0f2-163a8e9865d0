package com.ecommerce.logistics.service.impl;

import java.util.List;

import com.ecommerce.logistics.api.dto.pickingbill.OptionDTO;
import com.ecommerce.logistics.biz.IProductInfoBizService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.logistics.api.dto.productinfo.ProductInfoDTO;
import com.ecommerce.logistics.service.IProductInfoService;

/**
 * 
 * @Auther: <EMAIL>
 * @Date: 2018年8月31日 上午10:21:07
 * @Description:
 */
@Service
public class ProductInfoService implements IProductInfoService {
	
	@Autowired
	private IProductInfoBizService productInfoBizService;

	@Override
	public ItemResult<List<ProductInfoDTO>> queryInfoLikeNote(String note) {
		return new ItemResult<>(productInfoBizService.queryInfoLikeNote(note));
	}

	@Override
	public ItemResult<List<OptionDTO>> queryOptionsByKeyWord(String keyWord) {
		return new ItemResult<>(productInfoBizService.queryOptionsByKeyWord(keyWord));
	}
}
