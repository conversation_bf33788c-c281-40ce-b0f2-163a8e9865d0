package com.ecommerce.logistics.service.impl;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.ProxySyncRecord.ProxySyncRecordListDTO;
import com.ecommerce.logistics.api.dto.ProxySyncRecord.ProxySyncRecordQueryDTO;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.biz.IProxySyncRecordBizService;
import com.ecommerce.logistics.service.IProxySyncRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class ProxySyncRecordService implements IProxySyncRecordService {

	@Autowired
	private IProxySyncRecordBizService proxySyncRecordBizService;

	@Override
	public ItemResult<PageData<ProxySyncRecordListDTO>> queryProxySyncRecordList(PageQuery<ProxySyncRecordQueryDTO> pageQuery) {
		return new ItemResult<>(proxySyncRecordBizService.queryProxySyncRecordList(pageQuery));
	}

	@Override
	public ItemResult<Void> syncByProxySyncIds(List<String> proxySyncIds) {
		proxySyncRecordBizService.syncByProxySyncIds(proxySyncIds);
		return new ItemResult<>(null);
	}
}
