package com.ecommerce.logistics.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.ecommerce.base.api.common.RedisKey;
import com.ecommerce.base.api.dto.warehouse.WarehouseDetailsDTO;
import com.ecommerce.base.api.service.IWarehouseService;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.logistics.api.dto.carriage.TransportRouteDTO;
import com.ecommerce.logistics.api.dto.carriage.TransportRouteQueryDTO;
import com.ecommerce.logistics.service.IRouteContractService;
import com.ecommerce.member.api.service.IMemberConfigService;
import com.ecommerce.member.api.service.IMemberRelationService;
import com.ecommerce.open.api.dto.apicenter.ApiResult;
import com.ecommerce.open.api.dto.apicenter.logistic.route.TransportRouteSyncRequestDTO;
import com.ecommerce.open.api.dto.apicenter.logistic.route.TransportRouteSyncResponseDTO;
import com.ecommerce.open.api.service.apicenter.IOpenAPIInvokeService;
import com.ecommerce.open.enums.BizCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date 2021/1/28 17:26
 * @Version 1.0
 */
@Slf4j
@Service
public class RouteContractService implements IRouteContractService {

    //缓存一天的时间
    private static final long EXPIRE_TIME = 1;

    @Autowired
    private IOpenAPIInvokeService openAPIInvokeService;
    @Autowired
    private IMemberConfigService memberConfigService;
    @Autowired
    private IMemberRelationService memberRelationService;
    @Autowired
    private IWarehouseService iWarehouseService;
    @Autowired
    @Qualifier("bizRedisTemplate")
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    @Qualifier("myTaskAsyncPool")
    private Executor executor;


    @Override
    public List<TransportRouteDTO> queryERPTransportRoute(TransportRouteQueryDTO dto) {
        log.info("queryERPTransportRoute入参：{}", dto);
        //获取仓库地址对应erp系统的仓库编码
        ItemResult<WarehouseDetailsDTO> result = iWarehouseService.queryWarehouseDetail(dto.getPickupPointCode());
        if (!result.isSuccess() || result.getData() == null || CsStringUtils.isBlank(result.getData().getErpCode())) {
            throw new BizException(BasicCode.CUSTOM_ERROR, "提货仓库未与ERP系统绑定");
        }
        String pickupPointCode = result.getData().getErpCode();
        //通过erp的仓库编码查询erp的运输路线
        dto.setPickupPointCode(pickupPointCode);
        log.info("查询ERP运输路线信息入参：{}", dto);
        String key = RedisKey.join(RedisKey.TRANSPORT_ROUTE, dto.getSellerId(), dto.getBuyerId(), dto.getPickupPointCode());
        String strList = redisTemplate.opsForValue().get(key);
        List<TransportRouteDTO> list;
        if (CsStringUtils.isNotBlank(strList)) {
            list = JSON.parseObject(strList, new TypeReference<List<TransportRouteDTO>>() {});
            log.info("从Redis中获取ERP运输路线信息集合长度为：{}", list == null ? 0 : list.size());
            return list;
        } else
            {
            executor.execute(()->{
                String oldName = Thread.currentThread().getName();
                try {
                    Thread.currentThread().setName("ERPTransportRoute");
                    queryERPTransportRoute(key,dto);
                } catch (Exception e){
                    log.error(e.getMessage(),e);
                } finally {
                    Thread.currentThread().setName(oldName);
                }
            });
            strList = this.redisTemplate.opsForValue().get(key);
            list = JSON.parseObject(strList, new TypeReference<List<TransportRouteDTO>>() {});
            log.info("查询ERP运输路线返回结果：{}", list);
            return list;
        }
    }

    public void queryERPTransportRoute(String key, TransportRouteQueryDTO dto) {
        TransportRouteSyncRequestDTO request = new TransportRouteSyncRequestDTO();
        //ERP主数据编码
        request.setMdmCode(dto.getMdmCode());
        request.setExternalSellerId(dto.getSellerId());
        request.setPickupPointCode(dto.getPickupPointCode());
        log.info("EC_DEP_Q1 invoke args: {} {} {}", BizCodeEnum.EC_DEP_Q1.getCode(), dto.getSellerId(), JSON.toJSONString(request));
        ApiResult result = openAPIInvokeService.invoke(BizCodeEnum.EC_DEP_Q1.getCode(), dto.getSellerId(), JSON.toJSONString(request));
        log.info("EC_DEP_Q1 invoke result:{}", result);
        if (result.isSuccess()) {
            List<TransportRouteSyncResponseDTO> list = result.getList(TransportRouteSyncResponseDTO.class);
            log.info("erp查询接口成功,args:{} {} {},list.size:{}", BizCodeEnum.EC_DEP_Q1.getCode(), dto.getSellerId(),
                    JSON.toJSONString(request), list == null ? 0 : list.size());
            List<TransportRouteDTO> dtoList = new LinkedList<>();
            if (CollectionUtils.isNotEmpty(list)) {
                list.forEach(item ->{
                    TransportRouteDTO routeDto = new TransportRouteDTO();
                    BeanUtils.copyProperties(item, routeDto);
                    dtoList.add(routeDto);
                });
            }
            String strList = JSON.toJSON(dtoList).toString();
            //设置有效期1天，单位：天
            this.redisTemplate.opsForValue().set(key, strList, EXPIRE_TIME, TimeUnit.DAYS);
        }
    }

}
