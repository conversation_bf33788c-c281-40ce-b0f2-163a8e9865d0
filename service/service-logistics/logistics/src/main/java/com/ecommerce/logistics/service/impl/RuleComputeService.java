package com.ecommerce.logistics.service.impl;

import com.alibaba.fastjson.JSON;
import com.ecommerce.base.api.service.IWarehouseService;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.service.common.uuid.UUIDGenerator;
import com.ecommerce.logistics.api.dto.operationrecord.OperationRecordAddDTO;
import com.ecommerce.logistics.api.dto.rulecompute.CarriageComputeDTO;
import com.ecommerce.logistics.api.dto.rulecompute.CarriageComputeResultDTO;
import com.ecommerce.logistics.api.dto.rulecompute.CarriageRuleResultDTO;
import com.ecommerce.logistics.api.dto.rulecompute.CategoryQuantityDTO;
import com.ecommerce.logistics.api.dto.rulecompute.ComputeCarriagePriceDTO;
import com.ecommerce.logistics.api.dto.rulecompute.WaybillMergeDTO;
import com.ecommerce.logistics.api.dto.waybill.CreateWaybillDTO;
import com.ecommerce.logistics.api.enums.CarriageSettlementTypeEnum;
import com.ecommerce.logistics.api.enums.DeliveryTimeRangeEnum;
import com.ecommerce.logistics.api.enums.OperationRecordTypeEnum;
import com.ecommerce.logistics.api.enums.PickingBillTypeEnum;
import com.ecommerce.logistics.api.enums.WaybillOperationTypeEnum;
import com.ecommerce.logistics.api.enums.WaybillStatusEnum;
import com.ecommerce.logistics.api.enums.WaybillTypeEnum;
import com.ecommerce.logistics.biz.ICarriageRuleBizService;
import com.ecommerce.logistics.biz.IOperationRecordBizService;
import com.ecommerce.logistics.biz.IRuleComputeBizService;
import com.ecommerce.logistics.biz.IVehicleTypeBizService;
import com.ecommerce.logistics.biz.IWaybillCompositeBizService;
import com.ecommerce.logistics.biz.IWaybillQueryBizService;
import com.ecommerce.logistics.biz.carriage.CarriageFactoryContext;
import com.ecommerce.logistics.dao.dto.pickingbill.PickingBillListDO;
import com.ecommerce.logistics.dao.dto.waybill.WaybillQuantityDO;
import com.ecommerce.logistics.dao.vo.VehicleType;
import com.ecommerce.logistics.exception.LogisticsErrorCode;
import com.ecommerce.logistics.service.IRuleComputeService;
import com.ecommerce.member.api.dto.member.CollectionCarrierQueryDTO;
import com.ecommerce.member.api.dto.member.MemberDTO;
import com.ecommerce.member.api.service.IMemberAreaService;
import com.ecommerce.member.api.service.IMemberService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * @Author: <EMAIL>
 * @Description:
 * @Date: Create in 上午11:08 18/8/15
 */
@Slf4j
@Service
public class RuleComputeService implements IRuleComputeService{

    @Autowired
    protected IWaybillCompositeBizService waybillCompositeBizService;

    @Autowired
    protected IRuleComputeBizService ruleComputeBizService;

    @Autowired
    protected IVehicleTypeBizService vehicleTypeBizService;

    @Autowired
    private UUIDGenerator uuidGenerator;

    @Autowired
    private IWaybillQueryBizService waybillQueryBizService;

    @Autowired
    private ICarriageRuleBizService carriageRuleBizService;

    @Autowired
    private IWarehouseService warehouseService;

    @Autowired
    private IOperationRecordBizService operationRecordBizService;

    @Autowired
    private IMemberAreaService memberAreaService;


    @Autowired
    private IMemberService memberService;

    @Override
    public ItemResult<BigDecimal> waybillCarriageCompute(CarriageComputeDTO carriageComputeDTO) {
        return null;
    }

    @Override
    public ItemResult<List<CarriageComputeResultDTO>> orderCarriageCompute(CarriageComputeDTO carriageComputeDTO) {
        log.info("carriageComputeDTO:" + carriageComputeDTO);
        dataValid(carriageComputeDTO);
        List<CarriageComputeResultDTO> computeResult = new ArrayList<>();
//        //获取当前区域平台自有承运商
        ComputeCarriagePriceDTO computeCarriagePriceDTO = new ComputeCarriagePriceDTO();
        computeCarriagePriceDTO.setEffectiveDate(new Date());
        computeCarriagePriceDTO.setSettlementType(CarriageSettlementTypeEnum.COLLECT.getCode());

        handleCarriageRule(carriageComputeDTO, computeCarriagePriceDTO);

        computeCarriagePriceDTO.setWarehouseId(carriageComputeDTO.getWarehouseId());
        computeCarriagePriceDTO.setReceiveAddressId(carriageComputeDTO.getReceiveAddressId());
        computeCarriagePriceDTO.setReceiveAddressLocation(carriageComputeDTO.getReceiveAddressLocation());
        computeCarriagePriceDTO.setProvinceCode(carriageComputeDTO.getProvinceCode());
        computeCarriagePriceDTO.setCityCode(carriageComputeDTO.getCityCode());
        computeCarriagePriceDTO.setDistrictCode(carriageComputeDTO.getDistrictCode());
        computeCarriagePriceDTO.setStreetCode(carriageComputeDTO.getStreetCode());
        for (CategoryQuantityDTO categoryQuantityDTO : carriageComputeDTO.getCategoryQuantityList()) {
            //获取运输品类匹配的车型数据
            List<VehicleType> vehicleTypeList = vehicleTypeBizService.
                    queryVehicleTypeByCondition(categoryQuantityDTO.getTransportCategoryId());
            if (CollectionUtils.isEmpty(vehicleTypeList)) {
                throw new BizException(LogisticsErrorCode.DATA_VALID_FAIL, "找不到当前运输品类对应的车型,运输品类ID："
                        + categoryQuantityDTO.getTransportCategoryId());
            }
            VehicleType minVehicleType = vehicleTypeList.get(0);
            VehicleType maxVehicleType = vehicleTypeList.get(0);
            for (VehicleType vehicleType : vehicleTypeList) {
                minVehicleType = getMinVehicleType(categoryQuantityDTO, vehicleType, minVehicleType);
                maxVehicleType = getMaxVehicleType(vehicleType, maxVehicleType);
            }
            //找不到匹配车型
            minVehicleType = getMinVehicleTypeByNotFoundType(categoryQuantityDTO, maxVehicleType, minVehicleType);
            log.info("minVehicleType:" + minVehicleType);
            //调用车型路径规划

            computeCarriagePriceDTO.setTransportCategoryId(categoryQuantityDTO.getTransportCategoryId());
            computeCarriagePriceDTO.setProductQuantity(categoryQuantityDTO.getProductQuantity());
            //运费计算
            BigDecimal carriage = ruleComputeBizService.computeCarriagePrice(computeCarriagePriceDTO);
            if (BigDecimal.ZERO.compareTo(carriage) > 0) {
                throw new BizException(LogisticsErrorCode.DATA_EXCEPTION,
                        "找不到运输品类[" + categoryQuantityDTO.getTransportCategoryId() + "]对应运费规则");
            }
            log.info(categoryQuantityDTO.getTransportCategoryId() + "|carriage:" + carriage);
            //添加返回结果
            CarriageComputeResultDTO computeResultDTO = new CarriageComputeResultDTO();
            computeResultDTO.setTransportCategoryId(categoryQuantityDTO.getTransportCategoryId());
            computeResultDTO.setProductQuantity(categoryQuantityDTO.getProductQuantity());
            computeResultDTO.setCarriage(carriage.setScale(2, BigDecimal.ROUND_UP));
            computeResultDTO.setCarrierId(carriageComputeDTO.getUserId());
            if (CsStringUtils.equals(PickingBillTypeEnum.PLATFORM_DELIVERY.getCode(), carriageComputeDTO.getPickingBillType())) {
                CollectionCarrierQueryDTO collectionCarrierQueryDTO = new CollectionCarrierQueryDTO();
                MemberDTO memberDTO = memberService.findCollectionCarrier(collectionCarrierQueryDTO);
                log.info("memberDTO:" + JSON.toJSONString(memberDTO));
                if (memberDTO == null || CsStringUtils.isEmpty(memberDTO.getMemberId())) {
                    throw new BizException(BasicCode.UNDEFINED_ERROR, "无法获取平台承运商！");
                }
                computeResultDTO.setCarrierId(memberDTO.getMemberId());
            }

            computeResult.add(computeResultDTO);
        }

        return new ItemResult<>(computeResult);
    }

    private static VehicleType getMinVehicleTypeByNotFoundType(CategoryQuantityDTO categoryQuantityDTO, VehicleType maxVehicleType, VehicleType minVehicleType) {
        if (categoryQuantityDTO.getProductQuantity().compareTo(maxVehicleType.getMaxLoadCapacity()) > 0) {
            minVehicleType = maxVehicleType;
        }
        return minVehicleType;
    }

    private static VehicleType getMaxVehicleType(VehicleType vehicleType, VehicleType maxVehicleType) {
        if (vehicleType.getMaxLoadCapacity().compareTo(maxVehicleType.getMaxLoadCapacity()) > 0) {
            maxVehicleType = vehicleType;
        }
        return maxVehicleType;
    }

    private static VehicleType getMinVehicleType(CategoryQuantityDTO categoryQuantityDTO, VehicleType vehicleType, VehicleType minVehicleType) {
        if (categoryQuantityDTO.getProductQuantity().compareTo(vehicleType.getMaxLoadCapacity()) < 0 &&
            vehicleType.getMaxLoadCapacity().compareTo(minVehicleType.getMaxLoadCapacity()) < 0) {
            minVehicleType = vehicleType;
        }
        return minVehicleType;
    }

    private static void handleCarriageRule(CarriageComputeDTO carriageComputeDTO, ComputeCarriagePriceDTO computeCarriagePriceDTO) {
        //卖家规则
        if (PickingBillTypeEnum.SELLER_DELIVERY.getCode().equals(carriageComputeDTO.getPickingBillType())) {
            computeCarriagePriceDTO.setUserId(carriageComputeDTO.getUserId());
        //平台规则
        } else if (PickingBillTypeEnum.PLATFORM_DELIVERY.getCode().equals(carriageComputeDTO.getPickingBillType())) {
            computeCarriagePriceDTO.setUserId(CarriageFactoryContext.PLATFORM_ID);
        } else {
            throw new BizException(LogisticsErrorCode.DATA_EXCEPTION, "不支持的配送类型");
        }
    }

    @Override
    public ItemResult<String> waybillMerge(WaybillMergeDTO waybillMergeDTO) {
        if (CollectionUtils.isEmpty(waybillMergeDTO.getWaybillIdList())) {
            throw new BizException(BasicCode.INVALID_PARAM, "waybillIdList=null");
        }
        List<PickingBillListDO> pickingBillList  = ruleComputeBizService.getMergeInfo(waybillMergeDTO);
        List<String> wayPointIdList = new ArrayList<>();
        PickingBillListDO pickingBillDO = pickingBillList.get(0);
        Date date = pickingBillDO.getDeliveryTime();
        String warehouseId = pickingBillDO.getWarehouseId();
        Set<String> transportCategoryIdSet = new HashSet<>();
        handlePickingBillList(waybillMergeDTO, pickingBillList, date, warehouseId, wayPointIdList, transportCategoryIdSet);
        //根据运输品类查询车型
        List<VehicleType> vehicleTypeList = vehicleTypeBizService.queryVehicleTypeByCondition(pickingBillDO.getTransportCategoryId());
        if (CollectionUtils.isEmpty(vehicleTypeList)) {
            throw new BizException(LogisticsErrorCode.NOT_VALID_VEHICLE_TYPE, pickingBillDO.getTransportCategoryId());
        }
        ComputeCarriagePriceDTO computeCarriagePriceDTO = new ComputeCarriagePriceDTO();
        computeCarriagePriceDTO.setSettlementType(CarriageSettlementTypeEnum.PAY.getCode());
        computeCarriagePriceDTO.setUserId(CarriageFactoryContext.PLATFORM_ID);
        computeCarriagePriceDTO.setWarehouseId(pickingBillDO.getWarehouseId());
        computeCarriagePriceDTO.setReceiveAddressId("NONE");
        computeCarriagePriceDTO.setReceiveAddressLocation("NONE");
        computeCarriagePriceDTO.setProvinceCode(pickingBillDO.getReceiveProvinceCode());
        computeCarriagePriceDTO.setCityCode(pickingBillDO.getReceiveCityCode());
        computeCarriagePriceDTO.setDistrictCode(pickingBillDO.getReceiveDistrictCode());
        computeCarriagePriceDTO.setSettlementUserId(CarriageFactoryContext.SOCIETY_CARRIER_ID);
        computeCarriagePriceDTO.setTransportCategoryId(pickingBillDO.getTransportCategoryId());

        String mainWaybillId = waybillMergeDTO.getWaybillIdList().size() > 1 ?
                uuidGenerator.gain() : waybillMergeDTO.getWaybillIdList().get(0);
        //由于多点规划不支持批量规划，只能取任意车型进行距离规划
        VehicleType planVehicleType = vehicleTypeList.get(0);
        //获取当前规划路径距离

        //获取所有子运单的运输重量
        WaybillQuantityDO waybillQuantityDO = waybillQueryBizService.queryWaybillTotalQuantity(waybillMergeDTO.getWaybillIdList());
        BigDecimal totalProductQuantity = waybillQuantityDO.getQuantity();
        //车型按最大载重排序
        Collections.sort(vehicleTypeList, (o1, o2) -> o1.getMaxLoadCapacity().compareTo(o2.getMaxLoadCapacity()));
        //匹配车型
        VehicleType minVehicleType = null;
        BigDecimal carriage = BigDecimal.ZERO;
        for (VehicleType vehicleType : vehicleTypeList) {
            //获取车型、运输品类、地区获取多车型的运价信息
            computeCarriagePriceDTO.setVehicleTypeId(vehicleType.getVehicleTypeId());
            computeCarriagePriceDTO.setProductQuantity(vehicleType.getMaxLoadCapacity());
            computeCarriagePriceDTO.setEffectiveDate(pickingBillDO.getDeliveryTime());
            carriage = ruleComputeBizService.computeCarriagePrice(computeCarriagePriceDTO);
            //应测试要求将运费规则加入当前车型是否能够配送的限制里面
            if (carriage == null || BigDecimal.ZERO.compareTo(carriage) > 0) {
                log.info("当前车型找不到对应运费规则：" + JSON.toJSONString(computeCarriagePriceDTO));
                continue;
            }
            //获取当前载货量匹配的车型数据
            if (vehicleType.getMaxLoadCapacity().compareTo(totalProductQuantity) > -1) {
                minVehicleType = vehicleType;
                break;
            }
        }
        if (minVehicleType == null) {
            throw new BizException(LogisticsErrorCode.NOT_FOUND_CARRIAGE_RULR);
        }
        //可以合并的运输品类运费规则一致
        CreateWaybillDTO createWaybillDTO = new CreateWaybillDTO();
        createWaybillDTO.setEstimateCarriage(carriage);
        //多运单整合创建主运单
        if (waybillMergeDTO.getWaybillIdList().size() > 1) {
            createWaybillDTO.setPickingBillId("");
            createWaybillDTO.setDispatchBillId("");
            createWaybillDTO.setDriverName("");
            createWaybillDTO.setDriverPhone("");
            createWaybillDTO.setVehicleNum("");
            createWaybillDTO.setProductQuantity(totalProductQuantity);
            createWaybillDTO.setCarrierId("");
            createWaybillDTO.setDriverId("");
            createWaybillDTO.setOperationUserId(waybillMergeDTO.getOperationUserId());
            createWaybillDTO.setOperationUserName(waybillMergeDTO.getOperationUserName());
            createWaybillDTO.setDeliveryTime(pickingBillList.get(0).getDeliveryTime());
            createWaybillDTO.setDeliveryTimeRange(DeliveryTimeRangeEnum.WHOLE_DAY.getCode());
            createWaybillDTO.setType(WaybillTypeEnum.SOCIETY_SNATCH.getCode());
            createWaybillDTO.setStatus(WaybillStatusEnum.WAIT_AUDIT.getCode());
            createWaybillDTO.setWaybillId(mainWaybillId);
            //子运单路径排序
            List<String> orderWaybillList = new ArrayList<>();

            //生成主运单
            waybillCompositeBizService.mergeSubWaybill(createWaybillDTO, waybillMergeDTO, orderWaybillList);
        } else {
            //修改当前子运单状态并添加运单信息记录
            waybillCompositeBizService.addWaybillInfo(createWaybillDTO, mainWaybillId);
            waybillCompositeBizService.updateSubWaybillStatus(waybillMergeDTO.getWaybillIdList(),
                    WaybillStatusEnum.WAIT_AUDIT.getCode(),
                    mainWaybillId, 1);
            OperationRecordAddDTO operationRecordAddDTO = new OperationRecordAddDTO();
			operationRecordAddDTO.setEntryId(mainWaybillId);
			operationRecordAddDTO.setType(OperationRecordTypeEnum.WAYBILL.getCode());
			operationRecordAddDTO.setOperationType(WaybillOperationTypeEnum.WAYBILL_MERGE.getCode());
			operationRecordAddDTO.setOperatorId(waybillMergeDTO.getOperationUserId());
			operationRecordAddDTO.setOperatorName(waybillMergeDTO.getOperationUserName());
			operationRecordAddDTO.setContent("整合运单");
			operationRecordBizService.saveOperationRecord(operationRecordAddDTO);
        }

        return new ItemResult<>(mainWaybillId);
    }

    private static void handlePickingBillList(WaybillMergeDTO waybillMergeDTO, List<PickingBillListDO> pickingBillList, Date date, String warehouseId, List<String> wayPointIdList, Set<String> transportCategoryIdSet) {
        for (PickingBillListDO pickingBillListDO : pickingBillList) {
            if (pickingBillListDO.getDeliveryTime().equals(date) &&
                    pickingBillListDO.getWarehouseId().equals(warehouseId)) {
                wayPointIdList.add(pickingBillListDO.getReceiveAddressId());
                if (!transportCategoryIdSet.contains(pickingBillListDO.getTransportCategoryId())) {
                    transportCategoryIdSet.add(pickingBillListDO.getTransportCategoryId());
                }
            } else {
                throw new BizException(LogisticsErrorCode.MERGE_WAYBILL_ERROR, waybillMergeDTO);
            }
        }
    }

    @Override
    public ItemResult<List<CarriageRuleResultDTO>> queryCarriageRule(CarriageComputeDTO carriageComputeDTO) {
        log.info("queryCarriageRule:" + carriageComputeDTO);
        dataValid(carriageComputeDTO);
        List<CarriageRuleResultDTO> carriageRuleList = new ArrayList<>();
        ComputeCarriagePriceDTO computeCarriagePriceDTO = new ComputeCarriagePriceDTO();
        computeCarriagePriceDTO.setEffectiveDate(new Date());
        computeCarriagePriceDTO.setSettlementType(CarriageSettlementTypeEnum.COLLECT.getCode());
        //卖家规则
        handleCarriageRule(carriageComputeDTO, computeCarriagePriceDTO);
        computeCarriagePriceDTO.setWarehouseId(carriageComputeDTO.getWarehouseId());
        computeCarriagePriceDTO.setReceiveAddressId(carriageComputeDTO.getReceiveAddressId());
        computeCarriagePriceDTO.setReceiveAddressLocation(carriageComputeDTO.getReceiveAddressLocation());
        computeCarriagePriceDTO.setProvinceCode(carriageComputeDTO.getProvinceCode());
        computeCarriagePriceDTO.setCityCode(carriageComputeDTO.getCityCode());
        computeCarriagePriceDTO.setDistrictCode(carriageComputeDTO.getDistrictCode());
        computeCarriagePriceDTO.setStreetCode(carriageComputeDTO.getStreetCode());
        for (CategoryQuantityDTO categoryQuantityDTO : carriageComputeDTO.getCategoryQuantityList()) {
            computeCarriagePriceDTO.setDistance(new BigDecimal(10));
            computeCarriagePriceDTO.setTransportCategoryId(categoryQuantityDTO.getTransportCategoryId());
            computeCarriagePriceDTO.setProductQuantity(categoryQuantityDTO.getProductQuantity());
            //运费计算
            BigDecimal carriage = ruleComputeBizService.computeCarriagePrice(computeCarriagePriceDTO);
            //添加返回结果
            CarriageRuleResultDTO carriageComputeResultDTO = new CarriageRuleResultDTO();
            carriageComputeResultDTO.setTransportCategoryId(categoryQuantityDTO.getTransportCategoryId());
            carriageComputeResultDTO.setProductQuantity(categoryQuantityDTO.getProductQuantity());
            carriageComputeResultDTO.setRuleFlag(BigDecimal.ZERO.compareTo(carriage) > 0 ? Boolean.FALSE : Boolean.TRUE);
            carriageRuleList.add(carriageComputeResultDTO);
        }

        return new ItemResult<>(carriageRuleList);
    }

    /**
     * 数据验证
     * @param carriageComputeDTO 运费计算对象
     */
    private void dataValid(CarriageComputeDTO carriageComputeDTO) {
        if (CsStringUtils.isEmpty(carriageComputeDTO.getPickingBillType())) {
            throw new BizException(BasicCode.INVALID_PARAM, "配送类型");
        }
        if (CsStringUtils.isEmpty(carriageComputeDTO.getUserId())) {
            throw new BizException(BasicCode.INVALID_PARAM, "卖家ID");
        }
        if (CsStringUtils.isEmpty(carriageComputeDTO.getWarehouseId())) {
            throw new BizException(BasicCode.INVALID_PARAM, "提货点ID");
        }
        if (CsStringUtils.isEmpty(carriageComputeDTO.getReceiveAddressId())) {
            throw new BizException(BasicCode.INVALID_PARAM, "收货地址ID");
        }
        if (CsStringUtils.isEmpty(carriageComputeDTO.getReceiveAddressLocation())) {
            throw new BizException(BasicCode.INVALID_PARAM, "收货地址位置");
        }
        if (CsStringUtils.isEmpty(carriageComputeDTO.getProvinceCode())) {
            throw new BizException(BasicCode.INVALID_PARAM, "省份编码");
        }
        if (CsStringUtils.isEmpty(carriageComputeDTO.getCityCode())) {
            throw new BizException(BasicCode.INVALID_PARAM, "城市编码");
        }
        if (CollectionUtils.isEmpty(carriageComputeDTO.getCategoryQuantityList())) {
            throw new BizException(BasicCode.INVALID_PARAM, "运输品类集合");
        }
    }
}
