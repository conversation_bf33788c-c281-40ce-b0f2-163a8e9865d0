package com.ecommerce.logistics.service.impl;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.logistics.api.dto.rule.CarriageRuleDTO;
import com.ecommerce.logistics.api.dto.rule.CarriageRuleListDTO;
import com.ecommerce.logistics.api.dto.rule.CarriageRuleListQueryDTO;
import com.ecommerce.logistics.api.dto.rule.MatchCarriageRuleDTO;
import com.ecommerce.logistics.service.IRuleService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Description:
 * @Date: Create in 上午11:09 18/8/15
 */
@Service
public class RuleService implements IRuleService {
    @Override
    public ItemResult<List<CarriageRuleListDTO>> queryCarriageRuleList(CarriageRuleListQueryDTO carriageRuleListQueryDTO) {
        return null;
    }

    @Override
    public ItemResult<Void> addCarriageRule(CarriageRuleDTO carriageRuleDTO) {
        return null;
    }

    @Override
    public ItemResult<Void> removeCarriageRule(String carriageRuleId) {
        return null;
    }

    @Override
    public ItemResult<Void> editCarriageRule(CarriageRuleDTO carriageRuleDTO) {
        return null;
    }

    @Override
    public ItemResult<CarriageRuleDTO> matchCarriageRule(MatchCarriageRuleDTO matchCarriageRuleDTO) {
        return null;
    }
}
