package com.ecommerce.logistics.service.impl;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.settlement.MonthSettlementListDTO;
import com.ecommerce.logistics.api.dto.settlement.MonthSettlementListQueryDTO;
import com.ecommerce.logistics.api.dto.settlement.SettlementItemListDTO;
import com.ecommerce.logistics.api.dto.settlement.SettlementItemListQueryDTO;
import com.ecommerce.logistics.api.dto.settlement.SettlementListDTO;
import com.ecommerce.logistics.api.dto.settlement.SettlementListQueryDTO;
import com.ecommerce.logistics.api.dto.settlement.CarriageSettlementDTO;
import com.ecommerce.logistics.api.dto.settlement.UserSettlementAmountDTO;
import com.ecommerce.logistics.api.dto.settlement.UserSettlementListDTO;
import com.ecommerce.logistics.api.dto.settlement.UserSettlementListQueryDTO;
import com.ecommerce.logistics.biz.ISettlementBizService;
import com.ecommerce.logistics.service.ISettlementService;
import com.ecommerce.logistics.util.LogisticsUtils;
import com.ecommerce.member.api.dto.account.AccountDTO;
import com.ecommerce.member.api.dto.member.MemberDTO;
import com.ecommerce.member.api.dto.member.MemberDetailDTO;
import com.ecommerce.member.api.service.IAccountService;
import com.ecommerce.member.api.service.IMemberService;
import org.apache.commons.collections.CollectionUtils;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: <EMAIL>
 * @Description:
 * @Date: Create in 上午11:07 18/8/15
 */
@Service("settlementService")
public class SettlementService implements ISettlementService {

    @Autowired
    private ISettlementBizService settlementBizService;

    @Autowired
    private IMemberService memberService;

    @Autowired
    private IAccountService accountService;

    @Override
    public ItemResult<PageData<SettlementListDTO>> querySettlementList(PageQuery<SettlementListQueryDTO> pageQuery) {
        PageData<SettlementListDTO> pageData = settlementBizService.querySettlementList(pageQuery);
        List<SettlementListDTO> settlementListList = pageData.getList();
        if (CollectionUtils.isNotEmpty(settlementListList)) {
            Map<String, SettlementListDTO> settlementMap = new HashMap<>();
            //获取承运商名称和联系电话
            for (SettlementListDTO settlementListDTO : settlementListList) {
                settlementMap.put(settlementListDTO.getCarrierId(), settlementListDTO);
            }
            List<MemberDTO> memberList = memberService.findMemberByIds(new ArrayList<>(settlementMap.keySet()));
            Map<String, String> idMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(memberList)) {
                memberList.stream().filter(memberDTO -> settlementMap.get(memberDTO.getMemberId()) != null).forEach(memberDTO -> {
                    //获取会员的主帐号列表
                    idMap.put(memberDTO.getMainAccountId(), memberDTO.getMemberId());
                    settlementMap.get(memberDTO.getMemberId()).setPhone(memberDTO.getContactPhone());
                    settlementMap.get(memberDTO.getMemberId()).setCarrierName(memberDTO.getMemberName());
                });
            }
            //获取账户信息
            List<AccountDTO> accountList = accountService.findByIds(new ArrayList<>(idMap.keySet()));
            if (CollectionUtils.isNotEmpty(accountList)) {
                accountList.stream().filter(accountDTO -> idMap.get(accountDTO.getAccountId()) != null &&
                        settlementMap.get(idMap.get(accountDTO.getAccountId())) != null).forEach(accountDTO -> {
                    SettlementListDTO settlementListDTO = settlementMap.get(idMap.get(accountDTO.getAccountId()));
                    //个人帐号
                    handleSettlementCarriageNameAndPhone(accountDTO, settlementListDTO, settlementMap);
                });
            }

            pageData.setList(new ArrayList<>(settlementMap.values()));
        }
        return new ItemResult<>(pageData);
    }

    private static void handleSettlementCarriageNameAndPhone(AccountDTO accountDTO, SettlementListDTO settlementListDTO, Map<String, SettlementListDTO> settlementMap) {
        if (accountDTO.getAccountType().equals(AccountDTO.ACCOUNT_TYPE_PERSONAL)) {
            settlementListDTO.setCarrierName(CsStringUtils.isEmpty(accountDTO.getRealName()) ? accountDTO.getAccountName() : accountDTO.getRealName());
        }
        //会员联系电话为空
        if (CsStringUtils.isEmpty(settlementMap.get(accountDTO.getMemberId()).getPhone())) {
            settlementListDTO.setPhone(accountDTO.getMobile());
        }
    }

    @Override
    public ItemResult<PageData<MonthSettlementListDTO>> queryMonthSettlementList(PageQuery<MonthSettlementListQueryDTO> pageQuery) {
        PageData<MonthSettlementListDTO> pageData = settlementBizService.queryMonthSettlementList(pageQuery);
        List<MonthSettlementListDTO> monthSettlementList = pageData.getList();
        if (CollectionUtils.isNotEmpty(monthSettlementList)) {
            //获取承运商名称和联系电话
            MemberDetailDTO memberDetailDTO = memberService.findMemberById(monthSettlementList.get(0).getCarrierId());
            String carrierName = "";
            String carrierPhone = "";
            if (memberDetailDTO != null) {
                //获取账户信息
                AccountDTO accountDTO = accountService.findById(memberDetailDTO.getMainAccountId());
                //个人帐号
                if (accountDTO.getAccountType().equals(AccountDTO.ACCOUNT_TYPE_PERSONAL)) {
                    carrierName = LogisticsUtils.defaultIfCondition(CsStringUtils.isEmpty(accountDTO.getRealName()), accountDTO.getAccountName(), accountDTO.getRealName());
                    carrierPhone = accountDTO.getMobile();
                } else {
                    carrierName = memberDetailDTO.getMemberName();
                    carrierPhone = CsStringUtils.isEmpty(memberDetailDTO.getContactPhone()) ? accountDTO.getMobile() :
                            memberDetailDTO.getContactPhone();
                }
            }
            for (MonthSettlementListDTO monthSettlementListDTO : monthSettlementList) {
                monthSettlementListDTO.setCarrierName(carrierName);
                monthSettlementListDTO.setPhone(carrierPhone);
            }
        }

        return new ItemResult<>(pageData);
    }

    @Override
    public ItemResult<PageData<SettlementItemListDTO>> querySettlementItemList(PageQuery<SettlementItemListQueryDTO> pageQuery) {
        return new ItemResult<>(settlementBizService.querySettlementItemList(pageQuery));
    }

    @Override
    public ItemResult<Void> userCarriageSettlement(CarriageSettlementDTO carriageSettlementDTO) {
        settlementBizService.userCarriageSettlement(carriageSettlementDTO);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> waybillCarriageSettlement(CarriageSettlementDTO carriageSettlementDTO) {
        settlementBizService.waybillCarriageSettlement(carriageSettlementDTO);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<UserSettlementAmountDTO> queryUserSettlementAmount(String userId) {
        return new ItemResult<>(settlementBizService.queryUserSettlementAmount(userId));
    }

    @Override
    public ItemResult<PageData<UserSettlementListDTO>> queryUserSettlementList(PageQuery<UserSettlementListQueryDTO> pageQuery) {
        return new ItemResult<>(settlementBizService.queryUserSettlementList(pageQuery));
    }
}
