package com.ecommerce.logistics.service.impl;

import com.alibaba.fastjson.JSON;
import com.ecommerce.base.api.dto.ReceivingAddressMapQueryDTO;
import com.ecommerce.base.api.dto.warehouse.WarehouseDetailsDTO;
import com.ecommerce.base.api.dto.warehouse.WarehouseDetailsReqDTO;
import com.ecommerce.base.api.dto.wharf.ReceivingAddrMapWharfDTO;
import com.ecommerce.base.api.dto.wharf.WharfMaPQueryDTO;
import com.ecommerce.base.api.dto.wharf.WharfMapListDTO;
import com.ecommerce.base.api.service.IReceivingAddressService;
import com.ecommerce.base.api.service.IWarehouseService;
import com.ecommerce.base.api.service.IWharfService;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.exception.CodeMeta;
import com.ecommerce.common.exception.DistributeLockException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.service.ILockService;
import com.ecommerce.common.utils.ArithUtils;
import com.ecommerce.common.utils.DateUtil;
import com.ecommerce.logistics.LogisticsCommonBean;
import com.ecommerce.logistics.api.constant.StringConstant;
import com.ecommerce.logistics.api.dto.DeliveryInfoDTO;
import com.ecommerce.logistics.api.dto.externalexception.ExternalExceptionSaveDTO;
import com.ecommerce.logistics.api.dto.operationrecord.OperationRecordAddDTO;
import com.ecommerce.logistics.api.dto.productinfo.ProductDetailDTO;
import com.ecommerce.logistics.api.dto.shipbill.ShipBillCloseERPCallbackDTO;
import com.ecommerce.logistics.api.dto.shipbill.ShipBillDetailDTO;
import com.ecommerce.logistics.api.dto.shipbill.ShipBillERPCallbackDTO;
import com.ecommerce.logistics.api.dto.shipbill.ShipBillModifyERPCallbackDTO;
import com.ecommerce.logistics.api.dto.waybill.*;
import com.ecommerce.logistics.api.enums.*;
import com.ecommerce.logistics.biz.*;
import com.ecommerce.logistics.biz.delayed.RetryErpTaskManager;
import com.ecommerce.logistics.biz.delayed.TaskMessage;
import com.ecommerce.logistics.biz.delivery.IDeliveryInfoBizService;
import com.ecommerce.logistics.dao.mapper.ErpShippingMapMapper;
import com.ecommerce.logistics.dao.mapper.ShipBillItemMapper;
import com.ecommerce.logistics.dao.mapper.ShipBillMapper;
import com.ecommerce.logistics.dao.vo.ErpShippingMap;
import com.ecommerce.logistics.dao.vo.ExternalExceptionHandle;
import com.ecommerce.logistics.dao.vo.ShipBill;
import com.ecommerce.logistics.dao.vo.ShipBillItem;
import com.ecommerce.logistics.exception.LogisticsErrorCode;
import com.ecommerce.logistics.service.IShipBillExternalService;
import com.ecommerce.member.api.dto.carrier.BindingStatusEnum;
import com.ecommerce.member.api.dto.carrier.CarrierBindingConditionQueryDTO;
import com.ecommerce.member.api.dto.carrier.CarrierBindingListDTO;
import com.ecommerce.member.api.service.ICarrierBindingService;
import com.ecommerce.open.api.dto.apicenter.ApiResult;
import com.ecommerce.open.api.dto.apicenter.logistic.ECNotifyOpenCabinRequestDTO;
import com.ecommerce.open.api.dto.apicenter.logistic.ECShipBillCloseDTO;
import com.ecommerce.open.api.dto.apicenter.logistic.ECShipBillDTO;
import com.ecommerce.open.api.dto.apicenter.logistic.ECShipBillModifyDTO;
import com.ecommerce.open.api.service.apicenter.IOpenAPIInvokeService;
import com.ecommerce.open.enums.BizCodeEnum;
import com.ecommerce.open.enums.DefaultErpCodeEnum;
import com.ecommerce.order.api.dto.erp.ShipBillERPInfoDTO;
import com.ecommerce.order.api.dto.logistics.OverSendOutVerifyDTO;
import com.ecommerce.order.api.dto.logistics.OverSendOutVerifyResponseDTO;
import com.ecommerce.order.api.enums.OrderExceptionStatusEnum;
import com.ecommerce.order.api.service.ITakeInfoService;
import com.ecommerce.order.api.service.ITakeUpDataService;
import com.ecommerce.pay.api.v2.enums.ChannelCodeEnum;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @Description: ShipBillExternalService
 * @Author: <EMAIL>
 * @Date: 01/02/2021 14:42
 */
@Slf4j
@Service("shipBillExternalService")
public class ShipBillExternalService implements IShipBillExternalService {

    public static final String ERP_OPERATE_USER_ID = "erp_system";
    public static final String ERP_OPERATE_USER_NAME = "erp_system";
    private static final String SHIP_BILL_STATUS_EXCEPTION = "运单状态异常";

    @Value("${waybill.exception.retryCount:3}")
    private int retryCount;

    @Autowired
    @Qualifier("bizRedisTemplate")
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired(required = false)
    private ShipBillMapper shipBillMapper;

    @Autowired(required = false)
    private ShipBillItemMapper shipBillItemMapper;

    @Autowired(required = false)
    private ErpShippingMapMapper erpShippingMapMapper;

    @Autowired
    private ILockService redisLockService;

    @Autowired
    private IShipBillQueryBizService shipBillQueryBizService;

    @Autowired
    private IOperationRecordBizService operationRecordBizService;

    @Autowired
    private IDeliveryInfoBizService deliveryInfoBizService;

    @Autowired
    private ITakeUpDataService takeUpDataService;

    @Autowired
    private IOpenAPIInvokeService openAPIInvokeService;

    @Autowired
    private IWharfService wharfService;

    @Autowired
    private IWarehouseService warehouseService;

    @Autowired
    private ITakeInfoService takeInfoService;

    @Autowired
    private IShipBillOperateBizService shipBillOperateBizService;

    @Autowired
    private IProxySyncRecordBizService proxySyncRecordBizService;

    @Autowired
    private ICarrierBindingService carrierBindingService;

    @Autowired
    private IReceivingAddressService receivingAddressService;

    @Autowired
    private IShipBillLeaveWarehouseBizService shipBillLeaveWarehouseBizService;

    @Autowired
    private IExternalExceptionBizService externalExceptionBizService;

    @Autowired
    private RetryErpTaskManager retryErpTaskManager;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ItemResult<Void> updateECShipBill(ERPShipBillDTO erpShipBillDTO) {
        log.info("updateECShipBill:" + JSON.toJSONString(erpShipBillDTO));
        if (CsStringUtils.isEmpty(erpShipBillDTO.getWaybillNum()) || CsStringUtils.isEmpty(erpShipBillDTO.getExternalWaybillStatus())) {
            throw new BizException(LogisticsErrorCode.DATA_EXCEPTION, "erp调用数据异常");
        }
        ShipBill shipBill = shipBillQueryBizService.queryWaybillByWaybillNum(erpShipBillDTO.getWaybillNum());
        String identifier = "";
        try {
            //获取锁,获取失败则等待,获取成功则修改
            identifier = redisLockService.lock(shipBill.getWaybillId());
            ShipBill newShipBill = new ShipBill();

            newShipBill.setWaybillId(shipBill.getWaybillId());
            newShipBill.setExternalRequestNo(erpShipBillDTO.getExternalRequestNo());
            newShipBill.setExternalWaybillId(erpShipBillDTO.getExternalWaybillId());

            OperationRecordAddDTO operationRecordAddDTO = new OperationRecordAddDTO();
            operationRecordAddDTO.setEntryId(shipBill.getWaybillId());
            operationRecordAddDTO.setType(OperationRecordTypeEnum.WAYBILL.getCode());
            operationRecordAddDTO.setOperatorId(ERP_OPERATE_USER_ID);
            operationRecordAddDTO.setOperatorName(ERP_OPERATE_USER_NAME);
            operationRecordAddDTO.setCreateTime(erpShipBillDTO.getUpdateTime());

            switch (erpShipBillDTO.getExternalWaybillStatus()) {//00：已匹配计划、01：已调度、02：开始装船、03：完成装船
                case "00":
                    //修改运单信息
                    newShipBill.setExternalWaybillStatus(WaybillExternalStatusEnum.MATCH_PLAN.getCode());

                    //添加日志
                    operationRecordAddDTO.setOperationType(WaybillOperationTypeEnum.MATCH_PLAN.getCode());
                    operationRecordAddDTO.setContent("erp已匹配计划");
                    break;
                case "01":
                    //修改运单信息
                    newShipBill.setExternalWaybillStatus(WaybillExternalStatusEnum.ERP_DISPATCH.getCode());

                    //添加日志
                    operationRecordAddDTO.setOperationType(WaybillOperationTypeEnum.ERP_DISPATCH.getCode());
                    operationRecordAddDTO.setContent("erp已调度；装船泊位：" + erpShipBillDTO.getBerthCode());
                    break;
                case "02":
                    //修改运单信息
                    newShipBill.setExternalWaybillStatus(WaybillExternalStatusEnum.START_SHIPMENT.getCode());
                    newShipBill.setExternalWaybillNum(erpShipBillDTO.getExternalWaybillNum());

                    //添加日志
                    operationRecordAddDTO.setOperationType(WaybillOperationTypeEnum.START_SHIPMENT.getCode());
                    operationRecordAddDTO.setContent("erp开始装船；开始装船时间：" +  DateUtil.format(erpShipBillDTO.getBeginDate(), StringConstant.YYYY_MM_DD_HH_MM_SS));
                    break;
                case "03":
                    //修改运单信息  完成装船即表示扣款成功 需要改变运单状态
                    newShipBill.setExternalWaybillStatus(WaybillExternalStatusEnum.COMPLETE_SHIPMENT.getCode());

                    ShipBillDetailDTO shipBillDetailDTO = shipBillMapper.selectShipBillDetailByWaybillId(shipBill.getWaybillId());

                    //查询仓库信息
                    WarehouseDetailsReqDTO warehouseDetailsReqDTO = new WarehouseDetailsReqDTO();
                    warehouseDetailsReqDTO.setUserId(shipBillDetailDTO.getSellerId());
                    warehouseDetailsReqDTO.setWarehouseId(shipBillDetailDTO.getWarehouseId());
                    WarehouseDetailsDTO warehouseDetailsDTO = warehouseService.queryWarehouseDetailsByReq(warehouseDetailsReqDTO);
                    if(warehouseDetailsDTO == null){
                        throw new BizException(BasicCode.UNDEFINED_ERROR, "未找到对应仓库");
                    }

                    //erp出站 调用船运出站方法
                    LeaveWarehouseDTO leaveWarehouseDTO = new LeaveWarehouseDTO();
                    leaveWarehouseDTO.setWaybillId(shipBill.getWaybillId());
                    leaveWarehouseDTO.setWaybillItemId(shipBillDetailDTO.getWaybillItemId());
                    leaveWarehouseDTO.setTransportToolType(shipBill.getTransportToolType());
                    leaveWarehouseDTO.setActualQuantity(new BigDecimal(erpShipBillDTO.getShipWeight()));
                    leaveWarehouseDTO.setExternalFlag(1);
                    leaveWarehouseDTO.setCentralWarehouseFlag(CsStringUtils.equals(warehouseDetailsDTO.getType(), WarehouseTypeEnum.CENTRAL_WAREHOUSE.getCode()) ? 1 : 0);
                    leaveWarehouseDTO.setOriginPlace(shipBill.getOriginPlace());
                    leaveWarehouseDTO.setFromWebFlag((byte)0);
                    leaveWarehouseDTO.setUserId(ERP_OPERATE_USER_ID);
                    leaveWarehouseDTO.setUserName(ERP_OPERATE_USER_NAME);
                    leaveWarehouseDTO.setLeaveWarehouseTime(DateUtil.format(erpShipBillDTO.getEndDate(), StringConstant.YYYY_MM_DD_HH_MM_SS));
                    shipBillLeaveWarehouseBizService.ecShipLeaveWarehouse(leaveWarehouseDTO);

                    //添加日志
                    operationRecordAddDTO.setOperationType(WaybillOperationTypeEnum.COMPLETE_SHIPMENT.getCode());
                    operationRecordAddDTO.setContent("erp完成装船；装船数量：" + erpShipBillDTO.getShipWeight() + "；封签号：" + erpShipBillDTO.getSealNumber() + "；完成装船时间：" + DateUtil.format(erpShipBillDTO.getEndDate(), StringConstant.YYYY_MM_DD_HH_MM_SS));
                    break;
                default:
                    break;
            }
            newShipBill.setUpdateTime(new Date());
            newShipBill.setUpdateUser(ERP_OPERATE_USER_NAME);
            shipBillMapper.updateByPrimaryKeySelective(newShipBill);
            operationRecordBizService.saveOperationRecord(operationRecordAddDTO, operationRecordAddDTO.getCreateTime());
            return new ItemResult<>(null);
        } catch (DistributeLockException e) {
            log.info("ERP上传船运单调度信息,重复操作: waybillNum={}", erpShipBillDTO.getWaybillNum());
            throw new BizException(BasicCode.INVALID_PARAM, "重复操作：erpShipBillDTO=" + erpShipBillDTO);
        } finally {
            if (CsStringUtils.isNotEmpty(identifier)) {
                redisLockService.unlock(shipBill.getWaybillId(), identifier);
            }
        }
    }

    @Override
    public ItemResult<ERPShipBillDTO> erpNotifyPay(ERPShipBillDTO erpShipBillDTO) {
        log.info("erpNotifyPay:" + JSON.toJSONString(erpShipBillDTO));
        if (CsStringUtils.isEmpty(erpShipBillDTO.getWaybillNum()) || CsStringUtils.isEmpty(erpShipBillDTO.getExternalWaybillId())) {
            throw new BizException(LogisticsErrorCode.DATA_EXCEPTION, "erp调用数据异常");
        }
        ShipBill shipBill = shipBillQueryBizService.queryWaybillByWaybillNum(erpShipBillDTO.getWaybillNum());
        String identifier = "";
        try {
            //获取锁,获取失败则等待,获取成功则修改
            identifier = redisLockService.lock(shipBill.getWaybillId());

            ShipBillItem billItem = shipBillItemMapper.selectErpShipBillItem(shipBill.getWaybillId());
            DeliveryInfoDTO deliveryInfoDTO = deliveryInfoBizService.queryByInfoId(billItem.getDeliveryInfoId());

            OverSendOutVerifyDTO overSendOutVerifyDTO = new OverSendOutVerifyDTO();
            overSendOutVerifyDTO.setTakeCode(deliveryInfoDTO.getTakeCode());
            overSendOutVerifyDTO.setWaybillNum(shipBill.getWaybillNum() + "#" + billItem.getWaybillItemId() + "#" + shipBill.getTransportToolType());
            overSendOutVerifyDTO.setOrderItemId(deliveryInfoDTO.getOrderItemId());
            overSendOutVerifyDTO.setQuantity(shipBill.getAssignQuantity());
            overSendOutVerifyDTO.setActualQuantity(new BigDecimal(erpShipBillDTO.getShipWeight()));
            overSendOutVerifyDTO.setOperator(ERP_OPERATE_USER_ID);
            overSendOutVerifyDTO.setLogisticsAmount(billItem.getReceivableCarriagePrice()
                    .multiply(new BigDecimal(erpShipBillDTO.getShipWeight())));
            overSendOutVerifyDTO.setLogisticsPrice(billItem.getReceivableCarriagePrice());
            overSendOutVerifyDTO.setReturnPayInfoWay(true);

            //1.商品单价, 2.是否结算校验成功
            ItemResult<OverSendOutVerifyResponseDTO> verifyResult = takeUpDataService.overSendOutVerify(overSendOutVerifyDTO);
            if (!verifyResult.isSuccess() || verifyResult.getData() == null) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "调用交易超发验证接口异常:" + verifyResult.getDescription());
            }
            OverSendOutVerifyResponseDTO verifyDTO = verifyResult.getData();
            ERPShipBillDTO result = new ERPShipBillDTO();
            if (CsStringUtils.equals(ChannelCodeEnum.ERP.getCode(), verifyDTO.getPayinfoWay())) {
                result.setDeductResult("00");//00：ERP发起  01：扣款成功  02：扣款失败
            } else if (BooleanUtils.isTrue(verifyDTO.getCanOut())) {
                result.setDeductResult("01");
            } else {
                result.setDeductResult("02");
            }
            result.setGoodsPrice(verifyDTO.getPrice().setScale(2, RoundingMode.HALF_UP).toString());
            result.setLogisticsPrice(billItem.getReceivableCarriagePrice().setScale(2, RoundingMode.HALF_UP).toString());
            log.info("返回结果:{}", JSON.toJSONString(result));

            //更新商品单价
            billItem.setGoodsPrice(verifyDTO.getPrice().setScale(2, RoundingMode.HALF_UP));
            billItem.setUpdateTime(new Date());
            billItem.setUpdateUser(ERP_OPERATE_USER_ID);
            shipBillItemMapper.updateByPrimaryKeySelective(billItem);

            return new ItemResult<>(result);
        } catch (DistributeLockException e) {
            log.info("ERP通知电商扣款,重复操作: waybillNum={}", erpShipBillDTO.getWaybillNum());
            throw new BizException(BasicCode.INVALID_PARAM, "重复操作：erpShipBillDTO=" + erpShipBillDTO);
        } finally {
            if (CsStringUtils.isNotEmpty(identifier)) {
                redisLockService.unlock(shipBill.getWaybillId(), identifier);
            }
        }
    }

    @Override
    public ItemResult<Void> notifyOpenCabin(String waybillId) {
        log.info("notifyOpenCabin:{}",waybillId);
        ShipBillDetailDTO shipBillDetailDTO = shipBillQueryBizService.selectShipBillDetailByWaybillId(waybillId);
        if (shipBillDetailDTO == null) {
            throw new BizException(BasicCode.DATA_NOT_EXIST, "运单信息");
        }
        try {
            ShipBillERPInfoDTO shipBillERPInfoDTO = takeInfoService.findShipBillERPInfoByTakeCode(shipBillDetailDTO.getTakeCode(), shipBillDetailDTO.getDealsId());
            log.info("查到的船运单ERP相关信息:{}",JSON.toJSONString(shipBillDetailDTO));
            ProductDetailDTO productDetailDTO = shipBillDetailDTO.getProductDetails().get(0);

            ECNotifyOpenCabinRequestDTO ecNotifyOpenCabinRequestDTO = new ECNotifyOpenCabinRequestDTO();
            ecNotifyOpenCabinRequestDTO.setEcBillCode(shipBillDetailDTO.getWaybillNum());
            ecNotifyOpenCabinRequestDTO.setExternalBillId(shipBillDetailDTO.getExternalWaybillId());
            ecNotifyOpenCabinRequestDTO.setExternalBillNo(shipBillDetailDTO.getExternalWaybillNum());
            ecNotifyOpenCabinRequestDTO.setGoodsPrice(shipBillDetailDTO.getPrice().setScale(2,RoundingMode.HALF_UP).toString());
            ecNotifyOpenCabinRequestDTO.setLogisticsPrice(shipBillDetailDTO.getCarriagePrice().setScale(2,RoundingMode.HALF_UP).toString());

            //查询卸货码头
            if (CsStringUtils.equals(shipBillDetailDTO.getBillProxyType(), BillProxyTypeEnum.NORMAL.getCode())) {
                WharfMaPQueryDTO wharfMaPQueryDTO = new WharfMaPQueryDTO();
                wharfMaPQueryDTO.setMemberId(productDetailDTO.getBuyerId());
                wharfMaPQueryDTO.setEcWharfName(shipBillDetailDTO.getUnloadPortName());
                wharfMaPQueryDTO.setErpMemberId(productDetailDTO.getSellerId());
                handleWharfMapQuery(shipBillDetailDTO, wharfMaPQueryDTO);
                List<WharfMapListDTO> wharfMapListDTOList = wharfService.queryWharfMapList(wharfMaPQueryDTO).getData();
                log.info("wharfErpInfoListDTOS: {}", JSON.toJSONString(wharfMapListDTOList));
                if (CollectionUtils.isNotEmpty(wharfMapListDTOList) && CsStringUtils.equals(wharfMapListDTOList.get(0).getErpMemberId(), productDetailDTO.getSellerId())) {
                    ecNotifyOpenCabinRequestDTO.setUnloadingWharf(wharfMapListDTOList.get(0).getErpWharfCode());
                }
            } else {
                ReceivingAddressMapQueryDTO addressMapQueryDTO = new ReceivingAddressMapQueryDTO();
                addressMapQueryDTO.setAddressId(productDetailDTO.getReceiveAddressId());
                addressMapQueryDTO.setSellerId(productDetailDTO.getBuyerId());
                addressMapQueryDTO.setFirmId(productDetailDTO.getSellerId());
                List<ReceivingAddrMapWharfDTO> mapWharfDTOS = receivingAddressService.queryErpWharfList(addressMapQueryDTO);
                log.info("mapWharfDTOS: {}", JSON.toJSONString(mapWharfDTOS));
                if (CollectionUtils.isNotEmpty(mapWharfDTOS)) {
                    ecNotifyOpenCabinRequestDTO.setUnloadingWharf(mapWharfDTOS.get(0).getErpWharfCode());
                }
            }
            //设置内部调拨标识
            ecNotifyOpenCabinRequestDTO.setInternal(shipBillDetailDTO.getInternal());

            //设置实际ERP合同编号
            if(shipBillERPInfoDTO != null && shipBillERPInfoDTO.getContractDTO() != null){
                ecNotifyOpenCabinRequestDTO.setContractCode(shipBillERPInfoDTO.getContractDTO().getErpContractNum());
            }

            //设置结算方式
            handleSettlementWay(shipBillERPInfoDTO, ecNotifyOpenCabinRequestDTO);

            ApiResult result = openAPIInvokeService.invoke(BizCodeEnum.EC_SHP_G0.getCode(),productDetailDTO.getSellerId(), JSON.toJSONString(ecNotifyOpenCabinRequestDTO));
            log.info("open返回结果:" + result.getData());
            if (!result.isSuccess()) {
                throw new BizException(LogisticsErrorCode.EXTERNAL_EXCEPTION, "调用ERP开单接口失败：" + result.getDescription());
            }

        } catch (Exception e) {
            log.error("接口内部异常:" + e);
            String description = "接口内部异常";
            CodeMeta errorCode = BasicCode.UNKNOWN_ERROR;
            if (e instanceof BizException) {
                BizException be = (BizException)e;
                errorCode = be.getErrorCode();
                description = be.getMessage();
            }
            ItemResult itemResult = new ItemResult<>(errorCode.getCode(), description);
            ShipBill shipBill = new ShipBill();
            shipBill.setWaybillId(shipBillDetailDTO.getWaybillId());
            shipBill.setWaybillNum(shipBillDetailDTO.getWaybillNum());
            externalExceptionHandle(itemResult, shipBill, ExternalMethodTypeEnum.ERP_SHIPBILL_OPENCABIN.getCode());
        }
        return new ItemResult<>(null);
    }

    private static void handleSettlementWay(ShipBillERPInfoDTO shipBillERPInfoDTO, ECNotifyOpenCabinRequestDTO ecNotifyOpenCabinRequestDTO) {
        if(shipBillERPInfoDTO != null && shipBillERPInfoDTO.getOrderPayinfoDTO() != null){
            // 结算方式
            String settlementWay = shipBillERPInfoDTO.getOrderPayinfoDTO().getPayinfoWay();
            log.info("结算方式 : {}", settlementWay);
            if (CsStringUtils.equals(settlementWay, ChannelCodeEnum.ERP.getCode()) || CsStringUtils.isBlank(settlementWay)) {
                ecNotifyOpenCabinRequestDTO.setSettlementWay("01");
            } else {
                ecNotifyOpenCabinRequestDTO.setSettlementWay("00");
            }
        }
    }

    private static void handleWharfMapQuery(ShipBillDetailDTO shipBillDetailDTO, WharfMaPQueryDTO wharfMaPQueryDTO) {
        if (CsStringUtils.equals("01", shipBillDetailDTO.getInternal())) {
            //内部调拨特殊处理
            wharfMaPQueryDTO.setEcWharfId(shipBillDetailDTO.getUnloadPortId());
        } else {
            wharfMaPQueryDTO.setType("0");
        }
    }

    @Override
    public ItemResult<Void> openCabinCallback(ItemResult<ERPShipBillDTO> itemResult) {
        log.info("openCabinCallback:" + JSON.toJSONString(itemResult));
        if (itemResult == null || itemResult.getData() == null){
            return new ItemResult<>(null);
        }
        ShipBill shipBill = shipBillQueryBizService.queryWaybillByWaybillNum(itemResult.getData().getWaybillNum());
        ShipBill newShipBill = new ShipBill();
        newShipBill.setWaybillId(shipBill.getWaybillId());

        if (itemResult.isSuccess()) {
            if(BooleanUtils.isTrue(itemResult.getData().getOpenReult())){
                //成功
                ShipBillDetailDTO shipBillDetailDTO = shipBillQueryBizService.selectShipBillDetailByWaybillId(shipBill.getWaybillId());
                ShipBillItem billItem = shipBillItemMapper.selectByPrimaryKey(shipBillDetailDTO.getWaybillItemId());
                DeliveryInfoDTO deliveryInfoDTO = deliveryInfoBizService.queryByInfoId(billItem.getDeliveryInfoId());

                //转移货权
                shipBillMapper.normalChangeOwnerId(shipBillDetailDTO.getWaybillItemId(), ERP_OPERATE_USER_ID);
                //状态修改
                List<String> oldStatusList = Lists.newArrayList(
                        ShipBillItemStatusEnum.LEAVE_WAREHOUSE.getCode(),
                        ShipBillItemStatusEnum.CABIN_WAIT_SUPPLEMENT.getCode()
                );
                shipBillItemMapper.updateWaybillItemStatus(
                        shipBillDetailDTO.getWaybillItemId(),
                        ShipBillItemStatusEnum.OPEN_CABIN.getCode(),
                        oldStatusList,
                        ERP_OPERATE_USER_ID
                );

                shipBillOperateBizService.itemSendQuantityToOrder(
                        shipBill.getWaybillNum(), shipBillDetailDTO.getWaybillItemId(), shipBill.getTransportToolType(),
                        ERP_OPERATE_USER_ID, deliveryInfoDTO);

                newShipBill.setExternalWaybillStatus(WaybillExternalStatusEnum.OPEN_CABIN_SUCCESS.getCode());
                newShipBill.setSyncFailReason("");

                if (CsStringUtils.equals(billItem.getBillProxyType(), BillProxyTypeEnum.PRIMARY.getCode())) {
                    //同步尝试开仓另一级
                    OpenCabinShipBillDTO relationOpenCabinShipBillDTO = new OpenCabinShipBillDTO();
                    relationOpenCabinShipBillDTO.setWaybillId(shipBill.getWaybillId());
                    relationOpenCabinShipBillDTO.setWaybillItemId(billItem.getWaybillItemId());
                    relationOpenCabinShipBillDTO.setOperationUserId(ERP_OPERATE_USER_ID);
                    relationOpenCabinShipBillDTO.setOperationUserName(ERP_OPERATE_USER_NAME);
                    relationOpenCabinShipBillDTO.setOpenCabinTime(new Date());
                    String relationItemId = shipBillItemMapper.selectItemIdByWaybillProxy(
                            shipBill.getWaybillId(),
                            BillProxyTypeEnum.SECONDARY.getCode(),
                            shipBill.getMaxSeqNum());
                    relationOpenCabinShipBillDTO.setWaybillItemId(relationItemId);
                    proxySyncRecordBizService.syncOpenCabinWaybill(relationItemId, relationOpenCabinShipBillDTO);
                }
            }else {
                newShipBill.setExternalWaybillStatus(WaybillExternalStatusEnum.OPEN_CABIN_FAIL.getCode());
                newShipBill.setSyncFailReason(itemResult.getDescription());

                //开仓失败需要把ShipBillItem的结算总金额字段设置为零
                ShipBillItem shipBillItem = shipBillItemMapper.selectErpShipBillItem(shipBill.getWaybillId());
                ShipBillItem newShipBillItem = new ShipBillItem();
                newShipBillItem.setWaybillItemId(shipBillItem.getWaybillItemId());
                newShipBillItem.setSettlementAmount(BigDecimal.ZERO);
                shipBillItemMapper.updateByPrimaryKeySelective(newShipBillItem);
            }
        }else {
            newShipBill.setExternalWaybillStatus(WaybillExternalStatusEnum.OPEN_CABIN_FAIL.getCode());
            newShipBill.setSyncFailReason(itemResult.getData().getFailedMessage());

            //开仓失败需要把ShipBillItem的结算总金额字段设置为零
            ShipBillItem shipBillItem = shipBillItemMapper.selectErpShipBillItem(shipBill.getWaybillId());
            ShipBillItem newShipBillItem = new ShipBillItem();
            newShipBillItem.setWaybillItemId(shipBillItem.getWaybillItemId());
            newShipBillItem.setSettlementAmount(BigDecimal.ZERO);
            shipBillItemMapper.updateByPrimaryKeySelective(newShipBillItem);
        }
        newShipBill.setUpdateTime(new Date());
        shipBillMapper.updateByPrimaryKeySelective(newShipBill);
        externalExceptionHandle(itemResult, shipBill, ExternalMethodTypeEnum.ERP_SHIPBILL_OPENCABIN.getCode());

        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> waybillRetryHandler(WaybillRetryHandlerDTO waybillRetryHandlerDTO) {
        log.info("waybillRetryHandler:{}",JSON.toJSONString(waybillRetryHandlerDTO));
        //获取当前运单外部状态
        ShipBill shipBill = shipBillQueryBizService.selectShipBillById(waybillRetryHandlerDTO.getWaybillId());
        if (shipBill == null) {
            throw new BizException(BasicCode.DATA_NOT_EXIST, "运单");
        }
        WaybillExternalStatusEnum waybillExternalStatusEnum = WaybillExternalStatusEnum.valueOfCode(shipBill.getExternalWaybillStatus());
        if (waybillExternalStatusEnum == null) {
            throw new BizException(LogisticsErrorCode.DATA_EXCEPTION, "无效的外部运单状态");
        }
        ExternalWaybillRequestDTO externalWaybillRequestDTO = new ExternalWaybillRequestDTO();
        BeanUtils.copyProperties(waybillRetryHandlerDTO, externalWaybillRequestDTO);
        switch (waybillExternalStatusEnum) {
            case ERP_CREATING:
            case ERP_CREATE_FAIL:
                this.createExternalShipBill(externalWaybillRequestDTO);
                break;
            case ERP_CHANGING:
                this.modifyExternalShipBill(externalWaybillRequestDTO);
                break;
            case ERP_CLOSING:
                this.closeExternalShipBill(externalWaybillRequestDTO);
                break;
            case OPEN_CABIN_ING:
            case OPEN_CABIN_FAIL:
                this.notifyOpenCabin(externalWaybillRequestDTO.getWaybillId());
                break;
            default:
                break;
        }

        return new ItemResult<>(null);
    }

    @Override
    public void createExternalShipBill(ExternalWaybillRequestDTO requestDTO) {
        ShipBill shipBill = shipBillQueryBizService.selectShipBillById(requestDTO.getWaybillId());
        ShipBill failShipBill = null;
        try {
            ShipBillDetailDTO shipBillDetailDTO = shipBillQueryBizService.selectShipBillDetailByWaybillId(requestDTO.getWaybillId());
            log.info("createExternalShipBill : {}", JSON.toJSONString(shipBillDetailDTO));
            if (shipBillDetailDTO == null) {
                throw new BizException(BasicCode.DATA_NOT_EXIST, "运单信息");
            }

            ECShipBillDTO ecShipBillDTO = new ECShipBillDTO();
            ecShipBillDTO.setOperator(requestDTO.getOperatorUserId());
            ecShipBillDTO.setOperatorName(requestDTO.getOperatorUserName());
            ecShipBillDTO.setEcBillCode(shipBillDetailDTO.getWaybillNum());
            ecShipBillDTO.setMdmCode(shipBillDetailDTO.getMdmCode());
            ecShipBillDTO.setShipName(shipBillDetailDTO.getShippingName());
            ecShipBillDTO.setPlanBillWeight(shipBillDetailDTO.getQuantity().toString());
            ecShipBillDTO.setPrice(shipBillDetailDTO.getPrice().toString());
            //设置内部调拨单据
            ecShipBillDTO.setInternal(shipBillDetailDTO.getInternal());

            ShipBillERPInfoDTO shipBillERPInfoDTO = takeInfoService.findShipBillERPInfoByTakeCode(shipBillDetailDTO.getTakeCode(), shipBillDetailDTO.getDealsId());
            log.info("findShipBillERPInfoByTakeCode : {}", JSON.toJSONString(shipBillERPInfoDTO));

            handleEcShipBill(shipBillERPInfoDTO, ecShipBillDTO);
            Date deliveryTime = new Date();
            for (ProductDetailDTO productDetailDTO : shipBillDetailDTO.getProductDetails()) {
                ecShipBillDTO.setSellerId(productDetailDTO.getSellerId());
                // ERP商品物料编码
                ecShipBillDTO.setCommodityCode(productDetailDTO.getCommodityCode());
                // 电商提货点关联的ERP码头
                WharfMaPQueryDTO wharfMaPQueryDTO = new WharfMaPQueryDTO();
                wharfMaPQueryDTO.setMemberId(productDetailDTO.getSellerId());
                wharfMaPQueryDTO.setEcWharfName(shipBillDetailDTO.getLoadPortName());

                handleWharf(shipBillDetailDTO, wharfMaPQueryDTO);

                handleShipAndWharf(productDetailDTO, wharfMaPQueryDTO, ecShipBillDTO, shipBillDetailDTO);

                String deliveryTimeStr;
                DeliveryTimeRangeEnum deliveryTimeRangeEnum = DeliveryTimeRangeEnum.valueOfCode(productDetailDTO.getDeliveryTimeRange());
                if (deliveryTimeRangeEnum != null) {
                    deliveryTimeStr = productDetailDTO.getDeliveryTime() + " " + deliveryTimeRangeEnum.getDefaultTime();
                } else {
                    deliveryTimeStr = productDetailDTO.getDeliveryTime() + " 00:00:00";
                }
                log.info("deliveryTimeStr : {}", deliveryTimeStr);
                SimpleDateFormat dateFormat = new SimpleDateFormat(StringConstant.YYYY_MM_DD_HH_MM_SS);
                deliveryTime = dateFormat.parse(deliveryTimeStr);
            }
            SimpleDateFormat sdf = new SimpleDateFormat(StringConstant.YYYY_MM_DD_HH_MM_SS);
            // 卖家配送
            if (CsStringUtils.equals(shipBillDetailDTO.getItemType(), PickingBillTypeEnum.SELLER_DELIVERY.getCode())) {
                // 配送只支持到位价
                ecShipBillDTO.setPrice(ArithUtils.add(shipBillDetailDTO.getPrice(), shipBillDetailDTO.getCarriagePrice()).toString());
                ecShipBillDTO.setShippingMethod("02");
                ErpShippingMap erpShippingQueryCondition = new ErpShippingMap();
                erpShippingQueryCondition.setEcMemberId(shipBillDetailDTO.getSellerId());
                erpShippingQueryCondition.setEcShippingId(shipBillDetailDTO.getShippingId());

                handleErpShippingQueryCondition(shipBillDetailDTO, ecShipBillDTO, erpShippingQueryCondition);

                ErpShippingMap erpShippingMap = erpShippingMapMapper.findErpShippingMapByCondition(erpShippingQueryCondition);
                log.info("erpShippingMap : {}", JSON.toJSONString(erpShippingMap));
                if (erpShippingMap != null) {
                    // ERP船舶名称
                    ecShipBillDTO.setShipName(erpShippingMap.getErpShippingName());
                    // ERP船舶编号
                    ecShipBillDTO.setShipCode(erpShippingMap.getErpShippingCode());
                    // ERP承运商编号
                    ecShipBillDTO.setErpCarrierNo(erpShippingMap.getErpCarrierNo());
                }
                // 预计到达日期
                ecShipBillDTO.setEstimatedTime(shipBillDetailDTO.getPlanArriveTime());
                // 计划装货日期
                ecShipBillDTO.setLoadDate(shipBillDetailDTO.getPlanArriveTime());
                // 计划送达日期
                ecShipBillDTO.setUnloadDate(sdf.format(deliveryTime));
            } else {
                ecShipBillDTO.setShippingMethod("01");
                // 计划开始日期
                ecShipBillDTO.setDateFrom(sdf.format(deliveryTime));
                // 计划失效日期
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(deliveryTime);
                calendar.add(Calendar.DATE, 5);
                ecShipBillDTO.setDateTo(sdf.format(calendar.getTime()));
            }

            ShipBill needUpdateDBShipBill = new ShipBill();
            needUpdateDBShipBill.setWaybillId(shipBillDetailDTO.getWaybillId());
            needUpdateDBShipBill.setSyncFlag(ExternalSyncFlagEnum.FORWARD_SYNC.getCode());
            needUpdateDBShipBill.setExternalWaybillStatus(WaybillExternalStatusEnum.ERP_CREATING.getCode());
            needUpdateDBShipBill.setUpdateTime(new Date());
            failShipBill = needUpdateDBShipBill;
            shipBillMapper.updateByPrimaryKeySelective(needUpdateDBShipBill);

            log.info("ecShipBillDTO={}",ecShipBillDTO);
            ApiResult apiResult = openAPIInvokeService.invoke(BizCodeEnum.EC_SHP_B0.code(), ecShipBillDTO.getSellerId(), JSON.toJSONString(ecShipBillDTO));
            if (apiResult.isSuccess()) {
                log.info("EC船运计划信息下发ERP成功");
            } else {
                log.info("EC船运计划信息下发ERP失败");
                throw new BizException(BasicCode.UNDEFINED_ERROR, apiResult.getDescription());
            }
        } catch (Exception e) {
            String description = "船运计划信息下发异常";
            log.error(description, e);
            CodeMeta errorCode = BasicCode.UNKNOWN_ERROR;
            String syncFailReason = null;
            if (e instanceof BizException) {
                BizException be = (BizException) e;
                errorCode = be.getErrorCode();
                description = description + "," + be.getMessage();
                syncFailReason = be.getMessage();
            }

            if(Objects.nonNull(failShipBill)) failShipBill.setSyncFailReason(syncFailReason);

            ItemResult itemResult = new ItemResult<>(errorCode.getCode(), description);
            externalExceptionHandle(itemResult, shipBill, ExternalMethodTypeEnum.ERP_SHIPBILL_SEND.getCode());

            updateShipBill(failShipBill);
        }
    }

    private void handleShipAndWharf(ProductDetailDTO productDetailDTO, WharfMaPQueryDTO wharfMaPQueryDTO, ECShipBillDTO ecShipBillDTO, ShipBillDetailDTO shipBillDetailDTO) {
        ItemResult<List<WharfMapListDTO>> wharfItemResult = wharfService.queryWharfMapList(wharfMaPQueryDTO);
        log.info("wharfItemResult load : {}", JSON.toJSONString(wharfItemResult));
        if (wharfItemResult != null && wharfItemResult.isSuccess() && CollectionUtils.isNotEmpty(wharfItemResult.getData())) {
            wharfItemResult.getData().stream().filter(Objects::nonNull).forEach(i -> ecShipBillDTO.setLoadingWhart(i.getErpWharfCode()));
        }
        if (CsStringUtils.equals(shipBillDetailDTO.getItemType(), PickingBillTypeEnum.SELLER_DELIVERY.getCode())) {
            // 计划卸货码头
            handleWharf(productDetailDTO, shipBillDetailDTO, wharfMaPQueryDTO, ecShipBillDTO);
        } else {
            handleEcShipBill(productDetailDTO, shipBillDetailDTO, ecShipBillDTO);
        }
    }

    private static void handleWharf(ShipBillDetailDTO shipBillDetailDTO, WharfMaPQueryDTO wharfMaPQueryDTO) {
        if (CsStringUtils.equals("01", shipBillDetailDTO.getInternal())) {
            //内部调拨特殊处理
            wharfMaPQueryDTO.setEcWharfId(shipBillDetailDTO.getLoadPortId());
        } else {
            wharfMaPQueryDTO.setType("1");
        }
    }

    private void handleErpShippingQueryCondition(ShipBillDetailDTO shipBillDetailDTO, ECShipBillDTO ecShipBillDTO, ErpShippingMap erpShippingQueryCondition) {
        if (!CsStringUtils.equals(shipBillDetailDTO.getSellerId(), shipBillDetailDTO.getCarrierId())) {
            CarrierBindingConditionQueryDTO carrierQueryDTO = new CarrierBindingConditionQueryDTO();
            carrierQueryDTO.setBindingMemberId(shipBillDetailDTO.getSellerId());
            carrierQueryDTO.setBindingStatus(BindingStatusEnum.BINDING_SUCCESS.getCode());
            carrierQueryDTO.setCarrierId(shipBillDetailDTO.getCarrierId());
            ItemResult<List<CarrierBindingListDTO>> carrierBindingItemResult = carrierBindingService.queryCarrierBindingByCondition(carrierQueryDTO);
            log.info("carrierBindingItemResult : {}", JSON.toJSONString(carrierBindingItemResult));
            if (carrierBindingItemResult != null && carrierBindingItemResult.isSuccess() && CollectionUtils.isNotEmpty(carrierBindingItemResult.getData())) {
                // ERP承运商编号
                carrierBindingItemResult.getData().stream().filter(Objects::nonNull).forEach(i -> ecShipBillDTO.setErpCarrierNo(i.getErpCarrierNo()));
            }
            erpShippingQueryCondition.setErpCarrierNo(ecShipBillDTO.getErpCarrierNo());
        }
    }

    private void updateShipBill(ShipBill failShipBill) {
        if (Objects.nonNull(failShipBill)) {
            log.info("船运计划信息下发 : {}", failShipBill);
            failShipBill.setUpdateTime(new Date());
            failShipBill.setExternalWaybillStatus(WaybillExternalStatusEnum.ERP_CREATE_FAIL.getCode());
            shipBillMapper.updateByPrimaryKeySelective(failShipBill);
        }
    }

    private void handleEcShipBill(ProductDetailDTO productDetailDTO, ShipBillDetailDTO shipBillDetailDTO, ECShipBillDTO ecShipBillDTO) {
        if(Objects.isNull(productDetailDTO) || Objects.isNull(shipBillDetailDTO))
            return;
        WarehouseDetailsReqDTO warehouseDetailsReqDTO = new WarehouseDetailsReqDTO();
        warehouseDetailsReqDTO.setUserId(productDetailDTO.getSellerId());
        warehouseDetailsReqDTO.setWarehouseId(shipBillDetailDTO.getWarehouseId());
        WarehouseDetailsDTO warehouseDetailsDTO = warehouseService.queryWarehouseDetailsByReq(warehouseDetailsReqDTO);
        log.info("warehouseDetailsDTO : {}", warehouseDetailsDTO);
        if(Objects.nonNull(ecShipBillDTO)) {
            if (Objects.nonNull(warehouseDetailsDTO)) {
                // 提货点组织编码
                ecShipBillDTO.setOrganizationCode(warehouseDetailsDTO.getErpOrgCode());
                // 提货点编码
                ecShipBillDTO.setSubInv(warehouseDetailsDTO.getErpCode());
            } else {
                // 提货点组织编码
                ecShipBillDTO.setOrganizationCode("");
                // 提货点编码
                ecShipBillDTO.setSubInv("");
            }
        }
    }

    private void handleWharf(ProductDetailDTO productDetailDTO,
                             ShipBillDetailDTO shipBillDetailDTO,
                             WharfMaPQueryDTO wharfMaPQueryDTO,
                             ECShipBillDTO ecShipBillDTO) {
        ItemResult<List<WharfMapListDTO>> wharfItemResult;
        if (CsStringUtils.equals(shipBillDetailDTO.getBillProxyType(), BillProxyTypeEnum.NORMAL.getCode())) {
            wharfMaPQueryDTO.setMemberId(productDetailDTO.getBuyerId());
            wharfMaPQueryDTO.setErpMemberId(productDetailDTO.getSellerId());
            wharfMaPQueryDTO.setEcWharfName(shipBillDetailDTO.getUnloadPortName());
            handleWharfMapQuery(shipBillDetailDTO, wharfMaPQueryDTO);
            wharfItemResult = wharfService.queryWharfMapList(wharfMaPQueryDTO);
            log.info("wharfItemResult unload : {}", JSON.toJSONString(wharfItemResult));
            if (wharfItemResult != null && wharfItemResult.isSuccess() && CollectionUtils.isNotEmpty(wharfItemResult.getData())) {
                wharfItemResult.getData().stream().filter(Objects::nonNull).forEach(i -> ecShipBillDTO.setUnloadDockPlan(i.getErpWharfCode()));
            }
        } else {
            handleEcShipBillUnloadDockPlan(productDetailDTO, ecShipBillDTO);
        }
    }

    private void handleEcShipBillUnloadDockPlan(ProductDetailDTO productDetailDTO, ECShipBillDTO ecShipBillDTO) {
        ReceivingAddressMapQueryDTO addressMapQueryDTO = new ReceivingAddressMapQueryDTO();
        addressMapQueryDTO.setAddressId(productDetailDTO.getReceiveAddressId());
        addressMapQueryDTO.setSellerId(productDetailDTO.getBuyerId());
        addressMapQueryDTO.setFirmId(productDetailDTO.getSellerId());
        List<ReceivingAddrMapWharfDTO> mapWharfDTOS = receivingAddressService.queryErpWharfList(addressMapQueryDTO);
        log.info("mapWharfDTOS unload : {}", JSON.toJSONString(mapWharfDTOS));
        if (CollectionUtils.isNotEmpty(mapWharfDTOS)) {
            mapWharfDTOS.stream().filter(Objects::nonNull).forEach(i -> ecShipBillDTO.setUnloadDockPlan(i.getErpWharfCode()));
        }
    }

    private static void handleEcShipBill(ShipBillERPInfoDTO shipBillERPInfoDTO, ECShipBillDTO ecShipBillDTO) {
        if (shipBillERPInfoDTO != null) {
            if (shipBillERPInfoDTO.getContractDTO() != null) {
                // ERP合同编号
                ecShipBillDTO.setContractCode(shipBillERPInfoDTO.getContractDTO().getErpContractNum());
            }
            if (shipBillERPInfoDTO.getOrderPayinfoDTO() != null) {
                // 结算方式
                String settlementWay = shipBillERPInfoDTO.getOrderPayinfoDTO().getPayinfoWay();
                log.info("结算方式 : {}", settlementWay);
                if (CsStringUtils.equals(settlementWay, ChannelCodeEnum.ERP.getCode()) ||
                        CsStringUtils.isBlank(settlementWay)) {
                    ecShipBillDTO.setSettlementWay("01");
                } else {
                    ecShipBillDTO.setSettlementWay("00");
                }
            }
        }
    }

    @Override
    public void modifyExternalShipBill(ExternalWaybillRequestDTO requestDTO) {
        ShipBill shipBill = shipBillQueryBizService.selectShipBillById(requestDTO.getWaybillId());
        try {
            ShipBillDetailDTO shipBillDetailDTO = shipBillQueryBizService.selectShipBillDetailByWaybillId(requestDTO.getWaybillId());
            log.info("modifyExternalShipBill : {}", JSON.toJSONString(shipBillDetailDTO));
            if (shipBillDetailDTO == null) {
                throw new BizException(BasicCode.DATA_NOT_EXIST, "运单信息");
            }

            ECShipBillModifyDTO ecShipBillModifyDTO = new ECShipBillModifyDTO();
            ecShipBillModifyDTO.setOperator(requestDTO.getOperatorUserId());
            ecShipBillModifyDTO.setOperatorName(requestDTO.getOperatorUserName());
            ecShipBillModifyDTO.setSellerId(shipBillDetailDTO.getProductDetails().get(0).getSellerId());
            ecShipBillModifyDTO.setEcBillCode(shipBillDetailDTO.getWaybillNum());
            ecShipBillModifyDTO.setExternalBillId(shipBillDetailDTO.getExternalWaybillId());
            ecShipBillModifyDTO.setShipName(shipBillDetailDTO.getShippingName());
            // 卖家配送
            if (CsStringUtils.equals(shipBillDetailDTO.getItemType(), PickingBillTypeEnum.SELLER_DELIVERY.getCode())) {
                ErpShippingMap erpShippingQueryCondition = new ErpShippingMap();
                erpShippingQueryCondition.setEcMemberId(shipBillDetailDTO.getSellerId());
                erpShippingQueryCondition.setEcShippingId(shipBillDetailDTO.getShippingId());
                if (!CsStringUtils.equals(shipBillDetailDTO.getSellerId(), shipBillDetailDTO.getCarrierId())) {
                    CarrierBindingConditionQueryDTO carrierQueryDTO = new CarrierBindingConditionQueryDTO();
                    carrierQueryDTO.setBindingMemberId(shipBillDetailDTO.getSellerId());
                    carrierQueryDTO.setBindingStatus(BindingStatusEnum.BINDING_SUCCESS.getCode());
                    carrierQueryDTO.setCarrierId(shipBillDetailDTO.getCarrierId());
                    ItemResult<List<CarrierBindingListDTO>> carrierBindingItemResult = carrierBindingService.queryCarrierBindingByCondition(carrierQueryDTO);
                    log.info("carrierBindingItemResult : {}", JSON.toJSONString(carrierBindingItemResult));
                    if (carrierBindingItemResult != null && carrierBindingItemResult.isSuccess() && CollectionUtils.isNotEmpty(carrierBindingItemResult.getData())) {
                        // ERP承运商编号
                        carrierBindingItemResult.getData().stream().filter(Objects::nonNull).forEach(i -> ecShipBillModifyDTO.setErpCarrierNo(i.getErpCarrierNo()));
                    }
                    erpShippingQueryCondition.setErpCarrierNo(ecShipBillModifyDTO.getErpCarrierNo());
                }
                ErpShippingMap erpShippingMap = erpShippingMapMapper.findErpShippingMapByCondition(erpShippingQueryCondition);
                log.info("erpShippingMap : {}", JSON.toJSONString(erpShippingMap));
                handleEcShipBill(erpShippingMap, ecShipBillModifyDTO);
            }

            ApiResult apiResult = openAPIInvokeService.invoke(BizCodeEnum.EC_SHP_S0.code(), ecShipBillModifyDTO.getSellerId(), JSON.toJSONString(ecShipBillModifyDTO));
            ShipBill needUpdateDBShipBill = new ShipBill();
            needUpdateDBShipBill.setWaybillId(shipBillDetailDTO.getWaybillId());
            needUpdateDBShipBill.setExternalWaybillStatus(WaybillExternalStatusEnum.ERP_CHANGING.getCode());
            needUpdateDBShipBill.setUpdateTime(new Date());
            needUpdateDBShipBill.setSyncFailReason(apiResult.isSuccess() ? "" : apiResult.getDescription());
            shipBillMapper.updateByPrimaryKeySelective(needUpdateDBShipBill);
            if (apiResult.isSuccess()) {
                log.info("EC船运计划信息修改成功");
            } else {
                log.info("EC船运计划信息修改失败");
                throw new BizException(BasicCode.UNDEFINED_ERROR, apiResult.getDescription());
            }
        } catch (Exception e) {
            String description = "船运计划信息修改异常";
            log.error(description, e);
            CodeMeta errorCode = BasicCode.UNKNOWN_ERROR;
            if (e instanceof BizException) {
                BizException be = (BizException) e;
                errorCode = be.getErrorCode();
                description = description + "," + be.getMessage();
            }
            ItemResult itemResult = new ItemResult<>(errorCode.getCode(), description);
            externalExceptionHandle(itemResult, shipBill, ExternalMethodTypeEnum.ERP_SHIPBILL_CHANGE.getCode());

        }
    }

    private static void handleEcShipBill(ErpShippingMap erpShippingMap, ECShipBillModifyDTO ecShipBillModifyDTO) {
        if (erpShippingMap != null) {
            // ERP船舶名称
            ecShipBillModifyDTO.setShipName(erpShippingMap.getErpShippingName());
            // ERP船舶编号
            ecShipBillModifyDTO.setShipCode(erpShippingMap.getErpShippingCode());
            // ERP承运商编号
            ecShipBillModifyDTO.setErpCarrierNo(erpShippingMap.getErpCarrierNo());
        }
    }

    @Override
    public void closeExternalShipBill(ExternalWaybillRequestDTO requestDTO) {
        ShipBill shipBill = shipBillQueryBizService.selectShipBillById(requestDTO.getWaybillId());
        ShipBill needUpdateDBShipBill = new ShipBill();
        needUpdateDBShipBill.setWaybillId(requestDTO.getWaybillId());
        try {
            if (CsStringUtils.equals(shipBill.getExternalWaybillStatus(), WaybillExternalStatusEnum.ERP_CREATE_FAIL.getCode()) ||
                    CsStringUtils.equals(shipBill.getExternalWaybillStatus(), WaybillExternalStatusEnum.ERP_CLOSE_SUCCESS.getCode())) {
                // 原先ERP就创建失败,直接返回
                return;
            }
            ShipBillDetailDTO shipBillDetailDTO = shipBillQueryBizService.selectShipBillDetailByWaybillId(requestDTO.getWaybillId());
            log.info("closeExternalShipBill : {}", JSON.toJSONString(shipBillDetailDTO));
            if (shipBillDetailDTO == null) {
                throw new BizException(BasicCode.DATA_NOT_EXIST, "运单信息");
            }

            ECShipBillCloseDTO ecShipBillCloseDTO = new ECShipBillCloseDTO();
            ecShipBillCloseDTO.setOperator(requestDTO.getOperatorUserId());
            ecShipBillCloseDTO.setOperatorName(requestDTO.getOperatorUserName());
            ecShipBillCloseDTO.setSellerId(shipBillDetailDTO.getProductDetails().get(0).getSellerId());
            ecShipBillCloseDTO.setEcBillCode(shipBillDetailDTO.getWaybillNum());
            ecShipBillCloseDTO.setExternalBillId(shipBillDetailDTO.getExternalWaybillId());

            ApiResult apiResult = openAPIInvokeService.invoke(BizCodeEnum.EC_SHP_Z0.code(), ecShipBillCloseDTO.getSellerId(), JSON.toJSONString(ecShipBillCloseDTO));
            needUpdateDBShipBill.setExternalWaybillStatus(WaybillExternalStatusEnum.ERP_CLOSING.getCode());
            needUpdateDBShipBill.setUpdateTime(new Date());
            shipBillMapper.updateByPrimaryKeySelective(needUpdateDBShipBill);
            if (apiResult.isSuccess()) {
                log.info("EC船运计划作废成功");
            } else {
                log.info("EC船运计划作废失败");
                throw new BizException(BasicCode.UNDEFINED_ERROR, apiResult.getDescription());
            }
        } catch (Exception e) {
            String description = "船运计划作废异常";
            log.error(description, e);
            CodeMeta errorCode = BasicCode.UNKNOWN_ERROR;
            if (e instanceof BizException) {
                BizException be = (BizException) e;
                errorCode = be.getErrorCode();
                description = be.getMessage();
            }
            needUpdateDBShipBill.setSyncFailReason(description);
            shipBillMapper.updateByPrimaryKeySelective(needUpdateDBShipBill);
            ItemResult itemResult = new ItemResult<>(errorCode.getCode(), description);
            externalExceptionHandle(itemResult, shipBill, ExternalMethodTypeEnum.ERP_SHIPBILL_CLOSE.getCode());

        }
    }

    @Override
    public ItemResult<Void> createExternalShipBillCallback(ItemResult<ShipBillERPCallbackDTO> itemResult) {
        log.info("createExternalShipBillCallback : {}", JSON.toJSONString(itemResult));
        if (itemResult == null || itemResult.getData() == null){
            return new ItemResult<>(null);
        }
        ShipBill shipBill = shipBillQueryBizService.queryWaybillByWaybillNum(itemResult.getData().getEcBillCode());
        ShipBill needUpdateDBShipBill = new ShipBill();
        needUpdateDBShipBill.setWaybillId(shipBill.getWaybillId());
        if (itemResult.isSuccess()) {
            if (!WaybillExternalStatusEnum.ERP_CREATE_FAIL.getCode().equals(shipBill.getExternalWaybillStatus()) &&
                    !WaybillExternalStatusEnum.ERP_CREATING.getCode().equals(shipBill.getExternalWaybillStatus()) &&
                    !WaybillExternalStatusEnum.ERP_CREATE_SUCCESS.getCode().equals(shipBill.getExternalWaybillStatus())) {
                throw new BizException(BasicCode.INVALID_PARAM, SHIP_BILL_STATUS_EXCEPTION);
            }
            ShipBillERPCallbackDTO callbackDTO = itemResult.getData();
            if (!DefaultErpCodeEnum.DEFAULT_ERP_WAYBILL_CODE.getCode().equals(callbackDTO.getExternalBillId())) {
                needUpdateDBShipBill.setExternalWaybillId(callbackDTO.getExternalBillId());
                needUpdateDBShipBill.setExternalWaybillStatus(WaybillExternalStatusEnum.ERP_CREATE_SUCCESS.getCode());
            }
        } else {
            needUpdateDBShipBill.setExternalWaybillStatus(WaybillExternalStatusEnum.ERP_CREATE_FAIL.getCode());
            needUpdateDBShipBill.setSyncFailReason(itemResult.getDescription());
            itemResult.setCode(LogisticsErrorCode.ERP_CALLBACK_ERROR.getCode());
        }
        needUpdateDBShipBill.setUpdateTime(new Date());
        shipBillMapper.updateByPrimaryKeySelective(needUpdateDBShipBill);

        externalExceptionHandle(itemResult, shipBill, ExternalMethodTypeEnum.ERP_SHIPBILL_SEND.getCode());
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> modifyExternalShipBillCallback(ItemResult<ShipBillModifyERPCallbackDTO> itemResult) {
        log.info("modifyExternalShipBillCallback : {}", JSON.toJSONString(itemResult));
        if (itemResult == null || itemResult.getData() == null){
            return new ItemResult<>(null);
        }
        ShipBill shipBill = shipBillQueryBizService.queryWaybillByWaybillNum(itemResult.getData().getEcBillCode());
        ShipBill needUpdateDBShipBill = new ShipBill();
        needUpdateDBShipBill.setWaybillId(shipBill.getWaybillId());
        if (itemResult.isSuccess()) {
            if (!WaybillExternalStatusEnum.ERP_CHANGING.getCode().equals(shipBill.getExternalWaybillStatus()) &&
                    !WaybillExternalStatusEnum.ERP_CHANGE_SUCCESS.getCode().equals(shipBill.getExternalWaybillStatus())) {
                throw new BizException(BasicCode.INVALID_PARAM, SHIP_BILL_STATUS_EXCEPTION);
            }
            needUpdateDBShipBill.setExternalWaybillStatus(WaybillExternalStatusEnum.ERP_CHANGE_SUCCESS.getCode());
        } else {
            needUpdateDBShipBill.setSyncFailReason(itemResult.getDescription());
            itemResult.setCode(LogisticsErrorCode.ERP_CALLBACK_ERROR.getCode());
        }
        needUpdateDBShipBill.setUpdateTime(new Date());
        shipBillMapper.updateByPrimaryKeySelective(needUpdateDBShipBill);
        externalExceptionHandle(itemResult, shipBill, ExternalMethodTypeEnum.ERP_SHIPBILL_CHANGE.getCode());

        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> closeExternalShipBillCallback(ItemResult<ShipBillCloseERPCallbackDTO> itemResult) {
        log.info("closeExternalShipBillCallback : {}", JSON.toJSONString(itemResult));
        if (itemResult == null || itemResult.getData() == null){
            return new ItemResult<>(null);
        }
        ShipBill shipBill = shipBillQueryBizService.queryWaybillByWaybillNum(itemResult.getData().getEcBillCode());
        ShipBill needUpdateDBShipBill = new ShipBill();
        needUpdateDBShipBill.setWaybillId(shipBill.getWaybillId());
        if (itemResult.isSuccess()) {
            if (!WaybillExternalStatusEnum.ERP_CLOSING.getCode().equals(shipBill.getExternalWaybillStatus()) &&
                    !WaybillExternalStatusEnum.ERP_CLOSE_SUCCESS.getCode().equals(shipBill.getExternalWaybillStatus())) {
                throw new BizException(BasicCode.INVALID_PARAM, SHIP_BILL_STATUS_EXCEPTION);
            }
            needUpdateDBShipBill.setExternalWaybillStatus(WaybillExternalStatusEnum.ERP_CLOSE_SUCCESS.getCode());
        } else {
            needUpdateDBShipBill.setSyncFailReason(itemResult.getDescription());
            itemResult.setCode(LogisticsErrorCode.ERP_CALLBACK_ERROR.getCode());
        }
        needUpdateDBShipBill.setUpdateTime(new Date());
        shipBillMapper.updateByPrimaryKeySelective(needUpdateDBShipBill);
        externalExceptionHandle(itemResult, shipBill, ExternalMethodTypeEnum.ERP_SHIPBILL_CLOSE.getCode());

        return new ItemResult<>(null);
    }

    /**
     * 处理异常记录
     * @param itemResult 调用结果对象
     * @param shipBill 运单对象
     * @param methodType 方法类型
     */
    public void externalExceptionHandle(ItemResult itemResult, ShipBill shipBill, String methodType) {
        ExternalExceptionHandle externalExceptionHandle = externalExceptionBizService
                .queryExternalExceptionHandleByCondition(shipBill.getWaybillId(), methodType);
        if (externalExceptionHandle == null) {
            if (!itemResult.isSuccess()) {
                ExternalExceptionSaveDTO externalExceptionSaveDTO = new ExternalExceptionSaveDTO();
                externalExceptionSaveDTO.setWaybillId(shipBill.getWaybillId());
                externalExceptionSaveDTO.setWaybillNum(shipBill.getWaybillNum());
                externalExceptionSaveDTO.setMethodType(methodType);
                externalExceptionSaveDTO.setExceptionReason(itemResult.getDescription());
                externalExceptionSaveDTO.setOperatorUserId(ERP_OPERATE_USER_ID);
                externalExceptionSaveDTO.setOperatorUserName(ERP_OPERATE_USER_NAME);
                externalExceptionHandle = externalExceptionBizService.addExternalExceptionHandle(externalExceptionSaveDTO);
            }
        } else {
            if (itemResult.isSuccess()) {
                externalExceptionHandle.setStatus(ExternalExceptionStatusEnum.HANDLE_SUCCESS.getCode());
                externalExceptionHandle.setExceptionReason("");
            } else {
                externalExceptionHandle.setStatus(ExternalExceptionStatusEnum.HANDLE_FAIL.getCode());
                externalExceptionHandle.setExceptionReason(itemResult.getDescription());
            }
            if (externalExceptionHandle.getRetryCount() == null) {
                externalExceptionHandle.setRetryCount(0);
            }
            externalExceptionHandle.setRetryCount(externalExceptionHandle.getRetryCount() + 1);
            externalExceptionBizService.editExternalExceptionHandle(externalExceptionHandle);
        }
        if (externalExceptionHandle == null) return;
        //重试策略
        List<String> operationTypeList = Lists.newArrayList(
                ExternalMethodTypeEnum.ERP_SHIPBILL_SEND.getCode(),
                ExternalMethodTypeEnum.ERP_SHIPBILL_CHANGE.getCode(),
                ExternalMethodTypeEnum.ERP_SHIPBILL_CLOSE.getCode()
        );
        if (externalExceptionHandle.getRetryCount() < retryCount && operationTypeList.contains(methodType)
                && !CsStringUtils.equals(externalExceptionHandle.getStatus(), OrderExceptionStatusEnum.HANDLE_SUCCESS.getCode())
                && !CsStringUtils.equals(itemResult.getCode(), LogisticsErrorCode.ERP_CALLBACK_ERROR.getCode())) {
            //重试等待分钟数为以2为底数的重试次数幂
            long waitMinutes = (long)Math.pow(2, externalExceptionHandle.getRetryCount());
            TaskMessage taskMessage = new TaskMessage(1000L * 60 * waitMinutes, externalExceptionHandle.getExceptionHandleId(), LogisticsCommonBean.ERP_EXCEPTION_QUEUE);
            retryErpTaskManager.addToQueue(taskMessage);
        }
    }
}