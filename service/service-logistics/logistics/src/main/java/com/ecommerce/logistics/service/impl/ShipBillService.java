package com.ecommerce.logistics.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.ecommerce.base.api.dto.warehouse.WarehouseDetailsDTO;
import com.ecommerce.base.api.service.IWarehouseService;
import com.ecommerce.common.enums.AppNames;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.constant.NumberConstant;
import com.github.pagehelper.page.PageMethod;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.common.utils.DateUtil;
import com.ecommerce.logistics.LogisticsCommonBean;
import com.ecommerce.logistics.api.dto.DeliveryDetailVehicleDTO;
import com.ecommerce.logistics.api.dto.DeliveryInfoDTO;
import com.ecommerce.logistics.api.dto.EmallPublishWaybillListDTO;
import com.ecommerce.logistics.api.dto.EmallPublishWaybillQueryDTO;
import com.ecommerce.logistics.api.dto.EstimateKmQueryDTO;
import com.ecommerce.logistics.api.dto.ForBidCloseWaybillCountQueryDTO;
import com.ecommerce.logistics.api.dto.GoodsInfoDTO;
import com.ecommerce.logistics.api.dto.LastSignWaybillQueryDTO;
import com.ecommerce.logistics.api.dto.ShipBillWaitRerouteListDTO;
import com.ecommerce.logistics.api.dto.WaitRerouteShipBillCondDTO;
import com.ecommerce.logistics.api.dto.WaybillBriefDTO;
import com.ecommerce.logistics.api.dto.attachment.AttListAddDTO;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.carriage.SocialDriverCarriageDTO;
import com.ecommerce.logistics.api.dto.delivery.InternalAllocationDTO;
import com.ecommerce.logistics.api.dto.driver.AssignDriverLogAddDTO;
import com.ecommerce.logistics.api.dto.externalexception.ExternalExceptionSaveDTO;
import com.ecommerce.logistics.api.dto.operationrecord.OperationRecordAddDTO;
import com.ecommerce.logistics.api.dto.operationrecord.OperationRecordDTO;
import com.ecommerce.logistics.api.dto.operationrecord.QueryWaybillOperationDTO;
import com.ecommerce.logistics.api.dto.pickingbill.OneToManyOptionDTO;
import com.ecommerce.logistics.api.dto.pickingbill.PickingBillDTO;
import com.ecommerce.logistics.api.dto.pickingbill.TwoLeaveOptionDTO;
import com.ecommerce.logistics.api.dto.shipbill.CancelShipBillDTO;
import com.ecommerce.logistics.api.dto.shipbill.CheckWaybillInfoDTO;
import com.ecommerce.logistics.api.dto.shipbill.CheckWaybillQueryDTO;
import com.ecommerce.logistics.api.dto.shipbill.CloseShipBillDTO;
import com.ecommerce.logistics.api.dto.shipbill.ConcreteShipBillInfoDTO;
import com.ecommerce.logistics.api.dto.shipbill.QrCodeDTO;
import com.ecommerce.logistics.api.dto.shipbill.ReassignShipBillDTO;
import com.ecommerce.logistics.api.dto.shipbill.ShipBillBriCondDTO;
import com.ecommerce.logistics.api.dto.shipbill.ShipBillBriResDTO;
import com.ecommerce.logistics.api.dto.shipbill.ShipBillDTO;
import com.ecommerce.logistics.api.dto.shipbill.ShipBillDetailDTO;
import com.ecommerce.logistics.api.dto.shipbill.ShipBillListDTO;
import com.ecommerce.logistics.api.dto.shipbill.ShipBillOrderDetailDTO;
import com.ecommerce.logistics.api.dto.shipbill.ShipBillQueryDTO;
import com.ecommerce.logistics.api.dto.shipbill.ShipItemNotifySettlementDTO;
import com.ecommerce.logistics.api.dto.shipbill.ShipWaybillListDTO;
import com.ecommerce.logistics.api.dto.shipbill.ShipWaybillQueryDTO;
import com.ecommerce.logistics.api.dto.shipbill.TransportInfoDTO;
import com.ecommerce.logistics.api.dto.shipping.ShippingInfoDetailsDTO;
import com.ecommerce.logistics.api.dto.vehicletype.VehicleTypeDetailDTO;
import com.ecommerce.logistics.api.dto.warehouse.WarehouseOptionDTO;
import com.ecommerce.logistics.api.dto.waybill.AppWaybillQueryConditionsMapDTO;
import com.ecommerce.logistics.api.dto.waybill.AppWaybillStatisticsDTO;
import com.ecommerce.logistics.api.dto.waybill.ArriveWarehouseDTO;
import com.ecommerce.logistics.api.dto.waybill.BatchPassCheckDTO;
import com.ecommerce.logistics.api.dto.waybill.CarryWaybillSubmitDTO;
import com.ecommerce.logistics.api.dto.waybill.CheckWaybillListQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.CompleteShipBillDTO;
import com.ecommerce.logistics.api.dto.waybill.ContractSubmitInfoDTO;
import com.ecommerce.logistics.api.dto.waybill.DistrictListDTO;
import com.ecommerce.logistics.api.dto.waybill.DriverConfirmDTO;
import com.ecommerce.logistics.api.dto.waybill.DriverDeliveryDTO;
import com.ecommerce.logistics.api.dto.waybill.DriverWaybillAssignDTO;
import com.ecommerce.logistics.api.dto.waybill.DriverWaybillListQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.EnterFactoryDTO;
import com.ecommerce.logistics.api.dto.waybill.EvaluateWaybillListDTO;
import com.ecommerce.logistics.api.dto.waybill.ExternalWaybillRequestDTO;
import com.ecommerce.logistics.api.dto.waybill.LeaveWarehouseDTO;
import com.ecommerce.logistics.api.dto.waybill.OpenCabinShipBillDTO;
import com.ecommerce.logistics.api.dto.waybill.PassCheckDTO;
import com.ecommerce.logistics.api.dto.waybill.PublishWaybillListDTO;
import com.ecommerce.logistics.api.dto.waybill.QueryGoodsDTO;
import com.ecommerce.logistics.api.dto.waybill.RefundShipBillDTO;
import com.ecommerce.logistics.api.dto.waybill.SnatchWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.SnatchedWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.SnatchedWaybillQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.TradeWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.TradeWaybillDetailDTO;
import com.ecommerce.logistics.api.dto.waybill.UploadCertificateDTO;
import com.ecommerce.logistics.api.dto.waybill.UserSeckillWaybillListDTO;
import com.ecommerce.logistics.api.dto.waybill.UserSeckillWaybillQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.WaitCheckWaybillListDTO;
import com.ecommerce.logistics.api.dto.waybill.WarehouseAdminProccessedDTO;
import com.ecommerce.logistics.api.dto.waybill.WarehouseAdminProccessedQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.WarehouseAdminWaitDeliveryDTO;
import com.ecommerce.logistics.api.dto.waybill.WarehouseAdminWaitDeliveryQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillAssignDTO;
import com.ecommerce.logistics.api.enums.BillProxyTypeEnum;
import com.ecommerce.logistics.api.enums.ExternalExceptionStatusEnum;
import com.ecommerce.logistics.api.enums.ExternalMethodTypeEnum;
import com.ecommerce.logistics.api.enums.ExternalSyncFlagEnum;
import com.ecommerce.logistics.api.enums.LogisticsMessageTypeEnum;
import com.ecommerce.logistics.api.enums.OperationRecordTypeEnum;
import com.ecommerce.logistics.api.enums.PlanStatusEnum;
import com.ecommerce.logistics.api.enums.ShipBillStatusEnum;
import com.ecommerce.logistics.api.enums.SnatchWaybillReturnEnum;
import com.ecommerce.logistics.api.enums.TransportToolTypeEnum;
import com.ecommerce.logistics.api.enums.UserRoleEnum;
import com.ecommerce.logistics.api.enums.VehicleStatusEnum;
import com.ecommerce.logistics.api.enums.WaybillExternalStatusEnum;
import com.ecommerce.logistics.api.enums.WaybillOperationTypeEnum;
import com.ecommerce.logistics.api.enums.WaybillTypeEnum;
import com.ecommerce.logistics.biz.IAssignDriverLogBizService;
import com.ecommerce.logistics.biz.IAttachmentBizService;
import com.ecommerce.logistics.biz.IExternalExceptionBizService;
import com.ecommerce.logistics.biz.IInternalAllocationBizService;
import com.ecommerce.logistics.biz.IOperationRecordBizService;
import com.ecommerce.logistics.biz.IShipBillBizService;
import com.ecommerce.logistics.biz.IShipBillCompleteBizService;
import com.ecommerce.logistics.biz.IShipBillLeaveWarehouseBizService;
import com.ecommerce.logistics.biz.IShipBillOperateBizService;
import com.ecommerce.logistics.biz.IShipBillQueryBizService;
import com.ecommerce.logistics.biz.IShippingInfoBizService;
import com.ecommerce.logistics.biz.ITransportCategoryBizService;
import com.ecommerce.logistics.biz.IVehicleTypeBizService;
import com.ecommerce.logistics.biz.IWaybillSignBizService;
import com.ecommerce.logistics.biz.delivery.IDeliveryInfoBizService;
import com.ecommerce.logistics.biz.message.ISMSMessageBizService;
import com.ecommerce.logistics.biz.message.MessageQueueService;
import com.ecommerce.logistics.dao.dto.shipbill.ParentInfo;
import com.ecommerce.logistics.dao.dto.waybill.ConfirmWaybillDO;
import com.ecommerce.logistics.dao.dto.waybill.TradeWaybillDetailDO;
import com.ecommerce.logistics.dao.mapper.ShipBillItemMapper;
import com.ecommerce.logistics.dao.mapper.ShipBillMapper;
import com.ecommerce.logistics.dao.mapper.VehicleMapper;
import com.ecommerce.logistics.dao.vo.DeliveryInfo;
import com.ecommerce.logistics.dao.vo.ShipBill;
import com.ecommerce.logistics.dao.vo.ShipBillItem;
import com.ecommerce.logistics.dao.vo.Vehicle;
import com.ecommerce.logistics.dao.vo.VehicleType;
import com.ecommerce.logistics.exception.LogisticsErrorCode;
import com.ecommerce.logistics.service.IShipBillExternalService;
import com.ecommerce.logistics.service.IShipBillService;
import com.ecommerce.logistics.service.IWaybillNewExternalService;
import com.ecommerce.logistics.util.LogisticsUtils;
import com.ecommerce.member.api.dto.member.enums.MemberPlatform;
import com.ecommerce.order.api.enums.AdjustAddWayEnum;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @Auther: colu
 * @Date: 2021-01-19 15:36
 * @Description: ShipBillService
 */
@Slf4j
@Service("shipBillService")
public class ShipBillService implements IShipBillService {

    @Autowired(required = false)
    private ShipBillItemMapper shipBillItemMapper;

    @Autowired(required = false)
    private ShipBillMapper shipBillMapper;

    @Autowired(required = false)
    private VehicleMapper vehicleMapper;

    @Autowired
    private IShippingInfoBizService shippingInfoBizService;

    @Autowired
    private IShipBillLeaveWarehouseBizService shipBillLeaveWarehouseBizService;

    @Autowired
    private IShipBillCompleteBizService shipBillCompleteBizService;

    @Autowired
    private IShipBillOperateBizService shipBillOperateBizService;

    @Autowired
    private IDeliveryInfoBizService deliveryInfoBizService;

    @Autowired
    private IShipBillQueryBizService shipBillQueryBizService;

    @Autowired
    private IShipBillBizService shipBillBizService;

    @Autowired
    private IAttachmentBizService attachmentBizService;

    @Autowired
    private IWarehouseService warehouseService;

    @Autowired
    private IOperationRecordBizService operationRecordBizService;

    @Autowired
    private IWaybillNewExternalService waybillNewExternalService;

    @Autowired
    private IShipBillExternalService shipBillExternalService;

    @Autowired
    private MessageQueueService messageQueueService;

    @Autowired
    private ISMSMessageBizService smsMessageBizService;

    @Autowired
    protected IVehicleTypeBizService vehicleTypeBizService;


    @Autowired
    private ITransportCategoryBizService transportCategoryBizService;


    @Autowired
    private IAssignDriverLogBizService assignDriverLogBizService;

    @Autowired
    private IWaybillSignBizService waybillSignBizService;

    @Autowired
    private IExternalExceptionBizService externalExceptionBizService;

    @Autowired
    private IInternalAllocationBizService allocationBizService;

    private final static String YYYY_MM_DD_HH_MM_SS_FORMAT = "yyyy-MM-dd HH:mm:ss";

    @Resource(name = "bizRedisTemplate")
    private RedisTemplate bizRedisTemplate;

    @Override
    public void supplementFinishNotify(ShipItemNotifySettlementDTO shipItemNotifySettlementDTO) {
        if (CsStringUtils.equals(shipItemNotifySettlementDTO.getTransportToolType(), TransportToolTypeEnum.ROAD_TRANSPORT.getCode())) {
            shipBillLeaveWarehouseBizService.leaveSupplementCallBack(shipItemNotifySettlementDTO);
        }
    }

    @Override
    public ItemResult<PageData<ShipBillListDTO>> queryShipBillList(PageQuery<ShipBillQueryDTO> pageQuery) {
        return new ItemResult<>(shipBillQueryBizService.queryShipBillList(pageQuery));
    }

    @Override
    public ItemResult<AppWaybillStatisticsDTO> statisticsShipBill(ShipBillQueryDTO queryDTO) {
        return new ItemResult<>(shipBillQueryBizService.statisticsShipBill(queryDTO));
    }

    @Override
    public ItemResult<AppWaybillQueryConditionsMapDTO> selectShipBillQueryConditions(ShipBillQueryDTO queryDTO) {
        return new ItemResult<>(shipBillQueryBizService.selectShipBillQueryConditions(queryDTO));
    }

    @Override
    public ItemResult<Void> inputLeaveWarehouse(LeaveWarehouseDTO leaveWarehouseDTO) {
        Boolean res = false;
        log.info("inputLeaveWarehouse->{}",leaveWarehouseDTO);
        verifyParam(leaveWarehouseDTO);

        if (CsStringUtils.isNotBlank(leaveWarehouseDTO.getFactoryNumber())) {
            ShipBill newShipBill = new ShipBill();
            newShipBill.setWaybillId(leaveWarehouseDTO.getWaybillId());
            newShipBill.setFactoryNumber(leaveWarehouseDTO.getFactoryNumber());
            shipBillMapper.updateByPrimaryKeySelective(newShipBill);
        }

        ShipBillItem shipBillItem = shipBillItemMapper.selectByPrimaryKey(leaveWarehouseDTO.getWaybillItemId());

        if (CsStringUtils.equals(leaveWarehouseDTO.getTransportToolType(), TransportToolTypeEnum.WATER_TRANSPORT.getCode())) {
            if (BillProxyTypeEnum.isFirstLevel(shipBillItem.getBillProxyType())) {
                res = shipBillLeaveWarehouseBizService.ecShipLeaveWarehouse(leaveWarehouseDTO);
            } else {
                res = shipBillLeaveWarehouseBizService.ecProxyShipLeaveWarehouse(leaveWarehouseDTO);
            }
        } else {
            if (BillProxyTypeEnum.isFirstLevel(shipBillItem.getBillProxyType())) {
                res = shipBillLeaveWarehouseBizService.ecVehicleLeaveWarehouse(leaveWarehouseDTO);
            } else {
                res = shipBillLeaveWarehouseBizService.ecProxyVehicleLeaveWarehouse(leaveWarehouseDTO);
            }
        }
        ItemResult result = new ItemResult<>(null);
        result.setSuccess(res);
        if (res) {
            result.setDescription("出厂成功");

            //普通运单出站成功，给买家添加自动入库单
            try {
                if (CsStringUtils.equals(shipBillItem.getBillProxyType(), BillProxyTypeEnum.NORMAL.getCode())) {
                    //  暂时注释掉
                }
            }catch (Exception e){
                log.info("添加自动入库单报错:{}",e.getMessage());
            }
        } else {
            result.setDescription("预留资金不足,请补款后重试");
        }
        return result;
    }

    private void verifyParam(LeaveWarehouseDTO leaveWarehouseDTO) {
        if (CsStringUtils.isBlank(leaveWarehouseDTO.getWaybillItemId()) && LogisticsUtils.flagParse(leaveWarehouseDTO.getFromPlatform())) {
            String waybillItemId = shipBillItemMapper.selectCurrentItemForLeaveWarehouse(leaveWarehouseDTO.getWaybillId());
            log.info("来自平台,查询出站的子项ID:{}", waybillItemId);
            if (CsStringUtils.isBlank(waybillItemId)) {
            	throw new BizException(LogisticsErrorCode.REPEAT_OPERATION_OF_WAYBILL_OUTBOUND);
			}
            leaveWarehouseDTO.setWaybillItemId(waybillItemId);
        }
    }

    @Override
    public ItemResult<Void> ecProxyLeaveWarehouse(LeaveWarehouseDTO leaveWarehouseDTO) {
        if (CsStringUtils.equals(leaveWarehouseDTO.getTransportToolType(), TransportToolTypeEnum.WATER_TRANSPORT.getCode())) {
            shipBillLeaveWarehouseBizService.ecProxyShipLeaveWarehouse(leaveWarehouseDTO);
        } else {
            shipBillLeaveWarehouseBizService.ecProxyVehicleLeaveWarehouse(leaveWarehouseDTO);
        }
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Boolean> openCabin(OpenCabinShipBillDTO openCabinShipBillDTO) {
        ItemResult<Boolean> result = new ItemResult<>();
        String openCabin = shipBillCompleteBizService.openCabin(openCabinShipBillDTO);
        Boolean isSuccess = !CsStringUtils.equals(openCabin, "预留资金不足,请补款后重试");
        result.setData(isSuccess);
        result.setDescription(openCabin);
        result.setSuccess(isSuccess);
        return result;
    }

    @Override
    public ItemResult<Void> completeShipBill(CompleteShipBillDTO completeShipBillDTO) {
        if (CsStringUtils.equals(completeShipBillDTO.getSignType(), AdjustAddWayEnum.SELLER_SIGIN.getCode())) {
            shipBillCompleteBizService.completeConcrete(completeShipBillDTO);
        } else {
            shipBillCompleteBizService.completeShipBill(completeShipBillDTO);
        }

        //查询车辆是否已经全部运完
        ShipBill shipBill = shipBillMapper.selectByPrimaryKey(completeShipBillDTO.getWaybillId());

        if (CsStringUtils.equals(completeShipBillDTO.getSignType(), AdjustAddWayEnum.SELLER_SIGIN.getCode()) &&
                !CsStringUtils.equals(shipBill.getSyncFlag(), ExternalSyncFlagEnum.NO_SYNC.getCode())) {

            //异步通知erp改为同步:
            try {
                shipBillMapper.updateExternalStatus(shipBill.getWaybillId(), WaybillExternalStatusEnum.SETTLEMENT_ING.getCode());
                waybillSignBizService.sendSignedWaybillToErp(shipBill);
            } catch (Exception e) {
                //通知发生异常,添加到异常列表中
                log.error("处理异步通知ERP完成混凝土结算消息发生异常:{}", shipBill.getWaybillId(), e);
                ExternalExceptionSaveDTO externalExceptionSaveDTO = new ExternalExceptionSaveDTO();
                externalExceptionSaveDTO.setMethodType(ExternalMethodTypeEnum.ERP_CONCRETE_COMPLETE.getCode());
                externalExceptionSaveDTO.setExceptionReason("异常:" + e.getMessage());
                externalExceptionSaveDTO.setWaybillId(shipBill.getWaybillId());
                externalExceptionSaveDTO.setWaybillNum(shipBill.getWaybillNum());
                externalExceptionSaveDTO.setStatus(ExternalExceptionStatusEnum.HANDLING.getCode());
                externalExceptionBizService.addExternalExceptionHandle(externalExceptionSaveDTO);
                throw new BizException(BasicCode.UNDEFINED_ERROR, "混凝土签收信息通知erp发生异常");
            }

        }

        int count = shipBillMapper.countDeliveringWaybill(shipBill.getVehicleId());
        if (count == 0) {
            shipBillMapper.updateVehicleStatus(completeShipBillDTO.getWaybillId(), VehicleStatusEnum.CAN_DISPATCH.getCode(), completeShipBillDTO.getOperationUserId());
        }
        if (CsStringUtils.equals(shipBill.getStatus(), ShipBillStatusEnum.COMPLETE.getCode())) {
            this.generateBillCheckWaybillInfoList(completeShipBillDTO.getWaybillId());
        }

        if (CsStringUtils.equals(shipBill.getTransportToolType(), TransportToolTypeEnum.ROAD_TRANSPORT.getCode()) &&
                (CsStringUtils.equals(shipBill.getType(), WaybillTypeEnum.PLATFORM_ASSIGNING.getCode()) ||
                        CsStringUtils.equals(shipBill.getType(), WaybillTypeEnum.SOCIETY_SNATCH.getCode()))) {
            SocialDriverCarriageDTO socialDriverCarriageDTO = new SocialDriverCarriageDTO();
            BeanUtils.copyProperties(shipBill, socialDriverCarriageDTO);
            DeliveryInfo realBuyerDeliveryInfo = deliveryInfoBizService.selectRealBuyerDeliveryInfoByWaybillId(shipBill.getWaybillId());
            if (realBuyerDeliveryInfo != null) {
                socialDriverCarriageDTO.setReceiveAddressId(realBuyerDeliveryInfo.getReceiverAddressId());
                socialDriverCarriageDTO.setReceiveAddress(realBuyerDeliveryInfo.getProvince()
                        + realBuyerDeliveryInfo.getCity()
                        + realBuyerDeliveryInfo.getDistrict()
                        + realBuyerDeliveryInfo.getAddress());
            }
            Vehicle vehicle = vehicleMapper.selectByPrimaryKey(shipBill.getVehicleId());
            socialDriverCarriageDTO.setAccountId(shipBill.getDriverId());
            socialDriverCarriageDTO.setMemberId(vehicle.getUserId());

            try {
                log.info("社会运力司机结算信息:{}", socialDriverCarriageDTO);
                messageQueueService.sendMQWithoutProfile(socialDriverCarriageDTO, LogisticsMessageTypeEnum.SEND_SOCIAL_DRIVER_CARRIAGE.getCode());
                messageQueueService.sendMQ(shipBill.getWaybillNum(), LogisticsMessageTypeEnum.CARRY_SUBMIT_WAYBILL_FINISH.getCode());
            } catch (Exception e) {
                log.error("社会运力司机结算信息推送到支付发生异常:{}", socialDriverCarriageDTO, e);
            }
        }

        //如果是内部调拨单，则同步完成内部调拨单据
        syncUpdateInternalPlanStatus(completeShipBillDTO, shipBill);

        return new ItemResult<>(null);
    }

    private void syncUpdateInternalPlanStatus(CompleteShipBillDTO completeShipBillDTO, ShipBill shipBill) {
        if (CsStringUtils.equals("01", shipBill.getInternal())) {
            DeliveryInfo deliveryInfo = deliveryInfoBizService.selectDeliveryInfoByWaybillId(shipBill.getWaybillId());
            if (deliveryInfo == null || CsStringUtils.isBlank(deliveryInfo.getDeliveryInfoId())) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "找不到配送信息，无法完成运单！");
            }
            InternalAllocationDTO dto = new InternalAllocationDTO();
            //1待发货,2发货中,3已取消,4已改派,5已完成
            dto.setPlanStatus(PlanStatusEnum.COMPLETED.getCode());
            dto.setUserId(completeShipBillDTO.getOperationUserId());
            dto.setUserName(completeShipBillDTO.getOperationUserName());
            //通过配送信息ID修改内部调拨单状态，（注意：不要运单ID修改，因为改航导致内部调拨单与运单对应关系不正确）
            dto.setDeliveryInfoId(deliveryInfo.getDeliveryInfoId());
            allocationBizService.updateInternalPlanStatus(dto);
        }
    }

    @Override
    public ItemResult<List<DeliveryDetailVehicleDTO>> queryShipBillByDeliveryBillId(String deliveryBillId) {
        return new ItemResult<>(shipBillQueryBizService.queryShipBillByDeliveryBillId(deliveryBillId));
    }

    @Override
    public ItemResult<ShipBillDetailDTO> getWaybillDetail(String waybillItemId) {
        return new ItemResult<>(shipBillQueryBizService.selectWaybillDetailByWaybillItemId(waybillItemId));
    }

    @Override
    public ItemResult<ShipBillDetailDTO> getWaybillDetailForDriverApp(String waybillId) {
        return new ItemResult<>(shipBillQueryBizService.getWaybillDetailForDriverApp(waybillId));
    }

    @Override
    public ItemResult<List<ShipBillOrderDetailDTO>> queryWaybillListByTakeCode(List<String> takeCodeList) {
        return new ItemResult<>(shipBillQueryBizService.queryWaybillListByTakeCode(takeCodeList));
    }

    @Override
    public ItemResult<List<TradeWaybillDTO>> queryWaybillListByDeliveryNum(String deliverySheetNum) {
        return new ItemResult<>(shipBillQueryBizService.queryWaybillListByDeliveryNum(deliverySheetNum));
    }

    @Override
    public ItemResult<List<PickingBillDTO>> queryPickingListByWaybillNum(List<String> waybillNumList) {
        return new ItemResult<>(shipBillQueryBizService.queryPickingListByWaybillNum(waybillNumList));
    }

    @Override
    public ItemResult<PageData<ShipBillWaitRerouteListDTO>> queryWaitRerouteShipBills(PageQuery<WaitRerouteShipBillCondDTO> pageQuery) {
        return new ItemResult<>(shipBillQueryBizService.queryWaitRerouteShipBills(pageQuery));
    }

    @Override
    public ItemResult<Void> cancelShipBill(CancelShipBillDTO cancelShipBillDTO) {
        ParentInfo parentInfo = shipBillQueryBizService.getParent(cancelShipBillDTO.getWaybillId());
        if (parentInfo.getHasParent()) {
            cancelShipBillDTO.setWaybillId(parentInfo.getParentId());
            shipBillBizService.cancelMergeShipBill(cancelShipBillDTO);
        } else {
            ShipBill shipBill = shipBillBizService.cancelShipBill(cancelShipBillDTO);
            //如果是内部调拨单，则同步完成内部调拨单据
            if (CsStringUtils.equals("01", shipBill.getInternal())) {
                InternalAllocationDTO dto = new InternalAllocationDTO();
                dto.setWaybillId(shipBill.getWaybillId());
                //1待发货,2发货中,3已取消4,已改派,5已完成
                dto.setPlanStatus(PlanStatusEnum.CANCELED.getCode());
                dto.setUserId(cancelShipBillDTO.getOperateUserId());
                dto.setUserName(cancelShipBillDTO.getOperateUserName());
                allocationBizService.updateInternalPlanStatus(dto);
            }
        }

        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> assignCanceledShipBill(ReassignShipBillDTO reassignShipBillDTO) {

        ParentInfo parentInfo = shipBillQueryBizService.getParent(reassignShipBillDTO.getWaybillId());

        if (CsStringUtils.equals(parentInfo.getWaybillType(), WaybillTypeEnum.BUYER_PICK.getCode()) &&
                CsStringUtils.isBlank(reassignShipBillDTO.getVehicleId())) {
            //自提车辆ID为空,补充车辆
            DeliveryInfo deliveryInfo = deliveryInfoBizService.selectCanOperateByWaybillId(reassignShipBillDTO.getWaybillId());
            AssignDriverLogAddDTO assignDriverLogAddDTO = new AssignDriverLogAddDTO();
            BeanUtils.copyProperties(reassignShipBillDTO, assignDriverLogAddDTO);
            assignDriverLogAddDTO.setUserId(deliveryInfo.getBuyerId());
            assignDriverLogAddDTO.setUserType(UserRoleEnum.BUYER.getCode());
            assignDriverLogAddDTO.setUserName(deliveryInfo.getBuyerName());
            assignDriverLogAddDTO.setTransportCategoryIdList(Lists.newArrayList(deliveryInfo.getTransportCategoryId()));
            String vehicleId = assignDriverLogBizService.updateSelfPickAssignLog(assignDriverLogAddDTO);
            reassignShipBillDTO.setVehicleId(vehicleId);
        }

        if (parentInfo.getHasParent()) {
            reassignShipBillDTO.setWaybillId(parentInfo.getParentId());
            shipBillBizService.assignCanceledMergeShipBill(reassignShipBillDTO);
        } else {
            shipBillBizService.assignCanceledShipBill(reassignShipBillDTO);
        }
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> discardShipBill(CloseShipBillDTO closeShipBillDTO) {
        if (CsStringUtils.isNotEmpty(closeShipBillDTO.getOrderCode()))
        {
            List<ShipBillItem> items = shipBillItemMapper.getWaybillIdListByOrderCode(closeShipBillDTO.getOrderCode());
            log.info("根据订单编号关闭运单: {},{}", closeShipBillDTO.getOrderCode(), JSON.toJSONString(items));
            for(ShipBillItem item : items)
            {
                try
                {
                    CloseShipBillDTO billDTO = new CloseShipBillDTO();
                    billDTO.setWaybillId(item.getWaybillId());
                    billDTO.setCloseReason(closeShipBillDTO.getCloseReason());
                    closeShipBill(billDTO);
                }
                catch(Exception e)
                {
                    log.info("根据订单编号关闭运单ERR: {},{},{}", closeShipBillDTO.getOrderCode(), item.getWaybillNum(), e.getMessage());
                }
            }
        }
        else
        {
            closeShipBill(closeShipBillDTO);
        }

        return new ItemResult<>(null);
    }

    private void closeShipBill(CloseShipBillDTO closeShipBillDTO)
    {
        ParentInfo parentInfo = shipBillQueryBizService.getParent(closeShipBillDTO.getWaybillId());
        if (parentInfo.getHasParent()) {
            closeShipBillDTO.setWaybillId(parentInfo.getParentId());
            shipBillBizService.discardMergeMainShipBill(closeShipBillDTO);
        } else {
            shipBillBizService.discardShipBill(closeShipBillDTO);
        }
    }

    @Override
    public ItemResult<Void> uploadCertificate(UploadCertificateDTO uploadCertificateDTO) {
        // 保存附件
        AttListAddDTO attListAddDTO = uploadCertificateDTO.getAttListAddDTO();
        if (attListAddDTO != null && CollectionUtils.isNotEmpty(attListAddDTO.getAttAddListDTO())) {
            attListAddDTO.getAttAddListDTO().forEach(item -> {
                item.setCreateUser(uploadCertificateDTO.getOperationUserId());
            });
            attachmentBizService.saveAttList(uploadCertificateDTO.getAttListAddDTO());
        }
        //  更新运单是否上传支付凭证字段
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Integer> queryForbidCloseWaybillCountByTakeCode(ForBidCloseWaybillCountQueryDTO forBidCloseWaybillCountQueryDTO) {
        return new ItemResult<>(shipBillQueryBizService.queryForbidCloseWaybillCountByTakeCode(forBidCloseWaybillCountQueryDTO));
    }

    @Override
    public ItemResult<List<ShipBillDTO>> queryForbidCloseWaybillListByTakeCode(ForBidCloseWaybillCountQueryDTO forBidCloseWaybillCountQueryDTO)
    {
        List<ShipBill> list = shipBillQueryBizService.queryForbidCloseWaybillListByTakeCode(forBidCloseWaybillCountQueryDTO);

        List<ShipBillDTO> dataList = new ArrayList<>();
        for(ShipBill bill : list)
        {
            ShipBillStatusEnum shipBillStatusEnum = ShipBillStatusEnum.valueOfCode(bill.getStatus());
            WaybillExternalStatusEnum waybillExternalStatusEnum = WaybillExternalStatusEnum.valueOfCode(bill.getExternalWaybillStatus());

            ShipBillDTO dto = new ShipBillDTO();
            dto.setWaybillNum(bill.getWaybillNum());
            dto.setStatus(bill.getStatus());
            dto.setStatusName(shipBillStatusEnum == null ? bill.getStatus() : shipBillStatusEnum.getDesc());
            dto.setExternalWaybillStatusName(waybillExternalStatusEnum == null ? "" : waybillExternalStatusEnum.getDesc());
            dataList.add(dto);
        }

        return new ItemResult<>(dataList);
    }

    @Override
    public ItemResult<TradeWaybillDetailDTO> queryTradeWaybillDetail(String waybillId) {
        TradeWaybillDetailDO tradeWaybillDetailDO = shipBillQueryBizService.queryTradeWaybillDetail(waybillId);
        if (tradeWaybillDetailDO == null) {
            return null;
        }
        Boolean shipFlag = CsStringUtils.equals(tradeWaybillDetailDO.getTransportToolType(), TransportToolTypeEnum.WATER_TRANSPORT.getCode());
        String transportTool = shipFlag ? "船舶" : "车辆";
        String driver = shipFlag ? "船长" : "司机";

        TradeWaybillDetailDTO tradeWaybillDetailDTO = new TradeWaybillDetailDTO();
        BeanUtils.copyProperties(tradeWaybillDetailDO, tradeWaybillDetailDTO);
        tradeWaybillDetailDTO.setCreateTime(DateUtil.format(tradeWaybillDetailDO.getCreateTime(), YYYY_MM_DD_HH_MM_SS_FORMAT));
        verifyWarehouseIdIsNull(tradeWaybillDetailDO);

        //获取当前运单提货点信息
        WarehouseDetailsDTO warehouseDetailsDTO = warehouseService.queryWarehouseDetails(tradeWaybillDetailDO.getWarehouseId());
        handleWarehouseDetail(warehouseDetailsDTO, tradeWaybillDetailDTO);
        //获取运单操作记录
        QueryWaybillOperationDTO queryWaybillOperationDTO = new QueryWaybillOperationDTO();
        queryWaybillOperationDTO.setWaybillId(tradeWaybillDetailDTO.getWaybillId());
        List<OperationRecordDTO> operationRecordList = operationRecordBizService.queryWaybillOperation(queryWaybillOperationDTO);
        List<OperationRecordDTO> displayOperationRecordList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(operationRecordList)) {

            String unit = CsStringUtils.isBlank(tradeWaybillDetailDO.getProductUnit()) ? "吨" : tradeWaybillDetailDO.getProductUnit();
            //获取操作映射
            Map<String, List<Integer>> operationStatusMap = new HashMap<>();
            handleOperationStatusMap(tradeWaybillDetailDO, operationStatusMap);

            ShipBillStatusEnum shipBillStatusEnum = ShipBillStatusEnum.valueOfCode(tradeWaybillDetailDO.getStatus());
            verifyShipBillStatusEnumIsNull(shipBillStatusEnum, tradeWaybillDetailDO);

            Map<String, OperationRecordDTO> operationRecordMap = new HashMap<>();
            handleOperationRecordMap(shipBillStatusEnum, operationRecordList, operationRecordMap, operationStatusMap);

            for (Map.Entry<String, OperationRecordDTO> entry : operationRecordMap.entrySet()) {
                handleOperationRecordByWaitDelivery(entry, tradeWaybillDetailDO, transportTool, driver, tradeWaybillDetailDTO, unit, displayOperationRecordList);
                handleOperationRecordByDelivering(entry, transportTool, tradeWaybillDetailDTO, unit, displayOperationRecordList);
                handleOperationRecordByComplete(entry, tradeWaybillDetailDTO, tradeWaybillDetailDO, unit, displayOperationRecordList);
                handleOperationRecordByClosed(entry, displayOperationRecordList);
            }
        }
        for (OperationRecordDTO operationRecordDTO : operationRecordList) {
            handleOperationRecord(operationRecordDTO, displayOperationRecordList, transportTool);
        }
        displayOperationRecordList.sort(
                (o1, o2) -> {
                    if ((o2.getOperationTime().compareTo(o1.getOperationTime()) == 0)) {
                        return o2.getCreateTime().compareTo(o1.getCreateTime());
                    } else {
                        return o2.getOperationTime().compareTo(o1.getOperationTime());
                    }
                }
        );
        tradeWaybillDetailDTO.setOperationRecordList(displayOperationRecordList);
        return new ItemResult<>(tradeWaybillDetailDTO);
    }

    private static void handleOperationRecordByClosed(Map.Entry<String, OperationRecordDTO> entry, List<OperationRecordDTO> displayOperationRecordList) {
        if (entry.getKey().equals("已关闭") && entry.getValue() != null) {
            OperationRecordDTO operationRecordDTO = entry.getValue();
            operationRecordDTO.setOperationType(WaybillOperationTypeEnum.WAYBILL_CLOSED.getCode());
            operationRecordDTO.setStatusDesc("已关闭");
            String content = operationRecordDTO.getContent();
            if (CsStringUtils.contains(content, ":")) {
                String content1 = content.substring(0, content.indexOf(":"));
                operationRecordDTO.setContent("您的运单已关闭: " + content.substring(content1.length() + 1, content.length()));
            } else {
                operationRecordDTO.setContent("您的运单已关闭");
            }

            displayOperationRecordList.add(operationRecordDTO);
        }
    }

    private static void handleOperationRecordByComplete(Map.Entry<String, OperationRecordDTO> entry, TradeWaybillDetailDTO tradeWaybillDetailDTO, TradeWaybillDetailDO tradeWaybillDetailDO, String unit, List<OperationRecordDTO> displayOperationRecordList) {
        if (entry.getKey().equals("已签收") && entry.getValue() != null) {
            OperationRecordDTO operationRecordDTO = entry.getValue();
            operationRecordDTO.setOperationType(WaybillOperationTypeEnum.WAYBILL_COMPLETE.getCode());
            operationRecordDTO.setStatusDesc("已签收");
            BigDecimal signQuantity = LogisticsUtils.isGtZero(tradeWaybillDetailDTO.getSignQuantity()) ? tradeWaybillDetailDTO.getSignQuantity() : tradeWaybillDetailDTO.getActualQuantity();
            operationRecordDTO.setContent("您已在" + tradeWaybillDetailDO.getReceiveAddress() + "签收  实际签收量:" + signQuantity + unit);
            displayOperationRecordList.add(operationRecordDTO);
        }
    }

    private static void handleOperationRecordByDelivering(Map.Entry<String, OperationRecordDTO> entry, String transportTool, TradeWaybillDetailDTO tradeWaybillDetailDTO, String unit, List<OperationRecordDTO> displayOperationRecordList) {
        if (entry.getKey().equals("配送中") && entry.getValue() != null) {
            OperationRecordDTO operationRecordDTO = entry.getValue();
            operationRecordDTO.setStatusDesc("配送中");
            operationRecordDTO.setContent(transportTool + "已从出货点出站，正在快马加鞭的送到您的手中  实际出厂量:" + tradeWaybillDetailDTO.getActualQuantity() + unit);
            displayOperationRecordList.add(operationRecordDTO);
        }
    }

    private static void handleOperationRecordByWaitDelivery(Map.Entry<String, OperationRecordDTO> entry,
                                                            TradeWaybillDetailDO tradeWaybillDetailDO,
                                                            String transportTool,
                                                            String driver,
                                                            TradeWaybillDetailDTO tradeWaybillDetailDTO,
                                                            String unit,
                                                            List<OperationRecordDTO> displayOperationRecordList) {
        if (entry.getKey().equals("待配送") && entry.getValue() != null) {
            OperationRecordDTO operationRecordDTO = entry.getValue();
            operationRecordDTO.setStatusDesc("待配送");
            List<String> contentList = new ArrayList<>();
            if (CsStringUtils.isNotEmpty(tradeWaybillDetailDO.getVehicleNum())) {
                contentList.add(transportTool + "号：" + tradeWaybillDetailDO.getVehicleNum());
            }
            if (CsStringUtils.isNotEmpty(tradeWaybillDetailDO.getDriverName())) {
                contentList.add("配送" + driver + "：" + tradeWaybillDetailDO.getDriverName());
                contentList.add("联系电话：" + tradeWaybillDetailDO.getDriverPhone());
            }
            operationRecordDTO.setContent("已为您安排配送[" + CsStringUtils.join(contentList, ",") + "]  计划发货量:" + tradeWaybillDetailDTO.getQuantity() + unit);
            displayOperationRecordList.add(operationRecordDTO);
        }
    }

    private static void handleOperationRecord(OperationRecordDTO operationRecordDTO,
                                              List<OperationRecordDTO> displayOperationRecordList,
                                              String transportTool) {
        if (LogisticsUtils.isSame(operationRecordDTO.getOperationType(), WaybillOperationTypeEnum.VEHICLE_ENTER.getCode())) {
            operationRecordDTO.setStatusDesc("已开单");
            operationRecordDTO.setContent("您的车辆已开单");
            displayOperationRecordList.add(operationRecordDTO);
        }
        if (LogisticsUtils.isSame(operationRecordDTO.getOperationType(), WaybillOperationTypeEnum.PASS_TARE.getCode())) {
            operationRecordDTO.setStatusDesc("过皮重");
            operationRecordDTO.setContent("您的车辆已过皮重");
            displayOperationRecordList.add(operationRecordDTO);
        }
        if (LogisticsUtils.isSame(operationRecordDTO.getOperationType(), WaybillOperationTypeEnum.PASS_ROUGH.getCode())) {
            operationRecordDTO.setStatusDesc("过毛重");
            operationRecordDTO.setContent("您的车辆已过毛重");
            displayOperationRecordList.add(operationRecordDTO);
        }
        if (LogisticsUtils.isSame(operationRecordDTO.getOperationType(), WaybillOperationTypeEnum.VEHICLE_LEAVE.getCode())
                && CsStringUtils.contains(operationRecordDTO.getContent(), "-->出厂量")) {
            operationRecordDTO.setStatusDesc("已出站");
            operationRecordDTO.setContent("您的" + transportTool + "已出站");
            displayOperationRecordList.add(operationRecordDTO);
        }
        if (LogisticsUtils.isSame(operationRecordDTO.getOperationType(), WaybillOperationTypeEnum.MATCH_PLAN.getCode())) {
            operationRecordDTO.setStatusDesc("已匹配计划");
            operationRecordDTO.setContent("ERP已匹配计划");
            displayOperationRecordList.add(operationRecordDTO);
        }
        if (LogisticsUtils.isSame(operationRecordDTO.getOperationType(), WaybillOperationTypeEnum.ERP_DISPATCH.getCode())) {
            operationRecordDTO.setStatusDesc("已调度");
            operationRecordDTO.setContent("ERP已调度");
            displayOperationRecordList.add(operationRecordDTO);
        }
        if (LogisticsUtils.isSame(operationRecordDTO.getOperationType(), WaybillOperationTypeEnum.START_SHIPMENT.getCode())) {
            operationRecordDTO.setStatusDesc("开始装船");
            operationRecordDTO.setContent("ERP开始装船");
            displayOperationRecordList.add(operationRecordDTO);
        }
        if (LogisticsUtils.isSame(operationRecordDTO.getOperationType(), WaybillOperationTypeEnum.COMPLETE_SHIPMENT.getCode())) {
            operationRecordDTO.setStatusDesc("完成装船");
            operationRecordDTO.setContent("ERP完成装船");
            displayOperationRecordList.add(operationRecordDTO);
        }
        if (LogisticsUtils.isSame(operationRecordDTO.getOperationType(), WaybillOperationTypeEnum.OPEN_CABIN.getCode())) {
            operationRecordDTO.setStatusDesc("已开仓");
            operationRecordDTO.setContent("开仓成功");
            displayOperationRecordList.add(operationRecordDTO);
        }
    }

    private static void handleOperationRecordMap(ShipBillStatusEnum shipBillStatusEnum,
                                                 List<OperationRecordDTO> operationRecordList,
                                                 Map<String, OperationRecordDTO> operationRecordMap,
                                                 Map<String, List<Integer>> operationStatusMap) {
        switch (shipBillStatusEnum) {
            case WAIT_DELIVERY:
                handleMapByWaitDelivery(operationRecordList, operationRecordMap, operationStatusMap);
                break;
            case DELIVERING:
                handleMapByDelivering(operationRecordList, operationRecordMap, operationStatusMap);
                break;
            case COMPLETE:
                handleMapByComplete(operationRecordList, operationRecordMap, operationStatusMap);
                break;
            case CLOSED:
                handleMapByClosed(operationRecordList, operationRecordMap, operationStatusMap);
                break;
        }
    }

    private static void handleMapByClosed(List<OperationRecordDTO> operationRecordList, Map<String, OperationRecordDTO> operationRecordMap, Map<String, List<Integer>> operationStatusMap) {
        for (OperationRecordDTO operationRecordDTO : operationRecordList) {
            if (operationRecordMap.get("已签收") == null && operationStatusMap.get("已签收") != null &&
                    operationStatusMap.get("已签收").contains(operationRecordDTO.getOperationType())) {
                operationRecordMap.put("已签收", operationRecordDTO);
            }
            if (operationRecordMap.get("配送中") == null && operationStatusMap.get("配送中") != null &&
                    operationStatusMap.get("配送中").contains(operationRecordDTO.getOperationType())) {
                operationRecordMap.put("配送中", operationRecordDTO);
            }
            if (operationRecordMap.get("待配送") == null && operationStatusMap.get("待配送") != null &&
                    operationStatusMap.get("待配送").contains(operationRecordDTO.getOperationType())) {
                operationRecordMap.put("待配送", operationRecordDTO);
                break;
            }
            if (operationRecordMap.get("已关闭") == null && operationStatusMap.get("已关闭") != null &&
                    operationStatusMap.get("已关闭").contains(operationRecordDTO.getOperationType())) {
                operationRecordMap.put("已关闭", operationRecordDTO);
                break;
            }
        }
    }

    private static void handleMapByComplete(List<OperationRecordDTO> operationRecordList, Map<String, OperationRecordDTO> operationRecordMap, Map<String, List<Integer>> operationStatusMap) {
        for (OperationRecordDTO operationRecordDTO : operationRecordList) {
            if (operationRecordMap.get("已签收") == null && operationStatusMap.get("已签收") != null &&
                    operationStatusMap.get("已签收").contains(operationRecordDTO.getOperationType())) {
                operationRecordMap.put("已签收", operationRecordDTO);
            }
            if (operationRecordMap.get("配送中") == null && operationStatusMap.get("配送中") != null &&
                    operationStatusMap.get("配送中").contains(operationRecordDTO.getOperationType())) {
                operationRecordMap.put("配送中", operationRecordDTO);
            }
            if (operationRecordMap.get("待配送") == null && operationStatusMap.get("待配送") != null &&
                    operationStatusMap.get("待配送").contains(operationRecordDTO.getOperationType())) {
                operationRecordMap.put("待配送", operationRecordDTO);
                break;
            }
        }
    }

    private static void handleMapByDelivering(List<OperationRecordDTO> operationRecordList, Map<String, OperationRecordDTO> operationRecordMap, Map<String, List<Integer>> operationStatusMap) {
        for (OperationRecordDTO operationRecordDTO : operationRecordList) {
            if (operationRecordMap.get("配送中") == null && operationStatusMap.get("配送中") != null &&
                    operationStatusMap.get("配送中").contains(operationRecordDTO.getOperationType())) {
                operationRecordMap.put("配送中", operationRecordDTO);
            }
            if (operationRecordMap.get("待配送") == null && operationStatusMap.get("待配送") != null &&
                    operationStatusMap.get("待配送").contains(operationRecordDTO.getOperationType())) {
                operationRecordMap.put("待配送", operationRecordDTO);
                break;
            }
        }
    }

    private static void handleMapByWaitDelivery(List<OperationRecordDTO> operationRecordList, Map<String, OperationRecordDTO> operationRecordMap, Map<String, List<Integer>> operationStatusMap) {
        for (OperationRecordDTO operationRecordDTO : operationRecordList) {
            if (operationRecordMap.get("待配送") == null && operationStatusMap.get("待配送") != null &&
                    operationStatusMap.get("待配送").contains(operationRecordDTO.getOperationType())) {
                operationRecordMap.put("待配送", operationRecordDTO);
                break;
            }
        }
    }

    private static void verifyShipBillStatusEnumIsNull(ShipBillStatusEnum shipBillStatusEnum, TradeWaybillDetailDO tradeWaybillDetailDO) {
        if (shipBillStatusEnum == null) {
            throw new BizException(BasicCode.INVALID_PARAM, "无效的运单状态:status=" + tradeWaybillDetailDO.getStatus());
        }
    }

    private static void handleOperationStatusMap(TradeWaybillDetailDO tradeWaybillDetailDO, Map<String, List<Integer>> operationStatusMap) {
        if (WaybillTypeEnum.SELLER_DELIVERY.getCode().equals(tradeWaybillDetailDO.getType())) {
            operationStatusMap.put("待配送", Lists.newArrayList(WaybillOperationTypeEnum.WAYBILL_NEW.getCode()));
            operationStatusMap.put("配送中", Lists.newArrayList(WaybillOperationTypeEnum.VEHICLE_LEAVE.getCode()));
            operationStatusMap.put("已签收", Lists.newArrayList(WaybillOperationTypeEnum.WAYBILL_COMPLETE.getCode()));
            operationStatusMap.put("已关闭", Lists.newArrayList(WaybillOperationTypeEnum.WAYBILL_CLOSED.getCode(), WaybillOperationTypeEnum.WAYBILL_DISCARD.getCode()));
        } else if (WaybillTypeEnum.BUYER_PICK.getCode().equals(tradeWaybillDetailDO.getType())) {
            operationStatusMap.put("待配送", Lists.newArrayList(WaybillOperationTypeEnum.WAYBILL_NEW.getCode()));
            operationStatusMap.put("已签收", Lists.newArrayList(WaybillOperationTypeEnum.VEHICLE_LEAVE.getCode()));
            operationStatusMap.put("已关闭", Lists.newArrayList(WaybillOperationTypeEnum.WAYBILL_CLOSED.getCode(), WaybillOperationTypeEnum.WAYBILL_DISCARD.getCode()));
        } else if (WaybillTypeEnum.SOCIETY_SNATCH.getCode().equals(tradeWaybillDetailDO.getType()) ||
                WaybillTypeEnum.CARRIER_ASSIGNING.getCode().equals(tradeWaybillDetailDO.getType())) {
            operationStatusMap.put("待配送", Lists.newArrayList(WaybillOperationTypeEnum.WAYBILL_SNATCH.getCode(),
                    WaybillOperationTypeEnum.WAYBILL_CONFIRM.getCode()));
            operationStatusMap.put("配送中", Lists.newArrayList(WaybillOperationTypeEnum.VEHICLE_LEAVE.getCode()));
            operationStatusMap.put("已签收", Lists.newArrayList(WaybillOperationTypeEnum.WAYBILL_COMPLETE.getCode()));
            operationStatusMap.put("已关闭", Lists.newArrayList(WaybillOperationTypeEnum.WAYBILL_CLOSED.getCode(), WaybillOperationTypeEnum.WAYBILL_DISCARD.getCode()));
        } else {
            operationStatusMap.put("待配送", Lists.newArrayList(WaybillOperationTypeEnum.WAYBILL_NEW.getCode()));
            operationStatusMap.put("已签收", Lists.newArrayList(WaybillOperationTypeEnum.WAYBILL_COMPLETE.getCode()));
            operationStatusMap.put("已关闭", Lists.newArrayList(WaybillOperationTypeEnum.WAYBILL_CLOSED.getCode(), WaybillOperationTypeEnum.WAYBILL_DISCARD.getCode()));
        }
    }

    private static void handleWarehouseDetail(WarehouseDetailsDTO warehouseDetailsDTO, TradeWaybillDetailDTO tradeWaybillDetailDTO) {
        if (warehouseDetailsDTO != null) {
            tradeWaybillDetailDTO.setWarehouseName(warehouseDetailsDTO.getName());
            tradeWaybillDetailDTO.setWarehouseAddress(warehouseDetailsDTO.getProvince() + warehouseDetailsDTO.getCity() + warehouseDetailsDTO.getDistrict() + warehouseDetailsDTO.getAddress());
            tradeWaybillDetailDTO.setWarehouseAddressLocation(warehouseDetailsDTO.getLocation());
            tradeWaybillDetailDTO.setWarehousePhone(warehouseDetailsDTO.getAdministratorPhone());
        }
    }

    private static void verifyWarehouseIdIsNull(TradeWaybillDetailDO tradeWaybillDetailDO) {
        if (CsStringUtils.isEmpty(tradeWaybillDetailDO.getWarehouseId())) {
            throw new BizException(BasicCode.DATA_NOT_EXIST, "warehouseId=null");
        }
    }

    @Override
    public ItemResult<TradeWaybillDetailDTO> queryTradeWaybillDetailByNum(String waybillNum) {
        ShipBill shipBill = shipBillQueryBizService.queryWaybillByWaybillNum(waybillNum);
        ItemResult<TradeWaybillDetailDTO> itemResult = queryTradeWaybillDetail(shipBill.getWaybillId());
        itemResult.getData().setExternalWaybillNum(shipBill.getExternalWaybillNum());
        return itemResult;
    }

    @Override
    public ItemResult<Void> passCheck(PassCheckDTO passCheckDTO) {
        shipBillBizService.passCheck(passCheckDTO);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> batchPassCheck(BatchPassCheckDTO batchPassCheckDTO) {
        shipBillBizService.batchPassCheck(batchPassCheckDTO);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<PageData<WaitCheckWaybillListDTO>> queryCheckWaybillList(PageQuery<CheckWaybillListQueryDTO> pageQuery) {
        return new ItemResult<>(shipBillQueryBizService.queryCheckWaybillList(pageQuery));
    }

    @Override
    public ItemResult<PageData<WarehouseAdminWaitDeliveryDTO>> getWarehouseAdminWaitDelivery(PageQuery<WarehouseAdminWaitDeliveryQueryDTO> pageQuery) {
        return new ItemResult<>(shipBillQueryBizService.queryWarehouseAdminWaitDelivery(pageQuery));
    }

    @Override
    public ItemResult<PageData<WarehouseAdminProccessedDTO>> getWarehouseAdminProccessed(PageQuery<WarehouseAdminProccessedQueryDTO> pageQuery) {
        return new ItemResult<>(shipBillQueryBizService.queryWarehouseAdminProccessed(pageQuery));
    }

    @Override
    public ItemResult<Void> enterFactory(EnterFactoryDTO dto) {
        shipBillBizService.enterFactory(dto);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> withdrawEnterFactory(EnterFactoryDTO dto) {
        shipBillBizService.withdrawEnterFactory(dto);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> assignWaybill(WaybillAssignDTO waybillAssignDTO) {
        shipBillBizService.assignWaybill(waybillAssignDTO);
        return new ItemResult<>(null);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ItemResult<Void> confirmWaybillByDriver(DriverConfirmDTO driverConfirmDTO) {
        ConfirmWaybillDO confirmWaybillDO = new ConfirmWaybillDO();
        confirmWaybillDO.setWaybillId(driverConfirmDTO.getWaybillId());
        confirmWaybillDO.setStatus(ShipBillStatusEnum.WAIT_DELIVERY.getCode());
        confirmWaybillDO.setUserId(driverConfirmDTO.getOperationUserId());
        ShipBill shipBill = shipBillQueryBizService.selectShipBillById(driverConfirmDTO.getWaybillId());
        confirmWaybillDO.setEstimateKm(shipBill.getEstimateKm());
        confirmWaybillDO.setEstimateDuration(shipBill.getEstimateDuration());
        String driverId;
        if (CsStringUtils.equals(shipBill.getDriverId(), shipBill.getCarrierId())) {
            //个体司机
            driverId = driverConfirmDTO.getUserId();
        } else {
            driverId = driverConfirmDTO.getOperationUserId();
        }

        //  同步到trace: 社会运力调用监控前准备;承运商指派的调用发布运力
        //  运费设置到DO

        shipBillBizService.confirmWaybillByDriver(confirmWaybillDO);

        //添加操作记录
        OperationRecordAddDTO operationRecordAddDTO = new OperationRecordAddDTO();
        operationRecordAddDTO.setEntryId(driverConfirmDTO.getWaybillId());
        operationRecordAddDTO.setType(OperationRecordTypeEnum.WAYBILL.getCode());
        operationRecordAddDTO.setOperationType(WaybillOperationTypeEnum.WAYBILL_CONFIRM.getCode());
        operationRecordAddDTO.setOperatorId(driverConfirmDTO.getOperationUserId());
        operationRecordAddDTO.setOperatorName(driverConfirmDTO.getOperationUserName());
        operationRecordAddDTO.setContent("司机确认");
        operationRecordBizService.saveOperationRecord(operationRecordAddDTO);

        //同步外部系统
        if (CsStringUtils.equals(shipBill.getSyncFlag(), ExternalSyncFlagEnum.FORWARD_SYNC.getCode())) {
            ExternalWaybillRequestDTO externalWaybillRequestDTO = new ExternalWaybillRequestDTO();
            externalWaybillRequestDTO.setWaybillId(driverConfirmDTO.getWaybillId());
            externalWaybillRequestDTO.setOperatorUserId(driverConfirmDTO.getOperationUserId());
            externalWaybillRequestDTO.setOperatorUserName(driverConfirmDTO.getOperationUserName());
            if (CsStringUtils.isBlank(shipBill.getExternalWaybillNum())) {
                //同步创建外部运单
                createExternalWaybill(shipBill, externalWaybillRequestDTO);
            } else {
                //修改车辆
                if (CsStringUtils.equals(TransportToolTypeEnum.ROAD_TRANSPORT.getCode(), shipBill.getTransportToolType())) {
                    externalWaybillRequestDTO.setVehicleNum(shipBill.getVehicleNum());
                    ItemResult<Void> itemResult = waybillNewExternalService.updateExternalWaybill(externalWaybillRequestDTO);
                    if (CsStringUtils.equals(itemResult.getCode(), "E0B02D15")) {
                        log.error("运单修改_ERP处理失败：{}", JSON.toJSONString(itemResult));
                        throw new BizException(LogisticsErrorCode.UNABLE_MODIFY_WAYBILL);
                    }
                }else {
                    shipBillExternalService.modifyExternalShipBill(externalWaybillRequestDTO);
                }
            }
        }

        return new ItemResult<>(null);
    }

    private void createExternalWaybill(ShipBill shipBill, ExternalWaybillRequestDTO externalWaybillRequestDTO) {
        if (CsStringUtils.equals(TransportToolTypeEnum.ROAD_TRANSPORT.getCode(), shipBill.getTransportToolType())) {
            waybillNewExternalService.createExternalWaybill(externalWaybillRequestDTO);
        }else {
            shipBillExternalService.createExternalShipBill(externalWaybillRequestDTO);
        }
    }

    @Override
    public ItemResult<Void> rejectWaybillByDriver(DriverConfirmDTO driverConfirmDTO) {
        shipBillBizService.rejectWaybillByDriver(driverConfirmDTO);

        //添加操作记录
        OperationRecordAddDTO operationRecordAddDTO = new OperationRecordAddDTO();
        operationRecordAddDTO.setEntryId(driverConfirmDTO.getWaybillId());
        operationRecordAddDTO.setType(OperationRecordTypeEnum.WAYBILL.getCode());
        operationRecordAddDTO.setOperationType(WaybillOperationTypeEnum.WAYBILL_REJECT.getCode());
        operationRecordAddDTO.setOperatorId(driverConfirmDTO.getOperationUserId());
        operationRecordAddDTO.setOperatorName(driverConfirmDTO.getOperationUserName());
        operationRecordAddDTO.setContent("司机拒绝");
        operationRecordBizService.saveOperationRecord(operationRecordAddDTO);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<List<WarehouseOptionDTO>> selectSeckillWarehouseByUserId(String userId) {
        return new ItemResult<>(shipBillQueryBizService.selectSeckillWarehouseByUserId(userId));
    }

    @Override
    public ItemResult<List<DistrictListDTO>> selectSeckillDistrictsByUserId(String userId) {
        return new ItemResult<>(shipBillQueryBizService.selectSeckillDistrictsByUserId(userId));
    }

    @Override
    public ItemResult<PageData<UserSeckillWaybillListDTO>> selectUserSeckillWaybillList(PageQuery<UserSeckillWaybillQueryDTO> pageQuery) {
        return new ItemResult<>(shipBillQueryBizService.selectUserSeckillWaybillList(pageQuery));
    }

    @Override
    public ItemResult<PageData<UserSeckillWaybillListDTO>> selectUserSeckilledWaybillList(PageQuery<DriverWaybillListQueryDTO> pageQuery) {
        return new ItemResult<>(shipBillQueryBizService.selectUserSeckilledWaybillList(pageQuery));
    }

    @Override
    public ItemResult<Void> snatchWaybill(SnatchWaybillDTO snatchWaybillDTO) {
        log.info("开始抢单=====snatchWaybillDTO:{}", JSON.toJSONString(snatchWaybillDTO));
        String code = shipBillBizService.snatchWaybill(snatchWaybillDTO);
        ShipBill shipBill = shipBillQueryBizService.selectShipBillById(snatchWaybillDTO.getWaybillId());
        if (snatchWaybillDTO.getUserType().equals(UserRoleEnum.PERSONAL_DRIVER.getCode()) &&
                code.equals(SnatchWaybillReturnEnum.SUCCESS.getCode())) {

            //位置中心监控数据准备

            //同步创建外部运单
            if (ExternalSyncFlagEnum.FORWARD_SYNC.getCode().equals(shipBill.getSyncFlag())) {
                ExternalWaybillRequestDTO externalWaybillRequestDTO = new ExternalWaybillRequestDTO();
                externalWaybillRequestDTO.setWaybillId(snatchWaybillDTO.getWaybillId());
                externalWaybillRequestDTO.setOperatorUserId(snatchWaybillDTO.getUserId());
                externalWaybillRequestDTO.setOperatorUserName(snatchWaybillDTO.getUserName());
                if (CsStringUtils.isBlank(shipBill.getExternalWaybillStatus())) {
                    //同步创建外部运单
                    waybillNewExternalService.createExternalWaybill(externalWaybillRequestDTO);
                } else {
                    //修改车辆
                    externalWaybillRequestDTO.setVehicleNum(shipBill.getVehicleNum());
                    ItemResult<Void> itemResult = waybillNewExternalService.updateExternalWaybill(externalWaybillRequestDTO);
                    if (CsStringUtils.equals(itemResult.getCode(), "E0B02D15")) {
                        log.error("运单修改_ERP处理失败：{}", itemResult);
                        throw new BizException(LogisticsErrorCode.UNABLE_MODIFY_WAYBILL);
                    }
                }
            }
        }
        ItemResult<Void> result = new ItemResult<>();
        SnatchWaybillReturnEnum snatchWaybillReturnEnum = SnatchWaybillReturnEnum.valueOfCode(code);
        result.setDescription(snatchWaybillReturnEnum != null ? snatchWaybillReturnEnum.getDesc() : "");
        result.setSuccess(Boolean.TRUE);
        result.setCode(code);
        return result;
    }

    @Override
    public ItemResult<Void> changeArriveDestinationTime(String waybillId) {
        shipBillOperateBizService.changeArriveDestinationTime(waybillId);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> arriveWarehouse(ArriveWarehouseDTO dto) {
        shipBillOperateBizService.arriveWarehouse(dto);
        //通知位置中心到达提货点

        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<DriverDeliveryDTO> queryDriverDeliveryStatus(String driverId) {
        Integer halfWaybillCount = shipBillQueryBizService.queryDriverDeliveryStatus(driverId);
        DriverDeliveryDTO driverDeliveryDTO = new DriverDeliveryDTO();
        driverDeliveryDTO.setIsHalfWaybill(halfWaybillCount > 0 ? Boolean.TRUE : Boolean.FALSE);
        driverDeliveryDTO.setHalfWayVehicleNumList(shipBillQueryBizService.queryHalfWayVehicleNum(driverId));
        return new ItemResult<>(driverDeliveryDTO);
    }

    @Override
    public ItemResult<PageData<ShipWaybillListDTO>> queryShipWaybillList(PageQuery<ShipWaybillQueryDTO> pageQuery) {
        return new ItemResult<>(shipBillQueryBizService.queryShipWaybillList(pageQuery));
    }

    @Override
    public ItemResult<Void> carrierAssignWaybillToDriver(DriverWaybillAssignDTO dto) {
        shipBillBizService.carrierAssignWaybillToDriver(dto);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> openCabinRemind(String waybillItemId) {
        smsMessageBizService.openCabinRemind(waybillItemId);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> unloadingRemind(String waybillItemId) {
        smsMessageBizService.unloadingRemind(waybillItemId);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<List<EvaluateWaybillListDTO>> queryEvaluateWaybillList(List<String> deliverySheetNumList) {
        return new ItemResult<>(shipBillQueryBizService.queryEvaluateWaybillList(deliverySheetNumList));
    }

    @Override
    public ItemResult<List<SnatchedWaybillDTO>> querySnatchedWaybillList(SnatchedWaybillQueryDTO snatchedWaybillQueryDTO) {
        return new ItemResult<>(shipBillQueryBizService.querySnatchedWaybillList(snatchedWaybillQueryDTO));
    }

    @Override
    public ItemResult<List<EmallPublishWaybillListDTO>> selectWaybillForEmall(EmallPublishWaybillQueryDTO emallPublishWaybillQueryDTO) {
        return new ItemResult<>(shipBillQueryBizService.selectWaybillForEmall(emallPublishWaybillQueryDTO));
    }

    @Override
    public ItemResult<PublishWaybillListDTO> selectWaybillForNum(String waybillNum) {
        return new ItemResult<>(shipBillQueryBizService.selectWaybillForNum(waybillNum));
    }

    @Override
    public ItemResult<PageData<CheckWaybillInfoDTO>> queryBillCheckWaybillInfoList(PageQuery<CheckWaybillQueryDTO> pageQuery) {
        log.info("queryBillCheckWaybillInfoList:{}",pageQuery);
        CheckWaybillQueryDTO checkWaybillQueryDTO = pageQuery.getQueryDTO() == null ?
                new CheckWaybillQueryDTO() : pageQuery.getQueryDTO();
        if (CsStringUtils.equals(MemberPlatform.PLATFORM_MEMBERID.getId(), checkWaybillQueryDTO.getCarrierId())) {
            checkWaybillQueryDTO.setCarrierId(LogisticsCommonBean.PLATFORM_ID);
        }
        if (CsStringUtils.equals(MemberPlatform.PLATFORM_MEMBERID.getId(), checkWaybillQueryDTO.getConsignorId())) {
            checkWaybillQueryDTO.setConsignorId(LogisticsCommonBean.PLATFORM_ID);
        }
        PageInfo<CheckWaybillInfoDTO> pageInfo = PageMethod
                .startPage(ObjectUtil.defaultIfNull(pageQuery.getPageNum(), NumberConstant.DEFAULT_PAGE_NUM),
                        ObjectUtil.defaultIfNull(pageQuery.getPageSize(), NumberConstant.DEFAULT_PAGE_SIZE),
                        true, false, null).doSelectPageInfo(
                () -> shipBillItemMapper.queryBillCheckWaybillInfoList(checkWaybillQueryDTO));
        log.info("返回结果:{}",pageInfo);
        if (CollectionUtils.isEmpty(pageInfo.getList())) {
            return new ItemResult<>(new PageData<>(new ArrayList<>()));
        }
        return new ItemResult<>(new PageData<>(pageInfo));
    }

    @Override
    public ItemResult<BigDecimal> estimateKm(EstimateKmQueryDTO queryDTO) {
        log.info("开始预估距离:{}",queryDTO);

        List<VehicleType> vehicleTypeList = Lists.newArrayList();

        if (CsStringUtils.isNotBlank(queryDTO.getVehicleTypeId())) {
            VehicleTypeDetailDTO vehicleTypeDetailDTO = vehicleTypeBizService.queryVehicleType(queryDTO.getVehicleTypeId());
            if (vehicleTypeDetailDTO != null) {
                VehicleType vehicleType = new VehicleType();
                BeanUtils.copyProperties(vehicleTypeDetailDTO, vehicleType);
                vehicleTypeList.add(vehicleType);
            }
        } else {
            //获取运输品类匹配的车型数据
            vehicleTypeList = vehicleTypeBizService.
                    queryVehicleTypeByCondition(queryDTO.getTransportCategoryId());
        }
        //根据最大载重降序排列
        if(CollectionUtils.isNotEmpty(vehicleTypeList)){
            vehicleTypeList.sort((o1, o2)-> o2.getMaxLoadCapacity().compareTo(o1.getMaxLoadCapacity()));

            //调用车型路径规划
        }
        return new ItemResult<>(BigDecimal.ZERO);
    }

    @Override
    public ConcreteShipBillInfoDTO queryConcreteInfoByWaybillId(String waybillId) {
        return shipBillQueryBizService.queryConcreteInfoByWaybillId(waybillId);
    }

    @Override
    public ItemResult<List<CarryWaybillSubmitDTO>> selectCarryWaybill(String waybillNum) {
        return new ItemResult<>(shipBillQueryBizService.selectCarryWaybill(waybillNum));
    }

    @Override
    public ItemResult<Void> generateBillCheckWaybillInfoList(String waybillId) {
        try {
            //完成运单时生成对账单数据
            List<CheckWaybillInfoDTO> checkWaybillInfoDTOS = shipBillQueryBizService.queryBillCheckWaybillInfoList(waybillId);
            if (CollectionUtils.isNotEmpty(checkWaybillInfoDTOS)){
                //消息推送
                messageQueueService.sendMQ(checkWaybillInfoDTOS, LogisticsMessageTypeEnum.SEND_CHECK_WAYBILL.getCode());
            }
        }catch (Exception e){
            log.error("生成对账数据报错:{}",waybillId, e);
        }
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> generateBillCheckWaybill() {
        Condition condition = new Condition(ShipBill.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIsNotNull("completeTime");
        List<ShipBill> shipBills = shipBillMapper.selectByCondition(condition);
        for (ShipBill shipBill : shipBills){
            log.info("{}:开始生成对账数据",shipBill.getWaybillId());
            this.generateBillCheckWaybillInfoList(shipBill.getWaybillId());
        }
        return null;
    }

    @Override
    public ItemResult<ShipBillListDTO> againCreateExternalWaybill(String waybillId, String operationUserId, String operationUserName) throws BizException{
        ShipBillListDTO shipBillListDTO = new ShipBillListDTO();
        ShipBill shipbill = shipBillBizService.againCreateExternalWaybill(waybillId, operationUserId, operationUserName);
        if (shipbill != null) {
            BeanUtils.copyProperties(shipbill, shipBillListDTO);
            return new ItemResult<>(shipBillListDTO);
        }
        return new ItemResult<>(null);
    }


    @Override
    public ItemResult<TwoLeaveOptionDTO> queryPumpListByWaybillId(String waybillId) {
        log.info("查询运单的泵候选信息:{}", waybillId);
        TwoLeaveOptionDTO twoLeaveOptionDTO = new TwoLeaveOptionDTO();
        if (CsStringUtils.isBlank(waybillId)) {
            return new ItemResult<>(twoLeaveOptionDTO);
        }
        ShipBill shipBill = shipBillMapper.selectByPrimaryKey(waybillId);
        String allPumpNo = shipBill.getAllPumpNo();
        String allPumperName = shipBill.getAllPumperName();
        log.info("运单泵候选信息,处理前:{}/{}=>{}/{}", waybillId, shipBill.getWaybillNum(), allPumpNo, allPumperName);
        if (CsStringUtils.isBlank(allPumpNo) || CsStringUtils.isBlank(allPumperName)) {
            return new ItemResult<>(twoLeaveOptionDTO);
        }

        List<String> firstList = Lists.newArrayList();
        List<OneToManyOptionDTO> secondList = Lists.newArrayList();

        LinkedHashMap<String, List<String>> first2SecondListMap = Maps.newLinkedHashMap();

        String[] pumpNoArr = allPumpNo.split(",");
        String[] pumperNameArr = allPumperName.split(",");
        for (int i = 0; i < pumpNoArr.length; i++) {
            String pumpNo = pumpNoArr[i];
            String pumperName = pumperNameArr[i];
            if (firstList.contains(pumpNo)) {
                first2SecondListMap.get(pumpNo).add(pumperName);
            } else {
                firstList.add(pumpNo);
                List<String> valueList = Lists.newArrayList(pumperName);
                first2SecondListMap.put(pumpNo, valueList);
                OneToManyOptionDTO second = new OneToManyOptionDTO();
                second.setKey(pumpNo);
                second.setValueList(valueList);
                secondList.add(second);
            }
        }

        twoLeaveOptionDTO.setFirstList(firstList);
        twoLeaveOptionDTO.setSecondList(secondList);
        log.info("泵候选信息为:{}", JSON.toJSONString(twoLeaveOptionDTO));
        return new ItemResult<>(twoLeaveOptionDTO);
    }

    @Override
    public ItemResult<List<String>> queryPersonalDriverHalfWayWaybillNums(String personalDriverMemberId) {
        if (CsStringUtils.isBlank(personalDriverMemberId)) {
            return new ItemResult<>(null);
        }
        List<Vehicle> vehicles = vehicleMapper.selectVehicleInfoByUserId(personalDriverMemberId, UserRoleEnum.PERSONAL_DRIVER.getCode());
        if (CollectionUtils.isEmpty(vehicles)) {
            return new ItemResult<>(null);
        }
        String vehicleId = vehicles.get(0).getVehicleId();
        List<String> waybillNumList = shipBillMapper.queryWaybillNumByVehicleIdAndStatus(
                vehicleId,
                Lists.newArrayList(
                        ShipBillStatusEnum.WAIT_DELIVERY.getCode(),
                        ShipBillStatusEnum.DELIVERING.getCode(),
                        ShipBillStatusEnum.SIGNED.getCode()
                )
        );
        return new ItemResult<>(waybillNumList);
    }

    @Override
    public ItemResult<ContractSubmitInfoDTO> queryContractInfo(String waybillNum) {
        return new ItemResult<>(shipBillQueryBizService.queryContractInfo(waybillNum));
    }


    @Override
    public ItemResult<Void> refundShipBill(RefundShipBillDTO refundShipBillDTO) {
        shipBillCompleteBizService.refundShipBill(refundShipBillDTO);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<WaybillBriefDTO> queryLastSignWaybill(LastSignWaybillQueryDTO lastSignWaybillQueryDTO) {
        return new ItemResult<>(shipBillQueryBizService.queryLastSignWaybill(lastSignWaybillQueryDTO));
    }

    @Override
    public List<GoodsInfoDTO> queryGoodsNameListDropDownBox(QueryGoodsDTO queryGoodsDTO){
        return shipBillBizService.queryGoodsNameListDropDownBox(queryGoodsDTO);
    }

	@Override
	public List<String> queryVehicleOrShippingListByName(ShipBillQueryDTO queryDTO) {
		return shipBillBizService.queryVehicleOrShippingListByName(queryDTO);
	}

    @Override
    public TransportInfoDTO queryTransportByWaybillNum(String waybillNum) {
        if (CsStringUtils.isBlank(waybillNum)) {
            return null;
        }
        ShipBill shipBill = shipBillMapper.selectByWaybillNum(waybillNum);
        if (shipBill == null) {
            return null;
        }
        TransportInfoDTO transportInfoDTO = new TransportInfoDTO();
        transportInfoDTO.setWaybillNum(waybillNum);
        if (CsStringUtils.equals(shipBill.getTransportToolType(), TransportToolTypeEnum.ROAD_TRANSPORT.getCode())) {
            transportInfoDTO.setVehicleId(shipBill.getVehicleId());
            transportInfoDTO.setVehicleNum(shipBill.getVehicleNum());
            if (CsStringUtils.isBlank(shipBill.getVehicleId())) {
                transportInfoDTO.setUserName(shipBill.getDriverName());
            } else {
                Vehicle vehicle = vehicleMapper.selectByPrimaryKey(shipBill.getVehicleId());
                transportInfoDTO.setUserId(vehicle.getUserId());
                transportInfoDTO.setUserType(vehicle.getUserType());
                transportInfoDTO.setUserName(vehicle.getUserName());
            }
        } else {
            ShippingInfoDetailsDTO shippingInfoDetailsDTO = shippingInfoBizService.queryShippingInfoDetails(shipBill.getShippingId());
            transportInfoDTO.setVehicleNum(shippingInfoDetailsDTO.getShippingName());
            transportInfoDTO.setVehicleId(shippingInfoDetailsDTO.getShippingId());
            transportInfoDTO.setWaterFlag(1);
            // 10-企业承运商，11-个体船东承运商，20-卖家，30-买家
            String managerMemberType = shippingInfoDetailsDTO.getManagerMemberType();
            if (CsStringUtils.equals(managerMemberType, "20")) {
                transportInfoDTO.setUserType(UserRoleEnum.SELLER.getCode());
            } else if (CsStringUtils.equals(managerMemberType, "30")) {
                transportInfoDTO.setUserType(UserRoleEnum.BUYER.getCode());
            } else {
                transportInfoDTO.setUserType(UserRoleEnum.CARRIER.getCode());
            }

        }
        return transportInfoDTO;
    }

    @Override
    public ItemResult<List<QrCodeDTO>> queryWDQrCodeByOrderCode(String orderCode) {
        if (CsStringUtils.isBlank(orderCode)) {
            return new ItemResult<>(Lists.newArrayList());
        }
        List<QrCodeDTO> qrCodeDTOS = shipBillMapper.queryWDQrCodeByOrderCode(orderCode);
        if (qrCodeDTOS == null) {
            return new ItemResult<>(Lists.newArrayList());
        }
        return new ItemResult<>(qrCodeDTOS);
    }

    @Override
    public ShipBillBriResDTO queryShipBillBriByCond(ShipBillBriCondDTO shipBillBriCondDTO) {
        log.info("各端查询运单简要信息:{}", shipBillBriCondDTO);
        ShipBillBriResDTO resDTO = null;
        if (shipBillBriCondDTO == null || CsStringUtils.isBlank(shipBillBriCondDTO.getWaybillNum())) {
            return resDTO;
        }
        String appName = shipBillBriCondDTO.getAppName();
        String redisKey = "logistics:shipbill:" +
                shipBillBriCondDTO.getAccountId() + "#" +
                appName + "#" +
                shipBillBriCondDTO.getWaybillNum();
        ShipBillBriResDTO cacheBirResDTO = (ShipBillBriResDTO) bizRedisTemplate.opsForValue().get(redisKey);
        if (cacheBirResDTO != null) {
            log.info("缓存中获取的结果为:{}=>{}", redisKey, cacheBirResDTO);
            return cacheBirResDTO;
        }
        if (CsStringUtils.equals(appName, AppNames.WEB_SERVICE_SELLER.getCode())) {
            //卖家ID
            resDTO = shipBillMapper.queryTradeWaybillBri(shipBillBriCondDTO);
        } else if (CsStringUtils.equals(appName, AppNames.WEB_SERVICE_CARRIER.getCode())) {
            //承运商ID
            resDTO = shipBillMapper.queryCarryWaybillBri(shipBillBriCondDTO);
        } else if (CsStringUtils.equals(appName, AppNames.WEB_SERVICE_PLATFORM.getCode())) {
            //平台
            resDTO = shipBillMapper.queryTradeWaybillBri(shipBillBriCondDTO);
        } else {
            log.info("暂不支持此端查询");
            return null;
        }
        log.info("数据库中获取的结果为:{}=>{}", redisKey, resDTO);
        if (resDTO != null) {
            bizRedisTemplate.opsForValue().set(redisKey, resDTO, 30, TimeUnit.MINUTES);
        }
        return resDTO;
    }
}
