package com.ecommerce.logistics.service.impl;

import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.logistics.api.dto.shipping.erp.BindErpShippingDTO;
import com.ecommerce.logistics.api.dto.shipping.erp.ErpShipSyncDTO;
import com.ecommerce.logistics.api.dto.shipping.erp.ErpShippingInfoDTO;
import com.ecommerce.logistics.biz.IShippingErpInfoBizService;
import com.ecommerce.logistics.service.IShippingErpInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/1/11 17:51
 * @Version 1.0
 */
@Slf4j
@Service
public class ShippingErpInfoService implements IShippingErpInfoService {

    @Autowired
    private IShippingErpInfoBizService shippingErpInfoBizService;

    @Override
    public ItemResult<List<ErpShippingInfoDTO>> findErpShippingByErpCarrierNo(String erpCarrierNo, String manufacturerMemberId) {
        return new ItemResult<>(shippingErpInfoBizService.findErpShippingByErpCarrierNo(erpCarrierNo, manufacturerMemberId));
    }

    @Override
    public ItemResult<Boolean> syncErpInfo(ErpShipSyncDTO erpShipSyncDTO) throws BizException {
        return new ItemResult<>(shippingErpInfoBizService.syncErpInfo(erpShipSyncDTO));
    }

    /**
     * 电商和ERP船舶绑定
     * @param bindErpShippingDTO
     * @return
     */
    public ItemResult<Boolean> bindErpShippingInfo(BindErpShippingDTO bindErpShippingDTO) throws BizException {
        return new ItemResult<>(shippingErpInfoBizService.bindErpShippingInfo(bindErpShippingDTO));
    }
}
