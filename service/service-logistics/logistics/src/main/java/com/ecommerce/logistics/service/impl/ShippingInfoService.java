package com.ecommerce.logistics.service.impl;

import com.ecommerce.base.api.dto.wharf.WharfProvinceCityDTO;
import com.ecommerce.base.api.service.IAttachmentService;
import com.ecommerce.base.api.service.IWharfService;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.logistics.api.dto.attachment.AttListDTO;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.shipping.*;
import com.ecommerce.logistics.api.dto.shipping.erp.BindErpShippingDTO;
import com.ecommerce.logistics.biz.IShippingInfoBizService;
import com.ecommerce.logistics.service.IShippingInfoService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * Created on 2020/11/25 14:47
 */
@Slf4j
@Service
public class ShippingInfoService implements IShippingInfoService {

    @Autowired
    private IShippingInfoBizService shippingInfoBizService;
    @Autowired
    private IWharfService wharfService;
    @Autowired
    private IAttachmentService iAttachmentService;

    @Override
    public ItemResult<String> saveShippingInfo(ShippingInfoSaveDTO dto) throws BizException {
        return new ItemResult<>(shippingInfoBizService.saveShippingInfo(dto));
    }

    @Override
    public ItemResult<Void> delShippingInfo(ShippingBatchDTO dto) throws BizException {
        shippingInfoBizService.delShippingInfo(dto);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<String> submitShippingInfo(ShippingInfoSaveDTO dto) throws BizException {
        return new ItemResult<>(shippingInfoBizService.submitShippingInfo(dto));
    }

    @Override
    public ItemResult<Void> passShippingInfo(ShippingOperationDTO dto) throws BizException {
        shippingInfoBizService.passShippingInfo(dto);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> rejectShippingInfo(ShippingOperationDTO dto) throws BizException {
        shippingInfoBizService.rejectShippingInfo(dto);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<PageData<ShippingInfoListDTO>> queryShippingInfoListByPlatform(PageQuery<ShippingInfoListQueryDTO> pageQuery) throws BizException {
        return new ItemResult<>(shippingInfoBizService.queryShippingInfoListByPlatform(pageQuery));
    }

    @Override
    public ItemResult<PageData<ShippingInfoListDTO>> queryShippingInfoListByCarrier(PageQuery<ShippingInfoListQueryDTO> pageQuery) throws BizException {
        return new ItemResult<>(shippingInfoBizService.queryShippingInfoListByCarrier(pageQuery));
    }

    @Override
    public ItemResult<PageData<ShippingInfoListDTO>> queryShippingInfoListBySeller(PageQuery<ShippingInfoListQueryDTO> pageQuery) throws BizException {
        return new ItemResult<>(shippingInfoBizService.queryShippingInfoListBySeller(pageQuery));
    }

    @Override
    public ItemResult<PageData<ShippingInfoDTO>> queryShippingInfoListBySelf(PageQuery<ShippingInfoQueryDTO> pageQuery) throws BizException {
        return new ItemResult<>(shippingInfoBizService.queryShippingInfoListBySelf(pageQuery));
    }

    @Override
    public ItemResult<ShippingInfoDetailsDTO> queryShippingInfoDetailsByBuyer(String shippingId) throws BizException {
        ShippingInfoDetailsDTO shippingDetails = shippingInfoBizService.queryShippingInfoDetails(shippingId);
        handleRealFileUrl(shippingDetails);
        log.info("queryShippingInfoDetails详情接口：{}", shippingDetails);
        return new ItemResult<>(shippingDetails);
    }

    @Override
    public ItemResult<ShippingInfoDetailsDTO> queryShippingInfoDetails(String shippingId) throws BizException {
        ShippingInfoDetailsDTO shippingDetails = shippingInfoBizService.queryShippingInfoDetails(shippingId);

        if (CsStringUtils.isNotBlank(shippingDetails.getDepartureWharfId())) {
            //起运港ID
            List<String> departureWharfIdList = Arrays.asList(shippingDetails.getDepartureWharfId().split(","));
            if (CollectionUtils.isNotEmpty(departureWharfIdList)) {
                List<WharfProvinceCityDTO> departureDTOList = wharfService.getWharfInfoList(departureWharfIdList);
                if (CollectionUtils.isNotEmpty(departureDTOList)) {
                    List<List<String>> departureWharf = new LinkedList<>();
                    departureDTOList.forEach(item -> {
                        List<String> listDeparture = new LinkedList<>();
                        listDeparture.add(item.getProvinceCode());
                        listDeparture.add(item.getCityCode());
                        listDeparture.add(item.getWharfId());
                        departureWharf.addAll(Collections.singleton(listDeparture));
                    });
                    shippingDetails.setDepartureWharf(departureWharf);
                }
            }
        }

        if (CsStringUtils.isNotBlank(shippingDetails.getDestinationWharfId())) {
            //目的地港ID
            List<String> destinationWharfIdList = Arrays.asList(shippingDetails.getDestinationWharfId().split(","));
            if (CollectionUtils.isNotEmpty(destinationWharfIdList)) {
                List<WharfProvinceCityDTO> destinationDTOList = wharfService.getWharfInfoList(destinationWharfIdList);
                if (CollectionUtils.isNotEmpty(destinationDTOList)) {
                    List<List<String>> destinationWharf = new LinkedList<>();
                    destinationDTOList.forEach(item -> {
                        List<String> listDestination = new LinkedList<>();
                        listDestination.add(item.getProvinceCode());
                        listDestination.add(item.getCityCode());
                        listDestination.add(item.getWharfId());
                        destinationWharf.addAll(Collections.singleton(listDestination));
                    });
                    shippingDetails.setDestinationWharf(destinationWharf);
                }
            }
        }

        handleRealFileUrl(shippingDetails);
        log.info("queryShippingInfoDetails详情接口：{}", shippingDetails);
        return new ItemResult<>(shippingDetails);
    }

    private void handleRealFileUrl(ShippingInfoDetailsDTO shippingDetails) {
        if (CollectionUtils.isNotEmpty(shippingDetails.getAttListDTOS())) {
            List<AttListDTO> attListDTOS = shippingDetails.getAttListDTOS();
            attListDTOS.forEach(item -> {
                if (CsStringUtils.isNotBlank(item.getFileUrl())) {
                    String realFileUrl = iAttachmentService.getFullPath(item.getFileUrl());
                    item.setRealFileUrl(realFileUrl);
                }
            });
        }
    }

    @Override
    public ItemResult<Void> batchAuthorizeSeller(ShippingBatchAuthorizeDTO dto) throws BizException {
        shippingInfoBizService.batchAuthorizeSeller(dto);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> batchCancelAuthorizeSeller(ShippingBatchAuthorizeDTO dto) throws BizException {
        shippingInfoBizService.batchCancelAuthorizeSeller(dto);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> shippingInfoOverhaul(ShippingBatchDTO dto) throws BizException {
        shippingInfoBizService.shippingInfoOverhaul(dto);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> bindCaptain(BindCaptainDTO dto) throws BizException {
        shippingInfoBizService.bindCaptain(dto);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Boolean> shippingNameValid(String shippingName) throws BizException {
        return new ItemResult<>(shippingInfoBizService.shippingNameValid(shippingName));
    }

    @Override
    public ItemResult<Void> disableShippingInfo(ShippingOperationDTO dto) {
        shippingInfoBizService.disableShippingInfo(dto);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> updateServerType(EditServerTypeDTO dto) throws BizException {
        shippingInfoBizService.updateServerType(dto);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> batchFixedTemporary(ShippingBatchDTO dto) {
        shippingInfoBizService.batchFixedTemporary(dto);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> authorizeSellerList(AuthorizeSellerBatchDTO dto) throws BizException {
        shippingInfoBizService.authorizeSellerList(dto);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<List<AuthorizeSellerDTO>> authorizeSellerListInfo(String shippingId){
        return new ItemResult<>(shippingInfoBizService.authorizeSellerListInfo(shippingId));
    }

    @Override
    public ItemResult<Void> updateShippingStatus(ShippingOperationDTO dto) throws BizException {
        shippingInfoBizService.updateShippingStatus(dto);
        return new ItemResult<>(null);
    }

    /**
     * 查询船长绑定记录列表
     * @return
     * @throws BizException
     */
    @Override
    public ItemResult<List<BindCaptainLogDTO>> queryBindCaptainLog(String captainAccountName) throws BizException {
        return new ItemResult<>(shippingInfoBizService.queryBindCaptainLog(captainAccountName));
    }

    @Override
    public ItemResult<List<String>> getShippingCompanyList(String shippingCompany){
        return new ItemResult<>(shippingInfoBizService.getShippingCompanyList(shippingCompany));
    }

    @Override
    public ItemResult<Void> batchRecoveryTransport(ShippingBatchDTO dto) throws BizException {
        shippingInfoBizService.batchRecoveryTransport(dto);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<ShippingInfoDetailsDTO> queryShippingInfoByMemberId(String memberId) {
        ShippingInfoDetailsDTO shippingDetails = shippingInfoBizService.queryShippingInfoByMemberId(memberId);
        handleRealFileUrl(shippingDetails);
        log.info("queryShippingInfoByMemberId详情接口：{}", shippingDetails);
        return new ItemResult<>(shippingDetails);
    }

    @Override
    public ItemResult<Boolean> isPersonalShipowner(String memberId) {
        ShippingInfoDetailsDTO shippingDetails = shippingInfoBizService.queryShippingInfoByMemberId(memberId);
        if(shippingDetails == null){
            return new ItemResult<>(false);
        }
        List<Integer> auditStatusList = Lists.newArrayList(3,5,6);
        if (CsStringUtils.equals(shippingDetails.getManagerMemberType(), "40") && auditStatusList.contains(shippingDetails.getAuditStatus())) {
            return new ItemResult<>(true);
        }
        return new ItemResult<>(false);
    }

    @Override
    public ItemResult<List<ShippingInfoDTO>> queryPersonalShippingInfo(String shippingName) {
        return new ItemResult<>(shippingInfoBizService.queryPersonalShippingInfo(shippingName));
    }

    @Override
    public ItemResult<List<ShippingInfoDTO>> queryPlatformShippingInfo(String shippingName) {
        return new ItemResult<>(shippingInfoBizService.queryPlatformShippingInfo(shippingName));
    }

    @Override
    public List<BindErpShippingDTO> queryErpShippingBindList(String shippingId) {
        return shippingInfoBizService.queryErpShippingBindList(shippingId);
    }

	@Override
	public ItemResult<List<String>> selectShippingInfoName(ShippingInfoListQueryDTO queryDTO) {
		return new ItemResult<>(shippingInfoBizService.selectShippingInfoName(queryDTO));
	}

	@Override
	public ItemResult<List<ShippingInfoListDTO>> queryAllocationShippingInfo(String memberId, String carrierId, String shippingName) {
		return new ItemResult<>(shippingInfoBizService.queryAllocationShippingInfo(memberId, carrierId, shippingName));
	}

}
