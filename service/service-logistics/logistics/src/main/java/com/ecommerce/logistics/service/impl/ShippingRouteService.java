package com.ecommerce.logistics.service.impl;

import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.shippingroute.*;
import com.ecommerce.logistics.api.dto.shippingrule.QueryShippingRuleDTO;
import com.ecommerce.logistics.api.dto.shippingrule.ShippingRuleResultDTO;
import com.ecommerce.logistics.biz.IShippingRouteBizService;
import com.ecommerce.logistics.service.IShippingRouteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 航线运费定价服务
 * Created by he<PERSON><PERSON> on 2020/11/9 10:12
 */
@Slf4j
@Service
public class ShippingRouteService implements IShippingRouteService {

    @Autowired
    private IShippingRouteBizService shippingRouteBizService;

    @Override
    public ItemResult<String> addShippingRoute(ShippingRouteAddDTO dto) throws BizException {
        return new ItemResult<>(shippingRouteBizService.addShippingRoute(dto));
    }

    @Override
    public ItemResult<Void> delShippingRoute(String belongerId, String shippingRouteId, String operatorUserId) throws BizException {
        shippingRouteBizService.delShippingRoute(belongerId, shippingRouteId, operatorUserId);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> editShippingRoute(ShippingRouteEditDTO dto) throws BizException {
        shippingRouteBizService.editShippingRoute(dto);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<ShippingRouteDetailsDTO> queryShippingRouteDetails(String belongerId, String shippingRouteId) throws BizException {
        return new ItemResult<>(shippingRouteBizService.queryShippingRouteDetails(belongerId, shippingRouteId));
    }

    @Override
    public ItemResult<PageData<ShippingRouteListDTO>> queryShippingRouteList(PageQuery<ShippingRouteQueryDTO> pageQuery) throws BizException {
        return new ItemResult<>(shippingRouteBizService.queryShippingRouteList(pageQuery));
    }

    @Override
    public ItemResult<Void> delFarePayment(String shippingRouteId, String shippingFarePaymentId, String operatorUserId, String operatorUserName) throws BizException {
        shippingRouteBizService.delFarePayment(shippingRouteId, shippingFarePaymentId, operatorUserId, operatorUserName);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<List<ShippingAuxiliaryOperateLogDTO>> downloadShippingRouteOperateLog(String shippingRouteId) throws BizException {
        return new ItemResult<>(shippingRouteBizService.downloadShippingRouteOperateLog(shippingRouteId));
    }

    @Override
    public ItemResult<List<ShippingRuleResultDTO>> queryShippingRule(QueryShippingRuleDTO queryShippingRuleDTO) throws BizException {
        return new ItemResult<>(shippingRouteBizService.queryShippingRule(queryShippingRuleDTO));
    }

    @Override
    public ItemResult<List<ShippingRuleResultDTO>> queryShippingPaymentRule(QueryShippingRuleDTO queryShippingRuleDTO) throws BizException {
        return new ItemResult<>(shippingRouteBizService.queryShippingPaymentRule(queryShippingRuleDTO));
    }

    @Override
    public ItemResult<Map<String, String>> queryUnloadingWharf(String belongerId) throws BizException {
        return new ItemResult<>(shippingRouteBizService.queryUnloadingWharf(belongerId));
    }

    @Override
    public ItemResult<Map<String, String>> queryPickingWharf(String belongerId) throws BizException {
        return new ItemResult<>(shippingRouteBizService.queryPickingWharf(belongerId));
    }

}
