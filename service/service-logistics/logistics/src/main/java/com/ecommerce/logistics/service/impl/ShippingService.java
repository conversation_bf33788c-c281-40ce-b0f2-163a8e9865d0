package com.ecommerce.logistics.service.impl;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.shipping.ShippingAuditDTO;
import com.ecommerce.logistics.api.dto.shipping.ShippingDeleteDTO;
import com.ecommerce.logistics.api.dto.shipping.ShippingDetailDTO;
import com.ecommerce.logistics.api.dto.shipping.ShippingListDTO;
import com.ecommerce.logistics.api.dto.shipping.ShippingQueryDTO;
import com.ecommerce.logistics.api.dto.shipping.ShippingSaveDTO;
import com.ecommerce.logistics.biz.IShippingBizService;
import com.ecommerce.logistics.service.IShippingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Author: <EMAIL>
 * Description:
 * Date: Create in 下午3:51 18/12/25
 */
@Service
public class ShippingService implements IShippingService {

    @Autowired
    private IShippingBizService shippingBizService;


    @Override
    public ItemResult<String> addShipping(ShippingSaveDTO shippingSaveDTO) {
        return new ItemResult<>(shippingBizService.addShipping(shippingSaveDTO));
    }

    @Override
    public ItemResult<Void> deleteShipping(ShippingDeleteDTO shippingDeleteDTO) {
        shippingBizService.deleteShipping(shippingDeleteDTO);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> editShipping(ShippingSaveDTO shippingSaveDTO) {
        shippingBizService.editShipping(shippingSaveDTO);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<ShippingDetailDTO> queryShippingDetail(String shippingId) {
        return new ItemResult<>(shippingBizService.queryShippingDetail(shippingId));
    }

    @Override
    public ItemResult<PageData<ShippingListDTO>> queryShippingList(PageQuery<ShippingQueryDTO> pageQuery) {
        return new ItemResult<>(shippingBizService.queryShippingList(pageQuery));
    }

    @Override
    public ItemResult<Void> shippingAudit(ShippingAuditDTO shippingAuditDTO) {
        shippingBizService.shippingAudit(shippingAuditDTO);
        return new ItemResult<>(null);
    }
}
