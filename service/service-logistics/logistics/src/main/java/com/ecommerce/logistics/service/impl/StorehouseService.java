package com.ecommerce.logistics.service.impl;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.storehouse.StorehouseListDTO;
import com.ecommerce.logistics.api.dto.storehouse.StorehouseQueryDTO;
import com.ecommerce.logistics.api.dto.storehouse.WarehouseCapacityChangeDTO;
import com.ecommerce.logistics.api.dto.storehouse.WarehouseStorageListDTO;
import com.ecommerce.logistics.api.dto.storehouse.WarehouseStorageQueryDTO;
import com.ecommerce.logistics.biz.stock.IStorehouseBizService;
import com.ecommerce.logistics.service.IStorehouseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Auther: colu
 * @Date: 2019-07-11 11:49
 * @Description: 库位管理服务
 */
@Slf4j
@Service("storehouseService")
public class StorehouseService implements IStorehouseService {

    @Autowired
    private IStorehouseBizService storehouseBizService;

    @Override
    public List<StorehouseListDTO> fetchStockList(StorehouseQueryDTO storehouseQueryDTO) {
        return storehouseBizService.fetchStockList(storehouseQueryDTO);
    }

    @Override
    public ItemResult<PageData<WarehouseStorageListDTO>> queryPlatformStorageBySeller(PageQuery<WarehouseStorageQueryDTO> pageQuery) {
        return new ItemResult<>(storehouseBizService.queryPlatformStorageBySeller(pageQuery));
    }

    @Override
    public ItemResult<Void> changeWarehouseCapacity(WarehouseCapacityChangeDTO warehouseCapacityChangeDTO) {
        log.info("开始修改仓库库容:{}", warehouseCapacityChangeDTO);
        //失败就抛异常
        storehouseBizService.changeCapacity(warehouseCapacityChangeDTO);
        return new ItemResult<>(null);
    }
}
