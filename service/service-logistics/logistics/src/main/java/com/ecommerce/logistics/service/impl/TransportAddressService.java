package com.ecommerce.logistics.service.impl;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.transportaddress.TransportAddressDeleteDTO;
import com.ecommerce.logistics.api.dto.transportaddress.TransportAddressDetailDTO;
import com.ecommerce.logistics.api.dto.transportaddress.TransportAddressListDTO;
import com.ecommerce.logistics.api.dto.transportaddress.TransportAddressQueryDTO;
import com.ecommerce.logistics.api.dto.transportaddress.TransportAddressSaveDTO;
import com.ecommerce.logistics.api.dto.transportaddress.TransportAddressSearchDTO;
import com.ecommerce.logistics.biz.ITransportAddressBizService;
import com.ecommerce.logistics.service.ITransportAddressService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Author: <EMAIL>
 * Description:
 * Date: Create in 下午2:17 18/12/21
 */
@Service
public class TransportAddressService implements ITransportAddressService {

    @Autowired
    private ITransportAddressBizService transportAddressBizService;

    @Override
    public ItemResult<String> addTransportAddress(TransportAddressSaveDTO transportAddressSaveDTO) {
        return new ItemResult<>(transportAddressBizService.addTransportAddress(transportAddressSaveDTO));
    }

    @Override
    public ItemResult<Void> deleteTransportAddress(TransportAddressDeleteDTO transportAddressDeleteDTO) {
        transportAddressBizService.deleteTransportAddress(transportAddressDeleteDTO);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> editTransportAddress(TransportAddressSaveDTO transportCapacitySaveDTO) {
        transportAddressBizService.editTransportAddress(transportCapacitySaveDTO);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<TransportAddressDetailDTO> queryTransportAddressDetail(String transportAddressId) {
        return new ItemResult<>(transportAddressBizService.queryTransportAddressDetail(transportAddressId));
    }

    @Override
    public ItemResult<PageData<TransportAddressListDTO>> queryTransportAddressList(PageQuery<TransportAddressQueryDTO> pageQuery) {
        return new ItemResult<>(transportAddressBizService.queryTransportAddressList(pageQuery));
    }

    @Override
    public ItemResult<List<TransportAddressListDTO>> searchTransportAddressList(TransportAddressSearchDTO transportAddressSearchDTO) {
        return new ItemResult<>(transportAddressBizService.searchTransportAddressList(transportAddressSearchDTO));
    }
}
