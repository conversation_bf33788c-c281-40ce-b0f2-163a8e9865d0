package com.ecommerce.logistics.service.impl;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.transportcapacity.TransportCapacityDeleteDTO;
import com.ecommerce.logistics.api.dto.transportcapacity.TransportCapacityDetailDTO;
import com.ecommerce.logistics.api.dto.transportcapacity.TransportCapacityListDTO;
import com.ecommerce.logistics.api.dto.transportcapacity.TransportCapacityQueryDTO;
import com.ecommerce.logistics.api.dto.transportcapacity.TransportCapacitySaveDTO;
import com.ecommerce.logistics.api.dto.transportcapacity.TransportCapacityViewQueryDTO;
import com.ecommerce.logistics.biz.ITransportCapacityBizService;
import com.ecommerce.logistics.service.ITransportCapacityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Author: <EMAIL>
 * Description:
 * Date: Create in 上午11:42 18/12/19
 */
@Service
public class TransportCapacityService implements ITransportCapacityService {

    @Autowired
    private ITransportCapacityBizService transportCapacityBizService;

    @Override
    public ItemResult<String> addTransportCapacity(TransportCapacitySaveDTO transportCapacitySaveDTO) {
        return new ItemResult<>(transportCapacityBizService.addTransportCapacity(transportCapacitySaveDTO));
    }

    @Override
    public ItemResult<Void> deleteTransportCapacity(TransportCapacityDeleteDTO transportCapacityDeleteDTO) {
        transportCapacityBizService.deleteTransportCapacity(transportCapacityDeleteDTO);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> editTransportCapacity(TransportCapacitySaveDTO transportCapacitySaveDTO) {
        transportCapacityBizService.editTransportCapacity(transportCapacitySaveDTO);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<TransportCapacityDetailDTO> queryTransportCapacityDetail(String transportCapacityId) {
        return new ItemResult<>(transportCapacityBizService.queryTransportCapacityDetail(transportCapacityId));
    }

    @Override
    public ItemResult<PageData<TransportCapacityListDTO>> queryTransportCapacityList(PageQuery<TransportCapacityQueryDTO> pageQuery) {
        return new ItemResult<>(transportCapacityBizService.queryTransportCapacityList(pageQuery));
    }

    @Override
    public ItemResult<List<TransportCapacityListDTO>> queryTransportCapacityViewRecord(TransportCapacityViewQueryDTO transportCapacityViewQueryDTO) {
        return new ItemResult<>(transportCapacityBizService.queryTransportCapacityViewRecord(transportCapacityViewQueryDTO));
    }
}
