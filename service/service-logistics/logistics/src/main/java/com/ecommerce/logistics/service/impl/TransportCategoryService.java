package com.ecommerce.logistics.service.impl;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.common.service.common.BaseService;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.transportcategory.CategoryColDTO;
import com.ecommerce.logistics.api.dto.transportcategory.TransportCategoryAddDTO;
import com.ecommerce.logistics.api.dto.transportcategory.TransportCategoryDTO;
import com.ecommerce.logistics.api.dto.transportcategory.TransportCategoryDetailDTO;
import com.ecommerce.logistics.api.dto.transportcategory.TransportCategoryEditDTO;
import com.ecommerce.logistics.api.dto.transportcategory.TransportCategoryListDTO;
import com.ecommerce.logistics.api.dto.transportcategory.TransportCategoryListQueryDTO;
import com.ecommerce.logistics.api.dto.transportcategory.TransportCategoryRemoveDTO;
import com.ecommerce.logistics.api.dto.transportcategory.TransportOption;
import com.ecommerce.logistics.biz.ITransportCategoryBizService;
import com.ecommerce.logistics.biz.IWaybillQueryBizService;
import com.ecommerce.logistics.dao.dto.transport.WaybillIdCategoryMappingDO;
import com.ecommerce.logistics.dao.vo.TransportCategory;
import com.ecommerce.logistics.dao.vo.Waybill;
import com.ecommerce.logistics.service.ITransportCategoryService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * @Auther: <EMAIL>
 * @Date: 30/08/2018 11:06
 * @Description:
 */
@Service
@Slf4j
public class TransportCategoryService extends BaseService<TransportCategory> implements ITransportCategoryService {

    @Autowired
    private ITransportCategoryBizService iTransportCategoryBizService;

    @Autowired
    private IWaybillQueryBizService waybillQueryBizService;

    @Override
    public ItemResult<PageData<TransportCategoryListDTO>> queryTransportCategoryList(PageQuery<TransportCategoryListQueryDTO> pageQuery) {
        return new ItemResult<>(iTransportCategoryBizService.queryTransportCategoryList(pageQuery));
    }

    @Override
    public ItemResult<TransportCategoryDetailDTO> queryTransportCategory(String transportCategoryId) {
        return new ItemResult<>(iTransportCategoryBizService.queryTransportCategory(transportCategoryId));
    }

    @Override
    public ItemResult<Void> addTransportCategory(TransportCategoryAddDTO transportCategoryAddDTO) {
        iTransportCategoryBizService.saveTransportCategory(transportCategoryAddDTO);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> removeTransportCategory(TransportCategoryRemoveDTO transportCategoryRemoveDTO) {
        iTransportCategoryBizService.removeTransportCategory(transportCategoryRemoveDTO);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> modifyTransportCategory(TransportCategoryEditDTO transportCategoryEditDTO) {
        iTransportCategoryBizService.modifyTransportCategory(transportCategoryEditDTO);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<List<TransportCategoryDTO>> queryAll() {
        ItemResult<List<TransportCategoryDTO>> itemResult = new ItemResult<>();
        itemResult.setData(iTransportCategoryBizService.queryAll());
        itemResult.setSuccess(true);
        return itemResult;
    }

    @Override
    public ItemResult<List<TransportOption>> queryOptions() {
        return new ItemResult<>(iTransportCategoryBizService.queryOptions());
    }

    @Override
    public ItemResult<List<String>> transportCategoryMergeFilter(List<String> transportCategoryIdList) {
        return new ItemResult<>(iTransportCategoryBizService.transportCategoryMergeFilter(transportCategoryIdList));
    }

    @Override
    public ItemResult<List<TransportCategoryDTO>> queryTransportCategoryListById(List<String> transportCategoryIdList) {
        return new ItemResult<>(iTransportCategoryBizService.queryTransportCategoryListById(transportCategoryIdList));
    }

    @Override
    public ItemResult<List<CategoryColDTO>> queryWaybillTransportCategoryMap(List<String> waybillNumList) {
        if (CollectionUtils.isEmpty(waybillNumList)) {
            return new ItemResult<>(Lists.newArrayList());
        }
        List<Waybill> waybills = waybillQueryBizService.queryByWaybillNumList(waybillNumList);
        if (CollectionUtils.isEmpty(waybills)) {
            return new ItemResult<>(Lists.newArrayList());
        }
        List<String> waybillIdList = Lists.newArrayList();
        List<String> parentIdList = Lists.newArrayList();
        Map<String, String> waybillNum2IdMap = Maps.newHashMap();
        for (Waybill waybill : waybills) {
            waybillNum2IdMap.put(waybill.getWaybillNum(), waybill.getWaybillId());
            if (CsStringUtils.isEmpty(waybill.getPickingBillId())) {
                //有子运单
                parentIdList.add(waybill.getWaybillId());
            } else {
                waybillIdList.add(waybill.getWaybillId());
            }
        }
        List<WaybillIdCategoryMappingDO> waybillIdMappingDOs = iTransportCategoryBizService.queryTransportCategoryByWaybillIdList(waybillIdList);
        List<WaybillIdCategoryMappingDO> parentIdMappingDOs = iTransportCategoryBizService.queryTransportCategoryByParentIdList(parentIdList);
        Map<String, String> waybillId2TransportMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(waybillIdMappingDOs)) {
            for (WaybillIdCategoryMappingDO mappingDO : waybillIdMappingDOs) {
                waybillId2TransportMap.put(mappingDO.getWaybillId(), mappingDO.getTransportCategory());
            }
        }

        if (CollectionUtils.isNotEmpty(parentIdMappingDOs)) {
            for (WaybillIdCategoryMappingDO mappingDO : parentIdMappingDOs) {
                waybillId2TransportMap.put(mappingDO.getWaybillId(), mappingDO.getTransportCategory());
            }
        }

        List<CategoryColDTO> categoryColDTOList = Lists.newArrayList();
        for (String waybillNum : waybillNumList) {
            CategoryColDTO categoryColDTO = new CategoryColDTO();
            categoryColDTO.setWaybillNum(waybillNum);
            categoryColDTO.setTransportCategory(waybillId2TransportMap.get(waybillNum2IdMap.get(waybillNum)));
            categoryColDTOList.add(categoryColDTO);
        }

        return new ItemResult<>(categoryColDTOList);
    }

}
