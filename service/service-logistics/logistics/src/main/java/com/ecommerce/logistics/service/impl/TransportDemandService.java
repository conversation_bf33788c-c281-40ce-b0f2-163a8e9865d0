package com.ecommerce.logistics.service.impl;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.transportdemand.TransportDemandAuditDTO;
import com.ecommerce.logistics.api.dto.transportdemand.TransportDemandDeleteDTO;
import com.ecommerce.logistics.api.dto.transportdemand.TransportDemandDetailDTO;
import com.ecommerce.logistics.api.dto.transportdemand.TransportDemandListDTO;
import com.ecommerce.logistics.api.dto.transportdemand.TransportDemandQueryDTO;
import com.ecommerce.logistics.api.dto.transportdemand.TransportDemandSaveDTO;
import com.ecommerce.logistics.api.dto.transportdemand.TransportDemandViewQueryDTO;
import com.ecommerce.logistics.biz.ITransportDemandBizService;
import com.ecommerce.logistics.service.ITransportDemandService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Author: <EMAIL>
 * Description:
 * Date: Create in 上午10:28 18/12/20
 */
@Service
public class TransportDemandService implements ITransportDemandService {

    @Autowired
    private ITransportDemandBizService transportDemandBizService;

    @Override
    public ItemResult<String> addTransportDemand(TransportDemandSaveDTO transportDemandSaveDTO) {
        return new ItemResult<>(transportDemandBizService.addTransportDemand(transportDemandSaveDTO));
    }

    @Override
    public ItemResult<Void> deleteTransportDemand(TransportDemandDeleteDTO transportDemandDeleteDTO) {
        transportDemandBizService.deleteTransportDemand(transportDemandDeleteDTO);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> editTransportDemand(TransportDemandSaveDTO transportDemandSaveDTO) {
        transportDemandBizService.editTransportDemand(transportDemandSaveDTO);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<TransportDemandDetailDTO> queryTransportDemandDetail(String transportDemandId) {
        return new ItemResult<>(transportDemandBizService.queryTransportDemandDetail(transportDemandId));
    }

    @Override
    public ItemResult<PageData<TransportDemandListDTO>> queryTransportDemandList(PageQuery<TransportDemandQueryDTO> pageQuery) {
        return new ItemResult<>(transportDemandBizService.queryTransportDemandList(pageQuery));
    }

    @Override
    public ItemResult<Void> transportDemandAudit(TransportDemandAuditDTO transportDemandAuditDTO) {
        transportDemandBizService.transportDemandAudit(transportDemandAuditDTO);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<List<TransportDemandListDTO>> queryTransportDemandViewRecord(TransportDemandViewQueryDTO transportDemandViewQueryDTO) {
        return new ItemResult<>(transportDemandBizService.queryTransportDemandViewRecord(transportDemandViewQueryDTO));
    }
}
