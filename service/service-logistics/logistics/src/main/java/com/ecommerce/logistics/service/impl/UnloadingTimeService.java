package com.ecommerce.logistics.service.impl;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.intelligentDispatch.UnloadingTimeDTO;
import com.ecommerce.logistics.biz.IUnloadingTimeBizService;
import com.ecommerce.logistics.service.IUnloadingTimeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class UnloadingTimeService implements IUnloadingTimeService {

    @Autowired
    private IUnloadingTimeBizService unloadingTimeBizService;

    @Override
    public ItemResult<Boolean> add(UnloadingTimeDTO createDTO) {
        return new ItemResult<>(unloadingTimeBizService.add(createDTO));
    }

    @Override
    public ItemResult<Boolean> edit(UnloadingTimeDTO updateDTO) {
        return new ItemResult<>(unloadingTimeBizService.edit(updateDTO));
    }

    @Override
    public ItemResult<Boolean> delete(UnloadingTimeDTO deleteDTO) {
        return new ItemResult<>(unloadingTimeBizService.delete(deleteDTO));
    }

    @Override
    public ItemResult<PageData<UnloadingTimeDTO>> pageUnloadingTime(PageQuery<UnloadingTimeDTO> pageQuery) {
        return new ItemResult<>(unloadingTimeBizService.pageUnloadingTime(pageQuery));
    }
}
