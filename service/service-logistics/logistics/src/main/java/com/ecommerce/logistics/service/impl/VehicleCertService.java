package com.ecommerce.logistics.service.impl;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.logistics.api.dto.vehiclecert.VehicleCertDTO;
import com.ecommerce.logistics.api.param.vehiclecert.VehicleCertEditParam;
import com.ecommerce.logistics.api.param.vehiclecert.VehicleCertSaveParam;
import com.ecommerce.logistics.biz.IVehicleCertBizService;
import com.ecommerce.logistics.service.IVehicleCertService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Copyright (C),2020
 *
 * @className: VehicleCertService
 * @author: <EMAIL>
 * @Date: 2020/10/12 11:17 上午
 * @Description:
 */
@Service
public class VehicleCertService implements IVehicleCertService {

    @Autowired
    private IVehicleCertBizService vehicleCertBizService;

    @Override
    public ItemResult<String> saveVehicleCertAndSubmitAuth(VehicleCertSaveParam VehicleCertSaveParam) {
        return new ItemResult<>(vehicleCertBizService.saveVehicleCertAndSubmitAuth(VehicleCertSaveParam));
    }

    @Override
    public ItemResult<VehicleCertDTO> queryVehicleCertByUserId(String userId) {
        return new ItemResult<>(vehicleCertBizService.queryVehicleCertByUserId(userId));
    }

    @Override
    public ItemResult<VehicleCertDTO> queryVehicleCertByVehicleId(String vehicleId) {

        return new ItemResult<>(vehicleCertBizService.queryVehicleCertByVehicleId(vehicleId));
    }

    @Override
    public ItemResult<String> modifyVehicleCert(VehicleCertEditParam vehicleCertEditParam) {
        return new ItemResult<>(vehicleCertBizService.modifyVehicleCert(vehicleCertEditParam));
    }
}
