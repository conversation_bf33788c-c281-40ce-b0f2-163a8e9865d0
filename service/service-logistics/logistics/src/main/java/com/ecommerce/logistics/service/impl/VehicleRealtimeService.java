package com.ecommerce.logistics.service.impl;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.logistics.api.dto.realtime.VehicleRelationCondDTO;
import com.ecommerce.logistics.biz.IVehicleRealtimeBizService;
import com.ecommerce.logistics.service.IVehicleRealtimeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Auther: colu
 * @Date: 2020-07-22 14:50
 * @Description: 车辆实时处理的服务
 */
@Service("vehicleRealtimeService")
public class VehicleRealtimeService implements IVehicleRealtimeService {

    @Autowired
    private IVehicleRealtimeBizService vehicleRealtimeBizService;

    @Override
    public ItemResult<VehicleRelationCondDTO> filterVehicleCond(VehicleRelationCondDTO vehicleRelationCondDTO) {
        return new ItemResult<>(vehicleRealtimeBizService.filterVehicleCond(vehicleRelationCondDTO));
    }

}
