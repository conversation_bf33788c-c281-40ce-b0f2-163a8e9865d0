package com.ecommerce.logistics.service.impl;

import com.alibaba.fastjson.JSON;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.common.service.common.BaseService;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.dispatchbill.DriverListDTO;
import com.ecommerce.logistics.api.dto.driver.BindDriverDTO;
import com.ecommerce.logistics.api.dto.driver.PersonVehicleDTO;
import com.ecommerce.logistics.api.dto.vehicle.*;
import com.ecommerce.logistics.api.enums.UserRoleEnum;
import com.ecommerce.logistics.api.enums.VehicleCertificationStatusEnum;
import com.ecommerce.logistics.api.param.vehiclecert.VehicleAddParamDTO;
import com.ecommerce.logistics.biz.IVehicleBizService;
import com.ecommerce.logistics.dao.vo.Vehicle;
import com.ecommerce.logistics.service.IVehicleService;
import com.ecommerce.member.api.dto.account.AccountDTO;
import com.ecommerce.member.api.dto.account.AccountSearchDTO;
import com.ecommerce.member.api.dto.account.DriverAccountDTO;
import com.ecommerce.member.api.dto.member.MemberCertDTO;
import com.ecommerce.member.api.dto.member.enums.MemberCertTypeEnum;
import com.ecommerce.member.api.service.IAccountService;
import com.ecommerce.member.api.service.IMemberService;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Auther: <EMAIL>
 * @Date: 07/08/2018 14:38
 * @Description:
 */
@Service
@Slf4j
public class VehicleService extends BaseService<Vehicle> implements IVehicleService {
    @Autowired
    private IVehicleBizService vehicleBizService;

    @Autowired
    private IAccountService accountService;

    @Autowired
    private IMemberService memberService;


    @Override
    public ItemResult<ExternalVehicleDTO> queryVehicleInfoByNumber(String vehicleNumber) {
        //针对外部车辆，通过车牌号获取车辆和司机信息
        ExternalVehicleDTO externalVehicleDTO = vehicleBizService.queryVehicleInfoByNumber(vehicleNumber);
        log.info("根据车牌号获取信息===>001：{}", externalVehicleDTO);
        //调用会员服务，通过司机memberId获取身份证号(对于个体司机来说memberId=accountId)
        List<MemberCertDTO> dtoList = memberService.findMemberCertByMemberId(externalVehicleDTO.getBindDriverId());
        log.info("根据车牌号获取信息===>002：{}", JSON.toJSONString(dtoList));
        if (CollectionUtils.isNotEmpty(dtoList)) {
            for (MemberCertDTO dto : dtoList) {
                if (CsStringUtils.equals(dto.getCertType(), MemberCertTypeEnum.IDENTITY_LICENSE.getCode())) {
                    //身份证号
                    externalVehicleDTO.setIdNumber(dto.getIdNumber());
                    //真实姓名
                    externalVehicleDTO.setDriverName(dto.getRealName());
                }
            }
        }
        log.info("根据车牌号获取信息===>003：{}", externalVehicleDTO);
        return new ItemResult<>(externalVehicleDTO);
    }

    @Override
    public ItemResult<VehicleDefaultResDTO> findDefaultAssignVehicle(VehicleDefaultCondDTO vehicleDefaultCondDTO) {
        return new ItemResult<>(vehicleBizService.findDefaultAssignVehicle(vehicleDefaultCondDTO));
    }

    @Override
    public ItemResult<PageData<VehicleListDTO>> queryVehicleList(PageQuery<VehicleListQueryDTO> pageQuery) {
        /**
         *
         * 功能描述: 获取车辆列表
         *
         * @param: [pageQuery]
         * @return: com.ecommerce.common.result.ItemResult<com.ecommerce.common.result.PageData < com.ecommerce.logistics.api.dto.vehicle.VehicleListDTO>>
         */
        PageData<VehicleListDTO> vehicleListDTOPageData = vehicleBizService.queryVehicleList(pageQuery);
        if (vehicleListDTOPageData != null && CollectionUtils.isNotEmpty(vehicleListDTOPageData.getList())) {
            List<String> driverIds = vehicleListDTOPageData.getList().stream().map(VehicleListDTO::getBindDriverId).toList();
            if (CollectionUtils.isNotEmpty(driverIds)) {
                DriverAccountDTO driverAccountDTO = new DriverAccountDTO();
                //司机账号ID集合
                driverAccountDTO.setList(driverIds);
                driverAccountDTO.setCertType(MemberCertTypeEnum.IDENTITY_LICENSE.getCode());
                //调用会员服务，通过司机accountId获取身份证号码
                List<MemberCertDTO> dtoList = memberService.queryMemberCertList(driverAccountDTO);
                vehicleListDTOPageData.getList().stream().forEach(item -> {
                    dtoList.stream().forEach(dto -> {
                        //如果是同一个用户,则设置身份证号
                        if (CsStringUtils.equals(item.getBindDriverId(), dto.getAccountId())) {
                            //设置身份证号
                            item.setIdNumber(dto.getIdNumber());
                        }
                    });
                });
            }
        }
        return new ItemResult<>(vehicleListDTOPageData);
    }


    @Override
    public ItemResult<PageData<VehicleListDTO>> queryVehicleListPlatform(PageQuery<VehicleListQueryDTO> pageQuery){
        /**
         *
         * 功能描述: 获取车辆列表
         *
         * @param: [pageQuery]
         * @return: com.ecommerce.common.result.ItemResult<com.ecommerce.common.result.PageData < com.ecommerce.logistics.api.dto.vehicle.VehicleListDTO>>
         */
        PageData<VehicleListDTO> vehicleListDTOPageData = vehicleBizService.queryVehicleListPlatform(pageQuery);
        return new ItemResult<>(vehicleListDTOPageData);
    }

    @Override
    public ItemResult<String> addVehicle(VehicleAddDTO vehicleAddDTO) {
        /**
         *
         * 功能描述: 新增车辆
         *
         * @param: [vehicleAddDTO]
         * @return: void
         */
        return new ItemResult<>(vehicleBizService.saveVehicle(vehicleAddDTO));
    }

    @Override
    public ItemResult<String> addVehicleV2(VehicleAddParamDTO vehicleAddParamDTO) throws BizException{
        /**
         *
         * 功能描述: 新增车辆
         *
         * @param: [vehicleAddParam]
         * @return: void
         */
        return new ItemResult<>(vehicleBizService.saveVehicleV2(vehicleAddParamDTO));
    }

    @Override
    public ItemResult<Void> removeVehicle(List<VehicleRemoveDTO> list) {
        /**
         *
         * 功能描述: 逻辑删除车辆
         *
         * @param: [vehicleRemoveDTO]
         * @return: void
         */
        vehicleBizService.removeVehicle(list);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> modifyVehicle(VehicleEditDTO vehicleEditDTO) {
        /**
         *
         * 功能描述: 更新车辆信息
         *
         * @param: [vehicleEditDTO]
         * @return: void
         */
        vehicleBizService.modifyVehicle(vehicleEditDTO);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<VehicleDetailDTO> queryVehicle(String vehicleId) {
        /**
         *
         * 功能描述: 过去车辆的详情数据
         *
         * @param: [vehicleId]
         * @return: com.ecommerce.logistics.api.dto.vehicle.VehicleDetailDTO
         */
        return new ItemResult<>(vehicleBizService.queryVehicle(vehicleId));
    }

    @Override
    public ItemResult<Void> modifyCertificationStatusToOk(VehicleCertificationStatusDTO vehicleCertificationStatusDTO) {
        /**
         *
         * 功能描述: 通过认证，将认证状态改为认证成功
         *
         * @param: [vehicleCertificationStatusDTO]
         * @return: void
         */
        vehicleBizService.modifyCertificationStatusToOk(vehicleCertificationStatusDTO);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> modifyCertificationStatusToFailure(VehicleCertificationStatusDTO vehicleCertificationStatusDTO) {
        /**
         *
         * 功能描述: 驳回认证，将认证状态改为认证失败
         *
         * @param: [vehicleCertificationStatusDTO]
         * @return: void
         */
        vehicleBizService.modifyCertificationStatusToFailure(vehicleCertificationStatusDTO);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<PageData<UserVehicleListDTO>> queryVehicleListByUserId(PageQuery<UserVehicleListQueryDTO> pageQuery) {
        /**
         *
         * 功能描述: 通过用户id查询指派车辆列表
         *
         * @param: [pageQuery]
         * @return: com.ecommerce.common.result.ItemResult<com.ecommerce.common.result.PageData < com.ecommerce.logistics.api.dto.vehicle.UserVehicleListDTO>>
         */
        return new ItemResult<>(vehicleBizService.queryVehicleListByUserId(pageQuery));
    }

    @Override
    public ItemResult<List<VehicleAppDataDTO>> queryAppVehicleByUserId(String userId) {
        /**
         *
         * 功能描述: 通过用户id查询车辆列表包含车辆附件信息数据
         *
         * @param: [userId]
         * @return: com.ecommerce.common.result.ItemResult<java.util.List < com.ecommerce.logistics.api.dto.vehicle.VehicleAppDataDTO>>
         */
        return new ItemResult<>(vehicleBizService.queryVehicleByUserId(userId));
    }

    @Override
    public ItemResult<List<VehicleAppListDTO>> queryAppVehicleList(String userId) {
        /**
         *
         * 功能描述: 小程序通过userId查询用户的所有车辆
         *
         * @param: [userId]
         * @return: com.ecommerce.common.result.ItemResult<java.util.List < com.ecommerce.logistics.api.dto.vehicle.VehicleAppListDTO>>
         */
        return new ItemResult<>(vehicleBizService.queryAppVehicleList(userId));
    }

    @Override
    public ItemResult<List<VehicleAppListDTO>> queryAppVehicleListByUserDTO(VehicleUserCondDTO vehicleUserCondDTO) {
        return new ItemResult<>(vehicleBizService.queryAppVehicleListByUserDTO(vehicleUserCondDTO));
    }

    @Override
    public ItemResult<String> addAppVehicle(VehicleAppAddDTO vehicleAppAddDTO) {
        return new ItemResult<>(vehicleBizService.addAppVehicle(vehicleAppAddDTO));
    }

    @Override
    public ItemResult<Void> modifyAppVehicle(VehicleAppEditDTO vehicleAppEditDTO) {
        vehicleBizService.modifyAppVehicle(vehicleAppEditDTO);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> modifyAppIsDefault(VehicleModifyIsDefaultDTO vehicleModifyIsDefaultDTO) {
        vehicleBizService.modifyAppIsDefault(vehicleModifyIsDefaultDTO);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<VehicleDTO> selectVehicleById(String vehicleId) {
        /**
         *
         * 功能描述: 通过车辆Id查询车辆，服务于trace
         *
         * @param: [vehicleId]
         * @return: com.ecommerce.common.result.ItemResult<com.ecommerce.logistics.api.dto.vehicle.VehicleDTO>
         */
        ItemResult<VehicleDTO> itemResult = new ItemResult<>();
        itemResult.setData(vehicleBizService.selectVehicleById(vehicleId));
        return itemResult;
    }

    @Override
    public ItemResult<PageData<DriverListDTO>> queryDriverListByUserId(PageQuery<String> queryDTO) {
        List<DriverListDTO> driverList = new ArrayList<>();
        AccountSearchDTO searchDTO = new AccountSearchDTO();
        searchDTO.setMemberId(queryDTO.getQueryDTO());
        searchDTO.setEntDriver(true);
        searchDTO.setDisabled(false);
        searchDTO.setPageNumber(queryDTO.getPageNum());
        searchDTO.setPageSize(queryDTO.getPageSize());
        PageInfo<AccountDTO> pageInfo = accountService.findAll(searchDTO);
        if (null == pageInfo) {
            log.info(">>>>>>>从用户端获取pageInfo为null.");
        } else {
            List<AccountDTO> dtos = pageInfo.getList();
            if (CollectionUtils.isNotEmpty(dtos)) {
                log.info("get userinfo from accountService:{}", dtos.get(0));
            }
            dtos.forEach(item -> {
                DriverListDTO driver = new DriverListDTO();
                driver.setDriverId(item.getMemberId());
                driver.setDriverName(item.getRealName());
                driver.setDriverPhone(item.getMobile());
                driverList.add(driver);
            });
        }
		/*DriverListDTO driver = new DriverListDTO();
		driver.setDriverId("shdiusahdiusdhw43223");
		driver.setDriverName("小猪");
		driver.setDriverPhone("***********");
		driverList.add(driver);
		DriverListDTO driver1 = new DriverListDTO();
		driver1.setDriverId("shdiusahdiusdhw43222");
		driver1.setDriverName("小狗");
		driver1.setDriverPhone("***********");
		driverList.add(driver1);*/
        PageData<DriverListDTO> returnDTO = new PageData<DriverListDTO>(driverList);
        return new ItemResult<>(returnDTO);
    }

    @Override
    public ItemResult<List<VehicleBaseDataDTO>> queryVehicleBaseData(String userId) {
        return new ItemResult<>(vehicleBizService.queryVehicleBaseData(userId));
    }

    @Override
    public ItemResult<List<VehicleOptionsDTO>> queryVehicleOptions(VehicleOptionsQueryDTO vehicleOptionsQueryDTO) {
        return new ItemResult<>(vehicleBizService.queryVehicleOptions(vehicleOptionsQueryDTO));
    }

    @Override
    public ItemResult<Void> submitAuthentication(VehicleCertificationStatusDTO vehicleCertificationStatusDTO) {
        vehicleBizService.submitAuthentication(vehicleCertificationStatusDTO);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<String> getCertificationStatus(String userId) {
        return new ItemResult<>(vehicleBizService.getCertificationStatus(userId));
    }

    @Override
    public ItemResult<Void> updateVehicleSignFlag(VehicleSignalEditDTO vehicleSignalEditDTO) {
        log.info("修改车辆的GPS信号标识:{}", vehicleSignalEditDTO);
        try {
            vehicleBizService.updateVehicleSignFlag(vehicleSignalEditDTO);
        } catch (Exception e) {
            log.error("修改车辆GPS信号标识发生异常:{}", vehicleSignalEditDTO, e);
            ItemResult<Void> exResult = new ItemResult<>();
            exResult.setCode(BasicCode.UNKNOWN_ERROR.getCode());
            exResult.setDescription(BasicCode.UNKNOWN_ERROR.getMsg(e.getMessage()));
            return exResult;
        }
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<String> addVehicleAndSubmitAuth(VehicleAddDTO vehicleAddDTO) {
        log.info("添加车辆,并提交审核:{}", vehicleAddDTO);
        try {
            String vehicleId = vehicleBizService.addVehicleAndSubmitAuth(vehicleAddDTO);
            return new ItemResult<>(vehicleId);
        } catch (Exception e) {
            log.error("添加车辆,并提交审核发生异常:{}", vehicleAddDTO, e);
            ItemResult<String> exResult = new ItemResult<>();
            exResult.setCode(BasicCode.UNKNOWN_ERROR.getCode());
            exResult.setDescription(BasicCode.UNKNOWN_ERROR.getMsg(e.getMessage()));
            return exResult;
        }
    }

    @Override
    public ItemResult<VehicleBatchAddResultDTO> batchAddVehicle(List<VehicleAddDTO> vehicleAddDTOList) {
        return new ItemResult<>(vehicleBizService.batchSaveVehicle(vehicleAddDTOList));
    }

    @Override
    public ItemResult<VehicleBatchAddResultDTO> batchAddAppVehicle(List<VehicleAppAddDTO> vehicleAppAddDTOList) {
        return new ItemResult<>(vehicleBizService.batchAddAppVehicle(vehicleAppAddDTOList));
    }

    @Override
    public ItemResult<VehicleImportTemplateOptionDTO> queryVehicleImportTemplateOptions() {
        return new ItemResult<>(vehicleBizService.queryVehicleImportTemplateOptions());
    }

    @Override
    public ItemResult<Void> bindingVehicle(String operateUserId, String number, String carrierId) {
        try {
            log.info("绑定外协车辆，车牌号:{}", number);
            vehicleBizService.bindingVehicle(operateUserId, number, carrierId);
            return new ItemResult<>(null);
        } catch (Exception e) {
            log.error("绑定外协车辆发生异常 : {}", e);
            ItemResult<Void> exResult = new ItemResult<>();
            exResult.setCode(BasicCode.UNKNOWN_ERROR.getCode());
            exResult.setDescription(BasicCode.UNKNOWN_ERROR.getMsg(e.getMessage()));
            return exResult;
        }
    }

    @Override
    public ItemResult<Void> unBindingVehicle(String operateUserId, String number, String carrierId) {
        try {
            log.info("解绑外协车辆，车牌号:{}", number);
            vehicleBizService.unBindingVehicle(operateUserId, number, carrierId);
            return new ItemResult<>(null);
        } catch (Exception e) {
            log.error("解绑外协车辆发生异常 : {}", e);
            ItemResult<Void> exResult = new ItemResult<>();
            exResult.setCode(BasicCode.UNKNOWN_ERROR.getCode());
            exResult.setDescription(BasicCode.UNKNOWN_ERROR.getMsg(e.getMessage()));
            return exResult;
        }
    }

    @Override
    public ItemResult<Void> updateVehicleDisableFlg(List<VehicleDisableFlgEditDTO> list) {
        try {
            vehicleBizService.updateVehicleDisableFlg(list);
        } catch (Exception e) {
            log.error("修改车辆是否禁用发生异常 : {}", list, e);
            ItemResult<Void> exResult = new ItemResult<>();
            exResult.setCode(BasicCode.DB_UPDATE_FAILED.getCode());
            exResult.setDescription(e.getMessage());
            return exResult;
        }
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<VehicleDTO> queryVehicleByNumber(String number) {
        if (CsStringUtils.isBlank(number)) {
            throw new BizException(BasicCode.PARAM_NULL, "车牌号");
        }
        VehicleQueryConditionDTO vehicleQueryConditionDTO = new VehicleQueryConditionDTO();
        vehicleQueryConditionDTO.setVehicleNum(number);
        vehicleQueryConditionDTO.setCertificationStatus(VehicleCertificationStatusEnum.AUTHENTICATION_OK.getCode());
        Vehicle vehicle = vehicleBizService.selectVehicleByCondition(vehicleQueryConditionDTO);
        if(vehicle == null){
            return new ItemResult<>();
        }
        VehicleDTO vehicleDTO = new VehicleDTO();
        BeanUtils.copyProperties(vehicle,vehicleDTO);
        String name = vehicleBizService.selectCarrierNameByVehicle(vehicleDTO.getVehicleId());
        if (CsStringUtils.isNotBlank(name)) {
            vehicleDTO.setUserName(name);
        }
        return new ItemResult<>(vehicleDTO);
    }

    @Override
    public ItemResult<List<VehicleBaseDataDTO>> getBuyerTakeCars(VehicleBuyerTakeQueryDTO queryDTO) {
        if (CsStringUtils.isBlank(queryDTO.getUserId())) {
            throw new BizException(BasicCode.PARAM_NULL, "归属人");
        }
        queryDTO.setUserType(UserRoleEnum.BUYER.getCode());
        return new ItemResult<>(vehicleBizService.getBuyerTakeCars(queryDTO));
    }

    @Override
    public ItemResult<List<VehicleDetailDTO>> queryPersonalVehicle(VehicleListQueryDTO queryDTO) {
        return new ItemResult<>(vehicleBizService.queryPersonalVehicle(queryDTO));
    }

	@Override
	public ItemResult<Void> bindDriver(BindDriverDTO dto) {
		vehicleBizService.bindDriver(dto);
		return new ItemResult<>(null);
	}

	@Override
	public ItemResult<ExternalVehicleAppDTO> selectVehicleListByNumber(String number) {
		//针对外部车辆，通过车牌号获取车辆和司机信息
        ExternalVehicleAppDTO externalVehicleDTO = vehicleBizService.selectVehicleListByNumber(number);
        log.info("根据车牌号获取信息===>001：{}", externalVehicleDTO);
        //调用会员服务，通过司机memberId获取身份证号(对于个体司机来说memberId=accountId)
        List<MemberCertDTO> dtoList = memberService.findMemberCertByMemberId(externalVehicleDTO.getBindDriverId());
        log.info("根据车牌号获取信息===>002：{}", JSON.toJSONString(dtoList));
        if (CollectionUtils.isNotEmpty(dtoList)) {
            for (MemberCertDTO dto : dtoList) {
                if (CsStringUtils.equals(dto.getCertType(), MemberCertTypeEnum.IDENTITY_LICENSE.getCode())) {
                    //身份证号
                    externalVehicleDTO.setIdNumber(dto.getIdNumber());
                    //真实姓名
                    externalVehicleDTO.setDriverName(dto.getRealName());
                }
            }
        }
        log.info("根据车牌号获取信息===>003：{}", externalVehicleDTO);
        return new ItemResult<>(externalVehicleDTO);
	}

    @Override
    public ItemResult<PersonVehicleDTO> queryPersonVehicleByUserId(String userId) {
        return new ItemResult<>(vehicleBizService.queryPersonVehicleByUserId(userId));
    }


    @Override
    public ItemResult<List<VehicleListDTO>> queryExportVehicleList(VehicleListQueryDTO vehicleListQueryDTO){
        List<VehicleListDTO> vehicleListDTOList = vehicleBizService.queryExportVehicleList(vehicleListQueryDTO);
        if (CollectionUtils.isNotEmpty(vehicleListDTOList)) {
            return new ItemResult<>(vehicleListDTOList);
        } else {
            return new ItemResult<>(new ArrayList<>());
        }
    }


    @Override
    public ItemResult<List<UserVehicleListDTO>> queryVehicleListDropDownBox(UserVehicleListQueryDTO queryDTO){
        return new ItemResult<>(vehicleBizService.queryVehicleListDropDownBox(queryDTO));
    }
	@Override
	public ItemResult<List<String>> selectVehicleNumber(VehicleListQueryDTO queryDTO) {
		return new ItemResult<>(vehicleBizService.selectVehicleNumber(queryDTO));
	}

}
