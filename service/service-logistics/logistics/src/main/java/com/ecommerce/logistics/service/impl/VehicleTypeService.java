package com.ecommerce.logistics.service.impl;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.common.service.common.BaseService;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.vehicletype.*;
import com.ecommerce.logistics.biz.IVehicleTypeBizService;
import com.ecommerce.logistics.dao.vo.VehicleType;
import com.ecommerce.logistics.service.IVehicleTypeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Auther: <EMAIL>
 * @Date: 22/08/2018 11:04
 * @Description:
 */
@Service
@Slf4j
public class VehicleTypeService extends BaseService<VehicleType> implements IVehicleTypeService {

    @Autowired
    private IVehicleTypeBizService iVehicleTypeBizService;

    @Override
    public ItemResult<PageData<VehicleTypeListDTO>> queryVehicleTypeList(PageQuery<VehicleTypeListQueryDTO> pageQuery) {
        PageData<VehicleTypeListDTO> vehicleTypeListDTOPageData = iVehicleTypeBizService.queryVehicleTypeList(pageQuery);
        ItemResult<PageData<VehicleTypeListDTO>>  itemResult =  new ItemResult<>();
        itemResult.setSuccess(true);
        itemResult.setData(vehicleTypeListDTOPageData);
        return itemResult;
    }

    @Override
    public ItemResult<Void> addVehicleType(VehicleTypeAddDTO vehicleTypeAddDTO) {
        iVehicleTypeBizService.saveVehicleType(vehicleTypeAddDTO);
        ItemResult<Void> itemResult = new ItemResult<>();
        itemResult.setSuccess(true);
        return  itemResult;
    }

    @Override
    public ItemResult<Void> removeVehicleType(VehicleTypeRemoveDTO vehicleTypeRemoveDTO) {
        iVehicleTypeBizService.removeVehicleType(vehicleTypeRemoveDTO);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> modifyVehicleType(VehicleTypeEditDTO vehicleTypeEditDTO) {
        iVehicleTypeBizService.modifyVehicleType(vehicleTypeEditDTO);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<VehicleTypeDetailDTO> queryVehicleType(String vehicleTypeId) {
        return new ItemResult<>(iVehicleTypeBizService.queryVehicleType(vehicleTypeId));
    }

    @Override
    public ItemResult<List<VehicleTypeOptionDTO>> queryOptions() {
        return new ItemResult<>(iVehicleTypeBizService.queryOptions());
    }

    @Override
    public List<VehicleTypeListDTO> queryVehicleTypes(List<String> vehicleTypeIdList) {
        return iVehicleTypeBizService.queryVehicleTypes(vehicleTypeIdList);
    }

    @Override
    public VehicleTypeNameDTO querytUnlimiteType() {
        return iVehicleTypeBizService.querytUnlimiteType();
    }

    @Override
    public ItemResult<BigDecimal> queryDepositAmountByDriverUserId(String userId) {
        return new ItemResult<>(iVehicleTypeBizService.queryDepositAmountByDriverUserId(userId));
    }
}
