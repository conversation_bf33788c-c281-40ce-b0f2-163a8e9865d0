package com.ecommerce.logistics.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.ecommerce.logistics.api.dto.warehouse.*;
import com.ecommerce.logistics.api.enums.WarehouseTypeEnum;
import com.ecommerce.logistics.dao.vo.Warehouse;
import com.ecommerce.logistics.util.generator.WarehouseBizUidGenerator;
import com.ecommerce.member.api.dto.referrer.ReferrerBuyerRelationDTO;
import com.ecommerce.member.api.dto.referrer.ReferrerInfoDTO;
import com.ecommerce.member.api.dto.referrer.ReferrerInfoQueryDTO;
import com.ecommerce.member.api.service.IBuyerAndReferrerService;
import com.github.pagehelper.PageInfo;
import org.apache.commons.collections.CollectionUtils;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.common.service.common.BaseService;
import com.ecommerce.common.service.common.uuid.UUIDGenerator;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.biz.IWarehouseBizService;
import com.ecommerce.logistics.dao.vo.WarehouseZoneMap;
import com.ecommerce.logistics.service.IWarehouseService;

import lombok.extern.slf4j.Slf4j;


/**
 * @Auther: <EMAIL>
 * @Date: 10/08/2018 09:56
 * @Description:
 */
@Deprecated(since = "2.1.4-RELEASE")
@Slf4j
@Service
public class WarehouseService extends BaseService<Warehouse> implements IWarehouseService {
    @Autowired
    private IWarehouseBizService warehouseBizService;

    @Autowired
    private UUIDGenerator uuidGenerator;

    @Autowired
    private WarehouseBizUidGenerator warehouseBizUidGenerator;

    @Autowired
    private IBuyerAndReferrerService buyerAndReferrerService;

    @Override
    public ItemResult<PageData<WarehouseListDTO>> queryWarehouseList(PageQuery<WarehouseListQueryDTO> pageQuery) {
        /**
         *
         * 功能描述: 查询仓库的分页列表，可以进行条件组合查询，条件属性可以为空
         *
         * @param: [pageQuery]
         * @return: com.ecommerce.common.result.PageData<com.ecommerce.logistics.api.dto.warehouse.WarehouseListDTO>
         */
        return new ItemResult<>(warehouseBizService.getWarehouseList(pageQuery));
    }

    @Override
    public ItemResult<Void> removeWarehouse(WarehouseRemoveDTO warehouseRemoveDTO) {
        /**
         *
         * 功能描述: 逻辑删除仓库
         *
         * @param: [warehouseRemoveDTO]
         * @return: void
         */
        warehouseBizService.removeWarehouse(warehouseRemoveDTO);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<WarehouseInsertReturnDTO> saveWarehouse( WarehouseAddDTO warehouseAddDTO) {
        /**
         *
         * 功能描述: 新增仓库
         *
         * @param: [warehouseAddDTO]
         * @return: void
         */
        log.info("开始插入仓库信息:{}",warehouseAddDTO);
        Warehouse warehouse = new Warehouse();
        Date date = new Date();
        BeanUtils.copyProperties(warehouseAddDTO,warehouse);
        warehouse.setVersion(0L);
        warehouse.setDelFlg(0);
        warehouse.setCreateTime(date);
        warehouse.setNumber(warehouseBizUidGenerator.incrementCode());
        warehouse.setWarehouseId(uuidGenerator.gain());
        WarehouseZoneMap warehouseZoneMap = null;
        List<WarehouseZoneMap> maps = null;
        if(WarehouseTypeEnum.CENTRAL_WAREHOUSE.getCode().equals(warehouseAddDTO.getType())
                || WarehouseTypeEnum.STORE.getCode().equals(warehouseAddDTO.getType())) {
            maps = setWarehouseZoneMap(warehouse, warehouseAddDTO.getDeliveryAreas(), date);
        }
        log.info("新增仓库对象:{}",warehouse);
        log.info("新增仓库配送地址对象:{}",maps);
        warehouseBizService.saveWarehouse(warehouse, maps);
        WarehouseInsertReturnDTO returnDTO = new WarehouseInsertReturnDTO();
        returnDTO.setNumber(warehouse.getNumber());
        returnDTO.setWarehouseId(warehouse.getWarehouseId());
        return new ItemResult<>(returnDTO);
    }

    @Override
    public ItemResult<Void> updateWarehouse(WarehouseEditDTO warehouseEditDTO) {
        /**
         *
         * 功能描述: 更新仓库
         *
         * @param: [warehouseEditDTO]
         * @return: void
         */
        Warehouse warehouse = new Warehouse();
        BeanUtils.copyProperties(warehouseEditDTO,warehouse);
        WarehouseZoneMap warehouseZoneMap = null;
        Date date = new Date();
        List<WarehouseZoneMap> maps = new ArrayList<>();
        for (WarehouseZoneMapDTO w : warehouseEditDTO.getDeliveryAreas()
                ) {
            warehouseZoneMap = new WarehouseZoneMap();
            BeanUtils.copyProperties(w , warehouseZoneMap);
            warehouseZoneMap.setVersion(0L);
            warehouseZoneMap.setWarehouseId(warehouse.getWarehouseId());
            warehouseZoneMap.setDelFlg(0);
            warehouseZoneMap.setAddressMapId(uuidGenerator.gain());
            warehouseZoneMap.setCreateTime(date);
            warehouseZoneMap.setCreateUser(warehouse.getUpdateUser());
            warehouseZoneMap.setType(warehouse.getType());
            maps.add(warehouseZoneMap);
        }
        log.info("创建映射地址：{}",maps);
        warehouseBizService.modifyWarehouse(warehouseEditDTO, maps);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<List<WarehouseOptionDTO>> queryWarehouseIdAndName(WarehouseOptionQueryDTO warehouseOptionQueryDTO) {
        /**
         *
         * 功能描述: 通过仓库名字模糊查询仓库Options
         *
         * @param: [name]
         * @return: com.ecommerce.common.result.ItemResult<java.util.List<com.ecommerce.logistics.api.dto.warehouse.WarehouseOptionDTO>>
         */
        return new ItemResult<>(warehouseBizService.queryWarehouseOption(warehouseOptionQueryDTO));
    }

    @Override
    public ItemResult<WarehouseDetailsDTO> queryWarehouseDetails(String warehouseId) {
        WarehouseDetailsDTO result = warehouseBizService.queryWarehouseDetails(warehouseId);
        log.info("查询详情结果:{}",result);
        return new ItemResult<WarehouseDetailsDTO> (result);
    }

    @Override
    public ItemResult<String> searchAddress(SearchAddressDTO searchAddressDTO) {
        return new ItemResult<>(warehouseBizService.searchAddress(searchAddressDTO));
    }

    private List<WarehouseZoneMap> setWarehouseZoneMap(Warehouse warehouse, List<WarehouseZoneMapDTO> list, Date date){
        List<WarehouseZoneMap> maps = new ArrayList<>();
        WarehouseZoneMap warehouseZoneMap = null;
        for (WarehouseZoneMapDTO w : list
                ) {
            warehouseZoneMap = new WarehouseZoneMap();
            BeanUtils.copyProperties(w , warehouseZoneMap);
            warehouseZoneMap.setVersion(0L);
            warehouseZoneMap.setWarehouseId(warehouse.getWarehouseId());
            warehouseZoneMap.setDelFlg(0);
            warehouseZoneMap.setAddressMapId(uuidGenerator.gain());
            warehouseZoneMap.setCreateTime(date);
            warehouseZoneMap.setCreateUser(warehouse.getCreateUser());
            warehouseZoneMap.setType(warehouse.getType());
            maps.add(warehouseZoneMap);
        }
        return maps;
    }

	@Override
	public ItemResult<List<WarehouseOptionDTO>> selectSeckillWarehouseByUserId(String userId) {
		List<WarehouseOptionDTO> dtos = warehouseBizService.selectSeckillWarehouseByUserId(userId);
		return new ItemResult<>(dtos);
	}

    @Override
    public ItemResult<TradeWarehouseDTO> queryZoneWarehouse(WarehouseQueryDTO warehouseQueryDTO) {
        return null;
    }

    @Override
    public ItemResult<WarehouseBaseDataDTO> selectWarehouseBaseData(String warehouseId) {
        return new ItemResult<>(warehouseBizService.selectWarehouseBaseData(warehouseId));
    }

    @Override
    public ItemResult<WarehouseDetailsDTO> queryCentralWarehouseByArea(CentralWarehouseQueryDTO centralWarehouseQueryDTO) {
        //获取买家推荐门店
        WarehouseDetailsDTO warehouseDetailsDTO = null;
        if (CsStringUtils.isNotEmpty(centralWarehouseQueryDTO.getBuyerId())) {
            ReferrerBuyerRelationDTO referrerBuyerRelationDTO = buyerAndReferrerService.findByBuyerAccountId(centralWarehouseQueryDTO.getBuyerId());
            if (referrerBuyerRelationDTO != null &&
                    CsStringUtils.isNotEmpty(referrerBuyerRelationDTO.getReferrerStoreId()) &&
                referrerBuyerRelationDTO.getReferrerType().equals(1)) {
                warehouseDetailsDTO = warehouseBizService.queryWarehouseDetail(referrerBuyerRelationDTO.getReferrerStoreId());
            }
        }
        //没有推荐门店直接查询区域范围内中心仓
        if (warehouseDetailsDTO == null) {
            warehouseDetailsDTO = warehouseBizService.queryCentralWarehouseByArea(centralWarehouseQueryDTO);
        }

        return new ItemResult<>(warehouseDetailsDTO);
    }

    @Override
    public ItemResult<WarehouseDetailsDTO> queryStoreByCode(StoreQueryDTO storeQueryDTO) {
        //获取推荐门店
        WarehouseDetailsDTO warehouseDetailsDTO = null;
        if (CsStringUtils.isNotEmpty(storeQueryDTO.getStoreCode())) {
            ReferrerInfoQueryDTO referrerInfoQueryDTO = new ReferrerInfoQueryDTO();
            referrerInfoQueryDTO.setReferrerCode(storeQueryDTO.getStoreCode());
            PageInfo<ReferrerInfoDTO> pageInfo = buyerAndReferrerService.findAllReferrerInfoByCondition(referrerInfoQueryDTO);
            if (pageInfo != null && CollectionUtils.isNotEmpty(pageInfo.getList()) &&
                    CsStringUtils.isNotEmpty(pageInfo.getList().get(0).getReferrerStoreId())) {
                warehouseDetailsDTO = warehouseBizService.queryWarehouseDetail(pageInfo.getList().get(0).getReferrerStoreId());
            }
        }
        //没有推荐门店直接查询区域范围内中心仓
        if (warehouseDetailsDTO == null) {
            CentralWarehouseQueryDTO centralWarehouseQueryDTO = new CentralWarehouseQueryDTO();
            BeanUtils.copyProperties(storeQueryDTO, centralWarehouseQueryDTO);
            warehouseDetailsDTO = warehouseBizService.queryCentralWarehouseByArea(centralWarehouseQueryDTO);
        }

        return new ItemResult<>(warehouseDetailsDTO);
    }
}
