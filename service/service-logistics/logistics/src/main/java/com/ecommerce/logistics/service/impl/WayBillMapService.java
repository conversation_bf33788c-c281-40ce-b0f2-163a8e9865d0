package com.ecommerce.logistics.service.impl;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.logistics.biz.IWaybillMapBizService;
import com.ecommerce.logistics.dao.dto.waybill.WaybillMapDTO;
import com.ecommerce.logistics.service.IWaybillMapService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * @Auther: <EMAIL>
 * @Date: 12/08/2020 17:44
 * @Description:
 */
@Slf4j
@Service
public class WayBillMapService implements IWaybillMapService {

    @Autowired
    private IWaybillMapBizService waybillMapBizService;

    @Override
    public ItemResult<String> addWaybillMap(WaybillMapDTO waybillMapDTO) {
        return new ItemResult<>(waybillMapBizService.addWaybillMap(waybillMapDTO));
    }

    @Override
    public ItemResult<Void> updateWaybillMap(WaybillMapDTO waybillMapDTO) {
        waybillMapBizService.updateWaybillMap(waybillMapDTO);
        return new ItemResult<>(null);
    }
}
