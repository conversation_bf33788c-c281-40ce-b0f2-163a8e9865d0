package com.ecommerce.logistics.service.impl;

import com.alibaba.fastjson.JSON;
import com.ecommerce.base.api.dto.warehouse.WarehouseDetailsDTO;
import com.ecommerce.base.api.service.IWarehouseService;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.exception.DistributeLockException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.common.service.ILockService;
import com.ecommerce.common.service.common.BaseService;
import com.ecommerce.common.utils.DateUtil;
import com.ecommerce.logistics.api.dto.attachment.AttListAddDTO;
import com.ecommerce.logistics.api.dto.attachment.AttListDTO;
import com.ecommerce.logistics.api.dto.attachment.AttListQueryDTO;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.driver.AssignDriverLogAddDTO;
import com.ecommerce.logistics.api.dto.operationrecord.OperationRecordAddDTO;
import com.ecommerce.logistics.api.dto.operationrecord.OperationRecordDTO;
import com.ecommerce.logistics.api.dto.operationrecord.QueryWaybillOperationDTO;
import com.ecommerce.logistics.api.dto.pickingbill.AssignWaybillDTO;
import com.ecommerce.logistics.api.dto.pickingbill.AssignWaybillDetailDTO;
import com.ecommerce.logistics.api.dto.pickingbill.PickingBillDTO;
import com.ecommerce.logistics.api.dto.vehicle.SelfPickingVehicleDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleQueryConditionDTO;
import com.ecommerce.logistics.api.dto.warehouse.WarehouseOptionDTO;
import com.ecommerce.logistics.api.dto.waybill.AppWaybillListQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.AppWaybillQueryConditionsMapDTO;
import com.ecommerce.logistics.api.dto.waybill.AppWaybillStatisticsDTO;
import com.ecommerce.logistics.api.dto.waybill.ArriveWarehouseDTO;
import com.ecommerce.logistics.api.dto.waybill.BatchPassCheckDTO;
import com.ecommerce.logistics.api.dto.waybill.CancelReasonDTO;
import com.ecommerce.logistics.api.dto.waybill.CancelWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.CarrierWaybillListDTO;
import com.ecommerce.logistics.api.dto.waybill.CarrierWaybillListQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.CarryWaybillSubmitDTO;
import com.ecommerce.logistics.api.dto.waybill.ChangeCarriageDTO;
import com.ecommerce.logistics.api.dto.waybill.ChangePriceDTO;
import com.ecommerce.logistics.api.dto.waybill.CheckWaybillListQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.CloseWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.CompleteWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.DiscardWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.DistrictListDTO;
import com.ecommerce.logistics.api.dto.waybill.DriverCancelWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.DriverConfirmDTO;
import com.ecommerce.logistics.api.dto.waybill.DriverDeliveryDTO;
import com.ecommerce.logistics.api.dto.waybill.DriverWaybillAssignDTO;
import com.ecommerce.logistics.api.dto.waybill.DriverWaybillListQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.EnterFactoryDTO;
import com.ecommerce.logistics.api.dto.waybill.EvaluateWaybillListDTO;
import com.ecommerce.logistics.api.dto.waybill.ExternalWaybillRequestDTO;
import com.ecommerce.logistics.api.dto.waybill.LeaveWarehouseDTO;
import com.ecommerce.logistics.api.dto.waybill.LocationDTO;
import com.ecommerce.logistics.api.dto.waybill.MergeWaybillListDTO;
import com.ecommerce.logistics.api.dto.waybill.MergeWaybillListQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.MonitorTimeDTO;
import com.ecommerce.logistics.api.dto.waybill.NotifyLeaveWarehouseDTO;
import com.ecommerce.logistics.api.dto.waybill.PassCheckDTO;
import com.ecommerce.logistics.api.dto.waybill.PublishWaybillListDTO;
import com.ecommerce.logistics.api.dto.waybill.PublishWaybillListQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.ReassignmentVehicleDTO;
import com.ecommerce.logistics.api.dto.waybill.ReceiverInfoDTO;
import com.ecommerce.logistics.api.dto.waybill.RepublishWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.SeckillScanQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.SellerAssignWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.SnatchWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.SnatchedWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.SnatchedWaybillQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.TradeWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.TradeWaybillDetailDTO;
import com.ecommerce.logistics.api.dto.waybill.UploadCertificateDTO;
import com.ecommerce.logistics.api.dto.waybill.UserSeckillWaybillListDTO;
import com.ecommerce.logistics.api.dto.waybill.UserSeckillWaybillQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.WaitCheckWaybillListDTO;
import com.ecommerce.logistics.api.dto.waybill.WarehouseAdminProccessedDTO;
import com.ecommerce.logistics.api.dto.waybill.WarehouseAdminProccessedQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.WarehouseAdminWaitDeliveryDTO;
import com.ecommerce.logistics.api.dto.waybill.WarehouseAdminWaitDeliveryQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillAssignDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillAssignListDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillAssignListQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillCodeDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillDetailDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillDraftDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillLocationDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillLocationQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillMonitorPrepareDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillMonitorStatusNotifyDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillStatusOption;
import com.ecommerce.logistics.api.enums.AttachmentTypeEnum;
import com.ecommerce.logistics.api.enums.BillProxyTypeEnum;
import com.ecommerce.logistics.api.enums.ExternalSyncFlagEnum;
import com.ecommerce.logistics.api.enums.LogisticsMessageTypeEnum;
import com.ecommerce.logistics.api.enums.OperationRecordTypeEnum;
import com.ecommerce.logistics.api.enums.SnatchWaybillReturnEnum;
import com.ecommerce.logistics.api.enums.UserRoleEnum;
import com.ecommerce.logistics.api.enums.WarehouseTypeEnum;
import com.ecommerce.logistics.api.enums.WaybillOperationTypeEnum;
import com.ecommerce.logistics.api.enums.WaybillStatusEnum;
import com.ecommerce.logistics.api.enums.WaybillTypeEnum;
import com.ecommerce.logistics.biz.IAssignDriverLogBizService;
import com.ecommerce.logistics.biz.IAttachmentBizService;
import com.ecommerce.logistics.biz.IBillSyncBizService;
import com.ecommerce.logistics.biz.ICreditStatisticBizService;
import com.ecommerce.logistics.biz.IMonitorJudgeBizService;
import com.ecommerce.logistics.biz.IOperationRecordBizService;
import com.ecommerce.logistics.biz.IPickingMapBizService;
import com.ecommerce.logistics.biz.IProxySyncRecordBizService;
import com.ecommerce.logistics.biz.IWaybillMapBizService;
import com.ecommerce.logistics.biz.IWaybillSignBizService;
import com.ecommerce.logistics.biz.annotation.OperationLimit;
import com.ecommerce.logistics.biz.impl.PickingBillBizService;
import com.ecommerce.logistics.biz.impl.VehicleBizService;
import com.ecommerce.logistics.biz.impl.WaybillOperationBizService;
import com.ecommerce.logistics.biz.impl.WaybillQueryBizService;
import com.ecommerce.logistics.biz.message.ISMSMessageBizService;
import com.ecommerce.logistics.biz.message.MessageQueueService;
import com.ecommerce.logistics.biz.stock.IPlatformStoreMapBizService;
import com.ecommerce.logistics.dao.dto.storehouse.PlatformStoreChangeBean;
import com.ecommerce.logistics.dao.dto.waybill.CompleteWaybillResultDO;
import com.ecommerce.logistics.dao.dto.waybill.ConfirmWaybillDO;
import com.ecommerce.logistics.dao.dto.waybill.TradeWaybillDetailDO;
import com.ecommerce.logistics.dao.dto.waybill.UpdatePickingQuantityResultDO;
import com.ecommerce.logistics.dao.dto.waybill.WaybillQuantityDO;
import com.ecommerce.logistics.dao.dto.waybill.WaybillUserInfoDTO;
import com.ecommerce.logistics.dao.dto.waybillmap.PrimaryWaybillErpInfo;
import com.ecommerce.logistics.dao.vo.PickingMap;
import com.ecommerce.logistics.dao.vo.Vehicle;
import com.ecommerce.logistics.dao.vo.Waybill;
import com.ecommerce.logistics.exception.LogisticsErrorCode;
import com.ecommerce.logistics.service.IWaybillExternalService;
import com.ecommerce.logistics.service.IWaybillService;
import com.ecommerce.logistics.util.DigestUtils;
import com.ecommerce.logistics.util.LogisticsUtils;
import com.ecommerce.order.api.dto.TakeItemDTO;
import com.ecommerce.order.api.dto.UpdateTakeInfoShippedDTO;
import com.ecommerce.order.api.dto.UpdateTakeItemShippedDTO;
import com.ecommerce.order.api.dto.logistics.OverSendOutVerifyDTO;
import com.ecommerce.order.api.dto.logistics.OverSendOutVerifyResponseDTO;
import com.ecommerce.order.api.enums.AdjustAddWayEnum;
import com.ecommerce.order.api.service.ITakeInfoService;
import com.ecommerce.order.api.service.ITakeUpDataService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * @Author: <EMAIL>
 * @Date: 2018-08-03 13:29
 * @Description:
 */
@Slf4j
@Service
public class WaybillService extends BaseService<Waybill> implements IWaybillService {
    private final static String YYYYMMDDHHMMSS_FORMAT = "yyyy-MM-dd HH:mm:ss";
    private static final String FINISH_TAKE_INFO_DTO = "finishTakeInfoDTO=";

    @Autowired
    private WaybillQueryBizService waybillQueryBizService;

    @Autowired
    private WaybillOperationBizService waybillOperationBizService;

    @Autowired
    private VehicleBizService vehicleBizService;

    @Autowired
    private PickingBillBizService pickingBillBizService;

    @Autowired
    private ITakeUpDataService takeUpDataService;

    @Autowired
    private MessageQueueService messageQueueService;

    @Autowired
    private ILockService redisLockService;

    @Autowired
    private ISMSMessageBizService smsMessageBizService;

    @Autowired
    private IOperationRecordBizService operationRecordBizService;

    @Autowired
    private IWarehouseService warehouseService;

    @Autowired
    private IWaybillExternalService waybillExternalService;

    @Autowired
    private IWaybillSignBizService waybillSignBizService;

    @Autowired
    private IPlatformStoreMapBizService platformStoreMapBizService;

    @Autowired
    private ITakeInfoService takeInfoService;

    @Autowired
    private IAssignDriverLogBizService assignDriverLogBizService;

    @Autowired
    private IMonitorJudgeBizService monitorJudgeBizService;

    @Autowired
    private IWaybillMapBizService waybillMapBizService;

    @Autowired
    private IProxySyncRecordBizService proxySyncRecordBizService;

    @Autowired
    private IBillSyncBizService billSyncBizService;

    @Autowired
    private IPickingMapBizService pickingMapBizService;

    @Autowired
    private ICreditStatisticBizService creditStatisticBizService;

    @Autowired
    private IAttachmentBizService attachmentBizService;

    @Override
    public ItemResult<PageData<MergeWaybillListDTO>> queryMergeWaybillList(PageQuery<MergeWaybillListQueryDTO> pageQuery) {
        return new ItemResult<>(waybillQueryBizService.selectMergeWaybillList(pageQuery));
    }

    @Override
    public ItemResult<PageData<WaitCheckWaybillListDTO>> queryCheckWaybillList(PageQuery<CheckWaybillListQueryDTO> pageQuery) {
        return new ItemResult<>(waybillQueryBizService.selectCheckWaybillList(pageQuery));
    }

    @Override
    public ItemResult<PageData<PublishWaybillListDTO>> queryPublishWaybillList(PageQuery<PublishWaybillListQueryDTO> pageQuery) {
        return new ItemResult<>(waybillQueryBizService.selectPublishWaybillList(pageQuery));
    }

    @Override
    public ItemResult<PageData<WaybillAssignListDTO>> queryWaybillAssignList(PageQuery<WaybillAssignListQueryDTO> pageQuery) {
        return new ItemResult<>(waybillQueryBizService.selectWaybillAssignList(pageQuery));
    }

    @Override
    public ItemResult<WaybillDetailDTO> getWaybillDetail(String waybillId) {
        return new ItemResult<>(waybillQueryBizService.selectWaybillDetailByWaybillId(waybillId));
    }

    @Override
    public ItemResult<WaybillDetailDTO> getWaybillDetailByNum(String waybillNum) {
        return new ItemResult<>(waybillQueryBizService.selectWaybillDetailByWaybillNum(waybillNum));
    }

    @Override
    public ItemResult<Void> changePrice(ChangePriceDTO dto) {
        return null;
    }

    @Override
    public ItemResult<Void> assignWaybill(WaybillAssignDTO waybillAssignDTO) {
        /**
         *
         * 功能描述: 将无人接单或取消的运单指派给承运商，即修改运单的承运商ID，并且将运单状态
         * 修改为待配送
         *
         * @param: [waybillAssignDTO]
         * @return: void
         */
        waybillOperationBizService.waybillAssign(waybillAssignDTO);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> arriveWarehouse(ArriveWarehouseDTO arriveWarehouseDTO) {
        /**
         *
         * 功能描述: 通过运单Id去更新进站时间
         *
         * @param: [waybillId]
         * @return: void
         */
        waybillOperationBizService.arriveWarehouse(arriveWarehouseDTO);
        //通知位置中心到达提货点

        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> inputLeaveWarehouseQuantity(LeaveWarehouseDTO leaveWarehouseDTO) {
        log.info("inputLeaveWarehouseQuantity:" + JSON.toJSONString(leaveWarehouseDTO));
        //分布式锁控制重复操作问题
        String identifier = "";
        String bizResource = DigestUtils.md5Digest(leaveWarehouseDTO.toString());
        try {
            identifier = redisLockService.lockFast(bizResource);
            Waybill waybill = waybillQueryBizService.selectWaybillById(leaveWarehouseDTO.getWaybillId());

            verifyWaybillAndWarehouse(leaveWarehouseDTO, waybill);

            List<PlatformStoreChangeBean> platformStoreChangeBeanList = Lists.newArrayList();
            //录入中心仓出厂量检验
            if (LogisticsUtils.isSame(leaveWarehouseDTO.getCentralWarehouseFlag(), 1)) {
                //中心仓
                PlatformStoreChangeBean bean = new PlatformStoreChangeBean();
                PickingBillDTO pickingBillDetail = pickingBillBizService.getPickingBillDetail(waybill.getPickingBillId());
                bean.setWarehouseId(pickingBillDetail.getWarehouseId());
                bean.setUserId(pickingBillDetail.getSellerId());
                bean.setUserName(pickingBillDetail.getSellerName());
                List<TakeItemDTO> takeItemDTOS = takeInfoService.selectItemByOrderItemId(pickingBillDetail.getProductId());
                bean.setProductId(takeItemDTOS.get(0).getGoodsId());
                bean.setNote(pickingBillDetail.getProductDesc());
                bean.setUsedStock(leaveWarehouseDTO.getActualQuantity());
                bean.setUpdateUser(leaveWarehouseDTO.getUserId());
                platformStoreMapBizService.checkForLeaveCenterWarehouse(bean);
                platformStoreChangeBeanList.add(bean);
            }
            //当EC控制出站时需要先发起超发验证
            if (leaveWarehouseDTO.getExternalFlag() == null || leaveWarehouseDTO.getExternalFlag() != 1) {
                verifyWaybillActualQuantity(waybill);

                PickingBillDTO pickingBillDTO = pickingBillBizService.getPickingBillDetail(waybill.getPickingBillId());
                OverSendOutVerifyDTO overSendOutVerifyDTO = new OverSendOutVerifyDTO();
                overSendOutVerifyDTO.setTakeCode(pickingBillDTO.getDeliverySheetNum());
                overSendOutVerifyDTO.setWaybillNum(waybill.getWaybillNum());
                overSendOutVerifyDTO.setOrderItemId(pickingBillDTO.getResourceId());
                overSendOutVerifyDTO.setQuantity(pickingBillDTO.getProductQuantity());
                overSendOutVerifyDTO.setActualQuantity(leaveWarehouseDTO.getActualQuantity());
                overSendOutVerifyDTO.setOperator(leaveWarehouseDTO.getUserId());
                overSendOutVerifyDTO.setLogisticsAmount(pickingBillDTO.getUnitCarriage()
                        .multiply(leaveWarehouseDTO.getActualQuantity()));
                ItemResult<OverSendOutVerifyResponseDTO> verifyResult = takeUpDataService.overSendOutVerify(overSendOutVerifyDTO);
                verifyResult(verifyResult);

                if (!verifyResult.getData().getCanOut()) {
                    //发生超发补款时先填充出厂数量
                    waybillOperationBizService.fillLeaveWarehouseQuantity(leaveWarehouseDTO);
                    return new ItemResult<>(null);
                }
            }
            //正向同步时，如果为中心仓出货则允许进行页面出站操作并同步ERP
            WaybillDraftDTO waybillDraftDTO = waybillQueryBizService.selectWaybillDraftInfoByWaybillId(leaveWarehouseDTO.getWaybillId());
            WarehouseDetailsDTO warehouseDetailsDTO = warehouseService.queryWarehouseDetails(waybillDraftDTO.getWarehouseId());
            leaveWarehouseDTO.setCentralWarehouseFlag(0);
            if (WarehouseTypeEnum.CENTRAL_WAREHOUSE.getCode().equals(warehouseDetailsDTO.getType())) {
                leaveWarehouseDTO.setCentralWarehouseFlag(1);
            }
            Boolean mainWaybillLeaveFlag = waybillOperationBizService.inputLeaveWarehouseQuantity(leaveWarehouseDTO, platformStoreChangeBeanList);
            waybill = waybillQueryBizService.selectWaybillById(leaveWarehouseDTO.getWaybillId());
            //通知ERP发货(中心仓出货-平台调度)
            if (leaveWarehouseDTO.getCentralWarehouseFlag() == 1 && ExternalSyncFlagEnum.FORWARD_SYNC.getCode().equals(waybill.getSyncFlag())) {
                waybillExternalService.externalOpenBillAndSendOut(leaveWarehouseDTO.getWaybillId());
            } else {
                notifyTradeShippedQuantity(waybill, leaveWarehouseDTO.getUserName(), mainWaybillLeaveFlag);
            }

            //同步运单数据, 出站只会一级同步到二级
            if (CsStringUtils.equals(waybill.getBillProxyType(), BillProxyTypeEnum.PRIMARY.getCode())) {
                LeaveWarehouseDTO relationLeaveWarehouseDTO = new LeaveWarehouseDTO();
                String relationWaybillId = waybillMapBizService.findRelationWaybillId(waybill.getWaybillId(), waybill.getBillProxyType());
                BeanUtils.copyProperties(leaveWarehouseDTO, relationLeaveWarehouseDTO);
                relationLeaveWarehouseDTO.setWaybillId(relationWaybillId);
                relationLeaveWarehouseDTO.setFromWebFlag((byte)0);
                relationLeaveWarehouseDTO.setExternalFlag(0);
                proxySyncRecordBizService.syncLeaveWarehouse(waybill.getWaybillId(), relationLeaveWarehouseDTO);
            }

            //被动接收的同步方,判断是否需要自己完成:
            //出现原因:二级单,之前未补款,但是一级为操作方已经完成了.现在二级单补款,然后会自动调用出站,此时补充自动完成(其实是填补之前完成的同步操作)
            //满足条件: 1.canOperate为0; 2.billProxyType是二级; 3.relationWaybill已经完成了
            //试用场景: 一级是 卖家/平台(出站方+调度方);二级是卖家,且此时为二级
            if (monitorJudgeBizService.needSynBKBCompleteByLeaveWarehouse(waybill.getBillProxyType(), waybill.getCanOperate(), waybill.getWaybillId())) {
                //需要做同步
                CompleteWaybillDTO relationCompleteWaybillDTO = new CompleteWaybillDTO();
                relationCompleteWaybillDTO.setOperationUserId(leaveWarehouseDTO.getUserId());
                relationCompleteWaybillDTO.setOperationUserName("system");
                relationCompleteWaybillDTO.setWaybillId(waybill.getWaybillId());

                proxySyncRecordBizService.syncCompleteWaybill(waybill.getWaybillId(), relationCompleteWaybillDTO);
            }

            // 发送消息
            smsMessageBizService.sendVehicleFactoryBuyer(leaveWarehouseDTO.getWaybillId());
            smsMessageBizService.sendVehicleFactoryCarrier(leaveWarehouseDTO.getWaybillId());
            return new ItemResult<>(null);
        } catch (DistributeLockException e) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "您点击太快了，请勿重复操作！");
        } finally {
            if (CsStringUtils.isNotEmpty(identifier)) {
                redisLockService.unlock(bizResource, identifier);
            }
        }
    }

    private static void verifyWaybillActualQuantity(Waybill waybill) {
        if (waybill.getActualQuantity() != null && waybill.getActualQuantity().compareTo(BigDecimal.ZERO) > 0) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "运单[" + waybill.getWaybillNum() + "]已经录入出厂量，请不要重复操作！");
        }
    }

    private static void verifyResult(ItemResult<OverSendOutVerifyResponseDTO> verifyResult) {
        if (!verifyResult.isSuccess()) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "调用交易超发验证接口异常");
        }
    }

    private void verifyWaybillAndWarehouse(LeaveWarehouseDTO leaveWarehouseDTO, Waybill waybill) {
        if (LogisticsUtils.isSame(leaveWarehouseDTO.getFromWebFlag(), (byte)1)
                && CsStringUtils.equals(waybill.getBillProxyType(), BillProxyTypeEnum.SECONDARY.getCode())) {
            //web端只能出站一级
            throw new BizException(BasicCode.UNDEFINED_ERROR, "本级背靠背运单不可出站!");
        }
        //特殊情况: 运单只会出站一级运单,二级运单为同步运单!
        if (CsStringUtils.equals(waybill.getBillProxyType(), BillProxyTypeEnum.PRIMARY.getCode())) {
            if (!monitorJudgeBizService.canChangeStatus(waybill.getBillProxyType(), waybill.getWaybillId(), waybill.getStatus(), waybill.getCanOperate())) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "关联的背靠背运单当前状态不可出站!");
            }
        }
    }

    @Override
    public void notifyTradeShippedQuantity(Waybill waybill,
                                           String operatorName,
                                           Boolean mainWaybillLeaveFlag) {
        log.info("通知交易出站:{}/{}/{}", waybill, operatorName, mainWaybillLeaveFlag);
        //交易发货数量变更通知
        PickingBillDTO pickingBillDTO = pickingBillBizService.getPickingBillDetail(waybill.getPickingBillId());
        UpdateTakeInfoShippedDTO updateTakeInfoShippedDTO = new UpdateTakeInfoShippedDTO();
        updateTakeInfoShippedDTO.setTakeCode(pickingBillDTO.getDeliverySheetNum());
        updateTakeInfoShippedDTO.setOperator(waybill.getUpdateUser());
        List<UpdateTakeItemShippedDTO> updateTakeItemList = new ArrayList<>();
        UpdateTakeItemShippedDTO updateTakeItemShippedDTO = new UpdateTakeItemShippedDTO();
        updateTakeItemShippedDTO.setResourceId(pickingBillDTO.getResourceId());
        //(周曾要求从累加量变为运单出厂量)
        updateTakeItemShippedDTO.setShippedQuantity(waybill.getActualQuantity());
        updateTakeItemShippedDTO.setCost(pickingBillDTO.getUnitCarriage().multiply(waybill.getActualQuantity()));
        //设置当前实际出厂累加量
        BigDecimal actualQuantity = waybillQueryBizService.countActualSendQuantity(waybill.getPickingBillId());
        updateTakeItemShippedDTO.setTotalActualQuantity(actualQuantity);
        updateTakeItemList.add(updateTakeItemShippedDTO);
        updateTakeInfoShippedDTO.setWaybillNum(waybill.getWaybillNum());
        updateTakeInfoShippedDTO.setUpdateTakeItemShippedDTO(updateTakeItemList);
        try {
            log.info("notifyTradeShippedQuantity:" + JSON.toJSONString(updateTakeInfoShippedDTO));
            takeUpDataService.doLogisticUpdateShippedQuantity(updateTakeInfoShippedDTO);
        } catch (Exception e) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "交易中心接口异常");
        }
        //自提运单更新完成数量
        if (waybill.getType().equals(WaybillTypeEnum.BUYER_PICK.getCode())) {
            log.info("自提单,出站即完成操作!");
            UpdatePickingQuantityResultDO updatePickingResultDO = pickingBillBizService.updatePickingCompleteQuantity(
                    waybill.getPickingBillId(),
                    waybill.getActualQuantity(),
                    waybill.getUpdateUser(),
                    operatorName,
                    Boolean.FALSE);
            log.info("doLogisticFinishTakeInfo:" + JSON.toJSONString(updatePickingResultDO));
            //通知交易发货单完成
            if (updatePickingResultDO.getOrderNotifyFlag()) {
                ItemResult<Boolean> result = takeUpDataService.doLogisticFinishTakeInfo(updatePickingResultDO.getFinishTakeInfoDTO());
                if (result == null || !result.isSuccess()) {
                    throw new BizException(LogisticsErrorCode.NOTIFY_TRADE_ERROR,
                            FINISH_TAKE_INFO_DTO + updatePickingResultDO.getFinishTakeInfoDTO());
                }
            }
            //如果自提单-主运单出站且可以监控
            if (mainWaybillLeaveFlag && LogisticsUtils.isSame(waybill.getCanMonitor(), (byte)1)) {
                // 自提单可监控需要通知trace
            }
            return;
        }
        //非买家自提
        if (mainWaybillLeaveFlag) {
            //非买家自提的运单-主运单出站完成开始监控通知
        }
    }

    //现在只会用于非整合运单
    @Override
    public ItemResult<Void> completeWaybill(CompleteWaybillDTO completeWaybillDTO) {
        log.info("completeWaybill:" + JSON.toJSONString(completeWaybillDTO));
        //分布式锁控制重复操作问题
        String identifier = "";
        String bizResource = DigestUtils.md5Digest(completeWaybillDTO.toString());
        Waybill waybill = null;
        try {
            identifier = redisLockService.lockFast(bizResource);
            waybill = waybillQueryBizService.selectWaybillById(completeWaybillDTO.getWaybillId());
            if (WaybillStatusEnum.COMPLETED.getCode().equals(waybill.getStatus())) {
                return new ItemResult<>(null);
            }

            if (CsStringUtils.equals(waybill.getStatus(), WaybillStatusEnum.WAIT_DELIVERY.getCode()) &&
                    LogisticsUtils.isSame(waybill.getCanOperate(), (byte)0)) {
                //同步背靠背操作,未补款,忽略.
                return new ItemResult<>();
            }

            completeWaybillDTO.setCompleteTime(new Date());

            WaybillOperationTypeEnum waybillOperationTypeEnum = WaybillOperationTypeEnum.WAYBILL_COMPLETE;
            Boolean waybillCompleteFlag = true;
            CompleteWaybillResultDO completeWaybillResultDO = null;
            Boolean isSign = false;
            if (CsStringUtils.equals(waybill.getType(), WaybillTypeEnum.BUYER_PICK.getCode())) {
                // 买家自提的完成流程修改:
                log.info("此为买家自提运单的监控完成:{}", completeWaybillDTO);
                //临时补充 monitorCompleteTime 后续会被trace的通知修改
                waybillCompleteFlag = waybillOperationBizService.updateMCTForBuyerPickCurr(waybill);
            } else {
                completeWaybillResultDO = waybillOperationBizService.completeWaybill(completeWaybillDTO);
                log.info("CompleteWaybillResultDO:" + JSON.toJSONString(completeWaybillResultDO));
                waybillCompleteFlag = completeWaybillResultDO.getWaybillCompleteFlag();
                waybillOperationTypeEnum = completeWaybillResultDO.getWaybillOperationTypeEnum();
                isSign = completeWaybillResultDO.getIsSign();
            }
            //添加操作记录
            OperationRecordAddDTO operationRecordAddDTO = new OperationRecordAddDTO();
            operationRecordAddDTO.setEntryId(completeWaybillDTO.getWaybillId());
            operationRecordAddDTO.setType(OperationRecordTypeEnum.WAYBILL.getCode());
            operationRecordAddDTO.setOperationType(waybillOperationTypeEnum.getCode());
            operationRecordAddDTO.setOperatorId(completeWaybillDTO.getOperationUserId());
            operationRecordAddDTO.setOperatorName(completeWaybillDTO.getOperationUserName());
            operationRecordAddDTO.setContent(waybillOperationTypeEnum.getDesc());
            operationRecordBizService.saveOperationRecord(operationRecordAddDTO, completeWaybillDTO.getCompleteTime());
            //门店配送直接返回结果
            if (WaybillTypeEnum.STORE_DELIVERY.getCode().equals(waybill.getType())) {
                return new ItemResult<>(null);
            }
            if (!isSign) {

            }
            //混凝土通知签收同步通知交易
            if (completeWaybillResultDO != null && completeWaybillResultDO.getNeedNotifyCompleteConcrete()) {
                Waybill notifyWaybill = waybillQueryBizService.selectWaybillById(completeWaybillDTO.getWaybillId());
                waybillSignBizService.notifyTradeSignQuantity(notifyWaybill);
                //在消息队列中获取台班费,异步通知erp,失败保存到异常列表中
                messageQueueService.sendMQ(waybill.getWaybillId(), LogisticsMessageTypeEnum.NOTIFY_ERP_COMPLETE_CONCRETE.getCode());
            }
            //通知交易发货单完成
            completeWaybill(completeWaybillResultDO, waybill, waybillCompleteFlag, isSign);

            return new ItemResult<>(null);
        } catch (DistributeLockException e) {
            throw new BizException(BasicCode.INVALID_PARAM, "重复操作：completeWaybillDTO=" + completeWaybillDTO);
        } finally {
            if (CsStringUtils.isNotEmpty(identifier)) {
                redisLockService.unlock(bizResource, identifier);
            }
            // 发送消息
            smsMessageBizService.sendWaybillCarrierCompleted(completeWaybillDTO.getWaybillId());

            /**************   背靠背同步处理   ******************/
            if (waybill != null && monitorJudgeBizService.needSyncComplete(waybill.getBillProxyType(), waybill.getCanOperate(), waybill.getWaybillId())) {
                //需要做同步
                CompleteWaybillDTO relationCompleteWaybillDTO = new CompleteWaybillDTO();
                BeanUtils.copyProperties(completeWaybillDTO, relationCompleteWaybillDTO);
                String relationWaybillId = waybillMapBizService.findRelationWaybillId(waybill.getWaybillId(), waybill.getBillProxyType());
                relationCompleteWaybillDTO.setWaybillId(relationWaybillId);
                proxySyncRecordBizService.syncCompleteWaybill(waybill.getWaybillId(), relationCompleteWaybillDTO);
            }

        }
    }

    private void completeWaybill(CompleteWaybillResultDO completeWaybillResultDO, Waybill waybill, Boolean waybillCompleteFlag, Boolean isSign) {
        if (completeWaybillResultDO != null && completeWaybillResultDO.getUpdatePickingResultDO().getOrderNotifyFlag()) {
            log.info("通知交易发货单完成:" + JSON.toJSONString(completeWaybillResultDO.getUpdatePickingResultDO().getFinishTakeInfoDTO()));
            ItemResult<Boolean> result = takeUpDataService.doLogisticFinishTakeInfo(
                    completeWaybillResultDO.getUpdatePickingResultDO().getFinishTakeInfoDTO());
            if (result == null || !result.isSuccess()) {
                throw new BizException(LogisticsErrorCode.NOTIFY_TRADE_ERROR, FINISH_TAKE_INFO_DTO
                        + completeWaybillResultDO.getUpdatePickingResultDO().getFinishTakeInfoDTO());
            }
        }
        //子运单完成触发主运单完成
        if (waybill.getIsMainWaybill() == 0 && waybillCompleteFlag && !isSign) {
            //完成主运单监控通知
        }
    }

    //现用于完成整合运单的子运单
    @Override
    public ItemResult<Void> completeWaybillBySubWaybillIds(CompleteWaybillDTO completeWaybillDTO){
    	log.info("completeWaybillBySubWaybillIds:" + JSON.toJSONString(completeWaybillDTO));
        if(CollectionUtils.isEmpty(completeWaybillDTO.getSubWaybillIds())) {
            return new ItemResult<>(null);
        }
        List<Waybill> waybills = null;
    	//分布式锁控制重复操作问题
        String identifier = "";
        String bizResource = DigestUtils.md5Digest(completeWaybillDTO.toString());
        try {
            identifier = redisLockService.lockFast(bizResource);
            completeWaybillDTO.setCompleteTime(new Date());
            CompleteWaybillResultDO completeWaybillResultDO = waybillOperationBizService.completeWaybillBySubWaybillIds(completeWaybillDTO);
            log.info("CompleteWaybillResultDO:" + JSON.toJSONString(completeWaybillResultDO));
            Boolean waybillCompleteFlag = completeWaybillResultDO.getWaybillCompleteFlag();
            Boolean isSign = completeWaybillResultDO.getIsSign();
            waybills = waybillQueryBizService.selectWaybillByWaybillIds(completeWaybillDTO.getSubWaybillIds());
            waybills.parallelStream().forEach(waybill->{
            	//门店配送不用发MQ
                if (WaybillTypeEnum.STORE_DELIVERY.getCode().equals(waybill.getType())) {
                    return;
                }

                notifyByMq(waybill, isSign, completeWaybillResultDO);

                //通知交易发货单完成
                finishTakeInfo(completeWaybillResultDO);
            });
            Boolean needSendMainWaybillMQ = Boolean.TRUE;
            if (completeWaybillDTO.getSubWaybillIds().size() == 1) {
            	Waybill waybill = waybills.get(0);
            	if (waybill.getParentId().equals(waybill.getWaybillId())) {
            		needSendMainWaybillMQ = Boolean.FALSE;
            	}
            }
            //子运单完成触发主运单完成,不是签收
            if (needSendMainWaybillMQ && waybillCompleteFlag && !isSign) {
                //完成主运单监控通知
                Waybill mainWaybill = waybillQueryBizService.selectWaybillById(waybills.get(0).getParentId());
                if (!WaybillTypeEnum.STORE_DELIVERY.getCode().equals(mainWaybill.getType())) {
                    // 发送消息
                    smsMessageBizService.sendWaybillDriverCompleted(mainWaybill.getWaybillId());
                    smsMessageBizService.sendWaybillCarrierCompleted(mainWaybill.getWaybillId());
                    // 通知trace 主运单完成监控
                }
            }
            return new ItemResult<>(null);
        } catch (DistributeLockException e) {
            throw new BizException(BasicCode.INVALID_PARAM, "重复操作：completeWaybillDTO=" + completeWaybillDTO);
        } finally {
            if (CsStringUtils.isNotEmpty(identifier)) {
                redisLockService.unlock(bizResource, identifier);
            }

            /**************   背靠背同步处理   ******************/
            handleWaybills(completeWaybillDTO, waybills);

        }
    }

    private void handleWaybills(CompleteWaybillDTO completeWaybillDTO, List<Waybill> waybills) {
        if (CollectionUtils.isEmpty(waybills)) return;

        for (Waybill waybill : waybills) {
            if (waybill != null && monitorJudgeBizService.needSyncComplete(waybill.getBillProxyType(), waybill.getCanOperate(), waybill.getWaybillId())) {
                //需要做同步
                CompleteWaybillDTO relationCompleteWaybillDTO = new CompleteWaybillDTO();
                BeanUtils.copyProperties(completeWaybillDTO, relationCompleteWaybillDTO);
                String relationWaybillId = waybillMapBizService.findRelationWaybillId(waybill.getWaybillId(), waybill.getBillProxyType());
                relationCompleteWaybillDTO.setWaybillId(relationWaybillId);
                proxySyncRecordBizService.syncCompleteWaybill(waybill.getWaybillId(), relationCompleteWaybillDTO);
            }
        }
    }

    private void finishTakeInfo(CompleteWaybillResultDO completeWaybillResultDO) {
        if (completeWaybillResultDO.getUpdatePickingResultDO().getOrderNotifyFlag()) {
            log.info("通知交易发货单完成:" + JSON.toJSONString(completeWaybillResultDO.getUpdatePickingResultDO().getFinishTakeInfoDTO()));
            ItemResult<Boolean> result = takeUpDataService.doLogisticFinishTakeInfo(
                    completeWaybillResultDO.getUpdatePickingResultDO().getFinishTakeInfoDTO());
            if (result == null || !result.isSuccess()) {
                throw new BizException(LogisticsErrorCode.NOTIFY_TRADE_ERROR, FINISH_TAKE_INFO_DTO
                        + completeWaybillResultDO.getUpdatePickingResultDO().getFinishTakeInfoDTO());
            }
        }
    }

    private void notifyByMq(Waybill waybill, Boolean isSign, CompleteWaybillResultDO completeWaybillResultDO) {
        if (!isSign) {
            // 发送消息
            smsMessageBizService.sendWaybillDriverCompleted(waybill.getWaybillId());
            smsMessageBizService.sendWaybillCarrierCompleted(waybill.getWaybillId());
            // 通知trace 子运单完成监控
        }
        //混凝土通知签收同步通知交易
        if (completeWaybillResultDO.getNeedNotifyCompleteConcrete()) {
            waybillSignBizService.notifyTradeSignQuantity(waybill);
            //在消息队列中获取台班费,异步通知erp,失败保存到异常列表中
            messageQueueService.sendMQ(waybill.getWaybillId(), LogisticsMessageTypeEnum.NOTIFY_ERP_COMPLETE_CONCRETE.getCode());
        }
    }

    @Override
    public ItemResult<Void> changeCarriage(ChangeCarriageDTO changeCarriageDTO) {
        /**
         *
         * 功能描述: 仅更新发布运费
         *
         * @param: [changeCarriageDTO]
         * @return: void
         */
        waybillOperationBizService.changeCarriage(changeCarriageDTO);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> changeStatusToWaitReceiveById(String  waybillId) {
        /**
         *
         * 功能描述: 根据运单ID更新运单状态
         *
         * @param: [changeStatusDTO]
         * @return: void
         */
        waybillOperationBizService.changeStatusToWaitReceiveById(waybillId);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> changeStatusToCanceledById(String waybillId) {
        waybillOperationBizService.changeStatusToCanceledById(waybillId);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> changeCancelReason(CancelReasonDTO cancelReasonDTO) {
        /**
         *
         * 功能描述: 更新运单信息表中的运单取消原因
         *
         * @param: [cancelReasonDTO]
         * @return: void
         */
        waybillOperationBizService.changeCancelReason(cancelReasonDTO);
        //添加操作记录
        OperationRecordAddDTO operationRecordAddDTO = new OperationRecordAddDTO();
        operationRecordAddDTO.setEntryId(cancelReasonDTO.getWaybillId());
        operationRecordAddDTO.setType(OperationRecordTypeEnum.WAYBILL.getCode());
        operationRecordAddDTO.setOperationType(WaybillOperationTypeEnum.WAYBILL_CANCEL.getCode());
        operationRecordAddDTO.setOperatorId(cancelReasonDTO.getOperationUserId());
        operationRecordAddDTO.setOperatorName(cancelReasonDTO.getOperationUserName());
        operationRecordAddDTO.setContent("运单取消:" + cancelReasonDTO.getCancelReason());
        operationRecordBizService.saveOperationRecord(operationRecordAddDTO);

        return new ItemResult<>(null);
    }

	@Override
	public ItemResult<List<WaybillDTO>> queryWaybillsByDispatchIds(List<String> dispatchIds) {
		return new ItemResult<>(waybillQueryBizService.selectWaybillsByDispatchIds(dispatchIds));
	}

	@Override
	public ItemResult<List<WaybillDTO>> queryWaybillsByPickingBillIds(List<String> pickingBillIds) {
		return new ItemResult<>(waybillQueryBizService.selectWaybillsByPickingBillIds(pickingBillIds));
	}

	@Override
	public ItemResult<List<WaybillDTO>> queryWaybillsByWaybillIds(List<String> waybillIds) {
		return new ItemResult<>(waybillQueryBizService.selectWaybillsByWaybillIds(waybillIds));
	}
	
    @Override
    public ItemResult<Void> changeArriveDestinationTime(String waybillId) {
        /**
         *
         * 功能描述: 更新运单送达时间
         *
         * @param: [waybillId]
         * @return: void
         */
        waybillOperationBizService.changeArriveDestinationTime(waybillId);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> changeMonitorArriveTime(MonitorTimeDTO dto) {
        /**
         *
         * 功能描述: 更新监控送达时间
         *
         * @param: [waybillId]
         * @return: void
         */
        waybillOperationBizService.changeMonitorArriveTime(dto.getWaybillId(), dto.getDate());
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> changeBeginCarryTime(String waybillId) {
        /**
         *
         * 功能描述: 更新开始搬运时间
         *
         * @param: [waybillId]
         * @return: void
         */
        waybillOperationBizService.changeBeginCarryTime(waybillId);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> snatchWaybill(SnatchWaybillDTO snatchWaybillDTO) {
        log.info("开始抢单=====snatchWaybillDTO:{}",JSON.toJSONString(snatchWaybillDTO));
        String code = waybillOperationBizService.snatchWaybill(snatchWaybillDTO);
        Waybill waybill = waybillQueryBizService.selectWaybillById(snatchWaybillDTO.getWaybillId());
        if(waybill == null){
            log.info("未找到{}对应运单", snatchWaybillDTO.getWaybillId());
            throw new BizException(BasicCode.DATA_NOT_EXIST, snatchWaybillDTO.getWaybillId());
        }
        if (snatchWaybillDTO.getUserType().equals(UserRoleEnum.PERSONAL_DRIVER.getCode()) &&
        		code.equals(SnatchWaybillReturnEnum.SUCCESS.getCode())) {
            waybillMonitorPrepare(snatchWaybillDTO.getWaybillId(),UserRoleEnum.PERSONAL_DRIVER.getCode(), null);
            //同步创建外部运单
            WaybillMonitorPrepareDTO waybillMonitorPrepareDTO = waybillQueryBizService.selectWaybillMonitorPrepareParentData(snatchWaybillDTO.getWaybillId());
            if (ExternalSyncFlagEnum.FORWARD_SYNC.getCode().equals(waybill.getSyncFlag())) {
                ExternalWaybillRequestDTO externalWaybillRequestDTO = new ExternalWaybillRequestDTO();
                externalWaybillRequestDTO.setWaybillId(snatchWaybillDTO.getWaybillId());
                externalWaybillRequestDTO.setOperatorUserId(snatchWaybillDTO.getUserId());
                externalWaybillRequestDTO.setOperatorUserName(snatchWaybillDTO.getUserName());

                saveOrUpdateExternalWaybill(waybill, externalWaybillRequestDTO, waybillMonitorPrepareDTO);
            }
        }
        ItemResult<Void> result = new ItemResult<>();
        result.setCode(code);

        //查询出主运单关联的车辆和司机信息
        WaybillUserInfoDTO waybillUserInfoDTO = waybillQueryBizService.selectWaybillUserInfoById(waybill.getWaybillId());

        //是否为重新指派
        boolean isFirstAssign = true;
        String relationWaybillId = waybillMapBizService.findRelationWaybillId(waybill.getWaybillId(), waybill.getBillProxyType());
        if (monitorJudgeBizService.needSyncBill(waybill.getBillProxyType(), waybill.getCanOperate()) &&
                CsStringUtils.isNotBlank(relationWaybillId)) {
            isFirstAssign = false;
            SellerAssignWaybillDTO relationAssignWaybillDTO = new SellerAssignWaybillDTO();
            BeanUtils.copyProperties(waybillUserInfoDTO, relationAssignWaybillDTO);
            relationAssignWaybillDTO.setWaybillId(relationWaybillId);
            relationAssignWaybillDTO.setUserId(snatchWaybillDTO.getUserId());
            relationAssignWaybillDTO.setUserName(snatchWaybillDTO.getUserName());
            proxySyncRecordBizService.syncReassignmentWaybill(waybill.getWaybillId(), relationAssignWaybillDTO);
        }

        //当前运单是二级平台配送，且canOperate为1，需要同步到交易,并且创建一级发货单
        if (isFirstAssign && monitorJudgeBizService.needCreatePrimaryTakeInfo(waybill.getBillProxyType(), waybill.getCanOperate())) {
            billSyncBizService.syncPublishPrimaryWaybill(waybill);
        }

        //当前运单是一级平台配送，且canOperate为1，需要同步生成二级运单
        if (isFirstAssign && CsStringUtils.equals(waybill.getBillProxyType(), BillProxyTypeEnum.PRIMARY.getCode()) &&
                monitorJudgeBizService.needSyncBill(waybill.getBillProxyType(), waybill.getCanOperate())) {

            //根据一级提货单id去map表里查询出二级提货单
            List<PickingMap> pickingMaps = pickingMapBizService.findRelationPickingBillList(waybill.getPickingBillId(),BillProxyTypeEnum.PRIMARY.getCode());
            if(CollectionUtils.isEmpty(pickingMaps)){
                log.info("未找到{}对应提货单映射数据",waybill.getPickingBillId());
                throw new BizException(BasicCode.UNDEFINED_ERROR, "同步生成二级运单报错");
            }
            WaybillQuantityDO waybillQuantityDO = waybillQueryBizService.queryWaybillTotalQuantityByWaybillId(snatchWaybillDTO.getWaybillId());
            pickingMaps.forEach(PickingMap -> {
                AssignWaybillDTO assignWaybillDTO = new AssignWaybillDTO();
                assignWaybillDTO.setPickingBillId(PickingMap.getSecondaryPickingBillId());
                assignWaybillDTO.setOperatorUserId(snatchWaybillDTO.getUserId());
                assignWaybillDTO.setOperatorUserName(snatchWaybillDTO.getUserName());
                assignWaybillDTO.setSourceWaybillId(waybill.getWaybillId());
                assignWaybillDTO.setSourceWaybillNum(waybill.getWaybillNum());
                assignWaybillDTO.setSourceDeliverySheetNum(PickingMap.getPrimaryDeliverySheetNum());
                assignWaybillDTO.setCanOperate((byte)0);
                assignWaybillDTO.setBillProxyType(BillProxyTypeEnum.SECONDARY.getCode());

                AssignWaybillDetailDTO assignWaybillDetailDTO = new AssignWaybillDetailDTO();
                assignWaybillDetailDTO.setDriverId(waybillUserInfoDTO.getDriverId());
                assignWaybillDetailDTO.setDriverName(waybillUserInfoDTO.getDriverName());
                assignWaybillDetailDTO.setDriverPhone(waybillUserInfoDTO.getDriverPhone());
                assignWaybillDetailDTO.setVehicleId(waybillUserInfoDTO.getVehicleId());

                assignWaybillDetailDTO.setQuantity(waybillQuantityDO.getQuantity());
                assignWaybillDTO.setAssignWaybillDetailList(Collections.singletonList(assignWaybillDetailDTO));
                proxySyncRecordBizService.syncPublishSecondaryWaybill(assignWaybillDTO);
            });
        }

        SnatchWaybillReturnEnum snatchWaybillReturnEnum = SnatchWaybillReturnEnum.valueOfCode(code);
        result.setDescription(snatchWaybillReturnEnum != null ? snatchWaybillReturnEnum.getDesc() : "");
        result.setSuccess(Boolean.TRUE);
        return result;
    }

    private void saveOrUpdateExternalWaybill(Waybill waybill, ExternalWaybillRequestDTO externalWaybillRequestDTO, WaybillMonitorPrepareDTO waybillMonitorPrepareDTO) {
        if ("".equals(waybill.getExternalWaybillStatus())) {
            //同步创建外部运单
            waybillExternalService.createExternalWaybill(externalWaybillRequestDTO);
        } else {
            //修改车辆
            externalWaybillRequestDTO.setVehicleNum(waybillMonitorPrepareDTO.getVehicleNum());
            waybillExternalService.updateExternalWaybill(externalWaybillRequestDTO);
        }
    }

    /**
     * 运单监控准备
     * @param waybillId 监控准备的运单ID
     * @param userType 归属人类型(车辆ID为空时,根据此处判断归属人)
     * @param vehicleId 车辆ID
     */
    private void waybillMonitorPrepare(String waybillId, Integer userType, String vehicleId) {
        WaybillMonitorPrepareDTO waybillMonitorPrepareDTO = waybillQueryBizService
                .selectWaybillMonitorPrepareParentData(waybillId);
        //获取当前运输的车辆ID
        VehicleQueryConditionDTO vehicleQueryConditionDTO = new VehicleQueryConditionDTO();
        vehicleQueryConditionDTO.setVehicleNum(waybillMonitorPrepareDTO.getVehicleNum());
        vehicleQueryConditionDTO.setUserId(waybillMonitorPrepareDTO.getCarrierId());

        Vehicle vehicle = null;

        if (CsStringUtils.isNotBlank(vehicleId)) {
            vehicle = vehicleBizService.get(vehicleId);
        } else {
            vehicleQueryConditionDTO.setUserType(userType);
            vehicle = vehicleBizService.selectVehicleByCondition(vehicleQueryConditionDTO);
        }
        if (vehicle == null) {
            throw new BizException(BasicCode.DATA_NOT_EXIST, vehicleQueryConditionDTO);
        }
       /* if (CsStringUtils.isNotEmpty(waybillMonitorPrepareDTO.getPickingBillId())) {
            //位置中心监控数据准备

            //获取当前运单的准备数据

        } else {
            //位置中心监控数据准备

            //获取合并运单的所有主运单信息

        }*/
    }

    @Override
    public ItemResult<Void> removeWaybill(DriverCancelWaybillDTO dto) {
        waybillOperationBizService.removeWaybillAndRestoreDeliveryQuantity(dto);

        // 发送消息
        smsMessageBizService.sendWaybillDriverCancel(dto.getWaybillId());

        return new ItemResult<>(null);
    }

	@Override
	public ItemResult<Void> passCheck(PassCheckDTO dto) {
		waybillOperationBizService.passCheck(dto);
		return new ItemResult<>(null);
	}

    @Override
    public ItemResult<Void> batchPassCheck(BatchPassCheckDTO batchPassCheckDTO) {
        waybillOperationBizService.batchPassCheck(batchPassCheckDTO);
        return new ItemResult<>(null);
    }

    @Override
	public ItemResult<WaybillLocationDTO> getLocation(String waybillId) {
		return  new ItemResult<>(waybillQueryBizService.selectWaybillLocation(waybillId));
	}

	@Override
	public ItemResult<List<LocationDTO>> getWaybillLocationByCon(WaybillLocationQueryDTO dto) {
        if (CsStringUtils.isEmpty(dto.getWaybillId())) {
            throw new BizException(BasicCode.INVALID_PARAM, "运单ID=" + dto.getWaybillId());
        }
        return new ItemResult<>(waybillQueryBizService.queryWaybillLocationByCon(dto));
	}

	@Override
	public ItemResult<Void> cancelWaybillByPlatform(CancelWaybillDTO dto) {
		waybillOperationBizService.cancelWaybillByPlatform(dto);
		// sendMQ通知川东取消运单

        // 发送消息
        if (UserRoleEnum.CARRIER.getCode().equals(dto.getUserRole())) {
            smsMessageBizService.sendWaybillPlatformCancel(dto.getWaybillId());
        }
		return new ItemResult<>(null);
	}

	@Override
	public ItemResult<Void> republishWaybillByPlatform(RepublishWaybillDTO republish) {
		waybillOperationBizService.republishWaybillByPlatform(republish);
		// sendMQ通知川东重新发布运单(改为待接单)

		return new ItemResult<>(null);
	}
	
	@Override
	public ItemResult<Void> getCarriersByType(String carrierType){
		// 待从会员端获取
		return new ItemResult<>(null);
	}

	@Override
	public ItemResult<PageData<WarehouseAdminWaitDeliveryDTO>> getWarehouseAdminWaitDelivery(
			PageQuery<WarehouseAdminWaitDeliveryQueryDTO> pageQuery){
		return new ItemResult<>(waybillQueryBizService.queryWarehouseAdminWaitDelivery(pageQuery));
	}

	@Override
	public ItemResult<PageData<WarehouseAdminProccessedDTO>> getWarehouseAdminProccessed(
			PageQuery<WarehouseAdminProccessedQueryDTO> pageQuery) {
		return new ItemResult<>(waybillQueryBizService.queryWarehouseAdminProccessed(pageQuery));
	}

	@Override
	public ItemResult<PageData<PublishWaybillListDTO>> querySellerWaybillList( 
			PageQuery<PublishWaybillListQueryDTO> pageQuery) {
		return new ItemResult<>(waybillQueryBizService.selectBuyerSellerWaybillList(pageQuery));
	}

    @Override
    public ItemResult<PageData<PublishWaybillListDTO>> queryAppWaybillList(PageQuery<AppWaybillListQueryDTO> pageQuery) {
        return new ItemResult<>(waybillQueryBizService.selectAppWaybillList(pageQuery));
    }

    @Override
    public ItemResult<AppWaybillStatisticsDTO> statisticsAppWaybill(AppWaybillListQueryDTO queryDTO) {
        return new ItemResult<>(waybillQueryBizService.statisticsAppWaybill(queryDTO));
    }

    @Override
    public ItemResult<AppWaybillQueryConditionsMapDTO> selectAppWaybillQueryConditions(AppWaybillListQueryDTO queryDTO) {
        return new ItemResult<>(waybillQueryBizService.selectAppWaybillQueryConditions(queryDTO));
    }

    @Override
    public ItemResult<List<String>> queryWarehouseIdByBuyerId(String buyerId) {
        return new ItemResult<>(waybillQueryBizService.queryWarehouseIdByBuyerId(buyerId));
    }

    @Override
    @OperationLimit
    @Transactional(rollbackFor = Exception.class)
    public ItemResult<Void> reassignmentVehicle(ReassignmentVehicleDTO queryDTO) {
        log.info("ReassignmentVehicleDTO:{}",JSON.toJSONString(queryDTO));
        Waybill waybill = waybillQueryBizService.selectWaybillById(queryDTO.getWaybillId());
        if (waybill == null) {
            throw new BizException(BasicCode.DATA_NOT_EXIST, "找不到对应的运单数据！");
        }
        if (!monitorJudgeBizService.canChangeStatus(waybill.getBillProxyType(), waybill.getWaybillId(), waybill.getStatus(), waybill.getCanOperate())) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "关联的背靠背运单当前状态不可取消!");
        }
        if (!CsStringUtils.equals(waybill.getBillProxyType(), BillProxyTypeEnum.PRIMARY.getCode()) && !CsStringUtils.equals(waybill.getStatus(), WaybillStatusEnum.WAIT_DELIVERY.getCode())) {
            log.info("非待配送运单不能重新指派");
            throw new BizException(BasicCode.UNDEFINED_ERROR, "非待配送运单不能重新指派");
        }
        String vehicleId = queryDTO.getVehicleId();
        if(LogisticsUtils.isSame(waybill.getCanOperate(),(byte)1) ){
            //更新司机和车辆
            AssignDriverLogAddDTO assignDriverLogAddDTO = new AssignDriverLogAddDTO();
            BeanUtils.copyProperties(queryDTO,assignDriverLogAddDTO);
            Vehicle vehicle = assignDriverLogBizService.updateSelfAssignVehicleInfo(assignDriverLogAddDTO);
            vehicleId = vehicle.getVehicleId();
            assignDriverLogBizService.updateSelfAssignVehicleInfo(assignDriverLogAddDTO);
        }

        //更新lgs_waybill_info的车辆和司机信息
        SellerAssignWaybillDTO sellerAssignWaybillDTO = new SellerAssignWaybillDTO();
        BeanUtils.copyProperties(queryDTO, sellerAssignWaybillDTO);
        sellerAssignWaybillDTO.setVehicleNum(queryDTO.getNumber());
        //操作人Id和操作人名称是账号的
        if (CsStringUtils.isNotBlank(queryDTO.getAccountId())) {
            sellerAssignWaybillDTO.setUserId(queryDTO.getAccountId());
            sellerAssignWaybillDTO.setUserName(queryDTO.getAccountName());
        }
        waybillOperationBizService.assignWaybill(sellerAssignWaybillDTO);
        //同步外部系统
        this.updateExternalWaybill(sellerAssignWaybillDTO);

        SelfPickingVehicleDTO selfPickingVehicleDTO = new SelfPickingVehicleDTO();
        BeanUtils.copyProperties(queryDTO,selfPickingVehicleDTO);
        Boolean canMonitor = monitorJudgeBizService.canMonitor(selfPickingVehicleDTO);
        //背靠背运单 自提-自提，二级运单重新指派时同步一级运单
        if (CsStringUtils.equals(waybill.getBillProxyType(), BillProxyTypeEnum.SECONDARY.getCode())) {
            ReassignmentVehicleDTO reassignmentVehicleDTO = new ReassignmentVehicleDTO();
            String relationWaybillId = waybillMapBizService.findRelationWaybillId(waybill.getWaybillId(), waybill.getBillProxyType());
            BeanUtils.copyProperties(queryDTO,reassignmentVehicleDTO);
            reassignmentVehicleDTO.setWaybillId(relationWaybillId);
            proxySyncRecordBizService.syncReassignmentVehicle(queryDTO.getWaybillId(),reassignmentVehicleDTO);
        }
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> discardWaybill(DiscardWaybillDTO discardWaybillDTO) {
        waybillOperationBizService.discardWaybill(discardWaybillDTO);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> uploadCertificate(UploadCertificateDTO uploadCertificateDTO) {
        //先保存附件再更新运单是否上传支付凭证字段
        AttListAddDTO attListAddDTO = uploadCertificateDTO.getAttListAddDTO();
        if(attListAddDTO != null && CollectionUtils.isNotEmpty(attListAddDTO.getAttAddListDTO())){
            attListAddDTO.getAttAddListDTO().forEach(item ->{
                item.setCreateUser(uploadCertificateDTO.getOperationUserId());
            });
            attachmentBizService.saveAttList(uploadCertificateDTO.getAttListAddDTO());
        }

        Waybill waybill = new Waybill();
        waybill.setWaybillId(uploadCertificateDTO.getWaybillId());
        waybill.setDeliveryCertificateFlag((byte) 1);
        waybillQueryBizService.updateWaybill(waybill);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<List<AttListDTO>> findWaybillCertificate(String waybillId) {
        AttListQueryDTO attListQueryDTO = new AttListQueryDTO();
        attListQueryDTO.setEntryId(waybillId);
        attListQueryDTO.setType(AttachmentTypeEnum.DELIVERY_CERTIFICATE.getCode());
   /*   List<AttListDTO> attListDTOS = attachmentBizService.getAttList(attListQueryDTO);
       List<AttachmentinfoDTO> attachmentinfoDTOS = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(attListDTOS)){
            attachmentinfoDTOS = attachmentService.getAttachmentByBID(attListDTOS.get(0).getBid());
        }*/
        return new ItemResult<>(attachmentBizService.getAttList(attListQueryDTO));
    }

    @Override
    public ItemResult<CarryWaybillSubmitDTO> selectCarryWaybill(String waybillNum) {
        return new ItemResult<>(waybillQueryBizService.selectCarryWaybill(waybillNum));
    }

    @Override
	public ItemResult<Void> assignWaybillBySeller(SellerAssignWaybillDTO dto) {
		waybillOperationBizService.assignWaybill(dto);
        //同步外部系统
        updateExternalWaybill(dto);
        //监控准备
        this.waybillMonitorPrepare(dto.getWaybillId(), UserRoleEnum.SELLER.getCode(), dto.getVehicleId());
        //同步到另一级运单
        if (CsStringUtils.isBlank(dto.getVehicleId())) {
            WaybillUserInfoDTO waybillUserInfoDTO = waybillQueryBizService.selectWaybillUserInfoById(dto.getWaybillId());
            if (waybillUserInfoDTO != null &&
                    monitorJudgeBizService.needSyncBill(waybillUserInfoDTO.getBillProxyType(), waybillUserInfoDTO.getCanOperate())) {
                //来自web的重新指派,且需要同步到另一级运单的重新指派
                SellerAssignWaybillDTO reassignmentVehicleDTO = new SellerAssignWaybillDTO();
                BeanUtils.copyProperties(dto, reassignmentVehicleDTO);
                String relationWaybillId = waybillMapBizService.findRelationWaybillId(dto.getWaybillId(), waybillUserInfoDTO.getBillProxyType());
                reassignmentVehicleDTO.setWaybillId(relationWaybillId);
                proxySyncRecordBizService.syncReassignmentWaybill(dto.getWaybillId(), reassignmentVehicleDTO);

            }
        }
        return new ItemResult<>(null);
	}

	@Override
	public ItemResult<List<DistrictListDTO>> selectSeckillDistrictsByUserId(String userId) {
		List<DistrictListDTO> dtos = waybillQueryBizService.selectSeckillDistrictsByUserId(userId);
		return new ItemResult<>(dtos);
	}

	@Override
	public ItemResult<PageData<UserSeckillWaybillListDTO>> selectUserSeckillWaybillList(
			PageQuery<UserSeckillWaybillQueryDTO> pageQuery) {
		PageData<UserSeckillWaybillListDTO> dtos = waybillQueryBizService.selectUserSeckillWaybillList(pageQuery);
		return new ItemResult<>(dtos);
	}

	@Override
	public ItemResult<PageData<UserSeckillWaybillListDTO>> selectUserSeckilledWaybillList(
			PageQuery<DriverWaybillListQueryDTO> pageQuery) {
		PageData<UserSeckillWaybillListDTO> dtos = waybillQueryBizService.selectUserSeckilledWaybillList(pageQuery);
		return new ItemResult<>(dtos);
	}
    @Override
    public ItemResult<List<TradeWaybillDTO>> queryWaybillListByDeliveryNum(String deliverySheetNum) {
        return new ItemResult<>(waybillQueryBizService.queryWaybillListByDeliveryNum(deliverySheetNum));
    }

	@Override
	public ItemResult<Void> confirmWaybillByDriver(DriverConfirmDTO driverConfirmDTO) {
        List<WaybillDTO> waybillDTOList = waybillQueryBizService.selectWaybillsByWaybillIds(Collections.singletonList(driverConfirmDTO.getWaybillId()));
        if (CollectionUtils.isEmpty(waybillDTOList)) {
            throw new BizException(BasicCode.DATA_NOT_EXIST, "运单");
        }
        WaybillDTO waybillDTO = waybillDTOList.get(0);
        verifyWaybill(driverConfirmDTO, waybillDTO);

        //获取当前运单来源
        QueryWaybillOperationDTO queryWaybillOperationDTO = new QueryWaybillOperationDTO();
        queryWaybillOperationDTO.setWaybillId(driverConfirmDTO.getWaybillId());
        queryWaybillOperationDTO.setOperationType(WaybillOperationTypeEnum.WAYBILL_AUDIT.getCode());
        List<OperationRecordDTO> operationRecordList = operationRecordBizService.queryWaybillOperation(queryWaybillOperationDTO);
        //调度单指派
        ConfirmWaybillDO confirmWaybillDO = new ConfirmWaybillDO();
        confirmWaybillDO.setWaybillId(driverConfirmDTO.getWaybillId());
        confirmWaybillDO.setUserId(driverConfirmDTO.getOperationUserId());
        confirmWaybillDO.setStatus(WaybillStatusEnum.WAIT_DELIVERY.getCode());
        List<WaybillMonitorPrepareDTO> waybillMonitorPrepareList = waybillQueryBizService
                .selectWaybillMonitorPrepareForMergeData(driverConfirmDTO.getWaybillId());
        WaybillMonitorPrepareDTO waybillMonitorPrepareDTO = waybillMonitorPrepareList.get(0);
        //获取当前运输的车辆ID
        VehicleQueryConditionDTO vehicleQueryConditionDTO = new VehicleQueryConditionDTO();
        vehicleQueryConditionDTO.setVehicleNum(waybillMonitorPrepareDTO.getVehicleNum());
        vehicleQueryConditionDTO.setUserId(waybillMonitorPrepareDTO.getCarrierId());
        vehicleQueryConditionDTO.setUserType(UserRoleEnum.CARRIER.getCode());
        Vehicle vehicle = vehicleBizService.selectVehicleByCondition(vehicleQueryConditionDTO);
        if (vehicle == null) {
            throw new BizException(BasicCode.DATA_NOT_EXIST, vehicleQueryConditionDTO);
        }
        if (CollectionUtils.isEmpty(operationRecordList)) {
            //发送监控准备信息

        } else {
            List<WaybillDTO> waybillList = waybillQueryBizService.selectWaybillsByWaybillIds(Collections.singletonList(driverConfirmDTO.getWaybillId()));
            if (CollectionUtils.isEmpty(waybillList)) {
                throw new BizException(BasicCode.DATA_EXIST, "waybillId=" + driverConfirmDTO.getWaybillId());
            }
            confirmWaybillDO.setEstimateKm(waybillList.get(0).getEstimateKm());
            confirmWaybillDO.setEstimateDuration(waybillList.get(0).getEstimateDuration());
            //监控准备
            waybillMonitorPrepare(driverConfirmDTO.getWaybillId(),UserRoleEnum.CARRIER.getCode(), null);
        }
        //更新运单状态
        waybillOperationBizService.confirmWaybillByDriver(confirmWaybillDO);
        //添加操作记录
        OperationRecordAddDTO operationRecordAddDTO = new OperationRecordAddDTO();
        operationRecordAddDTO.setEntryId(driverConfirmDTO.getWaybillId());
        operationRecordAddDTO.setType(OperationRecordTypeEnum.WAYBILL.getCode());
        operationRecordAddDTO.setOperationType(WaybillOperationTypeEnum.WAYBILL_CONFIRM.getCode());
        operationRecordAddDTO.setOperatorId(driverConfirmDTO.getOperationUserId());
        operationRecordAddDTO.setOperatorName(driverConfirmDTO.getOperationUserName());
        operationRecordAddDTO.setContent("司机确认");
        operationRecordBizService.saveOperationRecord(operationRecordAddDTO);
        //司机确认发送短信给承运商
        smsMessageBizService.sendWaybillConfirm(driverConfirmDTO.getWaybillId());
        /*
        DeliveryWaybillSMSDTO deliveryWaybillSMSDTO = new DeliveryWaybillSMSDTO();
        deliveryWaybillSMSDTO.setNotifyAccountIdList(Lists.newArrayList(vehicle.getUserId()));
        deliveryWaybillSMSDTO.setMatchSuccessTime(new Date());
        deliveryWaybillSMSDTO.setDriverName(vehicle.getDriverName());
        deliveryWaybillSMSDTO.setPlateNumber(vehicle.getNumber());
        deliveryWaybillSMSDTO.setDriverPhone(vehicle.getDriverPhone());
        deliveryWaybillSMSDTO.setWaybillNumber(traceWaybillPublishDTO.getWaybillNum());
        smsMessageBizService.deliveryWaybillSMS(deliveryWaybillSMSDTO);
        */
        //同步外部系统
        Waybill waybill = waybillQueryBizService.selectWaybillById(confirmWaybillDO.getWaybillId());
        if (waybill.getSyncFlag().equals(ExternalSyncFlagEnum.FORWARD_SYNC.getCode())) {
            ExternalWaybillRequestDTO externalWaybillRequestDTO = new ExternalWaybillRequestDTO();
            externalWaybillRequestDTO.setWaybillId(driverConfirmDTO.getWaybillId());
            externalWaybillRequestDTO.setOperatorUserId(driverConfirmDTO.getOperationUserId());
            externalWaybillRequestDTO.setOperatorUserName(driverConfirmDTO.getOperationUserName());
            saveOrUpdateExternalWaybill(waybill, externalWaybillRequestDTO, waybillMonitorPrepareDTO);
        }

        //是否为重新指派
        boolean isFirstAssign = true;
        String relationWaybillId = waybillMapBizService.findRelationWaybillId(waybill.getWaybillId(), waybill.getBillProxyType());
        if (monitorJudgeBizService.needSyncBill(waybill.getBillProxyType(), waybill.getCanOperate()) &&
                CsStringUtils.isNotBlank(relationWaybillId)) {
            isFirstAssign = false;
            SellerAssignWaybillDTO relationAssignWaybillDTO = new SellerAssignWaybillDTO();
            relationAssignWaybillDTO.setWaybillId(relationWaybillId);
            relationAssignWaybillDTO.setUserId(driverConfirmDTO.getOperationUserId());
            relationAssignWaybillDTO.setUserName(driverConfirmDTO.getOperationUserName());
            relationAssignWaybillDTO.setVehicleNum(vehicle.getNumber());
            relationAssignWaybillDTO.setVehicleId(vehicle.getVehicleId());
            relationAssignWaybillDTO.setDriverId(waybillDTO.getDriverId());
            relationAssignWaybillDTO.setDriverName(waybillDTO.getDriverName());
            relationAssignWaybillDTO.setDriverPhone(waybillDTO.getDriverPhone());
            proxySyncRecordBizService.syncReassignmentWaybill(waybill.getWaybillId(), relationAssignWaybillDTO);
        }

        //当前运单是二级平台配送，且canOperate为1，需要同步到交易,并且创建一级发货单
        if (isFirstAssign && monitorJudgeBizService.needCreatePrimaryTakeInfo(waybill.getBillProxyType(), waybill.getCanOperate())) {
            billSyncBizService.syncPublishPrimaryWaybill(waybill);
        }

        //当前运单是一级平台配送，且canOperate为1，需要同步生成二级运单
        if (isFirstAssign && CsStringUtils.equals(waybill.getBillProxyType(), BillProxyTypeEnum.PRIMARY.getCode()) &&
                monitorJudgeBizService.needSyncBill(waybill.getBillProxyType(),waybill.getCanOperate())){
            //根据一级提货单id去map表里查询出二级提货单
            List<PickingMap> pickingMaps = pickingMapBizService.findRelationPickingBillList(waybill.getPickingBillId(),BillProxyTypeEnum.PRIMARY.getCode());
            if(CollectionUtils.isEmpty(pickingMaps)){
                log.info("未找到{}对应提货单映射数据",waybill.getPickingBillId());
                throw new BizException(BasicCode.UNDEFINED_ERROR, "同步生成二级运单报错");
            }
            pickingMaps.forEach(PickingMap -> {
                AssignWaybillDTO assignWaybillDTO = new AssignWaybillDTO();
                assignWaybillDTO.setPickingBillId(PickingMap.getSecondaryPickingBillId());
                assignWaybillDTO.setOperatorUserId(driverConfirmDTO.getOperationUserId());
                assignWaybillDTO.setOperatorUserName(driverConfirmDTO.getOperationUserName());
                assignWaybillDTO.setSourceWaybillId(waybill.getWaybillId());
                assignWaybillDTO.setSourceWaybillNum(waybill.getWaybillNum());
                assignWaybillDTO.setSourceDeliverySheetNum(PickingMap.getPrimaryDeliverySheetNum());
                assignWaybillDTO.setCanOperate((byte)0);
                assignWaybillDTO.setBillProxyType(BillProxyTypeEnum.SECONDARY.getCode());

                AssignWaybillDetailDTO assignWaybillDetailDTO = new AssignWaybillDetailDTO();
                assignWaybillDetailDTO.setDriverId(waybillDTO.getDriverId());
                assignWaybillDetailDTO.setDriverName(waybillDTO.getDriverName());
                assignWaybillDetailDTO.setDriverPhone(waybillDTO.getDriverPhone());
                assignWaybillDetailDTO.setQuantity(waybillDTO.getQuantity());
                assignWaybillDetailDTO.setVehicleId(vehicle.getVehicleId());
                assignWaybillDTO.setAssignWaybillDetailList(Collections.singletonList(assignWaybillDetailDTO));
                proxySyncRecordBizService.syncPublishSecondaryWaybill(assignWaybillDTO);
            });
        }

        return new ItemResult<>(null);
	}

    private static void verifyWaybill(DriverConfirmDTO driverConfirmDTO, WaybillDTO waybillDTO) {
        if (!waybillDTO.getStatus().equals(WaybillStatusEnum.WAIT_CONFIRM.getCode())) {
            throw new BizException(LogisticsErrorCode.CONFIRM_WAYBILL, "当前状态不允许确认操作");
        }
        String driverId;
        //个人司机确认
        if (waybillDTO.getCarrierId().equals(waybillDTO.getDriverId())) {
            driverId = driverConfirmDTO.getUserId();
        } else {
            driverId = driverConfirmDTO.getOperationUserId();
        }
        if (!driverId.equals(waybillDTO.getDriverId())) {
            throw new BizException(LogisticsErrorCode.CONFIRM_WAYBILL, "您没有操作权限");
        }
    }

    @Override
	public ItemResult<PageData<CarrierWaybillListDTO>> queryCarrierWaybillList(
			PageQuery<CarrierWaybillListQueryDTO> arg0) {
		PageData<CarrierWaybillListDTO> dto = waybillQueryBizService.queryCarrierWaybillList(arg0);
		return new ItemResult<>(dto);
	}

	@Override
	public ItemResult<Void> carrierAssignWaybillToDriver(DriverWaybillAssignDTO dto) {
		waybillOperationBizService.carrierAssignWaybillToDriver(dto);
		// sendMQ通知川东运单改为待确认
		return new ItemResult<>(null);
	}

    @Override
    public ItemResult<Void> waybillMonitorStatusNotify(WaybillMonitorStatusNotifyDTO waybillMonitorStatusNotifyDTO) {
        waybillOperationBizService.waybillMonitorStatusNotify(waybillMonitorStatusNotifyDTO);
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> closeWaybill(CloseWaybillDTO closeWaybillDTO) {
        log.info("关闭运单:{}", closeWaybillDTO);
        Waybill oldWaybill = null;
        if (CsStringUtils.isBlank(closeWaybillDTO.getWaybillId())) {
            if (CsStringUtils.isBlank(closeWaybillDTO.getWaybillNum())) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "请输入运单ID或运单号");
            }
            oldWaybill = waybillQueryBizService.selectWaybillByWaybillNum(closeWaybillDTO.getWaybillNum());
            closeWaybillDTO.setWaybillId(oldWaybill.getWaybillId());
        } else {
            oldWaybill = waybillQueryBizService.selectWaybillById(closeWaybillDTO.getWaybillId());
        }

        if (CsStringUtils.equals(oldWaybill.getStatus(), WaybillStatusEnum.CLOSED.getCode())) {
            log.info("运单已被关闭,请勿重复操作:{}", oldWaybill);
            return new ItemResult<>(null);
        }

        Boolean mainCloseFlag = waybillOperationBizService.closeWaybill(closeWaybillDTO);
        Waybill waybill = waybillQueryBizService.selectWaybillById(closeWaybillDTO.getWaybillId());
        //运单关闭通知
        //子运单关闭触发主运单关闭
        if (waybill.getIsMainWaybill() == 0 && mainCloseFlag) {
            //关闭主运单监控通知
        }
        //同步关闭外部运单
        if (waybill.getSyncFlag().equals(ExternalSyncFlagEnum.FORWARD_SYNC.getCode()) ||
            AdjustAddWayEnum.SELLER_SIGIN.getCode().equals(closeWaybillDTO.getSignType())) {
            ExternalWaybillRequestDTO externalWaybillRequestDTO = new ExternalWaybillRequestDTO();
            externalWaybillRequestDTO.setWaybillId(closeWaybillDTO.getWaybillId());
            externalWaybillRequestDTO.setOperatorUserId(closeWaybillDTO.getOperationUserId());
            externalWaybillRequestDTO.setOperatorUserName(closeWaybillDTO.getOperationUserName());
            waybillExternalService.closeExternalWaybill(externalWaybillRequestDTO);
        }

        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<TradeWaybillDetailDTO> queryTradeWaybillDetail(String waybillId) {
        TradeWaybillDetailDO tradeWaybillDetailDO = waybillQueryBizService.queryTradeWaybillDetail(waybillId);


        if (tradeWaybillDetailDO == null) {
            return null;
        }
        TradeWaybillDetailDTO tradeWaybillDetailDTO = new TradeWaybillDetailDTO();
        BeanUtils.copyProperties(tradeWaybillDetailDO, tradeWaybillDetailDTO);

        String trueWaybillId = waybillId;
        if (CsStringUtils.equals(tradeWaybillDetailDO.getBillProxyType(), BillProxyTypeEnum.SECONDARY.getCode())) {
            PrimaryWaybillErpInfo primaryERP = waybillMapBizService.findPrimaryERPBySecondaryWaybillId(waybillId);
            if (primaryERP != null) {
                trueWaybillId = primaryERP.getPrimaryWaybillId();
                tradeWaybillDetailDTO.setExternalWaybillNum(primaryERP.getExternalWaybillNum());
                tradeWaybillDetailDO.setExternalWaybillStatus(primaryERP.getExternalWaybillStatus());
                tradeWaybillDetailDO.setSyncFailReason(primaryERP.getSyncFailReason());
            }
        }

        tradeWaybillDetailDTO.setCreateTime(DateUtil.format(tradeWaybillDetailDO.getCreateTime(), YYYYMMDDHHMMSS_FORMAT));
        if (CsStringUtils.isEmpty(tradeWaybillDetailDO.getWarehouseId())) {
            throw new BizException(BasicCode.DATA_NOT_EXIST, "warehouseId=null");
        }
        //获取当前运单提货点信息
        WarehouseDetailsDTO warehouseDetailsDTO = warehouseService.queryWarehouseDetails(tradeWaybillDetailDO.getWarehouseId());
        tradeWaybillDetailDTO.setWarehouseName(warehouseDetailsDTO.getName());
        tradeWaybillDetailDTO.setWarehouseAddress(warehouseDetailsDTO.getProvince() + warehouseDetailsDTO.getCity()
                + warehouseDetailsDTO.getDistrict() + warehouseDetailsDTO.getAddress());
        tradeWaybillDetailDTO.setWarehouseAddressLocation(warehouseDetailsDTO.getLocation());
        tradeWaybillDetailDTO.setWarehousePhone(warehouseDetailsDTO.getAdministratorPhone());

        //获取运单实际签收量,实际出厂量,计划发货量
        WaybillQuantityDO waybillQuantityDO = waybillQueryBizService.queryWaybillTotalQuantityByWaybillId(waybillId);

        //获取运单操作记录
        QueryWaybillOperationDTO queryWaybillOperationDTO = new QueryWaybillOperationDTO();
        queryWaybillOperationDTO.setWaybillId(trueWaybillId);
        List<OperationRecordDTO> operationRecordList = operationRecordBizService.queryWaybillOperation(queryWaybillOperationDTO);
        List<OperationRecordDTO> displayOperationRecordList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(operationRecordList)) {
            //获取操作映射
            Map<String, List<Integer>> operationStatusMap = getOperationStatusMap(tradeWaybillDetailDO);

            WaybillStatusEnum waybillStatusEnum = WaybillStatusEnum.valueOfCode(tradeWaybillDetailDO.getStatus());
            if (waybillStatusEnum == null) {
                throw new BizException(BasicCode.INVALID_PARAM, "无效的运单状态:status=" + tradeWaybillDetailDO.getStatus());
            }
            Map<String, OperationRecordDTO> operationRecordMap = new HashMap<>();

            handleOperationRecordMap(waybillStatusEnum, operationRecordList, operationRecordMap, operationStatusMap);

            for (Map.Entry<String, OperationRecordDTO> entry : operationRecordMap.entrySet()) {
                handleDisplayOperationRecord(entry, tradeWaybillDetailDO, waybillQuantityDO, displayOperationRecordList);
            }
        }
        for (OperationRecordDTO operationRecordDTO : operationRecordList) {
            handleDisplayOperationRecordByOperationRecordList(operationRecordDTO, displayOperationRecordList);
        }

        displayOperationRecordList.sort(
                (o1, o2) -> {
                    if ((o2.getOperationTime().compareTo(o1.getOperationTime()) == 0)) {
                        return o2.getCreateTime().compareTo(o1.getCreateTime());
                    } else {
                        return o2.getOperationTime().compareTo(o1.getOperationTime());
                    }
                }
        );

        tradeWaybillDetailDTO.setOperationRecordList(displayOperationRecordList);
        return new ItemResult<>(tradeWaybillDetailDTO);
    }

    private static void handleDisplayOperationRecordByOperationRecordList(OperationRecordDTO operationRecordDTO, List<OperationRecordDTO> displayOperationRecordList) {
        if (LogisticsUtils.isSame(operationRecordDTO.getOperationType(), WaybillOperationTypeEnum.VEHICLE_ENTER.getCode())) {
            operationRecordDTO.setStatusDesc("已开单");
            operationRecordDTO.setContent("您的车辆已开单");
            displayOperationRecordList.add(operationRecordDTO);
        }
        if (LogisticsUtils.isSame(operationRecordDTO.getOperationType(), WaybillOperationTypeEnum.PASS_TARE.getCode())) {
            operationRecordDTO.setStatusDesc("过皮重");
            operationRecordDTO.setContent("您的车辆已过皮重");
            displayOperationRecordList.add(operationRecordDTO);
        }
        if (LogisticsUtils.isSame(operationRecordDTO.getOperationType(), WaybillOperationTypeEnum.PASS_ROUGH.getCode())) {
            operationRecordDTO.setStatusDesc("过毛重");
            operationRecordDTO.setContent("您的车辆已过毛重");
            displayOperationRecordList.add(operationRecordDTO);
        }
        if (LogisticsUtils.isSame(operationRecordDTO.getOperationType(), WaybillOperationTypeEnum.VEHICLE_LEAVE.getCode())
                && CsStringUtils.contains(operationRecordDTO.getContent(), "-->出厂量")) {
            operationRecordDTO.setStatusDesc("已出站");
            operationRecordDTO.setContent("您的车辆已出站");
            displayOperationRecordList.add(operationRecordDTO);
        }
    }

    private static Map<String, List<Integer>> getOperationStatusMap(TradeWaybillDetailDO tradeWaybillDetailDO) {
        Map<String, List<Integer>> operationStatusMap = new HashMap<>();
        if (WaybillTypeEnum.SELLER_DELIVERY.getCode().equals(tradeWaybillDetailDO.getType())) {
            operationStatusMap.put("待配送", Lists.newArrayList(WaybillOperationTypeEnum.WAYBILL_NEW.getCode()));
            operationStatusMap.put("配送中", Lists.newArrayList(WaybillOperationTypeEnum.VEHICLE_LEAVE.getCode()));
            operationStatusMap.put("已签收", Lists.newArrayList(WaybillOperationTypeEnum.WAYBILL_COMPLETE.getCode()));
            operationStatusMap.put("已关闭", Lists.newArrayList(WaybillOperationTypeEnum.WAYBILL_CLOSED.getCode()));
        } else if (WaybillTypeEnum.BUYER_PICK.getCode().equals(tradeWaybillDetailDO.getType())) {
            operationStatusMap.put("待配送", Lists.newArrayList(WaybillOperationTypeEnum.WAYBILL_NEW.getCode()));
            operationStatusMap.put("已签收", Lists.newArrayList(WaybillOperationTypeEnum.VEHICLE_LEAVE.getCode()));
            operationStatusMap.put("已关闭", Lists.newArrayList(WaybillOperationTypeEnum.WAYBILL_CLOSED.getCode()));
        } else if (WaybillTypeEnum.SOCIETY_SNATCH.getCode().equals(tradeWaybillDetailDO.getType()) ||
                WaybillTypeEnum.CARRIER_ASSIGNING.getCode().equals(tradeWaybillDetailDO.getType())) {
            operationStatusMap.put("待配送", Lists.newArrayList(WaybillOperationTypeEnum.WAYBILL_SNATCH.getCode(),
                    WaybillOperationTypeEnum.WAYBILL_CONFIRM.getCode()));
            operationStatusMap.put("配送中", Lists.newArrayList(WaybillOperationTypeEnum.VEHICLE_LEAVE.getCode()));
            operationStatusMap.put("已签收", Lists.newArrayList(WaybillOperationTypeEnum.WAYBILL_COMPLETE.getCode()));
            operationStatusMap.put("已关闭", Lists.newArrayList(WaybillOperationTypeEnum.WAYBILL_CLOSED.getCode()));
        } else {
            operationStatusMap.put("待配送", Lists.newArrayList(WaybillOperationTypeEnum.WAYBILL_NEW.getCode()));
            operationStatusMap.put("已签收", Lists.newArrayList(WaybillOperationTypeEnum.WAYBILL_COMPLETE.getCode()));
            operationStatusMap.put("已关闭", Lists.newArrayList(WaybillOperationTypeEnum.WAYBILL_CLOSED.getCode()));
        }
        return operationStatusMap;
    }

    private static void handleDisplayOperationRecord(Map.Entry<String, OperationRecordDTO> entry, TradeWaybillDetailDO tradeWaybillDetailDO, WaybillQuantityDO waybillQuantityDO, List<OperationRecordDTO> displayOperationRecordList) {
        if (entry.getKey().equals("待配送") && entry.getValue() != null) {
            OperationRecordDTO operationRecordDTO = entry.getValue();
            operationRecordDTO.setStatusDesc("待配送");
            List<String> contentList = handleContentList(tradeWaybillDetailDO);
            operationRecordDTO.setContent("已为您安排配送[" + CsStringUtils.join(contentList, ",") + "]  计划发货量:" + waybillQuantityDO.getQuantity() + "吨");
            displayOperationRecordList.add(operationRecordDTO);
        }
        if (entry.getKey().equals("配送中") && entry.getValue() != null) {
            OperationRecordDTO operationRecordDTO = entry.getValue();
            operationRecordDTO.setStatusDesc("配送中");
            operationRecordDTO.setContent("车辆已从出货点出站，正在快马加鞭的送到您的手中  实际出厂量:"+ waybillQuantityDO.getActualQuantity() + "吨");
            displayOperationRecordList.add(operationRecordDTO);
        }
        if (entry.getKey().equals("已签收") && entry.getValue() != null) {
            OperationRecordDTO operationRecordDTO = entry.getValue();
            operationRecordDTO.setOperationType(WaybillOperationTypeEnum.WAYBILL_COMPLETE.getCode());
            operationRecordDTO.setStatusDesc("已签收");
            BigDecimal signQuantity = LogisticsUtils.isGtZero(waybillQuantityDO.getSignQuantity()) ? waybillQuantityDO.getSignQuantity() : waybillQuantityDO.getActualQuantity();
            operationRecordDTO.setContent("您已在" + tradeWaybillDetailDO.getReceiveAddress() + "签收  实际签收量:"+ signQuantity + "吨");
            displayOperationRecordList.add(operationRecordDTO);
        }
        if (entry.getKey().equals("已关闭") && entry.getValue() != null) {
            OperationRecordDTO operationRecordDTO = entry.getValue();
            operationRecordDTO.setOperationType(WaybillOperationTypeEnum.WAYBILL_CLOSED.getCode());
            operationRecordDTO.setStatusDesc("已关闭");
            String content = operationRecordDTO.getContent();
            String content1 = content.substring(0,content.indexOf(":"));
            operationRecordDTO.setContent("您的运单已关闭: " + content.substring(content1.length()+1,content.length()));
            displayOperationRecordList.add(operationRecordDTO);
        }
    }

    private static List<String> handleContentList(TradeWaybillDetailDO tradeWaybillDetailDO) {
        List<String> contentList = new ArrayList<>();
        if (CsStringUtils.isNotEmpty(tradeWaybillDetailDO.getVehicleNum())) {
            contentList.add("车牌号：" + tradeWaybillDetailDO.getVehicleNum());
        }
        if (CsStringUtils.isNotEmpty(tradeWaybillDetailDO.getDriverName())) {
            contentList.add("配送司机：" + tradeWaybillDetailDO.getDriverName());
            contentList.add("联系电话：" + tradeWaybillDetailDO.getDriverPhone());
        }
        return contentList;
    }

    private static void handleOperationRecordMap(WaybillStatusEnum waybillStatusEnum,
                                                 List<OperationRecordDTO> operationRecordList,
                                                 Map<String, OperationRecordDTO> operationRecordMap,
                                                 Map<String, List<Integer>> operationStatusMap) {
        switch (waybillStatusEnum) {
            case WAIT_DELIVERY:
                handleOperationRecordMapByWaitDelivery(operationRecordList, operationRecordMap, operationStatusMap);
                break;
            case DELIVERING:
                handleOperationRecordMapByDelivering(operationRecordList, operationRecordMap, operationStatusMap);
                break;
            case COMPLETED:
                handleOperationRecordMapByComplete(operationRecordList, operationRecordMap, operationStatusMap);
                break;
            case CLOSED:
                handleOperationRecordMapByClosed(operationRecordList, operationRecordMap, operationStatusMap);
                break;
        }
    }

    private static void handleOperationRecordMapByClosed(List<OperationRecordDTO> operationRecordList, Map<String, OperationRecordDTO> operationRecordMap, Map<String, List<Integer>> operationStatusMap) {
        for (OperationRecordDTO operationRecordDTO : operationRecordList) {
            if (operationRecordMap.get("已签收") == null && operationStatusMap.get("已签收") != null &&
                    operationStatusMap.get("已签收").contains(operationRecordDTO.getOperationType())) {
                operationRecordMap.put("已签收", operationRecordDTO);
            }
            if (operationRecordMap.get("配送中") == null && operationStatusMap.get("配送中") != null &&
                    operationStatusMap.get("配送中").contains(operationRecordDTO.getOperationType())) {
                operationRecordMap.put("配送中", operationRecordDTO);
            }
            if (operationRecordMap.get("待配送") == null && operationStatusMap.get("待配送") != null &&
                    operationStatusMap.get("待配送").contains(operationRecordDTO.getOperationType())) {
                operationRecordMap.put("待配送", operationRecordDTO);
                break;
            }
            if (operationRecordMap.get("已关闭") == null && operationStatusMap.get("已关闭") != null &&
                    operationStatusMap.get("已关闭").contains(operationRecordDTO.getOperationType())) {
                operationRecordMap.put("已关闭", operationRecordDTO);
                break;
            }
        }
    }

    private static void handleOperationRecordMapByComplete(List<OperationRecordDTO> operationRecordList, Map<String, OperationRecordDTO> operationRecordMap, Map<String, List<Integer>> operationStatusMap) {
        for (OperationRecordDTO operationRecordDTO : operationRecordList) {
            if (operationRecordMap.get("已签收") == null && operationStatusMap.get("已签收") != null &&
                    operationStatusMap.get("已签收").contains(operationRecordDTO.getOperationType())) {
                operationRecordMap.put("已签收", operationRecordDTO);
            }
            if (operationRecordMap.get("配送中") == null && operationStatusMap.get("配送中") != null &&
                    operationStatusMap.get("配送中").contains(operationRecordDTO.getOperationType())) {
                operationRecordMap.put("配送中", operationRecordDTO);
            }
            if (operationRecordMap.get("待配送") == null && operationStatusMap.get("待配送") != null &&
                    operationStatusMap.get("待配送").contains(operationRecordDTO.getOperationType())) {
                operationRecordMap.put("待配送", operationRecordDTO);
                break;
            }
        }
    }

    private static void handleOperationRecordMapByDelivering(List<OperationRecordDTO> operationRecordList, Map<String, OperationRecordDTO> operationRecordMap, Map<String, List<Integer>> operationStatusMap) {
        for (OperationRecordDTO operationRecordDTO : operationRecordList) {
            if (operationRecordMap.get("配送中") == null && operationStatusMap.get("配送中") != null &&
                    operationStatusMap.get("配送中").contains(operationRecordDTO.getOperationType())) {
                operationRecordMap.put("配送中", operationRecordDTO);
            }
            if (operationRecordMap.get("待配送") == null && operationStatusMap.get("待配送") != null &&
                    operationStatusMap.get("待配送").contains(operationRecordDTO.getOperationType())) {
                operationRecordMap.put("待配送", operationRecordDTO);
                break;
            }
        }
    }

    private static void handleOperationRecordMapByWaitDelivery(List<OperationRecordDTO> operationRecordList, Map<String, OperationRecordDTO> operationRecordMap, Map<String, List<Integer>> operationStatusMap) {
        for (OperationRecordDTO operationRecordDTO : operationRecordList) {
            if (operationRecordMap.get("待配送") == null && operationStatusMap.get("待配送") != null &&
                    operationStatusMap.get("待配送").contains(operationRecordDTO.getOperationType())) {
                operationRecordMap.put("待配送", operationRecordDTO);
                break;
            }
        }
    }

    @Override
    public ItemResult<TradeWaybillDetailDTO> queryTradeWaybillDetailByNum(String waybillNum) {
        Waybill waybill = waybillQueryBizService.queryWaybillByWaybillNum(waybillNum);
        ItemResult<TradeWaybillDetailDTO> itemResult = queryTradeWaybillDetail(waybill.getWaybillId());
        itemResult.getData().setExternalWaybillNum(waybill.getExternalWaybillNum());
        return itemResult;
    }

    @Override
	public ItemResult<PageData<UserSeckillWaybillListDTO>> selectSeckillScanWaybillList(
			PageQuery<SeckillScanQueryDTO> pageQuery) {
		PageData<UserSeckillWaybillListDTO> dtos = waybillQueryBizService.selectSeckillScanWaybillList(pageQuery);
		return new ItemResult<>(dtos);
	}

    @Override
	public ItemResult<WaybillDetailDTO> selectMainWaybillInfoByMainWaybillId(String waybillId) {
		return new ItemResult<>(waybillQueryBizService.selectMainWaybillInfoByMainWaybillId(waybillId));
	}

	@Override
	public ItemResult<List<ReceiverInfoDTO>> selectReceiverInfoByMainWaybillId(String waybillId) {
		return new ItemResult<>(waybillQueryBizService.selectReceiverInfoByMainWaybillId(waybillId));
	}

    @Override
    public ItemResult<List<WarehouseOptionDTO>> selectSeckillWarehouseByUserId(String userId) {
        return new ItemResult<>(waybillQueryBizService.selectSeckillWarehouseByUserId(userId));
    }

    @Override
    public ItemResult<DriverDeliveryDTO> queryDriverDeliveryStatus(String driverId) {
        Integer halfWaybillCount = waybillQueryBizService.queryDriverDeliveryStatus(driverId);
        DriverDeliveryDTO driverDeliveryDTO = new DriverDeliveryDTO();
        driverDeliveryDTO.setIsHalfWaybill(halfWaybillCount > 0 ? Boolean.TRUE : Boolean.FALSE);
        driverDeliveryDTO.setHalfWayVehicleNumList(waybillQueryBizService.queryHalfWayVehicleNum(driverId));
        return new ItemResult<>(driverDeliveryDTO);
    }

    @Override
    public ItemResult<List<SnatchedWaybillDTO>> querySnatchedWaybillList(SnatchedWaybillQueryDTO snatchedWaybillQueryDTO) {
        return new ItemResult<>(waybillQueryBizService.querySnatchedWaybillList(snatchedWaybillQueryDTO));
    }

    @Override
    public ItemResult<List<EvaluateWaybillListDTO>> queryEvaluateWaybillList(List<String> deliverySheetNumList) {
        return new ItemResult<>(waybillQueryBizService.queryEvaluateWaybillList(deliverySheetNumList));
    }

    @Override
    public ItemResult<List<PickingBillDTO>> queryPickingListByWaybillNum(List<String> waybillNumList) {
        return new ItemResult<>(waybillQueryBizService.queryPickingListByWaybillNum(waybillNumList));
    }

    @Override
    public ItemResult<List<WaybillCodeDTO>> queryDeliverySheetNumByWaybillId(List<String> waybillIdList) {
        return new ItemResult<>(waybillQueryBizService.queryDeliverySheetNumByWaybillId(waybillIdList));
    }

    @Override
    public ItemResult<Void> notifyLeaveWarehouse(NotifyLeaveWarehouseDTO notifyLeaveWarehouseDTO) {
        if (CollectionUtils.isEmpty(notifyLeaveWarehouseDTO.getWaybillNumList())) {
            throw new BizException(BasicCode.INVALID_PARAM, "运单号列表");
        }
        if (notifyLeaveWarehouseDTO.getCentralWarehouseFlag() == null) {
            throw new BizException(BasicCode.INVALID_PARAM, "中心仓标识");
        }
        //获取运单信息
        List<Waybill> waybillList = waybillQueryBizService.queryByWaybillNumList(notifyLeaveWarehouseDTO.getWaybillNumList());
        if (CollectionUtils.isEmpty(waybillList)) {
            throw new BizException(BasicCode.DATA_NOT_EXIST, "运单:" + CsStringUtils.join(notifyLeaveWarehouseDTO.getWaybillNumList(), ","));
        }
        waybillList.stream().forEach(waybill -> {
            LeaveWarehouseDTO leaveWarehouseDTO = new LeaveWarehouseDTO();
            leaveWarehouseDTO.setWaybillId(waybill.getWaybillId());
            leaveWarehouseDTO.setActualQuantity(waybill.getActualQuantity());
            leaveWarehouseDTO.setCentralWarehouseFlag(notifyLeaveWarehouseDTO.getCentralWarehouseFlag());
            leaveWarehouseDTO.setUserId(waybill.getUpdateUser());
            leaveWarehouseDTO.setUserName("system");
            leaveWarehouseDTO.setExternalFlag(1);
            leaveWarehouseDTO.setFromWebFlag((byte)0);
            leaveWarehouseDTO.setOriginPlace(waybill.getOriginPlace());
            //调用出站操作
            inputLeaveWarehouseQuantity(leaveWarehouseDTO);
        });

        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<List<WaybillStatusOption>> queryWaybillStatusByNum(List<String> waybillNumList) {
        return new ItemResult<>(waybillQueryBizService.queryWaybillStatusByNum(waybillNumList));
    }

    @Override
    public ItemResult<Void> enterFactory(EnterFactoryDTO dto) {
        Waybill waybill = waybillQueryBizService.selectWaybillById(dto.getWaybillId());
        if (waybill == null) {
            throw new BizException(BasicCode.DATA_NOT_EXIST, "运单:" + dto.getWaybillId());
        }
        if (CsStringUtils.equals(waybill.getBillProxyType(), BillProxyTypeEnum.SECONDARY.getCode())) {
            //二级不可来自外部操作进厂
            throw new BizException(BasicCode.UNDEFINED_ERROR, "本级背靠背运单不可进厂!");
        }
        //先进厂外部操作
        waybillOperationBizService.enterFactory(dto);
        //同步进厂一级
        if (CsStringUtils.equals(waybill.getBillProxyType(), BillProxyTypeEnum.PRIMARY.getCode())) {
            EnterFactoryDTO relationEnterFactoryDTO = new EnterFactoryDTO();
            BeanUtils.copyProperties(dto, relationEnterFactoryDTO);
            relationEnterFactoryDTO.setWaybillId(waybillMapBizService.findRelationWaybillId(waybill.getWaybillId(), waybill.getBillProxyType()));
            waybillOperationBizService.enterFactory(relationEnterFactoryDTO);
        }
        return new ItemResult<>(null);
    }

    @Override
    public ItemResult<Void> withdrawEnterFactory(EnterFactoryDTO dto) {
        Waybill waybill = waybillQueryBizService.selectWaybillById(dto.getWaybillId());
        if (waybill == null) {
            throw new BizException(BasicCode.DATA_NOT_EXIST, "运单:" + dto.getWaybillId());
        }
        if (CsStringUtils.equals(waybill.getBillProxyType(), BillProxyTypeEnum.SECONDARY.getCode())) {
            // 二级不可来自外部操作撤回进厂
            throw new BizException(BasicCode.UNDEFINED_ERROR, "本级背靠背运单不可撤回进厂!");
        }
        // 先撤回进厂外部操作
        waybillOperationBizService.withdrawEnterFactory(dto);
        // 同步撤回进厂一级
        if (CsStringUtils.equals(waybill.getBillProxyType(), BillProxyTypeEnum.PRIMARY.getCode())) {
            EnterFactoryDTO relationEnterFactoryDTO = new EnterFactoryDTO();
            BeanUtils.copyProperties(dto, relationEnterFactoryDTO);
            relationEnterFactoryDTO.setWaybillId(waybillMapBizService.findRelationWaybillId(waybill.getWaybillId(), waybill.getBillProxyType()));
            waybillOperationBizService.withdrawEnterFactory(relationEnterFactoryDTO);
        }
        return new ItemResult<>(null);
    }

    private void updateExternalWaybill(SellerAssignWaybillDTO dto){
        Waybill waybill = waybillQueryBizService.selectWaybillById(dto.getWaybillId());
        if (waybill.getSyncFlag().equals(ExternalSyncFlagEnum.FORWARD_SYNC.getCode())) {
            ExternalWaybillRequestDTO externalWaybillRequestDTO = new ExternalWaybillRequestDTO();
            externalWaybillRequestDTO.setWaybillId(dto.getWaybillId());
            externalWaybillRequestDTO.setOperatorUserId(dto.getUserId());
            externalWaybillRequestDTO.setOperatorUserName(dto.getUserName());
            //修改车辆
            externalWaybillRequestDTO.setVehicleNum(dto.getVehicleNum());
            waybillExternalService.updateExternalWaybill(externalWaybillRequestDTO);
        }
    }
}
