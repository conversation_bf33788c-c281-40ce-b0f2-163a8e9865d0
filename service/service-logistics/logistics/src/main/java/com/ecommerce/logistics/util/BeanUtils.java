package com.ecommerce.logistics.util;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: chufeng
 * @Date: 03/01/2019 11:17
 * @DESCRIPTION:
 */
@Slf4j
public class BeanUtils {

    private BeanUtils() {
        throw new IllegalStateException("BeanUtils class");
    }
    
    public static <T> T convert(Object source, Class<T> targetClass){
        if( source == null ){
            return null;
        }
        try {
            T target = targetClass.getDeclaredConstructor().newInstance();
            org.springframework.beans.BeanUtils.copyProperties(source,target);
            return target;
        } catch (Exception e) {
            log.error(e.getMessage(),e);
        }
        return null;
    }

    public static <T> List<T> convert(List<?> sourceList,Class<T> itemClass){
        if(sourceList == null || sourceList.isEmpty() ){
            return Lists.newArrayList();
        }
        return sourceList.stream().map(item->convert(item,itemClass)).toList();
    }
}
