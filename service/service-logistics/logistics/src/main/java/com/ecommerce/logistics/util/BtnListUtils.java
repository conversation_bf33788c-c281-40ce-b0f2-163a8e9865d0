package com.ecommerce.logistics.util;

import com.alibaba.fastjson.JSON;
import com.ecommerce.common.enums.AppNames;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.logistics.LogisticsCommonBean;
import com.ecommerce.logistics.api.dto.shipbill.ShipBillListDTO;
import com.ecommerce.logistics.api.enums.BillProxyTypeEnum;
import com.ecommerce.logistics.api.enums.ExternalSyncFlagEnum;
import com.ecommerce.logistics.api.enums.PickingBillTypeEnum;
import com.ecommerce.logistics.api.enums.ShipBillItemStatusEnum;
import com.ecommerce.logistics.api.enums.ShipBillOperateEnum;
import com.ecommerce.logistics.api.enums.ShipBillStatusEnum;
import com.ecommerce.logistics.api.enums.TransportToolTypeEnum;
import com.ecommerce.logistics.api.enums.WarehouseTypeEnum;
import com.ecommerce.logistics.api.enums.WaybillExternalStatusEnum;
import com.ecommerce.logistics.api.enums.WaybillTypeEnum;
import com.ecommerce.order.api.enums.AdjustAddWayEnum;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * @Auther: colu
 * @Date: 2021-02-04 15:57
 * @Description: BtnListUtils
 */
@Slf4j
public class BtnListUtils {

    private BtnListUtils(){
        throw new IllegalStateException("BtnListUtils class");
    }

    public static List<String> getShipBillListBtnList(ShipBillListDTO dto, String appName) {
        log.info("getShipBillListBtnList:{},appName:{}", JSON.toJSONString(dto),appName);
        List<String> btnList;
        AppNames appNames = AppNames.getByCode(appName);

        switch (appNames) {
            case WEB_SERVICE_BUYERAPP:
            case WEB_SERVICE_BUYER : {
                btnList = getBuyerShipBillListBtnList(dto);
                break;
            } case WEB_SERVICE_SELLER: {
                btnList = getSellerShipBillListBtnList(dto);
                break;
            } case WEB_SERVICE_CARRIER: {
                btnList = getCarrierShipBillListBtnList(dto);
                break;
            } case WEB_SERVICE_PLATFORM: {
                btnList = getPlatformShipBillListBtnList(dto);
                break;
            } case WEB_SERVICE_CARRIERAPP: {
                btnList = getCarrierShipBillListBtnList(dto);
                break;
            }
            default: {
                log.info("不能判断运单列表查询的按钮列表:{}", appName);
                btnList = getSellerShipBillListBtnList(dto);
            }
        }
        return btnList;
    }

    private static List<String> getBuyerShipBillListBtnList(ShipBillListDTO dto) {
        List<String> btnList = new ArrayList<>();

        if (CsStringUtils.isNotBlank(dto.getQrCode())) {
            btnList.add(ShipBillOperateEnum.QR_CODE.getCode());
        }

        // 取消：can_operate = 1 & ShipBil.status 待配送 & external_waybill_status 没有进站的
        List<String> canCancelStatus = Lists.newArrayList(WaybillExternalStatusEnum.ERP_CREATE_SUCCESS.getCode(),
                WaybillExternalStatusEnum.ERP_CREATE_FAIL.getCode(),
                WaybillExternalStatusEnum.ERP_CHANGE_SUCCESS.getCode(),
                WaybillExternalStatusEnum.ERP_CLOSE_SUCCESS.getCode());
        if (dto.getCanOperate() == 1 &&
                ShipBillStatusEnum.WAIT_DELIVERY.getCode().equals(dto.getStatus()) &&
                (CsStringUtils.isBlank(dto.getExternalWaybillStatus()) || canCancelStatus.contains(dto.getExternalWaybillStatus())) &&
                CsStringUtils.equals(dto.getType(), WaybillTypeEnum.BUYER_PICK.getCode())) {
            //重新指派
            btnList.add(ShipBillOperateEnum.ASSIGN.getCode());
        }

        //完成
        if ((CsStringUtils.equals(dto.getItemStatus(), ShipBillItemStatusEnum.LEAVE_WAREHOUSE.getCode()) ||
                CsStringUtils.equals(dto.getItemStatus(), ShipBillItemStatusEnum.COMPLETE.getCode())) &&
                dto.getCanOperate() == 1 &&
                CsStringUtils.equals(dto.getStatus(), ShipBillStatusEnum.DELIVERING.getCode()) &&
                CsStringUtils.equals(dto.getItemType(), PickingBillTypeEnum.BUYER_TAKE.getCode())) {
            btnList.add(ShipBillOperateEnum.COMPLETE.getCode());
        }

        // 轨迹回放/报警查看：ShipBil.status 在 配送中或者之后
        if (ShipBillStatusEnum.DELIVERING.getCode().compareTo(dto.getStatus()) <= 0) {
            btnList.add(ShipBillOperateEnum.TRACK_PLAYBACK.getCode());
            btnList.add(ShipBillOperateEnum.WARNING_CHECK.getCode());
        }

        // 未进站之前都可以关闭运单：can_operate = 1 & ShipBil.status 待配送 & external_waybill_status 没有进站的
        List<String> canCloseStatus = Lists.newArrayList(
                WaybillExternalStatusEnum.ERP_CREATE_SUCCESS.getCode(),
                WaybillExternalStatusEnum.ERP_CREATE_FAIL.getCode(),
                WaybillExternalStatusEnum.ERP_CHANGE_SUCCESS.getCode());
        if (dto.getCanOperate() == 1 &&
                ShipBillStatusEnum.WAIT_DELIVERY.getCode().equals(dto.getStatus()) &&
                (CsStringUtils.isBlank(dto.getExternalWaybillStatus()) || canCloseStatus.contains(dto.getExternalWaybillStatus())) &&
                CsStringUtils.equals(dto.getType(), WaybillTypeEnum.BUYER_PICK.getCode())) {
            //关闭运单
            btnList.add(ShipBillOperateEnum.CLOSE.getCode());
        }
        return btnList;
    }

    private static List<String> getSellerShipBillListBtnList(ShipBillListDTO dto) {
        List<String> btnList = new ArrayList<>();

        if (CsStringUtils.isNotBlank(dto.getQrCode())) {
            btnList.add(ShipBillOperateEnum.QR_CODE.getCode());
        }

        // 取消：can_operate = 1 & ShipBil.status 待配送 & external_waybill_status 没有进站的
        addCancel(dto, btnList);

        if (dto.getCanOperate() == 1 &&
                (ShipBillStatusEnum.CANCELED.getCode().equals(dto.getStatus()) || ShipBillStatusEnum.WAIT_ASSIGN.getCode().equals(dto.getStatus())) &&
                !CsStringUtils.equals(dto.getSyncFlag(), ExternalSyncFlagEnum.BACKWARD_SYNC.getCode()) &&
                CsStringUtils.equals(dto.getType(), WaybillTypeEnum.SELLER_DELIVERY.getCode())) {
            btnList.add(ShipBillOperateEnum.ASSIGN.getCode());
        }

        // 出站：seller_id = owner_id & 子项状态 in (待配送,出站待补款)
        addLeaveWarehouse(dto, btnList);
        // 开仓：seller_id = owner_id & 子项状态 in (已出站, 开仓待补款) & 外部运单状态不是开仓中
        if (CsStringUtils.equals(dto.getTransportToolType(), TransportToolTypeEnum.WATER_TRANSPORT.getCode()) &&
                CsStringUtils.equals(dto.getSellerId(), dto.getOwnerId()) &&
                CsStringUtils.equals(dto.getStatus(), ShipBillStatusEnum.DELIVERING.getCode()) &&
                (CsStringUtils.equals(dto.getItemStatus(), ShipBillItemStatusEnum.LEAVE_WAREHOUSE.getCode()) ||
                        CsStringUtils.equals(dto.getItemStatus(), ShipBillItemStatusEnum.CABIN_WAIT_SUPPLEMENT.getCode())) &&
                CsStringUtils.equals(dto.getItemType(), PickingBillTypeEnum.SELLER_DELIVERY.getCode()) &&
                !CsStringUtils.equals(dto.getExternalWaybillStatus(), WaybillExternalStatusEnum.OPEN_CABIN_ING.getCode())) {
            btnList.add(ShipBillOperateEnum.OPEN_CABIN.getCode());
        }
        // 卸货提醒：ShipBil.status = 已开仓
        if (ShipBillItemStatusEnum.OPEN_CABIN.getCode().equals(dto.getItemStatus())) {
            btnList.add(ShipBillOperateEnum.UNLOADING_REMIND.getCode());
        }
        //完成
        addCompleteAndSign(dto, btnList);
        if (CsStringUtils.equals(dto.getItemStatus(), ShipBillItemStatusEnum.OPEN_CABIN.getCode()) && dto.getCanOperate() == 1) {
            btnList.add(ShipBillOperateEnum.COMPLETE.getCode());
        }
        if (CsStringUtils.equals(dto.getItemStatus(), ShipBillItemStatusEnum.COMPLETE.getCode()) &&
                CsStringUtils.equals(dto.getStatus(), ShipBillStatusEnum.DELIVERING.getCode()) &&
                dto.getCanOperate() == 1) {
            btnList.add(ShipBillOperateEnum.COMPLETE.getCode());
        }

        // 轨迹回放/报警查看：ShipBil.status 在 配送中或者之后
        if (ShipBillStatusEnum.DELIVERING.getCode().compareTo(dto.getStatus()) <= 0) {
            btnList.add(ShipBillOperateEnum.TRACK_PLAYBACK.getCode());
            btnList.add(ShipBillOperateEnum.WARNING_CHECK.getCode());
        }

        return btnList;
    }

    private static void addCompleteAndSign(ShipBillListDTO dto, List<String> btnList) {
        if (CsStringUtils.equals(dto.getItemStatus(), ShipBillItemStatusEnum.LEAVE_WAREHOUSE.getCode()) && dto.getCanOperate() == 1) {
            if (CsStringUtils.equals(TransportToolTypeEnum.WATER_TRANSPORT.getCode(), dto.getTransportToolType()) &&
                    (CsStringUtils.equals(dto.getItemType(), PickingBillTypeEnum.BUYER_TAKE.getCode()) ||
                            CsStringUtils.equals(dto.getItemType(), PickingBillTypeEnum.PLATFORM_DELIVERY.getCode()))) {
                btnList.add(ShipBillOperateEnum.COMPLETE.getCode());
            }
            if (CsStringUtils.equals(TransportToolTypeEnum.ROAD_TRANSPORT.getCode(), dto.getTransportToolType())) {
                if (CsStringUtils.equals(dto.getSignType(), AdjustAddWayEnum.SELLER_SIGIN.getCode())) {
                    btnList.add(ShipBillOperateEnum.SIGN.getCode());
                } else {
                    btnList.add(ShipBillOperateEnum.COMPLETE.getCode());
                }
            }
        }
    }

    private static void addLeaveWarehouse(ShipBillListDTO dto, List<String> btnList) {
        if (CsStringUtils.equals(dto.getTransportToolType(), TransportToolTypeEnum.WATER_TRANSPORT.getCode())) {
            //船运
            if (CsStringUtils.equals(dto.getSellerId(), dto.getOwnerId()) &&
                    (CsStringUtils.equals(dto.getStatus(), ShipBillStatusEnum.WAIT_DELIVERY.getCode()) ||
                            CsStringUtils.equals(dto.getStatus(), ShipBillStatusEnum.DELIVERING.getCode())) &&
                    (CsStringUtils.equals(dto.getItemStatus(), ShipBillItemStatusEnum.WAIT_DELIVERY.getCode()) ||
                            CsStringUtils.equals(dto.getItemStatus(), ShipBillItemStatusEnum.WAREHOUSE_WAIT_SUPPLEMENT.getCode())) &&
                    CsStringUtils.equals(dto.getSyncFlag(), ExternalSyncFlagEnum.NO_SYNC.getCode())) {
                    btnList.add(ShipBillOperateEnum.LEAVE_WAREHOUSE.getCode());
            }
        } else {
            //汽运
            if (CsStringUtils.equals(dto.getSellerId(), dto.getOwnerId()) &&
                    !CsStringUtils.equals(dto.getWarehouseType(), WarehouseTypeEnum.CENTRAL_WAREHOUSE.getCode()) &&
                    CsStringUtils.equals(dto.getStatus(), ShipBillStatusEnum.WAIT_DELIVERY.getCode()) &&
                    CsStringUtils.equals(dto.getItemStatus(), ShipBillItemStatusEnum.WAIT_DELIVERY.getCode()) &&
                    CsStringUtils.equals(dto.getSyncFlag(), ExternalSyncFlagEnum.NO_SYNC.getCode())) {
                    btnList.add(ShipBillOperateEnum.LEAVE_WAREHOUSE.getCode());
            }
        }
    }

    private static void addCancel(ShipBillListDTO dto, List<String> btnList) {
        List<String> canCancelStatus = Lists.newArrayList(WaybillExternalStatusEnum.ERP_CREATE_SUCCESS.getCode(),
                WaybillExternalStatusEnum.ERP_CREATE_FAIL.getCode(),
                WaybillExternalStatusEnum.ERP_CHANGE_SUCCESS.getCode(),
                WaybillExternalStatusEnum.ERP_CLOSE_SUCCESS.getCode());
        if (dto.getCanOperate() == 1 &&
                ShipBillStatusEnum.WAIT_DELIVERY.getCode().equals(dto.getStatus()) &&
                (CsStringUtils.isBlank(dto.getExternalWaybillStatus()) || canCancelStatus.contains(dto.getExternalWaybillStatus())) &&
                !CsStringUtils.equals(dto.getSyncFlag(), ExternalSyncFlagEnum.BACKWARD_SYNC.getCode()) &&
                CsStringUtils.equals(dto.getType(), WaybillTypeEnum.SELLER_DELIVERY.getCode())) {
                btnList.add(ShipBillOperateEnum.CANCEL.getCode());
        }
    }

    private static List<String> getCarrierShipBillListBtnList(ShipBillListDTO dto) {
        List<String> btnList = new ArrayList<>();

        if (CsStringUtils.isNotBlank(dto.getQrCode())) {
            btnList.add(ShipBillOperateEnum.QR_CODE.getCode());
        }

        // 取消：can_operate = 1 & ShipBil.status 待配送 & external_waybill_status 没有进站的
        List<String> canCancelStatus = Lists.newArrayList(WaybillExternalStatusEnum.ERP_CREATE_SUCCESS.getCode(),
                WaybillExternalStatusEnum.ERP_CREATE_FAIL.getCode(),
                WaybillExternalStatusEnum.ERP_CHANGE_SUCCESS.getCode(),
                WaybillExternalStatusEnum.ERP_CLOSE_SUCCESS.getCode());
        if (dto.getCanOperate() == 1 &&
                ShipBillStatusEnum.WAIT_DELIVERY.getCode().equals(dto.getStatus()) &&
                (CsStringUtils.isBlank(dto.getExternalWaybillStatus()) || canCancelStatus.contains(dto.getExternalWaybillStatus())) &&
                !CsStringUtils.equals(dto.getSyncFlag(), ExternalSyncFlagEnum.BACKWARD_SYNC.getCode()) &&
                CsStringUtils.equals(dto.getType(), WaybillTypeEnum.CARRIER_ASSIGNING.getCode())) {
            btnList.add(ShipBillOperateEnum.CANCEL.getCode());
        }

        //指派
        if (dto.getCanOperate() == 1 &&
                (ShipBillStatusEnum.CANCELED.getCode().equals(dto.getStatus()) || ShipBillStatusEnum.WAIT_ASSIGN.getCode().equals(dto.getStatus())) &&
                (CsStringUtils.equals(dto.getType(), WaybillTypeEnum.CARRIER_ASSIGNING.getCode()) || CsStringUtils.equals(dto.getType(), WaybillTypeEnum.SOCIETY_SNATCH.getCode()))
        ) {
            btnList.add(ShipBillOperateEnum.ASSIGN.getCode());
        }

        //完成
        handleBtnListByComplete(dto, btnList);

        // 轨迹回放/报警查看：ShipBil.status 在 配送中或者之后
        if (ShipBillStatusEnum.DELIVERING.getCode().compareTo(dto.getStatus()) <= 0) {
            btnList.add(ShipBillOperateEnum.TRACK_PLAYBACK.getCode());
            btnList.add(ShipBillOperateEnum.WARNING_CHECK.getCode());
        }

        // 上传支付凭证：ShipBil.status 配送中 & deliveryCertificateFlag = 0
        // 船已到港：船运，提货类型卖家配送，配送中状态，arrive_destination_time 送达时间为空
        // 开仓提醒：船运，提货类型卖家配送，配送中状态，arrive_destination_time 送达时间不为空
        handleBtnListByArriveDestinationTimeStr(dto, btnList);

        return btnList;
    }

    private static void handleBtnListByComplete(ShipBillListDTO dto, List<String> btnList) {
        if (CsStringUtils.equals(dto.getItemStatus(), ShipBillItemStatusEnum.LEAVE_WAREHOUSE.getCode()) && dto.getCanOperate() == 1) {
            if (CsStringUtils.equals(TransportToolTypeEnum.WATER_TRANSPORT.getCode(), dto.getTransportToolType()) &&
                    (CsStringUtils.equals(dto.getItemType(), PickingBillTypeEnum.BUYER_TAKE.getCode()) ||
                            CsStringUtils.equals(dto.getItemType(), PickingBillTypeEnum.PLATFORM_DELIVERY.getCode()))) {
                btnList.add(ShipBillOperateEnum.COMPLETE.getCode());
            }
            if (CsStringUtils.equals(TransportToolTypeEnum.ROAD_TRANSPORT.getCode(), dto.getTransportToolType()) &&
                    !CsStringUtils.equals(dto.getSignType(), AdjustAddWayEnum.SELLER_SIGIN.getCode())) {
                    btnList.add(ShipBillOperateEnum.COMPLETE.getCode());
            }
        }
        if (CsStringUtils.equals(dto.getItemStatus(), ShipBillItemStatusEnum.OPEN_CABIN.getCode()) && dto.getCanOperate() == 1) {
            btnList.add(ShipBillOperateEnum.COMPLETE.getCode());
        }
        if (CsStringUtils.equals(dto.getItemStatus(), ShipBillItemStatusEnum.COMPLETE.getCode()) &&
                CsStringUtils.equals(dto.getStatus(), ShipBillStatusEnum.DELIVERING.getCode()) &&
                dto.getCanOperate() == 1 &&
                !CsStringUtils.equals(dto.getSignType(), AdjustAddWayEnum.SELLER_SIGIN.getCode())) {
                btnList.add(ShipBillOperateEnum.COMPLETE.getCode());
        }
    }

    private static void handleBtnListByArriveDestinationTimeStr(ShipBillListDTO dto, List<String> btnList) {
        if (CsStringUtils.equals(dto.getTransportToolType(), TransportToolTypeEnum.WATER_TRANSPORT.getCode()) &&
                CsStringUtils.equals(dto.getItemType(), PickingBillTypeEnum.SELLER_DELIVERY.getCode()) &&
                CsStringUtils.equals(dto.getStatus(), ShipBillStatusEnum.DELIVERING.getCode())
        ){
            if (CsStringUtils.isNotBlank(dto.getArriveDestinationTimeStr())) {
                btnList.add(ShipBillOperateEnum.OPEN_CABIN_REMIND.getCode());
            }else {
                btnList.add(ShipBillOperateEnum.SHIP_ARRIVE.getCode());
            }
        }
    }

    private static List<String> getPlatformShipBillListBtnList(ShipBillListDTO dto) {
        List<String> btnList = new ArrayList<>();

        if (CsStringUtils.isNotBlank(dto.getQrCode())) {
            btnList.add(ShipBillOperateEnum.QR_CODE.getCode());
        }

        // 取消：can_operate = 1 & ShipBil.status 待配送 & external_waybill_status 没有进站的 只能是平台配送 承运商id是"PLATFORM_ID"
        List<String> canCancelStatus = Lists.newArrayList(WaybillExternalStatusEnum.ERP_CREATE_SUCCESS.getCode(),
                WaybillExternalStatusEnum.ERP_CREATE_FAIL.getCode(),
                WaybillExternalStatusEnum.ERP_CHANGE_SUCCESS.getCode(),
                WaybillExternalStatusEnum.ERP_CLOSE_SUCCESS.getCode());
        if (dto.getCanOperate() == 1 &&
                ShipBillStatusEnum.WAIT_DELIVERY.getCode().equals(dto.getStatus()) &&
                CsStringUtils.equals(dto.getCarrierId(), LogisticsCommonBean.PLATFORM_DELIVERY) &&
                (CsStringUtils.isBlank(dto.getExternalWaybillStatus()) || canCancelStatus.contains(dto.getExternalWaybillStatus())) &&
                !CsStringUtils.equals(dto.getSyncFlag(), ExternalSyncFlagEnum.BACKWARD_SYNC.getCode()) &&
                CsStringUtils.equals(dto.getItemType(), PickingBillTypeEnum.PLATFORM_DELIVERY.getCode())) {
            btnList.add(ShipBillOperateEnum.CANCEL.getCode());
        }

        //关闭, 状态满足, 所有配送方式

        List<String> canCloseStatusList = Lists.newArrayList(
                ShipBillStatusEnum.WAIT_DELIVERY.getCode(),
                ShipBillStatusEnum.WAIT_CONFIRM.getCode(),
                ShipBillStatusEnum.WAIT_AUDIT.getCode(),
                ShipBillStatusEnum.WAIT_RECEIVE.getCode(),
                ShipBillStatusEnum.WAIT_ASSIGN.getCode(),
                ShipBillStatusEnum.SNATCHED.getCode(),
                ShipBillStatusEnum.CANCELED.getCode()
        );
        handleBtnListByCanCloseStatusList(dto, canCloseStatusList, canCancelStatus, btnList);

        return btnList;
    }

    private static void handleBtnListByCanCloseStatusList(ShipBillListDTO dto,
                                                          List<String> canCloseStatusList,
                                                          List<String> canCancelStatus, List<String> btnList) {
        addClose(dto, canCloseStatusList, canCancelStatus, btnList);

        //指派
        addAssign(dto, btnList);

        //重新指派-司机拒绝后，平台重新指派车辆或船舶
        if (dto.getCanOperate() == 1 &&
                CsStringUtils.equals(dto.getType(), WaybillTypeEnum.PLATFORM_ASSIGNING.getCode()) &&
                CsStringUtils.equals(dto.getStatus(), ShipBillStatusEnum.WAIT_ASSIGN.getCode()) &&
                !CsStringUtils.equals(dto.getSyncFlag(), ExternalSyncFlagEnum.BACKWARD_SYNC.getCode())) {
            btnList.add(ShipBillOperateEnum.REASSIGNMENT.getCode());
        }
        //出站
        if (CsStringUtils.equals(dto.getTransportToolType(), TransportToolTypeEnum.ROAD_TRANSPORT.getCode()) &&
                !CsStringUtils.equals(dto.getBillProxyType(), BillProxyTypeEnum.SECONDARY.getCode()) &&
                CsStringUtils.equals(dto.getStatus(), ShipBillStatusEnum.WAIT_DELIVERY.getCode()) &&
                CsStringUtils.equals(dto.getItemStatus(), ShipBillItemStatusEnum.WAIT_DELIVERY.getCode()) &&
                CsStringUtils.equals(dto.getWarehouseType(), WarehouseTypeEnum.CENTRAL_WAREHOUSE.getCode())) {
                btnList.add(ShipBillOperateEnum.LEAVE_WAREHOUSE.getCode());
        }

        //完成
        addComplete(dto, btnList);

        // 轨迹回放/报警查看：ShipBil.status 在 配送中或者之后
        if (ShipBillStatusEnum.DELIVERING.getCode().compareTo(dto.getStatus()) <= 0) {
            btnList.add(ShipBillOperateEnum.TRACK_PLAYBACK.getCode());
            btnList.add(ShipBillOperateEnum.WARNING_CHECK.getCode());
        }

        //指派 运单类型是社会运力抢单,运单状态为待接单或已取消
        if (CsStringUtils.equals(dto.getType(), WaybillTypeEnum.SOCIETY_SNATCH.getCode())
                && (CsStringUtils.equals(dto.getStatus(), ShipBillStatusEnum.WAIT_RECEIVE.getCode()) || CsStringUtils.equals(dto.getStatus(), ShipBillStatusEnum.CANCELED.getCode()))) {
            btnList.add(ShipBillOperateEnum.ASSIGN.getCode());
        }
    }

    private static void addComplete(ShipBillListDTO dto, List<String> btnList) {
        if (CsStringUtils.equals(dto.getItemStatus(), ShipBillItemStatusEnum.LEAVE_WAREHOUSE.getCode()) && dto.getCanOperate() == 1) {
            if (CsStringUtils.equals(TransportToolTypeEnum.WATER_TRANSPORT.getCode(), dto.getTransportToolType()) &&
                    CsStringUtils.equals(dto.getItemType(), PickingBillTypeEnum.PLATFORM_DELIVERY.getCode())) {
                btnList.add(ShipBillOperateEnum.COMPLETE.getCode());
            }
            if (CsStringUtils.equals(TransportToolTypeEnum.ROAD_TRANSPORT.getCode(), dto.getTransportToolType()) &&
                    CsStringUtils.equals(dto.getItemType(), PickingBillTypeEnum.PLATFORM_DELIVERY.getCode())) {
                btnList.add(ShipBillOperateEnum.COMPLETE.getCode());
            }
        }
    }

    private static void addAssign(ShipBillListDTO dto, List<String> btnList) {
        if (dto.getCanOperate() == 1 &&
                CsStringUtils.equals(dto.getType(), WaybillTypeEnum.SOCIETY_SNATCH.getCode()) &&
                (CsStringUtils.equals(dto.getStatus(), ShipBillStatusEnum.WAIT_RECEIVE.getCode()) || CsStringUtils.equals(dto.getStatus(), ShipBillStatusEnum.CANCELED.getCode())) &&
                !CsStringUtils.equals(dto.getSyncFlag(), ExternalSyncFlagEnum.BACKWARD_SYNC.getCode())) {
            btnList.add(ShipBillOperateEnum.ASSIGN.getCode());
        }

        //指派-承运商
        if (dto.getCanOperate() == 1 &&
                CsStringUtils.equals(dto.getType(), WaybillTypeEnum.PLATFORM_ASSIGNING.getCode()) &&
                (CsStringUtils.equals(dto.getStatus(), ShipBillStatusEnum.CANCELED.getCode())) &&
                !CsStringUtils.equals(dto.getSyncFlag(), ExternalSyncFlagEnum.BACKWARD_SYNC.getCode())) {
            btnList.add(ShipBillOperateEnum.ASSIGN.getCode());
        }
    }

    private static void addClose(ShipBillListDTO dto, List<String> canCloseStatusList, List<String> canCancelStatus, List<String> btnList) {
        if (dto.getCanOperate() == 1 &&
                canCloseStatusList.contains(dto.getStatus()) &&
                !CsStringUtils.equals(dto.getSyncFlag(), ExternalSyncFlagEnum.BACKWARD_SYNC.getCode()) &&
                (CsStringUtils.isBlank(dto.getExternalWaybillStatus()) || canCancelStatus.contains(dto.getExternalWaybillStatus()))) {
            btnList.add(ShipBillOperateEnum.CLOSE.getCode());
        }
    }


}
