package com.ecommerce.logistics.util;

import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;

public class DigestUtils {

    private DigestUtils(){
        throw new IllegalStateException("DigestUtils class");
    }

    /**
     * MD5摘要算法
     *
     * @param content 完成内容
     * @return 摘要串
     */
    public static String md5Digest(String content) {
        try {
            String algorithm = "MD5";
            MessageDigest md = MessageDigest.getInstance(algorithm);
            byte[] bytes = md.digest(content.getBytes(StandardCharsets.UTF_8));
            return Bytes.bytes2hex(bytes);
        } catch (Exception e) {
            throw new BizException(BasicCode.INVALID_PARAM, "生成内容摘要失败:content=" + content);
        }
    }
}
