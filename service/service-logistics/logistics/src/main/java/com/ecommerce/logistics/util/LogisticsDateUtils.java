package com.ecommerce.logistics.util;

import cn.hutool.core.date.DateUtil;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 日期格式处理类
 * <AUTHOR>
 * Created on 2020/11/30 12:23
 */
@Slf4j
public class LogisticsDateUtils {

    private LogisticsDateUtils(){
        throw new IllegalStateException("DateUtils class");
    }

    private static final String YYYY_MM_DD = "yyyy-MM-dd";
    private static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
    private static final String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";
    private static final String YYYY_MM_DD_HH_MM_SS_SSS = "yyyy-MM-dd HH:mm:ss.SSS";
    private static final String HH_MM_SS = "HH:mm:ss";

    /**
     * 给指定日期添加时间后缀
     * @param date 2020-12-11
     * @param suffix 如23:59:59
     * @return 2020-12-11 23:59:59
     */
    public static Date splicingTime(Date date, String suffix) {
        Date time = null;
        SimpleDateFormat df = new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS);
        SimpleDateFormat ds = new SimpleDateFormat(YYYY_MM_DD);
        try {
            time = df.parse(ds.format(date) + " "+suffix);
        } catch (ParseException e) {
            log.error("给指定日期添加时间后缀异常", e);
            throw new BizException(BasicCode.CUSTOM_ERROR, "日期转化出错");
        }
        return time;
    }

    /**
     * 返回指定的日期格式
     * @param date
     * @return 2020-12-11
     */
    public static String getDateFormat(Date date){
        SimpleDateFormat sdf = new SimpleDateFormat(YYYY_MM_DD);
        return sdf.format(date);
    }
    
    /***
     * 获取当天开始时间
     * @return
     */
    public static String getStartTime() {
		return DateUtil.format(DateUtil.beginOfDay(new Date()),YYYY_MM_DD_HH_MM_SS);
	}
    
    /**
     * 获取当天结束时间
     * @return
     */
    public static String getEndTime() {
		return DateUtil.format(DateUtil.endOfDay(new Date()),YYYY_MM_DD_HH_MM_SS);
	}
    
    /**
     * 获取当月开始时间戳
     *
     * @return
     */
    public static String getMonthStartTime() {

        return DateUtil.format(DateUtil.beginOfMonth(new Date()),YYYY_MM_DD_HH_MM_SS);
    }
 
    /**
     * 获取当月的结束时间戳
     *
     * @return
     */
    public static String getMonthEndTime() {
        return DateUtil.format(DateUtil.endOfMonth(new Date()),YYYY_MM_DD_HH_MM_SS);
    }

    /**
     * 返回指定的日期格式
     * @param date
     * @return 20201211152323
     */
    public static String getYyyyMmDdHhMmSs(Date date){
        SimpleDateFormat sdf = new SimpleDateFormat(YYYYMMDDHHMMSS);
        return sdf.format(date);
    }

    /**
     * 返回指定的日期格式
     * @param date
     * @return 2020-12-11 15:23:23.210
     */
    public static String getYyyyMmDdHhMmSsSSS(Date date){
        SimpleDateFormat sdf = new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS_SSS);
        return sdf.format(date);
    }

    /**
     * 返回标准指定的日期格式
     * @param date
     * @return 2020-12-11 15:23:23
     */
    public static String getStandardTime(Date date){
        SimpleDateFormat sdf = new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS);
        return sdf.format(date);
    }

    public static Date getFormatDate(String dateStr) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat(YYYY_MM_DD);
        Date date = sdf.parse(dateStr);
        return date;
    }

    public static String getHHMMSSFormat(Date date) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat(HH_MM_SS);
        return sdf.format(date);
    }

}
