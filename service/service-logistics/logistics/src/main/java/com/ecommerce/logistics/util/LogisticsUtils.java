package com.ecommerce.logistics.util;

import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.logistics.api.enums.DeliveryTimeRangeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.BooleanUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Auther: <EMAIL>
 * @Date: 25/03/2019 19:30
 * @Description: LogisticsUtils
 */
@Slf4j
public class LogisticsUtils {

    private LogisticsUtils(){
        throw new IllegalStateException("LogisticsUtils class");
    }

    /**
     * 可接受误差
     */
    private static double ACCEPTABLE_ERROR = 0.001;

    /**
     * 经纬度之间判断的误差
     */
    private static double LON_LAT_ERROR = 0.000001;

    /**
     * 高德坐标测距使用的地球半径
     */
    private static double EARTH_R_FOR_DISTANCE = 6378137.0D;

    /**
     * list获取元素的属性求和
     * @param itemList
     * @param sumGetter
     * @param <T>
     * @return
     */
    public static  <T> BigDecimal sumQuantity(List<T> itemList, Function<T, BigDecimal> sumGetter) {
        return itemList.stream().map(sumGetter).reduce(LogisticsUtils::add).orElse(BigDecimal.ZERO);
    }

    public static Integer add(Integer... nums) {
        if (nums == null || nums.length == 0) {
            return 0;
        }
        int res = 0;
        for (Integer num : nums) {
            if (num == null) {
                continue;
            }
            res += num;
        }
        return res;
    }

    /**
     * 两个数是否相同
     * @param one
     * @param two
     * @param <T>
     * @return
     */
    public static <T> boolean isSame(Comparable<T> one, T two) {
        if (one == null || two == null) {
            return one == null && two == null;
        }
        return one.compareTo(two) == 0;
    }

    public static <T> int compareTo(Comparable<T> one, T two) {
        if (one == null && two == null) {
            return 0;
        }
        if (one == null) {
            return -1;
        }
        if (two == null) {
            return 1;
        }
        return one.compareTo(two);

    }

    /**
     * 加(空默认为0)
     * @param items
     * @return
     */
    public static BigDecimal add(BigDecimal... items) {
        BigDecimal result = BigDecimal.ZERO;
        if (items.length > 0) {
            for (BigDecimal item : items) {
                if (item != null) {
                    result = result.add(item);
                }
            }
        }

        return result;
    }

    /**
     * 减(空默认为0)
     * @param subs
     * @return
     */
    public static BigDecimal subtract(BigDecimal... subs) {

        if (subs == null || subs.length == 0) {
            return BigDecimal.ZERO;
        }

        BigDecimal result = BigDecimal.ZERO;

        if (subs[0] != null) {
            result = subs[0];
        }

        for (int i= 1; i < subs.length; i++) {
            BigDecimal sub = subs[i];
            if (sub != null) {
                result = result.subtract(sub);
            }
        }

        return result;
    }

    public static Date deliveryTimeStart(Date deliveryTime, DeliveryTimeRangeEnum deliveryTimeRangeEnum) {
        if (deliveryTimeRangeEnum == null) {
            return deliveryTime;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String dt = sdf.format(deliveryTime);
        SimpleDateFormat sdfTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        dt += " " + deliveryTimeRangeEnum.getDefaultTime();
        try {
            return sdfTime.parse(dt);
        } catch (ParseException e) {
            log.error("配送时间区间开始,时间处理失败:{},{}", deliveryTime, deliveryTimeRangeEnum, e);
            return deliveryTime;
        }
    }

    public static Date deliveryTimeEnd(Date deliveryTime, DeliveryTimeRangeEnum deliveryTimeRangeEnum) {
        if(Objects.isNull(deliveryTimeRangeEnum))
            return deliveryTime;
        Date startTime = deliveryTimeStart(deliveryTime, deliveryTimeRangeEnum);
        long time = startTime.getTime();
        if (deliveryTimeRangeEnum == DeliveryTimeRangeEnum.WHOLE_DAY) {
            return new Date(time + 86400000L);
        } else if (CsStringUtils.startsWith(deliveryTimeRangeEnum.name(), "TT")) {
            return new Date(time + 1800000L);
        } else {
            return new Date(time + 21600000L);
        }
    }

    /**
     * 分析配送时间区间
     * @param deliveryTimeStart
     * @param deliveryTimeEnd
     * @return
     */
    public static DeliveryTimeRangeEnum analystRange(Date deliveryTimeStart, Date deliveryTimeEnd) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");
            String defaultTime = sdf.format(sdf);
            if (CsStringUtils.equals(defaultTime, "00:00:00")) {
                long deltaTime = deliveryTimeEnd.getTime() - deliveryTimeStart.getTime();
                if (deltaTime >= 86400000L) {
                    return DeliveryTimeRangeEnum.WHOLE_DAY;
                } else if (deltaTime <= 1800000L) {
                    return DeliveryTimeRangeEnum.TTQ030020101;
                } else {
                    return DeliveryTimeRangeEnum.WEE_HOURS;
                }
            } else {
                for (DeliveryTimeRangeEnum deliveryTimeRangeEnum : DeliveryTimeRangeEnum.values()) {
                    if (CsStringUtils.equals(defaultTime, deliveryTimeRangeEnum.getDefaultTime())) {
                        return deliveryTimeRangeEnum;
                    }
                }
            }
        } catch (Exception e) {
            log.error("时间区间分析有误:{}-{}", deliveryTimeStart, deliveryTimeStart);
        }
        return DeliveryTimeRangeEnum.WHOLE_DAY;
    }

    public static String decimalStrWithDefault(BigDecimal one) {
        if (one == null) {
            return "-";
        }
        return one.setScale(2, RoundingMode.HALF_UP).toString();
    }

    public static boolean isGtZero(BigDecimal number) {
        return number != null && number.compareTo(BigDecimal.ZERO) > 0;
    }

    /**
     * 获取字符串的开头字母
     * @param source
     * @return
     */
    public static String findStartAlphabet(String source) {
        return findByPattern(source, "^[a-zA-Z]*");
    }

    /**
     * 获取字符串的开头数字
     * @param source
     * @return
     */
    public static String findStartNumeric(String source) {
        return findByPattern(source, "^[0-9]*");
    }

    /**
     * 正则表达式获取字符串
     * @param source
     * @param patternStr
     * @return
     */
    public static String findByPattern(String source, String patternStr) {
        if (CsStringUtils.isBlank(source) || CsStringUtils.isBlank(patternStr)) {
            return "";
        }

        Pattern pattern = Pattern.compile(patternStr);

        Matcher matcher = pattern.matcher(source);
        if (matcher.find()) {
            return matcher.group();
        }
        return "";
    }

    /**
     * 数字转boolean
     * @param flag
     * @return
     */
    public static boolean flagParse(Number flag) {
        return flag != null && flag.intValue() > 0;
    }

    /**
     * boolean 转 byte
     * @param flag
     * @return
     */
    public static byte flagParse(Boolean flag) {
        if (flag == null || !flag) {
            return 0;
        }
        return 1;
    }

    /**
     * 日期调整
     * @param date
     * @param deltaMS
     * @return
     */
    public static Date dateAdjust(Date date, long deltaMS) {
        return new Date(date.getTime() + deltaMS);
    }

    /**
     * 两点之间千米距离
     * @param startLoc
     * @param endLoc
     * @return
     */
    public static BigDecimal kmDistance(String startLoc, String endLoc) {
        if (!CsStringUtils.contains(startLoc, ",") || !CsStringUtils.contains(endLoc, ",")) {
            return BigDecimal.ZERO;
        }
        double dis = 0;
        try {
            String[] startXY = startLoc.trim().split(",");
            String[] endXY = endLoc.trim().split(",");
            dis = LogisticsUtils.distance(
                    Double.parseDouble(startXY[0]), Double.parseDouble(startXY[1]),
                    Double.parseDouble(endXY[0]), Double.parseDouble(endXY[1])
            );
        } catch (Exception e) {
            log.error("距离计算发生异常:{}-{}", startLoc, endLoc, e);
        }
        return BigDecimal.valueOf(dis/ 1000).setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 两坐标之间的距离
     * @param startLoc
     * @param endLoc
     * @return
     */
    public static BigDecimal distance(String startLoc, String endLoc) {
        if (!CsStringUtils.contains(startLoc, ",") || !CsStringUtils.contains(endLoc, ",")) {
            return BigDecimal.ZERO;
        }
        double dis = 0;
        try {
            String[] startXY = startLoc.trim().split(",");
            String[] endXY = endLoc.trim().split(",");
            dis = LogisticsUtils.distance(
                    Double.parseDouble(startXY[0]), Double.parseDouble(startXY[1]),
                    Double.parseDouble(endXY[0]), Double.parseDouble(endXY[1])
            );
        } catch (Exception e) {
            log.error("距离计算发生异常:{}-{}", startLoc, endLoc, e);
        }

        return BigDecimal.valueOf(dis).setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 两坐标之间的距离
     * @param startLon
     * @param startLat
     * @param endLon
     * @param endLat
     * @return
     */
    public static double distance(double startLon, double startLat, double endLon, double endLat) {
        if (Math.abs(startLat-endLat) <= LON_LAT_ERROR && Math.abs(startLon-endLon) <= LON_LAT_ERROR) {
            return 0;
        }
        double radLat1 = LogisticsUtils.radian(startLat);
        double radLat2 = LogisticsUtils.radian(endLat);
        double a = radLat1 - radLat2;
        double b = LogisticsUtils.radian(startLon) - LogisticsUtils.radian(endLon);
        double s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a/2),2) +
                Math.cos(radLat1)*Math.cos(radLat2)*Math.pow(Math.sin(b/2),2)));
        s = s * EARTH_R_FOR_DISTANCE;
        s = Math.round(s * 10000) / 10000d;
        return s;
    }

    /**
     * 角度转弧度
     * @param angle
     * @return
     */
    public static double radian(double angle) {
        return angle * Math.PI / 180.0d;
    }

    /**
     * 弧度转角度
     * @param radian
     * @return
     */
    public static double angle(double radian) {
        return radian * 180.0d / Math.PI;
    }

    /**
     * 根据对象属性去重
     * @param keyExtractor
     * @param <T>
     * @return
     */
    public static <T> Predicate<T> distinctByKey(Function<? super T, Object> keyExtractor) {
        Map<Object, Boolean> map = new ConcurrentHashMap<>();
        return t -> map.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }

    public static <T> T defaultIfNull(T value,T defaultValue) {
        return Objects.isNull(value) ? defaultValue : value;
    }

    public static <T> T defaultIfCondition(Boolean condition, T valueByTrue,T valueByFalse) {
        return BooleanUtils.isTrue(condition) ? valueByTrue : valueByFalse;
    }

    public static String defaultEmptyIfNull(String value){
        return Objects.isNull(value) ? "" : value;
    }

    public static Integer defaultAtLeastIfNull(Integer value,Integer leastValue){
        return Objects.isNull(value) || value.compareTo(leastValue) < 0? leastValue : value;
    }
}
