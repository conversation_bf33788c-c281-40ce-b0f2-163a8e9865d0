package com.ecommerce.logistics.util;

import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.greenpineyu.fel.FelEngine;
import com.greenpineyu.fel.FelEngineImpl;
import com.greenpineyu.fel.context.FelContext;
import lombok.extern.slf4j.Slf4j;

import java.beans.BeanInfo;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * Author: <EMAIL>
 * Description:
 * Date: Create in 下午3:11 19/1/7
 */
@Slf4j
public class RuleComputeUtils {

    public static BigDecimal expressionCompute(String exp, ComputeParamDTO computeParamDTO) {
        FelEngine fel = new FelEngineImpl();
        FelContext ctx = fel.getContext();
        Map<String, Object> computeParamMap = objectToMap(computeParamDTO);
        if(Objects.isNull(computeParamMap)) {
            throw new BizException(BasicCode.UNKNOWN_ERROR, "computeParamDTO is null");
        }
        for (Map.Entry<String, Object> entry : computeParamMap.entrySet()) {
            ctx.set(entry.getKey(), entry.getValue());
        }
        Object result = fel.eval(exp);

        return new BigDecimal(result.toString());
    }

    public static boolean validExpression(String exp) {
        FelEngine fel = new FelEngineImpl();
        FelContext ctx = fel.getContext();
        ctx.set("x", 1);
        try {
            fel.eval(exp);
        } catch (Exception e) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    public static void main(String[] args) {
        String exp = "3*x + 14.09";
        ComputeParamDTO computeParamDTO = new ComputeParamDTO();
        computeParamDTO.setX(new BigDecimal(10));
        log.info("{}",expressionCompute(exp, computeParamDTO));
        validExpression("3*x+ 2");
    }

    /**
     * 对象转换为map
     * @param obj 转换对象
     * @return  Map
     */
    public static Map<String, Object> objectToMap(Object obj) {
        if (obj == null) {
            return null;
        }
        Map<String, Object> map = new HashMap<>();
        try {
            BeanInfo beanInfo = Introspector.getBeanInfo(obj.getClass());
            PropertyDescriptor[] propertyDescriptors = beanInfo.getPropertyDescriptors();
            for (PropertyDescriptor property : propertyDescriptors) {
                String key = property.getName();
                if (key.compareToIgnoreCase("class") == 0) {
                    continue;
                }
                Method getter = property.getReadMethod();
                Object value = getter != null ? getter.invoke(obj) : null;
                if (value == null) {
                    value = "";
                }
                map.put(key, value);
            }
        } catch (Exception e) {
            throw new BizException(BasicCode.UNKNOWN_ERROR, "规则计算数据对象转换异常");
        }
        return map;
    }
}
