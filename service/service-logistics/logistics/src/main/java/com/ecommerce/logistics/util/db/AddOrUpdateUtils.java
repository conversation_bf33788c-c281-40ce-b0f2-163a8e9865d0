package com.ecommerce.logistics.util.db;

import cn.hutool.core.bean.BeanUtil;
import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.api.dto.attachment.AttAddDTO;
import com.ecommerce.logistics.api.dto.attachment.AttListAddDTO;
import com.ecommerce.logistics.api.dto.attachment.AttRemoveDTO;
import com.ecommerce.logistics.biz.impl.AttachmentBizService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * Copyright (C),2020
 *
 * @className: AddOrUpdateUtils
 * @author: <EMAIL>
 * @Date: 2020/9/28 12:08 上午
 * @Description:
 */
@Slf4j
public class AddOrUpdateUtils {

    private AddOrUpdateUtils() {
        throw new IllegalStateException("AddOrUpdateUtils class");
    }

    /**
     * 新增或编辑数据库实体
     *
     * @param targetDO      数据库实体
     * @param baseMapper    数据库表操作mapper
     * @param pkIdSetter    数据库实体主键ID set方法
     * @param pkIdMaker     数据库实体主键ID生成方法
     * @param pkIdGetter    数据库实体主键ID get方法
     * @param operateUserId 操作人ID
     * @param pkNumSetter   数据库实体主键编号 set方法
     * @param pkNumMaker    数据库实体主键编号生成方法
     * @param <T>          数据库实体类型
     * @return 数据库实体
     */
    public static <T> T addOrUpdate(T targetDO,
                                      IBaseMapper<T> baseMapper,
                                      BiConsumer<T, String> pkIdSetter,
                                      Supplier<String> pkIdMaker,
                                      Function<T, String> pkIdGetter,
                                      String operateUserId,
                                      BiConsumer<T, String> pkNumSetter,
                                      Supplier<String> pkNumMaker

    ) {
        return addOrUpdateWithAtt(targetDO, baseMapper, pkIdSetter, pkIdMaker, pkIdGetter, operateUserId,
                pkNumSetter, pkNumMaker, null, null);
    }


    /**
     * 新增或编辑数据库实体(包含附件列表)
     *
     * @param targetDO             数据库实体
     * @param baseMapper           数据库表操作mapper
     * @param pkIdSetter           数据库实体主键ID set方法
     * @param pkIdMaker            数据库实体主键ID生成方法
     * @param pkIdGetter           数据库实体主键ID get方法
     * @param operateUserId        操作人ID
     * @param pkNumSetter          数据库实体主键编号 set方法
     * @param pkNumMaker           数据库实体主键编号生成方法
     * @param attList              附件列表
     * @param attachmentBizService 附件操作方法
     * @param <T>                 数据库实体类型
     * @return 数据库实体
     */
    public static <T> T addOrUpdateWithAtt(T targetDO,
                                             IBaseMapper<T> baseMapper,
                                             BiConsumer<T, String> pkIdSetter,
                                             Supplier<String> pkIdMaker,
                                             Function<T, String> pkIdGetter,
                                             String operateUserId,
                                             BiConsumer<T, String> pkNumSetter,
                                             Supplier<String> pkNumMaker,
                                             List<AttAddDTO> attList,
                                             AttachmentBizService attachmentBizService
    ) {

        Objects.requireNonNull(targetDO);
        Objects.requireNonNull(baseMapper);
        Objects.requireNonNull(pkIdSetter);
        Objects.requireNonNull(pkIdMaker);
        Objects.requireNonNull(pkIdGetter);


        String pkId = pkIdGetter.apply(targetDO);

        if (Objects.isNull(pkId)) {
            String pkIdInsert = pkIdMaker.get();
            pkIdSetter.accept(targetDO, pkIdInsert);
            pkNumSetter.accept(targetDO, pkNumMaker.get());

            //web层只传递操作人
            try {
                BeanUtil.setProperty(targetDO, "createUser", operateUserId == null ? "" :
                        operateUserId);
                BeanUtil.setProperty(targetDO, "createTime", new Date());
                BeanUtil.setProperty(targetDO, "updateUser", operateUserId == null ? "" :
                        operateUserId);
                BeanUtil.setProperty(targetDO, "delFlg", 0);
                BeanUtil.setProperty(targetDO, "version", 0);
            } catch (Exception e) {
                log.error("addOrUpdateWithAtt数据拷贝异常");
            }

            baseMapper.insertSelective(targetDO);

            if (CollectionUtils.isNotEmpty(attList)) {
                attList.forEach(item -> {
                    item.setEntryId(pkIdInsert);
                    item.setCreateUser(operateUserId);
                });
                AttListAddDTO attListAddDTO = new AttListAddDTO();
                attListAddDTO.setAttAddListDTO(attList);
                attachmentBizService.saveAttList(attListAddDTO);
            }


        } else {

            handleUpdateUser(targetDO, operateUserId);

            baseMapper.updateByPrimaryKeySelective(targetDO);

            handleAttList(operateUserId, attList, attachmentBizService, pkId);


        }

        return targetDO;

    }

    private static void handleAttList(String operateUserId, 
                                      List<AttAddDTO> attList, 
                                      AttachmentBizService attachmentBizService, 
                                      String pkId) {
        if (CollectionUtils.isNotEmpty(attList)) {
            AttRemoveDTO attRemoveDTO = new AttRemoveDTO();
            attRemoveDTO.setEntryId(pkId);
            attRemoveDTO.setUpdateUser(operateUserId);
            attachmentBizService.deleteAtt(attRemoveDTO);

            attList.forEach(item -> {
                item.setEntryId(pkId);
                item.setCreateUser(operateUserId);
            });
            AttListAddDTO attListAddDTO = new AttListAddDTO();
            attListAddDTO.setAttAddListDTO(attList);
            attachmentBizService.saveAttList(attListAddDTO);
        }
    }

    private static <T> void handleUpdateUser(T targetDO, String operateUserId) {
        try {
            BeanUtil.setProperty(targetDO, "updateUser", operateUserId == null ? "" :
                    operateUserId);
        } catch (Exception e) {
            log.error("addOrUpdateWithAtt数据拷贝异常");
        }
    }
}
