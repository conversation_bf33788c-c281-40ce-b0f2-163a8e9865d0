package com.ecommerce.logistics.util.db;

import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Supplier;


public class DoTransUtils {

    private DoTransUtils(){
        throw new IllegalStateException("DoTransUtils class");
    }

    /**
     * 单个实例转换
     *
     * @param s        源实例
     * @param supplier 转换后实例生成器
     * @param <S>      源实例泛型
     * @param <R>      转换后实例泛型
     * @return 转换后实例
     */
    public static <S, R> S singleTransform(R s, Supplier<S> supplier) {
        Objects.requireNonNull(s);
        Objects.requireNonNull(supplier);
        S target = supplier.get();
        BeanUtils.copyProperties(s, target);
        return target;
    }


    /**
     * 批量实例转换
     *
     * @param dos      源实例集合
     * @param supplier 转换后实例生成器
     * @param <S>      源实例泛型
     * @param <R>      转换后实例泛型
     * @return 转换后实例
     */
    public static <S, R> List<R> batchTransform(Collection<S> dos, Supplier<R> supplier) {
        if (CollectionUtils.isEmpty(dos)) {
            return Collections.emptyList();
        }
        Objects.requireNonNull(supplier);
        ArrayList<R> result = new ArrayList<>();
        dos.stream().forEach(item -> {
            R target = singleTransform(item, supplier);
            result.add(target);
        });

        return result;
    }
}
