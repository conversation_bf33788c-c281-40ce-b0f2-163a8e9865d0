package com.ecommerce.logistics.util.db;



import com.ecommerce.common.exception.BizException;import com.ecommerce.common.exception.BasicCode;import lombok.extern.slf4j.Slf4j;
import com.ecommerce.common.utils.CsStringUtils;
import org.apache.ibatis.reflection.property.PropertyNamer;

import java.lang.invoke.SerializedLambda;
import java.lang.reflect.Method;
import java.util.Objects;

/**
 * Copyright (C),2020
 *
 * @className: FieldUtils
 * @author: <EMAIL>
 * @Date: 2020/10/15 3:24 下午
 * @Description:
 */
@Slf4j
public class FieldUtils {

    private FieldUtils(){
        throw new IllegalStateException("FieldUtils class");
    }


    /**
     * 通过get,set方法的函数式表达式获取对应的字段名称
     *
     * @param funcLambada get,set方法的函数式表达式
     * @param <T>         get,set方法的函数式表达式的入参类型
     * @param <R>         get,set方法的函数式表达式的返回值类型
     * @return get, set方法对应的字段名称
     * @throws Exception 异常信息
     */
    public static <T, R> String methodFuncToFieldName(GxFunction<T, R> funcLambada) {
        try {
            Objects.requireNonNull(funcLambada);
            SerializedLambda serializedLambda = getSerializedLambda(funcLambada);
            return PropertyNamer.methodToProperty(serializedLambda.getImplMethodName());
        } catch (Exception e) {
            log.error("methodFuncToFieldName 异常");
            return CsStringUtils.EMPTY;
        }

    }


    /**
     * 通过get,set方法的函数式表达式获取SerializedLambda
     *
     * @param func get,set方法的函数式表达式
     * @param <T>  get,set方法的函数式表达式的入参类型
     * @param <R>  get,set方法的函数式表达式的返回值类型
     * @return SerializedLambda
     * @throws Exception 反射获取 SerializedLambda 异常信息
     */
    private static <T, R> java.lang.invoke.SerializedLambda getSerializedLambda(GxFunction<T, R> func) throws Exception {
        // 直接调用writeReplace
        Method writeReplace = func.getClass().getDeclaredMethod("writeReplace");
        writeReplace.setAccessible(true);
        //反射调用
        Object sl = writeReplace.invoke(func);
        java.lang.invoke.SerializedLambda serializedLambda = (java.lang.invoke.SerializedLambda) sl;
        return serializedLambda;
    }

}
