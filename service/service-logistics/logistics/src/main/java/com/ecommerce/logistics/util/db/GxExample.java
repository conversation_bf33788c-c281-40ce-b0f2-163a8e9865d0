package com.ecommerce.logistics.util.db;

import lombok.extern.slf4j.Slf4j;
import com.ecommerce.common.utils.CsStringUtils;
import tk.mybatis.mapper.entity.EntityColumn;
import tk.mybatis.mapper.entity.Example;

import java.util.Map;
import java.util.Objects;

/**
 * Copyright (C),2020
 *
 * @className: GxExample
 * @author: <EMAIL>
 * @Date: 2020/10/15 4:32 下午
 * @Description:
 */
@Slf4j
public class GxExample extends Example {

    private GxCriteria gxCriteria;

    public GxExample(Class<?> entityClass) {
        super(entityClass);
    }

    public GxExample(Class<?> entityClass, boolean exists) {
        super(entityClass, exists);
    }

    public GxExample(Class<?> entityClass, boolean exists, boolean notNull) {
        super(entityClass, exists, notNull);
    }

    public <T, R> OrderBy orderBy(GxFunction<T, R> function) {
        Objects.requireNonNull(function);
        String fieldName = FieldUtils.methodFuncToFieldName(function);
        if (CsStringUtils.isNotEmpty(fieldName)) {
            this.ORDERBY.orderBy(fieldName);
        }
        return this.ORDERBY;
    }


    @Override
    public GxCriteria createCriteria() {
        GxCriteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            criteria.setAndOr("and");
            oredCriteria.add(criteria);
        }
        return criteria;
    }


    @Override
    protected GxCriteria createCriteriaInternal() {
        GxCriteria criteria = new GxCriteria(propertyMap, exists, notNull);
        return criteria;
    }

    public GxCriteria getGxCriteria() {
        return gxCriteria;
    }

    public void setGxCriteria(GxCriteria gxCriteria) {
        this.gxCriteria = gxCriteria;
    }


    /**
     * 基于tkmapper Criteria的封装
     */
    public static class GxCriteria extends Criteria {

        protected GxCriteria(Map<String, EntityColumn> propertyMap, boolean exists, boolean notNull) {
            super(propertyMap, exists, notNull);
        }


        public <T, R> GxCriteria andIsNull(GxFunction<T, R> function) {
            andIsNull(true, function);
            return this;
        }

        public <T, R> GxCriteria andIsNull(boolean checkParam, GxFunction<T, R> function
        ) {
            String fieldName = FieldUtils.methodFuncToFieldName(function);
            if (checkParam && CsStringUtils.isNotEmpty(fieldName)) {
                super.andIsNull(FieldUtils.methodFuncToFieldName(function));
            }
            return this;
        }


        public <T, R> GxCriteria andIsNotNull(GxFunction<T, R> function, Object value) {
            log.info("value:{}",value);
            andIsNotNull(true, function);
            return this;
        }

        public <T, R> GxCriteria andIsNotNull(boolean checkParam, GxFunction<T, R> function) {
            String fieldName = FieldUtils.methodFuncToFieldName(function);
            if (checkParam && CsStringUtils.isNotEmpty(fieldName)) {
                super.andIsNotNull(FieldUtils.methodFuncToFieldName(function));
            }
            return this;
        }


        public <T, R> GxCriteria andEqualTo(GxFunction<T, R> function, Object value) {
            andEqualTo(true, function, value);
            return this;
        }

        public <T, R> GxCriteria andEqualTo(boolean checkParam, GxFunction<T, R> function,
                                            Object value) {
            String fieldName = FieldUtils.methodFuncToFieldName(function);
            if (checkParam && CsStringUtils.isNotEmpty(fieldName)) {
                super.andEqualTo(FieldUtils.methodFuncToFieldName(function), value);
            }
            return this;
        }


        public <T, R> GxCriteria andNotEqualTo(GxFunction<T, R> function, Object value) {
            andNotEqualTo(true, function, value);
            return this;
        }

        public <T, R> GxCriteria andNotEqualTo(boolean checkParam, GxFunction<T, R> function,
                                               Object value) {
            String fieldName = FieldUtils.methodFuncToFieldName(function);
            if (checkParam && CsStringUtils.isNotEmpty(fieldName)) {
                super.andNotEqualTo(FieldUtils.methodFuncToFieldName(function), value);
            }
            return this;
        }


        public <T, R> GxCriteria andGreaterThan(GxFunction<T, R> function, Object value) {
            andGreaterThan(true, function, value);
            return this;
        }

        public <T, R> GxCriteria andGreaterThan(boolean checkParam, GxFunction<T, R> function,
                                                Object value) {
            String fieldName = FieldUtils.methodFuncToFieldName(function);
            if (checkParam && CsStringUtils.isNotEmpty(fieldName)) {
                super.andGreaterThan(FieldUtils.methodFuncToFieldName(function), value);
            }
            return this;
        }

        public <T, R> GxCriteria andGreaterThanOrEqualTo(GxFunction<T, R> function, Object value) {
            andGreaterThanOrEqualTo(true, function, value);
            return this;
        }

        public <T, R> GxCriteria andGreaterThanOrEqualTo(boolean checkParam, GxFunction<T, R> function,
                                                         Object value) {
            String fieldName = FieldUtils.methodFuncToFieldName(function);
            if (checkParam && CsStringUtils.isNotEmpty(fieldName)) {
                super.andGreaterThanOrEqualTo(FieldUtils.methodFuncToFieldName(function), value);
            }
            return this;
        }


        public <T, R> GxCriteria andLessThan(GxFunction<T, R> function, Object value) {
            andLessThan(true, function, value);
            return this;
        }

        public <T, R> GxCriteria andLessThan(boolean checkParam, GxFunction<T, R> function,
                                             Object value) {
            String fieldName = FieldUtils.methodFuncToFieldName(function);
            if (checkParam && CsStringUtils.isNotEmpty(fieldName)) {
                super.andLessThan(FieldUtils.methodFuncToFieldName(function), value);
            }
            return this;
        }


        public <T, R> GxCriteria andLessThanOrEqualTo(GxFunction<T, R> function, Object value) {
            andLessThanOrEqualTo(true, function, value);
            return this;
        }

        public <T, R> GxCriteria andLessThanOrEqualTo(boolean checkParam, GxFunction<T, R> function,
                                                      Object value) {
            String fieldName = FieldUtils.methodFuncToFieldName(function);
            if (checkParam && CsStringUtils.isNotEmpty(fieldName)) {
                super.andLessThanOrEqualTo(FieldUtils.methodFuncToFieldName(function), value);
            }
            return this;
        }

        public <T, R> GxCriteria andIn(GxFunction<T, R> function, Iterable<?> values) {
            andIn(true, function, values);
            return this;
        }

        public <T, R> GxCriteria andIn(boolean checkParam, GxFunction<T, R> function,
                                       Iterable<?> values) {
            String fieldName = FieldUtils.methodFuncToFieldName(function);
            if (checkParam && CsStringUtils.isNotEmpty(fieldName)) {
                super.andIn(FieldUtils.methodFuncToFieldName(function), values);
            }
            return this;
        }

        public <T, R> GxCriteria andNotIn(GxFunction<T, R> function, Iterable<?> values) {
            andNotIn(true, function, values);
            return this;
        }

        public <T, R> GxCriteria andNotIn(boolean checkParam, GxFunction<T, R> function,
                                          Iterable<?> values) {
            String fieldName = FieldUtils.methodFuncToFieldName(function);
            if (checkParam && CsStringUtils.isNotEmpty(fieldName)) {
                super.andNotIn(FieldUtils.methodFuncToFieldName(function), values);
            }
            return this;
        }


        public <T, R> GxCriteria andNotBetween(GxFunction<T, R> function, Object value1, Object value2) {
            andNotBetween(true, function, value1, value2);
            return this;
        }

        public <T, R> GxCriteria andNotBetween(boolean checkParam, GxFunction<T, R> function,
                                               Object value1, Object value2) {
            String fieldName = FieldUtils.methodFuncToFieldName(function);
            if (checkParam && CsStringUtils.isNotEmpty(fieldName)) {
                super.andNotBetween(FieldUtils.methodFuncToFieldName(function), value1, value2);
            }
            return this;
        }


        public <T, R> GxCriteria andBetween(GxFunction<T, R> function, Object value1, Object value2) {
            andBetween(true, function, value1, value2);
            return this;
        }

        public <T, R> GxCriteria andBetween(boolean checkParam, GxFunction<T, R> function,
                                            Object value1, Object value2) {
            String fieldName = FieldUtils.methodFuncToFieldName(function);
            if (checkParam && CsStringUtils.isNotEmpty(fieldName)) {
                super.andBetween(FieldUtils.methodFuncToFieldName(function), value1, value2);
            }
            return this;
        }

        public <T, R> GxCriteria andLike(GxFunction<T, R> function, String value) {
            andLike(true, function, value);
            return this;
        }

        public <T, R> GxCriteria andLike(boolean checkParam, GxFunction<T, R> function,
                                         String value) {
            String fieldName = FieldUtils.methodFuncToFieldName(function);
            if (checkParam && CsStringUtils.isNotEmpty(fieldName)) {
                super.andLike(fieldName, value);
            }
            return this;
        }


        public <T, R> GxCriteria andNotLike(GxFunction<T, R> function, String value) {
            andNotLike(true, function, value);
            return this;
        }

        public <T, R> GxCriteria andNotLike(boolean checkParam, GxFunction<T, R> function,
                                            String value) {
            String fieldName = FieldUtils.methodFuncToFieldName(function);
            if (checkParam && CsStringUtils.isNotEmpty(fieldName)) {
                super.andNotLike(fieldName, value);
            }
            return this;
        }


        @Override
        public GxCriteria andEqualTo(Object param) {
            andEqualTo(true, param);
            return this;
        }

        public GxCriteria andEqualTo(boolean checkParam,
                                     Object param) {
            if (checkParam) {
                super.andEqualTo(param);
            }
            return this;
        }


        @Override
        public GxCriteria andAllEqualTo(Object param) {
            andAllEqualTo(true, param);
            return this;
        }

        public GxCriteria andAllEqualTo(boolean checkParam,
                                        Object param) {
            if (checkParam) {
                super.andAllEqualTo(param);
            }
            return this;
        }


        public <T, R> GxCriteria orIsNull(GxFunction<T, R> function) {
            orIsNull(true, function);
            return this;
        }

        public <T, R> GxCriteria orIsNull(boolean checkParam, GxFunction<T, R> function) {
            String fieldName = FieldUtils.methodFuncToFieldName(function);
            if (checkParam && CsStringUtils.isNotEmpty(fieldName)) {
                super.orIsNull(fieldName);
            }
            return this;
        }


        public <T, R> GxCriteria orIsNotNull(boolean checkParam, GxFunction<T, R> function) {
            String fieldName = FieldUtils.methodFuncToFieldName(function);
            if (checkParam && CsStringUtils.isNotEmpty(fieldName)) {
                super.orIsNotNull(fieldName);
            }
            return this;
        }


        public <T, R> GxCriteria orIsNotNull(GxFunction<T, R> function) {
            orIsNotNull(true, function);
            return this;
        }


        public <T, R> GxCriteria orEqualTo(GxFunction<T, R> function, Object value) {
            orEqualTo(true, function, value);
            return this;
        }

        public <T, R> GxCriteria orEqualTo(boolean checkParam, GxFunction<T, R> function,
                                           Object value) {
            String fieldName = FieldUtils.methodFuncToFieldName(function);
            if (checkParam && CsStringUtils.isNotEmpty(fieldName)) {
                super.orEqualTo(fieldName, value);
            }
            return this;
        }

        public <T, R> GxCriteria orNotEqualTo(GxFunction<T, R> function, Object value) {
            orNotEqualTo(true, function, value);
            return this;
        }

        public <T, R> GxCriteria orNotEqualTo(boolean checkParam, GxFunction<T, R> function,
                                              Object value) {
            String fieldName = FieldUtils.methodFuncToFieldName(function);
            if (checkParam && CsStringUtils.isNotEmpty(fieldName)) {
                super.orNotEqualTo(fieldName, value);
            }
            return this;
        }


        public <T, R> GxCriteria orGreaterThan(GxFunction<T, R> function, Object value) {
            orGreaterThan(true, function, value);
            return this;
        }

        public <T, R> GxCriteria orGreaterThan(boolean checkParam, GxFunction<T, R> function,
                                               Object value) {
            String fieldName = FieldUtils.methodFuncToFieldName(function);
            if (checkParam && CsStringUtils.isNotEmpty(fieldName)) {
                super.orGreaterThan(fieldName, value);
            }
            return this;
        }


        public <T, R> GxCriteria orGreaterThanOrEqualTo(GxFunction<T, R> function, Object value) {
            orGreaterThanOrEqualTo(true, function, value);
            return this;
        }

        public <T, R> GxCriteria orGreaterThanOrEqualTo(boolean checkParam, GxFunction<T, R> function,
                                                        Object value) {
            String fieldName = FieldUtils.methodFuncToFieldName(function);
            if (checkParam && CsStringUtils.isNotEmpty(fieldName)) {
                super.orGreaterThanOrEqualTo(fieldName, value);
            }
            return this;
        }


        public <T, R> GxCriteria orLessThan(GxFunction<T, R> function, Object value) {
            orLessThan(true, function, value);
            return this;
        }

        public <T, R> GxCriteria orLessThan(boolean checkParam, GxFunction<T, R> function,
                                            Object value) {
            String fieldName = FieldUtils.methodFuncToFieldName(function);
            if (checkParam && CsStringUtils.isNotEmpty(fieldName)) {
                super.orLessThan(fieldName, value);
            }
            return this;
        }


        public <T, R> GxCriteria orLessThanOrEqualTo(GxFunction<T, R> function, Object value) {
            orLessThanOrEqualTo(true, function, value);
            return this;
        }

        public <T, R> GxCriteria orLessThanOrEqualTo(boolean checkParam, GxFunction<T, R> function,
                                                     Object value) {
            String fieldName = FieldUtils.methodFuncToFieldName(function);
            if (checkParam && CsStringUtils.isNotEmpty(fieldName)) {
                super.orLessThanOrEqualTo(fieldName, value);
            }
            return this;
        }


        public <T, R> GxCriteria orIn(GxFunction<T, R> function, Iterable<?> values) {
            orIn(true, function, values);
            return this;
        }

        public <T, R> GxCriteria orIn(boolean checkParam, GxFunction<T, R> function,
                                      Iterable<?> values) {
            String fieldName = FieldUtils.methodFuncToFieldName(function);
            if (checkParam && CsStringUtils.isNotEmpty(fieldName)) {
                super.orIn(fieldName, values);
            }
            return this;
        }


        public <T, R> GxCriteria orNotIn(GxFunction<T, R> function, Iterable values) {
            orNotIn(true, function, values);
            return this;
        }

        public <T, R> GxCriteria orNotIn(boolean checkParam, GxFunction<T, R> function,
                                         Iterable values) {
            String fieldName = FieldUtils.methodFuncToFieldName(function);
            if (checkParam && CsStringUtils.isNotEmpty(fieldName)) {
                super.orNotIn(fieldName, values);
            }
            return this;
        }


        public <T, R> GxCriteria orBetween(GxFunction<T, R> function, Object value1, Object value2) {
            orBetween(true, function, value1, value2);
            return this;
        }

        public <T, R> GxCriteria orBetween(boolean checkParam, GxFunction<T, R> function,
                                           Object value1, Object value2) {
            String fieldName = FieldUtils.methodFuncToFieldName(function);
            if (checkParam && CsStringUtils.isNotEmpty(fieldName)) {
                super.orBetween(fieldName, value1, value2);
            }
            return this;
        }


        public <T, R> GxCriteria orNotBetween(GxFunction<T, R> function, Object value1, Object value2) {
            orNotBetween(true, function, value1, value2);
            return this;
        }

        public <T, R> GxCriteria orNotBetween(boolean checkParam, GxFunction<T, R> function,
                                              Object value1, Object value2) {
            String fieldName = FieldUtils.methodFuncToFieldName(function);
            if (checkParam && CsStringUtils.isNotEmpty(fieldName)) {
                super.orNotBetween(fieldName, value1, value2);
            }
            return this;
        }


        public <T, R> GxCriteria orLike(GxFunction<T, R> function, String value) {
            orLike(true, function, value);
            return this;
        }

        public <T, R> GxCriteria orLike(boolean checkParam, GxFunction<T, R> function,
                                        String value) {
            String fieldName = FieldUtils.methodFuncToFieldName(function);
            if (checkParam && CsStringUtils.isNotEmpty(fieldName)) {
                super.orLike(fieldName, value);
            }
            return this;
        }


        public <T, R> GxCriteria orNotLike(GxFunction<T, R> function, String value) {
            orNotLike(true, function, value);
            return this;
        }

        public <T, R> GxCriteria orNotLike(boolean checkParam, GxFunction<T, R> function,
                                           String value) {
            String fieldName = FieldUtils.methodFuncToFieldName(function);
            if (checkParam && CsStringUtils.isNotEmpty(fieldName)) {
                super.orNotLike(fieldName, value);
            }
            return this;
        }

    }


    public static <T> GxExample buildGxExampleAndCriteria(Class<T> doClass) {
        Objects.requireNonNull(doClass);
        GxExample gxExample = new GxExample(doClass);
        GxExample.GxCriteria gxCriteria = gxExample.createCriteria();
        gxExample.setGxCriteria(gxCriteria);
        return gxExample;
    }

}
