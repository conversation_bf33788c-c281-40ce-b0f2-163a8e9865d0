package com.ecommerce.logistics.util.db;

import com.ecommerce.common.result.PageData;
import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.github.pagehelper.Page;
import com.github.pagehelper.page.PageMethod;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * Copyright (C),2020
 *
 * @className: QueryUtils
 * @author: <EMAIL>
 * @Date: 2020/9/24 5:15 下午
 * @Description: 基于通用mapper的查询工具封装
 */
public class QueryUtils {

    private QueryUtils(){
        throw new IllegalStateException("QueryUtils class");
    }

    private static final Integer DEFAULT_PAGENUM = 1;

    private static final Integer DEFAULT_PAGESIZE = 10;


    /**
     * 基于通用mapper的分页查询工具，解决重复代码过多的问题
     *
     * @param param        分页查询入参查询条件
     * @param queryMapper  查询db mapper
     * @param queryExample 查询example条件
     * @param dtoType      返回结果类型
     * @param <DTO>        返回结果类型
     * @param <DO>         数据库实体类型
     * @param <PARAM>      入参类型
     * @return 分页查询结果返回对象
     */
    public static <DTO, DO, PARAM> PageData<DTO> page(PageQuery<PARAM> param,
                                                      IBaseMapper<DO> queryMapper,
                                                      Example queryExample,
                                                      Supplier<DTO> dtoType) {
        Objects.requireNonNull(param);
        Objects.requireNonNull(queryExample);
        Objects.requireNonNull(queryMapper);
        Objects.requireNonNull(dtoType);

        Integer pageNum = param.getPageNum() == null ? DEFAULT_PAGENUM : param.getPageNum();
        Integer pageSize = param.getPageSize() == null ? DEFAULT_PAGESIZE : param.getPageSize();

        Page<DO> page = PageMethod.startPage(pageNum, pageSize);

        List<DO> result = queryMapper.selectByExample(queryExample);
        if (CollectionUtils.isEmpty(result)) {
            return new PageData<>(Collections.emptyList());
        }

        List<DTO> dtoList = result.stream().map(item -> {
            DTO dto = dtoType.get();
            BeanUtils.copyProperties(item, dto);
            // 对dto一些额外的属性进行操作
            return dto;
        }).toList();

        PageData<DTO> pageData = new PageData<>();

        pageData.setList(dtoList);
        pageData.setPageNum(page.getPageNum());
        pageData.setTotal(page.getTotal());
        pageData.setPageSize(page.getPageSize());
        pageData.setPages(page.getPages());
        return pageData;
    }
}
