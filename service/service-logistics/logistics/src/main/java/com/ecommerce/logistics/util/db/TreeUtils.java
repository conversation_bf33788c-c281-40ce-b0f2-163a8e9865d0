package com.ecommerce.logistics.util.db;

import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Copyright (C),2020
 *
 * @className: TreeUtils
 * @author: <EMAIL>
 * @Date: 2020/9/30 1:21 下午
 * @Description:
 */
public class TreeUtils {


    /**
     * 根据parentId将集合构造成树型结构
     *
     * @param sourceList     需要构造成树型结构的List
     * @param childGetter    集合中元素的子元素集合获取方法
     * @param parentIdGetter 集合中元素的parentId获取方法
     * @param pkIdGetter     集合中元素的主键id获取方法
     * @param <T>            集合中元素的类型
     * @param <PKTYPE>       集合中元素的主键类型
     * @return 生成树
     */
    public static <T, PKTYPE> List<T> buildTree(List<T> sourceList, Function<T, List<T>> childGetter,
                                                Function<T, PKTYPE> parentIdGetter,
                                                Function<T, PKTYPE> pkIdGetter) {
        Objects.requireNonNull(childGetter);
        Objects.requireNonNull(parentIdGetter);
        Objects.requireNonNull(pkIdGetter);

        if (CollectionUtils.isEmpty(sourceList)) {
            return Collections.emptyList();
        }

        List<T> treeList = new ArrayList<>();

        Map<PKTYPE, T> sourceMap = sourceList.stream().collect(Collectors.toMap(pkIdGetter, Function.identity()));

        sourceList.forEach(item -> {
            if (Objects.isNull(parentIdGetter.apply(item))) {
                treeList.add(sourceMap.get(pkIdGetter.apply(item)));
            } else {
                //如果是子节点，将自己加入到父节点中
                T parentDO = sourceMap.get(parentIdGetter.apply(item));
                List<T> childList = childGetter.apply(parentDO);
                // 目前必须保证childList已经初始化，待优化
                childList.add(item);
            }
        });

        return treeList;
    }


    @Data
    static class TreeNode {

        private Integer id;

        private Integer parentId;

        private String value;

        private List<TreeNode> treeNodeList = new ArrayList<>();
    }

    public static void main(String[] args) {

        TreeNode treeNode5 = new TreeNode();
        treeNode5.setId(5);
        treeNode5.setParentId(null);
        treeNode5.setValue("最顶层2");

        TreeNode treeNode6 = new TreeNode();
        treeNode6.setId(6);
        treeNode6.setParentId(null);
        treeNode6.setValue("最顶层3");

        TreeNode treeNode = new TreeNode();
        treeNode.setId(1);
        treeNode.setParentId(null);
        treeNode.setValue("最顶层");

        TreeNode treeNode2 = new TreeNode();
        treeNode2.setId(2);
        treeNode2.setParentId(1);
        treeNode2.setValue("第一层1");

        TreeNode treeNode3 = new TreeNode();
        treeNode3.setId(3);
        treeNode3.setParentId(1);
        treeNode3.setValue("第一层2");

        TreeNode treeNode4 = new TreeNode();
        treeNode4.setId(4);
        treeNode4.setParentId(2);
        treeNode4.setValue("第2层1");


        ArrayList<TreeNode> treeNodes = Lists.newArrayList(treeNode4, treeNode5, treeNode, treeNode2, treeNode3,
                treeNode6);
    }
}
