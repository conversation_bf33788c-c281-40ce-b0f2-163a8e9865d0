package com.ecommerce.logistics.util.generator;

import com.ecommerce.common.service.common.bsid.AbstractIBusinessIdGenerator;
import org.springframework.stereotype.Component;

/**
 * 生成辅助价目表编号服务
 * Created by <PERSON><PERSON><PERSON> on 2020/11/6 14:17
 */
@Component
public class AuxiliaryPriceBizUidGenerator extends AbstractIBusinessIdGenerator {

    /**
     * 辅助价目编号
     * @return
     */
    @Override
    public String businessCodePrefix() {
        return "FZJM" + gainDateString();
    }

}
