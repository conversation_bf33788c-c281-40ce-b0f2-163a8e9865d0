package com.ecommerce.logistics.util.generator;

import com.ecommerce.common.service.common.bsid.AbstractIBusinessIdGenerator;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * Author: lehu
 * Description:
 * Date: Create in 下午5:32 19/6/11
 */
@Component
public class CarriageRouteBizUidGenerator  extends AbstractIBusinessIdGenerator {

    private static final String YYYY_MM_MDD = "yyyyMMdd";

    @Override
    public String businessCodePrefix() {
        StringBuilder builder = new StringBuilder();
        String timeStr = new SimpleDateFormat(YYYY_MM_MDD).format(new Date());
        builder.append(timeStr);
        return builder.toString();
    }

    public String businessCode() {
        StringBuilder builder = new StringBuilder("CR");
        String timeStr = new SimpleDateFormat(YYYY_MM_MDD).format(new Date());
        builder.append(timeStr);
        builder.append(gainString());
        return builder.toString();
    }

    public String erpCertNumber() {
        StringBuilder builder = new StringBuilder("CN");
        String timeStr = new SimpleDateFormat(YYYY_MM_MDD).format(new Date());
        builder.append(timeStr);
        builder.append(gainString());
        return builder.toString();
    }
}
