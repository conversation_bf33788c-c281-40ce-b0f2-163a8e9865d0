package com.ecommerce.logistics.util.generator;

import com.ecommerce.common.service.common.bsid.AbstractIBusinessIdGenerator;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * Author: colu
 * Description:
 * Date: Create in 下午5:32 19/6/11
 */
@Component
public class DeliveryNoteBizUidGenerator extends AbstractIBusinessIdGenerator {

    @Override
    public String businessCodePrefix() {
        StringBuilder builder = new StringBuilder();
        String timeStr = new SimpleDateFormat("yyyyMMdd").format(new Date());
        builder.append(timeStr);
        return builder.toString();
    }

    public String businessCode() {
        StringBuilder builder = new StringBuilder("DN");
        String timeStr = new SimpleDateFormat("yyyyMMdd").format(new Date());
        builder.append(timeStr);
        builder.append(gainString());
        return builder.toString();
    }
}
