package com.ecommerce.logistics.util.generator;

import com.ecommerce.common.service.common.bsid.AbstractIBusinessIdGenerator;
import org.springframework.stereotype.Component;

/**
 * 生成司机合同编号服务
 * Created by <PERSON><PERSON><PERSON> on 2020/10/27 17:42
 */
@Component
public class DriverContractBizUidGenerator extends AbstractIBusinessIdGenerator {

    /**
     * 司机合同前缀编号
     * @return
     */
    @Override
    public String businessCodePrefix() {
        return "SJHT" + gainDateString();
    }
}
