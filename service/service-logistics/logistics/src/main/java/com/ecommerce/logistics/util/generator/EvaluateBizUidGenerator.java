package com.ecommerce.logistics.util.generator;

import com.ecommerce.common.service.common.bsid.AbstractIBusinessIdGenerator;
import org.springframework.stereotype.Component;

/**
 * @Author: <EMAIL>
 * @Description:
 * @Date: Create in 下午6:38 20/9/22
 */
@Component
public class EvaluateBizUidGenerator extends AbstractIBusinessIdGenerator {
    @Override
    public String businessCodePrefix() {
        return "P" + gainDateString();
    }
}
