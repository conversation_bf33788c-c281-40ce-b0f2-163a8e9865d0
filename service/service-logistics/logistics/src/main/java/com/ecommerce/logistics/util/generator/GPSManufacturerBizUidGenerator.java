package com.ecommerce.logistics.util.generator;

import com.ecommerce.common.enums.BusinessCode;
import com.ecommerce.common.service.common.bsid.AbstractIBusinessIdGenerator;
import org.springframework.stereotype.Component;

/**
 * GPS厂商编号生成工具
 * @Auther: <EMAIL>
 * @Date: 08/01/2019 10:34
 * @Description: GPSManufacturerBizUidGenerator
 */
@Component
public class GPSManufacturerBizUidGenerator extends AbstractIBusinessIdGenerator {
    @Override
    public String businessCodePrefix() {
        return BusinessCode.PICK.getCode() + gainDateString();
    }
}
