package com.ecommerce.logistics.util.generator;

import com.ecommerce.common.service.common.bsid.AbstractIBusinessIdGenerator;
import org.springframework.stereotype.Component;

/**
 * 调拨计划号
 * Created by hexinhui3 on 2021/8/10 15:27
 */
@Component
public class InternalAllocationBizUidGenerator extends AbstractIBusinessIdGenerator {

    /**
     * 调拨计划号
     * @return
     */
    @Override
    public String businessCodePrefix() {
        return "DB" + gainDateString();
    }
}
