package com.ecommerce.logistics.util.generator;

import com.ecommerce.common.service.common.bsid.AbstractIBusinessIdGenerator;
import org.springframework.stereotype.Component;

/**
 * 生成船舶编号服务
 * <AUTHOR>
 * Created on 2020/11/23 17:42
 */
@Component
public class ShippingInfoBizUidGenerator  extends AbstractIBusinessIdGenerator {

    /**
     * 船舶编号
     * @return
     */
    @Override
    public String businessCodePrefix() {
        return "CB" + gainDateString();
    }
}
