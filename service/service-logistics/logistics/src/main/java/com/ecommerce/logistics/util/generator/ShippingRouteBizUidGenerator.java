package com.ecommerce.logistics.util.generator;

import com.ecommerce.common.service.common.bsid.AbstractIBusinessIdGenerator;
import org.springframework.stereotype.Component;

/**
 * 生成航线编号服务
 * Created by <PERSON><PERSON><PERSON> on 2020/11/4 17:06
 */
@Component
public class ShippingRouteBizUidGenerator extends AbstractIBusinessIdGenerator {

    /**
     * 航线编号
     * @return
     */
    @Override
    public String businessCodePrefix() {
        return "HX" + gainDateString();
    }

}
