package com.ecommerce.logistics.util.generator;

import com.ecommerce.common.service.common.bsid.AbstractIBusinessIdGenerator;
import org.springframework.stereotype.Component;

/**
 * @Description:
 * @Author: <EMAIL>
 * @Date: 21/10/2020 17:50
 */
@Component
public class UnloadingObjectionBizUidGenerator extends AbstractIBusinessIdGenerator {
    @Override
    public String businessCodePrefix() {
        return "XHYY" + gainDateString();
    }
}
