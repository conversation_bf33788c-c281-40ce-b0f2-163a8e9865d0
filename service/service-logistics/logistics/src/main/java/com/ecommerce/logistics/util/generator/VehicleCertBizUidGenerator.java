package com.ecommerce.logistics.util.generator;

import com.ecommerce.common.service.common.bsid.AbstractIBusinessIdGenerator;
import org.springframework.stereotype.Component;

/**
 * Copyright (C),2020
 * FileName: VehicleCertBizUidGenerator
 * Author:   <EMAIL>
 * Date:     22/09/2020 15:09
 * Description:
 *
 * <AUTHOR>
 */
@Component
public class VehicleCertBizUidGenerator extends AbstractIBusinessIdGenerator {
    @Override
    public String businessCodePrefix() {
        return "VEHICLE" + gainDateString();
    }
}
