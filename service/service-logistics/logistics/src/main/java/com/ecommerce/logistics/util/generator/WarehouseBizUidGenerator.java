package com.ecommerce.logistics.util.generator;

import com.ecommerce.common.enums.BusinessCode;
import com.ecommerce.common.service.common.bsid.AbstractIBusinessIdGenerator;
import org.springframework.stereotype.Component;

/**
 * @Auther: shenz<PERSON>@deloitte.com.cn
 * @Date: 19/09/2018 16:08
 * @Description:
 */
@Component
public class WarehouseBizUidGenerator extends AbstractIBusinessIdGenerator {
    @Override
    public String businessCodePrefix() {
        return BusinessCode.REPERTORY.getCode() + gainDateString();
    }
}
