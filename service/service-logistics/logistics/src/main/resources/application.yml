logging:
  config: classpath:log/logback-${spring.profiles.active}.xml
  level:
    com.netflix.discovery.DiscoveryClient: WARN
mybatis:
     typeAliasesPackage: com.ecommerce.logistics.dao
     mapperScanPackage: com.ecommerce.logistics.dao
     mapperLocations: "classpath:/mapper/*.xml"
     configLocation: "classpath:/mybatis-config.xml"
vip:
  server:
    ip:
      member: **************
      trade: **************
      trace: **************
      base: **************
      order: ************** #************ #
      openapi: ************** #************ #************ #
      pay: **************
      price: **************
spring:
  datasource:
    name: db
    type: com.alibaba.druid.pool.DruidDataSource
    url: ${mysql.db.url:****************************************************************************************}
    username: ${mysql.db.username:root}
    password: ${mysql.db.password:0231625530}
    driver-class-name: com.mysql.cj.jdbc.Driver
    minIdle: 5
    maxActive: 100
    initialSize: 10
    maxWait: 60000
    timeBetweenEvictionRunsMillis: 60000
    minEvictableIdleTimeMillis: 300000
    validationQuery: select 'x'
    testWhileIdle: true
    testOnBorrow: false
    testOnReturn: false
    poolPreparedStatements: true
    maxPoolPreparedStatementPerConnectionSize: 50
    removeAbandoned: true
    filters: stat
  application:
    name: service-logistics
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  kafka:
    bootstrap-servers: ${kafka_bootstrap_servers:***************:9092}
  rabbitmq:
      host: ${rabbitmq.host:**************}
      port: ${rabbitmq.port:5672}
      username: ${rabbitmq.username:guest}
      password: ${rabbitmq.password:guest}
      publisher-confirms: false

  redis:
    lock:
      acquire:
        timeout: ${logistics.redis.lock.acquire.timeout:1000}
      key:
        timeout: ${logistics.redis.lock.key.timeout:5000}
    database: ${redis.defult.database:0}
    host: ${redis.defult.host:127.0.0.1}
    port: ${redis.defult.port:6379}
    password: ${redis.defult.password:123456}
    timeout: 3000
    lettuce:
      pool:
        max-active: 100
        max-wait: -1
        max-idle: 10
        min-idle:  5
  biz-redis:
    database: ${redis.biz.database:6}
    host: ${redis.biz.host:***************}
    port: ${redis.biz.port:6379}
    password: ${redis.biz.password:123456}
    timeout: 3000
  profiles:
    active: dev
eureka:
  client:
    serviceUrl:
      defaultZone: ${EUREKA_SERVER_ADDRESS:http://dev-euraka.dashuini.cn/eureka/}
  instance:
    hostname: ${DEV_IP:${spring.cloud.client.ip-address}}
    instance-id: ${DEV_IP:${spring.cloud.client.ip-address}}:${server.port}
    prefer-ip-address: ${PREFER_IP:true}
    status-page-url-path: http://${DEV_IP:${spring.cloud.client.ip-address}}:${server.port}/swagger-ui.html
server:
  port: ${app.server.port:9002}
  tomcat:
    max-http-post-size: 10000000

#请求处理的超时时间
ribbon.ReadTimeout: 60000
#请求连接的超时时间
ribbon.ConnectTimeout: 30000

feign:
  hystrix:
    enabled: true
  compression:
    request:
      enabled: true
      mime-types: text/xml,application/xml,application/json
      min-request-size: 2048
    response:
      enabled: true
  httpclient:
    enabled: false
  okhttp:
    enabled: true
hystrix:
  command:
    default:
      execution:
        isolation:
          strategy: SEMAPHORE
          thread:
            timeoutInMilliseconds: 10000 #缺省为1000
  threadpool:
    default:
      coreSize: 100 #缺省为10

#xinge push
tencent:
  push:
    config:
      iosAppId: 8b2a4496ecd2f
      iosSecretKey: 458342cd700efa026d89b65010c9900e
      iosEnviroment: dev
      andriodAppId: 64e29bb2d746d
      andriodSecretKey: bc815786daf60dc41ceb38dee9c17c74
      threadPool:
        maxQueueNum: 200
        maxThreadNum: 100
    andriod:
      seckillActivity: com.test.andriod
      customContent[username]: link
      customContent[password]: link11
      customContent[callback]: http://www.flyat.cc

# umeng push iosEnv is dev or prod
umeng:
  push:
    config:
      dominAddress: https://uatweb.guixiaoni.com/driverapp
      andriodActivity: com.deloittecn.driver.ui.WebviewActivity
      miActivity: com.dashuini.appdriver.ui.login.SplashActivity
      andriodUrlKey: webview_url
      andriodExtra[0100]: driver/snatchHall/details/
      andriodExtra[0200]: driver/waybillMana/details/
      iosUrlKey: url
      iosExtra[0100]: driver/snatchHall/details/
      iosExtra[0200]: driver/waybillMana/details/

#自定义签名，大水泥签名上线后设置为false
sms:
  signature:
    custom:
      enable: ${sms_signature_custom_enable:true}

xxl:
  job:
    admin:
      addresses: ${logistics.job.admin.addresses:http://127.0.0.1:8090/xxl-job-admin}
    executor:
      appname: logistics-xxl
      ip: 127.0.0.1
      port: ${logistics.job.admin.port:9999}
      ### xxl-job log path
      logpath: /data/applogs/xxl-job/jobhandler
      ### xxl-job log retention days
      logretentiondays: 30
    ### xxl-job, access token
    accessToken: default_token

#flyway:
#  locations: classpath:/db
#  placeholder-prefix: "#("
#  placeholder-suffix: ")"

rabbitmq:
  profile: ${crc-logistics.rabbitmq.profile:dev}
  binding:
    queuelist:
      - name: waybill.complete.notify.${rabbitmq.profile}
        key: trace.waybill.complete.notify.${rabbitmq.profile}
        exchange: trace
      - name: waybill.auto.complete.${rabbitmq.profile}
        key: trace.waybill.auto.complete.${rabbitmq.profile}
        exchange: trace
      - name: waybill.snatchSMS.${rabbitmq.profile}
        key: com.ecommerce.logistics.waybill.snatchSMS.${rabbitmq.profile}
        exchange: logistics
      - name: waybill.snatchMessage.push.${rabbitmq.profile}
        key: com.ecommerce.logistics.waybill.snatchMessage.push.${rabbitmq.profile}
        exchange: logistics
      - name: waybill.confirmMessage.push.${rabbitmq.profile}
        key: com.ecommerce.logistics.waybill.confirmMessage.push.${rabbitmq.profile}
        exchange: logistics
      - name: com.ecommerce.notifyCompleteConcrete.${rabbitmq.profile}
        key: erp.logistics.notifyCompleteConcrete.${rabbitmq.profile}
        exchange: logistics
      - name: waybill.leaveWarehouse.${rabbitmq.profile}
        key: com.ecommerce.logistics.waybill.leaveWarehouse.${rabbitmq.profile}
        exchange: logistics
      - name: waybill.relation.close.${rabbitmq.profile}
        key: com.ecommerce.logistics.waybill.relation.close.${rabbitmq.profile}
        exchange: logistics
      - name: waybill.relation.driverCancle.${rabbitmq.profile}
        key: com.ecommerce.logistics.waybill.relation.driverCancle.${rabbitmq.profile}
        exchange: logistics
      - name: waybill.relation.platformCancle.${rabbitmq.profile}
        key: com.ecommerce.logistics.waybill.relation.platformCancle.${rabbitmq.profile}
        exchange: logistics
      - name: waybill.relation.complete.${rabbitmq.profile}
        key: com.ecommerce.logistics.waybill.relation.complete.${rabbitmq.profile}
        exchange: logistics
      - name: waybill.relation.sync.createRelationWaybill.${rabbitmq.profile}
        key: com.ecommerce.logistics.sync.createRelationWaybill.${rabbitmq.profile}
        exchange: logistics
      - name: waybill.relation.sync.reassignmentVehicle.${rabbitmq.profile}
        key: com.ecommerce.logistics.sync.reassignmentVehicle.${rabbitmq.profile}
        exchange: logistics
      - name: waybill.relation.sync.assignWaybill.${rabbitmq.profile}
        key: com.ecommerce.logistics.sync.assignWaybill.${rabbitmq.profile}
        exchange: logistics
      - name: waybill.relation.sync.closePickingBill.${rabbitmq.profile}
        key: com.ecommerce.logistics.sync.closePickingBill.${rabbitmq.profile}
        exchange: logistics
      - name: waybill.relation.sync.openCabinWaybill.${rabbitmq.profile}
        key: com.ecommerce.logistics.sync.openCabinWaybill.${rabbitmq.profile}
        exchange: logistics
      - name: carry.submit.waybillFinish.${rabbitmq.profile}
        key: com.ecommerce.logistics.carry.submit.waybillFinish.${rabbitmq.profile}
        exchange: logistics
      - name: carry.submit.deliveryEnd.${rabbitmq.profile}
        key: com.ecommerce.logistics.carry.submit.deliveryEnd.${rabbitmq.profile}
        exchange: logistics
      - name: carry.submit.driverSettlementFinish
        key: com.ecommerce.logistics.carry.submit.driverSettlementFinish
        exchange: pay
      - name: order_evaluate_queue_logistics
        key: order.evaluate.info.#
        exchange: order_evaluate_exchange
      - name: order_supplement_complete_notify
        key: com.ecommerce.order.supplement.complete.notify
        exchange: order
      - name: order.take.limit.time.expire
        key: com.ecommerce.order.take.limit.time.expire
        exchange: order
      - name: pay.invoice.notify.result
        key: notify_logistics
        exchange: invoice_notify
      - name: ${spring.application.name}_member_info
        key: member.info
        exchange: member_exchange
      - name: order.erp.create.order.success
        key: com.ecommerce.order.erp.create.order.success
        exchange: order
    exchangelist:
      - logistics
      - trace
      - order_evaluate_exchange
      - order
      - invoice_notify
      - member_exchange

waybill:
  snatch:
    count: 200
  exception:
    retryCount: 3
  expire:
    maxTime: ${logistics.waybill.expire.maxTime:86400000}
  carrySubmit:
    carrier: ${logistics.waybill.carrySubmit.carrier:广东省爱泥电子商务有限公司}
    unifiedSocialCreditIdentifier: ${logistics.waybill.carrySubmit.unifiedSocialCreditIdentifier:91440300MA5FE3CHXQ}
    permitNumber: ${logistics.waybill.carrySubmit.permitNumber:123}
    senderCode: ${logistics.waybill.carrySubmit.senderCode:36274}

datasubscribe:
  secretid: AKID6RjqmZuXlxJh4HnwFQaefVBh4GOOqWs7
  secretkey: GNXebdlBcw54ub71tb8V3GwRxED2jQJ8
  regin: ap-guangzhou
  guid: dts-channel-7RUCdmALjQ2r7nHa



