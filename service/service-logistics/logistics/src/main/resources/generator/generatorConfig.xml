<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>

    <context id="Mysql" targetRuntime="MyBatis3Simple" defaultModelType="flat">
        <property name="beginningDelimiter" value="`"/>
        <property name="endingDelimiter" value="`"/>
        <property name="javaFileEncoding" value="UTF-8"/>
        <plugin type="org.mybatis.generator.plugins.SerializablePlugin" />
        <plugin type="org.mybatis.generator.plugins.ToStringPlugin" />

        <plugin type="tk.mybatis.mapper.generator.MapperPlugin">
            <property name="mappers" value="com.ecommerce.common.service.IBaseMapper"/>
        </plugin>

        <jdbcConnection driverClass="com.mysql.cj.jdbc.Driver"
                        connectionURL="***********************************************************************"
                        userId="root"
                        password="crc123456">
        </jdbcConnection>

        <javaModelGenerator targetPackage="com.ecommerce.logistics.dao.vo" targetProject="src/main/java">
            <property name="enableSubPackages" value="true" />
            <property name="trimStrings" value="true" />
        </javaModelGenerator>

        <sqlMapGenerator targetPackage="mapper" targetProject="src/main/resources">
            <property name="enableSubPackages" value="true" />
        </sqlMapGenerator>

        <javaClientGenerator targetPackage="com.ecommerce.logistics.dao.mapper" targetProject="src/main/java"
                             type="XMLMAPPER"/>
<!--        <table tableName="lgs_delivery_bill" domainObjectName="DeliveryBill" enableCountByExample="true"-->
<!--               enableUpdateByExample="true" enableDeleteByExample="true"-->
<!--               enableSelectByExample="true" selectByExampleQueryId="true">-->
<!--        </table>-->
<!--        <table tableName="lgs_delivery_info" domainObjectName="DeliveryInfo" enableCountByExample="true"-->
<!--               enableUpdateByExample="true" enableDeleteByExample="true"-->
<!--               enableSelectByExample="true" selectByExampleQueryId="true">-->
<!--        </table>-->
<!--         <table tableName="lgs_ship_bill" domainObjectName="ShipBill" enableCountByExample="true" -->
<!--                enableUpdateByExample="true" enableDeleteByExample="true" -->
<!--                enableSelectByExample="true" selectByExampleQueryId="true"> -->
<!--         </table> -->
<!--        <table tableName="lgs_ship_bill_item" domainObjectName="ShipBillItem" enableCountByExample="true"-->
<!--               enableUpdateByExample="true" enableDeleteByExample="true"-->
<!--               enableSelectByExample="true" selectByExampleQueryId="true">-->
<!--        </table>-->
<!--        <table tableName="lgs_bind_driver_log" domainObjectName="BindDriverLog" enableCountByExample="true"
               enableUpdateByExample="true" enableDeleteByExample="true"
               enableSelectByExample="true" selectByExampleQueryId="true">
            </table> -->
            <table tableName="lgs_dry_season_policy_info" domainObjectName="DrySeasonPolicyInfo" enableCountByExample="true"
               enableUpdateByExample="true" enableDeleteByExample="true"
               enableSelectByExample="true" selectByExampleQueryId="true">
            </table>
			<table tableName="lgs_dry_season_policy_item" domainObjectName="DrySeasonPolicyItem" enableCountByExample="true"
               enableUpdateByExample="true" enableDeleteByExample="true"
               enableSelectByExample="true" selectByExampleQueryId="true">
            </table>            
    </context>
</generatorConfiguration>