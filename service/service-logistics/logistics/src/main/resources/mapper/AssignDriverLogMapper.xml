<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.AssignDriverLogMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.AssignDriverLog">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="assign_driver_id" jdbcType="VARCHAR" property="assignDriverId" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="user_type" jdbcType="TINYINT" property="userType" />
    <result column="driver_id" jdbcType="VARCHAR" property="driverId" />
    <result column="driver_name" jdbcType="VARCHAR" property="driverName" />
    <result column="driver_phone" jdbcType="VARCHAR" property="driverPhone" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="version" jdbcType="BIGINT" property="version" />
  </resultMap>

  <select id="queryByCond" parameterType="com.ecommerce.logistics.api.dto.driver.AssignDriverLogCondDTO"
          resultType="com.ecommerce.logistics.api.dto.driver.AssignDriverLogResDTO">
    select distinct
        log.driver_id as driverId,
        log.driver_name as driverName,
        log.driver_phone as driverPhone
    from lgs_assign_driver_log log
    where log.del_flg = 0
    and log.user_id = #{userId} and log.user_type = #{userType}
    <if test="mustDriverId != null and mustDriverId > 0">
      and log.driver_id is not null and log.driver_id != ''
    </if>
    order by log.version desc , log.update_time desc
  </select>

  <select id="findByAssignDriver" resultMap="BaseResultMap"
          parameterType="com.ecommerce.logistics.api.dto.driver.AssignDriverLogAddDTO">
    select log.*
    from lgs_assign_driver_log log
    where log.del_flg = 0
    and log.user_id = #{userId} and log.user_type = #{userType}
    and log.driver_name = #{driverName} and log.driver_phone = #{driverPhone}
    <if test="driverId != null and driverId != ''">
      and log.driver_id = #{driverId}
    </if>
    order by version desc , update_time desc
    limit 1
  </select>

</mapper>