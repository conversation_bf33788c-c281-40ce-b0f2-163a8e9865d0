<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.AttachmentMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.Attachment">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="attachment_id" jdbcType="VARCHAR" property="attachmentId" />
    <result column="bid" jdbcType="VARCHAR" property="bid" />
    <result column="entry_id" jdbcType="VARCHAR" property="entryId" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
    <result column="file_ext" jdbcType="VARCHAR" property="fileExt" />
    <result column="file_url" jdbcType="VARCHAR" property="fileUrl" />
    <result column="serial_no" jdbcType="INTEGER" property="serialNo" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="version" jdbcType="BIGINT" property="version" />
  </resultMap>

  <update id="deleteAtt" parameterType="com.ecommerce.logistics.api.dto.attachment.AttRemoveDTO">
    UPDATE lgs_attachment
    SET del_flg = 1,update_user = #{updateUser}
    <where>
      <if test="attachmentId != null">
        AND attachment_id = #{attachmentId}
      </if>
      <if test="entryId != null">
        AND entry_id = #{entryId}
      </if>
    </where>
  </update>
  <select id="selectAttList" parameterType="com.ecommerce.logistics.api.dto.attachment.AttListQueryDTO" resultType="com.ecommerce.logistics.api.dto.attachment.AttListDTO">
    SELECT
    attachment_id AS attachmentId,
    entry_id AS entryId,
    type AS type,
    file_name AS fileName,
    file_ext AS fileExt,
    file_url AS fileUrl,
    bid
    FROM lgs_attachment
    <where>
      del_flg = 0
      <if test="attachmentId != null">
       AND attachment_id = #{attachmentId}
      </if>
      <if test="entryId != null">
        AND entry_id = #{entryId}
      </if>
      <if test="type != null">
        AND type = #{type}
      </if>
    </where>
  </select>
  
  <select id="selectUrlByEntryId" parameterType="java.lang.String" resultType="java.lang.String">
  	SELECT file_url FROM lgs_attachment WHERE entry_id = #{entryId} AND del_flg = 0
  </select>

  <insert id="insertAttachmentList" parameterType="java.util.List">
    INSERT INTO lgs_attachment (attachment_id, bid, entry_id, type, file_name, file_ext, file_url, serial_no, del_flg, create_user, create_time, version)
    VALUES
    <foreach collection="list" item="item" separator=",">
      (#{item.attachmentId}, #{item.bid}, #{item.entryId}, #{item.type}, #{item.fileName}, #{item.fileExt}, #{item.fileUrl},#{item.serialNo}, #{item.delFlg}, #{item.createUser}, #{item.createTime}, #{item.version})
    </foreach>
  </insert>

  <select id="selectAttListByIds" parameterType="java.util.List" resultType="com.ecommerce.logistics.api.dto.attachment.AttListDTO">
    SELECT
    attachment_id AS attachmentId,
    entry_id AS entryId,
    type AS type,
    file_name AS fileName,
    file_ext AS fileExt,
    file_url AS fileUrl,
    bid
    FROM lgs_attachment
    WHERE del_flg = 0 AND entry_id IN
    <foreach close=")" collection="list" index="index" item="item" open="(" separator=",">
      #{item}
    </foreach>
  </select>

  <select id="selectAttListById" parameterType="java.lang.String" resultType="com.ecommerce.logistics.api.dto.attachment.AttListDTO">
    SELECT
    attachment_id AS attachmentId,
    entry_id AS entryId,
    type AS type,
    file_name AS fileName,
    file_ext AS fileExt,
    file_url AS fileUrl,
    bid
    FROM lgs_attachment
    WHERE entry_id = #{entryId} AND del_flg = 0
    order by create_time desc
  </select>
  
  <select id="selectBidsByFileExtAndEntryId" resultType="java.lang.String">
  	SELECT
  		bid
  	FROM
  		lgs_attachment
  	WHERE
  		entry_id = #{entryId} AND del_flg = 0 AND file_ext = #{fileExt}
  </select>
  
  <select id="selectBidsByFileExtAndEntryIds" resultType="java.lang.String">
  	SELECT
  		bid
  	FROM
  		lgs_attachment
  	WHERE
  		 del_flg = 0 AND file_ext = #{fileExt} AND entry_id IN 
  		 <foreach collection="entryIds" close=")" index="index" item="entryId" open="(" separator=",">
  		 	#{entryId}
  		 </foreach>
  </select>


  <update id="deleteAttachmentByEntryIds" >
    UPDATE lgs_attachment
    SET del_flg = 1,update_user = #{operatorId}
    WHERE
        del_flg = 0 AND entry_id = #{entryId}
  </update>

</mapper>