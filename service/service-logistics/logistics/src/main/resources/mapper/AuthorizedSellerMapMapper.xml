<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.AuthorizedSellerMapMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.AuthorizedSellerMap">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="authorize_seller_id" jdbcType="VARCHAR" property="authorizeSellerId" />
    <result column="shipping_id" jdbcType="VARCHAR" property="shippingId" />
    <result column="authorize_user_id" jdbcType="VARCHAR" property="authorizeUserId" />
    <result column="authorize_user_name" jdbcType="VARCHAR" property="authorizeUserName" />
    <result column="service_type" jdbcType="INTEGER" property="serviceType" />
    <result column="authorize_status" jdbcType="INTEGER" property="authorizeStatus" />
    <result column="del_flg" jdbcType="INTEGER" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>


  <!-- 批量新增或修改授权-->
  <insert id="addOrUpdateBatchAuthorize" parameterType="java.util.List">
    INSERT INTO lgs_authorized_seller_map(authorize_seller_id, shipping_id, authorize_user_id,
        authorize_user_name, service_type, authorize_status, create_user, create_time)
    VALUES
    <foreach collection="list" item="item" separator=",">
      (
      #{item.authorizeSellerId,jdbcType=VARCHAR},
      #{item.shippingId,jdbcType=VARCHAR},
      #{item.authorizeUserId,jdbcType=VARCHAR},
      #{item.authorizeUserName,jdbcType=VARCHAR},
      #{item.serviceType,jdbcType=INTEGER},
      1,
      #{item.createUser,jdbcType=VARCHAR},
      NOW()
      )
    </foreach>
    ON DUPLICATE KEY UPDATE
    shipping_id = values(shipping_id),
    authorize_user_id = values(authorize_user_id),
    authorize_user_name = values(authorize_user_name),
    service_type = values(service_type),
    authorize_status = 1,
    update_time = NOW(),
    update_user = values(update_user),
    del_flg = 0
  </insert>

  <!-- 批量新增授权-->
  <insert id="batchInsertAuthorize" parameterType="java.util.List">
    INSERT INTO lgs_authorized_seller_map(authorize_seller_id, shipping_id, authorize_user_id,
    authorize_user_name, service_type, authorize_status, create_user, create_time)
    VALUES
    <foreach collection="list" item="item" separator=",">
      (
      #{item.authorizeSellerId,jdbcType=VARCHAR},
      #{item.shippingId,jdbcType=VARCHAR},
      #{item.authorizeUserId,jdbcType=VARCHAR},
      #{item.authorizeUserName,jdbcType=VARCHAR},
      #{item.serviceType,jdbcType=INTEGER},
      1,
      #{item.createUser,jdbcType=VARCHAR},
      NOW()
      )
    </foreach>
  </insert>

  <!--多艘船舶取消授权一个卖家接口-->
  <update id="batchCancelAuthorize" parameterType="java.util.Map" >
    UPDATE lgs_authorized_seller_map
    SET authorize_status = 0,
        update_time = NOW(),
        update_user = #{operatorId}
    WHERE del_flg = 0
    AND authorize_user_id = #{authorizeUserId}
    AND shipping_id IN
    <foreach collection="list" item="item" index="index" separator="," open="(" close=")">
      #{item}
    </foreach>
  </update>

  <!--批量修改服务类型-->
  <update id="batchFixedTemporary" parameterType="java.util.Map" >
    UPDATE lgs_authorized_seller_map
    SET
      service_type = #{serviceType},
      update_time = NOW(),
      update_user = #{operatorId}
    WHERE del_flg = 0
    AND authorize_user_id = #{authorizeUserId}
    AND shipping_id IN
    <foreach collection="list" item="item" index="index" separator="," open="(" close=")">
      #{item}
    </foreach>
  </update>

  <!--根据卖家id和船舶id查找数据是否存在-->
  <select id="queryExistShippingIds" parameterType="java.util.Map" resultType="com.ecommerce.logistics.dao.vo.AuthorizedSellerMap">
    SELECT
      authorize_seller_id AS authorizeSellerId,
      shipping_id AS shippingId,
      authorize_user_id AS authorizeUserId,
      authorize_user_name AS authorizeUserName,
      service_type AS serviceType,
      authorize_status AS authorizeStatus,
      create_user AS createUser
    FROM lgs_authorized_seller_map
    WHERE del_flg = 0
    AND authorize_user_id = #{authorizeUserId}
    AND shipping_id IN
    <foreach collection="list" item="item" index="index" separator="," open="(" close=")">
      #{item}
    </foreach>
  </select>


  <!--根据船舶id查找已授权卖家列表-->
  <select id="authorizeSellerListInfo" resultType="com.ecommerce.logistics.api.dto.shipping.AuthorizeSellerDTO" >
    SELECT
      authorize_seller_id AS authorizeSellerId,
      shipping_id AS shippingId,
      authorize_user_id AS authorizeUserId,
      authorize_user_name AS authorizeUserName,
      service_type AS serviceType,
      authorize_status AS authorizeStatus
    FROM
        lgs_authorized_seller_map
    WHERE del_flg = 0
    AND shipping_id = #{shippingId}
    AND authorize_status = 1
  </select>


  <!--根据船舶id查找已授权卖家列表-->
  <select id="queryExistFixed" resultType="java.lang.Integer" >
    SELECT
      count(*) AS total
    FROM
        lgs_authorized_seller_map
    WHERE del_flg = 0
    AND shipping_id = #{shippingId}
    AND service_type = 1
    AND authorize_status = 1
  </select>


</mapper>
