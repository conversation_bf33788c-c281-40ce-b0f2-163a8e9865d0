<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.AuxiliaryPriceInfoMapper">
    <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.AuxiliaryPriceInfo">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="auxiliary_price_id" jdbcType="BIGINT" property="auxiliaryPriceId"/>
        <result column="auxiliary_price_no" jdbcType="VARCHAR" property="auxiliaryPriceNo"/>
        <result column="auxiliary_price_name" jdbcType="VARCHAR" property="auxiliaryPriceName"/>
        <result column="min_tonnage" jdbcType="DECIMAL" property="minTonnage"/>
        <result column="max_tonnage" jdbcType="DECIMAL" property="maxTonnage"/>
        <result column="unit_price_difference" jdbcType="DECIMAL" property="unitPriceDifference"/>
        <result column="hide_status" jdbcType="TINYINT" property="hideStatus"/>
        <result column="belonger_id" jdbcType="VARCHAR" property="belongerId"/>
        <result column="user_type" jdbcType="INTEGER" property="userType" />
        <result column="del_flg" jdbcType="TINYINT" property="delFlg"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <insert id="insertAuxiliaryPriceBatch" parameterType="java.util.List">
        insert into lgs_auxiliary_price_info (
        auxiliary_price_no,
        auxiliary_price_name,
        min_tonnage,
        max_tonnage,
        unit_price_difference,
        belonger_id,
        user_type,
        create_user,
        create_time
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.auxiliaryPriceNo},
            #{item.auxiliaryPriceName},
            #{item.minTonnage},
            #{item.maxTonnage},
            #{item.unitPriceDifference},
            #{item.belongerId},
            #{item.userType},
            #{item.createUser},
            #{item.createTime}
            )
        </foreach>
    </insert>


    <select id="queryAuxiliaryPriceDetails" parameterType="java.lang.String"
            resultType="com.ecommerce.logistics.api.dto.shippingroute.AuxiliaryPriceDTO">
        SELECT
            t1.auxiliary_price_id AS auxiliaryPriceId,
            t1.auxiliary_price_no AS auxiliaryPriceNo,
            t1.auxiliary_price_name AS auxiliaryPriceName,
            t1.min_tonnage AS minTonnage,
            t1.max_tonnage AS maxTonnage,
            t1.unit_price_difference AS unitPriceDifference
        FROM lgs_auxiliary_price_info t1
        WHERE t1.del_flg = 0 AND t1.hide_status = 0 AND t1.auxiliary_price_no = #{auxiliaryPriceNo}
            AND t1.belonger_id = #{belongerId}
        ORDER BY t1.min_tonnage ASC
    </select>

    <select id="queryAuxiliaryPriceList" parameterType="com.ecommerce.logistics.api.dto.shippingroute.AuxiliaryPriceDTO"
            resultType="com.ecommerce.logistics.api.dto.shippingroute.AuxiliaryPriceListDTO">
        SELECT
            t1.auxiliary_price_no AS auxiliaryPriceNo,
            t1.auxiliary_price_name AS auxiliaryPriceName,
            t1.create_time AS createTime
        FROM lgs_auxiliary_price_info t1
        WHERE t1.del_flg = 0
            AND t1.hide_status = 0
            AND t1.belonger_id = #{belongerId}
        <if test="auxiliaryPriceName != null and auxiliaryPriceName != ''">
            AND t1.auxiliary_price_name like concat('%',#{auxiliaryPriceName},'%')
        </if>
        <if test="userType != null">
            AND t1.user_type = #{userType}
        </if>
        GROUP BY t1.auxiliary_price_no
        ORDER BY t1.create_time desc
    </select>

    <select id="queryAuxiliaryDetailList" resultType="com.ecommerce.logistics.api.dto.shippingroute.AuxiliaryPriceDTO">
        SELECT
            t1.auxiliary_price_id AS auxiliaryPriceId,
            t1.auxiliary_price_no AS auxiliaryPriceNo,
            t1.auxiliary_price_name AS auxiliaryPriceName,
            t1.min_tonnage AS minTonnage,
            t1.max_tonnage AS maxTonnage,
            t1.unit_price_difference AS unitPriceDifference
        FROM lgs_auxiliary_price_info t1
        WHERE t1.del_flg = 0
            AND t1.hide_status = 0
            AND t1.auxiliary_price_no = #{auxiliaryPriceNo}
            AND t1.belonger_id = #{belongerId}
            AND t1.auxiliary_price_id IN
        <foreach collection="auxiliaryPriceIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <!--根据编号查询出辅助价目表-->
    <select id="queryAuxiliaryByNo" resultType="com.ecommerce.logistics.api.dto.shippingroute.AuxiliaryPriceDTO">
        SELECT
            t1.auxiliary_price_id AS auxiliaryPriceId,
            t1.auxiliary_price_no AS auxiliaryPriceNo,
            t1.auxiliary_price_name AS auxiliaryPriceName,
            t1.min_tonnage AS minTonnage,
            t1.max_tonnage AS maxTonnage,
            t1.unit_price_difference AS unitPriceDifference
        FROM lgs_auxiliary_price_info t1
        WHERE t1.del_flg = 0
        AND t1.belonger_id = #{belongerId}
        AND t1.auxiliary_price_no = #{auxiliaryPriceNo}
    </select>


    <select id="queryAuxiliaryByName" resultType="java.lang.Integer">
        SELECT
            count(*) AS total
        FROM lgs_auxiliary_price_info t1
        WHERE t1.del_flg = 0
        AND t1.hide_status = 0
        AND t1.auxiliary_price_name = #{auxiliaryPriceName}
        limit 1
    </select>

</mapper>