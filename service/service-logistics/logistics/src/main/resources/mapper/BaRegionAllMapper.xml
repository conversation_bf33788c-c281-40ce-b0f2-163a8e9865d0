<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.BaRegionAllMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.BaRegionAll">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="region_code" jdbcType="VARCHAR" property="regionCode" />
    <result column="region_son_code" jdbcType="VARCHAR" property="regionSonCode" />
    <result column="depth" jdbcType="VARCHAR" property="depth" />
    <result column="root_code" jdbcType="VARCHAR" property="rootCode" />
    <result column="create_time" jdbcType="DATE" property="createTime" />
    <result column="update_time" jdbcType="DATE" property="updateTime" />
  </resultMap>

  <select id="queryBaRegionAllByRegionCode" resultType="java.lang.String">
    SELECT
    region_son_code AS regionSonCode
    FROM ba_region_all
    WHERE region_code IN
    <foreach collection="regionCodeList" item="regionCode" open="(" separator="," close=")">
      #{regionCode}
    </foreach>
  </select>
</mapper>