<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.BaSaleRegionAllMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.BaSaleRegionAll">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="region_id" jdbcType="VARCHAR" property="regionId" />
    <result column="region_son_id" jdbcType="VARCHAR" property="regionSonId" />
    <result column="root_id" jdbcType="VARCHAR" property="rootId" />
    <result column="depth" jdbcType="INTEGER" property="depth" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="seller_id" jdbcType="VARCHAR" property="sellerId" />
  </resultMap>

  <!--查询出销售区域最小集合-->
  <select id="queryRegionSonIdByRegionId" resultType="java.lang.String">
    SELECT region_son_id AS regionSonId FROM ba_sale_region_All
    WHERE seller_id = #{sellerId}
    <if test="saleRegionId != null and saleRegionId != ''">
      AND region_id= #{saleRegionId}
    </if>
    <if test="saleRegionIdList != null and saleRegionIdList.size() > 0">
      AND region_id IN
      <foreach close=")" collection="saleRegionIdList" index="index" item="region" open="(" separator=",">
        #{region}
      </foreach>
    </if>
  </select>

</mapper>