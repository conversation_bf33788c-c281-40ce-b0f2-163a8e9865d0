<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.BillEvaluateMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.BillEvaluate">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="evaluate_id" jdbcType="VARCHAR" property="evaluateId" />
    <result column="evaluate_num" jdbcType="VARCHAR" property="evaluateNum" />
    <result column="evaluate_type" jdbcType="VARCHAR" property="evaluateType" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="relation_bill_id" jdbcType="VARCHAR" property="relationBillId" />
    <result column="relation_bill_num" jdbcType="VARCHAR" property="relationBillNum" />
    <result column="relation_bill_type" jdbcType="VARCHAR" property="relationBillType" />
    <result column="evaluated_person_id" jdbcType="VARCHAR" property="evaluatedPersonId" />
    <result column="evaluated_person_name" jdbcType="VARCHAR" property="evaluatedPersonName" />
    <result column="evaluated_person_type" jdbcType="VARCHAR" property="evaluatedPersonType" />
    <result column="evaluator_id" jdbcType="VARCHAR" property="evaluatorId" />
    <result column="evaluator_name" jdbcType="VARCHAR" property="evaluatorName" />
    <result column="evaluator_type" jdbcType="VARCHAR" property="evaluatorType" />
    <result column="tc_score" jdbcType="TINYINT" property="tcScore" />
    <result column="ts_score" jdbcType="TINYINT" property="tsScore" />
    <result column="sq_score" jdbcType="TINYINT" property="sqScore" />
    <result column="cs_score" jdbcType="TINYINT" property="csScore" />
    <result column="shipper_score" jdbcType="TINYINT" property="shipperScore" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
  </resultMap>

  <select id="selectBillEvaluateListByCondition" parameterType="com.ecommerce.logistics.api.dto.billEvaluate.BillEvaluateQueryDTO"
          resultType="com.ecommerce.logistics.api.dto.billEvaluate.BillEvaluateListDTO">
    SELECT
    t1.evaluate_num as evaluateNum,
    t1.evaluate_type as evaluateType,
    t1.relation_bill_type as relationBillType,
    t1.relation_bill_num as relationBillNum,
    t1.evaluated_person_type as evaluatedPersonType,
    t1.evaluated_person_name as evaluatedPersonName,
    t1.tc_score as tcScore,
    t1.ts_score as tsScore,
    t1.sq_score as sqScore,
    t1.cs_score as csScore,
    t1.shipper_score as shipperScore,
    t1.evaluator_name as evaluatorName,
    t1.evaluator_type as evaluatorType,
    t1.create_time as createTime
    FROM
    lgs_bill_evaluate as t1
    <where>
      t1.del_flg = 0
      <if test="type != null and type != ''">
        <![CDATA[AND t1.type = #{type}]]>
      </if>
      <if test="evaluateType != null and evaluateType != ''">
        <![CDATA[AND t1.evaluate_type = #{evaluateType}]]>
      </if>
      <if test="evaluatedPersonId != null and evaluatedPersonId != ''">
        <![CDATA[AND t1.evaluated_person_id = #{evaluatedPersonId}]]>
      </if>
      <if test="evaluatedPersonType != null and evaluatedPersonType != ''">
        <![CDATA[AND t1.evaluated_person_type = #{evaluatedPersonType}]]>
      </if>
      <if test="evaluatorId != null and evaluatorId != ''">
        <![CDATA[AND t1.evaluator_id = #{evaluatorId}]]>
      </if>
      <if test="evaluatorType != null and evaluatorType != ''">
        <![CDATA[AND t1.evaluator_type = #{evaluatorType}]]>
      </if>
      <if test="beginTime != null and beginTime != ''">
        <![CDATA[AND t1.create_time >= #{beginTime}]]>
      </if>
      <if test="endTime != null and endTime != ''">
        <![CDATA[AND t1.create_time <= #{endTime}]]>
      </if>
    </where>
    ORDER BY
    t1.create_time DESC
  </select>

  <select id="selectCountByEvaluatedPersonId" parameterType="java.lang.String"
          resultType="com.ecommerce.logistics.api.dto.billEvaluate.BillEvaluateCountDTO">
    SELECT
    sum(tc_score) as tcScore,
    sum(ts_score) as tsScore,
    sum(sq_score) as sqScore,
    sum(cs_score) as csScore,
    sum(shipper_score) as shipperScore
    FROM
    lgs_bill_evaluate
    WHERE
    del_flg = 0 AND evaluated_person_id = #{evaluatedPersonId}
  </select>

</mapper>