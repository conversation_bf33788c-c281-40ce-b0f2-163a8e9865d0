<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.BillEvaluateStatisticMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.BillEvaluateStatistic">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="evaluate_statistic_id" jdbcType="VARCHAR" property="evaluateStatisticId" />
    <result column="evaluated_person_id" jdbcType="VARCHAR" property="evaluatedPersonId" />
    <result column="evaluated_person_name" jdbcType="VARCHAR" property="evaluatedPersonName" />
    <result column="evaluated_person_type" jdbcType="VARCHAR" property="evaluatedPersonType" />
    <result column="evaluate_count" jdbcType="INTEGER" property="evaluateCount" />
    <result column="ave_tc_score" jdbcType="TINYINT" property="aveTcScore" />
    <result column="ave_ts_score" jdbcType="TINYINT" property="aveTsScore" />
    <result column="ave_sq_score" jdbcType="TINYINT" property="aveSqScore" />
    <result column="ave_cs_score" jdbcType="TINYINT" property="aveCsScore" />
    <result column="ave_shipper_score" jdbcType="TINYINT" property="aveShipperScore" />
    <result column="ave_score" jdbcType="TINYINT" property="aveScore" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
  </resultMap>

  <select id="selectEvaluateStatisticList" parameterType="com.ecommerce.logistics.api.dto.billEvaluate.BillEvaluateStatisticListQueryDTO"
          resultType="com.ecommerce.logistics.api.dto.billEvaluate.BillEvaluateStatisticListDTO">
    SELECT
    evaluated_person_id as evaluatedPersonId,
    evaluated_person_name as evaluatedPersonName,
    evaluated_person_type as evaluatedPersonType,
    evaluate_count as evaluateCount,
    ave_tc_score as aveTcScore,
    ave_ts_score as aveTsScore,
    ave_sq_score as aveSqScore,
    ave_cs_score as aveCsScore,
    ave_score as aveScore
    FROM
    lgs_bill_evaluate_statistic
    WHERE
        del_flg = 0
    <if test="evaluatedPersonName != null and evaluatedPersonName != '' ">
      AND evaluated_person_name LIKE CONCAT('%', #{evaluatedPersonName}, '%')
    </if>
    <if test="evaluatedPersonType != null and evaluatedPersonType != '' ">
      AND evaluated_person_type = #{evaluatedPersonType}
    </if>
    order by aveScore desc
  </select>

  <select id="selectPersonIdAndScoreByType" parameterType="java.lang.String" resultType="com.ecommerce.logistics.api.dto.billEvaluate.BillEvaluateStatisticListDTO">
    SELECT
    evaluated_person_id as evaluatedPersonId,
    ave_score as aveScore
    FROM
    lgs_bill_evaluate_statistic
    WHERE
    del_flg = 0 AND evaluated_person_type = #{evaluatedPersonType}
    order by aveScore desc
  </select>
</mapper>