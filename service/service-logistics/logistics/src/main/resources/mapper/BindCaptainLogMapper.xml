<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.BindCaptainLogMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.BindCaptainLog">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="bind_captain_id" jdbcType="VARCHAR" property="bindCaptainId" />
    <result column="shipping_id" jdbcType="VARCHAR" property="shippingId" />
    <result column="captain_account_name" jdbcType="VARCHAR" property="captainAccountName" />
    <result column="captain_name" jdbcType="VARCHAR" property="captainName" />
    <result column="captain_phone" jdbcType="VARCHAR" property="captainPhone" />
    <result column="shipping_name" jdbcType="VARCHAR" property="shippingName" />
    <result column="manager_member_name" jdbcType="VARCHAR" property="managerMemberName" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <select id="queryBindCaptainLog" resultType="com.ecommerce.logistics.api.dto.shipping.BindCaptainLogDTO">
    SELECT
      bind_captain_id AS bindCaptainId,
      shipping_id AS shippingId,
      captain_account_name AS captainAccountName,
      captain_name AS captainName,
      captain_phone AS captainPhone,
      shipping_name AS shippingName,
      manager_member_name AS managerMemberName,
      type AS type,
      create_time  AS createTime
	FROM lgs_bind_captain_log
    WHERE captain_account_name = #{captainAccountName}
    ORDER BY create_time DESC
  </select>

  <select id="queryBindCaptainLogByShippingId" resultType="com.ecommerce.logistics.api.dto.shipping.BindCaptainLogDTO">
    SELECT
      bind_captain_id AS bindCaptainId,
      shipping_id AS shippingId,
      captain_account_name AS captainAccountName,
      captain_name AS captainName,
      captain_phone AS captainPhone,
      shipping_name AS shippingName,
      manager_member_name AS managerMemberName,
      type AS type,
      create_time  AS createTime
	FROM lgs_bind_captain_log
    WHERE shipping_id = #{shippingId}
    AND type = 1
    ORDER BY create_time DESC
  </select>

  <select id="getBindCaptainByShippingId" resultType="com.ecommerce.logistics.api.dto.shipping.BindCaptainLogDTO">
    SELECT
      bind_captain_id AS bindCaptainId,
      shipping_id AS shippingId,
      captain_account_name AS captainAccountName,
      captain_name AS captainName,
      captain_phone AS captainPhone,
      shipping_name AS shippingName,
      manager_member_name AS managerMemberName,
      type AS type,
      create_time  AS createTime
	FROM lgs_bind_captain_log
    WHERE shipping_id = #{shippingId}
    AND type = 1
    ORDER BY create_time DESC
    limit 1
  </select>

</mapper>