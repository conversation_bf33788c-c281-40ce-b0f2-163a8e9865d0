<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="com.ecommerce.logistics.dao.mapper.BindDriverLogMapper">
	<resultMap id="BaseResultMap"
		type="com.ecommerce.logistics.dao.vo.BindDriverLog">
		<!-- WARNING - @mbg.generated -->
		<id column="bind_driver_id" jdbcType="VARCHAR"
			property="bindDriverId" />
		<result column="vehicle_id" jdbcType="VARCHAR"
			property="vehicleId" />
		<result column="driver_account_name" jdbcType="VARCHAR"
			property="driverAccountName" />
		<result column="driver_name" jdbcType="VARCHAR"
			property="driverName" />
		<result column="driver_phone" jdbcType="VARCHAR"
			property="driverPhone" />
		<result column="driver_member_name" jdbcType="VARCHAR"
			property="driverMemberName" />
		<result column="type" jdbcType="INTEGER" property="type" />
		<result column="create_user" jdbcType="VARCHAR"
			property="createUser" />
		<result column="update_user" jdbcType="VARCHAR"
			property="updateUser" />
		<result column="create_time" jdbcType="TIMESTAMP"
			property="createTime" />
		<result column="update_time" jdbcType="TIMESTAMP"
			property="updateTime" />
	</resultMap>
	<select id="getBindCaptainByVehicleId"
		resultType="com.ecommerce.logistics.api.dto.driver.BindDriverLogDTO">
		SELECT
		bind_driver_id AS bindDriverId,
		vehicle_id AS vehicleId,
		driver_account_name AS driverAccountName,
		driver_name AS driverName,
		driver_phone AS driverPhone,
		driver_member_name AS driverMemberName,
		type AS type,
		create_time AS createTime
		FROM lgs_bind_driver_log
		WHERE vehicle_id = #{vehicleId}
		AND type = 1
		ORDER BY create_time DESC
		limit 1
	</select>
</mapper>