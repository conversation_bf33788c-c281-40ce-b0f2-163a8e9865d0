<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.BreakMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.Break">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="break_id" jdbcType="VARCHAR" property="breakId" />
    <result column="break_num" jdbcType="VARCHAR" property="breakNum" />
    <result column="relation_bill_type" jdbcType="VARCHAR" property="relationBillType" />
    <result column="relation_bill_num" jdbcType="VARCHAR" property="relationBillNum" />
    <result column="relation_bill_id" jdbcType="VARCHAR" property="relationBillId"/>
    <result column="break_init_role_type" jdbcType="VARCHAR" property="breakInitRoleType" />
    <result column="break_init_role_name" jdbcType="VARCHAR" property="breakInitRoleName" />
    <result column="break_init_role_id" jdbcType="VARCHAR" property="breakInitRoleId" />
    <result column="break_content" jdbcType="VARCHAR" property="breakContent" />
    <result column="delivery_quantity" jdbcType="VARCHAR" property="deliveryQuantity" />
    <result column="break_quantity" jdbcType="VARCHAR" property="breakQuantity" />
    <result column="propose_time" jdbcType="TIMESTAMP" property="proposeTime" />
    <result column="deal_finish_user_name" jdbcType="VARCHAR" property="dealFinishUserName" />
    <result column="deal_finish_user_id" jdbcType="VARCHAR" property="dealFinishUserId" />
    <result column="deal_finish_time" jdbcType="TIMESTAMP" property="dealFinishTime" />
    <result column="deal_status" jdbcType="VARCHAR" property="dealStatus" />
    <result column="responsible_role_name" jdbcType="VARCHAR" property="responsibleRoleName" />
    <result column="responsible_role_id" jdbcType="VARCHAR" property="responsibleRoleId" />
    <result column="responsible_role_type" jdbcType="VARCHAR" property="responsibleRoleType" />
    <result column="responsible_content" jdbcType="VARCHAR" property="responsibleContent" />
    <result column="break_amount" jdbcType="DECIMAL" property="breakAmount" />
    <result column="deal_result" jdbcType="VARCHAR" property="dealResult" />
    <result column="break_flag" jdbcType="INTEGER" property="breakFlag" />
    <result column="del_flg" jdbcType="INTEGER" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="version" jdbcType="BIGINT" property="version" />
  </resultMap>
</mapper>