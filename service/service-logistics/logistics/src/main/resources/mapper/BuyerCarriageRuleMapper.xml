<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.BuyerCarriageRuleMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.BuyerCarriageRule">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="carriage_rule_id" jdbcType="VARCHAR" property="carriageRuleId" />
    <result column="warehouse_id" jdbcType="VARCHAR" property="warehouseId" />
    <result column="transport_category_id" jdbcType="VARCHAR" property="transportCategoryId" />
    <result column="vehicle_type_id" jdbcType="VARCHAR" property="vehicleTypeId" />
    <result column="transport_price" jdbcType="DECIMAL" property="transportPrice" />
    <result column="carry_price" jdbcType="DECIMAL" property="carryPrice" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="version" jdbcType="BIGINT" property="version" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
  </resultMap>
  <select id="queryBuyerCarriageRuleList" parameterType="com.ecommerce.logistics.api.dto.carriagerule.BuyerCarriageRuleQueryDTO"
          resultType="com.ecommerce.logistics.dao.dto.carriage.BuyerCarriageRuleListDO">
    SELECT
      t1.carriage_rule_id as carriageRuleId,
      t1.warehouse_id as warehouseId,
      t1.transport_category_id as transportCategoryId,
      t1.transport_price as transportPrice,
      t1.carry_price as carryPrice,
      t1.create_time as createTime
    FROM
      lgs_buyer_carriage_rule as t1
    <where>
      t1.del_flg = 0
      <if test="warehouseId != null and warehouseId != ''">
        <![CDATA[AND t1.warehouse_id = #{warehouseId}]]>
      </if>
      <if test="transportCategoryId != null and transportCategoryId != ''">
        <![CDATA[AND t1.transport_category_id = #{transportCategoryId}]]>
      </if>
    </where>
    ORDER BY
      t1.create_time DESC
  </select>
</mapper>