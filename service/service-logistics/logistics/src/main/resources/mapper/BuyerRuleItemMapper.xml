<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.BuyerRuleItemMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.BuyerRuleItem">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="carriage_rule_id" jdbcType="VARCHAR" property="carriageRuleId" />
    <result column="carriage_item_id" jdbcType="VARCHAR" property="carriageItemId" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="district" jdbcType="VARCHAR" property="district" />
    <result column="district_code" jdbcType="VARCHAR" property="districtCode" />
    <result column="transport_price" jdbcType="DECIMAL" property="transportPrice" />
    <result column="carry_price" jdbcType="DECIMAL" property="carryPrice" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="version" jdbcType="BIGINT" property="version" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
  </resultMap>
  <select id="selectBuyerCarriageItemList" parameterType="com.ecommerce.logistics.dao.dto.carriage.BuyerCarriageQueryDO"
          resultMap="BaseResultMap">
    SELECT
      *
    FROM
      lgs_buyer_carriage_item as t1
    <where>
      t1.del_flg = 0 AND t1.carriage_rule_id = #{carriageRuleId}
      <if test="provinceCode != null and provinceCode != ''">
        <![CDATA[AND t1.province_code = #{provinceCode}]]>
      </if>
      <if test="cityCode != null">
        <![CDATA[AND t1.city_code = #{cityCode}]]>
      </if>
      <if test="districtCode != null">
        <![CDATA[AND t1.district_code = #{districtCode}]]>
      </if>
    </where>
  </select>
</mapper>