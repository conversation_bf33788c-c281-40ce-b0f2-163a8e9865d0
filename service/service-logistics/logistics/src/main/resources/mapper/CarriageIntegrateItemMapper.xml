<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.CarriageIntegrateItemMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.CarriageIntegrateItem">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="integrate_item_id" jdbcType="VARCHAR" property="integrateItemId" />
    <result column="carriage_rule_id" jdbcType="VARCHAR" property="carriageRuleId" />
    <result column="min_section" jdbcType="DECIMAL" property="minSection" />
    <result column="max_section" jdbcType="DECIMAL" property="maxSection" />
    <result column="rounding_flag" jdbcType="TINYINT" property="roundingFlag" />
    <result column="rounding_carriage" jdbcType="DECIMAL" property="roundingCarriage" />
    <result column="unit_price" jdbcType="DECIMAL" property="unitPrice" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>