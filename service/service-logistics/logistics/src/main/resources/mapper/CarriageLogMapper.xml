<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.CarriageLogMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.CarriageLog">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="carriage_log_id" jdbcType="VARCHAR" property="carriageLogId" />
    <result column="carriage_route_id" jdbcType="VARCHAR" property="carriageRouteId" />
    <result column="settlement_type" jdbcType="VARCHAR" property="settlementType" />
    <result column="attachment_url" jdbcType="VARCHAR" property="attachmentUrl" />
    <result column="category_type" jdbcType="VARCHAR" property="categoryType" />
    <result column="operator_id" jdbcType="VARCHAR" property="operatorId" />
    <result column="operator_name" jdbcType="VARCHAR" property="operatorName" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="pre_rule" jdbcType="LONGVARCHAR" property="preRule" />
    <result column="next_rule" jdbcType="LONGVARCHAR" property="nextRule" />
  </resultMap>
  <select id="selectCarriageLogList" parameterType="com.ecommerce.logistics.api.dto.carriage.CarriageRuleQueryDTO"
          resultType="com.ecommerce.logistics.api.dto.carriage.CarriageLogListDTO">
    SELECT
      t1.operator_id as operatorId,
      t1.operator_name as operatorName,
      t1.create_time as createTime,
      t1.pre_rule as preRule,
      t1.next_rule as nextRule,
      t1.attachment_url as attachmentUrl,
      t1.category_type as categoryType
    FROM
      lgs_carriage_log as t1
    WHERE
      t1.del_flg=0 AND
      t1.carriage_route_id = #{carriageRouteId} AND
      t1.settlement_type = #{settlementType}
    ORDER BY
      t1.create_time DESC
  </select>
</mapper>