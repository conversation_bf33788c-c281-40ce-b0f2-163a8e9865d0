<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.CarriageRouteMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.CarriageRoute">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="carriage_route_id" jdbcType="VARCHAR" property="carriageRouteId" />
    <result column="route_name" jdbcType="VARCHAR" property="routeName" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="user_type" jdbcType="INTEGER" property="userType" />
    <result column="pricing_type" jdbcType="VARCHAR" property="pricingType" />
    <result column="warehouse_id" jdbcType="VARCHAR" property="warehouseId" />
    <result column="warehouse_name" jdbcType="VARCHAR" property="warehouseName" />
    <result column="warehouse_type" jdbcType="VARCHAR" property="warehouseType" />
    <result column="warehouse_province" jdbcType="VARCHAR" property="warehouseProvince" />
    <result column="warehouse_province_code" jdbcType="VARCHAR" property="warehouseProvinceCode" />
    <result column="warehouse_city" jdbcType="VARCHAR" property="warehouseCity" />
    <result column="warehouse_city_code" jdbcType="VARCHAR" property="warehouseCityCode" />
    <result column="warehouse_district" jdbcType="VARCHAR" property="warehouseDistrict" />
    <result column="warehouse_district_code" jdbcType="VARCHAR" property="warehouseDistrictCode" />
    <result column="warehouse_address" jdbcType="VARCHAR" property="warehouseAddress" />
    <result column="warehouse_location" jdbcType="VARCHAR" property="warehouseLocation" />
    <result column="receive_user_id" jdbcType="VARCHAR" property="receiveUserId" />
    <result column="receive_user_name" jdbcType="VARCHAR" property="receiveUserName" />
    <result column="receive_address_id" jdbcType="VARCHAR" property="receiveAddressId" />
    <result column="receive_address_detail" jdbcType="VARCHAR" property="receiveAddressDetail" />
    <result column="receive_address_location" jdbcType="VARCHAR" property="receiveAddressLocation" />
    <result column="transport_category_id" jdbcType="VARCHAR" property="transportCategoryId" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
  </resultMap>

  <resultMap id="carriageRouteListMap" type="com.ecommerce.logistics.api.dto.carriage.CarriageRouteListDTO">
    <id column="carriage_route_id" jdbcType="VARCHAR" property="carriageRouteId" />
    <result column="route_name" jdbcType="VARCHAR" property="routeName" />
    <result column="pricing_type" jdbcType="VARCHAR" property="pricingType" />
    <result column="warehouse_id" jdbcType="VARCHAR" property="warehouseId" />
    <result column="warehouse_name" jdbcType="VARCHAR" property="warehouseName" />
    <result column="warehouse_type" jdbcType="VARCHAR" property="warehouseType" />
    <result column="warehouse_province" jdbcType="VARCHAR" property="warehouseProvince" />
    <result column="warehouse_province_code" jdbcType="VARCHAR" property="warehouseProvinceCode" />
    <result column="warehouse_city" jdbcType="VARCHAR" property="warehouseCity" />
    <result column="warehouse_city_code" jdbcType="VARCHAR" property="warehouseCityCode" />
    <result column="warehouse_district" jdbcType="VARCHAR" property="warehouseDistrict" />
    <result column="warehouse_district_code" jdbcType="VARCHAR" property="warehouseDistrictCode" />
    <result column="warehouse_address" jdbcType="VARCHAR" property="warehouseAddress" />
    <result column="warehouse_location" jdbcType="VARCHAR" property="warehouseLocation" />
    <result column="receive_address_id" jdbcType="VARCHAR" property="receiveAddressId" />
    <result column="receive_address_detail" jdbcType="VARCHAR" property="receiveAddressDetail" />
    <result column="receive_address_location" jdbcType="VARCHAR" property="receiveAddressLocation" />
    <result column="transport_category_id" jdbcType="VARCHAR" property="transportCategoryId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <select id="queryCarriageRouteList" parameterType="com.ecommerce.logistics.api.dto.carriage.CarriageRouteQueryDTO" resultMap="carriageRouteListMap">
    SELECT
      distinct t1.*
    FROM
      lgs_carriage_route as t1
    <if test="needMap != null and needMap == 1">
      , lgs_carriage_zone_map ma
    </if>
    WHERE
      t1.del_flg=0
      <if test="needMap != null and needMap == 1">
        and t1.carriage_route_id = ma.carriage_route_id and ma.del_flg = 0 and t1.pricing_type = ma.pricing_type
      </if>
      <if test="userId != null and userId != ''">
        <![CDATA[AND t1.user_id = #{userId}]]>
      </if>
      <if test="userType != null">
        <![CDATA[AND t1.user_type = #{userType}]]>
      </if>
      <if test="warehouseId != null and warehouseId != ''">
        <![CDATA[AND t1.warehouse_id = #{warehouseId}]]>
      </if>
      <if test="warehouseType != null and warehouseType != ''">
        <![CDATA[AND t1.warehouse_type = #{warehouseType}]]>
      </if>
      <if test="provinceCode != null and provinceCode != ''">
        <![CDATA[AND ma.province_code = #{provinceCode}]]>
      </if>
      <if test="cityCode != null and cityCode != ''">
        <![CDATA[AND ma.city_code = #{cityCode}]]>
      </if>
      <if test="districtCode != null and districtCode != ''">
        <![CDATA[AND ma.district_code = #{districtCode}]]>
      </if>
      <if test="pricingType != null and pricingType != ''">
        <![CDATA[AND t1.pricing_type = #{pricingType}]]>
      </if>
      <if test="transportCategoryId != null and transportCategoryId != ''">
        <![CDATA[AND t1.transport_category_id = #{transportCategoryId}]]>
      </if>
      <if test="receiveAddressId != null and receiveAddressId != ''">
        <![CDATA[AND t1.receive_address_id = #{receiveAddressId}]]>
      </if>
    ORDER BY
      t1.create_time DESC
  </select>

  <select id="queryCarriageRouteByCondition" parameterType="com.ecommerce.logistics.dao.dto.carriage.CarriageRouteQueryDO" resultType="com.ecommerce.logistics.dao.dto.carriage.CarriageRouteListDO">
    SELECT
      t1.carriage_route_id as carriageRouteId,
      t2.province_code as provinceCode,
      t2.city_code as cityCode,
      t2.district_code as districtCode,
      t2.street_code as streetCode,
      concat(t2.province_code, t2.city_code, t2.district_code, t2.street_code) as zoneCode
    FROM
      lgs_carriage_route AS t1
    LEFT JOIN
      lgs_carriage_zone_map AS t2 ON t1.carriage_route_id = t2.carriage_route_id
    WHERE
      t1.del_flg = 0 AND t1.warehouse_id = #{warehouseId} AND t1.pricing_type = #{pricingType}
      AND #{zoneCode} LIKE concat(t2.province_code, t2.city_code, t2.district_code, '%')
      AND t1.transport_category_id = #{transportCategoryId}
      <if test="userId != null and userId != ''">
        AND t1.user_id = #{userId}
      </if>
      <if test="userType != null">
        AND t1.user_type = #{userType}
      </if>
      order by t2.province_code desc, t2.city_code desc, t2.district_code desc, t2.street_code desc,
               t1.update_time desc, t2.update_time desc
  </select>

  <select id="selectDuplicateId" parameterType="com.ecommerce.logistics.api.dto.carriage.CarriageRouteSaveDTO" resultType="java.lang.String">
    select distinct route.carriage_route_id
    from lgs_carriage_route route, lgs_carriage_zone_map map
    where
    route.carriage_route_id = map.carriage_route_id
    and route.del_flg = 0 and map.del_flg = 0
    <if test="userId != null and userId != ''">
      AND route.user_id = #{userId}
    </if>
    <if test="userType != null">
      AND route.user_type = #{userType}
    </if>
    <if test="carriageRouteId != null and carriageRouteId != ''">
      and route.carriage_route_id != #{carriageRouteId}
    </if>
    <if test="pricingType != null and pricingType != ''">
      and route.pricing_type = #{pricingType} and map.pricing_type = #{pricingType}
    </if>
    <if test="warehouseId != null and warehouseId != ''">
      and route.warehouse_id = #{warehouseId}
    </if>
    <if test="transportCategoryId != null and transportCategoryId != ''">
      and route.transport_category_id = #{transportCategoryId}
    </if>
    <if test="zoneMapList != null and zoneMapList.size() > 0">
      <foreach collection="zoneMapList" item="zoneIt" index="zoneIn" open=" and ((" separator=" ) or ( " close="))">
        map.province_code = #{zoneIt.provinceCode} and map.city_code = #{zoneIt.cityCode} and map.district_code = #{zoneIt.districtCode}
        <if test="zoneIt.streetCode != null">
          and map.street_code = #{zoneIt.streetCode}
        </if>
        <if test="zoneIt.streetCode == null">
          and (map.street_code = '' or map.street_code is null)
        </if>
      </foreach>
    </if>
    order by route.update_time desc
  </select>

</mapper>