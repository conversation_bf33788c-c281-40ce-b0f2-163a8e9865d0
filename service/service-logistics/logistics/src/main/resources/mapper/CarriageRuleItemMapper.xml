<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.CarriageRuleItemMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.CarriageRuleItem">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="carriage_item_id" jdbcType="VARCHAR" property="carriageItemId" />
    <result column="carriage_rule_id" jdbcType="VARCHAR" property="carriageRuleId" />
    <result column="section_type" jdbcType="VARCHAR" property="sectionType" />
    <result column="min_section" jdbcType="INTEGER" property="minSection" />
    <result column="max_section" jdbcType="INTEGER" property="maxSection" />
    <result column="rule_expression" jdbcType="VARCHAR" property="ruleExpression" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="version" jdbcType="BIGINT" property="version" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
  </resultMap>
</mapper>