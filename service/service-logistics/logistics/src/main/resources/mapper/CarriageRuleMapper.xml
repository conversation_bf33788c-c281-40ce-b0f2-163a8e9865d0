<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.CarriageRuleMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.CarriageRule">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="carriage_rule_id" jdbcType="VARCHAR" property="carriageRuleId" />
    <result column="carriage_route_id" jdbcType="VARCHAR" property="carriageRouteId" />
    <result column="bid" jdbcType="VARCHAR" property="bid" />
    <result column="settlement_type" jdbcType="VARCHAR" property="settlementType" />
    <result column="settlement_user_id" jdbcType="VARCHAR" property="settlementUserId" />
    <result column="settlement_user_name" jdbcType="VARCHAR" property="settlementUserName" />
    <result column="settlement_user_type" jdbcType="INTEGER" property="settlementUserType" />
    <result column="vehicle_type_id" jdbcType="VARCHAR" property="vehicleTypeId" />
    <result column="unit_price" jdbcType="DECIMAL" property="unitPrice" />
    <result column="begin_effective_time" jdbcType="TIMESTAMP" property="beginEffectiveTime" />
    <result column="end_effective_time" jdbcType="TIMESTAMP" property="endEffectiveTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
  </resultMap>

  <!--查询不限承运商的运费规则-->
  <select id="queryUnlimiteCarrier" parameterType="java.util.Map" resultType="com.ecommerce.logistics.dao.vo.CarriageRule">
    SELECT
      t1.carriage_rule_id,
      t1.carriage_route_id,
      t1.bid,
      t1.settlement_type,
      t1.settlement_user_id,
      t1.settlement_user_name,
      t1.settlement_user_type,
      t1.vehicle_type_id,
      t1.unit_price,
      t1.begin_effective_time,
      t1.end_effective_time
    FROM
      lgs_carriage_rule t1
    LEFT JOIN lgs_carriage_route t2 ON t1.carriage_route_id = t2.carriage_route_id
    WHERE t1.del_flg = 0 AND t2.del_flg = 0
    AND t1.settlement_type = #{settlementType}
    AND t1.settlement_user_id = #{settlementUserId}
    AND t1.vehicle_type_id = #{vehicleTypeId}
    AND t2.user_id = #{userId}
    AND t2.pricing_type = #{pricingType}
    AND t2.warehouse_id = #{warehouseId}
    AND t2.transport_category_id = #{transportCategoryId}
    AND t2.receive_address_id = #{receiveAddressId}
  </select>


</mapper>