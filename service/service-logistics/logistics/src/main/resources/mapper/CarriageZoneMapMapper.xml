<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.CarriageZoneMapMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.CarriageZoneMap">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="zone_map_id" jdbcType="VARCHAR" property="zoneMapId" />
    <result column="carriage_route_id" jdbcType="VARCHAR" property="carriageRouteId" />
    <result column="pricing_type" jdbcType="VARCHAR" property="pricingType" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="district" jdbcType="VARCHAR" property="district" />
    <result column="district_code" jdbcType="VARCHAR" property="districtCode" />
    <result column="street" jdbcType="VARCHAR" property="street" />
    <result column="street_code" jdbcType="VARCHAR" property="streetCode" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>