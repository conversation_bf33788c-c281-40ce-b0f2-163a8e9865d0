<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.CarrierCarriageRuleMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.CarrierCarriageRule">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="carriage_rule_id" jdbcType="VARCHAR" property="carriageRuleId" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="district" jdbcType="VARCHAR" property="district" />
    <result column="district_code" jdbcType="VARCHAR" property="districtCode" />
    <result column="transport_category_id" jdbcType="VARCHAR" property="transportCategoryId" />
    <result column="vehicle_type_id" jdbcType="VARCHAR" property="vehicleTypeId" />
    <result column="initial_price" jdbcType="DECIMAL" property="initialPrice" />
    <result column="min_section" jdbcType="INTEGER" property="minSection" />
    <result column="max_section" jdbcType="INTEGER" property="maxSection" />
    <result column="section_price" jdbcType="DECIMAL" property="sectionPrice" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="version" jdbcType="BIGINT" property="version" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
  </resultMap>
  <select id="selectCarrierCarriageRuleList" parameterType="com.ecommerce.logistics.dao.dto.carriage.CarrierCarriageQueryDO" resultMap="BaseResultMap">
    SELECT
      *
    FROM
      lgs_carrier_carriage_rule as t1
    <where>
      t1.del_flg = 0
      <if test="provinceCode != null and provinceCode != ''">
        <![CDATA[AND t1.province_code = #{provinceCode}]]>
      </if>
      <if test="cityCode != null">
        <![CDATA[AND t1.city_code = #{cityCode}]]>
      </if>
      <if test="districtCode != null">
        <![CDATA[AND t1.district_code = #{districtCode}]]>
      </if>
      <if test="vehicleTypeId != null">
        <![CDATA[AND t1.vehicle_type_id = #{vehicleTypeId}]]>
      </if>
      <if test="transportCategoryIdCollection != null and transportCategoryIdCollection.size() > 0">
        AND t1.transport_category_id IN
        <foreach close=")" collection="transportCategoryIdCollection" index="index" item="transportCategoryId" open="(" separator=",">
          #{transportCategoryId}
        </foreach>
      </if>
    </where>
  </select>
  <select id="queryCarrierCarriageRuleList" parameterType="com.ecommerce.logistics.api.dto.carriagerule.CarrierCarriageRuleQueryDTO"
          resultType="com.ecommerce.logistics.dao.dto.carriage.CarrierCarriageRuleListDO">
    SELECT
      carriage_rule_id as carriageRuleId,
      province as province,
      province_code as provinceCode,
      city as city,
      city_code as cityCode,
      district as district,
      district_code as districtCode,
      transport_category_id as transportCategoryId,
      vehicle_type_id as vehicleTypeId,
      create_time as createTime
    FROM
      lgs_carrier_carriage_rule as t1
    <where>
      t1.del_flg = 0
      <if test="provinceCode != null and provinceCode != ''">
        <![CDATA[AND t1.province_code = #{provinceCode}]]>
      </if>
      <if test="cityCode != null and cityCode != ''">
        <![CDATA[AND t1.city_code = #{cityCode}]]>
      </if>
      <if test="districtCode != null and districtCode != ''">
        <![CDATA[AND t1.district_code = #{districtCode}]]>
      </if>
      <if test="transportCategoryId != null and transportCategoryId != ''">
        <![CDATA[AND t1.transport_category_id = #{transportCategoryId}]]>
      </if>
      <if test="vehicleTypeId != null">
        <![CDATA[AND t1.vehicle_type_id = #{vehicleTypeId}]]>
      </if>
    </where>
    ORDER BY
      t1.create_time DESC
  </select>
</mapper>