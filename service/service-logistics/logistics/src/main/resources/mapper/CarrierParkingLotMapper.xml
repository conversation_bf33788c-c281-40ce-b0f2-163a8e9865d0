<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.CarrierParkingLotMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.CarrierParkingLot">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="parking_lot_id" jdbcType="VARCHAR" property="parkingLotId" />
    <result column="carrier_id" jdbcType="VARCHAR" property="carrierId" />
    <result column="carrier_code" jdbcType="VARCHAR" property="carrierCode" />
    <result column="carrier_name" jdbcType="VARCHAR" property="carrierName" />
    <result column="consignor_id" jdbcType="VARCHAR" property="consignorId" />
    <result column="consignor_code" jdbcType="VARCHAR" property="consignorCode" />
    <result column="consignor_name" jdbcType="VARCHAR" property="consignorName" />
    <result column="consignor_user_type" jdbcType="INTEGER" property="consignorUserType" />
    <result column="transport_category_id" jdbcType="VARCHAR" property="transportCategoryId" />
    <result column="parking_lot_name" jdbcType="VARCHAR" property="parkingLotName" />
    <result column="parking_lot_address" jdbcType="VARCHAR" property="parkingLotAddress" />
    <result column="coordinate" jdbcType="VARCHAR" property="coordinate" />
    <result column="province_name" jdbcType="VARCHAR" property="provinceName" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="district_name" jdbcType="VARCHAR" property="districtName" />
    <result column="district_code" jdbcType="VARCHAR" property="districtCode" />
    <result column="street_name" jdbcType="VARCHAR" property="streetName" />
    <result column="street_code" jdbcType="VARCHAR" property="streetCode" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <select id="queryCarrierParkingLotList"
          resultType="com.ecommerce.logistics.api.dto.intelligentDispatch.CarrierParkingLotDTO"
          parameterType="com.ecommerce.logistics.api.dto.intelligentDispatch.CarrierParkingLotDTO">
    SELECT *
    FROM lgs_carrier_parking_lot
    <where>
      del_flg = 0
      <if test="carrierId != null and carrierId != ''">
        AND carrier_id = #{carrierId}
      </if>
      <if test="carrierName != null and carrierName != ''">
        AND carrier_name LIKE CONCAT('%', #{carrierName}, '%')
      </if>
      <if test="consignorId != null and consignorId != ''">
        AND consignor_id = #{consignorId}
      </if>
      <if test="consignorName != null and consignorName != ''">
        AND consignor_name LIKE CONCAT('%', #{consignorName}, '%')
      </if>
      <if test="consignorUserType != null">
        AND consignor_user_type = #{consignorUserType}
      </if>
      <if test="transportCategoryId != null and transportCategoryId != ''">
        AND transport_category_id = #{transportCategoryId}
      </if>
    </where>
    ORDER BY update_time DESC
  </select>

  <select id="queryCarrierParkingListByCarriers" resultType="com.ecommerce.logistics.api.dto.intelligentDispatch.CarrierParkingLotDTO">
    SELECT * FROM lgs_carrier_parking_lot
    WHERE
        consignor_id = #{consignorId}
    AND transport_category_id = #{transportCategoryId}
    AND carrier_id IN
    <foreach collection="list" index="index" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
    AND del_flg = 0
    ORDER BY update_time DESC
  </select>

</mapper>