<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.CarrierServiceAreaMapMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.CarrierServiceAreaMap">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="service_area_map_id" jdbcType="VARCHAR" property="serviceAreaMapId" />
    <result column="service_area_id" jdbcType="VARCHAR" property="serviceAreaId" />
    <result column="province_name" jdbcType="VARCHAR" property="provinceName" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="district_name" jdbcType="VARCHAR" property="districtName" />
    <result column="district_code" jdbcType="VARCHAR" property="districtCode" />
    <result column="street_name" jdbcType="VARCHAR" property="streetName" />
    <result column="street_code" jdbcType="VARCHAR" property="streetCode" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <select id="queryMapDTOListByServiceAreaId"
          resultType="com.ecommerce.logistics.api.dto.intelligentDispatch.CarrierServiceAreaMapDTO">
    SELECT
      *,
      CONCAT_WS( '', province_name, city_name, district_name, street_name ) AS serviceArea
    FROM lgs_carrier_service_area_map
    <where>
      del_flg = 0
      AND service_area_id = #{id}
    </where>
    ORDER BY province_code, city_code, district_code, street_code
  </select>

  <select id="queryMapDTOListByServiceAreaIds"
          resultType="com.ecommerce.logistics.api.dto.intelligentDispatch.CarrierServiceAreaMapDTO">
    SELECT
      *,
      CONCAT_WS( '', province_name, city_name, district_name, street_name ) AS serviceArea
    FROM lgs_carrier_service_area_map
    <where>
      del_flg = 0
      AND service_area_id IN
      <foreach collection="ids" item="id" open="(" separator="," close=")">
        #{id}
      </foreach>
    </where>
    ORDER BY province_code, city_code, district_code, street_code
  </select>
</mapper>