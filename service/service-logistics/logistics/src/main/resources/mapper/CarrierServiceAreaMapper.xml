<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.CarrierServiceAreaMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.CarrierServiceArea">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="service_area_id" jdbcType="VARCHAR" property="serviceAreaId" />
    <result column="carrier_id" jdbcType="VARCHAR" property="carrierId" />
    <result column="carrier_code" jdbcType="VARCHAR" property="carrierCode" />
    <result column="carrier_name" jdbcType="VARCHAR" property="carrierName" />
    <result column="consignor_id" jdbcType="VARCHAR" property="consignorId" />
    <result column="consignor_code" jdbcType="VARCHAR" property="consignorCode" />
    <result column="consignor_name" jdbcType="VARCHAR" property="consignorName" />
    <result column="consignor_user_type" jdbcType="INTEGER" property="consignorUserType" />
    <result column="transport_category_id" jdbcType="VARCHAR" property="transportCategoryId" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <select id="queryCarrierServiceAreaList"
          resultType="com.ecommerce.logistics.api.dto.intelligentDispatch.CarrierServiceAreaDTO"
          parameterType="com.ecommerce.logistics.api.dto.intelligentDispatch.CarrierServiceAreaDTO">
    SELECT *
    FROM lgs_carrier_service_area
    <where>
      del_flg = 0
      <if test="carrierId != null and carrierId != ''">
        AND carrier_id = #{carrierId}
      </if>
      <if test="carrierName != null and carrierName != ''">
        AND carrier_name LIKE CONCAT('%', #{carrierName}, '%')
      </if>
      <if test="consignorId != null and consignorId != ''">
        AND consignor_id = #{consignorId}
      </if>
      <if test="consignorName != null and consignorName != ''">
        AND consignor_name LIKE CONCAT('%', #{consignorName}, '%')
      </if>
      <if test="consignorUserType != null">
        AND consignor_user_type = #{consignorUserType}
      </if>
      <if test="transportCategoryId != null and transportCategoryId != ''">
        AND transport_category_id = #{transportCategoryId}
      </if>
    </where>
    ORDER BY update_time DESC
  </select>

  <select id="queryCarrierServiceAreaByConsignor" resultType="com.ecommerce.logistics.api.dto.intelligentDispatch.ServiceAreaResultDTO"
          parameterType="com.ecommerce.logistics.api.dto.intelligentDispatch.QueryServiceAreaDTO">
    SELECT
      t1.service_area_id AS serviceAreaId,
      t1.carrier_id AS carrierId,
      t1.carrier_name AS carrierName,
      t1.consignor_id AS consignorId,
      t1.consignor_name AS consignorName,
      t2.province_code AS provinceCode,
      t2.province_name AS provinceName,
      t2.city_code AS cityCode,
      t2.city_name AS cityName,
      t2.district_code AS districtCode,
      t2.district_name AS districtName,
      t2.street_code AS streetCode,
      t2.street_name AS streetName,
      CONCAT(t2.province_name,t2.city_name,t2.district_name,t2.street_name) AS serviceArea
    FROM lgs_carrier_service_area t1
    LEFT JOIN lgs_carrier_service_area_map t2 ON t1.service_area_id = t2.service_area_id
    WHERE t1.consignor_id = #{consignorId}
    AND t1.transport_category_id = #{transportCategoryId}
    <if test="consignorUserType != null">
      AND t1.consignor_user_type = #{consignorUserType}
    </if>
    <if test="provinceCode != null and provinceCode != ''">
      AND t2.province_code = #{provinceCode}
    </if>
    <if test="carrierIdList != null and carrierIdList.size > 0">
      AND t1.carrier_id IN
      <foreach close=")" collection="carrierIdList" index="index" item="carrierId" open="(" separator=",">
        #{carrierId}
      </foreach>
    </if>
    AND t1.del_flg = 0 AND t2.del_flg = 0
    ORDER BY t1.update_time DESC
  </select>
</mapper>