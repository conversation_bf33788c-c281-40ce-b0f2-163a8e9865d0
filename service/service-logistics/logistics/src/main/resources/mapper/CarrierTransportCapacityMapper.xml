<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.CarrierTransportCapacityMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.CarrierTransportCapacity">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="capacity_id" jdbcType="VARCHAR" property="capacityId" />
    <result column="carrier_id" jdbcType="VARCHAR" property="carrierId" />
    <result column="carrier_code" jdbcType="VARCHAR" property="carrierCode" />
    <result column="carrier_name" jdbcType="VARCHAR" property="carrierName" />
    <result column="consignor_id" jdbcType="VARCHAR" property="consignorId" />
    <result column="consignor_code" jdbcType="VARCHAR" property="consignorCode" />
    <result column="consignor_name" jdbcType="VARCHAR" property="consignorName" />
    <result column="consignor_user_type" jdbcType="INTEGER" property="consignorUserType" />
    <result column="transport_category_id" jdbcType="VARCHAR" property="transportCategoryId" />
    <result column="can_use_vehicle_num" jdbcType="INTEGER" property="canUseVehicleNum" />
    <result column="can_take_order_num" jdbcType="INTEGER" property="canTakeOrderNum" />
    <result column="can_take_order_quantity" jdbcType="DECIMAL" property="canTakeOrderQuantity" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <select id="queryCarrierTransportCapacityList"
          resultType="com.ecommerce.logistics.api.dto.intelligentDispatch.CarrierTransportCapacityDTO"
          parameterType="com.ecommerce.logistics.api.dto.intelligentDispatch.CarrierTransportCapacityDTO">
    SELECT *
    FROM lgs_carrier_transport_capacity
    <where>
      del_flg = 0
      <if test="carrierId != null and carrierId != ''">
        AND carrier_id = #{carrierId}
      </if>
      <if test="carrierName != null and carrierName != ''">
        AND carrier_name LIKE CONCAT('%', #{carrierName}, '%')
      </if>
      <if test="consignorId != null and consignorId != ''">
        AND consignor_id = #{consignorId}
      </if>
      <if test="consignorName != null and consignorName != ''">
        AND consignor_name LIKE CONCAT('%', #{consignorName}, '%')
      </if>
      <if test="consignorUserType != null">
        AND consignor_user_type = #{consignorUserType}
      </if>
      <if test="transportCategoryId != null and transportCategoryId != ''">
        AND transport_category_id = #{transportCategoryId}
      </if>
      <if test="carrierIdList != null and carrierIdList.size > 0">
        AND carrier_id IN
        <foreach close=")" collection="carrierIdList" index="index" item="carrierId" open="(" separator=",">
          #{carrierId}
        </foreach>
      </if>
    </where>
    ORDER BY update_time DESC
  </select>
</mapper>