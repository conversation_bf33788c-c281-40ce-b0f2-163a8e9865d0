<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.CarrierVehicleMapMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.CarrierVehicleMap">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="carrier_vehicle_id" jdbcType="VARCHAR" property="carrierVehicleId" />
    <result column="vehicle_id" jdbcType="VARCHAR" property="vehicleId" />
    <result column="number" jdbcType="VARCHAR" property="number" />
    <result column="carrier_id" jdbcType="VARCHAR" property="carrierId" />
    <result column="carrier_name" jdbcType="VARCHAR" property="carrierName" />
    <result column="belonger_id" jdbcType="VARCHAR" property="belongerId" />
    <result column="belonger_name" jdbcType="VARCHAR" property="belongerName" />
    <result column="bind_status" jdbcType="TINYINT" property="bindStatus" />
    <result column="vehicle_source" jdbcType="TINYINT" property="vehicleSource" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>

  <select id="selectByCarrierAndVehicle" resultMap="BaseResultMap">
    select *
    from lgs_carrier_vehicle_map
    where carrier_id = #{carrierId}
    and vehicle_id = #{vehicleId}
    and del_flg = 0
  </select>

  <select id="countVehicleByCarrierId" resultType="int">
    select count(distinct number)
    from lgs_carrier_vehicle_map
    where carrier_id = #{carrierId} and del_flg = 0
  </select>

</mapper>