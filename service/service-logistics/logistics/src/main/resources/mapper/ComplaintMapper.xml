<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.ComplaintMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.Complaint">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="complaint_id" jdbcType="VARCHAR" property="complaintId"/>
    <result column="complaint_num" jdbcType="VARCHAR" property="complaintNum" />
    <result column="complaint_type" jdbcType="VARCHAR" property="complaintType" />
    <result column="relation_bill_type" jdbcType="VARCHAR" property="relationBillType" />
    <result column="relation_bill_num" jdbcType="VARCHAR" property="relationBillNum" />
    <result column="complaint_init_role_type" jdbcType="VARCHAR" property="complaintInitRoleType" />
    <result column="complaint_init_role_name" jdbcType="VARCHAR" property="complaintInitRoleName" />
    <result column="complaint_init_role_id" jdbcType="VARCHAR" property="complaintInitRoleId" />
    <result column="complaint_content" jdbcType="VARCHAR" property="complaintContent" />
    <result column="complaint_accept_role_type" jdbcType="VARCHAR" property="complaintAcceptRoleType" />
    <result column="complaint_accept_role_id" jdbcType="VARCHAR" property="complaintAcceptRoleId" />
    <result column="complaint_accept_role_name" jdbcType="VARCHAR" property="complaintAcceptRoleName" />
    <result column="propose_time" jdbcType="TIMESTAMP" property="proposeTime" />
    <result column="deal_finish_user_name" jdbcType="VARCHAR" property="dealFinishUserName" />
    <result column="deal_finish_user_id" jdbcType="VARCHAR" property="dealFinishUserId" />
    <result column="deal_finish_time" jdbcType="TIMESTAMP" property="dealFinishTime" />
    <result column="deal_status" jdbcType="VARCHAR" property="dealStatus" />
    <result column="evaluation_score" jdbcType="REAL" property="evaluationScore" />
    <result column="evaluation_time" jdbcType="TIMESTAMP" property="evaluationTime" />
    <result column="responsible_role_name" jdbcType="VARCHAR" property="responsibleRoleName" />
    <result column="responsible_role_id" jdbcType="VARCHAR" property="responsibleRoleId" />
    <result column="responsible_role_type" jdbcType="VARCHAR" property="responsibleRoleType" />
    <result column="responsible_content" jdbcType="VARCHAR" property="responsibleContent" />
    <result column="fine_amount" jdbcType="DECIMAL" property="fineAmount" />
    <result column="deal_result" jdbcType="VARCHAR" property="dealResult" />
    <result column="fine_flag" jdbcType="INTEGER" property="fineFlag" />
    <result column="del_flg" jdbcType="INTEGER" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="version" jdbcType="BIGINT" property="version" />
  </resultMap>
</mapper>