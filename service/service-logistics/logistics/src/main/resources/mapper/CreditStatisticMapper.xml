<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.CreditStatisticMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.CreditStatistic">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="credit_statistic_id" jdbcType="VARCHAR" property="creditStatisticId" />
    <result column="person_id" jdbcType="VARCHAR" property="personId" />
    <result column="person_name" jdbcType="VARCHAR" property="personName" />
    <result column="person_type" jdbcType="VARCHAR" property="personType" />
    <result column="bill_count" jdbcType="INTEGER" property="billCount" />
    <result column="break_count" jdbcType="INTEGER" property="breakCount" />
    <result column="complaint_count" jdbcType="INTEGER" property="complaintCount" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
  </resultMap>

  <select id="queryCreditStatisticList" parameterType="com.ecommerce.logistics.api.dto.creditStatistic.CreditStatisticQueryDTO" resultType="com.ecommerce.logistics.api.dto.creditStatistic.CreditStatisticListDTO">
    SELECT
    a.personName AS personName,
    a.personType AS personType,
    a.billCount AS billCount,
    a.breakCount AS breakCount,
    a.complaintCount as complaintCount,
    a.creditGrade as creditGrade,
    a.aveScore as aveScore,
    ROUND(a.aveScore/1000 * (100- ROUND(a.breakRate/2,1) - ROUND(a.complaintRate/2,1)),1) as comprehensiveCredit,
    CASE when a.breakRate >= 100 then 100 ELSE a.breakRate end as breakRate,
    CASE when a.complaintRate >= 100 then 100 ELSE a.complaintRate end as complaintRate
    FROM
    (
      SELECT
       c.person_name as personName,
       c.person_type as personType,
       c.bill_count as billCount,
       c.break_count as breakCount,
       ROUND(c.break_count/c.bill_count * 100,1) as breakRate,
       c.complaint_count as complaintCount,
       ROUND(c.complaint_count/c.bill_count * 100,1) as complaintRate,
       ifnull(e.ave_score, 30) as aveScore,
       c.update_time,
       CASE
       WHEN e.ave_score > 40 THEN '优'
       WHEN 40 >= e.ave_score > 30 THEN '良'
       WHEN 30 >= e.ave_score > 25 THEN '中'
       WHEN (25 >= e.ave_score or e.ave_score is null) THEN '差'
       END AS creditGrade
    FROM
    lgs_credit_statistic c
    LEFT JOIN lgs_bill_evaluate_statistic e on c.person_id = e.evaluated_person_id and e.del_flg = 0
    WHERE c.del_flg = 0
    <if test="personName != null and personName != ''">
      AND c.person_name like concat('%', #{personName}, '%')
    </if>
    <if test="personType != null and personType != ''">
      AND c.person_type = #{personType}
    </if>
    ) as a
    ORDER BY a.update_time DESC
  </select>
</mapper>