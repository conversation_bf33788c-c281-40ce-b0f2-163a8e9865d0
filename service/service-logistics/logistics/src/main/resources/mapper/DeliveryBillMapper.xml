<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.DeliveryBillMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.DeliveryBill">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="delivery_bill_id" jdbcType="VARCHAR" property="deliveryBillId" />
    <result column="delivery_bill_num" jdbcType="VARCHAR" property="deliveryBillNum" />
    <result column="delivery_info_id" jdbcType="VARCHAR" property="deliveryInfoId" />
    <result column="consignor_id" jdbcType="VARCHAR" property="consignorId" />
    <result column="consignor_name" jdbcType="VARCHAR" property="consignorName" />
    <result column="carrier_id" jdbcType="VARCHAR" property="carrierId" />
    <result column="carrier_name" jdbcType="VARCHAR" property="carrierName" />
    <result column="carrier_type" jdbcType="VARCHAR" property="carrierType" />
    <result column="carrier_role_type" jdbcType="VARCHAR" property="carrierRoleType" />
    <result column="bill_proxy_type" jdbcType="VARCHAR" property="billProxyType" />
    <result column="transport_tool_type" jdbcType="VARCHAR" property="transportToolType" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="pay_status" jdbcType="VARCHAR" property="payStatus" />
    <result column="root_flag" jdbcType="TINYINT" property="rootFlag" />
    <result column="leaf_flag" jdbcType="TINYINT" property="leafFlag" />
    <result column="parent_id" jdbcType="VARCHAR" property="parentId" />
    <result column="tree_level" jdbcType="INTEGER" property="treeLevel" />
    <result column="node_id_path" jdbcType="VARCHAR" property="nodeIdPath" />
    <result column="node_num_path" jdbcType="VARCHAR" property="nodeNumPath" />
    <result column="entrust_source" jdbcType="VARCHAR" property="entrustSource" />
    <result column="quantity" jdbcType="DECIMAL" property="quantity" />
    <result column="complete_quantity" jdbcType="DECIMAL" property="completeQuantity" />
    <result column="reroute_quantity" jdbcType="DECIMAL" property="rerouteQuantity" />
    <result column="reroute_count" jdbcType="INTEGER" property="rerouteCount" />
    <result column="carry_plan_quantity" jdbcType="DECIMAL" property="carryPlanQuantity" />
    <result column="carry_plan_count" jdbcType="INTEGER" property="carryPlanCount" />
    <result column="carry_send_quantity" jdbcType="DECIMAL" property="carrySendQuantity" />
    <result column="carry_send_count" jdbcType="INTEGER" property="carrySendCount" />
    <result column="carry_complete_quantity" jdbcType="DECIMAL" property="carryCompleteQuantity" />
    <result column="carry_complete_count" jdbcType="INTEGER" property="carryCompleteCount" />
    <result column="consign_quantity" jdbcType="DECIMAL" property="consignQuantity" />
    <result column="consign_accept_quantity" jdbcType="DECIMAL" property="consignAcceptQuantity" />
    <result column="consign_send_quantity" jdbcType="DECIMAL" property="consignSendQuantity" />
    <result column="consign_send_count" jdbcType="INTEGER" property="consignSendCount" />
    <result column="consign_complete_quantity" jdbcType="DECIMAL" property="consignCompleteQuantity" />
    <result column="consign_complete_count" jdbcType="INTEGER" property="consignCompleteCount" />
    <result column="payable_carriage_price" jdbcType="DECIMAL" property="payableCarriagePrice" />
    <result column="payable_carriage_amount" jdbcType="DECIMAL" property="payableCarriageAmount" />
    <result column="carriage_rule_id" jdbcType="VARCHAR" property="carriageRuleId" />
    <result column="estimate_km" jdbcType="DECIMAL" property="estimateKm" />
    <result column="estimate_duration" jdbcType="DECIMAL" property="estimateDuration" />
    <result column="warehouse_id" jdbcType="VARCHAR" property="warehouseId" />
    <result column="warehouse_type" jdbcType="VARCHAR" property="warehouseType" />
    <result column="warehouse_name" jdbcType="VARCHAR" property="warehouseName" />
    <result column="warehouse_province" jdbcType="VARCHAR" property="warehouseProvince" />
    <result column="warehouse_province_code" jdbcType="VARCHAR" property="warehouseProvinceCode" />
    <result column="warehouse_city" jdbcType="VARCHAR" property="warehouseCity" />
    <result column="warehouse_city_code" jdbcType="VARCHAR" property="warehouseCityCode" />
    <result column="warehouse_district" jdbcType="VARCHAR" property="warehouseDistrict" />
    <result column="warehouse_district_code" jdbcType="VARCHAR" property="warehouseDistrictCode" />
    <result column="warehouse_street" jdbcType="VARCHAR" property="warehouseStreet" />
    <result column="warehouse_street_code" jdbcType="VARCHAR" property="warehouseStreetCode" />
    <result column="warehouse_address" jdbcType="VARCHAR" property="warehouseAddress" />
    <result column="warehouse_location" jdbcType="VARCHAR" property="warehouseLocation" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="version" jdbcType="BIGINT" property="version" />
  </resultMap>

  <update id="consignCarrierQuantity">
    update lgs_delivery_bill
    set consign_quantity = consign_quantity + #{consignQuantity}
    where delivery_bill_id = #{deliveryBillId} and del_flg = 0
  </update>

  <select id="queryChildLeafById" resultMap="BaseResultMap">
    select *
    from lgs_delivery_bill
    where parent_id = #{deliveryBillId} and carrier_id = #{actualCarrierId} and leaf_flag = 1 and del_flg = 0
  </select>

  <select id="queryChildById" resultMap="BaseResultMap">
    select *
    from lgs_delivery_bill
    where parent_id = #{deliveryBillId} and delivery_bill_id != #{deliveryBillId} and leaf_flag != 1 and del_flg = 0
  </select>

  <select id="queryByNum" resultMap="BaseResultMap">
    select *
    from lgs_delivery_bill
    where delivery_bill_num = #{deliveryBillNum}
  </select>

  <update id="assignTransportUpdate">
    update lgs_delivery_bill
    set carry_plan_count = carry_plan_count + #{assignCount},
        carry_plan_quantity = carry_plan_quantity + #{assignQuantity}
    where delivery_bill_id = #{deliveryBillId} and del_flg = 0
  </update>

  <select id="queryRootBillByInfoId" resultMap="BaseResultMap">
    select *
    from lgs_delivery_bill
    where delivery_info_id = #{deliveryInfoId} and del_flg = 0 and root_flag = 1
  </select>

  <update id="backAddCarrySend">
    update lgs_delivery_bill
    set update_user = #{updateUser},
        carry_send_quantity = carry_send_quantity + #{sendQuantity},
        carry_send_count = carry_send_count + 1
    where delivery_bill_id in
    <foreach collection="deliveryBillIdList" item="dit" index="din" open="(" separator="," close=")">
      #{dit}
    </foreach>
  </update>

  <update id="backAddConsignSend">
    update lgs_delivery_bill
    set update_user = #{updateUser},
    consign_send_quantity = consign_send_quantity + #{sendQuantity},
    consign_send_count = consign_send_count + 1
    where delivery_bill_id in
    <foreach collection="deliveryBillIdList" item="dit" index="din" open="(" separator="," close=")">
      #{dit}
    </foreach>
  </update>

  <update id="backAddCarryComplete">
    update lgs_delivery_bill
    set update_user = #{updateUser},
        carry_complete_quantity = carry_complete_quantity + #{carryCompleteQuantity},
        carry_complete_count = carry_complete_count + 1,
        complete_quantity = complete_quantity + #{carryCompleteQuantity},
        carry_plan_quantity = carry_plan_quantity - #{carryPlanQuantity} + #{carryCompleteQuantity}
    where delivery_bill_id in
    <foreach collection="deliveryBillIdList" item="dit" index="din" open="(" separator="," close=")">
      #{dit}
    </foreach>
  </update>

  <update id="backAddConsignComplete">
    update lgs_delivery_bill
    set update_user = #{updateUser},
    consign_complete_quantity = consign_complete_quantity + #{consignCompleteQuantity},
    consign_complete_count = consign_complete_count + 1,
    complete_quantity = complete_quantity + #{consignCompleteQuantity}
    where delivery_bill_id in
    <foreach collection="deliveryBillIdList" item="dit" index="din" open="(" separator="," close=")">
      #{dit}
    </foreach>
  </update>

  <select id="queryDeliveryList" parameterType="com.ecommerce.logistics.api.dto.DeliveryQueryDTO" resultType="com.ecommerce.logistics.api.dto.DeliveryListDTO">
    select
    b.delivery_bill_id as deliveryBillId,
    b.delivery_bill_num as deliveryBillNum,
    b.consignor_id as consignorId,
    b.consignor_name as consignorName,
    b.carrier_id as carrierId,
    b.carrier_name as carrierName,
    i.buyer_id as buyerId,
    i.seller_id as sellerId,
    i.transport_category_id as transportCategoryId,
    tc.category_name as transportCategoryName,
    b.transport_tool_type as transportToolType,
    b.quantity as quantity,
    i.take_code as takeCode,
    i.unit as unit,
    (b.quantity - b.carry_plan_quantity - b.consign_quantity) as unplannedQuantity,
    (b.carry_plan_quantity + b.consign_quantity) as plannedQuantity,
    (b.carry_complete_quantity + b.consign_complete_quantity) as completeQuantity,
    (b.carry_send_quantity + b.consign_send_quantity) as sendQuantity,
    b.warehouse_id as warehouseId,
    concat(b.warehouse_province, b.warehouse_city, b.warehouse_district) as warehouseAddress,
    concat(i.province, i.city, i.district) as receiveAddress,
    i.province_code as receiveProvinceCode,
    i.city_code as receiveCityCode,
    b.payable_carriage_price as carriageUnitPrice,
    i.delivery_time as deliveryTime,
    i.delivery_time_range as deliveryTimeRange,
    b.status as status,
    i.can_operate as canOperate,
    b.estimate_km as distance,
    b.warehouse_location as startLocation,
    i.location as endLocation,
    i.sync_flag as syncFlag,
    i.type as type,
    b.root_flag as rootFlag,
    b.bill_proxy_type as billProxyType,
    i.note as note,
    b.internal as internal
    from lgs_delivery_bill b
    left join lgs_delivery_info i on i.delivery_info_id = b.delivery_info_id
    left join lgs_transport_category tc on tc.transport_category_id = i.transport_category_id
    where b.del_flg = 0 AND b.leaf_flag = 0 and (b.root_flag = 0 or i.type != '030060100')
    <if test="deliveryBillNum != null and deliveryBillNum != '' ">
      AND b.delivery_bill_num LIKE CONCAT('%', #{deliveryBillNum}, '%')
    </if>
    <if test="takeCode != null and takeCode != '' ">
      AND i.take_code = #{takeCode}
    </if>
    <if test="consignorId != null and consignorId != '' ">
      AND b.consignor_id = #{consignorId}
    </if>
    <if test="consignorName != null and consignorName != '' ">
      AND b.consignor_name LIKE CONCAT('%', #{consignorName}, '%')
    </if>
    <if test="carrierId != null and carrierId != '' ">
      AND b.carrier_id = #{carrierId}
    </if>
    <if test="carrierName != null and carrierName != '' ">
      AND b.carrier_name LIKE CONCAT('%', #{carrierName}, '%')
    </if>
    <if test="carrierRoleType != null and carrierRoleType != '' ">
      AND b.carrier_role_type = #{carrierRoleType}
    </if>
    <if test="warehouseProvinceCode != null and warehouseProvinceCode != '' ">
      AND b.warehouse_province_code = #{warehouseProvinceCode}
    </if>
    <if test="warehouseCityCode != null and warehouseCityCode != '' ">
      AND b.warehouse_city_code = #{warehouseCityCode}
    </if>
    <if test="warehouseDistrictCode != null and warehouseDistrictCode != '' ">
      AND b.warehouse_district_code = #{warehouseDistrictCode}
    </if>
    <if test="receiveProvinceCode != null and receiveProvinceCode != '' ">
      AND i.province_code = #{receiveProvinceCode}
    </if>
    <if test="receiveCityCode != null and receiveCityCode != '' ">
      AND i.city_code = #{receiveCityCode}
    </if>
    <if test="receiveDistrictCode != null and receiveDistrictCode != '' ">
      AND i.district_code = #{receiveDistrictCode}
    </if>
    <if test="buyerId != null and buyerId != '' ">
      AND i.buyer_id = #{buyerId}
    </if>
    <if test="buyerName != null and buyerName != '' ">
      AND i.buyer_name LIKE CONCAT('%', #{buyerName}, '%')
    </if>
    <if test="sellerId != null and sellerId != '' ">
      AND i.seller_id = #{sellerId}
    </if>
    <if test="sellerName != null and sellerName != '' ">
      AND i.seller_name LIKE CONCAT('%', #{sellerName}, '%')
    </if>
    <if test="goodsId != null and goodsId != '' ">
      AND i.goods_id = #{goodsId}
    </if>
    <if test="goodsName != null and goodsName != '' ">
      AND i.note LIKE CONCAT('%', #{goodsName}, '%')
    </if>
    <if test="transportCategoryId != null and transportCategoryId != '' ">
      AND tc.transport_category_id = #{transportCategoryId}
    </if>
    <if test="receiver != null and receiver != '' ">
      AND i.receiver = #{receiver}
    </if>
    <if test="receiver != null and receiver != '' ">
      AND i.receiver = #{receiver}
    </if>
    <if test="deliveryTimeStart != null and deliveryTimeStart != ''">
      <![CDATA[AND i.delivery_time >= #{deliveryTimeStart}]]>
    </if>
    <if test="deliveryTimeEnd != null and deliveryTimeEnd != ''">
      <![CDATA[AND i.delivery_time <= #{deliveryTimeEnd}]]>
    </if>
    <if test="status != null and status != '' ">
      AND b.status = #{status}
    </if>
    <if test="statusList != null and statusList.size() > 0">
      AND b.status IN
      <foreach close=")" collection="statusList" index="index" item="statusList" open="(" separator=",">
        #{statusList}
      </foreach>
    </if>
    <if test="transportToolType != null and transportToolType != '' ">
      AND i.transport_tool_type = #{transportToolType}
    </if>
    <if test="isDispatchBill != null and isDispatchBill != '' ">
      AND (b.quantity - b.carry_plan_quantity - b.consign_quantity) > 0
    </if>
    <if test="internal != null and internal != '' ">
      AND b.internal = #{internal}
    </if>
    <if test="saleRegionIdList != null and saleRegionIdList.size() > 0">
      and i.sale_region_path in (
        select bsra.region_son_id
        from ba_sale_region_All as bsra
        where bsra.region_id in
        <foreach collection="saleRegionIdList" index="srin" item="srit" open="(" separator="," close=")">
          #{srit}
        </foreach>
      )
    </if>
    <if test="salesmanId != null and salesmanId != ''">
      and i.salesman_id = #{salesmanId}
    </if>
    order by b.create_time desc
  </select>

  <select id="queryDeliveryDetailById" parameterType="java.lang.String" resultType="com.ecommerce.logistics.api.dto.DeliveryDetailDTO">
    select
    b.delivery_bill_id as deliveryBillId,
    b.delivery_bill_num as deliveryBillNum,
    if(b.root_flag = 1,i.take_code,'') as takeCode,
    b.carrier_id as carrierId,
    b.consignor_id as consignorId,
    b.consignor_name as consignorName,
    i.buyer_id as buyerId,
    i.buyer_name as buyerName,
    i.seller_id as sellerId,
    i.seller_name as sellerName,
    b.parent_id as parentId,
    i.transport_category_id as transportCategoryId,
    tc.category_name as transportCategoryName,
    i.delivery_time as deliveryTime,
    i.delivery_time_range as deliveryTimeRange,
    b.warehouse_name as warehouseName,
    b.warehouse_id as warehouseId,
    b.warehouse_type as warehouseType,
    b.warehouse_province as warehouseProvince,
    b.warehouse_province_code as warehouseProvinceCode,
    b.warehouse_city as warehouseCity,
    b.warehouse_city_code as warehouseCityCode,
    b.warehouse_district as warehouseDistrict,
    b.warehouse_district_code as warehouseDistrictCode,
    concat(b.warehouse_province, b.warehouse_city, b.warehouse_district, b.warehouse_street, b.warehouse_address) as warehouseAddress,
    i.receiver_address_id as receiveAddressId,
    concat(i.province, i.city, i.district, i.street, i.address) as receiveAddress,
    i.province_code as receiveProvinceCode,
    i.city_code as receiveCityCode,
    i.district_code as receiveDistrictCode,
    i.street_code as receiveStreetCode,
    b.quantity as quantity,
    i.unit as unit,
    (b.quantity - b.carry_plan_quantity - b.consign_quantity) as unplannedQuantity,
    (b.carry_plan_quantity + b.consign_quantity) as plannedQuantity,
    b.complete_quantity as completeQuantity,
    (b.carry_send_quantity + b.consign_send_quantity) as sendQuantity,
    b.payable_carriage_price as carriageUnitPrice,
    ROUND(b.payable_carriage_price * b.complete_quantity,2) as carriageAmount,
    i.note as goodsName,
    i.receiver as receiver,
    i.receiver_phone as receiverPhone,
    b.status as status,
    i.can_operate as canOperate,
    b.estimate_km as distance,
    b.warehouse_location as startLocation,
    i.location as endLocation,
    b.transport_tool_type as transportToolType,
    b.carry_plan_quantity as carryPlanQuantity,
    b.carry_send_quantity as carrySendQuantity,
    b.carry_send_count as carrySendCount,
    b.carry_complete_quantity as carryCompleteQuantity,
    b.carry_complete_count as carryCompleteCount,
    b.consign_quantity as consignQuantity,
    b.consign_accept_quantity as consignAcceptQuantity,
    b.consign_send_quantity as consignSendQuantity,
    b.consign_send_count as consignSendCount,
    b.consign_complete_quantity as consignCompleteQuantity,
    b.consign_complete_count as consignCompleteCount,
    i.sync_flag as syncFlag,
    i.type as type,
    b.bill_proxy_type as billProxyType,
    b.root_flag as rootFlag
    from lgs_delivery_bill b
    left join lgs_delivery_info i on i.delivery_info_id = b.delivery_info_id
    left join lgs_transport_category tc on tc.transport_category_id = i.transport_category_id
    where b.del_flg = 0 and b.delivery_bill_id = #{deliveryBillId}
  </select>

  <select id="queryDeliveryByParentId" parameterType="java.lang.String" resultType="com.ecommerce.logistics.api.dto.DeliveryDetailRecordsDTO">
    select
    b.delivery_bill_id as deliveryBillId,
    b.delivery_bill_num as deliveryBillNum,
    b.carrier_id as carrierId,
    b.carrier_Name as carrierName,
    b.consignor_id as consignorId,
    b.consignor_name as consignorName,
    b.quantity as quantity,
    b.payable_carriage_price as carriageUnitPrice,
    b.status as status,
    b.leaf_flag as leafFlag,
    b.carrier_role_type as carrierRoleType,
    (b.quantity - b.carry_plan_quantity - b.consign_quantity) as unplannedQuantity,
    IF(b.consign_send_quantity = 0, b.carry_send_quantity ,b.consign_send_quantity) as sendQuantity,
    IF(b.consign_complete_quantity = 0, b.carry_complete_quantity ,b.consign_complete_quantity) as completeQuantity,
    i.can_operate as canOperate
    from lgs_delivery_bill b
    left join lgs_delivery_info i on i.delivery_info_id = b.delivery_info_id
    where b.del_flg = 0
    and b.leaf_flag = 0
    and b.parent_id = #{parentId}
    and b.delivery_bill_id != #{parentId}
    order by b.create_time desc
  </select>

  <select id="queryAllDeliveryByParentId" parameterType="java.lang.String" resultType="com.ecommerce.logistics.api.dto.DeliveryDetailRecordsDTO">
    select
    b.delivery_bill_id as deliveryBillId,
    b.delivery_bill_num as deliveryBillNum,
    b.carrier_id as carrierId,
    b.carrier_Name as carrierName,
    b.consignor_id as consignorId,
    b.consignor_name as consignorName,
    b.quantity as quantity,
    b.payable_carriage_price as carriageUnitPrice,
    b.status as status,
    b.leaf_flag as leafFlag,
    b.carrier_role_type as carrierRoleType,
    (b.quantity - b.carry_plan_quantity - b.consign_quantity) as unplannedQuantity,
    IF(b.consign_send_quantity = 0, b.carry_send_quantity ,b.consign_send_quantity) as sendQuantity,
    IF(b.consign_complete_quantity = 0, b.carry_complete_quantity ,b.consign_complete_quantity) as completeQuantity,
    i.can_operate as canOperate
    from lgs_delivery_bill b
    left join lgs_delivery_info i on i.delivery_info_id = b.delivery_info_id
    where b.del_flg = 0
    and b.leaf_flag = 0
    and b.node_id_path like concat('%', #{parentId}, ',%')
    and b.delivery_bill_id != #{parentId}
    order by b.create_time desc
  </select>

  <select id="queryDeliveryIdByParentId" parameterType="java.lang.String" resultType="java.lang.String">
    select
    distinct b.delivery_bill_id
    from lgs_delivery_bill b
    where b.del_flg = 0
    and b.leaf_flag = 1
    and b.node_num_path LIKE CONCAT ( (select node_num_path from lgs_delivery_bill where delivery_bill_id = #{parentId}) ,'%')
    order by b.node_num_path
  </select>

  <select id="queryDeliveryByTakeCode" parameterType="java.lang.String" resultMap="BaseResultMap">
    select b.*
    from lgs_delivery_bill b
    left join lgs_delivery_info i on i.delivery_info_id = b.delivery_info_id
    where b.root_flag = 1 and b.del_flg = 0 and i.take_code = #{takeCode}
  </select>

  <select id="queryByTakeCodeAndStatus" resultMap="BaseResultMap">
    select b.*
    from lgs_delivery_bill b
    left join lgs_delivery_info i on i.delivery_info_id = b.delivery_info_id
    where
    (b.root_flag = 1 or b.leaf_flag = 0)
    and b.del_flg = 0
    and i.take_code in
    <foreach close=")" collection="takeCodeList" index="index" item="takeCode" open="(" separator=",">
      #{takeCode}
    </foreach>
    and b.status in
    <foreach close=")" collection="statusList" index="index" item="status" open="(" separator=",">
      #{status}
    </foreach>
  </select>

  <select id="selectByIdForUpdate" resultMap="BaseResultMap">
    select *
    from lgs_delivery_bill
    where delivery_bill_id = #{deliveryBillId}
    and del_flg = 0
    for update
  </select>

  <update id="updateStatus">
    update lgs_delivery_bill
    set status = #{newStatus}, update_user = #{updateUser}
    where delivery_bill_id = #{deliveryBillId} and status = #{oldStatus}
  </update>

  <update id="addConsignQuantity">
    update lgs_delivery_bill
    set consign_quantity = consign_quantity + #{addConsignQuantity}, update_user = #{updateUser}
    where delivery_bill_id = #{deliveryBillId}
  </update>

  <update id="addRerouteQuantity">
    update lgs_delivery_bill
    set reroute_count = reroute_count + 1,
        reroute_quantity = reroute_quantity + #{rerouteQuantity},
        consign_send_quantity = consign_send_quantity - #{rerouteQuantity}
        update_user = #{updateUser}
    where delivery_bill_id in
    <foreach collection="deliveryBillIdList" item="dit" index="din" open="(" separator="," close=")">
      #{dit}
    </foreach>
  </update>

  <update id="updateCarryForReroute">
    update lgs_delivery_bill
    set carry_plan_quantity = carry_plan_quantity - #{reroutePlanQuantity},
        carry_plan_count = carry_plan_count -1,
        carry_send_quantity = carry_send_quantity - #{rerouteActualQuantity},
        carry_send_count = carry_send_count - 1,
        update_user = #{updateUser}
    where delivery_bill_id = #{deliveryBillId}
  </update>

  <update id="updateConsignForReroute">
    update lgs_delivery_bill
    set consign_quantity = consign_quantity - #{reroutePlanQuantity},
        consign_accept_quantity = consign_accept_quantity - #{reroutePlanQuantity},
        consign_send_quantity = consign_send_quantity - #{rerouteActualQuantity},
        consign_send_count = consign_send_count - 1,
        update_user = #{updateUser}
    where delivery_bill_id = #{deliveryBillId}
  </update>

  <update id="updateByQuantityAndCountChange" parameterType="com.ecommerce.logistics.dao.dto.delivery.DeliveryBillChangeDTO">
    update lgs_delivery_bill
    set update_user = #{updateUser}
    <if test="quantity != null">
      , quantity = quantity + #{quantity}
    </if>
    <if test="completeQuantity != null">
      , complete_quantity = complete_quantity + #{completeQuantity}
    </if>
    <if test="rerouteQuantity != null">
      , reroute_quantity = reroute_quantity + #{rerouteQuantity}
    </if>
    <if test="rerouteCount != null">
      , reroute_count = reroute_count + #{rerouteCount}
    </if>
    <if test="carryPlanQuantity != null">
      , carry_plan_quantity = carry_plan_quantity + #{carryPlanQuantity}
    </if>
    <if test="carryPlanCount != null">
      , carry_plan_count = carry_plan_count + #{carryPlanCount}
    </if>
    <if test="carrySendQuantity != null">
      , carry_send_quantity = carry_send_quantity + #{carrySendQuantity}
    </if>
    <if test="carrySendCount != null">
      , carry_send_count = carry_send_count + #{carrySendCount}
    </if>
    <if test="carryCompleteQuantity != null">
      , carry_complete_quantity = carry_complete_quantity + #{carryCompleteQuantity}
    </if>
    <if test="carryCompleteCount != null">
      , carry_complete_count = carry_complete_count + #{carryCompleteCount}
    </if>
    <if test="consignQuantity != null">
      , consign_quantity = consign_quantity + #{consignQuantity}
    </if>
    <if test="consignAcceptQuantity != null">
      , consign_accept_quantity = consign_accept_quantity + #{consignAcceptQuantity}
    </if>
    <if test="consignSendQuantity != null">
      , consign_send_quantity = consign_send_quantity + #{consignSendQuantity}
    </if>
    <if test="consignSendCount != null">
      , consign_send_count = consign_send_count + #{consignSendCount}
    </if>
    <if test="consignCompleteQuantity != null">
      , consign_complete_quantity = consign_complete_quantity + #{consignCompleteQuantity}
    </if>
    <if test="consignCompleteCount != null">
      , consign_complete_count = consign_complete_count + #{consignCompleteCount}
    </if>
    where delivery_bill_id = #{deliveryBillId}
  </update>

  <select id="queryBillChainByPathIds" resultType="com.ecommerce.logistics.dao.dto.delivery.DeliveryChainBriDTO">
    select delivery_bill_id as deliveryBillId,
           delivery_bill_num as deliveryBillNum,
           delivery_info_id as deliveryInfoId,
           consignor_id as consignorId,
           consignor_name as consignorName,
           carrier_id as carrierId,
           carrier_name as carrierName,
           carrier_type as carrierType,
           carrier_role_type as carrierRoleType,
           bill_proxy_type as billProxyType,
           transport_tool_type as transportToolType,
           status as status,
           pay_status as payStatus,
           root_flag as rootFlag,
           leaf_flag as leafFlag,
           parent_id as parentId,
           tree_level as treeLevel,
           node_id_path as nodeIdPath,
           node_num_path as nodeNumPath,
           entrust_source as entrustSource,
           payable_carriage_price as payableCarriagePrice,
           carriage_rule_id as carriageRuleId,
           warehouse_id as warehouseId,
           warehouse_type as warehouseType,
           warehouse_name as warehouseName,
           warehouse_province as warehouseProvince,
           warehouse_province_code as warehouseProvinceCode,
           warehouse_city as warehouseCity,
           warehouse_city_code as warehouseCityCode,
           warehouse_district as warehouseDistrict,
           warehouse_district_code as warehouseDistrictCode,
           warehouse_street as warehouseStreet,
           warehouse_street_code as warehouseStreetCode,
           warehouse_address as warehouseAddress,
           warehouse_location as warehouseLocation
    from lgs_delivery_bill
    where delivery_bill_id in
    <foreach collection="billPathIds" index="bin" item="bit" open="(" separator="," close=")">
      #{bit}
    </foreach>
    order by tree_level
  </select>

  <update id="updateStatusBackAfterReroute">
    update lgs_delivery_bill
    set status = #{status}
        , update_user = #{updateUser}
        <if test="consignQuantityChange != null">
          , consign_quantity = consign_quantity + #{consignQuantityChange}
        </if>
    where delivery_bill_id = #{deliveryBillId}
  </update>
  <select id="getSubDoingBillIdList" resultType="java.lang.String">
    select distinct delivery_bill_id
    from lgs_delivery_bill
    where parent_id = #{deliveryBillId}
    and del_flg = 0
    and leaf_flag = 0
    and delivery_bill_id != #{deliveryBillId}
    and status not in ('030590500', '030590600', '030590700', '030590800')
  </select>

  <update id="reduceCarryPlanQuantity">
    update lgs_delivery_bill
    set update_time = current_timestamp,
        update_user = #{updateUser},
        carry_plan_quantity = carry_plan_quantity - #{carryPlanQuantity},
        carry_plan_count = carry_plan_count - 1
    where delivery_bill_id in
    <foreach collection="deliveryBillIdList" item="dit" index="din" open="(" separator="," close=")">
      #{dit}
    </foreach>
    and del_flg = 0
  </update>

  <select id="queryDeliveryBillByTakeCodeAndCommodityCode" resultType="com.ecommerce.logistics.api.dto.DeliveryDetailDTO">
    select
    b.delivery_bill_id as deliveryBillId,
    b.delivery_bill_num as deliveryBillNum,
    i.seller_id as sellerId,
    i.seller_name as sellerName,
    i.buyer_id as buyerId,
    i.buyer_name as buyerName,
    i.type as type,
    b.carrier_id,
    b.carrier_name,
    b.carrier_role_type,
    concat(i.province, i.city, i.district, i.street, i.address) as receiverAddress,
    i.transport_category_id as transportCategoryId
    from lgs_delivery_bill b
    left join lgs_delivery_info i on i.delivery_info_id = b.delivery_info_id
    where b.del_flg = 0
    and b.root_flag = 1
    and i.take_code = #{takeCode}
    and i.commodity_code = #{commodityCode}
  </select>

  <select id="queryDeliveryBillByOrderCodeAndCommodityCode" resultType="com.ecommerce.logistics.api.dto.DeliveryDetailDTO">
    select
    b.delivery_bill_id as deliveryBillId,
    b.delivery_bill_num as deliveryBillNum,
    i.seller_id as sellerId,
    i.seller_name as sellerName,
    i.buyer_id as buyerId,
    i.buyer_name as buyerName,
    i.type as type,
    b.carrier_id,
    b.carrier_name,
    b.carrier_role_type,
    concat(i.province, i.city, i.district, i.street, i.address) as receiverAddress,
    i.transport_category_id as transportCategoryId
    from lgs_delivery_bill b
    left join lgs_delivery_info i on i.delivery_info_id = b.delivery_info_id
    where b.del_flg = 0
    and b.leaf_flag = 0
    and i.order_code = #{orderCode}
    and i.commodity_code = #{commodityCode}
    order by b.tree_level desc
    limit 1
  </select>

  <select id="queryDeliveryBillByConcreteWaybillId" resultType="com.ecommerce.logistics.api.dto.DeliveryDetailDTO">
    select item.delivery_bill_id as deliveryBillId,
           ldb.delivery_bill_num as deliveryBillNum,
           info.seller_id as sellerId,
           info.seller_name as sellerName,
           info.buyer_id as buyerId,
           info.buyer_name as buyerName,
           info.type as type,
           concat(info.province, info.city, info.district, info.street, info.address) as receiverAddress,
           info.transport_category_id as transportCategoryId
    from lgs_ship_bill_item as item
    left join lgs_delivery_bill as ldb on item.delivery_bill_id = ldb.delivery_bill_id
    left join lgs_delivery_info as info on item.delivery_info_id = info.delivery_info_id
  </select>

  <select id="queryWaitMergeDeliveryList" parameterType="com.ecommerce.logistics.api.dto.WaitMergeDeliveryQueryDTO"
          resultType="com.ecommerce.logistics.api.dto.DeliveryListDTO">
    select bill.delivery_bill_id as deliveryBillId,
           bill.delivery_bill_num as deliveryBillNum,
           bill.consignor_id as consignorId,
           bill.consignor_name as consignorName,
           bill.carrier_id as carrierId,
           bill.carrier_name as carrierName,
           info.buyer_id as buyerId,
           info.seller_id as sellerId,
           info.transport_category_id as transportCategoryId,
           tc.category_name as transportCategoryName,
           bill.transport_tool_type as transportToolType,
           bill.quantity as quantity,
           info.take_code as takeCode,
           info.unit as unit,
           (bill.quantity - bill.carry_plan_quantity - bill.consign_quantity) as unplannedQuantity,
           (bill.carry_plan_quantity + bill.consign_quantity) as plannedQuantity,
           (bill.carry_complete_quantity + bill.consign_complete_quantity) as completeQuantity,
           (bill.carry_send_quantity + bill.consign_send_quantity) as sendQuantity,
           bill.warehouse_id as warehouseId,
           concat(bill.warehouse_province, bill.warehouse_city, bill.warehouse_district) as warehouseAddress,
           concat(info.province, info.city, info.district) as receiveAddress,
           info.province_code as receiveProvinceCode,
           info.city_code as receiveCityCode,
           bill.payable_carriage_price as carriageUnitPrice,
           info.delivery_time as deliveryTime,
           info.delivery_time_range as deliveryTimeRange,
           bill.status as status,
           info.can_operate as canOperate,
           bill.estimate_km as distance,
           bill.warehouse_location as startLocation,
           info.location as endLocation,
           info.sync_flag as syncFlag,
           info.type as type,
           bill.root_flag as rootFlag,
           bill.bill_proxy_type as billProxyType
    from lgs_delivery_bill as bill
    left join lgs_delivery_info as info on bill.delivery_info_id = info.delivery_info_id
    left join lgs_transport_category as tc on info.transport_category_id = tc.transport_category_id
    where bill.carrier_id = #{carrierId}
      and bill.warehouse_id = #{warehouseId}
      and bill.status = '030590400'
      and bill.leaf_flag = 0
      and (bill.quantity - bill.carry_plan_quantity - bill.consign_quantity) &gt; 0
      and info.goods_id = #{goodsId}
      and info.transport_category_id = #{transportCategoryId}
      and info.transport_tool_type = #{transportToolType}
      and info.delivery_time &gt;= #{deliveryTimeStart}
      and info.delivery_time &lt;= #{deliveryTimeEnd}
      and info.can_operate = 1
      and info.sync_flag in ('030290100', '030290200')
    <choose>
      <when test="ascFlag != null and ascFlag == 1">
        order by info.delivery_time, info.delivery_time_range
      </when>
      <otherwise>
        order by info.delivery_time desc, info.delivery_time_range desc
      </otherwise>
    </choose>

  </select>

  <select id="countDeliveryCarryNotFinal" resultType="int">
    select count(lsb.waybill_id)
    from lgs_ship_bill_item as item
           left join lgs_ship_bill as lsb on item.waybill_id = lsb.waybill_id
    where item.real_delivery_bill_num = #{deliveryBillNum}
      and item.status not in ('030610600', '030610700')
      and lsb.del_flg = 0
  </select>
  <select id="statisticsDeliveryByStatusQuantity" parameterType="com.ecommerce.logistics.api.dto.DeliveryQueryDTO"
          resultType="com.ecommerce.logistics.api.dto.DeliveryStatisticsDTO">
  	SELECT
		sum( b.quantity ) AS quantity,
		sum( b.quantity - b.carry_plan_quantity - b.consign_quantity ) AS unplannedQuantity,
		sum( b.carry_plan_quantity + b.consign_quantity ) AS plannedQuantity,
		sum( b.carry_complete_quantity + b.consign_complete_quantity ) AS completeQuantity,
		sum( b.carry_send_quantity + b.consign_send_quantity ) AS sendQuantity,
		sum( b.carry_plan_count - b.carry_send_count) AS plannedNum,
		sum( b.carry_send_count - carry_complete_count) AS sendNum,
		sum( b.carry_complete_count) AS completeNum,
		b.`status` AS status
	FROM
		lgs_delivery_bill b
		LEFT JOIN lgs_delivery_info i ON i.delivery_info_id = b.delivery_info_id
	WHERE
		b.del_flg = 0
		AND b.leaf_flag = 0
		<if test="carrierId != null and carrierId != '' ">
      		  AND b.carrier_id = #{carrierId}
    	</if>
    	<if test="deliveryTimeStart != null and deliveryTimeStart != ''">
	      <![CDATA[AND i.delivery_time >= #{deliveryTimeStart}]]>
	    </if>
	    <if test="deliveryTimeEnd != null and deliveryTimeEnd != ''">
	      <![CDATA[AND i.delivery_time <= #{deliveryTimeEnd}]]>
	    </if>
		<if test="statusList != null and statusList.size() > 0">
		      AND b.status IN
		      <foreach close=")" collection="statusList" index="index" item="statusList" open="(" separator=",">
		        #{statusList}
		      </foreach>
    	</if>
    	<if test="transportToolType != null and transportToolType !=''">
    		AND i.transport_tool_type = #{transportToolType}
    	</if>
		AND ( b.quantity - b.carry_plan_quantity - b.consign_quantity ) >= 0
  </select>


  <!--紧急任务列表分页查询,只需查询出当天待安排(030590400)的数据-->
  <select id="queryUrgentTaskList" parameterType="com.ecommerce.logistics.api.dto.QueryUrgentTaskDTO"
          resultType="com.ecommerce.logistics.api.dto.DeliveryListDTO">
    select
      b.delivery_bill_id as deliveryBillId,
      b.delivery_bill_num as deliveryBillNum,
      b.consignor_id as consignorId,
      b.consignor_name as consignorName,
      b.carrier_id as carrierId,
      b.carrier_name as carrierName,
      i.buyer_id as buyerId,
      i.seller_id as sellerId,
      i.transport_category_id as transportCategoryId,
      tc.category_name as transportCategoryName,
      b.transport_tool_type as transportToolType,
      b.quantity as quantity,
      i.take_code as takeCode,
      i.unit as unit,
      (b.quantity - b.carry_plan_quantity - b.consign_quantity) as unplannedQuantity,
      (b.carry_plan_quantity + b.consign_quantity) as plannedQuantity,
      (b.carry_complete_quantity + b.consign_complete_quantity) as completeQuantity,
      (b.carry_send_quantity + b.consign_send_quantity) as sendQuantity,
      b.warehouse_id as warehouseId,
      concat(b.warehouse_province, b.warehouse_city, b.warehouse_district) as warehouseAddress,
      concat(i.province, i.city, i.district) as receiveAddress,
      i.province_code as receiveProvinceCode,
      i.city_code as receiveCityCode,
      b.payable_carriage_price as carriageUnitPrice,
      i.delivery_time as deliveryTime,
      i.delivery_time_range as deliveryTimeRange,
      b.status as status,
      i.can_operate as canOperate,
      b.warehouse_location as startLocation,
      i.location as endLocation,
      i.sync_flag as syncFlag,
      i.type as type,
      b.root_flag as rootFlag,
      b.bill_proxy_type as billProxyType
    from lgs_delivery_bill b
    left join lgs_delivery_info i on i.delivery_info_id = b.delivery_info_id
    left join lgs_transport_category tc on tc.transport_category_id = i.transport_category_id
    where b.del_flg = 0 AND b.leaf_flag = 0
    <!--只需查询出待安排(030590400)的数据-->
    AND b.status = '030590400'
    <if test="carrierId != null and carrierId != '' ">
      AND b.carrier_id = #{carrierId}
    </if>
    <if test="carrierRoleType != null and carrierRoleType != '' ">
      AND b.carrier_role_type = #{carrierRoleType}
    </if>
    <if test="deliveryTime != null">
      <![CDATA[AND i.delivery_time = #{deliveryTime}]]>
    </if>
    <if test="equalFlag == 1">
      <![CDATA[AND i.delivery_time_range <= #{deliveryTimeRange}]]>
    </if>
    <if test="equalFlag == 0">
      <![CDATA[AND i.delivery_time_range < #{deliveryTimeRange}]]>
    </if>
    AND ( b.quantity - b.carry_plan_quantity - b.consign_quantity ) > 0
    order by i.delivery_time, i.delivery_time_range
  </select>


  <!--紧急任务列表分页查询,只需查询出历史待安排(030590400)的数据-->
  <select id="queryUrgentTaskHistoryList" parameterType="com.ecommerce.logistics.api.dto.QueryUrgentTaskDTO"
          resultType="com.ecommerce.logistics.api.dto.DeliveryListDTO">
    select
      b.delivery_bill_id as deliveryBillId,
      b.delivery_bill_num as deliveryBillNum,
      b.consignor_id as consignorId,
      b.consignor_name as consignorName,
      b.carrier_id as carrierId,
      b.carrier_name as carrierName,
      i.buyer_id as buyerId,
      i.seller_id as sellerId,
      i.transport_category_id as transportCategoryId,
      tc.category_name as transportCategoryName,
      b.transport_tool_type as transportToolType,
      b.quantity as quantity,
      i.take_code as takeCode,
      i.unit as unit,
      (b.quantity - b.carry_plan_quantity - b.consign_quantity) as unplannedQuantity,
      (b.carry_plan_quantity + b.consign_quantity) as plannedQuantity,
      (b.carry_complete_quantity + b.consign_complete_quantity) as completeQuantity,
      (b.carry_send_quantity + b.consign_send_quantity) as sendQuantity,
      b.warehouse_id as warehouseId,
      concat(b.warehouse_province, b.warehouse_city, b.warehouse_district) as warehouseAddress,
      concat(i.province, i.city, i.district) as receiveAddress,
      i.province_code as receiveProvinceCode,
      i.city_code as receiveCityCode,
      b.payable_carriage_price as carriageUnitPrice,
      i.delivery_time as deliveryTime,
      i.delivery_time_range as deliveryTimeRange,
      b.status as status,
      i.can_operate as canOperate,
      b.warehouse_location as startLocation,
      i.location as endLocation,
      i.sync_flag as syncFlag,
      i.type as type,
      b.root_flag as rootFlag,
      b.bill_proxy_type as billProxyType
    from lgs_delivery_bill b
    left join lgs_delivery_info i on i.delivery_info_id = b.delivery_info_id
    left join lgs_transport_category tc on tc.transport_category_id = i.transport_category_id
    where b.del_flg = 0 AND b.leaf_flag = 0
    <!--只需查询出待安排(030590400)的数据-->
    AND b.status = '030590400'
    <if test="carrierId != null and carrierId != '' ">
      AND b.carrier_id = #{carrierId}
    </if>
    <if test="carrierRoleType != null and carrierRoleType != '' ">
      AND b.carrier_role_type = #{carrierRoleType}
    </if>
    <if test="deliveryTime != null">
      <![CDATA[AND i.delivery_time < #{deliveryTime}]]>
    </if>
    AND ( b.quantity - b.carry_plan_quantity - b.consign_quantity ) > 0
    order by i.delivery_time desc, i.delivery_time_range desc
  </select>

  <select id="queryDeliveryLocation" resultType="com.ecommerce.logistics.dao.dto.BillLocationDO">
    select ifnull(bill.warehouse_location, info.warehouse_location) as warehouseLocation,
           info.location as receiveLocation
    from lgs_delivery_bill as bill
    left join lgs_delivery_info as info on bill.delivery_info_id = info.delivery_info_id
    where bill.delivery_bill_id = #{deliveryBillId}
  </select>

  <select id="queryMemberInfoForCheckRule" parameterType="com.ecommerce.logistics.api.dto.DeliverySimpleQueryDTO" resultType="com.ecommerce.logistics.api.dto.DeliverySimpleDTO">
    select
        info.buyer_id as buyerId,
        info.buyer_name as buyerName,
        bill.carrier_id as carrierId,
        bill.carrier_name as carrierName
    from lgs_delivery_bill bill
    left join lgs_delivery_info as info on info.delivery_info_id = bill.delivery_info_id
    where bill.del_flg = 0 and info.del_flg = 0 and leaf_flag = 0
    <if test="transportToolType != null and transportToolType != ''">
      and bill.transport_tool_type = #{transportToolType}
    </if>
    <if test="consignorId != null and consignorId != ''">
        and bill.consignor_id = #{consignorId}
    </if>
    <if test="carrierId != null and carrierId != ''">
        and bill.carrier_id = #{carrierId} and info.type != '030060200'
    </if>
  </select>

  <!--查询出每天运力总和-->
  <select id="queryCapacityCount" parameterType="com.ecommerce.logistics.api.dto.auto.TransportCapacityDTO"
          resultType="com.ecommerce.logistics.api.dto.auto.TransportCapacityResultDTO">
    SELECT
      sum(t2.quantity) AS quantity,
      t2.delivery_info_id AS deliveryInfoId,
      t2.carrier_id AS carrierId,
      t2.consignor_id AS consignorId,
      t1.delivery_time AS deliveryTime
    FROM lgs_delivery_info t1
    LEFT JOIN lgs_delivery_bill t2 ON t1.delivery_info_id = t2.delivery_info_id
    WHERE t1.del_flg = 0 AND t2.del_flg = 0
    <if test="consignorId != null and consignorId != ''">
      AND t2.consignor_id = #{consignorId}
    </if>
    <if test="carrierId != null and carrierId != ''">
      AND t2.carrier_id = #{carrierId}
      AND t2.carrier_role_type = #{carrierRoleType}
    </if>
    <if test="deliveryTime != null">
      <![CDATA[AND t1.delivery_time = #{deliveryTime}]]>
    </if>
    LIMIT 1
  </select>

  <!--获取每天已经安排的车辆数，一个运单对应一个车次-->
  <select id="getShipBillNum" parameterType="com.ecommerce.logistics.api.dto.auto.TransportCapacityDTO"
          resultType="com.ecommerce.logistics.api.dto.auto.TransportCapacityResultDTO">
    SELECT
      COUNT( DISTINCT t3.waybill_id ) AS canTakeOrderNum
    FROM lgs_delivery_bill t1
    LEFT JOIN lgs_ship_bill_item t2 ON t1.delivery_bill_num = t2.real_delivery_bill_num
    LEFT JOIN lgs_ship_bill t3 ON t2.waybill_id = t3.waybill_id
    WHERE
      t2.can_operate = 1
    AND t2.seq_num = t3.max_seq_num
    AND t1.leaf_flag = 0
    AND t3.STATUS IN ( '030600600', '030600700', '030600800', '030600900' )
    <if test="deliveryTime != null">
      <![CDATA[AND t3.delivery_time = #{deliveryTime}]]>
    </if>
    <if test="consignorId != null and consignorId != ''">
      AND t1.consignor_id = #{consignorId}
    </if>
    <if test="carrierId != null and carrierId != ''">
      AND t1.carrier_id = #{carrierId}
      AND t1.carrier_role_type = #{carrierRoleType}
    </if>
    ORDER BY t1.create_time DESC
  </select>

  <select id="selectPlanDeliveryByIdList" resultType="com.ecommerce.logistics.api.dto.auto.PlanDeliveryListDTO">
    select bill.delivery_bill_id as deliveryBillId,
           bill.delivery_bill_num as deliveryBillNum,
           info.take_code as takeCode,
           bill.carrier_id as carrierId,
           bill.carrier_name as carrierName,
           bill.carrier_role_type as carrierRoleType,
           bill.consignor_id as consignorId,
           bill.consignor_name as consignorName,
           info.buyer_id as buyerId,
           info.seller_id as sellerId,
           info.transport_tool_type as transportToolType,
           bill.quantity as quantity,
           info.unit as unit,
           (bill.quantity - bill.carry_plan_quantity - bill.consign_quantity) as unplannedQuantity,
           (bill.carry_plan_quantity + bill.consign_quantity) as plannedQuantity,
           (bill.carry_complete_quantity + bill.consign_complete_quantity) as completeQuantity,
           (bill.carry_send_quantity + bill.consign_send_quantity) as sendQuantity,
           bill.warehouse_id as warehouseId,
           bill.warehouse_name as warehouseName,
           concat(bill.warehouse_province, bill.warehouse_city, bill.warehouse_district) as warehouseAddress,
           info.receiver_address_id as receiverAddressId,
           concat(info.province, info.city, info.district) as receiveAddress,
           info.province_code as receiveProvinceCode,
           info.city_code as receiveCityCode,
           bill.payable_carriage_price as carriageUnitPrice,
           info.delivery_time as deliveryTime,
           info.delivery_time_range as deliveryTimeRange,
           bill.status as status,
           info.can_operate as canOperate,
           bill.warehouse_location as startLocation,
           info.location as endLocation,
           bill.estimate_km as distance,
           bill.estimate_duration as estimateDuration,
           info.goods_id as goodsId,
           info.note as goodsName
    from lgs_delivery_bill as bill
    left join lgs_delivery_info as info on bill.delivery_info_id = info.delivery_info_id
    where bill.delivery_bill_id in
    <foreach collection="deliveryBillIdList" index="din" item="dit" open="(" separator="," close=")">
      #{dit}
    </foreach>
    and bill.del_flg = 0 and info.del_flg = 0
    order by bill.delivery_bill_num
  </select>

  <select id="queryDeliveryListById" resultMap="BaseResultMap">
    select *
    from lgs_delivery_bill
    where del_flg = 0
    and delivery_bill_id in
    <foreach collection="pathBillIdList" index="din" item="dit" open="(" separator="," close=")">
      #{dit}
    </foreach>
  </select>

  <!--已发货：获取当天已发货但是未完成的数量-->
  <select id="getSameDayQuantity" parameterType="com.ecommerce.logistics.api.dto.DeliveryQueryDTO" resultType="com.ecommerce.logistics.dao.dto.shipbill.ShipBillQuantityDTO">
    SELECT
      SUM(actual_quantity) AS quantity,
      status AS status
    FROM lgs_ship_bill
    WHERE
      carrier_id = #{carrierId}
    <if test="deliveryTimeStart != null">
      <![CDATA[ AND leave_warehouse_time >= #{deliveryTimeStart} ]]>
    </if>
    <if test="deliveryTimeEnd != null">
      <![CDATA[ AND leave_warehouse_time <= #{deliveryTimeEnd} ]]>
    </if>
    AND `status` in ('030600700','030600800','030600900')
    AND del_flg = 0
    GROUP BY status
  </select>

  <select id="queryContractInfo" resultType="com.ecommerce.logistics.api.dto.waybill.ContractSubmitInfoDTO">
    select
       bill.carrier_name as carrier,
       bill.create_time as dateSigned,
       bill.quantity as cargoFreight,
       bill.warehouse_province as placeOfLoadingProvince,
       bill.warehouse_city as placeOfLoadingCity,
       bill.warehouse_district as placeOfLoadingDistrict,
       info.province as placeOfReceiptProvince,
       info.city as placeOfReceiptCity,
       info.district as placeOfReceiptDistrict,
       bill.estimate_km as transportDistance,
       ROUND(bill.payable_carriage_price * bill.complete_quantity,3) as contractAmount
    from lgs_delivery_bill bill
    left join lgs_delivery_info info on info.delivery_info_id = bill.delivery_info_id
    where bill.del_flg = 0
    and bill.delivery_bill_num = #{deliveryBillNum}
  </select>

  <update id="reduceCarryForConcrete">
    update lgs_delivery_bill
    set quantity = if(root_flag = 1, quantity, quantity - #{reduceQuantity}),
        carry_plan_quantity = carry_plan_quantity - #{reduceQuantity},
        carry_send_quantity = carry_send_quantity = #{reduceQuantity},
        update_time = current_timestamp,
        update_user = #{updateUser}
    where delivery_bill_id in
    <foreach collection="carryDeliveryBillIdList" index="cin" item="cit" open="(" separator="," close=")">
      #{cit}
    </foreach>
    and del_flg = 0
  </update>

  <update id="reduceConsignForConcrete">
    update lgs_delivery_bill
    set quantity = if(root_flag = 1, quantity, quantity - #{reduceQuantity}),
    consign_quantity = consign_quantity - #{reduceQuantity},
    consign_accept_quantity = consign_accept_quantity = #{reduceQuantity},
    consign_send_quantity = consign_send_quantity = #{reduceQuantity},
    update_time = current_timestamp,
    update_user = #{updateUser}
    where delivery_bill_id in
    <foreach collection="consignDeliveryBillIdList" index="cin" item="cit" open="(" separator="," close=")">
      #{cit}
    </foreach>
    and del_flg = 0
  </update>

  <update id="updatePlanToQuantity">
    update lgs_delivery_bill
    set quantity = carry_plan_quantity + consign_quantity,
        update_time = current_timestamp,
        update_user = #{updateUser}
    where delivery_bill_id in
    <foreach collection="deliveryBillIdList" index="din" item="dit" open="(" separator="," close=")">
      #{dit}
    </foreach>
    and quantity &lt; (carry_plan_quantity + consign_quantity)
  </update>

  <update id="addQuantity">
    update lgs_delivery_bill
    set quantity = quantity + #{addQuantity},
        update_time = current_timestamp,
        update_user = #{updateUser}
    where delivery_bill_id in
    <foreach collection="deliveryBillIdList" index="din" item="dit" open="(" separator="," close=")">
      #{dit}
    </foreach>
  </update>
  <select id="queryDeliverBillId" resultType="com.ecommerce.logistics.api.dto.LogisticsAdjustPriceMemberDTO" parameterType="com.ecommerce.logistics.api.dto.LogisticsAdjustPriceDTO">
	SELECT
		bill.consignor_id AS entrustingSideId,
		bill.consignor_name AS entrustingSideName,
		bill.carrier_id AS entrustedSideId,
		bill.carrier_name AS entrustedSideName,
		bill.payable_carriage_price AS originPrice,
		bill.carriage_unit_price AS carriageUnitPrice,
		bill.auxiliary_price AS auxiliaryPrice,
		info.goods_id AS goodsId,
		info.note AS goodsName,
		info.price AS goodsPrice,
		info.province AS province,
		info.province_code AS provinceCode,
		info.city AS city,
		info.city_code AS cityCode,
		info.district AS district,
		info.district_code AS districtCode,
		info.street AS street,
		info.street_code AS streetCode,
		info.address AS address
	FROM
		lgs_delivery_bill AS bill,
		lgs_delivery_info AS info
	WHERE
		bill.del_flg = 0
		AND bill.delivery_info_id = info.delivery_info_id
		<if test="adjustType != null and adjustType == 0">
			AND bill.carrier_id = #{entrustedSideId}
		</if>
		<if test="adjustType != null and adjustType == 1">
			AND bill.consignor_id = #{entrustingSideId}
		</if>
		<if test="adjustType != null and adjustType == 2">
			AND (bill.consignor_id = #{entrustingSideId} OR bill.carrier_id = #{entrustedSideId})
		</if>
		AND bill.transport_tool_type = #{transportType}
		<if test="transportType == '030230100'">
			AND bill.warehouse_id = #{warehouseId}
		</if>
		<if test="transportType == '030230200'">
			AND info.load_port_id = #{warehouseId}
		</if>
		AND info.transport_category_id = #{transportCategoryId}
		<if test="provinceCode != null and provinceCode != ''">
			AND info.province_code = #{provinceCode}
		</if>
		<if test="cityCode != null and cityCode != ''">
			AND info.city_code = #{cityCode}
		</if>
		<if test="districtCode != null and districtCode != ''">
			AND info.district_code = #{districtCode}
		</if>
		<if test="address != null and address != ''">
			AND info.address = #{address}
		</if>
		GROUP BY bill.warehouse_id,info.province_code,info.city_code,info.district_code,carrier_id,consignor_id,goods_id
  </select>

  <select id="queryLeafFlagDeliverBillId" resultType="String" parameterType="String">
  	SELECT
		bill.delivery_bill_id
	FROM
		lgs_delivery_bill bill
	WHERE
		bill.root_flag = 0
		AND bill.leaf_flag = 1
		AND bill.node_id_path LIKE CONCAT('%',#{deliveryBillId},'%')
  </select>
  <update id="updateDeliveryInfoCarrier">
    UPDATE lgs_delivery_bill
    SET carrier_id = #{carrierId},
        carrier_name = #{carrierName},
        carrier_role_type = '030560300'
    WHERE delivery_info_id = #{deliveryInfoId}
    AND del_flg = 0
  </update>

  <select id="queryBillByBillNum" resultType="com.ecommerce.logistics.dao.vo.DeliveryBill">
    SELECT * FROM
    lgs_delivery_bill
    WHERE
        del_flg = 0
    AND delivery_bill_num = #{deliveryBillNum}
    LIMIT 1
  </select>
  
  <select id="getDeliverBillId" parameterType="com.ecommerce.logistics.api.dto.LogisticsAdjustPriceMemberDTO" resultType="java.lang.String">
    SELECT
      bill.delivery_bill_id
    FROM
      lgs_delivery_bill AS bill,
      lgs_delivery_info AS info
    WHERE
      bill.del_flg = 0
      AND bill.delivery_info_id = info.delivery_info_id
      AND bill.carrier_id = #{entrustedSideId}
      AND bill.consignor_id = #{entrustingSideId}
      AND bill.transport_tool_type = #{transportType}
    <if test="transportType == '030230100'">
      AND bill.warehouse_id = #{warehouseId}
    </if>
    <if test="transportType == '030230200'">
      AND info.load_port_id = #{warehouseId}
    </if>
    AND info.transport_category_id = #{transportCategoryId}
    <if test="provinceCode != null and provinceCode != ''">
      AND info.province_code = #{provinceCode}
    </if>
    <if test="cityCode != null and cityCode != ''">
      AND info.city_code = #{cityCode}
    </if>
    <if test="districtCode != null and districtCode != ''">
      AND info.district_code = #{districtCode}
    </if>
    <if test="address != null and address != ''">
      AND info.address = #{address}
    </if>
      AND info.goods_id = #{goodsId}
  </select>

</mapper>
