<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.DeliveryInfoMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.DeliveryInfo">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="delivery_info_id" jdbcType="VARCHAR" property="deliveryInfoId" />
    <result column="parent_info_id" jdbcType="VARCHAR" property="parentInfoId" />
    <result column="buyer_id" jdbcType="VARCHAR" property="buyerId" />
    <result column="buyer_name" jdbcType="VARCHAR" property="buyerName" />
    <result column="seller_id" jdbcType="VARCHAR" property="sellerId" />
    <result column="seller_name" jdbcType="VARCHAR" property="sellerName" />
    <result column="mdm_code" jdbcType="VARCHAR" property="mdmCode" />
    <result column="bill_type" jdbcType="TINYINT" property="billType" />
    <result column="take_code" jdbcType="VARCHAR" property="takeCode" />
    <result column="order_code" jdbcType="VARCHAR" property="orderCode" />
    <result column="order_item_id" jdbcType="VARCHAR" property="orderItemId" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="relation_type" jdbcType="VARCHAR" property="relationType" />
    <result column="bill_proxy_type" jdbcType="VARCHAR" property="billProxyType" />
    <result column="can_operate" jdbcType="TINYINT" property="canOperate" />
    <result column="if_agree_dispatch" jdbcType="TINYINT" property="ifAgreeDispatch" />
    <result column="base_preference" jdbcType="VARCHAR" property="basePreference" />
    <result column="load_port_id" jdbcType="VARCHAR" property="loadPortId" />
    <result column="load_port_name" jdbcType="VARCHAR" property="loadPortName" />
    <result column="unload_port_id" jdbcType="VARCHAR" property="unloadPortId" />
    <result column="unload_port_name" jdbcType="VARCHAR" property="unloadPortName" />
    <result column="warehouse_id" jdbcType="VARCHAR" property="warehouseId" />
    <result column="warehouse_type" jdbcType="VARCHAR" property="warehouseType" />
    <result column="warehouse_name" jdbcType="VARCHAR" property="warehouseName" />
    <result column="warehouse_province" jdbcType="VARCHAR" property="warehouseProvince" />
    <result column="warehouse_province_code" jdbcType="VARCHAR" property="warehouseProvinceCode" />
    <result column="warehouse_city" jdbcType="VARCHAR" property="warehouseCity" />
    <result column="warehouse_city_code" jdbcType="VARCHAR" property="warehouseCityCode" />
    <result column="warehouse_district" jdbcType="VARCHAR" property="warehouseDistrict" />
    <result column="warehouse_district_code" jdbcType="VARCHAR" property="warehouseDistrictCode" />
    <result column="warehouse_street" jdbcType="VARCHAR" property="warehouseStreet" />
    <result column="warehouse_street_code" jdbcType="VARCHAR" property="warehouseStreetCode" />
    <result column="warehouse_address" jdbcType="VARCHAR" property="warehouseAddress" />
    <result column="warehouse_location" jdbcType="VARCHAR" property="warehouseLocation" />
    <result column="receiver_address_id" jdbcType="VARCHAR" property="receiverAddressId" />
    <result column="receiver" jdbcType="VARCHAR" property="receiver" />
    <result column="receiver_phone" jdbcType="VARCHAR" property="receiverPhone" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="district" jdbcType="VARCHAR" property="district" />
    <result column="district_code" jdbcType="VARCHAR" property="districtCode" />
    <result column="street" jdbcType="VARCHAR" property="street" />
    <result column="street_code" jdbcType="VARCHAR" property="streetCode" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="location" jdbcType="VARCHAR" property="location" />
    <result column="delivery_time" jdbcType="DATE" property="deliveryTime" />
    <result column="delivery_time_range" jdbcType="VARCHAR" property="deliveryTimeRange" />
    <result column="sync_flag" jdbcType="VARCHAR" property="syncFlag" />
    <result column="transport_tool_type" jdbcType="VARCHAR" property="transportToolType" />
    <result column="deals_id" jdbcType="VARCHAR" property="dealsId" />
    <result column="deals_name" jdbcType="VARCHAR" property="dealsName" />
    <result column="project_name" jdbcType="VARCHAR" property="projectName" />
    <result column="data_source" jdbcType="VARCHAR" property="dataSource" />
    <result column="carry_flag" jdbcType="TINYINT" property="carryFlag" />
    <result column="height_limit" jdbcType="DECIMAL" property="heightLimit" />
    <result column="unload_require_flag" jdbcType="INTEGER" property="unloadRequireFlag" />
    <result column="carry_require_flag" jdbcType="INTEGER" property="carryRequireFlag" />
    <result column="transport_category_id" jdbcType="VARCHAR" property="transportCategoryId" />
    <result column="commodity_code" jdbcType="VARCHAR" property="commodityCode" />
    <result column="vendor" jdbcType="VARCHAR" property="vendor" />
    <result column="product_img" jdbcType="VARCHAR" property="productImg" />
    <result column="goods_id" jdbcType="VARCHAR" property="goodsId" />
    <result column="note" jdbcType="VARCHAR" property="note" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="carriage_unit_price" jdbcType="DECIMAL" property="carriageUnitPrice" />
    <result column="unloading_flow_code" jdbcType="VARCHAR" property="unloadingFlowCode" />
    <result column="need_monitor" jdbcType="TINYINT" property="needMonitor" />
    <result column="sign_type" jdbcType="VARCHAR" property="signType" />
    <result column="signer" jdbcType="VARCHAR" property="signer" />
    <result column="signer_phone" jdbcType="VARCHAR" property="signerPhone" />
    <result column="sale_region_path" jdbcType="VARCHAR" property="saleRegionPath" />
    <result column="salesman_id" jdbcType="VARCHAR" property="salesmanId" />
    <result column="salesman_name" jdbcType="VARCHAR" property="salesmanName" />
    <result column="special_flag" jdbcType="TINYINT" property="specialFlag" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="version" jdbcType="BIGINT" property="version" />
  </resultMap>

  <select id="selectParentInfoId" resultType="java.lang.String">
    select parent_info_id
    from lgs_delivery_info where delivery_info_id = #{deliveryInfoId} and del_flg = 0
  </select>

  <select id="selectProxyInfoId" resultType="java.lang.String">
    select delivery_info_id
    from lgs_delivery_info where parent_info_id = #{parentInfoId} and delivery_info_id != #{deliveryInfoId} and del_flg = 0
  </select>

  <select id="selectProxyTakeCode" resultType="java.lang.String">
    select distinct take_code
    from lgs_delivery_info where parent_info_id = #{parentInfoId} and delivery_info_id != #{deliveryInfoId} and del_flg = 0
  </select>

  <select id="queryDeliveryByTakeCodeAndOrderItemId" parameterType="java.lang.String" resultType="com.ecommerce.logistics.api.dto.DeliveryFinishTakeDTO">
    select
       i.take_code as takeCode,
       i.order_item_id as orderItemId,
       b.complete_quantity as completeQuantity,
       i.carriage_unit_price as carriageUnitPrice
    from lgs_delivery_info i
    left join lgs_delivery_bill b on b.delivery_info_id = i.delivery_info_id
    where b.del_flg = 0 and i.take_code = #{takeCode} and i.order_item_id = #{orderItemId} and b.root_flag = 1
  </select>

  <select id="queryOrderItemIdByTakeCode" parameterType="java.lang.String" resultType="java.lang.String">
    select
    distinct i.order_item_id
    from lgs_delivery_info i
    where i.take_code = #{takeCode}
    and i.del_flg = 0
  </select>

  <select id="queryInfoByTakeCode" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    *
    from lgs_delivery_info i
    left join lgs_delivery_bill b on b.delivery_info_id = i.delivery_info_id
    where i.take_code = #{takeCode}
    and b.leaf_flag = 0
    and i.del_flg = 0
  </select>

  <select id="queryTakeCodeByDeliveryBillId" parameterType="java.lang.String" resultType="java.lang.String">
    select
    distinct i.take_code
    from lgs_delivery_info i
    left join lgs_delivery_bill b on b.delivery_info_id = i.delivery_info_id
    where b.delivery_bill_id = #{deliveryBillId}
    and i.del_flg = 0
  </select>

   <select id="queryInfoByDeliveryBillId" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    i.*
    from lgs_delivery_info i
    left join lgs_delivery_bill b on b.delivery_info_id = i.delivery_info_id
    where b.delivery_bill_id = #{deliveryBillId}
    and i.del_flg = 0
  </select>

  <select id="selectTakeCodeByInfoList" resultType="java.lang.String">
    select distinct take_code
    from lgs_delivery_info
    where delivery_info_id in
    <foreach collection="deliveryInfoIdList" index="din" item="dit" open="(" separator="," close=")">
      #{dit}
    </foreach>
    and del_flg = 0
  </select>

  <select id="selectCanOperateByWaybillId" resultMap="BaseResultMap">
    select info.*
    from lgs_ship_bill as lsb
    left join lgs_ship_bill_item as item on lsb.waybill_id = item.waybill_id and lsb.max_seq_num = item.seq_num and item.can_operate = 1
    left join lgs_delivery_info as info on item.delivery_info_id = info.delivery_info_id
    where lsb.waybill_id = #{waybillId}
  </select>

  <select id="sumSignQuantity" resultType="java.math.BigDecimal">
    select sum(bill.sign_quantity)
    from lgs_ship_bill_item as item
    left join lgs_ship_bill as bill on bill.waybill_id = item.waybill_id and bill.max_seq_num = item.seq_num
    where item.delivery_info_id = #{deliveryInfoId} and bill.del_flg = 0 and bill.status = '030600900'
  </select>


  <select id="selectDeliveryInfo" parameterType="java.lang.String" resultMap="BaseResultMap">
    SELECT
      t1.*
    FROM
      lgs_delivery_info t1
    LEFT JOIN lgs_delivery_bill t2 ON t1.delivery_info_id = t2.delivery_info_id
    WHERE
      t2.delivery_bill_id = #{deliveryBillId}
    AND t1.del_flg = 0 AND t2.del_flg = 0
    LIMIT 1
  </select>


  <select id="queryProxyInfoList" parameterType="java.lang.String" resultType="com.ecommerce.logistics.api.dto.waybill.DeliveryInfoProxyDTO">
    SELECT
      t1.parent_info_id AS parentInfoId,
      t1.take_code AS takeCode,
      t1.order_item_id AS orderItemId,
      t1.bill_proxy_type AS billProxyType,
      t1.goods_id AS goodsId,
      t2.origin_price AS goodsPrice,
      t2.receivable_carriage_price AS receivableCarriagePrice,
      t2.origin_price AS originPrice
    FROM lgs_delivery_info t1
    LEFT JOIN lgs_ship_bill_item t2 ON t1.delivery_info_id = t2.delivery_info_id
    WHERE t1.parent_info_id = #{parentInfoId}
    AND t1.del_flg = 0 AND t2.del_flg = 0
  </select>

  <select id="selectDeliveryInfoByWaybillId" resultMap="BaseResultMap">
    select info.*
    from lgs_ship_bill as lsb
    left join lgs_ship_bill_item as item on lsb.waybill_id = item.waybill_id and lsb.max_seq_num = item.seq_num
    left join lgs_delivery_info as info on item.delivery_info_id = info.delivery_info_id
    where lsb.del_flg = 0 AND item.del_flg = 0 AND info.del_flg = 0
    AND lsb.waybill_id = #{waybillId}
    LIMIT 1
  </select>

  <select id="selectRealBuyerDeliveryInfoByWaybillId" resultMap="BaseResultMap">
    select info.*
    from lgs_ship_bill as lsb
    left join lgs_ship_bill_item as item on lsb.waybill_id = item.waybill_id and lsb.max_seq_num = item.seq_num
    left join lgs_delivery_info as info on item.delivery_info_id = info.delivery_info_id
    where lsb.waybill_id = #{waybillId} and lsb.del_flg = 0
      and item.bill_proxy_type in ('030400300', '030400100') and item.del_flg = 0
      and info.del_flg = 0
    order by item.bill_proxy_type desc
    limit 1
  </select>
  <select id="selectRealSellerByWaybillId" resultType="java.lang.String">
    select info.seller_id
    from lgs_ship_bill as lsb
           left join lgs_ship_bill_item as item on lsb.waybill_id = item.waybill_id and lsb.max_seq_num = item.seq_num
           left join lgs_delivery_info as info on item.delivery_info_id = info.delivery_info_id
    where lsb.waybill_id = #{waybillId} and lsb.del_flg = 0
      and item.bill_proxy_type in ('030400200', '030400100') and item.del_flg = 0
      and info.del_flg = 0
    order by item.bill_proxy_type desc
    limit 1
  </select>

  <update id="updatePriceByTakeCode" parameterType="com.ecommerce.logistics.api.dto.adjust.AdjustTakeInfoDTO">
    update lgs_delivery_info
    set price = #{newestPrice}, update_time = current_timestamp
    where take_code = #{takeCode}
  </update>

</mapper>
