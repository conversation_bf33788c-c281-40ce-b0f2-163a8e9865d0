<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.DeliveryNoteMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.DeliveryNote">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="delivery_note_id" jdbcType="VARCHAR" property="deliveryNoteId" />
    <result column="delivery_seq" jdbcType="VARCHAR" property="deliverySeq" />
    <result column="seller_id" jdbcType="VARCHAR" property="sellerId" />
    <result column="seller_name" jdbcType="VARCHAR" property="sellerName" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="receive_warehouse_id" jdbcType="VARCHAR" property="receiveWarehouseId" />
    <result column="receive_warehouse_name" jdbcType="VARCHAR" property="receiveWarehouseName" />
    <result column="receive_storehouse_number" jdbcType="VARCHAR" property="receiveStorehouseNumber" />
    <result column="delivery_time" jdbcType="DATE" property="deliveryTime" />
    <result column="delivery_time_range" jdbcType="VARCHAR" property="deliveryTimeRange" />
    <result column="vehicle_id" jdbcType="VARCHAR" property="vehicleId" />
    <result column="vehicle_number" jdbcType="VARCHAR" property="vehicleNumber" />
    <result column="delivery_name" jdbcType="VARCHAR" property="deliveryName" />
    <result column="delivery_phone" jdbcType="VARCHAR" property="deliveryPhone" />
    <result column="sign_time" jdbcType="TIMESTAMP" property="signTime" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="version" jdbcType="BIGINT" property="version" />
  </resultMap>
  
  <resultMap id="detailMap" type="com.ecommerce.logistics.api.dto.delivery.DeliveryNoteDetailDTO">
    <result column="delivery_note_id" jdbcType="VARCHAR" property="deliveryNoteId" />
    <result column="delivery_seq" jdbcType="VARCHAR" property="deliverySeq" />
    <result column="seller_id" jdbcType="VARCHAR" property="sellerId" />
    <result column="seller_name" jdbcType="VARCHAR" property="sellerName" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="receive_warehouse_id" jdbcType="VARCHAR" property="receiveWarehouseId" />
    <result column="receive_warehouse_name" jdbcType="VARCHAR" property="receiveWarehouseName" />
    <result column="receive_storehouse_number" jdbcType="VARCHAR" property="receiveStorehouseNumber" />
    <result column="delivery_time" jdbcType="DATE" property="deliveryTime" />
    <result column="delivery_time_range" jdbcType="VARCHAR" property="deliveryTimeRange" />
    <result column="vehicle_id" jdbcType="VARCHAR" property="vehicleId" />
    <result column="vehicle_number" jdbcType="VARCHAR" property="vehicleNumber" />
    <result column="delivery_name" jdbcType="VARCHAR" property="deliveryName" />
    <result column="delivery_phone" jdbcType="VARCHAR" property="deliveryPhone" />
    <result column="sign_time" jdbcType="TIMESTAMP" property="signTime" />
    <collection property="deliveryNoteDetailItemList" columnPrefix="d_"
                ofType="com.ecommerce.logistics.api.dto.delivery.item.DeliveryNoteDetailItem">
      <result column="send_warehouse_id" jdbcType="VARCHAR" property="sendWarehouseId" />
      <result column="send_warehouse_name" jdbcType="VARCHAR" property="sendWarehouseName" />
      <result column="send_storehouse_number" jdbcType="VARCHAR" property="sendStorehouseNumber" />
      <result column="send_quantity" jdbcType="DECIMAL" property="sendQuantity" />
      <result column="quantity" jdbcType="DECIMAL" property="quantity" />
      <result column="leave_quantity" jdbcType="DECIMAL" property="leaveQuantity" />
      <result column="sign_quantity" jdbcType="DECIMAL" property="signQuantity" />
      <result column="status" jdbcType="VARCHAR" property="status" />
      <result column="map_id" jdbcType="VARCHAR" property="mapId" />
      <result column="product_info_id" jdbcType="VARCHAR" property="productInfoId" />
      <result column="product_id" jdbcType="VARCHAR" property="productId" />
      <result column="product_name" jdbcType="VARCHAR" property="productName" />
      <result column="note" jdbcType="VARCHAR" property="note" />
      <result column="unit" jdbcType="VARCHAR" property="unit" />
    </collection>
  </resultMap>
  
  <select id="queryDetail" parameterType="com.ecommerce.logistics.api.dto.delivery.DeliveryNoteDetailQueryDTO" resultMap="detailMap">
    select
      dn.delivery_note_id,
      dn.delivery_seq,
      dn.seller_id,
      dn.seller_name,
      dn.status,
      dn.receive_warehouse_id,
      dn.receive_warehouse_name,
      dn.receive_storehouse_number,
      dn.delivery_time,
      dn.delivery_time_range,
      dn.vehicle_id,
      dn.vehicle_number,
      dn.delivery_name,
      dn.delivery_phone,
      dn.sign_time,
      map.send_warehouse_id as d_send_warehouse_id,
      map.send_warehouse_name as d_send_warehouse_name,
      map.send_storehouse_number as d_send_storehouse_number,
      map.send_quantity as d_send_quantity,
      map.leave_quantity as d_leave_quantity,
      map.sign_quantity as d_sign_quantity,
      map.status as d_status,
      info.product_info_id as d_product_info_id,
      info.product_id as d_product_id,
      info.note as d_product_name,
      info.note as d_note,
      map.send_quantity as d_quantity,
      info.unit as d_unit,
      map.map_id as d_map_id
    from lgs_delivery_note dn, lgs_delivery_send_map map, lgs_product_info info
    where dn.del_flg = 0 and map.del_flg = 0 and info.del_flg = 0
    and dn.delivery_note_id = map.delivery_note_id and map.product_info_id = info.product_info_id
    <if test="deliveryNoteId != null and deliveryNoteId != ''">
      and dn.delivery_note_id = #{deliveryNoteId}
    </if>
    <if test="deliverySeq != null and deliverySeq != ''">
      and dn.delivery_seq = #{deliverySeq}
    </if>
    <if test="sellerId != null and sellerId != ''">
      and dn.seller_id = #{sellerId}
    </if>
  </select>


  <select id="queryMainList" parameterType="com.ecommerce.logistics.api.dto.delivery.DeliveryNoteQueryDTO"
          resultType="com.ecommerce.logistics.api.dto.delivery.DeliveryNoteListDTO">
    select distinct
    dn.delivery_note_id as deliveryNoteId,
    dn.delivery_seq as deliverySeq,
    dn.seller_id as sellerId,
    dn.seller_name as sellerName,
    dn.vehicle_number as vehicleNumber,
    dn.delivery_time as deliveryTime,
    dn.delivery_time_range as deliveryTimeRange,
    dn.receive_warehouse_name as receiveWarehouseName,
    dn.status,
    dn.sign_time as signTime
    from lgs_delivery_note dn, lgs_delivery_send_map map, lgs_product_info info
    where dn.del_flg = 0 and map.del_flg = 0 and info.del_flg = 0
    AND dn.delivery_note_id = map.delivery_note_id AND map.product_info_id = info.product_info_id
    <if test="deliveryNoteId != null and deliveryNoteId != ''">
      and dn.delivery_note_id = #{deliveryNoteId}
    </if>
    <if test="deliverySeq != null and deliverySeq != ''">
      and dn.delivery_seq = #{deliverySeq}
    </if>
    <if test="sellerId != null and sellerId != ''">
      and dn.seller_id = #{sellerId}
    </if>
    <if test="status != null and status != ''">
      and dn.status = #{status}
    </if>
    <if test="deliveryTimeStr != null and deliveryTimeStr != ''">
      and dn.delivery_time = #{deliveryTimeStr}
    </if>
    <if test="signTimeStr != null and signTimeStr != ''">
      and dn.sign_time like concat('%', #{signTimeStr}, '%')
    </if>
    <if test="productNameLike != null and productNameLike != ''">
      and info.note like concat('%', #{productNameLike}, '%')
    </if>
    <if test="receiveWarehouseNameLike != null and receiveWarehouseNameLike != ''">
      and dn.receive_warehouse_name like concat('%', #{receiveWarehouseNameLike}, '%')
    </if>
    <if test="vehicleNumberLike != null and vehicleNumberLike != ''">
      and dn.vehicle_number like concat('%', #{vehicleNumberLike}, '%')
    </if>
    order by dn.update_time desc
  </select>
  
</mapper>