<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.DeliverySendMapMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.DeliverySendMap">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="map_id" jdbcType="VARCHAR" property="mapId" />
    <result column="product_info_id" jdbcType="VARCHAR" property="productInfoId" />
    <result column="delivery_note_id" jdbcType="VARCHAR" property="deliveryNoteId" />
    <result column="send_warehouse_id" jdbcType="VARCHAR" property="sendWarehouseId" />
    <result column="send_warehouse_name" jdbcType="VARCHAR" property="sendWarehouseName" />
    <result column="send_storehouse_number" jdbcType="VARCHAR" property="sendStorehouseNumber" />
    <result column="send_order" jdbcType="INTEGER" property="sendOrder" />
    <result column="send_quantity" jdbcType="DECIMAL" property="sendQuantity" />
    <result column="unload_quantity" jdbcType="DECIMAL" property="unloadQuantity" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="version" jdbcType="BIGINT" property="version" />
  </resultMap>

  <resultMap id="deliveryNoteProductMap" type="com.ecommerce.logistics.dao.dto.delivery.DeliveryNoteProductItem">
    <result column="delivery_note_id" jdbcType="VARCHAR" property="deliveryNoteId" />
    <collection property="productNameList" ofType="java.lang.String" column="product_name" />
  </resultMap>

  <resultMap id="extDeliverySendBeanMap" type="com.ecommerce.logistics.dao.dto.delivery.DeliverySendBean"
             extends="BaseResultMap">
    <result column="product_id" jdbcType="VARCHAR" property="productId" />
    <result column="note" jdbcType="VARCHAR" property="note" />
  </resultMap>

  <select id="selectProductNameByNoteId" resultMap="deliveryNoteProductMap">
    select
    info.note as product_name,
    map.delivery_note_id
    from lgs_delivery_send_map map, lgs_product_info info
    where map.del_flg = 0 and info.del_flg = 0 and map.product_info_id = info.product_info_id
    <if test="deliveryNoteIdList != null">
      and map.delivery_note_id in
      <foreach collection="deliveryNoteIdList" index="din" item="dii" open="(" separator="," close=")">
        #{dii}
      </foreach>
    </if>
    <if test="productNameLike != null and productNameLike != ''">
      and info.note like concat('%', #{productNameLike}, '%')
    </if>
    order by map.delivery_note_id asc, map.send_order asc
  </select>

  <select id="selectSendBeanByDeliveryNote" resultMap="extDeliverySendBeanMap">
    select
    m.*,
    i.product_id,
    i.note
    from lgs_delivery_send_map m, lgs_product_info i
    where m.del_flg = 0 and i.del_flg = 0 and m.product_info_id = i.product_info_id
    and m.delivery_note_id = #{deliveryNoteId}
  </select>

  <select id="selectByDeliveryNoteIdAndSubStatusList" resultMap="extDeliverySendBeanMap">
    select
    m.*,
    i.product_id,
    i.note
    from lgs_delivery_send_map m, lgs_product_info i
    where m.del_flg = 0 and i.del_flg = 0 and m.product_info_id = i.product_info_id
    and m.delivery_note_id = #{deliveryNoteId}
    and m.status in
    <foreach collection="subStatusList" open="(" separator="," close=")" index="ssi" item="ssit">
      #{ssit}
    </foreach>
    order by m.send_order asc
  </select>

</mapper>