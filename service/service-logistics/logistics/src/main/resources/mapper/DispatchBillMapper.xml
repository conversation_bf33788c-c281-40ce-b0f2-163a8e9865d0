<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.DispatchBillMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.DispatchBill">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="dispatch_bill_id" jdbcType="VARCHAR" property="dispatchBillId" />
    <result column="dispatch_bill_num" jdbcType="VARCHAR" property="dispatchBillNum" />
    <result column="picking_bill_id" jdbcType="VARCHAR" property="pickingBillId" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="endReason" jdbcType="VARCHAR" property="end_reason" />
    <result column="carrier_id" jdbcType="VARCHAR" property="carrierId" />
    <result column="carrier_name" jdbcType="VARCHAR" property="carrierName" />
    <result column="carrier_type" jdbcType="VARCHAR" property="carrierType" />
    <result column="income_carriage" jdbcType="DECIMAL" property="incomeCarriage" />
    <result column="picking_time" jdbcType="TIMESTAMP" property="pickingTime" />
    <result column="expend_carriage" jdbcType="DECIMAL" property="expendCarriage" />
    <result column="sync_flag" jdbcType="VARCHAR" property="syncFlag" />
    <result column="source_type" jdbcType="VARCHAR" property="sourceType" />
    <result column="source_user_id" jdbcType="VARCHAR" property="sourceUserId" />
    <result column="carrier_evaluate_flag" jdbcType="TINYINT" property="carrierEvaluateFlag" />
    <result column="platform_evaluate_flag" jdbcType="TINYINT" property="platformEvaluateFlag" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="version" jdbcType="BIGINT" property="version" />
  </resultMap>
  <sql id="allCloumn">
  	dispatch_bill_id,picking_bill_id,status,carrier_id,del_flg,create_user,create_time,update_time,version
  </sql>

  <select id="selectDispatchBillList" parameterType="com.ecommerce.logistics.api.dto.dispatchbill.DispatchBillListQueryDTO"
          resultType="com.ecommerce.logistics.dao.dto.dispatchbill.DispatchBillDO">
    SELECT
      p.warehouse_id as warehouseId,
      p.picking_bill_num as pickingBillNum,
      d.dispatch_bill_id as dispatchBillId,
      d.dispatch_bill_num as dispatchBillNum,
      q.quantity as productQuantity,
      q.delivery_quantity as deliveryQuantity,
      q.complete_quantity as completeQuantity,
      ltc.category_name as transportCategory,
      p.province as receiveProvince,
      p.city as receiveCity,
      p.district as receiveDistrict,
      p.street as receiveStreet,
      p.address as receiveAddress,
      p.delivery_time as deliveryTime,
      p.delivery_time_range as deliveryTimeRange,
      p.receiver as receiver,
      p.receiver_phone as receiverPhone,
      p.buyer_name as buyerName,
      p.seller_name as sellerName,
      d.carrier_id as carrierId,
      d.carrier_name as carrierName,
      d.status as dispatchStatus,
      d.create_time as createTime,
      d.carrier_evaluate_flag as carrierEvaluateFlag,
      d.platform_evaluate_flag as platformEvaluateFlag,
      i.special_flag as specialFlag,
      i.unit as productUnit,
      i.note as productDesc
    FROM
    	lgs_picking_bill p
    	<choose>
    		<when test="joinStatus == 3">
    			JOIN
    				lgs_dispatch_bill d on d.picking_bill_id = p.picking_bill_id
    		</when>
    		<otherwise>
    			RIGHT JOIN
    				lgs_dispatch_bill d on d.picking_bill_id = p.picking_bill_id
    		</otherwise>
    	</choose>
    LEFT JOIN
    	lgs_product_quantity_map q on q.map_id = d.dispatch_bill_id
    LEFT JOIN
    	lgs_product_info i on i.product_info_id = q.product_info_id
    left join
      lgs_transport_category ltc on i.transport_category_id = ltc.transport_category_id
    <if test="regionCodeList != null and regionCodeList.size() > 0">
    INNER JOIN
        ba_region_all as auth on p.district_code = auth.region_son_code
    </if>
    <where>
      <if test="regionCodeList != null and regionCodeList.size() > 0">
        AND auth.region_code IN
        <foreach close=")" collection="regionCodeList" index="index" item="item" open="(" separator=",">
          #{item}
        </foreach>
      </if>
      <if test="pickingBillNum != null and pickingBillNum != ''">
        AND p.picking_bill_num = #{pickingBillNum}
      </if>
      <if test="provinceCode != null and provinceCode != ''">
        AND p.province_code = #{provinceCode}
      </if>
      <if test="cityCode != null and cityCode != ''">
        AND p.city_code = #{cityCode}
      </if>
      <if test="districtCode != null and districtCode != ''">
        AND p.district_code = #{districtCode}
      </if>
      <if test="status != null and status != '' ">
      	AND d.status = #{status}
      </if>
      <if test="carrierId != null and carrierId!='' ">
      	AND d.carrier_id = #{carrierId}
      </if>
      <if test="dispatchBillNum != null and dispatchBillNum != '' ">
      	AND d.dispatch_bill_num = #{dispatchBillNum}
      </if>
      <if test="carrierName != null and carrierName!= '' ">
      	AND d.carrier_name LIKE '%${carrierName}%'
      </if>
      <if test="beginDeliveryTime != null and beginDeliveryTime != ''">
        <![CDATA[AND p.delivery_time >= #{beginDeliveryTime}]]>
      </if>
      <if test="endDeliveryTime != null and endDeliveryTime != ''">
        <![CDATA[AND p.delivery_time <= #{endDeliveryTime}]]>
      </if>
       <if test="warehouseId != null and warehouseId != '' ">
        AND p.warehouse_id = #{warehouseId}
      </if>
    </where>
    ORDER BY
    d.create_time DESC, d.dispatch_bill_id
  </select>
  
  <select id="selectDispatchBillForDownload" parameterType="com.ecommerce.logistics.api.dto.dispatchbill.DispatchBillListQueryDTO"
          resultType="com.ecommerce.logistics.dao.dto.dispatchbill.ExportDispatchBillDO">
    select
      dispatch.dispatch_bill_num as dispatchBillNum,
      dispatch.status as disPatchStatus,
      picking.picking_bill_num as pickingBillNum,
      picking.buyer_name as buyerName,
      picking.seller_name as sellerName,
      dispatch.carrier_name as carrierName,
      dispatch.source_type as sourceType,
      category.category_name as transportCategoryName,
      info.note as productName,
      map.quantity as quantity,
      map.delivery_quantity as deliveryQuantity,
      map.complete_quantity as sendQuantity,
      picking.warehouse_id as warehouseId,
      picking.receiver as receiver,
      picking.receiver_phone as receiverPhone,
      concat(picking.province, picking.city, picking.district, picking.street, picking.address) as receiveAddress,
      dispatch.create_time as createTime,
      dispatch.update_time as updateTime,
      picking.picking_time as pickingTime,
      picking.delivery_time as deliveryTime,
      picking.delivery_time_range as deliveryTimeRange,
      picking.delivery_sheet_num as deliverySheetNum,
      info.product_id as orderItemId
    from lgs_dispatch_bill as dispatch
    left join lgs_picking_bill as picking on dispatch.picking_bill_id = picking.picking_bill_id
    left join lgs_product_quantity_map as map on map.map_id = dispatch.dispatch_bill_id
    left join lgs_product_info as info on map.product_info_id = info.product_info_id
    left join lgs_transport_category as category on info.transport_category_id = category.transport_category_id
    <if test="regionCodeList != null and regionCodeList.size() > 0">
      INNER JOIN ba_region_all as auth on picking.district_code = auth.region_son_code
    </if>
    where dispatch.del_flg = 0
    <if test="regionCodeList != null and regionCodeList.size() > 0">
      AND auth.region_code IN
      <foreach close=")" collection="regionCodeList" index="index" item="item" open="(" separator=",">
        #{item}
      </foreach>
    </if>
    <if test="pickingBillNum != null and pickingBillNum != ''">
      AND picking.picking_bill_num = #{pickingBillNum}
    </if>
    <if test="provinceCode != null and provinceCode != ''">
      AND picking.province_code = #{provinceCode}
    </if>
    <if test="cityCode != null and cityCode != ''">
      AND picking.city_code = #{cityCode}
    </if>
    <if test="districtCode != null and districtCode != ''">
      AND picking.district_code = #{districtCode}
    </if>
    <if test="status != null and status != '' ">
      AND dispatch.status = #{status}
    </if>
    <if test="carrierId != null and carrierId!='' ">
      AND dispatch.carrier_id = #{carrierId}
    </if>
    <if test="dispatchBillNum != null and dispatchBillNum != '' ">
      AND dispatch.dispatch_bill_num = #{dispatchBillNum}
    </if>
    <if test="carrierName != null and carrierName!= '' ">
      AND dispatch.carrier_name LIKE '%${carrierName}%'
    </if>
    <if test="beginDeliveryTime != null and beginDeliveryTime != ''">
      <![CDATA[AND picking.delivery_time >= #{beginDeliveryTime}]]>
    </if>
    <if test="endDeliveryTime != null and endDeliveryTime != ''">
      <![CDATA[AND picking.delivery_time <= #{endDeliveryTime}]]>
    </if>
    <if test="warehouseId != null and warehouseId != '' ">
      AND picking.warehouse_id = #{warehouseId}
    </if>
    ORDER BY dispatch.create_time DESC, dispatch.dispatch_bill_id
  </select>

  <select id="selectDispatchBillDetail" parameterType="java.lang.String"
          resultType="com.ecommerce.logistics.dao.dto.dispatchbill.DispatchBillDO">
  	SELECT
  	 d.dispatch_bill_id as dispatchBillId,
  	 d.status as dispatchStatus,
  	 d.carrier_name as carrierName,
  	 d.dispatch_bill_num as dispatchBillNum,
  	 d.picking_time as pickingTime,
  	 d.create_time as createTime,
  	 q.quantity as productQuantity,
  	 q.delivery_quantity as deliveryQuantity,
  	 q.complete_quantity as completeQuantity,
  	 p.picking_bill_id as pickingBillId,
  	 p.picking_bill_num as pickingBillNum,
  	 p.warehouse_id as warehouseId,
  	 p.buyer_name as buyerName,
  	 p.seller_name as sellerName,
  	 p.receiver as receiver,
  	 p.receiver_phone as receiverPhone,
  	 p.province as receiveProvince,
     p.city as receiveCity,
     p.district as receiveDistrict,
     p.street as receiveStreet,
     p.address as receiveAddress,
     p.delivery_time as deliveryTime,
     p.delivery_time_range as deliveryTimeRange,
     i.transport_category_id as transportCategoryId,
     i.note as productDesc,
     i.product_img as productImg,
     i.special_flag as specialFlag,
     i.unit as productUnit
  	FROM
  		lgs_picking_bill p 
  	LEFT JOIN
  		lgs_dispatch_bill d on d.picking_bill_id = p.picking_bill_id
  	LEFT JOIN
  		lgs_product_quantity_map q on q.map_id = d.dispatch_bill_id
  	LEFT JOIN
  		lgs_product_info i on i.product_info_id = q.product_info_id
  	WHERE 
  		d.dispatch_bill_id = #{dispatchBillId}
  </select>
  
  <select id="selectProductInfoIdByDispatchBillId" parameterType="java.lang.String" resultType="java.lang.String">
  	SELECT
  		q.product_info_id
  	FROM
  		lgs_dispatch_bill d
  	LEFT JOIN
  		lgs_product_quantity_map q on q.map_id = d.dispatch_bill_id
  
  </select>
  
  <select id="selectDispatchBillPartInfo" parameterType="java.lang.String" resultType="com.ecommerce.logistics.dao.dto.dispatchbill.DispatchBillDO">
  	SELECT
  		d.picking_bill_id as pickingBillId,
  		q.quantity as productQuantity
  	FROM
  		lgs_dispatch_bill d 
  	LEFT JOIN
  		lgs_product_quantity_map q on q.map_id = d.dispatch_bill_id
  	WHERE
  		d.dispatch_bill_id = #{dispatchBillId}
  </select>
  
  <update id="updateDelFlgByKey" parameterType="java.util.Map">
  	UPDATE
  		lgs_dispatch_bill
  	SET
  		del_flg = 1,
  		update_user = #{operatorId},
  		update_time = CURRENT_TIMESTAMP()
  	WHERE
  		dispatch_bill_id = #{dispatchBillId}
  </update>

  <select id="selectPickingBillAssigns" parameterType="java.lang.String" resultType="com.ecommerce.logistics.api.dto.pickingbill.PickingBillAssignDispatchDTO">
    SELECT 
    	d.dispatch_bill_id as dispatchBillId,
    	d.dispatch_bill_num as dispatchBillNum,
    	d.carrier_id as carrierId,
    	d.carrier_name as carrierName,
    	d.carrier_type as carrierType,
    	d.status as status,
    	q.quantity as deliveryQuantity,
    	i.unit as productUnit
    FROM
    	lgs_dispatch_bill d
    LEFT JOIN
    	lgs_product_quantity_map q on q.map_id = d.dispatch_bill_id
    LEFT JOIN
        lgs_product_info as i on i.product_info_id=q.product_info_id
    WHERE
    	d.del_flg = 0 AND d.picking_bill_id = #{pickingBillId}
  </select>
  
  <select id="selectDispatchBillByPickingBillIds" resultType="com.ecommerce.logistics.api.dto.dispatchbill.DispatchBillListDTO">
    SELECT
      d.picking_bill_id as pickingBillId,
      d.dispatch_bill_id as dispatchBillId,
      q.quantity as productQuantity,
      d.status as dispatchStatus,
      i.product_id as productId
    FROM
      lgs_dispatch_bill d
    LEFT JOIN
      lgs_product_quantity_map q on q.map_id = d.dispatch_bill_id
    LEFT JOIN
      lgs_product_info as i on i.product_info_id=q.product_info_id
    WHERE
      d.del_flg = 0 AND d.picking_bill_id in
      <foreach close=")" collection="list" index="index" item="pickingBillId" open="(" separator=",">
        #{pickingBillId}
      </foreach>
  </select>
  
  <select id="selectAssignCapacityByCarrierId" resultType="com.ecommerce.logistics.dao.dto.dispatchbill.CapacityDO">
  	SELECT
      d.carrier_id as carrierId,
      SUM(q.quantity - q.complete_quantity) as capacity
  	FROM
      lgs_dispatch_bill d
  	LEFT JOIN
      lgs_product_quantity_map q on q.map_id = d.dispatch_bill_id
    LEFT JOIN
      lgs_product_info as p on q.product_info_id=p.product_info_id
    WHERE
      d.del_flg = 0 AND p.transport_category_id = #{capacityQueryDTO.transportCategoryId}
      AND d.carrier_id IN
      <foreach close=")" collection="capacityQueryDTO.carrierIdList" index="index" item="carrierId" open="(" separator=",">
        #{carrierId}
      </foreach>
      AND d.status in
      <foreach close=")" collection="statusList" index="index" item="status" open="(" separator=",">
        #{status}
      </foreach>
    GROUP BY carrier_id
  </select>
  
  <select id="selectPartInfoById" parameterType="java.lang.String" resultType="com.ecommerce.logistics.dao.dto.dispatchbill.DispatchBillDO">
  	SELECT
  		p.picking_bill_id as pickingBillId,
  		p.picking_bill_num as pickingBillNum,
  		p.delivery_time as deliveryTime,
    	p.delivery_time_range as deliveryTimeRange,
    	d.dispatch_bill_num as dispatchBillNum,
    	d.carrier_id as carrierId,
    	d.carrier_name as carrierName,
    	q.quantity as productQuantity,
    	q.delivery_quantity as deliveryQuantity,
    	q.product_info_id as productInfoId,
    	i.transport_category_id as transportCategoryId
  	FROM
  		lgs_picking_bill p
  	RIGHT JOIN
  		lgs_dispatch_bill d ON d.picking_bill_id = p.picking_bill_id
  	LEFT JOIN
  		lgs_product_quantity_map q on q.map_id = d.dispatch_bill_id
    LEFT JOIN
        lgs_product_info as i on q.product_info_id=i.product_info_id
  	WHERE
  		d.dispatch_bill_id = #{dispatchBillId}
  </select>

  <update id="closeExpireDispatchBillList">
    update lgs_dispatch_bill
    set status = '030030600',
    end_reason = '030360200',
    update_time = current_timestamp
    where dispatch_bill_id in
    <foreach collection="dispatchBillIdList" item="dit" index="dii" open="(" separator="," close=")">
      #{dit}
    </foreach>
    and del_flg = 0
  </update>

  <select id="selectByDeliverySheetNumAndStatus" resultMap="BaseResultMap">
    select dis.*
    from lgs_dispatch_bill as dis
    left join lgs_picking_bill as pick on pick.picking_bill_id = dis.picking_bill_id
    where pick.delivery_sheet_num in
    <foreach collection="deliverySheetNumList" open="(" separator="," close=")" item="dit" index="din">
      #{dit}
    </foreach>
    and dis.status in
    <foreach collection="statusList" open="(" separator="," close=")" item="sit" index="sin">
      #{sit}
    </foreach>
    and dis.del_flg = 0 and pick.del_flg = 0
  </select>
  

</mapper>