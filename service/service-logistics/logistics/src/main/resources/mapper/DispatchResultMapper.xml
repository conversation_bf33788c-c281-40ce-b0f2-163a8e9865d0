<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.DispatchResultMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.DispatchResult">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="dispatch_result_id" jdbcType="VARCHAR" property="dispatchResultId" />
    <result column="batch_id" jdbcType="VARCHAR" property="batchId" />
    <result column="delivery_bill_id" jdbcType="VARCHAR" property="deliveryBillId" />
    <result column="delivery_bill_num" jdbcType="VARCHAR" property="deliveryBillNum" />
    <result column="vehicle_id" jdbcType="VARCHAR" property="vehicleId" />
    <result column="vehicle_num" jdbcType="VARCHAR" property="vehicleNum" />
    <result column="driver_id" jdbcType="VARCHAR" property="driverId" />
    <result column="driver_name" jdbcType="VARCHAR" property="driverName" />
    <result column="driver_phone" jdbcType="VARCHAR" property="driverPhone" />
    <result column="delivery_time" jdbcType="TIMESTAMP" property="deliveryTime" />
    <result column="delivery_time_start" jdbcType="TIMESTAMP" property="deliveryTimeStart" />
    <result column="delivery_time_end" jdbcType="TIMESTAMP" property="deliveryTimeEnd" />
    <result column="km_distance" jdbcType="DECIMAL" property="kmDistance" />
    <result column="max_load_capacity" jdbcType="DECIMAL" property="maxLoadCapacity" />
    <result column="quantity" jdbcType="DECIMAL" property="quantity" />
    <result column="goods_id" jdbcType="VARCHAR" property="goodsId" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="vehicle_type_id" jdbcType="VARCHAR" property="vehicleTypeId" />
    <result column="vehicle_type_name" jdbcType="VARCHAR" property="vehicleTypeName" />
    <result column="warehouse_id" jdbcType="VARCHAR" property="warehouseId" />
    <result column="warehouse_name" jdbcType="VARCHAR" property="warehouseName" />
    <result column="warehouse_address" jdbcType="VARCHAR" property="warehouseAddress" />
    <result column="receiver_address_id" jdbcType="VARCHAR" property="receiverAddressId" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="estimate_km" jdbcType="DECIMAL" property="estimateKm" />
    <result column="estimate_duration" jdbcType="DECIMAL" property="estimateDuration" />
    <result column="no_load_km" jdbcType="DECIMAL" property="noLoadKm" />
    <result column="loading_time" jdbcType="DECIMAL" property="loadingTime" />
    <result column="unloading_time" jdbcType="DECIMAL" property="unloadingTime" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
  </resultMap>

  <select id="warehouseOptionsByBatchId" resultType="com.ecommerce.logistics.api.dto.pickingbill.OptionDTO">
    select distinct warehouse_id as value,
                    warehouse_name as label
     from lgs_dispatch_result where batch_id = #{batchId}
  </select>

  <select id="receiverAddressOptionsByBatchId" resultType="com.ecommerce.logistics.api.dto.pickingbill.OptionDTO">
    select distinct receiver_address_id as value,
                    address as label
    from lgs_dispatch_result where batch_id = #{batchId}
  </select>

  <update id="updateStatus">
    update lgs_dispatch_result
    set status = #{newStatus},
        update_time = current_timestamp,
        update_user = #{operateUserId}
    where dispatch_result_id in
    <foreach collection="dispatchResultIdList" item="dit" index="din" open="(" separator="," close=")">
      #{dit}
    </foreach>
    and status = #{oldStatus}
  </update>

  <update id="updateDriverInfo" parameterType="com.ecommerce.logistics.api.dto.auto.VehicleAutoAssignDTO">
    update lgs_dispatch_result
    set driver_id = #{driverId},
        driver_phone = #{driverPhone},
        driver_name = #{driverName}
    where dispatch_result_id = #{dispatchResultId}
  </update>

  <select id="queryAutoWaitAssignList" parameterType="com.ecommerce.logistics.api.dto.auto.VehicleAutoAssignQueryDTO"
          resultType="com.ecommerce.logistics.api.dto.auto.VehicleAutoAssignDTO">
    select batch_id as batchId,
           dispatch_result_id as dispatchResultId,
           delivery_bill_id as deliveryBillId,
           delivery_bill_num as deliveryBillNum,
           vehicle_id as vehicleId,
           vehicle_num as vehicleNum,
           driver_id as driverId,
           driver_name as driverName,
           driver_phone as driverPhone,
           delivery_time as deliveryTime,
           delivery_time_start as deliveryTimeStart,
           delivery_time_end as deliveryTimeEnd,
           km_distance as kmDistance,
           max_load_capacity as maxLoadCapacity,
           quantity as quantity,
           goods_id as goodsId,
           goods_name as goodsName,
           unit as unit,
           vehicle_type_id as vehicleTypeId,
           vehicle_type_name as vehicleTypeName,
           warehouse_id as warehouseId,
           warehouse_name as warehouseName,
           warehouse_address as warehouseAddress,
           receiver_address_id as receiverAddressId,
           address as address,
           estimate_km as estimateKm,
           estimate_duration as estimateDuration,
           no_load_km as noLoadKm,
           loading_time as loadingTime,
           unloading_time as unloadingTime,
           status as status
    from lgs_dispatch_result
    where batch_id = #{batchId}
    <if test="status != null and status != ''">
      and status = #{status}
    </if>
    <if test="warehouseId != null and warehouseId != ''">
      and warehouse_id = #{warehouseId}
    </if>
    <if test="receiverAddressId != null and receiverAddressId != ''">
      and receiver_address_id = #{receiverAddressId}
    </if>
    <if test="deliveryTimeStart != null and deliveryTimeStart != ''">
      and delivery_time &gt;= #{deliveryTimeStart}
    </if>
    <if test="deliveryTimeEnd != null and deliveryTimeEnd != ''">
      and delivery_time &lt;= #{deliveryTimeEnd}
    </if>
    order by create_time
  </select>

</mapper>