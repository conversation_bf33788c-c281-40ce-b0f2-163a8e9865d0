<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.DriverContractInfoMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.DriverContractInfo">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="driver_contract_id" jdbcType="VARCHAR" property="driverContractId" />
    <result column="driver_contract_num" jdbcType="VARCHAR" property="driverContractNum" />
    <result column="waybill_id" jdbcType="VARCHAR" property="waybillId" />
    <result column="waybill_num" jdbcType="VARCHAR" property="waybillNum" />
    <result column="goods_id" jdbcType="VARCHAR" property="goodsId" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="driver_id" jdbcType="VARCHAR" property="driverId" />
    <result column="driver_name" jdbcType="VARCHAR" property="driverName" />
    <result column="vehicle_number" jdbcType="VARCHAR" property="vehicleNumber" />
    <result column="warehouse_name" jdbcType="VARCHAR" property="warehouseName" />
    <result column="receive_address" jdbcType="VARCHAR" property="receiveAddress" />
    <result column="sign_date" jdbcType="DATE" property="signDate" />
    <result column="start_date" jdbcType="DATE" property="startDate" />
    <result column="end_date" jdbcType="DATE" property="endDate" />
    <result column="freight" jdbcType="DECIMAL" property="freight" />
    <result column="account_statement_status" jdbcType="TINYINT" property="accountStatementStatus" />
    <result column="pay_status" jdbcType="TINYINT" property="payStatus" />
    <result column="reporting_status" jdbcType="TINYINT" property="reportingStatus" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <select id="queryDriverContractList" parameterType="com.ecommerce.logistics.api.dto.contract.DriverContractListQueryDTO"
          resultType="com.ecommerce.logistics.api.dto.contract.DriverContractListDTO">
      SELECT
          t1.driver_contract_id AS driverContractId,
          t1.driver_contract_num AS driverContractNum,
          t1.waybill_num AS waybillNum,
          t1.goods_name AS goodsName,
          t1.driver_name AS driverName,
          t1.vehicle_number AS vehicleNumber,
          t1.warehouse_name AS warehouseName,
          t1.receive_address AS receiveAddress,
          t1.sign_date AS signDate,
          t1.start_date AS startDate,
          t1.end_date AS endDate,
          t1.freight AS freight,
          t1.account_statement_status AS accountStatementStatus,
          t1.pay_status AS payStatus,
          t1.reporting_status AS reportingStatus,
          t2.bid AS bid,
          t2.file_url AS fileUrl
      FROM
        lgs_driver_contract_info t1
      LEFT JOIN lgs_attachment t2 ON t1.driver_contract_id = t2.entry_id AND t2.del_flg = 0
      WHERE
        t1.del_flg = 0
      <if test="driverContractNum != null and driverContractNum !=''">
        AND t1.driver_contract_num = #{driverContractNum}
      </if>
      <if test="vehicleNumber != null and vehicleNumber !=''">
        AND t1.vehicle_number = like concat('%', #{vehicleNumber}, '%')
      </if>
      <if test="driverName != null and driverName !=''">
        AND t1.driver_name = #{driverName}
      </if>
      <if test="waybillNum != null and waybillNum !=''">
        AND t1.waybill_num = #{waybillNum}
      </if>
      <if test="goodsName != null and goodsName !=''">
        AND t1.goods_name = #{goodsName}
      </if>
      <if test="signDate != null and signDate !=''">
        AND t1.sign_date = #{signDate}
      </if>
      <if test="accountStatementStatus != null">
        AND t1.account_statement_status = #{accountStatementStatus}
      </if>
      <if test="payStatus != null">
        AND t1.pay_status = #{payStatus}
      </if>
      <if test="reportingStatus != null">
          AND t1.reporting_status = #{reportingStatus}
      </if>
      ORDER BY t1.create_time DESC
  </select>


</mapper>