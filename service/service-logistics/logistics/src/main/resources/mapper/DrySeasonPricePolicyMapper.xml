<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.DrySeasonPricePolicyMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.DrySeasonPricePolicy">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="policy_id" jdbcType="VARCHAR" property="policyId" />
    <result column="policy_type" jdbcType="VARCHAR" property="policyType" />
    <result column="policy_code" jdbcType="VARCHAR" property="policyCode" />
    <result column="policy_name" jdbcType="VARCHAR" property="policyName" />
    <result column="belonger_id" jdbcType="VARCHAR" property="belongerId" />
    <result column="support_shipping_type" jdbcType="VARCHAR" property="supportShippingType" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <!-- List<DrySeasonPolicyListDTO> pageInfoPolicy(DrySeasonPolicyQueryDTO dto); -->
  <select id="pageInfoPolicy" resultType="com.ecommerce.logistics.api.dto.dry.season.policy.DrySeasonPolicyListDTO"
          parameterType="java.lang.String">
    select
    a.policy_id as policyId,
    a.policy_name as policyName,
    a.support_shipping_type as supportShippingType,
    a.create_time as createTime,
    b.shipping_route_no as shippingRouteNo,
    e.picking_wharf_name pickingWharfName,
    e.unloading_wharf_name unloadingWharfName,
    c.start_time as startTime,
    c.end_time as endTime,
    d.selected_column as selectedColumn
    from
    ( select * from lgs_dry_season_price_policy where policy_id in ( ${policyIds} ) ) as a
    left join lgs_dry_season_price_policy_shipping_route_relation as b on a.policy_id = b.policy_id and b.del_flg=0
    left join lgs_dry_season_price_policy_time as c on a.policy_id = c.policy_id and c.del_flg=0
    left join ( select distinct policy_id,selected_column from lgs_dry_season_price_policy_water_level where policy_id in ( ${policyIds} ) and del_flg=0 ) as d on a.policy_id = d.policy_id
    left join lgs_shipping_route_info e on b.shipping_route_id = e.shipping_route_id and e.del_flg = 0
    order by a.create_time desc
  </select>

  <select id="pageInfoPolicy_COUNT" resultType="java.lang.Long" parameterType="java.lang.String"
  >
    select count(1) from
    ( select * from lgs_dry_season_price_policy where policy_id in ( ${policyIds} ) ) as a
    left join lgs_dry_season_price_policy_shipping_route_relation as b on a.policy_id = b.policy_id and b.del_flg=0
    left join lgs_dry_season_price_policy_time as c on a.policy_id = c.policy_id and c.del_flg=0
    left join ( select distinct policy_id,selected_column from lgs_dry_season_price_policy_water_level where policy_id in ( ${policyIds} ) and del_flg=0 ) as d
    on a.policy_id = d.policy_id
  </select>

  <select id="findPolicyId" resultType="java.lang.String"
          parameterType="com.ecommerce.logistics.api.dto.dry.season.policy.DrySeasonPolicyQueryDTO">
    select distinct policy_id from lgs_dry_season_price_policy as a
    where a.del_flg=0
    <if test="memberId != null and memberId != ''">
      AND belonger_id=#{memberId}
    </if>
    <if test="policyName != null and policyName != ''">
      AND policy_name LIKE CONCAT('%', #{policyName}, '%')
    </if>
    <if test="supportShippingType != null and supportShippingType != ''">
      AND ( support_shipping_type LIKE CONCAT('%', #{supportShippingType}, '%') or support_shipping_type LIKE CONCAT('%all%') )
    </if>
    <if test="selectedColumn != null and selectedColumn != ''">
      and policy_id in ( select distinct policy_id from lgs_dry_season_price_policy_water_level as b where
      b.policy_id is not null
      and b.del_flg=0
      and b.selected_column=#{selectedColumn})
    </if>
  </select>
</mapper>
