<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.DrySeasonPricePolicyShippingRouteRelationMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.DrySeasonPricePolicyShippingRouteRelation">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="relation_id" jdbcType="VARCHAR" property="relationId" />
    <result column="shipping_route_id" jdbcType="VARCHAR" property="shippingRouteId" />
    <result column="shipping_route_no" jdbcType="VARCHAR" property="shippingRouteNo" />
    <result column="policy_id" jdbcType="VARCHAR" property="policyId" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>
