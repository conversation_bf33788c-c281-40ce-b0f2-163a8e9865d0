<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.DrySeasonPricePolicyWaterLevelItemMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.DrySeasonPricePolicyWaterLevelItem">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="item_id" jdbcType="VARCHAR" property="itemId" />
    <result column="water_level_id" jdbcType="VARCHAR" property="waterLevelId" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="val" jdbcType="DECIMAL" property="val" />
    <result column="weight" jdbcType="INTEGER" property="weight" />
  </resultMap>
</mapper>