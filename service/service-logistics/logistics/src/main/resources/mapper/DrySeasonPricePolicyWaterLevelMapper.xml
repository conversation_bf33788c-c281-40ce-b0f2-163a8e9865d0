<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.DrySeasonPricePolicyWaterLevelMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.DrySeasonPricePolicyWaterLevel">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="water_level_id" jdbcType="VARCHAR" property="waterLevelId" />
    <result column="belonger_id" jdbcType="VARCHAR" property="belongerId" />
    <result column="templete_flg" jdbcType="BIT" property="templeteFlg" />
    <result column="policy_id" jdbcType="VARCHAR" property="policyId" />
    <result column="min_tonnage" jdbcType="DECIMAL" property="minTonnage" />
    <result column="max_tonnage" jdbcType="DECIMAL" property="maxTonnage" />
    <result column="selected_column" jdbcType="VARCHAR" property="selectedColumn" />
    <result column="floating_ratio15" jdbcType="DECIMAL" property="floatingRatio15" />
    <result column="floating_ratio16" jdbcType="DECIMAL" property="floatingRatio16" />
    <result column="floating_ratio17" jdbcType="DECIMAL" property="floatingRatio17" />
    <result column="floating_ratio18" jdbcType="DECIMAL" property="floatingRatio18" />
    <result column="floating_ratio19" jdbcType="DECIMAL" property="floatingRatio19" />
    <result column="floating_ratio20" jdbcType="DECIMAL" property="floatingRatio20" />
    <result column="floating_ratio21" jdbcType="DECIMAL" property="floatingRatio21" />
    <result column="floating_ratio22" jdbcType="DECIMAL" property="floatingRatio22" />
    <result column="floating_ratio23" jdbcType="DECIMAL" property="floatingRatio23" />
    <result column="floating_ratio24" jdbcType="DECIMAL" property="floatingRatio24" />
    <result column="floating_ratio25" jdbcType="DECIMAL" property="floatingRatio25" />
    <result column="floating_ratio26" jdbcType="DECIMAL" property="floatingRatio26" />
    <result column="floating_ratio27" jdbcType="DECIMAL" property="floatingRatio27" />
    <result column="floating_ratio28" jdbcType="DECIMAL" property="floatingRatio28" />
    <result column="floating_ratio29" jdbcType="DECIMAL" property="floatingRatio29" />
    <result column="floating_ratio30" jdbcType="DECIMAL" property="floatingRatio30" />
    <result column="floating_ratio31" jdbcType="DECIMAL" property="floatingRatio31" />
    <result column="floating_ratio32" jdbcType="DECIMAL" property="floatingRatio32" />
    <result column="floating_ratio33" jdbcType="DECIMAL" property="floatingRatio33" />
    <result column="floating_ratio34" jdbcType="DECIMAL" property="floatingRatio34" />
    <result column="floating_ratio35" jdbcType="DECIMAL" property="floatingRatio35" />
    <result column="floating_ratio36" jdbcType="DECIMAL" property="floatingRatio36" />
    <result column="floating_ratio37" jdbcType="DECIMAL" property="floatingRatio37" />
    <result column="floating_ratio38" jdbcType="DECIMAL" property="floatingRatio38" />
    <result column="floating_ratio39" jdbcType="DECIMAL" property="floatingRatio39" />
    <result column="floating_ratio40" jdbcType="DECIMAL" property="floatingRatio40" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>
