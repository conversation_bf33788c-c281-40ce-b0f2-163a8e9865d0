<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.ErpShippingInfoMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.ErpShippingInfo">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="member_id" jdbcType="VARCHAR" property="memberId" />
    <result column="erp_carrier_no" jdbcType="VARCHAR" property="erpCarrierNo" />
    <result column="erp_ship_name" jdbcType="VARCHAR" property="erpShipName" />
    <result column="erp_ship_code" jdbcType="VARCHAR" property="erpShipCode" />
    <result column="extend1" jdbcType="VARCHAR" property="extend1" />
    <result column="extend2" jdbcType="VARCHAR" property="extend2" />
    <result column="extend3" jdbcType="VARCHAR" property="extend3" />
    <result column="extend4" jdbcType="VARCHAR" property="extend4" />
    <result column="extend5" jdbcType="VARCHAR" property="extend5" />
    <result column="del_flg" jdbcType="INTEGER" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <select id="findErpShippingByErpCarrierNo" resultType="com.ecommerce.logistics.api.dto.shipping.erp.ErpShippingInfoDTO">
    SELECT
      erp_carrier_no AS erpCarrierNo,
      erp_ship_name AS erpShipName,
      erp_ship_code AS erpShipCode
    FROM lgs_erp_shipping_info
    WHERE
        del_flg = 0
    <if test="erpCarrierNo != null and erpCarrierNo != ''">
      AND erp_carrier_no = #{erpCarrierNo}
    </if>
    <if test="manufacturerMemberId != null and manufacturerMemberId != ''">
      AND member_id = #{manufacturerMemberId}
    </if>
  </select>


  <select id="findErpInfo" resultType="com.ecommerce.logistics.dao.vo.ErpShippingInfo">
    SELECT
      id AS id,
      member_id AS memberId,
      erp_carrier_no AS erpCarrierNo,
      erp_ship_name AS erpShipName,
      erp_ship_code AS erpShipCode
    FROM lgs_erp_shipping_info
    WHERE
        member_id = #{memberId}
    AND erp_ship_name = #{erpShipName}
    AND erp_ship_code = #{erpShipCode}
    AND del_flg = 0
    limit 1
  </select>

</mapper>