<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.ErpShippingMapMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.ErpShippingMap">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="ec_member_id" jdbcType="VARCHAR" property="ecMemberId" />
    <result column="ec_member_name" jdbcType="VARCHAR" property="ecMemberName" />
    <result column="ec_carrier_id" jdbcType="VARCHAR" property="ecCarrierId" />
    <result column="ec_shipping_id" jdbcType="VARCHAR" property="ecShippingId" />
    <result column="ec_shipping_name" jdbcType="VARCHAR" property="ecShippingName" />
    <result column="erp_shipping_code" jdbcType="VARCHAR" property="erpShippingCode" />
    <result column="erp_carrier_no" jdbcType="VARCHAR" property="erpCarrierNo" />
    <result column="erp_carrier_name" jdbcType="VARCHAR" property="erpCarrierName" />
    <result column="erp_shipping_name" jdbcType="VARCHAR" property="erpShippingName" />
    <result column="del_flg" jdbcType="INTEGER" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <!--新增或修改，通过唯一键判断-->
  <insert id="addOrUpdateErpShipping" parameterType="com.ecommerce.logistics.dao.vo.ErpShippingMap">
    INSERT INTO lgs_erp_shipping_map(id, ec_member_id,ec_member_name, ec_carrier_id, ec_shipping_id,
        ec_shipping_name, erp_carrier_no, erp_carrier_name, erp_shipping_code, erp_shipping_name, del_flg, create_user, create_time)
    VALUES
      (
      #{id,jdbcType=VARCHAR},
      #{ecMemberId,jdbcType=VARCHAR},
      #{ecMemberName,jdbcType=VARCHAR},
      #{ecCarrierId,jdbcType=VARCHAR},
      #{ecShippingId,jdbcType=VARCHAR},
      #{ecShippingName,jdbcType=VARCHAR},
      #{erpCarrierNo,jdbcType=VARCHAR},
      #{erpCarrierName,jdbcType=VARCHAR},
      #{erpShippingCode,jdbcType=VARCHAR},
      #{erpShippingName,jdbcType=VARCHAR},
      #{delFlg,jdbcType=INTEGER},
      #{createUser,jdbcType=VARCHAR},
      NOW()
      )
    ON DUPLICATE KEY UPDATE
    ec_member_id = values(ec_member_id),
    ec_member_name = values(ec_member_name),
    ec_carrier_id = values(ec_carrier_id),
    ec_shipping_id = values(ec_shipping_id),
    ec_shipping_name = values(ec_shipping_name),
    erp_carrier_no = values(erp_carrier_no),
    erp_carrier_name = values(erp_carrier_name),
    erp_shipping_code = values(erp_shipping_code),
    erp_shipping_name = values(erp_shipping_name),
    update_user = values(update_user),
    update_time = NOW(),
    del_flg = 0
  </insert>

  <select id="findErpShippingMapByCondition" parameterType="com.ecommerce.logistics.dao.vo.ErpShippingMap"
          resultType="com.ecommerce.logistics.dao.vo.ErpShippingMap">
    SELECT
      *
    FROM
      lgs_erp_shipping_map
    WHERE
      del_flg = 0
      <if test="ecMemberId != null and ecMemberId != ''">
        AND ec_member_id = #{ecMemberId}
      </if>
      <if test="ecCarrierId != null and ecCarrierId != ''">
        AND ec_carrier_id = #{ecCarrierId}
      </if>
      <if test="ecShippingId != null and ecShippingId != ''">
        AND ec_shipping_id = #{ecShippingId}
      </if>
      <if test="erpCarrierNo != null and erpCarrierNo != ''">
        AND erp_carrier_no = #{erpCarrierNo}
      </if>
    ORDER BY create_time DESC
    LIMIT 1
  </select>
</mapper>