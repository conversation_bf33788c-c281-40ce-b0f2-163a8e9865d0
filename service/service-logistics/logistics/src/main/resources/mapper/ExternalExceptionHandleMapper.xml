<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.ExternalExceptionHandleMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.ExternalExceptionHandle">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="exception_handle_id" jdbcType="VARCHAR" property="exceptionHandleId" />
    <result column="waybill_id" jdbcType="VARCHAR" property="waybillId" />
    <result column="waybill_num" jdbcType="VARCHAR" property="waybillNum" />
    <result column="method_type" jdbcType="VARCHAR" property="methodType" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="exception_reason" jdbcType="VARCHAR" property="exceptionReason" />
    <result column="retry_count" jdbcType="INTEGER" property="retryCount" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <select id="queryExternalExceptionList" parameterType="com.ecommerce.logistics.api.dto.externalexception.ExternalExceptionQueryDTO" resultType="com.ecommerce.logistics.api.dto.externalexception.ExternalExceptionListDTO">
    SELECT
      t1.exception_handle_id as exceptionHandleId,
      t1.waybill_id as waybillId,
      t1.waybill_num as waybillNum,
      t1.method_type as methodType,
      t1.status as status,
      t1.exception_reason as exceptionReason,
      t1.create_time as createTime,
      t2.transport_tool_type as transportToolType,
      t3.waybill_item_id as waybillItemId
    FROM
      lgs_external_exception_handle as t1
    LEFT JOIN lgs_ship_bill t2 on t2.waybill_id = t1.waybill_id
    LEFT JOIN lgs_ship_bill_item t3 on t3.waybill_id = t1.waybill_id
    where t3.del_flg = 0 and t3.bill_proxy_type in ('030400100', '030400200')
    <if test="waybillNum != null and waybillNum != ''">
      AND t1.waybill_num = #{waybillNum}
    </if>
    <if test="methodType != null and methodType != ''">
      AND t1.method_type = #{methodType}
    </if>
    <if test="status != null and status != ''">
      AND t1.status = #{status}
    </if>
    <if test="createTimeStart != null and createTimeStart != ''">
      <![CDATA[AND t1.create_time >= #{createTimeStart}]]>
    </if>
    <if test="createTimeEnd != null and createTimeEnd != ''">
      <![CDATA[AND t1.create_time <= #{createTimeEnd}]]>
    </if>
    ORDER BY
      t1.create_time DESC
  </select>

  <select id="queryExceptionIdsForCreateWaybillFail" resultType="java.lang.String">
    select distinct leeh.exception_handle_id
    from lgs_external_exception_handle as leeh
    left join lgs_ship_bill as lsb on leeh.waybill_id = lsb.waybill_id
    left join lgs_ship_bill_item as item on lsb.waybill_id = item.waybill_id and lsb.max_seq_num = item.seq_num
    left join lgs_delivery_info as info on info.delivery_info_id = item.delivery_info_id
    where leeh.method_type = '030310100'
      and lsb.external_waybill_status = '030280210'
      and info.take_code in
          <foreach close=")" collection="deliverySheetNumList" index="din" item="dit" open="(" separator=",">
            #{dit}
          </foreach>
  </select>

</mapper>