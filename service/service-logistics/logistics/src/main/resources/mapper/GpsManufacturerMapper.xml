<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.GpsManufacturerMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.GpsManufacturer">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="gps_manufacturer_id" jdbcType="VARCHAR" property="gpsManufacturerId" />
    <result column="gps_name" jdbcType="VARCHAR" property="gpsName" />
    <result column="number" jdbcType="VARCHAR" property="number" />
    <result column="gps_protocol_type" jdbcType="VARCHAR" property="gpsProtocolType" />
    <result column="gps_user_name" jdbcType="VARCHAR" property="gpsUserName" />
    <result column="gps_password" jdbcType="VARCHAR" property="gpsPassword" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="version" jdbcType="BIGINT" property="version" />
  </resultMap>

  <resultMap id="ExtDtoResultMap" type="com.ecommerce.logistics.api.dto.gps.GpsManufacturerDTO">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="gps_manufacturer_id" jdbcType="VARCHAR" property="gpsManufacturerId" />
    <result column="gps_name" jdbcType="VARCHAR" property="gpsName" />
    <result column="number" jdbcType="VARCHAR" property="number" />
    <result column="gps_protocol_type" jdbcType="VARCHAR" property="gpsProtocolType" />
    <result column="gps_user_name" jdbcType="VARCHAR" property="gpsUserName" />
    <result column="gps_password" jdbcType="VARCHAR" property="gpsPassword" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <select id="queryGpsNameByIds" resultType="com.ecommerce.logistics.api.dto.gpsmanufacturer.GpsNameDTO">
    SELECT gps_name,gps_manufacturer_id
    FROM lgs_gps_manufacturer
    WHERE del_flg = 0 AND gps_manufacturer_id IN
    <foreach close=")" collection="ids" index="index" item="item" open="(" separator=",">
      #{item}
    </foreach>
  </select>

  <select id="queryOptions" resultType="com.ecommerce.logistics.api.dto.gps.GpsManufacturerOptionDTO">
    SELECT gps_manufacturer_id, gps_name
    FROM lgs_gps_manufacturer
    WHERE del_flg = 0
  </select>

  <select id="queryGpsNameById" resultType="java.lang.String" parameterType="java.lang.String">
    SELECT gps_name
    FROM lgs_gps_manufacturer
    WHERE gps_manufacturer_id = #{gpsManufacturerId}
  </select>

  <select id="queryGpsManByCond" resultMap="ExtDtoResultMap" parameterType="com.ecommerce.logistics.api.dto.gps.GpsManCondDTO">
    SELECT
      gps_manufacturer_id,
      gps_name,
      number,
      gps_protocol_type,
      gps_user_name,
      gps_password,
      url,
      create_user,
      update_user,
      create_time,
      update_time
    FROM lgs_gps_manufacturer
    WHERE del_flg = 0
    <if test="number != null and number != ''">
      AND number = #{number}
    </if>
    <if test="gpsProtocolType != null and gpsProtocolType != ''">
      AND gps_protocol_type = #{gpsProtocolType}
    </if>
    <if test="gpsNameLike != null and gpsNameLike != ''">
      AND gps_name like concat('%', #{gpsNameLike}, '%')
    </if>
    ORDER BY create_time DESC
  </select>

</mapper>
