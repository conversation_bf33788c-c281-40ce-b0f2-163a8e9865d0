<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.IntelligentDispatchMapMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.IntelligentDispatchMap">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="rule_map_id" jdbcType="VARCHAR" property="ruleMapId" />
    <result column="rule_id" jdbcType="VARCHAR" property="ruleId" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="user_type" jdbcType="INTEGER" property="userType" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>