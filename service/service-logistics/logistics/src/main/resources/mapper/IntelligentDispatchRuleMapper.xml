<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.IntelligentDispatchRuleMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.IntelligentDispatchRule">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="rule_id" jdbcType="VARCHAR" property="ruleId" />
    <result column="rule_code" jdbcType="VARCHAR" property="ruleCode" />
    <result column="rule_type" jdbcType="INTEGER" property="ruleType" />
    <result column="applicable_object" jdbcType="INTEGER" property="applicableObject" />
    <result column="applicable_range" jdbcType="INTEGER" property="applicableRange" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="rule_algorithm" jdbcType="VARCHAR" property="ruleAlgorithm" />
    <result column="seq_num" jdbcType="INTEGER" property="seqNum" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <select id="queryRuleList" parameterType="com.ecommerce.logistics.api.dto.intelligentDispatch.IntelligentDispatchQueryDTO"
          resultType="com.ecommerce.logistics.api.dto.intelligentDispatch.IntelligentDispatchListDTO">
    select
        r.rule_id as ruleId,
        m.rule_map_id as ruleMapId,
        r.applicable_object as applicableObject,
        r.rule_type as type,
        r.rule_code as code,
        r.content as content,
        r.applicable_range as 'range',
        r.rule_algorithm as ruleAlgorithm,
        m.status as status,
        r.seq_num as seqNum
    from lgs_intelligent_dispatch_map m
    left join lgs_intelligent_dispatch_rule r on r.rule_id = m.rule_id
    where m.del_flg = 0 and r.del_flg = 0
    <if test="ruleCode != null and ruleCode != ''">
      AND r.rule_code = #{ruleCode}
    </if>
    <if test="ruleType != null">
      AND r.rule_type = #{ruleType}
    </if>
    <if test="applicableObject != null">
      AND r.applicable_object = #{applicableObject}
    </if>
    <if test="applicableRange != null">
        and r.applicable_range in
        <foreach collection="applicableRange" index="din" item="dii" open="(" separator="," close=")">
            #{dii}
        </foreach>
    </if>
    <if test="userId != null and userId != ''">
      and m.user_id = #{userId}
    </if>
    <if test="userType != null">
      and m.user_type = #{userType}
    </if>
    <if test="status != null">
      and m.status in
      <foreach collection="status" index="din" item="dii" open="(" separator="," close=")">
        #{dii}
      </foreach>
    </if>
    GROUP BY r.rule_code
    ORDER BY r.rule_code
  </select>

  <select id="queryAllRule" resultType="com.ecommerce.logistics.api.dto.intelligentDispatch.IntelligentDispatchListDTO">
    select
        r.rule_id as ruleId,
        r.applicable_object as applicableObject,
        r.rule_type as type,
        r.rule_code as code,
        r.content as content,
        r.applicable_range as 'range',
        r.rule_algorithm as ruleAlgorithm,
        r.status as status,
        r.seq_num as seqNum
    from lgs_intelligent_dispatch_rule r
    where r.del_flg = 0
  </select>

    <!--查询出用户下面的规则并按照约束，优先规则排序-->
    <select id="queryIntelligentDispatchRule" parameterType="com.ecommerce.logistics.api.dto.intelligentDispatch.IntelligentDispatchQueryDTO"
                resultType="com.ecommerce.logistics.api.dto.intelligentDispatch.IntelligentDispatchRuleDTO">
        SELECT
            t1.rule_code AS ruleCode,
            t1.rule_type AS ruleType,
            t1.applicable_object AS applicableObject,
            t1.applicable_range AS applicableRange,
            t1.content AS content,
            t1.rule_algorithm AS ruleAlgorithm,
            t1.seq_num AS seqNum,
            t2.user_id AS userId,
            t2.user_name AS userName,
            t2.user_type AS userType,
            t2.status AS status
        FROM lgs_intelligent_dispatch_rule t1
        LEFT JOIN lgs_intelligent_dispatch_map t2 ON t1.rule_id = t2.rule_id
        WHERE
        t1.status = 0
        AND t1.del_flg = 0
        AND t2.del_flg = 0
        <if test="userId != null">
            AND t2.user_id = #{userId}
        </if>
        <if test="userType != null">
            AND t2.user_type = #{userType}
        </if>
        <if test="applicableObject != null">
            AND t1.applicable_object = #{applicableObject}
        </if>
        <if test="applicableRange != null">
            AND t1.applicable_range in
            <foreach collection="applicableRange" index="din" item="dii" open="(" separator="," close=")">
                #{dii}
            </foreach>
        </if>
        <if test="status != null">
            AND t2.status in
            <foreach collection="status" index="din" item="dii" open="(" separator="," close=")">
                #{dii}
            </foreach>
        </if>
        ORDER BY t1.rule_type, t1.seq_num
    </select>
</mapper>