<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.InternalAllocationMapper">

  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.InternalAllocation">
    <id column="internal_allocation_id" jdbcType="VARCHAR" property="internalAllocationId" />
    <result column="plan_no" jdbcType="VARCHAR" property="planNo" />
    <result column="plan_status" jdbcType="INTEGER" property="planStatus" />
    <result column="delivery_info_id" jdbcType="VARCHAR" property="deliveryInfoId" />
    <result column="owner_id" jdbcType="VARCHAR" property="ownerId" />
    <result column="owner_name" jdbcType="VARCHAR" property="ownerName" />
    <result column="send_warehouse_id" jdbcType="VARCHAR" property="sendWarehouseId" />
    <result column="send_warehouse_name" jdbcType="VARCHAR" property="sendWarehouseName" />
    <result column="send_warehouse_address" jdbcType="VARCHAR" property="sendWarehouseAddress" />
    <result column="receive_warehouse_id" jdbcType="VARCHAR" property="receiveWarehouseId" />
    <result column="receive_warehouse_name" jdbcType="VARCHAR" property="receiveWarehouseName" />
    <result column="receive_warehouse_address" jdbcType="VARCHAR" property="receiveWarehouseAddress" />
    <result column="goods_id" jdbcType="VARCHAR" property="goodsId" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="receive_user_id" jdbcType="VARCHAR" property="receiveUserId" />
    <result column="receive_user_name" jdbcType="VARCHAR" property="receiveUserName" />
    <result column="receive_user_phone" jdbcType="VARCHAR" property="receiveUserPhone" />
    <result column="plan_time" jdbcType="TIMESTAMP" property="planTime" />
    <result column="plan_quantity" jdbcType="DECIMAL" property="planQuantity" />
    <result column="actual_quantity" jdbcType="DECIMAL" property="actualQuantity" />
    <result column="dispatch_mode" jdbcType="VARCHAR" property="dispatchMode" />
    <result column="logistics_mode" jdbcType="VARCHAR" property="logisticsMode" />
    <result column="carrier_id" jdbcType="VARCHAR" property="carrierId" />
    <result column="carrier_name" jdbcType="VARCHAR" property="carrierName" />
    <result column="delivery_bill_num" jdbcType="VARCHAR" property="deliveryBillNum" />
    <result column="waybill_id" jdbcType="VARCHAR" property="waybillId" />
    <result column="waybill_num" jdbcType="VARCHAR" property="waybillNum" />
    <result column="shipping_id" jdbcType="VARCHAR" property="shippingId" />
    <result column="shipping_no" jdbcType="VARCHAR" property="shippingNo" />
    <result column="shipping_name" jdbcType="VARCHAR" property="shippingName" />
    <result column="captain_id" jdbcType="VARCHAR" property="captainId" />
    <result column="captain_name" jdbcType="VARCHAR" property="captainName" />
    <result column="captain_phone" jdbcType="VARCHAR" property="captainPhone" />
    <result column="plan_arrival_time" jdbcType="TIMESTAMP" property="planArrivalTime" />
    <result column="external_waybill_num" jdbcType="VARCHAR" property="externalWaybillNum" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="del_flg" jdbcType="INTEGER" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <!--删除内部调拨-->
  <update id="delInternalAllocation" parameterType="com.ecommerce.logistics.api.dto.delivery.InternalAllocationDTO" >
    UPDATE lgs_internal_allocation
    <set>
      del_flg = 1,
      update_time = now(),
      update_user = #{updateUser}
    </set>
    WHERE internal_allocation_id = #{internalAllocationId}
    AND owner_id = #{ownerId}
  </update>

  <!--修改内部调拨信息-->
  <update id="updateInternalAllocation" parameterType="com.ecommerce.logistics.dao.vo.InternalAllocation" >
    UPDATE lgs_internal_allocation
    <set>
      <if test="planStatus != null and planStatus != '' " >
        plan_status = #{planStatus},
      </if>
      <if test="sendWarehouseId != null and sendWarehouseId != '' " >
        send_warehouse_id = #{sendWarehouseId},
      </if>
      <if test="sendWarehouseName != null and sendWarehouseName != '' " >
        send_warehouse_name = #{sendWarehouseName},
      </if>
      <if test="sendWarehouseAddress != null and sendWarehouseAddress != '' " >
        send_warehouse_address = #{sendWarehouseAddress},
      </if>
      <if test="receiveWarehouseId != null and receiveWarehouseId != '' " >
        receive_warehouse_id = #{receiveWarehouseId},
      </if>
      <if test="receiveWarehouseName != null and receiveWarehouseName != '' " >
        receive_warehouse_name = #{receiveWarehouseName},
      </if>
      <if test="receiveWarehouseAddress != null and receiveWarehouseAddress != '' " >
        receive_warehouse_address = #{receiveWarehouseAddress},
      </if>
      <if test="goodsId != null and goodsId != '' " >
        goods_id = #{goodsId},
      </if>
      <if test="goodsName != null and goodsName != '' " >
        goods_name = #{goodsName},
      </if>
      <if test="receiveUserId != null and receiveUserId != '' " >
        receive_user_id = #{receiveUserId},
      </if>
      <if test="receiveUserName != null and receiveUserName != '' " >
        receive_user_name = #{receiveUserName},
      </if>
      <if test="receiveUserPhone != null and receiveUserPhone != '' " >
        receive_user_phone = #{receiveUserPhone},
      </if>
      <if test="planTime != null" >
        plan_time = #{planTime},
      </if>
      <if test="planQuantity != null" >
        plan_quantity = #{planQuantity},
      </if>
      <if test="dispatchMode != null and dispatchMode != '' " >
        dispatch_mode = #{dispatchMode},
      </if>
      <if test="logisticsMode != null and logisticsMode != '' " >
        logistics_mode = #{logisticsMode},
      </if>
      <if test="carrierId != null and carrierId != '' " >
        carrier_id = #{carrierId},
      </if>
      <if test="carrierName != null and carrierName != '' " >
        carrier_name = #{carrierName},
      </if>
      <if test="deliveryBillNum != null and deliveryBillNum != '' " >
        delivery_bill_num = #{deliveryBillNum},
      </if>
      <if test="deliveryInfoId != null and deliveryInfoId != '' " >
        delivery_info_id = #{deliveryInfoId},
      </if>
      <if test="waybillId != null and waybillId != '' " >
        waybill_id = #{waybillId},
      </if>
      <if test="waybillNum != null and waybillNum != '' " >
        waybill_num = #{waybillNum},
      </if>
      <if test="shippingNo != null and shippingNo != '' " >
        shipping_no = #{shippingNo},
      </if>
      <if test="shippingName != null and shippingName != '' " >
        shipping_name = #{shippingName},
      </if>
      <if test="captainId != null and captainId != '' " >
        captain_id = #{captainId},
      </if>
      <if test="captainName != null and captainName != '' " >
        captain_name = #{captainName},
      </if>
      <if test="captainPhone != null and captainPhone != '' " >
        captain_phone = #{captainPhone},
      </if>
      <if test="planArrivalTime != null" >
        plan_arrival_time = #{planArrivalTime},
      </if>
      <if test="remarks != null and remarks != '' " >
        remarks = #{remarks},
      </if>
      update_time = now(),
      update_user = #{updateUser}
    </set>
    WHERE internal_allocation_id = #{internalAllocationId}
    AND owner_id = #{ownerId}
  </update>

  <!--获取内部调拨列表-->
  <select id="queryInternalAllocationList" parameterType="com.ecommerce.logistics.api.dto.waybill.AllocationListQueryDTO"
          resultType="com.ecommerce.logistics.api.dto.delivery.InternalAllocationDTO">
    SELECT
      internal_allocation_id AS internalAllocationId,
      plan_no AS planNo,
      plan_status planStatus,
      delivery_info_id AS deliveryInfoId,
      owner_id AS ownerId,
      owner_name AS ownerName,
      send_warehouse_id AS sendWarehouseId,
      send_warehouse_name AS sendWarehouseName,
      send_warehouse_address AS sendWarehouseAddress,
      receive_warehouse_id AS receiveWarehouseId,
      receive_warehouse_name AS receiveWarehouseName,
      receive_warehouse_address AS receiveWarehouseAddress,
      goods_id AS goodsId,
      goods_name AS goodsName,
      receive_user_id AS receiveUserId,
      receive_user_name AS receiveUserName,
      receive_user_phone AS receiveUserPhone,
      plan_time AS planTime,
      plan_quantity AS planQuantity,
      actual_quantity AS actualQuantity,
      dispatch_mode AS dispatchMode,
      logistics_mode AS logisticsMode,
      carrier_id AS carrierId,
      carrier_name AS carrierName,
      delivery_bill_num AS deliveryBillNum,
      waybill_id AS waybillId,
      waybill_num AS waybillNum,
      external_waybill_num AS externalWaybillNum,
      shipping_id AS shippingId,
      shipping_no AS shippingNo,
      shipping_name AS shippingName,
      captain_id AS captainId,
      captain_name AS captainName,
      captain_phone AS captainPhone,
      plan_arrival_time AS planArrivalTime,
      remarks AS remarks,
      create_time AS createTime,
      update_time AS updateTime
    FROM lgs_internal_allocation
    WHERE del_flg=0
    <if test="ownerId != null and ownerId != '' ">
      AND owner_id = #{ownerId}
    </if>
    <if test="planNo != null and planNo != '' ">
      AND plan_no LIKE CONCAT('%', #{planNo}, '%')
    </if>
    <if test="planStatus != null">
      AND plan_status = #{planStatus}
    </if>
    <if test="sendWarehouseId != null and sendWarehouseId != '' ">
      AND send_warehouse_id = #{sendWarehouseId}
    </if>
    <if test="receiveWarehouseId != null and receiveWarehouseId != '' ">
      AND receive_warehouse_id = #{receiveWarehouseId}
    </if>
    <if test="goodsName != null and goodsName != '' ">
      AND goods_name LIKE CONCAT('%', #{goodsName}, '%')
    </if>
    <if test="planTimeStart != null and planTimeStart != ''">
      <![CDATA[AND plan_time >= #{planTimeStart}]]>
    </if>
    <if test="planTimeEnd != null and planTimeEnd != ''">
      <![CDATA[AND plan_time <= #{planTimeEnd}]]>
    </if>
    <if test="logisticsMode != null and logisticsMode != ''">
      AND logistics_mode = #{logisticsMode}
    </if>
    ORDER BY create_time DESC
  </select>

  <select id="getInternalAllocation" parameterType="com.ecommerce.logistics.api.dto.delivery.InternalAllocationDTO"
          resultType="com.ecommerce.logistics.dao.vo.InternalAllocation">
    SELECT * FROM
    lgs_internal_allocation
    WHERE del_flg = 0
    AND internal_allocation_id = #{internalAllocationId}
    AND owner_id = #{ownerId}
  </select>

  <select id="getInternalByBillId" resultType="com.ecommerce.logistics.dao.vo.InternalAllocation">
    SELECT t1.* FROM
    lgs_internal_allocation t1
    LEFT JOIN lgs_delivery_bill t2 ON t1.delivery_bill_num = t2.delivery_bill_num
    WHERE t1.del_flg = 0 AND t2.del_flg = 0
    AND t2.delivery_bill_id = #{deliveryBillId}
  </select>

  <!--修改内部调拨状态-->
  <update id="updateInternalPlanStatus" parameterType="com.ecommerce.logistics.api.dto.delivery.InternalAllocationDTO" >
    UPDATE lgs_internal_allocation
    <set>
      <if test="planStatus != null and planStatus != '' " >
        plan_status = #{planStatus},
      </if>
      <if test="actualQuantity != null " >
        actual_quantity = #{actualQuantity},
      </if>
      update_time = now(),
      update_user = #{userId}
    </set>
    WHERE del_flg = 0
    <if test="waybillId != null and waybillId != '' " >
      AND waybill_id = #{waybillId}
    </if>
    <if test="deliveryInfoId != null and deliveryInfoId != '' " >
      AND delivery_info_id = #{deliveryInfoId}
    </if>
    <if test="ownerId != null and ownerId != '' " >
      AND owner_id = #{ownerId}
    </if>
  </update>

</mapper>
