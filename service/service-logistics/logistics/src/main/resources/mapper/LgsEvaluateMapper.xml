<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.LgsEvaluateMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.LgsEvaluate">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="evaluate_id" jdbcType="VARCHAR" property="evaluateId" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="buyer_id" jdbcType="VARCHAR" property="buyerId" />
    <result column="buyer_name" jdbcType="VARCHAR" property="buyerName" />
    <result column="carrier_id" jdbcType="VARCHAR" property="carrierId" />
    <result column="carrier_name" jdbcType="VARCHAR" property="carrierName" />
    <result column="driver_id" jdbcType="VARCHAR" property="driverId" />
    <result column="driver_name" jdbcType="VARCHAR" property="driverName" />
    <result column="seller_id" jdbcType="VARCHAR" property="sellerId" />
    <result column="seller_name" jdbcType="VARCHAR" property="sellerName" />
    <result column="score" jdbcType="DECIMAL" property="score" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="impression" jdbcType="VARCHAR" property="impression" />
    <result column="img" jdbcType="VARCHAR" property="img" />
    <result column="origin" jdbcType="BIT" property="origin" />
    <result column="is_auto" jdbcType="BIT" property="isAuto" />
    <result column="is_anonymous" jdbcType="BIT" property="isAnonymous" />
    <result column="is_show" jdbcType="BIT" property="isShow" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>
</mapper>