<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.LoadingTimeMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.LoadingTime">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="loading_time_id" jdbcType="VARCHAR" property="loadingTimeId" />
    <result column="loading_id" jdbcType="VARCHAR" property="loadingId" />
    <result column="seller_id" jdbcType="VARCHAR" property="sellerId" />
    <result column="seller_name" jdbcType="VARCHAR" property="sellerName" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="user_type" jdbcType="INTEGER" property="userType" />
    <result column="loading_time" jdbcType="INTEGER" property="loadingTime" />
    <result column="loading_km" jdbcType="DECIMAL" property="loadingKm" />
    <result column="loading_waiting_time" jdbcType="INTEGER" property="loadingWaitingTime" />
    <result column="loading_waiting_km" jdbcType="DECIMAL" property="loadingWaitingKm" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <select id="queryLoadingTimeList"
          resultType="com.ecommerce.logistics.api.dto.intelligentDispatch.LoadingTimeDTO"
          parameterType="com.ecommerce.logistics.api.dto.intelligentDispatch.LoadingTimeDTO">
    SELECT *
    FROM lgs_loading_time
    <where>
      del_flg = 0
      <if test="loadingId != null and loadingId != ''">
        AND loading_id = #{loadingId}
      </if>
      <if test="sellerId != null and sellerId != ''">
        AND seller_id = #{sellerId}
      </if>
      <if test="sellerName != null and sellerName != ''">
        AND seller_name LIKE CONCAT('%', #{sellerName}, '%')
      </if>
      <if test="userId != null and userId != ''">
        AND user_id = #{userId}
      </if>
      <if test="userName != null and userName != ''">
        AND user_name LIKE CONCAT('%', #{userName}, '%')
      </if>
      <if test="userType != null">
        AND user_type = #{userType}
      </if>
    </where>
    ORDER BY update_time DESC
  </select>
</mapper>