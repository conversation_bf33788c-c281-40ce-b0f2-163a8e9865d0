<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.OperationRecordMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.OperationRecord">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="operation_record_id" jdbcType="VARCHAR" property="operationRecordId" />
    <result column="entry_id" jdbcType="VARCHAR" property="entryId" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="operation_type" jdbcType="INTEGER" property="operationType" />
    <result column="operator_id" jdbcType="VARCHAR" property="operatorId" />
    <result column="operator_name" jdbcType="VARCHAR" property="operatorName" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="version" jdbcType="BIGINT" property="version" />
  </resultMap>
  <select id="selectOperationRecordListDTO" resultType="com.ecommerce.logistics.dao.vo.OperationRecord">
    SELECT
      operator_id as operatorId,
      operator_name as operatorName,
      content as content,
      update_time as updateTime
    FROM
      lgs_operation_record
    WHERE
      entry_id=#{entryId} AND del_flg=0
     ORDER BY
      update_time DESC, create_time desc
  </select>
  <select id="queryWaybillOperation" parameterType="com.ecommerce.logistics.api.dto.operationrecord.QueryWaybillOperationDTO"
          resultType="com.ecommerce.logistics.api.dto.operationrecord.OperationRecordDTO">
    SELECT
      operator_id as operationUserId,
      operator_name as operationUserName,
      operator_name as operationUserName,
      content as content,
      operation_type as operationType,
      update_time as operationTime,
      create_time as createTime
    FROM
      lgs_operation_record
    WHERE
      entry_id=#{waybillId} AND del_flg=0
      <if test="operationType != null and operationType > 0">
        AND operation_type = #{operationType}
      </if>
    ORDER BY
    update_time DESC, create_time desc
  </select>
</mapper>