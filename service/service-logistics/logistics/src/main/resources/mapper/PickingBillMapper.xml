<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.PickingBillMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.PickingBill">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="picking_bill_id" jdbcType="VARCHAR" property="pickingBillId" />
    <result column="delivery_sheet_num" jdbcType="VARCHAR" property="deliverySheetNum" />
    <result column="picking_bill_num" jdbcType="VARCHAR" property="pickingBillNum" />
    <result column="seq_num" jdbcType="BIGINT" property="seqNum" />
    <result column="warehouse_id" jdbcType="VARCHAR" property="warehouseId" />
    <result column="buyer_id" jdbcType="VARCHAR" property="buyerId" />
    <result column="buyer_name" jdbcType="VARCHAR" property="buyerName" />
    <result column="mdm_code" jdbcType="VARCHAR" property="mdmCode" />
    <result column="seller_id" jdbcType="VARCHAR" property="sellerId" />
    <result column="seller_name" jdbcType="VARCHAR" property="sellerName" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="relation_type" jdbcType="VARCHAR" property="relationType" />
    <result column="bill_proxy_type" jdbcType="VARCHAR" property="billProxyType" />
    <result column="can_operate" jdbcType="TINYINT" property="canOperate" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="endReason" jdbcType="VARCHAR" property="end_reason" />
    <result column="assign_mode" jdbcType="VARCHAR" property="assignMode" />
    <result column="transport_mode" jdbcType="VARCHAR" property="transportMode" />
    <result column="data_source" jdbcType="VARCHAR" property="dataSource" />
    <result column="receiver" jdbcType="VARCHAR" property="receiver" />
    <result column="receiver_phone" jdbcType="VARCHAR" property="receiverPhone" />
    <result column="receive_address_id" jdbcType="VARCHAR" property="receiveAddressId" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="district" jdbcType="VARCHAR" property="district" />
    <result column="district_code" jdbcType="VARCHAR" property="districtCode" />
    <result column="street" jdbcType="VARCHAR" property="street" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="location" jdbcType="VARCHAR" property="location" />
    <result column="delivery_time" jdbcType="DATE" property="deliveryTime" />
    <result column="delivery_time_range" jdbcType="VARCHAR" property="deliveryTimeRange" />
    <result column="income_carriage" jdbcType="DECIMAL" property="incomeCarriage" />
    <result column="is_evaluate" jdbcType="TINYINT" property="isEvaluate" />
    <result column="deals_id" jdbcType="VARCHAR" property="dealsId" />
    <result column="deals_name" jdbcType="VARCHAR" property="dealsName" />
    <result column="project_name" jdbcType="VARCHAR" property="projectName" />
    <result column="picking_time" jdbcType="TIMESTAMP" property="pickingTime" />
    <result column="expend_carriage" jdbcType="DECIMAL" property="expendCarriage" />
    <result column="sync_flag" jdbcType="VARCHAR" property="syncFlag" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="version" jdbcType="BIGINT" property="version" />
  </resultMap>
  <select id="selectPickingBillList" parameterType="com.ecommerce.logistics.api.dto.pickingbill.PickingBillListQueryDTO" resultType="com.ecommerce.logistics.dao.dto.pickingbill.PickingBillListDO">
    SELECT
      t1.picking_bill_id as pickingBillId,
      t1.picking_bill_num as pickingBillNum,
      t1.seq_num as seqNum,
      t1.delivery_sheet_num as deliverySheetNum,
      t3.product_id as productId,
      t3.note as productDesc,
      t3.special_flag as specialFlag,
      t3.transport_category_id as transportCategoryId,
      t2.quantity as productQuantity,
      t2.delivery_quantity as deliveryQuantity,
      t2.complete_quantity as completeQuantity,
      t3.unit as productUnit,
      t1.receiver as receiver,
      t1.receiver_phone as receiverPhone,
      t1.province as receiveProvince,
      t1.city as receiveCity,
      t1.district as receiveDistrict,
      t1.province_code as receiveProvinceCode,
      t1.city_code as receiveCityCode,
      t1.district_code as receiveDistrictCode,
      t1.street as receiveStreet,
      t1.address as receiveAddress,
      t1.warehouse_id as warehouseId,
      t1.buyer_id as buyerId,
      t1.buyer_name as buyerName,
      t1.seller_id as sellerId,
      t1.seller_name as sellerName,
      t1.delivery_time as deliveryTime,
      t1.delivery_time_range as deliveryTimeRange,
      t1.type as type,
      t1.assign_mode as assignMode,
      t1.status as status,
      t1.create_time as createTime,
      t1.sync_flag as syncFlag,
      t1.picking_time as pickingTime,
      t1.bill_proxy_type as billProxyType,
      t1.can_operate as canOperate,
      t1.is_evaluate as isEvaluate
    FROM
      lgs_picking_bill as t1
    LEFT JOIN
      lgs_product_quantity_map as t2 on t1.picking_bill_id=t2.map_id
    LEFT JOIN
      lgs_product_info as t3 on t2.product_info_id=t3.product_info_id
    <if test="regionCodeList != null and regionCodeList.size() > 0">
    INNER JOIN
      ba_region_all as auth on t1.district_code = auth.region_son_code
    </if>
    <where>
      <if test="regionCodeList != null and regionCodeList.size() > 0">
        AND auth.region_code IN
        <foreach close=")" collection="regionCodeList" index="index" item="item" open="(" separator=",">
          #{item}
        </foreach>
      </if>
      <if test="pickingBillNum != null and pickingBillNum != ''">
        AND t1.picking_bill_num = #{pickingBillNum}
      </if>
      <if test="deliverySheetNum != null and deliverySheetNum != ''">
        AND t1.delivery_sheet_num = #{deliverySheetNum}
      </if>
      <if test="warehouseId != null and warehouseId != ''">
        AND t1.warehouse_id = #{warehouseId}
      </if>
      <if test="provinceCode != null and provinceCode != ''">
        AND t1.province_code = #{provinceCode}
      </if>
      <if test="cityCode != null and cityCode != ''">
        AND t1.city_code = #{cityCode}
      </if>
      <if test="districtCode !=null and districtCode != ''">
        AND t1.district_code = #{districtCode}
      </if>
      <if test="buyerId != null and buyerId != ''">
        AND t1.buyer_id = #{buyerId}
      </if>
      <if test="sellerId != null and sellerId != ''">
        AND t1.seller_id = #{sellerId}
      </if>
      <if test="type != null and type != ''">
        <![CDATA[AND t1.type = #{type}]]>
      </if>
      <if test="assignMode != null and assignMode != ''">
        <![CDATA[AND t1.assign_mode = #{assignMode}]]>
      </if>
      <if test="deliveryTimeStart != null and deliveryTimeStart != ''">
        <![CDATA[AND t1.delivery_time >= #{deliveryTimeStart}]]>
      </if>
      <if test="deliveryTimeEnd != null and deliveryTimeEnd != ''">
        <![CDATA[AND t1.delivery_time <= #{deliveryTimeEnd}]]>
      </if>
      <if test="status != null and status != ''">
        AND t1.status = #{status}
      </if>
      <if test="transportCategoryId != null and transportCategoryId != ''">
        AND t3.transport_category_id = #{transportCategoryId}
      </if>
      <if test="receiver != null and receiver != ''">
        AND t1.receiver LIKE concat('%', #{receiver}, '%')
      </if>
    </where>
    ORDER BY
      t1.create_time DESC
  </select>
  <select id="getPickingBillDetail" resultType="com.ecommerce.logistics.api.dto.pickingbill.PickingBillDTO">
    SELECT
      t1.picking_bill_id as pickingBillId,
      t1.picking_bill_num as pickingBillNum,
      t1.seq_num as seqNum,
      t1.delivery_sheet_num as deliverySheetNum,
      t1.type as type,
      t1.assign_mode as assignMode,
      t1.sync_flag as syncFlag,
      t3.transport_category_id as transportCategoryId,
      t3.note as productDesc,
      t3.product_id as productId,
      t3.carriage_unit_price as unitCarriage,
      t2.shipping_quantity as shippingQuantity,
      t2.complete_quantity as completeQuantity,
      t2.delivery_quantity AS deliveryQuantity,
      t2.quantity as productQuantity,
      t3.unit as productUnit,
      t3.vendor as vendor,
      t3.product_img as productImg,
      t3.special_flag as specialFlag,
      t3.product_id as resourceId,
      t3.sign_type as signType,
      t1.receive_address_id as receiveAddressId,
      t1.location as receiveAddressLocation,
      t1.province as receiveProvince,
      t1.province_code as receiveProvinceCode,
      t1.city as receiveCity,
      t1.city_code as receiveCityCode,
      t1.district as receiveDistrict,
      t1.district_code as receiveDistrictCode,
      t1.street as receiveStreet,
      t1.receiver as receiver,
      t1.receiver_phone as receiverPhone,
      concat(t1.province, t1.city, t1.district, t1.street, t1.address) as receiveAddress,
      t1.warehouse_id as warehouseId,
      t1.delivery_time as deliveryTime,
      t1.delivery_time_range as deliveryTimeRange,
      t1.status as status,
      t1.buyer_id as buyerId,
      t1.buyer_name as buyerName,
      t1.seller_id as sellerId,
      t1.seller_name as sellerName,
      t1.create_time as createTime,
      t1.picking_time as pickingTime,
      t1.bill_proxy_type as billProxyType,
      t1.can_operate as canOperate
    FROM
      lgs_picking_bill as t1
    LEFT JOIN
      lgs_product_quantity_map as t2 on t1.picking_bill_id=t2.map_id
    LEFT JOIN
      lgs_product_info as t3 on t2.product_info_id=t3.product_info_id
    WHERE
      t1.picking_bill_id = #{pickingBillId}
  </select>
  <select id="batchCancelPickingBill">
    UPDATE
      lgs_picking_bill set status=5
    WHERE
      picking_bill_id IN
    <foreach close=")" collection="collection" index="index" item="item" open="(" separator=",">
      #{item}
    </foreach>
  </select>
  
  <select id="selectPickingBillsByWaybillIds" parameterType="java.lang.String" resultType="com.ecommerce.logistics.dao.dto.pickingbill.PickingBillListDO">
  	SELECT
  		p.picking_bill_id as pickingBillId,
  		p.delivery_sheet_num as deliverySheetNum,
  		p.warehouse_id as wareHouseId,
  		p.receiver as receiver,
  		p.receiver_phone as receiverPhone,
  		p.delivery_time as deliveryTime,
  		p.delivery_time_range as deliveryTimeRange,
  		p.province as receiveProvince,
  		p.city as receiveCity,
  		p.district as receiveDistrict,
  		p.street as receiveStreet,
  		p.address as receiveAddress,
  		m.product_info_id as productId,
  		m.quantity as productQuantity
  	FROM
  		lgs_waybill w
  	LEFT JOIN
  		lgs_picking_bill p ON p.picking_bill_id = w.picking_bill_id
  	LEFT JOIN
  		lgs_product_quantity_map m ON m.map_id = p.picking_bill_id
  	WHERE
  		w.parent_id = #{waybillId} AND w.del_flg = 0
  </select>
  <select id="selectBuyerOption" parameterType="java.lang.String" resultType="com.ecommerce.logistics.api.dto.pickingbill.OptionDTO">
    SELECT DISTINCT buyer_id AS value,buyer_name AS label
    FROM lgs_picking_bill
    WHERE buyer_name LIKE CONCAT('%',#{name},'%')
  </select>
  <select id="selectSellerOption" parameterType="java.lang.String" resultType="com.ecommerce.logistics.api.dto.pickingbill.OptionDTO">
    SELECT DISTINCT seller_id AS value,seller_name AS label
    FROM lgs_picking_bill
    WHERE seller_name LIKE CONCAT('%',#{name},'%')
  </select>
  <select id="selectPickingBillByIds" resultType="com.ecommerce.logistics.dao.dto.pickingbill.PickingBillListDO">
    SELECT
      t1.picking_bill_id as pickingBillId,
      t1.picking_bill_num as pickingBillNum,
      t1.seq_num as seqNum,
      t1.delivery_sheet_num as deliverySheetNum,
      t3.product_id as productId,
      t3.note as productDesc,
      t2.quantity as productQuantity,
      t2.delivery_quantity as deliveryQuantity,
      t2.complete_quantity as completeQuantity,
      t3.unit as productUnit,
      t3.transport_category_id as transportCategoryId,
      t1.receive_address_id as receiveAddressId,
      t1.province as receiveProvince,
      t1.city as receiveCity,
      t1.district as receiveDistrict,
      t1.province_code as receiveProvinceCode,
      t1.city_code as receiveCityCode,
      t1.district_code as receiveDistrictCode,
      t1.receiver_phone as receiverPhone,
      t1.street as receiveStreet,
      t1.address as receiveAddress,
      t1.location as receiveAddressLocation,
      t1.warehouse_id as warehouseId,
      t1.buyer_id as buyerId,
      t1.buyer_name as buyerName,
      t1.seller_id as sellerId,
      t1.seller_name as sellerName,
      t1.delivery_time as deliveryTime,
      t1.delivery_time_range as deliveryTimeRange,
      t1.type as type,
      t1.assign_mode as assignMode,
      t1.status as status,
      t1.create_time as createTime,
      t1.bill_proxy_type as billProxyType
    FROM
      lgs_picking_bill as t1
    LEFT JOIN
      lgs_product_quantity_map as t2 on t1.picking_bill_id=t2.map_id
    LEFT JOIN
      lgs_product_info as t3 on t2.product_info_id=t3.product_info_id
    WHERE
      t1.del_flg=0 AND picking_bill_id IN
    <foreach close=")" collection="collection" index="index" item="item" open="(" separator=",">
      #{item}
    </foreach>
  </select>
  <select id="selectPickingBillByDeliverySheetNum" resultType="com.ecommerce.logistics.dao.dto.pickingbill.ClosePickingBillDO">
    SELECT
      t1.picking_bill_id as pickingBillId,
      t1.status as status,
      t2.complete_quantity as completeQuantity,
      t2.quantity as quantity,
      t3.product_id as resourceId,
      t3.carriage_unit_price as unitCarriage
    FROM
      lgs_picking_bill as t1
    LEFT JOIN
      lgs_product_quantity_map as t2 on t1.picking_bill_id=t2.map_id
    LEFT JOIN
      lgs_product_info as t3 on t2.product_info_id=t3.product_info_id
    WHERE
      t1.delivery_sheet_num=#{deliverySheetNum} and t1.del_flg=0
  </select>

  <update id="updatePickingBillType" parameterType="com.ecommerce.logistics.api.dto.pickingbill.VendorEntrustmentDTO">
    UPDATE lgs_picking_bill
    SET type = #{type}, update_user = #{updateUser}
    WHERE picking_bill_id = #{pickingBillId}
  </update>
  <update id="updateDeliverySheetNumWarehouse" parameterType="com.ecommerce.logistics.dao.dto.pickingbill.UpdateWarehouseDO">
    UPDATE
      lgs_picking_bill
    SET
      type = #{deliveryType},
      status = #{status},
      warehouse_id = #{warehouseId}
    WHERE
      delivery_sheet_num = #{deliverySheetNum}
  </update>
  <select id="selectReceiveAddressByMainWaybillId" parameterType="java.lang.String" resultMap="BaseResultMap">
  	SELECT
  		DISTINCT t2.receive_address_id,
  		t2.province,
  		t2.city,
  		t2.district,
  		t2.street,
  		t2.address,
  		t2.warehouse_id
  	FROM
  		lgs_waybill t1
  	JOIN
  		lgs_picking_bill t2 ON t1.picking_bill_id = t2.picking_bill_id
  	WHERE
  		t1.parent_id = #{mainWaybillId}
  </select>
  <select id="statisticsPickingBillStatus" parameterType="com.ecommerce.logistics.api.dto.pickingbill.PickingBillListQueryDTO"
          resultType="com.ecommerce.logistics.api.dto.pickingbill.StatisticsPickingBillStatusDTO">
    SELECT
      t1.status   AS statusCode,
      count(*) AS statusCount
    FROM
      lgs_picking_bill as t1
    <where>
      <if test="pickingBillNum != null and pickingBillNum != ''">
        AND t1.picking_bill_num = #{pickingBillNum}
      </if>
      <if test="deliverySheetNum != null and deliverySheetNum != ''">
        AND t1.delivery_sheet_num = #{deliverySheetNum}
      </if>
      <if test="warehouseId != null and warehouseId != ''">
        AND t1.warehouse_id = #{warehouseId}
      </if>
      <if test="provinceCode != null and provinceCode != ''">
        AND t1.province_code = #{provinceCode}
      </if>
      <if test="cityCode != null and cityCode != ''">
        AND t1.city_code = #{cityCode}
      </if>
      <if test="districtCode !=null and districtCode != ''">
        AND t1.district_code = #{districtCode}
      </if>
      <if test="buyerId != null and buyerId != ''">
        AND t1.buyer_id = #{buyerId}
      </if>
      <if test="sellerId != null and sellerId != ''">
        AND t1.seller_id = #{sellerId}
      </if>
      <if test="type != null and type != ''">
        <![CDATA[AND t1.type = #{type}]]>
      </if>
      <if test="assignMode != null and assignMode != ''">
        <![CDATA[AND t1.assign_mode = #{assignMode}]]>
      </if>
      <if test="deliveryTimeStart != null and deliveryTimeStart != ''">
        <![CDATA[AND t1.delivery_time >= #{deliveryTimeStart}]]>
      </if>
      <if test="deliveryTimeEnd != null and deliveryTimeEnd != ''">
        <![CDATA[AND t1.delivery_time <= #{deliveryTimeEnd}]]>
      </if>
    </where>
    GROUP BY
      t1.status;
  </select>
  <select id="queryPickingBillByCondition" parameterType="com.ecommerce.logistics.api.dto.pickingbill.PickingBillQueryDTO"
          resultType="com.ecommerce.logistics.api.dto.pickingbill.PickingBillDTO">
    SELECT
      t1.picking_bill_id as pickingBillId,
      t1.picking_bill_num as pickingBillNum,
      t1.seq_num as seqNum,
      t1.delivery_sheet_num as deliverySheetNum,
      t1.type as type,
      t1.assign_mode as assignMode,
      t3.transport_category_id as transportCategoryId,
      t3.note as productDesc,
      t3.carriage_unit_price as unitCarriage,
      t2.shipping_quantity as shippingQuantity,
      t2.complete_quantity as completeQuantity,
      t2.delivery_quantity AS deliveryQuantity,
      t2.quantity as productQuantity,
      t3.unit as productUnit,
      t3.vendor as vendor,
      t3.product_img as productImg,
      t3.special_flag as specialFlag,
      t3.product_id as resourceId,
      t1.receive_address_id as receiveAddressId,
      t1.receiver as receiver,
      t1.receiver_phone as receiverPhone,
      concat(t1.province, t1.city, t1.district, t1.street, t1.address) as receiveAddress,
      t1.warehouse_id as warehouseId,
      t1.delivery_time as deliveryTime,
      t1.delivery_time_range as deliveryTimeRange,
      t1.status as status,
      t1.buyer_id as buyerId,
      t1.buyer_name as buyerName,
      t1.seller_id as sellerId,
      t1.seller_name as sellerName,
      t1.create_time as createTime,
      t1.picking_time as pickingTime
    FROM
      lgs_picking_bill as t1
    LEFT JOIN
      lgs_product_quantity_map as t2 on t1.picking_bill_id=t2.map_id
    LEFT JOIN
      lgs_product_info as t3 on t2.product_info_id=t3.product_info_id
    WHERE
      t1.delivery_sheet_num = #{deliverySheetNum} and t3.commodity_code = #{commodityCode}
  </select>

  <select id="exportPickingBill" resultType="com.ecommerce.logistics.api.dto.pickingbill.PickingBillExportDTO" parameterType="com.ecommerce.logistics.api.dto.pickingbill.PickingBillListQueryDTO">
    SELECT
    t1.picking_bill_num AS pickingBillNum,
    t1.status AS status,
    t1.buyer_name AS buyerName,
    t3.note AS goodsName,
    t2.quantity AS productQuantity,
    t2.delivery_quantity AS deliveryQuantity,
    t2.complete_quantity AS completeQuantity,
    t1.receiver AS receiver,
    t1.province AS provinceName,
    t1.city AS cityName,
    t1.district AS districtName,
    t1.street AS streetName,
    t1.address AS receiveAddress,
    t1.delivery_time AS deliveryTime,
    t1.delivery_time_range as deliveryTimeRange,
    t1.delivery_sheet_num AS deliverySheetNum,
    t1.type AS logisticType,
    t1.assign_mode AS assignMode,
    t3.transport_category_id AS transportCategoryId,
    t3.product_id AS orderCode,
    t3.special_flag AS specialFlag
    FROM lgs_picking_bill AS t1
    LEFT JOIN lgs_product_quantity_map AS t2 ON t1.picking_bill_id = t2.map_id
    LEFT JOIN lgs_product_info AS t3 ON t2.product_info_id = t3.product_info_id
    <if test="regionCodeList != null and regionCodeList.size() > 0">
      INNER JOIN ba_region_all AS auth ON t1.district_code = auth.region_son_code
    </if>
    <where>
      <if test="regionCodeList != null and regionCodeList.size() > 0">
        AND auth.region_code IN
        <foreach close=")" collection="regionCodeList" index="index" item="item" open="(" separator=",">
          #{item}
        </foreach>
      </if>
      <if test="pickingBillNum != null and pickingBillNum != ''">
        AND t1.picking_bill_num = #{pickingBillNum}
      </if>
      <if test="deliverySheetNum != null and deliverySheetNum != ''">
        AND t1.delivery_sheet_num = #{deliverySheetNum}
      </if>
      <if test="warehouseId != null and warehouseId != ''">
        AND t1.warehouse_id = #{warehouseId}
      </if>
      <if test="provinceCode != null and provinceCode != ''">
        AND t1.province_code = #{provinceCode}
      </if>
      <if test="cityCode != null and cityCode != ''">
        AND t1.city_code = #{cityCode}
      </if>
      <if test="districtCode !=null and districtCode != ''">
        AND t1.district_code = #{districtCode}
      </if>
      <if test="buyerId != null and buyerId != ''">
        AND t1.buyer_id = #{buyerId}
      </if>
      <if test="sellerId != null and sellerId != ''">
        AND t1.seller_id = #{sellerId}
      </if>
      <if test="type != null and type != ''">
        <![CDATA[AND t1.type = #{type}]]>
      </if>
      <if test="assignMode != null and assignMode != ''">
        <![CDATA[AND t1.assign_mode = #{assignMode}]]>
      </if>
      <if test="deliveryTimeStart != null and deliveryTimeStart != ''">
        <![CDATA[AND t1.delivery_time >= #{deliveryTimeStart}]]>
      </if>
      <if test="deliveryTimeEnd != null and deliveryTimeEnd != ''">
        <![CDATA[AND t1.delivery_time <= #{deliveryTimeEnd}]]>
      </if>
      <if test="status != null and status != ''">
        AND t1.status = #{status}
      </if>
      <if test="transportCategoryId != null and transportCategoryId != ''">
        AND t3.transport_category_id = #{transportCategoryId}
      </if>
      <if test="receiver != null and receiver != ''">
        AND t1.receiver LIKE concat('%', #{receiver}, '%')
      </if>
    </where>
    ORDER BY t1.create_time DESC
  </select>

  <update id="closeExpirePickingBillList">
    update lgs_picking_bill
    set status = '030050500',
    end_reason = '030360200',
    update_time = current_timestamp
    where picking_bill_id in
    <foreach collection="pickingBillIdList" item="pit" index="pii" open="(" separator="," close=")">
      #{pit}
    </foreach>
    and del_flg = 0
  </update>

  <select id="queryWarehouseIdByBuyerId" parameterType="java.lang.String" resultMap="BaseResultMap">
  	SELECT
        t2.warehouse_id
  	FROM
  		lgs_waybill t1
  	JOIN
  		lgs_picking_bill t2 ON t1.picking_bill_id = t2.picking_bill_id
  	WHERE
  	    t1.del_flg = 0 and t2.del_flg = 0
  		and t2.buyer_id = #{buyerId}
  		and t1.status in (
  		'030070400',
  		'030070500',
  		'030071200',
  		'030071100',
  		'030070600',
  		'030070700',
  		'030070800',
  		'030070810',
  		'030070900',
  		'030071000',
  		'030071300'
  		)
  </select>
  
  <select id="queryPickingBillByGoodsId" resultMap="BaseResultMap">
    SELECT
      t1.*
    FROM
      lgs_picking_bill as t1
    LEFT JOIN
      lgs_product_quantity_map as t2 on t1.picking_bill_id = t2.map_id
    LEFT JOIN
      lgs_product_info as t3 on t2.product_info_id = t3.product_info_id
    WHERE
      t1.delivery_sheet_num = #{deliverySheetNum} and t3.goods_id = #{goodsId}
  </select>

  <select id="queryPickingTypeById" resultType="java.lang.String">
    select type from lgs_picking_bill where picking_bill_id = #{pickingBillId}
  </select>

</mapper>