<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.PickingMapMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.PickingMap">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="picking_map_id" jdbcType="VARCHAR" property="pickingMapId" />
    <result column="primary_picking_bill_id" jdbcType="VARCHAR" property="primaryPickingBillId" />
    <result column="primary_picking_bill_num" jdbcType="VARCHAR" property="primaryPickingBillNum" />
    <result column="primary_delivery_sheet_num" jdbcType="VARCHAR" property="primaryDeliverySheetNum" />
    <result column="secondary_picking_bill_id" jdbcType="VARCHAR" property="secondaryPickingBillId" />
    <result column="secondary_picking_bill_num" jdbcType="VARCHAR" property="secondaryPickingBillNum" />
    <result column="secondary_delivery_sheet_num" jdbcType="VARCHAR" property="secondaryDeliverySheetNum" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="version" jdbcType="BIGINT" property="version" />
  </resultMap>
</mapper>