<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.PlatformStockLogMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.PlatformStockLog">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="log_id" jdbcType="VARCHAR" property="logId" />
    <result column="seller_id" jdbcType="VARCHAR" property="sellerId" />
    <result column="seller_name" jdbcType="VARCHAR" property="sellerName" />
    <result column="warehouse_id" jdbcType="VARCHAR" property="warehouseId" />
    <result column="storehouse_number" jdbcType="VARCHAR" property="storehouseNumber" />
    <result column="product_id" jdbcType="VARCHAR" property="productId" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="stock_direct" jdbcType="VARCHAR" property="stockDirect" />
    <result column="in_quantity" jdbcType="DECIMAL" property="inQuantity" />
    <result column="out_quantity" jdbcType="DECIMAL" property="outQuantity" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <select id="querySellerIdByTurnoverCond" parameterType="com.ecommerce.logistics.api.dto.storehouse.TurnoverQueryDTO" resultType="java.lang.String">
    select distinct log.seller_id
    from lgs_platform_stock_log log
    where
      log.del_flg = 0 and log.warehouse_id = #{warehouseId}
    <if test="sellerNameLike != null and sellerNameLike != ''">
      and log.seller_name like concat('%', #{sellerNameLike}, '%')
    </if>
    <if test="productNameLike != null and productNameLike != ''">
      and log.product_name like concat('%', #{productNameLike}, '%')
    </if>
    <if test="startDateStr != null and startDateStr != ''">
      and log.create_time &gt;= #{startDateStr}
    </if>
    <if test="endDateStr != null and endDateStr != ''">
      and log.create_time &lt;= concat(#{endDateStr}, ' 23:59:59')
    </if>
    order by log.seller_name

  </select>

  <select id="queryProductIdByTurnoverCond" parameterType="com.ecommerce.logistics.api.dto.storehouse.TurnoverQueryDTO" resultType="java.lang.String">
    select distinct log.product_id
    from lgs_platform_stock_log log
    where
    log.del_flg = 0 and log.warehouse_id = #{warehouseId}
    <if test="sellerId != null and sellerId != ''">
      and log.seller_id = #{sellerId}
    </if>
    <if test="sellerNameLike != null and sellerNameLike != ''">
      and log.seller_name like concat('%', #{sellerNameLike}, '%')
    </if>
    <if test="productNameLike != null and productNameLike != ''">
      and log.product_name like concat('%', #{productNameLike}, '%')
    </if>
    <if test="startDateStr != null and startDateStr != ''">
      and log.create_time &gt;= #{startDateStr}
    </if>
    <if test="endDateStr != null and endDateStr != ''">
      and log.create_time &lt;= concat(#{endDateStr}, ' 23:59:59')
    </if>
    order by log.product_name

  </select>

  <select id="queryLogByTurnoverCond" parameterType="com.ecommerce.logistics.dao.dto.storehouse.TurnoverCondBean" resultMap="BaseResultMap">
    select log.*
    from lgs_platform_stock_log log
    where
    log.del_flg = 0 and log.warehouse_id = #{warehouseId}
    <if test="sellerId != null and sellerId != ''">
      and log.seller_id = #{sellerId}
    </if>
    <if test="sellerIdList != null">
      and log.seller_id in
      <foreach collection="sellerIdList" item="si" open="(" separator="," close=")">
        #{si}
      </foreach>
    </if>
    <if test="productIdList != null">
      and log.product_id in
      <foreach collection="productIdList" item="pi" open="(" separator="," close=")">
        #{pi}
      </foreach>
    </if>
    <if test="startDateStr != null and startDateStr != ''">
      and log.create_time &gt;= #{startDateStr}
    </if>
    <if test="endDateStr != null and endDateStr != ''">
      and log.create_time &lt;= concat(#{endDateStr}, ' 23:59:59')
    </if>
    <if test="sellerNameLike != null and sellerNameLike != ''">
      and log.seller_name like concat('%', #{sellerNameLike}, '%')
    </if>
    <if test="productNameLike != null and productNameLike != ''">
      and log.product_name like concat('%', #{productNameLike}, '%')
    </if>
    order by log.create_time
  </select>

</mapper>