<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.PlatformStoreMapMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.PlatformStoreMap">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="store_map_id" jdbcType="VARCHAR" property="storeMapId" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="warehouse_id" jdbcType="VARCHAR" property="warehouseId" />
    <result column="storehouse_id" jdbcType="VARCHAR" property="storehouseId" />
    <result column="storehouse_number" jdbcType="VARCHAR" property="storehouseNumber" />
    <result column="product_id" jdbcType="VARCHAR" property="productId" />
    <result column="note" jdbcType="VARCHAR" property="note" />
    <result column="used_stock" jdbcType="DECIMAL" property="usedStock" />
    <result column="locked_stock" jdbcType="DECIMAL" property="lockedStock" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="version" jdbcType="BIGINT" property="version" />
  </resultMap>

  <resultMap id="detailListResultMap" type="com.ecommerce.logistics.api.dto.storehouse.PlatformStockListDTO">
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="total_used_stock" jdbcType="DECIMAL" property="totalUsedStock" />
    <result column="total_locked_stock" jdbcType="DECIMAL" property="totalLockedStock" />
    <collection property="productStockItemList" columnPrefix="d_"
                ofType="com.ecommerce.logistics.api.dto.storehouse.ProductStockItem">
      <result column="product_id" jdbcType="VARCHAR" property="productId" />
      <result column="note" jdbcType="VARCHAR" property="note" />
      <result column="used_stock" jdbcType="DECIMAL" property="usedStock" />
      <result column="locked_stock" jdbcType="DECIMAL" property="lockedStock" />
    </collection>
  </resultMap>

  <update id="changeStock" parameterType="com.ecommerce.logistics.dao.dto.storehouse.PlatformStoreChangeBean">
    update lgs_platform_store_map m
    left join lgs_storehouse s on m.storehouse_id = s.storehouse_id
    set m.update_user = #{updateUser}
    <if test="usedStock != null">
      , m.used_stock = ifnull(m.used_stock, 0) + #{usedStock}
    </if>
    <if test="lockedStock != null">
      , m.locked_stock = ifnull(m.locked_stock, 0) + #{lockedStock}
    </if>
    where m.del_flg = 0 and s.del_flg = 0
    and s.warehouse_id = #{warehouseId} and s.storehouse_number = #{storehouseNumber}
    and m.warehouse_id = #{warehouseId} and m.user_id = #{userId} and m.product_id = #{productId}
    <if test="usedStock != null">
      and ifnull(m.used_stock, 0) + #{usedStock} &gt;= 0
    </if>
    <if test="lockedStock != null">
      and ifnull(m.locked_stock, 0) + #{lockedStock} &gt;= 0
    </if>
  </update>

  <update id="changeStockWithoutCheck" parameterType="com.ecommerce.logistics.dao.dto.storehouse.PlatformStoreChangeBean">
    update lgs_platform_store_map m
    left join lgs_storehouse s on m.storehouse_id = s.storehouse_id
    set m.update_user = #{updateUser}
    <if test="usedStock != null">
      , m.used_stock = ifnull(m.used_stock, 0) + #{usedStock}
    </if>
    <if test="lockedStock != null">
      , m.locked_stock = ifnull(m.locked_stock, 0) + #{lockedStock}
    </if>
    where m.del_flg = 0 and s.del_flg = 0
    and s.warehouse_id = #{warehouseId} and s.storehouse_number = #{storehouseNumber}
    and m.warehouse_id = #{warehouseId} and m.user_id = #{userId} and m.product_id = #{productId}
  </update>

  <select id="checkForLeaveCenterWarehouse" parameterType="com.ecommerce.logistics.dao.dto.storehouse.PlatformStoreChangeBean" resultType="java.lang.Integer">
    select count(distinct m.product_id)
    from lgs_platform_store_map m
    left join lgs_storehouse s on m.storehouse_id = s.storehouse_id
    where m.del_flg = 0 and s.del_flg = 0
    and s.warehouse_id = #{warehouseId} and s.storehouse_number = #{storehouseNumber}
    and m.warehouse_id = #{warehouseId} and m.user_id = #{userId} and m.product_id = #{productId}
    <if test="usedStock != null">
      and ifnull(m.used_stock, 0) &gt;= #{usedStock}
    </if>
  </select>

  <select id="selectSellerPlatformStock" resultMap="detailListResultMap">
    select
        m.user_id,
        m.user_name,
        m.product_id as d_product_id,
        m.note as d_note,
        m.used_stock as d_used_stock,
        m.locked_stock as d_locked_stock
    from lgs_platform_store_map m
    where m.del_flg = 0
    and m.user_id = #{userId}
    <if test="noteLike != null and noteLike != ''">
      and m.note like concat('%', #{noteLike}, '%')
    </if>
    <if test="warehouseId != null and warehouseId != ''">
      and m.warehouse_id = #{warehouseId}
    </if>
  </select>

  <select id="selectPlatformStockSellerInfo" resultMap="detailListResultMap">
    select distinct
      m.user_id,
      m.user_name
    from lgs_platform_store_map m
    where m.del_flg = 0
    <if test="noteLike != null and noteLike != ''">
      and m.note like concat('%', #{noteLike}, '%')
    </if>
    <if test="warehouseId != null and warehouseId != ''">
      and m.warehouse_id = #{warehouseId}
    </if>
    order by m.user_name asc
  </select>

  <select id="selectProductStockList" resultMap="detailListResultMap">
    select
      m.user_id,
      m.product_id as d_product_id,
      m.note as d_note,
      m.used_stock as d_used_stock,
      m.locked_stock as d_locked_stock
    from lgs_platform_store_map m
    where m.del_flg = 0
      <if test="userIdList != null">
        and m.user_id in
        <foreach collection="userIdList" index="uin" item="uit" open="(" separator="," close=")">
          #{uit}
        </foreach>
      </if>
    <if test="noteLike != null and noteLike != ''">
      and m.note like concat('%', #{noteLike}, '%')
    </if>
    <if test="warehouseId != null and warehouseId != ''">
      and m.warehouse_id = #{warehouseId}
    </if>
  </select>



</mapper>