<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.PorterMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.Porter">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="porter_id" jdbcType="VARCHAR" property="porterId" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="user_type" jdbcType="VARCHAR" property="userType" />
    <result column="province_name" jdbcType="VARCHAR" property="provinceName" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="district_name" jdbcType="VARCHAR" property="districtName" />
    <result column="district_code" jdbcType="VARCHAR" property="districtCode" />
    <result column="porter_name" jdbcType="VARCHAR" property="porterName" />
    <result column="porter_phone" jdbcType="VARCHAR" property="porterPhone" />
    <result column="age" jdbcType="VARCHAR" property="age" />
    <result column="sex" jdbcType="INTEGER" property="sex" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <select id="queryPorterList" parameterType="com.ecommerce.logistics.api.dto.intelligentDispatch.PorterQueryDTO"
          resultType="com.ecommerce.logistics.api.dto.intelligentDispatch.PorterListDTO">
    select
    porter_id as porterId,
    province_name as provinceName,
    province_code as provinceCode,
    city_name as cityName,
    city_code as cityCode,
    district_name as districtName,
    district_code as districtCode,
    porter_name as porterName,
    porter_phone as porterPhone,
    age as age,
    sex as sex,
    content as content,
    user_id as userId
    from lgs_porter
    where del_flg = 0
    <if test="provinceCode != null and provinceCode != ''">
      and province_code = #{provinceCode}
    </if>
    <if test="cityCode != null and cityCode != ''">
      and city_code = #{cityCode}
    </if>
    <if test="districtCode != null and districtCode != ''">
      and district_code = #{districtCode}
    </if>
    <if test="content != null and content != ''">
      and content like CONCAT('%', #{content},'%')
    </if>
    order by update_time desc
  </select>
</mapper>