<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.PorterageRuleItemMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.PorterageRuleItem">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="porterage_rule_item_id" jdbcType="VARCHAR" property="porterageRuleItemId" />
    <result column="porterage_rule_id" jdbcType="VARCHAR" property="porterageRuleId" />
    <result column="metering_unit" jdbcType="VARCHAR" property="meteringUnit" />
    <result column="metering_num" jdbcType="DECIMAL" property="meteringNum" />
    <result column="transport_unit" jdbcType="VARCHAR" property="transportUnit" />
    <result column="transport_num" jdbcType="DECIMAL" property="transportNum" />
    <result column="porterage_price" jdbcType="DECIMAL" property="porteragePrice" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <select id="queryZoneRuleItemList" parameterType="com.ecommerce.logistics.dao.dto.porterage.ZoneQueryDO" resultMap="BaseResultMap">
    SELECT
      *
    FROM
      lgs_porterage_rule_item as t1
    <where>
      t1.porterage_rule_id = #{porterageRuleId}
      <if test="userIdList != null and userIdList.size &gt; 0">
        AND t1.user_id IN
        <foreach close=")" collection="userIdList" index="index" item="userId" open="(" separator=",">
          #{userId}
        </foreach>
      </if>
      <choose>
        <when test="districtCodeList == null">
          AND t1.district_code = ''
        </when>
        <when test="districtCodeList.size &gt; 0">
          AND t1.district_code IN
          <foreach close=")" collection="districtCodeList" index="index" item="districtCode" open="(" separator=",">
            #{districtCode}
          </foreach>
        </when>
      </choose>
      <choose>
        <when test="cityCodeList == null">
          AND t1.city_code = ''
        </when>
        <when test="cityCodeList.size &gt; 0">
          AND t1.city_code IN
          <foreach close=")" collection="cityCodeList" index="index" item="cityCode" open="(" separator=",">
            #{cityCode}
          </foreach>
        </when>
      </choose>
      <choose>
        <when test="provinceCodeList == null">
          AND t1.province_code = ''
        </when>
        <when test="provinceCodeList.size &gt; 0">
          AND t1.province_code IN
          <foreach close=")" collection="provinceCodeList" index="index" item="provinceCode" open="(" separator=",">
            #{provinceCode}
          </foreach>
        </when>
      </choose>
    </where>
  </select>
  <select id="queryComputeRuleItemList" parameterType="com.ecommerce.logistics.dao.dto.porterage.ComputeQueryDO" resultMap="BaseResultMap">
    SELECT
      *
    FROM
      lgs_porterage_rule_item as t1
    <where>
      t1.porterage_rule_id = #{porterageRuleId}
      <if test="userIdList != null and userIdList.size &gt; 0">
        AND t1.user_id IN
        <foreach close=")" collection="userIdList" index="index" item="userId" open="(" separator=",">
          #{userId}
        </foreach>
      </if>
      <if test="districtCode != null">
        AND t1.district_code = #{districtCode}
      </if>
      <if test="cityCode != null">
        AND t1.city_code = #{cityCode}
      </if>
      <if test="provinceCode != null">
        AND t1.province_code = #{provinceCode}
      </if>
    </where>
  </select>
</mapper>