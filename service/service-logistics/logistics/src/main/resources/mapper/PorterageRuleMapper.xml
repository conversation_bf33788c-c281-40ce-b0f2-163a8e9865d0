<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.PorterageRuleMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.PorterageRule">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="porterage_rule_id" jdbcType="VARCHAR" property="porterageRuleId" />
    <result column="rule_name" jdbcType="VARCHAR" property="ruleName" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="user_type" jdbcType="TINYINT" property="userType" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="district" jdbcType="VARCHAR" property="district" />
    <result column="district_code" jdbcType="VARCHAR" property="districtCode" />
    <result column="transport_category_id" jdbcType="VARCHAR" property="transportCategoryId" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <select id="queryPorterageRuleList" parameterType="com.ecommerce.logistics.api.dto.porterage.PorterageListQueryDTO" resultType="com.ecommerce.logistics.api.dto.porterage.PorterageListDTO">
    SELECT
      t1.porterage_rule_id as porterageRuleId,
      t1.rule_name as ruleName,
      t1.metering_unit as meteringUnit,
      t1.metering_min as meteringMin,
      t1.metering_max as meteringMax,
      t1.transport_unit as transportUnit,
      t1.transport_min as transportMin,
      t1.transport_max as transportMax,
      t1.create_time as createTime
    FROM
      lgs_porterage_rule as t1
    <where>
      t1.del_flg = 0
      <if test="ruleName != null and ruleName != ''">
        <![CDATA[AND t1.rule_name LIKE CONCAT('%', #{ruleName}, '%')]]>
      </if>
      <if test="createTimeStart != null and createTimeStart != ''">
        <![CDATA[AND t1.create_time >= #{createTimeStart}]]>
      </if>
      <if test="createTimeEnd != null and createTimeEnd != ''">
        <![CDATA[AND t1.create_time <= #{createTimeEnd}]]>
      </if>
    </where>
    ORDER BY
      t1.create_time DESC
  </select>
  <select id="searchPorterageRuleList" parameterType="com.ecommerce.logistics.api.dto.porterage.PorterageSearchDTO" resultType="com.ecommerce.logistics.api.dto.porterage.PorterageSearchResultDTO">
    SELECT
      t1.porterage_rule_id as porterageRuleId,
      t1.rule_name as ruleName,
      t1.metering_unit as meteringUnit,
      t1.transport_unit as transportUnit
    FROM
      lgs_porterage_rule as t1
    <where>
      t1.del_flg = 0
      <if test="ruleName != null and ruleName != ''">
        AND t1.rule_name LIKE CONCAT('%', #{ruleName}, '%')
      </if>
    </where>
  </select>
  <select id="selectZoneRuleList" parameterType="com.ecommerce.logistics.dao.dto.porterage.ZoneRuleQueryDO"
          resultType="com.ecommerce.logistics.dao.vo.PorterageRule">
    SELECT
      t1.*
    FROM
      lgs_porterage_rule as t1
    WHERE
      t1.del_flg=0
    <if test="userIdList != null and userIdList.size() > 0">
      AND t1.user_id IN
      <foreach close=")" collection="userIdList" index="index" item="userId" open="(" separator=",">
        #{userId}
      </foreach>
    </if>
    <if test="transportCategoryIdList != null and transportCategoryIdList.size() > 0">
      AND t1.transport_category_id IN
      <foreach close=")" collection="transportCategoryIdList" index="index" item="transportCategoryId" open="(" separator=",">
        #{transportCategoryId}
      </foreach>
    </if>
      AND concat(#{provinceCode}, #{cityCode}, #{districtCode}, #{streetCode}) LIKE concat(t1.province_code, t1.city_code, t1.district_code, t1.street_code, '%')
  </select>
</mapper>