<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.ProductInfoMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.ProductInfo">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="product_info_id" jdbcType="VARCHAR" property="productInfoId" />
    <result column="transport_category_id" jdbcType="VARCHAR" property="transportCategoryId" />
    <result column="product_id" jdbcType="VARCHAR" property="productId" />
    <result column="commodity_code" jdbcType="VARCHAR" property="commodityCode" />
    <result column="qr_code" jdbcType="VARCHAR" property="qrCode" />
    <result column="vendor" jdbcType="VARCHAR" property="vendor" />
    <result column="product_img" jdbcType="VARCHAR" property="productImg" />
    <result column="goods_id" jdbcType="VARCHAR" property="goodsId" />
    <result column="note" jdbcType="VARCHAR" property="note" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="carriage_unit_price" jdbcType="DECIMAL" property="carriageUnitPrice" />
    <result column="special_flag" jdbcType="TINYINT" property="specialFlag" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="version" jdbcType="BIGINT" property="version" />
    <result column="sign_type" jdbcType="VARCHAR" property="signType" />
    <result column="carry_flag" jdbcType="TINYINT" property="carryFlag" />
    <result column="unloading_flow_code" jdbcType="VARCHAR" property="unloadingFlowCode" />
  </resultMap>

  <select id="selectMapIdsByProductDescAndMapType" parameterType="java.util.Map" resultType="java.lang.String">
  	SELECT
  		m.map_id
  	FROM
  		lgs_product_quantity_map m
  	JOIN
  		lgs_product_info p on p.product_info_id = m.product_info_id
  	WHERE
  		p.desc link #{desc}% AND p.del_flg = 0 AND m.map_type = #{mapType} AND m.del_flg = 0
  </select>
  
  <select id="selectProductDescByPK" parameterType="java.lang.String" resultType="java.lang.String">
  	SELECT note FROM lgs_product_info WHERE product_info_id = #{productInfoId}
  </select>
  
  <select id="selectProductUnitByPK" parameterType="java.lang.String" resultType="java.lang.String">
  	SELECT unit FROM lgs_product_info WHERE product_info_id = #{productInfoId}
  </select>
  
  <select id="selectInfoLikeNote" parameterType="java.lang.String" resultType="com.ecommerce.logistics.api.dto.productinfo.ProductInfoDTO">
  	SELECT 
  		product_info_id as productInfoId,
  		note as note
  	FROM 
  		lgs_product_info
  	WHERE
  		note LIKE concat('%', #{note}, '%') AND del_flg = 0
  </select>
  
  <select id="selectNoteAndUnitByPK" parameterType="java.lang.String" resultMap="BaseResultMap">
  	SELECT note,unit FROM lgs_product_info WHERE product_info_id = #{productInfoId}
  </select>

  <select id="selectProductByWaybillId" resultMap="BaseResultMap">
      select
            lpi.*
      from
           lgs_product_info lpi, lgs_product_quantity_map lpqm
      where
            lpi.product_info_id = lpqm.product_info_id and lpqm.map_id = #{waybillId} and lpqm.map_type = '3'
            and lpi.del_flg = 0 and lpqm.del_flg = 0
  </select>

  <select id="queryOptionsByKeyWord" resultType="com.ecommerce.logistics.api.dto.pickingbill.OptionDTO">
    select distinct goods_id as `value`, note as label
    from lgs_product_info
    where del_flg = 0 and goods_id != ''
    <if test="keyWord != null and keyWord != ''">
      and note like concat('%', #{keyWord}, '%')
    </if>
    order by create_time
  </select>
  
</mapper>