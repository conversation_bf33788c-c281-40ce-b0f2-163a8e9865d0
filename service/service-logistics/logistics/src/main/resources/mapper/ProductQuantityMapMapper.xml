<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.ProductQuantityMapMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.ProductQuantityMap">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="quantity_map_id" jdbcType="VARCHAR" property="quantityMapId" />
    <result column="map_id" jdbcType="VARCHAR" property="mapId" />
    <result column="map_type" jdbcType="TINYINT" property="mapType" />
    <result column="product_info_id" jdbcType="VARCHAR" property="productInfoId" />
    <result column="quantity" jdbcType="DECIMAL" property="quantity" />
    <result column="delivery_quantity" jdbcType="DECIMAL" property="deliveryQuantity" />
  	<result column="shipping_quantity" jdbcType="DECIMAL" property="shippingQuantity" />
    <result column="complete_quantity" jdbcType="DECIMAL" property="completeQuantity" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="version" jdbcType="BIGINT" property="version" />
  </resultMap>

	<select id="queryProductQuantityForUpdate" resultType="java.lang.String">
		SELECT
			quantity_map_id
		FROM
			lgs_product_quantity_map
		WHERE
			map_id = #{mapId} FOR UPDATE;
	</select>

  <update id="reduceDeliveryQuantityByMapId" parameterType="java.util.Map">
  	UPDATE
  		lgs_product_quantity_map
  	SET
  		delivery_quantity = delivery_quantity - #{deliveryQuantity},
  		update_user = #{operatorId},
  		update_time = CURRENT_TIMESTAMP()
  	WHERE
  		map_id = #{mapId}
  </update>
  
  <update id="increaseDeliveryQuantityByMapId" parameterType="java.util.Map">
  	UPDATE
  		lgs_product_quantity_map
  	SET
  		delivery_quantity = delivery_quantity+#{deliveryQuantity},
  		update_user = #{operatorId},
  		update_time = CURRENT_TIMESTAMP()
  	WHERE
  		map_id = #{mapId}
  </update>
  
  <update id="increaseCompleteQuantityByMapId" parameterType="java.util.Map">
  	UPDATE
  		lgs_product_quantity_map
  	SET
  		complete_quantity = complete_quantity+#{completeQuantity},
  		update_user = #{operatorId},
  		update_time = CURRENT_TIMESTAMP()
  	WHERE
  		map_id = #{mapId}
  </update>

	<update id="increaseShippingQuantityByMapId" parameterType="java.util.Map">
		UPDATE
			lgs_product_quantity_map
		SET
			shipping_quantity = shipping_quantity + #{shippingQuantity},
			update_user = #{operatorId},
			update_time = CURRENT_TIMESTAMP()
		WHERE
			map_id = #{mapId}
	</update>
  
  <update id="updateDelFlgByMapIds" parameterType="java.util.Map">
  	UPDATE
  		lgs_product_quantity_map
  	SET
		del_flg = 1,
		update_user = #{operatorId},
		update_time = CURRENT_TIMESTAMP()
	WHERE
	 	map_id in 
	 	<foreach close=")" collection="mapIds" index="index" item="id" open="(" separator=",">
	 		#{id}
	 	</foreach>
  </update>
  
  <select id="selectPartQuantityInfoByMapId" parameterType="java.lang.String" resultMap="BaseResultMap">
  	SELECT quantity_map_id,quantity,delivery_quantity,product_info_id,complete_quantity FROM lgs_product_quantity_map WHERE map_id = #{mapId} AND del_flg=0
  </select>
  
  <select id="selectProductInfoByMapId" parameterType="java.lang.String" resultType="com.ecommerce.logistics.dao.dto.pickingbill.ProductQuantityDO">
  	SELECT 
  		m.quantity as quantity,
  		i.note as note,
  		i.unit as unit,
  		i.special_flag as specialFlag,
  	  i.sign_type as signType,
  	  i.transport_category_id as transportCategoryId
  	FROM
  		lgs_product_quantity_map m
  	LEFT JOIN
  		lgs_product_info i on i.product_info_id = m.product_info_id
  	WHERE m.map_id = #{mapId}
  </select>
  
  <select id="selectTotalQuantityByMapIds" parameterType="java.util.List" resultType="java.math.BigDecimal">
  	SELECT
  		SUM(quantity)
  	FROM
  		lgs_product_quantity_map
  	WHERE
  		map_id in 
  		<foreach collection="mapIds" close=")" index="index" item="mapId" open="(" separator=",">
  			#{mapId}
  		</foreach>
  </select>

	<select id="selectMapByParentWaybillId" resultMap="BaseResultMap">
		SELECT
			lpqm.*
		FROM
			lgs_product_quantity_map lpqm, lgs_waybill lw
		WHERE lw.del_flg = 0 AND lpqm.del_flg = 0
			  AND lw.parent_id = #{parentWaybillId} AND lpqm.map_id = lw.waybill_id
	</select>
  
  <select id="selectTotalQuantityByMainWaybillId" parameterType="java.lang.String" resultType="java.math.BigDecimal">
  	select 
		sum(t1.quantity)
	from 
		lgs_product_quantity_map t1
	JOIN
		lgs_waybill t2 on t1.map_id = t2.waybill_id
	where
		t2.parent_id = #{waybillId}
  </select>

	<update id="revertQuantity">
		update lgs_product_quantity_map
		set delivery_quantity = delivery_quantity - #{deltaQuantity},
		update_user = #{updateUser},
		update_time = current_timestamp
		where del_flg = 0 and map_id = #{mapId} and product_info_id = #{productInfoId}
	</update>

	<select id="selectByMapId" resultType="com.ecommerce.logistics.api.dto.pickingbill.ProductDetailDTO">
		select
			i.product_id as productId,
			i.transport_category_id as transportCategoryId,
			i.vendor as vendor,
			i.goods_id as goodsId,
			i.note as note,
			i.unit as unit,
			m.quantity as quantity,
			i.special_flag as specialFlag,
			i.price as price,
			i.product_img as productImg,
			i.commodity_code as commodityCode,
			i.sign_type as signType,
			i.carry_flag as carryFlag,
			i.unloading_flow_code as unloadingFlowCode
		from lgs_product_quantity_map m
		left join lgs_product_info i on m.product_info_id = i.product_info_id
		where m.map_id = #{mapId} and m.map_type = #{mapType} and m.del_flg = 0 and i.del_flg = 0
	</select>
  
</mapper>