<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.ProxySyncRecordMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.ProxySyncRecord">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="proxy_sync_id" jdbcType="VARCHAR" property="proxySyncId" />
    <result column="bill_type" jdbcType="VARCHAR" property="billType" />
    <result column="operate_bill_id" jdbcType="VARCHAR" property="operateBillId" />
    <result column="relation_bill_id" jdbcType="VARCHAR" property="relationBillId" />
    <result column="sync_type" jdbcType="VARCHAR" property="syncType" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="sync_desc" jdbcType="VARCHAR" property="syncDesc" />
    <result column="try_count" jdbcType="INTEGER" property="tryCount" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="version" jdbcType="BIGINT" property="version" />
    <result column="param_text" jdbcType="LONGVARCHAR" property="paramText" />
  </resultMap>

  <update id="updateStatusById">
    update lgs_proxy_sync_record
    set status = #{syncStatus},
    try_count = ifnull(try_count, 0) + 1,
    sync_desc = #{syncDesc}
    where proxy_sync_id = #{proxySyncId}
  </update>

  <select id="queryProxySyncRecordList" parameterType="com.ecommerce.logistics.api.dto.ProxySyncRecord.ProxySyncRecordQueryDTO" resultType="com.ecommerce.logistics.api.dto.ProxySyncRecord.ProxySyncRecordListDTO">
    SELECT
    p.proxy_sync_id as proxySyncId,
    p.bill_type as billType,
    p.operate_bill_id as operateBillId,
    w.waybill_num as operateBillNum,
    p.relation_bill_id as relationBillId,
    p.sync_type as syncType,
    p.status as status,
    p.sync_desc as syncDesc,
    p.try_count as tryCount
    FROM
    lgs_proxy_sync_record as p
    LEFT JOIN
    lgs_ship_bill as w on w.waybill_id = p.operate_bill_id
    WHERE p.del_flg = 0
    <if test="operateBillId != null and operateBillId != ''">
      AND p.operate_bill_id = #{operateBillId}
    </if>
    <if test="operateBillNum != null and operateBillNum != ''">
      AND w.waybill_num = #{operateBillNum}
    </if>
    <if test="relationBillId != null and relationBillId != ''">
      AND p.relation_bill_id = #{relationBillId}
    </if>
    <if test="syncType != null and syncType != ''">
      AND p.sync_type = #{syncType}
    </if>
    <if test="status != null and status != ''">
      AND p.status = #{status}
    </if>
    order by p.create_time desc
  </select>
</mapper>