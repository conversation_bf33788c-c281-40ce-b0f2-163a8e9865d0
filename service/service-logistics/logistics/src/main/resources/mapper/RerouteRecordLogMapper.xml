<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.RerouteRecordLogMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.RerouteRecordLog">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="reroute_record_id" jdbcType="VARCHAR" property="rerouteRecordId" />
    <result column="waybill_id" jdbcType="VARCHAR" property="waybillId" />
    <result column="waybill_num" jdbcType="VARCHAR" property="waybillNum" />
    <result column="curr_receive_member_id" jdbcType="VARCHAR" property="currCeceiveMemberId" />
    <result column="curr_receive_member_name" jdbcType="VARCHAR" property="currCeceiveMemberName" />
    <result column="curr_delivery_bill_id" jdbcType="VARCHAR" property="currDeliveryBillId" />
    <result column="curr_delivery_bill_num" jdbcType="VARCHAR" property="currDeliveryBillNum" />
    <result column="curr_unload_port_id" jdbcType="VARCHAR" property="currUnloadPortId" />
    <result column="curr_unload_port_name" jdbcType="VARCHAR" property="currUnloadPortName" />
    <result column="original_receive_member_id" jdbcType="VARCHAR" property="originalCeceiveMemberId" />
    <result column="original_receive_member_name" jdbcType="VARCHAR" property="originalCeceiveMemberName" />
    <result column="original_delivery_bill_id" jdbcType="VARCHAR" property="originalDeliveryBillId" />
    <result column="original_delivery_bill_num" jdbcType="VARCHAR" property="originalDeliveryBillNum" />
    <result column="original_unload_port_id" jdbcType="VARCHAR" property="originalUnloadPortId" />
    <result column="original_unload_port_name" jdbcType="VARCHAR" property="originalUnloadPortName" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  
  <select id="queryRerouteLogList" resultType="com.ecommerce.logistics.api.dto.waybill.RerouteRecordLogDTO"
          parameterType="com.ecommerce.logistics.api.dto.waybill.RerouteRecordLogDTO">
    SELECT
      reroute_record_id AS rerouteRecordId,
      waybill_id AS waybillId,
      waybill_num AS waybillNum,
      curr_receive_member_id AS currReceiveMemberId,
      curr_receive_member_name AS currReceiveMemberName,
      curr_delivery_bill_id AS currDeliveryBillId,
      curr_delivery_bill_num AS currDeliveryBillNum,
      curr_unload_port_id AS currUnloadPortId,
      curr_unload_port_name AS currUnloadPortName,
      original_receive_member_id AS originalReceiveMemberId,
      original_receive_member_name AS originalReceiveMemberName,
      original_delivery_bill_id AS originalDeliveryBillId,
      original_delivery_bill_num AS originalDeliveryBillNum,
      original_unload_port_id AS originalUnloadPortId,
      original_unload_port_name AS originalUnloadPortName,
      create_user_id AS createUserId,
      create_user_name AS createUserName,
      create_time AS createTime
    FROM lgs_reroute_record_log
    WHERE 1=1
    <if test="currDeliveryBillNum != null and currDeliveryBillNum != '' ">
      AND curr_delivery_bill_num = #{currDeliveryBillNum}
    </if>
    <if test="originalDeliveryBillNum != null and originalDeliveryBillNum != '' ">
      AND original_delivery_bill_num = #{originalDeliveryBillNum}
    </if>
    <if test="waybillNum != null and waybillNum != '' ">
      AND waybill_num = #{waybillNum}
    </if>
    ORDER BY create_time DESC
  </select>

</mapper>