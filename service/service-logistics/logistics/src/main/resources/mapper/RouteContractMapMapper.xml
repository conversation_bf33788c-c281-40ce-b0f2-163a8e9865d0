<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.RouteContractMapMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.RouteContractMap">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="carriage_route_id" jdbcType="VARCHAR" property="carriageRouteId" />
    <result column="contract_address_code" jdbcType="VARCHAR" property="contractAddressCode" />
    <result column="contract_address_detail" jdbcType="VARCHAR" property="contractAddressDetail" />
    <result column="contract_address_name" jdbcType="VARCHAR" property="contractAddressName" />
    <result column="del_flg" jdbcType="INTEGER" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>