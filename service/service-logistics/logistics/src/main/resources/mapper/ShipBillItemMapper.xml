<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.ShipBillItemMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.ShipBillItem">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="waybill_item_id" jdbcType="VARCHAR" property="waybillItemId" />
    <result column="waybill_id" jdbcType="VARCHAR" property="waybillId" />
    <result column="waybill_num" jdbcType="VARCHAR" property="waybillNum" />
    <result column="delivery_info_id" jdbcType="VARCHAR" property="deliveryInfoId" />
    <result column="delivery_bill_id" jdbcType="VARCHAR" property="deliveryBillId" />
    <result column="real_delivery_bill_num" jdbcType="VARCHAR" property="realDeliveryBillNum" />
    <result column="seq_num" jdbcType="INTEGER" property="seqNum" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="pay_status" jdbcType="VARCHAR" property="payStatus" />
    <result column="bill_proxy_type" jdbcType="VARCHAR" property="billProxyType" />
    <result column="can_operate" jdbcType="TINYINT" property="canOperate" />
    <result column="calc_flag" jdbcType="TINYINT" property="calcFlag" />
    <result column="complete_quantity" jdbcType="DECIMAL" property="completeQuantity" />
    <result column="complete_time" jdbcType="TIMESTAMP" property="completeTime" />
    <result column="pay_quantity" jdbcType="DECIMAL" property="payQuantity" />
    <result column="goods_price" jdbcType="DECIMAL" property="goodsPrice" />
    <result column="goods_amount" jdbcType="DECIMAL" property="settlementAmount" />
    <result column="receivable_carriage_price" jdbcType="DECIMAL" property="receivableCarriagePrice" />
    <result column="receivable_carriage_amount" jdbcType="DECIMAL" property="receivableCarriageAmount" />
    <result column="origin_price" jdbcType="DECIMAL" property="originPrice" />
    <result column="adjust_num" jdbcType="DECIMAL" property="adjustNum" />
    <result column="adjust_status" jdbcType="DECIMAL" property="adjustStatus" />
    <result column="empty_load_flag" jdbcType="TINYINT" property="emptyLoadFlag" />
    <result column="empty_load_charge" jdbcType="DECIMAL" property="emptyLoadCharge" />
    <result column="empty_load_quantity" jdbcType="DECIMAL" property="emptyLoadQuantity" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="version" jdbcType="BIGINT" property="version" />
  </resultMap>

  <select id="selectByWaybillIdAndSeq" resultMap="BaseResultMap">
    select *
    from lgs_ship_bill_item
    where waybill_id = #{waybillId} and seq_num = #{seqNum} and del_flg = 0
    order by bill_proxy_type
  </select>

  <update id="updateWaybillItemStatus">
    update lgs_ship_bill_item
    set status = #{newStatus}, update_user = #{updateUser}, update_time = current_timestamp
    where waybill_item_id = #{waybillItemId}
      and status in
        <foreach collection="oldStatusList" index="osin" item="osit" open="(" separator="," close=")">
          #{osit}
        </foreach>
      and del_flg = 0
  </update>

  <update id="updateWaybillItemQuantity">
    update lgs_ship_bill_item
    set pay_quantity = #{payQuantity},
        goods_price = #{goodsPrice},
        origin_price = #{goodsPrice},
        settlement_amount = #{settlementAmount},
        update_user = #{updateUser},
        update_time = current_timestamp
    where waybill_item_id = #{waybillItemId}
      and del_flg = 0
  </update>

  <select id="selectItemIdByWaybillProxy" resultType="java.lang.String">
    select waybill_item_id
    from lgs_ship_bill_item
    where waybill_id = #{waybillId} and seq_num = #{seqNum} and bill_proxy_type = #{billProxyType} and del_flg = 0
  </select>

  <update id="deleteItemByWaybillAndSeq">
    update lgs_ship_bill_item
    set del_flg = 1,
        update_user = #{updateUser}
    where waybill_id = #{waybillId} and seq_num = #{seqNum} and del_flg = 0
  </update>

  <select id="selectWaitRerouteShipBillList" resultType="com.ecommerce.logistics.api.dto.ShipBillWaitRerouteListDTO">
    select
      item.waybill_id as waybillId,
      item.waybill_item_id as waybillItemId,
      item.delivery_bill_id as deliveryBillId,
      item.waybill_num as waybillNum,
      info.buyer_id as buyerId,
      info.buyer_name as buyerName,
      info.seller_id as sellerId,
      info.seller_name as sellerName,
      info.goods_id as goodsId,
      info.note as goodsName,
      info.unload_port_id as unloadPortId,
      info.unload_port_name as unloadPortName,
      bill.carrier_name as carrierName,
      bill.shipping_name as shippingName,
      bill.driver_name as driverName,
      bill.driver_phone as driverPhone,
      bill.warehouse_id as warehouseId,
      bill.warehouse_name as warehouseName,
      bill.warehouse_type as warehouseType,
      bill.actual_quantity as actualQuantity,
      ldb.delivery_bill_num as deliveryBillNum
    from lgs_ship_bill_item as item
    left join lgs_ship_bill as bill on item.waybill_id = bill.waybill_id
    left join lgs_delivery_info as info on item.delivery_info_id = info.delivery_info_id
    LEFT JOIN lgs_delivery_bill AS ldb ON item.delivery_bill_id = ldb.delivery_bill_id
    where info.seller_id = #{sellerId}
      and info.goods_id = #{goodsId}
      and info.transport_tool_type = #{transportToolType}
      and info.type = #{pickingBillType}
      and info.bill_proxy_type in
      <foreach collection="billProxyTypeList" item="bit" index="bin" open="(" separator="," close=")">
        #{bit}
      </foreach>
      and bill.status = #{shipBillStatus}
      and item.status in ('030610200', '030610300', '030610500')
      and item.del_flg = 0
      and info.unload_port_id != #{excludeUnloadPortId}
      order by item.waybill_num
  </select>

  <select id="countByRealDeliveryNumAndStatusList" resultType="java.lang.Integer">
    select count(waybill_item_id)
    from lgs_ship_bill_item
    where real_delivery_bill_num = #{realDeliveryBillNum}
    and status in
    <foreach collection="itemStatusList" item="sit" index="sin" open="(" separator="," close=")">
      #{sit}
    </foreach>
    and del_flg = 0
  </select>

  <select id="selectErpShipBillItem" resultMap="BaseResultMap">
        select
        *
        from lgs_ship_bill_item
        where waybill_id = #{waybillId}
        and del_flg = 0
        and bill_proxy_type != '030400300'
  </select>

  <select id="selectShipBillItemByWaybillId" resultMap="BaseResultMap">
        select
        *
        from lgs_ship_bill_item
        where waybill_id = #{waybillId}
        and del_flg = 0
  </select>

  <update id="closeItemByWaybillId">
    update lgs_ship_bill_item
    set update_user = #{updateUser},
        update_time = current_timestamp,
        status = #{newStatus}
    where waybill_id = #{waybillId}
      and seq_num = #{seqNum}
      and status = #{oldStatus}
      and del_flg = 0
  </update>

  <select id="selectCurrentItemForLeaveWarehouse" resultType="java.lang.String">
    select item.waybill_item_id
    from lgs_ship_bill as lsb
    left join lgs_ship_bill_item as item on lsb.waybill_id = item.waybill_id and lsb.max_seq_num = item.seq_num
    where lsb.del_flg = 0 and item.del_flg = 0
      and lsb.waybill_id = #{waybillId}
      and lsb.status in ('030600600','030600700')
      and item.status in ('030610100', '030610300')
    order by item.bill_proxy_type
    limit 1
  </select>

  <select id="queryShipBillSMSByItemId" resultType="com.ecommerce.logistics.biz.message.dto.ShipBillSMSDTO">
    select
    item.waybill_id as waybillId,
    item.waybill_num as waybillNum,
    di.buyer_id as buyerId,
    di.buyer_name as buyerName,
    di.seller_id as sellerId,
    di.seller_name as sellerName,
    di.note as note,
    bill.shipping_name as shippingName,
    bill.actual_quantity as quantity,
    bill.driver_phone as captainPhone,
    bill.unload_port_name as unloadPortName
    from lgs_ship_bill_item item
    left join lgs_ship_bill bill on bill.waybill_id = item.waybill_id
    left join lgs_delivery_info di on di.delivery_info_id = item.delivery_info_id
    where item.del_flg = 0 and bill.del_flg = 0 and di.del_flg = 0
      and item.waybill_item_id = #{waybillItemId}
  </select>

  <select id="selectItemsByWaybillIdList" resultMap="BaseResultMap">
    select item.*
    from lgs_ship_bill as bill
    left join lgs_ship_bill_item as item on bill.waybill_id = item.waybill_id and bill.max_seq_num = item.seq_num
    where bill.waybill_id in
          <foreach collection="waybillIdList" item="wit" index="win" open="(" separator="," close=")">
            #{wit}
          </foreach>
      and bill.del_flg = 0 and item.del_flg = 0
    order by bill.waybill_num, item.bill_proxy_type
  </select>

    <select id="queryBillCheckWaybillInfoList" parameterType="com.ecommerce.logistics.api.dto.shipbill.CheckWaybillQueryDTO" resultType="com.ecommerce.logistics.api.dto.shipbill.CheckWaybillInfoDTO">
        select
            di.take_code as takeCode,
            item.real_delivery_bill_num as deliveryBillNum,
            item.waybill_num as waybillNum,
            bill.transport_tool_type as transportType,
            di.buyer_id as buyerId,
            di.buyer_name as buyerName,
            di.seller_id as sellerId,
            di.seller_name as sellerName,
            db.consignor_id as consignorId,
            db.consignor_name as consignorName,
            db.carrier_id as carrierId,
            db.carrier_name as carrierName,
            concat(di.province,di.city,di.district) as receiveProvinces,
            concat(di.province,di.city,di.district) as receiveAddress,
            concat(di.province,di.city,di.district) as receivingWharfAddress,
            concat(bill.warehouse_province,bill.warehouse_city,bill.warehouse_district) as warehouseAddress,
            concat(bill.warehouse_province,bill.warehouse_city,bill.warehouse_district) as pickingWharfAddress,
            di.goods_id as goodsId,
            di.note as goodsName,
            if(bill.transport_tool_type = '030230100',bill.vehicle_num,bill.shipping_name) as vehicleNum,
            if(bill.transport_tool_type = '030230100',v.load_capacity,si.blevel_payload) as vehiclePayload,
            bill.type as deliverWay,
            bill.warehouse_id as warehouseId,
            bill.warehouse_name as warehouseName,
            di.receiver_address_id as receiveAddressId,
            di.address as receiveAddressName,
            bill.load_port_id as pickingWharfId,
            bill.load_port_name as pickingWharfName,
            bill.unload_port_id as receivingWharfId,
            bill.unload_port_name as receivingWharfName,
            item.goods_price as unitGoodsPrice,
            db.payable_carriage_price as unitLogisticPrice,
            item.pay_quantity as sendQuantity,
            bill.leave_warehouse_time as leaveWarehouseTime,
            bill.complete_time as completeTime,
            bill.estimate_km as distance,
            di.deals_id as contractId,
            di.deals_name as contractNumber,
            di.signer as signer,
            di.signer_phone as signerPhone,
            bill.complete_time as signTime,
            bill.sign_quantity as signQuantity,
            item.empty_load_charge as emptyLoadCharge,
            di.transport_category_id as transportCategoryId,
            tc.category_name as transportCategoryName
        from lgs_ship_bill_item item
        left join lgs_ship_bill bill on bill.waybill_id = item.waybill_id
        LEFT JOIN lgs_delivery_bill db ON db.delivery_bill_num = item.real_delivery_bill_num
        LEFT JOIN lgs_delivery_info di ON di.delivery_info_id = db.delivery_info_id
        left join lgs_transport_category tc on tc.transport_category_id = di.transport_category_id
        left join lgs_vehicle v on v.vehicle_id = bill.vehicle_id
        left join lgs_shipping_info si on si.shipping_id = bill.shipping_id
        where item.del_flg = 0 and bill.del_flg = 0 and di.del_flg = 0 and db.del_flg = 0
        <if test="consignorId != null and consignorId != ''">
            and db.consignor_id = #{consignorId}
        </if>
        <if test="carrierId != null and carrierId != ''">
            and db.carrier_id = #{carrierId}
        </if>
        <if test="driverId != null and driverId != ''">
            and bill.driver_id = #{driverId}
        </if>
        <if test="transportType != null and transportType != ''">
            and bill.transport_tool_type = #{transportType}
        </if>
        <if test="completeTimeStart != null and completeTimeStart != ''">
            <![CDATA[and bill.complete_time >= #{completeTimeStart}]]>
        </if>
        <if test="completeTimeEnd != null and completeTimeEnd != ''">
            <![CDATA[and bill.complete_time <= #{completeTimeEnd}]]>
        </if>
        order by item.create_time desc
    </select>

    <select id="queryBillCheckWaybillInfo" resultType="com.ecommerce.logistics.api.dto.shipbill.CheckWaybillInfoDTO">
        select
            di.take_code as takeCode,
            item.real_delivery_bill_num as deliveryBillNum,
            item.waybill_num as waybillNum,
            bill.transport_tool_type as transportType,
            di.buyer_id as buyerId,
            di.buyer_name as buyerName,
            di.seller_id as sellerId,
            di.seller_name as sellerName,
            db.consignor_id as consignorId,
            db.consignor_name as consignorName,
            db.carrier_id as carrierId,
            db.carrier_name as carrierName,
            bill.driver_id as driverId,
            bill.driver_name as driverName,
            concat(di.province,di.city,di.district) as receiveProvinces,
            concat(di.province,di.city,di.district) as receiveAddress,
            concat(di.province,di.city,di.district) as receivingWharfAddress,
            concat(bill.warehouse_province,bill.warehouse_city,bill.warehouse_district) as warehouseAddress,
            concat(bill.warehouse_province,bill.warehouse_city,bill.warehouse_district) as pickingWharfAddress,
            di.goods_id as goodsId,
            di.note as goodsName,
            if(bill.transport_tool_type = '030230100',bill.vehicle_num,bill.shipping_name) as vehicleNum,
            if(bill.transport_tool_type = '030230100',v.load_capacity,si.blevel_payload) as vehiclePayload,
            bill.type as deliverWay,
            bill.warehouse_id as warehouseId,
            bill.warehouse_name as warehouseName,
            di.receiver_address_id as receiveAddressId,
            di.address as receiveAddressName,
            bill.load_port_id as pickingWharfId,
            bill.load_port_name as pickingWharfName,
            bill.unload_port_id as receivingWharfId,
            bill.unload_port_name as receivingWharfName,
            item.origin_price as unitGoodsPrice,
            item.goods_price as newestGoodsPrice,
            round(bill.published_carriage / bill.assign_quantity,2) as unitLogisticPrice,
            round(bill.published_carriage / bill.assign_quantity,2) as newestLogisticPrice,
            item.pay_quantity as sendQuantity,
            bill.leave_warehouse_time as leaveWarehouseTime,
            bill.complete_time as completeTime,
            bill.estimate_km as distance,
            di.deals_id as contractId,
            di.deals_name as contractNumber,
            di.signer as signer,
            di.signer_phone as signerPhone,
            bill.complete_time as signTime,
            bill.sign_quantity as signQuantity,
            item.empty_load_charge as emptyLoadCharge,
            di.transport_category_id as transportCategoryId,
            tc.category_name as transportCategoryName
        from lgs_ship_bill_item item
        left join lgs_ship_bill bill on bill.waybill_id = item.waybill_id
        LEFT JOIN lgs_delivery_bill db ON db.delivery_bill_num = item.real_delivery_bill_num
        LEFT JOIN lgs_delivery_info di ON di.delivery_info_id = db.delivery_info_id
        left join lgs_transport_category tc on tc.transport_category_id = di.transport_category_id
        left join lgs_vehicle v on v.vehicle_id = bill.vehicle_id
        left join lgs_shipping_info si on si.shipping_id = bill.shipping_id
        where item.del_flg = 0 and bill.del_flg = 0 and di.del_flg = 0 and db.del_flg = 0
        and item.waybill_item_id = #{waybillItemId}
    </select>

    <select id="selectCarryWaybill" parameterType="java.lang.String" resultType="com.ecommerce.logistics.api.dto.waybill.CarryWaybillSubmitDTO">
        select
           i.waybill_num as originalDocumentNumber,
           i.waybill_num as shippingNoteNumber,
           1 as vehicleCount,
		   1 as transportTypeCode,
		   '传统运输' as transportationComb,
           b.create_time as consignmentDateTime,
           '1003997' as businessTypeCode,
           b.leave_warehouse_time as despatchActualDateTime,
           b.complete_time as goodsReceiptDateTime,
           b.warehouse_id as warehouseId,
           b.warehouse_province as placeOfLoadingDeliProvince,
           b.warehouse_city as placeOfLoadingDeliCity,
           b.warehouse_district as placeOfLoadingDeliDistrict,
           b.warehouse_address as placeOfLoadingDeliAddress,
           b.warehouse_district_code as deliCountrySubdivisionCode,
           di.seller_id as sellerId,
           di.buyer_id as buyerId,
           di.province as goodsReceiptPlaceProvince,
           di.city as goodsReceiptPlaceCity,
           di.district as goodsReceiptPlaceDistrict,
           di.address as goodsReceiptPlaceAddress,
           di.district_code as receiptCountrySubdivisionCode,
           ROUND(i.receivable_carriage_amount,3) as totalMonetaryAmount,
           lv.vehicle_id as vehicleId,
           lv.user_id as vehicleUserId,
           b.vehicle_num as vehicleNumber,
           b.driver_id as ecDriverId,
           b.driver_name as driverName,
           di.note as descriptionOfGoods,
           '0600' as cargoTypeClassificationCode,
           ROUND(b.complete_quantity * 1000,3) as goodsItemGrossWeight,
           b.carrier_id as carrierId,
           if(b.actual_km > 0, b.actual_km, b.estimate_km) as transportDistance,
           0 as pstatus
        from lgs_ship_bill_item i
        left join lgs_ship_bill b on b.waybill_id = i.waybill_id
        left join lgs_delivery_info di on di.delivery_info_id = i.delivery_info_id
        left join lgs_vehicle as lv on b.vehicle_id = lv.vehicle_id
        where i.waybill_num = #{waybillNum}
        and i.status = '030610600'
        and i.del_flg = 0
        and b.del_flg = 0
        and di.del_flg = 0
    </select>

    <update id="updateShipBillItemByWaybillItemId">
        UPDATE `lgs_ship_bill_item` SET `goods_price` = #{newestPrice}, `adjust_num` = `adjust_num` + 1, `adjust_status` = 2 WHERE `waybill_item_id` = #{waybillItemId}
    </update>

  <select id="queryWaybillIdByItemIdList" resultType="java.lang.String">
    select distinct waybill_id
    from lgs_ship_bill_item
    where waybill_item_id in
    <foreach collection="waybillItemIdList" index="win" item="wit" open="(" separator="," close=")">
      #{wit}
    </foreach>
    and del_flg = 0
  </select>

    <update id="updateWaybillItemStatusByNum">
        update lgs_ship_bill_item
        set status = #{newStatus}, update_user = #{updateUser}, update_time = current_timestamp
        where waybill_num = #{waybillNum}
        and status in
        <foreach collection="oldStatusList" index="osin" item="osit" open="(" separator="," close=")">
            #{osit}
        </foreach>
        and del_flg = 0
    </update>

    <select id="queryWaybillItemByNum" parameterType="java.lang.String" resultType="com.ecommerce.logistics.dao.vo.ShipBillItem">
        SELECT *
        FROM `lgs_ship_bill_item`
        WHERE waybill_num = #{waybillNum}
        AND can_operate = 1
        AND del_flg = 0
        LIMIT 1
    </select>


    <select id="selectSellerIdByWaybillNum" parameterType="java.lang.String" resultType="com.ecommerce.logistics.dao.dto.pickingbill.WaybillDO">
        SELECT
        t2.seller_id AS sellerId,t3.external_waybill_status
        FROM lgs_ship_bill_item AS t1
        LEFT JOIN lgs_delivery_info AS t2 ON t2.delivery_info_id = t1.delivery_info_id
        LEFT JOIN lgs_ship_bill AS t3 ON t1.waybill_id = t3.waybill_id
        WHERE
        t2.del_flg = 0
        AND t1.del_flg = 0
        AND t1.waybill_num = #{waybillNum}
        AND t2.mdm_code != ''
        LIMIT 1
    </select>

    <update id="updatePriceByTakeCode">
        update lgs_ship_bill_item as item
        left join lgs_delivery_info as info on item.delivery_info_id = info.delivery_info_id
        set item.update_time = current_timestamp,
            item.goods_price = #{newestPrice},
            item.settlement_amount = RAND(
                ((#{newestPrice} + ifnull(item.receivable_carriage_price, 0)) * ifnull(item.pay_quantity, 0))
                , 2
                )
        where info.take_code = #{takeCode}
          and info.del_flg = 0
          and item.del_flg = 0
    </update>

    <select id="getWaybillIdListByOrderCode" parameterType="String" resultType="com.ecommerce.logistics.dao.vo.ShipBillItem">
        SELECT DISTINCT `waybill_id`, `waybill_num` FROM `lgs_ship_bill_item` WHERE delivery_info_id IN(SELECT delivery_info_id FROM `lgs_delivery_info`  WHERE order_code = #{orderCode})
    </select>
</mapper>
