<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.ShipBillMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.ShipBill">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="waybill_id" jdbcType="VARCHAR" property="waybillId" />
    <result column="waybill_num" jdbcType="VARCHAR" property="waybillNum" />
    <result column="parent_flag" jdbcType="TINYINT" property="parentFlag" />
    <result column="child_flag" jdbcType="TINYINT" property="childFlag" />
    <result column="owner_id" jdbcType="VARCHAR" property="ownerId" />
    <result column="parent_id" jdbcType="VARCHAR" property="parentId" />
    <result column="carrier_id" jdbcType="VARCHAR" property="carrierId" />
    <result column="carrier_name" jdbcType="VARCHAR" property="carrierName" />
    <result column="carrier_type" jdbcType="VARCHAR" property="carrierType" />
    <result column="transport_tool_type" jdbcType="VARCHAR" property="transportToolType" />
    <result column="driver_id" jdbcType="VARCHAR" property="driverId" />
    <result column="driver_name" jdbcType="VARCHAR" property="driverName" />
    <result column="driver_phone" jdbcType="VARCHAR" property="driverPhone" />
    <result column="shipping_id" jdbcType="VARCHAR" property="shippingId" />
    <result column="shipping_name" jdbcType="VARCHAR" property="shippingName" />
    <result column="shipping_no" jdbcType="VARCHAR" property="shippingNo" />
    <result column="vehicle_id" jdbcType="VARCHAR" property="vehicleId" />
    <result column="vehicle_num" jdbcType="VARCHAR" property="vehicleNum" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="internal" jdbcType="INTEGER" property="internal" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="qr_code" jdbcType="VARCHAR" property="qrCode" />
    <result column="assign_quantity" jdbcType="DECIMAL" property="assignQuantity" />
    <result column="actual_quantity" jdbcType="DECIMAL" property="actualQuantity" />
    <result column="complete_quantity" jdbcType="DECIMAL" property="completeQuantity" />
    <result column="sign_quantity" jdbcType="DECIMAL" property="signQuantity" />
    <result column="sign_remark" jdbcType="VARCHAR" property="signRemark" />
    <result column="receivable_carriage_price" jdbcType="DECIMAL" property="receivableCarriagePrice" />
    <result column="receivable_carriage_amount" jdbcType="DECIMAL" property="receivableCarriageAmount" />
    <result column="max_seq_num" jdbcType="INTEGER" property="maxSeqNum" />
    <result column="need_monitor" jdbcType="TINYINT" property="needMonitor" />
    <result column="can_monitor" jdbcType="TINYINT" property="canMonitor" />
    <result column="cancel_reason" jdbcType="VARCHAR" property="cancelReason" />
    <result column="close_reason" jdbcType="VARCHAR" property="closeReason" />
    <result column="close_time" jdbcType="TIMESTAMP" property="closeTime" />
    <result column="arrive_warehouse_time" jdbcType="TIMESTAMP" property="arriveWarehouseTime" />
    <result column="arrive_destination_time" jdbcType="TIMESTAMP" property="arriveDestinationTime" />
    <result column="enter_factory_time" jdbcType="TIMESTAMP" property="enterFactoryTime" />
    <result column="leave_warehouse_time" jdbcType="TIMESTAMP" property="leaveWarehouseTime" />
    <result column="complete_time" jdbcType="TIMESTAMP" property="completeTime" />
    <result column="sync_flag" jdbcType="VARCHAR" property="syncFlag" />
    <result column="sync_fail_reason" jdbcType="VARCHAR" property="syncFailReason" />
    <result column="external_waybill_id" jdbcType="VARCHAR" property="externalWaybillId" />
    <result column="external_waybill_num" jdbcType="VARCHAR" property="externalWaybillNum" />
    <result column="external_waybill_status" jdbcType="VARCHAR" property="externalWaybillStatus" />
    <result column="factory_number" jdbcType="VARCHAR" property="factoryNumber" />
    <result column="external_request_no" jdbcType="VARCHAR" property="externalRequestNo" />
    <result column="origin_place" jdbcType="VARCHAR" property="originPlace" />
    <result column="estimate_km" jdbcType="DECIMAL" property="estimateKm" />
    <result column="actual_km" jdbcType="DECIMAL" property="actualKm" />
    <result column="estimate_duration" jdbcType="DECIMAL" property="estimateDuration" />
    <result column="estimate_carriage" jdbcType="DECIMAL" property="estimateCarriage" />
    <result column="published_carriage" jdbcType="DECIMAL" property="publishedCarriage" />
    <result column="approval_time" jdbcType="TIMESTAMP" property="approvalTime" />
    <result column="receive_time" jdbcType="TIMESTAMP" property="receiveTime" />
    <result column="assign_time" jdbcType="TIMESTAMP" property="assignTime" />
    <result column="monitor_enter_time" jdbcType="TIMESTAMP" property="monitorEnterTime" />
    <result column="monitor_outer_time" jdbcType="TIMESTAMP" property="monitorOuterTime" />
    <result column="delivery_time" jdbcType="TIMESTAMP" property="deliveryTime" />
    <result column="delivery_time_start" jdbcType="TIMESTAMP" property="deliveryTimeStart" />
    <result column="delivery_time_end" jdbcType="TIMESTAMP" property="deliveryTimeEnd" />
    <result column="plan_arrive_time" jdbcType="TIMESTAMP" property="planArriveTime" />
    <result column="warehouse_id" jdbcType="VARCHAR" property="warehouseId" />
    <result column="warehouse_type" jdbcType="VARCHAR" property="warehouseType" />
    <result column="warehouse_name" jdbcType="VARCHAR" property="warehouseName" />
    <result column="warehouse_province" jdbcType="VARCHAR" property="warehouseProvince" />
    <result column="warehouse_province_code" jdbcType="VARCHAR" property="warehouseProvinceCode" />
    <result column="warehouse_city" jdbcType="VARCHAR" property="warehouseCity" />
    <result column="warehouse_city_code" jdbcType="VARCHAR" property="warehouseCityCode" />
    <result column="warehouse_district" jdbcType="VARCHAR" property="warehouseDistrict" />
    <result column="warehouse_district_code" jdbcType="VARCHAR" property="warehouseDistrictCode" />
    <result column="warehouse_street" jdbcType="VARCHAR" property="warehouseStreet" />
    <result column="warehouse_street_code" jdbcType="VARCHAR" property="warehouseStreetCode" />
    <result column="warehouse_address" jdbcType="VARCHAR" property="warehouseAddress" />
    <result column="warehouse_location" jdbcType="VARCHAR" property="warehouseLocation" />
    <result column="load_port_id" jdbcType="VARCHAR" property="loadPortId" />
    <result column="load_port_name" jdbcType="VARCHAR" property="loadPortName" />
    <result column="unload_port_id" jdbcType="VARCHAR" property="unloadPortId" />
    <result column="unload_port_name" jdbcType="VARCHAR" property="unloadPortName" />
    <result column="pump_no" jdbcType="VARCHAR" property="pumpNo" />
    <result column="pumper_name" jdbcType="VARCHAR" property="pumperName" />
    <result column="all_pump_no" jdbcType="VARCHAR" property="allPumpNo" />
    <result column="all_pumper_name" jdbcType="VARCHAR" property="allPumperName" />
    <result column="lubricity_quantity" jdbcType="DECIMAL" property="lubricityQuantity" />
    <result column="lubricity_price" jdbcType="DECIMAL" property="lubricityPrice" />
    <result column="refund_status" jdbcType="VARCHAR" property="refundStatus" />
    <result column="refund_reason" jdbcType="VARCHAR" property="refundReason" />
    <result column="refund_remark" jdbcType="VARCHAR" property="refundRemark" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="version" jdbcType="BIGINT" property="version" />
  </resultMap>

  <update id="updateActualQuantity">
    update lgs_ship_bill
    set actual_quantity = #{actualQuantity}, update_user = #{updateUser}
    where waybill_id = #{waybillId} and status = '030600600' and del_flg = 0
  </update>

  <update id="updateShipBillStatusById">
    update lgs_ship_bill
    set status = #{newStatus}, update_user = #{updateUser}
    where waybill_id = #{waybillId} and status = #{oldStatus} and del_flg = 0
  </update>

  <select id="selectByWaybillNum" resultMap="BaseResultMap">
    select * from lgs_ship_bill where waybill_num = #{waybillNum} and del_flg = 0
  </select>

  <update id="normalChangeOwnerId">
    update lgs_ship_bill as bill
    left join lgs_ship_bill_item as item on bill.waybill_id = item.waybill_id
    left join lgs_delivery_info as info on item.delivery_info_id = info.delivery_info_id
    set bill.owner_id = info.buyer_id, bill.update_user = #{updateUser}
    where item.waybill_item_id = #{waybillItemId} and bill.owner_id = info.seller_id
  </update>

  <select id="queryShipBillList" resultType="com.ecommerce.logistics.api.dto.shipbill.ShipBillListDTO"
          parameterType="com.ecommerce.logistics.api.dto.shipbill.ShipBillQueryDTO">
    SELECT
      item.waybill_id AS waybillId,
      item.waybill_item_id AS waybillItemId,
      item.real_delivery_bill_num AS deliveryBillNum,
      bill.waybill_num AS waybillBillNum,
      bill.vehicle_num AS vehicleNum,
      bill.shipping_name AS shippingName,
      bill.driver_name AS driverName,
      info.goods_id AS goodsId,
      info.note AS goodsName,
      info.unit AS unit,
      bill.assign_quantity AS assignQuantity,
      bill.warehouse_id as warehouseId,
      bill.warehouse_name as warehouseName,
      bill.warehouse_type as warehouseType,
      bill.warehouse_province as warehouseProvince,
      bill.warehouse_city as warehouseCity,
      bill.warehouse_district as warehouseDistrict,
      bill.warehouse_street as warehouseStreet,
      bill.warehouse_address as warehouseAddress,
      info.province as province,
      info.city as city,
      info.district as district,
      info.street as street,
      info.address as receiveAddress,
      info.delivery_time AS deliveryTime,
      info.delivery_time_range AS deliveryTimeRange,
      bill.type AS type,
      bill.`status` AS status,
      bill.external_waybill_status AS externalWaybillStatus,
      item.type AS itemType,
      item.`status` AS itemStatus,
      bill.owner_id AS ownerId,
      bill.carrier_id AS carrierId,
      info.seller_id AS sellerId,
      info.sign_type AS signType,
      item.can_operate AS canOperate,
      bill.qr_code AS qrCode,
      bill.assign_quantity AS totalQuantity,
      bill.actual_quantity AS actualQuantity,
      info.buyer_name AS buyerName,
      info.transport_category_id AS transportCategoryId,
      bill.transport_tool_type AS transportToolType,
      bill.sync_flag AS syncFlag,
      DATE_FORMAT( bill.create_time, '%Y-%m-%d' ) AS createTime,
      DATE_FORMAT( bill.arrive_destination_time, '%Y-%m-%d' ) AS arriveDestinationTimeStr,
      bill.need_monitor AS needMonitor,
      info.special_flag AS specialFlag,
      item.bill_proxy_type as billProxyType,
      bill.external_waybill_num AS externalWaybillNum,
      bill.sync_fail_reason AS syncFailReason,
      item.bill_proxy_type as billProxyType,
      info.price as orderPrice,
      item.origin_price as originPrice,
      item.goods_price as newstPrice,
      info.deals_id as dealsId,
      info.sale_region_path as saleRegionPath,
      ifnull(bill.vehicle_num,bill.shipping_name) AS transportToolName
      FROM
      lgs_ship_bill_item item
      RIGHT JOIN lgs_ship_bill bill ON item.waybill_id = bill.waybill_id
      JOIN lgs_delivery_info info ON item.delivery_info_id = info.delivery_info_id
    WHERE
      item.del_flg = 0
      AND bill.del_flg = 0
      AND info.del_flg = 0
      <if test="sellerId != null and sellerId != ''">
        AND info.seller_id = #{sellerId}
      </if>
      <if test="buyerId != null and buyerId != ''">
        AND info.buyer_id = #{buyerId}
      </if>
      <if test="carrierId != null and carrierId != ''">
        AND bill.carrier_id = #{carrierId}
      </if>
      <if test="sellerIdFrom != null and sellerIdFrom != ''">
          AND info.seller_id = #{sellerIdFrom}
      </if>
      <if test="buyerIdFrom != null and buyerIdFrom != ''">
          AND info.buyer_id = #{buyerIdFrom}
      </if>
      <if test="carrierIdFrom != null and carrierIdFrom != ''">
          AND bill.carrier_id = #{carrierIdFrom} AND item.can_operate = 1
      </if>
      <if test="queryAppName != null and (queryAppName == 'web-carrier' or queryAppName == 'app-carrier') ">
          AND bill.type = '030080200'
      </if>
      <if test="transportType != null and transportType != ''">
	  	  AND bill.transport_tool_type = #{transportType}
	  </if>
      <if test="status != null and status != ''">
        AND bill.status= #{status}
      </if>
      <if test="deliveryBillNum != null and deliveryBillNum != ''">
        AND item.real_delivery_bill_num LIKE CONCAT('%', #{deliveryBillNum}, '%')
      </if>
      <if test="waybillBillNum != null and waybillBillNum != ''">
        AND bill.waybill_num LIKE CONCAT('%', #{waybillBillNum}, '%')
      </if>
      <if test="type != null and type != '' ">
          AND bill.type = #{type}
      </if>
      <if test="shippingId != null and shippingId != ''">
        AND bill.shipping_id = #{shippingId}
      </if>
      <if test="shippingName != null and shippingName != ''">
        AND ( bill.shipping_name LIKE CONCAT('%', #{shippingName}, '%') OR bill.vehicle_num LIKE CONCAT('%', #{shippingName}, '%') )
      </if>
      <if test="warehouseId != null and warehouseId != ''">
        AND info.warehouse_id = #{warehouseId}
      </if>
      <if test="warehouseProvinceCode != null and warehouseProvinceCode != ''">
        AND bill.warehouse_province_code = #{warehouseProvinceCode}
      </if>
      <if test="warehouseCityCode != null and warehouseCityCode != ''">
        AND bill.warehouse_city_code = #{warehouseCityCode}
      </if>
      <if test="warehouseDistrictCode != null and warehouseDistrictCode != ''">
        AND bill.warehouse_district_code = #{warehouseDistrictCode}
      </if>
      <if test="receiverAddressId != null and receiverAddressId != ''">
        AND info.receiver_address_id = #{receiverAddressId}
      </if>
      <if test="receiveProvinceCode != null and receiveProvinceCode != ''">
        AND info.province_code = #{receiveProvinceCode}
      </if>
      <if test="receiveCityCode != null and receiveCityCode != ''">
        AND info.city_code = #{receiveCityCode}
      </if>
      <if test="receiveDistrictCode != null and receiveDistrictCode != ''">
        AND info.district_code = #{receiveDistrictCode}
      </if>
      <if test="receiveAddress != null and receiveAddress != ''">
        AND info.address LIKE CONCAT('%',#{receiveAddress},'%')
      </if>

      <if test="deliveryTimeStart != null and deliveryTimeStart != ''">
        <![CDATA[AND info.delivery_time >= #{deliveryTimeStart}]]>
      </if>
      <if test="deliveryTimeEnd != null and deliveryTimeEnd != ''">
        <![CDATA[AND info.delivery_time <= CONCAT(#{deliveryTimeEnd}, '23:59:59')]]>
      </if>
      <if test="beginCreateTime != null and beginCreateTime != ''">
          <![CDATA[AND bill.create_time >= CONCAT(#{beginCreateTime},' 00:00:00')]]>
      </if>
      <if test="endCreateTime != null and endCreateTime != ''">
          <![CDATA[AND bill.create_time <= CONCAT(#{endCreateTime},' 23:59:59')]]>
      </if>
      <if test="beginLeaveWarehouseTime != null and beginLeaveWarehouseTime != ''">
          <![CDATA[AND bill.leave_warehouse_time >= #{beginLeaveWarehouseTime}]]>
      </if>
      <if test="endLeaveWarehouseTime != null and endLeaveWarehouseTime != ''">
          <![CDATA[AND bill.leave_warehouse_time <= CONCAT(#{endLeaveWarehouseTime}, ' 23:59:59')]]>
      </if>
      <if test="transportCategoryId != null and transportCategoryId != ''">
          AND info.transport_category_id = #{transportCategoryId}
      </if>
      
      <if test="note != null and note != ''">
          AND info.note = #{note}
      </if>
      <if test="mdmCode != null and mdmCode != ''">
          AND info.mdm_code = #{mdmCode}
      </if>
      <if test="goodsName != null and goodsName != ''">
          AND info.note LIKE CONCAT('%', #{goodsName}, '%')
      </if>
      <if test="mdmCodeList != null and mdmCodeList.size() > 0">
          AND (info.mdm_code = '' OR info.mdm_code IN
          <foreach collection="mdmCodeList" item="mit" open="(" separator="," close=")">
              #{mit}
          </foreach>
          )
      </if>
      <if test="districtCodeList != null and districtCodeList.size() >0">
        AND info.district_code IN
        <foreach collection="districtCodeList" index="srin" item="srit" open="(" separator="," close=")">
          #{srit}
        </foreach>
      </if>
      <if test="regionSonIdList != null and regionSonIdList.size() >0">
        AND info.sale_region_path IN
        <foreach collection="regionSonIdList" index="srin" item="srit" open="(" separator="," close=")">
          #{srit}
        </foreach>
      </if>
    ORDER BY bill.create_time DESC
  </select>

  <select id="queryShipBillList_COUNT" resultType="Long"
          parameterType="com.ecommerce.logistics.api.dto.shipbill.ShipBillQueryDTO">
    SELECT
        count(1)
      FROM
      lgs_ship_bill_item item
      RIGHT JOIN lgs_ship_bill bill ON item.waybill_id = bill.waybill_id
      JOIN lgs_delivery_info info ON item.delivery_info_id = info.delivery_info_id
    WHERE
      item.del_flg = 0
      AND bill.del_flg = 0
      AND info.del_flg = 0
      <if test="sellerId != null and sellerId != ''">
        AND info.seller_id = #{sellerId}
      </if>
      <if test="buyerId != null and buyerId != ''">
        AND info.buyer_id = #{buyerId}
      </if>
      <if test="carrierId != null and carrierId != ''">
        AND bill.carrier_id = #{carrierId}
      </if>
      <if test="sellerIdFrom != null and sellerIdFrom != ''">
          AND info.seller_id = #{sellerIdFrom}
      </if>
      <if test="buyerIdFrom != null and buyerIdFrom != ''">
          AND info.buyer_id = #{buyerIdFrom}
      </if>
      <if test="carrierIdFrom != null and carrierIdFrom != ''">
          AND bill.carrier_id = #{carrierIdFrom} AND item.can_operate = 1
      </if>
      <if test="queryAppName != null and (queryAppName == 'web-carrier' or queryAppName == 'app-carrier') ">
          AND bill.type = '030080200'
      </if>
      <if test="transportType != null and transportType != ''">
	  	  AND bill.transport_tool_type = #{transportType}
	  </if>
      <if test="status != null and status != ''">
        AND bill.status= #{status}
      </if>
      <if test="deliveryBillNum != null and deliveryBillNum != ''">
        AND item.real_delivery_bill_num LIKE CONCAT('%', #{deliveryBillNum}, '%')
      </if>
      <if test="waybillBillNum != null and waybillBillNum != ''">
        AND bill.waybill_num LIKE CONCAT('%', #{waybillBillNum}, '%')
      </if>
      <if test="type != null and type != '' ">
          AND bill.type = #{type}
      </if>
      <if test="shippingId != null and shippingId != ''">
        AND bill.shipping_id = #{shippingId}
      </if>
      <if test="shippingName != null and shippingName != ''">
        AND ( bill.shipping_name LIKE CONCAT('%', #{shippingName}, '%') OR bill.vehicle_num LIKE CONCAT('%', #{shippingName}, '%') )
      </if>
      <if test="warehouseId != null and warehouseId != ''">
        AND info.warehouse_id = #{warehouseId}
      </if>
      <if test="warehouseProvinceCode != null and warehouseProvinceCode != ''">
        AND bill.warehouse_province_code = #{warehouseProvinceCode}
      </if>
      <if test="warehouseCityCode != null and warehouseCityCode != ''">
        AND bill.warehouse_city_code = #{warehouseCityCode}
      </if>
      <if test="warehouseDistrictCode != null and warehouseDistrictCode != ''">
        AND bill.warehouse_district_code = #{warehouseDistrictCode}
      </if>
      <if test="receiverAddressId != null and receiverAddressId != ''">
        AND info.receiver_address_id = #{receiverAddressId}
      </if>
      <if test="receiveProvinceCode != null and receiveProvinceCode != ''">
        AND info.province_code = #{receiveProvinceCode}
      </if>
      <if test="receiveCityCode != null and receiveCityCode != ''">
        AND info.city_code = #{receiveCityCode}
      </if>
      <if test="receiveDistrictCode != null and receiveDistrictCode != ''">
        AND info.district_code = #{receiveDistrictCode}
      </if>
      <if test="receiveAddress != null and receiveAddress != ''">
        AND info.address LIKE CONCAT('%',#{receiveAddress},'%')
      </if>

      <if test="deliveryTimeStart != null and deliveryTimeStart != ''">
        <![CDATA[AND info.delivery_time >= #{deliveryTimeStart}]]>
      </if>
      <if test="deliveryTimeEnd != null and deliveryTimeEnd != ''">
        <![CDATA[AND info.delivery_time <= CONCAT(#{deliveryTimeEnd}, '23:59:59')]]>
      </if>
      <if test="beginCreateTime != null and beginCreateTime != ''">
          <![CDATA[AND bill.create_time >= CONCAT(#{beginCreateTime},' 00:00:00')]]>
      </if>
      <if test="endCreateTime != null and endCreateTime != ''">
          <![CDATA[AND bill.create_time <= CONCAT(#{endCreateTime},' 23:59:59')]]>
      </if>
      <if test="beginLeaveWarehouseTime != null and beginLeaveWarehouseTime != ''">
          <![CDATA[AND bill.leave_warehouse_time >= #{beginLeaveWarehouseTime}]]>
      </if>
      <if test="endLeaveWarehouseTime != null and endLeaveWarehouseTime != ''">
          <![CDATA[AND bill.leave_warehouse_time <= CONCAT(#{endLeaveWarehouseTime}, ' 23:59:59')]]>
      </if>
      <if test="transportCategoryId != null and transportCategoryId != ''">
          AND info.transport_category_id = #{transportCategoryId}
      </if>
      
      <if test="note != null and note != ''">
          AND info.note = #{note}
      </if>
      <if test="mdmCode != null and mdmCode != ''">
          AND info.mdm_code = #{mdmCode}
      </if>
      <if test="goodsName != null and goodsName != ''">
          AND info.note LIKE CONCAT('%', #{goodsName}, '%')
      </if>
      <if test="mdmCodeList != null and mdmCodeList.size() > 0">
          AND (info.mdm_code = '' OR info.mdm_code IN
          <foreach collection="mdmCodeList" item="mit" open="(" separator="," close=")">
              #{mit}
          </foreach>
          )
      </if>
      <if test="districtCodeList != null and districtCodeList.size() >0">
          AND info.district_code IN
          <foreach collection="districtCodeList" index="srin" item="srit" open="(" separator="," close=")">
              #{srit}
          </foreach>
      </if>
      <if test="regionSonIdList != null and regionSonIdList.size() >0">
        AND info.sale_region_path IN
        <foreach collection="regionSonIdList" index="srin" item="srit" open="(" separator="," close=")">
            #{srit}
        </foreach>
      </if>
      <if test="salesmanId != null and salesmanId != ''">
          and info.salesman_id = #{salesmanId}
      </if>
  </select>

  <update id="completeShipBill">
    update lgs_ship_bill
    set status = #{status},
        complete_quantity = #{completeQuantity},
        complete_time = #{completeTime},
        update_user = #{updateUser},
        actual_km = #{actualKm}
    where waybill_id = #{waybillId}
  </update>


  <select id="queryAllShipBillByDeliveryBillId"  parameterType="java.lang.String"  resultType="com.ecommerce.logistics.api.dto.DeliveryDetailVehicleDTO">
    select
        i.real_delivery_bill_num as deliveryBillNum,
        b.carrier_name as carrierName,
        i.waybill_num as waybillNum,
        if(b.transport_tool_type = '030230100',b.vehicle_num,b.shipping_name) as vehicleNum,
        b.driver_name as driverName,
        CASE when b.complete_quantity is not null then b.complete_quantity
             when b.actual_quantity is not null then b.actual_quantity
        ELSE b.assign_quantity end as quantity,
        b.transport_tool_type as transportToolType,
        b.status as status
    from lgs_ship_bill_item i
    left join lgs_ship_bill b on b.waybill_id = i.waybill_id
    where i.del_flg = 0
    and i.delivery_bill_id in
    <foreach collection="deliveryBillIds" item="status" open="(" separator="," close=")">
      #{status}
    </foreach>
    order by i.create_time desc
  </select>

    <select id="queryShipBillByDeliveryBillId"  parameterType="java.lang.String"  resultType="com.ecommerce.logistics.api.dto.DeliveryDetailVehicleDTO">
        select
        i.real_delivery_bill_num as deliveryBillNum,
        b.carrier_name as carrierName,
        i.waybill_num as waybillNum,
        if(b.transport_tool_type = '030230100',b.vehicle_num,b.shipping_name) as vehicleNum,
        b.driver_name as driverName,
        CASE when b.complete_quantity is not null then b.complete_quantity
        when b.actual_quantity is not null then b.actual_quantity
        ELSE b.assign_quantity end as quantity,
        b.transport_tool_type as transportToolType,
        b.status as status
        from lgs_ship_bill_item i
        left join lgs_ship_bill b on b.waybill_id = i.waybill_id
        left join lgs_delivery_bill db on i.delivery_bill_id = db.delivery_bill_id
        where i.del_flg = 0
        and db.leaf_flag = 1
        and db.parent_id = #{deliveryBillId}
        order by i.create_time desc
    </select>

  <select id="queryForbidCloseWaybillCount"  parameterType="java.lang.String" resultType="java.lang.Integer">
        select
        count(b.waybill_id)
        from lgs_ship_bill_item i
        left join lgs_ship_bill b on b.waybill_id = i.waybill_id
        left join lgs_delivery_bill db on i.delivery_bill_id = db.delivery_bill_id
        where i.del_flg = 0
        and db.leaf_flag = 1
        and db.parent_id in (select delivery_bill_id from lgs_delivery_bill where del_flg = 0 and node_num_path LIKE CONCAT ( (select node_num_path from lgs_delivery_bill where delivery_bill_id = #{deliveryBillId}) ,'%'))
        and b.status not in ('030601000','030600900', '030601100')
   </select>

    <select id="queryForbidCloseWaybillCountByTakeCode" parameterType="com.ecommerce.logistics.api.dto.ForBidCloseWaybillCountQueryDTO" resultType="java.lang.Integer">
        select
        count(b.waybill_id)
        from lgs_ship_bill_item i
        left join lgs_ship_bill b on b.waybill_id = i.waybill_id
        left join lgs_delivery_info di on di.delivery_info_id = i.delivery_info_id
        where i.del_flg = 0
        and di.take_code = #{takeCode}
        and
        <if test="waitDeliveryFlag != null and waitDeliveryFlag == 0">
            b.status not in ('030601000','030600900', '030601100')
        </if>
        <if test="waitDeliveryFlag != null and waitDeliveryFlag == 1">
            (b.status in ('030600700','030600800') or
            (b.status = '030600600' and b.external_waybill_status not in ('','030280210','030280200','030280400','030281400','030281500')))
        </if>
   </select>

    <select id="queryForbidCloseWaybillListByTakeCode" parameterType="com.ecommerce.logistics.api.dto.ForBidCloseWaybillCountQueryDTO" resultType="com.ecommerce.logistics.dao.vo.ShipBill">
        select b.waybill_num, b.status, b.external_waybill_status
        from lgs_ship_bill_item i
        left join lgs_ship_bill b on b.waybill_id = i.waybill_id
        left join lgs_delivery_info di on di.delivery_info_id = i.delivery_info_id
        where i.del_flg = 0
        and di.take_code = #{takeCode}
        and
        <if test="waitDeliveryFlag != null and waitDeliveryFlag == 0">
            b.status not in ('030601000','030600900', '030601100')
        </if>
        <if test="waitDeliveryFlag != null and waitDeliveryFlag == 1">
            (b.status in ('030600700','030600800') or
            (b.status = '030600600' and b.external_waybill_status not in ('','030280210','030280200','030280400','030281400','030281500')))
        </if>
    </select>

    <select id="selectByTakeCodeAndStatus"  resultMap="BaseResultMap">
        select
        b.*
        from lgs_ship_bill_item i
        left join lgs_ship_bill b on b.waybill_id = i.waybill_id
        left join lgs_delivery_info di on di.delivery_info_id = i.delivery_info_id
        where i.del_flg = 0
        and b.del_flg = 0
        and di.take_code in
        <foreach close=")" collection="takeCodeList" index="index" item="takeCode" open="(" separator=",">
            #{takeCode}
        </foreach>
        and b.status in
        <foreach close=")" collection="statusList" index="index" item="status" open="(" separator=",">
            #{status}
        </foreach>
   </select>

    <update id="closeExpireWaybillList">
        update lgs_ship_bill
        set status = '030601000',
        update_time = current_timestamp
        where waybill_id in
        <foreach collection="shipBillList" item="wit" index="wii" open="(" separator="," close=")">
            #{wit}
        </foreach>
        and del_flg = 0
    </update>

  <select id="selectWaybillDetailsByWaybillItemId" parameterType="java.lang.String"
          resultType="com.ecommerce.logistics.api.dto.shipbill.ShipBillDetailDTO">
    SELECT
      item.waybill_item_id AS waybillItemId,
      item.waybill_id AS waybillId,
      item.waybill_num AS waybillNum,
      bill.parent_id AS parentId,
      bill.waybill_num AS parentNum,
      bill.child_flag AS childFlag,
      bill.type AS type,
      bill.internal AS internal,
      bill.`status` AS status,
      item.type AS itemType,
      item.`status` AS itemStatus,
      item.can_operate AS canOperate,
      bill.carrier_id AS carrierId,
      bill.carrier_name AS carrierName,
      bill.transport_tool_type AS transportToolType,
      bill.need_monitor AS needMonitor,
      bill.sync_flag AS syncFlag,
      bill.external_waybill_num AS externalWaybillNum,
      bill.external_waybill_status AS externalWaybillStatus,
      bill.sync_fail_reason AS syncFailReason,
      bill.cancel_reason AS cancelReason,
      info.load_port_name AS loadPortName,
      info.transport_category_id AS transportCategoryId,
      IFNULL( bill.complete_quantity, IFNULL( bill.actual_quantity, bill.assign_quantity ) ) AS quantity,
      info.unit AS productUnit,
      info.special_flag AS specialFlag,
      item.goods_price AS price,
      item.receivable_carriage_price as carriagePrice,
      item.settlement_amount AS orderPayedAmount,
      info.unload_port_name AS unloadPortName,
      bill.warehouse_id AS warehouseId,
      bill.warehouse_name AS warehouseName,
      DATE_FORMAT( bill.plan_arrive_time, '%Y-%m-%d %H:%i:%s' ) AS planArriveTime,
      CONCAT_WS( '', bill.warehouse_province, bill.warehouse_city, bill.warehouse_district, bill.warehouse_street, bill.warehouse_address ) AS warehouseAddress,
      bill.shipping_name AS shippingName,
      if(bill.transport_tool_type = '030230100',bill.vehicle_num,bill.shipping_name) AS vehicleNum,
      bill.driver_name AS driverName,
      bill.driver_phone AS driverPhone,
      bill.qr_code AS qrCode,
      DATE_FORMAT( bill.leave_warehouse_time, '%Y-%m-%d %H:%i:%s' ) AS leaveWarehouseTime,
      DATE_FORMAT( bill.arrive_warehouse_time, '%Y-%m-%d %H:%i:%s' ) AS arriveWarehouseTime,
      DATE_FORMAT( bill.create_time, '%Y-%m-%d %H:%i:%s' ) AS createTime,
      DATE_FORMAT( bill.update_time, '%Y-%m-%d %H:%i:%s' ) AS updateTime,
      DATE_FORMAT( bill.assign_time, '%Y-%m-%d %H:%i:%s' ) AS assignTime,
      bill.published_carriage AS publishedCarriage,
      bill.estimate_km AS estimateKm,
      info.delivery_time AS deliveryTime,
      info.delivery_time_range AS deliveryTimeRange,
      info.price as orderPrice,
      item.origin_price as originPrice,
      item.goods_price as newstPrice,
      info.deals_id as dealsId,
      bill.actual_quantity AS actualQuantity
    FROM
      lgs_ship_bill_item item
      RIGHT JOIN lgs_ship_bill bill ON item.waybill_id = bill.waybill_id
      JOIN lgs_delivery_info info ON item.delivery_info_id = info.delivery_info_id
    WHERE
      item.del_flg = 0
      AND bill.del_flg = 0
      AND info.del_flg = 0
      AND item.waybill_item_id = #{waybillItemId}
  </select>

  <select id="selectProductDetailByWaybillItemId" parameterType="java.lang.String"
          resultType="com.ecommerce.logistics.api.dto.productinfo.ProductDetailDTO">
    SELECT
      info.product_img AS productImg,
      bill.`status` AS status,
      bill.waybill_id AS waybillId,
      bill.waybill_num AS waybillNum,
      bill.parent_id AS parentId,
      item.waybill_item_id AS waybillItemId,
      bill.external_waybill_num AS externalWaybillNum,
      info.special_flag AS specialFlag,
      info.note AS productDesc,
      info.commodity_code AS commodityCode,
      info.take_code AS deliverySheetNum,
      info.unit AS productUnit,
      bill.assign_quantity AS quantity,
      bill.actual_quantity AS actualQuantity,
      info.carriage_unit_price AS carriageUnitPrice,
      info.buyer_id AS buyerId,
      info.buyer_name AS buyerName,
      info.seller_id AS sellerId,
      info.seller_name AS sellerName,
      info.receiver AS receiver,
      info.receiver_phone AS receiverPhone,
      info.location AS location,
      DATE_FORMAT( info.delivery_time, '%Y-%m-%d' ) AS deliveryTime,
      DATE_FORMAT( bill.arrive_destination_time, '%Y-%m-%d %H:%i:%s' ) AS arriveDestinationTime,
      DATE_FORMAT( bill.complete_time, '%Y-%m-%d %H:%i:%s' ) AS completeTime,
      info.delivery_time_range AS deliveryTimeRange,
      CONCAT_WS( '', info.province, info.city, info.district, info.street ) AS receiverDistrict,
      info.receiver_address_id AS receiveAddressId,
      info.address AS receiverAddress,
      info.sign_type AS signType,
      info.unload_port_name AS unloadPortName,
      info.unloading_flow_code AS unloadingFlowCode,
      info.carry_flag AS carryFlag,
      info.unload_port_name AS unloadPortName
    FROM
      lgs_ship_bill_item item
      RIGHT JOIN lgs_ship_bill bill ON item.waybill_id = bill.waybill_id
      JOIN lgs_delivery_info info ON item.delivery_info_id = info.delivery_info_id
    WHERE
      item.del_flg = 0
      AND bill.del_flg = 0
      AND info.del_flg = 0
      AND item.waybill_item_id = #{waybillItemId}
  </select>

  <select id="selectProductDetailByWaybillId" parameterType="java.lang.String"
            resultType="com.ecommerce.logistics.api.dto.productinfo.ProductDetailDTO">
    SELECT
      info.product_img AS productImg,
      bill.`status` AS status,
      bill.waybill_id AS waybillId,
      bill.waybill_num AS waybillNum,
      bill.parent_id AS parentId,
      bill.waybill_num AS parentNum,
      item.waybill_item_id AS waybillItemId,
      bill.external_waybill_num AS externalWaybillNum,
      info.special_flag AS specialFlag,
      info.note AS productDesc,
      info.commodity_code AS commodityCode,
      info.take_code AS deliverySheetNum,
      info.unit AS productUnit,
      bill.assign_quantity AS quantity,
      bill.actual_quantity AS actualQuantity,
      info.carriage_unit_price AS carriageUnitPrice,
      info.buyer_id AS buyerId,
      info.buyer_name AS buyerName,
      info.seller_id AS sellerId,
      info.seller_name AS sellerName,
      info.receiver AS receiver,
      info.receiver_phone AS receiverPhone,
      info.location AS location,
      DATE_FORMAT( info.delivery_time, '%Y-%m-%d' ) AS deliveryTime,
      DATE_FORMAT( bill.arrive_destination_time, '%Y-%m-%d %H:%i:%s' ) AS arriveDestinationTime,
      DATE_FORMAT( bill.complete_time, '%Y-%m-%d %H:%i:%s' ) AS completeTime,
      info.delivery_time_range AS deliveryTimeRange,
      CONCAT_WS( '', info.province, info.city, info.district, info.street ) AS receiverDistrict,
      info.receiver_address_id AS receiveAddressId,
      info.address AS receiverAddress,
      info.sign_type AS signType,
      info.carry_flag AS carryFlag,
      info.unload_port_name AS unloadPortName,
      info.order_item_id AS orderItemId
    FROM
      lgs_ship_bill_item item
      RIGHT JOIN lgs_ship_bill bill ON item.waybill_id = bill.waybill_id
      JOIN lgs_delivery_info info ON item.delivery_info_id = info.delivery_info_id
    WHERE
      item.del_flg = 0
      AND bill.del_flg = 0
      AND info.del_flg = 0
      AND bill.parent_id = #{waybillId}
      AND item.bill_proxy_type != '030400300'
    order by bill.create_time desc
  </select>

    <select id="selectProductDetailByWaybillIdForApp" parameterType="java.lang.String"
            resultType="com.ecommerce.logistics.api.dto.productinfo.ProductDetailDTO">
    SELECT
      info.product_img AS productImg,
      bill.`status` AS status,
      bill.waybill_id AS waybillId,
      bill.waybill_num AS waybillNum,
      bill.parent_id AS parentId,
      bill.waybill_num AS parentNum,
      item.waybill_item_id AS waybillItemId,
      bill.external_waybill_num AS externalWaybillNum,
      info.special_flag AS specialFlag,
      info.note AS productDesc,
      info.commodity_code AS commodityCode,
      info.take_code AS deliverySheetNum,
      info.unit AS productUnit,
      bill.assign_quantity AS quantity,
      bill.actual_quantity AS actualQuantity,
      info.carriage_unit_price AS carriageUnitPrice,
      info.buyer_id AS buyerId,
      info.buyer_name AS buyerName,
      info.seller_id AS sellerId,
      info.seller_name AS sellerName,
      info.receiver AS receiver,
      info.receiver_phone AS receiverPhone,
      info.location AS location,
      DATE_FORMAT( info.delivery_time, '%Y-%m-%d' ) AS deliveryTime,
      DATE_FORMAT( bill.arrive_destination_time, '%Y-%m-%d %H:%i:%s' ) AS arriveDestinationTime,
      DATE_FORMAT( bill.complete_time, '%Y-%m-%d %H:%i:%s' ) AS completeTime,
      info.delivery_time_range AS deliveryTimeRange,
      CONCAT_WS( '', info.province, info.city, info.district, info.street ) AS receiverDistrict,
      info.receiver_address_id AS receiveAddressId,
      info.address AS receiverAddress,
      info.sign_type AS signType,
      info.carry_flag AS carryFlag,
      info.unload_port_name AS unloadPortName
    FROM
      lgs_ship_bill_item item
      RIGHT JOIN lgs_ship_bill bill ON item.waybill_id = bill.waybill_id
      JOIN lgs_delivery_info info ON item.delivery_info_id = info.delivery_info_id
    WHERE
      item.del_flg = 0
      AND bill.del_flg = 0
      AND info.del_flg = 0
      AND bill.parent_id = #{waybillId}
      AND item.can_operate = 1
    order by
    bill.version asc
  </select>

  <update id="increaseMaxSeq">
    update lgs_ship_bill
    set max_seq_num = max_seq_num + 1,
        update_user = #{updateUser}
    where waybill_id = #{waybillId}
  </update>

  <update id="updateForReroute">
    update lgs_ship_bill
    set max_seq_num = max_seq_num + 1,
        unload_port_id = #{unloadPortId},
        unload_port_name = #{unloadPortName},
        update_user = #{updateUser},
        internal = #{internal}
    where waybill_id = #{waybillId}
  </update>


   <select id="queryWaybillListByTakeCode" resultType="com.ecommerce.logistics.api.dto.shipbill.ShipBillOrderDetailDTO">
        select
            si.waybill_item_id as waybillItemId,
            si.waybill_id as waybillId,
            si.waybill_num as waybillNum,
            di.goods_id as goodsId,
            di.note as goodsName,
            di.product_img as goodsImg,
            di.unit as unit,
            di.price as orderPrice,
            si.origin_price as originPrice,
            si.goods_price as newstPrice,
            DATE_FORMAT( s.leave_warehouse_time, '%Y-%m-%d %H:%i:%s' ) as leaveWarehouseTime,
            s.actual_quantity as actualQuantity,
            DATE_FORMAT( s.complete_time, '%Y-%m-%d %H:%i:%s' ) as logisticsTakeTime,
            s.complete_quantity as logisticsTakeQuantity,
            si.goods_price as goodsUnitPrice,
            si.receivable_carriage_price as logisticsUnitPrice,
            concat(s.shipping_name, s.vehicle_num) as transportToolName,
            concat(s.shipping_id, s.vehicle_id) as transportToolId,
            s.driver_id as driverId,
            s.driver_phone as driverPhone,
            s.driver_name as driverName,
            s.shipping_id as shippingId,
            s.shipping_name as shippingName,
            s.shipping_no as shippingNo
        from lgs_ship_bill_item si
        left join lgs_ship_bill s on s.waybill_id = si.waybill_id
        left join lgs_delivery_info di on di.delivery_info_id = si.delivery_info_id
        where si.del_flg = 0
--         and s.status = '030600900'
        and si.`status` = '030610600'
        and di.take_code in
        <foreach collection="takeCodeList" item="wit" index="wii" open="(" separator="," close=")">
           #{wit}
        </foreach>
        order by si.waybill_num
   </select>

    <select id="queryTradeWaybillList" resultType="com.ecommerce.logistics.api.dto.waybill.TradeWaybillDTO">
        SELECT
            bill.waybill_id AS waybillId,
            item.waybill_item_id AS waybillItemId,
            bill.waybill_num AS waybillNum,
            bill.`status` AS waybillStatus,
            item.`status` AS itemStatus,
            bill.complete_time AS completeTime,
            bill.actual_quantity AS actualShippingQuantity,
            bill.update_time AS updateTime,
            item.empty_load_flag AS emptyLoadFlag,
            item.empty_load_charge AS emptyLoadCharge,
            item.empty_load_quantity as emptyLoadQuantity,
            IFNULL( bill.sign_quantity, '-' ) AS signQuantity,
            bill.arrive_warehouse_time AS arriveWarehouseTime,
            bill.leave_warehouse_time AS leaveWarehouseTime,
            bill.driver_id AS driverId,
            bill.driver_name AS driverName,
            bill.driver_phone AS driverPhone,
            bill.vehicle_id AS vehicleId,
            bill.vehicle_num AS vehicleNum,
            bill.assign_quantity AS shippingQuantity,
            info.order_item_id AS resourceId,
            info.product_img AS productImg,
            info.sign_type AS signType,
            bill.transport_tool_type AS transportToolType,
            bill.shipping_id AS shippingId,
            bill.shipping_name AS shippingName,
            bill.shipping_no AS shippingNo,
            item.goods_price AS goodsPrice,
            bill.actual_quantity * item.goods_price AS goodsAmount,
            item.receivable_carriage_amount AS logisticAmount
        FROM
            lgs_ship_bill_item item
            RIGHT JOIN lgs_ship_bill bill ON item.waybill_id = bill.waybill_id
            JOIN lgs_delivery_info info ON item.delivery_info_id = info.delivery_info_id
        WHERE
            item.del_flg = 0
            AND bill.del_flg = 0
            AND info.del_flg = 0
            AND info.take_code = #{deliverySheetNum}
            AND bill.`status` IN
            <foreach collection="statusList" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        order by bill.waybill_num
    </select>

    <select id="selectShipBillDetailByWaybillId" parameterType="java.lang.String"
            resultType="com.ecommerce.logistics.api.dto.shipbill.ShipBillDetailDTO">
        SELECT
            item.waybill_item_id AS waybillItemId,
            bill.waybill_id AS waybillId,
            bill.waybill_num AS waybillNum,
            bill.type AS type,
            bill.internal AS internal,
            bill.parent_id AS parentId,
            bill.child_flag AS childFlag,
            bill.parent_flag AS parentFlag,
            bill.`status` AS status,
            item.type AS itemType,
            item.`status` AS itemStatus,
            bill.carrier_id AS carrierId,
            info.take_code AS takeCode,
            item.bill_proxy_type AS billProxyType,
            info.mdm_code AS mdmCode,
            bill.external_waybill_id AS externalWaybillId,
            bill.external_waybill_num AS externalWaybillNum,
            bill.load_port_id AS loadPortId,
            bill.load_port_name AS loadPortName,
            bill.assign_quantity AS quantity,
            bill.actual_quantity AS productCount,
            info.seller_id AS sellerId,
            info.deals_id AS dealsId,
            item.goods_price AS price,
            item.receivable_carriage_price as carriagePrice,
            bill.unload_port_id AS unloadPortId,
            bill.unload_port_name AS unloadPortName,
            bill.warehouse_id AS warehouseId,
            bill.shipping_id AS shippingId,
            bill.shipping_name AS shippingName,
            bill.shipping_no AS shippingNo,
            bill.vehicle_num AS vehicleNum,
            bill.driver_name AS driverName,
            bill.driver_phone AS driverPhone,
            DATE_FORMAT( bill.plan_arrive_time, '%Y-%m-%d %H:%i:%s' ) AS planArriveTime,
            DATE_FORMAT( bill.create_time, '%Y-%m-%d %H:%i:%s' ) AS createTime
        FROM
            lgs_ship_bill_item item
            RIGHT JOIN lgs_ship_bill bill ON item.waybill_id = bill.waybill_id
            JOIN lgs_delivery_info info ON item.delivery_info_id = info.delivery_info_id
        WHERE
            item.del_flg = 0
            AND bill.del_flg = 0
            AND info.del_flg = 0
            AND bill.waybill_id = #{waybillId}
            AND item.bill_proxy_type != '030400300'
    </select>

    <select id="selectShipBillDetailForDriverApp" parameterType="java.lang.String"
            resultType="com.ecommerce.logistics.api.dto.shipbill.ShipBillDetailDTO">
    SELECT
      item.waybill_item_id AS waybillItemId,
      bill.waybill_id AS waybillId,
      bill.waybill_num AS waybillNum,
      bill.parent_id AS parentId,
      bill.waybill_num AS parentNum,
      bill.child_flag AS childFlag,
      bill.parent_flag AS parentFlag,
      bill.type AS type,
      bill.`status` AS status,
      item.type AS itemType,
      item.`status` AS itemStatus,
      item.can_operate AS canOperate,
      bill.carrier_id AS carrierId,
      bill.carrier_name AS carrierName,
      bill.transport_tool_type AS transportToolType,
      bill.need_monitor AS needMonitor,
      bill.sync_flag AS syncFlag,
      bill.external_waybill_num AS externalWaybillNum,
      bill.external_waybill_status AS externalWaybillStatus,
      bill.sync_fail_reason AS syncFailReason,
      bill.cancel_reason AS cancelReason,
      bill.published_carriage AS publishedCarriage,
      info.load_port_name AS loadPortName,
      info.transport_category_id AS transportCategoryId,
      IFNULL( bill.complete_quantity, IFNULL( bill.actual_quantity, bill.assign_quantity ) ) AS quantity,
      info.unit AS productUnit,
      info.special_flag AS specialFlag,
      item.goods_price AS price,
      item.receivable_carriage_price as carriagePrice,
      item.settlement_amount AS orderPayedAmount,
      info.unload_port_name AS unloadPortName,
      bill.warehouse_id AS warehouseId,
      bill.warehouse_name AS warehouseName,
      DATE_FORMAT( bill.plan_arrive_time, '%Y-%m-%d %H:%i:%s' ) AS planArriveTime,
      CONCAT_WS( '', bill.warehouse_province, bill.warehouse_city, bill.warehouse_district, bill.warehouse_street, bill.warehouse_address ) AS warehouseAddress,
      bill.shipping_name AS shippingName,
      bill.vehicle_num AS vehicleNum,
      bill.driver_id AS driverId,
      bill.driver_name AS driverName,
      bill.driver_phone AS driverPhone,
      bill.qr_code AS qrCode,
      DATE_FORMAT( bill.leave_warehouse_time, '%Y-%m-%d %H:%i:%s' ) AS leaveWarehouseTime,
      DATE_FORMAT( bill.arrive_warehouse_time, '%Y-%m-%d %H:%i:%s' ) AS arriveWarehouseTime,
      DATE_FORMAT( bill.create_time, '%Y-%m-%d %H:%i:%s' ) AS createTime,
      DATE_FORMAT( bill.update_time, '%Y-%m-%d %H:%i:%s' ) AS updateTime,
      DATE_FORMAT( bill.assign_time, '%Y-%m-%d %H:%i:%s' ) AS assignTime,
      bill.published_carriage AS publishedCarriage,
      bill.estimate_km AS estimateKm,
      info.delivery_time AS deliveryTime,
      info.delivery_time_range AS deliveryTimeRange
      FROM
            lgs_ship_bill bill
        left join lgs_ship_bill_item item ON item.waybill_id = bill.waybill_id
        left join lgs_delivery_info info ON item.delivery_info_id = info.delivery_info_id
        WHERE
        (bill.del_flg = 0 AND bill.parent_flag = 1 AND bill.waybill_id = #{waybillId}) or
        (item.del_flg = 0 AND bill.del_flg = 0 AND info.del_flg = 0 AND bill.waybill_id = #{waybillId} AND item.bill_proxy_type != '030400300')
    </select>

    <select id="selectShipBillQueryConditions" parameterType="com.ecommerce.logistics.api.dto.shipbill.ShipBillQueryDTO"
            resultType="com.ecommerce.logistics.api.dto.waybill.AppWaybillQueryConditionsDTO">
        SELECT
            info.buyer_id AS buyerId,
            info.buyer_name AS buyerName,
            info.seller_id AS sellerId,
            info.seller_name AS sellerName,
            info.note AS note,
            info.warehouse_id AS warehouseId
        FROM
            lgs_ship_bill_item item
        LEFT JOIN lgs_delivery_info info ON item.delivery_info_id = info.delivery_info_id
        WHERE
            item.del_flg = 0
            AND info.del_flg = 0
            <if test="sellerIdFrom != null and sellerIdFrom != ''">
                AND info.seller_id = #{sellerIdFrom}
            </if>
            <if test="buyerIdFrom != null and buyerIdFrom != ''">
                AND info.buyer_id = #{buyerIdFrom}
            </if>
            <if test="sellerName != null and sellerName != ''">
            	AND info.seller_name LIKE CONCAT('%', #{sellerName}, '%')
            </if>
            <if test="buyerName != null and buyerName != ''">
            	AND info.buyer_name LIKE CONCAT('%', #{buyerName}, '%')
            </if>
            <if test="note != null and note != ''">
            	AND info.note LIKE CONCAT('%', #{note}, '%')
            </if>
            <if test="warehouseName != null and warehouseName != ''">
            	AND info.warehouse_name LIKE CONCAT('%', #{warehouseName}, '%')
            </if>
        ORDER BY
            item.create_time DESC
    </select>

    <select id="queryTradeWaybillDetail" parameterType="java.lang.String" resultType="com.ecommerce.logistics.dao.dto.waybill.TradeWaybillDetailDO">
        SELECT
            bill.waybill_id AS waybillId,
            bill.waybill_num AS waybillNum,
            bill.`status` AS status,
            bill.type AS type,
            bill.create_time AS createTime,
            bill.actual_quantity AS actualQuantity,
            bill.external_waybill_num AS externalWaybillNum,
            IF( bill.transport_tool_type = '030230100', bill.vehicle_num, bill.shipping_name) AS vehicleNum,
            bill.driver_name AS driverName,
            bill.driver_phone AS driverPhone,
            bill.leave_warehouse_time AS leaveWarehouseTime,
            info.receiver AS receiver,
            bill.warehouse_id AS warehouseId,
            CONCAT_WS( '', info.province, info.city, info.district, info.address ) AS receiveAddress,
            info.location AS receiveAddressLocation,
            bill.assign_quantity AS quantity,
            bill.sign_quantity AS signQuantity,
            info.unit AS productUnit,
            info.product_img AS productImg,
            info.note AS productDetail,
            info.sign_type AS signType,
            bill.external_waybill_status AS externalWaybillStatus,
            bill.sync_fail_reason AS syncFailReason,
            item.bill_proxy_type AS billProxyType,
            bill.transport_tool_type AS transportToolType
        FROM
            lgs_ship_bill_item item
            RIGHT JOIN lgs_ship_bill bill ON item.waybill_id = bill.waybill_id
            JOIN lgs_delivery_info info ON item.delivery_info_id = info.delivery_info_id
        WHERE
            item.del_flg = 0
            AND bill.del_flg = 0
            AND info.del_flg = 0
            AND bill.waybill_id = #{waybillId}
            AND item.bill_proxy_type != '030400300'
    </select>

  <update id="cancelShipBill">
    update lgs_ship_bill
    set update_time = current_timestamp,
        update_user = #{updateUser},
        status = #{newStatus}
    where waybill_id = #{waybillId}
      and status = #{oldStatus}
      and del_flg = 0
  </update>

  <update id="discardShipBill">
    update lgs_ship_bill
    set update_time = current_timestamp,
        update_user = #{updateUser},
        status = #{newStatus},
        close_time = #{closeTime},
        close_reason = #{closeReason}
    <if test="externalWaybillStatus != null and externalWaybillStatus != ''">
      , external_waybill_status = #{externalWaybillStatus}
    </if>
    where waybill_id = #{waybillId}
      and status = #{oldStatus}
      and del_flg = 0
  </update>

  <update id="updateShipBillByPassCheckDTO" parameterType="com.ecommerce.logistics.api.dto.waybill.PassCheckDTO">
    UPDATE
      lgs_ship_bill
    SET
      update_user = #{userId},
      update_time = CURRENT_TIMESTAMP(),
      published_carriage = #{publishedCarriage},
      approval_time = CURRENT_TIMESTAMP(),
      status = #{nextStatus},
      update_user = #{userId},
      update_time = CURRENT_TIMESTAMP(),
      version = version + 1
    WHERE
      waybill_id = #{waybillId} AND version = #{currentVersion} AND status = #{currentStatus}
  </update>

  <select id="selectCityAndQuantityByWaybillId" parameterType="java.lang.String" resultType="com.ecommerce.logistics.dao.dto.pickingbill.WaybillDO">
    select
           info.province_code as provinceCode,
           info.city_code as cityCode,
           lsb.assign_quantity as quantity
    from lgs_ship_bill as lsb
    left join lgs_ship_bill_item as item on lsb.waybill_id = item.waybill_id and lsb.max_seq_num = item.seq_num and item.bill_proxy_type in ('030400100', '030400200')
    left join lgs_delivery_info as info on info.delivery_info_id = item.delivery_info_id
    where
    item.del_flg = 0
    and lsb.del_flg = 0
    and lsb.waybill_id = #{waybillId}
  </select>

  <update id="batchPassCheckMainWaybill">
    update lgs_ship_bill
    set status = '030600200', update_time = current_timestamp, update_user = #{updateUser}
    where waybill_id in
    <foreach collection="waybillIdList" item="wit" index="win" open="(" separator="," close=")">
      #{wit}
    </foreach>
    and del_flg = 0 and status = '030600100'
  </update>

  <select id="selectSeckillByParentIdList" resultType="com.ecommerce.logistics.push.vo.SeckillWaybill">
    select
           lsb.waybill_id as waybillId,
           lsb.waybill_num as waybillNum,
           info.province_code as provinceCode,
           info.city_code as cityCode,
           lsb.assign_quantity as quantity
    from lgs_ship_bill as lsb
    left join lgs_ship_bill_item as item on lsb.waybill_id = item.waybill_id and lsb.max_seq_num = item.waybill_item_id and item.bill_proxy_type in ('030400100', '030400200')
    left join lgs_delivery_info as info on info.delivery_info_id = item.delivery_info_id
    where
    item.del_flg = 0
    and lsb.del_flg = 0
    and info.del_flg = 0
    and lsb.waybill_id in
    <foreach collection="waybillIdList" item="wit" index="win" open="(" separator="," close=")">
      #{wit}
    </foreach>
  </select>

  <select id="countByDeliveryInfoType" resultType="java.lang.Integer">
		select count(distinct s.waybill_id)
		from lgs_ship_bill_item i
		left join lgs_ship_bill s on s.waybill_id = i.waybill_id
		left join lgs_delivery_info d on d.delivery_info_id = i.delivery_info_id
		where i.del_flg = 0 and s.del_flg = 0
		and (s.waybill_id = #{waybillId} or s.parent_id = #{waybillId})
		and i.type = #{pickingBillType}
  </select>

  <select id="queryCheckWaybillList" parameterType="com.ecommerce.logistics.api.dto.waybill.CheckWaybillListQueryDTO"
          resultType="com.ecommerce.logistics.api.dto.waybill.WaitCheckWaybillListDTO">
    select
           info.take_code as deliverySheetNum,
           item.real_delivery_bill_num as realDeliveryBillNum,
           lsb.waybill_id as waybillId,
           item.waybill_item_id as waybillItemId,
           lsb.parent_id as parentId,
           lsb.waybill_num as mainWaybillNum,
           lsb.waybill_num as waybillNum,
           lsb.status as mainStatus,
           lsb.status as status,
           lsb.warehouse_name as warehouseName,
           concat(
               lsb.warehouse_province, lsb.warehouse_city, lsb.warehouse_district, lsb.warehouse_address
               ) as warehouseAddress,
           lsb.estimate_carriage as estimateCarriage,
           lsb.estimate_km as estimateKm,
           lsb.estimate_duration as estimateDuration,
           lsb.assign_quantity as productQuantity,
           info.unit as productUnit,
           info.note as productDesc,
           info.district as receiveDistrict,
           concat(info.province, info.city, info.district, info.address) as receiveDetailAddress,
           date_format(lsb.delivery_time, '%Y-%m-%d') as deliveryTime,
           info.delivery_time_range as deliveryTimeRange,
           lsb.create_time as createTime
    from lgs_ship_bill as lsb
    left join lgs_ship_bill_item as item on lsb.waybill_id = item.waybill_id and lsb.max_seq_num = item.seq_num and item.can_operate = 1
    left join lgs_delivery_info as info on item.delivery_info_id = info.delivery_info_id
    <if test="regionCodeList != null and regionCodeList.size() > 0">
      INNER JOIN
      ba_region_all as auth on info.district_code = auth.region_son_code
    </if>
    where lsb.del_flg = 0
    and item.del_flg = 0
    and lsb.status = '030600100'
    <if test="waybillNum != null and waybillNum != ''">
      and lsb.waybill_num = #{waybillNum}
    </if>
    <if test="deliveryTime != null and deliveryTime != ''">
      and lsb.delivery_time = #{deliveryTime}
    </if>
    <if test="realDeliveryBillNum != null and realDeliveryBillNum != ''">
      and item.real_delivery_bill_num = #{realDeliveryBillNum}
    </if>
    <if test="warehouseProvinceCode != null and warehouseProvinceCode != ''">
      and lsb.warehouse_province_code = #{warehouseProvinceCode}
    </if>
    <if test="warehouseCityCode != null and warehouseCityCode != ''">
      and lsb.warehouse_city_code = #{warehouseCityCode}
    </if>
    <if test="warehouseDistrictCode != null and warehouseDistrictCode != ''">
      and lsb.warehouse_district_code = #{warehouseDistrictCode}
    </if>
    <if test="regionCodeList != null and regionCodeList.size() > 0">
      AND auth.region_code IN
      <foreach close=")" collection="regionCodeList" index="index" item="item" open="(" separator=",">
        #{item}
      </foreach>
    </if>
    order by lsb.create_time desc
  </select>

  <select id="queryWarehouseAdminWaitDelivery" parameterType="com.ecommerce.logistics.api.dto.waybill.WarehouseAdminWaitDeliveryQueryDTO"
          resultType="com.ecommerce.logistics.api.dto.waybill.WarehouseAdminWaitDeliveryDTO">
    select lsb.waybill_id as waybillId,
           lsb.parent_id as parentId,
           lsb.waybill_num as mainWaybillNum,
           lsb.waybill_num as waybillNum,
           lsb.driver_id as driverId,
           lsb.driver_name as driverName,
           lsb.driver_phone as driverPhone,
           lsb.vehicle_num as vehicleNum,
           date_format(lsb.delivery_time, '%Y-%m-%d'),
           info.delivery_time_range as deliveryTimeRange,
           info.seller_name as sellerName,
           info.buyer_name as buyerName,
           if(lsb.enter_factory_time is not null, 1, 0) as areadyArriveWarehouse,
           if(lsb.enter_factory_time is not null, 1, 0) as subAreadyLeaveWarehouse,
           lsb.status as status,
           lsb.sync_flag as syncFlag,
           lsb.external_waybill_status as externalWaybillStatus,
           lsb.warehouse_id as warehouseId,
           lsb.warehouse_name as warehouseName,
           lsb.warehouse_type as warehouseType,
           info.special_flag as specialFlag,
           info.receiver as receiver,
           concat(info.province, info.city, info.district, info.address) as receiveAddress,
           lsb.enter_factory_time as enterFactoryTime

    from lgs_ship_bill as lsb
    left join lgs_ship_bill_item as item on lsb.waybill_id = item.waybill_id and lsb.max_seq_num = item.seq_num and item.can_operate = 1
    left join lgs_delivery_info as info on item.delivery_info_id = info.delivery_info_id
    <if test="regionCodeList != null and regionCodeList.size() > 0">
      INNER JOIN
      ba_region_all as auth on info.district_code = auth.region_son_code
    </if>
    where
    lsb.del_flg = 0 and item.del_flg = 0
    and lsb.status = '030600600' and lsb.transport_tool_type = '030230100'
    <if test="waybillNum != null and waybillNum != ''">
      and lsb.waybill_num = #{waybillNum}
    </if>
    <if test="vehicleNum != null and vehicleNum != ''">
      and lsb.vehicle_num = #{vehicleNum}
    </if>
    <if test="buyerId != null and buyerId != ''">
      and info.buyer_id = #{buyerId}
    </if>
    <if test="warehouseIdList != null and warehouseIdList.size() > 0">
      and lsb.warehouse_id in
      <foreach collection="warehouseIdList" index="whx" item="wht" open="(" separator="," close=")">
        #{wht}
      </foreach>
    </if>
    <if test="isEntered != null">
      <choose>
        <when test="isEntered">
          AND (lsb.enter_factory_time IS NOT NULL OR lsb.enter_factory_time != '')
        </when>
        <otherwise>
          AND (lsb.enter_factory_time IS NULL OR lsb.enter_factory_time = '')
        </otherwise>
      </choose>
    </if>
    <if test="regionCodeList != null and regionCodeList.size() > 0">
      AND auth.region_code IN
      <foreach close=")" collection="regionCodeList" index="index" item="item" open="(" separator=",">
        #{item}
      </foreach>
    </if>
    order by lsb.create_time
  </select>

  <select id="queryWarehouseAdminProccessed" parameterType="com.ecommerce.logistics.api.dto.waybill.WarehouseAdminProccessedQueryDTO"
          resultType="com.ecommerce.logistics.api.dto.waybill.WarehouseAdminProccessedDTO">
    select lsb.waybill_id as waybillId,
           lsb.waybill_num as waybillNum,
           lsb.driver_name as driverName,
           lsb.driver_phone as driverPhone,
           lsb.vehicle_num as vehicleNum,
           date_format(lsb.delivery_time, '%Y-%m-%d'),
           info.delivery_time_range as deliveryTimeRange,
           info.seller_name as sellerName,
           info.buyer_name as buyerName,
           info.note as productDesc,
           info.unit as productUnit,
           lsb.assign_quantity as quantity,
           lsb.actual_quantity as actualQuantity,
           date_format(lsb.enter_factory_time, '%Y-%m-%d %H:%i:%s') as arriveWarehouseTime,
           date_format(lsb.leave_warehouse_time, '%Y-%m-%d %H:%i:%s') as leaveWarehouseTime,
           lsb.status as status,
           info.special_flag as specialFlag,
           info.receiver as receiver,
           lsb.warehouse_id as warehouseId,
           lsb.warehouse_name as warehouseName
    from lgs_ship_bill as lsb
    left join lgs_ship_bill_item as item on lsb.waybill_id = item.waybill_id and lsb.max_seq_num = item.seq_num and item.can_operate = 1
    left join lgs_delivery_info as info on item.delivery_info_id = info.delivery_info_id
    <if test="regionCodeList != null and regionCodeList.size() > 0">
      INNER JOIN
      ba_region_all as auth on info.district_code = auth.region_son_code
    </if>
    where lsb.del_flg = 0
     and item.del_flg = 0
     and info.del_flg = 0
     and lsb.transport_tool_type = '030230100'
    <if test="status != null and status != ''">
      and lsb.status = #{status}
    </if>
    <if test="statuss != null and statuss.size() > 0">
      and lsb.status in
      <foreach collection="statuss" item="sit" index="sin" open="(" separator="," close=")">
        #{sit}
      </foreach>
    </if>
    <if test="waybillNum != null and waybillNum != ''">
      and lsb.waybill_num = #{waybillNum}
    </if>
    <if test="vehicleNum != null and vehicleNum != ''">
      and lsb.vehicle_num = #{vehicleNum}
    </if>
    <if test="buyerId != null and buyerId != ''">
      and info.buyer_id = #{buyerId}
    </if>
    <if test="warehouseIdList != null and warehouseIdList.size() > 0">
      and lsb.warehouse_id in
      <foreach collection="warehouseIdList" index="whx" item="wht" open="(" separator="," close=")">
        #{wht}
      </foreach>
    </if>
    <if test="beginArriveTime != null and beginArriveTime != ''">
      and lsb.enter_factory_time &gt;= #{beginArriveTime}
    </if>
    <if test="endArriveTime != null and endArriveTime != ''">
      and lsb.enter_factory_time &lt;= concat(#{endArriveTime}, ' 23:59:59')
    </if>
    <if test="beginLeaveTime != null and beginLeaveTime != ''">
      and lsb.leave_warehouse_time &gt;= #{beginLeaveTime}
    </if>
    <if test="endLeaveTime != null and endLeaveTime != ''">
      and lsb.leave_warehouse_time &lt;= concat(#{endLeaveTime}, ' 23:59:59')
    </if>
    <if test="regionCodeList != null and regionCodeList.size() > 0">
      AND auth.region_code IN
      <foreach close=")" collection="regionCodeList" index="index" item="item" open="(" separator=",">
        #{item}
      </foreach>
    </if>
    order by lsb.create_time
  </select>

  <update id="enterFactory">
    update lgs_ship_bill
    set enter_factory_time = #{enterFactoryTime},
        update_time = current_timestamp,
        update_user = #{updateUser}
    where waybill_id = #{waybillId} and del_flg = 0 and enter_factory_time is null and status = '030600600'
  </update>

  <update id="withdrawEnterFactory">
    update lgs_ship_bill
    set enter_factory_time = null,
        update_time = current_timestamp,
        update_user = #{updateUser}
    where waybill_id = #{waybillId}
  </update>


    <update id="updateExternalWaybillInfo" parameterType="com.ecommerce.logistics.dao.dto.waybill.UpdateExternalWaybillDO">
        UPDATE
            lgs_ship_bill AS t1
        SET
            t1.update_time=NOW()
            ,t1.sync_fail_reason=''
        <if test="externalWaybillNum != null and externalWaybillNum != ''">
            ,t1.external_waybill_num=#{externalWaybillNum}
        </if>
        <if test="externalWaybillStatus != null and externalWaybillStatus != ''">
            ,t1.external_waybill_status=#{externalWaybillStatus}
        </if>
        <if test="qrCode != null and qrCode != ''">
            ,t1.qr_code = #{qrCode}
        </if>
        <if test="originPlace != null and originPlace != ''">
            ,t1.origin_place = #{originPlace}
        </if>
        WHERE
            t1.waybill_id = #{waybillId}
    </update>

  <select id="selectSubWaybillListByParentId" resultType="com.ecommerce.logistics.dao.dto.waybill.SubWaybillDO">
        SELECT
        t1.waybill_id AS waybillId,
        t1.waybill_num AS waybillNum,
        t1.status as status,
        t1.parent_id AS parentId,
        t3.note AS productDesc,
        t1.external_waybill_num as externalWaybillNum
        FROM
        lgs_ship_bill AS t1
        LEFT JOIN lgs_ship_bill_item AS t2 ON t1.waybill_id = t2.waybill_id
        LEFT JOIN lgs_delivery_info AS t3 ON t2.delivery_info_id = t3.delivery_info_id
        WHERE
        t1.del_flg = 0
        AND (( t2.del_flg = 0 AND t2.can_operate = 1 ) OR t1.parent_flag = 1 )
        AND t1.parent_id IN
        <foreach collection="list" close=")" index="index" item="parentId" open="(" separator=",">
            #{parentId}
        </foreach>
        <if test="statusList != null and statusList.size > 0">
            AND t1.status in
            <foreach collection="statusList" close=")" index="index" item="status" open="(" separator=",">
                #{status}
            </foreach>
        </if>
  </select>

  <select id="selectWaybillForSMS" resultType="com.ecommerce.logistics.biz.message.dto.WaybillCreatedSMSDTO">
        SELECT
        b.waybill_id as waybillId,
        b.waybill_num as waybillNum,
        b.warehouse_id as warehouseId,
        b.warehouse_name as warehouseName,
        b.driver_id as driverId,
        b.driver_phone as driverPhone,
        b.driver_name as productName
        FROM
        lgs_ship_bill_item i
        left join lgs_ship_bill b on b.waybill_id = i.waybill_id
        WHERE i.real_delivery_bill_num = #{realDeliveryBilNum}
          and i.can_operate = 1
          AND i.del_flg = 0

    </select>
  <update id="updateCarrierIdByWaybillId" parameterType="com.ecommerce.logistics.api.dto.waybill.WaybillAssignDTO">
    update lgs_ship_bill
    set carrier_id = #{carrierId},
        version = version + 1,
        update_user = #{userId},
        update_time = current_timestamp,
        status = #{nextStatus},
        type = #{nextType},
        carrier_type = #{carrierType},
        actual_quantity = 0,
        estimate_carriage = 0,
        vehicle_num = '',
        vehicle_id = '',
        shipping_id = '',
        shipping_no = '',
        shipping_name = '',
        driver_id = '',
        driver_name = '',
        driver_phone = '',
        leave_warehouse_time = null,
        enter_factory_time = null
    where waybill_id = #{waybillId}
      and version = #{currentVersion}
      and status = #{currentStatus}
  </update>

  <update id="updateCarrierIdByParentId" parameterType="com.ecommerce.logistics.api.dto.waybill.WaybillAssignDTO">
    update lgs_ship_bill
    set carrier_id = #{carrierId},
        version = version + 1,
        update_user = #{userId},
        update_time = current_timestamp,
        status = #{nextStatus},
        type = #{nextType},
        carrier_type = #{carrierType},
        actual_quantity = 0,
        estimate_carriage = 0,
        vehicle_num = '',
        vehicle_id = '',
        shipping_id = '',
        shipping_no = '',
        shipping_name = '',
        driver_id = '',
        driver_name = '',
        driver_phone = '',
        leave_warehouse_time = null,
        enter_factory_time = null
    where parent_id = #{waybillId}
  </update>

  <update id="updateAssignToDriverByWaybillId" parameterType="com.ecommerce.logistics.api.dto.waybill.DriverWaybillAssignDTO">
    update lgs_ship_bill
    set driver_id = #{driverId},
        driver_name = #{driverName},
        driver_phone = #{driverPhone},
        status = #{newStatus},
        update_user = #{userId},
        update_time = current_timestamp
        <if test="vehicleId != null and vehicleId != ''">
          ,vehicle_id = #{vehicleId}
        </if>
        <if test="vehicleNum != null and vehicleNum != ''">
          , vehicle_num = #{vehicleNum}
        </if>
        <if test="shippingId != null and shippingId != ''">
          , shipping_id = #{shippingId}
        </if>
        <if test="shippingNo != null and shippingNo != ''">
          , shipping_no = #{shippingNo}
        </if>
        <if test="shippingName != null and shippingName != ''">
          ,shipping_name = #{shippingName}
        </if>
    where waybill_id = #{waybillId} and status = #{oldStatus}
  </update>

  <update id="updateAssignToDriverByParentId" parameterType="com.ecommerce.logistics.api.dto.waybill.DriverWaybillAssignDTO">
    update lgs_ship_bill
    set driver_id = #{driverId},
    driver_name = #{driverName},
    driver_phone = #{driverPhone},
    status = #{newStatus},
    update_user = #{userId},
    update_time = current_timestamp
    <if test="vehicleId != null and vehicleId != ''">
      ,vehicle_id = #{vehicleId}
    </if>
    <if test="vehicleNum != null and vehicleNum != ''">
      , vehicle_num = #{vehicleNum}
    </if>
    <if test="shippingId != null and shippingId != ''">
      , shipping_id = #{shippingId}
    </if>
    <if test="shippingNo != null and shippingNo != ''">
      , shipping_no = #{shippingNo}
    </if>
    <if test="shippingName != null and shippingName != ''">
      ,shipping_name = #{shippingName}
    </if>
    where parent_id = #{waybillId}
  </update>

  <update id="confirmWaybillByDriver" parameterType="com.ecommerce.logistics.dao.dto.waybill.ConfirmWaybillDO">
    UPDATE
      lgs_ship_bill
    SET
      status = #{status},
      update_user = #{userId},
      version = version + 1,
      estimate_km= #{estimateKm},
      estimate_duration = #{estimateDuration}
    WHERE
      waybill_id = #{waybillId} AND status = #{currentStatus} AND version = #{currentVersion}
  </update>

  <update id="updateSubWaybillStatusByParentId">
    UPDATE
      lgs_ship_bill
    SET
      status = #{status}
    WHERE
      del_flg=0 and parent_id=#{parentId} and parent_flag = 0 and status=#{currentStatus}
  </update>

  <select id="selectWaybillIdForCreateErp" resultType="java.lang.String">
    select distinct lsb.waybill_id as waybillId
    from lgs_ship_bill_item as item
    left join lgs_ship_bill as lsb on lsb.waybill_id = item.waybill_id
    where item.del_flg = 0 and lsb.del_flg = 0
      and item.delivery_bill_id = #{leafDeliveryBillId}
      and lsb.status = '030600600'
      and lsb.sync_flag = '030290200'
      and (lsb.external_waybill_status is null or lsb.external_waybill_status in ('', '030280210'))
  </select>

    <select id="selectSeckillWarehouseIdByUserId" resultType="java.lang.String">
		SELECT
			DISTINCT b.warehouse_id
		FROM
			lgs_ship_bill b
		LEFT JOIN
			lgs_waybill_seckill s ON s.waybill_id = b.parent_id
		WHERE
			b.status = #{status}
			AND s.user_id = #{userId}
			AND b.del_flg = 0
	</select>

    <select id="selectSeckillDistrictsByUserId" resultType="com.ecommerce.logistics.api.dto.waybill.DistrictListDTO">
		SELECT
			DISTINCT di.district as district, di.district_code as districtCode
		FROM
			lgs_ship_bill_item i
		LEFT JOIN
			lgs_ship_bill b ON b.waybill_id = i.waybill_id
		LEFT JOIN
			lgs_waybill_seckill s ON s.waybill_id = b.parent_id
		LEFT JOIN
		    lgs_delivery_info di on di.delivery_info_id = i.delivery_info_id
		WHERE
		di.del_flg = 0 AND b.del_flg = 0 AND i.del_flg = 0
		AND b.status = #{status} AND s.user_id = #{userId}
			AND i.del_flg = 0
	</select>


    <select id="selectSeckillWaybillIdsByCon" parameterType="com.ecommerce.logistics.api.dto.waybill.UserSeckillWaybillQueryDTO" resultType="java.lang.String">
        SELECT
        DISTINCT b.parent_id
        FROM
        lgs_waybill_seckill s
        LEFT JOIN
        lgs_ship_bill b ON b.parent_id = s.waybill_id
        LEFT JOIN
        lgs_ship_bill_item bi on bi.waybill_id = b.waybill_id
        LEFt JOIN
        lgs_delivery_info i on i.delivery_info_id = bi.delivery_info_id
        WHERE
        bi.del_flg = 0 AND b.del_flg = 0 AND i.del_flg = 0
        AND b.status = #{status}
        AND s.user_id = #{userId}
        AND bi.can_operate = 1
        AND b.del_flg = 0
        <if test="warehouseId != null and warehouseId != '' ">
            AND b.warehouse_id = #{warehouseId}
        </if>
        <if test="districtCode != null and districtCode != '' ">
            AND i.district_code = #{districtCode}
        </if>
        <if test="cityCode != null and cityCode != '' ">
            AND i.city_code = #{cityCode}
        </if>
        <if test="beginDeliveryTime != null and beginDeliveryTime != ''">
            <![CDATA[AND i.delivery_time >= #{beginDeliveryTime}]]>
        </if>
        <if test="endDeliveryTime != null and endDeliveryTime != ''">
            <![CDATA[AND i.delivery_time <= #{endDeliveryTime}]]>
        </if>
        <if test="transportCategoryId != null and transportCategoryId != '' ">
            AND i.transport_category_id = #{transportCategoryId}
        </if>
        <if test="transportToolType != null and transportToolType != '' ">
            AND i.transport_tool_type = #{transportToolType}
        </if>
        ORDER BY
        s.create_time DESC
    </select>

    <select id="selectSeckilledWaybillIdsByCon" parameterType="com.ecommerce.logistics.api.dto.waybill.DriverWaybillListQueryDTO" resultType="java.lang.String">
        SELECT
        DISTINCT b.parent_id
        FROM
        lgs_ship_bill b
        WHERE b.del_flg = 0
        <if test="userPhone != null and userPhone != ''">
        	AND b.driver_phone = #{userPhone}
        </if>
        <if test="userRole != null and userRole == 3 ">
            AND b.carrier_id = #{userId}
        </if>
        <choose>
            <when test="driverPhone != null and driverPhone != ''">
                AND b.driver_phone = #{driverPhone}
            </when>
            <when test="userRole != null and userRole == 4">
                and b.driver_id = #{userId}
            </when>
            <otherwise></otherwise>
        </choose>
        <if test="statuss != null">
            AND b.status in
            <foreach collection="statuss" item="pi" open="(" separator="," close=")">
                #{pi}
            </foreach>
        </if>
        ORDER BY
        b.create_time DESC
    </select>

    <select id="selectSeckillWaybillByParentIds" parameterType="java.util.List" resultType="com.ecommerce.logistics.api.dto.shipbill.ShipBillDetailDTO">
        SELECT
        b.waybill_id as waybillId,
        b.parent_id as parentId,
        b.waybill_num as waybillNum,
        bi.waybill_item_id as waybillItemId,
        b.status as status,
        b.published_carriage as publishedCarriage,
        b.assign_quantity as quantity,
        b.estimate_km as estimateKm,
        di.unit as productUnit,
        b.warehouse_id as warehouseId,
        b.warehouse_name as warehouseName,
        concat(b.warehouse_province, b.warehouse_city, b.warehouse_district, ifnull(di.warehouse_address,'')) as warehouseAddress,
        b.driver_name as driverName,
        b.driver_phone as driverPhone,
        b.vehicle_num as vehicleNum,
        b.transport_tool_type as transportToolType,
        ltc.category_name as transportCategoryName,
        di.note as note,
        di.product_img as productImg,
        di.delivery_time as deliveryTime,
        di.delivery_time_range as deliveryTimeRange,
        concat(di.province, di.city, di.district) as address,
        concat(di.street,di.address) as street,
        b.version as version
        FROM
        lgs_ship_bill b
        LEFT JOIN
        lgs_ship_bill_item bi ON bi.waybill_id = b.waybill_id
        LEFT JOIN
        lgs_delivery_info di ON di.delivery_info_id = bi.delivery_info_id
        LEFT JOIN
        lgs_transport_category ltc on ltc.transport_category_id = di.transport_category_id
        WHERE
        b.del_flg = 0
        AND ((bi.del_flg = 0 AND bi.can_operate = 1) or b.parent_flag = 1 )
        AND b.parent_id in
        <foreach collection="waybillIds" close=")" index="index" item="id" open="(" separator=",">
            #{id}
        </foreach>
        ORDER BY
        b.create_time desc
    </select>

  <select id="selectWaybillSnatchCountByCon" parameterType="com.ecommerce.logistics.dao.dto.waybill.SnatchCountQueryDO"
          resultType="int">
    SELECT
    COUNT(*)
    FROM
    lgs_ship_bill
    WHERE
    carrier_id = #{carrierId}
    AND delivery_time = #{deliveryTime}
    AND child_flag = 0
    AND del_flg = 0
    AND status in
    <foreach collection="statuss" close=")" index="index" item="status" open="(" separator=",">
      #{status}
    </foreach>
  </select>

  <update id="snatchShipBill" parameterType="com.ecommerce.logistics.dao.dto.waybill.SnatchWaybillDO">
    UPDATE
      lgs_ship_bill
    SET status = #{nextStatus},
        <if test="carrierId != null and carrierId != ''">
          carrier_id = #{carrierId},
        </if>
        <if test="driverId != null and driverId != ''">
          driver_id = #{driverId},
        </if>
        <if test="driverName != null and driverName != ''">
          driver_name = #{driverName},
        </if>
        <if test="driverPhone != null and driverPhone != ''">
          driver_phone= #{driverPhone},
        </if>
        <if test="vehicleId != null and vehicleId != ''">
          vehicle_id = #{vehicleId},
        </if>
        <if test="vehicleNum != null and vehicleNum != ''">
          vehicle_num = #{vehicleNum},
        </if>
        receive_time = CURRENT_TIMESTAMP,
        version = version + 1
    WHERE waybill_id = #{waybillId}
      AND version = #{currentVersion}
      AND status = #{currentStatus}
  </update>

  <update id="updateSnatchInfoByParentId" parameterType="com.ecommerce.logistics.dao.dto.waybill.SnatchWaybillDO">
    UPDATE
      lgs_ship_bill
    SET status = #{nextStatus},
    <if test="carrierId != null and carrierId != ''">
      carrier_id = #{carrierId},
    </if>
    <if test="driverId != null and driverId != ''">
      driver_id = #{driverId},
    </if>
    <if test="driverName != null and driverName != ''">
      driver_name = #{driverName},
    </if>
    <if test="driverPhone != null and driverPhone != ''">
      driver_phone= #{driverPhone},
    </if>
    <if test="vehicleId != null and vehicleId != ''">
      vehicle_id = #{vehicleId},
    </if>
    <if test="vehicleNum != null and vehicleNum != ''">
      vehicle_num = #{vehicleNum},
    </if>
    receive_time = CURRENT_TIMESTAMP,
    version = version + 1
    WHERE parent_id = #{waybillId}
  </update>

  <select id="selectRecInfoByWaybillId" resultType="com.ecommerce.logistics.dao.dto.shipbill.ShipBillRecInfo">
    select lsb.waybill_id as waybillId,
           lsb.assign_quantity as assignQuantity,
           info.receiver as receiver,
           concat(info.province, info.city, info.district, info.address) as address,
           concat(lsb.warehouse_province, lsb.warehouse_city, lsb.warehouse_district, lsb.warehouse_address) as warehouseAddress
    from lgs_ship_bill as lsb
    left join lgs_ship_bill_item as item on lsb.waybill_id = item.waybill_id and lsb.max_seq_num = item.seq_num and item.can_operate = 1
    left join lgs_delivery_info as info on info.delivery_info_id = item.delivery_info_id
    where lsb.parent_id = #{waybillId} and info.del_flg = 0 and item.del_flg = 0 and lsb.del_flg = 0
  </select>

    <update id="updateArriveDestinationTime">
        UPDATE lgs_ship_bill
        SET arrive_destination_time = #{date}
        WHERE waybill_id = #{waybillId} and del_flg = 0
    </update>

    <update id="updateArriveWarehouseTimeById">
        UPDATE lgs_ship_bill
        SET arrive_warehouse_time = #{arriveWarehouseTime}
        WHERE parent_id = #{waybillId} and del_flg = 0
    </update>

    <select id="countDriverHalfwayWaybill" parameterType="com.ecommerce.logistics.dao.dto.waybill.HalfwayWaybillDO"
            resultType="java.lang.Integer" >
		SELECT
			count(*)
		FROM
			lgs_ship_bill
		WHERE status = #{deliveringStatus}
		and driver_id = #{driverId}
		and del_flg = 0
	</select>

    <select id="queryHalfWayVehicleNum" resultType="java.lang.String">
        SELECT
			distinct vehicle_num
		FROM
			lgs_ship_bill
		WHERE status = '030600700'
		and driver_id = #{driverId}
		and vehicle_num is not null
		and del_flg = 0
	</select>

  <update id="updateChildMergeInfo">
    update lgs_ship_bill
    set parent_id = #{parentId},
        child_flag = 1
    where waybill_id in
    <foreach collection="childWaybillIdList" item="cit" index="cin" open="(" separator="," close=")">
      #{cit}
    </foreach>
    and del_flg = 0
  </update>


    <select id="queryShipWaybillParentIdList_COUNT" resultType="Long" parameterType="com.ecommerce.logistics.api.dto.shipbill.ShipWaybillQueryDTO">
      SELECT
        count(DISTINCT t1.parent_id)
      FROM
        lgs_ship_bill AS t1
      LEFT JOIN lgs_ship_bill_item AS t2 ON t1.waybill_id = t2.waybill_id
      LEFT JOIN lgs_delivery_info AS t3 ON t2.delivery_info_id = t3.delivery_info_id
        WHERE
            t1.del_flg = 0 and t2.del_flg = 0
            <if test="waybillNum != null and waybillNum != ''">
                AND t1.waybill_num= #{waybillNum}
            </if>
            <if test="warehouseId != null and warehouseId != ''">
                AND t1.warehouse_id= #{warehouseId}
            </if>
            <if test="warehouseProvinceCode != null and warehouseProvinceCode != ''">
                AND t1.warehouse_province_code= #{warehouseProvinceCode}
            </if>
            <if test="warehouseCityCode != null and warehouseCityCode != ''">
                AND t1.warehouse_city_code= #{warehouseCityCode}
            </if>
            <if test="warehouseDistrictCode != null and warehouseDistrictCode != ''">
                AND t1.warehouse_district_code= #{warehouseDistrictCode}
            </if>
            <if test="transportToolName != null and transportToolName != ''">
                AND concat(t1.vehicle_num, t1.shipping_name) LIKE concat('%', #{transportToolName}, '%')
            </if>
            <if test="status != null and status != ''">
                AND t1.status= #{status}
            </if>
            <if test="deliveryBillNum != null and deliveryBillNum != ''">
                AND t2.real_delivery_bill_num= #{deliveryBillNum}
            </if>
            <if test="buyerId != null and buyerId != ''">
                AND t3.buyer_id= #{buyerId}
            </if>
            <if test="sellerId != null and sellerId != ''">
                AND t3.seller_id= #{sellerId}
            </if>
            <if test="carrierId != null and carrierId != ''">
                AND t1.carrier_id= #{carrierId}
            </if>
            <if test="queryAppName != null and (queryAppName == 'web-carrier' or queryAppName == 'app-carrier') ">
              AND t1.type IN ('030080200','030080400')
            </if>
            <if test="transportType != null and transportType != ''">
		      	AND t1.transport_tool_type = #{transportType}
		    </if>
            <if test="transportCategoryId != null and transportCategoryId != ''">
                AND t3.transport_category_id= #{transportCategoryId}
            </if>
            <if test="receiverAddressId != null and receiverAddressId != ''">
                AND t3.receiver_address_id= #{receiverAddressId}
            </if>
            <if test="receiveProvinceCode != null and receiveProvinceCode != ''">
                AND t3.province_code= #{receiveProvinceCode}
            </if>
            <if test="receiveCityCode != null and receiveCityCode != ''">
                AND t3.city_code= #{receiveCityCode}
            </if>
            <if test="receiveDistrictCode != null and receiveDistrictCode != ''">
                AND t3.district_code= #{receiveDistrictCode}
            </if>
            <if test="receiveAddress != null and receiveAddress != ''">
            	AND t3.address LIKE CONCAT('%',#{receiveAddress},'%')
            </if>
            <if test="receiverAddressId != null and receiverAddressId != ''">
              and t3.receiver_address_id = #{receiverAddressId}
            </if>
            <if test="deliveryTimeStart != null and deliveryTimeStart != ''">
                <![CDATA[AND t3.delivery_time >= #{deliveryTimeStart}]]>
            </if>
            <if test="deliveryTimeEnd != null and deliveryTimeEnd != ''">
                <![CDATA[AND t3.delivery_time <= #{deliveryTimeEnd}]]>
            </if>
            <if test="beginLeaveWarehouseTime != null and beginLeaveWarehouseTime != ''">
                <![CDATA[AND t1.leave_warehouse_time >= #{beginLeaveWarehouseTime}]]>
            </if>
            <if test="endLeaveWarehouseTime != null and endLeaveWarehouseTime != ''">
                <![CDATA[AND t1.leave_warehouse_time <= CONCAT(#{endLeaveWarehouseTime}, ' 23:59:59')]]>
            </if>
            <if test="goodsName != null and goodsName != ''">
                AND t3.note LIKE CONCAT('%', #{goodsName}, '%')
            </if>
            <if test="mdmCodeList != null and mdmCodeList.size() > 0">
                AND (t3.mdm_code = '' or t3.mdm_code in
                <foreach collection="mdmCodeList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>)
            </if>
            <if test="regionSonIdList != null and regionSonIdList.size() >0">
              AND t3.sale_region_path IN
              <foreach collection="regionSonIdList" index="srin" item="srit" open="(" separator="," close=")">
                #{srit}
              </foreach>
            </if>
      <if test="salesmanId != null and salesmanId != ''">
        and t3.salesman_id = #{salesmanId}
      </if>
    </select>

    <select id="queryShipWaybillParentIdList" parameterType="com.ecommerce.logistics.api.dto.shipbill.ShipWaybillQueryDTO" resultType="java.lang.String">
      SELECT
        t1.parent_id
      FROM
        lgs_ship_bill AS t1
      LEFT JOIN lgs_ship_bill_item AS t2 ON t1.waybill_id = t2.waybill_id
      LEFT JOIN lgs_delivery_info AS t3 ON t2.delivery_info_id = t3.delivery_info_id
        WHERE
            t1.del_flg = 0 and t2.del_flg = 0
            <if test="waybillNum != null and waybillNum != ''">
                AND t1.waybill_num= #{waybillNum}
            </if>
            <if test="warehouseId != null and warehouseId != ''">
                AND t1.warehouse_id= #{warehouseId}
            </if>
            <if test="warehouseProvinceCode != null and warehouseProvinceCode != ''">
                AND t1.warehouse_province_code= #{warehouseProvinceCode}
            </if>
            <if test="warehouseCityCode != null and warehouseCityCode != ''">
                AND t1.warehouse_city_code= #{warehouseCityCode}
            </if>
            <if test="warehouseDistrictCode != null and warehouseDistrictCode != ''">
                AND t1.warehouse_district_code= #{warehouseDistrictCode}
            </if>
            <if test="transportToolName != null and transportToolName != ''">
                AND concat(t1.vehicle_num, t1.shipping_name) LIKE concat('%', #{transportToolName}, '%')
            </if>
            <if test="status != null and status != ''">
                AND t1.status= #{status}
            </if>
            <if test="deliveryBillNum != null and deliveryBillNum != ''">
                AND t2.real_delivery_bill_num= #{deliveryBillNum}
            </if>
            <if test="buyerId != null and buyerId != ''">
                AND t3.buyer_id= #{buyerId}
            </if>
            <if test="sellerId != null and sellerId != ''">
                AND t3.seller_id= #{sellerId}
            </if>
            <if test="carrierId != null and carrierId != ''">
                AND t1.carrier_id= #{carrierId}
            </if>
            <if test="queryAppName != null and (queryAppName == 'web-carrier' or queryAppName == 'app-carrier') ">
                AND t1.type IN ('030080200','030080400')
            </if>
            <if test="transportType != null and transportType != ''">
		      	AND t1.transport_tool_type = #{transportType}
		    </if>
            <if test="transportCategoryId != null and transportCategoryId != ''">
                AND t3.transport_category_id= #{transportCategoryId}
            </if>
            <if test="receiverAddressId != null and receiverAddressId != ''">
                AND t3.receiver_address_id= #{receiverAddressId}
            </if>
            <if test="receiveProvinceCode != null and receiveProvinceCode != ''">
                AND t3.province_code= #{receiveProvinceCode}
            </if>
            <if test="receiveCityCode != null and receiveCityCode != ''">
                AND t3.city_code= #{receiveCityCode}
            </if>
            <if test="receiveDistrictCode != null and receiveDistrictCode != ''">
                AND t3.district_code= #{receiveDistrictCode}
            </if>
            <if test="receiveAddress != null and receiveAddress != ''">
            	AND t3.address LIKE CONCAT('%',#{receiveAddress},'%')
            </if>
            <if test="receiverAddressId != null and receiverAddressId != ''">
              and t3.receiver_address_id = #{receiverAddressId}
            </if>
            <if test="deliveryTimeStart != null and deliveryTimeStart != ''">
                <![CDATA[AND t3.delivery_time >= #{deliveryTimeStart}]]>
            </if>
            <if test="deliveryTimeEnd != null and deliveryTimeEnd != ''">
                <![CDATA[AND t3.delivery_time <= #{deliveryTimeEnd}]]>
            </if>
            <if test="beginLeaveWarehouseTime != null and beginLeaveWarehouseTime != ''">
                <![CDATA[AND t1.leave_warehouse_time >= #{beginLeaveWarehouseTime}]]>
            </if>
            <if test="endLeaveWarehouseTime != null and endLeaveWarehouseTime != ''">
                <![CDATA[AND t1.leave_warehouse_time <= CONCAT(#{endLeaveWarehouseTime}, ' 23:59:59')]]>
            </if>
            <if test="goodsName != null and goodsName != ''">
                AND t3.note LIKE CONCAT('%', #{goodsName}, '%')
            </if>
            <if test="internal != null and internal != ''">
                AND t1.internal = #{internal}
            </if>
            <if test="mdmCodeList != null and mdmCodeList.size() > 0">
                AND (t3.mdm_code = '' or t3.mdm_code in
                <foreach collection="mdmCodeList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>)
            </if>
            <if test="regionSonIdList != null and regionSonIdList.size() >0">
              AND t3.sale_region_path IN
              <foreach collection="regionSonIdList" index="srin" item="srit" open="(" separator="," close=")">
                #{srit}
              </foreach>
            </if>
          <if test="salesmanId != null and salesmanId != ''">
            and t3.salesman_id = #{salesmanId}
          </if>
        GROUP BY t1.parent_id
        ORDER BY t1.create_time DESC
    </select>

    <select id="queryShipWaybillItemList" parameterType="com.ecommerce.logistics.api.dto.shipbill.ShipWaybillQueryDTO"
            resultType="com.ecommerce.logistics.api.dto.shipbill.ShipDeliveryItemDTO">
        SELECT
            t1.parent_id as parentId,
            t1.waybill_id as waybillId,
            t1.waybill_num as waybillNum,
            t1.qr_code as qrCode,
            t1.assign_quantity as totalQuantity,
            t1.actual_quantity as actualQuantity,
            t1.owner_id as ownerId,
            t1.external_waybill_num as externalWaybillNum,
            t1.external_waybill_status as externalWaybillStatus,
            t1.transport_tool_type as transportToolType,
            t1.arrive_destination_time as arriveDestinationTimeStr,
            t3.waybill_item_id as waybillItemId,
            t3.delivery_bill_id as deliveryBillId,
            t3.real_delivery_bill_num as deliveryBillNum,
            t3.type as itemType,
            t3.status as itemStatus,
            t3.create_time as createTime,
            t3.can_operate as canOperate,
            t2.buyer_id as buyerId,
            t2.buyer_name as buyerName,
            t2.seller_id as sellerId,
            t2.seller_name as sellerName,
            t2.goods_id as goodsId,
            t2.note as goodsName,
            t2.transport_category_id as transportCategoryId,
            t2.unit as unit,
            t2.receiver_address_id as receiveAddressId,
            t2.receiver as receiver,
            t2.receiver_phone as receiverPhone,
            t2.province as receiveProvince,
            t2.province_code as receiveProvinceCode,
            t2.city as receiveCity,
            t2.city_code as receiveCityCode,
            t2.district as receiveDistrict,
            t2.district_code as receiveDistrictCode,
            t2.street as receiveStreet,
            t2.street_code as receiveStreetCode,
            t2.address as receiveAddress,
            t2.location as receiveLocation,
            t2.delivery_time as deliveryTime,
            t2.delivery_time_range as deliveryTimeRange,
            t2.sign_type as signType,
            t2.sync_flag as syncFlag,
            t2.need_monitor as needMonitor,
            t2.bill_proxy_type as billProxyType,
            IFNULL(t1.vehicle_num, shipping_name) AS transportToolName
        FROM
            lgs_ship_bill as t1
        LEFT JOIN
            lgs_ship_bill_item as t3 on t1.waybill_id=t3.waybill_id
        LEFT JOIN
            lgs_delivery_info as t2 on t3.delivery_info_id=t2.delivery_info_id
        WHERE
            t1.del_flg = 0 AND t1.parent_flag = 0 AND t3.del_flg = 0
            <if test="deliveryBillNum != null and deliveryBillNum != ''">
                AND t3.real_delivery_bill_num = #{deliveryBillNum}
            </if>
            <if test="buyerId != null and buyerId != ''">
                AND t2.buyer_id = #{buyerId}
            </if>
            <if test="sellerId != null and sellerId != ''">
                AND t2.seller_id = #{sellerId}
            </if>
            <if test="carrierId != null and carrierId != ''">
                AND t3.can_operate = 1
            </if>
            <if test="parentIdList != null and parentIdList.size() > 0">
                AND t1.parent_id IN
                <foreach close=")" collection="parentIdList" index="index" item="item" open="(" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="saleRegionIdList != null and saleRegionIdList.size() > 0">
              AND t2.sale_region_path in (
              SELECT
              bsra.region_son_id
              FROM
              ba_sale_region_All AS bsra
              WHERE
              bsra.region_id IN
              <foreach close=")" collection="saleRegionIdList" index="index" item="region" open="(" separator=",">
                #{region}
              </foreach>
              )
            </if>
            <if test="salesmanId != null and salesmanId != ''">
                and t2.salesman_id = #{salesmanId}
            </if>
    </select>

  <select id="selectWaitMergeByWaybillIds" resultType="com.ecommerce.logistics.dao.dto.shipbill.ShipBillMergeInfo">
    select lsb.waybill_id as waybillId,
           lsb.waybill_num as waybillNum,
           item.type as deliveryType,
           ldi.delivery_time as deliveryTime,
           ldi.delivery_time_range as deliveryTimeRange,
           ldi.seller_id as sellerId,
           ldi.seller_name as sellerName,
           ldi.buyer_id as buyerId,
           ldi.buyer_name as buyerName,
           ldi.receiver_address_id as unloadId
    from lgs_ship_bill as lsb
    left join lgs_ship_bill_item as item on lsb.waybill_id = item.waybill_id and lsb.max_seq_num = item.seq_num and can_operate = 1
    left join lgs_delivery_info as ldi on item.delivery_info_id = ldi.delivery_info_id
    where lsb.del_flg = 0 and item.del_flg = 0 and ldi.del_flg = 0
    and lsb.waybill_id in
    <foreach collection="waybillIds" item="wit" index="win" open="(" separator="," close=")">
      #{wit}
    </foreach>
  </select>

  <select id="countSubNotStatus" resultType="int">
    select count(waybill_id)
    from lgs_ship_bill
    where parent_id = #{parentId} and waybill_id != #{waybillId} and child_flag = 1 and status != #{status}
  </select>

  <select id="countSubNotFinal" resultType="int">
    select count(waybill_id)
    from lgs_ship_bill
    where parent_id = #{parentId} and child_flag = 1 and status not in ('030600900', '030601000', '030601100')
  </select>

    <select id="queryShipWaybillParentIdListByTakeCodesAndStatusList" resultType="java.lang.String">
        SELECT
            DISTINCT t1.parent_id
        FROM
            lgs_ship_bill AS t1
        LEFT JOIN
            lgs_ship_bill_item AS t2 ON t1.waybill_id = t2.waybill_id
        LEFT JOIN
            lgs_delivery_info AS t3 ON t2.delivery_info_id = t3.delivery_info_id
        WHERE
            t1.del_flg = 0
            AND t2.del_flg = 0
            AND t3.del_flg = 0
            AND t3.take_code IN
            <foreach collection="deliverySheetNumList" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
            AND t1.status IN
            <foreach collection="shipBillStatusList" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
    </select>

    <select id="selectMainWaybillListByParentId" resultType="com.ecommerce.logistics.dao.dto.waybill.MainWaybillDO">
        SELECT
            t1.waybill_id AS waybillId,
            t1.waybill_num AS waybillNum,
            t1.`status` AS status,
            t1.type AS type,
            t1.create_time AS createTime,
            t1.complete_time AS completeTime,
            t3.delivery_time AS deliveryTime,
            t3.delivery_time_range AS deliveryTimeRange,
            t1.sync_flag AS syncFlag,
            t1.carrier_id AS carrierId,
            t1.external_waybill_status AS externalWaybillStatus,
            t1.estimate_carriage AS estimateCarriage,
            t1.estimate_km AS estimateKm,
            t1.estimate_duration AS estimateDuration,
            t1.published_carriage AS publishedCarriage,
            t2.receivable_carriage_price AS actualCarriage,
            t1.driver_id AS driverId,
            t1.driver_name AS driverName,
            t1.driver_phone AS driverPhone,
            IF ( t1.transport_tool_type = '030230100', t1.vehicle_num, t1.shipping_name ) AS vehicleNum,
            t1.arrive_warehouse_time AS arriveWarehouseTime,
            t1.can_monitor AS canMonitor,
            t1.monitor_outer_time AS monitorCompleteTime,
            t2.bill_proxy_type AS billProxyType,
            t2.can_operate AS canOperate
        FROM
            lgs_ship_bill AS t1
        LEFT JOIN
            lgs_ship_bill_item AS t2 ON t1.waybill_id = t2.waybill_id
        LEFT JOIN
            lgs_delivery_info AS t3 ON t2.delivery_info_id = t3.delivery_info_id
        WHERE
            t1.del_flg = 0
            AND (( t2.del_flg = 0 AND t2.can_operate = 1 ) OR t1.parent_flag = 1 )
            AND t1.parent_id IN
            <foreach collection="parentIdList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
    </select>

  <select id="queryPickingListByWaybillNum" resultType="com.ecommerce.logistics.api.dto.pickingbill.PickingBillDTO">
    select info.take_code as deliverySheetNum,
           info.note as productDesc,
           info.product_img as productImg,
           info.goods_id as productId,
           info.special_flag as specialFlag,
           info.vendor as vendor,
           info.transport_category_id as transportCategoryId,
           info.unit as productUnit,
           info.sign_type as signType,
           info.receiver_address_id as receiveAddressId,
           info. location as receiveAddressLocation,
           info.province as receiveProvince,
           info.province_code as receiveProvinceCode,
           info.city as receiveCity,
           info.city_code as receiveCityCode,
           info.district as receiveDistrict,
           info.district_code as receiveDistrictCode,
           info.street as receiveStreet,
           info.street_code as receiveStreetCode,
           info.receiver as receiver,
           info.address as receiveAddress,
           info.receiver_phone as receiverPhone,
           bill.warehouse_id as warehouseId,
           bill.warehouse_name as warehouseName,
           bill.warehouse_address as warehouseAddress,
           info.buyer_id as buyerId,
           info.buyer_name as buyerName,
           info.seller_id as sellerId,
           info.seller_name as sellerName,
           info.delivery_time as deliveryTime,
           info.type as type,
           info.delivery_time_range as deliveryTimeRange,
           info.create_time as createTime,
           info.order_item_id as resourceId,
           info.carriage_unit_price as unitCarriage,
           info.sync_flag as syncFlag,
           info.bill_proxy_type as billProxyType,
           info.can_operate as canOperate
    from lgs_ship_bill as bill
    left join lgs_ship_bill_item as item on bill.waybill_id = item.waybill_id and bill.max_seq_num = item.seq_num
    left join lgs_delivery_info as info on item.delivery_info_id = info.delivery_info_id
    where bill.waybill_num in
    <foreach collection="waybillNumList" index="win" item="wit" open="(" separator="," close=")">
      #{wit}
    </foreach>
    and item.bill_proxy_type != '030400300'
  </select>


  <select id="queryParentFlagById" resultType="java.lang.Byte">
    select parent_flag from lgs_ship_bill where waybill_id = #{waybillId} and del_flg = 0
  </select>

  <select id="queryNoFinalSubShipBillListByParentId" resultMap = "BaseResultMap">
    select *
    from lgs_ship_bill
    where parent_id = #{parentId}
      and child_flag = 1
      and del_flg = 0
      and status not in ('030600900', '030601000', '030601100')
  </select>

  <update id="updateLeaveWarehouseTimeByWaybillId">
    update lgs_ship_bill
    set leave_warehouse_time = #{leaveWarehouseTime}
    where waybill_id = #{waybillId}
  </update>

  <select id="selectSnatchedWaybillList" resultType="com.ecommerce.logistics.api.dto.waybill.SnatchedWaybillDTO">
    SELECT
      lsb.waybill_id as waybillId,
      rec.create_time as snatchTime,
      lsb.vehicle_num as vehicleNum,
      lsb.warehouse_id as warehouseId,
      info.province as receiveProvince,
      info.city as receiveCity,
      info.district as receiveDistrict
    FROM lgs_ship_bill as lsb
    LEFT JOIN lgs_operation_record as rec on lsb.waybill_id = rec.entry_id and rec.type = 1 and rec.operation_type = 4
    LEFT JOIN lgs_ship_bill_item as item on lsb.waybill_id = item.waybill_id
    LEFT JOIN lgs_delivery_info as info on item.delivery_info_id = info.delivery_info_id
    WHERE lsb.type = '030080100' and lsb.vehicle_num != ''
    order by rec.create_time DESC
    LIMIT 2
  </select>

  <select id="selectWaybillForEmall" parameterType="com.ecommerce.logistics.api.dto.EmallPublishWaybillQueryDTO" resultType="com.ecommerce.logistics.api.dto.EmallPublishWaybillListDTO">
    SELECT
      shipBill.waybill_id as waybillId,
      shipBill.waybill_num as waybillNum,
      shipBill.vehicle_num as vehicleNum,
      info.warehouse_province as warehouseProvince,
      info.warehouse_city  as warehouseCity,
      info.warehouse_district  as warehouseDistrict,
      info.province  as receiveProvince,
      info.city  as receiveCity,
      info.district  as district,
      date_format(info.delivery_time, '%Y-%m-%d %H:%i:%s') as deliveryTime,
      info.delivery_time_range as deliveryTimeRange,
      shipBill.assign_quantity as totalQuantity,
      info.unit as productUnit,
      info.note as productDesc,
      info.transport_category_id as transportCategoryId,
      ltc.category_name as transportCategoryName,
      shipBill.published_carriage as publishedCarriage,
      shipBill.estimate_km as estimateKm,
      shipBill.status as status,
      shipBill.create_time as createTime
    FROM
       lgs_ship_bill AS shipBill
    LEFT JOIN
        lgs_ship_bill_item AS item on item.waybill_id = shipBill.waybill_id
    LEFT JOIN
        lgs_delivery_info AS info on info.delivery_info_id = item.delivery_info_id
    LEFT JOIN
        lgs_transport_category ltc on ltc.transport_category_id = info.transport_category_id
    WHERE shipBill.del_flg = 0 AND item.del_flg = 0 AND info.del_flg = 0
    AND item.can_operate = 1
    AND shipBill.parent_id != 1
    <if test="status != null and status != ''">
          AND shipBill.status = #{status}
    </if>
    <if test="type != null and type != ''">
          AND shipBill.type = #{type}
    </if>
    ORDER BY shipBill.create_time DESC
    <if test="pageSize != null ">
        limit #{pageSize}
    </if>
  </select>

  <select id="selectTrackWaybillForNum" resultType="com.ecommerce.logistics.api.dto.waybill.PublishWaybillListDTO">
    select lsb.waybill_num as waybillNum,
           lsb.waybill_id as waybillId,
           lsb.type as type,
           lsb.warehouse_id as warehouseId,
           lsb.warehouse_name as warehouseName,
           lsb.warehouse_province as warehouseProvince,
           lsb.warehouse_city as warehouseCity,
           lsb.warehouse_district as warehouseDistrict,
           info.buyer_name as buyerName,
           info.seller_name as sellerName,
           info.receiver as receiver,
           info.receiver_phone as receiverPhone,
           info.address as receiveAddress,
           info.province_code as receiveProvinceCode,
           info.city_code as receiveCityCode,
           info.district_code as districtCode,
           info.province as receiveProvince,
           info.city as receiveCity,
           info.district as district,
           info.sync_flag as syncFlag,
           lsb.external_waybill_status as externalWaybillStatus,
           lsb.qr_code as qrCode,
           info.sign_type as signType
    from lgs_ship_bill as lsb
    left join lgs_ship_bill_item as item on lsb.waybill_id = item.waybill_id and lsb.max_seq_num = item.seq_num
    left join lgs_delivery_info as info on item.delivery_info_id = info.delivery_info_id
    where lsb.waybill_num = #{waybillNum}
      and item.can_operate = 1
  </select>

  <select id="queryConcreteInfoByWaybillId" resultType="com.ecommerce.logistics.api.dto.shipbill.ConcreteShipBillInfoDTO">
    select lsb.waybill_id as waybillId,
           lsb.waybill_num as waybillNum,
           item.empty_load_flag as emptyLoadFlag,
           lsb.actual_quantity as actualQuantity,
           info.deals_id as dealsId,
           info.take_code as takeCode,
           info.order_item_id as orderItemId,
           info.goods_id as goodsId,
           info.sign_type as signType
    from lgs_ship_bill as lsb
    left join lgs_ship_bill_item as item on lsb.waybill_id = item.waybill_id and lsb.max_seq_num = item.seq_num
    left join lgs_delivery_info as info on item.delivery_info_id = info.delivery_info_id
    where lsb.waybill_id = #{waybillId} and item.can_operate = 1 and info.del_flg = 0
  </select>

  <update id="signConcrete" parameterType="com.ecommerce.logistics.api.dto.waybill.CompleteShipBillDTO">
    update lgs_ship_bill as lsb
    left join lgs_ship_bill_item as item on lsb.waybill_id = item.waybill_id and lsb.max_seq_num = item.seq_num
    set lsb.status = '030600900',
        lsb.update_user = #{operationUserId},
        lsb.sign_quantity = #{signQuantity},
        lsb.complete_quantity = lsb.actual_quantity,
        lsb.complete_time = #{completeTime},
        lsb.update_user = #{operationUserId},
        lsb.update_time = current_timestamp,
        lsb.pump_no = #{pumpNo},
        lsb.pumper_name = #{pumperName}
        <if test="signRemark != null and signRemark != ''">
          ,lsb.sign_remark = #{signRemark}
        </if>
        <if test="arriveDestinationTime != null">
          , lsb.arrive_destination_time = #{arriveDestinationTime}
        </if>
        <if test="actualKm != null">
          , lsb.actual_km = #{actualKm}
        </if>
        <if test="emptyLoadCharge != null">
          ,item.empty_load_charge = #{emptyLoadCharge}
        </if>
        <if test="emptyLoadQuantity != null">
          ,item.empty_load_quantity = #{emptyLoadQuantity}
        </if>
        ,item.status = '030610600'
        ,item.update_user = #{operationUserId}
        ,item.update_time = current_timestamp
    where lsb.waybill_id = #{waybillId}
      and lsb.status = #{waybillOldStatus}
      and item.status = '030610200'
  </update>

  <select id="concreteTotalSignQuantity" resultType="java.math.BigDecimal">
    select sum(ifnull(lsb.sign_quantity, lsb.actual_quantity))
    from lgs_ship_bill as lsb
    left join lgs_ship_bill_item as item on lsb.waybill_id = item.waybill_id and lsb.max_seq_num = item.seq_num
    left join lgs_delivery_info as info on item.delivery_info_id = info.delivery_info_id
    where info.delivery_info_id = #{deliveryInfoId} and lsb.status in ('030600700', '030600800', '030600900')
  </select>

  <select id="concreteTotalSignQuantityByOrderItemId" resultType="java.math.BigDecimal">
    select sum(ifnull(lsb.sign_quantity, lsb.actual_quantity))
    from lgs_ship_bill as lsb
           left join lgs_ship_bill_item as item on lsb.waybill_id = item.waybill_id and lsb.max_seq_num = item.seq_num
           left join lgs_delivery_info as info on item.delivery_info_id = info.delivery_info_id
    where info.order_item_id = #{orderItemId} and lsb.status in ('030600700', '030600800', '030600900')
  </select>

  <update id="updateExternalStatus">
    update lgs_ship_bill
    set external_waybill_status = #{externalWaybillStatus},
        update_time = current_timestamp
    where waybill_id = #{waybillId}
  </update>

  <update id="updateVehicleStatus">
    update lgs_ship_bill as lsb
    left join lgs_vehicle as lv on lsb.vehicle_id = lv.vehicle_id
    set lv.status = #{vehicleStatus}
    <if test="updateUserId != null and updateUserId != ''">
      ,lv.update_user = #{updateUserId}
    </if>
    ,lv.update_time = current_timestamp
    where lsb.waybill_id = #{waybillId}
  </update>

  <select id="countDeliveringWaybill" resultType="java.lang.Integer">
    select count(waybill_id)
    from lgs_ship_bill
    where vehicle_id = #{vehicleId} and status in ('030600700', '030600800') and del_flg = 0
  </select>

  <select id="queryAllSnatchCarrierId" resultType="java.lang.String">
      select
        distinct carrier_id
      from lgs_ship_bill
      where del_flg = 0 and carrier_name = '平台' and carrier_id != 'PLATFORM_ID'
  </select>

  <select id="queryExternalWaybillStatus" resultType="com.ecommerce.logistics.dao.vo.ShipBill">
      SELECT *
      FROM lgs_ship_bill
      WHERE del_flg = 0 AND waybill_id = #{waybillId}
      LIMIT 1
  </select>
  <select id="getAdjustPriceMemberList" parameterType="com.ecommerce.logistics.api.dto.adjust.AdjustQueryDTO" resultType="com.ecommerce.logistics.api.dto.adjust.AdjustMemberDTO">
      SELECT di.seller_id, di.seller_name, di.buyer_id, di.buyer_name, di.deals_name, di.sale_region_path AS sale_region_id, di.goods_id, di.note AS goods_name, di.warehouse_id, di.warehouse_name, SUM(sbi.pay_quantity) AS total_quantity, SUM(sbi.origin_price * sbi.pay_quantity) AS origin_amount, SUM(sbi.goods_price * sbi.pay_quantity) AS newest_amount, di.order_item_id, COUNT(1) AS affect_num
      FROM lgs_delivery_info AS di, lgs_ship_bill_item AS sbi, lgs_ship_bill AS sb
      WHERE sbi.delivery_info_id = di.delivery_info_id
      AND sb.waybill_id = sbi.waybill_id
      AND sbi.status IN('030610200', '030610600')
      AND sb.leave_warehouse_time &gt;= #{shipStartTime}
      AND sb.leave_warehouse_time &lt;= #{shipEndTime}
      AND di.seller_id = #{sellerId}
      AND di.transport_category_id IN( '3eur6trcwaukfsafcaqxu84wb', '2eur6trcwaukfsafcaqxu84wb')
      AND sbi.adjust_status = 2
      <if test="transportType != null and transportType != ''">
          AND di.transport_tool_type = #{transportType}
      </if>
      <if test="transportTypes != null and transportTypes.size() > 0">
          AND di.transport_tool_type IN<foreach collection="transportTypes" item="id" open="(" close=")" separator=",">#{id}</foreach>
      </if>
      <if test="deliveryType != null and deliveryType != ''">
          AND di.type = #{deliveryType}
      </if>
      <if test="deliveryTypes != null and deliveryTypes.size() > 0">
          AND di.type IN<foreach collection="deliveryTypes" item="id" open="(" close=")" separator=",">#{id}</foreach>
      </if>
      <if test="billType != null">
          AND di.bill_type = #{billType}
      </if>
      <if test="buyerIds != null and buyerIds.size() > 0">
          AND di.buyer_id IN<foreach collection="buyerIds" item="buyerId" open="(" close=")" separator=", ">#{buyerId}</foreach>
      </if>

      <if test="goodsIds != null and goodsIds.size() > 0">
          AND di.goods_id IN<foreach collection="goodsIds" item="goodsId" open="(" close=")" separator=", ">#{goodsId}</foreach>
      </if>
      <if test="saleRegionIds != null and saleRegionIds.size() > 0">
          AND di.sale_region_path IN<foreach collection="saleRegionIds" item="saleRegionId" open="(" close=")" separator=", ">#{saleRegionId}</foreach>
      </if>
      <if test="warehouseList != null and warehouseList.size > 0">
          AND di.warehouse_id IN<foreach collection="warehouseList" item="warehouse" open="(" close=")" separator=", ">#{warehouse}</foreach>
      </if>
      <if test="excludeBuyerIds != null and excludeBuyerIds.size > 0">
          AND di.buyer_id NOT IN<foreach collection="excludeBuyerIds" item="buyerId" open="(" close=")" separator=", ">#{buyerId}</foreach>
      </if>
      <if test="waybillNums != null and waybillNums.size > 0">
          AND sbi.waybill_num IN<foreach collection="waybillNums" item="waybillNum" open="(" close=")" separator=", ">#{waybillNum}</foreach>
      </if>
      GROUP BY di.buyer_id, di.deals_name, di.sale_region_path, di.warehouse_id, di.goods_id
  </select>

    <select id="getAdjustPriceShipBillList" parameterType="com.ecommerce.logistics.api.dto.adjust.AdjustQueryDTO" resultType="com.ecommerce.logistics.api.dto.adjust.AdjustShipBillDTO">
        SELECT sbi.waybill_item_id, sbi.waybill_num, sb.external_waybill_num, di.seller_id, di.seller_name, di.buyer_id, di.buyer_name, di.deals_name, di.sale_region_path AS sale_region_id, di.goods_id, di.note AS goods_name, di.warehouse_id, di.warehouse_name, sbi.pay_quantity AS total_quantity, sbi.origin_price, sbi.origin_price * sbi.pay_quantity AS origin_amount, sbi.goods_price AS newest_price, sbi.goods_price * sbi.pay_quantity AS newest_amount, sbi.receivable_carriage_price AS logistics_price, di.order_item_id, di.type AS delivery_type, di.transport_tool_type AS transport_type, di.mdm_code, di.bill_type, sbi.adjust_num, sb.leave_warehouse_time, sb.complete_time
        FROM lgs_delivery_info AS di, lgs_ship_bill_item AS sbi, lgs_ship_bill AS sb
        WHERE sbi.delivery_info_id = di.delivery_info_id
        AND sb.waybill_id = sbi.waybill_id
        AND sbi.status IN('030610200', '030610600')
        AND sb.leave_warehouse_time &gt;= #{shipStartTime}
        AND sb.leave_warehouse_time &lt;= #{shipEndTime}
        AND di.seller_id = #{sellerId}
        AND di.transport_category_id IN( '3eur6trcwaukfsafcaqxu84wb', '2eur6trcwaukfsafcaqxu84wb')
        AND sbi.adjust_status = 2
        <if test="transportType != null and transportType != ''">
            AND di.transport_tool_type = #{transportType}
        </if>
        <if test="transportTypes != null and transportTypes.size() > 0">
            AND di.transport_tool_type IN<foreach collection="transportTypes" item="id" open="(" close=")" separator=",">#{id}</foreach>
        </if>
        <if test="deliveryType != null and deliveryType != ''">
            AND di.type = #{deliveryType}
        </if>
        <if test="deliveryTypes != null and deliveryTypes.size() > 0">
            AND di.type IN<foreach collection="deliveryTypes" item="id" open="(" close=")" separator=",">#{id}</foreach>
        </if>
        <if test="billType != null">
            AND di.bill_type = #{billType}
        </if>
        <if test="buyerIds != null and buyerIds.size() > 0">
            AND di.buyer_id IN<foreach collection="buyerIds" item="buyerId" open="(" close=")" separator=", ">#{buyerId}</foreach>
        </if>

        <if test="goodsIds != null and goodsIds.size() > 0">
            AND di.goods_id IN<foreach collection="goodsIds" item="goodsId" open="(" close=")" separator=", ">#{goodsId}</foreach>
        </if>
        <if test="saleRegionIds != null and saleRegionIds.size() > 0">
            AND di.sale_region_path IN<foreach collection="saleRegionIds" item="saleRegionId" open="(" close=")" separator=", ">#{saleRegionId}</foreach>
        </if>
        <if test="warehouseList != null and warehouseList.size > 0">
            AND di.warehouse_id IN<foreach collection="warehouseList" item="warehouse" open="(" close=")" separator=", ">#{warehouse}</foreach>
        </if>
        <if test="waybillNums != null and waybillNums.size > 0">
            AND sbi.waybill_num IN<foreach collection="waybillNums" item="waybillNum" open="(" close=")" separator=", ">#{waybillNum}</foreach>
        </if>
        ORDER BY sb.leave_warehouse_time DESC
    </select>

  <select id="queryWaybillNumByVehicleIdAndStatus" resultType="java.lang.String">
    select waybill_num
    from lgs_ship_bill
    where vehicle_id = #{vehicleId}
      and status in
    <foreach collection="statusList" item="sit" index="sin" open="(" separator="," close=")">
      #{sit}
    </foreach>
    and del_flg = 0
    order by waybill_num
  </select>

  <select id="queryContractInfo" resultType="com.ecommerce.logistics.api.dto.waybill.ContractSubmitInfoDTO">
      select
        bill.driver_name as carrier,
        bill.create_time as dateSigned,
        bill.complete_quantity as cargoFreight,
        bill.warehouse_province as placeOfLoadingProvince,
        bill.warehouse_city as placeOfLoadingCity,
        bill.warehouse_district as placeOfLoadingDistrict,
        info.province as placeOfReceiptProvince,
        info.city as placeOfReceiptCity,
        info.district as placeOfReceiptDistrict,
        ROUND(bill.actual_km,3) as transportDistance,
        ROUND(bill.published_carriage,3) as contractAmount
      from lgs_ship_bill bill
      left join lgs_ship_bill_item item on item.waybill_id = bill.waybill_id
      left join lgs_delivery_info info on info.delivery_info_id = item.delivery_info_id
      where bill.del_flg = 0
      and item.bill_proxy_type != '030400300'
      and bill.waybill_num = #{waybillNum}
  </select>

  <update id="updateLubricity">
    update lgs_ship_bill
    set lubricity_quantity = #{lubricityQuantity},
        lubricity_price = #{lubricityPrice},
        update_time = current_timestamp
    where waybill_id = #{waybillId}
  </update>


    <update id="refundShipBill" parameterType="com.ecommerce.logistics.api.dto.waybill.RefundShipBillDTO">
        update lgs_ship_bill
        set
        update_user = #{operationUserId},
        <if test="status != null and status != ''">
            status = #{status},
        </if>
        <if test="refundStatus != null and refundStatus != ''">
            refund_status = #{refundStatus},
        </if>
        <if test="externalWaybillStatus != null and externalWaybillStatus != ''">
            external_waybill_status = #{externalWaybillStatus},
        </if>
        <if test="refundReason != null and refundReason != ''">
            refund_reason = #{refundReason},
        </if>
        <if test="refundRemark != null and refundRemark != ''">
            refund_remark = #{refundRemark},
        </if>
        <if test="refundQuantity != null">
            complete_quantity = #{refundQuantity},
        </if>
        <if test="actualKm != null">
            actual_km = #{actualKm},
        </if>
        <if test="completeTime != null">
            complete_time = #{completeTime},
        </if>
        update_time = NOW()
        where del_flg = 0
        <if test="waybillId != null and waybillId != ''">
            AND waybill_id = #{waybillId}
        </if>
        <if test="waybillNum != null and waybillNum != ''">
            AND waybill_num = #{waybillNum}
        </if>
        <if test="externalWaybillNum != null and externalWaybillNum != ''">
            AND external_waybill_num = #{externalWaybillNum}
        </if>
    </update>

  <update id="updateQuantityForConcrete">
    update lgs_ship_bill as lsb
    left join lgs_ship_bill_item as item on lsb.waybill_id = item.waybill_id
    set lsb.assign_quantity = #{quantity},
        lsb.actual_quantity = #{quantity},
        lsb.update_time = current_timestamp,
        lsb.update_user = #{updateUser},
        item.pay_quantity = #{quantity},
        item.update_time = current_timestamp,
        item.update_user = #{updateUser}
    where lsb.waybill_id = #{waybillId}
  </update>

    <select id="queryShipBillInfoByNum" resultType="com.ecommerce.logistics.dao.vo.ShipBill">
        SELECT *
        FROM lgs_ship_bill
        WHERE
        del_flg = 0
        AND waybill_num =  #{waybillNum}
        AND external_waybill_num = #{externalWaybillNum}
        LIMIT 1
    </select>

  <select id="queryLastSignWaybill" parameterType="com.ecommerce.logistics.api.dto.LastSignWaybillQueryDTO"
          resultType="com.ecommerce.logistics.api.dto.WaybillBriefDTO">
    select bill.waybill_id as waybillId,
           bill.waybill_num as waybillNum,
           bill.external_waybill_num as externalWaybillNum
    from lgs_delivery_info as info
    left join lgs_ship_bill_item as item on info.delivery_info_id = item.delivery_info_id
    left join lgs_ship_bill as bill on item.waybill_id = bill.waybill_id and item.seq_num = bill.max_seq_num
    where info.order_item_id = #{orderItemId} and info.del_flg = 0 and item.del_flg = 0 and bill.del_flg = 0
    order by bill.complete_time desc
    limit 1
  </select>

    <select id="queryGoodsNameListDropDownBox" parameterType="com.ecommerce.logistics.api.dto.waybill.QueryGoodsDTO"
            resultType="com.ecommerce.logistics.api.dto.GoodsInfoDTO">
        SELECT t3.goods_id AS goodsId,t3.note AS goodsName
        FROM lgs_ship_bill t1
        LEFT JOIN lgs_ship_bill_item t2 ON t1.waybill_id = t2.waybill_id
        LEFT JOIN lgs_delivery_info t3 ON t2.delivery_info_id = t3.delivery_info_id
        WHERE  t1.del_flg = 0 AND t2.del_flg = 0 AND t3.del_flg = 0
        <if test="buyerId != null and buyerId != '' ">
            AND t3.buyer_id = #{buyerId}
        </if>
        <if test="sellerId != null and sellerId != '' ">
            AND t3.seller_id = #{sellerId}
        </if>
        <if test="carrierId != null and carrierId != '' ">
            AND t1.carrier_id = #{carrierId}
        </if>
        <if test="goodsName != null and goodsName != '' ">
            AND t3.note LIKE CONCAT('%', #{goodsName}, '%')
        </if>
        group by t3.goods_id
    </select>

	<select id="queryVehicleOrShippingListByName" parameterType="com.ecommerce.logistics.api.dto.shipbill.ShipBillQueryDTO" resultType="java.lang.String">
		SELECT
			<![CDATA[ IF( t1.vehicle_num <> '',t1.vehicle_num,t1.shipping_name ) AS transportToolName ]]>
		FROM
			lgs_ship_bill t1
			LEFT JOIN lgs_ship_bill_item t2 ON t1.waybill_id = t2.waybill_id
			LEFT JOIN lgs_delivery_info t3 ON t2.delivery_info_id = t3.delivery_info_id
		WHERE
			t1.del_flg = 0
			AND t2.del_flg = 0
			AND t3.del_flg = 0
			<if test="buyerId != null and buyerId != '' ">
	            AND t3.buyer_id = #{buyerId}
	        </if>
	        <if test="sellerId != null and sellerId != '' ">
	            AND t3.seller_id = #{sellerId}
	        </if>
	        <if test="carrierId != null and carrierId != '' ">
	            AND t1.carrier_id = #{carrierId}
	        </if>
			<if test="vehicleNum != null and vehicleNum != '' ">
	            AND (t1.vehicle_num LIKE CONCAT('%', #{vehicleNum}, '%') OR t1.shipping_name LIKE CONCAT('%', #{vehicleNum}, '%'))
	        </if>
	        group by transportToolName
	</select>
	<select id="queryShipBillByDeliveryBillIds" parameterType="com.ecommerce.logistics.api.dto.shipbill.ShipBillQueryDTO" resultType="com.ecommerce.logistics.api.dto.LogisticsAdjustPriceItemDTO">
		SELECT
			t1.waybill_id AS waybillId,
			t2.waybill_item_id AS waybillItemId,
			t2.waybill_num AS waybillNum,
			IFNULL(t1.complete_quantity,0) AS actualQuantity,
			t1.complete_time AS completeTime,
			t1.leave_warehouse_time AS leaveWarehouseTime,
			t1.factory_number AS mdmCode,
		    t1.external_waybill_num AS externalWaybillNum
		FROM
			lgs_ship_bill t1,
			lgs_ship_bill_item t2
		WHERE
			t1.del_flg = 0
            AND t1.waybill_id = t2.waybill_id
			AND t2.type = '030060200'
			<![CDATA[ AND t1.leave_warehouse_time >= #{beginCompleteTime}  ]]>
			<![CDATA[ AND t1.leave_warehouse_time <= #{endCompleteTime} ]]>
			AND t2.delivery_bill_id IN
			<foreach collection="deliveryBillIdList" item="mit" open="(" separator="," close=")">
              #{mit}
          	</foreach>

	</select>

  <select id="queryWDQrCodeByOrderCode" resultType="com.ecommerce.logistics.api.dto.shipbill.QrCodeDTO">
    select ldi.order_code as orderCode,
           lsb.waybill_num as waybillNum,
           lsb.external_waybill_num as externalWaybillNum,
           lsb.qr_code as qrCode,
           concat_ws('', lsb.vehicle_num, lsb.shipping_name) as vehicleNum,
           lsb.assign_quantity as totalQuantity,
           ldi.buyer_id as buyerId,
           ldi.buyer_name as buyerName,
           ldi.seller_id as sellerId,
           ldi.seller_name as sellerName,
           ldi.unit as productUnit,
           ldi.note as goodsName,
           lsb.warehouse_name as warehouseName,
           lsb.delivery_time as deliveryTime
    from lgs_delivery_info as ldi
    left join lgs_ship_bill_item as item on ldi.delivery_info_id = item.delivery_info_id
    left join lgs_ship_bill as lsb on item.waybill_id = lsb.waybill_id
    where ldi.order_code = #{orderCode} and lsb.status = '030600600' and lsb.qr_code != ''
    order by lsb.vehicle_num, lsb.create_time desc
  </select>


  <update id="updateCarrierInfo">
    update lgs_ship_bill
    set carrier_id = #{carrierId},
        carrier_name = #{carrierName}
    <if test="internal != null and internal != '' ">
      ,internal = #{internal}
    </if>
    where waybill_id = #{waybillId} AND del_flg = 0
  </update>

  <select id="queryTradeWaybillBri" parameterType="com.ecommerce.logistics.api.dto.shipbill.ShipBillBriCondDTO"
          resultType="com.ecommerce.logistics.api.dto.shipbill.ShipBillBriResDTO">
    select lsb.waybill_num as waybillNum,
           info.buyer_id as buyerId,
           info.buyer_name as buyerName,
           info.warehouse_name as warehouseName,
           info.address as receiverAddress,
           info.goods_id as goodsId,
           info.note as goodsName
    from lgs_ship_bill as lsb
    left join lgs_ship_bill_item as item on lsb.waybill_id = item.waybill_id and lsb.max_seq_num = item.seq_num
    left join lgs_delivery_info as info on item.delivery_info_id = info.delivery_info_id
    where lsb.waybill_num = #{waybillNum}
    <if test="sellerId != null and sellerId != ''">
      and info.seller_id = #{sellerId}
    </if>
    <if test="buyerId != null and buyerId != ''">
      and info.buyer_id = #{buyerId}
    </if>
    order by info.bill_proxy_type desc
    limit 1
  </select>

  <select id="queryCarryWaybillBri" parameterType="com.ecommerce.logistics.api.dto.shipbill.ShipBillBriCondDTO"
          resultType="com.ecommerce.logistics.api.dto.shipbill.ShipBillBriResDTO">
    select lsb.waybill_num as waybillNum,
           info.buyer_id as buyerId,
           info.buyer_name as buyerName,
           info.warehouse_name as warehouseName,
           info.address as receiverAddress,
           info.goods_id as goodsId,
           info.note as goodsName
    from lgs_ship_bill as lsb
    left join lgs_ship_bill_item as item on lsb.waybill_id = item.waybill_id and lsb.max_seq_num = item.seq_num
    left join lgs_delivery_bill as ldb on item.real_delivery_bill_num = ldb.delivery_bill_num
    left join lgs_delivery_info as info on ldb.delivery_info_id = info.delivery_info_id
    where lsb.waybill_num = #{waybillNum} and ldb.carrier_id = #{carrierId}
  </select>
    <select id="countShipBillByDeliveryBillIds" resultType="java.lang.Integer">
      SELECT
        COUNT(*)
      FROM
      lgs_ship_bill t1,
      lgs_ship_bill_item t2
      WHERE
      t1.del_flg = 0
      AND t2.type = '030060200'
      <![CDATA[ AND t1.leave_warehouse_time >= #{beginCompleteTime}  ]]>
      <![CDATA[ AND t1.leave_warehouse_time <= #{endCompleteTime} ]]>
      AND t2.delivery_bill_id IN
      <foreach collection="deliveryBillIdList" item="mit" open="(" separator="," close=")">
        #{mit}
      </foreach>

    </select>


</mapper>

