<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.ShippingAuditLogMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.ShippingAuditLog">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="shipping_audit_id" jdbcType="BIGINT" property="shippingAuditId" />
    <result column="shipping_id" jdbcType="VARCHAR" property="shippingId" />
    <result column="audit_status" jdbcType="INTEGER" property="auditStatus" />
    <result column="rejection_reasons" jdbcType="VARCHAR" property="rejectionReasons" />
    <result column="del_flg" jdbcType="INTEGER" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <select id="selectAuditLogByShippingId" resultType="com.ecommerce.logistics.api.dto.shipping.ShippingAuditLogDTO">
    SELECT
        shipping_audit_id AS shippingAuditId,
        shipping_id AS shippingId,
        audit_status AS auditStatus,
        rejection_reasons AS rejectionReasons,
        create_user AS createUser,
        create_user_name AS createUserName,
        create_time AS createTime
    FROM lgs_shipping_audit_log
    WHERE del_flg = 0 AND shipping_id = #{shippingId}
    ORDER BY create_time desc
  </select>


</mapper>