<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.ShippingFarePaymentMapper">
    <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.ShippingFarePayment">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="shipping_fare_payment_id" jdbcType="BIGINT" property="shippingFarePaymentId"/>
        <result column="shipping_route_id" jdbcType="VARCHAR" property="shippingRouteId"/>
        <result column="auxiliary_price_no" jdbcType="VARCHAR" property="auxiliaryPriceNo"/>
        <result column="route_tariff_type" jdbcType="INTEGER" property="routeTariffType"/>
        <result column="ship_type" jdbcType="VARCHAR" property="shipType"/>
        <result column="distinguish" jdbcType="INTEGER" property="distinguish"/>
        <result column="price" jdbcType="DECIMAL" property="price"/>
        <result column="start_date" jdbcType="DATE" property="startDate"/>
        <result column="end_date" jdbcType="DATE" property="endDate"/>
        <result column="del_flg" jdbcType="INTEGER" property="delFlg"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into lgs_shipping_fare_payment (
        shipping_route_id,
        auxiliary_price_no,
        route_tariff_type,
        ship_type,
        distinguish,
        price,
        start_date,
        end_date,
        create_user,
        create_time
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.shippingRouteId},
            #{item.auxiliaryPriceNo},
            #{item.routeTariffType},
            #{item.shipType},
            #{item.distinguish},
            #{item.price},
            #{item.startDate},
            #{item.endDate},
            #{item.createUser},
            #{item.createTime}
            )
        </foreach>
    </insert>

    <update id="updateShippingFarePayment" parameterType="com.ecommerce.logistics.dao.vo.ShippingFarePayment">
        UPDATE lgs_shipping_fare_payment
        SET
            update_time = current_timestamp, auxiliary_price_no = #{auxiliaryPriceNo}
        <if test="updateUser != null and updateUser != ''">
            ,update_user = #{updateUser}
        </if>
        <if test="shipType != null and shipType !=''">
            ,ship_type = #{shipType}
        </if>
        <if test="distinguish != null">
            , distinguish = #{distinguish}
        </if>
        <if test="price != null">
            ,price = #{price}
        </if>
        <if test="startDate != null">
            , start_date = #{startDate}
        </if>
        <if test="endDate != null">
            , end_date = #{endDate}
        </if>
        WHERE
        del_flg = 0
        AND shipping_route_id = #{shippingRouteId}
        AND shipping_fare_payment_id = #{shippingFarePaymentId}
    </update>


    <select id="getShippingFarePayment" resultType="com.ecommerce.logistics.api.dto.shippingroute.ShippingUnitFareDTO">
        SELECT
            t1.shipping_fare_payment_id AS shippingFarePaymentId,
            t1.shipping_route_id AS shippingRouteId,
            t1.auxiliary_price_no AS auxiliaryPriceNo,
            t1.route_tariff_type AS routeTariffType,
            t1.ship_type AS shipType,
            t1.distinguish AS distinguish,
            t1.price AS price,
            t1.start_date AS startDate,
            t1.end_date AS endDate,
            t2.auxiliary_price_name AS auxiliaryPriceName
        FROM lgs_shipping_fare_payment t1
        LEFT JOIN lgs_auxiliary_price_info t2 ON t1.auxiliary_price_no = t2.auxiliary_price_no AND t2.del_flg = 0
        WHERE t1.del_flg = 0
        AND t1.shipping_route_id = #{shippingRouteId}
        GROUP BY t1.shipping_fare_payment_id
    </select>


    <update id="updateAuxiliaryPriceNo" parameterType="com.ecommerce.logistics.dao.vo.ShippingFarePayment">
        UPDATE lgs_shipping_fare_payment
        SET
        update_time = current_timestamp, auxiliary_price_no = #{auxiliaryPriceNo}
        <if test="updateUser != null and updateUser != ''">
            ,update_user = #{updateUser}
        </if>
        <if test="distinguish != null">
            , distinguish = #{distinguish}
        </if>
        WHERE
        del_flg = 0
        AND shipping_route_id = #{shippingRouteId}
        AND route_tariff_type = #{routeTariffType}
        AND ship_type = #{shipType}
    </update>


</mapper>