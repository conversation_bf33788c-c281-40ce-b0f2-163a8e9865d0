<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.ShippingInfoMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.ShippingInfo">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="shipping_id" jdbcType="VARCHAR" property="shippingId" />
    <result column="shipping_no" jdbcType="VARCHAR" property="shippingNo" />
    <result column="shipping_name" jdbcType="VARCHAR" property="shippingName" />
    <result column="manager_member_type" jdbcType="VARCHAR" property="managerMemberType" />
    <result column="manager_member_id" jdbcType="VARCHAR" property="managerMemberId" />
    <result column="manager_member_name" jdbcType="VARCHAR" property="managerMemberName" />
    <result column="shipping_type" jdbcType="VARCHAR" property="shippingType" />
    <result column="belonger_name" jdbcType="VARCHAR" property="belongerName" />
    <result column="belonger_phone" jdbcType="VARCHAR" property="belongerPhone" />
    <result column="shipping_company" jdbcType="VARCHAR" property="shippingCompany" />
    <result column="ownership" jdbcType="VARCHAR" property="ownership" />
    <result column="departure_wharf_id" jdbcType="VARCHAR" property="departureWharfId" />
    <result column="destination_wharf_id" jdbcType="VARCHAR" property="destinationWharfId" />
    <result column="alevel_payload" jdbcType="DECIMAL" property="alevelPayload" />
    <result column="blevel_payload" jdbcType="DECIMAL" property="blevelPayload" />
    <result column="self_payload" jdbcType="DECIMAL" property="selfPayload" />
    <result column="total_payload" jdbcType="DECIMAL" property="totalPayload" />
    <result column="total_length" jdbcType="DECIMAL" property="totalLength" />
    <result column="max_width" jdbcType="DECIMAL" property="maxWidth" />
    <result column="moulded_depth" jdbcType="DECIMAL" property="mouldedDepth" />
    <result column="full_draft" jdbcType="DECIMAL" property="fullDraft" />
    <result column="empty_draft" jdbcType="DECIMAL" property="emptyDraft" />
    <result column="max_loading_capacity" jdbcType="DECIMAL" property="maxLoadingCapacity" />
    <result column="max_height" jdbcType="DECIMAL" property="maxHeight" />
    <result column="whether_control" jdbcType="INTEGER" property="whetherControl" />
    <result column="gps_device" jdbcType="INTEGER" property="gpsDevice" />
    <result column="gps_device_number" jdbcType="VARCHAR" property="gpsDeviceNumber" />
    <result column="captain_account_id" jdbcType="VARCHAR" property="captainAccountId" />
    <result column="captain_name" jdbcType="VARCHAR" property="captainName" />
    <result column="captain_phone" jdbcType="VARCHAR" property="captainPhone" />
    <result column="shipping_status" jdbcType="INTEGER" property="shippingStatus" />
    <result column="audit_status" jdbcType="INTEGER" property="auditStatus" />
    <result column="rejection_reasons" jdbcType="VARCHAR" property="rejectionReasons" />
    <result column="version" jdbcType="BIGINT" property="version" />
    <result column="del_flg" jdbcType="INTEGER" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>


  <select id="queryShippingInfoListByPlatform" parameterType="com.ecommerce.logistics.api.dto.shipping.ShippingInfoListQueryDTO"
          resultType="com.ecommerce.logistics.api.dto.shipping.ShippingInfoListDTO">
    SELECT
      t1.shipping_id AS shippingId,
      t1.shipping_no AS shippingNo,
      t1.shipping_name AS shippingName,
      t1.shipping_type AS shippingType,
      t1.shipping_company AS shippingCompany,
      t1.manager_member_name AS managerMemberName,
      t1.manager_member_type AS managerMemberType,
      t1.blevel_payload AS blevelPayload,
      t1.ownership AS ownership,
      t1.captain_account_id AS captainId,
      t1.captain_name AS captainName,
      t1.captain_phone AS captainPhone,
      t1.shipping_status AS shippingStatus,
      t1.audit_status AS auditStatus,
      t1.rejection_reasons AS rejectionReasons,
      t1.max_loading_capacity AS maxLoadingCapacity
    FROM lgs_shipping_info t1
    WHERE t1.del_flg = 0
    <if test="shippingCompany != null and shippingCompany != ''">
      AND t1.shipping_company like concat('%', #{shippingCompany}, '%')
    </if>
    <if test="shippingName != null and shippingName != ''">
      AND t1.shipping_name like concat('%', #{shippingName}, '%')
    </if>
    <if test="shippingType != null and shippingType != ''">
      AND t1.shipping_type = #{shippingType}
    </if>
    <if test="ownership != null and ownership != ''">
      AND t1.ownership = #{ownership}
    </if>
    <choose>
      <when test="auditStatus != null and auditStatus != ''">
        AND t1.audit_status = #{auditStatus}
      </when>
      <otherwise>
        AND t1.audit_status IN (2,3,4,5,6)
      </otherwise>
    </choose>
    <if test="blevelPayloadMin != null">
      <![CDATA[ AND t1.blevel_payload >= #{blevelPayloadMin} ]]>
    </if>
    <if test="blevelPayloadMax != null">
      <![CDATA[ AND t1.blevel_payload <= #{blevelPayloadMax} ]]>
    </if>
    ORDER BY t1.update_time desc
  </select>


  <select id="queryShippingInfoListByCarrier" parameterType="com.ecommerce.logistics.api.dto.shipping.ShippingInfoListQueryDTO"
          resultType="com.ecommerce.logistics.api.dto.shipping.ShippingInfoListDTO">
    SELECT
      t1.shipping_id AS shippingId,
      t1.shipping_no AS shippingNo,
      t1.shipping_name AS shippingName,
      t1.shipping_type AS shippingType,
      t1.shipping_company AS shippingCompany,
      t1.manager_member_id AS managerMemberId,
      t1.manager_member_name AS managerMemberName,
      t1.alevel_payload AS alevelPayload,
      t1.blevel_payload AS blevelPayload,
      t1.ownership AS ownership,
      t1.captain_account_id AS captainId,
      t1.captain_name AS captainName,
      t1.captain_phone AS captainPhone,
      t1.shipping_status AS shippingStatus,
      t1.audit_status AS auditStatus,
      t1.rejection_reasons AS rejectionReasons,
      IFNULL(t3.total,0) AS authorizeTotal,
      IF(t4.bindTotal,1,0) AS bindTotal,
      t1.max_loading_capacity AS maxLoadingCapacity
    FROM lgs_shipping_info t1
    LEFT JOIN (SELECT t2.shipping_id, COUNT(*) total from lgs_authorized_seller_map t2
      WHERE t2.del_flg = 0 AND t2.authorize_status = 1 GROUP BY t2.shipping_id) t3 ON t1.shipping_id = t3.shipping_id
    LEFT JOIN (SELECT ec_shipping_id, COUNT(*) bindTotal FROM lgs_erp_shipping_map WHERE del_flg = 0
      GROUP BY ec_shipping_id) t4 ON t4.ec_shipping_id = t1.shipping_id
    WHERE t1.del_flg = 0
    <if test="shippingNo != null and shippingNo != ''">
      AND t1.shipping_no LIKE CONCAT('%',#{shippingNo},'%')
    </if>
    <if test="memberId != null and memberId != ''">
      AND t1.manager_member_id = #{memberId}
    </if>
    <if test="managerMemberType != null and managerMemberType != ''">
      AND t1.manager_member_type = #{managerMemberType}
    </if>
    <if test="shippingCompany != null and shippingCompany != ''">
      AND t1.shipping_company = #{shippingCompany}
    </if>
    <if test="shippingType != null and shippingType != ''">
      AND t1.shipping_type = #{shippingType}
    </if>
    <if test="ownership != null and ownership != ''">
      AND t1.ownership = #{ownership}
    </if>
    <if test="shippingStatus != null and shippingStatus != ''">
      AND t1.shipping_status = #{shippingStatus}
    </if>
    <if test="shippingName != null and shippingName != ''">
      AND t1.shipping_name LIKE CONCAT('%',#{shippingName},'%')
    </if>
    <if test="auditStatus != null and auditStatus != ''">
      AND t1.audit_status = #{auditStatus}
    </if>
    <if test="auditStatusList != null and auditStatusList.size() > 0">
      AND t1.audit_status IN
      <foreach close=")" collection="auditStatusList" index="index" item="item" open="(" separator=",">
        #{item}
      </foreach>
    </if>
    <if test="blevelPayloadMin != null">
      <![CDATA[ AND t1.blevel_payload >= #{blevelPayloadMin} ]]>
    </if>
    <if test="blevelPayloadMax != null">
      <![CDATA[ AND t1.blevel_payload <= #{blevelPayloadMax} ]]>
    </if>
    <!--不等于0，则只查询出绑定了船长的船舶-->
    <if test="hasBindCaptain != null and hasBindCaptain != 0">
      <![CDATA[ AND t1.captain_account_id <> '' ]]>
    </if>
    ORDER BY t1.update_time desc
  </select>


  <select id="queryShippingInfoListBySeller" parameterType="com.ecommerce.logistics.api.dto.shipping.ShippingInfoListQueryDTO"
          resultType="com.ecommerce.logistics.api.dto.shipping.ShippingInfoListDTO">
    SELECT * FROM (
      SELECT
        t1.shipping_id AS shippingId,
        t1.shipping_no AS shippingNo,
        t1.shipping_name AS shippingName,
        t1.shipping_type AS shippingType,
        t1.shipping_company AS shippingCompany,
        t1.manager_member_id AS managerMemberId,
        t1.manager_member_name AS managerMemberName,
        t1.alevel_payload AS alevelPayload,
        t1.blevel_payload AS blevelPayload,
        t1.max_loading_capacity AS maxLoadingCapacity,
        t1.captain_account_id AS captainId,
        t1.captain_name AS captainName,
        t1.captain_phone AS captainPhone,
        t1.shipping_status AS shippingStatus,
        t1.audit_status AS auditStatus,
        t1.rejection_reasons AS rejectionReasons,
        t2.service_type AS serviceType,
        IF( t2.service_type,t1.manager_member_name, '-') AS carrierName,
        t1.update_time AS updateTime,
        t1.ownership AS ownership,
        t4.erp_carrier_no AS erpCarrierNo,
        t4.erp_carrier_name AS erpCarrierName,
        t4.erp_shipping_code AS erpShippingCode,
        t4.erp_shipping_name AS erpShippingName,
        IF(t4.bindTotal,1,0) AS bindTotal
      FROM lgs_shipping_info t1
      LEFT JOIN lgs_authorized_seller_map t2 ON t1.shipping_id = t2.shipping_id AND t2.del_flg = 0 AND t2.authorize_status = 1
      LEFT JOIN ( SELECT erp_carrier_no, erp_carrier_name, erp_shipping_code, erp_shipping_name,ec_shipping_id, COUNT(*) bindTotal
          FROM lgs_erp_shipping_map WHERE del_flg = 0 GROUP BY ec_shipping_id ) t4 ON t4.ec_shipping_id = t1.shipping_id
      WHERE t1.del_flg = 0
      <if test="memberId != null and memberId != ''">
        AND t1.manager_member_id = #{memberId}
      </if>
      <if test="managerMemberType != null and managerMemberType != ''">
        AND t1.manager_member_type = #{managerMemberType}
      </if>
      OR (t2.authorize_user_id = #{memberId} AND t1.del_flg = 0)
    ) t3
    WHERE 1=1
    <if test="shippingCompany != null and shippingCompany != ''">
      AND t3.shippingCompany = #{shippingCompany}
    </if>
    <if test="shippingType != null and shippingType != ''">
      AND t3.shippingType = #{shippingType}
    </if>
    <if test="ownership != null and ownership != ''">
      AND t3.ownership = #{ownership}
    </if>
    <if test="shippingStatus != null and shippingStatus != ''">
      AND t3.shippingStatus = #{shippingStatus}
    </if>
    <if test="shippingName != null and shippingName != ''">
      AND t3.shippingName LIKE concat('%', #{shippingName}, '%')
    </if>
    <if test="auditStatus != null and auditStatus != ''">
      AND t3.auditStatus = #{auditStatus}
    </if>
    <if test="auditStatusList != null and auditStatusList.size() > 0">
      AND t3.auditStatus IN
      <foreach close=")" collection="auditStatusList" index="index" item="item" open="(" separator=",">
        #{item}
      </foreach>
    </if>
    <if test="blevelPayloadMin != null">
      <![CDATA[ AND t1.blevel_payload >= #{blevelPayloadMin} ]]>
    </if>
    <if test="blevelPayloadMax != null">
      <![CDATA[ AND t1.blevel_payload <= #{blevelPayloadMax} ]]>
    </if>

    ORDER BY t3.updateTime desc
  </select>


  <select id="queryShippingInfoListBySelf" parameterType="com.ecommerce.logistics.api.dto.shipping.ShippingInfoQueryDTO"
          resultType="com.ecommerce.logistics.api.dto.shipping.ShippingInfoDTO">
    SELECT
      t1.shipping_id AS shippingId,
      t1.shipping_no AS shippingNo,
      t1.shipping_name AS shippingName,
      t1.shipping_type AS shippingType,
      t1.shipping_company AS shippingCompany,
      t1.manager_member_type AS managerMemberType,
      t1.manager_member_id AS managerMemberId,
      t1.manager_member_name AS managerMemberName,
      t1.captain_account_id AS belongerId,
      t1.belonger_name AS belongerName,
      t1.belonger_phone AS belongerPhone,
      t1.ownership AS ownership,
      t1.departure_wharf_id AS departureWharfId,
      t1.destination_wharf_id AS destinationWharfId,
      t1.alevel_payload AS alevelPayload,
      t1.blevel_payload AS blevelPayload,
      t1.self_payload AS selfPayload,
      t1.total_payload AS totalPayload,
      t1.total_length AS totalLength,
      t1.max_width AS maxWidth,
      t1.moulded_depth AS mouldedDepth,
      t1.full_draft AS fullDraft,
      t1.empty_draft AS emptyDraft,
      t1.gps_device AS gpsDevice,
      t1.captain_account_id AS captainId,
      t1.captain_name AS captainName,
      t1.captain_phone AS captainPhone,
      t1.shipping_status AS shippingStatus,
      t1.audit_status AS auditStatus,
      t1.rejection_reasons AS rejectionReasons,
      t1.create_user AS createUser,
      t1.update_user AS updateUser,
      t1.create_time AS createTime,
      t1.update_time AS updateTime,
      t2.service_type AS serviceType,
      t1.max_loading_capacity AS maxLoadingCapacity
    FROM lgs_shipping_info t1
    LEFT JOIN lgs_authorized_seller_map t2 ON t1.shipping_id = t2.shipping_id AND t2.del_flg = 0 AND t2.authorize_status = 1
    WHERE t1.del_flg = 0
    <if test="memberId != null and memberId != ''">
      AND t1.manager_member_id = #{memberId}
    </if>
    <if test="managerMemberType != null and managerMemberType != ''">
      AND t1.manager_member_type = #{managerMemberType}
    </if>
    <if test="shippingStatus != null and shippingStatus != ''">
      AND t1.shipping_status = #{shippingStatus}
    </if>
      AND t1.audit_status IN (3, 5, 6)
    <if test="shippingName != null and shippingName != ''">
      AND t1.shipping_name like concat ('%',#{shippingName},'%')
    </if>
    OR t2.authorize_user_id = #{memberId}
    ORDER BY t1.create_time desc
  </select>


  <!--批量修改船舶状态-->
  <update id="batchUpdateShippingStatus" parameterType="java.util.Map">
    UPDATE lgs_shipping_info
    SET shipping_status = #{shippingStatus},
        update_time = NOW(),
        update_user = #{operatorId}
    WHERE del_flg = 0
    <if test="oldShippingStatus != null and oldShippingStatus != ''">
      AND shipping_status = #{oldShippingStatus}
    </if>
    AND shipping_id IN
    <foreach collection="list" item="item" index="index" separator="," open="(" close=")">
      #{item}
    </foreach>
  </update>

  <!--批量删除船舶-->
  <update id="batchDelShippingInfo" parameterType="java.util.Map">
    UPDATE lgs_shipping_info
    SET update_time = NOW(),
        update_user = #{operatorId},
        del_flg = 1
    WHERE del_flg = 0 and manager_member_id = #{managerMemberId}
    AND shipping_id IN
    <foreach collection="list" item="item" index="index" separator="," open="(" close=")">
      #{item}
    </foreach>
  </update>

  <!--获取船务公司集合-->
  <select id="getShippingCompanyList" resultType="java.lang.String">
    SELECT
      shipping_company AS shippingCompany
    FROM lgs_shipping_info
    <if test="shippingCompany != null and shippingCompany != ''">
      WHERE shipping_company LIKE concat('%', #{shippingCompany}, '%')
    </if>
    GROUP BY manager_member_id
  </select>


  <select id="queryERPShippingInfoListBySeller" parameterType="com.ecommerce.logistics.api.dto.shipping.ShippingInfoListQueryDTO"
          resultType="com.ecommerce.logistics.api.dto.shipping.ShippingInfoListDTO">
    SELECT * FROM (
    SELECT
    t1.shipping_id AS shippingId,
    t1.shipping_no AS shippingNo,
    t1.shipping_name AS shippingName,
    t1.shipping_type AS shippingType,
    t1.shipping_company AS shippingCompany,
    t1.manager_member_id AS managerMemberId,
    t1.manager_member_name AS managerMemberName,
    t1.alevel_payload AS alevelPayload,
    t1.blevel_payload AS blevelPayload,
    t1.max_loading_capacity AS maxLoadingCapacity,
    t1.captain_account_id AS captainId,
    t1.captain_name AS captainName,
    t1.captain_phone AS captainPhone,
    t1.shipping_status AS shippingStatus,
    t1.audit_status AS auditStatus,
    t1.rejection_reasons AS rejectionReasons,
    t2.service_type AS serviceType,
    IF( t2.service_type,t1.manager_member_name, '-') AS carrierName,
    t1.update_time AS updateTime,
    t1.ownership AS ownership,
    t4.erp_shipping_code
    FROM lgs_shipping_info t1
    LEFT JOIN lgs_authorized_seller_map t2 ON t1.shipping_id = t2.shipping_id AND t2.del_flg = 0 AND t2.authorize_status = 1
    LEFT JOIN lgs_erp_shipping_map t4 ON ( t4.ec_member_id = #{manufacturerId} OR t4.ec_member_id = t1.manager_member_id ) AND t4.ec_shipping_id = t1.shipping_id
    WHERE t1.del_flg = 0
    <if test="memberId != null and memberId != ''">
      AND t1.manager_member_id = #{memberId}
    </if>
    <if test="managerMemberType != null and managerMemberType != ''">
      AND t1.manager_member_type = #{managerMemberType}
    </if>
    OR (t2.authorize_user_id = #{memberId} AND t1.del_flg = 0)
    ) t3
    WHERE 1=1
    AND t3.erp_shipping_code is not NULL
    <if test="shippingCompany != null and shippingCompany != ''">
      AND t3.shippingCompany = #{shippingCompany}
    </if>
    <if test="shippingType != null and shippingType != ''">
      AND t3.shippingType = #{shippingType}
    </if>
    <if test="ownership != null and ownership != ''">
      AND t3.ownership = #{ownership}
    </if>
    <if test="shippingStatus != null and shippingStatus != ''">
      AND t3.shippingStatus = #{shippingStatus}
    </if>
    <if test="shippingName != null and shippingName != ''">
      AND t3.shippingName LIKE concat('%', #{shippingName}, '%')
    </if>
    <if test="auditStatus != null and auditStatus != ''">
      AND t3.auditStatus = #{auditStatus}
    </if>
    <if test="auditStatusList != null and auditStatusList.size() > 0">
      AND t3.auditStatus IN
      <foreach close=")" collection="auditStatusList" index="index" item="item" open="(" separator=",">
        #{item}
      </foreach>
    </if>
    <if test="blevelPayloadMin != null">
      <![CDATA[ AND t1.blevel_payload >= #{blevelPayloadMin} ]]>
    </if>
    <if test="blevelPayloadMax != null">
      <![CDATA[ AND t1.blevel_payload <= #{blevelPayloadMax} ]]>
    </if>

    ORDER BY t3.updateTime desc
  </select>

  <select id="findManagerMemberIdByPK" resultType="com.ecommerce.logistics.api.dto.shipping.ShippingInfoDTO">
    SELECT
      manager_member_id AS managerMemberId,
      manager_member_name AS managerMemberName
    FROM lgs_shipping_info
    WHERE shipping_id = #{shippingId} AND del_flg = 0
  </select>


  <!--获取ERP船舶绑定列表-->
  <select id="queryErpShippingBindList" resultType="com.ecommerce.logistics.api.dto.shipping.erp.BindErpShippingDTO">
    SELECT
      ec_member_id AS ecMemberId,
      ec_member_name AS ecMemberName,
      ec_shipping_id AS ecShippingId,
      ec_shipping_name AS ecShippingName,
      erp_carrier_no AS erpCarrierNo,
      erp_carrier_name AS erpCarrierName,
      erp_shipping_code AS erpShippingCode,
      erp_shipping_name AS erpShippingName
    FROM lgs_erp_shipping_map
    WHERE del_flg = 0 AND ec_shipping_id = #{shippingId}
    ORDER BY update_time DESC
  </select>

  <select id="selectShippingInfoName" parameterType="com.ecommerce.logistics.api.dto.shipping.ShippingInfoListQueryDTO" resultType="java.lang.String">
  	SELECT
		info.shipping_name 
	FROM
		lgs_shipping_info info 
	WHERE
		info.del_flg = 0
		AND info.manager_member_id = #{memberId}
		AND info.manager_member_type = #{managerMemberType}
		<if test="shippingName != null and shippingName != ''">
			AND info.shipping_name LIKE CONCAT('%',#{shippingName},'%')
		</if>
  </select>

  <!--内部调拨船舶列表接口-->
  <select id="queryAllocationShippingInfo" resultType="com.ecommerce.logistics.api.dto.shipping.ShippingInfoListDTO">
    SELECT
      t1.shipping_id AS shippingId,
      t1.shipping_no AS shippingNo,
      t1.shipping_name AS shippingName,
      t1.shipping_type AS shippingType,
      t1.shipping_company AS shippingCompany,
      t1.manager_member_id AS managerMemberId,
      t1.manager_member_name AS managerMemberName,
      t1.alevel_payload AS alevelPayload,
      t1.blevel_payload AS blevelPayload,
      t1.max_loading_capacity AS maxLoadingCapacity,
      t1.captain_account_id AS captainId,
      t1.captain_name AS captainName,
      t1.captain_phone AS captainPhone,
      t1.shipping_status AS shippingStatus,
      t1.audit_status AS auditStatus
    FROM lgs_shipping_info t1
    LEFT JOIN lgs_authorized_seller_map t2 ON t1.shipping_id = t2.shipping_id
    WHERE
        t2.authorize_user_id = #{memberId}
    AND t1.manager_member_id = #{carrierId}
    AND t1.audit_status IN (3,5,6)
    AND t1.shipping_status = 1
    AND t1.del_flg=0 AND t2.del_flg=0
    <if test="shippingName != null and shippingName != ''">
      AND t1.shipping_name like concat ('%',#{shippingName},'%')
    </if>
    <![CDATA[ AND t1.captain_account_id <> '' ]]>
    ORDER BY t1.update_time desc
  </select>
</mapper>