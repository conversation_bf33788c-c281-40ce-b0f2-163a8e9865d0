<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.ShippingMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.Shipping">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="shipping_id" jdbcType="VARCHAR" property="shippingId" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="user_type" jdbcType="TINYINT" property="userType" />
    <result column="number" jdbcType="VARCHAR" property="number" />
    <result column="shipping_type" jdbcType="VARCHAR" property="shippingType" />
    <result column="shipping_name" jdbcType="VARCHAR" property="shippingName" />
    <result column="transport_category_id" jdbcType="VARCHAR" property="transportCategoryId" />
    <result column="load_capacity" jdbcType="DECIMAL" property="loadCapacity" />
    <result column="displacement" jdbcType="DECIMAL" property="displacement" />
    <result column="overall_length" jdbcType="DECIMAL" property="overallLength" />
    <result column="extreme_width" jdbcType="DECIMAL" property="extremeWidth" />
    <result column="extreme_height" jdbcType="DECIMAL" property="extremeHeight" />
    <result column="certification_status" jdbcType="VARCHAR" property="certificationStatus" />
    <result column="fail_reason" jdbcType="VARCHAR" property="failReason" />
    <result column="bind_driver_id" jdbcType="VARCHAR" property="bindDriverId" />
    <result column="driver_name" jdbcType="VARCHAR" property="driverName" />
    <result column="driver_phone" jdbcType="VARCHAR" property="driverPhone" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="version" jdbcType="BIGINT" property="version" />
  </resultMap>
  <select id="queryShippingList" parameterType="com.ecommerce.logistics.api.dto.shipping.ShippingQueryDTO"
          resultMap="BaseResultMap">
    SELECT
      t1.*
    FROM
      lgs_shipping as t1
    <where>
      t1.del_flg = 0
      <if test="userId != null and userId != ''">
        <![CDATA[AND t1.user_id = #{userId}]]>
      </if>
      <if test="userType != null">
        <![CDATA[AND t1.user_type = #{userType}]]>
      </if>
      <if test="number != null and number != ''">
        <![CDATA[AND t1.number = #{number}]]>
      </if>
      <if test="shippingType != null and shippingType != ''">
        <![CDATA[AND t1.shipping_type = #{shippingType}]]>
      </if>
      <if test="shippingName != null and shippingName != ''">
        <![CDATA[AND t1.shipping_name LIKE CONCAT('%', #{shippingName},'%')]]>
      </if>
      <if test="transportCategoryId != null and transportCategoryId != ''">
        <![CDATA[AND t1.transport_category_id = #{transportCategoryId}]]>
      </if>
      <if test="certificationStatusList != null and certificationStatusList.size() > 0 ">
        AND t1.certification_status IN
        <foreach collection="certificationStatusList" close=")" index="index" item="status" open="(" separator=",">
          #{status}
        </foreach>
      </if>
    </where>
    ORDER BY
      t1.create_time DESC
  </select>
</mapper>