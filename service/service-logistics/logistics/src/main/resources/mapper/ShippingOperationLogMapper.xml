<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.ShippingOperationLogMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.ShippingOperationLog">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="operation_log_id" jdbcType="VARCHAR" property="operationLogId" />
    <result column="entry_id" jdbcType="VARCHAR" property="entryId" />
    <result column="business_type" jdbcType="INTEGER" property="businessType" />
    <result column="operation_type" jdbcType="VARCHAR" property="operationType" />
    <result column="operator_id" jdbcType="VARCHAR" property="operatorId" />
    <result column="operator_name" jdbcType="VARCHAR" property="operatorName" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="del_flg" jdbcType="INTEGER" property="delFlg" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <!--查询最近半年的数据-->
  <select id="selectOperationLogListDTO" resultType="com.ecommerce.logistics.api.dto.operationrecord.shipping.ShippingOperationLogDTO">
    SELECT operation_log_id AS operationLogId,
      entry_id AS entryId,
      business_type AS businessType,
      operation_type AS operationType,
      operator_id AS operatorId,
      operator_name AS operatorName,
      content AS content,
      create_time AS operationTime
    FROM lgs_shipping_operation_log
    WHERE entry_id = #{entryId}
    AND create_time > DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
    ORDER BY create_time DESC
  </select>


</mapper>