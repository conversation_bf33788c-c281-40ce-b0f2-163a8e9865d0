<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.ShippingRouteInfoMapper">
    <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.ShippingRouteInfo">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="shipping_route_id" jdbcType="VARCHAR" property="shippingRouteId"/>
        <result column="shipping_route_no" jdbcType="VARCHAR" property="shippingRouteNo"/>
        <result column="picking_wharf_code" jdbcType="VARCHAR" property="pickingWharfCode"/>
        <result column="picking_wharf_name" jdbcType="VARCHAR" property="pickingWharfName"/>
        <result column="unloading_wharf_code" jdbcType="VARCHAR" property="unloadingWharfCode"/>
        <result column="unloading_wharf_name" jdbcType="VARCHAR" property="unloadingWharfName"/>
        <result column="transport_category_id" jdbcType="VARCHAR" property="transportCategoryId"/>
        <result column="belonger_id" jdbcType="VARCHAR" property="belongerId"/>
        <result column="user_type" jdbcType="INTEGER" property="userType" />
        <result column="seller_id" jdbcType="VARCHAR" property="sellerId"/>
        <result column="buyer_id" jdbcType="VARCHAR" property="buyerId"/>
        <result column="buyer_name" jdbcType="VARCHAR" property="buyerName"/>
        <result column="del_flg" jdbcType="INTEGER" property="delFlg"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>


    <select id="queryShippingRouteList" parameterType="com.ecommerce.logistics.api.dto.shippingroute.ShippingRouteQueryDTO"
            resultType="com.ecommerce.logistics.api.dto.shippingroute.ShippingRouteListDTO">
        SELECT
            t1.shipping_route_id AS shippingRouteId,
            t1.shipping_route_no AS shippingRouteNo,
            t1.picking_wharf_name AS pickingWharfName,
            t1.unloading_wharf_name AS unloadingWharfName,
            t1.buyer_name AS buyerName,
            t1.transport_category_id AS transportCategoryId,
            t1.create_time AS createTime,
            GROUP_CONCAT(t2.ship_type) AS shipType
        FROM lgs_shipping_route_info t1
        LEFT JOIN lgs_shipping_fare_payment t2 ON t1.shipping_route_id = t2.shipping_route_id AND t2.del_flg =0
        WHERE t1.del_flg = 0 AND t1.belonger_id = #{belongerId}
        <if test="userType != null">
            AND t1.user_type = #{userType}
        </if>
        <if test="pickingWharfCode != null and pickingWharfCode != ''">
            AND t1.picking_wharf_code = #{pickingWharfCode}
        </if>
        <if test="unloadingWharfCode != null and unloadingWharfCode != ''">
            AND t1.unloading_wharf_code = #{unloadingWharfCode}
        </if>
        GROUP BY t1.shipping_route_id
        ORDER BY t1.create_time DESC
    </select>

    <select id="checkData" parameterType="com.ecommerce.logistics.dao.vo.ShippingRouteInfo"
            resultType="java.lang.String">
        SELECT shipping_route_id AS shippingRouteId
        FROM lgs_shipping_route_info
        WHERE del_flg = 0
        AND belonger_id = #{belongerId}
        AND user_type = #{userType}
        AND seller_id = #{sellerId}
        AND buyer_id = #{buyerId}
        AND picking_wharf_code = #{pickingWharfCode}
        AND unloading_wharf_code = #{unloadingWharfCode}
        AND transport_category_id like concat('%', #{transportCategoryId}, '%')
    </select>

    <!-- 获取当前卖家的客户收货码头地址列表 -->
    <select id="queryUnloadingWharf" resultType="java.util.Map">
        SELECT
            unloading_wharf_code AS unloadingWharfCode,
            unloading_wharf_name AS unloadingWharfName
        FROM lgs_shipping_route_info
        WHERE belonger_id = #{belongerId}
        AND del_flg = 0
    </select>


    <!-- 查询航运运费规则 -->
    <select id="getShippingRoute" parameterType="com.ecommerce.logistics.api.dto.shippingrule.ShippingRuleDTO"
            resultType="com.ecommerce.logistics.api.dto.shippingrule.ShippingUnitDTO">
        SELECT
            t1.transport_category_id AS transportCategoryId,
            t1.shipping_route_id as shippingRouteId,
            t2.shipping_fare_payment_id AS shippingFarePaymentId,
            t2.ship_type AS shipType,
            t2.route_tariff_type AS routeTariffType,
            t2.distinguish AS distinguish,
            t2.auxiliary_price_no AS auxiliaryPriceNo,
            t2.price AS price,
            t2.start_date AS startDate,
            t2.end_date AS endDate
        FROM
            lgs_shipping_route_info t1
        LEFT JOIN lgs_shipping_fare_payment t2 ON t1.shipping_route_id = t2.shipping_route_id AND t2.del_flg = 0
        WHERE
            t1.del_flg = 0
        AND t1.belonger_id = #{belongerId}
        <if test="sellerId != null and sellerId != ''">
            AND t1.seller_id = #{sellerId}
        </if>
        AND t1.buyer_id = #{buyerId}
        AND t1.picking_wharf_code = #{pickingWharfCode}
        AND t1.unloading_wharf_code = #{unloadingWharfCode}
        AND t2.ship_type = #{shipType}
        AND t2.route_tariff_type = #{routeTariffType}
        <!-- 查询出有效期范围内的规则 -->
        <![CDATA[ AND t2.start_date <= NOW() AND t2.end_date >= NOW() ]]>
        AND t1.transport_category_id LIKE concat( '%', #{transportCategoryId}, '%' )
  </select>

</mapper>