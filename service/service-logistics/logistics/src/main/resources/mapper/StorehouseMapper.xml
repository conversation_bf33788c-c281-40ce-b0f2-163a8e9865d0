<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.StorehouseMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.Storehouse">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="storehouse_id" jdbcType="VARCHAR" property="storehouseId" />
    <result column="storehouse_number" jdbcType="VARCHAR" property="storehouseNumber" />
    <result column="storehouse_name" jdbcType="VARCHAR" property="storehouseName" />
    <result column="storehouse_capacity" jdbcType="DECIMAL" property="storehouseCapacity" />
    <result column="ava_stock" jdbcType="DECIMAL" property="avaStock" />
    <result column="used_stock" jdbcType="DECIMAL" property="usedStock" />
    <result column="locked_stock" jdbcType="DECIMAL" property="lockedStock" />
    <result column="warehouse_id" jdbcType="VARCHAR" property="warehouseId" />
    <result column="warehouse_name" jdbcType="VARCHAR" property="warehouseName" />
    <result column="warehouse_type" jdbcType="VARCHAR" property="warehouseType" />
    <result column="administrator" jdbcType="VARCHAR" property="administrator" />
    <result column="administrator_phone" jdbcType="VARCHAR" property="administratorPhone" />
    <result column="storehouse_status" jdbcType="VARCHAR" property="storehouseStatus" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="version" jdbcType="BIGINT" property="version" />
  </resultMap>

  <select id="fetchStockList" parameterType="com.ecommerce.logistics.api.dto.storehouse.StorehouseQueryDTO"
          resultType="com.ecommerce.logistics.api.dto.storehouse.StorehouseListDTO">
    select
      s.warehouse_id as warehouseId,
      s.storehouse_capacity as warehouseCapacity,
      s.locked_stock as lockedStock,
      s.used_stock as usedStock,
      s.ava_stock as avaStock
    from lgs_storehouse s
    where
      s.del_flg = 0
      <if test="warehouseIdList != null">
        and s.warehouse_id in
        <foreach collection="warehouseIdList" item="sii" index="six" open="(" separator="," close=")">
          #{sii}
        </foreach>
      </if>
  </select>

  <select id="queryPlatformWarehouseIdForSeller" parameterType="com.ecommerce.logistics.api.dto.storehouse.WarehouseStorageQueryDTO" resultType="java.lang.String">
    select distinct ls.warehouse_id
    from lgs_platform_store_map lpsm , lgs_storehouse ls
    where ls.del_flg = 0 and lpsm.del_flg = 0
    and lpsm.warehouse_id = ls.warehouse_id
    and (lpsm.locked_stock &gt;= 0 or lpsm.used_stock &gt;= 0)
    <if test="warehouseId != null and warehouseId != ''">
      and ls.warehouse_id = #{warehouseId}
    </if>
    <if test="userId != null and userId != ''">
      and lpsm.user_id = #{userId}
    </if>
    <if test="warehouseNameLike != null and warehouseNameLike != ''">
      and ls.warehouse_name like concat('%', #{warehouseNameLike},'%')
    </if>
    <if test="type != null and type != ''">
      and ls.warehouse_type = #{type}
    </if>
    order by ls.warehouse_name asc

  </select>

  <select id="queryPlatformStoreForSeller" resultType="com.ecommerce.logistics.api.dto.storehouse.WarehouseStorageListDTO">
    select
           ls.warehouse_id as warehouseId,
           max(ls.storehouse_capacity) as warehouseCapacity,
           min(ls.warehouse_name) as warehouseName,
           min(ls.warehouse_type) as type,
           sum(ifnull(lpsm.used_stock, 0)) as usedStock,
           sum(ifnull(lpsm.locked_stock, 0)) as lockedStock
    from lgs_platform_store_map lpsm , lgs_storehouse ls
    where lpsm.del_flg = 0 and ls.del_flg = 0 and lpsm.warehouse_id = ls.warehouse_id
    and lpsm.user_id = #{userId}
      and ls.warehouse_id in
    <foreach collection="warehouseIdList" item="wi" open="(" separator="," close=")">
      #{wi}
    </foreach>
    group by ls.warehouse_id
  </select>

  <update id="updateStoreStockChange" parameterType="com.ecommerce.logistics.dao.dto.storehouse.StockChangeBean">
    update lgs_storehouse

    set
        update_time = current_timestamp
        <if test="updateUser != null and updateUser != ''">
          ,update_user = #{updateUser}
        </if>
        <if test="stockChange != null">
          ,used_stock = ifnull(used_stock, 0) + #{stockChange}
        </if>
        <if test="lockChange != null">
          ,locked_stock = ifnull(locked_stock, 0) + #{lockChange}
        </if>
        <if test="avaChange != null">
          , ava_stock = ifnull(ava_stock, 0) + #{avaChange}
        </if>
    where
        warehouse_id = #{warehouseId} and storehouse_number = #{storehouseNumber} and del_flg = 0
        <if test="avaChange != null">
          and ifnull(ava_stock, 0) + #{avaChange} &gt;= 0
        </if>
        <if test="lockChange != null">
          and ifnull(locked_stock, 0) + #{lockChange} &gt;= 0
        </if>
        <if test="stockChange != null">
          and ifnull(used_stock, 0) + #{stockChange} &gt;= 0
        </if>
  </update>

</mapper>