<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.TransportAddressMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.TransportAddress">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="transport_address_id" jdbcType="VARCHAR" property="transportAddressId" />
    <result column="address_type" jdbcType="VARCHAR" property="addressType" />
    <result column="address_name" jdbcType="VARCHAR" property="addressName" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="user_type" jdbcType="TINYINT" property="userType" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="district" jdbcType="VARCHAR" property="district" />
    <result column="district_code" jdbcType="VARCHAR" property="districtCode" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="location" jdbcType="VARCHAR" property="location" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="version" jdbcType="BIGINT" property="version" />
  </resultMap>

  <select id="queryTransportAddressList" parameterType="com.ecommerce.logistics.api.dto.transportaddress.TransportAddressQueryDTO"
          resultMap="BaseResultMap">
    SELECT
      t1.*
    FROM
      lgs_transport_address as t1
    <where>
      t1.del_flg = 0
      <if test="addressType != null and addressType != ''">
        <![CDATA[AND t1.address_type = #{addressType}]]>
      </if>
      <if test="addressName != null and addressName != ''">
        <![CDATA[AND t1.address_name LIKE CONCAT('%', #{addressName},'%')]]>
      </if>
      <if test="userId != null and userId != ''">
        <![CDATA[AND t1.user_id = #{userId}]]>
      </if>
      <if test="userType != null">
        <![CDATA[AND t1.user_type = #{userType}]]>
      </if>
      <if test="provinceCode != null and provinceCode != ''">
        <![CDATA[AND t1.province_code = #{provinceCode}]]>
      </if>
      <if test="cityCode != null and cityCode != ''">
        <![CDATA[AND t1.city_code = #{cityCode}]]>
      </if>
      <if test="districtCode != null and districtCode != ''">
        <![CDATA[AND t1.district_code = #{districtCode}]]>
      </if>
    </where>
    ORDER BY
      t1.create_time DESC
  </select>
</mapper>