<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.TransportCapacityMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.TransportCapacity">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="transport_capacity_id" jdbcType="VARCHAR" property="transportCapacityId" />
    <result column="publisher_id" jdbcType="VARCHAR" property="publisherId" />
    <result column="publisher_name" jdbcType="VARCHAR" property="publisherName" />
    <result column="publisher_type" jdbcType="TINYINT" property="publisherType" />
    <result column="driver_id" jdbcType="VARCHAR" property="driverId" />
    <result column="driver_name" jdbcType="VARCHAR" property="driverName" />
    <result column="transport_tool_id" jdbcType="VARCHAR" property="transportToolId" />
    <result column="load_capacity" jdbcType="DECIMAL" property="loadCapacity" />
    <result column="transport_tool_type" jdbcType="VARCHAR" property="transportToolType" />
    <result column="delivery_time_start" jdbcType="DATE" property="deliveryTimeStart" />
    <result column="delivery_time_end" jdbcType="DATE" property="deliveryTimeEnd" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="fail_reason" jdbcType="VARCHAR" property="failReason" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="version" jdbcType="BIGINT" property="version" />
  </resultMap>
  <select id="queryTransportCapacityList" parameterType="com.ecommerce.logistics.api.dto.transportcapacity.TransportCapacityQueryDTO"
          resultType="com.ecommerce.logistics.dao.dto.transport.TransportCapacityListDO">
    SELECT
      t1.transport_capacity_id as transportCapacityId,
      t1.publisher_id as publisherId,
      t1.publisher_name as publisherName,
      t1.publisher_type as publisherType,
      t1.driver_id as driverId,
      t1.driver_name as driverName,
      t1.transport_tool_id as transportToolId,
      t1.load_capacity as loadCapacity,
      t1.transport_tool_type as transportToolType,
      t1.status as status,
      t1.create_time as createTime,
      t2.transport_tool_id as transportToolId,
      t2.transport_category_id as transportCategoryId,
      t2.tool_number as toolNumber
    FROM
      lgs_transport_capacity as t1
    LEFT JOIN (
      SELECT
        vehicle_id as transport_tool_id,
        transport_category_id as transport_category_id,
        number as tool_number
      FROM
        lgs_vehicle
      WHERE
        del_flg=0
        <if test="transportCategoryId != null and transportCategoryId != ''">
          AND transport_category_id=#{transportCategoryId}
        </if>
      UNION
      SELECT
        shipping_id as transport_tool_id,
        transport_category_id as transport_category_id,
        number as tool_number
      FROM
        lgs_shipping
      WHERE
        del_flg=0
        <if test="transportCategoryId != null and transportCategoryId != ''">
          AND transport_category_id=#{transportCategoryId}
        </if>
      ) as t2 ON t1.transport_tool_id = t2.transport_tool_id
    <where>
      t1.del_flg = 0
      <if test="publisherId != null and publisherId != ''">
        <![CDATA[AND t1.publisher_id = #{publisherId}]]>
      </if>
      <if test="publisherType != null">
        <![CDATA[AND t1.publisher_type = #{publisherType}]]>
      </if>
      <if test="driverId != null and driverId != ''">
        <![CDATA[AND t1.driver_id = #{driverId}]]>
      </if>
      <if test="transportToolType != null and transportToolType != ''">
        <![CDATA[AND t1.transport_tool_type = #{transportToolType}]]>
      </if>
    </where>
  </select>
  <select id="queryTransportCapacityViewRecord" resultType="com.ecommerce.logistics.dao.dto.transport.TransportCapacityListDO">
    SELECT
      t1.transport_capacity_id as transportCapacityId,
      t1.publisher_id as publisherId,
      t1.publisher_name as publisherName,
      t1.publisher_type as publisherType,
      t1.driver_id as driverId,
      t1.driver_name as driverName,
      t1.transport_tool_id as transportToolId,
      t1.load_capacity as loadCapacity,
      t1.transport_tool_type as transportToolType,
      t1.delivery_time_start as deliveryTimeStart,
      t1.delivery_time_end as deliveryTimeEnd,
      t1.status as status,
      t1.create_time as createTime,
      t2.transport_tool_id as transportToolId,
      t2.transport_category_id as transportCategoryId,
      t2.tool_number as toolNumber,
      t2.transport_tool_phone as transportToolPhone
    FROM
      lgs_transport_capacity as t1
    LEFT JOIN (
      SELECT
        vehicle_id as transport_tool_id,
        transport_category_id as transport_category_id,
        number as tool_number,
        driver_phone as transport_tool_phone
      FROM
        lgs_vehicle
      UNION
      SELECT
        shipping_id as transport_tool_id,
        'null' as transport_category_id,
        shipping_name as tool_number,
        captain_phone as transport_tool_phone
      FROM
        lgs_shipping_info
      ) as t2 ON t1.transport_tool_id = t2.transport_tool_id
    <where>
      t1.del_flg = 0 AND t1.transport_capacity_id IN
      <foreach close=")" collection="list" index="index" item="id" open="(" separator=",">
        #{id}
      </foreach>
    </where>
  </select>
  <select id="queryTransportCapacityIdList" parameterType="com.ecommerce.logistics.api.dto.transportcapacity.TransportCapacityQueryDTO"
          resultType="java.lang.String">
    SELECT
      DISTINCT t1.transport_capacity_id
    FROM
      lgs_transport_capacity as t1
    LEFT JOIN
      lgs_transport_zone_map as t2 on t1.transport_capacity_id = t2.transport_capacity_id
    <if test="needJoinVOrS != null and needJoinVOrS > 0">
      INNER JOIN (
        SELECT
          vehicle_id as transport_tool_id,
          transport_category_id as transport_category_id
        FROM
          lgs_vehicle
        WHERE del_flg = 0
          <if test="transportCategoryId != null and transportCategoryId != ''">
            and transport_category_id=#{transportCategoryId}
          </if>
          <if test="toolNumber != null and toolNumber != ''">
            and number = #{toolNumber}
          </if>
        UNION
        SELECT
          shipping_id as transport_tool_id,
          'null' as transport_category_id
        FROM
          lgs_shipping_info
        WHERE del_flg = 0
          <if test="toolNumber != null and toolNumber != ''">
            and shipping_name = #{toolNumber}
          </if>
      ) as t3 ON t1.transport_tool_id = t3.transport_tool_id
    </if>
    <where>
      t1.del_flg = 0
      <if test="publisherId != null and publisherId != ''">
        <![CDATA[AND t1.publisher_id = #{publisherId}]]>
      </if>
      <if test="publisherIdList != null and publisherIdList.size() > 0">
        AND t1.publisher_id IN
        <foreach close=")" collection="publisherIdList" index="index" item="publisherId" open="(" separator=",">
          #{publisherId}
        </foreach>
      </if>
      <if test="publisherType != null">
        <![CDATA[AND t1.publisher_type = #{publisherType}]]>
      </if>
      <if test="driverId != null and driverId != ''">
        <![CDATA[AND t1.driver_id = #{driverId}]]>
      </if>
      <if test="transportToolType != null and transportToolType != ''">
        <![CDATA[AND t1.transport_tool_type = #{transportToolType}]]>
      </if>
      <if test="(deliveryTimeStart != null and deliveryTimeStart != '') and
                (deliveryTimeEnd != null and deliveryTimeEnd != '')">
        <![CDATA[AND ((t1.delivery_time_start <= #{deliveryTimeStart} and t1.delivery_time_end >= #{deliveryTimeStart})
                 OR (t1.delivery_time_start <= #{deliveryTimeEnd} and t1.delivery_time_end >= #{deliveryTimeEnd}))]]>
      </if>
      <if test="(deliveryTimeStart != null and deliveryTimeStart != '') and
                (deliveryTimeEnd == null or deliveryTimeEnd == '')">
        <![CDATA[AND (t1.delivery_time_start <= #{deliveryTimeStart} and t1.delivery_time_end >= #{deliveryTimeStart})]]>
      </if>
      <if test="(deliveryTimeEnd != null and deliveryTimeEnd != '') and
                (deliveryTimeStart == null or deliveryTimeStart == '')">
        <![CDATA[AND (t1.delivery_time_start <= #{deliveryTimeEnd} and t1.delivery_time_end >= #{deliveryTimeEnd})]]>
      </if>
      <if test="loadCapacityMin != null">
        <![CDATA[AND t1.load_capacity >= #{loadCapacityMin}]]>
      </if>
      <if test="loadCapacityMax != null">
        <![CDATA[AND t1.load_capacity <= #{loadCapacityMax}]]>
      </if>
      <if test="provinceCode != null and provinceCode != ''">
        <![CDATA[AND t2.province_code = #{provinceCode}]]>
      </if>
      <if test="cityCode != null and cityCode != ''">
        <![CDATA[AND t2.city_code = #{cityCode}]]>
      </if>
      <if test="districtCode != null and districtCode != ''">
        <![CDATA[AND t2.drdistrict_code = #{districtCode}]]>
      </if>
    </where>
    <choose>
      <when test="orderMap != null and orderMap.size() > 0">
        <foreach collection="orderMap" index="field" item="order" separator=",">
          ORDER BY
          ${field} ${order}
        </foreach>
      </when>
      <otherwise>
        ORDER BY
        t1.create_time DESC
      </otherwise>
    </choose>
  </select>
</mapper>