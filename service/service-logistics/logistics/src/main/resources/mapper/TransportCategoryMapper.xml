<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.TransportCategoryMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.TransportCategory">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="transport_category_id" jdbcType="VARCHAR" property="transportCategoryId" />
    <result column="category_name" jdbcType="VARCHAR" property="categoryName" />
    <result column="is_integrable" jdbcType="TINYINT" property="isIntegrable" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="version" jdbcType="BIGINT" property="version" />
  </resultMap>
  <select id="selectTransportCategoryList" parameterType="com.ecommerce.logistics.api.dto.transportcategory.TransportCategoryListQueryDTO" resultType="com.ecommerce.logistics.api.dto.transportcategory.TransportCategoryListDTO">
    SELECT
    transport_category_id AS transportCategoryId,
      category_name AS categoryName,
      is_integrable  AS isIntegrable
    FROM
      lgs_transport_category
    <where>
      del_flg = 0
      <if test="categoryName != null and categoryName != ''">
        AND category_name like concat('%',#{categoryName},'%')
      </if>
      <if test="isIntegrable != null">
        AND is_integrable = #{isIntegrable}
      </if>
    </where>
    ORDER BY
    	update_time DESC
  </select>
  <update id="updateTransportCategory" parameterType="com.ecommerce.logistics.api.dto.transportcategory.TransportCategoryEditDTO">
    UPDATE
      lgs_transport_category
    SET
      category_name = #{categoryName},
      is_integrable = #{isIntegrable},
      update_time = CURRENT_TIMESTAMP()
    WHERE
      transport_category_id =#{transportCategoryId}
  </update>

  <select id="queryOptions" resultType="com.ecommerce.logistics.api.dto.transportcategory.TransportOption">
    SELECT
    transport_category_id AS transportCategoryId,
    category_name AS categoryName
    FROM lgs_transport_category
    WHERE  del_flg = 0
  </select>

  <select id="queryCategoryNameByIds" resultType="com.ecommerce.logistics.api.dto.transportcategory.CategoryNameDTO" parameterType="java.util.List">
    SELECT category_name,transport_category_id
    FROM lgs_transport_category
    WHERE  del_flg = 0 AND transport_category_id IN
    <foreach close=")" collection="ids" index="index" item="item" open="(" separator=",">
      #{item}
    </foreach>
  </select>

  <select id="queryCategoryNameById" resultType="java.lang.String" parameterType="java.lang.String">
    SELECT category_name,transport_category_id
    FROM lgs_transport_category
    WHERE transport_category_id = #{transportCategoryId} AND del_flg = 0
  </select>

  <select id="queryTransportCategoryByWaybillIdList" resultType="com.ecommerce.logistics.dao.dto.transport.WaybillIdCategoryMappingDO">
    select distinct lw.waybill_id as waybillId, ltc.category_name as transportCategory
    from lgs_transport_category ltc, lgs_waybill lw, lgs_product_quantity_map map, lgs_product_info info
    where lw.waybill_id = map.map_id and map.product_info_id = info.product_info_id and info.transport_category_id = ltc.transport_category_id
    and
        lw.waybill_id in
          <foreach collection="waybillIdList" item="wi" open="(" separator="," close=")">
            #{wi}
          </foreach>
  </select>

  <select id="queryTransportCategoryByParentIdList" resultType="com.ecommerce.logistics.dao.dto.transport.WaybillIdCategoryMappingDO">
    select distinct lw.parent_id as waybillId, ltc.category_name as transportCategory
    from lgs_transport_category ltc, lgs_waybill lw, lgs_product_quantity_map map, lgs_product_info info
    where lw.waybill_id = map.map_id and map.product_info_id = info.product_info_id and info.transport_category_id = ltc.transport_category_id
    and
    lw.parent_id in
    <foreach collection="parentIdList" item="wi" open="(" separator="," close=")">
      #{wi}
    </foreach>
  </select>

</mapper>