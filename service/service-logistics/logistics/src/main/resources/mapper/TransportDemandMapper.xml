<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.TransportDemandMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.TransportDemand">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="transport_demand_id" jdbcType="VARCHAR" property="transportDemandId" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="user_type" jdbcType="TINYINT" property="userType" />
    <result column="freight_desc" jdbcType="VARCHAR" property="freightDesc" />
    <result column="freight_quantity" jdbcType="DECIMAL" property="freightQuantity" />
    <result column="freight_unit" jdbcType="VARCHAR" property="freightUnit" />
    <result column="transport_category_id" jdbcType="VARCHAR" property="transportCategoryId" />
    <result column="transport_tool_type" jdbcType="VARCHAR" property="transportToolType" />
    <result column="delivery_time" jdbcType="DATE" property="deliveryTime" />
    <result column="delivery_time_range" jdbcType="VARCHAR" property="deliveryTimeRange" />
    <result column="picking_address_id" jdbcType="VARCHAR" property="pickingAddressId" />
    <result column="unload_address_id" jdbcType="VARCHAR" property="unloadAddressId" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="fail_reason" jdbcType="VARCHAR" property="failReason" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="version" jdbcType="BIGINT" property="version" />
  </resultMap>
  <select id="queryTransportDemandList" parameterType="com.ecommerce.logistics.api.dto.transportdemand.TransportDemandQueryDTO"
          resultType="com.ecommerce.logistics.dao.dto.transport.TransportDemandListDO">
    SELECT
      t1.transport_demand_id as transportDemandId,
      t1.user_id as userId,
      t1.user_name as userName,
      t1.user_type as userType,
      t1.freight_desc as freightDesc,
      t1.freight_quantity as freightQuantity,
      t1.freight_unit as freightUnit,
      t1.transport_category_id as transportCategoryId,
      t1.transport_tool_type as transportToolType,
      t1.delivery_time as deliveryTime,
      t1.delivery_time_range as deliveryTimeRange,
      t1.picking_address_id as pickingAddressId,
      t1.unload_address_id as unloadAddressId,
      t1.status as status,
      t1.fail_reason as failReason,
      t1.create_time as createTime
    FROM
      lgs_transport_demand as t1
    INNER JOIN
      lgs_transport_address as t2 on t1.unload_address_id = t2.transport_address_id
    <where>
      t1.del_flg = 0
      <if test="userId != null and userId != ''">
        <![CDATA[AND t1.user_id = #{userId}]]>
      </if>
      <if test="userType != null">
        <![CDATA[AND t1.user_type = #{userType}]]>
      </if>
      <if test="transportCategoryId != null and transportCategoryId != ''">
        <![CDATA[AND t1.transport_category_id = #{transportCategoryId}]]>
      </if>
      <if test="transportToolType != null and transportToolType != ''">
        <![CDATA[AND t1.transport_tool_type = #{transportToolType}]]>
      </if>
      <if test="deliveryTimeStart != null and deliveryTimeStart != ''">
        <![CDATA[AND t1.delivery_time >= #{deliveryTimeStart}]]>
      </if>
      <if test="deliveryTimeEnd != null and deliveryTimeEnd != ''">
        <![CDATA[AND t1.delivery_time <= #{deliveryTimeEnd}]]>
      </if>
      <if test="pickingAddressId != null and pickingAddressId != ''">
        <![CDATA[AND t1.picking_address_id = #{pickingAddressId}]]>
      </if>
      <if test="unloadAddressId != null and unloadAddressId != ''">
        <![CDATA[AND t1.unload_address_id = #{unloadAddressId}]]>
      </if>
      <if test="unloadProvinceCode != null and unloadProvinceCode != ''">
        <![CDATA[AND t2.province_code = #{unloadProvinceCode}]]>
      </if>
      <if test="unloadCityCode != null and unloadCityCode != ''">
        <![CDATA[AND t2.city_code = #{unloadCityCode}]]>
      </if>
      <if test="unloadDistrictCode != null and unloadDistrictCode != ''">
        <![CDATA[AND t2.district_code = #{unloadDistrictCode}]]>
      </if>
      <if test="statusList != null and statusList.size() > 0">
        AND t1.status IN
        <foreach collection="statusList" close=")" index="index" item="status" open="(" separator=",">
          #{status}
        </foreach>
      </if>
    </where>
    <choose>
      <when test="orderMap != null and orderMap.size() > 0">
        <foreach collection="orderMap" index="field" item="order" separator=",">
          ORDER BY
          ${field} ${order}
        </foreach>
      </when>
      <otherwise>
        ORDER BY
        t1.create_time DESC
      </otherwise>
    </choose>
  </select>
  <select id="queryTransportDemandViewRecord" resultType="com.ecommerce.logistics.dao.dto.transport.TransportDemandListDO">
    SELECT
      t1.transport_demand_id as transportDemandId,
      t1.user_id as userId,
      t1.user_name as userName,
      t1.user_type as userType,
      t1.freight_desc as freightDesc,
      t1.freight_quantity as freightQuantity,
      t1.freight_unit as freightUnit,
      t1.transport_category_id as transportCategoryId,
      t1.transport_tool_type as transportToolType,
      t1.delivery_time as deliveryTime,
      t1.delivery_time_range as deliveryTimeRange,
      t1.picking_address_id as pickingAddressId,
      t1.unload_address_id as unloadAddressId,
      t1.status as status,
      t1.create_time as createTime
    FROM
      lgs_transport_demand as t1
    <where>
      t1.del_flg = 0 AND t1.transport_demand_id IN
      <foreach collection="list" close=")" index="index" item="id" open="(" separator=",">
        #{id}
      </foreach>
    </where>
  </select>
</mapper>