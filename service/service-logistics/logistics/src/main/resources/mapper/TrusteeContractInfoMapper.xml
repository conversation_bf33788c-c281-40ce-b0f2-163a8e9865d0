<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.TrusteeContractInfoMapper">
    <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.TrusteeContractInfo">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="contract_id" jdbcType="VARCHAR" property="contractId"/>
        <result column="contract_number" jdbcType="VARCHAR" property="contractNumber"/>
        <result column="trustee_id" jdbcType="VARCHAR" property="trusteeId"/>
        <result column="trustee_type" jdbcType="INTEGER" property="trusteeType"/>
        <result column="trustee_name" jdbcType="VARCHAR" property="trusteeName"/>
        <result column="contract_type" jdbcType="VARCHAR" property="contractType"/>
        <result column="transport_category_id" jdbcType="VARCHAR" property="transportCategoryId"/>
        <result column="goods_id" jdbcType="VARCHAR" property="goodsId"/>
        <result column="goods_name" jdbcType="VARCHAR" property="goodsName"/>
        <result column="quantity" jdbcType="DECIMAL" property="quantity"/>
        <result column="quantity_fixed_flag" jdbcType="TINYINT" property="quantityFixedFlag"/>
        <result column="transport_price" jdbcType="DECIMAL" property="transportPrice"/>
        <result column="price_fixed_flag" jdbcType="TINYINT" property="priceFixedFlag"/>
        <result column="sign_date" jdbcType="DATE" property="signDate"/>
        <result column="start_date" jdbcType="DATE" property="startDate"/>
        <result column="end_date" jdbcType="DATE" property="endDate"/>
        <result column="invoice_type" jdbcType="VARCHAR" property="invoiceType"/>
        <result column="invoice_rate" jdbcType="DECIMAL" property="invoiceRate"/>
        <result column="settlement_period_type" jdbcType="VARCHAR" property="settlementPeriodType"/>
        <result column="contact_name" jdbcType="VARCHAR" property="contactName"/>
        <result column="contact_phone" jdbcType="VARCHAR" property="contactPhone"/>
        <result column="del_flg" jdbcType="TINYINT" property="delFlg"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="note" jdbcType="LONGVARCHAR" property="note"/>
        <result column="version" jdbcType="BIGINT" property="version" />
    </resultMap>


    <select id="queryTrusteeContractList" parameterType="com.ecommerce.logistics.api.dto.contract.TrusteeContractListQueryDTO"
            resultType="com.ecommerce.logistics.api.dto.contract.TrusteeContractListDTO">
        SELECT
            t1.contract_id AS contractId,
            t1.contract_number AS contractNumber,
            t1.trustee_id AS trusteeId,
            t1.trustee_type AS trusteeType,
            t1.trustee_name AS trusteeName,
            t1.transport_category_id AS transportCategoryId,
            t1.contract_type AS contractType,
            t1.sign_date AS signDate,
            t1.start_date AS startDate,
            t1.end_date AS endDate,
            t1.settlement_period_type AS settlementPeriodType,
            t1.invoice_type AS invoiceType,
            t1.create_time AS createTime,
            t2.attachment_id AS attachmentId,
            t2.entry_id AS entryId,
            t2.bid AS bid,
            t2.type AS type,
            t2.file_name AS fileName,
            t2.file_ext AS fileExt,
            t2.file_url AS fileUrl
        FROM
            lgs_trustee_contract_info t1
        LEFT JOIN lgs_attachment t2 ON t1.contract_id = t2.entry_id and t2.del_flg = 0
        where
        t1.del_flg = 0
        <if test="contractNumber != null and contractNumber !=''">
            AND t1.contract_number = #{contractNumber}
        </if>
        <if test="trusteeType != null">
            AND t1.trustee_type = #{trusteeType}
        </if>
        <if test="trusteeName != null and trusteeName !=''">
            AND t1.trustee_name like concat('%', #{trusteeName}, '%')
        </if>
        <if test="contractType != null and contractType !=''">
            AND t1.contract_type = #{contractType}
        </if>
        <if test="settlementPeriodType != null and settlementPeriodType !=''">
            AND t1.settlement_period_type = #{settlementPeriodType}
        </if>
        <if test="invoiceType != null and invoiceType !=''">
            AND t1.invoice_type = #{invoiceType}
        </if>
        <if test="signDateStart != null and signDateEnd != null">
            AND t1.sign_date BETWEEN #{signDateStart} AND #{signDateEnd}
        </if>
        <if test="createTimeStart != null and createTimeEnd != null">
            AND t1.create_time BETWEEN #{createTimeStart} AND #{createTimeEnd}
        </if>
        ORDER BY t1.create_time DESC
    </select>

</mapper>