<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.UnloadingObjectionMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.UnloadingObjection">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="objection_id" jdbcType="VARCHAR" property="objectionId" />
    <result column="objection_num" jdbcType="VARCHAR" property="objectionNum" />
    <result column="proposer_type" jdbcType="VARCHAR" property="proposerType" />
    <result column="proposer_id" jdbcType="VARCHAR" property="proposerId" />
    <result column="proposer_name" jdbcType="VARCHAR" property="proposerName" />
    <result column="relation_bill_id" jdbcType="VARCHAR" property="relationBillId" />
    <result column="relation_bill_num" jdbcType="VARCHAR" property="relationBillNum" />
    <result column="goods_id" jdbcType="VARCHAR" property="goodsId" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="vehicle_num" jdbcType="VARCHAR" property="vehicleNum" />
    <result column="driver_id" jdbcType="VARCHAR" property="driverId" />
    <result column="driver_name" jdbcType="VARCHAR" property="driverName" />
    <result column="buyer_id" jdbcType="VARCHAR" property="buyerId" />
    <result column="buyer_name" jdbcType="VARCHAR" property="buyerName" />
    <result column="carrier_id" jdbcType="VARCHAR" property="carrierId" />
    <result column="carrier_name" jdbcType="VARCHAR" property="carrierName" />
    <result column="seller_id" jdbcType="VARCHAR" property="sellerId" />
    <result column="seller_name" jdbcType="VARCHAR" property="sellerName" />
    <result column="warehouse_id" jdbcType="VARCHAR" property="warehouseId" />
    <result column="warehouse_name" jdbcType="VARCHAR" property="warehouseName" />
    <result column="address_id" jdbcType="VARCHAR" property="addressId" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="quantity" jdbcType="DECIMAL" property="quantity" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="complete_time" jdbcType="TIMESTAMP" property="completeTime" />
    <result column="complete_content" jdbcType="VARCHAR" property="completeContent" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <select id="selectUnloadingObjectionList" parameterType="com.ecommerce.logistics.api.dto.unloadingobjection.UnloadingObjectionQueryDTO" resultType="com.ecommerce.logistics.api.dto.unloadingobjection.UnloadingObjectionListDTO">
    SELECT
        u.objection_id as objectionId,
        u.objection_num as objectionNum,
        u.proposer_type as proposerType,
        u.proposer_name as proposerName,
        u.relation_bill_id as relationBillId,
        u.relation_bill_num as relationBillNum,
        u.vehicle_num as vehicleNum,
        u.quantity as quantity,
        u.unit as unit,
        u.address as address,
        u.complete_time as completeTime,
        u.create_time as createTime,
        u.status as status
    FROM lgs_unloading_objection u
    WHERE u.del_flg = 0
    <if test="objectionNum != null and objectionNum !=''">
      AND u.objection_num like concat('%', #{objectionNum}, '%')
    </if>
    <if test="proposerType != null and proposerType !=''">
      AND u.proposer_type = #{proposerType}
    </if>
    <if test="proposerName != null and proposerName !=''">
      AND u.proposer_name like concat('%', #{proposerName}, '%')
    </if>
    <if test="relationBillNum != null and relationBillNum !=''">
      AND u.relation_bill_num like concat('%', #{relationBillNum}, '%')
    </if>
    <if test="vehicleNum != null and vehicleNum !=''">
      AND u.vehicle_num like concat('%', #{vehicleNum}, '%')
    </if>
    <if test="beginTime != null and endTime != null">
      AND u.create_time between #{beginTime} and #{endTime}
    </if>
    <if test="status != null and status !=''">
      AND u.status = #{status}
    </if>
    <choose>
        <when test="status != null and status == '030550300'">
          ORDER BY u.complete_time DESC
        </when>
        <when test="status != null and status == '030550400'">
          ORDER BY u.complete_time DESC
        </when>
        <otherwise>
          ORDER BY u.create_time DESC
        </otherwise>
    </choose>
  </select>

</mapper>