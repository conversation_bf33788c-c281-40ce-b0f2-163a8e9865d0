<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.UnloadingTimeMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.UnloadingTime">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="unloading_time_id" jdbcType="VARCHAR" property="unloadingTimeId" />
    <result column="unloading_id" jdbcType="VARCHAR" property="unloadingId" />
    <result column="customer_id" jdbcType="VARCHAR" property="customerId" />
    <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="user_type" jdbcType="INTEGER" property="userType" />
    <result column="unloading_time" jdbcType="INTEGER" property="unloadingTime" />
    <result column="unloading_km" jdbcType="DECIMAL" property="unloadingKm" />
    <result column="unloading_waiting_time" jdbcType="INTEGER" property="unloadingWaitingTime" />
    <result column="unloading_waiting_km" jdbcType="DECIMAL" property="unloadingWaitingKm" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <select id="queryUnloadingTimeList"
          resultType="com.ecommerce.logistics.api.dto.intelligentDispatch.UnloadingTimeDTO"
          parameterType="com.ecommerce.logistics.api.dto.intelligentDispatch.UnloadingTimeDTO">
    SELECT *
    FROM lgs_unloading_time
    <where>
      del_flg = 0
      <if test="unloadingId != null and unloadingId != ''">
        AND unloading_id = #{unloadingId}
      </if>
      <if test="customerId != null and customerId != ''">
        AND customer_id = #{customerId}
      </if>
      <if test="customerName != null and customerName != ''">
        AND customer_name LIKE CONCAT('%', #{customerName}, '%')
      </if>
      <if test="userId != null and userId != ''">
        AND user_id = #{userId}
      </if>
      <if test="userName != null and userName != ''">
        AND user_name LIKE CONCAT('%', #{userName}, '%')
      </if>
      <if test="userType != null">
        AND user_type = #{userType}
      </if>
    </where>
    ORDER BY update_time DESC
  </select>
</mapper>