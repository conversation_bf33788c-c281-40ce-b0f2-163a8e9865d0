<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.UserSettlementMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.UserSettlement">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="user_settlement_id" jdbcType="VARCHAR" property="userSettlementId" />
    <result column="carrier_id" jdbcType="VARCHAR" property="carrierId" />
    <result column="unsettlement_mileage" jdbcType="DECIMAL" property="unsettlementMileage" />
    <result column="settlemented_mileage" jdbcType="DECIMAL" property="settlementedMileage" />
    <result column="total_mileage" jdbcType="DECIMAL" property="totalMileage" />
    <result column="unsettlement_weight" jdbcType="DECIMAL" property="unsettlementWeight" />
    <result column="settlemented_weight" jdbcType="DECIMAL" property="settlementedWeight" />
    <result column="total_weight" jdbcType="DECIMAL" property="totalWeight" />
    <result column="settlement_period" jdbcType="VARCHAR" property="settlementPeriod" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="version" jdbcType="BIGINT" property="version" />
    <result column="carrier_name" jdbcType="VARCHAR" property="carrierName" />
    <result column="carrier_type" jdbcType="VARCHAR" property="carrierType" />
    <result column="unsettlement_carriage" jdbcType="DECIMAL" property="unsettlementCarriage" />
    <result column="settlemented_carriage" jdbcType="DECIMAL" property="settlementedCarriage" />
  </resultMap>
  <select id="querySettlementList" parameterType="com.ecommerce.logistics.api.dto.settlement.SettlementListQueryDTO"
          resultType="com.ecommerce.logistics.api.dto.settlement.SettlementListDTO">
    SELECT
      u.carrier_id AS carrierId,
      u.carrier_name AS carrierName,
      u.carrier_type AS carrierType,
      min(u.settlement_period) as settlementBeginPeriod,
      max(u.settlement_period) as settlementEndPeriod,
      SUM(u.unsettlement_mileage) As unsettlementMileage,
      SUM(u.settlemented_mileage) AS settlementedMileage,
      SUM(u.total_mileage) As totalMileage,
      SUM(u.unsettlement_weight) As unsettlementWeight,
      SUM(u.settlemented_weight) As settlementedWeight,
      SUM(u.total_weight) AS totalWeight,
      SUM(u.unsettlement_carriage) As unsettlementCarriage,
      SUM(u.settlemented_carriage) As settlementedCarriage
    FROM
      lgs_user_settlement u
    WHERE
      u.del_flg = 0
      <if test="carrierId != null and carrierId != '' ">
        AND u.carrier_id = #{carrierId}
      </if>
      <if test="carrierType != null and carrierType != '' ">
        AND u.carrier_type = #{carrierType}
      </if>
    GROUP BY
      u.carrier_id
    <if test="settlementStatus != null and settlementStatus == 1 ">
      HAVING SUM(u.unsettlement_carriage) > 0
    </if>
    <if test="settlementStatus != null and settlementStatus == 2 ">
      <![CDATA[HAVING SUM(u.unsettlement_carriage) <= 0]]>
    </if>
  </select>
  <select id="querySettlementListPeriod"
          resultType="com.ecommerce.logistics.dao.dto.settlement.SettlementPeriodDO">
    SELECT
      u.carrier_id as carrierId,
      min(u.settlement_period) as settlementBeginPeriod,
      max(u.settlement_period) as settlementEndPeriod
    FROM
      lgs_user_settlement u
    WHERE
      u.del_flg = 0 AND u.unsettlement_carriage > 0 AND u.carrier_id IN
      <foreach collection="list" close=")" index="index" item="carrierId" open="(" separator=",">
        #{carrierId}
      </foreach>
    GROUP BY u.carrier_id
  </select>
  <select id="queryMonthSettlementList" parameterType="com.ecommerce.logistics.api.dto.settlement.MonthSettlementListQueryDTO"
          resultType="com.ecommerce.logistics.api.dto.settlement.MonthSettlementListDTO">
    SELECT
      u.settlement_period AS settlementPeriod,
      u.user_settlement_id AS userSettlementId,
      u.carrier_id AS carrierId,
      u.carrier_name AS carrierName,
      u.carrier_type AS carrierType,
      u.unsettlement_mileage As unsettlementMileage,
      u.settlemented_mileage AS settlementedMileage,
      u.total_mileage As totalMileage,
      u.unsettlement_weight As unsettlementWeight,
      u.settlemented_weight As settlementedWeight,
      u.total_weight AS totalWeight,
      u.unsettlement_carriage As unsettlementCarriage,
      u.settlemented_carriage As settlementedCarriage
    FROM
      lgs_user_settlement u
    WHERE
      u.del_flg = 0
      <if test="carrierId != null and carrierId != '' ">
        <![CDATA[AND u.carrier_id = #{carrierId}]]>
      </if>
      <if test="settlementPeriodA != null and settlementPeriodA != '' ">
        <![CDATA[AND u.settlement_period >= #{settlementPeriodA}]]>
      </if>
      <if test="settlementPeriodB != null and settlementPeriodB != '' ">
        <![CDATA[AND u.settlement_period <= #{settlementPeriodB}]]>
      </if>
      <if test="settlementStatus != null and settlementStatus == 1 ">
        <![CDATA[AND u.unsettlement_carriage > 0]]>
      </if>
      <if test="settlementStatus != null and settlementStatus == 2 ">
        <![CDATA[AND u.unsettlement_carriage <= 0]]>
      </if>
    ORDER BY
      u.settlement_period DESC
  </select>
  <update id="userCarriageSettlement" parameterType="com.ecommerce.logistics.api.dto.settlement.CarriageSettlementDTO">
    UPDATE
	  lgs_user_settlement u
    SET
      u.settlemented_mileage = u.settlemented_mileage + u.unsettlement_mileage,
      u.settlemented_weight = u.settlemented_weight + u.unsettlement_weight,
      u.settlemented_carriage = u.settlemented_carriage + u.unsettlement_carriage,
      u.unsettlement_mileage = 0,
      u.unsettlement_weight = 0,
      u.unsettlement_carriage = 0
    <where>
      <if test="carrierId != null and carrierId != '' ">
        AND u.carrier_id = #{carrierId}
      </if>
      <if test="settlementMonth != null and settlementMonth != '' ">
        AND u.settlement_period = #{settlementMonth}
      </if>
    </where>
  </update>
  <select id="selectSettlementForLock" resultType="java.lang.String">
      select
        user_settlement_id
      from
        lgs_user_settlement
      where
        user_settlement_id = #{userSettlementId} for update;
  </select>
  <update id="updateSettlementForLock" parameterType="com.ecommerce.logistics.dao.dto.settlement.SettlementUpdateDO">
      update
        lgs_user_settlement
      set
        unsettlement_carriage = unsettlement_carriage + #{publishedCarriage},
        unsettlement_weight = unsettlement_weight + #{quantity},
        total_weight = total_weight + #{quantity},
        unsettlement_mileage = unsettlement_mileage + #{estimateKm},
        total_mileage = total_mileage + #{estimateKm}
      where
        user_settlement_id = #{userSettlementId};
  </update>
  <select id="queryUserSettlementAmount" resultType="com.ecommerce.logistics.api.dto.settlement.UserSettlementAmountDTO">
    select
      sum(unsettlement_carriage) as  unsettlementAmount,
      sum(settlemented_carriage) as  settlementedAmount
    from
      lgs_user_settlement
    where
      carrier_id=#{userId}
  </select>
</mapper>