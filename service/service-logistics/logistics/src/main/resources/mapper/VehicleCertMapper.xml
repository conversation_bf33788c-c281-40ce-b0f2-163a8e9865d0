<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.VehicleCertMapper">
    <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.VehicleCert">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="cert_id" jdbcType="VARCHAR" property="certId"/>
        <result column="cert_num" jdbcType="VARCHAR" property="certNum"/>
        <result column="vehicle_id" jdbcType="VARCHAR" property="vehicleId"/>
        <result column="cert_type" jdbcType="VARCHAR" property="certType"/>
        <result column="cert_json" jdbcType="VARCHAR" property="certJson"/>
        <result column="del_flg" jdbcType="INTEGER" property="delFlg"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="version" jdbcType="BIGINT" property="version"/>
    </resultMap>

    <select id="selectAllDrivingLicense" resultType="com.ecommerce.logistics.api.dto.vehiclecert.DrivingLicenseInfoDTO">
        SELECT
        c.vehicle_id as vehicleId,
        v.user_id as userId,
        c.cert_json as certJson
        FROM
        lgs_vehicle_cert c
        LEFT JOIN lgs_vehicle v on v.vehicle_id = c.vehicle_id and v.del_flg = 0
        where c.del_flg = 0 and c.cert_type = '047610100'
    </select>
</mapper>