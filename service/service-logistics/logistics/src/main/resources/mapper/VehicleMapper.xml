<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.VehicleMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.Vehicle">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="vehicle_id" jdbcType="VARCHAR" property="vehicleId" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="user_type" jdbcType="TINYINT" property="userType" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="color" jdbcType="VARCHAR" property="color" />
    <result column="number" jdbcType="VARCHAR" property="number" />
    <result column="transport_category_id" jdbcType="VARCHAR" property="transportCategoryId" />
    <result column="energy_type_id" jdbcType="VARCHAR" property="energyTypeId"/>
    <result column="vehicle_type_id" jdbcType="VARCHAR" property="vehicleTypeId" />
    <result column="load_capacity" jdbcType="DECIMAL" property="loadCapacity" />
    <result column="max_loading_capacity" jdbcType="DECIMAL" property="maxLoadingCapacity" />
    <result column="width" jdbcType="DECIMAL" property="width" />
    <result column="length" jdbcType="DECIMAL" property="length" />
    <result column="height" jdbcType="DECIMAL" property="height" />
    <result column="axles" jdbcType="VARCHAR" property="axles" />
    <result column="certification_status" jdbcType="VARCHAR" property="certificationStatus" />
    <result column="bind_driver_id" jdbcType="VARCHAR" property="bindDriverId" />
    <result column="driver_name" jdbcType="VARCHAR" property="driverName" />
    <result column="sim_number" jdbcType="VARCHAR" property="simNumber" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="version" jdbcType="BIGINT" property="version" />
    <result column="self_capacity" jdbcType="DECIMAL" property="selfCapacity" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="certification_fail_reason" jdbcType="VARCHAR" property="certificationFailReason" />
    <result column="gps_manufacturer_id" jdbcType="VARCHAR" property="gpsManufacturerId" />
    <result column="gps_device_number" jdbcType="VARCHAR" property="gpsDeviceNumber" />
    <result column="driver_phone" jdbcType="VARCHAR" property="driverPhone" />
    <result column="is_monitor" jdbcType="TINYINT" property="isMonitor" />
    <result column="is_default" jdbcType="TINYINT" property="isDefault" />
    <result column="unload_ability" jdbcType="TINYINT" property="unloadAbility" />
    <result column="carry_ability" jdbcType="TINYINT" property="carryAbility" />
    <result column="signal_flag" jdbcType="TINYINT" property="signalFlag" />
    <result column="is_gps_device" jdbcType="TINYINT" property="isGpsDevice" />
    <result column="disable_flg" jdbcType="TINYINT" property="disableFlg" />
  </resultMap>

    <select id="selectVehicleList" parameterType="com.ecommerce.logistics.api.dto.vehicle.VehicleListQueryDTO"
            resultType="com.ecommerce.logistics.api.dto.vehicle.VehicleListDTO">
        SELECT
        a.vehicle_id AS vehicleId,
        a.color AS color,
        a.number AS number,
        a.length AS length,
        a.width  AS width,
        a.height AS height,
        a.axles  AS axles,
        a.transport_category_id AS transportCategoryId,
        a.vehicle_type_id AS vehicleTypeId,
        tvt.type_name AS typeName,
        a.load_capacity AS loadCapacity,
        a.certification_status AS certificationStatus,
        a.gps_manufacturer_id AS gpsManufacturerId,
        a.gps_device_number AS gpsDeviceNumber,
        a.sim_number AS simNumber,
        a.user_id AS userId,
        a.user_name AS userName,
        a.user_type AS userType,
        a.self_capacity AS selfCapacity,
        a.bind_driver_id AS bindDriverId,
        a.driver_name AS driverName,
        a.driver_phone AS driverPhone,
        a.certification_fail_reason AS certificationFailReason,
        a.is_monitor AS isMonitor,
        a.signal_flag AS signalFlag,
        a.is_gps_device AS isGpsDevice,
        a.disable_flg AS disableFlg,
        a.is_default AS isDefault,
        ifnull(t3.vehicle_source, 1) as vehicleSource,
        t3.bind_status as bindStatus,
        a.max_loading_capacity AS maxLoadingCapacity,
        a.status as status,
        a.unload_ability as unloadAbility,
        a.carry_ability as carryAbility
        FROM
        lgs_vehicle a
        LEFT JOIN
        lgs_vehicle_type tvt ON a.vehicle_type_id = tvt.vehicle_type_id
        LEFT JOIN
        lgs_carrier_vehicle_map t3 ON t3.vehicle_id = a.vehicle_id and t3.del_flg = 0
        where
        a.del_flg = 0
        <if test="statusList != null and statusList.size()>0">
            AND a.certification_status in
            <foreach close=")" collection="statusList" index="index" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="color != null and color !=''">
            AND a.color = #{color}
        </if>
        <if test="number != null and number !=''">
            AND a.number like concat('%', #{number}, '%')
        </if>
        <if test="transportCategoryId != null and transportCategoryId !=''">
            AND a.transport_category_id like concat('%', #{transportCategoryId}, '%')
        </if>
        <if test="userId != null and userId !=''">
            AND a.user_id = #{userId}
        </if>
        <if test="userType != null and userType != ''">
            AND a.user_type = #{userType}
        </if>
        <if test="vehicleTypeId != null and vehicleTypeId !=''">
            AND a.vehicle_type_id = #{vehicleTypeId}
        </if>
        <if test="isMonitor != null and isMonitor !='' or isMonitor==0">
            AND a.is_monitor = #{isMonitor}
        </if>
        <if test="driverName != null and driverName !=''">
            AND a.driver_name LIKE CONCAT('%',#{driverName},'%')
        </if>
        <if test="signalFlag != null">
            AND a.signal_flag = #{signalFlag}
        </if>
        <if test="isGpsDevice != null">
            AND a.is_gps_device = #{isGpsDevice}
        </if>
        <if test="disableFlg != null">
            AND a.disable_flg = #{disableFlg}
        </if>
        <if test="vehicleSource != null and vehicleSource !=''">
        AND t3.vehicle_source = #{vehicleSource}
        </if>
        <!--不等于0，则只查询出绑定了司机的车辆-->
        <if test="hasBindDriver != null and hasBindDriver != 0">
            <![CDATA[ AND a.bind_driver_id <> '' ]]>
        </if>
        ORDER BY a.create_time DESC
    </select>

    <select id="selectVehicleMapList" parameterType="com.ecommerce.logistics.api.dto.vehicle.VehicleListQueryDTO"
            resultType="com.ecommerce.logistics.api.dto.vehicle.VehicleListDTO">
        SELECT
        a.vehicle_id AS vehicleId,
        a.color AS color,
        a.number AS number,
        a.length AS length,
        a.width  AS width,
        a.height AS height,
        a.axles  AS axles,
        a.transport_category_id AS transportCategoryId,
        a.vehicle_type_id AS vehicleTypeId,
        tvt.type_name AS typeName,
        a.load_capacity AS loadCapacity,
        a.certification_status AS certificationStatus,
        a.gps_manufacturer_id AS gpsManufacturerId,
        a.gps_device_number AS gpsDeviceNumber,
        a.sim_number AS simNumber,
        a.user_id AS userId,
        a.user_name AS userName,
        a.user_type AS userType,
        a.self_capacity AS selfCapacity,
        a.bind_driver_id AS bindDriverId,
        a.driver_name AS driverName,
        a.driver_phone AS driverPhone,
        a.certification_fail_reason AS certificationFailReason,
        a.is_monitor AS isMonitor,
        a.signal_flag AS signalFlag,
        a.is_gps_device AS isGpsDevice,
        a.disable_flg AS disableFlg,
        a.is_default AS isDefault,
        ifnull(t3.vehicle_source, 1) as vehicleSource,
        t3.bind_status as bindStatus,
        a.max_loading_capacity AS maxLoadingCapacity,
        a.status as status,
        a.unload_ability as unloadAbility,
        a.carry_ability as carryAbility
        FROM
        lgs_carrier_vehicle_map t3
        LEFT JOIN
        lgs_vehicle a ON t3.vehicle_id = a.vehicle_id and t3.del_flg = 0
        LEFT JOIN
        lgs_vehicle_type tvt ON a.vehicle_type_id = tvt.vehicle_type_id
        where
        a.del_flg = 0
        <if test="userId != null and userId !=''">
            AND t3.carrier_id = #{userId}
        </if>
        <if test="vehicleSource != null and vehicleSource != ''">
            AND t3.vehicle_source = #{vehicleSource}
        </if>
        <if test="statusList != null and statusList.size()>0">
            AND a.certification_status in
            <foreach close=")" collection="statusList" index="index" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="color != null and color !=''">
            AND a.color = #{color}
        </if>
        <if test="number != null and number !=''">
            AND a.number like concat('%', #{number}, '%')
        </if>
        <if test="transportCategoryId != null and transportCategoryId !=''">
            AND a.transport_category_id like concat('%', #{transportCategoryId}, '%')
        </if>
        <if test="vehicleTypeId != null and vehicleTypeId !=''">
            AND a.vehicle_type_id = #{vehicleTypeId}
        </if>
        <if test="signalFlag != null">
            AND a.signal_flag = #{signalFlag}
        </if>
        <if test="isGpsDevice != null">
            AND a.is_gps_device = #{isGpsDevice}
        </if>
        <if test="disableFlg != null">
            AND a.disable_flg = #{disableFlg}
        </if>
        <!--不等于0，则只查询出绑定了司机的车辆-->
        <if test="hasBindDriver != null and hasBindDriver != 0">
            <![CDATA[ AND a.bind_driver_id <> '' ]]>
        </if>
        ORDER BY t3.create_time DESC
    </select>

    <select id="queryVehicleListPlatform" parameterType="com.ecommerce.logistics.api.dto.vehicle.VehicleListQueryDTO"
            resultType="com.ecommerce.logistics.api.dto.vehicle.VehicleListDTO">
        SELECT
            a.vehicle_id AS vehicleId,
            a.color AS color,
            a.number AS number,
            a.length AS length,
            a.width  AS width,
            a.height AS height,
            a.axles  AS axles,
            a.transport_category_id AS transportCategoryId,
            a.vehicle_type_id AS vehicleTypeId,
            tvt.type_name AS typeName,
            a.load_capacity AS loadCapacity,
            a.certification_status AS certificationStatus,
            a.gps_manufacturer_id AS gpsManufacturerId,
            a.gps_device_number AS gpsDeviceNumber,
            a.sim_number AS simNumber,
            a.user_id AS userId,
            a.user_name AS userName,
            a.user_type AS userType,
            a.self_capacity AS selfCapacity,
            a.bind_driver_id AS bindDriverId,
            a.driver_name AS driverName,
            a.driver_phone AS driverPhone,
            a.certification_fail_reason AS certificationFailReason,
            a.is_monitor AS isMonitor,
            a.signal_flag AS signalFlag,
            a.is_gps_device AS isGpsDevice,
            a.disable_flg AS disableFlg,
            a.is_default AS isDefault,
            a.max_loading_capacity AS maxLoadingCapacity,
            a.status as status,
            a.unload_ability as unloadAbility,
            a.carry_ability as carryAbility
        FROM
            lgs_vehicle a
        LEFT JOIN lgs_vehicle_type tvt ON a.vehicle_type_id = tvt.vehicle_type_id
        WHERE
        a.del_flg = 0
        <if test="statusList != null and statusList.size()>0">
            AND a.certification_status in
            <foreach close=")" collection="statusList" index="index" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="color != null and color !=''">
            AND a.color = #{color}
        </if>
        <if test="number != null and number !=''">
            AND a.number like concat('%', #{number}, '%')
        </if>
        <if test="transportCategoryId != null and transportCategoryId !=''">
            AND a.transport_category_id like concat('%', #{transportCategoryId}, '%')
        </if>
        <if test="userId != null and userId !=''">
            AND a.user_id = #{userId}
        </if>
        <if test="userType != null and userType != ''">
            AND a.user_type = #{userType}
        </if>
        <if test="vehicleTypeId != null and vehicleTypeId !=''">
            AND a.vehicle_type_id = #{vehicleTypeId}
        </if>
        <if test="isMonitor != null and isMonitor !='' or isMonitor==0">
            AND a.is_monitor = #{isMonitor}
        </if>
        <if test="driverName != null and driverName !=''">
            AND a.driver_name LIKE CONCAT('%',#{driverName},'%')
        </if>
        <if test="signalFlag != null">
            AND a.signal_flag = #{signalFlag}
        </if>
        <if test="isGpsDevice != null">
            AND a.is_gps_device = #{isGpsDevice}
        </if>
        <if test="disableFlg != null">
            AND a.disable_flg = #{disableFlg}
        </if>
        ORDER BY a.create_time DESC
    </select>


    <update id="updateCertificationStatusToOk" parameterType="com.ecommerce.logistics.api.dto.vehicle.VehicleCertificationStatusDTO">
        UPDATE lgs_vehicle
        SET certification_status = #{certificationStatus}, update_user = #{updateUser}
        WHERE vehicle_id = #{vehicleId}
    </update>
    <update id="updateCertificationStatusToFailure" parameterType="com.ecommerce.logistics.api.dto.vehicle.VehicleCertificationStatusDTO">
        UPDATE lgs_vehicle
        SET certification_status = #{certificationStatus},certification_fail_reason = #{certificationFailReason}, update_user = #{updateUser}
        WHERE vehicle_id = #{vehicleId}
    </update>

    <select id="selectTotalCapacityByUserId" resultType="com.ecommerce.logistics.dao.dto.dispatchbill.CapacityDO">
      SELECT
        user_id as carrierId,
        SUM(load_capacity) as capacity
      FROM
        lgs_vehicle
      WHERE
        del_flg = 0
        AND user_type = #{userType}
        AND transport_category_id = #{capacityQueryDTO.transportCategoryId}
        AND user_id in
        <foreach close=")" collection="capacityQueryDTO.carrierIdList" index="index" item="item" open="(" separator=",">
          #{item}
        </foreach>
      GROUP BY user_id
    </select>
    <select id="selectVehiclesByUserId" parameterType="com.ecommerce.logistics.api.dto.vehicle.UserVehicleListQueryDTO" resultType="com.ecommerce.logistics.api.dto.vehicle.VehicleListDTO">
    	SELECT
          t1.vehicle_id as vehicleId,
    	  t1.color as color,
          t1.number as number,
          t2.type_name
    	FROM
          lgs_vehicle as t1
        LEFT JOIN
          lgs_vehicle_type as t2 on t1.vehicle_type_id=t2.vehicle_type_id
        WHERE t1.user_id = #{userId} AND t1.del_flg = 0 AND t1.user_type = #{userType}
          <if test="signalFlag != null">
            AND t1.signal_flag = #{signalFlag}
          </if>
          <if test="isGpsDevice != null">
            and t1.is_gps_device =#{isGpsDevice}
          </if>
          <if test="transportCategoryId != null and transportCategoryId != ''">
            AND t2.transport_category_id LIKE CONCAT('%', #{transportCategoryId}, '%')
          </if>
          <if test="number != null and number != ''">
            AND t1.number LIKE CONCAT('%', #{number}, '%')
          </if>
          <if test="disableFlg != null">
            AND t1.disable_flg = #{disableFlg}
          </if>
    </select>

    <select id="selectVehicleInfoByUserId" resultMap="BaseResultMap">
    	SELECT number,driver_name,driver_phone,load_capacity
    	 FROM lgs_vehicle
    	WHERE user_id = #{userId} AND user_type = #{userType} AND del_flg = 0 order by number
    </select>

    <select id="selectVehicleInfoByUserIdAndSignalFlag" parameterType="com.ecommerce.logistics.api.dto.vehicle.UserVehicleListQueryDTO" resultMap="BaseResultMap">
        SELECT
          t1.number,
          t1.driver_name,
          t1.driver_phone,
          t1.load_capacity
        FROM
          lgs_vehicle as t1
    	WHERE t1.user_id = #{userId} AND t1.user_type = #{userType} AND t1.del_flg = 0
          <if test="signalFlag != null">
            AND t1.signal_flag = #{signalFlag}
          </if>
          <if test="transportCategoryId != null and transportCategoryId != ''">
            AND t1.transport_category_id like concat('%', #{transportCategoryId}, '%')
          </if>
          <if test="disableFlg != null">
            AND t1.disable_flg = #{disableFlg}
          </if>
    	ORDER BY
          t1.number
    </select>

    <select id="selectVehicleCountByUser" parameterType="java.util.Map" resultType="int">
    	SELECT COUNT(1)
    	FROM lgs_vehicle WHERE user_id = #{userId} AND del_flg = 0 AND user_type = #{userType}
    </select>

    <select id="selectVehicleInfoByNumber" parameterType="java.lang.String" resultMap="BaseResultMap">
    	SELECT driver_name,driver_phone FROM lgs_vehicle WHERE number = #{number} AND del_flg = 0
    </select>
    <update id="deleteVehicle" parameterType="com.ecommerce.logistics.api.dto.vehicle.VehicleRemoveDTO">
            UPDATE lgs_vehicle
            SET del_flg = 1, update_user = #{updateUser}
            WHERE vehicle_id = #{vehicleId}
    </update>

    <update id="updateVehicle" parameterType="com.ecommerce.logistics.api.dto.vehicle.VehicleEditDTO">
        UPDATE lgs_vehicle
        SET
        color = #{color},
        number = #{number},
        transport_category_id = #{transportCategoryId},
        vehicle_type_id = #{vehicleTypeId},
        load_capacity = #{loadCapacity},
        width = #{width},
        height = #{height},
        length = #{length},
        axles = #{axles},
        <if test="bindDriverId != null and bindDriverId != ''">
            bind_driver_id = #{bindDriverId},
        </if>
        <if test="driverName != null and driverName != ''">
            driver_name = #{driverName},
        </if>
        <if test="driverPhone != null and driverPhone != ''">
            driver_phone = #{driverPhone},
        </if>
        <if test="isGpsDevice != null">
            is_gps_device = #{isGpsDevice},
        </if>
        <if test="maxLoadingCapacity != null">
            max_loading_capacity = #{maxLoadingCapacity},
        </if>
        sim_number = #{simNumber},
        update_user = #{updateUser},
        self_capacity = #{selfCapacity},
        gps_manufacturer_id = #{gpsManufacturerId},
        gps_device_number = #{gpsDeviceNumber},
        version = version + 1
        WHERE vehicle_id = #{vehicleId}
    </update>
    <select id="selectAppVehicleData" parameterType="java.lang.String"
            resultType="com.ecommerce.logistics.api.dto.vehicle.VehicleAppDataDTO">
        SELECT
          lv.color,
          lv.number,
          lv.transport_category_id AS transportCategoryId,
          lv.load_capacity AS loadCapacity,
          lv.vehicle_type_id AS vehicleTypeId,
          lv.energy_type_id AS energyTypeId,
          tvt.type_name AS vehicleType,
          lv.gps_manufacturer_id AS gpsManufacturerId,
          lv.gps_device_number AS gpsDeviceNumber,
          lv.sim_number AS simNumber,
          lv.vehicle_id AS vehicleId,
          lv.bind_driver_id AS bindDriverId,
          lv.driver_name AS driverName,
          lv.driver_phone AS driverPhone,
          lv.length,
          lv.width,
          lv.height,
          lv.self_capacity AS selfCapacity,
          lv.signal_flag AS signalFlag,
          lv.is_gps_device AS isGpsDevice,
          lv.disable_flg AS disableFlg,
          lv.user_type AS userType,
          lv.certification_status as certificationStatus,
          lv.certification_fail_reason as certificationFailReason,
          lv.max_loading_capacity as maxLoadingCapacity,
          lv.status as status,
          lv.unload_ability as unloadAbility,
          lv.carry_ability as carryAbility
        FROM
          lgs_vehicle lv
        LEFT JOIN
          lgs_vehicle_type tvt ON lv.vehicle_type_id = tvt.vehicle_type_id
        WHERE
          lv.user_id = #{userId} AND lv.del_flg = 0
    </select>
    <select id="selectAppVehicleList" parameterType="java.lang.String"
            resultType="com.ecommerce.logistics.api.dto.vehicle.VehicleAppListDTO">
      SELECT
      bind_driver_id AS bindDriverId,
      driver_name AS driverName,
      driver_phone AS  driverPhone,
      load_capacity AS loadCapacity,
      vehicle_type_id AS vehicleTypeId,
      vehicle_id AS vehicleId,
      number,
      is_default AS isDefault,
      is_monitor AS isMonitor,
      is_gps_device as isGpsDevice,
      color,
      max_loading_capacity AS maxLoadingCapacity
      FROM lgs_vehicle
      WHERE user_id = #{userId} AND del_flg = 0
      ORDER BY is_default DESC
    </select>

    <insert id="batchInsertAppVehicle">
        INSERT INTO lgs_vehicle(
            color,
            vehicle_id,
            load_capacity,
            `number`,
            vehicle_type_id,
            transport_category_id,
            driver_name,
            driver_phone,
            is_monitor,
            is_default,
            is_gps_device,
            create_time,
            user_id,
            user_name,
            user_type,
            create_user,
            certification_status)
        VALUES
            <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
                #{item.color},
                #{item.vehicleId},
                #{item.loadCapacity},
                #{item.number},
                #{item.vehicleTypeId},
                #{item.transportCategoryId},
                #{item.driverName},
                #{item.driverPhone},
                #{item.isMonitor},
                #{item.isDefault},
                #{item.isGpsDevice},
                #{item.createTime},
                #{item.userId},
                #{item.userName},
                #{item.userType},
                #{item.createUser},
                #{item.certificationStatus}
            </foreach>
    </insert>

    <select id="selectAppVehicleListByUserDTO" parameterType="com.ecommerce.logistics.api.dto.vehicle.VehicleUserCondDTO"
            resultType="com.ecommerce.logistics.api.dto.vehicle.VehicleAppListDTO">
        SELECT
          lv.bind_driver_id AS bindDriverId,
          lv.driver_name AS driverName,
          lv.driver_phone AS  driverPhone,
          lv.load_capacity AS loadCapacity,
          lv.vehicle_type_id AS vehicleTypeId,
          lv.vehicle_id AS vehicleId,
          lv.number,
          lv.is_default AS isDefault,
          lv.is_monitor AS isMonitor,
          lv.color,
          lv.disable_flg AS disableFlg,
          lvt.type_name AS vehicleType,
          lv.max_loading_capacity AS maxLoadingCapacity
        FROM lgs_vehicle lv
        left join lgs_vehicle_type lvt on lv.vehicle_type_id = lvt.vehicle_type_id
        WHERE lv.user_id = #{userId} AND lv.user_type = #{userType} AND lv.del_flg = 0
        <if test="transportIdList != null">
          and
          <foreach collection="transportIdList" item="ti" index="tix" open="(" separator=" or " close=")">
            lv.transport_category_id like concat('%', #{ti}, '%')
          </foreach>
        </if>
        <if test="specialFlag != null and specialFlag == 1">
          and lv.is_gps_device = 1 and lv.signal_flag = 1
        </if>
        <if test="disableFlg != null">
          AND lv.disable_flg = #{disableFlg}
        </if>
        ORDER BY lv.is_default DESC
    </select>

    <insert id="insertAppVehicle" parameterType="com.ecommerce.logistics.api.dto.vehicle.VehicleAppAddDTO">
        INSERT INTO lgs_vehicle
        (
        color,
        vehicle_id,
        load_capacity,
        number,
        vehicle_type_id,
        transport_category_id,
        <if test="driverName != null and driverName != ''">
            driver_name,
        </if>
        <if test="driverPhone != null and driverPhone != ''">
            driver_phone,
        </if>
        is_monitor,
        is_default,
        is_gps_device,
        create_time,
        user_id,
        user_name,
        user_type,
        create_user,
        certification_status
        )
        VALUES(#{color},#{vehicleId},#{loadCapacity},#{number},#{vehicleTypeId},
        ifnull((select lvt.transport_category_id from lgs_vehicle_type lvt where lvt.vehicle_type_id = #{vehicleTypeId}),'')
        ,
        <if test="driverName != null and driverName != ''">
            #{driverName},
        </if>
        <if test="driverPhone != null and driverPhone != ''">
            #{driverPhone},
        </if>
        #{isMonitor},#{isDefault},
        <if test="isGpsDevice != null">
            #{isGpsDevice},
        </if>
        <if test="isGpsDevice == null">
          0,
        </if>
        #{createTime},#{userId},#{userName},#{userType},#{createUser},#{certificationStatus})
    </insert>

    <update id="updateAppVehicle" parameterType="com.ecommerce.logistics.api.dto.vehicle.VehicleAppEditDTO">
        UPDATE
          lgs_vehicle
        SET
            load_capacity = #{loadCapacity},
            number = #{number},
            color = #{color},
            vehicle_type_id = #{vehicleTypeId},
            driver_name = #{driverName},
            driver_phone = #{driverPhone},
            update_user = #{updateUser}
            <if test="isGpsDevice != null">
                ,is_gps_device = #{isGpsDevice}
            </if>
            <if test="isDefault != null">
                ,is_default = #{isDefault}
            </if>
            <if test="vehicleTypeId != null and vehicleTypeId != ''">
              ,transport_category_id = ifnull(
                (select lvt.transport_category_id from lgs_vehicle_type lvt where lvt.vehicle_type_id = #{vehicleTypeId} and del_flg = 0),
               '')
            </if>
        WHERE
          vehicle_id = #{vehicleId}
    </update>
    <select id="selectUserIdsByOverCapacity" resultType="java.lang.String">
    	SELECT
    		DISTINCT user_id
    	FROM
    		lgs_vehicle
    	WHERE
    		<![CDATA[load_capacity >= #{capacity}]]> AND del_flg = 0
    		AND
    		user_id in
    		<foreach collection="userIds" close=")" index="index" item="id" open="(" separator=",">
    			#{id}
    		</foreach>
    </select>
    <update id="cleanDefaultStatus">
        UPDATE lgs_vehicle
        SET is_default = 0
        WHERE user_id = #{userId};
    </update>
    <update id="setDefault">
        UPDATE lgs_vehicle
        SET is_default = 1
        WHERE vehicle_id = #{vehicleId}
    </update>
    <select id="selectVehicleBaseData" resultType="com.ecommerce.logistics.api.dto.vehicle.VehicleBaseDataDTO">
        SELECT vehicle_id, vehicle_type_id, number, driver_name, driver_phone,certification_status,is_monitor
        FROM lgs_vehicle
        WHERE user_id = #{userId} AND del_flg = 0
    </select>
    <select id="queryVehicleOptionsDTO" resultType="com.ecommerce.logistics.api.dto.vehicle.VehicleOptionsDTO" parameterType="com.ecommerce.logistics.api.dto.vehicle.VehicleOptionsQueryDTO">
        SELECT distinct `number` AS label,
               <if test="ignoreIdFlag == null or ignoreIdFlag == 0">
                   vehicle_id AS `value`,
                   user_type as userType,
                   user_id as userId,
               </if>
               <if test="ignoreIdFlag != null and ignoreIdFlag != 0">
                   concat(user_name, `number`) as `value`,
               </if>
               user_name as userName
        FROM lgs_vehicle
        WHERE del_flg = 0
        <if test="keyword != null and keyword != ''">
          AND `number` LIKE CONCAT('%',#{keyword},'%')
        </if>
        AND certification_status = #{certificationStatus}
        <if test="userId != null and userId != ''">
          and user_id = #{userId}
        </if>
        <if test="userType != null" >
            AND user_type = #{userType}
        </if>
        <if test="includeUserTypeList != null">
          and user_type in
          <foreach collection="includeUserTypeList" item="iut" index="iut_in" open="(" close=")" separator=",">
            #{iut}
          </foreach>
        </if>
        <if test="transportCategoryId != null and transportCategoryId != ''">
          and transport_category_id like concat('%', #{transportCategoryId}, '%')
        </if>
        <if test="signalFlag != null">
            AND signal_flag = #{signalFlag}
        </if>
        <if test="vehicleNumList != null and vehicleNumList.size() > 0">
            and number in
            <foreach collection="vehicleNumList" index="vin" item="vit" open="(" separator="," close=")">
                #{vit}
            </foreach>
        </if>
        order by user_type, update_time desc
        <if test="limitSize != null and limitSize > 0">
            limit #{limitSize}
        </if>
    </select>

    <select id="queryVehicleOptionsDTOForCarrier" resultType="com.ecommerce.logistics.api.dto.vehicle.VehicleOptionsDTO"
            parameterType="com.ecommerce.logistics.api.dto.vehicle.VehicleOptionsQueryDTO">
        SELECT distinct m.`number` AS label,
        <if test="ignoreIdFlag == null or ignoreIdFlag == 0">
            m.vehicle_id AS `value`,
            v.user_type as userType,
            m.belonger_id as userId,
        </if>
        <if test="ignoreIdFlag != null and ignoreIdFlag != 0">
            concat(v.user_name, m.`number`) as `value`,
        </if>
        m.belonger_name as userName
        FROM lgs_carrier_vehicle_map as m
        left join lgs_vehicle as v on m.vehicle_id = v.vehicle_id
        WHERE m.del_flg = 0 and v.del_flg = 0
        <if test="keyword != null and keyword != ''">
            AND m.`number` LIKE CONCAT('%',#{keyword},'%')
        </if>
        <if test="vehicleNumList != null and vehicleNumList.size() > 0">
            and m.number in
            <foreach collection="vehicleNumList" index="vin" item="vit" open="(" separator="," close=")">
                #{vit}
            </foreach>
        </if>
        <if test="transportCategoryId != null and transportCategoryId != ''">
            and v.transport_category_id like concat('%', #{transportCategoryId}, '%')
        </if>
        <if test="certificationStatus != null and certificationStatus != ''">
            AND v.certification_status = #{certificationStatus}
        </if>
        <if test="signalFlag != null and signalFlag > 0">
            and v.signal_flag = #{signalFlag}
        </if>
        order by m.number, m.update_time desc
        <if test="limitSize != null and limitSize > 0">
            limit #{limitSize}
        </if>
    </select>

    <update id="submitAuthentication" parameterType="com.ecommerce.logistics.api.dto.vehicle.VehicleCertificationStatusDTO">
        UPDATE lgs_vehicle
        SET certification_status = #{certificationStatus}, update_user = #{updateUser}, certification_fail_reason = #{certificationFailReason}
        WHERE vehicle_id = #{vehicleId}
    </update>

    <update id="updateVehicleDisableFlg" parameterType="com.ecommerce.logistics.api.dto.vehicle.VehicleDisableFlgEditDTO">
        UPDATE lgs_vehicle
        SET disable_flg = #{disableFlg}, update_user = #{updateUser}
        WHERE vehicle_id = #{vehicleId}
    </update>

    <select id="getCertificationStatus" resultType="java.lang.String">
        SELECT DISTINCT certification_status
        FROM lgs_vehicle
        WHERE user_id = #{userId} AND  user_type = 4 AND del_flg = 0
    </select>

    <select id="findAssignDefaultVehicle" parameterType="com.ecommerce.logistics.api.dto.vehicle.VehicleDefaultCondDTO"
            resultType="com.ecommerce.logistics.api.dto.vehicle.VehicleDefaultResDTO">
      select
        lv.vehicle_id as vehicleId,
        lvt.type_name as vehicleType,
        lv.number as number,
        lv.bind_driver_id as driverId,
        lv.driver_name as driverName,
        lv.transport_category_id as transportCategoryId,
        lv.driver_phone as driverPhone
      from lgs_vehicle lv
      left join lgs_vehicle_type lvt on lv.vehicle_type_id = lvt.vehicle_type_id
      where lv.del_flg = 0
        and lv.disable_flg = 0
      <if test="userId != null and userId != ''">
        and lv.user_id = #{userId}
      </if>
      <if test="userType != null">
        and lv.user_type = #{userType}
      </if>
      <if test="transportCategoryIdList != null and transportCategoryIdList.size() > 0">
        and
        <foreach collection="transportCategoryIdList" open="(" separator=" and " close=")" index="trix" item="trit">
          lv.transport_category_id like concat('%', #{trit}, '%')
        </foreach>
      </if>
      <if test="isGpsDevice != null and isGpsDevice > 0">
        and lv.is_gps_device = #{isGpsDevice}
      </if>
      <if test="signalFlag != null and signalFlag > 0">
        and lv.signal_flag = #{signalFlag}
      </if>
      <if test="minVersion != null and minVersion > 0">
        and lv.version &gt;= #{minVersion}
      </if>
      <if test="mustDriverId != null and mustDriverId > 0">
        and lv.bind_driver_id is not null and lv.bind_driver_id != ''
      </if>
      order by lv.version desc, lv.update_time desc
      limit 1
    </select>

    <select id="findByAssignCondWithoutTransportCategory" resultMap="BaseResultMap"
            parameterType="com.ecommerce.logistics.api.dto.driver.AssignDriverLogAddDTO">
      select lv.*
      from lgs_vehicle lv
      where lv.del_flg = 0 and lv.number = #{number}
      and lv.user_id = #{userId} and lv.user_type = #{userType}
      <if test="vehicleId != null and vehicleId != ''">
        and lv.vehicle_id = #{vehicleId}
      </if>
      order by version desc, update_time desc
      limit 1
    </select>

    <select id="selectOneIdByUserAndNumber" resultType="java.lang.String">
      select vehicle_id
      from lgs_vehicle
      where del_flg = 0 and user_id = #{userId} and user_type = #{userType} and number = #{number}
      order by update_time desc
      limit 1
    </select>

    <update id="batchUpdate" parameterType="java.util.List">
        UPDATE lgs_vehicle SET
            color = CASE vehicle_id
            <foreach collection="list" item="item" index="index" open="" separator="" close="">
                WHEN #{item.vehicleId} THEN #{item.color}
            </foreach>
            END,
            vehicle_type_id = CASE vehicle_id
            <foreach collection="list" item="item" index="index" open="" separator="" close="">
                WHEN #{item.vehicleId} THEN #{item.vehicleTypeId}
            </foreach>
            END,
            transport_category_id = CASE vehicle_id
            <foreach collection="list" item="item" index="index" open="" separator="" close="">
                WHEN #{item.vehicleId} THEN #{item.transportCategoryId}
            </foreach>
            END,
            self_capacity = CASE vehicle_id
            <foreach collection="list" item="item" index="index" open="" separator="" close="">
                WHEN #{item.vehicleId} THEN #{item.selfCapacity}
            </foreach>
            END,
            load_capacity = CASE vehicle_id
            <foreach collection="list" item="item" index="index" open="" separator="" close="">
                WHEN #{item.vehicleId} THEN #{item.loadCapacity}
            </foreach>
            END,
            `length` = CASE vehicle_id
            <foreach collection="list" item="item" index="index" open="" separator="" close="">
                WHEN #{item.vehicleId} THEN #{item.length}
            </foreach>
            END,
            width = CASE vehicle_id
            <foreach collection="list" item="item" index="index" open="" separator="" close="">
                WHEN #{item.vehicleId} THEN #{item.width}
            </foreach>
            END,
            height = CASE vehicle_id
            <foreach collection="list" item="item" index="index" open="" separator="" close="">
                WHEN #{item.vehicleId} THEN #{item.height}
            </foreach>
            END,
            axles = CASE vehicle_id
            <foreach collection="list" item="item" index="index" open="" separator="" close="">
                WHEN #{item.vehicleId} THEN #{item.axles}
            </foreach>
            END,
            certification_status = CASE vehicle_id
            <foreach collection="list" item="item" index="index" open="" separator="" close="">
                WHEN #{item.vehicleId} THEN #{item.certificationStatus}
            </foreach>
            END,
            certification_fail_reason = CASE vehicle_id
            <foreach collection="list" item="item" index="index" open="" separator="" close="">
                WHEN #{item.vehicleId} THEN #{item.certificationFailReason}
            </foreach>
            END,
            is_monitor = CASE vehicle_id
            <foreach collection="list" item="item" index="index" open="" separator="" close="">
                WHEN #{item.vehicleId} THEN #{item.isMonitor}
            </foreach>
            END,
            is_default = CASE vehicle_id
            <foreach collection="list" item="item" index="index" open="" separator="" close="">
                WHEN #{item.vehicleId} THEN #{item.isDefault}
            </foreach>
            END,
            signal_flag = CASE vehicle_id
            <foreach collection="list" item="item" index="index" open="" separator="" close="">
                WHEN #{item.vehicleId} THEN #{item.signalFlag}
            </foreach>
            END,
            is_gps_device = CASE vehicle_id
            <foreach collection="list" item="item" index="index" open="" separator="" close="">
                WHEN #{item.vehicleId} THEN #{item.isGpsDevice}
            </foreach>
            END,
            bind_driver_id = CASE vehicle_id
            <foreach collection="list" item="item" index="index" open="" separator="" close="">
                WHEN #{item.vehicleId} THEN #{item.bindDriverId}
            </foreach>
            END,
            driver_name = CASE vehicle_id
            <foreach collection="list" item="item" index="index" open="" separator="" close="">
                WHEN #{item.vehicleId} THEN #{item.driverName}
            </foreach>
            END,
            driver_phone = CASE vehicle_id
            <foreach collection="list" item="item" index="index" open="" separator="" close="">
                WHEN #{item.vehicleId} THEN #{item.driverPhone}
            </foreach>
            END,
            gps_manufacturer_id = CASE vehicle_id
            <foreach collection="list" item="item" index="index" open="" separator="" close="">
                WHEN #{item.vehicleId} THEN #{item.gpsManufacturerId}
            </foreach>
            END,
            gps_device_number = CASE vehicle_id
            <foreach collection="list" item="item" index="index" open="" separator="" close="">
                WHEN #{item.vehicleId} THEN #{item.gpsDeviceNumber}
            </foreach>
            END,
            sim_number = CASE vehicle_id
            <foreach collection="list" item="item" index="index" open="" separator="" close="">
                WHEN #{item.vehicleId} THEN #{item.simNumber}
            </foreach>
            END
        WHERE
            vehicle_id IN
            <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
                #{item.vehicleId}
            </foreach>
    </update>
    <select id="selectVehicleListByNumber" resultType="com.ecommerce.logistics.api.dto.vehicle.ExternalVehicleAppDTO">
    	SELECT
			v.number AS number,
			v.vehicle_id AS vehicleId,
			v.vehicle_type_id AS type,
			v.user_id AS bindDriverId,
			v.color AS color,
			v.transport_category_id AS transportCategoryId,
			v.length AS length,
			v.width AS width,
			v.height AS height,
			v.driver_name AS driverName,
			v.driver_phone AS driverPhone,
			v.user_type AS userType,
			v.max_loading_capacity AS maxLoadingCapacity,
			v.status AS status,
			v.unload_ability AS unloadAbility,
			v.carry_ability AS carryAbility
		FROM
			lgs_vehicle v
			LEFT JOIN lgs_vehicle_type vt ON vt.vehicle_type_id = v.vehicle_type_id
		WHERE
			v.del_flg = 0 AND vt.del_flg = 0
			AND v.number LIKE CONCAT('%',#{number},'%')
			<if test="userType != null and userType > 0">
			    AND v.user_type = #{userType}
			</if>
    </select>

    <select id="queryPersonUserId" resultType="java.lang.String">
        SELECT DISTINCT user_id
        from lgs_vehicle as lv
        LEFT JOIN lgs_carrier_vehicle_map as m on lv.vehicle_id = m.vehicle_id
        WHERE lv.user_type = 4
         and lv.user_id in
        <foreach close=")" collection="memberIds" index="index" item="item" open="(" separator=",">
            #{item}
        </foreach>
         and lv.del_flg = 0
         and m.del_flg = 0
         and m.bind_status = 1
    </select>

  <select id="queryByIds" resultMap="BaseResultMap">
    select *
    from lgs_vehicle
    where vehicle_id in
    <foreach collection="vehicleIds" index="vin" item="vid" open="(" separator="," close=")">
      #{vid}
    </foreach>
  </select>

  <select id="queryPersonVehicleByUserId" resultType="com.ecommerce.logistics.api.dto.driver.PersonVehicleDTO">
    select lv.number as vehicleNumber,
           lvt.vehicle_type_id as vehicleTypeId,
           lvt.type_name as vehicleTypeName,
           lv.max_loading_capacity as maxLoadingCapacity,
           lv.color as vehicleColor,
           lv.transport_category_id as transportCategoryId,
           lv.energy_type_id as energyTypeId
    from lgs_vehicle as lv, lgs_vehicle_type as lvt
    where lv.vehicle_type_id = lvt.vehicle_type_id
      and lv.user_id = #{userId} and lv.user_type = 4
      and lv.del_flg = 0 and lvt.del_flg = 0
    order by lv.update_time desc
    limit 1
  </select>
  <select id="selectVehicleNumber" parameterType="com.ecommerce.logistics.api.dto.vehicle.UserVehicleListQueryDTO" resultType="java.lang.String">
  	SELECT
        t1.number
    FROM
        lgs_vehicle t1
    WHERE
        t1.del_flg = 0
    AND t1.disable_flg = 0
    AND t1.user_id = #{userId}
    AND t1.user_type = #{userType}
    <if test="number != null and number != ''">
        AND t1.number LIKE CONCAT('%', #{number}, '%')
    </if>
  </select>

    <select id="queryVehicleListDropDownBox" parameterType="com.ecommerce.logistics.api.dto.vehicle.UserVehicleListQueryDTO"
            resultType="com.ecommerce.logistics.api.dto.vehicle.UserVehicleListDTO">
        SELECT
            t1.number
        FROM
            lgs_vehicle t1
        WHERE
            t1.del_flg = 0
        AND t1.disable_flg = 0
        AND t1.user_id = #{userId}
        AND t1.user_type = #{userType}
        <if test="number != null and number != ''">
            AND t1.number LIKE CONCAT('%', #{number}, '%')
        </if>
    </select>

</mapper>
