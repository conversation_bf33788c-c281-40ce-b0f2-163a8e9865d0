<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.VehicleTypeMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.VehicleType">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="vehicle_type_id" jdbcType="VARCHAR" property="vehicleTypeId" />
    <result column="type_name" jdbcType="VARCHAR" property="typeName" />
    <result column="note" jdbcType="VARCHAR" property="note" />
    <result column="axles" jdbcType="VARCHAR" property="axles" />
    <result column="carriage_type" jdbcType="VARCHAR" property="carriageType" />
    <result column="max_load_capacity" jdbcType="DECIMAL" property="maxLoadCapacity" />
    <result column="max_self_capacity" jdbcType="DECIMAL" property="maxSelfCapacity" />
    <result column="max_volume" jdbcType="DECIMAL" property="maxVolume" />
    <result column="max_length" jdbcType="DECIMAL" property="maxLength" />
    <result column="max_width" jdbcType="DECIMAL" property="maxWidth" />
    <result column="max_height" jdbcType="DECIMAL" property="maxHeight" />
    <result column="unlimite_type" jdbcType="TINYINT" property="unlimiteType" />
    <result column="verification_number" jdbcType="TINYINT" property="verificationNumber" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deposit_amount" jdbcType="DECIMAL" property="depositAmount" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="version" jdbcType="BIGINT" property="version" />
    <result column="transport_category_id" jdbcType="VARCHAR" property="transportCategoryId" />
  </resultMap>
    <select id="selectVehicleTypeList" parameterType="com.ecommerce.logistics.api.dto.vehicletype.VehicleTypeListQueryDTO" resultType="com.ecommerce.logistics.api.dto.vehicletype.VehicleTypeListDTO">
      SELECT
      vehicle_type_id as vehicleTypeId,
      type_name as typeName,
      note,
      axles,
      carriage_type as carriageType,
      max_load_capacity as maxLoadCapacity,
      max_self_capacity as maxSelfCapacity,
      max_volume as maxVolume,
      max_length as maxLength,
      max_width as maxWidth,
      max_height as maxHeight,
      transport_category_id as transportCategoryId,
      deposit_amount as depositAmount,
      verification_number AS verificationNumber
      FROM
        lgs_vehicle_type t
      <where>
        t.del_flg = 0
        <if test="typeName != null and typeName != ''">
          AND `type_name` LIKE  CONCAT('%',#{typeName},'%')
        </if>
        <if test="carriageType != null and carriageType != ''">
          AND `carriage_type` = #{carriageType}
        </if>
      </where>
      ORDER BY
      	update_time DESC
    </select>
    <update id="updateVehicleType" parameterType="com.ecommerce.logistics.api.dto.vehicletype.VehicleTypeEditDTO">
      UPDATE
      lgs_vehicle_type
      SET
      type_name = #{typeName},
      note = #{note},
      axles = #{axles},
      carriage_type = #{carriageType},
      max_load_capacity = #{maxLoadCapacity},
      max_self_capacity = #{maxSelfCapacity},
      max_volume = #{maxVolume},
      max_length = #{maxLength},
      max_width = #{maxWidth},
      max_height = #{maxHeight}
      <if test="depositAmount != null">
        ,deposit_amount = #{depositAmount}
      </if>
      <if test="verificationNumber != null">
        ,verification_number = #{verificationNumber}
      </if>
      WHERE
      vehicle_type_id = #{vehicleTypeId}
    </update>
  <select id="queryOptions" resultType="com.ecommerce.logistics.api.dto.vehicletype.VehicleTypeOptionDTO">
    SELECT
    vehicle_type_id AS vehicleTypeId,
    type_name AS typeName,
    axles,
    transport_category_id AS transportCategoryId,
    max_load_capacity AS maxLoadCapacity,
    max_self_capacity AS maxSelfCapacity,
    max_length AS maxLength,
    max_width AS maxWidth,
    max_height AS maxHeight,
    deposit_amount as depositAmount,
    unlimite_type as unlimiteType,
    verification_number as verificationNumber
    FROM lgs_vehicle_type
    WHERE del_flg = 0
  </select>
  <select id="queryTypeNameByIds" parameterType="java.util.List" resultType="com.ecommerce.logistics.api.dto.vehicletype.VehicleTypeNameDTO">
    SELECT type_name as typeName,
           vehicle_type_id as vehicleTypeId,
           axles as axles,
           carriage_type as carriageType
    FROM lgs_vehicle_type
    WHERE del_flg = 0
      AND vehicle_type_id IN
    <foreach close=")" collection="ids" index="index" item="item" open="(" separator=",">
      #{item}
    </foreach>
  </select>

  <select id="queryTypeNameById" parameterType="java.lang.String" resultType="java.lang.String">
    SELECT type_name
    FROM lgs_vehicle_type
    WHERE vehicle_type_id = #{vehicleTypeId}
    AND del_flg = 0
  </select>


  <!-- 查询是否存在不限车型 -->
  <select id="querytVehicleType" parameterType="java.lang.String" resultType="java.lang.Integer">
    SELECT count(*) AS total
    FROM lgs_vehicle_type
    WHERE axles = #{axles} AND carriage_type = #{carriageType}
    AND del_flg = 0
  </select>


  <!-- 查询出不限车型 -->
  <select id="querytUnlimiteType" parameterType="java.lang.String" resultType="com.ecommerce.logistics.api.dto.vehicletype.VehicleTypeNameDTO">
    SELECT type_name AS typeName, vehicle_type_id AS vehicleTypeId,axles AS axles, carriage_type AS carriageType
    FROM lgs_vehicle_type
    WHERE axles = #{axles} AND carriage_type = #{carriageType}
    AND del_flg = 0
  </select>

  <select id="queryDepositAmountByDriverUserId" resultType="java.math.BigDecimal">
    select lvt.deposit_amount
    from lgs_vehicle_type as lvt, lgs_vehicle as lv
    where lv.vehicle_type_id = lvt.vehicle_type_id
      and lv.user_id = #{userId} and lv.user_type = 4
      and lv.del_flg = 0 and lvt.del_flg = 0
    order by lv.update_time desc
    limit 1
  </select>

</mapper>