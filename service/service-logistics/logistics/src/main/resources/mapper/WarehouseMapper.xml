<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.WarehouseMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.Warehouse">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="warehouse_id" jdbcType="VARCHAR" property="warehouseId" />
    <result column="number" jdbcType="VARCHAR" property="number" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="district" jdbcType="VARCHAR" property="district" />
    <result column="district_code" jdbcType="VARCHAR" property="districtCode" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="location" jdbcType="VARCHAR" property="location" />
    <result column="administrator" jdbcType="VARCHAR" property="administrator" />
    <result column="administrator_phone" jdbcType="VARCHAR" property="administratorPhone" />
    <result column="area" jdbcType="INTEGER" property="area" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="version" jdbcType="BIGINT" property="version" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="user_type" jdbcType="TINYINT" property="userType" />
    <result column="note" jdbcType="VARCHAR" property="note" />
  </resultMap>
  <update id="deleteWarehouse" parameterType="String">
    UPDATE lgs_warehouse
    SET del_flg = 1
    WHERE warehouse_id = #{warehouseId}
  </update>
  <select id="selectWarehouseList" parameterType="com.ecommerce.logistics.api.dto.warehouse.WarehouseListQueryDTO" resultType="com.ecommerce.logistics.api.dto.warehouse.WarehouseListDTO">
    SELECT
      warehouse_id,
      number,
      name,
      type,
      province,
      province_code,
      city,
      city_code,
      district,
      district_code,
      address,
      location,
      administrator,
      administrator_phone,
      area,
      user_name,
      note,
      user_id
    FROM lgs_warehouse
    <where>
      del_flg = 0
      <if test="type != null and type != ''">
        AND `type` = #{type}
      </if>
      <if test="provinceCode != null and provinceCode != ''">
        AND `province_code` = #{provinceCode}
      </if>
      <if test="cityCode != null and cityCode != ''">
        AND `city_code` = #{cityCode}
      </if>
      <if test="districtCode != null and districtCode !=''">
        AND `district_code` = #{districtCode}
      </if>
      <if test="name != null">
        AND `name` LIKE CONCAT('%', #{name},'%')
      </if>
      <if test="userId != null and userId !=''">
        AND user_id = #{userId}
      </if>
      <if test="userType != null and userType !=''">
        AND user_type = #{userType}
      </if>
    </where>
    ORDER BY create_time DESC
  </select>
  
  <select id="selectWarehouseNameByPK" parameterType="java.lang.String" resultType="java.lang.String">
  	SELECT name FROM lgs_warehouse WHERE warehouse_id = #{warehouseId}
  </select>
  
  <select id="selectWarehouseIdLikeName" parameterType="java.lang.String" resultType="java.lang.String">
  	SELECT warehouse_id FROM lgs_warehouse 
  	WHERE name LIKE '%${warehouseName}%' AND del_flg = 0
  	ORDER BY warehouse_id
  </select>

  <select id="selectWarehouseIdAndName" parameterType="com.ecommerce.logistics.api.dto.warehouse.WarehouseOptionQueryDTO" resultType="com.ecommerce.logistics.api.dto.warehouse.WarehouseOptionDTO">
    SELECT
      warehouse_id AS value,
      name AS label,
      type as type
    FROM
      lgs_warehouse
    WHERE name LIKE CONCAT('%',#{name},'%') AND del_flg = 0
    <if test="userId != null and userId != ''">
      AND user_id = #{userId}
    </if>
    <if test="type != null and type != ''">
      AND type = #{type}
    </if>
  </select>
  
  <select id="selectWarehouseNameAndIDByWarehouseIds" parameterType="java.util.List" resultType="com.ecommerce.logistics.api.dto.warehouse.WarehouseOptionDTO">
  	SELECT `warehouse_id` AS `value`,`name` AS `label`
    FROM lgs_warehouse
    WHERE del_flg = 0 AND warehouse_id in
    <foreach close=")" collection="warehouseIds" index="index" item="id" open="(" separator=",">
    	#{id}
    </foreach>
  </select>

  <select id="queryZoneMapByWarehouseId" resultType="com.ecommerce.logistics.api.dto.warehouse.WarehouseZoneMapDTO">
    SELECT
    province AS province,
    province_code AS provinceCode,
    city AS city,
    city_code AS cityCode,
    district AS district,
    district_code AS districtCode,
    street AS street,
    street_code AS streetCode
    FROM lgs_warehouse_zone_map
    WHERE warehouse_id = #{warehouseId} AND del_flg = 0
  </select>
  <select id="queryWarehouseDetails" resultMap="selectWarehouseDetails">
    SELECT
      warehouse_id AS warehouseId,
      name AS name,
      type AS type,
      province AS province,
      province_code AS provinceCode,
      city AS city,
      city_code AS cityCode,
      district AS district,
      district_code AS districtCode,
      address AS address,
      location AS location,
      administrator AS administrator,
      administrator_phone AS administratorPhone,
      area AS area,
      user_id AS userId,
      user_type AS userType,
      user_name AS userName,
      note AS note,
      number AS number
    FROM
      lgs_warehouse
    WHERE warehouse_id = #{warehouseId} AND del_flg = 0
  </select>
  <resultMap id="selectWarehouseDetails" type="com.ecommerce.logistics.api.dto.warehouse.WarehouseDetailsDTO">
    <collection column="warehouseId" ofType="com.ecommerce.logistics.api.dto.warehouse.WarehouseZoneMapDTO" property="deliveryAreas" select="queryZoneMapByWarehouseId" />
  </resultMap>
  
  <select id="selectWarehouseIdByAdministrator" parameterType="java.lang.String" resultType="java.lang.String">
  	SELECT warehouse_id FROM lgs_warehouse WHERE administrator_id = #{administratorId}
  </select>
  
  <update id="updateWarehouse" parameterType="com.ecommerce.logistics.api.dto.warehouse.WarehouseEditDTO">
    UPDATE lgs_warehouse
    SET name = #{name},
    type = #{type},
    province = #{province},
    province_code = #{provinceCode},
    city = #{city},
    city_code = #{cityCode},
    district = #{district},
    district_code = #{districtCode},
    location = #{location},
    note = #{note},
    area = #{area},
    administrator = #{administrator},
    administrator_phone = #{administratorPhone},
    update_user = #{updateUser},
    address = #{address}
    WHERE warehouse_id = #{warehouseId}
  </update>

  <update id="deleteWarehouseById" parameterType="com.ecommerce.logistics.api.dto.warehouse.WarehouseRemoveDTO">
    UPDATE lgs_warehouse
    SET del_flg = 1, update_user = #{updateUser}
    WHERE warehouse_id = #{warehouseId}
  </update>
  <select id="selectWarehouseBaseData" parameterType="java.lang.String" resultType="com.ecommerce.logistics.api.dto.warehouse.WarehouseBaseDataDTO">
    SELECT warehouse_id,
    number,
    name,
    province,
    city,
    district,
    address,
    administrator,
    administrator_phone
    FROM lgs_warehouse
    WHERE warehouse_id = #{warehouseId}
  </select>

  <select id="queryWarehouseDetail" resultType="com.ecommerce.logistics.api.dto.warehouse.WarehouseDetailsDTO">
    SELECT
      warehouse_id AS warehouseId,
      name AS name,
      type AS type,
      province AS province,
      province_code AS provinceCode,
      city AS city,
      city_code AS cityCode,
      district AS district,
      district_code AS districtCode,
      address AS address,
      location AS location,
      administrator AS administrator,
      administrator_phone AS administratorPhone,
      area AS area,
      user_id AS userId,
      user_type AS userType,
      user_name AS userName,
      note AS note,
      number AS number
    FROM
      lgs_warehouse
    WHERE
      warehouse_id = #{warehouseId}
  </select>
  
  <select id="selectWarehouseByMainWaybillId" parameterType="java.lang.String"
  resultMap="BaseResultMap">
  	SELECT 
  		DISTINCT t3.province,t3.city,t3.district,t3.address,t3.name,t3.location
  	FROM
  		lgs_waybill t1
  	JOIN
  		lgs_picking_bill t2 ON t2.picking_bill_id = t1.picking_bill_id
  	JOIN
  		lgs_warehouse t3 ON t3.warehouse_id = t2.warehouse_id
  	WHERE
  		t1.parent_id = #{mainWaybillId}
  </select>
  
</mapper>