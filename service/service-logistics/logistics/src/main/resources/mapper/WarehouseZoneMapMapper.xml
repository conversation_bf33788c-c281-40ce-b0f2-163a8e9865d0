<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.WarehouseZoneMapMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.WarehouseZoneMap">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="address_map_id" jdbcType="VARCHAR" property="addressMapId" />
    <result column="warehouse_id" jdbcType="VARCHAR" property="warehouseId" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="district" jdbcType="VARCHAR" property="district" />
    <result column="district_code" jdbcType="VARCHAR" property="districtCode" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="version" jdbcType="BIGINT" property="version" />
    <result column="street" jdbcType="VARCHAR" property="street" />
    <result column="street_code" jdbcType="VARCHAR" property="streetCode" />
  </resultMap>
  <select id="queryProvinceWarehouse" resultType="com.ecommerce.logistics.dao.vo.WarehouseZoneMap">
    SELECT
      warehouse_id as warehouseId,
      province as province,
      province_code as provinceCode,
      city as city,
      city_code as cityCode,
      district as district,
      district_code as districtCode
    FROM
      lgs_warehouse_zone_map
    WHERE
      del_flg=0 and city_code='' and district_code='' and type IN
      <foreach close=")" collection="typeList" index="index" item="type" open="(" separator=",">
        #{type}
      </foreach>
      and province_code IN
      <foreach close=")" collection="list" index="index" item="item" open="(" separator=",">
        #{item.provinceCode}
      </foreach>
  </select>
  <select id="queryCityWarehouse" resultType="com.ecommerce.logistics.dao.vo.WarehouseZoneMap">
    SELECT
      warehouse_id as warehouseId,
      province as province,
      province_code as provinceCode,
      city as city,
      city_code as cityCode,
      district as district,
      district_code as districtCode
    FROM
      lgs_warehouse_zone_map
    WHERE
      del_flg=0 and district_code='' and type IN
      <foreach close=")" collection="typeList" index="index" item="type" open="(" separator=",">
        #{type}
      </foreach>
      and city_code IN
      <foreach close=")" collection="list" index="index" item="item" open="(" separator=",">
        #{item.cityCode}
      </foreach>
  </select>
  <select id="queryDistrictWarehouse" resultType="com.ecommerce.logistics.dao.vo.WarehouseZoneMap">
    SELECT
      warehouse_id as warehouseId,
      province as province,
      province_code as provinceCode,
      city as city,
      city_code as cityCode,
      district as district,
      district_code as districtCode
    FROM
      lgs_warehouse_zone_map
    WHERE
      del_flg=0 and type IN
      <foreach close=")" collection="typeList" index="index" item="type" open="(" separator=",">
        #{type}
      </foreach>
      and district_code IN
      <foreach close=")" collection="list" index="index" item="item" open="(" separator=",">
        #{item.districtCode}
      </foreach>
  </select>

  <update id="deleteZoneMap">
    UPDATE
    lgs_warehouse_zone_map
    SET del_flg = 1
    WHERE warehouse_id = #{warehouseId}
  </update>
  <insert id="insertZoneMapList" parameterType="com.ecommerce.logistics.dao.vo.WarehouseZoneMap">
    INSERT INTO lgs_warehouse_zone_map (
    address_map_id,
    warehouse_id,
    type,
    province,
    province_code,
    city,
    city_code,
    del_flg,
    create_user,
    create_time,
    street,
    street_code,
    district,
    district_code,
    version
    )
    VALUES
    <foreach collection="list" item="item" separator=",">
      (#{item.addressMapId},
      #{item.warehouseId},
      #{item.type},
      #{item.province},
      #{item.provinceCode},
      <choose>
        <when test="item.city != null and item.city != ''">
          #{item.city},
          #{item.cityCode},
        </when>
        <otherwise>
          '','',
        </otherwise>
      </choose>
      #{item.delFlg},
      #{item.createUser},
      #{item.createTime},
      <choose>
        <when test="item.street != null and item.street != ''">
          #{item.street},
          #{item.streetCode},
        </when>
        <otherwise>
          '', '',
        </otherwise>
      </choose>
      <choose>
        <when test="item.district != null and item.district != ''">
          #{item.district},
          #{item.districtCode},
        </when>
        <otherwise>
          '', '',
        </otherwise>
      </choose>
      #{item.version}
      )
    </foreach>
  </insert>
</mapper>