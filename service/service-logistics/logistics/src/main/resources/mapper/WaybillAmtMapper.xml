<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.WaybillAmtMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.WaybillAmt">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="waybill_amt_id" jdbcType="VARCHAR" property="waybillAmtId" />
    <result column="waybill_id" jdbcType="VARCHAR" property="waybillId" />
    <result column="main_waybill_id" jdbcType="VARCHAR" property="mainWaybillId" />
    <result column="waybill_num" jdbcType="VARCHAR" property="waybillNum" />
    <result column="seller_id" jdbcType="VARCHAR" property="sellerId" />
    <result column="seller_name" jdbcType="VARCHAR" property="sellerName" />
    <result column="buyer_id" jdbcType="VARCHAR" property="buyerId" />
    <result column="buyer_name" jdbcType="VARCHAR" property="buyerName" />
    <result column="carrier_id" jdbcType="VARCHAR" property="carrierId" />
    <result column="carrier_name" jdbcType="VARCHAR" property="carrierName" />
    <result column="quantity" jdbcType="DECIMAL" property="quantity" />
    <result column="goods_price" jdbcType="DECIMAL" property="goodsPrice" />
    <result column="goods_amount" jdbcType="DECIMAL" property="goodsAmount" />
    <result column="receivable_carriage_price" jdbcType="DECIMAL" property="receivableCarriagePrice" />
    <result column="receivable_carriage_amount" jdbcType="DECIMAL" property="receivableCarriageAmount" />
    <result column="payable_carriage_amount" jdbcType="DECIMAL" property="payableCarriageAmount" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="version" jdbcType="BIGINT" property="version" />
  </resultMap>

  <update id="updateParentIdByWaybillIdList">
    update lgs_waybill_amt
    set update_time = current_timestamp,
        main_waybill_id = #{mainWaybillId}
    where waybill_id in
    <foreach collection="waybillIdList" index="win" item="wit" open="(" separator="," close=")">
      #{wit}
    </foreach>
    and del_flg = 0
  </update>

  <select id="queryByWaybillId" parameterType="java.lang.String" resultMap="BaseResultMap">
    select * from lgs_waybill_amt where waybill_id = #{waybillId} and del_flg = 0
  </select>

</mapper>