<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.WaybillCompositeMapper">
  <update id="updateSubWaybillStatus">
    UPDATE
      lgs_waybill
    SET
      status = #{status},
      parent_id = #{parentId},
      is_main_waybill=#{isMainWaybill}
    WHERE
      del_flg=0 and waybill_id IN
    <foreach collection="waybillIdList" close=")" index="index" item="waybillId" open="(" separator=",">
      #{waybillId}
    </foreach>
  </update>
  <update id="updateSubWaybillStatusByParentId">
    UPDATE
      lgs_waybill
    SET
      status = #{status}
    WHERE
      del_flg=0 and parent_id=#{parentId} and is_main_waybill = 0 and status=#{currentStatus}
  </update>
  <update id="updateSubWaybillByParentId">
  	UPDATE
      lgs_waybill
    SET
      actual_quantity = 0.00,
      status = #{status},
      carrier_id = #{carrierId}
    WHERE
      del_flg=0 and parent_id=#{parentId} and is_main_waybill = 0 and status=#{currentStatus}
  </update>
  <update id="updateStatusByWaybillIds" parameterType="java.util.Map">
  	UPDATE
  		lgs_waybill
    SET
      status = #{status},
      update_user = #{userId},
      update_time = CURRENT_TIMESTAMP()
     WHERE
      waybill_id in
      	<foreach collection="waybillIds" close=")" index="index" item="waybillId" open="(" separator=",">
      		#{waybillId}
      	</foreach>
  </update>
  <update id="updateWaybillCarrier">
    UPDATE
      lgs_waybill
    SET
      carrier_id=#{carrierId},
      update_time = CURRENT_TIMESTAMP()
    WHERE
      parent_id=#{parentId}
  </update>
  <update id="updateStatusToCancelByWaybillIds" parameterType="java.util.Map">
  	UPDATE
  		lgs_waybill
    SET
      status = #{status},
      update_user = #{userId},
      cancel_time = NOW(),
      update_time = CURRENT_TIMESTAMP()
     WHERE
      waybill_id in
      	<foreach collection="waybillIds" close=")" index="index" item="waybillId" open="(" separator=",">
      		#{waybillId}
      	</foreach>
  </update>
  <update id="updateStatusToCompleteByWaybillIds" parameterType="java.util.Map">
  	UPDATE
  		lgs_waybill
    SET
      status = #{status},
      update_user = #{userId},
      complete_time = NOW(),
      update_time = CURRENT_TIMESTAMP()
     WHERE
      waybill_id in
      	<foreach collection="waybillIds" close=")" index="index" item="waybillId" open="(" separator=",">
      		#{waybillId}
      	</foreach>
  </update>
</mapper>