<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.WaybillInfoMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.WaybillInfo">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="waybill_info_id" jdbcType="VARCHAR" property="waybillInfoId" />
    <result column="waybill_id" jdbcType="VARCHAR" property="waybillId" />
    <result column="driver_id" jdbcType="VARCHAR" property="driverId" />
    <result column="driver_name" jdbcType="VARCHAR" property="driverName" />
    <result column="driver_phone" jdbcType="VARCHAR" property="driverPhone" />
    <result column="vehicle_num" jdbcType="VARCHAR" property="vehicleNum" />
    <result column="estimate_km" jdbcType="DECIMAL" property="estimateKm" />
    <result column="actual_km" jdbcType="DECIMAL" property="actualKm" />
    <result column="estimate_duration" jdbcType="DECIMAL" property="estimateDuration" />
    <result column="estimate_carriage" jdbcType="DECIMAL" property="estimateCarriage" />
    <result column="published_carriage" jdbcType="DECIMAL" property="publishedCarriage" />
    <result column="approval_time" jdbcType="TIMESTAMP" property="approvalTime" />
    <result column="receive_time" jdbcType="TIMESTAMP" property="receiveTime" />
    <result column="assign_time" jdbcType="TIMESTAMP" property="assignTime" />
    <result column="arrive_warehouse_time" jdbcType="TIMESTAMP" property="arriveWarehouseTime" />
    <result column="monitor_enter_time" jdbcType="TIMESTAMP" property="monitorEnterTime" />
    <result column="leave_warehouse_time" jdbcType="TIMESTAMP" property="leaveWarehouseTime" />
    <result column="actual_quantity" jdbcType="DECIMAL" property="actualQuantity" />
    <result column="monitor_outer_time" jdbcType="TIMESTAMP" property="monitorOuterTime" />
    <result column="cancel_reason" jdbcType="VARCHAR" property="cancelReason" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="version" jdbcType="BIGINT" property="version" />
    <result column="actual_carriage" jdbcType="DECIMAL" property="actualCarriage" />
  </resultMap>
  
  <update id="updateDelFlgByWaybillIds" parameterType="java.util.Map">
  	UPDATE
  		lgs_waybill_info
  	SET
		del_flg = 1,
		update_user = #{operatorId},
		update_time = CURRENT_TIMESTAMP()
	WHERE
  		waybill_id in
  		<foreach close=")" collection="waybillIds" index="index" item="id" open="(" separator=",">
	 		#{id}
	 	</foreach>
  </update>
  
  <update id="updateVehicleNumByWaybillId" parameterType="java.util.Map">
  	UPDATE
  		lgs_waybill_info
  	SET
  		vehicle_num = #{dto.vehicleNum},
  		driver_id = #{dto.driverId},
  		driver_name = #{dto.driverName},
  		driver_phone = #{dto.driverPhone},
  		update_user = #{operatorId},
		update_time = CURRENT_TIMESTAMP()
	WHERE
		waybill_id = #{dto.waybillId}
  </update>
  
  <select id="selectWaybillInfoByWaybillId" parameterType="java.lang.String" resultMap="BaseResultMap">
  	SELECT * FROM lgs_waybill_info WHERE waybill_id = #{waybillId}
  </select>

</mapper>