<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.WaybillMapMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.WaybillMap">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="waybill_map_id" jdbcType="VARCHAR" property="waybillMapId" />
    <result column="primary_waybill_id" jdbcType="VARCHAR" property="primaryWaybillId" />
    <result column="primary_waybill_num" jdbcType="VARCHAR" property="primaryWaybillNum" />
    <result column="primary_delivery_sheet_num" jdbcType="VARCHAR" property="primaryDeliverySheetNum" />
    <result column="secondary_waybill_id" jdbcType="VARCHAR" property="secondaryWaybillId" />
    <result column="secondary_waybill_num" jdbcType="VARCHAR" property="secondaryWaybillNum" />
    <result column="secondary_delivery_sheet_num" jdbcType="VARCHAR" property="secondaryDeliverySheetNum" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="version" jdbcType="BIGINT" property="version" />
  </resultMap>

  <select id="findPrimaryERPBySecondaryWaybillId" resultType="com.ecommerce.logistics.dao.dto.waybillmap.PrimaryWaybillErpInfo">
    select
    m.secondary_waybill_id as secondaryWaybillId,
    w.waybill_id as primaryWaybillId,
    w.waybill_num as primaryWaybillNum,
    w.qr_code as qrCode,
    w.external_waybill_num as externalWaybillNum,
    w.external_waybill_status as externalWaybillStatus,
    w.sync_fail_reason as syncFailReason
    from lgs_waybill_map as m
    left join lgs_waybill as w on w.waybill_id = m.primary_waybill_id
    where
    m.del_flg = 0 and m.secondary_waybill_id = #{secondaryWaybillId}
  </select>

  <select id="findPrimaryERPBySecondaryWaybillIdList" resultType="com.ecommerce.logistics.dao.dto.waybillmap.PrimaryWaybillErpInfo">
    select
      m.secondary_waybill_id as secondaryWaybillId,
      w.waybill_id as primaryWaybillId,
      w.waybill_num as primaryWaybillNum,
      w.qr_code as qrCode,
      w.external_waybill_num as externalWaybillNum,
      w.external_waybill_status as externalWaybillStatus,
      w.sync_fail_reason as syncFailReason
    from lgs_waybill_map as m
    left join lgs_waybill as w on w.waybill_id = m.primary_waybill_id
    where m.del_flg = 0
      and m.secondary_waybill_id in
        <foreach collection="secondaryWaybillIdList" index="sin" item="sit" open="(" separator="," close=")">
          #{sit}
        </foreach>
  </select>


</mapper>