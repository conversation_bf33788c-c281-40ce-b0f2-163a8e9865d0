<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.WaybillMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.Waybill">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="waybill_id" jdbcType="VARCHAR" property="waybillId" />
    <result column="waybill_num" jdbcType="VARCHAR" property="waybillNum" />
    <result column="dispatch_bill_id" jdbcType="VARCHAR" property="dispatchBillId" />
    <result column="carrier_id" jdbcType="VARCHAR" property="carrierId" />
    <result column="carrier_type" jdbcType="VARCHAR" property="carrierType" />
    <result column="picking_bill_id" jdbcType="VARCHAR" property="pickingBillId" />
    <result column="is_main_waybill" jdbcType="TINYINT" property="isMainWaybill" />
    <result column="parent_id" jdbcType="VARCHAR" property="parentId" />
    <result column="special_id" jdbcType="VARCHAR" property="specialId" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="bill_proxy_type" jdbcType="VARCHAR" property="billProxyType" />
    <result column="can_operate" jdbcType="TINYINT" property="canOperate" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="qr_code" jdbcType="VARCHAR" property="qrCode" />
    <result column="end_reason" jdbcType="VARCHAR" property="endReason" />
    <result column="delivery_time" jdbcType="DATE" property="deliveryTime" />
    <result column="delivery_time_range" jdbcType="VARCHAR" property="deliveryTimeRange" />
    <result column="picking_time" jdbcType="TIMESTAMP" property="pickingTime" />
    <result column="income_carriage" jdbcType="DECIMAL" property="incomeCarriage" />
    <result column="sla_status" jdbcType="VARCHAR" property="slaStatus" />
    <result column="arrive_destination_time" jdbcType="TIMESTAMP" property="arriveDestinationTime" />
    <result column="monitor_arrive_time" jdbcType="TIMESTAMP" property="monitorArriveTime" />
    <result column="begin_carry_time" jdbcType="TIMESTAMP" property="beginCarryTime" />
    <result column="complete_time" jdbcType="TIMESTAMP" property="completeTime" />
    <result column="monitor_complete_time" jdbcType="TIMESTAMP" property="monitorCompleteTime" />
    <result column="cancel_time" jdbcType="TIMESTAMP" property="cancelTime" />
    <result column="actual_quantity" jdbcType="DECIMAL" property="actualQuantity" />
    <result column="sign_quantity" jdbcType="DECIMAL" property="signQuantity" />
    <result column="sign_remark" jdbcType="VARCHAR" property="signRemark" />
    <result column="empty_load_charge" jdbcType="DECIMAL" property="emptyLoadCharge" />
    <result column="empty_load_flag" jdbcType="TINYINT" property="emptyLoadFlag" />
    <result column="empty_load_quantity" jdbcType="DECIMAL" property="emptyLoadQuantity" />
    <result column="waybill_sequence" jdbcType="INTEGER" property="waybillSequence" />
    <result column="need_monitor" jdbcType="TINYINT" property="needMonitor" />
    <result column="can_monitor" jdbcType="TINYINT" property="canMonitor" />
    <result column="driver_evaluate_flag" jdbcType="TINYINT" property="driverEvaluateFlag" />
    <result column="platform_evaluate_flag" jdbcType="TINYINT" property="platformEvaluateFlag" />
    <result column="delivery_certificate_flag" jdbcType="TINYINT" property="deliveryCertificateFlag" />
    <result column="objection_flag" jdbcType="TINYINT" property="objectionFlag" />
    <result column="ready_flag" jdbcType="TINYINT" property="readyFlag" />
    <result column="deals_id" jdbcType="VARCHAR" property="dealsId" />
    <result column="deals_name" jdbcType="VARCHAR" property="dealsName" />
    <result column="project_name" jdbcType="VARCHAR" property="projectName" />
    <result column="invoice_target_type" jdbcType="VARCHAR" property="invoiceTargetType" />
    <result column="invoice_user_id" jdbcType="VARCHAR" property="invoiceUserId" />
    <result column="invoice_status" jdbcType="VARCHAR" property="invoiceStatus" />
    <result column="invoice_message" jdbcType="VARCHAR" property="invoiceMessage" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="version" jdbcType="BIGINT" property="version" />
    <result column="close_reason" jdbcType="VARCHAR" property="closeReason" />
    <result column="close_time" jdbcType="TIMESTAMP" property="closeTime" />
    <result column="sync_flag" jdbcType="VARCHAR" property="syncFlag" />
    <result column="sync_fail_reason" jdbcType="VARCHAR" property="syncFailReason" />
    <result column="external_waybill_num" jdbcType="VARCHAR" property="externalWaybillNum" />
    <result column="external_waybill_status" jdbcType="VARCHAR" property="externalWaybillStatus" />
    <result column="external_request_no" jdbcType="VARCHAR" property="externalRequestNo" />
    <result column="origin_place" jdbcType="VARCHAR" property="originPlace" />
    <result column="enter_factory_time" jdbcType="TIMESTAMP" property="enterFactoryTime" />
  </resultMap>
  
  <select id="selectSubWaybillIdByParentId" parameterType="java.lang.String" resultType="java.lang.String">
  	SELECT 
  		waybill_id
  	FROM
  		lgs_waybill
  	WHERE
  		parent_id = #{parentId} AND is_main_waybill = 0
  </select>
  
  <select id="selectWaybillByWaybillIds" parameterType="java.util.List" resultMap="BaseResultMap">
  	SELECT
  		*
  	FROM
  		lgs_waybill
  	WHERE
  		del_flg = 0 AND waybill_id IN
  		<foreach close=")" collection="waybillIds" index="index" item="waybillId" open="(" separator=",">
  			#{waybillId}
  		</foreach>
  </select>
  <select id="selectSnatchedWaybillList" resultType="com.ecommerce.logistics.dao.dto.waybill.SnatchedWaybillDO">
    SELECT
       t1.entry_id as waybillId,
       MAX(t1.create_time) AS snatchTime,
       t2.vehicle_num as vehicleNum,
       t4.warehouse_id as warehouseId,
       t4.province as receiveProvince,
       t4.city as receiveCity,
       t4.district as receiveDistrict
     FROM
       lgs_operation_record AS t1
     INNER JOIN
       lgs_waybill_info as t2 on t1.entry_id=t2.waybill_id
     INNER JOIN
       lgs_waybill as t3 on t2.waybill_id=t3.waybill_id
     INNER JOIN
       lgs_picking_bill as t4 on t3.picking_bill_id=t4.picking_bill_id
     WHERE
       t1.type = 1 AND t1.operation_type = 4 and t2.vehicle_num != ''
     GROUP BY t1.entry_id ORDER BY t1.create_time DESC
  </select>

  <update id="updateECWaybill" parameterType="com.ecommerce.logistics.dao.dto.waybill.ECUpdateDO">
    UPDATE
      lgs_waybill_info as t1
    INNER JOIN
      lgs_product_quantity_map as t2 on t1.waybill_id=t2.map_id
    SET
      t1.vehicle_num = #{vehicleNum},
      t2.quantity = #{quantity},
      t1.update_time = CURRENT_TIMESTAMP()
    WHERE
      t1.waybill_id = #{waybillId}
  </update>

  <update id="updateExternalWaybillInfo" parameterType="com.ecommerce.logistics.dao.dto.waybill.UpdateExternalWaybillDO">
    UPDATE
      lgs_waybill AS t1
    SET
      t1.update_time=NOW(),
      t1.sync_fail_reason='',
      t1.external_waybill_num=#{externalWaybillNum},
      t1.external_waybill_status=#{externalWaybillStatus},
      t1.qr_code=#{qrCode},
      t1.origin_place = #{originPlace}
    WHERE
      t1.waybill_id = #{waybillId}
  </update>

  <update id="closeExpireWaybillList">
    update lgs_waybill
    set status = '030070900',
    end_reason = '030360200',
    update_time = current_timestamp
    where waybill_id in
    <foreach collection="waybillIdList" item="wit" index="wii" open="(" separator="," close=")">
      #{wit}
    </foreach>
    and del_flg = 0
  </update>

</mapper>