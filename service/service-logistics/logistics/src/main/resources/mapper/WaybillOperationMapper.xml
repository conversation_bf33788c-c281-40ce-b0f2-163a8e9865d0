<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.WaybillOperationMapper">
    <update id="updateCarriage" parameterType="com.ecommerce.logistics.api.dto.waybill.ChangeCarriageDTO">
        UPDATE lgs_waybill_info
        SET
        update_user = #{operationUserId},
        published_carriage = #{publishedCarriage}
        WHERE
        waybill_id = #{waybillId}
    </update>
    <update id="updateStatusToWaitReceiveById" parameterType="String">
        UPDATE lgs_waybill
        SET
        status = 4
        WHERE
        waybill_id = #{waybillId}
    </update>
	<update id="updateDelFlgByDispatchBillId" parameterType="java.util.Map">
	  	UPDATE
	  		lgs_waybill
	  	SET
	  		del_flg = 1,
	  		update_user = #{operatorId},
	  		update_time = CURRENT_TIMESTAMP()
	  	WHERE
	  		dispatch_bill_id = #{dispatchBillId}
	  </update>
    <update id="updateCarrierIdById" parameterType="com.ecommerce.logistics.api.dto.waybill.WaybillAssignDTO">
        UPDATE
            lgs_waybill
        SET
            carrier_id = #{carrierId},
            version = version + 1,
            update_user = #{userId},update_time = CURRENT_TIMESTAMP(),
            status = #{nextStatus},
            type = #{nextType},
            carrier_type = #{carrierType},
            actual_quantity = 0.00
        WHERE
            waybill_id = #{waybillId} AND
            version = #{currentVersion} AND
            status = #{currentStatus}
    </update>
    <update id="updateArriveWarehouseTimeById">
        UPDATE lgs_waybill_info
        SET arrive_warehouse_time = #{arriveWarehouseTime}
        WHERE waybill_id = #{waybillId}
    </update>
    <update id="updateEnterFactoryTimeById">
        UPDATE lgs_waybill
        SET enter_factory_time = #{enterFactoryTime}
        WHERE waybill_id = #{waybillId}
    </update>
    <update id="updateActualQuantity">
        UPDATE lgs_waybill
        SET actual_quantity = #{actualQuantity},
        status = #{status}
        WHERE waybill_id = #{waybillId}
    </update>
    <update id="updateStatusToCanceledById">
        UPDATE lgs_waybill
        SET
        status = 8,version = 0
        WHERE waybill_id = #{waybillId}
    </update>
    <update id="updateWaybillCancelReason" parameterType="com.ecommerce.logistics.api.dto.waybill.CancelReasonDTO">
        UPDATE lgs_waybill_info
        SET cancel_reason = #{cancelReson}
        WHERE waybill_id = #{waybillId}
    </update>
    <select id="selectDeliveryTime">
        SELECT 	delivery_time
        FROM lgs_waybill
        WHERE waybill_id = #{waybillId}
    </select>
    <select id="selectProductQuantity">
        SELECT 	quantity,product_info_id
        FROM lgs_product_quantity_map
        WHERE map_id = #{waybill_id} AND map_type = 3
    </select>
    <select id="selectProductCategory">
        SELECT 	transport_category_id
        FROM lgs_product_info
        WHERE 	product_info_id = #{productInfoId}
    </select>

    <update id="updateArriveDestinationTime">
        UPDATE lgs_waybill
        SET arrive_destination_time = #{date}
        WHERE waybill_id = #{waybillId}
    </update>
    <update id="updateMonitorArriveTime">
        UPDATE lgs_waybill
        SET monitor_arrive_time = #{date}
        WHERE waybill_id = #{waybillId}
    </update>
    <update id="updateBeginCarryTime">
        UPDATE lgs_waybill
        SET begin_carry_time = NOW()
        WHERE waybill_id = #{waybillId}
    </update>
    <update id="updateCompleteTimeAndStatus">
        UPDATE lgs_waybill
        SET complete_time = #{completeTime}, status = #{status}
        WHERE waybill_id = #{waybillId}
    </update>
    <update id="updateMonitorCompleteTime" parameterType="com.ecommerce.logistics.api.dto.waybill.SubWaybillMonitorStatusNotifyDTO">
        UPDATE
            lgs_waybill
        SET
            update_time = current_timestamp
            <if test="monitorArriveTime != null">
              , monitor_arrive_time = #{monitorArriveTime}
            </if>
            <if test="monitorCompleteTime != null">
              , monitor_complete_time = #{monitorCompleteTime}
            </if>
        WHERE
            waybill_id = #{subWaybillId}
    </update>

  <update id="updateMonitorCompleteTimeForCurrent">
    UPDATE
        lgs_waybill
    SET
        update_time = current_timestamp
      , monitor_complete_time = current_timestamp
    WHERE
        waybill_id in
        <foreach collection="waybillIdList" index="wid" item="wit" open="(" separator="," close=")">
          #{wit}
        </foreach>
  </update>

    <update id="snatchWaybill" parameterType="com.ecommerce.logistics.dao.dto.waybill.SnatchWaybillDO">
        UPDATE
          lgs_waybill_info a
        INNER JOIN
          lgs_waybill b ON a.waybill_id = b.waybill_id
        SET
          a.driver_id = #{driverId},
          a.driver_name = #{driverName},
          a.driver_phone= #{driverPhone},
          a.vehicle_num = #{vehicleNum},
          a.receive_time = CURRENT_TIMESTAMP(),
          b.carrier_id = #{driverId},
          b.status = #{nextStatus},
          b.version = b.version + 1
        WHERE
          a.waybill_id = #{waybillId} AND
          b.version = #{currentVersion} AND
          b.status = #{currentStatus}
    </update>
    <!-- 当订单取消时数据库需要完成以下一系列操作
        1.通过运单Id获取父运单Id，调度单Id，提货单Id
        2.通过运单Id查询出产品的数量
        3.通过运单Id将该运单删除
        4.若是该运单的父运单Id不为空，则将该运单相关联的运单状态修改为待整合(wait merger)
        5.更新产品映射表里的商品数量
     -->
    <select id="selectCancelObj" resultType="com.ecommerce.logistics.api.dto.waybill.WaybillRemoveDTO">
        SELECT parent_id,dispatch_bill_id,picking_bill_id
        FROM lgs_waybill
        WHERE waybill_id = #{waybillId}
    </select>
    <select id="selectProductDeliveryQuantity" resultType="java.math.BigDecimal">
        SELECT delivery_quantity
        FROM lgs_product_quantity_map
        WHERE map_id = #{waybillId}
    </select>
    <update id="driverCancelWaybill">
        UPDATE lgs_waybill
        SET status = #{status}
        WHERE waybill_id = #{waybillId}
    </update>
    <update id="updateWaybillStatusToWaitMerger">
        UPDATE lgs_waybill
        SET status = #{status}
        WHERE waybill_id = #{parentId}
    </update>
    <update id="updateProductQuantityMap">
        UPDATE lgs_product_quantity_map
        SET delivery_quantity = delivery_quantity - #{quantity}
        WHERE map_id = #{mapId} AND map_type = #{mapType}
    </update>
    	
    <update id="updateStatusToWaitDelivery" parameterType="com.ecommerce.logistics.api.dto.waybill.PassCheckDTO">
    	UPDATE
    		lgs_waybill
    	SET
    		status = #{status},
    		update_user = #{userId},
    		update_time = CURRENT_TIMESTAMP()
    	WHERE
    		parent_id = #{waybillId}
    </update>
    
    <update id="updateStatusByWaybillId" parameterType="java.util.Map">
    	UPDATE
    		lgs_waybill
    	SET
    		status = #{status},
    		update_user = #{operatorId},
    		update_time = CURRENT_TIMESTAMP()
    	WHERE
    		waybill_id = #{waybillId}
    </update>
    
    <update id="updateWaybillByPassCheckDTO" parameterType="com.ecommerce.logistics.api.dto.waybill.PassCheckDTO">
    	UPDATE
          lgs_waybill_info a
        INNER JOIN
          lgs_waybill b ON a.waybill_id = b.waybill_id
        SET
          a.update_user = #{userId},
          a.update_time = CURRENT_TIMESTAMP(),
          a.published_carriage = #{publishedCarriage},
          a.approval_time = CURRENT_TIMESTAMP(),
          b.status = #{nextStatus},
          b.update_user = #{userId},
          b.update_time = CURRENT_TIMESTAMP(),
          b.version = b.version + 1
        WHERE
          b.waybill_id = #{waybillId} AND
          b.version = #{currentVersion} AND
          b.status = #{currentStatus}
    </update>
    
    <update id="updateWaybillStatusToCancel" parameterType="com.ecommerce.logistics.api.dto.waybill.CancelWaybillDTO">
    	UPDATE
    	 	lgs_waybill
    	SET
    		version = version + 1,
        	update_user = #{userId},
        	update_time = CURRENT_TIMESTAMP(),
        	cancel_time = NOW(),
        	status = #{nextStatus}
        WHERE 
        	waybill_id = #{waybillId} 
        	AND version = #{currentVersion} 
        	AND status = #{currentStatus}
    </update>
    <update id="confirmWaybillByDriver" parameterType="com.ecommerce.logistics.dao.dto.waybill.ConfirmWaybillDO">
        UPDATE
            lgs_waybill a
        INNER JOIN
            lgs_waybill_info b ON a.waybill_id = b.waybill_id
        SET
            a.status = #{status},
            a.update_user = #{userId},
            a.version = a.version + 1,
            b.estimate_km= #{estimateKm},
            b.estimate_duration = #{estimateDuration}
        WHERE
            a.waybill_id = #{waybillId} AND a.status = #{currentStatus} AND a.version = #{currentVersion}
    </update>
    <update id="updateMainWaybillMonitorStatus" parameterType="com.ecommerce.logistics.api.dto.waybill.WaybillMonitorStatusNotifyDTO">
        UPDATE
          lgs_waybill as t1
        INNER JOIN
          lgs_waybill_info as t2 on t1.waybill_id = t2.waybill_id
        SET
          t1.monitor_arrive_time = #{monitorArriveTime},
          t1.monitor_complete_time = #{monitorCompleteTime},
          t2.monitor_outer_time = #{monitorLeaveTime}
        WHERE
          t1.waybill_id = #{waybillId}
    </update>
    <update id="updateSubWaybillSequence">
        UPDATE
            lgs_waybill
        SET
            waybill_sequence = #{sequence}
        WHERE
            waybill_id = #{waybillId}
    </update>
    <update id="updateMainWaybillStatusByCon" parameterType="com.ecommerce.logistics.dao.dto.waybill.UpdateStatusDTO">
    	UPDATE
    		lgs_waybill
    	SET
    		status = #{nextStatus},
    		version = version+1,
    		actual_quantity = #{actualQuantity},
    		update_user = #{updateUser},
    		update_time = CURRENT_TIMESTAMP()
    	WHERE
    		waybill_id in 
    		<foreach collection="waybillIds" close=")" index="index" item="waybillId" open="(" separator=",">
    			#{waybillId}
    		</foreach>
    		AND status = #{currentStatus} AND version = #{currentVersion}
    </update>
    <update id="updateSubWaybillStatusByCon" parameterType="com.ecommerce.logistics.dao.dto.waybill.UpdateStatusDTO">
    	UPDATE
    		lgs_waybill
    	SET
    		status = #{nextStatus},
    		actual_quantity = #{actualQuantity},
    		update_user = #{updateUser},
    		update_time = CURRENT_TIMESTAMP()
    	WHERE
    		waybill_id in 
    		<foreach collection="waybillIds" close=")" index="index" item="waybillId" open="(" separator=",">
    			#{waybillId}
    		</foreach>
    </update>
    <update id="updateSubWaybillStatusByRepublishCon" parameterType="com.ecommerce.logistics.dao.dto.waybill.UpdateStatusDTO">
    	UPDATE
    		lgs_waybill
    	SET
    		status = #{nextStatus},
  			arrive_destination_time = #{arriveDestinationTime},
  			begin_carry_time = #{beginCarryTime},
  			complete_time = #{completeTime},
  			monitor_complete_time = #{monitorCompleteTime},
    		update_user = #{updateUser},
    		update_time = CURRENT_TIMESTAMP(),
            actual_quantity = 0.00
    	WHERE
    		waybill_id in 
    		<foreach collection="waybillIds" close=")" index="index" item="waybillId" open="(" separator=",">
    			#{waybillId}
    		</foreach>
    </update>

  <update id="batchPassCheckMainWaybill">
    UPDATE lgs_waybill waybill
    left join lgs_waybill_info info ON info.waybill_id = waybill.waybill_id
    SET
      waybill.status = '030070400',
      waybill.update_user = #{operatorId},
      waybill.update_time = current_timestamp,
      waybill.version = waybill.version + 1,
      info.published_carriage = info.estimate_carriage
    WHERE
      waybill.waybill_id in
      <foreach collection="waybillIdList" item="wit" index="win" open="(" separator="," close=")">
        #{wit}
      </foreach>
      AND waybill.status = '030070200'
      and waybill.del_flg = 0
      and info.del_flg = 0
  </update>

  <update id="batchPassCheckSubWaybill">
    UPDATE lgs_waybill
    SET
    status = '030070400',
    update_user = #{operatorId},
    update_time = current_timestamp
    WHERE
    parent_id in
    <foreach collection="parentIdList" item="pit" index="pin" open="(" separator="," close=")">
      #{pit}
    </foreach>
    AND status = '030070200'
    and (is_main_waybill = 0 or is_main_waybill is null)
    and del_flg = 0
  </update>

  <update id="markSecondaryWaybillReady">
    update lgs_waybill
    set ready_flag = 1,
        update_time = current_timestamp
    where waybill_id = #{secondaryWaybillId} and bill_proxy_type = '030400300'
  </update>

</mapper>