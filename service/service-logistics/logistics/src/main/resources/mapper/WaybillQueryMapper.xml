<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.WaybillQueryMapper">
  <select id="selectPublishWaybillList" parameterType="com.ecommerce.logistics.api.dto.waybill.PublishWaybillListQueryDTO" resultType="com.ecommerce.logistics.api.dto.waybill.PublishWaybillListDTO">

  </select>
  
  <select id="selectBillAssignByDispatchBillId" parameterType="java.lang.String" resultType="com.ecommerce.logistics.api.dto.dispatchbill.DispatchBillAssignDTO">
  	SELECT 
  		w.waybill_id as waybillId,
  		w.waybill_num as waybillNum,
  		w.status as waybillStatus,
  		w.type as waybillType,
  		i.driver_name as driverName,
  		i.driver_phone as driverPhone,
  		i.vehicle_num as vehicleNum,
  		q.quantity as productQuantity
  	FROM 
  		lgs_waybill_info i 
  	RIGHT JOIN
  		lgs_waybill w on w.waybill_id = i.waybill_id
  	RIGHT JOIN
  		lgs_product_quantity_map q on q.map_id = w.waybill_id
  	WHERE
  		w.dispatch_bill_id = #{dispatchBillId}
  		
  </select>
  
  <select id="selectWaybillIdsByDispatchBillId" parameterType="java.lang.String" resultType="java.lang.String">
  	SELECT
  		waybill_id
  	FROM 
  		lgs_waybill
  	WHERE
  		dispatch_bill_id = #{dispatchBillId}
  </select>
  
  <select id="selectMergeWayBillByCondition" parameterType="com.ecommerce.logistics.api.dto.waybill.MergeWaybillListQueryDTO"
  	resultType="com.ecommerce.logistics.dao.dto.pickingbill.WaybillDO">
  		SELECT
  			w.waybill_id as waybillId,
  			w.waybill_num as waybillNum,
  			p.picking_bill_id as pickingBillId,
  			p.picking_bill_num as pickingBillNum,
  			p.province as province,
	  		p.province_code as provinceCode,
  			p.city as city,
	  		p.city_code as cityCode,
  			p.district as district,
	  		p.district_code as districtCode,
  			p.street as street,
  			p.address as address,
  			p.warehouse_id as warehouseId,
  			w.delivery_time as deliveryTime,
  			w.delivery_time_range as deliveryTimeRange,
  			m.quantity as quantity,
  			info.note as note,
  			info.unit as productUnit
  		FROM
  			lgs_picking_bill p
  			<choose>
  				<when test="pickingJoinStatus == 1">
  					LEFT JOIN
  						lgs_waybill w on w.picking_bill_id = p.picking_bill_id
  				</when>
  				<when test="pickingJoinStatus == 2">
  					RIGHT JOIN
  						lgs_waybill w on w.picking_bill_id = p.picking_bill_id
  				</when>
  				<otherwise>
  					JOIN
  						lgs_waybill w on w.picking_bill_id = p.picking_bill_id
  				</otherwise>
  			</choose>
			JOIN
				lgs_product_quantity_map m on m.map_id = w.waybill_id
			LEFT JOIN
				lgs_product_info info ON info.product_info_id = m.product_info_id
	  		<if test="regionCodeList != null and regionCodeList.size() > 0">
		  	INNER JOIN
		  		ba_region_all as auth on p.district_code = auth.region_son_code
	  		</if>
  		<where>
  			w.status = #{status} AND w.del_flg = 0 AND m.map_type = #{productType}
			<if test="regionCodeList != null and regionCodeList.size() > 0">
				AND auth.region_code IN
				<foreach close=")" collection="regionCodeList" index="index" item="item" open="(" separator=",">
					#{item}
				</foreach>
			</if>
			<if test="warehouseId != null and warehouseId != ''">
  				AND p.warehouse_id = #{warehouseId}
  			</if>
  			<if test="provinceCode != null and provinceCode!= '' ">
  				AND p.province_code = #{provinceCode}
  			</if>
  			<if test="cityCode != null and cityCode != '' ">
  				AND p.city_code = #{cityCode}
  			</if>
  			<if test="districtCode != null and districtCode!= '' ">
  				AND p.district_code = #{districtCode}
  			</if>
  			<if test="beginReceiveTime != null and beginReceiveTime != ''">
		        <![CDATA[AND w.delivery_time >= #{beginReceiveTime}]]>
		     </if>
		     <if test="endReceiveTime != null and endReceiveTime != ''">
		        <![CDATA[AND w.delivery_time <= #{endReceiveTime}]]>
		     </if>
		     <if test="pickingBillNum != null and pickingBillNum != '' ">
		     	AND p.picking_bill_num = #{pickingBillNum}
		     </if>
		     <if test="waybillNum != null and waybillNum != '' ">
		     	AND w.waybill_num = #{waybillNum}
		     </if>
		     <if test="minQuantity != null and minQuantity != '' ">
		     	<![CDATA[AND m.quantity >= #{minQuantity}]]>
		     </if>
		     <if test="maxQuantity != null and maxQuantity != '' ">
		     	<![CDATA[AND m.quantity <= #{maxQuantity}]]>
		     </if>
		     <if test="productInfoId != null and productInfoId != ''">
		     	AND m.product_info_id = #{productInfoId}
		     </if>
		     <if test="goodsId != null and goodsId != ''">
					 and info.goods_id = #{goodsId}
				 </if>
  		</where>
  		ORDER BY 
  			w.create_time DESC,w.waybill_id
  	</select>
  	
  	<select id="selectCheckWayBillIdsByCondition" parameterType="com.ecommerce.logistics.api.dto.waybill.CheckWaybillListQueryDTO" resultType="java.lang.String">
  		SELECT
  			w.waybill_id
  		FROM
  			lgs_waybill w
  		<if test="pickingBillNum != null and pickingBillNum != '' ">
  			JOIN 
  				lgs_picking_bill p on p.picking_bill_id = w.picking_bill_id
  		</if>
  		
  		<trim prefix="WHERE" suffixOverrides="AND|OR">
  			w.status = #{status} AND w.del_flg = 0 AND w.is_main_waybill = 1
  			AND w.dispatch_bill_id = ''
  			 <if test="pickingBillNum != null and pickingBillNum != '' ">
		     	AND p.picking_bill_num = #{pickingBillNum}
		     </if>
		     <if test="waybillNum != null and waybillNum != '' ">
		     	AND w.waybill_num = #{waybillNum}
		     </if>
		     <if test="beginReceiveTime != null and beginReceiveTime != ''">
		        <![CDATA[AND w.delivery_time >= #{beginReceiveTime}]]>
		     </if>
		     <if test="endReceiveTime != null and endReceiveTime != ''">
		        <![CDATA[AND w.delivery_time <= #{endReceiveTime}]]>
		     </if>
  		</trim>
  		ORDER BY 
  			w.create_time DESC,w.waybill_id
  	</select>
  	
  	<select id="selectCheckWayBillByWayBillIds" parameterType="java.util.List" 
  		resultType="com.ecommerce.logistics.dao.dto.pickingbill.WaybillDO">
  		SELECT
  			w.waybill_id as waybillId,
  			w.waybill_num as waybillNum,
  			w.parent_id as parentId,
  			w.is_main_waybill as isMainWaybill,
  			p.picking_bill_num as pickingBillNum,
  			m.product_info_id as productInfoId,
  			p.warehouse_id as warehouseId,
  			p.province as province,
  			p.city as city,
  			p.district as district,
  			p.street as street,
  			p.address as address,
  			p.delivery_time as deliveryTime,
  			p.delivery_time_range as deliveryTimeRange,
  			m.quantity as quantity,
  			(SELECT estimate_carriage FROM lgs_waybill_info WHERE waybill_id = w.waybill_id) as estimateCarriage
  		FROM
  			lgs_picking_bill p
  		RIGHT JOIN 
  			lgs_waybill w on w.picking_bill_id = p.picking_bill_id
  		LEFT JOIN
  			lgs_product_quantity_map m on m.map_id = w.waybill_id
  		WHERE w.parent_id in
  		<foreach collection="list" close=")" index="index" item="item" open="(" separator=",">
  			#{item}
  		</foreach>
  		ORDER BY w.parent_id,w.is_main_waybill DESC
  	</select>
  	
  	<select id="selectBuyerSellerWaybillByCondition" parameterType="com.ecommerce.logistics.api.dto.waybill.PublishWaybillListQueryDTO"
  		resultType="com.ecommerce.logistics.dao.dto.pickingbill.WaybillDO">
			SELECT
			w.waybill_id as waybillId,
			w.waybill_num as waybillNum,
			w.type as type,
			w.status as status,
			w.create_time as createTime,
			w.actual_quantity as actualQuantity,
			w.sync_flag as syncFlag,
			w.external_waybill_status as externalWaybillStatus,
			w.external_waybill_num as externalWaybillNum,
			p.delivery_time as deliveryTime,
			p.delivery_time_range as deliveryTimeRange,
			p.buyer_name as buyerName,
			p.seller_name as sellerName,
			concat(p.province, p.city, p.district, p.street, p.address) as receiveAddress,
			p.picking_bill_num as pickingBillNum,
			p.picking_bill_id as pickingBillId,
			p.type as pickingBillType,
			p.delivery_sheet_num as deliverySheetNum,
			p.warehouse_id as warehouseId,
			p.receiver as receiver,
			p.receiver_phone as receiverPhone,
			i.driver_name as driver,
			i.driver_phone as driverPhone,
			i.vehicle_num as vehicleNum,
			i.published_carriage as publishedCarriage,
			w.sign_quantity as signQuantity,
			w.sign_remark as signRemark,
			w.empty_load_flag as emptyLoadFlag,
			w.empty_load_charge as emptyLoadCharge,
			w.qr_code as qrCode,
			w.need_monitor as needMonitor,
			w.can_operate as canOperate,
			w.bill_proxy_type as billProxyType,
		  	i.leave_warehouse_time as leaveWarehouseTime,
		  	w.project_name as projectName
  		FROM
  			lgs_waybill_info i
  		RIGHT JOIN
  			lgs_waybill w ON w.parent_id = i.waybill_id
		JOIN
			lgs_picking_bill p on p.picking_bill_id = w.picking_bill_id
		<if test="regionCodeList != null and regionCodeList.size() > 0">
		INNER JOIN
			ba_region_all as auth on p.district_code = auth.region_son_code
		</if>
  		<where>
  			w.del_flg = 0 AND p.del_flg = 0
  			<if test="sellerId != null and sellerId != ''">
					AND ( p.seller_id = #{sellerId} )
			</if>
  			<if test="buyerId != null and buyerId != ''">
					and p.buyer_id = #{buyerId}
				</if>
  			AND w.status in 
  			<foreach collection="statusList" close=")" index="index" item="status" open="(" separator=",">
  				#{status}
  			</foreach>
			<if test="regionCodeList != null and regionCodeList.size() > 0">
				AND auth.region_code IN
				<foreach close=")" collection="regionCodeList" index="index" item="item" open="(" separator=",">
					#{item}
				</foreach>
			</if>
			<if test="type != null and type != '' ">
  				AND w.type = #{type}
  			</if>
  			<if test="pickingBillNum != null and pickingBillNum != '' ">
		     	AND p.picking_bill_num = #{pickingBillNum}
			</if>
			<if test="waybillNum != null and waybillNum != '' ">
		     	AND w.waybill_num = #{waybillNum}
			</if>
			<if test="beginDeliveryTime != null and beginDeliveryTime != ''">
		        <![CDATA[AND w.delivery_time >= #{beginDeliveryTime}]]>
			</if>
			<if test="endDeliveryTime != null and endDeliveryTime != ''">
		        <![CDATA[AND w.delivery_time <= concat(#{endDeliveryTime}, ' 23:59:59')]]>
			</if>
				<if test="beginLeaveWarehouseTime != null and beginLeaveWarehouseTime != ''">
					<![CDATA[AND i.leave_warehouse_time >= #{beginLeaveWarehouseTime}]]>
				</if>
				<if test="endLeaveWarehouseTime != null and endLeaveWarehouseTime != ''">
					<![CDATA[AND i.leave_warehouse_time <= concat(#{endLeaveWarehouseTime}, ' 23:59:59')]]>
				</if>
			<if test="warehouseId != null and warehouseId != ''">
  				AND p.warehouse_id = #{warehouseId}
			</if>
			<if test="deliverySheetNum != null and deliverySheetNum != '' ">
				AND p.delivery_sheet_num = #{deliverySheetNum}
			</if>
			<if test="contractId != null and contractId != '' ">
				AND w.deals_id = #{contractId}
			</if>
			<if test="contractSequence != null and contractSequence != '' ">
				AND w.deals_name = #{contractSequence}
			</if>
			<if test="vehicleNum != null and vehicleNum != ''">
				and i.vehicle_num LIKE CONCAT('%', #{vehicleNum}, '%')
			</if>
		  <if test="mdmCodeList != null and mdmCodeList.size() > 0">
				and (p.mdm_code = '' or p.mdm_code in
				<foreach collection="mdmCodeList" item="mit" index="min" open="(" separator="," close=")">
					#{mit}
				</foreach>
				)
			</if>
  		</where>
  		ORDER BY 
  			w.create_time DESC,w.waybill_id
  	</select>

	<select id="selectAppWaybillByCondition" parameterType="com.ecommerce.logistics.api.dto.waybill.AppWaybillListQueryDTO"
			resultType="com.ecommerce.logistics.api.dto.waybill.PublishWaybillListDTO">
		SELECT
		w.waybill_id as waybillId,
		w.waybill_num as waybillNum,
		w.type as type,
		w.status as status,
		w.qr_code as qrCode,
		DATE_FORMAT(w.create_time, '%Y-%m-%d') as createTime,
		info.note as productDesc,
		w.actual_quantity as actualQuantity,
		q.quantity as totalQuantity,
		p.warehouse_id as warehouseId,
		i.vehicle_num as vehicleNum,
		p.buyer_name as buyerName,
		info.unit as productUnit,
		w.can_operate as canOperate,
		p.delivery_time as deliveryTime,
		p.delivery_time_range as deliveryTimeRange,
		w.bill_proxy_type as billProxyType,
		date_format(i.leave_warehouse_time, '%Y-%m-%d %H:%i:%s')
		FROM
		lgs_waybill_info i
		RIGHT JOIN
		lgs_waybill w ON w.parent_id = i.waybill_id
		JOIN
		lgs_picking_bill p on p.picking_bill_id = w.picking_bill_id
		LEFT JOIN
		lgs_product_quantity_map q ON q.map_id = w.waybill_id
		LEFT JOIN
		lgs_product_info info ON info.product_info_id = q.product_info_id
		<if test="regionCodeList != null and regionCodeList.size() > 0">
			INNER JOIN
			ba_region_all as auth on p.district_code = auth.region_son_code
		</if>
		<where>
			w.del_flg = 0
			AND p.del_flg = 0
			AND q.del_flg = 0
			AND info.del_flg = 0
			AND w.status in
			<foreach collection="statusList" close=")" index="index" item="status" open="(" separator=",">
				#{status}
			</foreach>
			<if test="regionCodeList != null and regionCodeList.size() > 0">
				AND auth.region_code IN
				<foreach close=")" collection="regionCodeList" index="index" item="item" open="(" separator=",">
					#{item}
				</foreach>
			</if>
			<if test="type != null and type != '' ">
				AND w.type = #{type}
			</if>
			<if test="beginCreateTime != null and beginCreateTime != ''">
				<![CDATA[AND w.create_time >= CONCAT(#{beginCreateTime},' 00:00:00')]]>
			</if>
			<if test="endCreateTime != null and endCreateTime != ''">
				<![CDATA[AND w.create_time <= CONCAT(#{endCreateTime},' 23:59:59')]]>
			</if>
			<if test="beginLeaveWarehouseTime != null and beginLeaveWarehouseTime != ''">
				<![CDATA[AND i.leave_warehouse_time >= #{beginLeaveWarehouseTime}]]>
			</if>
			<if test="endLeaveWarehouseTime != null and endLeaveWarehouseTime != ''">
				<![CDATA[AND i.leave_warehouse_time <= concat(#{endLeaveWarehouseTime}, ' 23:59:59')]]>
			</if>
			<if test="warehouseId != null and warehouseId != ''">
				AND p.warehouse_id = #{warehouseId}
			</if>
			<if test="transportCategoryId != null and transportCategoryId != ''">
				and info.transport_category_id = #{transportCategoryId}
			</if>
			<if test="note != null and note != ''">
				and info.note = #{note}
			</if>
			<if test="buyerId != null and buyerId != ''">
				and p.buyer_id = #{buyerId}
			</if>
			<if test="sellerId != null and sellerId != ''">
				and ( p.seller_id = #{sellerId} )
			</if>
			<if test="mdmCode != null and mdmCode != ''">
				and p.mdm_code = #{mdmCode}
			</if>
			<if test="mdmCodeList != null and mdmCodeList.size() > 0">
				and (p.mdm_code = '' or p.mdm_code in
				<foreach collection="mdmCodeList" item="mit" index="min" open="(" separator="," close=")">
					#{mit}
				</foreach>
				)
			</if>
		</where>
		ORDER BY
		w.create_time DESC,w.waybill_id
	</select>

	<select id="statisticsAppWaybill" parameterType="com.ecommerce.logistics.api.dto.waybill.AppWaybillListQueryDTO"
			resultType="com.ecommerce.logistics.api.dto.waybill.AppWaybillStatisticsDTO">
		SELECT
		sum(w.actual_quantity) as totalActualQuantity,
		sum(q.quantity) as totalPlanQuantity
		FROM
		lgs_waybill_info i
		RIGHT JOIN
		lgs_waybill w ON w.parent_id = i.waybill_id
		JOIN
		lgs_picking_bill p on p.picking_bill_id = w.picking_bill_id
		LEFT JOIN
		lgs_product_quantity_map q ON q.map_id = i.waybill_id
		LEFT JOIN
		lgs_product_info info ON info.product_info_id = q.product_info_id
		<if test="regionCodeList != null and regionCodeList.size() > 0">
			INNER JOIN
			ba_region_all as auth on p.district_code = auth.region_son_code
		</if>
		<where>
			w.del_flg = 0
			AND p.del_flg = 0
			AND q.del_flg = 0
			AND info.del_flg = 0
			AND info.unit = '吨'
			AND w.status in
			<foreach collection="statusList" close=")" index="index" item="status" open="(" separator=",">
				#{status}
			</foreach>
			<if test="regionCodeList != null and regionCodeList.size() > 0">
				AND auth.region_code IN
				<foreach close=")" collection="regionCodeList" index="index" item="item" open="(" separator=",">
					#{item}
				</foreach>
			</if>
			<if test="type != null and type != '' ">
				AND w.type = #{type}
			</if>
			<if test="beginCreateTime != null and beginCreateTime != ''">
				<![CDATA[AND w.create_time >= CONCAT(#{beginCreateTime},' 00:00:00')]]>
			</if>
			<if test="endCreateTime != null and endCreateTime != ''">
				<![CDATA[AND w.create_time <= CONCAT(#{endCreateTime},' 23:59:59')]]>
			</if>
			<if test="warehouseId != null and warehouseId != ''">
				AND p.warehouse_id = #{warehouseId}
			</if>
			<if test="transportCategoryId != null and transportCategoryId != ''">
				and info.transport_category_id = #{transportCategoryId}
			</if>
			<if test="note != null and note != ''">
				and info.note = #{note}
			</if>
			<if test="buyerId != null and buyerId != ''">
				and p.buyer_id = #{buyerId}
			</if>
			<if test="sellerId != null and sellerId != ''">
				and ( p.seller_id = #{sellerId} or ( p.buyer_id = #{sellerId} and w.bill_proxy_type = '030400200') )
			</if>
			<if test="beginLeaveWarehouseTime != null and beginLeaveWarehouseTime != ''">
				<![CDATA[AND i.leave_warehouse_time >= #{beginLeaveWarehouseTime}]]>
			</if>
			<if test="endLeaveWarehouseTime != null and endLeaveWarehouseTime != ''">
				<![CDATA[AND i.leave_warehouse_time <= concat(#{endLeaveWarehouseTime}, ' 23:59:59')]]>
			</if>
			<if test="mdmCode != null and mdmCode != ''">
				and p.mdm_code = #{mdmCode}
			</if>
			<if test="mdmCodeList != null and mdmCodeList.size() > 0">
				and (p.mdm_code = '' or p.mdm_code in
				<foreach collection="mdmCodeList" item="mit" index="min" open="(" separator="," close=")">
				  #{mit}
				</foreach>
				)
			</if>
		</where>
		ORDER BY
		w.create_time DESC,w.waybill_id
	</select>

	<select id="selectAppWaybillQueryConditions" parameterType="com.ecommerce.logistics.api.dto.waybill.AppWaybillListQueryDTO"
			resultType="com.ecommerce.logistics.api.dto.waybill.AppWaybillQueryConditionsDTO">
		SELECT
		p.buyer_id as buyerId,
		p.buyer_name as buyerName,
		p.seller_id as sellerId,
		p.seller_name as sellerName,
		info.note as note,
		p.warehouse_id as warehouseId
		FROM
		lgs_waybill_info i
		RIGHT JOIN
		lgs_waybill w ON w.parent_id = i.waybill_id
		JOIN
		lgs_picking_bill p on p.picking_bill_id = w.picking_bill_id
		LEFT JOIN
		lgs_product_quantity_map q ON q.map_id = i.waybill_id
		LEFT JOIN
		lgs_product_info info ON info.product_info_id = q.product_info_id
		<where>
			w.del_flg = 0
			AND p.del_flg = 0
			AND q.del_flg = 0
			AND info.del_flg = 0
			<if test="buyerId != null and buyerId != ''">
				and p.buyer_id = #{buyerId}
			</if>
			<if test="sellerId != null and sellerId != ''">
				and ( p.seller_id = #{sellerId} or ( p.buyer_id = #{sellerId} and w.bill_proxy_type = '030400200') )
			</if>
		</where>
		ORDER BY
		w.create_time DESC,w.waybill_num
	</select>

  	<select id="selectMainCarrierWaybillByMainWaybillIds" parameterType="java.lang.String" 
  	resultType="com.ecommerce.logistics.api.dto.waybill.CarrierWaybillListDTO">
  		SELECT
			w.waybill_id as waybillId,
			w.waybill_num as waybillNum,
			w.type as type,
			d.dispatch_bill_num as dispatchBillNum,
			w.status as status,
			w.delivery_time as deliveryTime,
			w.delivery_time_range as deliveryTimeRange,
			w.create_time as createTimeDate,
			w.sync_flag as syncFlag,
			w.external_waybill_status as externalWaybillStatus,
			w.delivery_certificate_flag as deliveryCertificateFlag,
			w.objection_flag as objectionFlag,
			i.driver_name as driverName,
			i.driver_phone as driverPhone,
			i.vehicle_num as vehicleNum
		FROM
			lgs_waybill w
		LEFT JOIN
			lgs_dispatch_bill d ON d.dispatch_bill_id = w.dispatch_bill_id
		LEFT JOIN
			lgs_waybill_info i ON i.waybill_id = w.waybill_id
  		WHERE
  			w.waybill_id in 
  			<foreach collection="waybillIds" close=")" index="index" item="id" open="(" separator=",">
  				#{id}
  			</foreach>
	   ORDER BY 
 			w.create_time DESC,w.waybill_id
  	</select>
  	
  	<select id="selectMainCarrierWaybillByCondition" parameterType="com.ecommerce.logistics.api.dto.waybill.CarrierWaybillListQueryDTO"
  	resultType="com.ecommerce.logistics.api.dto.waybill.CarrierWaybillListDTO">
  		SELECT
  			w.waybill_id as waybillId,
  			w.waybill_num as waybillNum,
  			d.dispatch_bill_num as dispatchBillNum,
  			w.status as status,
			w.type as type,
  			w.delivery_time as deliveryTime,
  			w.delivery_time_range as deliveryTimeRange,
			w.create_time as createTimeDate,
  			i.driver_name as driverName,
  			i.driver_phone as driverPhone,
  			i.vehicle_num as vehicleNum	
		FROM
			lgs_waybill w
			<choose>
				<when test="dispatchBillNum != null and dispatchBillNum != '' ">
					JOIN
						lgs_dispatch_bill d ON d.dispatch_bill_id = w.dispatch_bill_id
				</when>
				<otherwise>
					LEFT JOIN
						lgs_dispatch_bill d ON d.dispatch_bill_id = w.dispatch_bill_id
				</otherwise>
			</choose>
			<choose>
				<when test="vehicleNum != null and driverId != null ">
					JOIN
						lgs_waybill_info i ON i.waybill_id = w.waybill_id
				</when>
				<otherwise>
					LEFT JOIN
						lgs_waybill_info i ON i.waybill_id = w.waybill_id
				</otherwise>
			</choose>
  		WHERE
  			w.del_flg = 0 AND w.carrier_id = #{carrierId} AND w.is_main_waybill = 1
  			AND w.status in 
  			<foreach collection="statuss" close=")" index="index" item="status" open="(" separator=",">
  				#{status}
  			</foreach>
  			<if test="dispatchBillNum != null and dispatchBillNum != '' ">
  				AND d.dispatch_bill_num = #{dispatchBillNum}
  			</if>
  			<if test="waybillNum != null and waybillNum != '' ">
  				AND w.waybill_num = #{waybillNum}
  			</if>
  			<if test="vehicleNum != null and vehicleNum != '' ">
  				AND i.vehicle_num LIKE CONCAT('%', #{vehicleNum}, '%')
  			</if>
  			<if test="driverId != null and driverId != '' ">
  				AND i.driver_id = #{driverId}
  			</if>
  			<if test="beginDeliveryTime != null and beginDeliveryTime != ''">
		        <![CDATA[AND w.delivery_time >= #{beginDeliveryTime}]]>
		     </if>
		     <if test="endDeliveryTime != null and endDeliveryTime != ''">
		        <![CDATA[AND w.delivery_time <= #{endDeliveryTime}]]>
		     </if>
		   ORDER BY
  			w.create_time DESC
  	</select>
  	
  	<select id="selectMainWaybillIdByCarrierCon" parameterType="com.ecommerce.logistics.api.dto.waybill.CarrierWaybillListQueryDTO"
  	resultType="java.lang.String">
  		SELECT
  			w.parent_id
  			FROM 
  				lgs_waybill w
			JOIN
				lgs_picking_bill p ON p.picking_bill_id = w.picking_bill_id
			<if test="dispatchBillNum != null and dispatchBillNum != '' ">
				JOIN 
					lgs_dispatch_bill d ON d.dispatch_bill_id = w.dispatch_bill_id
			</if>
			<if test="vehicleNum != null or driverId != null">
				JOIN 
					lgs_waybill_info i ON i.waybill_id = w.waybill_id
			</if>
			<if test="regionCodeList != null and regionCodeList.size() > 0">
			INNER JOIN
				ba_region_all as auth on p.district_code = auth.region_son_code
			</if>
  		WHERE
  			w.del_flg = 0 AND w.carrier_id = #{carrierId}
  			AND w.status in 
  			<foreach collection="statuss" close=")" index="index" item="status" open="(" separator=",">
  				#{status}
  			</foreach>
			<if test="regionCodeList != null and regionCodeList.size() > 0">
				AND auth.region_code IN
				<foreach close=")" collection="regionCodeList" index="index" item="item" open="(" separator=",">
					#{item}
				</foreach>
			</if>
  			<if test="warehouseId != null and warehouseId != '' ">
  				AND p.warehouse_id = #{warehouseId}
  			</if>
  			<if test="dispatchBillNum != null and dispatchBillNum != '' ">
  				AND d.dispatch_bill_num = #{dispatchBillNum}
  			</if>
  			<if test="waybillNum != null and waybillNum != '' ">
  				AND w.waybill_num = #{waybillNum}
  			</if>
  			<if test="vehicleNum != null and vehicleNum != '' ">
  				AND i.vehicle_num LIKE CONCAT('%', #{vehicleNum}, '%')
  			</if>
  			<if test="driverId != null and driverId != '' ">
  				AND i.driver_id = #{driverId}
  			</if>
  			<if test="beginDeliveryTime != null and beginDeliveryTime != ''">
		        <![CDATA[AND w.delivery_time >= #{beginDeliveryTime}]]>
		     </if>
		     <if test="endDeliveryTime != null and endDeliveryTime != ''">
		        <![CDATA[AND w.delivery_time <= #{endDeliveryTime}]]>
		     </if>
		   GROUP BY
		   	w.parent_id
		   ORDER BY 
  			w.create_time DESC
  	</select>
  	
  	<select id="selectVehicleInfoByCondition" parameterType="com.ecommerce.logistics.api.dto.waybill.WaybillAssignListQueryDTO"
  		resultType="com.ecommerce.logistics.dao.vo.Vehicle">
  		SELECT
  			DISTINCT v.number as number, v.driver_name as driverName, v.load_capacity as loadCapacity
  		FROM
  			lgs_waybill w 
  		JOIN
  			lgs_waybill_info i on i.waybill_id = w.waybill_id
  		JOIN
  			lgs_vehicle v ON v.number = i.vehicle_num
  		<trim prefix="WHERE" prefixOverrides="AND|OR">
  			w.del_flg = 0 AND v.user_id = #{carrierId}
  			<if test="carrierId != null and carrierId != '' ">
  				AND w.carrier_id = #{carrierId}
  			</if>
  			<if test="driverId != null and driverId != '' ">
  				AND i.driver_id = #{driverId}
  			</if>
  			<if test="beginDeliveryTime != null and beginDeliveryTime != ''">
		        <![CDATA[AND i.assign_time >= #{beginDeliveryTime}]]>
		     </if>
		     <if test="endDeliveryTime != null and endDeliveryTime != ''">
		        <![CDATA[AND i.assign_time <= #{endDeliveryTime}]]>
		     </if>
  		</trim>
  	</select>
  
  	<select id="selectWaybillAssignByCondition" parameterType="com.ecommerce.logistics.api.dto.waybill.WaybillAssignListQueryDTO"
  		resultType="com.ecommerce.logistics.dao.dto.pickingbill.WaybillDO">
  		SELECT
  			w.waybill_id as waybillId,
  			w.waybill_num as waybillNum,
  			w.delivery_time as deliveryTime,
  			w.delivery_time_range as deliveryTimeRange,
  			i.vehicle_num as vehicleNum,
  			i.driver_name as driver,
  			i.assign_time as assignTime
  		FROM
  			lgs_waybill w
  		JOIN
  			lgs_waybill_info i ON i.waybill_id = w.waybill_id
  		<trim prefix="WHERE" prefixOverrides="AND|OR">
  			w.del_flg = 0
  			<if test="carrierId != null and carrierId != '' ">
  				AND w.carrier_id = #{carrierId}
  			</if>
			AND i.vehicle_num in 
			<foreach collection="vehicleNums" close=")" index="index" item="vehicleNum" open="(" separator=",">
				#{vehicleNum}
			</foreach>
  			<if test="driverId != null and driverId != '' ">
  				AND i.driver_id = #{driverId}
  			</if>
  			<if test="beginDeliveryTime != null and beginDeliveryTime != ''">
		        <![CDATA[AND i.assign_time >= #{beginDeliveryTime}]]>
		     </if>
		     <if test="endDeliveryTime != null and endDeliveryTime != ''">
		        <![CDATA[AND i.assign_time <= #{endDeliveryTime}]]>
		     </if>
  		</trim>
  		ORDER BY
  			i.vehicle_num,w.delivery_time,w.delivery_time_range ASC
  	</select>
  	
  	<select id="selectWaybillsByDispatchIds" parameterType="java.util.List"
  		resultType="com.ecommerce.logistics.api.dto.waybill.WaybillDTO">
  		SELECT 
  			w.waybill_id as waybillId,
  			w.waybill_num as waybillNum,
  			w.picking_bill_id as pickingBillId,
  			w.dispatch_bill_id as dispatchBillId,
  			w.type as type,
  			w.status as status,
  			w.delivery_time as deliveryTime,
  			w.delivery_time_range as deliveryTimeRange,
  			m.quantity as quantity,
  			i.driver_name as driver,
  			i.driver_phone as driverPhone,
  			i.vehicle_num as vehicleNum
  		FROM
  			lgs_waybill_info i
  		RIGHT JOIN
  			lgs_waybill w on w.waybill_id = i.waybill_id
  		LEFT JOIN
  			lgs_product_quantity_map m on m.map_id = w.waybill_id
  		WHERE
  			w.del_flg = 0 AND w.dispatch_bill_id in
  			<foreach collection="list" close=")" index="index" item="dispatchBillId" open="(" separator=",">
  				#{dispatchBillId}
  			</foreach>
  		ORDER BY
  			w.create_time DESC,w.waybill_id
  	</select>

  	<select id="selectWaybillsByPickingBillIds" parameterType="java.util.List"
  		resultType="com.ecommerce.logistics.api.dto.waybill.WaybillDTO">
  		SELECT 
  			w.waybill_id as waybillId,
  			w.waybill_num as waybillNum,
			w.is_main_waybill as isMainWaybill,
			w.parent_id as parentId,
			w.picking_bill_id as pickingBillId,
  			w.dispatch_bill_id as dispatchBillId,
  			w.type as type,
  			w.status as status,
  			w.delivery_time as deliveryTime,
  			w.delivery_time_range as deliveryTimeRange,
			w.carrier_id as carrierId,
			w.complete_time as completeTime,
			w.actual_quantity as actualQuantity,
			w.sign_quantity as signQuantity,
			w.external_waybill_status as externalWaybillStatus,
			m.quantity as quantity,
			p.product_img as productImg,
			p.unit as unit,
			i.leave_warehouse_time as leaveWarehouseTime,
			i.driver_id as driverId,
  			i.driver_name as driverName,
  			i.driver_phone as driverPhone,
  			i.vehicle_num as vehicleNum
  		FROM
			lgs_waybill w
  		LEFT JOIN
			lgs_waybill_info i on w.parent_id = i.waybill_id
  		LEFT JOIN
  			lgs_product_quantity_map m on m.map_id = w.waybill_id
		LEFT JOIN
			lgs_product_info p on m.product_info_id = p.product_info_id
  		WHERE
  			w.del_flg = 0 AND w.picking_bill_id in
  			<foreach collection="list" close=")" index="index" item="pickingBillId" open="(" separator=",">
  				#{pickingBillId}
  			</foreach>
  		ORDER BY
  			w.create_time DESC,w.waybill_id
  	</select>
  	
  	<select id="selectWaybillsByWaybillIds" parameterType="java.util.List"
  		resultType="com.ecommerce.logistics.api.dto.waybill.WaybillDTO">
  		SELECT 
  			w.waybill_id as waybillId,
  			w.waybill_num as waybillNum,
  			w.picking_bill_id as pickingBillId,
  			w.dispatch_bill_id as dispatchBillId,
			w.carrier_id as carrierId,
  			w.type as type,
  			w.status as status,
  			w.delivery_time as deliveryTime,
  			w.delivery_time_range as deliveryTimeRange,
  			m.quantity as quantity,
			i.driver_id as driverId,
  			i.driver_name as driverName,
  			i.driver_phone as driverPhone,
  			i.vehicle_num as vehicleNum,
			i.estimate_km as estimateKm,
			i.estimate_duration as estimateDuration,
			i.published_carriage as publishedCarriage
		FROM
  			lgs_waybill_info i
  		RIGHT JOIN
  			lgs_waybill w on w.waybill_id = i.waybill_id
  		LEFT JOIN
  			lgs_product_quantity_map m on m.map_id = w.waybill_id
  		WHERE
  			w.del_flg = 0 AND w.waybill_id in
  			<foreach collection="list" close=")" index="index" item="waybillId" open="(" separator=",">
  				#{waybillId}
  			</foreach>
  	</select>
  	
  	<select id="selectWaybillDetailsBywaybillId" parameterType="java.lang.String"
  		resultType="com.ecommerce.logistics.dao.dto.pickingbill.WaybillDO">
			SELECT w.waybill_id                as waybillId,
						 w.waybill_num               as waybillNum,
						 w.carrier_id                as carrierId,
						 w.create_time               as createTime,
						 w.update_time               as updateTime,
						 w.type                      as type,
						 w.status                    as status,
						 w.is_main_waybill           as isMainWaybill,
						 w.parent_id                 as parentId,
						 w.picking_bill_id           as pickingBillId,
						 pb.type                     as pickingBillType,
						 w.actual_quantity           as actualQuantity,
						 w.dispatch_bill_id          as dispatchBillId,
						 w.cancel_time               as cancelTime,
						 w.complete_time             as completeTime,
						 w.picking_time              as pickingTime,
						 w.sync_flag                 as syncFlag,
						 w.external_waybill_status   as externalWaybillStatus,
						 w.external_waybill_num      as externalWaybillNum,
						 w.sync_fail_reason          as syncFailReason,
						 w.monitor_complete_time     as monitorCompleteTime,
						 w.can_monitor               as canMonitor,
						 i.driver_id                 as driverId,
						 i.driver_name               as driverName,
						 i.driver_phone              as driverPhone,
						 i.vehicle_num               as vehicleNum,
						 i.estimate_km               as estimateKm,
						 i.estimate_duration         as estimateDuration,
						 i.estimate_carriage         as estimateCarriage,
						 i.published_carriage        as publishedCarriage,
						 i.cancel_reason             as cancelReason,
						 i.receive_time              as receiveTime,
						 i.assign_time               as assignTime,
						 i.arrive_warehouse_time     as arriveWarehouseTime,
						 i.leave_warehouse_time      as leaveWarehouseTime,
						 i.actual_carriage           as actualCarriage,
						 i.actual_km                 as actualKm,
						 p.quantity                  as quantity,
						 w.sign_remark               as signRemark,
						 w.sign_quantity             as signQuantity,
						 w.empty_load_charge         as emptyLoadCharge,
						 w.empty_load_flag           as emptyLoadFlag,
						 w.qr_code                   as qrCode,
						 w.need_monitor              as needMonitor,
						 w.can_operate               as canOperate,
						 w.bill_proxy_type           as billProxyType,
						 w.ready_flag                as readyFlag,
						 w.delivery_certificate_flag as deliveryCertificateFlag,
						 w.objection_flag            as objectionFlag,
						 w.driver_evaluate_flag      as driverEvaluateFlag
			FROM lgs_waybill w
						 LEFT JOIN
					 lgs_waybill_info i on w.parent_id = i.waybill_id
						 LEFT JOIN
					 lgs_product_quantity_map p on w.waybill_id = p.map_id
						 left join lgs_picking_bill as pb on pb.picking_bill_id = w.picking_bill_id
			WHERE w.waybill_id = #{waybillId}
				AND w.del_flg = 0
  	</select>

	<select id="selectWaybillDetailsByWaybillNum" parameterType="java.lang.String"
			resultType="com.ecommerce.logistics.dao.dto.pickingbill.WaybillDO">
  		SELECT
  			w.waybill_id as waybillId,
  			w.waybill_num as waybillNum,
			w.carrier_id as carrierId,
			b.buyer_id as buyerId,
			b.buyer_name as buyerName,
			b.seller_id as sellerId,
			b.seller_name as sellerName,
			b.address as address,
  			w.create_time as createTime,
  			w.update_time as updateTime,
  			w.type as type,
  			w.status as status,
  			w.is_main_waybill as isMainWaybill,
  			w.parent_id as parentId,
  			w.picking_bill_id as pickingBillId,
  			w.dispatch_bill_id as dispatchBillId,
  			w.cancel_time as cancelTime,
  			w.complete_time as completeTime,
  			w.picking_time as pickingTime,
  			w.sync_flag as syncFlag,
  			w.external_waybill_status as externalWaybillStatus,
  			w.external_waybill_num as externalWaybillNum,
  			w.sync_fail_reason as syncFailReason,
  			w.monitor_complete_time as monitorCompleteTime,
  			w.can_monitor as canMonitor,
  			i.driver_id as driverId,
  			i.driver_name as driverName,
  			i.driver_phone as driverPhone,
  			i.vehicle_num as vehicleNum,
  			i.estimate_km as estimateKm,
  			i.estimate_duration as estimateDuration,
  			i.estimate_carriage as estimateCarriage,
  			i.published_carriage as publishedCarriage,
  			i.cancel_reason as cancelReason,
  			i.receive_time as receiveTime,
  			i.assign_time as assignTime,
  			i.arrive_warehouse_time as arriveWarehouseTime,
  			i.leave_warehouse_time as leaveWarehouseTime,
  			i.actual_carriage as actualCarriage,
  			i.actual_km as actualKm,
			p.quantity as quantity,
  		  	w.sign_remark as signRemark,
  		  	w.sign_quantity as signQuantity,
  		  	w.empty_load_charge as emptyLoadCharge,
  		  	w.empty_load_flag as emptyLoadFlag,
  		  	w.qr_code as qrCode,
  		  	w.need_monitor as needMonitor,
  		  	w.can_operate as canOperate,
  		  	w.bill_proxy_type as billProxyType,
  		  	w.ready_flag as readyFlag
  		FROM
			lgs_waybill w
  		LEFT JOIN
			lgs_waybill_info i on w.parent_id = i.waybill_id
		LEFT JOIN
			lgs_picking_bill b on b.picking_bill_id = w.picking_bill_id
		LEFT JOIN
			lgs_product_quantity_map p on w.waybill_id=p.map_id
  		WHERE
  			w.waybill_num = #{waybillNum} AND w.del_flg = 0
  	</select>
  	
  	<select id="selectWaybillByWaybillNum" parameterType="java.lang.String" resultType="com.ecommerce.logistics.dao.vo.Waybill">
  		SELECT * FROM lgs_waybill WHERE waybill_num = #{waybillNum} AND del_flg = 0
  	</select>

	<select id="selectWaybillQuantityByPickingBillIds" parameterType="java.util.List"
			resultType="com.ecommerce.logistics.api.dto.waybill.WaybillQuantityDTO">
		SELECT
			w.picking_bill_id as pickingBillId,
			w.waybill_id as waybillId,
			w.status as status,
			p.product_id as productId,
			m.quantity as quantity
		FROM
			lgs_waybill w
		LEFT JOIN
			lgs_product_quantity_map m on m.map_id = w.waybill_id
		LEFT JOIN
			lgs_product_info p on m.product_info_id = p.product_info_id
		WHERE
			w.del_flg = 0 AND w.picking_bill_id in
			<foreach collection="list" close=")" index="index" item="pickingBillId" open="(" separator=",">
				#{pickingBillId}
			</foreach>
	</select>
	
	<select id="selectWaybillIdsByParentId" parameterType="java.lang.String" resultType="java.lang.String">
		SELECT waybill_id FROM lgs_waybill WHERE parent_id = #{parentId} AND del_flg = 0
	</select>
	
	<select id="selectDispatchWaybillAssign" parameterType="java.lang.String" resultType="com.ecommerce.logistics.dao.dto.pickingbill.WaybillDO">
		SELECT 
			i.vehicle_num as vehicleNum,
			i.driver_name as driver,
			w.waybill_id as waybillId,
			w.waybill_num as waybillNum,
			m.quantity as quantity
		FROM 
			lgs_waybill_info i
		RIGHT JOIN
			lgs_waybill w ON w.waybill_id = i.waybill_id
		LEFT JOIN
			lgs_product_quantity_map m on m.map_id = w.waybill_id
		WHERE
			w.dispatch_bill_id = #{dispatchBillId}
	</select>
	<select id="selectWaybillDraftInfoByWaybillId" resultType="com.ecommerce.logistics.api.dto.waybill.WaybillDraftDTO">
		SELECT
			w.waybill_id as waybillId,
			w.waybill_num as waybillNum,
			p.warehouse_id as warehouseId,
			p.receive_address_id as receiveAddressId,
			w.delivery_time as deliveryTime,
			m.quantity as productQuantity
		FROM
			lgs_waybill w
		LEFT JOIN
			lgs_picking_bill p on w.picking_bill_id = p.picking_bill_id
		LEFT JOIN
			lgs_product_quantity_map m on m.map_id = w.waybill_id
		WHERE
		    w.waybill_id=#{waybillId} and w.del_flg=0
	</select>
	
	<select id="selectWaybillLocationInfo" parameterType="java.lang.String" 
	resultType="com.ecommerce.logistics.dao.dto.pickingbill.WaybillDO">
		SELECT 
			w.delivery_time as deliveryTime,
			w.delivery_time_range as deliveryTimeRange,
			w.waybill_id as waybillId,
			w.waybill_num as waybillNum,
			q.quantity as quantity,
			p.province_code as provinceCode,
			p.province as province,
			p.city as city,
			p.district as district,
			p.city_code as cityCode,
			p.district_code as districtCode,
			p.street as street,
			p.address as address,
			p.location as location,
			p.picking_bill_num as pickingBillNum,
			p.warehouse_id as warehouseId,
			info.note as note,
			info.unit as productUnit
		FROM
			lgs_picking_bill p
		RIGHT JOIN
			lgs_waybill w ON w.picking_bill_id = p.picking_bill_id
		LEFT JOIN
			lgs_product_quantity_map q ON q.map_id = w.waybill_id
		LEFT JOIN
			lgs_product_info info ON info.product_info_id = q.product_info_id
		WHERE
			w.waybill_id = #{waybillId} AND w.del_flg = 0
	</select>
	
	<select id="selectWaybillLocationByDistrict" parameterType="java.util.Map" resultType="com.ecommerce.logistics.dao.dto.pickingbill.WaybillDO">
		SELECT 
			w.delivery_time as deliveryTime,
			w.delivery_time_range as deliveryTimeRange,
			w.waybill_id as waybillId,
			w.waybill_num as waybillNum,
			q.quantity as quantity,
			p.province_code as provinceCode,
			p.city_code as cityCode,
			p.district_code as districtCode,
			p.province as province,
			p.city as city,
			p.district as district,
			p.street as street,
			p.address as address,
			p.location as location,
			p.picking_bill_num as pickingBillNum,
			p.warehouse_id as warehouseId,
			info.note as note,
			info.unit as productUnit
		FROM
			lgs_picking_bill p
		JOIN
			lgs_waybill w ON w.picking_bill_id = p.picking_bill_id
		LEFT JOIN
			lgs_product_quantity_map q ON q.map_id = w.waybill_id
		LEFT JOIN
			lgs_product_info info ON info.product_info_id = q.product_info_id
		WHERE
			w.status = #{waybillStatus} AND w.del_flg = 0 AND p.del_flg = 0
			<if test="provinceCode != null and provinceCode != '' ">
  				AND p.province_code = #{provinceCode}
	  		</if>
	  		<if test="cityCode != null and cityCode != '' ">
	  			AND p.city_code = #{cityCode}
	  		</if>
	  		<if test="districtCode != null and districtCode != '' ">
	  			AND p.district_code = #{districtCode}
	  		</if>
			<if test="warehouseId != null and warehouseId != ''">
				AND p.warehouse_id = #{warehouseId}
			</if>
			<if test="deliveryTime != null and deliveryTime != ''">
				AND w.delivery_time = #{deliveryTime}
			</if>
	</select>
	
	<select id="selectWaybillLocationByCon" parameterType="com.ecommerce.logistics.api.dto.waybill.WaybillLocationQueryDTO"
	resultType="com.ecommerce.logistics.dao.dto.pickingbill.WaybillDO">
		SELECT 
			w.delivery_time as deliveryTime,
			w.delivery_time_range as deliveryTimeRange,
			w.waybill_id as waybillId,
			w.waybill_num as waybillNum,
			q.quantity as quantity,
			p.province_code as provinceCode,
			p.city_code as cityCode,
			p.district_code as districtCode,
			p.province as province,
			p.city as city,
			p.district as district,
			p.street as street,
			p.address as address,
			p.location as location,
			p.picking_bill_num as pickingBillNum,
			p.warehouse_id as warehouseId,
			info.note as note,
			info.unit as productUnit
		FROM
			lgs_picking_bill p
		JOIN
			lgs_waybill w ON w.picking_bill_id = p.picking_bill_id
		LEFT JOIN
			lgs_product_quantity_map q ON q.map_id = w.waybill_id
		LEFT JOIN
			lgs_product_info info ON info.product_info_id = q.product_info_id
		WHERE
			w.status = #{waybillStatus} AND w.del_flg = 0 AND p.del_flg = 0
  			AND w.waybill_id in
  			<foreach collection="waybillIds" close=")" index="index" item="id" open="(" separator=",">
  				#{id}
  			</foreach>
	</select>

	<select id="selectWaybillMonitorPrepareForMergeData" resultType="com.ecommerce.logistics.api.dto.waybill.WaybillMonitorPrepareDTO">
		SELECT
			w.waybill_id as waybillId,
			w.waybill_num as waybillNum,
			w.is_main_waybill as isMainWaybill,
			w.carrier_id as carrierId,
			w.picking_bill_id as pickingBillId,
			w.type as waybillType,
			p.type as deliveryType,
			p.delivery_time_range as deliveryTimeRange,
			f.driver_id as driverId,
			f.driver_name as driverName,
			f.driver_phone as driverPhone,
			f.vehicle_num as vehicleNum,
			p.seller_id as sellerId,
			p.seller_name as sellerName,
			p.buyer_id as buyerId,
			p.buyer_name as buyerName,
			p.receive_address_id as unloadId,
			p.warehouse_id as warehouseId
		FROM
			lgs_waybill w
		LEFT JOIN
			lgs_picking_bill p on w.picking_bill_id = p.picking_bill_id
		LEFT JOIN
			lgs_waybill_info f on w.waybill_id = f.waybill_id
		WHERE
			w.del_flg = 0 AND w.parent_id=#{parentId}
	</select>
	<select id="selectWaybillMonitorPrepareParentData" resultType="com.ecommerce.logistics.api.dto.waybill.WaybillMonitorPrepareDTO">
		SELECT
			w.waybill_id as waybillId,
			w.waybill_num as waybillNum,
			w.is_main_waybill as isMainWaybill,
			w.carrier_id as carrierId,
			w.picking_bill_id as pickingBillId,
			w.type as waybillType,
			f.driver_id as driverId,
			f.driver_name as driverName,
			f.driver_phone as driverPhone,
			f.vehicle_num as vehicleNum
		FROM
			lgs_waybill w
		LEFT JOIN
			lgs_waybill_info f on w.waybill_id = f.waybill_id
		WHERE
			w.del_flg = 0 AND w.waybill_id=#{waybillId}
	</select>
	
	<select id="selectWaybillProductDetailByParentId" parameterType="java.lang.String"
	resultType="com.ecommerce.logistics.dao.dto.pickingbill.WaybillDO">
		SELECT
			p.province as province,
			p.city as city,
			p.district as district,
			p.province_code as provinceCode,
			p.city_code as cityCode,
			p.district_code as districtCode,
			p.street as street,
			p.address as address,
			p.delivery_sheet_num as deliverySheetNum,
			p.picking_bill_id as pickingBillId,
			p.picking_bill_num as pickingBillNum,
			p.buyer_id as buyerId,
			p.buyer_name as buyerName,
			p.seller_id as sellerId,
			p.seller_name as sellerName,
			p.receive_address_id as receiveAddressId,
			p.receiver as receiver,
			p.receiver_phone as receiverPhone,
			p.warehouse_id as warehouseId,
			p.delivery_time as deliveryTime,
			p.delivery_time_range as deliveryTimeRange,
			p.location as location,
			w.actual_quantity as actualQuantity,
			w.arrive_destination_time as arriveDestinationTime,
			w.begin_carry_time as beginCarryTime,
			w.complete_time as completeTime,
			w.picking_time as pickingTime,
			w.is_main_waybill as isMainWaybill,
			w.status as status,
			w.waybill_id as waybillId,
			w.waybill_num as waybillNum,
			w.actual_quantity as actualQuantity,
			w.sync_flag as syncFlag,
  		w.external_waybill_status as externalWaybillStatus,
  		w.external_waybill_num as externalWaybillNum,
  		w.sync_fail_reason as syncFailReason,
		  w.bill_proxy_type as billProxyType,
		  w.can_operate as canOperate,
			q.quantity as quantity,
			info.note as note,
			info.unit as productUnit,
			info.product_info_id as productInfoId,
			info.product_img as productImg,
			info.commodity_code as commodityCode,
			info.special_flag as specialFlag,
			info.carriage_unit_price as carriageUnitPrice,
			info.transport_category_id as transportCategoryId,
			info.product_id as productId,
			info.unloading_flow_code as unloadingFlowCode
		FROM
			lgs_picking_bill p
		RIGHT JOIN
			lgs_waybill w ON w.picking_bill_id = p.picking_bill_id
		LEFT JOIN
			lgs_product_quantity_map q ON q.map_id = w.waybill_id
		LEFT JOIN
			lgs_product_info info ON info.product_info_id = q.product_info_id
		WHERE
			w.parent_id = #{parentId} or w.waybill_id = #{parentId}
		ORDER BY  w.is_main_waybill DESC, w.waybill_sequence
	</select>
	
	<select id="selectSubWaybillInfoByParentId" parameterType="java.lang.String"
	resultType="com.ecommerce.logistics.dao.dto.pickingbill.WaybillDO">
		SELECT
			p.province as province,
			p.city as city,
			p.district as district,
			p.province_code as provinceCode,
			p.city_code as cityCode,
			p.district_code as districtCode,
			p.street as street,
			p.address as address,
			p.delivery_sheet_num as deliverySheetNum,
			p.picking_bill_num as pickingBillNum,
			p.receiver as receiver,
			p.receiver_phone as receiverPhone,
			p.warehouse_id as warehouseId,
			p.delivery_time as deliveryTime,
			p.delivery_time_range as deliveryTimeRange,
			p.location as location,
			p.receive_address_id as receiveAddressId,
			p.buyer_id as buyerId,
			p.buyer_name as buyerName,
			p.seller_id as sellerId,
			p.seller_name as sellerName,
			w.actual_quantity as actualQuantity,
			w.arrive_destination_time as arriveDestinationTime,
			w.begin_carry_time as beginCarryTime,
			w.complete_time as completeTime,
			w.picking_time as pickingTime,
			w.is_main_waybill as isMainWaybill,
			w.status as status,
			w.type as type,
			w.waybill_id as waybillId,
			w.actual_quantity as actualQuantity,
			w.close_reason as closeReason,
			w.close_time as closeTime,
			w.can_monitor as canMonitor,
			w.monitor_complete_time as monitorCompleteTime,
			q.quantity as quantity,
			info.note as note,
			info.unit as productUnit,
			info.special_flag as specialFlag,
			info.product_info_id as productInfoId,
			info.product_id as productId,
			info.product_img as productImg,
			info.qr_code as qrCode,
			info.carry_flag as carryFlag,
			info.transport_category_id as transportCategoryId,
			cate.category_name as transportCategoryName,
		    info.sign_type as signType
		FROM
			lgs_picking_bill p
		JOIN
			lgs_waybill w ON w.picking_bill_id = p.picking_bill_id
		LEFT JOIN
			lgs_product_quantity_map q ON q.map_id = w.waybill_id
		LEFT JOIN
			lgs_product_info info ON info.product_info_id = q.product_info_id
		LEFT JOIN
			lgs_transport_category cate ON info.transport_category_id = cate.transport_category_id
		WHERE
			w.parent_id = #{parentId}
		ORDER BY w.waybill_sequence
	</select>
	
	<select id="selectWaybillProductDetailByWaybillId" parameterType="java.lang.String"
	resultType="com.ecommerce.logistics.dao.dto.pickingbill.WaybillDO">
		SELECT
			p.province as province,
			p.city as city,
			p.district as district,
			p.street as street,
			p.address as address,
			p.delivery_sheet_num as deliverySheetNum,
			p.warehouse_id as warehouseId,
			p.receiver as receiver,
			p.receiver_phone as receiverPhone,
			p.delivery_time as deliveryTime,
			p.delivery_time_range as deliveryTimeRange,
			p.seller_id as sellerId,
			w.waybill_id as waybillId,
			w.waybill_num as waybillNum,
			w.external_waybill_num as externalWaybillNum,
			w.actual_quantity as actualQuantity,
			w.status as status,
			q.quantity as quantity,
			q.product_info_id as productInfoId,
			i.product_id as productId
		FROM
			lgs_waybill w
		LEFT JOIN
			lgs_picking_bill p ON w.picking_bill_id = p.picking_bill_id
		LEFT JOIN
			lgs_product_quantity_map q ON q.map_id = w.waybill_id
		LEFT JOIN
			lgs_product_info i ON q.product_info_id = i.product_info_id
		WHERE
			w.waybill_id = #{waybillId}
	</select>
	
	<select id="selectCompleteInfoBywaybillId" parameterType="java.lang.String"
	resultType="com.ecommerce.logistics.dao.dto.pickingbill.WaybillDO">
		SELECT
			w.complete_time as completeTime,
			i.driver_name as driver
		FROM
			lgs_waybill w
		LEFT JOIN
			lgs_waybill_info i ON i.waybill_id = w.waybill_id
		WHERE
			w.waybill_id = #{waybillId}
	</select>
	
	<select id="selectWaybillIdsByWarehouseAdminWaitDeliveryCon" parameterType="com.ecommerce.logistics.api.dto.waybill.WarehouseAdminWaitDeliveryQueryDTO"
	resultType="java.lang.String">
		SELECT
			w.waybill_id
		FROM
			lgs_picking_bill p
		JOIN
			lgs_waybill w ON w.picking_bill_id = p.picking_bill_id
			<if test="vehicleNum != null and vehicleNum!= '' ">
				JOIN
					lgs_waybill_info i ON i.waybill_id = w.waybill_id
			</if>
		WHERE
			p.warehouse_id = #{warehouseId} AND w.status = #{status} 
			AND w.is_main_waybill = 1 AND w.dispatch_bill_id = ''
			AND w.del_flg = 0 AND p.del_flg = 0
			<if test="waybillNum != null and waybillNum != '' ">
				AND w.waybill_num = #{waybillNum}
			</if>
			<if test="vehicleNum != null and vehicleNum != '' ">
				AND i.vehicle_num LIKE #{vehicleNum}
			</if>
		ORDER BY
			w.create_time DESC,w.waybill_id 
	</select>
	
	<select id="selectMainWaybillIdByWarehouseAdminProccessedCon" parameterType="com.ecommerce.logistics.api.dto.waybill.WarehouseAdminProccessedQueryDTO"
	resultType="java.lang.String">
		SELECT
			w.parent_id as parentId	
		FROM
			lgs_picking_bill p
		JOIN
			lgs_waybill w ON w.picking_bill_id = p.picking_bill_id and w.bill_proxy_type in ('030400100', '030400200')
		<if test="userId != null and userId != '' ">
			JOIN
				lgs_operation_record o ON o.entry_id = w.parent_id
		</if>
		<if test="regionCodeList != null and regionCodeList.size() > 0">
			INNER JOIN
			ba_region_all as auth on p.district_code = auth.region_son_code
		</if>
			left JOIN
				lgs_waybill_info i ON i.waybill_id = w.parent_id
		WHERE
			w.del_flg = 0 AND p.del_flg = 0 and w.parent_id is not null and w.parent_id != ''
			AND w.status in
			<foreach collection="statuss" close=")" index="index" item="status" open="(" separator=",">
				#{status}
			</foreach>
			<if test="userId != null and userId != '' ">
				AND o.operator_id = #{userId}
			</if>
			<if test="buyerId != null and buyerId != '' ">
				AND p.buyer_id = #{buyerId}
			</if>
			<if test="regionCodeList != null and regionCodeList.size() > 0">
				AND auth.region_code IN
				<foreach close=")" collection="regionCodeList" index="index" item="item" open="(" separator=",">
					#{item}
				</foreach>
			</if>
			<if test="waybillNum != null and waybillNum != '' ">
				AND w.parent_id = (SELECT waybill_id FROM lgs_waybill WHERE waybill_num = #{waybillNum})
			</if>
			<if test="beginArriveTime != null and beginArriveTime != ''">
		        <![CDATA[AND i.arrive_warehouse_time >= #{beginArriveTime}]]>
		     </if>
		     <if test="endArriveTime != null and endArriveTime != ''">
		        <![CDATA[AND i.arrive_warehouse_time <= concat(#{endArriveTime}, ' 23:59:59')]]>
		     </if>
		     <if test="beginLeaveTime != null and beginLeaveTime != ''">
		        <![CDATA[AND i.leave_warehouse_time >= #{beginLeaveTime}]]>
		     </if>
		     <if test="endLeaveTime != null and endLeaveTime != ''">
		        <![CDATA[AND i.leave_warehouse_time <= concat(#{endLeaveTime}, ' 23:59:59')]]>
		     </if>
			 <if test="warehouseIdList != null and warehouseIdList.size() > 0 ">
				AND p.warehouse_id IN
				<foreach collection="warehouseIdList" close=")" index="index" item="warehouseId" open="(" separator=",">
					#{warehouseId}
				</foreach>
			 </if>
			 <if test="vehicleNum != null and vehicleNum != ''">
				 and i.vehicle_num LIKE CONCAT('%', #{vehicleNum}, '%')
			 </if>
		GROUP BY
			w.parent_id
		ORDER BY
  			w.create_time DESC,w.parent_id
	</select>
	
	<select id="selectByWarehouseAdminWaitDeliveryByWaybillIds" parameterType="java.util.List"
	resultType="com.ecommerce.logistics.dao.dto.pickingbill.WaybillDO">
		SELECT
			w.waybill_id,
			w.waybill_num as waybillNum,
			w.status as status,
			p.delivery_time as deliveryTime,
			p.delivery_time_range as deliveryTimeRange,
			p.seller_name as sellerName,
			i.driver_name as driver,
			i.driver_phone as driverPhone,
			i.vehicle_num as vehicleNum,
			i.arrive_warehouse_time
		FROM
			lgs_picking_bill p
		JOIN
			lgs_waybill w ON w.picking_bill_id = p.picking_bill_id
		JOIN
			lgs_waybill_info i ON i.waybill_id = w.waybill_id
		WHERE
			w.parent_id IN 
			<foreach collection="waybillIds" close=")" index="index" item="id" open="(" separator=",">
				#{id}
			</foreach>
		ORDER BY
			w.waybill_id,w.is_main_waybill DESC 
	</select>
	
	<select id="selectByWarehouseAdminProccessedByMainWaybillIds" parameterType="java.util.List"
	resultType="com.ecommerce.logistics.dao.dto.pickingbill.WaybillDO">
		SELECT
		    w.waybill_id as waybillId,
			w.waybill_num as waybillNum,
			w.status as status,
			w.picking_bill_id as pickingBillId,
			w.is_main_waybill as isMainWaybill,
			w.actual_quantity as actualQuantity,
			i.driver_name as driverName,
			i.driver_phone as driverPhone,
			i.vehicle_num as vehicleNum,
			w.enter_factory_time as arriveWarehouseTime,
			i.leave_warehouse_time as leaveWarehouseTime,
			p.buyer_name as buyerName,
			p.seller_name as sellerName,
			p.delivery_time as deliveryTime,
			p.delivery_time_range as deliveryTimeRange,
			q.quantity as quantity,
			pi.note as note,
			pi.unit as productUnit,
		  pi.special_flag as specialFlag,
		  p.warehouse_id,
		  w.bill_proxy_type as billProxyType
		FROM
			lgs_picking_bill p
		RIGHT JOIN
			lgs_waybill w ON w.picking_bill_id = p.picking_bill_id
		LEFT JOIN
			lgs_product_quantity_map q on q.map_id = w.waybill_id
		LEFT JOIN
			lgs_product_info pi on pi.product_info_id = q.product_info_id
		LEFT JOIN
			lgs_waybill_info i ON i.waybill_id = w.waybill_id
		WHERE
			w.parent_id IN 
			<foreach collection="waybillIds" close=")" index="index" item="id" open="(" separator=",">
				#{id}
			</foreach>
		ORDER BY
			w.parent_id,w.is_main_waybill DESC
	</select>
	
	<select id="selectSecondaryStatusByParentId" parameterType="java.lang.String" resultType="java.lang.String">
		SELECT 
			status
		FROM
			lgs_waybill
		WHERE
			parent_id = #{parentId} AND is_main_waybill = 0 AND del_flg = 0
	</select>
	
	<select id="selectSubWaybillIdListByParentId" parameterType="java.lang.String" resultType="java.lang.String">
		SELECT 
			waybill_id
		FROM
			lgs_waybill
		WHERE
			parent_id = #{parentId} AND is_main_waybill = 0 AND del_flg = 0
	</select>
	
	<select id="selectSeckillWarehouseIdByUserId" resultType="java.lang.String">
		SELECT
			DISTINCT p.warehouse_id
		FROM
			lgs_picking_bill p
		JOIN
			lgs_waybill w ON w.picking_bill_id = p.picking_bill_id
		JOIN
			lgs_waybill_seckill s ON s.waybill_id = w.parent_id
		WHERE
			w.status = #{status} AND s.user_id = #{userId} 
			AND w.del_flg = 0 AND w.dispatch_bill_id = ''			
	</select>
	
	<select id="selectSeckillDistrictsByUserId" resultType="com.ecommerce.logistics.api.dto.waybill.DistrictListDTO">
		SELECT
			DISTINCT p.district as district, p.district_code as districtCode
		FROM
			lgs_picking_bill p
		JOIN
			lgs_waybill w ON w.picking_bill_id = p.picking_bill_id
		JOIN
			lgs_waybill_seckill s ON s.waybill_id = w.parent_id
		WHERE
			w.status = #{status} AND s.user_id = #{userId} 
			AND w.del_flg = 0 AND w.dispatch_bill_id = ''
	</select>
	
	<select id="selectSeckillWaybillIdsByCon" parameterType="com.ecommerce.logistics.api.dto.waybill.UserSeckillWaybillQueryDTO" resultType="java.lang.String">
		SELECT
			w.parent_id
		FROM
			lgs_waybill_seckill s
		JOIN
			lgs_waybill w ON w.parent_id = s.waybill_id
		JOIN
			lgs_picking_bill p ON p.picking_bill_id = w.picking_bill_id
		LEFT JOIN
			lgs_product_quantity_map q ON p.picking_bill_id = q.map_id
		LEFT JOIN
			lgs_product_info i ON q.product_info_id = i.product_info_id
		WHERE
			w.status = #{status} AND s.user_id = #{userId} 
			AND w.del_flg = 0
			<if test="warehouseId != null and warehouseId != '' ">
				AND p.warehouse_id = #{warehouseId}
			</if>
			<if test="districtCode != null and districtCode != '' ">
				AND p.district_code = #{districtCode}
			</if>
			<if test="beginDeliveryTime != null and beginDeliveryTime != ''">
		        <![CDATA[AND p.delivery_time >= #{beginDeliveryTime}]]>
			</if>
			<if test="endDeliveryTime != null and endDeliveryTime != ''">
		        <![CDATA[AND p.delivery_time <= #{endDeliveryTime}]]>
			</if>
			<if test="transportCategoryId != null and transportCategoryId != '' ">
				AND i.transport_category_id = #{transportCategoryId}
			</if>
		 GROUP BY
		 	w.parent_id
		 ORDER BY
		 	s.create_time DESC,w.parent_id
	</select>
	
	<select id="selectSeckillScanWaybillIdsByCon" parameterType="com.ecommerce.logistics.api.dto.waybill.SeckillScanQueryDTO" resultType="java.lang.String">
		SELECT
			w.parent_id
		FROM
			lgs_waybill w
		JOIN
			lgs_picking_bill p ON p.picking_bill_id = w.picking_bill_id
		LEFT JOIN
			lgs_product_quantity_map q ON p.picking_bill_id = q.map_id
		LEFT JOIN
			lgs_product_info i ON q.product_info_id = i.product_info_id
		WHERE
			w.status = #{status}
			AND w.del_flg = 0
			<if test="cityCode != null and cityCode != '' ">
				AND p.city_code = #{cityCode}
			</if>
			<if test="transportCategoryId != null and transportCategoryId != '' ">
				AND i.transport_category_id = #{transportCategoryId}
			</if>
		 GROUP BY
		 	w.parent_id
		 ORDER BY
		 	w.create_time DESC,w.parent_id
	</select>
	
	<select id="selectDriverWaybillIdsByCon" parameterType="com.ecommerce.logistics.api.dto.waybill.DriverWaybillListQueryDTO" resultType="java.lang.String">
		SELECT
			w.waybill_id as waybillId
		FROM
			lgs_waybill_info i
		JOIN
			lgs_waybill w ON i.waybill_id = w.waybill_id
		WHERE
			i.driver_id = #{userId} AND w.del_flg = 0 and w.can_operate = 1
			AND (
		w.status IN
			<foreach collection="statuss" close=")" index="index" item="status" open="(" separator=",">
				#{status}
			</foreach>
		<if test="hasBuyerDriverDoing != null and hasBuyerDriverDoing == 1">
			or (w.can_monitor = '1' and w.type = '030080300' and w.status = '030070700' and w.monitor_complete_time is null)
		</if>
		<if test="hasBuyerDriverComplete != null and hasBuyerDriverComplete == 1">
			and (
			(w.type != '030080300')
			or (w.can_monitor = '0')
			or (w.can_monitor = '1' and w.type = '030080300' and w.status = '030070700' and w.monitor_complete_time is not null)
			)
		</if>
		)

		 ORDER BY
		 	w.create_time DESC,w.waybill_id
	</select>
	
	<select id="selectCarrierWaybillIdsByCon" parameterType="com.ecommerce.logistics.api.dto.waybill.DriverWaybillListQueryDTO" resultType="java.lang.String">
		SELECT
			w.waybill_id as waybillId
		FROM
			lgs_waybill w
		WHERE
			w.carrier_id = #{userId}
			and w.can_operate = 1
			AND w.del_flg = 0 AND w.is_main_waybill = 1
			AND (
			w.status IN
				<foreach collection="statuss" close=")" index="index" item="status" open="(" separator=",">
					#{status}
				</foreach>
			)
		 ORDER BY
		 	w.create_time DESC,w.waybill_id
	</select>
	
	<select id="selectSeckillWaybillByParentIds" parameterType="java.util.List" resultType="com.ecommerce.logistics.dao.dto.pickingbill.WaybillDO">
		SELECT
			p.province as province,
			p.city as city,
			p.district as district,
			p.street as street,
			p.address as address,
			p.receive_address_id as receiveAddressId,
			p.warehouse_id as warehouseId,
			p.delivery_time as deliveryTime,
			p.delivery_time_range as deliveryTimeRange,
			p.picking_bill_id as pickingBillId,
			i.estimate_km as estimateKm,
			i.published_carriage as publishedCarriage,
			i.driver_name as driverName,
			i.driver_phone as driverPhone,
			i.vehicle_num as vehicleNum,
			w.waybill_id as waybillId,
			w.is_main_waybill as isMainWaybill,
			w.status as status,
			w.waybill_num as waybillNum,
			w.parent_id as parentId,
			w.type as type,
		  w.monitor_complete_time as monitorCompleteTime,
			m.quantity as quantity,
			lpi.product_id as productId,
			lpi.product_img as productImg,
			lpi.transport_category_id as transportCategoryId,
			lpi.note as note,
			lpi.unit as productUnit,
			ltc.category_name as transportCategoryName,
		  w.ready_flag as readyFlag
		FROM
			lgs_picking_bill p
		RIGHT JOIN
			lgs_waybill w ON w.picking_bill_id = p.picking_bill_id
		LEFT JOIN
			lgs_waybill_info i ON i.waybill_id = w.waybill_id
		LEFT JOIN
			lgs_product_quantity_map m on w.waybill_id = m.map_id
		LEFT JOIN
			lgs_product_info lpi on lpi.product_info_id = m.product_info_id
		LEFT JOIN
			lgs_transport_category ltc on lpi.transport_category_id = ltc.transport_category_id
		WHERE
			w.parent_id in
			<foreach collection="waybillIds" close=")" index="index" item="id" open="(" separator=",">
				#{id}
			</foreach>
		ORDER BY
			w.parent_id,w.is_main_waybill DESC,w.waybill_sequence ASC
	</select>
	
	<select id="queryTradeWaybillList" resultType="com.ecommerce.logistics.api.dto.waybill.TradeWaybillDTO">
		SELECT
			t1.waybill_id as waybillId,
			t1.waybill_num as waybillNum,
			t1.status as waybillStatus,
			t1.complete_time as completeTime,
			t1.actual_quantity as actualShippingQuantity,
			t1.update_time as updateTime,
		  t1.empty_load_flag as emptyLoadFlag,
		  t1.empty_load_charge as emptyLoadCharge,
		  ifnull(t1.sign_quantity, '-') as signQuantity,
			t2.leave_warehouse_time as leaveWarehouseTime,
			t2.driver_id as driverId,
			t2.driver_name as driverName,
			t2.driver_phone as driverPhone,
			t2.vehicle_num as vehicleNum,
			t3.quantity as shippingQuantity,
			t4.product_id as resourceId,
			t4.product_img as productImg,
      t4.sign_type as signType
		FROM
			lgs_waybill t1
		LEFT JOIN
			lgs_waybill_info t2 ON t1.parent_id=t2.waybill_id
		LEFT JOIN
			lgs_product_quantity_map t3 ON t1.waybill_id=t3.map_id
		LEFT JOIN
			lgs_product_info t4 on t3.product_info_id=t4.product_info_id
		<where>
			t1.picking_bill_id IN
			<foreach collection="pickingBillIdList" close=")" index="index" item="pickingBillId" open="(" separator=",">
				#{pickingBillId}
			</foreach>
			AND t1.status IN
			<foreach collection="statusList" close=")" index="index" item="status" open="(" separator=",">
				#{status}
			</foreach>
		</where>
	</select>
	
	<select id="selectCityAndQuantityByWaybilId" parameterType="java.lang.String"
			resultType="com.ecommerce.logistics.dao.dto.pickingbill.WaybillDO">
		SELECT
			p.city_code as cityCode,
			p.province_code as provinceCode,
			sum(m.quantity)  as quantity
		FROM
			lgs_picking_bill p
		LEFT JOIN
			lgs_waybill w ON w.picking_bill_id = p.picking_bill_id
		LEFT JOIN
			lgs_product_quantity_map m ON m.map_id = w.waybill_id
		WHERE
			w.parent_id = #{waybillId}
		GROUP BY
			w.parent_id
	</select>
	<select id="queryWaybillTotalQuantity" resultType="com.ecommerce.logistics.dao.dto.waybill.WaybillQuantityDO" >
		SELECT
			SUM(m.quantity) as quantity,
			SUM(w.actual_quantity) as actualQuantity
		FROM
			lgs_waybill w
		JOIN
			lgs_product_quantity_map m ON m.map_id = w.waybill_id
		WHERE
			w.waybill_id IN
		<foreach collection="list" close=")" index="index" item="waybillId" open="(" separator=",">
			#{waybillId}
		</foreach>
	</select>
	<select id="selectMainWaybillList" parameterType="com.ecommerce.logistics.dao.dto.waybill.WaybillQueryDO"
			resultType="com.ecommerce.logistics.dao.dto.waybill.MainWaybillDO">
		SELECT
			t1.waybill_id        AS waybillId,
			t1.waybill_num       AS waybillNum,
			t1.status 			 AS status,
			t1.type              AS type,
			t1.create_time       AS createTime,
			t1.delivery_time 	 AS deliveryTime,
			t1.delivery_time_range AS deliveryTimeRange,
			t2.estimate_carriage AS estimateCarriage,
			t2.estimate_km       AS estimateKm,
			t2.estimate_duration AS estimateDuration,
			t2.published_carriage AS publishedCarriage,
			t2.actual_carriage AS actualCarriage,
			t2.driver_name AS driverName,
			t2.driver_phone AS driverPhone,
			t2.vehicle_num AS vehicleNum
		FROM
			lgs_waybill AS t1
		LEFT JOIN
			lgs_waybill_info as t2 on t1.waybill_id=t2.waybill_id
		WHERE
			t1.is_main_waybill = 1
			<if test="waybillNum != null and waybillNum != '' ">
				AND t1.waybill_num = #{waybillNum}
			</if>
			<if test="statusList != null and statusList.size() > 0 ">
				AND t1.status IN
				<foreach collection="statusList" close=")" index="index" item="status" open="(" separator=",">
					#{status}
				</foreach>
			</if>
			<if test="deliveryTime != null and deliveryTime != '' ">
				AND t1.delivery_time = #{deliveryTime}
			</if>
			<if test="beginDeliveryTime != null and beginDeliveryTime != ''">
		        <![CDATA[AND t1.delivery_time >= #{beginDeliveryTime}]]>
		     </if>
		     <if test="endDeliveryTime != null and endDeliveryTime != ''">
		        <![CDATA[AND t1.delivery_time <= #{endDeliveryTime}]]>
		     </if>
			<if test="waybillType != null and waybillType != '' ">
				AND t1.type = #{waybillType}
			</if>
		ORDER BY
			t1.create_time DESC,t1.waybill_id
	</select>
	
	<select id="selectWaitCheckMainIdByCon" parameterType="com.ecommerce.logistics.dao.dto.waybill.WaybillQueryDO" resultType="java.lang.String">
		select 
			w.parent_id
		FROM
			lgs_waybill w
		JOIN
			lgs_picking_bill p ON p.picking_bill_id = w.picking_bill_id
		<if test="regionCodeList != null and regionCodeList.size() > 0">
		INNER JOIN
			ba_region_all as auth on p.district_code = auth.region_son_code
		</if>
		<where>
			w.del_flg = 0 AND w.status in
			<foreach collection="statusList" close=")" index="index" item="status" open="(" separator=",">
				#{status}
			</foreach>
			<if test="regionCodeList != null and regionCodeList.size() > 0">
				AND auth.region_code IN
				<foreach close=")" collection="regionCodeList" index="index" item="item" open="(" separator=",">
					#{item}
				</foreach>
			</if>
			<if test="warehouseIdList != null and warehouseIdList.size() > 0">
				AND p.warehouse_id IN
				<foreach close=")" collection="warehouseIdList" index="index" item="item" open="(" separator=",">
					#{item}
				</foreach>
			</if>
			<if test="pickingBillNum != null and pickingBillNum != ''">
				AND p.picking_bill_num = #{pickingBillNum} 
			</if>
			<if test="waybillNum != null and waybillNum != ''">
				AND w.parent_id = (select waybill_id from lgs_waybill where waybill_num = #{waybillNum})
			</if>
			<if test="deliveryTime != null and deliveryTime != '' ">
				AND w.delivery_time = #{deliveryTime}
			</if>
		</where>
		GROUP BY 
			w.parent_id
		ORDER BY
			w.create_time DESC, w.parent_id
	</select>

	<select id="selectSubWaybillListByParentId" resultType="com.ecommerce.logistics.dao.dto.waybill.SubWaybillDO">
		SELECT
			t1.waybill_id AS waybillId,
			t1.parent_id AS parentId,
			t1.waybill_num AS waybillNum,
			t1.status as status,
			t1.external_waybill_num as externalWaybillNum,
			t1.actual_quantity as actualQuantity,
			t1.enter_factory_time as enterFactoryTime,
			t2.picking_bill_id as pickingBillId,
			t2.picking_bill_num AS pickingBillNum,
			t2.delivery_sheet_num AS deliverySheetNum,
			t2.delivery_time AS deliveryTime,
			t2.delivery_time_range AS deliveryTimeRange,
			t2.delivery_sheet_num as deliverySheetNum,
			t2.seller_name as sellerName,
			t2.buyer_name as buyerName,
			t2.province_code as receiveProvinceCode,
			t2.city_code as receiveCityCode,
			t2.district_code as districtCode,
			t2.province as receiveProvince,
			t2.city as receiveCity,
			t2.district as district,
			t2.warehouse_id as warehouseId,
			t2.picking_time as pickingTime,
			concat(t2.province, t2.city, t2.district) as receiveDistrict,
			concat(t2.street, t2.address) as receiveDetailAddress,
			concat(t2.province, t2.city, t2.district, t2.street, t2.address) as receiveAddress,
			t3.quantity as productQuantity,
			t4.special_flag as specialFlag,
			t4.unit as productUnit,
			t4.note as productDesc,
			t4.product_info_id as productInfoId,
			t4.product_id as resourceId,
			t4.product_img as productImg,
			t5.transport_category_id as transportCategoryId,
			t5.category_name as transportCategoryName
		FROM
			lgs_waybill AS t1
		LEFT JOIN
			lgs_picking_bill AS t2 ON t1.picking_bill_id = t2.picking_bill_id
		LEFT JOIN
			lgs_product_quantity_map AS t3 ON t1.waybill_id=t3.map_id
		LEFT JOIN
			lgs_product_info AS t4 ON t3.product_info_id=t4.product_info_id
		LEFT JOIN
			lgs_transport_category AS t5 ON t4.transport_category_id=t5.transport_category_id
		WHERE
			t1.del_flg=0 AND t1.picking_bill_id != '' AND t1.parent_id IN
			<foreach collection="list" close=")" index="index" item="parentId" open="(" separator=",">
				#{parentId}
			</foreach>
			<if test="statusList != null and statusList.size > 0">
				AND t1.status in
				<foreach collection="statusList" close=")" index="index" item="status" open="(" separator=",">
					#{status}
				</foreach>
			</if>
	</select>
	<select id="selectMainWaybillIdByCondition" parameterType="com.ecommerce.logistics.dao.dto.waybill.WaybillQueryDO"
		resultType="java.lang.String">
		SELECT
			t1.parent_id
		FROM
			lgs_picking_bill AS t2
		LEFT JOIN
			lgs_waybill AS t1 ON t1.picking_bill_id = t2.picking_bill_id
		LEFT JOIN
			lgs_waybill_info AS t3 on t1.waybill_id=t3.waybill_id
		<if test="regionCodeList != null and regionCodeList.size() > 0">
		INNER JOIN
			ba_region_all as auth on t2.district_code = auth.region_son_code
		</if>
		WHERE
			t1.del_flg = 0
			<if test="regionCodeList != null and regionCodeList.size() > 0">
				AND auth.region_code IN
				<foreach close=")" collection="regionCodeList" index="index" item="item" open="(" separator=",">
					#{item}
				</foreach>
			</if>
			<if test="warehouseId != null and warehouseId != '' ">
				AND t2.warehouse_id = #{warehouseId}
			</if>
			<if test="pickingBillNum != null and pickingBillNum != '' ">
				AND t2.picking_bill_num = #{pickingBillNum}
			</if>
			<if test="deliverySheetNum != null and deliverySheetNum != ''">
				and t2.delivery_sheet_num = #{deliverySheetNum}
			</if>
			<if test="deliveryTime != null and deliveryTime != '' ">
				AND t1.delivery_time = #{deliveryTime}
			</if>
			<if test="beginDeliveryTime != null and beginDeliveryTime != ''">
				<![CDATA[AND t1.delivery_time >= #{beginDeliveryTime}]]>
			</if>
			<if test="endDeliveryTime != null and endDeliveryTime != ''">
		        <![CDATA[AND t1.delivery_time <= #{endDeliveryTime}]]>
		    </if>
			<if test="waybillNum != null and waybillNum != '' ">
				AND t1.waybill_num = #{waybillNum}
			</if>
		  <if test="candidateParentId != null and candidateParentId != ''">
				and t1.parent_id = #{candidateParentId}
			</if>
			<if test="waybillType != null and waybillType != '' ">
				AND t1.type = #{waybillType}
			</if>
			<if test="statusList != null and statusList.size() > 0 ">
				AND (t1.status IN
				<foreach collection="statusList" close=")" index="index" item="status" open="(" separator=",">
					#{status}
				</foreach>
				<if test="isSelfPickDelivering != null and isSelfPickDelivering == 1">
					or (t1.can_monitor = '1' and t1.type = '030080300' and t1.status = '030070700' and t1.monitor_complete_time is null)
				</if>
				<if test="isSelfPickComplete != null and isSelfPickComplete == 1">
					and (
						(t1.type != '030080300')
						or (t1.can_monitor = '0')
						or (t1.can_monitor = '1' and t1.type = '030080300' and t1.status = '030070700' and t1.monitor_complete_time is not null)
					)
				</if>
				)
			</if>
			<if test="vehicleNum != null and vehicleNum != '' ">
				AND t3.vehicle_num LIKE CONCAT('%', #{vehicleNum}, '%')
			</if>
			<if test="buyerId != null and buyerId != ''">
				and t2.buyer_id = #{buyerId}
			</if>
		GROUP BY
			t1.parent_id
		ORDER BY
			t1.create_time DESC, t1.parent_id
	</select>
	<select id="selectWaitDeliveryMainWaybillIdByCondition" parameterType="com.ecommerce.logistics.dao.dto.waybill.WaybillQueryDO"
		resultType="java.lang.String">
		SELECT
			t1.parent_id
		FROM
			lgs_picking_bill AS t2
		JOIN
			lgs_waybill AS t1 ON t1.picking_bill_id = t2.picking_bill_id
		<if test="vehicleNum != null and vehicleNum != '' ">
			JOIN
				lgs_waybill_info AS t3 on t1.parent_id=t3.waybill_id
		</if>
		<if test="regionCodeList != null and regionCodeList.size() > 0">
			INNER JOIN
			ba_region_all as auth on t2.district_code = auth.region_son_code
		</if>
		WHERE
			t1.del_flg = 0
			<if test="warehouseIdList != null and warehouseIdList.size() > 0 ">
				AND t2.warehouse_id IN
				<foreach collection="warehouseIdList" close=")" index="index" item="warehouseId" open="(" separator=",">
					#{warehouseId}
				</foreach>
			</if>
			<if test="pickingBillNum != null and pickingBillNum != '' ">
				AND t2.picking_bill_num = #{pickingBillNum}
			</if>
			<if test="beginDeliveryTime != null and beginDeliveryTime != ''">
		        <![CDATA[AND t1.delivery_time >= #{beginDeliveryTime}]]>
		     </if>
		     <if test="endDeliveryTime != null and endDeliveryTime != ''">
		        <![CDATA[AND t1.delivery_time <= #{endDeliveryTime}]]>
		     </if>
			<if test="waybillNum != null and waybillNum != '' ">
				AND t1.parent_id = (SELECT waybill_id FROM lgs_waybill WHERE waybill_num = #{waybillNum})
			</if>
			<if test="statusList != null and statusList.size() > 0 ">
				AND t1.status IN
				<foreach collection="statusList" close=")" index="index" item="status" open="(" separator=",">
					#{status}
				</foreach>
			</if>
			<if test="regionCodeList != null and regionCodeList.size() > 0">
				AND auth.region_code IN
				<foreach close=")" collection="regionCodeList" index="index" item="item" open="(" separator=",">
					#{item}
				</foreach>
			</if>
			<if test="vehicleNum != null and vehicleNum != '' ">
				AND t3.vehicle_num LIKE CONCAT('%', #{vehicleNum}, '%')
			</if>
			<if test="buyerId != null and buyerId != '' ">
				AND t2.buyer_id = #{buyerId}
			</if>
			<if test="isEntered != null">
				<choose>
					<when test="isEntered">
						AND (t1.enter_factory_time IS NOT NULL OR t1.enter_factory_time != '')
					</when>
					<otherwise>
						AND (t1.enter_factory_time IS NULL OR t1.enter_factory_time = '')
					</otherwise>
				</choose>
			</if>
		and (t1.bill_proxy_type = '030400100' or t1.bill_proxy_type = '030400200')
		GROUP BY
			t1.parent_id
		ORDER BY
			t1.create_time DESC, t1.parent_id
	</select>
	<select id="selectMainWaybillListById" resultType="com.ecommerce.logistics.dao.dto.waybill.MainWaybillDO">
		SELECT
			t1.waybill_id        AS waybillId,
			t1.waybill_num       AS waybillNum,
			t1.status 			 AS status,
			t1.type              AS type,
			t1.create_time       AS createTime,
			t1.complete_time     AS completeTime,
			t1.delivery_time 	 AS deliveryTime,
			t1.delivery_time_range AS deliveryTimeRange,
			t1.sync_flag 	 AS syncFlag,
			t1.carrier_id AS carrierId,
			t1.external_waybill_status AS externalWaybillStatus,
			t2.estimate_carriage AS estimateCarriage,
			t2.estimate_km       AS estimateKm,
			t2.estimate_duration AS estimateDuration,
			t2.published_carriage AS publishedCarriage,
			t2.actual_carriage AS actualCarriage,
			t2.driver_id as driverId,
			t2.driver_name AS driverName,
			t2.driver_phone AS driverPhone,
			t2.vehicle_num AS vehicleNum,
			t2.arrive_warehouse_time as arriveWarehouseTime,
			t1.can_monitor as canMonitor,
			t1.monitor_complete_time as monitorCompleteTime,
		  t1.bill_proxy_type as billProxyType,
		  t1.can_operate as canOperate,
		  t1.platform_evaluate_flag as platformEvaluateFlag,
		  t1.driver_evaluate_flag as driverEvaluateFlag
		FROM
			lgs_waybill AS t1
		LEFT JOIN
			lgs_waybill_info as t2 on t1.waybill_id=t2.waybill_id
		WHERE
			t1.is_main_waybill = 1 AND t1.parent_id IN
			<foreach collection="list" close=")" index="index" item="parentId" open="(" separator=",">
				#{parentId}
			</foreach>
	</select>
	<select id="selectMainWaybillInfoByMainIds" resultType="com.ecommerce.logistics.dao.dto.waybill.MainWaybillDO">
		SELECT
			t1.waybill_id        AS waybillId,
			t1.waybill_num       AS waybillNum,
			t1.status 			 AS status,
			t1.type              AS type,
			t1.create_time       AS createTime,
			t1.delivery_time 	 AS deliveryTime,
			t1.delivery_time_range AS deliveryTimeRange,
			t2.estimate_carriage AS estimateCarriage,
			t2.estimate_km       AS estimateKm,
			t2.estimate_duration AS estimateDuration,
			t2.published_carriage AS publishedCarriage,
			t2.driver_name AS driverName,
			t2.driver_phone AS driverPhone,
			t2.vehicle_num AS vehicleNum,
			t2.arrive_warehouse_time as arriveWarehouseTime			
		FROM
			lgs_waybill AS t1
		LEFT JOIN
			lgs_waybill_info as t2 on t1.waybill_id=t2.waybill_id
		WHERE
			t1.waybill_id IN
			<foreach collection="list" close=")" index="index" item="parentId" open="(" separator=",">
				#{parentId}
			</foreach>
	</select>
	<select id="selectSubWaybillByParentId" resultType="com.ecommerce.logistics.dao.vo.Waybill">
		SELECT
			status as status,
			waybill_id as waybillId
		FROM
			lgs_waybill
		WHERE
			parent_id = #{parentId} AND is_main_waybill = 0 AND del_flg = 0
	</select>
	<select id="queryTradeWaybillDetail" resultType="com.ecommerce.logistics.dao.dto.waybill.TradeWaybillDetailDO">
		SELECT
			t1.waybill_id as waybillId,
			t1.waybill_num as waybillNum,
			t1.status as status,
			t1.type as type,
			t1.create_time as createTime,
			t1.actual_quantity as actualQuantity,
			t1.external_waybill_num as externalWaybillNum,
			t2.vehicle_num as vehicleNum,
			t2.driver_name as driverName,
			t2.driver_phone as driverPhone,
			t2.leave_warehouse_time as leaveWarehouseTime,
			t3.receiver as receiver,
			t3.warehouse_id as warehouseId,
			concat(t3.province, t3.city, t3.district, t3.address) as receiveAddress,
			t3.location as receiveAddressLocation,
			t4.quantity as quantity,
			t5.unit as productUnit,
			t5.product_img as productImg,
			t5.note as productDetail,
			t5.sign_type as signType,
		  t1.external_waybill_status as externalWaybillStatus,
		  t1.sync_fail_reason as syncFailReason,
		  t1.bill_proxy_type as billProxyType
		FROM
			lgs_waybill as t1
		LEFT JOIN
			lgs_waybill_info as t2 on t1.parent_id=t2.waybill_id
		LEFT JOIN
			lgs_picking_bill as t3 on t1.picking_bill_id=t3.picking_bill_id
		LEFT JOIN
			lgs_product_quantity_map as t4 on t1.waybill_id=t4.map_id
		LEFT JOIN
			lgs_product_info as t5 on t4.product_info_id=t5.product_info_id
		WHERE
			t1.waybill_id=#{waybillId}
	</select>
	
	<select id="selectWaybillSnatchCountByCon" parameterType="com.ecommerce.logistics.dao.dto.waybill.SnatchCountQueryDO"
	resultType="int">
		SELECT
			COUNT(*)
		FROM 
			lgs_waybill
		WHERE
			carrier_id = #{carrierId}
			AND delivery_time = #{deliveryTime}
			AND is_main_waybill = 1
			AND del_flg = 0
			AND status in
			<foreach collection="statuss" close=")" index="index" item="status" open="(" separator=",">
				#{status}
			</foreach>
	</select>
	
	<select id="selectSubWaybillInfoByWaybillIds" parameterType="java.util.Map"
	resultType="com.ecommerce.logistics.dao.dto.pickingbill.WaybillDO">
		SELECT
			t1.waybill_id as waybillId,
			t1.status as status,
			t1.type as type,
			t1.dispatch_bill_id as dispatchBillId,
			t1.picking_bill_id as pickingBillId,
			t1.actual_quantity as actualQuantity,
			t2.quantity as quantity
		FROM
			lgs_waybill t1
		JOIN
			lgs_product_quantity_map t2 ON t1.waybill_id = t2.map_id
		WHERE
			t1.waybill_id IN
			<foreach collection="subWaybillIds" close=")" index="index" item="subWaybillId" open="(" separator=",">
				#{subWaybillId}
			</foreach>
	</select>

	<select id="queryWaybillTotalQuantityByParentId" resultType="com.ecommerce.logistics.dao.dto.waybill.WaybillQuantityDO" >
		SELECT
			w.parent_id as waybillId,
			SUM(m.quantity) as quantity,
			SUM(w.actual_quantity) as actualQuantity
		FROM
			lgs_waybill w
		INNER JOIN
			lgs_product_quantity_map m ON m.map_id = w.waybill_id
		WHERE
			w.parent_id IN
			<foreach collection="list" close=")" index="index" item="waybillId" open="(" separator=",">
				#{waybillId}
			</foreach>
		GROUP BY
			w.parent_id
	</select>

	<select id="queryWaybillTotalQuantityByWaybillId" resultType="com.ecommerce.logistics.dao.dto.waybill.WaybillQuantityDO" >
		SELECT
		w.waybill_id as waybillId,
		m.quantity as quantity,
		w.actual_quantity as actualQuantity,
		w.sign_quantity as signQuantity
		FROM
		lgs_waybill w
		INNER JOIN
		lgs_product_quantity_map m ON m.map_id = w.waybill_id
		WHERE
		w.waybill_id = #{waybillId}
		AND w.del_flg = 0
		GROUP BY
		w.waybill_id
	</select>


	<select id="countDriverHalfwayWaybill" parameterType="com.ecommerce.logistics.dao.dto.waybill.HalfwayWaybillDO"
			resultType="java.lang.Integer" >
		SELECT
			count(*)
		FROM
			lgs_waybill AS t1
		INNER JOIN
			lgs_waybill_info AS t2 ON t1.waybill_id = t2.waybill_id
		WHERE
			(
				t1.status=#{deliveringStatus}
				or (t1.status='030070700' and t1.can_monitor = 1 and t1.type = '030080300' and t1.monitor_complete_time is null)
			)
			and t2.driver_id=#{driverId}
	</select>

	<select id="queryHalfWayVehicleNum" resultType="java.lang.String">
		select
			distinct info.vehicle_num
		from lgs_waybill lw, lgs_waybill_info info
		where lw.waybill_id = info.waybill_id and lw.del_flg = 0 and info.del_flg = 0
		and info.driver_id = #{driverId}
		and (
			lw.status = '030070600'
			or (lw.status='030070700' and lw.can_monitor = 1 and lw.type = '030080300' and lw.monitor_complete_time is null)
		)
		and info.vehicle_num is not null
	</select>

	<select id="queryMainWaybillIdList" resultType="java.lang.String" >
		SELECT
		  DISTINCT t1.parent_id
		FROM
		  lgs_waybill AS t1
		INNER JOIN
	      lgs_picking_bill AS t2 ON t1.picking_bill_id = t2.picking_bill_id
		WHERE
		  t2.delivery_sheet_num IN
		  <foreach collection="deliverySheetNumList" close=")" index="index" item="deliverySheetNum" open="(" separator=",">
			#{deliverySheetNum}
		  </foreach>
		  AND t1.status IN
		  <foreach collection="waybillStatusList" close=")" index="index" item="waybillStatus" open="(" separator=",">
			#{waybillStatus}
		  </foreach>
	</select>
	<select id="queryPickingListByWaybillNum" resultType="com.ecommerce.logistics.api.dto.pickingbill.PickingBillDTO">
		SELECT
			t2.delivery_sheet_num as deliverySheetNum,
			t4.carriage_unit_price as unitCarriage
		FROM
			lgs_waybill as t1
		LEFT JOIN
			lgs_picking_bill as t2 on t1.picking_bill_id = t2.picking_bill_id
		LEFT JOIN
			lgs_product_quantity_map as t3 on t2.picking_bill_id = t3.map_id
		LEFT JOIN
			lgs_product_info as t4 on t3.product_info_id=t4.product_info_id
		WHERE
			t1.waybill_num IN
			<foreach collection="list" close=")" index="index" item="waybillNum" open="(" separator=",">
				#{waybillNum}
			</foreach>
	</select>

	<select id="queryDeliverySheetNumByWaybillId" resultType="com.ecommerce.logistics.api.dto.waybill.WaybillCodeDTO">
		select
		       waybill.waybill_id as waybillId,
		       picking.delivery_sheet_num as deliverySheetNum,
		       waybill.empty_load_flag as emptyLoadFlag,
		       waybill.actual_quantity as actualQuantity
		from
		lgs_picking_bill picking, lgs_waybill waybill
		where picking.picking_bill_id = waybill.picking_bill_id and picking.del_flg = 0 and waybill.del_flg = 0
		and waybill.waybill_id in
		<foreach collection="list" item="wii" open="(" separator="," close=")">
			#{wii}
		</foreach>
	</select>

	<select id="queryWaitInvoiceWaybillParentIdList" parameterType="com.ecommerce.logistics.api.dto.invoice.InvoiceBillCondDTO" resultType="java.lang.String">
		select distinct lw.parent_id
		from lgs_waybill lw
		<if test="regionCodeList != null and regionCodeList.size() > 0">
			inner join lgs_picking_bill as lpb on lw.picking_bill_id = lpb.picking_bill_id
			inner join ba_region_all as bra on lpb.district_code = bra.region_son_code
		</if>
		where lw.status = '030070700' and lw.type = '030080200' and lw.del_flg = 0
		and lw.invoice_status in ('', '2', '5', '7')
		and lw.carrier_id is not null and lw.carrier_id != ''
		<if test="waybillNum != null and waybillNum != ''">
			and lw.waybill_num = #{waybillNum}
		</if>
		<if test="carrierId != null and carrierId != ''">
			and lw.carrier_id = #{carrierId}
		</if>
		<if test="invoiceTargetType != null and invoiceTargetType != ''">
			and lw.invoice_target_type = #{invoiceTargetType}
		</if>
		<if test="invoiceUserId != null and invoiceUserId != ''">
			and lw.invoice_user_id = #{invoiceUserId}
		</if>
		<if test="regionCodeList != null and regionCodeList.size() > 0">
			and lpb.del_flg = 0
			and bra.region_code in
			<foreach collection="regionCodeList" index="rin" item="rit" open="(" separator="," close=")">
				#{rit}
			</foreach>
		</if>

		order by lw.create_time desc
	</select>

	<select id="queryWaitInvoiceWaybillByIds" resultType="com.ecommerce.logistics.api.dto.invoice.InvoiceBillListDTO">
		select
			lw.waybill_id as waybillId,
			lw.waybill_num as waybillNum,
			lw.carrier_id as carrierId,
			lw.invoice_status as invoiceStatus,
			lw.create_time as createTime,
			lw.complete_time as completeTime,
			lwi.actual_carriage as actualCarriage
		from lgs_waybill lw, lgs_waybill_info lwi
		where lw.waybill_id = lwi.waybill_id and lw.del_flg = 0 and lwi.del_flg = 0
		<if test="waybillIdList != null and waybillIdList.size() > 0">
			and lw.waybill_id in
			<foreach collection="waybillIdList" item="wit" index="win" open="(" separator="," close=")">
				#{wit}
			</foreach>
		</if>
		order by lw.create_time desc;
	</select>
	
	<update id="updateInvoiceStatus">
		update lgs_waybill
		set update_time = current_timestamp,
		, invoice_status = #{invoiceStatus}
		<if test="invoiceMessage != null and invoiceMessage != ''">
			, invoice_message = #{invoiceMessage}
		</if>
		where del_flg = 0
		and waybill_id in
		<foreach collection="waybillIdList" index="win" item="wit" open="(" separator="," close=")">
			#{wit}
		</foreach>
	</update>

	<update id="updateInvoiceForNotify">
		update lgs_waybill
		set update_time = current_timestamp,
		, invoice_status = #{invoiceStatus}
		<if test="invoiceMessage != null and invoiceMessage != ''">
			, invoice_message = #{invoiceMessage}
		</if>
		where
		del_flg = 0 and parent_id = #{parentId}
	</update>

	<select id="countByPickingBillType" resultType="java.lang.Integer">
		select count(distinct w.waybill_id)
		from lgs_waybill w, lgs_picking_bill p
		where w.picking_bill_id = p.picking_bill_id and w.del_flg = 0 and p.del_flg = 0
		and (w.waybill_id = #{waybillId} or w.parent_id = #{waybillId}) and p.type = #{pickingBillType}
	</select>

	<select id="queryWaybillStatusByNum" resultType="com.ecommerce.logistics.api.dto.waybill.WaybillStatusOption">
		select
		waybill_id as waybillId,
		waybill_num as waybill_num,
		status as status
		from lgs_waybill
		where del_flg = 0
		and waybill_num in
		<foreach collection="waybillNumList" item="wit" index="win" open="(" separator="," close=")">
			#{wit}
		</foreach>
	</select>

	<select id="queryWaybillForCreatedSMS" resultType="com.ecommerce.logistics.biz.message.dto.WaybillCreatedSMSDTO">
		select
			lw.waybill_num as waybillNum,
			lpb.warehouse_id as warehouseId,
			lwi.driver_id as driverId,
			lwi.driver_phone as driverPhone,
			pi.note as productName,
			pm.quantity as quantity,
			lpb.buyer_name as buyerName,
			concat(lpb.province, lpb.city, lpb.district, lpb.street, lpb.address) as receiverAddress
		from lgs_waybill lw
		left join lgs_waybill_info lwi on lw.waybill_id = lwi.waybill_id
		left join lgs_picking_bill lpb on lpb.picking_bill_id = lw.picking_bill_id
		left join lgs_product_quantity_map pm on pm.map_id = lw.waybill_id
		left join lgs_product_info pi on pi.product_info_id = pm.product_info_id
		where lw.del_flg = 0 and lwi.del_flg = 0 and lpb.del_flg = 0 and pm.del_flg = 0 and pi.del_flg = 0
		and lw.waybill_id = #{waybillId}
	</select>

	<select id="selectSeckillByParentIdList" resultType="com.ecommerce.logistics.push.vo.SeckillWaybill">

	select
		a.waybill_id as waybillId,
		a.waybill_num as waybillNum,
		b.province_code as provinceCode,
		b.city_code as cityCode,
		b.quantity as quantity
	from
		(
			select
				lw.waybill_id,
				lw.waybill_num
			from lgs_waybill lw
			where lw.del_flg = 0
			and lw.waybill_id in
				<foreach collection="parentIdList" item="wit" index="win" open="(" separator="," close=")">
					#{wit}
				</foreach>
		) as a,
		(
			SELECT
				w.parent_id,
				p.city_code,
				p.province_code,
				sum(m.quantity)  as quantity
			FROM lgs_waybill w
			LEFT JOIN lgs_picking_bill p ON w.picking_bill_id = p.picking_bill_id
			LEFT JOIN lgs_product_quantity_map m ON m.map_id = w.waybill_id
			WHERE w.del_flg = 0 and p.del_flg = 0
			and w.parent_id in
			<foreach collection="parentIdList" item="pit" index="pin" open="(" separator="," close=")">
				#{pit}
			</foreach>
			GROUP BY
			w.parent_id
		) as b
		where a.waybill_id = b.parent_id

	</select>

	<select id="selectWaybillIdByParentIdList" resultType="java.lang.String">
		select distinct waybill_id
		from lgs_waybill
		where parent_id in
		<foreach collection="parentIdList" item="pit" index="pin" open="(" separator="," close=")">
			#{pit}
		</foreach>
		and del_flg = 0
	</select>

	<select id="selectSimpleByVehicleRelationCond" parameterType="com.ecommerce.logistics.api.dto.realtime.VehicleRelationCondDTO"
					resultType="com.ecommerce.logistics.dao.dto.waybill.WaybillSimpleDO">
		select
			lw.waybill_num as waybillNum,
			dispatch.carrier_id as carrierId,
			picking.buyer_id as buyerId,
			picking.seller_id as sellerId,
			lwi.vehicle_num as vehicleNum
		from lgs_waybill as lw
		left join lgs_waybill_info as lwi on lw.parent_id = lwi.waybill_id
		left join lgs_dispatch_bill as dispatch on dispatch.dispatch_bill_id = lw.dispatch_bill_id
		left join lgs_picking_bill as picking on picking.picking_bill_id = lw.picking_bill_id
		where lw.del_flg = 0 and lwi.del_flg = 0 and lwi.vehicle_num != ''
		<if test="vehicleNumList != null and vehicleNumList.size() > 0">
			and lwi.vehicle_num in
			<foreach collection="vehicleNumList" item="vit" index="vin" open="(" separator="," close=")">
				#{vit}
			</foreach>
		</if>
		<if test="waybillNumLike != null and waybillNumLike != ''">
			and lw.waybill_num like concat('%', #{waybillNumLike}, '%')
		</if>
		<if test="sellerId != null and sellerId != ''">
			and picking.seller_id = #{sellerId}
		</if>
		<if test="sellerIdList != null and sellerIdList.size() > 0">
			and picking.seller_id in
			<foreach collection="sellerIdList" item="sit" index="sin" open="(" separator="," close=")">
				#{sit}
			</foreach>
		</if>
		<if test="buyerIdList != null and buyerIdList.size() > 0">
			and picking.buyer_id in
			<foreach collection="buyerIdList" item="bit" index="bin" open="(" separator="," close=")">
				#{bit}
			</foreach>
		</if>
		<if test="carrierId != null and carrierId != ''">
			and dispatch.carrier_id = #{carrierId}
		</if>
		<if test="carrierIdList != null and carrierIdList.size() > 0">
			and dispatch.carrier_id in
			<foreach collection="carrierIdList" item="cit" index="cin" open="(" separator="," close=")">
				#{cit}
			</foreach>
		</if>
	</select>

	<select id="selectWaybillUserInfo" parameterType="java.lang.String"
			resultType="com.ecommerce.logistics.dao.dto.waybill.WaybillUserInfoDTO">
		select
			w.waybill_id as WaybillId,
			w.waybill_num as WaybillNum,
			w.picking_bill_id as pickingBillId,
			p.delivery_sheet_num as deliverySheetNum,
			i.vehicle_num as vehicleNum,
			i.driver_id as driverId,
			i.driver_name as driverName,
			i.driver_phone as driverPhone,
			w.carrier_id as carrierId,
			p.buyer_id as buyerId,
			p.seller_id as sellerId,
			w.type as waybillType,
		  w.bill_proxy_type as billProxyType,
		  w.can_operate as canOperate
		from
			lgs_waybill w
		left join
			lgs_waybill_info i on i.waybill_id = w.waybill_id
		left join
			lgs_picking_bill p on p.picking_bill_id = w.picking_bill_id
		where
			w.waybill_id = #{waybillId}
		and w.del_flg = 0 and i.del_flg = 0 and p.del_flg = 0
	</select>

	<select id="queryInitWaybillAmtInfo" parameterType="java.lang.String" resultType="com.ecommerce.logistics.dao.dto.waybill.WaybillAmtDO">
		select w.waybill_id					 as waybillId,
		       b.buyer_id            as buyerId,
					 b.buyer_name          as buyerName,
					 b.seller_id           as sellerId,
					 b.seller_name         as sellerName,
					 w.carrier_id          as carrierId,
					 p.price               as goodsPrice,
					 p.carriage_unit_price as receivableCarriagePrice,
					 m.quantity            as planQuantity
		from lgs_waybill as w
					 left join lgs_picking_bill as b on w.picking_bill_id = b.picking_bill_id
					 left join lgs_product_quantity_map as m on w.waybill_id = m.map_id and map_type = 3
					 left join lgs_product_info as p on p.product_info_id = m.product_info_id
		where w.waybill_id = #{waybillId}
		order by w.create_time desc
		limit 1
	</select>

	<select id="selectByDeliverySheetNumAndStatus" resultMap="com.ecommerce.logistics.dao.mapper.WaybillMapper.BaseResultMap">
		select waybill.*
		from lgs_waybill as waybill
		left join lgs_picking_bill as pick on pick.picking_bill_id = waybill.picking_bill_id
		where pick.delivery_sheet_num in
		<foreach collection="deliverySheetNumList" open="(" separator="," close=")" item="dit" index="din">
			#{dit}
		</foreach>
		and waybill.status in
		<foreach collection="statusList" open="(" separator="," close=")" item="sit" index="sin">
			#{sit}
		</foreach>
		and waybill.del_flg = 0 and pick.del_flg = 0
		order by waybill.is_main_waybill desc
	</select>

	<select id="selectCarryWaybill" parameterType="java.lang.String" resultType="com.ecommerce.logistics.api.dto.waybill.CarryWaybillSubmitDTO">
		select
		w.waybill_num as originalDocumentNumber,
		w.waybill_num as shippingNoteNumber,
		1 as vehicleCount,
		1 as transportTypeCode,
		'传统运输' as transportationComb,
		w.create_time as consignmentDateTime,
		'1003997' as businessTypeCode,
		i.leave_warehouse_time as despatchActualDateTime,
		w.complete_time as goodsReceiptDateTime,
		p.seller_id as sellerId,
		p.warehouse_id as warehouseId,
		p.buyer_id as buyerId,
		p.province as goodsReceiptPlaceProvince,
		p.city as goodsReceiptPlaceCity,
		p.district as goodsReceiptPlaceDistrict,
		p.address as goodsReceiptPlaceAddress,
		p.district_code as receiptCountrySubdivisionCode,
		ROUND(i.actual_carriage,3) as totalMonetaryAmount,
		i.vehicle_num as vehicleNumber,
		i.driver_id as ecDriverId,
		pi.note as descriptionOfGoods,
		'0600' as cargoTypeClassificationCode,
		ROUND(m.quantity * 1000,3) as goodsItemGrossWeight,
		w.carrier_id as carrierId,
		i.actual_km as transportDistance,
		0 as pstatus
		from lgs_waybill_info i
		left join lgs_waybill w on i.waybill_id = w.waybill_id
		left join lgs_picking_bill p on p.picking_bill_id = w.picking_bill_id
		left join lgs_product_quantity_map m on m.map_id = i.waybill_id and m.map_type = 3
		left join lgs_product_info pi on pi.product_info_id = m.product_info_id
		where w.del_flg = 0 and w.status = '030070700' and w.waybill_num = #{waybillNum}
	</select>
</mapper>
