<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.WaybillSeckillMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.WaybillSeckill">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="waybill_seckill_id" jdbcType="VARCHAR" property="waybillSeckillId" />
    <result column="waybill_id" jdbcType="VARCHAR" property="waybillId" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="version" jdbcType="BIGINT" property="version" />
  </resultMap>
  
  <delete id="deleteByWaybillId" parameterType="java.lang.String">
  	DELETE FROM lgs_waybill_seckill WHERE waybill_id = #{waybillId}
  </delete>
  
</mapper>