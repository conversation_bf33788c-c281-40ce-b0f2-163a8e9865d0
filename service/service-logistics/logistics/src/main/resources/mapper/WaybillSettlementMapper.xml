<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.logistics.dao.mapper.WaybillSettlementMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.logistics.dao.vo.WaybillSettlement">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="waybill_settlement_id" jdbcType="VARCHAR" property="waybillSettlementId" />
    <result column="waybill_id" jdbcType="VARCHAR" property="waybillId" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="settlement_month" jdbcType="VARCHAR" property="settlementMonth" />
    <result column="settlement_date" jdbcType="VARCHAR" property="settlementDate" />
    <result column="settlement_time" jdbcType="TIMESTAMP" property="settlementTime" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="version" jdbcType="BIGINT" property="version" />
    <result column="carrier_id" jdbcType="VARCHAR" property="carrierId" />
  </resultMap>
  <select id="querySettlementItemList" parameterType="com.ecommerce.logistics.api.dto.settlement.SettlementItemListQueryDTO"
          resultType="com.ecommerce.logistics.dao.dto.settlement.SettlementItemListDO">
    SELECT
      t1.waybill_id as waybillId,
      t3.waybill_num as waybillNum,
      t3.complete_time as completeTime,
      t1.settlement_month as settlementMonth,
      t1.status as status,
      t2.driver_id as driverId,
      t2.driver_name as driverName,
      t2.driver_phone as driverPhone,
      t2.vehicle_num as vehicleNum,
      t2.estimate_km as estimateKm,
      t2.actual_km as actualKm,
      t2.published_carriage as publishedCarriage,
      t2.actual_carriage as actualCarriage,
      t2.leave_warehouse_time as leaveWarehouseTime
    FROM
      lgs_waybill_settlement as t1
    LEFT JOIN
      lgs_waybill_info as t2 on t1.waybill_id=t2.waybill_id
    LEFT JOIN
      lgs_waybill as t3 on t1.waybill_id=t3.waybill_id
    WHERE
      t1.del_flg = 0
      and t1.settlement_month=#{settlementMonth}
      and t1.carrier_id=#{carrierId}
      <if test="settlementStatus != null and settlementStatus != '' ">
        and t1.status=#{settlementStatus}
      </if>
      <if test="waybillNum != null and waybillNum != '' ">
        and t3.waybill_num=#{waybillNum}
      </if>
    ORDER BY
      t3.waybill_num DESC
  </select>
  <update id="waybillCarriageSettlement" parameterType="com.ecommerce.logistics.api.dto.settlement.CarriageSettlementDTO">
    UPDATE
      lgs_waybill_settlement u
    SET
      u.status = 2
    WHERE
      u.del_flg = 0 AND u.settlement_month=#{settlementMonth} AND u.waybill_id in
    <foreach collection="waybillIdList" close=")" index="index" item="waybillId" open="(" separator=",">
      #{waybillId}
    </foreach>
  </update>
  <select id="queryUserSettlementList" parameterType="com.ecommerce.logistics.api.dto.settlement.UserSettlementListQueryDTO"
          resultType="com.ecommerce.logistics.api.dto.settlement.UserSettlementListDTO">
    SELECT
      t1.waybill_id as waybillId,
      t2.waybill_num as waybillNum,
      t2.complete_time as completeTime,
      t3.actual_carriage as carriage,
      t3.estimate_km as mileage
    FROM
      lgs_waybill_settlement as t1
    LEFT JOIN
      lgs_waybill as t2 on t1.waybill_id=t2.waybill_id
    LEFT JOIN
      lgs_waybill_info as t3 on t2.waybill_id=t3.waybill_id
    WHERE
      t1.carrier_id=#{carrierId}
      <if test="settlementStatus != null and settlementStatus > 0 ">
        AND t1.status = #{settlementStatus}
      </if>
    ORDER BY
      t1.create_time DESC
  </select>
  <update id="userCarriageSettlement" parameterType="com.ecommerce.logistics.api.dto.settlement.CarriageSettlementDTO">
    UPDATE
      lgs_waybill_settlement as t1
    SET
      t1.status = 2
    <where>
      t1.status = 1
      <if test="carrierId != null and carrierId != '' ">
        AND t1.carrier_id = #{carrierId}
      </if>
      <if test="settlementMonth != null and settlementMonth != '' ">
        AND t1.settlement_month = #{settlementMonth}
      </if>
    </where>
  </update>
  <select id="queryWaybillSettlementData" resultType="com.ecommerce.logistics.dao.dto.settlement.SettlementUpdateDO">
    SELECT
      sum(t4.actual_quantity) as quantity,
      sum(t2.estimate_km) as estimateKm,
      sum(t2.actual_carriage) as publishedCarriage
    FROM
      lgs_waybill_settlement as t1
    INNER JOIN
      lgs_waybill as t4 on t1.waybill_id=t4.parent_id
    INNER JOIN
      lgs_waybill_info as t2 on t4.parent_id=t2.waybill_id
    INNER JOIN
      lgs_product_quantity_map as t3 on t4.waybill_id=t3.map_id
    WHERE
      t1.del_flg = 0 and t1.status = 1 AND t1.waybill_id IN
    <foreach collection="list" close=")" index="index" item="waybillId" open="(" separator=",">
      #{waybillId}
    </foreach>
  </select>
</mapper>