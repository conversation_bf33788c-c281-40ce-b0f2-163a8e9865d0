
services:
  service-member:
    image: ecommerce-service-member:1.0
    volumes:
      - /remote/logs/member:/var/log
      - /remote/skywalking:/home/<USER>
      - /remote/config/auth:/usr/local/auth
    deploy:
      resources:
        limits:
          memory: 800m
    container_name: member
    restart: always
    ports:
      - "9001:9001"
    environment:
      - SPRING_CLOUD_CONFIG_URI=http://10.201.188.5:8888
      - SPRING_CLOUD_CONFIG_LABEL=asset-2025
      - SPRING_CLOUD_CONFIG_PROFILE=dev
      - TZ=Asia/Shanghai
      - JAVA_TOOL_OPTIONS=-Xms256m -Xmx512m -XX:+UseSerialGC
    depends_on:
      - config
      - eureka