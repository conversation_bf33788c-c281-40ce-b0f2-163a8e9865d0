<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>service-member</artifactId>
        <groupId>com.ecommerce</groupId>
        <version>2.1.4-RELEASE</version>
    </parent>
    <artifactId>member</artifactId>
    <packaging>jar</packaging>
    <name>member</name>
    <description>Member Service</description>

    <properties>
        <docker.deploy.version>${project.version}</docker.deploy.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-config</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-eureka-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bus-amqp</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-aspects</artifactId>
        </dependency>

        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjweaver</artifactId>
        </dependency>
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-tracing-bridge-brave</artifactId>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>

        <!--session同步-->
        <dependency>
            <groupId>org.springframework.session</groupId>
            <artifactId>spring-session-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>jakarta.annotation</groupId>
            <artifactId>jakarta.annotation-api</artifactId>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>com.ecommerce/groupId>-->
        <!--            <artifactId>kafka-spring-boot-starter</artifactId>-->
        <!--        </dependency>-->

        <dependency>
            <groupId>com.ecommerce</groupId>
            <artifactId>rabbitmq-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ecommerce</groupId>
            <artifactId>member-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ecommerce</groupId>
            <artifactId>service-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ecommerce</groupId>
            <artifactId>base-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ecommerce</groupId>
            <artifactId>open-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ecommerce</groupId>
            <artifactId>pay-api</artifactId>
        </dependency>
        <dependency>
            <artifactId>order-api</artifactId>
            <groupId>com.ecommerce</groupId>
        </dependency>
        <dependency>
            <groupId>com.ecommerce</groupId>
            <artifactId>goods-api</artifactId>
        </dependency>


        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
        </dependency>

        <!-- 图片验证码 -->
        <!-- https://mvnrepository.com/artifact/com.cuisongliu/kaptcha-spring-boot-autoconfigure -->
        <dependency>
            <groupId>com.cuisongliu</groupId>
            <artifactId>kaptcha-spring-boot-autoconfigure</artifactId>
        </dependency>
        <!--		<dependency>-->
        <!--			<groupId>org.springframework.data</groupId>-->
        <!--			<artifactId>spring-data-redis</artifactId>-->
        <!--		</dependency>-->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

    </dependencies>

    <build>
        <finalName>member</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.14.0</version>
                <configuration>
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                    <release>${java.version}</release>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
