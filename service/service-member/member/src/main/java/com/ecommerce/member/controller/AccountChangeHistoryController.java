package com.ecommerce.member.controller;

import com.ecommerce.member.api.dto.account.AccountChangeHistoryDTO;
import com.ecommerce.member.api.dto.account.PageAccountChangeHistoryDTO;
import com.ecommerce.member.service.IAccountChangeHistoryService;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


import java.util.List;


/**
 * @Created锛�Fri Jun 28 17:13:06 CST 2019
 * <AUTHOR>
 * @Version:2
 * @Description:null
*/

@RestController
@Tag(name = "AccountChangeHistory", description = "null")
@RequestMapping("/accountChangeHistory")
@RequiredArgsConstructor
public class AccountChangeHistoryController {

   private final IAccountChangeHistoryService iAccountChangeHistoryService;

   @Operation(summary = "新增员工授权，启用，禁用历史")
   @PostMapping(value="/add")
   public void add(@RequestBody AccountChangeHistoryDTO accountChangeHistoryDTO,@RequestParam String operator){
      iAccountChangeHistoryService.add(accountChangeHistoryDTO,operator);
   }

   @Operation(summary = "修改员工授权，启用，禁用历史")
   @PostMapping(value="/update")
   public void update(@RequestBody AccountChangeHistoryDTO accountChangeHistoryDTO,@RequestParam String operator){
      iAccountChangeHistoryService.update(accountChangeHistoryDTO,operator);
   }

   @Operation(summary = "根据DTO查询员工授权，启用，禁用历史")
   @PostMapping(value="/findByQuery")
   public List<AccountChangeHistoryDTO> findByQuery(@RequestBody AccountChangeHistoryDTO accountChangeHistoryDTO){
      return iAccountChangeHistoryService.findByQuery(accountChangeHistoryDTO);
   }


   @Operation(summary = "根据DTO分页查询员工授权，启用，禁用历史")
   @PostMapping(value="/pageHistoryInfoList")
   public PageInfo<AccountChangeHistoryDTO> pageHistoryInfoList(@RequestBody PageAccountChangeHistoryDTO pageAccountChangeHistoryDTO){
      return iAccountChangeHistoryService.pageHistoryInfoList(pageAccountChangeHistoryDTO);
   }



}
