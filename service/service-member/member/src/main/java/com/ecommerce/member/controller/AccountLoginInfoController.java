package com.ecommerce.member.controller;

import com.ecommerce.member.api.dto.accountLoginInfo.AccountLoginInfoDTO;
import com.ecommerce.member.api.dto.accountLoginInfo.AccountLoginInfoQueryDTO;
import com.ecommerce.member.exception.DuplicateString;
import com.ecommerce.member.service.IAccountLoginInfoService;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import lombok.RequiredArgsConstructor;


import java.util.List;


/**
 * @Created锛�Thu Nov 08 19:33:46 CST 2018
 * <AUTHOR>
 * @Version:2
 * @Description:null
*/

@RestController
@Tag(name = "AccountLoginInfo", description = "登陆日志查询接口")
@RequestMapping("/accountLoginInfo")
@RequiredArgsConstructor
public class AccountLoginInfoController {

   private final IAccountLoginInfoService iAccountLoginInfoService;

   @Operation(summary = "根据id查询单条记录")
   @PostMapping(value="/findById")
   public AccountLoginInfoDTO findById(@RequestParam String id){
      return iAccountLoginInfoService.findById(id);
   }

   @Operation(summary = "根据条件查询")
   @PostMapping(value="/findAll")
   public PageInfo<AccountLoginInfoDTO> findAll(@RequestBody AccountLoginInfoQueryDTO query){
      return iAccountLoginInfoService.findAll(query);
   }

   @Operation(summary = "根据会话id查询")
   @PostMapping(value="/findBySessionId")
   public List<AccountLoginInfoDTO> findBySessionId(@RequestParam String sessionId){
      return iAccountLoginInfoService.findBySessionId(sessionId);
   }

   @Operation(summary = "根据账户id查询")
   @PostMapping(value="/findByAccountId")
   public AccountLoginInfoDTO findByAccountId(@RequestParam(DuplicateString.ACCOUNT_ID) String accountId){
      return iAccountLoginInfoService.findByAccountId(accountId);
   }

   @Operation(summary = "强制下线时更新登录日志")
   @PostMapping(value="/forceOffline")
   public void forceOffline(@RequestParam(value = DuplicateString.ACCOUNT_ID) String accountId,@RequestParam String reason,@RequestParam(required = false)  String operator){
      iAccountLoginInfoService.forceOffline(accountId,reason,operator);
   }

   @Operation(summary = "下线时更新登录日志")
   @PostMapping(value="/offline")
   public void offline(@RequestParam String sessionId,@RequestParam String reason,@RequestParam(required = false)  String operator){
      iAccountLoginInfoService.offline(sessionId,reason,operator);
   }

}
