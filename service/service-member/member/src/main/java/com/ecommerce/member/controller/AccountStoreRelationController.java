package com.ecommerce.member.controller;

import com.ecommerce.member.api.dto.storeAccountRelation.AccountStoreRelationCreateDTO;
import com.ecommerce.member.api.dto.storeAccountRelation.AccountStoreRelationDTO;
import com.ecommerce.member.api.dto.storeAccountRelation.AccountStoreRelationQueryDTO;
import com.ecommerce.member.api.dto.storeAccountRelation.AccountStoreRelationUpdateDTO;
import com.ecommerce.member.exception.DuplicateString;
import com.ecommerce.member.service.IAccountStoreRelationService;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 仓库管理员维护服务
 *
 * <AUTHOR>
*/

@RestController
@Tag(name = "AccountStoreRelationController", description = "仓库管理员维护服务")
@RequestMapping("/accountStoreRelation")
@RequiredArgsConstructor
public class AccountStoreRelationController {

   private final IAccountStoreRelationService accountStoreRelationService;

   @Operation(summary = "更新关系")
   @PostMapping(value="/update")
   public void update(@RequestBody AccountStoreRelationUpdateDTO storeAccountRelationUpdateDTO){
      accountStoreRelationService.update(storeAccountRelationUpdateDTO);

   }


   @Operation(summary = "天加关系")
   @PostMapping(value="/create")
   public String create(@RequestBody AccountStoreRelationCreateDTO storeAccountRelationCreateDTO){
      return accountStoreRelationService.create(storeAccountRelationCreateDTO);
   }


   @Operation(summary = "根据会员id和区域adcode查询")
   @PostMapping(value="/findByMemberIdAndRegion")
   public List<AccountStoreRelationDTO> findByMemberIdAndRegion(@RequestParam(DuplicateString.MEMBER_ID) String memberId, @RequestParam String adCode){
      return accountStoreRelationService.findByMemberIdAndRegion(memberId,adCode);
   }


   @Operation(summary = "根据id删除多个")
   @PostMapping(value="/deleteByIds")
   public void deleteByIds(@RequestBody List<String> ids,@RequestParam String operator){
      accountStoreRelationService.deleteByIds(ids,operator);

   }


   @Operation(summary = "删除关系")
   @PostMapping(value="/deleteById")
   public void deleteById(@RequestParam String id,@RequestParam String operator){
      accountStoreRelationService.deleteById(id,operator);

   }


   @Operation(summary = "根据账户id查询")
   @PostMapping(value="/findByAccountId")
   public List<AccountStoreRelationDTO> findByAccountId(@RequestParam(DuplicateString.ACCOUNT_ID) String accountId){
      return accountStoreRelationService.findByAccountId(accountId);
   }


   @Operation(summary = "查找单个")
   @PostMapping(value="/findById")
   public AccountStoreRelationDTO findById(@RequestParam String id){
      return accountStoreRelationService.findById(id);
   }


   @Operation(summary = "根据条件翻页查询")
   @PostMapping(value="/findAll")
   public PageInfo<AccountStoreRelationDTO> findAll(@RequestBody AccountStoreRelationQueryDTO query){
      return accountStoreRelationService.findAll(query);
   }


   @Operation(summary = "根据会员id查询")
   @PostMapping(value="/findByMemberId")
   public List<AccountStoreRelationDTO> findByMemberId(@RequestParam(DuplicateString.MEMBER_ID) String memberId){
      return accountStoreRelationService.findByMemberId(memberId);
   }


   @Operation(summary = "根据区域adcode查询，（eq 省 or eq市 or eq区)")
   @PostMapping(value="/findByRegion")
   public List<AccountStoreRelationDTO> findByRegion(@RequestParam String adCode){
      return accountStoreRelationService.findByRegion(adCode);
   }



}
