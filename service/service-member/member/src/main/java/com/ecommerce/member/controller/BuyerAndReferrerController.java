package com.ecommerce.member.controller;

import com.ecommerce.member.api.dto.referrer.*;
import com.ecommerce.member.service.IBuyerAndReferrerService;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * null
 *
 * <AUTHOR>
*/

@RestController
@Tag(name = "BuyerAndReferrer", description = "推荐人&账号关系服务(促销使用)")
@RequestMapping("/buyerAndReferrer")
@RequiredArgsConstructor
public class BuyerAndReferrerController {

   private final IBuyerAndReferrerService iBuyerAndReferrerService;

   @Operation(summary = "根据条件查询推荐人")
   @PostMapping(value="/findAllReferrerInfo")
   public List<ReferrerInfoDTO> findAllReferrerInfo(){
      return iBuyerAndReferrerService.findAllReferrerInfo();
   }


   @Operation(summary = "根据条件查询推荐人")
   @PostMapping(value="/findAllReferrerInfoByCondition")
   public PageInfo<ReferrerInfoDTO> findAllReferrerInfoByCondition(@RequestBody ReferrerInfoQueryDTO referrerInfoQueryDTO){
      return iBuyerAndReferrerService.findAllReferrerInfoByCondition(referrerInfoQueryDTO);
   }


   @Operation(summary = "根据推下单人账户id查询推荐人(下单时使用)")
   @PostMapping(value="/findByBuyerAccountId")
   public ReferrerBuyerRelationDTO findByBuyerAccountId(@RequestParam String buyerAccountId){
      return iBuyerAndReferrerService.findByBuyerAccountId(buyerAccountId);
   }


   @Operation(summary = "根据条件查询关系信息")
   @PostMapping(value="/findAllRelation")
   public PageInfo<ReferrerBuyerRelationDTO> findAllRelation(@RequestBody RelationQueryDTO relationQueryDTO){
      return iBuyerAndReferrerService.findAllRelation(relationQueryDTO);
   }


   @Operation(summary = "根据推荐人信息表id查询推荐人（物流使用）")
   @PostMapping(value="/findById")
   public ReferrerInfoDTO findById(@RequestParam String id){
      return iBuyerAndReferrerService.findById(id);
   }


   @Operation(summary = "null")
   @PostMapping(value="/findByIds")
   public List<ReferrerInfoDTO> findByIds(@RequestBody List<String> ids){
      return iBuyerAndReferrerService.findByIds(ids);
   }


   @Operation(summary = "扫描二维码创建买家与推荐人的关系，已创建关系的则覆盖更新")
   @PostMapping(value="/createRelation")
   public void createRelation(@RequestBody CreateRelationDTO createRelationDTO){
      iBuyerAndReferrerService.createRelation(createRelationDTO);

   }



}
