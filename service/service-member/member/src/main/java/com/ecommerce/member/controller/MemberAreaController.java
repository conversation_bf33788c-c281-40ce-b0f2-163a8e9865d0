package com.ecommerce.member.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.member.api.dto.member.MemberTypeDTO;
import com.ecommerce.member.api.dto.servicearea.*;
import com.ecommerce.member.exception.DuplicateString;
import com.ecommerce.member.service.IMemberServiceAreaService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;



@RestController
@Tag(name = "MemberArea", description = "会员服务范围")
@RequestMapping("/memberArea")
@RequiredArgsConstructor
public class MemberAreaController {

   private final IMemberServiceAreaService iMemberServiceAreaService;

   @Operation(summary = "查询社会运力承运商或个人司机的会员ID")
   @PostMapping(value="/findSociologyPersonIdOrCarrierIdByRegionCode")
   public List<String> findSociologyPersonIdOrCarrierIdByRegionCode(@RequestBody MemberServiceAreaItemDTO serviceAreaItemDTO){
      return iMemberServiceAreaService.findSociologyPersonIdOrCarrierIdByRegionCode(serviceAreaItemDTO);
   }

   @Operation(summary = "删除服务范围")
   @PostMapping(value="/deleteServiceArea")
   public void deleteServiceArea(@RequestParam(DuplicateString.MEMBER_ID) String memberId, @RequestParam String serviceAreaId, @RequestParam String operatorId){
      iMemberServiceAreaService.deleteServiceArea(memberId,serviceAreaId,operatorId);

   }


   @Operation(summary = "更新服务范围")
   @PostMapping(value="/updateServiceArea")
   public void updateServiceArea(@RequestBody MemberServiceAreaQueryDTO memberServiceAreaQueryDTO, @RequestParam String operatorId){
      iMemberServiceAreaService.updateServiceArea(memberServiceAreaQueryDTO,operatorId);

   }


   @Operation(summary = "根据地址编号查找会员")
   @PostMapping(value="/findMemberByServiceArea")
   public List<MemberTypeDTO> findMemberByServiceArea(@RequestBody MemberServiceAreaTypeQueryDTO memberServiceAreaItemQueryDTO){
      return iMemberServiceAreaService.findMemberByServiceArea(memberServiceAreaItemQueryDTO);
   }


   @Operation(summary = "查找一个会员的所有的服务范围")
   @PostMapping(value="/findListByMemberId")
   public MemberServiceAreaDTO findListByMemberId(@RequestParam(DuplicateString.MEMBER_ID) String memberId){
      return iMemberServiceAreaService.findListByMemberId(memberId);
   }


   @Operation(summary = "查找一个会员的所有的服务范围")
   @PostMapping(value="/findTreeByMemberId")
   public MemberServiceAreaResultDTO findTreeByMemberId(@RequestParam(DuplicateString.MEMBER_ID) String memberId){
      return iMemberServiceAreaService.findTreeByMemberId(memberId);
   }


   @Operation(summary = "查找一个会员的所有的服务范围")
   @PostMapping(value="/findTreeListByMemberId")
   public MemberServiceAreaResultDTO findTreeListByMemberId(@RequestParam(DuplicateString.MEMBER_ID) String memberId){
      return iMemberServiceAreaService.findTreeListByMemberId(memberId);
   }


   @Operation(summary = "增加服务范围")
   @PostMapping(value="/addServiceArea")
   public void addServiceArea(@RequestBody MemberServiceAreaQueryDTO memberServiceAreaQueryDTO, @RequestParam String operatorId){
      iMemberServiceAreaService.addServiceArea(memberServiceAreaQueryDTO,operatorId);

   }

   @Operation(summary = "批量删除服务范围")
   @PostMapping(value="/batchDeleteServiceArea")
   public void batchDeleteServiceArea(@RequestBody MemberServiceAreaBatchDeleteDTO dto, @RequestParam String operatorId){
      iMemberServiceAreaService.batchDeleteServiceArea(dto, operatorId);

   }

}
