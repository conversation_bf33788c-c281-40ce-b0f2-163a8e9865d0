package com.ecommerce.member.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.member.api.dto.member.config.*;
import com.ecommerce.member.exception.DuplicateString;
import com.ecommerce.member.service.IMemberConfigService;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;


/**
 * 企业配置参数
 *
 * <AUTHOR>
*/

@RestController
@Tag(name = "MemberConfig", description = ":企业配置参数")
@RequestMapping("/memberConfig")
@RequiredArgsConstructor
public class MemberConfigController {

   private final IMemberConfigService iMemberConfigService;

   @Operation(summary = "禁用配置参数")
   @PostMapping(value="/disable")
   public void disable(@RequestParam String arg0,@RequestParam String arg1){
      iMemberConfigService.disable(arg0,arg1);

   }


   @Operation(summary = "启用配置参数")
   @PostMapping(value="/enable")
   public void enable(@RequestParam String arg0,@RequestParam String arg1){
      iMemberConfigService.enable(arg0,arg1);

   }


   @Operation(summary = "更新配置参数")
   @PostMapping(value="/update")
   public void update(@RequestBody MemberConfigUpdateDTO arg0){
      iMemberConfigService.update(arg0);

   }


   @Operation(summary = "删除配置参数")
   @PostMapping(value="/delete")
   public void delete(@RequestParam String arg0,@RequestParam String arg1){
      iMemberConfigService.delete(arg0,arg1);

   }


   @Operation(summary = "添加配置参数")
   @PostMapping(value="/create")
   public String create(@RequestBody MemberConfigCreateDTO arg0){
      return iMemberConfigService.create(arg0);
   }


   @Operation(summary = "翻页查询所有配置参数")
   @PostMapping(value="/findAll")
   public PageInfo<MemberConfigDTO> findAll(@RequestBody MemberConfigQueryDTO arg0){
      return iMemberConfigService.findAll(arg0);
   }


   @Operation(summary = "验证键是否存在")
   @PostMapping(value="/checkKeyCode")
   public Boolean checkKeyCode(@RequestParam String arg0,@RequestParam String arg1,@RequestParam String arg2){
      return iMemberConfigService.checkKeyCode(arg0,arg1,arg2);
   }


   @Operation(summary = "根据id查找单个配置参数")
   @PostMapping(value="/findById")
   public MemberConfigDTO findById(@RequestParam String arg0){
      return iMemberConfigService.findById(arg0);
   }


   @Operation(summary = "根据会员id和键（键的匹配查找）查询配置")
   @PostMapping(value="/findByMemberIdAndKeyCodeLike")
   public List<MemberConfigDTO> findByMemberIdAndKeyCodeLike(@RequestParam String arg0, @RequestParam String arg1){
      return iMemberConfigService.findByMemberIdAndKeyCodeLike(arg0,arg1);
   }

   @Operation(summary = "根据会员id和键查询")
   @PostMapping(value="/findByMemberIdAndKeyCodes")
   public List<MemberConfigDTO> findByMemberIdAndKeyCodes(@RequestParam(DuplicateString.MEMBER_ID) String memberId, @RequestBody Collection<String> keyCodes){
      return iMemberConfigService.findByMemberIdAndKeyCodes(memberId,keyCodes);
   }

   @Operation(summary = "根据会员id和键批量查询")
   @PostMapping(value="/findByMemberIdsAndKeyCodes")
   public List<MemberConfigDTO> findByMemberIdsAndKeyCodes(@RequestBody MemberIdsAndKeyCodesDTO dto){
      return iMemberConfigService.findByMemberIdsAndKeyCodes(dto.getMemberIds(),dto.getKeyCode());
   }

   @Operation(summary = "根据会员id和键查询配置")
   @PostMapping(value="/findByMemberIdAndKeyCode")
   public MemberConfigDTO findByMemberIdAndKeyCode(@RequestParam String arg0,@RequestParam String arg1){
      return iMemberConfigService.findByMemberIdAndKeyCode(arg0,arg1);
   }

   @Operation(summary = "根据会员id查询配置")
   @PostMapping(value="/findByMemberId")
   public List<MemberConfigDTO> findByMemberId(@RequestParam(DuplicateString.MEMBER_ID) String memberId) {
      return iMemberConfigService.findByMemberId(memberId);
   }

   @Operation(summary = "根据会员id和分组查询配置")
   @PostMapping(value="/findByMemberIdAndGroup")
   public List<MemberConfigDTO> findByMemberIdAndGroup(@RequestParam(DuplicateString.MEMBER_ID) String memberId, @RequestParam String group) {
      return iMemberConfigService.findByMemberIdAndGroup(memberId, group);
   }

   @Operation(summary = "是否有ERP订单接口")
    @PostMapping(value="/hasErpOrder")
    public Boolean hasErpOrder(@RequestParam(DuplicateString.MEMBER_ID) String memberId){
        return iMemberConfigService.hasErpOrder(memberId);
    }

   @Operation(summary = "是否有接入ERP接口（keyCode对应的值为1或者为true时返回true）")
   @PostMapping(value="/hasErpFlg")
   public Boolean hasErpFlg(@RequestParam(DuplicateString.MEMBER_ID) String memberId,@RequestParam String keyCode){
      return iMemberConfigService.hasErpFlg(memberId,keyCode);
   }

   @Operation(summary = "是否能自动完成委托单")
   @PostMapping(value = "/canAutoCompleteDeliveryBill")
   public Boolean canAutoCompleteDeliveryBill(@RequestParam(DuplicateString.MEMBER_ID) String memberId,
                                              @RequestParam BigDecimal actualQuantity,
                                              @RequestParam BigDecimal planQuantity) {
      return iMemberConfigService.canAutoCompleteDeliveryBill(memberId, actualQuantity, planQuantity);
   }
}
