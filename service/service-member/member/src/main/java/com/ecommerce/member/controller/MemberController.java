package com.ecommerce.member.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.common.service.common.BizRedisService;
import com.ecommerce.member.api.dto.AttachmentDTO;
import com.ecommerce.member.api.dto.account.DriverAccountDTO;
import com.ecommerce.member.api.dto.base.PageQuery;
import com.ecommerce.member.api.dto.carrier.CarrierIdentityDTO;
import com.ecommerce.member.api.dto.exception.MemberBizException;
import com.ecommerce.member.api.dto.member.ApproveRequestDTO;
import com.ecommerce.member.api.dto.member.CancelRequestDTO;
import com.ecommerce.member.api.dto.member.CertQueryDTO;
import com.ecommerce.member.api.dto.member.CollectionCarrierQueryDTO;
import com.ecommerce.member.api.dto.member.MemberApprovalRequestDTO;
import com.ecommerce.member.api.dto.member.MemberApprovalRequestQueryDTO;
import com.ecommerce.member.api.dto.member.MemberApproveRequestDTO;
import com.ecommerce.member.api.dto.member.MemberBaseInfoDTO;
import com.ecommerce.member.api.dto.member.MemberBusinessInfoDTO;
import com.ecommerce.member.api.dto.member.MemberBusinessRequestDetailDTO;
import com.ecommerce.member.api.dto.member.MemberCarrierDTO;
import com.ecommerce.member.api.dto.member.MemberCertDTO;
import com.ecommerce.member.api.dto.member.MemberCertNewDTO;
import com.ecommerce.member.api.dto.member.MemberCertRequestDetailDTO;
import com.ecommerce.member.api.dto.member.MemberCertViewDTO;
import com.ecommerce.member.api.dto.member.MemberDTO;
import com.ecommerce.member.api.dto.member.MemberDetailDTO;
import com.ecommerce.member.api.dto.member.MemberIdAndNameDTO;
import com.ecommerce.member.api.dto.member.MemberListViewDTO;
import com.ecommerce.member.api.dto.member.MemberNameLikeQueryDTO;
import com.ecommerce.member.api.dto.member.MemberQueryByKeyDTO;
import com.ecommerce.member.api.dto.member.MemberQueryDTO;
import com.ecommerce.member.api.dto.member.MemberRegisterByAppDTO;
import com.ecommerce.member.api.dto.member.MemberRequestDTO;
import com.ecommerce.member.api.dto.member.MemberSearchDTO;
import com.ecommerce.member.api.dto.member.MemberSearchQueryDTO;
import com.ecommerce.member.api.dto.member.MemberSimpleDTO;
import com.ecommerce.member.api.dto.member.MemberStatusDTO;
import com.ecommerce.member.api.dto.member.MemberSuperSimpleDTO;
import com.ecommerce.member.api.dto.member.MemberTypeDTO;
import com.ecommerce.member.api.dto.member.MemberUpdateTypeDTO;
import com.ecommerce.member.api.dto.member.RejectRequestDTO;
import com.ecommerce.member.api.dto.member.SearchCarrierDTO;
import com.ecommerce.member.api.dto.member.SubmitCertDTO;
import com.ecommerce.member.api.dto.member.SubmitCertNoApprovalDTO;
import com.ecommerce.member.api.dto.member.UpdateCertDTO;
import com.ecommerce.member.api.dto.member.UpdateCertNoApprovalDTO;
import com.ecommerce.member.api.dto.member.enums.ApproveRequestTypeEnum;
import com.ecommerce.member.api.dto.member.enums.MemberPlatform;
import com.ecommerce.member.api.dto.member.param.MemberCertParam;
import com.ecommerce.member.api.redis.MemberRedisKeys;
import com.ecommerce.member.biz.IAccountBiz;
import com.ecommerce.member.biz.IMemberBiz;
import com.ecommerce.member.biz.IMemberBlacklistBiz;
import com.ecommerce.member.biz.IMemberRelationBiz;
import com.ecommerce.member.biz.impl.MemberApproveRequestIncrementIdGenerator;
import com.ecommerce.member.biz.impl.MemberCodeIncrementIdGenerator;
import com.ecommerce.member.dao.vo.Account;
import com.ecommerce.member.dao.vo.Member;
import com.ecommerce.member.dao.vo.MemberBlacklist;
import com.ecommerce.member.dao.vo.MemberRelation;
import com.ecommerce.member.exception.DuplicateString;
import com.ecommerce.member.service.IMemberService;
import com.ecommerce.member.service.handler.MemberCertHandler;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import tk.mybatis.mapper.entity.Condition;

import jakarta.validation.Valid;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 会员对外服务接口
 *
 * <AUTHOR>
*/

@RestController
@Tag(name = "Member", description = "会员对外服务接口")
@RequestMapping("/member")
@RequiredArgsConstructor
public class MemberController {


   private final IMemberService iMemberService;

   private final MemberCertHandler memberCertHandler;

   private final MemberCodeIncrementIdGenerator memberCodeIncrementIdGenerator;

   private final IMemberBiz memberBiz;

   private final IAccountBiz accountBiz;

   private final BizRedisService bizRedisService;

   private final MemberApproveRequestIncrementIdGenerator memberApproveRequestIncrementIdGenerator;

   private final IMemberBlacklistBiz memberBlacklistBiz;

   private final IMemberRelationBiz memberRelationBiz;

   //临时使用一下
   @Operation(summary = "重新生成memberCode(仅测试使用)")
   @PostMapping(value="/updateMemberCode")
   public void updateMemberCode(@RequestParam(value = DuplicateString.MEMBER_ID,required = false) String memberId,
                                @RequestParam(value = DuplicateString.MEMBER_CODE,required = false) String memberCode,
                                @RequestParam(required = false) Member member) {

       if (CsStringUtils.isNotBlank(memberId) && member == null) {
         member = memberBiz.get(memberId);
      }
       if (CsStringUtils.isBlank(memberCode) || memberCode.length() != 6) {
         throw new MemberBizException(BasicCode.INVALID_PARAM,":为空了或长度不等于6");
      }
      if( member == null ){
         throw new MemberBizException(BasicCode.INVALID_PARAM,":member没有找到");
      }
      //重新设置会员代码
      member.setMemberCode(memberCode);
      memberBiz.updateSelective(member);


      //更新会员下的账号信息
      Condition c2 = new Condition(Account.class);
      c2.createCriteria().andEqualTo(DuplicateString.MEMBER_ID, member.getMemberId());

      List<Account> accountList = accountBiz.findByCondition(c2);
      String key = MemberRedisKeys.MEMBER_INCR_CODE_SUBACCOUNT + memberCode;
      bizRedisService.del(key);
      accountList.forEach(item -> {
         //获取账户代码
         long code = bizRedisService.incr(key, 1);
         String formattedCode;
         if (code > 99) {
            formattedCode = String.valueOf(code);
         } else if (code > 9) {
            formattedCode = "0" + code;
         } else {
            formattedCode = "00" + code;
         }
         item.setAccountCode(memberCode + "-" + formattedCode);
         item.setMemberCode(memberCode);
         accountBiz.updateSelective(item);
      });
      bizRedisService.del(key);

      //更新会员关系
      MemberRelation mr = new MemberRelation();
      mr.setMemberId(member.getMemberId());
      List<MemberRelation> mrList = memberRelationBiz.find(mr);
      if(mrList != null && !mrList.isEmpty() ){
         mrList.forEach(item ->{
            item.setMemberCode(memberCode);
            memberRelationBiz.updateSelective(item);
         });
      }
      MemberRelation mr2 = new MemberRelation();
      mr2.setCustomerId(member.getMemberId());
      List<MemberRelation> mrList2 = memberRelationBiz.find(mr2);
      if(mrList2 != null && !mrList2.isEmpty() ){
         mrList2.forEach(item ->{
            item.setMemberCode(memberCode);
            memberRelationBiz.updateSelective(item);
         });
      }
      //更新黑名单
      MemberBlacklist mbl = new MemberBlacklist();
      mbl.setMemberId(member.getMemberId());
      List<MemberBlacklist>  mblList = memberBlacklistBiz.find(mbl);
      mblList.forEach(item ->{
         item.setMemberCode(memberCode);
         memberBlacklistBiz.updateSelective(item);
      });
      MemberBlacklist mbl2 = new MemberBlacklist();
      mbl2.setBlackMemberId(member.getMemberId());
      List<MemberBlacklist>  mblList2 = memberBlacklistBiz.find(mbl2);
      mblList2.forEach(item ->{
         item.setBlackMemberCode(memberCode);
         memberBlacklistBiz.updateSelective(item);
      });
   }

   @Operation(summary = "重新生成memberCode(仅测试使用)")
   @PostMapping(value="/updateAllMemberCode")
   public void updateAllMemberCode() {
      new Thread(()->{
         bizRedisService.del(MemberRedisKeys.MEMBER_INCR_CODE_MEMBER);

         Member admin = memberBiz.findById(MemberPlatform.ADMIN_MEMBERID.getId());
         Member platform = memberBiz.findById(MemberPlatform.PLATFORM_MEMBERID.getId());
         setMemberCode(Lists.newArrayList(admin,platform));

         //--卖家会员
         Condition c = new Condition(Member.class);
         c.createCriteria().andEqualTo("sellerFlg",1)
                 .andNotEqualTo(DuplicateString.MEMBER_ID,MemberPlatform.ADMIN_MEMBERID.getId())
                 .andNotEqualTo(DuplicateString.MEMBER_ID,MemberPlatform.PLATFORM_MEMBERID.getId());
         c.orderBy(DuplicateString.CREATE_TIME).asc();
         PageInfo<Member> p = new PageInfo<>();
         p.setPageSize(1000);
         p.setPageNum(1);
         Page<Member> memberPage = memberBiz.page(c, p);
         if (memberPage != null && !memberPage.isEmpty()) {
            setMemberCode(memberPage);
         }

         //--其它会员
         c = new Condition(Member.class);
         c.createCriteria().andNotEqualTo("sellerFlg",1)
                 .andNotEqualTo(DuplicateString.MEMBER_ID,MemberPlatform.ADMIN_MEMBERID.getId())
                 .andNotEqualTo(DuplicateString.MEMBER_ID,MemberPlatform.PLATFORM_MEMBERID.getId());
         c.orderBy(DuplicateString.CREATE_TIME).asc();
         p = new PageInfo<>();
         p.setPageSize(3000);
         p.setPageNum(1);
         memberPage = null;
         memberPage = memberBiz.page(c, p);
         if (memberPage != null && !memberPage.isEmpty()) {
            setMemberCode(memberPage);
         }
      }).start();
   }

   private void setMemberCode(List<Member> memberPage){
      for (Member member : memberPage) {
         String memberCode = memberCodeIncrementIdGenerator.incrementCode();
         updateMemberCode(null,memberCode,member);
      }
   }

   @Operation(summary = "资质过期任务测试")
   @PostMapping(value="/memberCertHandlerExecute")
   public void memberCertHandlerExecute() {
      memberCertHandler.execute();
   }

   @Operation(summary = "获取会员请求详情")
   @PostMapping(value="/getMemberApprovalDetails")
   public MemberDTO getMemberApprovalDetails(@RequestParam String memberApprovalRequestId){
      return iMemberService.getMemberApprovalDetails(memberApprovalRequestId);
   }


   @Operation(summary = "获取一条变更记录")
   @PostMapping(value="/findMemberApprovalRequest")
   public MemberApprovalRequestDTO findMemberApprovalRequest(@RequestParam String requestId){
      return iMemberService.findMemberApprovalRequest(requestId);
   }


   @Operation(summary = "企业注册（买家）")
   @PostMapping(value="/registerBuyerNoApproval")
   public String registerBuyerNoApproval(@RequestBody MemberRequestDTO memberRequestDTO){
      return iMemberService.registerBuyerNoApproval(memberRequestDTO);
   }


   @Operation(summary = "个人会员升级到买家企业会员")
   @PostMapping(value="/updateMemberToEnterpriseBuyer")
   public String updateMemberToEnterpriseBuyer(@RequestBody MemberUpdateTypeDTO memberUpdateTypeDTO){
      return iMemberService.updateMemberToEnterpriseBuyer(memberUpdateTypeDTO);

   }


   @Operation(summary = "根据条件查询会员ID")
   @PostMapping(value="/findMemberIdByQuery")
   public List<String> findMemberIdByQuery(@RequestBody MemberQueryDTO query){
      return iMemberService.findMemberIdByQuery(query);

   }


   @Operation(summary = "分页查询会员")
   @PostMapping(value="/pageMemberListView")
   public PageInfo<MemberListViewDTO> pageMemberListView(@RequestBody MemberQueryDTO query,@RequestParam Integer pageNum,@RequestParam Integer pageSize){
      return iMemberService.pageMemberListView(query,pageNum,pageSize);
   }


   @Operation(summary = "保存经营信息 *草稿*")
   @PostMapping(value="/saveBusinessInfoDraft")
   public String saveBusinessInfoDraft(@RequestBody MemberRegisterByAppDTO memberRequestDTO){
      MemberRequestDTO requestDTO = appDTO2RequestDTO(memberRequestDTO);
      return iMemberService.saveBusinessInfoDraft(requestDTO);
   }


   @Operation(summary = "保存经营信息 *草稿*")
   @PostMapping(value="/saveRequestUserDraft")
   public String saveRequestUserDraft(@RequestBody MemberCertDTO memberCertDTO){
      return iMemberService.saveRequestUserDraft(memberCertDTO);
   }

   /**
    * 提交保存的草稿
    * @param memberId
    * @return
    */
   @Operation(summary = "提交保存的草稿")
   @PostMapping(value="/submitDraftRegisterBuyer")
   public String submitDraftRegisterBuyer(@RequestParam(DuplicateString.MEMBER_ID) String memberId) {
      return iMemberService.submitDraftRegisterBuyer(memberId);
   }
   /**
    * 提交保存的草稿
    * @param memberId
    * @return
    */
   @Operation(summary = "提交保存的草稿")
   @PostMapping(value="/submitDraftRegisterSeller")
   public String submitDraftRegisterSeller(@RequestParam(DuplicateString.MEMBER_ID) String memberId) {
      return iMemberService.submitDraftRegisterSeller(memberId);
   }
   /**
    * 提交保存的草稿
    * @param memberId
    * @return
    */
   @Operation(summary = "提交保存的草稿")
   @PostMapping(value="/submitDraftRegisterCarrier")
   public String submitDraftRegisterCarrier(@RequestParam(DuplicateString.MEMBER_ID) String memberId) {
      return iMemberService.submitDraftRegisterCarrier(memberId);
   }

   @Operation(summary = "获取经营信息审批详情")
   @PostMapping(value="/getMemberApprovalBusinessDetails")
   public MemberBusinessRequestDetailDTO getMemberApprovalBusinessDetails(@RequestParam String requestId) {
      return iMemberService.getMemberApprovalBusinessDetails(requestId);
   }

   @Operation(summary = "获取资质信息审批详情")
   @PostMapping(value="/getMemberApprovalCertDetails")
   public MemberCertRequestDetailDTO getMemberApprovalCertDetails(@RequestParam String requestId) {
      return iMemberService.getMemberApprovalCertDetails(requestId);
   }

   /**
    * 提交保存的草稿
    * @param memberId
    * @return
    */
   @Operation(summary = "提交保存的草稿")
   @PostMapping(value="/submitDraftRegisterSupplier")
   public String submitDraftRegisterSupplier(@RequestParam(DuplicateString.MEMBER_ID) String memberId) {
      return iMemberService.submitDraftRegisterSupplier(memberId);
   }


   @Operation(summary = "审批司机认证")
   @PostMapping(value="/driverCertApproval")
   public void driverCertApproval(@RequestParam(DuplicateString.MEMBER_ID) String memberId){
      iMemberService.driverCertApproval(memberId);

   }


   @Operation(summary = "获取会员状态")
   @PostMapping(value="/getMemberStatus")
   public MemberStatusDTO getMemberStatus(@RequestParam(DuplicateString.MEMBER_ID) String memberId){
      return iMemberService.getMemberStatus(memberId);

   }


   @Operation(summary = "保存实名认证资料")
   @PostMapping(value="/saveRealNameCert")
   public void saveRealNameCert(@RequestBody MemberCertDTO memberCertDTO){
      if( memberCertDTO.getRequestTypeEnum() == null ){
         memberCertDTO.setRequestTypeEnum(ApproveRequestTypeEnum.REGISTER_PERSON_REAL_NAME_CERT);
      }
      iMemberService.saveRealNameCert(memberCertDTO);

   }

   @Operation(summary = "保存实名认证资料(主要供司机APP使用)")
   @PostMapping(value="/saveMemberCert")
   public void saveMemberCert(@Valid @RequestBody MemberCertParam memberCertParam){
      iMemberService.saveMemberCert(memberCertParam);
   }


   @Operation(summary = "实名认证")
   @PostMapping(value="/createRealNameCert")
   public String createRealNameCert(@RequestBody MemberCertDTO memberCertDTO){
      if( memberCertDTO.getRequestTypeEnum() == null ){
         memberCertDTO.setRequestTypeEnum(ApproveRequestTypeEnum.REGISTER_PERSON_REAL_NAME_CERT);
      }
      return iMemberService.createRealNameCert(memberCertDTO);

   }

   @Operation(summary = "提交实名认证")
   @PostMapping(value="/submitRealNameCert")
   public String submitRealNameCert(@RequestParam(DuplicateString.ACCOUNT_ID) String accountId, @RequestParam(value = "approveRequestTypeEnumCode",required = false)  String requestTypeEnum){
      return iMemberService.submitRealNameCert(accountId,requestTypeEnum == null ? ApproveRequestTypeEnum.REGISTER_PERSON_REAL_NAME_CERT : ApproveRequestTypeEnum.getByCode(requestTypeEnum));

   }

   /**
    * 更新会员信息 这里暂只能信法人身份证信息
    * @param memberDTO
    * @return
    */
   @PostMapping("/update")
   public MemberDTO updateMemberInfoByMemberId(@RequestBody MemberDTO memberDTO)
   {
      return iMemberService.updateMemberInfoByMemberId(memberDTO);
   }


   @Operation(summary = "根据ID查找会员详情（没有资质信息）")
   @PostMapping(value="/findMemberDetailById")
   public MemberDTO findMemberDetailById(@RequestParam(DuplicateString.MEMBER_ID) String memberId){
      return iMemberService.findMemberDetailById(memberId);
   }


   @Operation(summary = "根据电话查找会员信息")
   @PostMapping(value="/findMemberIdByContactPhone")
   public List<MemberSuperSimpleDTO> findMemberIdByContactPhone(@RequestParam String contactPhone){
      return iMemberService.findMemberIdByContactPhone(contactPhone);
   }


   @Operation(summary = "个人升级供应商")
   @PostMapping(value="/updateMemberToEnterpriseSupplier")
   public String updateMemberToEnterpriseSupplier(@RequestBody MemberUpdateTypeDTO memberUpdateTypeDTO){
      return iMemberService.updateMemberToEnterpriseSupplier(memberUpdateTypeDTO);

   }


   @Operation(summary = "个人升级承运商")
   @PostMapping(value="/updateMemberToEnterpriseCarrier")
   public String updateMemberToEnterpriseCarrier(@RequestBody MemberUpdateTypeDTO memberUpdateTypeDTO){
      return iMemberService.updateMemberToEnterpriseCarrier(memberUpdateTypeDTO);

   }


   @Operation(summary = "根据ID查看会员简要信息")
   @PostMapping(value="/findMemberSimpleById")
   public MemberSimpleDTO findMemberSimpleById(@RequestParam(DuplicateString.MEMBER_ID) String memberId){
      return iMemberService.findMemberSimpleById(memberId);
   }

   @Operation(summary = "根据会员代码查看会员简要信息")
   @PostMapping(value="/findMemberSimpleByMemberCode")
   public MemberSimpleDTO findMemberSimpleByMemberCode(@RequestParam(DuplicateString.MEMBER_CODE) String memberCode){
      return iMemberService.findMemberSimpleByMemberCode(memberCode);
   }


   @Operation(summary = "根据ID查找会员名")
   @PostMapping(value="/findMemberNameByMemberId")
   public String findMemberNameByMemberId(@RequestParam(DuplicateString.MEMBER_ID) String memberId){
      return iMemberService.findMemberNameByMemberId(memberId);
   }

   @Operation(summary = "根据ID查询买家名称(1如果是企业买家：如果有简称，则返回简称否则返回全称.2如果是个人买家：如果有真实姓名，则返回真实姓名；如果账户名是UUID，且微信名称不为空，则返回微信名称，如果微信名称为空但是手机号正确，则返回手机号，其它：返回账户名称)")
   @PostMapping(value="/findBuyerNameByMemberId")
   public String findBuyerNameByMemberId(@RequestParam(DuplicateString.MEMBER_ID) String memberId){
      return iMemberService.findBuyerNameByMemberId(memberId);
   }


   @Operation(summary = "根据账户ID查找会员名")
   @PostMapping(value="/findMemberNameByAccountId")
   public String findMemberNameByAccountId(@RequestParam(DuplicateString.ACCOUNT_ID) String accountId){
      return iMemberService.findMemberNameByAccountId(accountId);
   }

   @Operation(summary = "根据会员ID查找会员代码")
   @PostMapping(value="/findMemberCodeByMemberId")
   public String findMemberCodeByMemberId(@RequestParam(DuplicateString.MEMBER_ID) String memberId){
      return iMemberService.findMemberCodeByMemberId(memberId);
   }


   @Operation(summary = "根据名字查找企业承运商")
   @PostMapping(value="/findEnterpriseCarrierByName")
   public List<MemberSuperSimpleDTO> findEnterpriseCarrierByName(@RequestParam String carrierName){
      return iMemberService.findEnterpriseCarrierByName(carrierName);
   }


   @Operation(summary = "个人升级企业卖家")
   @PostMapping(value="/updateMemberToEnterpriseSeller")
   public String updateMemberToEnterpriseSeller(@RequestBody MemberUpdateTypeDTO memberUpdateTypeDTO){
      return iMemberService.updateMemberToEnterpriseSeller(memberUpdateTypeDTO);

   }


   @Operation(summary = "根据memberName模糊查询")
   @PostMapping(value="/findByBuyerNameLike")
   public List<MemberSuperSimpleDTO> findByBuyerNameLike(@RequestParam String buyerName){
      return iMemberService.findByBuyerNameLike(buyerName);
   }
   @Operation(summary = "根据memberName模糊查询卖家、承运商、供应商")
   @PostMapping(value="/findMemberByNameLike")
   public List<MemberSuperSimpleDTO> findMemberByNameLike(MemberNameLikeQueryDTO memberNameLikeQueryDTO){
      return iMemberService.findMemberByNameLike(memberNameLikeQueryDTO);
   }

   @Operation(summary = "根据memberName,memberCode模糊查询")
   @PostMapping(value="/findByNameLikeOrCodeLike")
   public List<MemberDTO> findByNameLikeOrCodeLike(@RequestParam String name,@RequestParam String code) {
      return iMemberService.findByNameLikeOrCodeLike(name,code);
   }

   @Operation(summary = "根据ids查找会员简单信息")
   @PostMapping(value="/findMemberSimpleByIds")
   public List<MemberSimpleDTO> findMemberSimpleByIds(@RequestBody List<String> ids){
      return iMemberService.findMemberSimpleByIds(ids);
   }

   @Operation(summary = "根据卖家查询其作为经销商的上游厂家")
   @PostMapping(value="/findAgentUpMemberByCustomerId")
   public List<MemberSimpleDTO> findAgentUpMemberByCustomerId(@RequestParam String customerId){
      return iMemberService.findAgentUpMemberByCustomerId(customerId);
   }

   @Operation(summary = "根据上游厂家查询其下游经销商(分页查询参考pageMember方法,设置memberQueryDTO.agentSellerId)")
   @PostMapping(value="/findAgentUnderCustomerBySellerId")
   public List<MemberSimpleDTO> findAgentUnderCustomerBySellerId(@RequestParam String sellerId){
      return iMemberService.findAgentUnderCustomerBySellerId(sellerId);
   }


   @Operation(summary = "根据ids查找会员详细信息")
   @PostMapping(value="/findMemberDetailsByIds")
   public List<MemberDetailDTO> findMemberDetailsByIds(@RequestBody List<String> ids){
      return iMemberService.findMemberDetailsByIds(ids);
   }


   @Operation(summary = "分页查询企业承运商")
   @PostMapping(value="/pageEnterpriseCarrier")
   public PageInfo<MemberSimpleDTO> pageEnterpriseCarrier(@RequestBody MemberQueryDTO query,@RequestParam Integer pageNum,@RequestParam Integer pageSize){
      return iMemberService.pageEnterpriseCarrier(query,pageNum,pageSize);
   }


   @Operation(summary = "根据资质ID查找认证详情")
   @PostMapping(value="/findMemberCertByCertId")
   public MemberCertDTO findMemberCertByCertId(@RequestParam String certId){
      return iMemberService.findMemberCertByCertId(certId);
   }


   @Operation(summary = "根据账户ID查找实名认证详情")
   @PostMapping(value="/findRealNameCertByAccountId")
   public MemberCertDTO findRealNameCertByAccountId(@RequestParam(DuplicateString.ACCOUNT_ID) String accountId){
      return iMemberService.findRealNameCertByAccountId(accountId);
   }

   @Operation(summary = "查询身份证信息（不含附件图片信息）")
   @PostMapping(value="/findOnlyRealNameCertByMemberId")
   public MemberCertDTO findOnlyRealNameCertByMemberId(@RequestParam(DuplicateString.MEMBER_ID) String memberId){
      return iMemberService.findOnlyRealNameCertByMemberId(memberId);
   }

   @Operation(summary = "根据账户ID查找实名认证详情(信)")
   @PostMapping(value="/findRealNameCertByAccountIdNew")
   public MemberCertNewDTO findRealNameCertByAccountIdNew(@RequestParam(DuplicateString.ACCOUNT_ID) String accountId){
      return iMemberService.findRealNameCertByAccountIdNew(accountId);
   }


   @Operation(summary = "资质文件认证(不审批),必须写明资质类型 certType")
   @PostMapping(value="/submitCertNoApproval")
   public String submitCertNoApproval(@RequestBody SubmitCertNoApprovalDTO dto){
      return iMemberService.submitCertNoApproval(dto);

   }


   @Operation(summary = "分页查询企业卖家")
   @PostMapping(value="/pageEnterpriseSeller")
   public PageInfo<MemberSimpleDTO> pageEnterpriseSeller(@RequestBody MemberQueryDTO query,@RequestParam Integer pageNum,@RequestParam Integer pageSize){
      return iMemberService.pageEnterpriseSeller(query,pageNum,pageSize);
   }


   @Operation(summary = "根据名字查找企业卖家")
   @PostMapping(value="/findEnterpriseSellerByName")
   public List<MemberSuperSimpleDTO> findEnterpriseSellerByName(@RequestParam String sellerName){
      return iMemberService.findEnterpriseSellerByName(sellerName);
   }

   @Operation(summary = "根据名字查找企业买家")
   @PostMapping(value = "/findEnterpriseBuyerByName")
   public List<MemberSuperSimpleDTO> findEnterpriseBuyerByName(@RequestParam String buyerName) {
      return iMemberService.findEnterpriseBuyerByName(buyerName);
   }


   @Operation(summary = "根据会员ID查找认证详情")
   @PostMapping(value="/findMemberCertByMemberId")
   public List<MemberCertDTO> findMemberCertByMemberId(@RequestParam(DuplicateString.MEMBER_ID) String memberId){
      return iMemberService.findMemberCertByMemberId(memberId);
   }


   @Operation(summary = "一般资质变更,必须写明资质类型 certType、CertId")
   @PostMapping(value="/updateCertNoApproval")
   public String updateCertNoApproval(@RequestBody UpdateCertNoApprovalDTO dto){
      return iMemberService.updateCertNoApproval(dto);
   }


   @Operation(summary = "变更企业经营信息")
   @PostMapping(value="/updateBusinessInfoNoApproval")
   public void updateBusinessInfoNoApproval(@RequestBody MemberBusinessInfoDTO memberBusinessInfoDTO){
      iMemberService.updateBusinessInfoNoApproval(memberBusinessInfoDTO);

   }


   @Operation(summary = "分页获取会员请求")
   @PostMapping(value="/pageMemberApprovalRequests")
   public PageInfo<MemberApprovalRequestDTO> pageMemberApprovalRequests(@RequestBody MemberApprovalRequestQueryDTO query,@RequestParam Integer pageNum,@RequestParam Integer pageSize){
      return iMemberService.pageMemberApprovalRequests(query,pageNum,pageSize);
   }


   @Operation(summary = "会员请求列表")
   @PostMapping(value="/findMemberApprovalRequestList")
   public List<MemberApprovalRequestDTO> findMemberApprovalRequestList(@RequestBody MemberApprovalRequestQueryDTO query){
      return iMemberService.findMemberApprovalRequestList(query);
   }


   @Operation(summary = "找到最近的变更记录")
   @PostMapping(value="/findRecentMemberApproveRequest")
   public MemberApprovalRequestDTO findRecentMemberApproveRequest(@RequestBody MemberApprovalRequestQueryDTO queryDTO){
      return iMemberService.findRecentMemberApproveRequest(queryDTO);
   }


   @Operation(summary = "变更企业经营信息")
   @PostMapping(value="/updateBusinessInfo")
   public String updateBusinessInfo(@RequestBody MemberBusinessInfoDTO memberBusinessInfoDTO){
      return iMemberService.updateBusinessInfo(memberBusinessInfoDTO);

   }


   @Operation(summary = "个人会员升级到买家企业会员")
   @PostMapping(value="/updateMemberToEnterpriseBuyerNoApproval")
   public String updateMemberToEnterpriseBuyerNoApproval(@RequestBody MemberUpdateTypeDTO memberUpdateTypeDTO){
      return iMemberService.updateMemberToEnterpriseBuyerNoApproval(memberUpdateTypeDTO);

   }


   @Operation(summary = "个人升级承运商(不审批)")
   @PostMapping(value="/updateMemberToEnterpriseCarrierNoApproval")
   public String updateMemberToEnterpriseCarrierNoApproval(@RequestBody MemberUpdateTypeDTO memberUpdateTypeDTO){
      return iMemberService.updateMemberToEnterpriseCarrierNoApproval(memberUpdateTypeDTO);

   }


   @Operation(summary = "分页获取会员请求")
   @PostMapping(value="/pageRegisterMemberApprovalRequests")
   public PageInfo<MemberApprovalRequestDTO> pageRegisterMemberApprovalRequests(@RequestBody MemberApprovalRequestQueryDTO query,@RequestParam Integer pageNum,@RequestParam Integer pageSize){
      return iMemberService.pageRegisterMemberApprovalRequests(query,pageNum,pageSize);
   }


   @Operation(summary = "个人升级企业卖家(不审批)")
   @PostMapping(value="/updateMemberToEnterpriseSellerNoApproval")
   public String updateMemberToEnterpriseSellerNoApproval(@RequestBody MemberUpdateTypeDTO memberUpdateTypeDTO){
      return iMemberService.updateMemberToEnterpriseSellerNoApproval(memberUpdateTypeDTO);

   }


   @Operation(summary = "个人升级供应商(不审批)")
   @PostMapping(value="/updateMemberToEnterpriseSupplierNoApproval")
   public String updateMemberToEnterpriseSupplierNoApproval(@RequestBody MemberUpdateTypeDTO memberUpdateTypeDTO){
      return iMemberService.updateMemberToEnterpriseSupplierNoApproval(memberUpdateTypeDTO);

   }


   @Operation(summary = "提交企业注册（买家） requestType - 申请类型 必须指明每个资质的资质类型，是经营资质、卖家资质、承运商资质。。。")
   @PostMapping(value="/registerBuyer")
   public String registerBuyer(@RequestBody MemberRequestDTO memberRequestDTO){
      return iMemberService.registerBuyer(memberRequestDTO);
   }


   @Operation(summary = "一般资质变更,必须写明资质类型 certType、CertId")
   @PostMapping(value="/updateCert")
   public String updateCert(@RequestBody UpdateCertDTO dto){
      return iMemberService.updateCert(dto);
   }


   @Operation(summary = "企业注册（卖家）")
   @PostMapping(value="/registerSeller")
   public String registerSeller(@RequestBody MemberRequestDTO memberRequestDTO){
      return iMemberService.registerSeller(memberRequestDTO);
   }


   @Operation(summary = "拒绝司机认证")
   @PostMapping(value="/driverCertReject")
   public void driverCertReject(@RequestParam(DuplicateString.MEMBER_ID) String memberId){
      iMemberService.driverCertReject(memberId);

   }


   @Operation(summary = "获取企业更新资质")
   @PostMapping(value="/findUpdateTypeMemberCertById")
   public List<MemberCertDTO> findUpdateTypeMemberCertById(@RequestParam String requestId){
      return iMemberService.findUpdateTypeMemberCertById(requestId);
   }


   @Operation(summary = "资质文件认证,必须写明资质类型 certType")
   @PostMapping(value="/submitCert")
   public String submitCert(@RequestBody SubmitCertDTO dto){
      return iMemberService.submitCert(dto);

   }


   @Operation(summary = "资质文件认证,必须写明资质类型 certType")
   @PostMapping(value="/findMemberCertByRequestId")
   public MemberCertViewDTO findMemberCertByRequestId(@RequestParam String requestId){
      return iMemberService.findMemberCertByRequestId(requestId);

   }

   @Operation(summary = "根据会员id批量查询审批状态")
   @PostMapping(value="/findRequestByMemberIdList")
   public Map<String,String> findRequestByMemberIdList(@RequestBody List<String> memberIdList){
      return iMemberService.findRequestByMemberIdList(memberIdList);
   }


   @Operation(summary = "禁用会员")
   @PostMapping(value="/disableMember")
   public void disableMember(@RequestParam(DuplicateString.MEMBER_ID) String memberId, @RequestParam String operatorId){
      iMemberService.disableMember(memberId, operatorId);

   }


   @Operation(summary = "获取会员类型")
   @PostMapping(value="/findMemberTypeByMemberId")
   public MemberTypeDTO findMemberTypeByMemberId(@RequestParam(DuplicateString.MEMBER_ID) String memberId){
      return iMemberService.findMemberTypeByMemberId(memberId);

   }


   @Operation(summary = "分页查询会员")
   @PostMapping(value="/pageMember")
   public PageInfo<MemberSimpleDTO> pageMember(@RequestBody MemberQueryDTO query,@RequestParam Integer pageNum,@RequestParam Integer pageSize){
      return iMemberService.pageMember(query,pageNum,pageSize);
   }

   @Operation(summary = "同步会员名称")
   @PostMapping(value="/syncMemberName")
   public Boolean syncMemberName(@RequestBody MemberQueryDTO query){
      iMemberService.syncMemberName(query);
      return true;
   }


   @Operation(summary = "根据ids查找会员")
   @PostMapping(value="/findMemberByIds")
   public List<MemberDTO> findMemberByIds(@RequestBody List<String> ids){
      return iMemberService.findMemberByIds(ids);
   }


   @Operation(summary = "按照id查询会员（包括资质）")
   @PostMapping(value="/findMemberById")
   public MemberDetailDTO findMemberById(@RequestParam String id){
      return iMemberService.findMemberById(id);
   }


   @Operation(summary = "按照id查询会员经营信息（包括审批中资质）")
   @PostMapping(value="/findMemberBusinessInfoById")
   public MemberBusinessInfoDTO findMemberBusinessInfoById(@RequestParam(DuplicateString.MEMBER_ID) String memberId){
      return iMemberService.findMemberBusinessInfoById(memberId);
   }

   @Operation(summary = "按照id查询会员（只包括已审批的资质）")
   @PostMapping(value="/findRealMemberById")
   public MemberDTO findRealMemberById(@RequestParam String id){
      return iMemberService.findRealMemberById(id);
   }


   @Operation(summary = "拒绝请求")
   @PostMapping(value="/rejectRequest")
   public void rejectRequest(@RequestBody RejectRequestDTO dto){
      iMemberService.rejectRequest(dto);

   }
   @Operation(summary = "删除（撤销）请求")
   @PostMapping(value="/cancelRequest")
   public Boolean cancelRequest(@RequestBody CancelRequestDTO dto){
      return iMemberService.cancelRequest(dto);
   }

   @Operation(summary = "企业注册（供应商）")
   @PostMapping(value="/registerSupplier")
   public String registerSupplier(@RequestBody MemberRequestDTO memberRequestDTO){
      return iMemberService.registerSupplier(memberRequestDTO);
   }


   @Operation(summary = "审批请求")
   @PostMapping(value="/approveRequest")
   public void approveRequest(@RequestBody ApproveRequestDTO dto){
      iMemberService.approveRequest(dto);

   }

   @Operation(summary = "查询会员最近一次审批请求")
   @PostMapping(value="/findLastChangeRequest")
   public MemberApproveRequestDTO findLastChangeRequest(@RequestParam(DuplicateString.MEMBER_ID) String memberId){
      return iMemberService.findLastChangeRequest(memberId);
   }



   @Operation(summary = "启用会员")
   @PostMapping(value="/enableMember")
   public void enableMember(@RequestParam(DuplicateString.MEMBER_ID) String memberId, @RequestParam String operatorId){
      iMemberService.enableMember(memberId, operatorId);

   }


   @Operation(summary = "查询草稿")
   @PostMapping(value="/findDraft")
   public MemberRequestDTO findDraft(@RequestParam(DuplicateString.MEMBER_ID) String memberId){
      return iMemberService.findDraft(memberId);
   }


   @Operation(summary = "是否有未完结的申请")
   @PostMapping(value="/hasNoApproveRequest")
   public boolean hasNoApproveRequest(@RequestParam(DuplicateString.MEMBER_ID) String memberId) {
      return iMemberService.hasNoApproveRequest(memberId);
   }


   @Operation(summary = "个人名是否使用")
   @PostMapping(value="/isMemberNameUsed")
   public boolean isMemberNameUsed(@RequestParam String memberName){
      return iMemberService.isMemberNameUsed(memberName);
   }


   @Operation(summary = "企业注册（承运商）")
   @PostMapping(value="/registerCarrier")
   public String registerCarrier(@RequestBody MemberRequestDTO memberRequestDTO){
      return iMemberService.registerCarrier(memberRequestDTO);
   }


   @Operation(summary = "根据承运商类型获取承运商")
   @PostMapping(value="/findMemberCarrierByCarrierType")
   public List<MemberListViewDTO> findMemberCarrierByCarrierType(@RequestParam String carrierType){
      return iMemberService.findMemberCarrierByCarrierType(carrierType);
   }


   @Operation(summary = "企业注册（卖家）")
   @PostMapping(value="/registerSellerNoApproval")
   public String registerSellerNoApproval(@RequestBody MemberRequestDTO memberRequestDTO){
      return iMemberService.registerSellerNoApproval(memberRequestDTO);
   }


   @Operation(summary = "企业注册（承运商）")
   @PostMapping(value="/registerCarrierNoApproval")
   public String registerCarrierNoApproval(@RequestBody MemberRequestDTO memberRequestDTO){
      return iMemberService.registerCarrierNoApproval(memberRequestDTO);
   }

   @Operation(summary = "获取资质（已拥有资质、待审批资质、拒绝资质）")
   @PostMapping(value="/findMemberCertList")
   public List<MemberCertViewDTO> findMemberCertList(@RequestParam(DuplicateString.MEMBER_ID) String memberId) {
      return iMemberService.findMemberCertList(memberId);
   }

   @Operation(summary = "企业注册（供应商）")
   @PostMapping(value="/registerSupplierNoApproval")
   public String registerSupplierNoApproval(@RequestBody MemberRequestDTO memberRequestDTO){
      return iMemberService.registerSupplierNoApproval(memberRequestDTO);
   }


   @Operation(summary = "更新会员基本信息")
   @PostMapping(value="/updateBaseInfo")
   public void updateBaseInfo(@RequestBody MemberBaseInfoDTO memberBaseInfoDTO){
      iMemberService.updateBaseInfo(memberBaseInfoDTO);

   }

   private MemberRequestDTO appDTO2RequestDTO(MemberRegisterByAppDTO dto) {
      MemberRequestDTO requestDTO = new MemberRequestDTO();
      BeanUtils.copyProperties(dto, requestDTO);
      MemberCertDTO bu = new MemberCertDTO();
      BeanUtils.copyProperties(dto.getBusinessQueryCert(), bu);
      requestDTO.setBusinessCert(bu);
      MemberCertDTO org = new MemberCertDTO();
      BeanUtils.copyProperties(dto.getOrganizationQueryCert(), org);
      requestDTO.setOrganizationCert(org);
      MemberCertDTO tax = new MemberCertDTO();
      BeanUtils.copyProperties(dto.getTaxQueryCert(), tax);
      requestDTO.setTaxCert(tax);
      if (dto.getMemberRegisterCertDTOList() != null && !dto.getMemberRegisterCertDTOList().isEmpty()) {
         List<MemberCertDTO> collect = dto.getMemberRegisterCertDTOList().stream().map(item -> {
            MemberCertDTO certDTO = new MemberCertDTO();
            BeanUtils.copyProperties(item, certDTO);
            return certDTO;
         }).toList();
         requestDTO.setMemberCertDTOList(collect);
      } else {
         requestDTO.setMemberCertDTOList(null);
      }
      requestDTO.setMemberServiceAreaItemQueryDTOList(dto.getMemberServiceAreaItemQueryDTOList());
      return requestDTO;
   }

   @Operation(summary = "分页搜索承运商列表")
   @PostMapping(value="/searchCarrierList")
   public ItemResult<PageData<MemberCarrierDTO>> searchCarrierList(
           @RequestBody PageQuery<SearchCarrierDTO> pageQuery) {
      return iMemberService.searchCarrierList(pageQuery);
   }

   @Schema(description = "查找平台物流款收款承运商")
   @PostMapping(value = "/findCollectionCarrier")
   public MemberDTO findCollectionCarrier(@RequestBody CollectionCarrierQueryDTO collectionCarrierQueryDTO){
      return iMemberService.findCollectionCarrier(collectionCarrierQueryDTO);
   }

   @Operation(summary = "根据会员名称和会员类型标记查询该类型的会员id和会员名称")
   @PostMapping(value="/findMemberIdAndNameByKey")
   public List<MemberIdAndNameDTO>  findMemberIdAndNameByKey(@RequestBody MemberQueryByKeyDTO query){
      return iMemberService.findMemberIdAndNameByKey(query);
   }

   @Operation(summary = "根据会员名称和会员代码搜索会员列表")
   @PostMapping(value="/search")
   public List<MemberSearchDTO> search(@Parameter(description = "查询条件") @RequestBody MemberSearchQueryDTO memberSearchQueryDTO)
   {
      return iMemberService.memberSearchByName(memberSearchQueryDTO);
   }

   @Operation(summary = "根据会员ID查找船舶所有权证列表")
   @PostMapping(value="/findMemberAttachmentByMemberId")
   public List<AttachmentDTO> findMemberAttachmentByMemberId(@RequestParam(DuplicateString.MEMBER_ID) String memberId){
      return iMemberService.findMemberAttachmentByMemberId(memberId);
   }


   @Operation(summary = "根据条件查询资质信息")
   @PostMapping(value="/selectMemberCertList")
   public List<MemberCertDTO> selectMemberCertList(@RequestBody CertQueryDTO query){
      return iMemberService.selectMemberCertList(query);
   }

   @Operation(summary = "根据会员Id查询未审核船舶所有证信息")
   @PostMapping(value="/findOwnerShipByMemberId")
   public List<MemberCertDTO> findOwnerShipByMemberId(@RequestParam String memberId){
      return iMemberService.findOwnerShipByMemberId(memberId);
   }

   @Operation(summary = "根据条件查询会员名称及会员ID")
   @PostMapping(value="/findMemberNameByQuery")
   public Map<String,String> findMemberNameByQuery(@RequestBody MemberQueryDTO query){
      return iMemberService.findMemberNameByQuery(query);
   }

   @Operation(summary = "根据会员ID集合查询联系电话")
   @PostMapping(value = "/findContactPhoneByIds")
   public Map<String, String> findContactPhoneByIds(@RequestBody Collection<String> ids) {
      return iMemberService.findContactPhoneByIds(ids);
   }

   @Operation(summary = "设置承运商的紧急任务时长")
   @PostMapping(value = "/setUrgentTaskTime")
   public ItemResult<Boolean> setUrgentTaskTime(@RequestParam Integer urgentTaskTime,
                                    @RequestParam String memberId,
                                    @RequestParam String accountId) {
      return iMemberService.setUrgentTaskTime(urgentTaskTime,  memberId, accountId);
   }

   @Operation(summary = "获取承运商的紧急任务设置时长")
   @PostMapping(value = "/getUrgentTaskTime")
   public Integer getUrgentTaskTime(@RequestParam String memberId) {
      return iMemberService.getUrgentTaskTime(memberId);
   }

   @Operation(summary = "根据accountID查询资质")
   @PostMapping(value="/queryMemberCertList")
   public List<MemberCertDTO> queryMemberCertList(@RequestBody DriverAccountDTO dto) {
      return iMemberService.queryMemberCertList(dto.getList(),dto.getCertType());
   }
   
   @Operation(summary = "根据ID查询承运商身份")
   @PostMapping(value="/carrierIdentityById")
   public CarrierIdentityDTO carrierIdentityById(@RequestParam String memberId) {
	   return iMemberService.carrierIdentityById(memberId);
   }

}
