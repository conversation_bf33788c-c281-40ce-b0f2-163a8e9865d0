package com.ecommerce.member.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.common.result.ItemResult;
import com.ecommerce.member.api.dto.base.PageQuery;
import com.ecommerce.member.api.dto.relation.ContractBatchAdjustPriceFlgUpdateDTO;
import com.ecommerce.member.api.dto.relation.CustomerDetailDTO;
import com.ecommerce.member.api.dto.relation.ErpCustomCondDTO;
import com.ecommerce.member.api.dto.relation.ErpCustomInfoDTO;
import com.ecommerce.member.api.dto.relation.ErpCustomRelationGroupDTO;
import com.ecommerce.member.api.dto.relation.ErpCustomerListDTO;
import com.ecommerce.member.api.dto.relation.ErpCustomerQueryDTO;
import com.ecommerce.member.api.dto.relation.ErpExecueResultDTO;
import com.ecommerce.member.api.dto.relation.ErpExecueResultQueryDTO;
import com.ecommerce.member.api.dto.relation.ErpMultiCustomerInfoDTO;
import com.ecommerce.member.api.dto.relation.ErpRelationMdmCodeDTO;
import com.ecommerce.member.api.dto.relation.ErpRelationOption;
import com.ecommerce.member.api.dto.relation.ErpRelationQueryDTO;
import com.ecommerce.member.api.dto.relation.MemberBlacklistDTO;
import com.ecommerce.member.api.dto.relation.MemberBlacklistQueryDTO;
import com.ecommerce.member.api.dto.relation.MemberRelationAddDTO;
import com.ecommerce.member.api.dto.relation.MemberRelationDTO;
import com.ecommerce.member.api.dto.relation.MemberRelationInvoiceTypeUpdateDTO;
import com.ecommerce.member.api.dto.relation.MemberRelationQueryDTO;
import com.ecommerce.member.api.dto.relation.MemberTagDTO;
import com.ecommerce.member.api.dto.relation.MemberTagGroupDTO;
import com.ecommerce.member.exception.DuplicateString;
import com.ecommerce.member.service.IMemberRelationService;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@RestController
@Tag(name = "MemberRelation", description = "null")
@RequestMapping("/memberRelation")
@RequiredArgsConstructor
public class MemberRelationController {

   private final IMemberRelationService iMemberRelationService;

   @Operation(summary = "通过系统添加会员关系")
   @PostMapping(value="/addMemberRelationBySystem")
   public void addMemberRelationBySystem(@RequestBody MemberRelationAddDTO dto){
      iMemberRelationService.addMemberRelationBySystem(dto);
   }

   @Operation(summary = "通过交易添加会员关系")
   @PostMapping(value="/addMemberRelationByTrade")
   public void addMemberRelationByTrade(@RequestParam String baseMemberId,@RequestParam String customerMemberId){
      MemberRelationAddDTO dto = new MemberRelationAddDTO();
      dto.setMemberId(baseMemberId);
      dto.setCustomerId(customerMemberId);
      iMemberRelationService.addMemberRelationByTrade(dto);
   }

   @Operation(summary = "通过交易添加会员关系")
   @PostMapping(value="/addMemberRelationByTrade2")
   public void addMemberRelationByTrade2(@RequestBody MemberRelationAddDTO dto){
      iMemberRelationService.addMemberRelationByTrade(dto);
   }

   @Operation(summary = "通过卖家添加经销商关系")
   @PostMapping(value="/addMemberRelationBySeller")
   public void addMemberRelationBySeller(@RequestBody MemberRelationAddDTO dto){
      iMemberRelationService.addMemberRelationBySeller(dto);
   }

   @Operation(summary = "更新经销商标记")
   @PostMapping(value="/updateAgentFlag")
   public Boolean updateAgentFlag(@RequestParam String relationId,@RequestParam String memberId,@RequestParam String operatorId) {
      return iMemberRelationService.updateAgentFlag(relationId,memberId,operatorId);
   }

   @Operation(summary = "更新票制 票制(1一票制、2两票制)")
   @PostMapping(value="/updateInvoiceType")
   public Boolean updateInvoiceType(@RequestBody MemberRelationInvoiceTypeUpdateDTO dto) {
      return iMemberRelationService.updateInvoiceType(dto);
   }

   @Operation(summary = "是否存在经销商关系")
   @PostMapping(value="/hasAgentRelation")
   public Boolean hasAgentRelation(@RequestParam(DuplicateString.MEMBER_ID) String memberId, @RequestParam String customerId){
      return iMemberRelationService.hasAgentRelation(memberId,customerId);
   }

   @Operation(summary = "通过收藏商品添加会员关系")
   @PostMapping(value="/addMemberRelationByFavoriteGoods")
   public void addMemberRelationByFavoriteGoods(@RequestParam(required = false) String baseMemberId,@RequestParam(required = false) String customerMemberId){
      iMemberRelationService.addMemberRelationByFavoriteGoods(baseMemberId,customerMemberId);
   }

   @Operation(summary = "通过收藏商品添加会员关系")
   @PostMapping(value="/addMemberRelationListByFavoriteGoods")
   public void addMemberRelationListByFavoriteGoods(@RequestBody List<String[]> relationIdList){
      iMemberRelationService.addMemberRelationListByFavoriteGoods(relationIdList);
   }

   @Operation(summary = "移除通过收藏商品添加的会员关系")
   @PostMapping(value="/removeMemberRelationListByFavoriteGoods")
   public void removeMemberRelationListByFavoriteGoods(@RequestBody List<String[]> relationIdList,@RequestParam String operator){
      iMemberRelationService.removeMemberRelationListByFavoriteGoods(relationIdList,operator);
   }

   @Operation(summary = "通过收藏卖家店铺添加会员关系")
   @PostMapping(value="/addMemberRelationByFavoriteShop")
   public void addMemberRelationByFavoriteShop(@RequestParam String baseMemberId,@RequestParam String customerMemberId){
      iMemberRelationService.addMemberRelationByFavoriteShop(baseMemberId,customerMemberId);
   }

   @Operation(summary = "通过收藏卖家店铺添加会员关系")
   @PostMapping(value="/addMemberRelationListyFavoriteShop")
   public void addMemberRelationListyFavoriteShop(@RequestBody List<String[]> relationIdList){
      iMemberRelationService.addMemberRelationListyFavoriteShop(relationIdList);
   }

   @Operation(summary = "移除通过收藏卖家店铺添加的会员关系")
   @PostMapping(value="/removeMemberRelationListyFavoriteShop")
   public void removeMemberRelationListyFavoriteShop(@RequestBody List<String[]> relationIdList,@RequestParam String operator){
      iMemberRelationService.removeMemberRelationListyFavoriteShop(relationIdList,operator);
   }


   @Operation(summary = "是否可以删除标签组")
   @PostMapping(value="/canDeleteMemberTagGroup")
   public boolean canDeleteMemberTagGroup(@RequestParam String memberGroupId){
      return iMemberRelationService.canDeleteMemberTagGroup(memberGroupId);
   }


   @Operation(summary = "查询标签组详情")
   @PostMapping(value="/findMemberTagGroupDetail")
   public MemberTagGroupDTO findMemberTagGroupDetail(@RequestParam String memberTagGroupId){
      return iMemberRelationService.findMemberTagGroupDetail(memberTagGroupId);
   }


   @Operation(summary = "增加某对象进黑名单")
   @PostMapping(value="/addObjToBlacklist")
   public void addObjToBlacklist(@RequestParam(DuplicateString.MEMBER_ID) String memberId, @RequestParam String blackMemberId, @RequestParam String operatorId, @RequestParam String remarks){
      iMemberRelationService.addObjToBlacklist(memberId,blackMemberId,remarks, operatorId);
   }


   @Operation(summary = "修改会员关系")
   @PostMapping(value="/updateMemberRelation")
   public void updateMemberRelation(@RequestBody MemberRelationDTO memberRelationDTO,@RequestParam String operatorId){
      iMemberRelationService.updateMemberRelation(memberRelationDTO,operatorId);

   }


   @Operation(summary = "移除客户关系")
   @PostMapping(value="/removeMemberRelationByRelationId")
   public void removeMemberRelationByRelationId(@RequestParam String relationId,@RequestParam String operatorId){
      iMemberRelationService.removeMemberRelationByRelationId(relationId,operatorId);
   }
   @Operation(summary = "移除客户关系")
   @PostMapping(value="/removeMemberRelationListByRelationId")
   public void removeMemberRelationListByRelationId(@RequestBody List<String> relationIdList,@RequestParam String operatorId){
      iMemberRelationService.removeMemberRelationListByRelationId(relationIdList,operatorId);
   }

   @Operation(summary = "分页查询客户（分类型）")
   @PostMapping(value="/pageRelationMember")
   public PageInfo<MemberRelationDTO> pageRelationMember(@RequestBody MemberRelationQueryDTO query,@RequestParam Integer pageNum,@RequestParam Integer pageSize){
      return iMemberRelationService.pageRelationMember(query,pageNum,pageSize);
   }


   @Operation(summary = "分页查询客户（分类型）")
   @PostMapping(value="/findRelationMemberList")
   public List<MemberRelationDTO> findRelationMemberList(@RequestBody MemberRelationQueryDTO query){
      return iMemberRelationService.findRelationMemberList(query);
   }

   @Operation(summary = "根据卖家id和客户ERP代码查询客户")
   @PostMapping(value="/findBySellerIdAndMdmCode")
   public MemberRelationDTO findBySellerIdAndMdmCode(@RequestParam String sellerId,@RequestParam String mdmCode){
      return iMemberRelationService.findBySellerIdAndMdmCode(sellerId,mdmCode);
   }

   @Operation(summary = "创建标签组 （数量控制）")
   @PostMapping(value="/createMemberTagGroup")
   public void createMemberTagGroup(@RequestBody MemberTagGroupDTO memberTagGroupDTO,@RequestParam String operatorId){
      iMemberRelationService.createMemberTagGroup(memberTagGroupDTO,operatorId);

   }


   @Operation(summary = "由商家自己添加客户关系")
   @PostMapping(value="/addMemberRelationBySelf")
   public void addMemberRelationBySelf(@RequestBody MemberRelationAddDTO dto){
      iMemberRelationService.addMemberRelationBySelf(dto);
   }


   @Operation(summary = "是否可以删除标签")
   @PostMapping(value="/canDeleteMemberTag")
   public boolean canDeleteMemberTag(@RequestParam String memberTagId){
      return iMemberRelationService.canDeleteMemberTag(memberTagId);
   }


   @Operation(summary = "为客户关系移除标签")
   @PostMapping(value="/removeTagRelation")
   public void removeTagRelation(@RequestParam String relationId,@RequestParam String tagId){
      iMemberRelationService.removeTagRelation(relationId,tagId);

   }


   @Operation(summary = "删除标签组")
   @PostMapping(value="/deleteMemberTagGroup")
   public void deleteMemberTagGroup(@RequestParam String memberTagGroupId,@RequestParam String operatorId){
      iMemberRelationService.deleteMemberTagGroup(memberTagGroupId,operatorId);

   }


   @Operation(summary = "修改标签组信息")
   @PostMapping(value="/updateMemberGroup")
   public void updateMemberGroup(@RequestBody MemberTagGroupDTO memberTagGroupDTO,@RequestParam String operatorId){
      iMemberRelationService.updateMemberGroup(memberTagGroupDTO,operatorId);

   }


   @Operation(summary = "管理员添加会员关系")
   @PostMapping(value="/addMemberRelationByAdmin")
   public void addMemberRelationByAdmin(@RequestParam String baseMemberId,@RequestParam String customerMemberId,@RequestParam String operatorId){
      iMemberRelationService.addMemberRelationByAdmin(baseMemberId,customerMemberId,operatorId);
   }

   @Operation(summary = "管理员添加会员关系")
   @PostMapping(value="/addMemberRelationByAdmin2")
   public void addMemberRelationByAdmin2(@RequestBody MemberRelationAddDTO dto){
      iMemberRelationService.addMemberRelationByAdmin2(dto);
   }


   @Operation(summary = "把某对象从某会员黑名单列表中删除")
   @PostMapping(value="/removeObjFromBlacklist")
   public void removeObjFromBlacklist(@RequestParam(DuplicateString.MEMBER_ID) String memberId,@RequestParam String blackMemberId,@RequestParam String operatorId,@RequestParam String remarks){
      iMemberRelationService.removeObjFromBlacklist(memberId,blackMemberId,remarks,operatorId);
   }


   @Operation(summary = "根据会员ID和客户ID查询会员关系详情")
   @PostMapping(value="/getMemberRelationDetailByMemberIdAndCustomerId")
   public MemberRelationDTO getMemberRelationDetailByMemberIdAndCustomerId(@RequestParam(DuplicateString.MEMBER_ID) String memberId,@RequestParam String customerId){
      return iMemberRelationService.getMemberRelationDetailByMemberIdAndCustomerId(memberId,customerId);
   }

   @Operation(summary = "根据会员ID和客户ID查询会员关系详情")
   @PostMapping(value="/getInvoiceType")
   public Integer getInvoiceType(@RequestParam(DuplicateString.MEMBER_ID) String memberId,@RequestParam String customerId){
      return iMemberRelationService.getInvoiceType(memberId,customerId);
   }

   @Operation(summary = "根据会员ID和客户ID查询会员关系是否存在")
   @PostMapping(value="/relationIsExists")
   public Boolean relationIsExists(@RequestParam(DuplicateString.MEMBER_ID) String memberId,@RequestParam String customerId){
      return iMemberRelationService.relationIsExists(memberId,customerId);
   }


   @Operation(summary = "移除客户关系")
   @PostMapping(value="/removeMemberRelationByMemberIdAndCustomerId")
   public void removeMemberRelationByMemberIdAndCustomerId(@RequestParam(DuplicateString.MEMBER_ID) String memberId,@RequestParam String customerId,@RequestParam String operatorId){
      iMemberRelationService.removeMemberRelationByMemberIdAndCustomerId(memberId,customerId,operatorId);

   }


   @Operation(summary = "根据关系ID查询会员关系详情")
   @PostMapping(value="/getMemberRelationDetailByRelationId")
   public MemberRelationDTO getMemberRelationDetailByRelationId(@RequestParam String relationId){
      return iMemberRelationService.getMemberRelationDetailByRelationId(relationId);
   }


   @Operation(summary = "创建标签")
   @PostMapping(value="/createMemberTag")
   public void createMemberTag(@RequestBody MemberTagDTO memberTagDTO,@RequestParam String operatorId){
      iMemberRelationService.createMemberTag(memberTagDTO,operatorId);

   }


   @Operation(summary = "删除标签")
   @PostMapping(value="/deleteMemberTag")
   public void deleteMemberTag(@RequestParam String memberTagId,@RequestParam String operatorId){
      iMemberRelationService.deleteMemberTag(memberTagId,operatorId);

   }


   @Operation(summary = "为客户关系添加标签")
   @PostMapping(value="/addTagRelation")
   public void addTagRelation(@RequestParam String relationId,@RequestParam String tagId,@RequestParam String operatorId){
      iMemberRelationService.addTagRelation(relationId,tagId,operatorId);

   }


   @Operation(summary = "查询标签组内的标签")
   @PostMapping(value="/listMemberGroup")
   public List<MemberTagDTO> listMemberGroup(@RequestParam String memberTagGroupId){
      return iMemberRelationService.listMemberGroup(memberTagGroupId);
   }


   @Operation(summary = "修改标签信息")
   @PostMapping(value="/updateMemberTag")
   public void updateMemberTag(@RequestBody MemberTagDTO memberTagDTO,@RequestParam String operatorId){
      iMemberRelationService.updateMemberTag(memberTagDTO,operatorId);

   }


   @Operation(summary = "批量得到某会员所有黑名单信息")
   @PostMapping(value="/pageBlacklist")
   public PageInfo<MemberBlacklistDTO> pageBlacklist(@RequestBody MemberBlacklistQueryDTO queryDTO,@RequestParam Integer pageNum,@RequestParam Integer pageSize){
      return iMemberRelationService.pageBlacklist(queryDTO,pageNum,pageSize);
   }


   @Operation(summary = "判断两个对象是否有黑名单关系（myself，target）")
   @PostMapping(value="/isDefriend")
   public Boolean isDefriend(@RequestParam(DuplicateString.MEMBER_ID) String memberId,@RequestParam String blackMemberId){
      return iMemberRelationService.isDefriend(memberId,blackMemberId);
   }


   @Operation(summary = "查询标签详情")
   @PostMapping(value="/getMemberTag")
   public MemberTagDTO getMemberTag(@RequestParam String memberTagId){
      return iMemberRelationService.getMemberTag(memberTagId);
   }


   @Operation(summary = "查询标签组")
   @PostMapping(value="/findMemberGroup")
   public List<MemberTagGroupDTO> findMemberGroup(@RequestBody MemberTagGroupDTO query){
      return iMemberRelationService.findMemberGroup(query);
   }


   @Operation(summary = "根据条件查询会员的erp客户信息")
   @PostMapping(value="/findErpCustomer")
   public List<ErpCustomerListDTO> findErpCustomer(@RequestBody ErpCustomerQueryDTO query){
      return iMemberRelationService.findErpCustomer(query);
   }

   @Operation(summary = "EC-MEM-A2(会员注册变更结果异步回调)")
   @PostMapping(value="/erpChangeResultCallBack")
   public ItemResult<Boolean> erpChangeResultCallBack(@RequestBody ErpExecueResultDTO dto){
      return new ItemResult<>(iMemberRelationService.erpChangeResultCallBack(dto));
   }

   @Operation(summary = "EC-MEM-A3会员注册（变更）结果查询（同步查询添加或者更新erp客户的结果）")
   @PostMapping(value="/checkAddOrUpdateResult")
   public ErpExecueResultDTO checkAddOrUpdateResult(@RequestBody ErpExecueResultQueryDTO dto) {
      return iMemberRelationService.checkAddOrUpdateResult(dto);
   }

   @Operation(summary = "查询客户详情")
   @PostMapping(value="/findCustomerDetail")
   public CustomerDetailDTO findCustomerDetail(@RequestParam(DuplicateString.MEMBER_ID) String memberId, @RequestParam String customerId){
      return iMemberRelationService.findCustomerDetail(memberId,customerId);
   }

   /**
    * 查询erp mdmCode项
    * @param erpRelationQueryDTO
    * @return
    */
   @Operation(summary = "查询erp mdmCode项")
   @PostMapping(value="/queryMdmOptions")
   public List<ErpRelationOption> queryMdmOptions(@RequestBody ErpRelationQueryDTO erpRelationQueryDTO) {
      return iMemberRelationService.queryMdmOptions(erpRelationQueryDTO);
   }

   @Operation(summary = "更新买家是否参与合同批量调价的标记字段")
   @PostMapping(value="/updateContractBatchAdjustPriceFlg")
   public Boolean updateContractBatchAdjustPriceFlg(@RequestBody ContractBatchAdjustPriceFlgUpdateDTO dto) {
      return iMemberRelationService.updateContractBatchAdjustPriceFlg(dto);
   }

   @Operation(summary = "查询当前卖家下不参与合同批量调价的买家id")
   @PostMapping(value="/findContractBatchAdjustPriceDisabledBuyerBySellerId")
   public List<String> findContractBatchAdjustPriceDisabledBuyerBySellerId(@RequestParam String sellerId) {
      return iMemberRelationService.findContractBatchAdjustPriceDisabledBuyerBySellerId(sellerId);
   }

    @Operation(summary = "当前会员是否已经是其它会员的经销商")
   @GetMapping(value = "/agentFlag")
   public Boolean agentFlag(@RequestParam("memberId") String memberId){
      return iMemberRelationService.agentFlag(memberId);
   }

   /**
    * 前端创建会员关系时使用
    * 根据条件查询会员的erp客户信息,并按照erpMemberName做聚合分组
    * @param dto
    * @return
    */
   @Operation(summary = "根据条件查询会员的erp客户信息,并按照erpMemberName做聚合分组")
   @PostMapping(value = "/findErpCustomGroup")
   public List<ErpCustomRelationGroupDTO> findErpCustomGroup(@RequestBody ErpCustomerQueryDTO dto) {
      return iMemberRelationService.findErpCustomGroup((dto));
   }

   /**
    * 客户关系查询erp的客户信息
    * @param erpCustomCondDTO
    * @return
    */
   @Operation(summary = "客户关系查询erp的客户信息")
   @PostMapping(value = "/queryCustomInfoByRelation")
   public ErpCustomInfoDTO queryCustomInfoByRelation(@RequestBody ErpCustomCondDTO erpCustomCondDTO) {
      return iMemberRelationService.queryCustomInfoByRelation(erpCustomCondDTO);
   }

   /**
    * 买家视角,分页查询erp信息
    * @param pageQuery
    * @return
    */
   @Operation(summary = "买家视角,分页查询erp信息")
   @PostMapping(value = "/pageCustomerInfoBuyerView")
   public PageInfo<ErpMultiCustomerInfoDTO> pageCustomerInfoBuyerView(@RequestBody PageQuery<ErpCustomCondDTO> pageQuery) {
      return iMemberRelationService.pageCustomerInfoBuyerView(pageQuery);
   }

   /**
    * 经销商查询下游买家
    * @param memberId
    * @return
    */
   @Operation(summary = "经销商查询下游买家")
   @GetMapping(value = "/findCustomerByMemberId")
   public List<MemberRelationDTO> findCustomerByMemberId(@RequestParam("memberId") String memberId) {
      return iMemberRelationService.findCustomerByMemberId(memberId);
   }

   /**
    * 经销商查询上游厂商且是ERP卖家
    * @param memberId
    * @return
    */
   @Operation(summary = "经销商查询上游厂商且是ERP卖家")
   @GetMapping(value = "/findManufacturerByMemberId")
   public List<MemberRelationDTO> findManufacturerByMemberId(@RequestParam("memberId") String memberId) {
      return iMemberRelationService.findManufacturerByMemberId(memberId);
   }


   /**
    * 根据会员ID和客户ID查询会员所有mdmCode
    * @param memberId
    * @return
    */
   @Operation(summary = "根据会员ID和客户ID查询会员所有mdmCode")
   @GetMapping(value = "/getMdmCodeByMemberIdAndCustomerId")
   public List<ErpRelationMdmCodeDTO> getMdmCodeByMemberIdAndCustomerId(@RequestParam("memberId") String memberId,
                                                                    @RequestParam("customerId") String customerId) {
      return iMemberRelationService.getMdmCodeByMemberIdAndCustomerId(memberId,customerId);
   }

   /**
    * 查询有对买家建立了客户关系的卖家ID
    * @param customerId
    * @return
    */
   @Operation(summary = "查询有对买家建立了客户关系的卖家ID")
   @GetMapping(value = "/findSellerIdByCustomerId")
   public List<String> findSellerIdByCustomerId(@RequestParam("customerId") String customerId) {
      return iMemberRelationService.findSellerIdByCustomerId(customerId);
   }

}
