package com.ecommerce.member.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.member.api.dto.organization.OrgInfoDTO;
import com.ecommerce.member.api.dto.organization.OrgInfoListDTO;
import com.ecommerce.member.api.dto.organization.OrgInfoQueryDTO;
import com.ecommerce.member.api.dto.organization.OrgInfoUpdateDTO;
import com.ecommerce.member.exception.DuplicateString;
import com.ecommerce.member.service.IOrgInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 组织机构服务接口
 *
 * <AUTHOR>
*/

@RestController
@RequestMapping("/orgInfo")
@RequiredArgsConstructor
@Tag(name = "OrgInfo", description = "组织机构服务接口")
public class OrgInfoController {

   private final IOrgInfoService iOrgInfoService;

   @Operation(summary = "修改企业组织机构节点内容")
   @PostMapping(value="/update")
   public void update(@RequestBody OrgInfoUpdateDTO orgInfoUpdateDTO,@RequestParam String operatorId){
      iOrgInfoService.update(orgInfoUpdateDTO,operatorId);

   }


   @Operation(summary = "删除企业组织机构以及所有下级")
   @PostMapping(value="/delete")
   public void delete(@RequestParam String orgId,@RequestParam String operatorId){
      iOrgInfoService.delete(orgId,operatorId);

   }


   @Operation(summary = "创建企业组织机构")
   @PostMapping(value="/create")
   public void create(@RequestBody OrgInfoQueryDTO orgInfoQueryDTO,@RequestParam String operatorId){
      iOrgInfoService.create(orgInfoQueryDTO,operatorId);

   }


   @Operation(summary = "根据orgId获取父节点id")
   @PostMapping(value="/findParentIdByOrgId")
   public String findParentIdByOrgId(@RequestParam String orgId){
      return iOrgInfoService.findParentIdByOrgId(orgId);
   }


   @Operation(summary = "根据orgId获取所有子节点id")
   @PostMapping(value="/findChildrenIdsByOrgId")
   public List<String> findChildrenIdsByOrgId(@RequestParam String orgId){
      return iOrgInfoService.findChildrenIdsByOrgId(orgId);
   }


   @Operation(summary = "根据会员id获取所有组织机构")
   @PostMapping(value="/findOrgByMemberId")
   public OrgInfoDTO findOrgByMemberId(@RequestParam(DuplicateString.MEMBER_ID) String memberId){
      return iOrgInfoService.findOrgByMemberId(memberId);
   }

   @Operation(summary = "根据账户id获取组织机构")
   @PostMapping(value="/findOrgByAccountId")
   public OrgInfoDTO findOrgByAccountId(@RequestParam(DuplicateString.ACCOUNT_ID) String accountId){
      return iOrgInfoService.findOrgByAccountId(accountId);
   }


   @Operation(summary = "根据会员id获取所有组织机构列表")
   @PostMapping(value="/findOrgListByMemberId")
   public List<OrgInfoListDTO> findOrgListByMemberId(@RequestParam(DuplicateString.MEMBER_ID) String memberId){
      return iOrgInfoService.findOrgListByMemberId(memberId);
   }


   @Operation(summary = "根据orgid获取org节点详情")
   @PostMapping(value="/findOrgInfoByOrgId")
   public OrgInfoDTO findOrgInfoByOrgId(@RequestParam String orgId){
      return iOrgInfoService.findOrgInfoByOrgId(orgId);
   }


   @Operation(summary = "根据组织机构id获取下级组织机构")
   @PostMapping(value="/findChildOrgByOrgId")
   public OrgInfoDTO findChildOrgByOrgId(@RequestParam String orgId){
      return iOrgInfoService.findChildOrgByOrgId(orgId);
   }


   @Operation(summary = "按层级获取某企业组织机构")
   @PostMapping(value="/findByFloorAndMemberId")
   public List<OrgInfoDTO> findByFloorAndMemberId(@RequestParam Integer floor,@RequestParam(DuplicateString.MEMBER_ID) String memberId){
      return iOrgInfoService.findByFloorAndMemberId(floor,memberId);
   }


   @Operation(summary = "上移/下移组织机构（排序）")
   @PostMapping(value="/updateOrder")
   public void updateOrder(@RequestParam String orgId,@RequestParam Integer order,@RequestParam String operatorId){
      iOrgInfoService.updateOrder(orgId,order,operatorId);

   }



}
