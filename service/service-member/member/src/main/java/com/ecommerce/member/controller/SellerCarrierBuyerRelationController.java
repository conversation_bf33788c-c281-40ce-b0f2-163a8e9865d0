package com.ecommerce.member.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.common.result.PageData;
import com.ecommerce.member.api.dto.base.PageQuery;
import com.ecommerce.member.api.dto.relation2.RelationExistsDTO;
import com.ecommerce.member.api.dto.relation2.SellerCarrierBuyerRelationCreateDTO;
import com.ecommerce.member.api.dto.relation2.SellerCarrierBuyerRelationDTO;
import com.ecommerce.member.api.dto.relation2.SellerCarrierBuyerRelationDeleteDTO;
import com.ecommerce.member.api.dto.relation2.SellerCarrierBuyerRelationListDTO;
import com.ecommerce.member.api.dto.relation2.SellerCarrierBuyerRelationQueryDTO;
import com.ecommerce.member.service.ISellerCarrierBuyerRelationService;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@RestController
@Tag(name = "SellerCarrierBuyerRelation", description = "卖家-承运商-买家关系")
@RequestMapping("/sellerCarrierBuyerRelation")
@RequiredArgsConstructor
public class SellerCarrierBuyerRelationController {

   private final ISellerCarrierBuyerRelationService sellerCarrierBuyerRelationService;

   @Operation(summary = "删除关系")
   @PostMapping(value="/delete")
   public Boolean delete(@RequestBody SellerCarrierBuyerRelationDeleteDTO deleteDTO){
      return sellerCarrierBuyerRelationService.delete(deleteDTO);
   }

   @Operation(summary = "关系是否已存在")
   @PostMapping(value="/relationExists")
   public Boolean relationExists(@RequestBody RelationExistsDTO dto){
      return sellerCarrierBuyerRelationService.relationExists(dto);
   }


   @Operation(summary = "批量新增关系")
   @PostMapping(value="/create")
   public Boolean create(@RequestBody List<SellerCarrierBuyerRelationCreateDTO> createList){
      return sellerCarrierBuyerRelationService.create(createList);
   }


   @Operation(summary = "翻页查询")
   @PostMapping(value="/findAll")
   public PageInfo<SellerCarrierBuyerRelationDTO> findAll(@RequestBody PageQuery<SellerCarrierBuyerRelationQueryDTO> query){
      return sellerCarrierBuyerRelationService.findAll(query);
   }


   @Operation(summary = "根据卖家id和承运商id查询")
   @PostMapping(value="/findBySellerIdAndCarrierId")
   public List<SellerCarrierBuyerRelationDTO> findBySellerIdAndCarrierId(@RequestParam String sellerId,@RequestParam String carrierId){
      return sellerCarrierBuyerRelationService.findBySellerIdAndCarrierId(sellerId,carrierId);
   }


   @Operation(summary = "根据承运商id和买家名称匹配查询")
   @PostMapping(value="/findByCarrierIdAndBuyerNameLike")
   public List<SellerCarrierBuyerRelationDTO> findByCarrierIdAndBuyerNameLike(@RequestParam String carrierId, @RequestParam(required = false) String sellerName, @RequestParam(required = false) String buyerName){
      return sellerCarrierBuyerRelationService.findByCarrierIdAndBuyerNameLike(carrierId,sellerName,buyerName);
   }

   @Operation(summary = "通过承运商查询出可以代客下单的买家列表")
   @PostMapping(value="/querySellerCarrierBuyerRelationList")
   public PageData<SellerCarrierBuyerRelationListDTO> querySellerCarrierBuyerRelationList(@RequestBody PageQuery<SellerCarrierBuyerRelationQueryDTO> query){
      return sellerCarrierBuyerRelationService.querySellerCarrierBuyerRelationList(query);
   }


}
