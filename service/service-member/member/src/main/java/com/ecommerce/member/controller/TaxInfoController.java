package com.ecommerce.member.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.member.api.dto.TaxInfoDTO;
import com.ecommerce.member.api.dto.TaxInfoDetailDTO;
import com.ecommerce.member.api.dto.TaxInvoiceInfoDTO;
import com.ecommerce.member.exception.DuplicateString;
import com.ecommerce.member.service.ITaxInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;


/**
 * 增票资质相关service
 *
 * <AUTHOR>
*/

@RestController
@Tag(name = "TaxInfo", description = "增票资质相关service")
@RequestMapping("/taxInfo")
@RequiredArgsConstructor
public class TaxInfoController {

   private final ITaxInfoService iTaxInfoService;

   @Operation(summary = "修改增票资质")
   @PostMapping(value="/update")
   public void update(@RequestBody TaxInfoDTO taxInfo,@RequestParam String operator){
      iTaxInfoService.update(taxInfo,operator);

   }


   @Operation(summary = "创建增票资质")
   @PostMapping(value="/create")
   public void create(@RequestBody TaxInfoDTO taxInfo,@RequestParam String operator){
      iTaxInfoService.create(taxInfo,operator);

   }


   @Operation(summary = "创建增票收票人信息")
   @PostMapping(value="/createInvoiceInfo")
   public void createInvoiceInfo(@RequestBody TaxInvoiceInfoDTO taxInvoiceInfoDTO,@RequestParam String operator){
      iTaxInfoService.createInvoiceInfo(taxInvoiceInfoDTO,operator);

   }


   @Operation(summary = "设置默认增票资质")
   @PostMapping(value="/setDefaultById")
   public void setDefaultById(@RequestParam String id,@RequestParam String memberd,@RequestParam String operator){
      iTaxInfoService.setDefaultById(id,memberd,operator);

   }


   @Operation(summary = "根据id删除增票资质")
   @PostMapping(value="/deleteById")
   public void deleteById(@RequestParam String id,@RequestParam(required = false) String operator){
      iTaxInfoService.deleteById(id,operator);

   }


   @Operation(summary = "根据会员id获取默认增票资质（企业员工则根据企业id获取默认收货地址）")
   @PostMapping(value="/getDefaultById")
   public TaxInfoDetailDTO getDefaultById(@RequestParam String id){
      return iTaxInfoService.getDefaultById(id);
   }


   @Operation(summary = "根据id查找增票资质")
   @PostMapping(value="/findById")
   public TaxInfoDetailDTO findById(@RequestParam String id){
      return iTaxInfoService.findById(id);
   }


   @Operation(summary = "查询某会员的所有增票资质")
   @PostMapping(value="/findByMemberId")
   public List<TaxInfoDetailDTO> findByMemberId(@RequestParam(DuplicateString.MEMBER_ID) String memberId){
      return iTaxInfoService.findByMemberId(memberId);
   }

   @Operation(summary = "检查权限")
   @PostMapping(value="/checkTaxInfo")
   public Boolean checkTaxInfo(@RequestParam(DuplicateString.MEMBER_ID) String memberId,@RequestParam String id){
      return iTaxInfoService.checkTaxInfo(memberId,id);
   }

   @Operation(summary = "根据公司名称或纳税人识别码查找公司名称和纳税人识别码")
   @PostMapping(value="/findByCompanyNameOrCode")
   public List<Map<String,String>> findByCompanyNameOrCode(@RequestParam String keyWord){
      return  iTaxInfoService.findByCompanyNameOrCode(keyWord);
   }
}
