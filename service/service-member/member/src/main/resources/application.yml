spring:
  config:
    import: 'optional:configserver:'
  cloud:
    config:
      uri: http://127.0.0.1:8888
      profile: dev
      label: asset-2025
      name: member,db,redis,rabbitmq,hystrix,vip-server,xxl-job,eureka,kafka
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
#mybatis:
mybatis.typeAliasesPackage: com.ecommerce.member.dao
mybatis.mapperScanPackage: com.ecommerce.member.dao
mybatis.mapperLocations: "classpath:/mapper/*.xml"
mybatis.configLocation: "classpath:/mybatis-config.xml"

#mybatis:
#  type-aliases-package: com.ecommerce.member.dao
#  mapper-locations: classpath:/mapper/*.xml
#  mapper-scan-package: com.ecommerce.member.dao
#  configLocation: "classpath:/mybatis-config.xml"
logging:
  pattern:
    # Logging pattern containing traceId and spanId; no longer provided through Sleuth by default
    level: "%5p [${spring.application.name:},%X{traceId:-},%X{spanId:-}]"


mapper:
  resolve-class: com.ecommerce.common.tk.MapperEntityResolve