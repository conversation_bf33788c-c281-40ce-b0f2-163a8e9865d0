<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.member.dao.mapper.AccountChangeHistoryMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.member.dao.vo.AccountChangeHistory">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="account_history_id" jdbcType="VARCHAR" property="accountHistoryId" />
    <result column="account_id" jdbcType="VARCHAR" property="accountId" />
    <result column="real_name" jdbcType="VARCHAR" property="realName" />
    <result column="operatro_name" jdbcType="VARCHAR" property="operatroName" />
    <result column="way" jdbcType="VARCHAR" property="way" />
    <result column="role_id_list" jdbcType="VARCHAR" property="roleIdList" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="is_effect" jdbcType="BIT" property="isEffect" />
    <result column="is_immediate_effect" jdbcType="BIT" property="isImmediateEffect" />
    <result column="effect_time" jdbcType="TIMESTAMP" property="effectTime" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>