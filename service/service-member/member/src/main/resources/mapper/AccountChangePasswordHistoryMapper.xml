<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.member.dao.mapper.AccountChangePasswordHistoryMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.member.dao.vo.AccountChangePasswordHistory">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="mb_account_change_password_history_id" jdbcType="VARCHAR" property="mbAccountChangePasswordId" />
    <result column="account_id" jdbcType="VARCHAR" property="accountId" />
    <result column="password" jdbcType="VARCHAR" property="password" />
    <result column="password_salt" jdbcType="VARCHAR" property="passwordSalt" />
    <result column="encode" jdbcType="VARCHAR" property="encode" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>