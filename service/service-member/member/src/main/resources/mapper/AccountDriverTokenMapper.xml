<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.member.dao.mapper.AccountDriverTokenMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.member.dao.vo.AccountDriverToken">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="account_driver_token_id" jdbcType="VARCHAR" property="accountDriverTokenId" />
    <result column="account_id" jdbcType="VARCHAR" property="accountId" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="driver_token" jdbcType="VARCHAR" property="driverToken" />
    <result column="mobile_os" jdbcType="VARCHAR" property="mobileOs" />
    <result column="login_time" jdbcType="TIMESTAMP" property="loginTime" />
    <result column="login_ip" jdbcType="VARCHAR" property="loginIp" />
    <result column="login_app" jdbcType="VARCHAR" property="loginApp" />
    <result column="login_info_id" jdbcType="VARCHAR" property="loginInfoId" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>