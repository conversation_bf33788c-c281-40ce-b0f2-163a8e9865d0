<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.member.dao.mapper.AccountLoginInfoMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.member.dao.vo.AccountLoginInfo">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="CHAR" property="id" />
    <result column="session_id" jdbcType="VARCHAR" property="sessionId" />
    <result column="account_id" jdbcType="VARCHAR" property="accountId" />
    <result column="login_name" jdbcType="VARCHAR" property="loginName" />
    <result column="login_type" jdbcType="VARCHAR" property="loginType" />
    <result column="login_time" jdbcType="TIMESTAMP" property="loginTime" />
    <result column="login_comment" jdbcType="VARCHAR" property="loginComment" />
    <result column="logout_time" jdbcType="TIMESTAMP" property="logoutTime" />
    <result column="logout_comment" jdbcType="VARCHAR" property="logoutComment" />
    <result column="ip" jdbcType="VARCHAR" property="ip" />
    <result column="mac" jdbcType="VARCHAR" property="mac" />
    <result column="terminal" jdbcType="VARCHAR" property="terminal" />
    <result column="os" jdbcType="VARCHAR" property="os" />
    <result column="os_version" jdbcType="VARCHAR" property="osVersion" />
    <result column="device_brand" jdbcType="VARCHAR" property="deviceBrand" />
    <result column="imei" jdbcType="VARCHAR" property="imei" />
    <result column="driver_token" jdbcType="VARCHAR" property="driverToken" />
    <result column="user_agent" jdbcType="VARCHAR" property="userAgent" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>