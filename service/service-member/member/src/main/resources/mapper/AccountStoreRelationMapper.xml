<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.member.dao.mapper.AccountStoreRelationMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.member.dao.vo.AccountStoreRelation">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="account_store_id" jdbcType="VARCHAR" property="accountStoreId" />
    <result column="account_id" jdbcType="VARCHAR" property="accountId" />
    <result column="member_id" jdbcType="VARCHAR" property="memberId" />
    <result column="account_name" jdbcType="VARCHAR" property="accountName" />
    <result column="member_name" jdbcType="VARCHAR" property="memberName" />
    <result column="account_mobile" jdbcType="VARCHAR" property="accountMobile" />
    <result column="store_id" jdbcType="VARCHAR" property="storeId" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="store_type" jdbcType="VARCHAR" property="storeType" />
    <result column="store_type_name" jdbcType="CHAR" property="storeTypeName" />
    <result column="province_name" jdbcType="VARCHAR" property="provinceName" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="district_name" jdbcType="VARCHAR" property="districtName" />
    <result column="district_code" jdbcType="VARCHAR" property="districtCode" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>