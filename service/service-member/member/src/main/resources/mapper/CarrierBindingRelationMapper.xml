<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.member.dao.mapper.CarrierBindingRelationMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.member.dao.vo.CarrierBindingRelation">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="binding_relation_id" jdbcType="VARCHAR" property="bindingRelationId" />
    <result column="carrier_id" jdbcType="VARCHAR" property="carrierId" />
    <result column="carrier_name" jdbcType="VARCHAR" property="carrierName" />
    <result column="carrier_type" jdbcType="VARCHAR" property="carrierType" />
    <result column="binding_member_id" jdbcType="VARCHAR" property="bindingMemberId" />
    <result column="binding_member_name" jdbcType="VARCHAR" property="bindingMemberName" />
    <result column="binding_status" jdbcType="VARCHAR" property="bindingStatus" />
    <result column="erp_carrier_no" jdbcType="VARCHAR" property="erpCarrierNo" />
    <result column="erp_carrier_name" jdbcType="VARCHAR" property="erpCarrierName" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <!--新增或修改，通过唯一键判断-->
  <insert id="addOrUpdate" parameterType="com.ecommerce.member.dao.vo.CarrierBindingRelation">
    INSERT INTO mb_carrier_binding_relation(binding_relation_id, carrier_id, carrier_name,carrier_type,
        binding_member_id, binding_member_name, binding_status,erp_carrier_no, erp_carrier_name, del_flg, create_user, create_time)
    VALUES
      (
      #{bindingRelationId,jdbcType=VARCHAR},
      #{carrierId,jdbcType=VARCHAR},
      #{carrierName,jdbcType=VARCHAR},
      #{carrierType,jdbcType=VARCHAR},
      #{bindingMemberId,jdbcType=VARCHAR},
      #{bindingMemberName,jdbcType=VARCHAR},
      #{bindingStatus,jdbcType=VARCHAR},
      #{erpCarrierNo,jdbcType=VARCHAR},
      #{erpCarrierName,jdbcType=VARCHAR},
      #{delFlg,jdbcType=TINYINT},
      #{createUser,jdbcType=VARCHAR},
      NOW()
      )
    ON DUPLICATE KEY UPDATE
    carrier_id = values(carrier_id),
    carrier_name = values(carrier_name),
    carrier_type = values(carrier_type),
    binding_member_id = values(binding_member_id),
    binding_member_name = values(binding_member_name),
    binding_status = values(binding_status),
    erp_carrier_no = values(erp_carrier_no),
    erp_carrier_name = values(erp_carrier_name),
    update_user = values(update_user),
    update_time = NOW(),
    del_flg = values(del_flg)
  </insert>


  <select id="queryCarrierBindingList" resultType="com.ecommerce.member.api.dto.carrier.CarrierBindingListDTO">
    SELECT
        binding_relation_id AS bindingRelationId,
        carrier_id AS carrierId,
        binding_member_id AS bindingMemberId,
        binding_member_name AS bindingMemberName,
        erp_carrier_no AS erpCarrierNo,
        erp_carrier_name AS erpCarrierName
    FROM mb_carrier_binding_relation
    WHERE del_flg = 0
    <if test="bindingMemberId != null and bindingMemberId != '' ">
      AND binding_member_id = #{bindingMemberId}
    </if>
    <if test="carrierId != null and carrierId != '' ">
      AND carrier_id = #{carrierId}
    </if>
  </select>

  <!--获取绑定承运商中支持船运的承运商列表-->
  <select id="queryBindCarrierList" resultType="com.ecommerce.member.api.dto.carrier.CarrierBindingListDTO">
    SELECT
        t2.carrier_id AS carrierId,
        t2.carrier_name AS carrierName,
        t2.binding_member_id AS bindingMemberId,
        t2.binding_member_name AS bindingMemberName,
        t2.erp_carrier_no AS erpCarrierNo,
        t2.erp_carrier_name AS erpCarrierName
    FROM mb_member t1
    LEFT JOIN mb_carrier_binding_relation t2 ON t1.member_id = t2.carrier_id
    WHERE t1.del_flg = 0 AND t2.del_flg = 0
    AND t2.binding_member_id = #{memberId}
    AND t2.binding_status = '2'
    AND t1.business_scope IN ('shipTransport','shipAndVehicleTransport')
    <if test="carrierName != null and carrierName != '' ">
      AND carrier_name LIKE concat("%", #{carrierName}, "%")
    </if>
  </select>


</mapper>