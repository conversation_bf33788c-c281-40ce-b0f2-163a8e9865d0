<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.member.dao.mapper.ErpMemberInfoMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.member.dao.vo.ErpMemberInfo">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="member_id" jdbcType="VARCHAR" property="memberId" />
    <result column="erp_carrier_no" jdbcType="VARCHAR" property="erpCarrierNo" />
    <result column="erp_carrier_name" jdbcType="VARCHAR" property="erpCarrierName" />
    <result column="gps_carrier_no" jdbcType="VARCHAR" property="gpsCarrierNo" />
    <result column="taxregister_no" jdbcType="VARCHAR" property="taxregisterNo" />
    <result column="supplier_type" jdbcType="VARCHAR" property="supplierType" />
    <result column="carrier_type" jdbcType="VARCHAR" property="carrierType" />
    <result column="extend1" jdbcType="VARCHAR" property="extend1" />
    <result column="extend2" jdbcType="VARCHAR" property="extend2" />
    <result column="extend3" jdbcType="VARCHAR" property="extend3" />
    <result column="extend4" jdbcType="VARCHAR" property="extend4" />
    <result column="extend5" jdbcType="VARCHAR" property="extend5" />
    <result column="del_flg" jdbcType="INTEGER" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <!-- 新增或修改ERP会员信息-->
  <insert id="addOrUpdateErpMember" parameterType="java.util.List">
    INSERT INTO mb_erp_member_info(id, member_id,erp_carrier_no, erp_carrier_name,
    gps_carrier_no, taxregister_no, supplier_type, carrier_type,
    extend1, extend2, extend3, extend4, extend5, del_flg, create_user, create_time)
    VALUES
    <foreach collection="list" item="item" separator=",">
      (
      #{item.id,jdbcType=VARCHAR},
      #{item.memberId,jdbcType=VARCHAR},
      #{item.erpCarrierNo,jdbcType=VARCHAR},
      #{item.erpCarrierName,jdbcType=VARCHAR},
      #{item.gpsCarrierNo,jdbcType=VARCHAR},
      #{item.taxregisterNo,jdbcType=VARCHAR},
      #{item.supplierType,jdbcType=VARCHAR},
      #{item.carrierType,jdbcType=VARCHAR},
      #{item.extend1,jdbcType=VARCHAR},
      #{item.extend2,jdbcType=VARCHAR},
      #{item.extend3,jdbcType=VARCHAR},
      #{item.extend4,jdbcType=VARCHAR},
      #{item.extend5,jdbcType=VARCHAR},
      #{item.delFlg,jdbcType=INTEGER},
      #{item.createUser,jdbcType=VARCHAR},
      NOW()
      )
    </foreach>
    ON DUPLICATE KEY UPDATE
    member_id = values(member_id),
    erp_carrier_no = values(erp_carrier_no),
    erp_carrier_name = values(erp_carrier_name),
    gps_carrier_no = values(gps_carrier_no),
    taxregister_no = values(taxregister_no),
    supplier_type = values(supplier_type),
    carrier_type = values(carrier_type),
    extend1 = values(extend1),
    extend2 = values(extend2),
    extend3 = values(extend3),
    extend4 = values(extend4),
    extend5 = values(extend5),
    update_user = values(update_user),
    update_time = NOW(),
    del_flg = 0
  </insert>

  <!--通过厂商的会员ID查询erp的承运商信息-->
  <select id="findErpCarrierInfo" resultType="com.ecommerce.member.api.dto.member.ErpMemberInfoDTO">
    SELECT
      erp_carrier_no AS erpCarrierNo,
      erp_carrier_name AS erpCarrierName
    FROM mb_erp_member_info
    WHERE member_id = #{memberId}
    AND del_flg = 0
    <if test="carrierName != null and carrierName != ''">
      AND erp_carrier_name LIKE concat(#{carrierName}, "%")
    </if>
  </select>

  <!--通过厂商ID和ERP承运商编号查询承运商信息-->
  <select id="getErpCarrierInfo" resultType="com.ecommerce.member.api.dto.member.ErpMemberInfoDTO">
    SELECT
    erp_carrier_no AS erpCarrierNo,
    erp_carrier_name AS erpCarrierName
    FROM mb_erp_member_info
    WHERE member_id = #{memberId}
    AND erp_carrier_no = #{erpCarrierNo}
    AND del_flg = 0
    LIMIT 1
  </select>
  
</mapper>