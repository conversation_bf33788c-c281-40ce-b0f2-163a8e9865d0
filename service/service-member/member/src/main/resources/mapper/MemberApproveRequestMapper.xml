<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.member.dao.mapper.MemberApproveRequestMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.member.dao.vo.MemberApproveRequest">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="request_id" jdbcType="VARCHAR" property="requestId" />
    <result column="member_id" jdbcType="VARCHAR" property="memberId" />
    <result column="member_name" jdbcType="VARCHAR" property="memberName" />
    <result column="contact_name" jdbcType="VARCHAR" property="contactName" />
    <result column="contact_phone" jdbcType="VARCHAR" property="contactPhone" />
    <result column="request_type" jdbcType="BIT" property="requestType" />
    <result column="status" jdbcType="BIT" property="status" />
    <result column="change_message" jdbcType="VARCHAR" property="changeMessage" />
    <result column="request_member_id" jdbcType="VARCHAR" property="requestMemberId" />
    <result column="approve_id" jdbcType="VARCHAR" property="approveId" />
    <result column="approve_name" jdbcType="VARCHAR" property="approveName" />
    <result column="request_time" jdbcType="TIMESTAMP" property="requestTime" />
    <result column="approve_time" jdbcType="TIMESTAMP" property="approveTime" />
    <result column="approve_text" jdbcType="VARCHAR" property="approveText" />
    <result column="proxy_status" jdbcType="BIT" property="proxyStatus" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>
</mapper>