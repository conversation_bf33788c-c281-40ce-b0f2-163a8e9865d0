<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.member.dao.mapper.MemberBlacklistMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.member.dao.vo.MemberBlacklist">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="member_id" jdbcType="VARCHAR" property="memberId" />
    <result column="black_member_id" jdbcType="VARCHAR" property="blackMemberId" />
    <result column="black_member_name" jdbcType="VARCHAR" property="blackMemberName" />
    <result column="black_member_phone" jdbcType="VARCHAR" property="blackMemberPhone" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="record_type" jdbcType="VARCHAR" property="recordType" />
    <result column="member_type" jdbcType="VARCHAR" property="memberType" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>