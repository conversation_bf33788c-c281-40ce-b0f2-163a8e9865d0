<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.member.dao.mapper.MemberCaptainMapMapper">
    <resultMap id="BaseResultMap" type="com.ecommerce.member.dao.vo.MemberCaptainMap">
        <id column="captain_map_id" jdbcType="VARCHAR" property="captainMapId" />
        <result column="captain_account_id" jdbcType="VARCHAR" property="captainAccountId" />
        <result column="binding_member_id" jdbcType="VARCHAR" property="bindingMemberId" />
        <result column="binding_member_name" jdbcType="VARCHAR" property="bindingMemberName" />
        <result column="binding_member_type" jdbcType="TINYINT" property="bindingMemberType" />
        <result column="disable_flg" jdbcType="TINYINT" property="disableFlg" />
        <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
        <result column="create_user" jdbcType="VARCHAR" property="createUser" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    </resultMap>

    <update id="updateCaptainMapDisableFlg" parameterType="com.ecommerce.member.api.dto.captain.CaptainUpdateDTO">
        UPDATE
            mb_member_captain_map
        SET
            disable_flg = #{disableFlg},
            update_user = #{updateUser},
            update_time = NOW()
        WHERE
            del_flg = 0
            AND captain_map_id = #{captainMapId}
    </update>

    <update id="deleteCaptainMap" parameterType="com.ecommerce.member.api.dto.captain.CaptainUpdateDTO">
        UPDATE
            mb_member_captain_map
        SET
            del_flg = 1,
            update_user = #{updateUser},
            update_time = NOW()
        WHERE
            del_flg = 0
            AND captain_map_id = #{captainMapId}
    </update>

    <update id="updateCaptainMapDisableFlgByAccountId" parameterType="com.ecommerce.member.api.dto.captain.CaptainUpdateDTO">
        UPDATE
            mb_member_captain_map
        SET
            disable_flg = #{disableFlg},
            update_user = #{updateUser},
            update_time = NOW()
        WHERE
            del_flg = 0
            AND captain_account_id = #{captainAccountId}
    </update>

    <update id="deleteCaptainMapByAccountId" parameterType="com.ecommerce.member.api.dto.captain.CaptainUpdateDTO">
        UPDATE
            mb_member_captain_map
        SET
            del_flg = 1,
            update_user = #{updateUser},
            update_time = NOW()
        WHERE
            del_flg = 0
            AND captain_account_id = #{captainAccountId}
    </update>

    <select id="selectCaptainList" parameterType="com.ecommerce.member.api.dto.captain.CaptainQueryDTO" resultType="com.ecommerce.member.api.dto.captain.CaptainDTO">
        SELECT
            map.captain_map_id AS captainMapId,
            map.captain_account_id AS captainAccountId,
            map.binding_member_type AS bindingMemberType,
            map.disable_flg AS disableFlg,
            account.account_name AS captainAccountName,
            account.real_name AS captainRealName,
            account.mobile AS captainMobile
        FROM mb_member_captain_map AS map
        LEFT JOIN mb_account AS account ON map.captain_account_id = account.account_id
        WHERE
            map.del_flg = 0
            AND map.binding_member_id = #{bindingMemberId}
            AND map.binding_member_type = #{bindingMemberType}
            <if test="captainRealName != null and captainRealName != ''">
                AND account.real_name LIKE CONCAT('%', #{captainRealName}, '%')
            </if>
            <if test="captainMobile != null and captainMobile != ''">
                AND account.mobile LIKE CONCAT('%', #{captainMobile}, '%')
            </if>
            <if test="disableFlg != null">
                AND map.disable_flg = #{disableFlg}
            </if>
            <if test="keyWords != null and keyWords != ''">
                AND ( account.real_name LIKE CONCAT('%', #{keyWords}, '%') OR account.mobile LIKE CONCAT('%', #{keyWords}, '%') )
            </if>
        ORDER BY map.update_time DESC
    </select>

    <select id="selectPlatformCaptainList" parameterType="com.ecommerce.member.api.dto.captain.CaptainQueryDTO" resultType="com.ecommerce.member.api.dto.captain.CaptainDTO">
        SELECT
            map.captain_map_id AS captainMapId,
            map.captain_account_id AS captainAccountId,
            map.binding_member_type AS bindingMemberType,
            map.disable_flg AS disableFlg,
            account.account_name AS captainAccountName,
            account.real_name AS captainRealName,
            account.mobile AS captainMobile
        FROM mb_member_captain_map AS map
        LEFT JOIN mb_account AS account ON map.captain_account_id = account.account_id
        WHERE
            map.del_flg = 0
            AND map.binding_member_type = #{bindingMemberType}
            <if test="captainRealName != null and captainRealName != ''">
                AND account.real_name LIKE CONCAT('%', #{captainRealName}, '%')
            </if>
            <if test="captainMobile != null and captainMobile != ''">
                AND account.mobile LIKE CONCAT('%', #{captainMobile}, '%')
            </if>
            <if test="disableFlg != null">
                AND map.disable_flg = #{disableFlg}
            </if>
            <if test="keyWords != null and keyWords != ''">
                AND ( account.real_name LIKE CONCAT('%', #{keyWords}, '%') OR account.mobile LIKE CONCAT('%', #{keyWords}, '%') )
            </if>
        ORDER BY map.update_time DESC
    </select>

    <select id="findCaptainByCondition" parameterType="com.ecommerce.member.api.dto.captain.CaptainQueryDTO" resultType="com.ecommerce.member.api.dto.captain.CaptainDTO">
        SELECT
            map.captain_map_id AS captainMapId,
            map.captain_account_id AS captainAccountId,
            map.binding_member_type AS bindingMemberType,
            map.disable_flg AS disableFlg,
            account.account_name AS captainAccountName,
            account.real_name AS captainRealName,
            account.mobile AS captainMobile
        FROM mb_member_captain_map AS map
        LEFT JOIN mb_account AS account ON map.captain_account_id = account.account_id
        WHERE
            map.del_flg = 0
            AND map.binding_member_id = #{bindingMemberId}
            AND map.binding_member_type = #{bindingMemberType}
            <if test="captainAccountId != null and captainAccountId != ''">
                AND map.captain_account_id = #{captainAccountId}
            </if>
            <if test="captainRealName != null and captainRealName != ''">
                AND account.real_name LIKE CONCAT('%', #{captainRealName}, '%')
            </if>
            <if test="captainMobile != null and captainMobile != ''">
                AND account.mobile LIKE CONCAT('%', #{captainMobile}, '%')
            </if>
            <if test="disableFlg != null">
                AND map.disable_flg = #{disableFlg}
            </if>
    </select>

    <select id="queryCaptainListByAccountId" parameterType="java.lang.String" resultType="com.ecommerce.member.api.dto.captain.CaptainDTO">
        SELECT *
        FROM mb_member_captain_map
        WHERE captain_account_id = #{accountId}
    </select>

    <select id="queryPlatformCaptainListByAccountIds" parameterType="java.util.Collection" resultType="com.ecommerce.member.api.dto.captain.CaptainDTO">
        SELECT *
        FROM mb_member_captain_map
        <where>
            del_flg = 0
            AND binding_member_type = 5
            <if test="ids != null and ids.size() > 0">
                AND captain_account_id IN
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>


    <select id="findCaptainDetails" parameterType="com.ecommerce.member.api.dto.captain.CaptainQueryDTO"
            resultType="com.ecommerce.member.api.dto.captain.CaptainDetailsDTO">
        SELECT
            t1.captain_map_id AS captainMapId,
            t1.captain_account_id AS captainAccountId,
            t1.binding_member_id AS bindingMemberId,
            t1.disable_flg AS disableFlg,
            t2.account_name AS captainAccountName,
            t2.real_name AS captainRealName,
            t2.mobile AS captainMobile
        FROM mb_member_captain_map t1
        LEFT JOIN mb_account t2 ON t1.captain_account_id = t2.account_id
        WHERE
            t1.del_flg = 0 AND t2.del_flg = 0
        AND t1.captain_account_id = #{captainAccountId}
        LIMIT 1
    </select>


</mapper>