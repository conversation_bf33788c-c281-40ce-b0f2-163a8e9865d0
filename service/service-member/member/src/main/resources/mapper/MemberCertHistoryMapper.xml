<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.member.dao.mapper.MemberCertHistoryMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.member.dao.vo.MemberCertHistory">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="request_id" jdbcType="VARCHAR" property="requestId" />
    <result column="member_id" jdbcType="VARCHAR" property="memberId" />
    <result column="account_id" jdbcType="VARCHAR" property="accountId" />
    <result column="cert_id" jdbcType="VARCHAR" property="certId" />
    <result column="default_cert" jdbcType="BIT" property="defaultCert" />
    <result column="cert_name" jdbcType="VARCHAR" property="certName" />
    <result column="cert_type" jdbcType="VARCHAR" property="certType" />
    <result column="status" jdbcType="BIT" property="status" />
    <result column="real_name" jdbcType="VARCHAR" property="realName" />
    <result column="id_number" jdbcType="VARCHAR" property="idNumber" />
    <result column="ethnicity" jdbcType="VARCHAR" property="ethnicity" />
    <result column="native_place" jdbcType="VARCHAR" property="nativePlace" />
    <result column="sub_cert_type" jdbcType="VARCHAR" property="subCertType" />
    <result column="issuing_authority" jdbcType="VARCHAR" property="issuingAuthority" />
    <result column="contact_name" jdbcType="VARCHAR" property="contactName" />
    <result column="contact_phone" jdbcType="VARCHAR" property="contactPhone" />
    <result column="effective_start_time" jdbcType="TIMESTAMP" property="effectiveStartTime" />
    <result column="effective_time" jdbcType="TIMESTAMP" property="effectiveTime" />
    <result column="need_cert" jdbcType="BIT" property="needCert" />
    <result column="road_license_no" jdbcType="VARCHAR" property="roadLicenseNo" />
    <result column="road_address" jdbcType="VARCHAR" property="roadAddress" />
    <result column="road_business_scope" jdbcType="VARCHAR" property="roadBusinessScope" />
    <result column="road_business_name" jdbcType="VARCHAR" property="roadBusinessName" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="attachment_id" jdbcType="VARCHAR" property="attachmentId" />
  </resultMap>
</mapper>