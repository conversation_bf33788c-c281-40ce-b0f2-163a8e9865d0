<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.member.dao.mapper.MemberCertMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.member.dao.vo.MemberCert">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="cert_id" jdbcType="VARCHAR" property="certId" />
    <result column="member_id" jdbcType="VARCHAR" property="memberId" />
      <result column="account_id" jdbcType="VARCHAR" property="accountId" />
    <result column="default_cert" jdbcType="BIT" property="defaultCert" />
    <result column="attachment_id" jdbcType="VARCHAR" property="attachmentId" />
    <result column="cert_name" jdbcType="VARCHAR" property="certName" />
    <result column="cert_type" jdbcType="VARCHAR" property="certType" />
    <result column="status" jdbcType="BIT" property="status" />
    <result column="real_name" jdbcType="VARCHAR" property="realName" />
    <result column="id_number" jdbcType="VARCHAR" property="idNumber" />
    <result column="ethnicity" jdbcType="VARCHAR" property="ethnicity" />
    <result column="native_place" jdbcType="VARCHAR" property="nativePlace" />
    <result column="contact_name" jdbcType="VARCHAR" property="contactName" />
    <result column="contact_phone" jdbcType="VARCHAR" property="contactPhone" />
    <result column="effective_start_time" jdbcType="TIMESTAMP" property="effectiveStartTime" />
    <result column="effective_time" jdbcType="TIMESTAMP" property="effectiveTime" />
    <result column="need_cert" jdbcType="BIT" property="needCert" />
    <result column="road_license_no" jdbcType="VARCHAR" property="roadLicenseNo" />
    <result column="road_address" jdbcType="VARCHAR" property="roadAddress" />
    <result column="road_business_scope" jdbcType="VARCHAR" property="roadBusinessScope" />
    <result column="road_business_name" jdbcType="VARCHAR" property="roadBusinessName" />
    <result column="sub_cert_type" jdbcType="VARCHAR" property="subCertType" />
    <result column="issuing_authority" jdbcType="VARCHAR" property="issuingAuthority" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <select id="selectMemberCertList" parameterType="com.ecommerce.member.api.dto.member.CertQueryDTO" resultType="com.ecommerce.member.api.dto.member.MemberCertDTO">
    select
    member_id as memberId,
    account_id as accountId,
    cert_id as certId,
    cert_name as certName,
    cert_type as certType,
    status as status,
    attachment_id as attachmentId,
    real_name as realName,
    id_number as idNumber,
    ethnicity as ethnicity,
    native_place as nativePlace,
    contact_name as contactName,
    contact_phone as contactPhone,
    effective_start_time as effectiveStartTime,
    effective_time as effectiveTime,
    need_cert as needCert,
    road_license_no as roadLicenseNo,
    road_address as roadAddress,
    road_business_scope as roadBusinessScope,
    road_business_name as roadBusinessName,
    sub_cert_type as subCertType,
    issuing_authority as issuingAuthority,
    update_time as updateTime
    from mb_member_cert
    where del_flg = 0
    <if test="memberId != null and memberId != ''">
      and member_id = #{memberId}
    </if>
    <if test="accountId != null and accountId != ''">
      and account_id = #{accountId}
    </if>
    <if test="certType != null and certType != ''">
      and cert_type = #{certType}
    </if>
    <if test="status != null and status != ''">
      and status = #{status}
    </if>
    order by update_time desc
  </select>

  <!--根据accountID查询资质-->
  <select id="queryMemberCertList" parameterType="java.util.List" resultType="com.ecommerce.member.api.dto.member.MemberCertDTO">
    SELECT
      member_id as memberId,
      account_id as accountId,
      cert_id as certId,
      cert_name as certName,
      cert_type as certType,
      status as status,
      attachment_id as attachmentId,
      real_name as realName,
      id_number as idNumber,
      ethnicity as ethnicity,
      native_place as nativePlace,
      contact_name as contactName,
      contact_phone as contactPhone,
      effective_start_time as effectiveStartTime,
      effective_time as effectiveTime,
      need_cert as needCert,
      road_license_no as roadLicenseNo,
      road_address as roadAddress,
      road_business_scope as roadBusinessScope,
      road_business_name as roadBusinessName,
      sub_cert_type as subCertType,
      issuing_authority as issuingAuthority,
      update_time as updateTime
    FROM mb_member_cert
    WHERE del_flg = 0
    <if test="certType != null and certType != ''">
      AND cert_type = #{certType}
    </if>
    AND account_id in
    <foreach collection="list" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>


</mapper>