<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.member.dao.mapper.MemberConfigMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.member.dao.vo.MemberConfig">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="member_id" jdbcType="VARCHAR" property="memberId" />
    <result column="status" jdbcType="BIT" property="status" />
    <result column="key_code" jdbcType="VARCHAR" property="keyCode" />
    <result column="value" jdbcType="VARCHAR" property="value" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>