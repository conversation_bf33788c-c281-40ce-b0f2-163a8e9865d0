<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.member.dao.mapper.MemberExtMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.member.dao.vo.MemberExt">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="member_ext_id" jdbcType="VARCHAR" property="memberExtId" />
    <result column="member_id" jdbcType="VARCHAR" property="memberId" />
    <result column="request_id" jdbcType="VARCHAR" property="requestId" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="province_name" jdbcType="VARCHAR" property="provinceName" />
    <result column="goods_category_id" jdbcType="VARCHAR" property="goodsCategoryId" />
    <result column="goods_category_name" jdbcType="VARCHAR" property="goodsCategoryName" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="tenant_name" jdbcType="VARCHAR" property="tenantName" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>