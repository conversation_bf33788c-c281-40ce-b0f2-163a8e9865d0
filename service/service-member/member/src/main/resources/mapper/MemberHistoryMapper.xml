<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.member.dao.mapper.MemberHistoryMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.member.dao.vo.MemberHistory">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="request_id" jdbcType="VARCHAR" property="requestId" />
    <result column="member_id" jdbcType="VARCHAR" property="memberId" />
    <result column="member_name" jdbcType="VARCHAR" property="memberName" />
    <result column="member_short_name" jdbcType="VARCHAR" property="memberShortName" />
    <result column="main_account_id" jdbcType="VARCHAR" property="mainAccountId" />
    <result column="member_code" jdbcType="VARCHAR" property="memberCode" />
    <result column="member_type" jdbcType="BIT" property="memberType" />
    <result column="tenant_id" jdbcType="BIT" property="tenantId" />
    <result column="tenant_level" jdbcType="BIT" property="tenantLevel" />
    <result column="seller_flg" jdbcType="BIT" property="sellerFlg" />
    <result column="carrier_flg" jdbcType="BIT" property="carrierFlg" />
    <result column="business_scope" jdbcType="BIT" property="businessScope" />
    <result column="enterprise_nature" jdbcType="BIT" property="enterpriseNature" />
    <result column="status" jdbcType="BIT" property="status" />
    <result column="cert_real_name" jdbcType="BIT" property="certRealName" />
    <result column="cert_driver" jdbcType="BIT" property="certDriver" />
    <result column="contact_name" jdbcType="VARCHAR" property="contactName" />
    <result column="contact_phone" jdbcType="VARCHAR" property="contactPhone" />
    <result column="country_code" jdbcType="VARCHAR" property="countryCode" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="area_code" jdbcType="VARCHAR" property="areaCode" />
    <result column="street_code" jdbcType="VARCHAR" property="streetCode" />
    <result column="address_detail" jdbcType="VARCHAR" property="addressDetail" />
    <result column="rigist_country_code" jdbcType="VARCHAR" property="rigistCountryCode" />
    <result column="rigist_province_code" jdbcType="VARCHAR" property="rigistProvinceCode" />
    <result column="rigist_city_code" jdbcType="VARCHAR" property="rigistCityCode" />
    <result column="rigist_area_code" jdbcType="VARCHAR" property="rigistAreaCode" />
    <result column="rigist_street_code" jdbcType="VARCHAR" property="rigistStreetCode" />
    <result column="rigist_address_detail" jdbcType="VARCHAR" property="rigistAddressDetail" />
    <result column="zip_code" jdbcType="VARCHAR" property="zipCode" />
    <result column="memo" jdbcType="VARCHAR" property="memo" />
    <result column="legal_name" jdbcType="VARCHAR" property="legalName" />
    <result column="legal_certificate_type" jdbcType="BIT" property="legalCertificateType" />
    <result column="legal_certificate_code" jdbcType="VARCHAR" property="legalCertificateCode" />
    <result column="buyer_type" jdbcType="BIT" property="buyerType" />
    <result column="seller_type" jdbcType="BIT" property="sellerType" />
    <result column="carrier_type" jdbcType="BIT" property="carrierType" />
    <result column="supplier_type" jdbcType="BIT" property="supplierType" />
    <result column="join_type" jdbcType="BIT" property="joinType" />
    <result column="fax" jdbcType="VARCHAR" property="fax" />
    <result column="website" jdbcType="VARCHAR" property="website" />
    <result column="is_syncretic" jdbcType="BIT" property="isSyncretic" />
    <result column="organization_code" jdbcType="VARCHAR" property="organizationCode" />
    <result column="business_license_code" jdbcType="VARCHAR" property="businessLicenseCode" />
    <result column="tax_code" jdbcType="VARCHAR" property="taxCode" />
    <result column="credit_code" jdbcType="VARCHAR" property="creditCode" />
    <result column="register_time" jdbcType="TIMESTAMP" property="registerTime" />
    <result column="register_fund" jdbcType="DECIMAL" property="registerFund" />
    <result column="main_products" jdbcType="VARCHAR" property="mainProducts" />
    <result column="tax_man_type" jdbcType="BIT" property="taxManType" />
    <result column="deposit_name" jdbcType="VARCHAR" property="depositName" />
    <result column="deposit_bank" jdbcType="VARCHAR" property="depositBank" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="description" jdbcType="LONGVARCHAR" property="description" />
  </resultMap>
</mapper>