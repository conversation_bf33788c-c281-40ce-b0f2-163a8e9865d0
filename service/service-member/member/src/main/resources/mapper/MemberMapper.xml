<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.member.dao.mapper.MemberMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.member.dao.vo.Member">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="member_id" jdbcType="VARCHAR" property="memberId" />
    <result column="member_name" jdbcType="VARCHAR" property="memberName" />
    <result column="member_short_name" jdbcType="VARCHAR" property="memberShortName" />
    <result column="main_account_id" jdbcType="VARCHAR" property="mainAccountId" />
    <result column="member_code" jdbcType="VARCHAR" property="memberCode" />
    <result column="member_type" jdbcType="BIT" property="memberType" />

    <result column="tenant_id" jdbcType="BIT" property="tenantId" />
    <result column="tenant_level" jdbcType="BIT" property="tenantLevel" />

    <result column="seller_flg" jdbcType="BIT" property="sellerFlg" />
    <result column="carrier_flg" jdbcType="BIT" property="carrierFlg" />
    <result column="business_scope" jdbcType="BIT" property="businessScope" />
    <result column="enterprise_nature" jdbcType="BIT" property="enterpriseNature" />
    <result column="status" jdbcType="BIT" property="status" />
    <result column="cert_real_name" jdbcType="BIT" property="certRealName" />
    <result column="cert_driver" jdbcType="BIT" property="certDriver" />
    <result column="contact_name" jdbcType="VARCHAR" property="contactName" />
    <result column="contact_phone" jdbcType="VARCHAR" property="contactPhone" />
    <result column="country_code" jdbcType="VARCHAR" property="countryCode" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="area_code" jdbcType="VARCHAR" property="areaCode" />
    <result column="street_code" jdbcType="VARCHAR" property="streetCode" />
    <result column="address_detail" jdbcType="VARCHAR" property="addressDetail" />
    <result column="rigist_country_code" jdbcType="VARCHAR" property="rigistCountryCode" />
    <result column="rigist_province_code" jdbcType="VARCHAR" property="rigistProvinceCode" />
    <result column="rigist_city_code" jdbcType="VARCHAR" property="rigistCityCode" />
    <result column="rigist_area_code" jdbcType="VARCHAR" property="rigistAreaCode" />
    <result column="rigist_street_code" jdbcType="VARCHAR" property="rigistStreetCode" />
    <result column="rigist_address_detail" jdbcType="VARCHAR" property="rigistAddressDetail" />
    <result column="zip_code" jdbcType="VARCHAR" property="zipCode" />
    <result column="memo" jdbcType="VARCHAR" property="memo" />
    <result column="legal_name" jdbcType="VARCHAR" property="legalName" />
    <result column="legal_certificate_type" jdbcType="BIT" property="legalCertificateType" />
    <result column="legal_certificate_code" jdbcType="VARCHAR" property="legalCertificateCode" />
    <result column="buyer_type" jdbcType="BIT" property="buyerType" />
    <result column="seller_type" jdbcType="BIT" property="sellerType" />
    <result column="carrier_type" jdbcType="BIT" property="carrierType" />
    <result column="supplier_type" jdbcType="BIT" property="supplierType" />
    <result column="join_type" jdbcType="BIT" property="joinType" />
    <result column="fax" jdbcType="VARCHAR" property="fax" />
    <result column="website" jdbcType="VARCHAR" property="website" />
    <result column="is_syncretic" jdbcType="BIT" property="isSyncretic" />
    <result column="organization_code" jdbcType="VARCHAR" property="organizationCode" />
    <result column="business_license_code" jdbcType="VARCHAR" property="businessLicenseCode" />
    <result column="tax_code" jdbcType="VARCHAR" property="taxCode" />
    <result column="credit_code" jdbcType="VARCHAR" property="creditCode" />
    <result column="register_time" jdbcType="TIMESTAMP" property="registerTime" />
    <result column="register_fund" jdbcType="DECIMAL" property="registerFund" />
    <result column="main_products" jdbcType="VARCHAR" property="mainProducts" />
    <result column="tax_man_type" jdbcType="BIT" property="taxManType" />
    <result column="deposit_name" jdbcType="VARCHAR" property="depositName" />
    <result column="deposit_bank" jdbcType="VARCHAR" property="depositBank" />
    <result column="urgent_task_time" jdbcType="BIT" property="urgentTaskTime" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="description" jdbcType="LONGVARCHAR" property="description" />
  </resultMap>
  <select id="searchCarrierList" parameterType="com.ecommerce.member.api.dto.member.SearchCarrierDTO"
          resultType="com.ecommerce.member.api.dto.member.MemberCarrierDTO">
    SELECT
      t1.member_id as memberId,
      t1.member_code as memberCode,
      t1.member_name as memberName,
      t1.carrier_type as carrierType,
      t1.contact_name as contactName,
      t1.contact_phone as contactPhone,
      t1.business_scope as businessScope
    FROM
      mb_member as t1
    WHERE
      t1.del_flg = 0 AND
      t1.carrier_flg = 1 AND
      t1.member_name LIKE concat('%', #{keyWord}, '%')
      <if test="carrierTypeList != null and carrierTypeList.size() > 0">
        AND t1.carrier_type IN
        <foreach close=")" collection="carrierTypeList" index="index" item="item" open="(" separator=",">
          #{item}
        </foreach>
      </if>
    ORDER BY
      t1.create_time DESC
  </select>
  <select id="findMemberIdAndNameByKey" parameterType="com.ecommerce.member.api.dto.member.MemberQueryByKeyDTO"
          resultType="com.ecommerce.member.api.dto.member.MemberIdAndNameDTO">
    SELECT
    t1.member_id as memberId,
    t1.member_name as memberName
    FROM
    mb_member as t1
    WHERE
    t1.del_flg = 0
    <if test="memberName != null and memberName != '' ">
      AND t1.member_name LIKE concat('%', #{memberName}, '%')
    </if>
    <!-- 如果不是按名称搜索，则必须是会员名称不为空的 -->
    <if test="memberName == null or memberName == '' ">
      AND length(t1.member_name) > 1
    </if>
    <if test="sellerFlg != null">
      AND t1.seller_flg = ${sellerFlg}
    </if>
    <if test="carrierFlg != null">
      AND t1.carrier_flg = ${carrierFlg}
    </if>
    <if test="supplierFlg != null">
      AND t1.supplier_flg = ${supplierFlg}
    </if>
    <if test="memberType != null and memberType != ''">
      AND t1.member_type = ${memberType}
    </if>
    ORDER BY t1.member_name asc
    <!-- 如果是按名称搜索，则限制返回行数-->
    <if test="memberName != null and memberName != '' ">
      limit 20
    </if>
    <!-- 如果不是按名称搜索，则限制一个最大返回行数 -->
    <if test="memberName == null or memberName == '' ">
      limit 2000
    </if>
  </select>

  <select id="getMemberListByPlatform" parameterType="com.ecommerce.member.api.dto.member.MemberSearchQueryDTO" resultType="com.ecommerce.member.dao.vo.Member">
    SELECT member_id, member_name, member_code FROM mb_member
    WHERE del_flg = 0
    <if test="keyword != null">AND (member_name LIKE CONCAT('%', #{keyword}, '%') OR member_code LIKE CONCAT('%', #{keyword}, '%'))</if>
    ORDER BY member_name ASC
    LIMIT #{num}
  </select>

  <select id="getMemberListBySeller" parameterType="com.ecommerce.member.api.dto.member.MemberSearchQueryDTO" resultType="com.ecommerce.member.dao.vo.Member">
    SELECT m.member_id, m.member_name, m.member_code
    FROM mb_member_relation AS mr, mb_member AS m
    WHERE m.member_id = mr.customer_id
      AND mr.relation_status != 103
      AND mr.member_id = #{memberId}
      <if test="keyword != null">AND (m.member_name LIKE CONCAT('%', #{keyword}, '%') OR m.member_code LIKE CONCAT('%', #{keyword}, '%'))</if>
      AND mr.del_flg = 0
    ORDER BY m.member_name ASC
    LIMIT #{num}
  </select>
  <select id="getMemberListByBuyer" parameterType="String" resultType="com.ecommerce.member.dao.vo.Member">
    SELECT m.member_id, m.member_name, m.member_code
    FROM mb_member_relation AS mr, mb_member AS m
    WHERE m.member_id = mr.member_id
      AND mr.relation_status != 103
      AND mr.customer_id = #{customerId}
      <if test="keyword != null">AND (m.member_name LIKE CONCAT('%', #{keyword}, '%') OR m.member_code LIKE CONCAT('%', #{keyword}, '%'))</if>
    AND mr.del_flg = 0
    ORDER BY m.member_name ASC
    LIMIT #{num}
  </select>

  <!--设置承运商的紧急任务时长-->
  <update id="setUrgentTaskTime" >
    UPDATE mb_member
    SET urgent_task_time = #{urgentTaskTime},
        update_time = NOW(),
        update_user = #{accountId}
    WHERE del_flg = 0
    AND member_id = #{memberId}
  </update>

  <!--获取承运商的紧急任务设置时长-->
  <select id="getUrgentTaskTime" resultType="java.lang.Integer">
    SELECT urgent_task_time FROM mb_member
    WHERE del_flg = 0
    AND member_id = #{memberId}
    LIMIT 1
  </select>

</mapper>
