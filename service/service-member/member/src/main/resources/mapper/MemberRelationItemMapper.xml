<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.member.dao.mapper.MemberRelationItemMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.member.dao.vo.MemberRelationItem">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="relation_item_id" jdbcType="VARCHAR" property="relationItemId" />
    <result column="relation_id" jdbcType="VARCHAR" property="relationId" />
    <result column="erp_member_name" jdbcType="VARCHAR" property="erpMemberName" />
    <result column="mdm_code" jdbcType="VARCHAR" property="mdmCode" />
    <result column="erp_account_name" jdbcType="VARCHAR" property="erpAccountName" />
    <result column="member_status" jdbcType="VARCHAR" property="memberStatus" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <update id="setDelByRelationId">
    update mb_member_relation_item
    set del_flg = 1, update_time = current_timestamp, update_user = #{operatorId}
    where relation_id = #{relationId}
  </update>

</mapper>