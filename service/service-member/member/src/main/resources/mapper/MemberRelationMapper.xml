<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.member.dao.mapper.MemberRelationMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.member.dao.vo.MemberRelation">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="relation_id" jdbcType="VARCHAR" property="relationId" />
    <result column="member_id" jdbcType="VARCHAR" property="memberId" />
    <result column="customer_id" jdbcType="VARCHAR" property="customerId" />
    <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
    <result column="erp_map_type" jdbcType="VARCHAR" property="erpMapType" />
    <result column="customer_phone" jdbcType="VARCHAR" property="customerPhone" />
    <result column="join_type" jdbcType="BIT" property="joinType" />
    <result column="customer_type" jdbcType="BIT" property="customerType" />
    <result column="agent_flag" jdbcType="BIT" property="agentFlag" />
    <result column="member_type" jdbcType="TINYINT" property="memberType" />
    <result column="customer_level" jdbcType="TINYINT" property="customerLevel" />
    <result column="integral" jdbcType="INTEGER" property="integral" />
    <result column="relation_type" jdbcType="TINYINT" property="relationType" />
    <result column="relation_status" jdbcType="BIT" property="relationStatus" />
    <result column="mdm_code" jdbcType="VARCHAR" property="mdmCode" />
    <result column="erp_member_name" jdbcType="VARCHAR" property="erpMemberName" />
    <result column="erp_account_name" jdbcType="VARCHAR" property="erpAccountName" />
    <result column="approval_status" jdbcType="BIT" property="approvalStatus" />
    <result column="first_trade" jdbcType="TIMESTAMP" property="firstTrade" />
    <result column="salesman_id" jdbcType="VARCHAR" property="salesmanId" />
    <result column="salesman_name" jdbcType="VARCHAR" property="salesmanName" />
    <result column="country_code" jdbcType="VARCHAR" property="countryCode" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="area_code" jdbcType="VARCHAR" property="areaCode" />
    <result column="street_code" jdbcType="VARCHAR" property="streetCode" />
    <result column="finance_type" jdbcType="BIT" property="financeType" />
    <result column="is_monthly" jdbcType="TINYINT" property="isMonthly" />
    <result column="quick_place_order" jdbcType="TINYINT" property="quickPlaceOrder" />
    <result column="factory_type" jdbcType="VARCHAR" property="factoryType" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <select id="queryMdmOptions" parameterType="com.ecommerce.member.api.dto.relation.ErpRelationQueryDTO"
          resultType="com.ecommerce.member.api.dto.relation.ErpRelationOption">
    select
        customer_id as memberId,
        mdm_code as mdmCode
    from mb_member_relation
    where del_flg = 0
    and member_id = #{sellerId}
    and customer_id in
    <foreach collection="buyerIdSet" item="mit" index="min" open="(" separator="," close=")">
      #{mit}
    </foreach>
  </select>

  <select id="listCustomerInfoBuyerView" parameterType="com.ecommerce.member.api.dto.relation.ErpCustomCondDTO"
          resultType="com.ecommerce.member.api.dto.relation.ErpMultiCustomerInfoDTO">
    select
        item.relation_item_id as relationItemId,
        relation.member_id as memberId,
        member.member_name as memberName,
        relation.customer_id as customerId,
        relation.customer_code as customerCode,
        relation.customer_name as customerName,
        item.mdm_code as mdmCode,
        item.erp_member_name as erpMemberName,
        item.erp_account_name as erpAccountName
    from mb_member_relation_item as item
    left join mb_member_relation as relation on item.relation_id = relation.relation_id
    left join mb_member as member on member.member_id = relation.member_id
    where item.del_flg = 0 and relation.del_flg = 0 and member.del_flg = 0
    <if test="memberId != null and memberId != ''">
      and member.member_id = #{memberId}
    </if>
    <if test="memberName != null and memberName != ''">
      and member.member_name like concat('%', #{memberName}, '%')
    </if>
    <if test="customerId != null and customerId != ''">
      and relation.customer_id = #{customerId}
    </if>
    <if test="customerName != null and customerName != ''">
      and relation.customer_name like concat('%', #{customerName}, '%')
    </if>
      and relation.mdm_code != ''
    order by relation.create_time desc, item.create_time desc
  </select>


  <select id="findManufacturerByMemberId" parameterType="com.ecommerce.member.api.dto.relation.ErpCustomCondDTO"
          resultType="com.ecommerce.member.api.dto.relation.MemberRelationDTO">
    select
      relation.member_id as memberId,
      member.member_name as memberName,
      relation.customer_id as customerId,
      relation.customer_code as customerCode,
      relation.customer_name as customerName
    from mb_member_relation as relation
    left join mb_member as member on member.member_id = relation.member_id
    where relation.del_flg = 0 and member.del_flg = 0
      and relation.agent_flag = 1
    <if test="memberId != null and memberId != ''">
      and member.member_id = #{memberId}
    </if>
    <if test="customerId != null and customerId != ''">
      and relation.customer_id = #{customerId}
    </if>
    order by relation.create_time desc
  </select>


  <select id="getMdmCodeByMemberIdAndCustomerId" resultType="com.ecommerce.member.api.dto.relation.ErpRelationMdmCodeDTO">
    SELECT
      t1.erp_member_name AS erpMemberName,
      t2.erp_account_name AS erpAccountName,
      t1.mdm_code AS mdmCode,
      t2.mdm_code AS mdmCodeItem,
      t2.member_status AS memberStatus
    FROM mb_member_relation t1
    LEFT JOIN mb_member_relation_item t2 ON t1.relation_id=t2.relation_id
    WHERE t1.del_flg= 0 AND t2.del_flg= 0
    <if test="memberId != null and memberId != ''">
      AND t1.member_id = #{memberId}
    </if>
    <if test="customerId != null and customerId != ''">
      AND t1.customer_id = #{customerId}
    </if>
  </select>
  <select id="getMemberRelationByCondition" resultMap="BaseResultMap">
    SELECT
      t2.*
    FROM
      mb_member_relation_item as t1
    LEFT JOIN
      mb_member_relation as t2 on t1.relation_id=t2.relation_id
    WHERE
      t1.mdm_code=#{mdmCode} and t1.del_flg=0 and t2.member_id=#{memberId}
  </select>

</mapper>
