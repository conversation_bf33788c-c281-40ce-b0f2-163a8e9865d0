<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.member.dao.mapper.MemberStdConfigMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.member.dao.vo.MemberStdConfig">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="std_config_id" jdbcType="VARCHAR" property="stdConfigId" />
    <result column="config_key" jdbcType="VARCHAR" property="key" />
    <result column="value_type" jdbcType="VARCHAR" property="valueType" />
    <result column="config_group" jdbcType="VARCHAR" property="group" />
    <result column="info" jdbcType="VARCHAR" property="info" />
    <result column="can_edit" jdbcType="BIT" property="canEdit" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>