<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.member.dao.mapper.MemberTagMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.member.dao.vo.MemberTag">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="tag_id" jdbcType="VARCHAR" property="tagId" />
    <result column="tag_group_id" jdbcType="VARCHAR" property="tagGroupId" />
    <result column="member_id" jdbcType="VARCHAR" property="memberId" />
    <result column="can_delete" jdbcType="BIT" property="canDelete" />
    <result column="tag_name" jdbcType="VARCHAR" property="tagName" />
    <result column="tag_order" jdbcType="TINYINT" property="tagOrder" />
    <result column="tag_code" jdbcType="VARCHAR" property="tagCode" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>