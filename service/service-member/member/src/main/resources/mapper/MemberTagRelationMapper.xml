<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.member.dao.mapper.MemberTagRelationMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.member.dao.vo.MemberTagRelation">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="relation_id" jdbcType="VARCHAR" property="relationId" />
    <result column="tag_id" jdbcType="VARCHAR" property="tagId" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>

  <select id="findTagsByRelationId" resultType="com.ecommerce.member.api.dto.relation.MemberTagDTO">
    select t.tag_id, t.member_id, t.tag_group_id,
    t.can_delete, t.tag_name, t.tag_order, t.tag_code
    from mb_member_tag t, mb_member_tag_relation tr
    where tr.relation_id=#{relationId} and tr.tag_id=t.tag_id and t.del_flg=0 and tr.del_flg=0
  </select>
</mapper>