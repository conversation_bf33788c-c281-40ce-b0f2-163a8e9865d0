<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.member.dao.mapper.OrgInfoMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.member.dao.vo.OrgInfo">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="org_id" jdbcType="VARCHAR" property="orgId" />
    <result column="org_code" jdbcType="VARCHAR" property="orgCode" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="member_id" jdbcType="VARCHAR" property="memberId" />
    <result column="org_order" jdbcType="INTEGER" property="orgOrder" />
    <result column="parent_id" jdbcType="VARCHAR" property="parentId" />
  </resultMap>
  
  <select id="findByFloorAndEnterpriseId" resultType="com.ecommerce.member.api.dto.organization.OrgInfoDTO">
    select i.org_id, i.org_code, i.org_name, i.member_id, i.org_order, i.parent_id, i.remark, r.is_leaf, r.org_floor
    from mb_org_info i, mb_org_relation r
    where i.member_id=#{memberId} and r.org_floor=#{floor} and i.org_id=r.org_id and r.org_id=r.child_id and i.del_flg=0
  </select>

  <select id="findParentIdByOrgId" resultType="java.lang.String">
    select parent_id from mb_org_info where org_id=#{orgId} and del_flg=0
  </select>
</mapper>