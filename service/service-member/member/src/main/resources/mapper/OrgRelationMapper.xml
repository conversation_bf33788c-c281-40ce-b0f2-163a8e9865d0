<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.member.dao.mapper.OrgRelationMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.member.dao.vo.OrgRelation">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="id" jdbcType="VARCHAR" property="id" />
    <result column="org_id" jdbcType="VARCHAR" property="orgId" />
    <result column="is_leaf" jdbcType="VARCHAR" property="isLeaf" />
    <result column="org_floor" jdbcType="INTEGER" property="orgFloor" />
    <result column="child_id" jdbcType="VARCHAR" property="childId" />
  </resultMap>

  <select id="findChildByOrgId" resultType="com.ecommerce.member.api.dto.organization.OrgInfoDTO">
    select i.org_id, r.is_leaf, r.org_floor, i.org_code, i.member_id, i.org_name, i.org_order, i.parent_id, i.remark
    from mb_org_relation r, mb_org_info i
    where r.org_id=#{orgId} and r.child_id=i.org_id and i.del_flg=0
  </select>
</mapper>