<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.member.dao.mapper.QuickOrderContractMapper">
    <resultMap id="BaseResultMap" type="com.ecommerce.member.dao.vo.QuickOrderContract">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="seller_id" jdbcType="CHAR" property="sellerId"/>
        <result column="buyer_id" jdbcType="CHAR" property="buyerId"/>
        <result column="delivery_type" jdbcType="CHAR" property="deliveryType"/>
        <result column="transport_type" jdbcType="CHAR" property="transportType"/>
        <result column="contract_sequence" jdbcType="CHAR" property="contractSequence"/>
        <result column="sale_region_id" jdbcType="CHAR" property="saleRegionId" />
        <result column="store_id" jdbcType="CHAR" property="storeId" />
        <result column="address_id" jdbcType="CHAR" property="addressId"/>
        <result column="del_flg" jdbcType="TINYINT" property="delFlg"/>
        <result column="create_user" jdbcType="CHAR" property="createUser"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user" jdbcType="CHAR" property="updateUser"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <insert id="insertQuickOrderContractInfo" parameterType="com.ecommerce.member.dao.vo.QuickOrderContract" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO `mb_quick_order_contract`
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sellerId != null">`seller_id`,</if>
            <if test="buyerId != null">`buyer_id`,</if>
            <if test="deliveryType != null">`delivery_type`,</if>
            <if test="transportType != null">`transport_type`,</if>
            <if test="contractSequence != null">`contract_sequence`,</if>
            <if test="saleRegionId != null">`sale_region_id`,</if>
            <if test="storeId != null">`store_id`,</if>
            <if test="addressId != null">`address_id`,</if>
            <if test="delFlg != null">`del_flg`,</if>
            <if test="createUser != null">`create_user`,</if>
            <if test="createTime != null">`create_time`,</if>
            <if test="updateUser != null">`update_user`,</if>
            <if test="updateTime != null">`update_time`,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sellerId != null">#{sellerId},</if>
            <if test="buyerId != null">#{buyerId},</if>
            <if test="deliveryType != null">#{deliveryType},</if>
            <if test="transportType != null">#{transportType},</if>
            <if test="contractSequence != null">#{contractSequence},</if>
            <if test="saleRegionId != null">#{saleRegionId},</if>
            <if test="storeId != null">#{storeId},</if>
            <if test="addressId != null">#{addressId},</if>
            <if test="delFlg != null">#{delFlg},</if>
            <if test="createUser != null">#{createUser},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateUser != null">#{updateUser},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateQuickOrderContractInfoById" parameterType="com.ecommerce.member.dao.vo.QuickOrderContract">
        UPDATE `mb_quick_order_contract`
        <trim prefix="SET" suffixOverrides=",">
            <if test="deliveryType != null">`delivery_type` = #{deliveryType},</if>
            <if test="transportType != null">`transport_type` = #{transportType},</if>
            <if test="contractSequence != null">`contract_sequence` = #{contractSequence},</if>
            <if test="saleRegionId != null">`sale_region_id` = #{saleRegionId},</if>
            <if test="storeId != null">`store_id` = #{storeId},</if>
            <if test="addressId != null">`address_id` = #{addressId},</if>
            <if test="delFlg != null">`del_flg` = #{delFlg},</if>
            <if test="updateUser != null">`update_user` = #{updateUser},</if>
            <if test="updateTime != null">`update_time` = #{updateTime},</if>
        </trim>
        WHERE `id` = #{id}
    </update>

    <select id="getQuickOrderContractInfoBySellerIdAndBuyerId" resultType="com.ecommerce.member.dao.vo.QuickOrderContract">
        SELECT * FROM `mb_quick_order_contract` WHERE `seller_id` = #{sellerId} AND `buyer_id` = #{buyerId} LIMIT 1
    </select>
</mapper>
