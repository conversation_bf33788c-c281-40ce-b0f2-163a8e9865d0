<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.member.dao.mapper.QuickOrderVehicleMapper">
    <resultMap id="BaseResultMap" type="com.ecommerce.member.dao.vo.QuickOrderVehicle">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="seller_id" jdbcType="CHAR" property="sellerId"/>
        <result column="buyer_id" jdbcType="CHAR" property="buyerId"/>
        <result column="vehicle_id" jdbcType="CHAR" property="vehicleId"/>
        <result column="vehicle_number" jdbcType="CHAR" property="vehicleNumber"/>
        <result column="vehicle_type" jdbcType="CHAR" property="vehicleType" />
        <result column="driver_id" jdbcType="CHAR" property="driverId"/>
        <result column="driver_name" jdbcType="CHAR" property="driverName"/>
        <result column="driver_phone" jdbcType="CHAR" property="driverPhone"/>
        <result column="plan_quantity" jdbcType="DECIMAL" property="planQuantity"/>
        <result column="plan_number" jdbcType="INTEGER" property="planNumber"/>
        <result column="del_flg" jdbcType="TINYINT" property="delFlg"/>
        <result column="create_user" jdbcType="CHAR" property="createUser"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user" jdbcType="CHAR" property="updateUser"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <insert id="insertQuickOrderVehicleInfo" parameterType="com.ecommerce.member.dao.vo.QuickOrderVehicle" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO `mb_quick_order_vehicle`
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sellerId != null">`seller_id`,</if>
            <if test="buyerId != null">`buyer_id`,</if>
            <if test="vehicleId != null">`vehicle_id`,</if>
            <if test="vehicleNumber != null">`vehicle_number`,</if>
            <if test="vehicleType != null">`vehicle_type`,</if>
            <if test="driverId != null">`driver_id`,</if>
            <if test="driverName != null">`driver_name`,</if>
            <if test="driverPhone != null">`driver_phone`,</if>
            <if test="planQuantity != null">`plan_quantity`,</if>
            <if test="planNumber != null">`plan_number`,</if>
            <if test="delFlg != null">`del_flg`,</if>
            <if test="createUser != null">`create_user`,</if>
            <if test="createTime != null">`create_time`,</if>
            <if test="updateUser != null">`update_user`,</if>
            <if test="updateTime != null">`update_time`,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sellerId != null">#{sellerId},</if>
            <if test="buyerId != null">#{buyerId},</if>
            <if test="vehicleId != null">#{vehicleId},</if>
            <if test="vehicleNumber != null">#{vehicleNumber},</if>
            <if test="vehicleType != null">#{vehicleType},</if>
            <if test="driverId != null">#{driverId},</if>
            <if test="driverName != null">#{driverName},</if>
            <if test="driverPhone != null">#{driverPhone},</if>
            <if test="planQuantity != null">#{planQuantity},</if>
            <if test="planNumber != null">#{planNumber},</if>
            <if test="delFlg != null">#{delFlg},</if>
            <if test="createUser != null">#{createUser},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateUser != null">#{updateUser},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateQuickOrderVehicleInfoById" parameterType="com.ecommerce.member.dao.vo.QuickOrderVehicle">
        UPDATE `mb_quick_order_vehicle`
        <trim prefix="SET" suffixOverrides=",">
            <if test="vehicleId != null">`vehicle_id` = #{vehicleId},</if>
            <if test="vehicleNumber != null">`vehicle_number` = #{vehicleNumber},</if>
            <if test="vehicleType != null">`vehicle_type` = #{vehicleType},</if>
            <if test="driverId != null">`driver_id` = #{driverId},</if>
            <if test="driverName != null">`driver_name` = #{driverName},</if>
            <if test="driverPhone != null">`driver_phone` = #{driverPhone},</if>
            <if test="planQuantity != null">`plan_quantity` = #{planQuantity},</if>
            <if test="planNumber != null">`plan_number` = #{planNumber},</if>
            <if test="delFlg != null">`del_flg` = #{delFlg},</if>
            <if test="updateUser != null">`update_user` = #{updateUser},</if>
            <if test="updateTime != null">`update_time` = #{updateTime},</if>
        </trim>
        WHERE `id` = #{id}
    </update>

    <select id="getQuickOrderVehicleListBySellerIdAndBuyerId" resultType="com.ecommerce.member.dao.vo.QuickOrderVehicle">
        SELECT * FROM `mb_quick_order_vehicle` WHERE `seller_id` = #{sellerId} AND `buyer_id` = #{buyerId} AND `del_flg` = 0 ORDER BY `id` ASC
    </select>

    <update id="deleteQuickOrderVehicleInfoByIds">
        UPDATE `mb_quick_order_vehicle` SET `del_flg` = 1 WHERE `id` IN<foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">#{id}</foreach>
    </update>
</mapper>
