<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.member.dao.mapper.ReferrerInfoMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.member.dao.vo.ReferrerInfo">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="referrer_id" jdbcType="VARCHAR" property="referrerId" />
    <id column="referrer_code" jdbcType="VARCHAR" property="referrerCode" />
    <result column="referrer_type" jdbcType="INTEGER" property="referrerType" />
    <result column="referrer_name" jdbcType="VARCHAR" property="referrerName" />
    <result column="referrer_mobile" jdbcType="VARCHAR" property="referrerMobile" />
    <result column="referrer_account_id" jdbcType="VARCHAR" property="referrerAccountId" />
    <result column="referrer_carrier_code" jdbcType="VARCHAR" property="referrerCarrierCode" />
    <result column="referrer_store_id" jdbcType="VARCHAR" property="referrerStoreId" />
    <result column="referrer_remark" jdbcType="VARCHAR" property="referrerRemark" />
    <result column="referrer_field1" jdbcType="VARCHAR" property="referrerField1" />
    <result column="referrer_field2" jdbcType="VARCHAR" property="referrerField2" />
    <result column="referrer_field3" jdbcType="VARCHAR" property="referrerField3" />
    <result column="referrer_field4" jdbcType="VARCHAR" property="referrerField4" />
    <result column="referrer_field5" jdbcType="VARCHAR" property="referrerField5" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>