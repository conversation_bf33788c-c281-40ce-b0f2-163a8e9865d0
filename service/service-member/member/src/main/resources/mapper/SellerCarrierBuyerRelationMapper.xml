<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.member.dao.mapper.SellerCarrierBuyerRelationMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.member.dao.vo.SellerCarrierBuyerRelation">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="relation_id" jdbcType="VARCHAR" property="relationId" />
    <result column="seller_id" jdbcType="VARCHAR" property="sellerId" />
    <result column="seller_code" jdbcType="VARCHAR" property="sellerCode" />
    <result column="seller_name" jdbcType="VARCHAR" property="sellerName" />
    <result column="carrier_id" jdbcType="VARCHAR" property="carrierId" />
    <result column="carrier_code" jdbcType="VARCHAR" property="carrierCode" />
    <result column="carrier_name" jdbcType="VARCHAR" property="carrierName" />
    <result column="buyer_id" jdbcType="VARCHAR" property="buyerId" />
    <result column="buyer_code" jdbcType="VARCHAR" property="buyerCode" />
    <result column="buyer_name" jdbcType="VARCHAR" property="buyerName" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <!--通过承运商查询出可以代客下单的列表-->
  <select id="querySellerCarrierBuyerRelationList" parameterType="com.ecommerce.member.api.dto.relation2.SellerCarrierBuyerRelationQueryDTO"
          resultType="com.ecommerce.member.api.dto.relation2.SellerCarrierBuyerRelationListDTO">
      SELECT t1.relation_id AS relationId,
      t1.carrier_id AS memberId,
      t1.carrier_code AS memberCode,
      t1.carrier_name AS memberName,
      t1.seller_id AS sellerId,
      t1.seller_code AS sellerCode,
      t1.seller_name AS sellerName,
      t1.buyer_id AS customerId,
      t1.buyer_code AS customerCode,
      t1.buyer_name AS customerName,
      t2.member_type AS customerType,
      t2.contact_phone AS customerPhone
      FROM mb_seller_carrier_buyer_relation t1
      LEFT JOIN mb_member t2 ON t2.member_id = t1.buyer_id
      WHERE t1.del_flg = 0
      AND t2.del_flg = 0
      AND t1.carrier_id = #{carrierId}
      <if test="buyerName != null and buyerName != ''">
          AND t1.buyer_name LIKE concat('%', #{buyerName}, '%')
      </if>
      ORDER BY t1.create_time DESC
  </select>

</mapper>