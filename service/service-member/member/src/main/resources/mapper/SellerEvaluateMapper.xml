<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.member.dao.mapper.SellerEvaluateMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.member.dao.vo.SellerEvaluate">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="evaluate_id" jdbcType="VARCHAR" property="evaluateId" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="buyer_id" jdbcType="VARCHAR" property="buyerId" />
    <result column="buyer_name" jdbcType="VARCHAR" property="buyerName" />
    <result column="seller_id" jdbcType="VARCHAR" property="sellerId" />
    <result column="seller_name" jdbcType="VARCHAR" property="sellerName" />
    <result column="del_spe_score" jdbcType="DECIMAL" property="delSpeScore" />
    <result column="srv_att_score" jdbcType="DECIMAL" property="srvAttScore" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="img" jdbcType="VARCHAR" property="img" />
    <result column="origin" jdbcType="BIT" property="origin" />
    <result column="is_auto" jdbcType="BIT" property="isAuto" />
    <result column="is_anonymous" jdbcType="BIT" property="isAnonymous" />
    <result column="is_show" jdbcType="BIT" property="isShow" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>
</mapper>