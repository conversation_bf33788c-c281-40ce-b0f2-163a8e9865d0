<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.member.dao.mapper.TaxInfoMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.member.dao.vo.TaxInfo">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="member_id" jdbcType="VARCHAR" property="memberId" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="taxpayer_identification_code" jdbcType="VARCHAR" property="taxpayerIdentificationCode" />
    <result column="registered_address" jdbcType="VARCHAR" property="registeredAddress" />
    <result column="registered_phone" jdbcType="VARCHAR" property="registeredPhone" />
    <result column="bank_name" jdbcType="VARCHAR" property="bankName" />
    <result column="bank_account" jdbcType="VARCHAR" property="bankAccount" />
    <result column="default_flg" jdbcType="TINYINT" property="defaultFlg" />
    <result column="invoice_collector" jdbcType="VARCHAR" property="invoiceCollector" />
    <result column="invoice_call" jdbcType="VARCHAR" property="invoiceCall" />
    <result column="invoice_province" jdbcType="VARCHAR" property="invoiceProvince" />
    <result column="invoice_address" jdbcType="VARCHAR" property="invoiceAddress" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>