<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.member.dao.mapper.TenantInfoMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.member.dao.vo.TenantInfo">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="tenant_level" jdbcType="INTEGER" property="tenantLevel" />
    <result column="tenant_name" jdbcType="VARCHAR" property="tenantName" />
    <result column="tenant_name_detail" jdbcType="VARCHAR" property="tenantNameDetail" />
    <result column="parent_id" jdbcType="VARCHAR" property="parentId" />
    <result column="parent_ids" jdbcType="VARCHAR" property="parentIds" />
    <result column="weight" jdbcType="INTEGER" property="weight" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>