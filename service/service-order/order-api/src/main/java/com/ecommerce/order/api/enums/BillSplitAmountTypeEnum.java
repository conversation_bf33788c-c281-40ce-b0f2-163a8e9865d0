package com.ecommerce.order.api.enums;

import lombok.Getter;

@Getter
public enum BillSplitAmountTypeEnum {
    LOGISTIC_AMOUNT("0", "物流款分账"),
    GOODS_AMOUNT("1", "货款分账"),
    OTHER_AMOUNT("2","其他分账");

    /**
     * 枚举值
     */
    private final String code;

    /**
     * 枚举描述
     */
    private final String message;

    private BillSplitAmountTypeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public static BillSplitAmountTypeEnum getByCode(String code) {
        for (BillSplitAmountTypeEnum _enum : values()) {
            if (_enum.getCode().equals(code)) {
                return _enum;
            }
        }
        return null;
    }
}
