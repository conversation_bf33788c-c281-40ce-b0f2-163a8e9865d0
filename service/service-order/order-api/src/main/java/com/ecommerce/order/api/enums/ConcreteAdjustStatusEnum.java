package com.ecommerce.order.api.enums;

import lombok.Getter;
import com.ecommerce.common.utils.CsStringUtils;

/**
 *
 * <AUTHOR>
 */
@Getter
public enum ConcreteAdjustStatusEnum {

    WAIT_ADJUST("wait_adjust", "未回调"),

    ADJUSTING("adjusting", "回调中"),

    ADJUST_SUCCESS("adjust_success", "回调成功"),

    ADJUST_FAILED("adjust_failed","回调失败");

    /**
     * 枚举值
     */
    private final String code;

    /**
     * 枚举描述
     */
    private final String message;

    ConcreteAdjustStatusEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public static ConcreteAdjustStatusEnum valueOfCode(String code) {
        for (ConcreteAdjustStatusEnum _enum : values()) {
            if (CsStringUtils.equals(_enum.getCode(), code)) {
                return _enum;
            }
        }
        return null;
    }
}
