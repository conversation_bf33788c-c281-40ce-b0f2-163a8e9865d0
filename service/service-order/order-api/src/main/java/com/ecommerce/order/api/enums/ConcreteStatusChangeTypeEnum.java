package com.ecommerce.order.api.enums;

import lombok.Getter;
import com.ecommerce.common.utils.CsStringUtils;

/**
 *
 * <AUTHOR>
 */
@Getter
public enum ConcreteStatusChangeTypeEnum {

    ADJUST_PLAN("adjust_plan", "调价计划"),

    ADJUST_GOODS("adjust_goods", "调价商品"),

    ADJUST_ORDER("adjust_order", "调价订单");

    /**
     * 枚举值
     */
    private final String code;

    /**
     * 枚举描述
     */
    private final String message;

    ConcreteStatusChangeTypeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public static ConcreteStatusChangeTypeEnum valueOfCode(String code) {
        for (ConcreteStatusChangeTypeEnum _enum : values()) {
            if (CsStringUtils.equals(_enum.getCode(), code)) {
                return _enum;
            }
        }
        return null;
    }
}
