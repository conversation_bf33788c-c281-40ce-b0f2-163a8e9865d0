package com.ecommerce.order.api.enums;

/**
 *
 * <AUTHOR>
 */
public enum MemberOrderConfigEnum {

    CONTRACT_ADJUST_AUTO_CLOSE("contract_adjust_auto_close", "合同调价订单自动关闭"),

    CROSS_MONTH_AUTO_CLOSE("cross_month_auto_close", "跨月订单自动关闭");

    /**
     * 描述
     */
    private final String code;

    /**
     * 编码
     */
    private final String desc;

    MemberOrderConfigEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static MemberOrderConfigEnum valueOfCode(String code) {
        MemberOrderConfigEnum[] enums = values();
        for (MemberOrderConfigEnum item : enums) {
            if (item.code.equals(code)) {
                return item;
            }
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}
