package com.ecommerce.order.api.enums;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 订单erp子状态
 /**
 * OrderStatusEnum
 *
 * <AUTHOR>
 */
public enum OrderExceptionStatusEnum {
	HANDLE_ERROR("handle_error", "处理失败"),
    HANDLE_SUCCESS("handle_success", "处理成功"),
    HANDLE_WAITING("handle_waiting", "待处理"),
	;

    /** 枚举值 */
    private final String code;

    /** 枚举描述 */
    private final String message;


    /**
     * 构造一个<code>ResourceStatusEnum</code>枚举对象
     *
     * @param code
     * @param message
     */
    private OrderExceptionStatusEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * @return Returns the code.
     */
    public String getCode() {
        return code;
    }

    /**
     * @return Returns the message.
     */
    public String getMessage() {
        return message;
    }

    /**
     * @return Returns the code.
     */
    public String code() {
        return code;
    }

    /**
     * @return Returns the message.
     */
    public String message() {
        return message;
    }

    /**
     * 通过枚举<code>code</code>获得枚举
     *
     * @param code
     * @return ResourceStatusEnum
     */
    public static OrderExceptionStatusEnum getByCode(String code) {
        for (OrderExceptionStatusEnum _enum : values()) {
            if (_enum.getCode().equals(code)) {
                return _enum;
            }
        }
        return null;
    }

    /**
     * 获取全部枚举
     *
     * @return List<ResourceStatusEnum>
     */
    public List<OrderExceptionStatusEnum> getAllEnum() {
        return new ArrayList<>(Arrays.asList(values()));
    }

    /**
     * 获取全部枚举值
     *
     * @return List<String>
     */
    public List<String> getAllEnumCode() {
        List<String> list = new ArrayList<>();
        for (OrderExceptionStatusEnum _enum : values()) {
            list.add(_enum.code());
        }
        return list;
    }
}
