package com.ecommerce.order.api.enums;

import lombok.Getter;

@Getter
public enum OrderInvoiceInstitution {
	ONE("100", "一票制"),
	SECOND("200", "两票制");

	/** 枚举值 */
	private final String code;

	/** 枚举描述 */
	private final String message;

	private OrderInvoiceInstitution(String code, String message) {
		this.code = code;
		this.message = message;
	}

	public static OrderInvoiceInstitution getByCode(String code) {
		for (OrderInvoiceInstitution _enum : values()) {
			if (_enum.getCode().equals(code)) {
				return _enum;
			}
		}
		return null;
	}
}
