package com.ecommerce.order.api.enums;

public enum OverSellValidateResultEnum {
    EXIST_AND_SUCCESS("existAndSuccess","超发存在且支付成功"),
    EXIST_BUT_ERROR("existButError","超发存在但支付失败"),
    NOT_EXIST("notExist","超发不存在");
    private final String code;
    private final String message;
    OverSellValidateResultEnum(String code, String message){
        this.code=code;
        this.message=message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
    public static OverSellValidateResultEnum getByCode(String code) {
        for (OverSellValidateResultEnum _enum : values()) {
            if (_enum.getCode().equals(code)) {
                return _enum;
            }
        }
        return null;
    }
}
