package com.ecommerce.order.api.enums;

/**
 *  一级订单映射状态
 *
 * <AUTHOR>
 */
public enum PrimaryMapStatusEnum {

    PROCESSING("processing", "处理中"),

    SUBMIT_FAIL("submit_fail", "提交失败"),

    SUBMIT_SUCCESS("submit_success", "提交成功"),

    PAY_SUCCESS("pay_success", "处理成功");

    /**
     * 描述
     */
    private final String code;

    /**
     * 编码
     */
    private final String desc;

    PrimaryMapStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PrimaryMapStatusEnum valueOfCode(String code) {
        PrimaryMapStatusEnum[] enums = values();
        for (PrimaryMapStatusEnum item : enums) {
            if (item.code.equals(code)) {
                return item;
            }
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}
