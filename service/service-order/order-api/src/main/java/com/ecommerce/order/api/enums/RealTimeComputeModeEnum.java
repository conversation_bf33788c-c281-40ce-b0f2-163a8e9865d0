package com.ecommerce.order.api.enums;

/**
 *
 * <AUTHOR>
 */
public enum RealTimeComputeModeEnum {

    SEND_QUANTITY("send_quantity", ""),

    PLATFORM_SIGN("0200", "平台签约"),

    PLATFORM_SELF("0300", "平台自有");

    /**
     * 描述
     */
    private final String code;

    /**
     * 编码
     */
    private final String desc;

    RealTimeComputeModeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static RealTimeComputeModeEnum valueOfCode(String code) {
        RealTimeComputeModeEnum[] enums = values();
        for (RealTimeComputeModeEnum item : enums) {
            if (item.code.equals(code)) {
                return item;
            }
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}
