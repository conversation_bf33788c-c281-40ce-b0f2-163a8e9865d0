package com.ecommerce.order.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.order.api.dto.auction.*;
import com.github.pagehelper.PageInfo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;

@FeignClient(name = "service-order")
public interface IAuctionService {

    /**
     * 1.查询竞拍列表
     *
     * @param param
     * @return
     */
    @PostMapping(value = "auction/getAuctionList")
    ItemResult<PageInfo<AuctionListBuyerResponseDTO>> getAuctionList(@RequestBody AuctionSessionRequestDTO param);


    /**
     * 2.创建竞拍
     *
     * @param param
     * @return
     */
    @PostMapping(value = "auction/createAuction")
    ItemResult<String> createAuction(@RequestBody AuctionDetailDTO param, @RequestParam String operator);

    /**
     * 3.编辑竞拍
     *
     * @param param
     * @return
     */
    @PostMapping(value = "auction/editAuction")
    ItemResult<String> editAuction(@RequestBody AuctionDetailDTO param, @RequestParam  String operator);

    /**
     * 4.提交
     *
     * @param auctionNo
     * @return
     */
    @PostMapping(value = "auction/submitAuction")
    ItemResult<String> submitAuction(@RequestParam String operatorId, @RequestParam String operatorName, @RequestParam("auctionNo") String auctionNo);


    /**
     * 5.创建竞拍 并提交
     *
     * @param param
     * @return
     */
    @PostMapping(value = "auction/createAuctionWithSubmit")
    ItemResult<String> createAuctionWithSubmit(@RequestParam String operatorId, @RequestBody AuctionDetailDTO param);

    /**
     * 6。撤回竞拍
     *
     * @param auctionNo
     * @return
     */
    @PostMapping(value = "auction/withdrawAuction")
    ItemResult<String> withdrawAuction(@RequestParam String operatorId, @RequestParam String operatorName, @RequestParam("auctionNo") String auctionNo);


    /**
     * 7.审核竞拍
     *
     * @param auctionNo
     * @param action:1-通过 0-驳回
     * @return
     */
    @PostMapping(value = "auction/approveAuction")
    ItemResult<String> approveAuction(@RequestParam String operatorId, @RequestParam String operatorName, @RequestParam("auctionNo") String auctionNo, @RequestParam("action") Integer action);

    /**
     * 8.查看竞拍详细
     *
     * @param auctionNo
     * @return
     */
    @GetMapping(value = "auction/getAuctionDetail")
    ItemResult<AuctionDetailDTO> getAuctionDetail(@RequestParam("auctionNo") String auctionNo);

    @GetMapping(value = "auction/getAuctionDetailForBid")
    ItemResult<AuctionDetailForBidDTO> getAuctionDetailForBid(@RequestParam("auctionNo") String auctionNo, @RequestParam(value = "buyerId", required = false) String buyerId);
    /**
     * 9.报名
     *
     * @param param
     * @return
     */
    @PostMapping(value = "auction/registerAuction")
    ItemResult<String> registerAuction(@RequestParam String operatorId,  @RequestParam String operatorName, @RequestBody RegisterAuctionRequestDTO param);

    /**
     * 10.竞价
     *
     * @param auctionNo 竞拍场次
     * @return
     */
    @PostMapping("auction/bidAuction")
    ItemResult<String> bidAuction(@RequestParam String operatorId, @RequestParam String operatorName, @RequestParam("auctionNo") String auctionNo, @RequestParam("bidCount") BigDecimal bidCount);

    /***
     * <AUTHOR>
     * @description 11.关闭竞拍场次
     * @date 2024/12/5 18:13
     * @param auctionNo:
     * @return: com.ecommerce.common.result.ItemResult<java.lang.String>
     */

    @PostMapping("auction/closeAuction")
    ItemResult<String> closeAuction(@RequestParam String operatorId, @RequestParam String operatorName, @RequestParam("auctionNo") String auctionNo);

    /***
     * <AUTHOR>
     * @description 12.设置中标人
     * @date 2024/12/5 18:15
     * @return: com.ecommerce.common.result.ItemResult<java.lang.String>
     */

    @PostMapping(value = "auction/setWinningBidder")
    ItemResult<String> setWinningBidder(@RequestParam String operatorId, @RequestParam String operatorName, @RequestBody SetAuctionWinnerRequestDTO param);
}

