package com.ecommerce.order.api.service;

import com.ecommerce.order.api.dto.BuyerSelectInfoDTO;

import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

@FeignClient(name = "service-order")
public interface IBuyerSelectInfoService {

	@Operation(summary = "定制化加工选项")
	@PostMapping( path = "/buyerSelectInfo/addBuyerSelectInfoFromOrder", consumes = "application/json")
	public void addBuyerSelectInfoFromOrder(@RequestBody Map<String, List<BuyerSelectInfoDTO>> arg0,
                                            @RequestParam String arg1, @RequestParam String arg2, @RequestParam String arg3);

	@Operation(summary = "买家添加到购物车")
	@PostMapping(value = "/buyerSelectInfo/addBuyerSelectInfoFromCart")
	public void addBuyerSelectInfoFromCart(@RequestBody List<BuyerSelectInfoDTO> arg0,
                                           @RequestParam String arg1, @RequestParam String arg2, @RequestParam String arg3);

	@Operation(summary = "更新购物车买家选项")
	@PostMapping(value = "/buyerSelectInfo/updateBuyerSelectInfoForCart")
	public String updateBuyerSelectInfoForCart(@RequestBody List<BuyerSelectInfoDTO> arg0,
                                               @RequestParam String arg1, @RequestParam String arg2, @RequestParam String arg3);

	@Operation(summary = "从购物车移除买家选项")
	@PostMapping(value = "/buyerSelectInfo/removeBuyerSelectInfoFromCart")
	public void removeBuyerSelectInfoFromCart(@RequestParam String arg0, @RequestParam String arg1);

	@Operation(summary = "获取买家选项，在购物车展示")
	@PostMapping(value = "/buyerSelectInfo/findBuyerSelectInfoForCart")
	public List<BuyerSelectInfoDTO> findBuyerSelectInfoForCart(@RequestParam String arg0,
                                                               @RequestParam String arg1);

	@Operation(summary = "获取买家选项，在订单详情展示")
	@PostMapping(value = "/buyerSelectInfo/findBuyerSelectInfoForOrder")
	public List<BuyerSelectInfoDTO> findBuyerSelectInfoForOrder(@RequestParam String arg0,
                                                                @RequestParam String arg1);

}
