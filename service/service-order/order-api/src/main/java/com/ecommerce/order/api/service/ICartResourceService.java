package com.ecommerce.order.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.order.api.dto.CartResourceDTO;
import com.ecommerce.order.api.dto.OrderCartResCheckDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;
import java.util.List;

/**
 * 购物车管理
*/

@FeignClient(name ="service-order")
public interface ICartResourceService {


   @PostMapping( path = "/cartResource/checkCartResource", consumes = "application/json")
   ItemResult<String> checkCartResource(@RequestBody List<OrderCartResCheckDTO> arg0);


   @PostMapping( path = "/cartResource/updateQuantity", consumes = "application/json")
   ItemResult<String> updateQuantity(@RequestParam String arg0, @RequestParam BigDecimal arg1, @RequestParam String arg2);


   @PostMapping( path = "/cartResource/moveCart", consumes = "application/json")
   ItemResult<String> moveCart(@RequestBody List<String> arg0, @RequestParam String arg1);


   @PostMapping( path = "/cartResource/addCart", consumes = "application/json")
   ItemResult<CartResourceDTO> addCart(@RequestBody CartResourceDTO arg0, @RequestParam String arg1);


   @PostMapping( path = "/cartResource/emptyCart", consumes = "application/json")
   ItemResult<String> emptyCart(@RequestParam String arg0);


   @PostMapping( path = "/cartResource/queryCartDetail", consumes = "application/json")
   ItemResult<CartResourceDTO> queryCartDetail(@RequestParam String arg0);


   @PostMapping( path = "/cartResource/queryCart", consumes = "application/json")
   ItemResult<List<CartResourceDTO>> queryCart(@RequestParam String arg0, @RequestParam String arg1);


   @PostMapping( path = "/cartResource/updateCart", consumes = "application/json")
   ItemResult<String> updateCart(@RequestBody CartResourceDTO arg0, @RequestParam String arg1);



}
