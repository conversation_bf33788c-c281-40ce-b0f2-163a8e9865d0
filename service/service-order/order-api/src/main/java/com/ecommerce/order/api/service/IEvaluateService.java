package com.ecommerce.order.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.order.api.dto.EvaluateSellerScoreResDTO;
import com.ecommerce.order.api.dto.EvaluateTotalReqDTO;
import com.ecommerce.order.api.dto.EvaluateTotalResDTO;
import com.ecommerce.order.api.dto.GoodsEvaluateGmbResDTO;
import com.ecommerce.order.api.dto.GoodsEvaluateListReqDTO;
import com.ecommerce.order.api.dto.GoodsEvaluateListResDTO;
import com.ecommerce.order.api.dto.OrderEvaluateDetailInfoReqDTO;
import com.ecommerce.order.api.dto.OrderEvaluateDetailInfoResDTO;
import com.ecommerce.order.api.dto.OrderEvaluateListReqDTO;
import com.ecommerce.order.api.dto.OrderEvaluateListResDTO;
import com.ecommerce.order.api.dto.OrderEvaluatePostReqDTO;
import com.github.pagehelper.PageInfo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 评价相关接口类
*/

@FeignClient(name ="service-order")
public interface IEvaluateService  {

   @PostMapping(path = "/evaluate/pageEvaluate", consumes = "application/json")
   ItemResult<PageInfo<OrderEvaluateListResDTO>> pageEvaluate(@RequestBody OrderEvaluateListReqDTO req);

   @PostMapping(path = "/evaluate/postOrderEvaluate", consumes = "application/json")
   ItemResult<Boolean> postOrderEvaluate(@RequestBody OrderEvaluatePostReqDTO req);

   @PostMapping(path = "/evaluate/getGoodsEvaluateList", consumes = "application/json")
   ItemResult<PageInfo<GoodsEvaluateListResDTO>> getGoodsEvaluateList(@RequestBody GoodsEvaluateListReqDTO req);

   @PostMapping(path = "/evaluate/getOrderEvaluateInfo", consumes = "application/json")
   ItemResult<OrderEvaluateDetailInfoResDTO> getOrderEvaluateInfo(@RequestBody OrderEvaluateDetailInfoReqDTO req);

   @PostMapping(path = "/evaluate/getSellerCompreScoreInfo", consumes = "application/json")
   ItemResult<EvaluateSellerScoreResDTO> getSellerCompreScoreInfo(@RequestParam("sellerId") String sellerId);

   @PostMapping(path = "/evaluate/getGoodsEvaluateCompreScoreInfo", consumes = "application/json")
   ItemResult<GoodsEvaluateGmbResDTO> getGoodsEvaluateCompreScoreInfo(@RequestParam("goodsId") String goodsId);

   @PostMapping(path = "/evaluate/getEvaluateTotalNum", consumes = "application/json")
   ItemResult<EvaluateTotalResDTO> getEvaluateTotalNum(@RequestBody EvaluateTotalReqDTO req);

}
