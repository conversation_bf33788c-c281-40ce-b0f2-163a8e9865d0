package com.ecommerce.order.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.order.api.dto.MonitorJudgeCondDTO;
import com.ecommerce.order.api.dto.MonitorJudgeResDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * :监控判断服务
 *
 * <AUTHOR>
*/

@FeignClient(name ="service-order")
public interface IMonitorJudgeService {


   @PostMapping( path = "/monitorJudge/monitorJudge", consumes = "application/json")
   public ItemResult<MonitorJudgeResDTO> monitorJudge(@RequestBody MonitorJudgeCondDTO monitorJudgeCondDTO);



}
