package com.ecommerce.order.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.order.api.dto.OrderAdjustDTO;
import com.ecommerce.order.api.dto.PageOrderAdjustDTO;
import com.github.pagehelper.PageInfo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;


@FeignClient(name ="service-order")
public interface IOrderAdjustService {


   @PostMapping( path = "/orderAdjust/doCreateOrderAdjust", consumes = "application/json")
   ItemResult<Boolean> doCreateOrderAdjust(@RequestBody OrderAdjustDTO orderAdjustDTO);


   @PostMapping( path = "/orderAdjust/pageOrderAdjustList", consumes = "application/json")
   ItemResult<PageInfo<OrderAdjustDTO>> pageOrderAdjustList(@RequestBody PageOrderAdjustDTO pageOrderAdjustDTO);


   @PostMapping( path = "/orderAdjust/confirmOrderAdjust", consumes = "application/json")
   ItemResult<Boolean> confirmOrderAdjust(@RequestParam String orderId,@RequestParam String operator);

   @PostMapping( path = "/orderAdjust/countAdjustAmount", consumes = "application/json")
   ItemResult<BigDecimal> countAdjustAmount(@RequestParam String orderId);



}
