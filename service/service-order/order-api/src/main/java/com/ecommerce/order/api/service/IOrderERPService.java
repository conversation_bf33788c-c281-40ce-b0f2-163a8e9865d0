package com.ecommerce.order.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.order.api.dto.OversoldDTO;
import com.ecommerce.order.api.dto.erp.OrderCloseERPCallback;
import com.ecommerce.order.api.dto.erp.OrderCompleteERPCallback;
import com.ecommerce.order.api.dto.erp.OrderCreateERPCallback;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name ="service-order")
public interface IOrderERPService {

    @PostMapping(path = "/ordererp/orderCreateERPCallback",consumes = "application/json")
    ItemResult<String> orderCreateERPCallback(@RequestBody OrderCreateERPCallback orderCreateERPCallback);

    @PostMapping(path = "/ordererp/orderCloseERPCallback",consumes = "application/json")
    ItemResult<String> orderCloseERPCallback(@RequestBody OrderCloseERPCallback orderCloseERPCallback);

    @PostMapping(path = "/orderCompleteERPCallback",consumes = "application/json")
    ItemResult<String> orderCompleteERPCallback(@RequestBody OrderCompleteERPCallback orderCompleteERPCallback);

    @PostMapping(path = "/ordererp/doERPCloseOrder",consumes = "application/json")
    ItemResult<String> doERPCloseOrder(@RequestBody OrderCloseERPCallback orderCloseERPCallback);

    @PostMapping(path = "/ordererp/doERPCompleteOrder",consumes = "application/json")
    ItemResult<String> doERPCompleteOrder(@RequestBody OrderCompleteERPCallback orderCompleteERPCallback);

    @PostMapping(path = "/ordererp/overSellConfirm",consumes = "application/json")
    ItemResult<String> overSellConfirm(@RequestBody OversoldDTO overSellDTO);
}
