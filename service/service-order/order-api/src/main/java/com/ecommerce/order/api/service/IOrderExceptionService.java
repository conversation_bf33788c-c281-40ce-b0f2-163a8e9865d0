package com.ecommerce.order.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.order.api.dto.OrderExceptionDTO;
import com.ecommerce.order.api.dto.OrderExceptionQueryDTO;
import com.github.pagehelper.PageInfo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name ="service-order")
public interface IOrderExceptionService {
    @PostMapping( path = "/exception/pageOrderException", consumes = "application/json")
    public ItemResult<PageInfo<OrderExceptionDTO>> pageOrderException(@RequestBody OrderExceptionQueryDTO arg0,
                                                                  @RequestParam Integer arg1, @RequestParam Integer arg2);
    @PostMapping(path = "/exception/reHandleException",consumes = "application/json")
    public ItemResult<String> reHandleException(@RequestParam String id);
}
