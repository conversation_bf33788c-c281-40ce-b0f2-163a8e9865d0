package com.ecommerce.order.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.order.api.dto.export.OrderExportCondDTO;
import com.ecommerce.order.api.dto.export.OrderExportDTO;
import com.ecommerce.order.api.dto.export.OrderItemOption;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Set;

/**
 * :: 列表导出服务
 *
 * <AUTHOR>
*/

@FeignClient(name ="service-order")
public interface IOrderExportService {


   @PostMapping( path = "/orderExport/exportOrderList", consumes = "application/json")
   ItemResult<List<OrderExportDTO>> exportOrderList(@RequestBody OrderExportCondDTO orderExportCondDTO);

   @PostMapping( path = "/orderExport/queryOrderItemOptionsByItemIdSet", consumes = "application/json")
   ItemResult<List<OrderItemOption>> queryOrderItemOptionsByItemIdSet(@RequestBody Set<String> orderItemIdSet);

}
