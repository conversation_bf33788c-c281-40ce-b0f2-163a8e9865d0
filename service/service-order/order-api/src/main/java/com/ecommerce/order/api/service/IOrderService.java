package com.ecommerce.order.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.order.api.dto.ApplyOrderInvoiceRequestDTO;
import com.ecommerce.order.api.dto.ApplyOrderInvoiceResponseDTO;
import com.ecommerce.order.api.dto.ConfirmOrderInvoiceReqDTO;
import com.ecommerce.order.api.dto.ERPAddressDTO;
import com.ecommerce.order.api.dto.FlowAddressQueryDTO;
import com.ecommerce.order.api.dto.OrderBillCheckDTO;
import com.ecommerce.order.api.dto.OrderBillCheckQueryDTO;
import com.ecommerce.order.api.dto.OrderCostsDTO;
import com.ecommerce.order.api.dto.OrderCountDTO;
import com.ecommerce.order.api.dto.OrderCreateDTO;
import com.ecommerce.order.api.dto.OrderDTO;
import com.ecommerce.order.api.dto.OrderDetailConditionDTO;
import com.ecommerce.order.api.dto.OrderIdAndContractIdDTO;
import com.ecommerce.order.api.dto.OrderInvoiceCallBackDTO;
import com.ecommerce.order.api.dto.OrderLogisticsQueryDTO;
import com.ecommerce.order.api.dto.OrderLogisticsResultDTO;
import com.ecommerce.order.api.dto.OrderOverviewDTO;
import com.ecommerce.order.api.dto.OrderQueryDTO;
import com.ecommerce.order.api.dto.OrderRecCostsDTO;
import com.ecommerce.order.api.dto.OrderSimpleDTO;
import com.ecommerce.order.api.dto.OrderSimpleQueryDTO;
import com.ecommerce.order.api.dto.OrderStatusStatisticDTO;
import com.ecommerce.order.api.dto.OrderStatusStatisticsDTO;
import com.ecommerce.order.api.dto.OrderUnderLinePayConfirmDTO;
import com.ecommerce.order.api.dto.OverviewReqDTO;
import com.ecommerce.order.api.dto.PayCallbackDTO;
import com.ecommerce.order.api.dto.RepurchaseOrderDTO;
import com.ecommerce.order.api.dto.TransactionsOverviewDTO;
import com.ecommerce.order.api.dto.UninvoicedOrderDTO;
import com.ecommerce.order.api.dto.UninvoicedOrderReqDTO;
import com.ecommerce.order.api.dto.UnitConversionDTO;
import com.ecommerce.order.api.dto.concrete.CreateConcreteOrderDTO;
import com.ecommerce.order.api.dto.logistics.PickingBillExportInfoDTO;
import com.ecommerce.order.api.dto.pay.TradingFlowExportInfoDTO;
import com.ecommerce.order.api.enums.OrderTakeStatus;

import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 订单服务
 */
@FeignClient(name = "service-order")
public interface IOrderService {

    @PostMapping(path = "/order/pageBuyerOrder", consumes = "application/json")
    ItemResult<PageInfo<OrderDTO>> pageBuyerOrder(@RequestBody OrderQueryDTO arg0,
                                                  @RequestParam Integer arg1, @RequestParam Integer arg2);

    @PostMapping(path = "/order/computCosts", consumes = "application/json")
    ItemResult<OrderCostsDTO> computCosts(@RequestBody OrderCreateDTO arg0);

    @PostMapping(path = "/order/recomputCosts", consumes = "application/json")
    ItemResult<OrderCostsDTO> recomputCosts(@RequestBody OrderRecCostsDTO recCostsDTO);

    @PostMapping(path = "/order/payTradeCallback", consumes = "application/json")
    ItemResult<String> payTradeCallback(@RequestBody PayCallbackDTO arg0);

    @PostMapping(path = "/order/underLinePay", consumes = "application/json")
    ItemResult<String> underLinePay(@RequestParam("arg0") String arg0, @RequestBody List<String> arg1,
                                    @RequestParam("arg2") String arg2, @RequestParam("arg3") String arg3);

    @PostMapping(path = "/order/getOrderDetail", consumes = "application/json")
    ItemResult<OrderDTO> getOrderDetail(@RequestParam("arg0") String arg0);

    @Operation(summary = "订单表数据查询(仅查询订单表的数据)")
    @PostMapping(path = "/order/getOrderTableInfo", consumes = "application/json")
    ItemResult<OrderDTO> getOrderTableInfo(@RequestParam("orderId") String orderId);

    @Operation(summary = "订单表数据查询(仅查询订单表的数据)")
    @PostMapping(path = "/order/getOrderTableInfoByTakeCode", consumes = "application/json")
    ItemResult<OrderDTO> getOrderTableInfoByTakeCode(@RequestParam("takeCode") String takeCode);

    @Operation(summary = "根据订单查询对应的合同id")
    @PostMapping(path = "/order/getContractIdByOrderIds", consumes = "application/json")
    ItemResult<List<OrderIdAndContractIdDTO>> getContractIdByOrderIds(@RequestBody Collection<String> orderIds);

    @PostMapping(path = "/order/checkOrderCancel", consumes = "application/json")
    ItemResult<String> checkOrderCancel();

    @PostMapping(path = "/order/pageSellerOrder", consumes = "application/json")
    ItemResult<PageInfo<OrderDTO>> pageSellerOrder(@RequestBody OrderQueryDTO arg0,
                                                   @RequestParam Integer arg1, @RequestParam Integer arg2);

    @PostMapping(path = "/order/updateOrderItemTakeCost", consumes = "application/json")
    ItemResult<String> updateOrderItemTakeCost(@RequestParam("arg0") String arg0,
                                               @RequestBody Map<String, BigDecimal> arg1, @RequestParam("arg2") String arg2);

    @PostMapping(path = "/order/doSellerConfirmOrder", consumes = "application/json")
    ItemResult<String> doSellerConfirmOrder(@RequestBody OrderDTO arg0, @RequestParam("arg1") String arg1);

    @PostMapping(path = "/order/doPlatformCloseOrder", consumes = "application/json")
    ItemResult<String> doPlatformCloseOrder(@RequestParam("arg0") String arg0, @RequestParam(name = "arg1", required = false) String arg1,
                                            @RequestParam("arg2") String arg2);

    @PostMapping(path = "/order/getBuyerOrderDetail", consumes = "application/json")
    ItemResult<OrderDTO> getBuyerOrderDetail(@RequestParam("arg0") String arg0,
                                             @RequestParam("arg1") String arg1);

    @PostMapping(path = "/order/doCreateEnquiryOrder", consumes = "application/json")
    ItemResult<OrderDTO> doCreateEnquiryOrder(@RequestBody OrderCreateDTO arg0,
                                              @RequestParam String arg1);

    @PostMapping(path = "/order/doSellerCloseOrder", consumes = "application/json")
    ItemResult<String> doSellerCloseOrder(@RequestParam("arg0") String arg0, @RequestParam(name = "arg1", required = false) String arg1,
                                          @RequestParam("arg2") String arg2);

    @PostMapping(path = "/order/doBuyerCancelOrder", consumes = "application/json")
    ItemResult<String> doBuyerCancelOrder(@RequestParam("arg0") String arg0, @RequestParam(name = "arg1", required = false) String arg1,
                                          @RequestParam("arg2") String arg2);

    @PostMapping(path = "/order/doCreateContractOrder", consumes = "application/json")
    ItemResult<OrderDTO> doCreateContractOrder(@RequestBody OrderCreateDTO arg0,
                                               @RequestParam String arg1);

    @PostMapping(path = "/order/doBuyerCloseOrder", consumes = "application/json")
    ItemResult<String> doBuyerCloseOrder(@RequestParam("arg0") String arg0, @RequestParam(name = "arg1", required = false) String arg1,
                                         @RequestParam("arg2") String arg2);

    @PostMapping(path = "/order/comfireUnderLinePay", consumes = "application/json")
    ItemResult<String> comfireUnderLinePay(@RequestBody OrderUnderLinePayConfirmDTO orderUnderLinePayConfirmDTO);

    @PostMapping(path = "/order/doSellerCancelOrder", consumes = "application/json")
    ItemResult<String> doSellerCancelOrder(@RequestParam("arg0") String arg0, @RequestParam(name = "arg1", required = false) String arg1,
                                           @RequestParam("arg2") String arg2);

    @PostMapping(path = "/order/pagePlatformOrder", consumes = "application/json")
    ItemResult<PageInfo<OrderDTO>> pagePlatformOrder(@RequestBody OrderQueryDTO arg0,
                                                     @RequestParam Integer arg1, @RequestParam Integer arg2);

    @PostMapping(path = "/order/getOrderCashierInfo", consumes = "application/json")
    ItemResult<String> getOrderCashierInfo(@RequestParam("arg0") String arg0);

    @PostMapping(path = "/order/updateOrderTakeStatus", consumes = "application/json")
    ItemResult<String> updateOrderTakeStatus(@RequestParam("arg0") String arg0,
                                             @RequestBody OrderTakeStatus arg1, @RequestParam("arg2") String arg2);

    @PostMapping(path = "/order/doCreateInsteadOrder", consumes = "application/json")
    ItemResult<OrderDTO> doCreateInsteadOrder(@RequestBody OrderCreateDTO arg0,
                                              @RequestParam String arg1);

    @PostMapping(path = "/order/getPlatformOrderDetail", consumes = "application/json")
    ItemResult<OrderDTO> getPlatformOrderDetail(@RequestParam("arg0") String arg0,
                                                @RequestParam("arg1") String arg1);

    @PostMapping(path = "/order/getSellerOrderDetail", consumes = "application/json")
    ItemResult<OrderDTO> getSellerOrderDetail(@RequestParam("arg0") String arg0,
                                              @RequestParam("arg1") String arg1);

    @PostMapping(path = "/order/doCreateSelfOrder", consumes = "application/json")
    ItemResult<OrderDTO> doCreateSelfOrder(@RequestBody OrderCreateDTO arg0, @RequestParam("arg1") String arg1);

    @PostMapping(path = "/order/getOrderAndItemInfo", consumes = "application/json")
    ItemResult<OrderDTO> getOrderAndItemInfo(@RequestParam("arg0") String arg0);

    @PostMapping(path = "/order/getOrderByTradeBillId", consumes = "application/json")
    ItemResult<OrderDTO> getOrderByTradeBillId(@RequestParam("arg0") String arg0);

    @PostMapping(path = "/order/doSellerDeleteOrder", consumes = "application/json")
    ItemResult<String> doSellerDeleteOrder(@RequestParam("arg0") String arg0, @RequestParam("arg1") String arg1);

    @PostMapping(path = "/order/doBuyerDeleteOrder", consumes = "application/json")
    ItemResult<String> doBuyerDeleteOrder(@RequestParam("arg0") String arg0, @RequestParam("arg1") String arg1);

    @PostMapping(path = "/order/doPlatformDeleteOrder", consumes = "application/json")
    ItemResult<String> doPlatformDeleteOrder(@RequestParam("arg0") String arg0,
                                             @RequestParam("arg1") String arg1);

    @PostMapping(path = "/order/updateTakeTimeLimit", consumes = "application/json")
    ItemResult<String> updateTakeTimeLimit(@RequestParam("orderId") String orderId,
                                           @RequestParam("takeTimeLimit") String takeTimeLimit, @RequestParam("operator") String operator);

    @PostMapping(path = "/order/getBuyerTradeBillIdByOrderId", consumes = "application/json")
    ItemResult<String> getBuyerTradeBillIdByOrderId(@RequestParam("orderId") String orderId,
                                                    @RequestParam("buyerId") String buyerId);

    @PostMapping(path = "/order/getBuyerResDayQuantity", consumes = "application/json")
    ItemResult<BigDecimal> getBuyerResDayQuantity(@RequestParam("buyerId") String buyerId,
                                                  @RequestParam("resourceId") String resourceId);

    @PostMapping(path = "/order/getOrderByContractId", consumes = "application/json")
    ItemResult<List<OrderDTO>> getOrderByContractId(@RequestParam("contractId") String contractId);

    @PostMapping(path = "/order/getOrderByContractSequence", consumes = "application/json")
    ItemResult<List<OrderDTO>> getOrderByContractSequence(@RequestParam("contractSequence") String contractSequence);

    @PostMapping(path = "/order/countOrders", consumes = "application/json")
    ItemResult<Integer> countOrders(@RequestBody OrderCountDTO orderCountDTO);

    @PostMapping(path = "/order/orderStatusStatistics", consumes = "application/json")
    ItemResult<List<OrderStatusStatisticsDTO>> orderStatusStatistics(@RequestBody List<String> saleRegionList, @RequestParam("sellerId") String sellerId);

    @PostMapping(path = "/order/transactionsOverview", consumes = "application/json")
    ItemResult<TransactionsOverviewDTO> transactionsOverview(@RequestBody OverviewReqDTO arg0);

    @PostMapping(path = "/order/orderOverview", consumes = "application/json")
    ItemResult<OrderOverviewDTO> orderOverview(@RequestBody OverviewReqDTO arg0);

    @PostMapping(path = "/order/getOrderByTakeId", consumes = "application/json")
    ItemResult<List<OrderDTO>> getOrderByTakeId(@RequestBody List<String> takeIds);

    @PostMapping(path = "/order/getOrderByTakeCodes", consumes = "application/json")
    ItemResult<List<OrderDTO>> getOrderByTakeCodes(@RequestBody List<String> takeCodes);

    @PostMapping(path = "/order/getOrderDetailByCondition", consumes = "application/json")
    ItemResult<OrderDTO> getOrderDetailByCondition(@RequestBody OrderDetailConditionDTO arg0);

    @PostMapping(path = "/order/pageUninvoicedOrderList", consumes = "application/json")
    ItemResult<PageInfo<UninvoicedOrderDTO>> pageUninvoicedOrderList(@RequestBody UninvoicedOrderReqDTO arg0);

    @PostMapping(path = "/order/applyOrderInvoice", consumes = "application/json")
    ItemResult<ApplyOrderInvoiceResponseDTO> applyOrderInvoice(@RequestBody ApplyOrderInvoiceRequestDTO arg0,
                                                               @RequestParam("arg1") String arg1);

    @PostMapping(path = "/order/doOrderInvoiceCallBack", consumes = "application/json")
    ItemResult<Boolean> doOrderInvoiceCallBack(@RequestBody OrderInvoiceCallBackDTO arg0);

    @PostMapping(path = "/order/confirmOrderInvoice", consumes = "application/json")
    ItemResult<Boolean> confirmOrderInvoice(@RequestBody ConfirmOrderInvoiceReqDTO arg0);

    @PostMapping(path = "/order/unitConversion", consumes = "application/json")
    ItemResult<UnitConversionDTO> unitConversion(@RequestParam("orderItemId") String orderItemId, @RequestParam("amount") BigDecimal amount);

    @PostMapping(path = "/order/statisticOrderStatus", consumes = "application/json")
    ItemResult<List<OrderStatusStatisticDTO>> statisticOrderStatus(@RequestBody OrderQueryDTO orderQueryDTO);

    @PostMapping(path = "/order/queryReceivingAddressMap", consumes = "application/json")
    ItemResult<List<ERPAddressDTO>> queryReceivingAddressMap(@RequestBody List<String> erpUnloadAddressIdList);

    @PostMapping(path = "/order/queryPickingBillExportInfo", consumes = "application/json")
    List<PickingBillExportInfoDTO> queryPickingBillExportInfo(@RequestBody Set<String> ids);

    @PostMapping(path = "/order/queryTradingFlowExportInfo", consumes = "application/json")
    List<TradingFlowExportInfoDTO> queryTradingFlowExportInfo(@RequestBody Set<String> ids);

    @Operation(summary = "根据合同批量调价信息查询（根据合同id和合同商品id查询对应的订单信息）")
    @PostMapping(path = "/order/queryOrderByContractBatchAdjustPriceInfo", consumes = "application/json")
    List<OrderSimpleDTO> queryOrderByContractBatchAdjustPriceInfo(@RequestBody OrderSimpleQueryDTO dto);

    @Operation(summary = "同批量调价后,尝试自动关闭订单")
    @PostMapping(path = "/order/tryAutoCloseOrder", consumes = "application/json")
    void tryAutoCloseOrder(@RequestBody OrderSimpleQueryDTO dto);

    @PostMapping(path = "/order/queryFlowControlAddress", consumes = "application/json")
    ItemResult<ERPAddressDTO> queryFlowControlAddress(@RequestBody FlowAddressQueryDTO flowAddressQueryDTO);

    @Operation(summary = "查询订单物流提货信息")
    @PostMapping(path = "/order/queryOrderLogisticsTakeInfo", consumes = "application/json")
    ItemResult<OrderLogisticsResultDTO> queryOrderLogisticsTakeInfo(@RequestBody OrderLogisticsQueryDTO orderLogisticsQueryDTO);

    @Operation(summary = "下单")
    @PostMapping(path = "/order/createConcreteOrder", consumes = "application/json")
    ItemResult<OrderDTO> createConcreteOrder(@RequestBody CreateConcreteOrderDTO createConcreteOrderDTO);

    @Operation(summary = "订单金额计算")
    @PostMapping(path = "/order/computeConcreteOrderCosts", consumes = "application/json")
    ItemResult<OrderCostsDTO> computeConcreteOrderCosts(@RequestBody CreateConcreteOrderDTO createConcreteOrderDTO);

    @Operation(summary = "订单信息查询(对账单专用)")
    @PostMapping(path = "/order/queryBillCheckInfo", consumes = "application/json")
    ItemResult<OrderBillCheckDTO> queryBillCheckInfo(@RequestBody OrderBillCheckQueryDTO query);

    @PostMapping(path = "/order/repurchaseOrder", consumes = "application/json")
    ItemResult<RepurchaseOrderDTO> repurchaseOrder(@RequestParam("orderId") String orderId);

    @PostMapping(path = "/order/queryAddressDetailByMemberId", consumes = "application/json")
    ItemResult<List<String>> queryAddressDetailByMemberId(@RequestBody OrderQueryDTO queryDTO);
}
