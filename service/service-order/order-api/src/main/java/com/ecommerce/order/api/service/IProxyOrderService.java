package com.ecommerce.order.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.order.api.dto.base.PageQuery;
import com.ecommerce.order.api.dto.proxy.BatchPayPrimaryOrderDTO;
import com.ecommerce.order.api.dto.proxy.BuyerSelectorDTO;
import com.ecommerce.order.api.dto.proxy.GoodsSelectorDTO;
import com.ecommerce.order.api.dto.proxy.OrderMapResultDTO;
import com.ecommerce.order.api.dto.proxy.OrderMapResultQueryDTO;
import com.ecommerce.order.api.dto.proxy.OrderSubmitQueryDTO;
import com.ecommerce.order.api.dto.proxy.OrderSubmitResultDTO;
import com.ecommerce.order.api.dto.proxy.PrimaryOrderCarriageDTO;
import com.ecommerce.order.api.dto.proxy.PrimaryOrderCreateDTO;
import com.ecommerce.order.api.dto.proxy.PrimaryOrderListDTO;
import com.ecommerce.order.api.dto.proxy.PrimaryOrderQueryDTO;
import com.ecommerce.order.api.dto.proxy.RetrySubmitPrimaryOrderDTO;
import com.ecommerce.order.api.dto.proxy.SecondaryOrderListDTO;
import com.ecommerce.order.api.dto.proxy.SecondaryOrderQueryDTO;
import com.ecommerce.order.api.dto.proxy.StoreSelectorDTO;
import com.ecommerce.order.api.dto.proxy.SubmitPrimaryOrderDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
@FeignClient(name ="service-order")
public interface IProxyOrderService {

    @PostMapping( "/proxyOrder/pageQuerySecondaryOrderList")
    ItemResult<PageData<SecondaryOrderListDTO>> pageQuerySecondaryOrderList(@RequestBody PageQuery<SecondaryOrderQueryDTO> pageQuery);

    @PostMapping( "/proxyOrder/newPageQuerySecondaryOrderList")
    ItemResult<PageData<SecondaryOrderListDTO>> newPageQuerySecondaryOrderList(@RequestBody PageQuery<SecondaryOrderQueryDTO> pageQuery);

    @PostMapping( "/proxyOrder/pageQueryPrimaryOrderList")
    ItemResult<PageData<PrimaryOrderListDTO>> pageQueryPrimaryOrderList(@RequestBody PageQuery<PrimaryOrderQueryDTO> pageQuery);

    @PostMapping( "/proxyOrder/batchSubmitProxyPrimaryOrder")
    ItemResult<Void> batchSubmitProxyPrimaryOrder(@RequestBody SubmitPrimaryOrderDTO submitPrimaryOrderDTO);

    @PostMapping( "/proxyOrder/queryOrderMapResultList")
    ItemResult<List<OrderMapResultDTO>> queryOrderMapResultList(@RequestBody OrderMapResultQueryDTO orderMapResultQueryDTO);

    @PostMapping( "/proxyOrder/pageQueryOrderSubmitList")
    ItemResult<PageData<OrderSubmitResultDTO>> pageQueryOrderSubmitList(@RequestBody PageQuery<OrderSubmitQueryDTO> pageQuery);

    @PostMapping( "/proxyOrder/batchPayPrimaryOrder")
    ItemResult<List<String>> batchPayPrimaryOrder(@RequestBody BatchPayPrimaryOrderDTO batchPayPrimaryOrderDTO);

    @PostMapping( "/proxyOrder/retrySubmitPrimaryOrder")
    ItemResult<Void> retrySubmitPrimaryOrder(@RequestBody RetrySubmitPrimaryOrderDTO retrySubmitPrimaryOrderDTO);

    @PostMapping( "/proxyOrder/computePrimaryOrderCarriage")
    ItemResult<PrimaryOrderCarriageDTO> computePrimaryOrderCarriage(@RequestBody PrimaryOrderCreateDTO primaryOrderCreateDTO);

    @PostMapping( "/proxyOrder/querySecondaryBuyerSelectorList")
    ItemResult<List<BuyerSelectorDTO>> querySecondaryBuyerSelectorList(@RequestBody SecondaryOrderQueryDTO secondaryOrderQueryDTO);

    @PostMapping( "/proxyOrder/querySecondaryStoreSelectorList")
    ItemResult<List<StoreSelectorDTO>> querySecondaryStoreSelectorList(@RequestBody SecondaryOrderQueryDTO secondaryOrderQueryDTO);

    @PostMapping( "/proxyOrder/querySecondaryGoodsSelectorList")
    ItemResult<List<GoodsSelectorDTO>> querySecondaryGoodsSelectorList(@RequestBody SecondaryOrderQueryDTO secondaryOrderQueryDTO);

    @PostMapping( "/proxyOrder/pageQueryOrderMapResultList")
    ItemResult<PageData<OrderMapResultDTO>> pageQueryOrderMapResultList(@RequestBody PageQuery<OrderMapResultQueryDTO> pageQuery);
}
