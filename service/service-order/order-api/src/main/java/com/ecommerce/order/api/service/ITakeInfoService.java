package com.ecommerce.order.api.service;

import com.ecommerce.order.api.dto.OrderDTO;
import com.ecommerce.order.api.dto.OverviewReqDTO;
import com.ecommerce.order.api.dto.PrimaryTakeInfoCreateDTO;
import com.ecommerce.order.api.dto.TakeInfoDTO;
import com.ecommerce.order.api.dto.TakeInfoExportDTO;
import com.ecommerce.order.api.dto.TakeInfoOverviewDTO;
import com.ecommerce.order.api.dto.TakeInfoSearchDTO;
import com.ecommerce.order.api.dto.TakeItemDTO;
import com.ecommerce.order.api.dto.TakeStatusStatisticDTO;
import com.ecommerce.order.api.dto.base.PageQuery;
import com.ecommerce.order.api.dto.erp.ShipBillERPInfoDTO;
import com.ecommerce.order.api.dto.proxy.map.error.ProxyMapErrorDTO;
import com.ecommerce.order.api.dto.proxy.map.error.ProxyMapErrorSearchDTO;
import com.ecommerce.order.api.dto.take.NeedBuyerPayDTO;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * null
 *
 * <AUTHOR>
 */

@FeignClient(name = "service-order")
public interface ITakeInfoService {

    @PostMapping("/takeInfo/doSellerCloseOrder")
    void doSellerCloseOrder(@RequestParam("arg0") String arg0, @RequestParam("arg1") String arg1);

    @PostMapping("/takeInfo/doSellerCloseTakeInfo")
    void doSellerCloseTakeInfo(@RequestParam("arg0") String arg0, @RequestParam("arg1") String arg1);

    @Schema(description = "原来就没有实现,应用不到,参见getTakeInfoForCreate 2020.6.17 ")
    @Deprecated(since = "2.1.4-RELEASE")
    @PostMapping("/takeInfo/prepareTakeInfo")
    TakeInfoDTO prepareTakeInfo(@RequestParam("arg0") String arg0, @RequestParam("arg1") String arg1);

    @PostMapping("/takeInfo/doBatchSellerConfirmTakeInfoNoChange")
    List<String> doBatchSellerConfirmTakeInfoNoChange(@RequestBody List<String> arg0,
                                                      @RequestParam String arg1, @RequestParam String arg2);

    @PostMapping("/takeInfo/getPlatformTakeInfoDetail")
    TakeInfoDTO getPlatformTakeInfoDetail(@RequestParam("arg0") String arg0, @RequestParam("arg1") String arg1);

    @PostMapping("/takeInfo/doSellerConfirmTakeInfo")
    List<TakeInfoDTO> doSellerConfirmTakeInfo(@RequestBody List<TakeInfoDTO> arg0,
                                              @RequestParam String arg1, @RequestParam String arg2);

    @PostMapping("/takeInfo/doSellerConfirmTakeInfoNoChange")
    String doSellerConfirmTakeInfoNoChange(@RequestParam("arg0") String arg0, @RequestParam("arg1") String arg1,
                                           @RequestParam("arg2") String arg2);

    @PostMapping("/takeInfo/getPlantformOrderTakeInfo")
    List<TakeInfoDTO> getPlantformOrderTakeInfo(@RequestParam("arg0") String arg0,
                                                @RequestParam("arg1") String arg1);

    @PostMapping("/takeInfo/doBuyerCreateTakeInfo")
    List<TakeInfoDTO> doBuyerCreateTakeInfo(@RequestBody List<TakeInfoDTO> arg0,
                                            @RequestParam String arg1, @RequestParam String arg2);

    @Operation(summary = "经销商创建一级发货单(二级发货单自动创建一级发货单异常时重建使用)")
    @PostMapping("/takeInfo/doSellerCreatePrimaryTakeInfo")
    List<TakeInfoDTO> doSellerCreatePrimaryTakeInfo(@Parameter(name = "secondaryTakeInfoIds", description = "secondaryTakeInfoIds – 发货单列表") @RequestBody List<String> secondaryTakeInfoIds,
                                                    @Parameter(name = "memberId", description = "memberId – 会员ID") @RequestParam("memberId") String memberId,
                                                    @Parameter(name = "operator", description = "operator – 操作人") @RequestParam("operator") String operator);

    @PostMapping("/takeInfo/pageBuyerTakeInfo")
    PageInfo<TakeInfoDTO> pageBuyerTakeInfo(@RequestBody TakeInfoSearchDTO arg0);

    @PostMapping("/takeInfo/doSellerCreateTakeInfo")
    List<TakeInfoDTO> doSellerCreateTakeInfo(@RequestBody List<TakeInfoDTO> arg0,
                                             @RequestParam String arg1, @RequestParam String arg2);

    @PostMapping("/takeInfo/doOrderCreateTakeInfo")
    List<TakeInfoDTO> doOrderCreateTakeInfo(@RequestBody OrderDTO arg0, @RequestParam("arg1") String arg1);

    @PostMapping("/takeInfo/updateSendQuantity")
    void updateSendQuantity(@RequestParam("arg0") String arg0, @RequestParam("arg1") String arg1,
                            @RequestBody Map<String, String> arg2);

    @PostMapping("/takeInfo/pageSellerTakeInfo")
    PageInfo<TakeInfoDTO> pageSellerTakeInfo(@RequestBody TakeInfoSearchDTO arg0);

    @Schema(description = "发货单创建后不可删除，只能关闭，当前页面没有删除按钮  2020.6.17")
    @Deprecated(since = "2.1.4-RELEASE")
    @PostMapping("/takeInfo/doSellerDeleteTakeInfo")
    void doSellerDeleteTakeInfo(@RequestParam("arg0") String arg0, @RequestParam("arg1") String arg1);

    @Schema(description = "发货单创建后不可删除，只能关闭，当前页面没有删除按钮  2020.6.17")
    @Deprecated(since = "2.1.4-RELEASE")
    @PostMapping("/takeInfo/doBuyerDeleteTakeInfo")
    void doBuyerDeleteTakeInfo(@RequestParam("arg0") String arg0, @RequestParam("arg1") String arg1);

    @PostMapping("/takeInfo/getSellerOrderTakeInfo")
    List<TakeInfoDTO> getSellerOrderTakeInfo(@RequestParam("arg0") String arg0,
                                             @RequestParam("arg1") String arg1);

    @PostMapping("/takeInfo/getBuyerOrderTakeInfo")
    List<TakeInfoDTO> getBuyerOrderTakeInfo(@RequestParam("arg0") String arg0,
                                            @RequestParam("arg1") String arg1);

    @PostMapping("/takeInfo/pagePlatformTakeInfo")
    PageInfo<TakeInfoDTO> pagePlatformTakeInfo(@RequestBody TakeInfoSearchDTO arg0);

    @PostMapping("/takeInfo/doFinishTakeInfoByLogistic")
    void doFinishTakeInfoByLogistic(@RequestParam("arg0") String arg0, @RequestBody List<TakeItemDTO> arg1,
                                    @RequestParam("arg2") String arg2);

    @PostMapping("/takeInfo/doCloseTakeInfoByLogistic")
    void doCloseTakeInfoByLogistic(@RequestParam("arg0") String arg0, @RequestBody List<TakeItemDTO> arg1,
                                   @RequestParam("arg2") String arg2);

    @PostMapping("/takeInfo/doBuyerCancelTakeInfo")
    String doBuyerCancelTakeInfo(@RequestParam("arg0") String arg0, @RequestParam("arg1") String arg1,
                                 @RequestParam("arg2") String arg2);

    @PostMapping("/takeInfo/updateSellerSignQuantity")
    void updateSellerSignQuantity(@RequestParam("arg0") String arg0, @RequestParam("arg1") String arg1, @RequestParam("arg2") String arg2,
                                  @RequestBody Map<String, String> arg3);

    @PostMapping("/takeInfo/getTakeInfoDetailForOrder")
    List<TakeInfoDTO> getTakeInfoDetailForOrder(@RequestParam("arg0") String arg0);

    @PostMapping("/takeInfo/getSellerTakeInfoDetail")
    TakeInfoDTO getSellerTakeInfoDetail(@RequestParam("arg0") String arg0, @RequestParam("arg1") String arg1,
                                        @RequestParam("arg2") String arg2);

    @PostMapping("/takeInfo/getBuyerTakeInfoDetail")
    TakeInfoDTO getBuyerTakeInfoDetail(@RequestParam("arg0") String arg0, @RequestParam("arg1") String arg1,
                                       @RequestParam("arg2") String arg2);

    @PostMapping("/takeInfo/checkTakeTimeLimited")
    void checkTakeTimeLimited();

    @PostMapping("/takeInfo/getTakeInfoByCode")
    TakeInfoDTO getTakeInfoByCode(@RequestParam("arg0") String arg0);

    @PostMapping("/takeInfo/doSellerCancelTakeInfo")
    String doSellerCancelTakeInfo(@RequestParam("arg0") String arg0, @RequestParam("arg1") String arg1,
                                  @RequestParam("arg2") String arg2);

    @PostMapping("/takeInfo/getTakeInfoForCreate")
    TakeInfoDTO getTakeInfoForCreate(@RequestParam("arg0") String arg0, @RequestParam("arg1") String arg1);

    @PostMapping("/takeInfo/doSellerFinishTakeInfo")
    void doSellerFinishTakeInfo(@RequestBody List<TakeItemDTO> takeItems, @RequestParam("takeInfoId") String takeInfoId,
                                @RequestParam("memberId") String memberId, @RequestParam("operator") String operator);

    @PostMapping("/takeInfo/doBuyerFinishTakeInfo")
    void doBuyerFinishTakeInfo(@RequestBody List<TakeItemDTO> takeItems, @RequestParam("takeInfoId") String takeInfoId,
                               @RequestParam("memberId") String memberId, @RequestParam("operator") String operator);

    @PostMapping("/takeInfo/doSellerSendTakeInfoToLogistic")
    TakeInfoDTO doSellerSendTakeInfoToLogistic(@RequestParam("takeInfoId") String takeInfoId,
                                               @RequestParam("memberId") String memberId, @RequestParam("operator") String operator);

    @PostMapping("/takeInfo/takeInfoOverview")
    TakeInfoOverviewDTO takeInfoOverview(@RequestBody OverviewReqDTO arg0);

    @PostMapping("/takeInfo/selectItemByOrderItemId")
    List<TakeItemDTO> selectItemByOrderItemId(@RequestParam("orderItemId") String orderItemId);

    @PostMapping("/takeInfo/statisticTakeStatus")
    List<TakeStatusStatisticDTO> statisticTakeStatus(@RequestBody TakeInfoSearchDTO takeInfoSearchDTO);

    @PostMapping("/takeInfo/exportTakeInfo")
    List<TakeInfoExportDTO> exportTakeInfo(@RequestBody TakeInfoSearchDTO takeInfoSearchDTO);

    @Operation(summary = "根据运单创建一级发货单")
    @PostMapping("/takeInfo/createPrimaryTakeInfo")
    Boolean createPrimaryTakeInfo(@RequestBody PrimaryTakeInfoCreateDTO dto);

    @Operation(summary = "二背靠背异常列表")
    @PostMapping("/takeInfo/pageProxyMapError")
    PageInfo<ProxyMapErrorDTO> pageProxyMapError(@RequestBody PageQuery<ProxyMapErrorSearchDTO> query);

    @PostMapping("/takeInfo/queryTakeCodeByContractIdList")
    List<String> queryTakeCodeByContractIdList(@RequestParam("contractIdList") List<String> contractIdList);

    @Operation(summary = "校验支付单是否需要支付，并可能会更新返回最新的支付单金额")
    @PostMapping("/takeInfo/needBuyerPay")
    NeedBuyerPayDTO needBuyerPay(@RequestBody NeedBuyerPayDTO needBuyerPayDTO);

    @Operation(summary = "根据发货单号查询船运单ERP相关信息")
    @PostMapping("/takeInfo/findShipBillERPInfoByTakeCode")
    ShipBillERPInfoDTO findShipBillERPInfoByTakeCode(@RequestParam("takeCode") String takeCode, @RequestParam("contractId") String contractId);
}
