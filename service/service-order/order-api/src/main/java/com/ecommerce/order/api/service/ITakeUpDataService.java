package com.ecommerce.order.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.order.api.dto.CloseTakeInfoDTO;
import com.ecommerce.order.api.dto.FinishTakeInfoDTO;
import com.ecommerce.order.api.dto.UpdateTakeInfoShippedDTO;
import com.ecommerce.order.api.dto.UpdateTakeInfoSignDTO;
import com.ecommerce.order.api.dto.logistics.OverSendOutVerifyDTO;
import com.ecommerce.order.api.dto.logistics.OverSendOutVerifyResponseDTO;
import com.ecommerce.order.api.dto.logistics.ShipBillRefundDTO;
import com.ecommerce.order.api.dto.take.DeliveringTakInfoDTO;

import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * :null
 *
 * <AUTHOR>
 */

@FeignClient(name = "service-order")
public interface ITakeUpDataService {


    @PostMapping("/takeUpData/doLogisticFinishTakeInfo")
    ItemResult<Boolean> doLogisticFinishTakeInfo(@RequestBody FinishTakeInfoDTO arg0);

    @Operation(summary = "物流开始发货了")
    @PostMapping("/takeUpData/doLogisticDeliveringTakInfo")
    ItemResult<Boolean> doLogisticDeliveringTakInfo(@RequestBody DeliveringTakInfoDTO deliveringTakInfoDTO);


    @PostMapping("/takeUpData/doLogisticUpdateSignQuantity")
    ItemResult<Boolean> doLogisticUpdateSignQuantity(@RequestBody UpdateTakeInfoSignDTO arg0);


    @PostMapping("/takeUpData/doLogisticUpdateShippedQuantity")
    ItemResult<Boolean> doLogisticUpdateShippedQuantity(@RequestBody UpdateTakeInfoShippedDTO arg0);


    @PostMapping("/takeUpData/doLogisticCloseTakeInfo")
    ItemResult<Boolean> doLogisticCloseTakeInfo(@RequestBody CloseTakeInfoDTO arg0);


    @PostMapping("/takeUpData/overSendOutVerify")
    ItemResult<OverSendOutVerifyResponseDTO> overSendOutVerify(@RequestBody OverSendOutVerifyDTO overSendOutVerifyDTO);

    @PostMapping("/takeUpData/doLogisticRefund")
    ItemResult<Boolean> doLogisticRefund(@RequestBody ShipBillRefundDTO shipBillRefundDTO);
}
