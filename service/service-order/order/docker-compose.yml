
services:
  service-order:
    image: ecommerce-service-order:1.0
    volumes:
      - /remote/logs/order:/var/log
      - /remote/skywalking:/home/<USER>
    deploy:
      resources:
        limits:
          memory: 800m
    container_name: order
    restart: always
    ports:
      - "9009:9009"
    environment:
      - SPRING_CLOUD_CONFIG_URI=http://************:8888
      - SPRING_CLOUD_CONFIG_LABEL=asset-2025
      - SPRING_CLOUD_CONFIG_PROFILE=dev
      - SPRING_CLOUD_CONFIG_NAME=order,db,redis,rabbitmq,hystrix,vip-server,xxl-job,eureka,kafka
      - TZ=Asia/Shanghai
      - JAVA_TOOL_OPTIONS=-Xms256m -Xmx512m -XX:+UseSerialGC --add-opens=java.base/java.lang=ALL-UNNAMED
    depends_on:
      - config
      - eureka