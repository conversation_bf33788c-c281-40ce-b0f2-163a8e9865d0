<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>service-order</artifactId>
        <groupId>com.ecommerce</groupId>
        <version>2.1.4-RELEASE</version>
    </parent>
    <artifactId>order</artifactId>
    <packaging>jar</packaging>
    <name>order</name>
    <description>Order service</description>

    <properties>
        <docker.deploy.version>${project.version}</docker.deploy.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-config</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-eureka-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bus-amqp</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>

        <!--session同步-->
        <dependency>
            <groupId>org.springframework.session</groupId>
            <artifactId>spring-session-data-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-tracing-bridge-brave</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.danielwegener</groupId>
            <artifactId>logback-kafka-appender</artifactId>
            <version>0.2.0-RC2</version>
        </dependency>


        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>

        <!--<dependency>-->
        <!--<groupId>org.flywaydb</groupId>-->
        <!--<artifactId>flyway-core</artifactId>-->
        <!--</dependency>-->
        <dependency>
            <groupId>com.ecommerce</groupId>
            <artifactId>rabbitmq-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ecommerce</groupId>
            <artifactId>kafka-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <artifactId>order-api</artifactId>
            <groupId>com.ecommerce</groupId>
        </dependency>
        <dependency>
            <groupId>com.ecommerce</groupId>
            <artifactId>service-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ecommerce</groupId>
            <artifactId>price-api</artifactId>
        </dependency>
        <dependency>
            <artifactId>goods-api</artifactId>
            <groupId>com.ecommerce</groupId>
        </dependency>
        <dependency>
            <artifactId>open-api</artifactId>
            <groupId>com.ecommerce</groupId>
        </dependency>


        <dependency>
            <groupId>com.ecommerce</groupId>
            <artifactId>member-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ecommerce</groupId>
            <artifactId>base-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ecommerce</groupId>
            <artifactId>pay-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ecommerce</groupId>
            <artifactId>logistics-api</artifactId>
        </dependency>
        <!--&lt;!&ndash;数据订阅&ndash;&gt;-->
        <dependency>
            <artifactId>ecommerce-otter</artifactId>
            <groupId>com.ecommerce</groupId>
        </dependency>

        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.statemachine</groupId>
            <artifactId>spring-statemachine-data-common</artifactId>
            <version>2.2.0.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.statemachine</groupId>
            <artifactId>spring-statemachine-redis</artifactId>
            <version>1.2.10.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>jakarta.annotation</groupId>
            <artifactId>jakarta.annotation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>order</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.14.0</version>
                <configuration>
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                    <release>${java.version}</release>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
