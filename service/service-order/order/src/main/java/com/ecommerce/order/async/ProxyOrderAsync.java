package com.ecommerce.order.async;

import com.alibaba.fastjson.JSON;
import com.ecommerce.common.exception.BasicRuntimeException;
import com.ecommerce.common.utils.Date2Util;
import com.ecommerce.member.api.dto.member.config.MemberConfigDTO;
import com.ecommerce.member.api.dto.member.config.MemberConfigEnum;
import com.ecommerce.member.api.service.IMemberConfigService;
import com.ecommerce.order.api.dto.OrderDTO;
import com.ecommerce.order.api.dto.base.PageQuery;
import com.ecommerce.order.api.dto.proxy.*;
import com.ecommerce.order.dao.vo.OrderInfo;
import com.ecommerce.order.fsm.service.IProxyOrderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class ProxyOrderAsync {

    private final IMemberConfigService memberConfigService;
    private final IProxyOrderService proxyOrderService;

    @Async
    public void submitPrimaryOrder(OrderInfo orderInfo) {
        boolean autoCreate = getMemberAutoCreatePrimaryOrderArgs(orderInfo.getSellerId());
        log.info("收到创建一级订单请求: {}, {}", autoCreate, JSON.toJSONString(orderInfo));

        if (!autoCreate) {
            return;
        }


        SecondaryOrderQueryDTO queryDTO = new SecondaryOrderQueryDTO();
        queryDTO.setOrderNo(orderInfo.getOrderCode());

        PageQuery<SecondaryOrderQueryDTO> pageQuery = new PageQuery<>();
        pageQuery.setQueryDTO(queryDTO);

        List<SecondaryOrderListDTO> list = proxyOrderService.newPageQuerySecondaryOrderList(pageQuery).getList();
        if(list.size() == 0) {
            return;
        }

        SubmitPrimaryOrderDTO primaryOrderDTO = new SubmitPrimaryOrderDTO();
        primaryOrderDTO.setOperatorId("async");
        primaryOrderDTO.setOperatorName("async");
        primaryOrderDTO.setSellerId(orderInfo.getSellerId());

        for(SecondaryOrderListDTO dto : list) {
            try {
                PrimaryContractOptionMapDTO contractOptionMapDTO = dto.getOptionContractList().get(0);
                if ("-".equals(contractOptionMapDTO.getContractId())) {
                    throw new BasicRuntimeException(contractOptionMapDTO.getContractNumber());
                }

                if(dto.getOptionContractList().size() != 1) {
                    throw new BasicRuntimeException("找到多个关联合同");
                }

                /*
                -- 找出最近的一个二级订单
                SELECT order_code FROM `tr_order_info` WHERE proxy_order_type = 'secondary' AND deals_sequence = '' AND order_code != '' ORDER BY create_time DESC LIMIT 1

                -- 通过二级订单找到一级订单号
                SELECT primary_order_id FROM `tr_order_proxy_map` WHERE secondary_order_code = '';

                -- 通过一级订单号找到合同序号
                SELECT deals_id， deals_sequence FROM `tr_order_info` WHERE order_id = ''


                -- 根据合同序号找到合同ID
                 */


                PrimaryContractGoodsMapDTO goodsMapDTO = contractOptionMapDTO.getContactGoodsList().get(0);

                PrimaryOrderCreateDTO createDTO = new PrimaryOrderCreateDTO();

                createDTO.setContractId(contractOptionMapDTO.getContractId());
                createDTO.setProjectName(contractOptionMapDTO.getProjectName());
                createDTO.setContractNumber(contractOptionMapDTO.getContractNumber());
                createDTO.setSaleRegionPath(dto.getSaleRegionPath());
                createDTO.setContractGoodsId(goodsMapDTO.getContractGoodsId());
                createDTO.setDeliverWay(goodsMapDTO.getSupportDeliveryWayList().get(0));
                createDTO.setPurchaseQuantity(dto.getBookingQuantity());
                createDTO.setSecondaryOrderId(dto.getOrderId());
                createDTO.setSecondaryOrderCode(dto.getOrderCode());
                createDTO.setWarehouseId(goodsMapDTO.getWarehouseId());
                createDTO.setWarehouseName(goodsMapDTO.getWarehouseName());
                createDTO.setMapWarehouseId(goodsMapDTO.getMapWarehouseId());
                createDTO.setMapWarehouseName(goodsMapDTO.getMapWarehouseName());
                createDTO.setGoodsId(dto.getGoodsId());
                createDTO.setAllocationDate(Date2Util.format("yyyyMM"));
                createDTO.setBatchNumber("");

                List<PrimaryOrderCreateDTO> orderList = new ArrayList<>();
                orderList.add(createDTO);
                primaryOrderDTO.setPrimaryOrderCreateList(orderList);

                OrderDTO order = proxyOrderService.submitProxyPrimaryOrder2(primaryOrderDTO, "");
                if(order == null) {
                    throw new BasicRuntimeException("订单为NULL");
                }

                PrimaryOrderCreateDTO primaryOrderCreateDTO = primaryOrderDTO.getPrimaryOrderCreateList().get(0);

                // 自动支付
                proxyOrderService.primaryOrderAutoPay(order.getOrderPayinfo().get(0), primaryOrderCreateDTO.getOperatorId());
            } catch(Exception e) {
                log.info("二级订单号: {}, {}", orderInfo.getOrderCode(), e.getMessage());
            }
        }
    }

    /**
     * 查询卖家参数 自动生成一级订单
     * @param memberId
     * @return
     */
    private boolean getMemberAutoCreatePrimaryOrderArgs(String memberId) {
        boolean autoCreate = false;
        try {
            MemberConfigDTO dto = memberConfigService.findByMemberIdAndKeyCode(memberId, MemberConfigEnum.AUTO_CREATE_PRIMARY_ORDER.getKeyCode());
            autoCreate = dto != null && "1".equals(dto.getValue());
        } catch(Exception e) {
            log.info("ERROR 获取卖家参数: {}, {}, {}", memberId, MemberConfigEnum.AUTO_CREATE_PRIMARY_ORDER.getKeyCode(), e.getMessage());
        }
        return autoCreate;
    }

}
