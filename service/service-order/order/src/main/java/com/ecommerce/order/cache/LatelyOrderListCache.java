package com.ecommerce.order.cache;

import com.ecommerce.common.service.ICacheEntry;
import com.ecommerce.order.api.dto.LatelyOrderDTO;

import java.io.Serial;
import java.util.List;

public class LatelyOrderListCache implements ICacheEntry<String> {
    @Serial
    private static final long serialVersionUID = 1L;

    private String key;

    private List<LatelyOrderDTO> list;

    @Override
    public String getKey() {
        return key;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public List<LatelyOrderDTO> getList() {
        return list;
    }

    public void setList(List<LatelyOrderDTO> list) {
        this.list = list;
    }
}
