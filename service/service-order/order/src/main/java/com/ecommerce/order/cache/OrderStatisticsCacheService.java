package com.ecommerce.order.cache;

import com.ecommerce.common.service.RedisService;
import com.ecommerce.order.api.dto.LatelyOrderDTO;
import com.ecommerce.order.api.dto.TodayOrderStatisticsDTO;
import com.ecommerce.order.biz.ITakeInfoBiz;
import com.ecommerce.order.dao.mapper.OrderInfoMapper;
import com.ecommerce.order.dao.mapper.OrderItemMapper;
import com.ecommerce.order.dao.vo.OrderInfo;
import com.ecommerce.order.dao.vo.OrderItem;
import com.github.pagehelper.Page;
import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
@RequiredArgsConstructor
public class OrderStatisticsCacheService implements IOrderStatisticsCacheService {

    private static final String TODAY_ORDER_STATISTICS_CACHE = "today_order_statistics_cache";

    private static final String LATELY_ORDER_LIST_CACHE = "lately_order_list_cache";

    private static final String DEL_FLG = "delFlg";
    private static final String CREATE_TIME = "createTime";

    private final RedisService redisService;

    private final OrderInfoMapper orderInfoMapper;
    private final OrderItemMapper orderItemMapper;
    private final ITakeInfoBiz takeInfoBiz;

    @Async
    @Override
    public void setTodayOrderStatisticsCache() {
        TodayOrderStatisticsCache cache = new TodayOrderStatisticsCache();
        cache.setKey(TODAY_ORDER_STATISTICS_CACHE);
        TodayOrderStatisticsDTO dto = new TodayOrderStatisticsDTO();
        //今日订单总数
        Condition orderInfoCondition = new Condition(OrderInfo.class);
        Example.Criteria orderInfoConditionCriteria = orderInfoCondition.createCriteria();
        orderInfoConditionCriteria.andEqualTo(DEL_FLG,false);
        orderInfoConditionCriteria.andGreaterThanOrEqualTo(CREATE_TIME, getStartTime());
        orderInfoConditionCriteria.andLessThanOrEqualTo(CREATE_TIME, getEndTime());
        int count = orderInfoMapper.selectCountByCondition(orderInfoCondition);
        dto.setTotalCount(count);
        //今日订单总数
        Condition orderInfoCondition2 = new Condition(OrderInfo.class);
        Example.Criteria orderInfoConditionCriteria2 = orderInfoCondition2.createCriteria();
        orderInfoConditionCriteria2.andEqualTo(DEL_FLG,false);
        orderInfoConditionCriteria2.andGreaterThanOrEqualTo(CREATE_TIME, getStartTime());
        orderInfoConditionCriteria2.andLessThanOrEqualTo(CREATE_TIME, getEndTime());
        StringBuilder keyWordsCondition =
                new StringBuilder("(order_id IN " +
                        "(SELECT item.order_id FROM tr_order_item item " +
                        "WHERE item.del_flg = 0 and item.category_code like '%002001001%' ))");
        orderInfoConditionCriteria2.andCondition(keyWordsCondition.toString());
        int cementcCount = orderInfoMapper.selectCountByCondition(orderInfoCondition2);
        dto.setCementCount(cementcCount);
        //今日订单总量
        Condition orderItemCondition = new Condition(OrderItem.class);
        Example.Criteria orderItemConditionCriteria = orderItemCondition.createCriteria();
        orderItemConditionCriteria.andEqualTo(DEL_FLG,false);
        orderItemConditionCriteria.andGreaterThanOrEqualTo(CREATE_TIME, getStartTime());
        orderItemConditionCriteria.andLessThanOrEqualTo(CREATE_TIME, getEndTime());
        orderItemConditionCriteria.andLike("categoryCode","%002001001%");
        List<OrderItem> orderItems = orderItemMapper.selectByCondition(orderItemCondition);
        BigDecimal cementWeight = BigDecimal.ZERO;
        if(CollectionUtils.isNotEmpty(orderItems)){
            for (OrderItem orderItem : orderItems) {
                if("袋".equals(orderItem.getUnits())){
                    cementWeight = cementWeight.add(orderItem.getItemQuantity().multiply(BigDecimal.valueOf(0.05)));
                }else {
                    cementWeight = cementWeight.add(orderItem.getItemQuantity());
                }
            }
        }
        BigDecimal formatCementWeigh = cementWeight.setScale(0, RoundingMode.HALF_DOWN);
        dto.setCementWeight(formatCementWeigh);
        cache.setDto(dto);
        redisService.set(TODAY_ORDER_STATISTICS_CACHE, cache);
        redisService.setTimeout(TODAY_ORDER_STATISTICS_CACHE,30, TimeUnit.MINUTES);
    }

    @Override
    public TodayOrderStatisticsCache getTodayOrderStatisticsCache() {
        TodayOrderStatisticsCache todayOrderStatisticsCache = redisService.get(TODAY_ORDER_STATISTICS_CACHE, TodayOrderStatisticsCache.class);
        if(todayOrderStatisticsCache == null){
            todayOrderStatisticsCache = new TodayOrderStatisticsCache();
        }
        return todayOrderStatisticsCache;
    }

    @Override
    public void setLatelyOrderListCache() {
        LatelyOrderListCache cache = new LatelyOrderListCache();
        cache.setKey(LATELY_ORDER_LIST_CACHE);
        List<LatelyOrderDTO> latelyOrderDTOS = Lists.newArrayList();
        //
        Condition condition = new Condition(OrderItem.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(DEL_FLG,false);
        condition.orderBy(CREATE_TIME).desc();
        Page<OrderItem> page = PageMethod.startPage(1, 2, true)
                .doSelectPage(() -> orderItemMapper.selectByExample(condition));
        if(page != null){
            List<OrderItem> list = page.getResult();
            if(CollectionUtils.isNotEmpty(list)){
                for (OrderItem orderItem : list) {
                    LatelyOrderDTO latelyOrderDTO = new LatelyOrderDTO();
                    OrderInfo orderInfo = orderInfoMapper.selectByPrimaryKey(orderItem.getOrderId());
                    latelyOrderDTO.setBuyerName(orderInfo.getBuyerName());
                    latelyOrderDTO.setItemQuantity(orderItem.getItemQuantity());
                    latelyOrderDTO.setGoodsName(orderItem.getGoodsName());
                    latelyOrderDTO.setActualAmountPrice(orderItem.getActualAmountPrice());
                    latelyOrderDTO.setUnits(orderItem.getUnits());
                    latelyOrderDTO.setCreateTime(orderItem.getCreateTime());
                    latelyOrderDTOS.add(latelyOrderDTO);
                }
            }
        }
        cache.setList(latelyOrderDTOS);
        redisService.set(LATELY_ORDER_LIST_CACHE, cache);
        redisService.setTimeout(LATELY_ORDER_LIST_CACHE,30, TimeUnit.MINUTES);
    }

    @Override
    public LatelyOrderListCache getLatelyOrderListCache() {
        return redisService.get(LATELY_ORDER_LIST_CACHE, LatelyOrderListCache.class);
    }

    public static Date getStartTime() {
        Calendar todayStart = Calendar.getInstance();
        todayStart.set(Calendar.HOUR_OF_DAY, 0);
        todayStart.set(Calendar.MINUTE, 0);
        todayStart.set(Calendar.SECOND, 0);
        todayStart.set(Calendar.MILLISECOND, 0);
        return todayStart.getTime();
    }

    public static Date getEndTime() {
        Calendar todayEnd = Calendar.getInstance();
        todayEnd.set(Calendar.HOUR_OF_DAY, 23);
        todayEnd.set(Calendar.MINUTE, 59);
        todayEnd.set(Calendar.SECOND, 59);
        todayEnd.set(Calendar.MILLISECOND, 999);
        return todayEnd.getTime();
    }
}
