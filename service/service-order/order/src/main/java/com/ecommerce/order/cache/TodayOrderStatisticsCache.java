package com.ecommerce.order.cache;

import com.ecommerce.common.service.ICacheEntry;
import com.ecommerce.order.api.dto.TodayOrderStatisticsDTO;

import java.io.Serial;


public class TodayOrderStatisticsCache  implements ICacheEntry<String> {
    @Serial
    private static final long serialVersionUID = 1L;

    private String key;

    private TodayOrderStatisticsDTO dto;

    @Override
    public String getKey() {
        return key;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public TodayOrderStatisticsDTO getDto() {
        return dto;
    }

    public void setDto(TodayOrderStatisticsDTO dto) {
        this.dto = dto;
    }
}
