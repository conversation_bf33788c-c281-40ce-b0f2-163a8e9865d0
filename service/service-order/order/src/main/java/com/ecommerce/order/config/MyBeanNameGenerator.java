package com.ecommerce.order.config;

import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.beans.factory.annotation.AnnotatedBeanDefinition;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.AnnotationBeanNameGenerator;


public class MyBeanNameGenerator extends AnnotationBeanNameGenerator {

    @Override
    protected String buildDefaultBeanName(BeanDefinition definition) {
        if (definition instanceof AnnotatedBeanDefinition beanDefinition) {
            String beanName = determineBeanNameFromAnnotation(beanDefinition);
            if (CsStringUtils.hasText(beanName)) {
                // Explicit bean name found.
                return beanName;
            }
        }
        //包名 + 类名 作为服务的名称
        return definition.getBeanClassName();
    }
}
