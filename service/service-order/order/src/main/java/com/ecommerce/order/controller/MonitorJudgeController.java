package com.ecommerce.order.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.common.result.ItemResult;
import com.ecommerce.order.api.dto.MonitorJudgeCondDTO;
import com.ecommerce.order.api.dto.MonitorJudgeResDTO;
import com.ecommerce.order.fsm.service.IMonitorJudgeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 监控判断服务
 *
 * <AUTHOR>
*/

@RestController
@Tag(name = "MonitorJudge", description = "监控判断服务")
@RequestMapping("/monitorJudge")
@AllArgsConstructor
public class MonitorJudgeController {

   private final IMonitorJudgeService iMonitorJudgeService;

   @Operation(summary = "null")
   @PostMapping(value="/monitorJudge")
   public ItemResult<MonitorJudgeResDTO> monitorJudge(@RequestBody MonitorJudgeCondDTO monitorJudgeCondDTO){
      return iMonitorJudgeService.monitorJudge(monitorJudgeCondDTO);
   }



}
