package com.ecommerce.order.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.common.result.ItemResult;
import com.ecommerce.order.api.dto.export.OrderExportCondDTO;
import com.ecommerce.order.api.dto.export.OrderExportDTO;
import com.ecommerce.order.api.dto.export.OrderItemOption;
import com.ecommerce.order.service.IOrderExportService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Set;

/**
 * : 列表导出服务
 *
 * <AUTHOR>
*/

@RestController
@Tag(name = "OrderExport", description = ": 列表导出服务")
@RequestMapping("/orderExport")
@AllArgsConstructor
public class OrderExportController {

   private final IOrderExportService iOrderExportService;

   @Operation(summary = "订单列表导出")
   @PostMapping(value="/exportOrderList")
   public ItemResult<List<OrderExportDTO>> exportOrderList(@RequestBody OrderExportCondDTO orderExportCondDTO){
      return iOrderExportService.exportOrderList(orderExportCondDTO);
   }

   /**
    * orderItemIdSet查询orderItemOption
    * @param orderItemIdSet
    * @return
    */
   @Operation(summary = "orderItemIdSet查询orderItemOption")
   @PostMapping(value="/queryOrderItemOptionsByItemIdSet")
   public ItemResult<List<OrderItemOption>> queryOrderItemOptionsByItemIdSet(@RequestBody Set<String> orderItemIdSet) {
      return iOrderExportService.queryOrderItemOptionsByItemIdSet(orderItemIdSet);
   }

}
