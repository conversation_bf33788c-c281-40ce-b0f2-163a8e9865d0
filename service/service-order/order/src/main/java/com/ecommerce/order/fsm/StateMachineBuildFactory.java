package com.ecommerce.order.fsm;

import com.ecommerce.common.exception.BasicRuntimeException;
import com.ecommerce.order.fsm.builder.IStateMachineBuilder;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.statemachine.StateMachine;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 下午2:45 20/5/21
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class StateMachineBuildFactory<S, E> implements ApplicationContextAware {

    private final BeanFactory beanFactory;

    /**
     * 用来存储builder-name及builder的map
     */
    public static final Map<String, IStateMachineBuilder> builderMap = Maps.newConcurrentMap();

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        String[] beanNamesForType = applicationContext.getBeanNamesForType(IStateMachineBuilder.class);
        for (String beanName : beanNamesForType) {
            IStateMachineBuilder bean = (IStateMachineBuilder) applicationContext.getBean(beanName);
            builderMap.put(bean.getName(), bean);
        }
    }

    /**
     * 创建状态机
     *
     * @param machineType 状态机类型
     */
    public StateMachine<S, E> createStateMachine(String machineType) throws Exception{
        if (CsStringUtils.isBlank(CsStringUtils.trim(machineType))) {
            throw new BasicRuntimeException("无效的状态机类型");
        }
        IStateMachineBuilder builder = builderMap.get(machineType);
        StateMachine<S, E> stateMachine;
        try {
            stateMachine = builder.build(beanFactory);
        } catch (Exception e) {
            throw new BasicRuntimeException("创建状态机异常");
        }

        return stateMachine;
    }

}
