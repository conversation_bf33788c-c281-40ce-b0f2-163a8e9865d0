package com.ecommerce.order.fsm;

/**
 * 上午12:52 20/5/22
 *
 * <AUTHOR>
 */
public class StateMachineConstants {

    // 私有构造函数，防止实例化
    private StateMachineConstants() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }

    /**
     * 状态参数对象
     */
    public static final String STATE_MODEL_DTO = "STATE_MODEL_DTO";

    /**
     * action执行返回参数
     */
    public static final String RETURN_PARAM = "RETURN_PARAM";
    /**
     * 事件
     */
    public static final String STATE_EVENT = "state_event";


    public static final String TRANSPORT_TYPE = "transportType";
    public static final String BILL_PAYMENT_TYPE = "bill_payment_type";
    public static final String PROXY_ORDER_TYPE = "proxy_order_type";

    public static final String ORDER_ID = "orderId";
    public static final String ORDER_CODE = "orderCode";
    public static final String ORDER_PAY_INFO_ID = "order_pay_info_id";
    public static final String ORDER_SUPPLEMENT = "order_supplement";

    public static final String TAKE_ID = "takeId";
    public static final String TAKE_ID_SECONDARY = "take_id_secondary";
    public static final String TAKE_CODE = "takeCode";
    public static final String TAKE_PRIMARY_CREATE_TRIGGER_FLAG = "take_primary_create_trigger_flag";

    public static final String TAKE_INFO_DTO = "takeInfoDTO";
    public static final String TAKE_INFO_CREATE_DTO = "takeInfoCreateDTO";
    public static final String TAKE_STATUS = "takeStatus";
    public static final String NEED_SELLER_CONFIRM = "needSellerConfirm";

    public static final String TAKE_INFO_CREATE_TYPE = "takeCreateType";
    public static final String TAKE_INFO_CANCEL_TYPE = "TakeCancelType";
    public static final String TAKE_INFO_FINISH_TYPE = "TakeFinishType";
    public static final String TAKE_INFO_CLOSE_TYPE = "TakeCloseType";
    public static final String TAKE_INFO_CONFIRM_NO_CHANGE = "take_info_confirm_no_change";

    public static final String IS_OFF_LINE_PAY = "is_Off_Line_Pay";

    public static final String OPERATOR = "operator";
    public static final String OPERATOR_MEMBER_ID = "operatorMemberId";
}
