package com.ecommerce.order.fsm.builder;



import com.ecommerce.common.exception.BizException;import com.ecommerce.common.exception.BasicCode;import org.springframework.beans.factory.BeanFactory;
import org.springframework.statemachine.StateMachine;

/**
 * 下午3:04 20/5/21
 *
 * <AUTHOR>
 */
public interface IStateMachineBuilder<S, E> {
    String getName();

    StateMachine<S, E> build(BeanFactory beanFactory) throws Exception;

    //订单状态机构造器
    String ORDER_BUILDER_NAME = "orderStateMachineBuilder";

    //发货状态构造器
    String TAKE_BUILDER_NAME = "takeStateMachineBuilder";
}
