package com.ecommerce.order.fsm.builder;

import com.alibaba.fastjson.JSON;
import com.ecommerce.order.fsm.even.OrderChangeEventEnum;
import com.ecommerce.order.fsm.status.OrderStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.action.Action;
import org.springframework.statemachine.config.StateMachineBuilder;
import org.springframework.statemachine.guard.Guard;
import org.springframework.statemachine.listener.StateMachineListener;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.EnumSet;

/**
 * 下午2:23 20/5/21
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class OrderStateMachineBuilder implements IStateMachineBuilder<OrderStatusEnum, OrderChangeEventEnum> {
    /**
     * 注入监听器
     */
    @Resource(name = "orderStateMachineListener")
    private StateMachineListener<OrderStatusEnum, OrderChangeEventEnum> orderStateMachineListener;

    /**
     * 注入状态分支条件
     */
    @Resource(name = "confirmOrderGuard")
    private Guard<OrderStatusEnum, OrderChangeEventEnum> confirmOrderGuard;

    @Resource(name = "confirmConcreteOrderGuard")
    private Guard<OrderStatusEnum, OrderChangeEventEnum> confirmConcreteOrderGuard;

    @Resource(name = "createPayOrderGuard")
    private Guard<OrderStatusEnum, OrderChangeEventEnum> createPayOrderGuard;

    @Resource(name = "confirmPayOrderGuard")
    private Guard<OrderStatusEnum, OrderChangeEventEnum> confirmPayOrderGuard;

    @Resource(name = "payConfirmGuard")
    private Guard<OrderStatusEnum, OrderChangeEventEnum> payConfirmGuard;

    @Resource(name = "erpCreateGuard")
    private Guard<OrderStatusEnum, OrderChangeEventEnum> erpCreateGuard;

    @Resource(name = "erpConcreteCreateGuard")
    private Guard<OrderStatusEnum, OrderChangeEventEnum> erpConcreteCreateGuard;

    @Resource(name = "supplementConfirmGuard")
    private Guard<OrderStatusEnum, OrderChangeEventEnum> supplementConfirmGuard;

    @Resource(name = "erpOrderCloseGuard")
    private Guard<OrderStatusEnum, OrderChangeEventEnum> erpOrderCloseGuard;

    @Resource(name = "erpOrderCompleteGuard")
    private Guard<OrderStatusEnum, OrderChangeEventEnum> erpOrderCompleteGuard;

    /**
     * 注入状态转移动作
     */
    @Resource(name = "errorHandlerAction")
    private Action<OrderStatusEnum, OrderChangeEventEnum> errorHandlerAction;

    @Resource(name = "orderCreateAction")
    private Action<OrderStatusEnum, OrderChangeEventEnum> orderCreateAction;

    @Resource(name = "orderNeedConfirmAction")
    private Action<OrderStatusEnum, OrderChangeEventEnum> orderNeedConfirmAction;

    @Resource(name = "orderWaitPaymentAction")
    private Action<OrderStatusEnum, OrderChangeEventEnum> orderWaitPaymentAction;

    @Resource(name = "orderConfirmAction")
    private Action<OrderStatusEnum, OrderChangeEventEnum> orderConfirmAction;

    @Resource(name = "payNeedConfirmAction")
    private Action<OrderStatusEnum, OrderChangeEventEnum> payNeedConfirmAction;

    @Resource(name = "erpNeedCreateAction")
    private Action<OrderStatusEnum, OrderChangeEventEnum> erpNeedCreateAction;

    @Resource(name = "orderPaySuccessAction")
    private Action<OrderStatusEnum, OrderChangeEventEnum> orderPaySuccessAction;

    @Resource(name = "confirmPayFailAction")
    private Action<OrderStatusEnum, OrderChangeEventEnum> confirmPayFailAction;

    @Resource(name = "confirmPaySuccessAction")
    private Action<OrderStatusEnum, OrderChangeEventEnum> confirmPaySuccessAction;

    @Resource(name = "orderWaitDeliveryAction")
    private Action<OrderStatusEnum, OrderChangeEventEnum> orderWaitDeliveryAction;

    @Resource(name = "erpCreateSuccessAction")
    private Action<OrderStatusEnum, OrderChangeEventEnum> erpCreateSuccessAction;

    @Resource(name = "notifyShippingAction")
    private Action<OrderStatusEnum, OrderChangeEventEnum> notifyShippingAction;

    @Resource(name = "orderCompleteAction")
    private Action<OrderStatusEnum, OrderChangeEventEnum> orderCompleteAction;

    @Resource(name = "refundNeedConfirmAction")
    private Action<OrderStatusEnum, OrderChangeEventEnum> refundNeedConfirmAction;

    @Resource(name = "orderCloseAction")
    private Action<OrderStatusEnum, OrderChangeEventEnum> orderCloseAction;

    @Resource(name = "supplementNeedConfirmAction")
    private Action<OrderStatusEnum, OrderChangeEventEnum> supplementNeedConfirmAction;

    @Resource(name = "confirmSupplementAction")
    private Action<OrderStatusEnum, OrderChangeEventEnum> confirmSupplementAction;

    @Resource(name = "erpOrderCloseAction")
    private Action<OrderStatusEnum, OrderChangeEventEnum> erpOrderCloseAction;

    @Resource(name = "erpCloseSuccessAction")
    private Action<OrderStatusEnum, OrderChangeEventEnum> erpCloseSuccessAction;

    @Resource(name = "orderCancelAction")
    private Action<OrderStatusEnum, OrderChangeEventEnum> orderCancelAction;

    @Resource(name = "erpOrderCompleteAction")
    private Action<OrderStatusEnum, OrderChangeEventEnum> erpOrderCompleteAction;

    @Resource(name = "erpCompleteSuccessAction")
    private Action<OrderStatusEnum, OrderChangeEventEnum> erpCompleteSuccessAction;


    @Override
    public String getName() {
        return ORDER_BUILDER_NAME;
    }

    @Override
    public StateMachine<OrderStatusEnum, OrderChangeEventEnum> build(BeanFactory beanFactory) throws Exception {
        StateMachineBuilder.Builder<OrderStatusEnum, OrderChangeEventEnum> builder = StateMachineBuilder.builder();

        //设置状态机Id
        builder.configureConfiguration()
                .withConfiguration()
                .autoStartup(true)
                .beanFactory(beanFactory)
                .machineId(ORDER_BUILDER_NAME + "Id")
                .listener(orderStateMachineListener);

        //初始化状态机，并指定状态集合
        builder.configureStates()
                .withStates()
                //初始状态
                .initial(OrderStatusEnum.CREATE)
                //创建->待卖家确认||待支付||待发货
                .choice(OrderStatusEnum.ORDER_CREATE_JUMP)
                //卖家确认->待支付||待发货
                .choice(OrderStatusEnum.ORDER_CONFIRM_JUMP)
                //待支付->支付确认中||ERP创建中||待发货
                .choice(OrderStatusEnum.PAYMENT_SUCCESS_JUMP)
                //待发货|发货中 -> 支付确认中||已关闭
                .choice(OrderStatusEnum.ORDER_CLOSE_JUMP)
                //待发货|发货中 -> 支付确认中||已完成
                .choice(OrderStatusEnum.ORDER_COMPLETE_JUMP)
                //结束状态(完成、关闭、取消)
                .end(OrderStatusEnum.CANCEL)
                .end(OrderStatusEnum.CLOSED)
                .end(OrderStatusEnum.COMPLETED)
                .states(EnumSet.allOf(OrderStatusEnum.class));

        //定义状态机节点，即迁移动作
        builder.configureTransitions()
                //订单的初始状态为虚拟状态为了满足节点跳转
                .withExternal()
                .source(OrderStatusEnum.CREATE)
                //先转换为瞬时状态，再由选择分支进行处理
                .target(OrderStatusEnum.ORDER_CREATE_JUMP)
                .event(OrderChangeEventEnum.CREATE_ORDER)
                .action(orderCreateAction, errorHandlerAction)

                //瞬时状态根据条件判断执行对用的action
                .and()
                .withChoice()
                .source(OrderStatusEnum.ORDER_CREATE_JUMP)
                .first(OrderStatusEnum.CONFIRMING, confirmOrderGuard, orderNeedConfirmAction, errorHandlerAction)
                .then(OrderStatusEnum.WAIT_PAYMENT, createPayOrderGuard, orderWaitPaymentAction, errorHandlerAction)
                .last(OrderStatusEnum.WAIT_DELIVERED, orderWaitDeliveryAction, errorHandlerAction)

                //确认订单
                .and()
                .withExternal()
                .source(OrderStatusEnum.CONFIRMING)
                .target(OrderStatusEnum.ORDER_CONFIRM_JUMP)
                .event(OrderChangeEventEnum.CONFIRM_ORDER)
                .action(orderConfirmAction, errorHandlerAction)

                .and()
                .withChoice()
                .source(OrderStatusEnum.ORDER_CONFIRM_JUMP)
                .first(OrderStatusEnum.IN_ERP_CREATE_CONFIRM, erpConcreteCreateGuard, erpNeedCreateAction, errorHandlerAction)
                .then(OrderStatusEnum.WAIT_PAYMENT, confirmPayOrderGuard, orderWaitPaymentAction, errorHandlerAction)
                .last(OrderStatusEnum.WAIT_DELIVERED, orderWaitDeliveryAction, errorHandlerAction)

                //取消订单
                .and()
                .withExternal()
                .source(OrderStatusEnum.WAIT_PAYMENT)
                .target(OrderStatusEnum.CANCEL)
                .event(OrderChangeEventEnum.CANCEL_ORDER)
                .action(orderCancelAction, errorHandlerAction)

                //取消订单
                .and()
                .withExternal()
                .source(OrderStatusEnum.CONFIRMING)
                .target(OrderStatusEnum.CANCEL)
                .event(OrderChangeEventEnum.CANCEL_ORDER)
                .action(orderCancelAction, errorHandlerAction)

                //支付订单
                .and()
                .withExternal()
                .source(OrderStatusEnum.WAIT_PAYMENT)
                .target(OrderStatusEnum.IN_PAYMENT)
                .event(OrderChangeEventEnum.PAY_ORDER)
                .guard(payConfirmGuard)
                .action(payNeedConfirmAction, errorHandlerAction)

                //线上支付成功
                .and()
                .withExternal()
                .source(OrderStatusEnum.WAIT_PAYMENT)
                .target(OrderStatusEnum.PAYMENT_SUCCESS_JUMP)
                .event(OrderChangeEventEnum.PAY_SUCCESS)
                .action(orderPaySuccessAction, errorHandlerAction)

                //线下支付驳回
                .and()
                .withExternal()
                .source(OrderStatusEnum.IN_PAYMENT)
                .target(OrderStatusEnum.WAIT_PAYMENT)
                .event(OrderChangeEventEnum.CONFIRM_PAY_FAIL)
                .action(confirmPayFailAction, errorHandlerAction)

                //线下支付确认
                .and()
                .withExternal()
                .source(OrderStatusEnum.IN_PAYMENT)
                .target(OrderStatusEnum.PAYMENT_SUCCESS_JUMP)
                .event(OrderChangeEventEnum.CONFIRM_PAY_SUCCESS)
                .action(confirmPaySuccessAction, errorHandlerAction)

                 //支付成功分支选择
                .and()
                .withChoice()
                .source(OrderStatusEnum.PAYMENT_SUCCESS_JUMP)
                //是否需要确认
                .first(OrderStatusEnum.CONFIRMING, confirmConcreteOrderGuard, orderNeedConfirmAction, errorHandlerAction)
                //同步创建ERP提货单
                .then(OrderStatusEnum.IN_ERP_CREATE_CONFIRM, erpCreateGuard, erpNeedCreateAction, errorHandlerAction)
                //支付完成
                .last(OrderStatusEnum.WAIT_DELIVERED, orderWaitDeliveryAction, errorHandlerAction)

                //ERP创建成功
                .and()
                .withExternal()
                .source(OrderStatusEnum.IN_ERP_CREATE_CONFIRM)
                .target(OrderStatusEnum.WAIT_DELIVERED)
                .event(OrderChangeEventEnum.ERP_CREATE_SUCCESS)
                .action(erpCreateSuccessAction, errorHandlerAction)

                //物流通知发货
                .and()
                .withExternal()
                .source(OrderStatusEnum.WAIT_DELIVERED)
                .target(OrderStatusEnum.IN_DELIVERY)
                .event(OrderChangeEventEnum.NOTIFY_SHIPPING)
                .action(notifyShippingAction, errorHandlerAction)

                //完成订单(待发货)
                .and()
                .withExternal()
                .source(OrderStatusEnum.WAIT_DELIVERED)
                .target(OrderStatusEnum.ORDER_COMPLETE_JUMP)
                .event(OrderChangeEventEnum.COMPLETE_ORDER)

                //完成订单(发货中)
                .and()
                .withExternal()
                .source(OrderStatusEnum.IN_DELIVERY)
                .target(OrderStatusEnum.ORDER_COMPLETE_JUMP)
                .event(OrderChangeEventEnum.COMPLETE_ORDER)

                //完成订单(支付确认中)
                .and()
                .withExternal()
                .source(OrderStatusEnum.IN_PAYMENT)
                .target(OrderStatusEnum.ORDER_COMPLETE_JUMP)
                .event(OrderChangeEventEnum.COMPLETE_ORDER)

                .and()
                .withChoice()
                .source(OrderStatusEnum.ORDER_COMPLETE_JUMP)
                //需要同步完成ERP订单
                .first(OrderStatusEnum.ERP_COMPLETING, erpOrderCompleteGuard, erpOrderCompleteAction, errorHandlerAction)
                //直接完成订单
                .last(OrderStatusEnum.COMPLETED, orderCompleteAction, errorHandlerAction)

                //ERP完成成功
                .and()
                .withExternal()
                .source(OrderStatusEnum.ERP_COMPLETING)
                .target(OrderStatusEnum.COMPLETED)
                .event(OrderChangeEventEnum.ERP_COMPLETE_SUCCESS)
                .action(erpCompleteSuccessAction, errorHandlerAction)

                //关闭订单(待发货)
                .and()
                .withExternal()
                .source(OrderStatusEnum.WAIT_DELIVERED)
                .target(OrderStatusEnum.ORDER_CLOSE_JUMP)
                .event(OrderChangeEventEnum.CLOSE_ORDER)

                //关闭订单(发货中)
                .and()
                .withExternal()
                .source(OrderStatusEnum.IN_DELIVERY)
                .target(OrderStatusEnum.ORDER_CLOSE_JUMP)
                .event(OrderChangeEventEnum.CLOSE_ORDER)

                //关闭订单(支付确认中)
                .and()
                .withExternal()
                .source(OrderStatusEnum.IN_PAYMENT)
                .target(OrderStatusEnum.ORDER_CLOSE_JUMP)
                .event(OrderChangeEventEnum.CLOSE_ORDER)

                //关闭运单(确认中-专用)
                .and()
                .withExternal()
                .source(OrderStatusEnum.CONFIRMING)
                .target(OrderStatusEnum.ORDER_CLOSE_JUMP)
                .event(OrderChangeEventEnum.CLOSE_ORDER)

                //关闭成功分支选择
                .and()
                .withChoice()
                .source(OrderStatusEnum.ORDER_CLOSE_JUMP)
                //需要同步关闭ERP订单
                .first(OrderStatusEnum.ERP_CLOSING, erpOrderCloseGuard, erpOrderCloseAction, errorHandlerAction)
                //直接关闭订单
                .last(OrderStatusEnum.CLOSED, orderCloseAction, errorHandlerAction)

                //ERP关闭成功
                .and()
                .withExternal()
                .source(OrderStatusEnum.ERP_CLOSING)
                .target(OrderStatusEnum.CLOSED)
                .event(OrderChangeEventEnum.ERP_CLOSE_SUCCESS)
                .action(erpCloseSuccessAction, errorHandlerAction)

                //订单需要确认退款(待发货->支付确认中)
                .and()
                .withExternal()
                .source(OrderStatusEnum.WAIT_DELIVERED)
                .target(OrderStatusEnum.IN_PAYMENT)
                .event(OrderChangeEventEnum.ORDER_REFUND)
                .action(refundNeedConfirmAction, errorHandlerAction)

                //订单需要确认退款(发货中->支付确认中)
                .and()
                .withExternal()
                .source(OrderStatusEnum.IN_DELIVERY)
                .target(OrderStatusEnum.IN_PAYMENT)
                .event(OrderChangeEventEnum.ORDER_REFUND)
                .action(refundNeedConfirmAction, errorHandlerAction)

                //订单补款(需要确认)
                .and()
                .withExternal()
                .source(OrderStatusEnum.IN_DELIVERY)
                .target(OrderStatusEnum.IN_PAYMENT)
                .guard(supplementConfirmGuard)
                .event(OrderChangeEventEnum.ORDER_SUPPLEMENT)
                .action(supplementNeedConfirmAction, errorHandlerAction)

                .and()
                .withExternal()
                .source(OrderStatusEnum.IN_PAYMENT)
                .target(OrderStatusEnum.IN_DELIVERY)
                .event(OrderChangeEventEnum.CONFIRM_SUPPLEMENT)
                .action(confirmSupplementAction, errorHandlerAction);

        log.info("stateMachine:" + JSON.toJSONString(builder.build()));

        return builder.build();
    }
}
