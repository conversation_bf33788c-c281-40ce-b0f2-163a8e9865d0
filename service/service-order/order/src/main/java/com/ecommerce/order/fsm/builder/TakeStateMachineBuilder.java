package com.ecommerce.order.fsm.builder;

import com.alibaba.fastjson.JSON;
import com.ecommerce.order.fsm.action.takeinfo.*;
import com.ecommerce.order.fsm.even.TakeChangeEventEnum;
import com.ecommerce.order.fsm.guard.takeinfo.*;
import com.ecommerce.order.fsm.status.TakeStatusEnum;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.action.Action;
import org.springframework.statemachine.config.StateMachineBuilder;
import org.springframework.statemachine.guard.Guard;
import org.springframework.statemachine.listener.StateMachineListener;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

/**
 * 发货单状态机
 */
@Slf4j
@Component
public class TakeStateMachineBuilder implements IStateMachineBuilder<TakeStatusEnum, TakeChangeEventEnum> {

    /**
     * 注入监听器
     */
    @Resource(name = "takeStateMachineListener")
    private StateMachineListener<TakeStatusEnum, TakeChangeEventEnum> takeStateMachineListener;

    /**
     * 注入状态分支条件(需要卖家确认)
     * @see NeedSellerConfirmGuard
     */
    @Resource(name = "needSellerConfirmGuard")
    private Guard<TakeStatusEnum, TakeChangeEventEnum> needSellerConfirmGuard;

    /**
     * 注入状态分支条件(发货单传物流)
     * @see SendToLogisticGuard
     */
    @Resource(name = "sendToLogisticGuard")
    private Guard<TakeStatusEnum, TakeChangeEventEnum> sendToLogisticGuard;
    /**
     * 判断是否需要支付
     * @see NeedBuyerPayGuard
     */
    @Resource(name = "needBuyerPayGuard")
    private Guard<TakeStatusEnum, TakeChangeEventEnum> needBuyerPayGuard;

    /**
     * 判断支付是否需要卖家确认
     * @see TakePayConfirmGuard
     */
    @Resource(name = "takePayConfirmGuard")
    private Guard<TakeStatusEnum, TakeChangeEventEnum> takePayConfirmGuard;

    /**
     * 判断支付是否补款确认
     * @see SupplementPayConfirmGuard
     */
    @Resource(name = "supplementPayConfirmGuard")
    private Guard<TakeStatusEnum, TakeChangeEventEnum> supplementPayConfirmGuard;

    /**
     * 注入状态转移动作
     * @see TakeErrorHandlerAction
     */
    @Resource(name = "takeErrorHandlerAction")
    private Action<TakeStatusEnum, TakeChangeEventEnum> takeErrorHandlerAction;
    /**
     * @see TakeCreateAction
     */
    @Resource(name = "takeCreateAction")
    private Action<TakeStatusEnum, TakeChangeEventEnum> takeCreateAction;
    /**
     * @see TakeSellerConfirmAction
     */
    @Resource(name = "takeSellerConfirmAction")
    private Action<TakeStatusEnum, TakeChangeEventEnum> takeSellerConfirmAction;

    /**
     * @see TakeSellerSupplementConfirmAction
     */
    @Resource(name = "takeSellerSupplementConfirmAction")
    private Action<TakeStatusEnum, TakeChangeEventEnum> takeSellerSupplementConfirmAction;

    /**
     * @see TakePendingAction
     */
    @Resource(name = "takePendingAction")
    private Action<TakeStatusEnum, TakeChangeEventEnum> takePendingAction;
    /**
     * @see TakeConfirmAction
     */
    @Resource(name = "takeConfirmAction")
    private Action<TakeStatusEnum, TakeChangeEventEnum> takeConfirmAction;
    /**
     * @see SendToLogisticFailAction
     */
    @Resource(name = "sendToLogisticFailAction")
    private Action<TakeStatusEnum, TakeChangeEventEnum> sendToLogisticFailAction;
    /**
     * @see SendToLogisticSuccessAction
     */
    @Resource(name = "sendToLogisticSuccessAction")
    private Action<TakeStatusEnum, TakeChangeEventEnum> sendToLogisticSuccessAction;
    /**
     * @see TakeWaitPaymentAction
     */
    @Resource(name = "takeWaitPaymentAction")
    private Action<TakeStatusEnum, TakeChangeEventEnum> takeWaitPaymentAction;
    /**
     * @see TakePaySuccessAction
     */
    @Resource(name = "takePaySuccessAction")
    private Action<TakeStatusEnum, TakeChangeEventEnum> takePaySuccessAction;
    /**
     * @see TakePayNeedConfirmAction
     */
    @Resource(name = "takePayNeedConfirmAction")
    private Action<TakeStatusEnum, TakeChangeEventEnum> takePayNeedConfirmAction;
    /**
     * @see TakePayConfirmSuccessAction
     */
    @Resource(name = "takePayConfirmSuccessAction")
    private Action<TakeStatusEnum, TakeChangeEventEnum> takePayConfirmSuccessAction;
    /**
     * @see TakePayConfirmFailAction
     */
    @Resource(name = "takePayConfirmFailAction")
    private Action<TakeStatusEnum, TakeChangeEventEnum> takePayConfirmFailAction;
    /**
     * @see TakeDeliveringAction
     */
    @Resource(name = "takeDeliveringAction")
    private Action<TakeStatusEnum, TakeChangeEventEnum> takeDeliveringAction;
    /**
     * @see TakeCloseAction
     */
    @Resource(name = "takeCloseAction")
    private Action<TakeStatusEnum, TakeChangeEventEnum> takeCloseAction;
    /**
     * @see TakeFinishAction
     */
    @Resource(name = "takeFinishAction")
    private Action<TakeStatusEnum, TakeChangeEventEnum> takeFinishAction;
    /**
     * @see TakeCancelAction
     */
    @Resource(name = "takeCancelAction")
    private Action<TakeStatusEnum, TakeChangeEventEnum> takeCancelAction;

    @Override
    public String getName() {
        return TAKE_BUILDER_NAME;
    }

    @Override
    public StateMachine<TakeStatusEnum, TakeChangeEventEnum> build(BeanFactory beanFactory) throws Exception {
        StateMachineBuilder.Builder<TakeStatusEnum, TakeChangeEventEnum> builder = StateMachineBuilder.builder();

        //设置状态机Id
        builder.configureConfiguration()
                .withConfiguration()
                .autoStartup(true)
                .beanFactory(beanFactory)
                .machineId(TAKE_BUILDER_NAME + "Id")
                .listener(takeStateMachineListener);

        //初始化状态机，并指定状态集合
        builder.configureStates()
                .withStates()
                //初始状态 新建
                .initial(TakeStatusEnum.CREATE)
                .choice(TakeStatusEnum.CREATE_JUMP)
                .choice(TakeStatusEnum.CONFIRMED_JUMP)
                .choice(TakeStatusEnum.PAYMENT_SUCCESS_JUMP)
                .choice(TakeStatusEnum.PENDING_CONFIRMED_JUMP)
                .end(TakeStatusEnum.CLOSED)
                .end(TakeStatusEnum.FINISHED)
                .end(TakeStatusEnum.CANCELED)
                .states(Sets.newHashSet(TakeStatusEnum.values()));

        //定义状态机节点，即迁移动作
        builder.configureTransitions()
                //发货单的初始状态为虚拟状态为了满足节点跳转
                //新建 -> 待确认、待支付、已确认、待发货
                .withExternal()
                .source(TakeStatusEnum.CREATE)
                //先转换为瞬时状态，再由选择分支进行处理
                .target(TakeStatusEnum.CREATE_JUMP)
                .event(TakeChangeEventEnum.CREATE_TAKE)
                .action(takeCreateAction, takeErrorHandlerAction)

                .and()
                .withChoice()
                .source(TakeStatusEnum.CREATE_JUMP)
                //先支付 后确认  娟姐确认2021.1.4
                //不需要确认 需要支付 (卖家创建) ->待支付
                .first(TakeStatusEnum.WAIT_PAYMENT, needBuyerPayGuard, takeWaitPaymentAction, takeErrorHandlerAction)
                //需要发货确认(买家创建) ->待确认
                .then(TakeStatusEnum.PENDING_CONFIRMED, needSellerConfirmGuard, takeSellerConfirmAction, takeErrorHandlerAction)
                //不需要确认 不需要支付，同步物流成功->待发货
                .then(TakeStatusEnum.PENDING_DELIVERY, sendToLogisticGuard, sendToLogisticSuccessAction, takeErrorHandlerAction)
                //不需要确认 不需要支付，同步物流失败->已确认
                .last(TakeStatusEnum.SELLER_CONFIRMED, sendToLogisticFailAction, takeErrorHandlerAction)

                //待确认 -> 待支付、已确认、待发货
                .and()
                .withExternal()
                .source(TakeStatusEnum.PENDING_CONFIRMED)
                //先转换为瞬时状态，再由选择分支进行处理
                .target(TakeStatusEnum.CONFIRMED_JUMP)
                .event(TakeChangeEventEnum.CONFIRM_TAKE)
                .action(takeConfirmAction, takeErrorHandlerAction)

                .and()
                .withChoice()
                .source(TakeStatusEnum.CONFIRMED_JUMP)
                //不需要支付，同步物流成功->待发货
                .first(TakeStatusEnum.PENDING_DELIVERY, sendToLogisticGuard, sendToLogisticSuccessAction, takeErrorHandlerAction)
                //不需要支付，同步物流失败->已确认
                .last(TakeStatusEnum.SELLER_CONFIRMED, sendToLogisticFailAction, takeErrorHandlerAction)

                //支付发货单
                //待支付 -> 支付中（待确认）、已确认、待发货
                .and()
                .withExternal()
                .source(TakeStatusEnum.WAIT_PAYMENT)
                .target(TakeStatusEnum.IN_PAYMENT)
                .event(TakeChangeEventEnum.PAY_TAKE)
                .guard(takePayConfirmGuard) //感觉没有必要
                .action(takePayNeedConfirmAction, takeErrorHandlerAction)

                //待支付 -> 支付成功瞬时状态
                .and()
                .withExternal()
                .source(TakeStatusEnum.WAIT_PAYMENT)
                .target(TakeStatusEnum.PAYMENT_SUCCESS_JUMP)
                .event(TakeChangeEventEnum.PAY_SUCCESS)
                .action(takePaySuccessAction, takeErrorHandlerAction)

                //支付中 ->支付确认驳回
                .and()
                .withExternal()
                .source(TakeStatusEnum.IN_PAYMENT)
                .target(TakeStatusEnum.WAIT_PAYMENT)
                .event(TakeChangeEventEnum.CONFIRM_PAY_FAIL)
                .action(takePayConfirmFailAction, takeErrorHandlerAction)

                //支付中 ->支付确认通过
                .and()
                .withExternal()
                .source(TakeStatusEnum.IN_PAYMENT)
                .target(TakeStatusEnum.PAYMENT_SUCCESS_JUMP)
                .event(TakeChangeEventEnum.CONFIRM_PAY_SUCCESS)
                .action(takePayConfirmSuccessAction, takeErrorHandlerAction)

                //支付成功瞬时状态 ->待发货、已确认
                .and()
                .withChoice()
                .source(TakeStatusEnum.PAYMENT_SUCCESS_JUMP)
                //是否补款确认
                .first(TakeStatusEnum.DELIVERING, supplementPayConfirmGuard, takeSellerSupplementConfirmAction, takeErrorHandlerAction)
                //需要发货确认(买家创建) ->待确认
                .then(TakeStatusEnum.PENDING_CONFIRMED, needSellerConfirmGuard, takeSellerConfirmAction, takeErrorHandlerAction)
                //同步物流成功->待发货
                .then(TakeStatusEnum.PENDING_DELIVERY, sendToLogisticGuard, sendToLogisticSuccessAction, takeErrorHandlerAction)
                //不需要确认(卖家创建)，同步物流失败->已确认
                .last(TakeStatusEnum.SELLER_CONFIRMED, sendToLogisticFailAction, takeErrorHandlerAction)

                //待支付 ->不需要支付了（关闭其它发货单导致订单已经支付金额足以支付当前发货单时触发）
                .and()
                .withExternal()
                .source(TakeStatusEnum.WAIT_PAYMENT)
                .event(TakeChangeEventEnum.CHANGE_TO_NOT_NEED_PAY)
                .target(TakeStatusEnum.PENDING_CONFIRMED_JUMP)
                .action(takePendingAction, takeErrorHandlerAction)
                //支付成功瞬时状态 ->待发货、已确认
                .and()
                .withChoice()
                .source(TakeStatusEnum.PENDING_CONFIRMED_JUMP)
                //需要发货确认(买家创建) ->待确认
                .first(TakeStatusEnum.PENDING_CONFIRMED, needSellerConfirmGuard, takeSellerConfirmAction, takeErrorHandlerAction)
                //同步物流成功->待发货
                .then(TakeStatusEnum.PENDING_DELIVERY, sendToLogisticGuard, sendToLogisticSuccessAction, takeErrorHandlerAction)
                //不需要确认(卖家创建)，同步物流失败->已确认
                .last(TakeStatusEnum.SELLER_CONFIRMED, sendToLogisticFailAction, takeErrorHandlerAction)

                //已确认 -> 待发货
                .and()
                .withExternal()
                .source(TakeStatusEnum.SELLER_CONFIRMED)
                .target(TakeStatusEnum.PENDING_DELIVERY)
                .event(TakeChangeEventEnum.SEND_LOGISTICS)
                .guard(sendToLogisticGuard)
                .action(sendToLogisticSuccessAction, takeErrorHandlerAction)

                //取消发货单
                .and()
                .withExternal()
                .source(TakeStatusEnum.WAIT_PAYMENT)
                .target(TakeStatusEnum.CANCELED)
                .event(TakeChangeEventEnum.CANCEL_TAKE)
                .action(takeCancelAction, takeErrorHandlerAction)

                //待确认 -> 已取消
                .and()
                .withExternal()
                .source(TakeStatusEnum.PENDING_CONFIRMED)
                .target(TakeStatusEnum.CANCELED)
                .event(TakeChangeEventEnum.CANCEL_TAKE)
                .action(takeCancelAction, takeErrorHandlerAction)

                //待发货 -> 发货中
                .and()
                .withExternal()
                .source(TakeStatusEnum.PENDING_DELIVERY)
                .target(TakeStatusEnum.DELIVERING)
                .event(TakeChangeEventEnum.DELIVERING_TAKE)
                .action(takeDeliveringAction, takeErrorHandlerAction)

                //待发货 -> 已关闭
                .and()
                .withExternal()
                .source(TakeStatusEnum.PENDING_DELIVERY)
                .target(TakeStatusEnum.CLOSED)
                .event(TakeChangeEventEnum.CLOSE_TAKE)
                .action(takeCloseAction, takeErrorHandlerAction)

                //发货中 -> 已关闭
                .and()
                .withExternal()
                .source(TakeStatusEnum.DELIVERING)
                .target(TakeStatusEnum.CLOSED)
                .event(TakeChangeEventEnum.CLOSE_TAKE)
                .action(takeCloseAction, takeErrorHandlerAction)

                //发货中 -> 已完成
                .and()
                .withExternal()
                .source(TakeStatusEnum.DELIVERING)
                .target(TakeStatusEnum.FINISHED)
                .event(TakeChangeEventEnum.FINISH_TAKE)
                .action(takeFinishAction, takeErrorHandlerAction);

        log.info("stateMachine:" + JSON.toJSONString(builder.build()));
        return builder.build();
    }
}
