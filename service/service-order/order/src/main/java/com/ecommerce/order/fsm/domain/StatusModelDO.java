package com.ecommerce.order.fsm.domain;

import com.google.common.collect.Maps;
import lombok.Data;

import java.util.Map;

/**
 * 上午11:50 20/5/22
 *
 * <AUTHOR>
 */
@Data
public class StatusModelDO {
    /**
     * 业务ID对象
     */
    private String bizId;

    /**
     * 当前状态
     */
    private String currentState;

    /**
     * 扩展参数对象
     */
    private Map<String, Object> externalParams;

    public void addParam(String key,Object value){
        externalParams = externalParams == null ? Maps.newConcurrentMap() : externalParams;
        externalParams.put(key,value);
    }

    public Object getParam(String key){
        if( externalParams == null ){
            return null;
        }
        return externalParams.get(key);
    }

}
