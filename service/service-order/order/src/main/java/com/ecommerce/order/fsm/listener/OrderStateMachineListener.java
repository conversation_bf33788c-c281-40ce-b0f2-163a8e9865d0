package com.ecommerce.order.fsm.listener;

import com.alibaba.fastjson.JSON;
import com.ecommerce.order.fsm.even.OrderChangeEventEnum;
import com.ecommerce.order.fsm.status.OrderStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.listener.StateMachineListenerAdapter;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 * Date: Create in 上午11:15 20/6/9
 */
@Slf4j
@Component("orderStateMachineListener")
public class OrderStateMachineListener extends StateMachineListenerAdapter<OrderStatusEnum, OrderChangeEventEnum> {

    /**
     * 扩展状态变更
     * @param key 变量Key
     * @param value 变量对象
     */
    @Override
    public void extendedStateChanged(Object key, Object value) {
        log.info("extendedStateChanged:" + key + "|" + JSON.toJSONString(value));
    }
}
