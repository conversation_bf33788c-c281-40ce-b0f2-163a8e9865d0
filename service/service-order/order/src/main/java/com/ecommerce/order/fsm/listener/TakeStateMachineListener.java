package com.ecommerce.order.fsm.listener;

import com.alibaba.fastjson.JSON;
import com.ecommerce.order.fsm.even.TakeChangeEventEnum;
import com.ecommerce.order.fsm.status.TakeStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.listener.StateMachineListenerAdapter;
import org.springframework.statemachine.state.State;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 * Date: Create in 上午11:15 20/6/9
 */
@Slf4j
@Component("takeStateMachineListener")
public class TakeStateMachineListener extends StateMachineListenerAdapter<TakeStatusEnum, TakeChangeEventEnum> {

    /**
     * 扩展状态变更
     * @param key 变量Key
     * @param value 变量对象
     */
    @Override
    public void extendedStateChanged(Object key, Object value) {
        log.info("takeStateMachineListener extendedStateChanged:" + key + "|" + JSON.toJSONString(value));
    }

    @Override
    public void stateChanged(State<TakeStatusEnum, TakeChangeEventEnum> from, State<TakeStatusEnum, TakeChangeEventEnum> to) {
        if(  from != null && from.getId() != null && to != null && to.getId() != null ){
            log.info("takeStateMachineListener stateChanged from:{}({}) to:{}({})",from.getId().getCode(),from.getId().getMessage(),to.getId().getCode(),to.getId().getMessage());
        }
    }
}
