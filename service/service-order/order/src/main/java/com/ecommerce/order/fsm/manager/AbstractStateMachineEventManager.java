package com.ecommerce.order.fsm.manager;

import com.alibaba.fastjson.JSON;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BasicRuntimeException;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.exception.BusinessOperationException;
import com.ecommerce.order.fsm.StateMachineBuildFactory;
import com.ecommerce.order.fsm.StateMachineConstants;
import com.ecommerce.order.fsm.domain.PersisterDO;
import com.ecommerce.order.fsm.domain.StatusModelDO;
import com.ecommerce.order.fsm.even.TakeChangeEventEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.state.State;
import org.springframework.statemachine.support.StateMachineUtils;
import org.springframework.statemachine.transition.Transition;
import org.springframework.statemachine.trigger.DefaultTriggerContext;
import org.springframework.statemachine.trigger.Trigger;

/**
 * 上午11:58 20/5/22
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractStateMachineEventManager<S, E> implements IStateMachineEventManager<E> {

    @Autowired
    private StateMachineBuildFactory<S, E> stateMachineBuildFactory;

    public abstract void stateMachinePersist(StateMachine<S, E> stateMachine, PersisterDO persisterDO) throws Exception;

    public abstract StateMachine<S, E> stateMachineRestore(StateMachine<S, E> stateMachine, PersisterDO persisterDO) throws Exception;

    @Override
    @SuppressWarnings("unchecked")
    public <R> R sendStatusChangeEvent(StatusModelDO statusModelDO, E eventEnum) {
        //获取当前状态机
        StateMachine<S, E> stateMachine = getStateMachine(statusModelDO);
        //执行状态机事件
        Boolean result = statusChangeExecute(stateMachine, statusModelDO, eventEnum);
        if (Boolean.FALSE.equals(result)) {
            throw new BasicRuntimeException("状态机状态执行失败");
        }
        //执行过程异常处理
        RuntimeException exception = (RuntimeException)stateMachine.getExtendedState().getVariables().get(RuntimeException.class);
        if (exception != null) {
            //业务异常
            if (exception instanceof BizException) {
                throw exception;
            //其他异常
            } else {
                log.error("状态机内部异常:{}",exception.getMessage(),exception);
                if( eventEnum instanceof TakeChangeEventEnum enum1 ){
                    throw new BizException(BasicCode.UNDEFINED_ERROR, enum1.getDesc() +"处理异常");
                }
                throw new BizException(BasicCode.UNDEFINED_ERROR, "状态机内部异常，请检查接口程序");
            }
        }
        try {
            //持久化状态机
            PersisterDO persisterDO = new PersisterDO();
            persisterDO.setBizId(statusModelDO.getBizId());
            stateMachinePersist(stateMachine, persisterDO);
        } catch (Exception e) {
            log.error("状态机持久化失败:{}",e.getMessage(),e);
            throw new BasicRuntimeException("状态机持久化失败");
        }

        return (R)stateMachine.getExtendedState().getVariables().get(StateMachineConstants.RETURN_PARAM);
    }

    /**
     * 获取状态机对象
     * @param statusModelDO 状态模型对象
     * @return 状态机
     */
    private StateMachine<S, E> getStateMachine(StatusModelDO statusModelDO) {
        log.info("StatusModelDO:" + JSON.toJSONString(statusModelDO));
        //构造状态机模板
        StateMachine<S, E> stateMachine;
        try {
            //构造初始化状态机
            StateMachine<S, E>  srcStateMachine = stateMachineBuildFactory.createStateMachine(getStateMachineType());
            //构造当前执行状态
            PersisterDO persisterDO = new PersisterDO();
            persisterDO.setBizId(statusModelDO.getBizId());
            persisterDO.setCurrentState(statusModelDO.getCurrentState());
            stateMachine = stateMachineRestore(srcStateMachine, persisterDO);
        } catch (Exception e) {
            throw new BasicRuntimeException("初始化状态机失败");
        }
        if (stateMachine == null) {
            throw new BasicRuntimeException("没有找到可用的状态机");
        }
        log.info("stateMachine:" + JSON.toJSONString(stateMachine));

        return stateMachine;
    }

    private boolean statusChangeExecute(StateMachine<S, E> stateMachine, StatusModelDO statusModelDO, E eventEnum) {
        log.info("事件请求statusChangeExecute:" + eventEnum);

        //构建消息对象
        Message<E> eventMsg = MessageBuilder.withPayload(eventEnum)
                .setHeader(StateMachineConstants.STATE_MODEL_DTO, statusModelDO).build();
        if (!acceptEvent(stateMachine, eventMsg)) {
            throw new BasicRuntimeException("找不到对应状态机事件触发定义");
        }

        return stateMachine.sendEvent(eventMsg);
    }

    /**
     * 事件执行条件判断
     */
    private static <S, E> boolean acceptEvent(StateMachine<S, E> stateMachine, Message<E> eventMsg) {
        //获取当前状态
        State<S, E> currentState  = stateMachine.getState();

        for (Transition<S, E> transition : stateMachine.getTransitions()) {
            State<S, E> source = transition.getSource();
            Trigger<S, E> trigger = transition.getTrigger();
            if (currentState != null && trigger != null &&
                    StateMachineUtils.containsAtleastOne(source.getIds(), currentState.getIds()) &&
                    trigger.evaluate(new DefaultTriggerContext<>(eventMsg.getPayload()))) {
                return true;
            }
        }

        return false;
    }

    /**
     * 获取状态机类型
     * @return 状态机类型
     */
    public abstract String getStateMachineType();
}
