package com.ecommerce.order.fsm.manager;

import com.ecommerce.order.fsm.builder.IStateMachineBuilder;
import com.ecommerce.order.fsm.domain.PersisterDO;
import com.ecommerce.order.fsm.even.OrderChangeEventEnum;
import com.ecommerce.order.fsm.status.OrderStatusEnum;
import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.persist.StateMachinePersister;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

/**
 * 下午4:16 20/5/22
 *
 * <AUTHOR>
 */
@Component
public class OrderStateMachineEventManager extends AbstractStateMachineEventManager<OrderStatusEnum, OrderChangeEventEnum> {

    @Resource(name = "orderStateMachinePersister")
    private StateMachinePersister<OrderStatusEnum, OrderChangeEventEnum, PersisterDO> stateMachinePersist;

    @Override
    public void stateMachinePersist(StateMachine<OrderStatusEnum, OrderChangeEventEnum> stateMachine,
                                    PersisterDO persisterDO) throws Exception {
        stateMachinePersist.persist(stateMachine, persisterDO);
    }

    @Override
    public StateMachine<OrderStatusEnum, OrderChangeEventEnum> stateMachineRestore(
            StateMachine<OrderStatusEnum, OrderChangeEventEnum> stateMachine,
            PersisterDO persisterDO) throws Exception {

        persisterDO.setMachineId(IStateMachineBuilder.ORDER_BUILDER_NAME);
        return stateMachinePersist.restore(stateMachine, persisterDO);
    }

    @Override
    public String getStateMachineType() {
        return IStateMachineBuilder.ORDER_BUILDER_NAME;
    }
}
