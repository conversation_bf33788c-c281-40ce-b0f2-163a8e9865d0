package com.ecommerce.order.fsm.manager;

import com.ecommerce.order.fsm.builder.IStateMachineBuilder;
import com.ecommerce.order.fsm.domain.PersisterDO;
import com.ecommerce.order.fsm.even.TakeChangeEventEnum;
import com.ecommerce.order.fsm.status.TakeStatusEnum;
import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.persist.StateMachinePersister;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

/**
 * 下午4:16 20/5/22
 *
 * <AUTHOR>
 */
@Component
public class TakeStateMachineEventManager extends AbstractStateMachineEventManager<TakeStatusEnum, TakeChangeEventEnum> {

    @Resource(name = "takeStateMachinePersister")
    private StateMachinePersister<TakeStatusEnum, TakeChangeEventEnum, PersisterDO> stateMachinePersist;

    @Override
    public void stateMachinePersist(StateMachine<TakeStatusEnum, TakeChangeEventEnum> stateMachine,
                                    PersisterDO persisterDO) throws Exception {
        stateMachinePersist.persist(stateMachine, persisterDO);
    }

    @Override
    public StateMachine<TakeStatusEnum, TakeChangeEventEnum> stateMachineRestore(
            StateMachine<TakeStatusEnum, TakeChangeEventEnum> stateMachine,
            PersisterDO persisterDO) throws Exception {

        persisterDO.setMachineId(IStateMachineBuilder.TAKE_BUILDER_NAME);
        return stateMachinePersist.restore(stateMachine, persisterDO);
    }

    @Override
    public String getStateMachineType() {
        return IStateMachineBuilder.TAKE_BUILDER_NAME;
    }
}
