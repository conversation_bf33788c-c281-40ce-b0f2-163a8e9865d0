package com.ecommerce.order.fsm.persister;

import com.ecommerce.order.fsm.domain.PersisterDO;
import com.ecommerce.order.fsm.even.OrderChangeEventEnum;
import com.ecommerce.order.fsm.status.OrderStatusEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateMachineContext;
import org.springframework.statemachine.StateMachinePersist;
import org.springframework.statemachine.redis.RedisStateMachineContextRepository;
import org.springframework.statemachine.support.DefaultStateMachineContext;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 * Date: Create in 下午6:45 20/5/21
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderStateMachinePersist implements StateMachinePersist<OrderStatusEnum, OrderChangeEventEnum, PersisterDO> {

    private final RedisStateMachineContextRepository<OrderStatusEnum, OrderChangeEventEnum> redisOrderStateMachineContextRepository;

    @Override
    public void write(StateMachineContext<OrderStatusEnum, OrderChangeEventEnum> context, PersisterDO persisterDO) throws Exception {
    }

    /**
     * Read a {@link org.springframework.statemachine.StateMachineContext} from a persistent store
     * with a context object {@code T}.
     *
     * @param persisterDO the context ojb
     * @return the state machine context
     * @throws Exception the exception
     */
    @Override
    public StateMachineContext<OrderStatusEnum, OrderChangeEventEnum> read(PersisterDO persisterDO) throws Exception {
//        //获取缓存数据
            return new DefaultStateMachineContext<>(OrderStatusEnum.getByCode(persisterDO.getCurrentState()),
                    null, null, null, null, persisterDO.getMachineId());
    }
}
