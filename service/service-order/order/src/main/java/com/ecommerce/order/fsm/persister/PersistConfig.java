package com.ecommerce.order.fsm.persister;

import com.ecommerce.order.fsm.domain.PersisterDO;
import com.ecommerce.order.fsm.even.OrderChangeEventEnum;
import com.ecommerce.order.fsm.even.TakeChangeEventEnum;
import com.ecommerce.order.fsm.status.OrderStatusEnum;
import com.ecommerce.order.fsm.status.TakeStatusEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.statemachine.persist.DefaultStateMachinePersister;
import org.springframework.statemachine.persist.StateMachinePersister;

/**
 *
 * <AUTHOR>
 * Date: Create in 下午2:26 20/5/22
 */
@Configuration
@RequiredArgsConstructor
public class PersistConfig {
    private final OrderStateMachinePersist orderStateMachinePersist;
    private final TakeStateMachinePersist takeStateMachinePersist;

    @Bean(name = "orderStateMachinePersister")
    public StateMachinePersister<OrderStatusEnum, OrderChangeEventEnum, PersisterDO> orderStateMachinePersister() {
        return new DefaultStateMachinePersister<>(orderStateMachinePersist);
    }

    @Bean(name = "takeStateMachinePersister")
    public StateMachinePersister<TakeStatusEnum, TakeChangeEventEnum, PersisterDO> takeStateMachinePersister() {
        return new DefaultStateMachinePersister<>(takeStateMachinePersist);
    }

}
