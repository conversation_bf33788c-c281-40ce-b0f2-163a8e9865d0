package com.ecommerce.order.fsm.persister;

import com.ecommerce.order.fsm.even.OrderChangeEventEnum;
import com.ecommerce.order.fsm.even.TakeChangeEventEnum;
import com.ecommerce.order.fsm.status.OrderStatusEnum;
import com.ecommerce.order.fsm.status.TakeStatusEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.statemachine.redis.RedisStateMachineContextRepository;

/**
 *
 * <AUTHOR>
 * Date: Create in 上午12:44 20/5/22
 */
@Configuration
@RequiredArgsConstructor
public class RedisStateMachineRepositoryConfig {
    /**
     * 接入asgard后，redis的connectionFactory可以通过serviceName + InnerConnectionFactory来注入
     */
    private final RedisConnectionFactory redisConnectionFactory;

    @Bean(name = "redisOrderStateMachineContextRepository")
    public RedisStateMachineContextRepository<OrderStatusEnum, OrderChangeEventEnum> redisOrderStateMachineContextRepository() {
        return new RedisStateMachineContextRepository<>(redisConnectionFactory);
    }

    @Bean(name = "redisTakeStateMachineContextRepository")
    public RedisStateMachineContextRepository<TakeStatusEnum, TakeChangeEventEnum> redisTakeStateMachineContextRepository() {
        return new RedisStateMachineContextRepository<>(redisConnectionFactory);
    }
}
