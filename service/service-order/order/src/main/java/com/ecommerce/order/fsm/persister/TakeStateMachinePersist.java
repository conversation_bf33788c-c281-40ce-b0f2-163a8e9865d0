package com.ecommerce.order.fsm.persister;

import com.ecommerce.order.fsm.domain.PersisterDO;
import com.ecommerce.order.fsm.even.TakeChangeEventEnum;
import com.ecommerce.order.fsm.status.TakeStatusEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateMachineContext;
import org.springframework.statemachine.StateMachinePersist;
import org.springframework.statemachine.redis.RedisStateMachineContextRepository;
import org.springframework.statemachine.support.DefaultStateMachineContext;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 * Date: Create in 下午6:45 20/5/21
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TakeStateMachinePersist implements StateMachinePersist<TakeStatusEnum, TakeChangeEventEnum, PersisterDO> {

    private final RedisStateMachineContextRepository<TakeStatusEnum, TakeChangeEventEnum> redisTakeStateMachineContextRepository;

    @Override
    public void write(StateMachineContext<TakeStatusEnum, TakeChangeEventEnum> context, PersisterDO persisterDO) throws Exception {
    }

    /**
     * Read a {@link StateMachineContext} from a persistent store
     * with a context object {@code T}.
     *
     * @param persisterDO the context ojb
     * @return the state machine context
     * @throws Exception the exception
     */
    @Override
    public StateMachineContext<TakeStatusEnum, TakeChangeEventEnum> read(PersisterDO persisterDO) throws Exception {
//        //获取缓存数据
            return new DefaultStateMachineContext<>(TakeStatusEnum.getByCode(persisterDO.getCurrentState()),
                    null, null, null, null, persisterDO.getMachineId());
    }
}
