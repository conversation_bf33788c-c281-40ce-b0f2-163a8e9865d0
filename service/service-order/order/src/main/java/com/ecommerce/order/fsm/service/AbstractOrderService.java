package com.ecommerce.order.fsm.service;

import com.alibaba.fastjson.JSON;
import com.ecommerce.base.api.dto.ReceivingAddressDTO;
import com.ecommerce.base.api.dto.saleregion.RegionQueryDTO;
import com.ecommerce.base.api.dto.saleregion.SaleRegionSampleDTO;
import com.ecommerce.base.api.dto.warehouse.WarehouseDetailsDTO;
import com.ecommerce.base.api.service.IReceivingAddressService;
import com.ecommerce.base.api.service.ISaleRegionService;
import com.ecommerce.base.api.service.IWarehouseService;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.service.common.BaseBiz;
import com.ecommerce.common.service.common.bsid.CommonBusinessIdGenerator;
import com.ecommerce.common.service.common.uuid.UUIDGenerator;
import com.ecommerce.common.utils.ArithUtils;
import com.ecommerce.common.utils.BeanConvertUtils;
import com.ecommerce.common.utils.ListUtils;
import com.ecommerce.goods.api.dto.GoodsDTO;
import com.ecommerce.goods.api.dto.SellerConfirmJudgeDTO;
import com.ecommerce.goods.api.dto.SpecialGoodsAttributeDTO;
import com.ecommerce.goods.api.dto.stock.StockChangeDTO;
import com.ecommerce.goods.api.dto.stock.StockItemDTO;
import com.ecommerce.goods.api.dto.stock.StockItemQueryDTO;
import com.ecommerce.goods.api.service.IGoodsAddItemService;
import com.ecommerce.goods.api.service.IGoodsService;
import com.ecommerce.goods.api.service.IResourceService;
import com.ecommerce.goods.api.service.IStockService;
import com.ecommerce.logistics.api.dto.rulecompute.CarriageComputeDTO;
import com.ecommerce.logistics.api.dto.rulecompute.CarriageRuleResultDTO;
import com.ecommerce.logistics.api.dto.rulecompute.CategoryQuantityDTO;
import com.ecommerce.logistics.api.dto.shippingrule.QueryShippingRuleDTO;
import com.ecommerce.logistics.api.dto.shippingrule.ShippingRuleResultDTO;
import com.ecommerce.logistics.api.enums.TransportToolTypeEnum;
import com.ecommerce.logistics.api.service.ICarriageRouteService;
import com.ecommerce.logistics.api.service.IRuleComputeService;
import com.ecommerce.logistics.api.service.IShippingRouteService;
import com.ecommerce.member.api.dto.TaxInfoDetailDTO;
import com.ecommerce.member.api.dto.account.AccountDTO;
import com.ecommerce.member.api.dto.member.MemberDTO;
import com.ecommerce.member.api.dto.member.MemberSimpleDTO;
import com.ecommerce.member.api.dto.member.config.MemberConfigDTO;
import com.ecommerce.member.api.service.*;
import com.ecommerce.order.api.dto.*;
import com.ecommerce.order.api.dto.proxy.OrderProxyMapDTO;
import com.ecommerce.order.api.dto.proxy.ProxyOrderActionMapDTO;
import com.ecommerce.order.api.enums.*;
import com.ecommerce.order.biz.*;
import com.ecommerce.order.biz.fsm.IOrderPayinfoBiz;
import com.ecommerce.order.biz.fsm.*;
import com.ecommerce.order.biz.impl.BaseOrderSearchBiz;
import com.ecommerce.order.dao.dto.OrderCancelDO;
import com.ecommerce.order.dao.dto.OrderCloseDO;
import com.ecommerce.order.dao.dto.OrderCompleteDO;
import com.ecommerce.order.dao.dto.TakeAutoCreateDTO;
import com.ecommerce.order.dao.vo.OrderInfo;
import com.ecommerce.order.dao.vo.OrderItem;
import com.ecommerce.order.fsm.domain.StatusModelDO;
import com.ecommerce.order.fsm.even.OrderChangeEventEnum;
import com.ecommerce.order.fsm.manager.OrderStateMachineEventManager;
import com.ecommerce.order.fsm.status.OrderStatusEnum;
import com.ecommerce.order.fsm.status.OrderTypeEnum;
import com.ecommerce.order.service.IBuyerSelectInfoService;
import com.ecommerce.order.service.impl.OrderSMSMessageProducer;
import com.ecommerce.order.service.message.MessageQueueService;
import com.ecommerce.price.api.dto.BuyerPromotionalDetailsDTO;
import com.ecommerce.price.api.dto.PromotionalDTO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Condition;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.ecommerce.common.service.common.BaseBiz.setOperInfo;



@Slf4j
public abstract class AbstractOrderService implements IOrderHandlerService {

    public static final String SAVE_ORDER = "saveOrder";
    public static final String COM_ECOMMERCE_ORDER_TAKE_INFO_AUTO_CREATE = "com.ecommerce.order.takeInfo.auto.create";
    public static final String OPERATOR = "operator";
    public static final String UNDEFINDED_ERROR_MSG = "无效的订单状态";
    public static final String IF_CONCRETE = "ifConcrete";
    public static final String ORDER_DTO = "orderDTO";

    public static final  Long SLEEP_MS = 6000L;

    @Autowired
    protected OrderStateMachineEventManager orderStateMachineEventManager;

    @Autowired
    protected IMemberService memberService;

    @Autowired
    protected IReceivingAddressService receivingAddressService;

    @Autowired
    protected IWarehouseService warehouseService;

    @Autowired
    protected IGoodsService goodsService;

    @Autowired
    protected IResourceService resourceService;

    @Autowired
    protected IGoodsAddItemService goodsAddItemService;

    @Autowired
    protected BaseOrderSearchBiz baseOrderSearchBiz;

    @Autowired
    protected IRuleComputeService ruleComputeService;

    @Autowired
    protected UUIDGenerator uuidGenerator;

    @Autowired
    protected CommonBusinessIdGenerator businessIdGenerator;

    @Autowired
    protected IOrderInfoBiz orderInfoBiz;
    @Autowired
    protected IOrderInfoExtBiz orderInfoExtBiz;
    @Autowired
    protected IOrderItemBiz orderItemBiz;

    @Autowired
    protected IOrderDiscountDetailBiz orderDiscountDetailBiz;

    @Autowired
    protected IOrderPayinfoBiz orderPayinfoBiz;

    @Autowired
    protected IOrderTaxinfoBiz orderTaxinfoBiz;

    @Autowired
    protected IOrderErpBiz orderErpBiz;

    @Autowired
    protected IOrderErpAddressBiz orderErpAddressBiz;

    @Autowired
    protected IOrderSendQuantityLogBiz orderSendQuantityLogBiz;

    @Autowired
    protected IOrderProxyMapBiz orderProxyMapBiz;

    @Autowired
    protected ITaxInfoService taxInfoService;

    @Autowired
    protected IOrderComputeService orderComputeService;

    @Autowired
    protected IMemberConfigService memberConfigService;

    @Autowired
    protected IMemberRelationService memberRelationService;

    @Autowired
    protected IBuyerSelectInfoService buyerSelectInfoService;

    @Autowired
    protected IOrderQueryService orderQueryService;

    @Autowired
    protected ITakeInfoService takeInfoService;

    @Autowired
    protected IOrderPaymentService orderPaymentService;

    @Autowired
    protected IOrderSplitBillService orderSplitBillService;

    @Autowired
    protected OrderSMSMessageProducer messageProducer;

    @Autowired
    protected IStockService stockService;

    @Autowired
    protected ISaleRegionService saleRegionService;

    @Autowired
    protected MessageQueueService messageQueueService;

    @Autowired
    protected IAccountService accountService;

    @Autowired
    protected IShippingRouteService shippingRouteService;

    @Autowired
    protected ICarriageRouteService carriageRouteService;

    /**
     * 转换订单明细项数据
     *
     * @param orderDTO 订单对象
     */
    public abstract void convertOrderItem(OrderDTO orderDTO);

    /**
     * 转换订单资源数据
     */
    public abstract void updateResourceNum(OrderDTO orderDTO, List<OrderItemDTO> orderItems, String operator);

    /**
     * 组装二级订单库存数据
     */
    public Boolean convertSecondaryOrderStock(OrderDTO orderDTO, StockChangeDTO stockChangeDTO) {
        OrderItemDTO orderItemDTO = orderDTO.getOrderItems().get(0);
        stockChangeDTO.setOrderLevel(2);
        stockChangeDTO.setBuyerId(orderDTO.getSellerId());
        stockChangeDTO.setBuyerName(orderDTO.getSellerName());
        //获取商品信息
        ItemResult<GoodsDTO> goodsResult = goodsService.getGoodsInfo(orderItemDTO.getGoodsId());
        if (!goodsResult.isSuccess() || goodsResult.getData() == null) {
            throw new BizException(BasicCode.DATA_NOT_EXIST, "商品:" + orderItemDTO.getGoodsId());
        }
        stockChangeDTO.setGoodsId(goodsResult.getData().getRefGoodsId());
        //获取映射仓库
        WarehouseDetailsDTO warehouse = warehouseService.queryWarehouseDetails(orderItemDTO.getStoreId());
        if (warehouse == null) {
            throw new BizException(BasicCode.DATA_NOT_EXIST, "仓库:" + orderItemDTO.getGoodsId());
        }
        stockChangeDTO.setWarehouseId(warehouse.getRefWarehouseId());
        //获取映射销售区域
        RegionQueryDTO regionQueryDTO = new RegionQueryDTO();
        regionQueryDTO.setSellerId(goodsResult.getData().getRefSellerId());
        regionQueryDTO.setProvinceCode(orderDTO.getProvinceCode());
        regionQueryDTO.setCityCode(orderDTO.getCityCode());
        regionQueryDTO.setDistrictCode(orderDTO.getDistrictCode());
        regionQueryDTO.setStreetCode(orderDTO.getStreetCode());
        List<SaleRegionSampleDTO> saleRegionList = saleRegionService.findByRegion(regionQueryDTO);
        log.info("stockDeduction_saleRegionList:" + JSON.toJSONString(saleRegionList));
        if (CollectionUtils.isEmpty(saleRegionList)) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "找不到行政区域对应的厂家销售区域");
        }
        stockChangeDTO.setSaleRegionId(saleRegionList.get(0).getSaleRegionId());
        //查询厂家是否分配了库存
        StockItemQueryDTO stockItemQueryDTO = new StockItemQueryDTO();
        stockItemQueryDTO.setGoodsId(stockChangeDTO.getGoodsId());
        stockItemQueryDTO.setBuyerId(stockChangeDTO.getBuyerId());
        stockItemQueryDTO.setWarehouseId(stockChangeDTO.getWarehouseId());
        stockItemQueryDTO.setSaleRegionId(stockChangeDTO.getSaleRegionId());
        List<StockItemDTO> refStockItemList = stockService.findStockItemByQuery(stockItemQueryDTO);
        log.info("refStockItemList_findStockItemByQuery:{},result:{}", stockItemQueryDTO, JSON.toJSONString(refStockItemList));

        return CollectionUtils.isNotEmpty(refStockItemList);
    }

    /**
     * 是否分配了商品库存
     */
    public Boolean ifAllocationGoodsStock(OrderDTO orderDTO, StockChangeDTO stockChangeDTO) {
        OrderItemDTO orderItemDTO = orderDTO.getOrderItems().get(0);
        //查询厂商是否做了库存分配
        StockItemQueryDTO stockItemQueryDTO = new StockItemQueryDTO();
        stockItemQueryDTO.setGoodsId(orderItemDTO.getGoodsId());
        stockItemQueryDTO.setBuyerId(orderDTO.getBuyerId());
        stockItemQueryDTO.setWarehouseId(orderItemDTO.getStoreId());
        stockItemQueryDTO.setSaleRegionId(orderDTO.getSaleRegionPath());
        List<StockItemDTO> stockItemList = stockService.findStockItemByQuery(stockItemQueryDTO);
        log.info("ifAllocationGoodsStock_findStockItemByQuery:{}, result:{}", stockItemQueryDTO, JSON.toJSONString(stockItemList));
        BeanUtils.copyProperties(stockItemQueryDTO, stockChangeDTO);
        stockChangeDTO.setBuyerName(orderDTO.getBuyerName());
        BigDecimal returnNum = ArithUtils.subtract(orderItemDTO.getItemQuantity(), orderItemDTO.getItemSendQuantity());
        stockChangeDTO.setChangeQuantity(returnNum.multiply(orderItemDTO.getConvertRate()).negate());
        stockChangeDTO.setOrderCode(orderDTO.getOrderCode());
        stockChangeDTO.setOrderLevel(1);
        //是否分配了库存
        Boolean ifAllocationGoodsStock = CollectionUtils.isNotEmpty(stockItemList);
        //二级订单扣减库存需要转换为映射的数据源
        if (CsStringUtils.equals(orderDTO.getProxyOrderType(), ProxyOrderTypeEnum.SECONDARY.getCode())) {
            //保留二级订单原本的库存扣减数据用于经销商分配的库存扣减
            stockChangeDTO.setAgentBuyerId(stockChangeDTO.getBuyerId());
            stockChangeDTO.setAgentBuyerName(stockChangeDTO.getBuyerName());
            stockChangeDTO.setAgentGoodsId(stockChangeDTO.getGoodsId());
            stockChangeDTO.setAgentWarehouseId(stockChangeDTO.getWarehouseId());
            stockChangeDTO.setAgentSaleRegionId(stockChangeDTO.getSaleRegionId());
            ifAllocationGoodsStock = convertSecondaryOrderStock(orderDTO, stockChangeDTO);
        }

        return ifAllocationGoodsStock;
    }

    /**
     * 订单数据准备
     *
     * @param orderDTO 订单对象
     */
    public void orderDataPrepare(OrderDTO orderDTO) {
        log.info("orderDataPrepare:{}", JSON.toJSONString(orderDTO));

        //验证创建订单入参
        validateOrderParam(orderDTO);

        //转换订单参数
        convertOrderParam(orderDTO);

        //判断黑名单关系（myself，target）
        Boolean deFriend = memberRelationService.isDefriend(orderDTO.getSellerId(), orderDTO.getBuyerId());
        if (Boolean.TRUE.equals(deFriend)) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "您已被此商家加入黑名单，无法购买该商品");
        }

        //判断物流费规则, 非自提订单需要计算物流费
    }

    public boolean isSellerConfirm(String sellerId, List<OrderItemDTO> itemList) {
        boolean needCementBusySeasonApproval = false;
        MemberConfigDTO busySeasonFlagConfig = memberConfigService.findByMemberIdAndKeyCode(sellerId, "busy_season_flag");
        if (busySeasonFlagConfig != null && CsStringUtils.equals(busySeasonFlagConfig.getValue(), "1")) {
            needCementBusySeasonApproval = true;
        }
        SellerConfirmJudgeDTO sellerConfirmJudgeDTO = new SellerConfirmJudgeDTO();
        sellerConfirmJudgeDTO.setNeedCementBusySeasonApproval(needCementBusySeasonApproval);
        sellerConfirmJudgeDTO.setGoodsIdList(itemList.stream().map(OrderItemDTO::getGoodsId).toList());

        ItemResult<Boolean> result = goodsService.isSellerConfirmBatch(sellerConfirmJudgeDTO);
        return result != null && result.getData();
    }

    /**
     * 验证资源是否配置运费规则
     *
     * @param orderDTO 订单对象
     * @param itemDTO  订单行对象
     */
    public void validateVehicleCarriageRule(OrderDTO orderDTO, OrderItemDTO itemDTO) {
        CategoryQuantityDTO quantityDTO = new CategoryQuantityDTO();
        quantityDTO.setTransportCategoryId(itemDTO.getLogistics());
        quantityDTO.setProductQuantity(itemDTO.getMeasureQuantity());
        CarriageComputeDTO carriageDTO = new CarriageComputeDTO();
        carriageDTO.setPickingBillType(orderDTO.getDeliverWay());
        carriageDTO.setUserId(orderDTO.getSellerId());
        carriageDTO.setReceiveAddressId(orderDTO.getAddressId());
        carriageDTO.setReceiveAddressLocation(orderDTO.getAddressMap());
        carriageDTO.setWarehouseId(itemDTO.getStoreId());
        carriageDTO.setCategoryQuantityList(Collections.singletonList(quantityDTO));
        carriageDTO.setProvinceCode(orderDTO.getProvinceCode());
        carriageDTO.setCityCode(orderDTO.getCityCode());
        carriageDTO.setDistrictCode(orderDTO.getDistrictCode());
        carriageDTO.setStreetCode(orderDTO.getStreetCode());
        log.error("queryCarriageRule_start:" + JSON.toJSONString(carriageDTO));
        ItemResult<List<CarriageRuleResultDTO>> computeResult = ruleComputeService.queryCarriageRule(carriageDTO);
        log.info("queryCarriageRule_end:" + JSON.toJSONString(computeResult));
        if (!computeResult.isSuccess()) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "查询运费规则失败！");
        }
        if (Boolean.FALSE.equals(computeResult.getData().get(0).getRuleFlag())) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "该地区无法配送该商品！");
        }
    }

    /**
     * 验证船运定价规则
     *
     * @param orderDTO 订单对象
     * @param itemDTO  订单明细项
     */
    public void validateShipCarriageRule(OrderDTO orderDTO, OrderItemDTO itemDTO) {
        //计算配送方式
        QueryShippingRuleDTO queryShippingRuleDTO = new QueryShippingRuleDTO();
        queryShippingRuleDTO.setSellerId(orderDTO.getSellerId());
        queryShippingRuleDTO.setBuyerId(orderDTO.getBuyerId());
        //提货码头编码
        queryShippingRuleDTO.setPickingWharfCode(orderDTO.getOrderInfoExtDTO().getPickingWharfId());
        //卸货码头编码
        queryShippingRuleDTO.setUnloadingWharfCode(orderDTO.getOrderInfoExtDTO().getReceivingWharfId());
        ShippingRuleResultDTO shippingRuleResultDTO = new ShippingRuleResultDTO();
        //物流类型
        shippingRuleResultDTO.setTransportCategoryId(itemDTO.getLogistics());
        //计量单位数量
        shippingRuleResultDTO.setProductQuantity(itemDTO.getMeasureQuantity());
        queryShippingRuleDTO.setShippingRuleList(Lists.newArrayList(shippingRuleResultDTO));
        queryShippingRuleDTO.setPickingBillType(orderDTO.getDeliverWay());
        //查询航线定价规则
        log.info("queryShippingRule_start:" + JSON.toJSONString(queryShippingRuleDTO));
        ItemResult<List<ShippingRuleResultDTO>> computeResult = shippingRouteService.queryShippingRule(queryShippingRuleDTO);
        log.info("queryShippingRule_end:" + JSON.toJSONString(computeResult));
        if (!computeResult.isSuccess()) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "查询航线运费定价异常！");
        }
        if (CollectionUtils.isEmpty(computeResult.getData()) || !computeResult.getData().get(0).getRuleFlag()) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "该地区无法配送该商品！");
        }
    }

    /**
     * 验证订单参数数据
     *
     * @param orderDTO 订单对象
     */
    public void validateOrderParam(OrderDTO orderDTO) {
        //订单类型
        if (CsStringUtils.isEmpty(orderDTO.getOrderType())) {
            throw new BizException(BasicCode.INVALID_PARAM, "订单类型");
        }
        //买家信息
        if (CsStringUtils.isBlank(orderDTO.getBuyerId())) {
            throw new BizException(BasicCode.PARAM_NULL, "买家ID");
        }
        //物流配送方式
        //收货地址
        if (CsStringUtils.isBlank(orderDTO.getAddressId())) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "请选择收货地址");
        }
        //下单渠道
        if (CsStringUtils.isBlank(orderDTO.getCreateWay())) {
            orderDTO.setCreateWay(CreateWayEnum.WEB.code());
        }
        //提货点地址
        if (CsStringUtils.isBlank(orderDTO.getStoreId())) {
            throw new BizException(BasicCode.INVALID_PARAM, "提货点地址");
        }
        //订单行项目
        if (CollectionUtils.isEmpty(orderDTO.getOrderItems())) {
            throw new BizException(BasicCode.PARAM_NULL, "订单商品不存在");
        }
        //订单扩展信息
        if (orderDTO.getOrderInfoExtDTO() == null) {
            orderDTO.setOrderInfoExtDTO(new OrderInfoExtDTO());
            orderDTO.getOrderInfoExtDTO().setTransportType(TransportToolTypeEnum.ROAD_TRANSPORT.getCode());
        }
    }

    /**
     * 转换订单参数对象
     *
     * @param orderDTO 订单对象
     */
    public void convertOrderParam(OrderDTO orderDTO) {
        //初始化订单ID
        orderDTO.setOrderId(uuidGenerator.gain());
        orderDTO.setOrderVersion(1);
        //初始化订单编号
        if (CsStringUtils.isBlank(orderDTO.getOrderCode())) {
            orderDTO.setOrderCode(businessIdGenerator.incrementOrderCode());
        }
        //初始化订单开票状态
        orderDTO.setInvoiceStatus(OrderInvoiceStatus.UNINVOICE.getCode());
        //初始化配送、调价、退款状态
        orderDTO.setDeliverStatus(null);
        orderDTO.setAdjustStatus(AdjustStatusEnum.WAIT_ADJUST.getCode());
        orderDTO.setRefundStatus(null);
        //买家下单时输入台班费、其他费用
        if (orderDTO.getMachineShiftCost() != null || orderDTO.getOthersAmount() != null) {
            orderDTO.setActualOthersAmount(ArithUtils.add(orderDTO.getMachineShiftCost(), orderDTO.getOthersAmount()));
        }
        if (orderDTO.getActualOthersAmount() == null) {
            orderDTO.setActualOthersAmount(orderDTO.getOriginOthersAmount());
        }
        //获取买家信息
        MemberSimpleDTO buyer = getMemberSimpleDTO(orderDTO);
        orderDTO.setBuyerName(buyer.getMemberName());
        orderDTO.setBuyerType(MemberDTO.PERSON_TYPE_PRE);
        if (buyer.getMemberType().startsWith(MemberDTO.ENTERPRISE_TYPE_PRE)) {
            orderDTO.setBuyerType(MemberDTO.ENTERPRISE_TYPE_PRE);
        }
        orderDTO.setBuyerContact(buyer.getContactName());
        orderDTO.setBuyerContactWay(buyer.getContactPhone());

        //获取买家地址信息
        ReceivingAddressDTO addressDTO = getReceivingAddressDTO(orderDTO);
        orderDTO.setProvinceCode(addressDTO.getProvinceCode());
        orderDTO.setProvinceName(addressDTO.getProvinceName());
        orderDTO.setCityCode(addressDTO.getCityCode());
        orderDTO.setCityName(addressDTO.getCityName());
        orderDTO.setDistrictCode(addressDTO.getDistrictCode());
        orderDTO.setDistrictName(addressDTO.getDistrictName());
        orderDTO.setStreetCode(addressDTO.getStreetCode());
        orderDTO.setStreetName(addressDTO.getStreetName());
        orderDTO.setAddressDetail((addressDTO.getAddress() == null ? "" : addressDTO.getAddress()).concat(addressDTO.getAddressDetail() == null ? "" : addressDTO.getAddressDetail()));
        orderDTO.setAddressMap(addressDTO.getCoordinate());
        orderDTO.setReceiver(addressDTO.getConsigneeName());
        orderDTO.setAlias(addressDTO.getAlias());
        orderDTO.setReceiverPhone(CsStringUtils.defaultIfEmpty(addressDTO.getMobilePhone(), addressDTO.getPhone()));
        //转换订单行对象
        convertOrderItem(orderDTO);
        /**
         * 卖家信息
         */
        if (CsStringUtils.isBlank(orderDTO.getSellerId())) {
            throw new BizException(BasicCode.DB_DATA_NOT_FOUND, "商品卖家信息");
        }
        MemberDTO seller = memberService.findMemberDetailById(orderDTO.getSellerId());
        if (seller == null) {
            throw new BizException(BasicCode.DB_DATA_NOT_FOUND, "商品卖家:" + orderDTO.getSellerId());
        }
        orderDTO.setSellerName(seller.getMemberName());
        orderDTO.setSellerContact(seller.getContactName());
        orderDTO.setSellerContactWay(seller.getContactPhone());
        //企业信息为空取主账号电话信息
        if (CsStringUtils.isBlank(seller.getContactPhone())) {
            AccountDTO accountDTO = accountService.findById(seller.getMainAccountId());
            if (accountDTO != null) orderDTO.setSellerContactWay(accountDTO.getMobile());
        }
        //币种(默认人民币)
        orderDTO.setCurrency("CNY");
        orderDTO.setCurrencyName("CNY");
        orderDTO.setCurrencySymbol("人民币");
        //合同单直接取合同上的确认标识
        if (!CsStringUtils.equals(orderDTO.getOrderType(), OrderTypeEnum.CONTRACT.getCode()) ||
                CsStringUtils.isBlank(orderDTO.getOrderInfoExtDTO().getSigner())) {
            //是否需要卖家确认
            orderDTO.setIfNeedsellerconfirm(isSellerConfirm(orderDTO.getSellerId(), orderDTO.getOrderItems()));
        }
    }

    private MemberSimpleDTO getMemberSimpleDTO(OrderDTO orderDTO) {
        MemberSimpleDTO buyer;
        try {
            buyer = memberService.findMemberSimpleById(orderDTO.getBuyerId());
        } catch (Exception e) {
            log.error("buyer:" + orderDTO.getBuyerId(), e);
            throw new BizException(BasicCode.UNDEFINED_ERROR, "查询买家信息异常");
        }
        if (buyer == null) {
            throw new BizException(BasicCode.DB_DATA_NOT_FOUND, "买家信息:" + orderDTO.getBuyerId());
        }
        return buyer;
    }


    private ReceivingAddressDTO getReceivingAddressDTO(OrderDTO orderDTO) {
        ReceivingAddressDTO addressDTO;
        try {
            addressDTO = receivingAddressService.findById(orderDTO.getAddressId());
            if (Objects.isNull(addressDTO)) {
                throw new BizException(BasicCode.DB_DATA_NOT_FOUND, "收货地址:" + orderDTO.getAddressId());
            }
            //设置收货码头信息
            orderDTO.getOrderInfoExtDTO().setReceivingWharfId(addressDTO.getWharfId());
            orderDTO.getOrderInfoExtDTO().setReceivingWharfName(addressDTO.getWharfName());
        } catch (Exception e) {
            log.error("addressService:" + orderDTO.getAddressId() + "," + orderDTO.getBuyerId(), e);
            throw new BizException(BasicCode.UNDEFINED_ERROR, "获取收获地址异常");
        }
        return addressDTO;
    }

    /**
     * 持久化订单数据
     *
     * @param orderDTO 订单对象
     * @param operator 操作人
     */
    @Transactional(rollbackFor = Exception.class)
    public void persistOrderData(OrderDTO orderDTO, String operator) {
        log.info("persistOrderData:{}", JSON.toJSONString(orderDTO));

        //创建订单信息
        orderInfoBiz.createOrderInfo(orderDTO, operator);

        //创建订单明细信息
        orderItemBiz.batchCreateOrderItem(orderDTO.getOrderItems(), orderDTO.getOrderId(), operator);

        //保存优惠明细信息
        batchCreateOrderDiscountDetail(orderDTO, operator);

        //保存erp地址信息
        if (orderDTO.getErpAddressDTO() != null && CsStringUtils.isNotBlank(orderDTO.getErpAddressDTO().getErpUnloadAddressId())) {
            orderDTO.getErpAddressDTO().setOrderId(orderDTO.getOrderId());
            orderDTO.getErpAddressDTO().setReceivingAddressId(orderDTO.getAddressId());
            orderDTO.getErpAddressDTO().setOperator(operator);
            orderErpAddressBiz.saveOrderErpAddress(orderDTO.getErpAddressDTO());
        }

        //保存支付信息
        if (CollectionUtils.isNotEmpty(orderDTO.getOrderPayinfo())) {
            orderDTO.getOrderPayinfo().stream().forEach(orderPayInfoDTO -> {
                orderPayInfoDTO.setObjectId(orderDTO.getOrderId());
                orderPayInfoDTO.setObjectCode(orderDTO.getOrderCode());
                orderPayInfoDTO.setPayinfoId(orderPayinfoBiz.createOrderPayInfo(orderPayInfoDTO, operator));
            });
        }

        //保存开票信息
        createOrderTaxInfo(orderDTO, operator);
        //一级订单保存映射关系
        if (CsStringUtils.equals(orderDTO.getProxyOrderType(), ProxyOrderTypeEnum.PRIMARY.getCode())) {
            OrderProxyMapDTO orderProxyMapDTO = orderDTO.getOrderProxyMapDTO();
            orderProxyMapDTO.setPrimaryOrderId(orderDTO.getOrderId());
            orderProxyMapDTO.setPrimaryOrderCode(orderDTO.getOrderCode());
            log.info("orderProxyMapDTO:" + JSON.toJSONString(orderProxyMapDTO));
            orderProxyMapBiz.saveOrderProxyRelation(orderProxyMapDTO, operator);
            //更新二级订单的仓库，并发送消息
            if (CsStringUtils.isNotBlank(orderProxyMapDTO.getSecondaryStoreId())) {
                OrderItemDTO orderItemDTO = new OrderItemDTO();
                orderItemDTO.setOrderId(orderProxyMapDTO.getSecondaryOrderId());
                WarehouseDetailsDTO warehouse = warehouseService.queryWarehouseDetails(orderProxyMapDTO.getSecondaryStoreId());
                if (warehouse != null) {
                    orderItemDTO.setStoreId(orderProxyMapDTO.getSecondaryStoreId());
                    orderItemDTO.setStoreName(warehouse.getName());
                    orderItemDTO.setStoreType(warehouse.getType());
                    orderItemDTO.setStoreAddress(warehouse.getAddress());
                }
                orderItemBiz.changeSecondaryOrderWarehouse(orderItemDTO);
            }
        }

        //更新资源数量(库存扣减失败全量回滚)
        updateResourceNum(orderDTO, orderDTO.getOrderItems(), operator);
    }

    private void createOrderTaxInfo(OrderDTO orderDTO, String operator) {
        if (CollectionUtils.isNotEmpty(orderDTO.getOrderTaxinfo())) {
            log.info("persistOrderData_orderTaxInfo:" + JSON.toJSONString(orderDTO.getOrderTaxinfo()));
            orderDTO.getOrderTaxinfo().stream().forEach(orderTaxInfoDTO -> {
                if (CsStringUtils.isNotBlank(orderTaxInfoDTO.getInvoiceId())) {
                    TaxInfoDetailDTO taxDetailDTO = taxInfoService.findById(orderTaxInfoDTO.getInvoiceId());
                    log.info("persistOrderData_taxDetailDTO:" + JSON.toJSONString(taxDetailDTO));
                    BeanConvertUtils.copyProperties(taxDetailDTO, orderTaxInfoDTO);
                }
                if (CsStringUtils.isBlank(orderTaxInfoDTO.getMemberId())) {
                    orderTaxInfoDTO.setMemberId(orderDTO.getBuyerId());
                    orderTaxInfoDTO.setMemberName(orderDTO.getBuyerName());
                }
                orderTaxInfoDTO.setOrderId(orderDTO.getOrderId());
                orderTaxinfoBiz.createOrderTaxInfo(orderTaxInfoDTO, operator);
            });
        }
    }

    private void batchCreateOrderDiscountDetail(OrderDTO orderDTO, String operator) {
        if (CollectionUtils.isNotEmpty(orderDTO.getDiscountDetail())) {
            orderDiscountDetailBiz.batchCreateOrderDiscountDetail(orderDTO.getDiscountDetail(), orderDTO.getOrderId(), operator);
        }
    }

    /**
     * 处理定制加工项
     *
     * @param orderDTO 订单对象
     * @param operator 操作人
     */
    private void processBuyerSelectInfo(OrderDTO orderDTO, String operator) {
        Map<String, List<BuyerSelectInfoDTO>> buyerSelectInfoMap = Maps.newHashMap();
        orderDTO.getOrderItems().stream().filter(orderItem -> CollectionUtils.isNotEmpty(orderItem.getSelectInfos()))
                .forEach(orderItem -> buyerSelectInfoMap.put(orderItem.getOrderItemId(), convertBuyerSelectInfo(orderItem.getSelectInfos())));
        if (CollectionUtils.isNotEmpty(buyerSelectInfoMap.values())) {
            log.info("addBuyerSelectInfoFromOrder:{}-{}", orderDTO.getOrderId(), JSON.toJSONString(buyerSelectInfoMap));
            buyerSelectInfoService.addBuyerSelectInfoFromOrder(
                    buyerSelectInfoMap, orderDTO.getBuyerId(), orderDTO.getOrderId(), operator);
        }
        if (CollectionUtils.isNotEmpty(orderDTO.getCartResourceIds())) {
            for (String carId : orderDTO.getCartResourceIds()) {
                buyerSelectInfoService.removeBuyerSelectInfoFromCart(carId, operator);
            }
        }
    }

    private List<BuyerSelectInfoDTO> convertBuyerSelectInfo(List<com.ecommerce.order.api.dto.BuyerSelectInfoDTO> buyerSelectInfoDTOS) {
        List<BuyerSelectInfoDTO> selectInfoList = new ArrayList<>();
        for (com.ecommerce.order.api.dto.BuyerSelectInfoDTO buyerSelectInfoDTO : buyerSelectInfoDTOS) {
            BuyerSelectInfoDTO selectInfo = new BuyerSelectInfoDTO();
            BeanUtils.copyProperties(buyerSelectInfoDTO, selectInfo);
            selectInfoList.add(selectInfo);
        }
        return selectInfoList;
    }

    /**
     * 建立企业关系
     *
     * @param orderDTO 订单对象
     */
    private void addMemberRelation(OrderDTO orderDTO) {
        try {
            log.info("addMemberRelationByTrade_{},{}", orderDTO.getSellerId(), orderDTO.getBuyerId());
            memberRelationService.addMemberRelationByTrade(orderDTO.getSellerId(), orderDTO.getBuyerId());
        } catch (Exception e) {
            log.error("addMemberRelationByTrade_Error", e);
        }
    }

    /**
     * 使用优惠券
     *
     * @param orderDTO 订单对象
     */
    private void useResourcePromotional(OrderDTO orderDTO) {
        List<OrderDiscountDetailDTO> disDetails = Collections.emptyList();
        if (CollectionUtils.isEmpty(disDetails)) {
            return;
        }
        Map<String, OrderItemDTO> orderItemMap = Maps.newHashMap();
        orderDTO.getOrderItems().forEach(orderItem -> orderItemMap.put(orderItem.getOrderItemId(), orderItem));
        PromotionalDTO promotional = new PromotionalDTO();
        List<BuyerPromotionalDetailsDTO> promotionalList = Lists.newArrayList();
        for (OrderDiscountDetailDTO discount : disDetails) {
            BuyerPromotionalDetailsDTO promotionalDetail = new BuyerPromotionalDetailsDTO();
            promotionalDetail.setOrderId(orderDTO.getOrderId());
            promotionalDetail.setOrderNo(orderDTO.getOrderCode());
            promotionalDetail.setUseTime(new Date());
            if (DiscountTypeEnum.ONE.code().equals(discount.getDiscountType())) {
                promotionalDetail.setBuyerPriceSaleRuleId(discount.getDiscountId());
            } else if (DiscountTypeEnum.SECTION.code().equals(discount.getDiscountType())) {
                promotionalDetail.setBuyerSectionSaleRuleId(discount.getDiscountId());
            } else if (DiscountTypeEnum.SHOP.code().equals(discount.getDiscountType())) {
                promotionalDetail.setReferrerRuleId(discount.getDiscountId());
            } else {
                continue;
            }
            promotionalDetail.setBuyerId(orderDTO.getBuyerId());
            promotionalDetail.setBuyerName(orderDTO.getBuyerName());
            promotionalDetail.setSellerId(orderDTO.getSellerId());
            promotionalDetail.setSellerName(orderDTO.getSellerName());
            promotionalDetail.setResourceSalePrice(discount.getUntiPrice());
            promotionalDetail.setResourceTotalPrice(discount.getAmountPrice());

            OrderItemDTO item = orderItemMap.get(discount.getObjectId());
            if (item != null) {
                promotionalDetail.setGoodsId(item.getGoodsId());
                promotionalDetail.setGoodsName(item.getGoodsName());
                promotionalDetail.setResourceId(item.getResourceId());
                promotionalDetail.setResourceName(item.getResourceName());
                promotionalDetail.setResourceAmount(item.getItemQuantity());
                promotionalDetail.setResourceOriginalPrice(item.getOriginUnitPrice());
                promotionalDetail.setUnit(item.getUnits());
            }
            promotionalList.add(promotionalDetail);
        }
        promotional.setList(promotionalList);
        try {
            log.info("savePromotionDetail:{}", JSON.toJSONString(promotionalList));
        } catch (Exception e) {
            log.error("savePromotionDetail_使用优惠信息出错", e);
            throw new BizException(BasicCode.UNDEFINED_ERROR, "优惠信息失效：" + e.getMessage());
        }
    }

    /**
     * 订单创建后置处理器
     *
     * @param orderDTO 订单对象
     * @param operator 操作人
     */
    public void orderCreatePostProcessor(OrderDTO orderDTO, String operator) {
        //处理定制加工项
        processBuyerSelectInfo(orderDTO, operator);
        log.info("processBuyerSelectInfo:" + JSON.toJSONString(orderDTO));
        //建立企业关系
        addMemberRelation(orderDTO);
        log.info("addMemberRelation:" + JSON.toJSONString(orderDTO));
        //确认使用优惠券
        if (!orderDTO.getIfNeedsellerconfirm() && !CsStringUtils.equals(orderDTO.getOrderInfoExtDTO().getTransportType(),
                TransportToolTypeEnum.WATER_TRANSPORT.getCode())) {
            useResourcePromotional(orderDTO);
            log.info("useResourcePromotional:" + JSON.toJSONString(orderDTO));
        }
        //是否触发自动创建发货单
        Boolean autoCreateTakeInfo = getAutoCreateTakeInfo(orderDTO);
        //承运商代客下单自动生成发货单
        if (autoCreateTakeInfo && orderDTO.getTakeCarrierDTO() != null &&
                CsStringUtils.isNotBlank(orderDTO.getTakeCarrierDTO().getCarrierId())) {
            TakeAutoCreateDTO takeAutoCreateDTO = new TakeAutoCreateDTO();
            takeAutoCreateDTO.setQuickPlaceOrder(0);
            takeAutoCreateDTO.setOrderId(orderDTO.getOrderId());
            takeAutoCreateDTO.setOperator(operator);
            takeAutoCreateDTO.setOperatorMemberId(orderDTO.getBuyerId());
            takeAutoCreateDTO.setTakeCarrierDTO(orderDTO.getTakeCarrierDTO());
            messageQueueService.sendMQ(takeAutoCreateDTO, COM_ECOMMERCE_ORDER_TAKE_INFO_AUTO_CREATE);
        }
        //船运创建一级订单，如果二级发货单已存在且处于待确认状态
        if (CsStringUtils.equals(orderDTO.getOrderInfoExtDTO().getTransportType(), TransportToolTypeEnum.WATER_TRANSPORT.getCode())) {
            messageQueueService.continueTakeInfoAutoConfirm(orderDTO.getOrderId());
        }

        // 快速下单配置
        OrderInfoExtDTO orderInfoExtDTO = orderDTO.getOrderInfoExtDTO();
        if (autoCreateTakeInfo && Objects.equals(orderInfoExtDTO.getQuickPlaceOrder(), 1)) {
            List<Integer> vehicles = new ArrayList<>();
            if (CsStringUtils.isNotEmpty(orderInfoExtDTO.getVarietyId())) {
                for (String idStr : ListUtils.toList(orderInfoExtDTO.getVarietyId())) {
                    vehicles.add(Integer.parseInt(idStr));
                }
            }
            TakeAutoCreateDTO takeAutoCreateDTO = new TakeAutoCreateDTO();
            takeAutoCreateDTO.setQuickPlaceOrder(orderInfoExtDTO.getQuickPlaceOrder());
            takeAutoCreateDTO.setOrderId(orderDTO.getOrderId());
            takeAutoCreateDTO.setOperator(operator);
            takeAutoCreateDTO.setOperatorMemberId(orderDTO.getBuyerId());
            takeAutoCreateDTO.setVehicles(vehicles);
            messageQueueService.sendMQ(takeAutoCreateDTO, COM_ECOMMERCE_ORDER_TAKE_INFO_AUTO_CREATE);
        }
    }


    private Boolean getAutoCreateTakeInfo(OrderDTO orderDTO) {
        Boolean autoCreateTakeInfo = Boolean.FALSE;
        //返回的时候没有orderPayInfo手动生成空对象适配前端订单准备页跳转逻辑
        if (CollectionUtils.isEmpty(orderDTO.getOrderPayinfo())) {
            OrderPayinfoDTO orderPayinfoDTO = new OrderPayinfoDTO();
            orderPayinfoDTO.setPayinfoId("NONE");
            orderDTO.setOrderPayinfo(Lists.newArrayList(orderPayinfoDTO));
            autoCreateTakeInfo = !orderDTO.getIfNeedsellerconfirm();
        } else if (CsStringUtils.isNotBlank(orderDTO.getAutoPayWay())) {
            //如果选择了自动支付方式，完成自动支付
            try {
                OrderPayinfoDTO orderPayinfoDTO = orderDTO.getOrderPayinfo().get(0);
                orderPayinfoDTO.setPayinfoWay(orderDTO.getAutoPayWay());
                orderPaymentService.completeAutoPay(orderPayinfoDTO);
                //需要自动支付成功后才能自动创建发货单
                autoCreateTakeInfo = Boolean.TRUE;
            } catch (Exception e) {
                log.error("授信扣款失败，{}", e);
            }
        }
        return autoCreateTakeInfo;
    }

    @Override
    public OrderDTO createOrder(OrderDTO orderDTO, String operator) {
        log.info("createOrder:" + JSON.toJSONString(orderDTO));
        //订单数据准备
        orderDataPrepare(orderDTO);
        log.info("orderDataPrepare:" + JSON.toJSONString(orderDTO));

        //计算空载和台班费
        orderComputeService.computeMachineAndEmptyCost(orderDTO);
        log.info("computeMachineAndEmptyCost:" + JSON.toJSONString(orderDTO));

        //计算订单商品优惠和资源金额
        orderComputeService.computeOrderItem(orderDTO, true);
        log.info("computeOrderItem:" + JSON.toJSONString(orderDTO));

        //获取订单支付信息，计算物流金额
        orderComputeService.computeOrderLogisticsFee(orderDTO, true);
        log.info("computeOrderLogisticsFee:" + JSON.toJSONString(orderDTO));

//        //不需要卖家确认直接创建支付信息(船运订单不需要创建支付信息)

        //汽运才需要创建支付单
        if ((CsStringUtils.equals(orderDTO.getOrderInfoExtDTO().getTransportType(), TransportToolTypeEnum.ROAD_TRANSPORT.getCode()))
            //不需要确认或者下单直接创建支付单
            && (!orderDTO.getIfNeedsellerconfirm() || CollectionUtils.isNotEmpty(orderDTO.getOrderItems().get(0).getOrderItemAdds()))) {
                orderDTO.setOrderPayinfo(Lists.newArrayList(orderPaymentService.generateOrderPayInfo(orderDTO)));
        }

        //订单金额校验
        if (!CsStringUtils.equals(orderDTO.getOrderInfoExtDTO().getTransportType(),
                TransportToolTypeEnum.WATER_TRANSPORT.getCode())) {
            orderComputeService.checkOrderAmount(orderDTO);
        }

        //触发订单创建事件
        StatusModelDO statusModelDO = new StatusModelDO();
        statusModelDO.setBizId(orderDTO.getOrderId());
        statusModelDO.setCurrentState(OrderStatusEnum.CREATE.getCode());
        Map<String, Object> externalParams = Maps.newHashMap();
        externalParams.put(ORDER_DTO, orderDTO);
        externalParams.put(OPERATOR, operator);
        externalParams.put("needSellerConfirm", orderDTO.getIfNeedsellerconfirm());
        externalParams.put(IF_CONCRETE, CsStringUtils.isNotBlank(orderDTO.getOrderInfoExtDTO().getSigner()));
        statusModelDO.setExternalParams(externalParams);
        orderStateMachineEventManager.sendStatusChangeEvent(statusModelDO, OrderChangeEventEnum.CREATE_ORDER);

        //订单后置处理器
        orderCreatePostProcessor(orderDTO, operator);

        return orderDTO;
    }

    /**
     * 持久化订单确认数据
     *
     * @param orderDTO 订单对象
     * @param operator 操作人
     */
    @Transactional(rollbackFor = Exception.class)
    public void persistOrderConfirmData(OrderDTO orderDTO, OrderDTO confirmDTO, String operator) {
        log.info("persistOrderConfirmData:{}", JSON.toJSONString(orderDTO));

        //更新订单信息
        orderInfoBiz.saveConfirmOrderInfo(orderDTO, operator);

        //更新订单明细信息
        orderItemBiz.batchSaveOrderItem(orderDTO.getOrderItems(), operator);

        //保存支付信息
        if (CollectionUtils.isNotEmpty(orderDTO.getOrderPayinfo())) {
            for (OrderPayinfoDTO orderPayinfoDTO : orderDTO.getOrderPayinfo()) {
                orderPayinfoDTO.setObjectId(orderDTO.getOrderId());
                orderPayinfoDTO.setObjectCode(orderDTO.getOrderCode());
                orderPayinfoBiz.createOrderPayInfo(orderPayinfoDTO, operator);
            }
        }

        //变更资源锁定量
        if (CollectionUtils.isNotEmpty(confirmDTO.getOrderItems())) {
            Map<String, OrderItemDTO> changeItemMap = Maps.newHashMap();
            confirmDTO.getOrderItems().stream().forEach(changeItem ->
                    changeItemMap.put(changeItem.getOrderItemId(), changeItem));
            List<OrderItemDTO> changeOrderItemList = Lists.newArrayList();
            orderDTO.getOrderItems().stream().forEach(orderItem -> {
                OrderItemDTO changeItem = new OrderItemDTO();
                BeanUtils.copyProperties(orderItem, changeItem);
                if (changeItemMap.get(orderItem.getOrderItemId()) != null) {
                    changeItem.setItemQuantity(ArithUtils.subtract(false,
                            changeItemMap.get(orderItem.getOrderItemId()).getItemQuantity(), orderItem.getItemQuantity()));
                }
                changeOrderItemList.add(changeItem);
            });
            updateResourceNum(orderDTO, changeOrderItemList, operator);
        }
    }

    @Override
    public void confirmOrder(OrderDTO confirmOrder, String operator) {
        log.info("confirmOrder:" + JSON.toJSONString(confirmOrder));
        String orderId = confirmOrder.getOrderId();
        OrderInfo orderInfo = getOrderInfo(orderId);
        OrderDTO orderDTO = orderQueryService.getOrderDetail(orderId);
        //卖家备注
        if (CsStringUtils.isNotEmpty(confirmOrder.getSellerMemo())) {
            orderDTO.setSellerMemo(confirmOrder.getSellerMemo());
        }
        //更新业务员信息
        if (CsStringUtils.isNotBlank(confirmOrder.getSalesmanId())) {
            orderDTO.setSalesmanId(confirmOrder.getSalesmanId());
            baseOrderSearchBiz.setOrderSalesman(orderDTO);
            orderDTO.setSalesmanId(confirmOrder.getSalesmanId());
            orderDTO.setSalesmanName(confirmOrder.getSalesmanName());
        }
        //船运单确认直接变更订单状态
        if (CsStringUtils.equals(orderDTO.getOrderInfoExtDTO().getTransportType(),
                TransportToolTypeEnum.WATER_TRANSPORT.getCode())) {
            //触发订单确认事件
            StatusModelDO statusModelDO = new StatusModelDO();
            statusModelDO.setBizId(orderDTO.getOrderId());
            statusModelDO.setCurrentState(OrderStatusEnum.CONFIRMING.getCode());
            Map<String, Object> externalParams = Maps.newHashMap();
            externalParams.put(ORDER_DTO, orderDTO);
            externalParams.put("confirmOrder", confirmOrder);
            externalParams.put(OPERATOR, operator);
            statusModelDO.setExternalParams(externalParams);
            orderStateMachineEventManager.sendStatusChangeEvent(statusModelDO, OrderChangeEventEnum.CONFIRM_ORDER);
            //承运商代客下单(确认后自动生成发货单)
            if (orderDTO.getOrderInfoExtDTO() != null &&
                    CsStringUtils.isNotBlank(orderDTO.getOrderInfoExtDTO().getProxyCarrierId())) {
                TakeAutoCreateDTO takeAutoCreateDTO = new TakeAutoCreateDTO();
                takeAutoCreateDTO.setOrderId(orderDTO.getOrderId());
                takeAutoCreateDTO.setOperator(operator);
                takeAutoCreateDTO.setOperatorMemberId(orderDTO.getBuyerId());
                takeAutoCreateDTO.setTakeCarrierDTO(orderDTO.getTakeCarrierDTO());
                messageQueueService.sendMQ(takeAutoCreateDTO, COM_ECOMMERCE_ORDER_TAKE_INFO_AUTO_CREATE);
            }
            return;
        }
//        //台班费

        //计算订单行
        Boolean reComputeOrderAmount = orderComputeService.computeOrderChangeItem(confirmOrder, orderDTO);
        if (Boolean.TRUE.equals(reComputeOrderAmount)) {
            //计算订单费用
            orderComputeService.computeOrderItem(orderDTO, false);
        } else {
            //重置商品原始资源费用
            BigDecimal originResourceAmount = BigDecimal.ZERO;
            for (OrderItemDTO orderItem : orderDTO.getOrderItems()) {
                originResourceAmount = ArithUtils.add(originResourceAmount,
                        ArithUtils.multiply(orderItem.getOriginUnitPrice(), orderItem.getItemQuantity()));
            }
            orderDTO.setOriginResourceAmount(originResourceAmount);
        }
        //计算物流费
        orderComputeService.computeOrderLogisticsFee(orderDTO, false);
        //订单实际总金额 = 物流 + 资源 + 其他
        orderDTO.setActualOrderAmount(ArithUtils.add(
                orderDTO.getActualResourceAmount(),
                orderDTO.getActualLogisticAmount(),
                orderDTO.getActualOthersAmount()));
        //订单实际总金额 = 物流 + 资源 + 其他
        orderDTO.setOriginOrderAmount(ArithUtils.add(
                orderDTO.getOriginResourceAmount(),
                orderDTO.getOriginLogisticAmount(),
                orderDTO.getOriginOthersAmount()));

        ItemResult<SpecialGoodsAttributeDTO> goodsResult =
                goodsService.getSpecialGoodsAttribute(orderDTO.getOrderItems().get(0).getGoodsId());
        if (!goodsResult.isSuccess()) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "获取商品特殊属性失败");
        }
        //生成订单支付信息(确认的时候支付单已生成)
        if (goodsResult.getData().getConcreteFlag() == 1) {
            orderDTO.setOrderPayinfo(Lists.newArrayList());
        } else {
            orderDTO.setOrderPayinfo(Lists.newArrayList(orderPaymentService.generateOrderPayInfo(orderDTO)));
        }
        //订单金额check
        orderComputeService.checkOrderAmount(orderDTO);

//        //信息

        //触发订单确认事件
        StatusModelDO statusModelDO = new StatusModelDO();
        statusModelDO.setBizId(orderDTO.getOrderId());
        statusModelDO.setCurrentState(OrderStatusEnum.CONFIRMING.getCode());
        Map<String, Object> externalParams = Maps.newHashMap();
        externalParams.put("confirmOrder", confirmOrder);
        externalParams.put(OPERATOR, operator);
        externalParams.put(IF_CONCRETE, CsStringUtils.isNotBlank(orderDTO.getOrderInfoExtDTO().getSigner()));
        //更新订单状态()
        sendStatusChangeEvent(operator, orderInfo, orderDTO, externalParams, statusModelDO);
        //判断是否使用自动支付
        completeAutoPay(operator, orderInfo, goodsResult, orderDTO);
    }

    private void sendStatusChangeEvent(String operator, OrderInfo orderInfo, OrderDTO orderDTO, Map<String, Object> externalParams, StatusModelDO statusModelDO) {
        OrderInfo saveOrder = new OrderInfo();
        saveOrder.setOrderId(orderInfo.getOrderId());
        saveOrder.setSellerId(orderInfo.getSellerId());
        saveOrder.setOrderType(orderInfo.getOrderType());
        if (CsStringUtils.isNotBlank(orderDTO.getOrderInfoExtDTO().getSigner())) {
            orderPaymentService.setOrderTakeTimeLimit(saveOrder);
            orderDTO.setTakeTimeLimit(saveOrder.getTakeTimeLimit());
        }
        BaseBiz.setUpdateOperInfo(saveOrder, operator);
        externalParams.put(SAVE_ORDER, saveOrder);
        externalParams.put(ORDER_DTO, orderDTO);
        statusModelDO.setExternalParams(externalParams);
        orderStateMachineEventManager.sendStatusChangeEvent(statusModelDO, OrderChangeEventEnum.CONFIRM_ORDER);
    }

    private void completeAutoPay(String operator, OrderInfo orderInfo, ItemResult<SpecialGoodsAttributeDTO> goodsResult, OrderDTO orderDTO) {
        if (CsStringUtils.isNotEmpty(orderInfo.getAutoPayWay()) && goodsResult.getData().getConcreteFlag() != 1) {
            try {
                OrderPayinfoDTO orderPayinfoDTO = orderDTO.getOrderPayinfo().get(0);
                orderPayinfoDTO.setPayinfoWay(orderInfo.getAutoPayWay());
                orderPaymentService.completeAutoPay(orderPayinfoDTO);
                //承运商代客下单(确认后自动生成发货单)
                if (orderDTO.getOrderInfoExtDTO() != null &&
                        CsStringUtils.isNotBlank(orderDTO.getOrderInfoExtDTO().getProxyCarrierId())) {
                    TakeAutoCreateDTO takeAutoCreateDTO = new TakeAutoCreateDTO();
                    takeAutoCreateDTO.setOrderId(orderDTO.getOrderId());
                    takeAutoCreateDTO.setOperator(operator);
                    takeAutoCreateDTO.setOperatorMemberId(orderDTO.getBuyerId());
                    takeAutoCreateDTO.setTakeCarrierDTO(orderDTO.getTakeCarrierDTO());
                    messageQueueService.sendMQ(takeAutoCreateDTO, COM_ECOMMERCE_ORDER_TAKE_INFO_AUTO_CREATE);
                }
            } catch (Exception e) {
                log.error("授信扣款失败，{}", e);
            }
        }
    }


    private OrderInfo getOrderInfo(String orderId) {
        if (CsStringUtils.isBlank(orderId)) {
            throw new BizException(BasicCode.PARAM_NULL, "订单Id");
        }
        OrderInfo orderInfo = orderInfoBiz.getOrderInfoById(orderId);
        if (!CsStringUtils.equals(OrderStatusEnum.CONFIRMING.getCode(), orderInfo.getOrderStatus())) {
            if (CsStringUtils.equals(OrderStatusEnum.WAIT_PAYMENT.getCode(), orderInfo.getOrderStatus())) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "当前订单已被确认，请刷新页面后查看");
            }
            throw new BizException(BasicCode.UNDEFINED_ERROR, "当前订单状态异常，请联系管理员");
        }
        return orderInfo;
    }

    @Override
    public void closeOrder(OrderCloseDO orderCloseDO) {
        log.info("closeOrder:" + JSON.toJSONString(orderCloseDO));
        if (CsStringUtils.isBlank(orderCloseDO.getOrderId())) {
            throw new BizException(BasicCode.PARAM_NULL, "订单Id");
        }
        if (CsStringUtils.isBlank(orderCloseDO.getOperator())) {
            throw new BizException(BasicCode.PARAM_NULL, "操作人");
        }
        if (CsStringUtils.isBlank(orderCloseDO.getOperatorType())) {
            throw new BizException(BasicCode.PARAM_NULL, "操作人类型");
        }
        OrderInfo orderInfo = orderInfoBiz.getOrderInfoById(orderCloseDO.getOrderId());
        //关闭前置状态集合
        Set<String> closePreStatusSet = Sets.newHashSet(
                OrderStatusEnum.WAIT_DELIVERED.getCode(),
                OrderStatusEnum.IN_DELIVERY.getCode(),
                OrderStatusEnum.IN_PAYMENT.getCode(),
                OrderStatusEnum.CONFIRMING.getCode()
        );
        if (!closePreStatusSet.contains(orderInfo.getOrderStatus())) {
            log.error("当前订单状态异常 orderId:{} orderStatus:{}", orderInfo.getOrderId(), orderInfo.getOrderStatus());
            throw new BizException(BasicCode.UNDEFINED_ERROR, "当前订单状态异常，请联系管理员");
        }
        //二级订单关闭验证
        orderProxyMapBiz.secondaryOrderCloseValidate(orderInfo);
        try {
            //关闭订单下的所有发货单
            takeInfoService.doSellerCloseOrder(orderCloseDO.getOrderId(), orderCloseDO.getOperator());
        } catch (Exception e) {
            log.error("closeOrder_doSellerCloseOrder_Error", e);
            throw new BizException(BasicCode.UNDEFINED_ERROR, "该订单无法关闭:" + e.getMessage());
        }
        //更新订单状态为已关闭
        OrderInfo saveOrder = new OrderInfo();
        saveOrder.setOrderId(orderCloseDO.getOrderId());
        saveOrder.setSellerId(orderInfo.getSellerId());
        saveOrder.setErpStatus(orderInfo.getErpStatus());
        saveOrder.setErporderCode(orderInfo.getErporderCode());
        if (CsStringUtils.equals(OperatorTypeEnum.SELLER.code(), orderCloseDO.getOperatorType())) {
            saveOrder.setIfSellerClose(true);
        } else if (CsStringUtils.equals(OperatorTypeEnum.PLATFORM.code(), orderCloseDO.getOperatorType())) {
            saveOrder.setIfPlatformClose(true);
        }
        saveOrder.setBuyerCancelReason(orderCloseDO.getRemarks());
        saveOrder.setCloseTime(new Date());
        setOperInfo(saveOrder, orderCloseDO.getOperator(), false);

        //触发订单关闭事件
        StatusModelDO statusModelDO = new StatusModelDO();
        statusModelDO.setBizId(orderCloseDO.getOrderId());
        OrderStatusEnum orderStatusEnum = OrderStatusEnum.getByCode(orderInfo.getOrderStatus());
        if (orderStatusEnum == null) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, UNDEFINDED_ERROR_MSG);
        }
        statusModelDO.setCurrentState(orderStatusEnum.getCode());
        Map<String, Object> externalParams = Maps.newHashMap();
        externalParams.put(SAVE_ORDER, saveOrder);
        externalParams.put("operatorType", orderCloseDO.getOperatorType());
        externalParams.put("currentOrderStatus", orderInfo.getOrderStatus());
        //设置运输类型
        OrderInfoExtDTO orderInfoExtDTO = orderInfoExtBiz.findByOrderId(orderCloseDO.getOrderId());
        externalParams.put("transportType", orderInfoExtDTO.getTransportType());
        statusModelDO.setExternalParams(externalParams);
        orderStateMachineEventManager.sendStatusChangeEvent(statusModelDO, OrderChangeEventEnum.CLOSE_ORDER);

        //关闭触发分账操作
        orderSplitBillService.triggerSplitByOrder(orderCloseDO.getOrderId());

        //累计会员积分信息(实际发生金额大于0)
        Optional.ofNullable(orderInfo.getRealtimeOrderAmount())
                .filter(realTimeOrderAmount -> realTimeOrderAmount.compareTo(BigDecimal.ZERO) > 0)
                .ifPresent(realTimeOrderAmount -> orderPaymentService.addUpMemberIntegral(orderInfo));

        //代理订单触发动作映射
        if (CsStringUtils.equals(orderInfo.getProxyOrderType(), ProxyOrderTypeEnum.PRIMARY.getCode())) {
            ProxyOrderActionMapDTO proxyOrderActionMapDTO = new ProxyOrderActionMapDTO();
            proxyOrderActionMapDTO.setPrimaryOrderId(orderCloseDO.getOrderId());
            proxyOrderActionMapDTO.setOperatorId(orderCloseDO.getOperator());
            proxyOrderActionMapDTO.setOrderChangeEvent(OrderChangeEventEnum.CLOSE_ORDER.getCode());
            proxyOrderActionMapDTO.setParam("remarks", orderCloseDO.getRemarks());
            log.info("close_proxyOrderActionMapDTO from:{}", proxyOrderActionMapDTO);
            //延时执行同步操作，为保证数据一致性
            CompletableFuture.runAsync(() -> {
                try {
                    Thread.sleep(SLEEP_MS);
                } catch (InterruptedException e) {
                    log.error("同步关闭订单延时异常:{}", proxyOrderActionMapDTO, e);
                    Thread.currentThread().interrupt();
                }
                messageQueueService.sendMQ(proxyOrderActionMapDTO, "com.ecommerce.order.proxy.close.map");
            });
        }
        //初略判断一下
        if (CsStringUtils.equals(orderInfoExtDTO.getTransportType(), TransportToolTypeEnum.ROAD_TRANSPORT.getCode())) {
            sendFloorTruckage(orderInfo.getOrderId(), orderInfo.getOrderCode());
        }

        orderInfoBiz.splitCompleteNotifyPay(orderInfo.getOrderCode(), "订单关闭:" + orderCloseDO.getRemarks());
    }

    @Override
    public void cancelOrder(OrderCancelDO orderCancelDO) {
        log.info("orderCancelDO:" + JSON.toJSONString(orderCancelDO));
        if (CsStringUtils.isBlank(orderCancelDO.getOrderId())) {
            throw new BizException(BasicCode.PARAM_NULL, "订单Id");
        }
        if (CsStringUtils.isBlank(orderCancelDO.getOperator())) {
            throw new BizException(BasicCode.PARAM_NULL, "操作人");
        }
        if (CsStringUtils.isBlank(orderCancelDO.getOperatorType())) {
            throw new BizException(BasicCode.PARAM_NULL, "操作人类型");
        }
        if ((CsStringUtils.equals(OperatorTypeEnum.SELLER.getCode(), orderCancelDO.getOperatorType()))
            && (baseOrderSearchBiz.isInPayment(orderCancelDO.getOrderId()))) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "订单存在待支付的支付信息，无法取消");
        }
        OrderInfo orderInfo = orderInfoBiz.getOrderInfoById(orderCancelDO.getOrderId());
        if (CsStringUtils.equals(OrderStatusEnum.CANCEL.getCode(), orderInfo.getOrderStatus())) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "当前订单已被取消，请刷新页面后查看");
        }
        OrderInfo saveOrder = new OrderInfo();
        saveOrder.setOrderId(orderCancelDO.getOrderId());
        //更新订单状态为已取消
        if (CsStringUtils.equals(OperatorTypeEnum.SELLER.code(), orderCancelDO.getOperatorType())) {
            saveOrder.setIfSellerClose(true);
        } else if (CsStringUtils.equals(OperatorTypeEnum.PLATFORM.code(), orderCancelDO.getOperatorType())) {
            saveOrder.setIfPlatformClose(true);
        }
        saveOrder.setBuyerCancelReason(orderCancelDO.getRemarks());
        BaseBiz.setOperInfo(saveOrder, orderCancelDO.getOperator(), false);

        //触发订单取消事件
        StatusModelDO statusModelDO = new StatusModelDO();
        statusModelDO.setBizId(orderCancelDO.getOrderId());
        OrderStatusEnum orderStatusEnum = OrderStatusEnum.getByCode(orderInfo.getOrderStatus());
        if (orderStatusEnum == null) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, UNDEFINDED_ERROR_MSG);
        }
        statusModelDO.setCurrentState(orderStatusEnum.getCode());
        Map<String, Object> externalParams = Maps.newHashMap();
        externalParams.put(SAVE_ORDER, saveOrder);
        statusModelDO.setExternalParams(externalParams);
        orderStateMachineEventManager.sendStatusChangeEvent(statusModelDO, OrderChangeEventEnum.CANCEL_ORDER);
        //代理订单触发动作映射
        if (CsStringUtils.equals(orderInfo.getProxyOrderType(), ProxyOrderTypeEnum.PRIMARY.getCode())) {
            ProxyOrderActionMapDTO proxyOrderActionMapDTO = new ProxyOrderActionMapDTO();
            proxyOrderActionMapDTO.setPrimaryOrderId(orderCancelDO.getOrderId());
            proxyOrderActionMapDTO.setOperatorId(orderCancelDO.getOperator());
            proxyOrderActionMapDTO.setOrderChangeEvent(OrderChangeEventEnum.CANCEL_ORDER.getCode());
            proxyOrderActionMapDTO.setParam("remarks", orderCancelDO.getRemarks());
            log.info("close_proxyOrderActionMapDTO from:{}", proxyOrderActionMapDTO);
            //延时执行同步操作，为保证数据一致性
            CompletableFuture.runAsync(() -> {
                try {
                    Thread.sleep(SLEEP_MS);
                } catch (InterruptedException e) {
                    log.error("同步关闭订单延时异常:{}", proxyOrderActionMapDTO, e);
                    Thread.currentThread().interrupt();
                }
                messageQueueService.sendMQ(proxyOrderActionMapDTO, "com.ecommerce.order.proxy.close.map");
            });
        }
    }

    @Override
    public void completeOrder(OrderCompleteDO orderCompleteDO) {
        log.info("completeOrder:" + JSON.toJSONString(orderCompleteDO));
        if (CsStringUtils.isBlank(orderCompleteDO.getOrderId())) {
            throw new BizException(BasicCode.PARAM_NULL, "订单Id");
        }
        if (CsStringUtils.isBlank(orderCompleteDO.getOperator())) {
            throw new BizException(BasicCode.PARAM_NULL, "操作人");
        }
        OrderInfo orderInfo = orderInfoBiz.getOrderInfoById(orderCompleteDO.getOrderId());
        if (CsStringUtils.equals(OrderStatusEnum.COMPLETED.getCode(), orderInfo.getOrderStatus())) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "当前订单已完成，请刷新页面后查看");
        }

        OrderInfo saveOrder = new OrderInfo();
        saveOrder.setOrderId(orderInfo.getOrderId());
        saveOrder.setSellerId(orderInfo.getSellerId());
        saveOrder.setDeliverStatus(OrderTakeStatus.FINISHED.getCode());
        saveOrder.setCompleteTime(new Date());
        BaseBiz.setOperInfo(saveOrder, orderCompleteDO.getOperator(), false);

        //触发订单取消事件
        StatusModelDO statusModelDO = new StatusModelDO();
        statusModelDO.setBizId(orderCompleteDO.getOrderId());
        OrderStatusEnum orderStatusEnum = OrderStatusEnum.getByCode(orderInfo.getOrderStatus());
        if (orderStatusEnum == null) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, UNDEFINDED_ERROR_MSG);
        }
        statusModelDO.setCurrentState(orderStatusEnum.getCode());
        Map<String, Object> externalParams = Maps.newHashMap();
        externalParams.put(SAVE_ORDER, saveOrder);
        //设置运输类型
        OrderInfoExtDTO orderInfoExtDTO = orderInfoExtBiz.findByOrderId(orderCompleteDO.getOrderId());
        externalParams.put("transportType", orderInfoExtDTO.getTransportType());
        externalParams.put(IF_CONCRETE, CsStringUtils.isNotBlank(orderInfoExtDTO.getSigner()));
        statusModelDO.setExternalParams(externalParams);
        orderStateMachineEventManager.sendStatusChangeEvent(statusModelDO, OrderChangeEventEnum.COMPLETE_ORDER);

        //完成订单时触发未分账金额分账
        orderSplitBillService.triggerSplitByOrder(orderCompleteDO.getOrderId());

        //累计会员积分信息
        orderPaymentService.addUpMemberIntegral(orderInfo);

        //发送消息
        messageProducer.sendOrderCompletedAsync(orderCompleteDO.getOrderId());

        //代理订单触发动作映射
        if (CsStringUtils.isNotBlank(orderInfo.getProxyOrderType())) {
            ProxyOrderActionMapDTO proxyOrderActionMapDTO = new ProxyOrderActionMapDTO();
            if (CsStringUtils.equals(orderInfo.getProxyOrderType(), ProxyOrderTypeEnum.PRIMARY.getCode())) {
                proxyOrderActionMapDTO.setPrimaryOrderId(orderCompleteDO.getOrderId());
            }
            if (CsStringUtils.equals(orderInfo.getProxyOrderType(), ProxyOrderTypeEnum.SECONDARY.getCode())) {
                proxyOrderActionMapDTO.setSecondaryOrderId(orderCompleteDO.getOrderId());
            }
            proxyOrderActionMapDTO.setOperatorId(orderCompleteDO.getOperator());
            proxyOrderActionMapDTO.setOrderChangeEvent(OrderChangeEventEnum.COMPLETE_ORDER.getCode());
            log.info("complete_proxyOrderActionMapDTO from:{}", proxyOrderActionMapDTO);
            //延时执行同步操作，为保证数据一致性
            CompletableFuture.runAsync(() -> {
                try {
                    Thread.sleep(SLEEP_MS);
                } catch (InterruptedException e) {
                    log.error("同步完成订单延时异常:{}", proxyOrderActionMapDTO, e);
                    Thread.currentThread().interrupt();
                }
                messageQueueService.sendMQ(proxyOrderActionMapDTO, "com.ecommerce.order.proxy.complete.map");
            });
        }
        //初略判断一下
        if (CsStringUtils.equals(orderInfoExtDTO.getTransportType(), TransportToolTypeEnum.ROAD_TRANSPORT.getCode())) {
            sendFloorTruckage(orderInfo.getOrderId(), orderInfo.getOrderCode());
        }

        orderInfoBiz.splitCompleteNotifyPay(orderInfo.getOrderCode(), "订单完成");

    }

    @Override
    public void shippedPostProcessor(String orderId, UpdateTakeInfoShippedDTO updateTakeInfoShippedDTO) {
        //do nothing
    }

    //触发通知所有订单的台班费通知给对账使用
    @Override
    public void floorTruckageNotifyAll(List<String> orderIds) {
        if (CollectionUtils.isNotEmpty(orderIds)) {
            for (String orderId : orderIds) {
                OrderInfo orderInfo = orderInfoBiz.get(orderId);
                if (orderInfo != null) {
                    sendFloorTruckage(orderId, orderInfo.getOrderCode());
                }
            }
            return;
        }
        //查询有台班费的订单项
        Condition condition = new Condition(OrderItem.class);
        condition.createCriteria().andEqualTo("delFlg", false).andGreaterThan("actualMachinePrice", BigDecimal.ZERO);
        for (OrderItem orderItem : orderItemBiz.findByCondition(condition)) {
            OrderItemDTO orderItemDTO = new OrderItemDTO();
            orderItemDTO.setGoodsId(orderItem.getGoodsId());
            orderItemDTO.setActualMachinePrice(orderItem.getActualMachinePrice());

            OrderInfo orderInfo = orderInfoBiz.get(orderItem.getOrderId());
            OrderDTO orderDTO = new OrderDTO();
            orderDTO.setOrderCode(orderInfo.getOrderCode());

            orderDTO.setOrderItems(Lists.newArrayList(orderItemDTO));

            log.info("com.ecommerce.order.floorTruckage.notify: {}", JSON.toJSONString(orderDTO));
            messageQueueService.sendMQ(orderDTO, "com.ecommerce.order.floorTruckage.notify");
        }
    }

    //台班费通知给对账使用
    private void sendFloorTruckage(String orderId, String orderCode) {
        if (CsStringUtils.isBlank(orderId) || CsStringUtils.isBlank(orderCode)) {
            return;
        }
        //异步
        CompletableFuture.runAsync(() -> {
            List<OrderItem> orderItems = orderItemBiz.findByOrderId(orderId);
            if (CollectionUtils.isNotEmpty(orderItems)) {
                List<OrderItemDTO> orderItemDTOList = orderItems.stream()
                        //如果存在台班费
                        .filter(item -> item.getActualMachinePrice() != null && item.getActualMachinePrice().compareTo(BigDecimal.ZERO) > 0)
                        .map(item -> {
                            OrderItemDTO orderItemDTO = new OrderItemDTO();
                            orderItemDTO.setGoodsId(item.getGoodsId());
                            orderItemDTO.setActualMachinePrice(item.getActualMachinePrice());
                            return orderItemDTO;
                        }).toList();
                //如果存在台班费
                if (CollectionUtils.isNotEmpty(orderItemDTOList)) {
                    OrderDTO orderDTO = new OrderDTO();
                    orderDTO.setOrderCode(orderCode);
                    orderDTO.setOrderItems(orderItemDTOList);
                    log.info("com.ecommerce.order.floorTruckage.notify: {}", JSON.toJSONString(orderDTO));
                    messageQueueService.sendMQ(orderDTO, "com.ecommerce.order.floorTruckage.notify");
                }
            }
        });
    }

}
