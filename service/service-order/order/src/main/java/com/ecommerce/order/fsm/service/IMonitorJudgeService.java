package com.ecommerce.order.fsm.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.order.api.dto.MonitorJudgeCondDTO;
import com.ecommerce.order.api.dto.MonitorJudgeResDTO;

/**
 * 监控判断服务
 * @Auther: colu
 * @Date: 2020-03-05 14:36
 * @Description: IMonitorJudgeService
 */
public interface IMonitorJudgeService {

    ItemResult<MonitorJudgeResDTO> monitorJudge(MonitorJudgeCondDTO monitorJudgeCondDTO);

}
