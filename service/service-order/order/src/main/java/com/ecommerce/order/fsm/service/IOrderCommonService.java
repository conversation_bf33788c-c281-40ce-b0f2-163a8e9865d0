package com.ecommerce.order.fsm.service;

import com.ecommerce.order.api.dto.CrossMonthCloseTriggerDTO;
import com.ecommerce.order.api.dto.OrderItemDTO;
import com.ecommerce.order.api.dto.OrderRefundDTO;
import com.ecommerce.order.api.dto.TakeTimeLimitTriggerDTO;
import com.ecommerce.order.api.enums.TakeCloseType;
import com.ecommerce.order.dao.vo.OrderInfo;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public interface IOrderCommonService {
    /**
     * 触发订单自动结束
     * @param orderId 订单ID
     */
    void triggerOrderAutoEnd(String orderId);

    /**
     * 尝试自动完成订单
     * @param orderId 订单ID
     */
    void tryAutoCompleteOrder(String orderId);

    /**
     * 处理订单退款
     * @param orderInfo
     * @param refundGoodsAmount
     * @param refundLogisticsAmount
     * @param refundOtherAmount
     * @param refundReason
     */
    void doOrderRefund(OrderInfo orderInfo, BigDecimal refundGoodsAmount, BigDecimal refundLogisticsAmount, BigDecimal refundOtherAmount, String refundReason);

    /**
     * 关闭跨月订单
     * @param crossMonthCloseTriggerDTO 跨月订单关闭对象
     */
    void closeCrossMonthOrder(CrossMonthCloseTriggerDTO crossMonthCloseTriggerDTO);

    /**
     * 尝试自动关闭订单
     * @param orderId 订单ID
     */
    boolean tryAutoCloseOrder(String orderId, String closeReason, TakeCloseType takeCloseType);

    /**
     * 关闭提货有效期失效订单
     */
    void closeExpireOrder(TakeTimeLimitTriggerDTO takeTimeLimitTriggerDTO);

    /**
     * 尝试手动关闭订单
     * @param orderRefundDTO 退款对象
     */
    String tryManualCloseOrder(OrderRefundDTO orderRefundDTO);

    /**
     * 执行订单价格调整
     * @param orderId 订单ID
     * @param orderItemList 订单明细列表
     */
    void executeOrderPriceAdjust(String orderId, List<OrderItemDTO> orderItemList, Map<String, BigDecimal> mortarAdjustMap);
}
