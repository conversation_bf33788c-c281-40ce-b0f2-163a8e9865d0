package com.ecommerce.order.fsm.service;

import com.ecommerce.goods.api.dto.contract.TrContractGoodsDTO;
import com.ecommerce.order.api.dto.OrderCostsDTO;
import com.ecommerce.order.api.dto.OrderDTO;
import com.ecommerce.order.api.dto.OrderDiscountDetailDTO;
import com.ecommerce.order.api.dto.OrderRecCostsDTO;
import com.ecommerce.order.api.dto.adjust.ContractAdjustDTO;
import com.ecommerce.order.api.dto.pay.TakeLogisticsFeeDTO;
import com.ecommerce.order.api.dto.proxy.PrimaryOrderCarriageDTO;
import com.ecommerce.order.api.dto.proxy.PrimaryOrderCreateDTO;
import com.ecommerce.order.dao.vo.OrderInfo;
import com.ecommerce.order.dao.vo.OrderItem;

import java.math.BigDecimal;
import java.util.List;

/**
 *
 * <AUTHOR>
 * Date: Create in 下午2:28 20/6/8
 */
public interface IOrderComputeService {
    /**
     * 检查订单金额
     * @param orderDTO 订单对象
     */
    void checkOrderAmount(OrderDTO orderDTO);

    /**
     * 重新计算订单费用
     * @param orderRecCostsDTO 重新计算费用对象
     * @return OrderCostsDTO
     */
    OrderCostsDTO reComputeOrderCosts(OrderRecCostsDTO orderRecCostsDTO);

    /**
     * 计算订单费用
     * @param orderDTO 订单对象
     * @return OrderCostsDTO
     */
    OrderCostsDTO computeOrderCosts(OrderDTO orderDTO);

    /**
     * 计算搬运费
     * @param orderDTO 订单对象
     * @return BigDecimal
     */
    BigDecimal computePorterage(OrderDTO orderDTO);

    /**
     * 计算台班费和空载费
     * @param order 订单对象
     */
    void computeMachineAndEmptyCost(OrderDTO order);

    /**
     * 计算订单费用及优惠
     * @param order 订单对象
     * @param processPromot 是否处理优惠
     * @return List<OrderDiscountDetailDTO>
     */
    List<OrderDiscountDetailDTO> computeOrderItem(OrderDTO order, boolean processPromot);

    /**
     * 计算订单物流费
     * @param order 订单对象
     * @param reversePush 是否执行到位价反推
     */
    void computeOrderLogisticsFee(OrderDTO order, Boolean reversePush);

    /**
     * 计算发货单物流费
     * @return
     */
    TakeLogisticsFeeDTO computeTakeLogisticsFee(OrderInfo order, OrderItem orderItem, TrContractGoodsDTO contractGoodsDTO, BigDecimal takeQuantity);

    /**
     * 计算确认价格数量
     * @param confirmOrder 确认订单对象
     * @param orderDTO 原始订单对象
     * @return 是否需要重新计算金额
     */
    Boolean computeOrderChangeItem(OrderDTO confirmOrder, OrderDTO orderDTO);

    /**
     * 计算背靠背一级订单物流费
     * @param primaryOrderCreateDTO 订单对象
     * @return PrimaryOrderCarriageDTO
     */
    PrimaryOrderCarriageDTO computePrimaryOrderCarriage(PrimaryOrderCreateDTO primaryOrderCreateDTO);

    /**
     * 计算订单实际台班费
     * @param orderDTO 订单对象
     * @return BigDecimal
     */
    BigDecimal computeOrderMachineCost(OrderDTO orderDTO);

    /**
     * 订单价格调整(合同调价触发)
     * @param contractAdjustDTO 合同调价对象
     */
    void concreteOrderPriceAdjust(ContractAdjustDTO contractAdjustDTO);

}
