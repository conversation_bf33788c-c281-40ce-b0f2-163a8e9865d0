package com.ecommerce.order.fsm.service;



import com.ecommerce.common.exception.BizException;import com.ecommerce.common.exception.BasicCode;import com.ecommerce.open.api.dto.apicenter.order.ERPOrderAdjustDTO;
import com.ecommerce.open.api.dto.apicenter.order.ERPOverSoldVerifyResponseDTO;
import com.ecommerce.order.api.dto.OrderDTO;
import com.ecommerce.order.api.dto.erp.ERPOverSoldDTO;
import com.ecommerce.order.api.dto.erp.OrderCloseERPCallback;
import com.ecommerce.order.api.dto.erp.OrderCompleteERPCallback;
import com.ecommerce.order.api.dto.erp.OrderCreateERPCallback;
import com.ecommerce.order.dao.vo.OrderInfo;
import com.ecommerce.order.dao.vo.OrderPayinfo;
import com.ecommerce.order.dao.vo.OrderPayinfoDetail;

import java.util.Date;
import java.util.List;

public interface IOrderErpService {

    /**
     * 订单创建传ERP
     * @param orderId
     * @throws Exception
     */
    void orderCreateToErp(String orderId, Date takeTimeLimit);
    /**
     * 订单创建传ERP(异步)
     * @param orderId
     * @throws Exception
     */
    void orderCreateToErpAsync(String orderId, Date takeTimeLimit);

    /**
     * ERP回调创建订单成功
     * @param orderCreateERPCallback
     * @return
     * @throws Exception
     */
    boolean orderCreateERPCallback(OrderCreateERPCallback orderCreateERPCallback, String operator)throws Exception;

    /**
     * 订单关闭传ERP
     * @param orderId
     * @throws Exception
     */
    void orderCloseToErp(String orderId);

    /**
     * ERP回调关闭订单成功
     * @param orderCloseERPCallback
     * @return
     * @throws Exception
     */
    boolean orderCloseERPCallback(OrderCloseERPCallback orderCloseERPCallback, String operator)throws Exception;

    /**
     * 订单完成传ERP
     * @param orderId
     * @return
     * @throws Exception
     */
    boolean orderCompleteToErp(String orderId);

    /**
     * ERP回调完成订单成功
     * @param orderCompleteERPCallback
     * @return
     * @throws Exception
     */
    boolean orderCompleteERPCallback(OrderCompleteERPCallback orderCompleteERPCallback, String operator)throws Exception;

    /**
     * ERP主动关闭订单
     * @param orderCloseERPCallback
     * @return
     * @throws Exception
     */
    boolean erpCloseOrder(OrderCloseERPCallback orderCloseERPCallback)throws Exception;

    /**
     * ERP主动完成订单
     * @param orderCompleteERPCallback
     * @return
     * @throws Exception
     */
    boolean erpCompleteOrder(OrderCompleteERPCallback orderCompleteERPCallback)throws Exception;

    /**
     * 三清校验
     * @param orderDTO
     * @return
     */
    boolean orderComplete(OrderDTO orderDTO);

    /**
     * 是否需要erp调度
     * @param orderInfo
     * @return
     * @throws Exception
     */
    boolean ERPDispatch(OrderDTO orderInfo);

    boolean ERPDispatch(OrderInfo orderInfo);

    /**
     * ERP出厂超发回调电商
     * @param erpOverSoldDTO
     * @return
     * @throws Exception
     */
    ERPOverSoldVerifyResponseDTO overSellConfirm(ERPOverSoldDTO erpOverSoldDTO)throws Exception;

    /**
     * 判断是否有erp
     * @param sellId
     * @param configCode 接入ERP标记
     * @return
     */
    boolean hasErpOrder(String sellId,String configCode);

    /**
     * 卖家确认发货单传ERP
     * @param takeCode 发货单号
     * @return
     */
    void sellerConfirmTakeInfoToErp(OrderInfo orderInfo ,String takeCode);

    /**
     * 卖家关闭发货单穿ERP
     * @param takeCode
     * @return
     */
    boolean sellerCloseTakeInfoToErp(String takeCode);

    void erpOverslodSupplement(String orderId, String operator);

    /**
     * ERP订单调价
     * @param erpOrderAdjustDTOList 订单调价对象列表
     * @return boolean
     */
    boolean doERPOrderAdjust(List<ERPOrderAdjustDTO> erpOrderAdjustDTOList);

    /**
     * 修改erp提货有效期
     * @param orderDTO 订单对象
     * @return boolean
     */
    boolean updateTakeEffectiveTime(OrderDTO orderDTO);

    void syncBillLogs(OrderPayinfo payinfo, OrderPayinfoDetail payDetail);

    /**
     * 通知Erp增加授信金额
     * @param payinfoId 支付单ID
     */
    void notifyErpIncreaseCreditAmount(String payinfoId);
}
