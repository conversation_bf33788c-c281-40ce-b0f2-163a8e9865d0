package com.ecommerce.order.fsm.service;

import com.ecommerce.order.api.dto.OrderDTO;
import com.ecommerce.order.api.dto.UpdateTakeInfoShippedDTO;
import com.ecommerce.order.dao.dto.OrderCancelDO;
import com.ecommerce.order.dao.dto.OrderCloseDO;
import com.ecommerce.order.dao.dto.OrderCompleteDO;

import java.util.List;

/**
 *  订单处理服务
 *
 * <AUTHOR>
 */
public interface IOrderHandlerService {

    String getServiceName();

    /**
     * 创建订单
     * @param orderDTO 订单对象
     * @return OrderDTO
     */
    OrderDTO createOrder(OrderDTO orderDTO, String operator);

    /**
     * 卖家确认订单
     * @param orderDTO 订单对象
     * @param operator 操作人
     */
    void confirmOrder(OrderDTO orderDTO, String operator);

    /**
     * 关闭订单
     * @param orderCloseDO 订单关闭对象
     */
    void closeOrder(OrderCloseDO orderCloseDO);

    /**
     * 取消订单
     * @param orderCancelDO 订单取消对象
     */
    void cancelOrder(OrderCancelDO orderCancelDO);

    /**
     * 取消订单
     * @param orderCompleteDO 订单完成对象
     */
    void completeOrder(OrderCompleteDO orderCompleteDO);

    /**
     * 发货后置处理器
     * @param orderId 订单ID
     * @param updateTakeInfoShippedDTO 发货信息对象
     */
    void shippedPostProcessor(String orderId, UpdateTakeInfoShippedDTO updateTakeInfoShippedDTO);

    /**
     * 触发推送指定订单或者所有订单的台班费给对账使用
     */
    void floorTruckageNotifyAll(List<String> orderIds);
}
