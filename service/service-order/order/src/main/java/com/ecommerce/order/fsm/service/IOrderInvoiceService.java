package com.ecommerce.order.fsm.service;

import com.ecommerce.order.api.dto.*;

/**
 * * Date: Create in 下午2:06 20/6/17
 * <AUTHOR>
 */
public interface IOrderInvoiceService {
    /**
     * 申请订单发票
     * @param applyOrderInvoiceRequestDTO 申请开票对象
     * @param operator 操作人
     * @return ApplyOrderInvoiceResponseDTO
     */
    ApplyOrderInvoiceResponseDTO applyOrderInvoice(ApplyOrderInvoiceRequestDTO applyOrderInvoiceRequestDTO, String operator);

    /**
     * 开票回调
     * @param orderInvoiceCallBackDTO 开票回调对象
     * @return boolean
     */
    boolean doOrderInvoiceCallBack(OrderInvoiceCallBackDTO orderInvoiceCallBackDTO);

    /**
     * 确认订单发票
     * @param confirmOrderInvoiceReqDTO 确认开票对象
     * @return boolean
     */
    boolean confirmOrderInvoice(ConfirmOrderInvoiceReqDTO confirmOrderInvoiceReqDTO);

    /**
     * 通过发票ID查询订单
     * @param invoiceId 开票ID
     * @return 订单对象
     */
    OrderDTO queryOrderByInvoiceId(String invoiceId);
}
