package com.ecommerce.order.fsm.service;

import com.ecommerce.order.api.dto.*;
import com.ecommerce.order.api.dto.pay.AutoPayWayQueryDTO;
import com.ecommerce.order.api.enums.PayDetailTypeEnum;
import com.ecommerce.order.api.enums.PayTypeEnum;
import com.ecommerce.order.dao.dto.PaymentAmountDO;
import com.ecommerce.order.dao.dto.PaymentAmountQueryDO;
import com.ecommerce.order.dao.vo.OrderInfo;

import java.math.BigDecimal;
import java.util.List;

/**
 * 上午11:46 20/6/8
 *
 * <AUTHOR>
 */
public interface IOrderPaymentService {

    /**
     * 订单确认线下支付
     */
    void confirmUnderLinePay(OrderUnderLinePayConfirmDTO orderUnderLinePayConfirmDTO);

    /**
     * 订单确认线下补款
     * @param orderId 订单ID
     * @param pass 是否通过
     * @param operator 操作人
     */
    void confirmUnderLineSupplement(String orderId, boolean pass, String operator);

    /**
     * 订单确认线下退款
     * @param orderId 订单ID
     * @param pass 是否通过
     * @param operator 操作人
     */
    void confirmUnderLineRefund(String orderId, boolean pass, String operator);

    /**
     * 支付ID线下支付确认
     * @param payinfoId 支付信息ID
     * @param pass 是否通过
     * @param operator 操作人
     */
    void confirmUnderLinePayinfoId(String payinfoId, boolean pass, String operator);


    /**
     * 创建订单支付信息
     * @param order 订单
     * @param payAmount 付款金额
     * @param payType 支付类型
     * @param payDetailType 支付详细类型
     * @param operator 操作人
     */
    void createOrderPayInfo(OrderInfo order, BigDecimal payAmount,
                            PayTypeEnum payType, PayDetailTypeEnum payDetailType, String operator);

    /**
     * 创建退款支付信息
     * @param payinfoCreateDTO 支付信息创建对象
     * @param operator 操作人
     * @return OrderPayinfoDTO
     */
    OrderPayinfoDTO createRefundPayInfo(OrderPayinfoCreateDTO payinfoCreateDTO, String operator);

    /**
     * 确认付款服务
     * @param paymentBillNo 付款单号
     * @param success 是否成功
     * @param operator 操作人
     */
    void confirmPaymentService(String paymentBillNo, boolean success, String operator);

    /**
     * 根据订单实际金额生成退款补款信息
     * @param orderId 订单ID
     * @param operator 操作人
     */
    void checkRealTimeOrderAmount(String orderId, String operator);


    void addUpMemberIntegral(OrderInfo order);

    /**
     * 交易支付回调
     * @param callbackDTO 回调对象
     */
    void payTradeCallback(PayCallbackDTO callbackDTO);

    /**
     * 计算订单支付金额
     * @param paymentAmountQueryDO 支付金额查询对象
     * @return PaymentAmountDO
     */
    PaymentAmountDO computeOrderPaymentAmount(PaymentAmountQueryDO paymentAmountQueryDO);

    /**
     * 计算待支付的补款金额
     * @param orderId
     * @return
     */
    BigDecimal calcOrderSupplementWaitPaymentAmount(String orderId);

    /**
     * Erp自动完成补款操作
     * @param orderId 订单ID
     * @param operator 操作人
     */
    void erpSupplementComplete(String orderId, String operator);


    /**
     * 生成订单支付信息
     * @param order 订单对象
     * @return 订单支付对象
     */
    OrderPayinfoDTO generateOrderPayInfo(OrderDTO order);

    /**
     * 生成订单退款信息
     * @param orderId 订单ID
     * @return OrderRefundDTO
     */
    OrderRefundDTO generateOrderRefundInfo(String orderId);

    /**
     * 生成订单退款信息
     * @param orderInfo
     * @param refundGoodsAmount
     * @param refundLogisticsAmount
     * @param refundOtherAmount
     * @return
     */
    OrderRefundDTO generateOrderRefundInfo(OrderInfo orderInfo, BigDecimal refundGoodsAmount, BigDecimal refundLogisticsAmount, BigDecimal refundOtherAmount);

    /**
     * 订单关闭退款
     * @param orderRefundDTO 订单退款对象
     * @return OrderRefundDTO
     */
    OrderRefundDTO closeOrderAndDoRefund(OrderRefundDTO orderRefundDTO);

    /**
     * 订单关闭退款包装方法
     * @param orderRefundDTO 订单退款对象
     * @return OrderRefundDTO
     */
    OrderRefundDTO closeOrderAndDoRefundWrap(OrderRefundDTO orderRefundDTO);

    /**
     * 完成自动支付(支持订单的支付单、发货单对应的支付单的自动支付)
     * @param orderPayinfoDTO 支付单对象
     */
    void completeAutoPay(OrderPayinfoDTO orderPayinfoDTO);

    /**
     * 查询支持自动支付的方式
     * @param autoPayWayQueryDTO 支付方式查询对象
     */
    List<String> querySupportAutoPayWayList(AutoPayWayQueryDTO autoPayWayQueryDTO);

    /**
     * 获取对应发货单的支付渠道
     * @param takeCode
     * @return
     */
    String getPayInfoWayByTakeCode(String takeCode);

    /**
     * 设置提货有效期
     * @param order 订单对象
     */
    void setOrderTakeTimeLimit(OrderInfo order);

    /**
     * 调价退款或补款
     * @param orderInfo 订单对象
     */
    void adjustRefundOrSupplement(OrderInfo orderInfo);
}
