package com.ecommerce.order.fsm.service;

import com.ecommerce.order.api.dto.*;
import com.ecommerce.order.api.dto.logistics.PickingBillExportInfoDTO;
import com.ecommerce.order.api.dto.pay.TradingFlowExportInfoDTO;
import com.ecommerce.order.api.enums.PayDetailTypeEnum;
import com.ecommerce.order.api.enums.PayTypeEnum;
import com.ecommerce.order.dao.vo.OrderPayinfo;
import com.ecommerce.price.api.dto.BuyerPromotionalDetailsDTO;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 *
 * <AUTHOR>
 * Date: Create in 下午3:53 19/9/18
 */
public interface IOrderQueryService {
    /**
     * 订单列表查询
     * @param orderQuery 订单查询对象
     * @param pageSize 分页大小
     * @param pageNum 当前页数
     * @return Page<OrderDTO>
     */
    Page<OrderDTO> searchOrderPage(OrderQueryDTO orderQuery, Integer pageSize, Integer pageNum);

    /**
     * 查询未开票订单列表
     * @param uninvoicedOrderReqDTO 未开票订单查询对象
     * @return PageInfo<UninvoicedOrderDTO>
     */
    PageInfo<UninvoicedOrderDTO> pageUninvoicedOrderList(UninvoicedOrderReqDTO uninvoicedOrderReqDTO);

    /**
     * 批量查询订单列表
     * @param orderIds 订单ID列表
     * @return List<OrderDTO>
     */
    List<OrderDTO> queryOrderListById(List<String> orderIds);

    /**
     * 统计订单数量
     * @param orderCountDTO 订单统计对象
     * @return int
     */
    int countOrders(OrderCountDTO orderCountDTO);

    /**
     * 获取订单详情
     * @param orderId 订单id
     * @return OrderDTO
     */
    OrderDTO getOrderDetail(String orderId);

    OrderDTO getOrderTableInfo(String orderId);

    OrderDTO getOrderTableInfoByTakeCode(String takeCode);
    List<OrderIdAndContractIdDTO> getContractIdByOrderIds(Collection<String> orderIds);
    /**
     * 根据订单编码查询订单
     * @param orderCode 订单编码
     * @return OrderDTO
     */
    OrderDTO queryOrderByCode(String orderCode);

    /**
     * 获取订单行项目信息
     * @param orderId 订单ID
     * @return List<OrderItemDTO>
     */
    List<OrderItemDTO> getOrderItems(String orderId);

    /**
     * 获取订单支付信息
     * @param orderId 订单ID
     * @param payType 支付类型
     * @param payDetailType 支付详情类型
     * @return List<OrderPayinfoDTO>
     */
    List<OrderPayinfoDTO> getOrderPayInfoList(String orderId, PayTypeEnum payType, PayDetailTypeEnum payDetailType);

    /**
     * 获取订单物流费用信息，并保存到OrderDTO
     * @param order 订单对象
     * @return List<OrderPayinfoDetailDTO>
     */
    List<OrderPayinfoDetailDTO> getOrderLogisticPayInfo(OrderDTO order, Boolean reversePush);

    /**
     * 取资源最优优惠信息
     * @return BuyerPromotionalDetailsDTO
     */
    BuyerPromotionalDetailsDTO getResourcePromotional(OrderDTO orderDTO, OrderItemDTO item);

    /**
     * 获取订单信息
     * @param orderId 订单ID
     * @return OrderDTO
     */
    OrderDTO getOrderInfo(String orderId);

    /**
     * 获取订单信息
     * @param orderId 订单ID
     * @return OrderDTO
     */
    OrderDTO getOrderInfo(String orderId, boolean showDetail);

    /**
     * 获取订单支付信息
     * @param orderId 订单ID
     * @return OrderPayinfo
     */
    OrderPayinfo getOrderPay(String orderId);

    /**
     * 获取订单支付信息
     * @param  payInfoQueryDTO 支付信息查询对象
     * @return List<OrderPayinfo> 订单支付信息列表
     */
    List<OrderPayinfo> queryOrderPayInfo(OrderPayinfoQueryDTO payInfoQueryDTO);

    /**
     * 根据交易ID查询订单
     * @param tradeBillId 交易ID
     * @return OrderDTO
     */
    OrderDTO getOrderByTradeBillId(String tradeBillId);

    /**
     * 根据订单ID和卖家ID获取卖家交易信息
     * @param orderId 订单ID
     * @param buyerId 买家ID
     * @return String
     */
    String getBuyerTradeBillIdByOrderId(String orderId, String buyerId);

    /**
     * 买家当日已购商品数量
     * @param buyerId 买家ID
     * @param resourceId 资源ID
     * @return BigDecimal
     */
    BigDecimal getBuyerResDayQuantity(String buyerId, String resourceId);

    /**
     * 交易概览
     * @param overviewReqDTO 概览请求对象
     * @return TransactionsOverviewDTO
     */
    TransactionsOverviewDTO transactionsOverview(OverviewReqDTO overviewReqDTO);

    /**
     * 订单概览
     * @param overviewReqDTO 概览请求对象
     * @return OrderOverviewDTO
     */
    OrderOverviewDTO orderOverview(OverviewReqDTO overviewReqDTO);

    /**
     * 条件查询订单详情
     * @param orderDetailConditionDTO 查询条件对象
     * @return OrderDTO
     */
    OrderDTO getOrderDetailByCondition(OrderDetailConditionDTO orderDetailConditionDTO);

    /**
     * 订单状态统计查询
     * @param saleRegionList 销售区域列表
     * @param sellerId 卖家ID
     * @return List<OrderStatusStatisticsDTO>
     */
    List<OrderStatusStatisticsDTO> orderStatusStatistics(List<String> saleRegionList, String sellerId);

    /**
     * 根据发货ID获取订单对象
     * @param takeIds 发货单ID
     * @return List<OrderDTO>
     */
    List<OrderDTO> getOrderByTakeId(List<String> takeIds);

    /**
     * 根据发货编码获取订单对象
     * @param takeCodes 发货单编码
     * @return List<OrderDTO>
     */
    List<OrderDTO> getOrderByTakeCodes(List<String> takeCodes);

    /**
     * 统计订单状态
     * @param orderQuery 订单查询对象
     * @return Page<OrderDTO>
     */
    List<OrderStatusStatisticDTO> statisticOrderStatus(OrderQueryDTO orderQuery);

    /**
     * 查询erp电商收货地址映射
     * @param erpUnloadAddressIdList erp地址ID列表
     * @return List<ERPAddressDTO>
     */
    List<ERPAddressDTO> queryReceivingAddressMap(List<String> erpUnloadAddressIdList);

    /**
     * 查询导出提货单需要的信息
     * @param ids id集合
     * @return List<PickingBillExportInfoDTO>
     */
    List<PickingBillExportInfoDTO> queryPickingBillExportInfo(Set<String> ids);

    /**
     * 查询导出交易流水需要的信息
     * @param ids id集合
     * @return List<TradingFlowExportInfoDTO>
     */
    List<TradingFlowExportInfoDTO> queryTradingFlowExportInfo(Set<String> ids);

    /**
     * 根据合同批量调价信息查询（根据合同id和合同商品id查询对应的订单信息）
     * 查询待发货或者发货中的合同订单
     */
    List<OrderSimpleDTO> queryOrderByContractBatchAdjustPriceInfo(OrderSimpleQueryDTO dto);

    /**
     * 查询订单物流提货信息
     * @param orderLogisticsQueryDTO 订单物流信息查询对象
     * @return OrderLogisticsResultDTO
     */
    OrderLogisticsResultDTO queryOrderLogisticsTakeInfo(OrderLogisticsQueryDTO orderLogisticsQueryDTO);

    /**
     * 对账单-订单信息查询
     */
    OrderBillCheckDTO queryBillCheckInfo(OrderBillCheckQueryDTO query);

    /**
     * 再次下单
     * @param orderId 订单ID
     * @return 再次下单对象
     */
    RepurchaseOrderDTO repurchaseOrder(String orderId);
    
    /**
     * 模糊查询订单地址详情
     * @param memberId
     * @param addressDetaile
     * @return
     */
	List<String> queryAddressDetailByMemberId(OrderQueryDTO queryDTO);
}
