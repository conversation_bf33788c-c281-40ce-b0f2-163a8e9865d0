package com.ecommerce.order.fsm.service;

import com.ecommerce.order.api.dto.WaybillSplitDTO;

/**
 *
 * <AUTHOR>
 * Date: Create in 上午11:18 20/7/7
 */
public interface IOrderSplitBillService {
    /**
     * 按出厂运单分账
     * @param waybillSplitDTO 运单分账对象
     */
    void triggerSplitByWaybill(WaybillSplitDTO waybillSplitDTO);

    /**
     * 触发订单分账
     * @param orderId 订单ID
     */
    void triggerSplitByOrder(String orderId);
}
