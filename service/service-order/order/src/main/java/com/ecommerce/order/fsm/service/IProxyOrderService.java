package com.ecommerce.order.fsm.service;

import com.ecommerce.common.result.PageData;
import com.ecommerce.order.api.dto.OrderDTO;
import com.ecommerce.order.api.dto.OrderPayinfoDTO;
import com.ecommerce.order.api.dto.base.PageQuery;
import com.ecommerce.order.api.dto.proxy.*;

import java.util.List;


/**
 *  代理订单服务
 *
 * <AUTHOR>
 */
public interface IProxyOrderService {
    /**
     * 分页查询二级订单列表
     * @return PageData<SecondaryOrderListDTO>
     */
    PageData<SecondaryOrderListDTO> pageQuerySecondaryOrderList(PageQuery<SecondaryOrderQueryDTO> pageQuery);

    /**
     * 新分页查询二级订单列表
     * @return PageData<SecondaryOrderListDTO>
     */
    PageData<SecondaryOrderListDTO> newPageQuerySecondaryOrderList(PageQuery<SecondaryOrderQueryDTO> pageQuery);

    OrderMapResultDTO submitProxyPrimaryOrder(PrimaryOrderCreateDTO primaryOrderCreateDTO);

    /**
     * 取消二级发货单
     * @param primaryOrderCreateDTO
     * @return
     */
    void cancelSecondaryTakeInfo(PrimaryOrderCreateDTO primaryOrderCreateDTO);

    /**
     * 批量提交一级订单处理
     * @param submitPrimaryOrderDTO 提交一级订单对象
     */
    void batchSubmitProxyPrimaryOrder(SubmitPrimaryOrderDTO submitPrimaryOrderDTO);

    /**
     * 分页查询一级订单列表
     * @return PageData<PrimaryOrderListDTO>
     */
    PageData<PrimaryOrderListDTO> pageQueryPrimaryOrderList(PageQuery<PrimaryOrderQueryDTO> pageQuery);

    /**
     * 查询订单映射结果列表
     * @param orderMapResultQueryDTO 查询对象
     * @return List<OrderMapResultDTO>
     */
    List<OrderMapResultDTO> queryOrderMapResultList(OrderMapResultQueryDTO orderMapResultQueryDTO);

    /**
     * 分页查询订单提交列表
     * @param pageQuery 订单提交查询对象
     * @return List<OrderSubmitResultDTO>
     */
    PageData<OrderSubmitResultDTO> pageQueryOrderSubmitList(PageQuery<OrderSubmitQueryDTO> pageQuery);

    /**
     * 合并支付一级订单
     * @param batchPayPrimaryOrderDTO 合并支付对象
     * @return List<String>
     */
    List<String> batchPayPrimaryOrder(BatchPayPrimaryOrderDTO batchPayPrimaryOrderDTO);

    /**
     * 重新提交一级订单
     * @param retrySubmitPrimaryOrderDTO 重试对象
     */
    void retrySubmitPrimaryOrder(RetrySubmitPrimaryOrderDTO retrySubmitPrimaryOrderDTO);

    /**
     * 查询买家选择器列表
     * @return PageData<SecondaryOrderListDTO>
     */
    List<BuyerSelectorDTO> querySecondaryBuyerSelectorList(SecondaryOrderQueryDTO pageQuery);

    /**
     * 查询仓库选择器列表
     * @return PageData<SecondaryOrderListDTO>
     */
    List<StoreSelectorDTO> querySecondaryStoreSelectorList(SecondaryOrderQueryDTO pageQuery);

    /**
     * 查询商品选择器列表
     * @return PageData<SecondaryOrderListDTO>
     */
    List<GoodsSelectorDTO> querySecondaryGoodsSelectorList(SecondaryOrderQueryDTO pageQuery);

    /**
     * 分页查询订单映射结果列表
     * @param pageQuery 查询对象
     * @return PageData<OrderMapResultDTO>
     */
    PageData<OrderMapResultDTO> pageQueryOrderMapResultList(PageQuery<OrderMapResultQueryDTO> pageQuery);

    OrderDTO submitProxyPrimaryOrder2(SubmitPrimaryOrderDTO submitPrimaryOrderDTO, String batchNumber);

    public void primaryOrderAutoPay(OrderPayinfoDTO orderPayInfoDTO, String operator);
}
