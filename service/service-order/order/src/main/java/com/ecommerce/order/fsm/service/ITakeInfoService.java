package com.ecommerce.order.fsm.service;

import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.order.api.dto.*;
import com.ecommerce.order.api.dto.base.PageQuery;
import com.ecommerce.order.api.dto.erp.ShipBillERPInfoDTO;
import com.ecommerce.order.api.dto.proxy.map.error.ProxyMapErrorDTO;
import com.ecommerce.order.api.dto.proxy.map.error.ProxyMapErrorSearchDTO;
import com.ecommerce.order.api.dto.take.NeedBuyerPayDTO;
import com.ecommerce.order.api.dto.take.TakeInfoCreateDTO;
import com.ecommerce.order.dao.dto.TakeAutoCreateDTO;
import com.github.pagehelper.PageInfo;
import org.springframework.web.bind.annotation.RequestBody;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface ITakeInfoService {

    /**
     * 创建前获取发货单信息
     * @param orderId 订单Id
     * @param operator 操作人
     * @return TakeInfoDTO
     */
    TakeInfoDTO getTakeInfoForCreate(String orderId, String operator);

    /**
     * @deprecated
     */
    @Deprecated(since = "2.1.4-RELEASE")
    List<TakeInfoDTO> doOrderCreateTakeInfo(OrderDTO orderDto, String operator);

    /**
     * 买家创建发货单
     * @param takeInfoDTOs 发货单列表
     * @param memberId 会员ID
     * @param operator 操作人
     * @return 发货单
     */
    List<TakeInfoDTO> doBuyerCreateTakeInfo(List<TakeInfoDTO> takeInfoDTOs, String memberId, String operator);
    /**
     * 买家创建发货单
     */
    List<TakeInfoDTO> doBuyerCreateTakeInfo(List<TakeInfoCreateDTO> list);
    /**
     * 经销商创建一级发货单
     * @param secondaryTakeInfoIds 二级发货单id
     * @param memberId
     * @param operator
     * @return
     */
    List<TakeInfoDTO> doSellerCreatePrimaryTakeInfo(List<String> secondaryTakeInfoIds, String memberId, String operator);
    /**
     * 卖家创建发货单
     * @param takeInfoDTOs 发货单列表
     * @param memberId 会员ID
     * @param operator 操作人
     * @return 发货单
     */
    List<TakeInfoDTO> doSellerCreateTakeInfo(List<TakeInfoDTO> takeInfoDTOs, String memberId, String operator);

    /**
     * 卖家确认发货单
     * @param takeInfoDTOs 发货单列表
     * @param memberId 会员ID
     * @param operator 操作人
     * @return 结果
     */
    List<TakeInfoDTO> doSellerConfirmTakeInfo(List<TakeInfoDTO> takeInfoDTOs, String memberId, String operator);
    /**
     * 卖家确认发货单
     * @param takeId 发货单id
     * @param memberId 会员id
     * @param operator 操作人
     * @return 结果
     */
    String doSellerConfirmTakeInfoNoChange(String takeId, String memberId, String operator);
    /**
     * 卖家批量确认发货单
     * @param takeIds 发货单ids
     * @param memberId 会员id
     * @param operator 操作人
     * @return 结果
     */
    List<String> doBatchSellerConfirmTakeInfoNoChange(List<String> takeIds, String memberId, String operator);

    /**
     * 物流通知发货单已完成
     * @param takeInfoId 发货单Id
     * @param logisticTakeItems 物流发货单
     * @param operator 操作人
     */
    void doFinishTakeInfoByLogistic(String takeInfoId, List<TakeItemDTO> logisticTakeItems, String operator);

    /**
     * 物流通知发货单已完成
     * @param takeInfoId 发货单Id
     * @param logisticTakeItems 物流发货单
     * @param operator 操作人
     */
    void doCloseTakeInfoByLogistic(String takeInfoId, List<TakeItemDTO> logisticTakeItems, String operator);

    /**
     * 关闭订单下所有的发货单
     * @param orderId
     * @param operator
     */
    void finishTakeInfoByERP(String orderId, String operator);
    //确认发货单Action
    //已确认发货单Action
    //待发货发货单Action
    //发货中发货单Action
    //关闭发货单Action
    //完成发货单Action
    //取消发货单Action
    /**
     * 买家取消发货单
     * @param takeInfoId 发货单ID
     * @param memberId 会员ID
     * @param operator 操作人
     * @return 结果
     */
    String doBuyerCancelTakeInfo(String takeInfoId, String memberId, String operator);

    /**
     * 卖家取消发货单
     * @param takeInfoId 发货单ID
     * @param memberId 会员ID
     * @param operator 操作人
     * @return 结果
     */
    String doSellerCancelTakeInfo(String takeInfoId, String memberId, String operator,boolean sendMessage);

    /**
     * 卖家完成发货单
     * @param takeInfoId 发货单ID
     * @param takeItems 发货单子项
     * @param memberId 会员ID
     * @param operator 操作人
     * @return 结果
     */
    String doSellerFinishTakeInfo(String takeInfoId, List<TakeItemDTO>takeItems, String memberId,
                                  String operator);

    /**
     * 买家完成发货单（没用到）
     * @param takeInfoId 发货单ID
     * @param takeItems 发货单子项
     * @param memberId 会员ID
     * @param operator 操作人
     * @return 结果
     */
    @Deprecated(since = "2.1.4-RELEASE")
    String doBuyerFinishTakeInfo(String takeInfoId, List<TakeItemDTO>takeItems, String memberId, String operator);

    /**
     * 卖家关闭发货单
     * @param orderId 订单Id
     * @param operator 操作人
     */
    void doSellerCloseOrder(String orderId, String operator);

    /**
     * 买家关闭发货单
     * @param orderId
     * @param operator
     */
    void doBuyerCloseOrder(String orderId, String operator);

    /**
     * 卖家关闭发货单
     * @param takeInfoId 发货单Id
     * @param operator 操作人
     */
    void doSellerCloseTakeInfo(String takeInfoId, String operator);

    /**
     * 卖家发送发货单到物流
     * @param takeInfoId 发货单ID
     * @param memberId 会员ID
     * @param operator 操作人
     * @return 结果
     */
    TakeInfoDTO doSellerSendTakeInfoToLogistic(String takeInfoId, String memberId, String operator);

    /**
     * 更新发货数量
     * @param takeInfoId 发货单ID
     * @param operator 操作人
     * @param reourceQuantityDetails 更新发货商品数量详情
     */
    @Deprecated(since = "2.1.4-RELEASE")
    void updateSendQuantity(String takeInfoId, String operator, Map<String, String> reourceQuantityDetails);

    /**
     * 更新物流数量和金额
     * @param takeInfoId 发货单ID
     * @param operator 操作人
     * @param reourceQuantityDetails 更新发货商品数量详情
     * @param takeCost 更新发货商品金额详情
     */
    @Deprecated(since = "2.1.4-RELEASE")
    void updateSendQuantity(String takeInfoId,
                            String operator,
                            Map<String, String> reourceQuantityDetails ,
                            Map<String, String> takeCost,
                            Map<String, BigDecimal> actualQuantityDetails);
    /**
     * 更新发货单签收数量
     * @param takeInfoId 发货单ID
     * @param operator 操作人
     * @param itemQuantityDetails 更新发货单签收数量详情
     */
    void updateSellerSignQuantity(String takeInfoId, String adjustAddWay, String operator, Map<String, String> itemQuantityDetails);

    /**
     * 定时任务检查是否到达提货有效期
     */
    void checkTakeTimeLimited();

    //----------------以下全是查询---------------------------------------------------------------------

    /**
     * 获取订单发货单详情
     * @param orderId 订单ID
     * @return 发货单
     */
    List<TakeInfoDTO> getTakeInfoDetailForOrder(String orderId);

    /**
     * 分页查询买家发货单
     * @param takeInfoQuery 查询发货单入参
     * @return PageInfo<TakeInfoDTO>
     */
    PageInfo<TakeInfoDTO> pageBuyerTakeInfo(TakeInfoSearchDTO takeInfoQuery);

    /**
     * 二背靠背异常列表
     * @param query
     * @return
     */
    PageInfo<ProxyMapErrorDTO> pageProxyMapError(PageQuery<ProxyMapErrorSearchDTO> query);
    /**
     * 分页查询卖家发货单
     * @param takeInfoQuery 查询发货单入参
     * @return PageInfo<TakeInfoDTO>
     */
    PageInfo<TakeInfoDTO> pageSellerTakeInfo(TakeInfoSearchDTO takeInfoQuery);

    /**
     * 分页查询平台发货单
     * @param takeInfoQuery 查询发货单入参
     * @return PageInfo<TakeInfoDTO>
     */
    PageInfo<TakeInfoDTO> pagePlatformTakeInfo(TakeInfoSearchDTO takeInfoQuery);

    /**
     * 获取卖家发货单详情
     * @param takeInfoId 发货单ID
     * @param memberId 会员ID
     * @param operator 操作人
     * @return 发货单
     */
    TakeInfoDTO getSellerTakeInfoDetail(String takeInfoId, String memberId, String operator);

    /**
     * 获取买家发货单详情
     * @param takeInfoId 发货单ID
     * @param memberId 会员ID
     * @param operator 操作人
     * @return 发货单
     */
    TakeInfoDTO getBuyerTakeInfoDetail(String takeInfoId, String memberId, String operator);

    /**
     * 获取平台发货单详情
     * @param takeInfoId 发货单ID
     * @param operator 操作人
     * @return 发货单
     */
    TakeInfoDTO getPlatformTakeInfoDetail(String takeInfoId, String operator);

    /**
     * 获取发货单BY CODE
     * @param code 发货单CODE
     * @return 发货单
     */
    TakeInfoDTO getTakeInfoByCode(String code);
    TakeInfoDTO getTakeInfoById(String id);

    /**
     * 获取卖家订单详情中包含的发货信息
     * @param orderId 订单Id
     * @param operator 操作人
     * @return List<TakeInfoDTO>
     */
    List<TakeInfoDTO> getSellerOrderTakeInfo(String orderId, String operator);

    /**
     * 获取买家订单详情中包含的发货信息
     * @param orderId 订单Id
     * @param operator 操作人
     * @return List<TakeInfoDTO>
     */
    List<TakeInfoDTO> getBuyerOrderTakeInfo(String orderId, String operator);

    /**
     * 获取平台查看订单详情时的发货信息
     * @param orderId 订单Id
     * @param operator 操作人
     * @return List<TakeInfoDTO>
     */
    List<TakeInfoDTO> getPlantformOrderTakeInfo(String orderId, String operator);

    /**
     * 发货概览
     * @param overviewReqDTO 入参
     * @return TakeInfoOverviewDTO
     */
    TakeInfoOverviewDTO takeInfoOverview(OverviewReqDTO overviewReqDTO);

    /**
     * 获取订单行发货明细列表
     * @param orderItemId 订单行明细ID
     * @return List<TakeItemDTO>
     */
    List<TakeItemDTO> selectItemByOrderItemId(String orderItemId);

    /**
     * 统计发货单状态
     * @param takeInfoSearchDTO 发货单查询对象
     * @return List<TakeStatusStatisticDTO>
     */
    List<TakeStatusStatisticDTO> statisticTakeStatus(TakeInfoSearchDTO takeInfoSearchDTO);

    /**
     * @deprecated 原来就没有实现,应该用不到
     * 准备发货单
     * @param orderId 订单ID
     * @param operator 操作人
     * @return 发货单
     */
    @Deprecated(since = "2.1.4-RELEASE")
    default TakeInfoDTO prepareTakeInfo(String orderId, String operator){
        throw new BizException(BasicCode.CUSTOM_ERROR,"方法已废弃");
    }

    /**
     * @deprecated 发货单创建后不可删除，只能关闭，当前页面没有删除按钮
     * 家删除发货单
     * @param takeId 发货单Id
     * @param operator 操作人
     */
    void doSellerDeleteTakeInfo(String takeId, String operator);

    /**
     * @deprecated 发货单创建后不可删除，只能关闭，当前页面没有删除按钮
     * 买家删除发货单
     * @param takeId 发货单Id
     * @param operator 操作人
     */
    void doBuyerDeleteTakeInfo(String takeId, String operator);

    /**
     * 支付前校验是否需要支付以及支付的金额
     */
    NeedBuyerPayDTO needBuyerPay(@RequestBody NeedBuyerPayDTO needBuyerPayDTO);
    /**
     * 导出发货单
     * @param takeInfoSearchDTO 发货单查询对象
     * @return List<TakeInfoExportDTO>
     */
    List<TakeInfoExportDTO> exportTakeInfo(TakeInfoSearchDTO takeInfoSearchDTO);

    /**
     * 通过合同Id获取发货单号
     */
    List<String> queryTakeCodeByContractIdList(List<String> contractIdList);

    ShipBillERPInfoDTO findShipBillERPInfoByTakeCode(String takeCode, String contractId);

    /**
     * 自动创建发货单
     * @param takeAutoCreateDTO 自动创建对象
     */
    void autoCreateTakeInfo(TakeAutoCreateDTO takeAutoCreateDTO);
}
