package com.ecommerce.order.fsm.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.order.api.dto.*;
import com.ecommerce.order.api.dto.logistics.OverSendOutVerifyDTO;
import com.ecommerce.order.api.dto.logistics.OverSendOutVerifyResponseDTO;
import com.ecommerce.order.api.dto.logistics.ShipBillRefundDTO;
import com.ecommerce.order.api.dto.take.DeliveringTakInfoDTO;
import com.ecommerce.order.dao.vo.OrderPayinfo;

/**
 *
 * <AUTHOR>
 * Date: Create in 下午5:39 20/7/6
 */
public interface ITakeUpDataService {
    /**
     * 物流关闭发货
     * @param closeTakeInfoDTO 对象
     * @return 结果
     */
     ItemResult<Boolean> doLogisticCloseTakeInfo(CloseTakeInfoDTO closeTakeInfoDTO);

    /**
     * 物流完成发货单
     * @param finishTakeInfoDTO 对象
     * @return 结果
     */
     ItemResult<Boolean> doLogisticFinishTakeInfo(FinishTakeInfoDTO finishTakeInfoDTO);

    /**
     * 更新发货单状态为发货中
     */
     ItemResult<Boolean> doLogisticDeliveringTakInfo(DeliveringTakInfoDTO deliveringTakInfoDTO);

    /**
     * 物流更新发货数量
     * @param updateTakeInfoShippedDTO 对象
     * @return 结果
     */
     ItemResult<Boolean> doLogisticUpdateShippedQuantity(UpdateTakeInfoShippedDTO updateTakeInfoShippedDTO);

    /**
     * 物流更新签收数量
     * @param updateTakeInfoSignDTO 对象
     * @return 结果
     */
     ItemResult<Boolean> doLogisticUpdateSignQuantity(UpdateTakeInfoSignDTO updateTakeInfoSignDTO);

    /**
     * 物流超发验证
     * @param overSendOutVerifyDTO 超发验证对象
     * @return 是否可出站
     */
     ItemResult<OverSendOutVerifyResponseDTO> overSendOutVerify(OverSendOutVerifyDTO overSendOutVerifyDTO);

    /**
     * 补款成功通知物流出站
     * @param orderPayInfo 支付单对象
     */
     ItemResult<Void> notifyLeaveWarehouse(OrderPayinfo orderPayInfo);

    /**
     * 超发验证计算接口
     * @param order 订单对象
     * @param overSendOutVerifyDTO 超发验证对象
     * @return OverSendOutVerifyResponseDTO
     */
     OverSendOutVerifyResponseDTO checkoutOverSendOut(OrderDTO order, OverSendOutVerifyDTO overSendOutVerifyDTO);

    /**
     * 运单退货
     * @param shipBillRefundDTO
     */
     void doLogisticRefund(ShipBillRefundDTO shipBillRefundDTO);
}
