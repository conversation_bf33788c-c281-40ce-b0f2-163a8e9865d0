package com.ecommerce.order.fsm.service;

import com.ecommerce.common.exception.BasicRuntimeException;
import com.google.common.collect.Maps;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 *
 * <AUTHOR>
 * Date: Create in 上午11:29 20/6/8
 */
@Component
public class OrderServiceFactory implements ApplicationContextAware {
    /**
     * 用来存储订单处理服务
     */
    public static final Map<String, AbstractOrderService> orderHandlerServiceMap = Maps.newConcurrentMap();

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        String[] beanNamesForType = applicationContext.getBeanNamesForType(IOrderHandlerService.class);
        for (String beanName : beanNamesForType) {
            AbstractOrderService bean = (AbstractOrderService) applicationContext.getBean(beanName);
            orderHandlerServiceMap.put(bean.getServiceName(), bean);
        }
    }

    /**
     * 获取订单处理服务
     *
     * @param serviceName 订单服务名称
     */
    public static AbstractOrderService getOrderHandlerService(String serviceName) {
        if (CsStringUtils.isBlank(CsStringUtils.trim(serviceName))) {
            throw new BasicRuntimeException("无效的订单服务名称");
        }
        AbstractOrderService abstractOrderService = orderHandlerServiceMap.get(serviceName);
        if (abstractOrderService == null) {
            throw new BasicRuntimeException("找不到对应订单服务serviceName:" + serviceName);
        }
        return abstractOrderService;
    }
}
