package com.ecommerce.order.fsm.service.impl;

import com.ecommerce.order.api.dto.OrderDTO;
import com.ecommerce.order.api.dto.OrderItemDTO;
import com.ecommerce.order.fsm.service.AbstractOrderService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *  用于调用AbstractOrderService方法
 *
 * <AUTHOR>
 */
@Service
public class BaseOrderHandlerService extends AbstractOrderService {

    public static final String SERVICE_NAME = "baseOrderHandlerService";

    @Override
    public void convertOrderItem(OrderDTO orderDTO) {
        // TODO Auto-generated method stub
    }

    @Override
    public void updateResourceNum(OrderDTO orderDTO, List<OrderItemDTO> orderItems, String operator) {
        // TODO Auto-generated method stub
    }

    @Override
    public String getServiceName() {
        return SERVICE_NAME;
    }
}
