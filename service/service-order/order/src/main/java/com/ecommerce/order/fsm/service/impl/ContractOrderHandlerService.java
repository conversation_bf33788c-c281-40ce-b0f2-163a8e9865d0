package com.ecommerce.order.fsm.service.impl;

import com.alibaba.fastjson.JSON;
import com.ecommerce.base.api.dto.ReceivingAddressMapQueryDTO;
import com.ecommerce.base.api.dto.saleregion.RegionQueryDTO;
import com.ecommerce.base.api.dto.saleregion.SaleRegionSampleDTO;
import com.ecommerce.base.api.dto.warehouse.WarehouseDetailsDTO;
import com.ecommerce.base.api.service.IReceivingAddressService;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.utils.ArithUtils;
import com.ecommerce.goods.api.dto.GoodsAddItemDTO;
import com.ecommerce.goods.api.dto.GoodsDTO;
import com.ecommerce.goods.api.dto.SpecialGoodsAttributeDTO;
import com.ecommerce.goods.api.dto.contract.OrderUpdateContractDTO;
import com.ecommerce.goods.api.dto.contract.TrContractDTO;
import com.ecommerce.goods.api.dto.contract.TrContractGoodsDTO;
import com.ecommerce.goods.api.dto.contract.TrContractGoodsVarietyDTO;
import com.ecommerce.goods.api.dto.contract.TrContractNumDTO;
import com.ecommerce.goods.api.dto.contract.concrete.ConcreteGoodsAddItemDTO;
import com.ecommerce.goods.api.dto.contract.concrete.ContractGoodsAddItemQueryDTO;
import com.ecommerce.goods.api.dto.contract.concrete.ContractGoodsVarietyQueryDTO;
import com.ecommerce.goods.api.dto.stock.StockChangeDTO;
import com.ecommerce.goods.api.dto.stock.StockItemDTO;
import com.ecommerce.goods.api.dto.stock.StockItemQueryDTO;
import com.ecommerce.goods.api.enums.PaydateTypeEnum;
import com.ecommerce.goods.api.enums.contract.ContractGoodsNumEnum;
import com.ecommerce.goods.api.enums.contract.ContractStatusEnum;
import com.ecommerce.goods.api.service.IContractService;
import com.ecommerce.logistics.api.dto.carriage.CarriageRouteQueryDTO;
import com.ecommerce.logistics.api.enums.PickingBillTypeEnum;
import com.ecommerce.logistics.api.enums.TransportToolTypeEnum;
import com.ecommerce.member.api.dto.member.config.MemberConfigEnum;
import com.ecommerce.member.api.dto.relation.MemberRelationDTO;
import com.ecommerce.member.api.service.IAccountService;
import com.ecommerce.order.api.dto.OrderDTO;
import com.ecommerce.order.api.dto.OrderInfoExtDTO;
import com.ecommerce.order.api.dto.OrderItemAddDTO;
import com.ecommerce.order.api.dto.OrderItemDTO;
import com.ecommerce.order.api.dto.UpdateTakeInfoShippedDTO;
import com.ecommerce.order.api.dto.UpdateTakeItemShippedDTO;
import com.ecommerce.order.api.dto.proxy.StockChangeMapDTO;
import com.ecommerce.order.api.enums.ProxyOrderTypeEnum;
import com.ecommerce.order.dao.dto.OrderCancelDO;
import com.ecommerce.order.dao.dto.OrderCloseDO;
import com.ecommerce.order.dao.dto.OrderCompleteDO;
import com.ecommerce.order.fsm.service.AbstractOrderService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import com.ecommerce.common.utils.CsStringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 *  合同下单
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ContractOrderHandlerService extends AbstractOrderService {

    public static final String SERVICE_NAME = "contractOrderHandlerService";

    private final IContractService contractService;

    private final IAccountService iAccountService;

    private final IReceivingAddressService iReceivingAddressService;

    @Override
    public String getServiceName() {
        return SERVICE_NAME;
    }


    /**
     * 计算单价
     *
     * @param orderDTO         订单对象
     * @param itemDTO          商品行对象
     * @param contractGoodsDTO 商品资源对象
     */
    private void computeConcreteOrderItemPrice(OrderDTO orderDTO, OrderItemDTO itemDTO, TrContractGoodsDTO contractGoodsDTO) {
        OrderInfoExtDTO orderInfoExtDTO = orderDTO.getOrderInfoExtDTO();
        ContractGoodsVarietyQueryDTO contractGoodsVarietyQueryDTO = new ContractGoodsVarietyQueryDTO();
        contractGoodsVarietyQueryDTO.setContractGoodsId(contractGoodsDTO.getContractGoodsId());
        contractGoodsVarietyQueryDTO.setVarietyId(orderInfoExtDTO.getVarietyId());
        TrContractGoodsVarietyDTO varietyDTO = contractService.queryContractGoodsVariety(contractGoodsVarietyQueryDTO);
        if (varietyDTO == null || varietyDTO.getVarietyPrice() == null ||
                varietyDTO.getVarietyPrice().compareTo(BigDecimal.ZERO) < 1) {
            throw new BizException(BasicCode.DATA_NOT_EXIST, "合同商品未配置该品种价格:" + orderInfoExtDTO.getVarietyId());
        }
        BigDecimal addItemPrice = BigDecimal.ZERO;
        BigDecimal resourceUnitPrice = varietyDTO.getVarietyPrice();
        //计算加价项
        if (CollectionUtils.isNotEmpty(itemDTO.getOrderItemAdds())) {
            ContractGoodsAddItemQueryDTO contractGoodsAddItemQueryDTO = new ContractGoodsAddItemQueryDTO();
            contractGoodsAddItemQueryDTO.setContractId(orderDTO.getDealsId());
            contractGoodsAddItemQueryDTO.setContractGoodsId(contractGoodsDTO.getContractGoodsId());
            List<ConcreteGoodsAddItemDTO> addItemList = contractService.queryContractGoodsAddItem(contractGoodsAddItemQueryDTO);
            log.info("computeOrderItemPrice:" + JSON.toJSONString(addItemList));
            if (CollectionUtils.isEmpty(addItemList)) {
                throw new BizException(BasicCode.DATA_NOT_EXIST, "加价项");
            }
            Map<String, ConcreteGoodsAddItemDTO> addItemMap = Maps.newHashMap();
            ConcreteGoodsAddItemDTO pumpingWay = null;
            for (ConcreteGoodsAddItemDTO addItem : addItemList) {
                //设置润管砂浆单价
                if (CsStringUtils.equals(addItem.getAdditemName(), GoodsAddItemDTO.EMBELLISH_PIPE_MORTAR)) {
                    orderInfoExtDTO.setLubricityPrice(addItem.getAdditemPrice());
                    continue;
                }
                //排除施工方式
                if (CsStringUtils.equals(addItem.getAdditemName(), GoodsAddItemDTO.PUMPING_WAY)) {
                    pumpingWay = addItem;
                    continue;
                }
                addItemMap.put(addItem.getAdditemId(), addItem);
            }
            //计算加价项单价
            addItemPrice = getAddItemPrice(itemDTO, pumpingWay, addItemMap, addItemPrice);
        }
        itemDTO.setAdditemPrice(addItemPrice);
        //资源原始单价
        itemDTO.setOriginUnitPrice(resourceUnitPrice);
        //资源成交单价(包含加价项):  resourceUnitPrice + addItemPrice
        itemDTO.setActualUnitPrice(resourceUnitPrice.add(addItemPrice));
    }

    private static BigDecimal getAddItemPrice(OrderItemDTO itemDTO, ConcreteGoodsAddItemDTO pumpingWay, Map<String, ConcreteGoodsAddItemDTO> addItemMap, BigDecimal addItemPrice) {
        for (OrderItemAddDTO addItem : itemDTO.getOrderItemAdds()) {
            //排除施工方式
            if (pumpingWay != null && CsStringUtils.equals(pumpingWay.getAdditemId(), addItem.getParentId())) {
                addItem.setParentName(pumpingWay.getAdditemName());
                continue;
            }
            if (addItemMap.get(addItem.getAdditemId()) != null) {
                addItemPrice = ArithUtils.add(addItemPrice,
                        addItemMap.get(addItem.getAdditemId()).getAdditemPrice());
            }
        }
        return addItemPrice;
    }

    /**
     * 计算订单行单价
     *
     * @param orderDTO         订单对象
     * @param itemDTO          商品行对象
     * @param contractGoodsDTO 商品资源对象
     */
    private void computeOrderItemPrice(OrderDTO orderDTO, OrderItemDTO itemDTO, TrContractGoodsDTO contractGoodsDTO) {
        ItemResult<SpecialGoodsAttributeDTO> goodsResult =
                goodsService.getSpecialGoodsAttribute(contractGoodsDTO.getGoodsId());
        if (!goodsResult.isSuccess()) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "查询商品特殊属性失败");
        }
        BigDecimal addItemPrice = BigDecimal.ZERO;
        if (goodsResult.getData().getConcreteFlag() == 1) {
            computeConcreteOrderItemPrice(orderDTO, itemDTO, contractGoodsDTO);
            return;
        }
        //资源单价
        BigDecimal resourceUnitPrice = contractGoodsDTO.getOutFactoryPrice();
        //卖家配送时进行到位价反推
        if (CsStringUtils.equals(PickingBillTypeEnum.SELLER_DELIVERY.getCode(), orderDTO.getDeliverWay())) {
            //船运卖家配送只支持到位价
            if (CsStringUtils.equals(orderDTO.getOrderInfoExtDTO().getTransportType(),
                    TransportToolTypeEnum.WATER_TRANSPORT.getCode()) &&
                    (contractGoodsDTO.getShipPrice() == null || BigDecimal.ZERO.compareTo(contractGoodsDTO.getShipPrice()) == 0)) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "未设置到位价，不支持船运卖家配送");
            }
            resourceUnitPrice = contractGoodsDTO.getShipPrice() == null || BigDecimal.ZERO.compareTo(contractGoodsDTO.getShipPrice()) == 0 ?
                    contractGoodsDTO.getOutFactoryPrice() : contractGoodsDTO.getShipPrice();
        }
        if (resourceUnitPrice.compareTo(BigDecimal.ZERO) <= 0) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "缺少商品价格");
        }
        itemDTO.setAdditemPrice(addItemPrice);
        log.info("================> originUnitPrice {},resourceUnitPrice {}==========item :{}", itemDTO.getOriginUnitPrice(), resourceUnitPrice, JSON.toJSONString(itemDTO));
        if (Objects.nonNull(itemDTO.getOriginUnitPrice()) && !Objects.equals(itemDTO.getOriginUnitPrice(), resourceUnitPrice)) {
            throw new BizException(BasicCode.CUSTOM_ERROR, "商品价格发生变化");
        }
        //资源原始单价
        itemDTO.setOriginUnitPrice(resourceUnitPrice);
        //资源成交单价(包含加价项):  resourceUnitPrice + addItemPrice
        itemDTO.setActualUnitPrice(resourceUnitPrice.add(addItemPrice));
    }

    /**
     * 转换订单商品行数据对象
     *
     * @param orderDTO 订单对象
     */
    @Override
    public void convertOrderItem(OrderDTO orderDTO) {
        //获取仓库数据
        WarehouseDetailsDTO warehouse = getWarehouseDetailsDTO(orderDTO);
        TrContractGoodsDTO trContractGoodsDTO = new TrContractGoodsDTO();
        //合同业务员
        TrContractDTO contractDTO = getTrContractDTO(orderDTO);
        // 检查合同状态
        checkContractStatus(contractDTO);
        // 检查合同有效期
        Date startDate = contractDTO.getStartDate();
        Date contractEnd = contractDTO.getContractEnd();
        Date date = new Date();
        log.info("合同有期启为：{}-{}，当前时间是：{}",startDate,contractEnd,date);
        if (!(date.after(startDate) && date.before(contractEnd))) {
            throw new BizException(BasicCode.CUSTOM_ERROR, "不在合同有效期");
        }


        orderDTO.setDealsSequence(contractDTO.getContractSequence());
        orderDTO.setDealsName(contractDTO.getContractNumber());
        orderDTO.setProjectName(contractDTO.getProjectName());
        orderDTO.setBillType(contractDTO.getBillType());
        //是否自动确认发货单
        orderDTO.setAutoConfirmTake(Optional.ofNullable(contractDTO.getAutoConfirmTake()).orElse(Boolean.TRUE));


        // 合同订单保存是否是月结订单属性
        OrderInfoExtDTO orderInfoExtDTO = orderDTO.getOrderInfoExtDTO();
        if (orderInfoExtDTO == null) {
            orderInfoExtDTO = new OrderInfoExtDTO();
        }

        orderInfoExtDTO.setIsMonthly(Optional.ofNullable(contractDTO.getIsMonthly()).orElse(0));
        orderInfoExtDTO.setIsEntrust(Optional.ofNullable(contractDTO.getIsEntrust()).orElse(0));

        orderDTO.setOrderInfoExtDTO(orderInfoExtDTO);

        //设置卖家ID
        orderDTO.setSellerId(contractDTO.getSellerId());
        //设置合同mdmCode 多ERP支付账户获取合同上的账户编码(合同下单)
        String mdmCode = contractDTO.getMdmCode();
        if (CsStringUtils.isEmpty(mdmCode)) {
            MemberRelationDTO relationDTO = memberRelationService.getMemberRelationDetailByMemberIdAndCustomerId(contractDTO.getSellerId(), orderDTO.getBuyerId());
            mdmCode = relationDTO != null ? relationDTO.getMdmCode() : "";
        }

        orderDTO.setMdmCode(mdmCode);
        String salesmanId = contractDTO.getSalemanAccountId();
        orderDTO.setSalesmanId(salesmanId);
        Optional.ofNullable(salesmanId)
                .filter(CsStringUtils::isNotBlank)
                .map(iAccountService::findById)
                .ifPresent(salesman -> {
                    orderDTO.setSalesmanName(salesman.getRealName());
                    orderDTO.setSalesmanMobile(salesman.getMobile());
                });
        for (OrderItemDTO itemDTO : orderDTO.getOrderItems()) {
            trContractGoodsDTO = doTrContractGoodsDTOLogic(orderDTO, itemDTO, contractDTO, warehouse, date);
        }
        //添加销售区域
        orderDTO.setSaleRegion1(trContractGoodsDTO.getSaleAreaCode());
        orderDTO.setSaleRegion2(trContractGoodsDTO.getSaleAreaCode2());
        orderDTO.setSaleRegion3(trContractGoodsDTO.getSaleAreaCode3());
        orderDTO.setSaleRegion4(trContractGoodsDTO.getSaleAreaCode4());
        orderDTO.setSaleRegion5(trContractGoodsDTO.getSaleAreaCode5());
        //添加销售区域名称
        orderDTO.setSaleRegionName1(trContractGoodsDTO.getSaleAreaName());
        orderDTO.setSaleRegionName2(trContractGoodsDTO.getSaleAreaName2());
        orderDTO.setSaleRegionName3(trContractGoodsDTO.getSaleAreaName3());
        orderDTO.setSaleRegionName4(trContractGoodsDTO.getSaleAreaName4());
        orderDTO.setSaleRegionName5(trContractGoodsDTO.getSaleAreaName5());

        //设置最小销售区域
        setSaleRegionPath(orderDTO, trContractGoodsDTO);
    }

    private TrContractGoodsDTO doTrContractGoodsDTOLogic(OrderDTO orderDTO, OrderItemDTO itemDTO, TrContractDTO contractDTO, WarehouseDetailsDTO warehouse, Date date) {
        TrContractGoodsDTO trContractGoodsDTO;
        TrContractGoodsDTO contractGoodsDTO = getTrContractGoodsDTO(orderDTO, itemDTO);
        trContractGoodsDTO = contractGoodsDTO;
        //合同商品是否流向管控
        itemDTO.setIfFlowControl(Boolean.FALSE);
        if (CsStringUtils.equals(contractGoodsDTO.getFlowMonitor(), "1")) {
            itemDTO.setIfFlowControl(Boolean.TRUE);
        }
        //需要电商监控
        itemDTO.setNeedECMonitor(Boolean.FALSE);
        if (CsStringUtils.equals(contractGoodsDTO.getNeedECMonitor(), "1")) {
            itemDTO.setNeedECMonitor(Boolean.TRUE);
        }
        itemDTO.setAutoCompleteType(contractGoodsDTO.getAutoCompleteType());
        itemDTO.setAutoCompleteThreshold(contractGoodsDTO.getAutoCompleteThreshold());
        ItemResult<SpecialGoodsAttributeDTO> specialGoodsResult =
                goodsService.getSpecialGoodsAttribute(contractGoodsDTO.getGoodsId());
        if (!specialGoodsResult.isSuccess()) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "查询商品特殊属性失败");
        }
        //取合同上的订单确认标识
        if (specialGoodsResult.getData().getConcreteFlag() == 1) {
            orderDTO.setIfNeedsellerconfirm(contractDTO.getAutoConfirmOrder() == null ?
                    Boolean.TRUE : !contractDTO.getAutoConfirmOrder());
        }
        //获取商品信息
        ItemResult<GoodsDTO> goodsResult = goodsService.getGoodsInfo(contractGoodsDTO.getGoodsId());
        if (!goodsResult.isSuccess() || goodsResult.getData() == null) {
            throw new BizException(BasicCode.DATA_NOT_EXIST, "商品:" + contractGoodsDTO.getGoodsId());
        }
        GoodsDTO goodsDTO = goodsResult.getData();
        if (CsStringUtils.isNotBlank(goodsDTO.getRefGoodsId())) {
            orderDTO.setProxyOrderType(ProxyOrderTypeEnum.SECONDARY.getCode());
        }
        //验证ERP地址
        if (specialGoodsResult.getData().getConcreteFlag() != 1) {
            validateErpAddress(orderDTO, contractGoodsDTO, goodsDTO.getLogistics());
        }
        //商品购买数量(下单量)
        BigDecimal quantity = itemDTO.getItemQuantity();
        //初始化剩余库存参数
        BigDecimal surplusQuantity = ArithUtils.subtract(contractGoodsDTO.getPlanGoodsAmount(), contractGoodsDTO.getActualNum());
        //查询商品是否做了库存分配
        StockChangeMapDTO stockChangeMapDTO = new StockChangeMapDTO();
        stockChangeMapDTO.setOrderLevel(1);
        stockChangeMapDTO.setBuyerName(orderDTO.getBuyerName());
        stockChangeMapDTO.setOrderCode(orderDTO.getOrderCode());
        stockChangeMapDTO.setChangeQuantity(quantity);
        StockItemQueryDTO stockItemQueryDTO = new StockItemQueryDTO();
        stockItemQueryDTO.setGoodsId(contractGoodsDTO.getGoodsId());
        stockItemQueryDTO.setBuyerId(orderDTO.getBuyerId());
        stockItemQueryDTO.setSaleRegionId(contractGoodsDTO.getSaleAreaRealCode());
        stockItemQueryDTO.setWarehouseId(contractGoodsDTO.getOutGoodsAddressId());
        List<StockItemDTO> stockItemList = stockService.findStockItemByQuery(stockItemQueryDTO);
        log.info("stockItemList_findStockItemByQuery:{},result:{}", stockItemQueryDTO, JSON.toJSONString(stockItemList));
        BeanUtils.copyProperties(stockItemQueryDTO, stockChangeMapDTO);
        if (CollectionUtils.isNotEmpty(stockItemList)) {
            surplusQuantity = stockItemList.get(0).getQuantitySurplusFirst();
            orderDTO.setStockChangeMapDTO(stockChangeMapDTO);
        }
        //如果是代理商品需要查询代理商品的库存量
        surplusQuantity = getSurplusQuantity(orderDTO, warehouse, goodsDTO, stockChangeMapDTO, stockItemQueryDTO, stockItemList, surplusQuantity);
        //校验库存可售量
        if (surplusQuantity.compareTo(quantity) < 0) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "商品【" + contractGoodsDTO.getGoodsName() + "】可售库存不足，剩余可售库存："
                    + surplusQuantity + contractGoodsDTO.getUnit());
        }
        //一级订单下单不校验
        checkOrderminNum(orderDTO, contractGoodsDTO, quantity);

        //设置基本信息
        itemDTO.setConvertRate(BigDecimal.ONE);
        itemDTO.setUnconvertRate(BigDecimal.ONE);
        itemDTO.setGoodsName(contractGoodsDTO.getGoodsName());
        itemDTO.setResourceName(contractGoodsDTO.getGoodsName());
        itemDTO.setResourceId(contractGoodsDTO.getContractGoodsId());
        itemDTO.setUnits(contractGoodsDTO.getUnit());
        itemDTO.setUnitsName(contractGoodsDTO.getUnit());
        itemDTO.setMeasureQuantity(itemDTO.getItemQuantity());
        itemDTO.setMeasureUnits(goodsDTO.getMeasureUnit());
        //运输品类
        itemDTO.setLogistics(goodsDTO.getLogistics());
        itemDTO.setCategoryType(goodsDTO.getCategoryType());
        itemDTO.setCategoryCode(goodsDTO.getCategoryCode());
        itemDTO.setGoodsKeywords(goodsDTO.getSearchKeywords());
        //设置基本信息
        itemDTO.setStoreId(orderDTO.getStoreId());
        itemDTO.setStoreName(warehouse.getName());
        itemDTO.setStoreType(warehouse.getType());
        itemDTO.setStoreAddress(warehouse.getAddress());
        //计算资源单价
        computeOrderItemPrice(orderDTO, itemDTO, contractGoodsDTO);
        //支付有效期
        setPayTimeLimit(orderDTO, date, contractGoodsDTO);
        return trContractGoodsDTO;
    }

    private BigDecimal getSurplusQuantity(OrderDTO orderDTO, WarehouseDetailsDTO warehouse, GoodsDTO goodsDTO, StockChangeMapDTO stockChangeMapDTO, StockItemQueryDTO stockItemQueryDTO, List<StockItemDTO> stockItemList, BigDecimal surplusQuantity) {
        if (CsStringUtils.isNotBlank(goodsDTO.getRefGoodsId())) {
            stockChangeMapDTO.setOrderLevel(2);
            stockChangeMapDTO.setBuyerName(orderDTO.getSellerName());
            //代理商品查询二级订单分配可售量
            stockItemQueryDTO.setGoodsId(goodsDTO.getRefGoodsId());
            stockItemQueryDTO.setBuyerId(goodsDTO.getSellerId());
            //查询映射仓库和映射销售区域
            if (CsStringUtils.isBlank(warehouse.getRefWarehouseId())) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "下单仓库没有映射上游厂家仓库");
            }
            stockItemQueryDTO.setWarehouseId(warehouse.getRefWarehouseId());
            RegionQueryDTO regionQueryDTO = new RegionQueryDTO();
            regionQueryDTO.setSellerId(goodsDTO.getRefSellerId());
            regionQueryDTO.setProvinceCode(orderDTO.getProvinceCode());
            regionQueryDTO.setCityCode(orderDTO.getCityCode());
            regionQueryDTO.setDistrictCode(orderDTO.getDistrictCode());
            regionQueryDTO.setStreetCode(orderDTO.getStreetCode());
            List<SaleRegionSampleDTO> saleRegionList = saleRegionService.findByRegion(regionQueryDTO);
            log.info("validateOrderItem_saleRegionList:" + JSON.toJSONString(saleRegionList));
            if (CollectionUtils.isEmpty(saleRegionList)) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "找不到行政区域对应的厂家销售区域");
            }
            stockItemQueryDTO.setSaleRegionId(saleRegionList.get(0).getSaleRegionId());
            List<StockItemDTO> refStockItemList = stockService.findStockItemByQuery(stockItemQueryDTO);
            log.info("refStockItemList_findStockItemByQuery:{},result:{}", stockItemQueryDTO, JSON.toJSONString(refStockItemList));
            //保留二级订单原本的库存扣减数据用于经销商分配的库存扣减
            stockChangeMapDTO.setAgentBuyerId(stockChangeMapDTO.getBuyerId());
            stockChangeMapDTO.setAgentBuyerName(stockChangeMapDTO.getBuyerName());
            stockChangeMapDTO.setAgentGoodsId(stockChangeMapDTO.getGoodsId());
            stockChangeMapDTO.setAgentWarehouseId(stockChangeMapDTO.getWarehouseId());
            stockChangeMapDTO.setAgentSaleRegionId(stockChangeMapDTO.getSaleRegionId());
            //设置代理商品的扣减数据
            BeanUtils.copyProperties(stockItemQueryDTO, stockChangeMapDTO);
            //经销商没有为终端买家分配库存则取厂家商品的二级库存验证
            if (CollectionUtils.isEmpty(stockItemList) && CollectionUtils.isNotEmpty(refStockItemList)) {
                surplusQuantity = refStockItemList.get(0).getQuantitySurplusSecond();
                orderDTO.setStockChangeMapDTO(stockChangeMapDTO);
            }
        }
        return surplusQuantity;
    }

    private static void setPayTimeLimit(OrderDTO orderDTO, Date date, TrContractGoodsDTO contractGoodsDTO) {
        if (CsStringUtils.equals(contractGoodsDTO.getPaydateType(), PaydateTypeEnum.PAYDATE_TYPE2.code()) &&
                contractGoodsDTO.getPaydateLimit() != null) {//小时
            Date timeLimit = DateUtils.addHours(date, contractGoodsDTO.getPaydateLimit().intValue());
            if (orderDTO.getPayTimeLimit() == null || orderDTO.getPayTimeLimit().after(timeLimit)) {
                orderDTO.setPayTimeLimit(timeLimit);
            }
        }
    }

    private void checkOrderminNum(OrderDTO orderDTO, TrContractGoodsDTO contractGoodsDTO, BigDecimal quantity) {
        if (!CsStringUtils.equals(orderDTO.getProxyOrderType(), ProxyOrderTypeEnum.PRIMARY.getCode())) {
            if (contractGoodsDTO.getOrderminNum() != null && contractGoodsDTO.getOrderminNum().compareTo(quantity) > 0) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "商品【" + contractGoodsDTO.getGoodsName() + "】低于单笔最小购买量");
            }
            if (contractGoodsDTO.getOrdermaxNum() != null && contractGoodsDTO.getOrdermaxNum().compareTo(quantity) < 0) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "商品【" + contractGoodsDTO.getGoodsName() + "】超出单笔最大购买量");
            }
            //商品最小变动量(最多支持两位小数)
            if (contractGoodsDTO.getOrderminNum() != null && contractGoodsDTO.getOrderminchangeNum() != null) {
                //订单最小购买量
                Integer orderMinNum = ArithUtils.multiply(contractGoodsDTO.getOrderminNum(), new BigDecimal(100)).intValue();
                //订单最小变动量
                Integer orderMinChangeNum = ArithUtils.multiply(contractGoodsDTO.getOrderminchangeNum(), new BigDecimal(100)).intValue();
                //订单购买量
                Integer purchaseQuantity = ArithUtils.multiply(quantity, new BigDecimal(100)).intValue();
                if ((purchaseQuantity - orderMinNum) % orderMinChangeNum != 0) {
                    throw new BizException(BasicCode.UNDEFINED_ERROR,
                            "不满足商品最小变动量，您的商品[" + contractGoodsDTO.getGoodsName() + "]购买量应为单笔最小购买量("
                                    + contractGoodsDTO.getOrderminNum().setScale(2, RoundingMode.HALF_UP)
                                    + contractGoodsDTO.getUnit() + ")加变动量("
                                    + contractGoodsDTO.getOrderminchangeNum().setScale(2, RoundingMode.HALF_UP)
                                    + contractGoodsDTO.getUnit() + ")的倍数");
                }
            }

            //验证合同商品单日最大购买量
            checkLimitDayOrderNum(orderDTO, contractGoodsDTO, quantity);
        }
    }

    private void checkLimitDayOrderNum(OrderDTO orderDTO, TrContractGoodsDTO contractGoodsDTO, BigDecimal quantity) {
        Optional.ofNullable(contractGoodsDTO.getDaymaxNum())
                .filter(dayMaxNum -> dayMaxNum.compareTo(BigDecimal.ZERO) > 0)
                .ifPresent(dayMaxNum -> {
                    BigDecimal dayOrderNum = baseOrderSearchBiz.countContractGoodsDayQuantity(orderDTO.getBuyerId(),
                            contractGoodsDTO.getContractGoodsId());
                    //当日已下单量需要加上本次下单量
                    BigDecimal limitDayOrderNum = ArithUtils.add(dayOrderNum, quantity);
                    if (dayMaxNum.compareTo(limitDayOrderNum) < 0) {
                        throw new BizException(BasicCode.UNDEFINED_ERROR,
                                "合同商品[" + contractGoodsDTO.getGoodsName() + "]单日最大购买量为"
                                        + dayMaxNum.setScale(2, RoundingMode.HALF_UP)
                                        + contractGoodsDTO.getUnit() +
                                        ", 本日已购买数量为" + dayOrderNum.setScale(2, RoundingMode.HALF_UP)
                                        + contractGoodsDTO.getUnit());
                    }
                });
    }

    private TrContractGoodsDTO getTrContractGoodsDTO(OrderDTO orderDTO, OrderItemDTO itemDTO) {
        String contractGoodsId = itemDTO.getContractGoodsId() == null ? itemDTO.getResourceId() : itemDTO.getContractGoodsId();
        TrContractGoodsDTO contractGoodsDTO = contractService.getContractGoodsById(contractGoodsId);
        if (contractGoodsDTO == null) {
            throw new BizException(BasicCode.DATA_NOT_EXIST, "合同商品:" + itemDTO.getContractGoodsId());
        }
        if (!CsStringUtils.equals(contractGoodsDTO.getOutGoodsAddressId(), orderDTO.getStoreId())) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "下单仓库与资源仓库不匹配");
        }
        return contractGoodsDTO;
    }

    private static void setSaleRegionPath(OrderDTO orderDTO, TrContractGoodsDTO trContractGoodsDTO) {
        if (CsStringUtils.isNotBlank(trContractGoodsDTO.getSaleAreaCode5())) {
            orderDTO.setSaleRegionPath(trContractGoodsDTO.getSaleAreaCode5());
        } else if (CsStringUtils.isNotBlank(trContractGoodsDTO.getSaleAreaCode4())) {
            orderDTO.setSaleRegionPath(trContractGoodsDTO.getSaleAreaCode4());
        } else if (CsStringUtils.isNotBlank(trContractGoodsDTO.getSaleAreaCode3())) {
            orderDTO.setSaleRegionPath(trContractGoodsDTO.getSaleAreaCode3());
        } else if (CsStringUtils.isNotBlank(trContractGoodsDTO.getSaleAreaCode2())) {
            orderDTO.setSaleRegionPath(trContractGoodsDTO.getSaleAreaCode2());
        } else if (CsStringUtils.isNotBlank(trContractGoodsDTO.getSaleAreaCode())) {
            orderDTO.setSaleRegionPath(trContractGoodsDTO.getSaleAreaCode());
        }
    }

    private static void checkContractStatus(TrContractDTO contractDTO) {
        String status = contractDTO.getContractStatus();
        ContractStatusEnum statusEnum = ContractStatusEnum.from(status);
        log.info("当前合同状态：{}",status);
        if (ContractStatusEnum.INEFFECT != statusEnum) {
            throw new BizException(BasicCode.CUSTOM_ERROR, "当前合同未启用");
        }
    }

    private TrContractDTO getTrContractDTO(OrderDTO orderDTO) {
        if (CsStringUtils.isBlank(orderDTO.getDealsId())) {
            throw new BizException(BasicCode.CUSTOM_ERROR, "关联合同不能为空");
        }

        TrContractDTO contractDTO = contractService.getContract(orderDTO.getDealsId());
        if (Objects.isNull(contractDTO)) {
            throw new BizException(BasicCode.CUSTOM_ERROR, "制定合同不存在");
        }
        return contractDTO;
    }

    private WarehouseDetailsDTO getWarehouseDetailsDTO(OrderDTO orderDTO) {
        WarehouseDetailsDTO warehouse;
        try {
            warehouse = warehouseService.queryWarehouseDetails(orderDTO.getStoreId());
            if (warehouse == null) {
                throw new BizException(BasicCode.DB_DATA_NOT_FOUND, "仓库:" + orderDTO.getStoreId());
            }
            log.info("WarehouseService.queryWarehouseDetails_end:{}", JSON.toJSONString(warehouse));
            //设置提货码头信息
            orderDTO.getOrderInfoExtDTO().setPickingWharfId(warehouse.getWharfId());
            orderDTO.getOrderInfoExtDTO().setPickingWharfName(warehouse.getWharfName());
        } catch (Exception e) {
            log.error("queryWarehouseDetails:" + orderDTO.getStoreId(), e);
            throw new BizException(BasicCode.UNDEFINED_ERROR, "查询仓库信息错误");
        }
        return warehouse;
    }

    /**
     * 验证ERP地址
     */
    private void validateErpAddress(OrderDTO orderDTO, TrContractGoodsDTO contractGoodsDTO, String transportCategoryId) {
        //ERP卖家、非二级订单需要验证erp地址
        if (orderErpBiz.hasErpOrder(contractGoodsDTO.getSellerId(), MemberConfigEnum.ERP_ORDER_FLG.getKeyCode()) &&
                !CsStringUtils.equals(ProxyOrderTypeEnum.SECONDARY.getCode(), orderDTO.getProxyOrderType())) {
            //船运
            checkWaterTransport(orderDTO);
            //汽运
            if (CsStringUtils.equals(TransportToolTypeEnum.ROAD_TRANSPORT.getCode(), orderDTO.getOrderInfoExtDTO().getTransportType())) {
                //卖家配送
                if (CsStringUtils.equals(PickingBillTypeEnum.SELLER_DELIVERY.getCode(), orderDTO.getDeliverWay())) {
                    doSellerDelivery(orderDTO, transportCategoryId);
                    //平台配送、自提且合同为流向管控
                } else if (CsStringUtils.equals(contractGoodsDTO.getFlowMonitor(), "1")) {
                    doFlowMonitorLogic(orderDTO);
                }
            }
        }
    }

    private void doFlowMonitorLogic(OrderDTO orderDTO) {
        ReceivingAddressMapQueryDTO addressMapQueryDTO = new ReceivingAddressMapQueryDTO();
        addressMapQueryDTO.setAddressId(orderDTO.getAddressId());
        addressMapQueryDTO.setFirmId(orderDTO.getSellerId());
        //背靠背一级订单设置经销商ID
        if (CsStringUtils.equals(ProxyOrderTypeEnum.PRIMARY.getCode(), orderDTO.getProxyOrderType())) {
            addressMapQueryDTO.setSellerId(orderDTO.getBuyerId());
        }
        if (!receivingAddressService.hasERPAddress(addressMapQueryDTO)) {
            throw new BizException(BasicCode.CUSTOM_ERROR, "收货地址未同步ERP");
        }
    }

    private void doSellerDelivery(OrderDTO orderDTO, String transportCategoryId) {
        doFlowMonitorLogic(orderDTO);
        //验证是否绑定了ERP运输路线
        CarriageRouteQueryDTO carriageRouteQueryDTO = new CarriageRouteQueryDTO();
        carriageRouteQueryDTO.setUserId(orderDTO.getSellerId());
        carriageRouteQueryDTO.setWarehouseId(orderDTO.getStoreId());
        carriageRouteQueryDTO.setReceiveAddressId(orderDTO.getAddressId());
        carriageRouteQueryDTO.setTransportCategoryId(transportCategoryId);
        if (!carriageRouteService.hasERPContractAddress(carriageRouteQueryDTO)) {
            throw new BizException(BasicCode.CUSTOM_ERROR, "运费定价未绑定ERP运输路线");
        }
    }

    private void checkWaterTransport(OrderDTO orderDTO) {
        if ((CsStringUtils.equals(TransportToolTypeEnum.WATER_TRANSPORT.getCode(), orderDTO.getOrderInfoExtDTO().getTransportType()) &&
                CsStringUtils.equals(PickingBillTypeEnum.SELLER_DELIVERY.getCode(), orderDTO.getDeliverWay()))
            && (!receivingAddressService.hasERPWharf(orderDTO.getAddressId(), orderDTO.getSellerId()))) {
                throw new BizException(BasicCode.CUSTOM_ERROR, "收货地址请绑定ERP卸货码头");
        }
    }

    @Override
    public void updateResourceNum(OrderDTO orderDTO, List<OrderItemDTO> orderItems, String operator) {
        if (orderDTO.getStockChangeMapDTO() != null) {
            StockChangeDTO stockChangeDTO = new StockChangeDTO();
            BeanUtils.copyProperties(orderDTO.getStockChangeMapDTO(), stockChangeDTO);
            if (stockChangeDTO.getOrderLevel() == 2) {
                stockChangeDTO.setBuyerName(orderDTO.getSellerName());
            }
            log.info("updateResourceNum_stockChange:" + JSON.toJSONString(stockChangeDTO));
            stockService.stockChange(stockChangeDTO);
            return;
        }
        //更新合同资源数量
        OrderUpdateContractDTO updateContract = new OrderUpdateContractDTO();
        updateContract.setContractId(orderDTO.getDealsId());
        List<TrContractNumDTO> contractNumList = Lists.newArrayList();
        orderDTO.getOrderItems().stream().forEach(itemDTO -> {
            TrContractNumDTO contractNum = new TrContractNumDTO();
            contractNum.setContractGoodsId(CsStringUtils.isBlank(itemDTO.getContractGoodsId()) ?
                    itemDTO.getResourceId() : itemDTO.getContractGoodsId());
            contractNum.setExpectNum(itemDTO.getItemQuantity().multiply(itemDTO.getConvertRate()));
            contractNum.setLockNum(itemDTO.getItemQuantity().multiply(itemDTO.getConvertRate()));
            contractNumList.add(contractNum);
        });
        updateContract.setTrContractNumList(contractNumList);
        contractService.updateContractStatusAndNum(updateContract, operator);
    }

    @Override
    public void cancelOrder(OrderCancelDO orderCancelDO) {
        super.cancelOrder(orderCancelDO);
        //更新合同资源数量
        OrderDTO orderDTO = orderQueryService.getOrderInfo(orderCancelDO.getOrderId());
        //生成库存扣减对象
        StockChangeDTO stockChangeDTO = new StockChangeDTO();
        if (ifAllocationGoodsStock(orderDTO, stockChangeDTO)) {
            log.info("cancelOrder_stockChange:" + JSON.toJSONString(stockChangeDTO));
            stockService.stockChange(stockChangeDTO);
            return;
        }
        OrderUpdateContractDTO updateContract = new OrderUpdateContractDTO();
        updateContract.setContractId(orderDTO.getDealsId());
        List<TrContractNumDTO> contractNumList = Lists.newArrayList();
        orderDTO.getOrderItems().stream().forEach(itemDTO -> {
            TrContractNumDTO contractNum = new TrContractNumDTO();
            contractNum.setType(ContractGoodsNumEnum.EXPECT_NUM.code());
            contractNum.setContractGoodsId(itemDTO.getResourceId());
            //取消时，返还锁定的资源数量
            contractNum.setLockNum(itemDTO.getItemQuantity().multiply(itemDTO.getConvertRate()).negate());
            contractNumList.add(contractNum);
        });
        updateContract.setTrContractNumList(contractNumList);
        contractService.updateContractStatusAndNum(updateContract, orderCancelDO.getOperator());
    }

    @Override
    public void closeOrder(OrderCloseDO orderCloseDO) {
        super.closeOrder(orderCloseDO);
        OrderDTO orderDTO = orderQueryService.getOrderInfo(orderCloseDO.getOrderId());
        //生成库存扣减对象
        StockChangeDTO stockChangeDTO = new StockChangeDTO();
        if (ifAllocationGoodsStock(orderDTO, stockChangeDTO)) {
            log.info("closeOrder_stockChange:" + JSON.toJSONString(stockChangeDTO));
            stockService.stockChange(stockChangeDTO);
            return;
        }
        //更新合同资源数量
        OrderUpdateContractDTO updateContract = new OrderUpdateContractDTO();
        updateContract.setContractId(orderDTO.getDealsId());
        List<TrContractNumDTO> contractNumList = Lists.newArrayList();
        orderDTO.getOrderItems().stream().forEach(itemDTO -> {
            TrContractNumDTO contractNum = new TrContractNumDTO();
            contractNum.setType(ContractGoodsNumEnum.EXPECT_NUM.code());
            contractNum.setContractGoodsId(itemDTO.getResourceId());
            //关闭时, 返还锁定的未提货资源数量
            BigDecimal returnNum = ArithUtils.subtract(itemDTO.getItemQuantity(), itemDTO.getItemSendQuantity());
            contractNum.setLockNum(returnNum.multiply(itemDTO.getConvertRate()).negate());
            contractNumList.add(contractNum);
        });
        updateContract.setTrContractNumList(contractNumList);
        contractService.updateContractStatusAndNum(updateContract, orderCloseDO.getOperator());
    }

    @Override
    public void shippedPostProcessor(String orderId, UpdateTakeInfoShippedDTO updateTakeInfoShippedDTO) {
        log.info("sendQuantityPostProcessor:" + JSON.toJSONString(updateTakeInfoShippedDTO));
        super.shippedPostProcessor(orderId, updateTakeInfoShippedDTO);
        //更新合同资源数量
        OrderDTO orderDTO = orderQueryService.getOrderInfo(orderId);
        OrderUpdateContractDTO updateContract = new OrderUpdateContractDTO();
        updateContract.setContractId(orderDTO.getDealsId());
        List<TrContractNumDTO> contractNumList = Lists.newArrayList();
        UpdateTakeItemShippedDTO itemShippedDTO = updateTakeInfoShippedDTO.getUpdateTakeItemShippedDTO().get(0);
        for (OrderItemDTO itemDTO : orderDTO.getOrderItems()) {
            if (!CsStringUtils.equals(itemShippedDTO.getResourceId(), itemDTO.getOrderItemId()))
                continue;
            TrContractNumDTO contractNum = new TrContractNumDTO();
            contractNum.setContractGoodsId(itemDTO.getResourceId());
            //更新实际提货量
            contractNum.setActualNum(itemShippedDTO.getShippedQuantity());
            itemDTO.setItemSendQuantity(itemDTO.getItemSendQuantity() == null ?
                    BigDecimal.ZERO : itemDTO.getItemSendQuantity());
            //计算锁定数量(发货数量大于于计划提货量时增加超发的锁定数量)
            if (itemDTO.getItemSendQuantity().compareTo(itemDTO.getItemQuantity()) > 0) {
                //累计超发数量
                BigDecimal overSendQuantity = itemDTO.getItemSendQuantity().subtract(itemDTO.getItemQuantity());
                overSendQuantity = overSendQuantity.multiply(itemDTO.getConvertRate());
                //判断是否为第一车超发(累计超发数量<=当前出站数量)
                BigDecimal lockNum = overSendQuantity.compareTo(itemShippedDTO.getShippedQuantity()) < 1 ?
                        overSendQuantity : itemShippedDTO.getShippedQuantity();
                contractNum.setLockNum(lockNum);
            }
            contractNumList.add(contractNum);
            break;
        }
        updateContract.setTrContractNumList(contractNumList);
        contractService.updateContractStatusAndNum(updateContract, updateTakeInfoShippedDTO.getOperator());
    }

    @Override
    public void completeOrder(OrderCompleteDO orderCompleteDO) {
        super.completeOrder(orderCompleteDO);
        OrderDTO orderDTO = orderQueryService.getOrderInfo(orderCompleteDO.getOrderId());
        OrderItemDTO orderItemDTO = orderDTO.getOrderItems().get(0);
        BigDecimal returnNum = ArithUtils.subtract(orderItemDTO.getItemQuantity(), orderItemDTO.getItemSendQuantity());
        //代理订单扣减库存,返还数量小于0时直接忽略
        StockChangeDTO stockChangeDTO = new StockChangeDTO();
        if (returnNum.compareTo(BigDecimal.ZERO) > 0 && ifAllocationGoodsStock(orderDTO, stockChangeDTO)) {
            log.info("completeOrder_stockChange:" + JSON.toJSONString(stockChangeDTO));
            stockService.stockChange(stockChangeDTO);
        }
    }
}
