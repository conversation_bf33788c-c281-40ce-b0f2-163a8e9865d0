package com.ecommerce.order.fsm.service.impl;

import com.alibaba.fastjson.JSON;
import com.ecommerce.base.api.dto.ReceivingAddressMapQueryDTO;
import com.ecommerce.base.api.dto.saleregion.RegionQueryDTO;
import com.ecommerce.base.api.dto.saleregion.SaleRegionSampleDTO;
import com.ecommerce.base.api.dto.warehouse.WarehouseDetailsDTO;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.utils.ArithUtils;
import com.ecommerce.goods.api.dto.GoodsDTO;
import com.ecommerce.goods.api.dto.ResourceDTO;
import com.ecommerce.goods.api.dto.SpecialGoodsAttributeDTO;
import com.ecommerce.goods.api.dto.stock.StockChangeDTO;
import com.ecommerce.goods.api.dto.stock.StockItemDTO;
import com.ecommerce.goods.api.dto.stock.StockItemQueryDTO;
import com.ecommerce.goods.api.enums.PaydateTypeEnum;
import com.ecommerce.goods.api.enums.TradeStatusEnum;
import com.ecommerce.goods.api.service.IResourceNumTCCService;
import com.ecommerce.logistics.api.dto.carriage.CarriageRouteQueryDTO;
import com.ecommerce.logistics.api.enums.PickingBillTypeEnum;
import com.ecommerce.logistics.api.enums.TransportToolTypeEnum;
import com.ecommerce.logistics.api.service.ITransportCategoryService;
import com.ecommerce.member.api.dto.member.config.MemberConfigEnum;
import com.ecommerce.order.api.dto.OrderDTO;
import com.ecommerce.order.api.dto.OrderInfoExtDTO;
import com.ecommerce.order.api.dto.OrderItemDTO;
import com.ecommerce.order.api.dto.proxy.StockChangeMapDTO;
import com.ecommerce.order.api.enums.ProxyOrderTypeEnum;
import com.ecommerce.order.dao.dto.OrderCancelDO;
import com.ecommerce.order.dao.dto.OrderCloseDO;
import com.ecommerce.order.dao.dto.OrderCompleteDO;
import com.ecommerce.order.fsm.service.AbstractOrderService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.ecommerce.common.utils.CsStringUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 挂牌下单
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class ListingOrderHandlerService extends AbstractOrderService {

    public static final String SERVICE_NAME = "listingOrderHandlerService";

    private final IResourceNumTCCService resourceNumTCCService;

    private final ITransportCategoryService transportCategoryService;

    @Override
    public String getServiceName() {
        return SERVICE_NAME;
    }

    /**
     * 验证订单行数据
     *
     * @param orderDTO 订单对象
     * @param itemDTO  商品行对象
     * @param resource 商品资源对象
     */
    private void validateOrderItem(OrderDTO orderDTO,
                                   OrderItemDTO itemDTO,
                                   ResourceDTO resource,
                                   GoodsDTO goodsDTO,
                                   WarehouseDetailsDTO warehouse) {
        log.info("validateOrder_resource:{}", JSON.toJSONString(resource));
        //卖家验证
        checkSellerId(orderDTO, resource);
        //购买数量验证
        BigDecimal quantity = itemDTO.getItemQuantity();
        if (quantity == null || quantity.compareTo(BigDecimal.ZERO) <= 0) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "购买数量必须大于0" + resource.getSaleUnit());
        }
        //初始化剩余库存参数
        BigDecimal surplusQuantity = resource.getCansaleNum();
        //查询商品是否做了库存分配
        StockChangeMapDTO stockChangeMapDTO = new StockChangeMapDTO();
        stockChangeMapDTO.setOrderLevel(1);
        stockChangeMapDTO.setBuyerName(orderDTO.getBuyerName());
        stockChangeMapDTO.setOrderCode(orderDTO.getOrderCode());
        stockChangeMapDTO.setChangeQuantity(quantity.multiply(itemDTO.getConvertRate()));
        StockItemQueryDTO stockItemQueryDTO = new StockItemQueryDTO();
        stockItemQueryDTO.setGoodsId(resource.getGoodsId());
        stockItemQueryDTO.setBuyerId(orderDTO.getBuyerId());
        stockItemQueryDTO.setSaleRegionId(resource.getSaleAreaRealCode());
        stockItemQueryDTO.setWarehouseId(resource.getStoreId());
        List<StockItemDTO> stockItemList = stockService.findStockItemByQuery(stockItemQueryDTO);
        log.info("stockItemList_findStockItemByQuery:{},result:{}", stockItemQueryDTO, JSON.toJSONString(stockItemList));
        BeanUtils.copyProperties(stockItemQueryDTO, stockChangeMapDTO);
        if (CollectionUtils.isNotEmpty(stockItemList)) {
            surplusQuantity = stockItemList.get(0).getQuantitySurplusFirst();
            orderDTO.setStockChangeMapDTO(stockChangeMapDTO);
        }
        //如果是代理商品需要查询代理商品的库存量
        if (CsStringUtils.isNotBlank(goodsDTO.getRefGoodsId())) {
            stockChangeMapDTO.setOrderLevel(2);
            stockChangeMapDTO.setBuyerName(orderDTO.getSellerName());
            //代理商品查询二级订单分配可售量
            stockItemQueryDTO.setGoodsId(goodsDTO.getRefGoodsId());
            stockItemQueryDTO.setBuyerId(goodsDTO.getSellerId());
            //查询映射仓库和映射销售区域
            if (CsStringUtils.isBlank(warehouse.getRefWarehouseId())) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "下单仓库没有映射上游厂家仓库");
            }
            stockItemQueryDTO.setWarehouseId(warehouse.getRefWarehouseId());
            RegionQueryDTO regionQueryDTO = new RegionQueryDTO();
            regionQueryDTO.setSellerId(goodsDTO.getRefSellerId());
            regionQueryDTO.setProvinceCode(orderDTO.getProvinceCode());
            regionQueryDTO.setCityCode(orderDTO.getCityCode());
            regionQueryDTO.setDistrictCode(orderDTO.getDistrictCode());
            regionQueryDTO.setStreetCode(orderDTO.getStreetCode());
            List<SaleRegionSampleDTO> saleRegionList = saleRegionService.findByRegion(regionQueryDTO);
            log.info("validateOrderItem_saleRegionList:" + JSON.toJSONString(saleRegionList));
            if (CollectionUtils.isEmpty(saleRegionList)) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "找不到行政区域对应的厂家销售区域");
            }
            stockItemQueryDTO.setSaleRegionId(saleRegionList.get(0).getSaleRegionId());
            List<StockItemDTO> refStockItemList = stockService.findStockItemByQuery(stockItemQueryDTO);
            log.info("refStockItemList_findStockItemByQuery:{},result:{}", stockItemQueryDTO, JSON.toJSONString(stockItemList));
            //保留二级订单原本的库存扣减数据用于经销商分配的库存扣减
            stockChangeMapDTO.setAgentBuyerId(stockChangeMapDTO.getBuyerId());
            stockChangeMapDTO.setAgentBuyerName(stockChangeMapDTO.getBuyerName());
            stockChangeMapDTO.setAgentGoodsId(stockChangeMapDTO.getGoodsId());
            stockChangeMapDTO.setAgentWarehouseId(stockChangeMapDTO.getWarehouseId());
            stockChangeMapDTO.setAgentSaleRegionId(stockChangeMapDTO.getSaleRegionId());
            //设置代理商品的扣减数据
            BeanUtils.copyProperties(stockItemQueryDTO, stockChangeMapDTO);
            //经销商没有为终端买家分配库存则取厂家商品的二级库存验证
            if (CollectionUtils.isEmpty(stockItemList) && CollectionUtils.isNotEmpty(refStockItemList)) {
                surplusQuantity = refStockItemList.get(0).getQuantitySurplusSecond();
                orderDTO.setStockChangeMapDTO(stockChangeMapDTO);
            }
        }
        checkConvertRate(itemDTO, resource, goodsDTO, surplusQuantity, quantity);
        //商品最小变动量(最多支持两位小数)
        checkOrderMinChangeNum(itemDTO, resource, quantity);
        //单日最大购买量
        checkDaymaxNum(orderDTO, itemDTO, resource, quantity);
    }

    private static void checkConvertRate(OrderItemDTO itemDTO, ResourceDTO resource, GoodsDTO goodsDTO, BigDecimal surplusQuantity, BigDecimal quantity) {
        //校验库存可售量
        if (surplusQuantity.compareTo(quantity.multiply(itemDTO.getConvertRate())) < 0) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "商品【" + goodsDTO.getGoodsName() + "】可售库存不足，剩余可售库存："
                    + surplusQuantity + resource.getPriceUnit());
        }
        //单笔最大购买量
        if (resource.getOrdermaxNum() != null && resource.getOrdermaxNum().compareTo(quantity.multiply(itemDTO.getConvertRate())) < 0) {
            throw new BizException(BasicCode.UNDEFINED_ERROR,
                    "商品[" + resource.getResourceName() + "]单笔最大购买量为" + resource.getOrdermaxNum().setScale(2, RoundingMode.HALF_UP) + resource.getPriceUnit());
        }
        //单笔最小购买量
        if (resource.getOrderminNum() != null && resource.getOrderminNum().compareTo(quantity.multiply(itemDTO.getConvertRate())) > 0) {
            throw new BizException(BasicCode.UNDEFINED_ERROR,
                    "商品[" + resource.getResourceName() + "]单笔最小购买量为" + resource.getOrderminNum().setScale(2, RoundingMode.HALF_UP) + resource.getPriceUnit());
        }
    }

    private static void checkOrderMinChangeNum(OrderItemDTO itemDTO, ResourceDTO resource, BigDecimal quantity) {
        if (resource.getOrderminNum() != null && resource.getOrderminchangeNum() != null) {
            //订单最小购买量
            Integer orderMinNum = ArithUtils.multiply(resource.getOrderminNum(), new BigDecimal(100)).intValue();
            //订单最小变动量
            Integer orderMinChangeNum = ArithUtils.multiply(resource.getOrderminchangeNum(), new BigDecimal(100)).intValue();
            //订单购买量
            Integer purchaseQuantity = ArithUtils.multiply(ArithUtils.multiply(quantity, itemDTO.getConvertRate()), new BigDecimal(100)).intValue();
            if ((purchaseQuantity - orderMinNum) % orderMinChangeNum != 0) {
                throw new BizException(BasicCode.UNDEFINED_ERROR,
                        "不满足商品最小变动量，您的商品[" + resource.getResourceName() + "]购买量应为单笔最小购买量("
                                + resource.getOrderminNum().setScale(2, RoundingMode.HALF_UP)
                                + resource.getPriceUnit() + ")加变动量("
                                + resource.getOrderminchangeNum().setScale(2, RoundingMode.HALF_UP)
                                + resource.getPriceUnit() + ")的倍数");
            }
        }
    }

    private void checkDaymaxNum(OrderDTO orderDTO, OrderItemDTO itemDTO, ResourceDTO resource, BigDecimal quantity) {
        if (resource.getDaymaxNum() != null) {
            BigDecimal ymQuantity = baseOrderSearchBiz.getBuyerResDayQuantity(orderDTO.getBuyerId(), resource.getResourceId());
            if (resource.getDaymaxNum().compareTo(ymQuantity.add(quantity.multiply(itemDTO.getConvertRate()))) < 0) {
                throw new BizException(BasicCode.UNDEFINED_ERROR,
                        "商品[" + resource.getResourceName() + "]单日最大购买量为" + resource.getDaymaxNum().setScale(2, RoundingMode.HALF_UP) + resource.getPriceUnit() +
                                ", 本日已购买数量为" + ymQuantity.setScale(2, RoundingMode.HALF_UP) + resource.getPriceUnit());
            }
        }
    }

    private static void checkSellerId(OrderDTO orderDTO, ResourceDTO resource) {
        if (CsStringUtils.isEmpty(orderDTO.getSellerId())) {
            orderDTO.setSellerId(resource.getSellerId());
        } else {
            if (!CsStringUtils.equals(orderDTO.getSellerId(), resource.getSellerId())) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "不允许同时购买不同卖家的商品");
            }
        }
    }

    /**
     * 验证ERP地址
     */
    private void validateErpAddress(OrderDTO orderDTO, ResourceDTO resource, String transportCategoryId) {
        //ERP卖家、非二级订单需要验证erp地址
        if ((orderErpBiz.hasErpOrder(resource.getSellerId(), MemberConfigEnum.ERP_ORDER_FLG.getKeyCode()) &&
                !CsStringUtils.equals(ProxyOrderTypeEnum.SECONDARY.getCode(), orderDTO.getProxyOrderType()))
            //卖家配送
                && (CsStringUtils.equals(PickingBillTypeEnum.SELLER_DELIVERY.getCode(), orderDTO.getDeliverWay()))) {
                ReceivingAddressMapQueryDTO addressMapQueryDTO = new ReceivingAddressMapQueryDTO();
                addressMapQueryDTO.setAddressId(orderDTO.getAddressId());
                addressMapQueryDTO.setFirmId(orderDTO.getSellerId());
                if (!receivingAddressService.hasERPAddress(addressMapQueryDTO)) {
                    throw new BizException(BasicCode.CUSTOM_ERROR, "收货地址未同步ERP");
                }
                //验证是否绑定了ERP运输路线
                CarriageRouteQueryDTO carriageRouteQueryDTO = new CarriageRouteQueryDTO();
                carriageRouteQueryDTO.setUserId(orderDTO.getSellerId());
                carriageRouteQueryDTO.setWarehouseId(orderDTO.getStoreId());
                carriageRouteQueryDTO.setReceiveAddressId(orderDTO.getAddressId());
                carriageRouteQueryDTO.setTransportCategoryId(transportCategoryId);
                if (!carriageRouteService.hasERPContractAddress(carriageRouteQueryDTO)) {
                    throw new BizException(BasicCode.CUSTOM_ERROR, "运费定价未绑定ERP运输路线");
                }
        }
    }

    /**
     * 计算订单行单价
     *
     * @param orderDTO 订单对象
     * @param itemDTO  商品行对象
     * @param resource 商品资源对象
     */
    private void computeOrderItemPrice(OrderDTO orderDTO, OrderItemDTO itemDTO, ResourceDTO resource) {
        //资源单价
        BigDecimal resourceUnitPrice = resource.getFactoryPrice();
        //配送时进行到位价反推
        if (CsStringUtils.equals(PickingBillTypeEnum.SELLER_DELIVERY.getCode(), orderDTO.getDeliverWay())) {
            ItemResult<SpecialGoodsAttributeDTO> goodsResult =
                    goodsService.getSpecialGoodsAttribute(resource.getGoodsId());
            if (!goodsResult.isSuccess()) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "查询商品特殊属性失败");
            }
            //配送时商品单价(资源单价＋运费单价)为到位价
            if (goodsResult.getData().getConcreteFlag() == 1) {
                resourceUnitPrice = resource.getArrivePrice();
                //到位价反推
            } else {
                resourceUnitPrice = resource.getArrivePrice() == null || BigDecimal.ZERO.compareTo(resource.getArrivePrice()) == 0 ?
                        resource.getFactoryPrice() : resource.getArrivePrice();
            }
        }
        if (resourceUnitPrice.compareTo(BigDecimal.ZERO) <= 0) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "缺少商品价格");
        }
        //计算加价项
        BigDecimal addItemPrice = BigDecimal.ZERO;
        itemDTO.setAdditemPrice(addItemPrice);
        //当前销售单位不等于记录单位时进行单价转换
        if (!itemDTO.getUnits().equals(resource.getPriceUnit())) {
            log.info("resourceUnitPrice.multiply(resource.getConvertRate()):{}  = resourceUnitPrice: {} * resource.getConvertRate():{} ", resourceUnitPrice.multiply(resource.getConvertRate()), resourceUnitPrice, resource.getConvertRate());
            resourceUnitPrice = resourceUnitPrice.multiply(resource.getConvertRate()).setScale(2, RoundingMode.HALF_UP);
            log.info("resourceUnitPrice.multiply(resource.getConvertRate()).setScale(2, BigDecimal.ROUND_HALF_UP:{}", resourceUnitPrice);
        }
        BigDecimal originUnitPrice = itemDTO.getOriginUnitPrice();
        // 检查资源价格是否发生变化
        BigDecimal arrivePrice = resource.getArrivePrice();
        BigDecimal factoryPrice = resource.getFactoryPrice();
        if (Objects.nonNull(itemDTO.getOriginUnitPrice()) && !Objects.equals(arrivePrice, originUnitPrice) && !Objects.equals(factoryPrice, originUnitPrice)) {
            throw new BizException(BasicCode.CUSTOM_ERROR, "商品价格有变化，请重新下单");
        }

        //资源原始单价
        itemDTO.setOriginUnitPrice(resourceUnitPrice);
        //资源成交单价(包含加价项):  resourceUnitPrice + addItemPrice
        itemDTO.setActualUnitPrice(resourceUnitPrice.add(addItemPrice));
    }

    /**
     * 转换订单商品行数据对象
     *
     * @param orderDTO 订单对象
     */
    @Override
    public void convertOrderItem(OrderDTO orderDTO) {
        //获取仓库数据
        WarehouseDetailsDTO warehouse;
        try {
            warehouse = warehouseService.queryWarehouseDetails(orderDTO.getStoreId());
            log.info("WarehouseService.queryWarehouseDetails_end:{}", JSON.toJSONString(warehouse));
        } catch (Exception e) {
            log.error("queryWarehouseDetails:" + orderDTO.getStoreId(), e);
            throw new BizException(BasicCode.UNDEFINED_ERROR, "查询仓库信息错误");
        }
        if (warehouse == null) {
            throw new BizException(BasicCode.DB_DATA_NOT_FOUND, "仓库:" + orderDTO.getStoreId());
        }
        Set<String> supportPayWaySet = Sets.newHashSet();//支持的支付方式
        ResourceDTO resourceDTO = new ResourceDTO();
        for (OrderItemDTO itemDTO : orderDTO.getOrderItems()) {
            //获取资源对象
            ResourceDTO resource = resourceService.getResourceDetail(itemDTO.getResourceId());
            if (resource == null) {
                throw new BizException(BasicCode.DB_DATA_NOT_FOUND, "订单商品[" + itemDTO.getResourceId() + "]不存在");
            }
            //Emall商城-提交订单：应不支持提交已下架资源订单
            if (!CsStringUtils.equals(TradeStatusEnum.TRADE_STATUS100.getCode(), resource.getTradeStatus())) {
                throw new BizException(BasicCode.DB_DATA_NOT_FOUND, "订单商品[" + resource.getGoodsName() + "]已下架");
            }
            if (!CsStringUtils.equals(resource.getStoreId(), orderDTO.getStoreId())) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "下单仓库与资源仓库不匹配");
            }
            resourceDTO = resource;
            //设置单位转换率
            setConvertRate(itemDTO, resource);
            //获取商品信息
            GoodsDTO goodsDTO = getGoodsDTO(orderDTO, resource);
            ItemResult<SpecialGoodsAttributeDTO> specialGoodsResult = getSpecialGoodsAttributeDTOItemResult(orderDTO, resource, goodsDTO);
            //验证商品行对象
            validateOrderItem(orderDTO, itemDTO, resource, goodsDTO, warehouse);

            //支持的支付方式
            supportPayWaySet = getSupportPayWaySet(resource, supportPayWaySet);
            orderDTO.setSupportPayWay(Lists.newArrayList(supportPayWaySet));
            itemDTO.setPayWay(resource.getPayWay());
            itemDTO.setGoodsId(resource.getGoodsId());
            itemDTO.setGoodsName(resource.getGoodsName());
            itemDTO.setResourceName(resource.getResourceName());
            itemDTO.setResourceVersion(resource.getResourceVersion());
            itemDTO.setMeasureQuantity(itemDTO.getItemQuantity().multiply(itemDTO.getConvertRate()));
            itemDTO.setAutoCompleteType(resource.getAutoCompleteType());
            itemDTO.setAutoCompleteThreshold(resource.getAutoCompleteThreshold());
            itemDTO.setMeasureUnits(goodsDTO.getMeasureUnit());
            //运输品类
            itemDTO.setLogistics(goodsDTO.getLogistics());
            itemDTO.setCategoryType(goodsDTO.getCategoryType());
            itemDTO.setCategoryCode(goodsDTO.getCategoryCode());
            itemDTO.setGoodsKeywords(goodsDTO.getSearchKeywords());
            //计算资源单价(到位价反推、加价项计算)
            computeOrderItemPrice(orderDTO, itemDTO, resource);
            //由于需求没有挂牌单相关逻辑，默认填充合同新增的扩展字段
            if (specialGoodsResult.getData().getConcreteFlag() == 1) {
                OrderInfoExtDTO orderInfoExtDTO = orderDTO.getOrderInfoExtDTO();
                orderInfoExtDTO.setTransportType(TransportToolTypeEnum.ROAD_TRANSPORT.getCode());
                orderInfoExtDTO.setIfMixedTransport(0);
                orderInfoExtDTO.setVarietyId("");
                orderInfoExtDTO.setSigner(orderDTO.getReceiver());
                orderInfoExtDTO.setSignerPhone(orderDTO.getReceiverPhone());
                orderInfoExtDTO.setFirstArriveTime(new Date());
            }
            //仓库
            itemDTO.setStoreId(orderDTO.getStoreId());
            itemDTO.setStoreName(warehouse.getName());
            itemDTO.setStoreType(warehouse.getType());
            itemDTO.setStoreAddress(warehouse.getAddress());
            //设置支付有效期
            setPayTimeLimit(orderDTO, resource);
        }
        //设置卖家ID
        orderDTO.setSellerId(resourceDTO.getSellerId());
        //设置销售区域
        orderDTO.setSaleRegion1(resourceDTO.getSaleAreaCode());
        orderDTO.setSaleRegion2(resourceDTO.getSaleAreaCode2());
        orderDTO.setSaleRegion3(resourceDTO.getSaleAreaCode3());
        orderDTO.setSaleRegion4(resourceDTO.getSaleAreaCode4());
        orderDTO.setSaleRegion5(resourceDTO.getSaleAreaCode5());

        //添加销售区域名称
        orderDTO.setSaleRegionName1(resourceDTO.getSaleAreaName());
        orderDTO.setSaleRegionName2(resourceDTO.getSaleAreaName2());
        orderDTO.setSaleRegionName3(resourceDTO.getSaleAreaName3());
        orderDTO.setSaleRegionName4(resourceDTO.getSaleAreaName4());
        orderDTO.setSaleRegionName5(resourceDTO.getSaleAreaName5());

        //设置最小销售区域
        setSaleRegionPath(orderDTO, resourceDTO);
    }

    private static void setConvertRate(OrderItemDTO itemDTO, ResourceDTO resource) {
        itemDTO.setConvertRate(BigDecimal.ONE);
        itemDTO.setUnconvertRate(BigDecimal.ONE);
        if (!CsStringUtils.equals(resource.getPriceUnit(), itemDTO.getUnits())) {
            BigDecimal convertRate = resource.getConvertRate() == null ? BigDecimal.ONE : resource.getConvertRate();
            itemDTO.setConvertRate(convertRate);
            itemDTO.setUnconvertRate(BigDecimal.ONE.divide(convertRate, 2, BigDecimal.ROUND_HALF_UP));
        }
    }


    private GoodsDTO getGoodsDTO(OrderDTO orderDTO, ResourceDTO resource) {
        ItemResult<GoodsDTO> goodsResult = goodsService.getGoodsInfo(resource.getGoodsId());
        if (!goodsResult.isSuccess() || goodsResult.getData() == null) {
            throw new BizException(BasicCode.DATA_NOT_EXIST, "商品:" + resource.getGoodsId());
        }
        GoodsDTO goodsDTO = goodsResult.getData();
        if (CsStringUtils.isNotBlank(goodsDTO.getRefGoodsId())) {
            orderDTO.setProxyOrderType(ProxyOrderTypeEnum.SECONDARY.getCode());
        }
        return goodsDTO;
    }


    private ItemResult<SpecialGoodsAttributeDTO> getSpecialGoodsAttributeDTOItemResult(OrderDTO orderDTO, ResourceDTO resource, GoodsDTO goodsDTO) {
        ItemResult<SpecialGoodsAttributeDTO> specialGoodsResult =
                goodsService.getSpecialGoodsAttribute(resource.getGoodsId());
        if (!specialGoodsResult.isSuccess()) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "查询商品特殊属性失败");
        }
        if (specialGoodsResult.getData().getConcreteFlag() != 1) {
            //验证ERP地址
            validateErpAddress(orderDTO, resource, goodsDTO.getLogistics());
        }
        return specialGoodsResult;
    }


    private static Set<String> getSupportPayWaySet(ResourceDTO resource, Set<String> supportPayWaySet) {
        Set<String> payWaySet = Sets.newHashSet(resource.getPayWay());
        if (CollectionUtils.isEmpty(supportPayWaySet)) {
            supportPayWaySet = payWaySet;
        } else {
            //交集
            supportPayWaySet = Sets.intersection(supportPayWaySet, payWaySet);
        }
        return supportPayWaySet;
    }

    private static void setPayTimeLimit(OrderDTO orderDTO, ResourceDTO resource) {
        if (CsStringUtils.equals(resource.getPaydateType(), PaydateTypeEnum.PAYDATE_TYPE2.code()) &&
                resource.getPaydateLimit() != null) {
            Date timeLimit = DateUtils.addHours(new Date(), resource.getPaydateLimit().intValue());
            if (orderDTO.getPayTimeLimit() == null || orderDTO.getPayTimeLimit().after(timeLimit)) {
                orderDTO.setPayTimeLimit(timeLimit);
            }
        }
    }

    private static void setSaleRegionPath(OrderDTO orderDTO, ResourceDTO resourceDTO) {
        if (CsStringUtils.isNotBlank(resourceDTO.getSaleAreaCode5())) {
            orderDTO.setSaleRegionPath(resourceDTO.getSaleAreaCode5());
        } else if (CsStringUtils.isNotBlank(resourceDTO.getSaleAreaCode4())) {
            orderDTO.setSaleRegionPath(resourceDTO.getSaleAreaCode4());
        } else if (CsStringUtils.isNotBlank(resourceDTO.getSaleAreaCode3())) {
            orderDTO.setSaleRegionPath(resourceDTO.getSaleAreaCode3());
        } else if (CsStringUtils.isNotBlank(resourceDTO.getSaleAreaCode2())) {
            orderDTO.setSaleRegionPath(resourceDTO.getSaleAreaCode2());
        } else if (CsStringUtils.isNotBlank(resourceDTO.getSaleAreaCode())) {
            orderDTO.setSaleRegionPath(resourceDTO.getSaleAreaCode());
        }
    }

    @Override
    public void updateResourceNum(OrderDTO orderDTO, List<OrderItemDTO> orderItems, String operator) {
        //代理订单扣减库存
        if (orderDTO.getStockChangeMapDTO() != null) {
            StockChangeDTO stockChangeDTO = new StockChangeDTO();
            BeanUtils.copyProperties(orderDTO.getStockChangeMapDTO(), stockChangeDTO);
            if (stockChangeDTO.getOrderLevel() == 2) {
                stockChangeDTO.setBuyerName(orderDTO.getSellerName());
            }
            log.info("updateResourceNum_stockChange:" + JSON.toJSONString(stockChangeDTO));
            stockService.stockChange(stockChangeDTO);
            return;
        }
        HashMap<String, BigDecimal> map = Maps.newHashMap();
        for (OrderItemDTO item : orderItems) {
            map.put(item.getResourceId(), item.getItemQuantity().multiply(item.getConvertRate()));
        }
        resourceNumTCCService.updateResourceNumForCreateOrder(map, operator);
    }

    @Override
    public void cancelOrder(OrderCancelDO orderCancelDO) {
        super.cancelOrder(orderCancelDO);
        //更新合同资源数量
        OrderDTO orderDTO = orderQueryService.getOrderInfo(orderCancelDO.getOrderId());
        //生成库存扣减对象
        StockChangeDTO stockChangeDTO = new StockChangeDTO();
        if (ifAllocationGoodsStock(orderDTO, stockChangeDTO)) {
            log.info("cancelOrder_stockChange:" + JSON.toJSONString(stockChangeDTO));
            stockService.stockChange(stockChangeDTO);
            return;
        }
        HashMap<String, BigDecimal> map = Maps.newHashMap();
        for (OrderItemDTO item : orderDTO.getOrderItems()) {
            map.put(item.getResourceId(), ArithUtils.subtract(Boolean.FALSE,
                    ArithUtils.multiply(item.getItemSendQuantity(), item.getConvertRate()),
                    ArithUtils.multiply(item.getItemQuantity(), item.getConvertRate())));
        }
        resourceNumTCCService.updateResourceNumForCreateOrder(map, orderCancelDO.getOperator());
    }

    @Override
    public void closeOrder(OrderCloseDO orderCloseDO) {
        super.closeOrder(orderCloseDO);
        //更新合同资源数量
        OrderDTO orderDTO = orderQueryService.getOrderInfo(orderCloseDO.getOrderId());
        //生成库存扣减对象
        StockChangeDTO stockChangeDTO = new StockChangeDTO();
        if (ifAllocationGoodsStock(orderDTO, stockChangeDTO)) {
            log.info("closeOrder_stockChange:" + JSON.toJSONString(stockChangeDTO));
            stockService.stockChange(stockChangeDTO);
            return;
        }
        HashMap<String, BigDecimal> map = Maps.newHashMap();
        for (OrderItemDTO item : orderDTO.getOrderItems()) {
            map.put(item.getResourceId(), ArithUtils.subtract(Boolean.FALSE,
                    ArithUtils.multiply(item.getItemSendQuantity(), item.getConvertRate()),
                    ArithUtils.multiply(item.getItemQuantity(), item.getConvertRate())));
        }
        resourceNumTCCService.updateResourceNumForCreateOrder(map, orderCloseDO.getOperator());
    }

    @Override
    public void completeOrder(OrderCompleteDO orderCompleteDO) {
        super.completeOrder(orderCompleteDO);
        //更新合同资源数量
        OrderDTO orderDTO = orderQueryService.getOrderInfo(orderCompleteDO.getOrderId());
        OrderItemDTO orderItemDTO = orderDTO.getOrderItems().get(0);
        BigDecimal returnNum = ArithUtils.subtract(orderItemDTO.getItemQuantity(), orderItemDTO.getItemSendQuantity());
        //代理订单扣减库存,返还数量小于0时直接忽略
        StockChangeDTO stockChangeDTO = new StockChangeDTO();
        if (returnNum.compareTo(BigDecimal.ZERO) > 0 && ifAllocationGoodsStock(orderDTO, stockChangeDTO)) {
            log.info("completeOrder_stockChange:" + JSON.toJSONString(stockChangeDTO));
            stockService.stockChange(stockChangeDTO);
        }
    }
}
