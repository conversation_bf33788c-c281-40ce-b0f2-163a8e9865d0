package com.ecommerce.order.fsm.service.impl;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.order.api.dto.MonitorJudgeCondDTO;
import com.ecommerce.order.api.dto.MonitorJudgeResDTO;
import com.ecommerce.order.biz.fsm.IMonitorJudgeBiz;
import com.ecommerce.order.fsm.service.IMonitorJudgeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @Auther: colu
 * @Date: 2020-03-05 14:50
 * @Description: MonitorJudgeService
 */
@RequiredArgsConstructor
@Slf4j
@Service("monitorJudgeService")
public class MonitorJudgeService implements IMonitorJudgeService {

    private final IMonitorJudgeBiz monitorJudgeBiz;

    @Override
    public ItemResult<MonitorJudgeResDTO> monitorJudge(MonitorJudgeCondDTO monitorJudgeCondDTO) {
        return new ItemResult<>(monitorJudgeBiz.monitorJudge(monitorJudgeCondDTO));
    }

}