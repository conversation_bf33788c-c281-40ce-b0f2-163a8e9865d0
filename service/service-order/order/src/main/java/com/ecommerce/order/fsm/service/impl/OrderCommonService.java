package com.ecommerce.order.fsm.service.impl;

import com.alibaba.fastjson.JSON;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.exception.DistributeLockException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.common.service.common.BaseBiz;
import com.ecommerce.common.service.lock.RedisLockService;
import com.ecommerce.common.utils.ArithUtils;
import com.ecommerce.common.utils.DateUtil;
import com.ecommerce.goods.api.dto.SpecialGoodsAttributeDTO;
import com.ecommerce.goods.api.service.IGoodsService;
import com.ecommerce.logistics.api.dto.shipbill.CloseShipBillDTO;
import com.ecommerce.logistics.api.service.IShipBillService;
import com.ecommerce.member.api.dto.member.config.MemberConfigDTO;
import com.ecommerce.member.api.dto.member.enums.MemberPlatform;
import com.ecommerce.member.api.service.IMemberConfigService;
import com.ecommerce.order.api.dto.*;
import com.ecommerce.order.api.dto.base.PageQuery;
import com.ecommerce.order.api.enums.*;
import com.ecommerce.order.biz.IOrderPayinfoBiz;
import com.ecommerce.order.biz.fsm.IOrderInfoBiz;
import com.ecommerce.order.biz.fsm.IOrderItemBiz;
import com.ecommerce.order.biz.fsm.ITakeInfoBiz;
import com.ecommerce.order.dao.dto.OrderCompleteDO;
import com.ecommerce.order.dao.dto.OrderInfoQueryDO;
import com.ecommerce.order.dao.vo.OrderInfo;
import com.ecommerce.order.dao.vo.OrderItem;
import com.ecommerce.order.fsm.domain.StatusModelDO;
import com.ecommerce.order.fsm.even.OrderChangeEventEnum;
import com.ecommerce.order.fsm.manager.OrderStateMachineEventManager;
import com.ecommerce.order.fsm.service.*;
import com.ecommerce.order.fsm.status.OrderTypeEnum;
import com.ecommerce.order.service.IOrderAdjustService;
import com.ecommerce.order.service.IOrderPayinfoService;
import com.ecommerce.pay.api.v2.enums.ChannelCodeEnum;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import com.ecommerce.common.utils.CsStringUtils;
import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;

/**
 *
 * <AUTHOR>
 * Date: Create in 上午11:01 20/7/6
 */
@Slf4j
@Service
public class OrderCommonService implements IOrderCommonService {

    public static final String TRY_AUTO_CLOSE_ORDER = "tryAutoCloseOrder_";
    public static final String SYSTEM = "system";

    @Autowired
    private IOrderInfoBiz orderInfoBiz;
    @Autowired
    private ITakeInfoBiz takeInfoBiz;
    @Autowired
    private IOrderItemBiz orderItemBiz;
    @Autowired
    private IMemberConfigService memberConfigService;
    @Autowired
    private IOrderPaymentService orderPaymentService;
    @Autowired
    private IOrderPayinfoService orderPayInfoService;
    @Autowired
    private IOrderPayinfoBiz orderPayInfoBiz;
    @Autowired
    private IOrderQueryService orderQueryService;
    @Autowired
    private IGoodsService goodsService;
    @Autowired
    private RedisLockService redisLockService;
    @Autowired
    private IOrderComputeService orderComputeService;
    @Autowired
    private OrderStateMachineEventManager orderStateMachineEventManager;
    @Autowired
    private IOrderAdjustService orderAdjustService;
    @Autowired
    private IShipBillService shipBillService;
    @Autowired
    private OrderCommonService commonService;

    /**
     * 订单自动完成校验
     * @return 校验结果
     */
    public Boolean orderAutoCompleteChecking(OrderDTO orderDTO) {
        Boolean autoCompleteFlag = Boolean.TRUE;
        List<OrderItemDTO> orderItemList = orderDTO.getOrderItems();
        for (OrderItemDTO orderItem : orderItemList) {
            BigDecimal distanceThreshold = ArithUtils.subtract(orderItem.getItemQuantity(), orderItem.getItemSendQuantity(), orderItem.getItemRefundQuantity());
            //发货数量超过订单下单量直接通过
            if (distanceThreshold.compareTo(BigDecimal.ZERO) <= 0) break;
            //判断是否有自动完成容差
            if (CsStringUtils.isBlank(orderItem.getAutoCompleteType())) {
                autoCompleteFlag = Boolean.FALSE;
                log.info("判断是否有自动完成容差 orderAutoCompleteChecking: false,orderCode: {}",orderDTO.getOrderCode());
                break;
            }
            //浮动比例,转换百分比数值
            if ((CsStringUtils.equals(orderItem.getAutoCompleteType(), AutoCompleteTypeEnum.FLOAT_SCALE.getCode()))
                && (distanceThreshold.divide(orderItem.getItemQuantity(), 6, RoundingMode.HALF_UP)
                        .compareTo(orderItem.getAutoCompleteThreshold().divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP)) > 0)) {
                    autoCompleteFlag = Boolean.FALSE;
                    log.info("浮动比例,转换百分比数值 orderAutoCompleteChecking: false,orderCode: {}",orderDTO.getOrderCode());
                    break;
            }
            //固定值,按计量单位计算
            if ((CsStringUtils.equals(orderItem.getAutoCompleteType(), AutoCompleteTypeEnum.FIXED_NUMERICAL.getCode()))
                && (ArithUtils.multiply(distanceThreshold, orderItem.getConvertRate())
                        .compareTo(orderItem.getAutoCompleteThreshold()) > 0)) {
                    autoCompleteFlag = Boolean.FALSE;
                    log.info("固定值,按计量单位计算 orderAutoCompleteChecking: false,orderCode: {}",orderDTO.getOrderCode());
                    break;
                }
        }

        return autoCompleteFlag;
    }

    /**
     * 触发订单自动完成
     * @param orderId 订单ID
     */
    @Transactional
    @Override
    public void triggerOrderAutoEnd(String orderId) {
        if (CsStringUtils.isBlank(orderId)) {
            throw new BizException(BasicCode.INVALID_PARAM, "订单ID");
        }
        OrderDTO orderDTO = orderQueryService.getOrderInfo(orderId);
        if (orderDTO == null || CollectionUtils.isEmpty(orderDTO.getOrderItems())) {
            throw new BizException(BasicCode.DATA_NOT_EXIST, "订单:" + orderId);
        }
        Set<String> finalOrderStatus = Sets.newHashSet(
                OrderStatusEnum.COMPLETED.code(),
                OrderStatusEnum.CLOSED.code(),
                OrderStatusEnum.CANCEL.code()
        );
        //订单已处于最终状态
        if (finalOrderStatus.contains(orderDTO.getOrderStatus())) return;
        //判断订单下所有的发货单是否处于最终状态
        List<TakeInfoDTO> takeInfoList = takeInfoBiz.getTakeInfoDetailsByOrderId(orderId);
        //没有发货时不触发自动完成
        if (CollectionUtils.isEmpty(takeInfoList)){
            log.info("没有发货时不触发自动完成 orderId:{}",orderId);
            return;
        }
        log.info("triggerOrderAutoEnd_takeInfoList:" + JSON.toJSONString(takeInfoList));
        Set<String> finalStatusSet = Sets.newHashSet(
                TakeStatus.CLOSED.getCode(),
                TakeStatus.CANCELED.getCode(),
                TakeStatus.FINISHED.getCode());
        for (TakeInfoDTO takeInfo : takeInfoList) {
            if (!finalStatusSet.contains(takeInfo.getTakeStatus())){
                log.info("有发货单不是最终状态时不触发自动完成 orderId:{},takeId:{},takeStatus:{}",orderId,takeInfo.getTakeId(),takeInfo.getTakeStatus());
                return;
            }
        }
        //获取商品属性
        ItemResult<SpecialGoodsAttributeDTO> goodsResult = goodsService.getSpecialGoodsAttribute(
                orderDTO.getOrderItems().get(0).getGoodsId());
        if (!goodsResult.isSuccess()) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "查询商品特殊属性失败");
        }
        Function<OrderDTO, Boolean> adjustStatusFunction = o -> {
            OrderInfo orderInfo = new OrderInfo();
            orderInfo.setOrderId(orderDTO.getOrderId());
            //计算台班费
            BigDecimal machineCost = orderComputeService.computeOrderMachineCost(orderDTO);
            orderInfo.setRealtimeOthersAmount(orderDTO.getRealtimeOthersAmount() == null ?
                    machineCost : orderDTO.getRealtimeOthersAmount().add(machineCost));
            orderInfo.setRealtimeOrderAmount(orderDTO.getRealtimeOrderAmount() == null ?
                    machineCost : orderDTO.getRealtimeOrderAmount().add(machineCost));
            orderInfo.setAdjustStatus(AdjustStatusEnum.CONFIRMING.getCode());
            orderInfoBiz.updateSelective(orderInfo);
            //发起确认流程
            orderAdjustService.confirmOrderAdjust(orderDTO.getOrderId(), SYSTEM);
            return Boolean.TRUE;
        };
        log.info("triggerOrderAutoEnd_orderItemList:" + JSON.toJSONString(orderDTO.getOrderItems()));
        Boolean autoCompleteFlag = orderAutoCompleteChecking(orderDTO);
        if (Boolean.TRUE.equals(autoCompleteFlag)) {
            doConcreteFlagLogic(orderId, goodsResult, adjustStatusFunction, orderDTO);
            return;
        }
        //非触发自动关闭
        triggerTryAutoCloseOrder(orderId, goodsResult, orderDTO, adjustStatusFunction);
    }

    private void doConcreteFlagLogic(String orderId, ItemResult<SpecialGoodsAttributeDTO> goodsResult, Function<OrderDTO, Boolean> adjustStatusFunction, OrderDTO orderDTO) {
        if (goodsResult.getData().getConcreteFlag() == 1) {
            adjustStatusFunction.apply(orderDTO);
        } else {
            commonService.tryAutoCompleteOrder(orderId);
            //一级订单完成后,需要锁定二级订单状态
            if (CsStringUtils.equals(orderDTO.getProxyOrderType(), ProxyOrderTypeEnum.PRIMARY.getCode())) {
                //设置订单失效时间
                OrderInfo orderInfo = new OrderInfo();
                orderInfo.setOrderId(orderId);
                orderInfo.setTakeTimeLimit(new Date(System.currentTimeMillis() - 1000));
                BaseBiz.setOperInfo(orderId, MemberPlatform.SYSTEM_OPERATOR.getId(), false);
                orderInfoBiz.updateSelective(orderInfo);
                orderDTO.setTakeTimeLimit(new Date(System.currentTimeMillis() - 1000));
            }
        }
    }

    private void triggerTryAutoCloseOrder(String orderId, ItemResult<SpecialGoodsAttributeDTO> goodsResult, OrderDTO orderDTO, Function<OrderDTO, Boolean> adjustStatusFunction) {
        if (goodsResult.getData().getConcreteFlag() != 1) {
            Optional.ofNullable(orderDTO.getTakeTimeLimit())
                    .filter(takeTimeLimit -> takeTimeLimit.before(new Date()))
                    .ifPresent(takeLimitTime -> tryAutoCloseOrder(orderId, "失效订单自动关闭退款",TakeCloseType.FAILURE_ORDER_CLOSE));
        } else {
            //变更为费用确认状态
            Optional.ofNullable(orderDTO.getTakeTimeLimit())
                    .filter(takeTimeLimit -> takeTimeLimit.before(new Date()))
                    .ifPresent(takeLimitTime -> adjustStatusFunction.apply(orderDTO));
        }
    }

    /**
     * 尝试自动完成订单
     * @param orderId 订单ID
     */
    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED, rollbackFor = Exception.class)
    public void tryAutoCompleteOrder(String orderId) {
        String identifier = "";
        try {
            identifier = redisLockService.lockFast("tryAutoCompleteOrder_" + orderId);
            log.info("自动完成订单触发orderId:" + orderId);
            OrderInfo orderInfo = orderInfoBiz.getOrderInfoById(orderId);
            //状态校验
            Set<String> finalOrderStatus = Sets.newHashSet(
                    OrderStatusEnum.CANCEL.getCode(),
                    OrderStatusEnum.CLOSED.getCode(),
                    OrderStatusEnum.COMPLETED.getCode(),
                    OrderStatusEnum.ERP_COMPLETING.getCode());
            if (finalOrderStatus.contains(orderInfo.getOrderStatus())) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "订单已结束，请勿重复操作");
            }
            OrderRefundDTO orderRefund = orderPaymentService.generateOrderRefundInfo(orderId);
            //进行退款操作
            if (orderRefund != null) {
                //订单自动完成退款
                orderRefund.setRefundType(RefundTypeEnum.AUTO_COMPLETE_REFUND.getCode());
                orderRefund.setMome("自动完成订单退款");
                orderRefund.setCloseReason("自动完成订单退款");
                orderRefund.setCreateUser(MemberPlatform.SYSTEM_OPERATOR.getId());
                orderRefund.setUpdateUser(MemberPlatform.SYSTEM_OPERATOR.getId());
                //保存退款信息
                orderPayInfoBiz.saveOrUpdateOrderRefund(orderRefund);
                generatePayInfoAndRefund(orderRefund);
            }
            //线下支付触发订单线下退款事件
            if (orderRefund != null && CsStringUtils.equals(ChannelCodeEnum.OFFLINE.getCode(), orderRefund.getPayInfoWay())) {
                OrderInfo saveOrder = new OrderInfo();
                saveOrder.setOrderId(orderRefund.getOrderId());
                BaseBiz.setOperInfo(saveOrder, orderRefund.getCreateUser(), false);
                StatusModelDO statusModelDO = new StatusModelDO();
                statusModelDO.setBizId(orderRefund.getOrderId());
                OrderStatusEnum orderStatusEnum = OrderStatusEnum.getByCode(orderInfo.getOrderStatus());
                if (orderStatusEnum == null) {
                    throw new BizException(BasicCode.UNDEFINED_ERROR, "无效的订单状态");
                }
                statusModelDO.setCurrentState(orderStatusEnum.getCode());
                statusModelDO.addParam("saveOrder", saveOrder);
                orderStateMachineEventManager.sendStatusChangeEvent(statusModelDO, OrderChangeEventEnum.ORDER_REFUND);
            } else {
                OrderCompleteDO orderCompleteDO = new OrderCompleteDO();
                orderCompleteDO.setOrderId(orderId);
                orderCompleteDO.setOperator(MemberPlatform.SYSTEM_OPERATOR.getId());
                OrderTypeEnum orderTypeEnum = OrderTypeEnum.getByCode(orderInfo.getOrderType());
                if (orderTypeEnum == null) {
                    throw new BizException(BasicCode.UNDEFINED_ERROR, "无效的订单类型");
                }
                OrderServiceFactory.getOrderHandlerService(orderTypeEnum.getBizType()).completeOrder(orderCompleteDO);
            }
        } catch (Exception e) {
            log.error("订单自动完成失败，请排除异常后手动操作！:" + JSON.toJSONString(e));
        } finally {
            if (CsStringUtils.isNotEmpty(orderId) && CsStringUtils.isNotEmpty(identifier)) {
                redisLockService.unlock("tryAutoCompleteOrder_" + orderId, identifier);
            }
        }
    }

    private void generatePayInfoAndRefund(OrderRefundDTO orderRefund) {
        try {
            //生成支付信息
            orderPayInfoService.generatePayInfoAndRefund(orderRefund);
        } catch (Exception e) {
            log.error("triggerOrderAutoEnd ===退款失败===" + JSON.toJSONString(e));
            orderRefund.setRefundStatus(RefundStatusEnum.FAILED.getCode());
            orderPayInfoBiz.saveOrUpdateOrderRefund(orderRefund);
        }
    }

    @Override
    public void doOrderRefund(OrderInfo orderInfo, BigDecimal refundGoodsAmount, BigDecimal refundLogisticsAmount, BigDecimal refundOtherAmount, String refundReason)
    {
        OrderRefundDTO orderRefund = orderPaymentService.generateOrderRefundInfo(orderInfo, refundGoodsAmount, refundLogisticsAmount, refundOtherAmount);
        orderRefund.setRefundType(RefundTypeEnum.GOODS_REFUND.getCode());
        orderRefund.setMome(refundReason);
        orderRefund.setCloseReason(refundReason);
        orderRefund.setCreateUser(MemberPlatform.SYSTEM_OPERATOR.getId());
        orderRefund.setUpdateUser(MemberPlatform.SYSTEM_OPERATOR.getId());
        //保存退款信息
        orderPayInfoBiz.saveOrUpdateOrderRefund(orderRefund);

        try
        {
            //生成支付信息
            orderPayInfoService.generatePayInfoAndRefund(orderRefund);
        }
        catch(Exception e)
        {
            log.error("doOrderRefund ===退款失败===" + JSON.toJSONString(e));
            orderRefund.setRefundStatus(RefundStatusEnum.FAILED.getCode());
            orderPayInfoBiz.saveOrUpdateOrderRefund(orderRefund);
        }

        //线下支付触发订单线下退款事件
        if (CsStringUtils.equals(ChannelCodeEnum.OFFLINE.getCode(), orderRefund.getPayInfoWay()))
        {
            OrderInfo saveOrder = new OrderInfo();
            saveOrder.setOrderId(orderRefund.getOrderId());
            BaseBiz.setOperInfo(saveOrder, orderRefund.getCreateUser(), false);
            StatusModelDO statusModelDO = new StatusModelDO();
            statusModelDO.setBizId(orderRefund.getOrderId());
            OrderStatusEnum orderStatusEnum = OrderStatusEnum.getByCode(orderInfo.getOrderStatus());
            if(orderStatusEnum == null)
            {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "无效的订单状态");
            }
            statusModelDO.setCurrentState(orderStatusEnum.getCode());
            statusModelDO.addParam("saveOrder", saveOrder);
            orderStateMachineEventManager.sendStatusChangeEvent(statusModelDO, OrderChangeEventEnum.ORDER_REFUND);
        }
    }

    /**
     * 跨越订单参数转换
     */
    private void convertCrossMonthOrderParam(CrossMonthCloseTriggerDTO crossMonthCloseTriggerDTO,
                                             OrderInfoQueryDO orderInfoQueryDO) {
        Date currentDate = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(currentDate);
        //前一个月
        calendar.add(Calendar.MONTH, -1);
        Date previousDate = calendar.getTime();
        String formatPattern = "yyyy-MM-dd";
        orderInfoQueryDO.setStartDate(DateUtil.parse(DateUtil.getFirstDayOfMonth(previousDate), formatPattern));
        orderInfoQueryDO.setEndDate(DateUtil.parse(DateUtil.getFirstDayOfMonth(currentDate), formatPattern));
        Optional<CrossMonthCloseTriggerDTO> crossOptional = Optional.ofNullable(crossMonthCloseTriggerDTO)
                .filter(param -> CsStringUtils.equals(param.getTriggerType(), "manual"));
        //设置开始时间
        crossOptional.map(CrossMonthCloseTriggerDTO::getStartDate)
                .ifPresent(startDate -> orderInfoQueryDO.setStartDate(DateUtil.parse(startDate, formatPattern)));
        //设置结束时间
        crossOptional.map(CrossMonthCloseTriggerDTO::getEndDate)
                .ifPresent(endDate -> {
                    Date end = DateUtil.parse(endDate, formatPattern);
                    calendar.setTime(end);
                    calendar.add(Calendar.DATE, 1);
                    orderInfoQueryDO.setEndDate(calendar.getTime());
                });
    }

    /**
     * 关闭跨月订单
     * @param crossMonthCloseTriggerDTO 跨月订单关闭对象
     */
    @Override
    public void closeCrossMonthOrder(CrossMonthCloseTriggerDTO crossMonthCloseTriggerDTO) {
        OrderInfoQueryDO orderInfoQueryDO = new OrderInfoQueryDO();
        //转换跨越订单参数
        convertCrossMonthOrderParam(crossMonthCloseTriggerDTO, orderInfoQueryDO);
        //待支付、待发货、发货中
        orderInfoQueryDO.setStatusList(Lists.newArrayList(
                OrderStatusEnum.WAIT_DELIVERED.getCode(),
                OrderStatusEnum.IN_DELIVERY.getCode()));
        //查询订单信息
        int pageNum = 1;
        PageQuery<OrderInfoQueryDO> pageQuery = new PageQuery<>();
        pageQuery.setPageSize(20);
        pageQuery.setQueryDTO(orderInfoQueryDO);
        PageData<OrderInfo> pageData;
        Map<String, Boolean> sellerAutoCloseMap = Maps.newHashMap();
        do {
            pageQuery.setPageNum(pageNum);
            pageData = orderInfoBiz.queryOrderInfoListByCondition(pageQuery);
            if (CollectionUtils.isEmpty(pageData.getList())) {
                return;
            }
            //添加卖家参数过滤，判断是否开启开关
            for (OrderInfo orderInfo : pageData.getList()) {
                if (sellerAutoCloseMap.get(orderInfo.getSellerId()) == null) {
                    MemberConfigDTO memberConfigDTO = memberConfigService.findByMemberIdAndKeyCode(
                            orderInfo.getSellerId(), MemberOrderConfigEnum.CROSS_MONTH_AUTO_CLOSE.getCode());
                    Optional<Boolean> optional = Optional.ofNullable(memberConfigDTO)
                            .map(MemberConfigDTO::getValue)
                            .map(value ->  Objects.equals(Integer.parseInt(value),1));
                    sellerAutoCloseMap.put(orderInfo.getSellerId(), optional.orElse(Boolean.FALSE));
                }
                if (sellerAutoCloseMap.get(orderInfo.getSellerId())) {
                    //尝试自动关闭订单
                    tryAutoCloseOrder(orderInfo.getOrderId(), "跨月订单自动关闭退款", TakeCloseType.ACROSS_MONTH_CLOSE);
                }
            }
            ++pageNum;
        } while (pageNum <= pageData.getPages());
    }

    /**
     * 尝试自动关闭订单
     * @param orderId 订单ID
     * @param closeReason 关闭原因
     */
    @Override
    public boolean tryAutoCloseOrder(String orderId, String closeReason, TakeCloseType takeCloseType) {
        boolean rest = true;
        String identifier = "";
        try {
            identifier = redisLockService.lockFast(TRY_AUTO_CLOSE_ORDER + orderId);
            //直接关闭订单
            try {
                log.info("尝试直接关闭订单orderId : " + orderId);
                OrderRefundDTO orderRefundDTO = new OrderRefundDTO();
                orderRefundDTO.setOrderId(orderId);
                orderRefundDTO.setRefundType(RefundTypeEnum.REFUND.getCode());
                orderRefundDTO.setOperatorTypeEnum(OperatorTypeEnum.SELLER.code());
                orderRefundDTO.setMome(closeReason);
                orderRefundDTO.setCloseReason(closeReason);
                orderRefundDTO.setCreateUser(MemberPlatform.SYSTEM_OPERATOR.getId());
                orderRefundDTO.setUpdateUser(MemberPlatform.SYSTEM_OPERATOR.getId());
                orderPaymentService.closeOrderAndDoRefund(orderRefundDTO);
                return true;
            } catch (Exception e) {
                log.info("自动关闭订单失败 orderId={},{}", orderId, e.getMessage());
                log.error(e.getMessage(), e);

                rest = false;
            }

            //锁定当前订单
            updateSelective(orderId, closeReason, takeCloseType);
        } catch (DistributeLockException e) {
            log.info("尝试直接关闭订单orderId: {},{}", orderId, e.getMessage());
            log.error(e.getMessage(), e);
        } finally {
            if (CsStringUtils.isNotEmpty(orderId) && CsStringUtils.isNotEmpty(identifier)) {
                redisLockService.unlock(TRY_AUTO_CLOSE_ORDER + orderId, identifier);
            }
        }

        return rest;
    }

    private void updateSelective(String orderId, String closeReason, TakeCloseType takeCloseType) {
        try {
            log.info("尝试锁定当前订单，设置失效时间为当前时间orderId:" + orderId);
            takeInfoBiz.closeOrderTakeInfo(orderId, takeCloseType);
            //设置订单失效时间
            OrderInfo orderInfo = new OrderInfo();
            orderInfo.setOrderId(orderId);
            //先设置关闭原因
            orderInfo.setBuyerCancelReason(closeReason);
            BaseBiz.setOperInfo(orderId, MemberPlatform.SYSTEM_OPERATOR.getId(), false);
            orderInfoBiz.updateSelective(orderInfo);
        } catch (Exception e) {
            log.info("锁定当前订单失败！orderId={},{}", orderId, e.getMessage());
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 关闭提货有效期失效订单
     */
    @Override
    public void closeExpireOrder(TakeTimeLimitTriggerDTO takeTimeLimitTriggerDTO) {
        log.info("关闭提货有效期失效订单: {}", JSON.toJSONString(takeTimeLimitTriggerDTO));

        OrderInfoQueryDO orderInfoQueryDO = new OrderInfoQueryDO();
        if (CsStringUtils.isNotBlank(takeTimeLimitTriggerDTO.getSellerId())) {
            orderInfoQueryDO.setSellerId(takeTimeLimitTriggerDTO.getSellerId());
        }
        //待支付、待发货、发货中
        orderInfoQueryDO.setStatusList(Lists.newArrayList(OrderStatusEnum.WAIT_DELIVERED.getCode(), OrderStatusEnum.IN_DELIVERY.getCode()));

        if(takeTimeLimitTriggerDTO.getTimeLen() == null)
        {
            String formatPattern = "yyyy-MM-dd HH:mm:ss";
            //默认只处理前一天提货有效期过期数据
            if (CsStringUtils.isNotEmpty(takeTimeLimitTriggerDTO.getStartTakeTimeLimit()))
            {
                orderInfoQueryDO.setStartTakeTimeLimit(DateUtil.parse(takeTimeLimitTriggerDTO.getStartTakeTimeLimit() + " 00:00:00", formatPattern));
            }

            if (CsStringUtils.isEmpty(takeTimeLimitTriggerDTO.getEndTakeTimeLimit()))
            {
                // 今天过期的暂不处理
                Date date = new Date(new Date().getTime() - 86400000L);
                takeTimeLimitTriggerDTO.setEndTakeTimeLimit(DateUtil.format(date, "yyyy-MM-dd"));
            }
            orderInfoQueryDO.setEndTakeTimeLimit(DateUtil.parse(takeTimeLimitTriggerDTO.getEndTakeTimeLimit() + " 23:59:59", formatPattern));
        }
        else
        {
            // 处理5天前的数据
            orderInfoQueryDO.setStartTakeTimeLimit(null);
            orderInfoQueryDO.setEndTakeTimeLimit(null);
            orderInfoQueryDO.setStartDate(new Date(new Date().getTime() - takeTimeLimitTriggerDTO.getTimeLen() * 86400000L));
            orderInfoQueryDO.setEndDate(new Date(new Date().getTime() - 5 * 86400000L));
        }

        orderInfoQueryDO.setOrderCode(takeTimeLimitTriggerDTO.getOrderCode());

        //查询订单信息
        Integer pageNum = 1;
        PageQuery<OrderInfoQueryDO> pageQuery = new PageQuery<>();
        pageQuery.setPageSize(20);
        pageQuery.setQueryDTO(orderInfoQueryDO);
        PageData<OrderInfo> pageData;
        log.info("closeExpireOrder_begin:" + JSON.toJSONString(orderInfoQueryDO));
        do {
            pageQuery.setPageNum(pageNum);
            pageData = orderInfoBiz.queryOrderInfoListByCondition(pageQuery);
            if (CollectionUtils.isEmpty(pageData.getList())) {
                log.info("closeExpireOrder_begin query result is empty.");
                return;
            }
            List<OrderInfo> orderInfoList = pageData.getList();
            orderInfoList.stream()
                //只处理一级订单或非背靠背订单，二级订单会被对应的一级订单联动关闭
                    .filter(item -> !CsStringUtils.equals(item.getProxyOrderType(), ProxyOrderTypeEnum.SECONDARY.getCode()))
                .forEach(orderInfo -> {
                    boolean isClosed = tryAutoCloseOrder(orderInfo.getOrderId(), "提货有效期到期自动关闭退款",TakeCloseType.TIME_LIMIT_CLOSE);
                    log.info("关闭提货有效期失效订单: {},{}", orderInfo.getOrderCode(), isClosed);
                    if(!isClosed)
                    {
                        CloseShipBillDTO billDTO = new CloseShipBillDTO();
                        billDTO.setOrderCode(orderInfo.getOrderCode());
                        billDTO.setCloseReason("订单提货有效期到期自动关闭");

                        log.info("订单提货有效期到期自动关闭失败，尝试关闭订单下可以关闭的运单: {}", JSON.toJSONString(billDTO));
                        shipBillService.discardShipBill(billDTO);
                    }
                });
            ++pageNum;
        } while (pageNum <= pageData.getPages());
    }

    /**
     * 尝试手动关闭订单
     * @param orderRefundDTO 退款对象
     */
    @Override
    public String tryManualCloseOrder(OrderRefundDTO orderRefundDTO) {
        if (CsStringUtils.isBlank(orderRefundDTO.getOrderId())) {
            throw new BizException(BasicCode.INVALID_PARAM, "订单ID");
        }
        String orderId = orderRefundDTO.getOrderId();
        String identifier = "";
        try {
            //与自动触发关闭操作共享分布式锁防止重复执行
            identifier = redisLockService.lockFast(TRY_AUTO_CLOSE_ORDER + orderId);
            //直接关闭订单
            String x = closeOrderAndDoRefund(orderRefundDTO, orderId);
            if (x != null) return x;
            //锁定当前订单
            return closeOrderTakeInfo(orderRefundDTO, orderId);
        } catch (DistributeLockException e) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "订单正在执行关闭操作，请刷新后重试");
        } finally {
            if (CsStringUtils.isNotEmpty(orderId) && CsStringUtils.isNotEmpty(identifier)) {
                redisLockService.unlock(TRY_AUTO_CLOSE_ORDER + orderId, identifier);
            }
        }
    }

    @Nullable
    private String closeOrderAndDoRefund(OrderRefundDTO orderRefundDTO, String orderId) {
        try {
            log.info("尝试直接关闭订单orderId:" + orderId);
            orderPaymentService.closeOrderAndDoRefund(orderRefundDTO);
            return "";
        } catch (Exception e) {
            log.error("直接关闭订单失败！orderId={}，{}", orderId, e.getMessage());
        }
        return null;
    }

    @NotNull
    private String closeOrderTakeInfo(OrderRefundDTO orderRefundDTO, String orderId) {
        try {
            log.info("尝试锁定当前订单，设置失效时间为当前时间orderId:" + orderId);
            if (CsStringUtils.equals(orderRefundDTO.getOperatorTypeEnum(), OperatorTypeEnum.BUYER.getCode())) {
                takeInfoBiz.closeOrderTakeInfo(orderId,TakeCloseType.BUYER_CLOSE);
            } else if (CsStringUtils.equals(orderRefundDTO.getOperatorTypeEnum(), OperatorTypeEnum.SELLER.getCode()) ||
                    CsStringUtils.equals(orderRefundDTO.getOperatorTypeEnum(), OperatorTypeEnum.ERP.getCode())) {
                takeInfoBiz.closeOrderTakeInfo(orderId,TakeCloseType.SELLER_CLOSE);
            } else if (CsStringUtils.equals(orderRefundDTO.getOperatorTypeEnum(), OperatorTypeEnum.PLATFORM.getCode())) {
                takeInfoBiz.closeOrderTakeInfo(orderId,TakeCloseType.PLATFORM_CLOSE);
            }else {
                if (CsStringUtils.isBlank(orderRefundDTO.getOperatorTypeEnum())) {
                    throw new BizException(BasicCode.CUSTOM_ERROR,"操作人类型枚举不可为空");
                }
                throw new BizException(BasicCode.CUSTOM_ERROR,"操作人类型枚举不正确");
            }
            //设置订单失效时间
            OrderInfo orderInfo = new OrderInfo();
            orderInfo.setOrderId(orderId);
            //先设置关闭原因
            orderInfo.setBuyerCancelReason(orderRefundDTO.getCloseReason());
            orderInfo.setTakeTimeLimit(new Date(System.currentTimeMillis() - 1000));
            BaseBiz.setOperInfo(orderId, orderRefundDTO.getCreateUser(), false);
            orderInfoBiz.updateSelective(orderInfo);
            return "PENGDING";
        } catch (Exception e) {
            log.error("锁定当前订单失败！orderId={}，{}", orderId, e);
            throw new BizException(BasicCode.UNDEFINED_ERROR, e.getMessage());
        }
    }

    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED, rollbackFor = Exception.class)
    public void executeOrderPriceAdjust(String changeOrderId, List<OrderItemDTO> orderItemList, Map<String, BigDecimal> mortarAdjustMap) {
        //分布式锁防止执行中的订单在改价时与运单出站或签收冲突导致数据异常
        String identifier = "";
        try {
            identifier = redisLockService.lock(changeOrderId);
            OrderInfo orderInfo = orderInfoBiz.getOrderInfoById(changeOrderId);
            orderInfo.setOrderId(changeOrderId);
            if (mortarAdjustMap == null) {
                final BigDecimal[] orderAmount = {BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO};
                orderItemList.stream().forEach(orderItemDTO -> {
                    orderAmount[0] = ArithUtils.add(orderAmount[0], orderItemDTO.getOriginAmountPrice());
                    orderAmount[1] = ArithUtils.add(orderAmount[1], orderItemDTO.getActualAmountPrice());
                    orderAmount[2] = ArithUtils.add(orderAmount[2], orderItemDTO.getRealtimeResPrice());
                });
                orderInfo.setOriginResourceAmount(orderAmount[0]);
                orderInfo.setActualResourceAmount(orderAmount[1]);
                orderInfo.setRealtimeResourceAmount(orderAmount[2]);
            } else {
                log.info("executeOrderPriceAdjust_mortarAdjustMap:" + JSON.toJSONString(mortarAdjustMap));
                orderInfo.setActualOthersAmount(ArithUtils.add(orderInfo.getActualOrderAmount(),
                                ArithUtils.multiply(mortarAdjustMap.get("quantity"), mortarAdjustMap.get("adjustPrice"))));
                orderInfo.setRealtimeOthersAmount(ArithUtils.add(orderInfo.getRealtimeOthersAmount(),
                        ArithUtils.multiply(mortarAdjustMap.get("signQuantity"), mortarAdjustMap.get("adjustPrice"))));
            }
            //原始订单金额
            orderInfo.setOriginOrderAmount(ArithUtils.add(
                    orderInfo.getOriginResourceAmount(),
                    orderInfo.getOriginLogisticAmount(),
                    orderInfo.getOriginOthersAmount()));
            //实际订单金额
            orderInfo.setActualOrderAmount(ArithUtils.add(
                    orderInfo.getActualResourceAmount(),
                    orderInfo.getActualLogisticAmount(),
                    orderInfo.getActualOthersAmount()));
            //实际订单金额
            orderInfo.setRealtimeOrderAmount(ArithUtils.add(
                    orderInfo.getRealtimeResourceAmount(),
                    orderInfo.getRealtimeLogisticAmount(),
                    orderInfo.getRealtimeOthersAmount()));
            //执行更新
            BaseBiz.setUpdateOperInfo(orderInfo, SYSTEM);
            orderInfoBiz.updateSelective(orderInfo);
            //更新orderItem
            OrderItem orderItem = new OrderItem();
            orderItemList.stream().forEach(orderItemDTO -> {
                BeanUtils.copyProperties(orderItemDTO, orderItem);
                BaseBiz.setUpdateOperInfo(orderItem, SYSTEM);
                orderItemBiz.updateSelective(orderItem);
            });
            //已结束订单需要计算支付金额进行退补款操作
            Set<String> finalOrderStatusSet = Sets.newHashSet(
                    OrderStatusEnum.CLOSED.getCode(),
                    OrderStatusEnum.COMPLETED.getCode(),
                    OrderStatusEnum.ERP_CLOSING.getCode(),
                    OrderStatusEnum.ERP_COMPLETING.getCode());
            if (finalOrderStatusSet.contains(orderInfo.getOrderStatus())) {
                //执行退款或补款
                orderPaymentService.adjustRefundOrSupplement(orderInfo);
            }
        } catch (BizException be) {
            throw be;
        } catch (Exception e) {
            log.error("更新订单金额并生成退补款记录失败 异常信息:{}", e.getMessage(), e);
            throw new BizException(BasicCode.UNDEFINED_ERROR, "更新订单金额并生成退补款记录失败");
        } finally {
            if (CsStringUtils.isNotEmpty(changeOrderId) && CsStringUtils.isNotEmpty(identifier)) {
                redisLockService.unlock(changeOrderId, identifier);
            }
        }
    }
}
