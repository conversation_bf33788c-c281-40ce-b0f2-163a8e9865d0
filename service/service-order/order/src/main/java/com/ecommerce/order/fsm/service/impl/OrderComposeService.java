package com.ecommerce.order.fsm.service.impl;

import com.ecommerce.order.api.dto.OrderDTO;
import com.ecommerce.order.fsm.service.OrderServiceFactory;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 *  订单组合服务，与各个OrderService形成组合关系
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class OrderComposeService {

    private final OrderServiceFactory orderServiceFactory;

    public String createOrder(String orderType) {
        // 调用订单处理工厂
        OrderServiceFactory.getOrderHandlerService(orderType).createOrder(new OrderDTO(), "xxx");
        return "";
    }

}
