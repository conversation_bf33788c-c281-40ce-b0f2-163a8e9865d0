package com.ecommerce.order.fsm.service.impl;

import com.alibaba.fastjson.JSON;
import com.ecommerce.base.api.service.IWarehouseService;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.service.common.uuid.UUIDGenerator;
import com.ecommerce.common.utils.ArithUtils;
import com.ecommerce.common.utils.DateUtil;
import com.ecommerce.goods.api.dto.AddItemQueryConditionDTO;
import com.ecommerce.goods.api.dto.GoodsAddItemDTO;
import com.ecommerce.goods.api.dto.GoodsDTO;
import com.ecommerce.goods.api.dto.ResourceDTO;
import com.ecommerce.goods.api.dto.SpecialGoodsAttributeDTO;
import com.ecommerce.goods.api.dto.contract.TrContractGoodsDTO;
import com.ecommerce.goods.api.enums.DeliveryWayEnum;
import com.ecommerce.goods.api.service.IGoodsAddItemService;
import com.ecommerce.goods.api.service.IGoodsService;
import com.ecommerce.goods.api.service.IResourceService;
import com.ecommerce.logistics.api.dto.adjust.AdjustTakeInfoDTO;
import com.ecommerce.logistics.api.dto.rulecompute.CarriageComputeDTO;
import com.ecommerce.logistics.api.dto.rulecompute.CarriageComputeResultDTO;
import com.ecommerce.logistics.api.dto.rulecompute.CategoryQuantityDTO;
import com.ecommerce.logistics.api.dto.shippingrule.QueryShippingRuleDTO;
import com.ecommerce.logistics.api.dto.shippingrule.ShippingRuleResultDTO;
import com.ecommerce.logistics.api.enums.PickingBillTypeEnum;
import com.ecommerce.logistics.api.enums.TransportToolTypeEnum;
import com.ecommerce.logistics.api.service.IAdjustPriceService;
import com.ecommerce.logistics.api.service.IRuleComputeService;
import com.ecommerce.logistics.api.service.IShippingRouteService;
import com.ecommerce.member.api.dto.member.CollectionCarrierQueryDTO;
import com.ecommerce.member.api.dto.member.MemberDTO;
import com.ecommerce.member.api.dto.member.config.MemberConfigEnum;
import com.ecommerce.member.api.service.IMemberService;
import com.ecommerce.order.api.dto.*;
import com.ecommerce.order.api.dto.adjust.ContractAdjustDTO;
import com.ecommerce.order.api.dto.adjust.ContractAdjustGoodsDTO;
import com.ecommerce.order.api.dto.adjust.OrderAdjustDTO;
import com.ecommerce.order.api.dto.concrete.ConcreteAdjustOrderQueryDTO;
import com.ecommerce.order.api.dto.concrete.ConcreteChangeAdjustStatusDTO;
import com.ecommerce.order.api.dto.pay.TakeLogisticsFeeDTO;
import com.ecommerce.order.api.dto.proxy.PrimaryOrderCarriageDTO;
import com.ecommerce.order.api.dto.proxy.PrimaryOrderCreateDTO;
import com.ecommerce.order.api.enums.ConcreteAdjustStatusEnum;
import com.ecommerce.order.api.enums.ConcreteStatusChangeTypeEnum;
import com.ecommerce.order.api.enums.DiscountTypeEnum;
import com.ecommerce.order.biz.IOrderErpBiz;
import com.ecommerce.order.biz.fsm.IConcreteAdjustBiz;
import com.ecommerce.order.biz.fsm.IOrderInfoBiz;
import com.ecommerce.order.biz.fsm.IOrderItemBiz;
import com.ecommerce.order.biz.fsm.ITakeInfoBiz;
import com.ecommerce.order.biz.impl.fsm.OrderInfoExtBiz;
import com.ecommerce.order.dao.vo.ConcreteAdjustGoods;
import com.ecommerce.order.dao.vo.ConcreteAdjustOrder;
import com.ecommerce.order.dao.vo.OrderInfo;
import com.ecommerce.order.dao.vo.OrderItem;
import com.ecommerce.order.dao.vo.TakeInfo;
import com.ecommerce.order.fsm.service.IOrderCommonService;
import com.ecommerce.order.fsm.service.IOrderComputeService;
import com.ecommerce.order.fsm.service.IOrderQueryService;
import com.ecommerce.order.fsm.service.OrderServiceFactory;
import com.ecommerce.order.fsm.status.OrderTypeEnum;
import com.ecommerce.order.service.impl.ConcreteAdjustService;
import com.ecommerce.price.api.dto.BuyerPromotionalDetailsDTO;
import com.ecommerce.price.api.dto.ExtraExpenseComputeDTO;
import com.ecommerce.price.api.dto.machine.MachineCalculatePriceDTO;
import com.ecommerce.price.api.service.IMachineFeeService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import com.ecommerce.common.utils.CsStringUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 * Date: Create in 上午10:55 20/6/11
 */
@Slf4j
@Component
public class OrderComputeService implements IOrderComputeService {

    public static final String UNDEFINED_ERROR_COMPUTE_RESULT_MSG = "该地区无法配送该商品！";

    @Autowired
    private IOrderItemBiz orderItemBiz;
    @Autowired
    private IOrderInfoBiz orderInfoBiz;
    @Autowired
    private ITakeInfoBiz takeInfoBiz;
    @Autowired
    private UUIDGenerator uuidGenerator;
    @Autowired
    private IResourceService resourceService;
    @Autowired
    private IOrderQueryService orderQueryService;
    @Autowired
    protected IWarehouseService warehouseService;
    @Autowired
    protected IGoodsService goodsService;
    @Autowired
    protected IGoodsAddItemService goodsAddItemService;
    @Autowired
    protected IRuleComputeService ruleComputeService;
    @Autowired
    private IShippingRouteService shippingRouteService;
    @Autowired
    protected IMemberService memberService;
    @Lazy
    @Autowired
    private OrderInfoExtBiz orderInfoExtBiz;
    @Autowired
    private IMachineFeeService machineFeeService;
    @Autowired
    private IOrderCommonService orderCommonService;
    @Autowired
    private IConcreteAdjustBiz concreteAdjustBiz;
    @Autowired
    private IOrderErpBiz orderErpBiz;
    @Autowired
    private IAdjustPriceService adjustPriceService;

    @Override
    public void checkOrderAmount(OrderDTO orderDTO) {
        //订单明细
        BigDecimal itemActualAmount = BigDecimal.ZERO;
        BigDecimal addItemTotalAmount = BigDecimal.ZERO;
        for (OrderItemDTO item : orderDTO.getOrderItems()) {
            BigDecimal itemQuantity = item.getItemQuantity();//购买数量
            BigDecimal originUnitPrice = item.getOriginUnitPrice();//资源原单价(包含加价项)
            BigDecimal originAmountPrice = item.getOriginAmountPrice();
            BigDecimal actualUnitPrice = item.getActualUnitPrice();//资源实际单价
            BigDecimal actualAmountPrice = item.getActualAmountPrice();//资源实际总价
            BigDecimal discountPrice = item.getDiscountPrice();//优惠单价（指优惠了多少）


            //actualUnitPrice = originUnitPrice(addItemPrice) +  - discountPrice
            checkActualUnitPrice(item, actualUnitPrice, originUnitPrice, discountPrice, actualAmountPrice, itemQuantity, originAmountPrice);
            itemActualAmount = ArithUtils.add(itemActualAmount, actualAmountPrice);
            addItemTotalAmount = ArithUtils.add(addItemTotalAmount, ArithUtils.multiply(itemQuantity, item.getAdditemPrice()));
        }

        //订单信息
        BigDecimal originOrderAmount = orderDTO.getOriginOrderAmount();//订单原总金额
        BigDecimal originLogisticAmount = orderDTO.getOriginLogisticAmount();//物流原总金额
        BigDecimal originResourceAmount = orderDTO.getOriginResourceAmount();//资源原总金额
        BigDecimal originOthersAmount = orderDTO.getOriginOthersAmount();//其他原总金额

        BigDecimal actualOrderAmount = orderDTO.getActualOrderAmount();//订单实际总金额
        BigDecimal actualLogisticAmount = orderDTO.getActualLogisticAmount();//物流实际总费用
        BigDecimal actualResourceAmount = orderDTO.getActualResourceAmount();//资源实际总金额
        BigDecimal actualOthersAmount = orderDTO.getActualOthersAmount();//其他实际总金额

        //1.originOrderAmount = originLogisticAmount + originResourceAmount + originOthersAmount
        if (originOrderAmount.compareTo(ArithUtils.add(originLogisticAmount, originResourceAmount, originOthersAmount)) != 0) {
            throw new BizException(BasicCode.UNDEFINED_ERROR,
                    "originOrderAmount(" + originOrderAmount + ")= originLogisticAmount(" + originLogisticAmount
                            + ") + originResourceAmount(" + originResourceAmount + ") + originOthersAmount(" + originOthersAmount + ")");
        }
        //2.actualOrderAmount = actualLogisticAmount + actualResourceAmount + actualOthersAmount
        if (actualOrderAmount.compareTo(ArithUtils.add(actualLogisticAmount, actualResourceAmount, actualOthersAmount)) != 0) {
            throw new BizException(BasicCode.UNDEFINED_ERROR,
                    "actualOrderAmount(" + actualOrderAmount + ")= actualLogisticAmount(" + actualLogisticAmount
                            + ") + actualResourceAmount(" + actualResourceAmount + ") + actualOthersAmount(" + actualOthersAmount + ")");
        }
        //优惠信息
        BigDecimal orderDiscount = BigDecimal.ZERO;//订单优惠总额
        BigDecimal specShop = BigDecimal.ZERO;//门店优惠金额
        if (orderDTO.getDiscountDetail() != null) {
            for (OrderDiscountDetailDTO discounts : orderDTO.getDiscountDetail()) {
                if (DiscountTypeEnum.SPEC_SHOP.code().equals(discounts.getDiscountType())) {
                    specShop = ArithUtils.add(specShop, discounts.getAmountPrice());
                }
                orderDiscount = ArithUtils.add(orderDiscount, discounts.getAmountPrice());
            }
        }
        //总优惠  = sum(order_discount) = origin_resource_amount - actual_resource_amount
        if (orderDiscount.compareTo(ArithUtils.subtract(false, ArithUtils.add(addItemTotalAmount, originResourceAmount), actualResourceAmount)) != 0) {
            throw new BizException(BasicCode.UNDEFINED_ERROR,
                    "总优惠  = sum(order_discount) = origin_resource_amount - actual_resource_amount");
        }
        //门店优惠金额（没有包含在item中） = order_discount.spec_shop = sum(orderItem.actualAmountPrice) - fsm.actualResourceAmount
        if (specShop.compareTo(ArithUtils.subtract(false, itemActualAmount, actualResourceAmount)) != 0) {
            throw new BizException(BasicCode.UNDEFINED_ERROR,
                    "门店优惠金额（没有包含在item中） = order_discount.spec_shop = sum(orderItem.actualAmountPrice) - fsm.actualResourceAmount");
        }
        if (CollectionUtils.isEmpty(orderDTO.getOrderPayinfo())) {
            return;
        }
        //订单支付信息
        OrderPayinfoDTO payInfoDTO = orderDTO.getOrderPayinfo().get(0);
        BigDecimal payAmount = payInfoDTO.getPayAmount();//订单支付金额
        BigDecimal totalDetailAmount = BigDecimal.ZERO;
        if (payInfoDTO.getOrderPayinfoDetail() != null) {
            for (OrderPayinfoDetailDTO payDetail : payInfoDTO.getOrderPayinfoDetail()) {
                totalDetailAmount = ArithUtils.add(totalDetailAmount, payDetail.getPayAdmount());
            }
        }
        checkTotalDetailAmount(payAmount, totalDetailAmount, actualOrderAmount);
    }

    private static void checkTotalDetailAmount(BigDecimal payAmount, BigDecimal totalDetailAmount, BigDecimal actualOrderAmount) {
        if (payAmount.compareTo(totalDetailAmount) != 0) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "totalDetailAmount(" + totalDetailAmount + ")= payAmount(" +
                    payAmount + ")");
        }
        //actualOrderAmount = payAmount
        if (payAmount.compareTo(actualOrderAmount) != 0) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "actualOrderAmount(" + actualOrderAmount + ")= payAmount(" +
                    payAmount + ")");
        }
    }

    private static void checkActualUnitPrice(OrderItemDTO item, BigDecimal actualUnitPrice, BigDecimal originUnitPrice, BigDecimal discountPrice, BigDecimal actualAmountPrice, BigDecimal itemQuantity, BigDecimal originAmountPrice) {
        if (actualUnitPrice.compareTo(ArithUtils.subtract(false, ArithUtils.add(originUnitPrice, item.getAdditemPrice()), discountPrice)) != 0) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "actualUnitPrice(" + actualUnitPrice +
                    ")= originUnitPrice(" + originUnitPrice + ") - discountPrice(" + discountPrice + ")");
        }
        if (actualAmountPrice.compareTo(ArithUtils.multiply(itemQuantity, actualUnitPrice)) != 0) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "actualAmountPrice(" + actualAmountPrice +
                    ")= itemQuantity.multiply(actualUnitPrice)(" + itemQuantity.multiply(actualUnitPrice) + ")");
        }
        if (originAmountPrice.compareTo(ArithUtils.multiply(itemQuantity, originUnitPrice)) != 0) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "originAmountPrice(" + originAmountPrice +
                    ")= itemQuantity.multiply(originUnitPrice)(" + itemQuantity.multiply(originUnitPrice) + ")");
        }
    }

    @Override
    public OrderCostsDTO reComputeOrderCosts(OrderRecCostsDTO orderRecCostsDTO) {
        log.info("reComputeOrderCosts:{}", JSON.toJSONString(orderRecCostsDTO));
        String orderId = orderRecCostsDTO.getOrderId();
        if (CsStringUtils.isBlank(orderId)) {
            throw new BizException(BasicCode.PARAM_NULL, "订单Id");
        }
        OrderDTO orderDTO = orderQueryService.getOrderInfo(orderId);
        if (orderDTO == null) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "订单不存在");
        }
        List<OrderRecCostsItemDTO> costsItemList = orderRecCostsDTO.getOrderItems();
        if (CollectionUtils.isNotEmpty(costsItemList)) {
            Map<String, OrderRecCostsItemDTO> orderCostsItemMap = new HashMap<>();
            costsItemList.stream().forEach(costsItem -> orderCostsItemMap.put(costsItem.getOrderItemId(), costsItem));
            for (OrderItemDTO orderItem : orderDTO.getOrderItems()) {
                //合同单
                if (CsStringUtils.isNotBlank(orderDTO.getDealsId())) {
                    orderItem.setContractGoodsId(orderItem.getResourceId());
                    orderItem.setResourceId("");
                }
                if (orderCostsItemMap.get(orderItem.getOrderItemId()) == null) continue;
                BigDecimal quantity = orderCostsItemMap.get(orderItem.getOrderItemId()).getItemQuantity();
                orderItem.setItemQuantity(quantity);
                if (CsStringUtils.isNotBlank(orderDTO.getDealsId())) {
                    orderItem.setMeasureQuantity(quantity);
                } else {
                    orderItem.setMeasureQuantity(quantity.multiply(orderItem.getConvertRate()));
                }
                orderDTO.setStoreId(orderItem.getStoreId());
            }
        }

        return computeOrderCosts(orderDTO);
    }

    @Override
    public OrderCostsDTO computeOrderCosts(OrderDTO orderDTO) {
        log.info("computeOrderCosts:{}", JSON.toJSONString(orderDTO));
        //订单数据准备
        orderDTO.setCreateWay("computeOrder");
        if (CsStringUtils.isNotBlank(orderDTO.getDealsId())) {
            OrderServiceFactory.getOrderHandlerService(OrderTypeEnum.CONTRACT.getBizType()).orderDataPrepare(orderDTO);
        } else {
            OrderServiceFactory.getOrderHandlerService(OrderTypeEnum.LISTING.getBizType()).orderDataPrepare(orderDTO);
        }
        //计算空载和台班费
        computeMachineAndEmptyCost(orderDTO);

        //计算订单商品优惠和资源金额
        computeOrderItem(orderDTO, true);

        //获取订单支付信息，计算物流金额
        computeOrderLogisticsFee(orderDTO, true);

        //订单总金额 = 物流 + 资源 + 其他
        orderDTO.setOriginOrderAmount(ArithUtils.add(
                orderDTO.getOriginResourceAmount(),
                orderDTO.getOriginLogisticAmount(),
                orderDTO.getOriginOthersAmount()));
        orderDTO.setActualOrderAmount(ArithUtils.add(
                orderDTO.getActualResourceAmount(),
                orderDTO.getActualLogisticAmount(),
                orderDTO.getActualOthersAmount()));

        //检查订单计算金额
        log.info("compute_complete1:" + JSON.toJSONString(orderDTO));
        checkOrderAmount(orderDTO);
        log.info("compute_complete2:" + JSON.toJSONString(orderDTO));

        OrderCostsDTO costsDTO = new OrderCostsDTO();
        //订单总费用
        costsDTO.setOrderCost(orderDTO.getActualOrderAmount());
        //需付款金额
        costsDTO.setPayment(orderDTO.getActualOrderAmount());
        //商品费用
        costsDTO.setGoods(orderDTO.getOriginResourceAmount());
        //运费
        costsDTO.setCarriage(orderDTO.getActualLogisticAmount());
        //台班费
        costsDTO.setMachineCost(orderDTO.getMachineShiftCost());
        //空载费
        costsDTO.setEmptyCost(orderDTO.getOthersAmount());
        //设置优惠
        if (CollectionUtils.isNotEmpty(orderDTO.getDiscountDetail())) {
            for (OrderDiscountDetailDTO discount : orderDTO.getDiscountDetail()) {
                costsDTO.setDiscount(ArithUtils.add(costsDTO.getDiscount(), discount.getAmountPrice()));
            }
        }
        //设置商品价格信息
        List<OrderCostsItemDTO> costsItemList = Lists.newArrayList();
        for (OrderItemDTO orderItem : orderDTO.getOrderItems()) {
            OrderCostsItemDTO costItem = new OrderCostsItemDTO();
            costItem.setGoodsId(orderItem.getGoodsId());
            costItem.setResourceId(CsStringUtils.isEmpty(orderItem.getResourceId()) ?
                    orderItem.getContractGoodsId() : orderItem.getResourceId());
            costItem.setResourcePrice(orderItem.getActualUnitPrice());
            costItem.setResourceOriginPrice(orderItem.getOriginUnitPrice());
            //原始单价累加加价项单价，商品金额累加加价项金额
            if (orderItem.getAdditemPrice() != null) {
                costItem.setResourceOriginPrice(costItem.getResourceOriginPrice().add(orderItem.getAdditemPrice()));
                costsDTO.setGoods(costsDTO.getGoods().add(orderItem.getAdditemPrice().multiply(orderItem.getItemQuantity())));
            }
            costItem.setTotalOriginPrice(costItem.getResourceOriginPrice().multiply(orderItem.getItemQuantity()));
            costsItemList.add(costItem);
        }
        costsDTO.setCostsItems(costsItemList);

        return costsDTO;
    }

    @Override
    public BigDecimal computePorterage(OrderDTO orderDTO) {
        return null;
    }

    @Override
    public void computeMachineAndEmptyCost(OrderDTO order) {
        OrderInfoExtDTO orderInfoExtDTO = order.getOrderInfoExtDTO();
        List<OrderItemDTO> orderItems = order.getOrderItems();
        if (CollectionUtils.isEmpty(orderItems)) {
            return;
        }
        List<ExtraExpenseComputeDTO> collect = orderItems.stream().map(item -> {
            ExtraExpenseComputeDTO extraExpenseComputeDTO = new ExtraExpenseComputeDTO();
            extraExpenseComputeDTO.setEmptyRuleId(item.getEmptyRuleId());
            extraExpenseComputeDTO.setMachineRuleId(item.getMachineRuleId());
            //泵送量=+润管砂浆
            extraExpenseComputeDTO.setItemQuantity(ArithUtils.add(item.getItemQuantity(), orderInfoExtDTO.getLubricityQuantity()));
            //获取台班费价格(加价项泵送方式：自卸、天泵、地泵)
            if (CollectionUtils.isEmpty(item.getOrderItemAdds())) {
                extraExpenseComputeDTO.setMachineRulePrice(BigDecimal.ZERO);
                return extraExpenseComputeDTO;
            }
            for (OrderItemAddDTO orderItemAddDTO : item.getOrderItemAdds()) {
                if (CsStringUtils.equals(orderItemAddDTO.getParentName(), GoodsAddItemDTO.PUMPING_WAY)) {
                    extraExpenseComputeDTO.setMachineRulePrice(orderItemAddDTO.getAdditemPrice());
                    break;
                }
            }
            return extraExpenseComputeDTO;
        }).toList();
        log.info("台班费空载费计算:" + JSON.toJSONString(collect));
        //设置订单行项目费用、为单商品
        orderItems.get(0).setEstimateMachinePrice(BigDecimal.ZERO);
        orderItems.get(0).setConfirmMachinePrice(BigDecimal.ZERO);
        orderItems.get(0).setActualMachinePrice(BigDecimal.ZERO);
        orderItems.get(0).setEstimateEmptyPrice(BigDecimal.ZERO);
        orderItems.get(0).setConfirmEmptyPrice(BigDecimal.ZERO);
        orderItems.get(0).setActualEmptyPrice(BigDecimal.ZERO);
        order.setMachineShiftCost(BigDecimal.ZERO);
        //下单时不进行空载费预收
        order.setOthersAmount(BigDecimal.ZERO);
        //计算润管砂浆金额
        if (orderInfoExtDTO.getLubricityQuantity() != null &&
                orderInfoExtDTO.getLubricityQuantity().compareTo(BigDecimal.ZERO) > 0) {
            order.setOthersAmount(ArithUtils.multiply(orderInfoExtDTO.getLubricityQuantity(),
                    ArithUtils.add(orderInfoExtDTO.getLubricityPrice(), orderItems.get(0).getAdditemPrice())));
        }
        //设置其他费用
        order.setActualOthersAmount(ArithUtils.add(order.getMachineShiftCost(), order.getOthersAmount()));
        order.setOriginOthersAmount(order.getActualOthersAmount());
        //累加订单总金额(其他费用)
        order.setOriginOrderAmount(ArithUtils.add(order.getOriginOrderAmount(), order.getOriginOthersAmount()));
        order.setActualOrderAmount(ArithUtils.add(order.getActualOrderAmount(), order.getActualOthersAmount()));

        log.info("processMachineAndEmptyCost:{}", JSON.toJSONString(order));
    }

    /**
     * 计算订单行优惠信息
     */
    public OrderDiscountDetailDTO computeOrderItemPromotion(BuyerPromotionalDetailsDTO promotion) {
        //订单优惠明细
        OrderDiscountDetailDTO discount = new OrderDiscountDetailDTO();
        discount.setDiscountdetailId(uuidGenerator.gain());
        //一客一价
        if (CsStringUtils.isNotBlank(promotion.getBuyerPriceSaleRuleId())) {
            discount.setDiscountId(promotion.getBuyerPriceSaleRuleId());
            //优惠类型
            discount.setDiscountType(DiscountTypeEnum.ONE.getCode());
            discount.setDiscountName(DiscountTypeEnum.ONE.getMessage());
            //优惠单价
            discount.setUntiPrice(promotion.getBuyerPriceSavePrice());
            //优惠总金额
            discount.setAmountPrice(promotion.getBuyerPriceTotalPrice());
        }
        //达量优惠
        if (CsStringUtils.isNotBlank(promotion.getBuyerSectionSaleRuleId())) {
            discount.setDiscountId(promotion.getBuyerSectionSaleRuleId());
            discount.setDiscountType(DiscountTypeEnum.SECTION.getCode());
            discount.setDiscountName(DiscountTypeEnum.SECTION.getMessage());
            discount.setUntiPrice(promotion.getBuyerSectionSavePrice());
            discount.setAmountPrice(promotion.getBuyerSectionTotalPrice());
        }
        //门店优惠券
        if (CsStringUtils.isNotBlank(promotion.getReferrerRuleId())) {
            discount.setDiscountId(promotion.getReferrerRuleId());
            discount.setDiscountType(DiscountTypeEnum.SHOP.getCode());
            discount.setDiscountName(DiscountTypeEnum.SHOP.getMessage());
            discount.setUntiPrice(promotion.getReferrerSavePrice());
            discount.setAmountPrice(promotion.getReferrerTotalPrice());
        }

        return discount;
    }

    @Override
    public List<OrderDiscountDetailDTO> computeOrderItem(OrderDTO order, boolean processPromotion) {
        BigDecimal originResourceAmount = BigDecimal.ZERO;//订单资源原总金额
        BigDecimal actualResourceAmount = BigDecimal.ZERO;//订单资源实际总金额
        List<OrderDiscountDetailDTO> discountList = Lists.newArrayList();
        for (OrderItemDTO item : order.getOrderItems()) {
            if (CsStringUtils.isBlank(item.getOrderItemId())) {
                item.setOrderItemId(uuidGenerator.gain());
            }
            //可申请发货数量(累加上润管砂浆的量)
            item.setItemcantakeQuantity(ArithUtils.add(item.getItemQuantity(), order.getOrderInfoExtDTO().getLubricityQuantity()));
            item.setDiscountPrice(BigDecimal.ZERO);
            //船运不计算订单金额
            if (CsStringUtils.equals(order.getOrderInfoExtDTO().getTransportType(),
                    TransportToolTypeEnum.WATER_TRANSPORT.getCode())) {
                continue;
            }
            //优惠信息：合同单不取优惠
            if (processPromotion && !CsStringUtils.isNotBlank(order.getDealsId())) {
                BuyerPromotionalDetailsDTO promotion = orderQueryService.getResourcePromotional(order, item);
                if (promotion != null) {
                    OrderDiscountDetailDTO discount = computeOrderItemPromotion(promotion);
                    discount.setObjectId(item.getOrderItemId());
                    discountList.add(discount);
                    item.setDiscountPrice(promotion.getResourceSavePrice());
                }
                //商品成交单价去除优惠单价
                item.setActualUnitPrice(ArithUtils.subtract(
                        item.getActualUnitPrice(),
                        item.getDiscountPrice()));
            }
            //资源原始总价
            item.setOriginAmountPrice(ArithUtils.multiply(item.getOriginUnitPrice(), item.getItemQuantity()));
            //优惠后资源实际总价
            item.setActualAmountPrice(ArithUtils.multiply(item.getActualUnitPrice(), item.getItemQuantity()));
            //初始化退货数量
            item.setItemRefundQuantity(BigDecimal.ZERO);
            //初始化退货金额
            item.setItemRefundAmount(BigDecimal.ZERO);
            //订单资源原始总金额
            originResourceAmount = ArithUtils.add(originResourceAmount, item.getOriginAmountPrice());
            //订单资源实际总金额
            actualResourceAmount = ArithUtils.add(actualResourceAmount, item.getActualAmountPrice());

            log.info("processOrderItem:{}", JSON.toJSONString(item));
        }

        order.setDiscountDetail(discountList);
        //保存订单资源总金额
        order.setOriginResourceAmount(originResourceAmount);
        order.setActualResourceAmount(actualResourceAmount);
        //累加订单总金额(商品费用)
        order.setOriginOrderAmount(ArithUtils.add(order.getOriginOrderAmount(), order.getOriginResourceAmount()));
        order.setActualOrderAmount(ArithUtils.add(order.getActualOrderAmount(), order.getActualResourceAmount()));

        return discountList;
    }

    /**
     * 计算订单项物流费
     * @param order 订单对象
     * @param orderItem 订单项
     */
    public CarriageComputeResultDTO computeOrderItemCarriage(OrderDTO order, OrderItemDTO orderItem) {
        //物流信息
        CategoryQuantityDTO quantityDTO = new CategoryQuantityDTO();
        //物流类型
        quantityDTO.setTransportCategoryId(orderItem.getLogistics());
        //计量单位数量
        quantityDTO.setProductQuantity(orderItem.getMeasureQuantity());
        //计算物流费
        CarriageComputeDTO carriageDTO = new CarriageComputeDTO();
        carriageDTO.setPickingBillType(order.getDeliverWay());
        carriageDTO.setUserId(order.getSellerId());
        carriageDTO.setReceiveAddressId(order.getAddressId());
        carriageDTO.setReceiveAddressLocation(order.getAddressMap());
        carriageDTO.setWarehouseId(orderItem.getStoreId());
        carriageDTO.setCategoryQuantityList(Lists.newArrayList(quantityDTO));
        carriageDTO.setProvinceCode(order.getProvinceCode());
        carriageDTO.setCityCode(order.getCityCode());
        carriageDTO.setDistrictCode(order.getDistrictCode());
        carriageDTO.setStreetCode(order.getStreetCode());
        log.info("orderCarriageCompute_start:" + JSON.toJSONString(carriageDTO));
        ItemResult<List<CarriageComputeResultDTO>> computeResult = ruleComputeService.orderCarriageCompute(carriageDTO);
        log.info("orderCarriageCompute_end:" + JSON.toJSONString(computeResult));
        if (!computeResult.isSuccess() || CollectionUtils.isEmpty(computeResult.getData())) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, UNDEFINED_ERROR_COMPUTE_RESULT_MSG);
        }
        return computeResult.getData().get(0);
    }

    /**
     * 参考
     * 计算发货单单项物流费
     * @param sellerId  卖家id order.sellerId
     * @param pickingWharfCode 提货码头编码 orderExtDTO.pickingWharfId
     * @param unloadingWharfCode 卸货码头编码 orderExtDTO.receivingWharfId
     * @param transportCategoryId 物流类型 orderItem.logistics
     * @param pickingBillType   提货类型 order.getDeliverWay
     * @param takeQuantity 计量单位数量
     * @return
     */
    private ShippingRuleResultDTO computeTakeItemCarriage(String sellerId, String buyerId, String pickingWharfCode, String unloadingWharfCode, String transportCategoryId, String pickingBillType, BigDecimal takeQuantity) {
        //计算配送方式
        QueryShippingRuleDTO queryShippingRuleDTO = new QueryShippingRuleDTO();
        queryShippingRuleDTO.setSellerId(sellerId);
        queryShippingRuleDTO.setBuyerId(buyerId);
        //提货码头编码
        queryShippingRuleDTO.setPickingWharfCode(pickingWharfCode);
        //卸货码头编码
        queryShippingRuleDTO.setUnloadingWharfCode(unloadingWharfCode);
        ShippingRuleResultDTO shippingRuleResultDTO = new ShippingRuleResultDTO();
        //物流类型
        shippingRuleResultDTO.setTransportCategoryId(transportCategoryId);
        //计量单位数量
        shippingRuleResultDTO.setProductQuantity(takeQuantity);
        queryShippingRuleDTO.setShippingRuleList(Lists.newArrayList(shippingRuleResultDTO));
        queryShippingRuleDTO.setPickingBillType(pickingBillType);
        //查询航线定价规则
        log.info("queryShippingRule_start:" + JSON.toJSONString(queryShippingRuleDTO));
        ItemResult<List<ShippingRuleResultDTO>> computeResult = shippingRouteService.queryShippingRule(queryShippingRuleDTO);
        log.info("queryShippingRule_end:" + JSON.toJSONString(computeResult));
        if (!computeResult.isSuccess()) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "查询航线运费定价异常！");
        }
        if (!computeResult.getData().get(0).getRuleFlag()) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, UNDEFINED_ERROR_COMPUTE_RESULT_MSG);
        }
        return computeResult.getData().get(0);
    }

    private String getCarrierId(String sellerId,String pickingBillType){
        if (CsStringUtils.equals(PickingBillTypeEnum.PLATFORM_DELIVERY.getCode(), pickingBillType)) {
            CollectionCarrierQueryDTO collectionCarrierQueryDTO = new CollectionCarrierQueryDTO();
            MemberDTO memberDTO = memberService.findCollectionCarrier(collectionCarrierQueryDTO);
            log.info("memberDTO:" + JSON.toJSONString(memberDTO));
            if (memberDTO == null || CsStringUtils.isEmpty(memberDTO.getMemberId())) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "无法获取平台承运商！");
            }
            return memberDTO.getMemberId();
        }
        return sellerId;
    }

    /**
     * 获取订单物流费
     * @param order 订单对象
     * @param reversePush 是否执行到位价反推
     */
    @Override
    public void computeOrderLogisticsFee(OrderDTO order, Boolean reversePush) {
        //物流款（非自提，非)
        order.setSpecPay(true);
        //订单物流原始总金额
        BigDecimal originLogisticsAmount = BigDecimal.ZERO;
        //订单物流实际总金额
        BigDecimal actualLogisticsAmount = BigDecimal.ZERO;
        for (OrderItemDTO orderItem : order.getOrderItems()) {
            ItemResult<SpecialGoodsAttributeDTO> goodsResult =
                    goodsService.getSpecialGoodsAttribute(orderItem.getGoodsId());
            if (!goodsResult.isSuccess()) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "查询商品特殊属性失败");
            }
            //自提或者船运或不需要计算物流费
            if (CsStringUtils.equals(PickingBillTypeEnum.BUYER_TAKE.getCode(), order.getDeliverWay()) ||
                    CsStringUtils.equals(order.getOrderInfoExtDTO().getTransportType(), TransportToolTypeEnum.WATER_TRANSPORT.getCode()) ||
                    goodsResult.getData().getConcreteFlag() == 1) {
                orderItem.setOriginLogisticPrice(BigDecimal.ZERO);
                orderItem.setActualLogisticPrice(BigDecimal.ZERO);
                continue;
            }
            //是否需要反向计算商品费用
            if (orderItem.getCategoryType() == null || orderItem.getCategoryCode() == null) {
                GoodsDTO goodsDTO = goodsService.getGoodsInfo(orderItem.getGoodsId()).getData();
                orderItem.setCategoryType(goodsDTO.getCategoryType());
                orderItem.setCategoryCode(goodsDTO.getCategoryCode());
            }
        }
        //设置物流费用
        order.setOriginLogisticAmount(originLogisticsAmount);
        order.setActualLogisticAmount(actualLogisticsAmount);
        //累加订单总金额(物流费用)
        order.setOriginOrderAmount(ArithUtils.add(order.getOriginOrderAmount(), order.getOriginLogisticAmount()));
        order.setActualOrderAmount(ArithUtils.add(order.getActualOrderAmount(), order.getActualLogisticAmount()));
    }

    @Override
    public TakeLogisticsFeeDTO computeTakeLogisticsFee(OrderInfo order, OrderItem orderItem, TrContractGoodsDTO contractGoodsDTO, BigDecimal takeQuantity) {
        TakeLogisticsFeeDTO takeLogisticsFeeDTO = new TakeLogisticsFeeDTO();
        //自提没有物流费
        if( DeliveryWayEnum.BUYER_TAKE.getCode().equals(order.getDeliverWay()) ){
            return null;
        }
        //固定运费单价：出厂价 - 到位价
        BigDecimal fixedUnitCarriage = BigDecimal.ZERO;
        GoodsDTO goodsDTO = null;
        //合同单
        if (CsStringUtils.isNotBlank(order.getDealsId())) {
            if (contractGoodsDTO.getShipPrice() != null && contractGoodsDTO.getShipPrice().compareTo(BigDecimal.ZERO) > 0) {
                fixedUnitCarriage = contractGoodsDTO.getShipPrice().subtract(contractGoodsDTO.getOutFactoryPrice());
            }
            goodsDTO = goodsService.getGoodsInfo(orderItem.getGoodsId()).getData();
        } else {
            ResourceDTO resource = resourceService.getResourceDetail(orderItem.getResourceId());
            goodsDTO = resource.getGoodsDTO();
            if (resource.getArrivePrice() != null && resource.getArrivePrice().compareTo(BigDecimal.ZERO) > 0) {
                fixedUnitCarriage = resource.getArrivePrice().subtract(resource.getFactoryPrice());
            }
        }
        if (orderItem.getCategoryType() == null || orderItem.getCategoryCode() == null) {
            orderItem.setCategoryType(goodsDTO.getCategoryType());
            orderItem.setCategoryCode(goodsDTO.getCategoryCode());
        }
        BigDecimal carriage;
        //物流费＝到位价-出厂价(直接使用固定运费单价)
        if (CsStringUtils.contains(goodsDTO.getCategoryCode(), "002001003")) {//参考goods项目 GoodsService.getGoodsCategoryAttribute.setConcreteFlag
            carriage = fixedUnitCarriage.multiply(takeQuantity);
            takeLogisticsFeeDTO.setPayeeId(order.getSellerId());
        } else {
            OrderInfoExtDTO orderExtDTO = orderInfoExtBiz.findByOrderId(order.getOrderId());
            ShippingRuleResultDTO compute = computeTakeItemCarriage(order.getSellerId(), order.getBuyerId(), orderExtDTO.getPickingWharfId(), orderExtDTO.getReceivingWharfId(), orderItem.getLogistics(), order.getDeliverWay(), takeQuantity);
            carriage = compute.getCarriage();
            takeLogisticsFeeDTO.setPayeeId(getCarrierId(order.getSellerId(),order.getDeliverWay()));
        }
        BigDecimal logisticUnitPrice = carriage.divide(takeQuantity, 2, RoundingMode.HALF_UP);
        if (!orderItem.getUnits().equals(orderItem.getMeasureUnits())) {
            logisticUnitPrice = logisticUnitPrice.divide(orderItem.getUnconvertRate(), 2, RoundingMode.HALF_UP);
        }
        //计算运费单价后覆盖运费（忽略精度丢失的影响）
        carriage = logisticUnitPrice.multiply(takeQuantity);
        takeLogisticsFeeDTO.setPayAmount(carriage);
        takeLogisticsFeeDTO.setLogisticUnitPrice(logisticUnitPrice);
        return takeLogisticsFeeDTO;
    }

    /**
     * 计算确认价格数量
     * @param confirmOrder 确认订单对象
     * @param orderDTO 原始订单对象
     * @return 是否需要重新计算金额
     */
    @Override
    public Boolean computeOrderChangeItem(OrderDTO confirmOrder, OrderDTO orderDTO) {
        List<OrderItemDTO> changeItemList = confirmOrder.getOrderItems();
        Map<String, OrderItemDTO> changeItemMap = Maps.newHashMap();
        changeItemList.stream().forEach(changeItem ->
                changeItemMap.put(changeItem.getOrderItemId(), changeItem));

        Boolean reComputeOrderAmount = Boolean.FALSE;
        //计算新的商品单价
        for (OrderItemDTO orderItem : orderDTO.getOrderItems()) {
            OrderItemDTO changeItem = changeItemMap.get(orderItem.getOrderItemId());
            if (changeItem == null) continue;
            //变更商品单价后优惠信息失效
            BigDecimal actualResUnitPrice = changeItem.getActualResUnitPrice();
            if (actualResUnitPrice.compareTo(orderItem.getActualResUnitPrice()) != 0) {
                orderItem.setActualResUnitPrice(actualResUnitPrice);
            }
            //计算加价项
            doOrderItemAddsLogic(orderDTO, orderItem, changeItem);
            reComputeOrderAmount = getReComputeOrderAmount(orderItem, actualResUnitPrice, reComputeOrderAmount, changeItem);
        }

        return reComputeOrderAmount;
    }

    private void doOrderItemAddsLogic(OrderDTO orderDTO, OrderItemDTO orderItem, OrderItemDTO changeItem) {
        if (CollectionUtils.isNotEmpty(changeItem.getOrderItemAdds())) {
            Map<String, OrderItemAddDTO> orderItemAddMap = Maps.newHashMap();
            List<String> addItemIdList = Lists.newArrayList();
            orderItem.getOrderItemAdds().stream().forEach(orderItemAdd -> {
                orderItemAddMap.put(orderItemAdd.getOrderItemAddId(), orderItemAdd);
                addItemIdList.add(orderItemAdd.getAdditemId());
            });
            changeItem.getOrderItemAdds().stream().forEach(addItem -> {
                if (orderItemAddMap.get(addItem.getOrderItemAddId()) != null) {
                    orderItemAddMap.get(addItem.getOrderItemAddId()).setMessageValue(addItem.getMessageValue());
                    orderItemAddMap.get(addItem.getOrderItemAddId()).setAdditemPrice(addItem.getAdditemPrice());
                }
            });
            AddItemQueryConditionDTO addItemQueryConditionDTO = new AddItemQueryConditionDTO();
            addItemQueryConditionDTO.setAddItemType(1);
            addItemQueryConditionDTO.setSellerId(orderDTO.getSellerId());
            addItemQueryConditionDTO.setAddItemIdList(addItemIdList);
            List<GoodsAddItemDTO> addItemList = goodsAddItemService.queryAddItemByCondition(addItemQueryConditionDTO);
            if (CollectionUtils.isEmpty(addItemList)) {
                throw new BizException(BasicCode.DATA_NOT_EXIST, "加价项");
            }
            Map<String, GoodsAddItemDTO> addItemPriceMap = Maps.newHashMap();
            addItemList.stream().forEach(goodsAddItem -> addItemPriceMap.put(goodsAddItem.getAdditemId(), goodsAddItem));
            BigDecimal newAddItemPrice = getNewAddItemPrice(orderItem, addItemPriceMap);
            if (newAddItemPrice.compareTo(orderItem.getAdditemPrice()) != 0) {
                orderItem.setAdditemPrice(newAddItemPrice);
            }
        }
    }
    private static BigDecimal getNewAddItemPrice(OrderItemDTO orderItem, Map<String, GoodsAddItemDTO> addItemPriceMap) {
        BigDecimal newAddItemPrice = BigDecimal.ZERO;
        for (OrderItemAddDTO orderItemAdd : orderItem.getOrderItemAdds()) {
            //不参与价格计算(特殊自定义加价项)
            if (addItemPriceMap.get(orderItemAdd.getAdditemId()) == null) continue;
            GoodsAddItemDTO goodsAddItem = addItemPriceMap.get(orderItemAdd.getAdditemId());
            //排除润管砂浆、泵送方式
            if (goodsAddItem.getPriceWay() == null || goodsAddItem.getPriceWay() != 1) continue;
            newAddItemPrice = newAddItemPrice.add(orderItemAdd.getAdditemPrice());
        }
        return newAddItemPrice;
    }
    private static Boolean getReComputeOrderAmount(OrderItemDTO orderItem, BigDecimal actualResUnitPrice, Boolean reComputeOrderAmount, OrderItemDTO changeItem) {
        //实际单价 = 资源单价 ＋ 加价项单价
        BigDecimal actualUnitPrice = ArithUtils.add(actualResUnitPrice, orderItem.getAdditemPrice());
        if (actualUnitPrice.compareTo(orderItem.getActualUnitPrice()) != 0) {
            orderItem.setActualUnitPrice(actualUnitPrice);
            reComputeOrderAmount = Boolean.TRUE;
        }

        //计算商品数量
        BigDecimal changeQuantity = changeItem.getItemQuantity();
        if (changeQuantity != null && changeQuantity.compareTo(orderItem.getItemQuantity()) != 0) {
            orderItem.setItemQuantity(changeQuantity);
            orderItem.setMeasureQuantity(changeQuantity.multiply(orderItem.getConvertRate()));
            reComputeOrderAmount =  Boolean.TRUE;
        }
        return reComputeOrderAmount;
    }

    @Override
    public PrimaryOrderCarriageDTO computePrimaryOrderCarriage(PrimaryOrderCreateDTO primaryOrderCreateDTO) {
        log.info("computePrimaryOrderCarriage:" + JSON.toJSONString(primaryOrderCreateDTO));
        //获取二级订单信息
        OrderDTO secondaryOrderDTO = orderQueryService.getOrderInfo(primaryOrderCreateDTO.getSecondaryOrderId());
        //组装商品行数据
        if (CollectionUtils.isEmpty(secondaryOrderDTO.getOrderItems())) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "查询不到二级订单的商品数据");
        }
        OrderItemDTO secondaryOrderItemDTO = secondaryOrderDTO.getOrderItems().get(0);
        ItemResult<GoodsDTO> goodsResult = goodsService.getGoodsInfo(secondaryOrderItemDTO.getGoodsId());
        if (goodsResult == null || !goodsResult.isSuccess() || goodsResult.getData() == null) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "查询商品信息出错");
        }
        GoodsDTO goodsDTO = goodsResult.getData();
        if (CsStringUtils.isBlank(goodsDTO.getRefGoodsId())) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "商品查找不到和厂家商品的映射关系");
        }
        //物流信息
        CategoryQuantityDTO quantityDTO = new CategoryQuantityDTO();
        //物流类型
        quantityDTO.setTransportCategoryId(goodsDTO.getLogistics());
        //计量单位数量
        quantityDTO.setProductQuantity(primaryOrderCreateDTO.getPurchaseQuantity());
        //计算物流费
        CarriageComputeDTO carriageDTO = new CarriageComputeDTO();
        carriageDTO.setPickingBillType(primaryOrderCreateDTO.getDeliverWay());
        carriageDTO.setUserId(goodsDTO.getRefSellerId());
        carriageDTO.setWarehouseId(primaryOrderCreateDTO.getWarehouseId());
        carriageDTO.setCategoryQuantityList(Lists.newArrayList(quantityDTO));
        //收货地址信息
        carriageDTO.setReceiveAddressId(secondaryOrderDTO.getAddressId());
        carriageDTO.setReceiveAddressLocation(secondaryOrderDTO.getAddressMap());
        carriageDTO.setProvinceCode(secondaryOrderDTO.getProvinceCode());
        carriageDTO.setCityCode(secondaryOrderDTO.getCityCode());
        carriageDTO.setDistrictCode(secondaryOrderDTO.getDistrictCode());
        carriageDTO.setStreetCode(secondaryOrderDTO.getStreetCode());
        log.info("orderCarriageCompute_start:" + JSON.toJSONString(carriageDTO));
        ItemResult<List<CarriageComputeResultDTO>> computeResult = ruleComputeService.orderCarriageCompute(carriageDTO);
        log.info("orderCarriageCompute_end:" + JSON.toJSONString(computeResult));
        if (!computeResult.isSuccess() || CollectionUtils.isEmpty(computeResult.getData())) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, UNDEFINED_ERROR_COMPUTE_RESULT_MSG);
        }
        CarriageComputeResultDTO carriageComputeResultDTO = computeResult.getData().get(0);
        PrimaryOrderCarriageDTO primaryOrderCarriageDTO = new PrimaryOrderCarriageDTO();
        primaryOrderCarriageDTO.setPrimaryOrderId(primaryOrderCreateDTO.getPrimaryOrderId());
        primaryOrderCarriageDTO.setPrimaryOrderCode(primaryOrderCreateDTO.getPrimaryOrderCode());
        BigDecimal logisticUnitPrice = carriageComputeResultDTO.getCarriage().divide(primaryOrderCreateDTO.getPurchaseQuantity(), 2, RoundingMode.HALF_UP);
        primaryOrderCarriageDTO.setCarriageUnit(logisticUnitPrice);
        //计算运费单价后覆盖运费（忽略精度丢失的影响）
        BigDecimal carriage = logisticUnitPrice.multiply(primaryOrderCreateDTO.getPurchaseQuantity());
        primaryOrderCarriageDTO.setCarriage(carriage);

        return primaryOrderCarriageDTO;
    }

    /**
     * 计算订单台班费
     * @param orderDTO 订单对象
     * @return 台班费
     */
    @Override
    public BigDecimal computeOrderMachineCost(OrderDTO orderDTO) {
        OrderItemDTO orderItemDTO = orderQueryService.getOrderItems(orderDTO.getOrderId()).get(0);
        OrderInfoExtDTO orderInfoExtDTO = orderDTO.getOrderInfoExtDTO();
        log.info("订单明细:" + JSON.toJSONString(orderItemDTO));
        MachineCalculatePriceDTO calculatePriceDTO = new MachineCalculatePriceDTO();
        calculatePriceDTO.setMachineRuleId(orderItemDTO.getMachineRuleId());
        BigDecimal countQuantity;
        BigDecimal totalActualMachineAmount = BigDecimal.ZERO;
        if (CsStringUtils.equals(orderDTO.getOrderType(), OrderTypeEnum.CONTRACT.getCode())) {
            //获取当前买家、卖家、合同、收货地址、时间范围进行台班费数量累加
            OrderQuantityCountQueryDTO orderQuantityCountQueryDTO = new OrderQuantityCountQueryDTO();
            orderQuantityCountQueryDTO.setBuyerId(orderDTO.getBuyerId());
            orderQuantityCountQueryDTO.setSellerId(orderDTO.getSellerId());
            orderQuantityCountQueryDTO.setAddressId(orderDTO.getAddressId());
            orderQuantityCountQueryDTO.setDealsSequence(orderDTO.getDealsSequence());
            orderQuantityCountQueryDTO.setQueryStartTime(DateUtil.convertDateToString(orderDTO.getCreateTime()));
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(orderDTO.getCreateTime());
            calendar.add(Calendar.DATE, 1);
            orderQuantityCountQueryDTO.setQueryEndTime(DateUtil.convertDateToString(calendar.getTime()));
            log.info("computeOrderMachineCost_orderQuantityCountList:" + JSON.toJSONString(orderQuantityCountQueryDTO));
            OrderQuantityCountSumDTO orderQuantityCountSumDTO = orderInfoBiz.queryOrderQuantityCountList(orderQuantityCountQueryDTO);
            log.info("queryOrderQuantityCountList_orderQuantityCountSumDTO:" + JSON.toJSONString(orderQuantityCountSumDTO));
            countQuantity = orderQuantityCountSumDTO.getTotalQuantity();
            totalActualMachineAmount = orderQuantityCountSumDTO.getTotalActualMachineAmount();
        } else {
            //买家自提取出厂数量
            if (CsStringUtils.equals(orderDTO.getDeliverWay(), PickingBillTypeEnum.BUYER_TAKE.getCode())) {
                countQuantity = ArithUtils.add(orderItemDTO.getItemSendQuantity(), orderInfoExtDTO.getLubricitySignQuantity());
            } else {
                countQuantity = ArithUtils.add(orderItemDTO.getItemSignQuantity(), orderInfoExtDTO.getLubricitySignQuantity());
            }
        }
        calculatePriceDTO.setQuantity(countQuantity == null ? BigDecimal.ZERO : countQuantity);
        //获取台班费价格(加价项泵送方式：自卸、天泵、地泵、车载泵)
        if (CollectionUtils.isNotEmpty(orderItemDTO.getOrderItemAdds())) {
            for (OrderItemAddDTO orderItemAddDTO : orderItemDTO.getOrderItemAdds()) {
                if (CsStringUtils.contains(orderItemAddDTO.getAdditemName(), "天泵") ||
                        CsStringUtils.contains(orderItemAddDTO.getAdditemName(), "地泵") ||
                        CsStringUtils.contains(orderItemAddDTO.getAdditemName(), "车载泵") ||
                        CsStringUtils.contains(orderItemAddDTO.getAdditemName(), "自卸")) {
                    calculatePriceDTO.setUnitPrice(orderItemAddDTO.getAdditemPrice());
                    break;
                }
            }
        }
        log.info("计算台班费:" + JSON.toJSONString(calculatePriceDTO));
        BigDecimal machineCost = machineFeeService.calculateAmount(calculatePriceDTO).getData().getAmount();
        //当前订单台班费 ＝ 当前应收 － 实际已收
        BigDecimal actualMachineAmount = ArithUtils.subtract(Boolean.FALSE, machineCost, totalActualMachineAmount);
        OrderItem orderItem = new OrderItem();
        orderItem.setOrderItemId(orderItemDTO.getOrderItemId());
        orderItem.setActualMachinePrice(actualMachineAmount);
        orderItemBiz.updateSelective(orderItem);

        return actualMachineAmount;
    }

    @Override
    public void concreteOrderPriceAdjust(ContractAdjustDTO contractAdjustDTO) {
        log.info("concreteOrderPriceAdjust_contractAdjustDTO:" + JSON.toJSONString(contractAdjustDTO));
        Set<String> triggerStatusSet = Sets.newHashSet(
                ConcreteAdjustStatusEnum.WAIT_ADJUST.getCode(),
                ConcreteAdjustStatusEnum.ADJUST_FAILED.getCode()
        );
        ConcreteAdjustGoods concreteAdjustGoods = concreteAdjustBiz.queryConcreteAdjustGoods(contractAdjustDTO.getAdjustGoodsId());
        if (concreteAdjustGoods == null || !triggerStatusSet.contains(concreteAdjustGoods.getAdjustStatus())) {
            log.info("调价商品不存在或当前状态不满足重新执行条件");
            return;
        }
        //获取所有待调价订单列表
        ConcreteAdjustOrderQueryDTO concreteAdjustOrderQueryDTO = new ConcreteAdjustOrderQueryDTO();
        concreteAdjustOrderQueryDTO.setAdjustGoodsId(concreteAdjustGoods.getAdjustGoodsId());
        List<ConcreteAdjustOrder> concreteAdjustOrderList = concreteAdjustBiz.queryAdjustOrderListByCondition(concreteAdjustOrderQueryDTO);
        if (CollectionUtils.isEmpty(concreteAdjustOrderList)) {
            return;
        }
        Map<String, ConcreteAdjustOrder> concreteAdjustOrderMap = Maps.newHashMap();
        concreteAdjustOrderList.stream().forEach(concreteAdjustOrder -> concreteAdjustOrderMap.put(concreteAdjustOrder.getOrderId(), concreteAdjustOrder));
        Map<String, ContractAdjustGoodsDTO> adjustGoodsMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(contractAdjustDTO.getGoodsList())) {
            contractAdjustDTO.getGoodsList().stream().forEach(
                    contractAdjustGoodsDTO -> adjustGoodsMap.put(contractAdjustGoodsDTO.getGoodsId(), contractAdjustGoodsDTO));
        }
        //获取调价相关的订单数据
        contractAdjustDTO.setOrderIdList(Lists.newArrayList(concreteAdjustOrderMap.keySet()));
        List<OrderAdjustDTO> orderAdjustList = orderInfoBiz.queryWaitAdjustOrderList(contractAdjustDTO);
        Map<String, List<OrderItemDTO>> orderItemMap = Maps.newHashMap();
        Map<String, Map<String, BigDecimal>> orderMortarMap = Maps.newHashMap();
        Set<String> orderItemIdSet = Sets.newHashSet();
        if (CsStringUtils.equals(concreteAdjustGoods.getGoodsId(), ConcreteAdjustService.MORTAR_GOODS_ID)) {
            List<OrderInfoExtDTO> orderInfoExtList = orderInfoExtBiz.findByOrderIds(Lists.newArrayList(concreteAdjustOrderMap.keySet()));
            if (CollectionUtils.isNotEmpty(orderInfoExtList)) {
                orderInfoExtList.stream().forEach(orderInfoExtDTO -> {
                    Map<String, BigDecimal> mortarAdjustMap = Maps.newHashMap();
                    mortarAdjustMap.put("quantity", orderInfoExtDTO.getLubricityQuantity());
                    mortarAdjustMap.put("signQuantity", orderInfoExtDTO.getLubricitySignQuantity());
                    mortarAdjustMap.put("adjustPrice", adjustGoodsMap.get(concreteAdjustGoods.getGoodsId()).getAdjustPrice());
                    orderMortarMap.put(orderInfoExtDTO.getOrderId(), mortarAdjustMap);
                });
            }
        } else {
            //计算商品金额
            orderAdjustList.stream().forEach(orderAdjustDTO -> {
                if (adjustGoodsMap.get(orderAdjustDTO.getGoodsId()) == null) {
                    return;
                }
                BigDecimal adjustPrice = adjustGoodsMap.get(orderAdjustDTO.getGoodsId()).getAdjustPrice();
                OrderItemDTO orderItemDTO = new OrderItemDTO();
                orderItemDTO.setOrderItemId(orderAdjustDTO.getOrderItemId());
                //计算原始商品金额
                BigDecimal newOriginUnitPrice = ArithUtils.add(orderAdjustDTO.getOriginUnitPrice(), adjustPrice);
                orderItemDTO.setOriginUnitPrice(newOriginUnitPrice);
                orderItemDTO.setOriginAmountPrice(ArithUtils.multiply(newOriginUnitPrice, orderAdjustDTO.getItemQuantity()));
                //计算实际商品金额
                BigDecimal newActualUnitPrice = concreteAdjustOrderMap.get(orderAdjustDTO.getOrderId()).getAdjustTradingPrice();
                orderItemDTO.setActualUnitPrice(newActualUnitPrice);
                orderItemDTO.setActualAmountPrice(ArithUtils.multiply(newActualUnitPrice, orderAdjustDTO.getItemQuantity()));
                //计算实发商品金额
                orderItemDTO.setRealtimeResPrice(ArithUtils.multiply(newActualUnitPrice, orderAdjustDTO.getItemSignQuantity()));
                if (orderItemMap.get(orderAdjustDTO.getOrderId()) == null) {
                    orderItemMap.put(orderAdjustDTO.getOrderId(), Lists.newArrayList(orderItemDTO));
                } else {
                    orderItemMap.get(orderAdjustDTO.getOrderId()).add(orderItemDTO);
                }
                orderItemIdSet.add(orderItemDTO.getOrderItemId());
            });
        }
        //更新调价计划商品行->调价中
        ConcreteChangeAdjustStatusDTO concreteChangeGoodsStatusDTO = new ConcreteChangeAdjustStatusDTO();
        concreteChangeGoodsStatusDTO.setOperatorUserId(contractAdjustDTO.getOperatorUserId());
        concreteChangeGoodsStatusDTO.setAdjustPlanId(contractAdjustDTO.getAdjustPlanId());
        concreteChangeGoodsStatusDTO.setAdjustGoodsId(contractAdjustDTO.getAdjustGoodsId());
        concreteChangeGoodsStatusDTO.setChangeType(ConcreteStatusChangeTypeEnum.ADJUST_GOODS.getCode());
        concreteChangeGoodsStatusDTO.setChangeStatus(ConcreteAdjustStatusEnum.ADJUSTING.getCode());
        concreteAdjustBiz.changeAdjustStatus(concreteChangeGoodsStatusDTO);
        Set<String> filterOrderIdList = Sets.newHashSet();
        //执行订单调价
        orderItemMapLogic(contractAdjustDTO, orderItemMap, concreteAdjustOrderMap, triggerStatusSet, filterOrderIdList, orderMortarMap);
        //非ERP合同则直接更新为成功
        changeAdjustStatus(contractAdjustDTO, concreteChangeGoodsStatusDTO, filterOrderIdList);
    }

    private void orderItemMapLogic(ContractAdjustDTO contractAdjustDTO, Map<String, List<OrderItemDTO>> orderItemMap, Map<String, ConcreteAdjustOrder> concreteAdjustOrderMap, Set<String> triggerStatusSet, Set<String> filterOrderIdList, Map<String, Map<String, BigDecimal>> orderMortarMap) {
        orderItemMap.keySet().stream().forEach(changeOrderId -> {
            try {
                ConcreteAdjustOrder concreteAdjustOrder = concreteAdjustOrderMap.get(changeOrderId);
                if (concreteAdjustOrder == null || !triggerStatusSet.contains(concreteAdjustOrder.getAdjustStatus())) {
                    log.info("订单已调价或处于锁定状态,concreteOrderPriceAdjust_concreteAdjustOrder:" + JSON.toJSONString(concreteAdjustOrder));
                    filterOrderIdList.add(changeOrderId);
                    return;
                }
                //待调价状态才执行电商修改动作
                if (CsStringUtils.equals(concreteAdjustOrder.getAdjustStatus(), ConcreteAdjustStatusEnum.WAIT_ADJUST.getCode())) {
                    orderCommonService.executeOrderPriceAdjust(changeOrderId,
                            orderItemMap.get(changeOrderId), orderMortarMap.get(changeOrderId));
                    //通知物流更新运单单价
                    List<TakeInfo> takeInfoList = takeInfoBiz.getTakeInfoByOrderId(changeOrderId);
                    if (CollectionUtils.isNotEmpty(takeInfoList)) {
                        List<AdjustTakeInfoDTO> adjustTakeInfoList = Lists.newArrayList();
                        takeInfoList.stream().forEach(takeInfo -> {
                            AdjustTakeInfoDTO adjustTakeInfoDTO = new AdjustTakeInfoDTO();
                            adjustTakeInfoDTO.setTakeCode(takeInfo.getTakeCode());
                            adjustTakeInfoDTO.setNewestPrice(concreteAdjustOrder.getAdjustTradingPrice());
                            adjustTakeInfoList.add(adjustTakeInfoDTO);
                        });
                        adjustPriceService.adjustConcreteByTakeCode(adjustTakeInfoList);
                    }
                }
                //更新调价状态
                ConcreteChangeAdjustStatusDTO concreteChangeOrderStatusDTO = new ConcreteChangeAdjustStatusDTO();
                concreteChangeOrderStatusDTO.setOperatorUserId(contractAdjustDTO.getOperatorUserId());
                concreteChangeOrderStatusDTO.setAdjustGoodsId(contractAdjustDTO.getAdjustGoodsId());
                concreteChangeOrderStatusDTO.setOrderId(changeOrderId);
                concreteChangeOrderStatusDTO.setChangeType(ConcreteStatusChangeTypeEnum.ADJUST_ORDER.getCode());
                //需要同步ERP则更新为调价中
                if (orderErpBiz.hasErpOrder(contractAdjustDTO.getSellerId(), MemberConfigEnum.ERP_ORDER_FLG.getKeyCode())) {
                    concreteChangeOrderStatusDTO.setChangeStatus(ConcreteAdjustStatusEnum.ADJUSTING.getCode());
                } else {
                    concreteChangeOrderStatusDTO.setChangeStatus(ConcreteAdjustStatusEnum.ADJUST_SUCCESS.getCode());
                }
                concreteAdjustBiz.changeAdjustStatus(concreteChangeOrderStatusDTO);
            } catch (Exception e) {
                filterOrderIdList.add(changeOrderId);
                //电商执行改价失败不修改调价状态(用于区分是否为ERP改价失败)
                log.info("执行订单调价异常orderId={},{}", changeOrderId, e.getMessage(), e);
            }
        });
    }

    private void changeAdjustStatus(ContractAdjustDTO contractAdjustDTO, ConcreteChangeAdjustStatusDTO concreteChangeGoodsStatusDTO, Set<String> filterOrderIdList) {
        if (!orderErpBiz.hasErpOrder(contractAdjustDTO.getSellerId(), MemberConfigEnum.ERP_ORDER_FLG.getKeyCode())) {
            concreteChangeGoodsStatusDTO.setChangeStatus(!filterOrderIdList.isEmpty() ?
                    ConcreteAdjustStatusEnum.ADJUST_FAILED.getCode() : ConcreteAdjustStatusEnum.ADJUST_SUCCESS.getCode());
            concreteAdjustBiz.changeAdjustStatus(concreteChangeGoodsStatusDTO);
        } else {
            //通知ERP
            orderErpBiz.concreteAdjustPriceNotify(contractAdjustDTO.getAdjustGoodsId(), filterOrderIdList);
        }
    }
}
