package com.ecommerce.order.fsm.service.impl;

import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.goods.api.enums.DeliveryWayEnum;
import com.ecommerce.member.api.dto.member.config.MemberConfigDTO;
import com.ecommerce.member.api.service.IMemberConfigService;
import com.ecommerce.open.api.dto.apicenter.order.ERPOrderAdjustDTO;
import com.ecommerce.open.api.dto.apicenter.order.ERPOverSoldVerifyResponseDTO;
import com.ecommerce.order.api.dto.OrderDTO;
import com.ecommerce.order.api.dto.erp.ERPOverSoldDTO;
import com.ecommerce.order.api.dto.erp.OrderCloseERPCallback;
import com.ecommerce.order.api.dto.erp.OrderCompleteERPCallback;
import com.ecommerce.order.api.dto.erp.OrderCreateERPCallback;
import com.ecommerce.order.biz.fsm.IOrderInfoBiz;
import com.ecommerce.order.dao.vo.OrderInfo;
import com.ecommerce.order.dao.vo.OrderPayinfo;
import com.ecommerce.order.dao.vo.OrderPayinfoDetail;
import com.ecommerce.order.fsm.service.IOrderErpService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.concurrent.ThreadPoolExecutor;

@Slf4j
@Service
@RequiredArgsConstructor
public class OrderErpService implements IOrderErpService {

    private final IMemberConfigService memberConfigService;
    private final ThreadPoolExecutor threadPoolExecutor;
    private final IOrderInfoBiz orderInfoBiz;

    @Override
    public void orderCreateToErp(String orderId, Date takeTimeLimit) {
        // TODO Implement this method
    }

    @Override
    public void orderCreateToErpAsync(String orderId, Date takeTimeLimit) {
        // TODO Implement this method
    }

    @Override
    public boolean orderCreateERPCallback(OrderCreateERPCallback orderCreateERPCallback, String operator) throws Exception {
        return false;
    }

    @Override
    public void orderCloseToErp(String orderId) {
        // TODO Implement this method
    }

    @Override
    public boolean orderCloseERPCallback(OrderCloseERPCallback orderCloseERPCallback, String operator) throws Exception {
        return false;
    }

    @Override
    public boolean orderCompleteToErp(String orderId) {
        return false;
    }

    @Override
    public boolean orderCompleteERPCallback(OrderCompleteERPCallback orderCompleteERPCallback, String operator) throws Exception {
        return false;
    }

    @Override
    public boolean erpCloseOrder(OrderCloseERPCallback orderCloseERPCallback) throws Exception {
        return false;
    }

    @Override
    public boolean erpCompleteOrder(OrderCompleteERPCallback orderCompleteERPCallback) throws Exception {
        return false;
    }

    @Override
    public boolean orderComplete(OrderDTO orderDTO) {
        return false;
    }

    @Override
    public boolean ERPDispatch(OrderDTO orderInfo) {
        if (orderInfo == null)
            throw new BizException(BasicCode.PARAM_NULL, "订单");
        // 卖家配送才支持ERP调度
        return DeliveryWayEnum.SELLER_DELIVERY.code().equals(orderInfo.getDeliverWay());
    }

    @Override
    public boolean ERPDispatch(OrderInfo orderInfo) {
        if (orderInfo == null)
            throw new BizException(BasicCode.PARAM_NULL, "订单");
        // 卖家配送才支持ERP调度
        return DeliveryWayEnum.SELLER_DELIVERY.code().equals(orderInfo.getDeliverWay());
    }

    @Override
    public ERPOverSoldVerifyResponseDTO overSellConfirm(ERPOverSoldDTO erpOverSoldDTO) throws Exception {
        return null;
    }

    @Override
    public boolean hasErpOrder(String sellId, String configCode) {
        MemberConfigDTO memberConfigDTO = memberConfigService.findByMemberIdAndKeyCode(sellId, configCode);
        if (memberConfigDTO == null) {
            return false;
        }
        return "1".equals(memberConfigDTO.getValue()) ||
                "true".equalsIgnoreCase(memberConfigDTO.getValue());
    }

    @Override
    public void sellerConfirmTakeInfoToErp(OrderInfo orderInfo, String takeCode) {
        // TODO Implement this method
    }

    @Override
    public boolean sellerCloseTakeInfoToErp(String takeCode) {
        return false;
    }

    @Override
    public void erpOverslodSupplement(String orderId, String operator) {
        // TODO Implement this method
    }

    @Override
    public boolean doERPOrderAdjust(List<ERPOrderAdjustDTO> erpOrderAdjustDTOList) {
        return false;
    }

    @Override
    public boolean updateTakeEffectiveTime(OrderDTO orderDTO) {
        return false;
    }

    @Override
    public void syncBillLogs(OrderPayinfo payinfo, OrderPayinfoDetail payDetail) {
        // TODO Implement this method
    }

    @Override
    public void notifyErpIncreaseCreditAmount(String payinfoId) {
        // TODO Implement this method
    }


}
