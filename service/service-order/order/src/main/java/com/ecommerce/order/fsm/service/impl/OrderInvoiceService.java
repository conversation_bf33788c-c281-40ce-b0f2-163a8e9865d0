package com.ecommerce.order.fsm.service.impl;

import com.alibaba.fastjson.JSON;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BasicRuntimeException;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.utils.BeanConvertUtils;
import com.ecommerce.common.utils.CommonConstants;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.goods.api.dto.GoodsDTO;
import com.ecommerce.goods.api.dto.contract.ContractOption;
import com.ecommerce.goods.api.enums.DeliveryWayEnum;
import com.ecommerce.goods.api.service.IContractService;
import com.ecommerce.goods.api.service.IGoodsService;
import com.ecommerce.logistics.api.enums.CarrierTypeEnum;
import com.ecommerce.member.api.dto.member.MemberDetailDTO;
import com.ecommerce.member.api.dto.servicearea.MemberServiceAreaItemDTO;
import com.ecommerce.member.api.service.IMemberAreaService;
import com.ecommerce.member.api.service.IMemberService;
import com.ecommerce.order.api.dto.*;
import com.ecommerce.order.api.enums.*;
import com.ecommerce.order.biz.IOrderTaxinfoBiz;
import com.ecommerce.order.biz.fsm.IOrderInfoBiz;
import com.ecommerce.order.biz.fsm.IOrderItemBiz;
import com.ecommerce.order.dao.vo.OrderInfo;
import com.ecommerce.order.dao.vo.OrderItem;
import com.ecommerce.order.fsm.service.IOrderInvoiceService;
import com.ecommerce.pay.api.v2.dto.electricInvoiceAccount.InvoiceAccountDTO;
import com.ecommerce.pay.api.v2.dto.invoice.InvoiceApplyAddDTO;
import com.ecommerce.pay.api.v2.dto.invoice.InvoiceApplyAddItem;
import com.ecommerce.pay.api.v2.dto.invoice.extra.InvoiceOrderExtra;
import com.ecommerce.pay.api.v2.enums.invoice.InvoiceApplyStatusEnum;
import com.ecommerce.pay.api.v2.enums.invoice.InvoiceBizTypeEnum;
import com.ecommerce.pay.api.v2.service.IElectricInvoiceAccountService;
import com.ecommerce.pay.api.v2.service.IElectricInvoiceApplyService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * * Date: Create in 下午2:06 20/6/17
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderInvoiceService implements IOrderInvoiceService {
    private final IOrderInfoBiz orderInfoBiz;
    private final IOrderItemBiz orderItemBiz;
    private final IMemberAreaService memberAreaService;
    private final IMemberService memberService;
    private final IGoodsService goodsService;
    private final IElectricInvoiceAccountService electricInvoiceAccountService;
    private final IElectricInvoiceApplyService invoiceApplyService;
    private final IOrderTaxinfoBiz orderTaxinfoBiz;
    private final IContractService contractService;

    private static final String ORDER_ID = "orderId";
    private static final String DEL_FLG = "delFlg";
    private static final String PATTERN = "yyyy-MM-dd HH:mm:ss";

    @Override
    public ApplyOrderInvoiceResponseDTO applyOrderInvoice(ApplyOrderInvoiceRequestDTO req, String operator) {
        //参数校验
        if (CollectionUtils.isEmpty(req.getOrderIds())) {
            throw new BizException(BasicCode.PARAM_NULL, "订单编号");
        }
        //初始化结果
        ApplyOrderInvoiceResponseDTO applyOrderInvoiceResponseDTO = new ApplyOrderInvoiceResponseDTO();
        applyOrderInvoiceResponseDTO.setOrderCount(req.getOrderIds().size());
        List<ApplyOrderInvoiceDTO> invoiceList = Lists.newArrayList();

        List<OrderInfo> orderInfos = getOrderInfoListByIds(req.getOrderIds());

        List<UninvoicedOrderDTO> list = Lists.newArrayList();
        List<String> goodsIds = Lists.newArrayList();
        Map<String, BigDecimal> goodsCostMap = Maps.newHashMap();
        Map<String, BigDecimal> logisticsCostMap = Maps.newHashMap();

        if (CollectionUtils.isNotEmpty(orderInfos)) {
            orderInfos.forEach(orderInfo -> {
                UninvoicedOrderDTO dto = new UninvoicedOrderDTO();
                BeanUtils.copyProperties(orderInfo, dto);
                if(com.ecommerce.goods.api.enums.DeliveryWayEnum.PLATFORM_DELIVERY.code().equals(orderInfo.getDeliverWay())){
                    dto.setInvoiceInstitution(OrderInvoiceInstitution.SECOND.getCode());
                } else if (CsStringUtils.equals(com.ecommerce.goods.api.enums.DeliveryWayEnum.BUYER_TAKE.code(), orderInfo.getDeliverWay())) {
                    dto.setInvoiceInstitution(OrderInvoiceInstitution.ONE.getCode());
                } else {
                    //查询合同
                    List<ContractOption> contractOptions = contractService.queryContractOptions(Sets.newHashSet(orderInfo.getDealsId()));
                    if (CollectionUtils.isNotEmpty(contractOptions) && CsStringUtils.equals(contractOptions.get(0).getBillType(), "02")) {
                        dto.setInvoiceInstitution(OrderInvoiceInstitution.SECOND.getCode());
                    } else {
                        dto.setInvoiceInstitution(OrderInvoiceInstitution.ONE.getCode());
                    }
                }
                list.add(dto);
                //计算费用
                calculateCost(dto,goodsCostMap,logisticsCostMap);
            });
            //设置订单子项
            batchSetOrderItemDTOs(list,goodsIds);
        }

        applyOrderInvoiceResponseDTO.setGoodsCount(goodsIds.size());

        //根据map生成ApplyOrderInvoiceDTO
        putApplyOrderInvoiceToList(goodsCostMap,invoiceList,OrderInvoiceType.GOODS.getCode());
        putApplyOrderInvoiceToList(logisticsCostMap,invoiceList,OrderInvoiceType.LOGISTICS.getCode());

        applyOrderInvoiceResponseDTO.setList(invoiceList);
        return applyOrderInvoiceResponseDTO;
    }

    @Override
    public boolean doOrderInvoiceCallBack(OrderInvoiceCallBackDTO callBackDTO) {
        log.info("doOrderInvoiceCallBack====>{}", JSON.toJSONString(callBackDTO));
        if(CsStringUtils.isNullOrBlank(callBackDTO.getOrderCode())){
            throw new BizException(BasicCode.PARAM_NULL, "订单编号");
        }
        OrderInfo orderInfo = this.getByCode(callBackDTO.getOrderCode());
        OrderInfo updateOrder = new OrderInfo();
        BeanUtils.copyProperties(orderInfo,updateOrder);
        if (CsStringUtils.isNotBlank(callBackDTO.getInvoiceStatus1())) {//交易开票回调
            orderInvoiceTransactionCallBack(callBackDTO.getInvoiceStatus1(),updateOrder);
        } else if (CsStringUtils.isNotBlank(callBackDTO.getInvoiceStatus2())) {//物流开票回调
            orderInvoiceLogisticCallBack(callBackDTO.getInvoiceStatus2(),updateOrder);
        }else{
            throw new BizException(BasicCode.PARAM_NULL, "开票状态");
        }

        String invoiceInstitution = null;

        if (CsStringUtils.equals(DeliveryWayEnum.PLATFORM_DELIVERY.code(), orderInfo.getDeliverWay())) {
            invoiceInstitution = OrderInvoiceInstitution.SECOND.getCode();
        } else if (CsStringUtils.equals(DeliveryWayEnum.BUYER_TAKE.code(), orderInfo.getDeliverWay())) {
            invoiceInstitution = OrderInvoiceInstitution.ONE.getCode();
        } else {
            //查询合同
            List<ContractOption> contractOptions = contractService.queryContractOptions(Sets.newHashSet(orderInfo.getDealsId()));
            if (CollectionUtils.isNotEmpty(contractOptions) && CsStringUtils.equals(contractOptions.get(0).getBillType(), "02")) {
                invoiceInstitution = OrderInvoiceInstitution.SECOND.getCode();
            } else {
                invoiceInstitution = OrderInvoiceInstitution.ONE.getCode();
            }
        }

        if (CsStringUtils.equals(invoiceInstitution, OrderInvoiceInstitution.SECOND.getCode())) {//两票(返回status2)
            //总开票状态
            twoInvoiceSetInvoiceStatus(updateOrder);
        }else {//一票制
            oneInvoiceSetInvoiceStatus(updateOrder);
        }
        updateOrder.setUpdateTime(new Date());
        orderInfoBiz.updateSelective(updateOrder);
        return true;
    }

    @Override
    public boolean confirmOrderInvoice(ConfirmOrderInvoiceReqDTO reqDTO){
        log.info("confirmOrderInvoice=====》{}",reqDTO);
        //参数校验
        if (CollectionUtils.isEmpty(reqDTO.getOrderIds())) {
            throw new BizException(BasicCode.PARAM_NULL, "订单编号");
        }
        String buyerMemberId = null;

        List<OrderInfo> orderInfos = getOrderInfoListByIds(reqDTO.getOrderIds());

        Map<String,List<UninvoicedOrderDTO>> goodsMap = new HashMap<>();//交易
        Map<String,List<UninvoicedOrderDTO>> logisticsMap = new HashMap<>();//物流
        Map<String,InvoiceApplyAddDTO> goodsInvoiceMap = new HashMap<>();
        Map<String,InvoiceApplyAddDTO> logisticsInvoiceMap = new HashMap<>();

        buyerMemberId = orderInfosLogic(reqDTO, orderInfos, buyerMemberId, logisticsMap, logisticsInvoiceMap, goodsMap, goodsInvoiceMap);

        log.info("goodsMap=======>keys:[{}]",goodsMap.keySet().toString());
        log.info("logisticsMap=======>keys:[{}]",logisticsMap.keySet().toString());
        List<InvoiceApplyAddDTO> invoiceApplyAddDTOList = new ArrayList<>();


        mapToInvoiceApplyAddDTOList(goodsMap,goodsInvoiceMap,invoiceApplyAddDTOList,false);
        mapToInvoiceApplyAddDTOList(logisticsMap,logisticsInvoiceMap,invoiceApplyAddDTOList,true);


        try{
            log.info("confirmOrderInvoice============>start![{}]",invoiceApplyAddDTOList);
            ItemResult<Void> result = invoiceApplyService.batchApplyInvoice(invoiceApplyAddDTOList);
            if(result.isSuccess()) {
                log.info("confirmOrderInvoice=========> Success!");
                //更新订单开票状态(已申请)
                updateSelective(orderInfos);

                //开票提交成功,更新到订单中
                saveOrderTaxInfo(reqDTO,buyerMemberId);

                return true;
            } else {
                return false;
            }
        } catch (Exception e) {
            throw new BizException(BasicCode.UNKNOWN_ERROR,e);
        }
    }

    private String orderInfosLogic(ConfirmOrderInvoiceReqDTO reqDTO, List<OrderInfo> orderInfos, String buyerMemberId, Map<String, List<UninvoicedOrderDTO>> logisticsMap, Map<String, InvoiceApplyAddDTO> logisticsInvoiceMap, Map<String, List<UninvoicedOrderDTO>> goodsMap, Map<String, InvoiceApplyAddDTO> goodsInvoiceMap) {
        if(CollectionUtils.isNotEmpty(orderInfos)){
            for (OrderInfo orderInfo : orderInfos) {
                if (CsStringUtils.isBlank(buyerMemberId)) {
                    buyerMemberId = orderInfo.getBuyerId();
                }
                UninvoicedOrderDTO dto = new UninvoicedOrderDTO();
                BeanUtils.copyProperties(orderInfo,dto);
                setInvoiceInstitution(orderInfo, dto);
                //查询订单子项
                List<OrderItem> orderItems = orderItemBiz.findByOrderId(orderInfo.getOrderId());
                List<UninvoicedOrderItemDTO> uninvoicedOrderItems = BeanConvertUtils.convertList(orderItems,UninvoicedOrderItemDTO.class);
                dto.setUninvoicedOrderItemDTOs(uninvoicedOrderItems);

                //买家类型
                String buyerType="01";
                if(!orderInfo.getBuyerType().equals("0"))
                    buyerType= AccountTypeEnum.PERSON.getCode();

                MemberDetailDTO buyer = memberService.findMemberById(orderInfo.getBuyerId());

                if(dto.getInvoiceInstitution().equals(OrderInvoiceInstitution.SECOND.getCode())){//两票票制，交易发票
                    setLogisticsMap(dto, reqDTO,orderInfo, logisticsMap, logisticsInvoiceMap,buyer,buyerType);
                }

                //票制
                if (!goodsMap.containsKey(orderInfo.getSellerId())) {
                    putInvoiceApplyAddDTOToMap(goodsMap, goodsInvoiceMap, reqDTO,buyer,orderInfo.getSellerId(),orderInfo.getSellerName(),orderInfo.getBuyerId(),orderInfo.getBuyerName(),buyerType,false);
                }
                List<UninvoicedOrderDTO> goodsInvoiceOrderList = goodsMap.get(orderInfo.getSellerId());
                goodsInvoiceOrderList.add(dto);

            }
        }
        return buyerMemberId;
    }

    private void setInvoiceInstitution(OrderInfo orderInfo, UninvoicedOrderDTO dto) {
        if (CsStringUtils.equals(DeliveryWayEnum.PLATFORM_DELIVERY.code(), orderInfo.getDeliverWay())) {
            dto.setInvoiceInstitution(OrderInvoiceInstitution.SECOND.getCode());
        } else if (CsStringUtils.equals(DeliveryWayEnum.BUYER_TAKE.code(), orderInfo.getDeliverWay())) {
            dto.setInvoiceInstitution(OrderInvoiceInstitution.ONE.getCode());
        } else {
            //查询合同
            List<ContractOption> contractOptions = contractService.queryContractOptions(Sets.newHashSet(orderInfo.getDealsId()));
            if (CollectionUtils.isNotEmpty(contractOptions) && CsStringUtils.equals(contractOptions.get(0).getBillType(), "02")) {
                dto.setInvoiceInstitution(OrderInvoiceInstitution.SECOND.getCode());
            } else {
                dto.setInvoiceInstitution(OrderInvoiceInstitution.ONE.getCode());
            }
        }
    }

    private void updateSelective(List<OrderInfo> orderInfos) {
        orderInfos.forEach(order ->{
            order.setInvoiceStatus(OrderInvoiceStatus.APPLIED.getCode());
            if (DeliveryWayEnum.PLATFORM_DELIVERY.code().equals(order.getDeliverWay())) {
                if(order.getInvoiceStatus1() == null || !order.getInvoiceStatus1().equals(OrderInvoiceStatus.INVOICED.getCode())) {
                    order.setInvoiceStatus1(OrderInvoiceStatus.APPLIED.getCode());
                }
                if(order.getInvoiceStatus2() == null || !order.getInvoiceStatus2().equals(OrderInvoiceStatus.INVOICED.getCode())) {
                    order.setInvoiceStatus2(OrderInvoiceStatus.APPLIED.getCode());
                }
            } else {
                order.setInvoiceStatus1(OrderInvoiceStatus.APPLIED.getCode());
            }

            order.setUpdateTime(new Date());
            orderInfoBiz.updateSelective(order);

        });
    }

    public OrderInfo getByCode(String orderSn){
        Condition condition = new Condition(OrderInfo.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("orderCode",orderSn);
        criteria.andEqualTo(DEL_FLG, CommonConstants.DEL_FLAG_FALSE);
        List<OrderInfo> list = orderInfoBiz.findByCondition(condition);
        if (list != null && list.size() > 0) {
            return list.get(0);
        }
        return null;
    }

    /**
     * 判断交易/物流是否已经开票（开票完成/申请）
     * @param orderInvoiceStatus
     * @return
     */
    private boolean hasOrderInvoice(String orderInvoiceStatus){
        return CsStringUtils.isNotBlank(orderInvoiceStatus) &&
                (orderInvoiceStatus.equals(OrderInvoiceStatus.INVOICED.getCode())
                        || orderInvoiceStatus.equals(OrderInvoiceStatus.APPLIED.getCode()));
    }

    @Override
    public OrderDTO queryOrderByInvoiceId(String invoiceId) {
        return null;
    }

    /**
     * 批量设置订单子项
     * @param uninvoicedOrderDTOList
     */
    private void batchSetOrderItemDTOs(List<UninvoicedOrderDTO> uninvoicedOrderDTOList,List<String> goodsIds){
        if (CollectionUtils.isEmpty(uninvoicedOrderDTOList)) {
            return;
        }
        List<String> orderIds = uninvoicedOrderDTOList.stream().map(UninvoicedOrderDTO::getOrderId).toList();
        Condition condition = new Condition(OrderItem.class);
        condition.createCriteria()
                .andIn(ORDER_ID, orderIds)
                .andEqualTo(DEL_FLG, false);
        List<OrderItem> orderItems = orderItemBiz.findByCondition(condition);
        if (CollectionUtils.isEmpty(orderItems)) {
            return;
        }
        Map<String, List<UninvoicedOrderItemDTO>> orderId2ItemsListMap = orderItems.stream()
                .filter(item -> CsStringUtils.isNotBlank(item.getOrderId()))
                .map(item -> {
                    UninvoicedOrderItemDTO itemDTO = new UninvoicedOrderItemDTO();
                    BeanUtils.copyProperties(item,itemDTO);
                    if(!goodsIds.contains(item.getGoodsId())){
                        goodsIds.add(item.getGoodsId());
                    }
                    return itemDTO;
                })
                .collect(Collectors.groupingBy(UninvoicedOrderItemDTO::getOrderId));
        for (UninvoicedOrderDTO uninvoicedOrderDTO : uninvoicedOrderDTOList) {
            List<UninvoicedOrderItemDTO> uninvoicedOrderDTOS1 = orderId2ItemsListMap.get(uninvoicedOrderDTO.getOrderId());
            uninvoicedOrderDTO.setUninvoicedOrderItemDTOs(uninvoicedOrderDTOS1);
        }
    }

    /**
     * 计算费用
     * @param dto
     * @param goodsCostMap
     * @param logisticsCostMap
     */
    private void calculateCost(UninvoicedOrderDTO dto,Map<String, BigDecimal> goodsCostMap,Map<String, BigDecimal> logisticsCostMap){
        if(OrderInvoiceInstitution.ONE.getCode().equals(dto.getInvoiceInstitution())){
            setSellerId(dto, goodsCostMap);
        } else if (OrderInvoiceInstitution.SECOND.getCode().equals(dto.getInvoiceInstitution())){
            //商品费用开票
            if (!OrderInvoiceStatus.INVOICED.getCode().equals(dto.getInvoiceStatus1())
                    && !OrderInvoiceStatus.APPLIED.getCode().equals(dto.getInvoiceStatus1())){
                if(goodsCostMap.containsKey(dto.getSellerId())){
                    //这里需要开发人员来确认
                    goodsCostMap.put(dto.getSellerId(),goodsCostMap.get(dto.getSellerId()).add(dto.getRealtimeOrderAmount().subtract(dto.getRealtimeLogisticAmount())));
                }else {
                    goodsCostMap.put(dto.getSellerId(),dto.getRealtimeOrderAmount().subtract(dto.getRealtimeLogisticAmount()));
                }
            }
            //物流费用开票
            carrierIdLogic(dto, logisticsCostMap);
        }
    }

    private void carrierIdLogic(UninvoicedOrderDTO dto, Map<String, BigDecimal> logisticsCostMap) {
        if(!OrderInvoiceStatus.INVOICED.getCode().equals(dto.getInvoiceStatus2())
                && !OrderInvoiceStatus.APPLIED.getCode().equals(dto.getInvoiceStatus2())){
            //查询承运商
            MemberServiceAreaItemDTO queryDTO=new MemberServiceAreaItemDTO();
            queryDTO.setProvinceCode(dto.getProvinceCode());
            queryDTO.setCityCode(dto.getCityCode());
            queryDTO.setCarrierType(CarrierTypeEnum.PLATFORM_SELF.getCode());
            List<String> memberIds = memberAreaService.findSociologyPersonIdOrCarrierIdByRegionCode(queryDTO);
            String carrierId="";
            if(CollectionUtils.isNotEmpty(memberIds)) {
                carrierId = memberIds.get(0);
            }

            if(logisticsCostMap.containsKey(carrierId)){
                //这里需要开发人员来确认
                logisticsCostMap.put(carrierId, logisticsCostMap.get(carrierId).add(dto.getRealtimeLogisticAmount()));
            }else {
                logisticsCostMap.put(carrierId, dto.getRealtimeLogisticAmount());
            }

        }
    }

    private static void setSellerId(UninvoicedOrderDTO dto, Map<String, BigDecimal> goodsCostMap) {
        if(goodsCostMap.containsKey(dto.getSellerId())){
            //这里需要开发人员来确认
            goodsCostMap.put(dto.getSellerId(), goodsCostMap.get(dto.getSellerId()).add(dto.getRealtimeOrderAmount()));
        }else {
            goodsCostMap.put(dto.getSellerId(), dto.getRealtimeOrderAmount());
        }
    }

    /**
     * 根据ids查找会员详细信息
     * @param ids
     */
    private Map<String, MemberDetailDTO> findMemberDetailsByIds(List<String> ids){
        List<MemberDetailDTO> memberDetailDTOList = memberService.findMemberDetailsByIds(ids);
        return memberDetailDTOList.stream().collect(Collectors.toMap(MemberDetailDTO::getMemberId, Function.identity()));
    }

    /**
     * 根据orderIds查找未开票订单
     * @param ids
     */
    private List<OrderInfo> getOrderInfoListByIds(List<String> ids){
        //查询选择的订单信息
        Condition orderInfoCondition = new Condition(OrderInfo.class);
        Example.Criteria orderInfonCriteria = orderInfoCondition.createCriteria();
        orderInfonCriteria.andIn(ORDER_ID,ids);
        //订单状态
        orderInfonCriteria.andIn("orderStatus", Lists.newArrayList(
                OrderStatusEnum.CLOSED.code(),
                OrderStatusEnum.COMPLETED.code()));
        orderInfonCriteria.andEqualTo(DEL_FLG,false);
        orderInfoCondition.orderBy("updateTime").asc();
        return orderInfoBiz.findByCondition(orderInfoCondition);
    }

    /**
     * 发票申请实体
     */
    private void putInvoiceApplyAddDTOToMap(Map<String,List<UninvoicedOrderDTO>> uninvoiceOrderMap,Map<String, InvoiceApplyAddDTO> invoiceApplyAddDTOMap,ConfirmOrderInvoiceReqDTO reqDTO,MemberDetailDTO buyer,
                                            String sellerId,String sellerName,String buyerId,String buyerName,String buyerType,Boolean isSpecial){
        List<UninvoicedOrderDTO> uninvoicedOrderDTOS = new LinkedList<>();
        uninvoiceOrderMap.put(sellerId, uninvoicedOrderDTOS);
        //交易发票申请实体
        InvoiceApplyAddDTO invoiceApplyAddDTO = new InvoiceApplyAddDTO();
        BeanUtils.copyProperties(reqDTO,invoiceApplyAddDTO);
        invoiceApplyAddDTO.setSellerId(sellerId);
        invoiceApplyAddDTO.setSellerName(sellerName);
        invoiceApplyAddDTO.setBuyerId(buyerId);
        invoiceApplyAddDTO.setBuyerName(buyerName);
        invoiceApplyAddDTO.setBuyerType(buyerType);
        if(Boolean.TRUE.equals(isSpecial)){
            invoiceApplyAddDTO.setIsSpecial(reqDTO.getIsSpecial());
        }

        if(buyer != null){
            invoiceApplyAddDTO.setBuyerProvince(buyer.getProvinceCode());
        }
        invoiceApplyAddDTOMap.put(sellerId, invoiceApplyAddDTO);
    }

    /**
     * 根据map生成ApplyOrderInvoiceDTO
     */
    private void putApplyOrderInvoiceToList(Map<String, BigDecimal> decimalMap,List<ApplyOrderInvoiceDTO> invoiceList,String invoiceType){
        if(MapUtils.isEmpty(decimalMap)){
            return;
        }

        Map<String, InvoiceAccountDTO> invoioceAccountMap = electricInvoiceAccountService.queryInvoiceAccountStatus(new ArrayList<>(decimalMap.keySet()));
        Map<String, MemberDetailDTO> memberDetailsByIds = findMemberDetailsByIds(new ArrayList<>(decimalMap.keySet()));
        decimalMap.keySet().forEach(key ->{
            if(decimalMap.get(key).compareTo(BigDecimal.ZERO) > 0){
                ApplyOrderInvoiceDTO applyOrderInvoiceDTO = new ApplyOrderInvoiceDTO();
                BigDecimal costDec = decimalMap.get(key);
                applyOrderInvoiceDTO.setSellerId(key);
                MemberDetailDTO member = memberDetailsByIds.get(key);
                String memberShortName = "";
                if(member != null){
                    memberShortName = member.getMemberShortName();
                    applyOrderInvoiceDTO.setSellerName(member.getMemberName());
                    applyOrderInvoiceDTO.setSellerNikeName(memberShortName);
                }

                if(invoioceAccountMap.get(key) != null){
                    applyOrderInvoiceDTO.setSellerTaxNo(invoioceAccountMap.get(key).getTaxNo());
                    applyOrderInvoiceDTO.setInvoiceAmount(costDec.setScale(2, RoundingMode.HALF_UP));
                    applyOrderInvoiceDTO.setInvoiceType(invoiceType);
                    invoiceList.add(applyOrderInvoiceDTO);
                }else{
                    throw new BasicRuntimeException("操作失败：交易开票方"+ memberShortName +"未开户！");
                }

            }
        });
    }

    /**
     * 物流开票回调
     * @param invoiceStatus
     * @param updateOrder
     */
    private void orderInvoiceLogisticCallBack(String invoiceStatus,OrderInfo updateOrder){
        //物流开票状态
        if(InvoiceApplyStatusEnum.INVOICE_SUCCESS.getCode().equals(invoiceStatus)){
            updateOrder.setInvoiceStatus2(OrderInvoiceStatus.INVOICED.getCode());
        }else if(InvoiceApplyStatusEnum.AUDIT_REJECT.getCode().equals(invoiceStatus)){//交易驳回
            updateOrder.setInvoiceStatus2(OrderInvoiceStatus.INVOICE_FAIL.getCode());
        }else{//交易已红冲
            updateOrder.setInvoiceStatus2(OrderInvoiceStatus.WRITE_OFF.getCode());
        }
    }

    /**
     * 交易开票回调
     * @param invoiceStatus
     * @param updateOrder
     */
    private void orderInvoiceTransactionCallBack(String invoiceStatus,OrderInfo updateOrder){
        //交易开票状态
        if(InvoiceApplyStatusEnum.INVOICE_SUCCESS.getCode().equals(invoiceStatus)){
            updateOrder.setInvoiceStatus1(OrderInvoiceStatus.INVOICED.getCode());
        }else if(InvoiceApplyStatusEnum.AUDIT_REJECT.getCode().equals(invoiceStatus)){//交易驳回
            updateOrder.setInvoiceStatus1(OrderInvoiceStatus.INVOICE_FAIL.getCode());
        }else{//交易已红冲
            updateOrder.setInvoiceStatus1(OrderInvoiceStatus.WRITE_OFF.getCode());
        }
    }

    /**
     * 一票制设置开票状态
     * @param updateOrder
     */
    private void oneInvoiceSetInvoiceStatus(OrderInfo updateOrder){
        if(updateOrder.getInvoiceStatus1().equals(OrderInvoiceStatus.INVOICED.getCode())){
            updateOrder.setInvoiceStatus(OrderInvoiceStatus.INVOICED.getCode());
        }else if(updateOrder.getInvoiceStatus1().equals(OrderInvoiceStatus.WRITE_OFF.getCode())){//已红冲
            updateOrder.setInvoiceStatus(OrderInvoiceStatus.WRITE_OFF.getCode());
        }else{
            updateOrder.setInvoiceStatus(OrderInvoiceStatus.INVOICE_FAIL.getCode());
        }
    }

    /**
     * 两票制设置开票状态
     * @param updateOrder
     */
    private void twoInvoiceSetInvoiceStatus(OrderInfo updateOrder){
        //总开票状态
        if(updateOrder.getInvoiceStatus1().equals(OrderInvoiceStatus.INVOICED.getCode()) && updateOrder.getInvoiceStatus2().equals(OrderInvoiceStatus.INVOICED.getCode())) {
            updateOrder.setInvoiceStatus(OrderInvoiceStatus.INVOICED.getCode());
        }else if(updateOrder.getInvoiceStatus1().equals(OrderInvoiceStatus.INVOICED.getCode()) || updateOrder.getInvoiceStatus2().equals(OrderInvoiceStatus.INVOICED.getCode())){
            updateOrder.setInvoiceStatus(OrderInvoiceStatus.INVOICEING.getCode());
        }else if(updateOrder.getInvoiceStatus1().equals(OrderInvoiceStatus.WRITE_OFF.getCode()) && updateOrder.getInvoiceStatus2().equals(OrderInvoiceStatus.WRITE_OFF.getCode())){
            updateOrder.setInvoiceStatus(OrderInvoiceStatus.WRITE_OFF.getCode());
        }else{
            updateOrder.setInvoiceStatus(OrderInvoiceStatus.INVOICE_FAIL.getCode());
        }
    }

    private void setLogisticsMap(UninvoicedOrderDTO dto,ConfirmOrderInvoiceReqDTO reqDTO,OrderInfo orderInfo,Map<String,List<UninvoicedOrderDTO>> logisticsMap,Map<String,InvoiceApplyAddDTO> logisticsInvoiceMap,MemberDetailDTO buyer,String buyerType){
        String carrierId="";//物流承运商Id
        String carrierName="";
        //查询承运商
        MemberServiceAreaItemDTO queryDTO = new MemberServiceAreaItemDTO();
        queryDTO.setProvinceCode(orderInfo.getProvinceCode());
        queryDTO.setCityCode(orderInfo.getCityCode());
        queryDTO.setCarrierType(CarrierTypeEnum.PLATFORM_SELF.getCode());
        List<String> memberIds = memberAreaService.findSociologyPersonIdOrCarrierIdByRegionCode(queryDTO);

        if(CollectionUtils.isNotEmpty(memberIds)) {
            carrierId = memberIds.get(0);
            MemberDetailDTO memberDetailDTO = memberService.findMemberById(carrierId);
            if(memberDetailDTO != null)
                carrierName = memberDetailDTO.getMemberName();
        }

        if (!logisticsMap.containsKey(carrierId)) {
            putInvoiceApplyAddDTOToMap(logisticsMap,logisticsInvoiceMap,reqDTO,buyer,carrierId,carrierName,orderInfo.getBuyerId(),orderInfo.getBuyerName(),buyerType,true);
        }
        List<UninvoicedOrderDTO> logisticsInvoiceOrderList = logisticsMap.get(carrierId);
        logisticsInvoiceOrderList.add(dto);
    }

    private void mapToInvoiceApplyAddDTOList(Map<String,List<UninvoicedOrderDTO>> map,Map<String,InvoiceApplyAddDTO> invoiceMap,List<InvoiceApplyAddDTO> invoiceApplyAddDTOList,Boolean isLogistics){
        if(MapUtils.isEmpty(map)){
            return;
        }

        map.keySet().forEach(key->{
            InvoiceApplyAddDTO dto = invoiceMap.get(key);
            List<InvoiceApplyAddItem> invoiceApplyAddItems=new ArrayList<>();
            if (CollectionUtils.isNotEmpty(map.get(key))) {
                if(isLogistics){
                    //物流费用开票
                    logisticsIMapInvoice(map,key,invoiceApplyAddItems);
                }else {
                    //交易费用开票
                    goodsMapInvoice(map,key,invoiceApplyAddItems);
                }

                if (CollectionUtils.isNotEmpty(invoiceApplyAddItems)) {
                    dto.setInvoiceApplyAddItemList(invoiceApplyAddItems);
                    invoiceApplyAddDTOList.add(dto);
                }

            }
        });
    }

    /**
     * 交易费用开票
     * @param goodsMap
     * @param key
     * @param invoiceApplyAddItems
     */
    private void goodsMapInvoice( Map<String,List<UninvoicedOrderDTO>> goodsMap,String key,List<InvoiceApplyAddItem> invoiceApplyAddItems){
        goodsMap.get(key).forEach(order -> {
            log.info("goodsUninvoicedOrderDTO:" + JSON.toJSONString(order));
            //判断订单交易发票是否已经开票
            if (!hasOrderInvoice(order.getInvoiceStatus1())) {
                //判断是否有物流费
                if (order.getRealtimeLogisticAmount().compareTo(BigDecimal.ZERO) > 0
                        && order.getInvoiceInstitution().equals(OrderInvoiceInstitution.ONE.getCode())) {
                    addInvoiceApplyAddItems(order,OrderInvoiceInstitution.ONE.getCode(),invoiceApplyAddItems);
                }

                //判断是否有未开票订单子项
                itemsAddInvoiceApplyAddItems(order,invoiceApplyAddItems);

            }
        });
    }

    /**
     * 物流费用开票
     * @param logisticsMap
     * @param key
     * @param invoiceApplyAddItems
     */
    private void logisticsIMapInvoice( Map<String,List<UninvoicedOrderDTO>> logisticsMap,String key,List<InvoiceApplyAddItem> invoiceApplyAddItems){
        logisticsMap.get(key).forEach(order ->{
            log.info("logisticsUninvoicedOrderDTO:" + JSON.toJSONString(order));
            //判断订单物流发票是否已经开票
            if ((!hasOrderInvoice(order.getInvoiceStatus2()))
                //判断订单是否有物流费
                && (order.getRealtimeLogisticAmount().compareTo(BigDecimal.ZERO) > 0)) {
                    addInvoiceApplyAddItems(order,OrderInvoiceInstitution.SECOND.getCode(),invoiceApplyAddItems);
            }
        });
    }

    /**
     * 生成invoiceApplyAddItems
     * @param order
     * @param ticketRule
     * @param invoiceApplyAddItems
     */
    private void addInvoiceApplyAddItems(UninvoicedOrderDTO order,String ticketRule,List<InvoiceApplyAddItem> invoiceApplyAddItems){
        InvoiceApplyAddItem invoiceApplyAddItem = new InvoiceApplyAddItem();
        invoiceApplyAddItem.setBizNo(order.getOrderCode());
        invoiceApplyAddItem.setBizType(InvoiceBizTypeEnum.LOGISTICS_INVOICE.getCode());
        invoiceApplyAddItem.setItemCategory("TC_CATEGORY");
        invoiceApplyAddItem.setItemCode("TC_CODE");
        invoiceApplyAddItem.setItemName("运输费");
        invoiceApplyAddItem.setItemNum(new BigDecimal(1));
        invoiceApplyAddItem.setItemPrice(order.getRealtimeLogisticAmount());
        invoiceApplyAddItem.setItemUnit("次");
        invoiceApplyAddItem.setItemMode("");
        invoiceApplyAddItem.setAmount(BigDecimal.ZERO);
        invoiceApplyAddItem.setLogisticsAmount(order.getRealtimeLogisticAmount());
        InvoiceOrderExtra orderExtra = new InvoiceOrderExtra();
        SimpleDateFormat dateFormat = new SimpleDateFormat(PATTERN);
        orderExtra.setStartTime(dateFormat.format(order.getCreateTime()));
        orderExtra.setEndTime(dateFormat.format(order.getUpdateTime()));
        orderExtra.setTicketRule(ticketRule);
        invoiceApplyAddItem.setInvoiceOrderExtra(orderExtra);
        invoiceApplyAddItems.add(invoiceApplyAddItem);
    }

    /**
     * 订单子项生成invoiceApplyAddItems
     * @param order
     * @param invoiceApplyAddItems
     */
    private void itemsAddInvoiceApplyAddItems(UninvoicedOrderDTO order,List<InvoiceApplyAddItem> invoiceApplyAddItems){
        if (CollectionUtils.isEmpty(order.getUninvoicedOrderItemDTOs())) {
            return;
        }

        order.getUninvoicedOrderItemDTOs().forEach(orderItem ->{
            if (orderItem.getRealtimeResPrice().compareTo(BigDecimal.ZERO) > 0) {
                InvoiceApplyAddItem invoiceApplyAddItem = new InvoiceApplyAddItem();
                invoiceApplyAddItem.setBizNo(order.getOrderCode());
                invoiceApplyAddItem.setBizType(InvoiceBizTypeEnum.TRADE_INVOICE.getCode());
                invoiceApplyAddItem.setItemCategory(orderItem.getCategoryCode());
                //查询商品Code(by goodsId)
                ItemResult<GoodsDTO> goodsDTOItemResult = goodsService.getGoodsInfo(orderItem.getGoodsId());
                String goodsCode = "";
                if (goodsDTOItemResult != null && goodsDTOItemResult.getData() != null) {
                    goodsCode = goodsDTOItemResult.getData().getGoodsCode();
                }
                invoiceApplyAddItem.setItemCode(goodsCode);
                invoiceApplyAddItem.setItemName(orderItem.getGoodsName());
                invoiceApplyAddItem.setItemNum(orderItem.getItemSendQuantity());
                invoiceApplyAddItem.setItemPrice(orderItem.getActualUnitPrice());
                invoiceApplyAddItem.setItemUnit(orderItem.getUnits());
                invoiceApplyAddItem.setItemMode("");
                invoiceApplyAddItem.setAmount(orderItem.getRealtimeResPrice());
                invoiceApplyAddItem.setLogisticsAmount(BigDecimal.ZERO);
                InvoiceOrderExtra orderExtra = new InvoiceOrderExtra();
                SimpleDateFormat dateFormat = new SimpleDateFormat(PATTERN);
                orderExtra.setStartTime(dateFormat.format(order.getCreateTime()));
                orderExtra.setEndTime(dateFormat.format(order.getUpdateTime()));
                if(order.getInvoiceInstitution().equals(OrderInvoiceInstitution.SECOND.getCode())){
                    orderExtra.setTicketRule(OrderInvoiceInstitution.SECOND.getCode());
                }else{
                    orderExtra.setTicketRule(OrderInvoiceInstitution.ONE.getCode());
                }

                invoiceApplyAddItem.setInvoiceOrderExtra(orderExtra);
                invoiceApplyAddItems.add(invoiceApplyAddItem);
            }
        });
    }

    /**
     * 开票提交成功,更新到订单中
     * @param reqDTO
     */
    private void saveOrderTaxInfo(ConfirmOrderInvoiceReqDTO reqDTO,String buyerMemberId){
        OrderTaxInfoSaveDTO modifyDTO = new OrderTaxInfoSaveDTO();
        modifyDTO.setOrderIdList(reqDTO.getOrderIds());
        modifyDTO.setMemberId(buyerMemberId);
        modifyDTO.setCompanyName(reqDTO.getInvoiceTitle());
        modifyDTO.setTaxpayerIdentificationCode(reqDTO.getBuyerTaxNo());

        modifyDTO.setBankName(reqDTO.getBuyerBankName());
        modifyDTO.setBankAccount(reqDTO.getBuyerBankNum());

        if (reqDTO.getIsSpecial() != null && reqDTO.getIsSpecial() == 1) {
            modifyDTO.setInvoiceType("02");
        } else {
            modifyDTO.setInvoiceType("01");
        }

        if (CsStringUtils.equals(reqDTO.getBuyerType(), "03")) {
            modifyDTO.setInvoiceHead("01");
        } else {
            modifyDTO.setInvoiceHead("02");
        }

        modifyDTO.setInvoiceCollector(reqDTO.getBuyerName());
        modifyDTO.setInvoiceCall(reqDTO.getBuyerMobile());
        modifyDTO.setInvoiceProvince(reqDTO.getBuyerProvince());
        modifyDTO.setInvoiceAddress(reqDTO.getBuyerAddress());
        modifyDTO.setOperatorId(reqDTO.getCreateUser());
        orderTaxinfoBiz.saveOrderTaxInfo(modifyDTO);
    }
}
