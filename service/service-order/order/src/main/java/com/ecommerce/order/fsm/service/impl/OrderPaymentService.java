package com.ecommerce.order.fsm.service.impl;

import com.alibaba.fastjson.JSON;
import com.ecommerce.common.annotation.PrintArgs;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.service.common.BaseBiz;
import com.ecommerce.common.service.common.bsid.CommonBusinessIdGenerator;
import com.ecommerce.common.service.lock.RedisLockService;
import com.ecommerce.common.utils.ArithUtils;
import com.ecommerce.common.utils.BeanConvertUtils;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.goods.api.dto.ResourceDTO;
import com.ecommerce.goods.api.dto.contract.TrContractGoodsDTO;
import com.ecommerce.goods.api.enums.DeliveryWayEnum;
import com.ecommerce.goods.api.enums.TakedateTypeEnum;
import com.ecommerce.goods.api.service.IContractService;
import com.ecommerce.goods.api.service.IResourceQueryService;
import com.ecommerce.logistics.api.dto.ForBidCloseWaybillCountQueryDTO;
import com.ecommerce.logistics.api.dto.shipbill.ShipBillDTO;
import com.ecommerce.logistics.api.enums.TransportToolTypeEnum;
import com.ecommerce.logistics.api.service.IShipBillService;
import com.ecommerce.member.api.dto.member.MemberSimpleDTO;
import com.ecommerce.member.api.dto.member.config.MemberConfigEnum;
import com.ecommerce.member.api.service.IMemberService;
import com.ecommerce.open.api.dto.MessageConfigCodeEnum;
import com.ecommerce.order.api.dto.OrderDTO;
import com.ecommerce.order.api.dto.OrderInfoExtDTO;
import com.ecommerce.order.api.dto.OrderItemDTO;
import com.ecommerce.order.api.dto.OrderPayInfoInstructionsDTO;
import com.ecommerce.order.api.dto.OrderPayinfoCreateDTO;
import com.ecommerce.order.api.dto.OrderPayinfoDTO;
import com.ecommerce.order.api.dto.OrderPayinfoDetailDTO;
import com.ecommerce.order.api.dto.OrderPayinfoQueryDTO;
import com.ecommerce.order.api.dto.OrderRefundDTO;
import com.ecommerce.order.api.dto.OrderUnderLinePayConfirmDTO;
import com.ecommerce.order.api.dto.PayCallbackDTO;
import com.ecommerce.order.api.dto.TakeInfoDTO;
import com.ecommerce.order.api.dto.adjust.AdjustItemConditionQueryDTO;
import com.ecommerce.order.api.dto.adjust.AdjustItemListDTO;
import com.ecommerce.order.api.dto.base.SendSMSMessageDTO;
import com.ecommerce.order.api.dto.information.MemberIntegralMqMessageDTO;
import com.ecommerce.order.api.dto.information.MemberIntegralMqMessageGoodsInfoDTO;
import com.ecommerce.order.api.dto.pay.AutoPayWayQueryDTO;
import com.ecommerce.order.api.enums.AdjustExecuteStatusEnum;
import com.ecommerce.order.api.enums.AdjustStatusEnum;
import com.ecommerce.order.api.enums.BillPaymentTypeEnum;
import com.ecommerce.order.api.enums.OperatorTypeEnum;
import com.ecommerce.order.api.enums.PayDetailTypeEnum;
import com.ecommerce.order.api.enums.PayStatusEnum;
import com.ecommerce.order.api.enums.PayTypeEnum;
import com.ecommerce.order.api.enums.PayWayEnum;
import com.ecommerce.order.api.enums.PickingBillTypeEnum;
import com.ecommerce.order.api.enums.ProxyOrderTypeEnum;
import com.ecommerce.order.api.enums.RefundStatusEnum;
import com.ecommerce.order.api.enums.RefundTypeEnum;
import com.ecommerce.order.api.enums.TakeStatus;
import com.ecommerce.order.async.ProxyOrderAsync;
import com.ecommerce.order.biz.IOrderAdjustBiz;
import com.ecommerce.order.biz.IOrderAdjustStrategyBiz;
import com.ecommerce.order.biz.IOrderErpBiz;
import com.ecommerce.order.biz.IOrderOverSellBiz;
import com.ecommerce.order.biz.IOrderPayinfoJoinBiz;
import com.ecommerce.order.biz.IOrderProxyMapBiz;
import com.ecommerce.order.biz.fsm.IOrderInfoBiz;
import com.ecommerce.order.biz.fsm.IOrderInfoExtBiz;
import com.ecommerce.order.biz.fsm.IOrderItemBiz;
import com.ecommerce.order.biz.fsm.IOrderPayinfoBiz;
import com.ecommerce.order.biz.fsm.IOrderPayinfoDetailBiz;
import com.ecommerce.order.biz.fsm.IOrderRefundBiz;
import com.ecommerce.order.biz.fsm.ITakeInfoBiz;
import com.ecommerce.order.biz.fsm.ITakeItemBiz;
import com.ecommerce.order.biz.impl.BillSplitBiz;
import com.ecommerce.order.dao.dto.OrderCloseDO;
import com.ecommerce.order.dao.dto.OrderCompleteDO;
import com.ecommerce.order.dao.dto.PaymentAmountDO;
import com.ecommerce.order.dao.dto.PaymentAmountQueryDO;
import com.ecommerce.order.dao.dto.SupplementPaymentDO;
import com.ecommerce.order.dao.vo.OrderInfo;
import com.ecommerce.order.dao.vo.OrderItem;
import com.ecommerce.order.dao.vo.OrderPayinfo;
import com.ecommerce.order.dao.vo.OrderPayinfoDetail;
import com.ecommerce.order.dao.vo.TakeInfo;
import com.ecommerce.order.dao.vo.TakeItem;
import com.ecommerce.order.fsm.StateMachineConstants;
import com.ecommerce.order.fsm.domain.StatusModelDO;
import com.ecommerce.order.fsm.even.OrderChangeEventEnum;
import com.ecommerce.order.fsm.even.TakeChangeEventEnum;
import com.ecommerce.order.fsm.manager.OrderStateMachineEventManager;
import com.ecommerce.order.fsm.manager.TakeStateMachineEventManager;
import com.ecommerce.order.fsm.service.IOrderCommonService;
import com.ecommerce.order.fsm.service.IOrderComputeService;
import com.ecommerce.order.fsm.service.IOrderErpService;
import com.ecommerce.order.fsm.service.IOrderPaymentService;
import com.ecommerce.order.fsm.service.IOrderQueryService;
import com.ecommerce.order.fsm.service.ITakeInfoService;
import com.ecommerce.order.fsm.service.ITakeUpDataService;
import com.ecommerce.order.fsm.service.OrderServiceFactory;
import com.ecommerce.order.fsm.status.OrderStatusEnum;
import com.ecommerce.order.fsm.status.OrderTypeEnum;
import com.ecommerce.order.fsm.status.TakeStatusEnum;
import com.ecommerce.order.model.OrderModel;
import com.ecommerce.order.service.IOrderAdjustService;
import com.ecommerce.order.service.IOrderPayinfoService;
import com.ecommerce.order.service.handler.SynchronizeIntegralHandler;
import com.ecommerce.order.service.impl.OrderPayIntegrationService;
import com.ecommerce.order.service.message.MessageQueueService;
import com.ecommerce.order.service.message.SMSMessageFactoryContext;
import com.ecommerce.order.service.order.IRefundQueryService;
import com.ecommerce.pay.api.v2.dto.AutoPayChannelRequestDTO;
import com.ecommerce.pay.api.v2.dto.AutoPaymentRequestDTO;
import com.ecommerce.pay.api.v2.dto.AutoPaymentResponseDTO;
import com.ecommerce.pay.api.v2.dto.MemberChannelDTO;
import com.ecommerce.pay.api.v2.dto.PaymentCallbackResponseDTO;
import com.ecommerce.pay.api.v2.dto.PaymentRequirementDetailDTO;
import com.ecommerce.pay.api.v2.dto.PaymentResponseDTO;
import com.ecommerce.pay.api.v2.dto.RefundRequestDTO;
import com.ecommerce.pay.api.v2.dto.bill.PaymentBillDTO;
import com.ecommerce.pay.api.v2.dto.query.PaymentBillQueryDTO;
import com.ecommerce.pay.api.v2.enums.ChannelCodeEnum;
import com.ecommerce.pay.api.v2.enums.ChannelTypeEnum;
import com.ecommerce.pay.api.v2.enums.ClientType;
import com.ecommerce.pay.api.v2.enums.OperatorStatusEnum;
import com.ecommerce.pay.api.v2.enums.PaymentStatusEnum;
import com.ecommerce.pay.api.v2.service.IMemberChannelService;
import com.ecommerce.pay.api.v2.service.IPaymentService;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * biz全部调用新代码 com.ecommerce.fsm.biz.fsm
 * 以Event结尾的方法为状态机调用方法
 */
@Slf4j
@Service
public class OrderPaymentService implements IOrderPaymentService {

    public static final String SAVE_ORDER = "saveOrder";
    public static final String UNDEFINED_ERROR_ORDER_STATUS_MSG = "订单状态异常";
    public static final String UNDEFINED_ERROR_ORDER_TYPE_MSG = "无效的订单类型";
    public static final String SYSTEM = "system";
    public static final String LOG_SET_ORDER_TAKE_TIME_LIMIT = "setOrderTakeTimeLimit:{}";

    @Autowired
    private IOrderInfoBiz orderInfoBiz;
    @Autowired
    private IOrderInfoExtBiz orderInfoExtBiz;
    @Autowired
    private IOrderItemBiz orderItemBiz;

    @Autowired
    private IOrderRefundBiz orderRefundBiz;

    @Autowired
    private IOrderPayinfoBiz orderPayInfoBiz;

    @Autowired
    private ITakeInfoBiz takeInfoBiz;
    @Autowired
    private ITakeItemBiz takeItemBiz;
    @Autowired
    protected IOrderErpBiz orderErpBiz;

    @Autowired
    private IOrderPayinfoDetailBiz orderPayInfoDetailBiz;
    @Autowired
    protected CommonBusinessIdGenerator businessIdGenerator;

    @Lazy
    @Autowired
    protected OrderStateMachineEventManager orderStateMachineEventManager;
    @Lazy
    @Autowired
    protected TakeStateMachineEventManager takeStateMachineEventManager;

    @Autowired
    protected SynchronizeIntegralHandler syncIntegralHandler;

    @Autowired
    protected IResourceQueryService resourceQueryService;

    @Autowired
    private IContractService contractService;

    @Autowired
    private IPaymentService paymentService;

    @Autowired
    private IOrderErpService orderErpService;

    @Autowired
    private IMemberService memberService;

    @Autowired
    private ITakeUpDataService takeUpDataService;

    @Autowired
    private IOrderQueryService orderQueryService;

    @Autowired
    private ITakeInfoService takeInfoService;

    @Autowired
    private IOrderProxyMapBiz orderProxyMapBiz;

    @Autowired
    private IRefundQueryService refundQueryService;
    @Autowired
    private IShipBillService shipBillService;
    @Autowired
    private IOrderPayinfoService orderPayinfoService;

    @Autowired
    private IOrderAdjustStrategyBiz orderAdjustStrategyBiz;

    @Autowired
    private RedisLockService redisLockService;

    @Autowired
    private IOrderCommonService orderCommonService;

    @Autowired
    private MessageQueueService messageQueueService;

    @Autowired
    private IMemberChannelService memberChannelService;

    @Autowired
    private SMSMessageFactoryContext smsMessageFactoryContext;
    @Lazy
    @Autowired
    private OrderPayIntegrationService orderPayIntegrationService;

    @Autowired
    private IOrderComputeService orderComputeService;

    @Autowired
    private ThreadPoolExecutor threadPoolExecutor;

    @Autowired
    private IOrderAdjustService orderAdjustService;
    @Lazy
    @Autowired
    private ProxyOrderAsync proxyOrderAsync;

    @Autowired
    private IOrderPayinfoJoinBiz orderPayinfoJoinBiz;

    @Autowired
    private IOrderOverSellBiz orderOverSellBiz;

    @Autowired
    private IOrderAdjustBiz orderAdjustBiz;

    private static final String PAY_INFO_ID = "payinfoId";
    private static final String REFUND_BIZ_CODE = "orderRefund";
    private static final String DEL_FLG = "delFlg";
    private static final String PAY_INFO_NOT_EXIST = "支付单不存在";

    @Override
    public void confirmUnderLinePay(OrderUnderLinePayConfirmDTO dto) {
        log.info("confirmUnderLinePay:{}",dto);
        if (CsStringUtils.isBlank(dto.getOrderId()) && CsStringUtils.isBlank(dto.getTakeId())) {
            throw new BizException(BasicCode.CUSTOM_ERROR, "参数orderId,takeId不可都为空");
        }
        OrderPayinfo orderPayinfo = null;
        orderPayinfo = getOrderPayinfo(dto, orderPayinfo);
        //触发支付回调，统一回调处理入口
        confirmPaymentService(orderPayinfo.getTradeBillNo(), dto.getPass(), dto.getOperator());
    }

    @NotNull
    private OrderPayinfo getOrderPayinfo(OrderUnderLinePayConfirmDTO dto, OrderPayinfo orderPayinfo) {
        if (CsStringUtils.isBlank(dto.getTakeId())) {
            orderPayinfo = getOrderPayinfo(dto);
        }else{
            orderPayinfo = getPayinfo(dto, orderPayinfo);

        }
        if (orderPayinfo == null || BooleanUtils.isTrue(orderPayinfo.getDelFlg())) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, PAY_INFO_NOT_EXIST);
        }
        return orderPayinfo;
    }

    private OrderPayinfo getOrderPayinfo(OrderUnderLinePayConfirmDTO dto) {
        OrderPayinfo orderPayinfo;
        OrderInfo orderInfo = orderInfoBiz.getOrderInfoById(dto.getOrderId());
        if (orderInfo == null) {
            throw new BizException(BasicCode.DB_DATA_NOT_FOUND);
        }
        if (!OrderStatusEnum.IN_PAYMENT.getCode().equals(orderInfo.getOrderStatus())) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, UNDEFINED_ERROR_ORDER_STATUS_MSG);
        }
        orderPayinfo = orderPayInfoBiz.getOrderPay(dto.getOrderId());
        return orderPayinfo;
    }

    @Nullable
    private OrderPayinfo getPayinfo(OrderUnderLinePayConfirmDTO dto, OrderPayinfo orderPayinfo) {
        TakeInfo takeInfo = takeInfoBiz.get(dto.getTakeId());
        if (takeInfo == null || BooleanUtils.isTrue(takeInfo.getDelFlg())) {
            throw new BizException(BasicCode.DB_DATA_NOT_FOUND);
        }
        if (!TakeStatusEnum.IN_PAYMENT.getCode().equals(takeInfo.getTakeStatus())) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "发货单状态异常");
        }
        List<OrderPayinfo> list = orderPayInfoBiz.getOrderPayListByTakeId(dto.getTakeId());
        list = list.stream().filter(item->PayStatusEnum.IN_PAYMENT.getCode().equals(item.getPayinfoStatus()) &&
                ChannelCodeEnum.OFFLINE.getCode().equals(item.getPayinfoWay()) ).toList();
        if ( CollectionUtils.isEmpty(list)) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, PAY_INFO_NOT_EXIST);
        }
        if (list.size() > 1 && CsStringUtils.isBlank(dto.getPayinfoId())) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "存在多个待确认的支付单");
        }
        if( list.size() == 1 ){
            orderPayinfo = list.get(0);
        } else if (CsStringUtils.isNotBlank(dto.getPayinfoId())) {
            orderPayinfo = list.stream().filter(item -> CsStringUtils.equals(dto.getPayinfoId(), item.getPayinfoId())).findFirst().orElse(null);
        }
        return orderPayinfo;
    }

    /**
     * 设置提货期限(order需要:orderId,orderType,orderPayTime)
     */
    @Override
    public void setOrderTakeTimeLimit(OrderInfo order) {
        List<OrderItem> orderItems = orderItemBiz.findByOrderId(order.getOrderId());
        if (CollectionUtils.isEmpty(orderItems)) {
            return;
        }
        Set<String> resourceIds = orderItems.stream().map(OrderItem::getResourceId).collect(Collectors.toSet());
        List<ResourceDTO> resourceList;
        if (OrderTypeEnum.CONTRACT.getCode().equals(order.getOrderType())) {
            List<TrContractGoodsDTO> contractGoodsDTOList = contractService.getContractGoodsByIds(resourceIds);
            resourceList = BeanConvertUtils.convertList(contractGoodsDTOList, ResourceDTO.class);
        } else {
            resourceList = resourceQueryService.findByResourceIds(resourceIds);
        }
        log.info("1order:{},setTakeTimeLimit resourceList.size:{}", order.getOrderId(), CollectionUtils.isNotEmpty(resourceList) ? resourceList.size() : 0);
        Date now = order.getPayTime() != null ? order.getPayTime() : new Date();
        if (CollectionUtils.isNotEmpty(resourceList)) {
            resourceList.stream()
                    .filter(Objects::nonNull)
                    .filter(item -> CsStringUtils.isNotBlank(item.getTakedateType()))
                    .filter(item-> TakedateTypeEnum.TAKEDATE_TYPE2.code().equals(item.getTakedateType()) || TakedateTypeEnum.TAKEDATE_TYPE3.code().equals(item.getTakedateType()))
                    .filter(item -> item.getTakedateLimit() != null)
                    .filter(item -> item.getTakedateLimit() >= 0)
                    .forEach(resource -> {
                        log.info(LOG_SET_ORDER_TAKE_TIME_LIMIT, JSON.toJSONString(resource));

                        Date timeLimit = DateUtils.addDays(now, resource.getTakedateLimit().intValue());
                        timeLimit = getDate(resource, timeLimit, now);
                        //取最小时间节点
                        setTakeTimeLimit(order, timeLimit);
                    });
        }
        log.info("1order:{},setTakeTimeLimit:{}", order.getOrderId(), order.getTakeTimeLimit());
    }

    private static Date getDate(ResourceDTO resource, Date timeLimit, Date now) {
        if (resource.getTakedateHour() != null && resource.getTakedateHour() > 0) {
            if( 24 == resource.getTakedateHour()){
                timeLimit = DateUtils.setHours(timeLimit, 23);
                timeLimit = DateUtils.setMinutes(timeLimit, 59);
                timeLimit = DateUtils.setSeconds(timeLimit, 59);
            }else {
                timeLimit = DateUtils.setHours(timeLimit, resource.getTakedateHour().intValue());
                timeLimit = DateUtils.setMinutes(timeLimit, 0);
                timeLimit = DateUtils.setSeconds(timeLimit, 0);
                //如果提货有效期设置是：0天下午6点前，则当日下午6点后支付的订单，提货截止时间为第二天6点前
                if (now.after(timeLimit)) {
                    timeLimit = DateUtils.addDays(timeLimit, 1);
                }
            }
        }
        return timeLimit;
    }

    private static void setTakeTimeLimit(OrderInfo order, Date timeLimit) {
        if (order.getTakeTimeLimit() == null) {
            order.setTakeTimeLimit(timeLimit);
            log.info(LOG_SET_ORDER_TAKE_TIME_LIMIT, timeLimit);
        } else if (order.getTakeTimeLimit().after(timeLimit)) {
            order.setTakeTimeLimit(timeLimit);
            log.info(LOG_SET_ORDER_TAKE_TIME_LIMIT, timeLimit);
        }
    }

    /**
     * 订单确认线下补款
     *
     * @param orderId  订单ID
     * @param pass     是否通过
     * @param operator 操作人
     */
    @PrintArgs
    @Override
    public void confirmUnderLineSupplement(String orderId, boolean pass, String operator) {
        OrderInfo orderInfo = orderInfoBiz.getOrderInfoById(orderId);
        if (orderInfo == null) {
            throw new BizException(BasicCode.DB_DATA_NOT_FOUND);
        }
        if (!OrderStatusEnum.IN_PAYMENT.getCode().equals(orderInfo.getOrderStatus())) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, UNDEFINED_ERROR_ORDER_STATUS_MSG);
        }
        //查询补款记录
        OrderPayinfoQueryDTO payInfoQueryDTO = new OrderPayinfoQueryDTO();
        payInfoQueryDTO.setOrderId(orderId);
        payInfoQueryDTO.setPayType(PayTypeEnum.SUPPLEMENT);
        payInfoQueryDTO.setPayWay(ChannelCodeEnum.OFFLINE.getCode());
        payInfoQueryDTO.setPayStatusList(Lists.newArrayList(PayStatusEnum.IN_PAYMENT.code()));
        List<OrderPayinfo> payInfoList = orderQueryService.queryOrderPayInfo(payInfoQueryDTO);
        if (CollectionUtils.isEmpty(payInfoList)) {
            throw new BizException(BasicCode.DATA_NOT_EXIST, "线下支付补款单");
        }
        log.info("confirmSupplement_payInfoQueryDTO:" + JSON.toJSONString(payInfoQueryDTO));
        //触发支付回调，统一回调处理入口
        confirmPaymentService(payInfoList.get(0).getTradeBillNo(), pass, operator);
    }

    /**
     * 订单确认线下退款
     *
     * @param orderId  订单ID
     * @param pass     是否通过
     * @param operator 操作人
     */
    @PrintArgs
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void confirmUnderLineRefund(String orderId, boolean pass, String operator) {
        OrderInfo orderInfo = orderInfoBiz.getOrderInfoById(orderId);
        if (orderInfo == null) {
            throw new BizException(BasicCode.DB_DATA_NOT_FOUND);
        }
        if (!orderInfoExtBiz.billPaymentTypeEquals(orderId,BillPaymentTypeEnum.DELIVERY_PAYMENT) &&
                !CsStringUtils.equals(OrderStatusEnum.IN_PAYMENT.getCode(), orderInfo.getOrderStatus())) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, UNDEFINED_ERROR_ORDER_STATUS_MSG);
        }
        //查询退款支付记录
        OrderPayinfoQueryDTO payInfoQueryDTO = new OrderPayinfoQueryDTO();
        payInfoQueryDTO.setOrderId(orderId);
        payInfoQueryDTO.setPayType(PayTypeEnum.REFUND);
        payInfoQueryDTO.setPayWay(ChannelCodeEnum.OFFLINE.getCode());
        payInfoQueryDTO.setPayStatusList(Lists.newArrayList(PayStatusEnum.IN_PAYMENT.code()));
        List<OrderPayinfo> payInfoList = orderQueryService.queryOrderPayInfo(payInfoQueryDTO);
        if (CollectionUtils.isEmpty(payInfoList)) {
            throw new BizException(BasicCode.DATA_NOT_EXIST, "线下支付退款单");
        }
        log.info("confirmRefund_payInfoList:" + JSON.toJSONString(payInfoList));
        OrderPayinfo refundPayInfo = payInfoList.get(0);
        OrderRefundDTO orderRefundDTO = refundQueryService.findOrderRefundByOrderId(refundPayInfo.getObjectId());
        //执行线下退款操作
        Boolean refundSuccess = Boolean.FALSE;
        if (pass) {
            refundSuccess = executeUnderLineRefund(refundPayInfo);
        }
        PayStatusEnum payStatusEnum = refundSuccess ? PayStatusEnum.COMPLETED : PayStatusEnum.FAILED;
        //更新退款记录
        refundPayInfo.setPayinfoStatus(payStatusEnum.code());
        BaseBiz.setOperInfo(refundPayInfo, operator, false);
        orderPayInfoBiz.updateSelective(refundPayInfo);
        orderRefundDTO.setRefundStatus(payStatusEnum.code());
        orderPayInfoBiz.saveOrUpdateOrderRefund(orderRefundDTO);
        if( OrderStatusEnum.COMPLETED.getCode().equals(orderInfo.getOrderStatus()) ||
                OrderStatusEnum.CLOSED.getCode().equals(orderInfo.getOrderStatus()) ||
                OrderStatusEnum.CANCEL.getCode().equals(orderInfo.getOrderStatus())){
            return;
        }

        //获取当前订单调价信息
        AdjustItemConditionQueryDTO adjustItemConditionQueryDTO = new AdjustItemConditionQueryDTO();
        adjustItemConditionQueryDTO.setOrderId(orderId);
        adjustItemConditionQueryDTO.setExecuteStatus(AdjustExecuteStatusEnum.EXECUTED.getCode());
        List<AdjustItemListDTO> adjustItemList = orderAdjustStrategyBiz.queryAdjustItemListByCondition(adjustItemConditionQueryDTO);
        log.info("adjustItemList:" + JSON.toJSONString(adjustItemList));
        if (CollectionUtils.isNotEmpty(adjustItemList)) return;
        OrderTypeEnum orderTypeEnum = OrderTypeEnum.getByCode(orderInfo.getOrderType());
        if (orderTypeEnum == null) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, UNDEFINED_ERROR_ORDER_TYPE_MSG);
        }
        if (CsStringUtils.equals(orderInfo.getAdjustStatus(), AdjustStatusEnum.CONFIRMED.getCode()) ||
                CsStringUtils.equals(orderRefundDTO.getRefundType(), RefundTypeEnum.AUTO_COMPLETE_REFUND.getCode())) {
            OrderCompleteDO orderCompleteDO = new OrderCompleteDO();
            orderCompleteDO.setOrderId(orderInfo.getOrderId());
            orderCompleteDO.setOperator(operator);
            //触发订单完成
            OrderServiceFactory.getOrderHandlerService(orderTypeEnum.getBizType()).completeOrder(orderCompleteDO);
        } else {
            OrderCloseDO orderCloseDO = new OrderCloseDO();
            orderCloseDO.setOrderId(orderInfo.getOrderId());
            orderCloseDO.setRemarks(orderRefundDTO.getRefundPic());
            orderCloseDO.setOperator(operator);
            orderCloseDO.setOperatorType(OperatorTypeEnum.SELLER.getCode());
            //触发订单关闭
            OrderServiceFactory.getOrderHandlerService(orderTypeEnum.getBizType()).closeOrder(orderCloseDO);
        }
    }

    /**
     * 执行线下退款
     *
     * @param refundPayInfo 退款支付单信息
     * @return 退款结果
     */
    public Boolean executeUnderLineRefund(OrderPayinfo refundPayInfo) {
        OrderPayinfoDTO orderPayinfoDTO = new OrderPayinfoDTO();
        BeanUtils.copyProperties(refundPayInfo, orderPayinfoDTO);
        //获取交易支付单号
        OrderPayinfo paymentInfo = orderPayInfoBiz.get(refundPayInfo.getOriginalPayinfoId());
        orderPayinfoDTO.setTradeBillNo(paymentInfo.getTradeBillNo());
        //获取支付明细
        List<OrderPayinfoDetail> payDetailList = orderPayInfoDetailBiz.findByOrderPayInfoId(refundPayInfo.getPayinfoId());
        orderPayinfoDTO.setOrderPayinfoDetail(BeanConvertUtils.convertList(payDetailList, OrderPayinfoDetailDTO.class));
        //调用退款
        log.info("executeUnderLineRefund:" + JSON.toJSONString(orderPayinfoDTO));
        PaymentResponseDTO response = paymentServiceRefund(orderPayinfoDTO);
        Boolean refundSuccess = Boolean.FALSE;
        if (response.isSuccess()) {
            refundSuccess = Boolean.TRUE;
            //设置支付流水号
            refundPayInfo.setTradeBillId(response.getPaymentBillId());
            refundPayInfo.setTradeBillNo(response.getPaymentBillNo());
        }

        return refundSuccess;
    }

    /**
     * 调用pay-api的paymentService.refund
     */
    public PaymentResponseDTO paymentServiceRefund(OrderPayinfoDTO orderPayinfoDTO) {
        PaymentResponseDTO response;
        try {
            RefundRequestDTO refundRequestDTO = new RefundRequestDTO();
            // 不可自动转线下
            refundRequestDTO.setAutoOffline(false);
            refundRequestDTO.setAmount(orderPayinfoDTO.getPayAmount().toString());
            refundRequestDTO.setBizCode(REFUND_BIZ_CODE);
            refundRequestDTO.setNewBizId(orderPayinfoDTO.getPayinfoId());
            refundRequestDTO.setOperator(orderPayinfoDTO.getUpdateUser());
            refundRequestDTO.setChannelCode(orderPayinfoDTO.getPayinfoWay());
            refundRequestDTO.setPaymentBillNo(orderPayinfoDTO.getTradeBillNo());
            List<PaymentRequirementDetailDTO> detailDTOList = Lists.newArrayList();
            if (!CollectionUtils.isEmpty(orderPayinfoDTO.getOrderPayinfoDetail())) {
                detailDTOList = orderPayinfoDTO.getOrderPayinfoDetail().stream()
                        .filter(item -> BigDecimal.ZERO.compareTo(item.getPayAdmount()) != 0)
                        .map(item -> {
                            PaymentRequirementDetailDTO detailDTO = new PaymentRequirementDetailDTO();
                            detailDTO.setSubject(item.getPaytypeDetail());
                            detailDTO.setPayeeMemberId(item.getPayeeId());
                            detailDTO.setPayeeMemberName(item.getPayeeName());
                            detailDTO.setPayAmount(item.getPayAdmount().toPlainString());
                            return detailDTO;
                        }).toList();
            }
            refundRequestDTO.setPaymentBillDetailDTOList(detailDTOList);
            //如果是按发货单支付的
            if (CsStringUtils.isNotBlank(orderPayinfoDTO.getTakeCode()) ||
                    (CsStringUtils.isNotBlank(orderPayinfoDTO.getObjectId()) &&
                            orderInfoExtBiz.billPaymentTypeEquals(orderPayinfoDTO.getObjectId(), BillPaymentTypeEnum.DELIVERY_PAYMENT)) ) {
                refundRequestDTO.setOnlyCheckTotalAmount(true);
            }
            log.info("退款请求 --> paymentService.refund_Request: {}", JSON.toJSONString(refundRequestDTO));
            response = paymentService.refund(refundRequestDTO);
            log.info("返回 --> paymentService.refund_Response:{}", JSON.toJSONString(response));
        } catch (Exception e) {
            log.error("paymentService.refund_Error", e);
            response = new PaymentResponseDTO();
            response.setSuccess(false);
            response.setContent(e.getMessage());
        }
        return response;
    }

    @Override
    public void confirmUnderLinePayinfoId(String payinfoId, boolean pass, String operator) {
        log.info("confirmUnderLinePayinfoId:" + payinfoId + "|" + payinfoId + "|" + operator);
        OrderPayinfo payinfo = orderPayInfoBiz.get(payinfoId);
        if (payinfo == null || BooleanUtils.isTrue(payinfo.getDelFlg())) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, PAY_INFO_NOT_EXIST);
        }
        //订单款
        if (PayTypeEnum.SINGLE.code().equals(payinfo.getPayinfoType()) || PayTypeEnum.GROUP.code().equals(payinfo.getPayinfoType())) {
            confirmUnderLinePay(new OrderUnderLinePayConfirmDTO(payinfo.getObjectId(),payinfo.getTakeId(),payinfo.getPayinfoId(),pass,operator,null));
        } else {
            String paymentBillNo = payinfo.getTradeBillNo();
            confirmPaymentService(paymentBillNo, pass, operator);
            OrderPayinfo updPayinfo = new OrderPayinfo();
            updPayinfo.setPayinfoId(payinfoId);
            updPayinfo.setPayinfoWay(PayWayEnum.OFFLINE.code());//线下支付
            updPayinfo.setPayinfoStatus(pass ? PayStatusEnum.COMPLETED.code() : PayStatusEnum.FAILED.code());//已完成
            //更新支付单状态
            BaseBiz.setUpdateOperInfo(updPayinfo, operator);
            orderPayInfoBiz.updateSelective(updPayinfo);
        }
    }

    @Override
    public void createOrderPayInfo(OrderInfo order, BigDecimal payAmount, PayTypeEnum payType,
                                   PayDetailTypeEnum payDetailType, String operator) {
        if (payAmount == null || BigDecimal.ZERO.compareTo(payAmount) == 0) {
            return;
        }
        String payinfoId = orderPayInfoBiz.save(order, payAmount, payType, operator);
        orderPayInfoDetailBiz.save(payinfoId, order, payAmount, payDetailType, operator);
    }

    /**
     * 创建退款支付信息
     */
    @PrintArgs
    @Override
    public OrderPayinfoDTO createRefundPayInfo(OrderPayinfoCreateDTO payinfoCreateDTO, String operator) {
        checkParams(payinfoCreateDTO);
        //准备数据
        if (CsStringUtils.isNotBlank(payinfoCreateDTO.getOriginalPayinfoId())) {
            OrderPayinfo orgPayinfo = orderPayInfoBiz.findPayInfoById(payinfoCreateDTO.getOriginalPayinfoId());
            payinfoCreateDTO.setObjectId(orgPayinfo.getObjectId());
            payinfoCreateDTO.setObjectCode(orgPayinfo.getObjectCode());
            payinfoCreateDTO.setCurrency(orgPayinfo.getCurrency());
            payinfoCreateDTO.setCurrencySymbol(orgPayinfo.getCurrencySymbol());
            payinfoCreateDTO.setCurrencyName(orgPayinfo.getCurrencyName());

            List<OrderPayinfoDetail> payinfoDetails = orderPayInfoDetailBiz.findByOrderPayInfoId(orgPayinfo.getPayinfoId());
            if (PayTypeEnum.REFUND == payinfoCreateDTO.getPayType() && CollectionUtils.isNotEmpty(payinfoDetails)) {//退款
                payinfoCreateDTO.setPayeeId(payinfoDetails.get(0).getPayerId());
                payinfoCreateDTO.setPayeeName(payinfoDetails.get(0).getPayerName());
                payinfoCreateDTO.setPayerId(payinfoDetails.get(0).getPayeeId());
                payinfoCreateDTO.setPayerName(payinfoDetails.get(0).getPayeeName());

                Set<String> paytypeDetailSet = payinfoDetails.stream().map(OrderPayinfoDetail::getPaytypeDetail).collect(Collectors.toSet());
                setPayDetailType(payinfoCreateDTO, paytypeDetailSet);
            } else if (CollectionUtils.isNotEmpty(payinfoDetails)) {
                payinfoCreateDTO.setPayeeId(payinfoDetails.get(0).getPayeeId());
                payinfoCreateDTO.setPayeeName(payinfoDetails.get(0).getPayeeName());
                payinfoCreateDTO.setPayerId(payinfoDetails.get(0).getPayerId());
                payinfoCreateDTO.setPayerName(payinfoDetails.get(0).getPayerName());
            }
        } else {
            OrderInfo order = orderInfoBiz.getOrderInfoById(payinfoCreateDTO.getObjectId());
            payinfoCreateDTO.setObjectCode(order.getOrderCode());
            payinfoCreateDTO.setCurrency(order.getCurrency());
            payinfoCreateDTO.setCurrencySymbol(order.getCurrencySymbol());
            payinfoCreateDTO.setCurrencyName(order.getCurrencyName());
            setpayinfoCreateDTO(payinfoCreateDTO, order);
        }
        //保存数据
        OrderPayinfo refundPayInfo = orderPayInfoBiz.createRefundPayInfo(payinfoCreateDTO, operator);
        OrderPayinfoDetail refundPayinfoDetail = orderPayInfoDetailBiz.createRefundPayInfo(refundPayInfo.getPayinfoId(), payinfoCreateDTO, operator);
        //转换结果对象
        OrderPayinfoDTO orderPayinfoDTO = BeanConvertUtils.convert(refundPayInfo, OrderPayinfoDTO.class);
        orderPayinfoDTO.setOrderPayinfoDetail(Lists.newArrayList(BeanConvertUtils.convert(refundPayinfoDetail, OrderPayinfoDetailDTO.class)));
        return orderPayinfoDTO;
    }

    private static void setPayDetailType(OrderPayinfoCreateDTO payinfoCreateDTO, Set<String> paytypeDetailSet) {
        if (paytypeDetailSet.size() == 1) {
            String paytypeDetail = paytypeDetailSet.iterator().next();
            if (PayDetailTypeEnum.GOODS.code().equals(paytypeDetail)) {
                payinfoCreateDTO.setPayDetailType(PayDetailTypeEnum.GOODS_REFUND);//商品款
            } else if (PayDetailTypeEnum.LOGISTICS.code().equals(paytypeDetail)) {
                payinfoCreateDTO.setPayDetailType(PayDetailTypeEnum.LOGISTICS_REFUND);//物流款
            }
        }
    }

    private static void setpayinfoCreateDTO(OrderPayinfoCreateDTO payinfoCreateDTO, OrderInfo order) {
        if (PayTypeEnum.REFUND == payinfoCreateDTO.getPayType()) {//退款
            payinfoCreateDTO.setPayeeId(order.getBuyerId());
            payinfoCreateDTO.setPayeeName(order.getBuyerName());
            payinfoCreateDTO.setPayerId(order.getSellerId());
            payinfoCreateDTO.setPayerName(order.getSellerName());
        } else {
            payinfoCreateDTO.setPayeeId(order.getSellerId());
            payinfoCreateDTO.setPayeeName(order.getSellerName());
            payinfoCreateDTO.setPayerId(order.getBuyerId());
            payinfoCreateDTO.setPayerName(order.getBuyerName());
        }
    }

    private static void checkParams(OrderPayinfoCreateDTO payinfoCreateDTO) {
        if (CsStringUtils.isBlank(payinfoCreateDTO.getObjectId()) && CsStringUtils.isBlank(payinfoCreateDTO.getOriginalPayinfoId())) {
            throw new BizException(BasicCode.PARAM_NULL, "objectId,originalPayinfoId不能都为空");
        }
        if (payinfoCreateDTO.getPayType() == null || payinfoCreateDTO.getPayDetailType() == null) {
            throw new BizException(BasicCode.PARAM_NULL, "payType,payDetailType不能为空");
        }
        if (CsStringUtils.isBlank(payinfoCreateDTO.getObjectId()) && CsStringUtils.isBlank(payinfoCreateDTO.getOriginalPayinfoId())) {
            throw new BizException(BasicCode.PARAM_NULL, "objectId,orgPayinfoId不能同时为空");
        }
    }

    /**
     * 确认付款服务
     */
    @Override
    public void confirmPaymentService(String paymentBillNo, boolean success, String operator) {
        log.info("confirmPaymentService_paymentBillNo:{},{}", paymentBillNo, success);
        try {
            PaymentCallbackResponseDTO callbackRes = paymentService.payCallbackByPlatform(paymentBillNo, success, operator);
            log.info("confirmPaymentService_return:{}", JSON.toJSONString(callbackRes));
            if (callbackRes == null || OperatorStatusEnum.FAIL.getCode().equals(callbackRes.getStatus())) {
                String msg = "支付确认失败" + (callbackRes == null ? "" : ":" + JSON.toJSONString(callbackRes));
                throw new BizException(BasicCode.UNDEFINED_ERROR, msg);
            }
        } catch (Exception e) {
            log.error("confirmPaymentService_error:%s,%s".formatted(paymentBillNo, success), e);
            throw new BizException(BasicCode.UNDEFINED_ERROR, "支付确认失败:{}", e.getMessage());
        }
    }

    @PrintArgs
    @Override
    public void checkRealTimeOrderAmount(String orderId, String operator) {
        if (CsStringUtils.isBlank(orderId))
            return;
        OrderInfo orderInfo = orderInfoBiz.getOrderInfoById(orderId);

        OrderInfo updOrder = new OrderInfo();
        updOrder.setOrderId(orderId);
        updOrder.setIfRepay(false);
        updOrder.setSupplementAmount(BigDecimal.ZERO);

        BigDecimal actualLogistic = orderInfo.getActualLogisticAmount();//物流实际总费用
        BigDecimal actualResource = orderInfo.getActualResourceAmount();//货物实际总费用
        BigDecimal realLogistic = orderInfo.getRealtimeLogisticAmount();//已执行物流费用
        BigDecimal realResource = orderInfo.getRealtimeResourceAmount();//已执行货物费用
        //物流实际总费用 - 已执行物流费用
        BigDecimal subLogistic = BigDecimal.ZERO;
        if (realLogistic != null) {
            subLogistic = ArithUtils.subtract(false, actualLogistic, realLogistic);
        }
        BigDecimal subResource = BigDecimal.ZERO;
        if (realResource != null) {
            subResource = ArithUtils.subtract(false, actualResource, realResource);
        }

        log.info("checkRealTimeOrderAmount:{}-subLogistic:{},subResource:{}", orderId, subLogistic, subResource);

        BigDecimal logisticPayAmout = BigDecimal.ZERO;
        //如果为负数，则需要补款
        if (subLogistic.compareTo(BigDecimal.ZERO) < 0) {
            //物流补款
            logisticPayAmout = subLogistic.abs().setScale(2, RoundingMode.HALF_UP);
            updOrder.setSupplementAmount(logisticPayAmout);
            updOrder.setIfRepay(true);
        }
        BigDecimal goodsPayAmout = BigDecimal.ZERO;
        if (subResource.compareTo(BigDecimal.ZERO) < 0) {
            //货款补款
            goodsPayAmout = subResource.abs().setScale(2, RoundingMode.HALF_UP);
            updOrder.setSupplementAmount(ArithUtils.add(updOrder.getSupplementAmount(), goodsPayAmout));
            updOrder.setIfRepay(true);
        }
        updOrder.setActualLogisticAmount(actualLogistic);
        updOrder.setActualResourceAmount(actualResource);
        BaseBiz.setUpdateOperInfo(updOrder, operator);
        log.info("checkRealtimeOrderAmount_updOrder:{}", JSON.toJSONString(updOrder));
        //更新订单补款标记及补款金额
        orderInfoBiz.updateSelective(updOrder);
        //支持的支付方式
        List<OrderItem> orderItems = orderItemBiz.findByOrderId(orderId);
        Set<String> resourceIds = orderItems.stream().filter(item -> CsStringUtils.isNotBlank(item.getResourceId())).map(OrderItem::getResourceId).collect(Collectors.toSet());
        List<String> supportPayWay = resourceQueryService.findPayWayByResourceIds(resourceIds);
        String supportPayWayStr = CollectionUtils.isEmpty(supportPayWay) ? null : Lists.newArrayList(supportPayWay).stream().filter(Objects::nonNull).collect(Collectors.joining(","));
        //保存补款信息
        String payinfoId = orderPayInfoBiz.save(orderInfo, updOrder.getSupplementAmount(), PayTypeEnum.SUPPLEMENT, supportPayWayStr, operator);
        orderPayInfoDetailBiz.save(payinfoId, orderInfo, logisticPayAmout, PayDetailTypeEnum.LOGISTICS_SUPPLEMENT, operator);
        orderPayInfoDetailBiz.save(payinfoId, orderInfo, goodsPayAmout, PayDetailTypeEnum.GOODS_SUPPLEMENT, operator);

    }

    @Override
    public void addUpMemberIntegral(OrderInfo order) {
        List<OrderItem> orderItems = orderItemBiz.findByOrderId(order.getOrderId());
        addUpMemberIntegral(order, orderItems);
    }

    private void addUpMemberIntegral(OrderInfo order, List<OrderItem> orderItems) {
        String orderId = order.getOrderId();
        try {
            MemberIntegralMqMessageDTO integral = new MemberIntegralMqMessageDTO();
            integral.setBuyerId(order.getBuyerId());
            integral.setSellerId(order.getSellerId());
            integral.setEventTime(new Date());
            integral.setRemark("订单结束");
            integral.setOrderNumber(order.getOrderCode());
            List<MemberIntegralMqMessageGoodsInfoDTO> goodsList = Lists.newArrayList();
            if (CollectionUtils.isEmpty(orderItems)) return;
            orderItems.stream()
                    .filter(item -> CsStringUtils.isNotBlank(item.getCategoryCode()))
                    .filter(item -> item.getItemSendQuantity() != null)
                    .filter(item -> BigDecimal.ZERO.compareTo(item.getItemSendQuantity()) < 0)
                    .forEach(orderItem -> {
                        MemberIntegralMqMessageGoodsInfoDTO goodsInfoDTO = new MemberIntegralMqMessageGoodsInfoDTO();
                        goodsInfoDTO.setGoodsName(orderItem.getGoodsName());
                        goodsInfoDTO.setCategoryCode(orderItem.getCategoryCode().substring(0, 9));
                        goodsInfoDTO.setAmount(takeInfoBiz.transferIntoDefaultUnit(orderItem.getItemSendQuantity(),
                                orderItem.getUnits(), orderItem.getGoodsId()));
                        goodsList.add(goodsInfoDTO);
                    });
            integral.setGoodsList(goodsList);
            syncIntegralHandler.syncIntegralInfo(integral);
        } catch (Exception e) {
            log.error("orderId:" + orderId + ", 添加积分失败:" + e.getMessage(), e);
        }
    }

    @Override
    public void payTradeCallback(PayCallbackDTO callbackDTO) {
        log.info("payTradeCallback:" + JSON.toJSONString(callbackDTO));
        OrderPayinfo payinfo = getOrderPayinfo(callbackDTO);
        if (payinfo == null) return;
        OrderInfo orderInfo = orderInfoBiz.getOrderInfoById(payinfo.getObjectId());
        //支付时间
        OrderPayinfo saveOrderPayInfo = new OrderPayinfo();
        saveOrderPayInfo.setPayinfoId(payinfo.getPayinfoId());
        saveOrderPayInfo.setObjectId(payinfo.getObjectId());
        saveOrderPayInfo.setObjectCode(payinfo.getObjectCode());
        if (CsStringUtils.isNotBlank(payinfo.getTakeId())) {
            saveOrderPayInfo.setTakeId(payinfo.getTakeId());
        }
        Function<PayCallbackDTO, Void> payInfoFunction = callback -> {
            if (!callbackDTO.isSucess()) {
                saveOrderPayInfo.setPayinfoStatus(PayStatusEnum.FAILED.code());
                return null;
            }
            //验证支付金额
            if (callbackDTO.getActualAmount().compareTo(payinfo.getPayAmount()) != 0) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "支付金额异常");
            }
            saveOrderPayInfo.setPayinfoStatus(PayStatusEnum.COMPLETED.code());
            saveOrderPayInfo.setPayTime(new Date());
            //支付单明细
            List<OrderPayinfoDetail> payDetails = orderPayInfoDetailBiz.findByOrderPayInfoId(payinfo.getPayinfoId());
            if (CollectionUtils.isEmpty(payDetails)) return null;
            for (OrderPayinfoDetail payInfoDetail : payDetails) {
                updateSelective(callbackDTO, payInfoDetail, payinfo, orderInfo);
            }
            return null;
        };
        payInfoFunction.apply(callbackDTO);
        //更新支付单状态
        saveOrderPayInfo.setPayinfoWay(CsStringUtils.isBlank(callbackDTO.getPayWay()) ? PayWayEnum.ONLINE.code() : callbackDTO.getPayWay());
        BaseBiz.setUpdateOperInfo(saveOrderPayInfo, callbackDTO.getOperator());
        orderPayInfoBiz.updateSelective(saveOrderPayInfo);
        //如果是补款支付回调
        if (PayTypeEnum.SUPPLEMENT.code().equals(payinfo.getPayinfoType())) {
            payTradeCallbackSupplement(callbackDTO, orderInfo, saveOrderPayInfo);
        }
        //如果是订单款支付回调
        sendSMSMessage(callbackDTO, payinfo, orderInfo, saveOrderPayInfo);
        //如果是退款支付回调
        if (PayTypeEnum.REFUND.code().equals(payinfo.getPayinfoType())) {//退款
            //刷新退款申请单状态
            orderRefundBiz.refreshOrderRefundStatus(payinfo.getInitiatorId());
        }
    }

    private void updateSelective(PayCallbackDTO callbackDTO, OrderPayinfoDetail payInfoDetail, OrderPayinfo payinfo, OrderInfo orderInfo) {
        Set<String> payWaySet = Sets.newHashSet(
                PayTypeEnum.SINGLE.getCode(),
                PayTypeEnum.GROUP.getCode(),
                PayTypeEnum.SUPPLEMENT.getCode()
        );
        Set<String> payDetailTypeSet = Sets.newHashSet(
                PayDetailTypeEnum.LOGISTICS.getCode(),
                PayDetailTypeEnum.LOGISTICS_SUPPLEMENT.getCode()
        );
        //当买家为erp余额支付的时候，不管配送方式，修改物流费明细到到卖家（物流费由卖家代收）
        if (payWaySet.contains(payinfo.getPayinfoType()) &&
                CsStringUtils.equals(payinfo.getPayinfoWay(), ChannelCodeEnum.ERP.getCode()) &&
                payDetailTypeSet.contains(payInfoDetail.getPaytypeDetail())) {
            payInfoDetail.setPayeeId(orderInfo.getSellerId());
            payInfoDetail.setPayeeName(orderInfo.getSellerName());
        }
        BaseBiz.setUpdateOperInfo(payInfoDetail, callbackDTO.getOperator());
        orderPayInfoDetailBiz.updateSelective(payInfoDetail);
    }

    private void sendSMSMessage(PayCallbackDTO callbackDTO, OrderPayinfo payinfo, OrderInfo orderInfo, OrderPayinfo saveOrderPayInfo) {
        if (PayTypeEnum.SINGLE.code().equals(payinfo.getPayinfoType()) ||
                PayTypeEnum.GROUP.code().equals(payinfo.getPayinfoType())) {
            payTradeCallbackSingleOrGroup(callbackDTO, orderInfo, saveOrderPayInfo);
            if (!orderErpBiz.hasErpOrder(orderInfo.getSellerId(), MemberConfigEnum.ERP_ORDER_FLG.getKeyCode())) {
                //一级订单支付成功时，获取二级订单下的发货单继续执行确认操作
                if (CsStringUtils.equals(orderInfo.getProxyOrderType(), ProxyOrderTypeEnum.PRIMARY.getCode()) &&
                        PayStatusEnum.COMPLETED.code().equals(saveOrderPayInfo.getPayinfoStatus())) {
                    messageQueueService.continueTakeInfoAutoConfirm(orderInfo.getOrderId());
                }
                //如果是二级订单，则发送消息通知
                if (CsStringUtils.equals(orderInfo.getProxyOrderType(), ProxyOrderTypeEnum.SECONDARY.getCode())) {

                    log.info("异步处理: 自动生成一级订单 {}", orderInfo.getOrderCode());
                    proxyOrderAsync.submitPrimaryOrder(orderInfo);

                    log.info("终端用户提交成功支付二级订单，平台通知经销商下对应的一级订单");
                    //发送消息
                    SendSMSMessageDTO sendSMSMessageDTO = new SendSMSMessageDTO();
                    OrderDTO orderDTO = new OrderDTO();
                    orderDTO.setOrderId(payinfo.getObjectId());
                    sendSMSMessageDTO.setOrderDTO(orderDTO);
                    sendSMSMessageDTO.setBusinessCode(orderDTO.getOrderId());
                    sendSMSMessageDTO.setMessageConfigCode(MessageConfigCodeEnum.PAY_CALLBACK_NOTICE_SELLER.getCode());
                    smsMessageFactoryContext.sendSMSMessage(sendSMSMessageDTO);
                }
            }
        }
    }

    @Nullable
    private OrderPayinfo getOrderPayinfo(PayCallbackDTO callbackDTO) {
        if (!ChannelTypeEnum.ERP.getCode().equals(callbackDTO.getPayWay()) && CsStringUtils.isBlank(callbackDTO.getTradeBillId())) {
            throw new BizException(BasicCode.PARAM_NULL, "TradeBillId");
        }
        if (CsStringUtils.isBlank(callbackDTO.getOperator())) {
            callbackDTO.setOperator("pay_system");
        }
        OrderPayinfo payinfo = CsStringUtils.isNotEmpty(callbackDTO.getPayinfoId()) ? orderPayInfoBiz.findPayInfoByTradePayinfoId(callbackDTO.getPayinfoId()) : orderPayInfoBiz.findPayInfoByTradeBillId(callbackDTO.getTradeBillId());
        if (CsStringUtils.equals(PayStatusEnum.COMPLETED.code(), payinfo.getPayinfoStatus())) {
            log.info("当前支付单已支付成功, tradeBillId:{}", callbackDTO.getTradeBillId());
            return null;
        }
        return payinfo;
    }

    private void payTradeCallbackSupplement(PayCallbackDTO callbackDTO, OrderInfo orderInfo, OrderPayinfo saveOrderPayInfo) {
        //支付失败
        if (!callbackDTO.isSucess()) {
            if (CsStringUtils.equals(callbackDTO.getPayWay(), ChannelCodeEnum.OFFLINE.getCode())) {
                //线下支付需要触发确认支付失败事件
                StatusModelDO statusModelDO = new StatusModelDO();
                statusModelDO.setBizId(orderInfo.getOrderId());
                OrderInfo saveOrder = new OrderInfo();
                saveOrder.setOrderId(orderInfo.getOrderId());
                BaseBiz.setUpdateOperInfo(saveOrder, callbackDTO.getOperator());
                statusModelDO.addParam(SAVE_ORDER, orderInfo.getOrderId());
                statusModelDO.setCurrentState(OrderStatusEnum.IN_PAYMENT.getCode());
                orderStateMachineEventManager.sendStatusChangeEvent(statusModelDO, OrderChangeEventEnum.CONFIRM_SUPPLEMENT);
            }
            return;
        }
        OrderInfo saveOrder = new OrderInfo();
        saveOrder.setOrderId(orderInfo.getOrderId());
        saveOrder.setPayStatus(saveOrderPayInfo.getPayinfoStatus());
        saveOrder.setPayTime(new Date());
        //已支付总金额
        saveOrder.setOrderPayedAmount(ArithUtils.add(orderInfo.getOrderPayedAmount(), callbackDTO.getActualAmount()));
        saveOrder.setSupplementAmount(ArithUtils.add(saveOrder.getSupplementAmount(), callbackDTO.getActualAmount()));
        BaseBiz.setUpdateOperInfo(saveOrder, callbackDTO.getOperator());
        orderInfoBiz.updateSelective(saveOrder);
        //运单完成补款的时候需要通知物流出站
        if (CsStringUtils.equals(orderInfo.getAdjustStatus(), AdjustStatusEnum.WAIT_ADJUST.getCode())) {
            //线下补款触发确认补款成功事件
            if (CsStringUtils.equals(callbackDTO.getPayWay(), ChannelCodeEnum.OFFLINE.getCode())) {
                StatusModelDO statusModelDO = new StatusModelDO();
                statusModelDO.setBizId(orderInfo.getOrderId());
                statusModelDO.addParam("orderId", orderInfo.getOrderId());
                statusModelDO.setCurrentState(OrderStatusEnum.IN_PAYMENT.getCode());
                orderStateMachineEventManager.sendStatusChangeEvent(statusModelDO, OrderChangeEventEnum.CONFIRM_SUPPLEMENT);
            }
            takeUpDataService.notifyLeaveWarehouse(saveOrderPayInfo);
        //确认补款直接完成订单
        } else {
            OrderTypeEnum orderTypeEnum = OrderTypeEnum.getByCode(orderInfo.getOrderType());
            if (orderTypeEnum == null) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, UNDEFINED_ERROR_ORDER_TYPE_MSG);
            }
            OrderCompleteDO orderCompleteDO = new OrderCompleteDO();
            orderCompleteDO.setOrderId(orderInfo.getOrderId());
            orderCompleteDO.setOperator(callbackDTO.getOperator());
            OrderServiceFactory.getOrderHandlerService(orderTypeEnum.getBizType()).completeOrder(orderCompleteDO);
        }
    }

    @PrintArgs
    @Override
    public PaymentAmountDO computeOrderPaymentAmount(PaymentAmountQueryDO paymentAmountQueryDO) {
        if (CsStringUtils.isEmpty(paymentAmountQueryDO.getOrderId())) {
            throw new BizException(BasicCode.INVALID_PARAM, "无效的支付对象ID:" + paymentAmountQueryDO.getOrderId());
        }
        //查询支付的金额
        Condition payInfoCondition = new Condition(OrderPayinfo.class);
        Example.Criteria payInfoCriteria = payInfoCondition.createCriteria();
        payInfoCriteria.andEqualTo("objectId", paymentAmountQueryDO.getOrderId());
        setPayInfoCriteria(paymentAmountQueryDO, payInfoCriteria);
        //支付金额大于0  按发货单支付有可能被改为0了的
        payInfoCriteria.andGreaterThan("payAmount",0);
        payInfoCriteria.andEqualTo(DEL_FLG, 0);
        List<OrderPayinfo> payInfoList = orderPayInfoBiz.findByCondition(payInfoCondition);

        List<String> payInfoIdList = Lists.newArrayList();
        List<OrderPayinfoDetail> payInfoDetailList = Lists.newArrayList();
        //统计合并支付金额
        PaymentAmountDO paymentAmountDO = new PaymentAmountDO();
        paymentAmountDO.setPaymentGoodsAmount(BigDecimal.ZERO);
        paymentAmountDO.setPaymentLogisticsAmount(BigDecimal.ZERO);
        paymentAmountDO.setPaymentOtherAmount(BigDecimal.ZERO);
        paymentAmountDO.setPaymentTotalAmount(BigDecimal.ZERO);
        if (CollectionUtils.isNotEmpty(payInfoList)) {
            payInfoIdList = payInfoList.stream().map(OrderPayinfo::getPayinfoId).toList();
            paymentAmountDO.setPayInfoWay(payInfoList.get(0).getPayinfoWay());
        }
        //获取所有支付单详情
        if (CollectionUtils.isNotEmpty(payInfoIdList)) {
            Condition payInfoDetailCondition = new Condition(OrderPayinfoDetail.class);
            Example.Criteria payInfoDetailCriteria = payInfoDetailCondition.createCriteria();
            payInfoDetailCriteria.andIn(PAY_INFO_ID, payInfoIdList);
            payInfoDetailList = orderPayInfoDetailBiz.findByCondition(payInfoDetailCondition);
        }
        if (CollectionUtils.isEmpty(payInfoDetailList)) {
            return paymentAmountDO;
        }
        Set<String> goodsPaymentTypeSet = Sets.newHashSet(
                PayDetailTypeEnum.GOODS.code(),
                PayDetailTypeEnum.GOODS_SUPPLEMENT.code()
        );
        Set<String> logisticsPaymentTypeSet = Sets.newHashSet(
                PayDetailTypeEnum.LOGISTICS.code(),
                PayDetailTypeEnum.LOGISTICS_SUPPLEMENT.code()
        );
        Map<String, String> payeeMap = Maps.newHashMap();
        for (OrderPayinfoDetail orderPayinfoDetail : payInfoDetailList) {
            if (goodsPaymentTypeSet.contains(orderPayinfoDetail.getPaytypeDetail())) {
                payeeMap.put(PayDetailTypeEnum.GOODS.code(), orderPayinfoDetail.getPayeeId());
                paymentAmountDO.setPaymentGoodsAmount(
                        paymentAmountDO.getPaymentGoodsAmount().add(orderPayinfoDetail.getPayAdmount()));
            }
            if (logisticsPaymentTypeSet.contains(orderPayinfoDetail.getPaytypeDetail())) {
                payeeMap.put(PayDetailTypeEnum.LOGISTICS.code(), orderPayinfoDetail.getPayeeId());
                paymentAmountDO.setPaymentLogisticsAmount(
                        paymentAmountDO.getPaymentLogisticsAmount().add(orderPayinfoDetail.getPayAdmount()));
            }
            if (!goodsPaymentTypeSet.contains(orderPayinfoDetail.getPaytypeDetail())
                    && !logisticsPaymentTypeSet.contains(orderPayinfoDetail.getPaytypeDetail())
                    && !PayDetailTypeEnum.REFUND.code().equals(orderPayinfoDetail.getPaytypeDetail())
                    && !PayDetailTypeEnum.GOODS_REFUND.code().equals(orderPayinfoDetail.getPaytypeDetail())
                    && !PayDetailTypeEnum.LOGISTICS_REFUND.code().equals(orderPayinfoDetail.getPaytypeDetail())
                    && !PayDetailTypeEnum.OTHER_REFUND.code().equals(orderPayinfoDetail.getPaytypeDetail())) {
                payeeMap.put(PayDetailTypeEnum.OTHER.code(), orderPayinfoDetail.getPayeeId());
                paymentAmountDO.setPaymentOtherAmount(
                        paymentAmountDO.getPaymentOtherAmount().add(orderPayinfoDetail.getPayAdmount()));
            }
            paymentAmountDO.setPaymentTotalAmount(
                    paymentAmountDO.getPaymentTotalAmount().add(orderPayinfoDetail.getPayAdmount()));
        }
        paymentAmountDO.setPayeeMap(payeeMap);

        return paymentAmountDO;
    }

    private static void setPayInfoCriteria(PaymentAmountQueryDO paymentAmountQueryDO, Example.Criteria payInfoCriteria) {
        if (CsStringUtils.isNotEmpty(paymentAmountQueryDO.getPayWay())) {
            payInfoCriteria.andIn("payinfoWay", BillSplitBiz.splitPayInfoWay);
        }
        if (CollectionUtils.isNotEmpty(paymentAmountQueryDO.getPayStatusList())) {
            if(paymentAmountQueryDO.getPayStatusList().size() == 1 ){
                payInfoCriteria.andEqualTo("payinfoStatus", paymentAmountQueryDO.getPayStatusList().get(0));
            }else{
                payInfoCriteria.andIn("payinfoStatus", paymentAmountQueryDO.getPayStatusList());
            }
        }
        if (CollectionUtils.isNotEmpty(paymentAmountQueryDO.getPayType())) {
            if(paymentAmountQueryDO.getPayType().size() == 1 ){
                payInfoCriteria.andEqualTo("payinfoType", paymentAmountQueryDO.getPayType().get(0));
            }else{
                payInfoCriteria.andIn("payinfoType", paymentAmountQueryDO.getPayType());
            }
        }
    }

    /**
     * 计算待支付的补款金额
     * @return
     */
    @Override
    public BigDecimal calcOrderSupplementWaitPaymentAmount(String orderId)
    {
        return orderPayInfoBiz.calcOrderSupplementWaitPaymentAmount(orderId);
    }

    private void payTradeCallbackSingleOrGroup(PayCallbackDTO callbackDTO, OrderInfo orderInfo, OrderPayinfo saveOrderPayInfo) {
        OrderInfoExtDTO orderInfoExtDTO = orderInfoExtBiz.findByOrderId(orderInfo.getOrderId());
        //支付失败
        if (!callbackDTO.isSucess()) {
            if (CsStringUtils.equals(callbackDTO.getPayWay(), ChannelCodeEnum.OFFLINE.getCode())) {
                if(BillPaymentTypeEnum.ORDER_PAYMENT.getCode().equals(orderInfoExtDTO.getBillPaymentType())) {
                    //线下支付需要触发确认支付失败事件
                    StatusModelDO statusModelDO = new StatusModelDO();
                    statusModelDO.setBizId(orderInfo.getOrderId());
                    statusModelDO.addParam("orderId", orderInfo.getOrderId());
                    statusModelDO.setCurrentState(OrderStatusEnum.IN_PAYMENT.getCode());
                    orderStateMachineEventManager.sendStatusChangeEvent(statusModelDO, OrderChangeEventEnum.CONFIRM_PAY_FAIL);
                }else if(BillPaymentTypeEnum.DELIVERY_PAYMENT.getCode().equals(orderInfoExtDTO.getBillPaymentType())) {
                    //线下支付需要触发确认支付失败事件
                    StatusModelDO statusModelDO = new StatusModelDO();
                    statusModelDO.setBizId(orderInfo.getOrderId());
                    statusModelDO.addParam(StateMachineConstants.TAKE_ID, saveOrderPayInfo.getTakeId());
                    statusModelDO.addParam(StateMachineConstants.ORDER_PAY_INFO_ID, saveOrderPayInfo.getPayinfoId());
                    statusModelDO.addParam(StateMachineConstants.OPERATOR,callbackDTO.getOperator());
                    statusModelDO.setCurrentState(TakeStatusEnum.IN_PAYMENT.getCode());
                    takeStateMachineEventManager.sendStatusChangeEvent(statusModelDO, TakeChangeEventEnum.CONFIRM_PAY_FAIL);
                }
            }
            return;
        }
        OrderInfo saveOrder = new OrderInfo();
        //更新订单支付状态
        saveOrder.setOrderId(orderInfo.getOrderId());
        saveOrder.setPayStatus(saveOrderPayInfo.getPayinfoStatus());
        saveOrder.setSellerId(orderInfo.getSellerId());
        saveOrder.setPayTime(new Date());
        //已支付总金额
        saveOrder.setOrderPayedAmount(callbackDTO.getActualAmount());
        //设置提货期限
        saveOrder.setOrderType(orderInfo.getOrderType());
        setOrderTakeTimeLimit(saveOrder);
        saveOrder.setProxyOrderType(orderInfo.getProxyOrderType());
        BaseBiz.setUpdateOperInfo(saveOrder, callbackDTO.getOperator());
        sendStatusChangeEvent(callbackDTO, orderInfo, saveOrderPayInfo, orderInfoExtDTO, saveOrder);
    }

    private void sendStatusChangeEvent(PayCallbackDTO callbackDTO, OrderInfo orderInfo, OrderPayinfo saveOrderPayInfo, OrderInfoExtDTO orderInfoExtDTO, OrderInfo saveOrder) {
        if(BillPaymentTypeEnum.ORDER_PAYMENT.getCode().equals(orderInfoExtDTO.getBillPaymentType())) {
            //根据支付方式判断当前的的订单状态和触发事件
            StatusModelDO statusModelDO = new StatusModelDO();
            statusModelDO.setBizId(orderInfo.getOrderId());
            statusModelDO.addParam("orderDTO", orderQueryService.getOrderInfo(orderInfo.getOrderId()));
            statusModelDO.addParam("operator", callbackDTO.getOperator());
            statusModelDO.addParam(SAVE_ORDER, saveOrder);
            statusModelDO.addParam("needSellerConfirm", orderInfo.getIfNeedsellerconfirm());
            statusModelDO.addParam("ifConcrete", CsStringUtils.isNotBlank(orderInfoExtDTO.getSigner()));
            OrderStatusEnum currentOrderStatus = CsStringUtils.equals(callbackDTO.getPayWay(), ChannelCodeEnum.OFFLINE.getCode())
                    ? OrderStatusEnum.IN_PAYMENT : OrderStatusEnum.WAIT_PAYMENT;
            OrderChangeEventEnum orderChangeEventEnum = CsStringUtils.equals(callbackDTO.getPayWay(), ChannelCodeEnum.OFFLINE.getCode())
                    ? OrderChangeEventEnum.CONFIRM_PAY_SUCCESS : OrderChangeEventEnum.PAY_SUCCESS;
            statusModelDO.setCurrentState(currentOrderStatus.getCode());
            orderStateMachineEventManager.sendStatusChangeEvent(statusModelDO, orderChangeEventEnum);
        }else if(BillPaymentTypeEnum.DELIVERY_PAYMENT.getCode().equals(orderInfoExtDTO.getBillPaymentType())) {
            StatusModelDO statusModelDO = new StatusModelDO();
            statusModelDO.setBizId(orderInfo.getOrderId());
            statusModelDO.addParam(StateMachineConstants.TAKE_ID, saveOrderPayInfo.getTakeId());
            statusModelDO.addParam(StateMachineConstants.OPERATOR, callbackDTO.getOperator());
            statusModelDO.addParam(StateMachineConstants.ORDER_PAY_INFO_ID, saveOrderPayInfo.getPayinfoId());
            if (CsStringUtils.equals(callbackDTO.getPayWay(), ChannelCodeEnum.OFFLINE.getCode())) {
                statusModelDO.setCurrentState(TakeStatusEnum.IN_PAYMENT.getCode());
                takeStateMachineEventManager.sendStatusChangeEvent(statusModelDO,TakeChangeEventEnum.CONFIRM_PAY_SUCCESS);
            }else{
                statusModelDO.setCurrentState(TakeStatusEnum.WAIT_PAYMENT.getCode());
                takeStateMachineEventManager.sendStatusChangeEvent(statusModelDO,TakeChangeEventEnum.PAY_SUCCESS);
            }
        }
    }

    @Override
    public void erpSupplementComplete(String orderId, String operator) {
        //更新支付单状态
        OrderInfo orderInfo = orderInfoBiz.getOrderInfoById(orderId);
        //查询当前订单未完成的补款记录
        OrderPayinfoQueryDTO payInfoQueryDTO = new OrderPayinfoQueryDTO();
        payInfoQueryDTO.setOrderId(orderId);
        payInfoQueryDTO.setPayType(PayTypeEnum.SUPPLEMENT);
        payInfoQueryDTO.setPayStatusList(Lists.newArrayList(
                PayStatusEnum.WAIT_PAYMENT.code(),
                PayStatusEnum.IN_PAYMENT.code(),
                PayStatusEnum.FAILED.code()));

        List<OrderPayinfo> payInfoList = orderPayInfoBiz.queryOrderPayInfo(payInfoQueryDTO);
        log.info("erpSupplementComplete_erpSupplementComplete:" + JSON.toJSONString(payInfoList));
        if (CollectionUtils.isEmpty(payInfoList)) {
            return;
        }
        BigDecimal supplementAmount = BigDecimal.ZERO;
        for (OrderPayinfo orderPayinfo : payInfoList) {
            supplementAmount = supplementAmount.add(orderPayinfo.getPayAmount());
            orderPayinfo.setPayinfoStatus(PayStatusEnum.COMPLETED.code());
            orderPayinfo.setPayTime(new Date());
            orderPayinfo.setPayinfoWay(ChannelCodeEnum.ERP.getCode());
            BaseBiz.setUpdateOperInfo(orderPayinfo, operator);
            orderPayInfoBiz.updateSelective(orderPayinfo);

            List<OrderPayinfoDetail> payInfoDetailList = orderPayInfoDetailBiz.findByOrderPayInfoId(orderPayinfo.getPayinfoId());
            if (CollectionUtils.isNotEmpty(payInfoDetailList)) {
                //同步添加交易流水记录
                payInfoDetailList.stream().forEach(payInfoDetail -> {
                    if (CsStringUtils.equals(payInfoDetail.getPaytypeDetail(), PayDetailTypeEnum.LOGISTICS_SUPPLEMENT.getCode())) {
                        payInfoDetail.setPayeeId(orderInfo.getSellerId());
                        payInfoDetail.setPayeeName(orderInfo.getSellerName());
                        orderPayInfoDetailBiz.updateSelective(payInfoDetail);
                    }
                    orderErpService.syncBillLogs(orderPayinfo, payInfoDetail);
                });
            } else {
                log.info("payInfoDetailList is empty,未添加交易流水记录");
            }
        }
        //更新订单支付单状态
        OrderInfo updOrder = new OrderInfo();
        updOrder.setOrderId(orderId);
        updOrder.setPayStatus(PayStatusEnum.COMPLETED.code());
        updOrder.setPayTime(new Date());
        updOrder.setOrderPayedAmount(ArithUtils.add(orderInfo.getOrderPayedAmount(), supplementAmount));
        updOrder.setSupplementAmount(ArithUtils.add(orderInfo.getSupplementAmount(), supplementAmount));
        BaseBiz.setUpdateOperInfo(updOrder, operator);
        orderInfoBiz.updateSelective(updOrder);
    }

    /**
     * 生成支付明细信息信息
     *
     * @param order 订单对象
     * @return 订单支付明细对象
     */
    public OrderPayinfoDetailDTO generateOrderPayDetail(OrderDTO order, PayDetailTypeEnum payDetailType) {
        OrderPayinfoDetailDTO payDetail = new OrderPayinfoDetailDTO();
        payDetail.setPayeeId(order.getSellerId());
        payDetail.setPayeeName(order.getSellerName());
        payDetail.setPayerId(order.getBuyerId());
        payDetail.setPayerName(order.getBuyerName());
        payDetail.setCurrency(order.getCurrency());
        payDetail.setCurrencySymbol(order.getCurrencySymbol());
        payDetail.setCurrencyName(order.getCurrencyName());
        //支付单明细类型:订单货款
        payDetail.setPaytypeDetail(payDetailType.code());
        //明细金额
        switch (payDetailType) {
            //商品费
            case GOODS:
                payDetail.setPayAdmount(order.getActualResourceAmount().setScale(2, RoundingMode.HALF_UP));
                break;
            //物流费
            case LOGISTICS:
                //平台配送方式
                if (!CsStringUtils.equals(order.getCarriagePayeeId(), order.getSellerId())) {
                    MemberSimpleDTO carrier = memberService.findMemberSimpleById(order.getCarriagePayeeId());
                    if (carrier == null) {
                        log.error("无法查找到运费收款承运商信息,memberId:{}", order.getCarriagePayeeId());
                        throw new BizException(BasicCode.UNDEFINED_ERROR, "无法查找到运费收款承运商信息");
                    }
                    payDetail.setPayeeId(carrier.getMemberId());
                    payDetail.setPayeeName(carrier.getMemberName());
                }
                payDetail.setPayAdmount(order.getActualLogisticAmount().setScale(2, RoundingMode.HALF_UP));
                break;
            //其他费用(台班费＋空载费)
            case OTHER:
                payDetail.setPayAdmount(order.getActualOthersAmount().setScale(2, RoundingMode.HALF_UP));
                break;
            default:
                break;
        }

        return payDetail;
    }

    /**
     * 生成订单支付信息
     *
     * @param order 订单对象
     * @return OrderPayinfoDTO
     */
    @Override
    public OrderPayinfoDTO generateOrderPayInfo(OrderDTO order) {
        OrderPayinfoDTO payInfo = new OrderPayinfoDTO();
        payInfo.setObjectId(order.getOrderId());
        payInfo.setObjectCode(order.getOrderCode());
        payInfo.setPayinfoCode(businessIdGenerator.incrementOrderPayInfoCode());
        //待支付
        payInfo.setPayinfoStatus(PayStatusEnum.WAIT_PAYMENT.code());
        //支付单类型-订单款
        payInfo.setPayinfoType(PayTypeEnum.SINGLE.code());
        payInfo.setCurrency(order.getCurrency());
        payInfo.setCurrencySymbol(order.getCurrencySymbol());
        payInfo.setCurrencyName(order.getCurrencyName());
        //发起方ID
        payInfo.setInitiatorId("0");
        //发起方名称
        payInfo.setInitiatorName("admin");
        //支付发起方
        payInfo.setPayerId(order.getBuyerId());
        //支付发起方名称
        payInfo.setPayerName(order.getBuyerName());
        if (order.getSupportPayWay() != null) {
            //支持的支付方式
            payInfo.setSupportPayway(order.getSupportPayWay().stream()
                    .filter(Objects::nonNull).collect(Collectors.joining(",")));
        }
        //支付截止时间
        payInfo.setPayDealline(order.getPayTimeLimit());
        //支付单总金额
        payInfo.setPayAmount(order.getActualOrderAmount());
        //生成支付详情
        List<OrderPayinfoDetailDTO> payDetailList = Lists.newArrayList();
        //商品费用
        payDetailList.add(generateOrderPayDetail(order, PayDetailTypeEnum.GOODS));
        //物流费用
        if (order.getActualLogisticAmount() != null && order.getActualLogisticAmount().compareTo(BigDecimal.ZERO) > 0) {
            payDetailList.add(generateOrderPayDetail(order, PayDetailTypeEnum.LOGISTICS));
        }
        //其他费用
        if (order.getActualOthersAmount() != null && order.getActualOthersAmount().compareTo(BigDecimal.ZERO) > 0) {
            payDetailList.add(generateOrderPayDetail(order, PayDetailTypeEnum.OTHER));
        }
        payInfo.setOrderPayinfoDetail(payDetailList);
        payInfo.setPayDescriptionList(Lists.newArrayList());
        for (OrderItemDTO orderItem : order.getOrderItems()) {
            //超发金额生成支付单时的支付单说明
            OrderPayInfoInstructionsDTO desc = new OrderPayInfoInstructionsDTO();
            desc.setGoodsId(orderItem.getGoodsId());
            desc.setGoodsName(orderItem.getGoodsName());
            desc.setUnits(orderItem.getMeasureUnits());
            //钱 除以 价格 = 量
            //资源超发量 = 超发金额 除以 实际价格
            desc.setGoodsQuantity(orderItem.getMeasureQuantity());
            desc.setGoodsPrice(orderItem.getActualUnitPrice());
            desc.setGoodsAmount(orderItem.getOriginAmountPrice());
            //物流平均单价 = 量 除以 物流费
            desc.setLogisticsPrice(ArithUtils.divide(orderItem.getOriginLogisticPrice(),orderItem.getMeasureQuantity()));
            desc.setLogisticsAmount(orderItem.getOriginLogisticPrice());
            desc.setTradeAmount(orderItem.getActualAmountPrice());
            desc.setTradePrice(ArithUtils.add(desc.getGoodsPrice(),desc.getLogisticsPrice()));
            payInfo.getPayDescriptionList().add(desc);
        }

        log.info("generateOrderPayInfo:{}", JSON.toJSONString(payInfo));

        return payInfo;
    }

    /**
     * 生成订单退款信息
     *
     * @param orderId 订单ID
     */
    @Override
    public OrderRefundDTO generateOrderRefundInfo(String orderId) {
        if (CsStringUtils.isBlank(orderId)) {
            throw new BizException(BasicCode.INVALID_PARAM, "订单ID");
        }
        //获取当前订单已支付金额
        PaymentAmountQueryDO paymentAmountQueryDO = new PaymentAmountQueryDO();
        paymentAmountQueryDO.setOrderId(orderId);
        paymentAmountQueryDO.setPayStatusList(Lists.newArrayList(
                PayStatusEnum.IN_PAYMENT.code(),
                PayStatusEnum.COMPLETED.code()
        ));
        PaymentAmountDO paymentAmountDO = computeOrderPaymentAmount(paymentAmountQueryDO);
        //获取订单信息
        OrderInfo orderInfo = orderInfoBiz.get(orderId);
        //计算当前的订单实际发生金额进行退补款
        // 要减去退货的金额
        // 查询有退货的订单Item
        BigDecimal overOrderAmount = getOrderAmount(orderId, orderInfo, paymentAmountDO);
        if (overOrderAmount == null) return null;
        BigDecimal overResourceAmount = ArithUtils.subtract(false, paymentAmountDO.getPaymentGoodsAmount(), orderInfo.getRealtimeResourceAmount());
        BigDecimal overLogisticsAmount = ArithUtils.subtract(false, paymentAmountDO.getPaymentLogisticsAmount(), orderInfo.getRealtimeLogisticAmount());
        BigDecimal overOthersAmount = ArithUtils.subtract(false, paymentAmountDO.getPaymentOtherAmount(), orderInfo.getRealtimeOthersAmount());
        log.info("overResourceAmount:{} = paymentAmountDO.getPaymentGoodsAmount():{} - orderInfo.getRealtimeResourceAmount():{}",
                overResourceAmount, paymentAmountDO.getPaymentGoodsAmount(), orderInfo.getRealtimeResourceAmount());
        log.info("overLogisticsAmount:{} = paymentAmountDO.getPaymentLogisticsAmount():{} - orderInfo.getRealtimeLogisticAmount():{}",
                overResourceAmount, paymentAmountDO.getPaymentLogisticsAmount(), orderInfo.getRealtimeLogisticAmount());
        log.info("overOthersAmount:{} = paymentAmountDO.getPaymentOtherAmount():{} - orderInfo.getRealtimeOthersAmount():{}",
                overResourceAmount, paymentAmountDO.getPaymentOtherAmount(), orderInfo.getRealtimeOthersAmount());
        if( orderInfoExtBiz.transportToolTypeEquals(orderId, TransportToolTypeEnum.WATER_TRANSPORT)) {
            //overResourceAmount overLogisticsAmount 如果有负 有正 先抵消负数部分
            if( overResourceAmount.compareTo(BigDecimal.ZERO) < 0 && overLogisticsAmount.compareTo(BigDecimal.ZERO) > 0 ){
                overLogisticsAmount = ArithUtils.add( overLogisticsAmount, overResourceAmount);//减去负数部分
                overResourceAmount = getResourceAmount(overLogisticsAmount, overResourceAmount);
            }else if( overResourceAmount.compareTo(BigDecimal.ZERO) > 0 && overLogisticsAmount.compareTo(BigDecimal.ZERO) < 0 ){
                overResourceAmount = ArithUtils.add( overResourceAmount, overLogisticsAmount);//减去负数部分
                overLogisticsAmount = getLogisticsAmount(overResourceAmount, overLogisticsAmount);
            }
            log.info("overResourceAmount2:{} = paymentAmountDO.getPaymentGoodsAmount():{} - orderInfo.getRealtimeResourceAmount():{}",
                    overResourceAmount, paymentAmountDO.getPaymentGoodsAmount(), orderInfo.getRealtimeResourceAmount());
            log.info("overLogisticsAmount2:{} = paymentAmountDO.getPaymentLogisticsAmount():{} - orderInfo.getRealtimeLogisticAmount():{}",
                    overResourceAmount, paymentAmountDO.getPaymentLogisticsAmount(), orderInfo.getRealtimeLogisticAmount());
        }else {
            //空载费问题修改  退款未剔除空载费问题导致了多退款的问题修复
            if (overResourceAmount.compareTo(BigDecimal.ZERO) > 0 && overOthersAmount.compareTo(BigDecimal.ZERO) < 0) {
                overResourceAmount = ArithUtils.add(overResourceAmount, overOthersAmount);//减去负数部分
                overOthersAmount = getOverOthersAmount(overResourceAmount, overOthersAmount);
            }
        }

        OrderRefundDTO orderRefundDTO = getOrderRefundDTO(orderId, paymentAmountDO, overOrderAmount, orderInfo, overResourceAmount, overLogisticsAmount, overOthersAmount);
        if (orderRefundDTO == null) return null;
        log.info("orderRefundDTO:{}",JSON.toJSONString(orderRefundDTO));
        return orderRefundDTO;
    }

    private static BigDecimal getOverOthersAmount(BigDecimal overResourceAmount, BigDecimal overOthersAmount) {
        if (overResourceAmount.compareTo(BigDecimal.ZERO) >= 0) {
            overOthersAmount = BigDecimal.ZERO;
        } else {
            overOthersAmount = ArithUtils.add(overOthersAmount, overResourceAmount.multiply(BigDecimal.valueOf(-1l)));
        }
        return overOthersAmount;
    }

    private static BigDecimal getLogisticsAmount(BigDecimal overResourceAmount, BigDecimal overLogisticsAmount) {
        if(overResourceAmount.compareTo(BigDecimal.ZERO) >=0){
            overLogisticsAmount = BigDecimal.ZERO;
        }else{
            overLogisticsAmount = ArithUtils.add(overLogisticsAmount, overResourceAmount.multiply(BigDecimal.valueOf(-1l)));
        }
        return overLogisticsAmount;
    }

    private static BigDecimal getResourceAmount(BigDecimal overLogisticsAmount, BigDecimal overResourceAmount) {
        if(overLogisticsAmount.compareTo(BigDecimal.ZERO) >=0){
            overResourceAmount = BigDecimal.ZERO;
        }else{
            overResourceAmount = ArithUtils.add(overResourceAmount, overLogisticsAmount.multiply(BigDecimal.valueOf(-1l)));
        }
        return overResourceAmount;
    }

    @Nullable
    private OrderRefundDTO getOrderRefundDTO(String orderId, PaymentAmountDO paymentAmountDO, BigDecimal overOrderAmount, OrderInfo orderInfo, BigDecimal overResourceAmount, BigDecimal overLogisticsAmount, BigDecimal overOthersAmount) {
        //生成退款记录
        OrderRefundDTO orderRefundDTO = new OrderRefundDTO();
        orderRefundDTO.setOrderId(orderId);
        orderRefundDTO.setRefundType(PayTypeEnum.REFUND.getCode());
        orderRefundDTO.setRefundStatus(RefundStatusEnum.WAIT_PAYMENT.code());
        orderRefundDTO.setPayInfoWay(paymentAmountDO.getPayInfoWay());
        //退款金额
        orderRefundDTO.setAmountPrice(overOrderAmount);
        orderRefundDTO.setResourcePrice(overOrderAmount);
        orderRefundDTO.setRealtimeResourceAmount(orderInfo.getRealtimeResourceAmount());
        orderRefundDTO.setRealtimeLogisticAmount(orderInfo.getRealtimeLogisticAmount());
        orderRefundDTO.setRealtimeOthersAmount(orderInfo.getRealtimeOthersAmount());
        orderRefundDTO.setRealtimeCarryAmount(BigDecimal.ZERO);
        orderRefundDTO.setResourcePrice(overResourceAmount);
        orderRefundDTO.setLogisticPrice(overLogisticsAmount);
        orderRefundDTO.setOtherPrice(overOthersAmount);
        //设置订单信息
        orderRefundDTO.setBuyerId(orderInfo.getBuyerId());
        orderRefundDTO.setBuyerName(orderInfo.getBuyerName());
        orderRefundDTO.setBuyerContact(orderInfo.getBuyerContact());
        orderRefundDTO.setBuyerContactWay(orderInfo.getBuyerContactWay());
        orderRefundDTO.setBuyerType(orderInfo.getBuyerType());
        orderRefundDTO.setSellerId(orderInfo.getSellerId());
        orderRefundDTO.setSellerContact(orderInfo.getSellerContact());
        orderRefundDTO.setSellerContactWay(orderInfo.getSellerContactWay());
        orderRefundDTO.setSellerName(orderInfo.getSellerName());
        //同一订单只保留一条退款记录
        OrderRefundDTO refundDTO = orderPayInfoBiz.findOrderRefundByOrderId(orderRefundDTO.getOrderId());
        if (refundDTO != null) {
            orderRefundDTO.setRefundId(refundDTO.getRefundId());
            //如果已经成功退款不再发起退款请求
            if (CsStringUtils.equals(refundDTO.getRefundStatus(), RefundStatusEnum.COMPLETED.getCode())) {
                return null;
            }
        }
        return orderRefundDTO;
    }

    @Nullable
    private BigDecimal getOrderAmount(String orderId, OrderInfo orderInfo, PaymentAmountDO paymentAmountDO) {
        BigDecimal refundAmount = BigDecimal.ZERO;
        BigDecimal refundLogisticAmount = BigDecimal.ZERO;
        List<OrderItem> itemList = orderItemBiz.findByOrderId(orderId);
        for(OrderItem orderItem : itemList)
        {
            if(orderItem.getItemRefundAmount() != null)
            {
                refundAmount = refundAmount.add(orderItem.getItemRefundAmount());
            }

            if(orderItem.getItemRefundLogisticAmount() != null)
            {
                refundLogisticAmount = refundLogisticAmount.add(orderItem.getItemRefundLogisticAmount());
            }
        }

        //订单执行金额和商品执行金额都要减去退货的金额
        BigDecimal realtimeOrderAmount = orderInfo.getRealtimeOrderAmount() == null ? BigDecimal.ZERO : orderInfo.getRealtimeOrderAmount();
        BigDecimal realtimeResourceAmount = orderInfo.getRealtimeResourceAmount() == null ? BigDecimal.ZERO : orderInfo.getRealtimeResourceAmount();
        BigDecimal realtimeLogisticAmount = orderInfo.getRealtimeLogisticAmount() == null ? BigDecimal.ZERO : orderInfo.getRealtimeLogisticAmount();


        orderInfo.setRealtimeOrderAmount(realtimeOrderAmount.subtract(refundAmount));
        orderInfo.setRealtimeResourceAmount(realtimeResourceAmount.subtract(refundAmount).subtract(refundLogisticAmount));
        orderInfo.setRealtimeLogisticAmount(realtimeLogisticAmount.subtract(refundLogisticAmount));


        BigDecimal overOrderAmount = ArithUtils.subtract(false, paymentAmountDO.getPaymentTotalAmount(), orderInfo.getRealtimeOrderAmount());

        log.info("========{} 生成订单退款信息 支付金额: {}, 已执行总金额: {}, 退货总金额: {}, 退货物流金额: {}, 待退款金额: {}", orderInfo.getOrderCode(), paymentAmountDO.getPaymentTotalAmount(), orderInfo.getRealtimeOrderAmount(), refundAmount, refundLogisticAmount, overOrderAmount);

        if (overOrderAmount.compareTo(BigDecimal.ZERO) < 1) {
            log.info("不需要进行退款:overOrderAmount=" + overOrderAmount);
            return null;
        }
        return overOrderAmount;
    }

    @Override
    public OrderRefundDTO generateOrderRefundInfo(OrderInfo orderInfo, BigDecimal refundGoodsAmount, BigDecimal refundLogisticsAmount, BigDecimal refundOtherAmount)
    {
        //获取当前订单已支付金额
        PaymentAmountQueryDO paymentAmountQueryDO = new PaymentAmountQueryDO();
        paymentAmountQueryDO.setOrderId(orderInfo.getOrderId());
        paymentAmountQueryDO.setPayStatusList(Lists.newArrayList(PayStatusEnum.IN_PAYMENT.code(), PayStatusEnum.COMPLETED.code()));
        PaymentAmountDO paymentAmountDO = computeOrderPaymentAmount(paymentAmountQueryDO);

        //生成退款记录
        OrderRefundDTO orderRefundDTO = new OrderRefundDTO();
        orderRefundDTO.setOrderId(orderInfo.getOrderId());
        orderRefundDTO.setRefundType(PayTypeEnum.REFUND.getCode());
        orderRefundDTO.setRefundStatus(RefundStatusEnum.WAIT_PAYMENT.code());
        orderRefundDTO.setPayInfoWay(paymentAmountDO.getPayInfoWay());
        //退款金额
        orderRefundDTO.setAmountPrice(ArithUtils.add(refundGoodsAmount, refundLogisticsAmount, refundOtherAmount));
        orderRefundDTO.setRealtimeResourceAmount(orderInfo.getRealtimeResourceAmount());
        orderRefundDTO.setRealtimeLogisticAmount(orderInfo.getRealtimeLogisticAmount());
        orderRefundDTO.setRealtimeOthersAmount(orderInfo.getRealtimeOthersAmount());
        orderRefundDTO.setRealtimeCarryAmount(BigDecimal.ZERO);
        orderRefundDTO.setResourcePrice(refundGoodsAmount);
        orderRefundDTO.setLogisticPrice(refundLogisticsAmount);
        orderRefundDTO.setOtherPrice(refundOtherAmount);
        //设置订单信息
        orderRefundDTO.setBuyerId(orderInfo.getBuyerId());
        orderRefundDTO.setBuyerName(orderInfo.getBuyerName());
        orderRefundDTO.setBuyerContact(orderInfo.getBuyerContact());
        orderRefundDTO.setBuyerContactWay(orderInfo.getBuyerContactWay());
        orderRefundDTO.setBuyerType(orderInfo.getBuyerType());
        orderRefundDTO.setSellerId(orderInfo.getSellerId());
        orderRefundDTO.setSellerContact(orderInfo.getSellerContact());
        orderRefundDTO.setSellerContactWay(orderInfo.getSellerContactWay());
        orderRefundDTO.setSellerName(orderInfo.getSellerName());

        return orderRefundDTO;
    }

    @Override
    public OrderRefundDTO closeOrderAndDoRefund(OrderRefundDTO orderRefundDTO) {
        String identifier = "";
        try {
            identifier = redisLockService.lockFast("closeOrderAndDoRefund_" + orderRefundDTO.getOrderId());
            log.info("closeOrderAndDoRefund:" + JSON.toJSONString(orderRefundDTO));
            OrderInfo orderInfo = orderInfoBiz.getOrderInfoById(orderRefundDTO.getOrderId());
            if (CsStringUtils.isNotBlank(orderInfo.getBuyerCancelReason())) {
                orderRefundDTO.setCloseReason(orderInfo.getBuyerCancelReason());
            }
            //状态校验
            Set<String> finalOrderStatus = Sets.newHashSet(
                    OrderStatusEnum.CANCEL.getCode(),
                    OrderStatusEnum.ERP_CLOSING.getCode(),
                    OrderStatusEnum.CLOSED.getCode(),
                    OrderStatusEnum.ERP_COMPLETING.getCode(),
                    OrderStatusEnum.COMPLETED.getCode());
            if (finalOrderStatus.contains(orderInfo.getOrderStatus())) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "订单已结束，请勿重复操作");
            }
            //二级订单关闭验证
            orderProxyMapBiz.secondaryOrderCloseValidate(orderInfo);
            //先尝试关闭订单下的所有发货单
            doSellerCloseOrder(orderRefundDTO);
            //判断是否需要退款
            OrderRefundDTO orderRefund = generateOrderRefundInfo(orderRefundDTO.getOrderId());
            generatePayInfoAndRefund(orderRefundDTO, orderRefund);
            if( orderInfoExtBiz.billPaymentTypeEquals(orderRefundDTO.getOrderId(),BillPaymentTypeEnum.DELIVERY_PAYMENT) ){
                //触发订单关闭
                OrderTypeEnum orderTypeEnum = OrderTypeEnum.getByCode(orderInfo.getOrderType());
                if (orderTypeEnum == null) {
                    throw new BizException(BasicCode.UNDEFINED_ERROR, UNDEFINED_ERROR_ORDER_TYPE_MSG);
                }
                OrderCloseDO orderCloseDO = new OrderCloseDO();
                orderCloseDO.setOrderId(orderInfo.getOrderId());
                orderCloseDO.setRemarks(orderRefundDTO.getCloseReason());
                orderCloseDO.setOperator(orderRefundDTO.getCreateUser());
                orderCloseDO.setOperatorType(OperatorTypeEnum.SELLER.getCode());
                OrderServiceFactory.getOrderHandlerService(orderTypeEnum.getBizType()).closeOrder(orderCloseDO);
            }else
            //线下退款需要确认
                generatePayInfoAndRefundV1(orderRefundDTO, orderRefund, orderInfo);
        } catch (BizException be) {
            throw be;
        } catch (Exception e) {
            log.info("关闭订单异常", e);
            throw new BizException(BasicCode.UNDEFINED_ERROR, "订单关闭异常！");
        } finally {
            if (CsStringUtils.isNotEmpty(orderRefundDTO.getOrderId()) && CsStringUtils.isNotEmpty(identifier)) {
                redisLockService.unlock("closeOrderAndDoRefund_" + orderRefundDTO.getOrderId(), identifier);
            }
        }

        return orderRefundDTO;
    }

    private void generatePayInfoAndRefundV1(OrderRefundDTO orderRefundDTO, OrderRefundDTO orderRefund, OrderInfo orderInfo) {
        if (orderRefund != null && CsStringUtils.equals(ChannelCodeEnum.OFFLINE.getCode(), orderRefund.getPayInfoWay())) {
            OrderInfo saveOrder = new OrderInfo();
            saveOrder.setOrderId(orderRefund.getOrderId());
            StatusModelDO statusModelDO = new StatusModelDO();
            statusModelDO.setBizId(orderRefund.getOrderId());
            OrderStatusEnum orderStatusEnum = OrderStatusEnum.getByCode(orderInfo.getOrderStatus());
            if (orderStatusEnum == null) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "无效的订单状态");
            }
            statusModelDO.setCurrentState(orderStatusEnum.getCode());
            statusModelDO.addParam(SAVE_ORDER, saveOrder);
            //触发订单线下退款事件
            orderStateMachineEventManager.sendStatusChangeEvent(statusModelDO, OrderChangeEventEnum.ORDER_REFUND);
        } else {
            //触发订单关闭
            OrderTypeEnum orderTypeEnum = OrderTypeEnum.getByCode(orderInfo.getOrderType());
            if (orderTypeEnum == null) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, UNDEFINED_ERROR_ORDER_TYPE_MSG);
            }
            OrderCloseDO orderCloseDO = new OrderCloseDO();
            orderCloseDO.setOrderId(orderInfo.getOrderId());
            orderCloseDO.setRemarks(orderRefundDTO.getCloseReason());
            orderCloseDO.setOperator(orderRefundDTO.getCreateUser());
            orderCloseDO.setOperatorType(OperatorTypeEnum.SELLER.getCode());
            OrderServiceFactory.getOrderHandlerService(orderTypeEnum.getBizType()).closeOrder(orderCloseDO);
        }
    }

    private void generatePayInfoAndRefund(OrderRefundDTO orderRefundDTO, OrderRefundDTO orderRefund) {
        if (orderRefund != null) {
            orderRefund.setRefundType(CsStringUtils.isBlank(orderRefundDTO.getRefundType()) ?
                    PayTypeEnum.REFUND.getCode() : orderRefundDTO.getRefundType());
            orderRefund.setMome(orderRefundDTO.getMome());
            orderRefund.setCloseReason(orderRefundDTO.getCloseReason());
            orderRefund.setCreateUser(orderRefundDTO.getCreateUser());
            orderRefund.setUpdateUser(orderRefundDTO.getUpdateUser());
            //设置选择的退款渠道(可能非原渠道)
            if (CsStringUtils.isNotBlank(orderRefundDTO.getPayInfoWay())) {
                orderRefund.setPayInfoWay(orderRefundDTO.getPayInfoWay());
            }
            orderPayInfoBiz.saveOrUpdateOrderRefund(orderRefund);
            try {
                orderPayinfoService.generatePayInfoAndRefund(orderRefund);
            } catch (Exception e) {
                log.error("closeOrderAndDoRefund ===退款失败=== {}", e);
                orderRefund.setRefundStatus(RefundStatusEnum.FAILED.getCode());
                orderPayInfoBiz.saveOrUpdateOrderRefund(orderRefund);
            }
        }
    }

    private void doSellerCloseOrder(OrderRefundDTO orderRefundDTO) {
        if (CsStringUtils.equals(orderRefundDTO.getOperatorTypeEnum(), OperatorTypeEnum.BUYER.code())) {
            takeInfoService.doBuyerCloseOrder(orderRefundDTO.getOrderId(), orderRefundDTO.getCreateUser());
        }else{
            takeInfoService.doSellerCloseOrder(orderRefundDTO.getOrderId(), orderRefundDTO.getCreateUser());
        }
    }

    @Override
    public OrderRefundDTO closeOrderAndDoRefundWrap(OrderRefundDTO orderRefundDTO) {
        log.info("closeOrderAndDoRefundWrap:{}",orderRefundDTO);
        OrderDTO orderDTO = orderQueryService.getOrderInfo(orderRefundDTO.getOrderId());
        //
        if (CsStringUtils.isNotBlank(orderDTO.getOrderInfoExtDTO().getSigner())) {
            //判断订单下所有的发货单是否处于最终状态
            List<TakeInfoDTO> takeInfoList = takeInfoBiz.getTakeInfoDetailsByOrderId(orderRefundDTO.getOrderId());
            log.info("closeOrderAndDoRefundWrap_takeInfoList:" + JSON.toJSONString(takeInfoList));
            Set<String> finalStatusSet = Sets.newHashSet(
                    TakeStatus.CLOSED.getCode(),
                    TakeStatus.CANCELED.getCode(),
                    TakeStatus.FINISHED.getCode());
            for (TakeInfoDTO takeInfo : takeInfoList) {
                if (!finalStatusSet.contains(takeInfo.getTakeStatus())) {
                    throw new BizException(BasicCode.UNDEFINED_ERROR, "请结束订单下所有发货单后再关闭订单！");
                }
            }
            //已经发货的订单需要进行调价确认
            OrderRefundDTO orderRefundDTO1 = getOrderRefundDTO(orderRefundDTO, orderDTO);
            if (orderRefundDTO1 != null) return orderRefundDTO1;
        }
        OrderInfo orderInfo = orderInfoBiz.getOrderInfoById(orderRefundDTO.getOrderId());
        //自提订单走尝试关闭流程(无法关闭时会锁定当前订单) 如果是自提
        closeOrderAndDoRefundV1(orderRefundDTO, orderInfo);

        return orderRefundDTO;
    }

    @Nullable
    private OrderRefundDTO getOrderRefundDTO(OrderRefundDTO orderRefundDTO, OrderDTO orderDTO) {
        BigDecimal sendQuantity = ArithUtils.add(orderDTO.getOrderItems().get(0).getItemSignQuantity(),
                orderDTO.getOrderInfoExtDTO().getLubricitySignQuantity());
        if (sendQuantity.compareTo(BigDecimal.ZERO) > 0) {
            OrderInfo saveOrder = new OrderInfo();
            saveOrder.setOrderId(orderDTO.getOrderId());
            //计算台班费
            BigDecimal machineCost = orderComputeService.computeOrderMachineCost(orderDTO);
            saveOrder.setRealtimeOthersAmount(orderDTO.getRealtimeOthersAmount() == null ?
                    machineCost : orderDTO.getRealtimeOthersAmount().add(machineCost));
            saveOrder.setRealtimeOrderAmount(orderDTO.getRealtimeOrderAmount() == null ?
                    machineCost : orderDTO.getRealtimeOrderAmount().add(machineCost));
            saveOrder.setAdjustStatus(AdjustStatusEnum.CONFIRMING.getCode());
            saveOrder.setBuyerCancelReason(orderRefundDTO.getCloseReason());
            orderInfoBiz.updateSelective(saveOrder);
            //发起确认流程
            orderAdjustService.confirmOrderAdjust(orderRefundDTO.getOrderId(), orderRefundDTO.getCreateUser());
            return orderRefundDTO;
        }
        return null;
    }

    private void closeOrderAndDoRefundV1(OrderRefundDTO orderRefundDTO, OrderInfo orderInfo) {
        if (CsStringUtils.equals(orderInfo.getDeliverWay(), PickingBillTypeEnum.BUYER_TAKE.getCode())) {
            //二级订单关闭验证
            orderProxyMapBiz.secondaryOrderCloseValidate(orderInfo);
            //如果是买家关闭
            if (CsStringUtils.equals(orderRefundDTO.getOperatorTypeEnum(), OperatorTypeEnum.BUYER.getCode())) {
                takeInfoBiz.getTakeInfoByOrderId(orderInfo.getOrderId()).stream()
                        .filter(item->TakeStatusEnum.PENDING_DELIVERY.getCode().equals(item.getTakeStatus()) || TakeStatusEnum.DELIVERING.getCode().equals(item.getTakeStatus()))
                        .forEach(item->{
                            //待配送状态是否可关闭
                            int waitDeliveryFlag = 1;
                            //2.如果是自提订单，如果是买家关闭订单，如果订单关联“待配送”状态且未接入ERP运单，订单可关闭  否则发货单不可以关闭
                            ItemResult<List<ShipBillDTO>> integerItemResult = shipBillService.queryForbidCloseWaybillListByTakeCode(new ForBidCloseWaybillCountQueryDTO(item.getTakeCode(),waitDeliveryFlag));
                            log.info("======= {},{}", waitDeliveryFlag, JSON.toJSONString(integerItemResult));
                            if (integerItemResult != null && integerItemResult.getData() != null && integerItemResult.getData().size() > 0) {
                                log.error("closeOrderAndDoRefundWrap orderId:{} takeId:{} takeCode:{} 发货单下有运单:{}", orderInfo.getOrderId(), item.getTakeId(), item.getTakeCode(), JSON.toJSONString(integerItemResult.getData()));
                                throw new BizException(BasicCode.CUSTOM_ERROR, OrderModel.formatCanNotCloseShipBillInfo(integerItemResult.getData()));
                            }
                            log.info("closeOrderAndDoRefundWrap orderId:{} takeId:{} takeCode:{} 发货单没有运单了", orderInfo.getOrderId(),item.getTakeId(), item.getTakeCode());
                        });
            }
            String executeStatus = orderCommonService.tryManualCloseOrder(orderRefundDTO);
            //订单处于锁定状态
            if (CsStringUtils.equals(executeStatus, "PENGDING")) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "订单已锁定，等待未完成的运单结束后订单自动关闭退款！");
            }
        } else {
            //买家只能关闭买家自提类型的订单
            if (CsStringUtils.equals(orderRefundDTO.getOperatorTypeEnum(), OperatorTypeEnum.BUYER.getCode())) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "您没有操作权限，请联系卖家处理！");
            }
            closeOrderAndDoRefund(orderRefundDTO);
        }
    }

    @Override
    public void completeAutoPay(OrderPayinfoDTO orderPayinfoDTO) {
        AutoPaymentRequestDTO paymentRequestDTO = new AutoPaymentRequestDTO();
        paymentRequestDTO.setBizId(orderPayinfoDTO.getPayinfoId());
        paymentRequestDTO.setOrderId(orderPayinfoDTO.getObjectId());
        paymentRequestDTO.setOrderNo(orderPayinfoDTO.getObjectCode());
        paymentRequestDTO.setPayerMemberId(orderPayinfoDTO.getPayerId());
        paymentRequestDTO.setOperatorId(orderPayinfoDTO.getUpdateUser());
        paymentRequestDTO.setAmount(orderPayinfoDTO.getPayAmount().toString());
        paymentRequestDTO.setChannelCode(orderPayinfoDTO.getPayinfoWay());
        if (CollectionUtils.isEmpty(orderPayinfoDTO.getOrderPayinfoDetail())) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "找不到对应支付单的支付明细");
        }

        //获取订单明细
        List<String> remarkList = Lists.newArrayList();
        List<OrderItem> orderItemList = orderItemBiz.findByOrderId(orderPayinfoDTO.getObjectId());
        if (CsStringUtils.isNotBlank(orderPayinfoDTO.getTakeId())) {
            Map<String, OrderItem> orderItemMap = orderItemList.stream().collect(Collectors.toMap(OrderItem::getOrderItemId, Function.identity()));
            for (TakeItem takeItem : takeItemBiz.selectItemByTakeId(orderPayinfoDTO.getTakeId())) {
                OrderItem orderItem = orderItemMap.get(takeItem.getOrderItemId());
                remarkList.add(orderItem.getGoodsName() +
                        ArithUtils.multiply(takeItem.getTakeQuantity(), orderItem.getConvertRate()) + orderItem.getMeasureUnits());
            }
        }else{
            if (CollectionUtils.isEmpty(orderItemList)) {
                orderItemList.stream().forEach(orderItem -> remarkList.add(orderItem.getGoodsName() +
                        ArithUtils.multiply(orderItem.getItemQuantity(), orderItem.getConvertRate()) + orderItem.getMeasureUnits())
                );
            }
        }
        paymentRequestDTO.setRemarks(CsStringUtils.join(remarkList, ";"));
        List<PaymentRequirementDetailDTO> paymentDetailList = Lists.newArrayList();
        orderPayinfoDTO.getOrderPayinfoDetail().forEach(payDetail -> {
            PaymentRequirementDetailDTO paymentDetailDTO = new PaymentRequirementDetailDTO();
            paymentDetailDTO.setSubject(payDetail.getPaytypeDetail());
            paymentDetailDTO.setPayAmount(payDetail.getPayAdmount().setScale(2, RoundingMode.HALF_UP).toString());
            paymentDetailDTO.setPayeeMemberId(payDetail.getPayeeId());
            paymentDetailDTO.setPayeeMemberName(payDetail.getPayeeName());
            paymentDetailList.add(paymentDetailDTO);
        });
        paymentRequestDTO.setPaymentBillDetailDTOList(paymentDetailList);
        //pay项目就可以不用查询了，如果是新增的支付，单由于当前事务未提交，查询不到的
        paymentRequestDTO.setPaymentRequirementDTO(orderPayIntegrationService.getPaymentRequirementDTO(orderPayinfoDTO));
        String payInfoId = orderPayinfoDTO.getPayinfoId();
        try {

            // 查询订单扩展信息
            int isMonthly = 0;
            BigDecimal subtractAmount = BigDecimal.ZERO;
            if(ChannelCodeEnum.ERP.getCode().equals(paymentRequestDTO.getChannelCode()))
            {
                OrderInfo orderInfo = orderInfoBiz.getOrderInfoById(orderPayinfoDTO.getObjectId());
                subtractAmount = DeliveryWayEnum.PLATFORM_DELIVERY.getCode().equals(orderInfo.getDeliverWay()) ? orderInfo.getActualLogisticAmount() : BigDecimal.ZERO;


                OrderInfoExtDTO orderInfoExtDTO = orderInfoExtBiz.findByOrderId(orderPayinfoDTO.getObjectId());
                if(orderInfoExtDTO != null && orderInfoExtDTO.getIsMonthly() != null)
                {
                    isMonthly = orderInfoExtDTO.getIsMonthly();
                }
            }
            paymentRequestDTO.setIsMonthly(isMonthly);
            paymentRequestDTO.setSubtractAmount(subtractAmount);

            log.info("completeAutoPay_paymentRequestDTO:" + JSON.toJSONString(paymentRequestDTO));
            AutoPaymentResponseDTO autoPaymentResponseDTO = paymentService.autoPayment(paymentRequestDTO);
            log.info("completeAutoPay_autoPaymentResponseDTO:" + JSON.toJSONString(autoPaymentResponseDTO));
            //保存交易流水号
            OrderPayinfo saveOrderPayInfo = new OrderPayinfo();
            saveOrderPayInfo.setPayinfoId(orderPayinfoDTO.getPayinfoId());
            saveOrderPayInfo.setTradeBillId(autoPaymentResponseDTO.getTradeBillId());
            saveOrderPayInfo.setTradeBillNo(autoPaymentResponseDTO.getTradeBillNo());
            //设置支付备注(异常信息)
            saveOrderPayInfo.setMome(autoPaymentResponseDTO.getComment());
            orderPayInfoBiz.updateSelective(saveOrderPayInfo);
            //模拟支付回调
            PayCallbackDTO payCallbackDTO = new PayCallbackDTO();
            payCallbackDTO.setSucess(autoPaymentResponseDTO.getSuccess());
            payCallbackDTO.setObjectId(orderPayinfoDTO.getObjectId());
            payCallbackDTO.setActualAmount(orderPayinfoDTO.getPayAmount());
            payCallbackDTO.setTradeBillId(autoPaymentResponseDTO.getTradeBillId());
            payCallbackDTO.setTradeBillNo(autoPaymentResponseDTO.getTradeBillNo());
            payCallbackDTO.setPayWay(orderPayinfoDTO.getPayinfoWay());
            payTradeCallback(payCallbackDTO);
        } catch (Exception e) {
            syncSearchPayResult(payInfoId);
            log.error("自动支付失败,{}",  e.getMessage(),e);
            throw new BizException(BasicCode.UNDEFINED_ERROR, "自动支付失败");
        }
    }

    /**
     * 针对paymentService.autoPayment支付超时时(特别是ERP支付)，orderPayInfo没有记录到pay模块的交易id(paymentBillId)，导致页面手工重试交易一致无法成功
     * @param payInfoId
     */
    public void syncSearchPayResult(final String payInfoId){
        if (CsStringUtils.isBlank(payInfoId)) {
            return;
        }
        threadPoolExecutor.execute(()->{
            String old = Thread.currentThread().getName();
            try{
                OrderPayinfo payinfo = getOrderPayinfo(payInfoId);
                if (payinfo == null) return;
                log.info("syncSearchPayResult end payInfoId:{},tradeBillId:{},payStatus:{}",payInfoId,payinfo.getTradeBillId(),payinfo.getPayinfoStatus());
            }catch (Exception e){
                log.error("syncSearchPayResult:{}",e.getMessage(),e);
                Thread.currentThread().interrupt();
            }finally {
                Thread.currentThread().setName(old);
            }
        });

    }

    @Nullable
    private OrderPayinfo getOrderPayinfo(String payInfoId) throws InterruptedException {
        Thread.currentThread().setName(UUID.randomUUID().toString().replace("-",""));
        for (int i = 0; i < 20; i++) {//累计循环等待1分钟
            log.info("syncSearchPayResult,payInfoId:{},retry:{}", payInfoId,(i+1));
            Thread.sleep(3000);
            OrderPayinfo payinfo = orderPayInfoBiz.get(payInfoId);
            if (CsStringUtils.isNotBlank(payinfo.getTradeBillId())) {
                log.info("syncSearchPayResult,payInfoId:{},tradeBillId is not empty.", payInfoId);
                return null;
            }
            PaymentBillQueryDTO paymentBillQueryDTO = new PaymentBillQueryDTO();
            paymentBillQueryDTO.setSrcBizNo(payInfoId);
            paymentBillQueryDTO.setPaymentBillId(payinfo.getTradeBillId());
            paymentBillQueryDTO.setPaymentBillNo(payinfo.getTradeBillNo());
            paymentBillQueryDTO.setPageNum(1);
            paymentBillQueryDTO.setPageSize(5);
            PageInfo<PaymentBillDTO> paymentBillDTOPageInfo = paymentService.pagePaymentBill(paymentBillQueryDTO);
            if(paymentBillDTOPageInfo != null && CollectionUtils.isNotEmpty(paymentBillDTOPageInfo.getList())){
                PaymentBillDTO paymentBillDTO = paymentBillDTOPageInfo.getList().get(0);
                log.info("payinfo.payAmount:{},paymentBillDTO: {}",payinfo.getPayAmount(),JSON.toJSONString(paymentBillDTO));
                //如果还在支付中，则需要再等等一会儿
                if( PaymentStatusEnum.NEW_PAY.getCode().equals(paymentBillDTO.getStatus()) || PaymentStatusEnum.NEW_PAY.getMessage().equals(paymentBillDTO.getStatus()) ||
                        PaymentStatusEnum.PAY_ING.getCode().equals(paymentBillDTO.getStatus()) || PaymentStatusEnum.PAY_ING.getMessage().equals(paymentBillDTO.getStatus())){
                    log.info("支付中,继续等待 paymentBillDTO.getStatus(): {}",paymentBillDTO.getStatus());
                    continue;
                }
                //保存交易流水号
                OrderPayinfo saveOrderPayInfo = new OrderPayinfo();
                saveOrderPayInfo.setPayinfoId(payInfoId);
                saveOrderPayInfo.setTradeBillId(paymentBillDTO.getPaymentBillId());
                saveOrderPayInfo.setTradeBillNo(paymentBillDTO.getPaymentBillNo());
                //设置支付备注(异常信息)
                orderPayInfoBiz.updateSelective(saveOrderPayInfo);
                //模拟支付回调
                PayCallbackDTO payCallbackDTO = new PayCallbackDTO();
                payCallbackDTO.setSucess(PaymentStatusEnum.PAY_SUCCESS.getCode().equals(paymentBillDTO.getStatus()) ||
                        PaymentStatusEnum.PAY_FREEZE.getCode().equals(paymentBillDTO.getStatus())
                );
                payCallbackDTO.setObjectId(payinfo.getObjectId());
                payCallbackDTO.setActualAmount(paymentBillDTO.getPayAmount());
                payCallbackDTO.setTradeBillId(paymentBillDTO.getPaymentBillId());
                payCallbackDTO.setTradeBillNo(paymentBillDTO.getPaymentBillNo());
                payCallbackDTO.setPayWay(payinfo.getPayinfoWay());
                payTradeCallback(payCallbackDTO);
                return null;
            }else{
                log.info("syncSearchPayResult,payInfoId:{},retry:{},pagePaymentBill is empty", payInfoId,(i+1));
            }
        }
        OrderPayinfo payinfo = orderPayInfoBiz.get(payInfoId);
        if (CsStringUtils.isBlank(payinfo.getTradeBillId())) {
            log.error("syncSearchPayResult end payInfoId:{},tradeBillId is empty.", payInfoId);
            return null;
        }
        return payinfo;
    }

    @Override
    public List<String> querySupportAutoPayWayList(AutoPayWayQueryDTO autoPayWayQueryDTO) {
        log.info("querySupportAutoPayWayList_autoPayWayQueryDTO:" + JSON.toJSONString(autoPayWayQueryDTO));
        checkParams(autoPayWayQueryDTO);
        Collection<String> supportPayWay = Sets.newHashSet();
        //挂牌单
        if (CsStringUtils.equals(autoPayWayQueryDTO.getOrderType(), OrderTypeEnum.LISTING.getCode())) {
            //获取商品信息
            List<ResourceDTO> resourceList = resourceQueryService.findByResourceIds(autoPayWayQueryDTO.getResourceIdList());
            if (CollectionUtils.isEmpty(resourceList)) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "查询商品资源信息失败");
            }
            log.info("querySupportAutoPayWayList_resourceList:" + JSON.toJSONString(resourceList));
            for (ResourceDTO resourceDTO : resourceList) {
                if (CollectionUtils.isEmpty(supportPayWay)) {
                    supportPayWay.addAll(resourceDTO.getPayWay());
                    continue;
                }
                //求不同资源之间支持支付方式的交集
                if (CollectionUtils.isNotEmpty(resourceDTO.getPayWay())) {
                    supportPayWay = CollectionUtils.intersection(supportPayWay, resourceDTO.getPayWay());
                }
            }
            log.info("querySupportAutoPayWayList_supportPayWay:" + JSON.toJSONString(supportPayWay));
        }
        AutoPayChannelRequestDTO autoPayChannelRequestDTO = new AutoPayChannelRequestDTO();
        autoPayChannelRequestDTO.setPayerId(autoPayWayQueryDTO.getBuyerId());
        autoPayChannelRequestDTO.setPayeeId(autoPayWayQueryDTO.getSellerId());
        autoPayChannelRequestDTO.setClient(ClientType.getByCode(autoPayWayQueryDTO.getClientType()));
        autoPayChannelRequestDTO.setSupportChannelIds(supportPayWay);
        log.info("querySupportAutoPayWayList_autoPayChannelRequestDTO:" + JSON.toJSONString(autoPayChannelRequestDTO));
        List<MemberChannelDTO> memberChannelList = memberChannelService.getMemberAvailChannelsByPayee2(autoPayChannelRequestDTO);
        log.info("querySupportAutoPayWayList_memberChannelList:" + JSON.toJSONString(memberChannelList));
        if (CollectionUtils.isEmpty(memberChannelList)) {
            return Lists.newArrayList();
        }

        return memberChannelList.stream().map(MemberChannelDTO::getChannelCode).toList();
    }

    private static void checkParams(AutoPayWayQueryDTO autoPayWayQueryDTO) {
        if (CsStringUtils.isBlank(autoPayWayQueryDTO.getBuyerId())) {
            throw new BizException(BasicCode.INVALID_PARAM, "买家ID");
        }
        if (CsStringUtils.isBlank(autoPayWayQueryDTO.getSellerId())) {
            throw new BizException(BasicCode.INVALID_PARAM, "卖家ID");
        }
        if (CsStringUtils.isBlank(autoPayWayQueryDTO.getClientType())) {
            throw new BizException(BasicCode.INVALID_PARAM, "客户端类型");
        }
        if (CsStringUtils.isBlank(autoPayWayQueryDTO.getOrderType())) {
            throw new BizException(BasicCode.INVALID_PARAM, "下单类型");
        }
        if (CollectionUtils.isEmpty(autoPayWayQueryDTO.getResourceIdList())) {
            throw new BizException(BasicCode.INVALID_PARAM, "资源ID");
        }
    }

    @Override
    public String getPayInfoWayByTakeCode(String takeCode) {
        return orderPayInfoBiz.getPayInfoWayByTakeCode(takeCode);
    }

    /**
     * 合同调价退补款操作
     */
    @Override
    public void adjustRefundOrSupplement(OrderInfo orderInfo) {
        //获取当前订单已支付金额
        PaymentAmountQueryDO paymentAmountQueryDO = new PaymentAmountQueryDO();
        paymentAmountQueryDO.setOrderId(orderInfo.getOrderId());
        paymentAmountQueryDO.setPayStatusList(Lists.newArrayList(
                PayStatusEnum.IN_PAYMENT.code(),
                PayStatusEnum.COMPLETED.code()
        ));
        paymentAmountQueryDO.setPayType(Lists.newArrayList(
                PayTypeEnum.SINGLE.getCode(),
                PayTypeEnum.GROUP.getCode(),
                PayTypeEnum.SUPPLEMENT.getCode()
        ));
        PaymentAmountDO paymentAmountDO = computeOrderPaymentAmount(paymentAmountQueryDO);
        //计算当前的订单实际发生金额进行退补款
        BigDecimal overOrderAmount = ArithUtils.subtract(
                Boolean.FALSE, orderInfo.getRealtimeOrderAmount(), paymentAmountDO.getPaymentTotalAmount());
        //由于可能发生了退款所以需要减去退款金额
        overOrderAmount = ArithUtils.add(overOrderAmount, orderInfo.getRealRefundAmount());
        //订单发生金额大于已支付金额#补款
        if (overOrderAmount.compareTo(BigDecimal.ZERO) > 0) {
            SupplementPaymentDO supplementPaymentDO = new SupplementPaymentDO();
            OrderDTO order = orderQueryService.getOrderInfo(orderInfo.getOrderId());
            supplementPaymentDO.setOrder(order);
            supplementPaymentDO.setOperator(SYSTEM);
            supplementPaymentDO.setOverResourceAmount(overOrderAmount);
            supplementPaymentDO.setOverLogisticsAmount(BigDecimal.ZERO);
            supplementPaymentDO.setOverOthersAmount(BigDecimal.ZERO);
            //支付单备注无法计算量和价格 2021.4.22
            OrderItemDTO orderItem = null;
            if (CollectionUtils.isNotEmpty(order.getOrderItems())) {
                orderItem = order.getOrderItems().get(0);
                log.info("orderItem is not null.");
            }
            getSupplementPaymentDO(orderInfo, orderItem, order, paymentAmountDO, supplementPaymentDO);
            OrderPayinfo oversoldPayInfo = orderPayinfoJoinBiz.createOversoldPayInfo(supplementPaymentDO);
            //如果是授信支付或ERP余额，则尝试自动支付 bug fix #10118  2021.4.22
            if ((ChannelTypeEnum.CREDIT.getCode().equals(oversoldPayInfo.getPayinfoWay()) || ChannelTypeEnum.ERP.getCode().equals(oversoldPayInfo.getPayinfoWay()))) {
                boolean autoPayResult = orderOverSellBiz.autoPay(oversoldPayInfo, SYSTEM);
                log.info("尝试自动支付 orderId:{},payInfoId:{},autoPayResult:{}", autoPayResult);
                updateSelectiveV1(orderInfo, autoPayResult, oversoldPayInfo, order);
            }
        }
        contractAdjustRefundV1(orderInfo, overOrderAmount, paymentAmountDO);
    }

    private void getSupplementPaymentDO(OrderInfo orderInfo, OrderItemDTO orderItem, OrderDTO order, PaymentAmountDO paymentAmountDO, SupplementPaymentDO supplementPaymentDO) {
        OrderPayInfoInstructionsDTO desc = new OrderPayInfoInstructionsDTO();
        desc.setGoodsName(orderItem == null ? null : orderItem.getGoodsName());
        desc.setUnits(orderItem == null ? null : orderItem.getMeasureUnits());
        desc.setGoodsQuantity(orderItem == null ? null : orderItem.getItemSignQuantity());
        desc.setGoodsPrice(orderItem == null ? null : orderItem.getActualUnitPrice());
        desc.setGoodsAmount(order.getActualResourceAmount());
        desc.setPayTotalAmount(paymentAmountDO.getPaymentTotalAmount());//补款前总支付金额 当前订单总支付金额
        desc.setRealtimeOrderAmount(order.getRealtimeOrderAmount());//补款前总成交金额  当前订单总执行金额
        desc.setActualAmount(order.getActualResourceAmount());
        desc.setActualQuantity(desc.getGoodsQuantity());
        desc.setTradePrice(desc.getGoodsPrice());
        desc.setAdjustAmount(orderAdjustBiz.countAdjustAmount(orderInfo.getOrderId()));//人工调整费用
        supplementPaymentDO.setOrderPayInfoInstructionsDTOList(Lists.newArrayList(desc));
        if (order.getOrderInfoExtDTO().getLubricityQuantity() != null &&  order.getOrderInfoExtDTO().getLubricityQuantity().compareTo(BigDecimal.ZERO) > 0) {
            OrderPayInfoInstructionsDTO desc2 = new OrderPayInfoInstructionsDTO();
            desc2.setGoodsName("润管砂浆");
            desc2.setUnits(orderItem == null ? null : orderItem.getMeasureUnits());
            desc2.setGoodsQuantity(order.getOrderInfoExtDTO().getLubricitySignQuantity());
            desc2.setGoodsPrice(ArithUtils.add(order.getOrderInfoExtDTO().getLubricityPrice(), orderItem == null ? BigDecimal.ZERO : orderItem.getAdditemPrice()));
            desc2.setGoodsAmount(ArithUtils.multiply(order.getOrderInfoExtDTO().getLubricitySignQuantity(),desc2.getGoodsPrice()));
            desc2.setTradeAmount(desc2.getGoodsAmount());
            desc2.setActualAmount(desc2.getGoodsAmount());
            desc2.setActualQuantity(desc2.getGoodsQuantity());
            desc2.setTradePrice(desc2.getGoodsPrice());
            supplementPaymentDO.getOrderPayInfoInstructionsDTOList().add(desc2);
        }
    }

    private void updateSelectiveV1(OrderInfo orderInfo, boolean autoPayResult, OrderPayinfo oversoldPayInfo, OrderDTO order) {
        if (autoPayResult) {
            //修改支付单信息
            OrderPayinfo updatePayInfo = new OrderPayinfo();
            updatePayInfo.setPayinfoId(oversoldPayInfo.getPayinfoId());
            updatePayInfo.setTradeBillId(oversoldPayInfo.getTradeBillId());
            updatePayInfo.setTradeBillNo(oversoldPayInfo.getTradeBillNo());
            updatePayInfo.setPayDescription(oversoldPayInfo.getPayDescription());
            //修改支付单状态为已完成
            updatePayInfo.setPayinfoStatus(PayStatusEnum.COMPLETED.getCode());
            updatePayInfo.setPayTime(new Date());
            orderPayInfoBiz.updateSelective(updatePayInfo);
            //更新已支付金额
            OrderInfo saveOrder = new OrderInfo();
            saveOrder.setOrderId(oversoldPayInfo.getObjectId());
            saveOrder.setOrderPayedAmount(ArithUtils.add(order.getOrderPayedAmount(), oversoldPayInfo.getPayAmount()));
            saveOrder.setSupplementAmount(ArithUtils.add(orderInfo.getSupplementAmount(), oversoldPayInfo.getPayAmount()));
            orderInfoBiz.updateSelective(saveOrder);
        }
    }

    private void contractAdjustRefundV1(OrderInfo orderInfo, BigDecimal overOrderAmount, PaymentAmountDO paymentAmountDO) {
        if (overOrderAmount.compareTo(BigDecimal.ZERO) < 0) {
            OrderRefundDTO orderRefundDTO = new OrderRefundDTO();
            orderRefundDTO.setOrderId(orderInfo.getOrderId());
            orderRefundDTO.setRefundType(PayTypeEnum.REFUND.getCode());
            orderRefundDTO.setRefundStatus(RefundStatusEnum.WAIT_PAYMENT.code());
            orderRefundDTO.setPayInfoWay(paymentAmountDO.getPayInfoWay());
            //退款金额
            orderRefundDTO.setAmountPrice(overOrderAmount.abs());
            orderRefundDTO.setRealtimeResourceAmount(orderInfo.getRealtimeResourceAmount());
            orderRefundDTO.setRealtimeLogisticAmount(orderInfo.getRealtimeLogisticAmount());
            orderRefundDTO.setRealtimeOthersAmount(orderInfo.getRealtimeOthersAmount());
            orderRefundDTO.setRealtimeCarryAmount(BigDecimal.ZERO);
            orderRefundDTO.setResourcePrice(overOrderAmount.negate());
            orderRefundDTO.setLogisticPrice(BigDecimal.ZERO);
            orderRefundDTO.setOtherPrice(BigDecimal.ZERO);
            orderRefundDTO.setMome("合同调价退款");
            orderRefundDTO.setCloseReason("合同调价退款");
            orderRefundDTO.setCreateUser(SYSTEM);
            orderRefundDTO.setUpdateUser(SYSTEM);
            //合同调价退款
            orderPayinfoService.contractAdjustRefund(orderRefundDTO);
        }
    }
}
