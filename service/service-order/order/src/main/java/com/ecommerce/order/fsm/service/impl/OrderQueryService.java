package com.ecommerce.order.fsm.service.impl;

import com.alibaba.fastjson.JSON;
import com.ecommerce.base.api.dto.ReceivingAddressDTO;
import com.ecommerce.base.api.dto.saleregion.SaleRegionCondDTO;
import com.ecommerce.base.api.dto.saleregion.SaleRegionDTO;
import com.ecommerce.base.api.dto.saleregion.SaleRegionSampleDTO;
import com.ecommerce.base.api.dto.warehouse.WarehouseDetailsDTO;
import com.ecommerce.base.api.service.IReceivingAddressService;
import com.ecommerce.base.api.service.ISaleRegionService;
import com.ecommerce.base.api.service.IWarehouseService;
import com.ecommerce.common.annotation.PrintArgs;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.service.common.uuid.UUIDGenerator;
import com.ecommerce.common.utils.ArithUtils;
import com.ecommerce.common.utils.BeanConvertUtils;
import com.ecommerce.common.utils.CommonConstants;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.common.utils.DateUtil;
import com.ecommerce.goods.api.dto.CategoryTreeDTO;
import com.ecommerce.goods.api.dto.GoodsCategorySimpleDTO;
import com.ecommerce.goods.api.dto.GoodsDTO;
import com.ecommerce.goods.api.dto.QueryResourceConditionDTO;
import com.ecommerce.goods.api.dto.ResourceDTO;
import com.ecommerce.goods.api.dto.SpecialGoodsAttributeDTO;
import com.ecommerce.goods.api.dto.UnitConverDTO;
import com.ecommerce.goods.api.dto.contract.ContractOption;
import com.ecommerce.goods.api.dto.contract.QueryContractConditionDTO;
import com.ecommerce.goods.api.dto.contract.SimpleContractDTO;
import com.ecommerce.goods.api.dto.contract.TrContractGoodsDTO;
import com.ecommerce.goods.api.dto.contract.TrContractGoodsVarietyDTO;
import com.ecommerce.goods.api.dto.contract.concrete.ContractGoodsVarietyQueryDTO;
import com.ecommerce.goods.api.enums.PriceWayEnum;
import com.ecommerce.goods.api.enums.TradeStatusEnum;
import com.ecommerce.goods.api.enums.contract.ContractStatusEnum;
import com.ecommerce.goods.api.service.IContractService;
import com.ecommerce.goods.api.service.IGoodsCategoryService;
import com.ecommerce.goods.api.service.IGoodsService;
import com.ecommerce.goods.api.service.IResourceService;
import com.ecommerce.logistics.api.dto.carriage.CarriageRouteQueryDTO;
import com.ecommerce.logistics.api.dto.carriage.TransportRouteDTO;
import com.ecommerce.logistics.api.dto.rulecompute.CarriageComputeDTO;
import com.ecommerce.logistics.api.dto.rulecompute.CarriageComputeResultDTO;
import com.ecommerce.logistics.api.dto.rulecompute.CategoryQuantityDTO;
import com.ecommerce.logistics.api.dto.shipbill.ShipBillOrderDetailDTO;
import com.ecommerce.logistics.api.enums.PickingBillTypeEnum;
import com.ecommerce.logistics.api.service.ICarriageRouteService;
import com.ecommerce.logistics.api.service.IRuleComputeService;
import com.ecommerce.logistics.api.service.IShipBillService;
import com.ecommerce.member.api.dto.account.AccountSimpleDTO;
import com.ecommerce.member.api.dto.member.MemberSimpleDTO;
import com.ecommerce.member.api.service.IAccountService;
import com.ecommerce.member.api.service.IMemberRelationService;
import com.ecommerce.member.api.service.IMemberService;
import com.ecommerce.order.api.dto.BillSplitDTO;
import com.ecommerce.order.api.dto.CartAdditemDTO;
import com.ecommerce.order.api.dto.ERPAddressDTO;
import com.ecommerce.order.api.dto.ERPAddressQueryDTO;
import com.ecommerce.order.api.dto.OrderBillCheckDTO;
import com.ecommerce.order.api.dto.OrderBillCheckQueryDTO;
import com.ecommerce.order.api.dto.OrderCountDTO;
import com.ecommerce.order.api.dto.OrderDTO;
import com.ecommerce.order.api.dto.OrderDetailConditionDTO;
import com.ecommerce.order.api.dto.OrderDiscountDetailDTO;
import com.ecommerce.order.api.dto.OrderIdAndCodeDTO;
import com.ecommerce.order.api.dto.OrderIdAndContractIdDTO;
import com.ecommerce.order.api.dto.OrderInfoExtDTO;
import com.ecommerce.order.api.dto.OrderItemAddDTO;
import com.ecommerce.order.api.dto.OrderItemDTO;
import com.ecommerce.order.api.dto.OrderLogisticsItemDTO;
import com.ecommerce.order.api.dto.OrderLogisticsQueryDTO;
import com.ecommerce.order.api.dto.OrderLogisticsResultDTO;
import com.ecommerce.order.api.dto.OrderOverviewDTO;
import com.ecommerce.order.api.dto.OrderPayInfoInstructionsDTO;
import com.ecommerce.order.api.dto.OrderPayinfoDTO;
import com.ecommerce.order.api.dto.OrderPayinfoDetailDTO;
import com.ecommerce.order.api.dto.OrderPayinfoQueryDTO;
import com.ecommerce.order.api.dto.OrderQueryDTO;
import com.ecommerce.order.api.dto.OrderRefundDTO;
import com.ecommerce.order.api.dto.OrderResoureChangeDTO;
import com.ecommerce.order.api.dto.OrderSimpleDTO;
import com.ecommerce.order.api.dto.OrderSimpleQueryContractDTO;
import com.ecommerce.order.api.dto.OrderSimpleQueryContractGoodsDTO;
import com.ecommerce.order.api.dto.OrderSimpleQueryDTO;
import com.ecommerce.order.api.dto.OrderStatusStatisticDTO;
import com.ecommerce.order.api.dto.OrderStatusStatisticsDTO;
import com.ecommerce.order.api.dto.OrderSubStatusDTO;
import com.ecommerce.order.api.dto.OrderTaxinfoDTO;
import com.ecommerce.order.api.dto.OverviewReqDTO;
import com.ecommerce.order.api.dto.ProportionDTO;
import com.ecommerce.order.api.dto.RepurchaseGoodsDTO;
import com.ecommerce.order.api.dto.RepurchaseOrderDTO;
import com.ecommerce.order.api.dto.TakeInfoDTO;
import com.ecommerce.order.api.dto.TrContractDTO;
import com.ecommerce.order.api.dto.TransactionsOverviewDTO;
import com.ecommerce.order.api.dto.UninvoicedOrderDTO;
import com.ecommerce.order.api.dto.UninvoicedOrderItemDTO;
import com.ecommerce.order.api.dto.UninvoicedOrderReqDTO;
import com.ecommerce.order.api.dto.logistics.PickingBillExportInfoDTO;
import com.ecommerce.order.api.dto.pay.TradingFlowExportInfoDTO;
import com.ecommerce.order.api.dto.trade.TrContractAdditemDTO;
import com.ecommerce.order.api.enums.BillPaymentTypeEnum;
import com.ecommerce.order.api.enums.FloorNumberEnum;
import com.ecommerce.order.api.enums.OrderExceptionStatusEnum;
import com.ecommerce.order.api.enums.OrderInvoiceInstitution;
import com.ecommerce.order.api.enums.OrderInvoiceStatus;
import com.ecommerce.order.api.enums.OrderStatusEnum;
import com.ecommerce.order.api.enums.OrderTypeEnum;
import com.ecommerce.order.api.enums.PayDetailTypeEnum;
import com.ecommerce.order.api.enums.PayStatusEnum;
import com.ecommerce.order.api.enums.PayTypeEnum;
import com.ecommerce.order.api.enums.PayWayEnum;
import com.ecommerce.order.api.enums.ProxyOrderTypeEnum;
import com.ecommerce.order.biz.IBillSplitBiz;
import com.ecommerce.order.biz.IOrderDiscountDetailBiz;
import com.ecommerce.order.biz.IOrderErpAddressBiz;
import com.ecommerce.order.biz.IOrderExceptionBiz;
import com.ecommerce.order.biz.IOrderPayinfoAttachmentBiz;
import com.ecommerce.order.biz.IOrderPayinfoDetailBiz;
import com.ecommerce.order.biz.IOrderProxyMapBiz;
import com.ecommerce.order.biz.IOrderResoureChangeBiz;
import com.ecommerce.order.biz.IOrderSubStatusBiz;
import com.ecommerce.order.biz.IOrderTaxinfoBiz;
import com.ecommerce.order.biz.fsm.IOrderInfoBiz;
import com.ecommerce.order.biz.fsm.IOrderInfoExtBiz;
import com.ecommerce.order.biz.fsm.IOrderItemAddBiz;
import com.ecommerce.order.biz.fsm.IOrderItemBiz;
import com.ecommerce.order.biz.fsm.IOrderPayinfoBiz;
import com.ecommerce.order.biz.fsm.IOrderRefundBiz;
import com.ecommerce.order.dao.vo.BillSplit;
import com.ecommerce.order.dao.vo.OrderDiscountDetail;
import com.ecommerce.order.dao.vo.OrderException;
import com.ecommerce.order.dao.vo.OrderInfo;
import com.ecommerce.order.dao.vo.OrderItem;
import com.ecommerce.order.dao.vo.OrderItemAdd;
import com.ecommerce.order.dao.vo.OrderPayinfo;
import com.ecommerce.order.dao.vo.OrderPayinfoDetail;
import com.ecommerce.order.dao.vo.OrderProxyMap;
import com.ecommerce.order.dao.vo.OrderRefund;
import com.ecommerce.order.dao.vo.OrderResoureChange;
import com.ecommerce.order.dao.vo.OrderTaxinfo;
import com.ecommerce.order.dao.vo.TakeInfo;
import com.ecommerce.order.fsm.service.IOrderQueryService;
import com.ecommerce.order.util.GoodsNameOpetimization;
import com.ecommerce.pay.api.v2.enums.ChannelCodeEnum;
import com.ecommerce.price.api.dto.BuyerPromotionalDetailsDTO;
import com.ecommerce.price.api.dto.PromotionalDTO;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.Comparator;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * * Date: Create in 下午3:06 20/6/15
 * <AUTHOR>
 */
@Slf4j
@Service
public class OrderQueryService implements IOrderQueryService {

    @Value("${spring.cloud.config.profile:dev}")
    private String profile;

    @Autowired
    protected UUIDGenerator uuidGenerator;
    @Autowired
    protected IAccountService accountService;
    @Autowired
    protected IOrderInfoBiz orderInfoBiz;
    @Autowired
    private IOrderInfoExtBiz orderInfoExtBiz;
    @Autowired
    protected IOrderTaxinfoBiz orderTaxinfoBiz;
    @Autowired
    protected IOrderPayinfoBiz orderPayinfoBiz;
    @Autowired
    protected IOrderPayinfoDetailBiz orderPayinfoDetailBiz;
    @Autowired
    protected IOrderPayinfoAttachmentBiz orderPayinfoAttachmentBiz;
    @Autowired
    protected IOrderRefundBiz orderRefundBiz;
    @Autowired
    protected IOrderResoureChangeBiz orderResoureChangeBiz;
    @Autowired
    protected IOrderDiscountDetailBiz orderDiscountDetailBiz;
    @Autowired
    protected IOrderItemBiz orderItemBiz;
    @Autowired
    protected IOrderItemAddBiz orderItemAddBiz;
    @Autowired
    protected com.ecommerce.order.biz.fsm.ITakeInfoBiz takeInfoBiz;
    @Autowired
    protected IGoodsService goodsService;
    @Autowired
    protected IResourceService resourceService;
    @Autowired
    protected IMemberService memberService;
    @Autowired
    protected IMemberRelationService memberRelationService;
    @Autowired
    protected IReceivingAddressService receivingAddressService;
    @Autowired
    protected IRuleComputeService ruleComputeService;
    @Autowired
    protected IContractService contractService;
    @Autowired
    protected IWarehouseService warehouseService;
    @Autowired
    protected IGoodsCategoryService goodsCategoryService;
    @Autowired
    private IBillSplitBiz billSplitBiz;
    @Autowired
    private IOrderExceptionBiz orderExceptionBiz;
    @Autowired
    private IOrderSubStatusBiz orderSubStatusBiz;
    @Autowired
    private ISaleRegionService saleRegionService;
    @Autowired
    private IOrderErpAddressBiz orderErpAddressBiz;
    @Autowired
    private IOrderProxyMapBiz orderProxyMapBiz;
    @Autowired
    private IShipBillService shipBillService;
    @Autowired
    protected ICarriageRouteService carriageRouteService;

    private static final String CREATE_TIME = "createTime";
    private static final String DEL_FLG = "delFlg";
    private static final String DEL_FLAG = "delFlag";
    private static final String PAYINFO_TYPE = "payinfoType";
    private static final String ORDER_STATUS = "orderStatus";
    private static final String OBJECT_ID = "objectId";
    private static final String CREATE_TIME1 = "create_time";
    private static final String ORDER_ID = "orderId";
    private static final String UPDATE_TIME = "updateTime";
    private static final String SELLER_ID = "sellerId";
    private static final String BUYER_ID = "buyerId";
    private static final String TAKE_ID = "takeId";
    private static final String TAKE_CODE = "takeCode";

    @Override
    public Page<OrderDTO> searchOrderPage(OrderQueryDTO orderQuery, Integer pageSize, Integer pageNum) {
        log.info("searchOrderPage_orderQuery:" + JSON.toJSONString(orderQuery));
        //开发环境不验证数据权限
        if (CsStringUtils.equals(profile.toLowerCase(), "dev") || CsStringUtils.equals(profile.toLowerCase(), "test")) {
            orderQuery.setRegionList(null);
            orderQuery.setRegionCodeList(null);
            orderQuery.setMdmCodeList(null);
        }
        if (CollectionUtils.isEmpty(orderQuery.getRegionList())) {
            orderQuery.setRegionList(null);
        }
        orderQuery.setKeyWords(GoodsNameOpetimization.queryOptimizationByGoodName(orderQuery.getKeyWords()));
        orderQuery.setResourceName((GoodsNameOpetimization.queryOptimizationByGoodName(orderQuery.getResourceName())));
        Page<OrderInfo> page = PageMethod.startPage(pageNum, pageSize).doSelectPage(() -> orderInfoBiz.listQuery(orderQuery));
        Page<OrderDTO> pageDTO = BeanConvertUtils.convertPage(page, OrderDTO.class);
        if (CollectionUtils.isNotEmpty(pageDTO.getResult())) {
            List<OrderDTO> orderDTOList = pageDTO.getResult();
            if (CollectionUtils.isNotEmpty(orderDTOList)) {
                batchSetOrderItems(orderDTOList);
                batchSetOrderPayInfo(orderDTOList);
                batchSetOrderRefund(orderDTOList);
                batchSetOrderSalesman(orderDTOList);
                batchSetReceiver(orderDTOList);
                batchSetOrderSubStatus(orderDTOList);
                batchSetContract(orderDTOList);
                batchSetSaleArea(orderDTOList);
                batchSetOrderProxyMapInfo(orderDTOList);
                batchSetERPAddress(orderDTOList);
                batchSetOrderInfoExt(orderDTOList);
            }
            for (OrderDTO order : orderDTOList) {
                //设置订单其它费用(货款、物流款以外的费用)
                setOrderOthersAmount(order);
                // 计算结算金额
                computeSettlementAmountWithoutQuery(order);
            }
        }
        return pageDTO;
    }

    @Override
    public PageInfo<UninvoicedOrderDTO> pageUninvoicedOrderList(UninvoicedOrderReqDTO uninvoicedOrderReqDTO) {
        String sellerId = uninvoicedOrderReqDTO.getSellerId();
        String sellerName = uninvoicedOrderReqDTO.getSellerName();
        String buyerId = uninvoicedOrderReqDTO.getBuyerId();
        String orderCode = uninvoicedOrderReqDTO.getOrderCode();
        Date completeStartTime = uninvoicedOrderReqDTO.getCompleteStartTime();
        Date completeEndTime = uninvoicedOrderReqDTO.getCompleteEndTime();
        Date createStartTime = uninvoicedOrderReqDTO.getCreateStartTime();
        Date createEndTime = uninvoicedOrderReqDTO.getCreateEndTime();
        Integer pageNum = uninvoicedOrderReqDTO.getPageNum();
        Integer pageSize = uninvoicedOrderReqDTO.getPageSize();
        // 参数检测
        if (CsStringUtils.isBlank(buyerId)) {
            throw new BizException(BasicCode.PARAM_NULL, "买家ID");
        }
        //查询满足条件的订单
        Condition selectCondition = new Condition(OrderInfo.class);
        Example.Criteria selectCriteria = selectCondition.createCriteria();
        selectCriteriaSetParams(sellerId, selectCriteria, sellerName, orderCode, createStartTime, createEndTime, completeStartTime, completeEndTime);
        //开票状态
        List<String> invoiceStatusList = Lists.newArrayList();
        invoiceStatusList.add(OrderInvoiceStatus.UNINVOICE.getCode());
        invoiceStatusList.add(OrderInvoiceStatus.INVOICEING.getCode());
        invoiceStatusList.add(OrderInvoiceStatus.WRITE_OFF.getCode());
        invoiceStatusList.add(OrderInvoiceStatus.INVOICE_FAIL.getCode());
        selectCriteria.andIn("invoiceStatus", invoiceStatusList);
        //订单状态(最终状态可以开票)
        selectCriteria.andIn(ORDER_STATUS, Lists.newArrayList(
                OrderStatusEnum.CLOSED.code(),
                OrderStatusEnum.COMPLETED.code()));
        //已执行总金额大于零
        selectCriteria.andGreaterThan("realtimeOrderAmount", BigDecimal.ZERO);
        selectCriteria.andEqualTo(BUYER_ID, buyerId);
        selectCriteria.andEqualTo(DEL_FLG, false);
        selectCondition.orderBy(UPDATE_TIME).asc();

        StringBuilder critCondition = new StringBuilder("invoice_status1 is null or (invoice_status1 <> 300 and invoice_status1 <> 400) or (invoice_status2 <> 300 and invoice_status2 <> 400)");
        Example.Criteria criteria = selectCondition.createCriteria();
        criteria.andCondition(critCondition.toString());
        selectCondition.and(criteria);
        // 分页查询数据库
        Page<OrderInfo> page = PageMethod.startPage(pageNum, pageSize, true, false, null)
                .doSelectPage(() -> orderInfoBiz.findByCondition(selectCondition));
        // 结果封装
        PageInfo<UninvoicedOrderDTO> pageInfo = new PageInfo<>();
        pageInfo.setPageNum(pageNum);
        pageInfo.setPageSize(pageSize);
        if (page.size() < 1) {
            pageInfo.setPages(0);
            pageInfo.setTotal(0);
            pageInfo.setList(new ArrayList<>());
            return pageInfo;
        }
        pageInfo.setPages(page.getPages());
        pageInfo.setTotal(page.getTotal());
        List<OrderInfo> orderInfos = page.getResult();
        List<UninvoicedOrderDTO> list = Lists.newArrayList();
        batchSetBillSplitDTOsAndBatchSetOrderItemDTOs(orderInfos, list);
        pageInfo.setList(list);
        return pageInfo;
    }

    private void batchSetBillSplitDTOsAndBatchSetOrderItemDTOs(List<OrderInfo> orderInfos, List<UninvoicedOrderDTO> list) {
        if (CollectionUtils.isNotEmpty(orderInfos)) {
            for (OrderInfo orderInfo : orderInfos) {
                UninvoicedOrderDTO dto = new UninvoicedOrderDTO();
                BeanUtils.copyProperties(orderInfo, dto);
                setCompleteTime(orderInfo, dto);
                if (CsStringUtils.equals(com.ecommerce.goods.api.enums.DeliveryWayEnum.PLATFORM_DELIVERY.code(), orderInfo.getDeliverWay())) {
                    dto.setInvoiceInstitution(OrderInvoiceInstitution.SECOND.getCode());
                } else if (CsStringUtils.equals(com.ecommerce.goods.api.enums.DeliveryWayEnum.BUYER_TAKE.code(), orderInfo.getDeliverWay())) {
                    dto.setInvoiceInstitution(OrderInvoiceInstitution.ONE.getCode());
                } else {
                    //查询合同
                    List<ContractOption> contractOptions = contractService.queryContractOptions(Sets.newHashSet(orderInfo.getDealsId()));
                    if (CollectionUtils.isNotEmpty(contractOptions) && CsStringUtils.equals(contractOptions.get(0).getBillType(), "02")) {
                        dto.setInvoiceInstitution(OrderInvoiceInstitution.SECOND.getCode());
                    } else {
                        dto.setInvoiceInstitution(OrderInvoiceInstitution.ONE.getCode());
                    }
                }

                list.add(dto);
            }
            //设置订单子项
            batchSetOrderItemDTOs(list);
            //设置分账明细
            batchSetBillSplitDTOs(list);
        }
    }

    private static void setCompleteTime(OrderInfo orderInfo, UninvoicedOrderDTO dto) {
        //关闭订单设置完成时间为关闭时间
        if (dto.getCompleteTime() == null) {
            dto.setCompleteTime(orderInfo.getCloseTime());
        }
        //关闭时间为空设置更新时间
        if (dto.getCompleteTime() == null) {
            dto.setCompleteTime(orderInfo.getUpdateTime());
        }
    }

    private static void selectCriteriaSetParams(String sellerId, Example.Criteria selectCriteria, String sellerName, String orderCode, Date createStartTime, Date createEndTime, Date completeStartTime, Date completeEndTime) {
        if (CsStringUtils.isNotBlank(sellerId)) {
            selectCriteria.andEqualTo(SELLER_ID, sellerId);
        }
        if (CsStringUtils.isNotBlank(sellerName)) {
            selectCriteria.andLike("sellerName", "%" + sellerName + "%");
        }
        if (CsStringUtils.isNotBlank(orderCode)) {
            selectCriteria.andLike("orderCode", "%" + orderCode + "%");
        }
        if (createStartTime != null) {
            selectCriteria.andGreaterThanOrEqualTo(CREATE_TIME, createStartTime);
        }
        if (createEndTime != null) {
            long timeStamp = createEndTime.getTime() + 86400000 - 1;
            selectCriteria.andLessThanOrEqualTo(CREATE_TIME, new Date(timeStamp));
        }
        if (completeStartTime != null) {
            selectCriteria.andGreaterThanOrEqualTo("completeTime", completeStartTime);
        }
        if (completeEndTime != null) {
            long timeStamp = completeEndTime.getTime() + 86400000 - 1;
            selectCriteria.andLessThanOrEqualTo("completeTime", new Date(timeStamp));
        }
    }

    @Override
    public List<OrderDTO> queryOrderListById(List<String> orderIds) {
        return null;
    }

    @Override
    public int countOrders(OrderCountDTO orderCountDTO) {
        return orderInfoBiz.countOrders(orderCountDTO);
    }

    @Override
    public OrderDTO getOrderDetail(String orderId) {
        if (CsStringUtils.isBlank(orderId)) {
            throw new BizException(BasicCode.PARAM_NULL, "订单Id");
        }

        OrderInfo orderInfo = orderInfoBiz.getOrderInfoById(orderId);
        if (orderInfo != null) {
            List<String> objectIds = Lists.newArrayList();//订单优惠明细objectId
            objectIds.add(orderId);
            OrderDTO order = BeanConvertUtils.convert(orderInfo, OrderDTO.class);
            if (CsStringUtils.isNotBlank(order.getCarryFloor())) {
                if (FloorNumberEnum.getByCode(order.getCarryFloor()) != null) {
                    order.setCarryFloorStr(FloorNumberEnum.getByCode(order.getCarryFloor()).message());
                } else {
                    order.setCarryFloorStr(order.getCarryFloor());
                }
            }

            BigDecimal refundAmount = BigDecimal.ZERO;

            //设置订单扩展信息
            order.setOrderInfoExtDTO(orderInfoExtBiz.findByOrderId(order.getOrderId()));
            order.setSupportCarryFlag(0);
            //订单行项目
            OrderItem queryItem = new OrderItem();
            queryItem.setOrderId(order.getOrderId());
            queryItem.setDelFlg(false);
            List<OrderItem> items = orderItemBiz.find(queryItem);
            refundAmount = getRefundAmount(items, refundAmount, order, objectIds);

            order.setItemRefundAmount(refundAmount);

            //设置订单异常信息
            List<OrderException> orderExceptions = orderExceptionBiz.queryByObjIdAndStatus(order.getOrderId(), OrderExceptionStatusEnum.HANDLE_WAITING.getCode(), "");
            if (CollectionUtils.isNotEmpty(orderExceptions))
                order.setErpErroescription(orderExceptions.get(0).getExContent());

            //订单优惠明细
            order.setDiscountDetail(getOrderDiscountDetails(objectIds));

            //锁价前价格变化历史
            setOrderResoureChange(order);
            //支付单
            setOrderPayinfo(order);
            //退款请求单
            order = setOrderRefund(order);
            //开票信息
            setOrderTaxinfo(order);
            //设置业务员信息
            order = setOrderSalesman(order);
            //设置收货员信息
            order = setReceiver(order);
            //设置订单其它费用(货款、物流款以外的费用)
            setOrderOthersAmount(order);
            // 计算结算金额
            computeSettlementAmount(order);
            // 设置合同信息
            setContract(order);
            // 设置订单子状态
            setOrderSubStatus(order);
            batchSetOrderProxyMapInfo(Lists.newArrayList(order));
            //获取ERP流向管控地址
            ERPAddressQueryDTO erpAddressQueryDTO = new ERPAddressQueryDTO();
            erpAddressQueryDTO.setOrderId(order.getOrderId());
            List<ERPAddressDTO> erpAddressList = orderErpAddressBiz.queryERPAddressList(erpAddressQueryDTO);
            if (CollectionUtils.isNotEmpty(erpAddressList) &&
                    CsStringUtils.isNotBlank(erpAddressList.get(0).getErpUnloadAddressId())) {
                order.setErpAddressDTO(erpAddressList.get(0));
            }
            if (order.getPrimaryOrder()) {
                // 设置二级订单买家名称
                order.setSecondaryBuyerName(Optional.ofNullable(orderProxyMapBiz.querySecondaryOrderInfoByPrimaryOrderId(order.getOrderId())).orElse(new OrderInfo()).getBuyerName());
            }

            return order;
        }

        return null;
    }

    private BigDecimal getRefundAmount(List<OrderItem> items, BigDecimal refundAmount, OrderDTO order, List<String> objectIds) {
        if (CollectionUtils.isNotEmpty(items)) {
            List<OrderItemDTO> orderItems = Lists.newArrayList();
            for (OrderItem item : items) {
                GoodsDTO goodsDTO = null;
                if (CsStringUtils.isNotBlank(item.getGoodsId())) {
                    goodsDTO = goodsService.getGoodsInfo(item.getGoodsId()).getData();
                }
                if (Objects.isNull(goodsDTO)) {
                    continue;
                }

                OrderItemDTO orderItemDTO = convertItemDTO(item, goodsDTO);

                if (orderItemDTO.getItemRefundAmount() != null) {
                    refundAmount = refundAmount.add(orderItemDTO.getItemRefundAmount());
                }

                //支持搬运标识
                order.setSupportCarryFlag(goodsDTO.getSupportCarryFlag());
                orderItemDTO.setLogisticsId(goodsDTO.getLogistics());
                orderItemDTO.setLogistics(goodsDTO.getLogistics());
                //计算计量单位数量
                setMeasureQuantity(item, orderItemDTO);
                orderItems.add(orderItemDTO);
                //在订单原始资源金额上累加上加价项金额
                order.setOriginResourceAmount(ArithUtils.add(order.getOriginResourceAmount(),
                        ArithUtils.multiply(orderItemDTO.getItemQuantity(), orderItemDTO.getAdditemPrice())));
                objectIds.add(item.getOrderItemId());
                //新增搬运费规则
                if (!order.isHaveCartageRule()) {
                    order.setHaveCartageRule(!CsStringUtils.isBlank(goodsDTO.getCartageRule()));
                }
                //分合同单和挂牌单分别判断，如果修改了定价逻辑需要做相应修改
                setPriceWay(order, orderItemDTO);
            }
            //设置加价项
            batchSetOrderItemAdd(orderItems);
            order.setOrderItems(orderItems);
        }
        return refundAmount;
    }

    private static void setPriceWay(OrderDTO order, OrderItemDTO orderItemDTO) {
        if (OrderTypeEnum.SELF.getCode().equals(order.getOrderType())) {
            //到位置价，忽略物流费
            if (order.getDeliverWay().equals(PickingBillTypeEnum.BUYER_TAKE.getCode())) {
                orderItemDTO.setPriceWay(PriceWayEnum.PRICE_TYPE1.code());
            } else {
                orderItemDTO.setPriceWay(PriceWayEnum.PRICE_TYPE2.code());
            }
        }
        if (OrderTypeEnum.CONTRACT.getCode().equals(order.getOrderType())) {
            //合同单
            if (order.getOriginLogisticAmount() != null && order.getOriginLogisticAmount().compareTo(BigDecimal.ZERO) > 0) {
                //有物流费，判断为出厂价，价格以物流为准计算补款
                orderItemDTO.setPriceWay(PriceWayEnum.PRICE_TYPE2.code());
            } else {
                //无物流费，判断为到位价格，物流费不计入补款
                orderItemDTO.setPriceWay(PriceWayEnum.PRICE_TYPE1.code());
            }
        }
    }

    private void setMeasureQuantity(OrderItem item, OrderItemDTO orderItemDTO) {
        try {
            orderItemDTO.setMeasureQuantity(item.getItemQuantity());
            List<UnitConverDTO> unitConvers = goodsService.getUnitConverInfo(item.getGoodsId()).getData();
            for (UnitConverDTO unitConv : unitConvers) {
                if (unitConv.isDefault() &&
                        item.getUnits().equals(unitConv.getUnit2())) {//计量单位
                    orderItemDTO.setMeasureQuantity(ArithUtils.divide(item.getItemQuantity(), unitConv.getRatio()));
                    break;
                }
            }
        } catch (Exception e) {
            log.error("getUnitConverInfo_Error:" + item.getGoodsId(), e);
            throw new BizException(BasicCode.UNDEFINED_ERROR, "计算计量单位数量失败");
        }
    }

    @Override
    public OrderDTO getOrderTableInfo(String orderId) {
        if (CsStringUtils.isBlank(orderId)) {
            return null;
        }
        OrderInfo orderInfo = orderInfoBiz.get(orderId);
        if (orderInfo == null || BooleanUtils.isTrue(orderInfo.getDelFlg())) {
            return null;
        }
        OrderDTO orderDTO = new OrderDTO();
        BeanUtils.copyProperties(orderInfo, orderDTO);
        return orderDTO;
    }

    @Override
    public OrderDTO getOrderTableInfoByTakeCode(String takeCode) {
        TakeInfo takeInfo = null;
        try {
            takeInfo = takeInfoBiz.getTakeInfoByCode(takeCode);
        } catch (BizException e) {
            //忽略不存在的异常
        }
        if (takeInfo == null) {
            return null;
        }
        return getOrderTableInfo(takeInfo.getOrderId());
    }

    @Override
    public List<OrderIdAndContractIdDTO> getContractIdByOrderIds(Collection<String> orderIds) {
        List<OrderInfo> list = orderInfoBiz.findByIds(orderIds);
        if (CollectionUtils.isNotEmpty(list)) {
            return list.stream().filter(item -> CsStringUtils.isNotBlank(item.getDealsId()))
                    .map(item -> new OrderIdAndContractIdDTO(item.getOrderId(), item.getOrderCode(), item.getDealsId()))
                    .toList();
        }
        return Lists.newArrayList();
    }

    @Override
    public OrderDTO queryOrderByCode(String orderCode) {
        return null;
    }

    @Override
    public List<OrderItemDTO> getOrderItems(String orderId) {
        if (CsStringUtils.isBlank(orderId)) {
            throw new BizException(BasicCode.PARAM_NULL, "订单Id");
        }
        List<OrderItem> items = orderItemBiz.findByOrderId(orderId);
        List<OrderItemDTO> orderItemList = BeanConvertUtils.convertList(items, OrderItemDTO.class);
        batchSetOrderItemAdd(orderItemList);
        return orderItemList;
    }

    @Override
    public List<OrderPayinfoDTO> getOrderPayInfoList(String orderId, PayTypeEnum payType, PayDetailTypeEnum payDetailType) {
        List<OrderPayinfoDTO> payinfoList = new ArrayList<>();
        Condition query = new Condition(OrderPayinfo.class);
        Example.Criteria criteria = query.createCriteria();
        criteria.andEqualTo(OBJECT_ID, orderId)
                .andNotEqualTo(DEL_FLG, CommonConstants.DEL_FLAG_TRUE);
        if (payType != null) {
            criteria.andEqualTo(PAYINFO_TYPE, payType.code());
        }
        query.setOrderByClause(CREATE_TIME1);
        List<OrderPayinfo> payList = orderPayinfoBiz.findByCondition(query);
        if (CollectionUtils.isNotEmpty(payList)) {
            payList.forEach(item -> {
                OrderPayinfoDTO payinfoDTO = BeanConvertUtils.convert(item, OrderPayinfoDTO.class);
                payinfoDTO.setPayDescriptionList(OrderPayInfoInstructionsDTO.json2List(item.getPayDescription()));
                payinfoList.add(payinfoDTO);
            });
            getOrderPayinfoDetailList(payinfoList, payDetailType);
        }
        return payinfoList;
    }

    @Override
    public List<OrderPayinfoDetailDTO> getOrderLogisticPayInfo(OrderDTO order, Boolean reversePush) {
        List<OrderPayinfoDetailDTO> detailList = Lists.newArrayList();
        List<OrderItemDTO> orderItems = order.getOrderItems();
        BigDecimal originLogAmount = BigDecimal.ZERO;//物流原总金额
        BigDecimal actualLogAmount = BigDecimal.ZERO;//物流实际总费用
        //物流款（非自提，非
        PickingBillTypeEnum deliverWay = PickingBillTypeEnum.valueOfCode(order.getDeliverWay());
        order.setSpecPay(true);
        if (PickingBillTypeEnum.BUYER_TAKE != deliverWay &&
                CsStringUtils.isNotBlank(order.getAddressId()) &&
                CollectionUtils.isNotEmpty(orderItems)) {
            //是否需要反向计算商品费用
            Boolean inverseFlag = Boolean.FALSE;
            for (OrderItemDTO itemDTO : orderItems) {
                //固定运费单价：出厂价-到位价
                BigDecimal fixedUnitCarriage = getFixedUnitCarriage(order, itemDTO);
                inverseFlag = getInverseFlag(order, itemDTO, inverseFlag);
                setCategoryType(itemDTO);
                ItemResult<SpecialGoodsAttributeDTO> goodsResult = getSpecialGoodsAttributeDTOItemResult(itemDTO);
                BigDecimal carriage = new BigDecimal(0);
                String carriagePayeeId = "";
                //物流费＝到位价-出厂价
                if (Objects.nonNull(goodsResult) && goodsResult.getData().getConcreteFlag() == 1) {
                    carriage = fixedUnitCarriage.multiply(itemDTO.getMeasureQuantity());
                    carriagePayeeId = order.getSellerId();
                } else {
                    //物流信息
                    CategoryQuantityDTO quantityDTO = new CategoryQuantityDTO();
                    //物流类型
                    quantityDTO.setTransportCategoryId(itemDTO.getLogistics());
                    //计量单位数量
                    quantityDTO.setProductQuantity(itemDTO.getMeasureQuantity());
                    //计算物流费
                    ItemResult<List<CarriageComputeResultDTO>> computeResult = getListItemResult(order, itemDTO, deliverWay, quantityDTO);
                    if (Objects.nonNull(computeResult) && CollectionUtils.isNotEmpty(computeResult.getData())) {
                        CarriageComputeResultDTO compute = computeResult.getData().get(0);
                        carriage = compute.getCarriage();
                        carriagePayeeId = compute.getCarrierId();
                    }
                }
                OrderPayinfoDetailDTO payDetail = new OrderPayinfoDetailDTO();
                payDetail.setDetailId(uuidGenerator.gain());
                payDetail.setPayeeId(carriagePayeeId);
                MemberSimpleDTO carrier = getMemberSimpleDTO(carriagePayeeId);
                payDetail.setPayeeName(carrier.getMemberName());
                payDetail.setPayerId(order.getBuyerId());
                payDetail.setPayerName(order.getBuyerName());
                payDetail.setPayAdmount(carriage);
                payDetail.setCurrency(order.getCurrency());
                payDetail.setCurrencySymbol(order.getCurrencySymbol());
                payDetail.setCurrencyName(order.getCurrencyName());
                payDetail.setPaytypeDetail(PayDetailTypeEnum.LOGISTICS.code());//支付单明细类型:订单物流款
                detailList.add(payDetail);
                itemDTO.setOriginLogisticPrice(carriage);
                itemDTO.setActualLogisticPrice(carriage);
                setOrderAndItem(order, reversePush, itemDTO, inverseFlag, carriage);

                originLogAmount = ArithUtils.add(originLogAmount, carriage);//物流原总金额
                actualLogAmount = ArithUtils.add(actualLogAmount, carriage);//物流实际总费用
            }
        }
        //保存物流费用
        order.setOriginLogisticAmount(originLogAmount);
        order.setActualLogisticAmount(actualLogAmount);

        return detailList;
    }


    private BigDecimal getFixedUnitCarriage(OrderDTO order, OrderItemDTO itemDTO) {
        BigDecimal fixedUnitCarriage = BigDecimal.ZERO;
        if (!CsStringUtils.isNullOrBlank(order.getDealsId())) {
            String contractGoodsId = itemDTO.getContractGoodsId() == null ?
                    itemDTO.getResourceId() : itemDTO.getContractGoodsId();
            TrContractGoodsDTO trContractGoodsDTO = contractService.getContractGoodsById(contractGoodsId);
            if (trContractGoodsDTO.getShipPrice() != null &&
                    trContractGoodsDTO.getShipPrice().compareTo(BigDecimal.ZERO) > 0) {
                fixedUnitCarriage = trContractGoodsDTO.getShipPrice()
                        .subtract(trContractGoodsDTO.getOutFactoryPrice());
            }
        } else {
            ResourceDTO resource = resourceService.getResourceDetail(itemDTO.getResourceId());
            if (resource.getArrivePrice() != null &&
                    resource.getArrivePrice().compareTo(BigDecimal.ZERO) > 0) {
                fixedUnitCarriage = resource.getArrivePrice()
                        .subtract(resource.getFactoryPrice());
            }
        }
        return fixedUnitCarriage;
    }

    private Boolean getInverseFlag(OrderDTO order, OrderItemDTO itemDTO, Boolean inverseFlag) {
        if (!CsStringUtils.isNullOrBlank(order.getDealsId())) {
            String contractGoodsId = itemDTO.getContractGoodsId() == null ?
                    itemDTO.getResourceId() : itemDTO.getContractGoodsId();
            TrContractGoodsDTO trContractGoodsDTO = contractService.getContractGoodsById(contractGoodsId);
            if (trContractGoodsDTO.getShipPrice() != null &&
                    trContractGoodsDTO.getShipPrice().compareTo(BigDecimal.ZERO) > 0) {
                inverseFlag = Boolean.TRUE;
            }
        } else {
            ResourceDTO resource = resourceService.getResourceDetail(itemDTO.getResourceId());
            if (resource.getArrivePrice() != null &&
                    resource.getArrivePrice().compareTo(BigDecimal.ZERO) > 0) {
                inverseFlag = Boolean.TRUE;
            }
        }
        return inverseFlag;
    }


    private MemberSimpleDTO getMemberSimpleDTO(String carriagePayeeId) {
        MemberSimpleDTO carrier = memberService.findMemberSimpleById(carriagePayeeId);
        if (carrier == null) {
            log.error("计算运费出错, 无法通过承运商id查找到承运商信息,memberId:{}", carriagePayeeId);
            throw new BizException(BasicCode.UNDEFINED_ERROR, "计算运费出错, 无法通过承运商id查找到承运商信息");
        }
        return carrier;
    }


    private ItemResult<List<CarriageComputeResultDTO>> getListItemResult(OrderDTO order, OrderItemDTO itemDTO, PickingBillTypeEnum deliverWay, CategoryQuantityDTO quantityDTO) {
        CarriageComputeDTO carriageDTO = new CarriageComputeDTO();
        carriageDTO.setPickingBillType(deliverWay.getCode());
        carriageDTO.setUserId(order.getSellerId());
        carriageDTO.setReceiveAddressId(order.getAddressId());
        carriageDTO.setReceiveAddressLocation(order.getAddressMap());
        carriageDTO.setWarehouseId(itemDTO.getStoreId());
        carriageDTO.setCategoryQuantityList(Lists.newArrayList(quantityDTO));
        carriageDTO.setProvinceCode(order.getProvinceCode());
        carriageDTO.setCityCode(order.getCityCode());
        carriageDTO.setDistrictCode(order.getDistrictCode());
        carriageDTO.setStreetCode(order.getStreetCode());
        ItemResult<List<CarriageComputeResultDTO>> computeResult;
        log.info("orderCarriageCompute_start:" + JSON.toJSONString(carriageDTO));
        computeResult = ruleComputeService.orderCarriageCompute(carriageDTO);//运费
        log.info("orderCarriageCompute_end:" + JSON.toJSONString(computeResult));
        if (Objects.nonNull(computeResult) && (!computeResult.isSuccess() || CollectionUtils.isEmpty(computeResult.getData()))) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "该地区无法配送该商品！");
        }
        return computeResult;
    }


    private ItemResult<SpecialGoodsAttributeDTO> getSpecialGoodsAttributeDTOItemResult(OrderItemDTO itemDTO) {
        ItemResult<SpecialGoodsAttributeDTO> goodsResult =
                goodsService.getSpecialGoodsAttribute(itemDTO.getGoodsId());
        if (Objects.nonNull(goodsResult) && !goodsResult.isSuccess()) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "查询商品特殊属性失败");
        }
        return goodsResult;
    }

    private void setCategoryType(OrderItemDTO itemDTO) {
        if (itemDTO.getCategoryType() == null || itemDTO.getCategoryCode() == null) {
            GoodsDTO goodsDTO = goodsService.getGoodsInfo(itemDTO.getGoodsId()).getData();
            itemDTO.setCategoryType(goodsDTO.getCategoryType());
            itemDTO.setCategoryCode(goodsDTO.getCategoryCode());
        }
    }

    private static void setOrderAndItem(OrderDTO order, Boolean reversePush, OrderItemDTO itemDTO, Boolean inverseFlag, BigDecimal carriage) {
        if (inverseFlag && reversePush) {
            BigDecimal logisticUnitPrice = carriage.divide(itemDTO.getMeasureQuantity(), 2, RoundingMode.HALF_UP);
            if (!itemDTO.getUnits().equals(itemDTO.getMeasureUnits())) {
                logisticUnitPrice = logisticUnitPrice.divide(itemDTO.getUnconvertRate(), 2, RoundingMode.HALF_UP);
            }
            //商品单价 = 到位价 - 物流单价
            itemDTO.setOriginUnitPrice(itemDTO.getOriginUnitPrice().subtract(logisticUnitPrice));
            if (itemDTO.getActualUnitPrice() != null) {
                itemDTO.setActualUnitPrice(itemDTO.getActualUnitPrice().subtract(logisticUnitPrice));
            }
            if (itemDTO.getActualResDiscPrice() != null) {
                itemDTO.setActualResUnitPrice(itemDTO.getActualResUnitPrice().subtract(logisticUnitPrice));
            }
            itemDTO.setOriginAmountPrice(itemDTO.getOriginAmountPrice().subtract(carriage));
            itemDTO.setActualAmountPrice(itemDTO.getActualAmountPrice().subtract(carriage));
            //扣除总金额
            order.setOriginResourceAmount(order.getOriginResourceAmount().subtract(carriage));
            order.setActualResourceAmount(order.getActualResourceAmount().subtract(carriage));
        }
    }

    @Override
    public BuyerPromotionalDetailsDTO getResourcePromotional(OrderDTO orderDTO, OrderItemDTO item) {
        String sellerId = orderDTO.getSellerId();
        String sellerName = orderDTO.getSellerName();
        String buyerId = orderDTO.getBuyerId();
        String buyerName = orderDTO.getBuyerName();
        log.info("getResourcePromotional:sellerId-{},sellerName-{},buyerId-{},buyerName-{},item-{}",
                sellerId, sellerName, buyerId, buyerName, item);
        BuyerPromotionalDetailsDTO result = null;
        if (CsStringUtils.isNotBlank(item.getResourceId())) {
            BuyerPromotionalDetailsDTO qPromot = new BuyerPromotionalDetailsDTO();
            qPromot.setBuyerId(buyerId);
            qPromot.setBuyerName(buyerName);
            qPromot.setSellerId(sellerId);
            qPromot.setSellerName(sellerName);
            qPromot.setGoodsId(item.getGoodsId());
            qPromot.setGoodsName(item.getGoodsName());
            qPromot.setResourceId(item.getResourceId());
            qPromot.setResourceName(item.getResourceName());
            qPromot.setResourceAmount(item.getItemQuantity());
            qPromot.setResourceOriginalPrice(item.getOriginUnitPrice());//资源原单价(不包含加价项)
            qPromot.setUnit(item.getUnits());
            qPromot.setReferrerRuleId(item.getDiscountId());//优惠码id
            qPromot.setStoreId(orderDTO.getStoreId());
            qPromot.setSaleAreaCode(orderDTO.getSaleRegionPath());

            PromotionalDTO queryPromot = new PromotionalDTO();
            queryPromot.setList(Lists.newArrayList(qPromot));
            log.info("countPromotionDetail_start:" + JSON.toJSONString(queryPromot));
        }

        log.info("getResourcePromotional_result:" + JSON.toJSONString(result));
        return result;
    }

    @Override
    public OrderDTO getOrderInfo(String orderId) {
        return getOrderInfo(orderId, true);
    }

    @Override
    public OrderDTO getOrderInfo(String orderId, boolean showDetail) {
        if (CsStringUtils.isBlank(orderId)) {
            throw new BizException(BasicCode.PARAM_NULL, "订单Id");
        }
        OrderInfo orderInfo = orderInfoBiz.getOrderInfoById(orderId);
        if (orderInfo != null) {
            OrderDTO order = BeanConvertUtils.convert(orderInfo, OrderDTO.class);
            //订单行项目
            OrderItem queryItem = new OrderItem();
            queryItem.setOrderId(order.getOrderId());
            queryItem.setDelFlg(false);
            List<OrderItem> items = orderItemBiz.find(queryItem);
            if (CollectionUtils.isNotEmpty(items)) {
                List<OrderItemDTO> orderItems = Lists.newArrayList();
                for (OrderItem item : items) {
                    GoodsDTO goodsDTO = null;
                    if (CsStringUtils.isNotBlank(item.getGoodsId())) {
                        goodsDTO = goodsService.getGoodsInfo(item.getGoodsId()).getData();
                    }
                    orderItems.add(convertItemDTO(item, goodsDTO));
                }
                //设置加价项
                batchSetOrderItemAdd(orderItems);
                order.setOrderItems(orderItems);
            }

            if (showDetail) {
                //设置业务员信息
                order = setOrderSalesman(order);
                //设置收货员信息
                order = setReceiver(order);
            }

            //设置订单支付方式
            setOrderPayWay(order);
            //设置订单其它费用(货款、物流款以外的费用)
            setOrderOthersAmount(order);
            batchSetOrderProxyMapInfo(Lists.newArrayList(order));
            //设置订单扩展信息
            order.setOrderInfoExtDTO(orderInfoExtBiz.findByOrderId(order.getOrderId()));
            return order;
        }
        return null;
    }

    @Override
    public OrderPayinfo getOrderPay(String orderId) {
        OrderPayinfo payinfo = null;
        Condition query = new Condition(OrderPayinfo.class);
        query.createCriteria().andEqualTo(OBJECT_ID, orderId)
                .andIn(PAYINFO_TYPE, Lists.newArrayList(PayTypeEnum.SINGLE.code(), PayTypeEnum.GROUP.code()))//订单款
                .andNotEqualTo(DEL_FLG, CommonConstants.DEL_FLAG_TRUE);
        query.setOrderByClause(CREATE_TIME1);
        List<OrderPayinfo> payList = orderPayinfoBiz.findByCondition(query);
        if (CollectionUtils.isNotEmpty(payList)) {
            payinfo = payList.get(0);
        }
        return payinfo;
    }

    @Override
    public List<OrderPayinfo> queryOrderPayInfo(OrderPayinfoQueryDTO payInfoQueryDTO) {
        if (CsStringUtils.isEmpty(payInfoQueryDTO.getOrderId())) {
            throw new BizException(BasicCode.INVALID_PARAM, "订单ID");
        }
        Condition condition = new Condition(OrderPayinfo.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(OBJECT_ID, payInfoQueryDTO.getOrderId());
        if (payInfoQueryDTO.getPayType() != null) {
            criteria.andEqualTo(PAYINFO_TYPE, payInfoQueryDTO.getPayType().code());
        }
        if (CollectionUtils.isNotEmpty(payInfoQueryDTO.getPayStatusList())) {
            criteria.andIn("payinfoStatus", payInfoQueryDTO.getPayStatusList());
        }
        if (CsStringUtils.isNotEmpty(payInfoQueryDTO.getPayWay())) {
            criteria.andEqualTo("payinfoWay", payInfoQueryDTO.getPayWay());
        }
        criteria.andEqualTo(DEL_FLG, CommonConstants.DEL_FLAG_FALSE);
        condition.setOrderByClause(CREATE_TIME1);
        List<OrderPayinfo> payList = orderPayinfoBiz.findByCondition(condition);
        if (CollectionUtils.isEmpty(payList)) {
            payList = Lists.newArrayList();
        }
        return payList;
    }

    @Override
    public OrderDTO getOrderByTradeBillId(String tradeBillId) {
        if (CsStringUtils.isBlank(tradeBillId)) {
            throw new BizException(BasicCode.PARAM_NULL, "tradeBillId");
        }
        //支付单
        OrderPayinfo payinfo = orderPayinfoBiz.findPayInfoByTradeBillId(tradeBillId);
        return this.getOrderDetail(payinfo.getObjectId());
    }

    @Override
    public String getBuyerTradeBillIdByOrderId(String orderId, String buyerId) {
        if (CsStringUtils.isBlank(orderId)) {
            throw new BizException(BasicCode.PARAM_NULL, "订单Id");
        }
        if (CsStringUtils.isBlank(buyerId)) {
            throw new BizException(BasicCode.PARAM_NULL, BUYER_ID);
        }

        OrderInfo orderInfo = orderInfoBiz.getOrderInfoById(orderId);

        if (orderInfo == null) {
            throw new BizException(BasicCode.DATA_NOT_EXIST, "订单");
        }

        if (!buyerId.equals(orderInfo.getBuyerId())) {
            throw new BizException(BasicCode.INVALID_PARAM, "你无权操作该订单");
        }

        OrderPayinfo queryPayinfo = new OrderPayinfo();
        queryPayinfo.setObjectId(orderId);
        queryPayinfo.setDelFlg(false);
        queryPayinfo.setPayinfoType(PayTypeEnum.SINGLE.getCode());
        List<OrderPayinfo> payList = orderPayinfoBiz.find(queryPayinfo);
        if (CollectionUtils.isEmpty(payList) || payList.size() > 1) {
            throw new BizException(BasicCode.DATA_STATE_NOT_NORMAL, payList);
        }

        return payList.get(0).getTradeBillId();
    }

    @Override
    public BigDecimal getBuyerResDayQuantity(String buyerId, String resourceId) {
        Date now = new Date();
        Calendar cal = Calendar.getInstance();
        cal.setTime(now);
        cal.add(Calendar.DATE, 1);
        Date lastDate = cal.getTime();

        String date1 = DateUtil.convertDateToString(now);
        String date2 = DateUtil.convertDateToString(lastDate);

        String notStatus = String.join(",", OrderStatusEnum.CANCEL.code(), OrderStatusEnum.CLOSED.code(), OrderStatusEnum.EXPIRED.code());
        String buyerCon = "order_id IN (SELECT o.order_id FROM tr_order_info o " +
                "WHERE o.del_flg = 0 and o.buyer_id = '" + buyerId + "' and o.order_status NOT IN ('" + notStatus + "'))";

        Condition condition = new Condition(OrderItem.class);
        condition.createCriteria()
                .andCondition(buyerCon)
                .andEqualTo("resourceId", resourceId)
                .andLessThan(CREATE_TIME, date2)
                .andGreaterThanOrEqualTo(CREATE_TIME, date1)
                .andEqualTo(DEL_FLG, 0);

        BigDecimal ymQuantity = BigDecimal.ZERO;
        List<OrderItem> ymRes = orderItemBiz.findByCondition(condition);
        if (CollectionUtils.isNotEmpty(ymRes)) {
            for (OrderItem oItem : ymRes) {
                BigDecimal convertRate = BigDecimal.ONE;
                ResourceDTO resource = resourceService.getResourceDetail(resourceId);
                if (!resource.getPriceUnit().equals(oItem.getUnits())) {
                    convertRate = resource.getConvertRate();
                    if (convertRate == null) {
                        convertRate = BigDecimal.ONE;
                    }
                }
                ymQuantity = ArithUtils.add(ymQuantity, oItem.getItemQuantity().multiply(convertRate).setScale(2, RoundingMode.HALF_UP));
            }
        }

        return ymQuantity;
    }

    @Override
    public TransactionsOverviewDTO transactionsOverview(OverviewReqDTO overviewReqDTO) {
        //参数检查
        checkParams(overviewReqDTO);
        //初始化结果
        TransactionsOverviewDTO transactionsOverviewDTO = new TransactionsOverviewDTO();
        BigDecimal totalAmount = BigDecimal.ZERO;
        Integer totalOrderCount = 0;
        Integer totalCustomerCount = 0;
        List<String> customerIds = Lists.newArrayList();
        List<String> orderIds = Lists.newArrayList();
        //查询订单子项
        List<ProportionDTO> proportionList = Lists.newArrayList();
        List<CategoryTreeDTO> categoryTreeDTOS = goodsCategoryService.goodsCategoryTree("000");
        if (CollectionUtils.isNotEmpty(categoryTreeDTOS)) {
            for (CategoryTreeDTO categoryTreeDTO : categoryTreeDTOS) {
                ProportionDTO proportionDTO = new ProportionDTO();
                proportionDTO.setKey(categoryTreeDTO.getCategoryCode());
                proportionDTO.setName(categoryTreeDTO.getCategoryName());
                proportionDTO.setValue(BigDecimal.ZERO);
                proportionList.add(proportionDTO);
            }
        }
        //查询订单信息
        Condition orderInfoCondition = new Condition(OrderInfo.class);
        Example.Criteria orderInfoConditionCriteria = orderInfoCondition.createCriteria();
        orderInfoConditionCriteria.andGreaterThanOrEqualTo(UPDATE_TIME, overviewReqDTO.getStartDate());
        orderInfoConditionCriteria.andLessThanOrEqualTo(UPDATE_TIME, overviewReqDTO.getEndDate());
        orderInfoConditionCriteria.andEqualTo(DEL_FLG, false);
        orderInfoConditionCriteria.andEqualTo(SELLER_ID, overviewReqDTO.getSellerId());
        orderInfoConditionCriteria.andEqualTo(ORDER_STATUS, OrderStatusEnum.COMPLETED.code());
        List<OrderInfo> orderInfos = orderInfoBiz.findByCondition(orderInfoCondition);
        if (CollectionUtils.isNotEmpty(orderInfos)) {
            totalOrderCount = orderInfos.size();
            for (OrderInfo orderInfo : orderInfos) {
                orderIds.add(orderInfo.getOrderId());
                totalAmount = totalAmount.add(orderInfo.getActualOrderAmount());
                if (!customerIds.contains(orderInfo.getBuyerId())) {
                    customerIds.add(orderInfo.getBuyerId());
                }
            }
            totalCustomerCount = customerIds.size();
            Condition orderItemCondition = new Condition(OrderItem.class);
            Example.Criteria orderItemConditionCriteria = orderItemCondition.createCriteria();
            orderItemConditionCriteria.andEqualTo(DEL_FLG, false);
            orderItemConditionCriteria.andIn(ORDER_ID, orderIds);
            List<OrderItem> orderItems = orderItemBiz.findByCondition(orderItemCondition);
            proportionDTOSetValue(orderItems, proportionList);
        }
        transactionsOverviewDTO.setProportionList(proportionList);
        transactionsOverviewDTO.setTotalAmount(totalAmount.setScale(2, BigDecimal.ROUND_HALF_DOWN));
        transactionsOverviewDTO.setTotalOrderCount(totalOrderCount);
        transactionsOverviewDTO.setTotalCustomerCount(totalCustomerCount);
        return transactionsOverviewDTO;
    }

    private static void proportionDTOSetValue(List<OrderItem> orderItems, List<ProportionDTO> proportionList) {
        if (CollectionUtils.isNotEmpty(orderItems)) {
            for (ProportionDTO proportionDTO : proportionList) {
                for (OrderItem orderItem : orderItems) {
                    if (CsStringUtils.isNotBlank(orderItem.getCategoryCode())
                            && proportionDTO.getKey().equals(orderItem.getCategoryCode().substring(0, 9))) {
                        proportionDTO.setValue(ArithUtils.add(proportionDTO.getValue(), orderItem.getActualAmountPrice()));
                    }
                }
            }
        }
    }

    private static void checkParams(OverviewReqDTO overviewReqDTO) {
        if (CsStringUtils.isNullOrBlank(overviewReqDTO.getSellerId())) {
            throw new BizException(BasicCode.PARAM_NULL, "卖家ID");
        }
        if (overviewReqDTO.getStartDate() == null) {
            throw new BizException(BasicCode.PARAM_NULL, "查询开始时间");
        }
        if (overviewReqDTO.getEndDate() == null) {
            throw new BizException(BasicCode.PARAM_NULL, "查询结束时间");
        }
    }

    @Override
    public OrderOverviewDTO orderOverview(OverviewReqDTO overviewReqDTO) {
        //参数检查
        if (CsStringUtils.isNullOrBlank(overviewReqDTO.getSellerId())) {
            throw new BizException(BasicCode.PARAM_NULL, "卖家ID");
        }
        if (overviewReqDTO.getStartDate() == null) {
            throw new BizException(BasicCode.PARAM_NULL, "查询开始时间");
        }
        if (overviewReqDTO.getEndDate() == null) {
            throw new BizException(BasicCode.PARAM_NULL, "查询结束时间");
        }
        //初始化结果
        OrderOverviewDTO orderOverviewDTO = new OrderOverviewDTO();
        List<ProportionDTO> proportionList = Lists.newArrayList();
        List<OrderStatusEnum> orderStatusEnums = Arrays.asList(OrderStatusEnum.values());
        if (CollectionUtils.isNotEmpty(orderStatusEnums)) {
            orderStatusEnums.forEach(orderStatusEnum -> {
                ProportionDTO proportionDTO = new ProportionDTO();
                proportionDTO.setKey(orderStatusEnum.code());
                proportionDTO.setName(orderStatusEnum.message());
                proportionDTO.setValue(BigDecimal.ZERO);
                proportionList.add(proportionDTO);
            });
        }

        //查询订单信息
        Condition orderInfoCondition = new Condition(OrderInfo.class);
        Example.Criteria orderInfoConditionCriteria = orderInfoCondition.createCriteria();
        orderInfoConditionCriteria.andGreaterThanOrEqualTo(UPDATE_TIME, overviewReqDTO.getStartDate());
        orderInfoConditionCriteria.andLessThanOrEqualTo(UPDATE_TIME, overviewReqDTO.getEndDate());
        orderInfoConditionCriteria.andEqualTo(DEL_FLG, false);
        orderInfoConditionCriteria.andEqualTo(SELLER_ID, overviewReqDTO.getSellerId());
        List<OrderInfo> orderInfos = orderInfoBiz.findByCondition(orderInfoCondition);
        for (ProportionDTO proportion : proportionList) {
            if (CollectionUtils.isNotEmpty(orderInfos)) {
                for (OrderInfo orderInfo : orderInfos) {
                    if (orderInfo.getOrderStatus().equals(proportion.getKey())) {
                        proportion.setValue(proportion.getValue().add(orderInfo.getActualOrderAmount()));
                    }
                }
            }
        }
        BigDecimal totalAmount = BigDecimal.ZERO;
        for (ProportionDTO proportion : proportionList) {
            totalAmount = totalAmount.add(proportion.getValue());
        }
        orderOverviewDTO.setTotalAmount(totalAmount);
        orderOverviewDTO.setProportionList(proportionList);

        return orderOverviewDTO;
    }

    @Override
    public OrderDTO getOrderDetailByCondition(OrderDetailConditionDTO orderDetailConditionDTO) {
        if (CsStringUtils.isBlank(orderDetailConditionDTO.getOrderCode())) {
            throw new BizException(BasicCode.PARAM_NULL, "订单编号");
        }
        Condition selectCondition = new Condition(OrderInfo.class);
        Example.Criteria selectCriteria = selectCondition.createCriteria();
        selectCriteria.andEqualTo("orderCode", orderDetailConditionDTO.getOrderCode());
        selectCriteria.andEqualTo(DEL_FLG, false);
        List<OrderInfo> orderInfos = orderInfoBiz.findByCondition(selectCondition);
        if (CollectionUtils.isNotEmpty(orderInfos)) {
            OrderInfo orderInfo = orderInfos.get(0);
            if (orderInfo != null) {
                List<String> objectIds = Lists.newArrayList();//订单优惠明细objectId
                objectIds.add(orderInfo.getOrderId());

                OrderDTO order = BeanConvertUtils.convert(orderInfo, OrderDTO.class);
                setCarryFloorStr(order);
                //设置订单扩展信息
                order.setOrderInfoExtDTO(orderInfoExtBiz.findByOrderId(order.getOrderId()));
                //订单行项目
                OrderItem queryItem = new OrderItem();
                queryItem.setOrderId(order.getOrderId());
                queryItem.setDelFlg(false);
                List<OrderItem> items = orderItemBiz.find(queryItem);
                setOrderItems(items, objectIds, order);

                //订单优惠明细
                order.setDiscountDetail(getOrderDiscountDetails(objectIds));

                //锁价前价格变化历史
                setOrderResoureChange(order);

                //支付单
                setOrderPayinfo(order);

                //退款请求单
                order = setOrderRefund(order);

                //开票信息
                setOrderTaxinfo(order);
                //设置业务员信息
                order = setOrderSalesman(order);
                //设置收货员信息
                order = setReceiver(order);
                //设置订单其它费用(货款、物流款以外的费用)
                setOrderOthersAmount(order);
                // 计算结算金额
                computeSettlementAmount(order);
                batchSetOrderProxyMapInfo(Lists.newArrayList(order));
                return order;
            }
        }
        return null;
    }

    private void setOrderItems(List<OrderItem> items, List<String> objectIds, OrderDTO order) {
        if (CollectionUtils.isNotEmpty(items)) {
            List<OrderItemDTO> orderItems = Lists.newArrayList();
            for (OrderItem item : items) {
                GoodsDTO goodsDTO = null;
                if (CsStringUtils.isNotBlank(item.getGoodsId())) {
                    goodsDTO = goodsService.getGoodsInfo(item.getGoodsId()).getData();
                }
                OrderItemDTO orderItemDTO = convertItemDTO(item, goodsDTO);
                orderItemDTO.setSupportAddItem(goodsService.ifComputePorterage(item.getGoodsId()).getData());
                orderItems.add(orderItemDTO);
                objectIds.add(item.getOrderItemId());
                //新增搬运费规则
                if (!order.isHaveCartageRule()) {
                    if (goodsDTO != null) {
                        order.setHaveCartageRule(!CsStringUtils.isBlank(goodsDTO.getCartageRule()));
                    }
                }
            }
            //设置加价项
            batchSetOrderItemAdd(orderItems);
            order.setOrderItems(orderItems);
        }
    }

    private static void setCarryFloorStr(OrderDTO order) {
        if (CsStringUtils.isNotBlank(order.getCarryFloor())) {
            if (FloorNumberEnum.getByCode(order.getCarryFloor()) != null) {
                order.setCarryFloorStr(FloorNumberEnum.getByCode(order.getCarryFloor()).message());
            } else {
                order.setCarryFloorStr(order.getCarryFloor());
            }
        }
    }

    @Override
    public List<OrderStatusStatisticsDTO> orderStatusStatistics(List<String> saleRegionList, String sellerId) {
        if (CsStringUtils.isNullOrBlank(sellerId)) {
            throw new BizException(BasicCode.PARAM_NULL, "卖家ID");
        }
        //初始化结果
        List<OrderStatusStatisticsDTO> list = Lists.newArrayList();
        Map<String, Integer> statusCountMap = Maps.newHashMap();
        List<OrderStatusEnum> orderStatusEnums = Arrays.asList(OrderStatusEnum.values());

        orderStatusEnums.forEach(orderStatusEnum -> {
            OrderQueryDTO orderQueryDTO = new OrderQueryDTO();
            orderQueryDTO.setSellerId(sellerId);
            orderQueryDTO.setOrderStatus(orderStatusEnum.code());
            orderQueryDTO.setRegionList(saleRegionList);
            int count = orderInfoBiz.countQueryWithDataPerm(orderQueryDTO);
            statusCountMap.put(orderStatusEnum.code(), Integer.valueOf(count));
        });
        for (Map.Entry<String, Integer> entry : statusCountMap.entrySet()) {
            String key = entry.getKey();
            OrderStatusStatisticsDTO orderStatusStatisticsDTO = new OrderStatusStatisticsDTO();
            orderStatusStatisticsDTO.setOrderStatusCode(key);
            orderStatusStatisticsDTO.setOrderStatusName(OrderStatusEnum.getByCode(key).message());
            orderStatusStatisticsDTO.setOrderStatusCount(entry.getValue());
            list.add(orderStatusStatisticsDTO);
        }
        return list;
    }

    @Override
    public List<OrderDTO> getOrderByTakeId(List<String> takeIds) {
        List<OrderDTO> orderDTOS = new ArrayList<>();
        if (CollectionUtils.isEmpty(takeIds)) {
            throw new BizException(BasicCode.PARAM_NULL, "发货单Ids");
        }
        List<String> newTakeIds = takeIds.stream().distinct().toList();
        Condition condition = new Condition(TakeInfo.class);
        condition.createCriteria()
                .andIn(TAKE_ID, newTakeIds);
        List<TakeInfo> takeInfos = takeInfoBiz.findByCondition(condition);
        //通过发货单id查询发货单，如果查询的出发货单数量少于发货单id数量则抛异常
        if (CollectionUtils.isEmpty(takeInfos) || newTakeIds.size() > takeInfos.size()) {
            throw new BizException(BasicCode.DB_DATA_NOT_FOUND, "发货单不存在");
        }
        Map<String, TakeInfoDTO> TakeInfoId2TakeInfoMap = takeInfos.stream()
                .map(item -> {
                    TakeInfoDTO takeInfoDTO = new TakeInfoDTO();
                    BeanUtils.copyProperties(item, takeInfoDTO);
                    return takeInfoDTO;
                })
                .collect(Collectors.toMap(TakeInfoDTO::getTakeId, Function.identity()));
        takeIds.forEach(takeId -> {
            OrderDTO orderDTO = this.getOrderInfo(TakeInfoId2TakeInfoMap.get(takeId).getOrderId());
            orderDTO.setStoreId(TakeInfoId2TakeInfoMap.get(takeId).getStoreId());
            orderDTOS.add(orderDTO);
        });
        return orderDTOS;
    }

    @Override
    public List<OrderDTO> getOrderByTakeCodes(List<String> takeCodes) {
        List<OrderDTO> orderDTOS = new ArrayList<>();
        if (CollectionUtils.isEmpty(takeCodes)) {
            throw new BizException(BasicCode.PARAM_NULL, "发货单Codes");
        }
        List<String> newTakeCodes = takeCodes.stream().distinct().toList();
        Condition condition = new Condition(TakeInfo.class);
        condition.createCriteria()
                .andIn(TAKE_CODE, newTakeCodes);
        List<TakeInfo> takeInfos = takeInfoBiz.findByCondition(condition);
        //通过发货单id查询发货单，如果查询的出发货单数量少于发货单id数量则抛异常
        if (CollectionUtils.isEmpty(takeInfos) || newTakeCodes.size() > takeInfos.size()) {
            throw new BizException(BasicCode.DB_DATA_NOT_FOUND, "发货单不存在");
        }
        Map<String, TakeInfoDTO> TakeInfoCode2TakeInfoMap = takeInfos.stream()
                .map(item -> {
                    TakeInfoDTO takeInfoDTO = new TakeInfoDTO();
                    BeanUtils.copyProperties(item, takeInfoDTO);
                    return takeInfoDTO;
                })
                .collect(Collectors.toMap(TakeInfoDTO::getTakeCode, Function.identity()));
        takeCodes.forEach(takeCode -> {
            OrderDTO orderDTO = this.getOrderInfo(TakeInfoCode2TakeInfoMap.get(takeCode).getOrderId());
            orderDTO.setTakeInfos(Lists.newArrayList(TakeInfoCode2TakeInfoMap.get(takeCode)));
            orderDTOS.add(orderDTO);
        });
        return orderDTOS;
    }

    @Override
    public List<OrderStatusStatisticDTO> statisticOrderStatus(OrderQueryDTO orderQuery) {
        if (CollectionUtils.isEmpty(orderQuery.getRegionList())) {
            orderQuery.setRegionList(null);
        }
        return orderInfoBiz.countQuery(orderQuery);
    }

    /**
     * 批量设置orderDTO中的orderItems
     *
     * @param orderDTOList
     */
    private void batchSetOrderItems(List<OrderDTO> orderDTOList) {
        if (CollectionUtils.isEmpty(orderDTOList)) {
            return;
        }
        List<String> orderIds = orderDTOList.stream().map(OrderDTO::getOrderId).toList();
        Condition orderItemExa = new Condition(OrderItem.class);
        orderItemExa.createCriteria()
                .andIn(ORDER_ID, orderIds)
                .andEqualTo(DEL_FLG, false);
        List<OrderItem> orderItems = orderItemBiz.findByCondition(orderItemExa);
        if (CollectionUtils.isEmpty(orderItems)) {
            return;
        }

        List<String> goodsIds = Lists.newArrayList();
        List<String> resourceIds = Lists.newArrayList();

        Map<String, GoodsDTO> goodsId2DTOMap = Maps.newHashMap();
        Map<String, ResourceDTO> resourceId2DTOMap = Maps.newHashMap();

        addGoodsIdsAndResourceIds(orderItems, goodsIds, resourceIds);

        ItemResult<List<GoodsDTO>> goodsListResult = goodsService.selectSimpleGoodsInfoByIds(goodsIds);
        getGoodsId2DTOMap(goodsListResult, goodsId2DTOMap);

        QueryResourceConditionDTO conditionDTO = new QueryResourceConditionDTO();
        conditionDTO.setResourceIdList(resourceIds);
        List<ResourceDTO> resourceDTOS = resourceService.queryResourceListByCondition(conditionDTO);

        if (CollectionUtils.isNotEmpty(resourceDTOS)) {
            for (ResourceDTO resourceDTO : resourceDTOS) {
                resourceId2DTOMap.put(resourceDTO.getResourceId(), resourceDTO);
            }
        }

        Map<String, List<OrderItemDTO>> orderId2ItemsListMap = orderItems.stream()
                .filter(item -> CsStringUtils.isNotBlank(item.getOrderId()))
                .map(item -> {
                    OrderItemDTO itemDTO = BeanConvertUtils.convert(item, OrderItemDTO.class);
                    ResourceDTO resourceDTO = resourceId2DTOMap.get(item.getResourceId());
                    if (resourceDTO != null) {
                        itemDTO.setOrderMinChangeNum(resourceDTO.getOrderminchangeNum());
                    }
                    GoodsDTO goods = goodsId2DTOMap.get(item.getGoodsId());
                    setItemDTO(goods, itemDTO);
                    return itemDTO;
                })
                .collect(Collectors.groupingBy(OrderItemDTO::getOrderId));
        for (OrderDTO orderDTO : orderDTOList) {
            List<OrderItemDTO> orderItemDTOS = orderId2ItemsListMap.get(orderDTO.getOrderId());

            BigDecimal itemRefundAmount = BigDecimal.ZERO;
            for (OrderItemDTO itemDTO : orderItemDTOS) {
                if (itemDTO.getItemRefundAmount() == null) {
                    continue;
                }

                itemRefundAmount = itemRefundAmount.add(itemDTO.getItemRefundAmount());
            }

            orderDTO.setItemRefundAmount(itemRefundAmount);
            orderDTO.setOrderItems(orderItemDTOS);
        }

    }

    private static void getGoodsId2DTOMap(ItemResult<List<GoodsDTO>> goodsListResult, Map<String, GoodsDTO> goodsId2DTOMap) {
        if (goodsListResult != null && CollectionUtils.isNotEmpty(goodsListResult.getData())) {
            for (GoodsDTO goodsDTO : goodsListResult.getData()) {
                goodsId2DTOMap.put(goodsDTO.getGoodsId(), goodsDTO);
            }
        }
    }

    private static void setItemDTO(GoodsDTO goods, OrderItemDTO itemDTO) {
        if (goods != null) {
            itemDTO.setBrand(goods.getBrand());
            itemDTO.setPack(goods.getPack());
            itemDTO.setSpecs(goods.getSpecs());
            itemDTO.setGoodsUnit(goods.getUnit());
            itemDTO.setMark(goods.getMark());
            itemDTO.setCategoryType(goods.getCategoryType());
            String[] imgs = goods.getImgs();
            if (imgs != null && imgs.length > 0) {
                itemDTO.setGoodsPic(goods.getImgs()[0]);//商品图片
            }
        }
    }

    private static void addGoodsIdsAndResourceIds(List<OrderItem> orderItems, List<String> goodsIds, List<String> resourceIds) {
        for (OrderItem item : orderItems) {
            if (CsStringUtils.isNotBlank(item.getGoodsId())) {
                goodsIds.add(item.getGoodsId());
            }
            if (CsStringUtils.isNotBlank(item.getResourceId())) {
                resourceIds.add(item.getResourceId());
            }
        }
    }

    /**
     * orderErpAddressBiz
     * 批量设置ERP流向管控地址
     *
     * @param orderDTOList
     */
    private void batchSetERPAddress(List<OrderDTO> orderDTOList) {
        if (CollectionUtils.isNotEmpty(orderDTOList)) {
            List<String> orderIds = orderDTOList.stream().map(OrderDTO::getOrderId).toList();
            // 获取ERP流向管控地址
            List<ERPAddressDTO> erpAddressList = orderErpAddressBiz.queryERPAddressListByOrderIds(orderIds);
            if (CollectionUtils.isNotEmpty(erpAddressList)) {
                Map<String, List<ERPAddressDTO>> orderId2ERPAddressMap =
                        erpAddressList.stream().collect(Collectors.groupingBy(ERPAddressDTO::getOrderId));
                for (OrderDTO orderDTO : orderDTOList) {
                    List<ERPAddressDTO> list = orderId2ERPAddressMap.get(orderDTO.getOrderId());
                    if (CollectionUtils.isNotEmpty(list) && CsStringUtils.isNotBlank(list.get(0).getErpUnloadAddressId())) {
                        orderDTO.setErpAddressDTO(list.get(0));
                    }
                }
            }
        }
    }

    /**
     * orderProxyMapBiz
     * 更新一级或者二级订单号
     *
     * @param orderDTOList
     */
    private void batchSetOrderProxyMapInfo(List<OrderDTO> orderDTOList) {
        if (CollectionUtils.isEmpty(orderDTOList)) {
            return;
        }
        List<String> orderIds = orderDTOList.stream().map(OrderDTO::getOrderId).toList();
        List<OrderProxyMap> primaryOrderIds = orderProxyMapBiz.findByPrimaryOrderIds(orderIds);
        List<OrderProxyMap> secondaryOrderIds = orderProxyMapBiz.findBySecondaryOrderIds(orderIds);
        //一、二级订单关系是一对一的
        Map<String, OrderProxyMap> primaryOrderIdsMap = primaryOrderIds.stream().collect(Collectors.toMap(OrderProxyMap::getPrimaryOrderId, Function.identity(), (k1, k2) -> k2));
        Map<String, OrderProxyMap> secondaryOrderIdsMap = secondaryOrderIds.stream().collect(Collectors.toMap(OrderProxyMap::getSecondaryOrderId, Function.identity(), (k1, k2) -> k2));

        if (CollectionUtils.isNotEmpty(primaryOrderIds) || CollectionUtils.isNotEmpty(secondaryOrderIds)) {
            for (OrderDTO item : orderDTOList) {
                if (secondaryOrderIdsMap != null && !secondaryOrderIdsMap.isEmpty() && secondaryOrderIdsMap.containsKey(item.getOrderId())) {
                    OrderProxyMap orderProxyMap = secondaryOrderIdsMap.get(item.getOrderId());
                    item.setSecondaryOrderIdAndCode(new OrderIdAndCodeDTO(orderProxyMap.getSecondaryOrderId(), orderProxyMap.getPrimaryOrderCode(), orderProxyMap.getPrimaryDeliverWay()));
                    item.setSecondaryOrder(true);
                    item.setPrimaryOrderIdAndCode(new OrderIdAndCodeDTO(orderProxyMap.getPrimaryOrderId(), orderProxyMap.getPrimaryOrderCode(), orderProxyMap.getPrimaryDeliverWay()));
                    item.setPrimaryOrder(false);
                    log.info("order:{} setPrimaryOrder true", item.getOrderCode());
                } else if (primaryOrderIdsMap != null && !primaryOrderIdsMap.isEmpty() && primaryOrderIdsMap.containsKey(item.getOrderId())) {
                    OrderProxyMap orderProxyMap = primaryOrderIdsMap.get(item.getOrderId());
                    item.setSecondaryOrderIdAndCode(new OrderIdAndCodeDTO(orderProxyMap.getSecondaryOrderId(), orderProxyMap.getPrimaryOrderCode(), orderProxyMap.getPrimaryDeliverWay()));
                    item.setSecondaryOrder(false);
                    item.setPrimaryOrderIdAndCode(new OrderIdAndCodeDTO(orderProxyMap.getPrimaryOrderId(), orderProxyMap.getPrimaryOrderCode(), orderProxyMap.getPrimaryDeliverWay()));
                    item.setPrimaryOrder(true);
                    log.info("order:{} setSecondaryOrder true", item.getOrderCode());
                } else {
                    log.info("order:{} setSecondaryOrder null", item.getOrderCode());
                }
            }
        }
    }

    /**
     * 批量设置支付单信息
     *
     * @param orderDTOList
     */
    private void batchSetOrderPayInfo(List<OrderDTO> orderDTOList) {
        List<OrderPayinfoDTO> dbPayInfoDTOList = getOrderPayinfoDTOS(orderDTOList);
        if (dbPayInfoDTOList == null) return;
        Map<String, List<OrderPayinfoDTO>> orderId2PayInfoMap = dbPayInfoDTOList.stream().collect(Collectors.groupingBy(OrderPayinfoDTO::getObjectId));
        for (OrderDTO orderDTO : orderDTOList) {
            //每一条订单的设置
            String orderId = orderDTO.getOrderId();
            List<OrderPayinfoDTO> payInfos = orderId2PayInfoMap.get(orderId);
            if (CollectionUtils.isEmpty(payInfos)) {
                continue;
            }
            BigDecimal orderUnPayAmount = BigDecimal.ZERO;
            orderDTO.setNeedPayment(Boolean.FALSE);
            for (OrderPayinfoDTO pay : payInfos) {
                orderUnPayAmount = getPayAmount(orderDTO, pay, orderUnPayAmount);
            }
            orderDTO.setOrderPayinfo(orderByPayTimeDesc(payInfos));
            if (CsStringUtils.equals(orderDTO.getOrderStatus(), OrderStatusEnum.CANCEL.code())) {
                orderUnPayAmount = BigDecimal.ZERO;
            }
            orderDTO.setOrderUnPayAmount(orderUnPayAmount);
        }
    }

    private BigDecimal getPayAmount(OrderDTO orderDTO, OrderPayinfoDTO pay, BigDecimal orderUnPayAmount) {
        //存在线下支付的支付中退款单则需要确认退款
        if (CsStringUtils.equals(pay.getPayinfoWay(), PayWayEnum.OFFLINE.getCode()) &&
                CsStringUtils.equals(pay.getPayinfoStatus(), PayStatusEnum.IN_PAYMENT.getCode()) &&
                CsStringUtils.equals(pay.getPayinfoType(), PayTypeEnum.REFUND.code())) {
            orderDTO.setConfirmRefundFlag(1);
        }
        //每一条支付单的设置
        if ((PayTypeEnum.SUPPLEMENT.code().equals(pay.getPayinfoType())
                || PayTypeEnum.SINGLE.code().equals(pay.getPayinfoType())
                || PayTypeEnum.GROUP.code().equals(pay.getPayinfoType()))
                && (PayStatusEnum.IN_PAYMENT.code().equals(pay.getPayinfoStatus())
                || PayStatusEnum.WAIT_PAYMENT.code().equals(pay.getPayinfoStatus())
                || PayStatusEnum.FAILED.code().equals(pay.getPayinfoStatus()))) {
            orderUnPayAmount = orderUnPayAmount.add(pay.getPayAmount());
            //是否需要支付
            orderDTO.setNeedPayment(Boolean.TRUE);
        }
        ChannelCodeEnum byCode = ChannelCodeEnum.getByCode(pay.getPayinfoWay());
        pay.setPayinfoWayText(byCode == null ? "" : byCode.getMessage());
        //支付方式
        if (PayTypeEnum.SINGLE.code().equals(pay.getPayinfoType()) ||
                PayTypeEnum.GROUP.code().equals(pay.getPayinfoType())) {
            setOrderPayWayInfo(orderDTO, pay);
        }
        return orderUnPayAmount;
    }

    @Nullable
    private List<OrderPayinfoDTO> getOrderPayinfoDTOS(List<OrderDTO> orderDTOList) {
        if (CollectionUtils.isEmpty(orderDTOList)) {
            return null;
        }
        List<String> orderIds = orderDTOList.stream().map(OrderDTO::getOrderId).toList();
        List<OrderPayinfoDTO> dbPayInfoDTOList = orderPayinfoBiz.queryByOrderIds(orderIds);
        if (CollectionUtils.isEmpty(dbPayInfoDTOList)) {
            return null;
        }
        return dbPayInfoDTOList;
    }

    /**
     * 设置订单支付方式
     *
     * @param payinfo
     */
    private void setOrderPayWayInfo(OrderDTO orderDTO, OrderPayinfoDTO payinfo) {
        if (orderDTO != null && payinfo != null) {
            if (orderDTO.getOrderInfoExtDTO() == null) {
                //设置订单扩展信息
                orderDTO.setOrderInfoExtDTO(orderInfoExtBiz.findByOrderId(orderDTO.getOrderId()));
            }
            if (CsStringUtils.isNotBlank(payinfo.getPayinfoWay())) {
                List<String> payWay = Lists.newArrayList();
                List<String> payWayText = Lists.newArrayList();
                addPayWayText(payinfo, payWay, payWayText);
                setPayWayText(orderDTO, payWay, payWayText);
            }
            setSupportPayWay(orderDTO, payinfo);
        }
    }

    private static void setPayWayText(OrderDTO orderDTO, List<String> payWay, List<String> payWayText) {
        if (orderDTO.getOrderInfoExtDTO() != null && BillPaymentTypeEnum.DELIVERY_PAYMENT.getCode().equals(orderDTO.getOrderInfoExtDTO().getBillPaymentType())) {
            if (orderDTO.getPayWay() == null) {
                orderDTO.setPayWay(payWay);
            } else {
                orderDTO.getPayWay().addAll(payWay);
            }
            if (orderDTO.getPayWayText() == null) {
                orderDTO.setPayWayText(payWayText);
            } else {
                orderDTO.getPayWayText().addAll(payWayText);
            }
        } else {
            orderDTO.setPayWay(payWay);
            orderDTO.setPayWayText(payWayText);
        }
    }

    private static void addPayWayText(OrderPayinfoDTO payinfo, List<String> payWay, List<String> payWayText) {
        for (String pay : payinfo.getPayinfoWay().split(",")) {
            if (PayWayEnum.ONLINE.code().equals(pay) ||
                    com.ecommerce.goods.api.enums.PayWayEnum.WENXIN_PAY.code().equals(pay)) {//旧数据:微信支付
                payWay.add(ChannelCodeEnum.WEIXIN.getCode());
                payWayText.add(ChannelCodeEnum.WEIXIN.getMessage());
            } else {
                payWay.add(pay);
                ChannelCodeEnum byCode = ChannelCodeEnum.getByCode(pay);
                payWayText.add(byCode == null ? "" : byCode.getMessage());
            }
        }
    }

    private static void setSupportPayWay(OrderDTO orderDTO, OrderPayinfoDTO payinfo) {
        if (CsStringUtils.isNotBlank(payinfo.getSupportPayway())) {
            if (orderDTO.getOrderInfoExtDTO() != null && BillPaymentTypeEnum.DELIVERY_PAYMENT.getCode().equals(orderDTO.getOrderInfoExtDTO().getBillPaymentType())) {
                if (orderDTO.getSupportPayWay() == null) {
                    orderDTO.setSupportPayWay(Lists.newArrayList(payinfo.getSupportPayway().split(",")));
                } else {
                    orderDTO.getSupportPayWay().addAll(Lists.newArrayList(payinfo.getSupportPayway().split(",")));
                }
            } else {
                orderDTO.setSupportPayWay(Lists.newArrayList(payinfo.getSupportPayway().split(",")));
            }
        }
    }

    /**
     * 批量设置退款信息
     *
     * @param orderDTOList
     */
    private void batchSetOrderRefund(List<OrderDTO> orderDTOList) {
        if (CollectionUtils.isEmpty(orderDTOList)) {
            return;
        }
        Condition condition = new Condition(OrderRefund.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn(ORDER_ID, orderDTOList.stream().map(OrderDTO::getOrderId).toList());
        criteria.andEqualTo(DEL_FLG, false);
        condition.orderBy(UPDATE_TIME).desc();
        List<OrderRefund> refundList = orderRefundBiz.findByCondition(condition);
        if (CollectionUtils.isEmpty(refundList)) {
            return;
        }

        Map<String, List<OrderRefundDTO>> orderId2RefundListMap = refundList.stream()
                .map(refund -> BeanConvertUtils.convert(refund, OrderRefundDTO.class))
                .collect(Collectors.groupingBy(OrderRefundDTO::getOrderId));

        orderDTOList.forEach(order -> order.setOrderRefund(orderId2RefundListMap.get(order.getOrderId())));
    }

    /**
     * 批量设置业务员信息
     *
     * @param orderDTOList
     */
    private void batchSetOrderSalesman(List<OrderDTO> orderDTOList) {
        if (CollectionUtils.isEmpty(orderDTOList)) {
            return;
        }
        List<String> salesManIdList = orderDTOList.stream()
                .map(OrderDTO::getSalesmanId)
                .filter(CsStringUtils::isNotBlank)
                .distinct()
                .toList();
        List<AccountSimpleDTO> accountSimpleDTOS = accountService.findSimpleByIds(salesManIdList);

        if (CollectionUtils.isEmpty(accountSimpleDTOS)) {
            return;
        }

        Map<String, AccountSimpleDTO> salesManId2AccountMap = accountSimpleDTOS.stream().collect(Collectors.toMap(AccountSimpleDTO::getAccountId, Function.identity()));

        for (OrderDTO orderDTO : orderDTOList) {
            AccountSimpleDTO salesManAccount = salesManId2AccountMap.get(orderDTO.getSalesmanId());
            if (salesManAccount != null) {
                orderDTO.setSalesmanName(salesManAccount.getRealName());
                orderDTO.setSalesmanMobile(salesManAccount.getMobile());
            }
        }

    }

    /**
     * 批量设置收货员信息
     *
     * @param orderDTOList
     */
    private void batchSetReceiver(List<OrderDTO> orderDTOList) {

        if (CollectionUtils.isEmpty(orderDTOList)) {
            return;
        }

        List<String> receivingAddressIdList = orderDTOList.stream().map(OrderDTO::getAddressId).filter(CsStringUtils::isNotBlank).distinct().toList();
        if (CollectionUtils.isEmpty(receivingAddressIdList)) {
            return;
        }

        List<ReceivingAddressDTO> receivingAddressDTOList = receivingAddressService.findByIds(receivingAddressIdList);
        if (CollectionUtils.isEmpty(receivingAddressDTOList)) {
            return;
        }

        Map<String, String> addrId2NameMap = receivingAddressDTOList.stream().collect(Collectors.toMap(ReceivingAddressDTO::getId, ReceivingAddressDTO::getConsigneeName));

        orderDTOList.forEach(item -> item.setReceiverName(addrId2NameMap.get(item.getAddressId())));

    }

    /**
     * 批量设置订单子状态
     *
     * @param orderDTOList
     */
    private void batchSetOrderSubStatus(List<OrderDTO> orderDTOList) {
        if (CollectionUtils.isEmpty(orderDTOList)) {
            return;
        }
        List<String> orderIds = orderDTOList.stream().map(OrderDTO::getOrderId).toList();
        List<OrderSubStatusDTO> orderSubStatusDTOList = orderSubStatusBiz.findByOrderIdList(orderIds);
        if (CollectionUtils.isEmpty(orderSubStatusDTOList)) {
            return;
        }

        Map<String, List<OrderSubStatusDTO>> orderId2OrderSubStatusListMap = orderSubStatusDTOList.stream().collect(Collectors.groupingBy(OrderSubStatusDTO::getOrderId));

        orderDTOList.forEach(orderDTO -> orderDTO.setOrderSubStatusList(orderId2OrderSubStatusListMap.get(orderDTO.getOrderId())));

    }

    /**
     * 批量设置合同信息
     *
     * @param orderDTOList
     */
    private void batchSetContract(List<OrderDTO> orderDTOList) {
        if (CollectionUtils.isEmpty(orderDTOList)) {
            return;
        }
        List<String> dealsIdList = orderDTOList.stream().map(OrderDTO::getDealsId).filter(CsStringUtils::isNotBlank).distinct().toList();
        if (CollectionUtils.isEmpty(dealsIdList)) {
            return;
        }
        QueryContractConditionDTO contractConditionDTO = new QueryContractConditionDTO();
        contractConditionDTO.setContractIdList(dealsIdList);
        contractConditionDTO.setIgnoreDelFlg(Boolean.TRUE);
        ItemResult<List<SimpleContractDTO>> contractDTOListResult = contractService.getContractList(contractConditionDTO);

        if (contractDTOListResult == null || CollectionUtils.isEmpty(contractDTOListResult.getData())) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "查询合同信息失败: contractIds-%s".formatted(dealsIdList));
        }

        Map<String, TrContractDTO> dealId2ContractMap = contractDTOListResult.getData()
                .stream()
                .map(item -> {
                    TrContractDTO contractDTO = new TrContractDTO();
                    BeanUtils.copyProperties(item, contractDTO);
                    return contractDTO;
                })
                .collect(Collectors.toMap(TrContractDTO::getContractId, Function.identity()));

        orderDTOList.forEach(orderDTO -> orderDTO.setContract(dealId2ContractMap.get(orderDTO.getDealsId())));
    }

    /**
     * 批量设置销售区域
     *
     * @param orderDTOList
     */
    private void batchSetSaleArea(List<OrderDTO> orderDTOList) {

        Set<String> saleRegionIdSet = Sets.newHashSet();

        saleRegionIdSetAdd(orderDTOList, saleRegionIdSet);

        if (CollectionUtils.isEmpty(saleRegionIdSet)) {
            return;
        }

        SaleRegionCondDTO saleRegionCondDTO = new SaleRegionCondDTO();
        saleRegionCondDTO.setSaleRegionIdList(Lists.newArrayList(saleRegionIdSet));

        List<SaleRegionSampleDTO> sampleDTOList = saleRegionService.batchQuerySaleRegion(saleRegionCondDTO);

        if (CollectionUtils.isEmpty(sampleDTOList)) {
            return;
        }

        Map<String, String> saleRegionId2NameMap = sampleDTOList.stream().collect(Collectors.toMap(SaleRegionSampleDTO::getSaleRegionId, SaleRegionSampleDTO::getSaleRegionName));

        setSaleRegionPath(orderDTOList, saleRegionId2NameMap);

    }

    private static void setSaleRegionPath(List<OrderDTO> orderDTOList, Map<String, String> saleRegionId2NameMap) {
        for (OrderDTO orderDTO : orderDTOList) {
            String saleRegionName1 = saleRegionId2NameMap.get(orderDTO.getSaleRegion1());
            String saleRegionName2 = saleRegionId2NameMap.get(orderDTO.getSaleRegion2());
            String saleRegionName3 = saleRegionId2NameMap.get(orderDTO.getSaleRegion3());
            StringBuilder buf = new StringBuilder();
            if (CsStringUtils.isNotBlank(saleRegionName1)) {
                buf.append(saleRegionName1);
            }
            if (CsStringUtils.isNotBlank(saleRegionName2)) {
                buf.append(saleRegionName2);
            }
            if (CsStringUtils.isNotBlank(saleRegionName3)) {
                buf.append(saleRegionName3);
            }
            orderDTO.setSaleRegionPath(buf.toString());

        }
    }

    private static void saleRegionIdSetAdd(List<OrderDTO> orderDTOList, Set<String> saleRegionIdSet) {
        for (OrderDTO orderDTO : orderDTOList) {
            String saleRegion1 = orderDTO.getSaleRegion1();
            if (CsStringUtils.isNotBlank(saleRegion1)) {
                saleRegionIdSet.add(saleRegion1);
            }
            String saleRegion2 = orderDTO.getSaleRegion2();
            if (CsStringUtils.isNotBlank(saleRegion2)) {
                saleRegionIdSet.add(saleRegion2);
            }
            String saleRegion3 = orderDTO.getSaleRegion3();
            if (CsStringUtils.isNotBlank(saleRegion3)) {
                saleRegionIdSet.add(saleRegion3);
            }
        }
    }

    /**
     * 设置订单其它费用(货款、物流款以外的费用)
     */
    private void setOrderOthersAmount(OrderDTO order) {
        order.setMachineShiftCost(getOrderMachineShiftCost(order));
        order.setTruckage(getOrderFloorTruckage(order));
        order.setOthersAmount(getOrderOthersAmount(order));
    }

    /**
     * 订单台班费
     *
     * @param order
     * @return
     */
    private BigDecimal getOrderMachineShiftCost(OrderDTO order) {
        if (order.getMachineShiftCost() == null) {
            order.setMachineShiftCost(BigDecimal.ZERO);
            if (CollectionUtils.isNotEmpty(order.getOrderPayinfo())) {
                order.getOrderPayinfo().stream().forEach(payinfo -> {
                    if (CollectionUtils.isNotEmpty(payinfo.getOrderPayinfoDetail()) && (
                            PayTypeEnum.SINGLE.code().equals(payinfo.getPayinfoType()) ||
                                    PayTypeEnum.GROUP.code().equals(payinfo.getPayinfoType()))) {//下单的订单费用
                        payinfo.getOrderPayinfoDetail().stream().forEach(detail -> {
                            if (PayDetailTypeEnum.MACHINE_SHIFT_COST.code().equals(detail.getPaytypeDetail())) {//台班费
                                order.setMachineShiftCost(ArithUtils.add(order.getMachineShiftCost(), detail.getPayAdmount()));
                            }
                        });
                    }
                });
            }
        }
        return order.getMachineShiftCost();
    }

    /**
     * 订单搬运费
     *
     * @param order
     * @return
     */
    private BigDecimal getOrderFloorTruckage(OrderDTO order) {
        if (order.getTruckage() == null) {
            order.setTruckage(BigDecimal.ZERO);
            if (CollectionUtils.isNotEmpty(order.getOrderPayinfo())) {
                order.getOrderPayinfo().stream().forEach(payinfo -> {
                    if (CollectionUtils.isNotEmpty(payinfo.getOrderPayinfoDetail()) && (
                            PayTypeEnum.SINGLE.code().equals(payinfo.getPayinfoType()) ||
                                    PayTypeEnum.GROUP.code().equals(payinfo.getPayinfoType()))) {//下单的订单费用
                        payinfo.getOrderPayinfoDetail().stream().forEach(detail -> {
                            if (PayDetailTypeEnum.FLOOR_TRUCKAGE.code().equals(detail.getPaytypeDetail())) {//搬运费
                                order.setTruckage(ArithUtils.add(order.getTruckage(), detail.getPayAdmount()));
                            }
                        });
                    }
                });
            }
        }
        return order.getTruckage();
    }

    /**
     * 订单其它费用（除台班费,搬运费）
     *
     * @param order
     * @return
     */
    private BigDecimal getOrderOthersAmount(OrderDTO order) {
        return ArithUtils.subtract(order.getActualOthersAmount(),
                getOrderMachineShiftCost(order), getOrderFloorTruckage(order));
    }

    private void computeSettlementAmount(OrderDTO order) {
        computeSettlementAmountWithoutQuery(order);
        //计算应支付金额
        Set<String> finalOrderStatusSet = Sets.newHashSet(
                OrderStatusEnum.CANCEL.getCode(),
                OrderStatusEnum.CLOSED.getCode(),
                OrderStatusEnum.COMPLETED.getCode(),
                OrderStatusEnum.ERP_CLOSING.getCode(),
                OrderStatusEnum.ERP_COMPLETING.getCode());
        //订单处于最终状态取订单实际发生金额
        order.setRealtimeOrderAmount(order.getRealtimeOrderAmount() == null ? BigDecimal.ZERO : order.getRealtimeOrderAmount());
        if (finalOrderStatusSet.contains(order.getOrderStatus())) {
            order.setShouldPayAmount(order.getRealtimeOrderAmount());
        } else {
            if (order.getActualOrderAmount().compareTo(order.getRealtimeOrderAmount()) > -1) {
                order.setShouldPayAmount(order.getActualOrderAmount());
            } else {
                order.setShouldPayAmount(order.getRealtimeOrderAmount());
            }
        }
    }

    private void computeSettlementAmountWithoutQuery(OrderDTO order) {
        BigDecimal settlementAmount = BigDecimal.ZERO;
        if (order.getActualOrderAmount() != null) {
            settlementAmount = settlementAmount.add(order.getActualOrderAmount());
        }
        List<OrderPayinfoDTO> orderPayinfo = order.getOrderPayinfo();

        if (CollectionUtils.isNotEmpty(orderPayinfo)) {
            BigDecimal supplement = orderPayinfo.stream()
                    .filter(item -> PayTypeEnum.SUPPLEMENT.code().equals(item.getPayinfoType()))
                    .map(i -> i.getPayAmount() == null ? BigDecimal.ZERO : i.getPayAmount())
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            settlementAmount = settlementAmount.add(supplement);
        }

        if (order.getRealRefundAmount() != null) {
            settlementAmount = settlementAmount.subtract(order.getRealRefundAmount());
        }
        // 支付金额 + 补款金额 - 退款金额
        order.setSettlementAmount(settlementAmount);
    }

    /**
     * 批量设置订单子项
     *
     * @param uninvoicedOrderDTOList
     */
    private void batchSetOrderItemDTOs(List<UninvoicedOrderDTO> uninvoicedOrderDTOList) {
        if (CollectionUtils.isEmpty(uninvoicedOrderDTOList)) {
            return;
        }
        List<String> orderIds = uninvoicedOrderDTOList.stream().map(UninvoicedOrderDTO::getOrderId).toList();
        Condition condition = new Condition(OrderItem.class);
        condition.createCriteria()
                .andIn(ORDER_ID, orderIds)
                .andEqualTo(DEL_FLG, false);
        List<OrderItem> orderItems = orderItemBiz.findByCondition(condition);
        if (CollectionUtils.isEmpty(orderItems)) {
            return;
        }
        Map<String, List<UninvoicedOrderItemDTO>> orderId2ItemsListMap = orderItems.stream()
                .filter(item -> CsStringUtils.isNotBlank(item.getOrderId()))
                .map(item -> {
                    UninvoicedOrderItemDTO itemDTO = new UninvoicedOrderItemDTO();
                    BeanUtils.copyProperties(item, itemDTO);
                    if (item.getItemSignQuantity() == null || item.getItemSignQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                        itemDTO.setItemSignQuantity(item.getItemSendQuantity());
                    }
                    return itemDTO;
                })
                .collect(Collectors.groupingBy(UninvoicedOrderItemDTO::getOrderId));
        for (UninvoicedOrderDTO uninvoicedOrderDTO : uninvoicedOrderDTOList) {
            List<UninvoicedOrderItemDTO> uninvoicedOrderDTOS1 = orderId2ItemsListMap.get(uninvoicedOrderDTO.getOrderId());
            uninvoicedOrderDTO.setUninvoicedOrderItemDTOs(uninvoicedOrderDTOS1);
        }
    }

    /**
     * 批量设置分账明细
     *
     * @param uninvoicedOrderDTOList
     */
    private void batchSetBillSplitDTOs(List<UninvoicedOrderDTO> uninvoicedOrderDTOList) {
        if (CollectionUtils.isEmpty(uninvoicedOrderDTOList)) {
            return;
        }
        List<String> orderIds = uninvoicedOrderDTOList.stream().map(UninvoicedOrderDTO::getOrderId).toList();
        Condition condition = new Condition(BillSplit.class);
        condition.createCriteria()
                .andIn(ORDER_ID, orderIds)
                .andEqualTo(DEL_FLAG, false);
        List<BillSplit> billSplits = billSplitBiz.findByCondition(condition);
        if (CollectionUtils.isEmpty(billSplits)) {
            return;
        }
        Map<String, List<BillSplitDTO>> orderId2BillSplitMap = billSplits.stream()
                .filter(item -> CsStringUtils.isNotBlank(item.getOrderId()))
                .map(item -> {
                    BillSplitDTO billSplitDTO = new BillSplitDTO();
                    BeanUtils.copyProperties(item, billSplitDTO);
                    return billSplitDTO;
                })
                .collect(Collectors.groupingBy(BillSplitDTO::getOrderId));
        for (UninvoicedOrderDTO uninvoicedOrderDTO : uninvoicedOrderDTOList) {
            List<BillSplitDTO> billSplitDTOS = orderId2BillSplitMap.get(uninvoicedOrderDTO.getOrderId());
            uninvoicedOrderDTO.setBillSplitDTOS(billSplitDTOS);
        }
    }

    /**
     * 批量设置订单加价项
     *
     * @param orderItemDTOList
     */
    private void batchSetOrderItemAdd(List<OrderItemDTO> orderItemDTOList) {
        if (CollectionUtils.isEmpty(orderItemDTOList)) {
            return;
        }
        List<String> orderItemIds = orderItemDTOList.stream().map(OrderItemDTO::getOrderItemId).toList();
        Condition condition = new Condition(OrderItemAdd.class);
        condition.createCriteria()
                .andIn("orderItemId", orderItemIds)
                .andEqualTo(DEL_FLG, 0);
        List<OrderItemAdd> orderItemAddList = orderItemAddBiz.findByCondition(condition);
        if (CollectionUtils.isEmpty(orderItemAddList)) {
            return;
        }
        Map<String, List<OrderItemAddDTO>> orderId2OrderItemAddMap = orderItemAddList.stream()
                .map(item -> {
                    OrderItemAddDTO orderItemAddDTO = new OrderItemAddDTO();
                    BeanUtils.copyProperties(item, orderItemAddDTO);
                    return orderItemAddDTO;
                })
                .collect(Collectors.groupingBy(OrderItemAddDTO::getOrderItemId));
        for (OrderItemDTO orderItemDTO : orderItemDTOList) {
            List<OrderItemAddDTO> orderItemAdds = orderId2OrderItemAddMap.get(orderItemDTO.getOrderItemId());
            orderItemDTO.setOrderItemAdds(orderItemAdds);
            orderItemDTO.setSupportAddItem(false);
            if (CollectionUtils.isNotEmpty(orderItemAdds)) {
                orderItemDTO.setSupportAddItem(true);
            }
        }
    }

    /**
     * OrderItemVO转DTO
     *
     * @param item
     * @param goods
     */
    private OrderItemDTO convertItemDTO(OrderItem item, GoodsDTO goods) {
        OrderItemDTO itemDTO = BeanConvertUtils.convert(item, OrderItemDTO.class);
        try {
            if (CsStringUtils.isNotBlank(itemDTO.getResourceId())) {
                ResourceDTO resource = resourceService.getResourceDetail(itemDTO.getResourceId());
                if (resource != null) {
                    itemDTO.setOrderMinChangeNum(resource.getOrderminchangeNum());
                }
            }

            if (goods != null) {
                itemDTO.setBrand(goods.getBrand());
                itemDTO.setPack(goods.getPack());
                itemDTO.setSpecs(goods.getSpecs());
                itemDTO.setGoodsUnit(goods.getUnit());
                itemDTO.setMark(goods.getMark());
                itemDTO.setCategoryType(goods.getCategoryType());
                String[] imgs = goods.getImgs();
                if (imgs != null && imgs.length > 0) {
                    itemDTO.setGoodsPic(goods.getImgs()[0]);//商品图片
                }
            }
            //商品单价
            itemDTO.setResourceUnitPrice(itemDTO.getActualUnitPrice());
            //成交单价 = 商品单价 + 物流单价 + 其他单价
            BigDecimal logisticsUnitPrice = ArithUtils.divide(itemDTO.getActualLogisticPrice(), itemDTO.getItemQuantity());
            itemDTO.setTradeUnitPrice(ArithUtils.add(itemDTO.getActualUnitPrice(), logisticsUnitPrice));

            itemDTO.setActualResDiscPrice(ArithUtils.subtract(itemDTO.getActualUnitPrice(), itemDTO.getAdditemPrice()));
            itemDTO.setActualResUnitPrice(ArithUtils.add(itemDTO.getActualResDiscPrice(), itemDTO.getDiscountPrice()));
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return itemDTO;
    }

    /**
     * 订单优惠明细
     *
     * @param objectIds
     */
    private List<OrderDiscountDetailDTO> getOrderDiscountDetails(List<String> objectIds) {
        List<OrderDiscountDetailDTO> discountDetails = null;
        Condition queryDiscount = new Condition(OrderDiscountDetail.class);
        queryDiscount.createCriteria().andIn(OBJECT_ID, objectIds);
        queryDiscount.createCriteria().andNotEqualTo(DEL_FLG, CommonConstants.DEL_FLAG_TRUE);
        List<OrderDiscountDetail> discountList = orderDiscountDetailBiz.findByCondition(queryDiscount);
        if (CollectionUtils.isNotEmpty(discountList)) {
            discountDetails = Lists.newArrayList();
            for (OrderDiscountDetail discount : discountList) {
                discountDetails.add(BeanConvertUtils.convert(discount, OrderDiscountDetailDTO.class));
            }
        }

        return discountDetails;
    }

    /**
     * 锁价前价格变化
     *
     * @param orderDTO
     */
    private void setOrderResoureChange(OrderDTO orderDTO) {
        OrderResoureChange queryChange = new OrderResoureChange();
        queryChange.setOrderId(orderDTO.getOrderId());
        queryChange.setDelFlg(false);
        List<OrderResoureChange> changeList = orderResoureChangeBiz.find(queryChange);
        if (CollectionUtils.isNotEmpty(changeList)) {
            List<OrderResoureChangeDTO> orderResoureChange = Lists.newArrayList();
            for (OrderResoureChange change : changeList) {
                orderResoureChange.add(BeanConvertUtils.convert(change, OrderResoureChangeDTO.class));
            }
            orderDTO.setOrderResoureChange(orderResoureChange);
        }
    }

    /**
     * 设置支付信息
     *
     * @param orderDTO
     */
    private void setOrderPayinfo(OrderDTO orderDTO) {
        Condition condition = new Condition(OrderPayinfo.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(OBJECT_ID, orderDTO.getOrderId());
        criteria.andEqualTo(DEL_FLG, false);
        condition.orderBy(CREATE_TIME).desc();
        List<OrderPayinfo> payList = orderPayinfoBiz.findByCondition(condition);
        BigDecimal orderUnPayAmount = BigDecimal.ZERO;
        List<OrderPayinfoDTO> orderPayinfo = Lists.newArrayList();
        orderUnPayAmount = getOrderUnPayAmount(orderDTO, payList, orderUnPayAmount, orderPayinfo);
        orderDTO.setOrderUnPayAmount(orderUnPayAmount);
    }

    private BigDecimal getOrderUnPayAmount(OrderDTO orderDTO, List<OrderPayinfo> payList, BigDecimal orderUnPayAmount, List<OrderPayinfoDTO> orderPayinfo) {
        if (CollectionUtils.isNotEmpty(payList)) {
            log.info("payList:{}", payList.stream().map(OrderPayinfo::getPayinfoId).collect(Collectors.joining(",")));
            for (OrderPayinfo pay : payList) {
                //存在线下支付的支付中退款单则需要确认退款
                setConfirmRefundFlag(orderDTO, pay);
                orderUnPayAmount = getPayAmount(orderUnPayAmount, pay);

                OrderPayinfoDTO payDTO = BeanConvertUtils.convert(pay, OrderPayinfoDTO.class);
                ChannelCodeEnum byCode = ChannelCodeEnum.getByCode(payDTO.getPayinfoWay());
                payDTO.setPayinfoWayText(byCode == null ? "" : byCode.getMessage());
                payDTO.setOfflineAttachment(orderPayinfoAttachmentBiz.findByPayinfoId(pay.getPayinfoId()));
                payDTO.setPayDescriptionList(OrderPayInfoInstructionsDTO.json2List(pay.getPayDescription()));
                orderPayinfo.add(payDTO);
                //支付方式
                if (PayTypeEnum.SINGLE.code().equals(pay.getPayinfoType()) ||
                        PayTypeEnum.GROUP.code().equals(pay.getPayinfoType())) {
                    setOrderPayWayInfo(orderDTO, payDTO);
                }
            }
            //设置支付单明细
            getOrderPayinfoDetailList(orderPayinfo, null);
        } else {
            orderUnPayAmount = orderDTO.getActualOrderAmount();
        }
        orderDTO.setOrderPayinfo(orderByPayTimeDesc(orderPayinfo));
        if (CsStringUtils.equals(orderDTO.getOrderStatus(), OrderStatusEnum.CANCEL.code())) {
            orderUnPayAmount = BigDecimal.ZERO;
        }
        return orderUnPayAmount;
    }

    private static void setConfirmRefundFlag(OrderDTO orderDTO, OrderPayinfo pay) {
        if (CsStringUtils.equals(pay.getPayinfoWay(), PayWayEnum.OFFLINE.getCode()) &&
                CsStringUtils.equals(pay.getPayinfoStatus(), PayStatusEnum.IN_PAYMENT.getCode()) &&
                CsStringUtils.equals(pay.getPayinfoType(), PayTypeEnum.REFUND.code())) {
            orderDTO.setConfirmRefundFlag(1);
        }
    }

    private static BigDecimal getPayAmount(BigDecimal orderUnPayAmount, OrderPayinfo pay) {
        if ((PayTypeEnum.SUPPLEMENT.code().equals(pay.getPayinfoType())
                || PayTypeEnum.SINGLE.code().equals(pay.getPayinfoType())
                || PayTypeEnum.GROUP.code().equals(pay.getPayinfoType()))
                && (PayStatusEnum.IN_PAYMENT.code().equals(pay.getPayinfoStatus())
                || PayStatusEnum.WAIT_PAYMENT.code().equals(pay.getPayinfoStatus())
                || PayStatusEnum.FAILED.code().equals(pay.getPayinfoStatus()))) {
            orderUnPayAmount = orderUnPayAmount.add(pay.getPayAmount());
        }
        return orderUnPayAmount;
    }

    /**
     * 设置订单支付方式
     *
     * @param orderDTO
     */
    private void setOrderPayWay(OrderDTO orderDTO) {
        OrderPayinfo orderPay = getOrderPay(orderDTO.getOrderId());
        if (orderPay != null) {
            setOrderPayWayInfo(orderDTO, BeanConvertUtils.convert(orderPay, OrderPayinfoDTO.class));
        }
    }

    /**
     * 设置支付明细
     *
     * @param orderPayinfoDTOList
     */
    private void getOrderPayinfoDetailList(List<OrderPayinfoDTO> orderPayinfoDTOList, PayDetailTypeEnum payDetailType) {
        if (CollectionUtils.isEmpty(orderPayinfoDTOList)) {
            return;
        }
        List<String> PayinfoIds = orderPayinfoDTOList.stream().map(OrderPayinfoDTO::getPayinfoId).toList();
        Condition orderItemExa = new Condition(OrderPayinfoDetail.class);
        orderItemExa.createCriteria()
                .andIn("payinfoId", PayinfoIds);
        List<OrderPayinfoDetail> orderPayinfoDetailList = orderPayinfoDetailBiz.findByCondition(orderItemExa);
        Map<String, List<OrderPayinfoDetailDTO>> payinfoId2OrderPayinfoDetailMap = orderPayinfoDetailList.stream()
                .filter(item -> payDetailType == null || payDetailType.code().equals(item.getPaytypeDetail()))
                .map(item -> {
                    OrderPayinfoDetailDTO orderPayinfoDetail = new OrderPayinfoDetailDTO();
                    BeanUtils.copyProperties(item, orderPayinfoDetail);
                    return orderPayinfoDetail;
                })
                .collect(Collectors.groupingBy(OrderPayinfoDetailDTO::getPayinfoId));
        for (OrderPayinfoDTO orderPayinfoDTO : orderPayinfoDTOList) {
            List<OrderPayinfoDetailDTO> orderPayinfoDetailDTOList = payinfoId2OrderPayinfoDetailMap.get(orderPayinfoDTO.getPayinfoId());
            orderPayinfoDTO.setOrderPayinfoDetail(orderPayinfoDetailDTOList);
        }
    }

    private List<OrderPayinfoDTO> orderByPayTimeDesc(List<OrderPayinfoDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return list;
        }
        //付款单列表，建议按创建时间倒叙排列
        //按支付时间倒序排列 空的放后面
        list.sort((Comparator.comparing(OrderPayinfoDTO::getCreateTime).reversed()));
        return list;
    }

    /**
     * 设置开票信息
     *
     * @param orderDTO
     */
    private void setOrderTaxinfo(OrderDTO orderDTO) {
        OrderTaxinfo queryTax = new OrderTaxinfo();
        queryTax.setOrderId(orderDTO.getOrderId());
        queryTax.setDelFlg(false);
        List<OrderTaxinfo> taxList = orderTaxinfoBiz.find(queryTax);
        if (CollectionUtils.isNotEmpty(taxList)) {
            List<OrderTaxinfoDTO> orderTaxinfo = Lists.newArrayList();
            for (OrderTaxinfo tax : taxList) {
                orderTaxinfo.add(BeanConvertUtils.convert(tax, OrderTaxinfoDTO.class));
            }
            orderDTO.setOrderTaxinfo(orderTaxinfo);
        }
    }

    /**
     * 设置退款请求单
     *
     * @param orderDTO
     */
    private OrderDTO setOrderRefund(OrderDTO orderDTO) {
        List<OrderDTO> orderDTOList = Lists.newArrayList(orderDTO);
        batchSetOrderRefund(orderDTOList);
        return orderDTOList.get(0);
    }

    /**
     * 设置业务员信息
     *
     * @param orderDTO
     */
    private OrderDTO setOrderSalesman(OrderDTO orderDTO) {
        List<OrderDTO> orderDTOList = Lists.newArrayList(orderDTO);
        batchSetOrderSalesman(orderDTOList);
        return orderDTOList.get(0);
    }

    /**
     * 设置收货员信息
     *
     * @param orderDTO
     */
    private OrderDTO setReceiver(OrderDTO orderDTO) {
        List<OrderDTO> orderDTOList = Lists.newArrayList(orderDTO);
        batchSetReceiver(orderDTOList);
        return orderDTOList.get(0);
    }

    /**
     * 设置合同信息
     *
     * @param orderDTO
     */
    private void setContract(OrderDTO orderDTO) {
        String dealsId = orderDTO.getDealsId();
        try {
            buildOrderDTO(orderDTO, dealsId);
        } catch (Exception e) {
            log.error("查询合同信息异常", e);
            throw new BizException(BasicCode.UNDEFINED_ERROR, String.format("查询合同信息失败:orderId-%s contractId-%s", orderDTO.getOrderId(), dealsId));
        }
    }

    private void buildOrderDTO(OrderDTO orderDTO, String dealsId) {
        if (CsStringUtils.isNotBlank(dealsId)) {
            TrContractDTO contractDTO = new TrContractDTO();
            com.ecommerce.goods.api.dto.contract.TrContractDTO trContractDTO = contractService.getContractByExist(dealsId);
            if (trContractDTO != null) {
                BeanUtils.copyProperties(trContractDTO, contractDTO, "contractAdditemDTOS");
                if (CollectionUtils.isNotEmpty(trContractDTO.getContractAdditemDTOS())) {
                    List<TrContractAdditemDTO> addItemList = Lists.newArrayList();
                    //子项去重
                    buildAddItemList(trContractDTO, addItemList);
                    contractDTO.setContractAdditemDTOS(addItemList);
                }
            }
            orderDTO.setContract(contractDTO);
        }
    }

    private static void buildAddItemList(com.ecommerce.goods.api.dto.contract.TrContractDTO trContractDTO, List<TrContractAdditemDTO> addItemList) {
        for (com.ecommerce.goods.api.dto.contract.TrContractAdditemDTO trContractAdditemDTO : trContractDTO.getContractAdditemDTOS()) {
            TrContractAdditemDTO contractAdditemDTO = new TrContractAdditemDTO();
            BeanUtils.copyProperties(trContractAdditemDTO, contractAdditemDTO, "children");
            Map<String, TrContractAdditemDTO> childrenMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(trContractAdditemDTO.getChildren())) {
                trContractAdditemDTO.getChildren().stream().forEach(addItemDTO -> {
                    if (childrenMap.get(addItemDTO.getAdditemId()) != null) return;
                    TrContractAdditemDTO childrenAdditemDTO = new TrContractAdditemDTO();
                    BeanUtils.copyProperties(addItemDTO, childrenAdditemDTO);
                    childrenMap.put(addItemDTO.getAdditemId(), childrenAdditemDTO);
                });
            }
            contractAdditemDTO.setChildren(Lists.newArrayList(childrenMap.values()));
            addItemList.add(contractAdditemDTO);
        }
    }

    /**
     * 设置合同信息
     *
     * @param orderDTO
     */
    private void setOrderSubStatus(OrderDTO orderDTO) {
        orderDTO.setOrderSubStatusList(orderSubStatusBiz.findByOrderId(orderDTO.getOrderId()));
    }

    @Override
    public List<ERPAddressDTO> queryReceivingAddressMap(List<String> erpUnloadAddressIdList) {
        return orderErpAddressBiz.queryReceivingAddressMap(erpUnloadAddressIdList);
    }

    @Override
    public List<PickingBillExportInfoDTO> queryPickingBillExportInfo(Set<String> ids) {
        List<PickingBillExportInfoDTO> result = orderItemBiz.queryPickingBillExportInfo(ids);
        Set<String> categoryCodeSet = new HashSet<>();
        result.forEach(pickingBillExportInfoDTO -> categoryCodeSet.add(pickingBillExportInfoDTO.getGoodsCategory()));
        List<GoodsCategorySimpleDTO> goodsCategorySimpleDTOS = goodsCategoryService.getSimpleList(categoryCodeSet);
        Map<String, String> map = goodsCategorySimpleDTOS.stream().collect(Collectors.toMap(GoodsCategorySimpleDTO::getCategoryCode, GoodsCategorySimpleDTO::getCategoryCode));
        result.forEach(pickingBillExportInfoDTO -> pickingBillExportInfoDTO.setGoodsCategory(map.get(pickingBillExportInfoDTO.getGoodsCategory())));
        return result;
    }

    @Override
    public List<TradingFlowExportInfoDTO> queryTradingFlowExportInfo(Set<String> ids) {
        return orderItemBiz.queryTradingFlowExportInfo(ids);
    }

    //根据合同和合同商品查询到的要关闭的订单
    @PrintArgs
    @Override
    public List<OrderSimpleDTO> queryOrderByContractBatchAdjustPriceInfo(OrderSimpleQueryDTO dto) {
        log.info("queryOrderByContractBatchAdjustPriceInfo:{}", dto);
        List<OrderSimpleDTO> result = Lists.newArrayList();
        if (dto == null || CollectionUtils.isEmpty(dto.getList())) {
            return result;
        }
        Set<String> contractIds = dto.getList().stream().map(OrderSimpleQueryContractDTO::getContractId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(contractIds)) {
            return result;
        }
        Set<OrderSimpleQueryContractGoodsDTO> contractGoodsIds = dto.getList().stream()
                .map(OrderSimpleQueryContractDTO::getContractGoods).flatMap(Collection::stream)
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(contractGoodsIds)) {
            return result;
        }
        //1、根据合同id查询待发货或者发货中的订单
        Condition condition = new Condition(OrderInfo.class);
        //二级订单关联一级订单后不触发自动关闭操作（必须由一级单关闭后触发）
        condition.createCriteria()
                .andIn(ORDER_STATUS, Sets.newHashSet(OrderStatusEnum.IN_DELIVERY.getCode(), OrderStatusEnum.WAIT_DELIVERED.getCode()))
                .andIn("dealsId", contractIds);
        List<OrderInfo> orderInfoList = orderInfoBiz.findByCondition(condition);
        if (CollectionUtils.isEmpty(orderInfoList)) {
            return result;
        }
        //2、根据第一步查询的结果和合同商品id查询合同商品
        Set<String> orderIds = orderInfoList.stream().map(OrderInfo::getOrderId).collect(Collectors.toSet());
        log.info("根据合同id查询待发货或者发货中的订单 orderIds:{}", orderIds);
        //3、查找是否关联了一集订单(过滤掉关联了一级订单的二级订单)
        List<OrderProxyMap> orderProxyMapList = orderProxyMapBiz.findBySecondaryOrderIds(orderIds);
        Set<String> secondaryOrder = Sets.newHashSet();//关联了一级订单的二级订单
        if (CollectionUtils.isNotEmpty(orderProxyMapList)) {
            //关联了一级订单的二级订单
            Set<String> collect = orderProxyMapList.stream()
                    .filter(item -> CsStringUtils.isNotBlank(item.getPrimaryOrderId()))
                    .map(OrderProxyMap::getSecondaryOrderId)
                    .collect(Collectors.toSet());
            secondaryOrder.addAll(collect);
            log.info("关联了一级订单的二级订单 orderIds:{}", orderIds);
        }
        if (CollectionUtils.isNotEmpty(secondaryOrder)) {
            orderIds.removeAll(secondaryOrder);//过滤掉关联了一级订单的二级订单
            if (CollectionUtils.isEmpty(orderIds)) {
                log.info("过滤掉关联了一级订单的二级订单后结果为空了");
                return result;
            }
        }
        //查询订单详情
        Set<String> goodsIds = contractGoodsIds.stream().map(OrderSimpleQueryContractGoodsDTO::getGoodsId).collect(Collectors.toSet());
        Condition condition2 = new Condition(OrderItem.class);
        condition2.createCriteria().andIn(ORDER_ID, orderIds).andIn("goodsId", goodsIds);
        List<OrderItem> orderItemList = orderItemBiz.findByCondition(condition2);
        log.info("根据第一步查询的结果和合同商品id查询合同商品:{}", orderItemList.stream().map(OrderItem::getOrderId).collect(Collectors.joining(",")));
        //根据条件过滤
        Map<String, List<OrderInfo>> orderInfoMap = orderInfoList.stream().collect(Collectors.groupingBy(OrderInfo::getDealsId));
        Map<String, List<OrderItem>> orderItemMap = orderItemList.stream().collect(Collectors.groupingBy(OrderItem::getOrderId));
        Map<String, OrderInfoExtDTO> orderInfoExtDTOMap = orderInfoExtBiz.findByOrderIds(orderIds).stream().collect(Collectors.toMap(OrderInfoExtDTO::getOrderId, Function.identity(), (k1, k2) -> k2));
        Set<String> resultOrderCodeSet = Sets.newHashSet();
        buildResultOrderCodeSet(dto, orderInfoMap, orderItemMap, resultOrderCodeSet, orderInfoExtDTOMap, result);
        log.info("筛选到订单:{}", resultOrderCodeSet);
        return result;
    }

    private static void buildResultOrderCodeSet(OrderSimpleQueryDTO dto, Map<String, List<OrderInfo>> orderInfoMap, Map<String, List<OrderItem>> orderItemMap, Set<String> resultOrderCodeSet, Map<String, OrderInfoExtDTO> orderInfoExtDTOMap, List<OrderSimpleDTO> result) {
        for (OrderSimpleQueryContractDTO contractGoods : dto.getList()) {
            List<OrderInfo> orderInfos = orderInfoMap.get(contractGoods.getContractId());
            if (CollectionUtils.isEmpty(orderInfos)) {
                continue;
            }
            for (OrderInfo orderInfo : orderInfos) {
                List<OrderItem> orderItems = orderItemMap.get(orderInfo.getOrderId());
                if (CollectionUtils.isEmpty(orderItems) || resultOrderCodeSet.contains(orderInfo.getOrderId())) {
                    continue;
                }
                Set<String> collect = contractGoods.getContractGoods().stream().map(item -> item.getGoodsId() + "_" + item.getTransportType()).collect(Collectors.toSet());
                OrderInfoExtDTO orderInfoExtDTO = orderInfoExtDTOMap.get(orderInfo.getOrderId());
                if (orderItems.stream().anyMatch(item -> collect.contains(item.getGoodsId() + "_" + orderInfoExtDTO.getTransportType()))) {
                    result.add(BeanConvertUtils.convert(orderInfo, OrderSimpleDTO.class));
                    resultOrderCodeSet.add(orderInfo.getOrderCode());
                }
            }
        }
    }

    /**
     * 批量设置订单扩展信息
     *
     * @param orderList 订单列表对象
     */
    public void batchSetOrderInfoExt(List<OrderDTO> orderList) {
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }
        List<String> orderIds = orderList.stream().map(OrderDTO::getOrderId).toList();
        List<OrderInfoExtDTO> orderInfoExtList = orderInfoExtBiz.findByOrderIds(orderIds);
        Map<String, OrderInfoExtDTO> orderInfoExtMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(orderInfoExtList)) {
            orderInfoExtList.stream().forEach(orderInfoExtDTO ->
                    orderInfoExtMap.put(orderInfoExtDTO.getOrderId(), orderInfoExtDTO));
        }
        orderList.stream().forEach(orderDTO -> {
            OrderInfoExtDTO orderInfoExtDTO = orderInfoExtMap.get(orderDTO.getOrderId());
            if (orderInfoExtDTO == null) {
                orderInfoExtDTO = new OrderInfoExtDTO();
                orderInfoExtBiz.defaultOrderExtDTO(orderInfoExtDTO);
            }
            //
            if (orderInfoExtDTO.getLubricityQuantity() != null &&
                    orderInfoExtDTO.getLubricityQuantity().compareTo(BigDecimal.ZERO) > 0) {
                OrderItemDTO orderItemDTO = orderDTO.getOrderItems().get(0);
                OrderItemDTO lubricityItemDTO = new OrderItemDTO();
                BeanUtils.copyProperties(orderItemDTO, lubricityItemDTO);
                lubricityItemDTO.setGoodsName("润管砂浆");
                lubricityItemDTO.setOriginAmountPrice(orderInfoExtDTO.getLubricityPrice());
                lubricityItemDTO.setOriginUnitPrice(orderInfoExtDTO.getLubricityPrice());
                lubricityItemDTO.setActualUnitPrice(ArithUtils.add(orderInfoExtDTO.getLubricityPrice(), orderItemDTO.getAdditemPrice()));
                lubricityItemDTO.setActualAmountPrice(ArithUtils.add(orderInfoExtDTO.getLubricityPrice(), orderItemDTO.getAdditemPrice()));
                lubricityItemDTO.setItemQuantity(orderInfoExtDTO.getLubricityQuantity());
                lubricityItemDTO.setItemSendQuantity(orderInfoExtDTO.getLubricitySignQuantity());
                lubricityItemDTO.setItemSignQuantity(orderInfoExtDTO.getLubricitySignQuantity());
                orderDTO.getOrderItems().add(lubricityItemDTO);
            }
            orderDTO.setOrderInfoExtDTO(orderInfoExtDTO);
        });
    }

    @Override
    public OrderLogisticsResultDTO queryOrderLogisticsTakeInfo(OrderLogisticsQueryDTO orderLogisticsQueryDTO) {
        if (CsStringUtils.isBlank(orderLogisticsQueryDTO.getOrderId())) {
            throw new BizException(BasicCode.INVALID_PARAM, "订单ID");
        }
        OrderLogisticsResultDTO orderLogisticsResultDTO = new OrderLogisticsResultDTO();
        orderLogisticsResultDTO.setRealOrderAmount(BigDecimal.ZERO);
        orderLogisticsResultDTO.setRealGoodsAmount(BigDecimal.ZERO);
        orderLogisticsResultDTO.setRealLogisticsAmount(BigDecimal.ZERO);
        orderLogisticsResultDTO.setLogisticsItemList(Lists.newArrayList());
        //获取订单信息
        List<OrderItem> orderItemList = orderItemBiz.findByOrderId(orderLogisticsQueryDTO.getOrderId());
        List<String> goodsIds = orderItemList.stream().map(OrderItem::getGoodsId).toList();
        ItemResult<List<GoodsDTO>> goodsListItemResult = goodsService.selectSimpleGoodsInfoByIds(goodsIds);
        if (goodsListItemResult == null || !goodsListItemResult.isSuccess() || CollectionUtils.isEmpty(goodsListItemResult.getData())) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "获取订单商品信息失败");
        }
        Map<String, GoodsDTO> goodsMap = Maps.newHashMap();
        goodsListItemResult.getData().stream().forEach(goodsDTO -> goodsMap.put(goodsDTO.getGoodsId(), goodsDTO));
        //获取当前发货单信息
        List<TakeInfo> takeInfoList = takeInfoBiz.getTakeInfoByOrderId(orderLogisticsQueryDTO.getOrderId());
        if (CollectionUtils.isEmpty(takeInfoList)) {
            return orderLogisticsResultDTO;
        }
        List<String> takeCodeList = Lists.newArrayList();
        takeInfoList.stream().forEach(takeInfo -> takeCodeList.add(takeInfo.getTakeCode()));
        //获取物流信息
        ItemResult<List<ShipBillOrderDetailDTO>> logisticsItemResult = shipBillService.queryWaybillListByTakeCode(takeCodeList);
        if (logisticsItemResult == null || !logisticsItemResult.isSuccess()) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "获取订单物流信息失败");
        }
        log.info("queryWaybillListByTakeCode : {}", JSON.toJSONString(logisticsItemResult.getData()));
        if (CollectionUtils.isNotEmpty(logisticsItemResult.getData())) {
            logisticsItemResult.getData().forEach(shipBillOrderDetailDTO -> {
                OrderLogisticsItemDTO orderLogisticsItemDTO = new OrderLogisticsItemDTO();
                BeanUtils.copyProperties(shipBillOrderDetailDTO, orderLogisticsItemDTO);

                if (goodsMap.get(orderLogisticsItemDTO.getGoodsId()) != null) {
                    GoodsDTO goodsDTO = goodsMap.get(orderLogisticsItemDTO.getGoodsId());
                    orderLogisticsItemDTO.setGoodsName(goodsDTO.getGoodsName());
                    orderLogisticsItemDTO.setGoodsImg(
                            goodsDTO.getImgs() != null && goodsDTO.getImgs().length > 0 ? goodsDTO.getImgs()[0] : "");
                }
                //成交单价
                orderLogisticsItemDTO.setActualUnitPrice(ArithUtils.add(orderLogisticsItemDTO.getGoodsUnitPrice(),
                        orderLogisticsItemDTO.getLogisticsUnitPrice()));
                //商品金额
                orderLogisticsItemDTO.setGoodsAmount(ArithUtils.multiply(orderLogisticsItemDTO.getGoodsUnitPrice(),
                        orderLogisticsItemDTO.getLogisticsTakeQuantity()));
                orderLogisticsResultDTO.setRealGoodsAmount(ArithUtils.add(orderLogisticsResultDTO.getRealGoodsAmount(),
                        orderLogisticsItemDTO.getGoodsAmount()));
                //物流金额
                orderLogisticsItemDTO.setLogisticsAmount(ArithUtils.multiply(orderLogisticsItemDTO.getLogisticsUnitPrice(),
                        orderLogisticsItemDTO.getLogisticsTakeQuantity()));
                orderLogisticsResultDTO.setRealLogisticsAmount(ArithUtils.add(orderLogisticsResultDTO.getRealLogisticsAmount(),
                        orderLogisticsItemDTO.getLogisticsAmount()));
                //总金额
                orderLogisticsItemDTO.setTotalAmount(ArithUtils.add(orderLogisticsItemDTO.getGoodsAmount(),
                        orderLogisticsItemDTO.getLogisticsAmount()));
                orderLogisticsResultDTO.setRealOrderAmount(ArithUtils.add(orderLogisticsResultDTO.getRealOrderAmount(),
                        orderLogisticsItemDTO.getTotalAmount()));

                orderLogisticsResultDTO.getLogisticsItemList().add(orderLogisticsItemDTO);
            });
        }

        return orderLogisticsResultDTO;
    }


    @Override
    public OrderBillCheckDTO queryBillCheckInfo(OrderBillCheckQueryDTO query) {
        log.info("queryBillCheckInfo: {}", query);
        OrderBillCheckDTO result = new OrderBillCheckDTO();
        if (query == null || CsStringUtils.isBlank(query.getTakeCode()) || CsStringUtils.isBlank(query.getGoodsId())) {
            log.info("query is null.");
            return result;
        }
        long s = System.currentTimeMillis();
        //根据发货单号查询发货单信息
        TakeInfo takeInfo = takeInfoBiz.findByCode(query.getTakeCode());
        if (takeInfo == null) {
            return result;
        }
        TakeInfoDTO takeInfoDTO = new TakeInfoDTO();
        BeanUtils.copyProperties(takeInfo, takeInfoDTO);
        result.setTakeInfo(takeInfoDTO);

        OrderInfo orderInfo = orderInfoBiz.get(takeInfo.getOrderId());
        OrderDTO orderDTO = new OrderDTO();
        BeanUtils.copyProperties(orderInfo, orderDTO);
        result.setOrderInfo(orderDTO);

        if (BooleanUtils.isTrue(query.getReturnOrderInfoExt())) {
            //查询订单扩展属性
            orderDTO.setOrderInfoExtDTO(orderInfoExtBiz.findByOrderId(takeInfo.getOrderId()));
        }
        List<OrderItem> orderItemList = orderItemBiz.findByOrderId(takeInfo.getOrderId());
        OrderItem orderItem = orderItemList.stream().filter(item -> CsStringUtils.equals(query.getGoodsId(), item.getGoodsId())).filter(Objects::nonNull).findFirst().orElse(null);
        setOrderItems(query, orderItemList, orderDTO);

        if (orderItem != null) {
            //获取ERP运输路线
            CarriageRouteQueryDTO carriageRouteQueryDTO = new CarriageRouteQueryDTO();
            carriageRouteQueryDTO.setUserId(orderInfo.getSellerId());
            carriageRouteQueryDTO.setWarehouseId(orderItem.getStoreId());
            carriageRouteQueryDTO.setReceiveAddressId(orderInfo.getAddressId());
            carriageRouteQueryDTO.setTransportCategoryId(orderItem.getLogistics());
            TransportRouteDTO transportRouteDTO = carriageRouteService.getERPContractAddress(carriageRouteQueryDTO);
            if (transportRouteDTO != null && CsStringUtils.isNotBlank(transportRouteDTO.getContractAddressName())) {
                result.setContractAddressName(transportRouteDTO.getContractAddressName());
            }
        }

        log.info("queryBillCheckInfo: {} cost time {} ms", query, System.currentTimeMillis() - s);
        return result;
    }

    private void setOrderItems(OrderBillCheckQueryDTO query, List<OrderItem> orderItemList, OrderDTO orderDTO) {
        if (BooleanUtils.isTrue(query.getReturnOrderItem())) {
            //根据订单id查询订单子项
            Map<String, List<OrderItemAddDTO>> orderItemAddDTOMap = null;
            if (BooleanUtils.isTrue(query.getReturnOrderItemAdd())) {
                Set<String> orderItemIds = orderItemList.stream()
                        //根据查询条件过滤掉多余的商品信息,减少返回数据量
                        .filter(item -> CsStringUtils.equals(query.getGoodsId(), item.getGoodsId()))
                        .map(OrderItem::getOrderItemId)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toSet());
                //查询商品加价项信息 并按orderItemId分组
                List<OrderItemAdd> orderItemAddList = orderItemAddBiz.findByOrderItemIds(orderItemIds);
                if (CollectionUtils.isNotEmpty(orderItemAddList)) {
                    orderItemAddDTOMap = orderItemAddList.stream()
                            .map(item -> {
                                OrderItemAddDTO dto = new OrderItemAddDTO();
                                BeanUtils.copyProperties(item, dto);
                                return dto;
                            })
                            .collect(Collectors.groupingBy(OrderItemAddDTO::getOrderItemId));
                }
            }
            Map<String, List<OrderItemAddDTO>> finalOrderItemAddDTOMap = orderItemAddDTOMap == null ? Maps.newHashMap() : orderItemAddDTOMap;
            List<OrderItemDTO> orderItemDTOS = orderItemList.stream()
                    //根据查询条件过滤掉多余的商品信息,减少返回数据量
                    .filter(item -> CsStringUtils.equals(query.getGoodsId(), item.getGoodsId()))
                    .map(item -> {
                        OrderItemDTO dto = new OrderItemDTO();
                        BeanUtils.copyProperties(item, dto);
                        if (BooleanUtils.isTrue(query.getReturnOrderItemAdd())) {
                            dto.setOrderItemAdds(finalOrderItemAddDTOMap.get(item.getOrderItemId()));
                        }
                        return dto;
                    })
                    .toList();
            orderDTO.setOrderItems(orderItemDTOS);
        }
    }

    /**
     * OrderDTO中的BigDecimal属性处理: 为空或小于0时返回0,否则返回原值
     */
    public static void nullBigDecimalAsZero(OrderDTO order) {
        order.setOriginOrderAmount(nullAsZero(order.getOriginOrderAmount()));
        order.setOriginLogisticAmount(nullAsZero(order.getOriginLogisticAmount()));
        order.setOriginResourceAmount(nullAsZero(order.getOriginResourceAmount()));
        order.setOriginOthersAmount(nullAsZero(order.getOriginOthersAmount()));
        order.setActualOrderAmount(nullAsZero(order.getActualOrderAmount()));
        order.setActualLogisticAmount(nullAsZero(order.getActualLogisticAmount()));
        order.setActualResourceAmount(nullAsZero(order.getActualResourceAmount()));
        order.setActualOthersAmount(nullAsZero(order.getActualOthersAmount()));
        order.setRealtimeOrderAmount(nullAsZero(order.getRealtimeOrderAmount()));
        order.setRealtimeLogisticAmount(nullAsZero(order.getRealtimeLogisticAmount()));
        order.setRealtimeResourceAmount(nullAsZero(order.getRealtimeResourceAmount()));
        order.setRealtimeOthersAmount(nullAsZero(order.getRealtimeOthersAmount()));
        order.setOrderPayedAmount(nullAsZero(order.getOrderPayedAmount()));
        order.setRequestRefundAmount(nullAsZero(order.getRequestRefundAmount()));
        order.setRealRefundAmount(nullAsZero(order.getRealRefundAmount()));
        order.setSupplementAmount(nullAsZero(order.getSupplementAmount()));
        order.setSettlementAmount(nullAsZero(order.getSettlementAmount()));
        order.setOrderUnPayAmount(nullAsZero(order.getOrderUnPayAmount()));
        order.setShouldPayAmount(nullAsZero(order.getShouldPayAmount()));
        order.setMachineShiftCost(nullAsZero(order.getMachineShiftCost()));
        order.setTruckage(nullAsZero(order.getTruckage()));
        order.setOthersAmount(nullAsZero(order.getOthersAmount()));
        order.setTotalPreAdjustAmount(nullAsZero(order.getTotalPreAdjustAmount()));
    }

    /**
     * 为空或小于0时返回0,否则返回原值
     */
    public static BigDecimal nullAsZero(BigDecimal b1) {
        return b1 == null || b1.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : b1;
    }

    @Override
    public RepurchaseOrderDTO repurchaseOrder(String orderId) {
        OrderDTO orderDTO = getOrderInfo(orderId);
        log.info("repurchaseOrder_orderDTO:" + JSON.toJSONString(orderDTO));
        //基础数据检查
        checkParams(orderDTO);
        RepurchaseOrderDTO repurchaseOrderDTO = new RepurchaseOrderDTO();
        //设置扩展属性
        BeanUtils.copyProperties(orderDTO.getOrderInfoExtDTO(), repurchaseOrderDTO);
        doOrderTypeEnumContractLogic(orderDTO, repurchaseOrderDTO);
        repurchaseOrderDTO.setStoreId(orderDTO.getOrderItems().get(0).getStoreId());
        repurchaseOrderDTO.setDeliveryWay(orderDTO.getDeliverWay());
        repurchaseOrderDTO.setBuyerId(orderDTO.getBuyerId());
        repurchaseOrderDTO.setSellerId(orderDTO.getSellerId());
        repurchaseOrderDTO.setSellerName(orderDTO.getSellerName());
        repurchaseOrderDTO.setAddressId(orderDTO.getAddressId());
        repurchaseOrderDTO.setOrderType(orderDTO.getOrderType());
        repurchaseOrderDTO.setConcreteFlag(CollectionUtils.isNotEmpty(
                orderDTO.getOrderItems().get(0).getOrderItemAdds()) ? 1 : 0);
        List<RepurchaseGoodsDTO> goodsItemList = Lists.newArrayList();
        orderDTO.getOrderItems().stream().forEach(orderItemDTO -> {
            RepurchaseGoodsDTO repurchaseGoodsDTO = new RepurchaseGoodsDTO();
            repurchaseGoodsDTO.setGoodsId(orderItemDTO.getGoodsId());
            repurchaseGoodsDTO.setResourceId(orderItemDTO.getResourceId());
            repurchaseGoodsDTO.setQuantity(orderItemDTO.getItemQuantity());
            repurchaseGoodsDTO.setUnit(orderItemDTO.getUnits());
            //设置加价项参数
            if (CollectionUtils.isNotEmpty(orderItemDTO.getOrderItemAdds())) {
                List<CartAdditemDTO> cartAddItemList = Lists.newArrayList();
                orderItemDTO.getOrderItemAdds().stream().forEach(orderItemAddDTO -> {
                    CartAdditemDTO cartAdditemDTO = new CartAdditemDTO();
                    BeanUtils.copyProperties(orderItemAddDTO, cartAdditemDTO);
                    cartAddItemList.add(cartAdditemDTO);
                });
                repurchaseGoodsDTO.setCartAddItemList(cartAddItemList);
            }
            goodsItemList.add(repurchaseGoodsDTO);
        });
        repurchaseOrderDTO.setGoodsItemList(goodsItemList);

        log.info("repurchaseOrder_repurchaseOrderDTO:" + JSON.toJSONString(repurchaseOrderDTO));

        return repurchaseOrderDTO;
    }

    private void doOrderTypeEnumContractLogic(OrderDTO orderDTO, RepurchaseOrderDTO repurchaseOrderDTO) {
        if (CsStringUtils.equals(orderDTO.getOrderType(), com.ecommerce.order.fsm.status.OrderTypeEnum.CONTRACT.getCode())) {
            //获取下单合同的最新版本信息
            List<com.ecommerce.goods.api.dto.contract.TrContractDTO> contractList = contractService.findByContractSequence(orderDTO.getDealsSequence());
            log.info("repurchaseOrder_contractList:" + JSON.toJSONString(contractList));
            com.ecommerce.goods.api.dto.contract.TrContractDTO effectContract = getTrContractDTO(contractList);
            repurchaseOrderDTO.setContractId(effectContract.getContractId());
            orderItemsLogic(orderDTO, repurchaseOrderDTO);
        } else if (CsStringUtils.equals(orderDTO.getOrderType(), com.ecommerce.order.fsm.status.OrderTypeEnum.LISTING.getCode())) {
            orderDTO.getOrderItems().stream().forEach(orderItemDTO -> {
                ResourceDTO resource = resourceService.getResourceDetail(orderItemDTO.getResourceId());
                if (resource == null) {
                    throw new BizException(BasicCode.DB_DATA_NOT_FOUND, "订单商品:" + orderItemDTO.getResourceId());
                }
                if (!CsStringUtils.equals(resource.getTradeStatus(), TradeStatusEnum.TRADE_STATUS100.code())) {
                    throw new BizException(BasicCode.UNDEFINED_ERROR, "资源已失效或已下架！");
                }
                repurchaseOrderDTO.setMachineRuleId(resource.getMachineRuleId());
            });
        } else {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "不支持的订单类型！");
        }
    }

    private void orderItemsLogic(OrderDTO orderDTO, RepurchaseOrderDTO repurchaseOrderDTO) {
        orderDTO.getOrderItems().stream().forEach(orderItemDTO -> {
            TrContractGoodsDTO contractGoodsDTO = contractService.getContractGoodsById(orderItemDTO.getResourceId());
            if (contractGoodsDTO == null) {
                throw new BizException(BasicCode.DATA_NOT_EXIST, "合同商品:" + orderItemDTO.getContractGoodsId());
            }
            //验证品种是否存在
            if (CsStringUtils.isNotBlank(repurchaseOrderDTO.getVarietyId())) {
                ContractGoodsVarietyQueryDTO contractGoodsVarietyQueryDTO = new ContractGoodsVarietyQueryDTO();
                contractGoodsVarietyQueryDTO.setContractGoodsId(contractGoodsDTO.getContractGoodsId());
                contractGoodsVarietyQueryDTO.setVarietyId(repurchaseOrderDTO.getVarietyId());
                TrContractGoodsVarietyDTO varietyDTO = contractService.queryContractGoodsVariety(contractGoodsVarietyQueryDTO);
                if (varietyDTO == null || varietyDTO.getVarietyPrice() == null ||
                        varietyDTO.getVarietyPrice().compareTo(BigDecimal.ZERO) < 1) {
                    throw new BizException(BasicCode.DATA_NOT_EXIST, "合同商品未配置该品种价格:" + repurchaseOrderDTO.getVarietyId());
                }
            }
        });
    }

    private static com.ecommerce.goods.api.dto.contract.TrContractDTO getTrContractDTO(List<com.ecommerce.goods.api.dto.contract.TrContractDTO> contractList) {
        com.ecommerce.goods.api.dto.contract.TrContractDTO effectContract = null;
        for (com.ecommerce.goods.api.dto.contract.TrContractDTO trContractDTO : contractList) {
            if (CsStringUtils.equals(trContractDTO.getContractStatus(), ContractStatusEnum.INEFFECT.getCode()) &&
                    !trContractDTO.getIsHistory()) {
                effectContract = trContractDTO;
                break;
            }
        }
        if (effectContract == null) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "合同已失效");
        }
        return effectContract;
    }

    private void checkParams(OrderDTO orderDTO) {
        if (CsStringUtils.equals(orderDTO.getProxyOrderType(), ProxyOrderTypeEnum.PRIMARY.getCode())) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "背靠背一级订单不允许执行此操作！");
        }
        ReceivingAddressDTO receivingAddressDTO = receivingAddressService.findById(orderDTO.getAddressId());
        if (receivingAddressDTO == null) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "订单收货地址不存在或已失效！");
        }
        WarehouseDetailsDTO warehouseDetailsDTO = warehouseService.queryWarehouseDetails(orderDTO.getOrderItems().get(0).getStoreId());
        if (warehouseDetailsDTO == null) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "订单仓库不存在或已失效！");
        }
        SaleRegionDTO saleRegionDTO = saleRegionService.findById(orderDTO.getSaleRegionPath());
        if (saleRegionDTO == null) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "订单销售区域不存在或已失效！");
        }
    }

    @Override
    public List<String> queryAddressDetailByMemberId(OrderQueryDTO queryDTO) {

        return orderInfoBiz.queryAddressDetailByMemberId(queryDTO);
    }
}
