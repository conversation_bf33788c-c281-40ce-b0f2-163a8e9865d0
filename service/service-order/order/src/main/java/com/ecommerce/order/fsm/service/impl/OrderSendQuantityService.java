package com.ecommerce.order.fsm.service.impl;

import com.ecommerce.order.api.dto.OrderSendQuantityDTO;
import com.ecommerce.order.biz.fsm.IOrderSendQuantityInfoBiz;
import com.ecommerce.order.fsm.service.IOrderSendQuantityService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class OrderSendQuantityService implements IOrderSendQuantityService {

    private final IOrderSendQuantityInfoBiz orderSendQuantityInfoBiz;

    @Override
    public List<OrderSendQuantityDTO> findAveByQuertDTO(OrderSendQuantityDTO dto) {
        return orderSendQuantityInfoBiz.findAveByQuertDTO(dto);
    }
}
