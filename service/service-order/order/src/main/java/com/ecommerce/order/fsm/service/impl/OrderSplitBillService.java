package com.ecommerce.order.fsm.service.impl;

import com.alibaba.fastjson.JSON;
import com.ecommerce.base.api.dto.saleregion.SaleRegionDTO;
import com.ecommerce.base.api.service.ISaleRegionService;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.service.common.uuid.UUIDGenerator;
import com.ecommerce.common.utils.ArithUtils;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.logistics.api.enums.TransportToolTypeEnum;
import com.ecommerce.order.api.dto.BillSplitDTO;
import com.ecommerce.order.api.dto.OrderDTO;
import com.ecommerce.order.api.dto.SplitTriggerDTO;
import com.ecommerce.order.api.dto.WaybillSplitDTO;
import com.ecommerce.order.api.enums.*;
import com.ecommerce.order.biz.fsm.IOrderBillSplitBiz;
import com.ecommerce.order.biz.fsm.IOrderInfoBiz;
import com.ecommerce.order.biz.fsm.IOrderInfoExtBiz;
import com.ecommerce.order.biz.fsm.IOrderItemBiz;
import com.ecommerce.order.dao.dto.PaymentAmountDO;
import com.ecommerce.order.dao.dto.PaymentAmountQueryDO;
import com.ecommerce.order.dao.dto.SplitBillGeneratorDO;
import com.ecommerce.order.dao.vo.BillSplit;
import com.ecommerce.order.dao.vo.OrderInfo;
import com.ecommerce.order.dao.vo.OrderItem;
import com.ecommerce.order.fsm.service.IOrderPaymentService;
import com.ecommerce.order.fsm.service.IOrderQueryService;
import com.ecommerce.order.fsm.service.IOrderSplitBillService;
import com.ecommerce.order.fsm.status.OrderStatusEnum;
import com.ecommerce.pay.api.v2.dto.BillSplitInfoDTO;
import com.ecommerce.pay.api.v2.dto.ConfirmPayRequestDTO;
import com.ecommerce.pay.api.v2.dto.PaymentRequirementDetailDTO;
import com.ecommerce.pay.api.v2.dto.PaymentResponseDTO;
import com.ecommerce.pay.api.v2.enums.ChannelCodeEnum;
import com.ecommerce.pay.api.v2.service.IPaymentService;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 * Date: Create in 上午11:19 20/7/7
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderSplitBillService implements IOrderSplitBillService{

    private final UUIDGenerator uuidGenerator;

    private final IOrderBillSplitBiz orderBillSplitBiz;

    private final IOrderInfoBiz orderInfoBiz;
    private final IOrderInfoExtBiz orderInfoExtBiz;
    private final IOrderItemBiz orderItemBiz;

    private final IPaymentService paymentService;

    private final IOrderQueryService orderQueryService;

    private final ISaleRegionService saleRegionService;

    private final IOrderPaymentService orderPaymentService;

    /**
     * 运单分账参数验证
     * @param waybillSplitDTO 运单分账对象
     */
    private void waybillSplitParamValidate(WaybillSplitDTO waybillSplitDTO) {
        if (CsStringUtils.isBlank(waybillSplitDTO.getOrderId())) {
            throw new BizException(BasicCode.PARAM_NULL, "订单Id");
        }
        if (CollectionUtils.isEmpty(waybillSplitDTO.getSplitTriggerList())) {
            throw new BizException(BasicCode.PARAM_NULL, "分账明细列表");
        }
        for (SplitTriggerDTO splitTriggerDTO : waybillSplitDTO.getSplitTriggerList()) {
            if (splitTriggerDTO.getNum() == null) {
                throw new BizException(BasicCode.PARAM_NULL, "商品数量");
            }
            if (splitTriggerDTO.getLogisticsAmount() == null) {
                throw new BizException(BasicCode.PARAM_NULL, "物流费");
            }
            if (CsStringUtils.isBlank(splitTriggerDTO.getResourceId())) {
                throw new BizException(BasicCode.PARAM_NULL, "商品Id");
            }
        }
    }

    /**
     * 生成分账记录
     * @param splitBillGeneratorDO 分账参数对象
     * @param orderInfo 订单信息对象
     * @return 分账记录
     */
    private BillSplit generatorBillSpilt(SplitBillGeneratorDO splitBillGeneratorDO, OrderInfo orderInfo) {
        BillSplit billSplit = new BillSplit();
        billSplit.setOrderId(orderInfo.getOrderId());
        billSplit.setBuyerId(orderInfo.getBuyerId());
        billSplit.setSellerId(orderInfo.getSellerId());
        billSplit.setCurrency(orderInfo.getCurrency());
        billSplit.setBillSplitType(splitBillGeneratorDO.getSplitType());
        billSplit.setAmountType(splitBillGeneratorDO.getAmountType());
        billSplit.setMemberId(splitBillGeneratorDO.getMemberId());
        billSplit.setResourceId(splitBillGeneratorDO.getResourceId());
        //剩余可分账金额 = 支付金额 - 分账金额
        BigDecimal remainSplitAmount = ArithUtils.subtract(false, splitBillGeneratorDO.getMaxPaymentAmount(), splitBillGeneratorDO.getHadSplitAmount());
        BigDecimal splitAmount = splitBillGeneratorDO.getSplitAmount();
        //分账金额不能超过剩余未分账金额
        if (remainSplitAmount.compareTo(splitAmount) < 0) {
            splitAmount = remainSplitAmount;
        }
        //分账金额
        billSplit.setSplitAmount(splitAmount);

        //返回分账对象
        return splitAmount.compareTo(BigDecimal.ZERO) > 0 ? billSplit : null;
    }

    /**
     * 计算分账货款
     * @param splitQuantity 商品数量
     * @param orderItemId 订单明细Id
     * @param actualUnitPrice   资源实际单价
     * @return 分账金额
     */
    private BigDecimal computeGoodsSplitAmount(BigDecimal splitQuantity, String orderItemId,BigDecimal actualUnitPrice) {
        OrderItem orderItem = orderItemBiz.get(orderItemId);
        if (orderItem == null) {
            throw new BizException(BasicCode.DATA_NOT_EXIST, "订单明细");
        }
        //计算分账货款
        if (CsStringUtils.isNullOrBlank(orderItem.getUnconvertRate())) {
            orderItem.setUnconvertRate(BigDecimal.ONE);
        }
        if (splitQuantity == null || splitQuantity.compareTo(BigDecimal.ZERO) < 0) {
            splitQuantity = BigDecimal.ZERO;
        }
        actualUnitPrice = actualUnitPrice != null ? actualUnitPrice : orderItem.getActualUnitPrice();
        if (actualUnitPrice == null || actualUnitPrice.compareTo(BigDecimal.ZERO) < 0) {
            actualUnitPrice = BigDecimal.ZERO;
        }
        log.info("computeGoodsSplitAmount actualUnitPrice:{}",actualUnitPrice);

        return splitQuantity.multiply(actualUnitPrice).multiply(orderItem.getUnconvertRate()).setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 处理订单分账记录
     * @param splitTrigger 分账对象
     * @param orderInfo 订单信息
     * @param splitType 分账类型
     * @return List<BillSplit>
     */
    public List<BillSplit> handleOrderSplitBill(SplitTriggerDTO splitTrigger, OrderInfo orderInfo, String splitType) {
        log.info("处理分账信息:"+ JSON.toJSONString(splitTrigger));
        //计算当前订单的已支付金额
        PaymentAmountQueryDO paymentAmountQueryDO = new PaymentAmountQueryDO();
        paymentAmountQueryDO.setOrderId(orderInfo.getOrderId());
        paymentAmountQueryDO.setPayWay(ChannelCodeEnum.PINGANJZ.getCode());//computeOrderPaymentAmount内部已修改
        paymentAmountQueryDO.setPayStatusList(Lists.newArrayList(PayStatusEnum.COMPLETED.code()));
        PaymentAmountDO paymentAmountDO = orderPaymentService.computeOrderPaymentAmount(paymentAmountQueryDO);
        if (paymentAmountDO.getPaymentTotalAmount().compareTo(BigDecimal.ZERO) == 0) {
            log.info("没有需要分账的支付金额paymentAmountDO:" + JSON.toJSONString(paymentAmountDO));
            return Lists.newArrayList();
        }
        //查询已分帐成功的记录
        List<BillSplitDTO> billSplitSuccessList = orderBillSplitBiz.querySplitList(
                orderInfo.getOrderId(), BillSplitStatusEnum.BILL_SPLIT_SUCCESS.getCode(), splitType);
        //计算已经分账的记录
        BigDecimal hadLogisticSplitAmount = BigDecimal.ZERO;
        BigDecimal hadGoodsSplitAmount = BigDecimal.ZERO;
        BigDecimal hadOtherSplitAmount = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(billSplitSuccessList)) {
            for (BillSplitDTO billSplitDTO : billSplitSuccessList) {
                if (billSplitDTO.getAmountType().equals(BillSplitAmountTypeEnum.LOGISTIC_AMOUNT.getCode())) {
                    hadLogisticSplitAmount = ArithUtils.add(hadLogisticSplitAmount, billSplitDTO.getSplitAmount());
                }
                if (billSplitDTO.getAmountType().equals(BillSplitAmountTypeEnum.GOODS_AMOUNT.getCode())) {
                    hadGoodsSplitAmount = ArithUtils.add(hadGoodsSplitAmount, billSplitDTO.getSplitAmount());
                }
                if (billSplitDTO.getAmountType().equals(BillSplitAmountTypeEnum.OTHER_AMOUNT.getCode())) {
                    hadOtherSplitAmount = ArithUtils.add(hadOtherSplitAmount, billSplitDTO.getSplitAmount());
                }
            }
        }

        List<BillSplit> billSplitList = new ArrayList<>();
        SplitBillGeneratorDO splitBillGeneratorDO = new SplitBillGeneratorDO();
        splitBillGeneratorDO.setSplitType(splitType);
        splitBillGeneratorDO.setResourceId(splitTrigger.getResourceId());
        //生成物流费分账记录
        splitBillGeneratorDO.setMemberId(paymentAmountDO.getPayeeMap().get(PayDetailTypeEnum.LOGISTICS.code()));
        splitBillGeneratorDO.setAmountType(BillSplitAmountTypeEnum.LOGISTIC_AMOUNT.getCode());
        splitBillGeneratorDO.setMaxPaymentAmount(paymentAmountDO.getPaymentLogisticsAmount());
        splitBillGeneratorDO.setHadSplitAmount(hadLogisticSplitAmount);
        splitBillGeneratorDO.setSplitAmount(splitTrigger.getLogisticsAmount());
        BillSplit logisticSplit = generatorBillSpilt(splitBillGeneratorDO, orderInfo);
        if (logisticSplit != null) {
            billSplitList.add(logisticSplit);
        }
        //生成商品费分账记录
        splitBillGeneratorDO.setMemberId(paymentAmountDO.getPayeeMap().get(PayDetailTypeEnum.GOODS.code()));
        splitBillGeneratorDO.setAmountType(BillSplitAmountTypeEnum.GOODS_AMOUNT.getCode());
        splitBillGeneratorDO.setMaxPaymentAmount(paymentAmountDO.getPaymentGoodsAmount());
        splitBillGeneratorDO.setHadSplitAmount(hadGoodsSplitAmount);
        //生成商品分账费用
        splitBillGeneratorDO.setSplitAmount(splitTrigger.getGoodsAmount());
        BillSplit goodsSplit = generatorBillSpilt(splitBillGeneratorDO, orderInfo);
        if (goodsSplit != null) {
            billSplitList.add(goodsSplit);
        }
        //订单结束时完成其他金额的分账
        Set<String> finalOrderStatusSet = Sets.newHashSet(
                OrderStatusEnum.CLOSED.getCode(),
                OrderStatusEnum.COMPLETED.getCode()
        );
        setOther(orderInfo, finalOrderStatusSet, splitBillGeneratorDO, paymentAmountDO, hadOtherSplitAmount, billSplitList);

        return billSplitList;
    }

    private void setOther(OrderInfo orderInfo, Set<String> finalOrderStatusSet, SplitBillGeneratorDO splitBillGeneratorDO, PaymentAmountDO paymentAmountDO, BigDecimal hadOtherSplitAmount, List<BillSplit> billSplitList) {
        if (finalOrderStatusSet.contains(orderInfo.getOrderStatus())) {
            //生成其他费分账记录
            splitBillGeneratorDO.setMemberId(paymentAmountDO.getPayeeMap().get(PayDetailTypeEnum.OTHER.code()));
            splitBillGeneratorDO.setAmountType(BillSplitAmountTypeEnum.OTHER_AMOUNT.getCode());
            splitBillGeneratorDO.setMaxPaymentAmount(paymentAmountDO.getPaymentOtherAmount());
            splitBillGeneratorDO.setHadSplitAmount(hadOtherSplitAmount);
            splitBillGeneratorDO.setSplitAmount(ArithUtils.subtract(paymentAmountDO.getPaymentOtherAmount(), hadOtherSplitAmount));
            BillSplit otherSpilt = generatorBillSpilt(splitBillGeneratorDO, orderInfo);
            if (otherSpilt != null) {
                billSplitList.add(otherSpilt);
            }
        }
    }

    /**
     * 执行分账
     * @param paymentDetailList 分账支付明细列表
     * @param orderId 订单ID
     * @param requestNum 请求号
     * @param waybillNum 运单号
     * @return 执行结果
     */
    private boolean splitBillExecute(List<PaymentRequirementDetailDTO> paymentDetailList,
                                     String orderId,
                                     String requestNum,
                                     String waybillNum) {
        if (CollectionUtils.isEmpty(paymentDetailList)) return false;

        ConfirmPayRequestDTO confirmPayRequestDTO = new ConfirmPayRequestDTO();
        confirmPayRequestDTO.setPaymentBillDetailDTOList(paymentDetailList);
        OrderDTO orderDTO = orderQueryService.getOrderInfo(orderId);
        //请求号
        confirmPayRequestDTO.setOrderNo(orderDTO.getOrderCode());
        confirmPayRequestDTO.setRequestId(requestNum);
        if (CsStringUtils.isNotBlank(waybillNum)) {
            confirmPayRequestDTO.setWaybillNum(waybillNum);
        }
        if(TransportToolTypeEnum.WATER_TRANSPORT.getCode().equals(orderDTO.getOrderInfoExtDTO().getTransportType())){
            confirmPayRequestDTO.setOnlyCheckTotalAmount(true);
        }
        //获取销售区域组织编码
        if (CsStringUtils.isEmpty(orderDTO.getSaleRegionPath())) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "分账失败 销售区域代码为空");
        }
        SaleRegionDTO saleRegionDTO = saleRegionService.findById(orderDTO.getSaleRegionPath());
        if (saleRegionDTO == null) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "获取销售区域失败");
        }
        confirmPayRequestDTO.setOrgCode(saleRegionDTO.getErpCode());
        //设置分账明细
        List<BillSplitInfoDTO> billSplitInfoList = orderDTO.getOrderItems().stream().map(orderItem -> {
            BillSplitInfoDTO billSplitInfoDTO = new BillSplitInfoDTO();
            billSplitInfoDTO.setGoodsName(orderItem.getGoodsName());
            billSplitInfoDTO.setItemSendQuantity(orderItem.getItemSendQuantity());
            billSplitInfoDTO.setUnitName(orderItem.getUnitsName());
            billSplitInfoDTO.setGoodsId(orderItem.getGoodsId());
            return billSplitInfoDTO;
        }).toList();
        confirmPayRequestDTO.setBillSplitInfoList(billSplitInfoList);
        //调用分账接口
        PaymentResponseDTO responseDTO = null;
        try {
            log.info("====billSplitPay====->{}", JSON.toJSONString(confirmPayRequestDTO));
            responseDTO = paymentService.confirmPay(confirmPayRequestDTO);
            log.info("====billSplitPay====->{}", JSON.toJSONString(responseDTO));
            return responseDTO.isSuccess();
        } catch (Exception e) {
            log.info("====billSplitPay====->responseDTO: {}", responseDTO == null ? "" : JSON.toJSONString(responseDTO));
            log.error("====billSplitPay==error==->{}",e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void triggerSplitByWaybill(WaybillSplitDTO waybillSplitDTO) {
        log.info("triggerSplitByWaybill:" + JSON.toJSONString(waybillSplitDTO));
        //参数验证
        waybillSplitParamValidate(waybillSplitDTO);
        OrderInfo orderInfo = orderInfoBiz.getOrderInfoById(waybillSplitDTO.getOrderId());
        //计算分账金额
        List<PaymentRequirementDetailDTO> paymentBillDetailList = Lists.newArrayList();
        String requestNum = uuidGenerator.gain();
        List<BillSplit> totalBillSplitList = Lists.newArrayList();
        waybillSplitDTO.getSplitTriggerList().stream().forEach(splitTrigger -> {
            //计算商品分账金额
            //此价格为物流传过来的价格，无需做转换

            //处理订单分账记录
            List<BillSplit> billSplitList = handleOrderSplitBill(splitTrigger, orderInfo, BillSplitTypeEnum.CAR_SPLIT.getCode());
            if (CollectionUtils.isEmpty(billSplitList)) return;
            billSplitList.stream().forEach(billSplit -> {
                billSplit.setRequestNum(requestNum);
                PaymentRequirementDetailDTO paymentRequirementDetailDTO = new PaymentRequirementDetailDTO();
                paymentRequirementDetailDTO.setPayAmount(billSplit.getSplitAmount().setScale(2, RoundingMode.HALF_UP).toString());
                paymentRequirementDetailDTO.setPayeeMemberId(billSplit.getMemberId());
                BillSplitAmountTypeEnum splitAmountTypeEnum = BillSplitAmountTypeEnum.getByCode(billSplit.getAmountType());
                paymentRequirementDetailDTO.setSubject(splitAmountTypeEnum == null ? "" : splitAmountTypeEnum.getMessage());
                paymentBillDetailList.add(paymentRequirementDetailDTO);
            });
            totalBillSplitList.addAll(billSplitList);
        });
        log.info("分账记录：" + JSON.toJSONString(totalBillSplitList));
        //执行交易分账
        Boolean executeResult = splitBillExecute(paymentBillDetailList, orderInfo.getOrderId(),
                requestNum, waybillSplitDTO.getWaybillNum());
        BillSplitStatusEnum billSplitStatusEnum = executeResult ?
                BillSplitStatusEnum.BILL_SPLIT_SUCCESS : BillSplitStatusEnum.BILL_SPLIT_ERROR;
        //保存分账记录
        orderBillSplitBiz.batchSaveBillSplit(totalBillSplitList, billSplitStatusEnum.getCode());
    }

    /**
     * 触发订单分账
     * @param orderId 订单ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void triggerSplitByOrder(String orderId) {
        log.info("triggerSplitByWaybill:" + orderId);
        OrderInfo orderInfo = orderInfoBiz.getOrderInfoById(orderId);
        if (orderInfo.getRealtimeOrderAmount() == null || orderInfo.getRealtimeOrderAmount().compareTo(BigDecimal.ZERO) < 1) {
            return;
        }

        SplitTriggerDTO splitTrigger = getSplitTrigger(orderInfo);//执行金额 - 已分账金 = 待分账金额
        log.info("=============>2 {}", JSON.toJSONString(splitTrigger));
        if( splitTrigger == null ){
            return;
        }
        // 2021.8.25 运单已经按车分账，已经有分账金额了，此处如果按订单分账去查询，已分账的金额会被忽略，所以分账类型应该为空
        //处理订单分账记录
        List<BillSplit> billSplitList = handleOrderSplitBill(splitTrigger, orderInfo, null);
        log.info("=============>3 {}", JSON.toJSONString(billSplitList));
        if (CollectionUtils.isEmpty(billSplitList)){
            return;
        }
        //计算分账金额
        String requestNum = uuidGenerator.gain();
        List<PaymentRequirementDetailDTO> paymentBillDetailList = Lists.newArrayList();
        billSplitList.stream().forEach(billSplit -> {
            billSplit.setRequestNum(requestNum);
            PaymentRequirementDetailDTO paymentRequirementDetailDTO = new PaymentRequirementDetailDTO();
            paymentRequirementDetailDTO.setPayAmount(billSplit.getSplitAmount().setScale(2, RoundingMode.HALF_UP).toString());
            paymentRequirementDetailDTO.setPayeeMemberId(billSplit.getMemberId());
            BillSplitAmountTypeEnum splitAmountTypeEnum = BillSplitAmountTypeEnum.getByCode(billSplit.getAmountType());
            paymentRequirementDetailDTO.setSubject(splitAmountTypeEnum == null ? "" : splitAmountTypeEnum.getMessage());
            paymentBillDetailList.add(paymentRequirementDetailDTO);
        });
        log.info("分账记录：" + JSON.toJSONString(billSplitList));
        //执行交易分账
        Boolean executeResult = splitBillExecute(paymentBillDetailList, orderInfo.getOrderId(),
                requestNum, "");
        BillSplitStatusEnum billSplitStatusEnum = executeResult ?
                BillSplitStatusEnum.BILL_SPLIT_SUCCESS : BillSplitStatusEnum.BILL_SPLIT_ERROR;
        //保存分账记录
        orderBillSplitBiz.batchSaveBillSplit(billSplitList, billSplitStatusEnum.getCode());
    }

    private SplitTriggerDTO getSplitTrigger(OrderInfo orderInfo){
        // 查询退货量
        List<OrderItem> orderItems = orderItemBiz.findByOrderId(orderInfo.getOrderId());
        BigDecimal itemRefundAmount = BigDecimal.ZERO;
        itemRefundAmount = getBigDecimal(orderItems, itemRefundAmount);

        log.info("=============> {},{}", itemRefundAmount, JSON.toJSONString(orderItems));


        SplitTriggerDTO splitTrigger = new SplitTriggerDTO();
        splitTrigger.setResourceId(orderInfo.getOrderId());
        //订单执行金额
        splitTrigger.setLogisticsAmount(orderInfo.getRealtimeLogisticAmount() == null ?
                BigDecimal.ZERO : orderInfo.getRealtimeLogisticAmount());
        splitTrigger.setGoodsAmount(orderInfo.getRealtimeResourceAmount() == null ?
                BigDecimal.ZERO : orderInfo.getRealtimeResourceAmount().subtract(itemRefundAmount));

        //如果是船运
        //可分账金额 钱包支付金额
        PaymentAmountQueryDO paymentAmountQueryDO = new PaymentAmountQueryDO();
        paymentAmountQueryDO.setOrderId(orderInfo.getOrderId());
        paymentAmountQueryDO.setPayWay(ChannelCodeEnum.PINGANJZ.getCode());//computeOrderPaymentAmount内部已修改
        paymentAmountQueryDO.setPayStatusList(Lists.newArrayList(PayStatusEnum.COMPLETED.code()));
        PaymentAmountDO paymentAmountDO = orderPaymentService.computeOrderPaymentAmount(paymentAmountQueryDO);//查询支付金额
        //没有可分账的金额
        if( paymentAmountDO.getPaymentTotalAmount().compareTo(BigDecimal.ZERO) <=0){
            return null;
        }
        //查询已分帐成功的记录
        List<BillSplitDTO> billSplitSuccessList = orderBillSplitBiz.querySplitList(
                orderInfo.getOrderId(), BillSplitStatusEnum.BILL_SPLIT_SUCCESS.getCode(), null);//查询分账金额
        //计算已经分账金额 已用金额
        //计算已经分账的记录
        BigDecimal splitAmount = BigDecimal.ZERO;
        BigDecimal hadLogisticSplitAmount = BigDecimal.ZERO;
        BigDecimal hadGoodsSplitAmount = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(billSplitSuccessList)) {
            for (BillSplitDTO billSplitDTO : billSplitSuccessList) {
                splitAmount = ArithUtils.add(splitAmount, billSplitDTO.getSplitAmount());
                if (billSplitDTO.getAmountType().equals(BillSplitAmountTypeEnum.LOGISTIC_AMOUNT.getCode())) {
                    hadLogisticSplitAmount = ArithUtils.add(hadLogisticSplitAmount, billSplitDTO.getSplitAmount());
                }
                if (billSplitDTO.getAmountType().equals(BillSplitAmountTypeEnum.GOODS_AMOUNT.getCode())) {
                    hadGoodsSplitAmount = ArithUtils.add(hadGoodsSplitAmount, billSplitDTO.getSplitAmount());
                }
            }
        }
        //订单已执行金额
        BigDecimal realtimeOrderAmount = OrderQueryService.nullAsZero(orderInfo.getRealtimeOrderAmount());
        //已全部分账
        if( splitAmount.compareTo(paymentAmountDO.getPaymentTotalAmount()) >= 0 || splitAmount.compareTo(realtimeOrderAmount) >= 0 ){
            return null;
        }
        //一般不会执行到下面
        //                ---3-------8-已执行订单金额
        //                ---3-------8-------15------------30已执行订单金额
        //                ---3-------8-------15已支付钱包金额
        //                ---3已分账
        //                    -------剩余可分账金额 = (已执行订单金额,已支付钱包金额)取小的个 - 已分账金额
        //最多可分账金额 = 钱包支付金额 - 已分账金额
        //待分账金额 = min(钱包支付金额，订单执行金额) -
        //待分账金额 = (已执行订单金额,已支付钱包金额)取小的个 - 已分账金额
        BigDecimal waitSplitAmount = min(paymentAmountDO.getPaymentTotalAmount(),realtimeOrderAmount).subtract(splitAmount);
        BigDecimal waitSplitGoodsAmount = ArithUtils.subtract(true,min(paymentAmountDO.getPaymentGoodsAmount(),orderInfo.getRealtimeResourceAmount()),hadGoodsSplitAmount);
        BigDecimal waitSplitLogisticsAmount = waitSplitAmount.subtract(waitSplitGoodsAmount);
        log.info("waitSplitAmount:{} = min(paymentAmountDO.getPaymentTotalAmount():{},realtimeOrderAmount:{}) - splitAmount:{}",
                waitSplitAmount,paymentAmountDO.getPaymentTotalAmount(),realtimeOrderAmount,splitAmount);
        log.info("waitSplitGoodsAmount:{} = ArithUtils.subtract(isZero:true,min(paymentAmountDO.getPaymentGoodsAmount():{},orderInfo.getRealtimeResourceAmount():{}) - hadGoodsSplitAmount:{}",
                waitSplitGoodsAmount,paymentAmountDO.getPaymentGoodsAmount(),orderInfo.getRealtimeResourceAmount(),hadGoodsSplitAmount);
        log.info("waitSplitLogisticsAmount:{} = waitSplitAmount:{} - waitSplitGoodsAmount:{}",waitSplitAmount,waitSplitGoodsAmount);

        splitTrigger.setGoodsAmount(waitSplitGoodsAmount);
        splitTrigger.setLogisticsAmount(waitSplitLogisticsAmount);
        return splitTrigger;
    }

    private static BigDecimal getBigDecimal(List<OrderItem> orderItems, BigDecimal itemRefundAmount) {
        for(OrderItem item : orderItems)
        {
            if(item.getItemRefundAmount() == null)
            {
                continue;
            }

            itemRefundAmount = itemRefundAmount.add(item.getItemRefundAmount());
        }
        return itemRefundAmount;
    }

    private BigDecimal min(BigDecimal a,BigDecimal b){
        a = OrderQueryService.nullAsZero(a);
        b = OrderQueryService.nullAsZero(b);
        return a.compareTo(b) > 0 ? b : a;
    }
}
