package com.ecommerce.order.fsm.service.impl;

import com.alibaba.fastjson.JSON;
import com.ecommerce.base.api.dto.ReceivingAddressMapDTO;
import com.ecommerce.base.api.dto.ReceivingAddressMapQueryDTO;
import com.ecommerce.base.api.dto.saleregion.RegionQueryDTO;
import com.ecommerce.base.api.dto.saleregion.SaleRegionCondDTO;
import com.ecommerce.base.api.dto.saleregion.SaleRegionSampleDTO;
import com.ecommerce.base.api.dto.warehouse.WarehouseBaseDataDTO;
import com.ecommerce.base.api.service.IReceivingAddressService;
import com.ecommerce.base.api.service.ISaleRegionService;
import com.ecommerce.base.api.service.IWarehouseService;
import com.ecommerce.common.annotation.SetThreadName;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.exception.DistributeLockException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.common.service.common.BaseBiz;
import com.ecommerce.common.service.lock.RedisLockService;
import com.ecommerce.common.utils.ArithUtils;
import com.ecommerce.common.utils.BeanConvertUtils;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.common.utils.DateUtil;
import com.ecommerce.goods.api.dto.GoodsDTO;
import com.ecommerce.goods.api.dto.contract.TrContractDTO;
import com.ecommerce.goods.api.dto.contract.proxy.PrimaryContractGoodsDTO;
import com.ecommerce.goods.api.dto.contract.proxy.PrimaryContractOptionDTO;
import com.ecommerce.goods.api.dto.contract.proxy.ProxyContractQueryDTO;
import com.ecommerce.goods.api.service.IContractService;
import com.ecommerce.goods.api.service.IGoodsService;
import com.ecommerce.logistics.api.dto.rulecompute.CarriageComputeDTO;
import com.ecommerce.logistics.api.dto.rulecompute.CarriageRuleResultDTO;
import com.ecommerce.logistics.api.dto.rulecompute.CategoryQuantityDTO;
import com.ecommerce.logistics.api.dto.shippingrule.QueryShippingRuleDTO;
import com.ecommerce.logistics.api.dto.shippingrule.ShippingRuleResultDTO;
import com.ecommerce.logistics.api.enums.PickingBillTypeEnum;
import com.ecommerce.logistics.api.enums.TransportToolTypeEnum;
import com.ecommerce.logistics.api.service.IRuleComputeService;
import com.ecommerce.logistics.api.service.IShippingRouteService;
import com.ecommerce.open.api.dto.MessageConfigCodeEnum;
import com.ecommerce.order.api.dto.ERPAddressDTO;
import com.ecommerce.order.api.dto.OrderDTO;
import com.ecommerce.order.api.dto.OrderInfoExtDTO;
import com.ecommerce.order.api.dto.OrderItemDTO;
import com.ecommerce.order.api.dto.OrderPayinfoDTO;
import com.ecommerce.order.api.dto.base.PageQuery;
import com.ecommerce.order.api.dto.base.SendSMSMessageDTO;
import com.ecommerce.order.api.dto.proxy.BatchPayPrimaryOrderDTO;
import com.ecommerce.order.api.dto.proxy.BuyerSelectorDTO;
import com.ecommerce.order.api.dto.proxy.GoodsSelectorDTO;
import com.ecommerce.order.api.dto.proxy.OrderMapResultDTO;
import com.ecommerce.order.api.dto.proxy.OrderMapResultQueryDTO;
import com.ecommerce.order.api.dto.proxy.OrderProxyMapDTO;
import com.ecommerce.order.api.dto.proxy.OrderSubmitQueryDTO;
import com.ecommerce.order.api.dto.proxy.OrderSubmitResultDTO;
import com.ecommerce.order.api.dto.proxy.PrimaryContractGoodsMapDTO;
import com.ecommerce.order.api.dto.proxy.PrimaryContractOptionMapDTO;
import com.ecommerce.order.api.dto.proxy.PrimaryOrderCreateDTO;
import com.ecommerce.order.api.dto.proxy.PrimaryOrderItemDTO;
import com.ecommerce.order.api.dto.proxy.PrimaryOrderListDTO;
import com.ecommerce.order.api.dto.proxy.PrimaryOrderQueryDTO;
import com.ecommerce.order.api.dto.proxy.RetrySubmitPrimaryOrderDTO;
import com.ecommerce.order.api.dto.proxy.SecondaryOrderInfoDTO;
import com.ecommerce.order.api.dto.proxy.SecondaryOrderListDTO;
import com.ecommerce.order.api.dto.proxy.SecondaryOrderQueryDTO;
import com.ecommerce.order.api.dto.proxy.StoreSelectorDTO;
import com.ecommerce.order.api.dto.proxy.SubmitPrimaryOrderDTO;
import com.ecommerce.order.api.enums.OrderStatusEnum;
import com.ecommerce.order.api.enums.PayStatusEnum;
import com.ecommerce.order.api.enums.PayTypeEnum;
import com.ecommerce.order.api.enums.PayWayEnum;
import com.ecommerce.order.api.enums.PrimaryMapStatusEnum;
import com.ecommerce.order.api.enums.ProxyOrderTypeEnum;
import com.ecommerce.order.biz.IOrderErpAddressBiz;
import com.ecommerce.order.biz.IOrderMapTaskBiz;
import com.ecommerce.order.biz.IOrderProxyMapBiz;
import com.ecommerce.order.biz.fsm.IOrderInfoBiz;
import com.ecommerce.order.biz.fsm.IOrderInfoExtBiz;
import com.ecommerce.order.biz.fsm.IOrderItemBiz;
import com.ecommerce.order.biz.fsm.IOrderPayinfoBiz;
import com.ecommerce.order.biz.fsm.ITakeInfoBiz;
import com.ecommerce.order.dao.dto.CarriageRuleValidateDO;
import com.ecommerce.order.dao.dto.OrderSubmitResultDO;
import com.ecommerce.order.dao.dto.SecondaryOrderListDO;
import com.ecommerce.order.dao.vo.OrderInfo;
import com.ecommerce.order.dao.vo.OrderItem;
import com.ecommerce.order.dao.vo.OrderMapTask;
import com.ecommerce.order.dao.vo.OrderPayinfo;
import com.ecommerce.order.dao.vo.OrderProxyMap;
import com.ecommerce.order.dao.vo.TakeInfo;
import com.ecommerce.order.exception.OrderErrorCode;
import com.ecommerce.order.fsm.service.IOrderQueryService;
import com.ecommerce.order.fsm.service.IProxyOrderService;
import com.ecommerce.order.fsm.service.ITakeInfoService;
import com.ecommerce.order.fsm.service.OrderServiceFactory;
import com.ecommerce.order.fsm.status.OrderTypeEnum;
import com.ecommerce.order.service.message.MessageQueueService;
import com.ecommerce.order.service.message.SMSMessageFactoryContext;
import com.ecommerce.pay.api.v2.dto.PaymentRequestDTO;
import com.ecommerce.pay.api.v2.dto.PaymentResponseDTO;
import com.ecommerce.pay.api.v2.enums.ChannelCodeEnum;
import com.ecommerce.pay.api.v2.enums.ClientType;
import com.ecommerce.pay.api.v2.service.IPaymentService;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Condition;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.TreeSet;
import java.util.stream.Collectors;

import static com.github.pagehelper.page.PageMethod.startPage;

/**
 *
 * <AUTHOR>
 * Date: Create in 下午3:26 20/8/4
 */
@Slf4j
@Service
public class ProxyOrderService implements IProxyOrderService {

    public static final String YYYY_MM = "yyyyMM";
    public static final String RESULT_COMMENT = "一级订单处理成功";
    public static final String LOG_ERROR_SECONDARY_ORDER = "二级订单号:{},一级订单自动支付失败:{}";
    public static final String UNDEFINED_ERROR_REF_GOODS_ID_BLANK_MSG = "商品查找不到和厂家商品的映射关系";
    public static final String UNDEFINED_ERROR_GOODS_IS_NULL_MSG = "查询商品信息出错";
    @Value("${spring.cloud.config.profile:dev}")
    private String profile;

    @Autowired
    private IOrderInfoBiz orderInfoBiz;
    @Autowired
    private IOrderItemBiz orderItemBiz;
    @Autowired
    private IOrderInfoExtBiz orderInfoExtBiz;
    @Autowired
    private IOrderPayinfoBiz orderPayInfoBiz;
    @Autowired
    private IOrderPayinfoBiz orderPayinfoBiz;
    @Autowired
    private IOrderMapTaskBiz orderMapTaskBiz;
    @Autowired
    private IOrderProxyMapBiz orderProxyMapBiz;
    @Autowired
    private ITakeInfoBiz takeInfoBiz;
    @Autowired
    private IOrderErpAddressBiz orderErpAddressBiz;
    @Autowired
    private IContractService contractService;
    @Autowired
    private ISaleRegionService saleRegionService;
    @Autowired
    private IGoodsService goodsService;
    @Autowired
    private IRuleComputeService ruleComputeService;
    @Autowired
    private RedisLockService redisLockService;
    @Autowired
    private IOrderQueryService orderQueryService;
    @Autowired
    private ITakeInfoService takeInfoService;
    @Autowired
    private IPaymentService paymentService;
    @Autowired
    private MessageQueueService messageService;
    @Autowired
    private SMSMessageFactoryContext smsMessageFactoryContext;
    @Autowired
    private IShippingRouteService shippingRouteService;
    @Autowired
    private IWarehouseService warehouseService;
    @Autowired
    private IProxyOrderService proxyOrderService;
    @Autowired
    private IReceivingAddressService receivingAddressService;

    public boolean validateVehicleCarriageRule(CarriageRuleValidateDO carriageRuleValidateDO) {
        log.info("validateCarriageRule:" + JSON.toJSONString(carriageRuleValidateDO));
        CarriageComputeDTO carriageDTO = new CarriageComputeDTO();
        BeanUtils.copyProperties(carriageRuleValidateDO, carriageDTO);
        CategoryQuantityDTO quantityDTO = new CategoryQuantityDTO();
        quantityDTO.setTransportCategoryId(carriageRuleValidateDO.getTransportCategoryId());
        quantityDTO.setProductQuantity(carriageRuleValidateDO.getProductQuantity());
        carriageDTO.setCategoryQuantityList(Lists.newArrayList(quantityDTO));
        ItemResult<List<CarriageRuleResultDTO>> computeResult = ruleComputeService.queryCarriageRule(carriageDTO);
        if (!computeResult.isSuccess()) {
            log.error("查询运费规则失败:" + JSON.toJSONString(carriageDTO));
            return Boolean.FALSE;
        }
        return computeResult.getData().get(0).getRuleFlag();
    }

    public boolean validateShipCarriageRule(CarriageRuleValidateDO carriageRuleValidateDO) {
        //计算配送方式
        QueryShippingRuleDTO queryShippingRuleDTO = new QueryShippingRuleDTO();
        queryShippingRuleDTO.setSellerId(carriageRuleValidateDO.getUserId());
        queryShippingRuleDTO.setBuyerId(carriageRuleValidateDO.getBuyerId());
        //提货码头编码
        WarehouseBaseDataDTO warehouseBaseDataDTO = warehouseService.selectWarehouseBaseData(carriageRuleValidateDO.getWarehouseId());
        if (warehouseBaseDataDTO == null) {
            log.error("仓库信息不存在 warehouseId : {}", carriageRuleValidateDO.getWarehouseId());
            throw new BizException(BasicCode.DB_DATA_NOT_FOUND, "仓库:" + carriageRuleValidateDO.getWarehouseId());
        }
        queryShippingRuleDTO.setPickingWharfCode(warehouseBaseDataDTO.getWharfId());
        //卸货码头编码
        queryShippingRuleDTO.setUnloadingWharfCode(carriageRuleValidateDO.getUnloadingWharfCode());
        ShippingRuleResultDTO shippingRuleResultDTO = new ShippingRuleResultDTO();
        //物流类型
        shippingRuleResultDTO.setTransportCategoryId(carriageRuleValidateDO.getTransportCategoryId());
        //计量单位数量
        shippingRuleResultDTO.setProductQuantity(carriageRuleValidateDO.getProductQuantity());
        queryShippingRuleDTO.setShippingRuleList(Lists.newArrayList(shippingRuleResultDTO));
        queryShippingRuleDTO.setPickingBillType(carriageRuleValidateDO.getPickingBillType());
        //查询航线定价规则
        log.info("queryShippingRule_start:" + JSON.toJSONString(queryShippingRuleDTO));
        ItemResult<List<ShippingRuleResultDTO>> computeResult = shippingRouteService.queryShippingRule(queryShippingRuleDTO);
        log.info("queryShippingRule_end:" + JSON.toJSONString(computeResult));
        if (!computeResult.isSuccess()) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "查询航线运费定价异常！");
        }

        return !CollectionUtils.isEmpty(computeResult.getData()) && computeResult.getData().get(0).getRuleFlag();
    }

    public List<PrimaryContractGoodsMapDTO> convertContractGoodsData(List<PrimaryContractGoodsDTO> contractGoodsList,
                                                                     CarriageRuleValidateDO carriageRuleValidateDO,
                                                                     String proxyCarrierId) {
        List<PrimaryContractGoodsMapDTO> primaryContractGoodsList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(contractGoodsList)) {
            return primaryContractGoodsList;
        }
        contractGoodsList.stream().forEach(primaryContractGoods -> {
            // 一级合同商品按二级订单运输方式过滤
            if (!CsStringUtils.equals(carriageRuleValidateDO.getTransportType(), primaryContractGoods.getTransportType()))
                return;
            PrimaryContractGoodsMapDTO primaryContractGoodsMapDTO = new PrimaryContractGoodsMapDTO();
            BeanUtils.copyProperties(primaryContractGoods, primaryContractGoodsMapDTO);
            //获取支持的配送方式
            List<String> supportDeliveryWayList = Lists.newArrayList(PickingBillTypeEnum.BUYER_TAKE.getCode());
            carriageRuleValidateDO.setWarehouseId(primaryContractGoods.getWarehouseId());
            //背靠背承运商代客下单一级订单只支持买家自提
            supportDeliveryWayList(carriageRuleValidateDO, proxyCarrierId, primaryContractGoods, supportDeliveryWayList);
            primaryContractGoodsMapDTO.setSupportDeliveryWayList(supportDeliveryWayList);
            primaryContractGoodsList.add(primaryContractGoodsMapDTO);
        });

        return primaryContractGoodsList;
    }

    private void supportDeliveryWayList(CarriageRuleValidateDO carriageRuleValidateDO, String proxyCarrierId, PrimaryContractGoodsDTO primaryContractGoods, List<String> supportDeliveryWayList) {
        if (CsStringUtils.isEmpty(proxyCarrierId)) {
            //卖家配送
            if (primaryContractGoods.getIfSellerDelivery()) {
                carriageRuleValidateDO.setPickingBillType(PickingBillTypeEnum.SELLER_DELIVERY.getCode());
                Boolean sellerDeliverFlag ;
                if (CsStringUtils.equals(carriageRuleValidateDO.getTransportType(), TransportToolTypeEnum.WATER_TRANSPORT.getCode())) {
                    //船运仅配置了到位价的时候支持卖家配送
                    sellerDeliverFlag = primaryContractGoods.getShipPrice() != null &&
                            primaryContractGoods.getShipPrice().compareTo(BigDecimal.ZERO) > 0 &&
                            validateShipCarriageRule(carriageRuleValidateDO);
                } else {
                    sellerDeliverFlag = validateVehicleCarriageRule(carriageRuleValidateDO);
                }
                if (sellerDeliverFlag) {
                    supportDeliveryWayList.add(PickingBillTypeEnum.SELLER_DELIVERY.getCode());
                }
            }
            //平台配送
            addPick(carriageRuleValidateDO, primaryContractGoods, supportDeliveryWayList);
        }
    }

    private void addPick(CarriageRuleValidateDO carriageRuleValidateDO, PrimaryContractGoodsDTO primaryContractGoods, List<String> supportDeliveryWayList) {
        if (primaryContractGoods.getIfPlatformDelivery()) {
            carriageRuleValidateDO.setPickingBillType(PickingBillTypeEnum.PLATFORM_DELIVERY.getCode());
            Boolean platformDeliverFlag;
            if (CsStringUtils.equals(carriageRuleValidateDO.getTransportType(), TransportToolTypeEnum.WATER_TRANSPORT.getCode())) {
                platformDeliverFlag = validateShipCarriageRule(carriageRuleValidateDO);
            } else {
                platformDeliverFlag = validateVehicleCarriageRule(carriageRuleValidateDO);
            }
            if (platformDeliverFlag) {
                supportDeliveryWayList.add(PickingBillTypeEnum.PLATFORM_DELIVERY.getCode());
            }
        }
    }

    @Override
    public PageData<SecondaryOrderListDTO> pageQuerySecondaryOrderList(PageQuery<SecondaryOrderQueryDTO> pageQuery) {
        log.info("pageQuerySecondaryOrderList:" + JSON.toJSONString(pageQuery));
        SecondaryOrderQueryDTO queryDTO = pageQuery.getQueryDTO() == null ?
                new SecondaryOrderQueryDTO() : pageQuery.getQueryDTO();
        //开发环境不验证销售区域权限
        if (CsStringUtils.equals(profile.toLowerCase(), "dev") || CsStringUtils.equals(profile.toLowerCase(), "test")) {
            queryDTO.setRegionList(null);
        }
        if (CsStringUtils.isBlank(queryDTO.getAllocationDate())) {
            throw new BizException(BasicCode.INVALID_PARAM, "分配日期");
        }
        Date allocationDate = DateUtil.parse(queryDTO.getAllocationDate(), YYYY_MM);
        //获取当前月第一天
        Calendar calendarFirst = Calendar.getInstance();
        calendarFirst.setTime(allocationDate);
        calendarFirst.add(Calendar.MONTH, 0);
        calendarFirst.set(Calendar.DAY_OF_MONTH, 1);//设置为1号,当前月第一天
        queryDTO.setQueryStartDate(calendarFirst.getTime());
        //获取下个月第一天
        Calendar calendarEnd = Calendar.getInstance();
        calendarEnd.setTime(allocationDate);
        calendarEnd.add(Calendar.MONTH, 1);
        calendarEnd.set(Calendar.DAY_OF_MONTH, 1);//设置为1号,下个月第一天
        queryDTO.setQueryEndDate(calendarEnd.getTime());
        PageInfo<SecondaryOrderListDO> pageInfo = orderInfoBiz.querySecondaryOrderList(pageQuery);
        if (CollectionUtils.isEmpty(pageInfo.getList())) {
            return new PageData<>(new ArrayList<>());
        }
        ItemResult<GoodsDTO> goodsResult = goodsService.getGoodsInfo(queryDTO.getGoodsId());
        GoodsDTO goodsDTO = getGoodsDTO(goodsResult);
        List<SecondaryOrderListDTO> secondaryOrderList = Lists.newArrayList();
        //本地缓存
        Map<String, List<PrimaryContractOptionDTO>> contractCacheMap = Maps.newHashMap();
        pageInfo.getList().stream().forEach(secondaryOrderListDO -> {
            //根据省市区街道查询销售区域ID
            RegionQueryDTO regionQueryDTO = new RegionQueryDTO();
            regionQueryDTO.setSellerId(goodsResult.getData().getRefSellerId());
            regionQueryDTO.setProvinceCode(secondaryOrderListDO.getProvinceCode());
            regionQueryDTO.setCityCode(secondaryOrderListDO.getCityCode());
            regionQueryDTO.setDistrictCode(secondaryOrderListDO.getDistrictCode());
            regionQueryDTO.setStreetCode(secondaryOrderListDO.getStreetCode());
            List<SaleRegionSampleDTO> saleRegionList = saleRegionService.findByRegion(regionQueryDTO);
            log.info("pageQuerySecondaryOrderList_saleRegionList:" + JSON.toJSONString(saleRegionList));
            if (CollectionUtils.isEmpty(saleRegionList)) {
                throw new BizException(BasicCode.UNDEFINED_ERROR,
                        "订单orderId=" + secondaryOrderListDO.getOrderId() + ":查找不到对应的厂家销售区域");
            }
            SaleRegionSampleDTO saleRegionSampleDTO = saleRegionList.get(0);
            List<PrimaryContractOptionDTO> primaryContractOptionList = contractCacheMap.get(saleRegionSampleDTO.getSaleRegionId());
            if (primaryContractOptionList == null) {
                //获取映射到的一级合同
                ProxyContractQueryDTO proxyContractQueryDTO = new ProxyContractQueryDTO();
                proxyContractQueryDTO.setBuyerId(queryDTO.getSellerId());
                proxyContractQueryDTO.setGoodsId(queryDTO.getGoodsId());
                proxyContractQueryDTO.setSaleRegionId(saleRegionSampleDTO.getSaleRegionId());
                primaryContractOptionList = contractService.queryProxyContractList(proxyContractQueryDTO);
                contractCacheMap.put(saleRegionSampleDTO.getSaleRegionId(), primaryContractOptionList);
            }
            SecondaryOrderListDTO secondaryOrderListDTO = new SecondaryOrderListDTO();
            BeanUtils.copyProperties(secondaryOrderListDO, secondaryOrderListDTO);
            if (!CsStringUtils.equals(goodsDTO.getMeasureUnit(), secondaryOrderListDO.getUnits())) {
                secondaryOrderListDTO.setBookingQuantity(ArithUtils.multiply(
                        secondaryOrderListDO.getBookingQuantity(), secondaryOrderListDO.getConvertRate()));
                secondaryOrderListDTO.setUnits(goodsDTO.getMeasureUnit());
            }
            List<PrimaryContractOptionMapDTO> primaryContractOptionMapList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(primaryContractOptionList)) {
                //组装运费规则查询对象
                CarriageRuleValidateDO carriageRuleValidateDO = new CarriageRuleValidateDO();
                carriageRuleValidateDO.setTransportType(secondaryOrderListDO.getTransportType());
                carriageRuleValidateDO.setPickingWharfCode(secondaryOrderListDO.getPickingWharfId());
                carriageRuleValidateDO.setUnloadingWharfCode(secondaryOrderListDO.getReceivingWharfId());
                carriageRuleValidateDO.setUserId(goodsDTO.getRefSellerId());
                carriageRuleValidateDO.setBuyerId(goodsDTO.getSellerId());
                carriageRuleValidateDO.setTransportCategoryId(goodsDTO.getLogistics());
                carriageRuleValidateDO.setReceiveAddressId(secondaryOrderListDO.getAddressId());
                carriageRuleValidateDO.setReceiveAddressLocation(secondaryOrderListDO.getAddressMap());
                carriageRuleValidateDO.setProvinceCode(secondaryOrderListDO.getProvinceCode());
                carriageRuleValidateDO.setCityCode(secondaryOrderListDO.getCityCode());
                carriageRuleValidateDO.setDistrictCode(secondaryOrderListDO.getDistrictCode());
                carriageRuleValidateDO.setStreetCode(secondaryOrderListDO.getStreetCode());
                carriageRuleValidateDO.setProductQuantity(secondaryOrderListDO.getBookingQuantity());
                //转换合同对象
                primaryContractOptionList.stream().forEach(primaryContractOption -> {
                    PrimaryContractOptionMapDTO primaryContractOptionMapDTO = new PrimaryContractOptionMapDTO();
                    BeanUtils.copyProperties(primaryContractOption, primaryContractOptionMapDTO);
                    List<PrimaryContractGoodsMapDTO> primaryContractGoodsList = convertContractGoodsData(
                            primaryContractOption.getContactGoodsList(), carriageRuleValidateDO, "");
                    log.info(primaryContractOption.getContractId() + "_primaryContractGoodsList:" + JSON.toJSONString(primaryContractGoodsList));
                    primaryContractOptionMapDTO.setContactGoodsList(primaryContractGoodsList);
                    primaryContractOptionMapList.add(primaryContractOptionMapDTO);
                });
            }
            secondaryOrderListDTO.setOptionContractList(primaryContractOptionMapList);
            secondaryOrderList.add(secondaryOrderListDTO);
        });

        //返回分页数据
        PageData<SecondaryOrderListDTO> pageData = new PageData<>();
        pageData.setPageNum(pageInfo.getPageNum());
        pageData.setPageSize(pageInfo.getPageSize());
        pageData.setPages(pageInfo.getPages());
        pageData.setTotal(pageInfo.getTotal());
        pageData.setList(secondaryOrderList);

        return pageData;
    }


    private static GoodsDTO getGoodsDTO(ItemResult<GoodsDTO> goodsResult) {
        if (goodsResult == null || !goodsResult.isSuccess() || goodsResult.getData() == null) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, UNDEFINED_ERROR_GOODS_IS_NULL_MSG);
        }
        GoodsDTO goodsDTO = goodsResult.getData();
        if (CsStringUtils.isBlank(goodsDTO.getRefGoodsId())) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, UNDEFINED_ERROR_REF_GOODS_ID_BLANK_MSG);
        }
        return goodsDTO;
    }

    @Override
    public PageData<SecondaryOrderListDTO> newPageQuerySecondaryOrderList(PageQuery<SecondaryOrderQueryDTO> pageQuery) {
        log.info("newPageQuerySecondaryOrderList:" + JSON.toJSONString(pageQuery));
        SecondaryOrderQueryDTO queryDTO = pageQuery.getQueryDTO() == null ?
                new SecondaryOrderQueryDTO() : pageQuery.getQueryDTO();
        setValue(queryDTO);
        PageInfo<SecondaryOrderListDO> pageInfo = orderInfoBiz.querySecondaryOrderList(pageQuery);
        if (CollectionUtils.isEmpty(pageInfo.getList())) {
            return new PageData<>(new ArrayList<>());
        }
        List<SecondaryOrderListDTO> secondaryOrderList = Lists.newArrayList();
        //本地缓存
        Map<String, List<PrimaryContractOptionDTO>> contractCacheMap = Maps.newHashMap();
        Set<String> receivingAddressIdList = Sets.newHashSet();
        pageInfo.getList().stream().forEach(secondaryOrderListDO -> {
            receivingAddressIdList.add(secondaryOrderListDO.getAddressId());
            ItemResult<GoodsDTO> goodsResult = goodsService.getGoodsInfo(secondaryOrderListDO.getGoodsId());
            GoodsDTO goodsDTO = getDto(goodsResult);
            SecondaryOrderListDTO secondaryOrderListDTO = new SecondaryOrderListDTO();
            secondaryOrderListDTO.setGoodsId(goodsDTO.getGoodsId());
            secondaryOrderListDTO.setRefGoodsId(goodsDTO.getRefGoodsId());
            secondaryOrderListDTO.setRefSellerId(goodsDTO.getRefSellerId());
            secondaryOrderListDTO.setGoodsName(goodsDTO.getGoodsName());
            secondaryOrderListDTO.setCategoryName("");
            secondaryOrderListDTO.setPack(goodsDTO.getPack());
            setAllocationDate(secondaryOrderListDO, secondaryOrderListDTO);
            //获取地址是否绑定了厂商销售区域
            String saleRegionId = "";
            ReceivingAddressMapQueryDTO addressMapQueryDTO = new ReceivingAddressMapQueryDTO();
            addressMapQueryDTO.setAddressId(secondaryOrderListDO.getAddressId());
            addressMapQueryDTO.setSellerId(goodsDTO.getSellerId());
            addressMapQueryDTO.setFirmId(goodsDTO.getRefSellerId());
            ReceivingAddressMapDTO receivingAddressMapDTO = receivingAddressService.getReceivingAddressMapInfo(addressMapQueryDTO);
            log.info("pageQuerySecondaryOrderList_receivingAddressMapDTO:" + JSON.toJSONString(receivingAddressMapDTO));

            saleRegionId = getRegionId(secondaryOrderListDO, receivingAddressMapDTO, goodsDTO);
            String cacheKey = goodsDTO.getSellerId() + "_" + goodsDTO.getGoodsId() + "_" + saleRegionId;
            List<PrimaryContractOptionDTO> primaryContractOptionList = contractCacheMap.get(cacheKey);
            if (primaryContractOptionList == null) {
                //获取映射到的一级合同
                ProxyContractQueryDTO proxyContractQueryDTO = new ProxyContractQueryDTO();
                proxyContractQueryDTO.setBuyerId(goodsDTO.getSellerId());
                proxyContractQueryDTO.setGoodsId(goodsDTO.getGoodsId());
                proxyContractQueryDTO.setSaleRegionId(saleRegionId);
                primaryContractOptionList = contractService.queryProxyContractList(proxyContractQueryDTO);
                contractCacheMap.put(cacheKey, primaryContractOptionList);
            }
            BeanUtils.copyProperties(secondaryOrderListDO, secondaryOrderListDTO);
            if (!CsStringUtils.equals(goodsDTO.getMeasureUnit(), secondaryOrderListDO.getUnits())) {
                secondaryOrderListDTO.setBookingQuantity(ArithUtils.multiply(
                        secondaryOrderListDO.getBookingQuantity(), secondaryOrderListDO.getConvertRate()));
                secondaryOrderListDTO.setUnits(goodsDTO.getMeasureUnit());
            }
            List<PrimaryContractOptionMapDTO> primaryContractOptionMapList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(primaryContractOptionList)) {
                //组装运费规则查询对象
                CarriageRuleValidateDO carriageRuleValidateDO = new CarriageRuleValidateDO();
                carriageRuleValidateDO.setTransportType(secondaryOrderListDO.getTransportType());
                carriageRuleValidateDO.setPickingWharfCode(secondaryOrderListDO.getPickingWharfId());
                carriageRuleValidateDO.setUnloadingWharfCode(secondaryOrderListDO.getReceivingWharfId());
                carriageRuleValidateDO.setUserId(goodsDTO.getRefSellerId());
                carriageRuleValidateDO.setBuyerId(goodsDTO.getSellerId());
                carriageRuleValidateDO.setTransportCategoryId(goodsDTO.getLogistics());
                carriageRuleValidateDO.setReceiveAddressId(secondaryOrderListDO.getAddressId());
                carriageRuleValidateDO.setReceiveAddressLocation(secondaryOrderListDO.getAddressMap());
                carriageRuleValidateDO.setProvinceCode(secondaryOrderListDO.getProvinceCode());
                carriageRuleValidateDO.setCityCode(secondaryOrderListDO.getCityCode());
                carriageRuleValidateDO.setDistrictCode(secondaryOrderListDO.getDistrictCode());
                carriageRuleValidateDO.setStreetCode(secondaryOrderListDO.getStreetCode());
                carriageRuleValidateDO.setProductQuantity(secondaryOrderListDO.getBookingQuantity());
                //获取二级订单扩展对象
                OrderInfoExtDTO orderInfoExtDTO = orderInfoExtBiz.findByOrderId(secondaryOrderListDO.getOrderId());
                //转换合同对象
                primaryContractOptionList.stream().forEach(primaryContractOption -> {
                    primaryContractOptionMapListAdd(primaryContractOption, carriageRuleValidateDO, orderInfoExtDTO, secondaryOrderListDTO, primaryContractOptionMapList);
                });
            }
            //为了前端数据兼容，mock一个空对象
            if (CollectionUtils.isEmpty(primaryContractOptionList) || CollectionUtils.isEmpty(primaryContractOptionMapList)) {
                PrimaryContractOptionMapDTO primaryContractOptionMapDTO = new PrimaryContractOptionMapDTO();
                primaryContractOptionMapDTO.setContractId("-");
                primaryContractOptionMapDTO.setContractNumber("查找不到映射厂商合同");
                primaryContractOptionMapDTO.setProjectName("-");
                PrimaryContractGoodsMapDTO primaryContractGoodsMapDTO = new PrimaryContractGoodsMapDTO();
                primaryContractGoodsMapDTO.setContractId("-");
                primaryContractGoodsMapDTO.setContractGoodsId("-");
                primaryContractGoodsMapDTO.setWarehouseId("-");
                primaryContractGoodsMapDTO.setWarehouseName("-");
                primaryContractGoodsMapDTO.setMapWarehouseId("-");
                primaryContractGoodsMapDTO.setMapWarehouseName("-");
                primaryContractGoodsMapDTO.setFactoryPrice(BigDecimal.ZERO);
                primaryContractGoodsMapDTO.setSurplusQuantity(BigDecimal.ZERO);
                primaryContractGoodsMapDTO.setSupportDeliveryWayList(Lists.newArrayList(PickingBillTypeEnum.BUYER_TAKE.getCode()));
                primaryContractOptionMapDTO.setContactGoodsList(Lists.newArrayList(primaryContractGoodsMapDTO));
                primaryContractOptionMapList.add(primaryContractOptionMapDTO);
                //标记没有映射到一级合同
                secondaryOrderListDTO.setContractMapFlag(Boolean.FALSE);
            }
            secondaryOrderListDTO.setOptionContractList(primaryContractOptionMapList);
            secondaryOrderList.add(secondaryOrderListDTO);
        });
        //批量设置
        batchSetSecondaryOrderSaleRegion(secondaryOrderList);

        //匹配流向管控默认地址
        matchERPAddress(secondaryOrderList, receivingAddressIdList);

        //返回分页数据
        PageData<SecondaryOrderListDTO> pageData = new PageData<>();
        pageData.setPageNum(pageInfo.getPageNum());
        pageData.setPageSize(pageInfo.getPageSize());
        pageData.setPages(pageInfo.getPages());
        pageData.setTotal(pageInfo.getTotal());
        pageData.setList(secondaryOrderList);

        return pageData;
    }

    private void primaryContractOptionMapListAdd(PrimaryContractOptionDTO primaryContractOption, CarriageRuleValidateDO carriageRuleValidateDO, OrderInfoExtDTO orderInfoExtDTO, SecondaryOrderListDTO secondaryOrderListDTO, List<PrimaryContractOptionMapDTO> primaryContractOptionMapList) {
        PrimaryContractOptionMapDTO primaryContractOptionMapDTO = new PrimaryContractOptionMapDTO();
        BeanUtils.copyProperties(primaryContractOption, primaryContractOptionMapDTO);
        List<PrimaryContractGoodsMapDTO> primaryContractGoodsList = convertContractGoodsData(
                primaryContractOption.getContactGoodsList(), carriageRuleValidateDO, orderInfoExtDTO.getProxyCarrierId());
        log.info(primaryContractOption.getContractId() + "_primaryContractGoodsList:" + JSON.toJSONString(primaryContractGoodsList));
        // 一级合同商品列表为空时跳过
        if (CollectionUtils.isEmpty(primaryContractGoodsList)) return;
        primaryContractOptionMapDTO.setContactGoodsList(primaryContractGoodsList);
        // 二级订单自提时，一级订单映射合同的仓库需与二级订单的仓库对应
        if (PickingBillTypeEnum.BUYER_TAKE.getCode().equals(secondaryOrderListDTO.getDeliveryWay()) &&
                primaryContractGoodsList.stream().noneMatch(item -> secondaryOrderListDTO.getMapWarehouseId().equals(item.getMapWarehouseId())))
            return;
        primaryContractOptionMapList.add(primaryContractOptionMapDTO);
    }

    private void setValue(SecondaryOrderQueryDTO queryDTO) {
        //开发环境不验证销售区域权限
        if (CsStringUtils.equals(profile.toLowerCase(), "dev") || CsStringUtils.equals(profile.toLowerCase(), "test")) {
            queryDTO.setRegionList(null);
        }
        //结束日期加一天
        if (CsStringUtils.isNotBlank(queryDTO.getCreateQueryEndDate())) {
            Date endDate = DateUtil.parse(queryDTO.getCreateQueryEndDate(), "yyyy-MM-dd");
            queryDTO.setCreateQueryEndDate(DateUtil.convertDateToString(new Date(endDate.getTime() + 86400000)));
        }
    }

    private String getRegionId(SecondaryOrderListDO secondaryOrderListDO, ReceivingAddressMapDTO receivingAddressMapDTO, GoodsDTO goodsDTO) {
        String saleRegionId;
        if (receivingAddressMapDTO != null && CsStringUtils.isNotBlank(receivingAddressMapDTO.getSaleRegionId())) {
            saleRegionId = receivingAddressMapDTO.getSaleRegionId();
        } else {
            //根据省市区街道查询销售区域ID
            RegionQueryDTO regionQueryDTO = new RegionQueryDTO();
            regionQueryDTO.setSellerId(goodsDTO.getRefSellerId());
            regionQueryDTO.setProvinceCode(secondaryOrderListDO.getProvinceCode());
            regionQueryDTO.setCityCode(secondaryOrderListDO.getCityCode());
            regionQueryDTO.setDistrictCode(secondaryOrderListDO.getDistrictCode());
            regionQueryDTO.setStreetCode(secondaryOrderListDO.getStreetCode());
            List<SaleRegionSampleDTO> saleRegionList = saleRegionService.findByRegion(regionQueryDTO);
            log.info("pageQuerySecondaryOrderList_saleRegionList:" + JSON.toJSONString(saleRegionList));
            if (CollectionUtils.isEmpty(saleRegionList)) {
                throw new BizException(BasicCode.UNDEFINED_ERROR,
                        "订单orderId=" + secondaryOrderListDO.getOrderId() + ":查找不到对应的厂家销售区域");
            }
            saleRegionId = saleRegionList.get(0).getSaleRegionId();
        }
        return saleRegionId;
    }

    private static void setAllocationDate(SecondaryOrderListDO secondaryOrderListDO, SecondaryOrderListDTO secondaryOrderListDTO) {
        if (secondaryOrderListDO.getPayTime() != null) {
            secondaryOrderListDTO.setAllocationDate(DateUtil.format(secondaryOrderListDO.getPayTime(), YYYY_MM));
        } else {
            // 船运二级订单支付时间为空
            secondaryOrderListDTO.setAllocationDate(DateUtil.format(secondaryOrderListDO.getCreateTime(), YYYY_MM));
        }
    }


    private static GoodsDTO getDto(ItemResult<GoodsDTO> goodsResult) {
        if (goodsResult == null || !goodsResult.isSuccess() || goodsResult.getData() == null) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, UNDEFINED_ERROR_GOODS_IS_NULL_MSG);
        }
        GoodsDTO goodsDTO = goodsResult.getData();
        if (CsStringUtils.isBlank(goodsDTO.getRefGoodsId())) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, UNDEFINED_ERROR_REF_GOODS_ID_BLANK_MSG);
        }
        return goodsDTO;
    }

    /**
     * 匹配ERP地址
     * @param secondaryOrderList 二级订单列表
     * @param receivingAddressIdList 收货地址ID列表
     */
    private void matchERPAddress(List<SecondaryOrderListDTO> secondaryOrderList,
                                 Set<String> receivingAddressIdList) {
        if (CollectionUtils.isEmpty(secondaryOrderList) || CollectionUtils.isEmpty(receivingAddressIdList)) {
            return;
        }
        log.info("queryFlowMonitorAddressByIds:" + receivingAddressIdList);
        List<ERPAddressDTO> erpAddressList = orderErpAddressBiz.queryFlowMonitorAddressByIds(Lists.newArrayList(receivingAddressIdList));
        Map<String, TreeSet<ERPAddressDTO>> erpAddressMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(erpAddressList)) {
            return;
        }
        Comparator<ERPAddressDTO> comparator = (o1, o2) -> o2.getUpdateTime().compareTo(o1.getUpdateTime());
        erpAddressList.stream().forEach(erpAddressDTO -> {
            if (erpAddressMap.get(erpAddressDTO.getReceivingAddressId()) == null) {
                //初始化排序树
                erpAddressMap.put(erpAddressDTO.getReceivingAddressId(), Sets.newTreeSet(comparator));
            }
            erpAddressMap.get(erpAddressDTO.getReceivingAddressId()).add(erpAddressDTO);
        });
        //过滤配送方式
        Set<String> deliverWaySet = Sets.newHashSet(
                PickingBillTypeEnum.BUYER_TAKE.getCode(),
                PickingBillTypeEnum.PLATFORM_DELIVERY.getCode());
        secondaryOrderList.stream().forEach(secondaryOrderListDTO -> {
            if (erpAddressMap.get(secondaryOrderListDTO.getAddressId()) == null) return;
            TreeSet<ERPAddressDTO> addressSet = erpAddressMap.get(secondaryOrderListDTO.getAddressId());
            String unloadingType = "00,01,02";
            //混合类型地址 00配送，01自提，02混合(买家自提和平台配送均相当于ERP自提)
            if (deliverWaySet.contains(secondaryOrderListDTO.getDeliveryWay())) {
                unloadingType = "01,02";
            } else if (CsStringUtils.equals(secondaryOrderListDTO.getDeliveryWay(),
                    PickingBillTypeEnum.SELLER_DELIVERY.getCode())) {
                unloadingType = "00,02";
            }
            final String finalUnloadingType = unloadingType;
            for (ERPAddressDTO erpAddressDTO : addressSet) {
                //匹配到最新的满足条件的ERP地址
                if (finalUnloadingType.contains(erpAddressDTO.getType())) {
                    secondaryOrderListDTO.setErpAddressDTO(erpAddressDTO);
                    break;
                }
            }
        });
    }

    /**
     * 批量设置销售区域
     *
     * @param secondaryOrderList 二级订单列表
     */
    private void batchSetSecondaryOrderSaleRegion(List<SecondaryOrderListDTO> secondaryOrderList) {
        Set<String> saleRegionIdSet = Sets.newHashSet();
        for (SecondaryOrderListDTO secondaryOrderListDTO : secondaryOrderList) {
            String saleRegion1 = secondaryOrderListDTO.getSaleRegion1();
            if (CsStringUtils.isNotBlank(saleRegion1)) {
                saleRegionIdSet.add(saleRegion1);
            }
            String saleRegion2 = secondaryOrderListDTO.getSaleRegion2();
            if (CsStringUtils.isNotBlank(saleRegion2)) {
                saleRegionIdSet.add(saleRegion2);
            }
            String saleRegion3 = secondaryOrderListDTO.getSaleRegion3();
            if (CsStringUtils.isNotBlank(saleRegion3)) {
                saleRegionIdSet.add(saleRegion3);
            }
        }
        if (CollectionUtils.isEmpty(saleRegionIdSet)) return;
        //查询销售区域信息
        SaleRegionCondDTO saleRegionCondDTO = new SaleRegionCondDTO();
        saleRegionCondDTO.setSaleRegionIdList(Lists.newArrayList(saleRegionIdSet));
        List<SaleRegionSampleDTO> sampleDTOList = saleRegionService.batchQuerySaleRegion(saleRegionCondDTO);
        if (CollectionUtils.isEmpty(sampleDTOList)) return;
        //设置销售区域名称
        setAreaName(secondaryOrderList, sampleDTOList);
    }

    private static void setAreaName(List<SecondaryOrderListDTO> secondaryOrderList, List<SaleRegionSampleDTO> sampleDTOList) {
        Map<String, String> saleRegionId2NameMap = sampleDTOList.stream().collect(
                Collectors.toMap(SaleRegionSampleDTO::getSaleRegionId, SaleRegionSampleDTO::getSaleRegionName));
        for (SecondaryOrderListDTO secondaryOrderListDTO : secondaryOrderList) {
            if (CsStringUtils.isNotBlank(secondaryOrderListDTO.getSaleRegion1())) {
                secondaryOrderListDTO.setSaleRegionName1(saleRegionId2NameMap.get(secondaryOrderListDTO.getSaleRegion1()));
            }
            if (CsStringUtils.isNotBlank(secondaryOrderListDTO.getSaleRegion2())) {
                secondaryOrderListDTO.setSaleRegionName2(saleRegionId2NameMap.get(secondaryOrderListDTO.getSaleRegion2()));
            }
            if (CsStringUtils.isNotBlank(secondaryOrderListDTO.getSaleRegion3())) {
                secondaryOrderListDTO.setSaleRegionName3(saleRegionId2NameMap.get(secondaryOrderListDTO.getSaleRegion3()));
            }
        }
    }


    /**
     * 转换一级订单创建数据
     *
     * @param primaryOrderCreateDTO 一级订单对象
     * @return OrderDTO
     */
    private OrderDTO convertCreateOrderParam(PrimaryOrderCreateDTO primaryOrderCreateDTO) {
        //获取二级订单信息
        OrderDTO secondaryOrderDTO = orderQueryService.getOrderInfo(primaryOrderCreateDTO.getSecondaryOrderId());
        //一级订单差异信息
        OrderDTO primaryOrderDTO = new OrderDTO();
        primaryOrderDTO.setProxyOrderType(ProxyOrderTypeEnum.PRIMARY.getCode());
        primaryOrderDTO.setDealsId(primaryOrderCreateDTO.getContractId());
        primaryOrderDTO.setProvinceName(primaryOrderCreateDTO.getProjectName());
        primaryOrderDTO.setStoreId(primaryOrderCreateDTO.getWarehouseId());
        primaryOrderDTO.setDeliverWay(primaryOrderCreateDTO.getDeliverWay());
        primaryOrderDTO.setErpAddressDTO(primaryOrderCreateDTO.getErpAddressDTO());
        //拷贝二级订单的相关信息
        primaryOrderDTO.setBuyerId(secondaryOrderDTO.getSellerId());
        primaryOrderDTO.setAddressId(secondaryOrderDTO.getAddressId());
        primaryOrderDTO.setSaleRegionPath(secondaryOrderDTO.getSaleRegionPath());
        primaryOrderDTO.setTakeTime(secondaryOrderDTO.getTakeTime());
        primaryOrderDTO.setTakeTimeQuantum(secondaryOrderDTO.getTakeTimeQuantum());
        //组装商品行数据
        if (CollectionUtils.isEmpty(secondaryOrderDTO.getOrderItems())) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "查询不到二级订单的商品数据");
        }
        OrderItemDTO secondaryOrderItemDTO = secondaryOrderDTO.getOrderItems().get(0);
        ItemResult<GoodsDTO> goodsResult = goodsService.getGoodsInfo(secondaryOrderItemDTO.getGoodsId());
        if (goodsResult == null || !goodsResult.isSuccess() || goodsResult.getData() == null) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, UNDEFINED_ERROR_GOODS_IS_NULL_MSG);
        }
        GoodsDTO goodsDTO = goodsResult.getData();
        if (CsStringUtils.isBlank(goodsDTO.getRefGoodsId())) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, UNDEFINED_ERROR_REF_GOODS_ID_BLANK_MSG);
        }

        TrContractDTO contractDTO = contractService.getContract(primaryOrderDTO.getDealsId());


        OrderItemDTO primaryOrderItemDTO = new OrderItemDTO();
        primaryOrderItemDTO.setContractGoodsId(primaryOrderCreateDTO.getContractGoodsId());
        primaryOrderItemDTO.setGoodsId(goodsDTO.getRefGoodsId());
        //一级订单统一使用计量单位下单
        primaryOrderItemDTO.setUnits(goodsDTO.getMeasureUnit());
        primaryOrderItemDTO.setUnitsName(goodsDTO.getMeasureUnit());
        primaryOrderItemDTO.setItemQuantity(primaryOrderCreateDTO.getPurchaseQuantity());
        //当前业务限制二级订单只能为单商品下单
        primaryOrderDTO.setOrderItems(Lists.newArrayList(primaryOrderItemDTO));
        //生成映射对象
        OrderProxyMapDTO orderProxyMapDTO = new OrderProxyMapDTO();
        orderProxyMapDTO.setSecondaryOrderId(secondaryOrderDTO.getOrderId());
        orderProxyMapDTO.setSecondaryOrderCode(secondaryOrderDTO.getOrderCode());
        orderProxyMapDTO.setSecondaryDeliverWay(secondaryOrderDTO.getDeliverWay());
        //二级订单仓库ID
        if (!CsStringUtils.equals(secondaryOrderItemDTO.getStoreId(), primaryOrderCreateDTO.getMapWarehouseId())) {
            orderProxyMapDTO.setSecondaryStoreId(primaryOrderCreateDTO.getMapWarehouseId());
            orderProxyMapDTO.setSecondaryStoreName(primaryOrderCreateDTO.getMapWarehouseName());
        }
        orderProxyMapDTO.setPrimaryDeliverWay(primaryOrderDTO.getDeliverWay());
        primaryOrderDTO.setOrderProxyMapDTO(orderProxyMapDTO);
        //设置扩展信息对象
        OrderInfoExtDTO orderInfoExtDTO = orderInfoExtBiz.findByOrderId(secondaryOrderDTO.getOrderId());
        OrderInfoExtDTO primaryOrderInfoExtDTO = new OrderInfoExtDTO();
        primaryOrderInfoExtDTO.setTransportType(orderInfoExtDTO.getTransportType());
        primaryOrderInfoExtDTO.setBillPaymentType(orderInfoExtDTO.getBillPaymentType());
        primaryOrderInfoExtDTO.setProxyCarrierId(orderInfoExtDTO.getProxyCarrierId());
        if (contractDTO.getIsMonthly() == null) {
            contractDTO.setIsMonthly(0);
        }
        primaryOrderInfoExtDTO.setIsMonthly(contractDTO.getIsMonthly());
        if (CsStringUtils.equals(primaryOrderCreateDTO.getDeliverWay(), PickingBillTypeEnum.SELLER_DELIVERY.getCode())) {
            primaryOrderInfoExtDTO.setIfAgreeDispatch(orderInfoExtDTO.getIfAgreeDispatch());
            primaryOrderInfoExtDTO.setBasePreference(orderInfoExtDTO.getBasePreference());
        }
        primaryOrderDTO.setOrderInfoExtDTO(primaryOrderInfoExtDTO);

        return primaryOrderDTO;
    }

    /**
     * 验证提交一级订单参数
     *
     * @param submitPrimaryOrderDTO 批量提交一级订单对象
     */
    private void validateSubmitPrimaryOrderParam(SubmitPrimaryOrderDTO submitPrimaryOrderDTO) {
        checkParams(submitPrimaryOrderDTO);
        submitPrimaryOrderDTO.getPrimaryOrderCreateList().forEach(ProxyOrderService::checkParamsV2);
    }

    private static void checkParamsV2(PrimaryOrderCreateDTO primaryOrderCreateDTO) {
        if (CsStringUtils.isBlank(primaryOrderCreateDTO.getSecondaryOrderId())) {
            throw new BizException(BasicCode.INVALID_PARAM, "二级订单ID");
        }
        if (CsStringUtils.isBlank(primaryOrderCreateDTO.getSecondaryOrderCode())) {
            throw new BizException(BasicCode.INVALID_PARAM, "二级订单号");
        }
        if (CsStringUtils.isBlank(primaryOrderCreateDTO.getSaleRegionPath())) {
            throw new BizException(BasicCode.INVALID_PARAM, "二级订单销售区域ID");
        }
        if (CsStringUtils.isBlank(primaryOrderCreateDTO.getContractId())) {
            throw new BizException(BasicCode.INVALID_PARAM, "一级合同Id");
        }
        if (CsStringUtils.isBlank(primaryOrderCreateDTO.getContractNumber())) {
            throw new BizException(BasicCode.INVALID_PARAM, "一级合同编号");
        }
        if (CsStringUtils.isBlank(primaryOrderCreateDTO.getDeliverWay())) {
            throw new BizException(BasicCode.INVALID_PARAM, "一级订单配送方式");
        }
        if (CsStringUtils.isBlank(primaryOrderCreateDTO.getWarehouseId())) {
            throw new BizException(BasicCode.INVALID_PARAM, "一级订单仓库ID");
        }
        if (CsStringUtils.isBlank(primaryOrderCreateDTO.getWarehouseName())) {
            throw new BizException(BasicCode.INVALID_PARAM, "一级订单仓库名称");
        }
        if (CsStringUtils.isBlank(primaryOrderCreateDTO.getMapWarehouseId())) {
            throw new BizException(BasicCode.INVALID_PARAM, "一级订单映射仓库ID");
        }
        if (CsStringUtils.isBlank(primaryOrderCreateDTO.getMapWarehouseName())) {
            throw new BizException(BasicCode.INVALID_PARAM, "一级订单映射仓库名称");
        }
        if (CsStringUtils.isBlank(primaryOrderCreateDTO.getGoodsId())) {
            throw new BizException(BasicCode.INVALID_PARAM, "代理商品ID");
        }
        if (CsStringUtils.isBlank(primaryOrderCreateDTO.getAllocationDate())) {
            throw new BizException(BasicCode.INVALID_PARAM, "库存分配月份");
        }
    }

    private static void checkParams(SubmitPrimaryOrderDTO submitPrimaryOrderDTO) {
        if (CsStringUtils.isBlank(submitPrimaryOrderDTO.getOperatorId())) {
            throw new BizException(BasicCode.INVALID_PARAM, "操作人ID");
        }
        if (CsStringUtils.isBlank(submitPrimaryOrderDTO.getOperatorName())) {
            throw new BizException(BasicCode.INVALID_PARAM, "操作人名称");
        }
        if (CsStringUtils.isBlank(submitPrimaryOrderDTO.getSellerId())) {
            throw new BizException(BasicCode.INVALID_PARAM, "经销商ID");
        }
        if (CollectionUtils.isEmpty(submitPrimaryOrderDTO.getPrimaryOrderCreateList())) {
            throw new BizException(BasicCode.INVALID_PARAM, "一级订单创建列表");
        }
    }

    /**
     * 一级订单自动支付
     *
     * @param orderPayInfoDTO 支付单信息
     * @param operator        操作人
     */
    @Override
    public void primaryOrderAutoPay(OrderPayinfoDTO orderPayInfoDTO, String operator) {
        log.info("primaryOrderAutoPay:" + JSON.toJSONString(orderPayInfoDTO));
        PaymentRequestDTO paymentRequestDTO = new PaymentRequestDTO();
        paymentRequestDTO.setBizId(orderPayInfoDTO.getPayinfoId());
        paymentRequestDTO.setAmount(orderPayInfoDTO.getPayAmount().setScale(2, RoundingMode.HALF_UP).toString());
        //目前只尝试使用ERP余额渠道
        paymentRequestDTO.setChannelCode(ChannelCodeEnum.ERP.getCode());
        paymentRequestDTO.setOperator(operator);
        //模拟PC取值
        paymentRequestDTO.setPaymentBizCode("order");//订单支付
        paymentRequestDTO.setClientType(ClientType.PC.getCode());//PC端
        paymentRequestDTO.setBody("订单款(订单货款:"+paymentRequestDTO.getAmount()+"元)");

        //更新支付单信息
        OrderPayinfo saveOrderPayInfo = new OrderPayinfo();
        saveOrderPayInfo.setPayinfoId(orderPayInfoDTO.getPayinfoId());
        saveOrderPayInfo.setPayinfoWay(ChannelCodeEnum.ERP.getCode());
        saveOrderPayInfo.setPayinfoStatus(PayStatusEnum.IN_PAYMENT.code());
        BaseBiz.setOperInfo(saveOrderPayInfo, operator, false);
        orderPayInfoBiz.updateSelective(saveOrderPayInfo);

        OrderInfoExtDTO orderInfoExtDTO = orderInfoExtBiz.findByOrderId(orderPayInfoDTO.getObjectId());
        Integer monthly = Optional.ofNullable(orderInfoExtDTO)
                .map(OrderInfoExtDTO::getIsMonthly)
                .orElse(0);
        paymentRequestDTO.setIsMonthly(monthly);

         //erp支付
        log.info("一级订单:{} erp渠道自动支付:{}",orderPayInfoDTO.getObjectCode(),paymentRequestDTO);
        PaymentResponseDTO paymentResponseDTO = paymentService.pay(paymentRequestDTO);
        if (paymentResponseDTO == null || !paymentResponseDTO.isSuccess()) {
            log.info("erp支付结果:支付异常，返回结果为空");
            throw new BizException(BasicCode.UNDEFINED_ERROR, "ERP余额支付异常");
        }
    }

    /**
     * 提交一级代理订单
     *
     * @param primaryOrderCreateDTO 一级代理订单创建对象
     */
    @Override
    @SetThreadName
    public OrderMapResultDTO submitProxyPrimaryOrder(PrimaryOrderCreateDTO primaryOrderCreateDTO) {
        log.info("submitProxyPrimaryOrder:" + JSON.toJSONString(primaryOrderCreateDTO));
        OrderMapResultDTO orderMapResultDTO = new OrderMapResultDTO();
        orderMapResultDTO.setSecondaryOrderId(primaryOrderCreateDTO.getSecondaryOrderId());
        orderMapResultDTO.setSecondaryOrderCode(primaryOrderCreateDTO.getSecondaryOrderCode());
        orderMapResultDTO.setBatchNumber(primaryOrderCreateDTO.getBatchNumber());
        orderMapResultDTO.setPrimaryOrderCreateDTO(primaryOrderCreateDTO);
        try {
            OrderDTO primaryOrderDTO;
            try {
                //组装创建订单数据
                OrderDTO orderCreateDTO = convertCreateOrderParam(primaryOrderCreateDTO);
                //一级订单合同下单
                orderCreateDTO.setOrderType(OrderTypeEnum.CONTRACT.getCode());
                primaryOrderDTO = OrderServiceFactory.getOrderHandlerService(
                        OrderTypeEnum.CONTRACT.getBizType()).createOrder(orderCreateDTO, primaryOrderCreateDTO.getOperatorId());
                //设置一级订单数据
                primaryOrderCreateDTO.setPrimaryOrderId(primaryOrderDTO.getOrderId());
                primaryOrderCreateDTO.setPrimaryOrderCode(primaryOrderDTO.getOrderCode());
                //发送消息取消二级订单的发货单，前提: 一级订单的量被调小了 将执行方法: cancelSecondaryTakeInfo
                messageService.sendMQ(primaryOrderCreateDTO, "com.ecommerce.order.takeInfo.cancel.secondary.notify");
                //如果一级订单需要厂家确认或者船运时，直接返回不再自动支付
                if (primaryOrderDTO.getIfNeedsellerconfirm() || CsStringUtils.equals(orderCreateDTO.getOrderInfoExtDTO().getTransportType(),
                                TransportToolTypeEnum.WATER_TRANSPORT.getCode())) {
                    //更新映射状态
                    orderMapResultDTO.setTaskStatus(PrimaryMapStatusEnum.PAY_SUCCESS.getCode());
                    orderMapResultDTO.setResultComment(RESULT_COMMENT);
                    orderMapTaskBiz.updateOrderMapTaskStatus(orderMapResultDTO);
                    return orderMapResultDTO;
                }
            } catch (BizException e) {
                log.error("二级订单号:{},创建一级订单失败:{}",primaryOrderCreateDTO.getSecondaryOrderCode(),e.getMessage(), e);
                throw new BizException(OrderErrorCode.PRIMARY_SUBMIT_FAILED, "一级订单创建失败，" + e.getMessage());
            } catch (Exception e) {
                log.error("二级订单号:{},创建一级订单失败:{}",primaryOrderCreateDTO.getSecondaryOrderCode(), e.getMessage(), e);
                throw new BizException(OrderErrorCode.PRIMARY_SUBMIT_FAILED, "一级订单创建失败");
            }
            try {
                //自动支付
                primaryOrderAutoPay(primaryOrderDTO.getOrderPayinfo().get(0), primaryOrderCreateDTO.getOperatorId());
            } catch (BizException e) {
                log.error(LOG_ERROR_SECONDARY_ORDER,primaryOrderCreateDTO.getSecondaryOrderCode(), e.getMessage(),  e);
                throw new BizException(OrderErrorCode.PRIMARY_PAY_FAILED, "一级订单自动支付失败，" + e.getMessage());
            } catch (Exception e) {
                log.error(LOG_ERROR_SECONDARY_ORDER,primaryOrderCreateDTO.getSecondaryOrderCode(), e.getMessage(),  e);
                throw new BizException(OrderErrorCode.PRIMARY_PAY_FAILED, "一级订单自动支付失败");
            }
            //更新映射状态
            orderMapResultDTO.setTaskStatus(PrimaryMapStatusEnum.PAY_SUCCESS.getCode());
            orderMapResultDTO.setResultComment(RESULT_COMMENT);
            orderMapTaskBiz.updateOrderMapTaskStatus(orderMapResultDTO);
        } catch (BizException be) {
            orderMapResultDTO.setResultComment(be.getMessage());
            //支付失败不再出现在操作列表，标记为已映射
            if (CsStringUtils.equals(be.getErrorCode().getName(), "PRIMARY_PAY_FAILED")) {
                orderMapResultDTO.setTaskStatus(PrimaryMapStatusEnum.SUBMIT_SUCCESS.getCode());
                orderMapTaskBiz.updateOrderMapTaskStatus(orderMapResultDTO);
            } else {
                orderMapResultDTO.setTaskStatus(PrimaryMapStatusEnum.SUBMIT_FAIL.getCode());
                orderMapTaskBiz.updateOrderMapTaskStatus(orderMapResultDTO);
            }
        }

        return orderMapResultDTO;
    }

    /**
     * 提交一级代理订单
     *
     * @param submitPrimaryOrderDTO 一级代理订单创建对象
     */
    @Override
    @Transactional
    public OrderDTO submitProxyPrimaryOrder2(SubmitPrimaryOrderDTO submitPrimaryOrderDTO, String batchNumber) {
        PrimaryOrderCreateDTO primaryOrderCreateDTO = submitPrimaryOrderDTO.getPrimaryOrderCreateList().get(0);
        log.info("submitProxyPrimaryOrder2:" + JSON.toJSONString(primaryOrderCreateDTO));

        String bizResource = "createOrderMapTask:" + primaryOrderCreateDTO.getSecondaryOrderId();
        String identifier = null;

        try {
            identifier = redisLockService.lockFast(bizResource);
            primaryOrderCreateDTO.setOperatorId(submitPrimaryOrderDTO.getOperatorId());
            primaryOrderCreateDTO.setOperatorName(submitPrimaryOrderDTO.getOperatorName());
            primaryOrderCreateDTO.setBatchNumber(batchNumber);
            Boolean createFlag = orderMapTaskBiz.createOrderMapTask(submitPrimaryOrderDTO, primaryOrderCreateDTO);
            if (createFlag) {
                OrderMapResultDTO orderMapResultDTO = new OrderMapResultDTO();
                orderMapResultDTO.setSecondaryOrderId(primaryOrderCreateDTO.getSecondaryOrderId());
                orderMapResultDTO.setSecondaryOrderCode(primaryOrderCreateDTO.getSecondaryOrderCode());
                orderMapResultDTO.setBatchNumber(primaryOrderCreateDTO.getBatchNumber());
                orderMapResultDTO.setPrimaryOrderCreateDTO(primaryOrderCreateDTO);

                //组装创建订单数据
                OrderDTO orderCreateDTO = convertCreateOrderParam(primaryOrderCreateDTO);
                //一级订单合同下单
                orderCreateDTO.setOrderType(OrderTypeEnum.CONTRACT.getCode());
                OrderDTO primaryOrderDTO = OrderServiceFactory.getOrderHandlerService(
                        OrderTypeEnum.CONTRACT.getBizType()).createOrder(orderCreateDTO, primaryOrderCreateDTO.getOperatorId());
                //设置一级订单数据
                primaryOrderCreateDTO.setPrimaryOrderId(primaryOrderDTO.getOrderId());
                primaryOrderCreateDTO.setPrimaryOrderCode(primaryOrderDTO.getOrderCode());

                //发送消息取消二级订单的发货单，前提: 一级订单的量被调小了 将执行方法: cancelSecondaryTakeInfo
                messageService.sendMQ(primaryOrderCreateDTO, "com.ecommerce.order.takeInfo.cancel.secondary.notify");
                //如果一级订单需要厂家确认或者船运时，直接返回不再自动支付
                if (primaryOrderDTO.getIfNeedsellerconfirm() || CsStringUtils.equals(orderCreateDTO.getOrderInfoExtDTO().getTransportType(),
                        TransportToolTypeEnum.WATER_TRANSPORT.getCode())) {
                    //更新映射状态
                    orderMapResultDTO.setTaskStatus(PrimaryMapStatusEnum.PAY_SUCCESS.getCode());
                    orderMapResultDTO.setResultComment(RESULT_COMMENT);
                    orderMapTaskBiz.updateOrderMapTaskStatus(orderMapResultDTO);
                    return null;
                }

                //更新映射状态
                orderMapResultDTO.setTaskStatus(PrimaryMapStatusEnum.PAY_SUCCESS.getCode());
                orderMapResultDTO.setResultComment(RESULT_COMMENT);
                orderMapTaskBiz.updateOrderMapTaskStatus(orderMapResultDTO);

                return primaryOrderDTO;
            }
        } finally {
            if (CsStringUtils.isNotEmpty(bizResource)) {
                redisLockService.unlock(bizResource, identifier);
            }
        }
        return null;
    }

    @Override
    public void cancelSecondaryTakeInfo(PrimaryOrderCreateDTO primaryOrderCreateDTO) {
        log.info("cancelSecondaryTakeInfo:{}",primaryOrderCreateDTO);
        List<TakeInfo> secondaryTakeInfoList = takeInfoBiz.getTakeInfoByOrderId(primaryOrderCreateDTO.getSecondaryOrderId());
        if(CollectionUtils.isEmpty(secondaryTakeInfoList)){
            return;
        }
        List<OrderItem> primaryOrderItems = orderItemBiz.findByOrderId(primaryOrderCreateDTO.getPrimaryOrderId());
        List<OrderItem> secondaryOrderItems = orderItemBiz.findByOrderId(primaryOrderCreateDTO.getSecondaryOrderId());
        if( CollectionUtils.isEmpty(primaryOrderItems) || CollectionUtils.isEmpty(secondaryOrderItems) ){
            log.info("cancelSecondaryTakeInfo primaryOrderItems or secondaryOrderItems is null");
            return;
        }
        BigDecimal primaryOrderQuantity = primaryOrderItems.stream().map(item -> item.getItemQuantity().multiply(item.getConvertRate())).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal secondaryOrderQuantity = secondaryOrderItems.stream().map(item -> item.getItemQuantity().multiply(item.getConvertRate())).reduce(BigDecimal.ZERO, BigDecimal::add);
        //如果一级订单量小于二级订单量，则关闭二级订单下的所有的发货单  一定是所有的，与海心确认过  2020.10.28
        if( primaryOrderQuantity.compareTo(secondaryOrderQuantity) < 0 ){
            log.info("cancelSecondaryTakeInfo primaryOrder:{} quantity:{},secondaryOrder:{} quantity:{}",primaryOrderCreateDTO.getPrimaryOrderCode(),primaryOrderQuantity,primaryOrderCreateDTO.getSecondaryOrderCode(),secondaryOrderQuantity);
            //通知买家
            //腾讯短信模板id:758944 您原预购的卖家[sellerName]的商品[productName][XXX]吨，因该商品库存有限，卖家已将订单量调整为[XXX]吨，差额已原路退还至您的账号。
            // 为确保您及时收到商品，请您尽快根据新的订单[申请提货的链接]重新申请提货。 如有疑问，请联系卖家。
            SendSMSMessageDTO sendSMSMessageDTO = new SendSMSMessageDTO();
            sendSMSMessageDTO.setBusinessCode(primaryOrderCreateDTO.getSecondaryOrderCode());
            sendSMSMessageDTO.setMessageConfigCode(MessageConfigCodeEnum.PRIMARY_ORDER_CHANGE_QUANTITY.getCode());
            sendSMSMessageDTO.setOrderDTO(orderQueryService.getOrderInfo(primaryOrderCreateDTO.getSecondaryOrderId()));
            Map<String,Object> extParamsMap = Maps.newHashMap();
            extParamsMap.put("sellerName",sendSMSMessageDTO.getOrderDTO().getSellerName());
            extParamsMap.put("productName",secondaryOrderItems.get(0).getGoodsName());
            extParamsMap.put("oldQuantity",secondaryOrderQuantity.setScale(2, RoundingMode.HALF_UP).toPlainString());
            extParamsMap.put("newQuantity",primaryOrderQuantity.setScale(2, RoundingMode.HALF_UP).toPlainString());
            sendSMSMessageDTO.setExtraParams(extParamsMap);
            smsMessageFactoryContext.sendSMSMessage(sendSMSMessageDTO);
            //关闭二级订单的发货单
            for (TakeInfo takeInfo : secondaryTakeInfoList) {
                takeInfoService.doSellerCancelTakeInfo(takeInfo.getTakeId(),takeInfo.getSellerId(),primaryOrderCreateDTO.getOperatorId(),false);
            }
            //订单关闭时统一退款
        }else{
            log.info("cancelSecondaryTakeInfo 二级发货单没有取消 primaryOrder:{} quantity:{},secondaryOrder:{} quantity:{}",primaryOrderCreateDTO.getPrimaryOrderCode(),primaryOrderQuantity,primaryOrderCreateDTO.getSecondaryOrderCode(),secondaryOrderQuantity);
        }
    }

    @Override
    public void batchSubmitProxyPrimaryOrder(SubmitPrimaryOrderDTO submitPrimaryOrderDTO) {
        log.info("batchSubmitProxyPrimaryOrder:" + JSON.toJSONString(submitPrimaryOrderDTO));
        List<PrimaryOrderCreateDTO> dtos = submitPrimaryOrderDTO.getPrimaryOrderCreateList().stream().filter(item -> !CsStringUtils.isEmpty(item.getContractId()) && !"-".equals(item.getContractId())).toList();
        submitPrimaryOrderDTO.setPrimaryOrderCreateList(dtos);
        log.info("batchSubmitProxyPrimaryOrder:" + JSON.toJSONString(submitPrimaryOrderDTO));

        if(dtos.size() == 0)
        {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "请选择要提交的二级订单");
        }

        //参数验证
        validateSubmitPrimaryOrderParam(submitPrimaryOrderDTO);
        //将提交的订单放入任务处理队列
        String batchNumber = "T" + DateUtil.format(new Date(), "yyyyMMddHHmmss");


        // 提交的订单数
        if(submitPrimaryOrderDTO.getPrimaryOrderCreateList().size() == 1) {
            log.info("开始提交一级订单: {}", submitPrimaryOrderDTO.getPrimaryOrderCreateList().get(0).getSecondaryOrderCode());
            OrderDTO primaryOrderDTO = proxyOrderService.submitProxyPrimaryOrder2(submitPrimaryOrderDTO, batchNumber);
            PrimaryOrderCreateDTO primaryOrderCreateDTO = submitPrimaryOrderDTO.getPrimaryOrderCreateList().get(0);

            if(primaryOrderDTO == null)
            {
                return;
            }

            try
            {
                //自动支付
                log.info("开始自动支付: {}", submitPrimaryOrderDTO.getPrimaryOrderCreateList().get(0).getSecondaryOrderCode());
                primaryOrderAutoPay(primaryOrderDTO.getOrderPayinfo().get(0), primaryOrderCreateDTO.getOperatorId());
            }
            catch(Exception e)
            {
                log.error(LOG_ERROR_SECONDARY_ORDER,primaryOrderCreateDTO.getSecondaryOrderCode(), e.getMessage(),  e);
                throw new BizException(BasicCode.UNDEFINED_ERROR, "一级订单自动支付失败，" + e.getMessage());
            }
            return;
        }


        for (PrimaryOrderCreateDTO primaryOrderCreateDTO : submitPrimaryOrderDTO.getPrimaryOrderCreateList()) {
            String identifier = "";
            String bizResource = "createOrderMapTask:" + primaryOrderCreateDTO.getSecondaryOrderId();
            buildOrderCreateDTO(submitPrimaryOrderDTO, primaryOrderCreateDTO, identifier, bizResource, batchNumber);
        }
    }

    private void buildOrderCreateDTO(SubmitPrimaryOrderDTO submitPrimaryOrderDTO, PrimaryOrderCreateDTO primaryOrderCreateDTO, String identifier, String bizResource, String batchNumber) {
        try {
            identifier = redisLockService.lockFast(bizResource);
            primaryOrderCreateDTO.setOperatorId(submitPrimaryOrderDTO.getOperatorId());
            primaryOrderCreateDTO.setOperatorName(submitPrimaryOrderDTO.getOperatorName());
            primaryOrderCreateDTO.setBatchNumber(batchNumber);
            Boolean createFlag = orderMapTaskBiz.createOrderMapTask(submitPrimaryOrderDTO, primaryOrderCreateDTO);
            if (createFlag) {
                log.info("成功插入任务后发送到消息队列");
                messageService.sendMQ(primaryOrderCreateDTO, "com.ecommerce.order.map.task.notify");
            }
        } catch (DistributeLockException e) {
            log.error("重复操作: createOrderMapTask:" + JSON.toJSONString(primaryOrderCreateDTO));
        } catch (Exception e) {
            log.error("创建订单任务失败：{}", e);
        } finally {
            if (CsStringUtils.isNotEmpty(bizResource)) {
                redisLockService.unlock(bizResource, identifier);
            }
        }
    }

    /**
     * 批量设置销售区域
     *
     * @param primaryOrderList 一级订单列表
     */
    private void batchSetSaleRegion(List<PrimaryOrderListDTO> primaryOrderList) {
        Set<String> saleRegionIdSet = Sets.newHashSet();
        for (PrimaryOrderListDTO primaryOrderListDTO : primaryOrderList) {
            String saleRegion1 = primaryOrderListDTO.getSaleRegion1();
            if (CsStringUtils.isNotBlank(saleRegion1)) {
                saleRegionIdSet.add(saleRegion1);
            }
            String saleRegion2 = primaryOrderListDTO.getSaleRegion2();
            if (CsStringUtils.isNotBlank(saleRegion2)) {
                saleRegionIdSet.add(saleRegion2);
            }
            String saleRegion3 = primaryOrderListDTO.getSaleRegion3();
            if (CsStringUtils.isNotBlank(saleRegion3)) {
                saleRegionIdSet.add(saleRegion3);
            }
        }
        if (CollectionUtils.isEmpty(saleRegionIdSet)) return;
        //查询销售区域信息
        SaleRegionCondDTO saleRegionCondDTO = new SaleRegionCondDTO();
        saleRegionCondDTO.setSaleRegionIdList(Lists.newArrayList(saleRegionIdSet));
        List<SaleRegionSampleDTO> sampleDTOList = saleRegionService.batchQuerySaleRegion(saleRegionCondDTO);
        if (CollectionUtils.isEmpty(sampleDTOList)) return;
        //设置销售区域名称
        setAreaNameV2(primaryOrderList, sampleDTOList);
    }

    private static void setAreaNameV2(List<PrimaryOrderListDTO> primaryOrderList, List<SaleRegionSampleDTO> sampleDTOList) {
        Map<String, String> saleRegionId2NameMap = sampleDTOList.stream().collect(
                Collectors.toMap(SaleRegionSampleDTO::getSaleRegionId, SaleRegionSampleDTO::getSaleRegionName));
        for (PrimaryOrderListDTO primaryOrderListDTO : primaryOrderList) {
            if (CsStringUtils.isNotBlank(primaryOrderListDTO.getSaleRegion1())) {
                primaryOrderListDTO.setSaleRegionName1(saleRegionId2NameMap.get(primaryOrderListDTO.getSaleRegion1()));
            }
            if (CsStringUtils.isNotBlank(primaryOrderListDTO.getSaleRegion2())) {
                primaryOrderListDTO.setSaleRegionName2(saleRegionId2NameMap.get(primaryOrderListDTO.getSaleRegion2()));
            }
            if (CsStringUtils.isNotBlank(primaryOrderListDTO.getSaleRegion3())) {
                primaryOrderListDTO.setSaleRegionName3(saleRegionId2NameMap.get(primaryOrderListDTO.getSaleRegion3()));
            }
        }
    }

    /**
     * 批量设置orderDTO中的orderItems
     *
     * @param primaryOrderList 一级订单列表
     */
    private void batchSetOrderItems(List<PrimaryOrderListDTO> primaryOrderList) {
        if (CollectionUtils.isEmpty(primaryOrderList)) return;
        List<String> orderIds = primaryOrderList.stream().map(PrimaryOrderListDTO::getOrderId).toList();
        Condition orderItemExa = new Condition(OrderItem.class);
        orderItemExa.createCriteria()
                .andIn("orderId", orderIds)
                .andEqualTo("delFlg", false);
        List<OrderItem> orderItems = orderItemBiz.findByCondition(orderItemExa);
        if (CollectionUtils.isEmpty(orderItems)) return;

        Map<String, GoodsDTO> goodsId2DTOMap = Maps.newHashMap();
        List<String> goodsIds = orderItems.stream().map(OrderItem::getGoodsId).toList();
        ItemResult<List<GoodsDTO>> goodsListResult = goodsService.selectSimpleGoodsInfoByIds(goodsIds);
        if (goodsListResult != null && CollectionUtils.isNotEmpty(goodsListResult.getData())) {
            for (GoodsDTO goodsDTO : goodsListResult.getData()) {
                goodsId2DTOMap.put(goodsDTO.getGoodsId(), goodsDTO);
            }
        }

        Map<String, List<PrimaryOrderItemDTO>> orderId2ItemsListMap = orderItems.stream()
                .map(item -> {
                    PrimaryOrderItemDTO primaryOrderItemDTO = BeanConvertUtils.convert(item, PrimaryOrderItemDTO.class);
                    GoodsDTO goods = goodsId2DTOMap.get(primaryOrderItemDTO.getGoodsId());
                    if (goods != null) {
                        String[] images = goods.getImgs();
                        if (images != null && images.length > 0) {
                            primaryOrderItemDTO.setGoodsPic(images[0]); // 商品图片
                        }
                    }
                    return primaryOrderItemDTO;
                })
                .collect(Collectors.groupingBy(PrimaryOrderItemDTO::getOrderId));
        for (PrimaryOrderListDTO primaryOrderListDTO : primaryOrderList) {
            primaryOrderListDTO.setOrderItemList(orderId2ItemsListMap.get(primaryOrderListDTO.getOrderId()));
        }
    }

    /**
     * 批量设置支付单信息
     *
     * @param primaryOrderList 一级订单列表
     */
    private void batchSetOrderPayInfo(List<PrimaryOrderListDTO> primaryOrderList) {
        if (CollectionUtils.isEmpty(primaryOrderList)) return;
        List<String> orderIds = primaryOrderList.stream().map(PrimaryOrderListDTO::getOrderId).toList();
        //获取订单支付信息
        List<OrderPayinfoDTO> dbPayInfoDTOList = orderPayInfoBiz.queryByOrderIds(orderIds);
        if (CollectionUtils.isEmpty(dbPayInfoDTOList)) {
            return;
        }
        Map<String, List<OrderPayinfoDTO>> orderId2PayInfoMap =
                dbPayInfoDTOList.stream().collect(Collectors.groupingBy(OrderPayinfoDTO::getObjectId));
        for (PrimaryOrderListDTO primaryOrderListDTO : primaryOrderList) {
            setAmount(primaryOrderListDTO, orderId2PayInfoMap);
        }
    }

    private static void setAmount(PrimaryOrderListDTO primaryOrderListDTO, Map<String, List<OrderPayinfoDTO>> orderId2PayInfoMap) {
        //每一条订单的设置
        List<OrderPayinfoDTO> orderPayInfoList = orderId2PayInfoMap.get(primaryOrderListDTO.getOrderId());
        if (CollectionUtils.isEmpty(orderPayInfoList)) return;
        primaryOrderListDTO.setOrderPayinfo(orderPayInfoList);
        primaryOrderListDTO.setNeedPayment(Boolean.FALSE);
        BigDecimal orderPayedAmount = BigDecimal.ZERO;
        BigDecimal orderUnPayAmount = BigDecimal.ZERO;
        for (OrderPayinfoDTO payInfoDTO : orderPayInfoList) {
            //存在线下支付的支付中退款单则需要确认退款
            if (CsStringUtils.equals(payInfoDTO.getPayinfoWay(), PayWayEnum.OFFLINE.getCode()) &&
                    CsStringUtils.equals(payInfoDTO.getPayinfoStatus(), PayStatusEnum.IN_PAYMENT.getCode()) &&
                    CsStringUtils.equals(payInfoDTO.getPayinfoType(), PayTypeEnum.REFUND.code())) {
                primaryOrderListDTO.setConfirmRefundFlag(1);
            }
            //待支付金额
            if ((PayTypeEnum.SUPPLEMENT.code().equals(payInfoDTO.getPayinfoType())
                    || PayTypeEnum.SINGLE.code().equals(payInfoDTO.getPayinfoType())
                    || PayTypeEnum.GROUP.code().equals(payInfoDTO.getPayinfoType()))
                    && (PayStatusEnum.IN_PAYMENT.code().equals(payInfoDTO.getPayinfoStatus())
                    || PayStatusEnum.WAIT_PAYMENT.code().equals(payInfoDTO.getPayinfoStatus())
                    || PayStatusEnum.FAILED.code().equals(payInfoDTO.getPayinfoStatus()))) {
                orderUnPayAmount = orderUnPayAmount.add(payInfoDTO.getPayAmount());
                //是否需要支付
                primaryOrderListDTO.setNeedPayment(Boolean.TRUE);
            }
            //已支付金额
            if ((PayTypeEnum.SUPPLEMENT.code().equals(payInfoDTO.getPayinfoType())
                    || PayTypeEnum.SINGLE.code().equals(payInfoDTO.getPayinfoType())
                    || PayTypeEnum.GROUP.code().equals(payInfoDTO.getPayinfoType()))
                    && (PayStatusEnum.COMPLETED.code().equals(payInfoDTO.getPayinfoStatus()))) {
                orderPayedAmount = orderPayedAmount.add(payInfoDTO.getPayAmount());
            }
        }
        if (CsStringUtils.equals(primaryOrderListDTO.getOrderStatus(), OrderStatusEnum.CANCEL.code())) {
            orderUnPayAmount = BigDecimal.ZERO;
        }
        primaryOrderListDTO.setOrderPayedAmount(orderPayedAmount);
        primaryOrderListDTO.setOrderUnPayAmount(orderUnPayAmount);
    }

    /**
     * 批量设置ERP流向管控地址
     *
     * @param primaryOrderList 一级订单列表
     */
    private void batchSetERPAddress(List<PrimaryOrderListDTO> primaryOrderList) {
        if (CollectionUtils.isNotEmpty(primaryOrderList)) {
            List<String> orderIds = primaryOrderList.stream().map(PrimaryOrderListDTO::getOrderId).toList();
            // 获取ERP流向管控地址
            List<ERPAddressDTO> erpAddressList = orderErpAddressBiz.queryERPAddressListByOrderIds(orderIds);
            if (CollectionUtils.isNotEmpty(erpAddressList)) {
                Map<String, List<ERPAddressDTO>> orderId2ERPAddressMap =
                        erpAddressList.stream().collect(Collectors.groupingBy(ERPAddressDTO::getOrderId));
                for (PrimaryOrderListDTO primaryOrderListDTO : primaryOrderList) {
                    List<ERPAddressDTO> list = orderId2ERPAddressMap.get(primaryOrderListDTO.getOrderId());
                    if (CollectionUtils.isNotEmpty(list) && CsStringUtils.isNotBlank(list.get(0).getErpUnloadAddressId())) {
                        primaryOrderListDTO.setErpAddressDTO(list.get(0));
                    }
                }
            }
        }
    }

    /**
     * 批量设置二级订单买家信息
     *
     * @param primaryOrderList 一级订单列表
     */
    private void batchSetSecondaryBuyerInfo(List<PrimaryOrderListDTO> primaryOrderList) {
        if (CollectionUtils.isNotEmpty(primaryOrderList)) {
            List<String> orderIds = primaryOrderList.stream().map(PrimaryOrderListDTO::getOrderId).toList();
            // 获取二级订单信息
            List<SecondaryOrderInfoDTO> secondaryOrderInfoDTOList = orderProxyMapBiz.querySecondaryOrderInfoListByPrimaryOrderIds(orderIds);
            if (CollectionUtils.isNotEmpty(secondaryOrderInfoDTOList)) {
                Map<String, List<SecondaryOrderInfoDTO>> orderId2SecondaryOrderInfoMap =
                        secondaryOrderInfoDTOList.stream().collect(Collectors.groupingBy(SecondaryOrderInfoDTO::getPrimaryOrderId));
                for (PrimaryOrderListDTO primaryOrderListDTO : primaryOrderList) {
                    List<SecondaryOrderInfoDTO> list = orderId2SecondaryOrderInfoMap.get(primaryOrderListDTO.getOrderId());
                    if (CollectionUtils.isNotEmpty(list) && CsStringUtils.isNotBlank(list.get(0).getBuyerName())) {
                        primaryOrderListDTO.setSecondaryBuyerName(list.get(0).getBuyerName());
                    }
                }
            }
        }
    }

    @Override
    public PageData<PrimaryOrderListDTO> pageQueryPrimaryOrderList(PageQuery<PrimaryOrderQueryDTO> pageQuery) {
        log.info("pageQueryPrimaryOrderList:" + JSON.toJSONString(pageQuery));
        //二级订单号查询转换为一级订单号查询
        PageInfo<PrimaryOrderListDTO> pageInfo = orderInfoBiz.queryPrimaryOrderList(pageQuery);
        if (CollectionUtils.isEmpty(pageInfo.getList())) {
            return new PageData<>(new ArrayList<>());
        }
        //批量设置支付信息
        batchSetOrderPayInfo(pageInfo.getList());
        //批量设置销售区域信息
        batchSetSaleRegion(pageInfo.getList());
        //批量设置订单商品行信息
        batchSetOrderItems(pageInfo.getList());
        //批量设置ERP流向管控地址
        batchSetERPAddress(pageInfo.getList());
        //批量设置订单扩展信息
        batchSetOrderInfoExt(pageInfo.getList());
        //获取映射的二级订单号
        Map<String, PrimaryOrderListDTO> primaryOrderListMap = Maps.newHashMap();
        pageInfo.getList().stream().forEach((PrimaryOrderListDTO primaryOrderListDTO) -> {
            primaryOrderListDTO.setOrderProxyMapList(Lists.newArrayList());
            primaryOrderListMap.put(primaryOrderListDTO.getOrderId(), primaryOrderListDTO);
        });
        List<OrderProxyMap> orderProxyMapList = orderProxyMapBiz.findByPrimaryOrderIds(primaryOrderListMap.keySet());
        if (CollectionUtils.isNotEmpty(orderProxyMapList)) {
            orderProxyMapList.stream().forEach(orderProxyMap -> {
                if (primaryOrderListMap.get(orderProxyMap.getPrimaryOrderId()) != null) {
                    OrderProxyMapDTO orderProxyMapDTO = new OrderProxyMapDTO();
                    BeanUtils.copyProperties(orderProxyMap, orderProxyMapDTO);
                    primaryOrderListMap.get(orderProxyMap.getPrimaryOrderId())
                            .getOrderProxyMapList().add(orderProxyMapDTO);
                }
            });
        }
        //按创建时间排序
        List<PrimaryOrderListDTO> primaryOrderList = Lists.newArrayList(primaryOrderListMap.values());
        Collections.sort(primaryOrderList, (o1, o2) -> o2.getCreateTime().compareTo(o1.getCreateTime()));

        return new PageData<>(pageInfo);
    }

    /**
     * 批量设置订单扩展信息
     * @param orderList 订单列表对象
     */
    public void batchSetOrderInfoExt(List<PrimaryOrderListDTO> orderList) {
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }
        List<String> orderIds = orderList.stream().map(PrimaryOrderListDTO::getOrderId).toList();
        List<OrderInfoExtDTO> orderInfoExtList = orderInfoExtBiz.findByOrderIds(orderIds);
        Map<String, OrderInfoExtDTO> orderInfoExtMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(orderInfoExtList)) {
            orderInfoExtList.stream().forEach(orderInfoExtDTO ->
                    orderInfoExtMap.put(orderInfoExtDTO.getOrderId(), orderInfoExtDTO));
        }
        orderList.stream().forEach(orderDTO -> {
            if (orderInfoExtMap.get(orderDTO.getOrderId()) != null) {
                orderDTO.setOrderInfoExtDTO(orderInfoExtMap.get(orderDTO.getOrderId()));
                return;
            }
            OrderInfoExtDTO orderInfoExtDTO = new OrderInfoExtDTO();
            orderInfoExtBiz.defaultOrderExtDTO(orderInfoExtDTO);
            orderDTO.setOrderInfoExtDTO(orderInfoExtDTO);
        });
    }

    @Override
    public List<OrderMapResultDTO> queryOrderMapResultList(OrderMapResultQueryDTO orderMapResultQueryDTO) {
        log.info("queryOrderMapResultList:" + JSON.toJSONString(orderMapResultQueryDTO));
        //开发环境不验证销售区域权限
        if (CsStringUtils.equals(profile.toLowerCase(), "dev") || CsStringUtils.equals(profile.toLowerCase(), "test")) {
            orderMapResultQueryDTO.setRegionList(null);
        }
        if (CsStringUtils.isBlank(orderMapResultQueryDTO.getBatchNumber()) &&
                CsStringUtils.isBlank(orderMapResultQueryDTO.getSellerId())) {
            throw new BizException(BasicCode.INVALID_PARAM, "提交批次号或卖家");
        }
        List<OrderMapTask> orderMapTaskList = orderMapTaskBiz.queryOrderMapResultList(orderMapResultQueryDTO);
        if (CollectionUtils.isEmpty(orderMapTaskList)) {
            return Lists.newArrayList();
        }
        Map<String, OrderMapResultDTO> orderMapResultMap = Maps.newHashMap();
        orderMapTaskList.stream().forEach(orderMapTask -> {
            OrderMapResultDTO orderMapResultDTO = new OrderMapResultDTO();
            BeanUtils.copyProperties(orderMapTask, orderMapResultDTO);
            //转换一级订单创建对象
            orderMapResultDTO.setPrimaryOrderCreateDTO(JSON.parseObject(orderMapTask.getBizData(), PrimaryOrderCreateDTO.class));
            orderMapResultMap.put(orderMapTask.getSecondaryOrderId(), orderMapResultDTO);
        });
        SecondaryOrderQueryDTO secondaryOrderQueryDTO = new SecondaryOrderQueryDTO();
        secondaryOrderQueryDTO.setOrderIdList(Lists.newArrayList(orderMapResultMap.keySet()));
        List<SecondaryOrderListDO> secondaryOrderList = orderInfoBiz.querySecondaryOrderByCondition(secondaryOrderQueryDTO);
        if (CollectionUtils.isNotEmpty(secondaryOrderList)) {
            secondaryOrderList.stream().forEach(secondaryOrderListDO -> {
                if (orderMapResultMap.get(secondaryOrderListDO.getOrderId()) != null) {
                    OrderMapResultDTO orderMapResultDTO = orderMapResultMap.get(secondaryOrderListDO.getOrderId());
                    BeanUtils.copyProperties(secondaryOrderListDO, orderMapResultDTO);
                }
            });
        }

        return Lists.newArrayList(orderMapResultMap.values());
    }

    @Override
    public PageData<OrderSubmitResultDTO> pageQueryOrderSubmitList(PageQuery<OrderSubmitQueryDTO> pageQuery) {
        log.info("pageQueryOrderSubmitList:" + JSON.toJSONString(pageQuery));
        OrderSubmitQueryDTO orderSubmitQueryDTO = pageQuery.getQueryDTO();
        //开发环境不验证销售区域权限
        if (CsStringUtils.equals(profile.toLowerCase(), "dev") || CsStringUtils.equals(profile.toLowerCase(), "test")) {
            orderSubmitQueryDTO.setRegionList(null);
        }
        PageInfo<OrderSubmitResultDO> pageInfo = orderMapTaskBiz.pageQueryOrderSubmitList(pageQuery);
        if (CollectionUtils.isEmpty(pageInfo.getList())) {
            return new PageData<>(new ArrayList<>());
        }
        Map<String, OrderSubmitResultDTO> orderSubmitResultMap = Maps.newHashMap();
        pageInfo.getList().stream().forEach(orderSubmitResultDO -> {
            OrderSubmitResultDTO orderSubmitResultDTO = new OrderSubmitResultDTO();
            BeanUtils.copyProperties(orderSubmitResultDO, orderSubmitResultDTO);
            //格式化时间
            orderSubmitResultDTO.setCreateTime(DateUtil.format(orderSubmitResultDO.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
            orderSubmitResultMap.put(orderSubmitResultDO.getBatchNumber(), orderSubmitResultDTO);
        });
        //查询执行状态
        orderSubmitQueryDTO.setBatchNumberList(Lists.newArrayList(orderSubmitResultMap.keySet()));
        List<OrderSubmitResultDO> orderSubmitStatusList = orderMapTaskBiz.countOrderSubmitStatus(orderSubmitQueryDTO);
        if (CollectionUtils.isNotEmpty(orderSubmitStatusList)) {
            orderSubmitStatusList.stream().forEach(orderSubmitResultDO -> {
                OrderSubmitResultDTO orderSubmitResultDTO = orderSubmitResultMap.get(orderSubmitResultDO.getBatchNumber());
                if (CsStringUtils.equals(orderSubmitResultDO.getTaskStatus(), PrimaryMapStatusEnum.PROCESSING.getCode())) {
                    orderSubmitResultDTO.setProcessingNum(orderSubmitResultDTO.getProcessingNum() + orderSubmitResultDO.getTotalNum());
                } else if (CsStringUtils.equals(orderSubmitResultDO.getTaskStatus(), PrimaryMapStatusEnum.SUBMIT_FAIL.getCode())) {
                    orderSubmitResultDTO.setFailedNum(orderSubmitResultDTO.getFailedNum() + orderSubmitResultDO.getTotalNum());
                } else if (CsStringUtils.equals(orderSubmitResultDO.getTaskStatus(), PrimaryMapStatusEnum.SUBMIT_SUCCESS.getCode())) {
                    orderSubmitResultDTO.setFailedNum(orderSubmitResultDTO.getFailedNum() + orderSubmitResultDO.getTotalNum());
                } else {
                    orderSubmitResultDTO.setSucceededNum(orderSubmitResultDTO.getSucceededNum() + orderSubmitResultDO.getTotalNum());
                }
            });
        }

        //返回分页数据
        PageData<OrderSubmitResultDTO> pageData = new PageData<>();
        pageData.setPageNum(pageInfo.getPageNum());
        pageData.setPageSize(pageInfo.getPageSize());
        pageData.setPages(pageInfo.getPages());
        pageData.setTotal(pageInfo.getTotal());
        //再次排序
        List<OrderSubmitResultDTO> orderSubmitResultList = Lists.newArrayList(orderSubmitResultMap.values());
        Collections.sort(orderSubmitResultList, (o1, o2) -> o2.getCreateTime().compareTo(o1.getCreateTime()));
        pageData.setList(orderSubmitResultList);

        return pageData;
    }

    @Override
    public List<String> batchPayPrimaryOrder(BatchPayPrimaryOrderDTO batchPayPrimaryOrderDTO) {
        log.info("batchPayPrimaryOrder:" + JSON.toJSONString(batchPayPrimaryOrderDTO));
        if (CollectionUtils.isEmpty(batchPayPrimaryOrderDTO.getPrimaryOrderIdList())) {
            throw new BizException(BasicCode.INVALID_PARAM, "一级订单ID");
        }
        //批量支付结果对象
        List<String> payResultList = Lists.newArrayList();
        batchPayPrimaryOrderDTO.getPrimaryOrderIdList().stream().forEach(primaryOrderId -> {
            String identifier = "";
            String bizResource = "primaryOrderAutoPay:" + primaryOrderId;
            String orderCode = "";
            try {
                identifier = redisLockService.lockFast(bizResource);
                //获取支付单
                OrderPayinfo orderPayInfo = orderPayInfoBiz.getOrderPay(primaryOrderId);
                orderCode = orderPayInfo.getObjectCode();
                if (CsStringUtils.equals(orderPayInfo.getPayinfoStatus(), PayStatusEnum.COMPLETED.getCode())) {
                    throw new BizException(BasicCode.UNDEFINED_ERROR, "订单" + orderCode + "已经支付成功，请勿重复支付");
                }
                OrderPayinfoDTO orderPayInfoDTO = new OrderPayinfoDTO();
                BeanUtils.copyProperties(orderPayInfo, orderPayInfoDTO);
                primaryOrderAutoPay(orderPayInfoDTO, batchPayPrimaryOrderDTO.getOperatorId());
            } catch (BizException be) {
                log.error("订单" + orderCode + "支付失败{}", be);
                payResultList.add("订单" + orderCode + "支付失败，" + be.getMessage());
            } catch (Exception e) {
                log.error("订单" + orderCode + "支付失败{}", e);
                payResultList.add("订单" + orderCode + "支付失败");
            } finally {
                if (CsStringUtils.isNotEmpty(bizResource)) {
                    redisLockService.unlock(bizResource, identifier);
                }
            }
        });

        return payResultList;
    }

    @Override
    public void retrySubmitPrimaryOrder(RetrySubmitPrimaryOrderDTO retrySubmitPrimaryOrderDTO) {
        log.info("retrySubmitPrimaryOrder:" + JSON.toJSONString(retrySubmitPrimaryOrderDTO));
        if (CsStringUtils.isBlank(retrySubmitPrimaryOrderDTO.getOrderTaskId())) {
            throw new BizException(BasicCode.INVALID_PARAM, "订单提交任务ID");
        }
        String identifier = "";
        String bizResource = "retrySubmitPrimaryOrder:" + retrySubmitPrimaryOrderDTO.getOrderTaskId();
        try {
            identifier = redisLockService.lockFast(bizResource);
            OrderMapTask orderMapTask = orderMapTaskBiz.get(retrySubmitPrimaryOrderDTO.getOrderTaskId());
            log.info("retrySubmitPrimaryOrder_orderMapTask:" + JSON.toJSONString(orderMapTask));
            Set<String> ignoreStatusSet = Sets.newHashSet(
                    PrimaryMapStatusEnum.PROCESSING.getCode(),
                    PrimaryMapStatusEnum.PAY_SUCCESS.getCode());
            if (ignoreStatusSet.contains(orderMapTask.getTaskStatus())) {
                return;
            }
            //转换执行参数
            PrimaryOrderCreateDTO primaryOrderCreateDTO = JSON.parseObject(orderMapTask.getBizData(), PrimaryOrderCreateDTO.class);
            //提交失败
            if (CsStringUtils.equals(PrimaryMapStatusEnum.SUBMIT_FAIL.getCode(), orderMapTask.getTaskStatus())) {
                OrderMapResultDTO orderMapResultDTO = submitProxyPrimaryOrder(primaryOrderCreateDTO);
                if (!CsStringUtils.equals(PrimaryMapStatusEnum.PAY_SUCCESS.getCode(), orderMapResultDTO.getTaskStatus())) {
                    throw new BizException(BasicCode.UNDEFINED_ERROR, orderMapResultDTO.getResultComment());
                }
            }
            //提交成功
            if (CsStringUtils.equals(PrimaryMapStatusEnum.SUBMIT_SUCCESS.getCode(), orderMapTask.getTaskStatus())) {
                OrderPayinfo orderPayInfo = orderPayInfoBiz.getOrderPay(primaryOrderCreateDTO.getPrimaryOrderId());
                if (orderPayInfo == null) {
                    throw new BizException(BasicCode.UNDEFINED_ERROR, "订单" +
                            primaryOrderCreateDTO.getPrimaryOrderCode() + "当前状态不可支付");
                }
                if (CsStringUtils.equals(orderPayInfo.getPayinfoStatus(), PayStatusEnum.COMPLETED.getCode())) {
                    throw new BizException(BasicCode.UNDEFINED_ERROR, "订单" +
                            primaryOrderCreateDTO.getPrimaryOrderCode() + "已经支付成功，请勿重复支付");
                }
                OrderPayinfoDTO orderPayInfoDTO = new OrderPayinfoDTO();
                BeanUtils.copyProperties(orderPayInfo, orderPayInfoDTO);
                primaryOrderAutoPay(orderPayInfoDTO, primaryOrderCreateDTO.getOperatorId());
            }
        }  catch (BizException be) {
            log.error("执行失败，{}", be);
            throw new BizException(BasicCode.UNDEFINED_ERROR, be.getMessage());
        } catch (Exception e) {
            log.error("执行失败，{}", e);
            throw new BizException(BasicCode.UNDEFINED_ERROR, "订单执行失败！");
        } finally {
            if (CsStringUtils.isNotEmpty(bizResource)) {
                redisLockService.unlock(bizResource, identifier);
            }
        }
    }

    @Override
    public List<BuyerSelectorDTO> querySecondaryBuyerSelectorList(SecondaryOrderQueryDTO pageQuery) {
        //开发环境不验证销售区域权限
        if (CsStringUtils.equals(profile.toLowerCase(), "dev") || CsStringUtils.equals(profile.toLowerCase(), "test")) {
            pageQuery.setRegionList(null);
        }

        return orderInfoBiz.querySecondaryBuyerSelectorList(pageQuery);
    }

    @Override
    public List<StoreSelectorDTO> querySecondaryStoreSelectorList(SecondaryOrderQueryDTO pageQuery) {
        //开发环境不验证销售区域权限
        if (CsStringUtils.equals(profile.toLowerCase(), "dev") || CsStringUtils.equals(profile.toLowerCase(), "test")) {
            pageQuery.setRegionList(null);
        }

        return orderInfoBiz.querySecondaryStoreSelectorList(pageQuery);
    }

    @Override
    public List<GoodsSelectorDTO> querySecondaryGoodsSelectorList(SecondaryOrderQueryDTO pageQuery) {
        //开发环境不验证销售区域权限
        if (CsStringUtils.equals(profile.toLowerCase(), "dev") || CsStringUtils.equals(profile.toLowerCase(), "test")) {
            pageQuery.setRegionList(null);
        }

        return orderInfoBiz.querySecondaryGoodsSelectorList(pageQuery);
    }

    @Override
    public PageData<OrderMapResultDTO> pageQueryOrderMapResultList(PageQuery<OrderMapResultQueryDTO> pageQuery) {
        log.info("pageQueryOrderMapResultList:" + JSON.toJSONString(pageQuery));
        int pageNum = pageQuery.getPageNum();
        int pageSize = pageQuery.getPageSize();
        OrderMapResultQueryDTO orderMapResultQueryDTO = pageQuery.getQueryDTO() == null ?
                new OrderMapResultQueryDTO() : pageQuery.getQueryDTO();
        //开发环境不验证销售区域权限
        if (CsStringUtils.equals(profile.toLowerCase(), "dev") || CsStringUtils.equals(profile.toLowerCase(), "test")) {
            orderMapResultQueryDTO.setRegionList(null);
        }
        if (CsStringUtils.isBlank(orderMapResultQueryDTO.getSellerId())) {
            throw new BizException(BasicCode.INVALID_PARAM, "卖家");
        }
        PageInfo<OrderMapTask> pageInfo = startPage(pageNum, pageSize).doSelectPageInfo(
                () -> orderMapTaskBiz.queryOrderMapResultList(orderMapResultQueryDTO));
        if (CollectionUtils.isEmpty(pageInfo.getList())) {
            return new PageData<>(new ArrayList<>());
        }
        Map<String, OrderMapResultDTO> orderMapResultMap = Maps.newHashMap();
        pageInfo.getList().stream().forEach(orderMapTask -> {
            OrderMapResultDTO orderMapResultDTO = new OrderMapResultDTO();
            BeanUtils.copyProperties(orderMapTask, orderMapResultDTO);
            //转换一级订单创建对象
            orderMapResultDTO.setPrimaryOrderCreateDTO(JSON.parseObject(orderMapTask.getBizData(), PrimaryOrderCreateDTO.class));
            orderMapResultMap.put(orderMapTask.getSecondaryOrderId(), orderMapResultDTO);
        });
        SecondaryOrderQueryDTO secondaryOrderQueryDTO = new SecondaryOrderQueryDTO();
        secondaryOrderQueryDTO.setOrderIdList(Lists.newArrayList(orderMapResultMap.keySet()));
        List<OrderInfo> secondaryOrderList = orderInfoBiz.findByIds(orderMapResultMap.keySet());
        if (CollectionUtils.isNotEmpty(secondaryOrderList)) {
            secondaryOrderList.stream().forEach(secondaryOrderListDO -> {
                if (orderMapResultMap.get(secondaryOrderListDO.getOrderId()) != null) {
                    OrderMapResultDTO orderMapResultDTO = orderMapResultMap.get(secondaryOrderListDO.getOrderId());
                    BeanUtils.copyProperties(secondaryOrderListDO, orderMapResultDTO);
                    orderMapResultDTO.setDeliveryWay(secondaryOrderListDO.getDeliverWay());
                }
            });
        }
        List<OrderMapResultDTO> orderMapResultList = Lists.newArrayList(orderMapResultMap.values());
        Collections.sort(orderMapResultList, (o1, o2) -> o2.getCreateTime().compareTo(o1.getCreateTime()));
        //返回分页数据
        PageData < OrderMapResultDTO > pageData = new PageData<>();
        pageData.setPageNum(pageInfo.getPageNum());
        pageData.setPageSize(pageInfo.getPageSize());
        pageData.setPages(pageInfo.getPages());
        pageData.setTotal(pageInfo.getTotal());
        pageData.setList(orderMapResultList);

        return pageData;
    }
}
