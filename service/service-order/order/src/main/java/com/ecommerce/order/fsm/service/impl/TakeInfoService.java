package com.ecommerce.order.fsm.service.impl;

import com.alibaba.fastjson.JSON;
import com.ecommerce.base.api.dto.ReceivingAddressDTO;
import com.ecommerce.base.api.dto.ReceivingAddressSimpleDTO;
import com.ecommerce.base.api.service.IReceivingAddressService;
import com.ecommerce.base.api.service.IWarehouseService;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BasicRuntimeException;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.exception.DistributeLockException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.common.service.ILockService;
import com.ecommerce.common.utils.ArithUtils;
import com.ecommerce.common.utils.BeanConvertUtils;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.goods.api.dto.GoodsCategorySimpleDTO;
import com.ecommerce.goods.api.dto.GoodsDTO;
import com.ecommerce.goods.api.service.IContractService;
import com.ecommerce.goods.api.service.IGoodsCategoryService;
import com.ecommerce.goods.api.service.IGoodsService;
import com.ecommerce.logistics.api.dto.shipping.ShippingInfoDTO;
import com.ecommerce.logistics.api.dto.shipping.ShippingInfoQueryDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleBaseDataDTO;
import com.ecommerce.logistics.api.enums.DeliveryTimeRangeEnum;
import com.ecommerce.logistics.api.enums.PickingBillTypeEnum;
import com.ecommerce.logistics.api.enums.ShippingEnum;
import com.ecommerce.logistics.api.enums.TransportToolTypeEnum;
import com.ecommerce.logistics.api.enums.UserRoleEnum;
import com.ecommerce.logistics.api.service.IShippingInfoService;
import com.ecommerce.logistics.api.service.IVehicleService;
import com.ecommerce.member.api.dto.account.AccountSimpleDTO;
import com.ecommerce.member.api.dto.member.MemberSimpleDTO;
import com.ecommerce.member.api.dto.member.enums.MemberPlatform;
import com.ecommerce.member.api.service.IAccountService;
import com.ecommerce.member.api.service.IMemberService;
import com.ecommerce.open.api.dto.MessageConfigCodeEnum;
import com.ecommerce.order.api.dto.OrderDTO;
import com.ecommerce.order.api.dto.OrderInfoExtDTO;
import com.ecommerce.order.api.dto.OverviewReqDTO;
import com.ecommerce.order.api.dto.TakeCarrierDTO;
import com.ecommerce.order.api.dto.TakeInfoDTO;
import com.ecommerce.order.api.dto.TakeInfoExportDTO;
import com.ecommerce.order.api.dto.TakeInfoOverviewDTO;
import com.ecommerce.order.api.dto.TakeInfoSearchDTO;
import com.ecommerce.order.api.dto.TakeItemDTO;
import com.ecommerce.order.api.dto.TakeShipDTO;
import com.ecommerce.order.api.dto.TakeStatusStatisticDTO;
import com.ecommerce.order.api.dto.TakeVehicleDTO;
import com.ecommerce.order.api.dto.TrContractDTO;
import com.ecommerce.order.api.dto.base.PageQuery;
import com.ecommerce.order.api.dto.base.SendSMSMessageDTO;
import com.ecommerce.order.api.dto.erp.ShipBillERPInfoDTO;
import com.ecommerce.order.api.dto.logistics.ShipSimpleDTO;
import com.ecommerce.order.api.dto.proxy.map.error.ProxyMapErrorDTO;
import com.ecommerce.order.api.dto.proxy.map.error.ProxyMapErrorSearchDTO;
import com.ecommerce.order.api.dto.take.NeedBuyerPayDTO;
import com.ecommerce.order.api.dto.take.TakeInfoCreateDTO;
import com.ecommerce.order.api.dto.take.TakeInfoItemCreateDTO;
import com.ecommerce.order.api.enums.OrderStatusEnum;
import com.ecommerce.order.api.enums.PayStatusEnum;
import com.ecommerce.order.api.enums.ProxyBillTypeEnum;
import com.ecommerce.order.api.enums.ProxyOrderTypeEnum;
import com.ecommerce.order.api.enums.TakeCancelType;
import com.ecommerce.order.api.enums.TakeCloseType;
import com.ecommerce.order.api.enums.TakeCreateType;
import com.ecommerce.order.api.enums.TakeDeleteType;
import com.ecommerce.order.api.enums.TakeStatus;
import com.ecommerce.order.api.enums.TakeTimeQuantumEnum;
import com.ecommerce.order.biz.IOrderProxyMapBiz;
import com.ecommerce.order.biz.ITakeInfoValidateBiz;
import com.ecommerce.order.biz.ITakeProxyMapBiz;
import com.ecommerce.order.biz.fsm.IOrderInfoBiz;
import com.ecommerce.order.biz.fsm.IOrderInfoExtBiz;
import com.ecommerce.order.biz.fsm.IOrderItemBiz;
import com.ecommerce.order.biz.fsm.IOrderPayinfoBiz;
import com.ecommerce.order.biz.fsm.IProxyMapErrorBiz;
import com.ecommerce.order.biz.fsm.ITakeInfoBiz;
import com.ecommerce.order.biz.fsm.ITakeItemBiz;
import com.ecommerce.order.biz.impl.fsm.TakeInfoBiz;
import com.ecommerce.order.dao.dto.TakeAutoCreateDTO;
import com.ecommerce.order.dao.vo.OrderInfo;
import com.ecommerce.order.dao.vo.OrderItem;
import com.ecommerce.order.dao.vo.OrderPayinfo;
import com.ecommerce.order.dao.vo.OrderProxyMap;
import com.ecommerce.order.dao.vo.ProxyMapError;
import com.ecommerce.order.dao.vo.TakeInfo;
import com.ecommerce.order.dao.vo.TakeItem;
import com.ecommerce.order.dao.vo.TakeProxyMap;
import com.ecommerce.order.exception.TakeInfoCode;
import com.ecommerce.order.fsm.StateMachineConstants;
import com.ecommerce.order.fsm.domain.StatusModelDO;
import com.ecommerce.order.fsm.even.TakeChangeEventEnum;
import com.ecommerce.order.fsm.manager.TakeStateMachineEventManager;
import com.ecommerce.order.fsm.service.IOrderCommonService;
import com.ecommerce.order.fsm.service.IOrderQueryService;
import com.ecommerce.order.fsm.service.ITakeInfoService;
import com.ecommerce.order.fsm.status.TakeStatusEnum;
import com.ecommerce.order.service.message.SMSMessageFactoryContext;
import com.ecommerce.order.util.DigestUtils;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TakeInfoService implements ITakeInfoService {

    public static final String CONFIRM_TAKE_INFO = "confirmTakeInfo:";
    public static final String INVALID_PARAM_NO_PERMISSION_MSG = "你无权操作该发货单";
    public static final String UNDEFINED_ERROR_REFRESH_AGAIN_MSG = "有其他用户正在操作，请刷新页面后重试";

    @Autowired
    private IOrderInfoBiz orderInfoBiz;
    @Autowired
    private IOrderInfoExtBiz orderInfoExtBiz;
    @Autowired
    private IOrderPayinfoBiz orderPayinfoBiz;
    @Autowired
    private IOrderItemBiz orderItemBiz;
    @Autowired
    private ITakeInfoBiz takeInfoBiz;
    @Autowired
    private ITakeItemBiz takeItemBiz;
    @Autowired
    private ITakeProxyMapBiz takeProxyMapBiz;
    @Autowired
    private IProxyMapErrorBiz proxyMapErrorBiz;
    @Autowired
    private IOrderProxyMapBiz orderProxyMapBiz;
    @Autowired
    private ITakeInfoValidateBiz takeInfoValidateBiz;
    @Autowired
    private IOrderQueryService orderQueryService;
    @Autowired
    private IMemberService memberService;
    @Autowired
    private IAccountService accountService;
    @Autowired
    private IWarehouseService warehouseService;
    @Autowired
    private IGoodsService goodsService;
    @Autowired
    private IVehicleService vehicleService;
    @Autowired
    private SMSMessageFactoryContext smsMessageFactoryContext;
    @Autowired
    private ILockService redisLockService;
    @Autowired
    private IOrderCommonService orderCommonService;
    @Autowired
    private TakeStateMachineEventManager takeStateMachineEventManager;
    @Autowired
    private IReceivingAddressService receivingAddressService;
    @Autowired
    private IShippingInfoService shippingInfoService;
    @Autowired
    private IGoodsCategoryService goodsCategoryService;
    @Autowired
    private IContractService contractService;

    private static final String TAKE_CODE = "takeCode";

    /**
     * 创建前获取发货单信息
     */
    @Override
    public TakeInfoDTO getTakeInfoForCreate(String orderId, String operator) {
        //获取订单信息，不存在则抛异常
        OrderInfo orderInfo = orderInfoBiz.getOrderInfoById(orderId);
        TakeInfoDTO result = BeanConvertUtils.convert(orderInfo,TakeInfoDTO.class);
        OrderInfoExtDTO orderExtInfo = orderInfoExtBiz.findByOrderId(orderId);
        result.setTransportType(orderExtInfo.getTransportType());
        result.setBillPaymentType(orderExtInfo.getBillPaymentType());
        //设置收货人姓名 如果存在
        setName(orderInfo, result);
        result.setTakeItems(Lists.newArrayList());
        result.setAssignBuyerCarrierFlag(false);//重新初始化默认值

        List<String> transportIdList = Lists.newArrayList();
        Map<String,String[]> goodsImgs = Maps.newHashMap();

        List<OrderItem> orderItemList = orderItemBiz.findByOrderId(orderId);
        if( CollectionUtils.isEmpty(orderItemList)){
            throw new BizException(BasicCode.CUSTOM_ERROR,"订单商品未找到");
        }
        Set<String> goodsIds = orderItemList.stream().map(OrderItem::getGoodsId).filter(CsStringUtils::isNotBlank).collect(Collectors.toSet());
        ItemResult<List<GoodsDTO>> goodsListItemResult = goodsService.selectSimpleGoodsInfoByIds(Lists.newArrayList(goodsIds));
        if( goodsListItemResult != null && CollectionUtils.isNotEmpty(goodsListItemResult.getData()) ){
            goodsListItemResult.getData().forEach(item->{
                transportIdList.add(item.getLogistics());
                goodsImgs.put(item.getGoodsId(),item.getImgs());
            });
        }

        //设置发货单货物子项
        for (OrderItem orderItem : orderItemList) {
            TakeItemDTO takeItem = new TakeItemDTO();
            BeanConvertUtils.copyProperties(orderItem, takeItem);
            takeItem.setOrderItemQuantity(orderItem.getItemQuantity());
            takeItem.setCanRequestQuantity(orderItem.getItemcantakeQuantity());
            //前端显示需要
            takeItem.setOrderItemQuantity(ArithUtils.add(takeItem.getOrderItemQuantity(),orderExtInfo.getLubricityQuantity()));
            if (CsStringUtils.isBlank(takeItem.getResourceName())) {
                takeItem.setResourceName(orderItem.getGoodsName());
            }
            if(goodsImgs.containsKey(orderItem.getGoodsId())){
                takeItem.setGoodsImgs(goodsImgs.get(orderItem.getGoodsId()));
            }
            if (goodsListItemResult != null && CollectionUtils.isNotEmpty(goodsListItemResult.getData())) {
                takeItem.setTransportId(goodsListItemResult.getData().stream().filter(i -> CsStringUtils.equals(i.getGoodsId(), takeItem.getGoodsId())).findFirst().orElse(new GoodsDTO()).getLogistics());
            }
            if (takeItem.getCanRequestQuantity().compareTo(BigDecimal.ZERO) > 0) {
                result.getTakeItems().add(takeItem);
            }
        }
        //是否需要流量监控(),1：是，2：否
        Integer flowControlCount = (int)orderItemList.stream().filter(item -> item.getIfFlowControl() != null && item.getIfFlowControl()).count();
        Integer ecFlowControlCount = (int)orderItemList.stream().filter(item -> item.getNeedECMonitor() != null && item.getNeedECMonitor()).count();
        result.setFlowControlCount(flowControlCount);
        result.setEcFlowControlCount(ecFlowControlCount);

        result.setTransportIdList(transportIdList);
        result.setStoreAddress(orderItemList.get(0).getStoreAddress());
        result.setStoreId(orderItemList.get(0).getStoreId());
        result.setStoreName(orderItemList.get(0).getStoreName());
        result.setStoreType(orderItemList.get(0).getStoreType());
        setParams(result, orderInfo, transportIdList, ecFlowControlCount);
        result.setTakeTime(new Date());
        result.setTakeTimeQuantum(DeliveryTimeRangeEnum.WHOLE_DAY.getCode());
        updateEnumName(result);
        updateButtonStatus(result);

        result.setCanSellerCreate(true);
        result.setCanBuyerCreate(true);
        return result;
    }

    private void setName(OrderInfo orderInfo, TakeInfoDTO result) {
        if (CsStringUtils.isNotBlank(orderInfo.getAddressId())) {
            ReceivingAddressDTO receivingAddressDTO = receivingAddressService.findById(orderInfo.getAddressId());
            if (receivingAddressDTO != null) {
                result.setReceiverName(receivingAddressDTO.getConsigneeName());
            }
        }
    }

    private void setParams(TakeInfoDTO result, OrderInfo orderInfo, List<String> transportIdList, Integer ecFlowControlCount) {
        if(TransportToolTypeEnum.ROAD_TRANSPORT.getCode().equals(result.getTransportType())) {
            //汽运设置车辆信息
            ItemResult<List<VehicleBaseDataDTO>> buyerCars = vehicleService.queryVehicleBaseData(orderInfo.getBuyerId());
            if (buyerCars != null && CollectionUtils.isNotEmpty(buyerCars.getData())) {
                //开始筛选:
                List<VehicleBaseDataDTO> vehicleList = buyerCars.getData();
                //只要买家车辆
                vehicleList.removeIf(item -> !integerEqual(item.getUserType(), UserRoleEnum.BUYER.getCode()));
                //不要禁用的车辆
                vehicleList.removeIf(item -> integerEqual(item.getDisableFlg(), 1));
                //如果商品有运输品类,筛选出运输品类可配送的车辆
                setType(transportIdList, ecFlowControlCount, vehicleList);
                result.setBuyerCars(BeanConvertUtils.convertList(vehicleList, com.ecommerce.order.api.dto.logistics.VehicleBaseDataDTO.class));
            }
        }else if(TransportToolTypeEnum.WATER_TRANSPORT.getCode().equals(result.getTransportType())){
            result.setBuyerShips(Lists.newArrayList());
            com.ecommerce.logistics.api.dto.base.PageQuery<ShippingInfoQueryDTO> pageQuery = new com.ecommerce.logistics.api.dto.base.PageQuery<>();
            pageQuery.setPageNum(1);
            pageQuery.setPageSize(100);
            ShippingInfoQueryDTO shippingInfoQueryDTO = new ShippingInfoQueryDTO();
            shippingInfoQueryDTO.setMemberId(orderInfo.getBuyerId());
            shippingInfoQueryDTO.setUserRole(UserRoleEnum.BUYER.getCode());
            pageQuery.setQueryDTO(shippingInfoQueryDTO);
            ItemResult<PageData<ShippingInfoDTO>> buyerShips = shippingInfoService.queryShippingInfoListBySelf(pageQuery);
            if( buyerShips != null && buyerShips.getData() !=null && CollectionUtils.isNotEmpty(buyerShips.getData().getList())){
                result.setBuyerShips(buyerShips.getData().getList().stream()
                        .filter(item->item.getShippingStatus() != null)
                        .filter(item-> ShippingEnum.ShippingStatusEnum.EMPTY_SHIP.getCode() == item.getShippingStatus())
                        .map(item->BeanConvertUtils.convert(item, ShipSimpleDTO.class)).toList());
            }
        }
    }

    private void setType(List<String> transportIdList, Integer ecFlowControlCount, List<VehicleBaseDataDTO> vehicleList) {
        if (CollectionUtils.isNotEmpty(transportIdList)) {
            vehicleList.removeIf(item -> CollectionUtils.isEmpty(item.getTransportCategoryIdList()) || !item.getTransportCategoryIdList().containsAll(transportIdList));
        }
        if (ecFlowControlCount > 0) {
            //有电商管控商品,筛选
            vehicleList.removeIf(item -> !integerEqual(item.getIsGpsDevice(), 1) || !integerEqual(item.getSignalFlag(), 1));
        }
    }

    private TakeInfoDTO updateEnumName(TakeInfoDTO takeInfoDTO) {
        TakeTimeQuantumEnum takeTimeQuantumEnum = TakeTimeQuantumEnum.getByCode(takeInfoDTO.getTakeTimeQuantum());
        if (takeTimeQuantumEnum != null) {
            takeInfoDTO.setTakeTimeQuantumShow(takeTimeQuantumEnum.getMessage());
        }

        PickingBillTypeEnum pickingBillTypeEnum = PickingBillTypeEnum.valueOfCode(takeInfoDTO.getDeliverWay());
        if (pickingBillTypeEnum != null) {
            takeInfoDTO.setIfBuyerTake(pickingBillTypeEnum.equals(PickingBillTypeEnum.BUYER_TAKE));
            takeInfoDTO.setDeliverWayName(pickingBillTypeEnum.getDesc());
        }
        return takeInfoDTO;
    }

    private TakeInfoDTO updateButtonStatus(TakeInfoDTO takeInfoDTO) {
        if (TakeStatus.WAIT_PAYMENT.getCode().equals(takeInfoDTO.getTakeStatus())) {
            takeInfoDTO.setCanBuyerCancel(true);
            takeInfoDTO.setCanSellerCancel(true);
        }
        if (TakeStatus.PENDING_CONFIRMED.getCode().equals(takeInfoDTO.getTakeStatus())) {
            takeInfoDTO.setCanBuyerCancel(true);
            takeInfoDTO.setCanSellerCancel(true);
            takeInfoDTO.setCanSellerConfirm(true);
        }
        if (TakeStatus.CANCELED.getCode().equals(takeInfoDTO.getTakeStatus())) {
            takeInfoDTO.setCanBuyerDelete(true);
            takeInfoDTO.setCanSellerDelete(true);
        }
        if (TakeStatus.SELLER_CONFIRMED.getCode().equals(takeInfoDTO.getTakeStatus())) {
            takeInfoDTO.setCanSellerSendLogistic(true);
        }
        if (!PickingBillTypeEnum.PLATFORM_DELIVERY.getCode().equals(takeInfoDTO.getDeliverWay())
                && !TakeStatus.FINISHED.getCode().equals(takeInfoDTO.getTakeStatus())) {
            takeInfoDTO.setCanSellerFinish(true);
        }
        if (TakeStatus.IN_PAYMENT.getCode().equals(takeInfoDTO.getTakeStatus())) {
            takeInfoDTO.setCanSellerPayConfirm(true);
        }
        return takeInfoDTO;
    }

    private boolean integerEqual(Integer one, Integer two) {
        if (one == null || two == null) {
            return one == null && two ==null;
        }
        return one.intValue() == two.intValue();
    }

    @Deprecated(since = "2.1.4-RELEASE")
    @Override
    public List<TakeInfoDTO> doOrderCreateTakeInfo(OrderDTO orderDto, String operator) {
        throw new BasicRuntimeException("method discard");
    }

    @Override
    public List<TakeInfoDTO> doBuyerCreateTakeInfo(List<TakeInfoDTO> takeInfoDTOs, String memberId, String operator) {
        log.info("doBuyerCreateTakeInfo:" + JSON.toJSONString(takeInfoDTOs));
        String key = "order:createTakeInfo:"+takeInfoDTOs.get(0).getOrderId();
        String identifier = null;
        try {
            identifier = redisLockService.lockFast(key);
            return takeInfoBiz.createTakeInfo(createTakeInfo(takeInfoDTOs, memberId, operator, TakeCreateType.BUYER_CREATE, TakeStatusEnum.PENDING_CONFIRMED));
        }catch (DistributeLockException e) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "您点击太快了，请勿重复操作！");
        }finally {
            redisLockService.unlock(key,identifier);
        }
    }

    private List<TakeInfoCreateDTO> createTakeInfo(List<TakeInfoDTO> takeInfoDTOs, String memberId, String operator,TakeCreateType takeCreateType,TakeStatusEnum takeStatusEnum){
        return takeInfoDTOs.stream().map(item -> {
            TakeInfoCreateDTO takeInfoCreateDTO = new TakeInfoCreateDTO();
            BeanUtils.copyProperties(item,takeInfoCreateDTO);
            takeInfoCreateDTO.setOperator(operator);
            takeInfoCreateDTO.setOperatorMemberId(memberId);
            takeInfoCreateDTO.setTakeCreateType(takeCreateType.getCode());
            takeInfoCreateDTO.setTakeStatus(takeStatusEnum.getCode());
            takeInfoCreateDTO.setTakeItems(Lists.newArrayList());
            for (TakeItemDTO takeItem : item.getTakeItems()) {
                TakeInfoItemCreateDTO takeInfoItemCreateDTO = new TakeInfoItemCreateDTO();
                BeanUtils.copyProperties(takeItem,takeInfoItemCreateDTO);
                takeInfoItemCreateDTO.setTakeShipList(Collections.emptyList());
                takeInfoItemCreateDTO.setTakeVehicleList(Collections.emptyList());
                takeInfoItemCreateDTO.setTakeCarrierDTOList(Collections.emptyList());

                if(CollectionUtils.isNotEmpty(takeItem.getTakeVehicleList())) {
                    takeInfoItemCreateDTO.setTakeVehicleList(BeanConvertUtils.convertList(takeItem.getTakeVehicleList(), TakeVehicleDTO.class));
                }
                if(CollectionUtils.isNotEmpty(takeItem.getTakeShipList())) {
                    takeInfoItemCreateDTO.setTakeShipList(BeanConvertUtils.convertList(takeItem.getTakeShipList(), TakeShipDTO.class));
                }
                if(CollectionUtils.isNotEmpty(takeItem.getTakeCarrierDTOList())) {
                    takeInfoItemCreateDTO.setTakeCarrierDTOList(BeanConvertUtils.convertList(takeItem.getTakeCarrierDTOList(), TakeCarrierDTO.class));
                }

                takeInfoCreateDTO.getTakeItems().add(takeInfoItemCreateDTO);
            }
            return takeInfoCreateDTO;
        }).toList();
    }

    @Override
    public List<TakeInfoDTO> doBuyerCreateTakeInfo(List<TakeInfoCreateDTO> list) {
        return takeInfoBiz.createTakeInfo(list);
    }

    @Override
    public List<TakeInfoDTO> doSellerCreatePrimaryTakeInfo(List<String> secondaryTakeInfoIds, String memberId, String operator) {
        List<TakeInfoDTO> result = Lists.newArrayList();
        for (String secondaryTakeInfoId : secondaryTakeInfoIds) {
            String primaryTakeId = takeInfoBiz.createPrimaryTakeInfoBySecondaryTakeId(secondaryTakeInfoId,memberId,operator);
            if (CsStringUtils.isNotBlank(primaryTakeId)) {
                result.add(takeInfoBiz.getTakeInfoDetails(primaryTakeId));
            }
        }
        return result;
    }

    @Override
    public List<TakeInfoDTO> doSellerCreateTakeInfo(List<TakeInfoDTO> takeInfoDTOs, String memberId, String operator) {
        log.info("卖家创建发货单:" + JSON.toJSONString(takeInfoDTOs));
        String key = "order:createTakeInfo:"+takeInfoDTOs.get(0).getOrderId();
        String identifier = null;
        try {
            identifier = redisLockService.lockFast(key);
            return takeInfoBiz.createTakeInfo(createTakeInfo(takeInfoDTOs, memberId, operator, TakeCreateType.SELLER_CREATE, TakeStatusEnum.SELLER_CONFIRMED));
        }catch (DistributeLockException e) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "您点击太快了，请勿重复操作！");
        }finally {
            redisLockService.unlock(key,identifier);
        }
    }

    @Override
    public List<TakeInfoDTO> doSellerConfirmTakeInfo(List<TakeInfoDTO> takeInfoList, String memberId, String operator) {
        log.info("卖家编辑确认发货单 {}", takeInfoList);
        if (takeInfoList == null || takeInfoList.isEmpty()) {
            throw new BizException(TakeInfoCode.INVALID_PARAM, "发货单信息不能为空");
        }
        //分布式锁控制重复操作问题
        String identifier = "";
        List<String> takeIds = takeInfoList.stream().map(TakeInfoDTO::getTakeId).toList();
        String bizResource = "confirmTakeInfo-"+DigestUtils.md5Digest(CONFIRM_TAKE_INFO + JSON.toJSONString(takeIds));
        checkPrimaryOrderStatusBySecondTakeInfoIds(takeIds,memberId,operator);
        try {
            identifier = redisLockService.lockFast(bizResource);
            takeInfoBiz.confirmTakeInfo(createTakeInfo(takeInfoList,memberId,operator,TakeCreateType.SELLER_CREATE,TakeStatusEnum.SELLER_CONFIRMED));
            return takeIds.stream().map(item-> takeInfoBiz.getTakeInfoDetails(item)).toList();
        } catch (DistributeLockException e) {
            log.error("重复操作: doSellerConfirmTakeInfo=" + JSON.toJSONString(takeInfoList));
            throw new BizException(BasicCode.UNDEFINED_ERROR, UNDEFINED_ERROR_REFRESH_AGAIN_MSG);
        } finally {
            if (CsStringUtils.isNotEmpty(identifier)) {
                redisLockService.unlock(bizResource, identifier);
            }
        }
    }

    @Override
    public String doSellerConfirmTakeInfoNoChange(String takeId, String memberId, String operator) {
        log.info("卖家直接确认发货单 {}", takeId);
        if (takeId == null || CsStringUtils.isBlank(takeId)) {
            throw new BizException(TakeInfoCode.INVALID_PARAM, "发货单和id不能为空");
        }
        checkPrimaryOrderStatusBySecondTakeInfoIds(Lists.newArrayList(takeId),memberId,operator);
        takeInfoBiz.confirmTakeInfo(takeId, memberId,operator);
        //分布式锁控制重复操作问题
        String identifier = "";
        String bizResource = DigestUtils.md5Digest(CONFIRM_TAKE_INFO + JSON.toJSONString(takeId));
        try {
            identifier = redisLockService.lock(bizResource);
            takeInfoBiz.confirmTakeInfo(takeId, memberId,operator);
            return takeId;
        } catch (DistributeLockException e) {
            log.error("重复操作: doSellerConfirmTakeInfoNoChange=" + JSON.toJSONString(takeId));
            throw new BizException(BasicCode.UNDEFINED_ERROR, UNDEFINED_ERROR_REFRESH_AGAIN_MSG);
        } finally {
            if (CsStringUtils.isNotEmpty(identifier)) {
                redisLockService.unlock(bizResource, identifier);
            }
        }
    }

    @Override
    public List<String> doBatchSellerConfirmTakeInfoNoChange(List<String> takeIds, String memberId, String operator) {
        log.info("卖家直接确认发货单 {}", takeIds);
        if (takeIds == null || takeIds.isEmpty()) {
            throw new BizException(TakeInfoCode.INVALID_PARAM, "takeIds不能为空");
        }
        checkPrimaryOrderStatusBySecondTakeInfoIds(takeIds,memberId,operator);
        //分布式锁控制重复操作问题
        String identifier = "";
        String bizResource = "confirmTakeInfo-"+DigestUtils.md5Digest(CONFIRM_TAKE_INFO + JSON.toJSONString(takeIds));
        try {
            identifier = redisLockService.lockFast(bizResource);
            for (String takeId : takeIds) {
                takeInfoBiz.confirmTakeInfo(takeId, memberId, operator);
            }
            return takeIds;
        } catch (DistributeLockException e) {
            log.error("重复操作: doBatchSellerConfirmTakeInfoNoChange=" + JSON.toJSONString(takeIds));
            throw new BizException(BasicCode.UNDEFINED_ERROR, UNDEFINED_ERROR_REFRESH_AGAIN_MSG);
        } finally {
            if (CsStringUtils.isNotEmpty(identifier)) {
                redisLockService.unlock(bizResource, identifier);
            }
        }
    }

    //二级发货单在吗，卖家确认前需要创建并支付一级订单 需求：客户可以提前创建二级发货单  2020.10.26
    private void checkPrimaryOrderStatusBySecondTakeInfoIds(Collection<String> secondaryTakeInfoIds,String memberId,String operator){
        checkParams(secondaryTakeInfoIds, memberId, operator);

        //查找发货单
        List<TakeInfo> secondTakeInfoList = takeInfoBiz.findByIds(secondaryTakeInfoIds);
        //查找二级发货单
        List<OrderInfo> secondOrderList = orderInfoBiz.findByIds(secondTakeInfoList.stream().map(TakeInfo::getOrderId).filter(Objects::nonNull).collect(Collectors.toSet()));
        Map<String,String> secondOrderMap = secondOrderList.stream()
                .filter(item->ProxyOrderTypeEnum.SECONDARY.getCode().equals(item.getProxyOrderType()))
                .collect(Collectors.toMap(OrderInfo::getOrderId,OrderInfo::getOrderCode,(k1,k2)->k2));
        if( secondOrderMap == null || secondOrderMap.isEmpty() ){
            //如果没有二级发货单，则返回
            return;
        }
        //查找一二级订单关系表
        List<OrderProxyMap> proxyOrderMapList = orderProxyMapBiz.findBySecondaryOrderIds(secondOrderMap.keySet());
        //如果没有找到关系表数据
        if(CollectionUtils.isEmpty(proxyOrderMapList)){
            log.error("一级订单没找到,二级订单:{}",secondOrderMap.values());
            throw new BizException(BasicCode.UNDEFINED_ERROR,"确认发货前请先创建并支付一级订单");//卖家正在备货，请耐心等待或联系卖家
        }
        //关系表数据转map<二级订单id,一级订单id>
        Map<String,OrderProxyMap> proxyOrderMap = proxyOrderMapList.stream().collect(Collectors.toMap(OrderProxyMap::getSecondaryOrderId,Function.identity(),(k1,k2)->k2));
        Map<String,String> secondOrderMap2 = Maps.newHashMap();
        secondOrderMap.forEach((k,v)->{
            if( !proxyOrderMap.keySet().contains(k)){
                secondOrderMap2.put(k,v);//保存一级订单不存在的二级订单 用于日志显示订单号
            }
        });
        //如果关系表数据与二级订单数据量不一致，则提示错误
        if(!secondOrderMap2.isEmpty()){
            log.error("一级订单没找到,二级订单:{}",secondOrderMap2.values());
            throw new BizException(BasicCode.UNDEFINED_ERROR,"对应的一级订单没有创建:"+secondOrderMap2.values().stream().collect(Collectors.joining(",")));//卖家正在备货，请耐心等待或联系卖家
        }
        //循环一级订单状态
        checkStatus(proxyOrderMap);
    }

    private void checkStatus(Map<String, OrderProxyMap> proxyOrderMap) {
        for (OrderProxyMap orderProxyMap : proxyOrderMap.values()) {
            if (orderProxyMap == null || CsStringUtils.isBlank(orderProxyMap.getPrimaryOrderId())) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "一级订单id为空了");
            }
            if( TransportToolTypeEnum.ROAD_TRANSPORT.getCode().equals(orderInfoExtBiz.findByOrderId(orderProxyMap.getPrimaryOrderId()).getTransportType()) ){
                OrderPayinfo orderPayinfo = orderPayinfoBiz.getOrderPay(orderProxyMap.getPrimaryOrderId());
                if(orderPayinfo == null ){
                    throw new BizException(BasicCode.UNDEFINED_ERROR, "一级订单还没有支付:"+orderProxyMap.getPrimaryOrderCode());
                }
                if(!PayStatusEnum.COMPLETED.code().equals(orderPayinfo.getPayinfoStatus())){
                    throw new BizException(BasicCode.UNDEFINED_ERROR, "请先支付一级订单:"+orderPayinfo.getObjectCode()+",再确认发货");
                }
            }
        }
    }

    private void checkParams(Collection<String> secondaryTakeInfoIds, String memberId, String operator) {
        for (String takeId : secondaryTakeInfoIds) {
            TakeInfo oldTakeInfo = getTakeInfo(takeId);
            String operatorMemberId = CsStringUtils.isBlank(memberId) ? getOperatorMemberId(operator) : memberId;
            if (operatorMemberId != null && !operatorMemberId.equals(oldTakeInfo.getSellerId())
                    && !operatorMemberId.equals(oldTakeInfo.getBuyerId()) &&
                    //bug fixed 8652 平台确认、取消发货单，接口报用户无操作权限  2020.9.23
                    !MemberPlatform.PLATFORM_MEMBERID.getId().equals(operatorMemberId) ) {
                throw new BizException(TakeInfoCode.INVALID_PARAM, INVALID_PARAM_NO_PERMISSION_MSG);
            }
            OrderInfo orderInfo = orderInfoBiz.getOrderInfoById(oldTakeInfo.getOrderId());
            if (CsStringUtils.equals(orderInfo.getOrderStatus(), OrderStatusEnum.IN_PAYMENT.code())) {
                throw new BizException(TakeInfoCode.UNDEFINED_ERROR, TakeInfoBiz.CURRENT_ORDER_STATUS_CAN_NOT_DO_THIS);
            }
            if (orderInfo.getTakeTimeLimit() != null && orderInfo.getTakeTimeLimit().before(new Date())) {
                throw new BizException(TakeInfoCode.UNDEFINED_ERROR, "当前订单已过期, 不能进行此操作！");
            }
        }
    }

    @NotNull
    private TakeInfo getTakeInfo(String takeId) {
        if (CsStringUtils.isBlank(takeId)) {
            throw new BizException(TakeInfoCode.INVALID_PARAM, "发货单id不能为空");
        }

        TakeInfo oldTakeInfo = takeInfoBiz.get(takeId);
        if (oldTakeInfo == null || BooleanUtils.isTrue(oldTakeInfo.getDelFlg())) {
            throw new BizException(TakeInfoCode.INVALID_PARAM, "无法查找到原记录");
        }
        return oldTakeInfo;
    }

    private String getOperatorMemberId(String operator){
        if (CsStringUtils.isBlank(operator)) {
            return null;
        }
        if( MemberPlatform.SYSTEM_OPERATOR.getId().equals(operator) || MemberPlatform.PLATFORM_ACCOUNTID.getId().equals(operator) ){
            return MemberPlatform.PLATFORM_MEMBERID.getId();
        }
        AccountSimpleDTO accountSimpleDTO = accountService.findSimpleById(operator);
        if( accountSimpleDTO == null ){
            log.warn("操作人账号id不正确，没查询到账号信息");
            return null;
        }
        return accountSimpleDTO.getMemberId();
    }

    @Override
    public void doFinishTakeInfoByLogistic(String takeInfoId, List<TakeItemDTO> logisticTakeItems, String operator) {
        // 检查发货单状态是否是已完成，已完成则不做处理
        log.info("物流完成发货单 {} {}", takeInfoId, logisticTakeItems);
        this.takeInfoBiz.finishTakeInfoByLogistic(takeInfoId, logisticTakeItems, operator);
    }

    @Override
    public void doCloseTakeInfoByLogistic(String takeInfoId, List<TakeItemDTO> logisticTakeItems, String operator) {
        log.info("物流关闭发货单 {} {}", takeInfoId, logisticTakeItems);
        this.takeInfoBiz.closeTakeInfoByLogistic(takeInfoId, logisticTakeItems, operator);
    }

    @Override
    public void finishTakeInfoByERP(String orderId, String operator) {
        takeInfoBiz.finishTakeInfoByERP(orderId,operator);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String doBuyerCancelTakeInfo(String takeInfoId, String memberId, String operator) {
        log.info("买家取消发货单:{}",takeInfoId);
        takeInfoBiz.validateTakeInfoCanEdit(takeInfoId, operator, false);
        TakeInfoDTO takeInfoDTO = getBuyerTakeInfoDetail(takeInfoId, memberId, operator);
        if (CsStringUtils.equals(takeInfoDTO.getProxyTakeType(), ProxyOrderTypeEnum.SECONDARY.getCode())) {
            log.info("takeInfoHook second,takeInfoCode:{},orderCode:{}",takeInfoDTO.getTakeCode(),takeInfoDTO.getOrderCode());
            closeOrCancelSecondaryTakeInfoHook(takeInfoDTO, TakeStatusEnum.CANCELED);
        }
        takeInfoBiz.doCancelTakeInfo(takeInfoId, TakeCancelType.BUYER_CANCEL, operator);
        //触发关联运单或发货单结束
        closeOrCancelTakeInfoHook(takeInfoDTO.getTakeId(),takeInfoDTO.getProxyTakeType(), TakeStatusEnum.CANCELED,TakeCancelType.BUYER_CANCEL,TakeCloseType.BUYER_CLOSE,operator);

        return takeInfoId;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String doSellerCancelTakeInfo(String takeInfoId, String memberId, String operator,boolean sendMessage) {
        log.info("卖家取消发货单 {}", takeInfoId);
        takeInfoBiz.validateTakeInfoCanEdit(takeInfoId, operator, false);
        TakeInfoDTO takeInfoDTO = takeInfoBiz.getTakeInfoDetails(takeInfoId);
        if (CsStringUtils.equals(takeInfoDTO.getProxyTakeType(), ProxyOrderTypeEnum.SECONDARY.getCode())) {
            closeOrCancelSecondaryTakeInfoHook(takeInfoDTO, TakeStatusEnum.CANCELED);
        }
        takeInfoBiz.doCancelTakeInfo(takeInfoId, TakeCancelType.SELLER_CANCEL, operator);
        //触发关联运单或发货单结束
        closeOrCancelTakeInfoHook(takeInfoDTO.getTakeId(),takeInfoDTO.getProxyTakeType(), TakeStatusEnum.CANCELED,TakeCancelType.SELLER_CANCEL,TakeCloseType.SELLER_CLOSE,operator);
        //发送消息
        if(sendMessage) {
            SendSMSMessageDTO sellerSendSMSMessageDTO = new SendSMSMessageDTO();
            OrderDTO orderDTO = new OrderDTO();
            orderDTO.setOrderId(takeInfoDTO.getOrderId());
            sellerSendSMSMessageDTO.setOrderDTO(orderDTO);
            sellerSendSMSMessageDTO.setBusinessCode(takeInfoDTO.getTakeCode());
            sellerSendSMSMessageDTO.setMessageConfigCode(
                    MessageConfigCodeEnum.TAKEINFO_SELLER_CANCEL.getCode());
            Map<String, Object> extraParams = new HashMap<>();
            extraParams.put(TAKE_CODE, takeInfoDTO.getTakeCode());
            sellerSendSMSMessageDTO.setExtraParams(extraParams);
            smsMessageFactoryContext.sendSMSMessage(sellerSendSMSMessageDTO);
        }
        return takeInfoId;
    }

    @Override
    public String doSellerFinishTakeInfo(String takeInfoId, List<TakeItemDTO> takeItems, String memberId, String operator) {
        if (CsStringUtils.isBlank(takeInfoId) || CsStringUtils.isBlank(memberId) || CsStringUtils.isBlank(operator)
                || takeItems == null || takeItems.isEmpty()) {
            throw new BizException(TakeInfoCode.INVALID_PARAM, "参数错误 不能为空");
        }
        log.info(" 卖家点击完成发货单 {}, {} , {},{}", takeItems, takeInfoId, memberId, operator);

        TakeInfoDTO takeInfoDTO = this.takeInfoBiz.getTakeInfoDetails(takeInfoId);
        if (takeInfoDTO == null) {
            throw new BizException(TakeInfoCode.DATA_NOT_EXIST, "发货单不存在");
        }
        Set<String> finalStatus = Sets.newHashSet(
                TakeStatus.CLOSED.getCode(),
                TakeStatus.CANCELED.getCode(),
                TakeStatus.FINISHED.getCode());
        if (finalStatus.contains(takeInfoDTO.getTakeStatus())) {
            log.info("发货单已结束 takeId:{},takeStatus:{}" ,takeInfoId,takeInfoDTO.getTakeStatus());
            return null;
        }
        checkParams(memberId, takeInfoDTO);
        takeItems.forEach(inputItem -> {
            for (TakeItemDTO takeItem : takeInfoDTO.getTakeItems()) {
                if (takeItem.getTakeItemId().equals(inputItem.getTakeItemId())) {
                    takeItem.setSellerSignQuantity(inputItem.getSellerSignQuantity());
                    takeItem.setShippedQuantity(inputItem.getShippedQuantity());
                    if (CsStringUtils.isBlank(takeItem.getResourceId())) {
                        takeItem.setResourceId(takeItem.getOrderItemId());
                    }
                    return;
                }
            }
        });
        try {
            this.takeInfoBiz.doSellerFinishTakeInfo(takeInfoDTO, operator);
        }catch (BizException e){
            return e.getMessage();
        }
        return null;
    }

    private static void checkParams(String memberId, TakeInfoDTO takeInfoDTO) {
        if (!memberId.equals(takeInfoDTO.getSellerId()) && !MemberPlatform.PLATFORM_MEMBERID.getId().equals(takeInfoDTO.getSellerId())) {
            throw new BizException(TakeInfoCode.NO_PERMISSION, INVALID_PARAM_NO_PERMISSION_MSG);
        }
        if (PickingBillTypeEnum.PLATFORM_DELIVERY.getCode().equals(takeInfoDTO.getDeliverWay())) {
            throw new BizException(TakeInfoCode.UN_SUPPORT, "卖家不可完成平台配送的发货单");
        }
    }

    @Deprecated(since = "2.1.4-RELEASE")
    @Override
    public String doBuyerFinishTakeInfo(String takeInfoId, List<TakeItemDTO> takeItems, String memberId, String operator) {
        log.info(" 买家点击完成发货单 {}, {} , {},{}", takeItems, takeInfoId, memberId, operator);
        //没用到
        throw new BasicRuntimeException("method discard");
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void doSellerCloseOrder(String orderId, String operator) {
        log.info("卖家关闭订单 orderId:{} ", orderId);
        List<TakeInfo> takeInfos = this.takeInfoBiz.getTakeInfoByOrderIdNoCancel(orderId);
        if (takeInfos == null || takeInfos.isEmpty()) {
            log.info("未找到发货单 orderId:{}",orderId);
            return;
        }
        takeInfos.forEach(takeInfo -> {
            if (takeInfo.getTakeStatus().equals(TakeStatus.IN_PAYMENT.getCode()) ){
                throw new BizException(BasicCode.CUSTOM_ERROR,"发货单("+takeInfo.getTakeCode()+")处于支付确认中");
            }
        });
        takeInfos.forEach(takeInfo ->
            takeInfoBiz.closeTakeInfo(takeInfo.getTakeId(), takeInfo.getSellerId(), operator,TakeCloseType.ORDER_CLOSE,false)
        );
        //尝试触发订单自动完成
        orderCommonService.triggerOrderAutoEnd(takeInfos.get(0).getOrderId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void doBuyerCloseOrder(String orderId, String operator) {
        log.info("卖家关闭订单 orderId:{} ", orderId);
        List<TakeInfo> takeInfos = this.takeInfoBiz.getTakeInfoByOrderIdNoCancel(orderId);
        if (takeInfos == null || takeInfos.isEmpty()) {
            log.info("未找到发货单 orderId:{}",orderId);
            return;
        }
        takeInfos.forEach(takeInfo -> {
            if (takeInfo.getTakeStatus().equals(TakeStatus.IN_PAYMENT.getCode()) ){
                throw new BizException(BasicCode.CUSTOM_ERROR,"发货单("+takeInfo.getTakeCode()+")处于支付确认中");
            }
        });
        takeInfos.forEach(takeInfo ->
            takeInfoBiz.closeTakeInfo(takeInfo.getTakeId(), takeInfo.getSellerId(), operator, TakeCloseType.ORDER_CLOSE,false)
        );
        //尝试触发订单自动完成
        orderCommonService.triggerOrderAutoEnd(takeInfos.get(0).getOrderId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void doSellerCloseTakeInfo(String takeInfoId, String operator) {
        log.info(" 卖家关闭发货单 takeInfoId {} ", takeInfoId);
        TakeInfo takeInfo = this.takeInfoBiz.findById(takeInfoId);
        if (takeInfo == null) {
            log.info(" 未找到发货单 ");
            return;
        }
        if (takeInfo.getTakeTimeLimit() != null &&
                takeInfo.getTakeTimeLimit().getTime() < new Date().getTime()) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "订单已过期，不允许执行此操作！");
        }
        //二级发货单关闭验证
        if (CsStringUtils.equals(takeInfo.getProxyTakeType(), ProxyOrderTypeEnum.SECONDARY.getCode())) {
            log.info("takeInfoHook second,takeInfoCode:{},orderCode:{}",takeInfo.getTakeCode(),takeInfo.getOrderCode());
            closeOrCancelSecondaryTakeInfoHook(takeInfo, TakeStatusEnum.CLOSED);
        }
        takeInfoBiz.closeTakeInfo(takeInfo.getTakeId(), takeInfo.getSellerId(), operator,TakeCloseType.SELLER_CLOSE,true);
        //触发关联运单或发货单结束
        closeOrCancelTakeInfoHook(takeInfo.getTakeId(),takeInfo.getProxyTakeType(), TakeStatusEnum.CLOSED,TakeCancelType.SELLER_CANCEL,TakeCloseType.SELLER_CLOSE, operator);
    }

    @Override
    public TakeInfoDTO doSellerSendTakeInfoToLogistic(String takeInfoId, String memberId, String operator) {
        if (CsStringUtils.isBlank(takeInfoId) || CsStringUtils.isBlank(memberId) || CsStringUtils.isBlank(operator)) {
            throw new BizException(TakeInfoCode.INVALID_PARAM, "参数错误 不能为空");
        }
        log.info(" 卖家点击发货单传物流 {}, {} , {}", takeInfoId, memberId, operator);

        TakeInfo takeInfo = takeInfoBiz.get(takeInfoId);
        if (takeInfo == null) {
            throw new BizException(TakeInfoCode.DATA_NOT_EXIST, "发货单不存在");
        }
        if (ProxyOrderTypeEnum.SECONDARY.getCode().equals(takeInfo.getProxyTakeType())) {
            throw new BizException(TakeInfoCode.CUSTOM_ERROR, "等待一级发货单处理");
        }
        if (!memberId.equals(takeInfo.getSellerId()) && !MemberPlatform.PLATFORM_MEMBERID.getId().equals(takeInfo.getSellerId())) {
            throw new BizException(TakeInfoCode.NO_PERMISSION, INVALID_PARAM_NO_PERMISSION_MSG);
        }
        if (!TakeStatus.SELLER_CONFIRMED.getCode().equals(takeInfo.getTakeStatus())) {
            throw new BizException(TakeInfoCode.UN_SUPPORT, "发货单状态不正确");
        }
        //发货单传物流事件
        StatusModelDO statusModelDO = new StatusModelDO();
        statusModelDO.setBizId(takeInfo.getTakeId());
        statusModelDO.setCurrentState(TakeStatusEnum.SELLER_CONFIRMED.getCode());
        statusModelDO.addParam(StateMachineConstants.TAKE_ID, takeInfo.getTakeId());
        statusModelDO.addParam(StateMachineConstants.OPERATOR, operator);
        takeStateMachineEventManager.sendStatusChangeEvent(statusModelDO, TakeChangeEventEnum.SEND_LOGISTICS);
        log.info("===========卖家点击发货单传物流完成！=========");
        return takeInfoBiz.getTakeInfoDetails(takeInfoId);
    }

    @Override
    public void updateSendQuantity(String takeInfoId, String operator, Map<String, String> reourceQuantityDetails) {
        this.takeInfoBiz.updateShippedQuantity(takeInfoId, operator, reourceQuantityDetails);
    }

    @Override
    public void updateSendQuantity(String takeInfoId, String operator, Map<String, String> reourceQuantityDetails, Map<String, String> takeCost, Map<String, BigDecimal> actualQuantityDetails) {
        takeInfoBiz.updateShippedQuantity(
                takeInfoId, operator, reourceQuantityDetails, takeCost, actualQuantityDetails);
    }

    @Override
    public void updateSellerSignQuantity(String takeInfoId, String adjustAddWay, String operator, Map<String, String> itemQuantityDetails) {
        log.info("物流更新签收数量发货单 {} {}", takeInfoId, itemQuantityDetails);
        this.takeInfoBiz.updateSellerSignQuantity(takeInfoId, adjustAddWay,operator, itemQuantityDetails);
    }

    @Override
    public void checkTakeTimeLimited() {
        takeInfoBiz.checkTakeTimeLimit();
    }

    @Override
    public List<TakeInfoDTO> getTakeInfoDetailForOrder(String orderId) {
        return takeInfoBiz.getTakeInfoDetailsByOrderId(orderId);
    }

    @Override
    public PageInfo<TakeInfoDTO> pageBuyerTakeInfo(TakeInfoSearchDTO takeInfoQuery) {
        if (takeInfoQuery == null || (CsStringUtils.isBlank(takeInfoQuery.getAccount())
                && CsStringUtils.isBlank(takeInfoQuery.getBuyerId()))) {
            throw new BizException(TakeInfoCode.INVALID_PARAM, "参数错误");
        }
        if (!CsStringUtils.isBlank(takeInfoQuery.getAccount())) {
            String memberId = takeInfoQuery.getBuyerId();
            if (CsStringUtils.isBlank(memberId)) {
                throw new BizException(TakeInfoCode.INVALID_PARAM, "参数错误");
            }
            takeInfoQuery.setBuyerId(memberId);
        }

        return new PageInfo<>(this.takeInfoBiz.pageTakeInfoDetails(takeInfoQuery));
    }

    @Override
    public PageInfo<ProxyMapErrorDTO> pageProxyMapError(PageQuery<ProxyMapErrorSearchDTO> query) {
        PageInfo<ProxyMapError> pageInfo = proxyMapErrorBiz.findAll(query);
        PageInfo<ProxyMapErrorDTO> result = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo,result);
        result.setList(Lists.newArrayList());
        if( CollectionUtils.isNotEmpty(pageInfo.getList())){
            pageInfo.getList().forEach(item->{
                ProxyMapErrorDTO proxyMapErrorDTO = new ProxyMapErrorDTO();
                BeanUtils.copyProperties(item,proxyMapErrorDTO);
                if (CsStringUtils.equals(ProxyBillTypeEnum.BILL_TYPE_TAKE.getCode(), item.getBillType())) {
                    proxyMapErrorDTO.setTakeInfoDTO(takeInfoBiz.getTakeInfoDetails(item.getObjectId()));
                    proxyMapErrorDTO.setOrderDTO(orderQueryService.getOrderInfo(proxyMapErrorDTO.getTakeInfoDTO().getOrderId()));
                }
                if (CsStringUtils.equals(ProxyBillTypeEnum.BILL_TYPE_ORDER.getCode(), item.getBillType())) {
                    proxyMapErrorDTO.setOrderDTO(orderQueryService.getOrderInfo(item.getObjectId()));
                }
                result.getList().add(proxyMapErrorDTO);
            });
        }
        return result;
    }

    @Override
    public PageInfo<TakeInfoDTO> pageSellerTakeInfo(TakeInfoSearchDTO takeInfoQuery) {
        if (takeInfoQuery == null || (CsStringUtils.isBlank(takeInfoQuery.getAccount())
                && CsStringUtils.isBlank(takeInfoQuery.getSellerId()))) {
            throw new BizException(TakeInfoCode.INVALID_PARAM, "参数错误");
        }
        if (!CsStringUtils.isBlank(takeInfoQuery.getAccount())) {
            String memberId = takeInfoQuery.getSellerId();
            if (CsStringUtils.isBlank(memberId)) {
                throw new BizException(TakeInfoCode.INVALID_PARAM, "参数错误");
            }
            takeInfoQuery.setSellerId(memberId);
        }
        return new PageInfo<>(this.takeInfoBiz.pageTakeInfoDetails(takeInfoQuery));
    }

    @Override
    public PageInfo<TakeInfoDTO> pagePlatformTakeInfo(TakeInfoSearchDTO takeInfoQuery) {
        return new PageInfo<>(this.takeInfoBiz.pageTakeInfoDetails(takeInfoQuery));
    }

    @Override
    public TakeInfoDTO getSellerTakeInfoDetail(String takeInfoId, String memberId, String operator) {
        if (CsStringUtils.isBlank(takeInfoId) || CsStringUtils.isBlank(operator)) {
            throw new BizException(TakeInfoCode.INVALID_PARAM);
        }
        TakeInfoDTO result = this.takeInfoBiz.getTakeInfoDetails(takeInfoId, null, null);
        //如果经销商，则可以查询一级发货单
        if (result == null || CsStringUtils.equals(result.getSellerId(), memberId) ||
                (CsStringUtils.equals(result.getProxyTakeType(), ProxyOrderTypeEnum.PRIMARY.getCode()) && CsStringUtils.equals(result.getBuyerId(), memberId))) {
            return result;
        }
        return null;
    }

    @Override
    public TakeInfoDTO getBuyerTakeInfoDetail(String takeInfoId, String memberId, String operator) {
        if (CsStringUtils.isBlank(takeInfoId) || CsStringUtils.isBlank(operator)) {
            throw new BizException(TakeInfoCode.INVALID_PARAM);
        }
        return this.takeInfoBiz.getTakeInfoDetails(takeInfoId, null, memberId);
    }

    @Override
    public TakeInfoDTO getPlatformTakeInfoDetail(String takeInfoId, String operator) {
        return this.takeInfoBiz.getTakeInfoDetails(takeInfoId);
    }

    @Override
    public TakeInfoDTO getTakeInfoById(String id) {
        TakeInfoDTO takeInfoDTO = BeanConvertUtils.convert(this.takeInfoBiz.findById(id), TakeInfoDTO.class);
        if (takeInfoDTO != null) {
            String takeId = takeInfoDTO.getTakeId();
            List<TakeItem> takeItems = takeItemBiz.selectItemByTakeId(takeId);
            if (CollectionUtils.isNotEmpty(takeItems)) {
                List<TakeItemDTO> takeItemDTOList = Lists.newArrayList();
                for (TakeItem item : takeItems) {
                    takeItemDTOList.add(BeanConvertUtils.convert(item, TakeItemDTO.class));
                }
                takeInfoDTO.setTakeItems(takeItemDTOList);
            }
        }
        return takeInfoDTO;
    }

    @Override
    public TakeInfoDTO getTakeInfoByCode(String code) {
        TakeInfoDTO takeInfoDTO = BeanConvertUtils.convert(this.takeInfoBiz.getTakeInfoByCode(code), TakeInfoDTO.class);
        if (takeInfoDTO != null) {
            String takeId = takeInfoDTO.getTakeId();
            List<TakeItem> takeItems = takeItemBiz.selectItemByTakeId(takeId);
            if (CollectionUtils.isNotEmpty(takeItems)) {
                List<TakeItemDTO> takeItemDTOList = Lists.newArrayList();
                for (TakeItem item : takeItems) {
                    takeItemDTOList.add(BeanConvertUtils.convert(item, TakeItemDTO.class));
                }
                takeInfoDTO.setTakeItems(takeItemDTOList);
            }
        }
        return takeInfoDTO;
    }

    @Override
    public List<TakeInfoDTO> getSellerOrderTakeInfo(String orderId, String operator) {
        validateOrderTakeInfoCanGet(orderId, operator, true, false);
        return this.takeInfoBiz.getTakeInfoDetailsByOrderId(orderId);
    }

    @Override
    public List<TakeInfoDTO> getBuyerOrderTakeInfo(String orderId, String operator) {
        OrderInfo orderInfo = validateOrderTakeInfoCanGet(orderId, operator, false, false);
        TakeInfo takeInfoQuery = new TakeInfo();
        takeInfoQuery.setBuyerId(orderInfo.getBuyerId());
        takeInfoQuery.setOrderId(orderId);
        List<TakeInfoDTO> takeInfoDTOs = takeInfoBiz.searchTakeInfoDetails(takeInfoQuery, new ArrayList<>());
        if (CollectionUtils.isEmpty(takeInfoDTOs)) {
            return Lists.newArrayList();
        }
        takeInfoDTOs.forEach(takeInfo -> {
            if (!CsStringUtils.isBlank(takeInfo.getStoreId())) {
                com.ecommerce.order.service.impl.TakeInfoService.setTakeInfoStoreAddressAndContact(takeInfo, warehouseService);
            }
            com.ecommerce.order.service.impl.TakeInfoService.setTakeInfoReceiverName(takeInfo, memberService);
        });

        return takeInfoDTOs;
    }

    @Override
    public List<TakeInfoDTO> getPlantformOrderTakeInfo(String orderId, String operator) {
        validateOrderTakeInfoCanGet(orderId, operator, false, true);
        return this.takeInfoBiz.getTakeInfoDetailsByOrderId(orderId);
    }

    private OrderInfo validateOrderTakeInfoCanGet(String orderId, String operator, boolean isSeller,
                                                boolean isPlantform) {
        validateNotNull(orderId);
        validateNotNull(operator);
        OrderInfo orderInfo = orderInfoBiz.getOrderInfoById(orderId);

        if (!isPlantform) {
            AccountSimpleDTO account = accountService.findSimpleById(operator);
            if (account == null) {
                throw new BizException(TakeInfoCode.DATA_NOT_FOUND, "账号信息有误");
            }
            if (isSeller) {
                if (!CsStringUtils.equals(account.getMemberId(), orderInfo.getSellerId())) {
                    throw new BizException(TakeInfoCode.DATA_NOT_FOUND);
                }
            } else {
                if (!CsStringUtils.equals(account.getMemberId(), orderInfo.getBuyerId())) {
                    throw new BizException(TakeInfoCode.DATA_NOT_FOUND);
                }
            }
        }
        return orderInfo;
    }

    private void validateNotNull(String test) {
        if (CsStringUtils.isBlank(test)) {
            throw new BizException(TakeInfoCode.PARAM_NULL);
        }
    }

    @Override
    public TakeInfoOverviewDTO takeInfoOverview(OverviewReqDTO overviewReqDTO) {
        return takeInfoBiz.takeInfoOverview(overviewReqDTO);
    }

    @Override
    public List<TakeItemDTO> selectItemByOrderItemId(String orderItemId) {
        if (CsStringUtils.isBlank(orderItemId)) {
            throw new BizException(BasicCode.INVALID_PARAM, "orderItemId");
        }
        List<TakeItem> itemList = takeItemBiz.selectItemByOrderItemId(orderItemId);
        List<TakeItemDTO> takeItemList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(itemList)) {
            for (TakeItem takeItem : itemList) {
                TakeItemDTO takeItemDTO = new TakeItemDTO();
                BeanUtils.copyProperties(takeItem, takeItemDTO);
                takeItemList.add(takeItemDTO);
            }
        }

        return takeItemList;
    }

    @Override
    public List<TakeStatusStatisticDTO> statisticTakeStatus(TakeInfoSearchDTO takeInfoSearchDTO) {
        return takeInfoBiz.statisticTakeStatus(takeInfoSearchDTO);
    }

    @Override
    public void doSellerDeleteTakeInfo(String takeId, String operator) {
        log.info("卖家删除发货单 {} {}", takeId, operator);
        takeInfoValidateBiz.validateTakeInfoCanDelete(takeId, operator, true, false);
        this.takeInfoBiz.doDeleteCanceledTakeInfo(takeId, TakeDeleteType.SELLER_DELETE);
    }

    @Override
    public void doBuyerDeleteTakeInfo(String takeId, String operator) {
        log.info("买家删除发货单 {} {}", takeId, operator);
        takeInfoValidateBiz.validateTakeInfoCanDelete(takeId, operator, false, false);
        this.takeInfoBiz.doDeleteCanceledTakeInfo(takeId, TakeDeleteType.BUYER_DELETE);
    }

    @Override
    public NeedBuyerPayDTO needBuyerPay(NeedBuyerPayDTO needBuyerPayDTO) {
        return takeInfoBiz.needBuyerPay(needBuyerPayDTO);
    }

    @Override
    public List<TakeInfoExportDTO> exportTakeInfo(TakeInfoSearchDTO takeInfoSearchDTO) {
        checkParams(takeInfoSearchDTO);
        List<TakeInfoExportDTO> takeInfoExportDTOS = takeInfoBiz.exportTakeInfo(takeInfoSearchDTO);
        Set<String> categorySet = new HashSet<>();
        Set<String> addressSet = new HashSet<>();
        takeInfoExportDTOS.forEach(takeInfoExportDTO -> {
            // 发货单状态
            TakeStatus takeStatus = TakeStatus.getByCode(takeInfoExportDTO.getTakeStatus());
            if (takeStatus != null) {
                takeInfoExportDTO.setTakeStatus(takeStatus.getMessage());
            }
            // 物流方式
            PickingBillTypeEnum pickingBillTypeEnum = PickingBillTypeEnum.valueOfCode(takeInfoExportDTO.getDeliverWayName());
            if (pickingBillTypeEnum != null) {
                takeInfoExportDTO.setDeliverWayName(pickingBillTypeEnum.getDesc());
            }
            // 期望发货时间
            TakeTimeQuantumEnum takeTimeQuantumEnum = TakeTimeQuantumEnum.getByCode(takeInfoExportDTO.getTakeTimeQuantumShow());
            if (takeTimeQuantumEnum != null) {
                takeInfoExportDTO.setTakeTimeQuantumShow(takeTimeQuantumEnum.getMessage());
            }
            takeInfoExportDTO.setTakeTimeQuantumShow(String.join(" ", getOrDefault(takeInfoExportDTO.getTakeTime(), ""), getOrDefault(takeInfoExportDTO.getTakeTimeQuantumShow(), "")));
            // 收货人 & 收货地址
            if (CsStringUtils.isNotBlank(takeInfoExportDTO.getAddress())) {
                addressSet.add(takeInfoExportDTO.getAddress());
            }
            // 商品分类编码集合
            if (CsStringUtils.isNotBlank(takeInfoExportDTO.getCategoryName())) {
                categorySet.add(takeInfoExportDTO.getCategoryName());
            }
        });
        // 设置商品分类
        if (!categorySet.isEmpty()) {
            Map<String, String> goodsCategoryMap = goodsCategoryService.getSimpleList(categorySet).stream().collect(Collectors.toMap(GoodsCategorySimpleDTO::getCategoryCode, GoodsCategorySimpleDTO::getCategoryName));
            takeInfoExportDTOS.forEach(takeInfoExportDTO -> takeInfoExportDTO.setCategoryName(goodsCategoryMap.get(takeInfoExportDTO.getCategoryName())));
        }
        // 收货人 & 收货地址
        if (!addressSet.isEmpty()) {
            Map<String, ReceivingAddressSimpleDTO> receivingAddressMap = receivingAddressService.findSimpleByIds(addressSet).stream().collect(Collectors.toMap(ReceivingAddressSimpleDTO::getId, Function.identity()));
            takeInfoExportDTOS.forEach(takeInfoExportDTO -> {
                ReceivingAddressSimpleDTO receivingAddressSimpleDTO = receivingAddressMap.get(takeInfoExportDTO.getAddress());
                if (receivingAddressSimpleDTO != null) {
                    takeInfoExportDTO.setConsigneeName(receivingAddressSimpleDTO.getConsigneeName());
                    takeInfoExportDTO.setProvinceName(receivingAddressSimpleDTO.getProvinceName());
                    takeInfoExportDTO.setCityName(receivingAddressSimpleDTO.getCityName());
                    takeInfoExportDTO.setDistrictName(receivingAddressSimpleDTO.getDistrictName());
                    takeInfoExportDTO.setStreetName(receivingAddressSimpleDTO.getStreetName());
                    takeInfoExportDTO.setAddress(receivingAddressSimpleDTO.getAddress());
                }
            });
        }

        return takeInfoExportDTOS;
    }

    private static void checkParams(TakeInfoSearchDTO takeInfoSearchDTO) {
        if (takeInfoSearchDTO == null || (CsStringUtils.isBlank(takeInfoSearchDTO.getAccount())
                && CsStringUtils.isBlank(takeInfoSearchDTO.getSellerId()))) {
            throw new BizException(TakeInfoCode.INVALID_PARAM, "参数错误");
        }
        if (!CsStringUtils.isBlank(takeInfoSearchDTO.getAccount())) {
            String memberId = takeInfoSearchDTO.getSellerId();
            if (CsStringUtils.isBlank(memberId)) {
                throw new BizException(TakeInfoCode.INVALID_PARAM, "参数错误");
            }
            takeInfoSearchDTO.setSellerId(memberId);
        }
    }

    private String getOrDefault(String s, String defaultValue) {
        return CsStringUtils.isNotBlank(s) ? s : defaultValue;
    }

    @Override
    public List<String> queryTakeCodeByContractIdList(List<String> contractIdList) {
        return takeInfoBiz.queryTakeCodeByContractIdList(contractIdList);
    }

    @Override
    public ShipBillERPInfoDTO findShipBillERPInfoByTakeCode(String takeCode, String contractId) {
        log.info("findShipBillERPInfoByTakeCode : takeCode : {} ; contractId : {}", takeCode, contractId);
        ShipBillERPInfoDTO shipBillERPInfoDTO = new ShipBillERPInfoDTO();
        if (CsStringUtils.isNotBlank(takeCode)) {
            shipBillERPInfoDTO.setOrderPayinfoDTO(orderPayinfoBiz.findByTakeCode(takeCode));
        }
        if (CsStringUtils.isNotBlank(contractId)) {
            TrContractDTO dto = new TrContractDTO();
            com.ecommerce.goods.api.dto.contract.TrContractDTO contractDTO = contractService.getContract(contractId);
            log.info("goods contractDTO : {}", JSON.toJSONString(contractDTO));
            BeanUtils.copyProperties(contractDTO, dto);
            shipBillERPInfoDTO.setContractDTO(dto);
        }
        return shipBillERPInfoDTO;
    }

    /**
     * 要调用的地方 参考 discardWaybill
     * 关闭或者取消发货单的关联操作
     * 一级发货单关闭/取消：同时关闭/取消对应的二级发货单
     * 二级发货单关闭/取消：如果一级发货单已经生成，则不能关闭/取消
     */
    private void closeOrCancelTakeInfoHook(String takeId,String proxyTakeType, TakeStatusEnum takeStatusEnum,TakeCancelType takeCancelType,TakeCloseType takeCloseType, String operatorId) {
        log.info("takeInfoHook takeId:{},proxyTakeType:{},takeStatusEnum:{},takeCancelType:{},operatorId:{}",takeId,proxyTakeType,takeStatusEnum,takeCancelType,operatorId);
        if (CsStringUtils.isBlank(proxyTakeType)) {
            return;//非背靠背
        }
        boolean primary = CsStringUtils.equals(proxyTakeType, ProxyOrderTypeEnum.PRIMARY.getCode());
        List<TakeProxyMap> takeProxyMapList;
        takeProxyMapList = getTakeProxyMaps(takeId, primary);
        if (CollectionUtils.isEmpty(takeProxyMapList)) {
            log.info("takeInfoHook takeProxyMapList.size:0");
            return;
        }
        log.info("takeInfoHook takeProxyMapList.size:{}",takeProxyMapList.size());
        Set<String> endStatus = Sets.newHashSet(TakeStatusEnum.CLOSED.getCode(),TakeStatusEnum.FINISHED.getCode(),TakeStatusEnum.CANCELED.getCode());
        Set<String> set = Sets.newHashSet(TakeStatusEnum.PENDING_CONFIRMED.getCode(),TakeStatusEnum.SELLER_CONFIRMED.getCode(),TakeStatusEnum.PENDING_DELIVERY.getCode(),TakeStatusEnum.DELIVERING.getCode());
        //一级发货单关闭/取消：同时关闭对应的二级发货单
        for (TakeProxyMap takeProxyMap : takeProxyMapList) {
            String closeTakeId = primary ? takeProxyMap.getSecondaryTakeId() : takeProxyMap.getPrimaryTakeId();
            TakeInfo closeTakeInfo = takeInfoBiz.get(closeTakeId);
            if (closeTakeInfo == null) {
                log.error("关闭关联的发货单没有找到,一级发货单:{}，二级发货单:{}",takeProxyMap.getPrimaryTakeId(),takeProxyMap.getSecondaryTakeId());
                continue;
            }
            if( endStatus.contains(closeTakeInfo.getTakeStatus())){
                log.error("关闭关联的发货单已是终止状态,一级发货单:{}，二级发货单:{}",takeProxyMap.getPrimaryTakeId(),takeProxyMap.getSecondaryTakeId());
                return;
            }
            log.info("takeInfoHook primary 一级发货单takeId:{} 二级发货单secondary takeId:{} takeStatus:{}",takeId,closeTakeInfo.getTakeId(),closeTakeInfo.getTakeStatus());
            //水运 一级取消二级为已支付及之后的状态，则关闭二级
            if( TakeStatusEnum.CLOSED == takeStatusEnum ||
                (primary && TransportToolTypeEnum.WATER_TRANSPORT.getCode().equals(closeTakeInfo.getTransportType()) &&
                        set.contains(closeTakeInfo.getTakeStatus())) ) {
                takeInfoBiz.closeTakeInfo(closeTakeInfo.getTakeId(), closeTakeInfo.getSellerId(), operatorId,takeCloseType,true);
            }else{
                takeInfoBiz.doCancelTakeInfo(closeTakeInfo.getTakeId(), takeCancelType, operatorId);
            }
        }
    }

    private List<TakeProxyMap> getTakeProxyMaps(String takeId, boolean primary) {
        List<TakeProxyMap> takeProxyMapList;
        if(primary){
            takeProxyMapList = takeProxyMapBiz.findByPrimaryTakeIds(Lists.newArrayList(takeId));
        }else{
            takeProxyMapList = takeProxyMapBiz.findBySecondaryTakeIds(Lists.newArrayList(takeId));
        }
        return takeProxyMapList;
    }

    private void closeOrCancelSecondaryTakeInfoHook(TakeInfoDTO secondaryTakeInfo, TakeStatusEnum takeStatusEnum) {
        TakeProxyMap takeProxyMap = takeProxyMapBiz.findBySecondaryTakeId(secondaryTakeInfo.getTakeId());
        log.info("closeOrCancelSecondaryTakeInfoHook:" + JSON.toJSONString(takeProxyMap));
        if (takeProxyMap == null || CsStringUtils.isBlank(takeProxyMap.getPrimaryTakeId())) {
            return;
        }
        //二级发货单为自提时不做此限制(因为自提模式下一级发货单和二级运单关联)
        if (!CsStringUtils.equals(secondaryTakeInfo.getDeliverWay(), PickingBillTypeEnum.BUYER_TAKE.getCode())) {
            //二级发货单关闭/取消：如果一级发货单已经生成，则不能关闭/取消
            String msg = TakeStatusEnum.CLOSED == takeStatusEnum ? "关闭" : "取消";
            log.error("takeInfoHook 二级发货单{},如果一级发货单已经生成，则不能{},二级发货单id:{},一级发货单id:{}",
                    msg, msg, secondaryTakeInfo.getTakeId(), takeProxyMap.getPrimaryTakeId());
            throw new BizException(BasicCode.UNDEFINED_ERROR, "不能" + msg);
        }
    }

    private void closeOrCancelSecondaryTakeInfoHook(TakeInfo secondaryTakeInfo, TakeStatusEnum takeStatusEnum) {
        TakeProxyMap takeProxyMap = takeProxyMapBiz.findBySecondaryTakeId(secondaryTakeInfo.getTakeId());
        log.info("closeOrCancelSecondaryTakeInfoHook:" + JSON.toJSONString(takeProxyMap));
        if (takeProxyMap == null || CsStringUtils.isBlank(takeProxyMap.getPrimaryTakeId())) {
            return;
        }
        //二级发货单为自提时不做此限制(因为自提模式下一级发货单和二级运单关联)
        if (!CsStringUtils.equals(secondaryTakeInfo.getDeliverWay(), PickingBillTypeEnum.BUYER_TAKE.getCode())) {
            //二级发货单关闭/取消：如果一级发货单已经生成，则不能关闭/取消
            String msg = TakeStatusEnum.CLOSED == takeStatusEnum ? "关闭" : "取消";
            log.error("takeInfoHook 二级发货单{},如果一级发货单已经生成，则不能{},二级发货单id:{},一级发货单id:{}"
                    , msg, msg, secondaryTakeInfo.getTakeId(), takeProxyMap.getPrimaryTakeId());
            throw new BizException(BasicCode.UNDEFINED_ERROR, "不能" + msg);
        }
    }

    @Override
    public void autoCreateTakeInfo(TakeAutoCreateDTO takeAutoCreateDTO) {
        log.info("takeAutoCreateDTO:{}", takeAutoCreateDTO);
        OrderInfo orderInfo = orderInfoBiz.get(takeAutoCreateDTO.getOrderId());
        TakeInfoCreateDTO takeInfoCreateDTO = new TakeInfoCreateDTO();
        takeInfoCreateDTO.setAssignBuyerCarrierFlag(false);
        takeInfoCreateDTO.setTakeTime(new Date());
        takeInfoCreateDTO.setTakeTimeQuantum(TakeTimeQuantumEnum.TTQ030020500.code());
        takeInfoCreateDTO.setOrderId(orderInfo.getOrderId());
        takeInfoCreateDTO.setDeliverWay(orderInfo.getDeliverWay());
        takeInfoCreateDTO.setOperator(takeAutoCreateDTO.getOperator());
        takeInfoCreateDTO.setOperatorMemberId(takeAutoCreateDTO.getOperatorMemberId());
        takeInfoCreateDTO.setTakeCreateType(takeAutoCreateDTO.getTakeCreateType());
        takeInfoCreateDTO.setTakeStatus(CsStringUtils.equals(takeAutoCreateDTO.getTakeCreateType(), TakeCreateType.SELLER_CREATE.getCode()) ?
                TakeStatusEnum.PENDING_DELIVERY.getCode() : TakeStatusEnum.PENDING_CONFIRMED.getCode());
        takeInfoCreateDTO.setTakeItems(Lists.newArrayList());
        //指派承运商标识
        if (takeAutoCreateDTO.getTakeCarrierDTO() != null &&
                CsStringUtils.isNotBlank(takeAutoCreateDTO.getTakeCarrierDTO().getCarrierId())) {
            takeInfoCreateDTO.setAssignBuyerCarrierFlag(Boolean.TRUE);
        }
        OrderInfoExtDTO orderInfoExtDTO = orderInfoExtBiz.findByOrderId(orderInfo.getOrderId());
        //背靠背订单都是单商品的
        for (OrderItem orderItem : orderItemBiz.findByOrderId(orderInfo.getOrderId())) {
            TakeInfoItemCreateDTO takeInfoItemCreateDTO = new TakeInfoItemCreateDTO();
            //增加润改砂浆数量
            takeInfoItemCreateDTO.setTakeQuantity(ArithUtils.add(orderItem.getItemQuantity(), orderInfoExtDTO.getLubricityQuantity()));
            takeInfoItemCreateDTO.setOrderItemId(orderItem.getOrderItemId());
            takeInfoItemCreateDTO.setResourceId(orderItem.getResourceId());
            takeInfoItemCreateDTO.setTakeVehicleList(Lists.newArrayListWithCapacity(0));
            takeInfoItemCreateDTO.setTakeShipList(Lists.newArrayListWithCapacity(0));
            //设置承运商
            if (takeInfoCreateDTO.getAssignBuyerCarrierFlag()) {
                TakeCarrierDTO takeCarrierDTO = new TakeCarrierDTO();
                takeCarrierDTO.setCarrierId(takeAutoCreateDTO.getTakeCarrierDTO().getCarrierId());
                if (CsStringUtils.isBlank(takeAutoCreateDTO.getTakeCarrierDTO().getCarrierName()) ||
                        CsStringUtils.isBlank(takeAutoCreateDTO.getTakeCarrierDTO().getCarrierType())) {
                    MemberSimpleDTO memberSimpleDTO = memberService.findMemberSimpleById(takeAutoCreateDTO.getTakeCarrierDTO().getCarrierId());
                    takeCarrierDTO.setCarrierName(memberSimpleDTO.getMemberName());
                    takeCarrierDTO.setCarrierType(memberSimpleDTO.getCarrierType());
                }
                takeCarrierDTO.setDeliverQuantity(orderItem.getItemQuantity());
                takeInfoItemCreateDTO.setTakeCarrierDTOList(Lists.newArrayList(takeCarrierDTO));
            }
            takeInfoCreateDTO.getTakeItems().add(takeInfoItemCreateDTO);
        }
        //触发发货单创建事件
        takeInfoBiz.createTakeInfo(Lists.newArrayList(takeInfoCreateDTO));
    }
}
