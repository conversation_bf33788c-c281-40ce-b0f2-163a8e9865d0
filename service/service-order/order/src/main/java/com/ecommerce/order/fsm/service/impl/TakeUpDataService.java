package com.ecommerce.order.fsm.service.impl;

import com.alibaba.fastjson.JSON;
import com.ecommerce.base.api.dto.warehouse.WarehouseDetailsDTO;
import com.ecommerce.base.api.service.IWarehouseService;
import com.ecommerce.common.annotation.AddLog;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.service.RedisService;
import com.ecommerce.common.service.common.BaseBiz;
import com.ecommerce.common.service.lock.RedisLockService;
import com.ecommerce.common.utils.ArithUtils;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.common.utils.MD5;
import com.ecommerce.goods.api.dto.SpecialGoodsAttributeDTO;
import com.ecommerce.goods.api.dto.contract.TrContractGoodsDTO;
import com.ecommerce.goods.api.enums.DeliveryWayEnum;
import com.ecommerce.goods.api.service.IContractService;
import com.ecommerce.goods.api.service.IGoodsService;
import com.ecommerce.logistics.api.dto.waybill.NotifyLeaveWarehouseDTO;
import com.ecommerce.logistics.api.enums.TransportToolTypeEnum;
import com.ecommerce.logistics.api.enums.WarehouseTypeEnum;
import com.ecommerce.member.api.dto.member.config.MemberConfigEnum;
import com.ecommerce.order.api.dto.CloseTakeInfoDTO;
import com.ecommerce.order.api.dto.FinishTakeInfoDTO;
import com.ecommerce.order.api.dto.OrderAdjustDTO;
import com.ecommerce.order.api.dto.OrderDTO;
import com.ecommerce.order.api.dto.OrderItemDTO;
import com.ecommerce.order.api.dto.OrderPayInfoInstructionsDTO;
import com.ecommerce.order.api.dto.OrderPayinfoDTO;
import com.ecommerce.order.api.dto.OversoldDTO;
import com.ecommerce.order.api.dto.OversoldQueryDTO;
import com.ecommerce.order.api.dto.SplitTriggerDTO;
import com.ecommerce.order.api.dto.TakeInfoDTO;
import com.ecommerce.order.api.dto.UpdateTakeInfoShippedDTO;
import com.ecommerce.order.api.dto.UpdateTakeInfoSignDTO;
import com.ecommerce.order.api.dto.UpdateTakeItemShippedDTO;
import com.ecommerce.order.api.dto.UpdateTakeItemSignDTO;
import com.ecommerce.order.api.dto.WaybillSplitDTO;
import com.ecommerce.order.api.dto.logistics.OverSendOutVerifyDTO;
import com.ecommerce.order.api.dto.logistics.OverSendOutVerifyResponseDTO;
import com.ecommerce.order.api.dto.logistics.ShipBillRefundDTO;
import com.ecommerce.order.api.dto.take.DeliveringTakInfoDTO;
import com.ecommerce.order.api.enums.AdjustAddWayEnum;
import com.ecommerce.order.api.enums.OrderStatusEnum;
import com.ecommerce.order.api.enums.PayStatusEnum;
import com.ecommerce.order.api.enums.PayTypeEnum;
import com.ecommerce.order.api.enums.ProxyOrderTypeEnum;
import com.ecommerce.order.api.enums.TakeCloseType;
import com.ecommerce.order.api.enums.TakeFinishType;
import com.ecommerce.order.api.enums.TakeStatus;
import com.ecommerce.order.biz.IOrderAtomicBiz;
import com.ecommerce.order.biz.IOrderErpBiz;
import com.ecommerce.order.biz.IOrderItemQuantityBiz;
import com.ecommerce.order.biz.IOrderOverSellBiz;
import com.ecommerce.order.biz.IOrderPayinfoJoinBiz;
import com.ecommerce.order.biz.fsm.IOrderInfoBiz;
import com.ecommerce.order.biz.fsm.IOrderInfoExtBiz;
import com.ecommerce.order.biz.fsm.IOrderSendQuantityLogBiz;
import com.ecommerce.order.biz.fsm.ITakeInfoBiz;
import com.ecommerce.order.biz.fsm.ITakeItemBiz;
import com.ecommerce.order.biz.impl.OrderProxyMapBiz;
import com.ecommerce.order.biz.impl.fsm.OrderItemBiz;
import com.ecommerce.order.dao.dto.PaymentAmountDO;
import com.ecommerce.order.dao.dto.PaymentAmountQueryDO;
import com.ecommerce.order.dao.dto.SupplementPaymentDO;
import com.ecommerce.order.dao.vo.OrderInfo;
import com.ecommerce.order.dao.vo.OrderItem;
import com.ecommerce.order.dao.vo.OrderPayinfo;
import com.ecommerce.order.dao.vo.TakeInfo;
import com.ecommerce.order.dao.vo.TakeItem;
import com.ecommerce.order.fsm.StateMachineConstants;
import com.ecommerce.order.fsm.domain.StatusModelDO;
import com.ecommerce.order.fsm.even.OrderChangeEventEnum;
import com.ecommerce.order.fsm.even.TakeChangeEventEnum;
import com.ecommerce.order.fsm.manager.OrderStateMachineEventManager;
import com.ecommerce.order.fsm.manager.TakeStateMachineEventManager;
import com.ecommerce.order.fsm.service.IOrderCommonService;
import com.ecommerce.order.fsm.service.IOrderComputeService;
import com.ecommerce.order.fsm.service.IOrderPaymentService;
import com.ecommerce.order.fsm.service.IOrderQueryService;
import com.ecommerce.order.fsm.service.IOrderSplitBillService;
import com.ecommerce.order.fsm.service.ITakeInfoService;
import com.ecommerce.order.fsm.service.ITakeUpDataService;
import com.ecommerce.order.fsm.status.TakeStatusEnum;
import com.ecommerce.order.service.impl.OrderSMSMessageProducer;
import com.ecommerce.order.service.message.IMessageService;
import com.ecommerce.order.service.order.IOrderRealTimeComputeService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 * Date: Create in 下午5:40 20/7/6
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TakeUpDataService implements ITakeUpDataService {

    private final IOrderOverSellBiz orderOverSellBiz;
    private final IOrderPayinfoJoinBiz orderPayInfoJoinBiz;
    private final IOrderInfoBiz orderInfoBiz;
    private final IOrderInfoExtBiz orderInfoExtBiz;
    private final ITakeInfoBiz takeInfoBiz;
    private final OrderItemBiz orderItemBiz;
    private final ITakeItemBiz takeItemBiz;
    private final IOrderErpBiz orderErpBiz;
    private final IOrderItemQuantityBiz orderItemQuantityBiz;
    private final IOrderAtomicBiz orderAtomicBiz;
    private final IOrderSendQuantityLogBiz orderSendQuantityLogBiz;
    private final IGoodsService goodsService;
    private final IOrderPaymentService orderPaymentService;
    private final IOrderQueryService orderQueryService;
    private final IWarehouseService warehouseService;
    private final IMessageService messageService;
    private final IOrderSplitBillService orderSplitBillService;
    private final ITakeInfoService takeInfoService;
    private final IOrderRealTimeComputeService orderRealTimeComputeService;
    private final IOrderCommonService orderCommonService;
    private final RedisLockService redisLockService;
    private final RedisService redisService;
    private final OrderSMSMessageProducer orderSMSMessageProducer;
    private final OrderStateMachineEventManager orderStateMachineEventManager;
    private final TakeStateMachineEventManager takeStateMachineEventManager;
    private final IContractService contractService;
    private final IOrderComputeService orderComputeService;
    private final OrderProxyMapBiz orderProxyMapBiz;


    private static final String LOGISTICS = "logistics";

    @Override
    @AddLog(operatorIndex = 0, operatorFieldName = "operator")
    public ItemResult<Boolean> doLogisticCloseTakeInfo(CloseTakeInfoDTO closeTakeInfoDTO) {
        log.info("物流通知交易 关闭发货单:" + JSON.toJSONString(closeTakeInfoDTO));
        String identifier = "";
        String orderId = "";
        try {
            TakeInfoDTO takeInfo = takeInfoService.getTakeInfoByCode(closeTakeInfoDTO.getTakeCode());
            Set<String> finalStatus = Sets.newHashSet(
                    TakeStatus.CLOSED.getCode(),
                    TakeStatus.CANCELED.getCode(),
                    TakeStatus.FINISHED.getCode());
            if (finalStatus.contains(takeInfo.getTakeStatus())) {
                log.info("发货单已结束 takId:{},takeStatus:{}",takeInfo.getTakeId(),takeInfo.getTakeStatus());
                return new ItemResult<>(true);
            }
            orderId = takeInfo.getOrderId();
            identifier = redisLockService.lock(orderId);
            //触发发货单关闭事件
            StatusModelDO statusModelDO = new StatusModelDO();
            statusModelDO.setBizId(takeInfo.getTakeId());
            TakeStatusEnum takeStatusEnum = TakeStatusEnum.getByCode(takeInfo.getTakeStatus());
            if (takeStatusEnum == null) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "无效的发货单状态");
            }
            statusModelDO.setCurrentState(takeStatusEnum.getCode());
            statusModelDO.addParam(StateMachineConstants.TAKE_ID, takeInfo.getTakeId());
            statusModelDO.addParam(StateMachineConstants.OPERATOR, closeTakeInfoDTO.getOperator());
            statusModelDO.addParam(StateMachineConstants.TAKE_INFO_CLOSE_TYPE, TakeCloseType.LOGISTIC_CLOSE);
            takeStateMachineEventManager.sendStatusChangeEvent(statusModelDO, TakeChangeEventEnum.CLOSE_TAKE);
            //触发订单状态变更
            orderCommonService.triggerOrderAutoEnd(orderId);
        } catch (Exception e) {
            log.info("物流关闭发货单异常，异常信息：{}", e.getMessage());
            return new ItemResult<>(false);
        } finally {
            if (CsStringUtils.isNotEmpty(identifier)) {
                redisLockService.unlock(orderId, identifier);
            }
        }

        return new ItemResult<>(true);
    }

    @Override
    @AddLog(operatorIndex = 0, operatorFieldName = "operator")
    public ItemResult<Boolean> doLogisticFinishTakeInfo(FinishTakeInfoDTO finishTakeInfoDTO) {
        log.info("物流通知交易 完成发货单:" + JSON.toJSONString(finishTakeInfoDTO));
        String identifier = "";
        String orderId = "";

        TakeInfoDTO takeInfo = takeInfoService.getTakeInfoByCode(finishTakeInfoDTO.getTakeCode());
        try {
            Set<String> finalStatus = Sets.newHashSet(
                    TakeStatus.CLOSED.getCode(),
                    TakeStatus.CANCELED.getCode(),
                    TakeStatus.FINISHED.getCode());
            if (finalStatus.contains(takeInfo.getTakeStatus())) {
                log.info("发货单已结束 takId:{},takeStatus:{}",takeInfo.getTakeId(),takeInfo.getTakeStatus());
                return new ItemResult<>(true);
            }
            orderId = takeInfo.getOrderId();
            identifier = redisLockService.lock(orderId);
            //触发发货单完成事件
            StatusModelDO statusModelDO = new StatusModelDO();
            statusModelDO.setBizId(takeInfo.getTakeId());
            TakeStatusEnum takeStatusEnum = TakeStatusEnum.getByCode(takeInfo.getTakeStatus());
            if (takeStatusEnum == null) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "无效的发货单状态");
            }
            statusModelDO.setCurrentState(takeStatusEnum.getCode());
            statusModelDO.addParam(StateMachineConstants.TAKE_ID, takeInfo.getTakeId());
            statusModelDO.addParam(StateMachineConstants.OPERATOR, finishTakeInfoDTO.getOperator());
            statusModelDO.addParam(StateMachineConstants.TAKE_INFO_FINISH_TYPE, TakeFinishType.LOGISTIC_FINISH);
            takeStateMachineEventManager.sendStatusChangeEvent(statusModelDO, TakeChangeEventEnum.FINISH_TAKE);
            //尝试触发订单自动完成
            orderCommonService.triggerOrderAutoEnd(takeInfo.getOrderId());
        } catch (Exception e) {
            log.info("TakeInfoDTO: {}", JSON.toJSONString(takeInfo));
            log.info("物流完成发货单异常，异常信息：{}", e.getMessage());
            return new ItemResult<>(false);
        } finally {
            if (CsStringUtils.isNotEmpty(identifier)) {
                redisLockService.unlock(orderId, identifier);
            }
        }
        return new ItemResult<>(true);
    }

    @Override
    public ItemResult<Boolean> doLogisticDeliveringTakInfo(DeliveringTakInfoDTO deliveringTakInfoDTO) {
        log.info("物流通知交易 更新发货单状态为发货中: {}" , deliveringTakInfoDTO);
        if (deliveringTakInfoDTO == null || CsStringUtils.isBlank(deliveringTakInfoDTO.getTakeCode())) {
            log.info("doLogisticDeliveringTakInfo 请求参数不正确");
            return new ItemResult<>(true);
        }
        TakeInfo takeInfo;
        try {
            takeInfo = takeInfoBiz.getTakeInfoByCode(deliveringTakInfoDTO.getTakeCode());
            if (takeInfo == null) {
                log.info("doLogisticDeliveringTakInfo 请求参数不正确 发货单不存在");
                return new ItemResult<>(true);
            }
        }catch (BizException e){
            log.info("doLogisticDeliveringTakInfo 发货查询错误:{}",e.getMessage());
            return new ItemResult<>(true);
        }
        if (!TakeStatusEnum.PENDING_DELIVERY.getCode().equals(takeInfo.getTakeStatus())) {
            log.info("发货单不是待发货状态 takId:{},takeStatus:{}",takeInfo.getTakeId(),takeInfo.getTakeStatus());
            return new ItemResult<>(true);
        }
        //触发 待发货 --> 发货中 仅是状态更新
        StatusModelDO statusModelDO = new StatusModelDO();
        statusModelDO.setBizId(takeInfo.getTakeId());
        TakeStatusEnum takeStatusEnum = TakeStatusEnum.getByCode(takeInfo.getTakeStatus());
        if (takeStatusEnum == null) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "无效的发货单状态");
        }
        statusModelDO.setCurrentState(takeStatusEnum.getCode());
        statusModelDO.addParam(StateMachineConstants.TAKE_ID, takeInfo.getTakeId());
        statusModelDO.addParam(StateMachineConstants.OPERATOR, deliveringTakInfoDTO.getOperator());
        takeStateMachineEventManager.sendStatusChangeEvent(statusModelDO, TakeChangeEventEnum.DELIVERING_TAKE);

        OrderInfo orderInfo = orderInfoBiz.get(takeInfo.getOrderId());
        //触发订单状态变更:待发货->发货中
        if (orderInfo != null && CsStringUtils.equals(orderInfo.getOrderStatus(), OrderStatusEnum.WAIT_DELIVERED.code())) {
            OrderInfo saveOrder = new OrderInfo();
            saveOrder.setOrderId(takeInfo.getOrderId());
            BaseBiz.setUpdateOperInfo(saveOrder, deliveringTakInfoDTO.getOperator());
            StatusModelDO statusModelDO1 = new StatusModelDO();
            statusModelDO1.setBizId(takeInfo.getOrderId());
            statusModelDO1.setCurrentState(OrderStatusEnum.WAIT_DELIVERED.getCode());
            statusModelDO1.addParam("saveOrder", saveOrder);
            orderStateMachineEventManager.sendStatusChangeEvent(statusModelDO1, OrderChangeEventEnum.NOTIFY_SHIPPING);
        }
        return new ItemResult<>(true);
    }

    @Override
    @AddLog(operatorIndex = 0, operatorFieldName = "operator")
    @Transactional(isolation = Isolation.READ_COMMITTED, rollbackFor = Exception.class)
    public ItemResult<Boolean> doLogisticUpdateShippedQuantity(UpdateTakeInfoShippedDTO updateTakeInfoShippedDTO) {
        log.info("物流通知交易 更新发货单发货数量:" + JSON.toJSONString(updateTakeInfoShippedDTO));
        TakeInfoDTO takeInfo = takeInfoService.getTakeInfoByCode(updateTakeInfoShippedDTO.getTakeCode());
        if (takeInfo == null) {
            throw new BizException(BasicCode.DATA_NOT_EXIST, "发货单");
        }
        String identifier = "";
        String orderId = takeInfo.getOrderId();
        try {
            identifier = redisLockService.lock(orderId);
            //记录通知数据
            log.info("物流通知交易 更新发货单发货数量 记录入参数据");
            orderSendQuantityLogBiz.saveLog(takeInfo.getOrderId(),updateTakeInfoShippedDTO);
            //发货单发货事件 待发货-->发货中
            if (CsStringUtils.equals(takeInfo.getTakeStatus(), TakeStatusEnum.PENDING_DELIVERY.getCode())) {
                log.info("物流通知交易 更新发货单发货数量 更新发货单状态为发货中");
                StatusModelDO statusModelDO = new StatusModelDO();
                statusModelDO.setBizId(takeInfo.getTakeId());
                statusModelDO.setCurrentState(TakeStatusEnum.PENDING_DELIVERY.getCode());
                statusModelDO.addParam(StateMachineConstants.TAKE_ID, takeInfo.getTakeId());
                statusModelDO.addParam(StateMachineConstants.OPERATOR, updateTakeInfoShippedDTO.getOperator());
                takeStateMachineEventManager.sendStatusChangeEvent(statusModelDO, TakeChangeEventEnum.DELIVERING_TAKE);
            }
            //触发订单状态变更:待发货->发货中
            OrderInfo orderInfo = orderInfoBiz.get(takeInfo.getOrderId());
            if (CsStringUtils.equals(orderInfo.getOrderStatus(), OrderStatusEnum.WAIT_DELIVERED.code())) {
                log.info("物流通知交易 更新发货单发货数量 更新订单状态为发货中");
                OrderInfo saveOrder = new OrderInfo();
                saveOrder.setOrderId(takeInfo.getOrderId());
                BaseBiz.setUpdateOperInfo(saveOrder, updateTakeInfoShippedDTO.getOperator());
                StatusModelDO statusModelDO = new StatusModelDO();
                statusModelDO.setBizId(takeInfo.getOrderId());
                statusModelDO.setCurrentState(OrderStatusEnum.WAIT_DELIVERED.getCode());
                statusModelDO.addParam("saveOrder", saveOrder);
                orderStateMachineEventManager.sendStatusChangeEvent(statusModelDO, OrderChangeEventEnum.NOTIFY_SHIPPING);
            }
            OrderDTO orderDTO = orderQueryService.getOrderInfo(orderId);
            ItemResult<SpecialGoodsAttributeDTO> goodsResult = goodsService.getSpecialGoodsAttribute(orderDTO.getOrderItems().get(0).getGoodsId());
            log.info("SpecialGoodsAttributeDTO: {}", JSON.toJSONString(goodsResult.getData()));
            if (!goodsResult.isSuccess()) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "查询商品特殊属性失败");
            }
            //更新发货单发货数量
            log.info("物流通知交易 更新发货单发货数量 更新发货单发货数量、订单发货数量");
            if (goodsResult.getData().getConcreteFlag() == 1) {
                takeInfoBiz.recomputeTakeInfoQuantity(orderDTO);
            } else {
                takeInfoBiz.renewalTakeInfoQuantity(takeInfo.getTakeId(), updateTakeInfoShippedDTO);
            }
            //计算订单实发金额
            log.info("物流通知交易 更新发货单发货数量 计算订单实发金额");
            List<TakeInfoDTO> takeInfoList = takeInfoBiz.getTakeInfoDetailsByOrderId(takeInfo.getOrderId());
            //使用物流传回的最新实时单价进行结算
            Map<String, BigDecimal> orderItemRealPriceMap = Maps.newHashMap();
            for (UpdateTakeItemShippedDTO shippedItem : updateTakeInfoShippedDTO.getUpdateTakeItemShippedDTO()) {
                orderItemRealPriceMap.put(shippedItem.getResourceId(), shippedItem.getGoodsPrice());
            }
            OrderDTO newOrderDTO = orderQueryService.getOrderInfo(orderId);
            newOrderDTO.setOrderInfoExtDTO(orderDTO.getOrderInfoExtDTO());
            orderRealTimeComputeService.calculateOrderItem(newOrderDTO, takeInfoList, null, orderItemRealPriceMap, LOGISTICS);
            //非商品或者汽运按车分账 船运按运单分账
            if (goodsResult.getData().getConcreteFlag() != 1) {
                //触发运单分账
                List<SplitTriggerDTO> splitTriggerList = Lists.newArrayList();
                for (UpdateTakeItemShippedDTO itemShippedDTO : updateTakeInfoShippedDTO.getUpdateTakeItemShippedDTO()) {
                    SplitTriggerDTO splitTriggerDTO = new SplitTriggerDTO();
                    splitTriggerDTO.setResourceId(itemShippedDTO.getResourceId());
                    //出厂数量
                    splitTriggerDTO.setNum(itemShippedDTO.getShippedQuantity());
                    //物流费用
                    splitTriggerDTO.setLogisticsAmount(itemShippedDTO.getCost());
                    //商品费用
                    splitTriggerDTO.setGoodsAmount(itemShippedDTO.getGoodsAmount());
                    splitTriggerDTO.setActualUnitPrice(itemShippedDTO.getGoodsPrice());//此价格为吨单价
                    splitTriggerList.add(splitTriggerDTO);
                }
                WaybillSplitDTO waybillSplitDTO = new WaybillSplitDTO();
                waybillSplitDTO.setSplitTriggerList(splitTriggerList);
                waybillSplitDTO.setOrderId(orderDTO.getOrderId());
                waybillSplitDTO.setOrderCode(orderDTO.getOrderCode());
                waybillSplitDTO.setWaybillNum(updateTakeInfoShippedDTO.getWaybillNum());
                log.info("物流通知交易 更新发货单发货数量 触发分账:" + JSON.toJSONString(waybillSplitDTO));
                orderSplitBillService.triggerSplitByWaybill(waybillSplitDTO);
            }
            //尝试触发订单自动完成
            log.info("物流通知交易 更新发货单发货数量 尝试触发订单自动完成 orderId:{}",takeInfo.getOrderId());
            orderCommonService.triggerOrderAutoEnd(takeInfo.getOrderId());
            //运单出站短信通知
            if (CsStringUtils.isNotEmpty(updateTakeInfoShippedDTO.getWaybillNum())) {
                log.info("物流通知交易 更新发货单发货数量 sendStartShippingAsync orderId:{},waybillNum:{}",takeInfo.getOrderId(),updateTakeInfoShippedDTO.getWaybillNum());
                orderSMSMessageProducer.sendStartShippingAsync(takeInfo.getOrderId(), Lists.newArrayList(updateTakeInfoShippedDTO.getWaybillNum()));
            }
        } catch (BizException be) {
            throw be;
        } catch (Exception e) {
            log.error("物流更新发货单发货数量异常 异常信息:{}", e.getMessage(),e);
            throw new BizException(BasicCode.UNDEFINED_ERROR, "更新发货数量异常");
        } finally {
            if (CsStringUtils.isNotEmpty(orderId) && CsStringUtils.isNotEmpty(identifier)) {
                redisLockService.unlock(orderId, identifier);
            }
        }
        return new ItemResult<>(true);
    }

    /**
     * 物流签收参数校验
     * @param updateTakeInfoSignDTO 签收对象
     */
    private void logisticsSignParamValidate(UpdateTakeInfoSignDTO updateTakeInfoSignDTO) {
        if (CsStringUtils.isEmpty(updateTakeInfoSignDTO.getTakeCode())) {
            throw new BizException(BasicCode.INVALID_PARAM, "发货单号");
        }
        if (CsStringUtils.isEmpty(updateTakeInfoSignDTO.getOperator())) {
            throw new BizException(BasicCode.INVALID_PARAM, "操作人");
        }
        if (CsStringUtils.isEmpty(updateTakeInfoSignDTO.getAdjustAddWay())) {
            throw new BizException(BasicCode.INVALID_PARAM, "签收方式");
        }
        if (CollectionUtils.isEmpty(updateTakeInfoSignDTO.getUpdateTakeItemSignDTOs())) {
            throw new BizException(BasicCode.INVALID_PARAM, "签收商品列表");
        }
        for (UpdateTakeItemSignDTO itemSign : updateTakeInfoSignDTO.getUpdateTakeItemSignDTOs()) {
            if (CsStringUtils.isEmpty(itemSign.getResourceId())) {
                throw new BizException(BasicCode.INVALID_PARAM, "订单明细ID");
            }
            if (itemSign.getBuyerSignQuantity() == null && itemSign.getSellerSignQuantity() == null) {
                throw new BizException(BasicCode.INVALID_PARAM, "签收数量");
            }
            if (itemSign.getEmptyLoadCost() == null) {
                throw new BizException(BasicCode.INVALID_PARAM, "空载费");
            }
            if (itemSign.getAdjustQuantity() == null) {
                throw new BizException(BasicCode.INVALID_PARAM, "签收调整量");
            }
        }
    }

    @Override
    @AddLog(operatorIndex = 0, operatorFieldName = "operator")
    @Transactional(isolation = Isolation.READ_COMMITTED, rollbackFor = Exception.class)
    public ItemResult<Boolean> doLogisticUpdateSignQuantity(UpdateTakeInfoSignDTO updateTakeInfoSignDTO) {
        log.info("物流通知交易 更新发货单签收数量:" + JSON.toJSONString(updateTakeInfoSignDTO));
        //参数验证
        logisticsSignParamValidate(updateTakeInfoSignDTO);
        TakeInfoDTO takeInfo = takeInfoService.getTakeInfoByCode(updateTakeInfoSignDTO.getTakeCode());
        if (takeInfo == null) {
            throw new BizException(BasicCode.DATA_NOT_EXIST, "发货单");
        }
        //添加分布式锁控制
        String identifier = "";
        String orderId = takeInfo.getOrderId();
        try {
            identifier = redisLockService.lock(orderId);
            //变更签收日志(为了支持后续重复签收需求)
            orderSendQuantityLogBiz.changeLog(takeInfo.getOrderId(), updateTakeInfoSignDTO);
            //物流签收时创建订单调价记录
            OrderAdjustDTO orderAdjustDTO = new OrderAdjustDTO();
            orderAdjustDTO.setOrderId(takeInfo.getOrderId());
            orderAdjustDTO.setCarNum(takeInfo.getCarNum());
            //设置签收方式
            String adjustAddWay = updateTakeInfoSignDTO.getAdjustAddWay();
            orderAdjustDTO.setAddWay(adjustAddWay);
            //都是到位价没有物流费记录,所以不用加物流费
            List<UpdateTakeItemSignDTO> updateTakeItemSignDTOS = updateTakeInfoSignDTO.getUpdateTakeItemSignDTOs();
            UpdateTakeItemSignDTO updateTakeItemSignDTO = updateTakeItemSignDTOS.get(0);
            if (updateTakeItemSignDTO == null) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "物流更新签收数量子项对象不能为空");
            }
            OrderDTO orderDTO = orderQueryService.getOrderInfo(orderId);
            //重新计算发货单签收量
            takeInfoBiz.recomputeTakeInfoQuantity(orderDTO);
            //按签收量重新计算实际物流和商品费用
            OrderDTO newOrderDTO = orderQueryService.getOrderInfo(orderId);
            newOrderDTO.setOrderInfoExtDTO(orderDTO.getOrderInfoExtDTO());
            List<TakeInfoDTO> takeInfoList = takeInfoBiz.getTakeInfoDetailsByOrderId(orderId);
            orderRealTimeComputeService.calculateOrderItem(newOrderDTO, takeInfoList,
                    AdjustAddWayEnum.SELLER_SIGIN.getCode(), null, LOGISTICS);
            return new ItemResult<>(true);
        } catch (Exception e) {
            log.info("物流更新发货单签收数量异常:{}", e.getMessage(),e);
            return new ItemResult<>(false);
        } finally {
            if (CsStringUtils.isNotEmpty(orderId) && CsStringUtils.isNotEmpty(identifier)) {
                redisLockService.unlock(orderId, identifier);
            }
        }
    }

    /**
     *
     * 同一个船运运单: 可能会多次超发验证，超发验证可能会导致多次补款
     * 船运超发验证: 实提量 * 最新的价格 - 订单多余的金额(支付金额 - 执行金额) = 超发金额
     * 汽运超发验证: 实提量 * 订单的价格 - 订单多余的金额(支付金额 - 执行金额) = 超发金额
     *
     * 超发金额 - 订单待支付金额 = 要生成的补款单金额
     *
     * 物流出站超发验证
     * @param order 订单对象
     * @param overSendOutVerifyDTO 超发验证对象
     * @return 出站标识
     */
    @Override
    public OverSendOutVerifyResponseDTO checkoutOverSendOut(OrderDTO order, OverSendOutVerifyDTO overSendOutVerifyDTO) {
        //检查当前运单是否已发起过超发补款
        OrderQueryService.nullBigDecimalAsZero(order);
        //计算当前订单的实际消耗总金额
        OrderItemDTO currentOrderItem = order.getOrderItems().stream()
                .filter(item -> CsStringUtils.equals(overSendOutVerifyDTO.getOrderItemId(), item.getOrderItemId())).findFirst().orElse(null);
        if (currentOrderItem == null) {
            throw new BizException(BasicCode.DATA_NOT_EXIST, "订单明细：" + overSendOutVerifyDTO.getOrderItemId());
        }
        SupplementPaymentDO supplementPaymentDO = new SupplementPaymentDO();
        supplementPaymentDO.setOverOthersAmount(BigDecimal.ZERO);
        supplementPaymentDO.setOrder(order);
        supplementPaymentDO.setOperator(overSendOutVerifyDTO.getOperator());
        supplementPaymentDO.setTakeCode(overSendOutVerifyDTO.getTakeCode());
        TakeInfo takeInfo = takeInfoBiz.findByCode(supplementPaymentDO.getTakeCode());
        supplementPaymentDO.setTakeId(takeInfo == null ? null : takeInfo.getTakeId());
        //判断超发 并返回超发金额信息 更新到 supplementPaymentDO
        boolean overFlag = updateSupplementPaymentDO(supplementPaymentDO,order,currentOrderItem,overSendOutVerifyDTO);
        //生成超发记录对应的支付单信息
        OrderPayinfoDTO orderPayinfoDTO = orderPayInfoJoinBiz.createOversoldPayInfoOnly(supplementPaymentDO);
        OversoldDTO oversoldDTO = new OversoldDTO();
        oversoldDTO.setPayDescription(OrderPayInfoInstructionsDTO.list2Json(supplementPaymentDO.getOrderPayInfoInstructionsDTOList()));
        oversoldDTO.setOversoldAmount(supplementPaymentDO.getOverResourceAmount().add(supplementPaymentDO.getOverLogisticsAmount()));//超发金额
        oversoldDTO.setOversoldResourceAmount(supplementPaymentDO.getOverResourceAmount());
        oversoldDTO.setOversoldLogisticsAmount(supplementPaymentDO.getOverLogisticsAmount());
        oversoldDTO.setCurrency(order.getCurrency());
        oversoldDTO.setCreateTime(new Date());
        oversoldDTO.setCreateUser(overSendOutVerifyDTO.getOperator());
        oversoldDTO.setOrderId(order.getOrderId());
        oversoldDTO.setLogisticsId(overSendOutVerifyDTO.getWaybillNum());
        //超发量 = 实际出厂量 - 计划提货量
        oversoldDTO.setOversoldNum(overSendOutVerifyDTO.getActualQuantity().subtract(overSendOutVerifyDTO.getQuantity()));
        oversoldDTO.setResourceId(overSendOutVerifyDTO.getOrderItemId());
        oversoldDTO.setSource(LOGISTICS);
        //保存运单超发验证记录、新增或修改补款单信息 并尝试自动支付
        boolean overResult = orderOverSellBiz.saveOrUpdateOverSoldAndPayInfo(oversoldDTO,orderPayinfoDTO);
        log.info("overFlag:{},overResult:{}",overFlag,overResult);
        OverSendOutVerifyResponseDTO overSendOutVerifyResponseDTO = new OverSendOutVerifyResponseDTO(!overFlag || (overResult));
        overSendOutVerifyResponseDTO.setPayInfoId(orderPayinfoDTO.getPayinfoId());
        overSendOutVerifyResponseDTO.setOversoldAmount(orderPayinfoDTO.getPayAmount());
        overSendOutVerifyResponseDTO.setPrice(overSendOutVerifyDTO.getGoodsPrice());
        //没有超发 或者超发自动补款成功
        return overSendOutVerifyResponseDTO;
    }

    /**
     * 船运超发验证 : 实提量 * 最新的价格 - 订单多余的金额(支付金额 - 执行金额) = 超发金额
     * 超发金额 - 待支付的补款金额 = 要生成的补款单的金额
     */
    private boolean updateSupplementPaymentDO(SupplementPaymentDO supplementPaymentDO, OrderDTO order, OrderItemDTO orderItem, OverSendOutVerifyDTO overSendOutVerifyDTO){
        //标识
        boolean isConcrete = CsStringUtils.isNotBlank(order.getOrderInfoExtDTO().getSigner());
        //运单结算标识(非都按运单实时单价结算)
        boolean waybillClearingFlag = !isConcrete;
        String waybillNum = overSendOutVerifyDTO.getWaybillNum().split("#")[0];
        String orderId = order.getOrderId();
        String takeCode = overSendOutVerifyDTO.getTakeCode();
        BigDecimal logisticsPrice = overSendOutVerifyDTO.getLogisticsPrice();
        BigDecimal goodsPrice = waybillClearingFlag ? (overSendOutVerifyDTO.getGoodsPrice() != null
                ? overSendOutVerifyDTO.getGoodsPrice() : getContractNewestPrice(order, orderItem, overSendOutVerifyDTO))
                : orderItem.getActualUnitPrice().multiply(orderItem.getUnconvertRate());
        overSendOutVerifyDTO.setGoodsPrice(goodsPrice);
        //润管砂浆
        BigDecimal lubricityPrice = ArithUtils.add(order.getOrderInfoExtDTO().getLubricityPrice(), orderItem.getAdditemPrice());
        log.info("获取商品价格 orderId:{},takeCode:{},waybillNum:{},transportType:{},goodsPrice:{},logisticsPrice:{},lubricityPrice",
                orderId,takeCode,waybillNum,order.getOrderInfoExtDTO().getTransportType(),goodsPrice,logisticsPrice,lubricityPrice);

        PaymentAmountQueryDO paymentAmountQueryDO = new PaymentAmountQueryDO();
        paymentAmountQueryDO.setOrderId(order.getOrderId());
        paymentAmountQueryDO.setPayType(Lists.newArrayList(PayTypeEnum.SUPPLEMENT.getCode(),PayTypeEnum.SINGLE.getCode(),PayTypeEnum.GROUP.getCode()));
        paymentAmountQueryDO.setPayStatusList(Lists.newArrayList(PayStatusEnum.COMPLETED.code()));
        PaymentAmountDO payAmount = orderPaymentService.computeOrderPaymentAmount(paymentAmountQueryDO);
        log.info("订单已支付金额信息 orderId:{},takeCode:{},payAmount:{}",orderId,takeCode,payAmount);

        BigDecimal realTimeAmount = order.getRealtimeOrderAmount();
        BigDecimal realTimeResourceAmount = order.getRealtimeResourceAmount();
        BigDecimal realTimeLogisticAmount = order.getRealtimeLogisticAmount();
        BigDecimal realTimeOtherAmount = order.getRealtimeOthersAmount();
        log.info("订单已执行金额信息 orderId:{},takeCode:{},realTimeAmount:{},realTimeResourceAmount:{},realTimeLogisticAmount:{},realTimeOtherAmount:{}",
                orderId,takeCode,realTimeAmount,realTimeResourceAmount,realTimeLogisticAmount,realTimeOtherAmount);

        //订单剩余金额 = 订单已支付金额 - 订单已执行金额
        BigDecimal remainAmount = ArithUtils.subtract(false, payAmount.getPaymentTotalAmount(),realTimeAmount);
        BigDecimal remainResourceAmount = ArithUtils.subtract(false, payAmount.getPaymentGoodsAmount(),realTimeResourceAmount);
        BigDecimal remainLogisticsAmount = ArithUtils.subtract(false, payAmount.getPaymentLogisticsAmount(),realTimeLogisticAmount);
        BigDecimal remainOtherAmount = ArithUtils.subtract(false, payAmount.getPaymentOtherAmount(),realTimeOtherAmount);
        log.info("订单剩余金额 orderId:{},takeCode:{},remainAmount:{},remainResourceAmount:{},remainLogisticsAmount:{},remainOtherAmount:{}",
                orderId,takeCode,remainAmount,remainResourceAmount,remainLogisticsAmount,remainOtherAmount);

        BigDecimal goodsQuantity = overSendOutVerifyDTO.getActualQuantity();
        BigDecimal lubricityQuantity = null;
        if (order.getOrderInfoExtDTO().getLubricityQuantity() != null) {
            //如果有润管砂浆
            //实际提货量 * 最新商品价格 = 运单商品金额
            //润管砂浆未签收数量
            lubricityQuantity = ArithUtils.subtract(true, order.getOrderInfoExtDTO().getLubricityQuantity(), order.getOrderInfoExtDTO().getLubricitySignQuantity());
            //如果润管砂浆未签收量大于验证量，则认为当前这次出站的商品都是润管砂浆
            //当前运单中润管砂浆的量
            lubricityQuantity = lubricityQuantity.compareTo(overSendOutVerifyDTO.getActualQuantity()) > 0 ? overSendOutVerifyDTO.getActualQuantity() : lubricityQuantity;
            //当前运单中的量
            goodsQuantity = ArithUtils.subtract(true, overSendOutVerifyDTO.getActualQuantity(), lubricityQuantity);
            log.info("区分润管砂浆与商品数量 orderId:{},takeCode:{},goodsQuantity:{},lubricityQuantity:{},lubricitySignQuantity:{}", orderId, takeCode, goodsQuantity, lubricityQuantity, order.getOrderInfoExtDTO().getLubricitySignQuantity());
        } else {
            log.info("没有区分润管砂浆与商品数量 orderId:{},takeCode:{},goodsQuantity:{},lubricityQuantity is null", orderId, takeCode, goodsQuantity);
        }
        //商品数据
        BigDecimal waybillResourceAmount = nullAsZero(ArithUtils.multiply(goodsQuantity, goodsPrice));
        BigDecimal wayBillLogisticsAmount = nullAsZero(overSendOutVerifyDTO.getLogisticsAmount());
        BigDecimal waybillLubricityAmount = nullAsZero(ArithUtils.multiply(lubricityQuantity, lubricityPrice));
        BigDecimal wayBillAmount = ArithUtils.add(waybillResourceAmount,wayBillLogisticsAmount,waybillLubricityAmount);
        log.info("运单金额 orderId:{},takeCode:{},wayBillAmount:{},waybillResourceAmount:{},wayBillLogisticsAmount:{},waybillLubricityAmount:{}",
                orderId,takeCode,wayBillAmount,waybillResourceAmount,wayBillLogisticsAmount,waybillLubricityAmount);

        // 超发金额 = 订单剩余金额 - 运单金额
        BigDecimal overResourceAmount = ArithUtils.subtract(false,waybillResourceAmount, remainResourceAmount);
        BigDecimal overLogisticsAmount = ArithUtils.subtract(false,wayBillLogisticsAmount, remainLogisticsAmount);
        BigDecimal overOtherAmount = ArithUtils.subtract(false,waybillLubricityAmount, remainOtherAmount);
        BigDecimal overAmount = ArithUtils.subtract(false,wayBillAmount, remainAmount);

        boolean resourceOverFlag = overResourceAmount.compareTo(BigDecimal.ZERO) > 0;
        boolean logisticsOverFlag = overLogisticsAmount.compareTo(BigDecimal.ZERO) > 0;
        boolean otherOverFlag = overOtherAmount.compareTo(BigDecimal.ZERO) > 0;
        boolean totalOverFlag = overAmount.compareTo(BigDecimal.ZERO) > 0;
        //是否超发 true 表示已经超发 船运只看总金额
        log.info("运单超发金额 orderId:{},takeCode:{},waybillNum:{},overAmount:{},overResourceAmount:{},overLogisticsAmount:{},overFlag:{},isWaterTransport:{},isConcrete:{},totalOverFlag:{},resourceOverFlag:{},logisticsOverFlag:{},otherOverFlag:{}",
                orderId, takeCode, waybillNum, overAmount, overResourceAmount, overLogisticsAmount, totalOverFlag, waybillClearingFlag, isConcrete, totalOverFlag, resourceOverFlag, logisticsOverFlag, otherOverFlag);

        supplementPaymentDO.setOverResourceAmount(overResourceAmount);
        supplementPaymentDO.setOverLogisticsAmount(overLogisticsAmount);
        supplementPaymentDO.setOverOthersAmount(BigDecimal.ZERO);
        supplementPaymentDOSetValue(supplementPaymentDO, waybillClearingFlag, totalOverFlag, overResourceAmount, overLogisticsAmount, orderId, takeCode, overAmount);
        //支付单说明
        supplementPaymentDO.setOrderPayInfoInstructionsDTOList(Lists.newArrayList());
        //支付单说明 如果有润管砂浆
        if (waybillLubricityAmount.compareTo(BigDecimal.ZERO) > 0) {
            OrderPayInfoInstructionsDTO desc = new OrderPayInfoInstructionsDTO();
            desc.setGoodsId(orderItem.getGoodsId());
            desc.setGoodsName("润管砂浆");
            desc.setUnits(orderItem.getMeasureUnits());
            //计划量 = 实提量
            desc.setGoodsQuantity(lubricityQuantity);
            //最新商品单价
            desc.setGoodsPrice(lubricityPrice);
            //商品费用
            desc.setGoodsAmount(waybillLubricityAmount);
            desc.setLogisticsPrice(overSendOutVerifyDTO.getLogisticsPrice());
            desc.setLogisticsAmount(wayBillLogisticsAmount);
            desc.setTradeAmount(waybillLubricityAmount);
            desc.setTradePrice(ArithUtils.add(goodsPrice, logisticsPrice));

            //补款前总支付金额 当前订单总支付金额
            desc.setPayTotalAmount(payAmount.getPaymentTotalAmount());
            //补款前总成交金额  当前订单总执行金额
            desc.setRealtimeOrderAmount(order.getRealtimeOrderAmount());
            //运单实提量
            desc.setActualQuantity(lubricityQuantity);
            //运单总金额
            desc.setActualAmount(waybillLubricityAmount);
            if (CsStringUtils.isNotBlank(overSendOutVerifyDTO.getWaybillNum())) {
                //运单号
                desc.setWaybillNum(waybillNum);
            }
            OrderPayInfoInstructionsDTO desc2 = new OrderPayInfoInstructionsDTO();
            desc2.setGoodsId(orderItem.getGoodsId());
            desc2.setGoodsName(orderItem.getGoodsName());
            desc2.setUnits(orderItem.getMeasureUnits());
            //计划量 = 实提量
            desc2.setGoodsQuantity(goodsQuantity);
            //最新商品单价
            desc2.setGoodsPrice(goodsPrice);
            //商品费用
            desc2.setGoodsAmount(waybillResourceAmount);
            desc2.setLogisticsPrice(overSendOutVerifyDTO.getLogisticsPrice());
            desc2.setLogisticsAmount(wayBillLogisticsAmount);
            //运单金额 商品费用 + 物流费用
            desc2.setTradeAmount(ArithUtils.add(waybillResourceAmount, wayBillLogisticsAmount));
            desc2.setTradePrice(ArithUtils.add(goodsPrice, logisticsPrice));
            //补款前总支付金额 当前订单总支付金额
            desc2.setPayTotalAmount(payAmount.getPaymentTotalAmount());
            //补款前总成交金额  当前订单总执行金额
            desc2.setRealtimeOrderAmount(order.getRealtimeOrderAmount());
            //运单实提量
            desc2.setActualQuantity(overSendOutVerifyDTO.getActualQuantity());
            //运单金额 商品费用 + 物流费用
            desc2.setActualAmount(ArithUtils.add(waybillResourceAmount, wayBillLogisticsAmount));
            if (CsStringUtils.isNotBlank(overSendOutVerifyDTO.getWaybillNum())) {
                //运单号
                desc2.setWaybillNum(waybillNum);
            }
            supplementPaymentDO.getOrderPayInfoInstructionsDTOList().add(desc2);
            //润管砂浆方后面
            supplementPaymentDO.getOrderPayInfoInstructionsDTOList().add(desc);
        } else {
            OrderPayInfoInstructionsDTO desc = getOrderPayInfoInstructionsDTO(order, orderItem, overSendOutVerifyDTO, goodsPrice, waybillResourceAmount, wayBillLogisticsAmount, wayBillAmount, logisticsPrice, payAmount, waybillNum);
            supplementPaymentDO.getOrderPayInfoInstructionsDTOList().add(desc);
        }
        return totalOverFlag;
    }

    @NotNull
    private static OrderPayInfoInstructionsDTO getOrderPayInfoInstructionsDTO(OrderDTO order, OrderItemDTO orderItem, OverSendOutVerifyDTO overSendOutVerifyDTO, BigDecimal goodsPrice, BigDecimal waybillResourceAmount, BigDecimal wayBillLogisticsAmount, BigDecimal wayBillAmount, BigDecimal logisticsPrice, PaymentAmountDO payAmount, String waybillNum) {
        OrderPayInfoInstructionsDTO desc = new OrderPayInfoInstructionsDTO();
        desc.setGoodsId(orderItem.getGoodsId());
        desc.setGoodsName(orderItem.getGoodsName());
        desc.setUnits(orderItem.getMeasureUnits());
        //计划量 = 实提量
        desc.setGoodsQuantity(overSendOutVerifyDTO.getQuantity());
            //最新商品单价
            desc.setGoodsPrice(goodsPrice);
            //商品费用
            desc.setGoodsAmount(waybillResourceAmount);
        desc.setLogisticsPrice(overSendOutVerifyDTO.getLogisticsPrice());
        desc.setLogisticsAmount(wayBillLogisticsAmount);
        desc.setTradeAmount(wayBillAmount);
        desc.setTradePrice(ArithUtils.add(goodsPrice, logisticsPrice));

        desc.setPayTotalAmount(payAmount.getPaymentTotalAmount());//补款前总支付金额 当前订单总支付金额
        desc.setRealtimeOrderAmount(order.getRealtimeOrderAmount());//补款前总成交金额  当前订单总执行金额
        desc.setActualQuantity(overSendOutVerifyDTO.getActualQuantity());//运单实提量
        desc.setActualAmount(wayBillAmount);//运单总金额
        if (CsStringUtils.isNotBlank(overSendOutVerifyDTO.getWaybillNum())) {
            desc.setWaybillNum(waybillNum);//运单号
        }
        return desc;
    }

    private static void supplementPaymentDOSetValue(SupplementPaymentDO supplementPaymentDO, boolean waybillClearingFlag, boolean totalOverFlag, BigDecimal overResourceAmount, BigDecimal overLogisticsAmount, String orderId, String takeCode, BigDecimal overAmount) {
        if (waybillClearingFlag && totalOverFlag) {
            //均衡超发金额 物流 商品费相互抵消
            if (overResourceAmount.compareTo(BigDecimal.ZERO) > 0  &&  overLogisticsAmount.compareTo(BigDecimal.ZERO) < 0) {
                overResourceAmount = ArithUtils.add(overResourceAmount, overLogisticsAmount);
                overLogisticsAmount = BigDecimal.ZERO;
            } else if (overResourceAmount.compareTo(BigDecimal.ZERO) < 0  &&  overLogisticsAmount.compareTo(BigDecimal.ZERO) > 0) {
                overLogisticsAmount =  ArithUtils.add(overLogisticsAmount, overResourceAmount);
                overResourceAmount = BigDecimal.ZERO;
            }
            supplementPaymentDO.setOverResourceAmount(overResourceAmount);
            supplementPaymentDO.setOverLogisticsAmount(overLogisticsAmount);
            log.info("超发金额 船运物流商品费相互抵消 orderId:{},takeCode:{} overAmount:{},overResourceAmount:{},overLogisticsAmount:{},canOut:true",
                    orderId, takeCode, overAmount, overResourceAmount, overLogisticsAmount);
        }
    }

    /**
     * 为空或小于0时返回0,否则返回原值
     */
    public static BigDecimal nullAsZero(BigDecimal b1){
        return b1 == null || b1.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : b1;
    }

    /**
     * 超发参数验证
     * @param overSendOutVerifyDTO 超发验证参数对象
     */
    private void overSendOutParamValidate(OverSendOutVerifyDTO overSendOutVerifyDTO) {
        if (CsStringUtils.isEmpty(overSendOutVerifyDTO.getTakeCode())) {
            throw new BizException(BasicCode.INVALID_PARAM, "发货单号");
        }
        if (CsStringUtils.isEmpty(overSendOutVerifyDTO.getWaybillNum())) {
            throw new BizException(BasicCode.INVALID_PARAM, "运单号");
        }
        if (CsStringUtils.isEmpty(overSendOutVerifyDTO.getOrderItemId())) {
            throw new BizException(BasicCode.INVALID_PARAM, "订单明细ID");
        }
        if (overSendOutVerifyDTO.getQuantity() == null) {
            throw new BizException(BasicCode.INVALID_PARAM, "运单计划提货量");
        }
        if (overSendOutVerifyDTO.getActualQuantity() == null) {
            throw new BizException(BasicCode.INVALID_PARAM, "运单实际出厂量");
        }
        if (overSendOutVerifyDTO.getLogisticsAmount() == null) {
            throw new BizException(BasicCode.INVALID_PARAM, "运单物流费");
        }
        if (CsStringUtils.isEmpty(overSendOutVerifyDTO.getOperator())) {
            throw new BizException(BasicCode.INVALID_PARAM, "操作人");
        }
    }

    @Transactional(isolation = Isolation.READ_COMMITTED, rollbackFor = Exception.class)
    @Override
    public ItemResult<OverSendOutVerifyResponseDTO> overSendOutVerify(OverSendOutVerifyDTO overSendOutVerifyDTO) {
        log.info("物流通知交易 超发验证 overSendOutVerify:" + JSON.toJSONString(overSendOutVerifyDTO));
        //参数验证
        overSendOutParamValidate(overSendOutVerifyDTO);
        //获取发货单信息
        TakeInfoDTO takeInfo = takeInfoService.getTakeInfoByCode(overSendOutVerifyDTO.getTakeCode());
        if (takeInfo == null) {
            throw new BizException(BasicCode.DATA_NOT_EXIST, "发货单:" + overSendOutVerifyDTO.getTakeCode());
        }
        String identifier = "";
        String key = "overSendOutVerify:"+takeInfo.getOrderId();
        try {
            identifier = redisLockService.lock(key);
            //获取订单明细信息
            OrderDTO order = orderQueryService.getOrderInfo(takeInfo.getOrderId(), false);
            if (order == null) {
                throw new BizException(BasicCode.DATA_NOT_EXIST, "订单:" + takeInfo.getOrderId());
            }
            if (CollectionUtils.isEmpty(order.getOrderItems())) {
                throw new BizException(BasicCode.DATA_NOT_EXIST, "订单明细不存在");
            }
            OverSendOutVerifyResponseDTO overSendOutVerifyResponseDTO = checkoutOverSendOut(order, overSendOutVerifyDTO);
            if(BooleanUtils.isTrue(overSendOutVerifyDTO.getReturnPayInfoWay())){
                overSendOutVerifyResponseDTO.setPayinfoWay(orderPaymentService.getPayInfoWayByTakeCode(overSendOutVerifyDTO.getTakeCode()));
            }
            log.info("overSendOutVerify return:{}",overSendOutVerifyResponseDTO);
            return new ItemResult<>(overSendOutVerifyResponseDTO);
        } catch (BizException be) {
            log.error("物流超发验证异常" + be.getMessage(), be);
            throw be;
        } catch (Exception e) {
            log.error("物流超发验证异常" + e.getMessage(), e);
            throw new BizException(BasicCode.UNDEFINED_ERROR, "物流超发验证异常");
        } finally {
            if (CsStringUtils.isNotEmpty(key) && CsStringUtils.isNotEmpty(identifier)) {
                redisLockService.unlock(key, identifier);
            }
        }
    }

    private BigDecimal getContractNewestPrice(OrderDTO order, OrderItemDTO orderItem,OverSendOutVerifyDTO overSendOutVerifyDTO){
        //如果不是合同单
        if (CsStringUtils.isBlank(order.getDealsId())) {
            return orderItem.getActualUnitPrice().multiply(orderItem.getUnconvertRate());
        }
        if (order.getOrderInfoExtDTO() == null) {
            order.setOrderInfoExtDTO(orderInfoExtBiz.findByOrderId(order.getOrderId()));
        }
        List<TrContractGoodsDTO> contractGoodsDTOList = contractService.queryContractGoodsNewestVersion(order.getDealsId(), order.getOrderInfoExtDTO().getTransportType());
        if (CollectionUtils.isEmpty(contractGoodsDTOList)) {
            log.error("======>合同船运商品没找到 orderId:{},contractId:{}",order.getOrderId(), order.getDealsId());
            //2021.7.23 经方亮、树平、一起讨论决定 合同最新商品价格没找到，则保持下单时的价格不变
            return orderItem.getActualUnitPrice().multiply(orderItem.getUnconvertRate());
        }
        TrContractGoodsDTO contractGoodsDTO = contractGoodsDTOList.stream().filter(item -> CsStringUtils.equals(item.getContractGoodsId(), orderItem.getResourceId())).findFirst().orElse(null);
        if (contractGoodsDTO == null || contractGoodsDTO.getOutFactoryPrice() == null) {
            log.info("合同最新出厂价 根据资源id未找到价格 ");
            //根据 运输类型 + 商品Id + 销售区域 + 仓库Id
            String key1 = order.getOrderInfoExtDTO().getTransportType()+ "-" + orderItem.getGoodsId() +"-"+order.getSaleRegionPath() + "-" + orderItem.getStoreId();
            List<String> key2List = Lists.newArrayList();
            contractGoodsDTO = getContractGoodsDTO(contractGoodsDTOList, key2List, key1, contractGoodsDTO);
            BigDecimal orderItem1 = getOrderItem1(order, orderItem, overSendOutVerifyDTO, contractGoodsDTO, contractGoodsDTOList, key1, key2List);
            return orderItem1;
        }

        log.info("最新合同船运商品价格 order:{},contract:{}", order, contractGoodsDTO);
        //物流单价
        BigDecimal logisticsPrice = overSendOutVerifyDTO.getLogisticsPrice();
        //合同最新单价 买家配送、平台配送 商品价格等于合同出厂价； 卖家配送商品价格 = 合同到位价 - 物流价格
        BigDecimal goodsPrice = BigDecimal.ZERO;
        goodsPrice = getGoodsPrice(order, goodsPrice, contractGoodsDTO, logisticsPrice);
        return goodsPrice;
    }

    private static BigDecimal getGoodsPrice(OrderDTO order, BigDecimal goodsPrice, TrContractGoodsDTO contractGoodsDTO, BigDecimal logisticsPrice) {
        goodsPrice = getGoodsPriceV2(order, goodsPrice, contractGoodsDTO);
        if(DeliveryWayEnum.SELLER_DELIVERY.getCode().equals(order.getDeliverWay()) ) {
            if (contractGoodsDTO.getShipPrice() == null || BigDecimal.ZERO.compareTo(contractGoodsDTO.getShipPrice()) == 0) {
                if (CsStringUtils.equals(TransportToolTypeEnum.WATER_TRANSPORT.getCode(), order.getOrderInfoExtDTO().getTransportType())) {
                    throw new BizException(BasicCode.CUSTOM_ERROR, "合同未配置出厂价不支持卖家配送");
                } else {
                    goodsPrice = contractGoodsDTO.getOutFactoryPrice();
                }
            } else {
                goodsPrice = ArithUtils.subtract(contractGoodsDTO.getShipPrice(), logisticsPrice);
            }
            log.info("SELLER_DELIVERY goodsPrice:{} = shipPrice:{} - logisticsPrice:{}", goodsPrice, contractGoodsDTO.getShipPrice(), logisticsPrice);
        }
        return goodsPrice;
    }

    private static BigDecimal getGoodsPriceV2(OrderDTO order, BigDecimal goodsPrice, TrContractGoodsDTO contractGoodsDTO) {
        if(DeliveryWayEnum.BUYER_TAKE.getCode().equals(order.getDeliverWay()) ) {
            goodsPrice = contractGoodsDTO.getOutFactoryPrice();
            log.info("BUYER_TAKE goodsPrice = outFactoryPrice:{}", goodsPrice);
            if(goodsPrice == null || BigDecimal.ZERO.compareTo(goodsPrice) == 0 ){
                throw new BizException(BasicCode.CUSTOM_ERROR,"合同未配置出厂价不支持买家自提");
            }
        }
        if(DeliveryWayEnum.PLATFORM_DELIVERY.getCode().equals(order.getDeliverWay()) ) {
            goodsPrice = contractGoodsDTO.getOutFactoryPrice();
            log.info("PLATFORM_DELIVERY goodsPrice = outFactoryPrice: {}", goodsPrice);
            if(goodsPrice == null || BigDecimal.ZERO.compareTo(goodsPrice) == 0 ){
                throw new BizException(BasicCode.CUSTOM_ERROR,"合同未配置出厂价不支持平台配送");
            }
        }
        return goodsPrice;
    }

    private static TrContractGoodsDTO getContractGoodsDTO(List<TrContractGoodsDTO> contractGoodsDTOList, List<String> key2List, String key1, TrContractGoodsDTO contractGoodsDTO) {
        for (TrContractGoodsDTO item : contractGoodsDTOList) {
            String key2 = (CsStringUtils.isBlank(item.getTransportType()) ? TransportToolTypeEnum.ROAD_TRANSPORT.getCode() : item.getTransportType())
                    + "-" + item.getGoodsId() + "-"+item.getSaleAreaRealCode() + "-" + item.getOutGoodsAddressId();
            key2List.add(key2);
            if (CsStringUtils.equals(key1, key2)) {
                contractGoodsDTO = item;
                break;
            }
        }
        return contractGoodsDTO;
    }

    @Nullable
    private static BigDecimal getOrderItem1(OrderDTO order, OrderItemDTO orderItem, OverSendOutVerifyDTO overSendOutVerifyDTO, TrContractGoodsDTO contractGoodsDTO, List<TrContractGoodsDTO> contractGoodsDTOList, String key1, List<String> key2List) {
        log.error("合同最新出厂价未找到 takeCode: {},contractId: {},goodsId: {},resourceId: {},transportType: {},contractGoodsDTOList: {}",
                overSendOutVerifyDTO.getTakeCode(), order.getDealsId(), orderItem.getGoodsId(), orderItem.getResourceId(), order.getOrderInfoExtDTO().getTransportType(), contractGoodsDTOList);
        log.error("合同最新出厂价未找到 key1: {},key2List:{}", key1, key2List);
        return orderItem.getActualUnitPrice().multiply(orderItem.getUnconvertRate());
    }

    @Override
    public ItemResult<Void> notifyLeaveWarehouse(OrderPayinfo orderPayinfo) {
        log.info("物流通知交易 补款成功通知物流出站 notifyLeaveWarehouse:" + JSON.toJSONString(orderPayinfo));
        OrderDTO order = orderQueryService.getOrderInfo(orderPayinfo.getObjectId());
        //获取仓库信息
        WarehouseDetailsDTO warehouseDetailsDTO = warehouseService.queryWarehouseDetails(order.getOrderItems().get(0).getStoreId());
        if (warehouseDetailsDTO == null) {
            throw new BizException(BasicCode.DATA_NOT_EXIST, "订单仓库：" + order.getOrderItems().get(0).getStoreId());
        }
        Boolean notifyFlag = Boolean.TRUE;
        if (orderErpBiz.hasErpOrder(order.getSellerId(), MemberConfigEnum.ERP_LOGISTICS_FLG.getKeyCode())) {
            //ERP卖家且非中心仓提货不需要通知物流出站
            if (!CsStringUtils.equals(WarehouseTypeEnum.CENTRAL_WAREHOUSE.getCode(), warehouseDetailsDTO.getType())) {
                notifyFlag = Boolean.FALSE;
            }
        }
        if (notifyFlag) {
            //获取支付单关联的超发运单信息
            OversoldQueryDTO oversoldQueryDTO = new OversoldQueryDTO();
            oversoldQueryDTO.setPayInfoId(orderPayinfo.getPayinfoId());
            List<OversoldDTO> overSoldList = orderOverSellBiz.queryOversold(oversoldQueryDTO);
            if (CollectionUtils.isEmpty(overSoldList)) {
                return new ItemResult<>(null);
            }
            List<String> waybillNumList = overSoldList.stream().map(OversoldDTO::getLogisticsId).toList();
            NotifyLeaveWarehouseDTO notifyLeaveWarehouseDTO = new NotifyLeaveWarehouseDTO();
            notifyLeaveWarehouseDTO.setWaybillNumList(waybillNumList);
            notifyLeaveWarehouseDTO.setCentralWarehouseFlag(CsStringUtils.equals(WarehouseTypeEnum.CENTRAL_WAREHOUSE.getCode(),
                    warehouseDetailsDTO.getType()) ? 1 : 0);
            try {
                //补款完成,异步通知物流出站
                messageService.sendMQ(notifyLeaveWarehouseDTO, "com.ecommerce.order.supplement.complete.notify");
            } catch (Exception e) {
                log.error("通知物流出站异常：" + JSON.toJSONString(notifyLeaveWarehouseDTO));
            }
        } else {
            orderErpBiz.notifyErpIncreaseCreditAmount(orderPayinfo.getPayinfoId());
        }

        return new ItemResult<>(null);
    }

    @Override
    @Transactional
    public void doLogisticRefund(ShipBillRefundDTO shipBillRefundDTO)
    {
        log.info("doLogisticRefund:start:运单退货: {}", JSON.toJSONString(shipBillRefundDTO));


        String lock = null;
        String lockKey = "DoLogisticRefundLock" + MD5.convertMD5(JSON.toJSONString(shipBillRefundDTO));

        try
        {
            lock = redisLockService.lock(lockKey);

            String cacheKey = "DoLogisticRefundCache" + MD5.convertMD5(JSON.toJSONString(shipBillRefundDTO));
            String cacheData = redisService.get(cacheKey, String.class);
            if (CsStringUtils.isNotBlank(cacheData))
            {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "重复提交");
            }

            // 订单商品信息
            OrderItem orderItem = orderItemBiz.get(shipBillRefundDTO.getOrderItemId());
            if(orderItem == null)
            {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "订单商品不存在");
            }

            // 查询订单信息
            OrderInfo orderInfo = orderInfoBiz.get(orderItem.getOrderId());
            if(orderInfo == null)
            {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "未查询到相关的订单信息");
            }

            if (!CsStringUtils.isEmpty(orderInfo.getProxyOrderType()) && !CsStringUtils.equals(orderInfo.getProxyOrderType(), ProxyOrderTypeEnum.PRIMARY.getCode()))
            {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "二级订单不能进行退货");
            }

            if(ProxyOrderTypeEnum.PRIMARY.getCode().equals(orderInfo.getProxyOrderType()) && shipBillRefundDTO.getSecondRefundDTO() == null)
            {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "背靠背运单需要二级运单信息");
            }

            doLogisticRefundHandle(orderInfo, orderItem, shipBillRefundDTO);

            // 该订单是一个背靠背订单
            if(ProxyOrderTypeEnum.PRIMARY.getCode().equals(orderInfo.getProxyOrderType()))
            {
                ShipBillRefundDTO shipBillRefundDTO2 = shipBillRefundDTO.getSecondRefundDTO();
                shipBillRefundDTO2.setWaybillNum(shipBillRefundDTO.getWaybillNum());
                shipBillRefundDTO2.setRefundQuantity(shipBillRefundDTO.getRefundQuantity());
                shipBillRefundDTO2.setRefundReason(shipBillRefundDTO.getRefundReason());

                // 查询订单信息
                OrderInfo orderInfo2 = orderProxyMapBiz.querySecondaryOrderInfoByPrimaryOrderId(orderInfo.getOrderId());

                // 订单商品信息
                OrderItem orderItem2 = orderItemBiz.get(shipBillRefundDTO2.getOrderItemId());

                doLogisticRefundHandle(orderInfo2, orderItem2, shipBillRefundDTO2);
            }

            // 尝试触发订单自动完成
            log.info("doLogisticRefund:尝试触发订单自动完成: {},{}", orderInfo.getOrderCode(), orderInfo.getOrderStatus());
            orderCommonService.triggerOrderAutoEnd(orderInfo.getOrderId());

            log.info("doLogisticRefund:end:运单退货: {}", JSON.toJSONString(shipBillRefundDTO));

            // 保存到redis中
            redisService.set(cacheKey, JSON.toJSONString(shipBillRefundDTO));
            redisService.expire(cacheKey, 86400);
        }
        catch(BizException be)
        {
            throw be;
        }
        catch(Exception e)
        {
            throw new BizException(BasicCode.UNDEFINED_ERROR, e.getMessage());
        }
        finally
        {
            if (CsStringUtils.isNotBlank(lock))
            {
                redisLockService.unlock(lockKey, lock);
            }
        }
    }

    private void doLogisticRefundHandle(OrderInfo orderInfo, OrderItem orderItem, ShipBillRefundDTO shipBillRefundDTO)
    {
        // 检查发货单是否可以更新为已完成
        TakeInfoDTO takeInfo = takeInfoService.getTakeInfoByCode(shipBillRefundDTO.getTakeCode());
        if(takeInfo == null)
        {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "发货单不存在");
        }

        TakeItem takeItem = takeItemBiz.getTakeItemInfoByTakeIdAndOrderItemId(takeInfo.getTakeId(), orderItem.getOrderItemId());
        if(takeItem == null)
        {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "发货商品不存在");
        }



        // 更新订单Item的退货数量
        log.info("doLogisticRefund:更新订单Item的退货数量: {},{}", orderItem.getOrderItemId(), shipBillRefundDTO.getRefundQuantity());
        OrderItem upOrderItem = new OrderItem();
        upOrderItem.setOrderItemId(orderItem.getOrderItemId());
        upOrderItem.setItemRefundQuantity(shipBillRefundDTO.getRefundQuantity());
        upOrderItem.setItemRefundAmount(ArithUtils.add(shipBillRefundDTO.getRefundGoodsAmount(), shipBillRefundDTO.getRefundLogisticsAmount()));
        upOrderItem.setItemRefundLogisticAmount(shipBillRefundDTO.getRefundLogisticsAmount());
        orderItemBiz.incrOrderItemQuantity(upOrderItem);



        // 更新发货单上的退货数量
        log.info("doLogisticRefund:更新发货Item上的退货数量: {},{}", takeItem.getTakeItemId(), shipBillRefundDTO.getRefundQuantity());
        TakeItem upTakeItem = new TakeItem();
        upTakeItem.setTakeItemId(takeItem.getTakeItemId());
        upTakeItem.setRefundQuantity(shipBillRefundDTO.getRefundQuantity());
        takeItemBiz.incrTakeItemQuantity(upTakeItem);



        // 订单已是最终态
        log.info("doLogisticRefund:对订单进行退款处理: {},{}", orderInfo.getOrderCode(), orderInfo.getOrderStatus());
        Set<String> finalStatus = Sets.newHashSet(OrderStatusEnum.COMPLETED.getCode(), OrderStatusEnum.CLOSED.getCode());
        log.info("doLogisticRefund:对订单进行退款处理: {},{},{}", orderInfo.getOrderCode(), orderInfo.getOrderStatus(), JSON.toJSONString(finalStatus));
        if(finalStatus.contains(orderInfo.getOrderStatus()))
        {
            log.info("doLogisticRefund:订单已是最终态,对订单进行退款处理: {},{}", orderInfo.getOrderCode(), orderInfo.getOrderStatus());
            orderCommonService.doOrderRefund(orderInfo, shipBillRefundDTO.getRefundGoodsAmount(), shipBillRefundDTO.getRefundLogisticsAmount(), BigDecimal.ZERO, shipBillRefundDTO.getRefundReason());
        }
    }
}
