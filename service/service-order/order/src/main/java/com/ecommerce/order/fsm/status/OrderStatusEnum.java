package com.ecommerce.order.fsm.status;

/**
 * 下午2:20 20/5/21
 *
 * <AUTHOR>
 */
public enum  OrderStatusEnum {

    CREATE("create", "订单创建"),

    CONFIRMING("confirming", "待卖家确认"),

    WAIT_PAYMENT("wait_payment", "待支付"),

    CANCEL("cancel", "已取消"),

    IN_PAYMENT("in_payment", "支付确认中"),

    IN_ERP_CREATE_CONFIRM("in_erp_confirm_create", "ERP创建中"),

    WAIT_DELIVERED("wait_delivered", "待发货"),

    IN_DELIVERY("in_delivery", "发货中"),

    ERP_COMPLETING("erp_completing", "ERP完成中"),

    COMPLETED("completed", "已完成"),

    ERP_CLOSING("erp_closing", "ERP关闭中"),

    CLOSED("closed", "已关闭"),

    /**
     * 为了满足状态流转的虚拟节点，依赖上一个节点动作
     */
    ORDER_CREATE_JUMP("order_create_jump", "订单创建瞬时状态"),

    ORDER_CONFIRM_JUMP("order_confirm_jump", "订单确认瞬时状态"),

    PAYMENT_SUCCESS_JUMP("payment_success_jump", "支付成功瞬时状态"),

    ORDER_COMPLETE_JUMP("order_complete_jump", "订单完成瞬时状态"),

    ORDER_CLOSE_JUMP("order_close_jump", "订单关闭瞬时状态");

    /** 枚举值 */
    private final String code;

    /** 枚举描述 */
    private final String message;


    /**
     * 构造一个<code>ResourceStatusEnum</code>枚举对象
     *
     * @param code
     * @param message
     */
    OrderStatusEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * @return Returns the code.
     */
    public String getCode() {
        return code;
    }

    /**
     * @return Returns the message.
     */
    public String getMessage() {
        return message;
    }

    /**
     * 通过枚举<code>code</code>获得枚举
     *
     * @param code
     * @return ResourceStatusEnum
     */
    public static OrderStatusEnum getByCode(String code) {
        for (OrderStatusEnum _enum : values()) {
            if (_enum.getCode().equals(code)) {
                return _enum;
            }
        }
        return null;
    }
}
