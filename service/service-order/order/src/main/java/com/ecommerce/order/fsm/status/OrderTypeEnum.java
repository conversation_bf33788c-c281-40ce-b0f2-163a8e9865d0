package com.ecommerce.order.fsm.status;

public enum OrderTypeEnum {

 	CONTRACT("contract", "合同订单", "contractOrderHandlerService"),

    LISTING("self", "挂牌订单", "listingOrderHandlerService"),

    INQUIRY("inquiry", "询价订单", "listingOrderHandlerService");

	/**
     * 编码
     */
    private String code;
    /**
     * 描述
     */
    private String description;

    /**
     * IOrderBiz类型
     */
    private String bizType;
    
    OrderTypeEnum(String code, String description, String bizType) {
        this.code = code;
        this.description = description;
        this.bizType = bizType;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

	public String getBizType() {
		return bizType;
	}
	
	public static OrderTypeEnum getByCode(String code) {
        for (OrderTypeEnum _enum : values()) {
            if (_enum.getCode().equals(code)) {
                return _enum;
            }
        }
        return null;
    }
}
