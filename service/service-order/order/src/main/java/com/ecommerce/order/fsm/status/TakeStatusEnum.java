package com.ecommerce.order.fsm.status;

/**
 * 发货单状态机状态枚举
 */
public enum TakeStatusEnum {

    CREATE("000", "新建"),
    CREATE_JUMP("010", "新建瞬时状态"),//状态机状态流转时，需要根据条件跳转到不同的状态时，先跳转此状态
    PENDING_CONFIRMED_JUMP("99", "待支付"),//TakeChangeEventEnum.CHANGE_TO_NOT_NEED_PAY事件使用
    PENDING_CONFIRMED("100", "待确认"),
    CONFIRMED_JUMP("110", "确认瞬时状态"),//状态机状态流转时，需要根据条件跳转到不同的状态时，先跳转此状态,
    SELLER_CONFIRMED("150", "已确认"),
    WAIT_PAYMENT("160", "待支付"),
    IN_PAYMENT("170", "支付确认中"),
    PAYMENT_SUCCESS_JUMP("180", "支付成功瞬时状态"),
    PENDING_DELIVERY("200", "待发货"),
    DELIVERING("300", "发货中"),
    CLOSED("400", "已关闭"),
    FINISHED("500", "已完成"),
    CANCELED("600", "已取消");

    /** 枚举值 */
    private final String code;

    /** 枚举描述 */
    private final String message;


    /**
     * 构造一个<code>TakeStatusEnum</code>枚举对象
     *
     * @param code
     * @param message
     */
    TakeStatusEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * @return Returns the code.
     */
    public String getCode() {
        return code;
    }

    /**
     * @return Returns the message.
     */
    public String getMessage() {
        return message;
    }

    /**
     * 通过枚举<code>code</code>获得枚举
     *
     * @param code
     * @return ResourceStatusEnum
     */
    public static TakeStatusEnum getByCode(String code) {
        for (TakeStatusEnum _enum : values()) {
            if (_enum.getCode().equals(code)) {
                return _enum;
            }
        }
        return null;
    }
}
