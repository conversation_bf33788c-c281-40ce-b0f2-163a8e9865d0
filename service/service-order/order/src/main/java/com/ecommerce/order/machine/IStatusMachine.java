package com.ecommerce.order.machine;

import com.ecommerce.order.dao.vo.OrderInfo;

import java.util.Map;

/**
 * <AUTHOR>
 * @created 17:43 20/09/2019
 * @description
 */
public interface IStatusMachine {

    /**
     * 前置状态
     * @return
     */
    String startStatus();

    /**
     * 结束状态
     * @return
     */
    String endStatus();

    /**
     * 前置条件
     * @return
     */
    boolean precondition(OrderInfo orderInfo);

    /**
     * 前置条件
     * @return
     */
    boolean precondition(OrderInfo orderInfo, Map<String, Object> param);

    /**
     * 触发状态变更
     * @return
     */
    void driveStatus(OrderInfo orderInfo, String operatorId);

    /**
     * 触发状态变更
     * @return
     */
    void driveStatus(OrderInfo orderInfo, Map<String, Object> param, String operatorId);

    /**
     * 前置条件
     * @return
     */
    void updateStatus(String orderId, String operatorId);
}
