package com.ecommerce.order.machine;

import com.ecommerce.order.api.dto.OrderSubStatusDTO;
import com.ecommerce.order.dao.vo.OrderInfo;

import java.util.Map;


/**
 * <AUTHOR>
 * @created 17:57 20/09/2019
 * @description
 */
public interface ISubStatusMachine {

    /**
     * 子状态类型
     * @return
     */
    String subStatusType();

    /**
     * 前置状态
     * @return
     */
    String startStatus();

    /**
     * 结束状态
     * @return
     */
    String endStatus();

    /**
     * 前置条件
     * @return
     */
    boolean precondition(OrderInfo orderInfo, OrderSubStatusDTO subStatusDTO);

    /**
     * 前置条件
     * @return
     */
    boolean precondition(OrderInfo orderInfo, Map<String, Object> param, OrderSubStatusDTO subStatusDTO);

    /**
     * 触发状态变更
     * @return
     */
    boolean driveStatus(OrderInfo orderInfo, String operatorId);
}
