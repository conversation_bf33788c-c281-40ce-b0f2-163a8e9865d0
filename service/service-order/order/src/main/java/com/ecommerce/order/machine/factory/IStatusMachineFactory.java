package com.ecommerce.order.machine.factory;

import com.ecommerce.order.dao.vo.OrderInfo;

import java.util.Map;

/**
 * <AUTHOR>
 * @created 17:44 20/09/2019
 * @description
 */
public interface IStatusMachineFactory {

    /**
     * 新建订单状态
     */
    void initOrderStatus(OrderInfo orderDTO, String operatorId);

    /**
     * 状态流转(自动)
     * @param orderId
     * @param operatorId
     */
    void driveStatus(String orderId, String operatorId);

    /**
     * 状态流转(指定最终状态)
     * @param orderId
     * @param operator
     */
    void driveStatus(String orderId, String targetStatus, String operator);

    /**
     * 状态流转(自动)
     * @param orderId
     * @param operatorId
     */
    void driveStatus(String orderId, Map<String, Object> param, String operatorId);

    /**
     * 状态流转(指定最终状态)
     * @param orderId
     * @param operator
     */
    void driveStatus(String orderId, String targetStatus, Map<String, Object> param, String operator);

}
