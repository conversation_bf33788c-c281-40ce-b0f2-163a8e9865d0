package com.ecommerce.order.machine.factory;


import java.util.Map;

/**
 * <AUTHOR>
 * @created 11:32 23/09/2019
 * @description
 */
public interface ISubStatusMachineFactory {

    /**
     * 状态流转(自动)
     * @param orderId
     * @param operatorId
     */
    boolean driveStatus(String orderId, String subStatusType, String operatorId);

    /**
     * 状态流转(指定最终状态)
     * @param orderId
     * @param operator
     */
    boolean driveStatus(String orderId, String subStatusType, String targetStatus, String operator);

    /**
     * 状态流转(自动)
     * @param orderId
     * @param operatorId
     */
    boolean driveStatus(String orderId, String subStatusType, Map<String, Object> param, String operatorId);

    /**
     * 状态流转(指定最终状态)
     * @param orderId
     * @param operator
     */
    boolean driveStatus(String orderId, String subStatusType, String targetStatus, Map<String, Object> param, String operator);
}
