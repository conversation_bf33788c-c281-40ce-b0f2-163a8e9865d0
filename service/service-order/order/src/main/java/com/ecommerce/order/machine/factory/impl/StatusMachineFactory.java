package com.ecommerce.order.machine.factory.impl;

import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.goods.api.service.IGoodsService;
import com.ecommerce.order.api.dto.OrderStatusLogDTO;
import com.ecommerce.order.api.enums.OrderStatusEnum;
import com.ecommerce.order.api.enums.OrderSubStatusTypeEnum;
import com.ecommerce.order.api.enums.PayStatusEnum;
import com.ecommerce.order.biz.IOrderStatusLogBiz;
import com.ecommerce.order.biz.IOrderSubStatusBiz;
import com.ecommerce.order.dao.mapper.OrderInfoMapper;
import com.ecommerce.order.dao.vo.OrderInfo;
import com.ecommerce.order.machine.IStatusMachine;
import com.ecommerce.order.machine.factory.IStatusMachineFactory;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @created 17:47 20/09/2019
 * @description
 */
@Slf4j
@Component
public class StatusMachineFactory implements IStatusMachineFactory {

    @Autowired
    private IOrderStatusLogBiz logBiz;

    /**
     * 状态机实例
     */
    @Autowired(required = false)
    private List<IStatusMachine> statusMachineList;

    @Autowired
    protected OrderInfoMapper orderInfoMapper;

    @Autowired
    protected IGoodsService goodsService;

    @Autowired
    private IOrderSubStatusBiz subStatusBiz;

    private static final String LOG_INFO_START = "=== status machine driveStatus start ===";
    private static final String LOG_INFO_END = "=== status machine driveStatus end ===";
    private static final String LOG_INFO_FORMAT = " == machine is: {}";

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void initOrderStatus(OrderInfo orderDTO, String operatorId) {
        //订单状态
        if(orderDTO.getIfNeedsellerconfirm()){
            orderDTO.setOrderStatus(OrderStatusEnum.CONFIRMING.code());
        }else{
            orderDTO.setOrderStatus(OrderStatusEnum.WAIT_PAYMENT.code());
            subStatusBiz.updateSubStatus(orderDTO.getOrderId(),
                    OrderSubStatusTypeEnum.PAY_STATUS.getCode(),
                    PayStatusEnum.WAIT_PAYMENT.code(),
                    PayStatusEnum.WAIT_PAYMENT.message(),
                    operatorId);
        }

        OrderInfo orderInfo = new OrderInfo();
        orderInfo.setOrderId(orderDTO.getOrderId());
        orderInfo.setOrderStatus(orderDTO.getOrderStatus());
        orderInfoMapper.updateByPrimaryKeySelective(orderInfo);

        OrderStatusLogDTO dto = new OrderStatusLogDTO();
        dto.setOrderId(orderInfo.getOrderId());
        dto.setStatusKey("order_status");
        dto.setStartStatus("init");
        dto.setEndStatus(orderInfo.getOrderStatus());
        dto.setMessage("新建订单");
        logBiz.create(dto, operatorId);
    }

    @Override
    public void driveStatus(String orderId, String operatorId) {
        log.info(LOG_INFO_START);
        OrderInfo orderInfo = getOrderInfo(orderId);
        IStatusMachine statusMachine = findStatusMachine(orderInfo);
        log.info(LOG_INFO_FORMAT, statusMachine.getClass().getSimpleName());
        statusMachine.driveStatus(orderInfo, operatorId);
        log.info(LOG_INFO_END);
    }

    @Override
    public void driveStatus(String orderId, String targetStatus, String operator) {
        log.info(LOG_INFO_START);
        OrderInfo orderInfo = getOrderInfo(orderId);
        List<IStatusMachine> machineList = findStatusMachineList(orderInfo);
        IStatusMachine targetMachine = null;
        for (IStatusMachine machine : machineList) {
            if (machine.endStatus().equals(targetStatus) && machine.precondition(orderInfo)) {
                targetMachine = machine;
                break;
            }
        }
        if (targetMachine == null) {
            return;
        }
        log.info(LOG_INFO_FORMAT, targetMachine.getClass().getSimpleName());
        targetMachine.driveStatus(orderInfo, operator);
        log.info(LOG_INFO_END);
    }

    @Override
    public void driveStatus(String orderId, Map<String, Object> param, String operatorId) {
        log.info(LOG_INFO_START);
        OrderInfo orderInfo = getOrderInfo(orderId);
        List<IStatusMachine> collect = statusMachineList.stream()
                .filter(i -> orderInfo.getOrderStatus().equals(i.startStatus())).toList();
        IStatusMachine targetMachine = null;
        for (IStatusMachine statusMachine : collect) {
            if (statusMachine.precondition(orderInfo, param)) {
                targetMachine = statusMachine;
                break;
            }
        }
        if (targetMachine != null) {
            log.info(LOG_INFO_FORMAT, targetMachine.getClass().getSimpleName());
            targetMachine.driveStatus(orderInfo, param, operatorId);
        }
        log.info(LOG_INFO_END);
    }

    @Override
    public void driveStatus(String orderId, String targetStatus, Map<String, Object> param, String operator) {
        log.info(LOG_INFO_START);
        OrderInfo orderInfo = getOrderInfo(orderId);
        List<IStatusMachine> machineList = findStatusMachineList(orderInfo);
        IStatusMachine targetMachine = null;
        for (IStatusMachine machine : machineList) {
            if (machine.endStatus().equals(targetStatus) && machine.precondition(orderInfo, param)) {
                targetMachine = machine;
                break;
            }
        }
        if (targetMachine == null) {
            return;
        }
        log.info(LOG_INFO_FORMAT, targetMachine.getClass().getSimpleName());
        targetMachine.driveStatus(orderInfo, param, operator);
        log.info(LOG_INFO_END);
    }

    private IStatusMachine findStatusMachine(OrderInfo orderInfo) {
        List<IStatusMachine> collect = statusMachineList.stream()
                .filter(i ->
//                        orderInfo.getOrderStatus().equals(i.startStatus()) ||
                                orderInfo.getOrderStatus().equals(i.startStatus())
                )
                .toList();
        for (IStatusMachine statusMachine : collect) {
            if (statusMachine.precondition(orderInfo)) {
                return statusMachine;
            }
        }
        throw new BizException(BasicCode.UNKNOWN_ERROR, "订单状态异常");
    }

    private List<IStatusMachine> findStatusMachineList(OrderInfo orderInfo) {
        List<IStatusMachine> collect = statusMachineList.stream()
                .filter(i ->
                        i.startStatus() == null ||
                        orderInfo.getOrderStatus().equals(i.startStatus()) ||
                        Lists.newArrayList(i.startStatus().split(",")).contains(orderInfo.getOrderStatus())
                )
                .toList();
        if (CollectionUtils.isNotEmpty(collect)) {
            return collect;
        }
        throw new BizException(BasicCode.UNKNOWN_ERROR, "订单状态异常");
    }

    private OrderInfo getOrderInfo(String orderId) {
        OrderInfo orderInfo = orderInfoMapper.selectByPrimaryKey(orderId);
        if (orderInfo == null) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "订单不存在");
        }
        return orderInfo;
    }
}
