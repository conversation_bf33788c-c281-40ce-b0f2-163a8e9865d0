package com.ecommerce.order.machine.factory.impl;

import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.order.api.dto.OrderSubStatusDTO;
import com.ecommerce.order.biz.IOrderSubStatusBiz;
import com.ecommerce.order.dao.mapper.OrderInfoMapper;
import com.ecommerce.order.dao.vo.OrderInfo;
import com.ecommerce.order.machine.ISubStatusMachine;
import com.ecommerce.order.machine.factory.ISubStatusMachineFactory;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @created 11:32 23/09/2019
 * @description
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SubStatusMachineFactory implements ISubStatusMachineFactory {

    /**
     * 状态机实例
     */
    @Autowired(required = false)
    private List<ISubStatusMachine> subStatusMachineList;

    private final OrderInfoMapper orderInfoMapper;

    private final IOrderSubStatusBiz subStatusBiz;

    @Override
    public boolean driveStatus(String orderId, String subStatusType, String operatorId) {
        OrderInfo orderInfo = getOrderInfo(orderId);
        ISubStatusMachine statusMachine = findSubStatusMachine(orderInfo, subStatusType);
        return statusMachine.driveStatus(orderInfo, operatorId);
    }

    @Override
    public boolean driveStatus(String orderId, String subStatusType, String targetStatus, String operator) {
        OrderInfo orderInfo = getOrderInfo(orderId);

        OrderSubStatusDTO subStatus = findSubStatusListByType(orderInfo.getOrderId(), subStatusType);
        List<ISubStatusMachine> machineList = findSubStatusMachineList(subStatusType, subStatus);
        ISubStatusMachine targetMachine = null;
        for (ISubStatusMachine machine : machineList) {
            if (machine.endStatus().equals(targetStatus) && machine.precondition(orderInfo, subStatus)) {
                targetMachine = machine;
                break;
            }
        }
        if (targetMachine == null) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "无法变更该订单状态");
        }
        return targetMachine.driveStatus(orderInfo, operator);
    }

    @Override
    public boolean driveStatus(String orderId, String subStatusType, Map<String, Object> param, String operatorId) {
        OrderInfo orderInfo = getOrderInfo(orderId);
        OrderSubStatusDTO subStatus = findSubStatusListByType(orderInfo.getOrderId(), subStatusType);
        log.info(" curr sub status: {}", subStatus);
        ISubStatusMachine targetMachine = null;
        List<ISubStatusMachine> collect = subStatusMachineList.stream()
                .filter(i ->
                        i.subStatusType().equals(subStatusType) &&
                                (i.startStatus() == null || (subStatus != null && i.startStatus().equals(subStatus.getStatus())))
                )
                .toList();

        List<ISubStatusMachine> list = Lists.newArrayList();
        for (ISubStatusMachine statusMachine : collect) {
            if (statusMachine.precondition(orderInfo, param, subStatus)) {
                list.add(statusMachine);
                if (statusMachine.startStatus() != null && subStatus != null && statusMachine.startStatus().equals(subStatus.getStatus())) {
                    targetMachine = statusMachine;
                }
            }
        }
        if (targetMachine == null) {
            targetMachine = list.get(0);
        }

        if (targetMachine == null) {
            return true;
        }
        log.info(" == sub machine: {}", targetMachine.getClass().getSimpleName());
        return targetMachine.driveStatus(orderInfo, operatorId);
    }

    @Override
    public boolean driveStatus(String orderId, String subStatusType, String targetStatus, Map<String, Object> param, String operator) {
        OrderInfo orderInfo = getOrderInfo(orderId);
        OrderSubStatusDTO subStatus = findSubStatusListByType(orderInfo.getOrderId(), subStatusType);
        ISubStatusMachine targetMachine = null;
        List<ISubStatusMachine> collect = subStatusMachineList.stream()
                .filter(i ->
                        i.subStatusType().equals(subStatusType) &&
                                (subStatus == null || subStatus.getStatus().equals(i.startStatus()))
                )
                .toList();

        for (ISubStatusMachine statusMachine : collect) {
            if (statusMachine.endStatus().equals(targetStatus) && statusMachine.precondition(orderInfo, param, subStatus)) {
                targetMachine = statusMachine;
                break;
            }
        }
        if (targetMachine == null) {
            return true;
        }
        return targetMachine.driveStatus(orderInfo, operator);
    }

    private ISubStatusMachine findSubStatusMachine(OrderInfo orderInfo, String type) {

        OrderSubStatusDTO subStatus = findSubStatusListByType(orderInfo.getOrderId(), type);

        List<ISubStatusMachine> collect = subStatusMachineList.stream()
                .filter(i ->
                        i.subStatusType().equals(type) &&
                        (subStatus == null || subStatus.getStatus().equals(i.startStatus()))
                )
                .toList();

        for (ISubStatusMachine statusMachine : collect) {
            if (statusMachine.precondition(orderInfo, subStatus)) {
                return statusMachine;
            }
        }
        throw new BizException(BasicCode.UNKNOWN_ERROR, "订单状态异常");
    }

    private List<ISubStatusMachine> findSubStatusMachineList(String type, OrderSubStatusDTO subStatus) {
        List<ISubStatusMachine> collect = subStatusMachineList.stream()
                .filter(i ->
                        i.subStatusType().equals(type) &&
                        (subStatus == null || subStatus.getStatus().equals(i.startStatus()))
                )
                .toList();
        if (CollectionUtils.isNotEmpty(collect)) {
            return collect;
        }
        throw new BizException(BasicCode.UNKNOWN_ERROR, "订单状态异常");
    }

    private OrderInfo getOrderInfo(String orderId) {
        OrderInfo orderInfo = orderInfoMapper.selectByPrimaryKey(orderId);
        if (orderInfo == null) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "订单不存在");
        }
        return orderInfo;
    }

    private List<OrderSubStatusDTO> findSubStatusList(String orderId) {
        return subStatusBiz.findByOrderId(orderId);
    }

    private OrderSubStatusDTO findSubStatusListByType(String orderId, String type) {
        return subStatusBiz.findByOrderId(orderId).stream().filter(i -> i.getStatusKey().equals(type)).findAny().orElse(null);
    }
}
