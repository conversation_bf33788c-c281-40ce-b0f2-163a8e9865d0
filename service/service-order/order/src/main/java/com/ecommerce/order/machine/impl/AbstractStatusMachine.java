package com.ecommerce.order.machine.impl;

import com.ecommerce.common.service.common.BaseBiz;
import com.ecommerce.order.api.dto.OrderDTO;
import com.ecommerce.order.api.dto.OrderStatusLogDTO;
import com.ecommerce.order.api.dto.OrderSubStatusDTO;
import com.ecommerce.order.biz.IOrderStatusLogBiz;
import com.ecommerce.order.biz.IOrderSubStatusBiz;
import com.ecommerce.order.biz.impl.BaseOrderSearchBiz;
import com.ecommerce.order.dao.vo.OrderInfo;
import com.ecommerce.order.machine.IStatusMachine;
import com.ecommerce.order.machine.factory.ISubStatusMachineFactory;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @created 11:06 25/09/2019
 * @description
 */
public abstract class AbstractStatusMachine implements IStatusMachine {

    protected static final String ALL_STATUS = null;

    @Autowired
    protected BaseOrderSearchBiz baseOrderSearchBiz;

    @Autowired
    protected IOrderStatusLogBiz logBiz;

    @Autowired
    protected ISubStatusMachineFactory subStatusMachineFactory;

    @Autowired
    protected IOrderSubStatusBiz subStatusBiz;

    protected void updateOrder(OrderInfo orderInfo, String operatorId) {
        BaseBiz.setOperInfo(orderInfo, operatorId, false);
        baseOrderSearchBiz.updateSelective(orderInfo);
    }

    protected OrderDTO getOrderDetail(String orderId) {
        return baseOrderSearchBiz.getOrderDetail(orderId);
    }

    protected String anyStatus(String... status) {
        return String.join(",", status);
    }

    protected void logs(String orderId, String startStatus, String message, String operatorId) {
        OrderStatusLogDTO dto = new OrderStatusLogDTO();
        dto.setOrderId(orderId);
        dto.setStatusKey("order_status");
        dto.setStartStatus(startStatus);
        dto.setEndStatus(endStatus());
        dto.setMessage(message);
        logBiz.create(dto, operatorId);
    }

    protected OrderSubStatusDTO findOrderSubStatus(String orderId, String subType) {
        return subStatusBiz.findByOrderId(orderId).stream().filter(i -> i.getStatusKey().equals(subType)).findAny().orElse(null);
    }

}
