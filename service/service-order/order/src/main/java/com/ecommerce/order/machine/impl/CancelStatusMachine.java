package com.ecommerce.order.machine.impl;

import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.order.api.enums.OrderStatusEnum;
import com.ecommerce.order.dao.vo.OrderInfo;
import com.ecommerce.order.service.IOrderPayinfoService;
import com.ecommerce.order.service.IResourceTCCService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @created 14:04 23/09/2019
 * @description
 */
@Slf4j
@Component
public class CancelStatusMachine extends AbstractStatusMachine {

    @Autowired
    private IResourceTCCService resourceTCCService;
    @Autowired
    private IOrderPayinfoService orderPayinfoService;

    @Override
    public String startStatus() {
        return ALL_STATUS;
    }

    @Override
    public String endStatus() {
        return OrderStatusEnum.CANCEL.getCode();
    }

    @Override
    public boolean precondition(OrderInfo orderInfo) {
        //
        return true;
    }

    @Override
    public boolean precondition(OrderInfo orderInfo, Map<String, Object> param) {
        return true;
    }

    @Override
    public void driveStatus(OrderInfo orderInfo, String operatorId) {
        OrderStatusEnum orderStatus = OrderStatusEnum.getByCode(orderInfo.getOrderStatus());
        if (OrderStatusEnum.COMPLETED.equals(orderStatus)) {
            return;
        }
        if (OrderStatusEnum.CONFIRMING == orderStatus) {//待卖家确认
            //返回已占用的资源数量
            resourceTCCService.returnResourceNum(orderInfo.getOrderId(), operatorId);
            //关闭支付信息
            orderPayinfoService.closedPayTradeBillByOrderId(orderInfo.getOrderId(), operatorId);
        }
        else if (OrderStatusEnum.WAIT_PAYMENT == orderStatus) {//卖家已确认，等待买家支付
            //返回已占用的资源数量
            resourceTCCService.returnResourceNum(orderInfo.getOrderId(), operatorId);
            //关闭支付信息
            orderPayinfoService.closedPayTradeBillByOrderId(orderInfo.getOrderId(), operatorId);
        }
        else if (OrderStatusEnum.IN_PAYMENT == orderStatus) {//支付确认中
            //关闭支付信息
            orderPayinfoService.closedPayTradeBillByOrderId(orderInfo.getOrderId(), operatorId);
        } else {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "该订单无法取消");
        }
        OrderInfo updOrder = new OrderInfo();
        updOrder.setOrderId(orderInfo.getOrderId());
		updOrder.setOrderStatus(OrderStatusEnum.CANCEL.code());
        updateOrder(updOrder, operatorId);
    }

    @Override
    public void driveStatus(OrderInfo orderInfo, Map<String, Object> param, String operatorId) {
        //
    }

    @Override
    public void updateStatus(String orderId, String operatorId) {
        //
    }
}
