package com.ecommerce.order.machine.impl;

import com.ecommerce.order.api.enums.ErpStatusEnum;
import com.ecommerce.order.api.enums.OrderStatusEnum;
import com.ecommerce.order.api.enums.OrderSubStatusTypeEnum;
import com.ecommerce.order.dao.vo.OrderInfo;
import com.ecommerce.order.service.IResourceTCCService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @created 10:54 25/09/2019
 * @description
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CloseStatusMachine extends AbstractStatusMachine {

    private final IResourceTCCService resourceTCCService;

    @Override
    public String startStatus() {
        return OrderStatusEnum.IN_DELIVERY.getCode();
    }

    @Override
    public String endStatus() {
        return OrderStatusEnum.CLOSED.getCode();
    }

    @Override
    public boolean precondition(OrderInfo orderInfo) {
        return false;
    }

    @Override
    public boolean precondition(OrderInfo orderInfo, Map<String, Object> param) {
        Boolean hasErp = (Boolean) param.get("hasErp");
        return hasErp != null;
    }

    @Override
    public void driveStatus(OrderInfo orderInfo, String operatorId) {
        //
    }

    @Override
    public void driveStatus(OrderInfo orderInfo, Map<String, Object> param, String operatorId) {
        Boolean hasErp = (Boolean) param.get("hasErp");
        if (Boolean.TRUE.equals(hasErp)) {
            subStatusBiz.updateSubStatus(orderInfo.getOrderId(),
                    OrderSubStatusTypeEnum.ERP_STATUS.getCode(),
                    ErpStatusEnum.ERP_CLOSING.getCode(),
                    ErpStatusEnum.ERP_CLOSING.getMsg(),
                    operatorId);
        }

        //返回已占用的资源数量
        resourceTCCService.returnResourceNum(orderInfo.getOrderId(), operatorId);

        OrderInfo updOrder = new OrderInfo();
        updOrder.setOrderId(orderInfo.getOrderId());
        updOrder.setOrderStatus(endStatus());
        updateOrder(updOrder, operatorId);

        logs(orderInfo.getOrderId(), startStatus(), "关闭订单", operatorId);
    }

    @Override
    public void updateStatus(String orderId, String operatorId) {
        //
    }
}
