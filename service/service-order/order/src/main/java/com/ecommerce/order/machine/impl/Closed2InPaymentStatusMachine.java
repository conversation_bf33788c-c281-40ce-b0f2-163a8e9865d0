package com.ecommerce.order.machine.impl;

import com.ecommerce.member.api.dto.member.config.MemberConfigEnum;
import com.ecommerce.order.api.dto.OrderSubStatusDTO;
import com.ecommerce.order.api.enums.*;
import com.ecommerce.order.biz.IOrderErpBiz;
import com.ecommerce.order.dao.vo.OrderInfo;
import com.ecommerce.pay.api.v2.enums.ChannelCodeEnum;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @created 15:27 26/09/2019
 * @description
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class Closed2InPaymentStatusMachine extends AbstractStatusMachine {

    private final IOrderErpBiz orderErpBiz;

    @Override
    public String startStatus() {
        return OrderStatusEnum.CLOSED.code();
    }

    @Override
    public String endStatus() {
        return OrderStatusEnum.IN_PAYMENT.code();
    }

    @Override
    public boolean precondition(OrderInfo orderInfo) {
        boolean hasErpOrder = orderErpBiz.hasErpOrder(orderInfo.getSellerId(), MemberConfigEnum.ERP_ORDER_FLG.getKeyCode());
        return !hasErpOrder;
    }

    @Override
    public boolean precondition(OrderInfo orderInfo, Map<String, Object> param) {
        Boolean hasErpOrder = (Boolean) param.get("hasErpOrder");
        if (hasErpOrder == null) {
            hasErpOrder = orderErpBiz.hasErpOrder(orderInfo.getSellerId(), MemberConfigEnum.ERP_ORDER_FLG.getKeyCode());
        }
        if (Boolean.TRUE.equals(hasErpOrder)) {
            return false;
        }
        String channelCode = (String) param.getOrDefault("channelCode", "");
        String payInfoType = (String) param.getOrDefault("payInfoType", "");

        return CsStringUtils.equals(channelCode, ChannelCodeEnum.OFFLINE.getCode()) &&
                CsStringUtils.equals(payInfoType, PayTypeEnum.REFUND.getCode());
    }

    @Override
    public void driveStatus(OrderInfo orderInfo, String operatorId) {
        driveStatus(orderInfo, Maps.newHashMap(), operatorId);
    }

    @Override
    public void driveStatus(OrderInfo orderInfo, Map<String, Object> param, String operatorId) {
        OrderSubStatusDTO orderSubStatus = findOrderSubStatus(orderInfo.getOrderId(), OrderSubStatusTypeEnum.PAY_STATUS.getCode());
        if (orderSubStatus == null || !PayStatusEnum.COMPLETED.code().equals(orderSubStatus.getStatus())) {
            subStatusMachineFactory.driveStatus(orderInfo.getOrderId(), OrderSubStatusTypeEnum.PAY_STATUS.getCode(), param, operatorId);
        }
        //支付子状态流转完毕
        orderInfo.setOrderStatus(endStatus());
        String payInfoType = (String) param.getOrDefault("payInfoType", "");
        String content = "";
        if (PayTypeEnum.SUPPLEMENT.code().equals(payInfoType)) {
            orderInfo.setPaymentConfirmType(PaymentConfirmTypeEnum.UNDERLINE_SUPPLEMENT.getCode());
            content = "订单买家补款完成，待卖家确认";
        } else if (PayTypeEnum.REFUND.code().equals(payInfoType)) {
            orderInfo.setPaymentConfirmType(PaymentConfirmTypeEnum.UNDERLINE_REFUND.getCode());
            content = "订单卖家退款完成，待买家确认";
        }
        updateOrder(orderInfo, operatorId);
        logs(orderInfo.getOrderId(), startStatus(), content, operatorId);
    }

    @Override
    public void updateStatus(String orderId, String operatorId) {
        //
    }
}
