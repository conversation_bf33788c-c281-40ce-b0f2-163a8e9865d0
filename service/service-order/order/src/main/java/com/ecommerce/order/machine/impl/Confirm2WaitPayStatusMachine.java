package com.ecommerce.order.machine.impl;

import com.alibaba.fastjson.JSON;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.order.api.dto.OrderDTO;
import com.ecommerce.order.api.dto.OrderDiscountDetailDTO;
import com.ecommerce.order.api.dto.OrderItemDTO;
import com.ecommerce.order.api.enums.DiscountTypeEnum;
import com.ecommerce.order.api.enums.OrderStatusEnum;
import com.ecommerce.order.api.enums.OrderSubStatusTypeEnum;
import com.ecommerce.order.dao.vo.OrderInfo;
import com.ecommerce.price.api.dto.BuyerPromotionalDetailsDTO;
import com.ecommerce.price.api.dto.PromotionalDTO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @created 15:47 24/09/2019
 * @description
 */
@Slf4j
@Component
public class Confirm2WaitPayStatusMachine extends AbstractStatusMachine {

    @Override
    public String startStatus() {
        return OrderStatusEnum.CONFIRMING.code();
    }

    @Override
    public String endStatus() {
        return OrderStatusEnum.WAIT_PAYMENT.code();
    }

    @Override
    public boolean precondition(OrderInfo orderInfo) {
        return true;
    }

    @Override
    public boolean precondition(OrderInfo orderInfo, Map<String, Object> param) {
        return true;
    }

    @Override
    public void driveStatus(OrderInfo orderInfo, String operatorId) {
        OrderDTO orderDTO = getOrderDetail(orderInfo.getOrderId());
        //确认使用优惠信息
        useResourcePromotional(orderDTO, operatorId);

        orderInfo.setOrderStatus(endStatus());
        updateOrder(orderInfo, operatorId);

        // 子状态变更
        subStatusMachineFactory.driveStatus(orderDTO.getOrderId(), OrderSubStatusTypeEnum.PAY_STATUS.getCode(), operatorId);
        logs(orderInfo.getOrderId(), startStatus(), "卖家确认订单", operatorId);
    }

    @Override
    public void driveStatus(OrderInfo orderInfo, Map<String, Object> param, String operatorId) {
        //
    }

    @Override
    public void updateStatus(String orderId, String operatorId) {
        //
    }

    /**
     * 使用优惠券
     *
     * @param order
     * @param operator
     */
    private void useResourcePromotional(OrderDTO order, String operator) {
        List<OrderDiscountDetailDTO> disDetails = Collections.emptyList();
        if (CollectionUtils.isNotEmpty(disDetails)) {
            Map<String, OrderItemDTO> orderItemMap = Maps.newHashMap();
            order.getOrderItems().forEach(action -> orderItemMap.put(action.getOrderItemId(), action));

            PromotionalDTO promot = new PromotionalDTO();
            List<BuyerPromotionalDetailsDTO> promlist = Lists.newArrayList();

            for (OrderDiscountDetailDTO discount : disDetails) {
                BuyerPromotionalDetailsDTO promotd = new BuyerPromotionalDetailsDTO();
                promotd.setOrderId(order.getOrderId());
                promotd.setOrderNo(order.getOrderCode());
                promotd.setUseTime(new Date());
                if (DiscountTypeEnum.ONE.code().equals(discount.getDiscountType())) {
                    promotd.setBuyerPriceSaleRuleId(discount.getDiscountId());
                } else if (DiscountTypeEnum.SECTION.code().equals(discount.getDiscountType())) {
                    promotd.setBuyerSectionSaleRuleId(discount.getDiscountId());
                } else if (DiscountTypeEnum.SHOP.code().equals(discount.getDiscountType())) {
                    promotd.setReferrerRuleId(discount.getDiscountId());//优惠码id
                } else {
                    continue;
                }

                promotd.setBuyerId(order.getBuyerId());
                promotd.setBuyerName(order.getBuyerName());
                promotd.setSellerId(order.getSellerId());
                promotd.setSellerName(order.getSellerName());
                promotd.setResourceSalePrice(discount.getUntiPrice());
                promotd.setResourceTotalPrice(discount.getAmountPrice());

                OrderItemDTO item = orderItemMap.get(discount.getObjectId());
                if (item != null) {
                    promotd.setGoodsId(item.getGoodsId());
                    promotd.setGoodsName(item.getGoodsName());
                    promotd.setResourceId(item.getResourceId());
                    promotd.setResourceName(item.getResourceName());
                    promotd.setResourceAmount(item.getItemQuantity());
                    promotd.setResourceOriginalPrice(item.getOriginUnitPrice());
                    promotd.setUnit(item.getUnits());
                }
                promlist.add(promotd);
            }
            promot.setList(promlist);
            try {
                log.info("savePromotionDetail:{}", JSON.toJSONString(promlist));
            } catch (Exception e) {
                log.error("savePromotionDetail_使用优惠信息出错", e);
                throw new BizException(BasicCode.UNDEFINED_ERROR, "优惠信息失效：" + e.getMessage());
            }
        }
    }
}
