package com.ecommerce.order.machine.impl;

import com.ecommerce.order.api.dto.OrderSubStatusDTO;
import com.ecommerce.order.api.enums.DeliveryTypeEnum;
import com.ecommerce.order.api.enums.OrderStatusEnum;
import com.ecommerce.order.api.enums.OrderSubStatusTypeEnum;
import com.ecommerce.order.dao.vo.OrderInfo;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @created 17:22 26/09/2019
 * @description
 */
@Component
public class InDelivery2CompletedStatusMachine extends AbstractStatusMachine {


    @Override
    public String startStatus() {
        return OrderStatusEnum.IN_DELIVERY.getCode();
    }

    @Override
    public String endStatus() {
        return OrderStatusEnum.COMPLETED.getCode();
    }

    @Override
    public boolean precondition(OrderInfo orderInfo) {
        return true;
    }

    @Override
    public boolean precondition(OrderInfo orderInfo, Map<String, Object> param) {
        return true;
    }

    @Override
    public void driveStatus(OrderInfo orderInfo, String operatorId) {
        driveStatus(orderInfo, Maps.newHashMap(), operatorId);
    }

    @Override
    public void driveStatus(OrderInfo orderInfo, Map<String, Object> param, String operatorId) {
        String orderStatus = orderInfo.getOrderStatus();
        OrderSubStatusDTO orderSubStatus = findOrderSubStatus(orderInfo.getOrderId(), OrderSubStatusTypeEnum.DELIVERY_STATUS.getCode());
        if (orderSubStatus == null || !orderSubStatus.getStatus().equals(DeliveryTypeEnum.COMPLETED.getCode())) {
            subStatusMachineFactory.driveStatus(orderInfo.getOrderId(), OrderSubStatusTypeEnum.DELIVERY_STATUS.getCode(), param, operatorId);
        }

        orderInfo.setOrderStatus(endStatus());
        updateOrder(orderInfo, operatorId);

        logs(orderInfo.getOrderId(), orderStatus, "订单完成", operatorId);
    }

    @Override
    public void updateStatus(String orderId, String operatorId) {
        //
    }
}
