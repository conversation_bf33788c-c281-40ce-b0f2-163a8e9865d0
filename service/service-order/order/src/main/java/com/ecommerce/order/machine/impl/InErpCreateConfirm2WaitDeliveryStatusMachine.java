package com.ecommerce.order.machine.impl;

import com.alibaba.fastjson.JSON;
import com.ecommerce.order.api.dto.OrderSubStatusDTO;
import com.ecommerce.order.api.dto.TakeInfoDTO;
import com.ecommerce.order.api.enums.ErpStatusEnum;
import com.ecommerce.order.api.enums.OrderStatusEnum;
import com.ecommerce.order.api.enums.OrderSubStatusTypeEnum;
import com.ecommerce.order.dao.vo.OrderInfo;
import com.ecommerce.order.fsm.service.ITakeInfoService;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @created 16:53 26/09/2019
 * @description
 */
@Slf4j
@Component
public class InErpCreateConfirm2WaitDeliveryStatusMachine extends AbstractStatusMachine {

    @Autowired
    private ITakeInfoService takeInfoService;

    @Override
    public String startStatus() {
        return OrderStatusEnum.IN_ERP_CREATE_CONFIRM.code();
    }

    @Override
    public String endStatus() {
        return OrderStatusEnum.WAIT_DELIVERED.code();
    }

    @Override
    public boolean precondition(OrderInfo orderInfo) {
        return true;
    }

    @Override
    public boolean precondition(OrderInfo orderInfo, Map<String, Object> param) {
        return true;
    }

    @Transactional
    @Override
    public void driveStatus(OrderInfo orderInfo, String operatorId) {
        driveStatus(orderInfo, Maps.newHashMap(), operatorId);
    }

    @Transactional
    @Override
    public void driveStatus(OrderInfo orderInfo, Map<String, Object> param, String operatorId) {
        orderInfo.setOrderStatus(endStatus());
        updateOrder(orderInfo, operatorId);
        logs(orderInfo.getOrderId(), startStatus(), "创建成功，开始发货", operatorId);

        OrderSubStatusDTO subStatus = findOrderSubStatus(orderInfo.getOrderId(), OrderSubStatusTypeEnum.ERP_STATUS.getCode());
        boolean flag = false;
        if (subStatus == null || !ErpStatusEnum.ERP_CREATE_SUCCESS.getCode().equals(subStatus.getStatus())) {
            flag = subStatusMachineFactory.driveStatus(orderInfo.getOrderId(), OrderSubStatusTypeEnum.ERP_STATUS.getCode(), operatorId);
        }

        if (!flag) {
            return;
        }

        // 流转发货状态
        subStatusMachineFactory.driveStatus(orderInfo.getOrderId(), OrderSubStatusTypeEnum.DELIVERY_STATUS.getCode(), param, operatorId);
        // 创建发货单
        try {
            List<TakeInfoDTO> takeInfos = takeInfoService.doOrderCreateTakeInfo(baseOrderSearchBiz.getOrderDetail(orderInfo.getOrderId()), operatorId);
            log.info("createTakeInfo_end:{},{}", orderInfo.getOrderId(), JSON.toJSONString(takeInfos));
        } catch (Exception e) {
            log.error("createTakeInfo_Error:{},{}", orderInfo.getOrderId(), e);
        }
    }

    @Override
    public void updateStatus(String orderId, String operatorId) {
        //
    }


}
