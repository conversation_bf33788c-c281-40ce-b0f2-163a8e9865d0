package com.ecommerce.order.machine.impl;

import com.ecommerce.order.api.dto.OrderRefundDTO;
import com.ecommerce.order.api.dto.OrderSubStatusDTO;
import com.ecommerce.order.api.enums.*;
import com.ecommerce.order.biz.IOrderErpBiz;
import com.ecommerce.order.dao.vo.OrderInfo;
import com.ecommerce.order.service.order.IRefundQueryService;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @created 17:22 26/09/2019
 * @description
 */
@RequiredArgsConstructor
@Component
public class InPayment2CompletedStatusMachine extends AbstractStatusMachine {

    private final IOrderErpBiz orderErpBiz;

    private final IRefundQueryService refundQueryService;


    @Override
    public String startStatus() {
        return OrderStatusEnum.IN_PAYMENT.getCode();
    }

    @Override
    public String endStatus() {
        return OrderStatusEnum.COMPLETED.getCode();
    }

    @Override
    public boolean precondition(OrderInfo orderInfo) {
        OrderRefundDTO orderRefundDTO = refundQueryService.findOrderRefundByOrderId(orderInfo.getOrderId());
        //确认或者自动完成退款
        return CsStringUtils.equals(orderInfo.getAdjustStatus(), AdjustStatusEnum.CONFIRMED.getCode()) ||
                CsStringUtils.equals(orderRefundDTO.getRefundType(), RefundTypeEnum.AUTO_COMPLETE_REFUND.getCode());
    }

    @Override
    public boolean precondition(OrderInfo orderInfo, Map<String, Object> param) {
        OrderRefundDTO orderRefundDTO = refundQueryService.findOrderRefundByOrderId(orderInfo.getOrderId());
        //确认或者自动完成退款
        return CsStringUtils.equals(orderInfo.getAdjustStatus(), AdjustStatusEnum.CONFIRMED.getCode()) ||
                CsStringUtils.equals(orderRefundDTO.getRefundType(), RefundTypeEnum.AUTO_COMPLETE_REFUND.getCode());
    }

    @Override
    public void driveStatus(OrderInfo orderInfo, String operatorId) {
        driveStatus(orderInfo, Maps.newHashMap(), operatorId);
    }

    @Override
    public void driveStatus(OrderInfo orderInfo, Map<String, Object> param, String operatorId) {
        String orderStatus = orderInfo.getOrderStatus();
        OrderSubStatusDTO orderSubStatus = findOrderSubStatus(orderInfo.getOrderId(), OrderSubStatusTypeEnum.DELIVERY_STATUS.getCode());
        if (orderSubStatus == null || !orderSubStatus.getStatus().equals(DeliveryTypeEnum.COMPLETED.getCode())) {
            subStatusMachineFactory.driveStatus(orderInfo.getOrderId(), OrderSubStatusTypeEnum.DELIVERY_STATUS.getCode(), param, operatorId);
        }

        orderInfo.setOrderStatus(endStatus());
        updateOrder(orderInfo, operatorId);

        logs(orderInfo.getOrderId(), orderStatus, "订单完成", operatorId);
    }

    @Override
    public void updateStatus(String orderId, String operatorId) {
        //
    }
}
