package com.ecommerce.order.machine.impl;

import com.ecommerce.member.api.dto.member.config.MemberConfigEnum;
import com.ecommerce.order.api.dto.OrderSubStatusDTO;
import com.ecommerce.order.api.enums.OrderStatusEnum;
import com.ecommerce.order.api.enums.OrderSubStatusTypeEnum;
import com.ecommerce.order.api.enums.PayStatusEnum;
import com.ecommerce.order.biz.IOrderErpBiz;
import com.ecommerce.order.dao.vo.OrderInfo;
import com.ecommerce.order.fsm.service.ITakeInfoService;
import com.ecommerce.pay.api.v2.enums.ChannelCodeEnum;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @created 15:27 26/09/2019
 * @description
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class InPayment2InErpCreateConfirmStatusMachine extends AbstractStatusMachine {

    private final ITakeInfoService takeInfoService;

    private final IOrderErpBiz orderErpBiz;

    @Override
    public String startStatus() {
        return OrderStatusEnum.IN_PAYMENT.code();
    }

    @Override
    public String endStatus() {
        return OrderStatusEnum.IN_ERP_CREATE_CONFIRM.code();
    }

    @Override
    public boolean precondition(OrderInfo orderInfo) {
        return orderErpBiz.hasErpOrder(orderInfo.getSellerId(), MemberConfigEnum.ERP_ORDER_FLG.getKeyCode());
    }

    @Override
    public boolean precondition(OrderInfo orderInfo, Map<String, Object> param) {
        String channelCode = (String) param.get("channelCode");
        if (channelCode == null) return false;
        Boolean pass = (Boolean) param.get("completed");
        if (pass == null) return false;
        if (ChannelCodeEnum.OFFLINE.getCode().equals(channelCode) && Boolean.TRUE.equals(pass)) {
            Boolean hasErpOrder = (Boolean) param.get("hasErpOrder");
            if (hasErpOrder == null) return precondition(orderInfo);
            return Boolean.TRUE.equals(hasErpOrder);
        }
        return false;
    }

    @Override
    public void driveStatus(OrderInfo orderInfo, String operatorId) {
        driveStatus(orderInfo, Maps.newHashMap(), operatorId);
    }

    @Override
    public void driveStatus(OrderInfo orderInfo, Map<String, Object> param, String operatorId) {
        OrderSubStatusDTO orderSubStatus = findOrderSubStatus(orderInfo.getOrderId(), OrderSubStatusTypeEnum.PAY_STATUS.getCode());
        if (orderSubStatus == null || !PayStatusEnum.COMPLETED.code().equals(orderSubStatus.getStatus())) {
            subStatusMachineFactory.driveStatus(orderInfo.getOrderId(), OrderSubStatusTypeEnum.PAY_STATUS.getCode(), param, operatorId);
        }
        // 支付子状态流转完毕
        orderInfo.setOrderStatus(endStatus());
        updateOrder(orderInfo, operatorId);

        logs(orderInfo.getOrderId(), startStatus(), "支付完成，ERP创建中", operatorId);
        subStatusMachineFactory.driveStatus(orderInfo.getOrderId(), OrderSubStatusTypeEnum.ERP_STATUS.getCode(), operatorId);

    }

    @Override
    public void updateStatus(String orderId, String operatorId) {
        //
    }
}
