package com.ecommerce.order.machine.impl;

import com.alibaba.fastjson.JSON;
import com.ecommerce.order.api.dto.OrderSubStatusDTO;
import com.ecommerce.order.api.dto.TakeInfoDTO;
import com.ecommerce.order.api.enums.OrderStatusEnum;
import com.ecommerce.order.api.enums.OrderSubStatusTypeEnum;
import com.ecommerce.order.api.enums.PayStatusEnum;
import com.ecommerce.order.biz.IOrderErpBiz;
import com.ecommerce.order.dao.vo.OrderInfo;
import com.ecommerce.order.fsm.service.ITakeInfoService;
import com.ecommerce.pay.api.v2.enums.ChannelCodeEnum;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @created 15:27 26/09/2019
 * @description
 */
@RequiredArgsConstructor
@Slf4j
@Component
public class InPayment2WaitDeliveryStatusMachine extends AbstractStatusMachine {

    private final ITakeInfoService takeInfoService;

    private final IOrderErpBiz orderErpBiz;

    @Override
    public String startStatus() {
        return OrderStatusEnum.IN_PAYMENT.code();
    }

    @Override
    public String endStatus() {
        return OrderStatusEnum.WAIT_DELIVERED.code();
    }

    @Override
    public boolean precondition(OrderInfo orderInfo) {
        return false;
    }

    @Override
    public boolean precondition(OrderInfo orderInfo, Map<String, Object> param) {
        String channelCode = (String) param.get("channelCode");
        if (channelCode == null) return false;
        Boolean pass = (Boolean) param.get("completed");
        if (pass == null) return false;
        return ChannelCodeEnum.OFFLINE.getCode().equals(channelCode) && Boolean.TRUE.equals(pass);
    }

    @Override
    public void driveStatus(OrderInfo orderInfo, String operatorId) {
        driveStatus(orderInfo, Maps.newHashMap(), operatorId);
    }

    @Override
    public void driveStatus(OrderInfo orderInfo, Map<String, Object> param, String operatorId) {
        OrderSubStatusDTO orderSubStatus = findOrderSubStatus(orderInfo.getOrderId(), OrderSubStatusTypeEnum.PAY_STATUS.getCode());
        if (orderSubStatus == null || !PayStatusEnum.COMPLETED.code().equals(orderSubStatus.getStatus())) {
            subStatusMachineFactory.driveStatus(orderInfo.getOrderId(), OrderSubStatusTypeEnum.PAY_STATUS.getCode(), param, operatorId);
        }
        //创建发货单（异步）
        createTakeInfoAsync(orderInfo.getOrderId(), operatorId);
        // 流转发货状态
        subStatusMachineFactory.driveStatus(orderInfo.getOrderId(), OrderSubStatusTypeEnum.DELIVERY_STATUS.getCode(), param, operatorId);
        // 支付子状态流转完毕
        orderInfo.setOrderStatus(endStatus());
        updateOrder(orderInfo, operatorId);
        logs(orderInfo.getOrderId(), startStatus(), "线下支付，待卖家确认支付", operatorId);
    }

    @Override
    public void updateStatus(String orderId, String operatorId) {
        //
    }

    /**
     * 创建发货单（异步）
     * @param orderId
     * @param operator
     * @return
     */
    private void createTakeInfoAsync(String orderId, String operator){
        try{
            CompletableFuture.supplyAsync(()->{
                log.info("createTakeInfoAsync_start:{},{}", orderId, operator);
                try{
                    List<TakeInfoDTO> takeInfos = takeInfoService.doOrderCreateTakeInfo(getOrderDetail(orderId), operator);
                    log.info("createTakeInfoAsync_end:{},{}", orderId, JSON.toJSONString(takeInfos));
                }catch(Exception e){
                    log.error("createTakeInfoAsync_Error:{},{}", orderId, e);
                }
                return "OK";
            });
        }catch(Exception e){
            log.error("createTakeInfoAsync_Error:{},{}", orderId, e);
        }
    }
}
