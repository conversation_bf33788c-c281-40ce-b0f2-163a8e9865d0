package com.ecommerce.order.machine.impl;

import com.ecommerce.order.api.enums.OrderStatusEnum;
import com.ecommerce.order.api.enums.OrderSubStatusTypeEnum;
import com.ecommerce.order.api.enums.PayTypeEnum;
import com.ecommerce.order.dao.vo.OrderInfo;
import com.ecommerce.order.fsm.service.ITakeInfoService;
import com.ecommerce.pay.api.v2.enums.ChannelCodeEnum;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @created 16:53 26/09/2019
 * @description
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class WaitDelivery2InDeliveryStatusMachine extends AbstractStatusMachine {

    protected final ITakeInfoService takeInfoService;

    @Override
    public String startStatus() {
        return OrderStatusEnum.WAIT_DELIVERED.code();
    }

    @Override
    public String endStatus() {
        return OrderStatusEnum.IN_DELIVERY.code();
    }

    @Override
    public boolean precondition(OrderInfo orderInfo) {
        return true;
    }

    @Override
    public boolean precondition(OrderInfo orderInfo, Map<String, Object> param) {
        String channelCode = (String) param.getOrDefault("channelCode", "");
        String payInfoType = (String) param.getOrDefault("payInfoType", "");
        Set<String> payInfoTypeSet = Sets.newHashSet(
                PayTypeEnum.SUPPLEMENT.code(),
                PayTypeEnum.REFUND.code());
        return !(CsStringUtils.equals(channelCode, ChannelCodeEnum.OFFLINE.getCode()) &&
                payInfoTypeSet.contains(payInfoType));
    }

    @Transactional
    @Override
    public void driveStatus(OrderInfo orderInfo, String operatorId) {
        driveStatus(orderInfo, Maps.newHashMap(), operatorId);
    }

    @Transactional
    @Override
    public void driveStatus(OrderInfo orderInfo, Map<String, Object> param, String operatorId) {
        orderInfo.setOrderStatus(endStatus());
        updateOrder(orderInfo, operatorId);
        logs(orderInfo.getOrderId(), startStatus(), "开始发货", operatorId);

        // 流转发货状态
        subStatusMachineFactory.driveStatus(orderInfo.getOrderId(), OrderSubStatusTypeEnum.DELIVERY_STATUS.getCode(), param, operatorId);
    }

    @Override
    public void updateStatus(String orderId, String operatorId) {
        //
    }


}
