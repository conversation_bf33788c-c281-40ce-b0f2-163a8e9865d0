package com.ecommerce.order.machine.impl;

import com.ecommerce.member.api.dto.member.config.MemberConfigEnum;
import com.ecommerce.order.api.dto.OrderSubStatusDTO;
import com.ecommerce.order.api.enums.*;
import com.ecommerce.order.biz.IOrderErpBiz;
import com.ecommerce.order.dao.vo.OrderInfo;
import com.ecommerce.order.fsm.service.ITakeInfoService;
import com.ecommerce.pay.api.v2.enums.ChannelCodeEnum;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import groovy.util.logging.Slf4j;
import lombok.RequiredArgsConstructor;
import com.ecommerce.common.utils.CsStringUtils;

import java.util.Map;
import java.util.Set;

/**
 *
 * <AUTHOR>
 * Date: Create in 下午5:03 20/2/19
 */
@Slf4j
@RequiredArgsConstructor
public class WaitDelivery2InPaymentStatusMachine extends AbstractStatusMachine {

    protected final ITakeInfoService takeInfoService;

    protected final IOrderErpBiz orderErpBiz;

    @Override
    public String startStatus() {
        return OrderStatusEnum.WAIT_DELIVERED.code();
    }

    @Override
    public String endStatus() {
        return OrderStatusEnum.IN_PAYMENT.code();
    }

    @Override
    public boolean precondition(OrderInfo orderInfo) {
        boolean hasErpOrder = orderErpBiz.hasErpOrder(orderInfo.getSellerId(), MemberConfigEnum.ERP_ORDER_FLG.getKeyCode());
        return !hasErpOrder;
    }

    @Override
    public boolean precondition(OrderInfo orderInfo, Map<String, Object> param) {
        Boolean hasErpOrder = (Boolean) param.get("hasErpOrder");
        if (hasErpOrder == null) {
            hasErpOrder = orderErpBiz.hasErpOrder(orderInfo.getSellerId(), MemberConfigEnum.ERP_ORDER_FLG.getKeyCode());
        }
        if (Boolean.TRUE.equals(hasErpOrder)) {
            return false;
        }
        String channelCode = (String) param.getOrDefault("channelCode", "");
        String payInfoType = (String) param.getOrDefault("payInfoType", "");
        Set<String> payInfoTypeSet = Sets.newHashSet(
                PayTypeEnum.SUPPLEMENT.code(),
                PayTypeEnum.REFUND.code());
        return CsStringUtils.equals(channelCode, ChannelCodeEnum.OFFLINE.getCode()) && payInfoTypeSet.contains(payInfoType);
    }

    @Override
    public void driveStatus(OrderInfo orderInfo, String operatorId) {
        driveStatus(orderInfo, Maps.newHashMap(), operatorId);
    }

    @Override
    public void driveStatus(OrderInfo orderInfo, Map<String, Object> param, String operatorId) {
        OrderSubStatusDTO orderSubStatus = findOrderSubStatus(orderInfo.getOrderId(), OrderSubStatusTypeEnum.PAY_STATUS.getCode());
        if (orderSubStatus == null || !PayStatusEnum.COMPLETED.code().equals(orderSubStatus.getStatus())) {
            subStatusMachineFactory.driveStatus(orderInfo.getOrderId(), OrderSubStatusTypeEnum.PAY_STATUS.getCode(), param, operatorId);
        }
        //支付子状态流转完毕
        orderInfo.setOrderStatus(endStatus());
        String payInfoType = (String) param.getOrDefault("payInfoType", "");
        String content = "";
        if (PayTypeEnum.SUPPLEMENT.code().equals(payInfoType)) {
            orderInfo.setPaymentConfirmType(PaymentConfirmTypeEnum.UNDERLINE_SUPPLEMENT.getCode());
            content = "订单买家补款完成，待卖家确认";
        } else if (PayTypeEnum.REFUND.code().equals(payInfoType)) {
            orderInfo.setPaymentConfirmType(PaymentConfirmTypeEnum.UNDERLINE_REFUND.getCode());
            content = "订单卖家退款完成，待买家确认";
        }
        updateOrder(orderInfo, operatorId);
        logs(orderInfo.getOrderId(), startStatus(), content, operatorId);
    }

    @Override
    public void updateStatus(String orderId, String operatorId) {
        //
    }
}
