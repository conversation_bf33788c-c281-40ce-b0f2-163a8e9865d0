package com.ecommerce.order.machine.impl;

import com.ecommerce.member.api.dto.member.config.MemberConfigEnum;
import com.ecommerce.order.api.enums.OrderStatusEnum;
import com.ecommerce.order.api.enums.OrderSubStatusTypeEnum;
import com.ecommerce.order.biz.IOrderErpBiz;
import com.ecommerce.order.dao.vo.OrderInfo;
import com.ecommerce.pay.api.v2.enums.ChannelCodeEnum;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @created 17:38 23/09/2019
 * @description
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WaitPay2InErpCreateConfirmStatusMachine extends AbstractStatusMachine {

    private final IOrderErpBiz orderErpBiz;

    @Override
    public String startStatus() {
        return OrderStatusEnum.WAIT_PAYMENT.code();
    }

    @Override
    public String endStatus() {
        return OrderStatusEnum.IN_ERP_CREATE_CONFIRM.code();
    }

    @Override
    public boolean precondition(OrderInfo orderInfo) {
        return orderErpBiz.hasErpOrder(orderInfo.getSellerId(), MemberConfigEnum.ERP_ORDER_FLG.getKeyCode());
    }

    @Override
    public boolean precondition(OrderInfo orderInfo, Map<String, Object> param) {
        Boolean hasErpOrder = (Boolean) param.get("hasErpOrder");
        if (hasErpOrder == null) return precondition(orderInfo);
        if (Boolean.FALSE.equals(hasErpOrder)) {
            return false;
        }
        String channelCode = (String) param.get("channelCode");
        return CsStringUtils.isEmpty(channelCode) || !ChannelCodeEnum.OFFLINE.getCode().equals(channelCode);
    }

    @Override
    public void driveStatus(OrderInfo orderInfo, String operatorId) {
        driveStatus(orderInfo, Maps.newHashMap(), operatorId);
    }

    @Override
    public void driveStatus(OrderInfo orderInfo, Map<String, Object> param, String operatorId) {
            // 支付子状态流转完毕
            orderInfo.setOrderStatus(endStatus());
            updateOrder(orderInfo, operatorId);

            logs(orderInfo.getOrderId(), startStatus(), "支付完成，ERP创建中", operatorId);
            subStatusMachineFactory.driveStatus(orderInfo.getOrderId(), OrderSubStatusTypeEnum.ERP_STATUS.getCode(), operatorId);
    }

    @Override
    public void updateStatus(String orderId, String operatorId) {
        //
    }
}
