package com.ecommerce.order.machine.impl;

import com.ecommerce.member.api.dto.member.config.MemberConfigEnum;
import com.ecommerce.order.api.enums.OrderStatusEnum;
import com.ecommerce.order.api.enums.PaymentConfirmTypeEnum;
import com.ecommerce.order.biz.IOrderErpBiz;
import com.ecommerce.order.dao.vo.OrderInfo;
import com.ecommerce.order.fsm.service.ITakeInfoService;
import com.ecommerce.pay.api.v2.enums.ChannelCodeEnum;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @created 15:27 26/09/2019
 * @description
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class WaitPay2InPaymentStatusMachine extends AbstractStatusMachine {

    protected final ITakeInfoService takeInfoService;

    protected final IOrderErpBiz orderErpBiz;

    @Override
    public String startStatus() {
        return OrderStatusEnum.WAIT_PAYMENT.code();
    }

    @Override
    public String endStatus() {
        return OrderStatusEnum.IN_PAYMENT.code();
    }

    @Override
    public boolean precondition(OrderInfo orderInfo) {
        boolean hasErpOrder = orderErpBiz.hasErpOrder(orderInfo.getSellerId(), MemberConfigEnum.ERP_ORDER_FLG.getKeyCode());
        return !hasErpOrder;
    }

    @Override
    public boolean precondition(OrderInfo orderInfo, Map<String, Object> param) {
        Boolean hasErpOrder = (Boolean) param.get("hasErpOrder");
        if (hasErpOrder == null) {
            hasErpOrder = orderErpBiz.hasErpOrder(orderInfo.getSellerId(), MemberConfigEnum.ERP_ORDER_FLG.getKeyCode());
        }
        if (Boolean.TRUE.equals(hasErpOrder)) {
            return false;
        }
        String channelCode = (String) param.get("channelCode");
        return !CsStringUtils.isEmpty(channelCode) && ChannelCodeEnum.OFFLINE.getCode().equals(channelCode);
    }

    @Override
    public void driveStatus(OrderInfo orderInfo, String operatorId) {
        driveStatus(orderInfo, Maps.newHashMap(), operatorId);
    }

    @Override
    public void driveStatus(OrderInfo orderInfo, Map<String, Object> param, String operatorId) {
        // 支付子状态流转完毕
        orderInfo.setOrderStatus(endStatus());
        orderInfo.setPaymentConfirmType(PaymentConfirmTypeEnum.UNDERLINE_PAY.getCode());
        updateOrder(orderInfo, operatorId);
        logs(orderInfo.getOrderId(), startStatus(), "订单买家支付完成，待卖家确认", operatorId);
    }

    @Override
    public void updateStatus(String orderId, String operatorId) {
        //
    }
}
