package com.ecommerce.order.machine.impl;

import com.alibaba.fastjson.JSON;
import com.ecommerce.member.api.dto.member.config.MemberConfigEnum;
import com.ecommerce.order.api.dto.OrderSubStatusDTO;
import com.ecommerce.order.api.dto.TakeInfoDTO;
import com.ecommerce.order.api.enums.OrderStatusEnum;
import com.ecommerce.order.api.enums.OrderSubStatusTypeEnum;
import com.ecommerce.order.api.enums.PayStatusEnum;
import com.ecommerce.order.biz.IOrderErpBiz;
import com.ecommerce.order.dao.vo.OrderInfo;
import com.ecommerce.order.fsm.service.ITakeInfoService;
import com.ecommerce.pay.api.v2.enums.ChannelCodeEnum;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @created 15:27 26/09/2019
 * @description
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class WaitPay2WaitDeliveryStatusMachine extends AbstractStatusMachine {

    protected final ITakeInfoService takeInfoService;

    protected final IOrderErpBiz orderErpBiz;

    @Override
    public String startStatus() {
        return OrderStatusEnum.WAIT_PAYMENT.code();
    }

    @Override
    public String endStatus() {
        return OrderStatusEnum.WAIT_DELIVERED.code();
    }

    @Override
    public boolean precondition(OrderInfo orderInfo) {
        boolean hasErpOrder = orderErpBiz.hasErpOrder(orderInfo.getSellerId(), MemberConfigEnum.ERP_ORDER_FLG.getKeyCode());
        return !hasErpOrder;
    }

    @Override
    public boolean precondition(OrderInfo orderInfo, Map<String, Object> param) {
        Boolean hasErpOrder = (Boolean) param.get("hasErpOrder");
        if (hasErpOrder == null) {
            hasErpOrder = orderErpBiz.hasErpOrder(orderInfo.getSellerId(), MemberConfigEnum.ERP_ORDER_FLG.getKeyCode());
        }
        if (Boolean.TRUE.equals(hasErpOrder)) {
            return false;
        }
        String channelCode = (String) param.get("channelCode");
        return CsStringUtils.isEmpty(channelCode) || !ChannelCodeEnum.OFFLINE.getCode().equals(channelCode);
    }

    @Override
    public void driveStatus(OrderInfo orderInfo, String operatorId) {
        driveStatus(orderInfo, Maps.newHashMap(), operatorId);
    }

    @Override
    public void driveStatus(OrderInfo orderInfo, Map<String, Object> param, String operatorId) {
        OrderSubStatusDTO orderSubStatus = findOrderSubStatus(orderInfo.getOrderId(), OrderSubStatusTypeEnum.PAY_STATUS.getCode());
        boolean flag = true;
        if (orderSubStatus == null || !PayStatusEnum.COMPLETED.code().equals(orderSubStatus.getStatus())) {
            flag = subStatusMachineFactory.driveStatus(orderInfo.getOrderId(), OrderSubStatusTypeEnum.PAY_STATUS.getCode(), param, operatorId);
        }
        if (flag) {
            // 支付子状态流转完毕
            orderInfo.setOrderStatus(endStatus());
            updateOrder(orderInfo, operatorId);
            //创建发货单（异步）
            createTakeInfoAsync(orderInfo.getOrderId(), operatorId);
            // 流转发货状态
            subStatusMachineFactory.driveStatus(orderInfo.getOrderId(), OrderSubStatusTypeEnum.DELIVERY_STATUS.getCode(), param, operatorId);

            logs(orderInfo.getOrderId(), startStatus(), "订单支付完成，开始进行发货", operatorId);
        }
    }

    @Override
    public void updateStatus(String orderId, String operatorId) {
        //
    }

    /**
     * 创建发货单（异步）
     * @param orderId
     * @param operator
     * @return
     */
    private void createTakeInfoAsync(String orderId, String operator){
        try{
            CompletableFuture.supplyAsync(()->{
                log.info("createTakeInfoAsync_start:{},{}", orderId, operator);
                try{
                    List<TakeInfoDTO> takeInfos = takeInfoService.doOrderCreateTakeInfo(getOrderDetail(orderId), operator);
                    log.info("createTakeInfoAsync_end:{},{}", orderId, JSON.toJSONString(takeInfos));
                }catch(Exception e){
                    log.error("createTakeInfoAsync_Error:{},{}", orderId, e);
                }
                return "OK";
            });
        }catch(Exception e){
            log.error("createTakeInfoAsync_Error:{},{}", orderId, e);
        }
    }
}
