package com.ecommerce.order.machine.impl.sub;

import com.ecommerce.order.api.dto.OrderStatusLogDTO;
import com.ecommerce.order.biz.IOrderStatusLogBiz;
import com.ecommerce.order.biz.IOrderSubStatusBiz;
import com.ecommerce.order.machine.ISubStatusMachine;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @created 11:06 25/09/2019
 * @description
 */
public abstract class AbstractSubStatusMachine implements ISubStatusMachine {


    @Autowired
    private IOrderStatusLogBiz logBiz;

    @Autowired
    protected IOrderSubStatusBiz subStatusBiz;

    protected String anyStatus(String... status) {
        return String.join(",", status);
    }

    protected void logs(String orderId, String startStatus, String message, String operatorId) {
        OrderStatusLogDTO dto = new OrderStatusLogDTO();
        dto.setOrderId(orderId);
        dto.setStatusKey(subStatusType());
        dto.setStartStatus(startStatus);
        dto.setEndStatus(endStatus());
        dto.setMessage(message);
        logBiz.create(dto, operatorId);
    }
}
