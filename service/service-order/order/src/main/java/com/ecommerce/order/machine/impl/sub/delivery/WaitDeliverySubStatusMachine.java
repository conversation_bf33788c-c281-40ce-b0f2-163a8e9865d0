package com.ecommerce.order.machine.impl.sub.delivery;

import com.ecommerce.order.api.dto.OrderSubStatusDTO;
import com.ecommerce.order.api.enums.DeliveryTypeEnum;
import com.ecommerce.order.api.enums.OrderSubStatusTypeEnum;
import com.ecommerce.order.dao.vo.OrderInfo;
import com.ecommerce.order.machine.impl.sub.AbstractSubStatusMachine;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @created 17:29 26/09/2019
 * @description
 */
@Slf4j
@Component
public class WaitDeliverySubStatusMachine extends AbstractSubStatusMachine {


    @Override
    public String subStatusType() {
        return OrderSubStatusTypeEnum.DELIVERY_STATUS.getCode();
    }

    @Override
    public String startStatus() {
        return null;
    }

    @Override
    public String endStatus() {
        return DeliveryTypeEnum.WAIT_DELIVERED.getCode();
    }

    @Override
    public boolean precondition(OrderInfo orderInfo, OrderSubStatusDTO subStatusDTO) {
        return true;
    }

    @Override
    public boolean precondition(OrderInfo orderInfo, Map<String, Object> param, OrderSubStatusDTO subStatusDTO) {
        return true;
    }

    @Override
    public boolean driveStatus(OrderInfo orderInfo, String operatorId) {

        subStatusBiz.updateSubStatus(orderInfo.getOrderId(),
                OrderSubStatusTypeEnum.DELIVERY_STATUS.getCode(),
                DeliveryTypeEnum.WAIT_DELIVERED.getCode(),
                DeliveryTypeEnum.WAIT_DELIVERED.getMsg(),
                operatorId);
        return false;
    }
}
