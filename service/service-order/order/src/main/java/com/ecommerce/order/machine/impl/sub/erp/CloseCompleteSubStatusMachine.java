package com.ecommerce.order.machine.impl.sub.erp;

import com.ecommerce.order.api.dto.OrderSubStatusDTO;
import com.ecommerce.order.api.enums.ErpStatusEnum;
import com.ecommerce.order.api.enums.OrderSubStatusTypeEnum;
import com.ecommerce.order.dao.vo.OrderInfo;
import com.ecommerce.order.machine.impl.sub.AbstractSubStatusMachine;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @created 11:37 27/09/2019
 * @description
 */
@Slf4j
@Component
public class CloseCompleteSubStatusMachine extends AbstractSubStatusMachine {



    @Override
    public String subStatusType() {
        return OrderSubStatusTypeEnum.ERP_STATUS.getCode();
    }

    @Override
    public String startStatus() {
        return ErpStatusEnum.ERP_CLOSING.getCode();
    }

    @Override
    public String endStatus() {
        return ErpStatusEnum.ERP_CLOSE_SUCCESS.getCode();
    }

    @Override
    public boolean precondition(OrderInfo orderInfo, OrderSubStatusDTO subStatusDTO) {
        return true;
    }

    @Override
    public boolean precondition(OrderInfo orderInfo, Map<String, Object> param, OrderSubStatusDTO subStatusDTO) {
        return true;
    }

    @Override
    public boolean driveStatus(OrderInfo orderInfo, String operatorId) {
        subStatusBiz.updateSubStatus(orderInfo.getOrderId(),
                OrderSubStatusTypeEnum.ERP_STATUS.getCode(),
                ErpStatusEnum.ERP_CLOSE_SUCCESS.getCode(),
                ErpStatusEnum.ERP_CLOSE_SUCCESS.getMsg(),
                operatorId);

        logs(orderInfo.getOrderId(), startStatus(), "ERP关闭成功", operatorId);
        return true;
    }
}
