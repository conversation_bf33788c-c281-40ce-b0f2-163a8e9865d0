package com.ecommerce.order.machine.impl.sub.erp;

import com.ecommerce.order.api.dto.OrderSubStatusDTO;
import com.ecommerce.order.api.enums.ErpStatusEnum;
import com.ecommerce.order.api.enums.OrderSubStatusTypeEnum;
import com.ecommerce.order.dao.vo.OrderInfo;
import com.ecommerce.order.machine.impl.sub.AbstractSubStatusMachine;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @created 15:54 26/09/2019
 * @description
 */

@Component
public class InErpCreateSubStatusMachine extends AbstractSubStatusMachine {


    @Override
    public String subStatusType() {
        return OrderSubStatusTypeEnum.ERP_STATUS.getCode();
    }

    @Override
    public String startStatus() {
        return null;
    }

    @Override
    public String endStatus() {
        return ErpStatusEnum.IN_ERP_CREATE_CONFIRM.getCode();
    }

    @Override
    public boolean precondition(OrderInfo orderInfo, OrderSubStatusDTO subStatusDTO) {
        return true;
    }

    @Override
    public boolean precondition(OrderInfo orderInfo, Map<String, Object> param, OrderSubStatusDTO subStatusDTO) {
        return true;
    }

    @Override
    public boolean driveStatus(OrderInfo orderInfo, String operatorId) {
        subStatusBiz.updateSubStatus(orderInfo.getOrderId(),
                OrderSubStatusTypeEnum.ERP_STATUS.getCode(),
                ErpStatusEnum.IN_ERP_CREATE_CONFIRM.getCode(),
                ErpStatusEnum.IN_ERP_CREATE_CONFIRM.getMsg(),
                operatorId);
        return false;
    }
}
