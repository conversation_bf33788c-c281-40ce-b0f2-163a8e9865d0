package com.ecommerce.order.machine.impl.sub.payment;

import com.ecommerce.order.api.dto.OrderSubStatusDTO;
import com.ecommerce.order.api.enums.OrderSubStatusTypeEnum;
import com.ecommerce.order.api.enums.PayStatusEnum;
import com.ecommerce.order.dao.vo.OrderInfo;
import com.ecommerce.order.machine.impl.sub.AbstractSubStatusMachine;
import com.ecommerce.pay.api.v2.enums.ChannelCodeEnum;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @created 15:37 26/09/2019
 * @description
 */
@Component
public class InPay2CompleteSubStatusMachine extends AbstractSubStatusMachine {


    @Override
    public String subStatusType() {
        return OrderSubStatusTypeEnum.PAY_STATUS.getCode();
    }

    @Override
    public String startStatus() {
        return PayStatusEnum.IN_PAYMENT.code();
    }

    @Override
    public String endStatus() {
        return PayStatusEnum.COMPLETED.code();
    }

    @Override
    public boolean precondition(OrderInfo orderInfo, OrderSubStatusDTO subStatusDTO) {
        return true;
    }

    @Override
    public boolean precondition(OrderInfo orderInfo, Map<String, Object> param, OrderSubStatusDTO subStatusDTO) {
        String channelCode = (String) param.get("channelCode");
        if (channelCode == null) return false;
        Boolean pass = (Boolean) param.get("completed");
        if (pass == null) return false;
        return ChannelCodeEnum.OFFLINE.getCode().equals(channelCode) && Boolean.TRUE.equals(pass);
    }

    @Override
    public boolean driveStatus(OrderInfo orderInfo, String operatorId) {
        subStatusBiz.updateSubStatus(orderInfo.getOrderId(),
                OrderSubStatusTypeEnum.PAY_STATUS.getCode(),
                PayStatusEnum.COMPLETED.code(),
                PayStatusEnum.COMPLETED.message(),
                operatorId);

        logs(orderInfo.getOrderId(), startStatus(), "支付完成", operatorId);
        return true;
    }
}
