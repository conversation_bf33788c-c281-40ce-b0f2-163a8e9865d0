package com.ecommerce.order.machine.impl.sub.payment;

import com.ecommerce.order.api.dto.OrderSubStatusDTO;
import com.ecommerce.order.api.enums.OrderSubStatusTypeEnum;
import com.ecommerce.order.api.enums.PayStatusEnum;
import com.ecommerce.order.dao.vo.OrderInfo;
import com.ecommerce.order.machine.impl.sub.AbstractSubStatusMachine;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @created 15:37 26/09/2019
 * @description
 */
@Component
public class WaitPay2CompleteSubStatusMachine extends AbstractSubStatusMachine {


    @Override
    public String subStatusType() {
        return OrderSubStatusTypeEnum.PAY_STATUS.getCode();
    }

    @Override
    public String startStatus() {
        return PayStatusEnum.WAIT_PAYMENT.code();
    }

    @Override
    public String endStatus() {
        return PayStatusEnum.COMPLETED.code();
    }

    @Override
    public boolean precondition(OrderInfo orderInfo, OrderSubStatusDTO subStatusDTO) {
        return true;
    }

    @Override
    public boolean precondition(OrderInfo orderInfo, Map<String, Object> param, OrderSubStatusDTO subStatusDTO) {
        Boolean completed = (Boolean) param.get("completed");
        if (completed == null) return false;
        return Boolean.TRUE.equals(completed);
    }

    @Override
    public boolean driveStatus(OrderInfo orderInfo, String operatorId) {
        subStatusBiz.updateSubStatus(orderInfo.getOrderId(),
                OrderSubStatusTypeEnum.PAY_STATUS.getCode(),
                PayStatusEnum.COMPLETED.code(),
                PayStatusEnum.COMPLETED.message(),
                operatorId);

        logs(orderInfo.getOrderId(), startStatus(), "支付完成", operatorId);
        return true;
    }
}
