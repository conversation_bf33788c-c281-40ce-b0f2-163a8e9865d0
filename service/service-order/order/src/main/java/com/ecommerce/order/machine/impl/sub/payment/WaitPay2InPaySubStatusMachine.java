package com.ecommerce.order.machine.impl.sub.payment;

import com.ecommerce.order.api.dto.OrderSubStatusDTO;
import com.ecommerce.order.api.enums.OrderSubStatusTypeEnum;
import com.ecommerce.order.api.enums.PayStatusEnum;
import com.ecommerce.order.biz.IOrderSubStatusBiz;
import com.ecommerce.order.dao.vo.OrderInfo;
import com.ecommerce.order.machine.impl.sub.AbstractSubStatusMachine;
import com.ecommerce.pay.api.v2.enums.ChannelCodeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @created 14:50 26/09/2019
 * @description
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class WaitPay2InPaySubStatusMachine extends AbstractSubStatusMachine {

    private final IOrderSubStatusBiz orderSubStatusBiz;

    @Override
    public String subStatusType() {
        return OrderSubStatusTypeEnum.PAY_STATUS.getCode();
    }

    @Override
    public String startStatus() {
        return PayStatusEnum.WAIT_PAYMENT.code();
    }

    @Override
    public String endStatus() {
        return PayStatusEnum.IN_PAYMENT.code();
    }

    @Override
    public boolean precondition(OrderInfo orderInfo, OrderSubStatusDTO subStatusDTO) {
        return false;
    }

    @Override
    public boolean precondition(OrderInfo orderInfo, Map<String, Object> param, OrderSubStatusDTO subStatusDTO) {
        String channelCode = (String) param.get("channelCode");
        if (channelCode == null) return false;
        return ChannelCodeEnum.OFFLINE.getCode().equals(channelCode);
    }

    @Override
    public boolean driveStatus(OrderInfo orderInfo, String operatorId) {
        orderSubStatusBiz.updateSubStatus(orderInfo.getOrderId(),
                OrderSubStatusTypeEnum.PAY_STATUS.getCode(),
                PayStatusEnum.IN_PAYMENT.code(),
                PayStatusEnum.IN_PAYMENT.message(),
                operatorId);

        logs(orderInfo.getOrderId(), startStatus(), "买家线下支付中", operatorId);
        return false;
    }
}
