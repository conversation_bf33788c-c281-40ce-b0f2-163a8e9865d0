package com.ecommerce.order.model;

import com.ecommerce.logistics.api.dto.shipbill.ShipBillDTO;
import com.ecommerce.common.utils.CsStringUtils;

import java.util.List;
import java.util.StringJoiner;

public class OrderModel
{

    // 添加私有构造函数，防止实例化
    private OrderModel() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }

    public static String formatCanNotCloseShipBillInfo(List<ShipBillDTO> billDTOS)
    {
        StringJoiner joiner = new StringJoiner(",");
        for(ShipBillDTO dto : billDTOS)
        {

            if (CsStringUtils.isNotEmpty(dto.getExternalWaybillStatusName()))
            {
                joiner.add(dto.getWaybillNum() + "[" + dto.getStatusName()  + "," + dto.getExternalWaybillStatusName() + "]");
            }
            else
            {
                joiner.add(dto.getWaybillNum() + "[" + dto.getStatusName() + "]");
            }
        }
        return "订单关闭失败，失败原因：运单" + joiner.toString() + "无法关闭";
    }
}
