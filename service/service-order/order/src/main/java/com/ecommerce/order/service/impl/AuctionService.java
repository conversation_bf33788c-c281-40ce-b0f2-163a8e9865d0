package com.ecommerce.order.service.impl;

import com.alibaba.fastjson.JSON;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.order.api.dto.auction.*;
import com.ecommerce.order.biz.*;
import com.ecommerce.order.dao.vo.*;
import com.ecommerce.order.service.IAuctionService;
import com.ecommerce.order.util.ConvertUtils;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@RequiredArgsConstructor
public class AuctionService implements IAuctionService {

    public static final String AUCTION_NO = "auctionNo";
    public static final String BUYER_ID = "buyerId";
    public static final String STATUS = "status";
    public static final String SYSTEM = "system";
    private final IAuctionSessionBiz auctionSessionBiz;

    private final IAuctionBidDetailBiz auctionBidDetailBiz;

    private final IAuctionConditionBiz auctionConditionBiz;

    private final IAuctionConditionDeliveryRegionBiz auctionConditionDeliveryRegionBiz;

    private final IAuctionDirectedBuyerBiz auctionDirectedBuyerBiz;

    private final IAuctionParticipantBiz auctionParticipantBiz;

    private final RedisTemplate<String, String> redisTemplate;


    @Override
    public PageInfo<AuctionListBuyerResponseDTO> findActions(AuctionSessionRequestDTO dto) {
        Condition condition = auctionSessionBiz.newCondition();

        Example.Criteria and = condition.and();
        addConditionForFindActions(dto, and);
        Page<AuctionSession> auctionSessions = PageMethod.startPage(dto.getPageNumber(), dto.getPageSize());
        auctionSessionBiz.findByCondition(condition);
        PageInfo<AuctionSession> pageInfo = new PageInfo<>(auctionSessions);
        PageInfo<AuctionListBuyerResponseDTO> buyerResponseDTOPageInfo = ConvertUtils.convert(pageInfo, AuctionSession::toResponseDTO);

        if (CollectionUtils.isNotEmpty(buyerResponseDTOPageInfo.getList())) {
            Map<String, AuctionListBuyerResponseDTO> responseDTOMap = buyerResponseDTOPageInfo.getList()
                    .stream()
                    .collect(Collectors.toMap(AuctionListBuyerResponseDTO::getAuctionNo, Function.identity(), (l, r) -> r));
            // fill total amount
            condition = auctionConditionBiz.newCondition();
            condition.and().andIn(AUCTION_NO, responseDTOMap.keySet());
            condition.selectProperties(AUCTION_NO, "totalAmount");
            for (AuctionCondition auctionCondition : auctionConditionBiz.findByCondition(condition)) {
                Optional.ofNullable(responseDTOMap.get(auctionCondition.getAuctionNo()))
                        .ifPresent(responseDTO -> responseDTO.setAuctionTotalAmount(Objects.toString(auctionCondition.getTotalAmount(), "")));
            }
            if (Objects.nonNull(dto.getBuyerId())) {
                List<String> auctionNoList = buyerResponseDTOPageInfo.getList().stream()
                        .filter(d -> d.getAuctionStatus().equals("报名中"))
                        .map(AuctionListBuyerResponseDTO::getAuctionNo)
                        .toList();
                if (CollectionUtils.isNotEmpty(auctionNoList)) {
                    condition = auctionParticipantBiz.newCondition();
                    condition.and().andIn(AUCTION_NO, auctionNoList)
                            .andEqualTo(BUYER_ID, dto.getBuyerId());
                    condition.selectProperties(AUCTION_NO);
                    for (AuctionParticipant participant : auctionParticipantBiz.findByCondition(condition)) {
                        responseDTOMap.get(participant.getAuctionNo()).setAuctionStatus("已报名");
                    }
                }
            }
        }
        return buyerResponseDTOPageInfo;
    }

    private static void addConditionForFindActions(AuctionSessionRequestDTO dto, Example.Criteria and) {
        if (CsStringUtils.isNotBlank(dto.getProductName())) {
            and.andLike("goodsName", "%" + dto.getProductName().trim() + "%");
        }

        if (CsStringUtils.isNotBlank(dto.getAuctionName())) {
            and.andLike("auctionName", "%" + dto.getAuctionName().trim() + "%");
        }

        if (Objects.nonNull(dto.getPublishStartTime()) && Objects.nonNull(dto.getPublishEndTime())) {
            and.andBetween("publishTime", dto.getPublishStartTime(), dto.getPublishEndTime());
        }

        if (CsStringUtils.isNotBlank(dto.getAuctionOwner())) {
            and.andEqualTo("sellerId", dto.getAuctionOwner());
        }

        if (CsStringUtils.isNotBlank(dto.getStatus())) {
            and.andEqualTo(STATUS, dto.getStatus());
        }


        if (CsStringUtils.isNotBlank(dto.getBuyerId())) {
            // 买家查看时 需限定
            and.andIn(STATUS, Arrays.asList("报名中", "竞价中", "已结束"));
            and.andCondition("((auction_range_type like '公开%') or (auction_range_type like '定向%' and auction_no in (select auction_no from auction_participant where buyer_id = '" + dto.getBuyerId().trim() + "')))");
        }
    }

    @Override
    public String createAuctionWithSubmit(String operatorId, AuctionDetailDTO param) {
        return save(buildAuctionNo(), param, operatorId, false, "待确认");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createAuction(AuctionDetailDTO param, String operator) {
        return save(buildAuctionNo(), param, operator, false);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String editAuction(AuctionDetailDTO param, String operator) {
        return save(param.getAuctionBase().getAuctionNo(), param, operator, true);
    }

    @Override
    public String submitAuction(String operatorId, String operatorName, String auctionNo) {
        AuctionSession auctionSession = getAuctionByAuctionNoAndCheckStatus(auctionNo);
        if (!auctionSession.getStatus().equals("已创建")) {
            return "当前状态无法审核竞拍场次";
        }

        // 判断当前人
        auctionSession.setStatus("待确认");
        auctionSession.setUpdateTime(new Date());
        auctionSession.setUpdateUser(operatorId);
        auctionSession.setUpdateName(operatorName);

        auctionSessionBiz.save(auctionSession);
        return "操作成功";
    }

    private String save(String auctionNo, AuctionDetailDTO param, String operator, boolean update){
       return save(auctionNo, param, operator, update, "已创建");
    }
    private String save(String auctionNo, AuctionDetailDTO param, String operator, boolean update, String status) {
        AuctionSession session = new AuctionSession();
        if (update) {
            Condition condition = auctionSessionBiz.newCondition();
            condition.and().andEqualTo(AUCTION_NO, auctionNo);
            List<AuctionSession> sessions = auctionSessionBiz.findByCondition(condition);
            if (CollectionUtils.isEmpty(sessions)) {
                throw new BizException(BasicCode.CUSTOM_ERROR, "修改的竞拍不存在");
            }
            session.setId(sessions.get(0).getId());
        } else {
            session.setSourceType(param.getUserRole());
        }
        AuctionBaseDTO auctionBase = param.getAuctionBase();

        session.setAuctionNo(auctionNo);
        session.setAuctionName(auctionBase.getAuctionName());
        session.setSellerId(operator);
        session.setSellerName(auctionBase.getSellerName());

        AuctionRuleDTO auctionRule = param.getAuctionRule();

        session.setAuctionType(auctionRule.getAuctionType());
        session.setProcessVisible(auctionRule.getProcessVisible());
        session.setSellerVisible(auctionRule.getSellerVisible());
        session.setLimitAuctionTimes(auctionRule.getLimitAuctionTimes());
        session.setMaxAuctionTimes(auctionRule.getMaxAuctionTimes());
        session.setMinBuyerCnt(auctionRule.getMinBuyerCnt());
        session.setAuctionRangeType(auctionRule.getAuctionRangeType());

        List<AuctionDirectedBuyer> directedBuyers = Optional.ofNullable(auctionRule.getDirectedBuyers())
                .filter(CollectionUtils::isNotEmpty)
                .map(Collection::stream)
                .orElse(Stream.empty())
                .map(user -> {
                    AuctionDirectedBuyer buyer = new AuctionDirectedBuyer();
                    buyer.setAuctionNo(auctionNo);
                    buyer.setBuyerName(user.getUserName());
                    buyer.setBuyerId(user.getUserId());

                    return buyer;
                })
                .toList();

        //  保存商品属性
        AuctionProductDetailDTO productDetail = param.getAuctionProductDetail();

        session.setGoodsId(productDetail.getProductId());
        session.setGoodsName(productDetail.getAuctionProductName());
        session.setGoodsExtend(JSON.toJSONString(productDetail));

        AuctionTimeDTO auctionTime = param.getAuctionTime();

        session.setAuctionDate(LocalDateTime.of(auctionTime.getAuctionDate(), LocalTime.MIN));
        session.setAuctionStartTime(LocalDateTime.of(auctionTime.getAuctionDate(), auctionTime.getStartTime()));
        session.setAuctionEndTime(LocalDateTime.of(auctionTime.getAuctionDate(), auctionTime.getEndTime()));

        if (auctionTime.getStartTime().isAfter(auctionTime.getEndTime())) {
            throw new BizException(BasicCode.CUSTOM_ERROR, "竞拍开始时间需小于结束时间");
        }
        AuctionDepositDTO auctionDeposit = param.getAuctionDeposit();
        session.setNeedDeposit(auctionDeposit.getNeedDeposit());
        if (!auctionDeposit.getAuctionDepositPerSession().isEmpty()) {
            Optional.of(auctionDeposit.getAuctionDepositPerSession())
                    .map(BigDecimal::new)
                    .ifPresent(session::setDepositAmount);
        }
            AuctionConditionDTO auctionConditionDTO = param.getAuctionCondition();
        AuctionCondition auctionCondition = AuctionCondition.form(auctionConditionDTO, auctionNo);
        List<AuctionConditionDeliveryRegion> conditionDeliveryRegions = AuctionConditionDeliveryRegion.form(auctionConditionDTO.getDeliveries(), auctionNo);

        session.setStatus(status);
        auctionSessionBiz.save(session, operator);
        if (update) {
            Condition condition = auctionConditionBiz.newCondition();
            condition.and().andEqualTo(AUCTION_NO, auctionNo);
            List<AuctionCondition> conditions = auctionConditionBiz.findByCondition(condition);
            conditions.stream().findFirst()
                    .map(AuctionCondition::getId)
                    .ifPresent(auctionCondition::setId);
            // 清理旧数据
            AuctionConditionDeliveryRegion region = new AuctionConditionDeliveryRegion();
            region.setAuctionNo(auctionNo);
            auctionConditionDeliveryRegionBiz.delete(region);
            AuctionDirectedBuyer directedBuyer = new AuctionDirectedBuyer();
            directedBuyer.setAuctionNo(auctionNo);
            auctionDirectedBuyerBiz.delete(directedBuyer);
        }
        auctionConditionBiz.save(auctionCondition, operator);
        for (AuctionConditionDeliveryRegion region : conditionDeliveryRegions) {
            region.setAuctionConditionId(auctionCondition.getId());
        }
        auctionConditionDeliveryRegionBiz.batchInsert(conditionDeliveryRegions, operator);
        if (!directedBuyers.isEmpty()) {
            auctionDirectedBuyerBiz.batchInsert(directedBuyers, operator);
        }
        return auctionNo;
    }

    private String buildAuctionNo() {
        String datePrefix = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        return String.format("JP%s%04d", datePrefix, redisTemplate.opsForValue().increment(datePrefix));
    }

    @Override
    public String withdrawAuction(String operatorId, String operatorName, String auctionNo) {
        AuctionSession auctionSession = getAuctionByAuctionNoAndCheckStatus(auctionNo);
        if (!auctionSession.getStatus().equals("待确认")) {
            return "当前状态无法撤回";
        }

        auctionSession.setStatus("已创建");
        auctionSession.setUpdateTime(new Date());
        auctionSession.setUpdateUser(operatorId);
        auctionSession.setUpdateName(operatorName);

        auctionSessionBiz.save(auctionSession);
        return "操作成功";
    }

    @Override
    public String approveAuction(String operatorId, String operatorName, String auctionNo, Integer action) {

        AuctionSession auctionSession = getAuctionByAuctionNoAndCheckStatus(auctionNo);

        if (!auctionSession.getStatus().equals("待确认")) {
            return "当前状态无法确认";
        }
        // 如果创建人是平台管理员，则只能卖家本人审核。反之，则只有平台管理员可以审核。

        auctionSession.setStatus("报名中");
        auctionSession.setUpdateUser(operatorId);
        auctionSession.setUpdateName(operatorName);
        auctionSession.setUpdateTime(new Date());
        auctionSession.setPublisherId(operatorId);
        auctionSession.setPublishTime(LocalDateTime.now());
        auctionSessionBiz.updateSelective(auctionSession);
        return "操作成功";
    }

    /***
     * <AUTHOR>
     * @description 根据场次编号获取竞拍场次，并且检查状态是否正确，如果不正确则更新状态后返回
     * @date 2024/12/7 23:58
     * @param auctionNo:
     * @return: com.ecommerce.order.dao.vo.AuctionSession
     */


    private static final List<String> auctionStatusList = Arrays.asList("报名中", "竞价中");

    /**
     * 固定间隔10分钟
     */
    @Scheduled(fixedDelay = 1000 * 60 * 10)
    public void updateAuctionStatus() {
        Condition condition = auctionSessionBiz.newCondition();

        condition.and().andIn(STATUS, auctionStatusList);

        auctionSessionBiz.findByCondition(condition)
                .forEach(this::updateStatus);
    }

    private void updateStatus(AuctionSession auctionSession) {
        boolean needUpdateAuctionSession = false;
        //当状态时报名中和竞拍中时，避免定时任务因时间差原因，未来得及更新状态，提前check下状态是否正确，如果不正确则更新
        if ("报名中".equals(auctionSession.getStatus())) {
            if (auctionSession.getAuctionStartTime().isBefore(LocalDateTime.now()) || auctionSession.getAuctionEndTime().isBefore(LocalDateTime.now())) {
                needUpdateAuctionSession = true;
                Condition condition = new Condition(AuctionParticipant.class);
                condition.and().andEqualTo(AUCTION_NO, auctionSession.getAuctionNo());
                List<AuctionParticipant> auctionParticipants = auctionParticipantBiz.findByCondition(condition);
                if (auctionParticipants.size() < auctionSession.getMinBuyerCnt() && auctionSession.getAuctionEndTime().isBefore(LocalDateTime.now())) {
                    auctionSession.setStatus("已作废");
                    auctionSession.setCancellationType("未满足最低竞拍要求");
                    auctionSession.setUpdateUser(SYSTEM);
                    auctionSession.setUpdateTime(new Date());
                    //如果人数不足，则开始竞拍之时即是作废之时
                    auctionSession.setCancellationTime(auctionSession.getAuctionStartTime());
                } else if (auctionSession.getAuctionStartTime().isBefore(LocalDateTime.now()) && auctionSession.getAuctionEndTime().isAfter(LocalDateTime.now())) {
                    auctionSession.setStatus("竞价中");
                    auctionSession.setUpdateUser(SYSTEM);
                    auctionSession.setUpdateTime(new Date());
                } else if (auctionSession.getAuctionEndTime().isBefore(LocalDateTime.now())) {
                    auctionSession.setStatus("已结束");
                    //中标人必须在结束后才能设置，所以现在结束的时候是没有状态的。
                    auctionSession.setUpdateUser(SYSTEM);
                    auctionSession.setUpdateTime(new Date());
                }
            }

        } else if (("竞价中".equals(auctionSession.getStatus()))
            && (auctionSession.getAuctionEndTime().isBefore(LocalDateTime.now()))) {
                needUpdateAuctionSession = true;
                auctionSession.setStatus("已结束");
                //中标人必须在结束后才能设置，所以现在结束的时候是没有状态的。
                auctionSession.setUpdateUser(SYSTEM);
                auctionSession.setUpdateTime(new Date());
        }

        if (needUpdateAuctionSession) {
            auctionSessionBiz.updateSelective(auctionSession);
        }
    }

    private AuctionSession getAuctionByAuctionNoAndCheckStatus(String auctionNo) {
        AuctionSession auctionSession = new AuctionSession();
        auctionSession.setAuctionNo(auctionNo);

        List<AuctionSession> auctionSessions = auctionSessionBiz.find(auctionSession);

        if (auctionSessions.size() != 1) {
            throw new BizException(BasicCode.CUSTOM_ERROR, "该竞拍场次不存在或存在多个竞拍场次");
        }

        auctionSession = auctionSessions.get(0);

        updateStatus(auctionSession);

        return auctionSession;
    }

    @Override
    public AuctionDetailDTO getAuctionDetail(String auctionNo) {
        AuctionSession auctionSession = getAuctionByAuctionNoAndCheckStatus(auctionNo);

        AuctionDetailDTO dto = new AuctionDetailDTO();

        dto.setUserRole(auctionSession.getSourceType());

        AuctionBaseDTO base = new AuctionBaseDTO();
        base.setAuctionNo(auctionNo);
        base.setAuctionName(auctionSession.getAuctionName());
        base.setAuctionStatus(auctionSession.computeStatus());
        base.setSellerId(auctionSession.getSellerId());
        base.setSellerName(auctionSession.getSellerName());
        dto.setAuctionBase(base);

        AuctionRuleDTO rule = new AuctionRuleDTO();
        rule.setAuctionType(auctionSession.getAuctionType());
        rule.setProcessVisible(auctionSession.getProcessVisible());
        rule.setSellerVisible(auctionSession.getSellerVisible());
        rule.setLimitAuctionTimes(auctionSession.getLimitAuctionTimes());
        rule.setMaxAuctionTimes(auctionSession.getMaxAuctionTimes());
        rule.setMinBuyerCnt(auctionSession.getMinBuyerCnt());
        rule.setAuctionRangeType(auctionSession.getAuctionRangeType());

        Condition condition = auctionDirectedBuyerBiz.newCondition();
        condition.and().andEqualTo(AUCTION_NO, auctionNo);
        List<AuctionBuyerBaseInfoDTO> buyerBaseInfoDTOS = auctionDirectedBuyerBiz.findByCondition(condition)
                .stream()
                .map(u -> {
                    AuctionBuyerBaseInfoDTO buyerBaseInfoDTO = new AuctionBuyerBaseInfoDTO();
                    buyerBaseInfoDTO.setUserId(u.getBuyerId());
                    buyerBaseInfoDTO.setUserName(u.getBuyerName());
                    return buyerBaseInfoDTO;
                }).toList();
        rule.setDirectedBuyers(buyerBaseInfoDTOS);
        dto.setAuctionRule(rule);

        //  读取商品属性
        AuctionProductDetailDTO detail = Optional.ofNullable(auctionSession.getGoodsExtend())
                .map(v -> JSON.parseObject(v, AuctionProductDetailDTO.class))
                .orElse(new AuctionProductDetailDTO());
        detail.setProductId(auctionSession.getGoodsId());
        detail.setAuctionProductName(auctionSession.getGoodsName());
        // fill other
        dto.setAuctionProductDetail(detail);

        AuctionConditionDTO auctionCondition = new AuctionConditionDTO();
        auctionConditionBiz.findByCondition(condition)
                .stream()
                .findFirst()
                .ifPresent(c -> {
                    auctionCondition.setAuctionTotalAmount(Objects.toString(c.getTotalAmount(), null));
                    auctionCondition.setStartAmount(Objects.toString(c.getStartQuantity(), null));
                    auctionCondition.setAddAmount(Objects.toString(c.getIncrementGradient(), null));
                    auctionCondition.setLimitUserQuantity(Objects.toString(c.getLimitUserQuantity(), null));
                    auctionCondition.setTradeClause(c.getTermsOfTrade());
                    auctionCondition.setPaymentType(c.getPaymentType());
                    auctionCondition.setContactName(c.getContractPersonName());
                    auctionCondition.setContactPhone(c.getContractPersonMobile());

                    AuctionConditionDeliveryRegion delivery = new AuctionConditionDeliveryRegion();
                    delivery.setAuctionNo(auctionNo);
                    List<AuctionConditionDeliveryDTO> deliveryDTOS = auctionConditionDeliveryRegionBiz.find(delivery)
                            .stream()
                            .map(d -> {
                                AuctionConditionDeliveryDTO deliveryDTO = new AuctionConditionDeliveryDTO();
                                deliveryDTO.setDeliveryArea(d.getDeliveryProvince());
                                deliveryDTO.setDeliveryRange(d.getDeliveryRange());
                                deliveryDTO.setLogisticsMode(d.getLogisticsMode());
                                return deliveryDTO;
                            })
                            .toList();
                    auctionCondition.setDeliveries(deliveryDTOS);


                });
        dto.setAuctionCondition(auctionCondition);

        AuctionTimeDTO time = new AuctionTimeDTO();
        time.setAuctionDate(auctionSession.getAuctionDate().toLocalDate());
        time.setStartTime(auctionSession.getAuctionStartTime().toLocalTime());
        time.setEndTime(auctionSession.getAuctionEndTime().toLocalTime());
        dto.setAuctionTime(time);

        AuctionDepositDTO auctionDeposit = new AuctionDepositDTO();
        auctionDeposit.setAuctionDepositPerSession(Objects.toString(auctionSession.getDepositAmount(), null));
        auctionDeposit.setNeedDeposit(auctionSession.getNeedDeposit());
        dto.setAuctionDeposit(auctionDeposit);
        return dto;
    }

    @Override
    public AuctionDetailForBidDTO getAuctionDetailForBid(String auctionNo, String buyerId) {

        AuctionDetailDTO detail = getAuctionDetail(auctionNo);

        AuctionDetailForBidDTO dto = new AuctionDetailForBidDTO(detail);

        AuctionRuleDTO rule = dto.getAuctionRule();

        AuctionBaseDTO auctionBase = dto.getAuctionBase();
        String auctionStatus = auctionBase.getAuctionStatus();
        if (Objects.equals("竞价中", auctionStatus) || Objects.equals("已结束", auctionStatus)) {
            auctionStatusLogic1(auctionNo, rule, auctionStatus, dto);
            if (Objects.equals(rule.getSellerVisible(), "可见")) {
                Condition condition = auctionParticipantBiz.newCondition();
                condition.and().andEqualTo(AUCTION_NO, auctionNo);
                List<AuctionParticipant> participants = auctionParticipantBiz.findByCondition(condition);
                List<AuctionDetailForBidDTO.AuctionParticipantDTO> participantDTOS = participants
                        .stream()
                        .map(part -> {
                            AuctionDetailForBidDTO.AuctionParticipantDTO participantDTO = new AuctionDetailForBidDTO.AuctionParticipantDTO();
                            participantDTO.setName(part.getCreateName());
                            participantDTO.setRegistrationTime(LocalDateTime.ofInstant(part.getCreateTime().toInstant(), ZoneId.systemDefault()));
                            participantDTO.setNeedDeposit(part.getNeedDeposit());
                            participantDTO.setBuyerId(part.getBuyerId());
                            return participantDTO;
                        }).toList();

                dto.setParticipants(participantDTOS);
                auctionStatusLogic2(auctionNo, auctionStatus, participants, dto);
            }
        }

        auctionStatusLogic3(auctionNo, buyerId, auctionBase);

        return dto;
    }

    private void auctionStatusLogic3(String auctionNo, String buyerId, AuctionBaseDTO auctionBase) {
        if (auctionBase.getAuctionStatus().equals("报名中")) {
            Condition condition = auctionParticipantBiz.newCondition();
            condition.and().andEqualTo(AUCTION_NO, auctionNo)
                    .andEqualTo(BUYER_ID, buyerId);
            condition.selectProperties(AUCTION_NO);
            for (AuctionParticipant participant : auctionParticipantBiz.findByCondition(condition)) {
                auctionBase.setAuctionStatus("已报名");
            }
        }
    }

    private void auctionStatusLogic2(String auctionNo, String auctionStatus, List<AuctionParticipant> participants, AuctionDetailForBidDTO dto) {
        Condition condition;
        if (Objects.equals("已结束", auctionStatus)) {
            Map<String, BigDecimal> decimalMap = participants.stream()
                    .filter(participant -> Objects.nonNull(participant.getWinAuctionQuantity()))
                    .collect(Collectors.toMap(AuctionParticipant::getBuyerId, AuctionParticipant::getWinAuctionQuantity, (l, r) -> r));
            List<AuctionDetailForBidDTO.AuctionBidDetailDTO> details = dto.getBidDetails();
            if (CollectionUtils.isEmpty(details)) {
                condition = auctionBidDetailBiz.newCondition();
                condition.and().andEqualTo(AUCTION_NO, auctionNo);
                List<AuctionDetailForBidDTO.AuctionBidDetailDTO> bidDetailDTOS = auctionBidDetailBiz.findByCondition(condition)
                        .stream()
                        .map(bid -> {
                            AuctionDetailForBidDTO.AuctionBidDetailDTO bidDetailDTO = new AuctionDetailForBidDTO.AuctionBidDetailDTO();
                            bidDetailDTO.setBidAmount(Objects.toString(bid.getBidAmount(), null));
                            bidDetailDTO.setBidTime(bid.getBidTime());
                            bidDetailDTO.setName(bid.getCreateName());
                            bidDetailDTO.setBuyerId(bid.getBuyerId());
                            return bidDetailDTO;
                        }).toList();
                details = bidDetailDTOS;
            }
            if (CollectionUtils.isEmpty(details)) {
                details = details.stream()
                        .collect(Collectors.toMap(AuctionDetailForBidDTO.AuctionBidDetailDTO::getBuyerId, Function.identity(), (a, b) -> {
                            if (a.getBidTime().isAfter(b.getBidTime())) {
                                return a;
                            } else {
                                return b;
                            }
                        }))
                        .values()
                        .stream()
                        .peek(d -> d.setWinAuctionQuantity(Objects.toString(decimalMap.get(d.getBuyerId()), null)))
                        .toList();
            }
            dto.setBidDetails(details);
        }
    }

    private void auctionStatusLogic1(String auctionNo, AuctionRuleDTO rule, String auctionStatus, AuctionDetailForBidDTO dto) {
        if (Objects.equals(rule.getProcessVisible(), "可见") && Objects.equals("竞价中", auctionStatus)) {
            Condition condition = auctionBidDetailBiz.newCondition();
            condition.and().andEqualTo(AUCTION_NO, auctionNo);
            List<AuctionDetailForBidDTO.AuctionBidDetailDTO> bidDetailDTOS = auctionBidDetailBiz.findByCondition(condition)
                    .stream()
                    .map(bid -> {
                        AuctionDetailForBidDTO.AuctionBidDetailDTO bidDetailDTO = new AuctionDetailForBidDTO.AuctionBidDetailDTO();
                        bidDetailDTO.setBidAmount(Objects.toString(bid.getBidAmount(), null));
                        bidDetailDTO.setBidTime(bid.getBidTime());
                        bidDetailDTO.setName(bid.getCreateName());
                        bidDetailDTO.setBuyerId(bid.getBuyerId());

                        return bidDetailDTO;
                    }).toList();
            dto.setBidDetails(bidDetailDTOS);
        } else if (Objects.equals("已结束", auctionStatus)) {
            Condition condition = auctionParticipantBiz.newCondition();
            condition.and().andEqualTo(AUCTION_NO, auctionNo);
            List<AuctionDetailForBidDTO.AuctionBidDetailDTO> bidDetailDTOS = auctionParticipantBiz.findByCondition(condition).stream()
                    .map(x -> {
                        AuctionDetailForBidDTO.AuctionBidDetailDTO bidDetailDTO = new AuctionDetailForBidDTO.AuctionBidDetailDTO();
                        bidDetailDTO.setName(x.getBuyerName());


                        //出价时间在出价表查
                        Condition condition2 = auctionBidDetailBiz.newCondition();
                        condition2.and().andEqualTo(AUCTION_NO, auctionNo);
                        condition2.and().andEqualTo(BUYER_ID, x.getBuyerId());
                        condition2.setOrderByClause("bid_time DESC");
                        AuctionBidDetail auctionBidDetail = auctionBidDetailBiz.findByCondition(condition2)
                                .stream()
                                .findFirst()
                                .orElse(null);

                        bidDetailDTO.setBidTime(auctionBidDetail == null ? x.getUpdateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime() : auctionBidDetail.getBidTime());

                        bidDetailDTO.setBidAmount(auctionBidDetail == null ? "0" : String.valueOf(auctionBidDetail.getBidAmount()));
                        bidDetailDTO.setWinAuctionQuantity(Objects.toString(x.getWinAuctionQuantity(), "0"));
                        bidDetailDTO.setBidStatus(x.getStatus().equals("已报名") ? "未中标" : x.getStatus());
                        bidDetailDTO.setTradeStatus(bidDetailDTO.getBidStatus().equals("已中标") ? "已成交" : "");
                        bidDetailDTO.setBuyerId(x.getBuyerId());
                        return bidDetailDTO;
                    }).toList();
            dto.setBidDetails(bidDetailDTOS);
        }
    }

    @Override
    public String registerAuction(String operatorId, String operatorName, RegisterAuctionRequestDTO param) {
        AuctionSession auctionSession = getAuctionByAuctionNoAndCheckStatus(param.getAuctionNo());

        if (!auctionSession.getStatus().equals("报名中")) {
            return "该竞拍场次报名时间已结束";
        }

        Condition condition = auctionParticipantBiz.newCondition();
        condition.and().andEqualTo(AUCTION_NO, param.getAuctionNo());
        condition.and().andEqualTo(BUYER_ID, operatorId);

        List<AuctionParticipant> auctionParticipants = auctionParticipantBiz.findByCondition(condition);
        if (CollectionUtils.isNotEmpty(auctionParticipants)) {
            return "您已经报名过该场次的竞价";
        }

        LocalDateTime now = LocalDateTime.now();

        AuctionParticipant auctionParticipant = new AuctionParticipant();
        auctionParticipant.setAuctionNo(param.getAuctionNo());
        auctionParticipant.setBuyerId(operatorId);
        auctionParticipant.setBuyerName(operatorName);
        auctionParticipant.setRegisterTime(now);
        auctionParticipant.setNeedDeposit("无需保证金");
        auctionParticipant.setPaymentedDeposit("TRUE");
        auctionParticipant.setRegisterUserId(operatorId);
        auctionParticipant.setStatus("已报名");
        auctionParticipant.setCreateUser(operatorId);
        auctionParticipant.setCreateName(operatorName);
        auctionParticipant.setUpdateUser(operatorId);
        auctionParticipant.setUpdateName(operatorName);
        auctionParticipant.setDelFlg(false);

        auctionSession.setParticipant(auctionSession.getParticipant() == null ? 1 : auctionSession.getParticipant() + 1);

        auctionSessionBiz.save(auctionSession);
        auctionParticipantBiz.save(auctionParticipant, operatorId);
        return "操作成功";
    }

    @Override
    public String bidAuction(String operatorId, String operatorName, String auctionNo, BigDecimal count) {
        //目前Demo展示，不做详细的条件验证

        LocalDateTime now = LocalDateTime.now();
        AuctionBidDetail auctionBidDetail = new AuctionBidDetail();
        auctionBidDetail.setAuctionNo(auctionNo);
        auctionBidDetail.setBidAmount(count);
        auctionBidDetail.setBidTime(now);
        auctionBidDetail.setBuyerId(operatorId);
        auctionBidDetail.setCreateUser(operatorId);
        auctionBidDetail.setCreateName(operatorName);
        auctionBidDetail.setUpdateUser(operatorId);
        auctionBidDetail.setUpdateName(operatorName);
        auctionBidDetail.setDelFlg(false);
        auctionBidDetailBiz.save(auctionBidDetail);
        return "操作成功";
    }

    @Override
    public String setWinningBidder(String operatorId, String operatorName, SetAuctionWinnerRequestDTO param) {
        AuctionSession auctionSession = getAuctionByAuctionNoAndCheckStatus(param.getAuctionNo());

        if (!"已结束".equals(auctionSession.getStatus())) {
            return "竞拍场次尚未结束，不能设置中标";
        }


        Condition condition2 = new Condition(AuctionParticipant.class);
        condition2.and().andEqualTo(AUCTION_NO, param.getAuctionNo());
        condition2.and().andIn(BUYER_ID, param.getWinners().stream().map(SetAuctionWinnerRequestDTO.Winner::getWinner).toList());
        List<AuctionParticipant> winnerInfo = auctionParticipantBiz.findByCondition(condition2);
        if (winnerInfo.size() < param.getWinners().size()) {
            return "竞拍人信息获取异常：中标人列表中存在未报名的买家";
        }

        for (int i = 0; i < param.getWinners().size(); i++) {
            SetAuctionWinnerRequestDTO.Winner winner = param.getWinners().get(i);
            AuctionParticipant auctionParticipant = winnerInfo.stream().
                    filter(x -> x.getBuyerId().equals(winner.getWinner())).findFirst().orElse(null);
            if (auctionParticipant == null) {
                throw new BizException(BasicCode.CUSTOM_ERROR, "中标人中存在未报名用户");
            }
            auctionParticipant.setStatus("已中标");
            auctionParticipant.setWinAuctionQuantity(winner.getWinCount());
            auctionParticipant.setUpdateUser(operatorId);
            auctionParticipant.setUpdateName(operatorName);
            auctionParticipantBiz.updateSelective(auctionParticipant);
        }

        return "操作成功";
    }

    @Override
    public String closeAuction(String operatorId, String operatorName, String auctionNo) {
        AuctionSession auctionSession = new AuctionSession();
        auctionSession.setAuctionNo(auctionNo);
        AuctionSession auctionSessions = auctionSessionBiz.find(auctionSession).stream().findFirst().orElseThrow(() -> new RuntimeException("找不到该拍品"));


        if (!"待确认".equals(auctionSessions.getStatus())) {
            throw new BizException(BasicCode.CUSTOM_ERROR, "拍品状态不是待确认，不能关闭");
        }
        Date date = new Date();
        auctionSession.setStatus("已作废");
        auctionSession.setUpdateUser(operatorId);
        auctionSession.setUpdateName(operatorName);
        auctionSession.setCancellationType("卖家关闭");
        auctionSession.setCancellationTime(LocalDateTime.now());
        auctionSession.setUpdateTime(date);
        auctionSessionBiz.updateSelective(auctionSession);
        return "操作成功";
    }
}
