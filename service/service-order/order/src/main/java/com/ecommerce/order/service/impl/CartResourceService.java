package com.ecommerce.order.service.impl;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.goods.api.service.IGoodsService;
import com.ecommerce.goods.api.service.IResourceService;
import com.ecommerce.order.api.dto.CartResourceDTO;
import com.ecommerce.order.api.dto.OrderCartResCheckDTO;
import com.ecommerce.order.biz.ICartResourceBiz;
import com.ecommerce.order.service.ICartResourceService;
import com.ecommerce.order.service.IOrderService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

@Service
@RequiredArgsConstructor
public class CartResourceService implements ICartResourceService {

    private final ICartResourceBiz cartResourceBiz;
	private final IResourceService resourceService;
	private final IOrderService orderService;
	private final IGoodsService goodsService;
	
	@Override
	public ItemResult<CartResourceDTO> addCart(CartResourceDTO cartDTO, String operatorId) {
		return new ItemResult<>(cartResourceBiz.addCart(cartDTO, operatorId));
	}

	@Override
	public ItemResult<List<CartResourceDTO>> queryCart(String accountId, String manageModel) {
		return new ItemResult<>(cartResourceBiz.queryBuyerCart(accountId, manageModel));
	}

	@Override
	public ItemResult<String> updateCart(CartResourceDTO cartDTO, String operatorId) {
		cartResourceBiz.updateCart(cartDTO, operatorId);
		return new ItemResult<>("OK");
	}

	@Override
	public ItemResult<String> emptyCart(String accountId) {
		cartResourceBiz.emptyCart(accountId);
		return new ItemResult<>("OK");
	}

	@Override
	public ItemResult<String> moveCart(List<String> cartIds, String operatorId) {
		cartResourceBiz.moveCart(cartIds, operatorId);
		return new ItemResult<>("OK");
	}

	@Override
	public ItemResult<CartResourceDTO> queryCartDetail(String cartId) {
		return new ItemResult<>(cartResourceBiz.queryCartDetail(cartId));
	}

	@Override
	public ItemResult<String> updateQuantity(String cartId, BigDecimal quantity, String operatorId) {
		cartResourceBiz.updateQuantity(cartId, quantity, operatorId);
		return new ItemResult<>("OK");
	}

	@Override
	public ItemResult<String> checkCartResource(List<OrderCartResCheckDTO> cartIds) {
		cartResourceBiz.checkCartResource(cartIds);
		return new ItemResult<>("OK");
	}

}
