package com.ecommerce.order.service.impl;

import com.ecommerce.common.utils.CommonConstants;
import com.ecommerce.order.biz.IErpOrderQueryBiz;
import com.ecommerce.order.dao.mapper.OrderInfoMapper;
import com.ecommerce.order.dao.vo.OrderInfo;
import com.ecommerce.order.service.IErpOrderQueryService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

@Service
@RequiredArgsConstructor
public class ErpOrderQueryService implements IErpOrderQueryService
{
    private final IErpOrderQueryBiz erpOrderQueryBiz;
    private final OrderInfoMapper orderInfoMapper;

    @Override
    public List<Object> handleErpOrderQuery(String orderCode)
    {
        Condition condition1 = new Condition(OrderInfo.class);
        Example.Criteria criteria1 = condition1.createCriteria();
        criteria1.andEqualTo("orderCode", orderCode);
        criteria1.andNotEqualTo("delFlg", CommonConstants.DEL_FLAG_TRUE);
        List<OrderInfo> dataList1 = orderInfoMapper.selectByCondition(condition1);
        return erpOrderQueryBiz.handleErpOrderQuery(dataList1);
    }
}
