package com.ecommerce.order.service.impl;

import com.alibaba.fastjson.JSON;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.order.api.dto.*;
import com.ecommerce.order.biz.IEvaluateBiz;
import com.ecommerce.order.biz.IEvaluateItemBiz;
import com.ecommerce.order.biz.IOrderEvaluateBiz;
import com.ecommerce.order.service.IEvaluateService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date: 2019/4/23 3:52 PM
 * @Description 评价相关接口实现类
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class EvaluateService implements IEvaluateService{

    private final IOrderEvaluateBiz iOrderEvaluateBiz;
    private final IEvaluateBiz iEvaluateBiz;
    private final IEvaluateItemBiz iEvaluateItemBiz;

    @Override
    public ItemResult<PageInfo<OrderEvaluateListResDTO>> pageEvaluate(OrderEvaluateListReqDTO req) {
        log.info("### 分页获取订单列表参数 ### OrderEvaluateListReqDTO：{}", JSON.toJSONString(req));
        Page<OrderEvaluateListResDTO> result = iOrderEvaluateBiz.pageEvaluate(req);
        return new ItemResult<>(new PageInfo<>(result));
    }

    @Override
    public ItemResult<OrderEvaluateDetailInfoResDTO> getOrderEvaluateInfo(OrderEvaluateDetailInfoReqDTO req) {
        log.info("### 获取订单评价详情/基础数据参数 ### OrderEvaluateDetailInfoReqDTO：{}", JSON.toJSONString(req));
        OrderEvaluateDetailInfoResDTO result = iOrderEvaluateBiz.getOrderEvaluateInfo(req);
        return new ItemResult<>(result);
    }

    @Override
    public ItemResult<PageInfo<GoodsEvaluateListResDTO>> getGoodsEvaluateList(GoodsEvaluateListReqDTO req) {
        log.info("### 分页获取商品评价列表参数 ### GoodsEvaluateListReqDTO：{}", JSON.toJSONString(req));
        Page<GoodsEvaluateListResDTO> result = iEvaluateItemBiz.getGoodsEvaluateList(req);
        return new ItemResult<>(new PageInfo<>(result));
    }

    @Override
    public ItemResult<GoodsEvaluateGmbResDTO> getGoodsEvaluateCompreScoreInfo(String goodsId) {
        log.info("### 获取商品综合评价情况参数 ### goodsId：{}", goodsId);
        GoodsEvaluateGmbResDTO result = iEvaluateItemBiz.getGoodsEvaluateCompreScoreInfo(goodsId);
        return new ItemResult<>(result);
    }

    @Override
    public ItemResult<EvaluateSellerScoreResDTO> getSellerCompreScoreInfo(String sellerId) {
        log.info("### 获取卖家综合评价情况参数 ### sellerId：{}", sellerId);
        EvaluateSellerScoreResDTO result = iEvaluateItemBiz.getSellerCompreScoreInfo(sellerId);
        return new ItemResult<>(result);
    }

    @Override
    public ItemResult<Boolean> postOrderEvaluate(OrderEvaluatePostReqDTO req) {
        log.info("### 提交订单评价参数 ### OrderEvaluatePostReqDTO：{}", JSON.toJSONString(req));
        return new ItemResult<>(iEvaluateBiz.postOrderEvaluate(req));
    }

    @Override
    public ItemResult<EvaluateTotalResDTO> getEvaluateTotalNum(EvaluateTotalReqDTO req) {
        log.info("### 获取已评价/待评价条数 ### EvaluateTotalReqDTO：{}", JSON.toJSONString(req));
        return new ItemResult<>(iOrderEvaluateBiz.getEvaluateTotalNum(req));
    }
}
