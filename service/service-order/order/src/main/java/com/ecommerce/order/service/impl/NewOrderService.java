package com.ecommerce.order.service.impl;

import com.alibaba.fastjson.JSON;
import com.ecommerce.common.annotation.AddLog;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.utils.BeanConvertUtils;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.goods.api.dto.contract.TrContractGoodsDTO;
import com.ecommerce.goods.api.service.IContractService;
import com.ecommerce.logistics.api.enums.TransportToolTypeEnum;
import com.ecommerce.member.api.dto.member.config.MemberConfigEnum;
import com.ecommerce.order.api.dto.ApplyOrderInvoiceRequestDTO;
import com.ecommerce.order.api.dto.ApplyOrderInvoiceResponseDTO;
import com.ecommerce.order.api.dto.ConfirmOrderInvoiceReqDTO;
import com.ecommerce.order.api.dto.ERPAddressDTO;
import com.ecommerce.order.api.dto.FlowAddressQueryDTO;
import com.ecommerce.order.api.dto.OrderAdjustItemDTO;
import com.ecommerce.order.api.dto.OrderBillCheckDTO;
import com.ecommerce.order.api.dto.OrderBillCheckQueryDTO;
import com.ecommerce.order.api.dto.OrderCostsDTO;
import com.ecommerce.order.api.dto.OrderCountDTO;
import com.ecommerce.order.api.dto.OrderDTO;
import com.ecommerce.order.api.dto.OrderDetailConditionDTO;
import com.ecommerce.order.api.dto.OrderIdAndContractIdDTO;
import com.ecommerce.order.api.dto.OrderInfoExtDTO;
import com.ecommerce.order.api.dto.OrderInvoiceCallBackDTO;
import com.ecommerce.order.api.dto.OrderItemAddDTO;
import com.ecommerce.order.api.dto.OrderItemDTO;
import com.ecommerce.order.api.dto.OrderLogisticsQueryDTO;
import com.ecommerce.order.api.dto.OrderLogisticsResultDTO;
import com.ecommerce.order.api.dto.OrderOverviewDTO;
import com.ecommerce.order.api.dto.OrderQueryDTO;
import com.ecommerce.order.api.dto.OrderRecCostsDTO;
import com.ecommerce.order.api.dto.OrderSimpleDTO;
import com.ecommerce.order.api.dto.OrderSimpleQueryDTO;
import com.ecommerce.order.api.dto.OrderStatusStatisticDTO;
import com.ecommerce.order.api.dto.OrderStatusStatisticsDTO;
import com.ecommerce.order.api.dto.OrderUnderLinePayConfirmDTO;
import com.ecommerce.order.api.dto.OverviewReqDTO;
import com.ecommerce.order.api.dto.PayCallbackDTO;
import com.ecommerce.order.api.dto.RepurchaseOrderDTO;
import com.ecommerce.order.api.dto.TransactionsOverviewDTO;
import com.ecommerce.order.api.dto.UninvoicedOrderDTO;
import com.ecommerce.order.api.dto.UninvoicedOrderReqDTO;
import com.ecommerce.order.api.dto.adjust.AdjustRecordDTO;
import com.ecommerce.order.api.dto.adjust.AdjustStrategyDetailDTO;
import com.ecommerce.order.api.dto.concrete.CreateConcreteOrderDTO;
import com.ecommerce.order.api.dto.logistics.PickingBillExportInfoDTO;
import com.ecommerce.order.api.dto.pay.TradingFlowExportInfoDTO;
import com.ecommerce.order.api.enums.AdjustExecuteStatusEnum;
import com.ecommerce.order.api.enums.OperatorTypeEnum;
import com.ecommerce.order.api.enums.OrderStatusEnum;
import com.ecommerce.order.api.enums.OrderTakeStatus;
import com.ecommerce.order.api.enums.TakeCloseType;
import com.ecommerce.order.biz.IOrderAdjustStrategyBiz;
import com.ecommerce.order.biz.IOrderAtomicBiz;
import com.ecommerce.order.biz.IOrderErpAddressBiz;
import com.ecommerce.order.biz.IOrderErpBiz;
import com.ecommerce.order.biz.IOrderItemQuantityBiz;
import com.ecommerce.order.biz.fsm.IOrderAdjustItemBiz;
import com.ecommerce.order.biz.fsm.IOrderInfoBiz;
import com.ecommerce.order.biz.fsm.IOrderInfoExtBiz;
import com.ecommerce.order.biz.fsm.ITakeInfoBiz;
import com.ecommerce.order.dao.dto.OrderCancelDO;
import com.ecommerce.order.dao.dto.OrderCloseDO;
import com.ecommerce.order.dao.vo.OrderInfo;
import com.ecommerce.order.fsm.service.IOrderCommonService;
import com.ecommerce.order.fsm.service.IOrderComputeService;
import com.ecommerce.order.fsm.service.IOrderInvoiceService;
import com.ecommerce.order.fsm.service.IOrderPaymentService;
import com.ecommerce.order.fsm.service.IOrderQueryService;
import com.ecommerce.order.fsm.service.OrderServiceFactory;
import com.ecommerce.order.fsm.status.OrderTypeEnum;
import com.ecommerce.order.service.IOrderService;
import com.ecommerce.order.service.handler.SynchronizeIntegralHandler;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Condition;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 * Date: Create in 下午2:24 19/9/24
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class NewOrderService implements IOrderService {

    public static final String UNDEFINED_ERROR_ORDER_TYPE_MSG = "无效的订单类型！";

    @Autowired
    private IOrderItemQuantityBiz orderItemQuantityBiz;
    @Autowired
    private IOrderAdjustItemBiz orderAdjustItemBiz;
    @Autowired
    private IOrderAdjustStrategyBiz orderAdjustStrategyBiz;
    @Autowired
    private IOrderAtomicBiz orderAtomicBiz;
    @Autowired
    private IOrderQueryService orderQueryService;
    @Autowired
    private IOrderComputeService orderComputeService;
    @Autowired
    private IOrderPaymentService orderPaymentService;
    @Autowired
    private IOrderInvoiceService orderInvoiceService;
    @Autowired
    private IOrderErpBiz orderErpBiz;
    @Autowired
    private ITakeInfoBiz takeInfoBiz;
    @Autowired
    private ThreadPoolExecutor threadPoolExecutor;
    @Autowired
    private IOrderInfoBiz orderInfoBiz;
    @Autowired
    private IOrderInfoExtBiz orderInfoExtBiz;
    @Autowired
    private IOrderCommonService orderCommonService;
    @Autowired
    private IOrderErpAddressBiz orderErpAddressBiz;
    @Autowired
    private IContractService contractService;
    @Autowired
    private SynchronizeIntegralHandler synchronizeIntegralHandler;

    @AddLog(operatorIndex = 1)
    @Override
    public ItemResult<OrderDTO> doCreateContractOrder(OrderDTO orderDTO, String operator) {
        orderDTO.setOrderType(OrderTypeEnum.CONTRACT.getCode());
        return new ItemResult<>(OrderServiceFactory.getOrderHandlerService(
                OrderTypeEnum.CONTRACT.getBizType()).createOrder(orderDTO, operator));
    }

    @AddLog(operatorIndex = 1)
    @Override
    public ItemResult<OrderDTO> doCreateSelfOrder(OrderDTO orderDTO, String operator) {
        orderDTO.setOrderType(OrderTypeEnum.LISTING.getCode());
        return new ItemResult<>(OrderServiceFactory.getOrderHandlerService(
                OrderTypeEnum.LISTING.getBizType()).createOrder(orderDTO, operator));
    }

    @AddLog(operatorIndex = 1)
    @Override
    public ItemResult<OrderDTO> doCreateEnquiryOrder(OrderDTO orderDTO, String operator) {
        orderDTO.setOrderType(OrderTypeEnum.INQUIRY.getCode());
        return new ItemResult<>(OrderServiceFactory.getOrderHandlerService(
                OrderTypeEnum.INQUIRY.getBizType()).createOrder(orderDTO, operator));
    }

    @AddLog(operatorIndex = 1)
    @Override
    public ItemResult<OrderDTO> doCreateInsteadOrder(OrderDTO orderDTO, String operator) {
        OrderTypeEnum orderTypeEnum;
        if (CsStringUtils.isNotBlank(orderDTO.getDealsId())) {
            orderTypeEnum = OrderTypeEnum.CONTRACT;
            orderDTO.setOrderType(OrderTypeEnum.CONTRACT.getCode());
        } else {
            orderTypeEnum = OrderTypeEnum.LISTING;
            orderDTO.setOrderType(OrderTypeEnum.LISTING.getCode());
        }
        return new ItemResult<>(OrderServiceFactory.getOrderHandlerService(
                orderTypeEnum.getBizType()).createOrder(orderDTO, operator));
    }

    @AddLog(operatorIndex = 2)
    @Override
    public ItemResult<String> doSellerCancelOrder(String orderId, String remarks, String operator) {
        OrderTypeEnum orderTypeEnum = OrderTypeEnum.getByCode(orderInfoBiz.getOrderInfoById(orderId).getOrderType());
        if (orderTypeEnum == null) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, UNDEFINED_ERROR_ORDER_TYPE_MSG);
        }
        OrderCancelDO orderCancelDO = new OrderCancelDO();
        orderCancelDO.setOrderId(orderId);
        orderCancelDO.setRemarks(remarks);
        orderCancelDO.setOperator(operator);
        orderCancelDO.setOperatorType(OperatorTypeEnum.SELLER.getCode());
        OrderServiceFactory.getOrderHandlerService(
                orderTypeEnum.getBizType()).cancelOrder(orderCancelDO);
        return new ItemResult<>("OK");
    }

    @AddLog(operatorIndex = 2)
    @Override
    public ItemResult<String> doBuyerCancelOrder(String orderId, String remarks, String operator) {
        OrderTypeEnum orderTypeEnum = OrderTypeEnum.getByCode(orderInfoBiz.getOrderInfoById(orderId).getOrderType());
        if (orderTypeEnum == null) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, UNDEFINED_ERROR_ORDER_TYPE_MSG);
        }
        OrderCancelDO orderCancelDO = new OrderCancelDO();
        orderCancelDO.setOrderId(orderId);
        orderCancelDO.setRemarks(remarks);
        orderCancelDO.setOperator(operator);
        orderCancelDO.setOperatorType(OperatorTypeEnum.BUYER.getCode());
        OrderServiceFactory.getOrderHandlerService(
                orderTypeEnum.getBizType()).cancelOrder(orderCancelDO);
        return new ItemResult<>("OK");
    }

    @AddLog(operatorIndex = 1)
    @Override
    public ItemResult<String> doSellerConfirmOrder(OrderDTO confirmOrder, String operator) {
        OrderInfo orderInfo = orderInfoBiz.getOrderInfoById(confirmOrder.getOrderId());
        OrderTypeEnum orderTypeEnum = OrderTypeEnum.getByCode(orderInfo.getOrderType());
        if (orderTypeEnum == null) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, UNDEFINED_ERROR_ORDER_TYPE_MSG);
        }
        OrderServiceFactory.getOrderHandlerService(
                orderTypeEnum.getBizType()).confirmOrder(confirmOrder, operator);
        return new ItemResult<>("OK");
    }

    @Override
    public ItemResult<String> checkOrderCancel() {
        Condition condition = orderAtomicBiz.newCondition();
        condition.createCriteria().andEqualTo("delFlg", 0)
                .andEqualTo("orderStatus", OrderStatusEnum.WAIT_PAYMENT)
                .andLessThan("payTimeLimit", new Date());

        List<OrderInfo> orders = orderAtomicBiz.findByCondition(condition);
        if (CollectionUtils.isEmpty(orders)) return new ItemResult<>("OK");
        String operator = "checkOrderCancel";
        log.info("checkOrderCancel:{}", JSON.toJSONString(orders));
        orders.stream().forEach(order -> {
            try {
                OrderTypeEnum orderTypeEnum = OrderTypeEnum.getByCode(orderInfoBiz.getOrderInfoById(order.getOrderId()).getOrderType());
                if (orderTypeEnum == null) {
                    throw new BizException(BasicCode.UNDEFINED_ERROR, UNDEFINED_ERROR_ORDER_TYPE_MSG);
                }
                OrderCancelDO orderCancelDO = new OrderCancelDO();
                orderCancelDO.setOrderId(order.getOrderId());
                orderCancelDO.setRemarks("支付有效期超时");
                orderCancelDO.setOperator(operator);
                orderCancelDO.setOperatorType(OperatorTypeEnum.PLATFORM.getCode());
                OrderServiceFactory.getOrderHandlerService(
                        orderTypeEnum.getBizType()).cancelOrder(orderCancelDO);
            } catch (Exception e) {
                log.error("处理订单支付有效期失败 {} , {}", order, e);
            }
        });

        return new ItemResult<>("OK");
    }

    @AddLog(operatorIndex = 2)
    @Override
    public ItemResult<String> doSellerCloseOrder(String orderId, String remarks, String operator) {
        OrderTypeEnum orderTypeEnum = OrderTypeEnum.getByCode(orderInfoBiz.getOrderInfoById(orderId).getOrderType());
        if (orderTypeEnum == null) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, UNDEFINED_ERROR_ORDER_TYPE_MSG);
        }
        OrderCloseDO orderCloseDO = new OrderCloseDO();
        orderCloseDO.setOrderId(orderId);
        orderCloseDO.setRemarks(remarks);
        orderCloseDO.setOperator(operator);
        orderCloseDO.setOperatorType(OperatorTypeEnum.SELLER.getCode());
        OrderServiceFactory.getOrderHandlerService(
                orderTypeEnum.getBizType()).closeOrder(orderCloseDO);
        return new ItemResult<>("OK");
    }

    @AddLog(operatorIndex = 2)
    @Override
    public ItemResult<String> doBuyerCloseOrder(String orderId, String remarks, String operator) {
        OrderTypeEnum orderTypeEnum = OrderTypeEnum.getByCode(orderInfoBiz.getOrderInfoById(orderId).getOrderType());
        if (orderTypeEnum == null) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, UNDEFINED_ERROR_ORDER_TYPE_MSG);
        }
        OrderCloseDO orderCloseDO = new OrderCloseDO();
        orderCloseDO.setOrderId(orderId);
        orderCloseDO.setRemarks(remarks);
        orderCloseDO.setOperator(operator);
        orderCloseDO.setOperatorType(OperatorTypeEnum.BUYER.getCode());
        OrderServiceFactory.getOrderHandlerService(
                orderTypeEnum.getBizType()).closeOrder(orderCloseDO);
        return new ItemResult<>("OK");
    }


    @AddLog(operatorIndex = 2)
    @Override
    public ItemResult<String> doPlatformCloseOrder(String orderId, String remarks, String operator) {
        OrderTypeEnum orderTypeEnum = OrderTypeEnum.getByCode(orderInfoBiz.getOrderInfoById(orderId).getOrderType());
        if (orderTypeEnum == null) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, UNDEFINED_ERROR_ORDER_TYPE_MSG);
        }
        OrderCloseDO orderCloseDO = new OrderCloseDO();
        orderCloseDO.setOrderId(orderId);
        orderCloseDO.setRemarks(remarks);
        orderCloseDO.setOperator(operator);
        orderCloseDO.setOperatorType(OperatorTypeEnum.PLATFORM.getCode());
        OrderServiceFactory.getOrderHandlerService(
                orderTypeEnum.getBizType()).closeOrder(orderCloseDO);
        return new ItemResult<>("OK");
    }

    @Override
    public ItemResult<String> getOrderCashierInfo(String orderId) {
        // TODO Auto-generated method stub
        return new ItemResult<>("OK");
    }

    @Override
    public ItemResult<PageInfo<OrderDTO>> pageBuyerOrder(OrderQueryDTO orderQuery, Integer pageSize, Integer pageNum) {
        if (CsStringUtils.isBlank(orderQuery.getBuyerId())) {
            throw new BizException(BasicCode.PARAM_NULL, "买家Id");
        }
        orderQuery.setBuyerDel(false);
        Page<OrderDTO> result = orderQueryService.searchOrderPage(orderQuery, pageSize, pageNum);
        for (OrderDTO orderDTO : result) {
            resolveAdjustByOrderDTOWithList(orderDTO);
        }
        return new ItemResult<>(new PageInfo<>(result));
    }

    @Override
    public ItemResult<PageInfo<OrderDTO>> pageSellerOrder(OrderQueryDTO orderQuery, Integer pageSize, Integer pageNum) {
        if (CsStringUtils.isBlank(orderQuery.getSellerId())) {
            throw new BizException(BasicCode.PARAM_NULL, "卖家Id");
        }
        orderQuery.setSellerDel(false);
        Page<OrderDTO> result = orderQueryService.searchOrderPage(orderQuery, pageSize, pageNum);
        this.batchResolveAdjustByOrderDTO(result.getResult());
        return new ItemResult<>(new PageInfo<>(result));
    }

    /**
     * 批量设置订单调价信息
     * @param orderDTOList
     */
    private void batchResolveAdjustByOrderDTO(List<OrderDTO> orderDTOList) {
        if (CollectionUtils.isEmpty(orderDTOList)){
            return ;
        }
        List<String> orderIds = orderDTOList.stream().map(OrderDTO::getOrderId).toList();
        List<OrderAdjustItemDTO> batchItemDTOS = orderAdjustItemBiz.findByOrderIdList(orderIds);

        Map<String, List<OrderAdjustItemDTO>> orderId2AdjustListMap = batchItemDTOS.stream().collect(Collectors.groupingBy(OrderAdjustItemDTO::getOrderId));
        for (OrderDTO orderDTO : orderDTOList) {
            List<OrderAdjustItemDTO> adjustItemList = orderId2AdjustListMap.get(orderDTO.getOrderId());
            if (CollectionUtils.isNotEmpty(adjustItemList)) {
                for (OrderItemDTO orderItem : orderDTO.getOrderItems()) {
                    Optional<OrderAdjustItemDTO> optional = adjustItemList.stream().filter(item -> item.getOrderItemId().equals(orderItem.getOrderItemId()) &&
                            CsStringUtils.equals(item.getExecuteStatus(), AdjustExecuteStatusEnum.EXECUTED.getCode())).findFirst();
                    orderItem.setAdjust(optional.isPresent());
                    if (optional.isEmpty()) {
                        continue;
                    }
                    orderDTO.setAdjust(true);
                    OrderAdjustItemDTO adjustItemDTO = optional.get();
                    orderItem.setAdjustAmount(adjustItemDTO.getNextPrice());
                }
            }
        }
    }

    @Override
    public ItemResult<PageInfo<OrderDTO>> pagePlatformOrder(OrderQueryDTO orderQuery, Integer pageSize,
                                                            Integer pageNum) {
        Page<OrderDTO> result = orderQueryService.searchOrderPage(orderQuery, pageSize, pageNum);
        for (OrderDTO orderDTO : result) {
            resolveAdjustByOrderDTOWithList(orderDTO);
        }
        return new ItemResult<>(new PageInfo<>(result));
    }

    @Override
    public ItemResult<OrderDTO> getSellerOrderDetail(String orderId, String operator) {
        OrderDTO orderDetail = orderQueryService.getOrderDetail(orderId);
        resolveAdjustByOrderDTO(orderDetail);
        return new ItemResult<>(orderDetail);
    }

    @Override
    public ItemResult<OrderDTO> getBuyerOrderDetail(String orderId, String operator) {
        OrderDTO orderDetail = orderQueryService.getOrderDetail(orderId);
        resolveAdjustByOrderDTO(orderDetail);
        return new ItemResult<>(orderDetail);
    }

    @Override
    public ItemResult<OrderDTO> getPlatformOrderDetail(String orderId, String operator) {
        OrderDTO orderDetail = orderQueryService.getOrderDetail(orderId);
        resolveAdjustByOrderDTO(orderDetail);
        return new ItemResult<>(orderDetail);
    }

    @Override
    public ItemResult<OrderDTO> getOrderDetail(String orderId) {
        OrderDTO orderDetail = orderQueryService.getOrderDetail(orderId);
        resolveAdjustByOrderDTO(orderDetail);
        return new ItemResult<>(orderDetail);
    }

    @Override
    public ItemResult<OrderDTO> getOrderTableInfo(String orderId) {
        return new ItemResult<>(orderQueryService.getOrderTableInfo(orderId));
    }

    @Override
    public ItemResult<OrderDTO> getOrderTableInfoByTakeCode(String takeCode) {
        return new ItemResult<>(orderQueryService.getOrderTableInfoByTakeCode(takeCode));
    }

    @Override
    public ItemResult<List<OrderIdAndContractIdDTO>> getContractIdByOrderIds(Collection<String> orderIds) {
        return new ItemResult<>(orderQueryService.getContractIdByOrderIds(orderIds));
    }

    @Deprecated(since = "2.1.4-RELEASE")
    @AddLog(operatorIndex = 2)
    @Override
    public ItemResult<String> updateOrderTakeStatus(String orderId, OrderTakeStatus orderTakeStatus, String operator) {
        return new ItemResult<>("OK");
    }

    @AddLog(operatorIndex = 2)
    @Override
    public ItemResult<String> updateOrderItemTakeCost(String orderId, Map<String, BigDecimal> itemCostDetails,
                                                      String operator) {
        orderAtomicBiz.updateOrderItemTakeCost(orderId, itemCostDetails, operator);
        return new ItemResult<>("OK");
    }

    @Override
    public ItemResult<OrderCostsDTO> computCosts(OrderDTO orderDTO) {
        return new ItemResult<>(orderComputeService.computeOrderCosts(orderDTO));
    }

    @Override
    public ItemResult<OrderCostsDTO> recomputCosts(OrderRecCostsDTO recCostsDTO) {
        return new ItemResult<>(orderComputeService.reComputeOrderCosts(recCostsDTO));
    }

    @AddLog(operatorIndex = 0,operatorFieldName = "operator")
    @Override
    public ItemResult<String> payTradeCallback(PayCallbackDTO callbackDTO) {
        orderPaymentService.payTradeCallback(callbackDTO);
        return new ItemResult<>("OK");
    }

    @Override
    public ItemResult<OrderDTO> getOrderAndItemInfo(String orderId) {
        return new ItemResult<>(orderQueryService.getOrderInfo(orderId));
    }

    @Deprecated(since = "2.1.4-RELEASE")
    @AddLog(operatorIndex = 3)
    @Override
    public ItemResult<String> underLinePay(String orderId, List<String> pics, String mome, String operator) {
        return new ItemResult<>("OK");
    }

    @AddLog(operatorIndex = 2)
    @Override
    public ItemResult<String> comfireUnderLinePay(OrderUnderLinePayConfirmDTO orderUnderLinePayConfirmDTO) {
        orderPaymentService.confirmUnderLinePay(orderUnderLinePayConfirmDTO);
        return new ItemResult<>("OK");
    }

    @Override
    public ItemResult<OrderDTO> getOrderByTradeBillId(String tradeBillId) {
        return new ItemResult<>(orderQueryService.getOrderByTradeBillId(tradeBillId));
    }

    @Deprecated(since = "2.1.4-RELEASE")
    @AddLog(operatorIndex = 1)
    @Override
    public ItemResult<String> doSellerDeleteOrder(String orderId, String operator) {
        return new ItemResult<>("OK");
    }

    @Deprecated(since = "2.1.4-RELEASE")
    @AddLog(operatorIndex = 1)
    @Override
    public ItemResult<String> doBuyerDeleteOrder(String orderId, String operator) {
        return new ItemResult<>("OK");
    }

    @Deprecated(since = "2.1.4-RELEASE")
    @AddLog(operatorIndex = 1)
    @Override
    public ItemResult<String> doPlatformDeleteOrder(String orderId, String operator) {
        return new ItemResult<>("OK");
    }

    @AddLog(operatorIndex = 2)
    @Override
    public ItemResult<String> updateTakeTimeLimit(String orderId, Date takeTimeLimit, String operator) {
        OrderInfo updOrder = new OrderInfo();
        updOrder.setOrderId(orderId);
        updOrder.setTakeTimeLimit(takeTimeLimit);
        updOrder.setUpdateTime(new Date());
        updOrder.setUpdateUser(operator);
        orderAtomicBiz.updateSelective(updOrder);
        //修改发货单有效期
        takeInfoBiz.updateTakeInfoTakeLimit(orderId, takeTimeLimit, operator);
        OrderDTO orderDTO = orderQueryService.getOrderInfo(orderId);
        //通知ERP修改提货有效期
        if (orderErpBiz.hasErpOrder(orderDTO.getSellerId(), MemberConfigEnum.ERP_ORDER_FLG.getKeyCode())) {
            orderErpBiz.updateTakeEffectiveTime(orderDTO);
        }

        return new ItemResult<>("OK");
    }

    @Override
    public ItemResult<String> getBuyerTradeBillIdByOrderId(String orderId, String sellerId) {
        return new ItemResult<>(orderQueryService.getBuyerTradeBillIdByOrderId(orderId, sellerId));
    }

    @Override
    public ItemResult<BigDecimal> getBuyerResDayQuantity(String buyerId, String resourceId) {
        return new ItemResult<>(orderQueryService.getBuyerResDayQuantity(buyerId, resourceId));
    }

    @Override
    public ItemResult<List<OrderDTO>> getOrderByContractId(String contractId) {
        List<OrderDTO> orderList = Lists.newArrayList();
        OrderInfo query = new OrderInfo();
        query.setDealsId(contractId);
        query.setDelFlg(false);
        List<OrderInfo> orders = orderAtomicBiz.find(query);
        if(CollectionUtils.isNotEmpty(orders)){
            orders.stream().forEach(action -> {
                orderList.add(BeanConvertUtils.convert(action, OrderDTO.class));
            });
        }
        return new ItemResult<>(orderList);
    }

    @Override
    public ItemResult<List<OrderDTO>> getOrderByContractSequence(String contractSequence) {
        List<OrderDTO> orderList = Lists.newArrayList();
        OrderInfo query = new OrderInfo();
        query.setDealsName(contractSequence);
        query.setDelFlg(false);
        List<OrderInfo> orders = orderAtomicBiz.find(query);
        if(CollectionUtils.isNotEmpty(orders)){
            orders.stream().forEach(action -> {
                orderList.add(BeanConvertUtils.convert(action, OrderDTO.class));
            });
        }
        return new ItemResult<>(orderList);
    }

    @Override
    public ItemResult<Integer> countOrders(OrderCountDTO orderCountDTO) {
        return new ItemResult<>(orderQueryService.countOrders(orderCountDTO));
    }

    @AddLog(operatorIndex = 1)
    @Override
    public ItemResult<String> checkRealTimeOrderAmount(String orderId, String operator) {
        orderPaymentService.checkRealTimeOrderAmount(orderId, operator);
        return new ItemResult<>("OK");
    }

    @Override
    public ItemResult<Boolean> updateRealTimeOrderAmount(String orderId, String signType, BigDecimal adjustQuantity, String operator) {
        return new ItemResult<>(Boolean.TRUE);
    }

    @Override
    public void oversoldUpdate(OrderInfo orderInfo, String operator) {
        orderAtomicBiz.oversoldUpdate(orderInfo, operator);
    }

    @Override
    public ItemResult<List<OrderDTO>> getOrderByTakeId(List<String> takeIds) {
        return new ItemResult<>(orderQueryService.getOrderByTakeId(takeIds));
    }

    @Override
    public ItemResult<List<OrderDTO>> getOrderByTakeCodes(List<String> takeCodes) {
        return new ItemResult<>(orderQueryService.getOrderByTakeCodes(takeCodes));
    }

    @Override
    public ItemResult<PageInfo<UninvoicedOrderDTO>> pageUninvoicedOrderList(UninvoicedOrderReqDTO uninvoicedOrderReqDTO) {
        return new ItemResult<>(orderQueryService.pageUninvoicedOrderList(uninvoicedOrderReqDTO));
    }

    @Override
    public ItemResult<ApplyOrderInvoiceResponseDTO> applyOrderInvoice(ApplyOrderInvoiceRequestDTO req, String operator) {
        return new ItemResult<>(orderInvoiceService.applyOrderInvoice(req, operator));
    }

    @Override
    public ItemResult<Boolean> doOrderInvoiceCallBack(OrderInvoiceCallBackDTO callBackDTO) {
        return new ItemResult<>(orderInvoiceService.doOrderInvoiceCallBack(callBackDTO));
    }

    @Override
    public ItemResult<OrderDTO> getOrderDetailByCondition(OrderDetailConditionDTO orderDetailConditionDTO) {
        return new ItemResult<>(orderQueryService.getOrderDetailByCondition(orderDetailConditionDTO));
    }


    @Override
    public ItemResult<List<OrderStatusStatisticsDTO>> orderStatusStatistics(List<String> saleRegionList,String sellerId) {
        return new ItemResult<>(orderQueryService.orderStatusStatistics(saleRegionList, sellerId));
    }

    @Override
    public ItemResult<TransactionsOverviewDTO> transactionsOverview(OverviewReqDTO overviewReqDTO) {
        return new ItemResult<>(orderQueryService.transactionsOverview(overviewReqDTO));
    }

    @Override
    public ItemResult<OrderOverviewDTO> orderOverview(OverviewReqDTO overviewReqDTO) {
        return new ItemResult<>(orderQueryService.orderOverview(overviewReqDTO));
    }

    @Override
    public ItemResult<Boolean> confirmOrderInvoice(ConfirmOrderInvoiceReqDTO reqDTO) {
        return new ItemResult<>(orderInvoiceService.confirmOrderInvoice(reqDTO));
    }

    @Override
    public OrderItemDTO getOrderItemDTOById(String orderItemId) {
        return orderItemQuantityBiz.getOrderItemDTOById(orderItemId);
    }

    private void resolveAdjustByOrderDTOWithList(OrderDTO orderDTO) {
        List<OrderAdjustItemDTO> adjustItemList = orderAdjustItemBiz.findByOrderId(orderDTO.getOrderId());
        if (CollectionUtils.isNotEmpty(adjustItemList)) {
            for (OrderItemDTO orderItem : orderDTO.getOrderItems()) {
                Optional<OrderAdjustItemDTO> optional = adjustItemList.stream().filter(item -> item.getOrderItemId().equals(orderItem.getOrderItemId()) &&
                        CsStringUtils.equals(item.getExecuteStatus(), AdjustExecuteStatusEnum.EXECUTED.getCode())).findFirst();
                orderItem.setAdjust(optional.isPresent());
                if (optional.isEmpty()) {
                    continue;
                }
                orderDTO.setAdjust(true);
                OrderAdjustItemDTO adjustItemDTO = optional.get();
                orderItem.setAdjustAmount(adjustItemDTO.getNextPrice());
            }
        }
    }

    private void resolveAdjustByOrderDTO(OrderDTO orderDTO) {
        List<OrderAdjustItemDTO> adjustItemList = orderAdjustItemBiz.findByOrderId(orderDTO.getOrderId());
        BigDecimal totalPreAdjustAmount = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(adjustItemList)) {
            String adjustStrategyId = null;
            for (OrderItemDTO orderItem : orderDTO.getOrderItems()) {
                Optional<OrderAdjustItemDTO> optional = adjustItemList.stream().filter(item -> item.getOrderItemId().equals(orderItem.getOrderItemId()) &&
                        CsStringUtils.equals(item.getExecuteStatus(), AdjustExecuteStatusEnum.EXECUTED.getCode())).findFirst();
                orderItem.setAdjust(optional.isPresent());
                if (optional.isEmpty()) {
                    totalPreAdjustAmount = totalPreAdjustAmount.add(orderItem.getActualUnitPrice().multiply(orderItem.getItemQuantity()));
                    continue;
                }
                orderDTO.setAdjust(true);
                OrderAdjustItemDTO adjustItemDTO = optional.get();
                orderItem.setAdjustAmount(adjustItemDTO.getNextPrice());
                totalPreAdjustAmount = totalPreAdjustAmount.add(adjustItemDTO.getPrePrice().multiply(adjustItemDTO.getAdjustQuantity()));
                adjustStrategyId = adjustItemDTO.getAdjustStrategyId();
            }
            if (CsStringUtils.isEmpty(adjustStrategyId)) {
                return;
            }
            AdjustStrategyDetailDTO strategyDetail = orderAdjustStrategyBiz.queryAdjustStrategyDetail(adjustStrategyId);
            if (strategyDetail != null && CollectionUtils.isNotEmpty(strategyDetail.getRecordList())) {
                Map<String, BigDecimal> resource2AUPMap = orderDTO.getOrderItems().stream()
                        .filter(item -> CsStringUtils.isNotBlank(item.getResourceId()))
                        .collect(Collectors.toMap(OrderItemDTO::getResourceId, OrderItemDTO::getActualUnitPrice));
                List<AdjustRecordDTO> strategyDetailRecordList = strategyDetail.getRecordList();
                for (AdjustRecordDTO rd : strategyDetailRecordList) {
                    rd.setPreAmount(resource2AUPMap.get(rd.getResourceId()));
                }
            }
            orderDTO.setAdjustStrategyDTO(strategyDetail);
        }
        orderDTO.setTotalPreAdjustAmount(totalPreAdjustAmount);
    }

    @Override
    public ItemResult<List<OrderStatusStatisticDTO>> statisticOrderStatus(OrderQueryDTO orderQuery) {
        return new ItemResult<>(orderQueryService.statisticOrderStatus(orderQuery));
    }

    @Override
    public ItemResult<List<ERPAddressDTO>> queryReceivingAddressMap(List<String> erpUnloadAddressIdList) {
        return new ItemResult<>(orderQueryService.queryReceivingAddressMap(erpUnloadAddressIdList));
    }

    @Override
    public List<PickingBillExportInfoDTO> queryPickingBillExportInfo(Set<String> ids) {
        return orderQueryService.queryPickingBillExportInfo(ids);
    }

    @Override
    public List<TradingFlowExportInfoDTO> queryTradingFlowExportInfo(Set<String> ids) {
        return orderQueryService.queryTradingFlowExportInfo(ids);
    }

    @Override
    public List<OrderSimpleDTO> queryOrderByContractBatchAdjustPriceInfo(OrderSimpleQueryDTO dto) {
        return orderQueryService.queryOrderByContractBatchAdjustPriceInfo(dto);
    }

    @Override
    public void tryAutoCloseOrder(OrderSimpleQueryDTO dto) {
        if( dto == null || CollectionUtils.isEmpty(dto.getList())){
            return;
        }
        threadPoolExecutor.execute(() -> {
            try {
                List<OrderSimpleDTO> orderSimpleDTOS = orderQueryService.queryOrderByContractBatchAdjustPriceInfo(dto);
                if (CollectionUtils.isNotEmpty(orderSimpleDTOS)) {
                    orderSimpleDTOS.forEach(item -> {
                        try {
                            if(!orderInfoExtBiz.transportToolTypeEquals(item.getOrderId(), TransportToolTypeEnum.WATER_TRANSPORT)) {
                                orderCommonService.tryAutoCloseOrder(item.getOrderId(), "调价订单自动关闭退款", TakeCloseType.ADJUST_PRICE_CLOSE);
                            }else{
                                log.info("合同修改不触发关闭船运订单 orderId:{},contractId:{}",item.getOrderId(),item.getDealsId());
                            }
                        } catch (Exception e) {
                            log.error(e.getMessage(), e);
                        }
                    });
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        });
    }

    @Override
    public ItemResult<ERPAddressDTO> queryFlowControlAddress(FlowAddressQueryDTO flowAddressQueryDTO) {
        log.info("queryFlowControlAddress:" + JSON.toJSONString(flowAddressQueryDTO));
        return new ItemResult<>(orderErpAddressBiz.queryFlowControlAddress(flowAddressQueryDTO));
    }

    /**
     * 批量提交代理一级订单
     */
    public ItemResult<OrderDTO> batchSubmitProxyPrimaryOrder(OrderDTO orderDTO, String operator) {
        return null;
    }

    @Override
    public OrderLogisticsResultDTO queryOrderLogisticsTakeInfo(OrderLogisticsQueryDTO orderLogisticsQueryDTO) {
        return orderQueryService.queryOrderLogisticsTakeInfo(orderLogisticsQueryDTO);
    }

    public void validateConcreteOrderParam(CreateConcreteOrderDTO createConcreteOrderDTO) {
        if (CsStringUtils.isBlank(createConcreteOrderDTO.getBuyerId())) {
            throw new BizException(BasicCode.INVALID_PARAM, "买家ID");
        }
        if (CsStringUtils.isBlank(createConcreteOrderDTO.getContractId())) {
            throw new BizException(BasicCode.INVALID_PARAM, "合同Id");
        }
        if (CsStringUtils.isBlank(createConcreteOrderDTO.getAddressId())) {
            throw new BizException(BasicCode.INVALID_PARAM, "地址ID");
        }
        if (CsStringUtils.isBlank(createConcreteOrderDTO.getDeliverWay())) {
            throw new BizException(BasicCode.INVALID_PARAM, "配送方式");
        }
        if (CsStringUtils.isBlank(createConcreteOrderDTO.getContractGoodsId())) {
            throw new BizException(BasicCode.INVALID_PARAM, "合同商品Id");
        }
        if (CsStringUtils.isBlank(createConcreteOrderDTO.getContractGoodsId())) {
            throw new BizException(BasicCode.INVALID_PARAM, "合同商品品种Id");
        }
        if (createConcreteOrderDTO.getQuantity() == null) {
            throw new BizException(BasicCode.INVALID_PARAM, "购买量");
        }
        if (CollectionUtils.isEmpty(createConcreteOrderDTO.getAddItemList())) {
            throw new BizException(BasicCode.INVALID_PARAM, "加价项列表");
        }
    }

    public OrderDTO convertConcreteOrderParam(CreateConcreteOrderDTO createConcreteOrderDTO) {
        //参数验证
        validateConcreteOrderParam(createConcreteOrderDTO);
        //组装订单参数
        OrderDTO orderDTO = new OrderDTO();
        BeanUtils.copyProperties(createConcreteOrderDTO, orderDTO);
        orderDTO.setDealsId(createConcreteOrderDTO.getContractId());
        TrContractGoodsDTO contractGoodsDTO = contractService.getContractGoodsById(createConcreteOrderDTO.getContractGoodsId());
        if (contractGoodsDTO == null) {
            throw new BizException(BasicCode.DATA_NOT_EXIST, "合同商品:" + createConcreteOrderDTO.getContractGoodsId());
        }
        if (CsStringUtils.isBlank(contractGoodsDTO.getOutGoodsAddressId())) {
            throw new BizException(BasicCode.INVALID_PARAM, "合同商品出货仓库");
        }
        if (CsStringUtils.isBlank(contractGoodsDTO.getSaleAreaRealCode())) {
            throw new BizException(BasicCode.INVALID_PARAM, "合同商品销售区域");
        }
        orderDTO.setStoreId(contractGoodsDTO.getOutGoodsAddressId());
        OrderInfoExtDTO orderInfoExtDTO = new OrderInfoExtDTO();
        orderInfoExtDTO.setTransportType(TransportToolTypeEnum.ROAD_TRANSPORT.getCode());
        BeanUtils.copyProperties(createConcreteOrderDTO, orderInfoExtDTO);
        orderDTO.setOrderInfoExtDTO(orderInfoExtDTO);
        if (createConcreteOrderDTO.getOrderTaxinfo() != null) {
            orderDTO.setOrderTaxinfo(Lists.newArrayList(createConcreteOrderDTO.getOrderTaxinfo()));
        }
        //设置商品明细
        OrderItemDTO orderItemDTO = new OrderItemDTO();
        orderItemDTO.setContractGoodsId(createConcreteOrderDTO.getContractGoodsId());
        orderItemDTO.setGoodsId(createConcreteOrderDTO.getGoodsId());
        orderItemDTO.setItemQuantity(createConcreteOrderDTO.getQuantity());
        orderItemDTO.setUnits(createConcreteOrderDTO.getUnit());
        orderItemDTO.setUnitsName(createConcreteOrderDTO.getUnit());
        orderItemDTO.setMachineRuleId(createConcreteOrderDTO.getMachineRuleId());
        orderItemDTO.setEmptyRuleId(createConcreteOrderDTO.getEmptyRuleId());
        List<OrderItemAddDTO> orderItemAddList = Lists.newArrayList();
        createConcreteOrderDTO.getAddItemList().stream().forEach(addItemDO -> {
            OrderItemAddDTO itemAdd = BeanConvertUtils.convert(addItemDO, OrderItemAddDTO.class);
            orderItemAddList.add(itemAdd);
        });
        orderItemDTO.setOrderItemAdds(orderItemAddList);
        orderDTO.setOrderItems(Lists.newArrayList(orderItemDTO));
        orderDTO.setOrderType(OrderTypeEnum.CONTRACT.getCode());

        return orderDTO;
    }

    @Override
    public OrderDTO createConcreteOrder(CreateConcreteOrderDTO createConcreteOrderDTO) {
        log.info("createConcreteOrder:" + JSON.toJSONString(createConcreteOrderDTO));
        if (CsStringUtils.isBlank(createConcreteOrderDTO.getSigner())) {
            throw new BizException(BasicCode.INVALID_PARAM, "签收人");
        }
        if (CsStringUtils.isBlank(createConcreteOrderDTO.getSignerPhone())) {
            throw new BizException(BasicCode.INVALID_PARAM, "签收人电话");
        }
        if (CsStringUtils.isBlank(createConcreteOrderDTO.getConstructPlace())) {
            throw new BizException(BasicCode.INVALID_PARAM, "施工部位");
        }
        if (CsStringUtils.isBlank(createConcreteOrderDTO.getOperator())) {
            throw new BizException(BasicCode.INVALID_PARAM, "操作人");
        }
        if (CsStringUtils.isBlank(createConcreteOrderDTO.getOperatorName())) {
            throw new BizException(BasicCode.INVALID_PARAM, "操作人名称");
        }
        OrderDTO orderDTO = convertConcreteOrderParam(createConcreteOrderDTO);
        return OrderServiceFactory.getOrderHandlerService(
                OrderTypeEnum.CONTRACT.getBizType()).createOrder(orderDTO,
                createConcreteOrderDTO.getOperator());
    }

    @Override
    public OrderCostsDTO computeConcreteOrderCosts(CreateConcreteOrderDTO createConcreteOrderDTO) {
        log.info("computeConcreteOrderCosts:" + JSON.toJSONString(createConcreteOrderDTO));
        OrderDTO orderDTO = convertConcreteOrderParam(createConcreteOrderDTO);
        //计算单价
        return orderComputeService.computeOrderCosts(orderDTO);
    }

    @Override
    public OrderBillCheckDTO queryBillCheckInfo(OrderBillCheckQueryDTO query) {
        return orderQueryService.queryBillCheckInfo(query);
    }

    /**
     * 触发推送指定订单或者所有订单的台班费给对账使用
     */
    public void floorTruckageNotifyAll(List<String> orderIds){
        //OrderTypeEnum 随意
        OrderServiceFactory.getOrderHandlerService(OrderTypeEnum.LISTING.getBizType()).floorTruckageNotifyAll(orderIds);
    }

    @Override
    public RepurchaseOrderDTO repurchaseOrder(String orderId) {
        return orderQueryService.repurchaseOrder(orderId);
    }

    @Override
    public Boolean sendMemberIntegralMqMessage(List<String> orderIdList) {
        synchronizeIntegralHandler.sendMemberIntegralMqMessage(orderIdList);
        return true;
    }

	@Override
	public List<String> queryAddressDetailByMemberId(OrderQueryDTO queryDTO) {
		return orderQueryService.queryAddressDetailByMemberId(queryDTO);
	}
}
