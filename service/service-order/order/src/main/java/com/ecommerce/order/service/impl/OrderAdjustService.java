package com.ecommerce.order.service.impl;

import com.alibaba.fastjson.JSON;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.service.common.BaseBiz;
import com.ecommerce.common.utils.ArithUtils;
import com.ecommerce.order.api.dto.*;
import com.ecommerce.order.api.enums.PayStatusEnum;
import com.ecommerce.order.api.enums.PayTypeEnum;
import com.ecommerce.order.api.enums.RefundStatusEnum;
import com.ecommerce.order.api.enums.TakeStatus;
import com.ecommerce.order.biz.IOrderAdjustBiz;
import com.ecommerce.order.biz.IOrderOverSellBiz;
import com.ecommerce.order.biz.IOrderPayinfoJoinBiz;
import com.ecommerce.order.biz.fsm.IOrderPayinfoBiz;
import com.ecommerce.order.biz.fsm.ITakeInfoBiz;
import com.ecommerce.order.biz.impl.BaseOrderSearchBiz;
import com.ecommerce.order.dao.dto.OrderCompleteDO;
import com.ecommerce.order.dao.dto.PaymentAmountDO;
import com.ecommerce.order.dao.dto.PaymentAmountQueryDO;
import com.ecommerce.order.dao.dto.SupplementPaymentDO;
import com.ecommerce.order.dao.vo.OrderAdjust;
import com.ecommerce.order.dao.vo.OrderInfo;
import com.ecommerce.order.dao.vo.OrderPayinfo;
import com.ecommerce.order.fsm.service.IOrderPaymentService;
import com.ecommerce.order.fsm.service.IOrderQueryService;
import com.ecommerce.order.fsm.service.OrderServiceFactory;
import com.ecommerce.order.fsm.status.OrderTypeEnum;
import com.ecommerce.order.service.IOrderAdjustService;
import com.ecommerce.order.service.IOrderPayinfoService;
import com.ecommerce.pay.api.v2.enums.ChannelTypeEnum;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.ecommerce.common.utils.CsStringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class OrderAdjustService implements IOrderAdjustService {

    private final IOrderAdjustBiz orderAdjustBiz;
    private final ITakeInfoBiz takeInfoBiz;
    private final BaseOrderSearchBiz baseOrderSearchBiz;
    private final IOrderPayinfoJoinBiz orderPayinfoJoinBiz;
    private final IOrderOverSellBiz orderOverSellBiz;
    private final IOrderPayinfoBiz orderPayinfoBiz;
    private final IOrderPayinfoService orderPayinfoService;
    private final IOrderPaymentService orderPaymentService;
    private final IOrderQueryService orderQueryService;

    @Override
    public ItemResult<PageInfo<OrderAdjustDTO>> pageOrderAdjustList(PageOrderAdjustDTO pageOrderAdjustDTO) {
        if (!CsStringUtils.isNotBlank(pageOrderAdjustDTO.getOrderId())) {
            throw new BizException(BasicCode.PARAM_NULL,"订单ID");
        }
        PageInfo<OrderAdjustDTO> pageInfo = new PageInfo<>();
        PageInfo<OrderAdjust> result = orderAdjustBiz.pageOrderAdjustList(pageOrderAdjustDTO);
        OrderInfo orderInfo=baseOrderSearchBiz.get(pageOrderAdjustDTO.getOrderId());
        if(orderInfo==null){
            throw new BizException(BasicCode.DATA_NOT_EXIST,"订单:"+pageOrderAdjustDTO.getOrderId());
        }
        String orderCode=orderInfo.getOrderCode();
        if( result != null ){
            BeanUtils.copyProperties(result,pageInfo);
            if( result.getList() != null && !result.getList().isEmpty() ) {
                List<OrderAdjustDTO> dtoList=result.getList().stream().map(this::vo2OrderAdjustDTO).toList();
                dtoList.forEach(item->item.setOrderCode(orderCode));
                pageInfo.setList(dtoList);
            }
        }
        return new ItemResult<>(pageInfo);
    }

    @Override
    public ItemResult<Boolean> doCreateOrderAdjust(OrderAdjustDTO orderAdjustDTO) {
        orderAdjustBiz.doCreateOrderAdjust(dto2OrderAdjustVo(orderAdjustDTO));
        OrderInfo orderInfo = baseOrderSearchBiz.get(orderAdjustDTO.getOrderId());
        orderInfo.setRealtimeOrderAmount(orderInfo.getRealtimeOrderAmount() == null ? orderAdjustDTO.getAdjustMoney() :
                orderInfo.getRealtimeOrderAmount().add(orderAdjustDTO.getAdjustMoney()));
        orderInfo.setRealtimeOthersAmount(orderInfo.getRealtimeOthersAmount() == null ? orderAdjustDTO.getAdjustMoney() :
                orderInfo.getRealtimeOthersAmount().add(orderAdjustDTO.getAdjustMoney()));
        BaseBiz.setOperInfo(orderInfo, orderAdjustDTO.getUpdateUser(), false);
        //更新订单的实际其他费用
        baseOrderSearchBiz.updateSelective(orderInfo);

        return new ItemResult<>(Boolean.TRUE);
    }

    @Override
    public ItemResult<BigDecimal> countAdjustAmount(String orderId) {
        return new ItemResult<>(orderAdjustBiz.countAdjustAmount(orderId));
    }

    @Override
    public ItemResult<Boolean> confirmOrderAdjust(String orderId, String operator) {
        log.info("confirmOrderAdjust orderId: {}",orderId);
        if (CsStringUtils.isEmpty(orderId)) {
           throw new BizException(BasicCode.INVALID_PARAM, "订单ID:" + orderId);
        }
        //获取订单明细信息
        OrderDTO order = orderQueryService.getOrderInfo(orderId);
        if (order == null) {
            throw new BizException(BasicCode.DATA_NOT_EXIST, "订单:" + orderId);
        }
        //判断订单下所有的发货单是否处于最终状态
        checkTakeStatus(orderId);
        setRealtimeResourceAmount(order);
        //获取当前订单已支付金额
        PaymentAmountQueryDO paymentAmountQueryDO = new PaymentAmountQueryDO();
        paymentAmountQueryDO.setOrderId(orderId);
        paymentAmountQueryDO.setPayStatusList(Lists.newArrayList(
                PayStatusEnum.IN_PAYMENT.code(),
                PayStatusEnum.COMPLETED.code()
        ));
        PaymentAmountDO paymentAmountDO = orderPaymentService.computeOrderPaymentAmount(paymentAmountQueryDO);

        /// 每完成一个运单，就会请求一次完成发货单，故有超发的时候补款金额计算有误
        // 订单待支付的补款金额
        BigDecimal unpayAmount = orderPayinfoBiz.calcOrderSupplementWaitPaymentAmount(orderId);


        //计算当前的订单实际发生金额进行退补款
        BigDecimal overOrderAmount = order.getRealtimeOrderAmount().subtract(paymentAmountDO.getPaymentTotalAmount()).subtract(unpayAmount);
        BigDecimal overResourceAmount = order.getRealtimeResourceAmount().subtract(paymentAmountDO.getPaymentGoodsAmount());
        BigDecimal overLogisticsAmount = order.getRealtimeLogisticAmount().subtract(paymentAmountDO.getPaymentLogisticsAmount());
        BigDecimal overOthersAmount = order.getRealtimeOthersAmount().subtract(paymentAmountDO.getPaymentOtherAmount());

        log.info("overOrderAmount: {} =  order.getRealtimeOrderAmount(): {}  - paymentAmountDO.getPaymentTotalAmount(): {} - unpayAmount: {}",overOrderAmount,order.getRealtimeOrderAmount(),paymentAmountDO.getPaymentTotalAmount(), unpayAmount);
        log.info("overResourceAmount: {} =  order.getRealtimeResourceAmount(): {}  - paymentAmountDO.getPaymentGoodsAmount(): {}",overResourceAmount,order.getRealtimeResourceAmount(),paymentAmountDO.getPaymentGoodsAmount());
        log.info("overLogisticsAmount: {} =  order.getRealtimeLogisticAmount(): {}  - paymentAmountDO.getPaymentLogisticsAmount(): {}",overLogisticsAmount,order.getRealtimeLogisticAmount(),paymentAmountDO.getPaymentLogisticsAmount());
        log.info("overOthersAmount: {} =  order.getRealtimeOthersAmount(): {}  - paymentAmountDO.getPaymentOtherAmount(): {}",overOthersAmount,order.getRealtimeOthersAmount(),paymentAmountDO.getPaymentOtherAmount());

        //订单发生金额大于已支付金额#补款
        if (overOrderAmount.compareTo(BigDecimal.ZERO) > 0) {
            SupplementPaymentDO supplementPaymentDO = new SupplementPaymentDO();
            supplementPaymentDO.setOrder(order);
            supplementPaymentDO.setOperator(operator);
            //差额转移全部设置为其他费用补款
            setOverResourceAmount(overResourceAmount, overLogisticsAmount, overOthersAmount, supplementPaymentDO, overOrderAmount);
            //支付单备注无法计算量和价格 2021.4.22
            OrderItemDTO orderItem = getOrderItemDTO(orderId, order, paymentAmountDO, supplementPaymentDO);

            if( order.getOrderInfoExtDTO().getLubricityQuantity() != null &&  order.getOrderInfoExtDTO().getLubricityQuantity().compareTo(BigDecimal.ZERO) > 0 ) {
                OrderPayInfoInstructionsDTO desc2 = new OrderPayInfoInstructionsDTO();
                desc2.setGoodsName("润管砂浆");
                desc2.setUnits(orderItem == null ? null : orderItem.getMeasureUnits());
                desc2.setGoodsQuantity(order.getOrderInfoExtDTO().getLubricitySignQuantity());
                desc2.setGoodsPrice(ArithUtils.add(order.getOrderInfoExtDTO().getLubricityPrice(),orderItem == null ? BigDecimal.ZERO : orderItem.getAdditemPrice()));
                desc2.setGoodsAmount(ArithUtils.multiply(order.getOrderInfoExtDTO().getLubricitySignQuantity(),desc2.getGoodsPrice()));
                desc2.setTradeAmount(desc2.getGoodsAmount());
                desc2.setActualAmount(desc2.getGoodsAmount());
                desc2.setActualQuantity(desc2.getGoodsQuantity());
                desc2.setTradePrice(desc2.getGoodsPrice());
                supplementPaymentDO.getOrderPayInfoInstructionsDTOList().add(desc2);
            }

            OrderPayinfo oversoldPayInfo = orderPayinfoJoinBiz.createOversoldPayInfo(supplementPaymentDO);
            //如果是授信支付或ERP余额，则尝试自动支付
            autoPayLogic(operator, order, oversoldPayInfo);
        } else if (overOrderAmount.compareTo(BigDecimal.ZERO) < 0) {
            //订单发生金额小于已支付金额#退款
            OrderRefundDTO orderRefundDTO = new OrderRefundDTO();
            orderRefundDTO.setOrderId(orderId);
            orderRefundDTO.setRefundType(PayTypeEnum.REFUND.getCode());
            orderRefundDTO.setRefundStatus(RefundStatusEnum.WAIT_PAYMENT.code());
            orderRefundDTO.setPayInfoWay(paymentAmountDO.getPayInfoWay());
            //退款金额
            orderRefundDTO.setAmountPrice(overOrderAmount.abs());
            orderRefundDTO.setRealtimeResourceAmount(order.getRealtimeResourceAmount());
            orderRefundDTO.setRealtimeLogisticAmount(order.getRealtimeLogisticAmount());
            orderRefundDTO.setRealtimeOthersAmount(order.getRealtimeOthersAmount());
            orderRefundDTO.setRealtimeCarryAmount(BigDecimal.ZERO);
            orderRefundDTO.setResourcePrice(overResourceAmount.negate());
            orderRefundDTO.setLogisticPrice(overLogisticsAmount.negate());
            orderRefundDTO.setOtherPrice(overOthersAmount.negate());
            //空载费问题修改  退款未剔除空载费问题导致了多退款的问题修复 参考OrderPaymentService.generateOrderRefundInfo
            setOtherPrice(orderRefundDTO);
            orderRefundDTO.setMome("确认订单退款");
            orderRefundDTO.setCloseReason("确认订单退款");
            orderRefundDTO.setOrderInfo(order);
            orderRefundDTO.setCreateUser(operator);
            orderRefundDTO.setUpdateUser(operator);
            //确认退款
            orderPayinfoService.confirmAdjustRefund(orderRefundDTO);
        } else {
            //结算金额相等直接完成订单
            completeOrder(orderId, operator, order);
        }

        return new ItemResult<>(orderAdjustBiz.confirmOrderAdjust(orderId,operator));
    }

    private static void setOtherPrice(OrderRefundDTO orderRefundDTO) {
        if (orderRefundDTO.getResourcePrice().compareTo(BigDecimal.ZERO) > 0 && orderRefundDTO.getOtherPrice().compareTo(BigDecimal.ZERO) < 0) {
            orderRefundDTO.setResourcePrice(ArithUtils.add(orderRefundDTO.getResourcePrice(), orderRefundDTO.getOtherPrice()));//减去负数部分
            if (orderRefundDTO.getResourcePrice().compareTo(BigDecimal.ZERO) >= 0) {
                orderRefundDTO.setOtherPrice(BigDecimal.ZERO);
            } else {
                orderRefundDTO.setOtherPrice(ArithUtils.add(orderRefundDTO.getResourcePrice(), orderRefundDTO.getOtherPrice().negate()));
            }
        }
    }

    @Nullable
    private OrderItemDTO getOrderItemDTO(String orderId, OrderDTO order, PaymentAmountDO paymentAmountDO, SupplementPaymentDO supplementPaymentDO) {
        OrderItemDTO orderItem = null;
        if( !CollectionUtils.isEmpty(order.getOrderItems()) ){
            orderItem = order.getOrderItems().get(0);
            log.info("orderItem is not null.");
        }
        OrderPayInfoInstructionsDTO desc = new OrderPayInfoInstructionsDTO();
        desc.setGoodsName(orderItem == null ? null : orderItem.getGoodsName());
        desc.setUnits(orderItem == null ? null : orderItem.getMeasureUnits());
        desc.setGoodsQuantity(orderItem == null ? null : orderItem.getItemSignQuantity());
        desc.setGoodsPrice(orderItem == null ? null : orderItem.getActualUnitPrice());
        desc.setGoodsAmount(order.getActualResourceAmount());
        desc.setPayTotalAmount(paymentAmountDO.getPaymentTotalAmount());//补款前总支付金额 当前订单总支付金额
        desc.setRealtimeOrderAmount(order.getRealtimeOrderAmount());//补款前总成交金额  当前订单总执行金额
        desc.setActualAmount(order.getActualResourceAmount());
        desc.setActualQuantity(desc.getGoodsQuantity());
        desc.setTradePrice(desc.getGoodsPrice());
        desc.setAdjustAmount(orderAdjustBiz.countAdjustAmount(orderId));//人工调整费用
        supplementPaymentDO.setOrderPayInfoInstructionsDTOList(Lists.newArrayList(desc));
        return orderItem;
    }

    private static void setOverResourceAmount(BigDecimal overResourceAmount, BigDecimal overLogisticsAmount, BigDecimal overOthersAmount, SupplementPaymentDO supplementPaymentDO, BigDecimal overOrderAmount) {
        if (overResourceAmount.compareTo(BigDecimal.ZERO) < 0 ||
                overLogisticsAmount.compareTo(BigDecimal.ZERO) < 0 ||
                overOthersAmount.compareTo(BigDecimal.ZERO) < 0) {
            supplementPaymentDO.setOverResourceAmount(BigDecimal.ZERO);
            supplementPaymentDO.setOverLogisticsAmount(BigDecimal.ZERO);
            supplementPaymentDO.setOverOthersAmount(overOrderAmount);
        } else {
            supplementPaymentDO.setOverResourceAmount(overResourceAmount);
            supplementPaymentDO.setOverLogisticsAmount(overLogisticsAmount);
            supplementPaymentDO.setOverOthersAmount(overOthersAmount);
        }
    }

    private void checkTakeStatus(String orderId) {
        List<TakeInfoDTO> takeInfoList = takeInfoBiz.getTakeInfoDetailsByOrderId(orderId);
        log.info("triggerOrderAutoEnd_takeInfoList:" + JSON.toJSONString(takeInfoList));
        Set<String> finalStatusSet = Sets.newHashSet(
                TakeStatus.CLOSED.getCode(),
                TakeStatus.CANCELED.getCode(),
                TakeStatus.FINISHED.getCode());
        for (TakeInfoDTO takeInfo : takeInfoList) {
            if (!finalStatusSet.contains(takeInfo.getTakeStatus())){
                throw new BizException(BasicCode.UNDEFINED_ERROR, "请等待订单下所有发货单结束后再确认订单！");
            }
        }
    }

    private static void setRealtimeResourceAmount(OrderDTO order) {
        if (order.getRealtimeOrderAmount() == null) {
            order.setRealtimeOrderAmount(BigDecimal.ZERO);
        }
        if (order.getRealtimeResourceAmount() == null) {
            order.setRealtimeResourceAmount(BigDecimal.ZERO);
        }
        if (order.getRealtimeLogisticAmount() == null) {
            order.setRealtimeLogisticAmount(BigDecimal.ZERO);
        }
        if (order.getRealtimeOthersAmount() == null) {
            order.setRealtimeOthersAmount(BigDecimal.ZERO);
        }
    }


    private void autoPayLogic(String operator, OrderDTO order, OrderPayinfo oversoldPayInfo) {
        if(oversoldPayInfo != null && (ChannelTypeEnum.CREDIT.getCode().equals(oversoldPayInfo.getPayinfoWay())
                        || ChannelTypeEnum.ERP.getCode().equals(oversoldPayInfo.getPayinfoWay()))) {
            boolean autoPayResult = orderOverSellBiz.autoPay(oversoldPayInfo, operator);
            log.info("尝试自动支付 orderId:{},payInfoId:{},autoPayResult:{}", order.getOrderCode(), oversoldPayInfo.getPayinfoId(),autoPayResult);
            if( autoPayResult ){
                //修改支付单信息
                OrderPayinfo updatePayInfo = new OrderPayinfo();
                updatePayInfo.setPayinfoId(oversoldPayInfo.getPayinfoId());
                updatePayInfo.setTradeBillId(oversoldPayInfo.getTradeBillId());
                updatePayInfo.setTradeBillNo(oversoldPayInfo.getTradeBillNo());
                updatePayInfo.setPayDescription(oversoldPayInfo.getPayDescription());
                orderPayinfoBiz.updateSelective(updatePayInfo);
                //模拟支付回调
                PayCallbackDTO payCallbackDTO = new PayCallbackDTO();
                payCallbackDTO.setSucess(true);
                payCallbackDTO.setObjectId(oversoldPayInfo.getObjectId());
                payCallbackDTO.setPayinfoId(oversoldPayInfo.getPayinfoId());
                payCallbackDTO.setActualAmount(oversoldPayInfo.getPayAmount());
                payCallbackDTO.setTradeBillId(oversoldPayInfo.getTradeBillId());
                payCallbackDTO.setTradeBillNo(oversoldPayInfo.getTradeBillNo());
                payCallbackDTO.setPayWay(oversoldPayInfo.getPayinfoWay());
                orderPaymentService.payTradeCallback(payCallbackDTO);
            }
        }
    }


    private static void completeOrder(String orderId, String operator, OrderDTO order) {
        OrderTypeEnum orderTypeEnum = OrderTypeEnum.getByCode(order.getOrderType());
        if (orderTypeEnum == null) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "无效的订单类型！");
        }
        OrderCompleteDO orderCompleteDO = new OrderCompleteDO();
        orderCompleteDO.setOrderId(orderId);
        orderCompleteDO.setOperator(operator);
        OrderServiceFactory.getOrderHandlerService(
                orderTypeEnum.getBizType()).completeOrder(orderCompleteDO);
    }

    private OrderAdjustDTO vo2OrderAdjustDTO(OrderAdjust orderAdjust){
        if(orderAdjust == null){
            return null;
        }
        OrderAdjustDTO dto = new OrderAdjustDTO();
        BeanUtils.copyProperties(orderAdjust,dto);
        return dto;
    }

    private OrderAdjust dto2OrderAdjustVo(Object dto){
        OrderAdjust vo = new OrderAdjust();
        if( dto != null ){
            BeanUtils.copyProperties(dto,vo);
        }
        return vo;
    }
}
