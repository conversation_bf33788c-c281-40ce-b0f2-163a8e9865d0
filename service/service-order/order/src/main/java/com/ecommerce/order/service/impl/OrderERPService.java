package com.ecommerce.order.service.impl;

import com.ecommerce.common.annotation.AddLog;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.open.api.dto.apicenter.order.ERPOrderAdjustDTO;
import com.ecommerce.open.api.dto.apicenter.order.ERPOverSoldVerifyResponseDTO;
import com.ecommerce.order.api.dto.erp.ERPOverSoldDTO;
import com.ecommerce.order.api.dto.erp.OrderCloseERPCallback;
import com.ecommerce.order.api.dto.erp.OrderCompleteERPCallback;
import com.ecommerce.order.api.dto.erp.OrderCreateERPCallback;
import com.ecommerce.order.biz.IOrderErpBiz;
import com.ecommerce.order.service.IOrderERPService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class OrderERPService implements IOrderERPService {

	private static final String ERROR = "ERROR";

	private final IOrderErpBiz orderErpBiz;

	@Override
	public ItemResult<String> doErpOrderCreate(String orderId) {
		orderErpBiz.orderCreateToErp(orderId, null);
		return new ItemResult<>("true");
	}

	@Override
	public ItemResult<String> doErpOrderClose(String orderId) {
		orderErpBiz.orderCloseToErp(orderId);
		return new ItemResult<>("true");
	}

	@Override
	public ItemResult<String> doErpOrderComplete(String orderId) {
		orderErpBiz.orderCompleteToErp(orderId);
		return new ItemResult<>("true");
	}

	@Override
	public ItemResult<String> doERPCloseOrder(OrderCloseERPCallback orderCloseERPCallback) {
		try {
			orderErpBiz.erpCloseOrder(orderCloseERPCallback);
			return new ItemResult<>("OK");
		} catch (Exception e) {
			log.error("ERP close order fail:{}",e);
			return new ItemResult<>("01",ERROR);
		}
	}

	@Override
	public ItemResult<String> doERPCompleteOrder(OrderCompleteERPCallback orderCompleteERPCallback) {
		try {
			boolean result = orderErpBiz.erpCompleteOrder(orderCompleteERPCallback);
			if(result){
				return new ItemResult<>("OK");
			}else{
				return new ItemResult<>("01","订单不符合三清条件");
			}

		} catch (Exception e) {
			log.error("ERP close order fail:{}",e);
			return new ItemResult<>("01",ERROR);
		}
	}

	@AddLog(operatorIndex = 1)
	@Override
	public ItemResult<String> doErpOrderCreateCallback(OrderCreateERPCallback orderCreateERPCallback,String operator) {
		try{
			orderErpBiz.orderCreateERPCallback(orderCreateERPCallback, operator);
		}catch (Exception e){
			log.error("ec order confirm fail:{}",e);
			return new ItemResult<>("01",ERROR);
		}
		return new ItemResult<>("OK");
	}
	@AddLog(operatorIndex = 1)
	@Override
	public ItemResult<String> doErpOrderCloseCallback(OrderCloseERPCallback orderCloseERPCallback, String operator){
		try{
			orderErpBiz.orderCloseERPCallback(orderCloseERPCallback,operator);
		}catch (Exception e){
			log.error("ec order close confirm fail:{}",e);
			return new ItemResult<>("01",ERROR);
		}

		return new ItemResult<>("OK");
	}
	@AddLog(operatorIndex = 1)
	@Override
	public ItemResult<String> doErpOrderCompleteCallback(OrderCompleteERPCallback orderCompleteERPCallback, String operator) {
		try{
			orderErpBiz.orderCompleteERPCallback(orderCompleteERPCallback,operator);
		}catch (Exception e){
			log.error("ec order confirm fail:{}",e);
			return new ItemResult<>("01",ERROR);
		}
		return new ItemResult<>("OK");
	}

	@Override
	public ItemResult<ERPOverSoldVerifyResponseDTO>  overSellConfirm(ERPOverSoldDTO erpOverSoldDTO) {
		return new ItemResult<>(orderErpBiz.overSellConfirm(erpOverSoldDTO));
	}

	@Override
	public ItemResult<String> doERPOrderAdjust(List<ERPOrderAdjustDTO> erpOrderAdjustDTOList) {
		try{
			orderErpBiz.doERPOrderAdjust(erpOrderAdjustDTOList);
		}catch (Exception e){
			log.error("ec order adjust fail:{}",e);
			return new ItemResult<>("01", ERROR);
		}
		return new ItemResult<>("OK");
	}
}
