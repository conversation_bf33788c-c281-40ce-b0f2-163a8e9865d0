package com.ecommerce.order.service.impl;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.order.api.dto.export.OrderExportCondDTO;
import com.ecommerce.order.api.dto.export.OrderExportDTO;
import com.ecommerce.order.api.dto.export.OrderItemOption;
import com.ecommerce.order.biz.IOrderExportBiz;
import com.ecommerce.order.service.IOrderExportService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * @Auther: colu
 * @Date: 2020-06-09 17:03
 * @Description: OrderExportService
 */
@Slf4j
@Service("orderExportService")
@RequiredArgsConstructor
public class OrderExportService implements IOrderExportService {

    private final IOrderExportBiz orderExportBiz;

    @Override
    public ItemResult<List<OrderExportDTO>> exportOrderList(OrderExportCondDTO orderExportCondDTO) {
        return new ItemResult<>(orderExportBiz.exportOrderList(orderExportCondDTO));
    }

    @Override
    public ItemResult<List<OrderItemOption>> queryOrderItemOptionsByItemIdSet(Set<String> orderItemIdSet) {
        return new ItemResult<>(orderExportBiz.queryOrderItemOptionsByItemIdSet(orderItemIdSet));
    }
}
