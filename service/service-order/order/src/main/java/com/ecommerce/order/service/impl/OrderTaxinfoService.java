package com.ecommerce.order.service.impl;

import com.ecommerce.order.api.dto.OrderTaxinfoDTO;
import com.ecommerce.order.api.dto.OrderTaxinfoReqDTO;
import com.ecommerce.order.biz.IOrderTaxinfoBiz;
import com.ecommerce.order.service.IOrderTaxinfoService;
import com.github.pagehelper.PageInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class OrderTaxinfoService implements IOrderTaxinfoService {
    private final IOrderTaxinfoBiz orderTaxinfoBiz;
    @Override
    public PageInfo<OrderTaxinfoDTO> pageOrderTaxinfo(OrderTaxinfoReqDTO req) {
        return orderTaxinfoBiz.pageOrderTaxinfo(req);
    }
}
