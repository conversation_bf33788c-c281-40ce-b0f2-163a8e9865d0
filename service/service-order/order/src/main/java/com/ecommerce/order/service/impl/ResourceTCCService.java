package com.ecommerce.order.service.impl;

import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.service.common.uuid.UUIDGenerator;
import com.ecommerce.goods.api.service.IResourceNumTCCService;
import com.ecommerce.order.api.dto.OrderDTO;
import com.ecommerce.order.api.dto.OrderItemDTO;
import com.ecommerce.order.api.enums.OrderTypeEnum;
import com.ecommerce.order.biz.impl.BaseOrderSearchBiz;
import com.ecommerce.order.service.IResourceTCCService;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;

/**
 * <AUTHOR>
 * @created 14:34 23/09/2019
 * @description
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ResourceTCCService implements IResourceTCCService {

    private final UUIDGenerator uuidGenerator;

    private final IResourceNumTCCService resourceNumTCCService;

    private final BaseOrderSearchBiz baseOrderSearchBiz;

    @Override
    public void returnResourceNum(String orderId, String operator) {
        String uid = this.uuidGenerator.gain();

        OrderDTO order = baseOrderSearchBiz.getOrderInfo(orderId);

        String orderType = order.getOrderType();
        boolean flag = !OrderTypeEnum.CONTRACT.getCode().equals(orderType);

        log.info("returnResourceNum:{},{},{}", orderId, operator, uid);
        this.tryResourceNumOTR(order, operator, uid, flag);
        log.info("returnResourceNum_tryResourceNumOTR:{},{},{}", orderId, operator, uid);

        try{
            this.confirmResourceNumOTR(orderId, operator, uid, flag);
            log.info("returnResourceNum_confirmResourceNumOTR:{},{},{}", orderId, operator, uid);
        }catch(Exception e){
            this.cancelResourceNumOTR(orderId, operator, uid, flag);
            log.info("returnResourceNum_cancelResourceNumOTR:{},{},{}", orderId, operator, uid);
            throw e;
        }
    }


    /**
     * 锁定返还资源数量
     */
    private void tryResourceNumOTR(OrderDTO order, String operator, String uid, boolean flag){
        try{
            if(flag){
                HashMap<String, BigDecimal> map = Maps.newHashMap();
                for(OrderItemDTO item : order.getOrderItems()){
                    BigDecimal rate = BigDecimal.ONE;
                    if(item.getConvertRate() != null){
                        rate = item.getConvertRate();
                    }
                    map.put(item.getResourceId(), item.getItemQuantity().multiply(rate).setScale(2, RoundingMode.HALF_UP));
                }
                resourceNumTCCService.tryResourceNumOrderToResource(map, order.getOrderId(), operator, uid);
            }
        }catch(Exception e){
            throw new BizException(BasicCode.UNDEFINED_ERROR, "锁定返还资源数量失败：" + e.getMessage());
        }
    }

    /**
     * 确定返还资源数量
     */
    private void confirmResourceNumOTR(String orderId, String operator, String uid, boolean flag){
        try{
            if(flag){
                resourceNumTCCService.confirmResourceNumOrderToResource(orderId, operator, uid);
            }
        }catch(Exception e){
            throw new BizException(BasicCode.UNDEFINED_ERROR, "确定返还资源数量失败：" + e.getMessage());
        }
    }


    /**
     * 解锁返还资源数量
     */
    private void cancelResourceNumOTR(String orderId, String operator, String uid, boolean flag){
        try{
            if(flag){
                resourceNumTCCService.cancelResourceNumOrderToResource(orderId, operator, uid);
            }
        }catch(Exception e){
            throw new BizException(BasicCode.UNDEFINED_ERROR, "无法解锁返还资源数量：" + e.getMessage());
        }
    }

}
