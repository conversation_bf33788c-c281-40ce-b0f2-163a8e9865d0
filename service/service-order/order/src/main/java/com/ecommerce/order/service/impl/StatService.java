package com.ecommerce.order.service.impl;

import com.alibaba.fastjson.JSON;
import com.ecommerce.order.api.dto.stat.OrderSaleStatDTO;
import com.ecommerce.order.dao.mapper.OrderInfoMapper;
import com.ecommerce.order.service.IStatService;
import lombok.RequiredArgsConstructor;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.concurrent.TimeUnit;

@Service
@RequiredArgsConstructor
public class StatService implements IStatService
{
    private final RedisTemplate<String, String> redisTemplate;
    private final OrderInfoMapper orderInfoMapper;

    @Override
    public OrderSaleStatDTO getOrderSaleStatData()
    {
        String cacheKey = "OrderSaleStatAll";
        //设置5秒过期时间
        String cacheData = redisTemplate.opsForValue().get(cacheKey);
        if (CsStringUtils.isNotBlank(cacheData))
        {
            return JSON.parseObject(cacheData, OrderSaleStatDTO.class);
        }

        // 统计总销售金额
        OrderSaleStatDTO yearDto = orderInfoMapper.getOrderSaleStatData();
        OrderSaleStatDTO statDTO = new OrderSaleStatDTO();
        statDTO.setTotalAmount(yearDto == null ? BigDecimal.ZERO : yearDto.getTotalAmount());

        //设置10分钟过期时间
        redisTemplate.opsForValue().set(cacheKey, JSON.toJSONString(statDTO), 600, TimeUnit.SECONDS);

        return statDTO;
    }
}
