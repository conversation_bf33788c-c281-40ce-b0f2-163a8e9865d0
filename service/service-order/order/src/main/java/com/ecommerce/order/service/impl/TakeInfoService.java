package com.ecommerce.order.service.impl;

import com.alibaba.fastjson.JSON;
import com.ecommerce.base.api.dto.warehouse.WarehouseDetailsDTO;
import com.ecommerce.base.api.service.IWarehouseService;
import com.ecommerce.common.annotation.AddLog;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.utils.BeanConvertUtils;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.goods.api.dto.GoodsDTO;
import com.ecommerce.goods.api.dto.UnitConverDTO;
import com.ecommerce.goods.api.service.IGoodsService;
import com.ecommerce.logistics.api.dto.driver.AssignDriverLogAddDTO;
import com.ecommerce.logistics.api.dto.waybill.TradeWaybillDTO;
import com.ecommerce.logistics.api.enums.PickingBillTypeEnum;
import com.ecommerce.logistics.api.enums.UserRoleEnum;
import com.ecommerce.logistics.api.enums.WaybillStatusEnum;
import com.ecommerce.logistics.api.service.IAssignDriverLogService;
import com.ecommerce.logistics.api.service.IWaybillService;
import com.ecommerce.member.api.dto.account.AccountDTO;
import com.ecommerce.member.api.dto.member.MemberDTO;
import com.ecommerce.member.api.service.IAccountService;
import com.ecommerce.member.api.service.IMemberService;
import com.ecommerce.open.api.dto.MessageConfigCodeEnum;
import com.ecommerce.order.api.dto.OrderDTO;
import com.ecommerce.order.api.dto.OverviewReqDTO;
import com.ecommerce.order.api.dto.TakeInfoDTO;
import com.ecommerce.order.api.dto.TakeInfoExportDTO;
import com.ecommerce.order.api.dto.TakeInfoOverviewDTO;
import com.ecommerce.order.api.dto.TakeInfoSearchDTO;
import com.ecommerce.order.api.dto.TakeItemDTO;
import com.ecommerce.order.api.dto.TakeStatusStatisticDTO;
import com.ecommerce.order.api.dto.base.SendSMSMessageDTO;
import com.ecommerce.order.api.enums.TakeCancelType;
import com.ecommerce.order.api.enums.TakeDeleteType;
import com.ecommerce.order.api.enums.TakeStatus;
import com.ecommerce.order.biz.ITakeInfoBiz;
import com.ecommerce.order.biz.ITakeInfoLogisticBiz;
import com.ecommerce.order.biz.ITakeInfoValidateBiz;
import com.ecommerce.order.biz.impl.TakeItemBiz;
import com.ecommerce.order.dao.vo.TakeInfo;
import com.ecommerce.order.dao.vo.TakeItem;
import com.ecommerce.order.exception.TakeInfoCode;
import com.ecommerce.order.service.ITakeInfoService;
import com.ecommerce.order.service.message.MessageQueueService;
import com.ecommerce.order.service.message.SMSMessageFactoryContext;
import com.ecommerce.order.service.order.IOrderCommonService;
import com.ecommerce.order.service.order.base.IOrderServiceExecutor;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Deprecated(since = "2.1.4-RELEASE")
@Slf4j
//@Service
@RequiredArgsConstructor
public class TakeInfoService implements ITakeInfoService {

	private final IMemberService memberService;
	private final ITakeInfoBiz takeInfoBiz;
	private final IAccountService iAccountService;
	private final ITakeInfoLogisticBiz takeInfoLogisticBiz;
	private final ITakeInfoValidateBiz takeInfoValidateBiz;
	private final IGoodsService iGoodsService;
	private final IWaybillService iWaybillService;
	private final IWarehouseService warehouseService;
	private final TakeItemBiz takeItemBiz;
	private final SMSMessageFactoryContext smsMessageFactoryContext;
	private final IAssignDriverLogService assignDriverLogService;
	private final IOrderServiceExecutor orderExecutor;
	private final MessageQueueService messageQueueService;
	private final IOrderCommonService orderCommonService;


	private static final String TAKE_CODE = "takeCode";

    private static final String AUTO_CONFIRM_MESSAGE_KEY = "com.ecommerce.order.auto.confirm.notify";

	@AddLog(operatorIndex = 2)
	@Override
	public List<TakeInfoDTO> doBuyerCreateTakeInfo(List<TakeInfoDTO> takeInfoDTOs, String memberId, String operator) {
		log.info("doBuyerCreateTakeInfo .... takeInfoDTOs {}", takeInfoDTOs);
		takeInfoDTOs = this.takeInfoBiz.doBuyerCreateTakeInfo(takeInfoDTOs, memberId, operator);
		sendTakeInfoToLogistic(takeInfoDTOs, operator);
		//发送消息
		for (TakeInfoDTO takeInfoDTO : takeInfoDTOs) {
			SendSMSMessageDTO sellerSendSMSMessageDTO = new SendSMSMessageDTO();
			OrderDTO orderDTO = new OrderDTO();
			orderDTO.setOrderId(takeInfoDTO.getOrderId());
			sellerSendSMSMessageDTO.setOrderDTO(orderDTO);
			sellerSendSMSMessageDTO.setBusinessCode(takeInfoDTO.getTakeCode());
			sellerSendSMSMessageDTO.setMessageConfigCode(
					MessageConfigCodeEnum.TAKEINFO_BUYER_CREATE.getCode());
			Map<String, Object> extraParams = new HashMap<>();
			extraParams.put(TAKE_CODE, takeInfoDTO.getTakeCode());
			sellerSendSMSMessageDTO.setExtraParams(extraParams);
			smsMessageFactoryContext.sendSMSMessage(sellerSendSMSMessageDTO);
            if (CsStringUtils.equals(takeInfoDTO.getDeliverWay(), PickingBillTypeEnum.BUYER_TAKE.getCode())) {
				AssignDriverLogAddDTO assignDriverLogAddDTO = new AssignDriverLogAddDTO();
				assignDriverLogAddDTO.setUserId(takeInfoDTO.getBuyerId());
				assignDriverLogAddDTO.setUserName(takeInfoDTO.getBuyerName());
				assignDriverLogAddDTO.setUserType(UserRoleEnum.BUYER.getCode());
				assignDriverLogAddDTO.setDriverId(takeInfoDTO.getDriverId());
				assignDriverLogAddDTO.setDriverName(takeInfoDTO.getDriverName());
				assignDriverLogAddDTO.setDriverPhone(takeInfoDTO.getDriverPhone());
				assignDriverLogAddDTO.setVehicleId(takeInfoDTO.getCarId());
				assignDriverLogAddDTO.setNumber(takeInfoDTO.getCarNum());

				List<String> goodsIdList = takeInfoDTO.getTakeItems().stream().map(TakeItemDTO::getGoodsId).toList();
				if (CollectionUtils.isNotEmpty(goodsIdList)) {
					ItemResult<List<GoodsDTO>> goodsListItemResult = iGoodsService.selectSimpleGoodsInfoByIds(goodsIdList);
					if (goodsListItemResult != null && CollectionUtils.isNotEmpty(goodsListItemResult.getData())) {
						List<String> transportCategoryIdList = goodsListItemResult.getData().stream()
								.map(GoodsDTO::getLogistics)
                                .filter(CsStringUtils::isNotBlank)
								.distinct()
								.toList();
						assignDriverLogAddDTO.setTransportCategoryIdList(transportCategoryIdList);
					}

				}

				assignDriverLogService.updateSelfPickAssignLog(assignDriverLogAddDTO);
			}
		}
		//触发自动确认
		List<String> takeInfoIdList = takeInfoDTOs.stream().map(TakeInfoDTO::getTakeId).toList();
		log.info("auto_confirm_notify sendMQ:{}",takeInfoIdList);
		messageQueueService.sendMQ(takeInfoIdList, AUTO_CONFIRM_MESSAGE_KEY);

		return takeInfoDTOs;
	}

	@AddLog(operatorIndex = 1)
	@Override
	public List<TakeInfoDTO> doOrderCreateTakeInfo(OrderDTO orderDto, String operator) {
		log.info("订单创建发货单");
		//支付后不再自动创建发货单
		return null;
	}

	@AddLog(operatorIndex = 2)
	@Override
	public List<TakeInfoDTO> doSellerCreateTakeInfo(List<TakeInfoDTO> takeInfoDTOs, String memberId, String operator) {
		log.info("卖家创建发货单:" + JSON.toJSONString(takeInfoDTOs));
        Boolean sendMessageFlag = CsStringUtils.isEmpty(takeInfoDTOs.get(0).getTakeId()) ? Boolean.FALSE : Boolean.TRUE;
		takeInfoDTOs = this.takeInfoBiz.doSellerCreateTakeInfo(takeInfoDTOs, memberId, operator);
		sendTakeInfoToLogistic(takeInfoDTOs, operator);
		//发送消息
		for (TakeInfoDTO takeInfoDTO : takeInfoDTOs) {
			if (Boolean.TRUE.equals(sendMessageFlag)) {
				SendSMSMessageDTO sellerSendSMSMessageDTO = new SendSMSMessageDTO();
				OrderDTO orderDTO = new OrderDTO();
				orderDTO.setOrderId(takeInfoDTO.getOrderId());
				sellerSendSMSMessageDTO.setOrderDTO(orderDTO);
				sellerSendSMSMessageDTO.setBusinessCode(takeInfoDTO.getTakeCode());
				sellerSendSMSMessageDTO.setMessageConfigCode(
						MessageConfigCodeEnum.TAKEINFO_SELLER_CONFIRM.getCode());
				Map<String, Object> extraParams = new HashMap<>();
				extraParams.put(TAKE_CODE, takeInfoDTO.getTakeCode());
				sellerSendSMSMessageDTO.setExtraParams(extraParams);
				smsMessageFactoryContext.sendSMSMessage(sellerSendSMSMessageDTO);
			} else {
                if (!CsStringUtils.equals(takeInfoDTO.getDeliverWay(), PickingBillTypeEnum.BUYER_TAKE.getCode()))
					continue;
				AssignDriverLogAddDTO assignDriverLogAddDTO = new AssignDriverLogAddDTO();
				assignDriverLogAddDTO.setUserId(takeInfoDTO.getBuyerId());
				assignDriverLogAddDTO.setUserName(takeInfoDTO.getBuyerName());
				assignDriverLogAddDTO.setUserType(UserRoleEnum.BUYER.getCode());
				assignDriverLogAddDTO.setDriverId(takeInfoDTO.getDriverId());
				assignDriverLogAddDTO.setDriverName(takeInfoDTO.getDriverName());
				assignDriverLogAddDTO.setDriverPhone(takeInfoDTO.getDriverPhone());
				assignDriverLogAddDTO.setVehicleId(takeInfoDTO.getCarId());
				assignDriverLogAddDTO.setNumber(takeInfoDTO.getCarNum());
				List<String> goodsIdList = takeInfoDTO.getTakeItems().stream().map(TakeItemDTO::getGoodsId).toList();
				if (CollectionUtils.isNotEmpty(goodsIdList)) {
					ItemResult<List<GoodsDTO>> goodsListItemResult = iGoodsService.selectSimpleGoodsInfoByIds(goodsIdList);
					if (goodsListItemResult != null && CollectionUtils.isNotEmpty(goodsListItemResult.getData())) {
						List<String> transportCategoryIdList = goodsListItemResult.getData().stream()
								.map(GoodsDTO::getLogistics)
                                .filter(CsStringUtils::isNotBlank)
								.distinct()
								.toList();
						assignDriverLogAddDTO.setTransportCategoryIdList(transportCategoryIdList);
					}
				}
				assignDriverLogService.updateSelfPickAssignLog(assignDriverLogAddDTO);
			}
		}

		return takeInfoDTOs;
	}

	@AddLog(operatorIndex = 2)
	@Override
	public String doBuyerCancelTakeInfo(String takeId, String memberId, String operator) {
		log.info("买家取消发货单");
		this.takeInfoBiz.validateTakeInfoCanEdit(takeId, operator, false);
		this.takeInfoBiz.doCancelTakeInfo(takeId, TakeCancelType.BUYER_CANCEL, operator);
		//尝试触发订单自动完成
		TakeInfo takeInfo = takeInfoBiz.getTakeInfoById(takeId);
		orderCommonService.triggerOrderAutoEnd(takeInfo.getOrderId());

		return takeId;
	}

	@AddLog(operatorIndex = 2)
	@Override
	public String doSellerCancelTakeInfo(String takeId, String memberId, String operator) {
		log.info("卖家取消发货单 {}", takeId);
		this.takeInfoBiz.validateTakeInfoCanEdit(takeId, operator, false);
		this.takeInfoBiz.doCancelTakeInfo(takeId, TakeCancelType.SELLER_CANCEL, operator);
		TakeInfoDTO takeInfo = takeInfoBiz.getTakeInfoDetails(takeId);
		//发送消息
		SendSMSMessageDTO sellerSendSMSMessageDTO = new SendSMSMessageDTO();
		OrderDTO orderDTO = new OrderDTO();
		orderDTO.setOrderId(takeInfo.getOrderId());
		sellerSendSMSMessageDTO.setOrderDTO(orderDTO);
		sellerSendSMSMessageDTO.setBusinessCode(takeInfo.getTakeCode());
		sellerSendSMSMessageDTO.setMessageConfigCode(
				MessageConfigCodeEnum.TAKEINFO_SELLER_CANCEL.getCode());
		Map<String, Object> extraParams = new HashMap<>();
		extraParams.put(TAKE_CODE, takeInfo.getTakeCode());
		sellerSendSMSMessageDTO.setExtraParams(extraParams);
		smsMessageFactoryContext.sendSMSMessage(sellerSendSMSMessageDTO);
		//尝试触发订单自动完成
		orderCommonService.triggerOrderAutoEnd(takeInfo.getOrderId());

		return takeId;
	}

	@AddLog(operatorIndex = 2)
	@Override
	public String doSellerConfirmTakeInfoNoChange(String takeId, String memberId, String operator) {
		log.info("卖家直接确认发货单 {}", takeId);
        if (takeId == null || CsStringUtils.isBlank(takeId)) {
			throw new BizException(TakeInfoCode.INVALID_PARAM, "发货单和id不能为空");
		}
		this.takeInfoBiz.validateTakeInfoCanEdit(takeId, operator, false);
		this.takeInfoBiz.doSellerConfirmTakeInfoNoChange(takeId, memberId, operator);
		List<String> takeInfoIds = new ArrayList<>();
		takeInfoIds.add(takeId);
		this.sendTakeInfoToLogisticById(takeInfoIds, operator);
		return takeId;
	}

	@AddLog(operatorIndex = 2)
	@Override
	public List<String> doBatchSellerConfirmTakeInfoNoChange(List<String> takeIds, String memberId, String operator) {
		log.info("卖家直接确认发货单 {}", takeIds);
		if (takeIds == null || takeIds.isEmpty()) {
			throw new BizException(TakeInfoCode.INVALID_PARAM, "takeIds不能为空");
		}
		List<String> takeInfoIds = new ArrayList<>();
		for (String takeId : takeIds) {
			this.takeInfoBiz.validateTakeInfoCanEdit(takeId, operator, true);
			this.takeInfoBiz.doSellerConfirmTakeInfoNoChange(takeId, memberId, operator);
		}
		this.sendTakeInfoToLogisticById(takeIds, operator);
		//发送消息
		for (String takeId : takeIds) {
			TakeInfo takeInfoDTO = takeInfoBiz.getTakeInfoById(takeId);
			SendSMSMessageDTO sellerSendSMSMessageDTO = new SendSMSMessageDTO();
			OrderDTO orderDTO = new OrderDTO();
			orderDTO.setOrderId(takeInfoDTO.getOrderId());
			sellerSendSMSMessageDTO.setOrderDTO(orderDTO);
			sellerSendSMSMessageDTO.setBusinessCode(takeInfoDTO.getTakeCode());
			sellerSendSMSMessageDTO.setMessageConfigCode(
					MessageConfigCodeEnum.TAKEINFO_SELLER_CONFIRM.getCode());
			Map<String, Object> extraParams = new HashMap<>();
			extraParams.put(TAKE_CODE, takeInfoDTO.getTakeCode());
			sellerSendSMSMessageDTO.setExtraParams(extraParams);
			smsMessageFactoryContext.sendSMSMessage(sellerSendSMSMessageDTO);
		}

		return takeInfoIds;
	}

	@AddLog(operatorIndex = 2)
	@Override
	public List<TakeInfoDTO> doSellerConfirmTakeInfo(List<TakeInfoDTO> takeInfoDTOs, String memberId, String operator) {
		log.info("卖家编辑确认发货单 {}", takeInfoDTOs);
		if (takeInfoDTOs == null || takeInfoDTOs.isEmpty()) {
			throw new BizException(TakeInfoCode.INVALID_PARAM, "发货单信息不能为空");
		}

		// 卖家确认
		takeInfoDTOs = this.takeInfoBiz.doSellerConfirmTakeInfo(takeInfoDTOs, memberId, operator);
		sendTakeInfoToLogistic(takeInfoDTOs, operator);
		//发送消息
		for (TakeInfoDTO takeInfoDTO : takeInfoDTOs) {
			SendSMSMessageDTO sellerSendSMSMessageDTO = new SendSMSMessageDTO();
			OrderDTO orderDTO = new OrderDTO();
			orderDTO.setOrderId(takeInfoDTO.getOrderId());
			sellerSendSMSMessageDTO.setOrderDTO(orderDTO);
			sellerSendSMSMessageDTO.setBusinessCode(takeInfoDTO.getTakeCode());
			sellerSendSMSMessageDTO.setMessageConfigCode(
					MessageConfigCodeEnum.TAKEINFO_SELLER_CONFIRM.getCode());
			Map<String, Object> extraParams = new HashMap<>();
			extraParams.put(TAKE_CODE, takeInfoDTO.getTakeCode());
			sellerSendSMSMessageDTO.setExtraParams(extraParams);
			smsMessageFactoryContext.sendSMSMessage(sellerSendSMSMessageDTO);
		}

		return takeInfoDTOs;
	}

	@AddLog(operatorIndex = 1)
	@Override
	public void updateSendQuantity(String takeInfoId, String operator, Map<String, String> reourceQuantityDetails) {
		this.takeInfoBiz.updateShippedQuantity(takeInfoId, operator, reourceQuantityDetails);
	}

	@AddLog(operatorIndex = 2)
	@Override
	public void doFinishTakeInfoByLogistic(String takeInfoId, List<TakeItemDTO> logisticTakeItems, String operator) {
		// 检查发货单状态是否是已完成，已完成则不做处理
		log.info("物流完成发货单 {} {}", takeInfoId, logisticTakeItems);
		this.takeInfoBiz.finishTakeInfoByLogistic(takeInfoId, logisticTakeItems, operator);
	}

	@AddLog(operatorIndex = 2)
	@Override
	public void doCloseTakeInfoByLogistic(String takeInfoId, List<TakeItemDTO> logisticTakeItems, String operator) {
		log.info("物流关闭发货单 {} {}", takeInfoId, logisticTakeItems);
		this.takeInfoBiz.closeTakeInfoByLogistic(takeInfoId, logisticTakeItems, operator);
	}

	@AddLog(operatorIndex = 1)
	@Override
	public void updateSellerSignQuantity(String takeInfoId, String adjustAddWay ,String operator, Map<String, String> itemQuantityDetails) {
		log.info("物流更新签收数量发货单 {} {}", takeInfoId, itemQuantityDetails);
		this.takeInfoBiz.updateSellerSignQuantity(takeInfoId, adjustAddWay,operator, itemQuantityDetails);
	}

	@Override
	public List<TakeInfoDTO> getTakeInfoDetailForOrder(String orderId) {
		return this.takeInfoBiz.getTakeInfoDetailsByOrderId(orderId);
	}

	@Override
	public PageInfo<TakeInfoDTO> pageBuyerTakeInfo(TakeInfoSearchDTO takeInfoQuery) {
        if (takeInfoQuery == null || (CsStringUtils.isBlank(takeInfoQuery.getAccount())
                && CsStringUtils.isBlank(takeInfoQuery.getBuyerId()))) {
			throw new BizException(TakeInfoCode.INVALID_PARAM, "参数错误");
		}
        if (!CsStringUtils.isBlank(takeInfoQuery.getAccount())) {
			String memberId = takeInfoQuery.getBuyerId();
            if (CsStringUtils.isBlank(memberId)) {
				throw new BizException(TakeInfoCode.INVALID_PARAM, "参数错误");
			}
			takeInfoQuery.setBuyerId(memberId);
		}

		return new PageInfo<>(this.takeInfoBiz.pageTakeInfoDetails(takeInfoQuery));
	}

	@Override
	public PageInfo<TakeInfoDTO> pageSellerTakeInfo(TakeInfoSearchDTO takeInfoQuery) {
        if (takeInfoQuery == null || (CsStringUtils.isBlank(takeInfoQuery.getAccount())
                && CsStringUtils.isBlank(takeInfoQuery.getSellerId()))) {
			throw new BizException(TakeInfoCode.INVALID_PARAM, "参数错误");
		}
        if (!CsStringUtils.isBlank(takeInfoQuery.getAccount())) {
			String memberId = takeInfoQuery.getSellerId();
            if (CsStringUtils.isBlank(memberId)) {
				throw new BizException(TakeInfoCode.INVALID_PARAM, "参数错误");
			}
			takeInfoQuery.setSellerId(memberId);
		}
		return new PageInfo<>(this.takeInfoBiz.pageTakeInfoDetails(takeInfoQuery));
	}

	@Override
	public PageInfo<TakeInfoDTO> pagePlatformTakeInfo(TakeInfoSearchDTO takeInfoQuery) {
		return new PageInfo<>(this.takeInfoBiz.pageTakeInfoDetails(takeInfoQuery));
	}

	@AddLog(operatorIndex = 1)
	@Override
	public TakeInfoDTO prepareTakeInfo(String orderId, String operator) {
		// TODO Auto-generated method stub
		return null;
	}

	public String getMemberId(String operator) {
		AccountDTO account = this.iAccountService.findById(operator);
		if (account == null) {
			throw new BizException(TakeInfoCode.DATA_NOT_FOUND, "账号信息有误");
		}
		return account.getMemberId();
	}

	@Override
	public TakeInfoDTO getSellerTakeInfoDetail(String takeInfoId, String memberId, String operator) {
        if (CsStringUtils.isBlank(takeInfoId) || CsStringUtils.isBlank(operator)) {
			throw new BizException(TakeInfoCode.INVALID_PARAM);
		}
		return this.takeInfoBiz.getTakeInfoDetails(takeInfoId, memberId, null);
	}

	@Override
	public TakeInfoDTO getTakeInfoForCreate(String orderId, String operator) {
		return this.takeInfoBiz.getTakeInfoForCreate(orderId, operator);
	}

	@Override
	public TakeInfoOverviewDTO takeInfoOverview(OverviewReqDTO overviewReqDTO) {
		return takeInfoBiz.takeInfoOverview(overviewReqDTO);
	}

	@Override
	public void finishTakeInfoByERP(String orderId, String operator) {
		takeInfoBiz.finishTakeInfoByERP(orderId,operator);
	}

	@Override
	public List<TakeItemDTO> selectItemByOrderItemId(String orderItemId) {
        if (CsStringUtils.isEmpty(orderItemId)) {
			throw new BizException(BasicCode.INVALID_PARAM, "orderItemId");
		}
		List<TakeItem> itemList = takeItemBiz.selectItemByOrderItemId(orderItemId);
		List<TakeItemDTO> takeItemList = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(itemList)) {
			for (TakeItem takeItem : itemList) {
				TakeItemDTO takeItemDTO = new TakeItemDTO();
				BeanUtils.copyProperties(takeItem, takeItemDTO);
				takeItemList.add(takeItemDTO);
			}
		}

		return takeItemList;
	}

	@Override
	public TakeInfoDTO getBuyerTakeInfoDetail(String takeInfoId, String memberId, String operator) {
        if (CsStringUtils.isBlank(takeInfoId) || CsStringUtils.isBlank(operator)) {
			throw new BizException(TakeInfoCode.INVALID_PARAM);
		}
		return this.takeInfoBiz.getTakeInfoDetails(takeInfoId, null, memberId);
	}

	@Override
	public TakeInfoDTO getPlatformTakeInfoDetail(String takeInfoId, String operator) {
		return this.takeInfoBiz.getTakeInfoDetails(takeInfoId);
	}

	@AddLog(operatorIndex = 1)
	@Override
	@Transactional
	public void doSellerCloseOrder(String orderId, String operator) {
		log.info(" 卖家关闭订单 orderId {} ", orderId);
		List<TakeInfo> takeInfos = this.takeInfoBiz.getTakeInfoByOrderIdNoCancel(orderId);
		if (takeInfos == null || takeInfos.isEmpty()) {
			log.info(" 未找到发货单 ");
			return;
		}
		List<String> needCloseIds = new ArrayList<>();
		takeInfos.stream().forEach(takeInfo -> {
			if (takeInfo.getTakeStatus().equals(TakeStatus.PENDING_DELIVERY.getCode())
					|| takeInfo.getTakeStatus().equals(TakeStatus.DELIVERING.getCode())) {
				needCloseIds.add(takeInfo.getTakeCode());
			}
		});
		if(!needCloseIds.isEmpty()) {
			this.sendCloseRequestToLogistic(needCloseIds, operator);
		}
		takeInfos.forEach(takeInfo -> this.takeInfoBiz.doSellerCloseTakeInfo(takeInfo.getTakeId(), takeInfo.getSellerId(), operator));
		// this.takeInfoBiz.c
	}

	@Override
	public void doSellerCloseTakeInfo(String takeInfoId, String operator) {
		log.info(" 卖家关闭发货单 takeInfoId {} ", takeInfoId);
		TakeInfo takeInfo = this.takeInfoBiz.getTakeInfoById(takeInfoId);
		if (takeInfo == null) {
			log.info(" 未找到发货单 ");
			return;
		}
		if (takeInfo.getTakeTimeLimit() != null &&
				takeInfo.getTakeTimeLimit().getTime() < new Date().getTime()) {
			throw new BizException(BasicCode.UNDEFINED_ERROR, "订单已过期，不允许执行此操作！");
		}
		List<String> needCloseIds = new ArrayList<>();
		if (takeInfo.getTakeStatus().equals(TakeStatus.PENDING_DELIVERY.getCode())
				|| takeInfo.getTakeStatus().equals(TakeStatus.DELIVERING.getCode())) {
			needCloseIds.add(takeInfo.getTakeCode());
		}
		if(!needCloseIds.isEmpty()) {
			this.sendCloseRequestToLogistic(needCloseIds, operator);
		}
		this.takeInfoBiz.doSellerCloseTakeInfo(takeInfo.getTakeId(), takeInfo.getSellerId(), operator);
		//尝试触发订单自动完成
		orderCommonService.triggerOrderAutoEnd(takeInfo.getOrderId());
	}

	@Override
	public void checkTakeTimeLimited() {
		this.takeInfoBiz.checkTakeTimeLimit();
	}

	/**
	 * 传物流
	 * 
	 * @param takeInfos
	 * @param operator
	 */
	public void sendTakeInfoToLogistic(List<TakeInfoDTO> takeInfos, String operator) {

		List<TakeInfoDTO> takeInfoDTOs = new ArrayList<>();
		takeInfos.stream().forEach(takeInfoDTO -> {
			if (TakeStatus.SELLER_CONFIRMED.getCode().equals(takeInfoDTO.getTakeStatus())) {
				takeInfoDTOs.add(takeInfoDTO);
				takeInfoDTO.getTakeItems().forEach(item -> item.setTakeQuantity(this.takeInfoBiz.transferIntoDefaultUnit(item.getTakeQuantity(), item.getUnits(), item.getGoodsId())));
			}
		});
		try {
			this.takeInfoLogisticBiz.sendTakeInfoToLogistic(takeInfoDTOs, operator);
		} catch (Exception e) {
			log.error("sendTakeInfoToLogistic:" + JSON.toJSONString(e));
			throw new BizException(TakeInfoCode.LOGISTIC_ERROR_CREATE, e.getMessage());
		}
		
	}

	private void sendTakeInfoToLogisticById(List<String> takeInfoIds, String operator) {
		// 需要判断发货单状态
		List<TakeInfoDTO> takeInfos = new ArrayList<>();
		takeInfoIds.stream().forEach(takeInfoId -> takeInfos.add(this.takeInfoBiz.getTakeInfoDetails(takeInfoId)));
		this.sendTakeInfoToLogistic(takeInfos, operator);
	}

	private void sendCloseRequestToLogistic(List<String> takeInfoIds, String operator) {
		this.takeInfoLogisticBiz.sendCloseRequestToLogistic(takeInfoIds, operator);
	}

	@Override
	public TakeInfoDTO getTakeInfoByCode(String code) {
		TakeInfoDTO takeInfoDTO = BeanConvertUtils.convert(this.takeInfoBiz.getTakeInfoByCode(code), TakeInfoDTO.class);
		if (takeInfoDTO != null) {
			String takeId = takeInfoDTO.getTakeId();
			List<TakeItem> takeItems = takeItemBiz.selectItemByTakeId(takeId);
			if (CollectionUtils.isNotEmpty(takeItems)) {
				List<TakeItemDTO> takeItemDTOList = Lists.newArrayList();
				for (TakeItem item : takeItems) {
					takeItemDTOList.add(BeanConvertUtils.convert(item, TakeItemDTO.class));
				}
				takeInfoDTO.setTakeItems(takeItemDTOList);
			}
		}
		return takeInfoDTO;
	}

	@AddLog(operatorIndex = 1)
	@Override
	public void doSellerDeleteTakeInfo(String takeId, String operator) {
		log.info("卖家删除发货单 {} {}", takeId, operator);
		takeInfoValidateBiz.validateTakeInfoCanDelete(takeId, operator, true, false);
		this.takeInfoBiz.doDeleteCanceledTakeInfo(takeId, TakeDeleteType.SELLER_DELETE);
	}

	@AddLog(operatorIndex = 1)
	@Override
	public void doBuyerDeleteTakeInfo(String takeId, String operator) {
		log.info("买家删除发货单 {} {}", takeId, operator);
		takeInfoValidateBiz.validateTakeInfoCanDelete(takeId, operator, false, false);
		this.takeInfoBiz.doDeleteCanceledTakeInfo(takeId, TakeDeleteType.BUYER_DELETE);
	}

	@Override
	public List<TakeInfoDTO> getSellerOrderTakeInfo(String orderId, String operator) {
		takeInfoValidateBiz.validateOrderTakeInfoCanGet(orderId, operator, true, false);
		return this.takeInfoBiz.getTakeInfoDetailsByOrderId(orderId);
	}

	@Override
	public List<TakeInfoDTO> getBuyerOrderTakeInfo(String orderId, String operator) {
		OrderDTO orderDTO = takeInfoValidateBiz.validateOrderTakeInfoCanGet(orderId, operator, false, false);
		List<String> takeStatus = new ArrayList<>();
		TakeInfo takeInfoQuery = new TakeInfo();
		takeInfoQuery.setBuyerId(orderDTO.getBuyerId());
		takeInfoQuery.setOrderId(orderId);
		List<TakeInfoDTO> takeInfoDTOs = takeInfoBiz.searchTakeInfoDetails(takeInfoQuery, takeStatus);
		Map<String, List<UnitConverDTO>> goodsUnits = new HashMap<>();
		Map<String, String> itemUnits = new HashMap<>();

		if (takeInfoDTOs == null) {
			return null;
		} else {
			for (TakeInfoDTO takeInfoDTO : takeInfoDTOs) {
				for (TakeItemDTO takeItem : takeInfoDTO.getTakeItems()) {
					if (!goodsUnits.containsKey(takeItem.getOrderItemId())) {
						List<UnitConverDTO> result = iGoodsService.getUnitConverInfo(takeItem.getGoodsId()).getData();
						goodsUnits.put(takeItem.getOrderItemId(), result);
					}
					if (!itemUnits.containsKey(takeItem.getOrderItemId())) {
						itemUnits.put(takeItem.getOrderItemId() , takeItem.getUnits());
					}
				}
			}
			for (TakeInfoDTO takeInfo : takeInfoDTOs) {
				//方法已废弃
				ItemResult<List<TradeWaybillDTO>> tradeWaybillDTOs = iWaybillService.queryWaybillListByDeliveryNum(takeInfo.getTakeCode());
				if (tradeWaybillDTOs != null && tradeWaybillDTOs.getData() != null) {
					takeInfo.setTradeWaybillDTOs(convertTradeWaybill(tradeWaybillDTOs.getData()));
					takeInfo.getTradeWaybillDTOs().forEach(takeBill -> {
						takeBill.setWaybillStatus(WaybillStatusEnum.valueOfCode(takeBill.getWaybillStatus()).getDesc());

						List<UnitConverDTO> result = goodsUnits.get(takeBill.getResourceId());
						String usedUnit = itemUnits.get(takeBill.getResourceId());
						if (result == null) {
							log.info(" 无法获取单位转换信息 {}", takeBill);
							return;
						}

						UnitConverDTO unitConverDTO = result.get(0);
						if ((result.size() == 1)
								|| ((unitConverDTO.isDefault() && (usedUnit.equals(unitConverDTO.getUnitId1())
								|| usedUnit.equals(unitConverDTO.getUnit1())))
								|| (usedUnit.equals(unitConverDTO.getUnitId2())
								|| usedUnit.equals(unitConverDTO.getUnit2())))) {
							return;
						} else {
							if (!unitConverDTO.isDefault()) {
								takeBill.setShippingQuantity((new BigDecimal(takeBill.getShippingQuantity())
										.divide(unitConverDTO.getRatio(), 2, RoundingMode.HALF_UP).toString()));
								takeBill.setActualShippingQuantity((new BigDecimal(takeBill.getActualShippingQuantity())
										.divide(unitConverDTO.getRatio(), 2, RoundingMode.HALF_UP).toString()));
							} else {
								takeBill.setShippingQuantity((new BigDecimal(takeBill.getShippingQuantity())
										.multiply(unitConverDTO.getRatio())).setScale(2, RoundingMode.HALF_UP).toString());
								takeBill.setActualShippingQuantity((new BigDecimal(takeBill.getActualShippingQuantity())
										.multiply(unitConverDTO.getRatio())).setScale(2, RoundingMode.HALF_UP).toString());
							}
						}
					});
				} else {
					log.info(" 未找到运单信息 {}", takeInfo);
				}
                if (!CsStringUtils.isEmpty(takeInfo.getStoreId())) {
					setTakeInfoStoreAddressAndContact(takeInfo, warehouseService);
				}

				setTakeInfoReceiverName(takeInfo, memberService);
				takeInfo.setStatusName(TakeStatus.getByCode(takeInfo.getTakeStatus()).getMessage());
			}
		}
		return takeInfoDTOs;
	}

	public static void setTakeInfoStoreAddressAndContact(TakeInfoDTO takeInfo, IWarehouseService warehouseService) {
		WarehouseDetailsDTO store = warehouseService.queryWarehouseDetails(takeInfo.getStoreId());
		if (store != null) {
			StringBuilder builder = new StringBuilder();
			builder.append(store.getProvince()).append(" ").append(store.getCity()).append(" ")
					.append(store.getDistrict()).append(" ").append(" ").append(store.getAddress())
					.append(" ").append(store.getName()).append(" ");
			takeInfo.setStoreAddressShow(builder.toString());
			takeInfo.setStoreContactShow(store.getAdministrator() + " " + store.getAdministratorPhone());

		}
	}

	public static void setTakeInfoReceiverName(TakeInfoDTO takeInfo, IMemberService memberService) {
		if (CsStringUtils.isNotBlank(takeInfo.getReceiver())) {
			takeInfo.setReceiverName(takeInfo.getReceiver());
			try {
				MemberDTO receiver = memberService.findMemberDetailById(takeInfo.getReceiver());
				if (receiver != null)
					takeInfo.setReceiverName(receiver.getMemberName());
			} catch (Exception e) {
				log.error("memberService.findMemberDetailById_Error:" + takeInfo.getReceiver(), e);
			}
		}
	}

	private List<com.ecommerce.order.api.dto.logistics.TradeWaybillDTO> convertTradeWaybill(List<TradeWaybillDTO> tradeWaybillDTOS){
		if(tradeWaybillDTOS == null){
			return null;
		}
		List<com.ecommerce.order.api.dto.logistics.TradeWaybillDTO> tradeWaybillList = new ArrayList<>();
		for(TradeWaybillDTO tradeWaybillDTO : tradeWaybillDTOS){
			com.ecommerce.order.api.dto.logistics.TradeWaybillDTO tradeWaybill = new com.ecommerce.order.api.dto.logistics.TradeWaybillDTO();
			BeanUtils.copyProperties(tradeWaybillDTO,tradeWaybill);
			tradeWaybillList.add(tradeWaybill);
		}
		return tradeWaybillList;
	}

	@Override
	public List<TakeInfoDTO> getPlantformOrderTakeInfo(String orderId, String operator) {
		takeInfoValidateBiz.validateOrderTakeInfoCanGet(orderId, operator, false, true);
		return this.takeInfoBiz.getTakeInfoDetailsByOrderId(orderId);
	}

	@AddLog(operatorIndex = 3)
	@Override
	public String doSellerFinishTakeInfo(String takeInfoId, List<TakeItemDTO> takeItems, String memberId,
			String operator) {
        if (CsStringUtils.isEmpty(takeInfoId) || CsStringUtils.isEmpty(memberId) || CsStringUtils.isEmpty(operator)
				|| takeItems == null || takeItems.isEmpty()) {
			throw new BizException(TakeInfoCode.INVALID_PARAM, "参数错误 不能为空");
		}
		log.info(" 卖家点击完成发货单 {}, {} , {},{}", takeItems, takeInfoId, memberId, operator);

		TakeInfoDTO takeInfoDTO = this.takeInfoBiz.getTakeInfoDetails(takeInfoId);
		if (takeInfoDTO == null) {
			throw new BizException(TakeInfoCode.DATA_NOT_EXIST, "发货单不存在");
		}
		if (!memberId.equals(takeInfoDTO.getSellerId())) {
			throw new BizException(TakeInfoCode.NO_PERMISSION, "你无权操作该发货单");
		}
		if (PickingBillTypeEnum.PLATFORM_DELIVERY.getCode().equals(takeInfoDTO.getDeliverWay())) {
			throw new BizException(TakeInfoCode.UN_SUPPORT, "卖家不可完成平台配送的发货单");
		}
		takeItems.forEach(inputItem -> {
			for (TakeItemDTO takeItem : takeInfoDTO.getTakeItems()) {
				if (takeItem.getTakeItemId().equals(inputItem.getTakeItemId())) {
					takeItem.setSellerSignQuantity(inputItem.getSellerSignQuantity());
					takeItem.setShippedQuantity(inputItem.getShippedQuantity());
                    if (CsStringUtils.isEmpty(takeItem.getResourceId())) {
						takeItem.setResourceId(takeItem.getOrderItemId());
					}
					return;
				}
			}
		});
		try {
			this.takeInfoBiz.doSellerFinishTakeInfo(takeInfoDTO, operator);
		}catch (BizException e){
			return e.getMessage();
		}
		return null;
	}

	@AddLog(operatorIndex = 3)
	@Override
	public String doBuyerFinishTakeInfo(String takeInfoId, List<TakeItemDTO> takeItems, String memberId,
			String operator) {
		log.info(" 买家点击完成发货单 {}, {} , {},{}", takeItems, takeInfoId, memberId, operator);
		return null;
	}

	@AddLog(operatorIndex = 2)
	@Override
	public String doSellerSendTakeInfoToLogistic(String takeInfoId, String memberId, String operator) {
        if (CsStringUtils.isEmpty(takeInfoId) || CsStringUtils.isEmpty(memberId) || CsStringUtils.isEmpty(operator)) {
			throw new BizException(TakeInfoCode.INVALID_PARAM, "参数错误 不能为空");
		}
		log.info(" 卖家点击发货单传物流 {}, {} , {}", takeInfoId, memberId, operator);

		TakeInfoDTO takeInfoDTO = this.takeInfoBiz.getTakeInfoDetails(takeInfoId);
		if (takeInfoDTO == null) {
			throw new BizException(TakeInfoCode.DATA_NOT_EXIST, "发货单不存在");
		}
		if (!memberId.equals(takeInfoDTO.getSellerId())) {
			throw new BizException(TakeInfoCode.NO_PERMISSION, "你无权操作该发货单");
		}
		if (!TakeStatus.SELLER_CONFIRMED.getCode().equals(takeInfoDTO.getTakeStatus())) {
			throw new BizException(TakeInfoCode.UN_SUPPORT, "发货单状态不正确");
		}
		List<TakeInfoDTO> takeInfoDTOs = new ArrayList<>();
		takeInfoDTOs.add(takeInfoDTO);
		this.sendTakeInfoToLogistic(takeInfoDTOs, operator);
		log.info("===========卖家点击发货单传物流完成！=========");
		return takeInfoId;
	}

	@AddLog(operatorIndex = 1)
	@Override
	public void updateSendQuantity(String takeInfoId,
								   String operator,
								   Map<String, String> reourceQuantityDetails,
								   Map<String, String> takeCost,
								   Map<String, BigDecimal> actualQuantityDetails) {
		this.takeInfoBiz.updateShippedQuantity(
				takeInfoId, operator, reourceQuantityDetails, takeCost, actualQuantityDetails);
		
	}


	@Override
	public List<TakeStatusStatisticDTO> statisticTakeStatus(TakeInfoSearchDTO takeInfoSearchDTO) {
		return takeInfoBiz.statisticTakeStatus(takeInfoSearchDTO);
	}

	@Override
	public List<TakeInfoExportDTO> exportTakeInfo(TakeInfoSearchDTO takeInfoSearchDTO) {
        if (takeInfoSearchDTO == null || (CsStringUtils.isBlank(takeInfoSearchDTO.getAccount())
                && CsStringUtils.isBlank(takeInfoSearchDTO.getSellerId()))) {
			throw new BizException(TakeInfoCode.INVALID_PARAM, "参数错误");
		}
        if (!CsStringUtils.isBlank(takeInfoSearchDTO.getAccount())) {
			String memberId = takeInfoSearchDTO.getSellerId();
            if (CsStringUtils.isBlank(memberId)) {
				throw new BizException(TakeInfoCode.INVALID_PARAM, "参数错误");
			}
			takeInfoSearchDTO.setSellerId(memberId);
		}
		return takeInfoBiz.exportTakeInfo(takeInfoSearchDTO);
	}
}
