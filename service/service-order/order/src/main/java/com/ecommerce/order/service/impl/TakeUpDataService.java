package com.ecommerce.order.service.impl;

import com.alibaba.fastjson.JSON;
import com.ecommerce.base.api.dto.warehouse.WarehouseDetailsDTO;
import com.ecommerce.base.api.service.IWarehouseService;
import com.ecommerce.common.annotation.AddLog;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.exception.DistributeLockException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.service.lock.RedisLockService;
import com.ecommerce.common.utils.ArithUtils;
import com.ecommerce.common.utils.BeanConvertUtils;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.goods.api.dto.ResourceDTO;
import com.ecommerce.goods.api.dto.SpecialGoodsAttributeDTO;
import com.ecommerce.goods.api.service.IGoodsService;
import com.ecommerce.goods.api.service.IResourceService;
import com.ecommerce.logistics.api.dto.waybill.NotifyLeaveWarehouseDTO;
import com.ecommerce.logistics.api.enums.WarehouseTypeEnum;
import com.ecommerce.member.api.dto.member.config.MemberConfigEnum;
import com.ecommerce.order.api.dto.CloseTakeInfoDTO;
import com.ecommerce.order.api.dto.FinishTakeInfoDTO;
import com.ecommerce.order.api.dto.FinishTakeItemDTO;
import com.ecommerce.order.api.dto.OrderAdjustDTO;
import com.ecommerce.order.api.dto.OrderDTO;
import com.ecommerce.order.api.dto.OrderItemDTO;
import com.ecommerce.order.api.dto.OversoldDTO;
import com.ecommerce.order.api.dto.OversoldQueryDTO;
import com.ecommerce.order.api.dto.SplitTriggerDTO;
import com.ecommerce.order.api.dto.TakeInfoDTO;
import com.ecommerce.order.api.dto.TakeItemDTO;
import com.ecommerce.order.api.dto.UpdateTakeInfoShippedDTO;
import com.ecommerce.order.api.dto.UpdateTakeInfoSignDTO;
import com.ecommerce.order.api.dto.UpdateTakeItemSignDTO;
import com.ecommerce.order.api.dto.logistics.OverSendOutVerifyDTO;
import com.ecommerce.order.api.enums.AdjustAddWayEnum;
import com.ecommerce.order.api.enums.OrderTypeEnum;
import com.ecommerce.order.api.enums.OverSellValidateResultEnum;
import com.ecommerce.order.api.enums.PayDetailTypeEnum;
import com.ecommerce.order.api.enums.PayStatusEnum;
import com.ecommerce.order.api.enums.TakeStatus;
import com.ecommerce.order.biz.IBillSplitBiz;
import com.ecommerce.order.biz.IOrderErpBiz;
import com.ecommerce.order.biz.IOrderItemQuantityBiz;
import com.ecommerce.order.biz.IOrderOverSellBiz;
import com.ecommerce.order.biz.IOrderPayinfoDetailBiz;
import com.ecommerce.order.biz.IOrderPayinfoJoinBiz;
import com.ecommerce.order.biz.ITakeInfoBiz;
import com.ecommerce.order.biz.ITakeItemBiz;
import com.ecommerce.order.dao.dto.PaymentAmountDO;
import com.ecommerce.order.dao.dto.PaymentAmountQueryDO;
import com.ecommerce.order.dao.dto.SupplementPaymentDO;
import com.ecommerce.order.dao.vo.OrderInfo;
import com.ecommerce.order.dao.vo.OrderPayinfo;
import com.ecommerce.order.dao.vo.OrderPayinfoDetail;
import com.ecommerce.order.dao.vo.TakeItem;
import com.ecommerce.order.fsm.service.IOrderQueryService;
import com.ecommerce.order.service.IOrderService;
import com.ecommerce.order.service.ITakeInfoService;
import com.ecommerce.order.service.ITakeUpDataService;
import com.ecommerce.order.service.message.IMessageService;
import com.ecommerce.order.service.order.IOrderCommonService;
import com.ecommerce.order.service.order.IOrderPaymentService;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Deprecated(since = "2.1.4-RELEASE")
@Slf4j
@RequiredArgsConstructor
public class TakeUpDataService implements ITakeUpDataService {

    private final ITakeInfoService takeInfoService;
    private final ITakeItemBiz takeItemBiz;
    private final OrderSMSMessageProducer orderSMSMessageProducer;
    private final IOrderService orderService;
    private final RedisLockService redisLockService;
    private final IOrderOverSellBiz orderOverSellBiz;
    private final IOrderPayinfoJoinBiz orderPayinfoJoinBiz;
    private final IGoodsService goodsService;
    private final IResourceService resourceService;
    private final IBillSplitBiz billSplitBiz;
    private final IOrderPayinfoDetailBiz orderPayinfoDetailBiz;
    private final ITakeInfoBiz takeInfoBiz;
    private final IOrderErpBiz orderErpBiz;
    private final IOrderItemQuantityBiz orderItemQuantityBiz;
    private final IOrderPaymentService orderPaymentService;
    private final IOrderQueryService orderQueryService;
    private final IWarehouseService warehouseService;
    private final IMessageService messageService;
    private final IOrderCommonService orderCommonService;


    private static final String LOGISTICS = "logistics";

    private String startLock(String orderId) {
        String identifier = null;
        log.info(" 获取分布式事务锁 {} ， {}", orderId, Thread.currentThread());
        try {
            identifier = redisLockService.lock(orderId);
            log.info(" 获取分布式事务锁成功 {} ，{}, {}", orderId, identifier, Thread.currentThread());
        } catch (DistributeLockException e) {
            log.info("获取分布式事务锁异常，异常信息：{}",e.getMessage());
            throw new BizException(BasicCode.INVALID_PARAM, " 获取订单失败 ");
        }
        return identifier;
    }

    private void endLock(String orderId, String identifier) {
        log.info(" 解锁 orderId :{}, identifier :{} , thread :{}", orderId, identifier, Thread.currentThread());
        try {
            redisLockService.unlock(orderId, identifier);
            log.info(" 解锁成功 orderId :{}, identifier :{} , thread :{}", orderId, identifier, Thread.currentThread());
        } catch (DistributeLockException e) {
            log.info("处理订单数据失败");
            throw new BizException(BasicCode.INVALID_PARAM, " 处理订单数据失败 ");
        }
    }

    @AddLog(operatorIndex = 0, operatorFieldName = "operator")
    @Override
    public ItemResult<Boolean> doLogisticCloseTakeInfo(CloseTakeInfoDTO closeTakeInfoDTO) {
        log.info("物流关闭发货单{}", closeTakeInfoDTO);
        String lock = null;
        String orderId = null;
        try {
            TakeInfoDTO takeInfo = this.takeInfoService.getTakeInfoByCode(closeTakeInfoDTO.getTakeCode());
            Set<String> finalStatus = Sets.newHashSet(
                    TakeStatus.CLOSED.getCode(),
                    TakeStatus.CANCELED.getCode(),
                    TakeStatus.FINISHED.getCode());
            if (finalStatus.contains(takeInfo.getTakeStatus())) {
                log.info("发货单已处于最终状态:" + JSON.toJSONString(takeInfo));
                return new ItemResult<>(true);
            }
            orderId = takeInfo.getOrderId();
            lock = this.startLock(orderId);
            List<TakeItemDTO> takeItemDTOs = new ArrayList<>();
            closeTakeInfoDTO.getCloseTakeItemDTOs().forEach(cTakeInfo -> takeItemDTOs.add(BeanConvertUtils.convert(cTakeInfo, TakeItemDTO.class)));
            this.takeInfoService.doCloseTakeInfoByLogistic(takeInfo.getTakeId(), takeItemDTOs,
                    closeTakeInfoDTO.getOperator());
            orderService.checkRealTimeOrderAmount(orderId, closeTakeInfoDTO.getOperator());
            //尝试触发订单自动完成
            orderCommonService.triggerOrderAutoEnd(takeInfo.getOrderId());
        } catch (Exception e) {
            log.info("物流关闭发货单异常，异常信息：{}",e.getMessage());
            return new ItemResult<>(false);
        } finally {
            if (orderId != null && lock != null) {
                this.endLock(orderId, lock);
            }
        }

        return new ItemResult<>(true);
    }

    @AddLog(operatorIndex = 0, operatorFieldName = "operator")
    @Override
    public ItemResult<Boolean> doLogisticFinishTakeInfo(FinishTakeInfoDTO finishTakeInfoDTO) {
        log.info("物流完成发货单{}", finishTakeInfoDTO);
        String lock = null;
        String orderId = null;
        try {
            TakeInfoDTO takeInfo = this.takeInfoService.getTakeInfoByCode(finishTakeInfoDTO.getTakeCode());
            Set<String> finalStatus = Sets.newHashSet(
                    TakeStatus.CLOSED.getCode(),
                    TakeStatus.CANCELED.getCode(),
                    TakeStatus.FINISHED.getCode());
            if (finalStatus.contains(takeInfo.getTakeStatus())) {
                log.info("发货单已处于最终状态:" + JSON.toJSONString(takeInfo));
                return new ItemResult<>(true);
            }
            orderId = takeInfo.getOrderId();
            OrderDTO order = this.orderService.getOrderAndItemInfo(orderId).getData();
            lock = this.startLock(orderId);
            List<TakeItemDTO> takeItemDTOs = new ArrayList<>();
            finishTakeInfoDTO.getFinishTakeItemDTOs().forEach(cTakeInfo -> {
                TakeItemDTO takeItemDTO = BeanConvertUtils.convert(cTakeInfo, TakeItemDTO.class);
                if (cTakeInfo.getCost() != null) {
                    //分合同单和挂牌单分别判断，如果修改了定价逻辑需要做相应修改
                    setRealLogisticCostAmount(finishTakeInfoDTO, cTakeInfo, order, takeItemDTO);
                    setRealLogisticCostAmountV2(finishTakeInfoDTO, cTakeInfo, order, takeItemDTO);
                } else {
                    takeItemDTO.setRealLogisticCostAmount(BigDecimal.ZERO);
                }
                takeItemDTOs.add(takeItemDTO);
            });
            this.takeInfoService.doFinishTakeInfoByLogistic(takeInfo.getTakeId(), takeItemDTOs,
                    finishTakeInfoDTO.getOperator());
            //尝试触发订单自动完成
            orderCommonService.triggerOrderAutoEnd(takeInfo.getOrderId());
        } catch (Exception e) {
            log.info("物流完成发货单异常，异常信息：{}",e.getMessage());
            return new ItemResult<>(false);
        } finally {
            if (orderId != null && lock != null) {
                this.endLock(orderId, lock);
            }
        }
        return new ItemResult<>(true);
    }

    private static void setRealLogisticCostAmountV2(FinishTakeInfoDTO finishTakeInfoDTO, FinishTakeItemDTO cTakeInfo, OrderDTO order, TakeItemDTO takeItemDTO) {
        if (OrderTypeEnum.CONTRACT.getCode().equals(order.getOrderType())) {
            //合同单
            if (order.getOriginLogisticAmount() != null
                    && order.getOriginLogisticAmount().compareTo(BigDecimal.ZERO) > 0) {
                //有物流费，判断为出厂价，价格以物流为准计算补款
                log.info("有物流费，判断为出厂价，价格以物流为准计算补款 ===>{}", finishTakeInfoDTO);
                takeItemDTO.setRealLogisticCostAmount(cTakeInfo.getCost());
            } else {
                //无物流费，判断为到位价格，物流费不计入补款
                log.info("无物流费，判断为到位价格，物流费不计入补款 ===>{}", finishTakeInfoDTO);
                takeItemDTO.setRealLogisticCostAmount(BigDecimal.ZERO);
            }
        }
    }

    private void setRealLogisticCostAmount(FinishTakeInfoDTO finishTakeInfoDTO, FinishTakeItemDTO cTakeInfo, OrderDTO order, TakeItemDTO takeItemDTO) {
        if (OrderTypeEnum.SELF.getCode().equals(order.getOrderType())) {
            //挂牌单
            String orderItemId = cTakeInfo.getResourceId();
            List<TakeItem> takeItems = takeItemBiz.selectItemByOrderItemId(orderItemId);
            log.info("价格以物流为准计算补款 查询takeItem结果 ===>{}",takeItems);
            if (takeItems != null && !takeItems.isEmpty()) {
                TakeItem takeItem = takeItems.get(0);
                String resourceId = takeItem.getResourceId();
                ResourceDTO resourceDTO = resourceService.getResourceDetail(resourceId);
                log.info("查询resourceDTO结果 ===>{}",resourceDTO);
                if (order.getOriginLogisticAmount() != null
                        && order.getOriginLogisticAmount().compareTo(BigDecimal.ZERO) > 0) {
                    log.info("需要计算物流费 ===>{}", finishTakeInfoDTO);
                    takeItemDTO.setRealLogisticCostAmount(cTakeInfo.getCost());
                } else {
                    log.info("不需要计算物流费 ===>{}", finishTakeInfoDTO);
                    takeItemDTO.setRealLogisticCostAmount(BigDecimal.ZERO);
                }
            } else {
                log.error("无法查询takeItem结果 orderItemId===>{}",orderItemId);
                takeItemDTO.setRealLogisticCostAmount(BigDecimal.ZERO);
            }
        }
    }

    @AddLog(operatorIndex = 0, operatorFieldName = "operator")
    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED, rollbackFor = Exception.class)
    public ItemResult<Boolean> doLogisticUpdateShippedQuantity(UpdateTakeInfoShippedDTO updateTakeInfoShippedDTO) {
        log.info("物流更新发货单发货数量 {}", updateTakeInfoShippedDTO);
        Map<String, String> shippedDetails = new HashMap<>();
        Map<String, String> takeCostDetails = new HashMap<>();
        Map<String, BigDecimal> actualQuantityDetails = new HashMap<>();
        String lock = null;
        String orderId = null;
        try {
            TakeInfoDTO takeInfo = this.takeInfoService.getTakeInfoByCode(updateTakeInfoShippedDTO.getTakeCode());
            orderId = takeInfo.getOrderId();
            OrderDTO order = this.orderService.getOrderAndItemInfo(orderId).getData();
            lock = this.startLock(orderId);
            ItemResult<SpecialGoodsAttributeDTO> goodsResult =
                    goodsService.getSpecialGoodsAttribute(order.getOrderItems().get(0).getGoodsId());
            log.info("SpecialGoodsAttributeDTO: {}", JSON.toJSONString(goodsResult.getData()));
            if (!goodsResult.isSuccess()) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "查询商品特殊属性失败");
            }
            Map<String, OrderItemDTO> orderItemMap = new HashMap<>();
            order.getOrderItems().stream().forEach(orderItemDTO -> orderItemMap.put(orderItemDTO.getOrderItemId(), orderItemDTO));
            log.info("orderItemMap:" + JSON.toJSONString(orderItemMap));
            updateTakeInfoShippedDTO.getUpdateTakeItemShippedDTO().forEach(itemShippedDTO -> {
                shippedDetails.put(itemShippedDTO.getResourceId(), itemShippedDTO.getShippedQuantity().toString());
                actualQuantityDetails.put(itemShippedDTO.getResourceId(), itemShippedDTO.getTotalActualQuantity());
                takeCostDetails.put(itemShippedDTO.getResourceId(), "0");
                if (orderItemMap.get(itemShippedDTO.getResourceId()) == null ||
                        order.getOriginLogisticAmount() == null ||
                        order.getOriginLogisticAmount().compareTo(BigDecimal.ZERO) == 0) return;
                OrderItemDTO currentOrderItem = orderItemMap.get(itemShippedDTO.getResourceId());
                BigDecimal logisticUnitPrice = ArithUtils.divide(currentOrderItem.getActualLogisticPrice(),
                        ArithUtils.multiply(currentOrderItem.getItemQuantity(), currentOrderItem.getConvertRate()));
                if (logisticUnitPrice.compareTo(BigDecimal.ZERO) > 0) {
                    takeCostDetails.put(itemShippedDTO.getResourceId(),
                            logisticUnitPrice.multiply(itemShippedDTO.getShippedQuantity()).toString());
                }
            });

            this.takeInfoService.updateSendQuantity(takeInfo.getTakeId(), updateTakeInfoShippedDTO.getOperator(),
                    shippedDetails, takeCostDetails, actualQuantityDetails);
            log.info("物流更新发货单发货数量 开始发送配送短信 {}", updateTakeInfoShippedDTO);

            sendStartShippingAsync(updateTakeInfoShippedDTO, takeInfo);

            /**
             * 判断商品是否在出场做realTime和超发
             * 先超发再realTime
             */
            log.info("orderDTO: {}", JSON.toJSONString(order));
            //计算订单实发金额
            BigDecimal actualQuantity = updateTakeInfoShippedDTO.getUpdateTakeItemShippedDTO().get(0).getTotalActualQuantity();
            orderService.updateRealTimeOrderAmount(orderId, AdjustAddWayEnum.NULL_SIGIN.getCode(), actualQuantity, LOGISTICS);
            //非商品按车分账
            setSplitTriggerDTOS(updateTakeInfoShippedDTO, goodsResult, shippedDetails, orderId, takeCostDetails, order);

            if (orderErpBiz.hasErpOrder(order.getSellerId(), MemberConfigEnum.ERP_ORDER_FLG.getKeyCode())) {
                log.info(" >>> ERP，自动生成ERP余额补款记录和超发记录");
                orderErpBiz.erpOverslodSupplement(order.getOrderId(), "erp");
            }
            log.info("物流更新发货单发货数量 处理完成{}", updateTakeInfoShippedDTO);
        } catch (BizException be) {
            throw be;
        } catch (Exception e) {
            log.info("物流更新发货单发货数量异常 异常信息{}", e);
            throw new BizException(BasicCode.UNDEFINED_ERROR, "更新发货数量异常");
        } finally {
            if (orderId != null && lock != null) {
                this.endLock(orderId, lock);
            }
        }
        return new ItemResult<>(true);
    }

    private void sendStartShippingAsync(UpdateTakeInfoShippedDTO updateTakeInfoShippedDTO, TakeInfoDTO takeInfo) {
        if (CsStringUtils.isNotEmpty(updateTakeInfoShippedDTO.getWaybillNum())) {
            List<String> waybillNums = new ArrayList<>();
            waybillNums.add(updateTakeInfoShippedDTO.getWaybillNum());
            orderSMSMessageProducer.sendStartShippingAsync(takeInfo.getOrderId(), waybillNums);
        } else {
            log.info("物流更新发货单发货数量 未收到物流单据，不发送短信 {}", updateTakeInfoShippedDTO);
        }
    }

    private void setSplitTriggerDTOS(UpdateTakeInfoShippedDTO updateTakeInfoShippedDTO, ItemResult<SpecialGoodsAttributeDTO> goodsResult, Map<String, String> shippedDetails, String orderId, Map<String, String> takeCostDetails, OrderDTO order) {
        if (goodsResult.getData().getConcreteFlag() != 1) {
            log.info("超发验证：" + JSON.toJSONString(shippedDetails));
            //超发验证
            doCheckOversold(shippedDetails, orderId, updateTakeInfoShippedDTO.getWaybillNum());
            //触发分账
            List<SplitTriggerDTO> splitTriggerDTOS = new ArrayList<>();
            shippedDetails.forEach((key, value)->{
                SplitTriggerDTO splitTriggerDTO = new SplitTriggerDTO();
                splitTriggerDTO.setResourceId(key);
                splitTriggerDTO.setNum(new BigDecimal(value));
                splitTriggerDTO.setLogisticsAmount(new BigDecimal(takeCostDetails.get(key)));
                List<OrderPayinfoDetail> paylist = orderPayinfoDetailBiz.findByOrderId(order.getOrderId());
                for(OrderPayinfoDetail detail:paylist){
                    if(PayDetailTypeEnum.LOGISTICS.code().equals(detail.getPaytypeDetail())){
                        splitTriggerDTO.setMemberId(detail.getPayeeId());
                        break;
                    }
                }
                splitTriggerDTOS.add(splitTriggerDTO);
            });
            billSplitBiz.triggerSplit(splitTriggerDTOS, orderId, order.getOrderCode(), updateTakeInfoShippedDTO.getWaybillNum());
        }
    }

    /**
     * 物流签收参数校验
     * @param updateTakeInfoSignDTO 签收对象
     */
    private void logisticsSignParamValidate(UpdateTakeInfoSignDTO updateTakeInfoSignDTO) {
        if (CsStringUtils.isEmpty(updateTakeInfoSignDTO.getTakeCode())) {
            throw new BizException(BasicCode.INVALID_PARAM, "发货单号");
        }
        if (CsStringUtils.isEmpty(updateTakeInfoSignDTO.getOperator())) {
            throw new BizException(BasicCode.INVALID_PARAM, "操作人");
        }
        if (CsStringUtils.isEmpty(updateTakeInfoSignDTO.getAdjustAddWay())) {
            throw new BizException(BasicCode.INVALID_PARAM, "签收方式");
        }
        if (CollectionUtils.isEmpty(updateTakeInfoSignDTO.getUpdateTakeItemSignDTOs())) {
            throw new BizException(BasicCode.INVALID_PARAM, "签收商品列表");
        }
        for (UpdateTakeItemSignDTO itemSign : updateTakeInfoSignDTO.getUpdateTakeItemSignDTOs()) {
            if (CsStringUtils.isEmpty(itemSign.getResourceId())) {
                throw new BizException(BasicCode.INVALID_PARAM, "订单明细ID");
            }
            if (itemSign.getBuyerSignQuantity() == null && itemSign.getSellerSignQuantity() == null) {
                throw new BizException(BasicCode.INVALID_PARAM, "签收数量");
            }
            if (itemSign.getEmptyLoadCost() == null) {
                throw new BizException(BasicCode.INVALID_PARAM, "空载费");
            }
            if (itemSign.getAdjustQuantity() == null) {
                throw new BizException(BasicCode.INVALID_PARAM, "签收调整量");
            }
        }
    }

    @AddLog(operatorIndex = 0, operatorFieldName = "operator")
    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED, rollbackFor = Exception.class)
    public ItemResult<Boolean> doLogisticUpdateSignQuantity(UpdateTakeInfoSignDTO updateTakeInfoSignDTO) {
        log.info("物流更新发货单签收数量" + JSON.toJSONString(updateTakeInfoSignDTO));
        //参数验证
        logisticsSignParamValidate(updateTakeInfoSignDTO);
        String adjustAddWay = updateTakeInfoSignDTO.getAdjustAddWay();
        //添加分布式锁控制
        String lock = null;
        String orderId = null;
        try {
            TakeInfoDTO takeInfo = this.takeInfoService.getTakeInfoByCode(updateTakeInfoSignDTO.getTakeCode());
            orderId = takeInfo.getOrderId();
            OrderDTO order = this.orderService.getOrderAndItemInfo(orderId).getData();
            lock = this.startLock(orderId);
            //物流签收时创建订单调价记录
            OrderAdjustDTO orderAdjustDTO = new OrderAdjustDTO();
            orderAdjustDTO.setOrderId(takeInfo.getOrderId());
            orderAdjustDTO.setCarNum(takeInfo.getCarNum());
            //设置签收方式
            orderAdjustDTO.setAddWay(adjustAddWay);
            //都是到位价没有物流费记录,所以不用加物流费
            List<UpdateTakeItemSignDTO> updateTakeItemSignDTOS = updateTakeInfoSignDTO.getUpdateTakeItemSignDTOs();
            UpdateTakeItemSignDTO updateTakeItemSignDTO = updateTakeItemSignDTOS.get(0);
            if (updateTakeItemSignDTO == null) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "物流更新签收数量子项对象不能为空");
            }
            OrderItemDTO orderItemDTO = order.getOrderItems().get(0);
            BigDecimal logisticUnitPrice = orderItemDTO.getActualLogisticPrice()
                    .divide(orderItemDTO.getItemQuantity(), 2, RoundingMode.HALF_UP);
            BigDecimal adjustLogisticsCost = logisticUnitPrice.multiply(updateTakeItemSignDTO.getAdjustQuantity());
            //修改发货单，订单签收数量（买家签收）
            if (AdjustAddWayEnum.BUYER_SIGIN.getCode().equals(adjustAddWay)) {
                takeInfoBiz.updateTakeInfoSignQuantity(
                        takeInfo.getTakeId(),
                        updateTakeItemSignDTO.getBuyerSignQuantity(),
                        adjustLogisticsCost,
                        adjustAddWay, updateTakeItemSignDTO.getTotalActualQuantity());
                //修改订单签收数量(订单只有一个商品，也就只有一个订单子项)
                orderItemDTO.setItemSignQuantity(orderItemDTO.getItemSignQuantity() == null ?
                        BigDecimal.ZERO : orderItemDTO.getItemSignQuantity());
                orderItemDTO.setActualEmptyPrice(orderItemDTO.getActualEmptyPrice() == null ?
                        BigDecimal.ZERO : orderItemDTO.getActualEmptyPrice());
                orderItemQuantityBiz.addOrderItemSignQuantity(orderItemDTO.getOrderItemId(),
                        orderItemDTO.getItemSignQuantity().add(updateTakeItemSignDTO.getBuyerSignQuantity()),
                        orderItemDTO.getActualEmptyPrice().add(updateTakeItemSignDTO.getEmptyLoadCost()));
            }

            //修改发货单，订单签收数量（卖家签收）
            if (AdjustAddWayEnum.SELLER_SIGIN.getCode().equals(adjustAddWay)) {
                takeInfoBiz.updateTakeInfoSignQuantity(
                        takeInfo.getTakeId(),
                        updateTakeItemSignDTO.getSellerSignQuantity(),
                        adjustLogisticsCost,
                        adjustAddWay, updateTakeItemSignDTO.getTotalActualQuantity());
                //修改订单签收数量
                orderItemDTO.setItemSignQuantity(orderItemDTO.getItemSignQuantity() == null ?
                        BigDecimal.ZERO : orderItemDTO.getItemSignQuantity());
                orderItemDTO.setActualEmptyPrice(orderItemDTO.getActualEmptyPrice() == null ?
                        BigDecimal.ZERO : orderItemDTO.getActualEmptyPrice());
                orderItemQuantityBiz.addOrderItemSignQuantity(orderItemDTO.getOrderItemId(),
                        orderItemDTO.getItemSignQuantity().add(updateTakeItemSignDTO.getSellerSignQuantity()),
                        orderItemDTO.getActualEmptyPrice().add(updateTakeItemSignDTO.getEmptyLoadCost()));
            }
            //按签收量重新计算实际物流和商品费用
            orderService.updateRealTimeOrderAmount(
                    order.getOrderId(),
                    AdjustAddWayEnum.SELLER_SIGIN.getCode(),
                    updateTakeItemSignDTO.getTotalActualQuantity(),
                    LOGISTICS);
            return new ItemResult<>(true);
        } catch (Exception e) {
            log.info("物流更新发货单签收数量异常：" + JSON.toJSONString(e));
            return new ItemResult<>(false);
        } finally {
            if (orderId != null && lock != null) {
                this.endLock(orderId, lock);
            }
        }

    }

    @Override
    @Transactional
    public ItemResult<Boolean> doCheckOversold(Map<String, String> signDetails, String orderId, String takeCode) {
        log.info("物流超发判断： {}", orderId);
        String lock = null;

        try {
            lock = this.startLock(orderId + "-" + LOGISTICS);
            OrderDTO order = this.orderService.getOrderAndItemInfo(orderId).getData();
            //如果ERP运单出站后电商有未支付的待补款记录，则ERP自动完成了余额补款，直接修改补款支付单状态和类型为完成
            if (orderErpBiz.hasErpOrder(order.getSellerId(), MemberConfigEnum.ERP_ORDER_FLG.getKeyCode())) {
                orderPaymentService.erpSupplementComplete(orderId, LOGISTICS);
                return new ItemResult<>(Boolean.TRUE);
            }
            String result = this.orderOverSellBiz.hadOverSell(takeCode);
            if (OverSellValidateResultEnum.EXIST_AND_SUCCESS.getCode().equals(result)) {
                return new ItemResult<>(true);
            } else if(OverSellValidateResultEnum.EXIST_BUT_ERROR.getCode().equals(result)) {
                return new ItemResult<>(false);
            }
            /**
             *  step2 计算出实际发货金额,如果未超发，返回true
             */
            BigDecimal realOrderResourceAmount = order.getRealtimeResourceAmount();
            BigDecimal realOrderLogisticsAmount = order.getRealtimeLogisticAmount();
            if ((realOrderResourceAmount.add(realOrderLogisticsAmount)).compareTo(order.getActualOrderAmount().subtract(order.getActualOthersAmount())) <= 0) {
                log.info("  >> 订单未超发 orderId: {}", orderId);
                return new ItemResult<>(true);
            }
            /**
             *  step3 触发超发，修改订单实际支付信息
             */
            BigDecimal overResourceAmount = BigDecimal.ZERO;
            BigDecimal overLogisticsAmount = BigDecimal.ZERO;
            //获取当前订单已支付金额
            PaymentAmountQueryDO paymentAmountQueryDO = new PaymentAmountQueryDO();
            paymentAmountQueryDO.setOrderId(orderId);
            paymentAmountQueryDO.setPayStatusList(Lists.newArrayList(
                    PayStatusEnum.IN_PAYMENT.code(),
                    PayStatusEnum.COMPLETED.code()
            ));
            PaymentAmountDO paymentAmountDO = orderPaymentService.computeOrderPaymentAmount(paymentAmountQueryDO);
            overResourceAmount = realOrderResourceAmount.subtract(paymentAmountDO.getPaymentGoodsAmount()).setScale(2, RoundingMode.HALF_UP);
            overLogisticsAmount = realOrderLogisticsAmount.subtract(paymentAmountDO.getPaymentLogisticsAmount()).setScale(2, RoundingMode.HALF_UP);

            if (overResourceAmount.compareTo(BigDecimal.ZERO) > 0 || overLogisticsAmount.compareTo(BigDecimal.ZERO) > 0) {
                log.info("  >> 订单已超发 orderId: {}", orderId);
                SupplementPaymentDO supplementPaymentDO = new SupplementPaymentDO();
                supplementPaymentDO.setOverResourceAmount(overResourceAmount);
                supplementPaymentDO.setOverLogisticsAmount(overLogisticsAmount);
                supplementPaymentDO.setOverOthersAmount(BigDecimal.ZERO);
                supplementPaymentDO.setOrder(order);
                supplementPaymentDO.setOperator(LOGISTICS);
                OrderPayinfo payinfo = null;//当前类已弃用 this.orderPayinfoJoinBiz.createOversoldPayInfo(supplementPaymentDO);
                //调用超发接口
                List<OversoldDTO> oversoldDTOList = new ArrayList<>();
                BigDecimal oversoldAmount = overResourceAmount.add(overLogisticsAmount);
                signDetails.forEach((key,value) -> {
                    OversoldDTO oversoldDTO = new OversoldDTO();
                    oversoldDTO.setCurrency(payinfo.getCurrency());
                    oversoldDTO.setCreateTime(new Date());
                    oversoldDTO.setCreateUser(LOGISTICS);
                    oversoldDTO.setOrderId(order.getOrderId());
                    oversoldDTO.setLogisticsId(takeCode);
                    oversoldDTO.setOversoldAmount(oversoldAmount);
                    oversoldDTO.setOversoldNum(new BigDecimal(value));
                    oversoldDTO.setResourceId(key);
                    oversoldDTO.setPayinfoId(payinfo.getPayinfoId());
                    oversoldDTO.setSource(LOGISTICS);
                    oversoldDTOList.add(oversoldDTO);
                });
                boolean overResult = false;//当前类已弃用 orderOverSellBiz.triggerOverSold(payinfo,oversoldDTOList);

                OrderInfo orderInfo = new OrderInfo();
                orderInfo.setOrderId(orderId);
                orderInfo.setOrderPayedAmount(ArithUtils.add(order.getOrderPayedAmount(), oversoldAmount).setScale(2, RoundingMode.HALF_UP));
                orderService.oversoldUpdate(orderInfo,LOGISTICS);
                return new ItemResult<>(overResult);
            } else {
                /**
                 *  step4 计算出实际超发金额,如果未超发，返回true
                 */
                log.info("  >> 订单未超发 orderId: {}", orderId);
                return new ItemResult<>(true);
            }
        } catch (Exception e) {
            log.error("物流超发判断异常 异常信息{}", e);
            throw new BizException(BasicCode.UNDEFINED_ERROR, "物流超发判断异常");
        } finally {
            if (orderId != null && lock != null) {
                this.endLock(orderId + "-" + LOGISTICS, lock);
            }
        }
    }


    /**
     * 超发参数验证
     * @param overSendOutVerifyDTO 超发验证参数对象
     */
    private void overSendOutParamValidate(OverSendOutVerifyDTO overSendOutVerifyDTO) {
        if (CsStringUtils.isEmpty(overSendOutVerifyDTO.getTakeCode())) {
            throw new BizException(BasicCode.INVALID_PARAM, "发货单号");
        }
        if (CsStringUtils.isEmpty(overSendOutVerifyDTO.getWaybillNum())) {
            throw new BizException(BasicCode.INVALID_PARAM, "运单号");
        }
        if (CsStringUtils.isEmpty(overSendOutVerifyDTO.getOrderItemId())) {
            throw new BizException(BasicCode.INVALID_PARAM, "订单明细ID");
        }
        if (overSendOutVerifyDTO.getQuantity() == null) {
            throw new BizException(BasicCode.INVALID_PARAM, "运单计划提货量");
        }
        if (overSendOutVerifyDTO.getActualQuantity() == null) {
            throw new BizException(BasicCode.INVALID_PARAM, "运单实际出厂量");
        }
        if (overSendOutVerifyDTO.getLogisticsAmount() == null) {
            throw new BizException(BasicCode.INVALID_PARAM, "运单物流费");
        }
        if (CsStringUtils.isEmpty(overSendOutVerifyDTO.getOperator())) {
            throw new BizException(BasicCode.INVALID_PARAM, "操作人");
        }
    }

    @Override
         public ItemResult<Void> notifyLeaveWarehouse(OrderPayinfo orderPayinfo) {
        log.info("notifyLeaveWarehouse:" + JSON.toJSONString(orderPayinfo));
        OrderDTO order = orderQueryService.getOrderInfo(orderPayinfo.getObjectId());
        //获取仓库信息
        WarehouseDetailsDTO warehouseDetailsDTO = warehouseService.queryWarehouseDetails(order.getOrderItems().get(0).getStoreId());
        if (warehouseDetailsDTO == null) {
            throw new BizException(BasicCode.DATA_NOT_EXIST, "订单仓库：" + order.getOrderItems().get(0).getStoreId());
        }
        Boolean notifyFlag = Boolean.TRUE;
        if (orderErpBiz.hasErpOrder(order.getSellerId(), MemberConfigEnum.ERP_LOGISTICS_FLG.getKeyCode())) {
            //ERP卖家且非中心仓提货不需要通知物流出站
            if (!CsStringUtils.equals(WarehouseTypeEnum.CENTRAL_WAREHOUSE.getCode(), warehouseDetailsDTO.getType())) {
                notifyFlag = Boolean.FALSE;
            }
        }
        if (notifyFlag) {
            //获取支付单关联的超发运单信息
            OversoldQueryDTO oversoldQueryDTO = new OversoldQueryDTO();
            oversoldQueryDTO.setPayInfoId(orderPayinfo.getPayinfoId());
            List<OversoldDTO> overSoldList = orderOverSellBiz.queryOversold(oversoldQueryDTO);
            if (CollectionUtils.isEmpty(overSoldList)) {
                return new ItemResult<>(null);
            }
            List<String> waybillNumList = overSoldList.stream().map(OversoldDTO::getLogisticsId).toList();
            NotifyLeaveWarehouseDTO notifyLeaveWarehouseDTO = new NotifyLeaveWarehouseDTO();
            notifyLeaveWarehouseDTO.setWaybillNumList(waybillNumList);
            notifyLeaveWarehouseDTO.setCentralWarehouseFlag(CsStringUtils.equals(WarehouseTypeEnum.CENTRAL_WAREHOUSE.getCode(),
                    warehouseDetailsDTO.getType()) ? 1 : 0);
            try {
                //补款完成,异步通知物流出站
                messageService.sendMQ(notifyLeaveWarehouseDTO, "com.ecommerce.order.supplement.complete.notify");
            } catch (Exception e) {
                log.error("通知物流出站异常：" + JSON.toJSONString(notifyLeaveWarehouseDTO));
            }
        } else {
            orderErpBiz.notifyErpIncreaseCreditAmount(orderPayinfo.getPayinfoId());
        }

        return new ItemResult<>(null);
    }
}
