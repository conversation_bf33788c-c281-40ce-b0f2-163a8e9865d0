package com.ecommerce.order.service.order.base;

import com.alibaba.fastjson.JSON;
import com.ecommerce.base.api.dto.ReceivingAddressDTO;
import com.ecommerce.base.api.dto.warehouse.WarehouseDetailsDTO;
import com.ecommerce.base.api.service.IReceivingAddressService;
import com.ecommerce.base.api.service.IWarehouseService;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.service.common.BaseBiz;
import com.ecommerce.common.service.common.bsid.CommonBusinessIdGenerator;
import com.ecommerce.common.service.common.uuid.UUIDGenerator;
import com.ecommerce.common.utils.ArithUtils;
import com.ecommerce.common.utils.BeanConvertUtils;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.goods.api.dto.AddItemQueryConditionDTO;
import com.ecommerce.goods.api.dto.GoodsAddItemDTO;
import com.ecommerce.goods.api.dto.GoodsDTO;
import com.ecommerce.goods.api.dto.ResourceDTO;
import com.ecommerce.goods.api.dto.SellerConfirmJudgeDTO;
import com.ecommerce.goods.api.dto.SpecialGoodsAttributeDTO;
import com.ecommerce.goods.api.dto.contract.TrContractDTO;
import com.ecommerce.goods.api.dto.contract.TrContractGoodsDTO;
import com.ecommerce.goods.api.enums.PaydateTypeEnum;
import com.ecommerce.goods.api.enums.TradeStatusEnum;
import com.ecommerce.goods.api.service.IContractService;
import com.ecommerce.goods.api.service.IGoodsAddItemService;
import com.ecommerce.goods.api.service.IGoodsService;
import com.ecommerce.goods.api.service.IResourceNumTCCService;
import com.ecommerce.goods.api.service.IResourceService;
import com.ecommerce.logistics.api.dto.rulecompute.CarriageComputeDTO;
import com.ecommerce.logistics.api.dto.rulecompute.CarriageRuleResultDTO;
import com.ecommerce.logistics.api.dto.rulecompute.CategoryQuantityDTO;
import com.ecommerce.logistics.api.enums.PickingBillTypeEnum;
import com.ecommerce.logistics.api.service.IPorterageService;
import com.ecommerce.logistics.api.service.IRuleComputeService;
import com.ecommerce.member.api.dto.TaxInfoDetailDTO;
import com.ecommerce.member.api.dto.account.AccountDTO;
import com.ecommerce.member.api.dto.member.MemberDTO;
import com.ecommerce.member.api.dto.member.MemberSimpleDTO;
import com.ecommerce.member.api.dto.member.config.MemberConfigDTO;
import com.ecommerce.member.api.dto.member.config.MemberConfigEnum;
import com.ecommerce.member.api.service.IAccountService;
import com.ecommerce.member.api.service.IBuyerAndReferrerService;
import com.ecommerce.member.api.service.IMemberAreaService;
import com.ecommerce.member.api.service.IMemberConfigService;
import com.ecommerce.member.api.service.IMemberRelationService;
import com.ecommerce.member.api.service.IMemberService;
import com.ecommerce.member.api.service.ITaxInfoService;
import com.ecommerce.open.api.dto.MessageConfigCodeEnum;
import com.ecommerce.order.api.dto.BuyerSelectInfoDTO;
import com.ecommerce.order.api.dto.OrderDTO;
import com.ecommerce.order.api.dto.OrderDiscountDetailDTO;
import com.ecommerce.order.api.dto.OrderItemAddDTO;
import com.ecommerce.order.api.dto.OrderItemDTO;
import com.ecommerce.order.api.dto.OrderPayinfoDTO;
import com.ecommerce.order.api.dto.OrderPayinfoDetailDTO;
import com.ecommerce.order.api.dto.OrderTaxinfoDTO;
import com.ecommerce.order.api.dto.UpdateTakeInfoShippedDTO;
import com.ecommerce.order.api.dto.base.SendSMSMessageDTO;
import com.ecommerce.order.api.dto.information.MemberIntegralMqMessageDTO;
import com.ecommerce.order.api.dto.information.MemberIntegralMqMessageGoodsInfoDTO;
import com.ecommerce.order.api.enums.AdjustStatusEnum;
import com.ecommerce.order.api.enums.CarryWayEnum;
import com.ecommerce.order.api.enums.CreateWayEnum;
import com.ecommerce.order.api.enums.DiscountTypeEnum;
import com.ecommerce.order.api.enums.OperatorTypeEnum;
import com.ecommerce.order.api.enums.OrderInvoiceStatus;
import com.ecommerce.order.api.enums.OrderStatusEnum;
import com.ecommerce.order.api.enums.OrderTakeStatus;
import com.ecommerce.order.api.enums.OrderTypeEnum;
import com.ecommerce.order.api.enums.PayStatusEnum;
import com.ecommerce.order.biz.IBillSplitBiz;
import com.ecommerce.order.biz.IOrderCloseBiz;
import com.ecommerce.order.biz.IOrderErpAddressBiz;
import com.ecommerce.order.biz.IOrderErpBiz;
import com.ecommerce.order.biz.ITakeInfoBiz;
import com.ecommerce.order.biz.impl.BaseOrderSearchBiz;
import com.ecommerce.order.biz.impl.OrderPayinfoBiz;
import com.ecommerce.order.dao.mapper.OrderDiscountDetailMapper;
import com.ecommerce.order.dao.mapper.OrderInfoMapper;
import com.ecommerce.order.dao.mapper.OrderItemAddMapper;
import com.ecommerce.order.dao.mapper.OrderItemMapper;
import com.ecommerce.order.dao.mapper.OrderPayinfoDetailMapper;
import com.ecommerce.order.dao.mapper.OrderPayinfoMapper;
import com.ecommerce.order.dao.mapper.OrderRefundMapper;
import com.ecommerce.order.dao.mapper.OrderResoureChangeMapper;
import com.ecommerce.order.dao.mapper.OrderTaxinfoMapper;
import com.ecommerce.order.dao.vo.OrderDiscountDetail;
import com.ecommerce.order.dao.vo.OrderInfo;
import com.ecommerce.order.dao.vo.OrderItem;
import com.ecommerce.order.dao.vo.OrderItemAdd;
import com.ecommerce.order.dao.vo.OrderPayinfo;
import com.ecommerce.order.dao.vo.OrderPayinfoDetail;
import com.ecommerce.order.dao.vo.OrderTaxinfo;
import com.ecommerce.order.fsm.service.IOrderPaymentService;
import com.ecommerce.order.fsm.service.IOrderQueryService;
import com.ecommerce.order.fsm.service.ITakeInfoService;
import com.ecommerce.order.machine.factory.IStatusMachineFactory;
import com.ecommerce.order.service.IBuyerSelectInfoService;
import com.ecommerce.order.service.ICartResourceService;
import com.ecommerce.order.service.IOrderPayinfoService;
import com.ecommerce.order.service.handler.SynchronizeIntegralHandler;
import com.ecommerce.order.service.impl.OrderSMSMessageProducer;
import com.ecommerce.order.service.message.SMSMessageFactoryContext;
import com.ecommerce.order.service.order.IOrderComputeService;
import com.ecommerce.price.api.dto.BuyerPromotionalDetailsDTO;
import com.ecommerce.price.api.dto.PromotionalDTO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;


@Slf4j
public abstract class AbstractOrderService implements IOrderService {

    @Autowired
    protected IAccountService accountService;

    @Autowired
    protected UUIDGenerator uuidGenerator;

    @Autowired
    protected OrderInfoMapper orderInfoMapper;

    @Autowired
    protected OrderTaxinfoMapper orderTaxinfoMapper;

    @Autowired
    protected OrderPayinfoMapper orderPayinfoMapper;

    @Autowired
    protected OrderPayinfoDetailMapper orderPayinfoDetailMapper;

    @Autowired
    protected OrderRefundMapper orderRefundMapper;

    @Autowired
    protected OrderResoureChangeMapper orderResoureChangeMapper;

    @Autowired
    protected OrderDiscountDetailMapper orderDiscountDetailMapper;

    @Autowired
    protected OrderItemMapper orderItemMapper;

    @Autowired
    protected OrderItemAddMapper orderItemAddMapper;

    @Autowired
    protected IResourceNumTCCService resourceNumTCCService;

    @Autowired
    protected IGoodsService goodsService;

    @Autowired
    protected IResourceService resourceService;

    @Autowired
    protected IMemberService memberService;

    @Autowired
    protected IMemberConfigService memberConfigService;

    @Autowired
    protected IMemberAreaService memberAreaService;

    @Autowired
    protected IMemberRelationService memberRelationService;

    @Autowired
    protected IBuyerAndReferrerService buyerAndReferrerService;

    @Autowired
    protected IReceivingAddressService receivingAddressService;

    @Autowired
    protected IBuyerSelectInfoService buyerSelectInfoService;

    @Autowired
    protected IRuleComputeService ruleComputeService;

    @Autowired
    protected CommonBusinessIdGenerator businessIdGenerator;

    @Autowired
    protected ICartResourceService cartResourceService;

    @Autowired
    protected ITakeInfoService takeInfoService;

    @Autowired
    protected ITakeInfoBiz takeInfoBiz;

    @Autowired
    protected ITaxInfoService taxInfoService;

    @Autowired
    protected IBillSplitBiz billSplitBiz;

    @Autowired
    protected OrderSMSMessageProducer messageProducer;

    @Autowired
    protected IWarehouseService warehouseService;

    @Autowired
    protected IPorterageService porterageService;

    @Autowired
    protected BaseOrderSearchBiz baseOrderSearchBiz;

    @Autowired
    protected OrderPayinfoBiz orderPayinfoBiz;

    @Autowired
    private IContractService contractService;

    @Autowired
    protected SynchronizeIntegralHandler syncIntegralHandler;

    @Autowired
    protected IOrderErpBiz orderErpBiz;

    @Autowired
    private IOrderCloseBiz orderCloseBiz;
    @Autowired
    private IGoodsAddItemService goodsAddItemService;

    @Autowired
    private IOrderComputeService orderComputeService;

    @Autowired
    private IOrderQueryService orderQueryService;

    @Autowired
    private IStatusMachineFactory statusMachineFactory;
    @Value("${spring.profiles.active:dev}")
    private String profile;

    @Autowired
    private SMSMessageFactoryContext smsMessageFactoryContext;

    @Autowired
    private IOrderErpAddressBiz orderErpAddressBiz;

    @Autowired
    private IOrderPayinfoService orderPayinfoService;

    @Autowired
    private IOrderPaymentService orderPaymentService;
    @Autowired
    private AbstractOrderService orderService;

    /**
     * 是否处理资源可售数量
     *
     * @return
     */
    public abstract boolean isProcessResSaleNum();

    /**
     * 是否处理优惠信息
     *
     * @return
     */
    public abstract boolean isProcessPromotional();

    /**
     * 订单数据准备
     *
     * @param orderDTO 订单业务对象
     * @return OrderDTO
     */
    public OrderDTO prepareOrder(OrderDTO orderDTO) {
        return orderDTO;
    }

    public void validateOrder(OrderDTO order, Boolean createFlag) {
        order.setInvoiceStatus(OrderInvoiceStatus.UNINVOICE.getCode());
        if (order.getMachineShiftCost() != null || order.getOthersAmount() != null) {//买家下单时输入台班费、其他费用
            order.setActualOthersAmount(ArithUtils.add(order.getMachineShiftCost(), order.getOthersAmount()));
        }
        if (order.getActualOthersAmount() == null) {
            order.setActualOthersAmount(order.getOriginOthersAmount());
        }
        String sellerId = order.getSellerId();
        /*
         * 订单类型
         */
        OrderTypeEnum orderType = OrderTypeEnum.getByCode(order.getOrderType());
        if (orderType == null) {
            throw new BizException(BasicCode.INVALID_PARAM, "订单类型");
        }
        if (orderType.getCode().equals(OrderTypeEnum.CONTRACT.getCode())) {
            TrContractDTO contract = contractService.getContract(order.getDealsId());
            log.info("查询合同信息：{}", contract);
            if (contract == null || CollectionUtils.isEmpty(contract.getContractGoodsDTOS())) {
                log.error("合同信息不存在或不完整：contract:{}", contract);
                throw new BizException(BasicCode.DB_DATA_NOT_FOUND, "合同信息不存在或不完整");
            }
            sellerId = contract.getSellerId();
            order.setDealsName(contract.getContractSequence());
        }
        /*
         * 买家信息
         */
        String buyerId = getBuyerId(order);
        //获取买家信息
        MemberSimpleDTO buyer = getMemberSimpleDTO(buyerId);
        order.setBuyerName(buyer.getMemberName());
        if (buyer.getMemberType().startsWith(MemberDTO.ENTERPRISE_TYPE_PRE)) {
            order.setBuyerType(MemberDTO.ENTERPRISE_TYPE_PRE);
        } else {
            order.setBuyerType(MemberDTO.PERSON_TYPE_PRE);
        }
        order.setBuyerContact(buyer.getContactName());
        order.setBuyerContactWay(buyer.getContactPhone());

        //物流配送方式
        PickingBillTypeEnum deliverWay = PickingBillTypeEnum.valueOfCode(order.getDeliverWay());
        if (deliverWay == null) {
            throw new BizException(BasicCode.INVALID_PARAM, "配送方式" + CsStringUtils.emptyIfNull(order.getDeliverWay()));
        }
        String addressId = order.getAddressId();//收货地址Id,包括自提地址
        if (CsStringUtils.isBlank(addressId)) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "请选择收货地址");
        }
        ReceivingAddressDTO addressDTO = getReceivingAddressDTO(addressId, buyerId);
        order.setProvinceCode(addressDTO.getProvinceCode());
        order.setProvinceName(addressDTO.getProvinceName());
        order.setCityCode(addressDTO.getCityCode());
        order.setCityName(addressDTO.getCityName());
        order.setDistrictCode(addressDTO.getDistrictCode());
        order.setDistrictName(addressDTO.getDistrictName());
        order.setStreetCode(addressDTO.getStreetCode());
        order.setStreetName(addressDTO.getStreetName());
        order.setAddressDetail(addressDTO.getAddress().concat(addressDTO.getAddressDetail()));
        order.setAddressMap(addressDTO.getCoordinate());
        order.setReceiver(addressDTO.getMemberId());
        order.setReceiverPhone(CsStringUtils.defaultIfEmpty(addressDTO.getMobilePhone(), addressDTO.getPhone()));

        /*
         * 获取渠道
         */
        CreateWayEnum createWay = CreateWayEnum.getByCode(order.getCreateWay());
        if (createWay == null) {
            throw new BizException(BasicCode.PARAM_NULL, "获取渠道");
        }
        String storeId = order.getStoreId();
        if (CsStringUtils.isBlank(storeId)) {
            throw new BizException(BasicCode.INVALID_PARAM, "提货点地址");
        }
        WarehouseDetailsDTO warehouse = getWarehouseDetailsDTO(storeId);

        /*
         * 订单行项目
         */
        List<OrderItemDTO> orderItems = order.getOrderItems();
        sellerId = doOrderItemsLogic(order, createFlag, orderItems, storeId, warehouse, buyerId, sellerId, deliverWay);

        //取可以发货的仓库

        /*
         * 卖家信息
         */
        setSellerInfoLogic(order, sellerId);

        /*
         * 判断黑名单关系（myself，target）
         */
        Boolean defriend = memberRelationService.isDefriend(sellerId, buyerId);
        if (defriend) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "您已被此商家加入黑名单，无法购买该商品");
        }
        /**
         * 判断物流费规则
         */
        //非自提订单需要计算物流费
        computeResultLogic(order, deliverWay, orderItems);
        log.info("validateOrder:{}", JSON.toJSONString(order));
    }


    private static String getBuyerId(OrderDTO order) {
        String buyerId = order.getBuyerId();
        if (CsStringUtils.isBlank(buyerId)) {
            throw new BizException(BasicCode.PARAM_NULL, "买家ID");
        }
        return buyerId;
    }


    private MemberSimpleDTO getMemberSimpleDTO(String buyerId) {
        MemberSimpleDTO buyer;
        try {
            buyer = memberService.findMemberSimpleById(buyerId);
        } catch (Exception e) {
            log.error("memberService.findMemberDetailById_Error:" + buyerId, e);
            throw new BizException(BasicCode.UNDEFINED_ERROR, "查询买家信息报错");
        }
        if (buyer == null) {
            throw new BizException(BasicCode.DATA_NOT_EXIST, "买家");
        }
        return buyer;
    }


    private ReceivingAddressDTO getReceivingAddressDTO(String addressId, String buyerId) {
        ReceivingAddressDTO addressDTO;
        try {
            addressDTO = receivingAddressService.findById(addressId);
        } catch (Exception e) {
            log.error("addressService:" + addressId + "," + buyerId, e);
            throw new BizException(BasicCode.UNDEFINED_ERROR, "错误的收货地址");
        }
        if (addressDTO == null) {
            throw new BizException(BasicCode.DB_DATA_NOT_FOUND, "收货地址:" + addressId);
        }
        return addressDTO;
    }


    private WarehouseDetailsDTO getWarehouseDetailsDTO(String storeId) {
        WarehouseDetailsDTO warehouse;
        try {
            log.info("WarehouseService.queryWarehouseDetails_start:{}", storeId);
            warehouse = warehouseService.queryWarehouseDetails(storeId);
            log.info("WarehouseService.queryWarehouseDetails_end:{}", JSON.toJSONString(warehouse));
        } catch (Exception e) {
            log.error("queryWarehouseDetails:" + storeId, e);
            throw new BizException(BasicCode.UNDEFINED_ERROR, "查询仓库信息错误");
        }
        if (warehouse == null) {
            throw new BizException(BasicCode.DB_DATA_NOT_FOUND, "仓库:" + storeId);
        }
        return warehouse;
    }

    private String doOrderItemsLogic(OrderDTO order, Boolean createFlag, List<OrderItemDTO> orderItems, String storeId, WarehouseDetailsDTO warehouse, String buyerId, String sellerId, PickingBillTypeEnum deliverWay) {
        if (CollectionUtils.isEmpty(orderItems)) {
            throw new BizException(BasicCode.PARAM_NULL, "订单商品不存在");
        }
        String resCurrency = null;//币种
        Set<String> supportPayWaySet = null;//支持的支付方式
        for (OrderItemDTO itemDTO : orderItems) {
            String resourceId = itemDTO.getResourceId();
            if (CsStringUtils.isBlank(resourceId)) {
                hasResourceIdLogic(order, createFlag, storeId, warehouse, buyerId, itemDTO);
            } else {
                // 挂牌
                if (CsStringUtils.isBlank(itemDTO.getUnits())) {
                    throw new BizException(BasicCode.PARAM_NULL, "商品销售单位");
                }
                ResourceDTO resource = getResourceDTO(resourceId);
                BigDecimal convertRate = getConvertRate(itemDTO, resource);
                log.info("validateOrder_resource:{},{}", order.getOrderCode(), JSON.toJSONString(resource));
                sellerId = getSellerId(sellerId, resource);
                resCurrency = getResCurrency(resCurrency, resource);

                BigDecimal quantity = getQuantity(itemDTO, resource);
                if (isProcessResSaleNum()) {
                    checkCansaleNum(buyerId, resource, quantity, convertRate, resourceId);
                    //支持的支付方式
                    supportPayWaySet = getPayWaySet(resource, supportPayWaySet);
                    itemDTO.setPayWay(resource.getPayWay());
                    order.setSupportPayWay(Lists.newArrayList(supportPayWaySet));

                    itemDTO.setResourceName(resource.getResourceName());
                    itemDTO.setResourceVersion(resource.getResourceVersion());
                    itemDTO.setGoodsId(resource.getGoodsId());
                    itemDTO.setGoodsName(resource.getGoodsName());
                    itemDTO.setMeasureQuantity(quantity.multiply(convertRate));
                    itemDTO.setMeasureUnits(resource.getPriceUnit());
                    itemDTO.setAutoCompleteType(resource.getAutoCompleteType());
                    itemDTO.setAutoCompleteThreshold(resource.getAutoCompleteThreshold());

                    BigDecimal resPrice = getResPrice(deliverWay, itemDTO, resource);
                    //计算加价项
                    BigDecimal addItemPrice = getAddItemPrice(itemDTO, resource);
                    itemDTO.setAdditemPrice(addItemPrice);
                    //当前销售单位不等于记录单位时进行单价转换
                    if (!itemDTO.getUnits().equals(resource.getPriceUnit())) {
                        resPrice = resPrice.multiply(resource.getConvertRate()).setScale(2, BigDecimal.ROUND_HALF_UP);
                    }
                    initItemDTO(storeId, warehouse, itemDTO, resPrice, addItemPrice, quantity, resource);
                    initOrderDTO(order, resource);
                }
            }
        }
        return sellerId;
    }


    private BigDecimal getResPrice(PickingBillTypeEnum deliverWay, OrderItemDTO itemDTO, ResourceDTO resource) {
        BigDecimal resPrice;
        if (itemDTO.getOriginUnitPrice() == null || BigDecimal.ZERO.equals(itemDTO.getOriginUnitPrice())) {
            //资源原单价
            resPrice = getResPriceByDeliverWay(deliverWay, resource);
        } else {
            //设定的资源价格：合同资源价格
            resPrice = itemDTO.getOriginUnitPrice();
        }
        return resPrice;
    }

    private static void initOrderDTO(OrderDTO order, ResourceDTO resource) {
        order.setCurrency(resource.getCurrency());//币种
        order.setCurrencyName(resource.getCurrencyName());
        order.setCurrencySymbol(resource.getCurrencySymbol());

        order.setSaleRegion1(resource.getSaleAreaCode());
        order.setSaleRegion2(resource.getSaleAreaCode2());
        order.setSaleRegion3(resource.getSaleAreaCode3());
        order.setSaleRegion4(resource.getSaleAreaCode4());
        order.setSaleRegion5(resource.getSaleAreaCode5());
        //设置最小销售区域
        setSaleRegionPath(order, resource);
        setPayTimeLimit(order, resource);
        order.setAutoConfirmTake(Boolean.FALSE);
    }

    private static void initItemDTO(String storeId, WarehouseDetailsDTO warehouse, OrderItemDTO itemDTO, BigDecimal resPrice, BigDecimal addItemPrice, BigDecimal quantity, ResourceDTO resource) {
        BigDecimal itemPrice = resPrice.add(addItemPrice);//资源原价(包含加价项):  originUnitPrice + additemPrice
        itemDTO.setActualUnitPrice(itemPrice);
        itemDTO.setOriginUnitPrice(resPrice);
        BigDecimal actualAmount = quantity.multiply(itemPrice);
        BigDecimal originAmount = quantity.multiply(resPrice);
        itemDTO.setActualAmountPrice(actualAmount);//资源实际总价
        itemDTO.setOriginAmountPrice(originAmount);//资源原总价
        //仓库
        itemDTO.setStoreId(storeId);
        itemDTO.setStoreName(warehouse.getName());
        itemDTO.setStoreType(warehouse.getType());
        itemDTO.setStoreAddress(warehouse.getAddress());

        itemDTO.setLogistics(resource.getGoodsDTO().getLogistics());//物流类型
        itemDTO.setCategoryType(resource.getGoodsDTO().getCategoryType());
        itemDTO.setGoodsKeywords(resource.getGoodsDTO().getSearchKeywords());
    }


    private static BigDecimal getQuantity(OrderItemDTO itemDTO, ResourceDTO resource) {
        BigDecimal quantity = itemDTO.getItemQuantity();//商品购买数量
        if (quantity == null || quantity.compareTo(BigDecimal.ZERO) <= 0) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "购买数量必须大于0" + resource.getSaleUnit());
        }
        return quantity;
    }

    private static String getResCurrency(String resCurrency, ResourceDTO resource) {
        if (resCurrency != null && !resCurrency.equals(resource.getCurrency())) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "不允许同时购买不同币种的商品");
        } else {
            resCurrency = resource.getCurrency();
        }
        return resCurrency;
    }

    private static String getSellerId(String sellerId, ResourceDTO resource) {
        if (sellerId != null && !sellerId.equals(resource.getSellerId())) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "不允许同时购买不同卖家的商品");
        } else {
            sellerId = resource.getSellerId();
        }
        return sellerId;
    }


    private static BigDecimal getConvertRate(OrderItemDTO itemDTO, ResourceDTO resource) {
        BigDecimal convertRate = BigDecimal.ONE;
        if (!resource.getPriceUnit().equals(itemDTO.getUnits())) {
            convertRate = resource.getConvertRate();
            if (convertRate == null) {
                convertRate = BigDecimal.ONE;
            }
            itemDTO.setConvertRate(convertRate);
            itemDTO.setUnconvertRate(BigDecimal.ONE.divide(convertRate, 2, BigDecimal.ROUND_HALF_UP));
        } else {
            itemDTO.setConvertRate(BigDecimal.ONE);
            itemDTO.setUnconvertRate(BigDecimal.ONE);
        }
        return convertRate;
    }


    private ResourceDTO getResourceDTO(String resourceId) {
        ResourceDTO resource = resourceService.getResourceDetail(resourceId);
        if (resource == null) {
            throw new BizException(BasicCode.DB_DATA_NOT_FOUND, "订单商品[" + resourceId + "]不存在");
        }
        //Emall商城-提交订单：应不支持提交已下架资源订单
        if (!TradeStatusEnum.TRADE_STATUS100.getCode().equals(resource.getTradeStatus())) {
            throw new BizException(BasicCode.DB_DATA_NOT_FOUND, "订单商品[" + resource.getGoodsName() + "]已下架");
        }
        return resource;
    }

    private void checkCansaleNum(String buyerId, ResourceDTO resource, BigDecimal quantity, BigDecimal convertRate, String resourceId) {
        if (resource.getCansaleNum() == null || resource.getCansaleNum().compareTo(quantity.multiply(convertRate)) < 0) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "商品[" + resource.getResourceName() + "]可售数量不足");
        }

        //单笔最大购买量
        if (resource.getOrdermaxNum() != null && resource.getOrdermaxNum().compareTo(quantity.multiply(convertRate)) < 0) {
            throw new BizException(BasicCode.UNDEFINED_ERROR,
                    "商品[" + resource.getResourceName() + "]单笔最大购买量为" + resource.getOrdermaxNum().setScale(2, RoundingMode.HALF_UP) + resource.getPriceUnit());
        }

        //单笔最小购买量
        if (resource.getOrderminNum() != null && resource.getOrderminNum().compareTo(quantity.multiply(convertRate)) > 0) {
            throw new BizException(BasicCode.UNDEFINED_ERROR,
                    "商品[" + resource.getResourceName() + "]单笔最小购买量为" + resource.getOrderminNum().setScale(2, RoundingMode.HALF_UP) + resource.getPriceUnit());
        }

        //单日最大购买量
        if (resource.getDaymaxNum() != null) {
            BigDecimal ymQuantity = baseOrderSearchBiz.getBuyerResDayQuantity(buyerId, resourceId);
            if (resource.getDaymaxNum().compareTo(ymQuantity.add(quantity.multiply(convertRate))) < 0) {
                throw new BizException(BasicCode.UNDEFINED_ERROR,
                        "商品[" + resource.getResourceName() + "]单日最大购买量为" + resource.getDaymaxNum().setScale(2, RoundingMode.HALF_UP) + resource.getPriceUnit() +
                                ", 本日已购买数量为" + ymQuantity.setScale(2, RoundingMode.HALF_UP) + resource.getPriceUnit());
            }
        }
    }


    private static Set<String> getPayWaySet(ResourceDTO resource, Set<String> supportPayWaySet) {
        Set<String> payWaySet = Sets.newHashSet(resource.getPayWay());
        if (supportPayWaySet == null) {
            supportPayWaySet = payWaySet;
        } else {
            supportPayWaySet = Sets.intersection(supportPayWaySet, payWaySet);
        }
        return supportPayWaySet;
    }


    private BigDecimal getAddItemPrice(OrderItemDTO itemDTO, ResourceDTO resource) {
        BigDecimal addItemPrice = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(itemDTO.getOrderItemAdds())) {
            List<String> addItemIdList = Lists.newArrayList();
            itemDTO.getOrderItemAdds().stream().forEach(addItem -> addItemIdList.add(addItem.getAdditemId()));
            AddItemQueryConditionDTO addItemQueryConditionDTO = new AddItemQueryConditionDTO();
            addItemQueryConditionDTO.setAddItemType(1);
            addItemQueryConditionDTO.setSellerId(resource.getSellerId());
            addItemQueryConditionDTO.setAddItemIdList(addItemIdList);
            List<GoodsAddItemDTO> addItemList = goodsAddItemService.queryAddItemByCondition(addItemQueryConditionDTO);
            if (CollectionUtils.isEmpty(addItemList)) {
                throw new BizException(BasicCode.DATA_NOT_EXIST, "加价项");
            }
            for (GoodsAddItemDTO addItem : addItemList) {
                //排除润管砂浆、泵送方式
                if (addItem.getPriceWay() != null && addItem.getPriceWay() == 1) {
                    addItemPrice = addItemPrice.add(addItem.getAdditemPrice());
                }
            }
        }
        return addItemPrice;
    }


    private BigDecimal getResPriceByDeliverWay(PickingBillTypeEnum deliverWay, ResourceDTO resource) {
        BigDecimal resPrice;
        if (PickingBillTypeEnum.BUYER_TAKE.equals(deliverWay)) {
            resPrice = resource.getFactoryPrice();
        } else {
            ItemResult<SpecialGoodsAttributeDTO> goodsResult =
                    goodsService.getSpecialGoodsAttribute(resource.getGoodsId());
            if (!goodsResult.isSuccess()) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "查询商品特殊属性失败");
            }
            //配送时商品单价(资源单价＋运费单价)为到位价
            if (goodsResult.getData().getConcreteFlag() == 1) {
                resPrice = resource.getArrivePrice();
            } else {
                if (resource.getArrivePrice() == null) {
                    resPrice = resource.getFactoryPrice();
                } else {
                    resPrice = resource.getArrivePrice();
                }
            }
        }
        if (resPrice == null || resPrice.compareTo(BigDecimal.ZERO) <= 0) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "缺少商品价格");
        }
        return resPrice;
    }

    private static void setSaleRegionPath(OrderDTO order, ResourceDTO resource) {
        if (CsStringUtils.isNotBlank(resource.getSaleAreaCode5())) {
            order.setSaleRegionPath(resource.getSaleAreaCode5());
        } else if (CsStringUtils.isNotBlank(resource.getSaleAreaCode4())) {
            order.setSaleRegionPath(resource.getSaleAreaCode4());
        } else if (CsStringUtils.isNotBlank(resource.getSaleAreaCode3())) {
            order.setSaleRegionPath(resource.getSaleAreaCode3());
        } else if (CsStringUtils.isNotBlank(resource.getSaleAreaCode2())) {
            order.setSaleRegionPath(resource.getSaleAreaCode2());
        } else if (CsStringUtils.isNotBlank(resource.getSaleAreaCode())) {
            order.setSaleRegionPath(resource.getSaleAreaCode());
        }
    }

    private static void setPayTimeLimit(OrderDTO order, ResourceDTO resource) {
        if (CsStringUtils.equals(resource.getPaydateType(), PaydateTypeEnum.PAYDATE_TYPE2.code()) &&
                resource.getPaydateLimit() != null) {//小时
            Date timeLimit = DateUtils.addHours(new Date(), resource.getPaydateLimit().intValue());
            if (order.getPayTimeLimit() == null) {
                order.setPayTimeLimit(timeLimit);
            } else {
                if (order.getPayTimeLimit().after(timeLimit)) {
                    order.setPayTimeLimit(timeLimit);
                }
            }
        }
    }

    private void hasResourceIdLogic(OrderDTO order, Boolean createFlag, String storeId, WarehouseDetailsDTO warehouse, String buyerId, OrderItemDTO itemDTO) {
        // 合同
        itemDTO.setStoreId(storeId);
        itemDTO.setStoreName(warehouse.getName());
        itemDTO.setStoreType(warehouse.getType());
        itemDTO.setStoreAddress(warehouse.getAddress());
        //物流类型
        GoodsDTO goodsDTO = goodsService.getGoodsInfo(itemDTO.getGoodsId()).getData();
        itemDTO.setLogistics(goodsDTO.getLogistics());
        itemDTO.setMeasureQuantity(itemDTO.getItemQuantity());
        TrContractGoodsDTO trContractGoodsDTO = contractService.getContractGoodsById(itemDTO.getContractGoodsId());
        //ERP卖家、流向管控的自提合同单需要验证erp地址
        checkErpAddressDTO(order, createFlag, trContractGoodsDTO, goodsDTO);
        //添加销售区域
        order.setSaleRegion1(trContractGoodsDTO.getSaleAreaCode());
        order.setSaleRegion2(trContractGoodsDTO.getSaleAreaCode2());
        order.setSaleRegion3(trContractGoodsDTO.getSaleAreaCode3());
        order.setSaleRegion4(trContractGoodsDTO.getSaleAreaCode4());
        order.setSaleRegion5(trContractGoodsDTO.getSaleAreaCode5());
        //设置最小销售区域
        setSaleRegionPath(order, trContractGoodsDTO);
        //合同商品是否流向管控
        itemDTO.setIfFlowControl(Boolean.FALSE);
        if (CsStringUtils.equals(trContractGoodsDTO.getFlowMonitor(), "1")) {
            itemDTO.setIfFlowControl(Boolean.TRUE);
        }
        itemDTO.setAutoCompleteType(trContractGoodsDTO.getAutoCompleteType());
        itemDTO.setAutoCompleteThreshold(trContractGoodsDTO.getAutoCompleteThreshold());
        //合同业务员
        setSalesmanInfo(order);
        BigDecimal quantity = itemDTO.getItemQuantity();//商品购买数量
        BigDecimal surplusGoodsAmount = trContractGoodsDTO.getPlanGoodsAmount().subtract(trContractGoodsDTO.getActualNum());
        checkOrderminNumAndOrdermaxNum(surplusGoodsAmount, quantity, goodsDTO, trContractGoodsDTO);
        //验证合同商品单日最大购买量
        Optional.ofNullable(trContractGoodsDTO.getDaymaxNum())
                .filter(dayMaxNum -> dayMaxNum.compareTo(BigDecimal.ZERO) > 0)
                .ifPresent(dayMaxNum -> {
                    BigDecimal dayOrderNum = baseOrderSearchBiz.countContractGoodsDayQuantity(buyerId, trContractGoodsDTO.getContractGoodsId());
                    //当日已下单量需要加上本次下单量
                    BigDecimal limitDayOrderNum = ArithUtils.add(dayOrderNum, quantity);
                    if (dayMaxNum.compareTo(limitDayOrderNum) < 0) {
                        throw new BizException(BasicCode.UNDEFINED_ERROR,
                                "合同商品[" + trContractGoodsDTO.getGoodsName() + "]单日最大购买量为"
                                        + dayMaxNum.setScale(2, BigDecimal.ROUND_HALF_UP)
                                        + trContractGoodsDTO.getUnit() +
                                        ", 本日已购买数量为" + dayOrderNum.setScale(2, BigDecimal.ROUND_HALF_UP)
                                        + trContractGoodsDTO.getUnit());
                    }
                });
        if (CsStringUtils.equals(trContractGoodsDTO.getPaydateType(), PaydateTypeEnum.PAYDATE_TYPE2.code()) &&
                trContractGoodsDTO.getPaydateLimit() != null) {//小时
            Date timeLimit = DateUtils.addHours(new Date(), trContractGoodsDTO.getPaydateLimit().intValue());
            if (order.getPayTimeLimit() == null) {
                order.setPayTimeLimit(timeLimit);
            } else {
                if (order.getPayTimeLimit().after(timeLimit)) {
                    order.setPayTimeLimit(timeLimit);
                }
            }
        }
    }

    private static void checkOrderminNumAndOrdermaxNum(BigDecimal surplusGoodsAmount, BigDecimal quantity, GoodsDTO goodsDTO, TrContractGoodsDTO trContractGoodsDTO) {
        if (surplusGoodsAmount.compareTo(quantity) < 0) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "商品【" + goodsDTO.getGoodsName() + "】超出商品剩余提货量");
        }
        if (trContractGoodsDTO.getOrderminNum() != null && trContractGoodsDTO.getOrderminNum().compareTo(quantity) > 0) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "商品【" + goodsDTO.getGoodsName() + "】低于单笔最小购买量");
        }
        if (trContractGoodsDTO.getOrdermaxNum() != null && trContractGoodsDTO.getOrdermaxNum().compareTo(quantity) < 0) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "商品【" + goodsDTO.getGoodsName() + "】超出单笔最大购买量");
        }
    }

    private void setSalesmanInfo(OrderDTO order) {
        if (CsStringUtils.isNotBlank(order.getDealsId())) {
            TrContractDTO contractDTO = contractService.getContract(order.getDealsId());
            String salesmanId = contractDTO.getSalemanAccountId();
            if (CsStringUtils.isNotBlank(salesmanId)) {
                order.setSalesmanId(salesmanId);
                AccountDTO salesman = accountService.findById(salesmanId);
                if (salesman != null) {
                    order.setSalesmanName(salesman.getRealName());
                    order.setSalesmanMobile(salesman.getMobile());
                }
            }
            order.setAutoConfirmTake(contractDTO.getAutoConfirmTake() == null ?
                    Boolean.FALSE : contractDTO.getAutoConfirmTake());
        }
    }

    private static void setSaleRegionPath(OrderDTO order, TrContractGoodsDTO trContractGoodsDTO) {
        if (CsStringUtils.isNotBlank(trContractGoodsDTO.getSaleAreaCode5())) {
            order.setSaleRegionPath(trContractGoodsDTO.getSaleAreaCode5());
        } else if (CsStringUtils.isNotBlank(trContractGoodsDTO.getSaleAreaCode4())) {
            order.setSaleRegionPath(trContractGoodsDTO.getSaleAreaCode4());
        } else if (CsStringUtils.isNotBlank(trContractGoodsDTO.getSaleAreaCode3())) {
            order.setSaleRegionPath(trContractGoodsDTO.getSaleAreaCode3());
        } else if (CsStringUtils.isNotBlank(trContractGoodsDTO.getSaleAreaCode2())) {
            order.setSaleRegionPath(trContractGoodsDTO.getSaleAreaCode2());
        } else if (CsStringUtils.isNotBlank(trContractGoodsDTO.getSaleAreaCode())) {
            order.setSaleRegionPath(trContractGoodsDTO.getSaleAreaCode());
        }
    }

    private void checkErpAddressDTO(OrderDTO order, Boolean createFlag, TrContractGoodsDTO trContractGoodsDTO, GoodsDTO goodsDTO) {
        if (createFlag && CsStringUtils.equals(trContractGoodsDTO.getFlowMonitor(), "1") &&
                CsStringUtils.equals(order.getDeliverWay(), PickingBillTypeEnum.BUYER_TAKE.getCode()) &&
                orderErpBiz.hasErpOrder(goodsDTO.getSellerId(), MemberConfigEnum.ERP_ORDER_FLG.getKeyCode())) {
            if (order.getErpAddressDTO() == null ||
                    CsStringUtils.isBlank(order.getErpAddressDTO().getErpUnloadAddressId())) {
                throw new BizException(BasicCode.INVALID_PARAM, "ERP流向管控地址");
            }
            log.info("erpAddressDTO:" + JSON.toJSONString(order.getErpAddressDTO()));
        }
    }

    private void setSellerInfoLogic(OrderDTO order, String sellerId) {
        if (CsStringUtils.isBlank(sellerId)) {
            throw new BizException(BasicCode.DB_DATA_NOT_FOUND, "商品卖家信息");
        }
        MemberDTO seller = memberService.findMemberDetailById(sellerId);
        if (seller == null) {
            throw new BizException(BasicCode.DB_DATA_NOT_FOUND, "商品卖家:" + sellerId);
        }
        order.setSellerId(sellerId);
        order.setSellerName(seller.getMemberName());
        order.setSellerContact(seller.getContactName());
        order.setSellerContactWay(seller.getContactPhone());
    }

    private void computeResultLogic(OrderDTO order, PickingBillTypeEnum deliverWay, List<OrderItemDTO> orderItems) {
        if (PickingBillTypeEnum.BUYER_TAKE != deliverWay
                && CsStringUtils.isNotBlank(order.getAddressId())
                && CollectionUtils.isNotEmpty(orderItems)) {
            orderItems.forEach(itemDTO -> {
                ItemResult<SpecialGoodsAttributeDTO> goodsResult =
                        goodsService.getSpecialGoodsAttribute(itemDTO.getGoodsId());
                if (!goodsResult.isSuccess()) {
                    throw new BizException(BasicCode.UNDEFINED_ERROR, "查询商品特殊属性失败");
                }
                //不使用运费规则
                if (goodsResult.getData().getConcreteFlag() == 1) {
                    return;
                }
                CategoryQuantityDTO quantityDTO = new CategoryQuantityDTO();
                quantityDTO.setTransportCategoryId(itemDTO.getLogistics());
                quantityDTO.setProductQuantity(itemDTO.getMeasureQuantity());
                CarriageComputeDTO carriageDTO = new CarriageComputeDTO();
                carriageDTO.setPickingBillType(deliverWay.getCode());
                carriageDTO.setUserId(order.getSellerId());
                carriageDTO.setReceiveAddressId(order.getAddressId());
                carriageDTO.setReceiveAddressLocation(order.getAddressMap());
                carriageDTO.setWarehouseId(itemDTO.getStoreId());
                carriageDTO.setCategoryQuantityList(Collections.singletonList(quantityDTO));
                carriageDTO.setProvinceCode(order.getProvinceCode());
                carriageDTO.setCityCode(order.getCityCode());
                carriageDTO.setDistrictCode(order.getDistrictCode());
                carriageDTO.setStreetCode(order.getStreetCode());
                ItemResult<List<CarriageRuleResultDTO>> computeResult;
                computeResult = ruleComputeService.queryCarriageRule(carriageDTO);
                if (!computeResult.isSuccess()) {
                    log.error("queryCarriageRule_start:" + JSON.toJSONString(carriageDTO));
                    throw new BizException(BasicCode.UNDEFINED_ERROR, "查询运费规则失败！");
                }
                if (Boolean.FALSE.equals(computeResult.getData().get(0).getRuleFlag())) {
                    throw new BizException(BasicCode.UNDEFINED_ERROR, "该地区无法配送该商品！");
                }
            });
        }
    }

    @Override
    @Transactional
    public OrderDTO createOrder(OrderDTO orderDTO, String operator) {
        //订单版本
        orderDTO.setOrderId(uuidGenerator.gain());
        orderDTO.setOrderVersion(1);
        //订单编号
        if (CsStringUtils.isBlank(orderDTO.getOrderCode())) {
            orderDTO.setOrderCode(businessIdGenerator.incrementOrderCode());
        }
        log.info("createOrder_start:" + JSON.toJSONString(orderDTO));
        //0.订单验证
        validateOrder(orderDTO, true);
        List<OrderItemDTO> orderItems = orderDTO.getOrderItems();
        //是否需要卖家确认
        orderDTO.setIfNeedsellerconfirm(isSellerConfirm(orderDTO.getSellerId(), orderItems));
        //订单状态
        if (!orderDTO.getIfNeedsellerconfirm()) {
            orderDTO.setPayStatus(PayStatusEnum.WAIT_PAYMENT.code());
        }
        orderDTO.setDeliverStatus(null);
        orderDTO.setAdjustStatus(AdjustStatusEnum.CONFIRMING.getCode());
        orderDTO.setRefundStatus(null);
        //计算空载和台班
        orderComputeService.computeMachineAndEmptyCost(orderDTO);
        log.info("computeMachineAndEmptyCost:" + JSON.toJSONString(orderDTO));
        //1.处理订单商品及其优惠信息,计算订单资源金额
        orderComputeService.computeOrderItem(orderDTO, true);
        log.info("computeOrderItem:" + JSON.toJSONString(orderDTO));
        //2.获取订单支付信息，计算物流金额
        orderComputeService.computeOrderLogisticsFee(orderDTO, true);
        log.info("computeOrderLogisticsFee:" + JSON.toJSONString(orderDTO));
        //3.保存订单并锁定资源数量
        orderService.saveOrderData(orderDTO, operator);

        log.info("saveOrderData:" + JSON.toJSONString(orderDTO));
        //4.处理定制加工项
        processBuyerSelectInfo(orderDTO, operator);
        log.info("processBuyerSelectInfo:" + JSON.toJSONString(orderDTO));
        //5.建立企业关系
        addMemberRelation(orderDTO);
        log.info("addMemberRelation:" + JSON.toJSONString(orderDTO));
        //6.确认使用优惠券
        if (!orderDTO.getIfNeedsellerconfirm()) {
            useResourcePromotional(orderDTO, operator);
            log.info("useResourcePromotional:" + JSON.toJSONString(orderDTO));
        }
        //旺季消息发送
        if (orderDTO.getIfNeedsellerconfirm()) {
            SendSMSMessageDTO buyerSendSMSMessageDTO = new SendSMSMessageDTO();
            buyerSendSMSMessageDTO.setOrderDTO(orderDTO);
            buyerSendSMSMessageDTO.setBusinessCode(orderDTO.getOrderCode());
            buyerSendSMSMessageDTO.setMessageConfigCode(
                    MessageConfigCodeEnum.BUYER_ORDER_CONFIRM_DURING_PEAK_SALES_SEASON1.getCode());
            Map<String, Object> extraParams = new HashMap<>();
            extraParams.put("accountId", operator);
            buyerSendSMSMessageDTO.setExtraParams(extraParams);
            smsMessageFactoryContext.sendSMSMessage(buyerSendSMSMessageDTO);
            SendSMSMessageDTO sellerSendSMSMessageDTO = new SendSMSMessageDTO();
            sellerSendSMSMessageDTO.setOrderDTO(orderDTO);
            sellerSendSMSMessageDTO.setBusinessCode(orderDTO.getOrderCode());
            sellerSendSMSMessageDTO.setMessageConfigCode(
                    MessageConfigCodeEnum.BUYER_ORDER_CONFIRM_DURING_PEAK_SALES_SEASON2.getCode());
            smsMessageFactoryContext.sendSMSMessage(sellerSendSMSMessageDTO);
        } else {
            messageProducer.sendOnOrderSubmitAsync(orderDTO.getOrderId());
        }

        return orderDTO;
    }

    private boolean isSellerConfirm(String sellerId, List<OrderItemDTO> itemList) {
        boolean needCementBusySeasonApproval = false;
        MemberConfigDTO busySeasonFlagConfig = memberConfigService.findByMemberIdAndKeyCode(sellerId, "busy_season_flag");
        if (busySeasonFlagConfig != null && CsStringUtils.equals(busySeasonFlagConfig.getValue(), "1")) {
            needCementBusySeasonApproval = true;
        }
        SellerConfirmJudgeDTO sellerConfirmJudgeDTO = new SellerConfirmJudgeDTO();
        sellerConfirmJudgeDTO.setNeedCementBusySeasonApproval(needCementBusySeasonApproval);
        sellerConfirmJudgeDTO.setGoodsIdList(itemList.stream().map(OrderItemDTO::getGoodsId).toList());

        ItemResult<Boolean> result = goodsService.isSellerConfirmBatch(sellerConfirmJudgeDTO);
        return result != null && result.getData();
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveOrderData(OrderDTO orderDTO, String operator) {
        log.info("saveOrderData:{}", JSON.toJSONString(orderDTO));
        List<OrderItemDTO> orderItems = orderDTO.getOrderItems();//商品信息
        List<OrderDiscountDetailDTO> discList = orderDTO.getDiscountDetail();//优惠信息
        List<OrderPayinfoDTO> payList = orderDTO.getOrderPayinfo();//支付单

        //订单总金额 = 物流+资源+其他
        orderDTO.setOriginOrderAmount(ArithUtils.add(
                        orderDTO.getOriginResourceAmount(), orderDTO.getOriginLogisticAmount(), orderDTO.getOriginOthersAmount())
                .setScale(2, RoundingMode.HALF_UP));
        orderDTO.setActualOrderAmount(ArithUtils.add(
                        orderDTO.getActualResourceAmount(), orderDTO.getActualLogisticAmount(), orderDTO.getActualOthersAmount())
                .setScale(2, RoundingMode.HALF_UP));
        if (orderDTO.getOriginOrderAmount().compareTo(BigDecimal.ZERO) < 0) {
            orderDTO.setOriginOrderAmount(BigDecimal.ZERO);
        }
        if (orderDTO.getActualOrderAmount().compareTo(BigDecimal.ZERO) < 0) {
            orderDTO.setActualOrderAmount(BigDecimal.ZERO);
        }
        //订单金额check
        orderComputeService.checkOrderAmount(orderDTO);
        //保存订单主表
        OrderInfo order = BeanConvertUtils.convert(orderDTO, OrderInfo.class);
        BaseBiz.setOperInfo(order, operator, true);
        log.info("saveOrder_start:" + new Date());
        //重新设置订单调价状态(等待调价)
        order.setAdjustStatus(AdjustStatusEnum.WAIT_ADJUST.getCode());

        //搬运类型的修改
        if (CsStringUtils.isBlank(order.getCarryWay())) {
            log.info("搬运类型为空,默认为不搬运");
            order.setCarryWay(CarryWayEnum.INPLACE.code());
        }
        if (CarryWayEnum.getByCode(order.getCarryWay()) == null) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "订单CarryWay值有误" + order.getCarryWay());
        }

        orderInfoMapper.insert(order);
        String orderId = order.getOrderId();
        orderDTO.setOrderId(orderId);

        statusMachineFactory.initOrderStatus(order, operator);

        //保存erp地址信息
        if (orderDTO.getErpAddressDTO() != null) {
            orderDTO.getErpAddressDTO().setOrderId(order.getOrderId());
            orderDTO.getErpAddressDTO().setReceivingAddressId(order.getAddressId());
            orderDTO.getErpAddressDTO().setOperator(operator);
            orderErpAddressBiz.saveOrderErpAddress(orderDTO.getErpAddressDTO());
        }

        saveOrderLogic(orderDTO, operator, orderItems, orderId, discList, order, payList);
    }

    private void saveOrderLogic(OrderDTO orderDTO, String operator, List<OrderItemDTO> orderItems, String orderId, List<OrderDiscountDetailDTO> discList, OrderInfo order, List<OrderPayinfoDTO> payList) {
        try {
            //保存商品信息
            log.info("saveOrderItem_start:" + new Date());
            saveOrderItem(operator, orderItems, orderId);

            log.info("orderDiscountDetailMapper_start:" + new Date());
            //保存优惠信息
            saveOrderDiscountDetail(operator, orderId, discList);

            log.info("orderPayinfoMapper_start:" + new Date());
            //待支付状态
            saveOrderPayinfoDTO(operator, orderId, order, payList);
            //开票信息
            log.info("orderTaxinfoMapper_start:" + new Date());
            saveOrderTaxinfoDTO(orderDTO, operator, orderId);
            //确认已锁定的资源可售数量
            log.info("confirmResourceNum_start:" + new Date());

            this.updateResourceNum(orderItems, operator);
            log.info("confirmResourceNum_end:" + new Date());
        } catch (Exception e) {
            log.error("保存订单错误", e);
            //解锁资源可售数量
            throw e;
        }
    }

    private void saveOrderTaxinfoDTO(OrderDTO orderDTO, String operator, String orderId) {
        List<OrderTaxinfoDTO> taxList = orderDTO.getOrderTaxinfo();
        if (CollectionUtils.isNotEmpty(taxList)) {
            for (OrderTaxinfoDTO taxDTO : taxList) {
                OrderTaxinfo taxinfo = new OrderTaxinfo();
                if (CsStringUtils.isNotBlank(taxDTO.getInvoiceId())) {
                    TaxInfoDetailDTO taxDetailDTO = taxInfoService.findById(taxDTO.getInvoiceId());
                    BeanConvertUtils.copyProperties(taxDetailDTO, taxinfo);
                }
                BeanConvertUtils.copyPropertiesIgnoreNull(taxDTO, taxinfo);//输入的数据
                taxinfo.setOrderTaxinfoId(uuidGenerator.gain());
                taxinfo.setOrderId(orderId);
                if (CsStringUtils.isBlank(taxinfo.getMemberId())) {
                    taxinfo.setMemberId(orderDTO.getBuyerId());
                }
                BaseBiz.setOperInfo(taxinfo, operator, true);
                orderTaxinfoMapper.insert(taxinfo);
            }
        }
    }

    private void saveOrderPayinfoDTO(String operator, String orderId, OrderInfo order, List<OrderPayinfoDTO> payList) {
        if ((OrderStatusEnum.WAIT_PAYMENT.code().equals(order.getOrderStatus()))
            //保存支付单
            && (CollectionUtils.isNotEmpty(payList))) {
                for (OrderPayinfoDTO payDTO : payList) {
                    payDTO.setObjectId(orderId);
                    payDTO.setObjectCode(order.getOrderCode());
                    OrderPayinfo payinfo = BeanConvertUtils.convert(payDTO, OrderPayinfo.class);
                    BaseBiz.setOperInfo(payinfo, operator, true);
                    orderPayinfoMapper.insert(payinfo);

                    List<OrderPayinfoDetailDTO> detailList = payDTO.getOrderPayinfoDetail();
                    for (OrderPayinfoDetailDTO detailDTO : detailList) {
                        detailDTO.setPayinfoId(payinfo.getPayinfoId());
                        detailDTO.setDetailId(uuidGenerator.gain());
                        OrderPayinfoDetail detail = BeanConvertUtils.convert(detailDTO, OrderPayinfoDetail.class);
                        BaseBiz.setOperInfo(detail, operator, true);
                        orderPayinfoDetailMapper.insert(detail);
                    }
                }
        }
    }

    private void saveOrderDiscountDetail(String operator, String orderId, List<OrderDiscountDetailDTO> discList) {
        if (CollectionUtils.isNotEmpty(discList)) {
            for (OrderDiscountDetailDTO discountDTO : discList) {
                OrderDiscountDetail discount = BeanConvertUtils.convert(discountDTO, OrderDiscountDetail.class);
                if (CsStringUtils.isBlank(discount.getObjectId())) {
                    discount.setObjectId(orderId);
                }
                BaseBiz.setOperInfo(discount, operator, true);
                orderDiscountDetailMapper.insertSelective(discount);
            }

            PickingBillTypeEnum.SELLER_DELIVERY.getCode();
        }
    }

    private void saveOrderItem(String operator, List<OrderItemDTO> orderItems, String orderId) {
        for (OrderItemDTO itemDTO : orderItems) {
//				//合同单添加物流管控
            itemDTO.setOrderId(orderId);
            if (CsStringUtils.isBlank(itemDTO.getOrderItemId())) {
                itemDTO.setOrderItemId(uuidGenerator.gain());
            }
            OrderItem item = BeanConvertUtils.convert(itemDTO, OrderItem.class);
            BaseBiz.setOperInfo(item, operator, true);
            //合同单存在resourcename是空，用goodsName填充
            GoodsDTO goodsDTO = goodsService.getGoodsInfo(item.getGoodsId()).getData();
            if (CsStringUtils.isBlank(item.getResourceName())) {
                item.setResourceName(goodsDTO.getGoodsName());
                //合同单合同商品id存这里 合同下单后取消，订单数量未加回到合同详情的剩余提货数量
                item.setResourceId(itemDTO.getContractGoodsId());
            }
            item.setMeasureUnits(goodsDTO.getMeasureUnit());
            item.setCategoryCode(goodsDTO.getCategoryCode());
            orderItemMapper.insertSelective(item);

            //行项目加价项
            List<OrderItemAddDTO> itemAddDTOs = itemDTO.getOrderItemAdds();
            if (CollectionUtils.isNotEmpty(itemAddDTOs)) {
                for (OrderItemAddDTO addDTO : itemAddDTOs) {
                    OrderItemAdd itemAdd = BeanConvertUtils.convert(addDTO, OrderItemAdd.class);
                    itemAdd.setOrderItemId(item.getOrderItemId());
                    itemAdd.setOrderItemAddId(uuidGenerator.gain());
                    BaseBiz.setOperInfo(itemAdd, operator, true);
                    orderItemAddMapper.insertSelective(itemAdd);
                }
            }
        }
    }

    /**
     * @param orderItems
     * @param operator
     * @Title: updateResourceNum
     * @Description: 更新资源数量
     * <AUTHOR>
     */
    private void updateResourceNum(List<OrderItemDTO> orderItems, String operator) {
        try {
            if (isProcessResSaleNum()) {
                HashMap<String, BigDecimal> map = Maps.newHashMap();
                for (OrderItemDTO item : orderItems) {
                    map.put(item.getResourceId(), item.getItemQuantity().multiply(item.getConvertRate()));
                }
                resourceNumTCCService.updateResourceNumForCreateOrder(map, operator);
            }
        } catch (Exception e) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "锁定商品数量失败：" + e.getMessage());
        }
    }

    /**
     * 处理定制加工项
     *
     * @param orderDTO
     * @param operator
     */
    private void processBuyerSelectInfo(OrderDTO orderDTO, String operator) {
        Map<String, List<BuyerSelectInfoDTO>> buyerSelectInfos = Maps.newHashMap();
        for (OrderItemDTO orderItem : orderDTO.getOrderItems()) {
            if (CollectionUtils.isNotEmpty(orderItem.getSelectInfos())) {
                buyerSelectInfos.put(orderItem.getOrderItemId(), convertBuyerSelectInfo(orderItem.getSelectInfos()));
            }
        }
        if (CollectionUtils.isNotEmpty(buyerSelectInfos.values())) {
            log.info("addBuyerSelectInfoFromOrder:{}-{}", orderDTO.getOrderId(), JSON.toJSONString(buyerSelectInfos));
            buyerSelectInfoService.addBuyerSelectInfoFromOrder(
                    buyerSelectInfos, orderDTO.getBuyerId(), orderDTO.getOrderId(), operator);
        }

        if (CollectionUtils.isNotEmpty(orderDTO.getCartResourceIds())) {
            for (String carId : orderDTO.getCartResourceIds()) {
                buyerSelectInfoService.removeBuyerSelectInfoFromCart(carId, operator);
            }
        }
    }

    private List<BuyerSelectInfoDTO> convertBuyerSelectInfo(List<com.ecommerce.order.api.dto.BuyerSelectInfoDTO> buyerSelectInfoDTOS) {
        List<BuyerSelectInfoDTO> selectInfoList = new ArrayList<>();
        for (com.ecommerce.order.api.dto.BuyerSelectInfoDTO buyerSelectInfoDTO : buyerSelectInfoDTOS) {
            BuyerSelectInfoDTO selectInfo = new BuyerSelectInfoDTO();
            BeanUtils.copyProperties(buyerSelectInfoDTO, selectInfo);
            selectInfoList.add(selectInfo);
        }
        return selectInfoList;
    }

    /**
     * 建立企业关系
     *
     * @param orderDTO
     */
    private void addMemberRelation(OrderDTO orderDTO) {
        try {
            log.info("addMemberRelationByTrade_{},{}", orderDTO.getSellerId(), orderDTO.getBuyerId());
            memberRelationService.addMemberRelationByTrade(orderDTO.getSellerId(), orderDTO.getBuyerId());
        } catch (Exception e) {
            log.error("addMemberRelationByTrade_Error", e);
        }
    }

    /**
     * 使用优惠券
     *
     * @param order
     * @param operator
     */
    protected void useResourcePromotional(OrderDTO order, String operator) {
        List<OrderDiscountDetailDTO> disDetails = Collections.emptyList();
        if (CollectionUtils.isNotEmpty(disDetails)) {
            Map<String, OrderItemDTO> orderItemMap = Maps.newHashMap();
            order.getOrderItems().forEach(action -> orderItemMap.put(action.getOrderItemId(), action));

            PromotionalDTO promot = new PromotionalDTO();
            List<BuyerPromotionalDetailsDTO> promlist = Lists.newArrayList();
            for (OrderDiscountDetailDTO discount : disDetails) {
                BuyerPromotionalDetailsDTO promotd = new BuyerPromotionalDetailsDTO();
                promotd.setOrderId(order.getOrderId());
                promotd.setOrderNo(order.getOrderCode());
                promotd.setUseTime(new Date());
                if (DiscountTypeEnum.ONE.code().equals(discount.getDiscountType())) {
                    promotd.setBuyerPriceSaleRuleId(discount.getDiscountId());
                } else if (DiscountTypeEnum.SECTION.code().equals(discount.getDiscountType())) {
                    promotd.setBuyerSectionSaleRuleId(discount.getDiscountId());
                } else if (DiscountTypeEnum.SHOP.code().equals(discount.getDiscountType())) {
                    promotd.setReferrerRuleId(discount.getDiscountId());//优惠码id
                } else {
                    continue;
                }
                promotd.setBuyerId(order.getBuyerId());
                promotd.setBuyerName(order.getBuyerName());
                promotd.setSellerId(order.getSellerId());
                promotd.setSellerName(order.getSellerName());
                promotd.setResourceSalePrice(discount.getUntiPrice());
                promotd.setResourceTotalPrice(discount.getAmountPrice());

                OrderItemDTO item = orderItemMap.get(discount.getObjectId());
                if (item != null) {
                    promotd.setGoodsId(item.getGoodsId());
                    promotd.setGoodsName(item.getGoodsName());
                    promotd.setResourceId(item.getResourceId());
                    promotd.setResourceName(item.getResourceName());
                    promotd.setResourceAmount(item.getItemQuantity());
                    promotd.setResourceOriginalPrice(item.getOriginUnitPrice());
                    promotd.setUnit(item.getUnits());
                }
                promlist.add(promotd);
            }
            promot.setList(promlist);
            try {
                log.info("savePromotionDetail:{}", JSON.toJSONString(promlist));
            } catch (Exception e) {
                log.error("savePromotionDetail_使用优惠信息出错", e);
                throw new BizException(BasicCode.UNDEFINED_ERROR, "优惠信息失效：" + e.getMessage());
            }
        }
    }

    @Override
    @Transactional
    public void confirmOrder(OrderDTO inOrderDTO, String operator) {
        log.info("confirmOrder:{}", JSON.toJSONString(inOrderDTO));
        String orderId = getOrderId(inOrderDTO);

        boolean priceChange = false;//价格变更
        boolean quantityChange = false;//数量变更

        OrderDTO order = baseOrderSearchBiz.getOrderDetail(orderId);

        BigDecimal machineShiftCost = getMachineShiftCost(inOrderDTO, order);
        BigDecimal othersAmount = getOthersAmount(inOrderDTO, order);
        setActualOthersAmount(order);
        List<OrderItemDTO> changeList = inOrderDTO.getOrderItems();
        HashMap<String, BigDecimal> resourceQuantityMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(changeList)) {
            //存在商品价格、数量变更信息: 1.影响订单金额 2.影响优惠信息 3.数量变更影响物流费、物流优惠 4.影响支付信息 5.重新锁定资源可售数量
            Map<String, OrderItemDTO> changeMap = Maps.uniqueIndex(changeList, OrderItemDTO::getOrderItemId);
            for (OrderItemDTO item : order.getOrderItems()) {
                OrderItemDTO changeItem = getOrderItemDTO(item, changeMap);
                if (changeItem == null) continue;
                BigDecimal changePrice = changeItem.getActualResUnitPrice();//变更商品单价后优惠信息失效
                //实际单价加上加价项单价
                BigDecimal actualUnitPrice = ArithUtils.add(changePrice, item.getAdditemPrice());
                priceChange = isPriceChange(item, actualUnitPrice, changePrice, priceChange);
                //变更加价项
                if (CollectionUtils.isNotEmpty(changeItem.getOrderItemAdds())) {
                    BigDecimal addItemPrice = BigDecimal.ZERO;//+加价项总价
                    Map<String, OrderItemAddDTO> newAddItemMap = Maps.newHashMap();
                    boolean addItemPriceChange = isAddItemPriceChange(item, newAddItemMap, changeItem);
                    if (addItemPriceChange) {
                        priceChange = true;
                        actualUnitPrice = getActualUnitPrice(item, newAddItemMap, addItemPrice, changeItem, changePrice);
                    }
                    log.info("confirmOrder_old_addItemMap:{}-{}", orderId, JSON.toJSONString(item.getOrderItemAdds()));
                    log.info("confirmOrder_new_addItemMap:{}-{}", orderId, JSON.toJSONString(newAddItemMap));
                }

                quantityChange = isQuantityChangeByItemQuantity(item, changeItem, resourceQuantityMap, quantityChange, actualUnitPrice);
            }
        }

        waitSalerConfirmLogic(operator, order, priceChange, quantityChange, orderId);

//        //商品数量变更后，需重新锁定资源可售数量
        try {
            //更新订单
            OrderInfo updOrder = getUpdateOrderInfo(inOrderDTO, orderId, order);
            //更新业务员信息
            updateSalesmanInfo(inOrderDTO, operator, updOrder);

            statusMachineFactory.driveStatus(orderId, operator);

            //更新订单明细、加价项、优惠信息
            doChangeListLogic(operator, changeList, order, machineShiftCost, othersAmount, orderId, priceChange, quantityChange);
            //商品数量变更后，需重更新资源可售数量
            updateResourceNumForCreateOrder(operator, quantityChange, resourceQuantityMap);
            //发送消息
            SendSMSMessageDTO sellerSendSMSMessageDTO = new SendSMSMessageDTO();
            sellerSendSMSMessageDTO.setOrderDTO(order);
            sellerSendSMSMessageDTO.setBusinessCode(order.getOrderCode());
            sellerSendSMSMessageDTO.setMessageConfigCode(
                    MessageConfigCodeEnum.SELLER_ORDER_CONFIRM_DURING_PEAK_SALES_SEASON.getCode());
            smsMessageFactoryContext.sendSMSMessage(sellerSendSMSMessageDTO);
        } catch (Exception e) {
            log.error("卖家确认接口异常：{}" , e.toString());
            throw new BizException(BasicCode.UNDEFINED_ERROR, "卖家确认接口异常");
        }
    }

    private void doChangeListLogic(String operator, List<OrderItemDTO> changeList, OrderDTO order, BigDecimal machineShiftCost, BigDecimal othersAmount, String orderId, boolean priceChange, boolean quantityChange) {
        if (CollectionUtils.isNotEmpty(changeList)) {
            //更新订单明细、加价项
            updateOrderItemAddDtoLogic(operator, changeList, order, machineShiftCost, othersAmount, orderId);

            doPriceChangeAndQuantityChangeLogic(operator, priceChange, quantityChange, orderId, order);
        } else {
            doConcreteFlagLogic(order, machineShiftCost, othersAmount, orderId);
        }
    }

    private void updateResourceNumForCreateOrder(String operator, boolean quantityChange, HashMap<String, BigDecimal> resourceQuantityMap) {
        if (quantityChange && CollectionUtils.isNotEmpty(resourceQuantityMap.keySet()) && isProcessResSaleNum()) {
            resourceNumTCCService.updateResourceNumForCreateOrder(resourceQuantityMap, operator);
        }
    }

    @Nullable
    private static OrderItemDTO getOrderItemDTO(OrderItemDTO item, Map<String, OrderItemDTO> changeMap) {
        OrderItemDTO changeItem = changeMap.get(item.getOrderItemId());
        if (changeItem == null) {
            return null;
        }
        return changeItem;
    }

    private static boolean isPriceChange(OrderItemDTO item, BigDecimal actualUnitPrice, BigDecimal changePrice, boolean priceChange) {
        if (actualUnitPrice != null && actualUnitPrice.compareTo(item.getActualUnitPrice()) != 0) {
            item.setActualResUnitPrice(changePrice);
            item.setActualUnitPrice(actualUnitPrice);
            priceChange = true;
        }
        return priceChange;
    }

    private void setActualOthersAmount(OrderDTO order) {
        BigDecimal actualOthersAmount = orderComputeService.processOrderOthersAmount(order);
        if (actualOthersAmount.compareTo(order.getActualOthersAmount()) != 0) {
            //其他费用变更: 1.影响订单金额 2.影响支付信息
            order.setActualOthersAmount(actualOthersAmount);
            order.setActualOrderAmount(ArithUtils.add(
                    order.getActualResourceAmount(), order.getActualLogisticAmount(), order.getActualOthersAmount()));
        }
    }

    @Nullable
    private static BigDecimal getMachineShiftCost(OrderDTO inOrderDTO, OrderDTO order) {
        BigDecimal machineShiftCost = inOrderDTO.getMachineShiftCost();//台班费
        if (machineShiftCost != null) {
            order.setMachineShiftCost(machineShiftCost);
        }
        return machineShiftCost;
    }

    @Nullable
    private static BigDecimal getOthersAmount(OrderDTO inOrderDTO, OrderDTO order) {
        BigDecimal othersAmount = inOrderDTO.getOthersAmount();//其他费用
        if (othersAmount != null) {
            order.setOthersAmount(othersAmount);
        }
        return othersAmount;
    }

    private static boolean isAddItemPriceChange(OrderItemDTO item, Map<String, OrderItemAddDTO> newAddItemMap, OrderItemDTO changeItem) {
        boolean addItemPriceChange = false;//加价项价格变更
        if (CollectionUtils.isNotEmpty(item.getOrderItemAdds())) {
            item.getOrderItemAdds().forEach(action -> newAddItemMap.put(action.getOrderItemAddId(), action));
        }

        for (int i = 0; i < changeItem.getOrderItemAdds().size(); i++) {
            addItemPriceChange = isAddItemPriceChangeByOrderItemAddId(changeItem, i, addItemPriceChange, newAddItemMap);
        }
        return addItemPriceChange;
    }


    private String getOrderId(OrderDTO inOrderDTO) {
        String orderId = inOrderDTO.getOrderId();
        if (CsStringUtils.isBlank(orderId)) {
            throw new BizException(BasicCode.PARAM_NULL, "订单Id");
        }
        OrderInfo orderInfo = this.selectForUpdate(orderId);
        if (orderInfo == null) {
            throw new BizException(BasicCode.DB_DATA_NOT_FOUND, "订单:" + orderId);
        }

        OrderStatusEnum orderStatus = OrderStatusEnum.getByCode(orderInfo.getOrderStatus());
        if (orderStatus != OrderStatusEnum.CONFIRMING) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "订单状态已变更");
        }
        return orderId;
    }

    private static boolean isAddItemPriceChangeByOrderItemAddId(OrderItemDTO changeItem, int i, boolean addItemPriceChange, Map<String, OrderItemAddDTO> newAddItemMap) {
        OrderItemAddDTO changeAddItem = changeItem.getOrderItemAdds().get(i);
        changeAddItem.setOrderItemId(changeItem.getOrderItemId());
        if (CsStringUtils.isBlank(changeAddItem.getOrderItemAddId())) {
            //新增加价项
            if (changeAddItem.getAdditemPrice() != null && BigDecimal.ZERO.compareTo(changeAddItem.getAdditemPrice()) != 0) {
                addItemPriceChange = true;
            }
            newAddItemMap.put("Add_" + i, changeAddItem);
        } else {
            addItemPriceChange = isAddItemPriceChange(addItemPriceChange, newAddItemMap, changeAddItem);
        }
        return addItemPriceChange;
    }

    private static boolean isAddItemPriceChange(boolean addItemPriceChange, Map<String, OrderItemAddDTO> newAddItemMap, OrderItemAddDTO changeAddItem) {
        OrderItemAddDTO updAddItem = newAddItemMap.get(changeAddItem.getOrderItemAddId());
        if ("0".equals(changeAddItem.getAdditemId()) || CsStringUtils.isBlank(changeAddItem.getAdditemId())) {
            //删除加价项
            if (updAddItem.getAdditemPrice() != null && BigDecimal.ZERO.compareTo(updAddItem.getAdditemPrice()) != 0) {
                                    //有价格变更
                addItemPriceChange = true;
            }
            updAddItem.setAdditemPrice(null);
            updAddItem.setDelFlg(true);
        } else {
            //修改加价项
            if (updAddItem.getAdditemPrice() != null && updAddItem.getAdditemPrice().compareTo(changeAddItem.getAdditemPrice()) != 0) {
                                    //有价格变更
                addItemPriceChange = true;
            }
            updAddItem.setMessageValue(changeAddItem.getMessageValue());
            updAddItem.setAdditemPrice(changeAddItem.getAdditemPrice());
        }
        return addItemPriceChange;
    }

    @Nullable
    private static BigDecimal getActualUnitPrice(OrderItemDTO item, Map<String, OrderItemAddDTO> newAddItemMap, BigDecimal addItemPrice, OrderItemDTO changeItem, BigDecimal changePrice) {
        BigDecimal actualUnitPrice;
        for (OrderItemAddDTO action : newAddItemMap.values()) {
            if (action.getAdditemPrice() != null) {
                addItemPrice = addItemPrice.add(action.getAdditemPrice());
            }
        }

        changeItem.setAdditemPrice(addItemPrice);
        item.setAdditemPrice(addItemPrice);

        //变更单价(包含加价项)
                        actualUnitPrice = ArithUtils.add(changePrice, addItemPrice);
        if (actualUnitPrice != null) {
            changeItem.setActualUnitPrice(actualUnitPrice);
            item.setActualUnitPrice(actualUnitPrice);
        }
        return actualUnitPrice;
    }

    private static boolean isQuantityChangeByItemQuantity(OrderItemDTO item, OrderItemDTO changeItem, HashMap<String, BigDecimal> resourceQuantityMap, boolean quantityChange, BigDecimal actualUnitPrice) {
        BigDecimal changeQuantity = changeItem.getItemQuantity();//变更数量
        if ((changeQuantity != null)
            && (changeQuantity.compareTo(item.getItemQuantity()) != 0)) {
                BigDecimal subQuantity = changeQuantity.subtract(item.getItemQuantity());
                resourceQuantityMap.put(item.getResourceId(), subQuantity.multiply(item.getConvertRate()));
                item.setItemQuantity(changeQuantity);
                item.setMeasureQuantity(changeQuantity.multiply(item.getConvertRate()));
                quantityChange = true;
        }

        //资源实际总价
                changeItem.setActualAmountPrice(item.getItemQuantity().multiply(actualUnitPrice));
        item.setActualAmountPrice(changeItem.getActualAmountPrice());
        return quantityChange;
    }

    private void waitSalerConfirmLogic(String operator, OrderDTO order, boolean priceChange, boolean quantityChange, String orderId) {
        if (CollectionUtils.isEmpty(order.getOrderPayinfo())) {
            //待卖家确认时还未产生支付信息
            if (priceChange || quantityChange) {
                //1.商品费用、不再计算优惠信息
                orderComputeService.computeOrderItem(order, false);
            }

            //2.获取订单支付信息，计算保存物流金额
            log.info("===processOrderPayinfo===->{}", JSON.toJSONString(order));
            OrderPayinfoDTO payDTO = orderComputeService.computeOrderLogisticsFee(order, false);

            //订单实际总金额 = 物流+资源+其他
            order.setActualOrderAmount(ArithUtils.add(
                            order.getActualResourceAmount(), order.getActualLogisticAmount(), order.getActualOthersAmount())
                    .setScale(2, RoundingMode.HALF_UP));
            if (order.getActualOrderAmount().compareTo(BigDecimal.ZERO) < 0) {
                order.setActualOrderAmount(BigDecimal.ZERO);
            }

            log.info("confirmOrder_processOrder:{}", JSON.toJSONString(order));

            //订单金额check
            orderComputeService.checkOrderAmount(order);

            //保存支付单
            insertOrderPayinfoDetailDTOLogic(operator, payDTO, orderId, order);
        } else {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "等待更新支付信息");
        }
    }

    private void insertOrderPayinfoDetailDTOLogic(String operator, OrderPayinfoDTO payDTO, String orderId, OrderDTO order) {
        if (payDTO != null) {
            payDTO.setObjectId(orderId);
            payDTO.setObjectCode(order.getOrderCode());
            OrderPayinfo payinfo = BeanConvertUtils.convert(payDTO, OrderPayinfo.class);
            BaseBiz.setOperInfo(payinfo, operator, true);
            orderPayinfoMapper.insert(payinfo);

            List<OrderPayinfoDetailDTO> detailList = payDTO.getOrderPayinfoDetail();
            for (OrderPayinfoDetailDTO detailDTO : detailList) {
                detailDTO.setPayinfoId(payinfo.getPayinfoId());
                detailDTO.setDetailId(uuidGenerator.gain());
                OrderPayinfoDetail detail = BeanConvertUtils.convert(detailDTO, OrderPayinfoDetail.class);
                BaseBiz.setOperInfo(detail, operator, true);
                orderPayinfoDetailMapper.insert(detail);
            }
        }
    }


    private static OrderInfo getUpdateOrderInfo(OrderDTO inOrderDTO, String orderId, OrderDTO order) {
        OrderInfo updOrder = new OrderInfo();
        updOrder.setOrderId(orderId);
        updOrder.setPayStatus(PayStatusEnum.WAIT_PAYMENT.code());//待支付
        //金额信息
        updOrder.setActualOthersAmount(order.getActualOthersAmount());
        updOrder.setActualLogisticAmount(order.getActualLogisticAmount());
        updOrder.setActualResourceAmount(order.getActualResourceAmount());
        updOrder.setActualOrderAmount(order.getActualOrderAmount());
        if (CsStringUtils.isNotEmpty(inOrderDTO.getSellerMemo())) {
            updOrder.setSellerMemo(inOrderDTO.getSellerMemo());
        }
        return updOrder;
    }

    private void updateSalesmanInfo(OrderDTO inOrderDTO, String operator, OrderInfo updOrder) {
        if (CsStringUtils.isNotBlank(inOrderDTO.getSalesmanId())) {
            baseOrderSearchBiz.setOrderSalesman(inOrderDTO);
            updOrder.setSalesmanId(inOrderDTO.getSalesmanId());
            updOrder.setSalesmanName(inOrderDTO.getSalesmanName());
            updOrder.setBuyerMemo(inOrderDTO.getBuyerMemo());
            updOrder.setSellerMemo(inOrderDTO.getSellerMemo());
        }

        BaseBiz.setOperInfo(updOrder, operator, false);
        log.info("confirmOrder_updOrder:{}", JSON.toJSONString(updOrder));
        orderInfoMapper.updateByPrimaryKeySelective(updOrder);
    }

    private void updateOrderItemAddDtoLogic(String operator, List<OrderItemDTO> changeList, OrderDTO order, BigDecimal machineShiftCost, BigDecimal othersAmount, String orderId) {
        for (OrderItemDTO changeItem : changeList) {
            for (OrderItemDTO updItemDTO : order.getOrderItems()) {
                updateByPrimaryKeySelective(operator, machineShiftCost, othersAmount, orderId, changeItem, updItemDTO);
            }

            //加价项
            List<OrderItemAddDTO> addDTOs = changeItem.getOrderItemAdds();
            if (CollectionUtils.isNotEmpty(addDTOs)) {
                log.info("confirmOrder_updOrderItemAdd:{}-{}", orderId, JSON.toJSONString(addDTOs));
                for (OrderItemAddDTO addDTO : addDTOs) {
                    updateByPrimaryKeySelective(operator, changeItem, addDTO);
                }
            }
        }
    }

    private void updateByPrimaryKeySelective(String operator, OrderItemDTO changeItem, OrderItemAddDTO addDTO) {
        addDTO.setOrderItemId(changeItem.getOrderItemId());
        if (CsStringUtils.isBlank(addDTO.getOrderItemAddId())) {
            //新增加价项
            OrderItemAdd newAdd = BeanConvertUtils.convert(addDTO, OrderItemAdd.class);
            newAdd.setOrderItemAddId(uuidGenerator.gain());
            BaseBiz.setOperInfo(newAdd, operator, true);
            orderItemAddMapper.insert(newAdd);

        } else {
            if ("0".equals(addDTO.getAdditemId()) || CsStringUtils.isBlank(addDTO.getAdditemId())) {
                //删除加价项
                OrderItemAdd delAdd = new OrderItemAdd();
                BaseBiz.setOperInfo(delAdd, operator, false);
                delAdd.setDelFlg(true);
                orderItemAddMapper.updateByPrimaryKeySelective(delAdd);
            } else {
                //修改加价项
                OrderItemAdd updAdd = BeanConvertUtils.convert(addDTO, OrderItemAdd.class);
                BaseBiz.setOperInfo(updAdd, operator, false);
                orderItemAddMapper.updateByPrimaryKeySelective(updAdd);

            }
        }
    }

    private void updateByPrimaryKeySelective(String operator, BigDecimal machineShiftCost, BigDecimal othersAmount, String orderId, OrderItemDTO changeItem, OrderItemDTO updItemDTO) {
        if (changeItem.getOrderItemId().equals(updItemDTO.getOrderItemId())) {
            OrderItem updItem = BeanConvertUtils.convert(updItemDTO, OrderItem.class);
            BaseBiz.setOperInfo(updItem, operator, false);
            ItemResult<SpecialGoodsAttributeDTO> goodsResult =
                    goodsService.getSpecialGoodsAttribute(updItem.getGoodsId());
            if (!goodsResult.isSuccess()) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "获取商品特殊属性失败");
            }
            //更新台班费空载费
            if (goodsResult.getData().getConcreteFlag() == 1) {
                if (machineShiftCost != null) {
                    updItem.setConfirmMachinePrice(machineShiftCost);
                }
                if (othersAmount != null) {
                    updItem.setConfirmEmptyPrice(othersAmount);
                }
            }
            log.info("confirmOrder_updOrderItem:{}-{}", orderId, JSON.toJSONString(updItem));
            orderItemMapper.updateByPrimaryKeySelective(updItem);
        }
    }

    private void doPriceChangeAndQuantityChangeLogic(String operator, boolean priceChange, boolean quantityChange, String orderId, OrderDTO order) {
        if (priceChange || quantityChange) {
            //更新优惠信息：删除后增加
            List<String> objectIds = Lists.newArrayList();//订单优惠明细objectId
            objectIds.add(orderId);
            for (OrderItemDTO item : order.getOrderItems()) {
                objectIds.add(item.getOrderItemId());
            }
            List<OrderDiscountDetailDTO> oldDiscounts = baseOrderSearchBiz.getOrderDiscountDetails(objectIds);
            if (CollectionUtils.isNotEmpty(oldDiscounts)) {
                for (OrderDiscountDetailDTO discountDTO : oldDiscounts) {
                    OrderDiscountDetail delDis = new OrderDiscountDetail();
                    delDis.setDiscountdetailId(discountDTO.getDiscountdetailId());
                    delDis.setDelFlg(true);
                    BaseBiz.setOperInfo(delDis, operator, false);
                    orderDiscountDetailMapper.updateByPrimaryKey(delDis);//删除
                }
            }
            insertOrderDiscountDetailDTO(operator, orderId, order);
        }
    }

    private void insertOrderDiscountDetailDTO(String operator, String orderId, OrderDTO order) {
        if (CollectionUtils.isNotEmpty(order.getDiscountDetail())) {
            for (OrderDiscountDetailDTO discountDTO : order.getDiscountDetail()) {
                OrderDiscountDetail discount = BeanConvertUtils.convert(discountDTO, OrderDiscountDetail.class);
                if (CsStringUtils.isBlank(discount.getObjectId())) {
                    discount.setObjectId(orderId);
                }
                BaseBiz.setOperInfo(discount, operator, true);
                orderDiscountDetailMapper.insert(discount);//增加
            }

            PickingBillTypeEnum.SELLER_DELIVERY.getCode();
        }
    }

    private void doConcreteFlagLogic(OrderDTO order, BigDecimal machineShiftCost, BigDecimal othersAmount, String orderId) {
        OrderItemDTO orderItemDTO = order.getOrderItems().get(0);
        ItemResult<SpecialGoodsAttributeDTO> goodsResult =
                goodsService.getSpecialGoodsAttribute(orderItemDTO.getGoodsId());
        if (!goodsResult.isSuccess()) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "获取商品特殊属性失败");
        }
        //更新台班费空载费
        if (goodsResult.getData().getConcreteFlag() == 1) {
            OrderItem orderItem = new OrderItem();
            orderItem.setOrderItemId(orderItemDTO.getOrderItemId());
            if (machineShiftCost != null) {
                orderItem.setConfirmMachinePrice(machineShiftCost);
            }
            if (othersAmount != null) {
                orderItem.setConfirmEmptyPrice(othersAmount);
            }
            log.info("更新台班费空载费", orderId, JSON.toJSONString(orderItem));
            orderItemMapper.updateByPrimaryKeySelective(orderItem);
        }
    }


    @Override
    @Transactional
    public void cancelOrder(String orderId, String remarks, String operator, OperatorTypeEnum type) {
        log.info("cancelOrder:orderId-{},remarks-{},operator-{},type-{}", orderId, remarks, operator, type);
        if (CsStringUtils.isBlank(orderId)) {
            throw new BizException(BasicCode.PARAM_NULL, "订单Id");
        }

        OrderInfo order = this.selectForUpdate(orderId);
        if (order == null) {
            throw new BizException(BasicCode.DB_DATA_NOT_FOUND, "订单:" + orderId);
        }

        if ((OperatorTypeEnum.SELLER == type)
            && (baseOrderSearchBiz.isInPayment(orderId))) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "存在待支付的支付信息，无法取消");
        }

        OrderInfo updOrder = new OrderInfo();
        updOrder.setOrderId(orderId);
        //更新订单状态为已取消
        if (OperatorTypeEnum.SELLER == type) {
            updOrder.setIfSellerClose(true);
        } else if (OperatorTypeEnum.PLATFORM == type) {
            updOrder.setIfPlatformClose(true);
        }
        updOrder.setBuyerCancelReason(remarks);
        BaseBiz.setOperInfo(updOrder, operator, false);
        orderInfoMapper.updateByPrimaryKeySelective(updOrder);

        statusMachineFactory.driveStatus(orderId, OrderStatusEnum.CANCEL.code(), operator);
    }

    @Override
    public void deleteOrder(String orderId, String operator, OperatorTypeEnum type) {
        if (CsStringUtils.isBlank(orderId)) {
            throw new BizException(BasicCode.PARAM_NULL, "订单Id");
        }
        OrderInfo order = this.selectForUpdate(orderId);
        if (order == null) {
            throw new BizException(BasicCode.DB_DATA_NOT_FOUND, "订单:" + orderId);
        }
        OrderStatusEnum orderStatus = OrderStatusEnum.getByCode(order.getOrderStatus());
        if (OrderStatusEnum.CANCEL == orderStatus ||            //已取消
                OrderStatusEnum.CLOSED == orderStatus ||    //已关闭
                OrderStatusEnum.COMPLETED == orderStatus || //已完成
                OrderStatusEnum.EXPIRED == orderStatus) {    //已过期

            if (OperatorTypeEnum.SELLER == type) {//卖家不可见
                order.setSellerDel(true);
            } else if (OperatorTypeEnum.BUYER == type) {//买家不可见
                order.setBuyerDel(true);
            } else if (OperatorTypeEnum.PLATFORM == type) {//平台删除，所有用户不可见
                order.setDelFlg(true);
            }
        } else {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "该订单状态已变更，无法取消");
        }
        BaseBiz.setOperInfo(order, operator, false);
        orderInfoMapper.updateByPrimaryKeySelective(order);
    }

    @Override
    @Transactional
    public void closeOrder(String orderId, String remarks, String operator, OperatorTypeEnum type) {
        orderCloseBiz.orderClose(orderId, remarks, operator, type);
    }

    private OrderInfo selectForUpdate(String orderId) {
        Condition condition = new Condition(OrderInfo.class);
        condition.createCriteria().andEqualTo("orderId", orderId);
        condition.setForUpdate(true);
        List<OrderInfo> orderInfo = orderInfoMapper.selectByCondition(condition);
        if (CollectionUtils.isNotEmpty(orderInfo)) {
            return orderInfo.get(0);
        }
        return null;
    }

    /**
     * 返回已占用的资源数量
     *
     * @param orderId
     * @param operator
     */
    private void returnResourceNum(String orderId, String operator) {
        String uid = this.uuidGenerator.gain();
        log.info("returnResourceNum:{},{},{}", orderId, operator, uid);
        this.tryResourceNumOTR(orderId, operator, uid);
        log.info("returnResourceNum_tryResourceNumOTR:{},{},{}", orderId, operator, uid);

        try {
            this.confirmResourceNumOTR(orderId, operator, uid);
            log.info("returnResourceNum_confirmResourceNumOTR:{},{},{}", orderId, operator, uid);
        } catch (Exception e) {
            this.cancelResourceNumOTR(orderId, operator, uid);
            log.info("returnResourceNum_cancelResourceNumOTR:{},{},{}", orderId, operator, uid);
            throw e;
        }
    }

    /**
     * 锁定返还资源数量
     */
    private void tryResourceNumOTR(String orderId, String operator, String uid) {
        try {
            if (isProcessResSaleNum()) {
                OrderDTO order = baseOrderSearchBiz.getOrderInfo(orderId);
                HashMap<String, BigDecimal> map = Maps.newHashMap();
                for (OrderItemDTO item : order.getOrderItems()) {
                    map.put(item.getResourceId(), item.getItemQuantity().multiply(item.getConvertRate()));
                }
                resourceNumTCCService.tryResourceNumOrderToResource(map, orderId, operator, uid);
            }
        } catch (Exception e) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "锁定返还资源数量失败：" + e.getMessage());
        }
    }

    /**
     * 确定返还资源数量
     */
    private void confirmResourceNumOTR(String orderId, String operator, String uid) {
        try {
            if (isProcessResSaleNum()) {
                resourceNumTCCService.confirmResourceNumOrderToResource(orderId, operator, uid);
            }
        } catch (Exception e) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "确定返还资源数量失败：" + e.getMessage());
        }
    }

    /**
     * 解锁返还资源数量
     */
    private void cancelResourceNumOTR(String orderId, String operator, String uid) {
        try {
            if (isProcessResSaleNum()) {
                resourceNumTCCService.cancelResourceNumOrderToResource(orderId, operator, uid);
            }
        } catch (Exception e) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "无法解锁返还资源数量：" + e.getMessage());
        }
    }

    /**
     * 锁定资源可售数量
     */
    private void tryResourceNum(String orderId, List<OrderItemDTO> orderItems, String operator, String uid) {
        try {
            if (isProcessResSaleNum()) {
                HashMap<String, BigDecimal> map = Maps.newHashMap();
                for (OrderItemDTO item : orderItems) {
                    map.put(item.getResourceId(), item.getItemQuantity().multiply(item.getConvertRate()));
                }
                resourceNumTCCService.tryResourceNumResourceToOrder(map, orderId, operator, uid);
            }
        } catch (Exception e) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "锁定商品数量失败：" + e.getMessage());
        }
    }

    /**
     * 更新资源可售数量
     */
    private void confirmResourceNum(String orderId, String operator, String uid) {
        try {
            if (isProcessResSaleNum()) {
                resourceNumTCCService.confirmResourceNumResourceToOrder(orderId, operator, uid);
            }
        } catch (Exception e) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "确认锁定商品数量失败：" + e.getMessage());
        }
    }

    /**
     * 解锁资源可售数量
     */
    private void cancelResourceNum(String orderId, String operator, String uid) {
        try {
            if (isProcessResSaleNum()) {
                resourceNumTCCService.cancelResourceNumResourceToOrder(orderId, operator, uid);
            }
        } catch (Exception e) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "无法解锁商品数量：" + e.getMessage());
        }
    }

    @Override
    @Transactional(propagation = Propagation.NESTED)
    public void updateOrderTakeStatus(String orderId, OrderTakeStatus orderTakeStatus, String operator) {
        OrderInfo order = orderInfoMapper.selectByPrimaryKey(orderId);
        if (order == null) {
            throw new BizException(BasicCode.DB_DATA_NOT_FOUND, "订单：" + orderId);
        }
        OrderInfo updOrder = new OrderInfo();
        updOrder.setOrderId(orderId);
        updOrder.setDeliverStatus(orderTakeStatus.getCode());
        if (OrderTakeStatus.FINISHED.equals(orderTakeStatus)) {
            this.orderComplete(order, operator, orderTakeStatus);
            //完成订单时触发未分账金额分账
            billSplitBiz.completeTriggerSplit(orderId);
        } else if (OrderTakeStatus.DELIVERING.equals(orderTakeStatus)) {
            BaseBiz.setOperInfo(updOrder, operator, false);
            orderInfoMapper.updateByPrimaryKeySelective(updOrder);
            statusMachineFactory.driveStatus(updOrder.getOrderId(), OrderStatusEnum.IN_DELIVERY.code(), operator);
        }
    }

    public void orderComplete(OrderInfo order, String operator, OrderTakeStatus orderTakeStatus) {
        OrderInfo updOrder = new OrderInfo();
        updOrder.setOrderId(order.getOrderId());
        updOrder.setDeliverStatus(orderTakeStatus.getCode());
        if (orderErpBiz.hasErpOrder(order.getSellerId(), MemberConfigEnum.ERP_ORDER_FLG.getKeyCode())) {
            BaseBiz.setOperInfo(updOrder, operator, false);
            orderInfoMapper.updateByPrimaryKeySelective(updOrder);
            //
            orderErpBiz.orderCompleteToErp(order.getOrderId());
        } else {
            Example orderPayinfoExample = new Example(OrderPayinfo.class);
            Example.Criteria orderPayinfoExampleCriteria = orderPayinfoExample.createCriteria();
            orderPayinfoExampleCriteria.andEqualTo("delFlg", false);
            orderPayinfoExampleCriteria.andEqualTo("objectId", order.getOrderId());
            List<String> payinfoStatusList = Lists.newArrayList();
            payinfoStatusList.add(PayStatusEnum.IN_PAYMENT.code());
            payinfoStatusList.add(PayStatusEnum.WAIT_PAYMENT.code());
            orderPayinfoExampleCriteria.andIn("payinfoStatus", payinfoStatusList);
            List<OrderPayinfo> orderPayinfos = orderPayinfoMapper.selectByExample(orderPayinfoExample);
            log.info("==========orderPayinfos========={}", JSON.toJSONString(orderPayinfos));
            //当订单确认退款时需要先完成订单状态再变更支付单状态
            if (CollectionUtils.isEmpty(orderPayinfos) ||
                    CsStringUtils.equals(order.getAdjustStatus(), AdjustStatusEnum.CONFIRMED.getCode())) {
                updOrder.setCompleteTime(new Date());
                BaseBiz.setOperInfo(updOrder, operator, false);
                orderInfoMapper.updateByPrimaryKeySelective(updOrder);
                statusMachineFactory.driveStatus(updOrder.getOrderId(), OrderStatusEnum.COMPLETED.code(), operator);
                takeInfoBiz.doOrderFinishTakeInfo(order.getOrderId(), operator);
            }
        }
        //发送消息
        messageProducer.sendOrderCompletedAsync(order.getOrderId());
        //累计会员积分信息
        addUpMemberIntegral(order);
    }

    /**
     * 累计会员积分信息
     *
     * @param order 订单信息
     */
    private void addUpMemberIntegral(OrderInfo order) {
        MemberIntegralMqMessageDTO integral = new MemberIntegralMqMessageDTO();
        integral.setBuyerId(order.getBuyerId());
        integral.setSellerId(order.getSellerId());
        integral.setEventTime(new Date());
        integral.setRemark("完成订单");
        integral.setOrderNumber(order.getOrderCode());
        List<MemberIntegralMqMessageGoodsInfoDTO> goodsList = Lists.newArrayList();
        Example orderItemExample = new Example(OrderItem.class);
        Example.Criteria orderItemExampleCriteria = orderItemExample.createCriteria();
        orderItemExampleCriteria.andEqualTo("orderId", order.getOrderId());
        orderItemExampleCriteria.andEqualTo("delFlg", false);
        List<OrderItem> orderItems = orderItemMapper.selectByExample(orderItemExample);
        if (CollectionUtils.isEmpty(orderItems)) {
            return;
        }
        orderItems.forEach(orderItem -> {
            if (CsStringUtils.isBlank(orderItem.getCategoryCode()) ||
                    orderItem.getItemSendQuantity().compareTo(BigDecimal.ZERO) < 1) {
                return;
            }
            MemberIntegralMqMessageGoodsInfoDTO goodsInfoDTO = new MemberIntegralMqMessageGoodsInfoDTO();
            goodsInfoDTO.setGoodsName(orderItem.getGoodsName());
            goodsInfoDTO.setCategoryCode(orderItem.getCategoryCode().substring(0, 9));
            goodsInfoDTO.setAmount(takeInfoBiz.transferIntoDefaultUnit(orderItem.getItemSendQuantity(),
                    orderItem.getUnits(), orderItem.getGoodsId()));
            goodsList.add(goodsInfoDTO);
        });
        integral.setGoodsList(goodsList);
        if (CollectionUtils.isNotEmpty(goodsList)) {
            syncIntegralHandler.syncIntegralInfo(integral);
        }
    }

    @Override
    public void sendQuantityPostProcessor(String orderId, UpdateTakeInfoShippedDTO updateTakeInfoShippedDTO) {
    }

}
