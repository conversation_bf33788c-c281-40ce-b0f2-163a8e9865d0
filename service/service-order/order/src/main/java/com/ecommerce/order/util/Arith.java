package com.ecommerce.order.util;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @Date: 2019/4/26 2:55 PM
 * @Description
 */
public class Arith {

    private static final int DEF_DIV_SCALE = 10;
    private static final int SCALE_4 = 4;

    private Arith() {}

    /**
     * 返回格式化后的小数
     */
    public static BigDecimal format(BigDecimal num,int scale){
        return num.setScale(scale);
    }

    /**
     * 提供精确加法计算的add方法
     *
     * @param value1 被加数
     * @param value2 加数
     * @return 两个参数的和
     */
    public static double add(double value1, double value2) {
        BigDecimal b1 = new BigDecimal(String.valueOf(value1));
        BigDecimal b2 = new BigDecimal(String.valueOf(value2));
        return b1.add(b2).doubleValue();
    }

    /**
     * 提供精确减法运算的sub方法
     *
     * @param value1 被减数
     * @param value2 减数
     * @return 两个参数的差
     */
    public static double sub(double value1, double value2) {
        BigDecimal b1 = new BigDecimal(String.valueOf(value1));
        BigDecimal b2 = new BigDecimal(String.valueOf(value2));
        return b1.subtract(b2).doubleValue();
    }

    /**
     * int数据类型减法
     *
     * @param value1
     * @param value2
     * @return
     */
    public static int subInt(int value1, int value2) {
        BigDecimal b1 = new BigDecimal(String.valueOf(value1));
        BigDecimal b2 = new BigDecimal(String.valueOf(value2));
        return b1.subtract(b2).intValue();
    }

    /**
     * 提供精确乘法运算的mul方法
     *
     * @param value1 被乘数
     * @param value2 乘数
     * @return 两个参数的积
     */
    public static double mul(double value1, double value2) {
        BigDecimal b1 = new BigDecimal(String.valueOf(value1));
        BigDecimal b2 = new BigDecimal(String.valueOf(value2));
        return b1.multiply(b2).setScale(2, RoundingMode.HALF_UP).doubleValue();
    }


    /**
     * 提供（相对）精确的除法运算，当发生除不尽的情况时，精确到 小数点以后10位，以后的数字四舍五入。
     *
     * @param v1 被除数
     * @param v2 除数
     * @return 两个参数的商
     */
    public static double div(double v1, double v2) {
        return div(v1, v2, DEF_DIV_SCALE);
    }

    /**
     * 提供（相对）精确的除法运算。当发生除不尽的情况时，由scale参数指 定精度，以后的数字四舍五入。
     *
     * @param v1 被除数
     * @param v2 除数
     * @param scale 表示表示需要精确到小数点以后几位。
     * @return 两个参数的商
     */
    public static double div(double v1, double v2, int scale) {
        if (scale < 0) {
            throw new IllegalArgumentException("The scale must be a positive integer or zero 1");
        }
        BigDecimal b1 = new BigDecimal(Double.toString(v1));
        BigDecimal b2 = new BigDecimal(Double.toString(v2));
        return b1.divide(b2, scale, RoundingMode.HALF_UP).doubleValue();
    }
    public static BigDecimal divDecimal(double v1, double v2, int scale) {
        if (scale < 0) {
            throw new IllegalArgumentException("The scale must be a positive integer or zero 2");
        }
        BigDecimal b1 = new BigDecimal(Double.toString(v1));
        BigDecimal b2 = new BigDecimal(Double.toString(v2));
        return b1.divide(b2, scale, RoundingMode.HALF_UP);
    }

    /**
     * 提供精确的小数位四舍五入处理。
     *
     * @param v 需要四舍五入的数字
     * @param scale 小数点后保留几位
     * @return 四舍五入后的结果
     */
    public static double round(double v, int scale) {
        if (scale < 0) {
            throw new IllegalArgumentException("The scale must be a positive integer or zero 3");
        }
        BigDecimal b = new BigDecimal(Double.toString(v));
        BigDecimal one = new BigDecimal("1");
        return b.divide(one, scale, RoundingMode.HALF_UP).doubleValue();
    }

    /**
     * 计算百分比
     *
     * @param value1
     * @param value2
     * @return
     * @throws IllegalAccessException
     */
    public static double percentageDouble(Double value1, Double value2) {
        if (value1 == null || value1.doubleValue() == 0) {
            return 0.0;
        }
        if (value2 == null || value2.doubleValue() == 0) {
            return 0.0;
        }
        BigDecimal b1 = new BigDecimal(String.valueOf(value1));
        BigDecimal b2 = new BigDecimal(String.valueOf(value2));
        return mul(b1.divide(b2, SCALE_4, RoundingMode.HALF_UP).doubleValue(), 100);
    }

    /**
     * 计算两个整数的百分比
     *
     * @param value1
     * @param value2
     * @return
     */
    public static double percentageInteger(Integer value1, Integer value2) {
        if (value1 == null || value1.intValue() == 0) {
            return 0.0;
        }
        if (value2 == null || value2.intValue() == 0) {
            return 0.0;
        }
        BigDecimal b1 = new BigDecimal(String.valueOf(value1));
        BigDecimal b2 = new BigDecimal(String.valueOf(value2));
        double tmp = b1.divide(b2, SCALE_4, RoundingMode.HALF_UP).doubleValue();
        return mul(tmp, 100);
    }

    /**
     * 比较两个对象是否相同
     * @param one
     * @param two
     * @param <T>
     * @return
     */
    public static <T> boolean isSame(Comparable<T> one, T two) {
        if (one == null || two == null) {
            return one == null && two == null;
        }
        return one.compareTo(two) == 0;
    }

}
