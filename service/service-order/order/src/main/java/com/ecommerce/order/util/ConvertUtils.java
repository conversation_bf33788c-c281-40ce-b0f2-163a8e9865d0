package com.ecommerce.order.util;

import com.github.pagehelper.PageInfo;

import java.util.function.Function;
import java.util.stream.Collectors;

public class ConvertUtils {

    // 添加私有构造函数，防止实例化
    private ConvertUtils() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated.");
    }

    public static <S, T> PageInfo<T> convert(PageInfo<S> source, Function<S, T> function) {
        PageInfo<T> result = new PageInfo<>();
        result.setList(source.getList().stream().map(function).toList());
        result.setTotal(source.getTotal());

        result.setPageNum(source.getPageNum());
        result.setPageSize(source.getPageSize());
        result.setSize(source.getSize());
        result.setStartRow(source.getStartRow());
        result.setEndRow(source.getEndRow());
        result.setPages(source.getPages());
        result.setPrePage(source.getPrePage());
        result.setNextPage(source.getNextPage());
        result.setIsFirstPage(source.isIsFirstPage());
        result.setIsLastPage(source.isIsLastPage());
        result.setHasPreviousPage(result.isHasPreviousPage());
        result.setHasNextPage(result.isHasNextPage());
        result.setNavigatePages(source.getNavigatePages());
        result.setNavigatepageNums(source.getNavigatepageNums());
        result.setNavigateFirstPage(source.getNavigateFirstPage());
        result.setNavigateLastPage(source.getNavigateLastPage());
        return result;
    }
}
