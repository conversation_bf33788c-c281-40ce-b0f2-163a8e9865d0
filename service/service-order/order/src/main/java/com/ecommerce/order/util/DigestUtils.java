package com.ecommerce.order.util;

import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;

/**
 * <AUTHOR>
 * @Description:
 * @Date: Create in 下午5:10 18/9/14
 */
public class DigestUtils {


    private DigestUtils() {
        throw new UnsupportedOperationException("工具类不能被实例化");
    }

    /**
     * MD5摘要算法
     *
     * @param content 完成内容
     * @return 摘要串
     */
    public static String md5Digest(String content) {
        try {
            String algorithm = "MD5";
            MessageDigest md = MessageDigest.getInstance(algorithm);
            byte[] bytes = md.digest(content.getBytes(StandardCharsets.UTF_8));
            return Bytes.bytes2hex(bytes);
        } catch (Exception e) {
            throw new BizException(BasicCode.INVALID_PARAM, "生成内容摘要失败:content=" + content);
        }
    }
}
