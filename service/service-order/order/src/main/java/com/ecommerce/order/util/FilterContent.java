package com.ecommerce.order.util;

import com.ecommerce.order.api.constant.ConstantEvaluate;
import org.jetbrains.annotations.NotNull;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date: 2019/4/29 6:32 PM
 * @Description
 */
public class FilterContent {
    // 添加私有构造函数以防止实例化
    private FilterContent() {
        throw new UnsupportedOperationException("Utility class should not be instantiated.");
    }

    public static String filterContent(String content, Integer type, BigDecimal score){
        if(content == null || content.equals("")){
            switch(type){
                case 1:
                    return caseOne(score);
                case 2:
                    return caseTwo(score);
                case 3:
                    return caseThree(score);
                default:
                    return ConstantEvaluate.DEFAULT_EVA_CONTENT_GOODS_GOOD;
            }
        }
        //TODO 这里需要对content字段进行敏感词过滤
        return content;
    }

    @NotNull
    private static String caseThree(BigDecimal score) {
        if(score.floatValue() <= ConstantEvaluate.BAD_EVA_SCORE){
            return ConstantEvaluate.DEFAULT_EVA_CONTENT_SERVI_BAD;
        }else if(score.floatValue() > ConstantEvaluate.BAD_EVA_SCORE && score.floatValue() <= ConstantEvaluate.MID_EVA_SCORE){
            return ConstantEvaluate.DEFAULT_EVA_CONTENT_SERVI_MID;
        }else{
            return ConstantEvaluate.DEFAULT_EVA_CONTENT_SERVI_GOOD;
        }
    }

    @NotNull
    private static String caseTwo(BigDecimal score) {
        if(score.floatValue() <= ConstantEvaluate.BAD_EVA_SCORE){
            return ConstantEvaluate.DEFAULT_EVA_CONTENT_LOGIS_BAD;
        }else if(score.floatValue() > ConstantEvaluate.BAD_EVA_SCORE && score.floatValue() <= ConstantEvaluate.MID_EVA_SCORE){
            return ConstantEvaluate.DEFAULT_EVA_CONTENT_LOGIS_MID;
        }else{
            return ConstantEvaluate.DEFAULT_EVA_CONTENT_LOGIS_GOOD;
        }
    }

    @NotNull
    private static String caseOne(BigDecimal score) {
        if(score.floatValue() <= ConstantEvaluate.BAD_EVA_SCORE){
            return ConstantEvaluate.DEFAULT_EVA_CONTENT_GOODS_BAD;
        }else if(score.floatValue() > ConstantEvaluate.BAD_EVA_SCORE && score.floatValue() <= ConstantEvaluate.MID_EVA_SCORE){
            return ConstantEvaluate.DEFAULT_EVA_CONTENT_GOODS_MID;
        }else{
            return ConstantEvaluate.DEFAULT_EVA_CONTENT_GOODS_GOOD;
        }
    }
}