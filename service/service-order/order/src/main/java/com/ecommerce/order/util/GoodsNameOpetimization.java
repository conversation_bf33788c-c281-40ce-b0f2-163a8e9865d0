
package com.ecommerce.order.util;

import lombok.extern.slf4j.Slf4j;
import com.ecommerce.common.utils.CsStringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 *
 * @version 创建时间：2021年7月7日 下午4:37:01
 */
@Slf4j
public class GoodsNameOpetimization {

	// 添加私有构造函数以防止实例化
	private GoodsNameOpetimization() {
		throw new UnsupportedOperationException("This is a utility class and cannot be instantiated.");
	}

	public static String queryOptimizationByGoodName(String keyWords) {
		log.info("关键词优化入参keyWords：{}", keyWords);
        if (CsStringUtils.isEmpty(keyWords)) {
			return keyWords;
		}
		
		String regex0 = "(PO)(.*?)";
		List<String> list0 = new ArrayList<>();
		keyWords = pattern0Logic(keyWords, regex0, list0);

		String regex1 = "(PC)(.*?)";
		List<String> list1 = new ArrayList<>();
		keyWords = pattern1Logic(keyWords, regex1, list1);

		String regex2 = "(PII)(.*?)";
		List<String> list2 = new ArrayList<>();
		keyWords = pattern2Logic(keyWords, regex2, list2);

		String regex3 = "(PP)(.*?)";
		List<String> list3 = new ArrayList<>();
		keyWords = pattern3Logic(keyWords, regex3, list3);

		keyWords = pattern4Logic(keyWords, list3);
		return keyWords;
	}

	private static String pattern0Logic(String keyWords, String regex0, List<String> list0) {
		Pattern pattern0 = Pattern.compile(regex0, Pattern.CASE_INSENSITIVE);
		Matcher m0 = pattern0.matcher(keyWords);
		// 得到所有匹配正则表达式的字段
		while (m0.find()) {
			int i = 1;
			list0.add(m0.group(i));
		}
		for (String str : list0) {
			String pstr = null;
			if (keyWords.length() > 2) {
				pstr = "P.O ";
			} else {
				pstr = "P.O";
			}
			Pattern p = Pattern.compile(str, Pattern.CASE_INSENSITIVE);
			Matcher matcher = p.matcher(keyWords);
			keyWords = matcher.replaceAll(pstr);
		}
		log.info("PO优化结果：{}", keyWords);
		return keyWords;
	}

	private static String pattern1Logic(String keyWords, String regex1, List<String> list1) {
		Pattern pattern1 = Pattern.compile(regex1, Pattern.CASE_INSENSITIVE);
		Matcher m1 = pattern1.matcher(keyWords);
		// 得到所有匹配正则表达式的字段
		while (m1.find()) {
			int i = 1;
			list1.add(m1.group(i));
		}
		for (String str : list1) {
			String pstr = null;
			if (keyWords.length() > 2) {
				pstr = "P.C ";
			} else {
				pstr = "P.C";
			}
			Pattern p = Pattern.compile(str, Pattern.CASE_INSENSITIVE);
			Matcher matcher = p.matcher(keyWords);
			keyWords = matcher.replaceAll(pstr);
		}
		log.info("PC优化结果：{}", keyWords);
		return keyWords;
	}

	private static String pattern2Logic(String keyWords, String regex2, List<String> list2) {
		Pattern pattern2 = Pattern.compile(regex2, Pattern.CASE_INSENSITIVE);
		Matcher m2 = pattern2.matcher(keyWords);
		// 得到所有匹配正则表达式的字段
		while (m2.find()) {
			int i = 1;
			list2.add(m2.group(i));
		}
		for (String str : list2) {
			String pstr = null;
			if (keyWords.length() > 3) {
				pstr = "P·II ";
			} else {
				pstr = "P·II";
			}
			Pattern p = Pattern.compile(str, Pattern.CASE_INSENSITIVE);
			Matcher matcher = p.matcher(keyWords);
			keyWords = matcher.replaceAll(pstr);
		}
		log.info("PII优化结果：{}", keyWords);
		return keyWords;
	}

	private static String pattern3Logic(String keyWords, String regex3, List<String> list3) {
		Pattern pattern3 = Pattern.compile(regex3, Pattern.CASE_INSENSITIVE);
		Matcher m3 = pattern3.matcher(keyWords);
		// 得到所有匹配正则表达式的字段
		while (m3.find()) {
			int i = 1;
			list3.add(m3.group(i));
		}
		for (String str : list3) {
			String pstr = null;
			if (keyWords.length() > 2) {
				pstr = "P·P ";
			} else {
				pstr = "P·P";
			}
			Pattern p = Pattern.compile(str, Pattern.CASE_INSENSITIVE);
			Matcher matcher = p.matcher(keyWords);
			keyWords = matcher.replaceAll(pstr);
		}
		log.info("PP优化结果：{}", keyWords);
		return keyWords;
	}




	private static String pattern4Logic(String keyWords, List<String> list3) {
		String regex4 = "(M)(.*?)";
		List<String> list4 = new ArrayList<>();
		Pattern pattern4 = Pattern.compile(regex4, Pattern.CASE_INSENSITIVE);
		Matcher m4 = pattern4.matcher(keyWords);
		// 得到所有匹配正则表达式的字段
		while (m4.find()) {
			int i = 1;
			list3.add(m4.group(i));
		}
		for (String str : list4) {
			String pstr = null;
			if (keyWords.length() > 1) {
				pstr = "M ";
			} else {
				pstr = "M";
			}
			Pattern p = Pattern.compile(str, Pattern.CASE_INSENSITIVE);
			Matcher matcher = p.matcher(keyWords);
			keyWords = matcher.replaceAll(pstr);
		}
		log.info("M优化结果：{}", keyWords);
		return keyWords;
	}

}
