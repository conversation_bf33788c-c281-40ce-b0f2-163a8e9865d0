mybatis:
     typeAliasesPackage: com.ecommerce.order.dao
     mapperScanPackage: com.ecommerce.order.dao
     mapperLocations: "classpath:/mapper/*.xml"
     configLocation: "classpath:/mybatis-config.xml"
spring:
  config:
    import: 'optional:configserver:'
  cloud:
    config:
      uri: http://127.0.0.1:8888
      profile: dev
      label: asset-2025
      name: order,db,redis,rabbitmq,hystrix,vip-server,xxl-job,eureka,kafka
  application:
    name: order
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true

logging:
  pattern:
    # Logging pattern containing traceId and spanId; no longer provided through Sleuth by default
    level: "%5p [${spring.application.name:},%X{traceId:-},%X{spanId:-}]"


mapper:
  resolve-class: com.ecommerce.common.tk.MapperEntityResolve