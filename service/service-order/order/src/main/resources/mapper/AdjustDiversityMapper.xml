<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.order.dao.mapper.AdjustDiversityMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.order.dao.vo.AdjustDiversity">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="adjust_diversity_id" jdbcType="VARCHAR" property="adjustDiversityId" />
    <result column="adjust_strategy_id" jdbcType="VARCHAR" property="adjustStrategyId" />
    <result column="adjust_record_id" jdbcType="VARCHAR" property="adjustRecordId" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="order_num" jdbcType="VARCHAR" property="orderNum" />
    <result column="ec_pre_price" jdbcType="DECIMAL" property="ecPrePrice" />
    <result column="erp_pre_price" jdbcType="DECIMAL" property="erpPrePrice" />
    <result column="erp_next_price" jdbcType="DECIMAL" property="erpNextPrice" />
    <result column="ec_adjust_quantity" jdbcType="DECIMAL" property="ecAdjustQuantity" />
    <result column="erp_adjust_quantity" jdbcType="DECIMAL" property="erpAdjustQuantity" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
  </resultMap>
</mapper>