<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.order.dao.mapper.AdjustStrategyMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.order.dao.vo.AdjustStrategy">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="adjust_strategy_id" jdbcType="VARCHAR" property="adjustStrategyId" />
    <result column="adjust_strategy_num" jdbcType="VARCHAR" property="adjustStrategyNum" />
    <result column="adjust_source" jdbcType="INTEGER" property="adjustSource" />
    <result column="external_strategy_num" jdbcType="VARCHAR" property="externalStrategyNum" />
    <result column="seller_id" jdbcType="VARCHAR" property="sellerId" />
    <result column="seller_name" jdbcType="VARCHAR" property="sellerName" />
    <result column="external_customer_code" jdbcType="VARCHAR" property="externalCustomerCode" />
    <result column="buyer_id" jdbcType="VARCHAR" property="buyerId" />
    <result column="buyer_type" jdbcType="VARCHAR" property="buyerType" />
    <result column="buyer_name" jdbcType="VARCHAR" property="buyerName" />
    <result column="buyer_contact" jdbcType="VARCHAR" property="buyerContact" />
    <result column="buyer_contact_way" jdbcType="VARCHAR" property="buyerContactWay" />
    <result column="adjust_begin_time" jdbcType="TIMESTAMP" property="adjustBeginTime" />
    <result column="adjust_end_time" jdbcType="TIMESTAMP" property="adjustEndTime" />
    <result column="sale_region_path" jdbcType="VARCHAR" property="saleRegionPath" />
    <result column="sale_region1" jdbcType="VARCHAR" property="saleRegion1" />
    <result column="sale_region2" jdbcType="VARCHAR" property="saleRegion2" />
    <result column="sale_region3" jdbcType="VARCHAR" property="saleRegion3" />
    <result column="sale_region4" jdbcType="VARCHAR" property="saleRegion4" />
    <result column="sale_region5" jdbcType="VARCHAR" property="saleRegion5" />
    <result column="adjust_reason" jdbcType="VARCHAR" property="adjustReason" />
    <result column="execute_time" jdbcType="TIMESTAMP" property="executeTime" />
    <result column="execute_status" jdbcType="VARCHAR" property="executeStatus" />
    <result column="adjust_total_amount" jdbcType="DECIMAL" property="adjustTotalAmount" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
  </resultMap>
  <select id="queryAdjustStrategyList" parameterType="com.ecommerce.order.api.dto.adjust.AdjustStrategyQueryDTO" resultType="com.ecommerce.order.api.dto.adjust.AdjustStrategyListDTO">
    SELECT
      t1.adjust_strategy_id as adjustStrategyId,
      t1.adjust_strategy_num as adjustStrategyNum,
      t1.buyer_id as buyerId,
      t1.buyer_name as buyerName,
      t1.external_customer_code as externalCustomerCode,
      t1.sale_region1 as saleRegion1,
      t1.adjust_begin_time as adjustBeginTime,
      t1.adjust_end_time as adjustEndTime,
      t1.execute_time as executeTime,
      t1.execute_status as executeStatus,
      t1.create_time as createTime,
      t1.adjust_source as adjustSource
    FROM
      tr_order_adjust_strategy as t1
    <where>
      t1.del_flg = 0
      <if test="sellerId != null and sellerId != ''">
        <![CDATA[AND t1.seller_id = #{sellerId}]]>
      </if>
      <if test="buyerId != null and buyerId != ''">
        <![CDATA[AND t1.buyer_id = #{buyerId}]]>
      </if>
      <if test="externalCustomerCode != null and externalCustomerCode != ''">
        <![CDATA[AND t1.external_customer_code = #{externalCustomerCode}]]>
      </if>
      <if test="saleRegion1 != null and saleRegion1 != ''">
        <![CDATA[AND t1.sale_region1 = #{saleRegion1}]]>
      </if>
      <if test="executeStatus != null and executeStatus != ''">
        <![CDATA[AND t1.execute_status = #{executeStatus}]]>
      </if>
      <if test="executeTimeStart != null">
        <![CDATA[AND t1.execute_time >= #{executeTimeStart}]]>
      </if>
      <if test="executeTimeEnd != null">
        <![CDATA[AND t1.execute_time <= #{executeTimeEnd}]]>
      </if>
      <if test="adjustBeginTime != null">
        <![CDATA[AND t1.adjust_end_time >= #{adjustBeginTime}]]>
      </if>
      <if test="adjustEndTime != null">
        <![CDATA[AND t1.adjust_begin_time <= #{adjustEndTime}]]>
      </if>
    </where>
    ORDER BY
      t1.create_time DESC
  </select>
</mapper>