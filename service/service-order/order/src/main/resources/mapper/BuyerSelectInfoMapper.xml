<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.order.dao.mapper.BuyerSelectInfoMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.order.dao.vo.BuyerSelectInfo">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="buyerselect_id" jdbcType="VARCHAR" property="buyerselectId" />
    <result column="goods_id" jdbcType="VARCHAR" property="goodsId" />
    <result column="goods_type" jdbcType="VARCHAR" property="goodsType" />
    <result column="select_type" jdbcType="VARCHAR" property="selectType" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="order_item_id" jdbcType="VARCHAR" property="orderItemId" />
    <result column="cart_id" jdbcType="VARCHAR" property="cartId" />
    <result column="resource_id" jdbcType="VARCHAR" property="resourceId" />
    <result column="option1_code" jdbcType="VARCHAR" property="option1Code" />
    <result column="option1_id" jdbcType="VARCHAR" property="option1Id" />
    <result column="option1_value" jdbcType="VARCHAR" property="option1Value" />
    <result column="buyer_id" jdbcType="VARCHAR" property="buyerId" />
    <result column="option1_unit" jdbcType="VARCHAR" property="option1Unit" />
    <result column="option2_code" jdbcType="VARCHAR" property="option2Code" />
    <result column="option2_id" jdbcType="VARCHAR" property="option2Id" />
    <result column="option2_value" jdbcType="VARCHAR" property="option2Value" />
    <result column="option2_name" jdbcType="VARCHAR" property="option2Name" />
    <result column="option2_pic" jdbcType="VARCHAR" property="option2Pic" />
    <result column="option2_type" jdbcType="VARCHAR" property="option2Type" />
    <result column="option2_unit" jdbcType="VARCHAR" property="option2Unit" />
    <result column="seller_id" jdbcType="VARCHAR" property="sellerId" />
    <result column="option3_code" jdbcType="VARCHAR" property="option3Code" />
    <result column="option3_id" jdbcType="VARCHAR" property="option3Id" />
    <result column="option3_value" jdbcType="VARCHAR" property="option3Value" />
    <result column="option3_name" jdbcType="VARCHAR" property="option3Name" />
    <result column="option3_pic" jdbcType="VARCHAR" property="option3Pic" />
    <result column="option3_type" jdbcType="VARCHAR" property="option3Type" />
    <result column="option3_unit" jdbcType="VARCHAR" property="option3Unit" />
    <result column="option4_code" jdbcType="VARCHAR" property="option4Code" />
    <result column="option4_id" jdbcType="VARCHAR" property="option4Id" />
    <result column="option4_value" jdbcType="VARCHAR" property="option4Value" />
    <result column="option4_name" jdbcType="VARCHAR" property="option4Name" />
    <result column="option4_pic" jdbcType="VARCHAR" property="option4Pic" />
    <result column="option4_type" jdbcType="VARCHAR" property="option4Type" />
    <result column="option4_unit" jdbcType="VARCHAR" property="option4Unit" />
    <result column="option1_name" jdbcType="VARCHAR" property="option1Name" />
    <result column="option1_pic" jdbcType="VARCHAR" property="option1Pic" />
    <result column="option1_type" jdbcType="VARCHAR" property="option1Type" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
  </resultMap>
</mapper>