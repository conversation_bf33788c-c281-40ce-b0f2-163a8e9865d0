<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.order.dao.mapper.ConcreteAdjustGoodsMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.order.dao.vo.ConcreteAdjustGoods">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="adjust_goods_id" jdbcType="VARCHAR" property="adjustGoodsId" />
    <result column="adjust_plan_id" jdbcType="VARCHAR" property="adjustPlanId" />
    <result column="adjust_plan_code" jdbcType="VARCHAR" property="adjustPlanCode" />
    <result column="goods_id" jdbcType="VARCHAR" property="goodsId" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="commodity_code" jdbcType="VARCHAR" property="commodityCode" />
    <result column="trading_quantity" jdbcType="DECIMAL" property="tradingQuantity" />
    <result column="trading_amount" jdbcType="DECIMAL" property="tradingAmount" />
    <result column="latest_trading_amount" jdbcType="DECIMAL" property="latestTradingAmount" />
    <result column="adjust_trading_amount" jdbcType="DECIMAL" property="adjustTradingAmount" />
    <result column="adjust_price" jdbcType="DECIMAL" property="adjustPrice" />
    <result column="adjust_status" jdbcType="VARCHAR" property="adjustStatus" />
    <result column="del_flg" jdbcType="INTEGER" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <select id="queryGoodsInitialTradingAmount" parameterType="com.ecommerce.order.api.dto.concrete.ConcreteAdjustGoodsQueryDTO" resultType="com.ecommerce.order.api.dto.concrete.ConcreteAdjustGoodsMapDTO">
    SELECT
      t1.goods_id as goodsId,
      t1.trading_amount as tradingAmount
    FROM
      tr_concrete_adjust_goods as t1
    LEFT JOIN
      tr_concrete_adjust_plan as t2 on t1.adjust_plan_id=t2.adjust_plan_id
    WHERE
      t1.del_flg = 0
      AND t2.contract_sequence = #{contractSequence}
      <if test="adjustGoodsIdList != null and adjustGoodsIdList.size() &gt; 0">
        AND t1.goods_id IN
        <foreach close=")" collection="adjustGoodsIdList" index="index" item="adjustGoodsId" open="(" separator=",">
          #{adjustGoodsId}
        </foreach>
      </if>
    GROUP BY
      t1.goods_id
    ORDER BY
      t1.create_time ASC;
  </select>

</mapper>