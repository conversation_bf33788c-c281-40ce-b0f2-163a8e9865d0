<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.order.dao.mapper.ConcreteAdjustOrderMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.order.dao.vo.ConcreteAdjustOrder">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="adjust_order_id" jdbcType="VARCHAR" property="adjustOrderId" />
    <result column="adjust_plan_id" jdbcType="VARCHAR" property="adjustPlanId" />
    <result column="adjust_plan_code" jdbcType="VARCHAR" property="adjustPlanCode" />
    <result column="adjust_goods_id" jdbcType="VARCHAR" property="adjustGoodsId" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="order_code" jdbcType="VARCHAR" property="orderCode" />
    <result column="deliver_way" jdbcType="VARCHAR" property="deliverWay" />
    <result column="if_mortar" jdbcType="BIT" property="ifMortar" />
    <result column="order_create_time" jdbcType="TIMESTAMP" property="orderCreateTime" />
    <result column="order_complete_time" jdbcType="TIMESTAMP" property="orderCompleteTime" />
    <result column="trading_quantity" jdbcType="DECIMAL" property="tradingQuantity" />
    <result column="trading_price" jdbcType="DECIMAL" property="tradingPrice" />
    <result column="latest_trading_price" jdbcType="DECIMAL" property="latestTradingPrice" />
    <result column="adjust_trading_price" jdbcType="DECIMAL" property="adjustTradingPrice" />
    <result column="adjust_status" jdbcType="VARCHAR" property="adjustStatus" />
    <result column="del_flg" jdbcType="INTEGER" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <select id="queryOrderInitialTradingPrice" resultType="com.ecommerce.order.api.dto.concrete.ConcreteAdjustOrderMapDTO">
    SELECT
      order_id as orderId,
      trading_price as tradingPrice
    FROM
      tr_concrete_adjust_order
    WHERE
      del_flg = 0
      <if test="orderIdList != null and orderIdList.size() &gt; 0">
        AND order_id IN
        <foreach close=")" collection="orderIdList" index="index" item="orderId" open="(" separator=",">
          #{orderId}
        </foreach>
      </if>
      <if test="ifMortar != null">
        AND if_mortar = #{ifMortar}
      </if>
    GROUP BY
      order_id
    ORDER BY
      create_time ASC
  </select>
  <select id="countOrderAdjustData" resultType="com.ecommerce.order.api.dto.concrete.ConcreteAdjustOrderMapDTO">
      SELECT
        order_id as orderId,
        count(order_id) as adjustCount
      FROM
        tr_concrete_adjust_order
      WHERE
        del_flg = 0
        <if test="orderIdList != null and orderIdList.size() &gt; 0">
          AND order_id IN
          <foreach close=")" collection="orderIdList" index="index" item="orderId" open="(" separator=",">
            #{orderId}
          </foreach>
        </if>
        <if test="ifMortar != null">
          AND if_mortar = #{ifMortar}
        </if>
      GROUP BY
        order_id
      ORDER BY
        create_time ASC;
  </select>


</mapper>