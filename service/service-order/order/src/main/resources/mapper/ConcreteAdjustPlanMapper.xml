<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.order.dao.mapper.ConcreteAdjustPlanMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.order.dao.vo.ConcreteAdjustPlan">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="adjust_plan_id" jdbcType="VARCHAR" property="adjustPlanId" />
    <result column="adjust_plan_code" jdbcType="VARCHAR" property="adjustPlanCode" />
    <result column="adjust_plan_name" jdbcType="VARCHAR" property="adjustPlanName" />
    <result column="seller_id" jdbcType="VARCHAR" property="sellerId" />
    <result column="contract_id" jdbcType="VARCHAR" property="contractId" />
    <result column="contract_name" jdbcType="VARCHAR" property="contractName" />
    <result column="contract_sequence" jdbcType="VARCHAR" property="contractSequence" />
    <result column="adjust_period" jdbcType="VARCHAR" property="adjustPeriod" />
    <result column="batch_adjust_price" jdbcType="DECIMAL" property="batchAdjustPrice" />
    <result column="if_include_mortar" jdbcType="BIT" property="ifIncludeMortar" />
    <result column="note" jdbcType="VARCHAR" property="note" />
    <result column="adjust_status" jdbcType="VARCHAR" property="adjustStatus" />
    <result column="del_flg" jdbcType="INTEGER" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <select id="queryConcreteAdjustPlanIdList"
          parameterType="com.ecommerce.order.api.dto.concrete.ConcreteAdjustPlanQueryDTO" resultType="java.lang.String">
    select
      distinct t1.adjust_plan_id
    from
      tr_concrete_adjust_plan as t1
    <if test="goodsName != null and goodsName != ''">
      left join
        tr_concrete_adjust_goods as t2 on t1.adjust_plan_id=t2.adjust_plan_id
    </if>
    where
      t1.del_flg = 0
      <if test="sellerId != null and sellerId != ''">
        and t1.seller_id = #{sellerId}
      </if>
      <if test="contractName != null and contractName != ''">
        and t1.contract_name like concat('%', #{contractName}, '%')
      </if>
      <if test="adjustPlanName != null and adjustPlanName != ''">
        and t1.adjust_plan_name like concat('%', #{adjustPlanName}, '%')
      </if>
      <if test="adjustPeriod != null and adjustPeriod != ''">
        and t1.adjust_period = #{adjustPeriod}
      </if>
      <if test="adjustStatus != null and adjustStatus != ''">
        and t1.adjust_status = #{adjustStatus}
      </if>
      <if test="goodsName != null and goodsName != ''">
        and t2.goods_name like concat('%', #{goodsName}, '%')
      </if>
  </select>

</mapper>