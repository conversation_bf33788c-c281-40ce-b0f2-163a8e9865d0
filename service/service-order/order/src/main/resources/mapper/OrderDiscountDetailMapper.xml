<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.order.dao.mapper.OrderDiscountDetailMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.order.dao.vo.OrderDiscountDetail">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="discountdetail_id" jdbcType="VARCHAR" property="discountdetailId" />
    <result column="object_id" jdbcType="VARCHAR" property="objectId" />
    <result column="discount_id" jdbcType="VARCHAR" property="discountId" />
    <result column="discount_name" jdbcType="VARCHAR" property="discountName" />
    <result column="discount_type" jdbcType="VARCHAR" property="discountType" />
    <result column="if_display" jdbcType="BIT" property="ifDisplay" />
    <result column="amount_price" jdbcType="DECIMAL" property="amountPrice" />
    <result column="unti_price" jdbcType="DECIMAL" property="untiPrice" />
    <result column="mome" jdbcType="VARCHAR" property="mome" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>
</mapper>