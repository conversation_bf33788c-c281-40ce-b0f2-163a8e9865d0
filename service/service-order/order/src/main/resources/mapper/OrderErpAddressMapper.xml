<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.order.dao.mapper.OrderErpAddressMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.order.dao.vo.OrderErpAddress">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="erp_address_id" jdbcType="VARCHAR" property="erpAddressId" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="receiving_address_id" jdbcType="VARCHAR" property="receivingAddressId" />
    <result column="erp_unload_address_id" jdbcType="VARCHAR" property="erpUnloadAddressId" />
    <result column="alias" jdbcType="VARCHAR" property="alias" />
    <result column="province_name" jdbcType="VARCHAR" property="provinceName" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="district_name" jdbcType="VARCHAR" property="districtName" />
    <result column="district_code" jdbcType="VARCHAR" property="districtCode" />
    <result column="street_code" jdbcType="VARCHAR" property="streetCode" />
    <result column="street_name" jdbcType="VARCHAR" property="streetName" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="address_detail" jdbcType="VARCHAR" property="addressDetail" />
    <result column="consignee_name" jdbcType="VARCHAR" property="consigneeName" />
    <result column="mobile_phone" jdbcType="VARCHAR" property="mobilePhone" />
    <result column="coordinate" jdbcType="VARCHAR" property="coordinate" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <select id="queryReceivingAddressMap" resultType="com.ecommerce.order.api.dto.ERPAddressDTO">
    select
      max(update_time),
      receiving_address_id as receivingAddressId,
      erp_unload_address_id as erpUnloadAddressId,
      province_name as provinceName,
      province_code as provinceCode,
      city_name as cityName,
      city_code as cityCode,
      district_name as districtName,
      district_code as districtCode,
      street_name as streetName,
      street_code as streetCode,
      consignee_name as consigneeName,
      mobile_phone as mobilePhone,
      address,
      coordinate
    FROM
      tr_order_erp_address
    WHERE
      erp_unload_address_id IN
      <foreach close=")" collection="list" item="item" open="(" separator=",">
        #{item}
      </foreach>
    GROUP BY
      erp_unload_address_id
  </select>

  <select id="queryFlowMonitorAddressByIds" resultType="com.ecommerce.order.api.dto.ERPAddressDTO">
    SELECT
      t1.receiving_address_id as receivingAddressId,
      t1.alias as alias,
      t1.erp_unload_address_id as erpUnloadAddressId,
      t1.province_name as provinceName,
      t1.province_code as provinceCode,
      t1.city_name as cityName,
      t1.city_code as cityCode,
      t1.district_name as districtName,
      t1.district_code as districtCode,
      t1.street_name as streetName,
      t1.address as address,
      t1.consignee_name as consigneeName,
      t1.mobile_phone as mobilePhone,
      t1.coordinate as coordinate,
      t1.type as type,
      t1.update_time as updateTime
    FROM
      (SELECT * FROM tr_order_erp_address WHERE
        receiving_address_id IN
        <foreach close=")" collection="list" item="item" open="(" separator=",">
          #{item}
        </foreach>
      AND erp_unload_address_id IS NOT NULL AND erp_unload_address_id != ''
      ORDER BY update_time DESC) AS t1
    GROUP BY
      t1.receiving_address_id,type
  </select>

  <select id="queryERPAddressListByOrderIds" resultType="com.ecommerce.order.api.dto.ERPAddressDTO">
    SELECT
      order_id AS orderId,
      receiving_address_id AS receivingAddressId,
      erp_unload_address_id AS erpUnloadAddressId,
      province_name AS provinceName,
      province_code AS provinceCode,
      city_name AS cityName,
      city_code AS cityCode,
      district_name AS districtName,
      district_code AS districtCode,
      street_name AS streetName,
      street_code AS streetCode,
      address AS address,
      consignee_name AS consigneeName,
      mobile_phone AS mobilePhone,
      coordinate,
      `type`
    FROM
      tr_order_erp_address
    WHERE
      del_flg = 0
      AND order_id IN
      <foreach collection="list" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
  </select>
</mapper>