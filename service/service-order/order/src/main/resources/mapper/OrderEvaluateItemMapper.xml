<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.order.dao.mapper.OrderEvaluateItemMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.order.dao.vo.OrderEvaluateItem">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="evaluate_id" jdbcType="VARCHAR" property="evaluateId" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="buyer_id" jdbcType="VARCHAR" property="buyerId" />
    <result column="buyer_name" jdbcType="VARCHAR" property="buyerName" />
    <result column="seller_id" jdbcType="VARCHAR" property="sellerId" />
    <result column="seller_name" jdbcType="VARCHAR" property="sellerName" />
    <result column="business_id" jdbcType="VARCHAR" property="businessId" />
    <result column="business_type" jdbcType="BIT" property="businessType" />
    <result column="score" jdbcType="DECIMAL" property="score" />
    <result column="score_add" jdbcType="DECIMAL" property="scoreAdd" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="impression" jdbcType="VARCHAR" property="impression" />
    <result column="img" jdbcType="VARCHAR" property="img" />
    <result column="origin" jdbcType="BIT" property="origin" />
    <result column="is_auto" jdbcType="BIT" property="isAuto" />
    <result column="is_anonymous" jdbcType="BIT" property="isAnonymous" />
    <result column="is_show" jdbcType="BIT" property="isShow" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>

  <select id="getAvgScore" resultType="DECIMAL">
    select AVG(score) AS averageScore from tr_order_evaluate_item
    where seller_id = #{sellerId,jdbcType=VARCHAR} and business_type = #{type,jdbcType=INTEGER}
  </select>

  <select id="getAvgScoreAdd" resultType="DECIMAL">
    select AVG(score_add) AS averageScore from tr_order_evaluate_item
    where seller_id = #{sellerId,jdbcType=VARCHAR} and business_type = #{type,jdbcType=INTEGER}
  </select>

</mapper>