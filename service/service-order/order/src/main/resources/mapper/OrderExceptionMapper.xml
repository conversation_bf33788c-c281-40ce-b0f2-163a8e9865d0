<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.order.dao.mapper.OrderExceptionMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.order.dao.vo.OrderException">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="obj_id" jdbcType="VARCHAR" property="objId" />
    <result column="param_class" jdbcType="VARCHAR" property="paramClass" />
    <result column="interface_name" jdbcType="VARCHAR" property="interfaceName" />
    <result column="operate_type" jdbcType="VARCHAR" property="operateType" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="retry_count" jdbcType="INTEGER" property="retryCount" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="ex_content" jdbcType="LONGVARCHAR" property="exContent" />
    <result column="param" jdbcType="LONGVARCHAR" property="param" />
  </resultMap>
  <resultMap id="pageResultMap" type="com.ecommerce.order.api.dto.OrderExceptionDTO">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="obj_id" jdbcType="VARCHAR" property="objId" />
    <result column="param_class" jdbcType="VARCHAR" property="paramClass" />
    <result column="interface_name" jdbcType="VARCHAR" property="interfaceName" />
    <result column="operate_type" jdbcType="VARCHAR" property="operateType" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="retry_count" jdbcType="INTEGER" property="retryCount" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="ex_content" jdbcType="LONGVARCHAR" property="exContent" />
    <result column="param" jdbcType="LONGVARCHAR" property="param" />
  </resultMap>

  <select id="pageOrderException" resultMap="pageResultMap">
    SELECT te.*,tor.order_code as objCode
    FROM `tr_exception` te
    inner JOIN
    (select * from `tr_order_info`
    where `seller_id`=#{exception.sellerId}
        <if test="exception.saleRegionIdList != null and exception.saleRegionIdList.size() > 0">
          and sale_region_path in (
              select bsra.region_son_id
                from ba_sale_region_All as bsra
               where bsra.region_id in
              <foreach collection="exception.saleRegionIdList" index="srin" item="srit" open="(" separator="," close=")">
                #{srit}
              </foreach>
                )
        </if>
        ) tor
    ON te.obj_id=tor.order_id
    WHERE 1=1
    <if test="exception.objCode!=null and exception.objCode!=''">
      and tor.order_code like concat('%', #{exception.objCode}, '%')
    </if>
    <if test="exception.status!=null and exception.status!=''">
      and te.status=#{exception.status}
    </if>
    <if test="exception.queryStartTime!=null">
      and te.create_time <![CDATA[>=]]> #{exception.queryStartTime}
    </if>
    <if test="exception.queryEndTime!=null">
      and te.create_time <![CDATA[<=]]> #{exception.queryEndTime}
    </if>
    and te.del_flag=0
    order by te.update_time DESC
    LIMIT ${(page.pageNum - 1) * page.pageSize},${page.pageSize};
  </select>

    <select id="countQuery" resultType="java.lang.Integer">
      SELECT count(te.id)
      FROM `tr_exception` te
      inner JOIN
      (select * from `tr_order_info` where `seller_id`=#{exception.sellerId}) tor
      ON te.obj_id=tor.order_id
      WHERE 1=1
      <if test="exception.objCode!=null and exception.objCode!=''">
          and tor.order_code like concat('%', #{exception.objCode}, '%')
      </if>
      <if test="exception.status!=null and exception.status!=''">
          and te.status=#{exception.status}
      </if>
      <if test="exception.queryStartTime!=null">
          and te.create_time <![CDATA[>=]]> #{exception.queryStartTime}
      </if>
      <if test="exception.queryEndTime!=null">
          and te.create_time <![CDATA[<=]]> #{exception.queryEndTime}
      </if>
      and te.del_flag=0
    </select>


</mapper>