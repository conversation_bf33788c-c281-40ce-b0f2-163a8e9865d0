<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.order.dao.mapper.OrderPayinfoAttachmentMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.order.dao.vo.OrderPayinfoAttachment">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="tr_order_payinfo_attachment_id" jdbcType="VARCHAR" property="trOrderPayinfoAttachmentId" />
    <result column="payinfo_id" jdbcType="VARCHAR" property="payinfoId" />
    <result column="payinfo_code" jdbcType="VARCHAR" property="payinfoCode" />
    <result column="object_id" jdbcType="VARCHAR" property="objectId" />
    <result column="object_code" jdbcType="VARCHAR" property="objectCode" />
    <result column="trade_bill_id" jdbcType="VARCHAR" property="tradeBillId" />
    <result column="trade_bill_no" jdbcType="VARCHAR" property="tradeBillNo" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>