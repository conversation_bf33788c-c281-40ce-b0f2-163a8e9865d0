<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.order.dao.mapper.OrderPayinfoDetailMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.order.dao.vo.OrderPayinfoDetail">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="detail_id" jdbcType="VARCHAR" property="detailId" />
    <result column="payinfo_id" jdbcType="VARCHAR" property="payinfoId" />
    <result column="pay_admount" jdbcType="DECIMAL" property="payAdmount" />
    <result column="actual_admount" jdbcType="DECIMAL" property="actualAdmount" />
    <result column="take_amount" jdbcType="DECIMAL" property="takeAmount" />
    <result column="order_over_amount" jdbcType="DECIMAL" property="orderOverAmount" />
    <result column="payer_id" jdbcType="VARCHAR" property="payerId" />
    <result column="payer_name" jdbcType="VARCHAR" property="payerName" />
    <result column="payee_id" jdbcType="VARCHAR" property="payeeId" />
    <result column="payee_name" jdbcType="VARCHAR" property="payeeName" />
    <result column="paytype_detail" jdbcType="VARCHAR" property="paytypeDetail" />
    <result column="currency" jdbcType="VARCHAR" property="currency" />
    <result column="currency_symbol" jdbcType="VARCHAR" property="currencySymbol" />
    <result column="currency_name" jdbcType="VARCHAR" property="currencyName" />
  </resultMap>
</mapper>