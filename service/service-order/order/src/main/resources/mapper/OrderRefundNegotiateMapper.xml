<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.order.dao.mapper.OrderRefundNegotiateMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.order.dao.vo.OrderRefundNegotiate">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="refund_id" jdbcType="VARCHAR" property="refundId" />
    <result column="negotiate_id" jdbcType="VARCHAR" property="negotiateId" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="negotiate_content" jdbcType="VARCHAR" property="negotiateContent" />
    <result column="negotiate_time" jdbcType="TIMESTAMP" property="negotiateTime" />
    <result column="negotiate_user_type" jdbcType="VARCHAR" property="negotiateUserType" />
    <result column="negotiate_user_id" jdbcType="VARCHAR" property="negotiateUserId" />
    <result column="negotiate_user_name" jdbcType="VARCHAR" property="negotiateUserName" />
    <result column="negotiate_user_contact_way" jdbcType="VARCHAR" property="negotiateUserContactWay" />
    <result column="negotiate_status" jdbcType="VARCHAR" property="negotiateStatus" />
    <result column="negotiate_pic" jdbcType="VARCHAR" property="negotiatePic" />
    <result column="negotiate_reason" jdbcType="VARCHAR" property="negotiateReason" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>
</mapper>