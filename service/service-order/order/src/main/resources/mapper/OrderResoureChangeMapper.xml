<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.order.dao.mapper.OrderResoureChangeMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.order.dao.vo.OrderResoureChange">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="history_id" jdbcType="VARCHAR" property="historyId" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="resource_id" jdbcType="VARCHAR" property="resourceId" />
    <result column="resource_version" jdbcType="INTEGER" property="resourceVersion" />
    <result column="change_time" jdbcType="TIMESTAMP" property="changeTime" />
    <result column="price_way" jdbcType="VARCHAR" property="priceWay" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="price_unit" jdbcType="VARCHAR" property="priceUnit" />
    <result column="price_unit_name" jdbcType="VARCHAR" property="priceUnitName" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>
</mapper>