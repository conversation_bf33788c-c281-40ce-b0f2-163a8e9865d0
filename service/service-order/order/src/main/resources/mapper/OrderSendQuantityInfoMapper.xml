<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.order.dao.mapper.OrderSendQuantityInfoMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.order.dao.vo.OrderSendQuantityInfo">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="allocation_date" jdbcType="CHAR" property="allocationDate" />
    <result column="seller_id" jdbcType="VARCHAR" property="sellerId" />
    <result column="buyer_id" jdbcType="VARCHAR" property="buyerId" />
    <result column="goods_id" jdbcType="VARCHAR" property="goodsId" />
    <result column="sale_region_id" jdbcType="VARCHAR" property="saleRegionId" />
    <result column="warehouse_id" jdbcType="VARCHAR" property="warehouseId" />
    <result column="total_actual_quantity" jdbcType="DECIMAL" property="totalActualQuantity" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>
</mapper>