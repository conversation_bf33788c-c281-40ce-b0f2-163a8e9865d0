<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.order.dao.mapper.OrderSendQuantityLogMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.order.dao.vo.OrderSendQuantityLog">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="order_item_id" jdbcType="VARCHAR" property="orderItemId" />
    <result column="take_code" jdbcType="VARCHAR" property="takeCode" />
    <result column="resource_id" jdbcType="VARCHAR" property="resourceId" />
    <result column="waybill_num" jdbcType="VARCHAR" property="waybillNum" />
    <result column="quantity" jdbcType="DECIMAL" property="quantity" />
    <result column="signed_quantity" jdbcType="DECIMAL" property="signedQuantity" />
    <result column="goods_amount" jdbcType="DECIMAL" property="goodsAmount" />
    <result column="logistic_amount" jdbcType="DECIMAL" property="logisticAmount" />
    <result column="other_amount" jdbcType="DECIMAL" property="otherAmount" />
    <result column="goods_price" jdbcType="DECIMAL" property="goodsPrice" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>
</mapper>