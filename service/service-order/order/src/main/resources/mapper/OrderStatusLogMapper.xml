<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.order.dao.mapper.OrderStatusLogMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.order.dao.vo.OrderStatusLog">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="order_status_log_id" jdbcType="VARCHAR" property="orderStatusLogId" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="status_key" jdbcType="VARCHAR" property="statusKey" />
    <result column="start_status" jdbcType="VARCHAR" property="startStatus" />
    <result column="end_status" jdbcType="VARCHAR" property="endStatus" />
    <result column="message" jdbcType="VARCHAR" property="message" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>