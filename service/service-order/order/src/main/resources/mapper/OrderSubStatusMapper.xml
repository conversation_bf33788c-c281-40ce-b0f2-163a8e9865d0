<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.order.dao.mapper.OrderSubStatusMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.order.dao.vo.OrderSubStatus">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="order_sub_status_id" jdbcType="VARCHAR" property="orderSubStatusId" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="status_key" jdbcType="VARCHAR" property="statusKey" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>