<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<artifactId>service</artifactId>
		<groupId>com.ecommerce</groupId>
		<version>2.1.4-RELEASE</version>
	</parent>
	<artifactId>service-order</artifactId>
	<packaging>pom</packaging>
	<name>service-order</name>

	<description>Order Microservice</description>

	<modules>
		<module>order</module>
		<module>order-api</module>
	</modules>
<!--	<distributionManagement>
		<repository>
			<id>maven-releases</id>
			<name>Department Repository</name>
			<url>http://*************:8081/repository/maven-releases/</url>
		</repository>
	</distributionManagement>

	<repositories>
		<repository>
			<id>maven-public</id>
			<name>Department Repository</name>
			<url>https://*************:8081/repository/maven-public/</url>
			<snapshots>
				<enabled>true</enabled>
				<updatePolicy>always</updatePolicy>
				<checksumPolicy>warn</checksumPolicy>
			</snapshots>
			<releases>
				<enabled>true</enabled>
				<updatePolicy>always</updatePolicy>
			</releases>
		</repository>
	</repositories>-->

	<profiles>
		<profile>
			<id>sonar</id>
			<properties>
				<sonar.host.url>http://***********:9000</sonar.host.url>
			</properties>
		</profile>
		<profile>
			<id>dev</id>
			<activation>
				<activeByDefault>true</activeByDefault>
			</activation>
			<properties>
				<distribution.url>http://*************:8081/repository/maven-releases/</distribution.url>
				<repository.url>https://*************:8081/repository/maven-public/</repository.url>
			</properties>
		</profile>
		<profile>
			<id>test</id>
			<properties>
				<distribution.url>http://***********:8081/repository/xxx-snapshot-release/</distribution.url>
				<repository.url>http://***********:8081/repository/xxx-group/</repository.url>
			</properties>
		</profile>
		<profile>
			<id>sit</id>
			<properties>
				<distribution.url>http://***********:8081/repository/xxx-snapshot-release/</distribution.url>
				<repository.url>http://***********:8081/repository/xxx-group/</repository.url>
			</properties>
		</profile>
		<profile>
			<id>newuat</id>
			<properties>
				<distribution.url>http://***********:8081/repository/xxx-snapshot-release/</distribution.url>
				<repository.url>http://***********:8081/repository/xxx-group/</repository.url>
			</properties>
		</profile>
		<profile>
			<id>prod</id>
			<properties>
				<distribution.url>http://***********:8081/repository/xxx-snapshot-release/</distribution.url>
				<repository.url>http://***********:8081/repository/xxx-group/</repository.url>
			</properties>
		</profile>
	</profiles>
</project>
