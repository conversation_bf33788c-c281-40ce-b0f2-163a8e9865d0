package com.ecommerce.pay;

import com.ecommerce.common.annotation.ExcludeFromComponetScan;
import com.ecommerce.rabbitmq.annotation.EnableRabbitMq;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import tk.mybatis.spring.annotation.MapperScan;

import javax.sql.DataSource;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@SpringBootApplication
@EnableFeignClients({
		"com.ecommerce.open.api",
		"com.ecommerce.member.api",
		"com.ecommerce.base.api",
		"com.ecommerce.logistics.api",
		"com.ecommerce.goods.api"
})
@EnableDiscoveryClient
@ComponentScan(value = {
		"com.ecommerce.pay",
		"com.ecommerce.common.config",
		"com.ecommerce.common.service.common",
		"com.ecommerce.common.service.reference",
		"com.ecommerce.common.service.lock",
		"com.ecommerce.common.filter"
}, excludeFilters = {
		@ComponentScan.Filter(type = FilterType.ANNOTATION, value = ExcludeFromComponetScan.class)
})
@MapperScan({"com.ecommerce.pay.dao.mapper", "com.ecommerce.pay.v2.dao.mapper"})
@EnableTransactionManagement
@EnableRabbitMq
public class PayApplication {
	@Bean
	public PlatformTransactionManager txManager(DataSource dataSource) {
		return new DataSourceTransactionManager(dataSource);
	}

	@Bean(destroyMethod = "shutdown")
	public ThreadPoolExecutor threadPoolExecutor() {
		return new ThreadPoolExecutor(
				16, 32,
				60, TimeUnit.SECONDS,
				new ArrayBlockingQueue<>(1000),
				Executors.defaultThreadFactory(),
				new ThreadPoolExecutor.DiscardPolicy()
		);
	}

	public static void main(String[] args) {
		SpringApplication.run(PayApplication.class, args);
	}
}
