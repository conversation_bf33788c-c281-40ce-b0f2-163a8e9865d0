package com.ecommerce.pay.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @Created: 11:34 10/12/2018
 * <AUTHOR>
 * @Description: TODO
 */
@Setter
@Getter
@Configuration
@ConfigurationProperties(prefix = "alipay")
public class AlipayProperties {

    private String getwayUrl;

    private String notifyUrl;

    private String signType;

    private String charset;

    private String format;

    private String productCodePc;

    private String productCodeWap;

    private String productCodeApp;
}
