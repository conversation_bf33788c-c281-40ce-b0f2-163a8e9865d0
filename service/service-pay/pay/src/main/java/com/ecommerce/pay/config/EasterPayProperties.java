package com.ecommerce.pay.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Setter
@Getter
@Configuration
@ConfigurationProperties(prefix = "easterpay")
public class EasterPayProperties {
    /**
     * 东方付通的商城ID
     */
    private String mallId;
    /**
     * 证书路径
     */
    private String certPath;

    /**
     * 证书密码
     */
    private String certPassword;

    /**
     * 交易成功状态码"000000"（适用于大部分交易，部分特别约定交易除外）
     */
    private String successCode;

    /**
     * 支付回调地址
     */
    private String callBackUrl;

    /**
     * 支付地址
     */
    private String url;

}
