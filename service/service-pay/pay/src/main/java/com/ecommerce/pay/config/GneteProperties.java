package com.ecommerce.pay.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 银联支付配置文件
 */
@Setter
@Getter
@Configuration
@ConfigurationProperties(prefix = "gnete")
public class GneteProperties {


    /**
     * 余额查询redis缓存保存时间（秒）
     */
    private Integer queryCacheTime=60;
}
