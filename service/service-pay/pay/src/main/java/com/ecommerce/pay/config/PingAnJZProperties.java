package com.ecommerce.pay.config;

import cn.hutool.core.util.ObjectUtil;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @Created: 10:16 10/01/2019
 * <AUTHOR>
 * @Description: TODO
 */
@Setter
@Getter
@Configuration
@ConfigurationProperties(prefix = "pinganjz")
public class PingAnJZProperties {

    /**
     * 见证宝 - 用户短号 (流水号前缀)
     */
    private String userCode;
    /**
     * 见证宝 - 商户号
     */
    private String mrchCode;
    /**
     * 见证宝 - 资金汇总账号
     */
    private String fundSummaryAcctNo;
    /**
     * 提现手续费
     */
    private String takeCashCommission;
    /**
     * 支付手续费
     */
    private String tranPayCommission;
    /**
     * 退款手续费
     */
    private String refundCommission;

    private String weixin;

    private String alipay;

    private String shopPass;

    /**
     * 电子回单URL
     */
    private String electronicReceiptUrl;

    private Boolean useSMSCode;

    public Boolean getUseSMSCode() {
        //默认使用短信验证码交易
        return ObjectUtil.defaultIfNull(useSMSCode,Boolean.TRUE);
    }

}
