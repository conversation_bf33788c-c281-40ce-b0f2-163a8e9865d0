package com.ecommerce.pay.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Setter
@Getter
@Configuration
@ConfigurationProperties(prefix = "qrcode")
public class QrCodeProperties {
    //内容
    private String contents;
    //图片格式，可选[png,jpg,bmp]
    private String format;
    //宽度
    private String width;
    //高度
    private String height;
    //边框间距
    private String margin;
    //纠错等级
    //    L(1),
    //    M(0),
    //    Q(3),
    //    H(2);
    private String errorLevel;
}
