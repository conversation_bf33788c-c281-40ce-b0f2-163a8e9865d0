package com.ecommerce.pay.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Setter
@Getter
@Configuration
@ConfigurationProperties(prefix = "wx")
public class WxProperties {

    private String appId;

    private String appSecret;

    private String mchId;

    private String mchKey;

    private String openUrl;

    private String notifyUrl;

    private String prePaymentUrl;

    private String refundUrl;

    private String refundNotifyUrl;

    private String buyerAppId;

    private String sellerAppId;

    private String driverAppId;
    /**
     * 支付有效期(秒) 默认90秒
     */
    private Integer effectiveTimeSeconds = 90;
}
