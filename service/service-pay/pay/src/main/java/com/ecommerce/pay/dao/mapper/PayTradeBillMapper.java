package com.ecommerce.pay.dao.mapper;

import com.ecommerce.common.service.IBaseMapper;
import com.ecommerce.pay.dao.vo.PayTradeBill;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

public interface PayTradeBillMapper extends IBaseMapper<PayTradeBill> {
    Integer updateByStatus(@Param("status") Integer status, @Param("version") Integer version, @Param("findStatus") Integer findStatus, @Param("payTradeBillNo") String payTradeBillNo, @Param("operatorId") String operatorId, @Param("updateDate") Date updateDate);
}