package com.ecommerce.pay.dao.vo;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import jakarta.persistence.*;
import lombok.Data;

@Data
@Table(name = "pay_account_freeze_bill")
public class AccountFreezeBill implements Serializable {
    /**
     * 账户冻结流水ID
     */
    @Id
    @Column(name = "account_freeze_bill_id")
    private String accountFreezeBillId;

    /**
     * 账户冻结流水编号
     */
    @Column(name = "account_freeze_bill_no")
    private String accountFreezeBillNo;

    /**
     * 账户ID
     */
    @Column(name = "account_id")
    private String accountId;

    /**
     * 账户编号,账户号，或称为账户ID，一般是系统自动生成。特别注意的是，要事先约定好账户ID的规则。比如头三位用来表示账户类型，后几位用来表示账户编号等。务必保证根据账号号能够快速确定账户类型，并且保证账户号是不重复的。
     */
    @Column(name = "account_no")
    private String accountNo;

    /**
     * 会员ID
     */
    @Column(name = "member_id")
    private String memberId;

    /**
     * 会员名称
     */
    @Column(name = "member_name")
    private String memberName;

    /**
     * 操作类型,1：冻结；2：注销；3：解冻
     */
    @Column(name = "operation_type")
    private Integer operationType;

    /**
     * 删除标记
     */
    @Column(name = "del_flg")
    private Boolean delFlg;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    @Serial
    private static final long serialVersionUID = 1L;
}