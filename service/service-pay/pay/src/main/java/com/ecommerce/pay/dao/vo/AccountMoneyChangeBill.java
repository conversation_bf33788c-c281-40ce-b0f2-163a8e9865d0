package com.ecommerce.pay.dao.vo;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import jakarta.persistence.*;
import lombok.Data;

@Data
@Table(name = "pay_account_money_change_bill")
public class AccountMoneyChangeBill implements Serializable {
    /**
     * 账户资金变动流水id
     */
    @Id
    @Column(name = "account_money_change_bill_id")
    private String accountMoneyChangeBillId;

    /**
     * 账户资金变动流水编号
     */
    @Column(name = "account_money_change_bill_no")
    private String accountMoneyChangeBillNo;

    /**
     * 账户ID
     */
    @Column(name = "account_id")
    private String accountId;

    /**
     * 账户编号,账户号，或称为账户ID，一般是系统自动生成。特别注意的是，要事先约定好账户ID的规则。比如头三位用来表示账户类型，后几位用来表示账户编号等。务必保证根据账号号能够快速确定账户类型，并且保证账户号是不重复的。
     */
    @Column(name = "account_no")
    private String accountNo;

    /**
     * 账户类型
     */
    @Column(name = "account_type")
    private Integer accountType;

    /**
     * 会员ID
     */
    @Column(name = "member_id")
    private String memberId;

    /**
     * 会员名称
     */
    @Column(name = "member_name")
    private String memberName;

    /**
     * 账务变动方向,1:来账；2：往账
     */
    @Column(name = "account_flow")
    private Integer accountFlow;

    /**
     * 变动类型,1:充值；2：支付；3：提现；4：内部调账；5：结息；6：利息税；7：原交易退款；8：原交易撤销；
     */
    @Column(name = "change_type")
    private Integer changeType;

    /**
     * 变动前总金额
     */
    @Column(name = "pre_amount")
    private BigDecimal preAmount;

    /**
     * 变动后总金额
     */
    private BigDecimal amount;

    /**
     * 删除标记
     */
    @Column(name = "del_flg")
    private Boolean delFlg;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 渠道类型
     */
    @Column(name = "channel_type")
    private Integer channelType;

    @Serial
    private static final long serialVersionUID = 1L;
}