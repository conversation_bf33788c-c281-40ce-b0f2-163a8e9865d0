package com.ecommerce.pay.dao.vo;

import jakarta.persistence.Column;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Table(name = "pay_channel")
public class Channel implements Serializable {
    /**
     * 渠道ID
     */
    @Id
    @Column(name = "channel_id")
    private String channelId;

    /**
     * 渠道编号
     */
    @Column(name = "channel_no")
    private String channelNo;

    /**
     * 支付方式ID
     */
    @Column(name = "payment_method_id")
    private String paymentMethodId;

    /**
     * 支付方式名称,冗余
     */
    @Column(name = "payment_method_name")
    private String paymentMethodName;

    /**
     * 渠道名称,微信小程序、微信扫描支付、微信h5支付
     */
    @Column(name = "channel_name")
    private String channelName;

    /**
     * 渠道类型,商业银行；第三方支付平台；
     */
    @Column(name = "channel_type")
    private Integer channelType;

    /**
     * 渠道为电商平台分配的APPID,比如：appid是微信小程序后台APP的唯一标识，在小程序后台申请小程序账号后，微信会自动分配对应的appid，用于标识该应用。
     */
    @Column(name = "channel_appid")
    private String channelAppid;

    /**
     * 渠道为电商平台分配的接口密码,比如：AppSecret是APPID对应的接口密码，用于获取接口调用凭证时使用。
     */
    @Column(name = "channel_appsecret")
    private String channelAppsecret;

    /**
     * 渠道为电商平台分配的api秘钥,比如：交易过程生成签名的密钥，仅保留在商户系统和微信支付后台，不会在网络中传播。
     */
    @Column(name = "channel_api_secret_key")
    private String channelApiSecretKey;

    /**
     * 渠道为电商平台分配的商户号,比如：商户申请微信支付后，由微信支付分配的商户收款账号
     */
    @Column(name = "channel_merchant_no")
    private String channelMerchantNo;

    /**
     * 渠道为电商平台分配的证书路径,一般放在web无法访问的服务器路径
     */
    @Column(name = "channel_merchant_certificate_path")
    private String channelMerchantCertificatePath;

    /**
     * 币种
     */
    @Column(name = "currency_type")
    private Integer currencyType;

    /**
     * 费率
     */
    @Column(name = "channel_rate")
    private BigDecimal channelRate;

    /**
     * 是否启用
     */
    @Column(name = "is_enabled")
    private Boolean isEnabled;

    /**
     * 删除标记
     */
    @Column(name = "del_flg")
    private Boolean delFlg;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 商户key
     */
    @Column(name = "channel_merchant_key")
    private String channelMerchantKey;

    @Serial
    private static final long serialVersionUID = 1L;
}