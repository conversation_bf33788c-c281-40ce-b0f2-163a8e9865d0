package com.ecommerce.pay.dao.vo;

import jakarta.persistence.Column;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

@Data
@Table(name = "pay_channel_details")
public class ChannelDetails implements Serializable {
    /**
     * 渠道详情id
     */
    @Id
    @Column(name = "channel_details_id")
    private String channelDetailsId;

    /**
     * 渠道ID
     */
    @Column(name = "channel_id")
    private String channelId;

    /**
     * 选项名称,比如：公众账号ID
     */
    @Column(name = "option_name")
    private String optionName;

    /**
     * 选项变量名,比如：appid
     */
    @Column(name = "option_variable")
    private String optionVariable;

    /**
     * 选项取值,比如：wxd678efh567hg6787
     */
    @Column(name = "option_value")
    private String optionValue;

    /**
     * 选项类型,比如：String(32)
     */
    @Column(name = "option_type")
    private String optionType;

    /**
     * 选项描述,比如：微信支付分配的公众账号ID（企业号corpid即为此appId）
     */
    @Column(name = "option_description")
    private String optionDescription;

    /**
     * 删除标记
     */
    @Column(name = "del_flg")
    private Boolean delFlg;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    @Serial
    private static final long serialVersionUID = 1L;
}