package com.ecommerce.pay.dao.vo;

import jakarta.persistence.Column;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

@Data
@Table(name = "pay_member_channel_account")
public class MemberChannelAccount implements Serializable {
    /**
     * 会员渠道开户账号绑定ID
     */
    @Id
    @Column(name = "member_channel_account_id")
    private String memberChannelAccountId;

    /**
     * 会员渠道开户账号绑定编号
     */
    @Column(name = "member_channel_account_no")
    private String memberChannelAccountNo;

    /**
     * 会员ID
     */
    @Column(name = "member_id")
    private String memberId;

    /**
     * 会员名称
     */
    @Column(name = "member_name")
    private String memberName;

    /**
     * 会员类型,1:个人，2:企业
     */
    @Column(name = "member_type")
    private Integer memberType;

    /**
     * 渠道ID
     */
    @Column(name = "channel_id")
    private String channelId;

    /**
     * 渠道编号,比如：建行的渠道编号：********
     */
    @Column(name = "channel_no")
    private String channelNo;

    /**
     * 渠道名称,比如：建行
     */
    @Column(name = "channel_name")
    private String channelName;

    /**
     * 渠道类型,比如：商业银行，第三方支付公司
     */
    @Column(name = "channel_type")
    private Integer channelType;

    /**
     * 渠道开户账号编号,比如：会员张晓东在建行的开户账号***********
     */
    @Column(name = "channel_account_no")
    private String channelAccountNo;

    /**
     * 渠道开户账号姓名,比如：张晓东
     */
    @Column(name = "channel_account_name")
    private String channelAccountName;

    /**
     * 渠道开户账号身份证号,比如：张晓东身份证：5000800020202200010100
     */
    @Column(name = "channel_account_icno")
    private String channelAccountIcno;

    /**
     * 渠道开户账号手机号
     */
    @Column(name = "channel_account_mobile_phone")
    private String channelAccountMobilePhone;

    /**
     * 删除标记
     */
    @Column(name = "del_flg")
    private Boolean delFlg;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 是否禁用
     */
    @Column(name = "allow_disabled")
    private Boolean allowDisabled;

    /**
     * 开户行
     */
    @Column(name = "opening_bank")
    private String openingBank;

    /**
     * 商户key
     */
    @Column(name = "mch_key")
    private String mchKey;

    /**
     * 商户号
     */
    @Column(name = "mch_no")
    private String mchNo;

    @Serial
    private static final long serialVersionUID = 1L;
}