package com.ecommerce.pay.dao.vo;

import jakarta.persistence.Column;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Table(name = "pay_account")
public class PayAccount implements Serializable {
    @Id
    @Column(name = "account_id")
    private String accountId;

    /**
     * 账户类型,
1:个人账户;2：企业账户；3：内部账户:；4：其他账户
            
            虚拟币账号：用户和使用虚拟币的商户都需要建立虚拟币账户。
            
            代扣账号： 用来支持订阅类型的定期代扣；
            
            零钱账号：即电商的内部账号，用户、商户、清算单位需要建立零钱账户
            
            第三方支付账号：用户在第三方支付机构建立的账户。
            
            银行卡账号：用户的银行卡信息，每个卡对应一个账户。
            
            结算账号：用来支持和第三方支付公司、银行进行结算用。 第三方支付需要为每个商户号建立结算账号；银行需要为借记卡、贷记卡分别建立结算账号。
            
            代扣代缴账户：用来支持代扣税款业务。
     */
    @Column(name = "account_type")
    private Integer accountType;

    /**
     * 账户状态,
1:冻结；2激活
     */
    @Column(name = "account_status")
    private Integer accountStatus;

    /**
     * 账户号,或称为账户ID，一般是系统自动生成。特别注意的是，要事先约定好账户ID的规则。比如头三位用来表示账户类型，后几位用来表示账户编号等。务必保证根据账号号能够快速确定账户类型，并且保证账户号是不重复的。
     */
    @Column(name = "account_no")
    private String accountNo;

    /**
     * 账户名称,一般是由用户自己设置的，显示用
     */
    @Column(name = "account_name")
    private String accountName;

    /**
     * 货币类型,
1：人民币
            
            
            账户使用的货币类型，注意虽然一张银行卡可以支持多个币种，实际在内部，还是针对每个币种建立独立的子账户。
     */
    @Column(name = "currency_type")
    private Integer currencyType;

    /**
     * 账户控制-是否允许充值
     */
    @Column(name = "allow_recharge")
    private Boolean allowRecharge;

    /**
     * 账户控制-是否允许提现
     */
    @Column(name = "allow_draw_money")
    private Boolean allowDrawMoney;

    /**
     * 账户控制-是否允许透支
     */
    @Column(name = "allow_overdraft")
    private Boolean allowOverdraft;

    /**
     * 账户控制-是否允许支付
     */
    @Column(name = "allow_payment")
    private Boolean allowPayment;

    /**
     * 账户控制-是否允许转账进入
     */
    @Column(name = "allow_transfer_account_into")
    private Boolean allowTransferAccountInto;

    /**
     * 账户控制-是否允许转账转出
     */
    @Column(name = "allow_transfer_account_out")
    private Boolean allowTransferAccountOut;

    /**
     * 账户控制-是否激活
     */
    @Column(name = "allow_active")
    private Boolean allowActive;

    /**
     * 账户控制-是否冻结
     */
    @Column(name = "allow_freeze")
    private Boolean allowFreeze;

    /**
     * 资金相关-当前账户余额,等于可用余额+冻结余额；
     */
    private BigDecimal balance;

    /**
     * 资金相关-当前账户可用余额
     */
    @Column(name = "available_balance")
    private BigDecimal availableBalance;

    /**
     * 资金相关-当前账户冻结的余额,冻结余额指在账户上暂不能使用的额度。在支付的时候，往往是先冻结，商品出库后， 再实际执行扣款。
     */
    @Column(name = "freeze_balance")
    private BigDecimal freezeBalance;

    /**
     * 会员相关-会员ID
     */
    @Column(name = "member_id")
    private String memberId;

    /**
     * 会员相关-会员名称
     */
    @Column(name = "member_name")
    private String memberName;

    /**
     * 会员相关-会员编号
     */
    @Column(name = "member_no")
    private String memberNo;

    /**
     * 会员相关-会员手机
     */
    @Column(name = "member_mobile_phone")
    private String memberMobilePhone;

    /**
     * 会员相关-会员邮箱
     */
    @Column(name = "member_email")
    private String memberEmail;

    /**
     * 校验字段,为了避免账户信息被意外修改，还可以增加一个校验字段，在写入数据时设置该字段，在读取数据时做校验，一旦发现数据有问题，则关闭该账号。
     */
    @Column(name = "verify_field")
    private String verifyField;

    /**
     * 删除标记
     */
    @Column(name = "del_flg")
    private Boolean delFlg;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 账户支付密码
     */
    @Column(name = "account_pay_password")
    private String accountPayPassword;

    @Serial
    private static final long serialVersionUID = 1L;
}