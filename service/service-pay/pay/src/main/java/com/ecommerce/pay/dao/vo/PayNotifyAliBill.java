package com.ecommerce.pay.dao.vo;

import jakarta.persistence.Column;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

@Data
@Table(name = "pay_notify_ali_bill")
public class PayNotifyAliBill implements Serializable {
    /**
     * 支付宝异步通知流水ID
     */
    @Id
    @Column(name = "notify_ali_bill_id")
    private String notifyAliBillId;

    /**
     * 交易流水ID
     */
    @Column(name = "trade_bill_id")
    private String tradeBillId;

    /**
     * 交易流水编号
     */
    @Column(name = "trade_bill_no")
    private String tradeBillNo;

    /**
     * 渠道ID
     */
    @Column(name = "channel_id")
    private String channelId;

    /**
     * 渠道编号
     */
    @Column(name = "channel_no")
    private String channelNo;

    /**
     * 删除标记
     */
    @Column(name = "del_flg")
    private Boolean delFlg;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    @Serial
    private static final long serialVersionUID = 1L;
}