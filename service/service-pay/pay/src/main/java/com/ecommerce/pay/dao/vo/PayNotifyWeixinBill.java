package com.ecommerce.pay.dao.vo;

import jakarta.persistence.Column;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

@Data
@Table(name = "pay_notify_weixin_bill")
public class PayNotifyWeixinBill implements Serializable {
    /**
     * 微信异步通知流水ID
     */
    @Id
    @Column(name = "notify_weixin_bill_id")
    private String notifyWeixinBillId;

    /**
     * 通知类型,小程序、公众号、APP、扫码
     */
    @Column(name = "notify_type")
    private Integer notifyType;

    /**
     * 通知方向,异步通知、主动查询
     */
    @Column(name = "notify_direction")
    private Integer notifyDirection;

    /**
     * 小程序ID
     */
    private String appid;

    /**
     * 商户号
     */
    @Column(name = "mch_id")
    private String mchId;

    /**
     * 设备号
     */
    @Column(name = "device_info")
    private String deviceInfo;

    /**
     * 业务结果
     */
    @Column(name = "result_code")
    private String resultCode;

    /**
     * 错误代码
     */
    @Column(name = "err_code")
    private String errCode;

    /**
     * 错误代码描述
     */
    @Column(name = "err_code_des")
    private String errCodeDes;

    /**
     * 用户标识
     */
    private String openid;

    /**
     * 是否关注公众账号
     */
    @Column(name = "is_subscribe")
    private String isSubscribe;

    /**
     * 交易类型
     */
    @Column(name = "trade_type")
    private String tradeType;

    /**
     * 付款银行
     */
    @Column(name = "bank_type")
    private String bankType;

    /**
     * 订单金额
     */
    @Column(name = "total_fee")
    private Integer totalFee;

    /**
     * 应结订单金额
     */
    @Column(name = "settlement_total_fee")
    private Integer settlementTotalFee;

    /**
     * 货币种类
     */
    @Column(name = "fee_type")
    private String feeType;

    /**
     * 现金支付金额
     */
    @Column(name = "cash_fee")
    private Integer cashFee;

    /**
     * 现金支付货币类型
     */
    @Column(name = "cash_fee_type")
    private String cashFeeType;

    /**
     * 总代金券金额
     */
    @Column(name = "coupon_fee")
    private Integer couponFee;

    /**
     * 代金券使用数量
     */
    @Column(name = "coupon_count")
    private Integer couponCount;

    /**
     * 代金券类型
     */
    @Column(name = "coupon_type_$n")
    private String couponTypeN;

    /**
     * 代金券ID
     */
    @Column(name = "coupon_id_$n")
    private String couponIdN;

    /**
     * 单个代金券支付金额
     */
    @Column(name = "coupon_fee_$n")
    private Integer couponFeeN;

    /**
     * 微信支付订单号
     */
    @Column(name = "transaction_id")
    private String transactionId;

    /**
     * 商户订单号
     */
    @Column(name = "out_trade_no")
    private String outTradeNo;

    /**
     * 商家数据包
     */
    private String attach;

    /**
     * 支付完成时间
     */
    @Column(name = "time_end")
    private String timeEnd;

    /**
     * 删除标记
     */
    @Column(name = "del_flg")
    private Boolean delFlg;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    @Serial
    private static final long serialVersionUID = 1L;
}