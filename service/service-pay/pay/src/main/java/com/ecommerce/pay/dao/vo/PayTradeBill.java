package com.ecommerce.pay.dao.vo;

import jakarta.persistence.Column;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Table(name = "pay_trade_bill")
public class PayTradeBill implements Serializable {
    /**
     * 交易流水ID
     */
    @Id
    @Column(name = "trade_bill_id")
    private String tradeBillId;

    /**
     * 交易流水号,根据业务情况设计流水号业务编码：比如前缀用+数字编码，做分库分表的依据
     */
    @Column(name = "trade_bill_no")
    private String tradeBillNo;

    /**
     * 会计科目名称
     */
    @Column(name = "account_subject_name")
    private String accountSubjectName;

    /**
     * 会计科目代码,一般是一级会计科目的代码。
     */
    @Column(name = "account_subject_no")
    private String accountSubjectNo;

    /**
     * 关联业务单据信息-业务单ID,关联的个业务系统业务单据（支付、退款、提现等）
     */
    @Column(name = "business_id")
    private String businessId;

    /**
     * 关联业务单据信息-业务单号
     */
    @Column(name = "business_no")
    private String businessNo;

    /**
     * 关联业务单据信息-名称
     */
    @Column(name = "business_name")
    private String businessName;

    /**
     * 关联业务单据信息-描述
     */
    @Column(name = "business_description")
    private String businessDescription;

    /**
     * 金额信息-结算币种类型
     */
    @Column(name = "currency_type")
    private Integer currencyType;

    /**
     * 金额信息-原始金额
     */
    @Column(name = "original_amount")
    private BigDecimal originalAmount;

    /**
     * 金额信息-实际金额
     */
    @Column(name = "actual_amount")
    private BigDecimal actualAmount;

    /**
     * 交易渠道信息-渠道ID
     */
    @Column(name = "channel_id")
    private String channelId;

    /**
     * 交易渠道信息-渠道编号
     */
    @Column(name = "channel_no")
    private String channelNo;

    /**
     * 交易渠道信息-渠道名称
     */
    @Column(name = "channel_name")
    private String channelName;

    /**
     * 交易渠道信息-渠道执行支付、退款的时间
     */
    @Column(name = "channel_business_tiime")
    private Date channelBusinessTiime;

    /**
     * 交易渠道信息-渠道返回的业务单号
     */
    @Column(name = "channel_business_no")
    private String channelBusinessNo;

    /**
     * 交易渠道信息-渠道返回错误信息
     */
    @Column(name = "channel_business_return_error_info")
    private String channelBusinessReturnErrorInfo;

    /**
     * 交易渠道信息-渠道返回错误编码
     */
    @Column(name = "channel_business_return_error_no")
    private String channelBusinessReturnErrorNo;

    /**
     * 交易渠道信息-渠道费率
     */
    @Column(name = "channel_rate")
    private BigDecimal channelRate;

    /**
     * 交易渠道信息-渠道费用,支付金额*手续费率
     */
    @Column(name = "channel_amount")
    private BigDecimal channelAmount;

    /**
     * 交易状态,1000: 未支付(订单保存未支付)；2000：待支付（已调用渠道接口）；3000支付中（已调用渠道）；4000支付成功；5支付失败
            
     */
    @Column(name = "trade_bill_state")
    private Integer tradeBillState;

    /**
     * 交易类型,1:支付；2：充值；3：退款；4：提现；5：内部调账；6：汇款；7：外部系统；8：撤销交易
     */
    @Column(name = "trade_type")
    private Integer tradeType;

    /**
     * 删除标记
     */
    @Column(name = "del_flg")
    private Boolean delFlg;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 预支付ID生成时间
     */
    @Column(name = "prepay_id_create_time")
    private Date prepayIdCreateTime;

    /**
     * 交易时间
     */
    @Column(name = "trade_time")
    private Date tradeTime;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 发送给微信的订单号
     */
    @Column(name = "out_trade_no")
    private String outTradeNo;

    @Serial
    private static final long serialVersionUID = 1L;
}