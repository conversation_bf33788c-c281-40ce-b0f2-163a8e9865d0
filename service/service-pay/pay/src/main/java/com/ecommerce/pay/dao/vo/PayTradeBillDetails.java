package com.ecommerce.pay.dao.vo;

import jakarta.persistence.Column;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Table(name = "pay_trade_bill_details")
public class PayTradeBillDetails implements Serializable {
    /**
     * 交易流水明细ID
     */
    @Id
    @Column(name = "trade_bill_details_id")
    private String tradeBillDetailsId;

    /**
     * 交易流水明细名称,说明交易明细项
     */
    @Column(name = "trade_bill_details_name")
    private String tradeBillDetailsName;

    /**
     * 交易流水明细编号
     */
    @Column(name = "trade_bill_details_no")
    private String tradeBillDetailsNo;

    /**
     * 交易流水ID
     */
    @Column(name = "trade_bill_id")
    private String tradeBillId;

    /**
     * 交易流水编号
     */
    @Column(name = "trade_bill_no")
    private String tradeBillNo;

    /**
     * 会计科目名称
     */
    @Column(name = "account_subject_name")
    private String accountSubjectName;

    /**
     * 会计科目代码
     */
    @Column(name = "account_subject_no")
    private String accountSubjectNo;

    /**
     * 金额信息-结算币种类型
     */
    @Column(name = "currency_type")
    private String currencyType;

    /**
     * 金额信息-原始金额
     */
    @Column(name = "original_amount")
    private BigDecimal originalAmount;

    /**
     * 金额信息-实际金额
     */
    @Column(name = "actual_amount")
    private BigDecimal actualAmount;

    /**
     * 交易主体信息-主体ID
     */
    @Column(name = "buyer_account_id")
    private String buyerAccountId;

    /**
     * 交易主体信息-名字
     */
    @Column(name = "buyer_account_name")
    private String buyerAccountName;

    /**
     * 交易主体信息-账户编号
     */
    @Column(name = "buyer_account_no")
    private String buyerAccountNo;

    /**
     * 交易主体信息-账户类型
     */
    @Column(name = "buyer_account_type")
    private String buyerAccountType;

    /**
     * 交易主体信息-手机号
     */
    @Column(name = "buyer_account_mobile_phone")
    private String buyerAccountMobilePhone;

    /**
     * 交易主体信息-通知邮箱
     */
    @Column(name = "buyer_account_email")
    private String buyerAccountEmail;

    /**
     * 交易对手信息-对手ID
     */
    @Column(name = "seller_account_id")
    private String sellerAccountId;

    /**
     * 交易对手信息-名字
     */
    @Column(name = "seller_account_name")
    private String sellerAccountName;

    /**
     * 交易对手信息-账户编号
     */
    @Column(name = "seller_account_no")
    private String sellerAccountNo;

    /**
     * 交易对手信息-账户类型
     */
    @Column(name = "seller_account_type")
    private String sellerAccountType;

    /**
     * 交易对手信息-手机号
     */
    @Column(name = "seller_account_mobile_phone")
    private String sellerAccountMobilePhone;

    /**
     * 交易对手信息-通知邮箱
     */
    @Column(name = "seller_account_email")
    private String sellerAccountEmail;

    /**
     * 删除标记
     */
    @Column(name = "del_flg")
    private Boolean delFlg;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    @Serial
    private static final long serialVersionUID = 1L;
}