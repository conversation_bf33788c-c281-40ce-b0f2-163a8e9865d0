package com.ecommerce.pay.dao.vo;

import jakarta.persistence.Column;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

@Data
@Table(name = "pay_payment_method")
public class PaymentMethod implements Serializable {
    /**
     * 支付方式ID
     */
    @Id
    @Column(name = "payment_method_id")
    private String paymentMethodId;

    /**
     * 支付方式编号
     */
    @Column(name = "payment_method_no")
    private String paymentMethodNo;

    /**
     * 支付方式名称,线上、线下、余额
     */
    @Column(name = "payment_method_name")
    private String paymentMethodName;

    /**
     * 是否启用
     */
    @Column(name = "is_enabled")
    private Boolean isEnabled;

    /**
     * 删除标记
     */
    @Column(name = "del_flg")
    private Boolean delFlg;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    @Serial
    private static final long serialVersionUID = 1L;
}