package com.ecommerce.pay.enums;

/**
 * <AUTHOR>
 */
public enum AccountFlowEnum {

    COMETO_ACCOUNT(1, "来账"),

    GOTO_ACCOUNT(2, "往账");

    /**
     * 描述
     */
    private final Integer code;

    /**
     * 编码
     */
    private final String desc;

    AccountFlowEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static AccountFlowEnum valueOfCode(Integer code) {
        AccountFlowEnum[] paymentStatusEnums = values();
        for (AccountFlowEnum paymentStatusEnum : paymentStatusEnums) {
            if (paymentStatusEnum.code.equals(code)) {
                return paymentStatusEnum;
            }
        }
        return null;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}
