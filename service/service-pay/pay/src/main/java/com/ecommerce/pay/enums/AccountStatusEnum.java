package com.ecommerce.pay.enums;

/**
 * <AUTHOR>
 */
public enum AccountStatusEnum {

    FROZEN(1, "冻结"),

    ACTIVE(2, "激活");

    /**
     * 描述
     */
    private final Integer code;

    /**
     * 编码
     */
    private final String desc;

    AccountStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static AccountStatusEnum valueOfCode(Integer code) {
        AccountStatusEnum[] paymentStatusEnums = values();
        for (AccountStatusEnum paymentStatusEnum : paymentStatusEnums) {
            if (paymentStatusEnum.code.equals(code)) {
                return paymentStatusEnum;
            }
        }
        return null;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}
