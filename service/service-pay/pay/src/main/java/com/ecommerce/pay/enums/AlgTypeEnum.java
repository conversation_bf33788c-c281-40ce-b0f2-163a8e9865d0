package com.ecommerce.pay.enums;

/**
 * <AUTHOR>
 */
public enum AlgTypeEnum {
    MD5(1, "MD5"),

    SHA1(1, "SHA-1");

    /**
     * 描述
     */
    private final int code;

    /**
     * 编码
     */
    private final String desc;

    AlgTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static AlgTypeEnum valueOfCode(int code) {
        AlgTypeEnum[] algTypeEnums = values();
        for (AlgTypeEnum algTypeEnum : algTypeEnums) {
            if (algTypeEnum.code == code) {
                return algTypeEnum;
            }
        }
        return null;
    }

    public int getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}
