package com.ecommerce.pay.enums;

/**
 * <AUTHOR>
 */
public enum ChangeTypeEnum {

    RECHARGE(1, "充值"),

    PAY(2, "支付"),

    PUTCASH(3, "提现"),

    INNERTRANSFER(4, "内部调账"),

    KNOT(5, "结息"),

    INTERESTTAX(6, "利息税"),

    ORIGINALREFUND(7, "原交易退款"),

    ORIGINALCANCEL(8, "原交易撤销");

    /**
     * 描述
     */
    private final Integer code;

    /**
     * 编码
     */
    private final String desc;

    ChangeTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ChangeTypeEnum valueOfCode(Integer code) {
        ChangeTypeEnum[] paymentStatusEnums = values();
        for (ChangeTypeEnum paymentStatusEnum : paymentStatusEnums) {
            if (paymentStatusEnum.code.equals(code)) {
                return paymentStatusEnum;
            }
        }
        return null;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}
