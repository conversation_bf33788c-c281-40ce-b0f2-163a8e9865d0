package com.ecommerce.pay.enums;

/**
 * <AUTHOR>
 */
public enum ChannelTypeEnum {

    ALI_PAY(1, "支付宝支付"),

    WEIXIN_PAY(2, "微信支付"),

    UNDERLINE_PAY(3, "线下支付");

    /**
     * 描述
     */
    private final Integer code;

    /**
     * 编码
     */
    private final String desc;

    ChannelTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ChannelTypeEnum valueOfCode(Integer code) {
        ChannelTypeEnum[] paymentStatusEnums = values();
        for (ChannelTypeEnum paymentStatusEnum : paymentStatusEnums) {
            if (paymentStatusEnum.code.equals(code)) {
                return paymentStatusEnum;
            }
        }
        return null;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}
