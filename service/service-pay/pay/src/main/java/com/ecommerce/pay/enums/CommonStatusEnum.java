package com.ecommerce.pay.enums;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public enum CommonStatusEnum {

    INIT("INIT", "初始化"),

    PROCESS("PROCESS", "处理中"),

    SUCCESS("SUCCESS", "成功"),

    FAIL("FAIL", "失败")

    ;


    /** 枚举值 */
    private final String code;

    /** 枚举描述 */
    private final String message;


    /**
     * 构造一个<code>CommonStatusEnum</code>枚举对象
     *
     * @param code
     * @param message
     */
    private CommonStatusEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * @return Returns the code.
     */
    public String getCode() {
        return code;
    }

    /**
     * @return Returns the message.
     */
    public String getMessage() {
        return message;
    }

    /**
     * @return Returns the code.
     */
    public String code() {
        return code;
    }

    /**
     * @return Returns the message.
     */
    public String message() {
        return message;
    }

    /**
     * 通过枚举<code>code</code>获得枚举
     *
     * @param code
     * @return CommonStatusEnum
     */
    public static CommonStatusEnum getByCode(String code) {
        for (CommonStatusEnum _enum : values()) {
            if (_enum.getCode().equals(code)) {
                return _enum;
            }
        }
        return null;
    }

    /**
     * 获取全部枚举
     *
     * @return List<CommonStatusEnum>
     */
    public List<CommonStatusEnum> getAllEnum() {
        return Arrays.asList(values());
    }

    /**
     * 获取全部枚举值
     *
     * @return List<String>
     */
    public List<String> getAllEnumCode() {
        List<String> list = new ArrayList<>();
        for (CommonStatusEnum _enum : values()) {
            list.add(_enum.code());
        }
        return list;
    }
}
