package com.ecommerce.pay.enums;

public enum MemberCreditRecordEnum
{
    CREDIT(1, "授信"),
    PAYMENT(2, "支付"),
    PAYBACK(3, "还款"),
    REFUND(4, "退款");

    private Integer type;
    private String message;

    private MemberCreditRecordEnum(Integer type, String message)
    {
        this.type = type;
        this.message = message;
    }

    public Integer getType()
    {
        return type;
    }

    public void setType(Integer type)
    {
        this.type = type;
    }

    public String getMessage()
    {
        return message;
    }

    public void setMessage(String message)
    {
        this.message = message;
    }
}
