package com.ecommerce.pay.enums;

import java.util.ArrayList;
import java.util.List;

/**
 * 支付单明细类型:订单货款/订单物流款/订单物流补款/订单货款补款/订单货款退款/订单物流退款/其它
 /**
 * PayDetailTypeEnum
 *
 * <AUTHOR>
 */
public enum PayDetailTypeEnum {

    GOODS("goods", "订单货款"),
    GOODS_SUPPLEMENT("goods_supplement", "订单货款补款"),
    GOODS_REFUND("goods_refund", "订单货款退款"),
    LOGISTICS("logistics", "订单物流款"),
    LOGISTICS_SUPPLEMENT("logistics_supplement", "订单物流补款"),
    LOGISTICS_REFUND("logistics_refund", "订单物流退款"),
    REFUND("refund", "订单退款"),
    OTHER("other", "其它费用"),
    OTHER_REFUND("other_refund", "其它费用退款"),
    MACHINE_SHIFT_COST("machine_shift_cost", "台班费"),
    MACHINE_SHIFT_COST_SUPPLEMENT("machine_shift_cost_supplement", "台班费补款"),
    FLOOR_TRUCKAGE("floor_truckage", "搬运费"),
    FLOOR_TRUCKAGE_SUPPLEMENT("floor_truckage_supplement", "搬运费补款"),
    FLOOR_TRUCKAGE_REFUND("floor_truckage_refund", "搬运费退款"),
    OTHER_SUPPLEMENT("other_supplement", "其他费用补款"),

    DRIVER_DEPOSIT("driver_deposit","支付保证金"),
    DRIVER_LOGISTICS_COST("driver_logistics_cost","司机结算费用"),
    DRIVER_DEPOSIT_REFUND("driver_deposit_refund","退还保证金"),
	;

    /** 枚举值 */
    private final String code;

    /** 枚举描述 */
    private final String message;

    private PayDetailTypeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * @return Returns the code.
     */
    public String getCode() {
        return code;
    }

    /**
     * @return Returns the message.
     */
    public String getMessage() {
        return message;
    }

    /**
     * @return Returns the code.
     */
    public String code() {
        return code;
    }

    /**
     * @return Returns the message.
     */
    public String message() {
        return message;
    }

    public static PayDetailTypeEnum getByCode(String code) {
        for (PayDetailTypeEnum _enum : values()) {
            if (_enum.getCode().equals(code)) {
                return _enum;
            }
        }
        return null;
    }

    public static PayDetailTypeEnum getByCodeOrMessage(String codeOrMessage) {
        for (PayDetailTypeEnum _enum : values()) {
            if (_enum.getCode().equals(codeOrMessage) || _enum.getMessage().equals(codeOrMessage)) {
                return _enum;
            }
        }
        return null;
    }

    public List<PayDetailTypeEnum> getAllEnum() {
        List<PayDetailTypeEnum> list = new ArrayList<PayDetailTypeEnum>();
        for (PayDetailTypeEnum _enum : values()) {
            list.add(_enum);
        }
        return list;
    }

    /**
     * 获取全部枚举值
     *
     * @return List<String>
     */
    public List<String> getAllEnumCode() {
        List<String> list = new ArrayList<String>();
        for (PayDetailTypeEnum _enum : values()) {
            list.add(_enum.code());
        }
        return list;
    }
}
