package com.ecommerce.pay.enums;

import java.util.ArrayList;
import java.util.List;

/**
 * type(thirdPay:第三方支付动账(先充值再支付),balancePay:余额支付动账,transfer：转账动账,recharge:充值动账,putCash:提现动账,refund:退款动账)
 /**
 * TODO    
 *
 * <AUTHOR>
 */
public enum PayTypeEnum {

    UNDERLINEPAY("underLinePay", "线下支付"),

    THIRDPAY("thirdPay", "第三方支付动账(先充值再支付)"),

    WECHATPAY("wechatPay","微信支付"),

    BALANCEPAY("balancePay", "余额支付动账"),

    TRANSFER("transfer", "转账动账"),

    RECHARGE("recharge", "充值动账"),

    PUTCASH("putCash", "提现动账"),

    REFUND("refund", "退款动账");

    /** 枚举值 */
    private final String code;

    /** 枚举描述 */
    private final String message;

    private PayTypeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * @return Returns the code.
     */
    public String getCode() {
        return code;
    }

    /**
     * @return Returns the message.
     */
    public String getMessage() {
        return message;
    }

    /**
     * @return Returns the code.
     */
    public String code() {
        return code;
    }

    /**
     * @return Returns the message.
     */
    public String message() {
        return message;
    }

    public static PayTypeEnum getByCode(String code) {
        for (PayTypeEnum _enum : values()) {
            if (_enum.getCode().equals(code)) {
                return _enum;
            }
        }
        return null;
    }
    
    public List<PayTypeEnum> getAllEnum() {
        List<PayTypeEnum> list = new ArrayList<PayTypeEnum>();
        for (PayTypeEnum _enum : values()) {
            list.add(_enum);
        }
        return list;
    }

    /**
     * 获取全部枚举值
     *
     * @return List<String>
     */
    public List<String> getAllEnumCode() {
        List<String> list = new ArrayList<String>();
        for (PayTypeEnum _enum : values()) {
            list.add(_enum.code());
        }
        return list;
    }
}
