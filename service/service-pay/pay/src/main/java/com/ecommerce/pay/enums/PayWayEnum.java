package com.ecommerce.pay.enums;

import java.util.ArrayList;
import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 */
public enum PayWayEnum {
    ALI_PAY("1", "支付宝支付"),
    WENXIN_PAY("2", "微信支付"),
    UNDERLINE_PAY("3", "线下支付");

    /** 枚举值 */
    private final String code;

    /** 枚举描述 */
    private final String message;


    /**
     * 构造一个<code>PayWayEnum</code>枚举对象
     *
     * @param code
     * @param message
     */
    private PayWayEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * @return Returns the code.
     */
    public String getCode() {
        return code;
    }

    /**
     * @return Returns the message.
     */
    public String getMessage() {
        return message;
    }

    /**
     * @return Returns the code.
     */
    public String code() {
        return code;
    }

    /**
     * @return Returns the message.
     */
    public String message() {
        return message;
    }

    /**
     * 通过枚举<code>code</code>获得枚举
     *
     * @param code
     * @return PayWayEnum
     */
    public static PayWayEnum getByCode(String code) {
        for (PayWayEnum _enum : values()) {
            if (_enum.getCode().equals(code)) {
                return _enum;
            }
        }
        return null;
    }

    /**
     * 获取全部枚举
     *
     * @return List<PayWayEnum>
     */
    public List<PayWayEnum> getAllEnum() {
        List<PayWayEnum> list = new ArrayList<PayWayEnum>();
        for (PayWayEnum _enum : values()) {
            list.add(_enum);
        }
        return list;
    }

    /**
     * 获取全部枚举值
     *
     * @return List<String>
     */
    public List<String> getAllEnumCode() {
        List<String> list = new ArrayList<String>();
        for (PayWayEnum _enum : values()) {
            list.add(_enum.code());
        }
        return list;
    }

}
