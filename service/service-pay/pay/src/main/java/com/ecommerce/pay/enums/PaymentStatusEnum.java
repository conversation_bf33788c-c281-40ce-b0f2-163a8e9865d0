package com.ecommerce.pay.enums;

/**
 * TODO    
 *
 * <AUTHOR>
 */
public enum PaymentStatusEnum {

    WAITING_OPERATION(1, "待操作"),

    OPERATION_ING(2, "操作中"),

    OPERATION_SUCCESS(3, "操作成功"),

    OPERATION_FAIL(4, "操作失败"),

    OPERATION_CLOSED(5,"操作关闭");


    /**
     * 描述
     */
    private final Integer code;

    /**
     * 编码
     */
    private final String desc;

    PaymentStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PaymentStatusEnum valueOfCode(Integer code) {
        PaymentStatusEnum[] paymentStatusEnums = values();
        for (PaymentStatusEnum paymentStatusEnum : paymentStatusEnums) {
            if (paymentStatusEnum.code.equals(code)) {
                return paymentStatusEnum;
            }
        }
        return null;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}
