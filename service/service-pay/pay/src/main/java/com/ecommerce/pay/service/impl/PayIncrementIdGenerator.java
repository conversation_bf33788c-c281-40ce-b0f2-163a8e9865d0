package com.ecommerce.pay.service.impl;

import com.ecommerce.common.service.common.bsid.AbstractIBusinessIdGenerator;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description:
 * @Date: Create in 14:05 28/08/2018
 */
@Service
public class PayIncrementIdGenerator extends AbstractIBusinessIdGenerator {
    @Override
    public String businessCodePrefix() {
        StringBuffer buffer = new StringBuffer();
        String timeStr = new SimpleDateFormat("yyyyMMdd").format(new Date());
        buffer.append(timeStr);
        return buffer.toString();
    }

    public String businessCodePrefixACC() {
        StringBuffer buffer = new StringBuffer("ACC");
        String timeStr = new SimpleDateFormat("yyyyMMdd").format(new Date());
        buffer.append(timeStr);
        buffer.append(gainString());
        return buffer.toString();
    }

    public String businessCodePrefixInvApplyNum() {
        StringBuffer buffer = new StringBuffer("INV");
        String timeStr = new SimpleDateFormat("yyyyMMdd").format(new Date());
        buffer.append(timeStr);
        buffer.append(gainString());
        return buffer.toString();
    }

    public String businessCodePrefixNext() {
        StringBuffer buffer = new StringBuffer();
        String timeStr = new SimpleDateFormat("yyyyMMdd").format(new Date());
        buffer.append("PM");
        buffer.append(timeStr);
        buffer.append(super.gainString());
        return buffer.toString();
    }

    public String businessCodePrefixTradeBill() {
        StringBuffer buffer = new StringBuffer();
        String timeStr = new SimpleDateFormat("yyyyMMdd").format(new Date());
        buffer.append("TBILL");
        buffer.append(timeStr);
        buffer.append(super.gainString());
        return buffer.toString();
    }

    public String businessCodePrefixTradeBillDetails() {
        StringBuffer buffer = new StringBuffer();
        String timeStr = new SimpleDateFormat("yyyyMMdd").format(new Date());
        buffer.append("TDBILL");
        buffer.append(timeStr);
        buffer.append(super.gainString());
        return buffer.toString();
    }

    public String businessCodePrefixTradeFBill() {
        StringBuffer buffer = new StringBuffer();
        String timeStr = new SimpleDateFormat("yyyyMMdd").format(new Date());
        buffer.append("FBILL");
        buffer.append(timeStr);
        buffer.append(super.gainString());
        return buffer.toString();
    }

    public String businessCodePrefixChannel() {
        StringBuffer buffer = new StringBuffer();
        String timeStr = new SimpleDateFormat("yyyyMMdd").format(new Date());
        buffer.append("CHAN");
        buffer.append(timeStr);
        buffer.append(super.gainString());
        return buffer.toString();
    }

    public String businessCodePrefixTradeMBill() {
        StringBuffer buffer = new StringBuffer();
        String timeStr = new SimpleDateFormat("yyyyMMdd").format(new Date());
        buffer.append("MBILL");
        buffer.append(timeStr);
        buffer.append(super.gainString());
        return buffer.toString();
    }

    public String businessCodePrefixMCA() {
        StringBuffer buffer = new StringBuffer();
        String timeStr = new SimpleDateFormat("yyyyMMdd").format(new Date());
        buffer.append("MCA");
        buffer.append(timeStr);
        buffer.append(super.gainString());
        return buffer.toString();
    }

    public String businessCodePrefixAliNotify() {
        StringBuffer buffer = new StringBuffer();
        String timeStr = new SimpleDateFormat("yyyyMMdd").format(new Date());
        buffer.append("ALINOTIFY");
        buffer.append(timeStr);
        buffer.append(super.gainString());
        return buffer.toString();
    }

    public String businessCodePrefixWXNotify() {
        StringBuffer buffer = new StringBuffer();
        String timeStr = new SimpleDateFormat("yyyyMMdd").format(new Date());
        buffer.append("WXNotify");
        buffer.append(timeStr);
        buffer.append(super.gainString());
        return buffer.toString();
    }

    public String businessCodePrefixPaymentBillNo() {
        StringBuffer buffer = new StringBuffer();
        String timeStr = new SimpleDateFormat("yyyyMMdd").format(new Date());
        buffer.append("P");
        buffer.append(timeStr);
        buffer.append(super.gainString());
        return buffer.toString();
    }
}
