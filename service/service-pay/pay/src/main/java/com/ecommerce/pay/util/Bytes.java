package com.ecommerce.pay.util;


/**
 * <AUTHOR>
 */
public class Bytes {
    private static final char[] BASE16 = new char[]{'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};

    private Bytes() {
    }

    public static byte[] copyOf(byte[] src, int length) {
        byte[] dest = new byte[length];
        System.arraycopy(src, 0, dest, 0, Math.min(src.length, length));
        return dest;
    }

    public static byte[] short2bytes(short v) {
        byte[] ret = new byte[]{(byte)0, (byte)0};
        short2bytes(v, ret);
        return ret;
    }

    public static void short2bytes(short v, byte[] b) {
        short2bytes(v, b, 0);
    }

    public static void short2bytes(short v, byte[] b, int off) {
        b[off + 1] = (byte)v;
        b[off + 0] = (byte)(v >>> 8);
    }

    public static byte[] int2bytes(int v) {
        byte[] ret = new byte[]{(byte)0, (byte)0, (byte)0, (byte)0};
        int2bytes(v, ret);
        return ret;
    }

    public static void int2bytes(int v, byte[] b) {
        int2bytes(v, b, 0);
    }

    public static void int2bytes(int v, byte[] b, int off) {
        b[off + 3] = (byte)v;
        b[off + 2] = (byte)(v >>> 8);
        b[off + 1] = (byte)(v >>> 16);
        b[off + 0] = (byte)(v >>> 24);
    }

    public static byte[] float2bytes(float v) {
        byte[] ret = new byte[]{(byte)0, (byte)0, (byte)0, (byte)0};
        float2bytes(v, ret);
        return ret;
    }

    public static void float2bytes(float v, byte[] b) {
        float2bytes(v, b, 0);
    }

    public static void float2bytes(float v, byte[] b, int off) {
        int i = Float.floatToIntBits(v);
        b[off + 3] = (byte)i;
        b[off + 2] = (byte)(i >>> 8);
        b[off + 1] = (byte)(i >>> 16);
        b[off + 0] = (byte)(i >>> 24);
    }

    public static byte[] long2bytes(long v) {
        byte[] ret = new byte[]{(byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0};
        long2bytes(v, ret);
        return ret;
    }

    public static void long2bytes(long v, byte[] b) {
        long2bytes(v, b, 0);
    }

    public static void long2bytes(long v, byte[] b, int off) {
        b[off + 7] = (byte)((int)v);
        b[off + 6] = (byte)((int)(v >>> 8));
        b[off + 5] = (byte)((int)(v >>> 16));
        b[off + 4] = (byte)((int)(v >>> 24));
        b[off + 3] = (byte)((int)(v >>> 32));
        b[off + 2] = (byte)((int)(v >>> 40));
        b[off + 1] = (byte)((int)(v >>> 48));
        b[off + 0] = (byte)((int)(v >>> 56));
    }

    public static byte[] double2bytes(double v) {
        byte[] ret = new byte[]{(byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0};
        double2bytes(v, ret);
        return ret;
    }

    public static void double2bytes(double v, byte[] b) {
        double2bytes(v, b, 0);
    }

    public static void double2bytes(double v, byte[] b, int off) {
        long j = Double.doubleToLongBits(v);
        b[off + 7] = (byte)((int)j);
        b[off + 6] = (byte)((int)(j >>> 8));
        b[off + 5] = (byte)((int)(j >>> 16));
        b[off + 4] = (byte)((int)(j >>> 24));
        b[off + 3] = (byte)((int)(j >>> 32));
        b[off + 2] = (byte)((int)(j >>> 40));
        b[off + 1] = (byte)((int)(j >>> 48));
        b[off] = (byte)((int)(j >>> 56));
    }

    public static short bytes2short(byte[] b) {
        return bytes2short(b, 0);
    }

    public static short bytes2short(byte[] b, int off) {
        return (short)((b[off + 1] & 255) + (b[off] << 8));
    }

    public static int bytes2int(byte[] b) {
        return bytes2int(b, 0);
    }

    public static int bytes2int(byte[] b, int off) {
        return (b[off + 3] & 255) + ((b[off + 2] & 255) << 8) + ((b[off + 1] & 255) << 16) + (b[off + 0] << 24);
    }

    public static float bytes2float(byte[] b) {
        return bytes2float(b, 0);
    }

    public static float bytes2float(byte[] b, int off) {
        int i = (b[off + 3] & 255) + ((b[off + 2] & 255) << 8) + ((b[off + 1] & 255) << 16) + (b[off + 0] << 24);
        return Float.intBitsToFloat(i);
    }

    public static long bytes2long(byte[] b) {
        return bytes2long(b, 0);
    }

    public static long bytes2long(byte[] b, int off) {
        return ((long) b[off + 7] & 255L) + (((long)b[off + 6] & 255L) << 8) + (((long)b[off + 5] & 255L) << 16) + (((long)b[off + 4] & 255L) << 24) + (((long)b[off + 3] & 255L) << 32) + (((long)b[off + 2] & 255L) << 40) + (((long)b[off + 1] & 255L) << 48) + ((long)b[off + 0] << 56);
    }

    public static double bytes2double(byte[] b) {
        return bytes2double(b, 0);
    }

    public static double bytes2double(byte[] b, int off) {
        long j = ((long) b[off + 7] & 255L) + (((long)b[off + 6] & 255L) << 8) + (((long)b[off + 5] & 255L) << 16) + (((long)b[off + 4] & 255L) << 24) + (((long)b[off + 3] & 255L) << 32) + (((long)b[off + 2] & 255L) << 40) + (((long)b[off + 1] & 255L) << 48) + ((long)b[off + 0] << 56);
        return Double.longBitsToDouble(j);
    }

    public static String bytes2hex(byte[] bs) {
        return bytes2hex(bs, 0, bs.length);
    }

    public static String bytes2hex(byte[] bs, int off, int len) {
        if(off < 0) {
            throw new IndexOutOfBoundsException("bytes2hex: offset < 0, offset is " + off);
        } else if(len < 0) {
            throw new IndexOutOfBoundsException("bytes2hex: length < 0, length is " + len);
        } else if(off + len > bs.length) {
            throw new IndexOutOfBoundsException("bytes2hex: offset + length > array length.");
        } else {
            int r = off;
            int w = 0;
            char[] cs = new char[len * 2];

            for(int i = 0; i < len; ++i) {
                byte b = bs[r++];
                cs[w++] = BASE16[b >> 4 & 15];
                cs[w++] = BASE16[b & 15];
            }

            return new String(cs);
        }
    }

    public static byte[] hex2bytes(String s) {
        byte[] bytes;

        bytes = new byte[s.length() / 2];

        for (int i = 0; i < bytes.length; i++) {
            bytes[i] = (byte) Integer.parseInt(s.substring(2 * i, 2 * i + 2), 16);
        }

        return bytes;
    }



}
