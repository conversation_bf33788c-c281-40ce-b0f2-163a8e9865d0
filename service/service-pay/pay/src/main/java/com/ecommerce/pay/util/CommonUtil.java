package com.ecommerce.pay.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ecommerce.common.utils.CsStringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import jakarta.servlet.http.HttpServletRequest;
import java.beans.BeanInfo;
import java.beans.IntrospectionException;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.security.MessageDigest;
import java.security.SecureRandom;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class CommonUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(CommonUtil.class);
    private static final String MAIL_EXP = "^(\\w)+(\\.\\w+)*@(\\w)+((\\.\\w{2,3}){1,3})$";
    private static final String MOBILE_EXP = "^((13[0-9])|(15[^4])|(18[0,2,3,5-9])|(17[0-8])|(147))\\d{8}$";
    private static final String PASS_EXP = "(?=.*[0-9])(?=.*[a-zA-Z])(?=.*[^a-zA-Z0-9]).{8,30}";
    private static final String TIME_HH_MM_EXP = "^((20|21|22|23|[0-1]?\\d):[0-5]?\\d)|24:00$";
    private static final String FULL_TIME_EXP = "^((20|21|22|23|[0-1]?\\d):[0-5]?\\d:[0-5]?\\d)$";
    private static final String charset = "UTF-8";

    /**
     * <AUTHOR>
     * @discription: md5加密
     * @date : 17:14 2018/1/11
     *@param strInput 加密字符串
     * @return java.lang.String
     **/
    public static String md5(String strInput){
        return md5(strInput, charset);
    }

    public static String md5(String strInput, String charset) {
        StringBuffer buf = null;
        try {
            String algorithm = "MD5";
            MessageDigest md = MessageDigest.getInstance(algorithm);
            md.update(strInput.getBytes());
            byte b[] = md.digest();
            buf = new StringBuffer(b.length * 2);
            for (int i = 0; i < b.length; i++) {
                // & 0xff转换无符号整型
                if (((int) b[i] & 0xff) < 0x10) {
                    buf.append("0");
                }
                //转换16进制
                buf.append(Long.toHexString((int) b[i] & 0xff));
            }
        } catch (Exception ex) {
            LOGGER.error(ex.getMessage(),ex);
            return null;
        }
        return buf.toString();
    }

    public static boolean isMobile(String str) {
      return matcher(MOBILE_EXP, str);
    }

    public static boolean checkPass(String pass){
       return matcher(PASS_EXP, pass);
    }

    /**
     *
     * @title ip4检验
     * <AUTHOR>
     * @date 2017年9月29日
     * @param ip
     * @return
     */
    public static boolean isIp4(String ip) {
        if (CsStringUtils.isBlank(ip) || ip.length() < 7 || ip.length() > 15) {
            return false;
        }
        /**
         * 判断IP格式和范围
         */
        String regex = "^([1-9]|[1-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])(\\.(\\d|[1-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])){3}$";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(ip);
        return matcher.matches();
    }

    public static boolean isMail(String str) {
       return matcher(MAIL_EXP, str);
    }

    public static boolean timeHHmmFormateCheck(String time){
        return matcher(TIME_HH_MM_EXP, time);
    }

    public static boolean timeFormateCheck(String time){
        return matcher(FULL_TIME_EXP, time);
    }

    public static boolean matcher(String rexp, String str){
        Pattern p = Pattern.compile(rexp);
        Matcher m = p.matcher(str);
        return m.matches();
    }

    /**
     *
     * @title ip转Long
     * <AUTHOR>
     * @date 2017年9月12日
     * @param strIp
     * @return
     */
    public static Long ip2Number(String strIp) {
        long[] ip = new long[4];
        // 先找到IP地址字符串中.的位置
        int position1 = strIp.indexOf(".");
        int position2 = strIp.indexOf(".", position1 + 1);
        int position3 = strIp.indexOf(".", position2 + 1);
        // 将每个.之间的字符串转换成整型
        ip[0] = Long.parseLong(strIp.substring(0, position1));
        ip[1] = Long.parseLong(strIp.substring(position1 + 1, position2));
        ip[2] = Long.parseLong(strIp.substring(position2 + 1, position3));
        ip[3] = Long.parseLong(strIp.substring(position3 + 1));
        return (ip[0] << 24) + (ip[1] << 16) + (ip[2] << 8) + ip[3];
    }

    /**
     *
     * @title long转ip
     * <AUTHOR>
     * @date 2017年9月12日
     * @param longIp
     * @return
     */
    public static String number2Ip(Long longIp) {
        StringBuffer sb = new StringBuffer("");
        // 直接右移24位
        sb.append(String.valueOf((longIp >>> 24)));
        sb.append(".");
        // 将高8位置0，然后右移16位
        sb.append(String.valueOf((longIp & 0x00FFFFFF) >>> 16));
        sb.append(".");
        // 将高16位置0，然后右移8位
        sb.append(String.valueOf((longIp & 0x0000FFFF) >>> 8));
        sb.append(".");
        // 将高24位置0
        sb.append(String.valueOf((longIp & 0x000000FF)));
        return sb.toString();
    }

    /**
     *
     * @title 将一个list等分成n个list
     * <AUTHOR>
     * @date 2017年11月28日
     * @param source
     * @param n
     * @return
     */
    public static <T> List<List<T>> averageAssign(List<T> source, int n) {
        List<List<T>> result = new ArrayList<List<T>>();
        // (先计算出余数)
        int remaider = source.size() % n;
        // 然后是商
        int number = source.size() / n;
        // 偏移量
        int offset = 0;
        for (int i = 0; i < n; i++) {
            List<T> value = null;
            if (remaider > 0) {
                value = source.subList(i * number + offset, (i + 1) * number + offset + 1);
                remaider--;
                offset++;
            } else {
                value = source.subList(i * number + offset, (i + 1) * number + offset);
            }
            result.add(value);
        }
        return result;
    }

    public static JSONObject convertObj2Json(Object object) {
        try {
            String jsonString = JSON.toJSONString(object);
            return JSON.parseObject(jsonString);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * javabean 转换为map
     * @param obj
     * @return
     * @throws Exception
     */
    public static Map<String, Object> transBean2Map(Object obj) throws IntrospectionException, IllegalAccessException, InvocationTargetException {
        if (obj == null) {
            return null;
        }
        Map<String, Object> map = new HashMap<>();
        BeanInfo beanInfo = Introspector.getBeanInfo(obj.getClass());
        PropertyDescriptor[] propertyDescriptors = beanInfo.getPropertyDescriptors();
        for (PropertyDescriptor property : propertyDescriptors) {
            String key = property.getName();
            // 过滤class属性
            if (!key.equals("class")) {
                // 得到property对应的getter方法
                Method getter = property.getReadMethod();
                Object value = getter.invoke(obj);
                map.put(key, value);
            }
        }
        return map;
    }

    /**
     * 获取当前网络ip
     * @param request
     * @return
     */
    public static String getIpAddr(HttpServletRequest request) throws UnknownHostException {
        String ipAddress = request.getHeader("x-forwarded-for");
        if (CsStringUtils.isEmpty(ipAddress) || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("Proxy-Client-IP");
        }
        if (CsStringUtils.isEmpty(ipAddress) || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("WL-Proxy-Client-IP");
        }
        if (CsStringUtils.isEmpty(ipAddress) || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getRemoteAddr();
            if (ipAddress.equals("127.0.0.1") || ipAddress.equals("0:0:0:0:0:0:0:1")) {
                //根据网卡取本机配置的IP
                InetAddress inet = InetAddress.getLocalHost();
                ipAddress= inet.getHostAddress();
            }
        }
        //对于通过多个代理的情况，第一个IP为客户端真实IP,多个IP按照','分割
        if (ipAddress!=null && ipAddress.length() > 15) { //"***.***.***.***".length() = 15
            if (ipAddress.indexOf(",") > 0) {
                ipAddress = ipAddress.substring(0, ipAddress.indexOf(","));
            }
        }
        return ipAddress;
    }

    private static Random rad = new SecureRandom();

    /**
     * 产生num位的随机数
     * @return
     */
    public static String getRandByNum(int num){
        String length = "1";
        for(int i=0;i<num;i++){
            length += "0";
        }

        String result  = rad.nextInt(Integer.parseInt(length)) +"";

        if(result.length()!=num){
            return getRandByNum(num);
        }
        return result;
    }

}