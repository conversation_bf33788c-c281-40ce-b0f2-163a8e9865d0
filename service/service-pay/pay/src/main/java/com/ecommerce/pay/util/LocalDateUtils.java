package com.ecommerce.pay.util;

import lombok.extern.slf4j.Slf4j;
import com.ecommerce.common.utils.CsStringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.Date;

@Slf4j
public class LocalDateUtils {

    public static long getLocalTime(long timestamp, LocalTime localTime){
        return Instant.ofEpochMilli(timestamp)
                .atZone(ZoneId.systemDefault())
                .toLocalDate()
                .atTime(localTime)
                .atZone(ZoneId.systemDefault())
                .toInstant()
                .toEpochMilli();
    }

    public static Date getBeginDate(Date date){
        if( date == null ){
            return null;
        }
        return new Date(getLocalTime(date.getTime(),LocalTime.MIN));
    }

    public static Date getEndDate(Date date){
        if( date == null ){
            return null;
        }
        return new Date(getLocalTime(date.getTime(),LocalTime.MAX));
    }

    public static Date str2Date(String dateStr, String pattern){
        try {
            if (CsStringUtils.isBlank(dateStr)) {
                return null;
            }
            SimpleDateFormat dateFormat = new SimpleDateFormat(pattern);
            return dateFormat.parse(dateStr);
        } catch (ParseException e) {
            log.error(e.getMessage(),e);
            return null;
        }
    }

    public static Integer now2yyyyMMdd(){
        return Integer.valueOf(LocalDateTime.now().format(dateTimeFormatter));
    }

    public static DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");
}
