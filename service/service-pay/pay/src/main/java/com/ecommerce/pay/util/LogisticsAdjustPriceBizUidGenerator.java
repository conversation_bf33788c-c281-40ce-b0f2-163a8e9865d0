package com.ecommerce.pay.util;

import com.ecommerce.common.service.common.bsid.AbstractIBusinessIdGenerator;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;
@Component
public class LogisticsAdjustPriceBizUidGenerator extends AbstractIBusinessIdGenerator {

	@Override
	public String businessCodePrefix() {
		StringBuilder builder = new StringBuilder();
        String timeStr = new SimpleDateFormat("yyyyMMdd").format(new Date());
        builder.append(timeStr);
        return builder.toString();
	}
	
	public String businessCode() {
        StringBuilder builder = new StringBuilder("LA");
        String timeStr = new SimpleDateFormat("yyyyMMdd").format(new Date());
        builder.append(timeStr);
        builder.append(gainString());
        return builder.toString();
    }

}
