package com.ecommerce.pay.util;

import com.ecommerce.common.exception.BizException;
import com.ecommerce.pay.api.exception.PayCode;
import com.google.zxing.*;
import com.google.zxing.client.j2se.BufferedImageLuminanceSource;
import com.google.zxing.client.j2se.MatrixToImageConfig;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.common.HybridBinarizer;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import lombok.extern.slf4j.Slf4j;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.util.Base64;
import java.util.EnumMap;
import java.util.HashMap;
import java.util.Map;

/**
 * 二维码图形生成工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class QRCodeUtil {

    /**
     * Zxing图形码生成工具
     *
     * @param contents      内容
     * @param barcodeFormat BarcodeFormat对象
     * @param format        图片格式，可选[png,jpg,bmp]
     * @param width         宽
     * @param height        高
     * @param margin        边框间距px
     * @return
     */
    public static String encode(String contents, BarcodeFormat barcodeFormat, Integer margin,
                                ErrorCorrectionLevel errorLevel, String format, int width, int height) {
        String bool = "";
        BufferedImage bufImg;
        Map<EncodeHintType, Object> hints = new HashMap<EncodeHintType, Object>();
        // 指定纠错等级
        hints.put(EncodeHintType.ERROR_CORRECTION, errorLevel);
        hints.put(EncodeHintType.MARGIN, margin);
        hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
        try {
            BitMatrix bitMatrix = new MultiFormatWriter().encode(contents, barcodeFormat, width, height, hints);
            MatrixToImageConfig config = new MatrixToImageConfig(0xFF000001, 0xFFFFFFFF);
            bufImg = MatrixToImageWriter.toBufferedImage(bitMatrix, config);
            bool = writeToFile(bufImg, format);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return bool;
    }

    /**
     * @param srcImgFilePath 要解码的图片地址
     * @return
     */
    @SuppressWarnings("finally")
    public static Result decode(String srcImgFilePath) {
        Result result = null;
        BufferedImage image;
        try {
            File srcFile = new File(srcImgFilePath);
            image = ImageIO.read(srcFile);
            if (null != image) {
                LuminanceSource source = new BufferedImageLuminanceSource(image);
                BinaryBitmap bitmap = new BinaryBitmap(new HybridBinarizer(source));

                Map<DecodeHintType, String> hints = new EnumMap<>(DecodeHintType.class);
                hints.put(DecodeHintType.CHARACTER_SET, "UTF-8");
                result = new MultiFormatReader().decode(bitmap, hints);
            } else {
                log.debug("Could not decode image.");
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return result;
    }

    /**
     * 将BufferedImage对象转换为base64返回
     *
     * @param bufImg BufferedImage对象
     * @param format 图片格式，可选[png,jpg,bmp]
     * @return
     */
    @SuppressWarnings("finally")
    private static String writeToFile(BufferedImage bufImg, String format) {
        ByteArrayOutputStream baos = null;
        try {
            baos = new ByteArrayOutputStream();//io流
            ImageIO.write(bufImg, format, baos);
            byte[] bytes = baos.toByteArray();//转换成字节
            String png_base64 = Base64.getEncoder().encodeToString(bytes).trim();//转换成base64串
            png_base64 = png_base64.replaceAll("\n", "").replaceAll("\r", "");//删除 \r\n
            return png_base64;
        } catch (Exception e) {
            log.error("创建二维码图片出错");
            throw new BizException(PayCode.WECHAT_CREATEQRCODE_ERROR, "创建二维码图片出错");
        } finally {
            if (null != baos) {
                try {
                    baos.close();
                } catch (IOException e) {
                    log.error("流关闭出错");
                }
            }
        }
    }
}

