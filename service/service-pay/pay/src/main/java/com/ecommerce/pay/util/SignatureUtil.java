package com.ecommerce.pay.util;



import com.ecommerce.common.exception.BizException;import com.ecommerce.common.exception.BasicCode;import cn.hutool.core.collection.CollectionUtil;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.pay.enums.AlgTypeEnum;
import lombok.extern.slf4j.Slf4j;

import java.beans.IntrospectionException;
import java.lang.reflect.InvocationTargetException;
import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.TreeSet;

@Slf4j
public class SignatureUtil {
    /**
     * 获取内容摘要
     * @param content
     * @param algType
     * @return
     * @throws Exception
     */
    public static String getDigest(String content, AlgTypeEnum algType) throws Exception{
        MessageDigest md = MessageDigest.getInstance(algType.getDesc());
        byte[] bytes = md.digest(content.getBytes("utf-8"));
        return Bytes.bytes2hex(bytes);
    }

    /**
     * 参数组装
     * @param objectBean
     * @return
     */
    public static String assemblyParam(Object objectBean, String secret) throws IntrospectionException, IllegalAccessException, InvocationTargetException {
        if (objectBean == null) {
            return "";
        }
        Map<String, Object> params;
        if (objectBean instanceof Map) {
            params = (Map<String, Object>)objectBean;
        } else {
            params = CommonUtil.transBean2Map(objectBean);
        }
        TreeSet<String> keySet = new TreeSet<>(params.keySet());
        List<String> keyList = new ArrayList<>();
        for (String key : keySet) {
            Object value = params.get(key);
            if (value != null && !"sign".equals(key)) {
                if (value instanceof String string) {
                    if (CsStringUtils.isEmpty(string)) {
                        continue;
                    }
                }
                if ("packageData".equals(key)) {
                    key = "package";
                }
                keyList.add(key + "=" + value);
            }
        }
        //加入密钥
        keyList.add("key=" + secret);
        log.info("---->："+CollectionUtil.join(keyList, "&"));
        return CollectionUtil.join(keyList, "&");
    }
}
