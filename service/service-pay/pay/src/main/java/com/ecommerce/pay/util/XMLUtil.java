package com.ecommerce.pay.util;


import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;

import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.JAXBException;
import jakarta.xml.bind.Marshaller;
import jakarta.xml.bind.Unmarshaller;
import java.io.StringReader;
import java.io.StringWriter;
import java.util.*;

/**
 * <AUTHOR>
 */
public class XMLUtil {

    /**
     * bean对象转换为xml文档字符串
     *
     * @param beanObject
     * @param <T>
     * @return
     * @throws Exception
     */
    public static <T> String beanToXML(T beanObject) throws JAXBException {
        StringWriter writer = new StringWriter();
        JAXBContext context = JAXBContext.newInstance(beanObject.getClass());
        Marshaller marshaller = context.createMarshaller();
        marshaller.marshal(beanObject, writer);
        return writer.toString();
    }

    /**
     * xml字符串转换为bean对象
     *
     * @param xmlStr
     * @param tClass
     * @param <T>
     * @return
     * @throws Exception
     */
    public static <T> T XMLStringToBean(String xmlStr, Class<T> tClass) throws JAXBException {
        StringReader reader = new StringReader(xmlStr);
        JAXBContext context = JAXBContext.newInstance(tClass);
        Unmarshaller unmarshaller = context.createUnmarshaller();
        return (T) unmarshaller.unmarshal(reader);
    }

    @SuppressWarnings("unchecked")
    public static Map<String, Object> Dom2Map(String xmlDoc) throws DocumentException {
        Document doc = DocumentHelper.parseText(xmlDoc);
        Map<String, Object> map = new HashMap<>();
        if (doc == null)
            return map;
        Element root = doc.getRootElement();
        for (Iterator<Element> iterator = root.elementIterator(); iterator.hasNext(); ) {
            Element e = (Element) iterator.next();
            List<Element> list = e.elements();
            if (list.size() > 0) {
                map.put(e.getName(), Dom2Map(e));
            } else {
                map.put(e.getName(), e.getText());
            }
        }
        return map;
    }

    @SuppressWarnings("unchecked")
    public static Map<String, Object> Dom2Map(Element e) {
        Map<String, Object> map = new HashMap<>();
        List<Element> list = e.elements();
        if (!list.isEmpty()) {
            for (int i = 0;i < list.size(); i++) {
                Element iter = (Element) list.get(i);
                List<Object> mapList = new ArrayList<>();
                if (!iter.elements().isEmpty()) {
                    Map<String, Object> m = Dom2Map(iter);
                    if (map.get(iter.getName()) != null){
                        Object obj = map.get(iter.getName());
                        if (!(obj instanceof java.util.ArrayList)){
                            mapList = new ArrayList<>();
                            mapList.add(obj);
                            mapList.add(m);
                        }
                        if (obj instanceof List<?> arrayList) {
                            mapList = (List<Object>) arrayList;
                            mapList.add(m);
                        }
                        map.put(iter.getName(), mapList);
                    } else {
                        map.put(iter.getName(), m);
                    }
                } else {
                    if (map.get(iter.getName()) != null) {
                        Object obj = map.get(iter.getName());
                        if (!(obj instanceof java.util.ArrayList)) {
                            mapList = new ArrayList<>();
                            mapList.add(obj);
                            mapList.add(iter.getText());
                        }
                        if (obj instanceof List<?> arrayList) {
                            mapList = (List<Object>) arrayList;
                            mapList.add(iter.getText());
                        }
                        map.put(iter.getName(), mapList);
                    } else {
                        map.put(iter.getName(), iter.getText());
                    }
                }
            }
        } else {
            map.put(e.getName(), e.getText());
        }
        return map;
    }
}