package com.ecommerce.pay.v2.biz;

import com.ecommerce.pay.api.v2.dto.adjust.AdjustPriceBillDTO;
import com.ecommerce.pay.api.v2.dto.adjust.AdjustPriceDTO;
import com.ecommerce.pay.api.v2.dto.adjust.AdjustPriceDetailDTO;
import com.ecommerce.pay.api.v2.dto.adjust.AdjustPriceMemberDTO;
import com.ecommerce.pay.api.v2.dto.adjust.AdjustPricePayResultDTO;
import com.ecommerce.pay.api.v2.dto.adjust.AdjustPriceQueryDTO;
import com.github.pagehelper.Page;

import java.util.List;

public interface IAdjustPriceBiz
{
    /**
     * 调价列表
     * @param query
     * @return
     */
    public Page<AdjustPriceDTO> getAdjustPriceList(AdjustPriceQueryDTO query);

    /**
     * 价格回调详情
     * @param priceAdjustId
     * @param excludeMember
     * @return
     */
    public AdjustPriceDTO adjustInfo(String priceAdjustId, boolean excludeMember);

    /**
     * 查询一个调价的买家列表
     * @param priceAdjustId
     * @param pageNum
     * @param pageSize
     * @return
     */
    public Page<AdjustPriceMemberDTO> getAdjustPriceMemberList(String priceAdjustId, int pageNum, int pageSize);

    /**
     * 查询一个调价的买家的运单列表
     * @param priceAdjustId
     * @param buyerId
     * @param
     * @param pageNum
     * @param pageSize
     * @return
     */
    public Page<AdjustPriceDetailDTO> getAdjustPriceDetailList(String priceAdjustId, String buyerId, String dealsName, int pageNum, int pageSize);

    /**
     * 查询一个调价的运单列表
     * @param priceAdjustId
     * @return
     */
    public List<AdjustPriceDetailDTO> getAdjustPriceDetailListByAdjustId(String priceAdjustId);

    /**
     * 回调记录查询
     * @param priceAdjustId
     * @param waybillId
     * @return
     */
    public List<AdjustPriceBillDTO> getAdjustPriceBillList(String priceAdjustId, String waybillId);

    /**
     * 获取调价涉及到的买家列表
     * @param query
     * @return
     */
    public List<AdjustPriceMemberDTO> calculateAdjustMember(AdjustPriceDTO query);

    /**
     * 获取调价涉及到的指定买家运单列表
     * @param query
     * @return
     */
    public List<AdjustPriceDetailDTO> calculateAdjustDetail(AdjustPriceDTO query);

    /**
     * 保存调价信息
     * @param dto
     * @param operator
     * @return
     */
    public boolean saveAdjustPriceList(AdjustPriceDTO dto, String operator);

    /**
     * ERP调价异步回调
     * @param resultDTO
     */
    public void processErpAdjustResult(AdjustPricePayResultDTO resultDTO);

    /**
     * 处理没有ERP买家的调价数据
     * @param adjustPriceDTO
     * @param detailList
     * @param operator
     */
    public void processNoErpAdjustPrice(AdjustPriceDTO adjustPriceDTO, List<AdjustPriceDetailDTO> detailList, String operator);
}
