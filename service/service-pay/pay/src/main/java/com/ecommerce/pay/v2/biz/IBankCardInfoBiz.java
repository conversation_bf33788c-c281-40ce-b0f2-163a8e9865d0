package com.ecommerce.pay.v2.biz;

import com.ecommerce.pay.api.v2.dto.BankCardInfoDTO;


/**
 * <AUTHOR>
 * @created 15:42 08/04/2019
 * @description
 */
public interface IBankCardInfoBiz {

    /**
     *
     * @return
     */
    BankCardInfoDTO findById(String id);

    /**
     *
     * @return
     */
    BankCardInfoDTO findByPrefix(String account);

    /**
     *
     * @return
     */
    BankCardInfoDTO findByBankName(String bankName);

    String findIconByBankName(String bankName);

}
