package com.ecommerce.pay.v2.biz;

import java.util.List;

import com.ecommerce.pay.api.v2.dto.BankCityDTO;

/**
 * 获取银行城市信息
 * 
 * <AUTHOR>
 *
 */
public interface IBankCityBiz {

	/**
	 * 根据城市编码获取银行城市
	 * 
	 * @param cityCode
	 * @return
	 */
	public BankCityDTO getBankCityByCode(String cityCode);

	/**
	 * 获取下级城市编码，如果参数为空则返回省
	 *
	 * @param cityCode bd => cityAreacode
	 * @return
	 */
	public List<BankCityDTO> getSubBankCity(String cityCode);

}
