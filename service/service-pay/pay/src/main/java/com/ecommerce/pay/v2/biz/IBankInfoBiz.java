package com.ecommerce.pay.v2.biz;

import com.ecommerce.pay.api.v2.dto.BankInfoDTO;
import com.github.pagehelper.PageInfo;

import java.util.List;

public interface IBankInfoBiz {

	/**
	 * 查询 银行信息，最多返回10条
	 * 
	 * @param query
	 * @return
	 */
	List<BankInfoDTO> getBankInfo(BankInfoDTO query, Integer limit);

	/**
	 * 查询 银行信息
	 *
	 * @param query
	 * @return
	 */
	PageInfo<BankInfoDTO> pageBankInfo(BankInfoDTO query);

	/**
	 * 新增
	 *
	 * @param bankInfoDTO
	 * @return
	 */
	void save(BankInfoDTO bankInfoDTO);

	/**
	 * 删除大小额联行号
	 * @param bankNo
	 */
	void delete(String bankNo);
	
	

}
