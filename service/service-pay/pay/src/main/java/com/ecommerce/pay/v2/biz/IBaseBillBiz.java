package com.ecommerce.pay.v2.biz;

import java.util.Optional;

/**
 * <AUTHOR>
 * @created 20:14 02/09/2019
 * @description TODO
 */
public interface IBaseBillBiz<T> {

    /**
     * 创建
     * @param billDTO
     */
    void create(T billDTO, String operatorId);

    /**
     * 根据编号查找
     * @param billNo
     * @return
     */
    Optional<T> findByNo(String billNo);

    /**
     * 根据ID查找
     * @param billId
     * @return
     */
    Optional<T> findById(String billId);

    /**
     * 更新
     * @param billDTO
     * @param operatorId
     */
    void update(T billDTO, String operatorId);

    /**
     * 更新状态
     * @param id
     * @param operatorId
     */
    void updateStatus(String id, String status, String operatorId);
}
