package com.ecommerce.pay.v2.biz;

import com.ecommerce.pay.api.v2.dto.bill.BillLogsDTO;
import com.ecommerce.pay.api.v2.dto.bill.CashWithdrawalBillDTO;
import com.ecommerce.pay.api.v2.dto.bill.DeliverWayUpdateDTO;
import com.ecommerce.pay.api.v2.dto.bill.PaymentBillDTO;
import com.ecommerce.pay.api.v2.dto.bill.RechargeBillDTO;
import com.ecommerce.pay.api.v2.dto.bill.RefundBillDTO;
import com.ecommerce.pay.api.v2.dto.bill.SplitPayBillDTO;
import com.ecommerce.pay.api.v2.dto.bill.SplitPayBillDetailDTO;
import com.ecommerce.pay.api.v2.dto.bill.TradingFlowExportDTO;
import com.ecommerce.pay.api.v2.dto.electricInvoiceAccount.PageQuery;
import com.ecommerce.pay.v2.dao.vo.BillLogs;
import com.ecommerce.pay.v2.dao.vo.DriverPayInfo;
import com.github.pagehelper.Page;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @created 19:58 02/09/2019
 * @description TODO
 */
public interface IBillLogsBiz {

    /**
     * 根据MemberId查询实际发生的支付渠道List
     * @param memberId
     * @param payerFlg
     * @return
     */
    Set<String> findMemberPayChannelForQuery(String memberId, Integer payerFlg);

    /**
     * 获取流水
     * @return
     */
    int countByRelatedBillNo(String relatedBillNo);

    /**
     * 获取流水
     * @param logsDTO
     * @return
     */
    Page<BillLogsDTO> pageBillLogs(PageQuery<BillLogsDTO> logsDTO);

    /**
     * 保存
     * @param logsDTO
     */
    void saveErpBillLogs(BillLogsDTO logsDTO, String operatorId);

    /**
     * for gnete withdraw,recharge
     * @param billLogs
     */
    void saveLogs(BillLogs billLogs, String operatorId);

    void saveErpBillLogs(PaymentBillDTO paymentBillDTO, String operatorId);

    /**
     * 提现流水
     * @param cashWithdrawalBillDTO
     * @param operatorId
     */
    void logsCashWithdrawal(CashWithdrawalBillDTO cashWithdrawalBillDTO, String operatorId);

    /**
     * 充值流水
     * @param rechargeBillDTO
     * @param operatorId
     */
    void logsRecharge(RechargeBillDTO rechargeBillDTO, String operatorId);

    /**
     * 退款流水
     * @param refundBillDTO
     * @param operatorId
     */
    void logsRefund(RefundBillDTO refundBillDTO, String operatorId);

    /**
     * 退款流水
     * @param refundBillDTO
     * @param operatorId
     */
    void logsRefundByFreeze(RefundBillDTO refundBillDTO, String operatorId);

    /**
     * 分账流水
     * @param splitPayBillDTO
     * @param operatorId
     */
    void logsSplit(SplitPayBillDTO splitPayBillDTO, String operatorId);

    /**
     * 分账流水
     * @param detailDTO
     * @param operatorId
     */
    void logsSingleSplit(SplitPayBillDTO splitPayBillDTO, SplitPayBillDetailDTO detailDTO, String operatorId);

    /**
     * 支付流水
     * @param paymentBillDTO
     * @param operatorId
     */
    void logsPayment(PaymentBillDTO paymentBillDTO, String operatorId);

    /**
     * 支付流水
     * @param paymentBillDTO
     * @param operatorId
     */
    void logsPaymentByFreeze(PaymentBillDTO paymentBillDTO, String operatorId);

    /**
     * 还款流水
     * @param paymentBillDTO
     * @param operatorId
     */
    void logsRepayment(PaymentBillDTO paymentBillDTO, String operatorId, String operatorName);

    void logsDriverPayInfo(DriverPayInfo driverPayInfo, String operator);
    /**
     * 更新订单配置方式
     */
    void updateDeliverWay(List<DeliverWayUpdateDTO> list);

    List<TradingFlowExportDTO> exportTradingFlow(BillLogsDTO queryDTO);
}
