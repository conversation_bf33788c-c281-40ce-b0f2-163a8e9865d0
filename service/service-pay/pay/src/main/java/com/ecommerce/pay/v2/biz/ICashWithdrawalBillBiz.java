package com.ecommerce.pay.v2.biz;

import com.ecommerce.pay.api.v2.dto.bill.CashWithdrawalBillDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @created 20:05 02/09/2019
 * @description TODO
 */
public interface ICashWithdrawalBillBiz extends IBaseBillBiz<CashWithdrawalBillDTO> {

    /**
     * 查询未完成的提现单
     * @param memberId
     * @return
     */
    List<CashWithdrawalBillDTO> findUnCompleteBillByMemberId(String memberId,String channelId);

    /**
     * 查询交易中的提现单
     */
    List<CashWithdrawalBillDTO> pageInfo(String payerMemberId,int pageNum);
}
