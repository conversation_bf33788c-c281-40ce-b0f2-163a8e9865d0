package com.ecommerce.pay.v2.biz;

import com.ecommerce.pay.api.v2.dto.ChannelCardDTO;
import com.ecommerce.pay.v2.dao.vo.ChannelCard;

import java.util.List;

public interface IChannelCardBiz {

	/**
	 * 根据会员id 获取线下支付渠道信息
	 * 
	 * @return
	 */
	List<ChannelCardDTO> getChannelCardByMemberChannelId(String memberChannelId);

	/**
	 * 根据会员id 获取线下支付渠道信息
	 *
	 * @return
	 */
	int getChannelCardCountByMemberChannelId(String memberChannelId);
	/**
	 * 根据会员id 获取线下支付渠道信息 不看状态：channelCardStatus
	 *
	 * @return
	 */
	int getChannelCardCountByMemberChannelId2(String memberChannelId);


	/**
	 * 设置默认银行卡
	 * @param memberChannelId
	 * @param channelCardId
	 */
	void updateDefaultChannelCard(String memberChannelId, String channelCardId, String operatorId);

	String hideAccountNo(String bankAccount);

	/**
	 * @param channelCardDTO
	 * @param operator
	 */
	void addChannelCard(ChannelCardDTO channelCardDTO, String operator);

	/**
	 * @param channelCardId
	 */
	void removeChannelCard(String channelCardId, String operator);

	/**
	 * 查询渠道下银行卡
	 * 
	 * @param channelCardId
	 * @return
	 */
	ChannelCardDTO getChannelCardById(String channelCardId);

	/** 
	 * @Title: removeAllChannelCardByChannelId 
	 * @Description: 移除所有银行卡
	 * <AUTHOR>
	 * @param memberChannelId
	 * @param operator
	 */
	void removeAllChannelCardByChannelId(String memberChannelId, String operator);

	/**
	 * @Title: removeAllChannelCardByChannelId
	 * @Description: 检查银行卡是否重复
	 * <AUTHOR>
	 * @param bankAccount 银行卡号
	 */
	boolean checkReduplicate(String bankAccount);

	/**
	 * 根据ID 修改银行卡状态
	 * @param channelCardId
	 * @param status
	 */
	void updateCardStatusById(String channelCardId, String status);

	/**
	 * 根据会员渠道
	 * @param memberId
	 * @param memberChannelId
	 * @param status
	 */
	void updateCardStatus(String memberId,String memberChannelId, String status);

	/**
	 * 根据银行卡号查询
	 * @param bankAccount
	 * @return
	 */
	List<ChannelCard> findByBankAccount(String bankAccount);
}
