package com.ecommerce.pay.v2.biz;

import com.ecommerce.common.service.IBaseBiz;
import com.ecommerce.pay.api.v2.dto.ChannelConfigDTO;
import com.ecommerce.pay.api.v2.enums.CustomerEnum;
import com.ecommerce.pay.v2.dao.vo.ChannelConfig;

import java.util.List;

public interface IChannelConfigBiz extends IBaseBiz<ChannelConfig> {

    /**
     * 获取渠道配置信息
     * @param type
     * @return
     */
    ChannelConfigDTO findChannelConfigByType(String type);

    /**
     * 根据ID查询
     * @param channelId
     * @return
     */
    ChannelConfigDTO findById(String channelId);

    /**
     * 根据ID列表查询
     * @param channelIds
     * @return
     */
    List<ChannelConfigDTO> findByIds(List<String> channelIds);

    /**
     * 根据ID查询
     * @param receiveId
     * @return
     */
    List<ChannelConfigDTO> findByReceiveId(String receiveId);

    /**
     * 根据ID查询
     * @param channelCode
     * @return
     */
    ChannelConfigDTO findByChannelCode(String channelCode);

    /**
     * 获取可用的平台支持的支付渠道
     *
     * @pdOid dd35d900-49b3-4a6f-800d-a372fa5cf09a
     */
    List<ChannelConfigDTO> getPlatformAvailChannels();

    /**
     * 根据买家或者卖家，获取支持的支付渠道
     *
     * @pdOid dd35d900-49b3-4a6f-800d-a372fa5cf09a
     */
    List<ChannelConfigDTO> getCustomerAvailChannels(CustomerEnum customer);
}
