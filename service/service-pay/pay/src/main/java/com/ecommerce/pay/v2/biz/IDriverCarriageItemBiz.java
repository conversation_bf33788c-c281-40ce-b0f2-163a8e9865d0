package com.ecommerce.pay.v2.biz;

import com.ecommerce.common.service.IBaseBiz;
import com.ecommerce.pay.api.v2.dto.driver.WaybillLogisticsInfoDTO;
import com.ecommerce.pay.api.v2.dto.driver.WaybillLogisticsInfoQueryDTO;
import com.ecommerce.pay.api.v2.dto.driver.WithdrawLogQueryDTO;
import com.ecommerce.pay.api.v2.dto.electricInvoiceAccount.PageQuery;
import com.ecommerce.pay.v2.dao.vo.DriverCarriageItem;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;

import java.util.List;

public interface IDriverCarriageItemBiz extends IBaseBiz<DriverCarriageItem> {

    List<DriverCarriageItem> findByWaybill(List<String> waybillNum);

    @Operation(summary = "司机app接口-账户明细")
    PageInfo<WaybillLogisticsInfoDTO> pageWaybillLogisticsInfo(PageQuery<WithdrawLogQueryDTO> dto);

    @Operation(summary = "平台接口-个体司机运费管理-司机运费明细数据-翻页查询")
    PageInfo<WaybillLogisticsInfoDTO> pageDriverCostItem(PageQuery<WaybillLogisticsInfoQueryDTO> dto);

    boolean notifyLogistics(String day,String accountId);
}
