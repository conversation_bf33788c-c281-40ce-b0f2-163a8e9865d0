package com.ecommerce.pay.v2.biz;

import com.ecommerce.common.service.IBaseBiz;
import com.ecommerce.pay.api.v2.dto.driver.WaybillDaySummerDTO;
import com.ecommerce.pay.api.v2.dto.driver.WithdrawLogQueryDTO;
import com.ecommerce.pay.api.v2.dto.electricInvoiceAccount.PageQuery;
import com.ecommerce.pay.v2.dao.vo.DriverDayCarriage;
import com.github.pagehelper.PageInfo;

import java.util.List;

public interface IDriverDayCarriageBiz extends IBaseBiz<DriverDayCarriage> {


    List<DriverDayCarriage> findByDay(List<String> day);
    List<DriverDayCarriage> findByDayAndPayNumber(List<String> day,String payNumber);
    DriverDayCarriage findByDay(String accountId,String day);

    PageInfo<WaybillDaySummerDTO> pageWaybillDaySummerInfo(PageQuery<WithdrawLogQueryDTO> dto);
}
