package com.ecommerce.pay.v2.biz;

import com.ecommerce.common.service.IBaseBiz;
import com.ecommerce.open.api.dto.gnete.ConsumeRefundResponseDTO;
import com.ecommerce.open.api.dto.gnete.WithdrawResponseDTO;
import com.ecommerce.pay.api.v2.dto.driver.DriverPayInfoDTO;
import com.ecommerce.pay.api.v2.dto.driver.DriverPayInfoQueryDTO;
import com.ecommerce.pay.api.v2.dto.driver.PrePayDepositDTO;
import com.ecommerce.pay.api.v2.dto.driver.WithdrawLogDTO;
import com.ecommerce.pay.api.v2.dto.driver.WithdrawLogQueryDTO;
import com.ecommerce.pay.api.v2.dto.electricInvoiceAccount.PageQuery;
import com.ecommerce.pay.api.v2.dto.gnete.WithdrawDTO;
import com.ecommerce.pay.v2.dao.vo.DriverPayInfo;
import com.github.pagehelper.PageInfo;

import java.math.BigDecimal;

public interface IDriverPayInfoBiz extends IBaseBiz<DriverPayInfo> {

    DriverPayInfo createDepositPayInfo(PrePayDepositDTO dto,String operator);
    DriverPayInfo createPlatformPayInfo(BigDecimal payAmount,String regId, String operator);
    DriverPayInfo createDepositRefundPayInfo(DriverPayInfo driverPayInfo, ConsumeRefundResponseDTO res,String operator);

    DriverPayInfo createWithdrawPayInfo(WithdrawDTO dto, WithdrawResponseDTO res);

    PageInfo<DriverPayInfoDTO> pageDriverPayInfo(PageQuery<DriverPayInfoQueryDTO> query);
    PageInfo<WithdrawLogDTO> pageWithdrawLog(PageQuery<WithdrawLogQueryDTO> dto);

    boolean updatePayInfo(String driverPayInfoId, String payNumber, String operator);
}
