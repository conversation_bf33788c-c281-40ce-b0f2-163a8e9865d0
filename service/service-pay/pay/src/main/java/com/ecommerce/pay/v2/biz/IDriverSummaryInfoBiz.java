package com.ecommerce.pay.v2.biz;

import com.ecommerce.common.service.IBaseBiz;
import com.ecommerce.pay.api.v2.dto.driver.DriverSummaryInfoQueryDTO;
import com.ecommerce.pay.api.v2.dto.gnete.OpenWalletDTO;
import com.ecommerce.pay.api.v2.dto.electricInvoiceAccount.PageQuery;
import com.ecommerce.pay.v2.dao.vo.DriverSummaryInfo;
import com.github.pagehelper.PageInfo;

import java.math.BigDecimal;
import java.util.List;

public interface IDriverSummaryInfoBiz extends IBaseBiz<DriverSummaryInfo> {

    List<String> findDepositPayedDriver(List<String> memberIds);

    PageInfo<DriverSummaryInfo> pageDriverSummaryInfo(PageQuery<DriverSummaryInfoQueryDTO> dto);

    Boolean openWallet(OpenWalletDTO dto,String walletId);

    DriverSummaryInfo findByMemberId(String memberId);

    DriverSummaryInfo findByAccountId(String accountId);

    /**
     * 保证金已缴
     */
    boolean updateDepositPayStatus(String driverSummaryInfoId, BigDecimal depositAmount,String payInfoId,String operator);

    boolean updateBankCard(String accountId,String bankCard);
    /**
     * 保证金已退
     */
    boolean updateDepositRefunded(String accountId);
    /**
     * 更新提现信息
     */
    boolean updateWithdraw(DriverSummaryInfo driverSummaryInfo,BigDecimal withdrawAmount,String operator);
}
