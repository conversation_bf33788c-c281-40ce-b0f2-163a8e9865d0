package com.ecommerce.pay.v2.biz;

import com.ecommerce.pay.api.v2.dto.invoice.AuditInvoiceApplyDTO;
import com.ecommerce.pay.api.v2.dto.invoice.InvoiceApplyAddDTO;
import com.ecommerce.pay.api.v2.dto.invoice.RevokeInvoiceApplyDTO;
import com.ecommerce.pay.v2.dao.bean.SendInvoiceBean;

import java.util.List;

/**
 *  IElectricInvoiceApplyBiz
 *
 * <AUTHOR>
 */
public interface IElectricInvoiceApplyBiz {

    /**
     * 批量申请开票
     * @param invoiceApplyAddList
     */
    void batchApplyInvoice(List<InvoiceApplyAddDTO> invoiceApplyAddList);

    /**
     * 单条申请开票
     * @param invoiceApplyAddDTO
     */
    void singleApplyInvoice(InvoiceApplyAddDTO invoiceApplyAddDTO);

    /**
     * 批量开票(用于定时任务)
     */
    void batchInvoice();

    /**
     * 单笔开票
     * @param sendInvoiceBean
     */
    void singleInvoice(SendInvoiceBean sendInvoiceBean);

    /**
     * 审核发票申请
     * @param auditInvoiceApplyDTO [description]
     */
    void auditInvoiceApply(AuditInvoiceApplyDTO auditInvoiceApplyDTO);

    /**
     * 红冲发票申请
     * @param revokeInvoiceApplyDTO [description]
     */
    void revokeInvoiceApply(RevokeInvoiceApplyDTO revokeInvoiceApplyDTO);

}
