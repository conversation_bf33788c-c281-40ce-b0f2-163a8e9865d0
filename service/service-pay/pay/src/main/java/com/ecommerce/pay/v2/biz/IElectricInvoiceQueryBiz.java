package com.ecommerce.pay.v2.biz;

import com.ecommerce.common.result.PageData;
import com.ecommerce.pay.api.v2.dto.electricInvoiceAccount.PageQuery;
import com.ecommerce.pay.api.v2.dto.invoice.InvoiceApplyBizListDTO;
import com.ecommerce.pay.api.v2.dto.invoice.InvoiceApplyBizListQueryDTO;
import com.ecommerce.pay.api.v2.dto.invoice.InvoiceApplyDetailDTO;
import com.ecommerce.pay.api.v2.dto.invoice.InvoiceApplyListDTO;
import com.ecommerce.pay.api.v2.dto.invoice.InvoiceApplyListQueryDTO;
import com.ecommerce.pay.api.v2.dto.invoice.StatistcsInvoiceApplyDTO;
import com.ecommerce.pay.api.v2.dto.invoice.StatistcsInvoiceApplyResultDTO;

/**
 *  ElectricInvoiceQueryBiz
 *
 * <AUTHOR>
 */
public interface IElectricInvoiceQueryBiz {

    /**
     * 分页查询开票申请列表  原型中的：开票管理 （卖家/买家）
     * @param  pageQuery [description]
     * @return           [description]
     */
    PageData<InvoiceApplyListDTO> queryInvoiceApplyList(PageQuery<InvoiceApplyListQueryDTO> pageQuery);

    /**
     * 获取发票申请详情 发票明细-详情 （除了对账单部分
     * @param  applyId [description]
     * @return         [description]
     */
    InvoiceApplyDetailDTO queryInvoiceApplyDetail(String applyId);

    /**
     * 分页查询发票业务明细列表 （查对账单， 条件暂时只有applyId和分页信息
     * @param  pageQuery [description]
     * @return         [description]
     */
    PageData<InvoiceApplyBizListDTO> queryInvoiceApplyBizList(PageQuery<InvoiceApplyBizListQueryDTO> pageQuery);

    /**
     * 统计发票申请数据 （电子发票账户 -》 已开户 的右上角
     * @param statistcsInvoiceApplyDTO [description]
     */
    StatistcsInvoiceApplyResultDTO statistcsInvoiceApply(StatistcsInvoiceApplyDTO statistcsInvoiceApplyDTO);

}
