package com.ecommerce.pay.v2.biz;

import com.ecommerce.pay.v2.dao.vo.GneteSplitRuleSett;

import java.util.Collection;
import java.util.Map;

public interface IGneteSplitRuleBiz {

    /**
     * 分账规则同步
     */
    void syncSplitRule();

    /**
     * 根据分账方钱包Id查询分账规则
     * @param walletIdList  分账方钱包Id集合
     * @param bizType 是否担保支付
     */
    Map<String, GneteSplitRuleSett> findRuleByProfitSharingWalletId(Collection<String> walletIdList, String bizType);
}
