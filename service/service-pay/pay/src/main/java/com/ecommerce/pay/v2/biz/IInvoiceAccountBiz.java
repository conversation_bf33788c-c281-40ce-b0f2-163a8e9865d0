package com.ecommerce.pay.v2.biz;

import com.ecommerce.common.result.PageData;
import com.ecommerce.common.service.IBaseBiz;
import com.ecommerce.pay.api.v2.dto.electricInvoiceAccount.*;
import com.ecommerce.pay.v2.dao.vo.InvoiceAccount;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: 卖家开票信息账户表
 * @date 25/04/2019 17:31
 */
public interface IInvoiceAccountBiz extends IBaseBiz<InvoiceAccount> {

    /**
     * 录入卖家开票账户
     */
    String enterInvoiceAccount(SaveInvoiceAccountDTO saveInvoiceAccountDTO);

    /**
     * 编辑卖家开票账户
     */
    void editInvoiceAccount(SaveInvoiceAccountDTO saveInvoiceAccountDTO);

    /**
     * 删除卖家开票账户
     */
    void deleteInvoiceAccount(DeleteInvoiceAccountDTO deleteInvoiceAccountDTO);

    /**
     * 获取发票账户详情
     * @param  sellerId [description]
     * @return         [description]
     */
    InvoiceAccount queryInvoiceAccountDetail(String sellerId,Boolean needToken);

    /**
     * 批量查询卖家发票账户
     */
    Map<String, InvoiceAccountDTO> queryInvoiceAccountStatus(List<String> sellerIdList);

    /**
     * 分页查询开票账户列表
     */
    PageData<InvoiceAccountListDTO> queryInvoiceAccountList(PageQuery<InvoiceAccountQueryDTO> pageQuery);
}
