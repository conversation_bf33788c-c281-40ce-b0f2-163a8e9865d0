package com.ecommerce.pay.v2.biz;

import com.ecommerce.common.result.PageData;
import com.ecommerce.pay.api.v2.dto.electricInvoiceAccount.PageQuery;
import com.ecommerce.pay.api.v2.dto.invoice.InvoiceApplyBizListDTO;
import com.ecommerce.pay.api.v2.dto.invoice.InvoiceApplyBizListQueryDTO;

/**
 * <AUTHOR>
 * @created 14:19 07/05/2019
 * @description
 */
public interface IInvoiceApplyItemBiz {

    PageData<InvoiceApplyBizListDTO> pageInvoiceApplyItemByBizNo(PageQuery<InvoiceApplyBizListQueryDTO> pageQuery);
}
