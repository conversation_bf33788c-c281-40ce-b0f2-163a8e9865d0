package com.ecommerce.pay.v2.biz;

import com.ecommerce.pay.api.v2.dto.invoice.InvoiceInfoDTO;


/**
 * <AUTHOR>
 * @created 9:47 07/05/2019
 * @description
 */
public interface IInvoiceInfoBiz {

    /**
     * 根据发票号码查询
     * @param invoiceNum
     * @return
     */
    InvoiceInfoDTO findByInvoiceNum(String invoiceNum);

    /**
     * 根据请求号查询
     * @param applyId
     * @return
     */
    InvoiceInfoDTO findByApplyId(String applyId);
}
