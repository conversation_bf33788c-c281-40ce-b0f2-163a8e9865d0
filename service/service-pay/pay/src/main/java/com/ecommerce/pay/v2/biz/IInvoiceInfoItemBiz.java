package com.ecommerce.pay.v2.biz;

import com.ecommerce.common.service.IBaseBiz;
import com.ecommerce.pay.api.v2.dto.invoice.InvoiceInfoItemDTO;
import com.ecommerce.pay.v2.dao.vo.InvoiceInfoItem;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 发票申请明细表
 * @date 26/04/2019 14:29
 */
public interface IInvoiceInfoItemBiz extends IBaseBiz<InvoiceInfoItem> {

    /**
     * 根据info_id查找
     * @param infoId
     * @return
     */
    List<InvoiceInfoItemDTO> findByInfoId(String infoId);
}
