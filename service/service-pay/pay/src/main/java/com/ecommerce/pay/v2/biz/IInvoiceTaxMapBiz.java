package com.ecommerce.pay.v2.biz;

import com.ecommerce.common.service.IBaseBiz;
import com.ecommerce.pay.v2.dao.vo.InvoiceTaxMap;

/**
 * <AUTHOR>
 * @description: 商品税号映射表
 * @date 26/04/2019 10:50
 */
public interface IInvoiceTaxMapBiz extends IBaseBiz<InvoiceTaxMap> {

    /**
     * 通过商品分类查询税率
     * @param itemCategory
     * @return
     */
    public InvoiceTaxMap findTaxRateByItemCategory(String itemCategory);

    /**
     * 通过商品code查询税率
     * @param itemCode
     * @return
     */
    public InvoiceTaxMap findTaxRateByItemCode(String itemCode);

}
