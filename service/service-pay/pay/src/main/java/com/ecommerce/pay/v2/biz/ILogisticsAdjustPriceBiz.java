package com.ecommerce.pay.v2.biz;

import java.util.List;

import com.ecommerce.common.result.PageData;
import com.ecommerce.pay.api.v2.dto.adjust.AdjustPricePayResultDTO;
import com.ecommerce.pay.api.v2.dto.logisticsAdjust.*;
import com.ecommerce.pay.v2.dao.vo.LogisticsAdjustPrice;
import com.ecommerce.pay.v2.dao.vo.LogisticsAdjustPriceItem;

public interface ILogisticsAdjustPriceBiz {

	/**
	 * 分页查询物流调价列表
	 * @param pageQuery
	 * @return
	 */
	public PageData<LogisticsAdjustPriceQueryDTO> pageLogisticsAdjustPrice(
			PageQuery<LogisticsAdjustPriceQueryDTO> pageQuery);

	/**
	 * 查询物流调价用户列表
	 * @param queryDTO
	 * @return
	 */
	public List<LogisticsAdjustPriceMemberDTO> queryLogisticsAdjustPriceMember(LogisticsAdjustPriceQueryDTO queryDTO);

	public LogisticsAdjustPriceCalculateDTO calculateLogistticsAdjustPrice(LogisticsAdjustPriceCalculateDTO dto);

	/**
	 * 保存
	 * @param dto
	 */
	public void addLogistticsAdjustPrice(LogisticsAdjustPriceDTO dto);

	/**
	 * 模糊查询物流调价名称
	 * @param adjustName
	 * @param memberId
	 * @return
	 */
	public List<String> fuzzyQueryAdjustName(String adjustName, String memberId);

	/**
	 * 根据物流调价ID查询用户列表
	 * @param adjustId
	 * @return
	 */
	public LogisticsAdjustPriceDTO queryAdjustPriceMemberListByAdjustId(String adjustId);

	/**
	 * 根据调价用户ID查询所有调价运单明细
	 * @param adjustMeberId
	 * @return
	 */
	public List<LogisticsAdjustPriceItemDTO> queryAdjustPriceItemListByAdjustMeberId(String adjustMeberId);

	/**
	 * 修改运单明细表状态
	 * @param logisticsAdjustPriceItem
	 */
	void updateAdjustItemStatusById(LogisticsAdjustPriceItem logisticsAdjustPriceItem);

	/**
	 * 根据条件获取物流调价列表
	 * @param logisticsAdjustPrice
	 * @return
	 */
	public List<LogisticsAdjustPriceDTO> queryLogisticsAdjustPrice(LogisticsAdjustPrice logisticsAdjustPrice);

	/**
	 * ERP调价异步回调
	 * @param resultDTO
	 */
	void processErpLogisticsAdjustResult(AdjustPricePayResultDTO resultDTO);

	List<LogisticsAdjustPriceItemDTO> queryLogisticsAdjustPriceItemList(LogisticsAdjustPriceMemberDTO queryDTO);

	/**
	 *根据adjustMemberId删除物流调价用户
	 * @param queryDTO
	 * @return
	 */
	Void deleteLogisticsAdjustPriceMemberByAdjustMemberId(LogisticsAdjustPriceMemberDTO queryDTO);

	/**
	 * 根据adjustItemId删除物流调价运单
	 * @param queryDTO
	 * @return
	 */
	Void deleteLogisticsAdjustPriceItemByAdjustItemId(LogisticsAdjustPriceItemDTO queryDTO);

	/**
	 * 根据adjustId查询物流调价运单明细
	 * @param adjustId
	 * @return
	 */
	List<LogisticsAdjustPriceItem> queryAdjustPriceItemListByAdjustId(String adjustId);

	/**
	 * 根据Id查询
	 * @param adjustPrice
	 */
	void updateAdjustPriceById(LogisticsAdjustPriceDTO adjustPrice);

	/**
	 *  获取最新物流运单调价明细
	 * @param queryDTO
	 * @return
	 */
	LogisticsAdjustPriceItemDTO getNewestLogisticsAdjustPriceItem(LogisticsAdjustPriceItemDTO queryDTO);
}
