package com.ecommerce.pay.v2.biz;

import com.ecommerce.common.service.IBaseBiz;
import com.ecommerce.pay.api.v2.dto.GneteAggregationChannelCloseDTO;
import com.ecommerce.pay.api.v2.dto.GneteAggregationChannelOpenDTO;
import com.ecommerce.pay.api.v2.dto.MemberChannelDTO;
import com.ecommerce.pay.api.v2.enums.ChannelCodeEnum;
import com.ecommerce.pay.api.v2.enums.ChannelPaymentTypeEnum;
import com.ecommerce.pay.api.v2.enums.ClientType;
import com.ecommerce.pay.api.v2.enums.MemberChannelStatusEnum;
import com.ecommerce.pay.v2.dao.vo.MemberChannel;
import com.github.pagehelper.Page;

import java.util.List;

public interface IMemberChannelBiz extends IBaseBiz<MemberChannel> {

	/**
	 * 保存支付渠道, 生成主键并返回保存的对象
	 *
	 * @param memberChannelDTO
	 * @param operator
	 * @return MemberChannelDTO
	 */
	MemberChannelDTO insertMemberChannel(MemberChannelDTO memberChannelDTO, String operator);

	/**
	 * 更新会员渠道信息
	 *
	 * @param memberChannelDTO
	 * @param operator
	 * @return
	 */
	MemberChannelDTO updateMemberChannel(MemberChannelDTO memberChannelDTO, String operator);


	/**
	 * 获取支付渠道信息
	 * 只查询opend
	 * @param memberId
	 * @return
	 */
	List<MemberChannelDTO> findByMemberIdAndChannelCode(String memberId, String channelCode);
	List<MemberChannelDTO> findByMemberIdAndChannelCode(String memberId, String channelCode,List<String> memberChannelStatus);
	List<MemberChannelDTO> findByMemberIdAndChannelType(String memberId, String channelType);

	/**
	 * 获取支付渠道信息 MemberChannelStatusEnum字段不作为查询条件
	 * @param memberId
	 * @param channelCode
	 * @return
	 */
	List<MemberChannelDTO> findByMemberIdAndChannelCode2(String memberId, String channelCode);

	/**
	 * 获取支付渠道信息
	 * 只查询opend
	 * @param memberId
	 * @return
	 */
	MemberChannelDTO findByMemberIdAndChannelCode(String memberId, String channelCode, ChannelPaymentTypeEnum channelPaymentType);

	/**
	 * 获取支付渠道信息
	 * 查询所有状态的
	 * @param memberId
	 * @return
	 */
	MemberChannelDTO findByMemberIdAndType(String memberId, String channelCode);


	/**
	 * 获取支付渠道信息
	 *
	 * @param memberChannelId
	 * @return
	 */
	MemberChannelDTO findByMemberChannelId(String memberChannelId);


	/**
	 * 获取会员支持的支付渠道
	 *
	 * @return
	 */
	List<MemberChannelDTO> getMemberAvailChannels(String memberId, ClientType client);

	/**
	 * 获取会员支持的支付渠道
	 *
	 * @return
	 */
	List<MemberChannelDTO> getMemberChannelByMemberId(String memberId);

	/**
	 * 根据mchNo查询mchkey
	 * @param mchNo
	 * @return
	 */
	String getMchkeyByMchNo(String mchNo);

	/**
	 * 分页查询开通的支付渠道
	 * @param memberChannelDTO
	 */
	Page<MemberChannelDTO> pageMemberChannelDTO(MemberChannelDTO memberChannelDTO);

	/**
	 * 通过买家和卖家ID查询渠道信息
	 * @param memberId
	 * @param payeeMemberId
	 * @param channelCodeEnum
	 * @return
	 */
	MemberChannelDTO findMemberChannelByPayee(String memberId, String payeeMemberId, ChannelCodeEnum channelCodeEnum);

	/**
	 * 批量新增授信渠道
	 */
	void batchAddCreditMemberChannel(List<MemberChannelDTO> memberChannelDTOS,String operatorId);

	/**
	 * 批量添加或更新授信渠道
	 * @param memberChannelDTOS
	 */
	void batchAddOrUpdateCreditMemberChannel(List<MemberChannelDTO> memberChannelDTOS);

    /**
     * @Title: findMemberChannelDTOList
     * 查询20个有关系会员中没有开通授信支付渠道的会员集合
     * @param payeeMemberId 卖家ID
     * @param payerMemberIds 20条有关系的会员集合
     * @return List<MemberChannelDTO> 会员渠道集合
     */
    List<MemberChannelDTO> findMemberChannelDTOList(String payeeMemberId , List<String> payerMemberIds);

    /**
     * @Title: findMemberChannelDTOList
     * @return List<MemberChannelDTO> 会员渠道集合
     */
    List<MemberChannelDTO> findMemberChannelDTO(MemberChannelDTO memberChannelDTO);

	/**
	 * 同步会员信息
	 * @param memberChannel
	 */
	void updateByMemberId(MemberChannel memberChannel);

	/**
	 * 同步会员信息
	 * @param memberChannel
	 */
	void updateByPayeeMemberId(MemberChannel memberChannel);

	/**
	 * 设置小额免密
	 * @param memberChannelId
	 * @param freePassword
	 * @param operator
	 */
	void freePasswordSetting(String memberChannelId, Boolean freePassword, String operator);

	/**
	 * 是否支持免密支付
	 * @param memberChannelDTO
	 * @return
	 */
	boolean checkMemberChannelPasswordFree(MemberChannelDTO memberChannelDTO);

	/**
	 * 设置聚合支付
	 * @param memberChannelId
	 * @param support
	 * @param operator
	 */
	void aggregationPaySetting(String memberChannelId, Boolean support, String operator);

	/**
	 * 是否支持聚合支付
	 * @param memberChannelDTO
	 * @return
	 */
	boolean checkMemberChannelAggregation(MemberChannelDTO memberChannelDTO);
	/**
	 * 是否支持聚合支付
	 * @param memberId
	 * @return
	 */
	boolean checkMemberChannelAggregation(String memberId);

	/**
	 * 当前会员是否可以开通聚合支付
	 * @param memberId
	 * @return
	 */
	boolean checkMemberChannelAllowOpenAggregation(String memberId);

	/**
	 * 开通银联聚合支付渠道 微信、支付宝、C扫B
	 */
	boolean openGneteAggregationChannel(GneteAggregationChannelOpenDTO dto);

	/**
	 * 关闭银联聚合支付渠道
	 */
	boolean closeGneteAggregationChannel(GneteAggregationChannelCloseDTO dto);
	/**
	 * 银联注册企业好，存备注字段1
	 * @param registerNo
	 * @return
	 */
	MemberChannel findByRegisterNo(String registerNo);

	void updateMemberChannelStatus(String memberChannelId, MemberChannelStatusEnum statusEnum,String operatorId);


}
