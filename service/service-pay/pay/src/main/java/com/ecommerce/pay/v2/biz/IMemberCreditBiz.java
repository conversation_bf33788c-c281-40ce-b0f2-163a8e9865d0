package com.ecommerce.pay.v2.biz;

import com.ecommerce.pay.api.v2.dto.MemberCreditDTO;
import com.ecommerce.pay.api.v2.dto.MemberCreditQueryDTO;
import com.ecommerce.pay.v2.dao.vo.MemberCredit;
import com.github.pagehelper.Page;

import java.util.List;

public interface IMemberCreditBiz
{
    /**
     * 插入买家授信
     * @param memberCreditDTOs
     */
    void insertMemberCreditInfos(List<MemberCreditDTO> memberCreditDTOs);

    /**
     * 更新授信
     * @param memberCreditDTOs
     */
    void updateMemberCreditInfos(List<MemberCreditDTO> memberCreditDTOs);

    /**
     * 删除授信
     * @param idList
     * @param operator
     */
    void deleteMemberCreditInfo(List<String> idList, String operator);

    /**
     * 分页获取买家授信记录列表
     * @param memberCreditQueryDTO
     * @return
     */
    Page<MemberCreditDTO> getMemberCreditListByPage(MemberCreditQueryDTO memberCreditQueryDTO);

    /**
     * @param payerMemberId
     * @param payeeMemberId
     */
    MemberCredit findNextOne(String payerMemberId, String payeeMemberId);

    MemberCredit findPreviousOne(String payerMemberId, String payeeMemberId);
}
