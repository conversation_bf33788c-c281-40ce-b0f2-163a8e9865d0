package com.ecommerce.pay.v2.biz;

import com.ecommerce.pay.api.v2.dto.MemberChannelDTO;
import com.ecommerce.pay.api.v2.dto.MemberCreditRecordDTO;
import com.ecommerce.pay.api.v2.dto.MemberCreditRecordQueryDTO;
import com.ecommerce.pay.api.v2.dto.electricInvoiceAccount.PageQuery;
import com.ecommerce.pay.enums.MemberCreditRecordEnum;
import com.github.pagehelper.Page;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
public interface IMemberCreditRecordBiz
{
    /**
     * 插入一条账单记录
     * @param memberCreditRecordDTO
     */
    void insertMemberCreditRecordInfo(MemberCreditRecordDTO memberCreditRecordDTO);

    void insertMemberCreditRecordByMemberChannel(MemberChannelDTO memberChannelDTO, BigDecimal amount, MemberCreditRecordEnum type, String billNo, String memo, String fileAttach, String operator);

    /**
     * 分页获取账单记录
     * @param pageQuery
     * @return
     */
    Page<MemberCreditRecordDTO> getMemberCreditRecordListByPage(PageQuery<MemberCreditRecordQueryDTO> pageQuery);
}
