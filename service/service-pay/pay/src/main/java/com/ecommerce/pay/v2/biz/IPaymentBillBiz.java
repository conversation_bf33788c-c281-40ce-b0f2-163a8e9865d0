package com.ecommerce.pay.v2.biz;

import com.ecommerce.common.service.IBaseBiz;
import com.ecommerce.pay.api.v2.dto.bill.PaymentBillDTO;
import com.ecommerce.pay.api.v2.dto.query.PaymentBillQueryDTO;
import com.ecommerce.pay.v2.dao.vo.PaymentBill;
import com.github.pagehelper.Page;

import java.util.List;

public interface IPaymentBillBiz extends IBaseBiz<PaymentBill> {

    /**
     * 创建支付单
     * @param paymentBillDTO
     * @return
     */
    void create(PaymentBillDTO paymentBillDTO, String operatorId);

    /**
     * 根据业务ID获取相应类型的未完结的支付单
     * @param srcBizNo
     * @return
     */
    PaymentBillDTO findRecentRecordBySrcBizNo(String srcBizNo);

    /**
     * 根据支付单编号 查询支付单
     * @param paymentBillNo
     * @return
     */
    PaymentBillDTO findByPaymentBillNo(String paymentBillNo);
    String findExtBizNoByPaymentBillNo(String paymentBillNo);

    /**
     * 根据订单编号 查询支付单列表
     * @param orderNo
     * @return
     */
    List<PaymentBillDTO> findByOrderNo(String orderNo);

    /**
     * 查询当前订单银联支付第一次成功的记录
     */
    PaymentBill findSuccessFirstByOrderNoAndChannel(String orderNo,String channelCode);
    /**
     * 根据支付单id 查询支付单
     * @param paymentBillId
     * @return
     */
    PaymentBillDTO findByPaymentBillId(String paymentBillId);

    /**
     * 更改支付单
     * @param paymentBillDTO
     * @return
     */
    PaymentBillDTO updatePaymentBill(PaymentBillDTO paymentBillDTO, String operatorId);

    /**
     * 更改支付单状态
     * @param billId
     * @param status
     * @return
     */
    void updateStatus(String billId, String status, String operatorId);


    /**
     * 按条件分页查询支付单
     * @param queryDTO
     * @return
     */
    Page<PaymentBillDTO> pagePaymentBill(PaymentBillQueryDTO queryDTO);

	/**
	 * 查询未回调的支付单
	 * @param timeStep
	 * @return
	 */
	List<PaymentBill> findPaymentBillDTOWithNoCallback(long timeStep);

	/**
	 * @param paymentBillNo
	 * @return
	 */
	Boolean findDealByPaymentBillNo(String paymentBillNo);

	/**
	 * @param paymentBillNo
	 * @return
	 */
	void updateDealByPaymentBillNo(String paymentBillNo, Boolean deal);

}
