package com.ecommerce.pay.v2.biz;

import com.ecommerce.common.service.IBaseBiz;
import com.ecommerce.pay.api.v2.dto.bill.PaymentBillDTO;
import com.ecommerce.pay.api.v2.dto.bill.PaymentBillDetailDTO;
import com.ecommerce.pay.api.v2.dto.PaymentRequirementDetailDTO;
import com.ecommerce.pay.v2.dao.vo.PaymentBillDetail;

import java.util.List;

/**
 * <AUTHOR>
 * @created 14:01 02/03/2019
 * @description
 */
public interface IPaymentBillDetailBiz extends IBaseBiz<PaymentBillDetail> {

    List<PaymentBillDetailDTO> findByPaymentBillNo(String paymentBillNo);

    PaymentBillDetailDTO findByDetailCode(String detailCode);

    void updateByPaymentBill(PaymentBillDTO paymentBillDTO, String operatorId);

    void updateStatusByPaymentBillNo(String paymentBillNo, String status, String operatorId);

    void updateByDTO(PaymentBillDetailDTO paymentBillDetailDTO, String operatorId);

    void save(PaymentBillDetailDTO detailDTO, String operatorId);

    PaymentBillDetailDTO convert2DTO(PaymentRequirementDetailDTO requirementDetailDTO, PaymentBillDTO paymentBillDTO);

    void deleteDb(String detailId);
}
