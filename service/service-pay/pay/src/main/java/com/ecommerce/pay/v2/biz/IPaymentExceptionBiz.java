package com.ecommerce.pay.v2.biz;

import com.ecommerce.pay.api.v2.dto.PaymentExceptionDTO;
import com.ecommerce.pay.api.v2.dto.electricInvoiceAccount.PageQuery;
import com.github.pagehelper.PageInfo;

import java.util.Collection;

/**
 * <AUTHOR>
 * @created 10:47 24/09/2019
 * @description TODO
 */
public interface IPaymentExceptionBiz {

    /**
     *
     * @param dto
     */
    void create(PaymentExceptionDTO dto, String operatorId);

    /**
     *
     * @param dto
     */
    void update(PaymentExceptionDTO dto, String operatorId);

    /**
     *
     * @param id
     * @return
     */
    PaymentExceptionDTO findById(String id);

    /**
     * 更新异常为成功
     */
    void splitSuccess(Collection<String> objIdList);

    /**
     *
     * @param query
     * @return
     */
    PageInfo<PaymentExceptionDTO> pageByObjectId(PageQuery<PaymentExceptionDTO> query);
}
