package com.ecommerce.pay.v2.biz;

import java.util.List;

import com.ecommerce.common.service.IBaseBiz;
import com.ecommerce.pay.api.v2.dto.BizConfigDTO;
import com.ecommerce.pay.api.v2.dto.PaymentBillInfo;
import com.ecommerce.pay.api.v2.dto.PaymentNotifyDTO;
import com.ecommerce.pay.api.v2.dto.PaymentRequirementDTO;
import com.ecommerce.pay.v2.dao.vo.BizConfig;

public interface IPaymentIntegrationBiz extends IBaseBiz<BizConfig> {
	/**
	 * 根据数据库配置调用支付接入方的接口，获取支付需求信息
	 * 
	 * @param bizId
	 * @param operator
	 * @return
	 */
	PaymentRequirementDTO getPaymentRequirement(String bizId, String paymentBizCode, String operator);

	/**
	 * 根据数据库配置，调用支付接入方的接口，获取支付需求信息
	 * 
	 * @param bizId
	 * @param paymentBizCode
	 * @param operator
	 * @return
	 */
	List<String> getBizPaymentChannel(String bizId, String paymentBizCode, String operator);

	/**
	 * 支付结果回调接口
	 * 
	 * @param paymentNotifyDTO
	 */
	public void paymentResultCallback(PaymentNotifyDTO paymentNotifyDTO);

	/**
	 * 调用支付业务接入放 更新支付单
	 * 
	 * @param paymentBillInfo
	 */
	public void updatePaymentBillInfo(PaymentBillInfo paymentBillInfo);

	/**
	 * 根据code查询业务配置
	 * @param code
	 * @return
	 */
	BizConfigDTO findByCode(String code);
}
