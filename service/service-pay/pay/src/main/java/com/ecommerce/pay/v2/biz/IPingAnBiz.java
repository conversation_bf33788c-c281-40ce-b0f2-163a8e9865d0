package com.ecommerce.pay.v2.biz;

import java.util.Map;

public interface IPingAnBiz
{
    /**
     * 会员补录法人信息-下发短信验证码
     * @param memberId
     * @param legalName
     * @param legalCertificateCode
     * @param clickTime
     * @param remoteIp
     * @param macAddress
     * @param channel
     * @return
     */
    public Map<String, String> clearingCorpInfoSendMsgCode(String memberId, String legalName, String legalCertificateCode, String clickTime, String remoteIp, String macAddress, String channel);

    /**
     * 会员补录法人信息-回填短信验证码
     * @param memberId
     * @param checkCode
     * @return
     */
    public Map<String, String> clearingCorpInfoCheckMsgCode(String memberId, String checkCode);

    /**
     * 登记行为记录信息
     * @param memberId
     * @param functionFlag
     * @param clickTime
     * @param remoteIp
     * @param macAddress
     * @param channel
     */
    public Map<String, Object> registerBehaviorRecordInfo(String memberId, String functionFlag, String clickTime, String remoteIp, String macAddress, String channel);
}
