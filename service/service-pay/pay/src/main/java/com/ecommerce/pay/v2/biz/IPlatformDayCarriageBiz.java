package com.ecommerce.pay.v2.biz;

import com.ecommerce.common.service.IBaseBiz;
import com.ecommerce.pay.api.v2.dto.driver.PlatformSummaryInfoDTO;
import com.ecommerce.pay.api.v2.dto.driver.PlatformSummaryInfoQueryDTO;
import com.ecommerce.pay.api.v2.dto.electricInvoiceAccount.PageQuery;
import com.ecommerce.pay.v2.dao.vo.PlatformDayCarriage;
import com.github.pagehelper.PageInfo;

import java.util.List;

public interface IPlatformDayCarriageBiz extends IBaseBiz<PlatformDayCarriage> {

    List<PlatformDayCarriage> findByIds(List<String> ids);

    List<PlatformDayCarriage> findByPayNumber(String payNumber);

    int updatePayStatusInPayIng(List<String> ids, String operator, String payNumber,String tradeBillNo);

    PlatformDayCarriage findByDay(String day);

    PageInfo<PlatformSummaryInfoDTO> pageDriverCostSummarizeToPlatform(PageQuery<PlatformSummaryInfoQueryDTO> dto);
}
