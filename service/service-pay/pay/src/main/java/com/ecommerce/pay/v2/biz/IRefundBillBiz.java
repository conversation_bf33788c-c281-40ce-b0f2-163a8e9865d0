package com.ecommerce.pay.v2.biz;

import com.ecommerce.pay.api.v2.dto.bill.RefundBillDTO;
import com.ecommerce.pay.api.v2.dto.bill.RefundBillDetailDTO;
import com.ecommerce.pay.v2.dao.vo.RefundBill;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @created 14:14 04/09/2019
 * @description TODO
 */
public interface IRefundBillBiz extends IBaseBillBiz<RefundBillDTO> {

    /**
     * 查询支付单已退款数量
     * @param paymentNo
     * @return
     */
    Integer findCountByRelatePaymentNo(String paymentNo);

    /**
     * 根据订单号查找
     * @param orderNo
     * @return
     */
    List<RefundBillDTO> findByOrderNo(String orderNo);

    Optional<RefundBillDTO> findByExtBizNo(String extBizNo);

    void updateGneteReceivableTransInfo(RefundBillDetailDTO refundBillDetailDTO);

    /**
     * 最多查询1000条
     * @param channelCodeEnumList
     * @param timeStep
     * @return
     */
    List<RefundBill> findWithNoCallback(List<String> channelCodeEnumList, long timeStep);
}
