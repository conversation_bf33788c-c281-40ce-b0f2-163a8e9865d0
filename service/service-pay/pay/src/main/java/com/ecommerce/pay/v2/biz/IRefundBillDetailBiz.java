package com.ecommerce.pay.v2.biz;

import com.ecommerce.pay.api.v2.dto.bill.RefundBillDetailDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @created 14:37 04/09/2019
 * @description TODO
 */
public interface IRefundBillDetailBiz extends IBaseBillBiz<RefundBillDetailDTO> {

    /**
     * 根据退款单号查询子单据
     * @param refundNo
     * @return
     */
    List<RefundBillDetailDTO> findRefundNo(String refundNo);

    void updateGneteReceivableTransInfo(RefundBillDetailDTO refundBillDetailDTO);

}
