package com.ecommerce.pay.v2.biz;

import com.ecommerce.pay.api.v2.dto.bill.SplitPayBillDTO;
import com.ecommerce.pay.v2.dao.mapper.SplitPayBillMapper;
import com.ecommerce.pay.v2.dao.vo.SplitPayBill;

import java.util.List;

/**
 * <AUTHOR>
 * @created 19:07 04/09/2019
 * @description TODO
 */
public interface ISplitPayBillBiz extends IBaseBillBiz<SplitPayBillDTO> {

    /**
     * 根据订单号查找
     * @param orderNo
     * @return
     */
    List<SplitPayBillDTO> findByOrderNo(String orderNo);

    SplitPayBillDTO findBySettOrderNo(String settOrderNo);

    /**
     * 是否有分账成功的记录(是否已分账)
     */
    boolean hasSplit(String orderNo);

    SplitPayBillMapper mapper();

    /**
     * 查询未回调的分账单 limit 1000
     */
    List<SplitPayBill> findWithNoCallback(List<String> channelCodeEnumList, long timeStep);

}
