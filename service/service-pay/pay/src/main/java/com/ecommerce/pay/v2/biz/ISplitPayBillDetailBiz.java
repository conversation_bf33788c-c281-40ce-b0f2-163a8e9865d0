package com.ecommerce.pay.v2.biz;

import com.ecommerce.pay.api.v2.dto.bill.SplitPayBillDetailDTO;
import com.ecommerce.pay.v2.dao.mapper.SplitPayBillDetailMapper;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @created 19:08 04/09/2019
 * @description TODO
 */
public interface ISplitPayBillDetailBiz extends IBaseBillBiz<SplitPayBillDetailDTO> {

    List<SplitPayBillDetailDTO> findBySplitPayBillNo(String splitPayBillNo);

    boolean hasSuccess(Set<String> splitPayBillNoSet);

    SplitPayBillDetailMapper mapper();
}
