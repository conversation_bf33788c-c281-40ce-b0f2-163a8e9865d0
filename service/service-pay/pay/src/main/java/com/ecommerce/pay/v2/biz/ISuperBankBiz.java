package com.ecommerce.pay.v2.biz;

import com.ecommerce.pay.api.v2.dto.BaseBankDTO;
import com.ecommerce.pay.api.v2.dto.SuperBankDTO;
import com.github.pagehelper.PageInfo;

import java.util.List;


public interface ISuperBankBiz {
	/**
	 * 模糊搜索银行
	 * 
	 * @return
	 */
	List<BaseBankDTO> searchSuperBankLimit(String bankName);

	PageInfo<SuperBankDTO> pageSuperBank(SuperBankDTO superBankDTO);

	/**
	 * 添加
	 */
	void addSuperBank(SuperBankDTO superBankDTO);

	/**
	 * 删除
	 * @param bankNo
	 */
	void deleteSuperBank(String bankNo);
}
