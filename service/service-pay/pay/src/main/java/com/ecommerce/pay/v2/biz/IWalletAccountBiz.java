package com.ecommerce.pay.v2.biz;

import com.ecommerce.pay.api.v2.dto.WalletAccountDTO;
import com.github.pagehelper.PageInfo;
import org.springframework.data.domain.Pageable;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @created 20:17 02/09/2019
 * @description TODO
 */
public interface IWalletAccountBiz {
    /**
     * 创建账户
     * @param walletAccountDTO
     * @return
     */
    void createAccount(WalletAccountDTO walletAccountDTO, String operatorId);

    /**
     * 创建账户
     * @param walletAccountDTO
     * @return
     */
    void createAccountBySync(WalletAccountDTO walletAccountDTO, String operatorId);

    /**
     * 关闭账户
     * @param walletAccountId
     * @return
     */
    void closeAccount(String walletAccountId, String operatorId);

    /**
     * 分页查询
     * @param pageable
     * @return
     */
    PageInfo<WalletAccountDTO> pageWalletAccount(Pageable pageable);

    /**
     * 关闭账户
     * @param memberId
     * @param type
     */
    void closeAccountByMemberId(String memberId, String type, String operatorId);

    /**
     * 根据memberId查询
     * @param memberId
     * @return
     */
    List<WalletAccountDTO> findByMemberId(String memberId);

    /**
     * 根据 memberChannelId 查询
     * @param memberChannelId
     * @return
     */
    Optional<WalletAccountDTO> findByMemberChannelId(String memberChannelId);

    /**
     * 根据 accountNo 查询
     * @param accountNo
     * @return
     */
    Optional<WalletAccountDTO> findByNo(String accountNo);

    /**
     * 根据memberId查询
     * @param memberId
     * @return
     */
    Optional<WalletAccountDTO> findByMemberId(String memberId, String walletType);

    /**
     * 更新
     * @param walletAccountDTO
     * @param operatorId
     */
    void update(WalletAccountDTO walletAccountDTO, String operatorId);

    /**
     * 更新
     * @param walletAccountDTO
     * @param operatorId
     */
    void updateBalance(WalletAccountDTO walletAccountDTO, String operatorId);

    /**
     * 增加余额
     * @param bigDecimal
     */
    void incrementBalance(String walletAccountId, BigDecimal bigDecimal);

    /**
     * 增加余额减少预收额
     * @param bigDecimal
     */
    void incrementBalanceAndDecrementAdvanceBalance(String walletAccountId, BigDecimal bigDecimal);

    /**
     * 增加冻结余额 减少 余额
     * @param bigDecimal
     */
    void incrementFreezeBalanceAndDecrementBalance(String walletAccountId, BigDecimal bigDecimal);

    /**
     * 减少余额和可提现余额
     * @param bigDecimal
     */
    void decrementBalance(String walletAccountId, BigDecimal bigDecimal);

    /**
     * 减少冻结余额并增加可用余额
     * @param bigDecimal
     */
    void decrementFreezeBalanceAndIncrementBalance(String walletAccountId, BigDecimal bigDecimal);

    /**
     * 减少冻结余额和预收金额并增加可用余额
     * @param bigDecimal
     */
    void decrementFreezeBalanceAdvanceBalanceAndIncrementBalance(String walletAccountId, BigDecimal bigDecimal);

    /**
     * 增加冻结余额
     * @param bigDecimal
     */
    void incrementFreezeBalance(String walletAccountId, BigDecimal bigDecimal);

    /**
     * 减少冻结余额
     * @param bigDecimal
     */
    void decrementFreezeBalance(String walletAccountId, BigDecimal bigDecimal);

    /**
     * 减少可提现金额和余额
     * @param bigDecimal
     */
    void decrementCashBalance(String walletAccountId, BigDecimal bigDecimal);

    /**
     * 增加预收余额
     * @param bigDecimal
     */
    void incrementAdvanceAmount(String walletAccountId, BigDecimal bigDecimal);

    /**
     * 减少预收余额
     * @param bigDecimal
     */
    void decrementAdvanceAmount(String walletAccountId, BigDecimal bigDecimal);

    /**
     * 增加冻结余额 增加预收余额
     * @param bigDecimal
     */
    void incrementFreezeBalanceAndAdvanceAmount(String walletAccountId, BigDecimal bigDecimal);

    /**
     * 对账余额
     */
    void reconciliation();

}
