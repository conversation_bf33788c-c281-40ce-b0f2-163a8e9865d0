package com.ecommerce.pay.v2.biz.impl;

import com.alibaba.fastjson.JSON;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.service.common.bsid.CommonBusinessIdGenerator;
import com.ecommerce.common.service.common.uuid.UUIDGenerator;
import com.ecommerce.common.utils.BeanConvertUtils;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.common.utils.MD5;
import com.ecommerce.logistics.api.dto.adjust.AdjustBillDTO;
import com.ecommerce.logistics.api.dto.adjust.AdjustMemberDTO;
import com.ecommerce.logistics.api.dto.adjust.AdjustPriceResultDTO;
import com.ecommerce.logistics.api.dto.adjust.AdjustQueryDTO;
import com.ecommerce.logistics.api.dto.adjust.AdjustShipBillDTO;
import com.ecommerce.logistics.api.enums.PickingBillTypeEnum;
import com.ecommerce.logistics.api.service.IAdjustPriceService;
import com.ecommerce.pay.api.v2.dto.adjust.AdjustPriceBillDTO;
import com.ecommerce.pay.api.v2.dto.adjust.AdjustPriceBillPayResultDTO;
import com.ecommerce.pay.api.v2.dto.adjust.AdjustPriceDTO;
import com.ecommerce.pay.api.v2.dto.adjust.AdjustPriceDetailDTO;
import com.ecommerce.pay.api.v2.dto.adjust.AdjustPriceExcludeDTO;
import com.ecommerce.pay.api.v2.dto.adjust.AdjustPriceMemberDTO;
import com.ecommerce.pay.api.v2.dto.adjust.AdjustPricePayResultDTO;
import com.ecommerce.pay.api.v2.dto.adjust.AdjustPriceQueryDTO;
import com.ecommerce.pay.api.v2.enums.TransportToolTypeEnum;
import com.ecommerce.pay.v2.biz.IAdjustPriceBiz;
import com.ecommerce.pay.v2.dao.mapper.AdjustPriceDetailMapper;
import com.ecommerce.pay.v2.dao.mapper.AdjustPriceExcludeMapper;
import com.ecommerce.pay.v2.dao.mapper.AdjustPriceMapper;
import com.ecommerce.pay.v2.dao.mapper.AdjustPriceMemberMapper;
import com.ecommerce.pay.v2.dao.vo.AdjustPrice;
import com.ecommerce.pay.v2.dao.vo.AdjustPriceDetail;
import com.ecommerce.pay.v2.dao.vo.AdjustPriceExclude;
import com.ecommerce.pay.v2.dao.vo.AdjustPriceMember;
import com.ecommerce.pay.v2.util.ListUtil;
import com.ecommerce.pay.v2.util.SetUtil;
import com.ecommerce.pay.v2.util.StringUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.page.PageMethod;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class AdjustPriceBiz implements IAdjustPriceBiz
{
    private final static Logger logger = LoggerFactory.getLogger(AdjustPriceBiz.class);
    private final static String DELIVERY_TYPE_FACTORY_PRICE = "100";

    private final UUIDGenerator uuidGenerator;
    private final IAdjustPriceService adjustPriceService;
    private final AdjustPriceMapper adjustPriceMapper;
    private final AdjustPriceMemberMapper adjustPriceMemberMapper;
    private final AdjustPriceDetailMapper adjustPriceDetailMapper;
    private final AdjustPriceExcludeMapper adjustPriceExcludeMapper;
    private final CommonBusinessIdGenerator commonBusinessIdGenerator;

    @Override
    public Page<AdjustPriceDTO> getAdjustPriceList(AdjustPriceQueryDTO query)
    {
        logger.info("getAdjustPriceList: {}", JSON.toJSONString(query));

        int pageNum = query.getPageNum();
        int pageSize = query.getPageSize();

        Condition condition = new Condition(AdjustPrice.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("delFlg", 0);

        if (CsStringUtils.isNotEmpty(query.getMemberId()))
        {
            criteria.andEqualTo("memberId", query.getMemberId());
        }

        if (CsStringUtils.isNotBlank(query.getAdjustName()))
        {
            criteria.andLike("adjustName", "%" + query.getAdjustName() + "%");
        }

        if (CsStringUtils.isNotBlank(query.getAdjustNo()))
        {
            criteria.andLike("adjustNo", query.getAdjustNo());
        }

        if (CsStringUtils.isNotBlank(query.getSaleRegionId()))
        {
            criteria.andEqualTo("saleRegionId", query.getSaleRegionId());
        }

        if (CsStringUtils.isNotBlank(query.getGoodsId()))
        {
            criteria.andEqualTo("goodsId", query.getGoodsId());
        }

        if(query.getShipStartTime() != null)
        {
            criteria.andEqualTo("shipStartTime", query.getShipStartTime());
        }

        if(query.getShipEndTime() != null)
        {
            criteria.andEqualTo("shipEndTime", query.getShipEndTime());
        }

        if(query.getStatus() != null)
        {
            criteria.andEqualTo("status", query.getStatus());
        }

        condition.orderBy("createTime").desc();

        Page<AdjustPrice> page = PageMethod.startPage(pageNum, pageSize).doSelectPage(() -> adjustPriceMapper.selectByCondition(condition));
        Page<AdjustPriceDTO> pageInfo = BeanConvertUtils.convertPage(page, AdjustPriceDTO.class);
        for(AdjustPriceDTO dto : pageInfo.getResult())
        {
            dto.setTransportTypeName(CsStringUtils.isEmpty(dto.getTransportType()) ? "" : TransportToolTypeEnum.valueOfCode(dto.getTransportType()).getDesc());
            dto.setDeliveryTypeName(codeToNameForDeliveryType(dto.getDeliveryType()));
            dto.setBillTypeName(codeToNameForBillType(dto.getBillType()));
        }

        return pageInfo;
    }

    private String codeToNameForDeliveryType(String deliveryType) {
        if (CsStringUtils.isEmpty(deliveryType)) {
            return CsStringUtils.EMPTY;
        }

        if (DELIVERY_TYPE_FACTORY_PRICE.equals(deliveryType)) {
            return "出厂价";
        } else {
            return "到位价";
        }
    }

    private String codeToNameForBillType(Integer billType) {
        if (billType == 0) {
            return CsStringUtils.EMPTY;
        }

        if (billType == 2) {
            return "二票制";
        }
        return "一票制";
    }

    @Override
    public AdjustPriceDTO adjustInfo(String priceAdjustId, boolean excludeMember)
    {
        AdjustPrice adjustPrice = adjustPriceMapper.selectByPrimaryKey(priceAdjustId);
        if(adjustPrice == null || adjustPrice.getDelFlg() == 1)
        {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "查询数据不存在");
        }

        AdjustPriceDTO dto = BeanConvertUtils.convert(adjustPrice, AdjustPriceDTO.class);

        dto.setTransportTypeName(CsStringUtils.isEmpty(dto.getTransportType()) ? "" : TransportToolTypeEnum.valueOfCode(dto.getTransportType()).getDesc());
        dto.setDeliveryTypeName(codeToNameForDeliveryType(dto.getDeliveryType()));
        dto.setBillTypeName(codeToNameForBillType(dto.getBillType()));

        Condition condition = new Condition(AdjustPriceExclude.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("delFlg", 0);
        criteria.andEqualTo("priceAdjustId", priceAdjustId);

        if(excludeMember)
        {
            List<AdjustPriceExclude> excludes = adjustPriceExcludeMapper.selectByCondition(condition);
            dto.setExcludeList(BeanConvertUtils.convertList(excludes, AdjustPriceExcludeDTO.class));
        }

        return dto;
    }

    @Override
    public Page<AdjustPriceMemberDTO> getAdjustPriceMemberList(String priceAdjustId, int pageNum, int pageSize)
    {
        Condition condition = new Condition(AdjustPriceMember.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("delFlg", 0);
        criteria.andEqualTo("priceAdjustId", priceAdjustId);
        condition.orderBy("createTime").desc();


        AdjustPriceDTO priceDTO = adjustInfo(priceAdjustId, true);


        Page<AdjustPriceMember> page =PageMethod.startPage(pageNum, pageSize).doSelectPage(() -> adjustPriceMemberMapper.selectByCondition(condition));
        Page<AdjustPriceMemberDTO> pageInfo = BeanConvertUtils.convertPage(page, AdjustPriceMemberDTO.class);
        for(AdjustPriceMemberDTO dto : pageInfo.getResult())
        {
            dto.setChangeAmount(dto.getAdjustAmount().subtract(dto.getNewestAmount()));
            dto.setStatus(priceDTO.getStatus());
        }

        return pageInfo;
    }

    @Override
    public Page<AdjustPriceDetailDTO> getAdjustPriceDetailList(String priceAdjustId, String buyerId, String dealsName, int pageNum, int pageSize)
    {
        AdjustPriceDTO priceDTO = adjustInfo(priceAdjustId, true);

        Condition condition = new Condition(AdjustPriceDetail.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("delFlg", 0);
        criteria.andEqualTo("priceAdjustId", priceAdjustId);

        if (CsStringUtils.isNotEmpty(buyerId))
        {
            criteria.andEqualTo("buyerId", buyerId);
        }

        if (CsStringUtils.isNotEmpty(dealsName))
        {
            criteria.andEqualTo("dealsName", dealsName);
        }
        condition.orderBy("completeTime").desc();

        Page<AdjustPriceDetailDTO> page =PageMethod.startPage(pageNum, pageSize).doSelectPage(() -> adjustPriceDetailMapper.selectByCondition(condition));
        Page<AdjustPriceDetailDTO> pageInfo = BeanConvertUtils.convertPage(page, AdjustPriceDetailDTO.class);
        for(AdjustPriceDetailDTO dto : pageInfo.getResult())
        {
            dto.setTransportTypeName(TransportToolTypeEnum.valueOfCode(dto.getTransportType()).getDesc());
            dto.setDeliveryTypeName(PickingBillTypeEnum.valueOfCode(dto.getDeliveryType()).getDesc());
            dto.setBillTypeName(dto.getBillType().equals(2) ? "二票制" : "一票制");
            if (CsStringUtils.isEmpty(dto.getErrMsg()))
            {
                dto.setStatus(5);
            }
            else
            {
                dto.setStatus(priceDTO.getStatus());
            }

            dto.setOriginArrvPrice(BigDecimal.ZERO);
            dto.setOriginArrvAmount(BigDecimal.ZERO);

            dto.setNewestArrvPrice(BigDecimal.ZERO);
            dto.setNewestArrvAmount(BigDecimal.ZERO);

            dto.setAdjustArrvPrice(BigDecimal.ZERO);
            dto.setAdjustArrvAmount(BigDecimal.ZERO);

            // 计算到位价
            if(!"100".equals(priceDTO.getDeliveryType()))
            {
                dto.setOriginArrvPrice(dto.getOriginPrice().add(dto.getLogisticsPrice()));
                dto.setOriginArrvAmount(dto.getOriginArrvPrice().multiply(dto.getActualQuantity()));

                dto.setNewestArrvPrice(dto.getNewestPrice().add(dto.getLogisticsPrice()));
                dto.setNewestArrvAmount(dto.getNewestArrvPrice().multiply(dto.getActualQuantity()));

                dto.setAdjustArrvPrice(dto.getAdjustPrice().add(dto.getLogisticsPrice()));
                dto.setAdjustArrvAmount(dto.getAdjustArrvPrice().multiply(dto.getActualQuantity()));
            }
        }

        return pageInfo;
    }

    @Override
    public List<AdjustPriceDetailDTO> getAdjustPriceDetailListByAdjustId(String priceAdjustId)
    {
        Condition condition = new Condition(AdjustPriceDetail.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("delFlg", 0);
        criteria.andEqualTo("priceAdjustId", priceAdjustId);

        return BeanConvertUtils.convertList(adjustPriceDetailMapper.selectByCondition(condition),AdjustPriceDetailDTO.class);
    }

    @Override
    public List<AdjustPriceBillDTO> getAdjustPriceBillList(String priceAdjustId, String waybillId)
    {
        AdjustPrice adjustPrice = adjustPriceMapper.selectByPrimaryKey(priceAdjustId);
        if(adjustPrice == null || adjustPrice.getDelFlg() == 1)
        {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "查询数据不存在");
        }

        AdjustPriceDetail detail = adjustPriceDetailMapper.getAdjustPriceDetailInfoByPriceAdjustIdAndWaybillItemId(priceAdjustId, waybillId);
        if(detail == null)
        {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "查询数据不存在");
        }

        List<AdjustPriceDetail> detailList = adjustPriceDetailMapper.getAdjustPriceDetailList(waybillId, detail.getAdjustNum());

        List<AdjustPriceBillDTO> dataList = new ArrayList<>();
        for(AdjustPriceDetail data : detailList)
        {
            AdjustPrice info = adjustPriceMapper.selectByPrimaryKey(data.getPriceAdjustId());

            AdjustPriceBillDTO dto = new AdjustPriceBillDTO();
            dto.setAdjustName(info.getAdjustName());
            dto.setNewestPrice(data.getNewestPrice());
            dto.setAdjustAddPrice(data.getAdjustAddPrice());
            dto.setAdjustPrice(data.getAdjustPrice());
            dto.setAuditTime(data.getCreateTime());

            dataList.add(dto);
        }

        return dataList;
    }

    @Override
    public List<AdjustPriceMemberDTO> calculateAdjustMember(AdjustPriceDTO query)
    {
        logger.info("{}", JSON.toJSONString(query));

        if (CsStringUtils.isEmpty(query.getSaleRegionId()))
        {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "请选择销售区域");
        }

        if (CsStringUtils.isEmpty(query.getGoodsId()))
        {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "请选择要调价的商品");
        }

        if(query.getShipStartTime() == null || query.getShipEndTime() == null)
        {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "请选择要调价的时间区间");
        }


        List<String> excludeBuyerIds = new ArrayList<>();
        if(query.getExcludeList() != null)
        {
            query.getExcludeList().forEach(item -> excludeBuyerIds.add(item.getBuyerId()));
        }

        // 卖家ID
        // 销售区域ID
        // 商品ID
        // 出站时间区间
        // 买家名称
        // 出库仓库
        AdjustQueryDTO adjustQuery = new AdjustQueryDTO();
        adjustQuery.setSellerId(query.getMemberId());
        adjustQuery.setSaleRegionIds(ListUtil.toList(query.getSaleRegionId()));
        adjustQuery.setGoodsIds(ListUtil.toList(query.getGoodsId()));
        adjustQuery.setShipStartTime(query.getShipStartTime());
        adjustQuery.setShipEndTime(query.getShipEndTime());
        adjustQuery.setTransportType(CsStringUtils.isEmpty(query.getTransportType()) ? null : query.getTransportType());
        adjustQuery.setBillType(query.getBillType());
        adjustQuery.setBuyerIds(ListUtil.toList(query.getBuyerId()));
        adjustQuery.setSaleRegionIds(ListUtil.toList(query.getSaleRegionId()));
        adjustQuery.setWarehouseList(ListUtil.toList(query.getWarehouseId()));
        adjustQuery.setExcludeBuyerIds(excludeBuyerIds);
        adjustQuery.setWaybillNums(ListUtil.toList(query.getWaybillNums()));

        if (CsStringUtils.isNotEmpty(query.getDeliveryType()))
        {
            // 买家自提
            if("100".equals(query.getDeliveryType()))
            {
                adjustQuery.setDeliveryType(PickingBillTypeEnum.BUYER_TAKE.getCode());
            }
            // 平台、卖家配送
            else
            {
                adjustQuery.setDeliveryTypes(SetUtil.newArraySet(PickingBillTypeEnum.SELLER_DELIVERY.getCode(), PickingBillTypeEnum.PLATFORM_DELIVERY.getCode()));
            }
        }

        String[] saleRegionIds = query.getSaleRegionId().split(",");
        String[] saleRegionNames = query.getSaleRegionName().split(",");
        Map<String, String> regionMap = new HashMap<>();
        for(int i = 0; i < saleRegionIds.length; i++) {
            regionMap.put(saleRegionIds[i], saleRegionNames[i]);
        }

        adjustQuery.setTransportType(query.getTransportType());

        ItemResult<List<AdjustMemberDTO>> queryMemberRes = adjustPriceService.memberQuery(adjustQuery);
        if (!queryMemberRes.isSuccess()) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, queryMemberRes.getDescription());
        }
        List<AdjustPriceMemberDTO> dataList = queryMemberRes.getData().stream().map(item -> {
            AdjustPriceMemberDTO memberDTO = new AdjustPriceMemberDTO();

            String id = item.getBuyerId() + "@" + item.getDealsName() + "@" + item.getSaleRegionId() + "@" + item.getWarehouseId() + "@" + item.getGoodsId();
            String priceAdjustId = MD5.getMD5(id);

            memberDTO.setId(id);
            memberDTO.setPriceAdjustId(priceAdjustId);
            memberDTO.setAdjustNo("");
            memberDTO.setBuyerId(item.getBuyerId());
            memberDTO.setBuyerName(item.getBuyerName());
            memberDTO.setDealsName(item.getDealsName());
            memberDTO.setSaleRegionId(item.getSaleRegionId());
            memberDTO.setSaleRegionName(regionMap.get(item.getSaleRegionId()));
            memberDTO.setWarehouseId(item.getWarehouseId());
            memberDTO.setWarehouseName(item.getWarehouseName());
            memberDTO.setGoodsId(item.getGoodsId());
            memberDTO.setGoodsName(item.getGoodsName());
            memberDTO.setTotalQuantity(item.getTotalQuantity());
            memberDTO.setOriginAmount(item.getOriginAmount());
            memberDTO.setNewestAmount(item.getNewestAmount());
            memberDTO.setAdjustAddPrice(BigDecimal.ZERO);
            memberDTO.setAdjustAmount(BigDecimal.ZERO);
            memberDTO.setChangeAmount(BigDecimal.ZERO);
            memberDTO.setAffectNum(item.getAffectNum());

            return memberDTO;
        }).toList();
        return dataList;
    }

    @Override
    public List<AdjustPriceDetailDTO> calculateAdjustDetail(AdjustPriceDTO query)
    {
        logger.info("query: {}", JSON.toJSONString(query));

        if (CsStringUtils.isEmpty(query.getSaleRegionId()))
        {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "请选择销售区域");
        }

        if (CsStringUtils.isEmpty(query.getGoodsId()))
        {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "请选择要调价的商品");
        }

        if(query.getShipStartTime() == null || query.getShipEndTime() == null)
        {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "请选择要调价的时间区间");
        }


        List<String> excludeBuyerIds = new ArrayList<>();
        if(query.getExcludeList() != null)
        {
            query.getExcludeList().forEach(item -> excludeBuyerIds.add(item.getBuyerId()));
        }

        AdjustQueryDTO adjustQuery = new AdjustQueryDTO();
        adjustQuery.setSellerId(query.getMemberId());
        adjustQuery.setSaleRegionIds(ListUtil.toList(query.getSaleRegionId()));
        adjustQuery.setGoodsIds(ListUtil.toList(query.getGoodsId()));
        adjustQuery.setShipStartTime(query.getShipStartTime());
        adjustQuery.setShipEndTime(query.getShipEndTime());
        adjustQuery.setTransportType(CsStringUtils.isEmpty(query.getTransportType()) ? null : query.getTransportType());
        adjustQuery.setBillType(query.getBillType());
        adjustQuery.setBuyerIds(ListUtil.toList(query.getBuyerId()));
        adjustQuery.setSaleRegionIds(ListUtil.toList(query.getSaleRegionId()));
        adjustQuery.setWarehouseList(ListUtil.toList(query.getWarehouseId()));
        adjustQuery.setExcludeBuyerIds(excludeBuyerIds);
        adjustQuery.setWaybillNums(ListUtil.toList(query.getWaybillNums()));

        if (CsStringUtils.isNotEmpty(query.getDeliveryType()))
        {
            // 买家自提
            if("100".equals(query.getDeliveryType()))
            {
                adjustQuery.setDeliveryType(PickingBillTypeEnum.BUYER_TAKE.getCode());
            }
            // 平台、卖家配送
            else
            {
                adjustQuery.setDeliveryTypes(SetUtil.newArraySet(PickingBillTypeEnum.SELLER_DELIVERY.getCode(), PickingBillTypeEnum.PLATFORM_DELIVERY.getCode()));
            }
        }

        String[] saleRegionIds = query.getSaleRegionId().split(",");
        String[] saleRegionNames = query.getSaleRegionName().split(",");
        Map<String, String> regionMap = new HashMap<>();
        for(int i = 0; i < saleRegionIds.length; i++)
        {
            regionMap.put(saleRegionIds[i], saleRegionNames[i]);
        }


        adjustQuery.setTransportType(query.getTransportType());

        ItemResult<List<AdjustShipBillDTO>> shipBillListRes = adjustPriceService.shipBillList(adjustQuery);
        if (!shipBillListRes.isSuccess()) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, shipBillListRes.getDescription());
        }
        List<AdjustPriceDetailDTO> dataList = shipBillListRes.getData().stream().map(item -> {
            if(item.getNewestPrice() == null)
            {
                item.setNewestPrice(item.getOriginPrice());
                item.setNewestAmount(item.getOriginAmount());
            }

            AdjustPriceDetailDTO dto = new AdjustPriceDetailDTO();
            dto.setWaybillItemId(item.getWaybillItemId());
            dto.setWaybillNum(item.getWaybillNum());
            dto.setExternalWaybillNum(item.getExternalWaybillNum());
            dto.setMdmCode(item.getMdmCode());
            dto.setBuyerId(item.getBuyerId());
            dto.setBuyerName(item.getBuyerName());
            dto.setDealsName(item.getDealsName());
            dto.setSaleRegionId(item.getSaleRegionId());
            dto.setSaleRegionName(regionMap.get(item.getSaleRegionId()));
            dto.setWarehouseId(item.getWarehouseId());
            dto.setWarehouseName(item.getWarehouseName());
            dto.setGoodsId(item.getGoodsId());
            dto.setGoodsName(item.getGoodsName());
            dto.setTransportType(item.getTransportType());
            dto.setTransportTypeName(TransportToolTypeEnum.valueOfCode(item.getTransportType()).getDesc());
            dto.setDeliveryType(item.getDeliveryType());
            dto.setDeliveryTypeName(PickingBillTypeEnum.valueOfCode(item.getDeliveryType()).getDesc());
            dto.setActualQuantity(item.getTotalQuantity());
            dto.setOriginPrice(item.getOriginPrice());
            dto.setOriginAmount(item.getOriginAmount());
            dto.setNewestPrice(item.getNewestPrice());
            dto.setNewestAmount(item.getNewestAmount());
            dto.setLogisticsPrice(item.getLogisticsPrice());
            dto.setAdjustPrice(BigDecimal.ZERO);
            dto.setAdjustAmount(BigDecimal.ZERO);
            dto.setMdmCode(item.getMdmCode());
            dto.setBillType(item.getBillType());
            dto.setAdjustNum(item.getAdjustNum());
            dto.setLeaveWarehouseTime(item.getLeaveWarehouseTime());
            dto.setCompleteTime(item.getCompleteTime());

            return dto;
        }).toList();

        return dataList;
    }

    @Transactional
    @Override
    // TODO: 重构此方法以降低认知复杂度 (当前: 28, 目标: ≤15)
    // 建议: 1) 提取子方法 2) 使用早期返回 3) 简化条件表达式
    public boolean saveAdjustPriceList(AdjustPriceDTO dto, String operator)
    {
        // 封装主数据
        AdjustPrice adjustPrice = new AdjustPrice();
        adjustPrice.setId(uuidGenerator.gain());
        adjustPrice.setAdjustNo(commonBusinessIdGenerator.incrementCode("J"));
        adjustPrice.setAdjustName(dto.getAdjustName());
        adjustPrice.setMemberId(dto.getMemberId());
        adjustPrice.setMemberName(dto.getMemberName());
        adjustPrice.setSaleRegionId(dto.getSaleRegionId());
        adjustPrice.setSaleRegionName(dto.getSaleRegionName());
        adjustPrice.setGoodsId(dto.getGoodsId());
        adjustPrice.setGoodsName(dto.getGoodsName());
        adjustPrice.setShipStartTime(dto.getShipStartTime());
        adjustPrice.setShipEndTime(dto.getShipEndTime());
        adjustPrice.setTransportType(dto.getTransportType());
        adjustPrice.setDeliveryType(dto.getDeliveryType());
        adjustPrice.setBillType(dto.getBillType());
        adjustPrice.setBuyerId(dto.getBuyerId());
        adjustPrice.setBuyerName(dto.getBuyerName());
        adjustPrice.setWarehouseId(dto.getWarehouseId());
        adjustPrice.setWarehouseName(dto.getWarehouseName());
        adjustPrice.setAdjustAddPrice(dto.getAdjustAddPrice());
        adjustPrice.setMemo(dto.getMemo());
        adjustPrice.setCreateUser(operator);
        adjustPrice.setCreateTime(new Date());
        adjustPrice.setUpdateUser(operator);
        adjustPrice.setUpdateTime(new Date());
        adjustPriceMapper.insertSelective(adjustPrice);

        // 封装排除的买家
        for(AdjustPriceExcludeDTO excludeDTO : dto.getExcludeList())
        {
            AdjustPriceExclude exclude = new AdjustPriceExclude();
            exclude.setId(uuidGenerator.gain());
            exclude.setPriceAdjustId(adjustPrice.getId());
            exclude.setAdjustNo(adjustPrice.getAdjustNo());
            exclude.setBuyerId(excludeDTO.getBuyerId());
            exclude.setBuyerName(excludeDTO.getBuyerName());
            exclude.setCreateUser(operator);
            exclude.setCreateTime(new Date());
            exclude.setUpdateUser(operator);
            exclude.setUpdateTime(new Date());
            adjustPriceExcludeMapper.insertSelective(exclude);
        }


        // 买家数据
        Map<String, AdjustPriceMemberDTO> buyerMap = getMemberAdjustPriceInfo(dto);
        logger.info("buyerMap: {}", JSON.toJSONString(buyerMap));

        if(buyerMap.isEmpty())
        {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "未查询到需要调价的买家");
        }
        // 买家运单数据
        String buyerIds = StringUtil.join(dto.getMemberList().stream().map(AdjustPriceMemberDTO::getBuyerId).collect(Collectors.toSet()));
        dto.setBuyerId(buyerIds);
        Map<String, List<AdjustPriceDetailDTO>> billMap = getMemberAdjustBillList(dto);
        logger.info("billMap: {}", JSON.toJSONString(billMap));

        if(buyerMap.isEmpty())
        {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "未查询到需要调价的运单");
        }

        List<AdjustBillDTO> lBillList = new ArrayList<>();
        for(AdjustPriceMemberDTO memberDTO : dto.getMemberList())
        {
            String key = memberDTO.getBuyerId() + "@" + memberDTO.getDealsName() + "@" + memberDTO.getSaleRegionId() + "@" + memberDTO.getWarehouseId() + "@" + memberDTO.getGoodsId();

            AdjustPriceMemberDTO info = buyerMap.get(key);
            if(info == null)
            {
                logger.info("buyerMap为空: {}", key);
                continue;
            }

            List<AdjustPriceDetailDTO> billList = billMap.get(key);
            if(billList == null || billList.isEmpty())
            {
                logger.info("billList为空: {}", key);
                continue;
            }

            if(memberDTO.getAdjustAddPrice() == null || BigDecimal.ZERO.compareTo(memberDTO.getAdjustAddPrice()) == 0)
            {
                logger.info("回调价格为0: {}", key);
                continue;
            }

            // 调价幅度介于-100~100间
            if(memberDTO.getAdjustAddPrice().compareTo(BigDecimal.valueOf(200L)) > 0 || memberDTO.getAdjustAddPrice().compareTo(BigDecimal.valueOf(-200L)) < 0)
            {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "调价幅度介于-200~200间");
            }


            AdjustPriceMember adjustPriceMember = new AdjustPriceMember();
            adjustPriceMember.setId(uuidGenerator.gain());
            adjustPriceMember.setPriceAdjustId(adjustPrice.getId());
            adjustPriceMember.setAdjustNo(adjustPrice.getAdjustNo());
            adjustPriceMember.setBuyerId(memberDTO.getBuyerId());
            adjustPriceMember.setBuyerName(memberDTO.getBuyerName());
            adjustPriceMember.setDealsName(memberDTO.getDealsName());
            adjustPriceMember.setSaleRegionId(memberDTO.getSaleRegionId());
            adjustPriceMember.setSaleRegionName(memberDTO.getSaleRegionName());
            adjustPriceMember.setWarehouseId(memberDTO.getWarehouseId());
            adjustPriceMember.setWarehouseName(memberDTO.getWarehouseName());
            adjustPriceMember.setGoodsId(memberDTO.getGoodsId());
            adjustPriceMember.setGoodsName(memberDTO.getGoodsName());
            adjustPriceMember.setTotalQuantity(info.getTotalQuantity());
            adjustPriceMember.setOriginAmount(info.getOriginAmount());
            adjustPriceMember.setNewestAmount(info.getNewestAmount());
            adjustPriceMember.setAdjustAddPrice(memberDTO.getAdjustAddPrice());
            adjustPriceMember.setAdjustAmount(info.getTotalQuantity().multiply(memberDTO.getAdjustAddPrice()).add(info.getNewestAmount()));
            adjustPriceMember.setCreateUser(operator);
            adjustPriceMember.setCreateTime(new Date());
            adjustPriceMember.setUpdateUser(operator);
            adjustPriceMember.setUpdateTime(new Date());
            adjustPriceMemberMapper.insertSelective(adjustPriceMember);

            // 调价运单明细数据
            for(AdjustPriceDetailDTO detailDTO : billList)
            {
                if(detailDTO.getOriginPrice() == null || detailDTO.getOriginPrice().compareTo(BigDecimal.ZERO) <= 0)
                {
                    logger.info("原始价格为0: {}", detailDTO.getWaybillNum());
                    continue;
                }

                AdjustPriceDetail adjustPriceDetail = new AdjustPriceDetail();
                adjustPriceDetail.setId(uuidGenerator.gain());
                adjustPriceDetail.setPriceAdjustId(adjustPrice.getId());
                adjustPriceDetail.setAdjustNo(adjustPrice.getAdjustNo());
                adjustPriceDetail.setWaybillItemId(detailDTO.getWaybillItemId());
                adjustPriceDetail.setWaybillNum(detailDTO.getWaybillNum());
                adjustPriceDetail.setExternalWaybillNum(detailDTO.getExternalWaybillNum());
                adjustPriceDetail.setBuyerId(detailDTO.getBuyerId());
                adjustPriceDetail.setBuyerName(detailDTO.getBuyerName());
                adjustPriceDetail.setDealsName(detailDTO.getDealsName());
                adjustPriceDetail.setSaleRegionId(detailDTO.getSaleRegionId());
                adjustPriceDetail.setSaleRegionName(detailDTO.getSaleRegionName());
                adjustPriceDetail.setWarehouseId(detailDTO.getWarehouseId());
                adjustPriceDetail.setWarehouseName(detailDTO.getWarehouseName());
                adjustPriceDetail.setGoodsId(detailDTO.getGoodsId());
                adjustPriceDetail.setGoodsName(detailDTO.getGoodsName());
                adjustPriceDetail.setTransportType(detailDTO.getTransportType());
                adjustPriceDetail.setDeliveryType(detailDTO.getDeliveryType());
                adjustPriceDetail.setActualQuantity(detailDTO.getActualQuantity());
                adjustPriceDetail.setOriginPrice(detailDTO.getOriginPrice());
                adjustPriceDetail.setOriginAmount(detailDTO.getOriginAmount());
                adjustPriceDetail.setNewestPrice(detailDTO.getNewestPrice());
                adjustPriceDetail.setNewestAmount(detailDTO.getNewestAmount());
                adjustPriceDetail.setAdjustAddPrice(memberDTO.getAdjustAddPrice());
                adjustPriceDetail.setAdjustPrice(adjustPriceDetail.getNewestPrice().add(memberDTO.getAdjustAddPrice()));
                adjustPriceDetail.setLogisticsPrice(detailDTO.getLogisticsPrice());
                adjustPriceDetail.setAdjustAmount(adjustPriceDetail.getAdjustPrice().multiply(adjustPriceDetail.getActualQuantity()));
                adjustPriceDetail.setMdmCode(detailDTO.getMdmCode());
                adjustPriceDetail.setBillType(detailDTO.getBillType());
                adjustPriceDetail.setAdjustNum(detailDTO.getAdjustNum() + 1);
                adjustPriceDetail.setLeaveWarehouseTime(detailDTO.getLeaveWarehouseTime());
                adjustPriceDetail.setCompleteTime(detailDTO.getCompleteTime());
                adjustPriceDetail.setCreateUser(operator);
                adjustPriceDetail.setCreateTime(new Date());
                adjustPriceDetail.setUpdateUser(operator);
                adjustPriceDetail.setUpdateTime(new Date());

                if(adjustPriceDetail.getActualQuantity().compareTo(BigDecimal.ZERO) <= 0)
                {
                    throw new BizException(BasicCode.UNDEFINED_ERROR, "运单" + detailDTO.getWaybillNum() + "的实际出货量为0");
                }

                if(adjustPriceDetail.getAdjustAmount().compareTo(BigDecimal.ZERO) <= 0)
                {
                    logger.info("调价金额有误，导致部分运单的单价小于等于0: {}", JSON.toJSONString(adjustPriceDetail));
                    throw new BizException(BasicCode.UNDEFINED_ERROR, "调价金额有误，导致运单" + detailDTO.getWaybillNum() + "的单价小于等于0");
                }

                adjustPriceDetailMapper.insertSelective(adjustPriceDetail);


                AdjustBillDTO billDTO = new AdjustBillDTO();
                billDTO.setWaybillItemId(detailDTO.getWaybillItemId());
                billDTO.setWaybillNum(detailDTO.getWaybillNum());
                lBillList.add(billDTO);
            }
        }

        if(lBillList.isEmpty())
        {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "没有要调价的买家数据");
        }

        AdjustPriceResultDTO resultDTO = new AdjustPriceResultDTO();
        resultDTO.setAdjustNo(adjustPrice.getAdjustNo());
        resultDTO.setBillList(lBillList);
        resultDTO.setAdjustStatus(1);
        resultDTO.setOperator(operator);
        adjustPriceService.saveAdjustPriceResult(resultDTO);

        logger.info("DONE: {}", JSON.toJSONString(resultDTO));
        return true;
    }

    @Override
    public void processErpAdjustResult(AdjustPricePayResultDTO resultDTO)
    {
        AdjustPrice adjustPrice = adjustPriceMapper.getAdjustPriceInfoByAdjustNo(resultDTO.getAdjustNo());
        if(adjustPrice == null)
        {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "回调单号不存在");
        }

        if(adjustPrice.getStatus() == 3)
        {
            return;
        }

        if(adjustPrice.getStatus() != 2)
        {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "回调状态错误");
        }

        // 回调成功
        if(resultDTO.getStatus() == 3)
        {
            List<AdjustPriceDetailDTO> detailList = getAdjustPriceDetailListByAdjustId(adjustPrice.getId());

            AdjustPriceDTO adjustPriceDTO = new AdjustPriceDTO();
            BeanUtils.copyProperties(adjustPrice, adjustPriceDTO);
            processNoErpAdjustPrice(adjustPriceDTO, detailList, "erp");
            return;
        }

        // 回调失败
        AdjustPrice upPrice = new AdjustPrice();
        upPrice.setId(adjustPrice.getId());
        upPrice.setStatus(4);
        upPrice.setErrMsg(CsStringUtils.isEmpty(resultDTO.getErrMsg()) ? "ERP告知回调失败" : StringUtil.substring(resultDTO.getErrMsg(), 240));
        adjustPriceMapper.updateByPrimaryKeySelective(upPrice);

        Date date = new Date();
        for(AdjustPriceBillPayResultDTO payResultDTO : resultDTO.getErrList())
        {
            AdjustPriceDetail adjustPriceDetail = adjustPriceDetailMapper.getAdjustPriceDetailInfoByPriceAdjustIdAndWaybillNum(adjustPrice.getId(), payResultDTO.getWaybillNum());
            if(adjustPriceDetail == null)
            {
                continue;
            }

            AdjustPriceDetail upDetail = new AdjustPriceDetail();
            upDetail.setId(adjustPriceDetail.getId());
            upDetail.setErrMsg(StringUtil.substring(payResultDTO.getMessage(), 210));
            upDetail.setUpdateUser("erp");
            upDetail.setUpdateTime(date);
            adjustPriceDetailMapper.updateByPrimaryKeySelective(upDetail);
        }



        List<AdjustPriceDetailDTO> detailList = getAdjustPriceDetailListByAdjustId(adjustPrice.getId());
        List<AdjustBillDTO> lBillList = new ArrayList<>();
        for(AdjustPriceDetailDTO detailDTO : detailList)
        {
            AdjustBillDTO billDTO = new AdjustBillDTO();
            billDTO.setWaybillItemId(detailDTO.getWaybillItemId());
            billDTO.setWaybillNum(detailDTO.getWaybillNum());
            billDTO.setNewestPrice(detailDTO.getNewestPrice());
            billDTO.setAdjustAddPrice(detailDTO.getAdjustAddPrice());
            billDTO.setAdjustPrice(detailDTO.getAdjustPrice());
            lBillList.add(billDTO);
        }


        AdjustPriceResultDTO result = new AdjustPriceResultDTO();
        result.setAdjustNo(adjustPrice.getAdjustNo());
        result.setBillList(lBillList);
        result.setAdjustStatus(0);
        result.setOperator("erp");
        adjustPriceService.saveAdjustPriceResult(result);
    }

    /**
     * 处理没有ERP买家的调价数据
     * @param adjustPriceDTO
     * @param detailList
     */
    @Override
    public void processNoErpAdjustPrice(AdjustPriceDTO adjustPriceDTO, List<AdjustPriceDetailDTO> detailList, String operator)
    {
        int status = 3;
        try
        {
            List<AdjustBillDTO> lBillList = new ArrayList<>();
            for(AdjustPriceDetailDTO detailDTO : detailList)
            {
                AdjustBillDTO billDTO = new AdjustBillDTO();
                billDTO.setWaybillItemId(detailDTO.getWaybillItemId());
                billDTO.setWaybillNum(detailDTO.getWaybillNum());
                billDTO.setNewestPrice(detailDTO.getNewestPrice());
                billDTO.setAdjustAddPrice(detailDTO.getAdjustAddPrice());
                billDTO.setAdjustPrice(detailDTO.getAdjustPrice());
                lBillList.add(billDTO);
            }

            AdjustPriceResultDTO resultDTO = new AdjustPriceResultDTO();
            resultDTO.setAdjustNo(adjustPriceDTO.getAdjustNo());
            resultDTO.setBillList(lBillList);
            resultDTO.setAdjustStatus(2);
            resultDTO.setOperator(operator);
            adjustPriceService.saveAdjustPriceResult(resultDTO);
        }
        catch(Exception e)
        {
            status = 4;
            logger.info(e.getMessage());
        }

        AdjustPrice upPrice = new AdjustPrice();
        upPrice.setId(adjustPriceDTO.getId());
        upPrice.setStatus(status);
        adjustPriceMapper.updateByPrimaryKeySelective(upPrice);
    }

    /**
     * 将调价的买家列表转为Map
     * @param query
     * @return
     */
    private Map<String, AdjustPriceMemberDTO> getMemberAdjustPriceInfo(AdjustPriceDTO query)
    {
        List<AdjustPriceMemberDTO> dataList = calculateAdjustMember(query);
        return dataList.stream().collect(Collectors.toMap(item -> item.getBuyerId() + "@" + item.getDealsName() + "@" + item.getSaleRegionId() + "@" + item.getWarehouseId() + "@" + item.getGoodsId(), dto -> dto));
    }

    /**
     * 获取买家的运单列表
     * @param query
     * @return
     */
    private Map<String, List<AdjustPriceDetailDTO>> getMemberAdjustBillList(AdjustPriceDTO query)
    {
        List<AdjustPriceDetailDTO> dataList = calculateAdjustDetail(query);
        return dataList.stream().collect(Collectors.groupingBy(item -> item.getBuyerId() + "@" + item.getDealsName() + "@" + item.getSaleRegionId() + "@" + item.getWarehouseId() + "@" + item.getGoodsId()));
    }
}
