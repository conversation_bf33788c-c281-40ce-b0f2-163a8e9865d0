package com.ecommerce.pay.v2.biz.impl;

import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.service.common.BaseBiz;
import com.ecommerce.common.service.common.BizRedisService;
import com.github.pagehelper.page.PageMethod;
import com.ecommerce.common.utils.RandomUtil;
import com.ecommerce.pay.api.v2.dto.BankCardInfoDTO;
import com.ecommerce.pay.api.v2.dto.redis.key.PayRedisKey;
import com.ecommerce.pay.api.v2.enums.CardTypeEnum;
import com.ecommerce.pay.v2.biz.IBankCardInfoBiz;
import com.ecommerce.pay.v2.dao.mapper.BankCardInfoMapper;
import com.ecommerce.pay.v2.dao.vo.BankCardInfo;
import com.github.pagehelper.Page;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Condition;

import java.util.Comparator;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @created 15:46 08/04/2019
 * @description TODO
 */
@Slf4j
@Service
public class BankCardInfoBiz extends BaseBiz<BankCardInfo> implements IBankCardInfoBiz {

    @Autowired
    private BankCardInfoMapper mapper;

    @Autowired
    private BizRedisService redisService;

    private static final String PROFILE_TPL = "{profile}";
    private static final Pattern NUM_PATTERN = Pattern.compile("^[0-9]+$");

    @Override
    public BankCardInfoDTO findById(String id) {
        if (CsStringUtils.isEmpty(id)) {
            return null;
        }
        return convert(get(id));
    }

    @Deprecated(since = "2.1.4-RELEASE")
    @Override
    public BankCardInfoDTO findByPrefix(String account) {
        log.info("查询银行 卡号: {}", account);
        int len = account.length();
        BankCardInfoDTO dto = new BankCardInfoDTO();
        dto.setSuccess(false);
        if (!NUM_PATTERN.matcher(account).matches() || len < 14) {
            return dto;
        }
        int limit = 6;
        String prefix;

        List<RedisTemplate> list;
        while (limit >= 2) {

            prefix = account.substring(0, limit);

            list = getList(len, prefix);

            if (CollectionUtils.isEmpty(list)) {
                limit--;
                if (limit == 2 && !(len == 16 && account.startsWith("65"))) {
                    // 最短的prefix是"65"同时长度为16
                    break;
                }
                continue;
            }

            return list.stream()
                    .filter(item -> account.startsWith(item.getCardPrefix()))
                    .max((Comparator.comparingInt(o -> o.getCardPrefix().length())))
                    .map(item -> this.findById(item.getBankCardInfoId())).orElse(null);
        }
        return dto;
    }

    @Override
    public BankCardInfoDTO findByBankName(String bankName) {
        BankCardInfoDTO dto = new BankCardInfoDTO();
        dto.setSuccess(false);
        if (CsStringUtils.isEmpty(bankName)) {
            throw new BizException(BasicCode.PARAM_NULL, "bankName");
        }
        Condition condition = new Condition(BankCardInfo.class);
        condition.createCriteria().andLike("bankName", "%" + bankName + "%");
        Page<BankCardInfo> bankInfoPage = PageMethod.startPage(1, 1)
                .doSelectPage(() -> mapper.selectByCondition(condition));

        if (bankInfoPage.getTotal() != 0) {
            return convert(bankInfoPage.get(0));
        }
        return dto;
    }

    @Override
    public String findIconByBankName(String bankName) {
        Condition condition = newCondition();
        condition.createCriteria().andLike("bankName","%"+bankName+"%").andIsNotNull("cardIcon");
       PageMethod.startPage(1, 10, false);
        List<BankCardInfo> list = this.mapper.selectByCondition(condition);
        if( CollectionUtils.isEmpty(list) ){
            return null;
        }
        BankCardInfo bankCardInfo = list.stream().filter(item->item.getCardIcon() != null && item.getCardIcon().length() > 1).findFirst().orElse(null);
        return bankCardInfo == null ? null : bankCardInfo.getCardIcon().replace(PROFILE_TPL, getProfile());
    }

    @SuppressWarnings("unchecked")
    private List<RedisTemplate> getList(int len, String prefix) {
        String key = PayRedisKey.PAY_BANK_CARD_INFO + len + "_" + prefix;
        List<RedisTemplate> list = redisService.get(key, List.class);

        // 24小时过期 60 * 60 * 24L + 随机100秒
        long expire = 60 * 60 * 24L + RandomUtil.RANDOM.nextInt(100);
        if (list == null) {
            log.info("当前银行卡前缀无缓存: {}", key);
            List<BankCardInfo> cardInfoList = mapper.findByPrefixAndCardLength(prefix, len);
            list = cardInfoList.stream().map(this::convertTemplate).toList();

            try {
                redisService.setex(key, expire, list);
            } catch (Exception e) {
                log.error(e.getMessage());
            }
        } else {
            Long e = redisService.getRedisTemplate().getExpire(key);
            if (e < 0) {
                redisService.expire(key, expire);
            }
        }
        return list;
    }

    private BankCardInfoDTO convert(BankCardInfo vo) {
        BankCardInfoDTO dto = new BankCardInfoDTO();
        BeanUtils.copyProperties(vo, dto);
        String cardIcon = dto.getCardIcon();
        if (CsStringUtils.isNotBlank(cardIcon)) {
            String s = cardIcon.replace(PROFILE_TPL, getProfile());
            dto.setCardIcon(s);
        }
        dto.setCardTypeString(CardTypeEnum.getMsgByCode(dto.getCardType()));
        dto.setSuccess(true);
        return dto;
    }

    private RedisTemplate convertTemplate(BankCardInfo vo) {
        RedisTemplate template = new RedisTemplate();
        template.setBankCardInfoId(vo.getBankCardInfoId());
        template.setCardPrefix(vo.getCardPrefix());
        return template;
    }

    @Value("${spring.profiles.active}")
    private String profile;

    private String getProfile() {
        if ("newuat".equalsIgnoreCase(profile)) {
            profile = "uat";
        }
        if ("default".equalsIgnoreCase(profile) || "test".equalsIgnoreCase(profile)) {
            profile = "dev";
        }
        return profile;
    }

    @Data
    private static class RedisTemplate {
        private String bankCardInfoId;
        private String cardPrefix;
    }

}
