package com.ecommerce.pay.v2.biz.impl;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.service.common.BaseBiz;
import com.ecommerce.pay.api.v2.dto.BankCityDTO;
import com.ecommerce.pay.api.v2.enums.BankCityTypeEnum;
import com.ecommerce.pay.api.exception.PayCode;
import com.ecommerce.pay.v2.biz.IBankCityBiz;
import com.ecommerce.pay.v2.dao.vo.BankCity;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

@Service
@Slf4j
public class BankCityBiz extends BaseBiz<BankCity> implements IBankCityBiz {

	private static final String CITY_AREA_TYPE = "cityAreatype";

	@Override
	public BankCityDTO getBankCityByCode(String cityCode) {
		log.info(" 查询开户行城市 {} ", cityCode);
        if (CsStringUtils.isEmpty(cityCode)) {
			throw new BizException(PayCode.DATA_NOT_FOUND);
		}
		return this.convertToDTO(this.get(cityCode));
	}

	@Override
	public List<BankCityDTO> getSubBankCity(String cityCode) {
		log.info(" 查询开户行城市 {} ", cityCode);
		List<BankCity> voList;
        if (CsStringUtils.isEmpty(cityCode)) {
			Condition condition = new Condition(BankCity.class);
			Example.Criteria criteria = condition.createCriteria();
			criteria.andEqualTo(CITY_AREA_TYPE, 1);
			voList = findByCondition(condition);
		} else {
			BankCity parent = this.get(cityCode);
			if (parent != null) {
				if (BankCityTypeEnum.PROVINCE.getCode().equals(parent.getCityAreatype())) {
					Condition condition = new Condition(BankCity.class);
					Example.Criteria criteria = condition.createCriteria();
					criteria.andEqualTo(CITY_AREA_TYPE, BankCityTypeEnum.CITY.getCode());
					criteria.andEqualTo("cityNodecode", parent.getCityAreacode());
					voList = findByCondition(condition);
				} else {
					Condition condition = new Condition(BankCity.class);
					Example.Criteria criteria = condition.createCriteria();
					criteria.andEqualTo(CITY_AREA_TYPE, BankCityTypeEnum.AREA.getCode());
					criteria.andEqualTo("cityTopareacode2", cityCode);
					voList = findByCondition(condition);
				}
			} else {
				throw new BizException(PayCode.DATA_NOT_FOUND);
			}
		}
		if (CollectionUtils.isEmpty(voList)) {
			return Collections.emptyList();
		}
		return voList.stream().map(this::convertToDTO).toList();
	}
	

	
	
	

	private BankCityDTO convertToDTO(BankCity offlineChannelInfo) {
		if (offlineChannelInfo == null) {
			return null;
		}
		BankCityDTO bankCityDTO = new BankCityDTO();
		BeanUtils.copyProperties(offlineChannelInfo, bankCityDTO);
		return bankCityDTO;
	}

}
