package com.ecommerce.pay.v2.biz.impl;

import com.ecommerce.common.exception.BizException;
import com.github.pagehelper.page.PageMethod;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.pay.api.exception.PayCode;
import com.ecommerce.pay.api.v2.dto.BankInfoDTO;
import com.ecommerce.pay.v2.biz.IBankInfoBiz;
import com.ecommerce.pay.v2.dao.mapper.BankInfoMapper;
import com.ecommerce.pay.v2.dao.vo.BankInfo;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class BankInfoBiz implements IBankInfoBiz {
	@Autowired
	private BankInfoMapper baseBankMapper;

	@Override
	public List<BankInfoDTO> getBankInfo(BankInfoDTO query, Integer limit) {
		log.info("查询 银行信息 {}", query);
        if (query == null || CsStringUtils.isEmpty(query.getCityCode()) || CsStringUtils.isEmpty(query.getBankClsCode())) {
			throw new BizException(PayCode.PARAM_NULL);
		}

		Condition condition = getCondition(query);

		PageHelper.startPage(0, limit);
		List<BankInfo> voList = baseBankMapper.selectByCondition(condition);
		if (CollectionUtils.isEmpty(voList)) {
			return Collections.emptyList();
		}
		return voList.stream().map(this::convertToDTO).toList();
	}

	@Override
	public PageInfo<BankInfoDTO> pageBankInfo(BankInfoDTO query) {
		Objects.requireNonNull(query, "参数为空");
		Condition condition = getCondition(query);
		int defaultPageNum = 1;
		int defaultPageSize = 10;
		if (query.getPageNum() == null || query.getPageNum() < defaultPageNum) {
			query.setPageNum(defaultPageNum);
		}
		if (query.getPageSize() == null || query.getPageSize() < defaultPageSize) {
			query.setPageSize(defaultPageSize);
		}
		Page<BankInfo> bankInfoPage = PageMethod.startPage(query.getPageNum(), query.getPageSize())
				.doSelectPage(() -> baseBankMapper.selectByCondition(condition));

		Page<BankInfoDTO> collect = bankInfoPage.stream().map(this::convertToDTO).collect(Collectors.toCollection(Page::new));
		BeanUtils.copyProperties(bankInfoPage, collect);
		return collect.toPageInfo();
	}

	@Override
	public void save(BankInfoDTO bankInfoDTO) {
		BankInfo info = convert(Objects.requireNonNull(bankInfoDTO, "参数不可为空"));
		baseBankMapper.insert(info);
	}

	@Override
	public void delete(String bankNo) {
		Condition condition = new Condition(BankInfo.class);
		condition.createCriteria()
				.andEqualTo("bankNo", Objects.requireNonNull(bankNo, "参数为空"));
		baseBankMapper.deleteByCondition(condition);
	}

	private Condition getCondition(BankInfoDTO query) {
		Condition condition = new Condition(BankInfo.class);
		Example.Criteria criteria = condition.createCriteria();
        if (CsStringUtils.isNotBlank(query.getCityCode())) {
			criteria.andEqualTo("cityCode", query.getCityCode());
		}
        if (CsStringUtils.isNotBlank(query.getBankClsCode())) {
			criteria.andEqualTo("bankClsCode", query.getBankClsCode());
		}
		criteria.andEqualTo("bankStatus", "1");
        if (CsStringUtils.isNotEmpty(query.getBankName())) {
			criteria.andLike("bankName", "%" + query.getBankName() + "%");
		}
		return condition;
	}

	private BankInfoDTO convertToDTO(BankInfo offlineChannelInfo) {
		if (offlineChannelInfo == null) {
			return null;
		}
		BankInfoDTO bankInfoDTO = new BankInfoDTO();
		BeanUtils.copyProperties(offlineChannelInfo, bankInfoDTO);
		return bankInfoDTO;
	}

	private BankInfo convert(BankInfoDTO offlineChannelInfoDTO) {
		if (offlineChannelInfoDTO == null) {
			return null;
		}
		BankInfo paymentBill = new BankInfo();
		BeanUtils.copyProperties(offlineChannelInfoDTO, paymentBill);
		return paymentBill;
	}

}
