package com.ecommerce.pay.v2.biz.impl;

import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.utils.BeanConvertUtils;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.pay.api.v2.dto.BaseBankDTO;
import com.ecommerce.pay.v2.biz.IBaseBankBiz;
import com.ecommerce.pay.v2.dao.mapper.BaseBankMapper;
import com.ecommerce.pay.v2.dao.vo.BaseBank;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class BaseBankBiz implements IBaseBankBiz {
	@Autowired
	private BaseBankMapper baseBankMapper;

	@Override
	public List<BaseBankDTO> searchBaseBankLimit(String bankName) {
		// BeanConvertUtils.convert(arg0, arg1)

		log.info("查询 银行信息 {}", bankName);

		int limit = 16;

		Condition condition = new Condition(BaseBank.class);
		Example.Criteria criteria = condition.createCriteria();
        if (CsStringUtils.isNotEmpty(bankName)) {
			criteria.andLike("bankName", "%" + bankName + "%");
		}
		PageHelper.startPage(0, limit);
		List<BaseBank> voList = baseBankMapper.selectByCondition(condition);
		if (CollectionUtils.isEmpty(voList)) {
			return Collections.emptyList();
		}
		return voList.stream()
				.map(item -> BeanConvertUtils.convert(item, BaseBankDTO.class))
				.toList();
	}

    @Override
    public BaseBankDTO findByCodeAndNameLike(String code,String nameLike) {
        if (CsStringUtils.isEmpty(code)) {
			throw new BizException(BasicCode.PARAM_NULL, "bankClsCode");
		}
        if (CsStringUtils.length(nameLike) < 4) {
			return null;
		}
		Condition condition = new Condition(BaseBank.class);
		condition.createCriteria().andEqualTo("bankCode", code).andLike("bankName",nameLike.substring(0,4)+"%");
		List<BaseBank> banks = baseBankMapper.selectByCondition(condition);
		if (CollectionUtils.isEmpty(banks)) {
			return null;
		}
		BaseBankDTO dto = new BaseBankDTO();
		BeanUtils.copyProperties(banks.get(0), dto);
		return dto;
    }

}
