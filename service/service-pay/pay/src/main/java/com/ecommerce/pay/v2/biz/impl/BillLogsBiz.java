package com.ecommerce.pay.v2.biz.impl;

import com.alibaba.fastjson.JSON;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.service.common.BaseBiz;
import com.ecommerce.common.service.common.BizRedisService;
import com.ecommerce.common.service.common.bsid.CommonBusinessIdGenerator;
import com.ecommerce.common.utils.BeanConvertUtils;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.common.utils.DateUtil;
import com.ecommerce.member.api.dto.account.AccountSimpleDTO;
import com.ecommerce.member.api.dto.member.enums.MemberPlatform;
import com.ecommerce.member.api.dto.member.enums.MemberTypeEnum;
import com.ecommerce.member.api.service.IAccountService;
import com.ecommerce.pay.api.exception.PayCode;
import com.ecommerce.pay.api.v2.dto.bill.BillLogsDTO;
import com.ecommerce.pay.api.v2.dto.bill.CashWithdrawalBillDTO;
import com.ecommerce.pay.api.v2.dto.bill.DeliverWayUpdateDTO;
import com.ecommerce.pay.api.v2.dto.bill.PaymentBillDTO;
import com.ecommerce.pay.api.v2.dto.bill.PaymentBillDetailDTO;
import com.ecommerce.pay.api.v2.dto.bill.RechargeBillDTO;
import com.ecommerce.pay.api.v2.dto.bill.RefundBillDTO;
import com.ecommerce.pay.api.v2.dto.bill.RefundBillDetailDTO;
import com.ecommerce.pay.api.v2.dto.bill.SplitPayBillDTO;
import com.ecommerce.pay.api.v2.dto.bill.SplitPayBillDetailDTO;
import com.ecommerce.pay.api.v2.dto.bill.TradingFlowExportDTO;
import com.ecommerce.pay.api.v2.dto.electricInvoiceAccount.PageQuery;
import com.ecommerce.pay.api.v2.enums.ChannelCodeEnum;
import com.ecommerce.pay.api.v2.enums.PaymentTypeEnum;
import com.ecommerce.pay.enums.PayDetailTypeEnum;
import com.ecommerce.pay.v2.biz.IBillLogsBiz;
import com.ecommerce.pay.v2.dao.mapper.BillLogsMapper;
import com.ecommerce.pay.v2.dao.vo.BillLogs;
import com.ecommerce.pay.v2.dao.vo.DriverPayInfo;
import com.github.pagehelper.Page;
import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.StringJoiner;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @created 20:18 02/09/2019
 * @description TODO
 */
@Slf4j
@Service
public class BillLogsBiz extends BaseBiz<BillLogs> implements IBillLogsBiz {

    public static final String DEL_FLG = "delFlg";
    public static final String CREATE_TIME = "createTime";
    public static final String PAY_TYPE = "payType";
    public static final String PAYER_FLG = "payerFlg";
    public static final String SUB_PAY_TYPE = "subPayType";
    public static final String CHANNEL_CODE = "channelCode";
    public static final String DELIVER_WAY = "deliverWay";
    @Autowired
    private BillLogsMapper mapper;
    @Autowired
    private CommonBusinessIdGenerator codeGenerator;
    @Autowired
    private IAccountService accountService;
    @Autowired
    private BizRedisService bizRedisService;
    @Autowired
    private ThreadPoolExecutor threadPoolExecutor;

    @Override
    public int countByRelatedBillNo(String relatedBillNo) {
        Condition condition = newCondition();
        condition.createCriteria().andEqualTo(DEL_FLG,false).andEqualTo("relatedBillNo",relatedBillNo);
        condition.orderBy(CREATE_TIME).desc();
        return mapper.selectCountByCondition(condition);
    }

    private void saveBillLogs(BillLogsDTO logsDTO, String operatorId) {
        if (CsStringUtils.isEmpty(operatorId)) {
            operatorId = "system_empty_operator";
        }
        logsDTO.setBillLogsId(null);
        logsDTO.setBillLogsNo(codeGenerator.incrementCode());
        if (CsStringUtils.isEmpty(logsDTO.getRelatedBillNo())) {
            logsDTO.setRelatedBillNo(logsDTO.getBillLogsNo());
        }
        BillLogs billLogs = dto2Vo(logsDTO);
        save(billLogs, operatorId);
        String billLogId = billLogs.getBillLogsId();
        logsDTO.setBillLogsId(billLogId);
    }

    @Override
    public Set<String> findMemberPayChannelForQuery(String memberId, Integer payerFlg)
    {
        List<BillLogs> billLogsList = mapper.getMemberPayChannelListByMemberId(memberId, payerFlg);
        return billLogsList.stream().map(BillLogs::getChannelCode).collect(Collectors.toSet());
    }

    /**
     * 更新订单配送方式
     */
    @Override
    public void updateDeliverWay(List<DeliverWayUpdateDTO> list){
        if( !CollectionUtils.isEmpty(list)) {
            threadPoolExecutor.execute(()->{
                try {
                    for (DeliverWayUpdateDTO deliverWayUpdateDTO : list) {
                        BillLogs billLog = new BillLogs();
                        billLog.setDeliverWay(deliverWayUpdateDTO.getDeliverWay());
                        Condition condition = newCondition();
                        condition.createCriteria().andEqualTo("orderId", deliverWayUpdateDTO.getOrderId());
                        //主键更新 防止死锁
                        for (BillLogs billLogs : findByCondition(condition)) {
                            if (CsStringUtils.isBlank(billLogs.getDeliverWay())) {
                                BillLogs updateBillLogs = new BillLogs();
                                updateBillLogs.setBillLogsId(billLogs.getBillLogsId());
                                updateBillLogs.setDeliverWay(deliverWayUpdateDTO.getDeliverWay());
                                mapper.updateByPrimaryKeySelective(updateBillLogs);
                            }
                        }
                    }
                }catch (Exception e){
                    log.error(e.getMessage(),e);
                }
            });
        }
    }

    /**
     * 更新订单配送方式
     */
    private void updateDeliverWayToPrepare(String orderId){
        if (CsStringUtils.isBlank(orderId)) {
            return;
        }
        bizRedisService.getRedisTemplate().opsForSet().add(BillLogsDTO.UPDATE_DELIVER_WAY,orderId);
    }

    @Override
    public List<TradingFlowExportDTO> exportTradingFlow(BillLogsDTO queryDTO) {

        List<TradingFlowExportDTO> flowExportDTOList = Lists.newArrayList();
        Condition condition = getCondition(queryDTO);
        condition.selectProperties("billLogsNo", "thirdBillNo", "orderId", "orderNo", "payType","detail",
                "subPayType", "targetMemberName", "actualPayAmount", "createTime", "channelName");
        // orderId -> orderNo
        List<BillLogs> billLogsList = mapper.selectByCondition(condition);
        if (CollectionUtils.isEmpty(billLogsList)) {
            return flowExportDTOList;
        }



        for (BillLogs billLogs : billLogsList) {
            if ((PaymentTypeEnum.REFUND.getCode().equals(billLogs.getPayType()) ||
                    PaymentTypeEnum.CASH_WITHDRAWAL.getCode().equals(billLogs.getPayType())) &&
                    billLogs.getActualPayAmount() != null) {
                billLogs.setActualPayAmount(billLogs.getActualPayAmount().negate());
            }
            TradingFlowExportDTO flowExportDTO = BeanConvertUtils.convert(billLogs, TradingFlowExportDTO.class);
            flowExportDTOList.add(flowExportDTO);

        }

        // 交易类型
        for (TradingFlowExportDTO flowExportDTO : flowExportDTOList) {
            if (CsStringUtils.isBlank(flowExportDTO.getSubPayType())) {
                PaymentTypeEnum paymentTypeEnum = PaymentTypeEnum.getByCode(flowExportDTO.getPayType());
                if (paymentTypeEnum != null) {
                    flowExportDTO.setSubPayType(paymentTypeEnum.getMessage());
                }
                continue;
            }
            String[] split = CsStringUtils.split(flowExportDTO.getSubPayType(), ",");
            if (split != null && split.length > 0) {
                for (int i = 0; i < split.length; i++) {
                    PayDetailTypeEnum payDetailTypeEnum = PayDetailTypeEnum.getByCodeOrMessage(split[i]);
                    if (payDetailTypeEnum != null) {
                        split[i] = payDetailTypeEnum.getMessage();
                    }
                }
                flowExportDTO.setSubPayType(String.join(",", split));
            }
        }
        return flowExportDTOList;
    }

    private boolean checkRepeat(String relatedBillNo) {
        if (CsStringUtils.isNotBlank(relatedBillNo)) {
            Condition condition = new Condition(BillLogs.class);
            condition.createCriteria()
                    .andEqualTo("relatedBillNo", relatedBillNo)
                    .andEqualTo(DEL_FLG, false);

            if ( mapper.selectCountByCondition(condition) > 0) {
                log.info("关联单据ID relatedBillNo: {} 重复插入流水", relatedBillNo);
                return true;
            }
        } else {
            log.info(" 关联单据ID relatedBillNo 为空!");
        }
        return false;
    }

    @Override
    public void logsCashWithdrawal(CashWithdrawalBillDTO cashWithdrawalBillDTO, String operatorId) {
        log.info(" -->> 记录提现流水 {}", cashWithdrawalBillDTO);
        if (checkRepeat(cashWithdrawalBillDTO.getCashWithdrawalBillNo())) {
            return;
        }
        BillLogsDTO logsDTO = new BillLogsDTO();
        BeanUtils.copyProperties(cashWithdrawalBillDTO, logsDTO);
        logsDTO.setRelatedBillId(cashWithdrawalBillDTO.getCashWithdrawalBillId());
        logsDTO.setRelatedBillNo(cashWithdrawalBillDTO.getCashWithdrawalBillNo());
        logsDTO.setMemberId(cashWithdrawalBillDTO.getPayerMemberId());
        logsDTO.setPayerFlg(true);
        logsDTO.setMemberName(cashWithdrawalBillDTO.getPayerMemberName());
        logsDTO.setPayType(PaymentTypeEnum.CASH_WITHDRAWAL.getCode());
        logsDTO.setActualPayAmount(cashWithdrawalBillDTO.getActualPayAmount());
        logsDTO.setThirdBillNo(cashWithdrawalBillDTO.getExtBizNo());

        saveBillLogs(logsDTO, cashWithdrawalBillDTO.getPayerMemberId());
    }

    @Override
    public void logsRecharge(RechargeBillDTO rechargeBillDTO, String operatorId) {
        log.info(" -->> 记录充值流水 {}", rechargeBillDTO);
        if (checkRepeat(rechargeBillDTO.getRechargeBillNo())) {
            return;
        }
        BillLogsDTO logsDTO = new BillLogsDTO();
        BeanUtils.copyProperties(rechargeBillDTO, logsDTO);
        logsDTO.setRelatedBillId(rechargeBillDTO.getRechargeBillId());
        logsDTO.setRelatedBillNo(rechargeBillDTO.getRechargeBillNo());
        logsDTO.setMemberId(rechargeBillDTO.getPayerMemberId());
        logsDTO.setPayerFlg(true);
        logsDTO.setMemberName(rechargeBillDTO.getPayerMemberName());
        logsDTO.setPayType(PaymentTypeEnum.RECHARGE.getCode());
        logsDTO.setActualPayAmount(rechargeBillDTO.getActualPayAmount());
        logsDTO.setThirdBillNo(rechargeBillDTO.getExtBizNo());

        saveBillLogs(logsDTO, rechargeBillDTO.getPayerMemberId());
    }

    @Transactional
    @Override
    public void logsRefund(RefundBillDTO refundBillDTO, String operatorId) {
        log.info(" -->> 记录退款流水 {}", refundBillDTO);
        if (checkRepeat(refundBillDTO.getRefundBillNo())) {
            return;
        }
        BillLogsDTO logsDTO = new BillLogsDTO();
        logsDTO.setRelatedBillId(refundBillDTO.getRefundBillId());
        logsDTO.setRelatedBillNo(refundBillDTO.getRefundBillNo());
        logsDTO.setChannelCode(refundBillDTO.getChannelCode());
        logsDTO.setChannelId(refundBillDTO.getChannelId());
        logsDTO.setChannelName(refundBillDTO.getChannelName());
        logsDTO.setOrderId(refundBillDTO.getOrderId());
        logsDTO.setOrderNo(refundBillDTO.getOrderNo());
        logsDTO.setPayType(PaymentTypeEnum.REFUND.getCode());
        logsDTO.setThirdBillNo(refundBillDTO.getExtBizNo());

        List<RefundBillDetailDTO> dtoList = refundBillDTO.getDetailDTOList();
        for (RefundBillDetailDTO dto : dtoList) {
            logsDTO.setSubPayType(getSubPayType(dto.getSubject()));
            logsDTO.setActualPayAmount(dto.getActualPayAmount());
            logsDTO.setDetail(refundBillDTO.getGoodsNames());

            logsDTO.setMemberId(refundBillDTO.getPayerMemberId());
            logsDTO.setPayerFlg(true);
            logsDTO.setMemberName(refundBillDTO.getPayerMemberName());
            logsDTO.setTargetMemberId(dto.getPayeeMemberId());
            logsDTO.setTargetMemberName(dto.getPayeeMemberName());
            saveBillLogs(logsDTO, operatorId);
            logsDTO.setBillLogsId(null);
            logsDTO.setMemberId(dto.getPayeeMemberId());
            logsDTO.setPayerFlg(false);
            logsDTO.setMemberName(dto.getPayeeMemberName());
            logsDTO.setTargetMemberId(refundBillDTO.getPayerMemberId());
            logsDTO.setTargetMemberName(refundBillDTO.getPayerMemberName());
            saveBillLogs(logsDTO, operatorId);
            logsDTO.setBillLogsId(null);
        }
        updateDeliverWayToPrepare(refundBillDTO.getOrderId());
    }

    @Override
    public void logsRefundByFreeze(RefundBillDTO refundBillDTO, String operatorId) {
        log.info(" -->> 记录退款流水 {}", refundBillDTO);
        if (checkRepeat(refundBillDTO.getRefundBillNo())) {
            return;
        }
        BillLogsDTO logsDTO = new BillLogsDTO();
        logsDTO.setRelatedBillId(refundBillDTO.getRefundBillId());
        logsDTO.setRelatedBillNo(refundBillDTO.getRefundBillNo());
        logsDTO.setChannelCode(refundBillDTO.getChannelCode());
        logsDTO.setChannelId(refundBillDTO.getChannelId());
        logsDTO.setChannelName(refundBillDTO.getChannelName());
        logsDTO.setOrderId(refundBillDTO.getOrderId());
        logsDTO.setOrderNo(refundBillDTO.getOrderNo());
        logsDTO.setPayType(PaymentTypeEnum.REFUND.getCode());
        logsDTO.setThirdBillNo(refundBillDTO.getExtBizNo());

        List<RefundBillDetailDTO> dtoList = refundBillDTO.getDetailDTOList();
        for (RefundBillDetailDTO dto : dtoList) {
            logsDTO.setSubPayType(getSubPayType(dto.getSubject()));
            logsDTO.setActualPayAmount(dto.getActualPayAmount());
            logsDTO.setDetail(refundBillDTO.getGoodsNames());

            logsDTO.setMemberId(refundBillDTO.getPayerMemberId());
            logsDTO.setPayerFlg(true);
            logsDTO.setMemberName(refundBillDTO.getPayerMemberName());
            logsDTO.setTargetMemberId(dto.getPayeeMemberId());
            logsDTO.setTargetMemberName(dto.getPayeeMemberName());
            saveBillLogs(logsDTO, operatorId);
            logsDTO.setBillLogsId(null);
            //冻结支付退款，卖家不需要记录退款记录
            if(! ChannelCodeEnum.PINGANJZPAY.getCode().equals(logsDTO.getChannelCode())) {
                logsDTO.setMemberId(dto.getPayeeMemberId());
                logsDTO.setPayerFlg(false);
                logsDTO.setMemberName(dto.getPayeeMemberName());
                logsDTO.setTargetMemberId(refundBillDTO.getPayerMemberId());
                logsDTO.setTargetMemberName(refundBillDTO.getPayerMemberName());
                saveBillLogs(logsDTO, operatorId);
                logsDTO.setBillLogsId(null);
            }
        }
        updateDeliverWayToPrepare(refundBillDTO.getOrderId());
    }

    @Override
    public void logsSplit(SplitPayBillDTO splitPayBillDTO, String operatorId) {
        log.info(" -->> 记录分账流水 {}", splitPayBillDTO);
        if (checkRepeat(splitPayBillDTO.getSplitPayBillNo())) {
            return;
        }
        BillLogsDTO logsDTO = new BillLogsDTO();
        logsDTO.setRelatedBillId(splitPayBillDTO.getSplitPayBillId());
        logsDTO.setRelatedBillNo(splitPayBillDTO.getSplitPayBillNo());
        logsDTO.setChannelCode(splitPayBillDTO.getChannelCode());
        logsDTO.setChannelId(splitPayBillDTO.getChannelId());
        logsDTO.setChannelName(splitPayBillDTO.getChannelName());
        logsDTO.setOrderId(splitPayBillDTO.getOrderId());
        logsDTO.setOrderNo(splitPayBillDTO.getOrderNo());
        logsDTO.setPayType(PaymentTypeEnum.PAY.getCode());
        logsDTO.setThirdBillNo(splitPayBillDTO.getExtBizNo());
        logsDTO.setDetail(splitPayBillDTO.getBillSplitInfoString());

        List<SplitPayBillDetailDTO> dtoList = splitPayBillDTO.getDetailDTOList();
        for (SplitPayBillDetailDTO dto : dtoList) {
            logsDTO.setSubPayType(getSubPayType(dto.getSubject()));
            logsDTO.setActualPayAmount(dto.getActualPayAmount());

            logsDTO.setMemberId(dto.getPayeeMemberId());
            logsDTO.setPayerFlg(false);
            logsDTO.setMemberName(dto.getPayeeMemberName());
            logsDTO.setTargetMemberId(splitPayBillDTO.getPayerMemberId());
            logsDTO.setTargetMemberName(splitPayBillDTO.getPayerMemberName());
            saveBillLogs(logsDTO, operatorId);
            logsDTO.setBillLogsId(null);
        }
        updateDeliverWayToPrepare(splitPayBillDTO.getOrderId());
    }

    @Override
    public void saveErpBillLogs(BillLogsDTO logsDTO, String operatorId) {
        saveBillLogs(logsDTO,operatorId);
        updateDeliverWayToPrepare(logsDTO.getOrderId());
    }

    @Override
    public void saveLogs(BillLogs billLogs, String operatorId) {
        if( billLogs == null ){
            return;
        }
        billLogs.setCreateUser(operatorId);
        billLogs.setCreateTime(new Date());
        billLogs.setBillLogsId(getUuidGeneratorGain());
        billLogs.setBillLogsNo(codeGenerator.incrementCode());
        getMapper().insert(billLogs);
    }

    @Override
    public void saveErpBillLogs(PaymentBillDTO paymentBillDTO, String operatorId) {
        log.info(" -->> 记录erp支付流水 {}", paymentBillDTO);
        if (checkRepeat(paymentBillDTO.getPaymentBillNo())) {
            return;
        }
        BillLogsDTO logsDTO = new BillLogsDTO();
        logsDTO.setRelatedBillId(paymentBillDTO.getPaymentBillId());
        logsDTO.setRelatedBillNo(paymentBillDTO.getPaymentBillNo());
        logsDTO.setChannelCode(paymentBillDTO.getChannelCode());
        logsDTO.setChannelId(paymentBillDTO.getChannelId());
        logsDTO.setChannelName(paymentBillDTO.getChannelName());
        logsDTO.setOrderId(paymentBillDTO.getOrderId());
        logsDTO.setOrderNo(paymentBillDTO.getOrderNo());
        logsDTO.setPayType(PaymentTypeEnum.PAY.getCode());
        logsDTO.setThirdBillNo(paymentBillDTO.getExtBizNo());
        logsDTO.setDetail(paymentBillDTO.getGoodsNames());

        List<PaymentBillDetailDTO> dtoList = paymentBillDTO.getDetailDTOList();
        if(CollectionUtils.isEmpty(dtoList)){
            return;
        }
        for (PaymentBillDetailDTO dto : dtoList) {
            logsDTO.setSubPayType(getSubPayType(dto.getSubject()));
            logsDTO.setActualPayAmount(dto.getActualPayAmount());

            logsDTO.setMemberId(paymentBillDTO.getPayerMemberId());
            logsDTO.setPayerFlg(true);
            logsDTO.setMemberName(paymentBillDTO.getPayerMemberName());
            logsDTO.setTargetMemberId(dto.getPayeeMemberId());
            logsDTO.setTargetMemberName(dto.getPayeeMemberName());
            saveBillLogs(logsDTO, operatorId);
            logsDTO.setBillLogsId(null);
            logsDTO.setMemberId(dto.getPayeeMemberId());
            logsDTO.setPayerFlg(false);
            logsDTO.setMemberName(dto.getPayeeMemberName());
            logsDTO.setTargetMemberId(paymentBillDTO.getPayerMemberId());
            logsDTO.setTargetMemberName(paymentBillDTO.getPayerMemberName());
            saveBillLogs(logsDTO, operatorId);
            logsDTO.setBillLogsId(null);
        }
        updateDeliverWayToPrepare(paymentBillDTO.getOrderId());
    }

    @Override
    public void logsSingleSplit(SplitPayBillDTO splitPayBillDTO, SplitPayBillDetailDTO detailDTO, String operatorId) {
        log.info(" -->> 记录单个分账流水 {}", detailDTO);
        BillLogsDTO logsDTO = new BillLogsDTO();
        logsDTO.setRelatedBillId(splitPayBillDTO.getSplitPayBillId());
        logsDTO.setRelatedBillNo(splitPayBillDTO.getSplitPayBillNo());
        logsDTO.setChannelCode(splitPayBillDTO.getChannelCode());
        logsDTO.setChannelId(splitPayBillDTO.getChannelId());
        logsDTO.setChannelName(splitPayBillDTO.getChannelName());
        logsDTO.setOrderId(splitPayBillDTO.getOrderId());
        logsDTO.setOrderNo(splitPayBillDTO.getOrderNo());
        logsDTO.setPayType(PaymentTypeEnum.PAY.getCode());
        logsDTO.setThirdBillNo(splitPayBillDTO.getExtBizNo());

        logsDTO.setSubPayType(getSubPayType(detailDTO.getSubject()));
        logsDTO.setActualPayAmount(detailDTO.getActualPayAmount());

        logsDTO.setMemberId(detailDTO.getPayeeMemberId());
        logsDTO.setPayerFlg(false);
        logsDTO.setMemberName(detailDTO.getPayeeMemberName());
        logsDTO.setTargetMemberId(splitPayBillDTO.getPayerMemberId());
        logsDTO.setTargetMemberName(splitPayBillDTO.getPayerMemberName());
        logsDTO.setDetail(splitPayBillDTO.getBillSplitInfoString());
        saveBillLogs(logsDTO, operatorId);
        logsDTO.setBillLogsId(null);
        updateDeliverWayToPrepare(splitPayBillDTO.getOrderId());
    }

    @Override
    public void logsPayment(PaymentBillDTO paymentBillDTO, String operatorId) {
        log.info(" -->> 记录支付流水 {}", paymentBillDTO);
        if (checkRepeat(paymentBillDTO.getPaymentBillNo())) {
            return;
        }
        BillLogsDTO logsDTO = new BillLogsDTO();
        logsDTO.setRelatedBillId(paymentBillDTO.getPaymentBillId());
        logsDTO.setRelatedBillNo(paymentBillDTO.getPaymentBillNo());
        logsDTO.setChannelCode(paymentBillDTO.getChannelCode());
        logsDTO.setChannelId(paymentBillDTO.getChannelId());
        logsDTO.setChannelName(paymentBillDTO.getChannelName());
        logsDTO.setOrderId(paymentBillDTO.getOrderId());
        logsDTO.setOrderNo(paymentBillDTO.getOrderNo());
        logsDTO.setPayType(PaymentTypeEnum.PAY.getCode());
        logsDTO.setThirdBillNo(paymentBillDTO.getExtBizNo());
        logsDTO.setDetail(paymentBillDTO.getGoodsNames());

        List<PaymentBillDetailDTO> dtoList = paymentBillDTO.getDetailDTOList();
        for (PaymentBillDetailDTO dto : dtoList) {
            logsDTO.setSubPayType(getSubPayType(dto.getSubject()));
            logsDTO.setActualPayAmount(dto.getActualPayAmount());

            logsDTO.setMemberId(paymentBillDTO.getPayerMemberId());
            logsDTO.setPayerFlg(true);
            logsDTO.setMemberName(paymentBillDTO.getPayerMemberName());
            logsDTO.setTargetMemberId(dto.getPayeeMemberId());
            logsDTO.setTargetMemberName(dto.getPayeeMemberName());
            saveBillLogs(logsDTO, operatorId);
            logsDTO.setBillLogsId(null);
            logsDTO.setMemberId(dto.getPayeeMemberId());
            logsDTO.setPayerFlg(false);
            logsDTO.setMemberName(dto.getPayeeMemberName());
            logsDTO.setTargetMemberId(paymentBillDTO.getPayerMemberId());
            logsDTO.setTargetMemberName(paymentBillDTO.getPayerMemberName());
            saveBillLogs(logsDTO, operatorId);
            logsDTO.setBillLogsId(null);
        }

        updateDeliverWayToPrepare(paymentBillDTO.getOrderId());
    }

    @Override
    public void logsPaymentByFreeze(PaymentBillDTO paymentBillDTO, String operatorId) {
        log.info(" -->> 记录冻结支付流水 {}", paymentBillDTO);
        if (checkRepeat(paymentBillDTO.getPaymentBillNo())) {
            return;
        }
        BillLogsDTO logsDTO = new BillLogsDTO();
        logsDTO.setRelatedBillId(paymentBillDTO.getPaymentBillId());
        logsDTO.setRelatedBillNo(paymentBillDTO.getPaymentBillNo());
        logsDTO.setChannelCode(paymentBillDTO.getChannelCode());
        logsDTO.setChannelId(paymentBillDTO.getChannelId());
        logsDTO.setChannelName(paymentBillDTO.getChannelName());
        logsDTO.setOrderId(paymentBillDTO.getOrderId());
        logsDTO.setOrderNo(paymentBillDTO.getOrderNo());
        logsDTO.setPayType(PaymentTypeEnum.PAY.getCode());
        logsDTO.setThirdBillNo(paymentBillDTO.getExtBizNo());
        logsDTO.setDetail(paymentBillDTO.getGoodsNames());

        List<PaymentBillDetailDTO> dtoList = paymentBillDTO.getDetailDTOList();
        for (PaymentBillDetailDTO dto : dtoList) {
            logsDTO.setSubPayType(getSubPayType(dto.getSubject()));
            logsDTO.setActualPayAmount(dto.getActualPayAmount());

            logsDTO.setMemberId(paymentBillDTO.getPayerMemberId());
            logsDTO.setPayerFlg(true);
            logsDTO.setMemberName(paymentBillDTO.getPayerMemberName());
            logsDTO.setTargetMemberId(dto.getPayeeMemberId());
            logsDTO.setTargetMemberName(dto.getPayeeMemberName());
            saveBillLogs(logsDTO, operatorId);
            logsDTO.setBillLogsId(null);
        }
    }

    @Override
    public void logsRepayment(PaymentBillDTO paymentBillDTO, String operatorId, String operatorName) {
        log.info(" -->> 记录授信还款流水 {}", paymentBillDTO);
        if (checkRepeat(paymentBillDTO.getPaymentBillNo())) {
            return;
        }
        BillLogsDTO logsDTO = new BillLogsDTO();
        logsDTO.setRelatedBillId(paymentBillDTO.getPaymentBillId());
        logsDTO.setRelatedBillNo(paymentBillDTO.getPaymentBillNo());
        logsDTO.setChannelCode(paymentBillDTO.getChannelCode());
        logsDTO.setChannelId(paymentBillDTO.getChannelId());
        logsDTO.setChannelName(paymentBillDTO.getChannelName());
        logsDTO.setOrderId(paymentBillDTO.getOrderId());
        logsDTO.setOrderNo(paymentBillDTO.getOrderNo());
        logsDTO.setPayType(PaymentTypeEnum.REPAYMENT.getCode());
        logsDTO.setThirdBillNo(paymentBillDTO.getExtBizNo());
        logsDTO.setDetail(paymentBillDTO.getGoodsNames());

        List<PaymentBillDetailDTO> dtoList = paymentBillDTO.getDetailDTOList();
        for (PaymentBillDetailDTO dto : dtoList) {
            logsDTO.setSubPayType(getSubPayType(dto.getSubject()));
            logsDTO.setActualPayAmount(dto.getActualPayAmount());
            logsDTO.setOperator(operatorName);

            logsDTO.setMemberId(paymentBillDTO.getPayerMemberId());
            logsDTO.setPayerFlg(true);
            logsDTO.setMemberName(paymentBillDTO.getPayerMemberName());
            logsDTO.setTargetMemberId(dto.getPayeeMemberId());
            logsDTO.setTargetMemberName(dto.getPayeeMemberName());
            saveBillLogs(logsDTO, operatorId);
            logsDTO.setBillLogsId(null);
            logsDTO.setMemberId(dto.getPayeeMemberId());
            logsDTO.setPayerFlg(false);
            logsDTO.setMemberName(dto.getPayeeMemberName());
            logsDTO.setTargetMemberId(paymentBillDTO.getPayerMemberId());
            logsDTO.setTargetMemberName(paymentBillDTO.getPayerMemberName());
            saveBillLogs(logsDTO, operatorId);
            logsDTO.setBillLogsId(null);
        }
    }

    @Override
    public void logsDriverPayInfo(DriverPayInfo driverPayInfo, String operator) {
        BillLogsDTO logsDTO = new BillLogsDTO();
        logsDTO.setRelatedBillId(driverPayInfo.getDriverPayInfoId());
        logsDTO.setRelatedBillNo(driverPayInfo.getPayinfoCode());
        logsDTO.setChannelCode(ChannelCodeEnum.GNETEPAY.getCode());
        logsDTO.setChannelName(ChannelCodeEnum.GNETEPAY.getMessage());

        logsDTO.setPayType(driverPayInfo.getPayinfoType());
        logsDTO.setThirdBillNo(driverPayInfo.getTradeBillNo());
        logsDTO.setDetail(driverPayInfo.getRemark());
        //---------------
        logsDTO.setSubPayType(getSubPayType(driverPayInfo.getSubject()));
        logsDTO.setActualPayAmount(driverPayInfo.getPayinfoAmount());

        //PaymentTypeEnum.DEPOSIT_REFUND 退还保证金
        //PaymentTypeEnum.PAY 平台结算费用
        if( PaymentTypeEnum.PAY.code().equals(driverPayInfo.getPayinfoType()) ||
                PaymentTypeEnum.DEPOSIT_REFUND.getCode().equals(driverPayInfo.getPayinfoType()) ){
            logsDTO.setPayerFlg(true);
            logsDTO.setMemberId(MemberPlatform.PLATFORM_MEMBERID.getId());
            logsDTO.setMemberName(MemberPlatform.PLATFORM_MEMBERID.getInfo());
            logsDTO.setTargetMemberId(driverPayInfo.getMemberId());
            logsDTO.setTargetMemberName(driverPayInfo.getAccountName());
        }else if(PaymentTypeEnum.DEPOSIT_PAY.code().equals(driverPayInfo.getPayinfoType())){
            logsDTO.setPayerFlg(true);
            logsDTO.setMemberId(driverPayInfo.getMemberId());
            logsDTO.setMemberName(driverPayInfo.getAccountName());
            logsDTO.setTargetMemberId(MemberPlatform.PLATFORM_MEMBERID.getId());
            logsDTO.setTargetMemberName(MemberPlatform.PLATFORM_MEMBERID.getInfo());
        }else if(PaymentTypeEnum.CASH_WITHDRAWAL.code().equals(driverPayInfo.getPayinfoType())){
            logsDTO.setPayerFlg(true);
            logsDTO.setMemberId(driverPayInfo.getMemberId());
            logsDTO.setMemberName(driverPayInfo.getAccountName());
            saveBillLogs(logsDTO, operator);
            logsDTO.setBillLogsId(null);
            return;
        }
        saveBillLogs(logsDTO, operator);
        logsDTO.setBillLogsId(null);

        //PaymentTypeEnum.DEPOSIT_REFUND 退还保证金
        //PaymentTypeEnum.PAY 平台结算费用
        if( PaymentTypeEnum.PAY.code().equals(driverPayInfo.getPayinfoType()) ||
                PaymentTypeEnum.DEPOSIT_REFUND.getCode().equals(driverPayInfo.getPayinfoType()) ){
            logsDTO.setPayerFlg(false);
            logsDTO.setMemberId(driverPayInfo.getMemberId());
            logsDTO.setMemberName(driverPayInfo.getAccountName());
            logsDTO.setTargetMemberId(MemberPlatform.PLATFORM_MEMBERID.getId());
            logsDTO.setTargetMemberName(MemberPlatform.PLATFORM_MEMBERID.getInfo());
        }else if(PaymentTypeEnum.DEPOSIT_PAY.code().equals(driverPayInfo.getPayinfoType())){
            logsDTO.setPayerFlg(false);
            logsDTO.setMemberId(MemberPlatform.PLATFORM_MEMBERID.getId());
            logsDTO.setMemberName(MemberPlatform.PLATFORM_MEMBERID.getInfo());
            logsDTO.setTargetMemberId(driverPayInfo.getMemberId());
            logsDTO.setTargetMemberName(driverPayInfo.getAccountName());
        }
        saveBillLogs(logsDTO, operator);
    }

    private String getSubPayType(String subject){
        if (CsStringUtils.isBlank(subject)) {
            return subject;
        }
        PayDetailTypeEnum itemEnum = PayDetailTypeEnum.getByCodeOrMessage(subject);
        if( itemEnum != null ){
            return itemEnum.getCode();
        }
        return subject;
    }

    @Override
    public Page<BillLogsDTO> pageBillLogs(PageQuery<BillLogsDTO> logsDTO) {
        int pageNum = logsDTO.getPageNum() == null ? 1 : logsDTO.getPageNum();
        int pageSize = logsDTO.getPageSize() == null ? 10 : logsDTO.getPageSize();
        Condition condition = getCondition(logsDTO.getQueryDTO());
        Page<BillLogs> page = PageMethod.startPage(pageNum, pageSize).doSelectPage(() -> mapper.selectByCondition(condition));
        updateOperator(page);
        Page<BillLogsDTO> list = BeanConvertUtils.convertPage(page, BillLogsDTO.class);
        updatePayTypeText(list);
        return list;
    }

    private static void updatePayTypeText(List<BillLogsDTO> dtos){
        for (BillLogsDTO dto : dtos) {
            PaymentTypeEnum anEnum = PaymentTypeEnum.getByCode(dto.getPayType());
            dto.setPayTypeText(anEnum == null ? dto.getPayType() : anEnum.getMessage());
            updatePayTypeText2(dto);
            dto.setSubPayType(CsStringUtils.isNotBlank(dto.getPayType()) && CsStringUtils.isBlank(dto.getSubPayType()) ? dto.getPayType() : dto.getSubPayType());
            dto.setSubPayTypeText(CsStringUtils.isNotBlank(dto.getPayTypeText()) && CsStringUtils.isBlank(dto.getSubPayTypeText()) ? dto.getPayTypeText() : dto.getSubPayTypeText());
        }
    }

    private static void updatePayTypeText2(BillLogsDTO dto) {
        if( dto.getSubPayType() != null && dto.getSubPayType().indexOf(",") != -1 ){
            StringJoiner subPayTypes = new StringJoiner(",");
            StringJoiner subPayTypeTexts = new StringJoiner(",");
            for (String item : dto.getSubPayType().split(",")) {
                PayDetailTypeEnum itemEnum = PayDetailTypeEnum.getByCodeOrMessage(item);
                if( itemEnum != null ){
                    subPayTypeTexts.add(itemEnum.getMessage());
                    subPayTypes.add(itemEnum.getCode());
                }else{
                    subPayTypeTexts.add(replace(item));
                    subPayTypes.add(item);
                }
            }
            dto.setSubPayType(subPayTypes.toString());
            dto.setSubPayTypeText(subPayTypeTexts.toString());
        }else {
            PayDetailTypeEnum payDetailTypeEnum = PayDetailTypeEnum.getByCodeOrMessage(dto.getSubPayType());
            dto.setSubPayType(payDetailTypeEnum == null ? dto.getSubPayType() : payDetailTypeEnum.getCode());
            dto.setSubPayTypeText(payDetailTypeEnum == null ? replace(dto.getSubPayType()) : payDetailTypeEnum.getMessage());
        }
    }

    private static Set<String> IGNORE_SET = Sets.newHashSet("erp","system_erp_sync");

    private void updateOperator(Page<BillLogs> dtos){
        if( CollectionUtils.isEmpty(dtos)){
            return;
        }
        List<String> createUserIdSet = dtos.stream()
                .filter(item -> CsStringUtils.isBlank(item.getOperator()))
                .filter(item -> CsStringUtils.isNotBlank(item.getCreateUser()))
                .filter(item->!IGNORE_SET.contains(item.getCreateUser()))
                .map(BillLogs::getCreateUser).distinct().toList();
        if( createUserIdSet.isEmpty() ){
            return;
        }
        List<AccountSimpleDTO> accountSimpleDTOS = accountService.findSimpleByIds(createUserIdSet);
        if( CollectionUtils.isEmpty(accountSimpleDTOS)){
            return;
        }
        Map<String, AccountSimpleDTO> accountSimpleDTOMap = accountSimpleDTOS.stream().collect(Collectors.toMap(AccountSimpleDTO::getAccountId, a -> a, (k1, k2) -> k1));
        dtos.stream().filter(item -> CsStringUtils.isNotBlank(item.getOperator())).forEach(item -> {

        });

        for (BillLogs item : dtos) {
            if (CsStringUtils.isNotBlank(item.getOperator()) || IGNORE_SET.contains(item.getCreateUser())) {
                continue;
            }
            AccountSimpleDTO accountSimpleDTO = accountSimpleDTOMap.get(item.getCreateUser());
            if( accountSimpleDTO != null ){
                item.setOperator(accountSimpleDTO.getAccountName());
            }
        }
    }

    /**
     * com.ecommerce.order.api.enums.BillSplitAmountTypeEnum描述更新 按紫云的要求  2020.2.6
     * LOGISTIC_AMOUNT("0", "物流款分账"), 物流款
     * OODS_AMOUNT("1", "货款分账"), 货款
     *  OTHER_AMOUNT("2","其他分账"); 其他
     * @param subPayTypeText
     * @return
     */
    private static String replace(String subPayTypeText){
        if( "物流款分账".equals(subPayTypeText)){
            return "物流款";
        }
        if( "货款分账".equals(subPayTypeText)){
            return "订单货款";
        }
        if( "其他分账".equals(subPayTypeText)){
            return "其他款";
        }
        return subPayTypeText;
    }

    private static Set<String> set = Sets.newHashSet("logistics","logistics_supplement","logistics_refund","物流款分账");
    /**
     * 卖家代收物流款的支付渠道
     */
    private static Set<String> set2 = Sets.newHashSet(
            ChannelCodeEnum.OFFLINE.getCode(),
            ChannelCodeEnum.CREDIT.getCode(),
            ChannelCodeEnum.ERP.getCode(),
            ChannelCodeEnum.WEIXIN.getCode(),
            ChannelCodeEnum.ALIPAY.getCode());

    private Condition getCondition(BillLogsDTO logsDTO) {
        log.info("======>logsDTO:{}", JSON.toJSONString(logsDTO));
        if( !MemberPlatform.PLATFORM_MEMBERID.getId().equals(logsDTO.getMemberId())
                && !MemberPlatform.PLATFORM_CARRIER_MEMBERID.getId().equals(logsDTO.getMemberId())
                && CsStringUtils.isBlank(logsDTO.getQueryMemberType())) {
            throw new BizException(PayCode.PAY_CUSTOM_ERR,"queryMemberType不可为空");
        }
        Condition condition = new Condition(BillLogs.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(DEL_FLG, false);

        updateConditionByPayType(logsDTO, condition, criteria);

        log.info("AFTER updateConditionByPayType: {}", JSON.toJSONString(logsDTO));

        if (CsStringUtils.isNotBlank(logsDTO.getMemberId())) {
            criteria.andEqualTo("memberId", logsDTO.getMemberId());
        }

        if (CsStringUtils.isNotBlank(logsDTO.getBillLogsNo())) {
            criteria.andLike("billLogsNo", "%" + logsDTO.getBillLogsNo() + "%");
        }
        if (CsStringUtils.isNotBlank(logsDTO.getSubPayType())) {
            criteria.andEqualTo(SUB_PAY_TYPE, logsDTO.getSubPayType());
        }
        if (CsStringUtils.isNotBlank(logsDTO.getChannelCode())) {
            criteria.andEqualTo(CHANNEL_CODE, logsDTO.getChannelCode());
        }

        if (CsStringUtils.isNotBlank(logsDTO.getTargetMemberId())) {
            criteria.andEqualTo("targetMemberId", logsDTO.getTargetMemberId());
        }
        if (CsStringUtils.isBlank(logsDTO.getTargetMemberId()) && CsStringUtils.isNotBlank(logsDTO.getTargetMemberName())) {
            criteria.andLike("targetMemberName", "%" + logsDTO.getTargetMemberName() + "%");
        }
        if (CsStringUtils.isNotBlank(logsDTO.getDetail())) {
            criteria.andLike("detail", "%" + logsDTO.getDetail() + "%");
        }
        if (logsDTO.getCreateTimeLeft() != null && logsDTO.getCreateTimeRight() != null) {
            criteria.andBetween(CREATE_TIME, DateUtil.getBeginDate(logsDTO.getCreateTimeLeft()), DateUtil.getEndDate(logsDTO.getCreateTimeRight()));
        }
        else if (logsDTO.getCreateTimeLeft() != null) {
            criteria.andGreaterThanOrEqualTo(CREATE_TIME, logsDTO.getCreateTimeLeft());
        }
        else if (logsDTO.getCreateTimeRight() != null) {
            criteria.andLessThanOrEqualTo(CREATE_TIME, DateUtil.getEndDate(logsDTO.getCreateTimeRight()));
        }else if( !CollectionUtils.isEmpty(logsDTO.getTime()) && logsDTO.getTime().size() ==2 ){
                criteria.andBetween(CREATE_TIME, DateUtil.getBeginDate(logsDTO.getTime().get(0)), DateUtil.getEndDate(logsDTO.getTime().get(1)));
        }
        condition.orderBy(CREATE_TIME).desc();
        return condition;
    }

    private void updateConditionByPayType(BillLogsDTO logsDTO, Condition condition, Example.Criteria criteria) {
        if (CsStringUtils.isNotBlank(logsDTO.getPayType())) {
            String payType = logsDTO.getPayType();
            boolean multiPayType = payType.contains(",");
            String[] list = multiPayType ? CsStringUtils.commaDelimitedListToStringArray(payType) : null;
            Set<String> paymentTypeCodeSet = multiPayType ? Arrays.stream(list).map(String::trim).collect(Collectors.toSet()) : Sets.newHashSet();

            boolean isPayAndRefund = paymentTypeCodeSet.contains(PaymentTypeEnum.PAY.getCode()) && paymentTypeCodeSet.contains(PaymentTypeEnum.REFUND.getCode());
            log.info("isPayAndRefund:{},paymentTypeCodeSet:{}",isPayAndRefund, JSON.toJSONString(paymentTypeCodeSet));
            //如果是卖家
            if(MemberTypeEnum.ENTERPRISE_SELLER.getCode().equals(logsDTO.getQueryMemberType()) ){
                updateSellerCondition(logsDTO, condition, criteria, payType, paymentTypeCodeSet, isPayAndRefund);

            }else//如果是承运商
            if(MemberTypeEnum.ENTERPRISE_CARRIER.getCode().equals(logsDTO.getQueryMemberType()) ){
                updateCarrierCondition(logsDTO,condition, criteria, payType, paymentTypeCodeSet, isPayAndRefund);
            }else
                //如果是平台
                if (MemberPlatform.PLATFORM_MEMBERID.getId().equals(logsDTO.getMemberId()) || MemberPlatform.PLATFORM_CARRIER_MEMBERID.getId().equals(logsDTO.getMemberId())) {
                    updatePlatformCondition(criteria, payType, multiPayType, paymentTypeCodeSet);
                }
            //如果是买家
            else {
                    updateBuyerCondition(criteria, payType, paymentTypeCodeSet, isPayAndRefund);
                }
        }else
        if(MemberTypeEnum.ENTERPRISE_CARRIER.getCode().equals(logsDTO.getQueryMemberType()) ){
            //目前的逻辑承运商没有交易的
            criteria.andEqualTo(PAY_TYPE, "noRecord");
        }
    }

    private void updateSellerCondition(BillLogsDTO logsDTO, Condition condition, Example.Criteria criteria, String payType, Set<String> paymentTypeCodeSet, boolean isPayAndRefund) {
        //交易日志查询
        if(   isPayAndRefund || PaymentTypeEnum.PAY.getCode().equals(payType) || PaymentTypeEnum.REFUND.getCode().equals(payType) ){
            if( isPayAndRefund ){
                criteria.andIn(PAY_TYPE, paymentTypeCodeSet).andEqualTo(PAYER_FLG,false);
            }else{
                criteria.andEqualTo(PAY_TYPE, payType).andEqualTo(PAYER_FLG,false);
            }
            //支付类型不是物流款，或者是卖家配送
            Example.Criteria criteria2 = newCondition().createCriteria().orNotIn(SUB_PAY_TYPE,set).orEqualTo(DELIVER_WAY,"030060200")
            //或者是平台配送且支付渠道需要卖家代收
            .orCondition(" (deliver_way='030060300' and channel_code in('offline','credit','erp','weixin','alipay'))");

            condition.and(criteria2);
            //充值、提现
        }else if( PaymentTypeEnum.RECHARGE.getCode().equals(payType) || PaymentTypeEnum.CASH_WITHDRAWAL.getCode().equals(payType) ){
            criteria.andEqualTo(PAY_TYPE, payType).andEqualTo(CHANNEL_CODE, ChannelCodeEnum.PINGANJZ.getCode());
            //还款
        }else if( PaymentTypeEnum.REPAYMENT.getCode().equals(logsDTO.getPayType()) ){
            criteria.andEqualTo(PAY_TYPE, payType).andEqualTo(PAYER_FLG,false);
        } else if("pay2pay".equals(payType)) {
            // 授信列表 - 支付(临时) by zhougang
            Set<String> payTypes = new HashSet<>(2);
            payTypes.add("pay");
            payTypes.add("refund");
            criteria.andIn(PAY_TYPE, payTypes);
        } else {
            throw new BizException(BasicCode.UNDEFINED_ERROR,"请完善查询方法");
        }
    }

    private void updateCarrierCondition(BillLogsDTO logsDTO,Condition condition, Example.Criteria criteria, String payType, Set<String> paymentTypeCodeSet, boolean isPayAndRefund) {
        //承运商和卖家都是查询收款账号流水，承运商只查询物流款相关流水
        //交易日志查询
        if(   isPayAndRefund || PaymentTypeEnum.PAY.getCode().equals(payType) || PaymentTypeEnum.REFUND.getCode().equals(payType) ){
            if( isPayAndRefund ){
                criteria.andIn(PAY_TYPE, paymentTypeCodeSet).andEqualTo(PAYER_FLG,false);
            }else{
                criteria.andEqualTo(PAY_TYPE, payType).andEqualTo(PAYER_FLG,false);
            }
            //与卖家条件相反
            //支付类型是物流款，且非卖家配送，且非平台配送或支付渠道不要卖家代收
            criteria.andIn(SUB_PAY_TYPE,set).andNotEqualTo(DELIVER_WAY,"030060200");
            condition.and(condition.createCriteria().andNotIn("channelCode",set2).orNotEqualTo(DELIVER_WAY,"030060300"));
            //充值、提现
        }else if( PaymentTypeEnum.RECHARGE.getCode().equals(payType) || PaymentTypeEnum.CASH_WITHDRAWAL.getCode().equals(payType) ){
            criteria.andEqualTo(PAY_TYPE, payType).andEqualTo(CHANNEL_CODE, ChannelCodeEnum.PINGANJZ.getCode());
            //还款
        }else if( PaymentTypeEnum.REPAYMENT.getCode().equals(logsDTO.getPayType()) ){
            criteria.andEqualTo(PAY_TYPE, payType).andEqualTo(PAYER_FLG,false);
        }else {
            throw new BizException(BasicCode.UNDEFINED_ERROR,"请完善查询方法");
        }
    }

    private void updatePlatformCondition(Example.Criteria criteria, String payType, boolean multiPayType, Set<String> paymentTypeCodeSet) {
        if( multiPayType ){
            criteria.andIn(PAY_TYPE, paymentTypeCodeSet);
        }else{
            criteria.andEqualTo(PAY_TYPE, payType);
        }
    }

    private void updateBuyerCondition(Example.Criteria criteria, String payType, Set<String> paymentTypeCodeSet, boolean isPayAndRefund) {
        //交易、退款
        if( isPayAndRefund || PaymentTypeEnum.PAY.getCode().equals(payType) || PaymentTypeEnum.REFUND.getCode().equals(payType) ){
            if( isPayAndRefund ){
                criteria.andIn(PAY_TYPE, paymentTypeCodeSet).andEqualTo(PAYER_FLG,true);
            }else{
                criteria.andEqualTo(PAY_TYPE, payType).andEqualTo(PAYER_FLG,true);
            }
        }
        //充值、提现
        else if( PaymentTypeEnum.RECHARGE.getCode().equals(payType) || PaymentTypeEnum.CASH_WITHDRAWAL.getCode().equals(payType) ){
            criteria.andEqualTo(PAY_TYPE, payType).andEqualTo(CHANNEL_CODE, ChannelCodeEnum.PINGANJZPAY.getCode());
        } else if ("pay2pay".equals(payType)) {
            // 授信列表 - 支付(临时) by zhougang
            Set<String> payTypes = new HashSet<>(2);
            payTypes.add("pay");
            payTypes.add("refund");
            criteria.andIn(PAY_TYPE, payTypes);
        }
    }

    private BillLogsDTO vo2Dto(BillLogs vo) {
        return BeanConvertUtils.convert(vo, BillLogsDTO.class);
    }

    private List<BillLogsDTO> vo2DtoByList(List<BillLogs> vo) {
        if (CollectionUtils.isEmpty(vo)) {
            return Lists.newArrayList();
        }
        return vo.stream().map(this::vo2Dto).toList();
    }

    private BillLogs dto2Vo(BillLogsDTO dto) {
        return BeanConvertUtils.convert(dto, BillLogs.class);
    }
}
