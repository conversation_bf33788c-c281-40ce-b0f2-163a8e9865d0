package com.ecommerce.pay.v2.biz.impl;

import com.ecommerce.common.enums.BusinessCode;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.service.common.BaseBiz;
import com.ecommerce.common.service.common.bsid.CommonBusinessIdGenerator;
import com.ecommerce.common.utils.BeanConvertUtils;
import com.github.pagehelper.page.PageMethod;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.pay.api.v2.dto.bill.CashWithdrawalBillDTO;
import com.ecommerce.pay.api.v2.enums.PaymentStatusEnum;
import com.ecommerce.pay.v2.biz.ICashWithdrawalBillBiz;
import com.ecommerce.pay.v2.dao.mapper.CashWithdrawalBillMapper;
import com.ecommerce.pay.v2.dao.vo.CashWithdrawalBill;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @created 9:33 03/09/2019
 * @description TODO
 */
@Service
public class CashWithdrawalBillBiz extends BaseBiz<CashWithdrawalBill> implements ICashWithdrawalBillBiz {

    @Autowired
    private CashWithdrawalBillMapper mapper;

    @Autowired
    private CommonBusinessIdGenerator codeGenerator;

    @Override
    public void create(CashWithdrawalBillDTO billDTO, String operatorId) {
        billDTO.setCashWithdrawalBillNo(BusinessCode.WITHDRAWAL.getCode() + codeGenerator.incrementCode());
        CashWithdrawalBill bill = BeanConvertUtils.convert(billDTO, CashWithdrawalBill.class);
        save(bill, operatorId);
        billDTO.setCashWithdrawalBillId(bill.getCashWithdrawalBillId());
    }

    @Override
    public Optional<CashWithdrawalBillDTO> findByNo(String billNo) {
        if (CsStringUtils.isEmpty(billNo)) {
            return Optional.empty();
        }
        CashWithdrawalBill bill = new CashWithdrawalBill();
        bill.setCashWithdrawalBillNo(billNo);
        bill.setDelFlg(false);
        List<CashWithdrawalBill> select = mapper.select(bill);
        if (CollectionUtils.isEmpty(select)) {
            return Optional.empty();
        }
        return Optional.ofNullable(BeanConvertUtils.convert(select.get(0), CashWithdrawalBillDTO.class));
    }

    @Override
    public Optional<CashWithdrawalBillDTO> findById(String billId) {
        if (CsStringUtils.isEmpty(billId)) {
            return Optional.empty();
        }
        CashWithdrawalBill bill = mapper.selectByPrimaryKey(billId);
        return Optional.ofNullable(BeanConvertUtils.convert(bill, CashWithdrawalBillDTO.class));
    }

    @Override
    public void update(CashWithdrawalBillDTO billDTO, String operatorId) {
        if (CsStringUtils.isEmpty(billDTO.getCashWithdrawalBillId())) {
            return;
        }
        CashWithdrawalBill bill = BeanConvertUtils.convert(billDTO, CashWithdrawalBill.class);
        Condition condition = new Condition(CashWithdrawalBill.class);
        condition.createCriteria()
                .andEqualTo("cashWithdrawalBillId", billDTO.getCashWithdrawalBillId())
                .andEqualTo("delFlg", false);
        setOperInfo(bill, operatorId, false);
        mapper.updateByConditionSelective(bill, condition);
    }

    @Override
    public void updateStatus(String id, String status, String operatorId) {

    }

    @Override
    public List<CashWithdrawalBillDTO> findUnCompleteBillByMemberId(String memberId,String channelId) {
        if (CsStringUtils.isBlank(memberId)) {
            throw new BizException(BasicCode.PARAM_NULL, "memberId");
        }
        CashWithdrawalBill bill = new CashWithdrawalBill();
        bill.setDelFlg(false);
        bill.setPayerMemberId(memberId);
        bill.setChannelId(channelId);
        bill.setStatus(PaymentStatusEnum.PAY_ING.getCode());
        List<CashWithdrawalBill> select = mapper.select(bill);
        return CollectionUtils.isEmpty(select) ? Lists.newArrayList() :
                select.stream().map(item->BeanConvertUtils.convert(item, CashWithdrawalBillDTO.class)).toList();
    }

    @Override
    public List<CashWithdrawalBillDTO> pageInfo(String payerMemberId,int pageNum) {
        Condition condition = new Condition(CashWithdrawalBill.class);
        Example.Criteria criteria = condition.createCriteria().andEqualTo("delFlg",false).andEqualTo("status",PaymentStatusEnum.PAY_ING.getCode());
        if (CsStringUtils.isNotBlank(payerMemberId)) {
            criteria.andEqualTo("payerMemberId",payerMemberId);
        }
        PageMethod.startPage(pageNum, 1000, false,false,null);
        List<CashWithdrawalBill> list = this.mapper.selectByCondition(condition);
        return CollectionUtils.isEmpty(list) ? Lists.newArrayList() :
                list.stream().map(item->BeanConvertUtils.convert(item, CashWithdrawalBillDTO.class)).toList();
    }
}
