package com.ecommerce.pay.v2.biz.impl;

import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.service.common.BaseBiz;
import com.ecommerce.common.service.common.bsid.CommonBusinessIdGenerator;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.pay.api.exception.PayCode;
import com.ecommerce.pay.api.v2.dto.ChannelCardDTO;
import com.ecommerce.pay.api.v2.enums.CardTypeEnum;
import com.ecommerce.pay.api.v2.enums.ChannelCardStatusEnum;
import com.ecommerce.pay.v2.biz.IChannelCardBiz;
import com.ecommerce.pay.v2.dao.mapper.ChannelCardMapper;
import com.ecommerce.pay.v2.dao.vo.ChannelCard;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class ChannelCardBiz extends BaseBiz<ChannelCard> implements IChannelCardBiz {
	@Autowired
	private CommonBusinessIdGenerator codeGenerator;
	@Autowired
	private ChannelCardMapper channelCardMapper;

	public static final String DEFAULT_SIGN = "default";

	private static final String MEMBER_CHANNEL_ID = "memberChannelId";

	@Override
	public void addChannelCard(ChannelCardDTO channelCardDTO, String operator) {
		log.info(" 添加银行卡 {} ", channelCardDTO);
		if (channelCardDTO == null) {
			throw new BizException(PayCode.DATA_NOT_FOUND);
		}
		String cardStatus = channelCardDTO.getChannelCardStatus();
        if (!CsStringUtils.isEmpty(cardStatus) && ChannelCardStatusEnum.OPEN.getCode().equalsIgnoreCase(cardStatus)) {
			ChannelCard dto = new ChannelCard();
			dto.setMemberChannelId(channelCardDTO.getMemberChannelId());
			dto.setChannelCardStatus(ChannelCardStatusEnum.OPEN.getCode());
			dto.setDelFlg(false);
			int count = channelCardMapper.selectCount(dto);
			if (count < 1) {
				channelCardDTO.setExt1(DEFAULT_SIGN);
			}
		}
		ChannelCard channelCard = convert(channelCardDTO);
		channelCard.setChannelCardCode(codeGenerator.incrementCode());
        if (CsStringUtils.isEmpty(channelCard.getBankBaseName())) {
			channelCard.setBankBaseName(channelCard.getBankName());
		}
		save(channelCard, operator);
		channelCardDTO.setChannelCardId(channelCard.getChannelCardId());

		resolveDefaultCard(channelCardDTO.getMemberChannelId());
	}

	@Override
	public void removeChannelCard(String channelCardId, String operator) {
		log.info(" 移除 银行卡 {} ", channelCardId);
        if (CsStringUtils.isEmpty(channelCardId)) {
			throw new BizException(PayCode.DATA_NOT_FOUND);
		}
		ChannelCard offlineChannelInfo = new ChannelCard();
		offlineChannelInfo.setChannelCardId(channelCardId);
		offlineChannelInfo.setDelFlg(true);
		updateSelective(offlineChannelInfo);

		ChannelCard card = channelCardMapper.selectByPrimaryKey(channelCardId);
		resolveDefaultCard(card.getMemberChannelId());
	}

	@Override
	public ChannelCardDTO getChannelCardById(String channelCardId) {
		log.info(" 获取 银行卡 详情 {} ", channelCardId);
        if (CsStringUtils.isEmpty(channelCardId)) {
			throw new BizException(BasicCode.PARAM_NULL, "channelCardId");
		}
		return this.convertToDTO(this.get(channelCardId));
	}

	private ChannelCardDTO convertToDTO(ChannelCard vo) {
		if (vo == null) {
			return null;
		}
		ChannelCardDTO channelCardDTO = new ChannelCardDTO();
		BeanUtils.copyProperties(vo, channelCardDTO);
		channelCardDTO.setMomeString(CardTypeEnum.getMsgByCode(vo.getMome()));
		return channelCardDTO;
	}

	private ChannelCard convert(ChannelCardDTO dto) {
		if (dto == null) {
			return null;
		}
		ChannelCard paymentBill = new ChannelCard();
		BeanUtils.copyProperties(dto, paymentBill);
		return paymentBill;
	}

	@Override
	public void removeAllChannelCardByChannelId(String memberChannelId, String operator) {
		log.info(" 移除 银行卡 {} ", memberChannelId);
        if (CsStringUtils.isEmpty(memberChannelId)) {
			throw new BizException(PayCode.DATA_NOT_FOUND);
		}
		channelCardMapper.removeAllCardByMemberChannelId(memberChannelId, operator);

	}

	@Override
	public boolean checkReduplicate(String bankAccount) {
        if (CsStringUtils.isEmpty(bankAccount)) {
			throw new BizException(BasicCode.UNDEFINED_ERROR, "银行卡号为空");
		}
		ChannelCard channelCard = new ChannelCard();
		channelCard.setBankAccount(bankAccount);
		channelCard.setChannelCardStatus(ChannelCardStatusEnum.OPEN.getCode());
		channelCard.setDelFlg(false);
		int t = channelCardMapper.selectCount(channelCard);
		return t > 0;
	}

	@Override
	public void updateCardStatusById(String channelCardId, String status) {
		Objects.requireNonNull(channelCardId, "银行卡ID");
		ChannelCard channelCard = new ChannelCard();
		channelCard.setChannelCardId(channelCardId);
		channelCard.setChannelCardStatus(status);
		channelCardMapper.updateByPrimaryKeySelective(channelCard);

		ChannelCard card = channelCardMapper.selectByPrimaryKey(channelCardId);
		resolveDefaultCard(card.getMemberChannelId());
	}

	@Override
	public void updateCardStatus(String memberId, String memberChannelId, String status) {
		Objects.requireNonNull(memberId, "会员ID");
		Objects.requireNonNull(memberChannelId, "会员渠道ID");
		Condition condition = newCondition();
		condition.createCriteria().andEqualTo("memberId",memberId).andEqualTo("memberChannelId",memberChannelId);
		ChannelCard channelCard = new ChannelCard();
		channelCard.setChannelCardStatus(status);
		channelCardMapper.updateByConditionSelective(channelCard,condition);
	}

	@Override
	public List<ChannelCard> findByBankAccount(String bankAccount) {
        if (CsStringUtils.isEmpty(bankAccount)) {
			throw new BizException(BasicCode.UNDEFINED_ERROR, "银行卡号为空");
		}
		ChannelCard channelCard = new ChannelCard();
		channelCard.setBankAccount(bankAccount);
		channelCard.setDelFlg(false);
		return channelCardMapper.select(channelCard);
	}

	@Override
	public List<ChannelCardDTO> getChannelCardByMemberChannelId(String memberChannelId) {
        if (CsStringUtils.isEmpty(memberChannelId)) {
			throw new BizException(BasicCode.PARAM_NULL, MEMBER_CHANNEL_ID);
		}
		log.info(" 查询 会员渠道下的  银行卡 {} ", memberChannelId);
		Condition condition = new Condition(ChannelCard.class);
		Example.Criteria criteria = condition.createCriteria();
		criteria.andEqualTo(MEMBER_CHANNEL_ID, memberChannelId);
		criteria.andEqualTo("delFlg", 0);
		List<ChannelCard> voList = findByCondition(condition);
		if (CollectionUtils.isEmpty(voList)) {
			return Collections.emptyList();
		}
		return voList.stream().map(this::convertToDTO).peek(this::hideAccountNo).toList();
	}

	@Override
	public int getChannelCardCountByMemberChannelId(String memberChannelId) {
        if (CsStringUtils.isEmpty(memberChannelId)) {
			throw new BizException(BasicCode.PARAM_NULL, MEMBER_CHANNEL_ID);
		}
		Condition condition = new Condition(ChannelCard.class);
		Example.Criteria criteria = condition.createCriteria();
		criteria.andEqualTo(MEMBER_CHANNEL_ID, memberChannelId);
		criteria.andEqualTo("channelCardStatus", ChannelCardStatusEnum.OPEN.getCode());
		criteria.andEqualTo("delFlg", 0);
		return channelCardMapper.selectCountByCondition(condition);
	}

	@Override
	public int getChannelCardCountByMemberChannelId2(String memberChannelId) {
        if (CsStringUtils.isEmpty(memberChannelId)) {
			throw new BizException(BasicCode.PARAM_NULL, MEMBER_CHANNEL_ID);
		}
		Condition condition = new Condition(ChannelCard.class);
		Example.Criteria criteria = condition.createCriteria();
		criteria.andEqualTo(MEMBER_CHANNEL_ID, memberChannelId);
		criteria.andEqualTo("delFlg", 0);
		return channelCardMapper.selectCountByCondition(condition);
	}

	private void hideAccountNo(ChannelCardDTO dto) {
		String bankAccount = dto.getBankAccount();
		dto.setBankAccount(hideAccountNo(bankAccount));
	}

	@Override
	public String hideAccountNo(String bankAccount) {
		int len = bankAccount.length();
        if (CsStringUtils.isEmpty(bankAccount) || len <= 8) {
			return "";
		}
		String prefix = bankAccount.substring(0, 4);
		String suffix = bankAccount.substring(len - 4);
		int i = len - 8;
		StringBuilder sb = new StringBuilder(prefix);
		while (i-- > 0) {
			sb.append("*");
		}
		sb.append(suffix);
		return sb.toString();
	}

	@Override
	public void updateDefaultChannelCard(String memberChannelId, String channelCardId, String operatorId) {
        if (CsStringUtils.isEmpty(memberChannelId)) {
			throw new BizException(BasicCode.PARAM_NULL, MEMBER_CHANNEL_ID);
		}
        if (CsStringUtils.isEmpty(channelCardId)) {
			throw new BizException(BasicCode.PARAM_NULL, "channelCardId");
		}
		ChannelCard query1 = new ChannelCard();
		query1.setChannelCardId(channelCardId);
		query1.setMemberChannelId(memberChannelId);
		List<ChannelCard> cards = channelCardMapper.select(query1);
		if (CollectionUtils.isEmpty(cards)) {
			throw new BizException(BasicCode.UNDEFINED_ERROR, "银行卡不存在");
		}
		ChannelCard channelCard = cards.get(0);
		if (!ChannelCardStatusEnum.OPEN.getCode().equals(channelCard.getChannelCardStatus())) {
			throw new BizException(BasicCode.UNDEFINED_ERROR, "未认证的银行卡无法进行此操作");
		}
		boolean doUpdate = true;
		ChannelCard query = new ChannelCard();
		query.setMemberChannelId(memberChannelId);
		query.setExt1(DEFAULT_SIGN);
		query.setDelFlg(false);
		List<ChannelCard> select = channelCardMapper.select(query);
		if (!CollectionUtils.isEmpty(select)) {
			for (ChannelCard card : select) {
				if (card.getChannelCardId().equals(channelCardId)) {
					log.info("要设置的默认银行卡与原默认银行卡相同");
					doUpdate = false;
				} else {
					ChannelCard t = new ChannelCard();
					t.setChannelCardId(card.getChannelCardId());
					t.setExt1("-");
					setOperInfo(t, operatorId, false);
					channelCardMapper.updateByPrimaryKeySelective(t);
				}
			}
		}
		if (doUpdate) {
			channelCard.setExt1(DEFAULT_SIGN);
			setOperInfo(channelCard, operatorId, false);
			channelCardMapper.updateByPrimaryKeySelective(channelCard);
		}
	}

	private void resolveDefaultCard(String memberChannelId) {
        if (CsStringUtils.isEmpty(memberChannelId)) {
			return;
		}
		Condition condition = new Condition(ChannelCard.class);
		Example.Criteria criteria = condition.createCriteria();
		criteria.andEqualTo(MEMBER_CHANNEL_ID, memberChannelId);
		criteria.andEqualTo("channelCardStatus", ChannelCardStatusEnum.OPEN.getCode());
		criteria.andEqualTo("delFlg", false);
		List<ChannelCard> cards = findByCondition(condition);

		if (CollectionUtils.isEmpty(cards)) {
			return;
		}
		if (cards.size() == 1) {
			ChannelCard card = cards.get(0);
			if (DEFAULT_SIGN.equals(card.getExt1())) {
				return;
			}
			ChannelCard t = new ChannelCard();
			t.setChannelCardId(card.getChannelCardId());
			t.setExt1(DEFAULT_SIGN);
			setOperInfo(t, "sys_auto_update", false);
			channelCardMapper.updateByPrimaryKeySelective(t);
			return;
		}
		boolean exist = false;
		for (ChannelCard card : cards) {
			if (DEFAULT_SIGN.equals(card.getExt1())) {
				if (exist) {
					ChannelCard t = new ChannelCard();
					t.setChannelCardId(card.getChannelCardId());
					t.setExt1("-");
					setOperInfo(t, "sys_auto_update", false);
					channelCardMapper.updateByPrimaryKeySelective(t);
				}
				exist = true;
			}
		}
	}
}
