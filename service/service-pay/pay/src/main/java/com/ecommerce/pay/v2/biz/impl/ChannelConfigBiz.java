package com.ecommerce.pay.v2.biz.impl;

import com.ecommerce.common.service.RedisService;
import com.ecommerce.common.service.common.BaseBiz;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.pay.api.v2.dto.ChannelConfigDTO;
import com.ecommerce.pay.api.v2.enums.ChannelCodeEnum;
import com.ecommerce.pay.api.v2.enums.CustomerEnum;
import com.ecommerce.pay.v2.biz.IChannelConfigBiz;
import com.ecommerce.pay.v2.dao.vo.ChannelConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @Date 05/12/2018 14:23
 */
@Service
@Slf4j
public class ChannelConfigBiz extends BaseBiz<ChannelConfig> implements IChannelConfigBiz, CommandLineRunner {

    @Autowired
    private RedisService redisService;

    private static final String CHANNEL_STATUS = "channelStatus";
    private static final String DEL_FLG = "delFlg";

    @Override
    public ChannelConfigDTO findChannelConfigByType(String type) {

        Condition condition = new Condition(ChannelConfig.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(CHANNEL_STATUS,1);
        criteria.andEqualTo("channelType",type);
        criteria.andEqualTo(DEL_FLG,0);
        List<ChannelConfig> voList = findByCondition(condition);
        if(CollectionUtils.isEmpty(voList)){
            return null;
        }
        return convertToDTO(voList.get(0));
    }

    @Override
    public ChannelConfigDTO findById(String channelId) {

        Condition condition = new Condition(ChannelConfig.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(CHANNEL_STATUS,1);
        criteria.andEqualTo("channelId",channelId);
        criteria.andEqualTo(DEL_FLG,0);
        List<ChannelConfig> voList = findByCondition(condition);
        if(voList == null || voList.isEmpty()){
            return null;
        }
        return convertToDTO(voList.get(0));
    }

    @Override
    public List<ChannelConfigDTO> findByIds(List<String> channelIds) {
        if (CollectionUtils.isEmpty(channelIds)) {
            return new ArrayList<>();
        }
        Condition condition = new Condition(ChannelConfig.class);
        condition.createCriteria()
                .andIn("channelId", channelIds)
                .andEqualTo(DEL_FLG, false);
        List<ChannelConfig> configList = findByCondition(condition);
        return configList.stream().map(ChannelConfigBiz::convertToDTO).toList();
    }

    @Override
    public List<ChannelConfigDTO> findByReceiveId(String receiveId) {
        if (CsStringUtils.isEmpty(receiveId)) {
            return new ArrayList<>();
        }
        ChannelConfig channelConfig = new ChannelConfig();
        channelConfig.setReceiveChannelId(receiveId);
        channelConfig.setChannelStatus(true);
        channelConfig.setDelFlg(false);
        List<ChannelConfig> channelConfigs = find(channelConfig);
        if (CollectionUtils.isEmpty(channelConfigs)) {
            return new ArrayList<>();
        }
        return channelConfigs.stream().map(ChannelConfigBiz::convertToDTO).toList();
    }

    @Override
    public ChannelConfigDTO findByChannelCode(String channelCode) {

        Condition condition = new Condition(ChannelConfig.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(CHANNEL_STATUS,1);
        criteria.andEqualTo("channelCode",channelCode);
        criteria.andEqualTo(DEL_FLG,0);
        List<ChannelConfig> voList = findByCondition(condition);
        if(CollectionUtils.isEmpty(voList)){
            return null;
        }
        return convertToDTO(voList.get(0));
    }

    @Override
    public List<ChannelConfigDTO> getPlatformAvailChannels() {
        String key = "pay:getPlatformAvailChannels";
        try {
            List<ChannelConfigDTO> o = redisService.get(key, List.class);
            if (o != null) {
                return o;
            }
        } catch (Exception e) {
            log.error(e.getMessage(),e);
        }

        Condition condition = new Condition(ChannelConfig.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(CHANNEL_STATUS,1);
        criteria.andEqualTo(DEL_FLG,0);
        List<ChannelConfig> voList = findByCondition(condition);
        if(CollectionUtils.isEmpty(voList)){
            return Collections.emptyList();
        }
        List<ChannelConfigDTO> dtoList = new ArrayList<>();
        for(ChannelConfig channelConfig : voList){
            dtoList.add(convertToDTO(channelConfig));
        }
        try {
            redisService.setex(key, 7200L, dtoList);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
        }
        return dtoList;
    }

    /**
     *系统重启后刷新缓存
     */
    @Override
    public void run(String... args) throws Exception {
        String key = "pay:getPlatformAvailChannels";
        Condition condition = new Condition(ChannelConfig.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(CHANNEL_STATUS,1);
        criteria.andEqualTo(DEL_FLG,0);
        List<ChannelConfig> voList = findByCondition(condition);
        if(CollectionUtils.isEmpty(voList)){
            return ;
        }
        List<ChannelConfigDTO> dtoList = new ArrayList<>();
        for(ChannelConfig channelConfig : voList){
            dtoList.add(convertToDTO(channelConfig));
        }
        try {
            redisService.setex(key, 7200L, dtoList);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
        }
    }

    @Override
    public List<ChannelConfigDTO> getCustomerAvailChannels(CustomerEnum customer) {
        Condition condition = new Condition(ChannelConfig.class);
        Example.Criteria criteria = condition.createCriteria();
        if(customer.getCode().equals(CustomerEnum.SELLER.getCode())){
            criteria.andEqualTo("needPayeeReg",1);
        }else if(customer.getCode().equals(CustomerEnum.BUYER.getCode())){
            criteria.andEqualTo("needPayerReg",1);
        }
        criteria.andEqualTo(CHANNEL_STATUS,1);
        criteria.andEqualTo(DEL_FLG,0);
        List<ChannelConfig> voList = findByCondition(condition);
        if(CollectionUtils.isEmpty(voList)){
            return Collections.emptyList();
        }
        List<ChannelConfigDTO> dtoList = new ArrayList<>();
        for(ChannelConfig channelConfig:voList){
            dtoList.add(convertToDTO(channelConfig));
        }
        return dtoList;
    }

    public static ChannelConfigDTO convertToDTO(ChannelConfig channelConfig) {
        if(channelConfig == null){
            return null;
        }
        ChannelConfigDTO channelConfigDTO = new ChannelConfigDTO();
        BeanUtils.copyProperties(channelConfig,channelConfigDTO);
        //渠道名称，值集重复展示 后端对聚合支付另起别名字段，前端可自由选择
        channelConfigDTO.setChannelNameAlias(channelConfigDTO.getChannelName());
        if(ChannelCodeEnum.PINGANJZWX.getCode().equals(channelConfigDTO.getChannelCode())){
            channelConfigDTO.setChannelNameAlias("聚合支付(微信)");
        }else
        if(ChannelCodeEnum.PINGANJZZFB.getCode().equals(channelConfigDTO.getChannelCode())){
            channelConfigDTO.setChannelNameAlias("聚合支付(支付宝)");
        }else if(ChannelCodeEnum.PINGANJZ.getCode().equals(channelConfigDTO.getChannelCode())){
            channelConfigDTO.setChannelNameAlias("钱包支付(收款)");
        }else if(ChannelCodeEnum.PINGANJZPAY.getCode().equals(channelConfigDTO.getChannelCode())){
            channelConfigDTO.setChannelNameAlias("钱包支付(付款)");
        }

        return channelConfigDTO;
    }
}
