package com.ecommerce.pay.v2.biz.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.ecommerce.common.service.common.BaseBiz;
import com.ecommerce.logistics.api.enums.LogisticsMessageTypeEnum;
import com.ecommerce.mq.core.MQMessage;
import com.ecommerce.mq.core.service.IMQProducer;
import com.ecommerce.pay.api.constant.PayNumberConstant;
import com.ecommerce.pay.api.v2.dto.driver.WaybillLogisticsInfoDTO;
import com.ecommerce.pay.api.v2.dto.driver.WaybillLogisticsInfoQueryDTO;
import com.ecommerce.pay.api.v2.dto.driver.WithdrawLogQueryDTO;
import com.ecommerce.pay.api.v2.dto.electricInvoiceAccount.PageQuery;
import com.ecommerce.pay.api.v2.enums.PaymentStatusEnum;
import com.ecommerce.pay.v2.biz.IDriverCarriageItemBiz;
import com.ecommerce.pay.v2.dao.mapper.DriverCarriageItemMapper;
import com.ecommerce.pay.v2.dao.vo.DriverCarriageItem;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class DriverCarriageItemBiz extends BaseBiz<DriverCarriageItem> implements IDriverCarriageItemBiz {

    private final DriverCarriageItemMapper driverCarriageItemMapper;

    @Autowired
    @Qualifier("rabbitProducer")
    private IMQProducer producer;

    @Override
    public List<DriverCarriageItem> findByWaybill(List<String> waybillNum) {
        if(CollectionUtils.isEmpty(waybillNum)){
            return Lists.newArrayList();
        }
        Condition condition = new Condition(DriverCarriageItem.class);
        if( waybillNum.size() == 1 ){
            condition.createCriteria().andEqualTo("waybillNum",waybillNum.iterator().next()).andEqualTo("delFlg",false);
        }else{
            condition.createCriteria().andIn("waybillNum",waybillNum).andEqualTo("delFlg",false);
        }
        return findByCondition(condition);
    }

    //@ApiOperation("司机app接口-账户明细") 根据账号id查询
    @Override
    public PageInfo<WaybillLogisticsInfoDTO> pageWaybillLogisticsInfo(PageQuery<WithdrawLogQueryDTO> dto) {
        log.info("pageWaybillLogisticsInfo: {}", JSON.toJSONString(dto));
        if (dto == null || dto.getQueryDTO() == null || CsStringUtils.isBlank(dto.getQueryDTO().getAccountId())) {
            return new PageInfo<>(Lists.newArrayList());
        }
        WaybillLogisticsInfoQueryDTO queryDTO = new WaybillLogisticsInfoQueryDTO();
        queryDTO.setAccountId(dto.getQueryDTO().getAccountId());
        queryDTO.setPayStatus(dto.getQueryDTO().getPayStatus());
        PageQuery<WaybillLogisticsInfoQueryDTO> query = new PageQuery<>();
        query.setPageSize(ObjectUtil.defaultIfNull(dto.getPageSize(), PayNumberConstant.TEN_INTEGER));
        query.setPageNum(ObjectUtil.defaultIfNull(dto.getPageNum(), PayNumberConstant.DEFAULT_PAGE_NUM));
        query.setQueryDTO(queryDTO);
        return pageDriverCostItem(query);
    }

    //@ApiOperation("平台接口-个体司机运费管理-司机运费明细数据-翻页查询")
    @Override
    public PageInfo<WaybillLogisticsInfoDTO> pageDriverCostItem(PageQuery<WaybillLogisticsInfoQueryDTO> dto) {
        log.info("pageDriverCostItem: {}",JSON.toJSONString(dto));
        Condition condition = new Condition(DriverCarriageItem.class);
        Example.Criteria criteria = condition.createCriteria();
        if (CsStringUtils.isNotBlank(dto.getQueryDTO().getAccountId())) {
            criteria.andEqualTo("accountId",dto.getQueryDTO().getAccountId());
        }else{
            if (CsStringUtils.isNotBlank(dto.getQueryDTO().getDay())) {
                criteria.andEqualTo("day",dto.getQueryDTO().getDay());
            }
            if (CsStringUtils.isNotBlank(dto.getQueryDTO().getWaybillNum())) {
                criteria.andEqualTo("waybillNum",dto.getQueryDTO().getWaybillNum());
            }
            if (CsStringUtils.isNotBlank(dto.getQueryDTO().getWarehouseName())) {
                criteria.andLike("warehouseName","%"+dto.getQueryDTO().getWarehouseName()+"%");
            }
            if (CsStringUtils.isNotBlank(dto.getQueryDTO().getReceiveAddress())) {
                criteria.andEqualTo("receiveAddress","%"+dto.getQueryDTO().getReceiveAddress()+"%");
            }
            if (CsStringUtils.isNotBlank(dto.getQueryDTO().getRealName())) {
                criteria.andEqualTo("realName",dto.getQueryDTO().getRealName());
            }
        }
        if (CsStringUtils.isNotBlank(dto.getQueryDTO().getPayStatus())) {
            if(PaymentStatusEnum.PAY_SUCCESS.getCode().equals(dto.getQueryDTO().getPayStatus())){
                criteria.andEqualTo("payStatus",PaymentStatusEnum.PAY_SUCCESS.getCode());
            }else if(PaymentStatusEnum.NEW_PAY.getCode().equals(dto.getQueryDTO().getPayStatus())){
                criteria.andIn("payStatus",Lists.newArrayList(
                        PaymentStatusEnum.NEW_PAY.getCode(),
                        PaymentStatusEnum.PAY_ING.getCode(),
                        PaymentStatusEnum.PAY_FAIL.getCode()));
            } else{
                criteria.andNotEqualTo("payStatus",dto.getQueryDTO().getPayStatus());
            }
        }
        criteria.andEqualTo("delFlg",false);
        condition.orderBy("createTime").desc();

        PageInfo<DriverCarriageItem> pageInfo = PageMethod.startPage(dto.getPageNum(),dto.getPageSize(),true,false,false)
                .doSelectPageInfo(()->super.findByCondition(condition));
        PageInfo<WaybillLogisticsInfoDTO> result = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo,result,"list");
        result.setList(Lists.newArrayList());
        if(CollectionUtils.isNotEmpty(pageInfo.getList())){
            for (DriverCarriageItem item : pageInfo.getList()) {
                WaybillLogisticsInfoDTO itemDTO = new WaybillLogisticsInfoDTO();
                BeanUtils.copyProperties(item,itemDTO);
                itemDTO.setPayStatusName(PaymentStatusEnum.getMessageByCode(item.getPayStatus()));
                result.getList().add(itemDTO);
            }
        }
        return result;
    }

    @Override
    public boolean notifyLogistics(String day,String accountId) {
        try {
            List<String> list = driverCarriageItemMapper.findWaybillListByDay(day,accountId);
            if(CollectionUtils.isEmpty(list)){
                log.info("list is null,day:{}, accountId: {}",day,accountId);
                return false;
            }
            MQMessage<List<String>> mqMessage = new MQMessage<>();
            mqMessage.setKey(LogisticsMessageTypeEnum.CARRY_SUBMIT_DRIVER_SETTLEMENT_FINISH.getCode());
            mqMessage.setRetryTimes(5);
            String dataString = JSON.toJSONString(list);
            log.info("司机费用结算通知物流 --->>> {}", dataString);
            mqMessage.setData(list);
            producer.send(mqMessage);
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(),e);
        }
        return false;
    }
}
