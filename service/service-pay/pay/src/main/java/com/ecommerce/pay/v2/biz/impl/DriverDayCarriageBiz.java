package com.ecommerce.pay.v2.biz.impl;

import com.alibaba.fastjson.JSON;
import com.ecommerce.common.service.common.BaseBiz;
import com.github.pagehelper.page.PageMethod;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.pay.api.v2.dto.driver.WaybillDaySummerDTO;
import com.ecommerce.pay.api.v2.dto.driver.WithdrawLogQueryDTO;
import com.ecommerce.pay.api.v2.dto.electricInvoiceAccount.PageQuery;
import com.ecommerce.pay.api.v2.enums.PaymentStatusEnum;
import com.ecommerce.pay.v2.biz.IDriverDayCarriageBiz;
import com.ecommerce.pay.v2.dao.vo.DriverDayCarriage;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

@Slf4j
@Service
public class DriverDayCarriageBiz extends BaseBiz<DriverDayCarriage> implements IDriverDayCarriageBiz {

    @Override
    public PageInfo<WaybillDaySummerDTO> pageWaybillDaySummerInfo(PageQuery<WithdrawLogQueryDTO> dto) {
        log.info("pageWaybillDaySummerInfo: {}", JSON.toJSONString(dto));
        Condition condition = new Condition(DriverDayCarriage.class);
        condition.createCriteria().andEqualTo("accountId",dto.getQueryDTO().getAccountId()).andEqualTo("delFlg",false);
        condition.orderBy("day").desc();

        PageInfo<DriverDayCarriage> pageInfo = new PageInfo<>();
        pageInfo.setPageSize(dto.getPageSize() == null ? 10 : dto.getPageSize());
        pageInfo.setPageNum(dto.getPageNum() == null ? 1 : dto.getPageNum());
        PageInfo<DriverDayCarriage> driverDayCarriagePageInfo = PageMethod.startPage(pageInfo.getPageNum(),pageInfo.getPageSize(),true,false,false)
                .doSelectPageInfo(()->super.findByCondition(condition));

        PageInfo<WaybillDaySummerDTO> result = new PageInfo<>(Lists.newArrayList());
        BeanUtils.copyProperties(driverDayCarriagePageInfo,result,"list");
        result.setList(Lists.newArrayList());

        if(CollectionUtils.isNotEmpty(driverDayCarriagePageInfo.getList())){
            for (DriverDayCarriage item : driverDayCarriagePageInfo.getList()) {
                WaybillDaySummerDTO itemDTO = new WaybillDaySummerDTO();
                BeanUtils.copyProperties(item,itemDTO);
                itemDTO.setPayStatusName(PaymentStatusEnum.getMessageByCode(item.getPayStatus()));
                result.getList().add(itemDTO);
            }
        }
        return result;
    }

    @Override
    public List<DriverDayCarriage> findByDay(List<String> day) {
        return findByDayAndPayNumber(day,null);
    }

    @Override
    public List<DriverDayCarriage> findByDayAndPayNumber(List<String> day, String payNumber) {
        if(CollectionUtils.isEmpty(day)) {
            return Lists.newArrayList();
        }
        Condition condition = new Condition(DriverDayCarriage.class);
        Example.Criteria criteria = condition.createCriteria();

        if (CsStringUtils.isNotBlank(payNumber)) {
            criteria.andEqualTo("payNumber",payNumber);
        }
        if( day.size() ==1 ){
            criteria.andEqualTo("day",day.get(0));
        }else {
            criteria.andIn("day",day);
        }
        criteria.andEqualTo("delFlg",false);
        return findByCondition(condition);
    }

    @Override
    public DriverDayCarriage findByDay(String accountId,String day) {
        if (CsStringUtils.isBlank(day)) {
            return null;
        }
        Condition condition = new Condition(DriverDayCarriage.class);
        condition.createCriteria().andEqualTo("accountId",accountId).andEqualTo("day",day).andEqualTo("delFlg",false);
        List<DriverDayCarriage> list = findByCondition(condition);
        return list == null || list.isEmpty() ? null : list.get(0);
    }
}
