package com.ecommerce.pay.v2.biz.impl;

import com.alibaba.fastjson.JSON;
import com.ecommerce.common.enums.AppNames;
import com.ecommerce.common.service.common.BaseBiz;
import com.ecommerce.common.service.common.bsid.CommonBusinessIdGenerator;
import com.github.pagehelper.page.PageMethod;
import com.github.pagehelper.Page;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.common.utils.DateUtil;
import com.ecommerce.member.api.dto.member.enums.MemberPlatform;
import com.ecommerce.open.api.dto.gnete.ConsumeRefundResponseDTO;
import com.ecommerce.open.api.dto.gnete.WithdrawResponseDTO;
import com.ecommerce.pay.api.v2.dto.driver.DriverPayInfoDTO;
import com.ecommerce.pay.api.v2.dto.driver.DriverPayInfoQueryDTO;
import com.ecommerce.pay.api.v2.dto.driver.PrePayDepositDTO;
import com.ecommerce.pay.api.v2.dto.driver.WithdrawLogDTO;
import com.ecommerce.pay.api.v2.dto.driver.WithdrawLogQueryDTO;
import com.ecommerce.pay.api.v2.dto.electricInvoiceAccount.PageQuery;
import com.ecommerce.pay.api.v2.dto.gnete.WithdrawDTO;
import com.ecommerce.pay.api.v2.enums.PaymentStatusEnum;
import com.ecommerce.pay.api.v2.enums.PaymentTypeEnum;
import com.ecommerce.pay.enums.PayDetailTypeEnum;
import com.ecommerce.pay.v2.biz.IDriverPayInfoBiz;
import com.ecommerce.pay.v2.dao.vo.DriverPayInfo;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.Date;

@Slf4j
@Service
public class DriverPayInfoBiz extends BaseBiz<DriverPayInfo> implements IDriverPayInfoBiz {

    @Autowired
    private CommonBusinessIdGenerator commonBusinessIdGenerator;
    /**
     * 创建支付单
     */
    @Override
    public DriverPayInfo createDepositPayInfo(PrePayDepositDTO dto,String operator) {
        Date now = new Date();
        DriverPayInfo driverPayInfo = new DriverPayInfo();
        driverPayInfo.setDriverPayInfoId(getUuidGeneratorGain());
        driverPayInfo.setMemberId(dto.getMemberId());
        driverPayInfo.setAccountId(dto.getAccountId());
        driverPayInfo.setAccountCode(dto.getAccountCode());
        driverPayInfo.setAccountName(dto.getAccountName());
        driverPayInfo.setRealName(dto.getRealName());
        driverPayInfo.setMobile(dto.getMobile());
        driverPayInfo.setPayinfoType(PaymentTypeEnum.DEPOSIT_PAY.getCode());
        driverPayInfo.setSubject(PayDetailTypeEnum.DRIVER_DEPOSIT.getCode());
        driverPayInfo.setOperateTime(now);
        driverPayInfo.setDay(DateUtil.format(now,"yyyy-MM-dd"));
        //支付单10分钟有效
        driverPayInfo.setExpireTime(new Date(System.currentTimeMillis() + 600000L));
        driverPayInfo.setPayinfoCode(dto.getPayInfoCode());
        driverPayInfo.setPayinfoAmount(dto.getDepositAmount());
        driverPayInfo.setPayinfoStatus(PaymentStatusEnum.PAY_SUCCESS.getCode());
        driverPayInfo.setPayTime(now);
        driverPayInfo.setReceiptTime(now);
        driverPayInfo.setReceiptTime(driverPayInfo.getPayTime());
        driverPayInfo.setMctOrderNo(dto.getMctOrderNo());
        driverPayInfo.setTradeBillNo(dto.getTransOrderNo());
        driverPayInfo.setRefunded(false);
        driverPayInfo.setDelFlg(false);
        driverPayInfo.setCreateTime(now);
        driverPayInfo.setCreateUser(operator);
        dto.setDepositOrderId(driverPayInfo.getDriverPayInfoId());
        dto.setDepositOrderCode(driverPayInfo.getPayinfoCode());
        //删除之前的支付单
        closeDriverPayInfo(dto.getMemberId(),driverPayInfo.getPayinfoType(),dto.getAccountId());
        //新增支付单
        getMapper().insert(driverPayInfo);
        return driverPayInfo;
    }

    private void closeDriverPayInfo(String memberId,String payinfoType,String operator){
        //修改支付单状态为已关闭
        Condition condition = new Condition(DriverPayInfo.class);
        condition.createCriteria()
                .andEqualTo("memberId",memberId)
                .andEqualTo("payinfoType",payinfoType)
                .andIn("payinfoStatus",Lists.newArrayList(
                        PaymentStatusEnum.NEW_PAY.getCode(),
                        PaymentStatusEnum.PAY_FAIL.getCode(),
                        PaymentStatusEnum.PAY_TIMEOUT.getCode(),
                        PaymentStatusEnum.PAY_CANCEL.getCode()))
                .andEqualTo("delFlg",false);

        DriverPayInfo driverPayInfo = new DriverPayInfo();
        driverPayInfo.setPayinfoStatus(PaymentStatusEnum.PAY_CLOSE.getCode());
        driverPayInfo.setDelFlg(true);
        driverPayInfo.setUpdateTime(new Date());
        driverPayInfo.setUpdateUser(operator);
        getMapper().updateByConditionSelective(driverPayInfo,condition);
    }

    @Override
    public DriverPayInfo createPlatformPayInfo(BigDecimal payAmount,String regId, String operator) {
        DriverPayInfo driverPayInfo = new DriverPayInfo();
        driverPayInfo.setDriverPayInfoId(getUuidGeneratorGain());
        driverPayInfo.setMemberId(MemberPlatform.PLATFORM_MEMBERID.getId());
        driverPayInfo.setTradeBillNo(regId);

        driverPayInfo.setPayinfoType(PaymentTypeEnum.PAY.getCode());
        driverPayInfo.setSubject(PayDetailTypeEnum.DRIVER_LOGISTICS_COST.getCode());
        driverPayInfo.setOperateTime(new Date());
        driverPayInfo.setDay(DateUtil.format(new Date(),"yyyy-MM-dd"));
        //支付单10分钟有效
        driverPayInfo.setExpireTime(new Date(System.currentTimeMillis() + 600000L));
        driverPayInfo.setPayinfoCode(commonBusinessIdGenerator.incrementCode("DR"));
        driverPayInfo.setPayinfoAmount(payAmount);
        driverPayInfo.setPayinfoStatus(PaymentStatusEnum.PAY_ING.getCode());
        driverPayInfo.setRefunded(false);
        driverPayInfo.setDelFlg(false);
        driverPayInfo.setCreateTime(new Date());
        driverPayInfo.setCreateUser(operator);
        //新增支付单
        getMapper().insert(driverPayInfo);
        return driverPayInfo;
    }

    /**
     * 根据支付单，补一个支付成功的退款单
     */
    @Override
    public DriverPayInfo createDepositRefundPayInfo(DriverPayInfo driverPayInfo, ConsumeRefundResponseDTO res, String operator) {
        DriverPayInfo driverRefundPayInfo = new DriverPayInfo();
        BeanUtils.copyProperties(driverPayInfo,driverRefundPayInfo);
        driverRefundPayInfo.setDriverPayInfoId(getUuidGeneratorGain());
        driverRefundPayInfo.setPayinfoCode(commonBusinessIdGenerator.incrementCode("DR"));
        driverRefundPayInfo.setOriginDriverPayInfoId(driverPayInfo.getDriverPayInfoId());
        driverRefundPayInfo.setPayinfoType(PaymentTypeEnum.DEPOSIT_REFUND.getCode());
        driverRefundPayInfo.setSubject(PayDetailTypeEnum.DRIVER_DEPOSIT_REFUND.getCode());
        driverRefundPayInfo.setOperateTime(new Date());
        driverRefundPayInfo.setDay(DateUtil.format(new Date(),"yyyy-MM-dd"));
        driverRefundPayInfo.setExpireTime(new Date(System.currentTimeMillis() + 600000L));
        driverRefundPayInfo.setPayinfoStatus(PaymentStatusEnum.PAY_SUCCESS.getCode());
        driverRefundPayInfo.setTradeBillNo(res.getTransOrderNo());
        driverRefundPayInfo.setMctOrderNo(res.getMctOrderNo());
        driverRefundPayInfo.setRemark(res.getActRefundAmt() != null ? res.getActRefundAmt().toString() : null);
        driverRefundPayInfo.setRefunded(true);
        driverRefundPayInfo.setDelFlg(false);
        driverRefundPayInfo.setCreateTime(new Date());
        driverRefundPayInfo.setCreateUser(operator);
        driverRefundPayInfo.setUpdateTime(new Date());
        driverRefundPayInfo.setUpdateUser(operator);
        getMapper().insert(driverRefundPayInfo);
        return driverRefundPayInfo;
    }

    /**
     * 补一个提现支付单
     */
    @Override
    public DriverPayInfo createWithdrawPayInfo(WithdrawDTO dto, WithdrawResponseDTO res) {
        Date now = new Date();
        DriverPayInfo driverPayInfo = new DriverPayInfo();
        driverPayInfo.setDriverPayInfoId(getUuidGeneratorGain());
        driverPayInfo.setMemberId(dto.getMemberId());
        driverPayInfo.setAccountId(dto.getAccountId());
        driverPayInfo.setAccountCode(dto.getAccountCode());
        driverPayInfo.setAccountName(dto.getAccountName());
        driverPayInfo.setRealName(dto.getRealName());
        driverPayInfo.setMobile(dto.getMobile());
        driverPayInfo.setPayinfoType(PaymentTypeEnum.CASH_WITHDRAWAL.getCode());
        driverPayInfo.setSubject(PayDetailTypeEnum.DRIVER_LOGISTICS_COST.getCode());
        driverPayInfo.setOperateTime(new Date());
        driverPayInfo.setDay(DateUtil.format(new Date(),"yyyy-MM-dd"));
        //支付单10分钟有效
        driverPayInfo.setExpireTime(new Date(System.currentTimeMillis() + 600000L));
        driverPayInfo.setPayinfoCode(commonBusinessIdGenerator.incrementCode("DR"));
        driverPayInfo.setPayinfoAmount(dto.getWithdrawAmount());
        driverPayInfo.setPayinfoStatus(PaymentStatusEnum.PAY_SUCCESS.getCode());
        driverPayInfo.setTradeBillNo(res.getTransOrderNo());
        driverPayInfo.setPayTime(now);
        driverPayInfo.setRemark(dto.getBankCard());
        driverPayInfo.setRefunded(false);
        driverPayInfo.setDelFlg(false);
        driverPayInfo.setCreateTime(now);
        driverPayInfo.setCreateUser(dto.getAccountId());
        getMapper().insert(driverPayInfo);
        return driverPayInfo;
    }

    /**
     * 平台接口-个体司机保证金管理-根据条件翻页查询
     */
    @Override
    public PageInfo<DriverPayInfoDTO> pageDriverPayInfo(PageQuery<DriverPayInfoQueryDTO> query) {
        log.info("pageDriverDeposit: {}", JSON.toJSONString(query));
        Condition condition = new Condition(DriverPayInfo.class);
        Example.Criteria criteria = condition.createCriteria();
        if(AppNames.WEB_SERVICE_DRIVER.getPlatform().equals(query.getQueryDTO().getAppName()) || BooleanUtils.isTrue(query.getQueryDTO().getPlatformDepositQuery())){
            if (CsStringUtils.isNotBlank(query.getQueryDTO().getPayType())) {
                criteria.andEqualTo("payinfoType",query.getQueryDTO().getPayType());
            }else {
                criteria.andIn("payinfoType", Lists.newArrayList(PaymentTypeEnum.DEPOSIT_PAY.getCode(), PaymentTypeEnum.DEPOSIT_REFUND.getCode()));
            }
        } else if (CsStringUtils.isNotBlank(query.getQueryDTO().getPayType())) {
            criteria.andEqualTo("payinfoType",query.getQueryDTO().getPayType());
        }
        if (CsStringUtils.isNotBlank(query.getQueryDTO().getDaysBegin())) {
            criteria.andGreaterThanOrEqualTo("day",query.getQueryDTO().getDaysBegin());
        }
        if (CsStringUtils.isNotBlank(query.getQueryDTO().getDaysEnd())) {
            criteria.andLessThanOrEqualTo("day",query.getQueryDTO().getDaysEnd());
        }
        if (CsStringUtils.isNotBlank(query.getQueryDTO().getAccountCode())) {
            criteria.andEqualTo("accountCode",query.getQueryDTO().getAccountCode());
        }
        if (CsStringUtils.isNotBlank(query.getQueryDTO().getMobile())) {
            criteria.andEqualTo("mobile",query.getQueryDTO().getMobile());
        }
        if (CsStringUtils.isNotBlank(query.getQueryDTO().getRealName())) {
            criteria.andEqualTo("realName",query.getQueryDTO().getRealName());
        }
        criteria.andEqualTo("delFlg",false);
        condition.orderBy("day").desc();

        PageInfo<DriverPayInfo> pageinfo = new PageInfo<DriverPayInfo>();
        pageinfo.setPageSize(query.getPageSize());
        pageinfo.setPageNum(query.getPageNum());

        Page<DriverPayInfo> page = super.page(condition, pageinfo);

        PageInfo<DriverPayInfoDTO> result = new PageInfo<DriverPayInfoDTO>();
        BeanUtils.copyProperties(page, result);

        result.setList(Lists.newArrayList());
        if (CollectionUtils.isNotEmpty(page.getResult())) {
            for (DriverPayInfo item : page.getResult()) {
                DriverPayInfoDTO itemDTO = new DriverPayInfoDTO();
                BeanUtils.copyProperties(item,itemDTO);
                itemDTO.setPayType(item.getPayinfoType());
                PaymentTypeEnum paymentTypeEnum = PaymentTypeEnum.getByCode(item.getPayinfoType());
                itemDTO.setPayTypeName(paymentTypeEnum.getMessage());
                itemDTO.setOperateTime(item.getCreateTime());
                itemDTO.setReceiptTime(item.getPayTime());
                itemDTO.setDepositNumber(item.getPayinfoCode());
                itemDTO.setPayNumber(item.getPayinfoCode());
                itemDTO.setDepositAmount(item.getPayinfoAmount());
                result.getList().add(itemDTO);
            }
        }
        return result;
    }

    /**
     * 司机app接口-提现流水查询
     */
    @Override
    public PageInfo<WithdrawLogDTO> pageWithdrawLog(PageQuery<WithdrawLogQueryDTO> dto) {
        log.info("pageWithdrawLog: {}", JSON.toJSONString(dto));
        Condition condition = new Condition(DriverPayInfo.class);
        condition.createCriteria()
                .andEqualTo("accountId",dto.getQueryDTO().getAccountId())
                .andEqualTo("payinfoType",PaymentTypeEnum.CASH_WITHDRAWAL.getCode())
                .andEqualTo("delFlg",false);
        condition.orderBy("day").desc();

        PageInfo<DriverPayInfo> pageinfo = new PageInfo<DriverPayInfo>();
        pageinfo.setPageSize(dto.getPageSize() == null ? 10 : dto.getPageSize());
        pageinfo.setPageNum(dto.getPageNum() == null ? 1 : dto.getPageNum());

        PageInfo<DriverPayInfo> queryResult = PageMethod.startPage(pageinfo.getPageNum(),pageinfo.getPageSize(),true,false,false)
                .doSelectPageInfo(()->super.findByCondition(condition));
        PageInfo<WithdrawLogDTO> result = new PageInfo<WithdrawLogDTO>();

        BeanUtils.copyProperties(queryResult,result,"list");

        result.setList(Lists.newArrayList());
        if(CollectionUtils.isNotEmpty(queryResult.getList())){
            for (DriverPayInfo item : queryResult.getList()) {
                WithdrawLogDTO itemDTO = new WithdrawLogDTO();
                itemDTO.setPayFlowNumber(item.getPayinfoCode());
                itemDTO.setWithdrawTime(item.getCreateTime());
                itemDTO.setWithdrawAmount(item.getPayinfoAmount());
                itemDTO.setPayTime(item.getPayTime());
                itemDTO.setBankCard(item.getRemark());//参见方法：createWithdrawPayInfo
                result.getList().add(itemDTO);
            }
        }
        return result;
    }

    @Override
    public boolean updatePayInfo(String driverPayInfoId, String payNumber, String operator) {
        DriverPayInfo driverDeposit = new DriverPayInfo();
        driverDeposit.setDriverPayInfoId(driverPayInfoId);
        driverDeposit.setPayinfoStatus(PaymentStatusEnum.PAY_SUCCESS.getCode());
        driverDeposit.setPayTime(new Date());
        driverDeposit.setUpdateTime(new Date());
        driverDeposit.setUpdateUser(operator);
        return getMapper().updateByPrimaryKeySelective(driverDeposit) > 0 ;
    }
}
