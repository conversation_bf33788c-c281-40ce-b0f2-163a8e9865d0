package com.ecommerce.pay.v2.biz.impl;

import com.alibaba.fastjson.JSON;
import com.ecommerce.common.service.common.BaseBiz;
import com.ecommerce.common.utils.ArithUtils;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.pay.api.v2.dto.driver.DriverSummaryInfoQueryDTO;
import com.ecommerce.pay.api.v2.dto.electricInvoiceAccount.PageQuery;
import com.ecommerce.pay.api.v2.dto.gnete.OpenWalletDTO;
import com.ecommerce.pay.v2.biz.IDriverSummaryInfoBiz;
import com.ecommerce.pay.v2.dao.mapper.DriverSummaryInfoMapper;
import com.ecommerce.pay.v2.dao.vo.DriverSummaryInfo;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DriverSummaryInfoBiz extends BaseBiz<DriverSummaryInfo> implements IDriverSummaryInfoBiz {

    @Autowired
    private DriverSummaryInfoMapper driverSummaryInfoMapper;
    @Value("${spring.cloud.config.profile:dev}")
    private String profile;

    @Override
    public List<String> findDepositPayedDriver(List<String> memberIds) {
        log.info("findDepositPayedDriver: {}", JSON.toJSONString(memberIds));
        if(CollectionUtils.isEmpty(memberIds)){
            return Lists.newArrayList();
        }
        //测试环境使用 此功能还没上生产
        if( !"prod".equals(profile)) {
            return driverSummaryInfoMapper.findDepositPayedDriver(memberIds.stream().map(item->"'"+item+"'").collect(Collectors.joining(",")));
        }
        //测试需要，原样返回，保证金缴纳未实现
        return memberIds;
    }

    @Override
    public PageInfo<DriverSummaryInfo> pageDriverSummaryInfo(PageQuery<DriverSummaryInfoQueryDTO> dto) {
        log.info("pageDriverSummaryInfo: {}", JSON.toJSONString(dto));
        Condition condition = new Condition(DriverSummaryInfo.class);
        Example.Criteria criteria = condition.createCriteria();
        if (CsStringUtils.isNotBlank(dto.getQueryDTO().getAccountCode())) {
            criteria.andLike("accountCode", "%"+dto.getQueryDTO().getAccountCode()+"%");
        }
        if (CsStringUtils.isNotBlank(dto.getQueryDTO().getRealName())) {
            criteria.andLike("realName", "%"+dto.getQueryDTO().getRealName()+"%");
        }
        if (CsStringUtils.isNotBlank(dto.getQueryDTO().getMobile())) {
            criteria.andLike("mobile", "%"+dto.getQueryDTO().getMobile()+"%");
        }
        if( dto.getQueryDTO().getOpenFlg() != null ) {
            criteria.andEqualTo("walletOpenFlg",dto.getQueryDTO().getOpenFlg());
        }
        if( dto.getQueryDTO().getDepositPayStatus() != null ) {
            if(BooleanUtils.isTrue(dto.getQueryDTO().getDepositPayStatus())) {
                //已支付 未退款
                criteria.andEqualTo("depositPayStatus", true)
                .andEqualTo("refunded",false);
            }else{
                //未支付
                criteria.andEqualTo("depositPayStatus", false);
            }
        }
        criteria.andEqualTo("delFlg",false);
        PageInfo<DriverSummaryInfo> pageInfo = new PageInfo<>();
        pageInfo.setPageSize(dto.getPageSize());
        pageInfo.setPageNum(dto.getPageNum());
        return pageInfo(condition,pageInfo);
    }

    @Override
    public DriverSummaryInfo findByMemberId(String memberId) {
        Condition condition = new Condition(DriverSummaryInfo.class);
        condition.createCriteria().andEqualTo("memberId",memberId).andEqualTo("delFlg",false);
        List<DriverSummaryInfo> list = findByCondition(condition);
        return list == null || list.isEmpty() ? null : list.get(0);
    }

    @Override
    public DriverSummaryInfo findByAccountId(String accountId) {
        Condition condition = new Condition(DriverSummaryInfo.class);
        condition.createCriteria().andEqualTo("accountId",accountId).andEqualTo("delFlg",false);
        List<DriverSummaryInfo> list = findByCondition(condition);
        return list == null || list.isEmpty() ? null : list.get(0);
    }

    @Override
    public Boolean openWallet(OpenWalletDTO dto, String walletId) {
        DriverSummaryInfo driverSummaryInfo = findByAccountId(dto.getAccountId());
        if( driverSummaryInfo != null ){
            DriverSummaryInfo up = new DriverSummaryInfo();
            up.setDriverSummaryInfoId(driverSummaryInfo.getDriverSummaryInfoId());
            up.setUpdateTime(new Date());
            up.setUpdateUser(dto.getAccountId());
            up.setWalletId(walletId);
            up.setWalletOpenFlg(true);
            driverSummaryInfoMapper.updateByPrimaryKeySelective(up);
            return true;
        }
        driverSummaryInfo = new DriverSummaryInfo();
        driverSummaryInfo.setDriverSummaryInfoId(getUuidGeneratorGain());
        driverSummaryInfo.setMemberId(dto.getMemberId());
        driverSummaryInfo.setAccountCode(dto.getAccountCode());
        driverSummaryInfo.setAccountId(dto.getAccountId());
        driverSummaryInfo.setRealName(dto.getRealName());
        driverSummaryInfo.setMobile(dto.getMobile());
        driverSummaryInfo.setWalletOpenFlg(true);
        driverSummaryInfo.setWalletId(walletId);
        driverSummaryInfo.setDepositPayStatus(false);
        driverSummaryInfo.setRefunded(false);
        driverSummaryInfo.setWaybillCount(0);
        driverSummaryInfo.setDelFlg(false);
        driverSummaryInfo.setCreateTime(new Date());
        driverSummaryInfo.setCreateUser(dto.getAccountId());
        driverSummaryInfo.setUpdateUser(dto.getAccountId());
        driverSummaryInfo.setUpdateTime(driverSummaryInfo.getCreateTime());
        return driverSummaryInfoMapper.insert(driverSummaryInfo) > 0;
    }

    @Override
    public boolean updateBankCard(String accountId, String bankCard) {
        Condition condition = new Condition(DriverSummaryInfo.class);
        condition.createCriteria().andEqualTo("accountId",accountId).andEqualTo("delFlg",false);

        DriverSummaryInfo up = new DriverSummaryInfo();
        up.setBankCard(bankCard);
        up.setUpdateTime(new Date());
        up.setUpdateUser(accountId);
        return driverSummaryInfoMapper.updateByConditionSelective(up,condition) > 0;
    }

    @Override
    public boolean updateDepositPayStatus(String driverSummaryInfoId, BigDecimal depositAmount,String payInfoId,String operator) {
        DriverSummaryInfo up = new DriverSummaryInfo();
        up.setDriverSummaryInfoId(driverSummaryInfoId);
        up.setDepositPayStatus(true);
        up.setRefunded(false);
        up.setDepositAmount(depositAmount);
        up.setDepositPayInfoId(payInfoId);
        up.setUpdateTime(new Date());
        up.setUpdateUser(operator);
        return driverSummaryInfoMapper.updateByPrimaryKeySelective(up) > 0;
    }

    @Override
    public boolean updateDepositRefunded(String accountId) {
        Condition condition = new Condition(DriverSummaryInfo.class);
        condition.createCriteria().andEqualTo("accountId",accountId).andEqualTo("delFlg",false);

        DriverSummaryInfo up = new DriverSummaryInfo();
        up.setDepositPayStatus(true);
        up.setDepositPayStatus(false);
        up.setRefunded(true);
        up.setUpdateTime(new Date());
        up.setUpdateUser(accountId);

        return driverSummaryInfoMapper.updateByConditionSelective(up,condition) > 0;
    }

    @Override
    public boolean updateWithdraw(DriverSummaryInfo driverSummaryInfo, BigDecimal withdrawAmount, String operator) {
        DriverSummaryInfo up = new DriverSummaryInfo();
        up.setDriverSummaryInfoId(driverSummaryInfo.getDriverSummaryInfoId());
        up.setUpdateTime(new Date());
        up.setUpdateUser(operator);
        up.setWithdraw(ArithUtils.add(driverSummaryInfo.getWithdraw(),withdrawAmount));

        return driverSummaryInfoMapper.updateByPrimaryKeySelective(up) > 0;
    }
}
