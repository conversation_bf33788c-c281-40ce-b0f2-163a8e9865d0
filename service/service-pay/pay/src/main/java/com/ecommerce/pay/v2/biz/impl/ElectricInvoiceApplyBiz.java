package com.ecommerce.pay.v2.biz.impl;

import com.alibaba.fastjson.JSON;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.service.common.bsid.CommonBusinessIdGenerator;
import com.ecommerce.common.service.common.uuid.UUIDGenerator;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.open.api.dto.invoice.InvoiceContentDTO;
import com.ecommerce.open.api.dto.invoice.InvoiceItemDTO;
import com.ecommerce.open.api.dto.invoice.InvoiceRequestDTO;
import com.ecommerce.open.api.dto.invoice.InvoiceRevokeDTO;
import com.ecommerce.open.api.dto.invoice.SendInvoiceDTO;
import com.ecommerce.open.api.service.IElectricInvoiceService;
import com.ecommerce.pay.api.v2.dto.invoice.AuditInvoiceApplyDTO;
import com.ecommerce.pay.api.v2.dto.invoice.InvoiceApplyAddDTO;
import com.ecommerce.pay.api.v2.dto.invoice.InvoiceApplyAddItem;
import com.ecommerce.pay.api.v2.dto.invoice.RevokeInvoiceApplyDTO;
import com.ecommerce.pay.api.v2.enums.invoice.InvoiceApplyStatusEnum;
import com.ecommerce.pay.api.v2.enums.invoice.InvoiceBizTypeEnum;
import com.ecommerce.pay.api.v2.enums.invoice.InvoiceProcessModeEnum;
import com.ecommerce.pay.v2.biz.IElectricInvoiceApplyBiz;
import com.ecommerce.pay.v2.biz.IElectricInvoiceNotifyBiz;
import com.ecommerce.pay.v2.dao.bean.InvoiceItemBean;
import com.ecommerce.pay.v2.dao.bean.SendInvoiceBean;
import com.ecommerce.pay.v2.dao.mapper.InvoiceApplyItemMapper;
import com.ecommerce.pay.v2.dao.mapper.InvoiceApplyMapper;
import com.ecommerce.pay.v2.dao.mapper.InvoiceInfoItemMapper;
import com.ecommerce.pay.v2.dao.mapper.InvoiceInfoMapper;
import com.ecommerce.pay.v2.dao.vo.InvoiceAccount;
import com.ecommerce.pay.v2.dao.vo.InvoiceApply;
import com.ecommerce.pay.v2.dao.vo.InvoiceApplyItem;
import com.ecommerce.pay.v2.dao.vo.InvoiceInfo;
import com.ecommerce.pay.v2.dao.vo.InvoiceInfoItem;
import com.ecommerce.pay.v2.dao.vo.InvoiceTaxMap;
import com.ecommerce.pay.v2.biz.IInvoiceAccountBiz;
import com.ecommerce.pay.v2.biz.IInvoiceTaxMapBiz;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 *  ElectricInvoiceApplyBiz
 *
 * <AUTHOR>
 */
@Slf4j
@Service("electricInvoiceApplyBiz")
public class ElectricInvoiceApplyBiz implements IElectricInvoiceApplyBiz {

    @Autowired
    private UUIDGenerator uuidGenerator;

    @Autowired
    private CommonBusinessIdGenerator payIncrementIdGenerator;

    @Autowired(required = false)
    private InvoiceApplyMapper invoiceApplyMapper;

    @Autowired(required = false)
    private InvoiceApplyItemMapper invoiceApplyItemMapper;

    @Autowired(required = false)
    private InvoiceInfoMapper invoiceInfoMapper;

    @Autowired(required = false)
    private InvoiceInfoItemMapper invoiceInfoItemMapper;

    @Autowired
    private IInvoiceAccountBiz invoiceAccountBiz;

    @Autowired
    private IInvoiceTaxMapBiz invoiceTaxMapBiz;

    @Autowired
    private IElectricInvoiceService electricInvoiceService;

    @Autowired
    private IElectricInvoiceNotifyBiz electricInvoiceNotifyBiz;

    private static final String APPLY_ID = "applyId";
    private static final String DEL_FLG = "delFlg";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchApplyInvoice(List<InvoiceApplyAddDTO> invoiceApplyAddList) {
        if (CollectionUtils.isEmpty(invoiceApplyAddList)) {
            throw new BizException(BasicCode.INVALID_PARAM, "申请列表为空");
        }
        for (InvoiceApplyAddDTO invoiceApplyAddDTO : invoiceApplyAddList) {
            singleApplyInvoice(invoiceApplyAddDTO);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void singleApplyInvoice(InvoiceApplyAddDTO invoiceApplyAddDTO) {
        //获取开票账户信息
        InvoiceAccount invoiceAccount = invoiceAccountBiz.queryInvoiceAccountDetail(invoiceApplyAddDTO.getSellerId(),false);
        if (invoiceAccount == null) {
            throw new BizException(BasicCode.PARAM_NULL, "开票账户,sellerId=" + invoiceApplyAddDTO.getSellerId());
        }
        LocalDate now = LocalDate.now();
        String applyId = uuidGenerator.gain();
        String infoId = uuidGenerator.gain();
        BigDecimal totalAmount ;
        BigDecimal totalTax = BigDecimal.ZERO;
        BigDecimal totalProductAmount = BigDecimal.ZERO;
        BigDecimal totalLogisticsAmount = BigDecimal.ZERO;
        //保存 invoiceApplyItem
        Set<String> applyItemBizTypeSet = Sets.newHashSet();
        List<InvoiceApplyAddItem> invoiceApplyAddItemList = invoiceApplyAddDTO.getInvoiceApplyAddItemList();
        for (InvoiceApplyAddItem applyAddItem : invoiceApplyAddItemList) {
            //校验单一申请项的金额是否与类型相符
            validateBizType(applyAddItem);
            applyItemBizTypeSet.add(applyAddItem.getBizType());
            InvoiceApplyItem dbApplyItem = new InvoiceApplyItem();
            totalProductAmount = add(totalProductAmount, applyAddItem.getAmount());
            totalLogisticsAmount = add(totalLogisticsAmount, applyAddItem.getLogisticsAmount());
            BeanUtils.copyProperties(applyAddItem, dbApplyItem);
            dbApplyItem.setApplyId(applyId);
            dbApplyItem.setApplyItemId(uuidGenerator.gain());
            if (applyAddItem.getInvoiceOrderExtra() != null) {
                dbApplyItem.setExtraContent(JSON.toJSONString(applyAddItem.getInvoiceOrderExtra()));
            }
            invoiceApplyItemMapper.insertSelective(dbApplyItem);
        }
        totalAmount = add(totalProductAmount, totalLogisticsAmount);
        //保存 invoiceApply
        InvoiceApply apply = new InvoiceApply();
        BeanUtils.copyProperties(invoiceApplyAddDTO, apply);
        apply.setApplyMonth(now.format(DateTimeFormatter.ofPattern("MM")));
        apply.setApplyYear(now.format(DateTimeFormatter.ofPattern("yyyy")));
        apply.setApplyNum(payIncrementIdGenerator.incrementCode("INV"));
        apply.setApplyId(applyId);
        apply.setTotalAmount(totalAmount);
        apply.setProductAmount(totalProductAmount);
        apply.setLogisticsAmount(totalLogisticsAmount);
        //自动审核用户的普票
        if (apply.getIsSpecial() == 0 && CsStringUtils.equals(InvoiceProcessModeEnum.AUTO_AUDIT.code(), invoiceAccount.getProcessMode())) {
            apply.setStatus(InvoiceApplyStatusEnum.AUDIT_PASS.code());
            apply.setAuditTime(new Date());
        } else {
            apply.setStatus(InvoiceApplyStatusEnum.APPLIED.code());
        }

        //校验申请项组合是否与类型组合相符,并设置bizType
        if (applyItemBizTypeSet.size() == 1) {
            apply.setBizType(applyItemBizTypeSet.iterator().next());
        } else if (applyItemBizTypeSet.size() == 2 && !applyItemBizTypeSet.contains(InvoiceBizTypeEnum.TRADE_WITH_LOGISTICS.code())) {
            apply.setBizType(InvoiceBizTypeEnum.MIX_INVOICE.code());
        } else {
            throw new BizException(BasicCode.INVALID_PARAM, "申请业务类型组合不合法");
        }
        invoiceApplyMapper.insertSelective(apply);
        //合并 invoiceApplyItem 为 invoiceInfoItem,并保存
        Map<String, List<InvoiceApplyAddItem>> itemGroupMaps = invoiceApplyAddItemList
                .stream()
                .collect(
                        Collectors.groupingBy(item -> item.getItemCode() + "#" + item.getItemCategory() + "#" + item.getBizType())
                );
        for (Map.Entry<String, List<InvoiceApplyAddItem>> entry : itemGroupMaps.entrySet()) {
            //key是商品编码和业务类型
            String key = entry.getKey();
            String[] keyArr = key.split("#");
            String itemCategory = keyArr[1];
            //根据商品分类查询税率mapVo
            InvoiceInfoItem mergeAppItem = mergeAppItem(itemCategory, infoId, entry.getValue());
            totalTax = add(totalTax, mergeAppItem.getTax());
            invoiceInfoItemMapper.insertSelective(mergeAppItem);
        }
        //汇总 invoiceInfo,并保存
        InvoiceInfo invoiceInfo = new InvoiceInfo();
        BeanUtils.copyProperties(invoiceApplyAddDTO, invoiceInfo);
        invoiceInfo.setApplyId(applyId);
        invoiceInfo.setInfoId(infoId);
        invoiceInfo.setDrawer(invoiceAccount.getDrawer());
        invoiceInfo.setReviewer(invoiceAccount.getReviewer());
        invoiceInfo.setPayee(invoiceAccount.getPayee());
        if (CsStringUtils.isNotBlank(invoiceAccount.getBankAccountName())) {
            invoiceInfo.setSellerBankAccount(invoiceAccount.getBankAccountName() + invoiceAccount.getBankAccountNo());
        }
        invoiceInfo.setTotalAmount(totalAmount);
        invoiceInfo.setTotalTaxAmount(totalTax);
        invoiceInfoMapper.insertSelective(invoiceInfo);
        //需要异步处理(mq/定时任务), 调用open的某个接口(未被提供), 开票的结果需要通知订单(接口未被提供)
    }

    @Override
    public void batchInvoice() {
        List<SendInvoiceBean> sendInvoiceBeans = invoiceInfoMapper.queryAuditList();
        if (CollectionUtils.isEmpty(sendInvoiceBeans)) {
            log.info("没有需要发起开票的申请!");
            return;
        }

        for (SendInvoiceBean bean : sendInvoiceBeans) {
            try {
                singleInvoice(bean);
            } catch (Exception e) {
                log.error("发起开票发生异常:{}", bean, e);
            }
        }
    }

    @Override
    public void singleInvoice(SendInvoiceBean bean) {
        log.info("发起开票:{}", bean);
        String applyId = bean.getApplyId();
        String sellerId = bean.getSellerId();
        String infoId = bean.getInfoId();
        int updateCont = invoiceApplyMapper.updateStatusById(applyId, InvoiceApplyStatusEnum.INVOICE_DOING.code(), bean.getStatus());
        if (updateCont == 0) {
            log.info("开票以被处理,无法锁定:{}", bean);
            return;
        }
        boolean isSuccess = false;
        try {
            InvoiceRequestDTO<SendInvoiceDTO> requestDTO = new InvoiceRequestDTO<>();
            SendInvoiceDTO param = new SendInvoiceDTO();
            BeanUtils.copyProperties(bean, param, "invoiceItemList");
            param.setSellerBankAcc(bean.getSellerBankAccount());
            param.setTotalAmountTax(decimalToString(bean.getTotalAmount()));
            param.setTotalTax(decimalToString(bean.getTotalTaxAmount()));
            param.setBuyerName(bean.getInvoiceTitle());
            List<InvoiceItemBean> itemBeanList = bean.getInvoiceItemList();
            if (CollectionUtils.isNotEmpty(itemBeanList)) {
                List<InvoiceItemDTO> itemDTOList = Lists.newArrayList();
                for (InvoiceItemBean itemBean : itemBeanList) {
                    InvoiceItemDTO itemDTO = new InvoiceItemDTO();
                    BeanUtils.copyProperties(itemBean, itemDTO, "amount", "itemNum", "tax", "taxRate");
                    itemDTO.setSpecMode(itemBean.getItemMode());
                    itemDTO.setAmount(decimalToString(itemBean.getAmount()));
                    itemDTO.setItemNum(decimalToString(itemBean.getItemNum()));
                    itemDTO.setTax(decimalToString(itemBean.getTax()));
                    itemDTO.setTaxRate(decimalToString(itemBean.getTaxRate()));
                    itemDTOList.add(itemDTO);
                }
                param.setInvoiceItemList(itemDTOList);
            }
            requestDTO.setParam(param);

            InvoiceAccount invoiceAccount = invoiceAccountBiz.queryInvoiceAccountDetail(sellerId,true);
            if (invoiceAccount == null) {
                throw new BizException(BasicCode.DATA_NOT_EXIST, "卖家开票信息账户:" + bean.getSellerId());
            }
            requestDTO.setAccessToken(invoiceAccount.getAccessToken());
            requestDTO.setTokenType(invoiceAccount.getTokenType());
            requestDTO.setTaxNo(invoiceAccount.getTaxNo());

            ItemResult<InvoiceContentDTO> itemResult = electricInvoiceService.directSendInvoice(requestDTO);
            log.info("开票发起:{}/{}===>结果:{}", bean, requestDTO, JSON.toJSONString(itemResult));
            isSuccess = itemResult.isSuccess();
            if (isSuccess) {
                //开票成功的处理
                //更新请求
                InvoiceApply successApply = new InvoiceApply();
                successApply.setApplyId(applyId);
                successApply.setStatus(InvoiceApplyStatusEnum.INVOICE_SUCCESS.code());
                LocalDate now = LocalDate.now();
                successApply.setFinishYear(now.format(DateTimeFormatter.ofPattern("yyyy")));
                successApply.setFinishMonth(now.format(DateTimeFormatter.ofPattern("MM")));
                invoiceApplyMapper.updateByPrimaryKeySelective(successApply);
                //更新请求项
                Example applyItemExa = new Example(InvoiceApplyItem.class);
                applyItemExa.createCriteria().andEqualTo(APPLY_ID, applyId).andEqualTo(DEL_FLG, 0);
                InvoiceApplyItem applyItem = new InvoiceApplyItem();
                applyItem.setOpenFlag((byte) 1);
                invoiceApplyItemMapper.updateByExampleSelective(applyItem, applyItemExa);
                //更新发票
                InvoiceContentDTO contentDTO = itemResult.getData();
                InvoiceInfo invoiceInfo = new InvoiceInfo();
                invoiceInfo.setInfoId(infoId);
                invoiceInfo.setTaxOrderNo(contentDTO.getOrderNo());
                invoiceInfo.setSerialNo(contentDTO.getSerialNo());
                invoiceInfo.setInvoiceUrl(contentDTO.getPdfUrl());
                invoiceInfo.setInvoiceCode(contentDTO.getInvoiceCode());
                invoiceInfo.setInvoiceNum(contentDTO.getInvoiceNum());
                invoiceInfo.setInvoiceDate(new Date(contentDTO.getInvoiceDate()));
                invoiceInfoMapper.updateByPrimaryKeySelective(invoiceInfo);
            } else {
                log.error("开票失败:" + itemResult.getDescription());
                InvoiceApply errorApply = new InvoiceApply();
                errorApply.setApplyId(applyId);
                errorApply.setStatus(InvoiceApplyStatusEnum.INVOICE_FAIL.code());
                errorApply.setFailReason("开票失败：" + itemResult.getDescription());
                invoiceApplyMapper.updateByPrimaryKeySelective(errorApply);
            }
        } catch (BizException e) {
            log.error("开票发生业务异常:{}", bean, e);
            InvoiceApply errorApply = new InvoiceApply();
            errorApply.setApplyId(applyId);
            errorApply.setStatus(InvoiceApplyStatusEnum.INVOICE_FAIL.code());
            errorApply.setFailReason(e.getMessage());
            invoiceApplyMapper.updateByPrimaryKeySelective(errorApply);
        } catch (Exception e) {
            log.error("开票发生未知异常:{}", bean, e);
            InvoiceApply errorApply = new InvoiceApply();
            errorApply.setApplyId(applyId);
            errorApply.setStatus(InvoiceApplyStatusEnum.INVOICE_FAIL.code());
            errorApply.setFailReason("未知异常");
            invoiceApplyMapper.updateByPrimaryKeySelective(errorApply);
        } finally {
            if (isSuccess) {
                electricInvoiceNotifyBiz.synNotifyByApplyId(applyId, InvoiceApplyStatusEnum.INVOICE_SUCCESS.code());
            }
            log.info("调用交易系统通知开票情况");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void auditInvoiceApply(AuditInvoiceApplyDTO auditInvoiceApplyDTO) {
        List<String> applyIdList = auditInvoiceApplyDTO.getApplyIdList();
        boolean isReject = false;
        if (CollectionUtils.isEmpty(applyIdList)) {
            throw new BizException(BasicCode.PARAM_NULL, "待审核申请ID");
        }
        Example example = new Example(InvoiceApply.class);
        example.createCriteria().andIn(APPLY_ID, applyIdList).andEqualTo("status", InvoiceApplyStatusEnum.APPLIED.code()).andEqualTo(DEL_FLG, "0");
        List<InvoiceApply> invoiceApplies = invoiceApplyMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(invoiceApplies) || invoiceApplies.size() != applyIdList.size()) {
            //查询结果不一致
            throw new BizException(BasicCode.INVALID_PARAM, "审批操作的记录状态必须为已申请");
        }
        String status = auditInvoiceApplyDTO.getStatus();
        InvoiceApply dbApply = new InvoiceApply();
        dbApply.setUpdateUser(auditInvoiceApplyDTO.getUpdateUser());

        if (CsStringUtils.equals(status, InvoiceApplyStatusEnum.AUDIT_PASS.code())) {
            // 通过
            dbApply.setAuditTime(new Date());
            //如果是专票,直接修改为开票完成,否则修改为通过
            List<String> specialIdList = invoiceApplies.stream().filter(item -> item.getIsSpecial() == 1).map(InvoiceApply::getApplyId).toList();

            if (!CollectionUtils.isEmpty(specialIdList)) {
                applyIdList.removeAll(specialIdList);
                dbApply.setStatus(InvoiceApplyStatusEnum.INVOICE_SUCCESS.code());
                Example successExa = new Example(InvoiceApply.class);
                successExa.createCriteria().andIn(APPLY_ID, specialIdList).andEqualTo(InvoiceApplyStatusEnum.APPLIED.code()).andEqualTo(DEL_FLG, "0");
                invoiceApplyMapper.updateByExampleSelective(dbApply, successExa);
                for (String specialId : specialIdList) {
                    electricInvoiceNotifyBiz.synNotifyByApplyId(specialId, InvoiceApplyStatusEnum.INVOICE_SUCCESS.code());
                }
            }
            if (!CollectionUtils.isEmpty(applyIdList)) {
                dbApply.setStatus(status);
                Example passExa = new Example(InvoiceApply.class);
                passExa.createCriteria().andIn(APPLY_ID, applyIdList).andEqualTo(InvoiceApplyStatusEnum.APPLIED.code()).andEqualTo(DEL_FLG, "0");
                invoiceApplyMapper.updateByExampleSelective(dbApply, passExa);
            }

        } else if (CsStringUtils.equals(status, InvoiceApplyStatusEnum.AUDIT_REJECT.code())) {
            // 驳回
            dbApply.setStatus(status);
            dbApply.setFailReason(auditInvoiceApplyDTO.getFailReason());
            isReject = true;
            Example upExa = new Example(InvoiceApply.class);
            upExa.createCriteria().andIn(APPLY_ID, applyIdList).andEqualTo(InvoiceApplyStatusEnum.APPLIED.code()).andEqualTo(DEL_FLG, "0");
            invoiceApplyMapper.updateByExampleSelective(dbApply, upExa);
        } else {
            // 状态异常
            throw new BizException(BasicCode.INVALID_PARAM, "状态只能为审核或者驳回");
        }

        if (isReject) {
            for (String applyId : applyIdList) {
                electricInvoiceNotifyBiz.synNotifyByApplyId(applyId, InvoiceApplyStatusEnum.AUDIT_REJECT.code());
            }
        }
    }

    @Override
    public void revokeInvoiceApply(RevokeInvoiceApplyDTO revokeInvoiceApplyDTO) {
        String applyId = revokeInvoiceApplyDTO.getApplyId();
        InvoiceApply apply = invoiceApplyMapper.selectByPrimaryKey(applyId);
        if (apply == null || !CsStringUtils.equals(apply.getStatus(), InvoiceApplyStatusEnum.INVOICE_SUCCESS.code()) || apply.getIsSpecial() == 1) {
            throw new BizException(BasicCode.DATA_NOT_EXIST, "可红冲的申请:" + applyId);
        }
        Example infoExa = new Example(InvoiceInfo.class);
        infoExa.createCriteria().andEqualTo(APPLY_ID, applyId).andEqualTo(DEL_FLG, "0");
        List<InvoiceInfo> invoiceInfos = invoiceInfoMapper.selectByExample(infoExa);
        if (CollectionUtils.isEmpty(invoiceInfos)) {
            throw new BizException(BasicCode.DATA_NOT_EXIST, "没找到对应的开票信息:" + applyId);
        }
        InvoiceInfo info = invoiceInfos.get(0);
        InvoiceRevokeDTO revokeDTO = new InvoiceRevokeDTO();
        revokeDTO.setSerialNo(info.getSerialNo());
        InvoiceAccount invoiceAccount = invoiceAccountBiz.queryInvoiceAccountDetail(apply.getSellerId(),true);
        if (invoiceAccount == null) {
            throw new BizException(BasicCode.DATA_NOT_EXIST, "销售方开户信息");
        }
        InvoiceRequestDTO<InvoiceRevokeDTO> requestDTO = new InvoiceRequestDTO<>();
        requestDTO.setTaxNo(invoiceAccount.getTaxNo());
        requestDTO.setTokenType(invoiceAccount.getTokenType());
        requestDTO.setAccessToken(invoiceAccount.getAccessToken());
        requestDTO.setParam(revokeDTO);
        ItemResult<Void> result = electricInvoiceService.invoiceRevoke(requestDTO);
        if (result.isSuccess()) {
            apply.setRevokeTime(new Date());
            apply.setStatus(InvoiceApplyStatusEnum.WRITE_OFF.code());
            apply.setRevokeReason(revokeInvoiceApplyDTO.getRevokeReason());
            apply.setUpdateUser(revokeInvoiceApplyDTO.getUpdateUser());
            invoiceApplyMapper.updateByPrimaryKeySelective(apply);
            electricInvoiceNotifyBiz.synNotifyByApplyId(applyId, InvoiceApplyStatusEnum.WRITE_OFF.code());
        } else {
            throw new BizException(BasicCode.UNKNOWN_ERROR, result.getDescription());
        }
    }

    private void validateBizType(InvoiceApplyAddItem applyAddItem) {
        String bizType = applyAddItem.getBizType();
        InvoiceBizTypeEnum bizTypeEnum = InvoiceBizTypeEnum.getByCode(bizType);
        if (bizTypeEnum == null) {
            throw new BizException(BasicCode.INVALID_PARAM, "bizType不合法:" + bizType);
        }
        switch (bizTypeEnum) {
            case TRADE_INVOICE:
                if (isSame(applyAddItem.getAmount(), BigDecimal.ZERO) || !isSame(applyAddItem.getLogisticsAmount(), BigDecimal.ZERO)) {
                    throw new BizException(BasicCode.INVALID_PARAM, "交易发票金额不合法");
                }
                break;
            case LOGISTICS_INVOICE:
                if (!isSame(applyAddItem.getAmount(), BigDecimal.ZERO) || isSame(applyAddItem.getLogisticsAmount(), BigDecimal.ZERO)) {
                    throw new BizException(BasicCode.INVALID_PARAM, "运输发票金额不合法");
                }
                break;
            case TRADE_WITH_LOGISTICS:
                //do nothing
                break;
            default:
                throw new BizException(BasicCode.INVALID_PARAM, "bizType不合法:" + bizType);
        }
    }

    /**
     * 申请合并
     *
     * @param itemCategory
     * @param infoId
     * @param applyAddItemList
     * @return
     */
    private InvoiceInfoItem mergeAppItem(String itemCategory, String infoId, List<InvoiceApplyAddItem> applyAddItemList) {
        InvoiceInfoItem infoItem = new InvoiceInfoItem();
        InvoiceApplyAddItem firstItem = applyAddItemList.get(0);
        BeanUtils.copyProperties(firstItem, infoItem);
        infoItem.setPreferentialContent("");
        BigDecimal totalLogistics = BigDecimal.ZERO;
        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal totalNum = BigDecimal.ZERO;

        for (InvoiceApplyAddItem addItem : applyAddItemList) {
            totalAmount = add(totalAmount, addItem.getAmount());
            totalLogistics = add(totalLogistics, addItem.getLogisticsAmount());
            totalNum = add(totalNum, addItem.getItemNum());
        }
        BigDecimal amount = add(totalLogistics, totalAmount);
        InvoiceTaxMap taxRateByItemCategory = invoiceTaxMapBiz.findTaxRateByItemCategory(itemCategory);
        if (taxRateByItemCategory == null || taxRateByItemCategory.getTaxRate() == null) {
            throw new BizException(BasicCode.PARAM_NULL, "商品品类没有设置税率:" + itemCategory);
        }
        BigDecimal taxRate = taxRateByItemCategory.getTaxRate();
        infoItem.setTaxRate(taxRate);
        infoItem.setItemTaxCode(taxRateByItemCategory.getTaxCode());
        infoItem.setTaxName(taxRateByItemCategory.getTaxName());
        infoItem.setInfoId(infoId);
        infoItem.setInfoItemId(uuidGenerator.gain());

        infoItem.setAmount(amount);
        infoItem.setTaxIncluded("1");//含税

        BigDecimal beforeTaxAmount = divide(amount, add(BigDecimal.ONE, taxRate), 2);
        infoItem.setBeforeTaxAmount(beforeTaxAmount);
        infoItem.setTax(subtract(amount, beforeTaxAmount));
        infoItem.setBeforeTaxPrice(divide(beforeTaxAmount, totalNum, 6).toString());
        infoItem.setItemPrice(divide(amount, totalNum, 6).toString());
        infoItem.setInvoiceNature(0);
        infoItem.setItemNum(totalNum);
        infoItem.setIsPreferential("0");

        return infoItem;
    }

    private BigDecimal add(BigDecimal one, BigDecimal two) {
        if (one == null) {
            one = BigDecimal.ZERO;
        }
        if (two == null) {
            return one;
        }
        return one.add(two);
    }

    private BigDecimal subtract(BigDecimal one, BigDecimal two) {
        if (one == null) {
            one = BigDecimal.ZERO;
        }
        if (two == null) {
            return one;
        }
        return one.subtract(two);
    }

    private BigDecimal divide(BigDecimal one, BigDecimal two, int scale) {
        if (isSame(one, BigDecimal.ZERO) || isSame(two, BigDecimal.ZERO)) {
            return BigDecimal.ZERO;
        }
        if (one == null) {
            one = BigDecimal.ZERO;
        }
        return one.divide(two, scale, RoundingMode.HALF_UP);
    }

    private boolean isSame(BigDecimal one, BigDecimal two) {
        if (one == null) {
            one = BigDecimal.ZERO;
        }
        if (two == null) {
            two = BigDecimal.ZERO;
        }
        return one.equals(two);
    }

    private String decimalToString(BigDecimal one) {
        if (one == null) {
            return null;
        }
        return one.toString();
    }

}
