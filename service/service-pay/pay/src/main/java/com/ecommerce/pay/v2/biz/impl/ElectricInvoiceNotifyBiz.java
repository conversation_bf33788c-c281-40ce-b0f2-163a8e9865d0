package com.ecommerce.pay.v2.biz.impl;

import com.alibaba.fastjson.JSONObject;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.service.common.uuid.UUIDGenerator;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.mq.core.MQMessage;
import com.ecommerce.mq.core.service.IMQProducer;
import com.ecommerce.pay.api.v2.enums.invoice.InvoiceBizTypeEnum;
import com.ecommerce.pay.enums.CommonStatusEnum;
import com.ecommerce.pay.v2.biz.IElectricInvoiceNotifyBiz;
import com.ecommerce.pay.v2.dao.mapper.InvoiceApplyItemMapper;
import com.ecommerce.pay.v2.dao.mapper.InvoiceNotifyInfoMapper;
import com.ecommerce.pay.v2.dao.vo.InvoiceApplyItem;
import com.ecommerce.pay.v2.dao.vo.InvoiceNotifyInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;


@Slf4j
@Service("electricInvoiceNotifyBiz")
public class ElectricInvoiceNotifyBiz implements IElectricInvoiceNotifyBiz {

    @Autowired(required = false)
    private InvoiceApplyItemMapper invoiceApplyItemMapper;

    @Autowired(required = false)
    private InvoiceNotifyInfoMapper invoiceNotifyInfoMapper;

    @Autowired
    private UUIDGenerator uuidGenerator;

    @Autowired
    @Qualifier("rabbitProducer")
    private IMQProducer producer;

    private static final String INVOICE_NOTIFY_EXCHANGE = "invoice_notify";

    private static final String INVOICE_NOTIFY_KEY = "notify";

    private static final String INVOICE_NOTIFY_LGS_KEY = "notify_logistics";

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void synNotifyByApplyId(String applyId, String applyStatus) {
        if (CsStringUtils.isBlank(applyId) || CsStringUtils.isBlank(applyStatus)) {
            throw new BizException(BasicCode.PARAM_NULL, "申请ID或者状态");
        }
        Example itemExa = new Example(InvoiceApplyItem.class);
        itemExa.createCriteria().andEqualTo("applyId", applyId).andEqualTo("delFlg", "0");
        List<InvoiceApplyItem> itemList = invoiceApplyItemMapper.selectByExample(itemExa);
        if (CollectionUtils.isEmpty(itemList)) {
            return;
        }

        Map<String, List<InvoiceApplyItem>> bizNoMap = itemList.stream().collect(Collectors.groupingBy(InvoiceApplyItem::getBizNo));

        for (Map.Entry<String, List<InvoiceApplyItem>> entry : bizNoMap.entrySet()) {
            String bizNo = entry.getKey();
            String bizType = InvoiceBizTypeEnum.MIX_INVOICE.code();
            List<InvoiceApplyItem> sameBizNoList = entry.getValue();
            if (CollectionUtils.isEmpty(sameBizNoList)) {
                continue;
            }
            Set<String> bizTypeSet = sameBizNoList.stream().map(InvoiceApplyItem::getBizType).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(bizTypeSet)) {
                continue;
            }
            if (bizTypeSet.size() == 1) {
                bizType = bizTypeSet.iterator().next();
            }

            InvoiceNotifyInfo info = new InvoiceNotifyInfo();
            info.setNotifyId(uuidGenerator.gain());
            info.setApplyId(applyId);
            info.setApplyStatus(applyStatus);
            info.setBizNo(bizNo);
            info.setBizType(bizType);
            info.setNotifyStatus(CommonStatusEnum.INIT.code());
            info.setNotifyCount(0);
            invoiceNotifyInfoMapper.insertSelective(info);
        }

    }

    @Override
    public void batchNotifyToOrderTask() {
        log.info("开始定时推送开票申请结果到交易系统");
        List<InvoiceNotifyInfo> invoiceNotifyInfos = invoiceNotifyInfoMapper.selectForNotify();
        if (CollectionUtils.isEmpty(invoiceNotifyInfos)) {
            log.info("无需要定时推送的开票申请结果");
            return;
        }
        for (InvoiceNotifyInfo info : invoiceNotifyInfos) {
            try {
                int updateRowCount = invoiceNotifyInfoMapper.lockNotify(info.getNotifyId(), info.getNotifyStatus(), CommonStatusEnum.PROCESS.code());
                if (updateRowCount == 0) {
                    continue;
                }
                MQMessage<String> mqMessage = new MQMessage<>();
                mqMessage.setExchange(INVOICE_NOTIFY_EXCHANGE);
                mqMessage.setRetryTimes(5);
                if (CsStringUtils.equals(info.getBizType(), InvoiceBizTypeEnum.INNER_LGS_INVOICE.code())) {
                    //logistics 发票
                    mqMessage.setKey(INVOICE_NOTIFY_LGS_KEY);
                    mqMessage.setData(info.getBizNo() + "|" + info.getApplyStatus() + "|" + info.getReason());
                } else {
                    //order 发票
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("orderCode", info.getBizNo());

                    String bizType = info.getBizType();
                    String status = info.getApplyStatus();
                    if (CsStringUtils.equals(bizType, InvoiceBizTypeEnum.LOGISTICS_INVOICE.code())) {
                        jsonObject.put("invoiceStatus2", status);
                    } else if (CsStringUtils.equals(bizType, InvoiceBizTypeEnum.MIX_INVOICE.code())) {
                        jsonObject.put("invoiceStatus1", status);
                        jsonObject.put("invoiceStatus2", status);
                    } else {
                        jsonObject.put("invoiceStatus1", status);
                    }

                    mqMessage.setKey(INVOICE_NOTIFY_KEY);
                    mqMessage.setData(jsonObject.toJSONString());
                }
                producer.send(mqMessage);
                info.setNotifyStatus(CommonStatusEnum.SUCCESS.code());
            } catch (Exception e) {
                log.error("开票申请结果通知交易系统发生异常:{}", info, e);
                info.setNotifyStatus(CommonStatusEnum.FAIL.code());
                info.setNotifyMessage(e.getMessage());
            }
            info.setNotifyCount(info.getNotifyCount() + 1);
            info.setUpdateTime(new Date());
            invoiceNotifyInfoMapper.updateByPrimaryKeySelective(info);
        }
        log.info("完成定时推送开票申请结果到交易系统");
    }
}
