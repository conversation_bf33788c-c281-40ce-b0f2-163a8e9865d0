package com.ecommerce.pay.v2.biz.impl;

import cn.hutool.core.util.ObjectUtil;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.result.PageData;
import com.ecommerce.pay.api.constant.PayNumberConstant;
import com.github.pagehelper.page.PageMethod;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.pay.api.v2.dto.electricInvoiceAccount.PageQuery;
import com.ecommerce.pay.api.v2.dto.invoice.InvoiceApplyBizListDTO;
import com.ecommerce.pay.api.v2.dto.invoice.InvoiceApplyBizListQueryDTO;
import com.ecommerce.pay.api.v2.dto.invoice.InvoiceApplyDetailDTO;
import com.ecommerce.pay.api.v2.dto.invoice.InvoiceApplyListDTO;
import com.ecommerce.pay.api.v2.dto.invoice.InvoiceApplyListQueryDTO;
import com.ecommerce.pay.api.v2.dto.invoice.InvoiceInfoDTO;
import com.ecommerce.pay.api.v2.dto.invoice.InvoiceInfoItemDTO;
import com.ecommerce.pay.api.v2.dto.invoice.StatistcsInvoiceApplyDTO;
import com.ecommerce.pay.api.v2.dto.invoice.StatistcsInvoiceApplyItemDTO;
import com.ecommerce.pay.api.v2.dto.invoice.StatistcsInvoiceApplyResultDTO;
import com.ecommerce.pay.v2.biz.IElectricInvoiceQueryBiz;
import com.ecommerce.pay.v2.biz.IInvoiceAccountBiz;
import com.ecommerce.pay.v2.biz.IInvoiceApplyItemBiz;
import com.ecommerce.pay.v2.biz.IInvoiceInfoBiz;
import com.ecommerce.pay.v2.biz.IInvoiceInfoItemBiz;
import com.ecommerce.pay.v2.dao.mapper.InvoiceApplyMapper;
import com.ecommerce.pay.v2.dao.vo.InvoiceAccount;
import com.ecommerce.pay.v2.dao.vo.InvoiceApply;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 *  ElectricInvoiceQueryBiz
 *
 * <AUTHOR>
 */
@Slf4j
@Service("electricInvoiceQueryBiz")
public class ElectricInvoiceQueryBiz implements IElectricInvoiceQueryBiz {

    @Autowired
    private InvoiceApplyMapper invoiceApplyMapper;

    @Autowired
    private IInvoiceInfoBiz invoiceInfoBiz;

    @Autowired
    private IInvoiceInfoItemBiz invoiceInfoItemBiz;

    @Autowired
    private IInvoiceApplyItemBiz invoiceApplyItemBiz;

    @Autowired
    private IInvoiceAccountBiz invoiceAccountBiz;

    private static final String APPLY_ID = "applyId";

    private static final String CREATE_TIME = "createTime";

    @Override
    public PageData<InvoiceApplyListDTO> queryInvoiceApplyList(PageQuery<InvoiceApplyListQueryDTO> pageQuery) {
        InvoiceApplyListQueryDTO queryDTO = pageQuery.getQueryDTO();
        if (queryDTO.getCreateTimeRight() != null) {
            long timeStamp = queryDTO.getCreateTimeRight().getTime() + ******** - 1;
            queryDTO.setCreateTimeRight(new Date(timeStamp));
        }
        PageInfo<InvoiceApplyListDTO> pageInfo = PageMethod.startPage(
                        ObjectUtil.defaultIfNull(pageQuery.getPageNum(), PayNumberConstant.DEFAULT_PAGE_NUM),
                        ObjectUtil.defaultIfNull(pageQuery.getPageSize(), PayNumberConstant.TEN_INTEGER), true, false,null)
                .doSelectPageInfo(() -> invoiceApplyMapper.queryInvoiceApplyList(queryDTO));
        return new PageData<>(pageInfo);
    }

    @Override
    public InvoiceApplyDetailDTO queryInvoiceApplyDetail(String applyId) {
        if (CsStringUtils.isEmpty(applyId)) {
            throw new BizException(BasicCode.PARAM_NULL, APPLY_ID);
        }
        InvoiceApply invoiceApply = invoiceApplyMapper.selectByPrimaryKey(applyId);
        if (invoiceApply == null || invoiceApply.getDelFlg() == 1) {
            throw new BizException(BasicCode.DB_DATA_NOT_FOUND);
        }
        InvoiceApplyDetailDTO dto = convertInvoiceApplyDetailDTO(invoiceApply);

        InvoiceAccount invoiceAccount = invoiceAccountBiz.queryInvoiceAccountDetail(dto.getSellerId(),false);
        if (invoiceAccount != null) {
            // 卖家发票抬头
            dto.setSellerTitle(invoiceAccount.getSellerTitle());
            // 卖家地址
            dto.setSellerAddress(invoiceAccount.getSellerAddress());
            // 卖家开户行、账户
            dto.setSellerBankAccountName(invoiceAccount.getBankAccountName());
            dto.setSellerBankAccountNo(invoiceAccount.getBankAccountNo());
            // 税号
            dto.setSellerTaxNo(invoiceAccount.getTaxNo());
            // 电话
            dto.setSellerMobile(invoiceAccount.getSellerPhone());
        }
        InvoiceInfoDTO infoDTO = invoiceInfoBiz.findByApplyId(applyId);
        if (infoDTO != null) {
            // 买家发票抬头
            dto.setInvoiceTitle(infoDTO.getInvoiceTitle());
            // 买家地址
            dto.setBuyerAddress(infoDTO.getBuyerAddress());
            // 买家开户行、账户
            dto.setBuyerBankAccountName(infoDTO.getBuyerBankName());
            dto.setBuyerBankAccountNo(infoDTO.getBuyerBankNum());
            // 税号
            dto.setBuyerTaxNo(infoDTO.getBuyerTaxNo());
            dto.setBuyerMobile(infoDTO.getBuyerMobile());
            dto.setInvoiceNum(infoDTO.getInvoiceNum());
            dto.setInvoiceCode(infoDTO.getInvoiceCode());

            dto.setInvoiceUrl(infoDTO.getInvoiceUrl());

            List<InvoiceInfoItemDTO> itemDTOS = invoiceInfoItemBiz.findByInfoId(infoDTO.getInfoId());
            dto.setInvoiceInfoItemDTOList(itemDTOS);
        }
        return dto;
    }

    @Override
    public PageData<InvoiceApplyBizListDTO> queryInvoiceApplyBizList(PageQuery<InvoiceApplyBizListQueryDTO> pageQuery) {
        InvoiceApplyBizListQueryDTO queryDTO = Objects.requireNonNull(pageQuery.getQueryDTO(), "查询条件为空");

        if (CsStringUtils.isEmpty(queryDTO.getApplyId())) {
            throw new BizException(BasicCode.PARAM_NULL, APPLY_ID);
        }
        InvoiceApply invoiceApply = invoiceApplyMapper.selectByPrimaryKey(queryDTO.getApplyId());
        if (invoiceApply == null || invoiceApply.getDelFlg() == 1) {
            return new PageData<>();
        }
        String sellerName = invoiceApply.getSellerName();

        PageData<InvoiceApplyBizListDTO> pageData = invoiceApplyItemBiz.pageInvoiceApplyItemByBizNo(pageQuery);
        // 设置卖家名称
        for (InvoiceApplyBizListDTO dto : pageData.getList()) {
            dto.setSellerName(sellerName);
        }
        return pageData;
    }

    @Override
    public StatistcsInvoiceApplyResultDTO statistcsInvoiceApply(StatistcsInvoiceApplyDTO statistcsInvoiceApplyDTO) {
        String sellerId = statistcsInvoiceApplyDTO.getSellerId();
        if (CsStringUtils.isEmpty(sellerId)) {
            throw new BizException(BasicCode.PARAM_NULL, "sellerId");
        }
        StatistcsInvoiceApplyItemDTO currentMonthApply = invoiceApplyMapper.statisicsInvoiceApply(sellerId, true, false, false, false);
        StatistcsInvoiceApplyItemDTO currentMonthComplete = invoiceApplyMapper.statisicsInvoiceApply(sellerId, false, true, false, false);
        StatistcsInvoiceApplyItemDTO allComplete = invoiceApplyMapper.statisicsInvoiceApply(sellerId, false, false, true, false);
        StatistcsInvoiceApplyItemDTO allRedStamp = invoiceApplyMapper.statisicsInvoiceApply(sellerId, false, false, false, true);
        StatistcsInvoiceApplyResultDTO resultDTO = new StatistcsInvoiceApplyResultDTO();
        resultDTO.setCurrentMonthApply(currentMonthApply);
        resultDTO.setCurrentMonthComplete(currentMonthComplete);
        resultDTO.setAllComplete(allComplete);
        resultDTO.setAllRedStamp(allRedStamp);
        return resultDTO;
    }

    private InvoiceApplyListDTO convertInvoiceApplyListDTO(InvoiceApply vo) {
        if (vo != null) {
            InvoiceApplyListDTO dto = new InvoiceApplyListDTO();
            BeanUtils.copyProperties(vo, dto);
            return dto;
        }
        return null;
    }

    private InvoiceApplyDetailDTO convertInvoiceApplyDetailDTO(InvoiceApply vo) {
        if (vo != null) {
            InvoiceApplyDetailDTO dto = new InvoiceApplyDetailDTO();
            BeanUtils.copyProperties(vo, dto);
            return dto;
        }
        return null;
    }
}
