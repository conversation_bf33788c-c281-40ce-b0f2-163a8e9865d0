package com.ecommerce.pay.v2.biz.impl;

import com.github.pagehelper.page.PageMethod;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.pay.api.v2.dto.gnete.GneteBankInfoDTO;
import com.ecommerce.pay.api.v2.dto.gnete.GneteBankInfoQueryDTO;
import com.ecommerce.pay.v2.biz.IGneteBankInfoBiz;
import com.ecommerce.pay.v2.dao.mapper.GneteBankInfoMapper;
import com.ecommerce.pay.v2.dao.vo.GneteBankInfo;
import com.github.pagehelper.Page;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Condition;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class GneteBankInfoBiz implements IGneteBankInfoBiz {

    @Autowired
    private GneteBankInfoMapper gneteBankInfoMapper;

    @Override
    public List<GneteBankInfoDTO> findBankInfo(GneteBankInfoQueryDTO dto) {
        Condition condition = new Condition(GneteBankInfo.class);
        if (CsStringUtils.isNotBlank(dto.getBankName())) {
            condition.createCriteria().andLike("bankName", "%" + dto.getBankName() + "%");
        }
        if (CsStringUtils.isNotBlank(dto.getBankDetailName())) {
            condition.createCriteria().andLike("bankDetailName", "%" + dto.getBankDetailName() + "%");
        }
        condition.orderBy("bankDetailName");

        Page<GneteBankInfo> page = PageMethod.startPage(1, 30, false, false, false)
                .doSelectPage(() -> gneteBankInfoMapper.selectByCondition(condition));
        if(CollectionUtils.isNotEmpty(page)){
            return page.stream().map(item->{
                GneteBankInfoDTO itemDTO = new GneteBankInfoDTO();
                BeanUtils.copyProperties(item,itemDTO,"bankNo");
                if (CsStringUtils.isNotBlank(item.getBankNo())) {
                    try {
                        itemDTO.setBankNo(Long.parseLong(item.getBankNo()));
                    } catch (NumberFormatException e) {

                    }
                }
                return itemDTO;
            }).toList();
        }
        return Lists.newArrayList();
    }
}
