package com.ecommerce.pay.v2.biz.impl;

import com.alibaba.fastjson.JSON;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.member.api.dto.member.enums.MemberPlatform;
import com.ecommerce.open.api.dto.gnete.QueryProfitSharingRuleRequestDTO;
import com.ecommerce.open.api.dto.gnete.QueryProfitSharingRuleResponseDTO;
import com.ecommerce.open.api.dto.gnete.QueryProfitSharingRuleResponseRuleDTO;
import com.ecommerce.open.api.service.IGneteConnectorService;
import com.ecommerce.pay.api.v2.dto.MemberChannelDTO;
import com.ecommerce.pay.api.v2.enums.ChannelCodeEnum;
import com.ecommerce.pay.v2.biz.IGneteSplitRuleBiz;
import com.ecommerce.pay.v2.biz.IMemberChannelBiz;
import com.ecommerce.pay.v2.channel.adapter.gnete.GneteAdapter;
import com.ecommerce.pay.v2.dao.mapper.GneteSplitRuleFeeMapper;
import com.ecommerce.pay.v2.dao.mapper.GneteSplitRuleMapper;
import com.ecommerce.pay.v2.dao.mapper.GneteSplitRuleSettMapper;
import com.ecommerce.pay.v2.dao.vo.GneteSplitRule;
import com.ecommerce.pay.v2.dao.vo.GneteSplitRuleFee;
import com.ecommerce.pay.v2.dao.vo.GneteSplitRuleSett;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Condition;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class GneteSplitRuleBiz implements IGneteSplitRuleBiz {

    @Lazy
    @Autowired
    private IMemberChannelBiz memberChannelBiz;

    @Autowired
    private GneteSplitRuleMapper gneteSplitRuleMapper;
    @Autowired
    private GneteSplitRuleSettMapper gneteSplitRuleSettMapper;
    @Autowired
    private GneteSplitRuleFeeMapper gneteSplitRuleFeeMapper;
    @Autowired
    private IGneteConnectorService gneteConnectorService;
    @Value("${spring.cloud.config.profile:dev}")
    private String profile;



    @Override
    public Map<String, GneteSplitRuleSett> findRuleByProfitSharingWalletId(Collection<String> walletIdList, String bizType) {
        if(CollectionUtils.isEmpty(walletIdList)){
            throw new BizException(BasicCode.CUSTOM_ERROR,"分账方钱包id不可为空");
        }
        String bizType2 = "030001".equals(bizType) ? "04" : "12";

        String walletIds = "'"+String.join("','",walletIdList) +"'";
        Map<String, List<GneteSplitRuleSett>> groupList = gneteSplitRuleSettMapper.findRuleByProfitSharingWalletId(walletIds,bizType2).stream()
                //按规则分组 key为规则Id，value为分账方钱包id集合
                .collect(Collectors.groupingBy(GneteSplitRuleSett::getProfitSharingRuleId));
        //key分账方钱包Id，value:分账规则
        Map<String, GneteSplitRuleSett> resultMap = null;
        for (Map.Entry<String, List<GneteSplitRuleSett>> entry : groupList.entrySet()) {
            //如果分账方钱包id集合 包含当前所有的分账方，则返回
            if( entry.getValue().stream().map(GneteSplitRuleSett::getProfitSharingWalletId).toList().containsAll(walletIdList)){
                resultMap = entry.getValue().stream().collect(Collectors.toMap(GneteSplitRuleSett::getProfitSharingWalletId, Function.identity(),(k1,k2)->k1));
                break;
            }
        }
        if( resultMap == null || resultMap.isEmpty() ){
            syncSplitRule(bizType);
        }

        groupList = gneteSplitRuleSettMapper.findRuleByProfitSharingWalletId(walletIds,bizType2).stream()
                //按规则分组 key为规则Id，value为分账方钱包id集合
                .collect(Collectors.groupingBy(GneteSplitRuleSett::getProfitSharingRuleId));
        for (Map.Entry<String, List<GneteSplitRuleSett>> entry : groupList.entrySet()) {
            //如果分账方钱包id集合 包含当前所有的分账方，则返回
            if( entry.getValue().stream().map(GneteSplitRuleSett::getProfitSharingWalletId).toList().containsAll(walletIdList)){
                resultMap = entry.getValue().stream().collect(Collectors.toMap(GneteSplitRuleSett::getProfitSharingWalletId, Function.identity(),(k1,k2)->k1));
                break;
            }
        }
        if( resultMap == null || resultMap.isEmpty() ){
            log.info("分账规则没有找到 walletId: {}",walletIdList);
            if( "prod".equals(profile)){
                throw new BizException(BasicCode.CUSTOM_ERROR,"分账规则没有找到,请登录好易联企业电子支付中心配置(https://easylinkpay.gnete.com)");
            }else {
                throw new BizException(BasicCode.CUSTOM_ERROR,"分账规则没有找到,请登录好易联企业电子支付中心配置(https://121.8.234.54:9130),有问题问银联交流群里叫菊晴");
            }
        }
        return resultMap;
    }

    @Override
    public void syncSplitRule() {
        syncSplitRule("030001");//担保支付
        syncSplitRule("030002");//好支付
    }

    private void syncSplitRule(String bizType) {
        long s = System.currentTimeMillis();
        try {
            List<MemberChannelDTO> memberChannelDTOList = memberChannelBiz.findByMemberIdAndChannelCode(MemberPlatform.PLATFORM_MEMBERID.getId(), ChannelCodeEnum.GNETEPAY.getCode());
            if (CollectionUtils.isEmpty(memberChannelDTOList)) {
                log.info("平台钱包没找到");
                return;
            }
            String platformWalletId = memberChannelDTOList.get(0).getExtCustAcctId();
            if (CsStringUtils.isBlank(platformWalletId)) {
                log.info("平台钱包Id没找到");
                return;
            }
            List<String> statusList = Lists.newArrayList("03", "04");//03生效、04失效
            for (String status : statusList) {

                QueryProfitSharingRuleRequestDTO req = new QueryProfitSharingRuleRequestDTO();
                req.setPlatformWalletId(platformWalletId);
                req.setBizType(bizType);//业务类型	2N	O	030002-好支付、030001-担保支付
                req.setStatus(status);//状态	2X	O	01新建（待提交）、02提交（待初审）、03生效、04失效
                log.info("gneteConnectorService.queryProfitSharingRule req: {}", JSON.toJSONString(req));
                QueryProfitSharingRuleResponseDTO res = gneteConnectorService.queryProfitSharingRule(req);
                log.info("gneteConnectorService.queryProfitSharingRule res: {}", JSON.toJSONString(res));

                if (res == null || !GneteAdapter.SUCCESS.equals(res.getRspCode())) {
                    log.error("银联分账规则查询失败: {}", res == null ? "res = null" : res.getRspResult());
                    continue;
                }
                if (CollectionUtils.isEmpty(res.getRuleList())) {
                    log.error("银联分账规则查询结果为空");
                    continue;
                }
                for (QueryProfitSharingRuleResponseRuleDTO ruleDTO : res.getRuleList()) {
                    //处理生效或失效的规则
                    processRule(ruleDTO);
                }
                log.info("status: {},count: {}", status, res.getRuleList().size());
            }
        }finally {
            log.info("syncSplitRule cost time: {}", System.currentTimeMillis() - s);
        }
    }

    private void processRule(QueryProfitSharingRuleResponseRuleDTO ruleDTO) {
        if (CsStringUtils.isBlank(ruleDTO.getId())) {
            log.info("id is null");
            return;
        }
        //01新建（待提交）、02提交（待初审）、03生效、04失效
        if( "03".equals(ruleDTO.getStatus())){
            //添加或更新规则
            GneteSplitRule gneteSplitRule = new GneteSplitRule();
            BeanUtils.copyProperties(ruleDTO,gneteSplitRule);

            List<GneteSplitRuleFee> gneteSplitRuleFeeList = null;
            if(CollectionUtils.isNotEmpty(ruleDTO.getFeeRuleList()) ){
                gneteSplitRuleFeeList = ruleDTO.getFeeRuleList().stream().map(item -> {
                    GneteSplitRuleFee gneteSplitRuleFee = new GneteSplitRuleFee();
                    BeanUtils.copyProperties(item, gneteSplitRuleFee);
                    return gneteSplitRuleFee;
                }).toList();

            }
            List<GneteSplitRuleSett> gneteSplitRuleSettList = null;
            if( CollectionUtils.isNotEmpty(ruleDTO.getSettRuleList()) ){
                gneteSplitRuleSettList = ruleDTO.getSettRuleList().stream().map(item -> {
                    GneteSplitRuleSett gneteSplitRuleSett = new GneteSplitRuleSett();
                    BeanUtils.copyProperties(item, gneteSplitRuleSett);
                    return gneteSplitRuleSett;
                }).toList();
            }
            if(gneteSplitRuleMapper.existsWithPrimaryKey(ruleDTO.getId())){
                return;
            }
            //新增
            gneteSplitRuleMapper.insert(gneteSplitRule);
            if( CollectionUtils.isNotEmpty(gneteSplitRuleFeeList) ){
                gneteSplitRuleFeeMapper.insertList(gneteSplitRuleFeeList);
            }
            if( CollectionUtils.isNotEmpty(gneteSplitRuleSettList) ){
                gneteSplitRuleSettMapper.insertList(gneteSplitRuleSettList);
            }
        }else{
            //04失效 删除失效的记录
            Condition feeCondition = new Condition(GneteSplitRuleFee.class);
            feeCondition.createCriteria().andEqualTo("profitSharingRuleId",ruleDTO.getId());
            gneteSplitRuleFeeMapper.deleteByCondition(feeCondition);
            Condition settCondition = new Condition(GneteSplitRuleSett.class);
            settCondition.createCriteria().andEqualTo("profitSharingRuleId",ruleDTO.getId());
            gneteSplitRuleSettMapper.deleteByCondition(settCondition);
            gneteSplitRuleMapper.deleteByPrimaryKey(ruleDTO.getId());
        }
    }
}
