package com.ecommerce.pay.v2.biz.impl;

import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Lists;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.common.service.common.BaseBiz;
import com.ecommerce.common.service.common.uuid.UUIDGenerator;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.open.api.dto.invoice.AccountTokenDTO;
import com.ecommerce.open.api.dto.invoice.TokenApplyDTO;
import com.ecommerce.open.api.service.IElectricInvoiceService;
import com.ecommerce.pay.api.v2.dto.electricInvoiceAccount.DeleteInvoiceAccountDTO;
import com.ecommerce.pay.api.v2.dto.electricInvoiceAccount.InvoiceAccountDTO;
import com.ecommerce.pay.api.v2.dto.electricInvoiceAccount.InvoiceAccountListDTO;
import com.ecommerce.pay.api.v2.dto.electricInvoiceAccount.InvoiceAccountQueryDTO;
import com.ecommerce.pay.api.v2.dto.electricInvoiceAccount.PageQuery;
import com.ecommerce.pay.api.v2.dto.electricInvoiceAccount.SaveInvoiceAccountDTO;
import com.ecommerce.pay.util.LocalDateUtils;
import com.ecommerce.pay.v2.biz.IInvoiceAccountBiz;
import com.ecommerce.pay.v2.dao.bean.InvoiceAccountSumBean;
import com.ecommerce.pay.v2.dao.mapper.InvoiceAccountMapper;
import com.ecommerce.pay.v2.dao.mapper.InvoiceApplyMapper;
import com.ecommerce.pay.v2.dao.vo.InvoiceAccount;
import com.ecommerce.pay.v2.service.IBankInfoService;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description:
 * @date 25/04/2019 17:35
 */
@Slf4j
@Service
public class InvoiceAccountBiz extends BaseBiz<InvoiceAccount> implements IInvoiceAccountBiz {

    @Autowired
    private InvoiceAccountMapper invoiceAccountMapper;

    @Autowired
    private InvoiceApplyMapper invoiceApplyMapper;

    @Autowired
    private IElectricInvoiceService electricInvoiceService;

    @Autowired
    private IBankInfoService bankInfoService;

    @Autowired
    private UUIDGenerator uuidGenerator;

    @Override
    public String enterInvoiceAccount(SaveInvoiceAccountDTO saveInvoiceAccountDTO) {
        if (CsStringUtils.isBlank(saveInvoiceAccountDTO.getSellerId())) {
            throw new BizException(BasicCode.INVALID_PARAM, "开票方平台账号ID");
        }
        Condition condition = new Condition(InvoiceAccount.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("sellerId", saveInvoiceAccountDTO.getSellerId()).andEqualTo("delFlg", false);
        List<InvoiceAccount> invoiceAccountList = invoiceAccountMapper.selectByCondition(condition);
        if (CollectionUtils.isNotEmpty(invoiceAccountList)) {
            throw new BizException(BasicCode.DATA_NOT_EXIST, "销售方开户信息");
        }
        //主键
        InvoiceAccount invoiceAccount = new InvoiceAccount();
        BeanUtils.copyProperties(saveInvoiceAccountDTO, invoiceAccount);
        invoiceAccount.setInvoiceAccountId(uuidGenerator.gain());
        //调用open接口返回token
        if (CsStringUtils.isBlank(saveInvoiceAccountDTO.getAccountName())) {
            throw new BizException(BasicCode.PARAM_NULL, "发票平台账号");
        }
        if (CsStringUtils.isBlank(saveInvoiceAccountDTO.getAccountPassword())) {
            throw new BizException(BasicCode.PARAM_NULL, "发票平台密码");
        }
        TokenApplyDTO tokenApplyDTO = new TokenApplyDTO();
        tokenApplyDTO.setUserName(saveInvoiceAccountDTO.getAccountName());
        tokenApplyDTO.setPassword(saveInvoiceAccountDTO.getAccountPassword());

        AccountTokenDTO tokenDTO = accessToken(tokenApplyDTO);
        if (tokenDTO == null) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "获取账号信息失败");
        }
        try {
            invoiceAccount.setAccessToken(tokenDTO.getAccessToken());
            invoiceAccount.setRefreshToken(tokenDTO.getRefreshToken());
            invoiceAccount.setScope(tokenDTO.getScope());
            invoiceAccount.setTokenType(tokenDTO.getTokenType());
        } catch (Exception e) {
            log.error("获取账户token信息失败:" + e);
            throw new BizException(BasicCode.UNDEFINED_ERROR, "请检查账号密码");
        }
        //token失效时间，暂时设置成永久
        Calendar curr = Calendar.getInstance();
        curr.set(Calendar.YEAR, curr.get(Calendar.YEAR) + 100);
        Date date = curr.getTime();
        invoiceAccount.setExpireTime(date);
        invoiceAccount.setDelFlg(false);
        Date now = new Date();
        invoiceAccount.setCreateTime(now);
        invoiceAccount.setUpdateTime(now);
        invoiceAccount.setCreateUser(saveInvoiceAccountDTO.getOperator());
        invoiceAccount.setUpdateUser(saveInvoiceAccountDTO.getOperator());
        invoiceAccountMapper.insert(invoiceAccount);

        return invoiceAccount.getInvoiceAccountId();
    }

    @Override
    public void editInvoiceAccount(SaveInvoiceAccountDTO saveInvoiceAccountDTO) {
        InvoiceAccount old = invoiceAccountMapper.selectByPrimaryKey(saveInvoiceAccountDTO.getInvoiceAccountId());
        if (old != null && !old.getDelFlg()) {
            InvoiceAccount vo = new InvoiceAccount();
            BeanUtils.copyProperties(saveInvoiceAccountDTO, vo);
            vo.setUpdateUser(saveInvoiceAccountDTO.getOperator());
            vo.setUpdateTime(new Date());
            invoiceAccountMapper.updateByPrimaryKeySelective(vo);
        }
    }

    @Override
    public void deleteInvoiceAccount(DeleteInvoiceAccountDTO deleteInvoiceAccountDTO) {
        Condition condition = new Condition(InvoiceAccount.class);
        condition.createCriteria().andEqualTo("invoiceAccountId", deleteInvoiceAccountDTO.getInvoiceAccountId());
        InvoiceAccount vo = new InvoiceAccount();
        vo.setDelFlg(true);
        vo.setUpdateUser(deleteInvoiceAccountDTO.getUpdateUser());
        vo.setUpdateTime(new Date());
        invoiceAccountMapper.updateByConditionSelective(vo, condition);
    }

    @Override
    public InvoiceAccount queryInvoiceAccountDetail(String sellerId,Boolean needToken) {
        if (CsStringUtils.isBlank(sellerId)) {
            throw new BizException(BasicCode.INVALID_PARAM, "销售方ID");
        }
        Condition condition = new Condition(InvoiceAccount.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("sellerId", sellerId).andEqualTo("delFlg", false);
        List<InvoiceAccount> invoiceAccountList = invoiceAccountMapper.selectByCondition(condition);
        if (CollectionUtils.isEmpty(invoiceAccountList)) {
           return null;
        }
        InvoiceAccount invoiceAccount = invoiceAccountList.get(0);

        if(needToken){
            //获取token
            TokenApplyDTO tokenApplyDTO = new TokenApplyDTO();
            tokenApplyDTO.setUserName(invoiceAccount.getAccountName());
            tokenApplyDTO.setPassword(invoiceAccount.getAccountPassword());
            AccountTokenDTO accountTokenDTO = accessToken(tokenApplyDTO);
            BeanUtils.copyProperties(accountTokenDTO, invoiceAccount);
        }
        return invoiceAccount;
    }

    @Override
    public Map<String, InvoiceAccountDTO> queryInvoiceAccountStatus(List<String> sellerIdList) {
        if (CollectionUtils.isEmpty(sellerIdList)) {
            throw new BizException(BasicCode.INVALID_PARAM, "销售方ID列表");
        }

        Map<String, InvoiceAccountDTO> accountMap = new HashMap<>();
        Condition condition = new Condition(InvoiceAccount.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("sellerId", sellerIdList);
        List<InvoiceAccount> invoiceAccountList = invoiceAccountMapper.selectByCondition(condition);
        if (CollectionUtils.isNotEmpty(invoiceAccountList)) {
            for (InvoiceAccount invoiceAccount : invoiceAccountList) {
                InvoiceAccountDTO invoiceAccountDTO = new InvoiceAccountDTO();
                BeanUtils.copyProperties(invoiceAccount, invoiceAccountDTO);
                accountMap.put(invoiceAccount.getSellerId(), invoiceAccountDTO);
            }
        }

        return accountMap;
    }

    //invoice_apply 查询
    //分页属性在 pageQuery 中,无视QueryDTO中的pageSize 和 pageNum
    @Override
    public PageData<InvoiceAccountListDTO> queryInvoiceAccountList(PageQuery<InvoiceAccountQueryDTO> pageQuery) {
        InvoiceAccountQueryDTO queryDTO = pageQuery.getQueryDTO();
        if (queryDTO == null) {
            return new PageData<>();
        }
        Condition condition = new Condition(InvoiceAccount.class);
        Example.Criteria criteria = condition.createCriteria();
        if (CsStringUtils.isNotBlank(queryDTO.getSellerName())) {
            criteria.andLike("sellerName", "%" + queryDTO.getSellerName() + "%");
        }
        if (CsStringUtils.isNotBlank(queryDTO.getTaxNo())) {
            criteria.andLike("taxNo", "%" + queryDTO.getTaxNo() + "%");
        }
        if (CsStringUtils.isNotBlank(queryDTO.getProcessMode())) {
            criteria.andEqualTo("processMode", queryDTO.getProcessMode());
        }
        if (null != queryDTO.getBeginDate()) {
            criteria.andGreaterThanOrEqualTo("createTime", LocalDateUtils.getBeginDate(queryDTO.getBeginDate()));
        }
        if (null != queryDTO.getEndDate()) {
            criteria.andLessThanOrEqualTo("createTime", LocalDateUtils.getEndDate(queryDTO.getEndDate()));
        }
        criteria.andEqualTo("delFlg", 0);
        PageInfo<InvoiceAccount> pageInfo = new PageInfo<>();
        pageInfo.setPageSize(pageQuery.getPageSize() == null || pageQuery.getPageSize() < 1 ? 20 : pageQuery.getPageSize());
        pageInfo.setPageNum(pageQuery.getPageNum() == null || pageQuery.getPageNum() < 1 ? 1 : pageQuery.getPageNum());
        pageInfo = PageMethod.startPage(pageInfo.getPageNum(), pageInfo.getPageSize()).doSelectPageInfo(() -> {
            invoiceAccountMapper.selectByCondition(condition);
        });
        PageData<InvoiceAccountListDTO> pageData = new PageData<>();
        if (pageInfo == null || pageInfo.getList() == null || pageInfo.getList().isEmpty()) {
            return pageData;
        }
        Map<String, InvoiceAccountListDTO> invoiceAccountMap = new HashMap<>();
        for (InvoiceAccount invoiceAccount : pageInfo.getList()) {
            InvoiceAccountListDTO invoiceAccountListDTO = new InvoiceAccountListDTO();
            BeanUtils.copyProperties(invoiceAccount, invoiceAccountListDTO);
            invoiceAccountListDTO.setTotalInvoice(0);
            invoiceAccountListDTO.setTotalMoney(BigDecimal.ZERO);
            invoiceAccountMap.put(invoiceAccount.getSellerId(), invoiceAccountListDTO);
        }
        List<InvoiceAccountSumBean> accountSumBeanList = invoiceApplyMapper.sumInvoiceAndMoneyBySellerId(Lists.newArrayList(invoiceAccountMap.keySet()));
        if (CollectionUtils.isNotEmpty(accountSumBeanList)) {
            accountSumBeanList.stream().filter(invoiceAccountSumBean -> invoiceAccountMap.get(invoiceAccountSumBean.getSellerId()) != null)
                    .forEach(invoiceAccountSumBean -> BeanUtils.copyProperties(invoiceAccountSumBean, invoiceAccountMap.get(invoiceAccountSumBean.getSellerId())));
        }
        pageData.setList(Lists.newArrayList(invoiceAccountMap.values()));
        pageData.setTotal(pageInfo.getTotal());
        pageData.setPages(pageInfo.getPages());
        pageData.setPageNum(pageInfo.getPageNum());
        pageData.setPageSize(pageInfo.getPageSize());
        return pageData;
    }

    private InvoiceAccountDTO vo2Dto(InvoiceAccount vo) {
        if (vo == null) {
            return null;
        }
        InvoiceAccountDTO dto = new InvoiceAccountDTO();
        BeanUtils.copyProperties(vo, dto);
        return dto;
    }

    //获取token
    private AccountTokenDTO accessToken(TokenApplyDTO tokenApplyDTO) {
        ItemResult<AccountTokenDTO> accountTokenDTO = electricInvoiceService.applyToken(tokenApplyDTO);
        log.info("AccountTokenDTO:" + JSON.toJSONString(accountTokenDTO));
        if (accountTokenDTO == null || !accountTokenDTO.isSuccess()) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "发票平台账号密码错误");
        }
        return accountTokenDTO.getData();
    }
}
