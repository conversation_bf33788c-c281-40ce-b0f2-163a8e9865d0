package com.ecommerce.pay.v2.biz.impl;

import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Lists;
import com.ecommerce.common.result.PageData;
import com.ecommerce.pay.api.v2.dto.electricInvoiceAccount.PageQuery;
import com.ecommerce.pay.api.v2.dto.invoice.InvoiceApplyBizListDTO;
import com.ecommerce.pay.api.v2.dto.invoice.InvoiceApplyBizListQueryDTO;
import com.ecommerce.pay.api.v2.dto.invoice.InvoiceApplyItemDTO;
import com.ecommerce.pay.api.v2.dto.invoice.extra.InvoiceOrderExtra;
import com.ecommerce.pay.api.v2.enums.invoice.InvoiceBizTypeEnum;
import com.ecommerce.pay.v2.biz.IInvoiceApplyItemBiz;
import com.ecommerce.pay.v2.dao.mapper.InvoiceApplyItemMapper;
import com.ecommerce.pay.v2.dao.mapper.InvoiceApplyMapper;
import com.ecommerce.pay.v2.dao.vo.InvoiceApply;
import com.ecommerce.pay.v2.dao.vo.InvoiceApplyItem;
import com.github.pagehelper.Page;
import com.github.pagehelper.page.PageMethod;
import lombok.extern.slf4j.Slf4j;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Condition;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @created 14:24 07/05/2019
 * @description
 */
@Slf4j
@Service
public class InvoiceApplyItemBiz implements IInvoiceApplyItemBiz {

    @Autowired
    private InvoiceApplyMapper invoiceApplyMapper;

    @Autowired
    private InvoiceApplyItemMapper invoiceApplyItemMapper;

    @Override
    public PageData<InvoiceApplyBizListDTO> pageInvoiceApplyItemByBizNo(PageQuery<InvoiceApplyBizListQueryDTO> pageQuery) {
        if (pageQuery.getPageNum() == null || pageQuery.getPageNum() == 0) {
            pageQuery.setPageNum(1);
        }
        if (pageQuery.getPageSize() == null || pageQuery.getPageSize() < 10) {
            pageQuery.setPageSize(10);
        }

        Page<String> bizNoList = pageBizNoListByApplyId(pageQuery);

        if (CollectionUtils.isEmpty(bizNoList)) {
            PageData<InvoiceApplyBizListDTO> pageData = new PageData<>();
            pageData.setPageNum(pageQuery.getPageNum());
            return pageData;
        }

        Condition condition = new Condition(InvoiceApplyItem.class);
        condition.createCriteria()
                .andIn("bizNo", bizNoList)
                .andEqualTo("delFlg", 0);
        List<InvoiceApplyItem> invoiceApplyItemList = invoiceApplyItemMapper.selectByCondition(condition);
        //获取申请类型
        InvoiceApply invoiceApply = invoiceApplyMapper.selectByPrimaryKey(pageQuery.getQueryDTO().getApplyId());
        Map<String, List<InvoiceApplyItemDTO>> listMap = new HashMap<>();
        Map<String, BigDecimal> logisticsMap = new HashMap<>();
        for (InvoiceApplyItem invoiceApplyItem : invoiceApplyItemList) {
            InvoiceApplyItemDTO invoiceApplyItemDTO = new InvoiceApplyItemDTO();
            BeanUtils.copyProperties(invoiceApplyItem, invoiceApplyItemDTO);
            //混合开票
            if (InvoiceBizTypeEnum.LOGISTICS_INVOICE.getCode().equals(invoiceApplyItem.getBizType())) {
                logisticsMap.put(invoiceApplyItem.getBizNo(), invoiceApplyItem.getLogisticsAmount());
                invoiceApplyItemDTO.setAmount(invoiceApplyItem.getLogisticsAmount());
                if (InvoiceBizTypeEnum.MIX_INVOICE.getCode().equals(invoiceApply.getBizType())) continue;
            }
            if (CsStringUtils.equals(invoiceApplyItem.getApplyId(), pageQuery.getQueryDTO().getApplyId())) {
                if (listMap.get(invoiceApplyItem.getBizNo()) == null) {
                    listMap.put(invoiceApplyItem.getBizNo(), Lists.newArrayList(invoiceApplyItemDTO));
                } else {
                    listMap.get(invoiceApplyItem.getBizNo()).add(invoiceApplyItemDTO);
                }
            }
        }

        Page<InvoiceApplyBizListDTO> collect = bizNoList.stream().map(bizNo -> {
            InvoiceApplyBizListDTO invoiceApplyBizListDTO = new InvoiceApplyBizListDTO();
            invoiceApplyBizListDTO.setBizNo(bizNo);
            List<InvoiceApplyItemDTO> invoiceApplyItemDTOList = listMap.get(bizNo);
            invoiceApplyBizListDTO.setInvoiceApplyItemDTOList(invoiceApplyItemDTOList);
            invoiceApplyBizListDTO.setLogisticsAmount(logisticsMap.getOrDefault(bizNo, BigDecimal.ZERO));
            if (!CollectionUtils.isEmpty(invoiceApplyItemDTOList)) {
                String extraContent = invoiceApplyItemDTOList.get(0).getExtraContent();
                if (!CsStringUtils.isEmpty(extraContent)) {
                    InvoiceOrderExtra extra = JSON.parseObject(extraContent, InvoiceOrderExtra.class);
                    invoiceApplyBizListDTO.setInvoiceOrderExtra(extra);
                }
            }
            return invoiceApplyBizListDTO;
        }).collect(Collectors.toCollection(Page::new));
        BeanUtils.copyProperties(bizNoList, collect);
        return new PageData<>(collect);
    }

    private Page<String> pageBizNoListByApplyId(PageQuery<InvoiceApplyBizListQueryDTO> pageQuery) {
        InvoiceApplyBizListQueryDTO queryDTO = pageQuery.getQueryDTO();
        Condition condition = new Condition(InvoiceApplyItem.class);
        condition.setDistinct(true);
        condition.createCriteria()
                .andEqualTo("applyId", queryDTO.getApplyId())
                .andEqualTo("delFlg", 0);
        condition.selectProperties("bizNo");
        condition.orderBy("createTime").desc();

        Page<InvoiceApplyItem> page = PageMethod.startPage(pageQuery.getPageNum(), pageQuery.getPageSize())
                .doSelectPage(() -> invoiceApplyItemMapper.selectByCondition(condition));
        Page<String> collect = page.stream().map(InvoiceApplyItem::getBizNo).collect(Collectors.toCollection(Page::new));
        BeanUtils.copyProperties(page, collect);
        return collect;
    }

    private InvoiceApplyItemDTO convertDTO(InvoiceApplyItem vo) {
        if (vo != null) {
            InvoiceApplyItemDTO dto = new InvoiceApplyItemDTO();
            BeanUtils.copyProperties(vo, dto);
            return dto;
        }
        return null;
    }
}
