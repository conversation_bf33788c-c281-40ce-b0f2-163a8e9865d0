package com.ecommerce.pay.v2.biz.impl;

import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.service.common.BaseBiz;
import com.ecommerce.pay.api.v2.dto.invoice.InvoiceInfoDTO;
import com.ecommerce.pay.v2.biz.IInvoiceInfoBiz;
import com.ecommerce.pay.v2.dao.mapper.InvoiceInfoMapper;
import com.ecommerce.pay.v2.dao.vo.InvoiceInfo;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @created 9:54 07/05/2019
 * @description
 */
@Service
public class InvoiceInfoBiz extends BaseBiz<InvoiceInfo> implements IInvoiceInfoBiz {

    @Autowired
    private InvoiceInfoMapper invoiceInfoMapper;

    @Override
    public InvoiceInfoDTO findByInvoiceNum(String invoiceNum) {
        if (ObjectUtils.isEmpty(invoiceNum)) {
            throw new BizException(BasicCode.PARAM_NULL, "invoiceNum");
        }
        InvoiceInfo info = new InvoiceInfo();
        info.setInvoiceNum(invoiceNum);
        info.setDelFlg((byte) 0);
        List<InvoiceInfo> select = invoiceInfoMapper.select(info);
        if (CollectionUtils.isEmpty(select)) {
            return null;
        }
        return convertDTO(select.get(0));
    }

    @Override
    public InvoiceInfoDTO findByApplyId(String applyId) {
        if (ObjectUtils.isEmpty(applyId)) {
            throw new BizException(BasicCode.PARAM_NULL, "applyId");
        }
        InvoiceInfo info = new InvoiceInfo();
        info.setApplyId(applyId);
        info.setDelFlg((byte) 0);
        List<InvoiceInfo> select = invoiceInfoMapper.select(info);
        if (CollectionUtils.isEmpty(select)) {
            return null;
        }
        return convertDTO(select.get(0));
    }

    private InvoiceInfoDTO convertDTO(InvoiceInfo vo) {
        if (vo != null) {
            InvoiceInfoDTO dto = new InvoiceInfoDTO();
            BeanUtils.copyProperties(vo, dto);
            return dto;
        }
        return null;
    }
}
