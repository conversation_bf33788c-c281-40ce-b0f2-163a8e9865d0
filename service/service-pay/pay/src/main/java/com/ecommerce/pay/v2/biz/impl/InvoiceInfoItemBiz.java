package com.ecommerce.pay.v2.biz.impl;

import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.service.common.BaseBiz;
import com.ecommerce.pay.api.v2.dto.invoice.InvoiceInfoItemDTO;
import com.ecommerce.pay.v2.biz.IInvoiceInfoItemBiz;
import com.ecommerce.pay.v2.dao.mapper.InvoiceInfoItemMapper;
import com.ecommerce.pay.v2.dao.vo.InvoiceInfoItem;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @created 13:58 07/05/2019
 * @description
 */
@Service
public class InvoiceInfoItemBiz extends BaseBiz<InvoiceInfoItem> implements IInvoiceInfoItemBiz {

    @Autowired
    private InvoiceInfoItemMapper invoiceInfoItemMapper;

    @Override
    public List<InvoiceInfoItemDTO> findByInfoId(String infoId) {
        if (ObjectUtils.isEmpty(infoId)) {
            throw new BizException(BasicCode.PARAM_NULL, "infoId");
        }
        InvoiceInfoItem item = new InvoiceInfoItem();
        item.setInfoId(infoId);
        item.setDelFlg((byte) 0);
        List<InvoiceInfoItem> select = invoiceInfoItemMapper.select(item);
        return select.stream().map(this::convertDTO).toList();
    }

    private InvoiceInfoItemDTO convertDTO(InvoiceInfoItem vo) {
        if (vo != null) {
            InvoiceInfoItemDTO dto = new InvoiceInfoItemDTO();
            BeanUtils.copyProperties(vo, dto);
            return dto;
        }
        return null;
    }
}
