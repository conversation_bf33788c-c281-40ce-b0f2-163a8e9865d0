package com.ecommerce.pay.v2.biz.impl;

import com.ecommerce.common.service.common.BaseBiz;
import com.ecommerce.pay.v2.dao.mapper.InvoiceTaxMapMapper;
import com.ecommerce.pay.v2.dao.vo.InvoiceTaxMap;
import com.ecommerce.pay.v2.biz.IInvoiceTaxMapBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;
import com.ecommerce.common.utils.CsStringUtils;

/**
 * <AUTHOR>
 * @description:
 * @date 26/04/2019 10:52
 */
@Service("invoiceTaxMapBiz")
public class InvoiceTaxMapBiz extends BaseBiz<InvoiceTaxMap> implements IInvoiceTaxMapBiz {

    @Autowired(required = false)
    private InvoiceTaxMapMapper invoiceTaxMapMapper;


    @Override
    public InvoiceTaxMap findTaxRateByItemCategory(String itemCategory) {
        if (CsStringUtils.isBlank(itemCategory)) {
            return null;
        }
        return invoiceTaxMapMapper.findOneByItemCategoryLike(itemCategory);
    }

    @Override
    public InvoiceTaxMap findTaxRateByItemCode(String itemCode) {
        if (CsStringUtils.isBlank(itemCode)) {
            return null;
        }
        Condition condition = new Condition(InvoiceTaxMap.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("delFlg", false).andEqualTo("itemCode", itemCode);
        return invoiceTaxMapMapper.selectByCondition(condition).stream().findFirst().orElse(null);
    }
}
