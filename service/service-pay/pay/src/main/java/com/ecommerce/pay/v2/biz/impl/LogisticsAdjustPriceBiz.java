package com.ecommerce.pay.v2.biz.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.result.PageData;
import com.ecommerce.common.service.common.uuid.UUIDGenerator;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.logistics.api.dto.LogisticsAdjustPriceDTO;
import com.ecommerce.logistics.api.enums.TransportToolTypeEnum;
import com.ecommerce.logistics.api.service.ILogisticsAdjustPriceService;
import com.ecommerce.pay.api.constant.PayNumberConstant;
import com.ecommerce.pay.api.v2.dto.adjust.AdjustPriceBillPayResultDTO;
import com.ecommerce.pay.api.v2.dto.adjust.AdjustPricePayResultDTO;
import com.ecommerce.pay.api.v2.dto.logisticsAdjust.LogisticsAdjustPriceCalculateDTO;
import com.ecommerce.pay.api.v2.dto.logisticsAdjust.LogisticsAdjustPriceItemCalculateDTO;
import com.ecommerce.pay.api.v2.dto.logisticsAdjust.LogisticsAdjustPriceItemDTO;
import com.ecommerce.pay.api.v2.dto.logisticsAdjust.LogisticsAdjustPriceMemberCalculateDTO;
import com.ecommerce.pay.api.v2.dto.logisticsAdjust.LogisticsAdjustPriceMemberDTO;
import com.ecommerce.pay.api.v2.dto.logisticsAdjust.LogisticsAdjustPriceQueryDTO;
import com.ecommerce.pay.api.v2.dto.logisticsAdjust.PageQuery;
import com.ecommerce.pay.util.LogisticsAdjustPriceBizUidGenerator;
import com.ecommerce.pay.v2.biz.ILogisticsAdjustPriceBiz;
import com.ecommerce.pay.v2.dao.mapper.LogisticsAdjustPriceItemMapper;
import com.ecommerce.pay.v2.dao.mapper.LogisticsAdjustPriceMapper;
import com.ecommerce.pay.v2.dao.mapper.LogisticsAdjustPriceMemberMapper;
import com.ecommerce.pay.v2.dao.vo.LogisticsAdjustPrice;
import com.ecommerce.pay.v2.dao.vo.LogisticsAdjustPriceItem;
import com.ecommerce.pay.v2.dao.vo.LogisticsAdjustPriceMember;
import com.ecommerce.pay.v2.exception.LogisticsAdjistPriceCode;
import com.ecommerce.pay.v2.service.jobhandler.LogisticsAdjustPriceSyncToERPJob;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.xxl.job.core.context.XxlJobContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example.Criteria;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Service
public class LogisticsAdjustPriceBiz implements ILogisticsAdjustPriceBiz {
    private final static Logger logger = LoggerFactory.getLogger(LogisticsAdjustPriceBiz.class);

    @Autowired
    private LogisticsAdjustPriceMapper logisticsAdjustPriceMapper;

    @Autowired
    private ILogisticsAdjustPriceService logisticsAdjustPriceService;

    @Autowired
    private UUIDGenerator uuidGenerator;

    @Autowired
    private LogisticsAdjustPriceBizUidGenerator logisticsAdjustPriceBizUidGenerator;

    @Autowired
    private LogisticsAdjustPriceMapper adjustPriceMapper;

    @Autowired
    private LogisticsAdjustPriceMemberMapper adjustPriceMemberMapper;

    @Autowired
    private LogisticsAdjustPriceItemMapper adjustPriceItemMapper;

    @Autowired
    private LogisticsAdjustPriceSyncToERPJob adjustPriceSyncToERPJob;

    @Override
    public PageData<LogisticsAdjustPriceQueryDTO> pageLogisticsAdjustPrice(
            PageQuery<LogisticsAdjustPriceQueryDTO> pageQuery) {
        logger.info("物流调价列表查询入参：{}", pageQuery);
        LogisticsAdjustPriceQueryDTO adjustPriceQueryDTO = pageQuery.getQueryDTO() == null ? new LogisticsAdjustPriceQueryDTO() : pageQuery.getQueryDTO();
        PageInfo<LogisticsAdjustPriceQueryDTO> pageInfo = PageMethod.startPage(
                ObjectUtil.defaultIfNull(pageQuery.getPageNum(), PayNumberConstant.DEFAULT_PAGE_NUM),
                ObjectUtil.defaultIfNull(pageQuery.getPageSize(), PayNumberConstant.DEFAULT_PAGE_SIZE)).doSelectPageInfo(() -> logisticsAdjustPriceMapper.queryLogisticAdjustPriceList(adjustPriceQueryDTO));
        return new PageData<>(pageInfo);
    }

    @Override
    public List<LogisticsAdjustPriceMemberDTO> queryLogisticsAdjustPriceMember(LogisticsAdjustPriceQueryDTO queryDTO) {
        logger.info("物流调价客户列表查询入参：{}", JSON.toJSONString(queryDTO));
        checkIntoTheReference(queryDTO);
        LogisticsAdjustPriceDTO adjustPriceDTO = new LogisticsAdjustPriceDTO();
        BeanUtils.copyProperties(queryDTO, adjustPriceDTO);
        if (queryDTO.getAdjustType() == 0) {
            adjustPriceDTO.setEntrustedSideId(queryDTO.getMemberId());
        } else if (queryDTO.getAdjustType() == 1) {
            adjustPriceDTO.setEntrustingSideId(queryDTO.getMemberId());
        } else {
            adjustPriceDTO.setEntrustedSideId(queryDTO.getMemberId());
            adjustPriceDTO.setEntrustingSideId(queryDTO.getMemberId());
        }
        List<com.ecommerce.logistics.api.dto.LogisticsAdjustPriceMemberDTO> memberDTOs = logisticsAdjustPriceService.queryLogisticsAdjustPriceMemberList(adjustPriceDTO);
        logger.info("queryLogisticsAdjustPriceMemberList->{}", JSON.toJSONString(memberDTOs));
        List<LogisticsAdjustPriceMemberDTO> dtos = new ArrayList<LogisticsAdjustPriceMemberDTO>();
        if (CollectionUtils.isEmpty(memberDTOs)) {
            return dtos;
        }
        for (com.ecommerce.logistics.api.dto.LogisticsAdjustPriceMemberDTO memberDTO : memberDTOs) {
            LogisticsAdjustPriceMemberDTO adjustPriceMemberDTO = new LogisticsAdjustPriceMemberDTO();
            BeanUtils.copyProperties(memberDTO, adjustPriceMemberDTO);
            dtos.add(adjustPriceMemberDTO);
        }
        return dtos;
    }

    private void checkIntoTheReference(LogisticsAdjustPriceQueryDTO queryDTO) {
        if (CsStringUtils.isBlank(queryDTO.getAdjustName())) {
            throw new BizException(BasicCode.PARAM_NULL, "物流调价名称不能为空");
        }

        if (CsStringUtils.isBlank(queryDTO.getTransportType())) {
            throw new BizException(BasicCode.PARAM_NULL, "运输方式不能为空");
        }

        if (CsStringUtils.isBlank(queryDTO.getWarehouseId())) {
            throw new BizException(BasicCode.PARAM_NULL, "发货点不能为空");
        }

        if (CsStringUtils.isBlank(queryDTO.getTransportCategoryId())) {
            throw new BizException(BasicCode.PARAM_NULL, "运输品类不能为空");
        }

        if (queryDTO.getShipStartTime() == null) {
            throw new BizException(BasicCode.PARAM_NULL, "开始时间不能为空");
        }

        if (queryDTO.getShipEndTime() == null) {
            throw new BizException(BasicCode.PARAM_NULL, "结束时间不能为空");
        }

        if (queryDTO.getAdjustType() == null) {
            throw new BizException(BasicCode.PARAM_NULL, "调价类型");
        }
    }

    @Override
    public LogisticsAdjustPriceCalculateDTO calculateLogistticsAdjustPrice(
            LogisticsAdjustPriceCalculateDTO dto) {
        logger.info("物流调价计算入参：{}", JSON.toJSONString(dto));
        List<LogisticsAdjustPriceMemberCalculateDTO> adjustPriceMemberCalculateDTOs = dto.getAdjustPriceMemberCalculateDTOs();
        if (CollectionUtils.isEmpty(adjustPriceMemberCalculateDTOs)) {
            throw new BizException(BasicCode.PARAM_NULL, "调价路线用户集合不能为空");
        }
        for (LogisticsAdjustPriceMemberCalculateDTO adjustPriceMemberCalculateDTO : adjustPriceMemberCalculateDTOs) {
            BigDecimal actualQuantity = adjustPriceMemberCalculateDTO.getActualQuantity();
            BigDecimal originPrice = adjustPriceMemberCalculateDTO.getOriginPrice();
            BigDecimal adjustAddPrice = adjustPriceMemberCalculateDTO.getAdjustAddPrice();
            BigDecimal newestPrice ;
            if (CsStringUtils.equals(TransportToolTypeEnum.ROAD_TRANSPORT.getCode(), adjustPriceMemberCalculateDTO.getTransportType())) {
                newestPrice = originPrice.multiply(adjustAddPrice);
            } else {
                BigDecimal carriageUnitPrice = adjustPriceMemberCalculateDTO.getCarriageUnitPrice();
                BigDecimal auxiliaryPrice = adjustPriceMemberCalculateDTO.getAuxiliaryPrice();
                newestPrice = carriageUnitPrice.multiply(adjustAddPrice).add(auxiliaryPrice);
            }
            BigDecimal adjustAddAmount = actualQuantity.multiply(newestPrice.subtract(originPrice));
            adjustPriceMemberCalculateDTO.setNewestPrice(newestPrice);
            adjustPriceMemberCalculateDTO.setAdjustAddAmount(adjustAddAmount);
            List<LogisticsAdjustPriceItemCalculateDTO> itemCalculateDTOs = adjustPriceMemberCalculateDTO.getAdjustPriceItemCalculateDTOs();
            if (CollectionUtils.isEmpty(itemCalculateDTOs)) {
                throw new BizException(BasicCode.PARAM_NULL, "调价运单集合不能为空");
            }

            for (LogisticsAdjustPriceItemCalculateDTO itemCalculateDTO : itemCalculateDTOs) {
                BigDecimal actualQuantity2 = itemCalculateDTO.getActualQuantity();
                BigDecimal adjustPrice = newestPrice;
                BigDecimal adjustAmount = actualQuantity2.multiply(newestPrice);
                itemCalculateDTO.setAdjustPrice(adjustPrice);
                itemCalculateDTO.setAdjustAmount(adjustAmount);
            }
        }
        return dto;
    }

    @Override
    public void addLogistticsAdjustPrice(com.ecommerce.pay.api.v2.dto.logisticsAdjust.LogisticsAdjustPriceDTO dto) {
        logger.info("保存物流调价入参：{}", JSON.toJSONString(dto));
        LogisticsAdjustPrice logisticsAdjustPrice = new LogisticsAdjustPrice();
        if (CsStringUtils.isNotBlank(dto.getId())) {
            dto.setStatus(1);
            updateAdjustPriceById(dto);
        } else {
            BeanUtils.copyProperties(dto, logisticsAdjustPrice);
            String id = uuidGenerator.gain();
            String adjustNo = logisticsAdjustPriceBizUidGenerator.businessCode();
            logisticsAdjustPrice.setId(id);
            logisticsAdjustPrice.setAdjustNo(adjustNo);
            logisticsAdjustPrice.setCreateUser(dto.getMemberId());
            logisticsAdjustPrice.setAdjustType(0);
            logisticsAdjustPrice.setDelFlg(0);
            logisticsAdjustPrice.setStatus(1);
            //保存
            adjustPriceMapper.insert(logisticsAdjustPrice);
            List<LogisticsAdjustPriceMemberDTO> adjustPriceMemberDTOs = dto.getAdjustPriceMemberDTOs();
            for (LogisticsAdjustPriceMemberDTO adjustPriceMemberDTO : adjustPriceMemberDTOs) {
                LogisticsAdjustPriceMember adjustPriceMember = new LogisticsAdjustPriceMember();
                BeanUtils.copyProperties(adjustPriceMemberDTO, adjustPriceMember);
                String idm = uuidGenerator.gain();
                adjustPriceMember.setId(idm);
                adjustPriceMember.setAdjustPriceId(id);
                adjustPriceMember.setCreateUser(dto.getMemberId());
                //校验前端计算值
                BigDecimal actualQuantity = adjustPriceMemberDTO.getActualQuantity();
                BigDecimal originPrice = adjustPriceMemberDTO.getOriginPrice();
                BigDecimal adjustAddPrice = adjustPriceMemberDTO.getAdjustAddPrice();
                BigDecimal newestPrice ;

                if (CsStringUtils.equals(TransportToolTypeEnum.ROAD_TRANSPORT.getCode(), adjustPriceMemberDTO.getTransportType())) {
                    newestPrice = originPrice.add(adjustAddPrice);
                } else {
                    BigDecimal carriageUnitPrice = adjustPriceMemberDTO.getCarriageUnitPrice();
                    BigDecimal auxiliaryPrice = adjustPriceMemberDTO.getAuxiliaryPrice();
                    newestPrice = carriageUnitPrice.add(adjustAddPrice).add(auxiliaryPrice);
                }
                if (newestPrice.compareTo(adjustPriceMemberDTO.getNewestPrice()) != 0) {
                    logger.info("用户物流调价ID为{}的事后物流单价计算错误" + idm);
                    throw new BizException(LogisticsAdjistPriceCode.NEWEST_PRICE_ERROR, adjustPriceMemberDTO.getEntrustedSideName(), adjustPriceMemberDTO.getEntrustingSideName(), newestPrice);
                }
                BigDecimal adjustAddAmount = actualQuantity.multiply(newestPrice.subtract(originPrice));
                if (adjustAddAmount.compareTo(adjustPriceMemberDTO.getAdjustAddAmount()) != 0) {
                    logger.info("用户物流调价ID为{}的事后物流总价计算错误" + idm);
                    throw new BizException(LogisticsAdjistPriceCode.ADJUST_AMOUNT_ERROR, adjustPriceMemberDTO.getEntrustedSideName(), adjustPriceMemberDTO.getEntrustingSideName(), adjustAddAmount);
                }
                adjustPriceMember.setAdjustAmount(newestPrice.multiply(actualQuantity));
                adjustPriceMember.setDelFlg(0);
                adjustPriceMember.setStatus(1);
                //保存
                adjustPriceMemberMapper.insert(adjustPriceMember);
                List<LogisticsAdjustPriceItemDTO> adjustPriceItemDTOs = queryLogisticsAdjustPriceItemList(adjustPriceMemberDTO);
                for (LogisticsAdjustPriceItemDTO adjustPriceItemDTO : adjustPriceItemDTOs) {
                    String idi = uuidGenerator.gain();
                    LogisticsAdjustPriceItem adjustPriceItem = new LogisticsAdjustPriceItem();
                    //物流调价用户
                    BeanUtils.copyProperties(adjustPriceItemDTO, adjustPriceItem);
                    adjustPriceItem.setId(idi);
                    adjustPriceItem.setAdjustId(id);
                    adjustPriceItem.setAdjustMemberId(idm);
                    adjustPriceItem.setCreateUser(dto.getMemberId());
                    adjustPriceItem.setAdjustType(0);
                    adjustPriceItem.setAdjustAddPrice(adjustPriceMember.getAdjustAddPrice());
                    adjustPriceItem.setNewestPrice(adjustPriceItem.getOriginPrice());
                    adjustPriceItem.setNewestAmount(adjustPriceItem.getOriginAmount());
                    adjustPriceItem.setAdjustPrice(adjustPriceMember.getNewestPrice());
                    adjustPriceItem.setAdjustAmount(adjustPriceItem.getAdjustPrice().multiply(adjustPriceItem.getActualQuantity()));
                    //TODO
                    adjustPriceItem.setAdjustNum(1);
                    adjustPriceItem.setDelFlg(0);
                    adjustPriceItem.setStatus(1);
                    adjustPriceItemMapper.insert(adjustPriceItem);
                }
            }
        }
        //TODO 触发调价机制
        try {
            XxlJobContext context = XxlJobContext.getXxlJobContext();
            XxlJobContext.setXxlJobContext(context);
            adjustPriceSyncToERPJob.execute();
        } catch (Exception e) {
            logger.error("物流调价调用ERP接口失败，失败原因：{}", e.getMessage());
        } finally {
            XxlJobContext.setXxlJobContext(null);
        }

    }

    @Override
    public List<String> fuzzyQueryAdjustName(String adjustName, String memberId) {

        return adjustPriceMapper.fuzzyQueryAdjustName(adjustName, memberId);
    }

    @Override
    public com.ecommerce.pay.api.v2.dto.logisticsAdjust.LogisticsAdjustPriceDTO queryAdjustPriceMemberListByAdjustId(String adjustId) {

        com.ecommerce.pay.api.v2.dto.logisticsAdjust.LogisticsAdjustPriceDTO adjustPriceDTO = adjustPriceMapper.queryAdjustPriceById(adjustId);

        List<LogisticsAdjustPriceMemberDTO> adjustPriceMemberDTOs = new ArrayList<LogisticsAdjustPriceMemberDTO>();
        Condition condition = new Condition(LogisticsAdjustPriceMember.class);
        Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("delFlg", 0);
        criteria.andEqualTo("adjustPriceId", adjustId);
        List<LogisticsAdjustPriceMember> adjustPriceMembers = adjustPriceMemberMapper.selectByCondition(condition);
        for (LogisticsAdjustPriceMember logisticsAdjustPriceMember : adjustPriceMembers) {
            LogisticsAdjustPriceMemberDTO adjustPriceMemberDTO = new LogisticsAdjustPriceMemberDTO();
            BeanUtils.copyProperties(logisticsAdjustPriceMember, adjustPriceMemberDTO);
            adjustPriceMemberDTOs.add(adjustPriceMemberDTO);
        }
        adjustPriceDTO.setAdjustPriceMemberDTOs(adjustPriceMemberDTOs);
        return adjustPriceDTO;
    }

    @Override
    public List<LogisticsAdjustPriceItemDTO> queryAdjustPriceItemListByAdjustMeberId(String adjustMeberId) {

        List<LogisticsAdjustPriceItemDTO> adjustPriceItemDTOs = new ArrayList<LogisticsAdjustPriceItemDTO>();
        Condition condition = new Condition(LogisticsAdjustPriceItem.class);
        Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("delFlg", 0);
        criteria.andEqualTo("adjustMemberId", adjustMeberId);
        List<LogisticsAdjustPriceItem> adjustPriceItems = adjustPriceItemMapper.selectByCondition(condition);
        for (LogisticsAdjustPriceItem logisticsAdjustPriceItem : adjustPriceItems) {
            LogisticsAdjustPriceItemDTO adjustPriceItemDTO = new LogisticsAdjustPriceItemDTO();
            BeanUtils.copyProperties(logisticsAdjustPriceItem, adjustPriceItemDTO);
            adjustPriceItemDTOs.add(adjustPriceItemDTO);
        }
        return adjustPriceItemDTOs;
    }

    @Override
    public void updateAdjustItemStatusById(LogisticsAdjustPriceItem logisticsAdjustPriceItem) {
        logger.info("修改物流调价运单明细状态入参：{}", JSON.toJSONString(logisticsAdjustPriceItem));
        int update = adjustPriceItemMapper.updateByPrimaryKeySelective(logisticsAdjustPriceItem);
        if (update > 0) {
            logger.info("更新完成");
        }
    }

    @Override
    public List<com.ecommerce.pay.api.v2.dto.logisticsAdjust.LogisticsAdjustPriceDTO> queryLogisticsAdjustPrice(LogisticsAdjustPrice logisticsAdjustPrice) {
        logger.info("获取物流调价列表入参：{}", JSON.toJSONString(logisticsAdjustPrice));
        List<com.ecommerce.pay.api.v2.dto.logisticsAdjust.LogisticsAdjustPriceDTO> adjustPriceDTOS = new ArrayList<>();
        Condition condition = new Condition(LogisticsAdjustPrice.class);
        Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("delFlg", 0);
        criteria.andEqualTo("status", logisticsAdjustPrice.getStatus());
        if (CsStringUtils.isNotBlank(logisticsAdjustPrice.getAdjustNo())) {
            criteria.andEqualTo("adjustNo", logisticsAdjustPrice.getAdjustNo());
        }
        List<LogisticsAdjustPrice> adjustPriceList = adjustPriceMapper.selectByCondition(condition);
        if (CollectionUtils.isEmpty(adjustPriceList)) {
            logger.info("没有查找物流调价数据");
            return null;
        }
        for (LogisticsAdjustPrice adjustPrice : adjustPriceList) {
            com.ecommerce.pay.api.v2.dto.logisticsAdjust.LogisticsAdjustPriceDTO adjustPriceDTO = new com.ecommerce.pay.api.v2.dto.logisticsAdjust.LogisticsAdjustPriceDTO();
            BeanUtils.copyProperties(adjustPrice, adjustPriceDTO);
            adjustPriceDTOS.add(adjustPriceDTO);
        }
        return adjustPriceDTOS;
    }

    @Override
    public void processErpLogisticsAdjustResult(AdjustPricePayResultDTO resultDTO) {
        logger.info("ERP物流调价一部回调结果入参：{}", JSON.toJSONString(resultDTO));
        com.ecommerce.pay.api.v2.dto.logisticsAdjust.LogisticsAdjustPriceDTO adjustPriceDTO = adjustPriceMapper.getLogisticsAdjustPriceByAdjustNo(resultDTO.getAdjustNo());
        if (adjustPriceDTO == null) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "物流回调单号不存在");
        }
        if (adjustPriceDTO.getStatus() == 3) {
            logger.info("没有需要调整的物流调价单");
            return;
        }
        if (adjustPriceDTO.getStatus() != 2) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "物流回调单号状态错误");
        }

        if (resultDTO.getStatus() == 3) {
            logger.info("物流价格回调成功，修改物流调整单状态");
            processLogisticsAdjustPrice(3, adjustPriceDTO, resultDTO);
        } else {
            logger.info("物流价格回调失败，分类处理运单");
            processLogisticsAdjustPrice(4, adjustPriceDTO, resultDTO);
        }
    }

    @Override
    public List<LogisticsAdjustPriceItemDTO> queryLogisticsAdjustPriceItemList(LogisticsAdjustPriceMemberDTO queryDTO) {
        logger.info("获取物流调价运单明细入参：{}", JSON.toJSONString(queryDTO));
        com.ecommerce.logistics.api.dto.LogisticsAdjustPriceMemberDTO adjustPriceMemberDTO = new com.ecommerce.logistics.api.dto.LogisticsAdjustPriceMemberDTO();
        BeanUtils.copyProperties(queryDTO, adjustPriceMemberDTO);
        List<com.ecommerce.logistics.api.dto.LogisticsAdjustPriceItemDTO> adjustPriceItemDTOS = Collections.emptyList();
        logger.info("获取物流调价运单明细结果：{}", JSON.toJSONString(adjustPriceItemDTOS));
        List<LogisticsAdjustPriceItemDTO> adjustPriceItemDTOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(adjustPriceItemDTOS)) {
            return adjustPriceItemDTOList;
        }
        for (com.ecommerce.logistics.api.dto.LogisticsAdjustPriceItemDTO adjustPriceItemDTO : adjustPriceItemDTOS) {
            LogisticsAdjustPriceItemDTO logisticsAdjustPriceItemDTO = new LogisticsAdjustPriceItemDTO();
            BeanUtils.copyProperties(adjustPriceItemDTO, logisticsAdjustPriceItemDTO);
            adjustPriceItemDTOList.add(logisticsAdjustPriceItemDTO);
        }
        return adjustPriceItemDTOList;
    }

    @Override
    public Void deleteLogisticsAdjustPriceMemberByAdjustMemberId(LogisticsAdjustPriceMemberDTO queryDTO) {
        logger.info("删除物流调价用户入参，{}", JSON.toJSONString(queryDTO));
        int update = adjustPriceMemberMapper.deleteLogisticsAdjustPriceMemberByAdjustMemberId(queryDTO);
        if (update <= 0) {
            throw new BizException(BasicCode.DB_DELETE_FAILED, "物流调价用户");
        }

        return null;
    }

    @Override
    public Void deleteLogisticsAdjustPriceItemByAdjustItemId(LogisticsAdjustPriceItemDTO queryDTO) {
        logger.info("删除物流调价运单入参：{}", JSON.toJSONString(queryDTO));
        int update = adjustPriceItemMapper.deleteLogisticsAdjustPriceItemByAdjustItemId(queryDTO);
        if (update <= 0) {
            throw new BizException(BasicCode.DB_DELETE_FAILED, "物流调价运单明细");
        }
        return null;
    }

    @Override
    public List<LogisticsAdjustPriceItem> queryAdjustPriceItemListByAdjustId(String adjustId) {
        logger.info("查询物流调价运单明细入参：{}", adjustId);
        Condition condition = new Condition(LogisticsAdjustPriceItem.class);
        Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("delFlg", 0);
        criteria.andEqualTo("adjustId", adjustId);
        List<LogisticsAdjustPriceItem> adjustPriceItems = adjustPriceItemMapper.selectByCondition(condition);
        return adjustPriceItems;
    }

    @Override
    public void updateAdjustPriceById(com.ecommerce.pay.api.v2.dto.logisticsAdjust.LogisticsAdjustPriceDTO adjustPrice) {
        logger.info("更新物流调价状态入参：{}", JSON.toJSONString(adjustPrice));
        LogisticsAdjustPrice logisticsAdjustPrice = new LogisticsAdjustPrice();
        logisticsAdjustPrice.setId(adjustPrice.getId());
        if (adjustPrice.getStatus() != null) {
            logisticsAdjustPrice.setStatus(adjustPrice.getStatus());
            LogisticsAdjustPriceMember logisticsAdjustPriceMember = new LogisticsAdjustPriceMember();
            logisticsAdjustPriceMember.setStatus(adjustPrice.getStatus());
            logisticsAdjustPriceMember.setAdjustPriceId(adjustPrice.getId());
            adjustPriceMemberMapper.updateStatusByAdjustPriceId(logisticsAdjustPriceMember);
            LogisticsAdjustPriceItem logisticsAdjustPriceItem = new LogisticsAdjustPriceItem();
            logisticsAdjustPriceItem.setAdjustId(adjustPrice.getId());
            logisticsAdjustPriceItem.setStatus(adjustPrice.getStatus());
            adjustPriceItemMapper.updateStatusByAdjustPriceId(logisticsAdjustPriceItem);
            if (adjustPrice.getStatus() == 3) {
                com.ecommerce.logistics.api.dto.LogisticsAdjustPriceMemberDTO adjustPriceMemberDTO = new com.ecommerce.logistics.api.dto.LogisticsAdjustPriceMemberDTO();
                List<com.ecommerce.logistics.api.dto.LogisticsAdjustPriceItemDTO> adjustPriceItemDTOS = new ArrayList<>();
                Condition condition = new Condition(LogisticsAdjustPriceItem.class);
                Criteria criteria = condition.createCriteria();
                criteria.andEqualTo("delFlg", 0);
                criteria.andEqualTo("adjustId", adjustPrice.getId());
                criteria.andEqualTo("status", 3);
                List<LogisticsAdjustPriceItem> adjustPriceItems = adjustPriceItemMapper.selectByCondition(condition);
                for (LogisticsAdjustPriceItem adjustPriceItem : adjustPriceItems) {
                    com.ecommerce.logistics.api.dto.LogisticsAdjustPriceItemDTO adjustPriceItemDTO = new com.ecommerce.logistics.api.dto.LogisticsAdjustPriceItemDTO();
                    BeanUtils.copyProperties(logisticsAdjustPriceItem, adjustPriceItemDTO);
                    adjustPriceItemDTOS.add(adjustPriceItemDTO);
                }
                adjustPriceMemberDTO.setAdjustPriceItemDTOs(adjustPriceItemDTOS);
            }
        } else {
            logisticsAdjustPrice.setErrMsg(adjustPrice.getErrMsg());
        }
        adjustPriceMapper.updateByPrimaryKeySelective(logisticsAdjustPrice);
    }

    @Override
    public LogisticsAdjustPriceItemDTO getNewestLogisticsAdjustPriceItem(LogisticsAdjustPriceItemDTO queryDTO) {
        logger.info(" 获取最新物流运单调价明细入参：{}", JSON.toJSONString(queryDTO));

        return adjustPriceItemMapper.getNewestLogisticsAdjustPriceItem(queryDTO);
    }

    private void processLogisticsAdjustPrice(int status, com.ecommerce.pay.api.v2.dto.logisticsAdjust.LogisticsAdjustPriceDTO adjustPriceDTO, AdjustPricePayResultDTO resultDTO) {
        LogisticsAdjustPrice adjustPrice = new LogisticsAdjustPrice();
        LogisticsAdjustPriceMember adjustPriceMember = new LogisticsAdjustPriceMember();
        adjustPrice.setId(adjustPriceDTO.getId());
        adjustPrice.setStatus(status);
        adjustPrice.setErrMsg(resultDTO.getErrMsg());
        adjustPriceMapper.updateByPrimaryKeySelective(adjustPrice);
        adjustPriceMember.setAdjustPriceId(adjustPrice.getId());
        adjustPriceMember.setStatus(status);
        adjustPriceMemberMapper.updateStatusByAdjustPriceId(adjustPriceMember);
        if (status == 3) {
            LogisticsAdjustPriceItem adjustPriceItem = new LogisticsAdjustPriceItem();
            adjustPriceItem.setAdjustId(adjustPrice.getId());
            adjustPriceItem.setStatus(status);
            adjustPriceItemMapper.updateStatusByAdjustPriceId(adjustPriceItem);
        } else {
            List<AdjustPriceBillPayResultDTO> errList = resultDTO.getErrList();
            for (AdjustPriceBillPayResultDTO itemPayResultDTO : errList) {
                LogisticsAdjustPriceItem adjustPriceItem = new LogisticsAdjustPriceItem();
                adjustPriceItem.setAdjustId(adjustPrice.getId());
                adjustPriceItem.setStatus(status);
                adjustPriceItem.setWaybillNum(itemPayResultDTO.getWaybillNum());
                adjustPriceItem.setErrMsg(itemPayResultDTO.getMessage());
                adjustPriceItemMapper.updateStatusByAdjustPriceId(adjustPriceItem);
            }
        }
    }

}
