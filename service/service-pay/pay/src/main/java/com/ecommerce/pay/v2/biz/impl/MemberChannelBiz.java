package com.ecommerce.pay.v2.biz.impl;

import com.alibaba.fastjson.JSON;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.service.common.BaseBiz;
import com.ecommerce.common.service.common.bsid.CommonBusinessIdGenerator;
import com.ecommerce.common.service.common.uuid.UUIDGenerator;
import com.github.pagehelper.page.PageMethod;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.pay.api.v2.dto.GneteAggregationChannelCloseDTO;
import com.ecommerce.pay.api.v2.dto.GneteAggregationChannelOpenDTO;
import com.ecommerce.pay.api.v2.dto.MemberChannelDTO;
import com.ecommerce.pay.api.v2.enums.AggregationPayStatusEnum;
import com.ecommerce.pay.api.v2.enums.ChannelCodeEnum;
import com.ecommerce.pay.api.v2.enums.ChannelPaymentTypeEnum;
import com.ecommerce.pay.api.v2.enums.ClientType;
import com.ecommerce.pay.api.v2.enums.MemberChannelStatusEnum;
import com.ecommerce.pay.api.v2.enums.PasswordFreeStatusEnum;
import com.ecommerce.pay.api.v2.enums.PasswordStatusEnum;
import com.ecommerce.pay.enums.MemberCreditRecordEnum;
import com.ecommerce.pay.util.LocalDateUtils;
import com.ecommerce.pay.v2.biz.IChannelConfigBiz;
import com.ecommerce.pay.v2.biz.IMemberChannelBiz;
import com.ecommerce.pay.v2.biz.IMemberCreditRecordBiz;
import com.ecommerce.pay.v2.channel.adapter.gnete.GneteAdapter;
import com.ecommerce.pay.v2.dao.mapper.MemberChannelMapper;
import com.ecommerce.pay.v2.dao.vo.MemberChannel;
import com.ecommerce.pay.v2.util.OrderGenUtil;
import com.github.pagehelper.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @Date 05/12/2018 14:27
 */
@Slf4j
@Service
public class MemberChannelBiz extends BaseBiz<MemberChannel> implements IMemberChannelBiz{

    @Autowired
    private CommonBusinessIdGenerator takeCodeGenerator;
    @Autowired
    private MemberChannelMapper memberChannelMapper;
    @Autowired
    private IMemberCreditRecordBiz memberCreditRecordBiz;
    @Lazy
    @Autowired
    private GneteAdapter gneteAdapter;
    @Lazy
    @Autowired
    private IChannelConfigBiz channelConfigBiz;
    @Autowired
    private UUIDGenerator uuidGenerator;

    public static final String CREATE_TIME = "createTime";
    public static final String PAYEE_MEMBER_ID = "payeeMemberId";
    public static final String MEMBER_ID = "memberId";
    public static final String CHANNEL_ID = "channelId";
    public static final String CHANNEL_CODE = "channelCode";
    public static final String DEL_FLG = "delFlg";
    public static final String CHANNEL_STATUS = "channelStatus";
    public static final String MEMBER_CHANNEL_STATUS = "memberChannelStatus";

    @Override
    public MemberChannelDTO insertMemberChannel(MemberChannelDTO memberChannelDTO, String operator) {
        if (CsStringUtils.isBlank(memberChannelDTO.getChannelCode()) || CsStringUtils.isBlank(memberChannelDTO.getMemberId())) {
            throw new BizException(BasicCode.PARAM_NULL);
        }
        if (CsStringUtils.isEmpty(memberChannelDTO.getMemberChannelStatus())) {
            memberChannelDTO.setMemberChannelStatus(MemberChannelStatusEnum.OPEND.getCode());
        }
        memberChannelDTO.setPasswordStatus(PasswordStatusEnum.NO_SETTING.getCode());
        if (memberChannelDTO.getArrearsAmount() == null) {
            memberChannelDTO.setArrearsAmount(new BigDecimal("0.00"));
        }
        if (memberChannelDTO.getBalanceAmount() == null) {
            memberChannelDTO.setBalanceAmount(new BigDecimal("0.00"));
        }
        memberChannelDTO.setMemberChannelCode(takeCodeGenerator.incrementChannelCode());
        MemberChannel memberChannel = save(convert(memberChannelDTO),operator);
        return convertToDTO(memberChannel);
    }

    @Override
    public MemberChannelDTO updateMemberChannel(MemberChannelDTO memberChannelDTO, String operator) {
        if (memberChannelDTO.getArrearsAmount() == null) {
            memberChannelDTO.setArrearsAmount(new BigDecimal("0.00"));
        }
        if (memberChannelDTO.getBalanceAmount() == null) {
            memberChannelDTO.setBalanceAmount(new BigDecimal("0.00"));
        }
        setOperInfo(memberChannelDTO,operator,false);
        return convertToDTO(updateSelective(convert(memberChannelDTO)));
    }

    @Override
    public List<MemberChannelDTO> findByMemberIdAndChannelCode(String memberId, String channelCode) {
        if (CsStringUtils.isEmpty(memberId)) {
            return new ArrayList<>();
        }
        Condition condition = new Condition(MemberChannel.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(MEMBER_ID,memberId);
        criteria.andEqualTo(CHANNEL_CODE,channelCode);
        criteria.andEqualTo(DEL_FLG,0);
        criteria.andEqualTo(CHANNEL_STATUS, 1);
        criteria.andEqualTo(MEMBER_CHANNEL_STATUS, MemberChannelStatusEnum.OPEND.getCode());
        List<MemberChannel> voList = findByCondition(condition);
        List<MemberChannelDTO> dtoList = new ArrayList<>();
        for(MemberChannel memberChannel:voList){
            dtoList.add(convertToDTO(memberChannel));
        }
        return dtoList;
    }

    @Override
    public List<MemberChannelDTO> findByMemberIdAndChannelCode(String memberId, String channelCode, List<String> memberChannelStatus) {
        if (CsStringUtils.isEmpty(memberId)) {
            return new ArrayList<>();
        }
        Condition condition = new Condition(MemberChannel.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(MEMBER_ID,memberId);
        criteria.andEqualTo(CHANNEL_CODE,channelCode);
        criteria.andEqualTo(DEL_FLG,0);
        criteria.andEqualTo(CHANNEL_STATUS, 1);
        if(!CollectionUtils.isEmpty(memberChannelStatus)) {
            criteria.andIn(MEMBER_CHANNEL_STATUS, memberChannelStatus);
        }
        List<MemberChannel> voList = findByCondition(condition);
        List<MemberChannelDTO> dtoList = new ArrayList<>();
        for(MemberChannel memberChannel:voList){
            dtoList.add(convertToDTO(memberChannel));
        }
        return dtoList;
    }

    @Override
    public List<MemberChannelDTO> findByMemberIdAndChannelType(String memberId, String channelType) {
        if (CsStringUtils.isEmpty(memberId)) {
            return new ArrayList<>();
        }
        Condition condition = new Condition(MemberChannel.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(MEMBER_ID,memberId);
        criteria.andEqualTo("channelType",channelType);
        criteria.andEqualTo(DEL_FLG,0);
        criteria.andEqualTo(CHANNEL_STATUS, 1);
        List<MemberChannel> voList = findByCondition(condition);
        List<MemberChannelDTO> dtoList = new ArrayList<>();
        for(MemberChannel memberChannel:voList){
            dtoList.add(convertToDTO(memberChannel));
        }
        return dtoList;
    }

    @Override
    public List<MemberChannelDTO> findByMemberIdAndChannelCode2(String memberId, String channelCode) {
        if (CsStringUtils.isEmpty(memberId)) {
            return new ArrayList<>();
        }
        Condition condition = new Condition(MemberChannel.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(MEMBER_ID,memberId);
        criteria.andEqualTo(CHANNEL_CODE,channelCode);
        criteria.andEqualTo(DEL_FLG,0);
        criteria.andEqualTo(CHANNEL_STATUS, 1);
        List<MemberChannel> voList = findByCondition(condition);
        List<MemberChannelDTO> dtoList = new ArrayList<>();
        for(MemberChannel memberChannel:voList){
            dtoList.add(convertToDTO(memberChannel));
        }
        return dtoList;
    }

    @Override
    public MemberChannelDTO findByMemberIdAndChannelCode(String memberId, String channelCode, ChannelPaymentTypeEnum channelPaymentType) {
        log.info("findByMemberIdAndChannelCode memberId:{},channelCode:{},channelPaymentType:{}",memberId,channelCode,channelPaymentType);
        if (CsStringUtils.isBlank(memberId) || CsStringUtils.isBlank(channelCode) || channelPaymentType == null) {
            return null;
        }
        Condition condition = new Condition(MemberChannel.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(MEMBER_ID,memberId);
        criteria.andEqualTo(CHANNEL_CODE,channelCode);
        criteria.andEqualTo(DEL_FLG,0);
        criteria.andEqualTo(CHANNEL_STATUS, 1);
        criteria.andEqualTo(MEMBER_CHANNEL_STATUS, MemberChannelStatusEnum.OPEND.getCode());
        List<MemberChannel> voList = findByCondition(condition);
        if (CollectionUtils.isEmpty(voList)) {
            return null;
        }
        List<MemberChannelDTO> dtoList = new ArrayList<>();
        for(MemberChannel memberChannel: voList){
            if(!memberChannel.getPayerPayeeSep()){
                dtoList.add(convertToDTO(memberChannel));
                break;
            }else{
                //如果是买家并且是付款账户(即允许付款)
                if(channelPaymentType.getCode().equals(ChannelPaymentTypeEnum.PAYER.getCode()) && memberChannel.getAllowPayment()){
                    dtoList.add(convertToDTO(memberChannel));
                    break;
                }
                //如果是卖家并且是收款账户（即允许收款）
                if(channelPaymentType.getCode().equals(ChannelPaymentTypeEnum.PAYEE.getCode()) && memberChannel.getAllowReceive()){
                    dtoList.add(convertToDTO(memberChannel));
                    break;
                }
            }
        }
        if(CollectionUtils.isEmpty(dtoList)){
            return null;
        }else{
            return dtoList.get(0);
        }
    }

    @Override
    public MemberChannelDTO findByMemberChannelId(String memberChannelId) {
        MemberChannel memberChannel = get(memberChannelId);
        if (memberChannel != null && !memberChannel.getDelFlg()) {
            return convertToDTO(memberChannel);
        }
        return null;
    }

    @Override
    public MemberChannelDTO  findByMemberIdAndType(String memberId, String channelCode){
        Condition condition = new Condition(MemberChannel.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(MEMBER_ID,memberId);
        criteria.andEqualTo(CHANNEL_CODE,channelCode);
        criteria.andEqualTo(DEL_FLG,0);
        criteria.andEqualTo(CHANNEL_STATUS, 1);
        List<MemberChannel> voList = findByCondition(condition);
        if(!CollectionUtils.isEmpty(voList)){
            for (MemberChannel memberChannel : voList) {
                if (MemberChannelStatusEnum.OPEND.getCode().equals(memberChannel.getMemberChannelStatus())) {
                    return convertToDTO(memberChannel);
                }
            }
        }
        return null;
    }

    @Override
    public List<MemberChannelDTO> getMemberAvailChannels(String memberId, ClientType client) {
        // fixme 构建一个线下收款渠道
        Condition condition = new Condition(MemberChannel.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(MEMBER_ID,memberId);
        criteria.andEqualTo(DEL_FLG,0);
        criteria.andEqualTo(CHANNEL_STATUS, 1);
        criteria.andEqualTo(MEMBER_CHANNEL_STATUS, MemberChannelStatusEnum.OPEND.getCode());
        if(client != null){
            if(client.getCode().equals(ClientType.APP.getCode())){
                criteria.andEqualTo("isSupportApp",1);
            }
            if(client.getCode().equals(ClientType.MINI.getCode())){
                criteria.andEqualTo("isSupportMini",1);
            }
            if(client.getCode().equals(ClientType.PC.getCode())){
                criteria.andEqualTo("isSupportWeb",1);
            }
        }
        List<MemberChannel> voList = findByCondition(condition);
        if(CollectionUtils.isEmpty(voList)){
            return new ArrayList<>();
        }
        List<MemberChannelDTO> dtoList = new ArrayList<>();
        for(MemberChannel memberChannel:voList){
            dtoList.add(convertToDTO(memberChannel));
        }
        return dtoList;
    }

    @Override
    public List<MemberChannelDTO> getMemberChannelByMemberId(String memberId) {
        Condition condition = new Condition(MemberChannel.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(MEMBER_ID,memberId);
        criteria.andEqualTo(DEL_FLG,0);
        criteria.andEqualTo(CHANNEL_STATUS, 1);
        criteria.andEqualTo(MEMBER_CHANNEL_STATUS, MemberChannelStatusEnum.OPEND.getCode());
        List<MemberChannel> voList = findByCondition(condition);
        List<MemberChannelDTO> dtoList = new ArrayList<>();

        if (CollectionUtils.isEmpty(voList)) {
            return new ArrayList<>();
        }

        for (MemberChannel memberChannel:voList) {
            if (memberChannel == null){
                    continue;
                }
                MemberChannelDTO memberChannelDTO = convertToDTO(memberChannel);

            // 授信支付需要计算可用余额 by luyao
            if(ChannelCodeEnum.CREDIT.getCode().equals(memberChannelDTO.getChannelCode())) {
                BigDecimal balanceAmount = Optional.ofNullable(memberChannelDTO.getBalanceAmount()).orElse(BigDecimal.ZERO);
                BigDecimal arrearsAmount = Optional.ofNullable(memberChannelDTO.getArrearsAmount()).orElse(BigDecimal.ZERO);
                memberChannelDTO.setAvailableAmount(balanceAmount.subtract(arrearsAmount));

                // 欠款为负数时，将欠款重置为0
                if(arrearsAmount.compareTo(BigDecimal.ZERO) < 0) {
                    memberChannelDTO.setArrearsAmount(BigDecimal.ZERO);
                }
            }

            dtoList.add(memberChannelDTO);
        }
        return dtoList;
    }

    @Override
    public String getMchkeyByMchNo(String mchNo) {
        Condition condition = new Condition(MemberChannel.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("mchNo",mchNo);
        criteria.andEqualTo(DEL_FLG,0);
        criteria.andEqualTo(CHANNEL_STATUS, 1);
        criteria.andEqualTo(MEMBER_CHANNEL_STATUS, MemberChannelStatusEnum.OPEND.getCode());
        List<MemberChannel> voList = findByCondition(condition);
        List<MemberChannelDTO> dtoList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(voList)){
            for(MemberChannel memberChannel:voList){
                dtoList.add(convertToDTO(memberChannel));
            }
            return dtoList.get(0).getMchKey();
        }else{
            return null;
        }
    }

    @Override
    public Page<MemberChannelDTO> pageMemberChannelDTO(MemberChannelDTO memberChannelDTO) {
        // modify by zhougang
        memberChannelDTO.setMemberChannelStatus(MemberChannelStatusEnum.OPEND.getCode());

        // 排序判断
        if(memberChannelDTO.getSortColumn() != null && ("asc".equals(memberChannelDTO.getSortType()) || "desc".equals(memberChannelDTO.getSortType())))
        {
            switch(memberChannelDTO.getSortColumn())
            {
                // balanceAmount：授信额度、arrearsTotal：应还总额、availableAmount：可用余额、repaymentTotal：已还款总额
                case "balanceAmount":
                    memberChannelDTO.setSortColumn("balance_amount");
                    break;
                case "arrearsTotal":
                    memberChannelDTO.setSortColumn("arrears_total");
                    break;
                case "availableAmount":
                    memberChannelDTO.setSortColumn("available_amount");
                    break;
                case "repaymentTotal":
                    memberChannelDTO.setSortColumn("repayment_total");
                    break;
                default:
                    memberChannelDTO.setSortColumn(null);
                    memberChannelDTO.setSortType(null);
                    break;
            }
        }
        else
        {
            memberChannelDTO.setSortColumn(null);
            memberChannelDTO.setSortType(null);
        }

        log.info("优化后的查询条件: {}", JSON.toJSONString(memberChannelDTO));


        Page<MemberChannel> page = PageMethod.startPage(memberChannelDTO.getPageNum(), memberChannelDTO.getPageSize()).doSelectPage(() -> memberChannelMapper.getMemberChannelList(memberChannelDTO));



        /*
        Condition condition = findCondition(memberChannelDTO);

        PageInfo<MemberChannel> pageInfo = new PageInfo<>();
        pageInfo.setPageNum(memberChannelDTO.getPageNum());
        pageInfo.setPageSize(memberChannelDTO.getPageSize());

        Page<MemberChannel> page = super.page(condition,pageInfo);
        */

        Page<MemberChannelDTO> collect = page.stream().map(this::convertToDTO).collect(Collectors.toCollection(Page::new));
        BeanUtils.copyProperties(page, collect);

        log.info(" pageMemberChannel >>> {}", JSON.toJSONString(collect.getResult()));

        return collect;
    }

    private Condition findCondition(MemberChannelDTO memberChannelDTO) {
        Condition condition = new Condition(MemberChannel.class);
        Example.Criteria criteria1 = condition.createCriteria();
        if (!CsStringUtils.isBlank(memberChannelDTO.getMemberCode())) {
            criteria1.orEqualTo("memberCode",memberChannelDTO.getMemberCode());
        }
        if (!CsStringUtils.isBlank(memberChannelDTO.getMemberName())) {
            criteria1.orLike("memberName",'%'+memberChannelDTO.getMemberName()+'%');
        }
        Example.Criteria criteria = condition.createCriteria();
        if (!CsStringUtils.isBlank(memberChannelDTO.getChannelName())) {
            criteria.andLike("channelName",'%'+memberChannelDTO.getChannelName()+'%');
        }
        //平台支付渠道根据时间过滤问题修复  2020.6.18
        Date startTime = LocalDateUtils.getBeginDate(LocalDateUtils.str2Date(memberChannelDTO.getStartTime(),"yyyy-MM-dd"));
        Date endTime = LocalDateUtils.getEndDate(LocalDateUtils.str2Date(memberChannelDTO.getEndTime(),"yyyy-MM-dd"));
        if( startTime != null){
            criteria.andGreaterThanOrEqualTo(CREATE_TIME,startTime);
        }
        if(endTime != null ){
            criteria.andLessThanOrEqualTo(CREATE_TIME,endTime);
        }
        if (!CsStringUtils.isBlank(memberChannelDTO.getPayeeMemberId())) {
            criteria.andEqualTo(PAYEE_MEMBER_ID,memberChannelDTO.getPayeeMemberId());
        }
        if (!CsStringUtils.isBlank(memberChannelDTO.getMemberId())) {
            criteria.andEqualTo(MEMBER_ID,memberChannelDTO.getMemberId());
        }
        if (!CsStringUtils.isBlank(memberChannelDTO.getChannelId())) {
            criteria.andEqualTo(CHANNEL_ID,memberChannelDTO.getChannelId());
        }
        if (!CsStringUtils.isBlank(memberChannelDTO.getChannelCode())) {
            criteria.andEqualTo(CHANNEL_CODE,memberChannelDTO.getChannelCode());
        }
        if (!CsStringUtils.isBlank(memberChannelDTO.getExtSupAcctId())) {
            criteria.andEqualTo("extSupAcctId",memberChannelDTO.getExtSupAcctId());
        }
        if (!CsStringUtils.isBlank(memberChannelDTO.getExtSysName())) {
            criteria.andLike("extSysName", "%" + memberChannelDTO.getExtSysName() + "%");
        }
        if (memberChannelDTO.getAllowReceive() != null) {
            criteria.andEqualTo("allowReceive", memberChannelDTO.getAllowReceive());
        }
        if (memberChannelDTO.getAllowPayment() != null) {
            criteria.andEqualTo("allowPayment", memberChannelDTO.getAllowPayment());
        }

        // 授信开始时间
        if(memberChannelDTO.getEffectiveBeginDate() != null)
        {
            criteria.andGreaterThanOrEqualTo("effectiveBeginDate", memberChannelDTO.getEffectiveBeginDate());
        }

        // 授信结束时间
        if(memberChannelDTO.getEffectiveEndDate() != null)
        {
            criteria.andLessThanOrEqualTo("effectiveEndDate", new Date(memberChannelDTO.getEffectiveEndDate().getTime() + 86399000));
        }

        // 最小授信额度
        if(memberChannelDTO.getMinBalanceAmount() != null)
        {
            criteria.andGreaterThanOrEqualTo("balanceAmount", memberChannelDTO.getMinBalanceAmount());
        }

        // 最大授信额度
        if(memberChannelDTO.getMaxBalanceAmount() != null)
        {
            criteria.andLessThanOrEqualTo("balanceAmount", memberChannelDTO.getMaxBalanceAmount());
        }

        // 最小欠款金额
        if(memberChannelDTO.getMinArrearsAmount() != null)
        {
            criteria.andGreaterThanOrEqualTo("arrearsAmount", memberChannelDTO.getMinArrearsAmount());
        }

        // 最大欠款金额
        if(memberChannelDTO.getMaxArrearsAmount() != null)
        {
            criteria.andLessThanOrEqualTo("arrearsAmount", memberChannelDTO.getMaxArrearsAmount());
        }

        // 最小欠款总额
        if(memberChannelDTO.getMinArrearsTotal() != null)
        {
            criteria.andGreaterThanOrEqualTo("arrearsTotal", memberChannelDTO.getMinArrearsTotal());
        }

        // 最大欠款总额
        if(memberChannelDTO.getMaxArrearsTotal() != null)
        {
            criteria.andLessThanOrEqualTo("arrearsTotal", memberChannelDTO.getMaxArrearsTotal());
        }

        // 最小已还款总额
        if(memberChannelDTO.getMinRepaymentTotal() != null)
        {
            criteria.andGreaterThanOrEqualTo("repaymentTotal", memberChannelDTO.getMinRepaymentTotal());
        }

        // 最大已还款总额
        if(memberChannelDTO.getMaxRepaymentTotal() != null)
        {
            criteria.andLessThanOrEqualTo("repaymentTotal", memberChannelDTO.getMaxRepaymentTotal());
        }

        // TODO: 最小可用授信额度
        if(memberChannelDTO.getMinAvailableAmount() != null)
        {}

        // TODO: 最大可用授信额度
        if(memberChannelDTO.getMaxAvailableAmount() != null)
        {}


        criteria.andEqualTo(DEL_FLG,0);
        criteria.andEqualTo(MEMBER_CHANNEL_STATUS,MemberChannelStatusEnum.OPEND.getCode());
        condition.orderBy(CREATE_TIME).desc();
        condition.and(criteria);
        return condition;
    }

    @Override
    public MemberChannelDTO findMemberChannelByPayee(String memberId, String payeeMemberId, ChannelCodeEnum channelCodeEnum) {
        log.info("findMemberChannelByPayee memberId:{},payeeMemberId:{},channelCodeEnum:{}",memberId,payeeMemberId,channelCodeEnum);
        if (CsStringUtils.isBlank(memberId) || CsStringUtils.isBlank(payeeMemberId) || channelCodeEnum == null) {
            return null;
        }
        Condition condition = new Condition(MemberChannel.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(MEMBER_ID,memberId);
        criteria.andEqualTo(PAYEE_MEMBER_ID,payeeMemberId);
        criteria.andEqualTo(CHANNEL_CODE, channelCodeEnum.getCode());
        criteria.andEqualTo(DEL_FLG,0);
        criteria.andEqualTo(CHANNEL_STATUS, 1);
        criteria.andEqualTo(MEMBER_CHANNEL_STATUS, MemberChannelStatusEnum.OPEND.getCode());
        List<MemberChannel> voList = findByCondition(condition);
        List<MemberChannelDTO> dtoList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(voList)){
            for(MemberChannel memberChannel:voList){
                dtoList.add(convertToDTO(memberChannel));
            }
            return dtoList.get(0);
        }else{
            return null;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAddCreditMemberChannel(List<MemberChannelDTO> memberChannelDTOS,String operatorId) {
        List<MemberChannel> voList = new ArrayList<>();
        List<String> memberIdList = new ArrayList<>();
        for(MemberChannelDTO memberChannelDTO : memberChannelDTOS){
            memberIdList.add(memberChannelDTO.getMemberId());
        }
        List<MemberChannelDTO> dtoList = findMemberChannelDTOList(memberChannelDTOS.get(0).getPayeeMemberId(),memberIdList);
        if(!CollectionUtils.isEmpty(dtoList)){
            throw new BizException(BasicCode.DATA_EXIST,"参数中的会员ID已经开通了该渠道");
        }

        for(MemberChannelDTO memberChannelDTO:memberChannelDTOS){
            memberChannelDTO.setPasswordStatus(PasswordStatusEnum.NO_SETTING.getCode());
            memberChannelDTO.setArrearsAmount(new BigDecimal("0.00"));
            voList.add(convert(memberChannelDTO));
        }
        batchInsert(voList,operatorId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAddOrUpdateCreditMemberChannel(List<MemberChannelDTO> memberChannelDTOS)
    {
        for(MemberChannelDTO memberChannelDTO : memberChannelDTOS)
        {
            MemberChannelDTO dto = findMemberChannelByPayee(memberChannelDTO.getMemberId(), memberChannelDTO.getPayeeMemberId(), ChannelCodeEnum.CREDIT);
            if(dto == null)
            {
                memberChannelDTO.setMemberChannelId(uuidGenerator.gain());
                memberChannelDTO.setPasswordStatus(PasswordStatusEnum.NO_SETTING.getCode());
                memberChannelDTO.setArrearsAmount(BigDecimal.ZERO);
                memberChannelDTO.setArrearsTotal(BigDecimal.ZERO);
                memberChannelDTO.setRepaymentTotal(BigDecimal.ZERO);
                memberChannelDTO.setDelFlg(false);

                MemberChannel memberChannel = convert(memberChannelDTO);
                insert(memberChannel);
            }
            else
            {
                MemberChannelDTO upDto = new MemberChannelDTO();
                upDto.setMemberChannelId(dto.getMemberChannelId());
                upDto.setBalanceAmount(memberChannelDTO.getBalanceAmount());
                upDto.setEffectiveBeginDate(memberChannelDTO.getEffectiveBeginDate());
                upDto.setEffectiveEndDate(memberChannelDTO.getEffectiveEndDate());
                upDto.setUpdateUser(memberChannelDTO.getCreateUser());
                upDto.setUpdateTime(new Date());

                log.info("{}更新额度为：{}", dto.getMemberName(), upDto.getBalanceAmount());

                MemberChannel upChannel = convert(upDto);
                log.info("{}更新数据：{}", dto.getMemberName(), JSON.toJSONString(upChannel));

                memberChannelMapper.updateByPrimaryKeySelective(upChannel);
            }
            log.info("batchAddOrUpdateCreditMemberChannel");
            memberCreditRecordBiz.insertMemberCreditRecordByMemberChannel(memberChannelDTO, memberChannelDTO.getBalanceAmount(), MemberCreditRecordEnum.CREDIT, OrderGenUtil.genId(), "批量新增/更新授信历史", "", memberChannelDTO.getCreateUser());
        }
    }

    @Override
    public List<MemberChannelDTO> findMemberChannelDTOList(String payeeMemberId , List<String> payerMemberIds) {
        Condition condition = new Condition(MemberChannel.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(PAYEE_MEMBER_ID,payeeMemberId);
        criteria.andEqualTo(CHANNEL_CODE, ChannelCodeEnum.CREDIT.getCode());
        criteria.andEqualTo(DEL_FLG,0);
        criteria.andEqualTo(CHANNEL_STATUS, 1);
        criteria.andEqualTo(MEMBER_CHANNEL_STATUS, MemberChannelStatusEnum.OPEND.getCode());
        if(!CollectionUtils.isEmpty(payerMemberIds)){
            criteria.andIn(MEMBER_ID,payerMemberIds);
        }
        List<MemberChannel> voList = findByCondition(condition);
        List<MemberChannelDTO> dtoList = new ArrayList<>();
        if(CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        }else {
            for(MemberChannel memberChannel:voList){
                dtoList.add(convertToDTO(memberChannel));
            }
            return dtoList;
        }
    }

    @Override
    public List<MemberChannelDTO> findMemberChannelDTO(MemberChannelDTO memberChannelDTO) {
        Condition condition = findCondition(memberChannelDTO);
        List<MemberChannel> memberChannels = memberChannelMapper.selectByCondition(condition);
        if (CollectionUtils.isEmpty(memberChannels)) {
            return new ArrayList<>();
        }
        return memberChannels.stream().map(this::convertToDTO).toList();
    }

    @Override
    public void updateByMemberId(MemberChannel memberChannel) {
        Assert.notNull(memberChannel.getMemberId(), MEMBER_ID);
        Condition condition = new Condition(MemberChannel.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(MEMBER_ID, memberChannel.getMemberId());
        criteria.andEqualTo(DEL_FLG, false);
        memberChannelMapper.updateByConditionSelective(memberChannel, condition);
    }

    @Override
    public void updateByPayeeMemberId(MemberChannel memberChannel) {
        Assert.notNull(memberChannel.getPayeeMemberId(), PAYEE_MEMBER_ID);
        Condition condition = new Condition(MemberChannel.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(PAYEE_MEMBER_ID, memberChannel.getPayeeMemberId());
        criteria.andEqualTo(DEL_FLG, false);
        memberChannelMapper.updateByConditionSelective(memberChannel, condition);
    }

    @Override
    public void freePasswordSetting(String memberChannelId, Boolean freePassword, String operator) {
        if (CsStringUtils.isEmpty(memberChannelId)) {
            throw new BizException(BasicCode.PARAM_NULL, "memberChannelId");
        }
        if (freePassword == null) {
            throw new BizException(BasicCode.PARAM_NULL, "freePassword");
        }
        MemberChannelDTO memberChannelDTO = findByMemberChannelId(memberChannelId);
        if (memberChannelDTO == null || Boolean.TRUE.equals(memberChannelDTO.getDelFlg())) {
            throw new BizException(BasicCode.DB_DATA_NOT_FOUND, "会员渠道");
        }
        String oldStatus = memberChannelDTO.getExt1();

        String status = freePassword ?
                PasswordFreeStatusEnum.FREE_PWD.getCode() :
                PasswordFreeStatusEnum.REQUIRE_PWD.getCode();

        if (!status.equals(oldStatus)) {
            memberChannelDTO.setExt1(status);
            updateMemberChannel(memberChannelDTO, operator);
        }
    }

    @Override
    public boolean checkMemberChannelPasswordFree(MemberChannelDTO memberChannelDTO) {
        String freePwd = memberChannelDTO.getExt1();
        if (CsStringUtils.isEmpty(freePwd)) {
            return false;
        }
        return PasswordFreeStatusEnum.FREE_PWD.getCode().equals(freePwd);
    }

    @Override
    public void aggregationPaySetting(String memberChannelId, Boolean support, String operator) {
        if (CsStringUtils.isEmpty(memberChannelId)) {
            throw new BizException(BasicCode.PARAM_NULL, "memberChannelId");
        }
        if (support == null) {
            throw new BizException(BasicCode.PARAM_NULL, "support");
        }
        MemberChannelDTO memberChannelDTO = findByMemberChannelId(memberChannelId);
        if (memberChannelDTO == null) {
            throw new BizException(BasicCode.DB_DATA_NOT_FOUND, "渠道");
        }
        String memberId = memberChannelDTO.getMemberId();

        if (support && !checkMemberChannelAllowOpenAggregation(memberId)) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "关闭微信或支付宝后才能使用聚合支付功能!");
        }

        String oldStatus = memberChannelDTO.getExt2();

        String status = support ?
                AggregationPayStatusEnum.SUPPORT_AGGREGATION.getCode() :
                AggregationPayStatusEnum.NO_SUPPORT_AGGREGATION.getCode();

        if (!status.equals(oldStatus)) {
            memberChannelDTO.setExt2(status);
            updateMemberChannel(memberChannelDTO, operator);
        }
    }

    @Override
    public boolean checkMemberChannelAggregation(MemberChannelDTO memberChannelDTO) {
        String aggs = memberChannelDTO.getExt2();
        if (CsStringUtils.isEmpty(aggs)) {
            return false;
        }
        return AggregationPayStatusEnum.SUPPORT_AGGREGATION.getCode().equals(aggs);
    }

    @Override
    public boolean checkMemberChannelAggregation(String memberId) {
        Condition condition = new Condition(MemberChannel.class);
        condition.createCriteria()
                .andEqualTo(MEMBER_ID, Objects.requireNonNull(memberId, "memberId is null"))
                .andEqualTo(DEL_FLG,0)
                .andEqualTo(CHANNEL_STATUS, 1)
                .andEqualTo(MEMBER_CHANNEL_STATUS, MemberChannelStatusEnum.OPEND.getCode())
                .andEqualTo(CHANNEL_CODE, ChannelCodeEnum.PINGANJZ.getCode());
        List<MemberChannel> list = memberChannelMapper.selectByCondition(condition);
        if (CollectionUtils.isEmpty(list)) {
            return false;
        }
        for (MemberChannel memberChannel : list) {
            String aggs = memberChannel.getExt2();
            if (!CsStringUtils.isEmpty(aggs) && AggregationPayStatusEnum.SUPPORT_AGGREGATION.getCode().equals(aggs)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean checkMemberChannelAllowOpenAggregation(String memberId) {
        boolean wechat = CollectionUtils.isEmpty(findByMemberIdAndChannelCode(memberId, ChannelCodeEnum.WEIXIN.getCode()));
        boolean alipay = CollectionUtils.isEmpty(findByMemberIdAndChannelCode(memberId, ChannelCodeEnum.ALIPAY.getCode()));
        return wechat && alipay;
    }

    @Override
    public MemberChannel findByRegisterNo(String registerNo) {
        Condition condition = new Condition(MemberChannel.class);
        condition.createCriteria()
                .andEqualTo(DEL_FLG,0)
                .andEqualTo(CHANNEL_CODE, ChannelCodeEnum.GNETEPAY.getCode())
                .andEqualTo("gneteRegisterNo", registerNo);
        List<MemberChannel> list = memberChannelMapper.selectByCondition(condition);
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }

    @Override
    public void updateMemberChannelStatus(String memberChannelId, MemberChannelStatusEnum statusEnum,String operatorId) {
        if (CsStringUtils.isBlank(memberChannelId)) {
            return;
        }
        MemberChannel memberChannel = new MemberChannel();
        memberChannel.setMemberChannelId(memberChannelId);
        memberChannel.setMemberChannelStatus(statusEnum.getCode());
        memberChannel.setUpdateTime(new Date());
        memberChannel.setUpdateUser(operatorId);
        memberChannelMapper.updateByPrimaryKeySelective(memberChannel);
    }

    @Override
    public boolean openGneteAggregationChannel(GneteAggregationChannelOpenDTO dto) {
        return false;
    }

    @Override
    public boolean closeGneteAggregationChannel(GneteAggregationChannelCloseDTO dto) {
        return false;
    }

    private MemberChannel convert(MemberChannelDTO memberChannelDTO) {
        if(memberChannelDTO == null){
            return null;
        }
        MemberChannel memberChannel = new MemberChannel();
        BeanUtils.copyProperties(memberChannelDTO,memberChannel);
        return memberChannel;
    }

    private MemberChannelDTO convertToDTO(MemberChannel memberChannel) {
        MemberChannelDTO memberChannelDTO = new MemberChannelDTO();
        BeanUtils.copyProperties(memberChannel,memberChannelDTO);
        return memberChannelDTO;
    }

}
