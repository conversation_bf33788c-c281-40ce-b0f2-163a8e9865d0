package com.ecommerce.pay.v2.biz.impl;

import com.alibaba.fastjson.JSON;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.service.common.bsid.CommonBusinessIdGenerator;
import com.ecommerce.common.service.common.uuid.UUIDGenerator;
import com.ecommerce.common.utils.BeanConvertUtils;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.common.utils.DateUtil;
import com.ecommerce.pay.api.exception.PayCode;
import com.ecommerce.pay.api.v2.dto.ChannelConfigDTO;
import com.ecommerce.pay.api.v2.dto.MemberChannelDTO;
import com.ecommerce.pay.api.v2.dto.MemberCreditDTO;
import com.ecommerce.pay.api.v2.dto.MemberCreditQueryDTO;
import com.ecommerce.pay.api.v2.enums.ChannelCodeEnum;
import com.ecommerce.pay.api.v2.enums.MemberChannelStatusEnum;
import com.ecommerce.pay.v2.biz.IChannelConfigBiz;
import com.ecommerce.pay.v2.biz.IMemberChannelBiz;
import com.ecommerce.pay.v2.biz.IMemberCreditBiz;
import com.ecommerce.pay.v2.dao.mapper.MemberCreditMapper;
import com.ecommerce.pay.v2.dao.vo.MemberCredit;
import com.github.pagehelper.Page;
import com.github.pagehelper.page.PageMethod;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Service
public class MemberCreditBiz implements IMemberCreditBiz
{
    private static Logger logger = LoggerFactory.getLogger(MemberCreditBiz.class);

    @Autowired
    private MemberCreditMapper memberCreditMapper;

    @Autowired
    private IMemberChannelBiz memberChannelBiz;

    @Autowired
    private IChannelConfigBiz channelConfigBiz;

    @Autowired
    private CommonBusinessIdGenerator takeCodeGenerator;

    @Autowired
    private UUIDGenerator uuidGenerator;

    @Override
    public Page<MemberCreditDTO> getMemberCreditListByPage(MemberCreditQueryDTO memberCreditQueryDTO)
    {
        int pageNum = memberCreditQueryDTO.getPageNum() == null ? 1 : memberCreditQueryDTO.getPageNum();
        int pageSize = memberCreditQueryDTO.getPageSize() == null ? 20 : memberCreditQueryDTO.getPageSize();

        Condition condition = new Condition(MemberCredit.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("delFlg", 0);


        // 买家名称查询
        if(!CsStringUtils.isNullOrBlank(memberCreditQueryDTO.getMemberName()))
        {
            criteria.andLike("memberName", "%" + memberCreditQueryDTO.getMemberName() + "%");
        }

        // 买家ID查询
        if(!CsStringUtils.isNullOrBlank(memberCreditQueryDTO.getMemberId()))
        {
            criteria.andEqualTo("memberId", memberCreditQueryDTO.getMemberId());
        }

        // 授信历史
        if(memberCreditQueryDTO.getIsHistory() == 1)
        {
            condition.orderBy("memberId").asc();
            condition.orderBy("endDate").desc();
            criteria.andEqualTo("status", 0);
        }
        // 修改授信
        else
        {
            condition.orderBy("startDate").desc();

            Set<Integer> statuses = new HashSet<>();
            statuses.add(1);
            statuses.add(2);
            criteria.andIn("status", statuses);
        }

        // 卖家名称查询
        if(!CsStringUtils.isNullOrBlank(memberCreditQueryDTO.getPayeeMemberName()))
        {
            criteria.andLike("payeeMemberName", "%" + memberCreditQueryDTO.getPayeeMemberName() + "%");
        }
        // 卖家ID查询
        if(!CsStringUtils.isNullOrBlank(memberCreditQueryDTO.getPayeeMemberId()))
        {
            criteria.andEqualTo("payeeMemberId", memberCreditQueryDTO.getPayeeMemberId());
        }

        // 开始有效期
        if(memberCreditQueryDTO.getEffectiveBeginDate() != null)
        {
            criteria.andEqualTo("startDate", memberCreditQueryDTO.getEffectiveBeginDate());
        }

        // 结束有效期
        if(memberCreditQueryDTO.getEffectiveEndDate() != null)
        {
            criteria.andEqualTo("endDate", memberCreditQueryDTO.getEffectiveEndDate());
        }

        if(memberCreditQueryDTO.getMinBalanceAmount() != null && memberCreditQueryDTO.getMinBalanceAmount().equals(BigDecimal.ZERO) == false)
        {
            criteria.andGreaterThanOrEqualTo("creditAmount", memberCreditQueryDTO.getMinBalanceAmount());
        }

        if(memberCreditQueryDTO.getMaxBalanceAmount() != null && memberCreditQueryDTO.getMaxBalanceAmount().equals(BigDecimal.ZERO) == false)
        {
            criteria.andLessThanOrEqualTo("creditAmount", memberCreditQueryDTO.getMaxBalanceAmount());
        }

        Page<MemberCredit> page = PageMethod.startPage(pageNum, pageSize).doSelectPage(() -> memberCreditMapper.selectByCondition(condition));

        return BeanConvertUtils.convertPage(page, MemberCreditDTO.class);
    }

    @Override
    public void insertMemberCreditInfos(List<MemberCreditDTO> memberCreditDTOs)
    {
        // 查出有授信记录的时间包含今天的
        List<MemberChannelDTO> todayList = new ArrayList<>();
        ChannelConfigDTO channelConfigDTO = channelConfigBiz.findByChannelCode(ChannelCodeEnum.CREDIT.getCode());

        Set<MemberCreditDTO> checkMember = new HashSet<>();
        String yesterday = DateUtil.convertDateToString(new Date(System.currentTimeMillis() - 86400000));


        memberCreditDTOs.stream().forEach(memberCreditDTO -> {
            // 判断授信的时间是否有重叠的
            memberCreditDateIsOk(memberCreditDTO);

            // 将用户授信的状态2 => 0
            memberCreditMapper.updateMemberCreditInfoToExpire(memberCreditDTO.getMemberId(), memberCreditDTO.getPayeeMemberId(), yesterday);

            MemberCredit memberCredit = new MemberCredit();
            BeanUtils.copyProperties(memberCreditDTO, memberCredit);
            memberCredit.setUpdateUser(memberCredit.getCreateUser());

            // 今日的授信
            int status = addTodayCreditList(memberCreditDTO, channelConfigDTO, todayList);
            memberCredit.setStatus(status);
            memberCreditMapper.insertSelective(memberCredit);

            // 将来的授信 需要保证授信记录中有记录
            if(status == 1)
            {
                checkMember.add(memberCreditDTO);
            }
        });

        for(MemberCreditDTO memberCreditDTO : checkMember)
        {
            MemberChannelDTO dto = memberChannelBiz.findMemberChannelByPayee(memberCreditDTO.getMemberId(), memberCreditDTO.getPayeeMemberId(), ChannelCodeEnum.CREDIT);
            if(dto == null)
            {
                // 新增一个为0的授信
                MemberCreditDTO creditDTO = new MemberCreditDTO();
                creditDTO.setMemberId(memberCreditDTO.getMemberId());
                creditDTO.setMemberCode(memberCreditDTO.getMemberCode());
                creditDTO.setMemberName(memberCreditDTO.getMemberName());
                creditDTO.setPayeeMemberCode(memberCreditDTO.getPayeeMemberCode());
                creditDTO.setPayeeMemberId(memberCreditDTO.getPayeeMemberId());
                creditDTO.setPayeeMemberName(memberCreditDTO.getPayeeMemberName());
                creditDTO.setCreditAmount(BigDecimal.ZERO);
                creditDTO.setCreateUser(memberCreditDTO.getCreateUser());

                todayList.add(convertToDtoMemberChannelDTO(channelConfigDTO, creditDTO));
            }
        }

        // 将今天得授信数据写入MemberChannel表中
        memberChannelBiz.batchAddOrUpdateCreditMemberChannel(todayList);
    }

    @Override
    public void updateMemberCreditInfos(List<MemberCreditDTO> memberCreditDTOs)
    {
        logger.info("updateMemberCreditInfos - {}", JSON.toJSONString(memberCreditDTOs));

        // 查出有授信记录的时间包含今天的
        Map<String, MemberChannelDTO> todayMap = new HashMap<>();
        ChannelConfigDTO channelConfigDTO = channelConfigBiz.findByChannelCode(ChannelCodeEnum.CREDIT.getCode());

        String yesterday = DateUtil.convertDateToString(new Date(System.currentTimeMillis() - 86400000));
        Date yesDate = DateUtil.parse(yesterday, "yyyy-MM-dd");

        Map<String, MemberCreditDTO> userList = new HashMap<>();
        memberCreditDTOs.stream().forEach(memberCreditDTO -> {
            // 判断授信的时间是否有重叠的
            memberCreditDateIsOk(memberCreditDTO);

            String key = memberCreditDTO.getMemberId() + "@" + memberCreditDTO.getPayeeMemberId();
            if(memberCreditDTO.getMemberCreditId() == null)
            {
                userList.put(key, memberCreditDTO);
            }

            // 判断新增的授信记录中是否有今天的授信
            int status = hasTodayCreditRecord(memberCreditDTO);
            memberCreditDTO.setStatus(status);

            switch(status)
            {
                case 1:
                    // 这是未来时间的数据
                    updateOrInsertMemberCreditInfo(memberCreditDTO);
                    break;
                case 2:
                    // 这是今天的数据


                    // 更新的话查询一下当前生效的数据
                    if(memberCreditDTO.getMemberCreditId() != null)
                    {
                        MemberCredit dbCredit = memberCreditMapper.getMemberCreditInfoById(memberCreditDTO.getMemberCreditId());
                        if(dbCredit.getStatus() == 2)
                        {
                            if(memberCreditDTO.getCreditAmount().compareTo(dbCredit.getCreditAmount()) != 0)
                            {
                                todayMap.put(key, convertToDtoMemberChannelDTO(channelConfigDTO, memberCreditDTO));
                                updateOrInsertMemberCreditInfo(memberCreditDTO);
                            }
                        }
                        else if(dbCredit.getStatus() == 0)
                        {}
                    }
                    else
                    {
                        // 查询生效中的数据
                        MemberCredit credit = memberCreditMapper.getOneMemberCreditInfoByMemberIdAndPayeeMemberIdAndStatus(memberCreditDTO.getMemberId(), memberCreditDTO.getPayeeMemberId(), 2);
                        if (credit != null)
                        {
                            // 数据库中生效中的数据和现在的数据是不一样的
                            // 将数据库中的数据更新为已失效
                            if (credit.getMemberCreditId().equals(memberCreditDTO.getMemberCreditId()) == false)
                            {
                                MemberCredit upCredit = new MemberCredit();
                                upCredit.setMemberCreditId(credit.getMemberCreditId());
                                upCredit.setEndDate(yesDate);
                                upCredit.setStatus(0);
                                memberCreditMapper.updateMemberCreditInfoById(upCredit);
                            }
                        }
                        updateOrInsertMemberCreditInfo(memberCreditDTO);

                        todayMap.put(key, convertToDtoMemberChannelDTO(channelConfigDTO, memberCreditDTO));
                    }
                    break;
            }
        });

        logger.info("1 =====> {}", JSON.toJSONString(todayMap));

        // 检查新增记录的用户是否有开通授信
        for(Map.Entry<String, MemberCreditDTO> entry : userList.entrySet())
        {
            if(todayMap.containsKey(entry.getKey()))
            {
                continue;
            }

            MemberCreditDTO memberCreditDTO = entry.getValue();
            MemberChannelDTO dto = memberChannelBiz.findMemberChannelByPayee(memberCreditDTO.getMemberId(), memberCreditDTO.getPayeeMemberId(), ChannelCodeEnum.CREDIT);
            if(dto == null)
            {
                // 新增一个为0的授信
                MemberCreditDTO creditDTO = new MemberCreditDTO();
                creditDTO.setMemberId(memberCreditDTO.getMemberId());
                creditDTO.setMemberCode(memberCreditDTO.getMemberCode());
                creditDTO.setMemberName(memberCreditDTO.getMemberName());
                creditDTO.setPayeeMemberCode(memberCreditDTO.getPayeeMemberCode());
                creditDTO.setPayeeMemberId(memberCreditDTO.getPayeeMemberId());
                creditDTO.setPayeeMemberName(memberCreditDTO.getPayeeMemberName());
                creditDTO.setCreditAmount(BigDecimal.ZERO);
                creditDTO.setCreateUser(memberCreditDTO.getCreateUser());

                todayMap.put(entry.getKey(), convertToDtoMemberChannelDTO(channelConfigDTO, creditDTO));
            }
        }

        List<MemberChannelDTO> todayList = new ArrayList<>();
        for(Map.Entry<String, MemberChannelDTO> entry : todayMap.entrySet())
        {
            todayList.add(entry.getValue());
        }

        logger.info("2 =====> {}", JSON.toJSONString(todayMap));
        logger.info("3 =====> {}", JSON.toJSONString(todayList));

        // 将今天得授信数据写入MemberChannel表中
        memberChannelBiz.batchAddOrUpdateCreditMemberChannel(todayList);
    }

    private void updateOrInsertMemberCreditInfo(MemberCreditDTO memberCreditDTO)
    {
        if(memberCreditDTO.getMemberCreditId() == null)
        {
            memberCreditDTO.setMemberCreditId(uuidGenerator.gain());
            MemberCredit memberCredit = new MemberCredit();
            BeanUtils.copyProperties(memberCreditDTO, memberCredit);
            memberCreditMapper.insertSelective(memberCredit);
            return;
        }

        // 更新
        MemberCredit memberCredit = new MemberCredit();
        BeanUtils.copyProperties(memberCreditDTO, memberCredit);
        memberCreditMapper.updateByPrimaryKeySelective(memberCredit);
    }

    @Override
    public void deleteMemberCreditInfo(List<String> idList, String operator)
    {
        Date today = new Date();

        idList.stream().forEach(id -> {
            MemberCredit memberCredit = memberCreditMapper.selectByPrimaryKey(id);
            if(memberCredit != null)
            {
                // 查询删除的记录是否是今天的记录
                // 今天的记录不能删除
                if(today.after(memberCredit.getStartDate()) && today.before(memberCredit.getEndDate()))
                {
                    throw new BizException(PayCode.UNDEFINED_ERROR, "该授信已生效，不能删除！");
                }

                // 删除记录
                MemberCredit upCredit = new MemberCredit();
                upCredit.setMemberCreditId(id);
                upCredit.setDelFlg(true);
                upCredit.setUpdateUser(operator);
                upCredit.setUpdateTime(today);
                memberCreditMapper.updateByPrimaryKeySelective(upCredit);
            }
        });
    }

    /**
     * 将今日的授信加入列表
     * @param memberCreditDTO
     * @param channelConfigDTO
     * @param todayList
     */
    private int addTodayCreditList(MemberCreditDTO memberCreditDTO, ChannelConfigDTO channelConfigDTO, List<MemberChannelDTO> todayList)
    {
        // 判断新增的授信记录中是否有今天的授信
        int status = hasTodayCreditRecord(memberCreditDTO);
        if(status == 2 && memberCreditDTO.getMemberCreditId() == null)
        {
            todayList.add(convertToDtoMemberChannelDTO(channelConfigDTO, memberCreditDTO));
        }

        return status;
    }

    private MemberChannelDTO convertToDtoMemberChannelDTO(ChannelConfigDTO channelConfigDTO, MemberCreditDTO memberCreditDTO)
    {
        MemberChannelDTO memberChannelDTO = new MemberChannelDTO();
        BeanUtils.copyProperties(channelConfigDTO, memberChannelDTO);

        memberChannelDTO.setMemberId(memberCreditDTO.getMemberId());
        memberChannelDTO.setMemberCode(memberCreditDTO.getMemberCode());
        memberChannelDTO.setMemberName(memberCreditDTO.getMemberName());
        memberChannelDTO.setExt4(memberCreditDTO.getPayeeMemberCode());
        memberChannelDTO.setPayeeMemberId(memberCreditDTO.getPayeeMemberId());
        memberChannelDTO.setMemberChannelStatus(MemberChannelStatusEnum.OPEND.getCode());
        memberChannelDTO.setMemberChannelCode(takeCodeGenerator.incrementChannelCode());
        memberChannelDTO.setAllowReceive(false);
        memberChannelDTO.setAllowPayment(true);
        memberChannelDTO.setExtSysName(memberCreditDTO.getPayeeMemberName());
        memberChannelDTO.setBalanceAmount(memberCreditDTO.getCreditAmount());
        memberChannelDTO.setEffectiveBeginDate(memberCreditDTO.getStartDate());
        memberChannelDTO.setEffectiveEndDate(memberCreditDTO.getEndDate() == null ? null : new Date(memberCreditDTO.getEndDate().getTime() + 86399000));
        memberChannelDTO.setCreateUser(memberCreditDTO.getCreateUser());
        memberChannelDTO.setCreateTime(new Date());

        return memberChannelDTO;
    }

    /**
     * 判断新增的授信记录中是否有今天的授信
     * @param memberCreditDTO
     * @return
     */
    private int hasTodayCreditRecord(MemberCreditDTO memberCreditDTO)
    {
        // 获取今天凌晨的时间戳
        String pattern = "yyyy-MM-dd";
        long today = DateUtil.parse(DateUtil.format(new Date(), pattern), pattern).getTime();

        logger.info("今天凌晨的时间戳: {}", DateUtil.format(new Date(today), "yyyy-MM-dd HH:mm:ss"));

        // 永久授信
        if(CsStringUtils.isNullOrBlank(memberCreditDTO.getEndDate()))
        {
            return today >= memberCreditDTO.getStartDate().getTime() ? 2 : 1;
        }

        int status = 1;
        if(today >= memberCreditDTO.getStartDate().getTime() && today <= memberCreditDTO.getEndDate().getTime())
        {
            status = 2;
        }
        else if(today > memberCreditDTO.getEndDate().getTime())
        {
            status = 0;
        }
        return status;
    }

    /**
     * 判断当前记录的时间是否
     * @param memberCreditDTO
     * @throws Exception
     */
    private void memberCreditDateIsOk(MemberCreditDTO memberCreditDTO)
    {
        logger.info("{}", JSON.toJSONString(memberCreditDTO));

        if(CsStringUtils.isNullOrBlank(memberCreditDTO.getStartDate()) == true)
        {
            throw new BizException(PayCode.UNDEFINED_ERROR, "授信开始时间不能为空");
        }

        if(CsStringUtils.isNullOrBlank(memberCreditDTO.getEndDate()) == false && memberCreditDTO.getStartDate().after(memberCreditDTO.getEndDate()))
        {
            throw new BizException(PayCode.UNDEFINED_ERROR, "授信开始时间不能晚于结束时间");
        }
        // 获取今天凌晨的时间戳
        String pattern = "yyyy-MM-dd";
        long today = DateUtil.parse(DateUtil.format(new Date(), pattern), pattern).getTime();
        if(memberCreditDTO.getMemberCreditId() == null && memberCreditDTO.getStartDate().getTime() < today)
        {
            throw new BizException(PayCode.UNDEFINED_ERROR, "授信开始时间不能晚于今天");
        }
        if(CsStringUtils.isNullOrBlank(memberCreditDTO.getEndDate()) == false && memberCreditDTO.getEndDate().getTime() < today)
        {
            throw new BizException(PayCode.UNDEFINED_ERROR, "不能添加已过期的授信");
        }

        List<MemberCredit> memberCredits = getMemberCreditListByMemberIdAndPayeeMemberId(memberCreditDTO.getMemberId(), memberCreditDTO.getPayeeMemberId());
        if(CollectionUtils.isEmpty(memberCredits))
        {
            return;
        }

        for(MemberCredit memberCredit : memberCredits)
        {
            // 这是更新操作
            if(memberCreditDTO.getMemberCreditId() != null && memberCreditDTO.getMemberCreditId().equals(memberCredit.getMemberCreditId()))
            {
                continue;
            }

            // 要修改的时间是一个时间段
            if(memberCreditDTO.getEndDate() != null)
            {
                // 数据库中的时间是一个时间段
                if(memberCredit.getEndDate() != null) {
                    if(memberCredit.getStartDate().after(memberCreditDTO.getEndDate()) == false && memberCredit.getEndDate().before(memberCreditDTO.getStartDate()) == false)
                    {
                        logger.info("{} {} => 重复", memberCreditDTO, memberCredit);
                        throw new BizException(PayCode.UNDEFINED_ERROR, "授信存在重复时间段" + DateUtil.format(memberCredit.getStartDate(), "yyyy-MM-dd") + "至" + DateUtil.format(memberCredit.getEndDate(), "yyyy-MM-dd"));
                    }
                }
                // 数据库中的时间没有结束时间
                else {
                    // 要修改的开始时间不能在时间段内
                    if(memberCredit.getStartDate().after(memberCreditDTO.getStartDate()) && memberCredit.getStartDate().before(memberCreditDTO.getEndDate())) {
                        throw new BizException(PayCode.UNDEFINED_ERROR, "授信存在重复时间段" + DateUtil.format(memberCredit.getStartDate(), "yyyy-MM-dd") + "至-");
                    }

                    if(memberCreditDTO.getStartDate().equals(memberCredit.getStartDate()) || memberCreditDTO.getEndDate().equals(memberCredit.getStartDate())) {
                        throw new BizException(PayCode.UNDEFINED_ERROR, "授信存在重复时间段" + DateUtil.format(memberCredit.getStartDate(), "yyyy-MM-dd") + "至-");
                    }
                }
            }
            // 要修改的时间是没有结束时间
            else {
                // 数据库中的时间没有结束时间
                if(memberCredit.getEndDate() == null) {
                    if(memberCreditDTO.getStartDate().equals(memberCredit.getStartDate())) {
                        throw new BizException(PayCode.UNDEFINED_ERROR, "授信存在重复时间段" + DateUtil.format(memberCredit.getStartDate(), "yyyy-MM-dd") + "至-");
                    }
                }
                // 数据库中的时间是一个时间段
                else {
                    // 要修改的开始时间不能在时间段内
                    if(memberCreditDTO.getStartDate().after(memberCredit.getStartDate()) && memberCreditDTO.getStartDate().before(memberCredit.getEndDate())) {
                        throw new BizException(PayCode.UNDEFINED_ERROR, "授信存在重复时间段" + DateUtil.format(memberCredit.getStartDate(), "yyyy-MM-dd") + "至" + DateUtil.format(memberCredit.getEndDate(), "yyyy-MM-dd"));
                    }

                    if(memberCredit.getStartDate().equals(memberCreditDTO.getStartDate()) || memberCredit.getEndDate().equals(memberCreditDTO.getStartDate())) {
                        throw new BizException(PayCode.UNDEFINED_ERROR, "授信存在重复时间段" + DateUtil.format(memberCredit.getStartDate(), "yyyy-MM-dd") + "至" + DateUtil.format(memberCredit.getEndDate(), "yyyy-MM-dd"));
                    }
                }
            }
        }
    }

    /**
     * 根据MemberId和PayeeMemberId获取授信列表
     * @param memberId
     * @param payeeMemberId
     * @return
     */
    public List<MemberCredit> getMemberCreditListByMemberIdAndPayeeMemberId(String memberId, String payeeMemberId)
    {
        Condition condition = new Condition(MemberCredit.class);
        condition.orderBy("startDate").desc();
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("delFlg", 0);
        criteria.andEqualTo("memberId", memberId);
        criteria.andEqualTo("payeeMemberId", payeeMemberId);

        //


        return memberCreditMapper.selectByCondition(condition);
    }


    @Override
    public MemberCredit findNextOne(String payerMemberId, String payeeMemberId) {
        return memberCreditMapper.findNextOne(payerMemberId,payeeMemberId);
    }

    @Override
    public MemberCredit findPreviousOne(String payerMemberId, String payeeMemberId) {
        return memberCreditMapper.findPreviousOne(payerMemberId,payeeMemberId);
    }
}
