package com.ecommerce.pay.v2.biz.impl;

import com.alibaba.fastjson.JSON;
import com.ecommerce.common.utils.BeanConvertUtils;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.member.api.service.IMemberService;
import com.ecommerce.pay.api.v2.dto.MemberChannelDTO;
import com.ecommerce.pay.api.v2.dto.MemberCreditRecordDTO;
import com.ecommerce.pay.api.v2.dto.MemberCreditRecordQueryDTO;
import com.ecommerce.pay.api.v2.dto.electricInvoiceAccount.PageQuery;
import com.ecommerce.pay.enums.MemberCreditRecordEnum;
import com.ecommerce.pay.v2.biz.IMemberCreditRecordBiz;
import com.ecommerce.pay.v2.dao.mapper.MemberCreditRecordMapper;
import com.ecommerce.pay.v2.dao.vo.MemberCreditRecord;
import com.github.pagehelper.Page;
import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.Sets;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Set;

@Service
public class MemberCreditRecordBiz implements IMemberCreditRecordBiz
{
    private static Logger logger = LoggerFactory.getLogger(MemberCreditRecordBiz.class);

    @Autowired
    private MemberCreditRecordMapper memberCreditRecordMapper;

    @Autowired
    private IMemberService memberService;

    @Override
    public void insertMemberCreditRecordInfo(MemberCreditRecordDTO memberCreditRecordDTO)
    {
        memberCreditRecordDTO.setCreateTime(new Date());

        MemberCreditRecord memberCreditRecord = new MemberCreditRecord();
        BeanUtils.copyProperties(memberCreditRecordDTO, memberCreditRecord);

        logger.info("payback:memberCreditRecordDTO: {}", JSON.toJSONString(memberCreditRecordDTO));
        logger.info("payback:memberCreditRecord: {}", JSON.toJSONString(memberCreditRecord));

        memberCreditRecordMapper.insertSelective(memberCreditRecord);
    }

    @Override
    public void insertMemberCreditRecordByMemberChannel(MemberChannelDTO memberChannelDTO, BigDecimal amount, MemberCreditRecordEnum type, String billNo, String memo, String fileAttach, String operator)
    {
        String payeeMemberName = memberService.findMemberNameByMemberId(memberChannelDTO.getPayeeMemberId());

        MemberCreditRecordDTO memberCreditRecordDTO = new MemberCreditRecordDTO();
        memberCreditRecordDTO.setMemberId(memberChannelDTO.getMemberId());
        memberCreditRecordDTO.setMemberCode(memberChannelDTO.getMemberCode());
        memberCreditRecordDTO.setMemberName(memberChannelDTO.getMemberName());
        memberCreditRecordDTO.setPayeeMemberId(memberChannelDTO.getPayeeMemberId());
        memberCreditRecordDTO.setPayeeMemberName(payeeMemberName);
        memberCreditRecordDTO.setType(type.getType());
        memberCreditRecordDTO.setAmount(amount);
        memberCreditRecordDTO.setBillNo(billNo);
        memberCreditRecordDTO.setMemo(memo);
        memberCreditRecordDTO.setFileAttach(fileAttach);
        memberCreditRecordDTO.setCreateUser(operator);

        insertMemberCreditRecordInfo(memberCreditRecordDTO);
    }

    @Override
    public Page<MemberCreditRecordDTO> getMemberCreditRecordListByPage(PageQuery<MemberCreditRecordQueryDTO> pageQuery)
    {
        pageQuery = pageQuery == null ? new PageQuery<>() : pageQuery;

        int pageNum = pageQuery.getPageNum() == null ? 1 : pageQuery.getPageNum();
        int pageSize = pageQuery.getPageSize() == null ? 10 : pageQuery.getPageSize();

        MemberCreditRecordQueryDTO queryDto = pageQuery.getQueryDTO();

        Condition condition = new Condition(MemberCreditRecord.class);
        condition.orderBy("id").desc();
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("delFlg", 0);


        // 买家ID
        if (CsStringUtils.isNotBlank(queryDto.getMemberId()))
        {
            criteria.andEqualTo("memberId", queryDto.getMemberId());
        }

        // 买家名称
        if (CsStringUtils.isNotBlank(queryDto.getMemberName()))
        {
            criteria.andLike("memberName", "%" + queryDto.getMemberName() + "%");
        }

        // 卖家ID
        if (CsStringUtils.isNotBlank(queryDto.getPayeeMemberId()))
        {
            criteria.andEqualTo("payeeMemberId", queryDto.getPayeeMemberId());
        }

        // 卖家名称
        if (CsStringUtils.isNotBlank(queryDto.getPayeeMemberName()))
        {
            criteria.andLike("payeeMemberName", "%" + queryDto.getPayeeMemberName() + "%");
        }

        // 账单流水号
        if (CsStringUtils.isNotBlank(queryDto.getBillNo()))
        {
            criteria.andEqualTo("billNo", queryDto.getBillNo());
        }

        // 记录类型 1：授信、2：支付、3：还款、4：退款
        if(queryDto.getType() != null)
        {
            if(queryDto.getType() > 0)
            {
                criteria.andEqualTo("type", queryDto.getType());
            }
        }
        else
        {
            Set<Integer> types = Sets.newHashSet(MemberCreditRecordEnum.CREDIT.getType(), MemberCreditRecordEnum.PAYBACK.getType());
            criteria.andIn("type", types);
        }

        // 最小账单金额
        if(queryDto.getMinAmount() != null)
        {
            criteria.andGreaterThanOrEqualTo("amount", queryDto.getMinAmount());
        }

        // 最大账单金额
        if(queryDto.getMaxAmount() != null)
        {
            criteria.andLessThanOrEqualTo("amount", queryDto.getMaxAmount());
        }

        // 查询开始时间
        if(queryDto.getStartTime() != null)
        {
            criteria.andGreaterThanOrEqualTo("createTime", queryDto.getStartTime());
        }

        // 查询结束时间
        if(queryDto.getEndTime() != null)
        {
            criteria.andLessThanOrEqualTo("createTime", queryDto.getEndTime());
        }

        Page<MemberCreditRecord> page = PageMethod.startPage(pageNum, pageSize).doSelectPage(() -> memberCreditRecordMapper.selectByCondition(condition));

        return BeanConvertUtils.convertPage(page, MemberCreditRecordDTO.class);
    }
}
