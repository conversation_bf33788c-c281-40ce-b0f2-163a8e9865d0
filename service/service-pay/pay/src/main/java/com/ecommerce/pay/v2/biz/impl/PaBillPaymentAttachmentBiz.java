package com.ecommerce.pay.v2.biz.impl;

import com.ecommerce.base.api.service.IAttachmentService;
import com.ecommerce.common.service.common.BaseBiz;
import com.ecommerce.common.service.common.uuid.UUIDGenerator;
import com.ecommerce.pay.api.v2.enums.ChannelCodeEnum;
import com.ecommerce.pay.v2.biz.IPaBillPaymentAttachmentBiz;
import com.ecommerce.pay.v2.dao.mapper.PaBillPaymentAttachmentMapper;
import com.ecommerce.pay.v2.dao.vo.PaBillPaymentAttachment;
import com.ecommerce.pay.v2.dao.vo.PaymentBill;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Condition;

import java.util.List;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class PaBillPaymentAttachmentBiz extends BaseBiz<PaBillPaymentAttachment> implements IPaBillPaymentAttachmentBiz {

    @Autowired
    private PaBillPaymentAttachmentMapper paBillPaymentAttachmentMapper;
    @Autowired
    private UUIDGenerator uuidGenerator;
    @Autowired
    private IAttachmentService attachmentService;

    //保存线下支付的附件
    @Override
    public void save(List<String> list, PaymentBill paymentBill) {
        if( CollectionUtils.isEmpty(list) ){
            log.debug("附件为空");
            return;
        }
        //特别问了Lola_0521 只有线下支付才有附件
        if( !ChannelCodeEnum.OFFLINE.getCode().equalsIgnoreCase(paymentBill.getChannelCode()) ){
            log.info("channel code is: {}",paymentBill.getChannelCode());
            return;
        }
        paBillPaymentAttachmentMapper.insertList(
        list.stream().map(item->{
            PaBillPaymentAttachment paBillPaymentAttachment = new PaBillPaymentAttachment();
            paBillPaymentAttachment.setCreateTime(paymentBill.getCreateTime());
            paBillPaymentAttachment.setCreateUser(paymentBill.getCreateUser());
            paBillPaymentAttachment.setDelFlg(false);
            paBillPaymentAttachment.setPaBillPaymentAttachmentId(uuidGenerator.gain());
            paBillPaymentAttachment.setPaymentBillId(paymentBill.getPaymentBillId());
            paBillPaymentAttachment.setPaymentBillNo(paymentBill.getPaymentBillNo());
            //去掉参数
            if( item.indexOf("?") != -1 ){
                paBillPaymentAttachment.setUrl(item.substring(0,item.indexOf("?")));
            }else{
                paBillPaymentAttachment.setUrl(item);
            }
            return paBillPaymentAttachment;
        }).toList());
    }

    @Override
    public List<String> findByPaymentBillId(String paymentBillId, String channelCode) {
        if( !ChannelCodeEnum.OFFLINE.getCode().equalsIgnoreCase(channelCode) ){
            return Lists.newArrayList();
        }
        Condition condition = newCondition();
        condition.createCriteria().andEqualTo("paymentBillId",paymentBillId);
        List<PaBillPaymentAttachment> list = paBillPaymentAttachmentMapper.selectByCondition(condition);
        if( CollectionUtils.isEmpty(list) ){
            return Lists.newArrayList();
        }
        //添加签名
        return attachmentService.getFullPathList(list.stream().map(PaBillPaymentAttachment::getUrl).toList());
    }
}
