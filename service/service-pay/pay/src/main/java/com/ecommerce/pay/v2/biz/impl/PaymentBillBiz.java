package com.ecommerce.pay.v2.biz.impl;

import com.beust.jcommander.internal.Lists;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.service.common.BaseBiz;
import com.ecommerce.common.service.common.bsid.CommonBusinessIdGenerator;
import com.ecommerce.common.service.common.uuid.UUIDGenerator;
import com.ecommerce.common.utils.ArithUtils;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.pay.api.exception.PayCode;
import com.ecommerce.pay.api.v2.dto.bill.PaymentBillDTO;
import com.ecommerce.pay.api.v2.dto.bill.PaymentBillDetailDTO;
import com.ecommerce.pay.api.v2.dto.query.PaymentBillQueryDTO;
import com.ecommerce.pay.api.v2.enums.PaymentStatusEnum;
import com.ecommerce.pay.v2.biz.IPaBillPaymentAttachmentBiz;
import com.ecommerce.pay.v2.biz.IPaymentBillBiz;
import com.ecommerce.pay.v2.biz.IPaymentBillDetailBiz;
import com.ecommerce.pay.v2.dao.mapper.PaymentBillMapper;
import com.ecommerce.pay.v2.dao.vo.PaymentBill;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @Date 05/12/2018 14:35
 */
@Slf4j
@Service
public class PaymentBillBiz extends BaseBiz<PaymentBill> implements IPaymentBillBiz {

    @Autowired
    private CommonBusinessIdGenerator takeCodeGenerator;
    @Autowired
    private IPaymentBillDetailBiz paymentBillDetailBiz;
    @Autowired
    private PaymentBillMapper mapper;
    @Autowired
    private IPaBillPaymentAttachmentBiz paBillPaymentAttachmentBiz;
    @Autowired
    private UUIDGenerator uuidGenerator;

    public static final String SRC_BIZ_NO = "srcBizNo";
    public static final String CREATE_TIME = "createTime";
    public static final String PAYMENT_BILL_NO = "paymentBillNo";
    public static final String STATUS = "status";
    public static final String DEL_FLG = "delFlg";

    @Override
    public void create(PaymentBillDTO paymentBillDTO, String operatorId) {
        if (paymentBillDTO == null) {
            throw new BizException(PayCode.DATA_NOT_FOUND);
        }
        paymentBillDTO.setDeal(false);
        paymentBillDTO.setPaymentBillNo(takeCodeGenerator.incrementPayCode());
        if (findByBizNo(paymentBillDTO.getSrcBizNo()) == null) {
            PaymentBill paymentBill = convert(paymentBillDTO);
            paymentBill.setPaymentBillId(uuidGenerator.gain());
            paymentBill.setDelFlg(false);
            paymentBill.setCreateUser(operatorId);
            if( paymentBill.getCreateTime() == null ){
                paymentBill.setCreateTime(new Date(System.currentTimeMillis()));
            }
            paymentBill.setUpdateTime(paymentBill.getCreateTime());
            paymentBill.setUpdateUser(paymentBill.getCreateUser());
            mapper.insert(paymentBill);
            paBillPaymentAttachmentBiz.save(paymentBillDTO.getOfflineAttachment(),paymentBill);
            PaymentBillDTO t = convertToDTO(paymentBill);
            paymentBillDTO.setPaymentBillId(t.getPaymentBillId());
            paymentBillDTO.setCreateTime(t.getCreateTime());
        } else {
            throw new BizException(BasicCode.DATA_EXIST);
        }
    }

    @Override
    public PaymentBillDTO findRecentRecordBySrcBizNo(String srcBizNo) {
    	log.info(" start findRecentRecordBySrcBizNo {}" , srcBizNo);
        Condition condition = new Condition(PaymentBill.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(SRC_BIZ_NO, srcBizNo);
        criteria.andEqualTo(DEL_FLG, 0);
        criteria.andNotEqualTo(STATUS, PaymentStatusEnum.PAY_CLOSE.getCode());
        condition.orderBy(CREATE_TIME).desc();
        List<PaymentBill> voList = findByCondition(condition);
        if (CollectionUtils.isEmpty(voList)) {
            return null;
        }
        List<PaymentBillDTO> dtoList = new ArrayList<>();
        for (PaymentBill paymentBill : voList) {
            dtoList.add(convertToDTO(paymentBill));
        }
        PaymentBillDTO paymentBillDTO = dtoList.get(0);
        List<PaymentBillDetailDTO> detailDTOList = paymentBillDetailBiz.findByPaymentBillNo(paymentBillDTO.getPaymentBillNo());
        paymentBillDTO.setDetailDTOList(detailDTOList);
        paymentBillDTO.setOfflineAttachment(paBillPaymentAttachmentBiz.findByPaymentBillId(paymentBillDTO.getPaymentBillId(),paymentBillDTO.getChannelCode()));
        return paymentBillDTO;
    }

    @Override
    public PaymentBillDTO findByPaymentBillNo(String paymentBillNo) {
        if (CsStringUtils.isEmpty(paymentBillNo)) {
            return null;
        }
        if( paymentBillNo.indexOf(",") != -1){
            //兼容退款功能 同一个渠道多次支付的退款，合并一次退款的情况
            return findByPaymentBillNo2(paymentBillNo.split(","));
        }
        Condition condition = new Condition(PaymentBill.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(PAYMENT_BILL_NO, paymentBillNo);
        criteria.andEqualTo(DEL_FLG, 0);
        List<PaymentBill> voList = findByCondition(condition);
        if (CollectionUtils.isEmpty(voList)) {
            return null;
        }
        PaymentBillDTO paymentBillDTO = convertToDTO(voList.get(0));
        List<PaymentBillDetailDTO> detailDTOList = paymentBillDetailBiz.findByPaymentBillNo(paymentBillDTO.getPaymentBillNo());
        paymentBillDTO.setDetailDTOList(detailDTOList);
        paymentBillDTO.setOfflineAttachment(paBillPaymentAttachmentBiz.findByPaymentBillId(paymentBillDTO.getPaymentBillId(),paymentBillDTO.getChannelCode()));
        return paymentBillDTO;
    }

    @Override
    public String findExtBizNoByPaymentBillNo(String paymentBillNo) {
        Condition condition = new Condition(PaymentBill.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(PAYMENT_BILL_NO, paymentBillNo);
        criteria.andEqualTo(DEL_FLG, 0);
        List<PaymentBill> voList = findByCondition(condition);
        return CollectionUtils.isEmpty(voList) ? null : voList.get(0).getExtBizNo();
    }

    //兼容退款功能 同一个渠道多次支付的退款，合并一次退款的情况  见：order项目OrderPayinfoService.genRefundPayInfoList
    //paymentBillNo,paymentBillNo,paymentBillNo
    private PaymentBillDTO findByPaymentBillNo2(String[] paymentBillNoArray) {
        Condition condition = new Condition(PaymentBill.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn(PAYMENT_BILL_NO, Lists.newArrayList(paymentBillNoArray));
        criteria.andEqualTo(DEL_FLG, 0);
        List<PaymentBill> voList = findByCondition(condition);
        if (CollectionUtils.isEmpty(voList)) {
            return null;
        }
        PaymentBillDTO paymentBillDTO = convertToDTO(voList.get(0));
        Map<String,PaymentBillDetailDTO> detailDTOMap = Maps.newHashMap();
        //合并支付金额
        for (int i = 1; i < voList.size(); i++) {
            paymentBillDTO.setPayAmount(ArithUtils.add(paymentBillDTO.getPayAmount(),voList.get(i).getPayAmount()));
            paymentBillDTO.setActualPayAmount(ArithUtils.add(paymentBillDTO.getActualPayAmount(),voList.get(i).getActualPayAmount()));
            paymentBillDTO.setServiceFee(ArithUtils.add(paymentBillDTO.getServiceFee(),voList.get(i).getServiceFee()));
        }
        //合并明细项支付金额
        for (int i = 0; i < voList.size(); i++) {
            List<PaymentBillDetailDTO> paymentBillDetailDTOS = paymentBillDetailBiz.findByPaymentBillNo(voList.get(i).getPaymentBillNo());
            for (PaymentBillDetailDTO paymentBillDetailDTO : paymentBillDetailDTOS) {
                String key = CsStringUtils.isBlank(paymentBillDetailDTO.getSubject()) ? "" : paymentBillDetailDTO.getSubject();
                if (detailDTOMap.containsKey(key)) {
                    PaymentBillDetailDTO paymentBillDetailDTO1 = detailDTOMap.get(key);
                    paymentBillDetailDTO1.setPayAmount(ArithUtils.add(paymentBillDetailDTO1.getPayAmount(), paymentBillDetailDTO.getPayAmount()));
                } else {
                    detailDTOMap.put(key, paymentBillDetailDTO);
                }
            }
        }
        paymentBillDTO.setDetailDTOList(Lists.newArrayList(detailDTOMap.values()));
        return paymentBillDTO;
    }

    @Override
    public List<PaymentBillDTO> findByOrderNo(String orderNo) {
        if (CsStringUtils.isEmpty(orderNo)) {
            throw new BizException(BasicCode.PARAM_NULL, "orderNo");
        }
        Condition condition = newCondition();
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("orderNo", orderNo);
        criteria.andEqualTo(DEL_FLG, false);
        condition.orderBy(CREATE_TIME).asc();

        List<PaymentBill> paymentBills = findByCondition(condition);
        return paymentBills.stream().map(this::convertToDTO).toList();
    }

    @Override
    public PaymentBill findSuccessFirstByOrderNoAndChannel(String orderNo, String channelCode) {
        if (CsStringUtils.isEmpty(orderNo)) {
            return null;
        }
        Condition condition = newCondition();
        condition.createCriteria()
        .andEqualTo("orderNo", orderNo)
        .andEqualTo("channelCode", channelCode)
        .andEqualTo("status", PaymentStatusEnum.PAY_FREEZE.getCode())
        .andEqualTo(DEL_FLG, false);
        condition.orderBy("completeTime").asc();

        List<PaymentBill> list = findByCondition(condition);
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }

    @Override
    public PaymentBillDTO findByPaymentBillId(String paymentBillId) {
        Condition condition = new Condition(PaymentBill.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("paymentBillId", paymentBillId);
        criteria.andEqualTo(DEL_FLG, 0);
        List<PaymentBill> voList = findByCondition(condition);
        if (CollectionUtils.isEmpty(voList)) {
            return null;
        }
        List<PaymentBillDTO> dtoList = new ArrayList<>();
        for (PaymentBill paymentBill : voList) {
            dtoList.add(convertToDTO(paymentBill));
        }
        PaymentBillDTO paymentBillDTO = dtoList.get(0);
        List<PaymentBillDetailDTO> detailDTOList = paymentBillDetailBiz.findByPaymentBillNo(paymentBillDTO.getPaymentBillNo());
        paymentBillDTO.setDetailDTOList(detailDTOList);
        paymentBillDTO.setOfflineAttachment(paBillPaymentAttachmentBiz.findByPaymentBillId(paymentBillDTO.getPaymentBillId(),paymentBillDTO.getChannelCode()));
        return paymentBillDTO;
    }


    @Override
    public PaymentBillDTO updatePaymentBill(PaymentBillDTO paymentBillDTO, String operatorId) {
        if (paymentBillDTO == null) {
            log.info("paymentBillDTO为空！");
            throw new BizException(BasicCode.INVALID_PARAM);
        }
        if (CsStringUtils.isEmpty(paymentBillDTO.getPaymentBillId())) {
            log.info("paymentBillId为空！");
            throw new BizException(BasicCode.INVALID_PARAM);
        }
        PaymentBill paymentBill = convert(paymentBillDTO);
        paymentBill.setUpdateUser(operatorId);
        paymentBill.setUpdateTime(new Date());
        PaymentBillDTO t = convertToDTO(updateSelective(paymentBill));
        paymentBillDetailBiz.updateByPaymentBill(paymentBillDTO, operatorId);
        return t;
    }

    @Override
    public void updateStatus(String billId, String status, String operatorId) {
        PaymentBill payTradeBill = get(billId);
        if (payTradeBill == null) {
            throw new BizException(BasicCode.INVALID_PARAM, "未查询到对应记录！");
        }
        PaymentBillDTO paymentBillDTO = convertToDTO(payTradeBill);
        Date date = new Date();
        // 修改完成时间
        if (status.equals(PaymentStatusEnum.PAY_SUCCESS.getCode())) {
            paymentBillDTO.setCompleteTime(date);
        }
        if (status.equals(PaymentStatusEnum.PAY_CLOSE.getCode())) {
            paymentBillDTO.setCloseTime(date);
        }
        paymentBillDTO.setStatus(status);
        updatePaymentBill(paymentBillDTO, operatorId);
    }

    @Override
    public Page<PaymentBillDTO> pagePaymentBill(PaymentBillQueryDTO queryDTO) {
        Condition condition = new Condition(PaymentBill.class);
        Example.Criteria criteria = condition.createCriteria();

        if (!CsStringUtils.isEmpty(queryDTO.getPaymentBillNo())) {
            criteria.andEqualTo(PAYMENT_BILL_NO, queryDTO.getPaymentBillNo());
        }
        if (!CsStringUtils.isEmpty(queryDTO.getChannelCode())) {
            criteria.andEqualTo("channel_code", queryDTO.getChannelCode());
        }
        if (!CsStringUtils.isEmpty(queryDTO.getSrcBizNo())) {
            criteria.andEqualTo(SRC_BIZ_NO, queryDTO.getSrcBizNo());
        }
        if (!CsStringUtils.isEmpty(queryDTO.getChannelCode())) {
            criteria.andEqualTo("channelCode", queryDTO.getChannelCode());
        }
        if (!CsStringUtils.isEmpty(queryDTO.getPayerMemberId())) {
            criteria.andEqualTo("payerMemberId", queryDTO.getPayerMemberId());
        }
        if (!CsStringUtils.isEmpty(queryDTO.getPayType())) {
            criteria.andEqualTo("payType", queryDTO.getPayType());
        }
        if (!CsStringUtils.isEmpty(queryDTO.getPayeeMemberName())) {
            criteria.andLike("payeeMemberName", '%' + queryDTO.getPayeeMemberName() + '%');
        }
        if (!CsStringUtils.isEmpty(queryDTO.getPayerMemberName())) {
            criteria.andLike("payerMemberName", '%' + queryDTO.getPayerMemberName() + '%');
        }
        if (queryDTO.getShowWarning() != null && !queryDTO.getShowWarning().equals(0)) {
            if (queryDTO.getShowWarning().equals(1)) {
                criteria.andIsNotNull("warningCode");
            }
            else if (queryDTO.getShowWarning().equals(2)) {
                criteria.andIsNull("warningCode");
            }
        }
        if(queryDTO.getStartTime() != null){
            criteria.andGreaterThanOrEqualTo(CREATE_TIME,queryDTO.getStartTime());
        }
        if(queryDTO.getEndTime() != null){
            criteria.andLessThanOrEqualTo(CREATE_TIME,queryDTO.getEndTime());
        }
        criteria.andEqualTo(DEL_FLG, 0);
        condition.orderBy(CREATE_TIME).desc();

        PageInfo<PaymentBill> pageInfo = new PageInfo<>();
        pageInfo.setPageNum(queryDTO.getPageNum());
        pageInfo.setPageSize(queryDTO.getPageSize());

        Page<PaymentBill> page = super.page(condition, pageInfo);
        Page<PaymentBillDTO> collect = page.stream().map(this::convertToDTO)
                .collect(Collectors.toCollection(Page::new));
        BeanUtils.copyProperties(page, collect);

        return collect;
    }

    @Override
    public List<PaymentBill> findPaymentBillDTOWithNoCallback(long timeStep) {
        Condition condition = new Condition(PaymentBill.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(DEL_FLG, 0);
        criteria.andEqualTo(STATUS, PaymentStatusEnum.PAY_ING.getCode());
        criteria.andLessThan(CREATE_TIME, new Date(System.currentTimeMillis() - timeStep));
        List<PaymentBill> voList = findByCondition(condition);
        if (CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        } else {
            return voList;
        }
    }

    @Override
    public Boolean findDealByPaymentBillNo(String paymentBillNo) {
        if (CsStringUtils.isEmpty(paymentBillNo)) {
            return false;
        }
        Condition condition = new Condition(PaymentBill.class);
        condition.createCriteria()
                .andEqualTo("paymentBillNo", paymentBillNo)
                .andEqualTo("delFlg", false);
        condition.selectProperties("deal");
        List<PaymentBill> bills = findByCondition(condition);
        if (CollectionUtils.isEmpty(bills)) {
            return false;
        }
        Boolean deal = bills.get(0).getDeal();
        return deal != null && deal;
    }

    @Override
    public void updateDealByPaymentBillNo(String paymentBillNo, Boolean deal) {
        if (CsStringUtils.isEmpty(paymentBillNo)) {
            return;
        }
        Condition condition = new Condition(PaymentBill.class);
        condition.createCriteria()
                .andEqualTo("paymentBillNo", paymentBillNo)
                .andEqualTo("delFlg", false);
        PaymentBill vo = new PaymentBill();
        vo.setDeal(deal);

        mapper.updateByConditionSelective(vo, condition);
    }

    private PaymentBill findByBizNo(String srcBuzNo) {
        if(srcBuzNo == null) {
            return null;
        }

        Condition condition = new Condition(PaymentBill.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(SRC_BIZ_NO, srcBuzNo);
        criteria.andEqualTo(DEL_FLG, 0);
        criteria.andEqualTo(STATUS, PaymentStatusEnum.NEW_PAY.getCode());
        List<PaymentBill> voList = findByCondition(condition);
        if (CollectionUtils.isEmpty(voList)) {
            return null;
        } else {
            return voList.get(0);
        }
    }

    private PaymentBill convert(PaymentBillDTO paymentBillDTO) {
        if (paymentBillDTO == null) {
            return null;
        }
        PaymentBill paymentBill = new PaymentBill();
        BeanUtils.copyProperties(paymentBillDTO, paymentBill);
        return paymentBill;
    }

    private PaymentBillDTO convertToDTO(PaymentBill paymentBill) {
        PaymentBillDTO paymentBillDTO = new PaymentBillDTO();
        BeanUtils.copyProperties(paymentBill, paymentBillDTO);
        return paymentBillDTO;
    }
}
