package com.ecommerce.pay.v2.biz.impl;

import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.service.common.BaseBiz;
import com.ecommerce.common.service.common.bsid.CommonBusinessIdGenerator;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.pay.api.v2.dto.PaymentRequirementDetailDTO;
import com.ecommerce.pay.api.v2.dto.bill.PaymentBillDTO;
import com.ecommerce.pay.api.v2.dto.bill.PaymentBillDetailDTO;
import com.ecommerce.pay.api.v2.enums.PaymentStatusEnum;
import com.ecommerce.pay.v2.biz.IPaymentBillDetailBiz;
import com.ecommerce.pay.v2.dao.mapper.PaymentBillDetailMapper;
import com.ecommerce.pay.v2.dao.vo.PaymentBillDetail;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @created 14:02 02/03/2019
 * @description
 */
@Slf4j
@Service
public class PaymentBillDetailBiz extends BaseBiz<PaymentBillDetail> implements IPaymentBillDetailBiz {

    @Autowired
    private PaymentBillDetailMapper mapper;

    @Autowired
    private CommonBusinessIdGenerator codeGenerator;

    public static final String PAYMENT_BILL_NO = "paymentBillNo";
    public static final String DEL_FLG = "delFlg";

    @Override
    public List<PaymentBillDetailDTO> findByPaymentBillNo(String paymentBillNo) {
        if (CsStringUtils.isEmpty(paymentBillNo)) {
            return new ArrayList<>(0);
        }
        Condition condition = new Condition(PaymentBillDetail.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(PAYMENT_BILL_NO, paymentBillNo);
        criteria.andEqualTo(DEL_FLG, false);
        List<PaymentBillDetail> details = mapper.selectByCondition(condition);
        return convertDTOList(details);
    }

    @Override
    public PaymentBillDetailDTO findByDetailCode(String detailCode) {
        if (CsStringUtils.isEmpty(detailCode)) {
            return null;
        }
        Condition condition = newCondition();
        condition.createCriteria()
                .andEqualTo("paymentBillDetailCode", detailCode)
                .andEqualTo(DEL_FLG, false);
        List<PaymentBillDetail> billDetails = mapper.selectByCondition(condition);
        if (CollectionUtils.isEmpty(billDetails)) {
            return null;
        }
        return convertDTO(billDetails.get(0));
    }

    @Override
    public void updateByPaymentBill(PaymentBillDTO paymentBillDTO, String operatorId) {
        Assert.notNull(paymentBillDTO, "paymentBillDTO");
        if (CsStringUtils.isEmpty(paymentBillDTO.getPaymentBillNo())) {
            return;
        }

        boolean finish = PaymentStatusEnum.PAY_SUCCESS.getCode().equals(paymentBillDTO.getStatus()) ||
                PaymentStatusEnum.PAY_FREEZE.getCode().equals(paymentBillDTO.getStatus());
        List<PaymentBillDetailDTO> dtoList = paymentBillDTO.getDetailDTOList();
        if (!CollectionUtils.isEmpty(dtoList)) {
            for (PaymentBillDetailDTO detailDTO : dtoList) {
                if (CsStringUtils.isEmpty(detailDTO.getPaymentBillDetailId())) {
                    log.info("更新 PaymentBillDetail 失败, 缺少主键 ID, paymentBillNo: {}", paymentBillDTO.getPaymentBillNo());
                    continue;
                }
                detailDTO.setStatus(paymentBillDTO.getStatus());
                if (finish) {
                    detailDTO.setActualPayAmount(detailDTO.getPayAmount());
                }
                PaymentBillDetail detail = convertVO(detailDTO);
                setOperInfo(detail, operatorId, false);
                updateSelective(detail);
            }
        } else {
            PaymentBillDetail detail = new PaymentBillDetail();
            BeanUtils.copyProperties(paymentBillDTO, detail);
            detail.setPayeeMemberId(null);
            detail.setPayeeMemberName(null);
            detail.setPayAmount(null);
            detail.setActualPayAmount(null);
            detail.setCurrency(null);
            detail.setServiceFee(null);
            detail.setActualPayAmount(null);
            detail.setOtherFee(null);
            detail.setCreateTime(null);
            detail.setCreateUser(null);

            Condition condition = newCondition();
            condition.createCriteria()
                    .andEqualTo(PAYMENT_BILL_NO, paymentBillDTO.getPaymentBillNo())
                    .andEqualTo(DEL_FLG, false);

            setOperInfo(detail, operatorId, false);
            mapper.updateByConditionSelective(detail, condition);
        }
    }

    @Override
    public void updateStatusByPaymentBillNo(String paymentBillNo, String status, String operatorId) {
        if (CsStringUtils.isEmpty(paymentBillNo)) {
            return;
        }
        PaymentBillDetail detail = new PaymentBillDetail();
        detail.setStatus(status);
        setOperInfo(detail, operatorId, false);

        Condition condition = newCondition();
        condition.createCriteria()
                .andEqualTo(PAYMENT_BILL_NO, paymentBillNo)
                .andEqualTo(DEL_FLG, false);

        mapper.updateByConditionSelective(detail, condition);
    }

    @Override
    public void updateByDTO(PaymentBillDetailDTO paymentBillDetailDTO, String operatorId) {
        Assert.notNull(paymentBillDetailDTO, "paymentBillDetailDTO");

        PaymentBillDetail detail = convertVO(paymentBillDetailDTO);
        setOperInfo(detail, operatorId, false);
        if (CsStringUtils.isEmpty(detail.getPaymentBillDetailId())) {
            if (CsStringUtils.isNotEmpty(detail.getPaymentBillNo()) && CsStringUtils.isNotEmpty(detail.getPayeeMemberId())) {
                Condition condition = newCondition();
                condition.createCriteria()
                        .andEqualTo(PAYMENT_BILL_NO, detail.getPaymentBillNo())
                        .andEqualTo("payeeMemberId", detail.getPayeeMemberId())
                        .andEqualTo(DEL_FLG, false);
                mapper.updateByConditionSelective(detail, condition);
            } else {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "更新出错: 缺少条件 " + detail);
            }
        } else {
            mapper.updateByPrimaryKeySelective(detail);
        }
    }

    @Override
    public void save(PaymentBillDetailDTO detailDTO, String operatorId) {
        detailDTO.setPaymentBillDetailCode(codeGenerator.incrementPayCode());
        detailDTO.setPaymentBillDetailId(null);
        PaymentBillDetail detail = save(convertVO(detailDTO), operatorId);
        detailDTO.setPaymentBillDetailId(detail.getPaymentBillDetailId());
    }

    @Override
    public PaymentBillDetailDTO convert2DTO(PaymentRequirementDetailDTO requirementDetailDTO, PaymentBillDTO paymentBillDTO) {
        Assert.notNull(requirementDetailDTO.getPayAmount(), "payAmount");

        PaymentBillDetailDTO detailDTO = new PaymentBillDetailDTO();
        BeanUtils.copyProperties(paymentBillDTO, detailDTO);
        detailDTO.setPayeeMemberId(requirementDetailDTO.getPayeeMemberId());
        detailDTO.setPayeeMemberName(requirementDetailDTO.getPayeeMemberName());
        detailDTO.setPayAmount(new BigDecimal(requirementDetailDTO.getPayAmount()));
        detailDTO.setActualPayAmount(BigDecimal.ZERO);
        return detailDTO;
    }

    @Override
    public void deleteDb(String detailId) {
        mapper.deleteByPrimaryKey(detailId);
    }

    private PaymentBillDetail convertVO(PaymentBillDetailDTO detailDTO) {
        PaymentBillDetail vo = new PaymentBillDetail();
        BeanUtils.copyProperties(detailDTO, vo);
        return vo;
    }

    private PaymentBillDetailDTO convertDTO(PaymentBillDetail detail) {
        PaymentBillDetailDTO dto = new PaymentBillDetailDTO();
        BeanUtils.copyProperties(detail, dto);
        return dto;
    }

    private List<PaymentBillDetailDTO> convertDTOList(List<PaymentBillDetail> details) {
        if (CollectionUtils.isEmpty(details)) {
            return new ArrayList<>(0);
        } else {
            return details.stream().map(this::convertDTO).toList();
        }
    }
}
