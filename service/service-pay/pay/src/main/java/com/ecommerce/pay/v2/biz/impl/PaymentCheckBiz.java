package com.ecommerce.pay.v2.biz.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ecommerce.pay.v2.biz.IPaymentCheckBiz;
import com.ecommerce.pay.v2.dao.mapper.PaymentBillMapper;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class PaymentCheckBiz implements IPaymentCheckBiz {
	@Autowired
	private PaymentBillMapper paymentBillMapper;

	@Override
	public void checkPaymentTimeout() {
		log.info(" 检查支付单是否过期 更新支付单状态");
		int updateNum = paymentBillMapper.updatePaymentBillTimeoutStatus();
		log.info(" 检查支付单是否过期 共更新支付单状态 {}", updateNum);
	}

}
