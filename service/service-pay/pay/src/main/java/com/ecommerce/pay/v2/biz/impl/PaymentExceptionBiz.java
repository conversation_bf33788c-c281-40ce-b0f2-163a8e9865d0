package com.ecommerce.pay.v2.biz.impl;

import com.ecommerce.common.service.common.BaseBiz;
import com.ecommerce.common.service.common.uuid.UUIDGenerator;
import com.ecommerce.common.utils.BeanConvertUtils;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.pay.api.v2.dto.PaymentExceptionDTO;
import com.ecommerce.pay.api.v2.dto.electricInvoiceAccount.PageQuery;
import com.ecommerce.pay.api.v2.enums.ExceptionStatusEnum;
import com.ecommerce.pay.v2.biz.IPaymentExceptionBiz;
import com.ecommerce.pay.v2.dao.mapper.PaymentExceptionMapper;
import com.ecommerce.pay.v2.dao.vo.PaymentException;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.Collection;

/**
 * <AUTHOR>
 * @created 10:52 24/09/2019
 * @description TODO
 */
@Slf4j
@Service
public class PaymentExceptionBiz extends BaseBiz<PaymentException> implements IPaymentExceptionBiz {

    @Autowired
    private PaymentExceptionMapper mapper;

    @Autowired
    private UUIDGenerator uuidGenerator;

    @Override
    public void create(PaymentExceptionDTO dto, String operatorId) {
        dto.setId(uuidGenerator.gain());
        dto.setStatus(ExceptionStatusEnum.WAIT_DEAL.getStatus());
        PaymentException convert = BeanConvertUtils.convert(dto, PaymentException.class);
        setOperInfo(convert, operatorId, true);
        mapper.insert(convert);
    }

    @Override
    public void update(PaymentExceptionDTO dto, String operatorId) {
        PaymentException convert = BeanConvertUtils.convert(dto, PaymentException.class);
        setOperInfo(convert, operatorId, false);
        mapper.updateByPrimaryKeySelective(convert);
    }

    @Override
    public PaymentExceptionDTO findById(String id) {
        PaymentException exception = mapper.selectByPrimaryKey(id);
        if (exception == null || Boolean.TRUE.equals(exception.getDelFlag())) {
            return null;
        }
        return BeanConvertUtils.convert(exception, PaymentExceptionDTO.class);
    }

    @Override
    public void splitSuccess(Collection<String> objIdList) {
        if(CollectionUtils.isEmpty(objIdList)){
            return;
        }
        PaymentException exception = new PaymentException();
        exception.setStatus(ExceptionStatusEnum.SUCCESS.getStatus());
        Condition condition = new Condition(PaymentException.class);
        Example.Criteria criteria = condition.createCriteria();
        if(objIdList.size() == 1 ){
            criteria.andEqualTo("objId",objIdList.iterator().next());
        }else{
            criteria.andIn("objId",objIdList);
        }
        criteria.andEqualTo("status",ExceptionStatusEnum.FAIL.getStatus());
        mapper.updateByConditionSelective(exception,condition);
    }

    @Override
    public PageInfo<PaymentExceptionDTO> pageByObjectId(PageQuery<PaymentExceptionDTO> query) {
        PaymentExceptionDTO queryDTO = query.getQueryDTO();

        Condition condition = new Condition(PaymentException.class);
        Example.Criteria criteria = condition.createCriteria();

        if (CsStringUtils.isNotBlank(queryDTO.getObjId())) {
            criteria.andEqualTo("objId", queryDTO.getObjId());
        }

        if (CsStringUtils.isNotBlank(queryDTO.getOperateType())) {
            criteria.andEqualTo("operateType", queryDTO.getOperateType());
        }

        if (CsStringUtils.isNotBlank(queryDTO.getStatus())) {
            criteria.andEqualTo("status", queryDTO.getStatus());
        }

        if (CsStringUtils.isNotBlank(queryDTO.getOrderNo())) {
            criteria.andLike("orderNo", "%" + queryDTO.getOrderNo() + "%");
        }

        if (queryDTO.getQueryStartTime() != null && queryDTO.getQueryEndTime() != null) {
            criteria.andBetween("createTime", queryDTO.getQueryStartTime(), queryDTO.getQueryEndTime());
        } else if (queryDTO.getQueryStartTime() != null) {
            criteria.andGreaterThanOrEqualTo("createTime", queryDTO.getQueryStartTime());
        } else if (queryDTO.getQueryEndTime() != null) {
            criteria.andLessThanOrEqualTo("createTime", queryDTO.getQueryEndTime());
        }

        condition.orderBy("createTime").desc();
        PageInfo<PaymentException> p = new PageInfo<>();
        p.setPageNum(query.getPageNum());
        p.setPageSize(query.getPageSize());

        Page<PaymentException> page = page(condition, p);
        Page<PaymentExceptionDTO> pages = BeanConvertUtils.convertPage(page, PaymentExceptionDTO.class);
        return new PageInfo<>(pages);
    }
}
