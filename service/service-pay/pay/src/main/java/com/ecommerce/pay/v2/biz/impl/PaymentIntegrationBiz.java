package com.ecommerce.pay.v2.biz.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.service.common.BaseBiz;
import com.ecommerce.pay.api.exception.PayCode;
import com.ecommerce.pay.api.v2.dto.BizConfigDTO;
import com.ecommerce.pay.api.v2.dto.PaymentBillInfo;
import com.ecommerce.pay.api.v2.dto.PaymentNotifyDTO;
import com.ecommerce.pay.api.v2.dto.PaymentRequirementDTO;
import com.ecommerce.pay.v2.biz.IPaymentIntegrationBiz;
import com.ecommerce.pay.v2.dao.vo.BizConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 05/12/2018 15:29
 */
@Slf4j
@Service
public class PaymentIntegrationBiz extends BaseBiz<BizConfig> implements IPaymentIntegrationBiz {

    @Autowired
    private RestTemplate restTemplate;

    public static final String BIZ_CODE = "bizCode";
    public static final String DEL_FLG = "delFlg";
    public static final String BIZ_STATUS = "bizStatus";
    public static final String LOG_NOT_FUND = "通过code未查询到可用的业务方信息";

    private String sendRequest(String url, Object data) {
        return restTemplate.postForObject(url, data, String.class);
    }

    @Override
    public PaymentRequirementDTO getPaymentRequirement(String bizId, String paymentBizCode, String operator) {

        Condition condition = new Condition(BizConfig.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(BIZ_CODE, paymentBizCode);
        criteria.andEqualTo(DEL_FLG, 0);
        criteria.andEqualTo(BIZ_STATUS, 1);
        List<BizConfig> voList = findByCondition(condition);
        String url;
        if (CollectionUtils.isEmpty(voList)) {
            log.info(LOG_NOT_FUND);
            return null;
        } else {
            url = voList.get(0).getQueryBizUrl();
        }
        //调用远程方法获取配置项
        PaymentRequirementDTO paymentRequirementDTO = null;

        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            HttpEntity<String> formEntity = new HttpEntity<>("bizId=" + bizId + "&operator=" + operator, headers);
            log.info("restTemplate --> getPaymentRequirement url: {}, param: {}", url, formEntity.getBody());

            String json = sendRequest(url, formEntity);

            log.info("restTemplate --> getPaymentRequirement result: {}", json);

            //反射调用方法，methodParam:方法参数
            paymentRequirementDTO = JSON.parseObject(json, PaymentRequirementDTO.class);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BizException(PayCode.CALL_SERVICE_ERROR);
        }
        return paymentRequirementDTO;
    }

    @Override
    public List<String> getBizPaymentChannel(String bizId, String paymentBizCode, String operator) {

        Condition condition = new Condition(BizConfig.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(BIZ_CODE, paymentBizCode);
        criteria.andEqualTo(DEL_FLG, 0);
        criteria.andEqualTo(BIZ_STATUS, 1);
        List<BizConfig> voList = findByCondition(condition);
        String url = "";
        if (CollectionUtils.isEmpty(voList)) {
            log.info(LOG_NOT_FUND);
            return Collections.emptyList();
        } else {
            url = voList.get(0).getQueryChannelUrl();
        }
        //调用远程方法获取配置项
        List<String> optionList;

        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            HttpEntity<String> formEntity = new HttpEntity<>("bizId=" + bizId + "&operator=" + operator, headers);

            String json = sendRequest(url, formEntity);
            log.info("restTemplate --> getBizPaymentChannel result: {}", json);
            //反射调用方法，methodParam:方法参数
            optionList = JSONArray.parseArray(json, String.class);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new BizException(PayCode.CALL_SERVICE_ERROR);
        }
        return optionList;
    }

    @Override
    public void paymentResultCallback(PaymentNotifyDTO paymentNotifyDTO) {
        Condition condition = new Condition(BizConfig.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(BIZ_CODE, paymentNotifyDTO.getPaymentBizCode());
        criteria.andEqualTo(DEL_FLG, 0);
        criteria.andEqualTo(BIZ_STATUS, 1);
        List<BizConfig> voList = findByCondition(condition);
        String url = "";
        if (CollectionUtils.isEmpty(voList)) {
            log.info(LOG_NOT_FUND);
            throw new BizException(BasicCode.DATA_NOT_EXIST);
        } else {
            url = voList.get(0).getNotifyUrl();
        }

        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            String value = JSON.toJSONString(paymentNotifyDTO);
            log.info("notifyUrl:{},通知内容：{}", url, value);
            HttpEntity<String> entity = new HttpEntity<>(value, headers);

            sendRequest(url, entity);
            //反射调用方法，methodParam:方法参数
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new BizException(PayCode.CALL_SERVICE_ERROR);
        }
    }

    @Override
    public void updatePaymentBillInfo(PaymentBillInfo paymentBillInfo) {
        Condition condition = new Condition(BizConfig.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(BIZ_CODE, paymentBillInfo.getPaymentBizCode());
        criteria.andEqualTo(DEL_FLG, 0);
        criteria.andEqualTo(BIZ_STATUS, 1);
        List<BizConfig> voList = findByCondition(condition);
        String url = "";
        if (CollectionUtils.isEmpty(voList)) {
            log.info(LOG_NOT_FUND);
            throw new BizException(BasicCode.DATA_NOT_EXIST);
        } else {
            url = voList.get(0).getUpdateBillUrl();
        }

        log.info("开始执行");
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            String value = JSON.toJSONString(paymentBillInfo);
            log.info("updateBillUrl:{},通知内容：{}", url, value);
            HttpEntity<String> entity = new HttpEntity<>(value, headers);

            log.info("发起通知");
            sendRequest(url, entity);
            log.info("发起通知-成功");
            //反射调用方法，methodParam:方法参数
        } catch (Exception e) {
            log.info("发起通知-失败");
            log.error(e.getMessage());
            throw new BizException(PayCode.CALL_SERVICE_ERROR);
        }
    }

    @Override
    public BizConfigDTO findByCode(String code) {
        Condition condition = new Condition(BizConfig.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo(BIZ_CODE, code);
        criteria.andEqualTo(DEL_FLG, 0);
        criteria.andEqualTo(BIZ_STATUS, 1);
        List<BizConfig> voList = findByCondition(condition);
        if (CollectionUtils.isEmpty(voList)) {
            log.info(LOG_NOT_FUND);
            throw new BizException(BasicCode.DATA_NOT_EXIST);
        } else {
            return convertToDTO(voList.get(0));
        }
    }

    private BizConfigDTO convertToDTO(BizConfig bizConfig) {
        if (bizConfig == null) {
            return null;
        }
        BizConfigDTO bizConfigDTO = new BizConfigDTO();
        BeanUtils.copyProperties(bizConfig, bizConfigDTO);
        return bizConfigDTO;
    }
}
