package com.ecommerce.pay.v2.biz.impl;

import com.alibaba.fastjson.JSON;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.member.api.service.IMemberService;
import com.ecommerce.open.api.dto.pinganjz.ClearingCorpInfoCheckRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.ClearingCorpInfoSendRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.query.RegisterBehaviorRecordRequestDTO;
import com.ecommerce.open.api.service.IPingAnJZConnectorService;
import com.ecommerce.pay.api.exception.PayCode;
import com.ecommerce.pay.api.v2.dto.MemberChannelDTO;
import com.ecommerce.pay.api.v2.enums.ChannelCodeEnum;
import com.ecommerce.pay.api.v2.enums.ChannelPaymentTypeEnum;
import com.ecommerce.pay.config.PingAnJZProperties;
import com.ecommerce.pay.v2.biz.IMemberChannelBiz;
import com.ecommerce.pay.v2.biz.IPingAnBiz;
import com.ecommerce.pay.v2.channel.adapter.pinganjz.PingAnJZAdapter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class PingAnBiz implements IPingAnBiz
{
    private static Logger logger = LoggerFactory.getLogger(PingAnBiz.class);

    @Autowired
    private IMemberChannelBiz memberChannelBiz;

    @Autowired
    private IMemberService memberService;

    @Autowired
    private IPingAnJZConnectorService pingAnJZConnectorService;

    @Autowired
    public PingAnJZAdapter pingAnJZAdapter;

    @Autowired
    private PingAnJZProperties properties;

    @Override
    public Map<String, String> clearingCorpInfoSendMsgCode(String memberId, String legalName, String legalCertificateCode, String clickTime, String remoteIp, String macAddress, String channel)
    {
        MemberChannelDTO memberChannel = memberChannelBiz.findByMemberIdAndChannelCode(memberId, ChannelCodeEnum.PINGANJZ.getCode(), ChannelPaymentTypeEnum.PAYEE);
        if(memberChannel == null)
        {
            throw new BizException(PayCode.UNDEFINED_ERROR, "未查询到用户渠道信息");
        }

        ClearingCorpInfoSendRequestDTO requestDTO = new ClearingCorpInfoSendRequestDTO();
        pingAnJZAdapter.setPublicRequestParam(requestDTO);

        requestDTO.setFundSummaryAcctNo(properties.getFundSummaryAcctNo());
        requestDTO.setSubAcctNo(memberChannel.getExtCustAcctId());
        requestDTO.setTranNetMemberCode(memberChannel.getMemberChannelCode());
        requestDTO.setMemberName(memberChannel.getAccountName());
        requestDTO.setMemberGlobalType(memberChannel.getIdType());
        requestDTO.setMemberGlobalId(memberChannel.getIdCode());

        // 工商个体户
        if("73".equals(memberChannel.getIdType()))
        {
            // 这里写企业
            requestDTO.setIndivBusinessFlag("2");
            requestDTO.setShopId(memberChannel.getMemberChannelCode());
            requestDTO.setShopName(memberChannel.getAccountName());


            requestDTO.setRepFlag("2");
            requestDTO.setReprClientName(legalName);
            requestDTO.setReprGlobalType("1");
            requestDTO.setReprGlobalId(legalCertificateCode);
        }

        requestDTO.setOpClickTime(clickTime);
        requestDTO.setIpAddress(remoteIp);
        requestDTO.setMacAddress(macAddress);
        requestDTO.setSignChannel(channel);

        logger.info("========会员补录法人信息-下发短信验证码========");
        logger.info(JSON.toJSONString(requestDTO));
        Map<String, String> rest = pingAnJZConnectorService.clearingCorpInfoSendMsgCode(requestDTO);
        logger.info(JSON.toJSONString(rest));

        return rest;
    }

    @Override
    public Map<String, String> clearingCorpInfoCheckMsgCode(String memberId, String checkCode)
    {
        MemberChannelDTO memberChannel = memberChannelBiz.findByMemberIdAndChannelCode(memberId, ChannelCodeEnum.PINGANJZ.getCode(), ChannelPaymentTypeEnum.PAYEE);
        if(memberChannel == null)
        {
            throw new BizException(PayCode.UNDEFINED_ERROR, "未查询到用户渠道信息");
        }

        ClearingCorpInfoCheckRequestDTO requestDTO = new ClearingCorpInfoCheckRequestDTO();
        pingAnJZAdapter.setPublicRequestParam(requestDTO);

        requestDTO.setFundSummaryAcctNo(properties.getFundSummaryAcctNo());
        requestDTO.setSubAcctNo(memberChannel.getExtCustAcctId());
        requestDTO.setTranNetMemberCode(memberChannel.getMemberChannelCode());
        requestDTO.setMessageCheckCode(checkCode);

        logger.info("========会员补录法人信息-回填短信验证码========");
        logger.info(JSON.toJSONString(requestDTO));
        Map<String, String> rest = pingAnJZConnectorService.clearingCorpInfoCheckMsgCode(requestDTO);
        logger.info(JSON.toJSONString(rest));

        return rest;
    }

    @Override
    public Map<String, Object> registerBehaviorRecordInfo(String memberId, String functionFlag, String clickTime, String remoteIp, String macAddress, String channel)
    {
        MemberChannelDTO memberChannel = memberChannelBiz.findByMemberIdAndChannelCode(memberId, ChannelCodeEnum.PINGANJZ.getCode(), ChannelPaymentTypeEnum.PAYEE);
        if(memberChannel == null)
        {
            throw new BizException(PayCode.UNDEFINED_ERROR, "未查询到用户渠道信息");
        }

        RegisterBehaviorRecordRequestDTO requestDTO = new RegisterBehaviorRecordRequestDTO();
        pingAnJZAdapter.setPublicRequestParam(requestDTO);

        requestDTO.setFundSummaryAcctNo(properties.getFundSummaryAcctNo());
        requestDTO.setSubAcctNo(memberChannel.getExtCustAcctId());
        requestDTO.setTranNetMemberCode(memberChannel.getMemberChannelCode());

        requestDTO.setFunctionFlag(functionFlag);
        requestDTO.setOpClickTime(clickTime);
        requestDTO.setIpAddress(remoteIp);
        requestDTO.setMacAddress(macAddress);
        requestDTO.setSignChannel(channel);

        if(!"1".equals(functionFlag) && !"2".equals(functionFlag))
        {
            throw new BizException(PayCode.UNDEFINED_ERROR, "功能标志错误");
        }

        if("1".equals(functionFlag) && (clickTime == null || remoteIp == null || macAddress == null || channel == null))
        {
            throw new BizException(PayCode.UNDEFINED_ERROR, "必填参数不能未空");
        }

        logger.info("========登记行为记录信息========");
        logger.info(JSON.toJSONString(requestDTO));
        Map<String, Object> rest = pingAnJZConnectorService.registerBehaviorRecordInfo(requestDTO);
        logger.info(JSON.toJSONString(rest));

        return rest;
    }
}
