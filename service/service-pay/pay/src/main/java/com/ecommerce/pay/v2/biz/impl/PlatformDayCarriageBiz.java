package com.ecommerce.pay.v2.biz.impl;

import com.alibaba.fastjson.JSON;
import com.ecommerce.common.service.common.BaseBiz;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.pay.api.v2.dto.driver.PlatformSummaryInfoDTO;
import com.ecommerce.pay.api.v2.dto.driver.PlatformSummaryInfoQueryDTO;
import com.ecommerce.pay.api.v2.dto.electricInvoiceAccount.PageQuery;
import com.ecommerce.pay.api.v2.enums.PaymentStatusEnum;
import com.ecommerce.pay.v2.biz.IPlatformDayCarriageBiz;
import com.ecommerce.pay.v2.dao.mapper.PlatformDayCarriageMapper;
import com.ecommerce.pay.v2.dao.vo.PlatformDayCarriage;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PlatformDayCarriageBiz extends BaseBiz<PlatformDayCarriage> implements IPlatformDayCarriageBiz {

    @Autowired
    private PlatformDayCarriageMapper platformDayCarriageMapper;

    @Override
    public PageInfo<PlatformSummaryInfoDTO> pageDriverCostSummarizeToPlatform(PageQuery<PlatformSummaryInfoQueryDTO> dto) {
        log.info("pageDriverCostSummarizeToPlatform: {}", JSON.toJSONString(dto));
        Condition condition = new Condition(PlatformDayCarriage.class);
        Example.Criteria criteria = condition.createCriteria();
        if (CsStringUtils.isNotBlank(dto.getQueryDTO().getDaysBegin())) {
            criteria.andGreaterThanOrEqualTo("day",dto.getQueryDTO().getDaysBegin());
        }
        if (CsStringUtils.isNotBlank(dto.getQueryDTO().getDaysEnd())) {
            criteria.andLessThanOrEqualTo("day",dto.getQueryDTO().getDaysEnd());
        }
        if (CsStringUtils.isNotBlank(dto.getQueryDTO().getPayNumber())) {
            criteria.andLike("payNumber","%"+dto.getQueryDTO().getPayNumber()+"%");
        }
        if (CsStringUtils.isNotBlank(dto.getQueryDTO().getPayStatus())) {
            criteria.andLessThanOrEqualTo("payStatus",dto.getQueryDTO().getPayStatus());
        }
        criteria.andEqualTo("delFlg",false);
        condition.orderBy("day").desc();

        PageInfo<PlatformDayCarriage> pageInfo = new PageInfo<>();
        pageInfo.setPageSize(dto.getPageSize());
        pageInfo.setPageNum(dto.getPageNum());
        PageInfo<PlatformDayCarriage> driverDayCarriagePageInfo = pageInfo(condition, pageInfo);

        PageInfo<PlatformSummaryInfoDTO> result = new PageInfo<>(Lists.newArrayList());
        BeanUtils.copyProperties(driverDayCarriagePageInfo,result,"list");
        result.setList(Lists.newArrayList());

        if(CollectionUtils.isNotEmpty(driverDayCarriagePageInfo.getList())){
            for (PlatformDayCarriage item : driverDayCarriagePageInfo.getList()) {
                PlatformSummaryInfoDTO itemDTO = new PlatformSummaryInfoDTO();
                BeanUtils.copyProperties(item,itemDTO);
                itemDTO.setPayStatusName(PaymentStatusEnum.getMessageByCode(item.getPayStatus()));
                result.getList().add(itemDTO);
            }
        }
        return result;
    }

    @Override
    public PlatformDayCarriage findByDay(String day) {
        if (CsStringUtils.isBlank(day)) {
            return null;
        }
        Condition condition = new Condition(PlatformDayCarriage.class);
        condition.createCriteria().andEqualTo("day",day).andEqualTo("delFlg",false);
        List<PlatformDayCarriage> list = findByCondition(condition);
        return list == null || list.isEmpty() ? null : list.get(0);
    }

    @Override
    public List<PlatformDayCarriage> findByIds(List<String> ids) {
        if( CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        Condition condition = new Condition(PlatformDayCarriage.class);
        if( ids.size() == 1 ){
            condition.createCriteria().andEqualTo("platformDayCarriageId",ids.iterator().next()).andEqualTo("delFlg",false);
        }else{
            condition.createCriteria().andIn("platformDayCarriageId",ids).andEqualTo("delFlg",false);
        }
        return findByCondition(condition);
    }

    @Override
    public List<PlatformDayCarriage> findByPayNumber(String payNumber) {
        if (CsStringUtils.isBlank(payNumber)) {
            return Lists.newArrayList();
        }
        Condition condition = new Condition(PlatformDayCarriage.class);
        condition.createCriteria().andLike("payNumber","%"+payNumber+"%").andEqualTo("delFlg",false);
        return findByCondition(condition);
    }

    @Override
    public int updatePayStatusInPayIng(List<String> ids, String operator, String payNumber,String tradeBillNo) {
        String idsStr = ids.stream().map(item->"'"+item+"'").collect(Collectors.joining(","));
        return platformDayCarriageMapper.updatePayStatusInPayIng(idsStr,payNumber,tradeBillNo,operator);
    }
}
