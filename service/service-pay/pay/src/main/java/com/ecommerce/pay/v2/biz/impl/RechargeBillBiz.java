package com.ecommerce.pay.v2.biz.impl;

import com.ecommerce.common.service.common.BaseBiz;
import com.ecommerce.common.service.common.bsid.CommonBusinessIdGenerator;
import com.ecommerce.common.utils.BeanConvertUtils;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.pay.api.v2.dto.bill.RechargeBillDTO;
import com.ecommerce.pay.v2.biz.IRechargeBillBiz;
import com.ecommerce.pay.v2.dao.mapper.RechargeBillMapper;
import com.ecommerce.pay.v2.dao.vo.RechargeBill;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Condition;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @created 9:54 03/09/2019
 * @description TODO
 */
@Service
public class RechargeBillBiz extends BaseBiz<RechargeBill> implements IRechargeBillBiz {

    @Autowired
    private RechargeBillMapper mapper;

    @Autowired
    private CommonBusinessIdGenerator codeGenerator;

    @Override
    public void create(RechargeBillDTO billDTO, String operatorId) {
        billDTO.setRechargeBillId(null);
        billDTO.setRechargeBillNo(codeGenerator.incrementRechargeCode());
        RechargeBill bill = BeanConvertUtils.convert(billDTO, RechargeBill.class);
        save(bill, operatorId);
        billDTO.setRechargeBillId(bill.getRechargeBillId());
    }

    @Override
    public Optional<RechargeBillDTO> findByNo(String billNo) {
        if (CsStringUtils.isEmpty(billNo)) {
            return Optional.empty();
        }
        RechargeBill bill = new RechargeBill();
        bill.setRechargeBillNo(billNo);
        bill.setDelFlg(false);
        List<RechargeBill> select = mapper.select(bill);
        if (CollectionUtils.isEmpty(select)) {
            return Optional.empty();
        }
        return Optional.ofNullable(BeanConvertUtils.convert(select.get(0), RechargeBillDTO.class));
    }

    @Override
    public Optional<RechargeBillDTO> findById(String billId) {
        if (CsStringUtils.isEmpty(billId)) {
            return Optional.empty();
        }
        RechargeBill bill = mapper.selectByPrimaryKey(billId);
        return Optional.ofNullable(BeanConvertUtils.convert(bill, RechargeBillDTO.class));
    }

    @Override
    public void update(RechargeBillDTO billDTO, String operatorId) {
        if (CsStringUtils.isEmpty(billDTO.getRechargeBillId())) {
            return;
        }
        RechargeBill bill = BeanConvertUtils.convert(billDTO, RechargeBill.class);
        Condition condition = new Condition(RechargeBill.class);
        condition.createCriteria()
                .andEqualTo("rechargeBillId", billDTO.getRechargeBillId())
                .andEqualTo("delFlg", false);
        setOperInfo(bill, operatorId, false);
        mapper.updateByConditionSelective(bill, condition);
    }

    @Override
    public void updateStatus(String id, String status, String operatorId) {
        if (CsStringUtils.isBlank(id)) {
            return;
        }
        RechargeBill bill = new RechargeBill();
        bill.setStatus(status);
        Condition condition = new Condition(RechargeBill.class);
        condition.createCriteria()
                .andEqualTo("rechargeBillId", id)
                .andEqualTo("delFlg", false);
        setOperInfo(bill, operatorId, false);
        mapper.updateByConditionSelective(bill, condition);
    }

    @Override
    public Optional<RechargeBillDTO> findByExtBizNo(String extBizNo) {
        if (CsStringUtils.isBlank(extBizNo)) {
            return Optional.empty();
        }
        RechargeBill bill = new RechargeBill();
        bill.setExtBizNo(extBizNo);
        bill.setDelFlg(false);
        List<RechargeBill> select = mapper.select(bill);
        if (CollectionUtils.isEmpty(select)) {
            return Optional.empty();
        }
        return Optional.ofNullable(BeanConvertUtils.convert(select.get(0), RechargeBillDTO.class));
    }
}
