package com.ecommerce.pay.v2.biz.impl;

import com.ecommerce.common.service.common.BaseBiz;
import com.ecommerce.common.service.common.bsid.CommonBusinessIdGenerator;
import com.ecommerce.common.utils.BeanConvertUtils;
import com.github.pagehelper.page.PageMethod;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.pay.api.v2.dto.bill.RefundBillDTO;
import com.ecommerce.pay.api.v2.dto.bill.RefundBillDetailDTO;
import com.ecommerce.pay.api.v2.enums.PaymentStatusEnum;
import com.ecommerce.pay.v2.biz.IRefundBillBiz;
import com.ecommerce.pay.v2.biz.IRefundBillDetailBiz;
import com.ecommerce.pay.v2.dao.mapper.RefundBillMapper;
import com.ecommerce.pay.v2.dao.vo.RefundBill;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @created 14:19 04/09/2019
 * @description TODO
 */
@Slf4j
@Service
public class RefundBillBiz extends BaseBiz<RefundBill> implements IRefundBillBiz {

    @Autowired
    private RefundBillMapper refundBillMapper;

    @Autowired
    private CommonBusinessIdGenerator codeGenerator;

    @Autowired
    private IRefundBillDetailBiz detailBiz;

    @Override
    public void create(RefundBillDTO billDTO, String operatorId) {
        billDTO.setRefundBillId(null);
        billDTO.setRefundBillNo(codeGenerator.incrementRefundCode());
        RefundBill refundBill = BeanConvertUtils.convert(billDTO, RefundBill.class);
        save(refundBill, operatorId);
        billDTO.setRefundBillId(refundBill.getRefundBillId());
        createDetail(billDTO, operatorId);
    }

    @Override
    public Optional<RefundBillDTO> findByNo(String billNo) {
        if (CsStringUtils.isBlank(billNo)) {
            return Optional.empty();
        }
        RefundBill refundBill = newVo();
        refundBill.setRefundBillNo(billNo);
        refundBill.setDelFlg(false);
        List<RefundBill> select = refundBillMapper.select(refundBill);
        if (CollectionUtils.isEmpty(select)) {
            return Optional.empty();
        }
        RefundBillDTO refundBillDTO = BeanConvertUtils.convert(select.get(0), RefundBillDTO.class);
        List<RefundBillDetailDTO> detailDTOList = detailBiz.findRefundNo(refundBillDTO.getRefundBillNo());
        refundBillDTO.setDetailDTOList(detailDTOList);
        return Optional.of(refundBillDTO);
    }

    @Override
    public Optional<RefundBillDTO> findById(String billId) {
        if (CsStringUtils.isBlank(billId)) {
            return Optional.empty();
        }
        RefundBill refundBill = refundBillMapper.selectByPrimaryKey(billId);
        if (refundBill == null || Boolean.TRUE.equals(refundBill.getDelFlg())) {
            return Optional.empty();
        }
        RefundBillDTO refundBillDTO = BeanConvertUtils.convert(refundBill, RefundBillDTO.class);
        List<RefundBillDetailDTO> detailDTOList = detailBiz.findRefundNo(refundBillDTO.getRefundBillNo());
        refundBillDTO.setDetailDTOList(detailDTOList);
        return Optional.of(refundBillDTO);
    }

    @Override
    public void update(RefundBillDTO billDTO, String operatorId) {
        RefundBill refundBill = BeanConvertUtils.convert(billDTO, RefundBill.class);
        setOperInfo(refundBill, operatorId, false);
        updateSelective(refundBill);
        updateDetail(billDTO, operatorId);
    }

    @Override
    public void updateStatus(String id, String status, String operatorId) {
        if (CsStringUtils.isBlank(id)) {
            return;
        }
        RefundBill refundBill = refundBillMapper.selectByPrimaryKey(id);
        if (refundBill == null || Boolean.TRUE.equals(refundBill.getDelFlg())) {
            return;
        }
        refundBill.setStatus(status);
        setOperInfo(refundBill, operatorId, false);
        updateSelective(refundBill);

        RefundBillDTO billDTO = BeanConvertUtils.convert(refundBill, RefundBillDTO.class);
        List<RefundBillDetailDTO> detailDTOS = detailBiz.findRefundNo(refundBill.getRefundBillNo());
        billDTO.setDetailDTOList(detailDTOS);
        updateDetail(billDTO, operatorId);
    }

    private void updateDetail(RefundBillDTO billDTO, String operatorId) {
        boolean finish = PaymentStatusEnum.PAY_SUCCESS.getCode().equals(billDTO.getStatus());

        List<RefundBillDetailDTO> detailDTOList = billDTO.getDetailDTOList();
        if (!CollectionUtils.isEmpty(detailDTOList)) {
            for (RefundBillDetailDTO detailDTO : detailDTOList) {
                if (CsStringUtils.isBlank(detailDTO.getRefundBillDetailId())) {
                    continue;
                }
                detailDTO.setStatus(billDTO.getStatus());
                if (finish) {
                    detailDTO.setActualPayAmount(detailDTO.getPayAmount());
                }
                detailBiz.update(detailDTO, operatorId);
            }
        }
    }

    private RefundBill newVo() {
        return new RefundBill();
    }

    private RefundBillDTO newDto() {
        return new RefundBillDTO();
    }

    private void createDetail(RefundBillDTO billDTO, String operatorId) {
        List<RefundBillDetailDTO> detailDTOList = billDTO.getDetailDTOList();
        if (!CollectionUtils.isEmpty(detailDTOList)) {
            for (RefundBillDetailDTO detailDTO : detailDTOList) {
                detailDTO.setRefundBillNo(billDTO.getRefundBillNo());
                detailDTO.setStatus(billDTO.getStatus());
                detailBiz.create(detailDTO, operatorId);
            }
        }
    }

    @Override
    public Integer findCountByRelatePaymentNo(String paymentNo) {
        if (CsStringUtils.isBlank(paymentNo)) {
            return 0;
        }
        RefundBill refundBill = new RefundBill();
        refundBill.setRelatedPaymentBillNo(paymentNo);
        refundBill.setDelFlg(false);
        refundBill.setStatus(PaymentStatusEnum.PAY_SUCCESS.getCode());
        return refundBillMapper.selectCount(refundBill);
    }

    @Override
    public List<RefundBillDTO> findByOrderNo(String orderNo) {
        if (CsStringUtils.isBlank(orderNo)) {
            return Lists.newArrayList();
        }
        RefundBill refundBill = new RefundBill();
        refundBill.setOrderNo(orderNo);
        refundBill.setDelFlg(false);
        List<RefundBill> select = refundBillMapper.select(refundBill);
        if (CollectionUtils.isEmpty(select)) {
            return Lists.newArrayList();
        }
        return select.stream().map(i -> BeanConvertUtils.convert(i, RefundBillDTO.class)).toList();
    }

    @Override
    public Optional<RefundBillDTO> findByExtBizNo(String extBizNo) {
        if (CsStringUtils.isBlank(extBizNo)) {
            return Optional.empty();
        }
        RefundBill refundBill = newVo();
        refundBill.setExtBizNo(extBizNo);
        refundBill.setDelFlg(false);
        List<RefundBill> select = refundBillMapper.select(refundBill);
        if (CollectionUtils.isEmpty(select)) {
            return Optional.empty();
        }
        RefundBillDTO refundBillDTO = BeanConvertUtils.convert(select.get(0), RefundBillDTO.class);
        List<RefundBillDetailDTO> detailDTOList = detailBiz.findRefundNo(refundBillDTO.getRefundBillNo());
        refundBillDTO.setDetailDTOList(detailDTOList);
        return Optional.of(refundBillDTO);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @Override
    public void updateGneteReceivableTransInfo(RefundBillDetailDTO refundBillDetailDTO) {
        detailBiz.updateGneteReceivableTransInfo(refundBillDetailDTO);
    }

    @Override
    public List<RefundBill> findWithNoCallback(List<String> channelCodeEnumList, long timeStep) {
        if(CollectionUtils.isEmpty(channelCodeEnumList)){
            return Lists.newArrayList();
        }
        Condition condition = new Condition(RefundBill.class);
        Example.Criteria criteria = condition.createCriteria();
        if(!CollectionUtils.isEmpty(channelCodeEnumList)){
            if( channelCodeEnumList.size() == 1 ){
                criteria.andEqualTo("channelCode",channelCodeEnumList.get(0));
            }else {
                criteria.andIn("channelCode",channelCodeEnumList);
            }
        }
        criteria.andEqualTo("status",PaymentStatusEnum.PAY_ING.getCode())
                .andLessThan("createTime", new Date(System.currentTimeMillis() - timeStep))
                .andEqualTo("delFlg",false);

        PageInfo<RefundBill> pageInfo = PageMethod.startPage(1, 1000, false, false, false)
                .doSelectPageInfo(() -> refundBillMapper.selectByCondition(condition));
        return pageInfo == null ? Lists.newArrayList() : pageInfo.getList();
    }
}
