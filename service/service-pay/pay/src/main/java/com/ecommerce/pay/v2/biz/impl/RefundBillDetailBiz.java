package com.ecommerce.pay.v2.biz.impl;

import com.ecommerce.common.service.common.BaseBiz;
import com.ecommerce.common.service.common.bsid.CommonBusinessIdGenerator;
import com.ecommerce.common.utils.BeanConvertUtils;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.pay.api.v2.dto.bill.RefundBillDetailDTO;
import com.ecommerce.pay.v2.biz.IRefundBillDetailBiz;
import com.ecommerce.pay.v2.dao.mapper.RefundBillDetailMapper;
import com.ecommerce.pay.v2.dao.vo.RefundBillDetail;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Condition;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @created 14:37 04/09/2019
 * @description TODO
 */
@Slf4j
@Service
public class RefundBillDetailBiz extends BaseBiz<RefundBillDetail> implements IRefundBillDetailBiz {

    @Autowired
    private RefundBillDetailMapper mapper;

    @Autowired
    private CommonBusinessIdGenerator codeGenerator;

    @Override
    public void create(RefundBillDetailDTO billDTO, String operatorId) {
        billDTO.setRefundBillDetailId(null);
        billDTO.setRefundBillDetailCode(codeGenerator.incrementRefundCode());
        RefundBillDetail detail = BeanConvertUtils.convert(billDTO, RefundBillDetail.class);
        save(detail, operatorId);
        billDTO.setRefundBillDetailId(detail.getRefundBillDetailId());
    }

    @Override
    public Optional<RefundBillDetailDTO> findByNo(String billNo) {
        if (CsStringUtils.isBlank(billNo)) {
            return Optional.empty();
        }
        RefundBillDetail refundBill = new RefundBillDetail();
        refundBill.setRefundBillNo(billNo);
        refundBill.setDelFlg(false);
        List<RefundBillDetail> select = mapper.select(refundBill);
        if (CollectionUtils.isEmpty(select)) {
            return Optional.empty();
        }
        return Optional.ofNullable(BeanConvertUtils.convert(select.get(0), RefundBillDetailDTO.class));
    }

    @Override
    public Optional<RefundBillDetailDTO> findById(String billId) {
        if (CsStringUtils.isBlank(billId)) {
            return Optional.empty();
        }
        RefundBillDetail refundBill = mapper.selectByPrimaryKey(billId);
        if (refundBill == null || Boolean.TRUE.equals(refundBill.getDelFlg())) {
            return Optional.empty();
        }
        return Optional.ofNullable(BeanConvertUtils.convert(refundBill, RefundBillDetailDTO.class));
    }

    @Override
    public void update(RefundBillDetailDTO billDTO, String operatorId) {
        RefundBillDetail refundBill = BeanConvertUtils.convert(billDTO, RefundBillDetail.class);
        setOperInfo(refundBill, operatorId, false);
        updateSelective(refundBill);
    }

    @Override
    public void updateStatus(String id, String status, String operatorId) {
        if (CsStringUtils.isBlank(id)) {
            return;
        }
        Condition condition = new Condition(RefundBillDetail.class);
        condition.createCriteria()
                .andEqualTo("delFlg", false)
                .andEqualTo("refundBillId", id);
        RefundBillDetail refundBill = new RefundBillDetail();
        refundBill.setStatus(status);
        setOperInfo(refundBill, operatorId, false);
        mapper.updateByConditionSelective(refundBill, condition);
    }

    @Override
    public List<RefundBillDetailDTO> findRefundNo(String refundNo) {
        if (CsStringUtils.isBlank(refundNo)) {
            return Lists.newArrayList();
        }
        RefundBillDetail detail = new RefundBillDetail();
        detail.setRefundBillNo(refundNo);
        detail.setDelFlg(false);
        List<RefundBillDetail> select = mapper.select(detail);
        if (CollectionUtils.isEmpty(select)) {
            return Lists.newArrayList();
        }
        return select.stream().map(i -> BeanConvertUtils.convert(i, RefundBillDetailDTO.class)).toList();
    }

    @Override
    public void updateGneteReceivableTransInfo(RefundBillDetailDTO refundBillDetailDTO) {
        if (refundBillDetailDTO == null || CsStringUtils.isBlank(refundBillDetailDTO.getRefundBillDetailId())) {
            return;
        }
        RefundBillDetail refundBillDetail = new RefundBillDetail();
        refundBillDetail.setRefundBillDetailId(refundBillDetailDTO.getRefundBillDetailId());
        refundBillDetail.setGneteReceivableTransOrderNo(refundBillDetailDTO.getGneteReceivableTransOrderNo());
        refundBillDetail.setGneteReceivableTransResult(refundBillDetailDTO.getGneteReceivableTransResult());
        refundBillDetail.setGneteReceivableTransStatus(refundBillDetailDTO.getGneteReceivableTransStatus());
        mapper.updateByPrimaryKeySelective(refundBillDetail);
    }
}
