package com.ecommerce.pay.v2.biz.impl;

import com.ecommerce.common.service.common.BaseBiz;
import com.ecommerce.common.service.common.bsid.CommonBusinessIdGenerator;
import com.ecommerce.common.utils.BeanConvertUtils;
import com.github.pagehelper.page.PageMethod;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.pay.api.v2.dto.bill.SplitPayBillDTO;
import com.ecommerce.pay.api.v2.dto.bill.SplitPayBillDetailDTO;
import com.ecommerce.pay.api.v2.enums.PaymentStatusEnum;
import com.ecommerce.pay.v2.biz.ISplitPayBillBiz;
import com.ecommerce.pay.v2.biz.ISplitPayBillDetailBiz;
import com.ecommerce.pay.v2.biz.ISplitPayBillQuantityInfoBiz;
import com.ecommerce.pay.v2.dao.mapper.SplitPayBillMapper;
import com.ecommerce.pay.v2.dao.vo.SplitPayBill;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @created 19:08 04/09/2019
 * @description TODO
 */
@Slf4j
@Service
public class SplitPayBillBiz extends BaseBiz<SplitPayBill> implements ISplitPayBillBiz {

    @Autowired
    private SplitPayBillMapper mapper;
    @Autowired
    private ISplitPayBillQuantityInfoBiz splitPayBillQuantityInfoBiz;
    @Autowired
    private ISplitPayBillDetailBiz detailBiz;
    @Autowired
    private CommonBusinessIdGenerator codeGenerator;

    @Override
    public void create(SplitPayBillDTO billDTO, String operatorId) {
        billDTO.setSplitPayBillId(null);
        billDTO.setSplitPayBillNo(codeGenerator.incrementPaySplitCode());
        SplitPayBill splitPayBill = BeanConvertUtils.convert(billDTO, SplitPayBill.class);
        save(splitPayBill, operatorId);
        billDTO.setSplitPayBillId(splitPayBill.getSplitPayBillId());
        createDetail(billDTO, operatorId);
        billDTO.setBillSplitInfoString(splitPayBillQuantityInfoBiz.createQuantityInfo(billDTO,operatorId));
        log.info("SplitPayBillDTO.setBillSplitInfoString:{}",billDTO.getBillSplitInfoString());
    }

    @Override
    public Optional<SplitPayBillDTO> findByNo(String billNo) {
        if (CsStringUtils.isBlank(billNo)) {
            return Optional.empty();
        }
        SplitPayBill refundBill = new SplitPayBill();
        refundBill.setSplitPayBillNo(billNo);
        refundBill.setDelFlg(false);
        List<SplitPayBill> select = mapper.select(refundBill);
        if (CollectionUtils.isEmpty(select)) {
            return Optional.empty();
        }
        SplitPayBillDTO billDTO = BeanConvertUtils.convert(select.get(0), SplitPayBillDTO.class);
        List<SplitPayBillDetailDTO> detailDTOList = detailBiz.findBySplitPayBillNo(billDTO.getSplitPayBillNo());
        billDTO.setDetailDTOList(detailDTOList);
        billDTO.setBillSplitInfoString(splitPayBillQuantityInfoBiz.findBySplitPayBillId(billDTO.getSplitPayBillId()));
        log.info("SplitPayBillDTO.setBillSplitInfoString:{}",billDTO.getBillSplitInfoString());
        return Optional.of(billDTO);
    }

    @Override
    public Optional<SplitPayBillDTO> findById(String billId) {
        if (CsStringUtils.isBlank(billId)) {
            return Optional.empty();
        }
        SplitPayBill refundBill = mapper.selectByPrimaryKey(billId);
        if (refundBill == null || Boolean.TRUE.equals(refundBill.getDelFlg())) {
            return Optional.empty();
        }
        SplitPayBillDTO billDTO = BeanConvertUtils.convert(refundBill, SplitPayBillDTO.class);
        List<SplitPayBillDetailDTO> detailDTOList = detailBiz.findBySplitPayBillNo(billDTO.getSplitPayBillNo());
        billDTO.setDetailDTOList(detailDTOList);
        billDTO.setBillSplitInfoString(splitPayBillQuantityInfoBiz.findBySplitPayBillId(billDTO.getSplitPayBillId()));
        log.info("SplitPayBillDTO.setBillSplitInfoString:{}",billDTO.getBillSplitInfoString());
        return Optional.of(billDTO);
    }

    @Override
    public void update(SplitPayBillDTO billDTO, String operatorId) {
        SplitPayBill refundBill = BeanConvertUtils.convert(billDTO, SplitPayBill.class);
        setOperInfo(refundBill, operatorId, false);
        updateSelective(refundBill);
        updateDetail(billDTO, operatorId);
    }

    @Override
    public void updateStatus(String id, String status, String operatorId) {
        if (CsStringUtils.isBlank(id)) {
            return;
        }
        SplitPayBill refundBill = mapper.selectByPrimaryKey(id);
        if (refundBill == null || Boolean.TRUE.equals(refundBill.getDelFlg())) {
            return;
        }
        refundBill.setStatus(status);
        setOperInfo(refundBill, operatorId, false);
        updateSelective(refundBill);

        updateDetail(BeanConvertUtils.convert(refundBill, SplitPayBillDTO.class), operatorId);
    }

    private void createDetail(SplitPayBillDTO billDTO, String operatorId) {
        List<SplitPayBillDetailDTO> detailDTOList = billDTO.getDetailDTOList();
        if (!CollectionUtils.isEmpty(detailDTOList)) {
            for (SplitPayBillDetailDTO detailDTO : detailDTOList) {
                detailDTO.setSplitPayBillNo(billDTO.getSplitPayBillNo());
                detailDTO.setStatus(billDTO.getStatus());
                detailBiz.create(detailDTO, operatorId);
            }
        }
    }

    private void updateDetail(SplitPayBillDTO billDTO, String operatorId) {
        boolean finish = PaymentStatusEnum.PAY_SUCCESS.getCode().equals(billDTO.getStatus());

        List<SplitPayBillDetailDTO> detailDTOList = billDTO.getDetailDTOList();
        if (!CollectionUtils.isEmpty(detailDTOList)) {
            for (SplitPayBillDetailDTO detailDTO : detailDTOList) {
                if (CsStringUtils.isBlank(detailDTO.getSplitPayBillDetailId())) {
                    continue;
                }
                detailDTO.setStatus(billDTO.getStatus());
                if (finish) {
                    detailDTO.setActualPayAmount(detailDTO.getPayAmount());
                }
                detailBiz.update(detailDTO, operatorId);
            }
        }
    }

    @Override
    public List<SplitPayBillDTO> findByOrderNo(String orderNo) {
        if (CsStringUtils.isBlank(orderNo)) {
            return Lists.newArrayList();
        }
        SplitPayBill splitPayBill = new SplitPayBill();
        splitPayBill.setOrderNo(orderNo);
        splitPayBill.setDelFlg(false);
        List<SplitPayBill> select = mapper.select(splitPayBill);
        if (CollectionUtils.isEmpty(select)) {
            return Lists.newArrayList();
        }
        return select.stream().map(i -> BeanConvertUtils.convert(i, SplitPayBillDTO.class)).toList();
    }

    @Override
    public SplitPayBillDTO findBySettOrderNo(String settOrderNo) {
        SplitPayBill splitPayBill = new SplitPayBill();
        splitPayBill.setSettOrderNo(settOrderNo);
        splitPayBill.setDelFlg(false);
        List<SplitPayBill> select =  mapper.select(splitPayBill);
        if (CollectionUtils.isEmpty(select)) {
            return null;
        }
        return select.stream().map(i -> BeanConvertUtils.convert(i, SplitPayBillDTO.class)).findFirst().orElse(null);
    }

    @Override
    public boolean hasSplit(String orderNo) {
        if (CsStringUtils.isBlank(orderNo)) {
            log.info("orderNo is null");
            return false;
        }
        SplitPayBill splitPayBill = new SplitPayBill();
        splitPayBill.setOrderNo(orderNo);
        splitPayBill.setDelFlg(false);
        List<SplitPayBill> list = mapper.select(splitPayBill);
        if (!CollectionUtils.isEmpty(list)) {
            if( list.stream().anyMatch(item->PaymentStatusEnum.PAY_SUCCESS.getCode().equals(item.getStatus()) ||
                            PaymentStatusEnum.PAY_ING.getCode().equals(item.getStatus())
                    ) ){
                return true;
            }
            Set<String> splitPayBillNoSet = list.stream().map(SplitPayBill::getSplitPayBillNo).collect(Collectors.toSet());
            return detailBiz.hasSuccess(splitPayBillNoSet);
        }
        return false;
    }

    @Override
    public SplitPayBillMapper mapper() {
        return mapper;
    }


    @Override
    public List<SplitPayBill> findWithNoCallback(List<String> channelCodeEnumList, long timeStep) {
        Condition condition = new Condition(SplitPayBill.class);
        Example.Criteria criteria = condition.createCriteria();
        if(!CollectionUtils.isEmpty(channelCodeEnumList)){
            if( channelCodeEnumList.size() == 1 ){
                criteria.andEqualTo("channelCode",channelCodeEnumList.get(0));
            }else {
                criteria.andIn("channelCode",channelCodeEnumList);
            }
        }
        criteria.andEqualTo("status",PaymentStatusEnum.PAY_ING.getCode())
                .andLessThan("createTime", new Date(System.currentTimeMillis() - timeStep))
                .andEqualTo("delFlg",false);

        PageInfo<SplitPayBill> pageInfo = PageMethod.startPage(1, 1000, false, false, false)
                .doSelectPageInfo(() -> mapper.selectByCondition(condition));
        return pageInfo == null ? Lists.newArrayList() : pageInfo.getList();
    }
}
