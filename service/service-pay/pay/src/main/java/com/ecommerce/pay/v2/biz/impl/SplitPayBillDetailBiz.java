package com.ecommerce.pay.v2.biz.impl;

import com.ecommerce.common.service.common.BaseBiz;
import com.ecommerce.common.service.common.bsid.CommonBusinessIdGenerator;
import com.ecommerce.common.utils.BeanConvertUtils;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.pay.api.v2.dto.bill.SplitPayBillDetailDTO;
import com.ecommerce.pay.api.v2.enums.PaymentStatusEnum;
import com.ecommerce.pay.v2.biz.ISplitPayBillDetailBiz;
import com.ecommerce.pay.v2.dao.mapper.SplitPayBillDetailMapper;
import com.ecommerce.pay.v2.dao.vo.SplitPayBillDetail;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @created 19:15 04/09/2019
 * @description TODO
 */
@Slf4j
@Service
public class SplitPayBillDetailBiz extends BaseBiz<SplitPayBillDetail> implements ISplitPayBillDetailBiz {

    @Autowired
    private SplitPayBillDetailMapper mapper;

    @Autowired
    private CommonBusinessIdGenerator codeGenerator;

    @Override
    public void create(SplitPayBillDetailDTO billDTO, String operatorId) {
        billDTO.setSplitPayBillDetailId(null);
        billDTO.setSplitPayBillDetailCode(codeGenerator.incrementPaySplitCode());
        SplitPayBillDetail detail = BeanConvertUtils.convert(billDTO, SplitPayBillDetail.class);
        save(detail, operatorId);
        billDTO.setSplitPayBillDetailId(detail.getSplitPayBillDetailId());
    }

    @Override
    public Optional<SplitPayBillDetailDTO> findByNo(String billNo) {
        if (CsStringUtils.isBlank(billNo)) {
            return Optional.empty();
        }
        SplitPayBillDetail refundBill = new SplitPayBillDetail();
        refundBill.setSplitPayBillDetailCode(billNo);
        refundBill.setDelFlg(false);
        List<SplitPayBillDetail> select = mapper.select(refundBill);
        if (CollectionUtils.isEmpty(select)) {
            return Optional.empty();
        }
        return Optional.ofNullable(BeanConvertUtils.convert(select.get(0), SplitPayBillDetailDTO.class));
    }

    @Override
    public Optional<SplitPayBillDetailDTO> findById(String billId) {
        if (CsStringUtils.isBlank(billId)) {
            return Optional.empty();
        }
        SplitPayBillDetail refundBill = mapper.selectByPrimaryKey(billId);
        if (refundBill == null || Boolean.TRUE.equals(refundBill.getDelFlg())) {
            return Optional.empty();
        }
        return Optional.ofNullable(BeanConvertUtils.convert(refundBill, SplitPayBillDetailDTO.class));
    }

    @Override
    public void update(SplitPayBillDetailDTO billDTO, String operatorId) {
        SplitPayBillDetail billDetail = BeanConvertUtils.convert(billDTO, SplitPayBillDetail.class);
        setOperInfo(billDetail, operatorId, false);
        updateSelective(billDetail);
    }

    @Override
    public void updateStatus(String id, String status, String operatorId) {
        if (CsStringUtils.isBlank(id)) {
            return;
        }
        Condition condition = new Condition(SplitPayBillDetail.class);
        condition.createCriteria()
                .andEqualTo("delFlg", false)
                .andEqualTo("splitPayBillDetailId", id);
        SplitPayBillDetail billDetail = new SplitPayBillDetail();
        billDetail.setStatus(status);
        setOperInfo(billDetail, operatorId, false);
        mapper.updateByConditionSelective(billDetail, condition);
    }

    @Override
    public List<SplitPayBillDetailDTO> findBySplitPayBillNo(String splitPayBillNo) {
        if (CsStringUtils.isBlank(splitPayBillNo)) {
            return Lists.newArrayList();
        }
        SplitPayBillDetail detail = new SplitPayBillDetail();
        detail.setSplitPayBillNo(splitPayBillNo);
        detail.setDelFlg(false);
        List<SplitPayBillDetail> select = mapper.select(detail);
        if (CollectionUtils.isEmpty(select)) {
            return Lists.newArrayList();
        }
        return select.stream().map(i -> BeanConvertUtils.convert(i, SplitPayBillDetailDTO.class)).toList();
    }

    @Override
    public boolean hasSuccess(Set<String> splitPayBillNoSet) {
        if(CollectionUtils.isEmpty(splitPayBillNoSet)){
            return false;
        }
        Condition condition = new Condition(SplitPayBillDetail.class);
        Example.Criteria criteria = condition.createCriteria();
        if( splitPayBillNoSet.size() == 1 ) {
            criteria.andEqualTo("splitPayBillNo",splitPayBillNoSet.iterator().next());
        }else{
            criteria.andIn("splitPayBillNo",splitPayBillNoSet);
        }
        criteria.andEqualTo("status", PaymentStatusEnum.PAY_SUCCESS.getCode()).andEqualTo("delFlg",false);
        return mapper.selectCountByCondition(condition) > 0;
    }

    @Override
    public SplitPayBillDetailMapper mapper() {
        return mapper;
    }
}
