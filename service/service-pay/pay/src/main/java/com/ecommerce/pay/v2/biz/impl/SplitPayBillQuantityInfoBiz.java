package com.ecommerce.pay.v2.biz.impl;

import com.beust.jcommander.internal.Lists;
import com.ecommerce.common.service.common.BaseBiz;
import com.ecommerce.common.service.common.uuid.UUIDGenerator;
import com.ecommerce.common.service.lock.RedisLockService;
import com.ecommerce.pay.api.v2.dto.BillSplitInfoDTO;
import com.ecommerce.pay.api.v2.dto.bill.SplitPayBillDTO;
import com.ecommerce.pay.v2.biz.ISplitPayBillQuantityInfoBiz;
import com.ecommerce.pay.v2.dao.mapper.SplitPayBillQuantityInfoMapper;
import com.ecommerce.pay.v2.dao.vo.SplitPayBillQuantityInfo;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Condition;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SplitPayBillQuantityInfoBiz  extends BaseBiz<SplitPayBillQuantityInfo> implements ISplitPayBillQuantityInfoBiz {

    @Autowired
    private UUIDGenerator uuidGenerator;
    @Autowired
    private SplitPayBillQuantityInfoMapper splitPayBillQuantityInfoMapper;
    @Autowired
    private RedisLockService redisLockService;

    @Override
    public String createQuantityInfo(SplitPayBillDTO splitPayBillDTO, String operator) {
        if( CollectionUtils.isEmpty(splitPayBillDTO.getBillSplitInfoList()) ){
            log.info("createQuantityInfo getBillSplitInfoList is empty.");
            return null;
        }
        try {
            Date now = new Date();
            Map<String, BigDecimal> history = findLastTime(splitPayBillDTO.getOrderNo(), now);
            List<SplitPayBillQuantityInfo> list = Lists.newArrayList();
            for (BillSplitInfoDTO billSplitInfoDTO : splitPayBillDTO.getBillSplitInfoList()) {
                SplitPayBillQuantityInfo splitPayBillQuantityInfo = new SplitPayBillQuantityInfo();
                splitPayBillQuantityInfo.setSplitPayBillId(splitPayBillDTO.getSplitPayBillId());
                splitPayBillQuantityInfo.setSplitPayBillNo(splitPayBillDTO.getSplitPayBillNo());
                splitPayBillQuantityInfo.setPaBillSplitPayQuantityInfoId(uuidGenerator.gain());
                splitPayBillQuantityInfo.setGoodsName(billSplitInfoDTO.getGoodsName());
                splitPayBillQuantityInfo.setGoodsId(billSplitInfoDTO.getGoodsId());
                splitPayBillQuantityInfo.setOrderNo(splitPayBillDTO.getOrderNo());
                splitPayBillQuantityInfo.setItemSendQuantity(billSplitInfoDTO.getItemSendQuantity() == null ? BigDecimal.ZERO : billSplitInfoDTO.getItemSendQuantity());
                splitPayBillQuantityInfo.setUnitsName(billSplitInfoDTO.getUnitName());
                BigDecimal lastItemSendQuantity = history.containsKey(billSplitInfoDTO.getGoodsId()) ? history.get(billSplitInfoDTO.getGoodsId()) : BigDecimal.ZERO;
                //当前累计量减去上次累计量，等于当次增量
                splitPayBillQuantityInfo.setItemSendIncremental(splitPayBillQuantityInfo.getItemSendQuantity().subtract(lastItemSendQuantity));
                if (BigDecimal.ZERO.compareTo(splitPayBillQuantityInfo.getItemSendIncremental()) == 0) {
                    //没有增量则丢弃
                    continue;
                }
                splitPayBillQuantityInfo.setCreateTime(now);
                splitPayBillQuantityInfo.setDelFlg(false);
                splitPayBillQuantityInfo.setCreateUser(operator);
                list.add(splitPayBillQuantityInfo);
            }
            if(!list.isEmpty()) {
                splitPayBillQuantityInfoMapper.insertList(list);
            }
            return getDetail(list);
        }catch (Exception e){
            log.error(e.getMessage(),e);
            return null;
        }
    }

    @Override
    public String findBySplitPayBillId(String billNo) {
        Condition condition = newCondition();
        condition.createCriteria().andEqualTo("billNo",billNo);
        List<SplitPayBillQuantityInfo> list = splitPayBillQuantityInfoMapper.selectByCondition(condition);
        if( CollectionUtils.isEmpty(list)){
            return null;
        }
        return getDetail(list);
    }

    private String getDetail(List<SplitPayBillQuantityInfo> list){
        String s = list.stream().map(item->item.getGoodsName()+"*"+item.getItemSendIncremental().stripTrailingZeros().toPlainString()+item.getUnitsName()).collect(Collectors.joining(","));
        log.info("getDetail:{}",s);
        return s;
    }

    private Map<String,BigDecimal> findLastTime(String orderNo, Date now){
        Condition condition = newCondition();
        //小于当前时间
        condition.createCriteria().andEqualTo("orderNo",orderNo).andLessThan("createTime",now);
        condition.orderBy("createTime").desc();
        List<SplitPayBillQuantityInfo> list = splitPayBillQuantityInfoMapper.selectByCondition(condition);
        if( CollectionUtils.isEmpty(list)){
            return Maps.newHashMap();
        }
        //上一次插入的数据
        Long lastTimeCreateTime = list.get(0).getCreateTime().getTime();
        return list.stream().filter(item->lastTimeCreateTime.compareTo(item.getCreateTime().getTime())==0)
                .collect(Collectors.toMap(SplitPayBillQuantityInfo::getGoodsId,SplitPayBillQuantityInfo::getItemSendQuantity));
    }
}
