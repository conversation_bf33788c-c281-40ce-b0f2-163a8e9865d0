package com.ecommerce.pay.v2.biz.impl;

import com.github.pagehelper.page.PageMethod;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.pay.api.v2.dto.BaseBankDTO;
import com.ecommerce.pay.api.v2.dto.SuperBankDTO;
import com.ecommerce.pay.v2.biz.ISuperBankBiz;
import com.ecommerce.pay.v2.dao.mapper.SuperBankMapper;
import com.ecommerce.pay.v2.dao.vo.SuperBank;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @created 12:02 25/02/2019
 * @description
 */
@Slf4j
@Service
public class SuperBankBiz implements ISuperBankBiz {

    @Autowired
    private SuperBankMapper superBankMapper;

    @Override
    public List<BaseBankDTO> searchSuperBankLimit(String bankName) {
        log.info("查询 银行信息 {}", bankName);

        int limit = 16;

        Condition condition = new Condition(SuperBank.class);
        Example.Criteria criteria = condition.createCriteria();
        if (CsStringUtils.isNotEmpty(bankName)) {
            criteria.andLike("bankName", "%" + bankName + "%");
        }
       PageMethod.startPage(0, limit);
        List<SuperBank> voList = superBankMapper.selectByCondition(condition);
        if (CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        }
        return voList.stream().map(this::convertDTO).toList();
    }

    @Override
    public PageInfo<SuperBankDTO> pageSuperBank(SuperBankDTO superBankDTO) {
        Condition condition = new Condition(SuperBank.class);
        Example.Criteria criteria = condition.createCriteria();
        if (CsStringUtils.isNotBlank(superBankDTO.getBankNo())) {
            criteria.andLike("bankNo", "%" + superBankDTO.getBankNo() + "%");
        }
        if (CsStringUtils.isNotBlank(superBankDTO.getBankName())) {
            criteria.andLike("bankName", "%" + superBankDTO.getBankName() + "%");
        }
        int defaultPageNum = 1;
        int defaultPageSize = 10;
        if (superBankDTO.getPageNum() == null || superBankDTO.getPageNum() < defaultPageNum) {
            superBankDTO.setPageNum(defaultPageNum);
        }
        if (superBankDTO.getPageSize() == null || superBankDTO.getPageSize() < defaultPageSize) {
            superBankDTO.setPageSize(defaultPageSize);
        }
        Page<SuperBank> page = PageMethod.startPage(superBankDTO.getPageNum(), superBankDTO.getPageSize())
                .doSelectPage(() -> superBankMapper.selectByCondition(condition));
        Page<SuperBankDTO> collect = page.stream().map(this::convertSuperDTO).collect(Collectors.toCollection(Page::new));
        BeanUtils.copyProperties(page, collect);
        return collect.toPageInfo();
    }

    @Override
    public void addSuperBank(SuperBankDTO superBankDTO) {
        SuperBank bank = convert(Objects.requireNonNull(superBankDTO));
        superBankMapper.insert(bank);
    }

    @Override
    public void deleteSuperBank(String bankNo) {
        Condition condition = new Condition(SuperBank.class);
        condition.createCriteria()
                .andEqualTo("bankNo", Objects.requireNonNull(bankNo, "参数为空"));
        superBankMapper.deleteByCondition(condition);
    }

    private BaseBankDTO convertDTO(SuperBank superBank) {
        BaseBankDTO dto = new BaseBankDTO();
        dto.setBankCode(superBank.getBankNo());
        dto.setBankName(superBank.getBankAliasName());
        return dto;
    }

    private SuperBankDTO convertSuperDTO(SuperBank superBank) {
        SuperBankDTO dto = new SuperBankDTO();
        BeanUtils.copyProperties(superBank, dto);
        return dto;
    }

    private SuperBank convert(SuperBankDTO dto) {
        SuperBank vo = new SuperBank();
        BeanUtils.copyProperties(dto, vo);
        return vo;
    }
}
