package com.ecommerce.pay.v2.biz.impl;

import com.github.pagehelper.page.PageMethod;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.pay.v2.biz.ISuperBankCodeBiz;
import com.ecommerce.pay.v2.dao.mapper.SuperBankCodeMapper;
import com.ecommerce.pay.v2.dao.vo.SuperBankCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

@Slf4j
@Service
public class SuperBankCodeBiz implements ISuperBankCodeBiz {

    @Autowired
    private SuperBankCodeMapper superBankCodeMapper;

    @Override
    public SuperBankCode findByDirectBankNo(String directBankNo) {
        Condition condition = new Condition(SuperBankCode.class);
        condition.createCriteria().andEqualTo("directBankNo",directBankNo);
        List<SuperBankCode> list = superBankCodeMapper.selectByCondition(condition);
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }

    @Override
    public List<SuperBankCode> findByBankNameLike(String bankName, Integer limitSize) {
        log.info("查询 银行信息 {}", bankName);

        int limit = limitSize == null ? 20 : limitSize;

        Condition condition = new Condition(SuperBankCode.class);
        Example.Criteria criteria = condition.createCriteria();
        if (CsStringUtils.isNotEmpty(bankName)) {
            criteria.andLike("bankName", "%" + bankName + "%");
        }
        condition.orderBy("superBankNo").asc();
        PageMethod.startPage(0, limit);
        return superBankCodeMapper.selectByCondition(condition);
    }
}
