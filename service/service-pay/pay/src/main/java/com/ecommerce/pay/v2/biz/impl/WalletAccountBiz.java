package com.ecommerce.pay.v2.biz.impl;

import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.service.common.BaseBiz;
import com.ecommerce.common.service.common.bsid.CommonBusinessIdGenerator;
import com.ecommerce.common.service.lock.RedisLockService;
import com.ecommerce.common.utils.BeanConvertUtils;
import com.github.pagehelper.page.PageMethod;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.pay.api.v2.dto.WalletAccountDTO;
import com.ecommerce.pay.api.v2.enums.MemberChannelStatusEnum;
import com.ecommerce.pay.api.v2.enums.WalletAccountTypeEnum;
import com.ecommerce.pay.v2.biz.IWalletAccountBiz;
import com.ecommerce.pay.v2.dao.mapper.WalletAccountMapper;
import com.ecommerce.pay.v2.dao.vo.WalletAccount;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Condition;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @created 10:15 03/09/2019
 * @description TODO
 */
@Slf4j
@Service
public class WalletAccountBiz extends BaseBiz<WalletAccount> implements IWalletAccountBiz {

    @Autowired
    private WalletAccountMapper mapper;

    @Autowired
    private CommonBusinessIdGenerator codeGenerator;

    @Autowired
    private RedisLockService lockService;

    @Override
    public void createAccount(WalletAccountDTO walletAccountDTO, String operatorId) {
        walletAccountDTO.setWalletAccountId(null);
        walletAccountDTO.setWalletAccountNo(codeGenerator.incrementCode());
        walletAccountDTO.setStatus(MemberChannelStatusEnum.OPEND.getCode());
        walletAccountDTO.setAvailableBalance(BigDecimal.ZERO);
        walletAccountDTO.setFreezeAmount(BigDecimal.ZERO);
        walletAccountDTO.setCashBalance(BigDecimal.ZERO);
        walletAccountDTO.setAdvanceAmount(BigDecimal.ZERO);

        WalletAccount walletAccount = BeanConvertUtils.convert(walletAccountDTO, WalletAccount.class);
        save(walletAccount, operatorId);
        walletAccountDTO.setWalletAccountId(walletAccount.getWalletAccountId());
    }

    @Override
    public void createAccountBySync(WalletAccountDTO walletAccountDTO, String operatorId) {
        walletAccountDTO.setWalletAccountId(null);
        walletAccountDTO.setWalletAccountNo(codeGenerator.incrementCode());
        walletAccountDTO.setStatus(MemberChannelStatusEnum.OPEND.getCode());
        walletAccountDTO.setAdvanceAmount(BigDecimal.ZERO);

        WalletAccount walletAccount = BeanConvertUtils.convert(walletAccountDTO, WalletAccount.class);
        save(walletAccount, operatorId);
        walletAccountDTO.setWalletAccountId(walletAccount.getWalletAccountId());
    }

    @Override
    public void closeAccount(String walletAccountId, String operatorId) {
        WalletAccount walletAccount = get(walletAccountId);
        if (walletAccount == null || Boolean.TRUE.equals(walletAccount.getDelFlg())) {
            throw new BizException(BasicCode.DB_DATA_NOT_FOUND);
        }
        walletAccount.setStatus(MemberChannelStatusEnum.BE_STOP.getCode());
        setOperInfo(walletAccount, operatorId, false);
        updateSelective(walletAccount);
    }

    @Override
    public PageInfo<WalletAccountDTO> pageWalletAccount(Pageable pageable) {
        int pageNumber = pageable.getPageNumber();
        int pageSize = pageable.getPageSize();

        Condition condition = newCondition();
        condition.createCriteria()
                .andEqualTo("delFlg", false)
                .andEqualTo("status", MemberChannelStatusEnum.OPEND.getCode());
        condition.orderBy("createTime").desc();
        Page<WalletAccount> objects = PageMethod.startPage(pageNumber, pageSize).doSelectPage(() -> mapper.selectByCondition(condition));
        PageInfo<WalletAccountDTO> pageInfo = new PageInfo<>(BeanConvertUtils.convertPage(objects, WalletAccountDTO.class));
        if (pageNumber > pageInfo.getPages()) {
            return new PageInfo<>();
        }
        return pageInfo;
    }

    @Override
    public void closeAccountByMemberId(String memberId, String type, String operatorId) {
        Optional<WalletAccountDTO> dto = findByMemberId(memberId, type);
        if (dto.isPresent()) {
            WalletAccountDTO walletAccountDTO = dto.get();
            WalletAccount walletAccount = new WalletAccount();
            walletAccount.setWalletAccountId(walletAccountDTO.getWalletAccountId());
            walletAccount.setStatus(MemberChannelStatusEnum.BE_STOP.getCode());
            setOperInfo(walletAccount, operatorId, false);
            updateSelective(walletAccount);
        }
    }

    @Override
    public List<WalletAccountDTO> findByMemberId(String memberId) {
        if (CsStringUtils.isEmpty(memberId)) {
            return Lists.newArrayList();
        }
        WalletAccount vo = new WalletAccount();
        vo.setMemberId(memberId);
        vo.setDelFlg(false);
        vo.setStatus(MemberChannelStatusEnum.OPEND.getCode());
        List<WalletAccount> select = mapper.select(vo);
        if (CollectionUtils.isEmpty(select)) {
            return Lists.newArrayList();
        }
        return select.stream().map(i -> BeanConvertUtils.convert(i, WalletAccountDTO.class)).toList();
    }

    @Override
    public Optional<WalletAccountDTO> findByMemberChannelId(String memberChannelId) {
        if (CsStringUtils.isBlank(memberChannelId)) {
            return Optional.empty();
        }
        WalletAccount walletAccount = new WalletAccount();
        walletAccount.setMemberChannelId(memberChannelId);
        walletAccount.setDelFlg(false);
        List<WalletAccount> select = mapper.select(walletAccount);
        if (CollectionUtils.isEmpty(select)) {
            return Optional.empty();
        }
        WalletAccountDTO walletAccountDTO = BeanConvertUtils.convert(select.get(0), WalletAccountDTO.class);
        setChannelCode(walletAccountDTO);
        return Optional.of(walletAccountDTO);
    }

    @Override
    public Optional<WalletAccountDTO> findByNo(String accountNo) {
        if (CsStringUtils.isBlank(accountNo)) {
            return Optional.empty();
        }
        WalletAccount walletAccount = new WalletAccount();
        walletAccount.setWalletAccountNo(accountNo);
        walletAccount.setDelFlg(false);
        List<WalletAccount> select = mapper.select(walletAccount);
        if (CollectionUtils.isEmpty(select)) {
            return Optional.empty();
        }
        return Optional.ofNullable(BeanConvertUtils.convert(select.get(0), WalletAccountDTO.class));
    }

    @Override
    public Optional<WalletAccountDTO> findByMemberId(String memberId, String walletType) {
        List<WalletAccountDTO> dtos = findByMemberId(memberId);
        if (CollectionUtils.isEmpty(dtos)) {
            return Optional.empty();
        }
        for (WalletAccountDTO dto : dtos) {
            if (dto.getWalletAccountType().equals(walletType)) {
                return Optional.of(dto);
            }
        }
        return Optional.empty();
    }

    @Override
    public void update(WalletAccountDTO walletAccountDTO, String operatorId) {
        if (CsStringUtils.isEmpty(walletAccountDTO.getWalletAccountId())) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "更新失败，缺失主键");
        }
        walletAccountDTO.setCashBalance(null);
        walletAccountDTO.setAvailableBalance(null);
        walletAccountDTO.setFreezeAmount(null);
        walletAccountDTO.setAdvanceAmount(null);
        WalletAccount walletAccount = BeanConvertUtils.convert(walletAccountDTO, WalletAccount.class);
        setOperInfo(walletAccount, operatorId, false);
        updateSelective(walletAccount);
    }

    @Override
    public void updateBalance(WalletAccountDTO walletAccountDTO, String operatorId) {
        WalletAccount convert = BeanConvertUtils.convert(walletAccountDTO, WalletAccount.class);
        setOperInfo(convert, operatorId, false);
        mapper.updateByPrimaryKeySelective(convert);
    }

    private Condition getCondition() {
        return new Condition(WalletAccount.class);
    }

    private void setChannelCode(WalletAccountDTO walletAccountDTO) {
        if (WalletAccountTypeEnum.PAYER.getCode().equals(walletAccountDTO.getWalletAccountType())) {
            walletAccountDTO.setChannelCode("pinganjzpay");
        }
        if (WalletAccountTypeEnum.PAYEE.getCode().equals(walletAccountDTO.getWalletAccountType())) {
            walletAccountDTO.setChannelCode("pinganjz");
        }
    }

    @Transactional
    @Override
    public void incrementBalance(String walletAccountId, BigDecimal bigDecimal) {
        if (CsStringUtils.isEmpty(walletAccountId)) {
            throw new BizException(BasicCode.PARAM_NULL, "walletAccount");
        }
        String lock = lockService.lockFast(walletAccountId);
        try {
            Condition condition = getCondition();
            condition.setForUpdate(true);
            condition.createCriteria()
                    .andEqualTo("walletAccountId", walletAccountId)
                    .andEqualTo("delFlg", false);
            List<WalletAccount> walletAccounts = mapper.selectByCondition(condition);
            if (CollectionUtils.isEmpty(walletAccounts)) {
                throw new BizException(BasicCode.DB_DATA_NOT_FOUND);
            }
            WalletAccount walletAccount = walletAccounts.get(0);
            BigDecimal availableBalance = walletAccount.getAvailableBalance();
            BigDecimal decimal = availableBalance.add(bigDecimal);
            walletAccount.setAvailableBalance(decimal);
            updateSelective(walletAccount);
        } finally {
            lockService.unlock(walletAccountId, lock);
        }
    }

    @Transactional
    @Override
    public void incrementBalanceAndDecrementAdvanceBalance(String walletAccountId, BigDecimal bigDecimal) {
        if (CsStringUtils.isEmpty(walletAccountId)) {
            throw new BizException(BasicCode.PARAM_NULL, "walletAccount");
        }
        String lock = lockService.lockFast(walletAccountId);
        try {
            Condition condition = getCondition();
            condition.setForUpdate(true);
            condition.createCriteria()
                    .andEqualTo("walletAccountId", walletAccountId)
                    .andEqualTo("delFlg", false);
            List<WalletAccount> walletAccounts = mapper.selectByCondition(condition);
            if (CollectionUtils.isEmpty(walletAccounts)) {
                throw new BizException(BasicCode.DB_DATA_NOT_FOUND);
            }
            WalletAccount walletAccount = walletAccounts.get(0);
            BigDecimal availableBalance = walletAccount.getAvailableBalance();
            BigDecimal advanceAmount = walletAccount.getAdvanceAmount();

            if (advanceAmount.compareTo(bigDecimal) < 0) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "余额不足");
            }

            BigDecimal decimal = availableBalance.add(bigDecimal);
            BigDecimal subtract = advanceAmount.subtract(bigDecimal);

            walletAccount.setAvailableBalance(decimal);
            walletAccount.setAdvanceAmount(subtract);
            updateSelective(walletAccount);
        } finally {
            lockService.unlock(walletAccountId, lock);
        }
    }

    @Transactional
    @Override
    public void incrementFreezeBalanceAndDecrementBalance(String walletAccountId, BigDecimal bigDecimal) {
        if (CsStringUtils.isEmpty(walletAccountId)) {
            throw new BizException(BasicCode.PARAM_NULL, "walletAccount");
        }
        String lock = lockService.lockFast(walletAccountId);
        try {
            Condition condition = getCondition();
            condition.setForUpdate(true);
            condition.createCriteria()
                    .andEqualTo("walletAccountId", walletAccountId)
                    .andEqualTo("delFlg", false);
            List<WalletAccount> walletAccounts = mapper.selectByCondition(condition);
            if (CollectionUtils.isEmpty(walletAccounts)) {
                throw new BizException(BasicCode.DB_DATA_NOT_FOUND);
            }
            WalletAccount walletAccount = walletAccounts.get(0);
            BigDecimal availableBalance = walletAccount.getAvailableBalance();
            BigDecimal freezeAmount = walletAccount.getFreezeAmount();
            BigDecimal cashBalance = walletAccount.getCashBalance();

            if (availableBalance.compareTo(bigDecimal) < 0) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "余额不足");
            }

            BigDecimal decimal = availableBalance.subtract(bigDecimal);
            BigDecimal add = freezeAmount.add(bigDecimal);

            if (cashBalance.compareTo(decimal) > 0) {
                cashBalance = decimal;
            }

            walletAccount.setAvailableBalance(decimal);
            walletAccount.setFreezeAmount(add);
            walletAccount.setCashBalance(cashBalance);
            updateSelective(walletAccount);
        } finally {
            lockService.unlock(walletAccountId, lock);
        }
    }

    @Transactional
    @Override
    public void decrementBalance(String walletAccountId, BigDecimal bigDecimal) {
        if (CsStringUtils.isEmpty(walletAccountId)) {
            throw new BizException(BasicCode.PARAM_NULL, "walletAccount");
        }
        String lock = lockService.lockFast(walletAccountId);
        try {
            Condition condition = getCondition();
            condition.setForUpdate(true);
            condition.createCriteria()
                    .andEqualTo("walletAccountId", walletAccountId)
                    .andEqualTo("delFlg", false);
            List<WalletAccount> walletAccounts = mapper.selectByCondition(condition);
            if (CollectionUtils.isEmpty(walletAccounts)) {
                throw new BizException(BasicCode.DB_DATA_NOT_FOUND);
            }
            WalletAccount walletAccount = walletAccounts.get(0);
            BigDecimal availableBalance = walletAccount.getAvailableBalance();
            BigDecimal cashBalance = walletAccount.getCashBalance();

            if (availableBalance.compareTo(bigDecimal) < 0) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "余额不足");
            }

            BigDecimal decimal = availableBalance.subtract(bigDecimal);

            if (cashBalance.compareTo(decimal) > 0) {
                cashBalance = decimal;
            }

            String memberId = walletAccount.getMemberId();
            log.info(" 账户: {}, old: [availableBalance: {}, cashBalance: {}]",memberId, availableBalance, cashBalance);
            log.info(" 账户: {}, new: [availableBalance: {}, cashBalance: {}]",memberId, decimal, cashBalance);

            walletAccount.setAvailableBalance(decimal);
            walletAccount.setCashBalance(cashBalance);
            updateSelective(walletAccount);
        } finally {
            lockService.unlock(walletAccountId, lock);
        }
    }

    @Transactional
    @Override
    public void decrementFreezeBalanceAndIncrementBalance(String walletAccountId, BigDecimal bigDecimal) {
        if (CsStringUtils.isEmpty(walletAccountId)) {
            throw new BizException(BasicCode.PARAM_NULL, "walletAccount");
        }
        String lock = lockService.lockFast(walletAccountId);
        try {
            Condition condition = getCondition();
            condition.setForUpdate(true);
            condition.createCriteria()
                    .andEqualTo("walletAccountId", walletAccountId)
                    .andEqualTo("delFlg", false);
            List<WalletAccount> walletAccounts = mapper.selectByCondition(condition);
            if (CollectionUtils.isEmpty(walletAccounts)) {
                throw new BizException(BasicCode.DB_DATA_NOT_FOUND);
            }
            WalletAccount walletAccount = walletAccounts.get(0);
            BigDecimal freezeAmount = walletAccount.getFreezeAmount();
            BigDecimal availableBalance = walletAccount.getAvailableBalance();

            if (freezeAmount.compareTo(bigDecimal) < 0) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "冻结金额不足");
            }

            BigDecimal subtract = freezeAmount.subtract(bigDecimal);
            BigDecimal decimal = availableBalance.add(bigDecimal);

            String memberId = walletAccount.getMemberId();
            log.info(" 账户: {}, old: [freezeAmount: {}, availableBalance: {}]",memberId, freezeAmount, availableBalance);
            log.info(" 账户: {}, new: [freezeAmount: {}, availableBalance: {}]",memberId, subtract, decimal);

            walletAccount.setFreezeAmount(subtract);
            walletAccount.setAvailableBalance(decimal);
            updateSelective(walletAccount);
        } finally {
            lockService.unlock(walletAccountId, lock);
        }
    }

    @Transactional
    @Override
    public void decrementFreezeBalanceAdvanceBalanceAndIncrementBalance(String walletAccountId, BigDecimal bigDecimal) {
        if (CsStringUtils.isEmpty(walletAccountId)) {
            throw new BizException(BasicCode.PARAM_NULL, "walletAccount");
        }
        String lock = lockService.lockFast(walletAccountId);
        try {
            Condition condition = getCondition();
            condition.setForUpdate(true);
            condition.createCriteria()
                    .andEqualTo("walletAccountId", walletAccountId)
                    .andEqualTo("delFlg", false);
            List<WalletAccount> walletAccounts = mapper.selectByCondition(condition);
            if (CollectionUtils.isEmpty(walletAccounts)) {
                throw new BizException(BasicCode.DB_DATA_NOT_FOUND);
            }
            WalletAccount walletAccount = walletAccounts.get(0);
            BigDecimal freezeAmount = walletAccount.getFreezeAmount();
            BigDecimal availableBalance = walletAccount.getAvailableBalance();
            BigDecimal advanceAmount = walletAccount.getAdvanceAmount();

            if (freezeAmount.compareTo(bigDecimal) < 0) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "冻结金额不足");
            }
            if (advanceAmount.compareTo(bigDecimal) < 0) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "预收金额不足");
            }

            BigDecimal subtract = freezeAmount.subtract(bigDecimal);
            BigDecimal decimal = availableBalance.add(bigDecimal);
            BigDecimal subtract1 = advanceAmount.subtract(bigDecimal);

            String memberId = walletAccount.getMemberId();
            log.info(" 账户: {}, old: [freezeAmount: {}, availableBalance: {}, advanceAmount: {}]",memberId, freezeAmount, availableBalance, advanceAmount);
            log.info(" 账户: {}, new: [freezeAmount: {}, availableBalance: {}, advanceAmount: {}]",memberId, subtract, decimal, subtract1);
            walletAccount.setFreezeAmount(subtract);
            walletAccount.setAvailableBalance(decimal);
            walletAccount.setAdvanceAmount(subtract1);
            updateSelective(walletAccount);
        } finally {
            lockService.unlock(walletAccountId, lock);
        }
    }

    @Transactional
    @Override
    public void incrementFreezeBalance(String walletAccountId, BigDecimal bigDecimal) {
        if (CsStringUtils.isEmpty(walletAccountId)) {
            throw new BizException(BasicCode.PARAM_NULL, "walletAccount");
        }
        String lock = lockService.lockFast(walletAccountId);
        try {
            Condition condition = getCondition();
            condition.setForUpdate(true);
            condition.createCriteria()
                    .andEqualTo("walletAccountId", walletAccountId)
                    .andEqualTo("delFlg", false);
            List<WalletAccount> walletAccounts = mapper.selectByCondition(condition);
            if (CollectionUtils.isEmpty(walletAccounts)) {
                throw new BizException(BasicCode.DB_DATA_NOT_FOUND);
            }
            WalletAccount walletAccount = walletAccounts.get(0);
            BigDecimal freezeAmount = walletAccount.getFreezeAmount();
            BigDecimal add = freezeAmount.add(bigDecimal);

            String memberId = walletAccount.getMemberId();
            log.info(" 账户: {}, old: [freezeAmount: {}]",memberId, freezeAmount);
            log.info(" 账户: {}, new: [freezeAmount: {}]",memberId, add);

            walletAccount.setFreezeAmount(add);
            updateSelective(walletAccount);
        } finally {
            lockService.unlock(walletAccountId, lock);
        }
    }

    @Transactional
    @Override
    public void decrementFreezeBalance(String walletAccountId, BigDecimal bigDecimal) {
        if (CsStringUtils.isEmpty(walletAccountId)) {
            throw new BizException(BasicCode.PARAM_NULL, "walletAccount");
        }
        String lock = lockService.lockFast(walletAccountId);
        try {
            Condition condition = getCondition();
            condition.setForUpdate(true);
            condition.createCriteria()
                    .andEqualTo("walletAccountId", walletAccountId)
                    .andEqualTo("delFlg", false);
            List<WalletAccount> walletAccounts = mapper.selectByCondition(condition);
            if (CollectionUtils.isEmpty(walletAccounts)) {
                throw new BizException(BasicCode.DB_DATA_NOT_FOUND);
            }
            WalletAccount walletAccount = walletAccounts.get(0);
            BigDecimal freezeAmount = walletAccount.getFreezeAmount();

            if (freezeAmount.compareTo(bigDecimal) < 0) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "冻结金额不足");
            }

            BigDecimal decimal = freezeAmount.subtract(bigDecimal);

            String memberId = walletAccount.getMemberId();
            log.info(" 账户: {}, old: [freezeAmount: {}]",memberId, freezeAmount);
            log.info(" 账户: {}, new: [freezeAmount: {}]",memberId, decimal);

            walletAccount.setFreezeAmount(decimal);
            updateSelective(walletAccount);
        } finally {
            lockService.unlock(walletAccountId, lock);
        }
    }

    @Transactional
    @Override
    public void decrementCashBalance(String walletAccountId, BigDecimal bigDecimal) {
        if (CsStringUtils.isEmpty(walletAccountId)) {
            throw new BizException(BasicCode.PARAM_NULL, "walletAccount");
        }
        String lock = lockService.lockFast(walletAccountId);
        try {
            Condition condition = getCondition();
            condition.setForUpdate(true);
            condition.createCriteria()
                    .andEqualTo("walletAccountId", walletAccountId)
                    .andEqualTo("delFlg", false);
            List<WalletAccount> walletAccounts = mapper.selectByCondition(condition);
            if (CollectionUtils.isEmpty(walletAccounts)) {
                throw new BizException(BasicCode.DB_DATA_NOT_FOUND);
            }
            WalletAccount walletAccount = walletAccounts.get(0);
            BigDecimal availableBalance = walletAccount.getAvailableBalance();
            BigDecimal cashBalance = walletAccount.getCashBalance();

            if (cashBalance.compareTo(bigDecimal) < 0) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "可提现金额不足");
            }

            BigDecimal decimal = cashBalance.subtract(bigDecimal);
            BigDecimal subtract = availableBalance.subtract(bigDecimal);

            String memberId = walletAccount.getMemberId();
            log.info(" 账户: {}, old: [availableBalance: {}, cashBalance: {}]",memberId, availableBalance, cashBalance);
            log.info(" 账户: {}, new: [availableBalance: {}, cashBalance: {}]",memberId, subtract, decimal);

            walletAccount.setAvailableBalance(subtract);
            walletAccount.setCashBalance(decimal);
            updateSelective(walletAccount);
        } finally {
            lockService.unlock(walletAccountId, lock);
        }
    }

    @Transactional
    @Override
    public void incrementAdvanceAmount(String walletAccountId, BigDecimal bigDecimal) {
        if (CsStringUtils.isEmpty(walletAccountId)) {
            throw new BizException(BasicCode.PARAM_NULL, "walletAccount");
        }
        String lock = lockService.lockFast(walletAccountId);
        try {
            Condition condition = getCondition();
            condition.setForUpdate(true);
            condition.createCriteria()
                    .andEqualTo("walletAccountId", walletAccountId)
                    .andEqualTo("delFlg", false);
            List<WalletAccount> walletAccounts = mapper.selectByCondition(condition);
            if (CollectionUtils.isEmpty(walletAccounts)) {
                throw new BizException(BasicCode.DB_DATA_NOT_FOUND);
            }
            WalletAccount walletAccount = walletAccounts.get(0);
            BigDecimal advanceAmount = walletAccount.getAdvanceAmount();

            BigDecimal decimal = advanceAmount.add(bigDecimal);

            String memberId = walletAccount.getMemberId();
            log.info(" 账户: {}, old: [advanceAmount: {}]",memberId, advanceAmount);
            log.info(" 账户: {}, new: [advanceAmount: {}]",memberId, decimal);

            walletAccount.setAdvanceAmount(decimal);
            updateSelective(walletAccount);
        } finally {
            lockService.unlock(walletAccountId, lock);
        }
    }

    @Transactional
    @Override
    public void decrementAdvanceAmount(String walletAccountId, BigDecimal bigDecimal) {
        if (CsStringUtils.isEmpty(walletAccountId)) {
            throw new BizException(BasicCode.PARAM_NULL, "walletAccount");
        }
        String lock = lockService.lockFast(walletAccountId);
        try {
            Condition condition = getCondition();
            condition.setForUpdate(true);
            condition.createCriteria()
                    .andEqualTo("walletAccountId", walletAccountId)
                    .andEqualTo("delFlg", false);
            List<WalletAccount> walletAccounts = mapper.selectByCondition(condition);
            if (CollectionUtils.isEmpty(walletAccounts)) {
                throw new BizException(BasicCode.DB_DATA_NOT_FOUND);
            }
            WalletAccount walletAccount = walletAccounts.get(0);
            BigDecimal advanceAmount = walletAccount.getAdvanceAmount();

            if (advanceAmount.compareTo(bigDecimal) < 0) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "预收金额不足");
            }

            BigDecimal decimal = advanceAmount.subtract(bigDecimal);

            String memberId = walletAccount.getMemberId();
            log.info(" 账户: {}, old: [advanceAmount: {}]",memberId, advanceAmount);
            log.info(" 账户: {}, new: [advanceAmount: {}]",memberId, decimal);

            walletAccount.setAdvanceAmount(decimal);
            updateSelective(walletAccount);
        } finally {
            lockService.unlock(walletAccountId, lock);
        }
    }

    @Override
    public void incrementFreezeBalanceAndAdvanceAmount(String walletAccountId, BigDecimal bigDecimal) {
        if (CsStringUtils.isEmpty(walletAccountId)) {
            throw new BizException(BasicCode.PARAM_NULL, "walletAccount");
        }
        String lock = lockService.lockFast(walletAccountId);
        try {
            Condition condition = getCondition();
            condition.setForUpdate(true);
            condition.createCriteria()
                    .andEqualTo("walletAccountId", walletAccountId)
                    .andEqualTo("delFlg", false);
            List<WalletAccount> walletAccounts = mapper.selectByCondition(condition);
            if (CollectionUtils.isEmpty(walletAccounts)) {
                throw new BizException(BasicCode.DB_DATA_NOT_FOUND);
            }
            WalletAccount walletAccount = walletAccounts.get(0);
            BigDecimal advanceAmount = walletAccount.getAdvanceAmount();
            BigDecimal freezeAmount = walletAccount.getFreezeAmount();

            BigDecimal decimal = advanceAmount.add(bigDecimal);
            BigDecimal add = freezeAmount.add(bigDecimal);

            String memberId = walletAccount.getMemberId();
            log.info(" 账户: {}, old: [freezeAmount: {}, advanceAmount: {}]",memberId,freezeAmount, advanceAmount);
            log.info(" 账户: {}, new: [freezeAmount: {}, advanceAmount: {}]",memberId,add, decimal);

            walletAccount.setFreezeAmount(add);
            walletAccount.setAdvanceAmount(decimal);
            updateSelective(walletAccount);
        } finally {
            lockService.unlock(walletAccountId, lock);
        }
    }

    @Override
    public void reconciliation() {
        // todo
    }

}
