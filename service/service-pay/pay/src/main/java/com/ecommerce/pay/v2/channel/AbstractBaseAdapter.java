package com.ecommerce.pay.v2.channel;

import com.alibaba.fastjson.JSON;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.common.utils.DateUtil;
import com.ecommerce.member.api.dto.member.config.MemberConfigDTO;
import com.ecommerce.member.api.dto.member.config.MemberConfigEnum;
import com.ecommerce.member.api.dto.relation.MemberRelationDTO;
import com.ecommerce.member.api.dto.relation.MemberRelationQueryDTO;
import com.ecommerce.member.api.service.IMemberConfigService;
import com.ecommerce.member.api.service.IMemberRelationService;
import com.ecommerce.open.api.dto.apicenter.ApiResult;
import com.ecommerce.open.api.dto.apicenter.pay.ERPBalanceChangeNotifyRequestDTO;
import com.ecommerce.open.api.dto.apicenter.pay.ERPBalanceChangeNotifyResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.ApplicationTextMsgDynamicCodeResponseDTO;
import com.ecommerce.open.api.service.apicenter.IOpenAPIInvokeService;
import com.ecommerce.open.enums.BizCodeEnum;
import com.ecommerce.pay.api.exception.PayCode;
import com.ecommerce.pay.api.v2.dto.AutoPaymentResponseDTO;
import com.ecommerce.pay.api.v2.dto.ChannelCardDTO;
import com.ecommerce.pay.api.v2.dto.ChannelCardRequestDTO;
import com.ecommerce.pay.api.v2.dto.ChannelCardResponseDTO;
import com.ecommerce.pay.api.v2.dto.DirectPayRequestDTO;
import com.ecommerce.pay.api.v2.dto.DirectPayResponseDTO;
import com.ecommerce.pay.api.v2.dto.MemberChannelCheckDTO;
import com.ecommerce.pay.api.v2.dto.MemberChannelDTO;
import com.ecommerce.pay.api.v2.dto.MemberChannelRequestDTO;
import com.ecommerce.pay.api.v2.dto.MemberChannelResponseDTO;
import com.ecommerce.pay.api.v2.dto.PasswordCallbackRequestDTO;
import com.ecommerce.pay.api.v2.dto.PasswordCallbackResponseDTO;
import com.ecommerce.pay.api.v2.dto.PasswordRequestDTO;
import com.ecommerce.pay.api.v2.dto.PasswordResponseDTO;
import com.ecommerce.pay.api.v2.dto.PaymentBillInfo;
import com.ecommerce.pay.api.v2.dto.PaymentCallbackRequestDTO;
import com.ecommerce.pay.api.v2.dto.PaymentCallbackResponseDTO;
import com.ecommerce.pay.api.v2.dto.PaymentExceptionDTO;
import com.ecommerce.pay.api.v2.dto.PaymentNotifyDTO;
import com.ecommerce.pay.api.v2.dto.PaymentRequestWrapDTO;
import com.ecommerce.pay.api.v2.dto.PaymentResponseDTO;
import com.ecommerce.pay.api.v2.dto.RechargeCallbackRequestDTO;
import com.ecommerce.pay.api.v2.dto.RechargeCallbackResponseDTO;
import com.ecommerce.pay.api.v2.dto.RechargeRequestDTO;
import com.ecommerce.pay.api.v2.dto.RechargeResponseDTO;
import com.ecommerce.pay.api.v2.dto.SendVerificationCodeRequestDTO;
import com.ecommerce.pay.api.v2.dto.SendVerificationCodeResponseDTO;
import com.ecommerce.pay.api.v2.dto.SmallAmountTransferDTO;
import com.ecommerce.pay.api.v2.dto.SplitPayCompleteRequestDTO;
import com.ecommerce.pay.api.v2.dto.SplitPayCompleteResponseDTO;
import com.ecommerce.pay.api.v2.dto.ValidateResultDTO;
import com.ecommerce.pay.api.v2.dto.bill.CashWithdrawalBillDTO;
import com.ecommerce.pay.api.v2.dto.bill.PaymentBillDTO;
import com.ecommerce.pay.api.v2.dto.bill.RefundBillDTO;
import com.ecommerce.pay.api.v2.dto.bill.SplitPayBillDTO;
import com.ecommerce.pay.api.v2.dto.bill.SplitPayBillDetailDTO;
import com.ecommerce.pay.api.v2.dto.query.DownloadStatementDTO;
import com.ecommerce.pay.api.v2.enums.ChannelCardStatusEnum;
import com.ecommerce.pay.api.v2.enums.ChannelCodeEnum;
import com.ecommerce.pay.api.v2.enums.ExceptionTypeEnum;
import com.ecommerce.pay.api.v2.enums.OperatorStatusEnum;
import com.ecommerce.pay.api.v2.enums.PaymentCallbackEnum;
import com.ecommerce.pay.api.v2.enums.PaymentTypeEnum;
import com.ecommerce.pay.api.v2.enums.PaymentWarningCodeEnum;
import com.ecommerce.pay.enums.PayDetailTypeEnum;
import com.ecommerce.pay.v2.biz.IBillLogsBiz;
import com.ecommerce.pay.v2.biz.IChannelCardBiz;
import com.ecommerce.pay.v2.biz.IChannelConfigBiz;
import com.ecommerce.pay.v2.biz.IMemberChannelBiz;
import com.ecommerce.pay.v2.biz.IPaymentBillBiz;
import com.ecommerce.pay.v2.biz.IPaymentExceptionBiz;
import com.ecommerce.pay.v2.biz.IPaymentIntegrationBiz;
import com.ecommerce.pay.v2.biz.IRefundBillBiz;
import com.ecommerce.pay.v2.biz.ISplitPayBillBiz;
import com.ecommerce.pay.v2.biz.ISplitPayBillDetailBiz;
import com.ecommerce.pay.v2.biz.impl.ChannelCardBiz;
import com.ecommerce.pay.v2.executor.IChannelHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Slf4j
public abstract class AbstractBaseAdapter implements IChannelHandler {

	@Autowired
	protected IPaymentIntegrationBiz paymentIntegrationBiz;

	@Autowired
	protected IPaymentBillBiz paymentBillBiz;

	@Autowired
	protected ISplitPayBillBiz splitPayBillBiz;

	@Autowired
	protected ISplitPayBillDetailBiz splitPayBillDetailBiz;

	@Autowired
	protected IRefundBillBiz refundBillBiz;

	@Autowired
	protected IChannelCardBiz channelCardBiz;

	@Autowired
	protected IChannelConfigBiz channelConfigBiz;

	@Autowired
	protected IMemberChannelBiz memberChannelBiz;

	@Autowired
	protected IBillLogsBiz billLogsBiz;

	@Autowired
	protected IMemberConfigService memberConfigService;
	@Autowired
	protected IMemberRelationService memberRelationService;
	@Autowired
	protected IOpenAPIInvokeService openAPIInvokeService;
	@Autowired
	protected IPaymentExceptionBiz paymentExceptionBiz;

	@Value("${spring.profiles.active}")
	protected String profile;

	protected static final int MAX_CARDS = 10;

	/**
	 * 记录支付流水
	 * 
	 * @pdOid f35925d7-533d-42b8-8793-eae461dbee74
	 */
	protected void logsRefund(RefundBillDTO refundBillDTO, String operator) {
		billLogsBiz.logsRefund(refundBillDTO, operator);
	}

	protected void logsPay(PaymentBillDTO paymentBillDTO, String operatorId) {
		billLogsBiz.logsPayment(paymentBillDTO, operatorId);
	}

	/**
	 * 直接远程调用 通知接入方 支付成功
	 * 
	 * @param paymentBillDTO
	 * @param ifSuccess
	 */
	protected void notifyByRemoteInvoke(PaymentBillDTO paymentBillDTO, Boolean ifSuccess, String channel,
			String operator) {
		PaymentBillDTO update = new PaymentBillDTO();
		update.setPaymentBillId(paymentBillDTO.getPaymentBillId());
		try {
			if (Boolean.TRUE.equals(ifSuccess)) {
				//如果钱没有直接到卖家
				if(ChannelCodeEnum.GNETEPAY.getCode().equals(paymentBillDTO.getChannelCode()) ||
						ChannelCodeEnum.GNETEPAYC2B.getCode().equals(paymentBillDTO.getChannelCode()) ||
						ChannelCodeEnum.GNETEPAYWX.getCode().equals(paymentBillDTO.getChannelCode()) ||
						ChannelCodeEnum.GNETEPAYZFB.getCode().equals(paymentBillDTO.getChannelCode()) ){
					billLogsBiz.logsPaymentByFreeze(paymentBillDTO, operator);
				}else{
					this.logsPay(paymentBillDTO, operator);
				}
			}
			PaymentNotifyDTO paymentNotifyDTO = new PaymentNotifyDTO();
			paymentNotifyDTO.setAmount(paymentBillDTO.getPayAmount());
			paymentNotifyDTO.setBizId(paymentBillDTO.getSrcBizNo());
			paymentNotifyDTO.setChannel(channel);
			paymentNotifyDTO.setIfSuccess(ifSuccess);
			paymentNotifyDTO.setOperator(operator);
			paymentNotifyDTO.setPaymentBillId(paymentBillDTO.getPaymentBillId());
			paymentNotifyDTO.setPaymentBillNo(paymentBillDTO.getPaymentBillNo());
			paymentNotifyDTO.setPaymentBizCode(paymentBillDTO.getPaymentBizCode());
			paymentIntegrationBiz.paymentResultCallback(paymentNotifyDTO);
			log.info("回调支付接入方成功 paymentBillDTO: {}, flag: {}", paymentBillDTO, ifSuccess);
			update.setNotifyBusStatus(PaymentCallbackEnum.BIZ_CALL_SUCCESS.getCode());
			paymentBillBiz.updatePaymentBill(update, operator);
		} catch (Exception e) {
			log.error(" 回调 支付接入方失败 {} {}", e.getMessage(), e.getCause(),e);
			update.setNotifyBusStatus(PaymentCallbackEnum.BIZ_CALL_FAILED.getCode());
			update.setWarningCode(PaymentWarningCodeEnum.UPDATE_PAYBILLINFO_FAIL.getCode());
			paymentBillBiz.updatePaymentBill(update, operator);
		}
	}

	/**
	 * 通知支付成功
	 * 
	 * 
	 * @param paymentBillDTO
	 * @param ifSuccess
	 * @pdOid d03fb922-5b26-4b3e-ba57-8d1ef2ef1684
	 */
	public void notifyPay(PaymentBillDTO paymentBillDTO, Boolean ifSuccess, String channel, String operator) {
		this.notifyByRemoteInvoke(paymentBillDTO, ifSuccess, channel, operator);
	}

	protected void notifyPaymentResult(PaymentBillDTO paymentBillDTO, PaymentRequestWrapDTO paymentRequestDTO,String operator) {
		PaymentBillInfo info = new PaymentBillInfo();
		info.setBizId(paymentRequestDTO.getBizId());
		info.setPaymentBillId(paymentBillDTO.getPaymentBillId());
		info.setPaymentBillNo(paymentBillDTO.getPaymentBillNo());
		info.setPaymentBizCode(paymentBillDTO.getPaymentBizCode());
		info.setChannel(paymentBillDTO.getChannelCode());
		info.setRemarks(paymentBillDTO.getRemarks());
		info.setOfflineAttachment(paymentBillDTO.getOfflineAttachment());
		info.setOperator(operator);
		try {
			paymentIntegrationBiz.updatePaymentBillInfo(info);
		} catch (Exception e) {
			PaymentBillDTO dto = new PaymentBillDTO();
			dto.setPaymentBillId(paymentBillDTO.getPaymentBillId());
			dto.setWarningCode(PaymentWarningCodeEnum.UPDATE_PAYBILLINFO_FAIL.getCode());
			paymentBillBiz.updatePaymentBill(dto, operator);
		}
	}

	/**
	 * 通知支付成功 不记录流水
	 *
	 *
	 * @param paymentBillDTO
	 * @param ifSuccess
	 * @pdOid d03fb922-5b26-4b3e-ba57-8d1ef2ef1684
	 */
	public void notifyPayWithoutLogs(PaymentBillDTO paymentBillDTO, Boolean ifSuccess, String channel, String operator) {
		log.info("直接远程调用 通知接入方 支付成功  {} {}", paymentBillDTO, ifSuccess);
		PaymentBillDTO update = new PaymentBillDTO();
		update.setPaymentBillId(paymentBillDTO.getPaymentBillId());
		try {
			PaymentNotifyDTO paymentNotifyDTO = new PaymentNotifyDTO();
			paymentNotifyDTO.setAmount(paymentBillDTO.getPayAmount());
			paymentNotifyDTO.setBizId(paymentBillDTO.getSrcBizNo());
			paymentNotifyDTO.setChannel(channel);
			paymentNotifyDTO.setIfSuccess(ifSuccess);
			paymentNotifyDTO.setOperator(operator);
			paymentNotifyDTO.setPaymentBillId(paymentBillDTO.getPaymentBillId());
			paymentNotifyDTO.setPaymentBillNo(paymentBillDTO.getPaymentBillNo());
			paymentNotifyDTO.setPaymentBizCode(paymentBillDTO.getPaymentBizCode());
			paymentIntegrationBiz.paymentResultCallback(paymentNotifyDTO);

			log.info("回调支付接入方成功 paymentBillDTO: {}, flag: {}", paymentBillDTO, ifSuccess);
			update.setNotifyBusStatus(PaymentCallbackEnum.BIZ_CALL_SUCCESS.getCode());
			paymentBillBiz.updatePaymentBill(update, operator);
		} catch (Exception e) {
			log.error(" 回调 支付接入方失败 {} {}", e.getMessage(), e.getCause());
			update.setNotifyBusStatus(PaymentCallbackEnum.BIZ_CALL_FAILED.getCode());
			update.setWarningCode(PaymentWarningCodeEnum.UPDATE_PAYBILLINFO_FAIL.getCode());
			paymentBillBiz.updatePaymentBill(update, operator);
		}
	}

	/**
	 * @param paymentBillDTO
	 * @param ifSuccess
	 * @pdOid 9ce91b21-1dbc-4e15-9f98-61cf0e879487
	 */
	public void notifyRefund(PaymentBillDTO paymentBillDTO, Boolean ifSuccess, String channel, String operator) {
		this.notifyByRemoteInvoke(paymentBillDTO, ifSuccess, channel, operator);
	}

	/**
	 * @param paymentBillDTO
	 * @param ifSuccess
	 * @pdOid 82497b52-eedb-4cbb-b335-8d40e3bca7d7
	 */
	public void notifyFreeze(PaymentBillDTO paymentBillDTO, Boolean ifSuccess, String channel, String operator) {
		this.notifyByRemoteInvoke(paymentBillDTO, ifSuccess, channel, operator);
	}

	@Override
	public PaymentResponseDTO handleUnFreeze(PaymentBillDTO paymentBillDTO, String returnUrl, BigDecimal unfreezeAmount, String operator) {
		return null;
	}

	/**
	 * @param paymentBillDTO
	 * @param ifSuccess
	 * @pdOid 488f69ec-62dd-46b7-93cb-4f7676572a50
	 */
	public void notifyPayDeposit(PaymentBillDTO paymentBillDTO, Boolean ifSuccess, String channel, String operator) {
		this.notifyByRemoteInvoke(paymentBillDTO, ifSuccess, channel, operator);
	}

	/**
	 * @param paymentBillDTO
	 * @param ifSuccess
	 * @pdOid ce6b8795-1d08-4475-875e-6b18e06b9863
	 */
	public void notifyTransferDeposit(PaymentBillDTO paymentBillDTO, Boolean ifSuccess, String channel,
			String operator) {
		this.notifyByRemoteInvoke(paymentBillDTO, ifSuccess, channel, operator);
	}

	/**
	 * 罚没保证金通知
	 * 
	 * @param paymentBillDTO
	 * @param ifSuccess
	 * @pdOid 02f206b4-e7da-46c1-86c7-000cdb9b4dcb
	 */
	public void notifyConfiscateDeposit(PaymentBillDTO paymentBillDTO, Boolean ifSuccess, String channel,
			String operator) {
		this.notifyByRemoteInvoke(paymentBillDTO, ifSuccess, channel, operator);
	}

	/**
	 * @param paymentBillDTO
	 * @param ifSuccess
	 * @pdOid e63da48d-5e06-4d9d-bc23-440680d6e72b
	 */
	public void notifyUnFreeze(PaymentBillDTO paymentBillDTO, Boolean ifSuccess, String channel, String operator) {
		this.notifyByRemoteInvoke(paymentBillDTO, ifSuccess, channel, operator);
	}

	protected PaymentCallbackResponseDTO createSuccessCallbackResponse(
			PaymentCallbackResponseDTO paymentCallbackResponseDTO) {
		paymentCallbackResponseDTO.setCode(OperatorStatusEnum.OK.getCode());
		return paymentCallbackResponseDTO;
	}

	@Override
    public MemberChannelResponseDTO handleSearchCloseMemberChannelStatus(
			MemberChannelRequestDTO memberChannelRequestDTO, String operator) {
		MemberChannelResponseDTO response = new MemberChannelResponseDTO();
		response.setMemberChannelDTO(memberChannelRequestDTO.getMemberChannelDTO());
		return response;
	}

	@Override
    public MemberChannelResponseDTO handleSearchOpenMemberChannelStatus(MemberChannelRequestDTO memberChannelRequestDTO,
                                                                        String operator) {
		MemberChannelResponseDTO response = new MemberChannelResponseDTO();
		response.setMemberChannelDTO(memberChannelRequestDTO.getMemberChannelDTO());
		return response;
	}

	@Override
	public PasswordResponseDTO handleSetPassword(PasswordRequestDTO passwordRequest, MemberChannelDTO memberChannelDTO) {

		return null;
	}

	@Override
	public PasswordCallbackResponseDTO handlePasswordCallback(PasswordCallbackRequestDTO passwordCallbackRequest) {

		return null;
	}

	@Override
	public PaymentCallbackResponseDTO handleConfirmPayCallback(PaymentCallbackRequestDTO paymentRequest) {
		throw new BizException(BasicCode.UNDEFINED_ERROR,"未实现");
	}

	@Override
	public ApplicationTextMsgDynamicCodeResponseDTO applicationTextMsgDynamicCode(String tranNetMemberCode, String subAcctNo, String tranType, String tranAmt) {
		throw new BizException(BasicCode.UNDEFINED_ERROR,"未实现");
	}

	@Override
	public SendVerificationCodeResponseDTO verificationCode(SendVerificationCodeRequestDTO requestDTO) {
		throw new BizException(BasicCode.UNDEFINED_ERROR,"未实现");
	}

	@Override
	public List<ChannelCardDTO> getChannelCardByMemberChannelId(String memberChannelId) {
		return this.channelCardBiz.getChannelCardByMemberChannelId(memberChannelId);
	}

	@Override
	public ChannelCardDTO getChannelCardById(String channelCardId) {
		return this.channelCardBiz.getChannelCardById(channelCardId);
	}

	protected ChannelCardResponseDTO createChannelCardResponseDTO(ChannelCardRequestDTO request) {
		ChannelCardResponseDTO response = new ChannelCardResponseDTO();
		response.setChannelCardDTO(request.getChannelCardDTO());
		response.setResult(true);
		return response;
	}

	@Override
	public ChannelCardResponseDTO handleAddChannelCard(ChannelCardRequestDTO request, MemberChannelDTO memberChannelDTO, String operator) {
		int cardsCount = getCardsCount(request.getMemberChannelId());
		if (cardsCount >= MAX_CARDS) {
			throw new BizException(PayCode.INVALID_PARAM, "最多只能添加10张银行卡");
		}
		ChannelCardDTO channelCardDTO = request.getChannelCardDTO();
		if (cardsCount == 0) {
			channelCardDTO.setExt1(ChannelCardBiz.DEFAULT_SIGN);
		}
		channelCardDTO.setBankAccountName(memberChannelDTO.getAccountName());
		channelCardDTO.setMemberChannelNo(memberChannelDTO.getMemberChannelCode());
		channelCardDTO.setMemberChannelId(memberChannelDTO.getMemberChannelId());
		channelCardDTO.setMemberId(memberChannelDTO.getMemberId());
		channelCardDTO.setMemberName(memberChannelDTO.getMemberName());
		channelCardDTO.setLastVerifyTime(new Date());
		channelCardDTO.setPassVerifyTime(new Date());
		channelCardDTO.setChannelCardStatus(ChannelCardStatusEnum.OPEN.getCode());
		channelCardDTO.setExt2(null);
		this.channelCardBiz.addChannelCard(channelCardDTO, operator);
		return createChannelCardResponseDTO(request);
	}

	protected int getCardsCount(String memberChannelId) {
		return this.channelCardBiz.getChannelCardCountByMemberChannelId(memberChannelId);
	}

	@Override
	public ChannelCardResponseDTO handleVerifyChannelCard(ChannelCardRequestDTO request,
			MemberChannelDTO memberChannelDTO, String verifyCode, String operator) {
		return createChannelCardResponseDTO(request);
	}

	@Override
	public ChannelCardResponseDTO handleRemoveChannelCard(ChannelCardRequestDTO request,
			MemberChannelDTO memberChannelDTO, String operator) {
		ChannelCardDTO channelCardDTO = request.getChannelCardDTO();
		this.channelCardBiz.removeChannelCard(channelCardDTO.getChannelCardId(), operator);
		return createChannelCardResponseDTO(request);
	}

	@Override
	public RechargeResponseDTO handleRecharge(RechargeRequestDTO rechargeRequestDTO) {
		return null;
	}

	@Override
	public RechargeCallbackResponseDTO rechargeCallback(RechargeCallbackRequestDTO rechargeCallbackRequestDTO) {
		return null;
	}

	@Override
	public PasswordResponseDTO handleCashWithdrawal(MemberChannelDTO memberChannelDTO, ChannelCardDTO channelCardDTO,
                                                    CashWithdrawalBillDTO paymentBillDTO, String returnUrl) {
		return null;
	}

	@Override
	public void cashWithdrawalResultCheck(String payerMemberId) {
		//do nothing
	}

	@Override
	public PaymentResponseDTO handleConfirmPay(SplitPayBillDTO splitPayBillDTO, String operatorId) {
		PaymentResponseDTO res = new PaymentResponseDTO();
		res.setSuccess(true);
		return res;
	}

	@Override
	public SplitPayCompleteResponseDTO splitCompleteNotify(SplitPayCompleteRequestDTO dto) {
		return null;
	}

	@Override
    public Map<String, Object> getMemberChannelSpecialInfo(MemberChannelDTO memberChannelDTO) {
        return null;
    }

    @Override
	public DirectPayResponseDTO handleDirectPay(DirectPayRequestDTO directPayRequestDTO) {
		return null;
	}

	@Override
	public DownloadStatementDTO handleDownloadStatement(MemberChannelDTO memberChannelDTO, Date targetDate, PaymentTypeEnum paymentTypeEnum) {
		return null;
	}

	@Override
	public AutoPaymentResponseDTO handleAutoPayment(PaymentBillDTO paymentBillDTO, MemberChannelDTO payerMemberChannelDTO,String operator) {
		AutoPaymentResponseDTO res = new AutoPaymentResponseDTO();
		res.setSuccess(false);
		res.setComment("没有实现");
		return res;
	}

    @Override
    public PaymentCallbackResponseDTO handleOrderCreateCallback(PaymentBillDTO paymentBillDTO, Boolean success, String operatorId) {
		PaymentCallbackResponseDTO dto = new PaymentCallbackResponseDTO();
		dto.setCode(OperatorStatusEnum.OK.getCode());
        return dto;
    }

    @Override
    public PaymentResponseDTO handleRefundByOrderNo(RefundBillDTO refundBillDTO, String operator) {
        return null;
    }

	@Override
	public MemberChannelCheckDTO handleCheckMemberAllowUnBind(String memberId) {
		return null;
	}

	@Override
	public SmallAmountTransferDTO findSmallAmountTransfer(SmallAmountTransferDTO smallAmountTransferDTO) {
		SmallAmountTransferDTO result = new SmallAmountTransferDTO();
		result.setTxnReturnCode("999999");
		result.setTxnReturnMsg("服务调用失败");
		log.error("需要具体子类实现该方法");
		return result;
	}


	protected ValidateResultDTO createSuccessValidate() {
		ValidateResultDTO dto = new ValidateResultDTO();
		dto.setSuccess(true);
		return dto;
	}

	protected static void checkObjectNull(Object object, String msg) {
		if (object == null) {
            throw new BizException(BasicCode.PARAM_NULL, CsStringUtils.isEmpty(msg) ? " " : msg);
		}
        if ((object instanceof String) && CsStringUtils.isBlank((String) object)) {
            throw new BizException(BasicCode.PARAM_NULL, CsStringUtils.isEmpty(msg) ? " " : msg);
		}
	}


	/**
	 * erp入账
	 * @param splitPayBillDTO 分账单
	 */
	// TODO: 重构此方法以降低认知复杂度 (当前: 31, 目标: ≤15)
	// 建议: 1) 提取子方法 2) 使用早期返回 3) 简化条件表达式
	public void notifyBalanceChange(SplitPayBillDTO splitPayBillDTO) {
		try {
			log.info(" >> 入账通知");
			log.info(" == 开始ERP入账 == ");
			String payerMemberId = splitPayBillDTO.getPayerMemberId();
			List<SplitPayBillDetailDTO> detailDTOList = splitPayBillDTO.getDetailDTOList();
			if (CollectionUtils.isEmpty(detailDTOList)) {
				return;
			}
			for (SplitPayBillDetailDTO detailDTO : detailDTOList) {
				String payeeMemberId = detailDTO.getPayeeMemberId();
				BigDecimal amount = detailDTO.getPayAmount();
				MemberConfigDTO configDTO = memberConfigService.findByMemberIdAndKeyCode(payeeMemberId, MemberConfigEnum.ERP_PAYMENT_FLG.getKeyCode());
				if (configDTO == null || Boolean.TRUE.equals(configDTO.getDelFlg())) {
					log.info("该卖家 {}, 没有接入支付erp", payeeMemberId);
					continue;
				}
				// 查询买家是否有erp
				MemberRelationQueryDTO queryDTO = new MemberRelationQueryDTO();
				queryDTO.setCustomerId(payerMemberId);
				queryDTO.setMemberId(payeeMemberId);
				List<MemberRelationDTO> relationMemberList = memberRelationService.findRelationMemberList(queryDTO);
				if (CollectionUtils.isEmpty(relationMemberList)) {
					log.info("该买家 {} 与该卖家 {} 没有会员关系", payerMemberId, payeeMemberId);
					continue;
				}
				MemberRelationDTO t = null;
				for (MemberRelationDTO dto : relationMemberList) {
                    if (!CsStringUtils.isEmpty(dto.getMdmCode())) {
						t = dto;
						break;
					}
				}
				if (t == null) {
					log.info("该买家 {} 不是该卖家 {} erp会员", payerMemberId, payeeMemberId);
					continue;
				}
				String amountString = new DecimalFormat("#").format(amount.multiply(new BigDecimal("100")).stripTrailingZeros());
				String date = DateUtil.format(new Date(), "yyyyMMddHHmmss");
				ERPBalanceChangeNotifyRequestDTO requestDTO = new ERPBalanceChangeNotifyRequestDTO();
				requestDTO.setAmount(amountString);
				requestDTO.setCurrencyCode("12");
				requestDTO.setGlDate(date);
				requestDTO.setReceiptDate(date);
				requestDTO.setCustomerNumber(t.getMdmCode());
				requestDTO.setPaymentBillNum(splitPayBillDTO.getSplitPayBillNo());
				// 卖家账户
				List<MemberConfigDTO> codes = memberConfigService.findByMemberIdAndKeyCodes(payeeMemberId,
						Arrays.asList(
								MemberConfigEnum.CUSTOMER_BANK_ACCOUNT_ID.getKeyCode(),
								MemberConfigEnum.CUSTOMER_BANK_ACCOUNT_NUM.getKeyCode(),
								MemberConfigEnum.CUSTOMER_BANK_ACCOUNT_NAME.getKeyCode()
						));
				for (MemberConfigDTO code : codes) {
					if (code.getKeyCode().equals(MemberConfigEnum.CUSTOMER_BANK_ACCOUNT_ID.getKeyCode())) {
						requestDTO.setCustomerBankAccountId(code.getValue());
					} else if (code.getKeyCode().equals(MemberConfigEnum.CUSTOMER_BANK_ACCOUNT_NUM.getKeyCode())) {
						requestDTO.setCustomerBankAccountNum(code.getValue());
					} else if (code.getKeyCode().equals(MemberConfigEnum.CUSTOMER_BANK_ACCOUNT_NAME.getKeyCode())) {
						requestDTO.setCustomerBankAccountName(code.getValue());
					}
				}

				String orgCode = splitPayBillDTO.getOrgCode();

                if (CsStringUtils.isEmpty(orgCode)) {
					splitPayBillDTO.setRemarks("虚拟子账户资金入账通知失败: orgCode 为空");
					log.info("虚拟子账户资金入账通知失败: orgCode 为空");
					continue;
				}

				requestDTO.setOrgCode(orgCode);
				requestDTO.setWaybillCode(splitPayBillDTO.getRemarks());
				requestDTO.setEcOrderCode(splitPayBillDTO.getOrderNo());

				ApiResult apiResult;
				String jsonString = "";
				try {
					jsonString = JSON.toJSONString(requestDTO);
					log.info("虚拟子账户资金入账通知ERP - request: {}", jsonString);
					apiResult = openAPIInvokeService.invoke(BizCodeEnum.EC_MON_T0.getCode(), payeeMemberId, jsonString);
					if (apiResult.isSuccess()) {
						ERPBalanceChangeNotifyResponseDTO resultDTO = apiResult.getDTO(ERPBalanceChangeNotifyResponseDTO.class);
						log.info("虚拟子账户资金入账通知ERP - response: {}", resultDTO);
					} else {
						PaymentExceptionDTO dto = new PaymentExceptionDTO();
						dto.setExContent(apiResult.getDescription());
						dto.setParam(jsonString);
						dto.setObjId(payeeMemberId);
						dto.setOrderNo(splitPayBillDTO.getOrderNo());
						dto.setInterfaceName(BizCodeEnum.EC_MON_T0.getCode());
						dto.setOperateType(ExceptionTypeEnum.NOTIFY_ERR.getCode());
						dto.setRetryCount(0);
						paymentExceptionBiz.create(dto, payeeMemberId);
					}
				} catch (Exception e) {
					splitPayBillDTO.setRemarks("虚拟子账户资金入账通知失败: " + e.getMessage());
					log.info("虚拟子账户资金入账通知失败: {}", e.getMessage());
					PaymentExceptionDTO dto = new PaymentExceptionDTO();
					dto.setExContent(e.getMessage());
					dto.setParam(jsonString);
					dto.setObjId(payeeMemberId);
					dto.setOrderNo(splitPayBillDTO.getOrderNo());
					dto.setInterfaceName(BizCodeEnum.EC_MON_T0.getCode());
					dto.setOperateType(ExceptionTypeEnum.NOTIFY_ERR.getCode());
					dto.setRetryCount(0);
					paymentExceptionBiz.create(dto, payeeMemberId);
				}
			}
		}catch (Exception e){
			if(!"prod".equals(profile)){
				log.error(" == ERP入账失败 == {}",e.getMessage(),e);
			}
			log.info(" == ERP入账失败 == {}", e.getMessage());
		}
	}

	protected String getSubjectInfo(String subject){
        if (CsStringUtils.isBlank(subject)) {
			return null;
		}
		StringBuilder stringBuilder = new StringBuilder();
		for (String s : subject.split(",")) {
			stringBuilder.append(getSubject(s)).append(",");
		}
		stringBuilder.deleteCharAt(stringBuilder.length()-1);
		return stringBuilder.toString();
	}

	private String getSubject(String subject){
		PayDetailTypeEnum itemEnum = PayDetailTypeEnum.getByCode(subject);
		if( itemEnum != null ){
			return itemEnum.getMessage();
		}
		return subject;
	}
}
