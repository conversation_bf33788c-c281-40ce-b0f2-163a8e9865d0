package com.ecommerce.pay.v2.channel.adapter.erp;

import com.alibaba.fastjson.JSON;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.member.api.dto.member.config.MemberConfigDTO;
import com.ecommerce.member.api.dto.member.config.MemberConfigEnum;
import com.ecommerce.member.api.service.IMemberConfigService;
import com.ecommerce.open.api.dto.apicenter.ApiResult;
import com.ecommerce.open.api.dto.apicenter.pay.ERPBalanceQueryDTO;
import com.ecommerce.open.api.dto.apicenter.pay.ERPBalanceResultDTO;
import com.ecommerce.open.api.service.apicenter.IOpenAPIInvokeService;
import com.ecommerce.open.enums.BizCodeEnum;
import com.ecommerce.pay.api.exception.PayCode;
import com.ecommerce.pay.api.v2.dto.AutoPaymentResponseDTO;
import com.ecommerce.pay.api.v2.dto.HealthResponseDTO;
import com.ecommerce.pay.api.v2.dto.MemberChannelCallbackRequestDTO;
import com.ecommerce.pay.api.v2.dto.MemberChannelCallbackResponseDTO;
import com.ecommerce.pay.api.v2.dto.MemberChannelDTO;
import com.ecommerce.pay.api.v2.dto.MemberChannelRequestDTO;
import com.ecommerce.pay.api.v2.dto.MemberChannelResponseDTO;
import com.ecommerce.pay.api.v2.dto.PaymentCallbackRequestDTO;
import com.ecommerce.pay.api.v2.dto.PaymentCallbackResponseDTO;
import com.ecommerce.pay.api.v2.dto.PaymentRequestWrapDTO;
import com.ecommerce.pay.api.v2.dto.PaymentResponseDTO;
import com.ecommerce.pay.api.v2.dto.RefundRequestWrapDTO;
import com.ecommerce.pay.api.v2.dto.ValidateResultDTO;
import com.ecommerce.pay.api.v2.dto.bill.PaymentBillDTO;
import com.ecommerce.pay.api.v2.dto.bill.PaymentBillDetailDTO;
import com.ecommerce.pay.api.v2.dto.bill.RefundBillDTO;
import com.ecommerce.pay.api.v2.dto.constant.PayConstant;
import com.ecommerce.pay.api.v2.enums.ChannelCodeEnum;
import com.ecommerce.pay.api.v2.enums.MemberChannelStatusEnum;
import com.ecommerce.pay.api.v2.enums.OperatorStatusEnum;
import com.ecommerce.pay.api.v2.enums.PaymentPageBehaviorEnum;
import com.ecommerce.pay.api.v2.enums.PaymentStatusEnum;
import com.ecommerce.pay.v2.biz.IBillLogsBiz;
import com.ecommerce.pay.v2.channel.AbstractBaseAdapter;
import com.ecommerce.pay.v2.dao.vo.MemberChannel;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * @created 15:12 21/03/2019
 * @description
 */
@Slf4j
@Service("erp")
public class ErpAdapter extends AbstractBaseAdapter {

    @Autowired
    private IOpenAPIInvokeService iOpenAPIInvokeService;

    @Autowired
    private IMemberConfigService iMemberConfigService;

    @Autowired
    private ThreadPoolExecutor executor;

    @Lazy
    @Autowired
    private IBillLogsBiz iBillLogsBiz;

    private ValidateResultDTO createDefaultValidate() {
        ValidateResultDTO resultDTO = new ValidateResultDTO();
        resultDTO.setSuccess(true);
        resultDTO.setCode(OperatorStatusEnum.OK.getCode());
        return resultDTO;
    }

    @Override
    public ValidateResultDTO validateMemberChannelWhenOpen(MemberChannelRequestDTO memberChannelDTO) {
        return createDefaultValidate();
    }

    @Override
    public ValidateResultDTO validatePaymentBillWhenPay(PaymentRequestWrapDTO paymentRequest) {
        return createDefaultValidate();
    }

    @Override
    public ValidateResultDTO validatePaymentBillWhenRefund(RefundRequestWrapDTO paymentRequest) {
        return createDefaultValidate();
    }

    @Override
    public ValidateResultDTO validatePaymentBillWhenFreeze(PaymentRequestWrapDTO paymentRequest) {
        return createDefaultValidate();
    }

    @Override
    public ValidateResultDTO validatePaymentBillWhenUnFreeze(PaymentRequestWrapDTO paymentRequest) {
        return createDefaultValidate();
    }

    @Override
    public ValidateResultDTO validatePaymentBillWhenPayDeposit(PaymentRequestWrapDTO paymentRequest) {
        return createDefaultValidate();
    }

    @Override
    public ValidateResultDTO validatePaymentBillWhenCancelDeposit(PaymentRequestWrapDTO paymentRequest) {
        return createDefaultValidate();
    }

    @Override
    public ValidateResultDTO validatePaymentBillWhenTransferDeposit(PaymentRequestWrapDTO paymentRequest) {
        return createDefaultValidate();
    }

    @Override
    public ValidateResultDTO validatePaymentBillWhenConfiscateDeposit(PaymentRequestWrapDTO paymentRequest) {
        return createDefaultValidate();
    }

    @Override
    public AutoPaymentResponseDTO handleAutoPayment(PaymentBillDTO paymentBillDTO, MemberChannelDTO payerMemberChannelDTO,String operator) {
        payerMemberChannelDTO.setContractCode(paymentBillDTO.getContractCode());
        payerMemberChannelDTO.setMemberMDMCode(paymentBillDTO.getMemberMDMCode());
        payerMemberChannelDTO.setWarehouse(paymentBillDTO.getWarehouse());
        payerMemberChannelDTO.setSaleRegion(paymentBillDTO.getSaleRegion());
        payerMemberChannelDTO.setPickupPointOrgId(paymentBillDTO.getPickupPointOrgId());
        Map<String, Object> memberChannelSpecialInfo = getMemberChannelSpecialInfo(payerMemberChannelDTO);

        if(paymentBillDTO.getSubtractAmount() == null)
        {
            paymentBillDTO.setSubtractAmount(BigDecimal.ZERO);
        }

        // 判断用户是否为月结客户
        boolean isMonthly = paymentBillDTO.getIsMonthly() != null && paymentBillDTO.getIsMonthly() == 1;

        //erp可用余额
        BigDecimal availBlance = isMonthly ? BigDecimal.ZERO : new BigDecimal((String)memberChannelSpecialInfo.getOrDefault(PayConstant.AVAIL_BALANCE, "0"));
        AutoPaymentResponseDTO responseDTO = new AutoPaymentResponseDTO();
        responseDTO.setSuccess(true);
        if(!isMonthly && availBlance.compareTo(paymentBillDTO.getPayAmount().subtract(paymentBillDTO.getSubtractAmount())) <0 ){
            log.info("自动支付失败,ERP余额不足");
            responseDTO.setSuccess(false);
            responseDTO.setComment("ERP余额不足");

            paymentBillDTO.setStatus(PaymentStatusEnum.PAY_FAIL.getCode());
            paymentBillDTO.setRemarks("ERP余额不足");
            paymentBillBiz.updatePaymentBill(paymentBillDTO, operator);
        }else{
            paymentBillDTO.setStatus(PaymentStatusEnum.PAY_SUCCESS.getCode());
            paymentBillDTO.setActualPayAmount(paymentBillDTO.getPayAmount());
            paymentBillDTO.setCompleteTime(new Date());
            paymentBillBiz.updatePaymentBill(paymentBillDTO, operator);
            iBillLogsBiz.logsPayment(paymentBillDTO, paymentBillDTO.getPayerMemberId());
        }
        responseDTO.setOrderNo(paymentBillDTO.getOrderNo());
        return responseDTO;
    }

    @Override
    public Map<String, Object> getMemberChannelSpecialInfo(MemberChannelDTO memberChannelDTO) {
        if (CsStringUtils.isBlank(memberChannelDTO.getExtSupAcctId()) ||
                CsStringUtils.isBlank(memberChannelDTO.getPayeeMemberId())) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "该会员未开通ERP支付渠道");
        }

        if (Boolean.TRUE.equals(memberChannelDTO.getAllowReceive()) && Boolean.FALSE.equals(memberChannelDTO.getAllowPayment())) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "卖家无法查询余额");
        }
        ERPBalanceQueryDTO dto = new ERPBalanceQueryDTO();
        dto.setMemberMDMCode(memberChannelDTO.getExtSupAcctId());
        dto.setSellerId(memberChannelDTO.getPayeeMemberId());

        if (CsStringUtils.isNotBlank(memberChannelDTO.getMemberMDMCode())) {
            dto.setMemberMDMCode(memberChannelDTO.getMemberMDMCode());
        }
        if (CsStringUtils.isNotBlank(memberChannelDTO.getSaleRegion())) {
            dto.setOrgCode(memberChannelDTO.getSaleRegion());
        }
        if (CsStringUtils.isNotBlank(memberChannelDTO.getContractCode())) {
            dto.setErpContractCode(memberChannelDTO.getContractCode());
        }
        if (CsStringUtils.isNotBlank(memberChannelDTO.getWarehouse())) {
            dto.setPickupPointCode(memberChannelDTO.getWarehouse());
        }
        if (CsStringUtils.isNotBlank(memberChannelDTO.getPickupPointOrgId())) {
            dto.setPickupPointOrgId(memberChannelDTO.getPickupPointOrgId());
        }
        return sendRequestErpBalance(dto);
    }

    private Map<String, Object> sendRequestErpBalance(ERPBalanceQueryDTO dto) {
        if (CsStringUtils.isBlank(dto.getOrgCode())) {
            log.info("ERP余额查询 orgCode为空");
        }
        ApiResult apiResult;
        Map<String, Object> map = Maps.newHashMap();
        try {
            long s = System.currentTimeMillis();
            log.info(">>> 向ERP查询余额sellerId: {}, request: {}", dto.getSellerId(), dto);
            //dev环境直接返回一个写死的数据
            if("dev".equals(profile) || "test".equals(profile)){
                BigDecimal credit = new BigDecimal(1000000L);
                BigDecimal balance = new BigDecimal(1000000L);
                BigDecimal baseBalance = new BigDecimal(1000000L);
                // 授信余额
                map.put(PayConstant.CREDIT_TOTAL_AMOUNT, credit.toPlainString());
                // 余额
                map.put(PayConstant.BALANCE_AMOUNT, balance.toPlainString());
                String amount = balance.add(credit).toPlainString();
                // 授信 + 余额 = 总和
                map.put(PayConstant.AVAIL_BALANCE, amount);
                map.put(PayConstant.BASE_BALANCE, baseBalance.setScale(2, RoundingMode.HALF_UP).toPlainString());
                log.info(">>> {} 环境，erp余额mock数据:{}",profile, map);
                return map;
            }
            apiResult = iOpenAPIInvokeService.invoke(BizCodeEnum.EC_FAP_A0.getCode(), dto.getSellerId(), JSON.toJSONString(dto));
            log.info("<<< 向ERP查询余额cost time:{},sellerId: {} result: {}",(System.currentTimeMillis() - s), dto.getSellerId(), apiResult);
            if (apiResult.isSuccess()) {
                ERPBalanceResultDTO resultDTO = apiResult.getDTO(ERPBalanceResultDTO.class);
                BigDecimal credit = new BigDecimal(CsStringUtils.isBlank(resultDTO.getCredit()) ? "0" : resultDTO.getCredit());
                BigDecimal balance = new BigDecimal(CsStringUtils.isBlank(resultDTO.getBalance()) ? "0" : resultDTO.getBalance());
                BigDecimal baseBalance = new BigDecimal(CsStringUtils.isBlank(resultDTO.getBaceBalance()) ? "0" : resultDTO.getBaceBalance());
                // 授信余额
                map.put(PayConstant.CREDIT_TOTAL_AMOUNT, credit.toPlainString());
                // 余额
                map.put(PayConstant.BALANCE_AMOUNT, balance.toPlainString());
                String amount = balance.add(credit).toPlainString();
                // 授信 + 余额 = 总和
                map.put(PayConstant.AVAIL_BALANCE, amount);
                map.put(PayConstant.BASE_BALANCE, baseBalance.setScale(2, RoundingMode.HALF_UP).toPlainString());
                return map;
            }
        } catch (Throwable e) {
            log.info("ERP余额查询失败: {}", e.getMessage(),e);
            throw new BizException(BasicCode.UNDEFINED_ERROR, "ERP余额查询失败");
        }
        throw new BizException(BasicCode.UNDEFINED_ERROR, "ERP余额查询失败");
    }

    @Override
    public PaymentResponseDTO searchPaymentBill(String paymentBillNo, String operator) {
        log.info(" 查询 支付单 paymentBillNo {}", paymentBillNo);
        PaymentResponseDTO paymentResponseDTO = new PaymentResponseDTO();
        if (CsStringUtils.isBlank(paymentBillNo)) {
            throw new BizException(BasicCode.PARAM_NULL);
        }
        PaymentBillDTO paymentBillDTO = paymentBillBiz.findByPaymentBillNo(paymentBillNo);
        if (PaymentStatusEnum.PAY_ING.getCode().equals(paymentBillDTO.getStatus())) {
            paymentBillDTO.setStatus(PaymentStatusEnum.PAY_CLOSE.getCode());
        }
        paymentResponseDTO.setPaymentBillDTO(paymentBillDTO);
        paymentResponseDTO.setSuccess(true);
        return paymentResponseDTO;
    }

    @Override
    public MemberChannelResponseDTO handleOpenMemberChannel(MemberChannelRequestDTO memberChannelRequestDTO, String operator) {
        MemberChannelDTO memberChannelDTO = memberChannelRequestDTO.getMemberChannelDTO();
        Boolean allowPayment = memberChannelDTO.getAllowPayment();// true
        Boolean allowReceive = memberChannelDTO.getAllowReceive();// false
        String memberId = memberChannelDTO.getMemberId();
        if (CsStringUtils.isBlank(memberId)) {
            throw new BizException(BasicCode.PARAM_NULL, "memberId");
        }
        if (Boolean.TRUE.equals(allowPayment) && Boolean.FALSE.equals(allowReceive) && !"system_sync".equals(operator)) {
            // 买家
            throw new BizException(BasicCode.UNDEFINED_ERROR, "当前用户不允许开通该支付方式");
        }
        boolean flag;
        if (Boolean.FALSE.equals(allowPayment) && Boolean.TRUE.equals(allowReceive)) {
            // 卖家
            MemberConfigDTO configDTO = iMemberConfigService.findByMemberIdAndKeyCode(memberId, MemberConfigEnum.ERP_PAYMENT_FLG.getKeyCode());
            flag = configDTO == null || Boolean.TRUE.equals(configDTO.getDelFlg()) || !"1".equals(configDTO.getValue());

            if (flag) {
                if (!CsStringUtils.isBlank(memberChannelDTO.getMemberChannelId())) {
                    MemberChannel vo = new MemberChannel();
                    vo.setMemberChannelId(memberChannelDTO.getMemberChannelId());
                    vo.setMemberChannelStatus(MemberChannelStatusEnum.BE_STOP.getCode());
                    memberChannelBiz.delete(vo);
                }
                throw new BizException(BasicCode.UNDEFINED_ERROR, "当前用户不允许开通该支付方式");
            } else {
                log.info(">>> 新增ERP支付渠道");
                memberChannelDTO.setPayeeMemberId(ChannelCodeEnum.ERP.getCode());
            }
        }
        MemberChannelResponseDTO responseDTO = new MemberChannelResponseDTO();

        if (CsStringUtils.isBlank(memberChannelDTO.getMemberChannelId())) {
            log.info(" 插入erp支付渠道 memberId: {}, payeeMemberId: {}",
                    memberChannelDTO.getMemberId(), memberChannelDTO.getPayeeMemberId());
            memberChannelBiz.insertMemberChannel(memberChannelDTO, operator);
        } else {
            log.info("已存在erp支付渠道 更新 memberId: {}, payeeMemberId: {}",
                    memberChannelDTO.getMemberId(), memberChannelDTO.getPayeeMemberId());
            memberChannelBiz.updateMemberChannel(memberChannelDTO, operator);
            responseDTO.setOpenFlag(1);
        }
        responseDTO.setMemberChannelDTO(memberChannelDTO);
        return responseDTO;
    }

    @Override
    public MemberChannelCallbackResponseDTO handleOpenMemberChannelCallback(MemberChannelCallbackRequestDTO memberChannelDTO) {
        return null;
    }

    @Override
    public MemberChannelResponseDTO handleCloseMemberChannel(MemberChannelRequestDTO memberChannelRequestDTO, String operator) {
        throw new BizException(PayCode.MEMBER_CHANNEL_CLOSE_FAIL, "当前渠道不允许解绑");
    }

    @Override
    public MemberChannelCallbackResponseDTO handleCloseMemberChannelCallback(MemberChannelCallbackRequestDTO memberChannelDTO) {
        return null;
    }

    @Override
    public PaymentResponseDTO handlePay(PaymentRequestWrapDTO paymentRequest, String operator) {
        PaymentBillDTO paymentBillDTO = paymentRequest.getPaymentBillDTO();

        String payeeMemberId = paymentBillDTO.getPayeeMemberId();

        MemberChannelDTO payerMemberChannelDTO = memberChannelBiz.findMemberChannelByPayee(paymentBillDTO.getPayerMemberId(), payeeMemberId, ChannelCodeEnum.ERP);
        if (payerMemberChannelDTO == null) {
            throw new BizException(BasicCode.DB_DATA_NOT_FOUND, "买家未开通ERP支付");
        }


        // 判断用户是否为月结客户
        boolean isMonthly = paymentRequest.getIsMonthly() != null && paymentRequest.getIsMonthly() == 1;

        BigDecimal availBalance = BigDecimal.ZERO;//erp系统已关闭，此处模拟erp余额查询
        if("dev".equals(profile) || "test".equals(profile) ){
            availBalance = new BigDecimal(1000000L);//erp系统已关闭，此处模拟erp余额查询
            log.info("erp系统已关闭，此处模拟erp余额查询 availBlance = 1000000");
        } else if(!isMonthly){
            payerMemberChannelDTO.setContractCode(paymentBillDTO.getContractCode());
            payerMemberChannelDTO.setMemberMDMCode(paymentBillDTO.getMemberMDMCode());
            payerMemberChannelDTO.setSaleRegion(paymentBillDTO.getSaleRegion());
            payerMemberChannelDTO.setWarehouse(paymentBillDTO.getWarehouse());
            payerMemberChannelDTO.setPickupPointOrgId(paymentBillDTO.getPickupPointOrgId());
            Map<String, Object> memberChannelSpecialInfo = getMemberChannelSpecialInfo(payerMemberChannelDTO);
            //erp可用余额
            availBalance = new BigDecimal((String) memberChannelSpecialInfo.getOrDefault(PayConstant.AVAIL_BALANCE, "0"));
            log.info("erp系统余额查询 availBalance = {}",availBalance);
        }

        if(paymentRequest.getSubtractAmount() == null)
        {
            paymentRequest.setSubtractAmount(BigDecimal.ZERO);
        }

        log.info("ERP支付 isMonthly:{}, availBalance:{}, payAmount:{}, subtractAmount:{}, realPayAmount:{}", isMonthly, availBalance, paymentBillDTO.getPayAmount(), paymentRequest.getSubtractAmount(), paymentBillDTO.getPayAmount().subtract(paymentRequest.getSubtractAmount()));

        PaymentResponseDTO responseDTO = new PaymentResponseDTO();
        responseDTO.setSuccess(true);
        responseDTO.setContent("支付成功");
        if(!isMonthly && availBalance.compareTo(paymentBillDTO.getPayAmount().subtract(paymentRequest.getSubtractAmount())) <0 ){
            log.info("自动支付失败,ERP余额不足");
            responseDTO.setSuccess(false);
            responseDTO.setContent("ERP余额不足");

            paymentBillDTO.setStatus(PaymentStatusEnum.PAY_FAIL.getCode());
            paymentBillDTO.setRemarks("ERP余额不足");
        }else{
            paymentBillDTO.setStatus(PaymentStatusEnum.PAY_SUCCESS.getCode());
            paymentBillDTO.setActualPayAmount(paymentBillDTO.getPayAmount());
            paymentBillDTO.setCompleteTime(new Date());
            for (PaymentBillDetailDTO detailDTO : paymentBillDTO.getDetailDTOList()) {
                detailDTO.setActualPayAmount(detailDTO.getPayAmount());
            }
        }
        paymentBillBiz.updatePaymentBill(paymentBillDTO, operator);

        notifyPaymentResult(paymentBillDTO, paymentRequest ,operator);
        notifyPayWithoutLogs(paymentRequest.getPaymentBillDTO(),responseDTO.isSuccess(), ChannelCodeEnum.ERP.getCode(),operator);
        if( responseDTO.isSuccess() ){
           //见handleOrderCreateCallback方法，erp扣款会在erp订单创建完成时回调，然后生成支付流水
        }
        responseDTO.setChangeStatus(true);
        responseDTO.setBehavior(PaymentPageBehaviorEnum.TO_SUCCESS.getCode());
        return responseDTO;
    }

    @Override
    public PaymentCallbackResponseDTO handlePayCallback(PaymentCallbackRequestDTO paymentRequest) {
        return null;
    }

    @Override
    public PaymentCallbackResponseDTO handleOrderCreateCallback(PaymentBillDTO paymentBillDTO, Boolean success, String operatorId) {
        if (success) {
            paymentBillDTO.setStatus(PaymentStatusEnum.PAY_SUCCESS.getCode());
            paymentBillDTO.setActualPayAmount(paymentBillDTO.getPayAmount());
            List<PaymentBillDetailDTO> dtoList = paymentBillDTO.getDetailDTOList();
            PaymentBillDetailDTO payeeBillDetail = new PaymentBillDetailDTO();
            if (!CollectionUtils.isEmpty(dtoList)) {
                for (PaymentBillDetailDTO dto : dtoList) {
                    dto.setActualPayAmount(dto.getPayAmount());
                    if (dto.getPayeeMemberId().equals(paymentBillDTO.getPayeeMemberId())) {
                        BeanUtils.copyProperties(dto, payeeBillDetail);
                        payeeBillDetail.setActualPayAmount(paymentBillDTO.getActualPayAmount());
                        payeeBillDetail.setPayAmount(paymentBillDTO.getActualPayAmount());
                    }
                }
            }
            ArrayList<PaymentBillDetailDTO> list = Lists.newArrayList(payeeBillDetail);
            paymentBillDTO.setDetailDTOList(list);
            super.logsPay(paymentBillDTO, operatorId);
            paymentBillDTO.setDetailDTOList(dtoList);
        }
        return this.createSuccessCallbackResponse(new PaymentCallbackResponseDTO());
    }

    @Override
    public PaymentResponseDTO handleRefund(RefundRequestWrapDTO paymentRequest, String operator) {
        RefundBillDTO refundBillDTO = paymentRequest.getRefundBillDTO();

        refundBillDTO.setStatus(PaymentStatusEnum.PAY_SUCCESS.getCode());
        refundBillDTO.setActualPayAmount(refundBillDTO.getPayAmount());
        refundBillBiz.update(refundBillDTO, operator);

        super.logsRefund(refundBillDTO, operator);

        PaymentResponseDTO responseDTO = new PaymentResponseDTO();
        responseDTO.setSuccess(true);
        responseDTO.setChangeStatus(true);
        responseDTO.setBehavior(PaymentPageBehaviorEnum.TO_SUCCESS.getCode());
        return responseDTO;
    }

    @Override
    public PaymentCallbackResponseDTO handleRefundCallback(PaymentCallbackRequestDTO paymentRequest) {
        return null;
    }

    @Override
    public PaymentResponseDTO handleFreeze(PaymentRequestWrapDTO paymentRequest, String operator) {
        return null;
    }

    @Override
    public PaymentCallbackResponseDTO handleFreezeCallback(PaymentCallbackRequestDTO paymentRequest) {
        return null;
    }

    @Override
    public PaymentCallbackResponseDTO handleUnFreezeCallback(PaymentCallbackRequestDTO paymentRequest) {
        return null;
    }

    @Override
    public PaymentResponseDTO handlePayDeposit(PaymentRequestWrapDTO payRequest, String operator) {
        return null;
    }

    @Override
    public PaymentCallbackResponseDTO handlePayDepositCallBack(PaymentCallbackRequestDTO payRequest) {
        return null;
    }

    @Override
    public PaymentResponseDTO handleCancelDeposit(PaymentRequestWrapDTO payRequest, String operator) {
        return null;
    }

    @Override
    public PaymentCallbackResponseDTO handleCancelDepositCallBack(PaymentCallbackRequestDTO payRequest) {
        return null;
    }

    @Override
    public PaymentResponseDTO handleConfiscateDeposit(PaymentRequestWrapDTO payRequest, String operator) {
        return null;
    }

    @Override
    public PaymentCallbackResponseDTO handleConfiscateDepositCallBack(PaymentCallbackRequestDTO payRequest) {
        return null;
    }

    @Override
    public PaymentResponseDTO handleTransferDeposit(PaymentRequestWrapDTO payRequest, String operator) {
        return null;
    }

    @Override
    public PaymentCallbackResponseDTO handleTransferDepositCallBack(PaymentCallbackRequestDTO payRequest) {
        return null;
    }

    @Override
    public PaymentResponseDTO handleCancelPayment(PaymentRequestWrapDTO paymentRequest, String operator) {
        return null;
    }

    @Override
    public HealthResponseDTO checkHealth() {
        return null;
    }
}
