package com.ecommerce.pay.v2.channel.adapter.offline;

import com.alibaba.fastjson.JSON;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.pay.api.exception.PayCode;
import com.ecommerce.pay.api.v2.dto.ChannelCardDTO;
import com.ecommerce.pay.api.v2.dto.ChannelCardRequestDTO;
import com.ecommerce.pay.api.v2.dto.ChannelCardResponseDTO;
import com.ecommerce.pay.api.v2.dto.HealthResponseDTO;
import com.ecommerce.pay.api.v2.dto.MemberChannelCallbackRequestDTO;
import com.ecommerce.pay.api.v2.dto.MemberChannelCallbackResponseDTO;
import com.ecommerce.pay.api.v2.dto.MemberChannelDTO;
import com.ecommerce.pay.api.v2.dto.MemberChannelRequestDTO;
import com.ecommerce.pay.api.v2.dto.MemberChannelResponseDTO;
import com.ecommerce.pay.api.v2.dto.PaymentCallbackRequestDTO;
import com.ecommerce.pay.api.v2.dto.PaymentCallbackResponseDTO;
import com.ecommerce.pay.api.v2.dto.PaymentNotifyDTO;
import com.ecommerce.pay.api.v2.dto.PaymentRequestWrapDTO;
import com.ecommerce.pay.api.v2.dto.PaymentResponseDTO;
import com.ecommerce.pay.api.v2.dto.RefundRequestWrapDTO;
import com.ecommerce.pay.api.v2.dto.ValidateResultDTO;
import com.ecommerce.pay.api.v2.dto.bill.PaymentBillDTO;
import com.ecommerce.pay.api.v2.dto.bill.PaymentBillDetailDTO;
import com.ecommerce.pay.api.v2.dto.bill.RefundBillDTO;
import com.ecommerce.pay.api.v2.enums.ChannelCardStatusEnum;
import com.ecommerce.pay.api.v2.enums.ChannelCodeEnum;
import com.ecommerce.pay.api.v2.enums.MemberChannelStatusEnum;
import com.ecommerce.pay.api.v2.enums.PaymentCallbackEnum;
import com.ecommerce.pay.api.v2.enums.PaymentPageBehaviorEnum;
import com.ecommerce.pay.api.v2.enums.PaymentStatusEnum;
import com.ecommerce.pay.api.v2.enums.PaymentWarningCodeEnum;
import com.ecommerce.pay.v2.biz.IPaymentIntegrationBiz;
import com.ecommerce.pay.v2.biz.impl.ChannelCardBiz;
import com.ecommerce.pay.v2.channel.AbstractBaseAdapter;
import lombok.extern.slf4j.Slf4j;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Service("offline")
@Slf4j
public class OfflineAdapter extends AbstractBaseAdapter {

	@Autowired
	private IPaymentIntegrationBiz iPaymentIntegrationBiz;
	
	private ValidateResultDTO validateMemberChannel(MemberChannelRequestDTO memberChannelRequestDTO) {
		ValidateResultDTO result = new ValidateResultDTO();
		result.setSuccess(false);
		log.info(" 验证 memberChannelRequestDTO {} ", memberChannelRequestDTO);
		if (memberChannelRequestDTO == null) {
			result.setMessage(" 会员渠道请求数据为空 ");
			return result;
		}
		if (memberChannelRequestDTO.getMemberChannelDTO() == null) {
			result.setMessage(" 会员dto为空 ");
			return result;
		}
        if (CsStringUtils.isEmpty(memberChannelRequestDTO.getMemberChannelDTO().getMemberId())) {
			result.setMessage(" 会员id为空 ");
			return result;
		}

		result.setSuccess(true);
		return result;
	}

	private ValidateResultDTO validatePaymentBill(PaymentRequestWrapDTO paymentRequest) {
		log.info(" 验证 paymentRequest {} ", paymentRequest);
		ValidateResultDTO result = new ValidateResultDTO();
		result.setSuccess(false);
		if (paymentRequest == null) {
			log.error("  支付请求数据为空 ");
			result.setMessage(" 支付请求数据为空 ");

			result.setCode(PayCode.PARAM_NULL.getCode());
			return result;
		}
		if (paymentRequest.getPaymentBillDTO() == null) {
			log.error("  支付单数据为空 ");
			result.setMessage(" 支付单数据为空 ");
			result.setCode(PayCode.PARAM_NULL.getCode());
			return result;
		}

		BigDecimal payAmount = paymentRequest.getPaymentBillDTO().getPayAmount();

		if (payAmount == null) {
			result.setMessage(" 交易金额为空 ");
			log.error("  交易金额为空 ");
			result.setCode(PayCode.PARAM_NULL.getCode());
			return result;
		}

		if (payAmount.compareTo(BigDecimal.ZERO) < 0) {
			result.setMessage(" 交易金额必须大于0 ");
			log.error("  交易金额必须大于0 ");
			result.setCode(PayCode.PARAM_NULL.getCode());
			return result;
		}

        if (CsStringUtils.isEmpty(paymentRequest.getPaymentBillDTO().getPayerMemberId())) {
			log.error("  付款方id为空 ");
			result.setMessage(" 付款方id为空 ");
			result.setCode(PayCode.PARAM_NULL.getCode());
			return result;
		}
		String payeeMemberId = paymentRequest.getPaymentBillDTO().getPayeeMemberId();
        if (CsStringUtils.isEmpty(payeeMemberId)) {
			log.error("  收款方id为空 ");
			result.setMessage(" 收款方id为空 ");
			result.setCode(PayCode.PARAM_NULL.getCode());
			return result;
		}
		result.setSuccess(true);
		return result;
	}

	private ValidateResultDTO validatePaymentBill(RefundRequestWrapDTO paymentRequest) {
		log.info(" 验证 paymentRequest {} ", paymentRequest);
		ValidateResultDTO result = new ValidateResultDTO();
		result.setSuccess(false);
		if (paymentRequest == null) {
			log.error("  支付请求数据为空 ");
			result.setMessage(" 支付请求数据为空 ");

			result.setCode(PayCode.PARAM_NULL.getCode());
			return result;
		}
		if (paymentRequest.getRefundBillDTO() == null) {
			log.error("  支付单数据为空 ");
			result.setMessage(" 支付单数据为空 ");
			result.setCode(PayCode.PARAM_NULL.getCode());
			return result;
		}

		BigDecimal payAmount = paymentRequest.getRefundBillDTO().getPayAmount();

		if (payAmount == null) {
			result.setMessage(" 交易金额为空 ");
			log.error("  交易金额为空 ");
			result.setCode(PayCode.PARAM_NULL.getCode());
			return result;
		}

		if (payAmount.compareTo(BigDecimal.ZERO) < 0) {
			result.setMessage(" 交易金额必须大于0 ");
			log.error("  交易金额必须大于0 ");
			result.setCode(PayCode.PARAM_NULL.getCode());
			return result;
		}

        if (CsStringUtils.isEmpty(paymentRequest.getRefundBillDTO().getPayerMemberId())) {
			log.error("  付款方id为空 ");
			result.setMessage(" 付款方id为空 ");
			result.setCode(PayCode.PARAM_NULL.getCode());
			return result;
		}
		result.setSuccess(true);
		return result;
	}

	@Override
	public ChannelCardResponseDTO handleAddChannelCard(ChannelCardRequestDTO request, MemberChannelDTO memberChannelDTO, String operator) {
		int cardsCount = getCardsCount(request.getMemberChannelId());
		if (cardsCount >= MAX_CARDS) {
			throw new BizException(PayCode.INVALID_PARAM, "最多只能添加10张银行卡");
		}
		ChannelCardDTO channelCardDTO = request.getChannelCardDTO();
		if (cardsCount == 0) {
			channelCardDTO.setExt1(ChannelCardBiz.DEFAULT_SIGN);
		}
		channelCardDTO.setMemberChannelNo(memberChannelDTO.getMemberChannelCode());
		channelCardDTO.setMemberChannelId(memberChannelDTO.getMemberChannelId());
		channelCardDTO.setMemberId(memberChannelDTO.getMemberId());
		channelCardDTO.setMemberName(memberChannelDTO.getMemberName());
		channelCardDTO.setLastVerifyTime(new Date());
		channelCardDTO.setPassVerifyTime(new Date());
		channelCardDTO.setChannelCardStatus(ChannelCardStatusEnum.OPEN.getCode());
		channelCardDTO.setExt2(null);
		this.channelCardBiz.addChannelCard(channelCardDTO, operator);
		return createChannelCardResponseDTO(request);
	}

	@Override
	public PaymentResponseDTO searchPaymentBill(String paymentBillNo, String operator) {
		log.info(" 查询 支付单 paymentBillNo {}", paymentBillNo);
		PaymentResponseDTO paymentResponseDTO = new PaymentResponseDTO();
        if (CsStringUtils.isEmpty(paymentBillNo)) {
			throw new BizException(PayCode.PARAM_NULL);
		}
		paymentResponseDTO.setPaymentBillDTO(paymentBillBiz.findByPaymentBillNo(paymentBillNo));
		paymentResponseDTO.setSuccess(true);
		return paymentResponseDTO;
	}

	private PaymentResponseDTO createResponse(PaymentRequestWrapDTO paymentRequest, boolean success, String operator) {
		log.info("operator: {}", operator);
		PaymentResponseDTO response = new PaymentResponseDTO();
		response.setBehavior(PaymentPageBehaviorEnum.TO_OFFLINE.getCode());
		response.setPaymentBillDTO(paymentRequest.getPaymentBillDTO());
		response.setPaymentBillId(paymentRequest.getPaymentBillDTO().getPaymentBillId());
		response.setSuccess(success);
		response.setChangeStatus(false);
		return response;
	}

	private PaymentResponseDTO handleOffline(PaymentRequestWrapDTO paymentRequest, String operator) {
		return createResponse(paymentRequest, true, operator);
	}

	private PaymentCallbackResponseDTO handleOfflineCallBack(PaymentCallbackRequestDTO paymentRequest) {

        if (CsStringUtils.isEmpty(paymentRequest.getPaymentBillId())) {
			throw new BizException(PayCode.PARAM_NULL);
		}

		PaymentBillDTO paymentBillDTO = this.paymentBillBiz.findByPaymentBillId(paymentRequest.getPaymentBillId());
		if (paymentBillDTO == null) {
			throw new BizException(PayCode.PARAM_NULL, " 未查询到支付单 ");
		}
		if (paymentRequest.isSuccess()) {
			paymentBillDTO.setActualPayAmount(paymentBillDTO.getPayAmount());
			List<PaymentBillDetailDTO> dtoList = paymentBillDTO.getDetailDTOList();
			if (!CollectionUtils.isEmpty(dtoList)) {
				for (PaymentBillDetailDTO dto : dtoList) {
					dto.setActualPayAmount(dto.getPayAmount());
				}
			}
			paymentBillDTO.setStatus(PaymentStatusEnum.PAY_SUCCESS.getCode());
		} else {
			paymentBillDTO.setStatus(PaymentStatusEnum.PAY_FAIL.getCode());
		}
		this.notifyByRemoteInvoke(paymentBillDTO, paymentRequest.isSuccess(), ChannelCodeEnum.OFFLINE.getCode(), paymentRequest.getOperator());

		PaymentCallbackResponseDTO paymentCallbackResponseDTO = new PaymentCallbackResponseDTO();
		paymentCallbackResponseDTO.setPaymentBillDTO(paymentBillDTO);

		return this.createSuccessCallbackResponse(paymentCallbackResponseDTO);
	}

	@Override
	public PaymentResponseDTO handlePay(PaymentRequestWrapDTO paymentRequest, String operator) {
		log.info(" 线下支付 handlePay {} ", JSON.toJSONString(paymentRequest));
		return handleOffline(paymentRequest, operator);
	}

	@Override
	public PaymentCallbackResponseDTO handlePayCallback(PaymentCallbackRequestDTO paymentRequest) {
		log.info(" 线下支付 handlePayCallback {} ", JSON.toJSONString(paymentRequest));
		return handleOfflineCallBack(paymentRequest);
	}

	@Override
	public PaymentResponseDTO handleRefund(RefundRequestWrapDTO paymentRequest, String operator) {
		log.info(" 线下支付 handleRefund {} ", JSON.toJSONString(paymentRequest));
		RefundBillDTO refundBillDTO = paymentRequest.getRefundBillDTO();
		refundBillDTO.setStatus(PaymentStatusEnum.PAY_SUCCESS.getCode());
		refundBillDTO.setActualPayAmount(refundBillDTO.getPayAmount());
		refundBillBiz.update(refundBillDTO, operator);
		super.logsRefund(refundBillDTO, operator);
		PaymentResponseDTO responseDTO = new PaymentResponseDTO();
		responseDTO.setSuccess(true);
		responseDTO.setBehavior(PaymentPageBehaviorEnum.TO_OFFLINE.getCode());
		responseDTO.setChangeStatus(true);
		return responseDTO;
	}

	@Override
	public PaymentCallbackResponseDTO handleRefundCallback(PaymentCallbackRequestDTO paymentRequest) {
		log.info(" 线下支付 handleRefundCallback {} ", JSON.toJSONString(paymentRequest));
		return handleOfflineCallBack(paymentRequest);
	}

	@Override
	public PaymentResponseDTO handleFreeze(PaymentRequestWrapDTO paymentRequest, String operator) {
		log.info(" 线下支付 handleFreeze {} ", JSON.toJSONString(paymentRequest));
		return handleOffline(paymentRequest, operator);
	}

	@Override
	public PaymentCallbackResponseDTO handleFreezeCallback(PaymentCallbackRequestDTO paymentRequest) {
		log.info(" 线下支付 handleFreezeCallback {} ", JSON.toJSONString(paymentRequest));
		return handleOfflineCallBack(paymentRequest);
	}

	@Override
	public PaymentCallbackResponseDTO handleUnFreezeCallback(PaymentCallbackRequestDTO paymentRequest) {
		log.info(" 线下支付 handleUnFreezeCallback {} ", JSON.toJSONString(paymentRequest));
		return handleOfflineCallBack(paymentRequest);
	}

	@Override
	public ValidateResultDTO validatePaymentBillWhenPay(PaymentRequestWrapDTO paymentRequest) {
		return validatePaymentBill(paymentRequest);
	}

	@Override
	public ValidateResultDTO validatePaymentBillWhenRefund(RefundRequestWrapDTO paymentRequest) {
		return validatePaymentBill(paymentRequest);
	}

	@Override
	public ValidateResultDTO validatePaymentBillWhenFreeze(PaymentRequestWrapDTO paymentRequest) {
		return validatePaymentBill(paymentRequest);
	}

	@Override
	public ValidateResultDTO validatePaymentBillWhenUnFreeze(PaymentRequestWrapDTO paymentRequest) {
		return validatePaymentBill(paymentRequest);
	}

	@Override
	public ValidateResultDTO validatePaymentBillWhenPayDeposit(PaymentRequestWrapDTO paymentRequest) {
		return validatePaymentBill(paymentRequest);
	}

	@Override
	public ValidateResultDTO validatePaymentBillWhenCancelDeposit(PaymentRequestWrapDTO paymentRequest) {
		return validatePaymentBill(paymentRequest);
	}

	@Override
	public ValidateResultDTO validatePaymentBillWhenTransferDeposit(PaymentRequestWrapDTO paymentRequest) {
		return validatePaymentBill(paymentRequest);
	}

	@Override
	public ValidateResultDTO validatePaymentBillWhenConfiscateDeposit(PaymentRequestWrapDTO paymentRequest) {
		return validatePaymentBill(paymentRequest);
	}

	@Override
	public ValidateResultDTO validateMemberChannelWhenOpen(MemberChannelRequestDTO memberChannelDTO) {
		return this.validateMemberChannel(memberChannelDTO);
	}

	@Override
	public MemberChannelResponseDTO handleOpenMemberChannel(MemberChannelRequestDTO memberChannelRequestDTO, String operator) {
		log.info(" 线下支付 handleOpenMemberChannel {} ", JSON.toJSONString(memberChannelRequestDTO));
		MemberChannelDTO memberChannelDTO = memberChannelRequestDTO.getMemberChannelDTO();
		MemberChannelResponseDTO memberChannelResponseDTO = new MemberChannelResponseDTO();

        if (CsStringUtils.isBlank(memberChannelDTO.getMemberChannelStatus())) {
			memberChannelDTO.setMemberChannelStatus(MemberChannelStatusEnum.OPEND.getCode());
			memberChannelDTO.setIsSupportApp(true);
			memberChannelDTO.setIsSupportMini(true);
			memberChannelDTO.setIsSupportWeb(true);
		}else{
			memberChannelDTO.setIsSupportApp(false);
			memberChannelDTO.setIsSupportMini(false);
			memberChannelDTO.setIsSupportWeb(false);
		}
        if (CsStringUtils.isEmpty(memberChannelDTO.getMemberChannelId())) {
			MemberChannelDTO memberChannelDTO1 = this.memberChannelBiz.findByMemberIdAndType(memberChannelRequestDTO.getMemberChannelDTO().getMemberId(), ChannelCodeEnum.OFFLINE.getCode());
			if( memberChannelDTO1 == null) {
				log.info(" 线下支付 未找到已开通的支付渠道 插入新的线下支付渠道 ");
				memberChannelDTO = this.memberChannelBiz.insertMemberChannel(memberChannelDTO, operator);
			}else if( !MemberChannelStatusEnum.OPEND.getCode().equals(memberChannelDTO1.getMemberChannelStatus()) ){
				//如果要求开通渠道，但是上送状态为BE_STOP，则不做任何操作
				memberChannelResponseDTO.setMemberChannelDTO(memberChannelDTO);
				memberChannelResponseDTO.setOpenFlag(2);
				return memberChannelResponseDTO;
			}else{
				log.info(" 线下支付 找到已开通的支付渠道 不做处理 ");
				memberChannelResponseDTO.setOpenFlag(2);
			}
		}else{
			memberChannelDTO.setMemberChannelStatus(MemberChannelStatusEnum.OPEND.getCode());
			memberChannelDTO.setIsSupportApp(true);
			memberChannelDTO.setIsSupportMini(true);
			memberChannelDTO.setIsSupportWeb(true);
			log.info(" 线下支付 找到支付渠道记录,但状态未开通,更新状态未开通状态");
			memberChannelBiz.updateMemberChannel(memberChannelDTO,operator);
			//开启渠道下的银行卡
			channelCardBiz.updateCardStatus(memberChannelRequestDTO.getMemberChannelDTO().getMemberId(),memberChannelRequestDTO.getMemberChannelDTO().getMemberChannelId(),ChannelCardStatusEnum.OPEN.getCode());
		}
		memberChannelResponseDTO.setMemberChannelDTO(memberChannelDTO);
		return memberChannelResponseDTO;
	}

	@Override
	public MemberChannelCallbackResponseDTO handleOpenMemberChannelCallback(
			MemberChannelCallbackRequestDTO memberChannelDTO) {
		return null;
	}

	@Override
	@Transactional
	public MemberChannelResponseDTO handleCloseMemberChannel(MemberChannelRequestDTO memberChannelRequestDTO,
			String operator) {
		log.info(" 线下支付 handleCloseMemberChannel {} ", JSON.toJSONString(memberChannelRequestDTO));

		if (memberChannelRequestDTO.getMemberChannelDTO() == null
                || CsStringUtils.isEmpty(memberChannelRequestDTO.getMemberChannelDTO().getMemberChannelId())) {
			throw new BizException(PayCode.PARAM_NULL, " 未找到渠道信息 ");
		}

		MemberChannelDTO memberChannelDTO = new MemberChannelDTO();
		memberChannelDTO.setMemberChannelId(memberChannelRequestDTO.getMemberChannelDTO().getMemberChannelId());
		memberChannelDTO.setMemberChannelStatus(MemberChannelStatusEnum.BE_STOP.getCode());
		//关闭渠道
		MemberChannelDTO memberChannelDTOResult = this.memberChannelBiz.updateMemberChannel(memberChannelDTO, operator);
		//关闭渠道下的银行卡
		channelCardBiz.updateCardStatus(memberChannelRequestDTO.getMemberChannelDTO().getMemberId(),memberChannelRequestDTO.getMemberChannelDTO().getMemberChannelId(),ChannelCardStatusEnum.CLOSED.getCode());
		MemberChannelResponseDTO memberChannelResponseDTO = new MemberChannelResponseDTO();
		memberChannelResponseDTO.setMemberChannelDTO(memberChannelDTOResult);
		return memberChannelResponseDTO;
	}

	@Override
	public MemberChannelCallbackResponseDTO handleCloseMemberChannelCallback(
			MemberChannelCallbackRequestDTO memberChannelDTO) {
		return null;
	}

	@Override
	public PaymentResponseDTO handlePayDeposit(PaymentRequestWrapDTO paymentRequest, String operator) {
		log.info(" 线下支付 handleUnFreeze {} ", JSON.toJSONString(paymentRequest));
		return handleOffline(paymentRequest, operator);
	}

	@Override
	public PaymentCallbackResponseDTO handlePayDepositCallBack(PaymentCallbackRequestDTO paymentRequest) {
		log.info(" 线下支付 handlePayDepositCallBack {} ", JSON.toJSONString(paymentRequest));
		return handleOfflineCallBack(paymentRequest);
	}

	@Override
	public PaymentCallbackResponseDTO handleCancelDepositCallBack(PaymentCallbackRequestDTO paymentRequest) {
		log.info(" 线下支付 handleCancelDepositCallBack {} ", JSON.toJSONString(paymentRequest));
		return handleOfflineCallBack(paymentRequest);
	}

	@Override
	public PaymentResponseDTO handleConfiscateDeposit(PaymentRequestWrapDTO paymentRequest, String operator) {
		log.info(" 线下支付 handleUnFreeze {} ", JSON.toJSONString(paymentRequest));
		return handleOffline(paymentRequest, operator);
	}

	@Override
	public PaymentCallbackResponseDTO handleConfiscateDepositCallBack(PaymentCallbackRequestDTO paymentRequest) {
		log.info(" 线下支付 handleConfiscateDepositCallBack {} ", JSON.toJSONString(paymentRequest));
		return handleOfflineCallBack(paymentRequest);
	}

	@Override
	public PaymentResponseDTO handleTransferDeposit(PaymentRequestWrapDTO paymentRequest, String operator) {
		log.info(" 线下支付 handleUnFreeze {} ", JSON.toJSONString(paymentRequest));
		return handleOffline(paymentRequest, operator);
	}

	@Override
	public PaymentCallbackResponseDTO handleTransferDepositCallBack(PaymentCallbackRequestDTO paymentRequest) {
		log.info(" 线下支付 handleTransferDepositCallBack {} ", JSON.toJSONString(paymentRequest));
		return handleOfflineCallBack(paymentRequest);
	}

	@Override
	public PaymentResponseDTO handleCancelDeposit(PaymentRequestWrapDTO paymentRequest, String operator) {
		log.info(" 线下支付 handleUnFreeze {} ", JSON.toJSONString(paymentRequest));
		return handleOffline(paymentRequest, operator);
	}

	@Override
	public PaymentResponseDTO handleCancelPayment(PaymentRequestWrapDTO paymentRequest, String operator) {
		log.info(" 取消线下支付单 {} ", paymentRequest);
		return this.createResponse(paymentRequest, true, operator);
	}

	@Override
	public HealthResponseDTO checkHealth() {
		return null;
	}

	@Override
	public ChannelCardResponseDTO handleRemoveChannelCard(ChannelCardRequestDTO request, MemberChannelDTO memberChannelDTO, String operator) {
		ChannelCardResponseDTO responseDTO = super.handleRemoveChannelCard(request, memberChannelDTO, operator);

		int cards = channelCardBiz.getChannelCardCountByMemberChannelId(memberChannelDTO.getMemberChannelId());
		if (cards <= 0) {
			MemberChannelDTO t = new MemberChannelDTO();
			t.setMemberChannelId(memberChannelDTO.getMemberChannelId());
			t.setMemberChannelStatus(MemberChannelStatusEnum.BE_STOP.getCode());
			t.setDelFlg(true);
			this.memberChannelBiz.updateMemberChannel(t, operator);
		}
		return responseDTO;
	}

	/**
	 * 直接远程调用 通知接入方 支付成功
	 * 
	 * @param paymentBillDTO
	 * @param ifSuccess
	 */
	@Override
	protected void notifyByRemoteInvoke(PaymentBillDTO paymentBillDTO, Boolean ifSuccess, String channel,
										String operator) {
		log.info("线下支付成功 保存支付流水  {} {}", paymentBillDTO, ifSuccess);
		PaymentBillDTO update = new PaymentBillDTO();
		update.setPaymentBillId(paymentBillDTO.getPaymentBillId());
		try {
			if (ifSuccess) {
				super.logsPay(paymentBillDTO, operator);
			}
			PaymentNotifyDTO paymentNotifyDTO = new PaymentNotifyDTO();
			paymentNotifyDTO.setAmount(paymentBillDTO.getPayAmount());
			paymentNotifyDTO.setBizId(paymentBillDTO.getSrcBizNo());
			paymentNotifyDTO.setChannel(channel);
			paymentNotifyDTO.setIfSuccess(ifSuccess);
			paymentNotifyDTO.setOperator(operator);
			paymentNotifyDTO.setPaymentBillId(paymentBillDTO.getPaymentBillId());
			paymentNotifyDTO.setPaymentBillNo(paymentBillDTO.getPaymentBillNo());
			paymentNotifyDTO.setPaymentBizCode(paymentBillDTO.getPaymentBizCode());
			iPaymentIntegrationBiz.paymentResultCallback(paymentNotifyDTO);
			log.error(" 线下支付 保存支付流成功 {} ", paymentBillDTO);
			update.setNotifyBusStatus(PaymentCallbackEnum.BIZ_CALL_SUCCESS.getCode());
			update.setNotifyTime(new Date());
			paymentBillBiz.updatePaymentBill(update, operator);
		} catch (Exception e) {
			log.error(" 保存支付流水失败 {} {}", e.getMessage(), e.getCause());
			update.setNotifyBusStatus(PaymentCallbackEnum.BIZ_CALL_FAILED.getCode());
			update.setWarningCode(PaymentWarningCodeEnum.UPDATE_PAYBILLINFO_FAIL.getCode());
			paymentBillBiz.updatePaymentBill(update, operator);
		}
	}


}
