package com.ecommerce.pay.v2.channel.adapter.weixin;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.service.common.bsid.CommonBusinessIdGenerator;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.open.api.service.IWeiXinConnectorService;
import com.ecommerce.pay.api.exception.PayCode;
import com.ecommerce.pay.api.request.WXQueryRefundRequest;
import com.ecommerce.pay.api.v2.dto.HealthResponseDTO;
import com.ecommerce.pay.api.v2.dto.MemberChannelCallbackRequestDTO;
import com.ecommerce.pay.api.v2.dto.MemberChannelCallbackResponseDTO;
import com.ecommerce.pay.api.v2.dto.MemberChannelDTO;
import com.ecommerce.pay.api.v2.dto.MemberChannelRequestDTO;
import com.ecommerce.pay.api.v2.dto.MemberChannelResponseDTO;
import com.ecommerce.pay.api.v2.dto.PaymentCallbackRequestDTO;
import com.ecommerce.pay.api.v2.dto.PaymentCallbackResponseDTO;
import com.ecommerce.pay.api.v2.dto.PaymentRequestWrapDTO;
import com.ecommerce.pay.api.v2.dto.PaymentResponseDTO;
import com.ecommerce.pay.api.v2.dto.RefundRequestWrapDTO;
import com.ecommerce.pay.api.v2.dto.ValidateResultDTO;
import com.ecommerce.pay.api.v2.dto.bill.PaymentBillDTO;
import com.ecommerce.pay.api.v2.dto.bill.PaymentBillDetailDTO;
import com.ecommerce.pay.api.v2.dto.bill.RefundBillDTO;
import com.ecommerce.pay.api.v2.enums.ChannelCodeEnum;
import com.ecommerce.pay.api.v2.enums.ChannelPaymentTypeEnum;
import com.ecommerce.pay.api.v2.enums.ClientType;
import com.ecommerce.pay.api.v2.enums.MemberChannelStatusEnum;
import com.ecommerce.pay.api.v2.enums.PaymentPageBehaviorEnum;
import com.ecommerce.pay.api.v2.enums.PaymentPageContentTypeEnum;
import com.ecommerce.pay.api.v2.enums.PaymentStatusEnum;
import com.ecommerce.pay.config.WxProperties;
import com.ecommerce.pay.enums.AlgTypeEnum;
import com.ecommerce.pay.util.QRCodeUtil;
import com.ecommerce.pay.util.SignatureUtil;
import com.ecommerce.pay.v2.channel.AbstractBaseAdapter;
import com.ecommerce.pay.v2.util.wechat.WXPayConstants;
import com.ecommerce.pay.v2.util.wechat.WXPayUtil;
import com.google.common.collect.Maps;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

@Service("weixin")
@Slf4j
public class WeiXinAdapter extends AbstractBaseAdapter {

	@Autowired
	private WxProperties wxProperties;

	@Autowired
	private IWeiXinConnectorService weXinConnector;

    @Autowired
	private CommonBusinessIdGenerator takeCodeGenerator;

	@Override
	public ValidateResultDTO validateMemberChannelWhenOpen(MemberChannelRequestDTO memberChannelRequestDTO) {

		ValidateResultDTO result = new ValidateResultDTO();
		result.setSuccess(false);
		if(memberChannelRequestDTO == null){
			result.setMessage("会员渠道请求数据为空！");
			return result;
		}
		String memberId = memberChannelRequestDTO.getMemberChannelDTO().getMemberId();
		if(memberChannelRequestDTO.getMemberChannelDTO() == null){
			result.setMessage("会员渠道dto为空！");
			return result;
		}
        if (CsStringUtils.isEmpty(memberId)) {
			result.setMessage("会员渠道id为空！");
			return result;
		}
		MemberChannelDTO memberChannelDTO = memberChannelBiz.findByMemberIdAndType(memberId, ChannelCodeEnum.WEIXIN.getCode());
		if(memberChannelDTO == null){
			result.setSuccess(true);
			return result;
		}else{
			result.setMessage("该会员已经开通该支付渠道！");
			return result;
		}
	}

	@Override
	public ValidateResultDTO validatePaymentBillWhenPay(PaymentRequestWrapDTO paymentRequest) {
		log.info(" 验证 paymentRequest {} ", paymentRequest);
		ValidateResultDTO result = new ValidateResultDTO();
		result.setSuccess(false);
		if (paymentRequest == null) {
			log.error("  支付请求数据为空 ");
			result.setMessage(" 支付请求数据为空 ");
			result.setCode(BasicCode.PARAM_NULL.getCode());
			return result;
		}
		if (paymentRequest.getPaymentBillDTO() == null) {
			log.error("  支付单数据为空 ");
			result.setMessage(" 支付单数据为空 ");
			result.setCode(BasicCode.PARAM_NULL.getCode());
			return result;
		}

		BigDecimal payAmount = paymentRequest.getPaymentBillDTO().getPayAmount();

		if (payAmount == null) {
			result.setMessage(" 交易金额为空 ");
			log.error("  交易金额为空 ");
			result.setCode(BasicCode.PARAM_NULL.getCode());
			return result;
		}

		if (payAmount.compareTo(BigDecimal.ZERO) < 0) {
			result.setMessage(" 交易金额必须大于0 ");
			log.error("  交易金额必须大于0 ");
			result.setCode(BasicCode.PARAM_NULL.getCode());
			return result;
		}

        if (CsStringUtils.isEmpty(paymentRequest.getPaymentBillDTO().getPayerMemberId())) {
			log.error("  付款方id为空 ");
			result.setMessage(" 付款方id为空 ");
			result.setCode(BasicCode.PARAM_NULL.getCode());
			return result;
		}
        if (CsStringUtils.isEmpty(paymentRequest.getPaymentBillDTO().getPayeeMemberId())) {
			log.error("  收款方id为空 ");
			result.setMessage(" 收款方id为空 ");
			result.setCode(BasicCode.PARAM_NULL.getCode());
			return result;
		}

        if (CsStringUtils.isEmpty(paymentRequest.getPaymentBillDTO().getPaymentBillNo()) ||
                CsStringUtils.isEmpty(paymentRequest.getMemberChannelDTO().getMchKey()) ||
                CsStringUtils.isEmpty(paymentRequest.getMemberChannelDTO().getMchNo()) ||
                CsStringUtils.isEmpty(paymentRequest.getClientType()) ||
                CsStringUtils.isEmpty(paymentRequest.getBody()) ||
                CsStringUtils.isEmpty(paymentRequest.getClientIP())) {
            log.error("  微信支付必须参数为空 ");
            result.setMessage(" 微信支付必须参数为空 ");
            result.setCode(BasicCode.PARAM_NULL.getCode());
            return result;
        }

		//查询订单编号
		PaymentBillDTO paymentBillDTO = paymentBillBiz.findByPaymentBillNo(paymentRequest.getPaymentBillDTO().getPaymentBillNo());
		if (paymentBillDTO == null) {
			log.error("无效的订单");
			result.setMessage(" 无效的订单 ");
			result.setCode(PayCode.INVALID_PARAM.getCode());
			return result;
		}

		result.setSuccess(true);
		return result;
	}

	@Override
	public ValidateResultDTO validatePaymentBillWhenRefund(RefundRequestWrapDTO paymentRequest) {
        log.info(" 验证 paymentRequest {} ", paymentRequest);
        ValidateResultDTO result = new ValidateResultDTO();
        result.setSuccess(false);
        if (paymentRequest == null) {
            log.error("  支付请求数据为空 ");
            result.setMessage(" 支付请求数据为空 ");
            result.setCode(BasicCode.PARAM_NULL.getCode());
            return result;
        }

        if (paymentRequest.getRefundBillDTO() == null) {
            log.error("  支付单数据为空 ");
            result.setMessage(" 支付单数据为空 ");
            result.setCode(BasicCode.PARAM_NULL.getCode());
            return result;
        }

        if (CsStringUtils.isEmpty(paymentRequest.getRefundBillDTO().getRefundBillNo()) ||
                CsStringUtils.isEmpty(paymentRequest.getMemberChannelDTO().getMchKey()) ||
                CsStringUtils.isEmpty(paymentRequest.getMemberChannelDTO().getMchNo()) ||
                CsStringUtils.isEmpty(paymentRequest.getRefundBillDTO().getRelatedPaymentBillNo())) {
            log.error("  微信退款必须参数为空 ");
            result.setMessage(" 微信退款必须参数为空 ");
            result.setCode(BasicCode.PARAM_NULL.getCode());
            return result;
        }

        if(paymentRequest.getRefundAmount().compareTo(BigDecimal.ZERO) < 0){
            result.setMessage(" 退款金额必须大于0 ");
            log.error("  退款金额必须大于0 ");
            result.setCode(BasicCode.PARAM_NULL.getCode());
            return result;
        }

        if(paymentRequest.getTotalPayAmount().compareTo(paymentRequest.getRefundAmount()) < 0){
            result.setMessage(" 退款金额必须小于订单总金额");
            log.error("  退款金额必须小于订单总金额 ");
            result.setCode(BasicCode.PARAM_NULL.getCode());
            return result;
        }

        result.setSuccess(true);
        return result;
	}

	@Override
	public ValidateResultDTO validatePaymentBillWhenFreeze(PaymentRequestWrapDTO paymentRequest) {
		ValidateResultDTO result = new ValidateResultDTO();
		log.info("微信支付不支持账户冻结操作");
		result.setMessage("微信支付不支持账户冻结操作");
		result.setSuccess(false);
		return result;
	}

	@Override
	public ValidateResultDTO validatePaymentBillWhenUnFreeze(PaymentRequestWrapDTO paymentRequest) {
		ValidateResultDTO result = new ValidateResultDTO();
		log.info("微信支付不支持账户解冻操作");
		result.setMessage("微信支付不支持账户解冻操作");
		result.setSuccess(false);
		return result;
	}

	@Override
	public ValidateResultDTO validatePaymentBillWhenPayDeposit(PaymentRequestWrapDTO paymentRequest) {
		ValidateResultDTO result = new ValidateResultDTO();
		log.info("微信支付不支持账户存款操作");
		result.setMessage("微信支付不支持账户存款操作");
		result.setSuccess(false);
		return result;
	}

	@Override
	public ValidateResultDTO validatePaymentBillWhenCancelDeposit(PaymentRequestWrapDTO paymentRequest) {
		ValidateResultDTO result = new ValidateResultDTO();
		log.info("微信支付不支持账户取消存款操作");
		result.setMessage("微信支付不支持账户取消存款操作");
		result.setSuccess(false);
		return result;
	}

	@Override
	public ValidateResultDTO validatePaymentBillWhenTransferDeposit(PaymentRequestWrapDTO paymentRequest) {
		ValidateResultDTO result = new ValidateResultDTO();
		log.info("微信支付不支持账户转账操作");
		result.setMessage("微信支付不支持账户转账操作");
		result.setSuccess(false);
		return result;
	}

	@Override
	public ValidateResultDTO validatePaymentBillWhenConfiscateDeposit(PaymentRequestWrapDTO paymentRequest) {
		return validatePaymentBillWhenPay(paymentRequest);
	}

	/**
	 * 微信支付查询订单
	 * @param operator
	 * @return
	 */
	@Override
	public PaymentResponseDTO searchPaymentBill(String paymentBillNo, String operator) {
		log.info(" 微信支付 searchPaymentBill {} ", paymentBillNo);
		PaymentResponseDTO result = new PaymentResponseDTO();
		result.setSuccess(false);
        if (CsStringUtils.isEmpty(paymentBillNo)) {
			log.info("参数paymentBillNo为空");
			throw new BizException(BasicCode.PARAM_NULL);
		}
		//查询订单编号
		PaymentBillDTO paymentBillDTO = paymentBillBiz.findByPaymentBillNo(paymentBillNo);
		if (paymentBillDTO == null) {
			log.info("无效的订单");
			throw new BizException(BasicCode.DB_DATA_NOT_FOUND);
		}

		String payeeMemberId = paymentBillDTO.getPayeeMemberId();
        if (CsStringUtils.isEmpty(payeeMemberId)) {
			log.info("无效的订单,未查询到收款方信息");
			throw new BizException(BasicCode.DB_DATA_NOT_FOUND);
		}

		MemberChannelDTO memberChannelDTO = memberChannelBiz.findByMemberIdAndChannelCode(payeeMemberId, ChannelCodeEnum.WEIXIN.getCode(), ChannelPaymentTypeEnum.PAYEE);
		if(memberChannelDTO == null){
			log.info("未查询到该卖家对应的支付渠道信息");
			throw new BizException(BasicCode.PARAM_NULL,"memberChannelDTO");
		}
            try {
                //调用open-api发送请求
                Map<String, String> reqData = buildQueryRequest(paymentBillDTO,memberChannelDTO);
				String xmlRequest = WXPayUtil.mapToXml(reqData);
                log.info("查询订单发送微信请求："+xmlRequest);
                String xmlResponse = weXinConnector.wxPostRequest(xmlRequest, wxProperties.getPrePaymentUrl() + "/pay/orderquery",paymentBillNo).getData();
                log.info("查询订单接收微信请求："+xmlResponse);
				Map<String, String> map = WXPayUtil.xmlToMap(xmlResponse);

				if ("FAIL".equals(map.get("return_code"))) {
					log.info("调用微信查询订单接口错误："+xmlResponse);
					throw new BizException(PayCode.WECHAT_REQUEST_FAIL, map.get("return_msg"));
				}

				boolean valid = WXPayUtil.isSignatureValid(map, memberChannelDTO.getMchKey(), WXPayConstants.SignType.MD5);

				if (!valid) {
					String sign = map.get(WXPayConstants.FIELD_SIGN);
					log.info("签名校验错误,微信返回的sign: {}", sign);
                    throw new BizException(PayCode.WECHAT_SIGN_ERROR);
				}

				String tradeState = "";
                if ("FAIL".equals(map.get("result_code"))) {
					log.info("订单请求失败：" + map.get("result_code"));
					if ("REFUNDNOTEXIST".equals(map.get("err_code"))) {
						paymentBillDTO.setStatus(PaymentStatusEnum.PAY_CLOSE.getCode());
						paymentBillDTO.setExtStatus(map.get("err_code"));
					}
					if ("ORDERNOTEXIST".equals(map.get("err_code"))) {
						paymentBillDTO.setStatus(PaymentStatusEnum.PAY_CLOSE.getCode());
						paymentBillDTO.setExtStatus(map.get("err_code"));
					}
                } else {
					tradeState = map.get("trade_state");

					switch (tradeState) {
						case "SUCCESS":
							log.info("微信返回成功");
							paymentBillDTO.setStatus(PaymentStatusEnum.PAY_SUCCESS.getCode());
							paymentBillDTO.setExtStatus(tradeState);
							super.notifyPayWithoutLogs(paymentBillDTO, true, paymentBillDTO.getChannelCode(), "system");
							break;
						case "NOTPAY":
							log.info("该订单未支付");
							Date expireTime = paymentBillDTO.getExpireTime();
							if (expireTime != null && System.currentTimeMillis() >= expireTime.getTime()) {
								paymentBillDTO.setStatus(PaymentStatusEnum.PAY_TIMEOUT.getCode());
								paymentBillDTO.setExtStatus(tradeState);
							} else {
								paymentBillDTO.setStatus(PaymentStatusEnum.PAY_ING.getCode());
								paymentBillDTO.setExtStatus(tradeState);
							}
							break;
						case "USERPAYING":
							log.info("该订单正在支付中");
							paymentBillDTO.setStatus(PaymentStatusEnum.PAY_ING.getCode());
							paymentBillDTO.setExtStatus(tradeState);
							break;
						default:
							paymentBillDTO.setStatus(PaymentStatusEnum.PAY_FAIL.getCode());
							paymentBillDTO.setExtStatus(tradeState);
							log.info("订单支付失败");
							break;
					}
				}

                log.info("查询订单接口调用完成");
                paymentBillBiz.updatePaymentBill(paymentBillDTO, operator);
				result.setPaymentBillDTO(paymentBillDTO);
                result.setSuccess(true);
                result.setChangeStatus(true);
                result.setContent(tradeState);
            }  catch (Exception e) {
                log.error(e.toString());
                throw new BizException(BasicCode.UNDEFINED_ERROR, e.getMessage());
            }
        return result;
	}

	@Override
	public MemberChannelResponseDTO handleOpenMemberChannel(MemberChannelRequestDTO memberChannelRequestDTO,
			String operator) {
		log.info(" 微信支付 handleOpenMemberChannel {} ", JSON.toJSONString(memberChannelRequestDTO));
		MemberChannelDTO memberChannelDTO = memberChannelRequestDTO.getMemberChannelDTO();

		// 是否开通了聚合支付
		if (memberChannelBiz.checkMemberChannelAggregation(memberChannelDTO.getMemberId())) {
			throw new BizException(BasicCode.UNDEFINED_ERROR, "已开通了聚合支付，无法开通微信支付");
		}
		// 校验参数是否正确
		try {
			this.checkByOpen(memberChannelDTO);
		} catch (Exception e) {
			throw new BizException(PayCode.UNDEFINED_ERROR, "输入信息错误，请查证后重新填写");
		}

		memberChannelDTO.setMemberChannelStatus(MemberChannelStatusEnum.OPEND.getCode());
        if (CsStringUtils.isEmpty(memberChannelDTO.getMemberChannelId())) {
			memberChannelDTO = this.memberChannelBiz.insertMemberChannel(memberChannelDTO, operator);
		}
		MemberChannelResponseDTO memberChannelResponseDTO = new MemberChannelResponseDTO();
		memberChannelResponseDTO.setMemberChannelDTO(memberChannelDTO);
		return memberChannelResponseDTO;
	}

	private void checkByOpen(MemberChannelDTO memberChannelDTO) throws Exception {
		Map<String, String> reqData = Maps.newHashMap();
		reqData.put("appid", wxProperties.getAppId());
		reqData.put("mch_id", memberChannelDTO.getMchNo());
		String nonceStr = WXPayUtil.generateNonceStr();
		reqData.put("nonce_str", nonceStr);
		// 查询随机订单号
		reqData.put("out_trade_no", String.valueOf(System.currentTimeMillis()));
		reqData.put("sign_type", WXPayConstants.MD5);
		//生成摘要签名
		reqData.put("sign", WXPayUtil.generateSignature(reqData, memberChannelDTO.getMchKey(), WXPayConstants.SignType.MD5));
		String xmlRequest = WXPayUtil.mapToXml(reqData);

		log.info("查询订单发送微信请求："+xmlRequest);
		String xmlResponse = weXinConnector.wxPostRequest(xmlRequest, wxProperties.getPrePaymentUrl() + "/pay/orderquery", nonceStr).getData();
		log.info("查询订单接收微信请求："+xmlResponse);

		Map<String, String> respData = WXPayUtil.xmlToMap(xmlResponse);
		log.info("wechat result: {}", respData);

		String RETURN_CODE = "return_code";
		String return_code = respData.get(RETURN_CODE);
		if (!respData.containsKey(RETURN_CODE) || !return_code.equals(WXPayConstants.SUCCESS)) {
			throw new BizException(PayCode.WECHAT_SIGN_ERROR);
		}

		boolean valid = WXPayUtil.isSignatureValid(respData, memberChannelDTO.getMchKey(), WXPayConstants.SignType.MD5);
		if (!valid) {
			log.info("签名校验错误,微信返回的sign:" + respData.get("sign"));
			throw new BizException(PayCode.WECHAT_SIGN_ERROR);
		}
	}

	@Override
	public MemberChannelCallbackResponseDTO handleOpenMemberChannelCallback(
			MemberChannelCallbackRequestDTO memberChannelDTO) {
		return null;
	}

	@Override
	public MemberChannelResponseDTO handleCloseMemberChannel(MemberChannelRequestDTO memberChannelRequestDTO,
			String operator) {
		log.info(" 微信支付 handleCloseMemberChannel {} ", JSON.toJSONString(memberChannelRequestDTO));

		if (memberChannelRequestDTO.getMemberChannelDTO() == null
                || CsStringUtils.isEmpty(memberChannelRequestDTO.getMemberChannelDTO().getMemberChannelId())) {
			throw new BizException(BasicCode.PARAM_NULL, " 未找到渠道信息 ");
		}

		MemberChannelDTO memberChannelDTO = new MemberChannelDTO();
		memberChannelDTO.setMemberChannelId(memberChannelRequestDTO.getMemberChannelDTO().getMemberChannelId());
		memberChannelDTO.setMemberChannelStatus(MemberChannelStatusEnum.BE_STOP.getCode());

		MemberChannelDTO memberChannelDTOResult = this.memberChannelBiz.updateMemberChannel(memberChannelDTO, operator);

		MemberChannelResponseDTO memberChannelResponseDTO = new MemberChannelResponseDTO();
		memberChannelResponseDTO.setMemberChannelDTO(memberChannelDTOResult);
		return memberChannelResponseDTO;
	}

	@Override
	public MemberChannelCallbackResponseDTO handleCloseMemberChannelCallback(
			MemberChannelCallbackRequestDTO memberChannelDTO) {
		return null;
	}

	@Override
	public PaymentResponseDTO handlePay(PaymentRequestWrapDTO paymentRequest, String operator) {
		log.info(" 微信支付 handlePay {} ", JSON.toJSONString(paymentRequest));

		PaymentResponseDTO paymentResponseDTO = new PaymentResponseDTO();
		paymentResponseDTO.setSuccess(false);

		if(paymentRequest.getReplicate()){
		    if(paymentRequest.getPaymentBillDTO().getStatus().equals(PaymentStatusEnum.PAY_ING.getCode())){
                paymentRequest.getPaymentBillDTO().setPaymentBillNo(takeCodeGenerator.incrementPayCode());
                PaymentBillDTO newPaymentBillDTO = paymentBillBiz.updatePaymentBill(paymentRequest.getPaymentBillDTO(),operator);
				paymentRequest.setPaymentBillDTO(newPaymentBillDTO);
            }
        }
		Map<String, Object> paramMap = createPrePaymentOrder(paymentRequest);
		Map<String,String> returnMap = generatePaymentParam(paramMap,paymentRequest);
		paymentBillBiz.updatePaymentBill(paymentRequest.getPaymentBillDTO(),operator);

		if(paymentRequest.getClientType().equals(ClientType.APP.getCode())){
			paymentResponseDTO.setBehavior(PaymentPageBehaviorEnum.WAKE_UP.getCode());
		}else{
			paymentResponseDTO.setBehavior(PaymentPageBehaviorEnum.QR_CODE.getCode());
		}
        if(paymentRequest.getClientType().equals(ClientType.PC.getCode())){
            paymentResponseDTO.setContentType(PaymentPageContentTypeEnum.BASE64.getCode());
            paymentResponseDTO.setContent(returnMap.get("code_url"));
        }else{
            paymentResponseDTO.setContentType(PaymentPageContentTypeEnum.PARMS.getCode());
        }
		paymentResponseDTO.setSuccess(true);
		paymentResponseDTO.setParms(returnMap);
		return paymentResponseDTO;
	}

	@Override
	public PaymentCallbackResponseDTO handlePayCallback(PaymentCallbackRequestDTO paymentRequest) {
		log.info(" 微信支付 handlePayCallback {} ", JSON.toJSONString(paymentRequest));
		PaymentCallbackResponseDTO responseDTO = new PaymentCallbackResponseDTO();
		Map<String,String> wxNotifyResponse= new HashMap<>();
		wxNotifyResponse.put("return_code","FAIL");

		if(paymentRequest == null){
			log.error("微信支付回调为空:"+paymentRequest);
            wxNotifyResponse.put("return_msg","微信支付回调为空");
			responseDTO.setContent(wxNotifyResponse);
			return responseDTO;
		}
        if (CsStringUtils.isEmpty(paymentRequest.getPaymentBillId())) {
			log.error("支付单ID为空:"+paymentRequest.getPaymentBillId());
            wxNotifyResponse.put("return_msg","支付单ID为空");
			responseDTO.setContent(wxNotifyResponse);
			return responseDTO;
		}

		PaymentBillDTO paymentBillDTO = paymentBillBiz.findByPaymentBillId(paymentRequest.getPaymentBillId());
		String memberId = paymentBillDTO.getPayeeMemberId();
		MemberChannelDTO memberChannelDTO = memberChannelBiz.findByMemberIdAndChannelCode(memberId, ChannelCodeEnum.WEIXIN.getCode(), ChannelPaymentTypeEnum.PAYEE);
		Assert.notNull(memberChannelDTO, "该卖家" + memberId + "微信渠道不存在");

		Map<String,Object> map = paymentRequest.getParms();
		Map<String, String> wxNotifyRequest = Maps.newHashMap();
		for (Map.Entry<String, Object> entry : map.entrySet()) {
			wxNotifyRequest.put(entry.getKey(), (String) entry.getValue());
		}

		log.info("开始验签----");
		boolean valid = false;
		try {
			valid = WXPayUtil.isSignatureValid(wxNotifyRequest, memberChannelDTO.getMchKey());
		} catch (Exception e) {
			log.error(e.getMessage(),e);
		}
		log.info("验签结果----{}", valid);
		if (!valid) {
			log.info("微信验签失败");
			throw new BizException(PayCode.WECHAT_SIGN_ERROR);
		}

		if (PaymentStatusEnum.PAY_SUCCESS.getCode().equals(paymentBillDTO.getStatus())) {
			log.info("该笔支付单已处理");
			responseDTO.setPaymentBillDTO(paymentBillDTO);
			return responseDTO;
		}

		String result_code = wxNotifyRequest.get("result_code");
		String transaction_id = wxNotifyRequest.get("transaction_id");
        paymentBillDTO.setNotifyTime(new Date());
        boolean doNotify = true;
		boolean ifSuccess = false;
		if("SUCCESS".equals(result_code)){
            if (!CsStringUtils.isEmpty(transaction_id)) {
				paymentBillDTO.setExtBizNo(transaction_id);
			}
			paymentBillDTO.setExtStatus(PaymentStatusEnum.PAY_SUCCESS.getCode());
			//接口幂等性处理
			BigDecimal totalFee = new BigDecimal(wxNotifyRequest.get("total_fee")).setScale(2, RoundingMode.HALF_UP);
			BigDecimal payAmount = paymentBillDTO.getPayAmount().multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP);

			if (!totalFee.equals(payAmount)) {
				log.info("支付金额与订单金额不符");
                paymentBillDTO.setStatus(PaymentStatusEnum.PAY_FAIL.getCode());
				wxNotifyResponse.put("return_msg","支付金额与订单金额不符");
			}else{
				paymentBillDTO.setStatus(PaymentStatusEnum.PAY_SUCCESS.getCode());
				paymentBillDTO.setActualPayAmount(totalFee.divide(new BigDecimal(100)));
				for (PaymentBillDetailDTO dto : paymentBillDTO.getDetailDTOList()) {
					dto.setActualPayAmount(dto.getPayAmount());
				}
				ifSuccess = true;
				wxNotifyResponse.put("return_code","SUCCESS");
				wxNotifyResponse.put("return_msg","支付成功");
			}
		}else{
            paymentBillDTO.setStatus(PaymentStatusEnum.PAY_FAIL.getCode());
			paymentBillDTO.setExtStatus(PaymentStatusEnum.PAY_FAIL.getCode());
		}
		paymentBillBiz.updatePaymentBill(paymentBillDTO, paymentRequest.getOperator());
		responseDTO.setContent(wxNotifyResponse);
		responseDTO.setPaymentBillDTO(paymentBillDTO);
		if (doNotify) {
			super.notifyPay(paymentBillDTO,ifSuccess,ChannelCodeEnum.WEIXIN.getCode(),paymentRequest.getOperator());
		}
		return responseDTO;
	}

	@Override
	public PaymentResponseDTO handleRefund(RefundRequestWrapDTO paymentRequest, String operator) {
		log.info(" 微信退款 handleRefund {} ", JSON.toJSONString(paymentRequest));
		PaymentResponseDTO paymentResponseDTO = new PaymentResponseDTO();
		paymentResponseDTO.setSuccess(false);
        Map<String, String> response = createRefund(paymentRequest);
        if(response.get("result_code").equals("SUCCESS")){
            //接口幂等性处理
            BigDecimal refundFee = new BigDecimal(response.get("refund_fee")).setScale(2, RoundingMode.HALF_UP);
            BigDecimal refundAmount = paymentRequest.getRefundAmount().multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP);
            BigDecimal totalFee = new BigDecimal(response.get("total_fee")).setScale(2, RoundingMode.HALF_UP);
            BigDecimal totalAmount = paymentRequest.getTotalPayAmount().multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP);
            if (refundFee.compareTo(refundAmount) != 0 ||totalFee.compareTo(totalAmount) != 0) {
                log.info("退款金额或订单金额不符");
                return paymentResponseDTO;
            }else{
                paymentResponseDTO.setSuccess(true);
            }
			RefundBillDTO refundBillDTO = paymentRequest.getRefundBillDTO();
			refundBillDTO.setActualPayAmount(refundBillDTO.getPayAmount());
			refundBillDTO.setStatus(PaymentStatusEnum.PAY_SUCCESS.getCode());
			refundBillBiz.update(refundBillDTO, operator);
			super.logsRefund(refundBillDTO, operator);
        }
		return paymentResponseDTO;
	}

	@Override
	public PaymentCallbackResponseDTO handleRefundCallback(PaymentCallbackRequestDTO paymentRequest) {
        log.info(" 微信退款 handleRefundCallback {} ", JSON.toJSONString(paymentRequest));
        PaymentCallbackResponseDTO responseDTO = new PaymentCallbackResponseDTO();
        Map<String,String> wxNotifyResponse= new HashMap<>();
        wxNotifyResponse.put("return_code","FAIL");

		if(paymentRequest == null){
			log.error("微信退款回调为空:"+paymentRequest);
			wxNotifyResponse.put("return_msg","微信退款回调为空");
			responseDTO.setContent(wxNotifyResponse);
			return responseDTO;
		}

		Map<String,Object> wxNotifyRequest = paymentRequest.getParms();

        if (CsStringUtils.isEmpty(paymentRequest.getPaymentBillId())) {
            log.error("支付单ID为空:"+paymentRequest.getPaymentBillId());
            wxNotifyResponse.put("return_msg","支付单ID为空");
            responseDTO.setContent(wxNotifyResponse);
            return responseDTO;
        }
		// 退款单
        Optional<RefundBillDTO> optional = refundBillBiz.findByNo((String) wxNotifyRequest.get("out_refund_no"));
        if (optional.isEmpty()) {
        	throw new BizException(BasicCode.DB_DATA_NOT_FOUND);
		}
		RefundBillDTO refundBillDTO = optional.get();
		// 支付单
        PaymentBillDTO oldPaymentBillDTO = paymentBillBiz.findByPaymentBillNo((String) wxNotifyRequest.get("out_trade_no"));

		if (PaymentStatusEnum.PAY_SUCCESS.getCode().equals(refundBillDTO.getStatus())) {
			log.info("该笔支付单已处理");
			return responseDTO;
		}

		Assert.notNull(oldPaymentBillDTO, "原支付单为空: out_trade_no: "+ wxNotifyRequest.get("out_trade_no"));

        //接口幂等性处理
        BigDecimal totalFee = new BigDecimal((String)wxNotifyRequest.get("total_fee")).setScale(2, RoundingMode.HALF_UP);
        BigDecimal refundAmount = new BigDecimal((String)wxNotifyRequest.get("refund_fee")).setScale(2, RoundingMode.HALF_UP);
        if (refundBillDTO.getPayAmount().multiply(new BigDecimal(100)).compareTo(refundAmount) != 0 ||totalFee.compareTo(oldPaymentBillDTO.getPayAmount().multiply(new BigDecimal(100))) != 0) {
            log.info("退款金额或订单金额不符");
            wxNotifyResponse.put("return_msg","退款金额或订单金额不符");
            responseDTO.setContent(wxNotifyResponse);
            return responseDTO;
        }
		refundBillDTO.setActualPayAmount(refundBillDTO.getPayAmount());
		refundBillDTO.setStatus(PaymentStatusEnum.PAY_SUCCESS.getCode());

		refundBillBiz.update(refundBillDTO, paymentRequest.getOperator());

        wxNotifyResponse.put("return_code","SUCCESS");
        wxNotifyResponse.put("return_msg","退款成功");
        responseDTO.setContent(wxNotifyResponse);
		responseDTO.setChangeStatus(true);
        return responseDTO;
	}

	@Override
	public PaymentResponseDTO handleFreeze(PaymentRequestWrapDTO paymentRequest, String operator) {
		log.info("微信支付不支持此操作");
		return null;
	}

	@Override
	public PaymentCallbackResponseDTO handleFreezeCallback(PaymentCallbackRequestDTO paymentRequest) {
		log.info("微信支付不支持此操作");
		return null;
	}

	@Override
	public PaymentCallbackResponseDTO handleUnFreezeCallback(PaymentCallbackRequestDTO paymentRequest) {
		log.info("微信支付不支持此操作");
		return null;
	}

	@Override
	public PaymentResponseDTO handlePayDeposit(PaymentRequestWrapDTO payRequest, String operator) {
		log.info("微信支付不支持此操作");
		return null;
	}

	@Override
	public PaymentCallbackResponseDTO handlePayDepositCallBack(PaymentCallbackRequestDTO payRequest) {
		log.info("微信支付不支持此操作");
		return null;
	}

	@Override
	public PaymentResponseDTO handleCancelDeposit(PaymentRequestWrapDTO payRequest, String operator) {
		log.info("微信支付不支持此操作");
		return null;
	}

	@Override
	public PaymentCallbackResponseDTO handleCancelDepositCallBack(PaymentCallbackRequestDTO payRequest) {
		log.info("微信支付不支持此操作");
		return null;
	}

	@Override
	public PaymentResponseDTO handleConfiscateDeposit(PaymentRequestWrapDTO payRequest, String operator) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public PaymentCallbackResponseDTO handleConfiscateDepositCallBack(PaymentCallbackRequestDTO payRequest) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public PaymentResponseDTO handleTransferDeposit(PaymentRequestWrapDTO payRequest, String operator) {
		log.info("微信支付不支持此操作");
		return null;
	}

	@Override
	public PaymentCallbackResponseDTO handleTransferDepositCallBack(PaymentCallbackRequestDTO payRequest) {
		log.info("微信支付不支持此操作");
		return null;
	}

	@Override
	public PaymentResponseDTO handleCancelPayment(PaymentRequestWrapDTO paymentRequest, String operator) {
		log.info(" 微信支付 handleCancelPayment {} ", JSON.toJSONString(paymentRequest));
		PaymentResponseDTO result = new PaymentResponseDTO();
		result.setSuccess(false);
		if(paymentRequest == null){
			log.info("参数为空");
			throw new BizException(BasicCode.PARAM_NULL);
		}
		if(paymentRequest.getPaymentBillDTO().getCreateTime() == null) {
		    log.info("微信预支付时间为空！");
		    return null;
        }
		//当前支付单状态为未支付且支付窗口已过期
		if( System.currentTimeMillis() - paymentRequest.getPaymentBillDTO().getCreateTime().getTime() > wxProperties.getEffectiveTimeSeconds()*1000  &&
				"NOTPAY".equals(paymentRequest.getPaymentBillDTO().getExtStatus())){
			result.setSuccess(true);
			return result;
		}
		// 2.订单创建时间与当前时间时间差小于5M
		if((paymentRequest.getPaymentBillDTO().getCreateTime().getTime() + 5*60*1000) > System.currentTimeMillis()){
			log.info("订单生成后不能马上调用关单接口，最短调用时间间隔为5分钟");
			throw new BizException(PayCode.WECHAT_CLOSEORDERTIME_ERROR);
		}
		Map<String, String> reqData;
		//调用open-api发送请求
		try {
			reqData = buildQueryRequest(paymentRequest.getPaymentBillDTO(),paymentRequest.getMemberChannelDTO());
		} catch (Exception e) {
			throw new BizException(PayCode.WECHAT_SIGN_ERROR);
		}
		String xmlRequest;
		try {
			xmlRequest = WXPayUtil.mapToXml(reqData);
		} catch (Exception e) {
			throw new BizException(PayCode.WECHAT_BEANTOXML_ERROR);
		}
		log.info("发送微信请求："+xmlRequest);
		String xmlResponse = weXinConnector.wxPostRequest(xmlRequest, wxProperties.getPrePaymentUrl() + "/pay/closeorder",paymentRequest.getPaymentBillDTO().getPaymentBillNo()).getData();
		log.info("接收微信请求："+xmlResponse);

		Map<String, String> resData;
		try {
			resData = WXPayUtil.xmlToMap(xmlResponse);
		} catch (Exception e) {
			throw new BizException(PayCode.WECHAT_XMLTOBEAN_ERROR);
		}

		String RETURN_CODE = "return_code";
		String return_code = resData.get(RETURN_CODE);
		if (!resData.containsKey(RETURN_CODE) || !return_code.equals(WXPayConstants.SUCCESS)) {
			throw new BizException(PayCode.WECHAT_REQUEST_FAIL, resData.get("return_msg"));
		}
		//响应签名验证
		boolean valid = false;
		try {
			valid = WXPayUtil.isSignatureValid(resData, paymentRequest.getMemberChannelDTO().getMchKey(), WXPayConstants.SignType.MD5);
		} catch (Exception e) {
			throw new BizException(PayCode.WECHAT_SIGN_ERROR);
		}
		if (!valid) {
			log.info("验签失败 微信返回的sign:", resData.get("sign"));
			throw new BizException(PayCode.WECHAT_SIGN_ERROR);
		}
		if ("FAIL".equals(resData.get("result_code"))) {
			log.info("微信关闭订单接口请求失败");
			throw new BizException(PayCode.WECHAT_REQUEST_FAIL);
		}
		String tradeState = resData.get("result_code");
		log.info("关闭订单接口调用完成");
		if(tradeState.equals("SUCCESS")){
			result.setSuccess(true);
			return result;
		}
		return result;
	}

	@Override
	public HealthResponseDTO checkHealth() {
		return null;
	}

	private String getAppId(String clientType, String appType) {
		String appId;
        if (CsStringUtils.isBlank(clientType)) {
			appId = wxProperties.getAppId();
		}else if(clientType.equalsIgnoreCase("app")){
            if (CsStringUtils.isBlank(appType)) {
				appId = wxProperties.getBuyerAppId();
			}else{
				if(appType.equalsIgnoreCase("seller")){
					appId = wxProperties.getSellerAppId();
				}else if(appType.equalsIgnoreCase("driver")){
					appId = wxProperties.getDriverAppId();
				}else{
					appId = wxProperties.getBuyerAppId();
				}
			}
		}else{
			appId = wxProperties.getAppId();
		}
		return appId;
	}

	/**
	 * 构建订单查询参数对象
	 * @param paymentBillDTO
	 * @param memberChannelDTO
	 * @return
	 * @throws Exception
	 */
	private Map<String, String> buildQueryRequest(PaymentBillDTO paymentBillDTO,MemberChannelDTO memberChannelDTO) throws Exception {
		Map<String, String> reqData = Maps.newHashMap();

		String appId = getAppId(paymentBillDTO.getClientType(), paymentBillDTO.getAppType());

		reqData.put("appid", appId);
		reqData.put("mch_id", memberChannelDTO.getMchNo());
		reqData.put("nonce_str", WXPayUtil.generateNonceStr());
		reqData.put("out_trade_no", paymentBillDTO.getPaymentBillNo());
		reqData.put("sign_type", WXPayConstants.MD5);
		//生成摘要签名
		String signature = WXPayUtil.generateSignature(reqData, memberChannelDTO.getMchKey(), WXPayConstants.SignType.MD5);
		reqData.put("sign", signature);

		return reqData;
	}

    /**
     * 构建订单查询退款参数对象
     * @param paymentBillDTO
     * @param memberChannelDTO
     * @return
     * @throws Exception
     */

    private WXQueryRefundRequest buildQueryRefundRequest(PaymentBillDTO paymentBillDTO,MemberChannelDTO memberChannelDTO) throws Exception {
        WXQueryRefundRequest wxQueryRefundRequest = new WXQueryRefundRequest();
        if (CsStringUtils.isBlank(paymentBillDTO.getClientType())) {
			wxQueryRefundRequest.setAppid(wxProperties.getAppId());
		}else if(paymentBillDTO.getClientType().equals("app")){
            if (CsStringUtils.isBlank(paymentBillDTO.getAppType())) {
				wxQueryRefundRequest.setAppid(wxProperties.getBuyerAppId());
			}else{
				if(paymentBillDTO.getAppType().equals("seller")){
					wxQueryRefundRequest.setAppid(wxProperties.getSellerAppId());
				}else if(paymentBillDTO.getAppType().equals("driver")){
					wxQueryRefundRequest.setAppid(wxProperties.getDriverAppId());
				}else{
					wxQueryRefundRequest.setAppid(wxProperties.getBuyerAppId());
				}
			}
		}else{
			wxQueryRefundRequest.setAppid(wxProperties.getAppId());
		}
        wxQueryRefundRequest.setMch_id(memberChannelDTO.getMchNo());
        wxQueryRefundRequest.setNonce_str(UUID.randomUUID().toString().replace("-", ""));
        wxQueryRefundRequest.setOut_trade_no(paymentBillDTO.getPaymentBillNo());
        //生成摘要签名
        String sign = SignatureUtil.getDigest(SignatureUtil.assemblyParam(wxQueryRefundRequest, memberChannelDTO.getMchKey()), AlgTypeEnum.MD5);
        wxQueryRefundRequest.setSign(sign.toUpperCase());

        return wxQueryRefundRequest;
    }

	/**
	 * 创建支付订单
	 * @param paymentRequest
	 * @return
	 */

	private Map<String, Object> createPrePaymentOrder(PaymentRequestWrapDTO paymentRequest) {
		String clientType = "";
		String openId = "";
		if(paymentRequest.getClientType().equals(ClientType.MINI.getCode())){
			clientType = "JSAPI";
		}else if(paymentRequest.getClientType().equals(ClientType.APP.getCode()) ||paymentRequest.getClientType().equals(ClientType.WAP.getCode()) ){
			clientType = "APP";
		}else if(paymentRequest.getClientType().equals(ClientType.PC.getCode())){
			clientType = "NATIVE";
		}
		paymentRequest.setClientType(clientType);
		if("JSAPI".equals(clientType)){
			String code = paymentRequest.getCode();
            if (CsStringUtils.isEmpty(code)) {
				log.info("参数code为空");
				throw new BizException(BasicCode.PARAM_NULL,"参数code为空");
			}
			openId = onLogin(code);
		}

		try {
			Map<String, String> reqData = buildPayRequest(paymentRequest,openId);
			String xmlRequest = WXPayUtil.mapToXml(reqData);
			log.info("xmlRequest:"+xmlRequest);
            if (CsStringUtils.isEmpty(xmlRequest) || CsStringUtils.isEmpty(wxProperties.getPrePaymentUrl())) {
				log.info("微信支付统一下单接口发送post请求参数为空！");
				throw new BizException(BasicCode.PARAM_NULL,"微信支付统一下单接口发送post请求参数为空！");
			}
			//此处需要调用open-api的http请求接口，发送post请求
			String xmlResponse = weXinConnector.wxPostRequest(xmlRequest, wxProperties.getPrePaymentUrl() + "/pay/unifiedorder",paymentRequest.getPaymentBillDTO().getPaymentBillNo()).getData();
			log.info("xmlResponse:" + xmlResponse);
            if (CsStringUtils.isBlank(xmlResponse)) {
				log.info("微信支付统一下单接口发送post请求返回为空！");
				throw new BizException(BasicCode.PARAM_NULL,"微信支付统一下单接口发送post请求返回为空！");
			}
			Map<String, String> respData = WXPayUtil.xmlToMap(xmlResponse);
			log.info("微信返回map: {}", respData);

			String RETURN_CODE = "return_code";
			String return_code = respData.get(RETURN_CODE);
			if (!respData.containsKey(RETURN_CODE) || !return_code.equals(WXPayConstants.SUCCESS)) {
				throw new BizException(PayCode.WECHAT_REQUEST_FAIL,respData.get("return_msg"));
			}
			//响应签名验证
			boolean valid = WXPayUtil.isSignatureValid(respData, paymentRequest.getMemberChannelDTO().getMchKey(), WXPayConstants.SignType.MD5);
			if (!valid) {
				log.info("响应签名验证失败");
				throw new BizException(PayCode.WECHAT_SIGN_ERROR);
			}

			if ("FAIL".equals(respData.get("result_code"))) {
				log.info("wxPayResponse返回错误信息："+respData.get("err_code_des"));
				throw new BizException(PayCode.WECHAT_REQUEST_FAIL,"wxPayResponse返回错误信息："+respData.get("err_code_des"));
			}

			Map<String, Object> paramMap = new HashMap<>();
			paramMap.put("prepayId", respData.get("prepay_id"));
			paramMap.put("paymentBillNo", paymentRequest.getPaymentBillDTO().getPaymentBillNo());
			if(paymentRequest.getClientType().equals("NATIVE")){
				paramMap.put("code_url", respData.get("code_url"));
			}

			return paramMap;
		} catch (Exception e) {
			log.error(e.toString());
			throw new BizException(BasicCode.UNDEFINED_ERROR,e.getMessage());
		}
	}


	/**
	 * 构建预支付请求
	 * @param paymentRequest
	 * @param openId
	 * @return
	 */

	private Map<String, String> buildPayRequest(PaymentRequestWrapDTO paymentRequest,String openId) throws Exception {
		MemberChannelDTO memberChannelDTO = paymentRequest.getMemberChannelDTO();
		PaymentBillDTO paymentBillDTO = paymentRequest.getPaymentBillDTO();

		Map<String, String> reqData = Maps.newHashMap();

		String appId = getAppId(paymentRequest.getClientType(), paymentRequest.getAppType());
		reqData.put("appid", appId);
		reqData.put("mch_id", memberChannelDTO.getMchNo());

        if (!CsStringUtils.isBlank(openId)) {
			reqData.put("openid", openId);
		}
		reqData.put("body", paymentRequest.getBody());
		//生成随机字符串
		reqData.put("nonce_str", WXPayUtil.generateNonceStr());
		//支付结果回调url
		reqData.put("notify_url", wxProperties.getNotifyUrl());
		//交易订单号
		reqData.put("out_trade_no", paymentBillDTO.getPaymentBillNo());
		//设置客户端ip
		reqData.put("spbill_create_ip", paymentRequest.getClientIP());
		//订单支付金额(单位：分)
		//3.
		reqData.put("total_fee", String.valueOf(paymentBillDTO.getPayAmount().multiply(new BigDecimal(100)).intValue()));
		//交易类型
		reqData.put("trade_type", paymentRequest.getClientType());
		//重置创建时间 后续比对需要
		if( paymentBillDTO.getCreateTime() == null ){
			paymentBillDTO.setCreateTime(new Date(System.currentTimeMillis()));
		}
		Long time = paymentBillDTO.getCreateTime().getTime();
		Date now = new Date(time);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
		String dateNowStr = sdf.format(now);
		reqData.put("time_start",dateNowStr);
        //支付有效时长
		Date date = new Date(time+wxProperties.getEffectiveTimeSeconds()*1000);
		String endDateStr = sdf.format(date);
		reqData.put("time_expire",endDateStr);

		reqData.put("sign_type", WXPayConstants.MD5);
		//生成摘要签名
		String sign = WXPayUtil.generateSignature(reqData, memberChannelDTO.getMchKey(), WXPayConstants.SignType.MD5);
		reqData.put("sign", sign);
		log.info("响应签名生成的signStr:" + sign);
		return reqData;
	}

	/**
	 * 创建退款
	 * @param paymentRequest
	 * @return
	 */
	private Map<String, String> createRefund(RefundRequestWrapDTO paymentRequest) {
		try {
			Map<String, String> reqData = buildRefundRequest(paymentRequest);
			String xmlRequest = WXPayUtil.mapToXml(reqData);
			log.info("xmlRequest:"+xmlRequest);
            if (CsStringUtils.isEmpty(xmlRequest) || CsStringUtils.isEmpty(wxProperties.getRefundUrl())) {
				log.info("微信支付退款接口发送post请求参数为空！");
				throw new BizException(BasicCode.PARAM_NULL);
			}
			//此处需要调用open-api的http请求接口，发送post请求
			String xmlResponse = weXinConnector.wxRefundPostRequest(xmlRequest, wxProperties.getRefundUrl(),paymentRequest.getRefundBillDTO().getRefundBillNo(),paymentRequest.getMemberChannelDTO().getMchNo()).getData();
			log.info("xmlResponse:"+xmlResponse);
            if (CsStringUtils.isEmpty(xmlResponse)) {
				log.info("微信支付退款接口发送post请求返回为空！");
				throw new BizException(BasicCode.PARAM_NULL,"微信支付退款接口发送post请求返回为空！");
			}
			Map<String, String> response = WXPayUtil.xmlToMap(xmlResponse);
			log.info("xmlMap: {}", response);

			String RETURN_CODE = "return_code";
			String return_code = response.get(RETURN_CODE);
			if (!response.containsKey(RETURN_CODE) || !return_code.equals(WXPayConstants.SUCCESS)) {
				throw new BizException(PayCode.WECHAT_REQUEST_FAIL,response.get("return_msg"));
			}
			//响应签名验证
			boolean valid = WXPayUtil.isSignatureValid(response, paymentRequest.getMemberChannelDTO().getMchKey(), WXPayConstants.SignType.MD5);
			if (!valid) {
				log.info("响应签名验证失败");
				throw new BizException(PayCode.WECHAT_SIGN_ERROR);
			}

			if ("FAIL".equals(response.get("result_code"))) {
				log.info("微信通信返回结果："+response.get("err_code_des"));
				throw new BizException(PayCode.WECHAT_REQUEST_FAIL,response.get("err_code_des"));
			}

			return response;
		} catch (Exception e) {
			log.error(e.toString());
			throw new BizException(BasicCode.UNDEFINED_ERROR, e.getMessage());
		}
	}

	/**
	 * 构建退款请求
	 * @param paymentRequest
	 * @return
	 */
	private Map<String, String> buildRefundRequest(RefundRequestWrapDTO paymentRequest) throws Exception {
		MemberChannelDTO memberChannelDTO = paymentRequest.getMemberChannelDTO();
		RefundBillDTO refundBillDTO = paymentRequest.getRefundBillDTO();
		Map<String, String> reqData = Maps.newHashMap();

		String clientType = refundBillDTO.getClientType();
		String appType = refundBillDTO.getAppType();

		String appId = getAppId(clientType, appType);
		reqData.put("appid", appId);
		reqData.put("mch_id", memberChannelDTO.getMchNo());
		//生成随机字符串
		reqData.put("nonce_str", WXPayUtil.generateNonceStr());
		//支付结果回调url
		reqData.put("notify_url", wxProperties.getRefundNotifyUrl());
		//交易订单号
		reqData.put("out_trade_no", refundBillDTO.getRelatedPaymentBillNo());
		reqData.put("out_refund_no", refundBillDTO.getRefundBillNo());
		//订单支付金额(单位：分)
		//3.
		reqData.put("total_fee", String.valueOf(paymentRequest.getTotalPayAmount().multiply(new BigDecimal(100)).intValue()));
		//退款金额（单位：分）
		reqData.put("refund_fee", String.valueOf(paymentRequest.getRefundAmount().multiply(new BigDecimal(100)).intValue()));

		reqData.put("refund_desc", "订单:"+refundBillDTO.getRelatedPaymentBillNo()+"退款");

		reqData.put("sign_type", WXPayConstants.MD5);
		//生成摘要签名
		log.info("mchKey: {}", memberChannelDTO.getMchKey());

		String sign = WXPayUtil.generateSignature(reqData, memberChannelDTO.getMchKey());
		log.info("响应签名生成的signStr:"+sign);
		reqData.put("sign", sign);
		return reqData;
	}

	/**
	 * 生成再次支付参数对象
	 * @param paramMap
	 * @return
	 */
	private Map<String,String> generatePaymentParam(Map<String, Object> paramMap,PaymentRequestWrapDTO paymentRequest) {
		MemberChannelDTO memberChannelDTO = paymentRequest.getMemberChannelDTO();
		try {
			Map<String,String> returnMap = new HashMap<>();
			if(paymentRequest.getClientType().equals("APP")){
                if (CsStringUtils.isBlank(paymentRequest.getAppType())) {
					returnMap.put("appid",wxProperties.getBuyerAppId());
				}else{
					if(paymentRequest.getAppType().equals("seller")){
						returnMap.put("appid",wxProperties.getSellerAppId());
					}else if(paymentRequest.getAppType().equals("driver")){
						returnMap.put("appid",wxProperties.getDriverAppId());
					}else{
						returnMap.put("appid",wxProperties.getBuyerAppId());
					}
				}
                returnMap.put("partnerid",memberChannelDTO.getMchNo());
                returnMap.put("prepayid",paramMap.get("prepayId").toString());
                returnMap.put("package","Sign=WXPay");
				returnMap.put("noncestr",UUID.randomUUID().toString().replace("-", ""));
				returnMap.put("timestamp",Long.valueOf(System.currentTimeMillis() / 1000).toString());
			}else{
				returnMap.put("appId",wxProperties.getAppId());
				//小程序签名校验失败，参考微信小程序相关文档：https://pay.weixin.qq.com/wiki/doc/api/wxa/wxa_api.php?chapter=7_7&index=5
                returnMap.put("package","prepay_id=" + paramMap.get("prepayId"));
				returnMap.put("timeStamp",Long.valueOf(System.currentTimeMillis() / 1000).toString());
				returnMap.put("nonceStr",UUID.randomUUID().toString().replace("-", ""));
				returnMap.put("signType",AlgTypeEnum.MD5.getDesc());
			}

			String paySign = WXPayUtil.generateSignature(returnMap, memberChannelDTO.getMchKey());

            if(paymentRequest.getClientType().equals("APP")){
                returnMap.put("sign",paySign.toUpperCase());
            }else{
				returnMap.put("packageData","prepay_id=" + paramMap.get("prepayId"));
                returnMap.put("paySign",paySign.toUpperCase());
            }
			returnMap.put("paymentBillNo",paramMap.get("paymentBillNo").toString());

			if(null != paramMap.get("code_url")){
				String content = paramMap.get("code_url").toString();
				String base64Image = QRCodeUtil.encode(content, BarcodeFormat.QR_CODE,
						3, ErrorCorrectionLevel.forBits(2),"png",200,200);
                if (CsStringUtils.isBlank(base64Image)) {
					log.error("创建二维码图片出错");
					throw new BizException(PayCode.WECHAT_CREATEQRCODE_ERROR,"创建二维码图片出错");
				}
				returnMap.put("base64Image",base64Image);
			}
			log.info("统一下单接口调用完成");
			return returnMap;
		} catch (Exception e) {
			log.error(e.toString());
			throw new BizException(PayCode.UNDEFINED_ERROR,e.getMessage());
		}
	}

	public String onLogin(String code){
		String url=wxProperties.getOpenUrl();
		Map<String,String> map = new HashMap<>();
		map.put("appid",wxProperties.getAppId());
		map.put("secret",wxProperties.getAppSecret());
		map.put("js_code",code);
		map.put("grant_type","authorization_code");
		log.info("用code换取openId请求的数据：url:"+url+",map:"+map.toString());
		String rspJson = weXinConnector.wxGetRequest(url,map).getData();
		log.info("用code换取openId请求返回的数据："+rspJson);
		JSONObject rspJsonObj = JSONObject.parseObject(rspJson);
		String openId = "";
        if (CsStringUtils.isEmpty(rspJsonObj.getString("errcode"))) {
			openId = rspJsonObj.getString("openid");
		}else{
			throw new BizException(PayCode.WECHAT_GETOPENID_ERROR,"获取用户openId失败");
		}
		return  openId;
	}

}
