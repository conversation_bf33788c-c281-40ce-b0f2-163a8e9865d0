package com.ecommerce.pay.v2.controller;

import com.alibaba.fastjson.JSON;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.pay.api.v2.dto.adjust.AdjustPriceBillDTO;
import com.ecommerce.pay.api.v2.dto.adjust.AdjustPriceDTO;
import com.ecommerce.pay.api.v2.dto.adjust.AdjustPriceDetailDTO;
import com.ecommerce.pay.api.v2.dto.adjust.AdjustPriceMemberDTO;
import com.ecommerce.pay.api.v2.dto.adjust.AdjustPricePayResultDTO;
import com.ecommerce.pay.api.v2.dto.adjust.AdjustPriceQueryDTO;
import com.ecommerce.pay.v2.service.IAdjustPriceService;
import com.ecommerce.pay.v2.service.ILogisticsAdjustPriceService;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@RestController
@Tag(name = "AdjustPriceController")
@RequestMapping("/adjustPrice")
public class AdjustPriceController
{
    private final static Logger logger = LoggerFactory.getLogger(AdjustPriceController.class);

    @Autowired
    private IAdjustPriceService adjustPriceService;

    @Autowired
    private ILogisticsAdjustPriceService logisticsAdjustPriceService;
    /**
     * 调价列表
     * @param query
     * @return
     */
    @Operation(summary = "调价列表")
    @PostMapping("/adjustList")
    public PageInfo<AdjustPriceDTO> adjustList(@RequestBody AdjustPriceQueryDTO query)
    {
        return adjustPriceService.getAdjustPriceList(query);
    }

    /**
     * 价格回调详情
     * @param priceAdjustId
     * @return
     */
    @Operation(summary = "价格回调详情")
    @GetMapping("/adjustInfo")
    public AdjustPriceDTO adjustInfo(@RequestParam String priceAdjustId)
    {
        return adjustPriceService.adjustInfo(priceAdjustId);
    }

    /**
     * 查询一个调价的买家列表
     * @param priceAdjustId
     * @param pageNum
     * @param pageSize
     * @return
     */
    @Operation(summary = "查询一个调价的买家列表")
    @GetMapping("/adjustMemberList")
    public PageInfo<AdjustPriceMemberDTO> adjustMemberList(@RequestParam String priceAdjustId, @RequestParam int pageNum, @RequestParam int pageSize)
    {
        return adjustPriceService.getAdjustPriceMemberList(priceAdjustId, pageNum, pageSize);
    }

    /**
     * 查询一个调价的买家的运单列表
     * @param priceAdjustId
     * @param buyerId
     * @param pageNum
     * @param pageSize
     * @return
     */
    @Operation(summary = "查询一个调价的买家的运单列表")
    @GetMapping("/adjustDetailList")
    public PageInfo<AdjustPriceDetailDTO> adjustDetailList(@RequestParam String priceAdjustId, @RequestParam String buyerId, @RequestParam String dealsName, @RequestParam int pageNum, @RequestParam int pageSize)
    {
        return adjustPriceService.getAdjustPriceDetailList(priceAdjustId, buyerId, dealsName, pageNum, pageSize);
    }

    /**
     * 回调记录查询
     * @param priceAdjustId
     * @param waybillId
     * @return
     */
    @Operation(summary = "回调记录查询")
    @GetMapping("/adjustBillList")
    public List<AdjustPriceBillDTO> adjustBillList(@RequestParam String priceAdjustId, @RequestParam String waybillId)
    {
        return adjustPriceService.getAdjustPriceBillList(priceAdjustId, waybillId);
    }

    /**
     * 获取调价涉及到的买家列表
     * @param query
     * @return
     */
    @Operation(summary = "获取调价涉及到的买家列表")
    @PostMapping("/calculateAdjustMember")
    public List<AdjustPriceMemberDTO> calculateAdjustMember(@RequestBody AdjustPriceDTO query)
    {
        return adjustPriceService.calculateAdjustMember(query);
    }

    /**
     * 获取调价涉及到的买家列表
     * @param query
     * @return
     */
    @Operation(summary = "获取调价涉及到的买家运单列表")
    @PostMapping("/calculateAdjustBillList")
    public List<AdjustPriceDetailDTO> calculateAdjustMemberBillList(@RequestBody AdjustPriceDTO query)
    {
        return adjustPriceService.calculateAdjustMemberBillList(query);
    }

    /**
     * 保存调价信息
     * @param dto
     */
    @Operation(summary = "保存调价信息")
    @PostMapping("/savePrice")
    public boolean savePrice(@RequestBody AdjustPriceDTO dto)
    {
        return adjustPriceService.saveAdjustPriceList(dto, dto.getCreateUser());
    }

    @Operation(summary = "ERP价格回调回调")
    @PostMapping("/erpCallback")
    public ItemResult<String> erpCallback(@RequestBody AdjustPricePayResultDTO resultDTO)
    {
        logger.info("erpCallback===》{}", JSON.toJSONString(resultDTO));

        try
        {
            if (resultDTO.getType()!=null && "LOGISTICS".equals(resultDTO.getType())){
                logisticsAdjustPriceService.processErpLogisticsAdjustResult(resultDTO);
            }else{
                adjustPriceService.processErpAdjustResult(resultDTO);
            }
        }
        catch (Exception e)
        {
            String message = e.getMessage();
            logger.error(message, e);
            return new ItemResult<>("01", CsStringUtils.isEmpty(message) ? "调整失败" : message);
        }

        return new ItemResult<>("Ok");
    }
}
