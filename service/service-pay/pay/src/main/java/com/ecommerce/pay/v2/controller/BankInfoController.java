package com.ecommerce.pay.v2.controller;

import com.ecommerce.pay.api.v2.dto.*;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ecommerce.pay.v2.service.IBankInfoService;

import java.util.List;


/**
 * @Created锛�Thu Jan 10 10:45:56 CST 2019
 * <AUTHOR>
 * @Version:2
 * @Description:null
 */

@RestController
@Tag(name = "BankInfoController", description = "银行信息")
@RequestMapping("/bankInfo")
public class BankInfoController {

    @Autowired
    private IBankInfoService iBankInfoService;

    @Operation(summary = "查询银行信息，最多返回10条")
    @PostMapping(value = "/searchBankInfoLimit")
    public List<BankInfoDTO> searchBankInfoLimit(@Parameter(name = "query", description = "银行信息DTO") @RequestBody BankInfoDTO query) {
        return iBankInfoService.searchBankInfoLimit(query);
    }

    @Operation(summary = "查询基础银行")
    @PostMapping(value = "/searchBaseBankLimit")
    public List<BaseBankDTO> searchBaseBankLimit(@Parameter(name = "bankName", description = "银行名称") @RequestParam(required = false) String bankName) {
        return iBankInfoService.searchBaseBankLimit(bankName);
    }

    @Operation(summary = "通过父code查询城市信息")
    @PostMapping(value = "/searchBankCity")
    public List<BankCityDTO> searchBankCity(@Parameter(name = "parentCode", description = "父code") @RequestParam(required = false) String parentCode) {
        return iBankInfoService.searchBankCity(parentCode);
    }


    @Operation(summary = "查询超级网银号银行")
    @PostMapping(value = "/searchSuperBankLimit")
    public List<BaseBankDTO> searchSuperBankLimit(@Parameter(name = "bankName", description = "银行名称") @RequestParam(required = false) String bankName) {
        return iBankInfoService.searchSuperBankLimit(bankName);
    }

    @Operation(summary = "查询银行卡类型和图标")
    @PostMapping(value = "/searchBankCardInfo")
    public BankCardInfoDTO searchBankCardInfo(@Parameter(name = "bankAccount", description = "银行账号") @RequestParam String bankAccount) {
        return iBankInfoService.searchBankCardInfo(bankAccount);
    }

    @Operation(summary = "查询银行卡类型和图标")
    @PostMapping(value = "/searchBankCardInfoByBankName")
    public BankCardInfoDTO searchBankCardInfoByBankName(@Parameter(name = "bankName", description = "银行名称") @RequestParam String bankName) {
        return iBankInfoService.searchBankCardInfoByBankName(bankName);
    }

    @Operation(summary = "查询银行卡类型和图标和超级网银号")
    @PostMapping(value = "/searchBankCardInfoAndSuperBank")
    public BankCardInfoDTO searchBankCardInfoAndSuperBank(@Parameter(name = "bankAccount", description = "银行账号") @RequestParam String bankAccount) {
        return iBankInfoService.searchBankCardInfoAndSuperBank(bankAccount);
    }

    @Operation(summary = "获取大小额联行号")
    @PostMapping(value = "/pageBankInfo")
    public PageInfo<BankInfoDTO> pageBankInfo(@Parameter(name = "bankInfoDTO", description = "银行信息DTO") @RequestBody BankInfoDTO bankInfoDTO) {
        return iBankInfoService.pageBankInfo(bankInfoDTO);
    }

    @Operation(summary = "添加大小额联行号")
    @PostMapping(value = "/addBankInfo")
    public void addBankInfo(@Parameter(name = "bankInfoDTO", description = "银行信息DTO") @RequestBody BankInfoDTO bankInfoDTO) {
        iBankInfoService.addBankInfo(bankInfoDTO);
    }

    @Operation(summary = "删除大小额联行号")
    @PostMapping(value = "/deleteBankInfo")
    public void deleteBankInfo(@Parameter(name = "bankNo", description = "银行编号") @RequestParam String bankNo) {
        iBankInfoService.deleteBankInfo(bankNo);
    }

    @Operation(summary = "分页查询超级网银号")
    @PostMapping(value = "/pageSuperBank")
    public PageInfo<SuperBankDTO> pageSuperBank(@Parameter(name = "superBankDTO", description = "超级银行DTO") @RequestBody SuperBankDTO superBankDTO) {
        return iBankInfoService.pageSuperBank(superBankDTO);
    }

    @Operation(summary = "添加超级网银号")
    @PostMapping(value = "/addSuperBank")
    public void addSuperBank(@Parameter(name = "superBankDTO", description = "超级银行DTO") @RequestBody SuperBankDTO superBankDTO) {
        iBankInfoService.addSuperBank(superBankDTO);
    }

    @Operation(summary = "删除超级网银号")
    @PostMapping(value = "/deleteSuperBank")
    public void deleteSuperBank(@Parameter(name = "bankNo", description = "银行编号") @RequestParam String bankNo) {
        iBankInfoService.deleteSuperBank(bankNo);
    }

}
