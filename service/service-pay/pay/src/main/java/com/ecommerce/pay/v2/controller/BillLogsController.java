package com.ecommerce.pay.v2.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.pay.api.v2.dto.bill.BillLogsDTO;
import com.ecommerce.pay.api.v2.dto.bill.DeliverWayUpdateDTO;
import com.ecommerce.pay.api.v2.dto.bill.TradingFlowExportDTO;
import com.ecommerce.pay.api.v2.dto.electricInvoiceAccount.PageQuery;
import com.ecommerce.pay.v2.service.IBillLogsService;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Set;


/**
 * <AUTHOR>
 * @created 10:19 10/09/2019
 * @description TODO
 */
@RestController
@Tag(name = "BillLogsController", description = "流水")
@RequestMapping("/billLogs")
public class BillLogsController {

    @Autowired
    private IBillLogsService logsService;

    @Operation(summary = "获取支付渠道信息给资金流水页面查询")
    @GetMapping(value = "/findMemberPayChannelForQuery")
    public Set<String> findMemberPayChannelForQuery(@Parameter(name = "memberId", description = "会员id") @RequestParam String memberId, @Parameter(name = "payerFlg", description = "卖家买家标志") @RequestParam Integer payerFlg) {
        return logsService.findMemberPayChannelForQuery(memberId, payerFlg);
    }

    @Operation(summary = "分页查询流水")
    @PostMapping(value = "/pageBillLogs")
    public PageInfo<BillLogsDTO> pageBillLogs(@Parameter(name = "pageQuery", description = "交易日志分页查询对象") @RequestBody PageQuery<BillLogsDTO> pageQuery) {
        return logsService.pageBillLogs(pageQuery);
    }

    @Operation(summary = "流水导出")
    @PostMapping(value = "/excelExport")
    public void excelExport(@Parameter(name = "pageQuery", description = "交易日志分页查询对象") @RequestBody PageQuery<BillLogsDTO> pageQuery,
                            HttpServletResponse response) {
        logsService.excelExport(pageQuery, response);
    }

    @Operation(summary = "同步erp流水")
    @PostMapping(value = "/saveErpBillLogs")
    public ItemResult<Boolean> saveErpBillLogs(@Parameter(name = "logsDTO", description = "交易日志") @RequestBody BillLogsDTO logsDTO) {
        logsService.saveErpBillLogs(logsDTO);
        return new ItemResult<>(true);
    }

    @Operation(summary = "更新订单配送方式")
    @PostMapping(value = "/updateDeliverWay")
    public void updateDeliverWay(@RequestBody List<DeliverWayUpdateDTO> list) {
        logsService.updateDeliverWay(list);
    }

    @Operation(summary = "导出交易流水")
    @PostMapping(value = "/exportTradingFlow")
    public List<TradingFlowExportDTO> exportTradingFlow(@Parameter(name = "queryDTO", description = "交易日志对象") @RequestBody BillLogsDTO queryDTO) {
        return logsService.exportTradingFlow(queryDTO);
    }
}
