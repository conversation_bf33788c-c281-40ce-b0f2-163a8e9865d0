package com.ecommerce.pay.v2.controller;

import com.ecommerce.logistics.api.dto.carriage.SocialDriverCarriageDTO;
import com.ecommerce.mq.core.MQMessage;
import com.ecommerce.pay.api.v2.dto.driver.DriverWaybillPayDTO;
import com.ecommerce.pay.api.v2.dto.driver.PlatformSummaryInfoDTO;
import com.ecommerce.pay.api.v2.dto.driver.PlatformSummaryInfoPayDTO;
import com.ecommerce.pay.api.v2.dto.driver.PlatformSummaryInfoQueryDTO;
import com.ecommerce.pay.api.v2.dto.driver.WaybillLogisticsInfoDTO;
import com.ecommerce.pay.api.v2.dto.driver.WaybillLogisticsInfoQueryDTO;
import com.ecommerce.pay.api.v2.dto.electricInvoiceAccount.PageQuery;
import com.ecommerce.pay.v2.service.IDriverLogisticsCostService;
import com.ecommerce.pay.v2.service.jobhandler.GnetePlatformBatchTransferCheckJob;
import com.ecommerce.pay.v2.service.synchandler.SynnAddDriverCarriageItemHandler;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@RestController
@Tag(name = "DriverLogisticsCostController", description = "个体司机物流费相关接口服务")
@RequestMapping("/driverLogisticsCost")
public class DriverLogisticsCostController {

    @Autowired
    private IDriverLogisticsCostService driverLogisticsCostService;
    @Autowired
    private SynnAddDriverCarriageItemHandler synnAddDriverCarriageItemHandler;
    @Autowired
    private GnetePlatformBatchTransferCheckJob gnetePlatformBatchTransferCheckJob;

    @Operation(summary = "供物流中心调用-根据运单号查询已支付司机费用的运单的支付流水号")
    @PostMapping(value = "/findTransOrderNoByWaybill")
    public List<DriverWaybillPayDTO> findTransOrderNoByWaybill(@RequestBody List<String> waybillNumList) {
        return driverLogisticsCostService.findTransOrderNoByWaybill(waybillNumList);
    }

    @Operation(summary = "内部调用-重新触发")
    @PostMapping(value = "/reTryAddDriverCarriageItem")
    public Boolean reTryAddDriverCarriageItem(@RequestBody SocialDriverCarriageDTO dto) {
        synnAddDriverCarriageItemHandler.handle(MQMessage.builder().data(dto).build());
        return true;
    }

    @Operation(summary = "平台接口-个体司机运费管理-按日汇总到平台的司机费用数据列表查询")
    @PostMapping(value = "/pageDriverCostSummarizeToPlatform")
    public PageInfo<PlatformSummaryInfoDTO> pageDriverCostSummarizeToPlatform(@RequestBody PageQuery<PlatformSummaryInfoQueryDTO> dto) {
        return driverLogisticsCostService.pageDriverCostSummarizeToPlatform(dto);
    }

    @Operation(summary = "平台接口-个体司机运费管理-按日汇总到平台的司机费用数据-去支付(平台按日支付费用给所有司机)")
    @PostMapping(value = "/platformSummaryInfoPay")
    public Boolean platformSummaryInfoPay(@RequestBody PlatformSummaryInfoPayDTO dto) {
        return driverLogisticsCostService.platformSummaryInfoPay(dto);
    }

    @Operation(summary = "内部接口-触发支付结果检查任务")
    @PostMapping(value = "/platformBatchTransferCheck")
    public Boolean platformBatchTransferCheck() throws Exception {
        gnetePlatformBatchTransferCheckJob.execute();
        return true;
    }

    @Operation(summary = "平台接口-个体司机运费管理-司机运费明细数据-翻页查询")
    @PostMapping(value = "/pageDriverCostItem")
    public PageInfo<WaybillLogisticsInfoDTO> pageDriverCostItem(@RequestBody PageQuery<WaybillLogisticsInfoQueryDTO> dto) {
        return driverLogisticsCostService.pageDriverCostItem(dto);
    }

    @Operation(summary = "内部测试调用接口-通知物流支付完成")
    @PostMapping(value = "/notifyLogistics")
    public Boolean notifyLogistics(@RequestParam String day, @RequestParam String accountId) {
        return driverLogisticsCostService.notifyLogistics(day, accountId);
    }
}
