package com.ecommerce.pay.v2.controller;

import com.ecommerce.common.result.PageData;
import com.ecommerce.pay.api.v2.dto.electricInvoiceAccount.*;
import com.ecommerce.pay.v2.service.IElectricInvoiceAccountService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;


/**
 * @Created锛�Sun Apr 28 10:43:08 CST 2019
 * <AUTHOR>
 * @Version:2
 * @Description:电子发票管理服务
 */

@RestController
@Tag(name = "ElectricInvoiceAccountController", description = "电子发票账户")
@RequestMapping("/electricInvoiceAccount")
public class ElectricInvoiceAccountController {

    @Autowired
    private IElectricInvoiceAccountService iElectricInvoiceAccountService;

    @Operation(summary = "编辑卖家开票账户")
    @PostMapping(value = "/editInvoiceAccount")
    public void editInvoiceAccount(@Parameter(name = "saveInvoiceAccountDTO", description = "保存开票账户DTO") @RequestBody SaveInvoiceAccountDTO saveInvoiceAccountDTO) {
        iElectricInvoiceAccountService.editInvoiceAccount(saveInvoiceAccountDTO);
    }

    @Operation(summary = "删除卖家开票账户")
    @PostMapping(value = "/deleteInvoiceAccount")
    public void deleteInvoiceAccount(@Parameter(name = "deleteInvoiceAccountDTO", description = "删除卖家开票账户DTO") @RequestBody DeleteInvoiceAccountDTO deleteInvoiceAccountDTO) {
        iElectricInvoiceAccountService.deleteInvoiceAccount(deleteInvoiceAccountDTO);
    }

    @Operation(summary = "获取发票账户详情  已开户信息")
    @PostMapping(value = "/queryInvoiceAccountDetail")
    public InvoiceAccountDetailDTO queryInvoiceAccountDetail(@Parameter(name = "sellerId", description = "卖家id") @RequestParam String sellerId) {
        return iElectricInvoiceAccountService.queryInvoiceAccountDetail(sellerId);
    }

    @Operation(summary = "分页查询开票账户列表")
    @PostMapping(value = "/queryInvoiceAccountList")
    public PageData<InvoiceAccountListDTO> queryInvoiceAccountList(@Parameter(name = "pageQuery", description = "开票账户查询DTO分页查询对象") @RequestBody PageQuery<InvoiceAccountQueryDTO> pageQuery) {
        return iElectricInvoiceAccountService.queryInvoiceAccountList(pageQuery);
    }

    @Operation(summary = "录入卖家开票账户")
    @PostMapping(value = "/enterInvoiceAccount")
    public String enterInvoiceAccount(@Parameter(name = "saveInvoiceAccountDTO", description = "保存开票账户DTO") @RequestBody SaveInvoiceAccountDTO saveInvoiceAccountDTO) {
        return iElectricInvoiceAccountService.enterInvoiceAccount(saveInvoiceAccountDTO);
    }

    @Operation(summary = "批量查询卖家发票账户 没有的 value存空")
    @PostMapping(value = "/queryInvoiceAccountStatus")
    public Map<String, InvoiceAccountDTO> queryInvoiceAccountStatus(@Parameter(name = "sellerIdList", description = "卖家id列表") @RequestBody List<String> sellerIdList) {
        return iElectricInvoiceAccountService.queryInvoiceAccountStatus(sellerIdList);
    }

}
