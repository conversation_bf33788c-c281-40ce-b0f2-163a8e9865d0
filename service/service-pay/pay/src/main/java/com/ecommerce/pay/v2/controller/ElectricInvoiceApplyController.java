package com.ecommerce.pay.v2.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.pay.api.v2.dto.electricInvoiceAccount.PageQuery;
import com.ecommerce.pay.api.v2.dto.invoice.*;
import com.ecommerce.pay.v2.service.IElectricInvoiceApplyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * @Created锛�Tue May 07 09:56:14 CST 2019
 * <AUTHOR>
 * @Version:2
 * @Description:电子发票申请服务
 */

@RestController
@Tag(name = "ElectricInvoiceApplyController", description = "电子发票申请")
@RequestMapping("/electricInvoiceApply")
public class ElectricInvoiceApplyController {

    @Autowired
    private IElectricInvoiceApplyService iElectricInvoiceApplyService;

    @Operation(summary = "批量开票")
    @PostMapping(value = "/batchInvoice")
    public ItemResult<Void> batchInvoice() {
        return iElectricInvoiceApplyService.batchInvoice();
    }

    @Operation(summary = "获取发票申请详情")
    @PostMapping(value = "/queryInvoiceApplyDetail")
    public ItemResult<InvoiceApplyDetailDTO> queryInvoiceApplyDetail(@Parameter(name = "arg0", description = "申请id") @RequestParam String arg0) {
        return iElectricInvoiceApplyService.queryInvoiceApplyDetail(arg0);
    }

    @Operation(summary = "审核发票申请")
    @PostMapping(value = "/auditInvoiceApply")
    public ItemResult<Void> auditInvoiceApply(@Parameter(name = "arg0", description = "审批实体") @RequestBody AuditInvoiceApplyDTO arg0) {
        return iElectricInvoiceApplyService.auditInvoiceApply(arg0);
    }

    @Operation(summary = "分页查询开票申请列表")
    @PostMapping(value = "/queryInvoiceApplyList")
    public ItemResult<PageData<InvoiceApplyListDTO>> queryInvoiceApplyList(@Parameter(name = "arg0", description = "发票申请列表查询DTO分页查询对象") @RequestBody PageQuery<InvoiceApplyListQueryDTO> arg0) {
        return iElectricInvoiceApplyService.queryInvoiceApplyList(arg0);
    }

    @Operation(summary = "统计发票申请数据")
    @PostMapping(value = "/statistcsInvoiceApply")
    public ItemResult<StatistcsInvoiceApplyResultDTO> statistcsInvoiceApply(@Parameter(name = "arg0", description = "统计发票申请DTO") @RequestBody StatistcsInvoiceApplyDTO arg0) {
        return iElectricInvoiceApplyService.statistcsInvoiceApply(arg0);
    }

    @Operation(summary = "分页查询发票业务明细列表")
    @PostMapping(value = "/queryInvoiceApplyBizList")
    public ItemResult<PageData<InvoiceApplyBizListDTO>> queryInvoiceApplyBizList(@Parameter(name = "arg0", description = "发票申请业务列表查询DTO分页查询对象") @RequestBody PageQuery<InvoiceApplyBizListQueryDTO> arg0) {
        return iElectricInvoiceApplyService.queryInvoiceApplyBizList(arg0);
    }

    @Operation(summary = "批量申请开票")
    @PostMapping(value = "/batchApplyInvoice")
    public ItemResult<Void> batchApplyInvoice(@Parameter(name = "arg0", description = "开票申请实体列表") @RequestBody List<InvoiceApplyAddDTO> arg0) {
        return iElectricInvoiceApplyService.batchApplyInvoice(arg0);
    }

    @Operation(summary = "红冲发票申请")
    @PostMapping(value = "/revokeInvoiceApply")
    public ItemResult<Void> revokeInvoiceApply(@Parameter(name = "arg0", description = "红冲请求实体") @RequestBody RevokeInvoiceApplyDTO arg0) {
        return iElectricInvoiceApplyService.revokeInvoiceApply(arg0);
    }

}
