package com.ecommerce.pay.v2.controller;

import java.util.List;

import com.ecommerce.pay.api.v2.dto.logisticsAdjust.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.pay.v2.service.ILogisticsAdjustPriceService;


@RestController
@Tag(name = "LogisticsAdjustPriceController")
@RequestMapping("/logisticsAdjustPrice")
public class LogisticsAdjustPriceController {
	
	@Autowired
	private ILogisticsAdjustPriceService logisticsAdjustPriceService;
	
	@Operation(summary = "调价列表")
    @PostMapping("/pageLogisticsAdjustPrice")
	public ItemResult<PageData<LogisticsAdjustPriceQueryDTO>> pageLogisticsAdjustPrice(@RequestBody PageQuery<LogisticsAdjustPriceQueryDTO> pageQuery){
		
		return logisticsAdjustPriceService.pageLogisticsAdjustPrice(pageQuery);
	}
	
	@Operation(summary = "调价用户列表")
	@PostMapping("/queryLogisticsAdjustPriceMember")
	public ItemResult<List<LogisticsAdjustPriceMemberDTO>> queryLogisticsAdjustPriceMember(@RequestBody LogisticsAdjustPriceQueryDTO queryDTO){
		return logisticsAdjustPriceService.queryLogisticsAdjustPriceMember(queryDTO);
	}
	
	@Operation(summary = "物流调价计算")
	@PostMapping("/calculateLogistticsAdjustPrice")
	public ItemResult<LogisticsAdjustPriceCalculateDTO> calculateLogistticsAdjustPrice(@RequestBody LogisticsAdjustPriceCalculateDTO dto ){
		return logisticsAdjustPriceService.calculateLogistticsAdjustPrice(dto);
	}
	
	@Operation(summary = "保存物流调价")
	@PostMapping("/addLogistticsAdjustPrice")
	public ItemResult<Void> addLogistticsAdjustPrice(@RequestBody LogisticsAdjustPriceDTO dto ){
		return logisticsAdjustPriceService.addLogistticsAdjustPrice(dto);
	}
	
	@Operation(summary = "模糊查询方案名")
	@PostMapping("/fuzzyQueryAdjustName")
	public ItemResult<List<String>> fuzzyQueryAdjustName(@Parameter(name = "adjustName", description = "方案名") @RequestParam String adjustName,@RequestParam String memberId){
		return logisticsAdjustPriceService.fuzzyQueryAdjustName(adjustName,memberId);
	}
	
	@Operation(summary = "根据adjustId查询调价用户列表")
	@PostMapping("/queryAdjustPriceMemberListByAdjustId")
	public ItemResult<LogisticsAdjustPriceDTO> queryAdjustPriceMemberListByAdjustId( @RequestParam String adjustId){
		return logisticsAdjustPriceService.queryAdjustPriceMemberListByAdjustId(adjustId);
	}
	
	@Operation(summary = "根据adjustMeberId查询调价运单详情列表")
	@PostMapping("/queryAdjustPriceItemListByAdjustMeberId")
	public ItemResult<List<LogisticsAdjustPriceItemDTO>> queryAdjustPriceItemListByAdjustMeberId( @RequestParam String adjustMeberId){
		return logisticsAdjustPriceService.queryAdjustPriceItemListByAdjustMeberId(adjustMeberId);
	}

	@Operation(summary = "物流调价运单明细列表")
	@PostMapping(value = "/queryLogisticsAdjustPriceItemList")
	public ItemResult<List<LogisticsAdjustPriceItemDTO>> queryLogisticsAdjustPriceItemList(@RequestBody LogisticsAdjustPriceMemberDTO queryDTO){
		return logisticsAdjustPriceService.queryLogisticsAdjustPriceItemList(queryDTO);
	}

	@Operation(summary = "根据adjustMemberId删除物流调价用户")
	@PostMapping(value = "/deleteLogisticsAdjustPriceMemberByAdjustMemberId")
	public ItemResult<Void> deleteLogisticsAdjustPriceMemberByAdjustMemberId(@RequestBody LogisticsAdjustPriceMemberDTO queryDTO){
		return logisticsAdjustPriceService.deleteLogisticsAdjustPriceMemberByAdjustMemberId(queryDTO);
	}

	@Operation(summary = "根据adjustItemId删除物流调价运单")
	@PostMapping(value = "/deleteLogisticsAdjustPriceItemByAdjustItemId")
	public ItemResult<Void> deleteLogisticsAdjustPriceItemByAdjustItemId(@RequestBody LogisticsAdjustPriceItemDTO queryDTO){
		return logisticsAdjustPriceService.deleteLogisticsAdjustPriceItemByAdjustItemId(queryDTO);
	}

	@Operation(summary = "获取最新物流运单调价明细")
	@PostMapping(value = "/getNewestLogisticsAdjustPriceItem")
	public LogisticsAdjustPriceItemDTO getNewestLogisticsAdjustPriceItem(LogisticsAdjustPriceItemDTO queryDTO){
		return logisticsAdjustPriceService.getNewestLogisticsAdjustPriceItem(queryDTO);
	}
}
