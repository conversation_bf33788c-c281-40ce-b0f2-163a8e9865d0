package com.ecommerce.pay.v2.controller;

import com.ecommerce.pay.api.v2.dto.AutoPayChannelRequestDTO;
import com.ecommerce.pay.api.v2.dto.ChannelCardDTO;
import com.ecommerce.pay.api.v2.dto.ChannelCardRequestDTO;
import com.ecommerce.pay.api.v2.dto.ChannelCardResponseDTO;
import com.ecommerce.pay.api.v2.dto.ChannelConfigDTO;
import com.ecommerce.pay.api.v2.dto.CreditMemberChannelDTO;
import com.ecommerce.pay.api.v2.dto.GneteAggregationChannelCloseDTO;
import com.ecommerce.pay.api.v2.dto.GneteAggregationChannelOpenDTO;
import com.ecommerce.pay.api.v2.dto.GneteWithholdGrantDTO;
import com.ecommerce.pay.api.v2.dto.GneteWithholdGrantQueryDTO;
import com.ecommerce.pay.api.v2.dto.MemberChannelCallbackRequestDTO;
import com.ecommerce.pay.api.v2.dto.MemberChannelCallbackResponseDTO;
import com.ecommerce.pay.api.v2.dto.MemberChannelCheckDTO;
import com.ecommerce.pay.api.v2.dto.MemberChannelDTO;
import com.ecommerce.pay.api.v2.dto.MemberChannelResponseDTO;
import com.ecommerce.pay.api.v2.dto.PasswordCallbackRequestDTO;
import com.ecommerce.pay.api.v2.dto.PasswordCallbackResponseDTO;
import com.ecommerce.pay.api.v2.dto.PasswordRequestDTO;
import com.ecommerce.pay.api.v2.dto.PasswordResponseDTO;
import com.ecommerce.pay.api.v2.dto.SmallAmountTransferDTO;
import com.ecommerce.pay.api.v2.dto.gnete.ApplyTicketReqDTO;
import com.ecommerce.pay.api.v2.dto.gnete.ApplyTicketResDTO;
import com.ecommerce.pay.api.v2.dto.gnete.FrontConfigDTO;
import com.ecommerce.pay.api.v2.dto.gnete.RegisterCallBackDTO;
import com.ecommerce.pay.api.v2.dto.query.BalanceQueryDTO;
import com.ecommerce.pay.api.v2.dto.query.ChannelQueryDTO;
import com.ecommerce.pay.api.v2.dto.query.DownloadStatementDTO;
import com.ecommerce.pay.api.v2.enums.ChannelPaymentTypeEnum;
import com.ecommerce.pay.api.v2.enums.ClientType;
import com.ecommerce.pay.v2.service.IMemberChannelService;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * @Created锛�Mon Jan 07 15:50:24 CST 2019
 * <AUTHOR>
 * @Version:2
 * @Description:null
 */
@Slf4j
@Tag(name = "MemberChannelController", description = "会员渠道")
@RestController
@RequestMapping("/memberChannel")
public class MemberChannelController {

    @Autowired
    private IMemberChannelService iMemberChannelService;

    @Operation(summary = "获取渠道配置信息")
    @PostMapping(value = "/getChannelConfig")
    public ChannelConfigDTO getChannelConfig(@Parameter(name = "channelId", description = "渠道id") @RequestParam String channelId,
                                             @Parameter(name = "operator", description = "操作人") @RequestParam String operator) {
        return iMemberChannelService.getChannelConfig(channelId, operator);
    }

    @Operation(summary = "获取渠道配置信息")
    @PostMapping(value = "/getChannelConfigByChannelCode")
    public ChannelConfigDTO getChannelConfigByChannelCode(@Parameter(name = "channelCode", description = "渠道编号") @RequestParam String channelCode) {
        return iMemberChannelService.getChannelConfigByChannelCode(channelCode);
    }

    @Operation(summary = "获取渠道配置信息")
    @PostMapping(value = "/getChannelConfigPayerByIdList")
    public List<ChannelConfigDTO> getChannelConfigPayerByIdList(@Parameter(name = "channelIds", description = "渠道id列表") @RequestBody List<String> channelIds,
                                                                @Parameter(name = "payeeMemberId", description = "收款方会员id") @RequestParam String payeeMemberId) {
        return iMemberChannelService.getChannelConfigPayerByIdList(channelIds, payeeMemberId);
    }

    @Operation(summary = "获取渠道配置信息")
    @PostMapping(value = "/getChannelConfigByChannelQuery")
    public List<ChannelConfigDTO> getChannelConfigByChannelQuery(@Parameter(name = "channelQueryDTO", description = "渠道查询DTO") @RequestBody ChannelQueryDTO channelQueryDTO) {
        return iMemberChannelService.getChannelConfigByChannelQuery(channelQueryDTO);
    }

    @Operation(summary = "获取买家或卖家渠道配置信息")
    @PostMapping(value = "/getCustomerAvailChannels")
    public List<ChannelConfigDTO> getCustomerAvailChannels(@Parameter(name = "customer", description = "客户") @RequestParam String customer) {
        return iMemberChannelService.getCustomerAvailChannels(customer);
    }

    @Operation(summary = "开通支付渠道")
    @PostMapping(value = "/openMemberChannel")
    public MemberChannelResponseDTO openMemberChannel(@Parameter(name = "memberChannelDTO", description = "会员渠道DTO") @RequestBody MemberChannelDTO memberChannelDTO,
                                                      @Parameter(name = "channelPaymentType", description = "渠道支付类型") @RequestParam String channelPaymentType,
                                                      @Parameter(name = "operator", description = "操作人") @RequestParam String operator) {
        return iMemberChannelService.openMemberChannel(memberChannelDTO, channelPaymentType, operator);
    }


    @Operation(summary = "获取会员支持的支付渠道")
    @PostMapping(value = "/getMemberAvailChannels")
    public List<MemberChannelDTO> getMemberAvailChannels(@Parameter(name = "memberId", description = "会员id") @RequestParam String memberId,
                                                         @Parameter(name = "clientType", description = "客户端类型") @RequestParam String clientType) {
        return iMemberChannelService.getMemberAvailChannels(memberId, ClientType.getByCode(clientType));
    }

    @Operation(summary = "获取会员支持的支付渠道，区分付款收款")
    @PostMapping(value = "/getMemberAvailChannelsByCustomer")
    public List<MemberChannelDTO> getMemberAvailChannelsByCustomer(@Parameter(name = "memberId", description = "会员id") @RequestParam String memberId,
                                                                   @Parameter(name = "clientType", description = "客户端类型") @RequestParam String clientType,
                                                                   @Parameter(name = "channelPaymentType", description = "渠道支付类型") @RequestParam String channelPaymentType) {
        return iMemberChannelService.getMemberAvailChannelsByCustomer(memberId, ClientType.getByCode(clientType),
                ChannelPaymentTypeEnum.getByCode(channelPaymentType));
    }

    @Operation(summary = "获取支付方在执行某支付业务时，在某收款方可用的支付渠道")
    @PostMapping(value = "/getMemberAvailChannelsByPayee")
    public List<MemberChannelDTO> getMemberAvailChannelsByPayee(@Parameter(name = "payerId", description = "支付方id") @RequestParam String payerId,
                                                                @Parameter(name = "bizId", description = "业务id") @RequestParam String bizId,
                                                                @Parameter(name = "clientType", description = "客户端类型") @RequestParam String clientType,
                                                                @Parameter(name = "paymentBizCode", description = "支付业务编号") @RequestParam String paymentBizCode,
                                                                @Parameter(name = "isMonthly", description = "是否月结") @RequestParam(required = false, defaultValue = "0") Integer isMonthly,
                                                                @Parameter(name = "operator", description = "操作人") @RequestParam String operator) {
        return iMemberChannelService.getMemberAvailChannelsByPayee(payerId, bizId, ClientType.getByCode(clientType),
                paymentBizCode, isMonthly, operator);
    }

    @Schema(description = "获取支付方在执行某支付业务时，在某收款方可用的支付渠道(可用于订单生成前使用)")
    @PostMapping(value = "/getMemberAvailChannelsByPayee2")
    public List<MemberChannelDTO> getMemberAvailChannelsByPayee2(@RequestBody AutoPayChannelRequestDTO autoPayChannelRequestDTO) {
        return iMemberChannelService.getMemberAvailChannelsByPayee2(autoPayChannelRequestDTO);
    }

    @Operation(summary = "关闭支付渠道时的回调")
    @PostMapping(value = "/closeMemberChannelCallback")
    public MemberChannelCallbackResponseDTO closeMemberChannelCallback(@Parameter(name = "requestDTO", description = "会员渠道回调请求DTO") @RequestBody MemberChannelCallbackRequestDTO requestDTO) {
        return iMemberChannelService.closeMemberChannelCallback(requestDTO);
    }

    @Operation(summary = "获取可用的平台支持的支付渠道")
    @PostMapping(value = "/getPlatformAvailChannels")
    public List<ChannelConfigDTO> getPlatformAvailChannels() {
        return iMemberChannelService.getPlatformAvailChannels();
    }

    @Operation(summary = "获取可用的平台支持的支付渠道(付款)")
    @PostMapping(value = "/getPlatformAvailChannelsByPayer")
    public List<ChannelConfigDTO> getPlatformAvailChannelsByPayer() {
        return iMemberChannelService.getPlatformAvailChannelsByPayer();
    }

    @Operation(summary = "开通支付渠道时的回调")
    @PostMapping(value = "/openMemberChannelCallback")
    public MemberChannelCallbackResponseDTO openMemberChannelCallback(@Parameter(name = "requestDTO", description = "会员渠道回调请求DTO") @RequestBody MemberChannelCallbackRequestDTO requestDTO) {
        return iMemberChannelService.openMemberChannelCallback(requestDTO);
    }

    @Operation(summary = "获取支付渠道信息 仅包含我们平台")
    @PostMapping(value = "/findMemberChannelListByPlatform")
    public List<MemberChannelDTO> findMemberChannelListByPlatform(@Parameter(name = "memberId", description = "会员id") @RequestParam String memberId) {
        return iMemberChannelService.findMemberChannelListByPlatform(memberId);
    }

    @Operation(summary = "获取会员支付渠道信息")
    @PostMapping(value = "/getMemberChannelInfo")
    public MemberChannelDTO getMemberChannelInfo(@Parameter(name = "memberChannelId", description = "会员渠道id") @RequestParam String memberChannelId) {
        return iMemberChannelService.getMemberChannelInfo(memberChannelId);
    }

    @Operation(summary = "关闭支付渠道")
    @PostMapping(value = "/closeMemberChannel")
    public MemberChannelResponseDTO closeMemberChannel(@Parameter(name = "memberChannelDTO", description = "会员渠道DTO") @RequestBody MemberChannelDTO memberChannelDTO,
                                                       @Parameter(name = "channelPaymentType", description = "渠道支付类型") @RequestParam String channelPaymentType,
                                                       @Parameter(name = "operator", description = "操作人") @RequestParam String operator) {
        return iMemberChannelService.closeMemberChannel(memberChannelDTO, channelPaymentType, operator);
    }

    @Operation(summary = "根据mchno获取mchkey")
    @PostMapping(value = "/getMchkeyByMchNo")
    public String getMchkeyByMchNo(@Parameter(name = "mchNo", description = "会员渠道编号") @RequestParam String mchNo) {
        return iMemberChannelService.getMchkeyByMchNo(mchNo);
    }

    @Operation(summary = "获取买家不需要开通就可使用的渠道")
    @PostMapping(value = "/findBuyerAvailChannel")
    public List<ChannelConfigDTO> findBuyerAvailChannel() {
        return iMemberChannelService.findBuyerAvailChannel();
    }

    @Operation(summary = "分页查询开通的渠道列表")
    @PostMapping(value = "/pageMemberChannel")
    public PageInfo<MemberChannelDTO> pageMemberChannel(@Parameter(name = "memberChannelDTO", description = "会员渠道DTO") @RequestBody MemberChannelDTO memberChannelDTO) {
        return iMemberChannelService.pageMemberChannelDTO(memberChannelDTO);
    }

    @Operation(summary = "根据memberChannelId查询绑卡情况")
    @PostMapping(value = "/getChannelCard")
    public List<ChannelCardDTO> getChannelCard(@Parameter(name = "memberChannelId", description = "会员渠道id") @RequestParam String memberChannelId) {
        return iMemberChannelService.getChannelCardByMemberChannelId(memberChannelId);
    }

    @Operation(summary = "根据memberId和channelCode查询绑卡情况")
    @PostMapping(value = "/getChannelCardByMemberIdAndChannelCode")
    public List<ChannelCardDTO> getChannelCardByMemberIdAndChannelCode(@Parameter(name = "memberId", description = "会员id") @RequestParam String memberId,
                                                                       @Parameter(name = "channelCode", description = "渠道编号") @RequestParam String channelCode) {
        return iMemberChannelService.getChannelCardByMemberIdAndChannelCode(memberId, channelCode);
    }

    @Operation(summary = "设置默认银行卡")
    @PostMapping(value = "/updateDefaultChannelCard")
    public void updateDefaultChannelCard(@Parameter(name = "memberChannelId", description = "会员渠道id") @RequestParam String memberChannelId,
                                         @Parameter(name = "channelCardId", description = "渠道银行卡id") @RequestParam String channelCardId,
                                         @Parameter(name = "operatorId", description = "操作人id") @RequestParam String operatorId) {
        iMemberChannelService.updateDefaultChannelCard(memberChannelId, channelCardId, operatorId);
    }

    @Operation(summary = "验证银行卡")
    @PostMapping(value = "/verifyChannelCard")
    public ChannelCardResponseDTO verifyChannelCard(@Parameter(name = "request", description = "渠道银行卡请求DTO") @RequestBody ChannelCardRequestDTO request,
                                                    @Parameter(name = "operator", description = "操作人") @RequestParam String operator) {
        return iMemberChannelService.verifyChannelCard(request, operator);
    }

    @Operation(summary = "设置小额免密")
    @PostMapping(value = "/freePasswordSetting")
    public PasswordResponseDTO freePasswordSetting(@Parameter(name = "memberChannelId", description = "会员渠道id") @RequestParam String memberChannelId,
                                                   @Parameter(name = "freePassword", description = "是否免密") @RequestParam boolean freePassword,
                                                   @Parameter(name = "operator", description = "操作人") @RequestParam String operator) {
        return iMemberChannelService.freePasswordSetting(memberChannelId, freePassword, operator);
    }

    @Operation(summary = "设置启用聚合支付")
    @PostMapping(value = "/aggregationPaySetting")
    public PasswordResponseDTO aggregationPaySetting(@Parameter(name = "memberChannelId", description = "会员渠道id") @RequestParam String memberChannelId,
                                                     @Parameter(name = "support", description = "是否支持") @RequestParam boolean support,
                                                     @Parameter(name = "operator", description = "操作人") @RequestParam String operator) {
        return iMemberChannelService.aggregationPaySetting(memberChannelId, support, operator);
    }

    @Operation(summary = "移除银行卡")
    @PostMapping(value = "/doRemoveChannelCard")
    public ChannelCardResponseDTO doRemoveChannelCard(@Parameter(name = "channelCardId", description = "渠道银行卡id") @RequestParam String channelCardId,
                                                      @Parameter(name = "memberId", description = "会员id") @RequestParam String memberId,
                                                      @Parameter(name = "operator", description = "操作人") @RequestParam String operator) {
        return iMemberChannelService.doRemoveChannelCard(channelCardId, memberId, operator);
    }

    @Operation(summary = "查询银行卡详情")
    @PostMapping(value = "/getChannelCardById")
    public ChannelCardDTO getChannelCardById(@Parameter(name = "channelCardId", description = "渠道银行卡id") @RequestParam String channelCardId) {
        return iMemberChannelService.getChannelCardById(channelCardId);
    }

    @Operation(summary = "从第三方获取定制化信息")
    @PostMapping(value = "/getMemberChannelInfoFromThird")
    public Map<String, Object> getMemberChannelInfoFromThird(@Parameter(name = "memberChannelId", description = "会员渠道id") @RequestParam String memberChannelId) {
        return iMemberChannelService.getMemberChannelInfoFromThird(memberChannelId);
    }

    @Operation(summary = "从第三方获取定制化信息")
    @PostMapping(value = "/getMemberChannelInfoFromThirdByQueryDTO")
    public Map<String, Object> getMemberChannelInfoFromThirdByQueryDTO(@Parameter(name = "balanceQueryDTO", description = "余额查询DTO") @RequestBody BalanceQueryDTO balanceQueryDTO) {
        return iMemberChannelService.getMemberChannelInfoFromThirdByQueryDTO(balanceQueryDTO);
    }


    @Operation(summary = "支付密码回调")
    @PostMapping(value = "/passwordCallback")
    public PasswordCallbackResponseDTO passwordCallback(@Parameter(name = "passwordCallbackRequest", description = "密码回调请求DTO") @RequestBody PasswordCallbackRequestDTO passwordCallbackRequest) {
        return iMemberChannelService.passwordCallback(passwordCallbackRequest);
    }

    @Operation(summary = "添加银行卡")
    @PostMapping(value = "/doAddChannelCard")
    public ChannelCardResponseDTO doAddChannelCard(@Parameter(name = "request", description = "渠道银行卡请求DTO") @RequestBody ChannelCardRequestDTO request,
                                                   @Parameter(name = "operator", description = "操作人") @RequestParam String operator) {
        return iMemberChannelService.doAddChannelCard(request, operator);
    }

    @Operation(summary = "添加对公银行账户(查询小额鉴权转账结果)")
    @PostMapping(value = "/findSmallAmountTransfer")
    public SmallAmountTransferDTO findSmallAmountTransfer(@RequestBody SmallAmountTransferDTO dto) {
        return iMemberChannelService.findSmallAmountTransfer(dto);
    }

    @Operation(summary = "设置支付密码")
    @PostMapping(value = "/setPassword")
    public PasswordResponseDTO setPassword(@Parameter(name = "passwordRequest", description = "密码请求DTO") @RequestBody PasswordRequestDTO passwordRequest) {
        return iMemberChannelService.setPassword(passwordRequest);
    }

    @Operation(summary = "更新授信memberChannel")
    @PostMapping(value = "/updateCreditMemberChannel")
    public void updateCreditMemberChannel(@Parameter(name = "memberChannelDTO", description = "会员渠道DTO") @RequestBody MemberChannelDTO memberChannelDTO,
                                          @Parameter(name = "operator", description = "操作人") @RequestParam String operator) {
        iMemberChannelService.updateCreditMemberChannel(memberChannelDTO, operator);
    }

    @Operation(summary = "批量新增授信渠道")
    @PostMapping(value = "/batchAddCreditMemberChannel")
    @Deprecated(since = "2.1.4-RELEASE")
    public void batchAddCreditMemberChannel(@Parameter(name = "creditMemberChannelDTOS", description = "授信渠道DTO列表") @RequestBody List<CreditMemberChannelDTO> creditMemberChannelDTOS,
                                            @Parameter(name = "sellerId", description = "卖家id") @RequestParam String sellerId,
                                            @Parameter(name = "sellerCode", description = "卖家编号") @RequestParam String sellerCode,
                                            @Parameter(name = "sellerName", description = "卖家名称") @RequestParam String sellerName,
                                            @Parameter(name = "operator", description = "操作人") @RequestParam String operator) {
        iMemberChannelService.batchAddCreditMemberChannel(creditMemberChannelDTOS, sellerId, sellerCode, sellerName, operator);
    }

    @Operation(summary = "获取授信支付卖家下的开通过的买家集合")
    @PostMapping(value = "/findMemberChannelDTOList")
    public List<MemberChannelDTO> findMemberChannelDTOList(@Parameter(name = "payeeMemberId", description = "收款方会员id") @RequestParam String payeeMemberId,
                                                           @Parameter(name = "payerMemberIds", description = "付款方会员id列表") @RequestBody List<String> payerMemberIds) {
        return iMemberChannelService.findMemberChannelDTOList(payeeMemberId, payerMemberIds);
    }

    @Operation(summary = "下载对账单")
    @PostMapping(value = "/downloadStatement")
    public DownloadStatementDTO downloadStatement(@Parameter(name = "memberChannelId", description = "会员渠道id") @RequestParam String memberChannelId,
                                                  @Parameter(name = "paymentType", description = "支付类型") @RequestParam String paymentType,
                                                  @Parameter(name = "targetDate", description = "目标日期") @RequestParam long targetDate) {
        return iMemberChannelService.downloadStatement(memberChannelId, targetDate, paymentType);
    }

    @Operation(summary = "检查该会员是否可以变更实名信息")
    @PostMapping(value = "/checkMemberAllowUnBind")
    public MemberChannelCheckDTO checkMemberAllowUnBind(@Parameter(name = "memberId", description = "会员id") @RequestParam String memberId) {
        return iMemberChannelService.checkMemberAllowUnBind(memberId);
    }

    @Operation(summary = "解绑该会员钱包")
    @PostMapping(value = "/unbindMemberWallet")
    public void unbindMemberWallet(@Parameter(name = "memberId", description = "会员id") @RequestParam String memberId) {
        iMemberChannelService.unbindMemberWallet(memberId);
    }

    @Operation(summary = "解绑该会员钱包")
    @PostMapping(value = "/findByMemberIdAndChannelCode")
    public List<MemberChannelDTO> findByMemberIdAndChannelCode(@Parameter(name = "memberId", description = "会员id") @RequestParam String memberId,
                                                               @Parameter(name = "channelCode", description = "渠道编号") @RequestParam String channelCode) {
        return iMemberChannelService.findByMemberIdAndChannelCode(memberId, channelCode);
    }


    @Operation(summary = "银联支付钱包操作ticket请求")
    @PostMapping(value = "/getTicket")
    public ApplyTicketResDTO getTicket(@RequestBody ApplyTicketReqDTO dto){
        return iMemberChannelService.getTicket(dto);
    }

    @Operation(summary = "开通银联聚合支付渠道")
    @PostMapping(value = "/openGneteAggregationChannel")
    public Boolean openGneteAggregationChannel(@RequestBody GneteAggregationChannelOpenDTO dto){
        return iMemberChannelService.openGneteAggregationChannel(dto);
    }

    @Operation(summary = "关闭银联聚合支付渠道")
    @PostMapping(value = "/closeGneteAggregationChannel")
    public Boolean closeGneteAggregationChannel(@RequestBody GneteAggregationChannelCloseDTO dto){
        return iMemberChannelService.closeGneteAggregationChannel(dto);
    }

    @Operation(summary = "银联支付-收款 - 签约解约")
    @PostMapping(value = "/withholdGrant")
    public GneteWithholdGrantQueryDTO withholdGrant(@RequestBody GneteWithholdGrantDTO dto){
        return iMemberChannelService.withholdGrant(dto);
    }


    @Operation(summary = "银联注册审批状态变化回调")
    @PostMapping(value = "/gneteRegisterCallBack")
    public void gneteRegisterCallBack(@RequestBody RegisterCallBackDTO dto){
        iMemberChannelService.gneteRegisterCallBack(dto);
    }

    @Operation(summary = "获取银联支付密码加密随机因子")
    @PostMapping(value = "/getPlugRandomKey")
    public String getPlugRandomKey(){
        return iMemberChannelService.getPlugRandomKey();
    }

    @Operation(summary = "获取前端加密配置参数")
    @PostMapping(value = "/getConfig")
    public FrontConfigDTO getConfig(){
        return iMemberChannelService.getConfig();
    }

    @Operation(summary = "银联支付添加证书文件")
    @PostMapping(value = "/uploadGnetePrivateCert")
    public Boolean uploadGnetePrivateCert(@RequestParam String memberChannelId,@RequestParam String certPwd,@RequestParam String operator,@RequestParam String cert){
        return iMemberChannelService.uploadGnetePrivateCert(memberChannelId, certPwd,operator,cert);
    }

    @Operation(summary = "json转换测试")
    @PostMapping(value = "/configTest")
    public MemberChannelDTO configTest(MemberChannelDTO dto){
        log.info("configTest MemberChannelDTO: {}",dto);
        return dto;
    }
}
