package com.ecommerce.pay.v2.controller;

import com.ecommerce.pay.api.v2.dto.MemberCreditChannelDTO;
import com.ecommerce.pay.api.v2.dto.MemberCreditContextDTO;
import com.ecommerce.pay.api.v2.dto.MemberCreditDTO;
import com.ecommerce.pay.api.v2.dto.MemberCreditQueryDTO;
import com.ecommerce.pay.api.v2.dto.MemberCreditRecordDTO;
import com.ecommerce.pay.api.v2.dto.MemberCreditRecordQueryDTO;
import com.ecommerce.pay.api.v2.dto.electricInvoiceAccount.PageQuery;
import com.ecommerce.pay.v2.service.IMemberCreditService;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@RestController
@Tag(name = "MemberCreditController", description = "新版买家授信额度管理")
@RequestMapping("/membercredit")
public class MemberCreditController {
    private static Logger logger = LoggerFactory.getLogger(MemberCreditController.class);

    @Autowired
    private IMemberCreditService memberCreditService;

    @Operation(summary = "查询授信记录列表")
    @PostMapping(value = "/list", consumes = "application/json")
    public PageInfo<MemberCreditDTO> list(@RequestBody MemberCreditQueryDTO memberCreditQueryDTO) {
        return memberCreditService.getMemberCreditListByPage(memberCreditQueryDTO);
    }


    @Operation(summary = "删除授信记录")
    @PostMapping(value = "/delete", consumes = "application/json")
    public void delete(@Parameter(name = "memberCreditChannelDTO", description = "授信渠道DTO") @RequestBody MemberCreditChannelDTO memberCreditChannelDTO, @Parameter(name = "operator", description = "操作人") @RequestParam String operator) {
        memberCreditService.deleteMemberCreditInfo(memberCreditChannelDTO, operator);
    }

    @Operation(summary = "批量新增授信额度")
    @PostMapping(value = "/batchadd", consumes = "application/json")
    public void batchAddMemberCredit(@Parameter(name = "memberCreditChannelDTOs", description = "授信渠道DTO列表") @RequestBody List<MemberCreditChannelDTO> memberCreditChannelDTOs, @Parameter(name = "delIds", description = "要删除的ID合集 多个用逗号分开") @RequestParam(required = false) String delIds, @Parameter(name = "sellerId", description = "卖家id") @RequestParam String sellerId, @Parameter(name = "sellerCode", description = "卖家编号") @RequestParam String sellerCode, @Parameter(name = "sellerName", description = "卖家名称") @RequestParam String sellerName, @Parameter(name = "operator", description = "操作人") @RequestParam String operator) {
        memberCreditService.addMemberCreditInfo(memberCreditChannelDTOs, delIds, sellerId, sellerCode, sellerName, operator);
    }

    @Operation(summary = "查询授信资金记录列表")
    @PostMapping(value = "/recordlist", consumes = "application/json")
    public PageInfo<MemberCreditRecordDTO> recordList(@Parameter(name = "pageQuery", description = "买家授信资金记录分页查询对象") @RequestBody PageQuery<MemberCreditRecordQueryDTO> pageQuery) {
        if (pageQuery.getQueryDTO() == null) {
            pageQuery.setQueryDTO(new MemberCreditRecordQueryDTO());
        }
        return memberCreditService.getMemberCreditRecordListByPage(pageQuery);
    }

    @Operation(summary = "授信列表查询时，补全记录的授信额度以及时间")
    @PostMapping(value = "/findMemberCreditContext", consumes = "application/json")
    public List<MemberCreditContextDTO> findMemberCreditContext(@Parameter(name = "pageQuery", description = "买家授信资金记录分页查询对象") @RequestBody List<MemberCreditQueryDTO> list) {
        return memberCreditService.findMemberCreditContext(list);
    }
}
