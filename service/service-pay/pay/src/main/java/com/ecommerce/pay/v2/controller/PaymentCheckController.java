package com.ecommerce.pay.v2.controller;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ecommerce.pay.v2.service.IPaymentCheckService;


@RestController
@Tag(name = "PaymentCheckController", description = "支付检查")
@RequestMapping("/paymentCheck")
public class PaymentCheckController {

    @Autowired
    private IPaymentCheckService paymentCheckService;

    @Operation(summary = "检查第三方回掉")
    @PostMapping(value = "/checkThirdPartyCallback")
    public void checkThirdPartyCallback() {
        paymentCheckService.checkThirdPartyCallback();
    }

    @Operation(summary = "检查支付单超时")
    @PostMapping(value = "/checkPaymentTimeout")
    public void checkPaymentTimeout(@RequestParam(required = false) Boolean force) {
        paymentCheckService.checkPaymentTimeout(force == null ? false : force);
    }

    @Operation(summary = "检查分账单超时")
    @PostMapping(value = "/checkSplitCallBack")
    public void checkSplitCallBack() {
        paymentCheckService.checkSplitCallBack();
    }

    @Operation(summary = "检查退款单超时")
    @PostMapping(value = "/checkRefundCallBack")
    public void checkRefundCallBack() {
        paymentCheckService.checkRefundCallBack();
    }

}
