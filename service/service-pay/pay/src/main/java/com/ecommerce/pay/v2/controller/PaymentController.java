package com.ecommerce.pay.v2.controller;

import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.pay.api.v2.dto.*;
import com.ecommerce.pay.api.v2.dto.bill.PaymentBillDTO;
import com.ecommerce.pay.api.v2.dto.pinganjz.CashWithdrawalDTO;
import com.ecommerce.pay.api.v2.dto.query.OrderPaymentDetailDTO;
import com.ecommerce.pay.api.v2.dto.query.PaymentBillQueryDTO;
import com.ecommerce.pay.v2.service.IPaymentService;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * @Created锛�Mon Jan 07 15:51:06 CST 2019
 * <AUTHOR>
 * @Version:2
 * @Description:null
 */

@RestController
@Tag(name = "PaymentController", description = "支付")
@RequestMapping("/payment")
public class PaymentController {

    @Autowired
    private IPaymentService iPaymentService;

    @Operation(summary = "支付回调")
    @PostMapping(value = "/payCallbackByPlatform")
    public PaymentCallbackResponseDTO payCallbackByPlatform(@Parameter(name = "paymentBillNo", description = "支付单号") @RequestParam String paymentBillNo,
                                                            @Parameter(name = "success", description = "是否成功") @RequestParam Boolean success,
                                                            @Parameter(name = "operatorId", description = "操作人id") @RequestParam String operatorId) {
        return iPaymentService.payCallbackByPlatform(paymentBillNo, success, operatorId);
    }

    @Operation(summary = "订单创建回调")
    @PostMapping(value = "/orderCreateCallback")
    public PaymentCallbackResponseDTO orderCreateCallback(@Parameter(name = "paymentBillNo", description = "支付单号") @RequestParam String paymentBillNo,
                                                          @Parameter(name = "success", description = "是否成功") @RequestParam Boolean success,
                                                          @Parameter(name = "operatorId", description = "操作人id") @RequestParam String operatorId) {
        return iPaymentService.orderCreateCallback(paymentBillNo, success, operatorId);
    }

    @Operation(summary = "取消支付业务，")
    @PostMapping(value = "/doCancelPaymentBiz")
    public void doCancelPaymentBiz(@Parameter(name = "bizId", description = "业务id") @RequestParam String bizId,
                                   @Parameter(name = "operator", description = "操作人") @RequestParam String operator) {
        iPaymentService.doCancelPaymentBiz(bizId, operator);
    }

    @Operation(summary = "向第三方支付系统查询支付单")
    @PostMapping(value = "/getPayBillFromThirdParty")
    public PaymentBillDTO getPayBillFromThirdParty(@Parameter(name = "payBillNo", description = "支付单号") @RequestParam String payBillNo,
                                                   @Parameter(name = "operator", description = "操作人") @RequestParam String operator) {
        return iPaymentService.getPayBillFromThirdParty(payBillNo, operator);
    }

    @Operation(summary = "根据 paymentBillNo 查询 MemberChannelDTO")
    @PostMapping(value = "/findMemberChannelByPaymentBillNo")
    public MemberChannelDTO findMemberChannelByPaymentBillNo(@Parameter(name = "paymentBillNo", description = "支付单号") @RequestParam String paymentBillNo) {
        return iPaymentService.findMemberChannelByPaymentBillNo(paymentBillNo);
    }

    @Operation(summary = "根据数据库配置调用支付接入方的接口，获取支付需求信息")
    @PostMapping(value = "/getPaymentRequirement")
    public PaymentRequirementDTO getPaymentRequirement(@Parameter(name = "bizId", description = "业务id") @RequestParam String bizId,
                                                       @Parameter(name = "paymentBizCode", description = "支付业务编号") @RequestParam String paymentBizCode,
                                                       @Parameter(name = "operator", description = "操作人") @RequestParam String operator) {
        return iPaymentService.getPaymentRequirement(bizId, paymentBizCode, operator);
    }

    @Operation(summary = "预支付，并不实际支付，适用于支付需要二次确认的情况")
    @PostMapping(value = "/prePay")
    public PaymentResponseDTO prePay(@Parameter(name = "bizId", description = "业务id") @RequestParam String bizId,
                                     @Parameter(name = "payerChannelId", description = "付款方渠道id") @RequestParam String payerChannelId,
                                     @Parameter(name = "amount", description = "金额") @RequestParam String amount,
                                     @Parameter(name = "operator", description = "操作人") @RequestParam String operator) {
        return iPaymentService.prePay(bizId, payerChannelId, amount, operator);
    }

    @Operation(summary = "支付")
    @PostMapping(value = "/pay")
    public PaymentResponseDTO pay(@Parameter(name = "paymentRequestDTO", description = "支付请求DTO") @RequestBody PaymentRequestDTO paymentRequestDTO) {
        return iPaymentService.pay(paymentRequestDTO);
    }

    @Operation(summary = "直接支付")
    @PostMapping(value = "/directPayByPlatform")
    public DirectPayResponseDTO directPayByPlatform(@Parameter(name = "directPayRequestDTO", description = "直接支付请求DTO") @RequestBody DirectPayRequestDTO directPayRequestDTO) {
        return iPaymentService.directPayByPlatform(directPayRequestDTO);
    }

    @Operation(summary = "自动支付")
    @PostMapping(value = "/autoPayment")
    public AutoPaymentResponseDTO autoPayment(@Parameter(name = "autoPaymentRequestDTO", description = "自动支付请求DTO") @RequestBody AutoPaymentRequestDTO autoPaymentRequestDTO) {
        return iPaymentService.autoPayment(autoPaymentRequestDTO);
    }

    @Operation(summary = "查询订单的支付详细")
    @PostMapping(value = "/queryPaymentByOrderNo")
    public OrderPaymentDetailDTO queryPaymentByOrderNo(@Parameter(name = "orderNo", description = "订单编号") @RequestParam String orderNo) {
        return iPaymentService.queryPaymentByOrderNo(orderNo);
    }

    @Operation(summary = "冻结")
    @PostMapping(value = "/freeze")
    public PaymentResponseDTO freeze(@Parameter(name = "orderNo", description = "订单编号") @RequestParam String orderNo,
                                     @Parameter(name = "amount", description = "金额") @RequestParam String amount,
                                     @Parameter(name = "returnUrl", description = "返回url") @RequestParam String returnUrl,
                                     @Parameter(name = "operator", description = "操作人") @RequestParam String operator) {
        return iPaymentService.freeze(orderNo, amount, returnUrl, operator);
    }

    @Operation(summary = "解冻")
    @PostMapping(value = "/unfreeze")
    public PaymentResponseDTO unfreeze(@Parameter(name = "orderNo", description = "订单编号") @RequestParam String orderNo,
                                       @Parameter(name = "returnUrl", description = "返回url") @RequestParam String returnUrl,
                                       @Parameter(name = "unfreezeAmount", description = "解冻金额") @RequestParam String unfreezeAmount,
                                       @Parameter(name = "operator", description = "操作人") @RequestParam String operator) {
        return iPaymentService.unfreeze(orderNo, returnUrl, unfreezeAmount, operator);
    }

    @Operation(summary = "支付回调")
    @PostMapping(value = "/payCallback")
    public PaymentCallbackResponseDTO payCallback(@Parameter(name = "callbackMsg", description = "支付回调请求DTO") @RequestBody PaymentCallbackRequestDTO callbackMsg) {
        return iPaymentService.payCallback(callbackMsg);
    }

    @Operation(summary = "退款回调")
    @PostMapping(value = "/refundCallback")
    public PaymentCallbackResponseDTO refundCallback(@Parameter(name = "callbackMsg", description = "支付回调请求DTO") @RequestBody PaymentCallbackRequestDTO callbackMsg) {
        return iPaymentService.refundCallback(callbackMsg);
    }

    @Operation(summary = "确认支付，适用于支付二次确认的情况")
    @PostMapping(value = "/confirmPay")
    public PaymentResponseDTO confirmPay(@Parameter(name = "confirmPayRequestDTO", description = "确认支付请求DTO") @RequestBody ConfirmPayRequestDTO confirmPayRequestDTO) {
        return iPaymentService.confirmPay(confirmPayRequestDTO);
    }

    @Operation(summary = "分账结束通知(适用于银联担保支付订单分账完结后调用)")
    @PostMapping(value = "/splitCompleteNotify")
    public SplitPayCompleteResponseDTO splitCompleteNotify(@RequestBody SplitPayCompleteRequestDTO dto) {
        return iPaymentService.splitCompleteNotify(dto);
    }

    @Operation(summary = "确认支付异步回调(callBackRequestCode传结算单号（settOrderNo）)")
    @PostMapping(value = "/confirmPayCallback")
    public PaymentCallbackResponseDTO confirmPayCallback(@RequestBody PaymentCallbackRequestDTO callbackMsg) {
        return iPaymentService.confirmPayCallback(callbackMsg);
    }

    @Operation(summary = "退款")
    @PostMapping(value = "/refund")
    public PaymentResponseDTO refund(@Parameter(name = "refundRequestDTO", description = "退款请求DTO") @RequestBody RefundRequestDTO refundRequestDTO) {
        return iPaymentService.refund(refundRequestDTO);
    }

    @Operation(summary = "分页查询支付单")
    @PostMapping(value = "/pagePaymentBill")
    public PageInfo<PaymentBillDTO> pagePaymentBill(@Parameter(name = "paymentBillDTO", description = "支付单查询DTO") @RequestBody PaymentBillQueryDTO paymentBillDTO) {
        return iPaymentService.pagePaymentBill(paymentBillDTO);
    }

    @Operation(summary = "获取支付单信息")
    @PostMapping(value = "/getPayBill")
    public PaymentBillDTO getPayBill(@Parameter(name = "payBillNo", description = "支付单编号") @RequestParam String payBillNo) {
        return iPaymentService.getPayBill(payBillNo);
    }

    @Operation(summary = "定时处理支付单，可能向第三方发起主动查询，处理支付单超时情况")
    @PostMapping(value = "/checkPaymentBill")
    public void checkPaymentBill() {
        iPaymentService.checkPaymentBill();
    }

    @Deprecated(since = "2.1.4-RELEASE")
    @Operation(summary = "提现接口")
    @PostMapping(value = "/cashWithdrawal")
    public PasswordResponseDTO cashWithdrawal(@Parameter(name = "cashWithdrawalDTO", description = "提现DTO") @RequestBody CashWithdrawalDTO cashWithdrawalDTO) {
        throw new BizException(BasicCode.UNDEFINED_ERROR, "请改用IWalletAccountService服务对应的方法");
    }

    @Operation(summary = "通知业务方支付结果")
    @PostMapping(value = "/notifyPaymentBusiness")
    public PaymentBillDTO notifyPaymentBusiness(@Parameter(name = "paymentBillNo", description = "支付单编号") @RequestParam String paymentBillNo,
                                                @Parameter(name = "operatorId", description = "操作人id") @RequestParam String operatorId) {
        return iPaymentService.notifyPaymentBusiness(paymentBillNo, operatorId);
    }

    @Operation(summary = "获取所有支付类型")
    @PostMapping(value = "/getPayType")
    public List<KeyValueDTO> getPayType() {
        return iPaymentService.getPayType();
    }

    @Operation(summary = "渠道是否支持多次支付")
    @PostMapping(value = "/checkChannelSupportMultiplePay")
    public boolean checkChannelSupportMultiplePay(@Parameter(name = "channelCode", description = "渠道编号") @RequestParam String channelCode) {
        return iPaymentService.checkChannelSupportMultiplePay(channelCode);
    }

    @Operation(summary = "发送交易验证短信验证码")
    @PostMapping(value = "/sendVerificationCode")
    public SendVerificationCodeResponseDTO sendVerificationCode(@RequestBody SendVerificationCodeRequestDTO requestDTO) {
        return iPaymentService.sendVerificationCode(requestDTO);
    }

    @Operation(summary = "回填验证码校验")
    @PostMapping(value = "/verificationCode")
    public Boolean verificationCode(@RequestBody SendVerificationCodeRequestDTO requestDTO) {
        return iPaymentService.verificationCode(requestDTO);
    }
}
