package com.ecommerce.pay.v2.controller;

import com.ecommerce.pay.api.v2.dto.PaymentExceptionDTO;
import com.ecommerce.pay.api.v2.dto.electricInvoiceAccount.PageQuery;
import com.ecommerce.pay.v2.service.IPaymentExceptionService;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR>
 * @created 14:07 24/09/2019
 * @description TODO
 */
@RestController
@Tag(name = "PaymentExceptionController", description = "支付异常")
@RequestMapping("/paymentException")
public class PaymentExceptionController {

    @Autowired
    private IPaymentExceptionService paymentExceptionService;

    @Operation(summary = "重试")
    @PostMapping(value = "/redo")
    public void redo(@Parameter(name = "id", description = "id") @RequestParam String id,
                     @Parameter(name = "operatorId", description = "操作人id") @RequestParam String operatorId) {
        paymentExceptionService.redo(id, operatorId);
    }

    @Operation(summary = "更新")
    @PostMapping(value = "/update")
    public void update(@Parameter(name = "dto", description = "支付异常DTO") @RequestBody PaymentExceptionDTO dto,
                       @Parameter(name = "operatorId", description = "操作人id") @RequestParam String operatorId) {
        paymentExceptionService.update(dto, operatorId);
    }

    @Operation(summary = "更新")
    @PostMapping(value = "/findById")
    public PaymentExceptionDTO findById(@Parameter(name = "id", description = "id") @RequestParam String id) {
        return paymentExceptionService.findById(id);
    }

    @Operation(summary = "更新")
    @PostMapping(value = "/pageByObjectId")
    public PageInfo<PaymentExceptionDTO> pageByObjectId(@Parameter(name = "query", description = "支付异常DTO分页查询对象") @RequestBody PageQuery<PaymentExceptionDTO> query) {
        return paymentExceptionService.pageByObjectId(query);
    }

}
