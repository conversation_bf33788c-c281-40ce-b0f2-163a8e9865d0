package com.ecommerce.pay.v2.controller;

import com.alibaba.fastjson.JSON;
import com.ecommerce.pay.api.v2.dto.MemberChannelDTO;
import com.ecommerce.pay.v2.service.IPingAnService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;


@RestController
@Tag(name = "PingAnV2", description = "平安见证宝相关接口")
@RequestMapping("/v2/pingan")
public class PingAnV2Controller
{
    @Autowired
    private IPingAnService pingAnService;

    @Operation(summary = "会员补录法人信息-下发短信验证码[6242]")
    @PostMapping("/clearingCorpInfoSendMsgCode")
    public String clearingCorpInfoSendMsgCode(@Parameter(name = "memberId", description = "会员id", required = true) @RequestParam String memberId, @Parameter(name = "legalName", description = "法人姓名", required = true) @RequestParam String legalName, @Parameter(name = "legalCertificateCode", description = "法人身份证号码", required = true) @RequestParam String legalCertificateCode, @Parameter(name = "clickTime", description = "操作点击时间 20201222171623") @RequestParam(required = false) String clickTime, @Parameter(name = "remoteIp", description = "IP地址 *************") @RequestParam(required = false) String remoteIp, @Parameter(name = "macAddress", description = "MAC地址 5E:E3:90:4R:E5:33") @RequestParam(required = false)  String macAddress, @Parameter(name = "channel", description = "签约渠道 1：app、2：平台H5网页、3：公众号、4：小程序") @RequestParam(required = false) String channel)
    {
        Map<String, String> rest = pingAnService.clearingCorpInfoSendMsgCode(memberId, legalName, legalCertificateCode, clickTime, remoteIp, macAddress, channel);
        return JSON.toJSONString(rest);
    }

    @Operation(summary = "会员补录法人信息-回填短信验证码[6243]")
    @PostMapping("/clearingCorpInfoCheckMsgCode")
    public String clearingCorpInfoCheckMsgCode(@Parameter(name = "memberId", description = "会员id", required = true) @RequestParam String memberId, @Parameter(name = "checkCode", description = "短信验证码", required = true) @RequestParam String checkCode)
    {
        Map<String, String> rest = pingAnService.clearingCorpInfoCheckMsgCode(memberId, checkCode);
        return JSON.toJSONString(rest);
    }

    @Operation(summary = "登记行为记录信息[6244]")
    @PostMapping("/registerBehaviorRecordInfo")
    public String registerBehaviorRecordInfo(@Parameter(name = "memberId", description = "会员id", required = true) @RequestParam String memberId, @Parameter(name = "functionFlag", description = "功能标志 1-登记行为记录信息  2-查询补录信息") @RequestParam String functionFlag, @Parameter(name = "clickTime", description = "操作点击时间 20201222171623") @RequestParam String clickTime, @Parameter(name = "remoteIp", description = "IP地址 *************") @RequestParam String remoteIp, @Parameter(name = "macAddress", description = "MAC地址 5E:E3:90:4R:E5:33") @RequestParam String macAddress, @Parameter(name = "channel", description = "签约渠道 1：app、2：平台H5网页、3：公众号、4：小程序") @RequestParam String channel)
    {
        Map<String, Object> rest = pingAnService.registerBehaviorRecordInfo(memberId, functionFlag, clickTime, remoteIp, macAddress, channel);
        return JSON.toJSONString(rest);
    }

    @Operation(summary = "获取卖家见证宝账户信息")
    @GetMapping("/sellerChannelInfo")
    public MemberChannelDTO sellerChannelInfo(@Parameter(name = "memberId", description = "会员id", required = true) @RequestParam String memberId)
    {
        return pingAnService.getSellerChannelInfo(memberId);
    }
}
