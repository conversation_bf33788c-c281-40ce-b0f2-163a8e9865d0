package com.ecommerce.pay.v2.controller;

import com.ecommerce.pay.api.v2.dto.*;
import com.ecommerce.pay.api.v2.dto.pinganjz.CashWithdrawalDTO;
import com.ecommerce.pay.v2.service.IWalletAccountService;
import com.ecommerce.common.exception.BizException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR>
 * @created 10:41 04/09/2019
 * @description TODO
 */
@RestController
@Tag(name = "WalletAccountController", description = "钱包账户")
@RequestMapping("/walletAccount")
public class WalletAccountController {

    @Autowired
    private IWalletAccountService walletAccountService;

    /**
     * 根据memberId查询
     *
     * @param memberId
     * @return
     */
    @Operation(summary = "根据memberId查询")
    @PostMapping(value = "/findByMemberId")
    public WalletAccountDTO findByMemberId(@Parameter(name = "memberId", description = "会员id") String memberId,
                                           @Parameter(name = "walletType", description = "钱包类型") String walletType) {
        return walletAccountService.findByMemberId(memberId, walletType);
    }

    /**
     * @param rechargeRequestDTO 充值请求实体
     * @return RechargeResponseDTO 充值响应实体
     * @Title: recharge
     * 充值
     */
    @Operation(summary = "充值")
    @PostMapping(value = "/recharge")
    public RechargeResponseDTO recharge(@Parameter(name = "rechargeRequestDTO", description = "充值请求DTO") @RequestBody RechargeRequestDTO rechargeRequestDTO) {
        return walletAccountService.recharge(rechargeRequestDTO);
    }

    /**
     * @param requestDTO 充值回调请求实体
     * @return RechargeCallbackResponseDTO 充值回调响应实体
     * @Title: rechargeCallback
     * 充值回调
     */
    @Operation(summary = "充值回调")
    @PostMapping(value = "/rechargeCallback")
    public RechargeCallbackResponseDTO rechargeCallback(@Parameter(name = "requestDTO", description = "充值回调请求DTO") @RequestBody RechargeCallbackRequestDTO requestDTO) {
        return walletAccountService.rechargeCallback(requestDTO);
    }

    /**
     * 提现接口
     *
     * @param cashWithdrawalDTO 提现请求实体
     * @throws BizException 如果提现失败将抛出异常
     */
    @Operation(summary = "提现接口")
    @PostMapping(value = "/cashWithdrawal")
    public PasswordResponseDTO cashWithdrawal(@Parameter(name = "cashWithdrawalDTO", description = "提现DTO") @RequestBody CashWithdrawalDTO cashWithdrawalDTO) {
        return walletAccountService.cashWithdrawal(cashWithdrawalDTO);
    }

    /**
     *
     */
    @Operation(summary = "定时对账")
    @GetMapping(value = "/checkBillBalance")
    public String checkBillBalance() {
        walletAccountService.checkBillBalance();
        return "ok";
    }
}
