package com.ecommerce.pay.v2.controller.channel;

import com.ecommerce.open.api.dto.pinganjz.AccountRegulationRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.AccountRegulationResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.ApplicationTextMsgDynamicCodeResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.ApplyForChangeOfCellPhoneNumRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.ApplyForChangeOfCellPhoneNumResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.BackfillDynamicPasswordRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.BackfillDynamicPasswordResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.MbrPropertyReviseRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.MbrPropertyReviseResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.PlatformAccountSupplyResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.aggregate.PayRefundRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.aggregate.PayRefundResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.aggregate.ReconciliationBillDTO;
import com.ecommerce.open.api.dto.pinganjz.query.BalanceQueryRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.query.BalanceQueryResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.query.BankClearQueryResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.query.BankCostDsDealResultQueryResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.query.BankTransactionDetailsQueryResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.query.BankWithDrawCashBackQueryResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.query.BankWithdrawCashDetailsQueryResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.query.ChargeDetailQueryResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.query.CommonTransferRechargeQueryResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.query.CustAcctIdHistoryBalanceQueryRequestDTO;
import com.ecommerce.open.api.dto.pinganjz.query.CustAcctIdHistoryBalanceQueryResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.query.MemberAccountQueryResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.query.MemberBindQueryItemDTO;
import com.ecommerce.open.api.dto.pinganjz.query.MemberBindQueryResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.query.PlatFormBalanceQueryResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.query.ReconciliationDocumentQueryResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.query.SingleTransactionStatusQueryResponseDTO;
import com.ecommerce.open.api.dto.pinganjz.query.SmallAmountTransferQueryResponseDTO;
import com.ecommerce.pay.api.v2.dto.PaymentResponseDTO;
import com.ecommerce.pay.v2.channel.adapter.pinganjz.PingAnJZAdapter;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * @created 11:16 14/02/2019
 * @description TODO
 */
@RestController
@Tag(name = "PingAnController", description = "平安")
@RequestMapping("/paymentChannel/pinganjz")
public class PingAnController {

    @Autowired
    private PingAnJZAdapter pingAnJZAdapter;

    @Operation(summary = "查询对账文件信息 6103 日期 yyyyMMdd")
    @PostMapping(value = "/reconciliationDocumentQuery")
    public ReconciliationDocumentQueryResponseDTO reconciliationDocumentQuery(@Parameter(name = "fileType", description = "文件类型") @RequestParam String fileType,
                                                                              @Parameter(name = "fileDate", description = "文件日期") @RequestParam String fileDate) {
        return pingAnJZAdapter.reconciliationDocumentQuery(fileType, fileDate);
    }

    @Operation(summary = "下载文件")
    @PostMapping(value = "/fileDownload")
    public String fileDownload(@Parameter(name = "filePath", description = "文件路径") @RequestParam String filePath,
                               @Parameter(name = "fileName", description = "文件名称") @RequestParam String fileName,
                               @Parameter(name = "drawCode", description = "drawCode") @RequestParam String drawCode,
                               @Parameter(name = "randomPassword", description = "随机码") @RequestParam String randomPassword) {
        return pingAnJZAdapter.fileDownload(filePath, fileName, drawCode, randomPassword);
    }

    @Operation(summary = "获取清算记录")
    @PostMapping(value = "/findBankClear")
    public BankClearQueryResponseDTO findBankClear(@Parameter(name = "flag", description = "标记") @RequestParam String flag,
                                                   @Parameter(name = "startDate", description = "开始日期") @RequestParam String startDate,
                                                   @Parameter(name = "endDate", description = "结束日期") @RequestParam String endDate,
                                                   @Parameter(name = "pageNum", description = "当前页码") @RequestParam String pageNum) {
        return pingAnJZAdapter.findBankClear(flag, startDate, endDate, pageNum);
    }

    @Operation(summary = "查询银行费用扣收结果 6109 flag 1:全部，2：指定时间段")
    @PostMapping(value = "/findBankCostDsDealResult")
    public BankCostDsDealResultQueryResponseDTO findBankCostDsDealResult(@Parameter(name = "flag", description = "标记") String flag,
                                                                         @Parameter(name = "startDate", description = "开始日期") String startDate,
                                                                         @Parameter(name = "endDate", description = "结束日期") String endDate,
                                                                         @Parameter(name = "pageNum", description = "当前页码") String pageNum) {
        return pingAnJZAdapter.findBankCostDsDealResult(flag, startDate, endDate, pageNum);
    }

    @Operation(summary = "查询子帐号历史余额及待转可提现状态信息 6114")
    @PostMapping(value = "/findCustAcctIdHistoryBalance")
    public CustAcctIdHistoryBalanceQueryResponseDTO findCustAcctIdHistoryBalance(@Parameter(name = "requestDTO", description = "子帐号历史余额查询请求DTO") @RequestBody CustAcctIdHistoryBalanceQueryRequestDTO requestDTO) {
        return pingAnJZAdapter.findCustAcctIdHistoryBalance(requestDTO);
    }

    @Operation(summary = "商户对账单")
    @PostMapping(value = "/searchAggregationBill")
    public List<ReconciliationBillDTO> searchAggregationBill(@Parameter(name = "day", description = "day") String day,
                                                             @Parameter(name = "pmtTag", description = "pmtTag") String pmtTag) {
        return pingAnJZAdapter.searchAggregationBill(day, pmtTag);
    }

    @Operation(summary = "申请修改手机号码 6083")
    @PostMapping(value = "/applyForChangeOfCellPhoneNum")
    public ApplyForChangeOfCellPhoneNumResponseDTO applyForChangeOfCellPhoneNum(@Parameter(name = "requestDTO", description = "申请修改手机号码请求DTO") @RequestBody ApplyForChangeOfCellPhoneNumRequestDTO requestDTO) {
        return pingAnJZAdapter.applyForChangeOfCellPhoneNum(requestDTO);
    }

    @Operation(summary = "回填动态码-修改手机 6084")
    @PostMapping(value = "/backfillDynamicPassword")
    public BackfillDynamicPasswordResponseDTO backfillDynamicPassword(@Parameter(name = "requestDTO", description = "回填动态码请求DTO") @RequestBody BackfillDynamicPasswordRequestDTO requestDTO) {
        return pingAnJZAdapter.backfillDynamicPassword(requestDTO);
    }

    @Operation(summary = "查询明细单验证码 6142")
    @PostMapping(value = "/findDetailVerifiedCode")
    public PaymentResponseDTO findDetailVerifiedCode(@Parameter(name = "paymentBillNo", description = "支付单号") String paymentBillNo) {
        return pingAnJZAdapter.findDetailVerifiedCode(paymentBillNo);
    }

    @Operation(summary = "查询银行提现退单信息 6048")
    @PostMapping(value = "/findBankWithdrawCashBack")
    public BankWithDrawCashBackQueryResponseDTO findBankWithdrawCashBack(@Parameter(name = "startDate", description = "开始日期") String startDate,
                                                                         @Parameter(name = "endDate", description = "结束日期") String endDate) {
        return pingAnJZAdapter.findBankWithdrawCashBack(startDate, endDate);
    }

    @Operation(summary = "调账-见证收单 6145")
    @PostMapping(value = "/accountRegulation")
    public AccountRegulationResponseDTO accountRegulation(@Parameter(name = "requestDTO", description = "见证收单请求DTO") @RequestBody AccountRegulationRequestDTO requestDTO) {
        return pingAnJZAdapter.accountRegulation(requestDTO);
    }

    @Operation(summary = "查询普通转账充值明细 6050")
    @PostMapping(value = "/findCommonTransferRecharge")
    public CommonTransferRechargeQueryResponseDTO findCommonTransferRecharge(@Parameter(name = "flag", description = "标记") String flag,
                                                                             @Parameter(name = "startDate", description = "开始") String startDate,
                                                                             @Parameter(name = "endDate", description = "结束日期") String endDate,
                                                                             @Parameter(name = "pageNum", description = "当前页码") String pageNum) {
        return pingAnJZAdapter.findCommonTransferRecharge(flag, startDate, endDate, pageNum);
    }

    @Operation(summary = "查询充值记录 6146")
    @PostMapping(value = "/findChargeDetail")
    public ChargeDetailQueryResponseDTO findChargeDetail(@Parameter(name = "orderId", description = "订单id") String orderId) {
        return pingAnJZAdapter.findChargeDetail(orderId);
    }

    @Operation(summary = "查询单笔交易详细 6110")
    @PostMapping(value = "/findSinglePayment")
    public SingleTransactionStatusQueryResponseDTO findSinglePayment(@Parameter(name = "orderId", description = "订单id") String orderId,
                                                                     @Parameter(name = "searchType", description = "搜索类型") String searchType) {
        return pingAnJZAdapter.findSinglePayment(orderId, searchType);
    }

    @Operation(summary = "平台补帐 6147")
    @PostMapping(value = "/platformAccountSupply")
    public PlatformAccountSupplyResponseDTO platformAccountSupply(@Parameter(name = "orderId", description = "订单id") String orderId,
                                                                  @Parameter(name = "type", description = "类型") String type,
                                                                  @Parameter(name = "amt", description = "补帐金额") String amt) {
        return pingAnJZAdapter.platformAccountSupply(orderId, type, amt);
    }

    @Operation(summary = "查询时间段的会员成功交易 6072")
    @PostMapping(value = "/findBankTransactionDetails")
    public BankTransactionDetailsQueryResponseDTO findBankTransactionDetails(@Parameter(name = "today", description = "是否当日") boolean today,
                                                                             @Parameter(name = "subAcctNo", description = "子账号编号") String subAcctNo,
                                                                             @Parameter(name = "queryFlag", description = "查询标记") String queryFlag,
                                                                             @Parameter(name = "pageNum", description = "当前页码") String pageNum,
                                                                             @Parameter(name = "startDate", description = "开始日期") String startDate,
                                                                             @Parameter(name = "endDate", description = "结束日期") String endDate) {
        return pingAnJZAdapter.findBankTransactionDetails(today, subAcctNo, queryFlag, pageNum, startDate, endDate);
    }

    @Operation(summary = "查询银行时间段内清分提现明细 6073")
    @PostMapping(value = "/findBankWithdrawCashDetails")
    public BankWithdrawCashDetailsQueryResponseDTO findBankWithdrawCashDetails(@Parameter(name = "flag", description = "标记") String flag,
                                                                               @Parameter(name = "subAcctNo", description = "子账号编号") String subAcctNo,
                                                                               @Parameter(name = "queryFlag", description = "查询标记") String queryFlag,
                                                                               @Parameter(name = "pageNum", description = "当前页码") String pageNum,
                                                                               @Parameter(name = "startDate", description = "开始日期") String startDate,
                                                                               @Parameter(name = "endDate", description = "结束日期") String endDate) {
        return pingAnJZAdapter.findBankWithdrawCashDetails(flag, subAcctNo, queryFlag, pageNum, startDate, endDate);
    }

    @Operation(summary = "查询资金汇总账户余额")
    @PostMapping(value = "/findPlatformBalance")
    public PlatFormBalanceQueryResponseDTO findPlatformBalance() {
        return pingAnJZAdapter.findPlatformBalance();
    }

    @Operation(summary = "修改会员属性-普通商户子账户 6162")
    @PostMapping(value = "/mbrPropertyRevise")
    public MbrPropertyReviseResponseDTO mbrPropertyRevise(@Parameter(name = "requestDTO", description = "会员属性修改请求DTO") @RequestBody MbrPropertyReviseRequestDTO requestDTO) {
        return pingAnJZAdapter.mbrPropertyRevise(requestDTO);
    }

    @Operation(summary = "查询银行子账户余额 6010")
    @PostMapping(value = "/findBalance")
    public BalanceQueryResponseDTO findBalance(@Parameter(name = "requestDTO", description = "余额查询请求DTO") @RequestBody BalanceQueryRequestDTO requestDTO) {
        return pingAnJZAdapter.findBalance(requestDTO);
    }

    @Operation(summary = "根据会员代码查询会员子账号")
    @PostMapping(value = "/findMemberAccount")
    public MemberAccountQueryResponseDTO findMemberAccount(@Parameter(name = "memberChannelCode", description = "会员渠道编号") String memberChannelCode) {
        return pingAnJZAdapter.findMemberAccount(memberChannelCode);
    }

    @Operation(summary = "发送短信验证码")
    @PostMapping(value = "/applicationTextMsgDynamicCode")
    public ApplicationTextMsgDynamicCodeResponseDTO applicationTextMsgDynamicCode(
            @Parameter(name = "tranNetMemberCode", description = "交易网会员代码 memberChannelCode") String tranNetMemberCode,
            @Parameter(name = "subAcctNo", description = "见证子账户的账号") String subAcctNo,
            @Parameter(name = "tranType", description = "交易类型1：提现 2：支付 3：批次号支付 4：批次号作废") String tranType,
                    @Parameter(name = "tranAmt", description = "交易金额") String tranAmt) {
        return pingAnJZAdapter.applicationTextMsgDynamicCode(tranNetMemberCode,subAcctNo,tranType,tranAmt);
    }

    @Operation(summary = "会员绑定信息查询")
    @PostMapping(value = "/findMemberBind")
    public MemberBindQueryResponseDTO findMemberBind(@Parameter(name = "queryFlag", description = "查询标记") String queryFlag,
                                                     @Parameter(name = "subAcctNo", description = "子账号编号") String subAcctNo) {
        return pingAnJZAdapter.findMemberBind(queryFlag, subAcctNo);
    }

    @Operation(summary = "单个会员绑定信息查询")
    @PostMapping(value = "/findMemberBindSingle")
    public MemberBindQueryItemDTO findMemberBindSingle(@Parameter(name = "subAcctNo", description = "子账号编号") String subAcctNo) {
        return pingAnJZAdapter.findMemberBindSingle(subAcctNo);
    }

    @Operation(summary = "查询小额鉴权转账结果")
    @PostMapping(value = "/findSmallAmountTransfer")
    public SmallAmountTransferQueryResponseDTO findSmallAmountTransfer(@Parameter(name = "cnsmrSeqNo", description = "序列号") String cnsmrSeqNo,
                                                                       @Parameter(name = "date", description = "查询日期") long date) {
        return pingAnJZAdapter.findSmallAmountTransfer(cnsmrSeqNo, new Date(date));
    }

    @Operation(summary = "聚合支付 已分账退款")
    @PostMapping(value = "/aggregationRefund")
    public PayRefundResponseDTO aggregationRefund(@Parameter(name = "requestDTO", description = "支付退款请求DTO") @RequestBody PayRefundRequestDTO requestDTO) {
        return pingAnJZAdapter.aggregationRefund(requestDTO);
    }

    @Operation(summary = "聚合支付 已分账退款")
    @PostMapping(value = "/aggregationRefundByPaymentBillNo")
    public PayRefundResponseDTO aggregationRefundByPaymentBillNo(@Parameter(name = "paymentBillNo", description = "支付单编号") String paymentBillNo) {
        return pingAnJZAdapter.aggregationRefundByPaymentBillNo(paymentBillNo);
    }

    @Operation(summary = "聚合支付分账失败后重试")
    @PostMapping(value = "/retryAggregationConfirmPay")
    public PaymentResponseDTO retryAggregationConfirmPay(@Parameter(name = "detailCode", description = "详情编号") String detailCode) {
        return pingAnJZAdapter.retryAggregationConfirmPay(detailCode);
    }

    @Operation(summary = "test")
    @PostMapping(value = "/test")
    public Object test(String orderNo) {
        return pingAnJZAdapter.findAggregationSplitList(orderNo);
    }

    @Operation(summary = "提现结果检查")
    @PostMapping(value = "/cashWithdrawalResultCHeck")
    public void cashWithdrawalResultCHeck(@Parameter(name = "payerMemberId", description = "付款方会员id") @RequestParam(required = false) String payerMemberId) {
        pingAnJZAdapter.cashWithdrawalResultCheck(null);
    }

}
