package com.ecommerce.pay.v2.dao.bean;

import lombok.Data;

import java.math.BigDecimal;

/**
 *  InvoiceItemBean
 *
 * <AUTHOR>
 */
@Data
public class InvoiceItemBean {
    //"发票属性 0:正常 1:折扣行 2:被折扣行"
    private Integer invoiceNature;
    //"金额"
    private BigDecimal amount;
    //"商品名称"
    private String itemName;
    //"商品数量"
    private BigDecimal itemNum;
    //"商品单位"
    private String itemUnit;
    //"商品单价"
    private String itemPrice;
    //"商品型号"
    private String itemMode;
    //"税控分类编码 不足19位后面补零"
    private String itemTaxCode;
    //"税额"
    private BigDecimal tax;
    //"税率"
    private BigDecimal taxRate;
    //"1:含税  0:不含税"
    private String taxIncluded;
    //"税率标识 空：非零税率 1:免税 2:不征税 3:普通零税率"
    private String taxFlag;
    //"是否享受优惠政策 0:否 1:是"
    private String isPreferential;
    //"优惠内容"
    private String preferentialContent;

}
