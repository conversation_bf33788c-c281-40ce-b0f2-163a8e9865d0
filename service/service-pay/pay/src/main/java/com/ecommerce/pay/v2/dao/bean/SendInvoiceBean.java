package com.ecommerce.pay.v2.dao.bean;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 *  SendInvoiceBean
 *
 * <AUTHOR>
 */
@Data
public class SendInvoiceBean {
    //申请Id
    private String applyId;
    //发票Id
    private String infoId;
    //卖家ID
    private String sellerId;
    //状态
    private String status;
    //"买家地址"
    private String buyerAddress;
    //"买家开户行名称"
    private String buyerBankName;
    //"买家开户行帐号"
    private String buyerBankNum;
    //"买家手机号码"
    private String buyerMobile;
    //"买家邮箱"
    private String buyerEmail;
    //"买家名称"
    private String buyerName;
    //"买家省份"
    private String buyerProvince;
    //"买家税号"
    private String buyerTaxNo;
    //"买家电话"
    private String buyerTelephone;
    //"买家类型"
    private String buyerType;
    //发票抬头
    private String invoiceTitle;
    //"分机号"
    private String machineNo;
    //"总不含税金额"
    private String noTaxAmount = "0";
    //"开票总额"
    private BigDecimal totalTaxAmount;
    //"总税额"
    private BigDecimal totalAmount;
    //"开票人"
    private String drawer;
    //"收款人"
    private String payee;
    //"复核人"
    private String reviewer;
    //"备注"
    private String remarks;
    //"卖家开户行地址及账号"
    private String sellerBankAccount;
    //"开票明细列表"
    private List<InvoiceItemBean> invoiceItemList;
}
