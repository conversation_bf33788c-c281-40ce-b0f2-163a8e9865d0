package com.ecommerce.pay.v2.dao.vo;

import jakarta.persistence.Column;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Table(name = "pa_adjust_price")
public class AdjustPrice implements Serializable {
    /**
     * ID
     */
    @Id
    private String id;

    /**
     * 回调编号
     */
    @Column(name = "adjust_no")
    private String adjustNo;

    /**
     * 名称
     */
    @Column(name = "adjust_name")
    private String adjustName;

    /**
     * 会员ID
     */
    @Column(name = "member_id")
    private String memberId;

    /**
     * 会员名称
     */
    @Column(name = "member_name")
    private String memberName;

    /**
     * 销售区域ID
     */
    @Column(name = "sale_region_id")
    private String saleRegionId;

    /**
     * 销售区域名称
     */
    @Column(name = "sale_region_name")
    private String saleRegionName;

    /**
     * 商品ID
     */
    @Column(name = "goods_id")
    private String goodsId;

    /**
     * 商品名称
     */
    @Column(name = "goods_name")
    private String goodsName;

    /**
     * 回调区间 开始时间
     */
    @Column(name = "ship_start_time")
    private Date shipStartTime;

    /**
     * 回调区间 结束时间
     */
    @Column(name = "ship_end_time")
    private Date shipEndTime;

    /**
     * 运输类型 030230100:汽运 030230200:船运
     */
    @Column(name = "transport_type")
    private String transportType;

    /**
     * 配送方式 100:出厂价 101:到位价
     */
    @Column(name = "delivery_type")
    private String deliveryType;

    /**
     * 发票类型 1：一票制、2：2票制
     */
    @Column(name = "bill_type")
    private Integer billType;

    /**
     * 买家ID
     */
    @Column(name = "buyer_id")
    private String buyerId;

    /**
     * 买家名称
     */
    @Column(name = "buyer_name")
    private String buyerName;

    /**
     * 出库仓库ID
     */
    @Column(name = "warehouse_id")
    private String warehouseId;

    /**
     * 出库仓库名称
     */
    @Column(name = "warehouse_name")
    private String warehouseName;

    /**
     * 价幅度 单位：元/吨
     */
    @Column(name = "adjust_add_price")
    private BigDecimal adjustAddPrice;

    /**
     * 状态 1：待回调、2：回调中、3：回调成功、4：回调失败
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 备注
     */
    @Column(name = "memo")
    private String memo;

    /**
     * ERP返回的错误信息
     */
    @Column(name = "err_msg")
    private String errMsg;

    /**
     * 删除标记 1：已删除、0：未删除
     */
    @Column(name = "del_flg")
    private Integer delFlg;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 获取ID
     *
     * @return id - ID
     */
    public String getId() {
        return id;
    }

    /**
     * 设置ID
     *
     * @param id ID
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * 获取回调编号
     *
     * @return adjust_no - 回调编号
     */
    public String getAdjustNo() {
        return adjustNo;
    }

    /**
     * 设置回调编号
     *
     * @param adjustNo 回调编号
     */
    public void setAdjustNo(String adjustNo) {
        this.adjustNo = adjustNo == null ? null : adjustNo.trim();
    }

    /**
     * 获取名称
     *
     * @return adjust_name - 名称
     */
    public String getAdjustName() {
        return adjustName;
    }

    /**
     * 设置名称
     *
     * @param adjustName 名称
     */
    public void setAdjustName(String adjustName) {
        this.adjustName = adjustName == null ? null : adjustName.trim();
    }

    /**
     * 获取会员ID
     *
     * @return member_id - 会员ID
     */
    public String getMemberId() {
        return memberId;
    }

    /**
     * 设置会员ID
     *
     * @param memberId 会员ID
     */
    public void setMemberId(String memberId) {
        this.memberId = memberId == null ? null : memberId.trim();
    }

    /**
     * 获取会员名称
     *
     * @return member_name - 会员名称
     */
    public String getMemberName() {
        return memberName;
    }

    /**
     * 设置会员名称
     *
     * @param memberName 会员名称
     */
    public void setMemberName(String memberName) {
        this.memberName = memberName == null ? null : memberName.trim();
    }

    /**
     * 获取销售区域ID
     *
     * @return sale_region_id - 销售区域ID
     */
    public String getSaleRegionId() {
        return saleRegionId;
    }

    /**
     * 设置销售区域ID
     *
     * @param saleRegionId 销售区域ID
     */
    public void setSaleRegionId(String saleRegionId) {
        this.saleRegionId = saleRegionId == null ? null : saleRegionId.trim();
    }

    /**
     * 获取销售区域名称
     *
     * @return sale_region_name - 销售区域名称
     */
    public String getSaleRegionName() {
        return saleRegionName;
    }

    /**
     * 设置销售区域名称
     *
     * @param saleRegionName 销售区域名称
     */
    public void setSaleRegionName(String saleRegionName) {
        this.saleRegionName = saleRegionName == null ? null : saleRegionName.trim();
    }

    /**
     * 获取商品ID
     *
     * @return goods_id - 商品ID
     */
    public String getGoodsId() {
        return goodsId;
    }

    /**
     * 设置商品ID
     *
     * @param goodsId 商品ID
     */
    public void setGoodsId(String goodsId) {
        this.goodsId = goodsId == null ? null : goodsId.trim();
    }

    /**
     * 获取商品名称
     *
     * @return goods_name - 商品名称
     */
    public String getGoodsName() {
        return goodsName;
    }

    /**
     * 设置商品名称
     *
     * @param goodsName 商品名称
     */
    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName == null ? null : goodsName.trim();
    }

    /**
     * 获取回调区间 开始时间
     *
     * @return ship_start_time - 回调区间 开始时间
     */
    public Date getShipStartTime() {
        return shipStartTime;
    }

    /**
     * 设置回调区间 开始时间
     *
     * @param shipStartTime 回调区间 开始时间
     */
    public void setShipStartTime(Date shipStartTime) {
        this.shipStartTime = shipStartTime;
    }

    /**
     * 获取回调区间 结束时间
     *
     * @return ship_end_time - 回调区间 结束时间
     */
    public Date getShipEndTime() {
        return shipEndTime;
    }

    /**
     * 设置回调区间 结束时间
     *
     * @param shipEndTime 回调区间 结束时间
     */
    public void setShipEndTime(Date shipEndTime) {
        this.shipEndTime = shipEndTime;
    }

    public String getTransportType()
    {
        return transportType;
    }

    public void setTransportType(String transportType)
    {
        this.transportType = transportType;
    }

    public String getDeliveryType()
    {
        return deliveryType;
    }

    public void setDeliveryType(String deliveryType)
    {
        this.deliveryType = deliveryType;
    }

    public Integer getBillType()
    {
        return billType;
    }

    public void setBillType(Integer billType)
    {
        this.billType = billType;
    }

    /**
     * 获取买家ID
     *
     * @return buyer_id - 买家ID
     */
    public String getBuyerId() {
        return buyerId;
    }

    /**
     * 设置买家ID
     *
     * @param buyerId 买家ID
     */
    public void setBuyerId(String buyerId) {
        this.buyerId = buyerId == null ? null : buyerId.trim();
    }

    /**
     * 获取买家名称
     *
     * @return buyer_name - 买家名称
     */
    public String getBuyerName() {
        return buyerName;
    }

    /**
     * 设置买家名称
     *
     * @param buyerName 买家名称
     */
    public void setBuyerName(String buyerName) {
        this.buyerName = buyerName == null ? null : buyerName.trim();
    }

    /**
     * 获取出库仓库ID
     *
     * @return warehouse_id - 出库仓库ID
     */
    public String getWarehouseId() {
        return warehouseId;
    }

    /**
     * 设置出库仓库ID
     *
     * @param warehouseId 出库仓库ID
     */
    public void setWarehouseId(String warehouseId) {
        this.warehouseId = warehouseId == null ? null : warehouseId.trim();
    }

    /**
     * 获取出库仓库名称
     *
     * @return warehouse_name - 出库仓库名称
     */
    public String getWarehouseName() {
        return warehouseName;
    }

    /**
     * 设置出库仓库名称
     *
     * @param warehouseName 出库仓库名称
     */
    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName == null ? null : warehouseName.trim();
    }

    public BigDecimal getAdjustAddPrice()
    {
        return adjustAddPrice;
    }

    public void setAdjustAddPrice(BigDecimal adjustAddPrice)
    {
        this.adjustAddPrice = adjustAddPrice;
    }

    /**
     * 获取状态 1：待回调、2：回调中、3：回调成功、4：回调失败
     *
     * @return status - 状态 1：待回调、2：回调中、3：回调成功、4：回调失败
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * 设置状态 1：待回调、2：回调中、3：回调成功、4：回调失败
     *
     * @param status 状态 1：待回调、2：回调中、3：回调成功、4：回调失败
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * 获取备注
     *
     * @return memo - 备注
     */
    public String getMemo() {
        return memo;
    }

    /**
     * 设置备注
     *
     * @param memo 备注
     */
    public void setMemo(String memo) {
        this.memo = memo == null ? null : memo.trim();
    }

    public String getErrMsg()
    {
        return errMsg;
    }

    public void setErrMsg(String errMsg)
    {
        this.errMsg = errMsg;
    }

    /**
     * 获取删除标记 1：已删除、0：未删除
     *
     * @return del_flg - 删除标记 1：已删除、0：未删除
     */
    public Integer getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标记 1：已删除、0：未删除
     *
     * @param delFlg 删除标记 1：已删除、0：未删除
     */
    public void setDelFlg(Integer delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", adjustNo=").append(adjustNo);
        sb.append(", adjustName=").append(adjustName);
        sb.append(", memberId=").append(memberId);
        sb.append(", memberName=").append(memberName);
        sb.append(", saleRegionId=").append(saleRegionId);
        sb.append(", saleRegionName=").append(saleRegionName);
        sb.append(", goodsId=").append(goodsId);
        sb.append(", goodsName=").append(goodsName);
        sb.append(", shipStartTime=").append(shipStartTime);
        sb.append(", shipEndTime=").append(shipEndTime);
        sb.append(", buyerId=").append(buyerId);
        sb.append(", buyerName=").append(buyerName);
        sb.append(", warehouseId=").append(warehouseId);
        sb.append(", warehouseName=").append(warehouseName);
        sb.append(", adjustAddPrice=").append(adjustAddPrice);
        sb.append(", status=").append(status);
        sb.append(", memo=").append(memo);
        sb.append(", errMsg=").append(errMsg);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}
