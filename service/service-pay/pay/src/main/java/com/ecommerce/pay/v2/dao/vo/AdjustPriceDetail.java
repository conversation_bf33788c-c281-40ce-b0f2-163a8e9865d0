package com.ecommerce.pay.v2.dao.vo;

import jakarta.persistence.Column;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Table(name = "pa_adjust_price_detail")
public class AdjustPriceDetail implements Serializable {
    /**
     * ID
     */
    @Id
    private String id;

    /**
     * 调价ID
     */
    @Column(name = "price_adjust_id")
    private String priceAdjustId;

    /**
     * 调价编号
     */
    @Column(name = "adjust_no")
    private String adjustNo;

    /**
     * 运单ID
     */
    @Column(name = "waybill_item_id")
    private String waybillItemId;

    /**
     * 运单号
     */
    @Column(name = "waybill_num")
    private String waybillNum;

    @Column(name = "external_waybill_num")
    private String externalWaybillNum;

    /**
     * 买家ID
     */
    @Column(name = "buyer_id")
    private String buyerId;

    /**
     * 买家名称
     */
    @Column(name = "buyer_name")
    private String buyerName;

    /**
     * 合同编号
     */
    @Column(name = "deals_name")
    private String dealsName;

    @Column(name = "sale_region_id")
    private String saleRegionId;

    @Column(name = "sale_region_name")
    private String saleRegionName;

    @Column(name = "goods_id")
    private String goodsId;

    @Column(name = "goods_name")
    private String goodsName;

    /**
     * 运输类型
     */
    @Column(name = "transport_type")
    private String transportType;

    /**
     * 配送方式
     */
    @Column(name = "delivery_type")
    private String deliveryType;

    /**
     * 出库仓库ID
     */
    @Column(name = "warehouse_id")
    private String warehouseId;

    /**
     * 出库仓库名称
     */
    @Column(name = "warehouse_name")
    private String warehouseName;

    /**
     * 成交量
     */
    @Column(name = "actual_quantity")
    private BigDecimal actualQuantity;

    /**
     * 原始成交单价
     */
    @Column(name = "origin_price")
    private BigDecimal originPrice;

    /**
     * 原始成交金额
     */
    @Column(name = "origin_amount")
    private BigDecimal originAmount;

    /**
     * 最新成交单价
     */
    @Column(name = "newest_price")
    private BigDecimal newestPrice;

    /**
     * 物流单价
     */
    @Column(name = "logistics_price")
    private BigDecimal logisticsPrice;

    /**
     * 最新成交金额
     */
    @Column(name = "newest_amount")
    private BigDecimal newestAmount;

    /**
     * 价幅度 单位：元/吨
     */
    @Column(name = "adjust_add_price")
    private BigDecimal adjustAddPrice;

    /**
     * 回调后成交单价 单位：元/吨
     */
    @Column(name = "adjust_price")
    private BigDecimal adjustPrice;

    /**
     * 调整后成交金额
     */
    @Column(name = "adjust_amount")
    private BigDecimal adjustAmount;

    /**
     * 客户ERP编码
     */
    @Column(name = "mdm_code")
    private String mdmCode;

    /**
     * 发票类型 1：一票制、2：2票制
     */
    @Column(name = "bill_type")
    private Integer billType;

    /**
     * 调价次数
     */
    @Column(name = "adjust_num")
    private Integer adjustNum;

    /**
     * 运单出厂时间
     */
    @Column(name = "leave_warehouse_time")
    private Date leaveWarehouseTime;

    /**
     * 运单完成时间
     */
    @Column(name = "complete_time")
    private Date completeTime;

    /**
     * ERP返回的错误信息
     */
    @Column(name = "err_msg")
    private String errMsg;

    /**
     * 删除标记 1：已删除、0：未删除
     */
    @Column(name = "del_flg")
    private Integer delFlg;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 获取ID
     *
     * @return id - ID
     */
    public String getId() {
        return id;
    }

    /**
     * 设置ID
     *
     * @param id ID
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * 获取调价ID
     *
     * @return price_adjust_id - 调价ID
     */
    public String getPriceAdjustId() {
        return priceAdjustId;
    }

    /**
     * 设置调价ID
     *
     * @param priceAdjustId 调价ID
     */
    public void setPriceAdjustId(String priceAdjustId) {
        this.priceAdjustId = priceAdjustId == null ? null : priceAdjustId.trim();
    }

    /**
     * 获取调价编号
     *
     * @return adjust_no - 调价编号
     */
    public String getAdjustNo() {
        return adjustNo;
    }

    /**
     * 设置调价编号
     *
     * @param adjustNo 调价编号
     */
    public void setAdjustNo(String adjustNo) {
        this.adjustNo = adjustNo == null ? null : adjustNo.trim();
    }

    /**
     * 获取运单ID
     *
     * @return waybill_id - 运单ID
     */
    public String getWaybillItemId() {
        return waybillItemId;
    }

    /**
     * 设置运单ID
     *
     * @param waybillItemId 运单ID
     */
    public void setWaybillItemId(String waybillItemId) {
        this.waybillItemId = waybillItemId == null ? null : waybillItemId.trim();
    }

    /**
     * 获取运单号
     *
     * @return waybill_num - 运单号
     */
    public String getWaybillNum() {
        return waybillNum;
    }

    /**
     * 设置运单号
     *
     * @param waybillNum 运单号
     */
    public void setWaybillNum(String waybillNum) {
        this.waybillNum = waybillNum == null ? null : waybillNum.trim();
    }

    public String getExternalWaybillNum()
    {
        return externalWaybillNum;
    }

    public void setExternalWaybillNum(String externalWaybillNum)
    {
        this.externalWaybillNum = externalWaybillNum;
    }

    /**
     * 获取买家ID
     *
     * @return buyer_id - 买家ID
     */
    public String getBuyerId() {
        return buyerId;
    }

    /**
     * 设置买家ID
     *
     * @param buyerId 买家ID
     */
    public void setBuyerId(String buyerId) {
        this.buyerId = buyerId == null ? null : buyerId.trim();
    }

    /**
     * 获取买家名称
     *
     * @return buyer_name - 买家名称
     */
    public String getBuyerName() {
        return buyerName;
    }

    /**
     * 设置买家名称
     *
     * @param buyerName 买家名称
     */
    public void setBuyerName(String buyerName) {
        this.buyerName = buyerName == null ? null : buyerName.trim();
    }

    public String getDealsName()
    {
        return dealsName;
    }

    public void setDealsName(String dealsName)
    {
        this.dealsName = dealsName;
    }

    public String getSaleRegionId()
    {
        return saleRegionId;
    }

    public void setSaleRegionId(String saleRegionId)
    {
        this.saleRegionId = saleRegionId;
    }

    public String getSaleRegionName()
    {
        return saleRegionName;
    }

    public void setSaleRegionName(String saleRegionName)
    {
        this.saleRegionName = saleRegionName;
    }

    public String getGoodsId()
    {
        return goodsId;
    }

    public void setGoodsId(String goodsId)
    {
        this.goodsId = goodsId;
    }

    public String getGoodsName()
    {
        return goodsName;
    }

    public void setGoodsName(String goodsName)
    {
        this.goodsName = goodsName;
    }

    /**
     * 获取运输类型
     *
     * @return transport_type - 运输类型
     */
    public String getTransportType() {
        return transportType;
    }

    /**
     * 设置运输类型
     *
     * @param transportType 运输类型
     */
    public void setTransportType(String transportType) {
        this.transportType = transportType == null ? null : transportType.trim();
    }

    public String getDeliveryType()
    {
        return deliveryType;
    }

    public void setDeliveryType(String deliveryType)
    {
        this.deliveryType = deliveryType;
    }

    /**
     * 获取出库仓库ID
     *
     * @return warehouse_id - 出库仓库ID
     */
    public String getWarehouseId() {
        return warehouseId;
    }

    /**
     * 设置出库仓库ID
     *
     * @param warehouseId 出库仓库ID
     */
    public void setWarehouseId(String warehouseId) {
        this.warehouseId = warehouseId == null ? null : warehouseId.trim();
    }

    /**
     * 获取出库仓库名称
     *
     * @return warehouse_name - 出库仓库名称
     */
    public String getWarehouseName() {
        return warehouseName;
    }

    /**
     * 设置出库仓库名称
     *
     * @param warehouseName 出库仓库名称
     */
    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName == null ? null : warehouseName.trim();
    }

    /**
     * 获取成交量
     *
     * @return actual_quantity - 成交量
     */
    public BigDecimal getActualQuantity() {
        return actualQuantity;
    }

    /**
     * 设置成交量
     *
     * @param actualQuantity 成交量
     */
    public void setActualQuantity(BigDecimal actualQuantity) {
        this.actualQuantity = actualQuantity;
    }

    /**
     * 获取原始成交单价
     *
     * @return origin_price - 原始成交单价
     */
    public BigDecimal getOriginPrice() {
        return originPrice;
    }

    /**
     * 设置原始成交单价
     *
     * @param originPrice 原始成交单价
     */
    public void setOriginPrice(BigDecimal originPrice) {
        this.originPrice = originPrice;
    }

    /**
     * 获取原始成交金额
     *
     * @return origin_amount - 原始成交金额
     */
    public BigDecimal getOriginAmount() {
        return originAmount;
    }

    /**
     * 设置原始成交金额
     *
     * @param originAmount 原始成交金额
     */
    public void setOriginAmount(BigDecimal originAmount) {
        this.originAmount = originAmount;
    }

    /**
     * 获取最新成交单价
     *
     * @return newest_price - 最新成交单价
     */
    public BigDecimal getNewestPrice() {
        return newestPrice;
    }

    /**
     * 设置最新成交单价
     *
     * @param newestPrice 最新成交单价
     */
    public void setNewestPrice(BigDecimal newestPrice) {
        this.newestPrice = newestPrice;
    }

    public BigDecimal getLogisticsPrice()
    {
        return logisticsPrice;
    }

    public void setLogisticsPrice(BigDecimal logisticsPrice)
    {
        this.logisticsPrice = logisticsPrice;
    }

    /**
     * 获取最新成交金额
     *
     * @return newest_amount - 最新成交金额
     */
    public BigDecimal getNewestAmount() {
        return newestAmount;
    }

    /**
     * 设置最新成交金额
     *
     * @param newestAmount 最新成交金额
     */
    public void setNewestAmount(BigDecimal newestAmount) {
        this.newestAmount = newestAmount;
    }

    /**
     * 获取价幅度 单位：元/吨
     *
     * @return adjust_add_price - 价幅度 单位：元/吨
     */
    public BigDecimal getAdjustAddPrice() {
        return adjustAddPrice;
    }

    /**
     * 设置价幅度 单位：元/吨
     *
     * @param adjustAddPrice 价幅度 单位：元/吨
     */
    public void setAdjustAddPrice(BigDecimal adjustAddPrice) {
        this.adjustAddPrice = adjustAddPrice;
    }


    /**
     * 获取回调后成交单价 单位：元/吨
     *
     * @return adjust_price - 回调后成交单价 单位：元/吨
     */
    public BigDecimal getAdjustPrice() {
        return adjustPrice;
    }

    /**
     * 设置回调后成交单价 单位：元/吨
     *
     * @param adjustPrice 回调后成交单价 单位：元/吨
     */
    public void setAdjustPrice(BigDecimal adjustPrice) {
        this.adjustPrice = adjustPrice;
    }

    /**
     * 获取调整后成交金额
     *
     * @return adjust_amount - 调整后成交金额
     */
    public BigDecimal getAdjustAmount() {
        return adjustAmount;
    }

    /**
     * 设置调整后成交金额
     *
     * @param adjustAmount 调整后成交金额
     */
    public void setAdjustAmount(BigDecimal adjustAmount) {
        this.adjustAmount = adjustAmount;
    }

    public String getMdmCode()
    {
        return mdmCode;
    }

    public void setMdmCode(String mdmCode)
    {
        this.mdmCode = mdmCode;
    }

    public Integer getBillType()
    {
        return billType;
    }

    public void setBillType(Integer billType)
    {
        this.billType = billType;
    }

    /**
     * 获取调价次数
     *
     * @return adjust_num - 调价次数
     */
    public Integer getAdjustNum() {
        return adjustNum;
    }

    /**
     * 设置调价次数
     *
     * @param adjustNum 调价次数
     */
    public void setAdjustNum(Integer adjustNum) {
        this.adjustNum = adjustNum;
    }

    public Date getLeaveWarehouseTime()
    {
        return leaveWarehouseTime;
    }

    public void setLeaveWarehouseTime(Date leaveWarehouseTime)
    {
        this.leaveWarehouseTime = leaveWarehouseTime;
    }

    public Date getCompleteTime()
    {
        return completeTime;
    }

    public void setCompleteTime(Date completeTime)
    {
        this.completeTime = completeTime;
    }

    /**
     * 获取删除标记 1：已删除、0：未删除
     *
     * @return del_flg - 删除标记 1：已删除、0：未删除
     */
    public Integer getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标记 1：已删除、0：未删除
     *
     * @param delFlg 删除标记 1：已删除、0：未删除
     */
    public void setDelFlg(Integer delFlg) {
        this.delFlg = delFlg;
    }

    public String getErrMsg()
    {
        return errMsg;
    }

    public void setErrMsg(String errMsg)
    {
        this.errMsg = errMsg;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", priceAdjustId=").append(priceAdjustId);
        sb.append(", adjustNo=").append(adjustNo);
        sb.append(", waybillItemId=").append(waybillItemId);
        sb.append(", waybillNum=").append(waybillNum);
        sb.append(", buyerId=").append(buyerId);
        sb.append(", buyerName=").append(buyerName);
        sb.append(", transportType=").append(transportType);
        sb.append(", warehouseId=").append(warehouseId);
        sb.append(", warehouseName=").append(warehouseName);
        sb.append(", actualQuantity=").append(actualQuantity);
        sb.append(", originPrice=").append(originPrice);
        sb.append(", originAmount=").append(originAmount);
        sb.append(", newestPrice=").append(newestPrice);
        sb.append(", newestAmount=").append(newestAmount);
        sb.append(", adjustPrice=").append(adjustPrice);
        sb.append(", adjustAmount=").append(adjustAmount);
        sb.append(", adjustNum=").append(adjustNum);
        sb.append(", errMsg=").append(errMsg);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}
