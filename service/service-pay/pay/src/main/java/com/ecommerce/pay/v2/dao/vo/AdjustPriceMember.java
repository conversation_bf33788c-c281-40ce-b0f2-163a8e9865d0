package com.ecommerce.pay.v2.dao.vo;

import jakarta.persistence.Column;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Table(name = "pa_adjust_price_member")
public class AdjustPriceMember implements Serializable {
    /**
     * ID
     */
    @Id
    private String id;

    /**
     * 价格回调ID
     */
    @Column(name = "price_adjust_id")
    private String priceAdjustId;

    /**
     * 价格回调编号
     */
    @Column(name = "adjust_no")
    private String adjustNo;

    /**
     * 买家ID
     */
    @Column(name = "buyer_id")
    private String buyerId;

    /**
     * 买家名称
     */
    @Column(name = "buyer_name")
    private String buyerName;

    /**
     * 合同编号
     */
    @Column(name = "deals_name")
    private String dealsName;

    @Column(name = "sale_region_id")
    private String saleRegionId;

    @Column(name = "sale_region_name")
    private String saleRegionName;

    @Column(name = "warehouse_id")
    private String  warehouseId;

    @Column(name = "warehouse_name")
    private String warehouseName;

    @Column(name = "goods_id")
    private String goodsId;

    @Column(name = "goods_name")
    private String goodsName;

    /**
     * 成交量 单位：吨
     */
    @Column(name = "total_quantity")
    private BigDecimal totalQuantity;

    /**
     * 原始成交金额
     */
    @Column(name = "origin_amount")
    private BigDecimal originAmount;

    /**
     * 最新成交金额
     */
    @Column(name = "newest_amount")
    private BigDecimal newestAmount;

    /**
     * 价幅度 单位：元/吨
     */
    @Column(name = "adjust_add_price")
    private BigDecimal adjustAddPrice;

    /**
     * 调整后成交金额
     */
    @Column(name = "adjust_amount")
    private BigDecimal adjustAmount;

    /**
     * 删除标记 1：已删除、0：未删除
     */
    @Column(name = "del_flg")
    private Integer delFlg;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 获取ID
     *
     * @return id - ID
     */
    public String getId() {
        return id;
    }

    /**
     * 设置ID
     *
     * @param id ID
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * 获取价格回调ID
     *
     * @return price_adjust_id - 价格回调ID
     */
    public String getPriceAdjustId() {
        return priceAdjustId;
    }

    /**
     * 设置价格回调ID
     *
     * @param priceAdjustId 价格回调ID
     */
    public void setPriceAdjustId(String priceAdjustId) {
        this.priceAdjustId = priceAdjustId == null ? null : priceAdjustId.trim();
    }

    /**
     * 获取价格回调编号
     *
     * @return adjust_no - 价格回调编号
     */
    public String getAdjustNo() {
        return adjustNo;
    }

    /**
     * 设置价格回调编号
     *
     * @param adjustNo 价格回调编号
     */
    public void setAdjustNo(String adjustNo) {
        this.adjustNo = adjustNo == null ? null : adjustNo.trim();
    }

    /**
     * 获取买家ID
     *
     * @return buyer_id - 买家ID
     */
    public String getBuyerId() {
        return buyerId;
    }

    /**
     * 设置买家ID
     *
     * @param buyerId 买家ID
     */
    public void setBuyerId(String buyerId) {
        this.buyerId = buyerId == null ? null : buyerId.trim();
    }

    /**
     * 获取买家名称
     *
     * @return buyer_name - 买家名称
     */
    public String getBuyerName() {
        return buyerName;
    }

    public String getDealsName()
    {
        return dealsName;
    }

    public void setDealsName(String dealsName)
    {
        this.dealsName = dealsName;
    }

    /**
     * 设置买家名称
     *
     * @param buyerName 买家名称
     */
    public void setBuyerName(String buyerName) {
        this.buyerName = buyerName == null ? null : buyerName.trim();
    }


    public String getSaleRegionId()
    {
        return saleRegionId;
    }

    public void setSaleRegionId(String saleRegionId)
    {
        this.saleRegionId = saleRegionId;
    }

    public String getSaleRegionName()
    {
        return saleRegionName;
    }

    public void setSaleRegionName(String saleRegionName)
    {
        this.saleRegionName = saleRegionName;
    }

    public String getWarehouseId()
    {
        return warehouseId;
    }

    public void setWarehouseId(String warehouseId)
    {
        this.warehouseId = warehouseId;
    }

    public String getWarehouseName()
    {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName)
    {
        this.warehouseName = warehouseName;
    }

    public String getGoodsId()
    {
        return goodsId;
    }

    public void setGoodsId(String goodsId)
    {
        this.goodsId = goodsId;
    }

    public String getGoodsName()
    {
        return goodsName;
    }

    public void setGoodsName(String goodsName)
    {
        this.goodsName = goodsName;
    }

    /**
     * 获取成交量 单位：吨
     *
     * @return total_quantity - 成交量 单位：吨
     */
    public BigDecimal getTotalQuantity() {
        return totalQuantity;
    }

    /**
     * 设置成交量 单位：吨
     *
     * @param totalQuantity 成交量 单位：吨
     */
    public void setTotalQuantity(BigDecimal totalQuantity) {
        this.totalQuantity = totalQuantity;
    }

    /**
     * 获取原始成交金额
     *
     * @return origin_amount - 原始成交金额
     */
    public BigDecimal getOriginAmount() {
        return originAmount;
    }

    /**
     * 设置原始成交金额
     *
     * @param originAmount 原始成交金额
     */
    public void setOriginAmount(BigDecimal originAmount) {
        this.originAmount = originAmount;
    }

    /**
     * 获取最新成交金额
     *
     * @return newest_amount - 最新成交金额
     */
    public BigDecimal getNewestAmount() {
        return newestAmount;
    }

    /**
     * 设置最新成交金额
     *
     * @param newestAmount 最新成交金额
     */
    public void setNewestAmount(BigDecimal newestAmount) {
        this.newestAmount = newestAmount;
    }

    /**
     * 获取价幅度 单位：元/吨
     *
     * @return adjust_add_price - 价幅度 单位：元/吨
     */
    public BigDecimal getAdjustAddPrice() {
        return adjustAddPrice;
    }

    /**
     * 设置价幅度 单位：元/吨
     *
     * @param adjustAddPrice 价幅度 单位：元/吨
     */
    public void setAdjustAddPrice(BigDecimal adjustAddPrice) {
        this.adjustAddPrice = adjustAddPrice;
    }

    /**
     * 获取调整后成交金额
     *
     * @return adjust_amount - 调整后成交金额
     */
    public BigDecimal getAdjustAmount() {
        return adjustAmount;
    }

    /**
     * 设置调整后成交金额
     *
     * @param adjustAmount 调整后成交金额
     */
    public void setAdjustAmount(BigDecimal adjustAmount) {
        this.adjustAmount = adjustAmount;
    }

    /**
     * 获取删除标记 1：已删除、0：未删除
     *
     * @return del_flg - 删除标记 1：已删除、0：未删除
     */
    public Integer getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标记 1：已删除、0：未删除
     *
     * @param delFlg 删除标记 1：已删除、0：未删除
     */
    public void setDelFlg(Integer delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", priceAdjustId=").append(priceAdjustId);
        sb.append(", adjustNo=").append(adjustNo);
        sb.append(", buyerId=").append(buyerId);
        sb.append(", buyerName=").append(buyerName);
        sb.append(", totalQuantity=").append(totalQuantity);
        sb.append(", originAmount=").append(originAmount);
        sb.append(", newestAmount=").append(newestAmount);
        sb.append(", adjustAddPrice=").append(adjustAddPrice);
        sb.append(", adjustAmount=").append(adjustAmount);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}
