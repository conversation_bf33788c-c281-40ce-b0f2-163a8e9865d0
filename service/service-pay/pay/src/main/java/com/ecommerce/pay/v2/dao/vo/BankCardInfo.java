package com.ecommerce.pay.v2.dao.vo;

import java.io.Serial;
import java.io.Serializable;
import jakarta.persistence.*;

@Table(name = "pa_bank_card_info")
public class BankCardInfo implements Serializable {
    @Id
    @Column(name = "bank_card_info_id")
    private String bankCardInfoId;

    /**
     * 银行机构编号
     */
    @Column(name = "bank_card_no")
    private String bankCardNo;

    /**
     * 银行名
     */
    @Column(name = "bank_name")
    private String bankName;

    /**
     * 银行卡号长度
     */
    @Column(name = "card_length")
    private Integer cardLength;

    /**
     * 银行卡号前缀
     */
    @Column(name = "card_prefix")
    private String cardPrefix;

    /**
     * 是否银联品牌卡
     */
    @Column(name = "card_union_pay")
    private String cardUnionPay;

    /**
     * 卡种 1-借记卡,2-贷记卡,3-准贷记卡,4-预付卡
     */
    @Column(name = "card_type")
    private String cardType;

    /**
     * 银行logo
     */
    @Column(name = "card_icon")
    private String cardIcon;

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * @return bank_card_info_id
     */
    public String getBankCardInfoId() {
        return bankCardInfoId;
    }

    /**
     * @param bankCardInfoId
     */
    public void setBankCardInfoId(String bankCardInfoId) {
        this.bankCardInfoId = bankCardInfoId == null ? null : bankCardInfoId.trim();
    }

    /**
     * 获取银行机构编号
     *
     * @return bank_card_no - 银行机构编号
     */
    public String getBankCardNo() {
        return bankCardNo;
    }

    /**
     * 设置银行机构编号
     *
     * @param bankCardNo 银行机构编号
     */
    public void setBankCardNo(String bankCardNo) {
        this.bankCardNo = bankCardNo == null ? null : bankCardNo.trim();
    }

    /**
     * 获取银行名
     *
     * @return bank_name - 银行名
     */
    public String getBankName() {
        return bankName;
    }

    /**
     * 设置银行名
     *
     * @param bankName 银行名
     */
    public void setBankName(String bankName) {
        this.bankName = bankName == null ? null : bankName.trim();
    }

    /**
     * 获取银行卡号长度
     *
     * @return card_length - 银行卡号长度
     */
    public Integer getCardLength() {
        return cardLength;
    }

    /**
     * 设置银行卡号长度
     *
     * @param cardLength 银行卡号长度
     */
    public void setCardLength(Integer cardLength) {
        this.cardLength = cardLength;
    }

    /**
     * 获取银行卡号前缀
     *
     * @return card_prefix - 银行卡号前缀
     */
    public String getCardPrefix() {
        return cardPrefix;
    }

    /**
     * 设置银行卡号前缀
     *
     * @param cardPrefix 银行卡号前缀
     */
    public void setCardPrefix(String cardPrefix) {
        this.cardPrefix = cardPrefix == null ? null : cardPrefix.trim();
    }

    /**
     * 获取是否银联品牌卡
     *
     * @return card_union_pay - 是否银联品牌卡
     */
    public String getCardUnionPay() {
        return cardUnionPay;
    }

    /**
     * 设置是否银联品牌卡
     *
     * @param cardUnionPay 是否银联品牌卡
     */
    public void setCardUnionPay(String cardUnionPay) {
        this.cardUnionPay = cardUnionPay == null ? null : cardUnionPay.trim();
    }

    /**
     * 获取卡种 1-借记卡,2-贷记卡,3-准贷记卡,4-预付卡
     *
     * @return card_type - 卡种 1-借记卡,2-贷记卡,3-准贷记卡,4-预付卡
     */
    public String getCardType() {
        return cardType;
    }

    /**
     * 设置卡种 1-借记卡,2-贷记卡,3-准贷记卡,4-预付卡
     *
     * @param cardType 卡种 1-借记卡,2-贷记卡,3-准贷记卡,4-预付卡
     */
    public void setCardType(String cardType) {
        this.cardType = cardType == null ? null : cardType.trim();
    }

    /**
     * 获取银行logo
     *
     * @return card_icon - 银行logo
     */
    public String getCardIcon() {
        return cardIcon;
    }

    /**
     * 设置银行logo
     *
     * @param cardIcon 银行logo
     */
    public void setCardIcon(String cardIcon) {
        this.cardIcon = cardIcon == null ? null : cardIcon.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", bankCardInfoId=").append(bankCardInfoId);
        sb.append(", bankCardNo=").append(bankCardNo);
        sb.append(", bankName=").append(bankName);
        sb.append(", cardLength=").append(cardLength);
        sb.append(", cardPrefix=").append(cardPrefix);
        sb.append(", cardUnionPay=").append(cardUnionPay);
        sb.append(", cardType=").append(cardType);
        sb.append(", cardIcon=").append(cardIcon);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}