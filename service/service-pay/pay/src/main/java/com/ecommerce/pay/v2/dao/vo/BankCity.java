package com.ecommerce.pay.v2.dao.vo;

import java.io.Serial;
import java.io.Serializable;
import jakarta.persistence.*;

@Table(name = "pa_bankcity")
public class BankCity implements Serializable {
    @Id
    @Column(name = "city_areacode")
    private String cityAreacode;

    @Column(name = "city_areaname")
    private String cityAreaname;

    @Column(name = "city_areatype")
    private String cityAreatype;

    @Column(name = "city_nodecode")
    private String cityNodecode;

    @Column(name = "city_topareacode1")
    private String cityTopareacode1;

    @Column(name = "city_topareacode2")
    private String cityTopareacode2;

    @Column(name = "city_topareacode3")
    private String cityTopareacode3;

    @Column(name = "city_oraareacode")
    private String cityOraareacode;

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * @return city_areacode
     */
    public String getCityAreacode() {
        return cityAreacode;
    }

    /**
     * @param cityAreacode
     */
    public void setCityAreacode(String cityAreacode) {
        this.cityAreacode = cityAreacode == null ? null : cityAreacode.trim();
    }

    /**
     * @return city_areaname
     */
    public String getCityAreaname() {
        return cityAreaname;
    }

    /**
     * @param cityAreaname
     */
    public void setCityAreaname(String cityAreaname) {
        this.cityAreaname = cityAreaname == null ? null : cityAreaname.trim();
    }

    /**
     * @return city_areatype
     */
    public String getCityAreatype() {
        return cityAreatype;
    }

    /**
     * @param cityAreatype
     */
    public void setCityAreatype(String cityAreatype) {
        this.cityAreatype = cityAreatype == null ? null : cityAreatype.trim();
    }

    /**
     * @return city_nodecode
     */
    public String getCityNodecode() {
        return cityNodecode;
    }

    /**
     * @param cityNodecode
     */
    public void setCityNodecode(String cityNodecode) {
        this.cityNodecode = cityNodecode == null ? null : cityNodecode.trim();
    }

    /**
     * @return city_topareacode1
     */
    public String getCityTopareacode1() {
        return cityTopareacode1;
    }

    /**
     * @param cityTopareacode1
     */
    public void setCityTopareacode1(String cityTopareacode1) {
        this.cityTopareacode1 = cityTopareacode1 == null ? null : cityTopareacode1.trim();
    }

    /**
     * @return city_topareacode2
     */
    public String getCityTopareacode2() {
        return cityTopareacode2;
    }

    /**
     * @param cityTopareacode2
     */
    public void setCityTopareacode2(String cityTopareacode2) {
        this.cityTopareacode2 = cityTopareacode2 == null ? null : cityTopareacode2.trim();
    }

    /**
     * @return city_topareacode3
     */
    public String getCityTopareacode3() {
        return cityTopareacode3;
    }

    /**
     * @param cityTopareacode3
     */
    public void setCityTopareacode3(String cityTopareacode3) {
        this.cityTopareacode3 = cityTopareacode3 == null ? null : cityTopareacode3.trim();
    }

    /**
     * @return city_oraareacode
     */
    public String getCityOraareacode() {
        return cityOraareacode;
    }

    /**
     * @param cityOraareacode
     */
    public void setCityOraareacode(String cityOraareacode) {
        this.cityOraareacode = cityOraareacode == null ? null : cityOraareacode.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", cityAreacode=").append(cityAreacode);
        sb.append(", cityAreaname=").append(cityAreaname);
        sb.append(", cityAreatype=").append(cityAreatype);
        sb.append(", cityNodecode=").append(cityNodecode);
        sb.append(", cityTopareacode1=").append(cityTopareacode1);
        sb.append(", cityTopareacode2=").append(cityTopareacode2);
        sb.append(", cityTopareacode3=").append(cityTopareacode3);
        sb.append(", cityOraareacode=").append(cityOraareacode);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}