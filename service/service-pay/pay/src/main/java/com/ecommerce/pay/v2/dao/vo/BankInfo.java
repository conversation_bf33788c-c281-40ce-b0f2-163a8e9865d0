package com.ecommerce.pay.v2.dao.vo;

import java.io.Serial;
import java.io.Serializable;
import jakarta.persistence.*;

@Table(name = "pa_bankinfo")
public class BankInfo implements Serializable {
    /**
     * 支付行号
     */
    @Id
    @Column(name = "bank_no")
    private String bankNo;

    /**
     * 行号状态
     */
    @Column(name = "bank_status")
    private String bankStatus;

    /**
     * 行别代码
     */
    @Column(name = "bank_cls_code")
    private String bankClsCode;

    /**
     * 城市代码
     */
    @Column(name = "city_code")
    private String cityCode;

    /**
     * 行名全称
     */
    @Column(name = "bank_name")
    private String bankName;

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 获取支付行号
     *
     * @return bank_no - 支付行号
     */
    public String getBankNo() {
        return bankNo;
    }

    /**
     * 设置支付行号
     *
     * @param bankNo 支付行号
     */
    public void setBankNo(String bankNo) {
        this.bankNo = bankNo == null ? null : bankNo.trim();
    }

    /**
     * 获取行号状态
     *
     * @return bank_status - 行号状态
     */
    public String getBankStatus() {
        return bankStatus;
    }

    /**
     * 设置行号状态
     *
     * @param bankStatus 行号状态
     */
    public void setBankStatus(String bankStatus) {
        this.bankStatus = bankStatus == null ? null : bankStatus.trim();
    }

    /**
     * 获取行别代码
     *
     * @return bank_cls_code - 行别代码
     */
    public String getBankClsCode() {
        return bankClsCode;
    }

    /**
     * 设置行别代码
     *
     * @param bankClsCode 行别代码
     */
    public void setBankClsCode(String bankClsCode) {
        this.bankClsCode = bankClsCode == null ? null : bankClsCode.trim();
    }

    /**
     * 获取城市代码
     *
     * @return city_code - 城市代码
     */
    public String getCityCode() {
        return cityCode;
    }

    /**
     * 设置城市代码
     *
     * @param cityCode 城市代码
     */
    public void setCityCode(String cityCode) {
        this.cityCode = cityCode == null ? null : cityCode.trim();
    }

    /**
     * 获取行名全称
     *
     * @return bank_name - 行名全称
     */
    public String getBankName() {
        return bankName;
    }

    /**
     * 设置行名全称
     *
     * @param bankName 行名全称
     */
    public void setBankName(String bankName) {
        this.bankName = bankName == null ? null : bankName.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", bankNo=").append(bankNo);
        sb.append(", bankStatus=").append(bankStatus);
        sb.append(", bankClsCode=").append(bankClsCode);
        sb.append(", cityCode=").append(cityCode);
        sb.append(", bankName=").append(bankName);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}