package com.ecommerce.pay.v2.dao.vo;

import lombok.Data;

import jakarta.persistence.Column;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Table(name = "pa_bill_logs")
public class BillLogs implements Serializable {

    @Serial
    private static final long serialVersionUID = 1475511917458015859L;

    @Id
    @Column(name = "bill_logs_id")
    private String billLogsId;

    @Column(name = "bill_logs_no")
    private String billLogsNo;

    /**
     * 关联单据ID
     */
    @Column(name = "related_bill_id")
    private String relatedBillId;

    /**
     * 关联单据no
     */
    @Column(name = "related_bill_no")
    private String relatedBillNo;

    @Column(name = "third_bill_no")
    private String thirdBillNo;

    @Column(name = "member_id")
    private String memberId;

    @Column(name = "member_name")
    private String memberName;

    @Column(name = "payer_flg")
    private Boolean payerFlg;

    @Column(name = "channel_code")
    private String channelCode;

    @Column(name = "channel_name")
    private String channelName;

    @Column(name = "channel_id")
    private String channelId;

    @Column(name = "order_id")
    private String orderId;

    @Column(name = "order_no")
    private String orderNo;

    @Column(name = "pay_type")
    private String payType;

    @Column(name = "sub_pay_type")
    private String subPayType;

    @Column(name = "target_member_id")
    private String targetMemberId;

    @Column(name = "target_member_name")
    private String targetMemberName;

    @Column(name = "actual_pay_amount")
    private BigDecimal actualPayAmount;

    /**
     * 详情
     */
    private String detail;

    private String operator;

    /**
     * 删除标记
     */
    @Column(name = "del_flg")
    private Boolean delFlg;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;
    /**
     * 订单配送方式
     */
    @Column(name = "deliver_way")
    private String deliverWay;
}