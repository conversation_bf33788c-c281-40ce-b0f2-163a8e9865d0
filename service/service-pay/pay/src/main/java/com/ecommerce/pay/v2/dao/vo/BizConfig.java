package com.ecommerce.pay.v2.dao.vo;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import jakarta.persistence.*;

@Table(name = "pa_biz_config")
public class BizConfig implements Serializable {
    @Id
    @Column(name = "biz_conf_id")
    private String bizConfId;

    @Column(name = "biz_src_name")
    private String bizSrcName;

    @Column(name = "biz_src_code")
    private String bizSrcCode;

    /**
     * 不可用，已启用
     */
    @Column(name = "biz_status")
    private Boolean bizStatus;

    /**
     * 微信小程序、微信扫描支付、微信h5支付
     */
    @Column(name = "biz_name")
    private String bizName;

    /**
     * 商业银行；第三方支付平台；
     */
    @Column(name = "biz_type")
    private String bizType;

    @Column(name = "biz_code")
    private String bizCode;

    /**
     * 查询支付渠道信息的URL
     */
    @Column(name = "query_channel_url")
    private String queryChannelUrl;

    /**
     * 查询支付业务信息的URL
     */
    @Column(name = "query_biz_url")
    private String queryBizUrl;

    /**
     * 业务完成回调链接
     */
    @Column(name = "notify_url")
    private String notifyUrl;

    /**
     * 更新第三方业务支付单url
     */
    @Column(name = "update_bill_url")
    private String updateBillUrl;

    /**
     * 从第三方查询线下支付结果
     */
    @Column(name = "query_offlineresult_url")
    private String queryOfflineresultUrl;

    /**
     * 是否使用担保支付
     */
    @Column(name = "is_use_guaranteepay")
    private Boolean isUseGuaranteepay;

    /**
     * 删除标记
     */
    @Column(name = "del_flg")
    private Boolean delFlg;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * @return biz_conf_id
     */
    public String getBizConfId() {
        return bizConfId;
    }

    /**
     * @param bizConfId
     */
    public void setBizConfId(String bizConfId) {
        this.bizConfId = bizConfId == null ? null : bizConfId.trim();
    }

    /**
     * @return biz_src_name
     */
    public String getBizSrcName() {
        return bizSrcName;
    }

    /**
     * @param bizSrcName
     */
    public void setBizSrcName(String bizSrcName) {
        this.bizSrcName = bizSrcName == null ? null : bizSrcName.trim();
    }

    /**
     * @return biz_src_code
     */
    public String getBizSrcCode() {
        return bizSrcCode;
    }

    /**
     * @param bizSrcCode
     */
    public void setBizSrcCode(String bizSrcCode) {
        this.bizSrcCode = bizSrcCode == null ? null : bizSrcCode.trim();
    }

    /**
     * 获取不可用，已启用
     *
     * @return biz_status - 不可用，已启用
     */
    public Boolean getBizStatus() {
        return bizStatus;
    }

    /**
     * 设置不可用，已启用
     *
     * @param bizStatus 不可用，已启用
     */
    public void setBizStatus(Boolean bizStatus) {
        this.bizStatus = bizStatus;
    }

    /**
     * 获取微信小程序、微信扫描支付、微信h5支付
     *
     * @return biz_name - 微信小程序、微信扫描支付、微信h5支付
     */
    public String getBizName() {
        return bizName;
    }

    /**
     * 设置微信小程序、微信扫描支付、微信h5支付
     *
     * @param bizName 微信小程序、微信扫描支付、微信h5支付
     */
    public void setBizName(String bizName) {
        this.bizName = bizName == null ? null : bizName.trim();
    }

    /**
     * 获取商业银行；第三方支付平台；
     *
     * @return biz_type - 商业银行；第三方支付平台；
     */
    public String getBizType() {
        return bizType;
    }

    /**
     * 设置商业银行；第三方支付平台；
     *
     * @param bizType 商业银行；第三方支付平台；
     */
    public void setBizType(String bizType) {
        this.bizType = bizType == null ? null : bizType.trim();
    }

    /**
     * @return biz_code
     */
    public String getBizCode() {
        return bizCode;
    }

    /**
     * @param bizCode
     */
    public void setBizCode(String bizCode) {
        this.bizCode = bizCode == null ? null : bizCode.trim();
    }

    /**
     * 获取查询支付渠道信息的URL
     *
     * @return query_channel_url - 查询支付渠道信息的URL
     */
    public String getQueryChannelUrl() {
        return queryChannelUrl;
    }

    /**
     * 设置查询支付渠道信息的URL
     *
     * @param queryChannelUrl 查询支付渠道信息的URL
     */
    public void setQueryChannelUrl(String queryChannelUrl) {
        this.queryChannelUrl = queryChannelUrl == null ? null : queryChannelUrl.trim();
    }

    /**
     * 获取查询支付业务信息的URL
     *
     * @return query_biz_url - 查询支付业务信息的URL
     */
    public String getQueryBizUrl() {
        return queryBizUrl;
    }

    /**
     * 设置查询支付业务信息的URL
     *
     * @param queryBizUrl 查询支付业务信息的URL
     */
    public void setQueryBizUrl(String queryBizUrl) {
        this.queryBizUrl = queryBizUrl == null ? null : queryBizUrl.trim();
    }

    /**
     * 获取业务完成回调链接
     *
     * @return notify_url - 业务完成回调链接
     */
    public String getNotifyUrl() {
        return notifyUrl;
    }

    /**
     * 设置业务完成回调链接
     *
     * @param notifyUrl 业务完成回调链接
     */
    public void setNotifyUrl(String notifyUrl) {
        this.notifyUrl = notifyUrl == null ? null : notifyUrl.trim();
    }

    /**
     * 获取更新第三方业务支付单url
     *
     * @return update_bill_url - 更新第三方业务支付单url
     */
    public String getUpdateBillUrl() {
        return updateBillUrl;
    }

    /**
     * 设置更新第三方业务支付单url
     *
     * @param updateBillUrl 更新第三方业务支付单url
     */
    public void setUpdateBillUrl(String updateBillUrl) {
        this.updateBillUrl = updateBillUrl == null ? null : updateBillUrl.trim();
    }

    /**
     * 获取从第三方查询线下支付结果
     *
     * @return query_offlineresult_url - 从第三方查询线下支付结果
     */
    public String getQueryOfflineresultUrl() {
        return queryOfflineresultUrl;
    }

    /**
     * 设置从第三方查询线下支付结果
     *
     * @param queryOfflineresultUrl 从第三方查询线下支付结果
     */
    public void setQueryOfflineresultUrl(String queryOfflineresultUrl) {
        this.queryOfflineresultUrl = queryOfflineresultUrl == null ? null : queryOfflineresultUrl.trim();
    }

    /**
     * 获取是否使用担保支付
     *
     * @return is_use_guaranteepay - 是否使用担保支付
     */
    public Boolean getIsUseGuaranteepay() {
        return isUseGuaranteepay;
    }

    /**
     * 设置是否使用担保支付
     *
     * @param isUseGuaranteepay 是否使用担保支付
     */
    public void setIsUseGuaranteepay(Boolean isUseGuaranteepay) {
        this.isUseGuaranteepay = isUseGuaranteepay;
    }

    /**
     * 获取删除标记
     *
     * @return del_flg - 删除标记
     */
    public Boolean getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标记
     *
     * @param delFlg 删除标记
     */
    public void setDelFlg(Boolean delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建人
     *
     * @return create_user - 创建人
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建人
     *
     * @param createUser 创建人
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取修改人
     *
     * @return update_user - 修改人
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置修改人
     *
     * @param updateUser 修改人
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取修改时间
     *
     * @return update_time - 修改时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置修改时间
     *
     * @param updateTime 修改时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", bizConfId=").append(bizConfId);
        sb.append(", bizSrcName=").append(bizSrcName);
        sb.append(", bizSrcCode=").append(bizSrcCode);
        sb.append(", bizStatus=").append(bizStatus);
        sb.append(", bizName=").append(bizName);
        sb.append(", bizType=").append(bizType);
        sb.append(", bizCode=").append(bizCode);
        sb.append(", queryChannelUrl=").append(queryChannelUrl);
        sb.append(", queryBizUrl=").append(queryBizUrl);
        sb.append(", notifyUrl=").append(notifyUrl);
        sb.append(", updateBillUrl=").append(updateBillUrl);
        sb.append(", queryOfflineresultUrl=").append(queryOfflineresultUrl);
        sb.append(", isUseGuaranteepay=").append(isUseGuaranteepay);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}