package com.ecommerce.pay.v2.dao.vo;

import jakarta.persistence.Column;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

@Table(name = "pa_channel_card")
public class ChannelCard implements Serializable {
    @Id
    @Column(name = "channel_card_id")
    private String channelCardId;

    @Column(name = "channel_card_code")
    private String channelCardCode;

    /**
     * opend已开通，created待认证，closed已关闭
     */
    @Column(name = "channel_card_status")
    private String channelCardStatus;

    @Column(name = "member_channel_id")
    private String memberChannelId;

    @Column(name = "member_channel_no")
    private String memberChannelNo;

    /**
     * 会员id
     */
    @Column(name = "member_id")
    private String memberId;

    @Column(name = "member_name")
    private String memberName;

    /**
     * 基础银行名称
     */
    @Column(name = "bank_base_name")
    private String bankBaseName;

    /**
     * 银行名称
     */
    @Column(name = "bank_name")
    private String bankName;

    /**
     * 开户号
     */
    @Column(name = "bank_account")
    private String bankAccount;

    /**
     * 开户名称
     */
    @Column(name = "bank_account_name")
    private String bankAccountName;

    /**
     * 银行类型 1：本行 2：他行
     */
    @Column(name = "bank_type")
    private String bankType;

    /**
     * 大小额行号
     */
    @Column(name = "bank_cnap_id")
    private String bankCnapId;

    /**
     * 超级网银行号
     */
    @Column(name = "bank_eicon_id")
    private String bankEiconId;

    /**
     * 图片路径
     */
    @Column(name = "pic_url")
    private String picUrl;

    /**
     * 备注
     */
    private String mome;

    /**
     * 联系人
     */
    @Column(name = "contact_name")
    private String contactName;

    /**
     * 电话
     */
    @Column(name = "contact_phone")
    private String contactPhone;

    /**
     * 收款开始时间
     */
    @Column(name = "start_time")
    private Date startTime;

    /**
     * 结束收款时间
     */
    @Column(name = "end_time")
    private Date endTime;

    /**
     * 最后验证时间
     */
    @Column(name = "last_verify_time")
    private Date lastVerifyTime;

    /**
     * 验证通过时间
     */
    @Column(name = "pass_verify_time")
    private Date passVerifyTime;

    /**
     * 关闭时间
     */
    @Column(name = "close_time")
    private Date closeTime;

    private String days;

    private String ext1;

    private String ext2;

    private String ext3;

    private String ext4;

    /**
     * 删除标记
     */
    @Column(name = "del_flg")
    private Boolean delFlg;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    @Serial
    private static final long serialVersionUID = 1L;

    public String getBankBaseName() {
        return bankBaseName;
    }

    public void setBankBaseName(String bankBaseName) {
        this.bankBaseName = bankBaseName;
    }

    /**
     * @return channel_card_id
     */
    public String getChannelCardId() {
        return channelCardId;
    }

    /**
     * @param channelCardId
     */
    public void setChannelCardId(String channelCardId) {
        this.channelCardId = channelCardId == null ? null : channelCardId.trim();
    }

    /**
     * @return channel_card_code
     */
    public String getChannelCardCode() {
        return channelCardCode;
    }

    /**
     * @param channelCardCode
     */
    public void setChannelCardCode(String channelCardCode) {
        this.channelCardCode = channelCardCode == null ? null : channelCardCode.trim();
    }

    /**
     * 获取opend已开通，created待认证，closed已关闭
     *
     * @return channel_card_status - opend已开通，created待认证，closed已关闭
     */
    public String getChannelCardStatus() {
        return channelCardStatus;
    }

    /**
     * 设置opend已开通，created待认证，closed已关闭
     *
     * @param channelCardStatus opend已开通，created待认证，closed已关闭
     */
    public void setChannelCardStatus(String channelCardStatus) {
        this.channelCardStatus = channelCardStatus == null ? null : channelCardStatus.trim();
    }

    /**
     * @return member_channel_id
     */
    public String getMemberChannelId() {
        return memberChannelId;
    }

    /**
     * @param memberChannelId
     */
    public void setMemberChannelId(String memberChannelId) {
        this.memberChannelId = memberChannelId == null ? null : memberChannelId.trim();
    }

    /**
     * @return member_channel_no
     */
    public String getMemberChannelNo() {
        return memberChannelNo;
    }

    /**
     * @param memberChannelNo
     */
    public void setMemberChannelNo(String memberChannelNo) {
        this.memberChannelNo = memberChannelNo == null ? null : memberChannelNo.trim();
    }

    /**
     * 获取会员id
     *
     * @return member_id - 会员id
     */
    public String getMemberId() {
        return memberId;
    }

    /**
     * 设置会员id
     *
     * @param memberId 会员id
     */
    public void setMemberId(String memberId) {
        this.memberId = memberId == null ? null : memberId.trim();
    }

    /**
     * @return member_name
     */
    public String getMemberName() {
        return memberName;
    }

    /**
     * @param memberName
     */
    public void setMemberName(String memberName) {
        this.memberName = memberName == null ? null : memberName.trim();
    }

    /**
     * 获取银行名称
     *
     * @return bank_name - 银行名称
     */
    public String getBankName() {
        return bankName;
    }

    /**
     * 设置银行名称
     *
     * @param bankName 银行名称
     */
    public void setBankName(String bankName) {
        this.bankName = bankName == null ? null : bankName.trim();
    }

    /**
     * 获取开户号
     *
     * @return bank_account - 开户号
     */
    public String getBankAccount() {
        return bankAccount;
    }

    /**
     * 设置开户号
     *
     * @param bankAccount 开户号
     */
    public void setBankAccount(String bankAccount) {
        this.bankAccount = bankAccount == null ? null : bankAccount.trim();
    }

    /**
     * 获取开户名称
     *
     * @return bank_account_name - 开户名称
     */
    public String getBankAccountName() {
        return bankAccountName;
    }

    /**
     * 设置开户名称
     *
     * @param bankAccountName 开户名称
     */
    public void setBankAccountName(String bankAccountName) {
        this.bankAccountName = bankAccountName == null ? null : bankAccountName.trim();
    }

    /**
     * 获取银行类型 1：本行 2：他行
     *
     * @return bank_type - 银行类型 1：本行 2：他行
     */
    public String getBankType() {
        return bankType;
    }

    /**
     * 设置银行类型 1：本行 2：他行
     *
     * @param bankType 银行类型 1：本行 2：他行
     */
    public void setBankType(String bankType) {
        this.bankType = bankType == null ? null : bankType.trim();
    }

    /**
     * 获取大小额行号
     *
     * @return bank_cnap_id - 大小额行号
     */
    public String getBankCnapId() {
        return bankCnapId;
    }

    /**
     * 设置大小额行号
     *
     * @param bankCnapId 大小额行号
     */
    public void setBankCnapId(String bankCnapId) {
        this.bankCnapId = bankCnapId == null ? null : bankCnapId.trim();
    }

    /**
     * 获取超级网银行号
     *
     * @return bank_eicon_id - 超级网银行号
     */
    public String getBankEiconId() {
        return bankEiconId;
    }

    /**
     * 设置超级网银行号
     *
     * @param bankEiconId 超级网银行号
     */
    public void setBankEiconId(String bankEiconId) {
        this.bankEiconId = bankEiconId == null ? null : bankEiconId.trim();
    }

    /**
     * 获取图片路径
     *
     * @return pic_url - 图片路径
     */
    public String getPicUrl() {
        return picUrl;
    }

    /**
     * 设置图片路径
     *
     * @param picUrl 图片路径
     */
    public void setPicUrl(String picUrl) {
        this.picUrl = picUrl == null ? null : picUrl.trim();
    }

    /**
     * 获取备注
     *
     * @return mome - 备注
     */
    public String getMome() {
        return mome;
    }

    /**
     * 设置备注
     *
     * @param mome 备注
     */
    public void setMome(String mome) {
        this.mome = mome == null ? null : mome.trim();
    }

    /**
     * 获取联系人
     *
     * @return contact_name - 联系人
     */
    public String getContactName() {
        return contactName;
    }

    /**
     * 设置联系人
     *
     * @param contactName 联系人
     */
    public void setContactName(String contactName) {
        this.contactName = contactName == null ? null : contactName.trim();
    }

    /**
     * 获取电话
     *
     * @return contact_phone - 电话
     */
    public String getContactPhone() {
        return contactPhone;
    }

    /**
     * 设置电话
     *
     * @param contactPhone 电话
     */
    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone == null ? null : contactPhone.trim();
    }

    /**
     * 获取收款开始时间
     *
     * @return start_time - 收款开始时间
     */
    public Date getStartTime() {
        return startTime;
    }

    /**
     * 设置收款开始时间
     *
     * @param startTime 收款开始时间
     */
    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    /**
     * 获取结束收款时间
     *
     * @return end_time - 结束收款时间
     */
    public Date getEndTime() {
        return endTime;
    }

    /**
     * 设置结束收款时间
     *
     * @param endTime 结束收款时间
     */
    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    /**
     * 获取最后验证时间
     *
     * @return last_verify_time - 最后验证时间
     */
    public Date getLastVerifyTime() {
        return lastVerifyTime;
    }

    /**
     * 设置最后验证时间
     *
     * @param lastVerifyTime 最后验证时间
     */
    public void setLastVerifyTime(Date lastVerifyTime) {
        this.lastVerifyTime = lastVerifyTime;
    }

    /**
     * 获取验证通过时间
     *
     * @return pass_verify_time - 验证通过时间
     */
    public Date getPassVerifyTime() {
        return passVerifyTime;
    }

    /**
     * 设置验证通过时间
     *
     * @param passVerifyTime 验证通过时间
     */
    public void setPassVerifyTime(Date passVerifyTime) {
        this.passVerifyTime = passVerifyTime;
    }

    /**
     * 获取关闭时间
     *
     * @return close_time - 关闭时间
     */
    public Date getCloseTime() {
        return closeTime;
    }

    /**
     * 设置关闭时间
     *
     * @param closeTime 关闭时间
     */
    public void setCloseTime(Date closeTime) {
        this.closeTime = closeTime;
    }

    /**
     * @return days
     */
    public String getDays() {
        return days;
    }

    /**
     * @param days
     */
    public void setDays(String days) {
        this.days = days == null ? null : days.trim();
    }

    /**
     * @return ext1
     */
    public String getExt1() {
        return ext1;
    }

    /**
     * @param ext1
     */
    public void setExt1(String ext1) {
        this.ext1 = ext1 == null ? null : ext1.trim();
    }

    /**
     * @return ext2
     */
    public String getExt2() {
        return ext2;
    }

    /**
     * @param ext2
     */
    public void setExt2(String ext2) {
        this.ext2 = ext2 == null ? null : ext2.trim();
    }

    /**
     * @return ext3
     */
    public String getExt3() {
        return ext3;
    }

    /**
     * @param ext3
     */
    public void setExt3(String ext3) {
        this.ext3 = ext3 == null ? null : ext3.trim();
    }

    /**
     * @return ext4
     */
    public String getExt4() {
        return ext4;
    }

    /**
     * @param ext4
     */
    public void setExt4(String ext4) {
        this.ext4 = ext4 == null ? null : ext4.trim();
    }

    /**
     * 获取删除标记
     *
     * @return del_flg - 删除标记
     */
    public Boolean getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标记
     *
     * @param delFlg 删除标记
     */
    public void setDelFlg(Boolean delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建人
     *
     * @return create_user - 创建人
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建人
     *
     * @param createUser 创建人
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取修改人
     *
     * @return update_user - 修改人
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置修改人
     *
     * @param updateUser 修改人
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取修改时间
     *
     * @return update_time - 修改时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置修改时间
     *
     * @param updateTime 修改时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", channelCardId=").append(channelCardId);
        sb.append(", channelCardCode=").append(channelCardCode);
        sb.append(", channelCardStatus=").append(channelCardStatus);
        sb.append(", memberChannelId=").append(memberChannelId);
        sb.append(", memberChannelNo=").append(memberChannelNo);
        sb.append(", memberId=").append(memberId);
        sb.append(", bankBaseName=").append(bankBaseName);
        sb.append(", memberName=").append(memberName);
        sb.append(", bankName=").append(bankName);
        sb.append(", bankAccount=").append(bankAccount);
        sb.append(", bankAccountName=").append(bankAccountName);
        sb.append(", bankType=").append(bankType);
        sb.append(", bankCnapId=").append(bankCnapId);
        sb.append(", bankEiconId=").append(bankEiconId);
        sb.append(", picUrl=").append(picUrl);
        sb.append(", mome=").append(mome);
        sb.append(", contactName=").append(contactName);
        sb.append(", contactPhone=").append(contactPhone);
        sb.append(", startTime=").append(startTime);
        sb.append(", endTime=").append(endTime);
        sb.append(", lastVerifyTime=").append(lastVerifyTime);
        sb.append(", passVerifyTime=").append(passVerifyTime);
        sb.append(", closeTime=").append(closeTime);
        sb.append(", days=").append(days);
        sb.append(", ext1=").append(ext1);
        sb.append(", ext2=").append(ext2);
        sb.append(", ext3=").append(ext3);
        sb.append(", ext4=").append(ext4);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}