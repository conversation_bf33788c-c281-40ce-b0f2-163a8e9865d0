package com.ecommerce.pay.v2.dao.vo;

import jakarta.persistence.Column;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Table(name = "pa_channel_config")
public class ChannelConfig implements Serializable {
    @Id
    @Column(name = "channel_id")
    private String channelId;

    /**
     * 微信小程序、微信扫描支付、微信h5支付
     */
    @Column(name = "channel_name")
    private String channelName;

    /**
     * 渠道图片logo
     */
    @Column(name = "channel_logo")
    private String channelLogo;

    /**
     * 渠道介绍
     */
    @Column(name = "channel_info")
    private String channelInfo;

    /**
     * 商业银行；第三方支付平台；
     */
    @Column(name = "channel_type")
    private String channelType;

    @Column(name = "channel_code")
    private String channelCode;

    /**
     * 不可用，已启用
     */
    @Column(name = "channel_status")
    private Boolean channelStatus;

    /**
     * 账户控制-是否允许充值
     */
    @Column(name = "allow_recharge")
    private Boolean allowRecharge;

    /**
     * 账户控制-是否允许提现
     */
    @Column(name = "allow_draw_money")
    private Boolean allowDrawMoney;

    /**
     * 账户控制-是否允许透支
     */
    @Column(name = "allow_overdraft")
    private Boolean allowOverdraft;

    /**
     * 账户控制-是否允许退款
     */
    @Column(name = "allow_refund")
    private Boolean allowRefund;

    /**
     * 账户控制-是否允许存款
     */
    @Column(name = "allow_deposit")
    private Boolean allowDeposit;

    /**
     * 账户控制-是否允许支付
     */
    @Column(name = "allow_payment")
    private Boolean allowPayment;

    /**
     * 是否允许重试
     */
    @Column(name = "allow_retry")
    private Boolean allowRetry;

    /**
     * 是否允许分账
     */
    @Column(name = "allow_split")
    private Boolean allowSplit;

    /**
     * 支付收款是否分离,如平安见证宝，支付和收款是在分开的虚拟子账户
     */
    @Column(name = "payer_payee_sep")
    private Boolean payerPayeeSep;

    /**
     * 收款方是否需要开通
     */
    @Column(name = "need_payee_reg")
    private Boolean needPayeeReg;

    /**
     * 付款方是否需要开通
     */
    @Column(name = "need_payer_reg")
    private Boolean needPayerReg;

    /**
     * 费率
     */
    @Column(name = "channel_rate")
    private BigDecimal channelRate;

    /**
     * 是否支持app端
     */
    @Column(name = "is_support_app")
    private Boolean isSupportApp;

    /**
     * 是否支持web端
     */
    @Column(name = "is_support_web")
    private Boolean isSupportWeb;

    /**
     * 是否支持小程序
     */
    @Column(name = "is_support_mini")
    private Boolean isSupportMini;

    /**
     * 渠道开通是否需要调用第三方
     */
    @Column(name = "open_need_call")
    private Boolean openNeedCall;

    /**
     * 关闭渠道是否需要调用第三方
     */
    @Column(name = "close_need_call")
    private Boolean closeNeedCall;

    /**
     * 开通url
     */
    @Column(name = "open_url")
    private String openUrl;

    /**
     * 开通url类型
     */
    @Column(name = "open_url_type")
    private String openUrlType;

    /**
     * 支持的退款最大时间数（毫秒）
     */
    @Column(name = "max_refund_time")
    private Long maxRefundTime;

    /**
     * 是否可以多次退款
     */
    @Column(name = "can_many_refund")
    private Boolean canManyRefund;

    /**
     * 渠道支持的最大退款次数
     */
    @Column(name = "max_refund_number")
    private Integer maxRefundNumber;

    /**
     * 是否按卖家创建
     */
    @Column(name = "payer_acct_spec")
    private Boolean payerAcctSpec;

    /**
     * 是否显示余额
     */
    @Column(name = "is_display_balance")
    private Boolean isDisplayBalance;

    /**
     * 渠道支付超时时间
     */
    @Column(name = "pay_timeout")
    private Integer payTimeout;

    /**
     * 是否允许解绑
     */
    @Column(name = "is_allow_unbind")
    private Boolean isAllowUnbind;

    /**
     * 是否允许担保交易
     */
    @Column(name = "is_allow_guarantee")
    private Boolean isAllowGuarantee;

    /**
     * 删除标记
     */
    @Column(name = "del_flg")
    private Boolean delFlg;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    @Column(name = "allow_receive")
    private Boolean allowReceive;

    @Column(name = "if_aggregation")
    private Boolean ifAggregation;

    @Column(name = "receive_channel_id")
    private String receiveChannelId;

    private String ext1;

    private String ext2;

    private String ext3;

    private String ext4;

    private String ext5;

    @Column(name = "if_guid_open")
    private Boolean ifGuidOpen;

    @Column(name = "channel_sequence")
    private Integer channelSequence;

    @Column(name = "need_password")
    private Boolean needPassword;

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * @return channel_id
     */
    public String getChannelId() {
        return channelId;
    }

    /**
     * @param channelId
     */
    public void setChannelId(String channelId) {
        this.channelId = channelId == null ? null : channelId.trim();
    }

    /**
     * 获取微信小程序、微信扫描支付、微信h5支付
     *
     * @return channel_name - 微信小程序、微信扫描支付、微信h5支付
     */
    public String getChannelName() {
        return channelName;
    }

    public Boolean getAllowSplit() {
        return allowSplit;
    }

    public void setAllowSplit(Boolean allowSplit) {
        this.allowSplit = allowSplit;
    }

    /**
     * 设置微信小程序、微信扫描支付、微信h5支付
     *
     * @param channelName 微信小程序、微信扫描支付、微信h5支付
     */
    public void setChannelName(String channelName) {
        this.channelName = channelName == null ? null : channelName.trim();
    }

    /**
     * 获取渠道图片logo
     *
     * @return channel_logo - 渠道图片logo
     */
    public String getChannelLogo() {
        return channelLogo;
    }

    /**
     * 设置渠道图片logo
     *
     * @param channelLogo 渠道图片logo
     */
    public void setChannelLogo(String channelLogo) {
        this.channelLogo = channelLogo == null ? null : channelLogo.trim();
    }

    /**
     * 获取渠道介绍
     *
     * @return channel_info - 渠道介绍
     */
    public String getChannelInfo() {
        return channelInfo;
    }

    /**
     * 设置渠道介绍
     *
     * @param channelInfo 渠道介绍
     */
    public void setChannelInfo(String channelInfo) {
        this.channelInfo = channelInfo == null ? null : channelInfo.trim();
    }

    /**
     * 获取商业银行；第三方支付平台；
     *
     * @return channel_type - 商业银行；第三方支付平台；
     */
    public String getChannelType() {
        return channelType;
    }

    /**
     * 设置商业银行；第三方支付平台；
     *
     * @param channelType 商业银行；第三方支付平台；
     */
    public void setChannelType(String channelType) {
        this.channelType = channelType == null ? null : channelType.trim();
    }

    /**
     * @return channel_code
     */
    public String getChannelCode() {
        return channelCode;
    }

    /**
     * @param channelCode
     */
    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode == null ? null : channelCode.trim();
    }

    /**
     * 获取不可用，已启用
     *
     * @return channel_status - 不可用，已启用
     */
    public Boolean getChannelStatus() {
        return channelStatus;
    }

    /**
     * 设置不可用，已启用
     *
     * @param channelStatus 不可用，已启用
     */
    public void setChannelStatus(Boolean channelStatus) {
        this.channelStatus = channelStatus;
    }

    /**
     * 获取账户控制-是否允许充值
     *
     * @return allow_recharge - 账户控制-是否允许充值
     */
    public Boolean getAllowRecharge() {
        return allowRecharge;
    }

    /**
     * 设置账户控制-是否允许充值
     *
     * @param allowRecharge 账户控制-是否允许充值
     */
    public void setAllowRecharge(Boolean allowRecharge) {
        this.allowRecharge = allowRecharge;
    }

    /**
     * 获取账户控制-是否允许提现
     *
     * @return allow_draw_money - 账户控制-是否允许提现
     */
    public Boolean getAllowDrawMoney() {
        return allowDrawMoney;
    }

    /**
     * 设置账户控制-是否允许提现
     *
     * @param allowDrawMoney 账户控制-是否允许提现
     */
    public void setAllowDrawMoney(Boolean allowDrawMoney) {
        this.allowDrawMoney = allowDrawMoney;
    }

    /**
     * 获取账户控制-是否允许透支
     *
     * @return allow_overdraft - 账户控制-是否允许透支
     */
    public Boolean getAllowOverdraft() {
        return allowOverdraft;
    }

    /**
     * 设置账户控制-是否允许透支
     *
     * @param allowOverdraft 账户控制-是否允许透支
     */
    public void setAllowOverdraft(Boolean allowOverdraft) {
        this.allowOverdraft = allowOverdraft;
    }

    /**
     * 获取账户控制-是否允许退款
     *
     * @return allow_refund - 账户控制-是否允许退款
     */
    public Boolean getAllowRefund() {
        return allowRefund;
    }

    /**
     * 设置账户控制-是否允许退款
     *
     * @param allowRefund 账户控制-是否允许退款
     */
    public void setAllowRefund(Boolean allowRefund) {
        this.allowRefund = allowRefund;
    }

    /**
     * 获取账户控制-是否允许存款
     *
     * @return allow_deposit - 账户控制-是否允许存款
     */
    public Boolean getAllowDeposit() {
        return allowDeposit;
    }

    /**
     * 设置账户控制-是否允许存款
     *
     * @param allowDeposit 账户控制-是否允许存款
     */
    public void setAllowDeposit(Boolean allowDeposit) {
        this.allowDeposit = allowDeposit;
    }

    /**
     * 获取账户控制-是否允许支付
     *
     * @return allow_payment - 账户控制-是否允许支付
     */
    public Boolean getAllowPayment() {
        return allowPayment;
    }

    /**
     * 设置账户控制-是否允许支付
     *
     * @param allowPayment 账户控制-是否允许支付
     */
    public void setAllowPayment(Boolean allowPayment) {
        this.allowPayment = allowPayment;
    }

    /**
     * 获取是否允许重试
     *
     * @return allow_retry - 是否允许重试
     */
    public Boolean getAllowRetry() {
        return allowRetry;
    }

    /**
     * 设置是否允许重试
     *
     * @param allowRetry 是否允许重试
     */
    public void setAllowRetry(Boolean allowRetry) {
        this.allowRetry = allowRetry;
    }

    /**
     * 获取支付收款是否分离,如平安见证宝，支付和收款是在分开的虚拟子账户
     *
     * @return payer_payee_sep - 支付收款是否分离,如平安见证宝，支付和收款是在分开的虚拟子账户
     */
    public Boolean getPayerPayeeSep() {
        return payerPayeeSep;
    }

    /**
     * 设置支付收款是否分离,如平安见证宝，支付和收款是在分开的虚拟子账户
     *
     * @param payerPayeeSep 支付收款是否分离,如平安见证宝，支付和收款是在分开的虚拟子账户
     */
    public void setPayerPayeeSep(Boolean payerPayeeSep) {
        this.payerPayeeSep = payerPayeeSep;
    }

    /**
     * 获取收款方是否需要开通
     *
     * @return need_payee_reg - 收款方是否需要开通
     */
    public Boolean getNeedPayeeReg() {
        return needPayeeReg;
    }

    /**
     * 设置收款方是否需要开通
     *
     * @param needPayeeReg 收款方是否需要开通
     */
    public void setNeedPayeeReg(Boolean needPayeeReg) {
        this.needPayeeReg = needPayeeReg;
    }

    /**
     * 获取付款方是否需要开通
     *
     * @return need_payer_reg - 付款方是否需要开通
     */
    public Boolean getNeedPayerReg() {
        return needPayerReg;
    }

    /**
     * 设置付款方是否需要开通
     *
     * @param needPayerReg 付款方是否需要开通
     */
    public void setNeedPayerReg(Boolean needPayerReg) {
        this.needPayerReg = needPayerReg;
    }

    /**
     * 获取费率
     *
     * @return channel_rate - 费率
     */
    public BigDecimal getChannelRate() {
        return channelRate;
    }

    /**
     * 设置费率
     *
     * @param channelRate 费率
     */
    public void setChannelRate(BigDecimal channelRate) {
        this.channelRate = channelRate;
    }

    /**
     * 获取是否支持app端
     *
     * @return is_support_app - 是否支持app端
     */
    public Boolean getIsSupportApp() {
        return isSupportApp;
    }

    /**
     * 设置是否支持app端
     *
     * @param isSupportApp 是否支持app端
     */
    public void setIsSupportApp(Boolean isSupportApp) {
        this.isSupportApp = isSupportApp;
    }

    /**
     * 获取是否支持web端
     *
     * @return is_support_web - 是否支持web端
     */
    public Boolean getIsSupportWeb() {
        return isSupportWeb;
    }

    /**
     * 设置是否支持web端
     *
     * @param isSupportWeb 是否支持web端
     */
    public void setIsSupportWeb(Boolean isSupportWeb) {
        this.isSupportWeb = isSupportWeb;
    }

    /**
     * 获取是否支持小程序
     *
     * @return is_support_mini - 是否支持小程序
     */
    public Boolean getIsSupportMini() {
        return isSupportMini;
    }

    /**
     * 设置是否支持小程序
     *
     * @param isSupportMini 是否支持小程序
     */
    public void setIsSupportMini(Boolean isSupportMini) {
        this.isSupportMini = isSupportMini;
    }

    /**
     * 获取渠道开通是否需要调用第三方
     *
     * @return open_need_call - 渠道开通是否需要调用第三方
     */
    public Boolean getOpenNeedCall() {
        return openNeedCall;
    }

    /**
     * 设置渠道开通是否需要调用第三方
     *
     * @param openNeedCall 渠道开通是否需要调用第三方
     */
    public void setOpenNeedCall(Boolean openNeedCall) {
        this.openNeedCall = openNeedCall;
    }

    /**
     * 获取关闭渠道是否需要调用第三方
     *
     * @return close_need_call - 关闭渠道是否需要调用第三方
     */
    public Boolean getCloseNeedCall() {
        return closeNeedCall;
    }

    /**
     * 设置关闭渠道是否需要调用第三方
     *
     * @param closeNeedCall 关闭渠道是否需要调用第三方
     */
    public void setCloseNeedCall(Boolean closeNeedCall) {
        this.closeNeedCall = closeNeedCall;
    }

    /**
     * 获取开通url
     *
     * @return open_url - 开通url
     */
    public String getOpenUrl() {
        return openUrl;
    }

    /**
     * 设置开通url
     *
     * @param openUrl 开通url
     */
    public void setOpenUrl(String openUrl) {
        this.openUrl = openUrl == null ? null : openUrl.trim();
    }

    /**
     * 获取开通url类型
     *
     * @return open_url_type - 开通url类型
     */
    public String getOpenUrlType() {
        return openUrlType;
    }

    /**
     * 设置开通url类型
     *
     * @param openUrlType 开通url类型
     */
    public void setOpenUrlType(String openUrlType) {
        this.openUrlType = openUrlType == null ? null : openUrlType.trim();
    }

    /**
     * 获取支持的退款最大时间数（毫秒）
     *
     * @return max_refund_time - 支持的退款最大时间数（毫秒）
     */
    public Long getMaxRefundTime() {
        return maxRefundTime;
    }

    /**
     * 设置支持的退款最大时间数（毫秒）
     *
     * @param maxRefundTime 支持的退款最大时间数（毫秒）
     */
    public void setMaxRefundTime(Long maxRefundTime) {
        this.maxRefundTime = maxRefundTime;
    }

    /**
     * 获取是否可以多次退款
     *
     * @return can_many_refund - 是否可以多次退款
     */
    public Boolean getCanManyRefund() {
        return canManyRefund;
    }

    /**
     * 设置是否可以多次退款
     *
     * @param canManyRefund 是否可以多次退款
     */
    public void setCanManyRefund(Boolean canManyRefund) {
        this.canManyRefund = canManyRefund;
    }

    /**
     * 获取渠道支持的最大退款次数
     *
     * @return max_refund_number - 渠道支持的最大退款次数
     */
    public Integer getMaxRefundNumber() {
        return maxRefundNumber;
    }

    /**
     * 设置渠道支持的最大退款次数
     *
     * @param maxRefundNumber 渠道支持的最大退款次数
     */
    public void setMaxRefundNumber(Integer maxRefundNumber) {
        this.maxRefundNumber = maxRefundNumber;
    }

    /**
     * 获取是否按卖家创建
     *
     * @return payer_acct_spec - 是否按卖家创建
     */
    public Boolean getPayerAcctSpec() {
        return payerAcctSpec;
    }

    /**
     * 设置是否按卖家创建
     *
     * @param payerAcctSpec 是否按卖家创建
     */
    public void setPayerAcctSpec(Boolean payerAcctSpec) {
        this.payerAcctSpec = payerAcctSpec;
    }

    /**
     * 获取是否显示余额
     *
     * @return is_display_balance - 是否显示余额
     */
    public Boolean getIsDisplayBalance() {
        return isDisplayBalance;
    }

    /**
     * 设置是否显示余额
     *
     * @param isDisplayBalance 是否显示余额
     */
    public void setIsDisplayBalance(Boolean isDisplayBalance) {
        this.isDisplayBalance = isDisplayBalance;
    }

    /**
     * 获取渠道支付超时时间
     *
     * @return pay_timeout - 渠道支付超时时间
     */
    public Integer getPayTimeout() {
        return payTimeout;
    }

    /**
     * 设置渠道支付超时时间
     *
     * @param payTimeout 渠道支付超时时间
     */
    public void setPayTimeout(Integer payTimeout) {
        this.payTimeout = payTimeout;
    }

    /**
     * 获取是否允许解绑
     *
     * @return is_allow_unbind - 是否允许解绑
     */
    public Boolean getIsAllowUnbind() {
        return isAllowUnbind;
    }

    /**
     * 设置是否允许解绑
     *
     * @param isAllowUnbind 是否允许解绑
     */
    public void setIsAllowUnbind(Boolean isAllowUnbind) {
        this.isAllowUnbind = isAllowUnbind;
    }

    /**
     * 获取是否允许担保交易
     *
     * @return is_allow_guarantee - 是否允许担保交易
     */
    public Boolean getIsAllowGuarantee() {
        return isAllowGuarantee;
    }

    /**
     * 设置是否允许担保交易
     *
     * @param isAllowGuarantee 是否允许担保交易
     */
    public void setIsAllowGuarantee(Boolean isAllowGuarantee) {
        this.isAllowGuarantee = isAllowGuarantee;
    }

    /**
     * 获取删除标记
     *
     * @return del_flg - 删除标记
     */
    public Boolean getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标记
     *
     * @param delFlg 删除标记
     */
    public void setDelFlg(Boolean delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建人
     *
     * @return create_user - 创建人
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建人
     *
     * @param createUser 创建人
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取修改人
     *
     * @return update_user - 修改人
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置修改人
     *
     * @param updateUser 修改人
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取修改时间
     *
     * @return update_time - 修改时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置修改时间
     *
     * @param updateTime 修改时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * @return allow_receive
     */
    public Boolean getAllowReceive() {
        return allowReceive;
    }

    /**
     * @param allowReceive
     */
    public void setAllowReceive(Boolean allowReceive) {
        this.allowReceive = allowReceive;
    }

    /**
     * @return if_aggregation
     */
    public Boolean getIfAggregation() {
        return ifAggregation;
    }

    /**
     * @param ifAggregation
     */
    public void setIfAggregation(Boolean ifAggregation) {
        this.ifAggregation = ifAggregation;
    }

    /**
     * @return receive_channel_id
     */
    public String getReceiveChannelId() {
        return receiveChannelId;
    }

    /**
     * @param receiveChannelId
     */
    public void setReceiveChannelId(String receiveChannelId) {
        this.receiveChannelId = receiveChannelId == null ? null : receiveChannelId.trim();
    }

    /**
     * @return ext1
     */
    public String getExt1() {
        return ext1;
    }

    /**
     * @param ext1
     */
    public void setExt1(String ext1) {
        this.ext1 = ext1 == null ? null : ext1.trim();
    }

    /**
     * @return ext2
     */
    public String getExt2() {
        return ext2;
    }

    /**
     * @param ext2
     */
    public void setExt2(String ext2) {
        this.ext2 = ext2 == null ? null : ext2.trim();
    }

    /**
     * @return ext3
     */
    public String getExt3() {
        return ext3;
    }

    /**
     * @param ext3
     */
    public void setExt3(String ext3) {
        this.ext3 = ext3 == null ? null : ext3.trim();
    }

    /**
     * @return ext4
     */
    public String getExt4() {
        return ext4;
    }

    /**
     * @param ext4
     */
    public void setExt4(String ext4) {
        this.ext4 = ext4 == null ? null : ext4.trim();
    }

    /**
     * @return ext5
     */
    public String getExt5() {
        return ext5;
    }

    /**
     * @param ext5
     */
    public void setExt5(String ext5) {
        this.ext5 = ext5 == null ? null : ext5.trim();
    }

    /**
     * @return if_guid_open
     */
    public Boolean getIfGuidOpen() {
        return ifGuidOpen;
    }

    /**
     * @param ifGuidOpen
     */
    public void setIfGuidOpen(Boolean ifGuidOpen) {
        this.ifGuidOpen = ifGuidOpen;
    }

    /**
     * @return channel_sequence
     */
    public Integer getChannelSequence() {
        return channelSequence;
    }

    /**
     * @param channelSequence
     */
    public void setChannelSequence(Integer channelSequence) {
        this.channelSequence = channelSequence;
    }

    /**
     * @return need_password
     */
    public Boolean getNeedPassword() {
        return needPassword;
    }

    /**
     * @param needPassword
     */
    public void setNeedPassword(Boolean needPassword) {
        this.needPassword = needPassword;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", channelId=").append(channelId);
        sb.append(", channelName=").append(channelName);
        sb.append(", channelLogo=").append(channelLogo);
        sb.append(", channelInfo=").append(channelInfo);
        sb.append(", channelType=").append(channelType);
        sb.append(", channelCode=").append(channelCode);
        sb.append(", channelStatus=").append(channelStatus);
        sb.append(", allowRecharge=").append(allowRecharge);
        sb.append(", allowDrawMoney=").append(allowDrawMoney);
        sb.append(", allowOverdraft=").append(allowOverdraft);
        sb.append(", allowRefund=").append(allowRefund);
        sb.append(", allowDeposit=").append(allowDeposit);
        sb.append(", allowPayment=").append(allowPayment);
        sb.append(", allowRetry=").append(allowRetry);
        sb.append(", allowSplit=").append(allowSplit);
        sb.append(", payerPayeeSep=").append(payerPayeeSep);
        sb.append(", needPayeeReg=").append(needPayeeReg);
        sb.append(", needPayerReg=").append(needPayerReg);
        sb.append(", channelRate=").append(channelRate);
        sb.append(", isSupportApp=").append(isSupportApp);
        sb.append(", isSupportWeb=").append(isSupportWeb);
        sb.append(", isSupportMini=").append(isSupportMini);
        sb.append(", openNeedCall=").append(openNeedCall);
        sb.append(", closeNeedCall=").append(closeNeedCall);
        sb.append(", openUrl=").append(openUrl);
        sb.append(", openUrlType=").append(openUrlType);
        sb.append(", maxRefundTime=").append(maxRefundTime);
        sb.append(", canManyRefund=").append(canManyRefund);
        sb.append(", maxRefundNumber=").append(maxRefundNumber);
        sb.append(", payerAcctSpec=").append(payerAcctSpec);
        sb.append(", isDisplayBalance=").append(isDisplayBalance);
        sb.append(", payTimeout=").append(payTimeout);
        sb.append(", isAllowUnbind=").append(isAllowUnbind);
        sb.append(", isAllowGuarantee=").append(isAllowGuarantee);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", allowReceive=").append(allowReceive);
        sb.append(", ifAggregation=").append(ifAggregation);
        sb.append(", receiveChannelId=").append(receiveChannelId);
        sb.append(", ext1=").append(ext1);
        sb.append(", ext2=").append(ext2);
        sb.append(", ext3=").append(ext3);
        sb.append(", ext4=").append(ext4);
        sb.append(", ext5=").append(ext5);
        sb.append(", ifGuidOpen=").append(ifGuidOpen);
        sb.append(", channelSequence=").append(channelSequence);
        sb.append(", needPassword=").append(needPassword);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}