package com.ecommerce.pay.v2.dao.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.persistence.Column;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 个体司机运单费用明细表
 */
@Data
@Table(name = "pa_driver_carriage_item")
public class DriverCarriageItem implements Serializable {
    @Serial
    private static final long serialVersionUID = -7513743566681128483L;
    /**
     * id
     */
    @Id
    @Column(name = "driver_carriage_item_id")
    private String driverCarriageItemId;

    /**
     * 会员id
     */
    @Column(name = "member_id")
    private String memberId;

    /**
     * 账号id
     */
    @Column(name = "account_id")
    private String accountId;

    /**
     * 账号代码
     */
    @Column(name = "account_code")
    private String accountCode;

    /**
     * 姓名
     */
    @Column(name = "real_name")
    private String realName;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 记账日期yyyy-MM-dd
     */
    private String day;

    @Schema(description = "运单ID")
    @Column(name = "waybill_id")
    private String waybillId;
    /**
     * 运单号
     */
    @Column(name = "waybill_num")
    private String waybillNum;

    /**
     * 运距
     */
    private BigDecimal distance;

    /**
     * 运量
     */
    private BigDecimal quantity;

    /**
     * 运费
     */
    @Column(name = "logistics_amount")
    private BigDecimal logisticsAmount;

    /**
     * 签收时间
     */
    @Column(name = "sign_time")
    private Date signTime;

    /**
     * 可提现时间
     */
    @Column(name = "withdraw_future_date")
    private Date withdrawFutureDate;

    /**
     * 发货点名称
     */
    @Column(name = "warehouse_id")
    private String warehouseId;

    /**
     * 发货点名称
     */
    @Column(name = "warehouse_name")
    private String warehouseName;

    /**
     * 收货地址
     */
    @Column(name = "receive_address")
    private String receiveAddress;

    /**
     * 支付金额
     */
    @Column(name = "actual_pay_amount")
    private BigDecimal actualPayAmount;

    /**
     * 支付状态
     */
    @Column(name = "pay_status")
    private String payStatus;

    /**
     * 计划付款时间
     */
    @Column(name = "scheduled_pay_time")
    private Date scheduledPayTime;

    /**
     * 实际付款时间
     */
    @Column(name = "actual_pay_time")
    private Date actualPayTime;

    /**
     * 支付单据号
     */
    @Column(name = "pay_number")
    private String payNumber;

    @Schema(description = "收款方名称")
    private String recipient;

    /**
     * 收款账号
     */
    @Schema(description = "收款账号")
    @Column(name = "receipt_account")
    private String receiptAccount;
    /**
     * 支付系统tradeNo
     */
    @Column(name = "trade_bill_no")
    private String tradeBillNo;

    /**
     * 删除标识
     */
    @Column(name = "del_flg")
    private Boolean delFlg;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}