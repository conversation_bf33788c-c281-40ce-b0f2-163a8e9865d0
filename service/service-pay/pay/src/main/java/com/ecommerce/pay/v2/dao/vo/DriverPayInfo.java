package com.ecommerce.pay.v2.dao.vo;

import lombok.Data;

import jakarta.persistence.Column;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 个体司机保证金缴纳、退还、提现支付相关操作的支付单表，
 * 相当于pa_bill_payment + pa_bill_refund + pa_bill_cash_withdrawal
 */
@Data
@Table(name = "pa_driver_payinfo")
public class DriverPayInfo implements Serializable {
    @Serial
    private static final long serialVersionUID = 7412877931494289524L;
    /**
     * id
     */
    @Id
    @Column(name = "driver_payinfo_id")
    private String driverPayInfoId;

    /**
     * 原支付单据号(退款时才有)
     */
    @Column(name = "origin_driver_payinfo_id")
    private String originDriverPayInfoId;

    /**
     * 会员id
     */
    @Column(name = "member_id")
    private String memberId;

    /**
     * 账号id
     */
    @Column(name = "account_id")
    private String accountId;

    /**
     * 账号代码
     */
    @Column(name = "account_code")
    private String accountCode;

    /**
     * 账号名
     */
    @Column(name = "account_name")
    private String accountName;

    /**
     * 姓名
     */
    @Column(name = "real_name")
    private String realName;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 支付单类型
     */
    @Column(name = "payinfo_type")
    private String payinfoType;
    /**
     * 详细分类
     */
    private String subject;

    /**
     * 操作时间
     */
    @Column(name = "operate_time")
    private Date operateTime;

    /**
     * 资金到账时间
     */
    @Column(name = "receipt_time")
    private Date receiptTime;

    /**
     * 支付完成时间(实际支付时间)
     */
    @Column(name = "pay_time")
    private Date payTime;

    /**
     * 支付在什么时间点超时
     */
    @Column(name = "expire_time")
    private Date expireTime;

    /**
     * 记账日(yyyy-MM-dd)
     */
    private String day;

    /**
     * 支付单code
     */
    @Column(name = "payinfo_code")
    private String payinfoCode;

    /**
     * 交易金额(保证金缴纳或退还金额)
     */
    @Column(name = "payinfo_amount")
    private BigDecimal payinfoAmount;

    /**
     * 支付状态
     */
    @Column(name = "payinfo_status")
    private String payinfoStatus;
    /**
     * 支付我方流水号
     */
    @Column(name = "mct_order_no")
    private String mctOrderNo;
    /**
     * 支付银联方流水号（支付系统tradeNo）
     */
    @Column(name = "trade_bill_no")
    private String tradeBillNo;

    /**
     * 保证金是否已经退还 0否 1是
     */
    private Boolean refunded;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标识
     */
    @Column(name = "del_flg")
    private Boolean delFlg;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}