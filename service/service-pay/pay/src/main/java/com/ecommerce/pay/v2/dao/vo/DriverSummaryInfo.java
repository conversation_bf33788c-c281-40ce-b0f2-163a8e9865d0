package com.ecommerce.pay.v2.dao.vo;

import lombok.Data;

import jakarta.persistence.Column;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 个体司机汇总信息表，不包含身份证、车辆、司机信息，包含钱包、保证金缴纳情况、总的收入、提现等信息
 */
@Data
@Table(name = "pa_driver_summary_info")
public class DriverSummaryInfo implements Serializable {
    @Serial
    private static final long serialVersionUID = -6570927761769609625L;
    /**
     * id
     */
    @Id
    @Column(name = "driver_summary_info_id")
    private String driverSummaryInfoId;

    /**
     * 会员id
     */
    @Column(name = "member_id")
    private String memberId;

    /**
     * 账号id
     */
    @Column(name = "account_id")
    private String accountId;

    /**
     * 账号代码
     */
    @Column(name = "account_code")
    private String accountCode;

    /**
     * 姓名
     */
    @Column(name = "real_name")
    private String realName;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 钱包id
     */
    @Column(name = "wallet_id")
    private String walletId;
    /**
     * 钱包开通状态
     */
    @Column(name = "wallet_open_flg")
    private Boolean walletOpenFlg;

    /**
     * 保证金支付状态
     */
    @Column(name = "deposit_pay_status")
    private Boolean depositPayStatus;

    /**
     * 保证金缴纳金额
     */
    @Column(name = "deposit_amount")
    private BigDecimal depositAmount;

    /**
     * 保证金是否已经退还
     */
    private Boolean refunded;

    /**
     * 银行卡
     */
    @Column(name = "bank_card")
    private String bankCard;

    /**
     * 运单量
     */
    @Column(name = "waybill_count")
    private Integer waybillCount;

    /**
     * 运距
     */
    private BigDecimal distance;

    /**
     * 运量
     */
    private BigDecimal quantity;

    /**
     * 运费(收入)
     */
    private BigDecimal income;

    /**
     * 累计提现
     */
    private BigDecimal withdraw;

    /**
     * 可提现收入
     */
    @Column(name = "withdraw_allow_amount")
    private BigDecimal withdrawAllowAmount;

    /**
     * 当前不可提现收入(未来可提现收入)
     */
    @Column(name = "withdraw_future_amount")
    private BigDecimal withdrawFutureAmount;

    @Column(name = "deposit_payinfo_id")
    private String depositPayInfoId;

    /**
     * 删除标识
     */
    @Column(name = "del_flg")
    private Boolean delFlg;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}