package com.ecommerce.pay.v2.dao.vo;

import lombok.Data;

import jakarta.persistence.Column;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import java.io.Serial;
import java.io.Serializable;

@Data
@Table(name = "pa_gnete_bank_info")
public class GneteBankInfo implements Serializable {
    @Serial
    private static final long serialVersionUID = -5555849952032365615L;
    @Id
    private Integer id;

    /**
     * 省名称
     */
    @Column(name = "province_name")
    private String provinceName;

    /**
     * 省编码
     */
    @Column(name = "province_code")
    private String provinceCode;

    /**
     * 市名称
     */
    @Column(name = "city_name")
    private String cityName;

    /**
     * 市编码
     */
    @Column(name = "city_code")
    private String cityCode;

    /**
     * 区名称
     */
    @Column(name = "district_name")
    private String districtName;

    /**
     * 区编码
     */
    @Column(name = "district_code")
    private String districtCode;

    /**
     * 3位行号
     */
    @Column(name = "bank_cls_code")
    private Integer bankClsCode;

    /**
     * 行名称（行别）
     */
    @Column(name = "bank_name")
    private String bankName;

    /**
     * 开户行行号
     */
    @Column(name = "bank_no")
    private String bankNo;

    /**
     * 开户行名称
     */
    @Column(name = "bank_detail_name")
    private String bankDetailName;

    /**
     * 生效日期
     */
    @Column(name = "effective_date")
    private String effectiveDate;

    /**
     * 失效日期
     */
    @Column(name = "expiry_date")
    private String expiryDate;

}