package com.ecommerce.pay.v2.dao.vo;

import lombok.Data;

import jakarta.persistence.Column;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import java.io.Serial;
import java.io.Serializable;

@Data
@Table(name = "pa_gnete_split_rule")
public class GneteSplitRule implements Serializable {
    @Serial
    private static final long serialVersionUID = 6655386904620439187L;
    /**
     * 分账规则ID-分账规则的唯一标识
     */
    @Id
    private String id;

    /**
     * 商户编号
     */
    @Column(name = "mer_no")
    private String merNo;

    /**
     * 商户名称
     */
    @Column(name = "mer_name")
    private String merName;

    /**
     * 商户钱包ID
     */
    @Column(name = "mer_walletId")
    private String platformWalletid;

    /**
     * 业务类型 12-好支付，04-担保支付
     */
    @Column(name = "biz_type")
    private String bizType;

    /**
     * 分账起始金额 单位：分
     */
    @Column(name = "start_amt")
    private Long startAmt;

    /**
     * 状态 01新建（待提交）、02提交（待初审）、03生效、04失效
     */
    private String status;

    /**
     * 审批状态 01新建（待提交）、02提交（待初审）、03初审不通过、04待复审、05生效、06失效提交（待初审）、07失效初审不通过、08失效待复审、09失效
     */
    @Column(name = "approve_status")
    private String approveStatus;

    /**
     * 分账资金清算周期 T+N日
     */
    @Column(name = "sett_cyc")
    private String settCyc;

    /**
     * 是否指令分账 0否（默认）、1是
     */
    @Column(name = "is_instr")
    private String isInstr;

}