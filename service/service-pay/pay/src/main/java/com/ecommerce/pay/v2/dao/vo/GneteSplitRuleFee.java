package com.ecommerce.pay.v2.dao.vo;

import lombok.Data;

import jakarta.persistence.Column;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import java.io.Serial;
import java.io.Serializable;

@Data
@Table(name = "pa_gnete_split_rule_fee")
public class GneteSplitRuleFee implements Serializable {
    @Serial
    private static final long serialVersionUID = -2003832117469836364L;
    /**
     * 分账清算规则id
     */
    @Id
    private String id;

    /**
     * 分账规则id
     */
    @Column(name = "profit_sharing_rule_id")
    private String profitSharingRuleId;

    /**
     * 分账钱包ID
     */
    @Column(name = "profit_sharing_wallet_id")
    private String profitSharingWalletId;

    /**
     * 清算比例 分账方式为01按比例分账，该字段才有意义
     */
    @Column(name = "fee_rate")
    private Long feeRate;

    /**
     * 单笔金额下限 单位：分，分账方式为01按比例分账，该字段才有意义
     */
    @Column(name = "rate_min_fee_amt")
    private Long rateMinFeeAmt;

    /**
     * 单笔金额上限 单位：分，分账方式为01按比例分账，该字段才有意义
     */
    @Column(name = "rate_max_fee_amt")
    private Long rateMaxFeeAmt;

    /**
     * 固定清算金额 单位：分，分账方式为02按金额分账，该字段才有意义
     */
    @Column(name = "fixed_sett_amt")
    private Long fixedSettAmt;

    /**
     * 清算方式 01按比例分账、02按金额分账
     */
    @Column(name = "fee_rule_type")
    private String feeRuleType;

}