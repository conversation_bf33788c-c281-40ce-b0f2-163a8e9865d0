package com.ecommerce.pay.v2.dao.vo;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import jakarta.persistence.*;

@Table(name = "pa_invoice_account")
public class InvoiceAccount implements Serializable {
    /**
     * 开票账户Id
     */
    @Id
    @Column(name = "invoice_account_id")
    private String invoiceAccountId;

    /**
     * 开票方平台账户ID
     */
    @Column(name = "seller_id")
    private String sellerId;

    /**
     * 开票方平台账户名称
     */
    @Column(name = "seller_name")
    private String sellerName;

    /**
     * 纳税人识别码
     */
    @Column(name = "tax_no")
    private String taxNo;

    /**
     * 账户名
     */
    @Column(name = "account_name")
    private String accountName;

    /**
     * 账户密码
     */
    @Column(name = "account_password")
    private String accountPassword;

    /**
     * 开票申请处理方式 1：自动审核 2：手动审核
     */
    @Column(name = "process_mode")
    private String processMode;

    /**
     * 开票方发票抬头
     */
    @Column(name = "seller_title")
    private String sellerTitle;

    /**
     * 开票方发票抬头
     */
    @Column(name = "seller_address")
    private String sellerAddress;

    /**
     * 开票方联系电话
     */
    @Column(name = "seller_phone")
    private String sellerPhone;

    /**
     * 卖家开户行
     */
    @Column(name = "bank_account_name")
    private String bankAccountName;

    /**
     * 卖家开户行账号
     */
    @Column(name = "bank_account_no")
    private String bankAccountNo;

    /**
     * 访问令牌
     */
    @Column(name = "access_token")
    private String accessToken;

    /**
     * 刷新令牌
     */
    @Column(name = "refresh_token")
    private String refreshToken;

    /**
     * 令牌类型
     */
    @Column(name = "token_type")
    private String tokenType;

    /**
     * 令牌作用域
     */
    private String scope;

    /**
     * token失效时间
     */
    @Column(name = "expire_time")
    private Date expireTime;

    /**
     * 开票人
     */
    private String drawer;

    /**
     * 收款人
     */
    private String payee;

    /**
     * 复核人
     */
    private String reviewer;

    /**
     * 删除标识
     */
    @Column(name = "del_flg")
    private Boolean delFlg;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 获取开票账户Id
     *
     * @return invoice_account_id - 开票账户Id
     */
    public String getInvoiceAccountId() {
        return invoiceAccountId;
    }

    /**
     * 设置开票账户Id
     *
     * @param invoiceAccountId 开票账户Id
     */
    public void setInvoiceAccountId(String invoiceAccountId) {
        this.invoiceAccountId = invoiceAccountId == null ? null : invoiceAccountId.trim();
    }

    /**
     * 获取开票方平台账户ID
     *
     * @return seller_id - 开票方平台账户ID
     */
    public String getSellerId() {
        return sellerId;
    }

    /**
     * 设置开票方平台账户ID
     *
     * @param sellerId 开票方平台账户ID
     */
    public void setSellerId(String sellerId) {
        this.sellerId = sellerId == null ? null : sellerId.trim();
    }

    /**
     * 获取开票方平台账户名称
     *
     * @return seller_name - 开票方平台账户名称
     */
    public String getSellerName() {
        return sellerName;
    }

    /**
     * 设置开票方平台账户名称
     *
     * @param sellerName 开票方平台账户名称
     */
    public void setSellerName(String sellerName) {
        this.sellerName = sellerName == null ? null : sellerName.trim();
    }

    /**
     * 获取纳税人识别码
     *
     * @return tax_no - 纳税人识别码
     */
    public String getTaxNo() {
        return taxNo;
    }

    /**
     * 设置纳税人识别码
     *
     * @param taxNo 纳税人识别码
     */
    public void setTaxNo(String taxNo) {
        this.taxNo = taxNo == null ? null : taxNo.trim();
    }

    /**
     * 获取账户名
     *
     * @return account_name - 账户名
     */
    public String getAccountName() {
        return accountName;
    }

    /**
     * 设置账户名
     *
     * @param accountName 账户名
     */
    public void setAccountName(String accountName) {
        this.accountName = accountName == null ? null : accountName.trim();
    }

    /**
     * 获取账户密码
     *
     * @return account_password - 账户密码
     */
    public String getAccountPassword() {
        return accountPassword;
    }

    /**
     * 设置账户密码
     *
     * @param accountPassword 账户密码
     */
    public void setAccountPassword(String accountPassword) {
        this.accountPassword = accountPassword == null ? null : accountPassword.trim();
    }

    /**
     * 获取开票申请处理方式 1：自动审核 2：手动审核
     *
     * @return process_mode - 开票申请处理方式 1：自动审核 2：手动审核
     */
    public String getProcessMode() {
        return processMode;
    }

    /**
     * 设置开票申请处理方式 1：自动审核 2：手动审核
     *
     * @param processMode 开票申请处理方式 1：自动审核 2：手动审核
     */
    public void setProcessMode(String processMode) {
        this.processMode = processMode == null ? null : processMode.trim();
    }

    /**
     * 获取开票方发票抬头
     *
     * @return seller_title - 开票方发票抬头
     */
    public String getSellerTitle() {
        return sellerTitle;
    }

    /**
     * 设置开票方发票抬头
     *
     * @param sellerTitle 开票方发票抬头
     */
    public void setSellerTitle(String sellerTitle) {
        this.sellerTitle = sellerTitle == null ? null : sellerTitle.trim();
    }

    /**
     * 获取开票方发票抬头
     *
     * @return seller_address - 开票方发票抬头
     */
    public String getSellerAddress() {
        return sellerAddress;
    }

    /**
     * 设置开票方发票抬头
     *
     * @param sellerAddress 开票方发票抬头
     */
    public void setSellerAddress(String sellerAddress) {
        this.sellerAddress = sellerAddress == null ? null : sellerAddress.trim();
    }

    /**
     * 获取开票方联系电话
     *
     * @return seller_phone - 开票方联系电话
     */
    public String getSellerPhone() {
        return sellerPhone;
    }

    /**
     * 设置开票方联系电话
     *
     * @param sellerPhone 开票方联系电话
     */
    public void setSellerPhone(String sellerPhone) {
        this.sellerPhone = sellerPhone == null ? null : sellerPhone.trim();
    }

    /**
     * 获取卖家开户行
     *
     * @return bank_account_name - 卖家开户行
     */
    public String getBankAccountName() {
        return bankAccountName;
    }

    /**
     * 设置卖家开户行
     *
     * @param bankAccountName 卖家开户行
     */
    public void setBankAccountName(String bankAccountName) {
        this.bankAccountName = bankAccountName == null ? null : bankAccountName.trim();
    }

    /**
     * 获取卖家开户行账号
     *
     * @return bank_account_no - 卖家开户行账号
     */
    public String getBankAccountNo() {
        return bankAccountNo;
    }

    /**
     * 设置卖家开户行账号
     *
     * @param bankAccountNo 卖家开户行账号
     */
    public void setBankAccountNo(String bankAccountNo) {
        this.bankAccountNo = bankAccountNo == null ? null : bankAccountNo.trim();
    }

    /**
     * 获取访问令牌
     *
     * @return access_token - 访问令牌
     */
    public String getAccessToken() {
        return accessToken;
    }

    /**
     * 设置访问令牌
     *
     * @param accessToken 访问令牌
     */
    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken == null ? null : accessToken.trim();
    }

    /**
     * 获取刷新令牌
     *
     * @return refresh_token - 刷新令牌
     */
    public String getRefreshToken() {
        return refreshToken;
    }

    /**
     * 设置刷新令牌
     *
     * @param refreshToken 刷新令牌
     */
    public void setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken == null ? null : refreshToken.trim();
    }

    /**
     * 获取令牌类型
     *
     * @return token_type - 令牌类型
     */
    public String getTokenType() {
        return tokenType;
    }

    /**
     * 设置令牌类型
     *
     * @param tokenType 令牌类型
     */
    public void setTokenType(String tokenType) {
        this.tokenType = tokenType == null ? null : tokenType.trim();
    }

    /**
     * 获取令牌作用域
     *
     * @return scope - 令牌作用域
     */
    public String getScope() {
        return scope;
    }

    /**
     * 设置令牌作用域
     *
     * @param scope 令牌作用域
     */
    public void setScope(String scope) {
        this.scope = scope == null ? null : scope.trim();
    }

    /**
     * 获取token失效时间
     *
     * @return expire_time - token失效时间
     */
    public Date getExpireTime() {
        return expireTime;
    }

    /**
     * 设置token失效时间
     *
     * @param expireTime token失效时间
     */
    public void setExpireTime(Date expireTime) {
        this.expireTime = expireTime;
    }

    /**
     * 获取开票人
     *
     * @return drawer - 开票人
     */
    public String getDrawer() {
        return drawer;
    }

    /**
     * 设置开票人
     *
     * @param drawer 开票人
     */
    public void setDrawer(String drawer) {
        this.drawer = drawer == null ? null : drawer.trim();
    }

    /**
     * 获取收款人
     *
     * @return payee - 收款人
     */
    public String getPayee() {
        return payee;
    }

    /**
     * 设置收款人
     *
     * @param payee 收款人
     */
    public void setPayee(String payee) {
        this.payee = payee == null ? null : payee.trim();
    }

    /**
     * 获取复核人
     *
     * @return reviewer - 复核人
     */
    public String getReviewer() {
        return reviewer;
    }

    /**
     * 设置复核人
     *
     * @param reviewer 复核人
     */
    public void setReviewer(String reviewer) {
        this.reviewer = reviewer == null ? null : reviewer.trim();
    }

    /**
     * 获取删除标识
     *
     * @return del_flg - 删除标识
     */
    public Boolean getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标识
     *
     * @param delFlg 删除标识
     */
    public void setDelFlg(Boolean delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", invoiceAccountId=").append(invoiceAccountId);
        sb.append(", sellerId=").append(sellerId);
        sb.append(", sellerName=").append(sellerName);
        sb.append(", taxNo=").append(taxNo);
        sb.append(", accountName=").append(accountName);
        sb.append(", accountPassword=").append(accountPassword);
        sb.append(", processMode=").append(processMode);
        sb.append(", sellerTitle=").append(sellerTitle);
        sb.append(", sellerAddress=").append(sellerAddress);
        sb.append(", sellerPhone=").append(sellerPhone);
        sb.append(", bankAccountName=").append(bankAccountName);
        sb.append(", bankAccountNo=").append(bankAccountNo);
        sb.append(", accessToken=").append(accessToken);
        sb.append(", refreshToken=").append(refreshToken);
        sb.append(", tokenType=").append(tokenType);
        sb.append(", scope=").append(scope);
        sb.append(", expireTime=").append(expireTime);
        sb.append(", drawer=").append(drawer);
        sb.append(", payee=").append(payee);
        sb.append(", reviewer=").append(reviewer);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}