package com.ecommerce.pay.v2.dao.vo;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import jakarta.persistence.*;

@Table(name = "pa_invoice_apply")
public class InvoiceApply implements Serializable {
    /**
     * 申请Id
     */
    @Id
    @Column(name = "apply_id")
    private String applyId;

    /**
     * 申请编号
     */
    @Column(name = "apply_num")
    private String applyNum;

    /**
     * 业务类型：1:交易发票 2:运输发票 3:交易含运输发票 4:交易项与运输项混合发票
     */
    @Column(name = "biz_type")
    private String bizType;

    /**
     * 开票状态 1：已申请 2:审核驳回 3: 审核通过 4:开票完成 5：已红冲
     */
    private String status;

    /**
     * 开票方用户ID
     */
    @Column(name = "buyer_id")
    private String buyerId;

    /**
     * 开票方名称
     */
    @Column(name = "buyer_name")
    private String buyerName;

    /**
     * 开票方平台账户ID
     */
    @Column(name = "seller_id")
    private String sellerId;

    /**
     * 开票方平台账户名称
     */
    @Column(name = "seller_name")
    private String sellerName;

    /**
     * 驳回原因
     */
    @Column(name = "fail_reason")
    private String failReason;

    /**
     * 红冲原因
     */
    @Column(name = "revoke_reason")
    private String revokeReason;

    /**
     * 是否为专票
     */
    @Column(name = "is_special")
    private Byte isSpecial;

    /**
     * 审核通过时间
     */
    @Column(name = "audit_time")
    private Date auditTime;

    /**
     * 红冲时间
     */
    @Column(name = "revoke_time")
    private Date revokeTime;

    /**
     * 申请年份
     */
    @Column(name = "apply_year")
    private String applyYear;

    /**
     * 申请月份
     */
    @Column(name = "apply_month")
    private String applyMonth;

    /**
     * 完成年
     */
    @Column(name = "finish_year")
    private String finishYear;

    /**
     * 完成月
     */
    @Column(name = "finish_month")
    private String finishMonth;

    /**
     * 商品金额
     */
    @Column(name = "product_amount")
    private BigDecimal productAmount;

    /**
     * 物流费金额
     */
    @Column(name = "logistics_amount")
    private BigDecimal logisticsAmount;

    /**
     * 申请总金额
     */
    @Column(name = "total_amount")
    private BigDecimal totalAmount;

    /**
     * 删除标识
     */
    @Column(name = "del_flg")
    private Byte delFlg;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 获取申请Id
     *
     * @return apply_id - 申请Id
     */
    public String getApplyId() {
        return applyId;
    }

    /**
     * 设置申请Id
     *
     * @param applyId 申请Id
     */
    public void setApplyId(String applyId) {
        this.applyId = applyId == null ? null : applyId.trim();
    }

    /**
     * 获取申请编号
     *
     * @return apply_num - 申请编号
     */
    public String getApplyNum() {
        return applyNum;
    }

    /**
     * 设置申请编号
     *
     * @param applyNum 申请编号
     */
    public void setApplyNum(String applyNum) {
        this.applyNum = applyNum == null ? null : applyNum.trim();
    }

    /**
     * 获取业务类型：1:交易发票 2:运输发票 3:交易含运输发票 4:交易项与运输项混合发票
     *
     * @return biz_type - 业务类型：1:交易发票 2:运输发票 3:交易含运输发票 4:交易项与运输项混合发票
     */
    public String getBizType() {
        return bizType;
    }

    /**
     * 设置业务类型：1:交易发票 2:运输发票 3:交易含运输发票 4:交易项与运输项混合发票
     *
     * @param bizType 业务类型：1:交易发票 2:运输发票 3:交易含运输发票 4:交易项与运输项混合发票
     */
    public void setBizType(String bizType) {
        this.bizType = bizType == null ? null : bizType.trim();
    }

    /**
     * 获取开票状态 1：已申请 2:审核驳回 3: 审核通过 4:开票完成 5：已红冲
     *
     * @return status - 开票状态 1：已申请 2:审核驳回 3: 审核通过 4:开票完成 5：已红冲
     */
    public String getStatus() {
        return status;
    }

    /**
     * 设置开票状态 1：已申请 2:审核驳回 3: 审核通过 4:开票完成 5：已红冲
     *
     * @param status 开票状态 1：已申请 2:审核驳回 3: 审核通过 4:开票完成 5：已红冲
     */
    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    /**
     * 获取开票方用户ID
     *
     * @return buyer_id - 开票方用户ID
     */
    public String getBuyerId() {
        return buyerId;
    }

    /**
     * 设置开票方用户ID
     *
     * @param buyerId 开票方用户ID
     */
    public void setBuyerId(String buyerId) {
        this.buyerId = buyerId == null ? null : buyerId.trim();
    }

    /**
     * 获取开票方名称
     *
     * @return buyer_name - 开票方名称
     */
    public String getBuyerName() {
        return buyerName;
    }

    /**
     * 设置开票方名称
     *
     * @param buyerName 开票方名称
     */
    public void setBuyerName(String buyerName) {
        this.buyerName = buyerName == null ? null : buyerName.trim();
    }

    /**
     * 获取开票方平台账户ID
     *
     * @return seller_id - 开票方平台账户ID
     */
    public String getSellerId() {
        return sellerId;
    }

    /**
     * 设置开票方平台账户ID
     *
     * @param sellerId 开票方平台账户ID
     */
    public void setSellerId(String sellerId) {
        this.sellerId = sellerId == null ? null : sellerId.trim();
    }

    /**
     * 获取开票方平台账户名称
     *
     * @return seller_name - 开票方平台账户名称
     */
    public String getSellerName() {
        return sellerName;
    }

    /**
     * 设置开票方平台账户名称
     *
     * @param sellerName 开票方平台账户名称
     */
    public void setSellerName(String sellerName) {
        this.sellerName = sellerName == null ? null : sellerName.trim();
    }

    /**
     * 获取驳回原因
     *
     * @return fail_reason - 驳回原因
     */
    public String getFailReason() {
        return failReason;
    }

    /**
     * 设置驳回原因
     *
     * @param failReason 驳回原因
     */
    public void setFailReason(String failReason) {
        this.failReason = failReason == null ? null : failReason.trim();
    }

    public Byte getIsSpecial() {
        return isSpecial;
    }

    public void setIsSpecial(Byte isSpecial) {
        this.isSpecial = isSpecial;
    }

    /**
     * 获取红冲原因
     *
     * @return revoke_reason - 红冲原因
     */
    public String getRevokeReason() {
        return revokeReason;
    }

    /**
     * 设置红冲原因
     *
     * @param revokeReason 红冲原因
     */
    public void setRevokeReason(String revokeReason) {
        this.revokeReason = revokeReason == null ? null : revokeReason.trim();
    }

    /**
     * 获取审核通过时间
     *
     * @return audit_time - 审核通过时间
     */
    public Date getAuditTime() {
        return auditTime;
    }

    /**
     * 设置审核通过时间
     *
     * @param auditTime 审核通过时间
     */
    public void setAuditTime(Date auditTime) {
        this.auditTime = auditTime;
    }

    /**
     * 获取红冲时间
     *
     * @return revoke_time - 红冲时间
     */
    public Date getRevokeTime() {
        return revokeTime;
    }

    /**
     * 设置红冲时间
     *
     * @param revokeTime 红冲时间
     */
    public void setRevokeTime(Date revokeTime) {
        this.revokeTime = revokeTime;
    }

    /**
     * 获取申请年份
     *
     * @return apply_year - 申请年份
     */
    public String getApplyYear() {
        return applyYear;
    }

    /**
     * 设置申请年份
     *
     * @param applyYear 申请年份
     */
    public void setApplyYear(String applyYear) {
        this.applyYear = applyYear == null ? null : applyYear.trim();
    }

    /**
     * 获取申请月份
     *
     * @return apply_month - 申请月份
     */
    public String getApplyMonth() {
        return applyMonth;
    }

    /**
     * 设置申请月份
     *
     * @param applyMonth 申请月份
     */
    public void setApplyMonth(String applyMonth) {
        this.applyMonth = applyMonth == null ? null : applyMonth.trim();
    }

    /**
     * 获取完成年
     *
     * @return finish_year - 完成年
     */
    public String getFinishYear() {
        return finishYear;
    }

    /**
     * 设置完成年
     *
     * @param finishYear 完成年
     */
    public void setFinishYear(String finishYear) {
        this.finishYear = finishYear == null ? null : finishYear.trim();
    }

    /**
     * 获取完成月
     *
     * @return finish_month - 完成月
     */
    public String getFinishMonth() {
        return finishMonth;
    }

    /**
     * 设置完成月
     *
     * @param finishMonth 完成月
     */
    public void setFinishMonth(String finishMonth) {
        this.finishMonth = finishMonth == null ? null : finishMonth.trim();
    }

    /**
     * 获取商品金额
     *
     * @return product_amount - 商品金额
     */
    public BigDecimal getProductAmount() {
        return productAmount;
    }

    /**
     * 设置商品金额
     *
     * @param productAmount 商品金额
     */
    public void setProductAmount(BigDecimal productAmount) {
        this.productAmount = productAmount;
    }

    /**
     * 获取物流费金额
     *
     * @return logistics_amount - 物流费金额
     */
    public BigDecimal getLogisticsAmount() {
        return logisticsAmount;
    }

    /**
     * 设置物流费金额
     *
     * @param logisticsAmount 物流费金额
     */
    public void setLogisticsAmount(BigDecimal logisticsAmount) {
        this.logisticsAmount = logisticsAmount;
    }

    /**
     * 获取申请总金额
     *
     * @return total_amount - 申请总金额
     */
    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    /**
     * 设置申请总金额
     *
     * @param totalAmount 申请总金额
     */
    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    /**
     * 获取删除标识
     *
     * @return del_flg - 删除标识
     */
    public Byte getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标识
     *
     * @param delFlg 删除标识
     */
    public void setDelFlg(Byte delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", applyId=").append(applyId);
        sb.append(", applyNum=").append(applyNum);
        sb.append(", bizType=").append(bizType);
        sb.append(", status=").append(status);
        sb.append(", buyerId=").append(buyerId);
        sb.append(", buyerName=").append(buyerName);
        sb.append(", sellerId=").append(sellerId);
        sb.append(", sellerName=").append(sellerName);
        sb.append(", failReason=").append(failReason);
        sb.append(", revokeReason=").append(revokeReason);
        sb.append(", isSpecial=").append(isSpecial);
        sb.append(", auditTime=").append(auditTime);
        sb.append(", revokeTime=").append(revokeTime);
        sb.append(", applyYear=").append(applyYear);
        sb.append(", applyMonth=").append(applyMonth);
        sb.append(", finishYear=").append(finishYear);
        sb.append(", finishMonth=").append(finishMonth);
        sb.append(", productAmount=").append(productAmount);
        sb.append(", logisticsAmount=").append(logisticsAmount);
        sb.append(", totalAmount=").append(totalAmount);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}