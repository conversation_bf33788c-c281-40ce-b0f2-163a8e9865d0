package com.ecommerce.pay.v2.dao.vo;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import jakarta.persistence.*;

@Table(name = "pa_invoice_apply_item")
public class InvoiceApplyItem implements Serializable {
    /**
     * 申请明细Id
     */
    @Id
    @Column(name = "apply_item_id")
    private String applyItemId;

    /**
     * 申请Id
     */
    @Column(name = "apply_id")
    private String applyId;

    /**
     * 业务单据号
     */
    @Column(name = "biz_no")
    private String bizNo;

    /**
     * 业务类型：1:交易发票 2:运输发票 3:交易运输混合开票
     */
    @Column(name = "biz_type")
    private String bizType;

    /**
     * 商品编码
     */
    @Column(name = "item_code")
    private String itemCode;

    /**
     * 商品名
     */
    @Column(name = "item_name")
    private String itemName;

    /**
     * 商品数量
     */
    @Column(name = "item_num")
    private BigDecimal itemNum;

    /**
     * 商品单价
     */
    @Column(name = "item_price")
    private BigDecimal itemPrice;

    /**
     * 计量单位
     */
    @Column(name = "item_unit")
    private String itemUnit;

    /**
     * 商品型号
     */
    @Column(name = "item_mode")
    private String itemMode;

    /**
     * 商品金额
     */
    private BigDecimal amount;

    /**
     * 物流费用
     */
    @Column(name = "logistics_amount")
    private BigDecimal logisticsAmount;

    /**
     * 开票标识 0:未开 1:已开
     */
    @Column(name = "open_flag")
    private Byte openFlag;

    /**
     * 删除标识
     */
    @Column(name = "del_flg")
    private Byte delFlg;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 附加内容 json串存储
     */
    @Column(name = "extra_content")
    private String extraContent;

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 获取申请明细Id
     *
     * @return apply_item_id - 申请明细Id
     */
    public String getApplyItemId() {
        return applyItemId;
    }

    /**
     * 设置申请明细Id
     *
     * @param applyItemId 申请明细Id
     */
    public void setApplyItemId(String applyItemId) {
        this.applyItemId = applyItemId == null ? null : applyItemId.trim();
    }

    /**
     * 获取申请Id
     *
     * @return apply_id - 申请Id
     */
    public String getApplyId() {
        return applyId;
    }

    /**
     * 设置申请Id
     *
     * @param applyId 申请Id
     */
    public void setApplyId(String applyId) {
        this.applyId = applyId == null ? null : applyId.trim();
    }

    /**
     * 获取业务单据号
     *
     * @return biz_no - 业务单据号
     */
    public String getBizNo() {
        return bizNo;
    }

    /**
     * 设置业务单据号
     *
     * @param bizNo 业务单据号
     */
    public void setBizNo(String bizNo) {
        this.bizNo = bizNo == null ? null : bizNo.trim();
    }

    /**
     * 获取业务类型：1:交易发票 2:运输发票 3:交易运输混合开票
     *
     * @return biz_type - 业务类型：1:交易发票 2:运输发票 3:交易运输混合开票
     */
    public String getBizType() {
        return bizType;
    }

    /**
     * 设置业务类型：1:交易发票 2:运输发票 3:交易运输混合开票
     *
     * @param bizType 业务类型：1:交易发票 2:运输发票 3:交易运输混合开票
     */
    public void setBizType(String bizType) {
        this.bizType = bizType == null ? null : bizType.trim();
    }

    /**
     * 获取商品编码
     *
     * @return item_code - 商品编码
     */
    public String getItemCode() {
        return itemCode;
    }

    /**
     * 设置商品编码
     *
     * @param itemCode 商品编码
     */
    public void setItemCode(String itemCode) {
        this.itemCode = itemCode == null ? null : itemCode.trim();
    }

    /**
     * 获取商品名
     *
     * @return item_name - 商品名
     */
    public String getItemName() {
        return itemName;
    }

    /**
     * 设置商品名
     *
     * @param itemName 商品名
     */
    public void setItemName(String itemName) {
        this.itemName = itemName == null ? null : itemName.trim();
    }

    /**
     * 获取商品数量
     *
     * @return item_num - 商品数量
     */
    public BigDecimal getItemNum() {
        return itemNum;
    }

    /**
     * 设置商品数量
     *
     * @param itemNum 商品数量
     */
    public void setItemNum(BigDecimal itemNum) {
        this.itemNum = itemNum;
    }

    /**
     * 获取商品单价
     *
     * @return item_price - 商品单价
     */
    public BigDecimal getItemPrice() {
        return itemPrice;
    }

    /**
     * 设置商品单价
     *
     * @param itemPrice 商品单价
     */
    public void setItemPrice(BigDecimal itemPrice) {
        this.itemPrice = itemPrice;
    }

    /**
     * 获取计量单位
     *
     * @return item_unit - 计量单位
     */
    public String getItemUnit() {
        return itemUnit;
    }

    /**
     * 设置计量单位
     *
     * @param itemUnit 计量单位
     */
    public void setItemUnit(String itemUnit) {
        this.itemUnit = itemUnit == null ? null : itemUnit.trim();
    }

    /**
     * 获取商品型号
     *
     * @return item_mode - 商品型号
     */
    public String getItemMode() {
        return itemMode;
    }

    /**
     * 设置商品型号
     *
     * @param itemMode 商品型号
     */
    public void setItemMode(String itemMode) {
        this.itemMode = itemMode == null ? null : itemMode.trim();
    }

    /**
     * 获取商品金额
     *
     * @return amount - 商品金额
     */
    public BigDecimal getAmount() {
        return amount;
    }

    /**
     * 设置商品金额
     *
     * @param amount 商品金额
     */
    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    /**
     * 获取物流费用
     *
     * @return logistics_amount - 物流费用
     */
    public BigDecimal getLogisticsAmount() {
        return logisticsAmount;
    }

    /**
     * 设置物流费用
     *
     * @param logisticsAmount 物流费用
     */
    public void setLogisticsAmount(BigDecimal logisticsAmount) {
        this.logisticsAmount = logisticsAmount;
    }

    /**
     * 获取开票标识 0:未开 1:已开
     *
     * @return open_flag - 开票标识 0:未开 1:已开
     */
    public Byte getOpenFlag() {
        return openFlag;
    }

    /**
     * 设置开票标识 0:未开 1:已开
     *
     * @param openFlag 开票标识 0:未开 1:已开
     */
    public void setOpenFlag(Byte openFlag) {
        this.openFlag = openFlag;
    }

    /**
     * 获取删除标识
     *
     * @return del_flg - 删除标识
     */
    public Byte getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标识
     *
     * @param delFlg 删除标识
     */
    public void setDelFlg(Byte delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取附加内容 json串存储
     *
     * @return extra_content - 附加内容 json串存储
     */
    public String getExtraContent() {
        return extraContent;
    }

    /**
     * 设置附加内容 json串存储
     *
     * @param extraContent 附加内容 json串存储
     */
    public void setExtraContent(String extraContent) {
        this.extraContent = extraContent == null ? null : extraContent.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", applyItemId=").append(applyItemId);
        sb.append(", applyId=").append(applyId);
        sb.append(", bizNo=").append(bizNo);
        sb.append(", bizType=").append(bizType);
        sb.append(", itemCode=").append(itemCode);
        sb.append(", itemName=").append(itemName);
        sb.append(", itemNum=").append(itemNum);
        sb.append(", itemPrice=").append(itemPrice);
        sb.append(", itemUnit=").append(itemUnit);
        sb.append(", itemMode=").append(itemMode);
        sb.append(", amount=").append(amount);
        sb.append(", logisticsAmount=").append(logisticsAmount);
        sb.append(", openFlag=").append(openFlag);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", extraContent=").append(extraContent);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}