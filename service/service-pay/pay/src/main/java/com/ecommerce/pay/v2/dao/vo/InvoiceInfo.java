package com.ecommerce.pay.v2.dao.vo;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import jakarta.persistence.*;

@Table(name = "pa_invoice_info")
public class InvoiceInfo implements Serializable {
    /**
     * 申请信息Id
     */
    @Id
    @Column(name = "info_id")
    private String infoId;

    /**
     * 申请Id
     */
    @Column(name = "apply_id")
    private String applyId;

    /**
     * 是否为专票
     */
    @Column(name = "is_special")
    private Byte isSpecial;

    /**
     * 发票抬头
     */
    @Column(name = "invoice_title")
    private String invoiceTitle;

    /**
     * 开票方类型 01:企业 02：机关事业单位 03:个人 04:其他
     */
    @Column(name = "buyer_type")
    private String buyerType;

    /**
     * 开票方税号
     */
    @Column(name = "buyer_tax_no")
    private String buyerTaxNo;

    /**
     * 开票方手机号
     */
    @Column(name = "buyer_mobile")
    private String buyerMobile;

    /**
     * 开票方电话
     */
    @Column(name = "buyer_telephone")
    private String buyerTelephone;

    /**
     * 开票方邮箱
     */
    @Column(name = "buyer_email")
    private String buyerEmail;

    /**
     * 开票方地址
     */
    @Column(name = "buyer_address")
    private String buyerAddress;

    /**
     * 开票方省份
     */
    @Column(name = "buyer_province")
    private String buyerProvince;

    /**
     * 开票方开户行名称
     */
    @Column(name = "buyer_bank_name")
    private String buyerBankName;

    /**
     * 开票方开户行账号
     */
    @Column(name = "buyer_bank_num")
    private String buyerBankNum;

    /**
     * 分机号
     */
    @Column(name = "machine_no")
    private String machineNo;

    /**
     * 卖家开户行账户
     */
    @Column(name = "seller_bank_account")
    private String sellerBankAccount;

    /**
     * 开票人
     */
    private String drawer;

    /**
     * 收款人
     */
    private String payee;

    /**
     * 总税额
     */
    @Column(name = "total_tax_amount")
    private BigDecimal totalTaxAmount;

    /**
     * 申请总金额
     */
    @Column(name = "total_amount")
    private BigDecimal totalAmount;

    /**
     * 复核人
     */
    private String reviewer;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 开票订单编号
     */
    @Column(name = "tax_order_no")
    private String taxOrderNo;

    /**
     * 开票流水号
     */
    @Column(name = "serial_no")
    private String serialNo;

    /**
     * 开票日期
     */
    @Column(name = "invoice_date")
    private Date invoiceDate;

    /**
     * 发票代码
     */
    @Column(name = "invoice_code")
    private String invoiceCode;

    /**
     * 发票号码
     */
    @Column(name = "invoice_num")
    private String invoiceNum;

    /**
     * 发票地址
     */
    @Column(name = "invoice_url")
    private String invoiceUrl;

    /**
     * 删除标识
     */
    @Column(name = "del_flg")
    private Byte delFlg;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 获取申请信息Id
     *
     * @return info_id - 申请信息Id
     */
    public String getInfoId() {
        return infoId;
    }

    /**
     * 设置申请信息Id
     *
     * @param infoId 申请信息Id
     */
    public void setInfoId(String infoId) {
        this.infoId = infoId == null ? null : infoId.trim();
    }

    /**
     * 获取申请Id
     *
     * @return apply_id - 申请Id
     */
    public String getApplyId() {
        return applyId;
    }

    /**
     * 设置申请Id
     *
     * @param applyId 申请Id
     */
    public void setApplyId(String applyId) {
        this.applyId = applyId == null ? null : applyId.trim();
    }

    public Byte getIsSpecial() {
        return isSpecial;
    }

    public void setIsSpecial(Byte isSpecial) {
        this.isSpecial = isSpecial;
    }

    /**
     * 获取发票抬头
     *
     * @return invoice_title - 发票抬头
     */
    public String getInvoiceTitle() {
        return invoiceTitle;
    }

    /**
     * 设置发票抬头
     *
     * @param invoiceTitle 发票抬头
     */
    public void setInvoiceTitle(String invoiceTitle) {
        this.invoiceTitle = invoiceTitle == null ? null : invoiceTitle.trim();
    }

    /**
     * 获取开票方类型 01:企业 02：机关事业单位 03:个人 04:其他
     *
     * @return buyer_type - 开票方类型 01:企业 02：机关事业单位 03:个人 04:其他
     */
    public String getBuyerType() {
        return buyerType;
    }

    /**
     * 设置开票方类型 01:企业 02：机关事业单位 03:个人 04:其他
     *
     * @param buyerType 开票方类型 01:企业 02：机关事业单位 03:个人 04:其他
     */
    public void setBuyerType(String buyerType) {
        this.buyerType = buyerType == null ? null : buyerType.trim();
    }

    /**
     * 获取开票方税号
     *
     * @return buyer_tax_no - 开票方税号
     */
    public String getBuyerTaxNo() {
        return buyerTaxNo;
    }

    /**
     * 设置开票方税号
     *
     * @param buyerTaxNo 开票方税号
     */
    public void setBuyerTaxNo(String buyerTaxNo) {
        this.buyerTaxNo = buyerTaxNo == null ? null : buyerTaxNo.trim();
    }

    /**
     * 获取开票方手机号
     *
     * @return buyer_mobile - 开票方手机号
     */
    public String getBuyerMobile() {
        return buyerMobile;
    }

    /**
     * 设置开票方手机号
     *
     * @param buyerMobile 开票方手机号
     */
    public void setBuyerMobile(String buyerMobile) {
        this.buyerMobile = buyerMobile == null ? null : buyerMobile.trim();
    }

    /**
     * 获取开票方电话
     *
     * @return buyer_telephone - 开票方电话
     */
    public String getBuyerTelephone() {
        return buyerTelephone;
    }

    /**
     * 设置开票方电话
     *
     * @param buyerTelephone 开票方电话
     */
    public void setBuyerTelephone(String buyerTelephone) {
        this.buyerTelephone = buyerTelephone == null ? null : buyerTelephone.trim();
    }

    /**
     * 获取开票方邮箱
     *
     * @return buyer_email - 开票方邮箱
     */
    public String getBuyerEmail() {
        return buyerEmail;
    }

    /**
     * 设置开票方邮箱
     *
     * @param buyerEmail 开票方邮箱
     */
    public void setBuyerEmail(String buyerEmail) {
        this.buyerEmail = buyerEmail == null ? null : buyerEmail.trim();
    }

    /**
     * 获取开票方地址
     *
     * @return buyer_address - 开票方地址
     */
    public String getBuyerAddress() {
        return buyerAddress;
    }

    /**
     * 设置开票方地址
     *
     * @param buyerAddress 开票方地址
     */
    public void setBuyerAddress(String buyerAddress) {
        this.buyerAddress = buyerAddress == null ? null : buyerAddress.trim();
    }

    /**
     * 获取开票方省份
     *
     * @return buyer_province - 开票方省份
     */
    public String getBuyerProvince() {
        return buyerProvince;
    }

    /**
     * 设置开票方省份
     *
     * @param buyerProvince 开票方省份
     */
    public void setBuyerProvince(String buyerProvince) {
        this.buyerProvince = buyerProvince == null ? null : buyerProvince.trim();
    }

    /**
     * 获取开票方开户行名称
     *
     * @return buyer_bank_name - 开票方开户行名称
     */
    public String getBuyerBankName() {
        return buyerBankName;
    }

    /**
     * 设置开票方开户行名称
     *
     * @param buyerBankName 开票方开户行名称
     */
    public void setBuyerBankName(String buyerBankName) {
        this.buyerBankName = buyerBankName == null ? null : buyerBankName.trim();
    }

    /**
     * 获取开票方开户行账号
     *
     * @return buyer_bank_num - 开票方开户行账号
     */
    public String getBuyerBankNum() {
        return buyerBankNum;
    }

    /**
     * 设置开票方开户行账号
     *
     * @param buyerBankNum 开票方开户行账号
     */
    public void setBuyerBankNum(String buyerBankNum) {
        this.buyerBankNum = buyerBankNum == null ? null : buyerBankNum.trim();
    }

    /**
     * 获取分机号
     *
     * @return machine_no - 分机号
     */
    public String getMachineNo() {
        return machineNo;
    }

    /**
     * 设置分机号
     *
     * @param machineNo 分机号
     */
    public void setMachineNo(String machineNo) {
        this.machineNo = machineNo == null ? null : machineNo.trim();
    }

    /**
     * 获取卖家开户行账户
     *
     * @return seller_bank_account - 卖家开户行账户
     */
    public String getSellerBankAccount() {
        return sellerBankAccount;
    }

    /**
     * 设置卖家开户行账户
     *
     * @param sellerBankAccount 卖家开户行账户
     */
    public void setSellerBankAccount(String sellerBankAccount) {
        this.sellerBankAccount = sellerBankAccount == null ? null : sellerBankAccount.trim();
    }

    /**
     * 获取开票人
     *
     * @return drawer - 开票人
     */
    public String getDrawer() {
        return drawer;
    }

    /**
     * 设置开票人
     *
     * @param drawer 开票人
     */
    public void setDrawer(String drawer) {
        this.drawer = drawer == null ? null : drawer.trim();
    }

    /**
     * 获取收款人
     *
     * @return payee - 收款人
     */
    public String getPayee() {
        return payee;
    }

    /**
     * 设置收款人
     *
     * @param payee 收款人
     */
    public void setPayee(String payee) {
        this.payee = payee == null ? null : payee.trim();
    }

    /**
     * 获取总税额
     *
     * @return total_tax_amount - 总税额
     */
    public BigDecimal getTotalTaxAmount() {
        return totalTaxAmount;
    }

    /**
     * 设置总税额
     *
     * @param totalTaxAmount 总税额
     */
    public void setTotalTaxAmount(BigDecimal totalTaxAmount) {
        this.totalTaxAmount = totalTaxAmount;
    }

    /**
     * 获取申请总金额
     *
     * @return total_amount - 申请总金额
     */
    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    /**
     * 设置申请总金额
     *
     * @param totalAmount 申请总金额
     */
    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    /**
     * 获取复核人
     *
     * @return reviewer - 复核人
     */
    public String getReviewer() {
        return reviewer;
    }

    /**
     * 设置复核人
     *
     * @param reviewer 复核人
     */
    public void setReviewer(String reviewer) {
        this.reviewer = reviewer == null ? null : reviewer.trim();
    }

    /**
     * 获取备注
     *
     * @return remarks - 备注
     */
    public String getRemarks() {
        return remarks;
    }

    /**
     * 设置备注
     *
     * @param remarks 备注
     */
    public void setRemarks(String remarks) {
        this.remarks = remarks == null ? null : remarks.trim();
    }

    /**
     * 获取开票订单编号
     *
     * @return tax_order_no - 开票订单编号
     */
    public String getTaxOrderNo() {
        return taxOrderNo;
    }

    /**
     * 设置开票订单编号
     *
     * @param taxOrderNo 开票订单编号
     */
    public void setTaxOrderNo(String taxOrderNo) {
        this.taxOrderNo = taxOrderNo == null ? null : taxOrderNo.trim();
    }

    /**
     * 获取开票流水号
     *
     * @return serial_no - 开票流水号
     */
    public String getSerialNo() {
        return serialNo;
    }

    /**
     * 设置开票流水号
     *
     * @param serialNo 开票流水号
     */
    public void setSerialNo(String serialNo) {
        this.serialNo = serialNo == null ? null : serialNo.trim();
    }

    /**
     * 获取开票日期
     *
     * @return invoice_date - 开票日期
     */
    public Date getInvoiceDate() {
        return invoiceDate;
    }

    /**
     * 设置开票日期
     *
     * @param invoiceDate 开票日期
     */
    public void setInvoiceDate(Date invoiceDate) {
        this.invoiceDate = invoiceDate;
    }

    /**
     * 获取发票代码
     *
     * @return invoice_code - 发票代码
     */
    public String getInvoiceCode() {
        return invoiceCode;
    }

    /**
     * 设置发票代码
     *
     * @param invoiceCode 发票代码
     */
    public void setInvoiceCode(String invoiceCode) {
        this.invoiceCode = invoiceCode == null ? null : invoiceCode.trim();
    }

    /**
     * 获取发票号码
     *
     * @return invoice_num - 发票号码
     */
    public String getInvoiceNum() {
        return invoiceNum;
    }

    /**
     * 设置发票号码
     *
     * @param invoiceNum 发票号码
     */
    public void setInvoiceNum(String invoiceNum) {
        this.invoiceNum = invoiceNum == null ? null : invoiceNum.trim();
    }

    /**
     * 获取发票地址
     *
     * @return invoice_url - 发票地址
     */
    public String getInvoiceUrl() {
        return invoiceUrl;
    }

    /**
     * 设置发票地址
     *
     * @param invoiceUrl 发票地址
     */
    public void setInvoiceUrl(String invoiceUrl) {
        this.invoiceUrl = invoiceUrl == null ? null : invoiceUrl.trim();
    }

    /**
     * 获取删除标识
     *
     * @return del_flg - 删除标识
     */
    public Byte getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标识
     *
     * @param delFlg 删除标识
     */
    public void setDelFlg(Byte delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", infoId=").append(infoId);
        sb.append(", applyId=").append(applyId);
        sb.append(", isSpecial=").append(isSpecial);
        sb.append(", invoiceTitle=").append(invoiceTitle);
        sb.append(", buyerType=").append(buyerType);
        sb.append(", buyerTaxNo=").append(buyerTaxNo);
        sb.append(", buyerMobile=").append(buyerMobile);
        sb.append(", buyerTelephone=").append(buyerTelephone);
        sb.append(", buyerEmail=").append(buyerEmail);
        sb.append(", buyerAddress=").append(buyerAddress);
        sb.append(", buyerProvince=").append(buyerProvince);
        sb.append(", buyerBankName=").append(buyerBankName);
        sb.append(", buyerBankNum=").append(buyerBankNum);
        sb.append(", machineNo=").append(machineNo);
        sb.append(", sellerBankAccount=").append(sellerBankAccount);
        sb.append(", drawer=").append(drawer);
        sb.append(", payee=").append(payee);
        sb.append(", totalTaxAmount=").append(totalTaxAmount);
        sb.append(", totalAmount=").append(totalAmount);
        sb.append(", reviewer=").append(reviewer);
        sb.append(", remarks=").append(remarks);
        sb.append(", taxOrderNo=").append(taxOrderNo);
        sb.append(", serialNo=").append(serialNo);
        sb.append(", invoiceDate=").append(invoiceDate);
        sb.append(", invoiceCode=").append(invoiceCode);
        sb.append(", invoiceNum=").append(invoiceNum);
        sb.append(", invoiceUrl=").append(invoiceUrl);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}