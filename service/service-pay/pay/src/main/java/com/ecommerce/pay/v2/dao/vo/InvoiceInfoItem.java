package com.ecommerce.pay.v2.dao.vo;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import jakarta.persistence.*;

@Table(name = "pa_invoice_info_item")
public class InvoiceInfoItem implements Serializable {
    /**
     * 申请明细Id
     */
    @Id
    @Column(name = "info_item_id")
    private String infoItemId;

    /**
     * 申请信息Id
     */
    @Column(name = "info_id")
    private String infoId;

    /**
     * 业务类型：1:交易发票 2:运输发票 3:交易含运输发票
     */
    @Column(name = "biz_type")
    private String bizType;

    /**
     * 发票属性 0:正常 1:折扣行 2:被折扣行
     */
    @Column(name = "invoice_nature")
    private Integer invoiceNature;

    /**
     * 商品名
     */
    @Column(name = "item_name")
    private String itemName;

    /**
     * 商品数量
     */
    @Column(name = "item_num")
    private BigDecimal itemNum;

    /**
     * 商品单价
     */
    @Column(name = "item_price")
    private String itemPrice;

    /**
     * 不含税单价
     */
    @Column(name = "before_tax_price")
    private String beforeTaxPrice;

    /**
     * 税控分类编码，不足19位后面补0
     */
    @Column(name = "item_tax_code")
    private String itemTaxCode;

    /**
     * 计量单位
     */
    @Column(name = "item_unit")
    private String itemUnit;

    /**
     * 商品型号
     */
    @Column(name = "item_mode")
    private String itemMode;

    /**
     * 税费
     */
    private BigDecimal tax;

    /**
     * 商品金额
     */
    private BigDecimal amount;

    /**
     * 不含税金额
     */
    @Column(name = "before_tax_amount")
    private BigDecimal beforeTaxAmount;

    /**
     * 是否含税 0:不含税 1:含税
     */
    @Column(name = "tax_included")
    private String taxIncluded;

    /**
     * 税率
     */
    @Column(name = "tax_rate")
    private BigDecimal taxRate;

    private String taxName;

    /**
     * 税率标识 空：非零税率 1:免税 2:不征税 3:普通零税率
     */
    @Column(name = "tax_flag")
    private String taxFlag;

    /**
     * 0:否 1:是
     */
    @Column(name = "is_preferential")
    private String isPreferential;

    /**
     * 优惠内容
     */
    @Column(name = "preferential_content")
    private String preferentialContent;

    /**
     * 删除标识
     */
    @Column(name = "del_flg")
    private Byte delFlg;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    @Serial
    private static final long serialVersionUID = 1L;

    public String getTaxName() {
        return taxName;
    }

    public void setTaxName(String taxName) {
        this.taxName = taxName;
    }

    /**
     * 获取申请明细Id
     *
     * @return info_item_id - 申请明细Id
     */
    public String getInfoItemId() {
        return infoItemId;
    }

    /**
     * 设置申请明细Id
     *
     * @param infoItemId 申请明细Id
     */
    public void setInfoItemId(String infoItemId) {
        this.infoItemId = infoItemId == null ? null : infoItemId.trim();
    }

    /**
     * 获取申请信息Id
     *
     * @return info_id - 申请信息Id
     */
    public String getInfoId() {
        return infoId;
    }

    /**
     * 设置申请信息Id
     *
     * @param infoId 申请信息Id
     */
    public void setInfoId(String infoId) {
        this.infoId = infoId == null ? null : infoId.trim();
    }

    /**
     * 获取业务类型：1:交易发票 2:运输发票 3:交易含运输发票
     *
     * @return biz_type - 业务类型：1:交易发票 2:运输发票 3:交易含运输发票
     */
    public String getBizType() {
        return bizType;
    }

    /**
     * 设置业务类型：1:交易发票 2:运输发票 3:交易含运输发票
     *
     * @param bizType 业务类型：1:交易发票 2:运输发票 3:交易含运输发票
     */
    public void setBizType(String bizType) {
        this.bizType = bizType == null ? null : bizType.trim();
    }

    /**
     * 获取发票属性 0:正常 1:折扣行 2:被折扣行
     *
     * @return invoice_nature - 发票属性 0:正常 1:折扣行 2:被折扣行
     */
    public Integer getInvoiceNature() {
        return invoiceNature;
    }

    /**
     * 设置发票属性 0:正常 1:折扣行 2:被折扣行
     *
     * @param invoiceNature 发票属性 0:正常 1:折扣行 2:被折扣行
     */
    public void setInvoiceNature(Integer invoiceNature) {
        this.invoiceNature = invoiceNature;
    }

    /**
     * 获取商品名
     *
     * @return item_name - 商品名
     */
    public String getItemName() {
        return itemName;
    }

    /**
     * 设置商品名
     *
     * @param itemName 商品名
     */
    public void setItemName(String itemName) {
        this.itemName = itemName == null ? null : itemName.trim();
    }

    /**
     * 获取商品数量
     *
     * @return item_num - 商品数量
     */
    public BigDecimal getItemNum() {
        return itemNum;
    }

    /**
     * 设置商品数量
     *
     * @param itemNum 商品数量
     */
    public void setItemNum(BigDecimal itemNum) {
        this.itemNum = itemNum;
    }

    /**
     * 获取商品单价
     *
     * @return item_price - 商品单价
     */
    public String getItemPrice() {
        return itemPrice;
    }

    /**
     * 设置商品单价
     *
     * @param itemPrice 商品单价
     */
    public void setItemPrice(String itemPrice) {
        this.itemPrice = itemPrice == null ? null : itemPrice.trim();
    }

    /**
     * 获取不含税单价
     *
     * @return before_tax_price - 不含税单价
     */
    public String getBeforeTaxPrice() {
        return beforeTaxPrice;
    }

    /**
     * 设置不含税单价
     *
     * @param beforeTaxPrice 不含税单价
     */
    public void setBeforeTaxPrice(String beforeTaxPrice) {
        this.beforeTaxPrice = beforeTaxPrice == null ? null : beforeTaxPrice.trim();
    }

    /**
     * 获取税控分类编码，不足19位后面补0
     *
     * @return item_tax_code - 税控分类编码，不足19位后面补0
     */
    public String getItemTaxCode() {
        return itemTaxCode;
    }

    /**
     * 设置税控分类编码，不足19位后面补0
     *
     * @param itemTaxCode 税控分类编码，不足19位后面补0
     */
    public void setItemTaxCode(String itemTaxCode) {
        this.itemTaxCode = itemTaxCode == null ? null : itemTaxCode.trim();
    }

    /**
     * 获取计量单位
     *
     * @return item_unit - 计量单位
     */
    public String getItemUnit() {
        return itemUnit;
    }

    /**
     * 设置计量单位
     *
     * @param itemUnit 计量单位
     */
    public void setItemUnit(String itemUnit) {
        this.itemUnit = itemUnit == null ? null : itemUnit.trim();
    }

    /**
     * 获取商品型号
     *
     * @return item_mode - 商品型号
     */
    public String getItemMode() {
        return itemMode;
    }

    /**
     * 设置商品型号
     *
     * @param itemMode 商品型号
     */
    public void setItemMode(String itemMode) {
        this.itemMode = itemMode == null ? null : itemMode.trim();
    }

    /**
     * 获取税费
     *
     * @return tax - 税费
     */
    public BigDecimal getTax() {
        return tax;
    }

    /**
     * 设置税费
     *
     * @param tax 税费
     */
    public void setTax(BigDecimal tax) {
        this.tax = tax;
    }

    /**
     * 获取商品金额
     *
     * @return amount - 商品金额
     */
    public BigDecimal getAmount() {
        return amount;
    }

    /**
     * 设置商品金额
     *
     * @param amount 商品金额
     */
    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    /**
     * 获取不含税金额
     *
     * @return before_tax_amount - 不含税金额
     */
    public BigDecimal getBeforeTaxAmount() {
        return beforeTaxAmount;
    }

    /**
     * 设置不含税金额
     *
     * @param beforeTaxAmount 不含税金额
     */
    public void setBeforeTaxAmount(BigDecimal beforeTaxAmount) {
        this.beforeTaxAmount = beforeTaxAmount;
    }

    /**
     * 获取是否含税 0:不含税 1:含税
     *
     * @return tax_included - 是否含税 0:不含税 1:含税
     */
    public String getTaxIncluded() {
        return taxIncluded;
    }

    /**
     * 设置是否含税 0:不含税 1:含税
     *
     * @param taxIncluded 是否含税 0:不含税 1:含税
     */
    public void setTaxIncluded(String taxIncluded) {
        this.taxIncluded = taxIncluded == null ? null : taxIncluded.trim();
    }

    /**
     * 获取税率
     *
     * @return tax_rate - 税率
     */
    public BigDecimal getTaxRate() {
        return taxRate;
    }

    /**
     * 设置税率
     *
     * @param taxRate 税率
     */
    public void setTaxRate(BigDecimal taxRate) {
        this.taxRate = taxRate;
    }

    /**
     * 获取税率标识 空：非零税率 1:免税 2:不征税 3:普通零税率
     *
     * @return tax_flag - 税率标识 空：非零税率 1:免税 2:不征税 3:普通零税率
     */
    public String getTaxFlag() {
        return taxFlag;
    }

    /**
     * 设置税率标识 空：非零税率 1:免税 2:不征税 3:普通零税率
     *
     * @param taxFlag 税率标识 空：非零税率 1:免税 2:不征税 3:普通零税率
     */
    public void setTaxFlag(String taxFlag) {
        this.taxFlag = taxFlag == null ? null : taxFlag.trim();
    }

    /**
     * 获取0:否 1:是
     *
     * @return is_preferential - 0:否 1:是
     */
    public String getIsPreferential() {
        return isPreferential;
    }

    /**
     * 设置0:否 1:是
     *
     * @param isPreferential 0:否 1:是
     */
    public void setIsPreferential(String isPreferential) {
        this.isPreferential = isPreferential == null ? null : isPreferential.trim();
    }

    /**
     * 获取优惠内容
     *
     * @return preferential_content - 优惠内容
     */
    public String getPreferentialContent() {
        return preferentialContent;
    }

    /**
     * 设置优惠内容
     *
     * @param preferentialContent 优惠内容
     */
    public void setPreferentialContent(String preferentialContent) {
        this.preferentialContent = preferentialContent == null ? null : preferentialContent.trim();
    }

    /**
     * 获取删除标识
     *
     * @return del_flg - 删除标识
     */
    public Byte getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标识
     *
     * @param delFlg 删除标识
     */
    public void setDelFlg(Byte delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", infoItemId=").append(infoItemId);
        sb.append(", infoId=").append(infoId);
        sb.append(", bizType=").append(bizType);
        sb.append(", invoiceNature=").append(invoiceNature);
        sb.append(", itemName=").append(itemName);
        sb.append(", itemNum=").append(itemNum);
        sb.append(", itemPrice=").append(itemPrice);
        sb.append(", beforeTaxPrice=").append(beforeTaxPrice);
        sb.append(", itemTaxCode=").append(itemTaxCode);
        sb.append(", itemUnit=").append(itemUnit);
        sb.append(", itemMode=").append(itemMode);
        sb.append(", tax=").append(tax);
        sb.append(", amount=").append(amount);
        sb.append(", beforeTaxAmount=").append(beforeTaxAmount);
        sb.append(", taxIncluded=").append(taxIncluded);
        sb.append(", taxName=").append(taxName);
        sb.append(", taxRate=").append(taxRate);
        sb.append(", taxFlag=").append(taxFlag);
        sb.append(", isPreferential=").append(isPreferential);
        sb.append(", preferentialContent=").append(preferentialContent);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}