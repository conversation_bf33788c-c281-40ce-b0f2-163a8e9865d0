package com.ecommerce.pay.v2.dao.vo;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import jakarta.persistence.*;

@Table(name = "pa_invoice_notify_info")
public class InvoiceNotifyInfo implements Serializable {
    /**
     * 主键
     */
    @Id
    @Column(name = "notify_id")
    private String notifyId;

    /**
     * 申请ID
     */
    @Column(name = "apply_id")
    private String applyId;

    /**
     * 业务号
     */
    @Column(name = "biz_no")
    private String bizNo;

    @Column(name = "biz_type")
    private String bizType;

    /**
     * 商品编码
     */
    @Column(name = "item_code")
    private String itemCode;

    /**
     * 申请状态
     */
    @Column(name = "apply_status")
    private String applyStatus;

    /**
     * 原因
     */
    private String reason;

    /**
     * 通知状态
     */
    @Column(name = "notify_status")
    private String notifyStatus;

    /**
     * 通知描述
     */
    @Column(name = "notify_message")
    private String notifyMessage;

    /**
     * 通知次数
     */
    @Column(name = "notify_count")
    private Integer notifyCount;

    /**
     * 删除标识
     */
    @Column(name = "del_flg")
    private Byte delFlg;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 获取主键
     *
     * @return notify_id - 主键
     */
    public String getNotifyId() {
        return notifyId;
    }

    /**
     * 设置主键
     *
     * @param notifyId 主键
     */
    public void setNotifyId(String notifyId) {
        this.notifyId = notifyId == null ? null : notifyId.trim();
    }

    /**
     * 获取申请ID
     *
     * @return apply_id - 申请ID
     */
    public String getApplyId() {
        return applyId;
    }

    /**
     * 设置申请ID
     *
     * @param applyId 申请ID
     */
    public void setApplyId(String applyId) {
        this.applyId = applyId == null ? null : applyId.trim();
    }

    /**
     * 获取业务号
     *
     * @return biz_no - 业务号
     */
    public String getBizNo() {
        return bizNo;
    }

    /**
     * 设置业务号
     *
     * @param bizNo 业务号
     */
    public void setBizNo(String bizNo) {
        this.bizNo = bizNo == null ? null : bizNo.trim();
    }

    /**
     * @return biz_type
     */
    public String getBizType() {
        return bizType;
    }

    /**
     * @param bizType
     */
    public void setBizType(String bizType) {
        this.bizType = bizType == null ? null : bizType.trim();
    }

    /**
     * 获取商品编码
     *
     * @return item_code - 商品编码
     */
    public String getItemCode() {
        return itemCode;
    }

    /**
     * 设置商品编码
     *
     * @param itemCode 商品编码
     */
    public void setItemCode(String itemCode) {
        this.itemCode = itemCode == null ? null : itemCode.trim();
    }

    /**
     * 获取申请状态
     *
     * @return apply_status - 申请状态
     */
    public String getApplyStatus() {
        return applyStatus;
    }

    /**
     * 设置申请状态
     *
     * @param applyStatus 申请状态
     */
    public void setApplyStatus(String applyStatus) {
        this.applyStatus = applyStatus == null ? null : applyStatus.trim();
    }

    /**
     * 获取原因
     *
     * @return reason - 原因
     */
    public String getReason() {
        return reason;
    }

    /**
     * 设置原因
     *
     * @param reason 原因
     */
    public void setReason(String reason) {
        this.reason = reason == null ? null : reason.trim();
    }

    /**
     * 获取通知状态
     *
     * @return notify_status - 通知状态
     */
    public String getNotifyStatus() {
        return notifyStatus;
    }

    /**
     * 设置通知状态
     *
     * @param notifyStatus 通知状态
     */
    public void setNotifyStatus(String notifyStatus) {
        this.notifyStatus = notifyStatus == null ? null : notifyStatus.trim();
    }

    /**
     * 获取通知描述
     *
     * @return notify_message - 通知描述
     */
    public String getNotifyMessage() {
        return notifyMessage;
    }

    /**
     * 设置通知描述
     *
     * @param notifyMessage 通知描述
     */
    public void setNotifyMessage(String notifyMessage) {
        this.notifyMessage = notifyMessage == null ? null : notifyMessage.trim();
    }

    /**
     * 获取通知次数
     *
     * @return notify_count - 通知次数
     */
    public Integer getNotifyCount() {
        return notifyCount;
    }

    /**
     * 设置通知次数
     *
     * @param notifyCount 通知次数
     */
    public void setNotifyCount(Integer notifyCount) {
        this.notifyCount = notifyCount;
    }

    /**
     * 获取删除标识
     *
     * @return del_flg - 删除标识
     */
    public Byte getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标识
     *
     * @param delFlg 删除标识
     */
    public void setDelFlg(Byte delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", notifyId=").append(notifyId);
        sb.append(", applyId=").append(applyId);
        sb.append(", bizNo=").append(bizNo);
        sb.append(", bizType=").append(bizType);
        sb.append(", itemCode=").append(itemCode);
        sb.append(", applyStatus=").append(applyStatus);
        sb.append(", reason=").append(reason);
        sb.append(", notifyStatus=").append(notifyStatus);
        sb.append(", notifyMessage=").append(notifyMessage);
        sb.append(", notifyCount=").append(notifyCount);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}