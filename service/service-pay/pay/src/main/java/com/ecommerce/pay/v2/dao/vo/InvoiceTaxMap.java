package com.ecommerce.pay.v2.dao.vo;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import jakarta.persistence.*;

@Table(name = "pa_invoice_tax_map")
public class InvoiceTaxMap implements Serializable {
    /**
     * 税号映射ID
     */
    @Id
    @Column(name = "tax_map_id")
    private String taxMapId;

    /**
     * 税收商品分类
     */
    @Column(name = "item_category")
    private String itemCategory;

    /**
     * 商品名
     */
    @Column(name = "item_name")
    private String itemName;

    /**
     * 商品编码
     */
    @Column(name = "item_code")
    private String itemCode;

    /**
     * 商品型号
     */
    @Column(name = "item_mode")
    private String itemMode;

    /**
     * 税率
     */
    @Column(name = "tax_rate")
    private BigDecimal taxRate;

    /**
     * 税控分类编码，不足19位后面补0
     */
    @Column(name = "tax_code")
    private String taxCode;

    /**
     *
     */
    @Column(name = "tax_name")
    private String taxName;

    /**
     * 删除标识
     */
    @Column(name = "del_flg")
    private Byte delFlg;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    @Serial
    private static final long serialVersionUID = 1L;

    public String getTaxName() {
        return taxName;
    }

    public void setTaxName(String taxName) {
        this.taxName = taxName;
    }

    /**
     * 获取税号映射ID
     *
     * @return tax_map_id - 税号映射ID
     */
    public String getTaxMapId() {
        return taxMapId;
    }

    /**
     * 设置税号映射ID
     *
     * @param taxMapId 税号映射ID
     */
    public void setTaxMapId(String taxMapId) {
        this.taxMapId = taxMapId == null ? null : taxMapId.trim();
    }

    /**
     * 获取税收商品分类
     *
     * @return item_category - 税收商品分类
     */
    public String getItemCategory() {
        return itemCategory;
    }

    /**
     * 设置税收商品分类
     *
     * @param itemCategory 税收商品分类
     */
    public void setItemCategory(String itemCategory) {
        this.itemCategory = itemCategory == null ? null : itemCategory.trim();
    }

    /**
     * 获取商品名
     *
     * @return item_name - 商品名
     */
    public String getItemName() {
        return itemName;
    }

    /**
     * 设置商品名
     *
     * @param itemName 商品名
     */
    public void setItemName(String itemName) {
        this.itemName = itemName == null ? null : itemName.trim();
    }

    /**
     * 获取商品编码
     *
     * @return item_code - 商品编码
     */
    public String getItemCode() {
        return itemCode;
    }

    /**
     * 设置商品编码
     *
     * @param itemCode 商品编码
     */
    public void setItemCode(String itemCode) {
        this.itemCode = itemCode == null ? null : itemCode.trim();
    }

    /**
     * 获取商品型号
     *
     * @return item_mode - 商品型号
     */
    public String getItemMode() {
        return itemMode;
    }

    /**
     * 设置商品型号
     *
     * @param itemMode 商品型号
     */
    public void setItemMode(String itemMode) {
        this.itemMode = itemMode == null ? null : itemMode.trim();
    }

    /**
     * 获取税率
     *
     * @return tax_rate - 税率
     */
    public BigDecimal getTaxRate() {
        return taxRate;
    }

    /**
     * 设置税率
     *
     * @param taxRate 税率
     */
    public void setTaxRate(BigDecimal taxRate) {
        this.taxRate = taxRate;
    }

    /**
     * 获取税控分类编码，不足19位后面补0
     *
     * @return tax_code - 税控分类编码，不足19位后面补0
     */
    public String getTaxCode() {
        return taxCode;
    }

    /**
     * 设置税控分类编码，不足19位后面补0
     *
     * @param taxCode 税控分类编码，不足19位后面补0
     */
    public void setTaxCode(String taxCode) {
        this.taxCode = taxCode == null ? null : taxCode.trim();
    }

    /**
     * 获取删除标识
     *
     * @return del_flg - 删除标识
     */
    public Byte getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标识
     *
     * @param delFlg 删除标识
     */
    public void setDelFlg(Byte delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", taxMapId=").append(taxMapId);
        sb.append(", itemCategory=").append(itemCategory);
        sb.append(", itemName=").append(itemName);
        sb.append(", itemCode=").append(itemCode);
        sb.append(", itemMode=").append(itemMode);
        sb.append(", taxName=").append(taxName);
        sb.append(", taxRate=").append(taxRate);
        sb.append(", taxCode=").append(taxCode);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}