package com.ecommerce.pay.v2.dao.vo;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import jakarta.persistence.*;

@Table(name = "pa_logistics_adjust_price")
public class LogisticsAdjustPrice implements Serializable {
    /**
     * 主键
     */
    @Id
    private String id;

    /**
     * 物流价格回调编码
     */
    @Column(name = "adjust_no")
    private String adjustNo;

    /**
     * 物流价格回调名称
     */
    @Column(name = "adjust_name")
    private String adjustName;

    /**
     * 调价类型 0：收费  1：付费   2：收付费
     */
    @Column(name = "adjust_type")
    private Integer adjustType;

    /**
     * 运输类型 030230100:汽运 030230200:船运
     */
    @Column(name = "transport_type")
    private String transportType;

    /**
     * 回调区间 开始时间
     */
    @Column(name = "ship_start_time")
    private Date shipStartTime;

    /**
     * 回调区间 结束时间
     */
    @Column(name = "ship_end_time")
    private Date shipEndTime;

    /**
     * 出库仓库ID
     */
    @Column(name = "warehouse_id")
    private String warehouseId;

    /**
     * 出库仓库名称
     */
    @Column(name = "warehouse_name")
    private String warehouseName;

    /**
     * 收货仓库ID
     */
    @Column(name = "receive_warehouse_id")
    private String receiveWarehouseId;

    /**
     * 收货仓库名称
     */
    @Column(name = "receive_warehouse_name")
    private String receiveWarehouseName;

    /**
     * 收货省级名
     */
    private String province;

    /**
     * 收货省编码
     */
    @Column(name = "province_code")
    private String provinceCode;

    /**
     * 收货市级名
     */
    private String city;

    /**
     * 收货市编码
     */
    @Column(name = "city_code")
    private String cityCode;

    /**
     * 收货区级名
     */
    private String district;

    /**
     * 收货区编码
     */
    @Column(name = "district_code")
    private String districtCode;

    /**
     * 收货街道名
     */
    private String street;

    /**
     * 收货街道编码
     */
    @Column(name = "street_code")
    private String streetCode;

    /**
     * 收货详细地址
     */
    private String address;

    /**
     * 运输品类ID
     */
    @Column(name = "transport_category_id")
    private String transportCategoryId;

    /**
     * 运输品类名称
     */
    @Column(name = "transport_category_name")
    private String transportCategoryName;

    /**
     * 价幅度 单位：元/吨
     */
    @Column(name = "adjust_add_price")
    private BigDecimal adjustAddPrice;

    /**
     * 状态 1：待回调、2：回调中、3：回调成功、4：回调失败
     */
    private Integer status;

    /**
     * 备注
     */
    private String memo;

    /**
     * ERP返回的错误信息
     */
    @Column(name = "err_msg")
    private String errMsg;

    /**
     * 删除标记 1：已删除、0：未删除
     */
    @Column(name = "del_flg")
    private Integer delFlg;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 获取主键
     *
     * @return id - 主键
     */
    public String getId() {
        return id;
    }

    /**
     * 设置主键
     *
     * @param id 主键
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * 获取物流价格回调编码
     *
     * @return adjust_no - 物流价格回调编码
     */
    public String getAdjustNo() {
        return adjustNo;
    }

    /**
     * 设置物流价格回调编码
     *
     * @param adjustNo 物流价格回调编码
     */
    public void setAdjustNo(String adjustNo) {
        this.adjustNo = adjustNo == null ? null : adjustNo.trim();
    }

    /**
     * 获取物流价格回调名称
     *
     * @return adjust_name - 物流价格回调名称
     */
    public String getAdjustName() {
        return adjustName;
    }

    /**
     * 设置物流价格回调名称
     *
     * @param adjustName 物流价格回调名称
     */
    public void setAdjustName(String adjustName) {
        this.adjustName = adjustName == null ? null : adjustName.trim();
    }

    /**
     * 获取调价类型 0：收费  1：付费   2：收付费
     *
     * @return adjust_type - 调价类型 0：收费  1：付费   2：收付费
     */
    public Integer getAdjustType() {
        return adjustType;
    }

    /**
     * 设置调价类型 0：收费  1：付费   2：收付费
     *
     * @param adjustType 调价类型 0：收费  1：付费   2：收付费
     */
    public void setAdjustType(Integer adjustType) {
        this.adjustType = adjustType;
    }

    /**
     * 获取运输类型 030230100:汽运 030230200:船运
     *
     * @return transport_type - 运输类型 030230100:汽运 030230200:船运
     */
    public String getTransportType() {
        return transportType;
    }

    /**
     * 设置运输类型 030230100:汽运 030230200:船运
     *
     * @param transportType 运输类型 030230100:汽运 030230200:船运
     */
    public void setTransportType(String transportType) {
        this.transportType = transportType == null ? null : transportType.trim();
    }

    /**
     * 获取回调区间 开始时间
     *
     * @return ship_start_time - 回调区间 开始时间
     */
    public Date getShipStartTime() {
        return shipStartTime;
    }

    /**
     * 设置回调区间 开始时间
     *
     * @param shipStartTime 回调区间 开始时间
     */
    public void setShipStartTime(Date shipStartTime) {
        this.shipStartTime = shipStartTime;
    }

    /**
     * 获取回调区间 结束时间
     *
     * @return ship_end_time - 回调区间 结束时间
     */
    public Date getShipEndTime() {
        return shipEndTime;
    }

    /**
     * 设置回调区间 结束时间
     *
     * @param shipEndTime 回调区间 结束时间
     */
    public void setShipEndTime(Date shipEndTime) {
        this.shipEndTime = shipEndTime;
    }

    /**
     * 获取出库仓库ID
     *
     * @return warehouse_id - 出库仓库ID
     */
    public String getWarehouseId() {
        return warehouseId;
    }

    /**
     * 设置出库仓库ID
     *
     * @param warehouseId 出库仓库ID
     */
    public void setWarehouseId(String warehouseId) {
        this.warehouseId = warehouseId == null ? null : warehouseId.trim();
    }

    /**
     * 获取出库仓库名称
     *
     * @return warehouse_name - 出库仓库名称
     */
    public String getWarehouseName() {
        return warehouseName;
    }

    /**
     * 设置出库仓库名称
     *
     * @param warehouseName 出库仓库名称
     */
    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName == null ? null : warehouseName.trim();
    }

    /**
     * 获取收货仓库ID
     *
     * @return receive_warehouse_id - 收货仓库ID
     */
    public String getReceiveWarehouseId() {
        return receiveWarehouseId;
    }

    /**
     * 设置收货仓库ID
     *
     * @param receiveWarehouseId 收货仓库ID
     */
    public void setReceiveWarehouseId(String receiveWarehouseId) {
        this.receiveWarehouseId = receiveWarehouseId == null ? null : receiveWarehouseId.trim();
    }

    /**
     * 获取收货仓库名称
     *
     * @return receive_warehouse_name - 收货仓库名称
     */
    public String getReceiveWarehouseName() {
        return receiveWarehouseName;
    }

    /**
     * 设置收货仓库名称
     *
     * @param receiveWarehouseName 收货仓库名称
     */
    public void setReceiveWarehouseName(String receiveWarehouseName) {
        this.receiveWarehouseName = receiveWarehouseName == null ? null : receiveWarehouseName.trim();
    }

    /**
     * 获取收货省级名
     *
     * @return province - 收货省级名
     */
    public String getProvince() {
        return province;
    }

    /**
     * 设置收货省级名
     *
     * @param province 收货省级名
     */
    public void setProvince(String province) {
        this.province = province == null ? null : province.trim();
    }

    /**
     * 获取收货省编码
     *
     * @return province_code - 收货省编码
     */
    public String getProvinceCode() {
        return provinceCode;
    }

    /**
     * 设置收货省编码
     *
     * @param provinceCode 收货省编码
     */
    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode == null ? null : provinceCode.trim();
    }

    /**
     * 获取收货市级名
     *
     * @return city - 收货市级名
     */
    public String getCity() {
        return city;
    }

    /**
     * 设置收货市级名
     *
     * @param city 收货市级名
     */
    public void setCity(String city) {
        this.city = city == null ? null : city.trim();
    }

    /**
     * 获取收货市编码
     *
     * @return city_code - 收货市编码
     */
    public String getCityCode() {
        return cityCode;
    }

    /**
     * 设置收货市编码
     *
     * @param cityCode 收货市编码
     */
    public void setCityCode(String cityCode) {
        this.cityCode = cityCode == null ? null : cityCode.trim();
    }

    /**
     * 获取收货区级名
     *
     * @return district - 收货区级名
     */
    public String getDistrict() {
        return district;
    }

    /**
     * 设置收货区级名
     *
     * @param district 收货区级名
     */
    public void setDistrict(String district) {
        this.district = district == null ? null : district.trim();
    }

    /**
     * 获取收货区编码
     *
     * @return district_code - 收货区编码
     */
    public String getDistrictCode() {
        return districtCode;
    }

    /**
     * 设置收货区编码
     *
     * @param districtCode 收货区编码
     */
    public void setDistrictCode(String districtCode) {
        this.districtCode = districtCode == null ? null : districtCode.trim();
    }

    /**
     * 获取收货街道名
     *
     * @return street - 收货街道名
     */
    public String getStreet() {
        return street;
    }

    /**
     * 设置收货街道名
     *
     * @param street 收货街道名
     */
    public void setStreet(String street) {
        this.street = street == null ? null : street.trim();
    }

    /**
     * 获取收货街道编码
     *
     * @return street_code - 收货街道编码
     */
    public String getStreetCode() {
        return streetCode;
    }

    /**
     * 设置收货街道编码
     *
     * @param streetCode 收货街道编码
     */
    public void setStreetCode(String streetCode) {
        this.streetCode = streetCode == null ? null : streetCode.trim();
    }

    /**
     * 获取收货详细地址
     *
     * @return address - 收货详细地址
     */
    public String getAddress() {
        return address;
    }

    /**
     * 设置收货详细地址
     *
     * @param address 收货详细地址
     */
    public void setAddress(String address) {
        this.address = address == null ? null : address.trim();
    }

    /**
     * 获取运输品类ID
     *
     * @return transport_category_id - 运输品类ID
     */
    public String getTransportCategoryId() {
        return transportCategoryId;
    }

    /**
     * 设置运输品类ID
     *
     * @param transportCategoryId 运输品类ID
     */
    public void setTransportCategoryId(String transportCategoryId) {
        this.transportCategoryId = transportCategoryId == null ? null : transportCategoryId.trim();
    }

    /**
     * 获取运输品类名称
     *
     * @return transport_category_name - 运输品类名称
     */
    public String getTransportCategoryName() {
        return transportCategoryName;
    }

    /**
     * 设置运输品类名称
     *
     * @param transportCategoryName 运输品类名称
     */
    public void setTransportCategoryName(String transportCategoryName) {
        this.transportCategoryName = transportCategoryName == null ? null : transportCategoryName.trim();
    }

    /**
     * 获取价幅度 单位：元/吨
     *
     * @return adjust_add_price - 价幅度 单位：元/吨
     */
    public BigDecimal getAdjustAddPrice() {
        return adjustAddPrice;
    }

    /**
     * 设置价幅度 单位：元/吨
     *
     * @param adjustAddPrice 价幅度 单位：元/吨
     */
    public void setAdjustAddPrice(BigDecimal adjustAddPrice) {
        this.adjustAddPrice = adjustAddPrice;
    }

    /**
     * 获取状态 1：待回调、2：回调中、3：回调成功、4：回调失败
     *
     * @return status - 状态 1：待回调、2：回调中、3：回调成功、4：回调失败
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * 设置状态 1：待回调、2：回调中、3：回调成功、4：回调失败
     *
     * @param status 状态 1：待回调、2：回调中、3：回调成功、4：回调失败
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * 获取备注
     *
     * @return memo - 备注
     */
    public String getMemo() {
        return memo;
    }

    /**
     * 设置备注
     *
     * @param memo 备注
     */
    public void setMemo(String memo) {
        this.memo = memo == null ? null : memo.trim();
    }

    /**
     * 获取ERP返回的错误信息
     *
     * @return err_msg - ERP返回的错误信息
     */
    public String getErrMsg() {
        return errMsg;
    }

    /**
     * 设置ERP返回的错误信息
     *
     * @param errMsg ERP返回的错误信息
     */
    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg == null ? null : errMsg.trim();
    }

    /**
     * 获取删除标记 1：已删除、0：未删除
     *
     * @return del_flg - 删除标记 1：已删除、0：未删除
     */
    public Integer getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标记 1：已删除、0：未删除
     *
     * @param delFlg 删除标记 1：已删除、0：未删除
     */
    public void setDelFlg(Integer delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", adjustNo=").append(adjustNo);
        sb.append(", adjustName=").append(adjustName);
        sb.append(", adjustType=").append(adjustType);
        sb.append(", transportType=").append(transportType);
        sb.append(", shipStartTime=").append(shipStartTime);
        sb.append(", shipEndTime=").append(shipEndTime);
        sb.append(", warehouseId=").append(warehouseId);
        sb.append(", warehouseName=").append(warehouseName);
        sb.append(", receiveWarehouseId=").append(receiveWarehouseId);
        sb.append(", receiveWarehouseName=").append(receiveWarehouseName);
        sb.append(", province=").append(province);
        sb.append(", provinceCode=").append(provinceCode);
        sb.append(", city=").append(city);
        sb.append(", cityCode=").append(cityCode);
        sb.append(", district=").append(district);
        sb.append(", districtCode=").append(districtCode);
        sb.append(", street=").append(street);
        sb.append(", streetCode=").append(streetCode);
        sb.append(", address=").append(address);
        sb.append(", transportCategoryId=").append(transportCategoryId);
        sb.append(", transportCategoryName=").append(transportCategoryName);
        sb.append(", adjustAddPrice=").append(adjustAddPrice);
        sb.append(", status=").append(status);
        sb.append(", memo=").append(memo);
        sb.append(", errMsg=").append(errMsg);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}