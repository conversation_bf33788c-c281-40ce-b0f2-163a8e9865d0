package com.ecommerce.pay.v2.dao.vo;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import jakarta.persistence.*;

@Table(name = "pa_logistics_adjust_price_item")
public class LogisticsAdjustPriceItem implements Serializable {
    /**
     * ID
     */
    @Id
    private String id;

    /**
     * 物流调价ID
     */
    @Column(name = "adjust_id")
    private String adjustId;

    /**
     * 物流用户ID
     */
    @Column(name = "adjust_member_id")
    private String adjustMemberId;

    /**
     * 委托单ID
     */
    @Column(name = "delivery_bill_id")
    private String deliveryBillId;

    /**
     * 运单ID
     */
    @Column(name = "waybill_id")
    private String waybillId;

    /**
     * 运单itemID
     */
    @Column(name = "waybill_item_id")
    private String waybillItemId;

    /**
     * 运单号
     */
    @Column(name = "waybill_num")
    private String waybillNum;

    /**
     * 客户ERP编码
     */
    @Column(name = "mdm_code")
    private String mdmCode;

    /**
     * 外部运单号
     */
    @Column(name = "external_waybill_num")
    private String externalWaybillNum;

    /**
     * 商品ID
     */
    @Column(name = "goods_id")
    private String goodsId;

    /**
     * 商品名称
     */
    @Column(name = "goods_name")
    private String goodsName;

    /**
     * 商品单价
     */
    @Column(name = "goods_price")
    private BigDecimal goodsPrice;

    /**
     * 运输类型 030230100:汽运 030230200:船运
     */
    @Column(name = "transport_type")
    private String transportType;

    /**
     * 委托方ID
     */
    @Column(name = "entrusting_side_id")
    private String entrustingSideId;

    /**
     * 委托方名称
     */
    @Column(name = "entrusting_side_name")
    private String entrustingSideName;

    /**
     * 被委托方ID
     */
    @Column(name = "entrusted_side_id")
    private String entrustedSideId;

    /**
     * 被委托方名称
     */
    @Column(name = "entrusted_side_name")
    private String entrustedSideName;

    /**
     * 承运量
     */
    @Column(name = "actual_quantity")
    private BigDecimal actualQuantity;

    /**
     * 出库仓库ID
     */
    @Column(name = "warehouse_id")
    private String warehouseId;

    /**
     * 出库仓库名称
     */
    @Column(name = "warehouse_name")
    private String warehouseName;

    /**
     * 原始物流单价
     */
    @Column(name = "origin_price")
    private BigDecimal originPrice;

    /**
     * 原始物流金额
     */
    @Column(name = "origin_amount")
    private BigDecimal originAmount;

    /**
     * 最新物流单价
     */
    @Column(name = "newest_price")
    private BigDecimal newestPrice;

    /**
     * 最新物流金额
     */
    @Column(name = "newest_amount")
    private BigDecimal newestAmount;

    /**
     * 调整后物流单价 单位：元/吨
     */
    @Column(name = "adjust_price")
    private BigDecimal adjustPrice;

    /**
     * 调整后物流金额
     */
    @Column(name = "adjust_amount")
    private BigDecimal adjustAmount;

    /**
     * 价幅度 单位：元/吨
     */
    @Column(name = "adjust_add_price")
    private BigDecimal adjustAddPrice;

    /**
     * 调价次数
     */
    @Column(name = "adjust_num")
    private Integer adjustNum;

    /**
     * 调价类型 0：收费  1：付费   2：收付费
     */
    @Column(name = "adjust_type")
    private Integer adjustType;

    /**
     * 运单出厂时间
     */
    @Column(name = "leave_warehouse_time")
    private Date leaveWarehouseTime;

    /**
     * 运单完成时间
     */
    @Column(name = "complete_time")
    private Date completeTime;

    /**
     * 状态 1：待回调、2：回调中、3：回调成功、4：回调失败
     */
    private Integer status;

    /**
     * ERP返回的错误信息
     */
    @Column(name = "err_msg")
    private String errMsg;

    /**
     * 删除标记 1：已删除、0：未删除
     */
    @Column(name = "del_flg")
    private Integer delFlg;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 获取ID
     *
     * @return id - ID
     */
    public String getId() {
        return id;
    }

    /**
     * 设置ID
     *
     * @param id ID
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * 获取物流调价ID
     *
     * @return adjust_id - 物流调价ID
     */
    public String getAdjustId() {
        return adjustId;
    }

    /**
     * 设置物流调价ID
     *
     * @param adjustId 物流调价ID
     */
    public void setAdjustId(String adjustId) {
        this.adjustId = adjustId == null ? null : adjustId.trim();
    }

    /**
     * 获取物流用户ID
     *
     * @return adjust_member_id - 物流用户ID
     */
    public String getAdjustMemberId() {
        return adjustMemberId;
    }

    /**
     * 设置物流用户ID
     *
     * @param adjustMemberId 物流用户ID
     */
    public void setAdjustMemberId(String adjustMemberId) {
        this.adjustMemberId = adjustMemberId == null ? null : adjustMemberId.trim();
    }

    /**
     * 获取委托单ID
     *
     * @return delivery_bill_id - 委托单ID
     */
    public String getDeliveryBillId() {
        return deliveryBillId;
    }

    /**
     * 设置委托单ID
     *
     * @param deliveryBillId 委托单ID
     */
    public void setDeliveryBillId(String deliveryBillId) {
        this.deliveryBillId = deliveryBillId == null ? null : deliveryBillId.trim();
    }

    /**
     * 获取运单ID
     *
     * @return waybill_id - 运单ID
     */
    public String getWaybillId() {
        return waybillId;
    }

    /**
     * 设置运单ID
     *
     * @param waybillId 运单ID
     */
    public void setWaybillId(String waybillId) {
        this.waybillId = waybillId == null ? null : waybillId.trim();
    }

    /**
     * 获取运单itemID
     *
     * @return waybill_item_id - 运单itemID
     */
    public String getWaybillItemId() {
        return waybillItemId;
    }

    /**
     * 设置运单itemID
     *
     * @param waybillItemId 运单itemID
     */
    public void setWaybillItemId(String waybillItemId) {
        this.waybillItemId = waybillItemId == null ? null : waybillItemId.trim();
    }

    /**
     * 获取运单号
     *
     * @return waybill_num - 运单号
     */
    public String getWaybillNum() {
        return waybillNum;
    }

    /**
     * 设置运单号
     *
     * @param waybillNum 运单号
     */
    public void setWaybillNum(String waybillNum) {
        this.waybillNum = waybillNum == null ? null : waybillNum.trim();
    }

    /**
     * 获取客户ERP编码
     *
     * @return mdm_code - 客户ERP编码
     */
    public String getMdmCode() {
        return mdmCode;
    }

    /**
     * 设置客户ERP编码
     *
     * @param mdmCode 客户ERP编码
     */
    public void setMdmCode(String mdmCode) {
        this.mdmCode = mdmCode == null ? null : mdmCode.trim();
    }

    /**
     * 获取外部运单号
     *
     * @return external_waybill_num - 外部运单号
     */
    public String getExternalWaybillNum() {
        return externalWaybillNum;
    }

    /**
     * 设置外部运单号
     *
     * @param externalWaybillNum 外部运单号
     */
    public void setExternalWaybillNum(String externalWaybillNum) {
        this.externalWaybillNum = externalWaybillNum == null ? null : externalWaybillNum.trim();
    }

    /**
     * 获取商品ID
     *
     * @return goods_id - 商品ID
     */
    public String getGoodsId() {
        return goodsId;
    }

    /**
     * 设置商品ID
     *
     * @param goodsId 商品ID
     */
    public void setGoodsId(String goodsId) {
        this.goodsId = goodsId == null ? null : goodsId.trim();
    }

    /**
     * 获取商品名称
     *
     * @return goods_name - 商品名称
     */
    public String getGoodsName() {
        return goodsName;
    }

    /**
     * 设置商品名称
     *
     * @param goodsName 商品名称
     */
    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName == null ? null : goodsName.trim();
    }

    /**
     * 获取商品单价
     *
     * @return goods_price - 商品单价
     */
    public BigDecimal getGoodsPrice() {
        return goodsPrice;
    }

    /**
     * 设置商品单价
     *
     * @param goodsPrice 商品单价
     */
    public void setGoodsPrice(BigDecimal goodsPrice) {
        this.goodsPrice = goodsPrice;
    }

    /**
     * 获取运输类型 030230100:汽运 030230200:船运
     *
     * @return transport_type - 运输类型 030230100:汽运 030230200:船运
     */
    public String getTransportType() {
        return transportType;
    }

    /**
     * 设置运输类型 030230100:汽运 030230200:船运
     *
     * @param transportType 运输类型 030230100:汽运 030230200:船运
     */
    public void setTransportType(String transportType) {
        this.transportType = transportType == null ? null : transportType.trim();
    }

    /**
     * 获取委托方ID
     *
     * @return entrusting_side_id - 委托方ID
     */
    public String getEntrustingSideId() {
        return entrustingSideId;
    }

    /**
     * 设置委托方ID
     *
     * @param entrustingSideId 委托方ID
     */
    public void setEntrustingSideId(String entrustingSideId) {
        this.entrustingSideId = entrustingSideId == null ? null : entrustingSideId.trim();
    }

    /**
     * 获取委托方名称
     *
     * @return entrusting_side_name - 委托方名称
     */
    public String getEntrustingSideName() {
        return entrustingSideName;
    }

    /**
     * 设置委托方名称
     *
     * @param entrustingSideName 委托方名称
     */
    public void setEntrustingSideName(String entrustingSideName) {
        this.entrustingSideName = entrustingSideName == null ? null : entrustingSideName.trim();
    }

    /**
     * 获取被委托方ID
     *
     * @return entrusted_side_id - 被委托方ID
     */
    public String getEntrustedSideId() {
        return entrustedSideId;
    }

    /**
     * 设置被委托方ID
     *
     * @param entrustedSideId 被委托方ID
     */
    public void setEntrustedSideId(String entrustedSideId) {
        this.entrustedSideId = entrustedSideId == null ? null : entrustedSideId.trim();
    }

    /**
     * 获取被委托方名称
     *
     * @return entrusted_side_name - 被委托方名称
     */
    public String getEntrustedSideName() {
        return entrustedSideName;
    }

    /**
     * 设置被委托方名称
     *
     * @param entrustedSideName 被委托方名称
     */
    public void setEntrustedSideName(String entrustedSideName) {
        this.entrustedSideName = entrustedSideName == null ? null : entrustedSideName.trim();
    }

    /**
     * 获取承运量
     *
     * @return actual_quantity - 承运量
     */
    public BigDecimal getActualQuantity() {
        return actualQuantity;
    }

    /**
     * 设置承运量
     *
     * @param actualQuantity 承运量
     */
    public void setActualQuantity(BigDecimal actualQuantity) {
        this.actualQuantity = actualQuantity;
    }

    /**
     * 获取出库仓库ID
     *
     * @return warehouse_id - 出库仓库ID
     */
    public String getWarehouseId() {
        return warehouseId;
    }

    /**
     * 设置出库仓库ID
     *
     * @param warehouseId 出库仓库ID
     */
    public void setWarehouseId(String warehouseId) {
        this.warehouseId = warehouseId == null ? null : warehouseId.trim();
    }

    /**
     * 获取出库仓库名称
     *
     * @return warehouse_name - 出库仓库名称
     */
    public String getWarehouseName() {
        return warehouseName;
    }

    /**
     * 设置出库仓库名称
     *
     * @param warehouseName 出库仓库名称
     */
    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName == null ? null : warehouseName.trim();
    }

    /**
     * 获取原始物流单价
     *
     * @return origin_price - 原始物流单价
     */
    public BigDecimal getOriginPrice() {
        return originPrice;
    }

    /**
     * 设置原始物流单价
     *
     * @param originPrice 原始物流单价
     */
    public void setOriginPrice(BigDecimal originPrice) {
        this.originPrice = originPrice;
    }

    /**
     * 获取原始物流金额
     *
     * @return origin_amount - 原始物流金额
     */
    public BigDecimal getOriginAmount() {
        return originAmount;
    }

    /**
     * 设置原始物流金额
     *
     * @param originAmount 原始物流金额
     */
    public void setOriginAmount(BigDecimal originAmount) {
        this.originAmount = originAmount;
    }

    /**
     * 获取最新物流单价
     *
     * @return newest_price - 最新物流单价
     */
    public BigDecimal getNewestPrice() {
        return newestPrice;
    }

    /**
     * 设置最新物流单价
     *
     * @param newestPrice 最新物流单价
     */
    public void setNewestPrice(BigDecimal newestPrice) {
        this.newestPrice = newestPrice;
    }

    /**
     * 获取最新物流金额
     *
     * @return newest_amount - 最新物流金额
     */
    public BigDecimal getNewestAmount() {
        return newestAmount;
    }

    /**
     * 设置最新物流金额
     *
     * @param newestAmount 最新物流金额
     */
    public void setNewestAmount(BigDecimal newestAmount) {
        this.newestAmount = newestAmount;
    }

    /**
     * 获取调整后物流单价 单位：元/吨
     *
     * @return adjust_price - 调整后物流单价 单位：元/吨
     */
    public BigDecimal getAdjustPrice() {
        return adjustPrice;
    }

    /**
     * 设置调整后物流单价 单位：元/吨
     *
     * @param adjustPrice 调整后物流单价 单位：元/吨
     */
    public void setAdjustPrice(BigDecimal adjustPrice) {
        this.adjustPrice = adjustPrice;
    }

    /**
     * 获取调整后物流金额
     *
     * @return adjust_amount - 调整后物流金额
     */
    public BigDecimal getAdjustAmount() {
        return adjustAmount;
    }

    /**
     * 设置调整后物流金额
     *
     * @param adjustAmount 调整后物流金额
     */
    public void setAdjustAmount(BigDecimal adjustAmount) {
        this.adjustAmount = adjustAmount;
    }

    /**
     * 获取价幅度 单位：元/吨
     *
     * @return adjust_add_price - 价幅度 单位：元/吨
     */
    public BigDecimal getAdjustAddPrice() {
        return adjustAddPrice;
    }

    /**
     * 设置价幅度 单位：元/吨
     *
     * @param adjustAddPrice 价幅度 单位：元/吨
     */
    public void setAdjustAddPrice(BigDecimal adjustAddPrice) {
        this.adjustAddPrice = adjustAddPrice;
    }

    /**
     * 获取调价次数
     *
     * @return adjust_num - 调价次数
     */
    public Integer getAdjustNum() {
        return adjustNum;
    }

    /**
     * 设置调价次数
     *
     * @param adjustNum 调价次数
     */
    public void setAdjustNum(Integer adjustNum) {
        this.adjustNum = adjustNum;
    }

    /**
     * 获取调价类型 0：收费  1：付费   2：收付费
     *
     * @return adjust_type - 调价类型 0：收费  1：付费   2：收付费
     */
    public Integer getAdjustType() {
        return adjustType;
    }

    /**
     * 设置调价类型 0：收费  1：付费   2：收付费
     *
     * @param adjustType 调价类型 0：收费  1：付费   2：收付费
     */
    public void setAdjustType(Integer adjustType) {
        this.adjustType = adjustType;
    }

    /**
     * 获取运单出厂时间
     *
     * @return leave_warehouse_time - 运单出厂时间
     */
    public Date getLeaveWarehouseTime() {
        return leaveWarehouseTime;
    }

    /**
     * 设置运单出厂时间
     *
     * @param leaveWarehouseTime 运单出厂时间
     */
    public void setLeaveWarehouseTime(Date leaveWarehouseTime) {
        this.leaveWarehouseTime = leaveWarehouseTime;
    }

    /**
     * 获取运单完成时间
     *
     * @return complete_time - 运单完成时间
     */
    public Date getCompleteTime() {
        return completeTime;
    }

    /**
     * 设置运单完成时间
     *
     * @param completeTime 运单完成时间
     */
    public void setCompleteTime(Date completeTime) {
        this.completeTime = completeTime;
    }

    /**
     * 获取状态 1：待回调、2：回调中、3：回调成功、4：回调失败
     *
     * @return status - 状态 1：待回调、2：回调中、3：回调成功、4：回调失败
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * 设置状态 1：待回调、2：回调中、3：回调成功、4：回调失败
     *
     * @param status 状态 1：待回调、2：回调中、3：回调成功、4：回调失败
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * 获取ERP返回的错误信息
     *
     * @return err_msg - ERP返回的错误信息
     */
    public String getErrMsg() {
        return errMsg;
    }

    /**
     * 设置ERP返回的错误信息
     *
     * @param errMsg ERP返回的错误信息
     */
    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg == null ? null : errMsg.trim();
    }

    /**
     * 获取删除标记 1：已删除、0：未删除
     *
     * @return del_flg - 删除标记 1：已删除、0：未删除
     */
    public Integer getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标记 1：已删除、0：未删除
     *
     * @param delFlg 删除标记 1：已删除、0：未删除
     */
    public void setDelFlg(Integer delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", adjustId=").append(adjustId);
        sb.append(", adjustMemberId=").append(adjustMemberId);
        sb.append(", deliveryBillId=").append(deliveryBillId);
        sb.append(", waybillId=").append(waybillId);
        sb.append(", waybillItemId=").append(waybillItemId);
        sb.append(", waybillNum=").append(waybillNum);
        sb.append(", mdmCode=").append(mdmCode);
        sb.append(", externalWaybillNum=").append(externalWaybillNum);
        sb.append(", goodsId=").append(goodsId);
        sb.append(", goodsName=").append(goodsName);
        sb.append(", goodsPrice=").append(goodsPrice);
        sb.append(", transportType=").append(transportType);
        sb.append(", entrustingSideId=").append(entrustingSideId);
        sb.append(", entrustingSideName=").append(entrustingSideName);
        sb.append(", entrustedSideId=").append(entrustedSideId);
        sb.append(", entrustedSideName=").append(entrustedSideName);
        sb.append(", actualQuantity=").append(actualQuantity);
        sb.append(", warehouseId=").append(warehouseId);
        sb.append(", warehouseName=").append(warehouseName);
        sb.append(", originPrice=").append(originPrice);
        sb.append(", originAmount=").append(originAmount);
        sb.append(", newestPrice=").append(newestPrice);
        sb.append(", newestAmount=").append(newestAmount);
        sb.append(", adjustPrice=").append(adjustPrice);
        sb.append(", adjustAmount=").append(adjustAmount);
        sb.append(", adjustAddPrice=").append(adjustAddPrice);
        sb.append(", adjustNum=").append(adjustNum);
        sb.append(", adjustType=").append(adjustType);
        sb.append(", leaveWarehouseTime=").append(leaveWarehouseTime);
        sb.append(", completeTime=").append(completeTime);
        sb.append(", status=").append(status);
        sb.append(", errMsg=").append(errMsg);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}