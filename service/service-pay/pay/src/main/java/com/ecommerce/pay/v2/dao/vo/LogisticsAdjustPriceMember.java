package com.ecommerce.pay.v2.dao.vo;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import jakarta.persistence.*;

@Table(name = "pa_logistics_adjust_price_member")
public class LogisticsAdjustPriceMember implements Serializable {
    /**
     * ID
     */
    @Id
    private String id;

    /**
     * 物流调价ID
     */
    @Column(name = "adjust_price_id")
    private String adjustPriceId;

    /**
     * 委托方ID
     */
    @Column(name = "entrusting_side_id")
    private String entrustingSideId;

    /**
     * 委托方名称
     */
    @Column(name = "entrusting_side_name")
    private String entrustingSideName;

    /**
     * 被委托方ID
     */
    @Column(name = "entrusted_side_id")
    private String entrustedSideId;

    /**
     * 被委托方名称
     */
    @Column(name = "entrusted_side_name")
    private String entrustedSideName;

    /**
     * 运输量
     */
    @Column(name = "actual_quantity")
    private BigDecimal actualQuantity;

    /**
     * 商品ID
     */
    @Column(name = "goods_id")
    private String goodsId;

    /**
     * 商品名称
     */
    @Column(name = "goods_name")
    private String goodsName;

    /**
     * 商品单价
     */
    @Column(name = "goods_price")
    private BigDecimal goodsPrice;

    /**
     * 运输类型 030230100:汽运 030230200:船运
     */
    @Column(name = "transport_type")
    private String transportType;

    /**
     * 调整前物流单价 单位：元/吨
     */
    @Column(name = "origin_price")
    private BigDecimal originPrice;

    /**
     * 调整前物流总额
     */
    @Column(name = "origin_amount")
    private BigDecimal originAmount;

    /**
     * 调整后物流单价 单位：元/吨
     */
    @Column(name = "newest_price")
    private BigDecimal newestPrice;

    /**
     * 调整后物流总额
     */
    @Column(name = "adjust_amount")
    private BigDecimal adjustAmount;

    /**
     * 调整幅度 单位：元/吨
     */
    @Column(name = "adjust_add_price")
    private BigDecimal adjustAddPrice;

    /**
     * 物流费调增金额
     */
    @Column(name = "adjust_add_amount")
    private BigDecimal adjustAddAmount;

    /**
     * 状态 1：待回调、2：回调中、3：回调成功、4：回调失败
     */
    private Integer status;

    /**
     * 出库仓库ID
     */
    @Column(name = "warehouse_id")
    private String warehouseId;

    /**
     * 出库仓库名称
     */
    @Column(name = "warehouse_name")
    private String warehouseName;

    /**
     * 收货省级名
     */
    private String province;

    /**
     * 收货省编码
     */
    @Column(name = "province_code")
    private String provinceCode;

    /**
     * 收货市级名
     */
    private String city;

    /**
     * 收货市编码
     */
    @Column(name = "city_code")
    private String cityCode;

    /**
     * 收货区级名
     */
    private String district;

    /**
     * 收货区编码
     */
    @Column(name = "district_code")
    private String districtCode;

    /**
     * 收货街道名
     */
    private String street;

    /**
     * 收货街道编码
     */
    @Column(name = "street_code")
    private String streetCode;

    /**
     * 收货详细地址
     */
    private String address;

    /**
     * 删除标记 1：已删除、0：未删除
     */
    @Column(name = "del_flg")
    private Integer delFlg;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 获取ID
     *
     * @return id - ID
     */
    public String getId() {
        return id;
    }

    /**
     * 设置ID
     *
     * @param id ID
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * 获取物流调价ID
     *
     * @return adjust_price_id - 物流调价ID
     */
    public String getAdjustPriceId() {
        return adjustPriceId;
    }

    /**
     * 设置物流调价ID
     *
     * @param adjustPriceId 物流调价ID
     */
    public void setAdjustPriceId(String adjustPriceId) {
        this.adjustPriceId = adjustPriceId == null ? null : adjustPriceId.trim();
    }

    /**
     * 获取委托方ID
     *
     * @return entrusting_side_id - 委托方ID
     */
    public String getEntrustingSideId() {
        return entrustingSideId;
    }

    /**
     * 设置委托方ID
     *
     * @param entrustingSideId 委托方ID
     */
    public void setEntrustingSideId(String entrustingSideId) {
        this.entrustingSideId = entrustingSideId == null ? null : entrustingSideId.trim();
    }

    /**
     * 获取委托方名称
     *
     * @return entrusting_side_name - 委托方名称
     */
    public String getEntrustingSideName() {
        return entrustingSideName;
    }

    /**
     * 设置委托方名称
     *
     * @param entrustingSideName 委托方名称
     */
    public void setEntrustingSideName(String entrustingSideName) {
        this.entrustingSideName = entrustingSideName == null ? null : entrustingSideName.trim();
    }

    /**
     * 获取被委托方ID
     *
     * @return entrusted_side_id - 被委托方ID
     */
    public String getEntrustedSideId() {
        return entrustedSideId;
    }

    /**
     * 设置被委托方ID
     *
     * @param entrustedSideId 被委托方ID
     */
    public void setEntrustedSideId(String entrustedSideId) {
        this.entrustedSideId = entrustedSideId == null ? null : entrustedSideId.trim();
    }

    /**
     * 获取被委托方名称
     *
     * @return entrusted_side_name - 被委托方名称
     */
    public String getEntrustedSideName() {
        return entrustedSideName;
    }

    /**
     * 设置被委托方名称
     *
     * @param entrustedSideName 被委托方名称
     */
    public void setEntrustedSideName(String entrustedSideName) {
        this.entrustedSideName = entrustedSideName == null ? null : entrustedSideName.trim();
    }

    /**
     * 获取运输量
     *
     * @return actual_quantity - 运输量
     */
    public BigDecimal getActualQuantity() {
        return actualQuantity;
    }

    /**
     * 设置运输量
     *
     * @param actualQuantity 运输量
     */
    public void setActualQuantity(BigDecimal actualQuantity) {
        this.actualQuantity = actualQuantity;
    }

    /**
     * 获取商品ID
     *
     * @return goods_id - 商品ID
     */
    public String getGoodsId() {
        return goodsId;
    }

    /**
     * 设置商品ID
     *
     * @param goodsId 商品ID
     */
    public void setGoodsId(String goodsId) {
        this.goodsId = goodsId == null ? null : goodsId.trim();
    }

    /**
     * 获取商品名称
     *
     * @return goods_name - 商品名称
     */
    public String getGoodsName() {
        return goodsName;
    }

    /**
     * 设置商品名称
     *
     * @param goodsName 商品名称
     */
    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName == null ? null : goodsName.trim();
    }

    /**
     * 获取商品单价
     *
     * @return goods_price - 商品单价
     */
    public BigDecimal getGoodsPrice() {
        return goodsPrice;
    }

    /**
     * 设置商品单价
     *
     * @param goodsPrice 商品单价
     */
    public void setGoodsPrice(BigDecimal goodsPrice) {
        this.goodsPrice = goodsPrice;
    }

    /**
     * 获取运输类型 030230100:汽运 030230200:船运
     *
     * @return transport_type - 运输类型 030230100:汽运 030230200:船运
     */
    public String getTransportType() {
        return transportType;
    }

    /**
     * 设置运输类型 030230100:汽运 030230200:船运
     *
     * @param transportType 运输类型 030230100:汽运 030230200:船运
     */
    public void setTransportType(String transportType) {
        this.transportType = transportType == null ? null : transportType.trim();
    }

    /**
     * 获取调整前物流单价 单位：元/吨
     *
     * @return origin_price - 调整前物流单价 单位：元/吨
     */
    public BigDecimal getOriginPrice() {
        return originPrice;
    }

    /**
     * 设置调整前物流单价 单位：元/吨
     *
     * @param originPrice 调整前物流单价 单位：元/吨
     */
    public void setOriginPrice(BigDecimal originPrice) {
        this.originPrice = originPrice;
    }

    /**
     * 获取调整前物流总额
     *
     * @return origin_amount - 调整前物流总额
     */
    public BigDecimal getOriginAmount() {
        return originAmount;
    }

    /**
     * 设置调整前物流总额
     *
     * @param originAmount 调整前物流总额
     */
    public void setOriginAmount(BigDecimal originAmount) {
        this.originAmount = originAmount;
    }

    /**
     * 获取调整后物流单价 单位：元/吨
     *
     * @return newest_price - 调整后物流单价 单位：元/吨
     */
    public BigDecimal getNewestPrice() {
        return newestPrice;
    }

    /**
     * 设置调整后物流单价 单位：元/吨
     *
     * @param newestPrice 调整后物流单价 单位：元/吨
     */
    public void setNewestPrice(BigDecimal newestPrice) {
        this.newestPrice = newestPrice;
    }

    /**
     * 获取调整后物流总额
     *
     * @return adjust_amount - 调整后物流总额
     */
    public BigDecimal getAdjustAmount() {
        return adjustAmount;
    }

    /**
     * 设置调整后物流总额
     *
     * @param adjustAmount 调整后物流总额
     */
    public void setAdjustAmount(BigDecimal adjustAmount) {
        this.adjustAmount = adjustAmount;
    }

    /**
     * 获取调整幅度 单位：元/吨
     *
     * @return adjust_add_price - 调整幅度 单位：元/吨
     */
    public BigDecimal getAdjustAddPrice() {
        return adjustAddPrice;
    }

    /**
     * 设置调整幅度 单位：元/吨
     *
     * @param adjustAddPrice 调整幅度 单位：元/吨
     */
    public void setAdjustAddPrice(BigDecimal adjustAddPrice) {
        this.adjustAddPrice = adjustAddPrice;
    }

    /**
     * 获取物流费调增金额
     *
     * @return adjust_add_amount - 物流费调增金额
     */
    public BigDecimal getAdjustAddAmount() {
        return adjustAddAmount;
    }

    /**
     * 设置物流费调增金额
     *
     * @param adjustAddAmount 物流费调增金额
     */
    public void setAdjustAddAmount(BigDecimal adjustAddAmount) {
        this.adjustAddAmount = adjustAddAmount;
    }

    /**
     * 获取状态 1：待回调、2：回调中、3：回调成功、4：回调失败
     *
     * @return status - 状态 1：待回调、2：回调中、3：回调成功、4：回调失败
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * 设置状态 1：待回调、2：回调中、3：回调成功、4：回调失败
     *
     * @param status 状态 1：待回调、2：回调中、3：回调成功、4：回调失败
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * 获取出库仓库ID
     *
     * @return warehouse_id - 出库仓库ID
     */
    public String getWarehouseId() {
        return warehouseId;
    }

    /**
     * 设置出库仓库ID
     *
     * @param warehouseId 出库仓库ID
     */
    public void setWarehouseId(String warehouseId) {
        this.warehouseId = warehouseId == null ? null : warehouseId.trim();
    }

    /**
     * 获取出库仓库名称
     *
     * @return warehouse_name - 出库仓库名称
     */
    public String getWarehouseName() {
        return warehouseName;
    }

    /**
     * 设置出库仓库名称
     *
     * @param warehouseName 出库仓库名称
     */
    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName == null ? null : warehouseName.trim();
    }

    /**
     * 获取收货省级名
     *
     * @return province - 收货省级名
     */
    public String getProvince() {
        return province;
    }

    /**
     * 设置收货省级名
     *
     * @param province 收货省级名
     */
    public void setProvince(String province) {
        this.province = province == null ? null : province.trim();
    }

    /**
     * 获取收货省编码
     *
     * @return province_code - 收货省编码
     */
    public String getProvinceCode() {
        return provinceCode;
    }

    /**
     * 设置收货省编码
     *
     * @param provinceCode 收货省编码
     */
    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode == null ? null : provinceCode.trim();
    }

    /**
     * 获取收货市级名
     *
     * @return city - 收货市级名
     */
    public String getCity() {
        return city;
    }

    /**
     * 设置收货市级名
     *
     * @param city 收货市级名
     */
    public void setCity(String city) {
        this.city = city == null ? null : city.trim();
    }

    /**
     * 获取收货市编码
     *
     * @return city_code - 收货市编码
     */
    public String getCityCode() {
        return cityCode;
    }

    /**
     * 设置收货市编码
     *
     * @param cityCode 收货市编码
     */
    public void setCityCode(String cityCode) {
        this.cityCode = cityCode == null ? null : cityCode.trim();
    }

    /**
     * 获取收货区级名
     *
     * @return district - 收货区级名
     */
    public String getDistrict() {
        return district;
    }

    /**
     * 设置收货区级名
     *
     * @param district 收货区级名
     */
    public void setDistrict(String district) {
        this.district = district == null ? null : district.trim();
    }

    /**
     * 获取收货区编码
     *
     * @return district_code - 收货区编码
     */
    public String getDistrictCode() {
        return districtCode;
    }

    /**
     * 设置收货区编码
     *
     * @param districtCode 收货区编码
     */
    public void setDistrictCode(String districtCode) {
        this.districtCode = districtCode == null ? null : districtCode.trim();
    }

    /**
     * 获取收货街道名
     *
     * @return street - 收货街道名
     */
    public String getStreet() {
        return street;
    }

    /**
     * 设置收货街道名
     *
     * @param street 收货街道名
     */
    public void setStreet(String street) {
        this.street = street == null ? null : street.trim();
    }

    /**
     * 获取收货街道编码
     *
     * @return street_code - 收货街道编码
     */
    public String getStreetCode() {
        return streetCode;
    }

    /**
     * 设置收货街道编码
     *
     * @param streetCode 收货街道编码
     */
    public void setStreetCode(String streetCode) {
        this.streetCode = streetCode == null ? null : streetCode.trim();
    }

    /**
     * 获取收货详细地址
     *
     * @return address - 收货详细地址
     */
    public String getAddress() {
        return address;
    }

    /**
     * 设置收货详细地址
     *
     * @param address 收货详细地址
     */
    public void setAddress(String address) {
        this.address = address == null ? null : address.trim();
    }

    /**
     * 获取删除标记 1：已删除、0：未删除
     *
     * @return del_flg - 删除标记 1：已删除、0：未删除
     */
    public Integer getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标记 1：已删除、0：未删除
     *
     * @param delFlg 删除标记 1：已删除、0：未删除
     */
    public void setDelFlg(Integer delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建用户
     *
     * @return create_user - 创建用户
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取更新用户
     *
     * @return update_user - 更新用户
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", adjustPriceId=").append(adjustPriceId);
        sb.append(", entrustingSideId=").append(entrustingSideId);
        sb.append(", entrustingSideName=").append(entrustingSideName);
        sb.append(", entrustedSideId=").append(entrustedSideId);
        sb.append(", entrustedSideName=").append(entrustedSideName);
        sb.append(", actualQuantity=").append(actualQuantity);
        sb.append(", goodsId=").append(goodsId);
        sb.append(", goodsName=").append(goodsName);
        sb.append(", goodsPrice=").append(goodsPrice);
        sb.append(", transportType=").append(transportType);
        sb.append(", originPrice=").append(originPrice);
        sb.append(", originAmount=").append(originAmount);
        sb.append(", newestPrice=").append(newestPrice);
        sb.append(", adjustAmount=").append(adjustAmount);
        sb.append(", adjustAddPrice=").append(adjustAddPrice);
        sb.append(", adjustAddAmount=").append(adjustAddAmount);
        sb.append(", status=").append(status);
        sb.append(", warehouseId=").append(warehouseId);
        sb.append(", warehouseName=").append(warehouseName);
        sb.append(", province=").append(province);
        sb.append(", provinceCode=").append(provinceCode);
        sb.append(", city=").append(city);
        sb.append(", cityCode=").append(cityCode);
        sb.append(", district=").append(district);
        sb.append(", districtCode=").append(districtCode);
        sb.append(", street=").append(street);
        sb.append(", streetCode=").append(streetCode);
        sb.append(", address=").append(address);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}