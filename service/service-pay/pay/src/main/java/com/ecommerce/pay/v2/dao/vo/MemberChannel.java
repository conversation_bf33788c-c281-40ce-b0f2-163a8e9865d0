package com.ecommerce.pay.v2.dao.vo;

import io.swagger.v3.oas.annotations.media.Schema;

import jakarta.persistence.Column;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Table(name = "pa_member_channel")
public class MemberChannel implements Serializable {
    @Id
    @Column(name = "member_channel_id")
    private String memberChannelId;

    @Column(name = "member_channel_code")
    private String memberChannelCode;

    /**
     * 已开通,开通中，被停用
     */
    @Column(name = "member_channel_status")
    private String memberChannelStatus;

    /**
     * pay_account 的primary key，可以在同一个第三方支付系统有多个虚拟子账户
     */
    @Column(name = "member_id")
    private String memberId;

    @Column(name = "member_name")
    private String memberName;

    @Column(name = "member_code")
    private String memberCode;

    /**
     * 账户
     */
    private String account;

    /**
     * 账户名
     */
    @Column(name = "account_name")
    private String accountName;

    /**
     * 会员证件类型
     */
    @Column(name = "id_type")
    private String idType;

    /**
     * 会员证件号码
     */
    @Column(name = "id_code")
    private String idCode;

    /**
     * 第三方支付系统名称
     */
    @Column(name = "ext_sys_name")
    private String extSysName;

    /**
     * 第三方支付系统的资金汇总账号（平台的资金汇总账号，在pay_config里也有配置）
     */
    @Column(name = "ext_sup_acct_id")
    private String extSupAcctId;

    /**
     * 第三方支付系统的虚拟子账户账号
     */
    @Column(name = "ext_cust_acct_id")
    private String extCustAcctId;
    /**
     * 银联钱包类型（用于密码验证设置stradeWayCode字段使用）
     */
    @Column(name = "ext_acct_type")
    private Integer extAcctType;

    @Column(name = "mch_key")
    private String mchKey;

    @Column(name = "mch_no")
    private String mchNo;

    /**
     * 支付宝appid
     */
    @Column(name = "app_id")
    private String appId;

    /**
     * 支付宝公钥
     */
    @Column(name = "public_key")
    private String publicKey;

    /**
     * 支付宝私钥
     */
    @Column(name = "private_key")
    private String privateKey;

    @Column(name = "open_time")
    private Date openTime;

    /**
     * 删除标记
     */
    @Column(name = "del_flg")
    private Boolean delFlg;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    @Column(name = "channel_id")
    private String channelId;

    /**
     * 支付渠道编码
     */
    @Column(name = "channel_code")
    private String channelCode;

    /**
     * 微信小程序、微信扫描支付、微信h5支付(渠道表冗余字段)
     */
    @Column(name = "channel_name")
    private String channelName;

    /**
     * 商业银行；第三方支付平台；(渠道表冗余字段)
     */
    @Column(name = "channel_type")
    private String channelType;

    /**
     * 不可用，已启用(渠道表冗余字段)
     */
    @Column(name = "channel_status")
    private Boolean channelStatus;

    /**
     * 账户控制-是否允许充值(渠道表冗余字段)
     */
    @Column(name = "allow_recharge")
    private Boolean allowRecharge;

    /**
     * 账户控制-是否允许提现(渠道表冗余字段)
     */
    @Column(name = "allow_draw_money")
    private Boolean allowDrawMoney;

    /**
     * 账户控制-是否允许透支(渠道表冗余字段)
     */
    @Column(name = "allow_overdraft")
    private Boolean allowOverdraft;

    /**
     * 账户控制-是否允许退款(渠道表冗余字段)
     */
    @Column(name = "allow_refund")
    private Boolean allowRefund;

    /**
     * 账户控制-是否允许存款(渠道表冗余字段)
     */
    @Column(name = "allow_deposit")
    private Boolean allowDeposit;

    /**
     * 账户控制-是否允许支付(渠道表冗余字段)
     */
    @Column(name = "allow_payment")
    private Boolean allowPayment;

    /**
     * 是否允许重试(渠道表冗余字段)
     */
    @Column(name = "allow_retry")
    private Boolean allowRetry;

    /**
     * 支付收款是否分离,如平安见证宝，支付和收款是在分开的虚拟子账户(渠道表冗余字段)
     */
    @Column(name = "payer_payee_sep")
    private Boolean payerPayeeSep;

    /**
     * 收款方是否需要开通(渠道表冗余字段)
     */
    @Column(name = "need_payee_reg")
    private Boolean needPayeeReg;

    /**
     * 付款方是否需要开通(渠道表冗余字段)
     */
    @Column(name = "need_payer_reg")
    private Boolean needPayerReg;

    /**
     * 费率(渠道表冗余字段)
     */
    @Column(name = "channel_rate")
    private BigDecimal channelRate;

    /**
     * 是否支持app端(渠道表冗余字段)
     */
    @Column(name = "is_support_app")
    private Boolean isSupportApp;

    /**
     * 是否支持web端(渠道表冗余字段)
     */
    @Column(name = "is_support_web")
    private Boolean isSupportWeb;

    /**
     * 是否支持小程序(渠道表冗余字段)
     */
    @Column(name = "is_support_mini")
    private Boolean isSupportMini;

    /**
     * 渠道开通是否需要调用第三方(渠道表冗余字段)
     */
    @Column(name = "open_need_call")
    private Boolean openNeedCall;

    /**
     * 关闭渠道是否需要调用第三方(渠道表冗余字段)
     */
    @Column(name = "close_need_call")
    private Boolean closeNeedCall;

    /**
     * 开通url(渠道表冗余字段)
     */
    @Column(name = "open_url")
    private String openUrl;

    /**
     * 开通url类型(渠道表冗余字段)
     */
    @Column(name = "open_url_type")
    private String openUrlType;

    /**
     * 支持的退款最大时间数（毫秒）（渠道表冗余）
     */
    @Column(name = "max_refund_time")
    private Long maxRefundTime;

    /**
     * 是否可以多次退款
     */
    @Column(name = "can_many_refund")
    private Boolean canManyRefund;

    /**
     * 是否按卖家创建（渠道表冗余）
     */
    @Column(name = "payer_acct_spec")
    private Boolean payerAcctSpec;

    /**
     * 收款方memberId
     */
    @Column(name = "payee_member_id")
    private String payeeMemberId;

    /**
     * 客户关系编码
     */
    @Column(name = "customer_relation_code")
    private String customerRelationCode;

    /**
     * 是否显示余额（渠道表冗余）
     */
    @Column(name = "is_display_balance")
    private Boolean isDisplayBalance;

    /**
     * 渠道支持的最大退款次数（渠道表冗余）
     */
    @Column(name = "max_refund_number")
    private Integer maxRefundNumber;

    /**
     * 渠道支付超时时间
     */
    @Column(name = "pay_timeout")
    private Integer payTimeout;

    /**
     * 是否允许解绑
     */
    @Column(name = "is_allow_unbind")
    private Boolean isAllowUnbind;

    /**
     * 是否允许担保交易
     */
    @Column(name = "is_allow_guarantee")
    private Boolean isAllowGuarantee;

    @Column(name = "allow_receive")
    private Boolean allowReceive;

    @Column(name = "if_aggregation")
    private Boolean ifAggregation;

    @Column(name = "receive_channel_id")
    private String receiveChannelId;

    @Schema(description = "注册号")
    @Column(name = "gnete_register_no")
    private String gneteRegisterNo;
    @Schema(description = "注册状态")
    @Column(name = "gnete_register_status")
    private String gneteRegisterStatus;
    @Schema(description = "注册类型")
    @Column(name = "gnete_register_type")
    private Integer gneteRegisterType;

    @Schema(description = "注册时间")
    @Column(name = "gnete_register_time")
    private Date gneteRegisterTime;

    @Schema(description = "钱包名称")
    @Column(name = "gnete_wallet_name")
    private String gneteWalletName;
    @Schema(description = "签约协议号")
    @Column(name = "gnete_protocol_no")
    private String gneteProtocolNo;
    /**
     *InputStream inputStream = new FileInputStream(new File("C:\\work\\projects\\crcement\\doc\\支付-银联对接相关文档\\2bca893b114a43ff813cb7b14c2679bc.p12"));
     *         ByteArrayOutputStream output = new ByteArrayOutputStream();
     *         byte[] buffer = new byte[1024*4];
     *         int n = 0;
     *         while (-1 != (n = inputStream.read(buffer))) {
     *             output.write(buffer, 0, n);
     *         }
     *         String gnetePublicCert = Hex.encodeHexString(output.toByteArray());
     */
    @Schema(description = "公钥证书")
    @Column(name = "gnete_public_cert")
    private String gnetePublicCert;
    @Schema(description = "私钥证书")
    @Column(name = "gnete_private_cert")
    private String gnetePrivateCert;
    /**
     * Hex.encodeHexString("xxx135!!!".getBytes(StandardCharsets.UTF_8))
     */
    @Schema(description = "私钥证书密码")
    @Column(name = "gnete_private_cert_pwd")
    private String gnetePrivatePwd;


    private String ext1;

    private String ext2;

    private String ext3;

    private String ext4;

    private String ext5;

    @Column(name = "password_status")
    private String passwordStatus;

    @Column(name = "need_password")
    private Boolean needPassword;

    @Column(name = "channel_sequence")
    private Integer channelSequence;

    @Column(name = "balance_amount")
    private BigDecimal balanceAmount;

    @Column(name = "arrears_amount")
    private BigDecimal arrearsAmount;

    @Column(name = "effective_begin_date")
    private Date effectiveBeginDate;

    @Column(name = "effective_end_date")
    private Date effectiveEndDate;

    @Column(name = "arrears_total")
    private BigDecimal arrearsTotal;

    @Column(name = "repayment_total")
    private BigDecimal repaymentTotal;


    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * @return member_channel_id
     */
    public String getMemberChannelId() {
        return memberChannelId;
    }

    /**
     * @param memberChannelId
     */
    public void setMemberChannelId(String memberChannelId) {
        this.memberChannelId = memberChannelId == null ? null : memberChannelId.trim();
    }

    /**
     * @return member_channel_code
     */
    public String getMemberChannelCode() {
        return memberChannelCode;
    }

    /**
     * @param memberChannelCode
     */
    public void setMemberChannelCode(String memberChannelCode) {
        this.memberChannelCode = memberChannelCode == null ? null : memberChannelCode.trim();
    }

    /**
     * 获取已开通,开通中，被停用
     *
     * @return member_channel_status - 已开通,开通中，被停用
     */
    public String getMemberChannelStatus() {
        return memberChannelStatus;
    }

    /**
     * 设置已开通,开通中，被停用
     *
     * @param memberChannelStatus 已开通,开通中，被停用
     */
    public void setMemberChannelStatus(String memberChannelStatus) {
        this.memberChannelStatus = memberChannelStatus == null ? null : memberChannelStatus.trim();
    }

    /**
     * 获取pay_account 的primary key，可以在同一个第三方支付系统有多个虚拟子账户
     *
     * @return member_id - pay_account 的primary key，可以在同一个第三方支付系统有多个虚拟子账户
     */
    public String getMemberId() {
        return memberId;
    }

    /**
     * 设置pay_account 的primary key，可以在同一个第三方支付系统有多个虚拟子账户
     *
     * @param memberId pay_account 的primary key，可以在同一个第三方支付系统有多个虚拟子账户
     */
    public void setMemberId(String memberId) {
        this.memberId = memberId == null ? null : memberId.trim();
    }

    /**
     * @return member_name
     */
    public String getMemberName() {
        return memberName;
    }

    /**
     * @param memberName
     */
    public void setMemberName(String memberName) {
        this.memberName = memberName == null ? null : memberName.trim();
    }

    /**
     * @return member_code
     */
    public String getMemberCode() {
        return memberCode;
    }

    /**
     * @param memberCode
     */
    public void setMemberCode(String memberCode) {
        this.memberCode = memberCode == null ? null : memberCode.trim();
    }

    /**
     * 获取账户
     *
     * @return account - 账户
     */
    public String getAccount() {
        return account;
    }

    /**
     * 设置账户
     *
     * @param account 账户
     */
    public void setAccount(String account) {
        this.account = account == null ? null : account.trim();
    }

    /**
     * 获取账户名
     *
     * @return account_name - 账户名
     */
    public String getAccountName() {
        return accountName;
    }

    /**
     * 设置账户名
     *
     * @param accountName 账户名
     */
    public void setAccountName(String accountName) {
        this.accountName = accountName == null ? null : accountName.trim();
    }

    /**
     * 获取会员证件类型
     *
     * @return id_type - 会员证件类型
     */
    public String getIdType() {
        return idType;
    }

    /**
     * 设置会员证件类型
     *
     * @param idType 会员证件类型
     */
    public void setIdType(String idType) {
        this.idType = idType == null ? null : idType.trim();
    }

    /**
     * 获取会员证件号码
     *
     * @return id_code - 会员证件号码
     */
    public String getIdCode() {
        return idCode;
    }

    /**
     * 设置会员证件号码
     *
     * @param idCode 会员证件号码
     */
    public void setIdCode(String idCode) {
        this.idCode = idCode == null ? null : idCode.trim();
    }

    /**
     * 获取第三方支付系统名称
     *
     * @return ext_sys_name - 第三方支付系统名称
     */
    public String getExtSysName() {
        return extSysName;
    }

    /**
     * 设置第三方支付系统名称
     *
     * @param extSysName 第三方支付系统名称
     */
    public void setExtSysName(String extSysName) {
        this.extSysName = extSysName == null ? null : extSysName.trim();
    }

    /**
     * 获取第三方支付系统的资金汇总账号（平台的资金汇总账号，在pay_config里也有配置）
     *
     * @return ext_sup_acct_id - 第三方支付系统的资金汇总账号（平台的资金汇总账号，在pay_config里也有配置）
     */
    public String getExtSupAcctId() {
        return extSupAcctId;
    }

    /**
     * 设置第三方支付系统的资金汇总账号（平台的资金汇总账号，在pay_config里也有配置）
     *
     * @param extSupAcctId 第三方支付系统的资金汇总账号（平台的资金汇总账号，在pay_config里也有配置）
     */
    public void setExtSupAcctId(String extSupAcctId) {
        this.extSupAcctId = extSupAcctId == null ? null : extSupAcctId.trim();
    }


    /**
     * 获取第三方支付系统的虚拟子账户账号
     *
     * @return ext_cust_acct_id - 第三方支付系统的虚拟子账户账号
     */
    public String getExtCustAcctId() {
        return extCustAcctId;
    }

    /**
     * 设置第三方支付系统的虚拟子账户账号
     *
     * @param extCustAcctId 第三方支付系统的虚拟子账户账号
     */
    public void setExtCustAcctId(String extCustAcctId) {
        this.extCustAcctId = extCustAcctId == null ? null : extCustAcctId.trim();
    }

    public Integer getExtAcctType() {
        return extAcctType;
    }

    public void setExtAcctType(Integer extAcctType) {
        this.extAcctType = extAcctType;
    }

    /**
     * @return mch_key
     */
    public String getMchKey() {
        return mchKey;
    }

    /**
     * @param mchKey
     */
    public void setMchKey(String mchKey) {
        this.mchKey = mchKey == null ? null : mchKey.trim();
    }

    /**
     * @return mch_no
     */
    public String getMchNo() {
        return mchNo;
    }

    /**
     * @param mchNo
     */
    public void setMchNo(String mchNo) {
        this.mchNo = mchNo == null ? null : mchNo.trim();
    }

    /**
     * 获取支付宝appid
     *
     * @return app_id - 支付宝appid
     */
    public String getAppId() {
        return appId;
    }

    /**
     * 设置支付宝appid
     *
     * @param appId 支付宝appid
     */
    public void setAppId(String appId) {
        this.appId = appId == null ? null : appId.trim();
    }

    /**
     * 获取支付宝公钥
     *
     * @return public_key - 支付宝公钥
     */
    public String getPublicKey() {
        return publicKey;
    }

    /**
     * 设置支付宝公钥
     *
     * @param publicKey 支付宝公钥
     */
    public void setPublicKey(String publicKey) {
        this.publicKey = publicKey == null ? null : publicKey.trim();
    }

    /**
     * 获取支付宝私钥
     *
     * @return private_key - 支付宝私钥
     */
    public String getPrivateKey() {
        return privateKey;
    }

    /**
     * 设置支付宝私钥
     *
     * @param privateKey 支付宝私钥
     */
    public void setPrivateKey(String privateKey) {
        this.privateKey = privateKey == null ? null : privateKey.trim();
    }

    /**
     * @return open_time
     */
    public Date getOpenTime() {
        return openTime;
    }

    /**
     * @param openTime
     */
    public void setOpenTime(Date openTime) {
        this.openTime = openTime;
    }

    /**
     * 获取删除标记
     *
     * @return del_flg - 删除标记
     */
    public Boolean getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标记
     *
     * @param delFlg 删除标记
     */
    public void setDelFlg(Boolean delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建人
     *
     * @return create_user - 创建人
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建人
     *
     * @param createUser 创建人
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取修改人
     *
     * @return update_user - 修改人
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置修改人
     *
     * @param updateUser 修改人
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取修改时间
     *
     * @return update_time - 修改时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置修改时间
     *
     * @param updateTime 修改时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * @return channel_id
     */
    public String getChannelId() {
        return channelId;
    }

    /**
     * @param channelId
     */
    public void setChannelId(String channelId) {
        this.channelId = channelId == null ? null : channelId.trim();
    }

    /**
     * 获取支付渠道编码
     *
     * @return channel_code - 支付渠道编码
     */
    public String getChannelCode() {
        return channelCode;
    }

    /**
     * 设置支付渠道编码
     *
     * @param channelCode 支付渠道编码
     */
    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode == null ? null : channelCode.trim();
    }

    /**
     * 获取微信小程序、微信扫描支付、微信h5支付(渠道表冗余字段)
     *
     * @return channel_name - 微信小程序、微信扫描支付、微信h5支付(渠道表冗余字段)
     */
    public String getChannelName() {
        return channelName;
    }

    /**
     * 设置微信小程序、微信扫描支付、微信h5支付(渠道表冗余字段)
     *
     * @param channelName 微信小程序、微信扫描支付、微信h5支付(渠道表冗余字段)
     */
    public void setChannelName(String channelName) {
        this.channelName = channelName == null ? null : channelName.trim();
    }

    /**
     * 获取商业银行；第三方支付平台；(渠道表冗余字段)
     *
     * @return channel_type - 商业银行；第三方支付平台；(渠道表冗余字段)
     */
    public String getChannelType() {
        return channelType;
    }

    /**
     * 设置商业银行；第三方支付平台；(渠道表冗余字段)
     *
     * @param channelType 商业银行；第三方支付平台；(渠道表冗余字段)
     */
    public void setChannelType(String channelType) {
        this.channelType = channelType == null ? null : channelType.trim();
    }

    /**
     * 获取不可用，已启用(渠道表冗余字段)
     *
     * @return channel_status - 不可用，已启用(渠道表冗余字段)
     */
    public Boolean getChannelStatus() {
        return channelStatus;
    }

    /**
     * 设置不可用，已启用(渠道表冗余字段)
     *
     * @param channelStatus 不可用，已启用(渠道表冗余字段)
     */
    public void setChannelStatus(Boolean channelStatus) {
        this.channelStatus = channelStatus;
    }

    /**
     * 获取账户控制-是否允许充值(渠道表冗余字段)
     *
     * @return allow_recharge - 账户控制-是否允许充值(渠道表冗余字段)
     */
    public Boolean getAllowRecharge() {
        return allowRecharge;
    }

    /**
     * 设置账户控制-是否允许充值(渠道表冗余字段)
     *
     * @param allowRecharge 账户控制-是否允许充值(渠道表冗余字段)
     */
    public void setAllowRecharge(Boolean allowRecharge) {
        this.allowRecharge = allowRecharge;
    }

    /**
     * 获取账户控制-是否允许提现(渠道表冗余字段)
     *
     * @return allow_draw_money - 账户控制-是否允许提现(渠道表冗余字段)
     */
    public Boolean getAllowDrawMoney() {
        return allowDrawMoney;
    }

    /**
     * 设置账户控制-是否允许提现(渠道表冗余字段)
     *
     * @param allowDrawMoney 账户控制-是否允许提现(渠道表冗余字段)
     */
    public void setAllowDrawMoney(Boolean allowDrawMoney) {
        this.allowDrawMoney = allowDrawMoney;
    }

    /**
     * 获取账户控制-是否允许透支(渠道表冗余字段)
     *
     * @return allow_overdraft - 账户控制-是否允许透支(渠道表冗余字段)
     */
    public Boolean getAllowOverdraft() {
        return allowOverdraft;
    }

    /**
     * 设置账户控制-是否允许透支(渠道表冗余字段)
     *
     * @param allowOverdraft 账户控制-是否允许透支(渠道表冗余字段)
     */
    public void setAllowOverdraft(Boolean allowOverdraft) {
        this.allowOverdraft = allowOverdraft;
    }

    /**
     * 获取账户控制-是否允许退款(渠道表冗余字段)
     *
     * @return allow_refund - 账户控制-是否允许退款(渠道表冗余字段)
     */
    public Boolean getAllowRefund() {
        return allowRefund;
    }

    /**
     * 设置账户控制-是否允许退款(渠道表冗余字段)
     *
     * @param allowRefund 账户控制-是否允许退款(渠道表冗余字段)
     */
    public void setAllowRefund(Boolean allowRefund) {
        this.allowRefund = allowRefund;
    }

    /**
     * 获取账户控制-是否允许存款(渠道表冗余字段)
     *
     * @return allow_deposit - 账户控制-是否允许存款(渠道表冗余字段)
     */
    public Boolean getAllowDeposit() {
        return allowDeposit;
    }

    /**
     * 设置账户控制-是否允许存款(渠道表冗余字段)
     *
     * @param allowDeposit 账户控制-是否允许存款(渠道表冗余字段)
     */
    public void setAllowDeposit(Boolean allowDeposit) {
        this.allowDeposit = allowDeposit;
    }

    /**
     * 获取账户控制-是否允许支付(渠道表冗余字段)
     *
     * @return allow_payment - 账户控制-是否允许支付(渠道表冗余字段)
     */
    public Boolean getAllowPayment() {
        return allowPayment;
    }

    /**
     * 设置账户控制-是否允许支付(渠道表冗余字段)
     *
     * @param allowPayment 账户控制-是否允许支付(渠道表冗余字段)
     */
    public void setAllowPayment(Boolean allowPayment) {
        this.allowPayment = allowPayment;
    }

    /**
     * 获取是否允许重试(渠道表冗余字段)
     *
     * @return allow_retry - 是否允许重试(渠道表冗余字段)
     */
    public Boolean getAllowRetry() {
        return allowRetry;
    }

    /**
     * 设置是否允许重试(渠道表冗余字段)
     *
     * @param allowRetry 是否允许重试(渠道表冗余字段)
     */
    public void setAllowRetry(Boolean allowRetry) {
        this.allowRetry = allowRetry;
    }

    /**
     * 获取支付收款是否分离,如平安见证宝，支付和收款是在分开的虚拟子账户(渠道表冗余字段)
     *
     * @return payer_payee_sep - 支付收款是否分离,如平安见证宝，支付和收款是在分开的虚拟子账户(渠道表冗余字段)
     */
    public Boolean getPayerPayeeSep() {
        return payerPayeeSep;
    }

    /**
     * 设置支付收款是否分离,如平安见证宝，支付和收款是在分开的虚拟子账户(渠道表冗余字段)
     *
     * @param payerPayeeSep 支付收款是否分离,如平安见证宝，支付和收款是在分开的虚拟子账户(渠道表冗余字段)
     */
    public void setPayerPayeeSep(Boolean payerPayeeSep) {
        this.payerPayeeSep = payerPayeeSep;
    }

    /**
     * 获取收款方是否需要开通(渠道表冗余字段)
     *
     * @return need_payee_reg - 收款方是否需要开通(渠道表冗余字段)
     */
    public Boolean getNeedPayeeReg() {
        return needPayeeReg;
    }

    /**
     * 设置收款方是否需要开通(渠道表冗余字段)
     *
     * @param needPayeeReg 收款方是否需要开通(渠道表冗余字段)
     */
    public void setNeedPayeeReg(Boolean needPayeeReg) {
        this.needPayeeReg = needPayeeReg;
    }

    /**
     * 获取付款方是否需要开通(渠道表冗余字段)
     *
     * @return need_payer_reg - 付款方是否需要开通(渠道表冗余字段)
     */
    public Boolean getNeedPayerReg() {
        return needPayerReg;
    }

    /**
     * 设置付款方是否需要开通(渠道表冗余字段)
     *
     * @param needPayerReg 付款方是否需要开通(渠道表冗余字段)
     */
    public void setNeedPayerReg(Boolean needPayerReg) {
        this.needPayerReg = needPayerReg;
    }

    /**
     * 获取费率(渠道表冗余字段)
     *
     * @return channel_rate - 费率(渠道表冗余字段)
     */
    public BigDecimal getChannelRate() {
        return channelRate;
    }

    /**
     * 设置费率(渠道表冗余字段)
     *
     * @param channelRate 费率(渠道表冗余字段)
     */
    public void setChannelRate(BigDecimal channelRate) {
        this.channelRate = channelRate;
    }

    /**
     * 获取是否支持app端(渠道表冗余字段)
     *
     * @return is_support_app - 是否支持app端(渠道表冗余字段)
     */
    public Boolean getIsSupportApp() {
        return isSupportApp;
    }

    /**
     * 设置是否支持app端(渠道表冗余字段)
     *
     * @param isSupportApp 是否支持app端(渠道表冗余字段)
     */
    public void setIsSupportApp(Boolean isSupportApp) {
        this.isSupportApp = isSupportApp;
    }

    /**
     * 获取是否支持web端(渠道表冗余字段)
     *
     * @return is_support_web - 是否支持web端(渠道表冗余字段)
     */
    public Boolean getIsSupportWeb() {
        return isSupportWeb;
    }

    /**
     * 设置是否支持web端(渠道表冗余字段)
     *
     * @param isSupportWeb 是否支持web端(渠道表冗余字段)
     */
    public void setIsSupportWeb(Boolean isSupportWeb) {
        this.isSupportWeb = isSupportWeb;
    }

    /**
     * 获取是否支持小程序(渠道表冗余字段)
     *
     * @return is_support_mini - 是否支持小程序(渠道表冗余字段)
     */
    public Boolean getIsSupportMini() {
        return isSupportMini;
    }

    /**
     * 设置是否支持小程序(渠道表冗余字段)
     *
     * @param isSupportMini 是否支持小程序(渠道表冗余字段)
     */
    public void setIsSupportMini(Boolean isSupportMini) {
        this.isSupportMini = isSupportMini;
    }

    /**
     * 获取渠道开通是否需要调用第三方(渠道表冗余字段)
     *
     * @return open_need_call - 渠道开通是否需要调用第三方(渠道表冗余字段)
     */
    public Boolean getOpenNeedCall() {
        return openNeedCall;
    }

    /**
     * 设置渠道开通是否需要调用第三方(渠道表冗余字段)
     *
     * @param openNeedCall 渠道开通是否需要调用第三方(渠道表冗余字段)
     */
    public void setOpenNeedCall(Boolean openNeedCall) {
        this.openNeedCall = openNeedCall;
    }

    /**
     * 获取关闭渠道是否需要调用第三方(渠道表冗余字段)
     *
     * @return close_need_call - 关闭渠道是否需要调用第三方(渠道表冗余字段)
     */
    public Boolean getCloseNeedCall() {
        return closeNeedCall;
    }

    /**
     * 设置关闭渠道是否需要调用第三方(渠道表冗余字段)
     *
     * @param closeNeedCall 关闭渠道是否需要调用第三方(渠道表冗余字段)
     */
    public void setCloseNeedCall(Boolean closeNeedCall) {
        this.closeNeedCall = closeNeedCall;
    }

    /**
     * 获取开通url(渠道表冗余字段)
     *
     * @return open_url - 开通url(渠道表冗余字段)
     */
    public String getOpenUrl() {
        return openUrl;
    }

    /**
     * 设置开通url(渠道表冗余字段)
     *
     * @param openUrl 开通url(渠道表冗余字段)
     */
    public void setOpenUrl(String openUrl) {
        this.openUrl = openUrl == null ? null : openUrl.trim();
    }

    /**
     * 获取开通url类型(渠道表冗余字段)
     *
     * @return open_url_type - 开通url类型(渠道表冗余字段)
     */
    public String getOpenUrlType() {
        return openUrlType;
    }

    /**
     * 设置开通url类型(渠道表冗余字段)
     *
     * @param openUrlType 开通url类型(渠道表冗余字段)
     */
    public void setOpenUrlType(String openUrlType) {
        this.openUrlType = openUrlType == null ? null : openUrlType.trim();
    }

    /**
     * 获取支持的退款最大时间数（毫秒）（渠道表冗余）
     *
     * @return max_refund_time - 支持的退款最大时间数（毫秒）（渠道表冗余）
     */
    public Long getMaxRefundTime() {
        return maxRefundTime;
    }

    /**
     * 设置支持的退款最大时间数（毫秒）（渠道表冗余）
     *
     * @param maxRefundTime 支持的退款最大时间数（毫秒）（渠道表冗余）
     */
    public void setMaxRefundTime(Long maxRefundTime) {
        this.maxRefundTime = maxRefundTime;
    }

    /**
     * 获取是否可以多次退款
     *
     * @return can_many_refund - 是否可以多次退款
     */
    public Boolean getCanManyRefund() {
        return canManyRefund;
    }

    /**
     * 设置是否可以多次退款
     *
     * @param canManyRefund 是否可以多次退款
     */
    public void setCanManyRefund(Boolean canManyRefund) {
        this.canManyRefund = canManyRefund;
    }

    /**
     * 获取是否按卖家创建（渠道表冗余）
     *
     * @return payer_acct_spec - 是否按卖家创建（渠道表冗余）
     */
    public Boolean getPayerAcctSpec() {
        return payerAcctSpec;
    }

    /**
     * 设置是否按卖家创建（渠道表冗余）
     *
     * @param payerAcctSpec 是否按卖家创建（渠道表冗余）
     */
    public void setPayerAcctSpec(Boolean payerAcctSpec) {
        this.payerAcctSpec = payerAcctSpec;
    }

    /**
     * 获取收款方memberId
     *
     * @return payee_member_id - 收款方memberId
     */
    public String getPayeeMemberId() {
        return payeeMemberId;
    }

    /**
     * 设置收款方memberId
     *
     * @param payeeMemberId 收款方memberId
     */
    public void setPayeeMemberId(String payeeMemberId) {
        this.payeeMemberId = payeeMemberId == null ? null : payeeMemberId.trim();
    }

    /**
     * 获取客户关系编码
     *
     * @return customer_relation_code - 客户关系编码
     */
    public String getCustomerRelationCode() {
        return customerRelationCode;
    }

    /**
     * 设置客户关系编码
     *
     * @param customerRelationCode 客户关系编码
     */
    public void setCustomerRelationCode(String customerRelationCode) {
        this.customerRelationCode = customerRelationCode == null ? null : customerRelationCode.trim();
    }

    /**
     * 获取是否显示余额（渠道表冗余）
     *
     * @return is_display_balance - 是否显示余额（渠道表冗余）
     */
    public Boolean getIsDisplayBalance() {
        return isDisplayBalance;
    }

    /**
     * 设置是否显示余额（渠道表冗余）
     *
     * @param isDisplayBalance 是否显示余额（渠道表冗余）
     */
    public void setIsDisplayBalance(Boolean isDisplayBalance) {
        this.isDisplayBalance = isDisplayBalance;
    }

    /**
     * 获取渠道支持的最大退款次数（渠道表冗余）
     *
     * @return max_refund_number - 渠道支持的最大退款次数（渠道表冗余）
     */
    public Integer getMaxRefundNumber() {
        return maxRefundNumber;
    }

    /**
     * 设置渠道支持的最大退款次数（渠道表冗余）
     *
     * @param maxRefundNumber 渠道支持的最大退款次数（渠道表冗余）
     */
    public void setMaxRefundNumber(Integer maxRefundNumber) {
        this.maxRefundNumber = maxRefundNumber;
    }

    /**
     * 获取渠道支付超时时间
     *
     * @return pay_timeout - 渠道支付超时时间
     */
    public Integer getPayTimeout() {
        return payTimeout;
    }

    /**
     * 设置渠道支付超时时间
     *
     * @param payTimeout 渠道支付超时时间
     */
    public void setPayTimeout(Integer payTimeout) {
        this.payTimeout = payTimeout;
    }

    /**
     * 获取是否允许解绑
     *
     * @return is_allow_unbind - 是否允许解绑
     */
    public Boolean getIsAllowUnbind() {
        return isAllowUnbind;
    }

    /**
     * 设置是否允许解绑
     *
     * @param isAllowUnbind 是否允许解绑
     */
    public void setIsAllowUnbind(Boolean isAllowUnbind) {
        this.isAllowUnbind = isAllowUnbind;
    }

    /**
     * 获取是否允许担保交易
     *
     * @return is_allow_guarantee - 是否允许担保交易
     */
    public Boolean getIsAllowGuarantee() {
        return isAllowGuarantee;
    }

    /**
     * 设置是否允许担保交易
     *
     * @param isAllowGuarantee 是否允许担保交易
     */
    public void setIsAllowGuarantee(Boolean isAllowGuarantee) {
        this.isAllowGuarantee = isAllowGuarantee;
    }

    /**
     * @return allow_receive
     */
    public Boolean getAllowReceive() {
        return allowReceive;
    }

    /**
     * @param allowReceive
     */
    public void setAllowReceive(Boolean allowReceive) {
        this.allowReceive = allowReceive;
    }

    /**
     * @return if_aggregation
     */
    public Boolean getIfAggregation() {
        return ifAggregation;
    }

    /**
     * @param ifAggregation
     */
    public void setIfAggregation(Boolean ifAggregation) {
        this.ifAggregation = ifAggregation;
    }

    /**
     * @return receive_channel_id
     */
    public String getReceiveChannelId() {
        return receiveChannelId;
    }

    /**
     * @param receiveChannelId
     */
    public void setReceiveChannelId(String receiveChannelId) {
        this.receiveChannelId = receiveChannelId == null ? null : receiveChannelId.trim();
    }

    public String getGneteRegisterNo() {
        return gneteRegisterNo;
    }

    public void setGneteRegisterNo(String gneteRegisterNo) {
        this.gneteRegisterNo = gneteRegisterNo;
    }

    public String getGneteRegisterStatus() {
        return gneteRegisterStatus;
    }

    public void setGneteRegisterStatus(String gneteRegisterStatus) {
        this.gneteRegisterStatus = gneteRegisterStatus;
    }

    public Integer getGneteRegisterType() {
        return gneteRegisterType;
    }

    public void setGneteRegisterType(Integer gneteRegisterType) {
        this.gneteRegisterType = gneteRegisterType;
    }

    public Date getGneteRegisterTime() {
        return gneteRegisterTime;
    }

    public void setGneteRegisterTime(Date gneteRegisterTime) {
        this.gneteRegisterTime = gneteRegisterTime;
    }

    public String getGneteWalletName() {
        return gneteWalletName;
    }

    public void setGneteWalletName(String gneteWalletName) {
        this.gneteWalletName = gneteWalletName;
    }

    public String getGneteProtocolNo() {
        return gneteProtocolNo;
    }

    public void setGneteProtocolNo(String gneteProtocolNo) {
        this.gneteProtocolNo = gneteProtocolNo;
    }

    public String getGnetePublicCert() {
        return gnetePublicCert;
    }

    public void setGnetePublicCert(String gnetePublicCert) {
        this.gnetePublicCert = gnetePublicCert;
    }

    public String getGnetePrivateCert() {
        return gnetePrivateCert;
    }

    public void setGnetePrivateCert(String gnetePrivateCert) {
        this.gnetePrivateCert = gnetePrivateCert;
    }

    public String getGnetePrivatePwd() {
        return gnetePrivatePwd;
    }

    public void setGnetePrivatePwd(String gnetePrivatePwd) {
        this.gnetePrivatePwd = gnetePrivatePwd;
    }

    /**
     * @return ext1
     */
    public String getExt1() {
        return ext1;
    }

    /**
     * @param ext1
     */
    public void setExt1(String ext1) {
        this.ext1 = ext1 == null ? null : ext1.trim();
    }

    /**
     * @return ext2
     */
    public String getExt2() {
        return ext2;
    }

    /**
     * @param ext2
     */
    public void setExt2(String ext2) {
        this.ext2 = ext2 == null ? null : ext2.trim();
    }

    /**
     * @return ext3
     */
    public String getExt3() {
        return ext3;
    }

    /**
     * @param ext3
     */
    public void setExt3(String ext3) {
        this.ext3 = ext3 == null ? null : ext3.trim();
    }

    /**
     * @return ext4
     */
    public String getExt4() {
        return ext4;
    }

    /**
     * @param ext4
     */
    public void setExt4(String ext4) {
        this.ext4 = ext4 == null ? null : ext4.trim();
    }

    /**
     * @return ext5
     */
    public String getExt5() {
        return ext5;
    }

    /**
     * @param ext5
     */
    public void setExt5(String ext5) {
        this.ext5 = ext5 == null ? null : ext5.trim();
    }

    /**
     * @return password_status
     */
    public String getPasswordStatus() {
        return passwordStatus;
    }

    /**
     * @param passwordStatus
     */
    public void setPasswordStatus(String passwordStatus) {
        this.passwordStatus = passwordStatus == null ? null : passwordStatus.trim();
    }

    /**
     * @return need_password
     */
    public Boolean getNeedPassword() {
        return needPassword;
    }

    /**
     * @param needPassword
     */
    public void setNeedPassword(Boolean needPassword) {
        this.needPassword = needPassword;
    }

    /**
     * @return channel_sequence
     */
    public Integer getChannelSequence() {
        return channelSequence;
    }

    /**
     * @param channelSequence
     */
    public void setChannelSequence(Integer channelSequence) {
        this.channelSequence = channelSequence;
    }

    /**
     * @return balance_amount
     */
    public BigDecimal getBalanceAmount() {
        return balanceAmount;
    }

    /**
     * @param balanceAmount
     */
    public void setBalanceAmount(BigDecimal balanceAmount) {
        this.balanceAmount = balanceAmount;
    }

    /**
     * @return arrears_amount
     */
    public BigDecimal getArrearsAmount() {
        return arrearsAmount;
    }

    /**
     * @param arrearsAmount
     */
    public void setArrearsAmount(BigDecimal arrearsAmount) {
        this.arrearsAmount = arrearsAmount;
    }

    public Date getEffectiveBeginDate()
    {
        return effectiveBeginDate;
    }

    public void setEffectiveBeginDate(Date effectiveBeginDate)
    {
        this.effectiveBeginDate = effectiveBeginDate;
    }

    public Date getEffectiveEndDate()
    {
        return effectiveEndDate;
    }

    public void setEffectiveEndDate(Date effectiveEndDate)
    {
        this.effectiveEndDate = effectiveEndDate;
    }

    public BigDecimal getArrearsTotal()
    {
        return arrearsTotal;
    }

    public void setArrearsTotal(BigDecimal arrearsTotal)
    {
        this.arrearsTotal = arrearsTotal;
    }

    public BigDecimal getRepaymentTotal()
    {
        return repaymentTotal;
    }

    public void setRepaymentTotal(BigDecimal repaymentTotal)
    {
        this.repaymentTotal = repaymentTotal;
    }

    @Override
    public String toString() {
        return "MemberChannel{" +
                "memberChannelId='" + memberChannelId + '\'' +
                ", memberChannelCode='" + memberChannelCode + '\'' +
                ", memberChannelStatus='" + memberChannelStatus + '\'' +
                ", memberId='" + memberId + '\'' +
                ", memberName='" + memberName + '\'' +
                ", memberCode='" + memberCode + '\'' +
                ", account='" + account + '\'' +
                ", accountName='" + accountName + '\'' +
                ", idType='" + idType + '\'' +
                ", idCode='" + idCode + '\'' +
                ", extSysName='" + extSysName + '\'' +
                ", extSupAcctId='" + extSupAcctId + '\'' +
                ", extCustAcctId='" + extCustAcctId + '\'' +
                ", extAcctType=" + extAcctType +
                ", mchKey='" + mchKey + '\'' +
                ", mchNo='" + mchNo + '\'' +
                ", appId='" + appId + '\'' +
                ", publicKey='" + publicKey + '\'' +
                ", privateKey='" + privateKey + '\'' +
                ", openTime=" + openTime +
                ", delFlg=" + delFlg +
                ", createUser='" + createUser + '\'' +
                ", createTime=" + createTime +
                ", updateUser='" + updateUser + '\'' +
                ", updateTime=" + updateTime +
                ", channelId='" + channelId + '\'' +
                ", channelCode='" + channelCode + '\'' +
                ", channelName='" + channelName + '\'' +
                ", channelType='" + channelType + '\'' +
                ", channelStatus=" + channelStatus +
                ", allowRecharge=" + allowRecharge +
                ", allowDrawMoney=" + allowDrawMoney +
                ", allowOverdraft=" + allowOverdraft +
                ", allowRefund=" + allowRefund +
                ", allowDeposit=" + allowDeposit +
                ", allowPayment=" + allowPayment +
                ", allowRetry=" + allowRetry +
                ", payerPayeeSep=" + payerPayeeSep +
                ", needPayeeReg=" + needPayeeReg +
                ", needPayerReg=" + needPayerReg +
                ", channelRate=" + channelRate +
                ", isSupportApp=" + isSupportApp +
                ", isSupportWeb=" + isSupportWeb +
                ", isSupportMini=" + isSupportMini +
                ", openNeedCall=" + openNeedCall +
                ", closeNeedCall=" + closeNeedCall +
                ", openUrl='" + openUrl + '\'' +
                ", openUrlType='" + openUrlType + '\'' +
                ", maxRefundTime=" + maxRefundTime +
                ", canManyRefund=" + canManyRefund +
                ", payerAcctSpec=" + payerAcctSpec +
                ", payeeMemberId='" + payeeMemberId + '\'' +
                ", customerRelationCode='" + customerRelationCode + '\'' +
                ", isDisplayBalance=" + isDisplayBalance +
                ", maxRefundNumber=" + maxRefundNumber +
                ", payTimeout=" + payTimeout +
                ", isAllowUnbind=" + isAllowUnbind +
                ", isAllowGuarantee=" + isAllowGuarantee +
                ", allowReceive=" + allowReceive +
                ", ifAggregation=" + ifAggregation +
                ", receiveChannelId='" + receiveChannelId + '\'' +
                ", gneteRegisterNo='" + gneteRegisterNo + '\'' +
                ", gneteRegisterStatus='" + gneteRegisterStatus + '\'' +
                ", gneteRegisterType=" + gneteRegisterType +
                ", gneteRegisterTime=" + gneteRegisterTime +
                ", gneteWalletName='" + gneteWalletName + '\'' +
                ", gneteProtocolNo='" + gneteProtocolNo + '\'' +
                ", gnetePublicCert=" + (gnetePublicCert) +
                ", gnetePrivateCert=" + (gnetePrivateCert) +
                ", gnetePrivatePwd=" + (gnetePrivatePwd) +
                ", ext1='" + ext1 + '\'' +
                ", ext2='" + ext2 + '\'' +
                ", ext3='" + ext3 + '\'' +
                ", ext4='" + ext4 + '\'' +
                ", ext5='" + ext5 + '\'' +
                ", passwordStatus='" + passwordStatus + '\'' +
                ", needPassword=" + needPassword +
                ", channelSequence=" + channelSequence +
                ", balanceAmount=" + balanceAmount +
                ", arrearsAmount=" + arrearsAmount +
                ", effectiveBeginDate=" + effectiveBeginDate +
                ", effectiveEndDate=" + effectiveEndDate +
                ", arrearsTotal=" + arrearsTotal +
                ", repaymentTotal=" + repaymentTotal +
                '}';
    }
}
