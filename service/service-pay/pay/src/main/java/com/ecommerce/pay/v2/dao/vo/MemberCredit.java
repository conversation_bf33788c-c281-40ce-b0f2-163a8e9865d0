package com.ecommerce.pay.v2.dao.vo;

import com.alibaba.fastjson.JSON;

import jakarta.persistence.Column;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Table(name = "pa_member_credit")
public class MemberCredit implements Serializable {

    /**
     * ID
     */
    @Id
    @Column(name = "member_credit_id")
    private String memberCreditId;

    /**
     * 买家ID
     */
    @Column(name = "member_id")
    private String memberId;

    /**
     * 买家名称
     */
    @Column(name = "member_name")
    private String memberName;

    /**
     * 买家编码
     */
    @Column(name = "member_code")
    private String memberCode;

    /**
     * 卖家ID
     */
    @Column(name = "payee_member_id")
    private String payeeMemberId;

    /**
     * 卖家名称
     */
    @Column(name = "payee_member_name")
    private String payeeMemberName;

    /**
     * 卖家编码
     */
    @Column(name = "payee_member_code")
    private String payeeMemberCode;

    /**
     * 授信额度 单位：元
     */
    @Column(name = "credit_amount")
    private BigDecimal creditAmount;

    /**
     * 授信开始时间
     */
    @Column(name = "start_date")
    private Date startDate;

    /**
     * 授信结束时间
     */
    @Column(name = "end_date")
    private Date endDate;

    /**
     * 使用状态 0：已过期、1：暂未使用、2：使用中
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 删除标记 0：未删除、1：已删除
     */
    @Column(name = "del_flg")
    private Boolean delFlg;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    @Serial
    private static final long serialVersionUID = 1L;


    public String getMemberCreditId()
    {
        return memberCreditId;
    }
    public void setMemberCreditId(String memberCreditId)
    {
        this.memberCreditId = memberCreditId;
    }

    /**
     * 获取买家ID
     *
     * @return member_id - 买家ID
     */
    public String getMemberId() {
        return memberId;
    }

    /**
     * 设置买家ID
     *
     * @param memberId 买家ID
     */
    public void setMemberId(String memberId) {
        this.memberId = memberId == null ? null : memberId.trim();
    }

    /**
     * 获取买家名称
     *
     * @return member_name - 买家名称
     */
    public String getMemberName() {
        return memberName;
    }

    /**
     * 设置买家名称
     *
     * @param memberName 买家名称
     */
    public void setMemberName(String memberName) {
        this.memberName = memberName == null ? null : memberName.trim();
    }

    /**
     * 获取买家编码
     *
     * @return member_code - 买家编码
     */
    public String getMemberCode() {
        return memberCode;
    }

    /**
     * 设置买家编码
     *
     * @param memberCode 买家编码
     */
    public void setMemberCode(String memberCode) {
        this.memberCode = memberCode == null ? null : memberCode.trim();
    }

    /**
     * 获取卖家ID
     *
     * @return payee_member_id - 卖家ID
     */
    public String getPayeeMemberId() {
        return payeeMemberId;
    }

    /**
     * 设置卖家ID
     *
     * @param payeeMemberId 卖家ID
     */
    public void setPayeeMemberId(String payeeMemberId) {
        this.payeeMemberId = payeeMemberId == null ? null : payeeMemberId.trim();
    }

    /**
     * 获取卖家名称
     *
     * @return payee_member_name - 卖家名称
     */
    public String getPayeeMemberName() {
        return payeeMemberName;
    }

    /**
     * 设置卖家名称
     *
     * @param payeeMemberName 卖家名称
     */
    public void setPayeeMemberName(String payeeMemberName) {
        this.payeeMemberName = payeeMemberName == null ? null : payeeMemberName.trim();
    }

    /**
     * 获取卖家编码
     *
     * @return payee_member_code - 卖家编码
     */
    public String getPayeeMemberCode() {
        return payeeMemberCode;
    }

    /**
     * 设置卖家编码
     *
     * @param payeeMemberCode 卖家编码
     */
    public void setPayeeMemberCode(String payeeMemberCode) {
        this.payeeMemberCode = payeeMemberCode == null ? null : payeeMemberCode.trim();
    }

    /**
     * 获取授信额度 单位：元
     *
     * @return credit_amount - 授信额度 单位：元
     */
    public BigDecimal getCreditAmount() {
        return creditAmount;
    }

    /**
     * 设置授信额度 单位：元
     *
     * @param creditAmount 授信额度 单位：元
     */
    public void setCreditAmount(BigDecimal creditAmount) {
        this.creditAmount = creditAmount;
    }

    /**
     * 获取授信开始时间
     *
     * @return start_date - 授信开始时间
     */
    public Date getStartDate() {
        return startDate;
    }

    /**
     * 设置授信开始时间
     *
     * @param startDate 授信开始时间
     */
    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    /**
     * 获取授信结束时间
     *
     * @return end_date - 授信结束时间
     */
    public Date getEndDate() {
        return endDate;
    }

    /**
     * 设置授信结束时间
     *
     * @param endDate 授信结束时间
     */
    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    /**
     * 获取使用状态 1：启用
     * @return status - 使用状态 1：启用
     */
    public Integer getStatus()
    {
        return status;
    }

    /**
     * 设置使用状态 1：启用
     * @param status 使用状态 1：启用
     */
    public void setStatus(Integer status)
    {
        this.status = status;
    }

    /**
     * 获取删除标记 0：未删除、1：已删除
     *
     * @return del_flg - 删除标记 0：未删除、1：已删除
     */
    public Boolean getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标记 0：未删除、1：已删除
     *
     * @param delFlg 删除标记 0：未删除、1：已删除
     */
    public void setDelFlg(Boolean delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建人
     *
     * @return create_user - 创建人
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建人
     *
     * @param createUser 创建人
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新人
     *
     * @return update_user - 更新人
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新人
     *
     * @param updateUser 更新人
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString()
    {
        return JSON.toJSONString(this);
    }
}
