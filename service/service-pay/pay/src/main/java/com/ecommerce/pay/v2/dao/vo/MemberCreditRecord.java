package com.ecommerce.pay.v2.dao.vo;

import jakarta.persistence.Column;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Table(name = "pa_member_credit_record")
public class MemberCreditRecord implements Serializable {
    /**
     * ID
     */
    @Id
    private Integer id;

    /**
     * 买家ID
     */
    @Column(name = "member_id")
    private String memberId;

    /**
     * 买家CODE
     */
    @Column(name = "member_code")
    private String memberCode;

    /**
     * 买家名称
     */
    @Column(name = "member_name")
    private String memberName;

    /**
     * 卖家ID
     */
    @Column(name = "payee_member_id")
    private String payeeMemberId;

    /**
     * 卖家名称
     */
    @Column(name = "payee_member_name")
    private String payeeMemberName;

    /**
     * 记录类型 1：支付、2：还款、3：退款
     */
    private Integer type;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 流水号
     */
    private String billNo;

    /**
     * 备注
     */
    private String memo;

    /**
     * 附件
     */
    @Column(name = "file_attach")
    private String fileAttach;

    /**
     * 是否删除 0：否、1：是
     */
    @Column(name = "del_flg")
    private Boolean delFlg;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 获取ID
     *
     * @return id - ID
     */
    public Integer getId() {
        return id;
    }

    /**
     * 设置ID
     *
     * @param id ID
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * 获取买家ID
     *
     * @return member_id - 买家ID
     */
    public String getMemberId() {
        return memberId;
    }

    /**
     * 设置买家ID
     *
     * @param memberId 买家ID
     */
    public void setMemberId(String memberId) {
        this.memberId = memberId == null ? null : memberId.trim();
    }

    /**
     * 获取买家CODE
     *
     * @return member_code - 买家CODE
     */
    public String getMemberCode() {
        return memberCode;
    }

    /**
     * 设置买家CODE
     *
     * @param memberCode 买家CODE
     */
    public void setMemberCode(String memberCode) {
        this.memberCode = memberCode == null ? null : memberCode.trim();
    }

    /**
     * 获取买家名称
     *
     * @return member_name - 买家名称
     */
    public String getMemberName() {
        return memberName;
    }

    /**
     * 设置买家名称
     *
     * @param memberName 买家名称
     */
    public void setMemberName(String memberName) {
        this.memberName = memberName == null ? null : memberName.trim();
    }

    /**
     * 获取卖家ID
     *
     * @return payee_member_id - 卖家ID
     */
    public String getPayeeMemberId() {
        return payeeMemberId;
    }

    /**
     * 设置卖家ID
     *
     * @param payeeMemberId 卖家ID
     */
    public void setPayeeMemberId(String payeeMemberId) {
        this.payeeMemberId = payeeMemberId == null ? null : payeeMemberId.trim();
    }

    public String getPayeeMemberName()
    {
        return payeeMemberName;
    }

    public void setPayeeMemberName(String payeeMemberName)
    {
        this.payeeMemberName = payeeMemberName == null ? null : payeeMemberName.trim();
    }

    /**
     * 获取记录类型 1：支付、2：还款、3：退款
     *
     * @return type - 记录类型 1：支付、2：还款、3：退款
     */
    public Integer getType() {
        return type;
    }

    /**
     * 设置记录类型 1：支付、2：还款、3：退款
     *
     * @param type 记录类型 1：支付、2：还款、3：退款
     */
    public void setType(Integer type) {
        this.type = type;
    }

    /**
     * 获取金额
     *
     * @return amount - 金额
     */
    public BigDecimal getAmount() {
        return amount;
    }

    /**
     * 设置金额
     *
     * @param amount 金额
     */
    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    /**
     * 获取流水号
     * @return
     */
    public String getBillNo()
    {
        return billNo;
    }

    /**
     * 设置流水号
     * @param billNo
     */
    public void setBillNo(String billNo)
    {
        this.billNo = billNo;
    }

    /**
     * 获取备注
     *
     * @return memo - 备注
     */
    public String getMemo() {
        return memo;
    }

    /**
     * 设置备注
     *
     * @param memo 备注
     */
    public void setMemo(String memo) {
        this.memo = memo == null ? null : memo.trim();
    }

    /**
     * 获取附件
     *
     * @return file_attach - 附件
     */
    public String getFileAttach() {
        return fileAttach;
    }

    /**
     * 设置附件
     *
     * @param fileAttach 附件
     */
    public void setFileAttach(String fileAttach) {
        this.fileAttach = fileAttach == null ? null : fileAttach.trim();
    }

    /**
     * 获取是否删除 0：否、1：是
     *
     * @return del_flg - 是否删除 0：否、1：是
     */
    public Boolean getDelFlg() {
        return delFlg;
    }

    /**
     * 设置是否删除 0：否、1：是
     *
     * @param delFlg 是否删除 0：否、1：是
     */
    public void setDelFlg(Boolean delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建人
     *
     * @return create_user - 创建人
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建人
     *
     * @param createUser 创建人
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新人
     *
     * @return update_user - 更新人
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新人
     *
     * @param updateUser 更新人
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", memberId=").append(memberId);
        sb.append(", memberCode=").append(memberCode);
        sb.append(", memberName=").append(memberName);
        sb.append(", payeeMemberId=").append(payeeMemberId);
        sb.append(", type=").append(type);
        sb.append(", amount=").append(amount);
        sb.append(", memo=").append(memo);
        sb.append(", fileAttach=").append(fileAttach);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}
