package com.ecommerce.pay.v2.dao.vo;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import jakarta.persistence.*;

@Table(name = "pa_operation_record")
public class OperationRecord implements Serializable {
    @Id
    @Column(name = "operation_record_id")
    private String operationRecordId;

    @Column(name = "operation_no")
    private String operationNo;

    @Column(name = "refer_no1")
    private String referNo1;

    @Column(name = "refer_no2")
    private String referNo2;

    @Column(name = "refer_no3")
    private String referNo3;

    private String type;

    private String content1;

    private String content2;

    private String content3;

    private Boolean success;

    @Column(name = "start_time")
    private Date startTime;

    @Column(name = "end_time")
    private Date endTime;

    /**
     * 删除标记
     */
    @Column(name = "del_flg")
    private Boolean delFlg;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * @return operation_record_id
     */
    public String getOperationRecordId() {
        return operationRecordId;
    }

    /**
     * @param operationRecordId
     */
    public void setOperationRecordId(String operationRecordId) {
        this.operationRecordId = operationRecordId == null ? null : operationRecordId.trim();
    }

    /**
     * @return operation_no
     */
    public String getOperationNo() {
        return operationNo;
    }

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    /**
     * @param operationNo
     */
    public void setOperationNo(String operationNo) {
        this.operationNo = operationNo == null ? null : operationNo.trim();
    }

    /**
     * @return refer_no1
     */
    public String getReferNo1() {
        return referNo1;
    }

    /**
     * @param referNo1
     */
    public void setReferNo1(String referNo1) {
        this.referNo1 = referNo1 == null ? null : referNo1.trim();
    }

    /**
     * @return refer_no2
     */
    public String getReferNo2() {
        return referNo2;
    }

    /**
     * @param referNo2
     */
    public void setReferNo2(String referNo2) {
        this.referNo2 = referNo2 == null ? null : referNo2.trim();
    }

    /**
     * @return refer_no3
     */
    public String getReferNo3() {
        return referNo3;
    }

    /**
     * @param referNo3
     */
    public void setReferNo3(String referNo3) {
        this.referNo3 = referNo3 == null ? null : referNo3.trim();
    }

    /**
     * @return type
     */
    public String getType() {
        return type;
    }

    /**
     * @param type
     */
    public void setType(String type) {
        this.type = type == null ? null : type.trim();
    }

    /**
     * @return content1
     */
    public String getContent1() {
        return content1;
    }

    /**
     * @param content1
     */
    public void setContent1(String content1) {
        this.content1 = content1 == null ? null : content1.trim();
    }

    /**
     * @return content2
     */
    public String getContent2() {
        return content2;
    }

    /**
     * @param content2
     */
    public void setContent2(String content2) {
        this.content2 = content2 == null ? null : content2.trim();
    }

    /**
     * @return content3
     */
    public String getContent3() {
        return content3;
    }

    /**
     * @param content3
     */
    public void setContent3(String content3) {
        this.content3 = content3 == null ? null : content3.trim();
    }

    /**
     * @return start_time
     */
    public Date getStartTime() {
        return startTime;
    }

    /**
     * @param startTime
     */
    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    /**
     * @return end_time
     */
    public Date getEndTime() {
        return endTime;
    }

    /**
     * @param endTime
     */
    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    /**
     * 获取删除标记
     *
     * @return del_flg - 删除标记
     */
    public Boolean getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标记
     *
     * @param delFlg 删除标记
     */
    public void setDelFlg(Boolean delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建人
     *
     * @return create_user - 创建人
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建人
     *
     * @param createUser 创建人
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取修改人
     *
     * @return update_user - 修改人
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置修改人
     *
     * @param updateUser 修改人
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取修改时间
     *
     * @return update_time - 修改时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置修改时间
     *
     * @param updateTime 修改时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", operationRecordId=").append(operationRecordId);
        sb.append(", operationNo=").append(operationNo);
        sb.append(", referNo1=").append(referNo1);
        sb.append(", referNo2=").append(referNo2);
        sb.append(", referNo3=").append(referNo3);
        sb.append(", type=").append(type);
        sb.append(", content1=").append(content1);
        sb.append(", content2=").append(content2);
        sb.append(", content3=").append(content3);
        sb.append(", startTime=").append(startTime);
        sb.append(", endTime=").append(endTime);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}