package com.ecommerce.pay.v2.dao.vo;

import lombok.Data;

import jakarta.persistence.Column;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

@Data
@Table(name = "pa_bill_payment_attachment")
public class PaBillPaymentAttachment implements Serializable {

    @Serial
    private static final long serialVersionUID = -8849983969088298209L;

    @Id
    @Column(name = "pa_bill_payment_attachment_id")
    private String paBillPaymentAttachmentId;

    /**
     * 继承自表:pa_bill_payment
     */
    @Column(name = "payment_bill_id")
    private String paymentBillId;

    /**
     * 继承自表:pa_bill_payment
     */
    @Column(name = "payment_bill_no")
    private String paymentBillNo;

    /**
     * 支付时上传的附件url
     */
    private String url;

    /**
     * 删除标记
     */
    @Column(name = "del_flg")
    private Boolean delFlg;

    /**
     * 创建人id
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新人id
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}