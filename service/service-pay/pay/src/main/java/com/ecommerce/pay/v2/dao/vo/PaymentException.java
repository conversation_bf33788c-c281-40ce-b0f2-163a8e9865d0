package com.ecommerce.pay.v2.dao.vo;

import jakarta.persistence.Column;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

@Table(name = "pa_exception")
public class PaymentException implements Serializable {
    /**
     *
     */
    @Id
    private String id;

    /**
     * 关联ID
     */
    @Column(name = "obj_id")
    private String objId;

    /**
     * 订单ID
     */
    @Column(name = "order_no")
    private String orderNo;

    /**
     * 参数class类型
     */
    @Column(name = "param_class")
    private String paramClass;

    /**
     * 接口名称
     */
    @Column(name = "interface_name")
    private String interfaceName;

    @Column(name = "operate_type")
    private String operateType;

    /**
     * 处理状态
     */
    private String status;

    /**
     * 重试次数
     */
    @Column(name = "retry_count")
    private Integer retryCount;

    /**
     * 是否删除
     */
    @Column(name = "del_flag")
    private Boolean delFlag;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 更新人
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 异常信息
     */
    @Column(name = "ex_content")
    private String exContent;

    /**
     * 异常方法调用参数
     */
    private String param;

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 获取主键自增
     *
     * @return id - 主键自增
     */
    public String getId() {
        return id;
    }

    /**
     * 设置主键自增
     *
     * @param id 主键自增
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * 获取关联ID
     *
     * @return obj_id - 关联ID
     */
    public String getObjId() {
        return objId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    /**
     * 设置关联ID
     *
     * @param objId 关联ID
     */
    public void setObjId(String objId) {
        this.objId = objId == null ? null : objId.trim();
    }

    /**
     * 获取参数class类型
     *
     * @return param_class - 参数class类型
     */
    public String getParamClass() {
        return paramClass;
    }

    /**
     * 设置参数class类型
     *
     * @param paramClass 参数class类型
     */
    public void setParamClass(String paramClass) {
        this.paramClass = paramClass == null ? null : paramClass.trim();
    }

    /**
     * 获取接口名称
     *
     * @return interface_name - 接口名称
     */
    public String getInterfaceName() {
        return interfaceName;
    }

    /**
     * 设置接口名称
     *
     * @param interfaceName 接口名称
     */
    public void setInterfaceName(String interfaceName) {
        this.interfaceName = interfaceName == null ? null : interfaceName.trim();
    }

    /**
     * @return operate_type
     */
    public String getOperateType() {
        return operateType;
    }

    /**
     * @param operateType
     */
    public void setOperateType(String operateType) {
        this.operateType = operateType == null ? null : operateType.trim();
    }

    /**
     * 获取处理状态
     *
     * @return status - 处理状态
     */
    public String getStatus() {
        return status;
    }

    /**
     * 设置处理状态
     *
     * @param status 处理状态
     */
    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    /**
     * 获取重试次数
     *
     * @return retry_count - 重试次数
     */
    public Integer getRetryCount() {
        return retryCount;
    }

    /**
     * 设置重试次数
     *
     * @param retryCount 重试次数
     */
    public void setRetryCount(Integer retryCount) {
        this.retryCount = retryCount;
    }

    /**
     * 获取是否删除
     *
     * @return del_flag - 是否删除
     */
    public Boolean getDelFlag() {
        return delFlag;
    }

    /**
     * 设置是否删除
     *
     * @param delFlag 是否删除
     */
    public void setDelFlag(Boolean delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * 获取创建人
     *
     * @return create_user - 创建人
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建人
     *
     * @param createUser 创建人
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新人
     *
     * @return update_user - 更新人
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新人
     *
     * @param updateUser 更新人
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取更新人
     *
     * @return update_time - 更新人
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新人
     *
     * @param updateTime 更新人
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取异常信息
     *
     * @return ex_content - 异常信息
     */
    public String getExContent() {
        return exContent;
    }

    /**
     * 设置异常信息
     *
     * @param exContent 异常信息
     */
    public void setExContent(String exContent) {
        this.exContent = exContent == null ? null : exContent.trim();
    }

    /**
     * 获取异常方法调用参数
     *
     * @return param - 异常方法调用参数
     */
    public String getParam() {
        return param;
    }

    /**
     * 设置异常方法调用参数
     *
     * @param param 异常方法调用参数
     */
    public void setParam(String param) {
        this.param = param == null ? null : param.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", objId=").append(objId);
        sb.append(", orderNo=").append(orderNo);
        sb.append(", paramClass=").append(paramClass);
        sb.append(", interfaceName=").append(interfaceName);
        sb.append(", operateType=").append(operateType);
        sb.append(", status=").append(status);
        sb.append(", retryCount=").append(retryCount);
        sb.append(", delFlag=").append(delFlag);
        sb.append(", createUser=").append(createUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", exContent=").append(exContent);
        sb.append(", param=").append(param);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}