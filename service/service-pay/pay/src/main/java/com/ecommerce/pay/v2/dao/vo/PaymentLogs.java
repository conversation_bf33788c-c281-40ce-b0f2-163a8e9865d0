package com.ecommerce.pay.v2.dao.vo;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import jakarta.persistence.*;

@Table(name = "pa_payment_logs")
public class PaymentLogs implements Serializable {
    @Id
    @Column(name = "payment_logs_id")
    private String paymentLogsId;

    @Column(name = "payment_bill_id")
    private String paymentBillId;

    /**
     * 外部订单号
     */
    @Column(name = "out_trade_no")
    private String outTradeNo;

    /**
     * 外部订单号
     */
    @Column(name = "ext_biz_no")
    private String extBizNo;

    @Column(name = "payer_member_id")
    private String payerMemberId;

    @Column(name = "payer_member_name")
    private String payerMemberName;

    @Column(name = "pay_start_time")
    private Date payStartTime;

    @Column(name = "pay_finish_time")
    private Date payFinishTime;

    @Column(name = "channel_code")
    private String channelCode;

    @Column(name = "channel_name")
    private String channelName;

    @Column(name = "channel_id")
    private String channelId;

    @Column(name = "payee_member_id")
    private String payeeMemberId;

    @Column(name = "payee_member_name")
    private String payeeMemberName;

    @Column(name = "actual_pay_amount")
    private BigDecimal actualPayAmount;

    @Column(name = "pay_type")
    private String payType;

    /**
     * 删除标记
     */
    @Column(name = "del_flg")
    private Boolean delFlg;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 调用方支付业务名称
     */
    @Column(name = "biz_name")
    private String bizName;

    @Column(name = "receive_channel_id")
    private String receiveChannelId;

    @Column(name = "receive_channel_code")
    private String receiveChannelCode;

    @Column(name = "receive_channel_name")
    private String receiveChannelName;

    @Column(name = "ec_order_no")
    private String ecOrderNo;

    @Column(name = "payer_member_code")
    private String payerMemberCode;

    @Column(name = "payee_member_code")
    private String payeeMemberCode;

    @Column(name = "display_type")
    private String displayType;

    private String detail;

    private String ext1;

    private String ext2;

    @Serial
    private static final long serialVersionUID = 1L;

    public String getDetail() {
        return detail;
    }

    public void setDetail(String detail) {
        this.detail = detail;
    }

    public String getExt1() {
        return ext1;
    }

    public void setExt1(String ext1) {
        this.ext1 = ext1;
    }

    public String getExt2() {
        return ext2;
    }

    public void setExt2(String ext2) {
        this.ext2 = ext2;
    }

    public String getExtBizNo() {
        return extBizNo;
    }

    public void setExtBizNo(String extBizNo) {
        this.extBizNo = extBizNo;
    }

    public String getDisplayType() {
        return displayType;
    }

    public void setDisplayType(String displayType) {
        this.displayType = displayType;
    }

    /**
     * @return payment_logs_id
     */
    public String getPaymentLogsId() {
        return paymentLogsId;
    }

    /**
     * @param paymentLogsId
     */
    public void setPaymentLogsId(String paymentLogsId) {
        this.paymentLogsId = paymentLogsId == null ? null : paymentLogsId.trim();
    }

    /**
     * @return payment_bill_id
     */
    public String getPaymentBillId() {
        return paymentBillId;
    }

    /**
     * @param paymentBillId
     */
    public void setPaymentBillId(String paymentBillId) {
        this.paymentBillId = paymentBillId == null ? null : paymentBillId.trim();
    }

    /**
     * 获取外部订单号
     *
     * @return out_trade_no - 外部订单号
     */
    public String getOutTradeNo() {
        return outTradeNo;
    }

    /**
     * 设置外部订单号
     *
     * @param outTradeNo 外部订单号
     */
    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo == null ? null : outTradeNo.trim();
    }

    /**
     * @return payer_member_id
     */
    public String getPayerMemberId() {
        return payerMemberId;
    }

    /**
     * @param payerMemberId
     */
    public void setPayerMemberId(String payerMemberId) {
        this.payerMemberId = payerMemberId == null ? null : payerMemberId.trim();
    }

    /**
     * @return payer_member_name
     */
    public String getPayerMemberName() {
        return payerMemberName;
    }

    /**
     * @param payerMemberName
     */
    public void setPayerMemberName(String payerMemberName) {
        this.payerMemberName = payerMemberName == null ? null : payerMemberName.trim();
    }

    /**
     * @return pay_start_time
     */
    public Date getPayStartTime() {
        return payStartTime;
    }

    /**
     * @param payStartTime
     */
    public void setPayStartTime(Date payStartTime) {
        this.payStartTime = payStartTime;
    }

    /**
     * @return pay_finish_time
     */
    public Date getPayFinishTime() {
        return payFinishTime;
    }

    /**
     * @param payFinishTime
     */
    public void setPayFinishTime(Date payFinishTime) {
        this.payFinishTime = payFinishTime;
    }

    /**
     * @return channel_code
     */
    public String getChannelCode() {
        return channelCode;
    }

    /**
     * @param channelCode
     */
    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode == null ? null : channelCode.trim();
    }

    /**
     * @return channel_name
     */
    public String getChannelName() {
        return channelName;
    }

    /**
     * @param channelName
     */
    public void setChannelName(String channelName) {
        this.channelName = channelName == null ? null : channelName.trim();
    }

    /**
     * @return channel_id
     */
    public String getChannelId() {
        return channelId;
    }

    /**
     * @param channelId
     */
    public void setChannelId(String channelId) {
        this.channelId = channelId == null ? null : channelId.trim();
    }

    /**
     * @return payee_member_id
     */
    public String getPayeeMemberId() {
        return payeeMemberId;
    }

    /**
     * @param payeeMemberId
     */
    public void setPayeeMemberId(String payeeMemberId) {
        this.payeeMemberId = payeeMemberId == null ? null : payeeMemberId.trim();
    }

    /**
     * @return payee_member_name
     */
    public String getPayeeMemberName() {
        return payeeMemberName;
    }

    /**
     * @param payeeMemberName
     */
    public void setPayeeMemberName(String payeeMemberName) {
        this.payeeMemberName = payeeMemberName == null ? null : payeeMemberName.trim();
    }

    /**
     * @return actual_pay_amount
     */
    public BigDecimal getActualPayAmount() {
        return actualPayAmount;
    }

    /**
     * @param actualPayAmount
     */
    public void setActualPayAmount(BigDecimal actualPayAmount) {
        this.actualPayAmount = actualPayAmount;
    }

    /**
     * @return pay_type
     */
    public String getPayType() {
        return payType;
    }

    /**
     * @param payType
     */
    public void setPayType(String payType) {
        this.payType = payType == null ? null : payType.trim();
    }

    /**
     * 获取删除标记
     *
     * @return del_flg - 删除标记
     */
    public Boolean getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标记
     *
     * @param delFlg 删除标记
     */
    public void setDelFlg(Boolean delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建人
     *
     * @return create_user - 创建人
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建人
     *
     * @param createUser 创建人
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取修改人
     *
     * @return update_user - 修改人
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置修改人
     *
     * @param updateUser 修改人
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取修改时间
     *
     * @return update_time - 修改时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置修改时间
     *
     * @param updateTime 修改时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取调用方支付业务名称
     *
     * @return biz_name - 调用方支付业务名称
     */
    public String getBizName() {
        return bizName;
    }

    /**
     * 设置调用方支付业务名称
     *
     * @param bizName 调用方支付业务名称
     */
    public void setBizName(String bizName) {
        this.bizName = bizName == null ? null : bizName.trim();
    }

    /**
     * @return receive_channel_id
     */
    public String getReceiveChannelId() {
        return receiveChannelId;
    }

    /**
     * @param receiveChannelId
     */
    public void setReceiveChannelId(String receiveChannelId) {
        this.receiveChannelId = receiveChannelId == null ? null : receiveChannelId.trim();
    }

    /**
     * @return receive_channel_code
     */
    public String getReceiveChannelCode() {
        return receiveChannelCode;
    }

    /**
     * @param receiveChannelCode
     */
    public void setReceiveChannelCode(String receiveChannelCode) {
        this.receiveChannelCode = receiveChannelCode == null ? null : receiveChannelCode.trim();
    }

    /**
     * @return receive_channel_name
     */
    public String getReceiveChannelName() {
        return receiveChannelName;
    }

    /**
     * @param receiveChannelName
     */
    public void setReceiveChannelName(String receiveChannelName) {
        this.receiveChannelName = receiveChannelName == null ? null : receiveChannelName.trim();
    }

    /**
     * @return ec_order_no
     */
    public String getEcOrderNo() {
        return ecOrderNo;
    }

    /**
     * @param ecOrderNo
     */
    public void setEcOrderNo(String ecOrderNo) {
        this.ecOrderNo = ecOrderNo == null ? null : ecOrderNo.trim();
    }

    /**
     * @return payer_member_code
     */
    public String getPayerMemberCode() {
        return payerMemberCode;
    }

    /**
     * @param payerMemberCode
     */
    public void setPayerMemberCode(String payerMemberCode) {
        this.payerMemberCode = payerMemberCode == null ? null : payerMemberCode.trim();
    }

    /**
     * @return payee_member_code
     */
    public String getPayeeMemberCode() {
        return payeeMemberCode;
    }

    /**
     * @param payeeMemberCode
     */
    public void setPayeeMemberCode(String payeeMemberCode) {
        this.payeeMemberCode = payeeMemberCode == null ? null : payeeMemberCode.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", paymentLogsId=").append(paymentLogsId);
        sb.append(", paymentBillId=").append(paymentBillId);
        sb.append(", outTradeNo=").append(outTradeNo);
        sb.append(", extBizNo=").append(extBizNo);
        sb.append(", payerMemberId=").append(payerMemberId);
        sb.append(", payerMemberName=").append(payerMemberName);
        sb.append(", payStartTime=").append(payStartTime);
        sb.append(", payFinishTime=").append(payFinishTime);
        sb.append(", channelCode=").append(channelCode);
        sb.append(", channelName=").append(channelName);
        sb.append(", channelId=").append(channelId);
        sb.append(", payeeMemberId=").append(payeeMemberId);
        sb.append(", payeeMemberName=").append(payeeMemberName);
        sb.append(", actualPayAmount=").append(actualPayAmount);
        sb.append(", payType=").append(payType);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", bizName=").append(bizName);
        sb.append(", receiveChannelId=").append(receiveChannelId);
        sb.append(", receiveChannelCode=").append(receiveChannelCode);
        sb.append(", receiveChannelName=").append(receiveChannelName);
        sb.append(", ecOrderNo=").append(ecOrderNo);
        sb.append(", payerMemberCode=").append(payerMemberCode);
        sb.append(", payeeMemberCode=").append(payeeMemberCode);
        sb.append(", displayType=").append(displayType);
        sb.append(", detail=").append(detail);
        sb.append(", ext1=").append(ext1);
        sb.append(", ext2=").append(ext2);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}