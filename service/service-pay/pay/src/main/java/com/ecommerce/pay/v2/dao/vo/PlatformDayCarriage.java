package com.ecommerce.pay.v2.dao.vo;

import lombok.Data;

import jakarta.persistence.Column;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 个体司机费用-按平台按日汇总信息表，以及支付信息
 */
@Data
@Table(name = "pa_platform_day_carriage")
public class PlatformDayCarriage implements Serializable {
    @Serial
    private static final long serialVersionUID = 2764722266260974081L;
    /**
     * id
     */
    @Id
    @Column(name = "platform_day_carriage_id")
    private String platformDayCarriageId;

    /**
     * 记账日期yyyy-MM-dd
     */
    private String day;

    /**
     * 运单量
     */
    @Column(name = "waybill_count")
    private Integer waybillCount;

    /**
     * 运距
     */
    private BigDecimal distance;

    /**
     * 运量
     */
    private BigDecimal quantity;

    /**
     * 运费
     */
    @Column(name = "logistics_amount")
    private BigDecimal logisticsAmount;

    /**
     * 签收时间
     */
    @Column(name = "sign_time")
    private Date signTime;

    /**
     * 可提现时间
     */
    @Column(name = "withdraw_future_date")
    private Date withdrawFutureDate;

    /**
     * 支付金额
     */
    @Column(name = "actual_pay_amount")
    private BigDecimal actualPayAmount;

    /**
     * 支付状态
     */
    @Column(name = "pay_status")
    private String payStatus;

    /**
     * 计划付款时间
     */
    @Column(name = "scheduled_pay_time")
    private Date scheduledPayTime;

    /**
     * 实际付款时间
     */
    @Column(name = "actual_pay_time")
    private Date actualPayTime;

    /**
     * 支付单据号
     */
    @Column(name = "pay_number")
    private String payNumber;

    /**
     * 支付系统tradeNo
     */
    @Column(name = "trade_bill_no")
    private String tradeBillNo;

    /**
     * 删除标识
     */
    @Column(name = "del_flg")
    private Boolean delFlg;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}