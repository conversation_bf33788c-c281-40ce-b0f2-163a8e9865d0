package com.ecommerce.pay.v2.dao.vo;

import io.swagger.v3.oas.annotations.media.Schema;

import jakarta.persistence.Column;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Table(name = "pa_bill_refund")
public class RefundBill implements Serializable {
    @Id
    @Column(name = "refund_bill_id")
    private String refundBillId;

    /**
     * 支付单编号（流水号）
     */
    @Column(name = "refund_bill_no")
    private String refundBillNo;

    /**
     * 退款时填入的支付明细记录id
     */
    @Column(name = "related_payment_bill_no")
    private String relatedPaymentBillNo;

    /**
     * 来源业务单号
     */
    @Column(name = "src_biz_no")
    private String srcBizNo;

    /**
     * 第三方支付系统流水号
     */
    @Column(name = "ext_biz_no")
    private String extBizNo;

    /**
     * 订单ID
     */
    @Column(name = "order_id")
    private String orderId;

    @Column(name = "order_no")
    private String orderNo;

    /**
     * 第三方系统支付状态
     */
    @Column(name = "ext_status")
    private String extStatus;

    /**
     * 1000 - 已创建，未支付; 2000 - 支付中； 2010 - 支付失败（可重试）; 3000 - 已失效; 3010 - 已取消; 9000 - 支付成功;
     */
    private String status;

    /**
     * 支付业务类型编码
     */
    @Column(name = "payment_biz_code")
    private String paymentBizCode;

    /**
     * 调用方支付业务名称
     */
    @Column(name = "biz_name")
    private String bizName;

    /**
     * 具体描述
     */
    private String detail;

    /**
     * 付款方会员ID
     */
    @Column(name = "payer_member_id")
    private String payerMemberId;

    /**
     * 付款方会员名称
     */
    @Column(name = "payer_member_name")
    private String payerMemberName;

    @Column(name = "payer_member_code")
    private String payerMemberCode;

    /**
     * 收款方会员ID
     */
    @Column(name = "payee_member_id")
    private String payeeMemberId;

    /**
     * 初始交易金额
     */
    @Column(name = "pay_amount")
    private BigDecimal payAmount;

    /**
     * 实际交易金额
     */
    @Column(name = "actual_pay_amount")
    private BigDecimal actualPayAmount;

    /**
     * 手续费
     */
    @Column(name = "service_fee")
    private BigDecimal serviceFee;

    /**
     * 红包、零钱，退款时需要考虑如何处理
     */
    @Column(name = "other_fee")
    private String otherFee;

    /**
     * 以便后面分类采取进一步措施
     */
    @Column(name = "warning_code")
    private String warningCode;

    private String currency;

    /**
     * 支付渠道类型
     */
    @Column(name = "channel_type")
    private String channelType;

    /**
     * 支付渠道名称
     */
    @Column(name = "channel_name")
    private String channelName;

    /**
     * 支付渠道id
     */
    @Column(name = "channel_id")
    private String channelId;

    /**
     * 支付渠道code
     */
    @Column(name = "channel_code")
    private String channelCode;

    /**
     * 支付完成通知业务方的状态
     */
    @Column(name = "notify_bus_status")
    private String notifyBusStatus;

    @Schema(description = "银联支付-交易流水-电商流水号")
    @Column(name = "mct_order_no")
    private String mctOrderNo;

    @Schema(description = "银联支付-担保支付订单号")
    @Column(name = "guaranteed_order_no")
    private String guaranteedOrderNo;

    @Column(name = "copy_src_no")
    @Schema(description = "银联支付-聚合支付退款-初始退款单号",hidden = true)
    private String copySrcNo;

    /**
     * 支付单备注
     */
    private String remarks;

    @Column(name = "app_type")
    private String appType;

    @Column(name = "client_type")
    private String clientType;

    @Column(name = "goods_names")
    private String goodsNames;

    @Column(name = "del_flg")
    private Boolean delFlg;

    @Column(name = "create_user")
    private String createUser;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_user")
    private String updateUser;

    @Column(name = "update_time")
    private Date updateTime;

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * @return refund_bill_id
     */
    public String getRefundBillId() {
        return refundBillId;
    }

    /**
     * @param refundBillId
     */
    public void setRefundBillId(String refundBillId) {
        this.refundBillId = refundBillId == null ? null : refundBillId.trim();
    }

    /**
     * 获取支付单编号（流水号）
     *
     * @return refund_bill_no - 支付单编号（流水号）
     */
    public String getRefundBillNo() {
        return refundBillNo;
    }

    public String getPayeeMemberId() {
        return payeeMemberId;
    }

    public void setPayeeMemberId(String payeeMemberId) {
        this.payeeMemberId = payeeMemberId;
    }

    /**
     * 设置支付单编号（流水号）
     *
     * @param refundBillNo 支付单编号（流水号）
     */
    public void setRefundBillNo(String refundBillNo) {
        this.refundBillNo = refundBillNo == null ? null : refundBillNo.trim();
    }

    /**
     * 获取退款时填入的支付明细记录id
     *
     * @return related_payment_bill_no - 退款时填入的支付明细记录id
     */
    public String getRelatedPaymentBillNo() {
        return relatedPaymentBillNo;
    }

    /**
     * 设置退款时填入的支付明细记录id
     *
     * @param relatedPaymentBillNo 退款时填入的支付明细记录id
     */
    public void setRelatedPaymentBillNo(String relatedPaymentBillNo) {
        this.relatedPaymentBillNo = relatedPaymentBillNo == null ? null : relatedPaymentBillNo.trim();
    }

    /**
     * 获取来源业务单号
     *
     * @return src_biz_no - 来源业务单号
     */
    public String getSrcBizNo() {
        return srcBizNo;
    }

    /**
     * 设置来源业务单号
     *
     * @param srcBizNo 来源业务单号
     */
    public void setSrcBizNo(String srcBizNo) {
        this.srcBizNo = srcBizNo == null ? null : srcBizNo.trim();
    }

    /**
     * 获取第三方支付系统流水号
     *
     * @return ext_biz_no - 第三方支付系统流水号
     */
    public String getExtBizNo() {
        return extBizNo;
    }

    /**
     * 设置第三方支付系统流水号
     *
     * @param extBizNo 第三方支付系统流水号
     */
    public void setExtBizNo(String extBizNo) {
        this.extBizNo = extBizNo == null ? null : extBizNo.trim();
    }

    /**
     * 获取订单ID
     *
     * @return order_id - 订单ID
     */
    public String getOrderId() {
        return orderId;
    }

    /**
     * 设置订单ID
     *
     * @param orderId 订单ID
     */
    public void setOrderId(String orderId) {
        this.orderId = orderId == null ? null : orderId.trim();
    }

    /**
     * @return order_no
     */
    public String getOrderNo() {
        return orderNo;
    }

    /**
     * @param orderNo
     */
    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo == null ? null : orderNo.trim();
    }

    /**
     * 获取第三方系统支付状态
     *
     * @return ext_status - 第三方系统支付状态
     */
    public String getExtStatus() {
        return extStatus;
    }

    /**
     * 设置第三方系统支付状态
     *
     * @param extStatus 第三方系统支付状态
     */
    public void setExtStatus(String extStatus) {
        this.extStatus = extStatus == null ? null : extStatus.trim();
    }

    /**
     * 获取1000 - 已创建，未支付; 2000 - 支付中； 2010 - 支付失败（可重试）; 3000 - 已失效; 3010 - 已取消; 9000 - 支付成功;
     *
     * @return status - 1000 - 已创建，未支付; 2000 - 支付中； 2010 - 支付失败（可重试）; 3000 - 已失效; 3010 - 已取消; 9000 - 支付成功;
     */
    public String getStatus() {
        return status;
    }

    /**
     * 设置1000 - 已创建，未支付; 2000 - 支付中； 2010 - 支付失败（可重试）; 3000 - 已失效; 3010 - 已取消; 9000 - 支付成功;
     *
     * @param status 1000 - 已创建，未支付; 2000 - 支付中； 2010 - 支付失败（可重试）; 3000 - 已失效; 3010 - 已取消; 9000 - 支付成功;
     */
    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    /**
     * 获取支付业务类型编码
     *
     * @return payment_biz_code - 支付业务类型编码
     */
    public String getPaymentBizCode() {
        return paymentBizCode;
    }

    /**
     * 设置支付业务类型编码
     *
     * @param paymentBizCode 支付业务类型编码
     */
    public void setPaymentBizCode(String paymentBizCode) {
        this.paymentBizCode = paymentBizCode == null ? null : paymentBizCode.trim();
    }

    /**
     * 获取调用方支付业务名称
     *
     * @return biz_name - 调用方支付业务名称
     */
    public String getBizName() {
        return bizName;
    }

    /**
     * 设置调用方支付业务名称
     *
     * @param bizName 调用方支付业务名称
     */
    public void setBizName(String bizName) {
        this.bizName = bizName == null ? null : bizName.trim();
    }

    /**
     * 获取具体描述
     *
     * @return detail - 具体描述
     */
    public String getDetail() {
        return detail;
    }

    /**
     * 设置具体描述
     *
     * @param detail 具体描述
     */
    public void setDetail(String detail) {
        this.detail = detail == null ? null : detail.trim();
    }

    /**
     * 获取付款方会员ID
     *
     * @return payer_member_id - 付款方会员ID
     */
    public String getPayerMemberId() {
        return payerMemberId;
    }

    /**
     * 设置付款方会员ID
     *
     * @param payerMemberId 付款方会员ID
     */
    public void setPayerMemberId(String payerMemberId) {
        this.payerMemberId = payerMemberId == null ? null : payerMemberId.trim();
    }

    /**
     * 获取付款方会员名称
     *
     * @return payer_member_name - 付款方会员名称
     */
    public String getPayerMemberName() {
        return payerMemberName;
    }

    /**
     * 设置付款方会员名称
     *
     * @param payerMemberName 付款方会员名称
     */
    public void setPayerMemberName(String payerMemberName) {
        this.payerMemberName = payerMemberName == null ? null : payerMemberName.trim();
    }

    /**
     * @return payer_member_code
     */
    public String getPayerMemberCode() {
        return payerMemberCode;
    }

    /**
     * @param payerMemberCode
     */
    public void setPayerMemberCode(String payerMemberCode) {
        this.payerMemberCode = payerMemberCode == null ? null : payerMemberCode.trim();
    }

    /**
     * 获取初始交易金额
     *
     * @return pay_amount - 初始交易金额
     */
    public BigDecimal getPayAmount() {
        return payAmount;
    }

    /**
     * 设置初始交易金额
     *
     * @param payAmount 初始交易金额
     */
    public void setPayAmount(BigDecimal payAmount) {
        this.payAmount = payAmount;
    }

    /**
     * 获取实际交易金额
     *
     * @return actual_pay_amount - 实际交易金额
     */
    public BigDecimal getActualPayAmount() {
        return actualPayAmount;
    }

    /**
     * 设置实际交易金额
     *
     * @param actualPayAmount 实际交易金额
     */
    public void setActualPayAmount(BigDecimal actualPayAmount) {
        this.actualPayAmount = actualPayAmount;
    }

    /**
     * 获取手续费
     *
     * @return service_fee - 手续费
     */
    public BigDecimal getServiceFee() {
        return serviceFee;
    }

    /**
     * 设置手续费
     *
     * @param serviceFee 手续费
     */
    public void setServiceFee(BigDecimal serviceFee) {
        this.serviceFee = serviceFee;
    }

    /**
     * 获取红包、零钱，退款时需要考虑如何处理
     *
     * @return other_fee - 红包、零钱，退款时需要考虑如何处理
     */
    public String getOtherFee() {
        return otherFee;
    }

    /**
     * 设置红包、零钱，退款时需要考虑如何处理
     *
     * @param otherFee 红包、零钱，退款时需要考虑如何处理
     */
    public void setOtherFee(String otherFee) {
        this.otherFee = otherFee == null ? null : otherFee.trim();
    }

    /**
     * 获取以便后面分类采取进一步措施
     *
     * @return warning_code - 以便后面分类采取进一步措施
     */
    public String getWarningCode() {
        return warningCode;
    }

    /**
     * 设置以便后面分类采取进一步措施
     *
     * @param warningCode 以便后面分类采取进一步措施
     */
    public void setWarningCode(String warningCode) {
        this.warningCode = warningCode == null ? null : warningCode.trim();
    }

    /**
     * @return currency
     */
    public String getCurrency() {
        return currency;
    }

    /**
     * @param currency
     */
    public void setCurrency(String currency) {
        this.currency = currency == null ? null : currency.trim();
    }

    /**
     * 获取支付渠道类型
     *
     * @return channel_type - 支付渠道类型
     */
    public String getChannelType() {
        return channelType;
    }

    /**
     * 设置支付渠道类型
     *
     * @param channelType 支付渠道类型
     */
    public void setChannelType(String channelType) {
        this.channelType = channelType == null ? null : channelType.trim();
    }

    /**
     * 获取支付渠道名称
     *
     * @return channel_name - 支付渠道名称
     */
    public String getChannelName() {
        return channelName;
    }

    /**
     * 设置支付渠道名称
     *
     * @param channelName 支付渠道名称
     */
    public void setChannelName(String channelName) {
        this.channelName = channelName == null ? null : channelName.trim();
    }

    /**
     * 获取支付渠道id
     *
     * @return channel_id - 支付渠道id
     */
    public String getChannelId() {
        return channelId;
    }

    /**
     * 设置支付渠道id
     *
     * @param channelId 支付渠道id
     */
    public void setChannelId(String channelId) {
        this.channelId = channelId == null ? null : channelId.trim();
    }

    public String getAppType() {
        return appType;
    }

    public void setAppType(String appType) {
        this.appType = appType == null ? null : appType.trim();
    }

    /**
     * 获取支付渠道code
     *
     * @return channel_code - 支付渠道code
     */
    public String getChannelCode() {
        return channelCode;
    }

    /**
     * 设置支付渠道code
     *
     * @param channelCode 支付渠道code
     */
    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode == null ? null : channelCode.trim();
    }

    /**
     * 获取支付完成通知业务方的状态
     *
     * @return notify_bus_status - 支付完成通知业务方的状态
     */
    public String getNotifyBusStatus() {
        return notifyBusStatus;
    }

    /**
     * 设置支付完成通知业务方的状态
     *
     * @param notifyBusStatus 支付完成通知业务方的状态
     */
    public void setNotifyBusStatus(String notifyBusStatus) {
        this.notifyBusStatus = notifyBusStatus == null ? null : notifyBusStatus.trim();
    }

    /**
     * 获取支付单备注
     *
     * @return remarks - 支付单备注
     */
    public String getRemarks() {
        return remarks;
    }

    /**
     * 设置支付单备注
     *
     * @param remarks 支付单备注
     */
    public void setRemarks(String remarks) {
        this.remarks = remarks == null ? null : remarks.trim();
    }

    /**
     * @return del_flg
     */
    public Boolean getDelFlg() {
        return delFlg;
    }

    /**
     * @param delFlg
     */
    public void setDelFlg(Boolean delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * @return create_user
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * @param createUser
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * @return create_time
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * @param createTime
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * @return update_user
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * @param updateUser
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * @return update_time
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * @param updateTime
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * @return client_type
     */
    public String getClientType() {
        return clientType;
    }

    /**
     * @param clientType
     */
    public void setClientType(String clientType) {
        this.clientType = clientType == null ? null : clientType.trim();
    }

    /**
     * @return goods_names
     */
    public String getGoodsNames() {
        return goodsNames;
    }

    /**
     * @param goodsNames
     */
    public void setGoodsNames(String goodsNames) {
        this.goodsNames = goodsNames == null ? null : goodsNames.trim();
    }

    public String getMctOrderNo() {
        return mctOrderNo;
    }

    public void setMctOrderNo(String mctOrderNo) {
        this.mctOrderNo = mctOrderNo;
    }

    public String getGuaranteedOrderNo() {
        return guaranteedOrderNo;
    }

    public void setGuaranteedOrderNo(String guaranteedOrderNo) {
        this.guaranteedOrderNo = guaranteedOrderNo;
    }

    public String getCopySrcNo() {
        return copySrcNo;
    }

    public void setCopySrcNo(String copySrcNo) {
        this.copySrcNo = copySrcNo;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", refundBillId=").append(refundBillId);
        sb.append(", refundBillNo=").append(refundBillNo);
        sb.append(", relatedPaymentBillNo=").append(relatedPaymentBillNo);
        sb.append(", srcBizNo=").append(srcBizNo);
        sb.append(", extBizNo=").append(extBizNo);
        sb.append(", orderId=").append(orderId);
        sb.append(", orderNo=").append(orderNo);
        sb.append(", extStatus=").append(extStatus);
        sb.append(", status=").append(status);
        sb.append(", paymentBizCode=").append(paymentBizCode);
        sb.append(", bizName=").append(bizName);
        sb.append(", detail=").append(detail);
        sb.append(", payerMemberId=").append(payerMemberId);
        sb.append(", payerMemberName=").append(payerMemberName);
        sb.append(", payerMemberCode=").append(payerMemberCode);
        sb.append(", payeeMemberId=").append(payeeMemberId);
        sb.append(", payAmount=").append(payAmount);
        sb.append(", actualPayAmount=").append(actualPayAmount);
        sb.append(", serviceFee=").append(serviceFee);
        sb.append(", otherFee=").append(otherFee);
        sb.append(", warningCode=").append(warningCode);
        sb.append(", currency=").append(currency);
        sb.append(", channelType=").append(channelType);
        sb.append(", channelName=").append(channelName);
        sb.append(", channelId=").append(channelId);
        sb.append(", channelCode=").append(channelCode);
        sb.append(", notifyBusStatus=").append(notifyBusStatus);
        sb.append(", remarks=").append(remarks);
        sb.append(", mctOrderNo=").append(mctOrderNo);
        sb.append(", guaranteedOrderNo=").append(guaranteedOrderNo);
        sb.append(", copySrcNo=").append(copySrcNo);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", appType=").append(appType);
        sb.append(", clientType=").append(clientType);
        sb.append(", goodsNames=").append(goodsNames);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}