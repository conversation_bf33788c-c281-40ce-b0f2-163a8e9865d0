package com.ecommerce.pay.v2.dao.vo;

import jakarta.persistence.Column;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Table(name = "pa_bill_refund_detail")
public class RefundBillDetail implements Serializable {
    @Id
    @Column(name = "refund_bill_detail_id")
    private String refundBillDetailId;

    /**
     * 编号
     */
    @Column(name = "refund_bill_detail_code")
    private String refundBillDetailCode;

    /**
     * 退款时原来的支付单
     */
    @Column(name = "related_payment_bill_detail_code")
    private String relatedPaymentBillDetailCode;

    /**
     * 支付单编号（流水号）
     */
    private String subject;

    /**
     * 支付单编号（流水号）
     */
    @Column(name = "refund_bill_no")
    private String refundBillNo;

    private String status;

    /**
     * 收款方会员ID
     */
    @Column(name = "payee_member_id")
    private String payeeMemberId;

    /**
     * 收款方会员名称
     */
    @Column(name = "payee_member_name")
    private String payeeMemberName;

    /**
     * 交易金额
     */
    @Column(name = "pay_amount")
    private BigDecimal payAmount;

    private String currency;

    /**
     * 实际交易金额
     */
    @Column(name = "actual_pay_amount")
    private BigDecimal actualPayAmount;

    /**
     * 手续费
     */
    @Column(name = "service_fee")
    private BigDecimal serviceFee;

    /**
     * 红包、零钱，退款时需要考虑如何处理
     */
    @Column(name = "other_fee")
    private String otherFee;

    /**
     * 支付渠道名称
     */
    @Column(name = "channel_name")
    private String channelName;

    /**
     * 支付渠道code
     */
    @Column(name = "channel_code")
    private String channelCode;

    private String warning;

    //-- 退款时分2步,此为记录第一步交易结果  卖家钱 -->平台钱包 --->买家对应付款终端（可能是：银联钱包、微信、支付宝）
    //ALTER TABLE `pa_bill_refund_detail`
    //ADD COLUMN `gnete_receivable_trans_status`  tinyint(1) NULL DEFAULT 0 COMMENT '从卖家收款到平台-收款状态' AFTER `warning`,
    //ADD COLUMN `gnete_receivable_trans_result`  varchar(128) NULL DEFAULT NULL COMMENT '从卖家收款到平台-收款说明' AFTER `gnete_receivable_trans_status`,
    @Column(name = "gnete_receivable_trans_status")
    private Boolean gneteReceivableTransStatus;

    @Column(name = "gnete_receivable_trans_result")
    private String gneteReceivableTransResult;

    @Column(name = "gnete_receivable_trans_order_no")
    private String gneteReceivableTransOrderNo;

    @Column(name = "del_flg")
    private Boolean delFlg;

    @Column(name = "create_user")
    private String createUser;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_user")
    private String updateUser;

    @Column(name = "update_time")
    private Date updateTime;

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * @return refund_bill_detail_id
     */
    public String getRefundBillDetailId() {
        return refundBillDetailId;
    }

    /**
     * @param refundBillDetailId
     */
    public void setRefundBillDetailId(String refundBillDetailId) {
        this.refundBillDetailId = refundBillDetailId == null ? null : refundBillDetailId.trim();
    }

    /**
     * 获取编号
     *
     * @return refund_bill_detail_code - 编号
     */
    public String getRefundBillDetailCode() {
        return refundBillDetailCode;
    }

    /**
     * 设置编号
     *
     * @param refundBillDetailCode 编号
     */
    public void setRefundBillDetailCode(String refundBillDetailCode) {
        this.refundBillDetailCode = refundBillDetailCode == null ? null : refundBillDetailCode.trim();
    }

    /**
     * 获取退款时原来的支付单
     *
     * @return related_payment_bill_detail_code - 退款时原来的支付单
     */
    public String getRelatedPaymentBillDetailCode() {
        return relatedPaymentBillDetailCode;
    }

    /**
     * 设置退款时原来的支付单
     *
     * @param relatedPaymentBillDetailCode 退款时原来的支付单
     */
    public void setRelatedPaymentBillDetailCode(String relatedPaymentBillDetailCode) {
        this.relatedPaymentBillDetailCode = relatedPaymentBillDetailCode == null ? null : relatedPaymentBillDetailCode.trim();
    }

    /**
     * 获取支付单编号（流水号）
     *
     * @return subject - 支付单编号（流水号）
     */
    public String getSubject() {
        return subject;
    }

    /**
     * 设置支付单编号（流水号）
     *
     * @param subject 支付单编号（流水号）
     */
    public void setSubject(String subject) {
        this.subject = subject == null ? null : subject.trim();
    }

    /**
     * 获取支付单编号（流水号）
     *
     * @return refund_bill_no - 支付单编号（流水号）
     */
    public String getRefundBillNo() {
        return refundBillNo;
    }

    /**
     * 设置支付单编号（流水号）
     *
     * @param refundBillNo 支付单编号（流水号）
     */
    public void setRefundBillNo(String refundBillNo) {
        this.refundBillNo = refundBillNo == null ? null : refundBillNo.trim();
    }

    /**
     * @return status
     */
    public String getStatus() {
        return status;
    }

    /**
     * @param status
     */
    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    /**
     * 获取收款方会员ID
     *
     * @return payee_member_id - 收款方会员ID
     */
    public String getPayeeMemberId() {
        return payeeMemberId;
    }

    /**
     * 设置收款方会员ID
     *
     * @param payeeMemberId 收款方会员ID
     */
    public void setPayeeMemberId(String payeeMemberId) {
        this.payeeMemberId = payeeMemberId == null ? null : payeeMemberId.trim();
    }

    /**
     * 获取收款方会员名称
     *
     * @return payee_member_name - 收款方会员名称
     */
    public String getPayeeMemberName() {
        return payeeMemberName;
    }

    /**
     * 设置收款方会员名称
     *
     * @param payeeMemberName 收款方会员名称
     */
    public void setPayeeMemberName(String payeeMemberName) {
        this.payeeMemberName = payeeMemberName == null ? null : payeeMemberName.trim();
    }

    /**
     * 获取交易金额
     *
     * @return pay_amount - 交易金额
     */
    public BigDecimal getPayAmount() {
        return payAmount;
    }

    /**
     * 设置交易金额
     *
     * @param payAmount 交易金额
     */
    public void setPayAmount(BigDecimal payAmount) {
        this.payAmount = payAmount;
    }

    /**
     * @return currency
     */
    public String getCurrency() {
        return currency;
    }

    /**
     * @param currency
     */
    public void setCurrency(String currency) {
        this.currency = currency == null ? null : currency.trim();
    }

    /**
     * 获取实际交易金额
     *
     * @return actual_pay_amount - 实际交易金额
     */
    public BigDecimal getActualPayAmount() {
        return actualPayAmount;
    }

    /**
     * 设置实际交易金额
     *
     * @param actualPayAmount 实际交易金额
     */
    public void setActualPayAmount(BigDecimal actualPayAmount) {
        this.actualPayAmount = actualPayAmount;
    }

    /**
     * 获取手续费
     *
     * @return service_fee - 手续费
     */
    public BigDecimal getServiceFee() {
        return serviceFee;
    }

    /**
     * 设置手续费
     *
     * @param serviceFee 手续费
     */
    public void setServiceFee(BigDecimal serviceFee) {
        this.serviceFee = serviceFee;
    }

    /**
     * 获取红包、零钱，退款时需要考虑如何处理
     *
     * @return other_fee - 红包、零钱，退款时需要考虑如何处理
     */
    public String getOtherFee() {
        return otherFee;
    }

    /**
     * 设置红包、零钱，退款时需要考虑如何处理
     *
     * @param otherFee 红包、零钱，退款时需要考虑如何处理
     */
    public void setOtherFee(String otherFee) {
        this.otherFee = otherFee == null ? null : otherFee.trim();
    }

    /**
     * 获取支付渠道名称
     *
     * @return channel_name - 支付渠道名称
     */
    public String getChannelName() {
        return channelName;
    }

    /**
     * 设置支付渠道名称
     *
     * @param channelName 支付渠道名称
     */
    public void setChannelName(String channelName) {
        this.channelName = channelName == null ? null : channelName.trim();
    }

    /**
     * 获取支付渠道code
     *
     * @return channel_code - 支付渠道code
     */
    public String getChannelCode() {
        return channelCode;
    }

    /**
     * 设置支付渠道code
     *
     * @param channelCode 支付渠道code
     */
    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode == null ? null : channelCode.trim();
    }

    /**
     * @return warning
     */
    public String getWarning() {
        return warning;
    }

    /**
     * @param warning
     */
    public void setWarning(String warning) {
        this.warning = warning == null ? null : warning.trim();
    }

    public Boolean getGneteReceivableTransStatus() {
        return gneteReceivableTransStatus;
    }

    public void setGneteReceivableTransStatus(Boolean gneteReceivableTransStatus) {
        this.gneteReceivableTransStatus = gneteReceivableTransStatus;
    }

    public String getGneteReceivableTransResult() {
        return gneteReceivableTransResult;
    }

    public void setGneteReceivableTransResult(String gneteReceivableTransResult) {
        this.gneteReceivableTransResult = gneteReceivableTransResult;
    }

    public String getGneteReceivableTransOrderNo() {
        return gneteReceivableTransOrderNo;
    }

    public void setGneteReceivableTransOrderNo(String gneteReceivableTransOrderNo) {
        this.gneteReceivableTransOrderNo = gneteReceivableTransOrderNo;
    }

    /**
     * @return del_flg
     */
    public Boolean getDelFlg() {
        return delFlg;
    }

    /**
     * @param delFlg
     */
    public void setDelFlg(Boolean delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * @return create_user
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * @param createUser
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * @return create_time
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * @param createTime
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * @return update_user
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * @param updateUser
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * @return update_time
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * @param updateTime
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", refundBillDetailId=").append(refundBillDetailId);
        sb.append(", refundBillDetailCode=").append(refundBillDetailCode);
        sb.append(", relatedPaymentBillDetailCode=").append(relatedPaymentBillDetailCode);
        sb.append(", subject=").append(subject);
        sb.append(", refundBillNo=").append(refundBillNo);
        sb.append(", status=").append(status);
        sb.append(", payeeMemberId=").append(payeeMemberId);
        sb.append(", payeeMemberName=").append(payeeMemberName);
        sb.append(", payAmount=").append(payAmount);
        sb.append(", currency=").append(currency);
        sb.append(", actualPayAmount=").append(actualPayAmount);
        sb.append(", serviceFee=").append(serviceFee);
        sb.append(", otherFee=").append(otherFee);
        sb.append(", channelName=").append(channelName);
        sb.append(", channelCode=").append(channelCode);
        sb.append(", warning=").append(warning);
        sb.append(", gneteReceivableTransStatus=").append(gneteReceivableTransStatus);
        sb.append(", gneteReceivableTransOrderNo=").append(gneteReceivableTransOrderNo);
        sb.append(", gneteReceivableTransResult=").append(gneteReceivableTransResult);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}