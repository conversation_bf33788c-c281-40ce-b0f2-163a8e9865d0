package com.ecommerce.pay.v2.dao.vo;

import io.swagger.v3.oas.annotations.media.Schema;

import jakarta.persistence.Column;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Table(name = "pa_bill_split_pay_detail")
public class SplitPayBillDetail implements Serializable {
    @Id
    @Column(name = "split_pay_bill_detail_id")
    private String splitPayBillDetailId;

    /**
     * 编号
     */
    @Column(name = "split_pay_bill_detail_code")
    private String splitPayBillDetailCode;

    /**
     * 退款时原来的支付单
     */
    @Column(name = "related_payment_bill_detail_code")
    private String relatedPaymentBillDetailCode;

    /**
     * 支付单编号（流水号）
     */
    private String subject;

    /**
     * 支付单编号（流水号）
     */
    @Column(name = "split_pay_bill_no")
    private String splitPayBillNo;

    private String status;

    /**
     * 收款方会员ID
     */
    @Column(name = "payee_member_id")
    private String payeeMemberId;

    /**
     * 收款方会员名称
     */
    @Column(name = "payee_member_name")
    private String payeeMemberName;

    @Schema(description = "收款方银联钱包id")
    @Column(name = "payee_wallet_id")
    private String payeeWalletId;

    /**
     * 交易金额
     */
    @Column(name = "pay_amount")
    private BigDecimal payAmount;

    private String currency;

    /**
     * 实际交易金额
     */
    @Column(name = "actual_pay_amount")
    private BigDecimal actualPayAmount;

    /**
     * 手续费
     */
    @Column(name = "service_fee")
    private BigDecimal serviceFee;

    /**
     * 红包、零钱，退款时需要考虑如何处理
     */
    @Column(name = "other_fee")
    private String otherFee;

    /**
     * 支付渠道名称
     */
    @Column(name = "channel_name")
    private String channelName;

    /**
     * 支付渠道code
     */
    @Column(name = "channel_code")
    private String channelCode;

    private String warning;

    @Schema(description = "银联支付-结算单号（分账交易银联单号）")
    @Column(name = "sett_order_no")
    private String settOrderNo;

    @Schema(description = "银联支付-交易流水-电商流水号")
    @Column(name = "mct_order_no")
    private String mctOrderNo;

    @Schema(description = "银联支付-担保支付订单号")
    @Column(name = "guaranteed_order_no")
    private String guaranteedOrderNo;

    @Column(name = "del_flg")
    private Boolean delFlg;

    @Column(name = "create_user")
    private String createUser;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_user")
    private String updateUser;

    @Column(name = "update_time")
    private Date updateTime;

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * @return split_pay_bill_detail_id
     */
    public String getSplitPayBillDetailId() {
        return splitPayBillDetailId;
    }

    /**
     * @param splitPayBillDetailId
     */
    public void setSplitPayBillDetailId(String splitPayBillDetailId) {
        this.splitPayBillDetailId = splitPayBillDetailId == null ? null : splitPayBillDetailId.trim();
    }

    public String getRelatedPaymentBillDetailCode() {
        return relatedPaymentBillDetailCode;
    }

    public void setRelatedPaymentBillDetailCode(String relatedPaymentBillDetailCode) {
        this.relatedPaymentBillDetailCode = relatedPaymentBillDetailCode;
    }

    /**
     * 获取编号
     *
     * @return split_pay_bill_detail_code - 编号
     */
    public String getSplitPayBillDetailCode() {
        return splitPayBillDetailCode;
    }

    /**
     * 设置编号
     *
     * @param splitPayBillDetailCode 编号
     */
    public void setSplitPayBillDetailCode(String splitPayBillDetailCode) {
        this.splitPayBillDetailCode = splitPayBillDetailCode == null ? null : splitPayBillDetailCode.trim();
    }

    /**
     * 获取支付单编号（流水号）
     *
     * @return subject - 支付单编号（流水号）
     */
    public String getSubject() {
        return subject;
    }

    /**
     * 设置支付单编号（流水号）
     *
     * @param subject 支付单编号（流水号）
     */
    public void setSubject(String subject) {
        this.subject = subject == null ? null : subject.trim();
    }

    /**
     * 获取支付单编号（流水号）
     *
     * @return split_pay_bill_no - 支付单编号（流水号）
     */
    public String getSplitPayBillNo() {
        return splitPayBillNo;
    }

    /**
     * 设置支付单编号（流水号）
     *
     * @param splitPayBillNo 支付单编号（流水号）
     */
    public void setSplitPayBillNo(String splitPayBillNo) {
        this.splitPayBillNo = splitPayBillNo == null ? null : splitPayBillNo.trim();
    }

    /**
     * @return status
     */
    public String getStatus() {
        return status;
    }

    /**
     * @param status
     */
    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    /**
     * 获取收款方会员ID
     *
     * @return payee_member_id - 收款方会员ID
     */
    public String getPayeeMemberId() {
        return payeeMemberId;
    }

    /**
     * 设置收款方会员ID
     *
     * @param payeeMemberId 收款方会员ID
     */
    public void setPayeeMemberId(String payeeMemberId) {
        this.payeeMemberId = payeeMemberId == null ? null : payeeMemberId.trim();
    }

    /**
     * 获取收款方会员名称
     *
     * @return payee_member_name - 收款方会员名称
     */
    public String getPayeeMemberName() {
        return payeeMemberName;
    }

    /**
     * 设置收款方会员名称
     *
     * @param payeeMemberName 收款方会员名称
     */
    public void setPayeeMemberName(String payeeMemberName) {
        this.payeeMemberName = payeeMemberName == null ? null : payeeMemberName.trim();
    }

    public String getPayeeWalletId() {
        return payeeWalletId;
    }

    public void setPayeeWalletId(String payeeWalletId) {
        this.payeeWalletId = payeeWalletId;
    }

    /**
     * 获取交易金额
     *
     * @return pay_amount - 交易金额
     */
    public BigDecimal getPayAmount() {
        return payAmount;
    }

    /**
     * 设置交易金额
     *
     * @param payAmount 交易金额
     */
    public void setPayAmount(BigDecimal payAmount) {
        this.payAmount = payAmount;
    }

    /**
     * @return currency
     */
    public String getCurrency() {
        return currency;
    }

    /**
     * @param currency
     */
    public void setCurrency(String currency) {
        this.currency = currency == null ? null : currency.trim();
    }

    /**
     * 获取实际交易金额
     *
     * @return actual_pay_amount - 实际交易金额
     */
    public BigDecimal getActualPayAmount() {
        return actualPayAmount;
    }

    /**
     * 设置实际交易金额
     *
     * @param actualPayAmount 实际交易金额
     */
    public void setActualPayAmount(BigDecimal actualPayAmount) {
        this.actualPayAmount = actualPayAmount;
    }

    /**
     * 获取手续费
     *
     * @return service_fee - 手续费
     */
    public BigDecimal getServiceFee() {
        return serviceFee;
    }

    /**
     * 设置手续费
     *
     * @param serviceFee 手续费
     */
    public void setServiceFee(BigDecimal serviceFee) {
        this.serviceFee = serviceFee;
    }

    /**
     * 获取红包、零钱，退款时需要考虑如何处理
     *
     * @return other_fee - 红包、零钱，退款时需要考虑如何处理
     */
    public String getOtherFee() {
        return otherFee;
    }

    /**
     * 设置红包、零钱，退款时需要考虑如何处理
     *
     * @param otherFee 红包、零钱，退款时需要考虑如何处理
     */
    public void setOtherFee(String otherFee) {
        this.otherFee = otherFee == null ? null : otherFee.trim();
    }

    /**
     * 获取支付渠道名称
     *
     * @return channel_name - 支付渠道名称
     */
    public String getChannelName() {
        return channelName;
    }

    /**
     * 设置支付渠道名称
     *
     * @param channelName 支付渠道名称
     */
    public void setChannelName(String channelName) {
        this.channelName = channelName == null ? null : channelName.trim();
    }

    /**
     * 获取支付渠道code
     *
     * @return channel_code - 支付渠道code
     */
    public String getChannelCode() {
        return channelCode;
    }

    /**
     * 设置支付渠道code
     *
     * @param channelCode 支付渠道code
     */
    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode == null ? null : channelCode.trim();
    }

    /**
     * @return warning
     */
    public String getWarning() {
        return warning;
    }

    /**
     * @param warning
     */
    public void setWarning(String warning) {
        this.warning = warning == null ? null : warning.trim();
    }

    public String getSettOrderNo() {
        return settOrderNo;
    }

    public void setSettOrderNo(String settOrderNo) {
        this.settOrderNo = settOrderNo;
    }

    public String getMctOrderNo() {
        return mctOrderNo;
    }

    public void setMctOrderNo(String mctOrderNo) {
        this.mctOrderNo = mctOrderNo;
    }

    public String getGuaranteedOrderNo() {
        return guaranteedOrderNo;
    }

    public void setGuaranteedOrderNo(String guaranteedOrderNo) {
        this.guaranteedOrderNo = guaranteedOrderNo;
    }

    /**
     * @return del_flg
     */
    public Boolean getDelFlg() {
        return delFlg;
    }

    /**
     * @param delFlg
     */
    public void setDelFlg(Boolean delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * @return create_user
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * @param createUser
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * @return create_time
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * @param createTime
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * @return update_user
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * @param updateUser
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * @return update_time
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * @param updateTime
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", splitPayBillDetailId=").append(splitPayBillDetailId);
        sb.append(", splitPayBillDetailCode=").append(splitPayBillDetailCode);
        sb.append(", relatedPaymentBillDetailCode=").append(relatedPaymentBillDetailCode);
        sb.append(", subject=").append(subject);
        sb.append(", splitPayBillNo=").append(splitPayBillNo);
        sb.append(", status=").append(status);
        sb.append(", payeeMemberId=").append(payeeMemberId);
        sb.append(", payeeMemberName=").append(payeeMemberName);
        sb.append(", payeeWalletId=").append(payeeWalletId);
        sb.append(", payAmount=").append(payAmount);
        sb.append(", currency=").append(currency);
        sb.append(", actualPayAmount=").append(actualPayAmount);
        sb.append(", serviceFee=").append(serviceFee);
        sb.append(", otherFee=").append(otherFee);
        sb.append(", channelName=").append(channelName);
        sb.append(", channelCode=").append(channelCode);
        sb.append(", warning=").append(warning);
        sb.append(", settOrderNo=").append(settOrderNo);
        sb.append(", mctOrderNo=").append(mctOrderNo);
        sb.append(", guaranteedOrderNo=").append(guaranteedOrderNo);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}