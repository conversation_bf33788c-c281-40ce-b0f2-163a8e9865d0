package com.ecommerce.pay.v2.dao.vo;

import lombok.Data;

import jakarta.persistence.Column;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Table(name = "pa_bill_split_pay_quantity_info")
public class SplitPayBillQuantityInfo implements Serializable {
    @Serial
    private static final long serialVersionUID = -6493634665351246187L;
    @Id
    @Column(name = "pa_bill_split_pay_quantity_info_id")
    private String paBillSplitPayQuantityInfoId;

    @Column(name = "split_pay_bill_id")
    private String splitPayBillId;

    @Column(name = "split_pay_bill_no")
    private String splitPayBillNo;

    @Column(name = "order_no")
    private String orderNo;

    @Column(name = "goods_id")
    private String goodsId;
    /**
     * 商品名称
     */
    @Column(name = "goods_name")
    private String goodsName;

    /**
     * 计量单位名称
     */
    @Column(name = "units_name")
    private String unitsName;

    /**
     * 累计已发货数量
     */
    @Column(name = "item_send_quantity")
    private BigDecimal itemSendQuantity;

    /**
     * 当前分账对应的发货数量
     */
    @Column(name = "item_send_incremental")
    private BigDecimal itemSendIncremental;

    @Column(name = "del_flg")
    private Boolean delFlg;

    @Column(name = "create_user")
    private String createUser;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_user")
    private String updateUser;

    @Column(name = "update_time")
    private Date updateTime;

}