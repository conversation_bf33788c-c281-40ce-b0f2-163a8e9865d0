package com.ecommerce.pay.v2.dao.vo;

import java.io.Serial;
import java.io.Serializable;
import jakarta.persistence.*;

@Table(name = "pa_superbank")
public class SuperBank implements Serializable {
    @Column(name = "bank_no")
    private String bankNo;

    @Column(name = "bank_name")
    private String bankName;

    @Column(name = "bank_alias_name")
    private String bankAliasName;

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * @return bank_no
     */
    public String getBankNo() {
        return bankNo;
    }

    /**
     * @param bankNo
     */
    public void setBankNo(String bankNo) {
        this.bankNo = bankNo == null ? null : bankNo.trim();
    }

    /**
     * @return bank_name
     */
    public String getBankName() {
        return bankName;
    }

    /**
     * @param bankName
     */
    public void setBankName(String bankName) {
        this.bankName = bankName == null ? null : bankName.trim();
    }

    /**
     * @return bank_alias_name
     */
    public String getBankAliasName() {
        return bankAliasName;
    }

    /**
     * @param bankAliasName
     */
    public void setBankAliasName(String bankAliasName) {
        this.bankAliasName = bankAliasName == null ? null : bankAliasName.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", bankNo=").append(bankNo);
        sb.append(", bankName=").append(bankName);
        sb.append(", bankAliasName=").append(bankAliasName);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}