package com.ecommerce.pay.v2.dao.vo;

import lombok.Data;

import jakarta.persistence.Column;
import jakarta.persistence.Table;

import java.io.Serial;
import java.io.Serializable;

@Data
@Table(name = "pa_super_bank_code")
public class SuperBankCode implements Serializable {
    @Serial
    private static final long serialVersionUID = 961649175562484722L;
    /**
     * 超级网银行号
     */
    @Column(name = "super_bank_no")
    private String superBankNo;

    /**
     * 行号状态
     */
    @Column(name = "bank_status")
    private String bankStatus;

    /**
     * 行别代码
     */
    @Column(name = "bank_cls_code")
    private String bankClsCode;

    /**
     * 行名全称
     */
    @Column(name = "bank_name")
    private String bankName;

    /**
     * 清算行行号(所属直接参与行行号)
     */
    @Column(name = "direct_bank_no")
    private String directBankNo;

}