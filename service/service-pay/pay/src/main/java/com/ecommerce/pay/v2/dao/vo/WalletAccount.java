package com.ecommerce.pay.v2.dao.vo;

import jakarta.persistence.Column;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Table(name = "pa_wallet_account")
public class WalletAccount implements Serializable {
    @Id
    @Column(name = "wallet_account_id")
    private String walletAccountId;

    @Column(name = "wallet_account_no")
    private String walletAccountNo;

    @Column(name = "wallet_account_type")
    private String walletAccountType;

    @Column(name = "member_channel_id")
    private String memberChannelId;

    @Column(name = "member_channel_code")
    private String memberChannelCode;

    @Column(name = "member_id")
    private String memberId;

    @Column(name = "member_name")
    private String memberName;

    private String status;

    /**
     * 子账户
     */
    @Column(name = "ext_cust_acct_id")
    private String extCustAcctId;

    /**
     * 可用金额
     */
    @Column(name = "available_balance")
    private BigDecimal availableBalance;

    /**
     * 可提现金额
     */
    @Column(name = "cash_balance")
    private BigDecimal cashBalance;

    /**
     * 冻结金额
     */
    @Column(name = "freeze_amount")
    private BigDecimal freezeAmount;

    /**
     * 冻结金额
     */
    @Column(name = "advance_amount")
    private BigDecimal advanceAmount;

    /**
     * 删除标记
     */
    @Column(name = "del_flg")
    private Boolean delFlg;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * @return wallet_account_id
     */
    public String getWalletAccountId() {
        return walletAccountId;
    }

    /**
     * @param walletAccountId
     */
    public void setWalletAccountId(String walletAccountId) {
        this.walletAccountId = walletAccountId == null ? null : walletAccountId.trim();
    }

    /**
     * @return wallet_account_no
     */
    public String getWalletAccountNo() {
        return walletAccountNo;
    }

    /**
     * @param walletAccountNo
     */
    public void setWalletAccountNo(String walletAccountNo) {
        this.walletAccountNo = walletAccountNo == null ? null : walletAccountNo.trim();
    }

    /**
     * @return wallet_account_type
     */
    public String getWalletAccountType() {
        return walletAccountType;
    }

    /**
     * @param walletAccountType
     */
    public void setWalletAccountType(String walletAccountType) {
        this.walletAccountType = walletAccountType == null ? null : walletAccountType.trim();
    }

    public String getMemberChannelId() {
        return memberChannelId;
    }

    public void setMemberChannelId(String memberChannelId) {
        this.memberChannelId = memberChannelId;
    }

    public String getMemberChannelCode() {
        return memberChannelCode;
    }

    public void setMemberChannelCode(String memberChannelCode) {
        this.memberChannelCode = memberChannelCode;
    }

    public BigDecimal getAdvanceAmount() {
        return advanceAmount;
    }

    public void setAdvanceAmount(BigDecimal advanceAmount) {
        this.advanceAmount = advanceAmount;
    }

    /**
     * @return member_id
     */
    public String getMemberId() {
        return memberId;
    }

    /**
     * @param memberId
     */
    public void setMemberId(String memberId) {
        this.memberId = memberId == null ? null : memberId.trim();
    }

    /**
     * @return member_name
     */
    public String getMemberName() {
        return memberName;
    }

    /**
     * @param memberName
     */
    public void setMemberName(String memberName) {
        this.memberName = memberName == null ? null : memberName.trim();
    }

    /**
     * @return status
     */
    public String getStatus() {
        return status;
    }

    /**
     * @param status
     */
    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    /**
     * 获取子账户
     *
     * @return ext_cust_acct_id - 子账户
     */
    public String getExtCustAcctId() {
        return extCustAcctId;
    }

    /**
     * 设置子账户
     *
     * @param extCustAcctId 子账户
     */
    public void setExtCustAcctId(String extCustAcctId) {
        this.extCustAcctId = extCustAcctId == null ? null : extCustAcctId.trim();
    }

    /**
     * 获取可用金额
     *
     * @return available_balance - 可用金额
     */
    public BigDecimal getAvailableBalance() {
        return availableBalance;
    }

    /**
     * 设置可用金额
     *
     * @param availableBalance 可用金额
     */
    public void setAvailableBalance(BigDecimal availableBalance) {
        this.availableBalance = availableBalance;
    }

    /**
     * 获取可提现金额
     *
     * @return cash_balance - 可提现金额
     */
    public BigDecimal getCashBalance() {
        return cashBalance;
    }

    /**
     * 设置可提现金额
     *
     * @param cashBalance 可提现金额
     */
    public void setCashBalance(BigDecimal cashBalance) {
        this.cashBalance = cashBalance;
    }

    /**
     * 获取冻结金额
     *
     * @return freeze_amount - 冻结金额
     */
    public BigDecimal getFreezeAmount() {
        return freezeAmount;
    }

    /**
     * 设置冻结金额
     *
     * @param freezeAmount 冻结金额
     */
    public void setFreezeAmount(BigDecimal freezeAmount) {
        this.freezeAmount = freezeAmount;
    }

    /**
     * 获取删除标记
     *
     * @return del_flg - 删除标记
     */
    public Boolean getDelFlg() {
        return delFlg;
    }

    /**
     * 设置删除标记
     *
     * @param delFlg 删除标记
     */
    public void setDelFlg(Boolean delFlg) {
        this.delFlg = delFlg;
    }

    /**
     * 获取创建人
     *
     * @return create_user - 创建人
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建人
     *
     * @param createUser 创建人
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取修改人
     *
     * @return update_user - 修改人
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置修改人
     *
     * @param updateUser 修改人
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    /**
     * 获取修改时间
     *
     * @return update_time - 修改时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置修改时间
     *
     * @param updateTime 修改时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", walletAccountId=").append(walletAccountId);
        sb.append(", walletAccountNo=").append(walletAccountNo);
        sb.append(", walletAccountType=").append(walletAccountType);
        sb.append(", memberChannelId=").append(memberChannelId);
        sb.append(", memberChannelCode=").append(memberChannelCode);
        sb.append(", memberId=").append(memberId);
        sb.append(", memberName=").append(memberName);
        sb.append(", status=").append(status);
        sb.append(", extCustAcctId=").append(extCustAcctId);
        sb.append(", availableBalance=").append(availableBalance);
        sb.append(", cashBalance=").append(cashBalance);
        sb.append(", freezeAmount=").append(freezeAmount);
        sb.append(", advanceAmount=").append(advanceAmount);
        sb.append(", delFlg=").append(delFlg);
        sb.append(", createUser=").append(createUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}