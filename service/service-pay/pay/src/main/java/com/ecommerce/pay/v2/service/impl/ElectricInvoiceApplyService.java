package com.ecommerce.pay.v2.service.impl;

import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.pay.api.v2.dto.electricInvoiceAccount.PageQuery;
import com.ecommerce.pay.api.v2.dto.invoice.AuditInvoiceApplyDTO;
import com.ecommerce.pay.api.v2.dto.invoice.InvoiceApplyAddDTO;
import com.ecommerce.pay.api.v2.dto.invoice.InvoiceApplyBizListDTO;
import com.ecommerce.pay.api.v2.dto.invoice.InvoiceApplyBizListQueryDTO;
import com.ecommerce.pay.api.v2.dto.invoice.InvoiceApplyDetailDTO;
import com.ecommerce.pay.api.v2.dto.invoice.InvoiceApplyListDTO;
import com.ecommerce.pay.api.v2.dto.invoice.InvoiceApplyListQueryDTO;
import com.ecommerce.pay.api.v2.dto.invoice.RevokeInvoiceApplyDTO;
import com.ecommerce.pay.api.v2.dto.invoice.StatistcsInvoiceApplyDTO;
import com.ecommerce.pay.api.v2.dto.invoice.StatistcsInvoiceApplyResultDTO;
import com.ecommerce.pay.v2.biz.IElectricInvoiceApplyBiz;
import com.ecommerce.pay.v2.biz.IElectricInvoiceNotifyBiz;
import com.ecommerce.pay.v2.biz.IElectricInvoiceQueryBiz;
import com.ecommerce.pay.v2.service.IElectricInvoiceApplyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *  ElectricInvoiceApplyService
 *
 * <AUTHOR>
 */

@Slf4j
@Service("electricInvoiceApplyService")
public class ElectricInvoiceApplyService implements IElectricInvoiceApplyService {

    @Autowired
    private IElectricInvoiceApplyBiz electricInvoiceApplyBiz;

    @Autowired
    private IElectricInvoiceQueryBiz electricInvoiceQueryBiz;

    @Autowired
    private IElectricInvoiceNotifyBiz electricInvoiceNotifyBiz;

    @Override
    public ItemResult<Void> batchApplyInvoice(List<InvoiceApplyAddDTO> invoiceApplyAddList) {
        try {
            log.info("开始批量开票申请:{}", invoiceApplyAddList);
            electricInvoiceApplyBiz.batchApplyInvoice(invoiceApplyAddList);
            return new ItemResult<>(null);
        } catch (BizException be) {
            log.info("批量开票申请发生异常:", be);
            ItemResult<Void> result = new ItemResult<>();
            result.setSuccess(false);
            result.setCode(be.getErrorCode().getCode());
            result.setDescription(be.getMessage());
            return result;
        } catch (Exception e) {
            log.info("批量开票申请发生未知异常:", e);
            ItemResult<Void> result = new ItemResult<>();
            result.setSuccess(false);
            result.setCode(BasicCode.UNKNOWN_ERROR.getCode());
            result.setDescription(e.getMessage());
            return result;
        }
    }

//    @Scheduled(cron = "0 0/5 * * * ?")
    @Override
    public ItemResult<Void> batchInvoice() {
        try {
            log.info("开始批量开票定时任务");
            electricInvoiceApplyBiz.batchInvoice();
            return new ItemResult<>(null);
        } catch (BizException be) {
            log.info("批量开票定时任务发生异常:", be);
            ItemResult<Void> result = new ItemResult<>();
            result.setSuccess(false);
            result.setCode(be.getErrorCode().getCode());
            result.setDescription(be.getMessage());
            return result;
        } catch (Exception e) {
            log.info("批量开票定时任务发生未知异常:", e);
            ItemResult<Void> result = new ItemResult<>();
            result.setSuccess(false);
            result.setCode(BasicCode.UNKNOWN_ERROR.getCode());
            result.setDescription(e.getMessage());
            return result;
        }
    }

    @Override
    public ItemResult<Void> batchNotifyToOrderTask() {
        try {
            log.info("开始批量通知开票申请结果定时任务");
            electricInvoiceNotifyBiz.batchNotifyToOrderTask();
            return new ItemResult<>(null);
        } catch (BizException be) {
            log.info("批量通知开票申请结果定时任务发生异常:", be);
            ItemResult<Void> result = new ItemResult<>();
            result.setSuccess(false);
            result.setCode(be.getErrorCode().getCode());
            result.setDescription(be.getMessage());
            return result;
        } catch (Exception e) {
            log.info("批量通知开票申请结果定时任务发生未知异常:", e);
            ItemResult<Void> result = new ItemResult<>();
            result.setSuccess(false);
            result.setCode(BasicCode.UNKNOWN_ERROR.getCode());
            result.setDescription(e.getMessage());
            return result;
        }
    }

    @Override
    public ItemResult<PageData<InvoiceApplyListDTO>> queryInvoiceApplyList(PageQuery<InvoiceApplyListQueryDTO> pageQuery) {
        try {
            log.info("开始分页查询开票申请列表:{}", pageQuery);
            PageData<InvoiceApplyListDTO> pageData = electricInvoiceQueryBiz.queryInvoiceApplyList(pageQuery);
            log.info("分页查询开票申请列表结果为:{}", pageData);
            return new ItemResult<>(pageData);
        } catch (BizException be) {
            log.error("分页查询开票申请列表发生异常: {}", pageQuery,be);
            ItemResult<PageData<InvoiceApplyListDTO>> result = new ItemResult<>();
            result.setSuccess(false);
            result.setCode(be.getErrorCode().getCode());
            result.setDescription(be.getMessage());
            return result;
        } catch (Exception e) {
            log.info("分页查询开票申请列表发生未知异常:{}", pageQuery, e);
            ItemResult<PageData<InvoiceApplyListDTO>> result = new ItemResult<>();
            result.setSuccess(false);
            result.setCode(BasicCode.UNKNOWN_ERROR.getCode());
            result.setDescription(e.getMessage());
            return result;
        }
    }

    @Override
    public ItemResult<InvoiceApplyDetailDTO> queryInvoiceApplyDetail(String applyId) {
        try {
            log.info("开始获取发票申请详情:{}", applyId);
            InvoiceApplyDetailDTO detailDTO = electricInvoiceQueryBiz.queryInvoiceApplyDetail(applyId);
            log.info("获取发票申请详情结果为:{}", detailDTO);
            return new ItemResult<>(detailDTO);
        } catch (BizException be) {
            log.error("获取发票申请详情发生异常: {}", applyId, be);
            ItemResult<InvoiceApplyDetailDTO> result = new ItemResult<>();
            result.setSuccess(false);
            result.setCode(be.getErrorCode().getCode());
            result.setDescription(be.getMessage());
            return result;
        } catch (Exception e) {
            log.info("获取发票申请详情发生未知异常:{}", applyId, e);
            ItemResult<InvoiceApplyDetailDTO> result = new ItemResult<>();
            result.setSuccess(false);
            result.setCode(BasicCode.UNKNOWN_ERROR.getCode());
            result.setDescription(e.getMessage());
            return result;
        }
    }

    @Override
    public ItemResult<PageData<InvoiceApplyBizListDTO>> queryInvoiceApplyBizList(PageQuery<InvoiceApplyBizListQueryDTO> pageQuery) {
        try {
            log.info("开始分页查询发票业务明细列表:{}", pageQuery);
            PageData<InvoiceApplyBizListDTO> pageData = electricInvoiceQueryBiz.queryInvoiceApplyBizList(pageQuery);
            log.info("分页查询发票业务明细列表结果为:{}", pageData);
            return new ItemResult<>(pageData);
        } catch (BizException be) {
            log.error("分页查询发票业务明细列表发生异常: {}", pageQuery,be);
            ItemResult<PageData<InvoiceApplyBizListDTO>> result = new ItemResult<>();
            result.setSuccess(false);
            result.setCode(be.getErrorCode().getCode());
            result.setDescription(be.getMessage());
            return result;
        } catch (Exception e) {
            log.info("分页查询发票业务明细列表发生未知异常:{}", pageQuery, e);
            ItemResult<PageData<InvoiceApplyBizListDTO>> result = new ItemResult<>();
            result.setSuccess(false);
            result.setCode(BasicCode.UNKNOWN_ERROR.getCode());
            result.setDescription(e.getMessage());
            return result;
        }
    }

    @Override
    public ItemResult<Void> auditInvoiceApply(AuditInvoiceApplyDTO auditInvoiceApplyDTO) {
        try {
            log.info("开始审核发票申请:{}", auditInvoiceApplyDTO);
            electricInvoiceApplyBiz.auditInvoiceApply(auditInvoiceApplyDTO);
            return new ItemResult<>(null);
        } catch (BizException be) {
            log.error("审核发票申请发生异常: {}", auditInvoiceApplyDTO, be);
            ItemResult<Void> result = new ItemResult<>();
            result.setSuccess(false);
            result.setCode(be.getErrorCode().getCode());
            result.setDescription(be.getMessage());
            return result;
        } catch (Exception e) {
            log.error("审核发票申请未知异常: {}", auditInvoiceApplyDTO, e);
            ItemResult<Void> result = new ItemResult<>();
            result.setSuccess(false);
            result.setCode(BasicCode.UNKNOWN_ERROR.getCode());
            result.setDescription(e.getMessage());
            return result;
        }
    }

    @Override
    public ItemResult<Void> revokeInvoiceApply(RevokeInvoiceApplyDTO revokeInvoiceApplyDTO) {
        try {
            log.info("开始红冲发票申请:{}", revokeInvoiceApplyDTO);
            electricInvoiceApplyBiz.revokeInvoiceApply(revokeInvoiceApplyDTO);
            return new ItemResult<>(null);
        } catch (BizException be) {
            log.error("红冲发票申请发生异常: {}", revokeInvoiceApplyDTO, be);
            ItemResult<Void> result = new ItemResult<>();
            result.setSuccess(false);
            result.setCode(be.getErrorCode().getCode());
            result.setDescription(be.getMessage());
            return result;
        } catch (Exception e) {
            log.error("红冲发票申请未知异常: {}", revokeInvoiceApplyDTO, e);
            ItemResult<Void> result = new ItemResult<>();
            result.setSuccess(false);
            result.setCode(BasicCode.UNKNOWN_ERROR.getCode());
            result.setDescription(e.getMessage());
            return result;
        }
    }

    @Override
    public ItemResult<StatistcsInvoiceApplyResultDTO> statistcsInvoiceApply(StatistcsInvoiceApplyDTO statistcsInvoiceApplyDTO) {
        try {
            log.info("开始统计发票申请数据:{}", statistcsInvoiceApplyDTO);
            StatistcsInvoiceApplyResultDTO resultDTO = electricInvoiceQueryBiz.statistcsInvoiceApply(statistcsInvoiceApplyDTO);
            log.info("统计发票申请数据结果为:{}", resultDTO);
            return new ItemResult<>(resultDTO);
        } catch (BizException be) {
            log.error("统计发票申请数据发生异常: {}", statistcsInvoiceApplyDTO, be);
            ItemResult<StatistcsInvoiceApplyResultDTO> result = new ItemResult<>();
            result.setSuccess(false);
            result.setCode(be.getErrorCode().getCode());
            result.setDescription(be.getMessage());
            return result;
        } catch (Exception e) {
            log.info("统计发票申请数据发生未知异常:{}", statistcsInvoiceApplyDTO, e);
            ItemResult<StatistcsInvoiceApplyResultDTO> result = new ItemResult<>();
            result.setSuccess(false);
            result.setCode(BasicCode.UNKNOWN_ERROR.getCode());
            result.setDescription(e.getMessage());
            return result;
        }
    }
}
