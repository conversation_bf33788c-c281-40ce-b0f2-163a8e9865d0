package com.ecommerce.pay.v2.service.impl;

import com.alibaba.fastjson.JSON;
import com.ecommerce.common.enums.AppNames;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.exception.DistributeLockException;
import com.ecommerce.common.service.common.bsid.CommonBusinessIdGenerator;
import com.ecommerce.common.service.common.uuid.UUIDGenerator;
import com.ecommerce.common.service.lock.RedisLockService;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.member.api.dto.account.AccountDTO;
import com.ecommerce.member.api.dto.member.MemberDTO;
import com.ecommerce.member.api.dto.member.MemberDetailDTO;
import com.ecommerce.member.api.dto.member.MemberSimpleDTO;
import com.ecommerce.member.api.dto.member.enums.MemberPlatform;
import com.ecommerce.member.api.service.IAccountService;
import com.ecommerce.member.api.service.IMemberService;
import com.ecommerce.open.api.dto.gnete.ApplyTicketRequestDTO;
import com.ecommerce.open.api.dto.gnete.ApplyTicketResponseDTO;
import com.ecommerce.open.api.dto.gnete.ConfigDTO;
import com.ecommerce.open.api.dto.gnete.QueryWithholdGrantRequestDTO;
import com.ecommerce.open.api.dto.gnete.QueryWithholdGrantResponseDTO;
import com.ecommerce.open.api.dto.gnete.WithholdGrantRequestDTO;
import com.ecommerce.open.api.dto.gnete.enums.ApplyTicketJumpTypeEnum;
import com.ecommerce.pay.api.exception.PayCode;
import com.ecommerce.pay.api.v2.dto.AutoPayChannelRequestDTO;
import com.ecommerce.pay.api.v2.dto.ChannelCardDTO;
import com.ecommerce.pay.api.v2.dto.ChannelCardRequestDTO;
import com.ecommerce.pay.api.v2.dto.ChannelCardResponseDTO;
import com.ecommerce.pay.api.v2.dto.ChannelConfigDTO;
import com.ecommerce.pay.api.v2.dto.CreditMemberChannelDTO;
import com.ecommerce.pay.api.v2.dto.GneteAggregationChannelCloseDTO;
import com.ecommerce.pay.api.v2.dto.GneteAggregationChannelOpenDTO;
import com.ecommerce.pay.api.v2.dto.GneteWithholdGrantDTO;
import com.ecommerce.pay.api.v2.dto.GneteWithholdGrantQueryDTO;
import com.ecommerce.pay.api.v2.dto.MemberChannelCallbackRequestDTO;
import com.ecommerce.pay.api.v2.dto.MemberChannelCallbackResponseDTO;
import com.ecommerce.pay.api.v2.dto.MemberChannelCheckDTO;
import com.ecommerce.pay.api.v2.dto.MemberChannelDTO;
import com.ecommerce.pay.api.v2.dto.MemberChannelResponseDTO;
import com.ecommerce.pay.api.v2.dto.PasswordCallbackRequestDTO;
import com.ecommerce.pay.api.v2.dto.PasswordCallbackResponseDTO;
import com.ecommerce.pay.api.v2.dto.PasswordRequestDTO;
import com.ecommerce.pay.api.v2.dto.PasswordResponseDTO;
import com.ecommerce.pay.api.v2.dto.PaymentRequirementDTO;
import com.ecommerce.pay.api.v2.dto.SmallAmountTransferDTO;
import com.ecommerce.pay.api.v2.dto.bill.PaymentBillDTO;
import com.ecommerce.pay.api.v2.dto.bill.PaymentBillDetailDTO;
import com.ecommerce.pay.api.v2.dto.gnete.ApplyTicketReqDTO;
import com.ecommerce.pay.api.v2.dto.gnete.ApplyTicketResDTO;
import com.ecommerce.pay.api.v2.dto.gnete.FrontConfigDTO;
import com.ecommerce.pay.api.v2.dto.gnete.RegisterCallBackDTO;
import com.ecommerce.pay.api.v2.dto.query.BalanceQueryDTO;
import com.ecommerce.pay.api.v2.dto.query.ChannelQueryDTO;
import com.ecommerce.pay.api.v2.dto.query.DownloadStatementDTO;
import com.ecommerce.pay.api.v2.enums.AggregationPayStatusEnum;
import com.ecommerce.pay.api.v2.enums.ChannelCodeEnum;
import com.ecommerce.pay.api.v2.enums.ChannelPaymentTypeEnum;
import com.ecommerce.pay.api.v2.enums.ChannelTypeEnum;
import com.ecommerce.pay.api.v2.enums.ClientType;
import com.ecommerce.pay.api.v2.enums.CustomerEnum;
import com.ecommerce.pay.api.v2.enums.MemberChannelStatusEnum;
import com.ecommerce.pay.api.v2.enums.PaymentStatusEnum;
import com.ecommerce.pay.api.v2.enums.PaymentTypeEnum;
import com.ecommerce.pay.enums.MemberCreditRecordEnum;
import com.ecommerce.pay.v2.biz.IBillLogsBiz;
import com.ecommerce.pay.v2.biz.IChannelCardBiz;
import com.ecommerce.pay.v2.biz.IChannelConfigBiz;
import com.ecommerce.pay.v2.biz.IMemberChannelBiz;
import com.ecommerce.pay.v2.biz.IMemberCreditRecordBiz;
import com.ecommerce.pay.v2.biz.IPaymentIntegrationBiz;
import com.ecommerce.pay.v2.biz.impl.ChannelConfigBiz;
import com.ecommerce.pay.v2.biz.impl.PaymentBillBiz;
import com.ecommerce.pay.v2.biz.impl.PaymentBillDetailBiz;
import com.ecommerce.pay.v2.channel.adapter.gnete.GneteAdapter;
import com.ecommerce.pay.v2.dao.mapper.MemberChannelMapper;
import com.ecommerce.pay.v2.dao.vo.ChannelConfig;
import com.ecommerce.pay.v2.dao.vo.MemberChannel;
import com.ecommerce.pay.v2.executor.IChannelExecutor;
import com.ecommerce.pay.v2.service.IMemberChannelService;
import com.ecommerce.pay.v2.util.OrderGenUtil;
import com.ecommerce.pay.v2.util.gnete.RSAUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.DecoderException;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Condition;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Created: 21:17 04/12/2018
 * <AUTHOR>
 * @Description: TODO
 */
@Service
@Slf4j
public class MemberChannelService implements IMemberChannelService {

    @Autowired
    private IChannelConfigBiz channelConfigBiz;

    @Autowired
    private IMemberChannelBiz memberChannelBiz;

    @Autowired
    private IChannelExecutor channelExecutor;

    @Autowired
    private IPaymentIntegrationBiz paymentIntegrationBiz;

    @Autowired
    private IChannelCardBiz channelCardBiz;

    @Autowired
    private RedisLockService redisLockService;

    @Autowired
    private PaymentBillBiz paymentBillBiz;

    @Autowired
    private PaymentBillDetailBiz paymentBillDetailBiz;

    @Autowired
    private CommonBusinessIdGenerator takeCodeGenerator;
    @Autowired
    private UUIDGenerator uuidGenerator;
    @Autowired
    private IMemberService memberService;

    @Autowired
    private IAccountService accountService;

    @Autowired
    private IBillLogsBiz billLogsBiz;

    @Autowired
    private IMemberCreditRecordBiz memberCreditRecordBiz;
    @Autowired
    private GneteAdapter gneteAdapter;

    @Override
    public ChannelConfigDTO getChannelConfig(String channelConfigId, String operator) {
        return channelConfigBiz.findById(channelConfigId);
    }

    @Override
    public ChannelConfigDTO getChannelConfigByChannelCode(String channelCode) {
        if (CsStringUtils.isEmpty(channelCode)) {
            throw new BizException(BasicCode.PARAM_NULL, "channelCode");
        }
        return channelConfigBiz.findByChannelCode(channelCode);
    }

    @Override
    public List<ChannelConfigDTO> getChannelConfigPayerByIdList(List<String> channelIds, String payeeMemberId) {
        List<ChannelConfigDTO> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(channelIds) || CsStringUtils.isEmpty(payeeMemberId)) {
            return list;
        }
        List<MemberChannelDTO> memberChannelDTOList = memberChannelBiz.getMemberChannelByMemberId(payeeMemberId);
        List<ChannelConfigDTO> configDTOList = channelConfigBiz.findByIds(channelIds);
        if (CollectionUtils.isEmpty(configDTOList) || CollectionUtils.isEmpty(memberChannelDTOList)) {
            return list;
        }
        boolean support = false;
        Optional<MemberChannelDTO> dto = memberChannelDTOList.stream().filter(i -> ChannelCodeEnum.PINGANJZ.getCode().equals(i.getChannelCode())).findAny();
        if (dto.isPresent()) {
            String aggs = dto.get().getExt2();
            if (!CsStringUtils.isEmpty(aggs) && AggregationPayStatusEnum.SUPPORT_AGGREGATION.getCode().equals(aggs)) {
                support = true;
            }
        }
        boolean finalSupport = support;
        return configDTOList.stream()
                .filter(i -> memberChannelDTOList.stream().anyMatch(t -> t.getChannelId().equals(i.getChannelId())))
                .flatMap(i -> {
                    if (Boolean.FALSE.equals(i.getAllowPayment()) && i.getPayerPayeeSep()) {
                        List<ChannelConfigDTO> channelConfigDTOList = channelConfigBiz.findByReceiveId(i.getChannelId());
                        return channelConfigDTOList.stream();
                    } else {
                        return Stream.of(i);
                    }
                })
                .filter(i -> {
                    if (ChannelTypeEnum.PINGANJZ.getCode().equals(i.getChannelType())) {
                        return finalSupport || ChannelCodeEnum.PINGANJZPAY.getCode().equals(i.getChannelCode());
                    }
                    return true;
                })
                .sorted(Comparator.comparingInt(ChannelConfigDTO::getChannelSequence))
                .toList();
    }

    @Override
    public List<ChannelConfigDTO> getChannelConfigByChannelQuery(ChannelQueryDTO channelQueryDTO) {
        log.info("获取支付渠道 by {}", channelQueryDTO);
        ClientType clientType = ClientType.getByCode(channelQueryDTO.getClientType());
        if (CollectionUtils.isEmpty(channelQueryDTO.getChannelIds()) ||
                CsStringUtils.isEmpty(channelQueryDTO.getPayeeMemberId()) ||
                CsStringUtils.isEmpty(channelQueryDTO.getPayerMemberId())) {
            return Collections.emptyList();
        }
        MemberSimpleDTO memberSimpleDTO = memberService.findMemberSimpleById(channelQueryDTO.getPayerMemberId());
        if (memberSimpleDTO == null) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "会员不存在");
        }

        log.info(" 1查询系统已配置的所有渠道:");
        List<ChannelConfigDTO> channelConfigs = this.channelConfigBiz.getPlatformAvailChannels();
        log.info(" 1查询系统已配置的所有渠道:{}", channelConfigs.isEmpty() ? "[]" : channelConfigs.stream().map(ChannelConfigDTO::getChannelCode).collect(Collectors.joining(",")));

        log.info(" 1查询买家开通的所有支付渠道:");
        List<MemberChannelDTO> payerMemberChannels = this.memberChannelBiz.getMemberAvailChannels(channelQueryDTO.getPayerMemberId(), clientType);
        log.info(" 1查询买家开通的所有支付渠道:{}", payerMemberChannels.isEmpty() ? "[]" : payerMemberChannels.stream().map(MemberChannelDTO::getChannelCode).collect(Collectors.joining(",")));

        log.info(" 1查询卖家开通的所有支付渠道:");
        List<MemberChannelDTO> payeeMemberChannels = this.memberChannelBiz.getMemberAvailChannels(channelQueryDTO.getPayeeMemberId(), clientType);
        log.info(" 1查询卖家开通的所有支付渠道:{}", payeeMemberChannels.isEmpty() ? "[]" : payeeMemberChannels.stream().map(MemberChannelDTO::getChannelCode).collect(Collectors.joining(",")));
        // 改为消息队列同步

        if (!CollectionUtils.isEmpty(channelConfigs)) {
            List<ChannelConfigDTO> configDTOS = getChannelByPayerAndPeyeeAndClientAndBusiness(channelConfigs, clientType,
                    channelQueryDTO.getPayeeMemberId(), channelQueryDTO.getPayerMemberId(), channelQueryDTO.getChannelIds(),
                    payerMemberChannels, payeeMemberChannels, 0);
            if (memberSimpleDTO.getMemberType().startsWith(MemberDTO.ENTERPRISE_TYPE_PRE)) {
                log.info(" 当前用户是 [企业买家] 过滤 微信/支付宝");
                configDTOS = configDTOS.stream()
                        .filter(item ->
                                !item.getChannelCode().equals(ChannelCodeEnum.WEIXIN.getCode()) &&
                                        !item.getChannelCode().equals(ChannelCodeEnum.ALIPAY.getCode()) &&
                                        !item.getChannelCode().equals(ChannelCodeEnum.PINGANJZZFB.getCode()) &&
                                        !item.getChannelCode().equals(ChannelCodeEnum.PINGANJZWX.getCode()))
                        .toList();
            }

            configDTOS.sort(Comparator.comparingInt(ChannelConfigDTO::getChannelSequence));
            log.info("返回结果: {}", JSON.toJSONString(configDTOS));
            return configDTOS;
        }
        return Collections.emptyList();
    }

    @Override
    public MemberChannelResponseDTO openMemberChannel(MemberChannelDTO memberChannelDTO, String channelPaymentType, String operator) {
        String key = "openMemberChannel_" + memberChannelDTO.getChannelCode() + "_" + memberChannelDTO.getMemberId();
        log.info("开通支付渠道: {}",key);
        String lock = null;
        try {
            lock = redisLockService.lockFast(key);

            this.validateParamByMemberChannelDTO(memberChannelDTO);
            ChannelConfigDTO channelConfigDTO = channelConfigBiz.findByChannelCode(memberChannelDTO.getChannelCode());
            String ext1 = memberChannelDTO.getExt1();
            String ext2 = memberChannelDTO.getExt2();
            String ext3 = memberChannelDTO.getExt3();
            String ext4 = memberChannelDTO.getExt4();
            String ext5 = memberChannelDTO.getExt5();
            this.validateMemberChannelByOpen(channelConfigDTO, channelPaymentType);

            ChannelPaymentTypeEnum paymentTypeEnum = ChannelPaymentTypeEnum.getByCode(channelPaymentType);
            if (paymentTypeEnum == null) {
                throw new BizException(BasicCode.PARAM_NULL, "channelPaymentType");
            }
            List<MemberChannelDTO> temp = memberChannelBiz.findByMemberIdAndChannelCode2(memberChannelDTO.getMemberId(), channelConfigDTO.getChannelCode());
            int openFlag = 0;
            if (!CollectionUtils.isEmpty(temp)) {
                Optional<MemberChannelDTO> dto = temp.stream()
                        //ChannelCodeEnum.OFFLINE不看opend状态
                        .filter(item->item.getChannelId().equalsIgnoreCase(channelConfigDTO.getChannelId()))
                        .filter(item->!channelConfigDTO.getPayerAcctSpec() || item.getPayeeMemberId().equals(memberChannelDTO.getPayeeMemberId()))
                        .findAny();
                if (dto.isPresent()) {
                    log.info("会员已拥有此支付渠道, 执行更新操作");
                    memberChannelDTO.setMemberChannelId(dto.get().getMemberChannelId());
                    openFlag = 1;
                    //ChannelCodeEnum.OFFLINE不看opend状态
                    if (ChannelCodeEnum.OFFLINE.getCode().equals(memberChannelDTO.getChannelCode()) ){
                        //如果要求开通渠道，但是上送状态为BE_STOP，则不做任何操作
                        if( MemberChannelStatusEnum.BE_STOP.getCode().equalsIgnoreCase(memberChannelDTO.getMemberChannelStatus()) ){
                            MemberChannelResponseDTO memberChannelResponseDTO = new MemberChannelResponseDTO();
                            memberChannelResponseDTO.setMemberChannelDTO(dto.get());
                            memberChannelResponseDTO.setOpenFlag(2);
                            return memberChannelResponseDTO;
                        }else{
                            if ( channelCardBiz.getChannelCardCountByMemberChannelId2(dto.get().getMemberChannelId()) < 1 ) {
                                throw new BizException(BasicCode.UNDEFINED_ERROR, "请先添加线下收款账户");
                            }
                        }
                    }
                } else {
                    log.info("会员支付渠道不存在, 执行新增操作");
                    //ChannelCodeEnum.OFFLINE不看opend状态
                    if (ChannelCodeEnum.OFFLINE.getCode().equals(memberChannelDTO.getChannelCode()) && !MemberChannelStatusEnum.BE_STOP.getCode().equalsIgnoreCase(memberChannelDTO.getMemberChannelStatus()) ) {
                        throw new BizException(BasicCode.UNDEFINED_ERROR, "请先添加线下收款账户");
                    }
                    BeanUtils.copyProperties(channelConfigDTO, memberChannelDTO);
                }
            } else {
                //ChannelCodeEnum.OFFLINE不看opend状态
                if (ChannelCodeEnum.OFFLINE.getCode().equals(memberChannelDTO.getChannelCode()) && !MemberChannelStatusEnum.BE_STOP.getCode().equalsIgnoreCase(memberChannelDTO.getMemberChannelStatus()) ) {
                    throw new BizException(BasicCode.UNDEFINED_ERROR, "请先添加线下收款账户");
                }
                BeanUtils.copyProperties(channelConfigDTO, memberChannelDTO);
            }
            memberChannelDTO.setExt1(ext1);
            memberChannelDTO.setExt2(ext2);
            memberChannelDTO.setExt3(ext3);
            memberChannelDTO.setExt4(ext4);
            memberChannelDTO.setExt5(ext5);
            if (Boolean.TRUE.equals(channelConfigDTO.getPayerPayeeSep())) {
                if (ChannelPaymentTypeEnum.PAYEE.getCode().equals(paymentTypeEnum.getCode())) {
                    memberChannelDTO.setAllowPayment(false);
                    memberChannelDTO.setAllowReceive(true);
                }
                else if (ChannelPaymentTypeEnum.PAYER.getCode().equals(paymentTypeEnum.getCode())) {
                    memberChannelDTO.setAllowPayment(true);
                    memberChannelDTO.setAllowReceive(false);
                }
            }

            if (CsStringUtils.isEmpty(memberChannelDTO.getMemberName())) {
                log.info("无会员名称 设置会员名称");
                MemberDetailDTO memberDetailDTO = memberService.findMemberById(memberChannelDTO.getMemberId());
                if(memberDetailDTO == null) {
                    log.error("无会员名称 无法查找会员信息");
                }else {
                    memberChannelDTO.setMemberName(memberDetailDTO.getMemberName());
                    memberChannelDTO.setMemberCode(memberDetailDTO.getMemberCode());
                }
            }
            MemberChannelResponseDTO responseDTO =  channelExecutor.doOpenMemberChannel(memberChannelDTO, operator);
            //设置开通渠道的内部操作类型：0开通渠道  1更新渠道 2未做任何操作
            if( responseDTO != null && responseDTO.getOpenFlag() == null ){
                responseDTO.setOpenFlag(openFlag);
            }
            return responseDTO;
        } catch (DistributeLockException e) {
            log.error("  >>> OpenMemberChannel : 获取锁失败");
            throw new BizException(BasicCode.UNDEFINED_ERROR, "操作太频繁，请稍后再试");
        } catch (Exception e) {
            log.error(" >>> OpenMemberChannel : {}", e.getMessage());
            throw e;
        } finally {
            if (lock != null) {
                redisLockService.unlock(key, lock);
            }
        }
    }

    @Override
    public MemberChannelResponseDTO closeMemberChannel(MemberChannelDTO memberChannelDTO, String channelPaymentType,
                                                       String operator) {
        this.validateParamByMemberChannelDTO(memberChannelDTO);

        MemberChannelDTO temp = memberChannelBiz.findByMemberIdAndChannelCode(memberChannelDTO.getMemberId(),
                memberChannelDTO.getChannelCode(), ChannelPaymentTypeEnum.getByCode(channelPaymentType));

        if (temp == null) {
            throw new BizException(PayCode.MEMBER_CHANNEL_CLOSE_FAIL, "当前会员未拥有此支付渠道");
        }
        temp.setGnetePersonalRegisterDTO(memberChannelDTO.getGnetePersonalRegisterDTO());

        return channelExecutor.doCloseMemberChannel(temp, operator);
    }

    @Override
    public MemberChannelCallbackResponseDTO openMemberChannelCallback(MemberChannelCallbackRequestDTO requestDTO) {
        return channelExecutor.doOpenMemberChannelCallback(requestDTO);
    }

    @Override
    public MemberChannelCallbackResponseDTO closeMemberChannelCallback(MemberChannelCallbackRequestDTO requestDTO) {
        return channelExecutor.doCloseMemberChannelCallback(requestDTO);
    }

    @Override
    public MemberChannelDTO getMemberChannelInfo(String memberChannelId) {
        return memberChannelBiz.findByMemberChannelId(memberChannelId);
    }

    @Override
    public List<MemberChannelDTO> findMemberChannelListByPlatform(String memberId) {
        List<MemberChannelDTO> dtos = memberChannelBiz.getMemberChannelByMemberId(memberId);
        // 改为消息队列同步
        return dtos;
    }

    @Override
    public List<ChannelConfigDTO> getCustomerAvailChannels(String customer) {
        CustomerEnum customerEnum = CustomerEnum.getByCode(customer);
        return channelConfigBiz.getCustomerAvailChannels(customerEnum);
    }

    @Override
    public List<ChannelConfigDTO> getPlatformAvailChannels() {
        return channelConfigBiz.getPlatformAvailChannels();
    }

    @Override
    public List<ChannelConfigDTO> getPlatformAvailChannelsByPayer() {
        List<ChannelConfigDTO> channels = channelConfigBiz.getPlatformAvailChannels();
        ChannelConfigDTO aggs = new ChannelConfigDTO();
        List<ChannelConfigDTO> collect = channels.stream().filter(ChannelConfigDTO::getAllowPayment)
                .filter(i -> {
                    if (ChannelCodeEnum.PINGANJZWX.getCode().equals(i.getChannelCode()) ||
                            ChannelCodeEnum.PINGANJZZFB.getCode().equals(i.getChannelCode())) {
                        BeanUtils.copyProperties(i, aggs);
                        aggs.setChannelCode("aggregation");
                        aggs.setChannelName("聚合支付");
                        return false;
                    }
                    return true;
                })
                .toList();
        collect.add(aggs);
        return collect;
    }

    @Override
    public List<MemberChannelDTO> getMemberAvailChannels(String memberId, ClientType client) {
        return memberChannelBiz.getMemberAvailChannels(memberId, client);
    }

    @Override
    public List<MemberChannelDTO> getMemberAvailChannelsByPayee(String payerId, String bizId, ClientType client,
                                                                String paymentBizCode, Integer isMonthly, String operator) {
        if (client == null) {
            throw new BizException(PayCode.INVALID_PARAM);
        }
        if (log.isInfoEnabled()) {
            log.info(" 查询付款方{}，在终端{}，在执行支付业务 {} 时  可用的渠道信息 ", payerId, client.getMessage(), bizId);
        }
        if (CsStringUtils.isEmpty(payerId) || CsStringUtils.isEmpty(bizId) || CsStringUtils.isEmpty(paymentBizCode)) {
            throw new BizException(PayCode.INVALID_PARAM);
        }
        PaymentRequirementDTO paymentRequirementDTO = paymentIntegrationBiz.getPaymentRequirement(bizId, paymentBizCode,
                operator);
        log.info(" 查询支付需求结果  {} ", paymentRequirementDTO);
        if (paymentRequirementDTO == null) {
            throw new BizException(PayCode.PARAM_NULL, " 未查询到 支付需求 ");
        }
        if (CsStringUtils.isNotBlank(paymentRequirementDTO.getExceptionMsg())) {
            throw new BizException(PayCode.CUSTOM_ERROR, paymentRequirementDTO.getExceptionMsg());
        }
        String payeeId = paymentRequirementDTO.getPayeeId();

        MemberSimpleDTO memberSimpleDTO = memberService.findMemberSimpleById(payerId);
        if (memberSimpleDTO == null) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "买家不存在");
        }

        log.info(" 2查询系统已配置的所有渠道");
        List<ChannelConfigDTO> channelConfigs = this.channelConfigBiz.getPlatformAvailChannels();
        log.info(" 2查询系统已配置的所有渠道:{}", channelConfigs.isEmpty() ? "[]" : channelConfigs.stream().map(ChannelConfigDTO::getChannelCode).collect(Collectors.joining(",")));

        log.info(" 2查询买家开通的所有支付渠道");
        List<MemberChannelDTO> payerMemberChannels = this.memberChannelBiz.getMemberAvailChannels(payerId, client);
        log.info(" 2查询买家开通的所有支付渠道:{}", payerMemberChannels.isEmpty() ? "[]" : payerMemberChannels.stream().map(MemberChannelDTO::getChannelCode).collect(Collectors.joining(",")));

        log.info(" 2查询卖家开通的所有支付渠道");
        List<MemberChannelDTO> payeeMemberChannels = this.memberChannelBiz.getMemberAvailChannels(payeeId, client);
        log.info(" 2查询卖家开通的所有支付渠道:{}", payeeMemberChannels.isEmpty() ? "[]" : payeeMemberChannels.stream().map(MemberChannelDTO::getChannelCode).collect(Collectors.joining(",")));

        // 改为消息队列同步

        //当卖家已接入erp 且当前支付单对应的订单时erp订单 且当前支付单是补款，则补款渠道和订单支付渠道保持一致，这时方法返回的是第一次支付的支付渠道代码 见order对应方法的处理
        List<String> channelIds = paymentIntegrationBiz.getBizPaymentChannel(bizId, paymentBizCode, operator);
        //当前业务对应的订单的货款支付渠道
        List<String> erpOrderPayWay = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(channelIds)) {
            List<String> channelIdsUpdateResult = Lists.newArrayList();
            List<String> configLimitList = channelIds.stream().map(i -> {
                String channelId = i;
                String channelCode = "";
                for (ChannelConfigDTO channelConfig : channelConfigs) {
                    if (channelConfig.getChannelId().equals(i) ) {
                        channelId = channelConfig.getChannelId();
                        channelCode = channelConfig.getChannelCode();
                        channelIdsUpdateResult.add(channelId);
                        break;
                    }
                    if (CsStringUtils.equals(i, channelConfig.getChannelCode())) {
                        channelId = channelConfig.getChannelId();
                        channelCode = channelConfig.getChannelCode();
                        erpOrderPayWay.add(channelCode);
                        break;
                    }
                }
                return channelId + "-" + channelCode;
            }).toList();
            channelIds = channelIdsUpdateResult;
            log.info(" 查询到支付业务接入方对支付渠道的限制 {} ,erp订单补款限制支付渠道限制为:{}", configLimitList,erpOrderPayWay);
        }

        if (CollectionUtils.isEmpty(channelConfigs)) {
            return Collections.emptyList();
        }

        List<ChannelConfigDTO> paymentChannelConfigs = getChannelByPayerAndPeyeeAndClientAndBusiness(channelConfigs, client, payeeId, payerId, channelIds, payerMemberChannels, payeeMemberChannels, isMonthly);

        if (memberSimpleDTO.getMemberType().startsWith(MemberDTO.ENTERPRISE_TYPE_PRE)) {
            log.info(" 当前用户是 [企业买家] 过滤 微信/支付宝");
            paymentChannelConfigs = paymentChannelConfigs.stream()
                    .filter(item ->
                            !item.getChannelCode().equals(ChannelCodeEnum.WEIXIN.getCode()) &&
                                    !item.getChannelCode().equals(ChannelCodeEnum.ALIPAY.getCode()) &&
                                    !item.getChannelCode().equals(ChannelCodeEnum.PINGANJZZFB.getCode()) &&
                                    !item.getChannelCode().equals(ChannelCodeEnum.PINGANJZWX.getCode()))
                    .toList();
        }

        List<MemberChannelDTO> result = new ArrayList<>();
        if (!paymentChannelConfigs.isEmpty()) {
            result = paymentChannelConfigs.stream()
                    .map(config -> {
                        MemberChannelDTO data = new MemberChannelDTO();
                        BeanUtils.copyProperties(config, data);
                        if (!CollectionUtils.isEmpty(payerMemberChannels)) {
                            Optional<MemberChannelDTO> memberChannel = payerMemberChannels.stream()
                                    .filter(item -> {
                                        if (!config.getChannelId().equals(item.getChannelId())) {
                                            return false;
                                        }
                                        if (Boolean.TRUE.equals(item.getPayerAcctSpec())) {
                                            return payeeId.equals(item.getPayeeMemberId());
                                        }
                                        return true;
                                    }).findFirst();
                            if (memberChannel.isPresent()) {
                                data = memberChannel.get();
                            }
                        }
                        return data;
                    })
                    .sorted(Comparator.comparingInt(MemberChannelDTO::getChannelSequence))
                    .toList();
            if( !erpOrderPayWay.isEmpty() ){
                result = result.stream().filter(item->erpOrderPayWay.contains(item.getChannelCode())).toList();
                log.info("erp订单补款限制支付渠道限制为:{}",erpOrderPayWay);
            }
        }
        log.info("返回结果: {}", result.stream().map(MemberChannelDTO::getChannelCode).toList());
        log.info("resultDTO: {}", JSON.toJSONString(result));
        return result;
    }

    private List<ChannelConfigDTO> getChannelByPayerAndPeyeeAndClientAndBusiness(List<ChannelConfigDTO> channelConfigs,
                                                                                 ClientType client, String payeeId,
                                                                                 String payerId,
                                                                                 List<String> channelIds,
                                                                                 List<MemberChannelDTO> payerMemberChannels,
                                                                                 List<MemberChannelDTO> payeeMemberChannels, Integer isMonthly) {
        // 系统支持的付款渠道
        log.info("1.过滤付款渠道");
        List<ChannelConfigDTO> paymentChannelConfigs = channelConfigs.stream().filter(ChannelConfigDTO::getAllowPayment)
                .toList();

        // 根据支持的客户端过滤
        if (!paymentChannelConfigs.isEmpty()) {
            paymentChannelConfigs = paymentChannelConfigs.stream().filter(item -> {
                switch (client) {
                    case APP:
                        return Boolean.TRUE.equals(item.getIsSupportApp());
                    case MINI:
                        return Boolean.TRUE.equals(item.getIsSupportMini());
                    case PC:
                        return Boolean.TRUE.equals(item.getIsSupportWeb());
                    default:
                        return false;
                }
            }).toList();
        }
        log.info("2.根据支持的客户端 [{}] 过滤 {}", client.getCode(),
                paymentChannelConfigs.stream().map(ChannelConfigDTO::getChannelCode).toList());

        // 根据需要付款方开通的过滤掉付款方未开通的
        if (!CollectionUtils.isEmpty(paymentChannelConfigs)) {
            paymentChannelConfigs = paymentChannelConfigs.stream().filter(item -> {
                if (Boolean.TRUE.equals(item.getNeedPayerReg())) {
                    if (!CollectionUtils.isEmpty(payerMemberChannels)) {
                        for (MemberChannelDTO memberChannel : payerMemberChannels) {
                            if (!item.getChannelId().equals(memberChannel.getChannelId())) {
                                continue;
                            }
                            if (Boolean.TRUE.equals(item.getPayerAcctSpec())) {
                                if (!payeeId.equals(memberChannel.getPayeeMemberId())) {
                                    continue;
                                }
                            }
                            return true;
                        }
                    }
                    return false;
                } else {
                    return true;
                }
            }).toList();
        }
        log.info("3.根据需要付款方开通的过滤掉付款方未开通的 {}",
                paymentChannelConfigs.stream().map(ChannelConfigDTO::getChannelCode).toList());

        // 根据可以收款并且需要卖家开通过滤掉卖家未开通的
        if (!paymentChannelConfigs.isEmpty()) {
            paymentChannelConfigs = paymentChannelConfigs.stream().filter(item -> {
                if (Boolean.TRUE.equals(item.getNeedPayeeReg()) && Boolean.TRUE.equals(item.getAllowReceive())) {
                    if(!CollectionUtils.isEmpty(payeeMemberChannels)) {
                        for (MemberChannelDTO memberChannelDTO : payeeMemberChannels) {
                            if (memberChannelDTO.getChannelId().equals(item.getChannelId())) {
                                return true;
                            }
                        }
                    }
                    return false;
                }
                return true;
            }).toList();
        }
        log.info("4.根据可以收款并且需要卖家开通 过滤掉卖家未开通的 {}",
                paymentChannelConfigs.stream().map(ChannelConfigDTO::getChannelCode).toList());

        // 根据聚合收款并且需要卖家开通的过滤掉卖家未开通的
        if (!paymentChannelConfigs.isEmpty()) {
            paymentChannelConfigs = paymentChannelConfigs.stream().filter(item -> {
                if (CsStringUtils.isNotBlank(item.getReceiveChannelId())) {
                    for (ChannelConfigDTO channelConfig : channelConfigs) {
                        if (channelConfig.getChannelId().equals(item.getReceiveChannelId())) {
                            if (Boolean.FALSE.equals(channelConfig.getNeedPayeeReg())) {
                                return true;
                            } else {
                                if (!CollectionUtils.isEmpty(payeeMemberChannels)) {
                                    for (MemberChannelDTO memberChannelDTO : payeeMemberChannels) {
                                        if (memberChannelDTO.getChannelId().equals(channelConfig.getChannelId())) {
                                            return true;
                                        }
                                    }
                                }
                                return false;
                            }
                        }
                    }
                    return false;
                } else {
                    return true;
                }
            }).toList();
        }
        log.info("5.根据聚合收款并且需要卖家开通的过滤掉卖家未开通的 {}",
                paymentChannelConfigs.stream().map(ChannelConfigDTO::getChannelCode).toList());

        // 根据可以收款并且业务支持的过滤掉业务不支持的
        if (channelIds != null && !channelIds.isEmpty()) {
            if (!paymentChannelConfigs.isEmpty()) {
                paymentChannelConfigs = paymentChannelConfigs.stream().filter(item -> {
                    if (Boolean.TRUE.equals(item.getAllowReceive())) {
                        if (CollectionUtils.isEmpty(channelIds)) {
                            return true;
                        }
                        for (String channelId : channelIds) {
                            if (channelId.equals(item.getChannelId())) {
                                return true;
                            }
                        }
                        return false;
                    } else {
                        return true;
                    }
                }).toList();
            }
        }
        log.info("6.根据可以收款并且业务支持的过滤掉业务不支持的 {}",
                paymentChannelConfigs.stream().map(ChannelConfigDTO::getChannelCode).toList());

        // 根据可以聚合收款并且业务支持的过滤掉业务不支持的
        if (!paymentChannelConfigs.isEmpty()) {
            paymentChannelConfigs = paymentChannelConfigs.stream().filter(item -> {
                if (CsStringUtils.isNotBlank(item.getReceiveChannelId())) {
                    for (ChannelConfigDTO channelConfig : channelConfigs) {
                        if (channelConfig.getChannelId().equals(item.getReceiveChannelId())) {
                            if (CollectionUtils.isEmpty(channelIds)) {
                                return true;
                            }
                            for (String channelId : channelIds) {
                                if (channelId.equals(channelConfig.getChannelId())) {
                                    return true;
                                }
                            }
                            return false;
                        }
                    }
                    return false;
                } else {
                    return true;
                }
            }).toList();
        }
        log.info("7.根据可以聚合收款并且业务支持的过滤掉业务不支持的 {}",
                paymentChannelConfigs.stream().map(ChannelConfigDTO::getChannelCode).toList());

        // 根据是否支持聚合支付，过滤掉聚合支付
        boolean supportAggregation = false;
        if (!CollectionUtils.isEmpty(payeeMemberChannels)) {
            for (MemberChannelDTO memberChannel : payeeMemberChannels) {
                if (ChannelCodeEnum.PINGANJZ.getCode().equals(memberChannel.getChannelCode())) {
                    supportAggregation = memberChannelBiz.checkMemberChannelAggregation(memberChannel);
                    break;
                }
            }
        }
        boolean finalSupportAggregation = supportAggregation;
        if (!CollectionUtils.isEmpty(paymentChannelConfigs)) {
            paymentChannelConfigs = paymentChannelConfigs.stream().filter(item -> {
                if (ChannelTypeEnum.PINGANJZ.getCode().equals(item.getChannelType())) {
                    return finalSupportAggregation || item.getChannelCode().equals(ChannelCodeEnum.PINGANJZPAY.getCode());
                }
                return true;
            }).peek(i -> {
                if (ChannelTypeEnum.PINGANJZ.getCode().equals(i.getChannelType()) &&
                        !ChannelCodeEnum.PINGANJZPAY.getCode().equals(i.getChannelCode())) {
                    // 聚合支付在商品详情页使用(微信/支付宝)logo
                    i.setChannelLogo(i.getExt1());
                    i.setChannelInfo(i.getExt2());
                }
            }).toList();
        }
        log.info("8.根据是否支持聚合支付 [{}]，过滤掉聚合支付 {}", supportAggregation,
                paymentChannelConfigs.stream().map(ChannelConfigDTO::getChannelCode).toList());

        ChannelConfigDTO offline = null;
        for (ChannelConfigDTO dto : paymentChannelConfigs) {
            if (ChannelCodeEnum.OFFLINE.getCode().equals(dto.getChannelCode())) {
                offline = dto;
                break;
            }
        }
        if (offline != null) {
            for (MemberChannelDTO dto : payeeMemberChannels) {
                if (ChannelCodeEnum.OFFLINE.getCode().equals(dto.getChannelCode())) {
                    int cards = channelCardBiz.getChannelCardCountByMemberChannelId(dto.getMemberChannelId());
                    if (cards <= 0) {
                        paymentChannelConfigs.removeIf(i -> ChannelCodeEnum.OFFLINE.getCode().equals(i.getChannelCode()));
                    }
                    break;
                }
            }
        }
        log.info("9.根据卖家开通了线下支付，是否绑定了线下银行卡过滤 {}",
                paymentChannelConfigs.stream().map(ChannelConfigDTO::getChannelCode).toList());

        // 月结客户 只需要展示ERP余额支付
        log.info("10.月结客户 只需要展示ERP余额支付 {},{},{}", payeeId, payerId, isMonthly);
        if(isMonthly == 1)
        {
            List<ChannelConfigDTO> newPaymentChannelConfigs = paymentChannelConfigs.stream().filter(item -> ChannelCodeEnum.ERP.getCode().equals(item.getChannelCode())).toList();
            if(newPaymentChannelConfigs.size() > 0)
            {
                log.info("10A.月结客户 过滤后没有ERP支付了，故不过滤");
                paymentChannelConfigs = newPaymentChannelConfigs;
            }
        }

        return paymentChannelConfigs;
    }

    @Override
    public List<MemberChannelDTO> getMemberAvailChannelsByPayee2(AutoPayChannelRequestDTO autoPayChannelRequestDTO){
        log.info("autoPayChannelRequestDTO: {}",autoPayChannelRequestDTO);
        String payerId = autoPayChannelRequestDTO.getPayerId();
        String payeeId = autoPayChannelRequestDTO.getPayeeId();
        ClientType client = autoPayChannelRequestDTO.getClient();
        List<String> channelIds = CollectionUtils.isEmpty(autoPayChannelRequestDTO.getSupportChannelIds()) ? Lists.newArrayList() : Lists.newArrayList(autoPayChannelRequestDTO.getSupportChannelIds());

        List<MemberChannelDTO> payerMemberChannels = this.memberChannelBiz.getMemberAvailChannels(payerId, client);
        List<ChannelConfigDTO> channelConfigs = this.channelConfigBiz.getPlatformAvailChannels();
        List<MemberChannelDTO> payeeMemberChannels = this.memberChannelBiz.getMemberAvailChannels(payeeId, client);

        MemberSimpleDTO memberSimpleDTO = memberService.findMemberSimpleById(payerId);
        List<ChannelConfigDTO> paymentChannelConfigs = getChannelByPayerAndPeyeeAndClientAndBusiness(channelConfigs, client, payeeId, payerId, channelIds, payerMemberChannels, payeeMemberChannels, 0);

        if (memberSimpleDTO.getMemberType().startsWith(MemberDTO.ENTERPRISE_TYPE_PRE)) {
            log.info(" 当前用户是 [企业买家] 过滤 微信/支付宝");
            paymentChannelConfigs = paymentChannelConfigs.stream()
                    .filter(item ->
                            !item.getChannelCode().equals(ChannelCodeEnum.WEIXIN.getCode()) &&
                                    !item.getChannelCode().equals(ChannelCodeEnum.ALIPAY.getCode()) &&
                                    !item.getChannelCode().equals(ChannelCodeEnum.PINGANJZZFB.getCode()) &&
                                    !item.getChannelCode().equals(ChannelCodeEnum.PINGANJZWX.getCode()))
                    .toList();
        }

        List<MemberChannelDTO> result = new ArrayList<>();
        if (!paymentChannelConfigs.isEmpty()) {
            result = paymentChannelConfigs.stream()
                    .map(config -> {
                        MemberChannelDTO data = new MemberChannelDTO();
                        BeanUtils.copyProperties(config, data);
                        if (!CollectionUtils.isEmpty(payerMemberChannels)) {
                            Optional<MemberChannelDTO> memberChannel = payerMemberChannels.stream()
                                    .filter(item -> {
                                        if (!config.getChannelId().equals(item.getChannelId())) {
                                            return false;
                                        }
                                        if (Boolean.TRUE.equals(item.getPayerAcctSpec())) {
                                            return payeeId.equals(item.getPayeeMemberId());
                                        }
                                        return true;
                                    }).findFirst();
                            if (memberChannel.isPresent()) {
                                data = memberChannel.get();
                            }
                        }
                        return data;
                    })
                    .sorted(Comparator.comparingInt(MemberChannelDTO::getChannelSequence))
                    .toList();
            if( !CollectionUtils.isEmpty(autoPayChannelRequestDTO.getSupportChannelIds()) ){
                result = result.stream().filter(item->autoPayChannelRequestDTO.getSupportChannelIds().contains(item.getChannelId())).toList();
                log.info("剔除限制的渠道:{}",autoPayChannelRequestDTO.getSupportChannelIds());
            }
        }
        log.info("返回结果: {}", result.stream().map(MemberChannelDTO::getChannelCode).toList());
        log.info("resultDTO: {}", JSON.toJSONString(result));
        return result;
    }

    @Override
    public String getMchkeyByMchNo(String mchNo) {
        return memberChannelBiz.getMchkeyByMchNo(mchNo);
    }

    @Override
    public List<ChannelConfigDTO> findBuyerAvailChannel() {
        ChannelConfig channelConfig = new ChannelConfig();
        channelConfig.setNeedPayerReg(false);
        channelConfig.setChannelStatus(true);
        channelConfig.setDelFlg(false);
        List<ChannelConfig> channelConfigList = channelConfigBiz.find(channelConfig);
        if (channelConfigList != null && !channelConfigList.isEmpty()) {
            return channelConfigList.stream().map(ChannelConfigBiz::convertToDTO).toList();
        }
        return new ArrayList<>();
    }

    @Override
    public PageInfo<MemberChannelDTO> pageMemberChannelDTO(MemberChannelDTO memberChannelDTO) {

        log.info("授信列表: {}", JSON.toJSONString(memberChannelDTO));

        Page<MemberChannelDTO> memberChannelDTOS = memberChannelBiz.pageMemberChannelDTO(memberChannelDTO);

        for(MemberChannelDTO dto : memberChannelDTOS)
        {
            // 计算可用余额
            BigDecimal balanceAmount = dto.getBalanceAmount() == null ? BigDecimal.ZERO : dto.getBalanceAmount();
            BigDecimal arrearsAmount = dto.getArrearsAmount() == null ? BigDecimal.ZERO : dto.getArrearsAmount();
            dto.setAvailableAmount(balanceAmount.subtract(arrearsAmount));

            // 欠款为负数时，需要将欠款重置为0给前端
            if(arrearsAmount.compareTo(BigDecimal.ZERO) < 0)
            {
                dto.setArrearsAmount(BigDecimal.ZERO);
            }

            // 欠款总额
            BigDecimal arrearsTotal = dto.getArrearsTotal() == null ? BigDecimal.ZERO : dto.getArrearsTotal();
            // 还款总额
            BigDecimal repaymentTotal = dto.getRepaymentTotal() == null ? BigDecimal.ZERO : dto.getRepaymentTotal();

            dto.setArrearsTotal(arrearsTotal);
            dto.setRepaymentTotal(repaymentTotal);
        }

        return new PageInfo<>(memberChannelDTOS);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCreditMemberChannel(MemberChannelDTO memberChannelDTO, String operatorId) {
        log.info(" 授信支付 updateCreditMemberChannel {} ", JSON.toJSONString(memberChannelDTO));
        String memberChannelId = memberChannelDTO.getMemberChannelId();
        PaymentBillDTO paymentBillDTO = new PaymentBillDTO();
        if (CsStringUtils.isBlank(memberChannelId)) {
            log.error("memberChannelId为空！");
            throw new BizException(BasicCode.PARAM_NULL, "memberChannelId为空！");
        }
        String lock = null;
        try {
            lock = redisLockService.lockFast(memberChannelId);

            // 根据唯一ID查询该条授信记录
            MemberChannelDTO memberChannelDTO1 = memberChannelBiz.findByMemberChannelId(memberChannelId);



            if (memberChannelDTO1 == null) {
                log.error("根据memberChannelId没有找到对应记录！");
                throw new BizException(BasicCode.PARAM_NULL, "根据memberChannelId没有找到对应记录！");
            }

            AccountDTO account = accountService.findById(operatorId);
            if(account == null) {
                log.error("根据operatorId没有找到对应记录！");
                throw new BizException(BasicCode.PARAM_NULL, "根据operatorId没有找到对应记录！");
            }
            if(!account.getMemberId().equals(memberChannelDTO1.getPayeeMemberId())) {
                log.error("当前operator与渠道不匹配！");
                throw new BizException(BasicCode.PARAM_NULL, "当前operator与渠道不匹配！");
            }
            if (!memberChannelDTO.getMemberId().equals(memberChannelDTO1.getMemberId())
                    || !memberChannelDTO.getPayeeMemberId().equals(memberChannelDTO1.getPayeeMemberId())) {
                log.error("数据不匹配！");
                throw new BizException(BasicCode.INVALID_PARAM, "数据不匹配！");
            }

            BigDecimal repaymentAmount = memberChannelDTO.getRepaymentAmount();

            if (repaymentAmount != null && repaymentAmount.compareTo(BigDecimal.ZERO) > 0) {
                memberChannelDTO1.setArrearsAmount(memberChannelDTO1.getArrearsAmount().subtract(repaymentAmount).setScale(2, RoundingMode.HALF_UP));
                memberChannelDTO1.setRepaymentAmount(memberChannelDTO.getRepaymentAmount());

                // 已还款总额
                BigDecimal repaymentTotal = memberChannelDTO1.getRepaymentTotal() == null ? BigDecimal.ZERO : memberChannelDTO1.getRepaymentTotal();
                memberChannelDTO1.setRepaymentTotal(repaymentTotal.add(repaymentAmount));

                // 创建支付单
                paymentBillDTO = createPaymentBillDTO(memberChannelDTO1, operatorId);
            }
            memberChannelBiz.updateMemberChannel(memberChannelDTO1, operatorId);
            if (repaymentAmount != null && repaymentAmount.compareTo(BigDecimal.ZERO) > 0) {
                paymentBillBiz.updateStatus(paymentBillDTO.getPaymentBillId(), PaymentStatusEnum.PAY_SUCCESS.getCode(),
                        operatorId);
                paymentBillDTO.setPayerMemberCode(memberChannelDTO1.getMemberCode());
                log.info(" 记录支付流水 {} ", paymentBillDTO);
                billLogsBiz.logsRepayment(paymentBillDTO, operatorId, account.getAccountName());


                memberCreditRecordBiz.insertMemberCreditRecordByMemberChannel(memberChannelDTO1, repaymentAmount, MemberCreditRecordEnum.PAYBACK, OrderGenUtil.genId(), memberChannelDTO.getMemo(), memberChannelDTO.getFileAttach(), operatorId);
            }
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, e.getMessage());
        } finally {
            if (!CsStringUtils.isBlank(lock)) {
                redisLockService.unlock(memberChannelId, lock);
            }
        }
    }

    @Override
    public void batchAddCreditMemberChannel(List<CreditMemberChannelDTO> creditMemberChannelDTOS, String sellerId,
                                            String sellerCode, String sellerName, String operatorId) {
        log.info("batchAddCreditMemberChannel:{}",creditMemberChannelDTOS);
        //检查卖家是否开通了授信渠道，如没有开通则开通
        MemberChannelDTO sellerMemberChannelDTO = memberChannelBiz.findMemberChannelByPayee(sellerId,"credit",ChannelCodeEnum.CREDIT);
        if(sellerMemberChannelDTO == null){
            MemberChannelDTO sellerMemberChannelDTOReq = new MemberChannelDTO();
            ChannelConfigDTO channelConfigDTO = channelConfigBiz.findByChannelCode(ChannelCodeEnum.CREDIT.getCode());
            BeanUtils.copyProperties(channelConfigDTO, sellerMemberChannelDTOReq);
            sellerMemberChannelDTOReq.setMemberCode(sellerCode);
            sellerMemberChannelDTOReq.setMemberId(sellerId);
            sellerMemberChannelDTOReq.setMemberName(sellerName);
            sellerMemberChannelDTOReq.setAllowPayment(false);
            sellerMemberChannelDTOReq.setAllowReceive(true);
            sellerMemberChannelDTOReq.setPayeeMemberId(ChannelCodeEnum.CREDIT.getCode());
            sellerMemberChannelDTOReq.setMemberChannelStatus(MemberChannelStatusEnum.OPEND.getCode());
            sellerMemberChannelDTOReq.setMemberChannelCode(takeCodeGenerator.incrementChannelCode());
            memberChannelBiz.insertMemberChannel(sellerMemberChannelDTOReq,operatorId);
        }

        // DTO转化
        // 检查单个用户的记录时间是否有重叠
        List<MemberChannelDTO> memberChannelDTOS = new ArrayList<>();
        for (CreditMemberChannelDTO creditMemberChannelDTO : creditMemberChannelDTOS) {
            MemberChannelDTO memberChannelDTO = new MemberChannelDTO();
            BeanUtils.copyProperties(creditMemberChannelDTO, memberChannelDTO);
            ChannelConfigDTO channelConfigDTO = channelConfigBiz.findByChannelCode(ChannelCodeEnum.CREDIT.getCode());
            BeanUtils.copyProperties(channelConfigDTO, memberChannelDTO);
            memberChannelDTO.setExt4(sellerCode);
            memberChannelDTO.setPayeeMemberId(sellerId);
            memberChannelDTO.setMemberChannelStatus(MemberChannelStatusEnum.OPEND.getCode());
            memberChannelDTO.setMemberChannelCode(takeCodeGenerator.incrementChannelCode());
            memberChannelDTO.setAllowReceive(false);
            memberChannelDTO.setAllowPayment(true);
            memberChannelDTO.setExtSysName(sellerName);
            memberChannelDTOS.add(memberChannelDTO);
        }
        memberChannelBiz.batchAddCreditMemberChannel(memberChannelDTOS, operatorId);

    }

    private PaymentBillDTO createPaymentBillDTO(MemberChannelDTO memberChannelDTO, String operatorId) {
        PaymentBillDTO paymentBillDTO = new PaymentBillDTO();
        paymentBillDTO.setStatus(PaymentStatusEnum.NEW_PAY.getCode());
        paymentBillDTO.setBizName("授信还款");
        paymentBillDTO.setPayerMemberId(memberChannelDTO.getMemberId());
        paymentBillDTO.setPayerMemberName(memberChannelDTO.getMemberName());
        paymentBillDTO.setPayAmount(memberChannelDTO.getRepaymentAmount());
        paymentBillDTO.setActualPayAmount(memberChannelDTO.getRepaymentAmount());
        paymentBillDTO.setCurrency("CNY");
        paymentBillDTO.setChannelType(memberChannelDTO.getChannelType());
        paymentBillDTO.setChannelName(memberChannelDTO.getChannelName());
        paymentBillDTO.setChannelId(memberChannelDTO.getChannelId());
        paymentBillDTO.setChannelCode(memberChannelDTO.getChannelCode());

        paymentBillBiz.create(paymentBillDTO, operatorId);

        PaymentBillDetailDTO detailDTO = new PaymentBillDetailDTO();
        detailDTO.setPaymentBillNo(paymentBillDTO.getPaymentBillNo());
        detailDTO.setPayeeMemberId(memberChannelDTO.getPayeeMemberId());
        detailDTO.setPayeeMemberName(memberChannelDTO.getExt5());
        detailDTO.setPayAmount(memberChannelDTO.getRepaymentAmount());
        detailDTO.setActualPayAmount(memberChannelDTO.getRepaymentAmount());
        detailDTO.setStatus(PaymentStatusEnum.NEW_PAY.getCode());
        detailDTO.setChannelCode(memberChannelDTO.getChannelCode());
        detailDTO.setChannelName(memberChannelDTO.getChannelName());
        paymentBillDetailBiz.save(detailDTO, operatorId);
        paymentBillDTO.setDetailDTOList(Lists.newArrayList(detailDTO));
        return paymentBillDTO;
    }

    private void validateMemberChannelByOpen(ChannelConfigDTO channelConfigDTO, String channelPaymentType) {
        if (channelConfigDTO == null) {
            throw new BizException(PayCode.PARAM_NULL, "channelCode");
        }
        if (!channelConfigDTO.getChannelStatus()) {
            log.info("平台支付渠道被禁用");
            throw new BizException(PayCode.CHANNEL_DISABLE);
        }

        if (ChannelPaymentTypeEnum.PAYER.getCode().equals(channelPaymentType) && !channelConfigDTO.getNeedPayerReg()) {
            throw new BizException(PayCode.MEMBER_CHANNEL_OPEN_FAIL, "买方无需开通此支付渠道");
        }
        if (ChannelPaymentTypeEnum.PAYEE.getCode().equals(channelPaymentType) && !channelConfigDTO.getNeedPayeeReg()) {
            throw new BizException(PayCode.MEMBER_CHANNEL_OPEN_FAIL, "卖方无需开通此支付渠道");
        }
    }

    private void validateParamByMemberChannelDTO(MemberChannelDTO memberChannelDTO) {
        if (memberChannelDTO == null) {
            throw new BizException(PayCode.PARAM_NULL, "memberChannelDTO");
        }
        if (CsStringUtils.isEmpty(memberChannelDTO.getMemberId())) {
            throw new BizException(PayCode.PARAM_NULL, "memberId");
        }
        if (CsStringUtils.isEmpty(memberChannelDTO.getChannelCode())) {
            throw new BizException(PayCode.PARAM_NULL, "channelCode");
        }
    }

    @Override
    public List<MemberChannelDTO> getMemberAvailChannelsByCustomer(String memberId, ClientType client,
                                                                   ChannelPaymentTypeEnum channelPaymentType) {
        // 此处只要卖家收款渠道，可忽略聚合与微信的互斥
        List<MemberChannelDTO> channels = this.memberChannelBiz.getMemberAvailChannels(memberId, client);
        if (channelPaymentType == null) {
            return channels;
        }
        if (channels != null) {
            // fixme 构建一个线下收款渠道
            return channels.stream().filter(channel -> {
                if (channelPaymentType.getCode().equals(ChannelPaymentTypeEnum.PAYER.getCode())
                        && channel.getAllowPayment()) {
                    return true;
                }
                if (channelPaymentType.getCode().equals(ChannelPaymentTypeEnum.PAYEE.getCode())
                        && Boolean.TRUE.equals(channel.getAllowReceive())) {
                    return true;
                }
                return false;
            })
                    .sorted(Comparator.comparingInt(MemberChannelDTO::getChannelSequence))
                    .toList();
        }
        return null;
    }

    @Override
    public List<ChannelCardDTO> getChannelCardByMemberChannelId(String memberChannelId) {
        return channelCardBiz.getChannelCardByMemberChannelId(memberChannelId);
    }

    @Override
    public List<ChannelCardDTO> getChannelCardByMemberIdAndChannelCode(String memberId, String channelCode) {
        List<ChannelCardDTO> result = Lists.newArrayList();
        List<MemberChannelDTO> list = memberChannelBiz.findByMemberIdAndChannelCode2(memberId,channelCode);
        if( CollectionUtils.isEmpty(list) ){
            return result;
        }
        list.forEach(item->{
            result.addAll(channelCardBiz.getChannelCardByMemberChannelId(item.getMemberChannelId()));
        });
        return result;
    }

    @Override
    public void updateDefaultChannelCard(String memberChannelId, String channelCardId, String operatorId) {
        channelCardBiz.updateDefaultChannelCard(memberChannelId, channelCardId, operatorId);
    }

    @Override
    public ChannelCardDTO getChannelCardById(String channelCardId) {
        return channelCardBiz.getChannelCardById(channelCardId);
    }

    @Override
    public Map<String, Object> getMemberChannelInfoFromThird(String memberChannelId) {
        if (CsStringUtils.isEmpty(memberChannelId)) {
            throw new BizException(BasicCode.PARAM_NULL, "memberChannelId");
        }
        MemberChannelDTO memberChannelDTO = memberChannelBiz.findByMemberChannelId(memberChannelId);
        if (memberChannelDTO == null) {
            log.info("memberChannelId:{}",memberChannelId);
            throw new BizException(PayCode.DATA_NOT_FOUND, "memberChannelDTO");
        }
        return this.channelExecutor.getMemberChannelInfoFromThird(memberChannelDTO);
    }

    @Override
    public Map<String, Object> getMemberChannelInfoFromThirdByQueryDTO(BalanceQueryDTO balanceQueryDTO) {
        log.info("balanceQueryDTO:{}",balanceQueryDTO);
        if ((CsStringUtils.isEmpty(balanceQueryDTO.getMemberChannelId()) &&
                (CsStringUtils.isEmpty(balanceQueryDTO.getMemberMDMCode()) || !ChannelCodeEnum.ERP.getCode().equals(balanceQueryDTO.getChannelCode()))) &&
                (CsStringUtils.isEmpty(balanceQueryDTO.getMemberId()) ||
                        CsStringUtils.isEmpty(balanceQueryDTO.getPayeeMemberId()))) {
            throw new BizException(BasicCode.PARAM_NULL);
        }
        MemberChannelDTO memberChannelDTO;
        if (!CsStringUtils.isEmpty(balanceQueryDTO.getMemberChannelId())) {
            memberChannelDTO = memberChannelBiz.findByMemberChannelId(balanceQueryDTO.getMemberChannelId());
            if (memberChannelDTO == null) {
                log.info("balanceQueryDTO:{}",balanceQueryDTO);
                throw new BizException(PayCode.DATA_NOT_FOUND, "memberChannelDTO");
            }
            log.info("memberChannelDTO1:{}",memberChannelDTO);
        } else {
            memberChannelDTO = new MemberChannelDTO();
            memberChannelDTO.setExtSupAcctId(balanceQueryDTO.getMemberMDMCode());
            memberChannelDTO.setMemberId(balanceQueryDTO.getMemberId());
            memberChannelDTO.setPayeeMemberId(balanceQueryDTO.getPayeeMemberId());
            memberChannelDTO.setChannelCode(balanceQueryDTO.getChannelCode());
            memberChannelDTO.setAllowReceive(false);
            memberChannelDTO.setAllowPayment(true);
            log.info("memberChannelDTO2:{}",memberChannelDTO);
        }
        memberChannelDTO.setContractCode(balanceQueryDTO.getContractCode());
        memberChannelDTO.setSaleRegion(balanceQueryDTO.getSaleRegion());
        memberChannelDTO.setWarehouse(balanceQueryDTO.getWarehouse());
        memberChannelDTO.setPickupPointOrgId(balanceQueryDTO.getPickupPointOrgId());
        memberChannelDTO.setMemberMDMCode(balanceQueryDTO.getMemberMDMCode());
        return this.channelExecutor.getMemberChannelInfoFromThird(memberChannelDTO);
    }


    @Override
    public PasswordResponseDTO setPassword(PasswordRequestDTO passwordRequest) {
        return this.channelExecutor.doSetPassword(passwordRequest);
    }

    @Override
    public PasswordCallbackResponseDTO passwordCallback(PasswordCallbackRequestDTO passwordCallbackRequest) {
        return this.channelExecutor.doPasswordCallback(passwordCallbackRequest);
    }

    @Override
    public ChannelCardResponseDTO doAddChannelCard(ChannelCardRequestDTO request, String operator) {
        ChannelCardDTO channelCardDTO = request.getChannelCardDTO();
        Objects.requireNonNull(channelCardDTO, "银行卡信息");
        return this.channelExecutor.doAddChannelCard(request, operator);
    }

    @Override
    public SmallAmountTransferDTO findSmallAmountTransfer(SmallAmountTransferDTO smallAmountTransferDTO) {
        return this.channelExecutor.findSmallAmountTransfer(smallAmountTransferDTO);
    }

    @Override
    public ChannelCardResponseDTO verifyChannelCard(ChannelCardRequestDTO request, String operator) {
        return this.channelExecutor.doVerifyChannelCard(request, operator);
    }

    @Override
    public PasswordResponseDTO freePasswordSetting(String memberChannelId, Boolean freePassword, String operator) {
        memberChannelBiz.freePasswordSetting(memberChannelId, freePassword, operator);
        PasswordResponseDTO responseDTO = new PasswordResponseDTO();
        responseDTO.setSuccess(true);
        return responseDTO;
    }

    @Override
    public PasswordResponseDTO aggregationPaySetting(String memberChannelId, Boolean support, String operator) {
        memberChannelBiz.aggregationPaySetting(memberChannelId, support, operator);
        PasswordResponseDTO responseDTO = new PasswordResponseDTO();
        responseDTO.setSuccess(true);
        return responseDTO;
    }

    @Override
    public ChannelCardResponseDTO doRemoveChannelCard(String channelCardId, String memberId, String operator) {
        //删除最后一个线下收款账户时，弹窗提示“请先关闭线下收款方式”，关闭线下收款方式后才能成功删除最后一个线下收款账户
        ChannelCardDTO channelCardDTO = channelCardBiz.getChannelCardById(channelCardId);
        if( channelCardDTO != null ) {
            MemberChannelDTO memberChannelDTO = memberChannelBiz.findByMemberChannelId(channelCardDTO.getMemberChannelId());
            //如果是线下收款 且渠道未关闭 且当前删除的是最后一张银行卡
            if (ChannelCodeEnum.OFFLINE.getCode().equals(memberChannelDTO.getChannelCode())
                    && MemberChannelStatusEnum.OPEND.getCode().equalsIgnoreCase(memberChannelDTO.getMemberChannelStatus())
                    && channelCardBiz.getChannelCardCountByMemberChannelId2(channelCardDTO.getMemberChannelId()) < 2
                   ){
                throw new BizException(BasicCode.UNDEFINED_ERROR, "请先关闭线下收款方式");
            }
        }
        return this.channelExecutor.doRemoveChannelCard(channelCardId, memberId, operator);
    }

    @Override
    public List<MemberChannelDTO> findMemberChannelDTOList(String payeeMemberId,List<String> payerMemberIds) {
        return memberChannelBiz.findMemberChannelDTOList(payeeMemberId,payerMemberIds);
    }

    @Override
    public DownloadStatementDTO downloadStatement(String memberChannelId, long targetDate, String paymentType) {
        PaymentTypeEnum typeEnum = PaymentTypeEnum.getByCode(paymentType);
        if (typeEnum == null) {
            throw new BizException(BasicCode.PARAM_NULL, "paymentType");
        }
        return channelExecutor.doDownloadStatement(memberChannelId, new Date(targetDate), typeEnum);
    }

    @Override
    public MemberChannelCheckDTO checkMemberAllowUnBind(String memberId) {
        return channelExecutor.doCheckMemberAllowUnBind(memberId);
    }

    @Override
    public void unbindMemberWallet(String memberId) {
        List<MemberChannelDTO> memberChannelDTOS = memberChannelBiz.findByMemberIdAndChannelCode(memberId, ChannelCodeEnum.PINGANJZ.getCode());
        if (!CollectionUtils.isEmpty(memberChannelDTOS)) {
            channelExecutor.doCloseMemberChannel(memberChannelDTOS.get(0), memberId);
        }
        List<MemberChannelDTO> memberChannelDTOS2 = memberChannelBiz.findByMemberIdAndChannelCode(memberId, ChannelCodeEnum.PINGANJZPAY.getCode());
        if (!CollectionUtils.isEmpty(memberChannelDTOS2)) {
            channelExecutor.doCloseMemberChannel(memberChannelDTOS2.get(0), memberId);
        }
    }

    @Override
    public List<MemberChannelDTO> findByMemberIdAndChannelCode(String memberId, String channelCode) {
        return memberChannelBiz.findByMemberIdAndChannelCode(memberId, channelCode);
    }

    @Override
    public void checkPayeeMemberChannel(String sellerId, String sellerCode, String sellerName, String operatorId)
    {
        //检查卖家是否开通了授信渠道，如没有开通则开通
        MemberChannelDTO sellerMemberChannelDTO = memberChannelBiz.findMemberChannelByPayee(sellerId,"credit",ChannelCodeEnum.CREDIT);
        if(sellerMemberChannelDTO == null)
        {

            log.info("[{},{},{}]卖家未开通授信渠道，现在我们自动为他开通。", sellerId, sellerCode, sellerName);

            MemberChannelDTO sellerMemberChannelDTOReq = new MemberChannelDTO();
            ChannelConfigDTO channelConfigDTO = channelConfigBiz.findByChannelCode(ChannelCodeEnum.CREDIT.getCode());
            BeanUtils.copyProperties(channelConfigDTO, sellerMemberChannelDTOReq);
            sellerMemberChannelDTOReq.setMemberCode(sellerCode);
            sellerMemberChannelDTOReq.setMemberId(sellerId);
            sellerMemberChannelDTOReq.setMemberName(sellerName);
            sellerMemberChannelDTOReq.setAllowPayment(false);
            sellerMemberChannelDTOReq.setAllowReceive(true);
            sellerMemberChannelDTOReq.setPayeeMemberId(ChannelCodeEnum.CREDIT.getCode());
            sellerMemberChannelDTOReq.setMemberChannelStatus(MemberChannelStatusEnum.OPEND.getCode());
            sellerMemberChannelDTOReq.setMemberChannelCode(takeCodeGenerator.incrementChannelCode());
            memberChannelBiz.insertMemberChannel(sellerMemberChannelDTOReq,operatorId);
        }
    }

    @Override
    public boolean openGneteAggregationChannel(GneteAggregationChannelOpenDTO dto) {
        if (CsStringUtils.isBlank(dto.getMemberId())) {
            throw new BizException(BasicCode.CUSTOM_ERROR,"会员Id不可为空");
        }
        List<MemberChannelDTO> channelDTOList = findByMemberIdAndChannelCode(dto.getMemberId(), ChannelCodeEnum.GNETEPAY.getCode());
        if( CollectionUtils.isEmpty(channelDTOList)){
            throw new BizException(BasicCode.CUSTOM_ERROR,"需要先开通银联钱包");
        }
        MemberChannelDTO memberChannelDTO = channelDTOList.get(0);
        Date now = new Date();
        //开通聚合支付渠道
        List<String> channelCodeList = Lists.newArrayList(ChannelCodeEnum.GNETEPAYZFB.getCode(),ChannelCodeEnum.GNETEPAYWX.getCode(), ChannelCodeEnum.GNETEPAYC2B.getCode());
        for (String channelCode : channelCodeList) {
            //如果存在，则更新，否则新增
            List<MemberChannelDTO> aggChannelDTOList = memberChannelBiz.findByMemberIdAndChannelCode(dto.getMemberId(),channelCode,Lists.newArrayList());
            if(CollectionUtils.isEmpty(aggChannelDTOList)){
                MemberChannel memberChannel = new MemberChannel();
                BeanUtils.copyProperties(channelConfigBiz.findByChannelCode(channelCode),memberChannel);
                memberChannel.setMemberChannelStatus(MemberChannelStatusEnum.OPEND.getCode());
                memberChannel.setMemberChannelId(uuidGenerator.gain());
                memberChannel.setMemberChannelCode(takeCodeGenerator.incrementChannelCode());
                memberChannel.setDelFlg(false);
                memberChannel.setCreateTime(now);
                memberChannel.setCreateUser(dto.getOperator());
                memberChannel.setExtSupAcctId(memberChannelDTO.getExtSupAcctId());
                memberChannel.setExtCustAcctId(memberChannelDTO.getExtCustAcctId());
                memberChannel.setMemberName(memberChannelDTO.getMemberName());
                memberChannel.setMemberId(memberChannelDTO.getMemberId());
                memberChannel.setMemberCode(memberChannelDTO.getMemberCode());
                memberChannel.setReceiveChannelId(memberChannelDTO.getChannelId());
                memberChannelBiz.getMapper().insert(memberChannel);
            }else{
                MemberChannel memberChannel = new MemberChannel();
                memberChannel.setMemberChannelId(memberChannelDTO.getMemberChannelId());
                memberChannel.setMemberChannelStatus(MemberChannelStatusEnum.OPEND.getCode());
                memberChannel.setExtSupAcctId(memberChannelDTO.getExtSupAcctId());
                memberChannel.setExtCustAcctId(memberChannelDTO.getExtCustAcctId());
                memberChannel.setReceiveChannelId(memberChannelDTO.getChannelId());
                memberChannel.setUpdateTime(now);
                memberChannel.setUpdateUser(dto.getOperator());
                memberChannelBiz.getMapper().updateByPrimaryKeySelective(memberChannel);
            }
        }
        return false;
    }

    @Override
    public boolean closeGneteAggregationChannel(GneteAggregationChannelCloseDTO dto) {
        if (CsStringUtils.isBlank(dto.getMemberId())) {
            throw new BizException(BasicCode.CUSTOM_ERROR,"会员Id不可为空");
        }
        List<String> channelCodeList = Lists.newArrayList(ChannelCodeEnum.GNETEPAYZFB.getCode(),ChannelCodeEnum.GNETEPAYWX.getCode(), ChannelCodeEnum.GNETEPAYC2B.getCode());
        //关闭聚合支付渠道
        Condition condition = new Condition(MemberChannel.class);
        condition.createCriteria()
                .andEqualTo("memberId",dto.getMemberId())
                .andIn("channelCode",channelCodeList)
                .andEqualTo("delFlg",false);
        MemberChannel memberChannel = new MemberChannel();
        memberChannel.setUpdateUser(dto.getOperator());
        memberChannel.setUpdateTime(new Date());
        memberChannel.setMemberChannelStatus(MemberChannelStatusEnum.BE_STOP.getCode());
        memberChannelBiz.getMapper().updateByConditionSelective(memberChannel,condition);
        return true;
    }

    @Override
    public GneteWithholdGrantQueryDTO withholdGrant(GneteWithholdGrantDTO dto) {
        List<MemberChannelDTO> channelDTOList = findByMemberIdAndChannelCode(dto.getMemberId(), ChannelCodeEnum.GNETEPAY.getCode());
        if( CollectionUtils.isEmpty(channelDTOList)){
            throw new BizException(BasicCode.CUSTOM_ERROR,"需要先开通银联钱包");
        }
        MemberChannelDTO memberChannelDTO = channelDTOList.get(0);

        GneteWithholdGrantQueryDTO gneteWithholdGrantQueryDTO = new GneteWithholdGrantQueryDTO();
        gneteWithholdGrantQueryDTO.setMemberChannelId(memberChannelDTO.getMemberChannelId());
        gneteWithholdGrantQueryDTO.setMemberId(memberChannelDTO.getMemberId());
        gneteWithholdGrantQueryDTO.setGrantWalletId(memberChannelDTO.getExtSupAcctId());
        gneteWithholdGrantQueryDTO.setGrantWalletName(memberChannelDTO.getGneteWalletName());
        gneteWithholdGrantQueryDTO.setWalletId(memberChannelDTO.getExtCustAcctId());
        gneteWithholdGrantQueryDTO.setWalletName(memberChannelDTO.getGneteWalletName());
        if(dto.getOprtType() ==1 ){
            //签约
            WithholdGrantRequestDTO req = new WithholdGrantRequestDTO();
            req.setGrantWalletId(gneteAdapter.toLong(memberChannelDTO.getExtSupAcctId()));
            req.setWalletId(gneteAdapter.toLong(memberChannelDTO.getExtCustAcctId()));
            req.setTradeWayCode(dto.getTradeWayCode());
            req.setTradeWayFeilds(dto.getTradeWayFeilds());
            req.setOprtType(1);
            req.setGrantBeginDate(Integer.parseInt(dto.getGrantBeginDate()));
            req.setGrantEndDate(Integer.parseInt(dto.getGrantEndDate()));
            req.setProtocolName(dto.getProtocolName());
            req.setLimitAmt(gneteAdapter.toFen(dto.getLimitAmt()));
            req.setDayLimitAmt(gneteAdapter.toFen(dto.getDayLimitAmt()));
            req.setMonthLimitAmt(gneteAdapter.toFen(dto.getMonthLimitAmt()));
            req.setTotalLimitAmt(gneteAdapter.toFen(dto.getTotalLimitAmt()));
            String protocolNo = gneteAdapter.withholdGrant(req);
            gneteWithholdGrantQueryDTO.setGrantBeginDate(dto.getGrantBeginDate());
            gneteWithholdGrantQueryDTO.setGrantEndDate(dto.getGrantEndDate());
            gneteWithholdGrantQueryDTO.setProtocolNo(protocolNo);
            gneteWithholdGrantQueryDTO.setLimitAmt(dto.getLimitAmt());
            gneteWithholdGrantQueryDTO.setDayLimitAmt(dto.getDayLimitAmt());
            gneteWithholdGrantQueryDTO.setMonthLimitAmt(dto.getMonthLimitAmt());
            gneteWithholdGrantQueryDTO.setTotalLimitAmt(dto.getTotalLimitAmt());
            gneteWithholdGrantQueryDTO.setProtocolName(dto.getProtocolName());

            if (CsStringUtils.isBlank(protocolNo)) {
                throw new BizException(BasicCode.CUSTOM_ERROR,"签约失败，没有返回协议编号");
            }
            MemberChannel up = new MemberChannel();
            up.setMemberChannelId(memberChannelDTO.getMemberChannelId());
            //保存电子协议编号
            up.setGneteProtocolNo(protocolNo);
            if( dto.getPublicKeyByte() != null && dto.getPublicKeyByte().length > 0 ) {
                up.setGnetePublicCert(Hex.encodeHexString(dto.getPublicKeyByte()));
            }
            if( dto.getPrivateKeyByte() != null && dto.getPrivateKeyByte().length > 0 ) {
                up.setGnetePrivateCert(Hex.encodeHexString(dto.getPrivateKeyByte()));
            }
            if (CsStringUtils.isNotBlank(dto.getPrivateKeyPwd())) {
                up.setGnetePrivatePwd(Hex.encodeHexString(dto.getPrivateKeyPwd().getBytes(StandardCharsets.UTF_8)));
            }
            up.setUpdateTime(new Date());
            up.setUpdateUser(dto.getOperator());
            memberChannelBiz.getMapper().updateByPrimaryKeySelective(up);
        }else if(dto.getOprtType() == 2 ){
            if (CsStringUtils.isBlank(memberChannelDTO.getGneteProtocolNo())) {
                throw new BizException(BasicCode.CUSTOM_ERROR,"你还没有签约或者已经解约了");
            }
            //解约
            WithholdGrantRequestDTO req = new WithholdGrantRequestDTO();
            req.setGrantWalletId(gneteAdapter.toLong(memberChannelDTO.getExtSupAcctId()));
            req.setWalletId(gneteAdapter.toLong(memberChannelDTO.getExtCustAcctId()));
            req.setTradeWayCode(dto.getTradeWayCode());
            req.setTradeWayFeilds(dto.getTradeWayFeilds());
            req.setOprtType(2);
            req.setProtocolNo(memberChannelDTO.getGneteProtocolNo());
            gneteAdapter.withholdGrant(req);

            ((MemberChannelMapper)memberChannelBiz.getMapper()).
                    updateGneteNull(memberChannelDTO.getMemberChannelId(),dto.getOperator());

            return null;
        }else {
            if (CsStringUtils.isBlank(memberChannelDTO.getGneteProtocolNo())) {
                return gneteWithholdGrantQueryDTO;
            }
            //查询
            QueryWithholdGrantRequestDTO req = new QueryWithholdGrantRequestDTO();
            req.setGrantWalletIdList(Lists.newArrayList(Long.parseLong(memberChannelDTO.getExtSupAcctId())));
            req.setWalletIdList(Lists.newArrayList(Long.parseLong(memberChannelDTO.getExtCustAcctId())));
            req.setProtocolNo(memberChannelDTO.getGneteProtocolNo());
            req.setGrantStatus(1);//签约
            req.setPageType(0);//分页
            req.setPageNumber(1);
            req.setPageSize(10);
            QueryWithholdGrantResponseDTO grantList = gneteAdapter.queryWithholdGrant(req);
            if( grantList != null && !CollectionUtils.isEmpty(grantList.getList())){
                gneteWithholdGrantQueryDTO.setGrantBeginDate(grantList.getList().get(0).getGrantBeginDate()+"");
                gneteWithholdGrantQueryDTO.setGrantEndDate(grantList.getList().get(0).getGrantEndDate()+"");
                gneteWithholdGrantQueryDTO.setProtocolNo(grantList.getList().get(0).getProtocolNo());
                gneteWithholdGrantQueryDTO.setProtocolName(grantList.getList().get(0).getProtocolName());
                gneteWithholdGrantQueryDTO.setLimitAmt(gneteAdapter.toYuan(grantList.getList().get(0).getLimitAmt()));
                gneteWithholdGrantQueryDTO.setTotalLimitAmt(gneteAdapter.toYuan(grantList.getList().get(0).getTotalLimitAmt()));
            }
        }
        return gneteWithholdGrantQueryDTO;
    }

    @Override
    public ApplyTicketResDTO getTicket(ApplyTicketReqDTO dto) {
        log.info("getTicket: {}",dto);
        CsStringUtils.checkIsNull(dto.getJumpType(),"跳转类型");
        String key = "pay:memberChannelService:getTicket:"+dto.getMemberId() + ":"+ dto.getJumpType();
        String identifier = null;
        try {
            boolean enterprise = dto.getMemberType().startsWith(MemberDTO.ENTERPRISE_TYPE_PRE);
            log.info("getTicket 当前会员类型: {} {}", dto.getMemberType(), enterprise ? "企业" : "个人");
            identifier = redisLockService.lockFast(key);
            List<MemberChannelDTO> gneteChannelList = memberChannelBiz.findByMemberIdAndChannelCode(dto.getMemberId(), ChannelCodeEnum.GNETEPAY.getCode(),Lists.newArrayList());
            MemberChannelDTO memberChannelDTO = CollectionUtils.isEmpty(gneteChannelList) ? null : gneteChannelList.get(0);
            ApplyTicketRequestDTO request = new ApplyTicketRequestDTO();
            request.setJumpType(dto.getJumpType());
            request.setClientIp(dto.getClientIp());
            request.setExtUserId(dto.getMemberId());
            request.setWalletId(memberChannelDTO == null || CsStringUtils.isBlank(memberChannelDTO.getExtCustAcctId()) ? null : Long.valueOf(memberChannelDTO.getExtCustAcctId()));
            request.setExtData(dto.getExtData());
            request.setCallbackUrl(dto.getCallbackUrl());
            //判断是企业注册还是个人注册
            if (ApplyTicketJumpTypeEnum.TYPE_01.getCode() == dto.getJumpType() ||
                    ApplyTicketJumpTypeEnum.TYPE_10.getCode() == dto.getJumpType() ||
                    ApplyTicketJumpTypeEnum.TYPE_11.getCode() == dto.getJumpType()) {
                //如果是已开通 或者是已关闭
                if (memberChannelDTO != null && CsStringUtils.isNotBlank(memberChannelDTO.getExtCustAcctId())) {
                    if (MemberChannelStatusEnum.OPEND.getCode().equals(memberChannelDTO.getMemberChannelStatus())) {
                        if(MemberPlatform.PLATFORM_MEMBERID.getId().equals(memberChannelDTO.getMemberId())){
                            throw new BizException(BasicCode.CUSTOM_ERROR,"平台银联支付渠道不可关闭");
                        }
                        MemberChannel up = new MemberChannel();
                        up.setMemberChannelId(memberChannelDTO.getMemberChannelId());
                        up.setUpdateUser(dto.getOperator());
                        up.setUpdateTime(new Date());
                        up.setMemberChannelStatus(MemberChannelStatusEnum.BE_STOP.getCode());
                        memberChannelBiz.updateSelective(up);
                        //同步关闭聚合支付渠道
                        closeGneteAggregationChannel(new GneteAggregationChannelCloseDTO(memberChannelDTO.getMemberId(),dto.getOperator()));
                        return new ApplyTicketResDTO(null,"渠道已禁用");
                    } else if (MemberChannelStatusEnum.BE_STOP.getCode().equals(memberChannelDTO.getMemberChannelStatus())) {
                        //仅关闭渠道，不注销银联钱包，银联钱包只能注销个人的，不能注销企业的
                        MemberChannel up = new MemberChannel();
                        up.setMemberChannelId(memberChannelDTO.getMemberChannelId());
                        up.setUpdateUser(dto.getOperator());
                        up.setUpdateTime(new Date());
                        up.setMemberChannelStatus(MemberChannelStatusEnum.OPEND.getCode());
                        memberChannelBiz.updateSelective(up);
                        //如果是卖家 则同步开闭渠道 ChannelCodeEnum.GNETEPAYC2B
                        if( enterprise && BooleanUtils.isTrue(dto.getSellerFlg()) ) {
                            openGneteAggregationChannel(new GneteAggregationChannelOpenDTO(memberChannelDTO.getMemberId(),memberChannelDTO.getOpenUrl()));
                        }
                        return new ApplyTicketResDTO(null,"渠道已开启");
                        //个人注册成功后，再点注册，则查询钱包id
                    } else if (!enterprise && CsStringUtils.isNotBlank(memberChannelDTO.getGneteRegisterNo()) && MemberChannelStatusEnum.OPEN_ING.getCode().equals(memberChannelDTO.getMemberChannelStatus())) {
                        String walletId = gneteAdapter.queryWalletId(memberChannelDTO.getMemberId());
                        if (CsStringUtils.isNotBlank(walletId)) {
                            MemberChannel up = new MemberChannel();
                            up.setMemberChannelId(memberChannelDTO.getMemberChannelId());
                            up.setExtCustAcctId(walletId);
                            up.setMemberChannelStatus(MemberChannelStatusEnum.OPEND.getCode());
                            up.setGneteRegisterType(1);
                            if( !MemberPlatform.PLATFORM_MEMBERID.getId().equals(dto.getMemberId())) {
                                //查询平台钱包id
                                List<MemberChannelDTO> platformChannelList = memberChannelBiz.findByMemberIdAndChannelCode(MemberPlatform.PLATFORM_MEMBERID.getId(), ChannelCodeEnum.GNETEPAY.getCode());
                                if( CollectionUtils.isEmpty(platformChannelList)){
                                    throw new BizException(BasicCode.CUSTOM_ERROR,"平台银联钱包没有开通");
                                }
                                up.setExtSupAcctId(platformChannelList.get(0).getExtCustAcctId());//平台钱包id
                            }
                            memberChannelBiz.updateSelective(up);
                            return new ApplyTicketResDTO(null,"已注册");
                        }
                    }
                }
                request.setRegisterNo(memberChannelDTO != null && CsStringUtils.isNotBlank(memberChannelDTO.getGneteRegisterNo()) ? memberChannelDTO.getGneteRegisterNo() : null);
                if (ApplyTicketJumpTypeEnum.TYPE_01.getCode() == dto.getJumpType()) {
                    request.setJumpType(ApplyTicketJumpTypeEnum.TYPE_01.getCode() );
                    if (enterprise) {
                        boolean isApp = AppNames.WEB_SERVICE_BUYERAPP.getPlatform().equals(dto.getAppName()) ||
                                AppNames.WEB_SERVICE_SELLERAPP.getPlatform().equals(dto.getAppName()) ||
                                AppNames.WEB_SERVICE_CARRIERAPP.getPlatform().equals(dto.getAppName()) ||
                                AppNames.WEB_SERVICE_DRIVER.getPlatform().equals(dto.getAppName());
                        request.setJumpType(isApp ? ApplyTicketJumpTypeEnum.TYPE_11.getCode() : ApplyTicketJumpTypeEnum.TYPE_10.getCode());
                    }
                }else if( !enterprise ){
                    request.setJumpType(ApplyTicketJumpTypeEnum.TYPE_01.getCode() );
                }
            }else if( request.getWalletId() == null ){
                throw new BizException(BasicCode.CUSTOM_ERROR,"你还没有开通银联钱包");
            }
            ApplyTicketResponseDTO applyTicketResponseDTO = gneteAdapter.applyTicket(request);
            if( applyTicketResponseDTO != null && "00000".equals(applyTicketResponseDTO.getRspCode())) {
                if (ApplyTicketJumpTypeEnum.TYPE_01.getCode() == dto.getJumpType() ||
                        ApplyTicketJumpTypeEnum.TYPE_10.getCode() == dto.getJumpType() ||
                        ApplyTicketJumpTypeEnum.TYPE_11.getCode() == dto.getJumpType()) {
                    if (memberChannelDTO != null && CsStringUtils.isBlank(memberChannelDTO.getGneteRegisterNo())) {
                        MemberChannel up = new MemberChannel();
                        up.setMemberChannelId(memberChannelDTO.getMemberChannelId());
                        up.setGneteRegisterNo(applyTicketResponseDTO.getRegisterNo());
                        up.setGneteRegisterType(request.getJumpType());
                        up.setUpdateUser(dto.getOperator());
                        up.setUpdateTime(new Date());
                        up.setMemberChannelStatus(MemberChannelStatusEnum.OPEN_ING.getCode());
                        memberChannelBiz.updateSelective(up);
                    }
                    if (memberChannelDTO == null) {
                        ChannelConfigDTO sysChannel = channelConfigBiz.findByChannelCode(ChannelCodeEnum.GNETEPAY.getCode());
                        MemberChannel memberChannel = new MemberChannel();
                        BeanUtils.copyProperties(sysChannel, memberChannel);

                        memberChannel.setMemberChannelId(uuidGenerator.gain());
                        memberChannel.setMemberChannelCode(takeCodeGenerator.incrementChannelCode());
                        memberChannel.setMemberChannelStatus(MemberChannelStatusEnum.OPEN_ING.getCode());
                        memberChannel.setMemberId(dto.getMemberId());
                        memberChannel.setMemberCode(dto.getMemberCode());
                        memberChannel.setMemberName(dto.getMemberName());
                        if (!MemberPlatform.PLATFORM_MEMBERID.getId().equals(dto.getMemberId())) {
                            //查询平台钱包id
                            List<MemberChannelDTO> platformChannelList = memberChannelBiz.findByMemberIdAndChannelCode(MemberPlatform.PLATFORM_MEMBERID.getId(), ChannelCodeEnum.GNETEPAY.getCode());
                            if (CollectionUtils.isEmpty(platformChannelList)) {
                                throw new BizException(BasicCode.CUSTOM_ERROR, "平台银联钱包没有开通");
                            }
                            memberChannel.setExtSupAcctId(platformChannelList.get(0).getExtCustAcctId());//平台钱包id
                        }
                        memberChannel.setGneteRegisterNo(applyTicketResponseDTO.getRegisterNo());
                        memberChannel.setGneteRegisterType(request.getJumpType());
                        memberChannel.setDelFlg(false);
                        memberChannel.setCreateUser(dto.getOperator());
                        memberChannel.setCreateTime(new Date());
                        memberChannel.setUpdateTime(memberChannel.getCreateTime());
                        memberChannel.setUpdateUser(dto.getOperator());
                        memberChannelBiz.getMapper().insert(memberChannel);
                    }
                }
            }else{
                throw new BizException(BasicCode.CUSTOM_ERROR, "银联支付接口调用失败"+ (applyTicketResponseDTO  == null ? "!" : ": " + applyTicketResponseDTO.getRspResult()));
            }
            return new ApplyTicketResDTO(applyTicketResponseDTO.getJumpUrl(),null);
        }finally {
            redisLockService.unlock(key,identifier);
        }
    }

    @Override
    public void gneteRegisterCallBack(RegisterCallBackDTO dto) {
        log.info("gneteRegisterCallBack: {}",JSON.toJSONString(dto));
        MemberChannel memberChannel = memberChannelBiz.findByRegisterNo(dto.getRegisterNo());
        if( memberChannel == null ){
            log.error("渠道信息不存在gneteRegisterCallBack dto:{}",JSON.toJSONString(dto));
            return;
        }
        MemberChannel up = new MemberChannel();
        if (CsStringUtils.isBlank(memberChannel.getMemberName())) {
            MemberSimpleDTO memberSimpleDTO = memberService.findMemberSimpleById(memberChannel.getMemberId());
            memberChannel.setMemberName(memberSimpleDTO.getMemberName());
            up.setMemberName(memberSimpleDTO.getMemberName());
            up.setMemberCode(memberSimpleDTO.getMemberCode());
        }
        if (CsStringUtils.equals(dto.getCompanyName(), memberChannel.getMemberName())) {
            log.warn("钱包注册的企业名称与会员名称不一致 memberId:{} memberName:{},companyName:{}",
                    memberChannel.getMemberId(),memberChannel.getMemberName(),dto.getCompanyName());
        }

        up.setMemberChannelId(memberChannel.getMemberChannelId());
        up.setExtCustAcctId(dto.getWalletId());
        up.setGneteRegisterStatus(dto.getRegStatus());
        up.setGneteWalletName(dto.getWalletName());
        up.setGneteRegisterTime(new Date());
        if( "10".equals(dto.getRegStatus()) ){
            up.setMemberChannelStatus(MemberChannelStatusEnum.OPEND.getCode());
            memberChannelBiz.updateSelective(up);
            //平台或者卖家 钱包注册完成后自动开通聚合支付渠道  仅用于收款，其实是收款到平台钱包，最终再转账对对应的收款方
            if( MemberPlatform.PLATFORM_MEMBERID.getId().equals(memberChannel.getMemberId())){
                openGneteAggregationChannel(new GneteAggregationChannelOpenDTO(memberChannel.getMemberId(),memberChannel.getCreateUser()));
            }else{
                MemberSimpleDTO member = memberService.findMemberSimpleById(memberChannel.getMemberId());
                if( member != null && member.getSellerFlg() != null && member.getSellerFlg() == 1 ){
                    openGneteAggregationChannel(new GneteAggregationChannelOpenDTO(memberChannel.getMemberId(),memberChannel.getCreateUser()));
                }
            }
        }else {
            memberChannelBiz.updateSelective(up);
        }
    }

    @Override
    public String getPlugRandomKey() {
        return gneteAdapter.getPlugRandomKey();
    }

    @Override
    public Boolean uploadGnetePrivateCert(String memberChannelId, String certPwd,String operator,String cert) {
        if (CsStringUtils.isBlank(memberChannelId)) {
            throw new BizException(BasicCode.CUSTOM_ERROR,"渠道Id不可为空");
        }
        if (CsStringUtils.isBlank(certPwd)) {
            throw new BizException(BasicCode.CUSTOM_ERROR,"证书密码不可为空");
        }
        if (CsStringUtils.isBlank(cert)) {
            throw new BizException(BasicCode.CUSTOM_ERROR,"证书不可为空");
        }
        try {
            if( RSAUtil.readPrivate(Hex.decodeHex(cert.toCharArray()), Hex.decodeHex(certPwd.toCharArray())) == null ){
                throw new BizException(BasicCode.CUSTOM_ERROR,"证书或证书密码不正确");
            }
        }catch (DecoderException e){
            throw new BizException(BasicCode.CUSTOM_ERROR,"证书或证书密码解码不正确");
        }
        MemberChannel memberChannel = memberChannelBiz.get(memberChannelId);
        if(memberChannel == null || BooleanUtils.isTrue(memberChannel.getDelFlg()) ){
            throw new BizException(BasicCode.CUSTOM_ERROR,"会员渠道不存在或已删除");
        }

        MemberChannel up = new MemberChannel();
        up.setMemberChannelId(memberChannelId);
        up.setGnetePrivateCert(cert);
        up.setGnetePrivatePwd(certPwd);
        up.setUpdateTime(new Date());
        up.setUpdateUser(operator);
        memberChannelBiz.getMapper().updateByPrimaryKeySelective(up);
        return true;
    }

    @Override
    public FrontConfigDTO getConfig() {
        ConfigDTO config = gneteAdapter.getConfig();
        FrontConfigDTO frontConfigDTO = new FrontConfigDTO();
        BeanUtils.copyProperties(config,frontConfigDTO);
        return frontConfigDTO;
    }
}
