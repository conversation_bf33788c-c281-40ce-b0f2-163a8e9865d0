package com.ecommerce.pay.v2.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Sets;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.exception.DistributeLockException;
import com.ecommerce.common.service.RedisService;
import com.ecommerce.common.service.common.BaseBiz;
import com.ecommerce.common.service.lock.RedisLockService;
import com.ecommerce.common.utils.ArithUtils;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.common.utils.DateUtil;
import com.ecommerce.member.api.dto.member.enums.MemberPlatform;
import com.ecommerce.open.api.dto.gnete.QueryBatchTransResultResponseDTO;
import com.ecommerce.open.api.dto.gnete.QueryBatchTransResultResponseItemDTO;
import com.ecommerce.open.api.dto.pinganjz.ApplicationTextMsgDynamicCodeResponseDTO;
import com.ecommerce.pay.api.exception.PayCode;
import com.ecommerce.pay.api.v2.dto.AutoPaymentRequestDTO;
import com.ecommerce.pay.api.v2.dto.AutoPaymentResponseDTO;
import com.ecommerce.pay.api.v2.dto.BizConfigDTO;
import com.ecommerce.pay.api.v2.dto.ChannelConfigDTO;
import com.ecommerce.pay.api.v2.dto.CodeAndMessage;
import com.ecommerce.pay.api.v2.dto.ConfirmPayRequestDTO;
import com.ecommerce.pay.api.v2.dto.DirectPayRequestDTO;
import com.ecommerce.pay.api.v2.dto.DirectPayResponseDTO;
import com.ecommerce.pay.api.v2.dto.KeyValueDTO;
import com.ecommerce.pay.api.v2.dto.MemberChannelDTO;
import com.ecommerce.pay.api.v2.dto.MemberItemDTO;
import com.ecommerce.pay.api.v2.dto.PaymentBillInfo;
import com.ecommerce.pay.api.v2.dto.PaymentCallbackRequestDTO;
import com.ecommerce.pay.api.v2.dto.PaymentCallbackResponseDTO;
import com.ecommerce.pay.api.v2.dto.PaymentNotifyDTO;
import com.ecommerce.pay.api.v2.dto.PaymentRequestDTO;
import com.ecommerce.pay.api.v2.dto.PaymentRequestWrapDTO;
import com.ecommerce.pay.api.v2.dto.PaymentRequirementDTO;
import com.ecommerce.pay.api.v2.dto.PaymentRequirementDetailDTO;
import com.ecommerce.pay.api.v2.dto.PaymentResponseDTO;
import com.ecommerce.pay.api.v2.dto.RefundDTO;
import com.ecommerce.pay.api.v2.dto.RefundRequestDTO;
import com.ecommerce.pay.api.v2.dto.SendVerificationCodeRequestDTO;
import com.ecommerce.pay.api.v2.dto.SendVerificationCodeResponseDTO;
import com.ecommerce.pay.api.v2.dto.SplitPayCompleteRequestDTO;
import com.ecommerce.pay.api.v2.dto.SplitPayCompleteResponseDTO;
import com.ecommerce.pay.api.v2.dto.bill.PaymentBillDTO;
import com.ecommerce.pay.api.v2.dto.bill.PaymentBillDetailDTO;
import com.ecommerce.pay.api.v2.dto.bill.RefundBillDTO;
import com.ecommerce.pay.api.v2.dto.bill.RefundBillDetailDTO;
import com.ecommerce.pay.api.v2.dto.bill.SplitPayBillDTO;
import com.ecommerce.pay.api.v2.dto.bill.SplitPayBillDetailDTO;
import com.ecommerce.pay.api.v2.dto.query.OrderPaymentDetailDTO;
import com.ecommerce.pay.api.v2.dto.query.PaymentBillQueryDTO;
import com.ecommerce.pay.api.v2.dto.redis.key.PayRedisKey;
import com.ecommerce.pay.api.v2.enums.ChannelCodeEnum;
import com.ecommerce.pay.api.v2.enums.ChannelPaymentTypeEnum;
import com.ecommerce.pay.api.v2.enums.ChannelTypeEnum;
import com.ecommerce.pay.api.v2.enums.ClientType;
import com.ecommerce.pay.api.v2.enums.OperatorStatusEnum;
import com.ecommerce.pay.api.v2.enums.PaymentCallbackEnum;
import com.ecommerce.pay.api.v2.enums.PaymentStatusEnum;
import com.ecommerce.pay.api.v2.enums.PaymentTypeEnum;
import com.ecommerce.pay.api.v2.enums.PaymentWarningCodeEnum;
import com.ecommerce.pay.enums.PayDetailTypeEnum;
import com.ecommerce.pay.v2.biz.IBillLogsBiz;
import com.ecommerce.pay.v2.biz.IChannelConfigBiz;
import com.ecommerce.pay.v2.biz.IDriverCarriageItemBiz;
import com.ecommerce.pay.v2.biz.IDriverDayCarriageBiz;
import com.ecommerce.pay.v2.biz.IDriverPayInfoBiz;
import com.ecommerce.pay.v2.biz.IDriverSummaryInfoBiz;
import com.ecommerce.pay.v2.biz.IMemberChannelBiz;
import com.ecommerce.pay.v2.biz.IOperationRecordBiz;
import com.ecommerce.pay.v2.biz.IPaymentBillBiz;
import com.ecommerce.pay.v2.biz.IPaymentBillDetailBiz;
import com.ecommerce.pay.v2.biz.IPaymentIntegrationBiz;
import com.ecommerce.pay.v2.biz.IPlatformDayCarriageBiz;
import com.ecommerce.pay.v2.biz.IRefundBillBiz;
import com.ecommerce.pay.v2.biz.IRefundBillDetailBiz;
import com.ecommerce.pay.v2.biz.ISplitPayBillBiz;
import com.ecommerce.pay.v2.biz.ISplitPayBillDetailBiz;
import com.ecommerce.pay.v2.channel.adapter.gnete.GneteAdapter;
import com.ecommerce.pay.v2.dao.mapper.DriverCarriageItemMapper;
import com.ecommerce.pay.v2.dao.vo.DriverDayCarriage;
import com.ecommerce.pay.v2.dao.vo.DriverPayInfo;
import com.ecommerce.pay.v2.dao.vo.DriverSummaryInfo;
import com.ecommerce.pay.v2.dao.vo.OperationRecord;
import com.ecommerce.pay.v2.dao.vo.PlatformDayCarriage;
import com.ecommerce.pay.v2.executor.IChannelExecutor;
import com.ecommerce.pay.v2.service.IPaymentService;
import com.ecommerce.pay.v2.service.IWalletAccountService;
import com.ecommerce.pay.v2.util.SecretService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.StringJoiner;
import java.util.stream.Collectors;


@Slf4j
@Service
public class PaymentService implements IPaymentService {

    @Autowired
    private IPaymentBillBiz paymentBillBiz;

    @Autowired
    private IRefundBillBiz refundBillBiz;

    @Autowired
    private IRefundBillDetailBiz refundBillDetailBiz;

    @Autowired
    private IPaymentIntegrationBiz paymentIntegrationBiz;

    @Autowired
    private IChannelConfigBiz channelConfigBiz;

    @Autowired
    private IChannelExecutor channelExecutor;

    @Autowired
    private IMemberChannelBiz memberChannelBiz;

    @Autowired
    private RedisLockService redisLockService;

    @Autowired
    private SecretService secretService;

    @Autowired
    private ISplitPayBillBiz splitPayBillBiz;

    @Autowired
    private ISplitPayBillDetailBiz splitPayBillDetailBiz;

    @Autowired
    private IPaymentBillDetailBiz paymentBillDetailBiz;

    @Autowired
    private IBillLogsBiz billLogsBiz;

    @Autowired
    @Qualifier("bizRedisService")
    private RedisService redisService;

    @Autowired
    private IOperationRecordBiz operationRecordBiz;
    @Lazy
    @Autowired
    private IWalletAccountService walletAccountService;

    @Autowired
    private IPlatformDayCarriageBiz platformDayCarriageBiz;
    @Autowired
    private IDriverPayInfoBiz driverPayInfoBiz;
    @Autowired
    private IDriverSummaryInfoBiz driverSummaryInfoBiz;
    @Resource
    private DriverCarriageItemMapper driverCarriageItemMapper;
    @Autowired
    private IDriverDayCarriageBiz driverDayCarriageBiz;
    @Autowired
    private IDriverCarriageItemBiz driverCarriageItemBiz;

    @Override
    public void doCancelPaymentBiz(String bizId, String operator) {
        String lock = null;
        try {
            lock = redisLockService.lockFast(bizId);
            log.info("取消支付 bizId: {}", bizId);

            PaymentBillDTO paymentBillDTO = paymentBillBiz.findRecentRecordBySrcBizNo(bizId);
            if (paymentBillDTO == null) {
                return;
            }
            String status = paymentBillDTO.getStatus();
            boolean doCancel = false;
            if (PaymentStatusEnum.NEW_PAY.getCode().equals(status)) {
                // todo 锁机制失败时
                return;
            } else if (PaymentStatusEnum.PAY_ING.getCode().equals(status)) {
                paymentBillDTO = channelExecutor.searchPaymentBill(paymentBillDTO.getPaymentBillNo(), paymentBillDTO.getChannelCode(), operator);
                doCancel = true;
            } else if (PaymentStatusEnum.PAY_FAIL.getCode().equals(status)) {
                doCancel = true;
            } else if (PaymentStatusEnum.PAY_TIMEOUT.getCode().equals(status)) {
                doCancel = true;
            } else if (PaymentStatusEnum.PAY_FREEZE.getCode().equals(status)) {
                doCancel = true;
            } else if (PaymentStatusEnum.PAY_CANCEL.getCode().equals(status)) {
                return;
            } else if (PaymentStatusEnum.PAY_SUCCESS.getCode().equals(status)) {
                throw new BizException(PayCode.PAY_CAN_NOT_CLOSE);
            } else if (PaymentStatusEnum.PAY_CLOSE.getCode().equals(status)) {
                return;
            }
            if (doCancel) {
                PaymentResponseDTO responseDTO = channelExecutor.doCancelPayment(paymentBillDTO, operator);
                log.info("关闭支付单: {}", responseDTO);
                if (!responseDTO.isChangeStatus()) {
                    paymentBillBiz.updateStatus(paymentBillDTO.getPaymentBillId(), PaymentStatusEnum.PAY_CANCEL.getCode(),
                            operator);
                }
            }
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new BizException(PayCode.PAY_FAIL);
        } finally {
            if (!CsStringUtils.isEmpty(lock)) {
                redisLockService.unlock(bizId, lock);
            }
        }
    }

    @Override
    public PaymentResponseDTO prePay(String bizId, String payerChannelId, String amount, String operator) {
        return null;
    }

    private static String checkString(String str, String msg) {
        if (str == null || CsStringUtils.isEmpty(str.trim())) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, msg);
        }
        return str;
    }

    @Override
    public PaymentResponseDTO confirmPay(ConfirmPayRequestDTO confirmPayRequestDTO) {
        log.info("--->> 确认支付 <<---confirmPayRequestDTO:{}", confirmPayRequestDTO);
        if (CsStringUtils.isEmpty(confirmPayRequestDTO.getRequestId())) {
            throw new BizException(BasicCode.PARAM_NULL, "requestId");
        }
        if (CsStringUtils.isEmpty(confirmPayRequestDTO.getOrderNo())) {
            throw new BizException(BasicCode.PARAM_NULL, "orderNo");
        }
        List<PaymentRequirementDetailDTO> requireDtoList = confirmPayRequestDTO.getPaymentBillDetailDTOList();
        if (CollectionUtils.isEmpty(requireDtoList)) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "分账列表 [paymentBillDetailDTOList] 不可为空");
        }
        // 对入参分账列表根据收款方去重--begin--
        Map<String, List<PaymentRequirementDetailDTO>> map = requireDtoList.stream().collect(Collectors.groupingBy(PaymentRequirementDetailDTO::getPayeeMemberId));
        requireDtoList.clear();
        for (Map.Entry<String, List<PaymentRequirementDetailDTO>> entry : map.entrySet()) {
            PaymentRequirementDetailDTO dto = entry.getValue().get(0);
            BigDecimal reduce = entry.getValue().stream()
                    .map(PaymentRequirementDetailDTO::getPayAmount)
                    .map(i -> CsStringUtils.isEmpty(i) ? "0" : i)
                    .map(BigDecimal::new)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            String subject = entry.getValue().stream().map(PaymentRequirementDetailDTO::getSubject).filter(i -> !CsStringUtils.isEmpty(i)).collect(Collectors.joining(","));
            dto.setPayAmount(reduce.toPlainString());
            dto.setSubject(subject);
            requireDtoList.add(dto);
        }
        // 去重--end--
        String lock = null;
        String splitPayBillId;
        PaymentResponseDTO responseDTO = new PaymentResponseDTO();
        boolean existHistory = false;
        try {
            lock = redisLockService.lockFast(confirmPayRequestDTO.getOrderNo());
            log.info("requestId: {}", confirmPayRequestDTO.getRequestId());
            // 判断该 requestId请求号 是否存在
            OperationRecord or = new OperationRecord();
            or.setOperationNo(confirmPayRequestDTO.getRequestId());
            List<OperationRecord> list = operationRecordBiz.find(or);
            if (!CollectionUtils.isEmpty(list)) {
                log.info("该requestId: {}, 存在操作记录", confirmPayRequestDTO.getRequestId());
                existHistory = true;
                // 返回原结果
                return JSON.parseObject(list.get(0).getContent3(), PaymentResponseDTO.class);
            }
            log.info("该requestId: {}, 不存在操作记录", confirmPayRequestDTO.getRequestId());
            responseDTO = new PaymentResponseDTO();

            // 查询该订单的支付详情(已支付金额、已退款金额、已分账金额、每个收款方的收款详细)
            OrderPaymentDetailDTO orderPaymentDetailDTO = queryPaymentByOrderNo(confirmPayRequestDTO.getOrderNo());

            log.info("校验金额是否超过最大值. db: {}", orderPaymentDetailDTO);
            List<MemberItemDTO> detailDTOList = orderPaymentDetailDTO.getMemberItemDTOListList();
            //如果是只校验总金额
            if (BooleanUtils.isTrue(confirmPayRequestDTO.getOnlyCheckTotalAmount())) {
                //船运调价后收款人收款金额有变化,无法按此校验,只能校验总金额
                Set<String> payeeMemberIds = detailDTOList.stream().map(MemberItemDTO::getMemberId).filter(Objects::nonNull).collect(Collectors.toSet());
                BigDecimal splitAmount = BigDecimal.ZERO;// 本次分支总金额
                for (PaymentRequirementDetailDTO dto : requireDtoList) {
                    if (!payeeMemberIds.contains(dto.getPayeeMemberId())) {
                        throw new BizException(BasicCode.UNDEFINED_ERROR, "未知的收款方:" + dto.getPayeeMemberId());
                    }
                    BigDecimal decimal = new BigDecimal(checkString(dto.getPayAmount(), "分账金额不可为空"));
                    checkDecimalNumberAndFormat(decimal);// 检查数字精度
                    splitAmount = ArithUtils.add(splitAmount, decimal);
                }
                //当前剩余可分账金额
                BigDecimal leftAmount = ArithUtils.subtract(true, orderPaymentDetailDTO.getPayerPayTotalAmount(), orderPaymentDetailDTO.getRefundAmount(), orderPaymentDetailDTO.getExpenseAmount());
                if (leftAmount.compareTo(splitAmount) < 0) {
                    log.info(">>> 金额不足, 本次分账总额: {},orderPaymentDetailDTO:{}", splitAmount, JSON.toJSON(orderPaymentDetailDTO));
                    throw new BizException(BasicCode.UNDEFINED_ERROR, "金额不足, 无法支付");
                }
            } else {
                for (PaymentRequirementDetailDTO dto : requireDtoList) {
                    boolean exist = false;
                    for (MemberItemDTO detailDTO : detailDTOList) {
                        if (detailDTO.getMemberId().equals(dto.getPayeeMemberId())) {
                            exist = true;
                            BigDecimal decimal = new BigDecimal(checkString(dto.getPayAmount(), "分账金额不可为空"));
                            // 检查数字精度
                            checkDecimalNumberAndFormat(decimal);
                            BigDecimal add = detailDTO.getActualPayAmount().add(decimal);
                            if (add.compareTo(detailDTO.getPayAmount().subtract(detailDTO.getRefundAmount())) > 0) {
                                log.info(">>> 金额不足, 无法支付 卖家: {}, 应收: {}, 已收: {}, 已退: {} 本次: {}",
                                        detailDTO.getMemberId(), detailDTO.getPayAmount(), detailDTO.getActualPayAmount(),
                                        detailDTO.getRefundAmount(), dto.getPayAmount());
                                throw new BizException(BasicCode.UNDEFINED_ERROR, "金额不足, 无法支付");
                            }
                        }
                    }
                    if (!exist) {
                        throw new BizException(BasicCode.UNDEFINED_ERROR, "未知的收款方:" + dto.getPayeeMemberId());
                    }
                }
            }

            List<PaymentBillDTO> paymentBillDTOList = orderPaymentDetailDTO.getPaymentBillDTOList();
            PaymentBillDTO firstBillDTO = paymentBillDTOList.get(0);

            // 确认本次分账应从哪个渠道支付
            Map<String, List<MemberItemDTO>> paymentBillMap = orderPaymentDetailDTO.getMemberItemChannelMap();
            for (Map.Entry<String, List<MemberItemDTO>> entry : paymentBillMap.entrySet()) {
                // 渠道
                String channelCode = entry.getKey();
                // 当前渠道所有卖家可分账金额
                List<MemberItemDTO> dtoList = entry.getValue();
                boolean goOn = true;
                for (MemberItemDTO dto : dtoList) {
                    // 支付金额 [是否等于] (已分账金额 + 已退款金额)
                    goOn = dto.getPayAmount().compareTo(dto.getActualPayAmount().add(dto.getRefundAmount())) == 0 && goOn;
                }
                if (goOn) {
                    // 当前渠道已分账完
                    continue;
                }
                // 从本渠道所有卖家本次应分账金额
                Set<String> memberIdSet = Sets.newHashSet();
                List<SplitPayBillDetailDTO> tempDtoList = dtoList.stream()
                        .filter(item -> CsStringUtils.isNotBlank(item.getMemberId())).filter(item -> !memberIdSet.contains(item.getMemberId())).map(item -> {
                            SplitPayBillDetailDTO t = new SplitPayBillDetailDTO();
                            t.setPayeeMemberId(item.getMemberId());
                            t.setPayeeMemberName(item.getMemberName());
                            t.setPayAmount(BigDecimal.ZERO);
                            t.setActualPayAmount(BigDecimal.ZERO);
                            //收款方剔除重复
                            memberIdSet.add(item.getMemberId());
                            return t;
                        }).toList();

                boolean doNotSplit = true;
                for (PaymentRequirementDetailDTO requirement : requireDtoList) {
                    // 若金额为0 不分帐
                    doNotSplit = new BigDecimal(requirement.getPayAmount()).compareTo(BigDecimal.ZERO) == 0 && doNotSplit;

                    for (MemberItemDTO detailDTO : dtoList) {
                        if (requirement.getPayeeMemberId().equals(detailDTO.getMemberId())) {
                            // 可分账金额
                            BigDecimal available = detailDTO.getPayAmount().subtract(detailDTO.getActualPayAmount())
                                    .subtract(detailDTO.getRefundAmount());
                            // 本次请求分账金额
                            BigDecimal current = new BigDecimal(requirement.getPayAmount());
                            if (current.compareTo(BigDecimal.ZERO) == 0) {
                                continue;
                            }
                            for (SplitPayBillDetailDTO dto : tempDtoList) {
                                if (dto.getPayeeMemberId().equals(detailDTO.getMemberId())) {
                                    if (available.compareTo(current) >= 0) {
                                        requirement.setPayAmount("0");
                                        //收款方剔除重复 收款金额合并
                                        dto.setPayAmount(ArithUtils.add(current, dto.getPayAmount()));
                                    } else {
                                        requirement.setPayAmount(current.subtract(available).toPlainString());
                                        //收款方剔除重复 收款金额合并
                                        dto.setPayAmount(ArithUtils.add(available, dto.getPayAmount()));
                                    }
                                }
                            }
                        }
                    }
                }

                if (!doNotSplit) {
                    tempDtoList = tempDtoList.stream().filter(item -> item.getPayAmount().compareTo(BigDecimal.ZERO) > 0).toList();
                    if (CollectionUtils.isEmpty(tempDtoList)) {
                        continue;
                    }
                    log.info("渠道: {}, 分账: {}", channelCode, tempDtoList);
                    ChannelConfigDTO channelConfigDTO = channelConfigBiz.findByChannelCode(channelCode);
                    BigDecimal amount = BigDecimal.ZERO;
                    for (SplitPayBillDetailDTO dto : tempDtoList) {
                        amount = amount.add(dto.getPayAmount());
                        for (PaymentRequirementDetailDTO detailDTO : requireDtoList) {
                            if (dto.getPayeeMemberId().equals(detailDTO.getPayeeMemberId())) {
                                dto.setSubject(detailDTO.getSubject());
                            }
                        }
                    }
                    SplitPayBillDTO splitPayBillDTO = new SplitPayBillDTO();

                    splitPayBillDTO.setOrderId(firstBillDTO.getOrderId());
                    splitPayBillDTO.setOrderNo(confirmPayRequestDTO.getOrderNo());
                    splitPayBillDTO.setPayerMemberId(firstBillDTO.getPayerMemberId());
                    splitPayBillDTO.setPayerMemberName(firstBillDTO.getPayerMemberName());
                    splitPayBillDTO.setPayerMemberCode(firstBillDTO.getPayerMemberCode());
                    splitPayBillDTO.setPayAmount(amount);
                    splitPayBillDTO.setChannelCode(channelCode);
                    splitPayBillDTO.setChannelId(channelConfigDTO.getChannelId());
                    splitPayBillDTO.setChannelName(channelConfigDTO.getChannelName());
                    splitPayBillDTO.setChannelType(channelConfigDTO.getChannelType());
                    splitPayBillDTO.setStatus(PaymentStatusEnum.PAY_ING.getCode());
                    splitPayBillDTO.setRemarks(confirmPayRequestDTO.getWaybillNum());
                    splitPayBillDTO.setOrgCode(confirmPayRequestDTO.getOrgCode());
                    splitPayBillDTO.setBillSplitInfoList(confirmPayRequestDTO.getBillSplitInfoList());
                    createSplitBillDetail(splitPayBillDTO, tempDtoList);
                    splitPayBillBiz.create(splitPayBillDTO, "System_" + PaymentTypeEnum.CONFIRM_PAY.getCode());
                    splitPayBillId = splitPayBillDTO.getSplitPayBillId();

                    try {
                        PaymentResponseDTO res = channelExecutor.doConfirmPay(splitPayBillDTO, confirmPayRequestDTO.getOperatorId());
                        if (res.isSuccess() && !ChannelCodeEnum.GNETEPAY.getCode().equals(channelCode)) {//银联支付分账为异步操作
                            splitPayBillBiz.updateStatus(splitPayBillId, PaymentStatusEnum.PAY_SUCCESS.getCode(),
                                    confirmPayRequestDTO.getOperatorId());
                        }
                    } catch (Exception e) {
                        log.info("渠道: {}, 分账失败 {}", channelCode, e.getMessage());
                        splitPayBillBiz.updateStatus(splitPayBillId, PaymentStatusEnum.PAY_FAIL.getCode(),
                                confirmPayRequestDTO.getOperatorId());
                        throw e;
                    }
                }
            }
            responseDTO.setSuccess(true);
            return responseDTO;
        } catch (DistributeLockException e) {
            // 获取锁失败
            responseDTO.setSuccess(false);
            responseDTO.setContent("您的操作太频繁，请稍后再试");
            throw new BizException(BasicCode.UNDEFINED_ERROR, "您的操作太频繁，请稍后再试");
        } catch (BizException e) {
            responseDTO.setSuccess(false);
            responseDTO.setContent(e.getMessage());
            throw e;
        } catch (Exception e) {
            responseDTO.setSuccess(false);
            responseDTO.setContent(e.getMessage());
            log.error(e.getMessage(), e);
            throw new BizException(BasicCode.UNKNOWN_ERROR, e.getMessage());
        } finally {
            if (!existHistory) {
                // 保存本次的操作记录
                OperationRecord or = new OperationRecord();
                or.setOperationNo(confirmPayRequestDTO.getRequestId());
                or.setContent3(JSON.toJSONString(responseDTO));
                or.setSuccess(responseDTO.isSuccess());
                or.setType("confirmPay");
                BaseBiz.setOperInfo(or, "system_confirm_pay", true);
                operationRecordBiz.save(or, confirmPayRequestDTO.getOperatorId());
            }
            if (lock != null) {
                redisLockService.unlock(confirmPayRequestDTO.getOrderNo(), lock);
            }
            log.info("--->> 确认支付 - 结束 <<---");
        }
    }

    private void createSplitBillDetail(SplitPayBillDTO splitPayBillDTO, List<SplitPayBillDetailDTO> detailDTOList) {
        List<SplitPayBillDetailDTO> list = Lists.newArrayList();
        for (SplitPayBillDetailDTO dto : detailDTOList) {
            if (dto.getPayAmount().compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }
            SplitPayBillDetailDTO detailDTO = new SplitPayBillDetailDTO();
            BeanUtils.copyProperties(splitPayBillDTO, detailDTO);
            detailDTO.setPayAmount(checkDecimalNumberAndFormat(dto.getPayAmount()));
            detailDTO.setPayeeMemberId(dto.getPayeeMemberId());
            detailDTO.setPayeeMemberName(dto.getPayeeMemberName());
            detailDTO.setActualPayAmount(BigDecimal.ZERO);
            detailDTO.setServiceFee(BigDecimal.ZERO);
            detailDTO.setSubject(dto.getSubject());

            list.add(detailDTO);
        }
        splitPayBillDTO.setDetailDTOList(list);
    }

    private void validatePayStatus(PaymentRequestDTO paymentRequestDTO) {
        if (paymentRequestDTO == null || CsStringUtils.isEmpty(paymentRequestDTO.getBizId())) {
            throw new BizException(PayCode.INVALID_PARAM, "非法的支付请求");
        }
        try {
            //校验订单状态，如果是订单支付，但是订单状态不正确，则取消支付 改到web层或调用方实现
            /*ItemResult<OrderPayinfoDTO> payinfoResult = orderPayinfoService.getOrderPayinfo(paymentRequestDTO.getBizId());
            if (!payinfoResult.isSuccess() || payinfoResult.getData() == null) {
                return;
            }
            OrderPayinfoDTO orderPayinfoDTO = payinfoResult.getData();
            //订单付款时检查订单状态
            if (CsStringUtils.equals(orderPayinfoDTO.getPayinfoType(),
                    com.ecommerce.order.api.enums.PayTypeEnum.SINGLE.getCode())) {
                OrderDTO orderDTO = orderService.getOrderDetail(orderPayinfoDTO.getObjectId()).getData();
                if (CsStringUtils.equals(orderDTO.getOrderStatus(), OrderStatusEnum.CANCEL.code())) {
                    throw new BizException(BasicCode.UNDEFINED_ERROR, "订单已被取消，无法继续支付！");
                }
            }*/
        } catch (BizException be) {
            throw be;
        } catch (Exception e) {
            log.error("查询订单支付信息异常：" + JSON.toJSONString(e));
        }
    }

    @Override
    public SplitPayCompleteResponseDTO splitCompleteNotify(SplitPayCompleteRequestDTO dto) {
        log.info("-------------splitComplete begin-----------------{}", dto);
        CsStringUtils.checkIsNull(dto.getOrderCode(), "orderCode");
        String lock = null;
        String key = "splitCompleteNotify:" + dto.getOrderCode();
        try {
            lock = redisLockService.lockFast(key);
            //当前方法仅适用银联担保支付
            dto.setChannelCode(ChannelCodeEnum.GNETEPAY.getCode());
            return channelExecutor.splitCompleteNotify(dto);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BizException(BasicCode.CUSTOM_ERROR, e.getMessage());
        } finally {
            log.info("-------------splitComplete end-------------");
            if (!CsStringUtils.isEmpty(lock)) {
                redisLockService.unlock(key, lock);
            }
        }
    }

    @Override
    public PaymentCallbackResponseDTO confirmPayCallback(PaymentCallbackRequestDTO callbackMsg) {
        try {
            log.info("----------进入支付确认回调-----------{}", callbackMsg);

            if (callbackMsg == null) {
                log.info("PaymentCallbackRequestDTO 对象为空");
                throw new BizException(PayCode.DATA_NOT_FOUND);
            }
            //忽略分账结束的回调通知
            String key = GneteAdapter.REDIS_KEY_PREFIX + GneteAdapter.SPLIT_COMPLETE + callbackMsg.getCallBackRequestCode();
            if (redisService.hasKey(key)) {
                redisService.del(key);
                return null;
            }

            Optional<SplitPayBillDTO> optional = splitPayBillBiz.findByNo(callbackMsg.getPaymentBillNo());

            if (optional.isEmpty()) {
                log.info("根据 PaymentBillNo 未查询到支付确认单 {}", callbackMsg.getPaymentBillNo());
                SplitPayBillDTO bySettOrderNo = splitPayBillBiz.findBySettOrderNo(callbackMsg.getCallBackRequestCode());
                optional = Optional.ofNullable(bySettOrderNo);
            }
            if (optional.isEmpty()) {
                log.info("根据 PaymentBillNo 未查询到支付确认单 {}", callbackMsg.getPaymentBillNo());
                throw new BizException(PayCode.DATA_NOT_FOUND);
            }
            callbackMsg.setPaymentBillId(optional.get().getSplitPayBillId());
            PaymentCallbackResponseDTO responseDTO = channelExecutor.confirmPayCallback(callbackMsg);
            log.info("----------支付确认回调结束----------");
            return responseDTO;
        } catch (Exception e) {
            log.error("支付确认回调出错, message: {}", e.getMessage(), e);
            return this.createCallbackResponseByFail();
        }
    }

    @Override
    public PaymentResponseDTO pay(PaymentRequestDTO paymentRequestDTO) {
        // TODO: 重构此方法以降低认知复杂度 (当前: 26, 目标: ≤15)
        // 建议: 1) 提取子方法 2) 使用早期返回 3) 简化条件表达式
        log.info("-------------开始支付-----------------{}", paymentRequestDTO);
        String lock = null;
        // TODO: 重构此方法以降低认知复杂度 (当前: 26, 目标: ≤15)
        // 建议: 1) 提取子方法 2) 使用早期返回 3) 简化条件表达式
        String paymentBillId = null;
        //验证支付状态
        validatePayStatus(paymentRequestDTO);
        try {
            lock = redisLockService.lockFast(paymentRequestDTO.getBizId());
            log.info("-------------end lock-----------------");
            PaymentRequestWrapDTO paymentRequest = new PaymentRequestWrapDTO();
            paymentRequest.setReplicate(false);
            paymentRequest.setIsMonthly(paymentRequestDTO.getIsMonthly());
            paymentRequest.setSubtractAmount(paymentRequestDTO.getSubtractAmount());

            PaymentBillDTO paymentBillDTO = paymentBillBiz.findRecentRecordBySrcBizNo(paymentRequestDTO.getBizId());
            log.info("-------------查询历史支付单 findRecentRecordBySrcBizNo----------------- {}", paymentBillDTO);
            ChannelConfigDTO channelConfigDTO = channelConfigBiz.findByChannelCode(paymentRequestDTO.getChannelCode());
            if (Objects.isNull(channelConfigDTO)) {
                channelConfigDTO = new ChannelConfigDTO();
                channelConfigDTO.setAllowRetry(Boolean.TRUE);
                channelConfigDTO.setChannelCode(ChannelCodeEnum.OFFLINE.getCode());
                channelConfigDTO.setChannelStatus(Boolean.TRUE);
            }
            log.info("-------------findByChannelConfig----------------- {}", channelConfigDTO);

            PaymentBillDTO bizRequest = this.generatePaymentBillDTO(paymentRequestDTO.getBizId(),
                    paymentRequestDTO.getPaymentBizCode(), paymentRequestDTO.getOperator());
            log.info("获取业务方的支付请求: {}", bizRequest);

            boolean needCreateNewPaymentBill = true;
            if (paymentBillDTO != null) {
                log.info("存在历史支付单 {}", paymentBillDTO);
                boolean sameChannel = paymentBillDTO.getChannelCode().equals(paymentRequestDTO.getChannelCode());

                paymentRequest.setOldChannelCode(paymentBillDTO.getChannelCode());
                paymentRequest.setOldStatus(paymentBillDTO.getStatus());
                paymentRequest.setReplicate(true);

                needCreateNewPaymentBill = this.checkPaymentBillStatusByPayMethod(bizRequest, channelConfigDTO, paymentBillDTO, sameChannel, paymentRequestDTO.getOperator());
            }

            log.info("是否生成新的支付单: {}", needCreateNewPaymentBill);
            if (needCreateNewPaymentBill) {

                if (paymentBillDTO != null) {
                    if (PaymentStatusEnum.PAY_ING.getCode().equals(paymentBillDTO.getStatus())) {
                        log.info("关闭历史支付单");
                        try {
                            PaymentResponseDTO responseDTO = channelExecutor.doCancelPayment(paymentBillDTO,
                                    paymentRequestDTO.getOperator());
                            log.info("调用第三方关闭支付单，返回: {}", responseDTO);
                        } catch (Exception e) {
                            log.error(e.getMessage(), e);
                            throw new BizException(PayCode.HAS_NOT_CLOSE_PAYMENT, e.getMessage());
                        }
                    }
                    paymentBillBiz.updateStatus(paymentBillDTO.getPaymentBillId(),
                            PaymentStatusEnum.PAY_CLOSE.getCode(), paymentRequestDTO.getOperator());
                }

                paymentRequest.setNewPaymentBill(true);
                paymentBillDTO = this.createNewPaymentBillByPay(bizRequest, channelConfigDTO,
                        paymentRequestDTO);
            }
            paymentBillId = paymentBillDTO.getPaymentBillId();

            MemberChannelDTO memberChannelDTO = new MemberChannelDTO();
            memberChannelDTO.setChannelCode("offline");
            memberChannelDTO.setAllowReceive(Boolean.TRUE);

            if (!CsStringUtils.isEmpty(paymentRequestDTO.getAppType())) {
                paymentRequest.setAppType(paymentRequestDTO.getAppType().toLowerCase());
            }
            paymentRequest.setChannelConfigDTO(channelConfigDTO);
            paymentRequest.setPaymentBillDTO(paymentBillDTO);
            paymentRequest.setMemberChannelDTO(memberChannelDTO);
            paymentRequest.setClientType(paymentRequestDTO.getClientType().toLowerCase());
            paymentRequest.setReturnUrl(bindReturnUrl(paymentRequestDTO.getReturnUrl(), paymentBillDTO.getPaymentBillNo()));
            paymentRequest.setClientIP(paymentRequestDTO.getClientIP());
            paymentRequest.setBody(paymentRequestDTO.getBody());
            paymentRequest.setCode(paymentRequestDTO.getCode());
            paymentRequest.setBizId(paymentRequestDTO.getBizId());
            paymentRequest.setGneteTradeWayDTO(paymentRequestDTO.getGneteTradeWayDTO());
            log.info("组装request对象: {}", paymentRequest);

            PaymentResponseDTO paymentResponseDTO = channelExecutor.doPay(paymentRequest,
                    paymentRequestDTO.getOperator());

            boolean success = paymentResponseDTO.isSuccess();
            paymentResponseDTO.setPaymentBillNo(paymentBillDTO.getPaymentBillNo());
            log.info("返回结果 : {}, 状态: {}", success, paymentResponseDTO.getStatus());
            log.info("内容: {}", paymentResponseDTO);

            if (success) {
                log.info("是否发起支付通知：{}", paymentResponseDTO.isChangeStatus());
                if (!paymentResponseDTO.isChangeStatus()) {
                    paymentBillBiz.updateStatus(paymentBillId, PaymentStatusEnum.PAY_ING.getCode(),
                            paymentRequestDTO.getOperator());
                    log.info("发送支付通知:{}", JSON.toJSONString(paymentBillDTO));
                    this.notifyPaymentResult(paymentBillDTO, paymentRequestDTO);
                    this.setRedisStatus(paymentBillDTO.getPaymentBillNo(), PaymentStatusEnum.PAY_ING.getCode());
                    return paymentResponseDTO;
                }
                this.setRedisStatus(paymentBillDTO.getPaymentBillNo(), paymentBillDTO.getStatus());
                return paymentResponseDTO;
            } else {
                this.setRedisStatus(paymentBillDTO.getPaymentBillNo(), paymentBillDTO.getStatus());
                throw new BizException(PayCode.PAY_FAIL, paymentResponseDTO.getContent());
            }
        } catch (BizException e) {
            if (!CsStringUtils.isEmpty(paymentBillId)) {
                if (e.getErrorCode().getCode().equals(PayCode.PAYMENT_TIMEOUT.getCode())) {
                    paymentBillBiz.updateStatus(paymentBillId, PaymentStatusEnum.PAY_TIMEOUT.getCode(),
                            paymentRequestDTO.getOperator());
                } else {
                    this.setPaymentBillFail(paymentBillId, paymentRequestDTO.getOperator());
                }
            }
            throw e;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            this.setPaymentBillFail(paymentBillId, paymentRequestDTO.getOperator());
            throw new BizException(PayCode.PAY_FAIL, e.getMessage());
        } finally {
            log.info("-------------支付结束-------------");
            if (!CsStringUtils.isEmpty(lock)) {
                redisLockService.unlock(paymentRequestDTO.getBizId(), lock);
            }
        }
    }

    private String bindReturnUrl(String returnUrl, String paymentBillNo) {
        if (CsStringUtils.isEmpty(returnUrl)) {
            return "";
        }
        returnUrl = returnUrl.trim();
        if (returnUrl.contains("?")) {
            if (!returnUrl.endsWith("&") && !returnUrl.endsWith("?")) {
                returnUrl = returnUrl + "&";
            }
        } else {
            returnUrl = returnUrl + "?";
        }
        return returnUrl + "paymentBillNo=" + paymentBillNo;
    }

    @Override
    public DirectPayResponseDTO directPayByPlatform(DirectPayRequestDTO directPayRequestDTO) {
        if (CsStringUtils.isEmpty(directPayRequestDTO.getPayerMemberId())) {
            throw new BizException(BasicCode.PARAM_NULL, "PayerMemberId");
        }
        return channelExecutor.doDirectPay(directPayRequestDTO);
    }

    @Override
    public AutoPaymentResponseDTO autoPayment(AutoPaymentRequestDTO autoPaymentRequestDTO) {
        log.info("----------->>> 自动支付 <<<-------- {}", autoPaymentRequestDTO);
        String lock = null;
        String paymentBillId = null;
        try {
            lock = redisLockService.lockFast(autoPaymentRequestDTO.getBizId());
            log.info("-------------end lock-----------------");
            if (CsStringUtils.isEmpty(autoPaymentRequestDTO.getOrderNo())) {
                throw new BizException(BasicCode.PARAM_NULL, "orderNo");
            }
            if (CollectionUtils.isEmpty(autoPaymentRequestDTO.getPaymentBillDetailDTOList())) {
                throw new BizException(BasicCode.CUSTOM_ERROR, "支付请求详情DTO列表不可为空");
            }
            AutoPaymentResponseDTO responseDTO;

            String channelCode = autoPaymentRequestDTO.getChannelCode();
            if (CsStringUtils.isEmpty(channelCode)) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "channelCode 为空");
            }
            if (!ChannelCodeEnum.PINGANJZPAY.getCode().equals(channelCode) &&
                    !ChannelCodeEnum.ERP.getCode().equals(channelCode) && !ChannelCodeEnum.CREDIT.getCode().equals(channelCode)) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "当前渠道不支持自动支付");
            }
            autoPaymentRequestDTO.setOperatorId(CsStringUtils.isBlank(autoPaymentRequestDTO.getOperatorId()) ? "System_auto_pay" : autoPaymentRequestDTO.getOperatorId());

            ChannelConfigDTO channelConfigDTO = channelConfigBiz.findByChannelCode(channelCode);
            if (channelConfigDTO == null) {
                log.info("channelConfigDTO: null");
                throw new BizException(PayCode.DATA_NOT_FOUND);
            }
            log.info("-------------findByChannelConfig----------------- {}", channelConfigDTO);
            List<PaymentBillDTO> paymentBillDTOList = paymentBillBiz.findByOrderNo(autoPaymentRequestDTO.getOrderNo());
            PaymentBillDTO paymentBillDTO = CollectionUtils.isEmpty(paymentBillDTOList) ? null : paymentBillDTOList.get(0);
            MemberChannelDTO payerMemberChannelDTO = null;
            String payerMemberId = paymentBillDTO != null ? paymentBillDTO.getPayerMemberId() : autoPaymentRequestDTO.getPayerMemberId();
            if (ChannelCodeEnum.CREDIT.getCode().equals(channelCode) || ChannelCodeEnum.ERP.getCode().equals(channelCode)) {
                String payeeMemberId = autoPaymentRequestDTO.getPaymentBillDetailDTOList().get(0).getPayeeMemberId();
                if (autoPaymentRequestDTO.getPaymentBillDetailDTOList().size() > 1) {
                    Optional<PaymentRequirementDetailDTO> payeeOptional = autoPaymentRequestDTO.getPaymentBillDetailDTOList().stream()
                            .filter(item -> (CsStringUtils.isNotBlank(item.getSubject()) && (item.getSubject().contains("goods") || item.getSubject().contains("订单货款"))) ||
                                    (!MemberPlatform.PLATFORM_MEMBERID.getId().equals(item.getPayeeMemberId()) && !MemberPlatform.PLATFORM_CARRIER_MEMBERID.getId().equals(item.getPayeeMemberId()))
                            ).findFirst();
                    payeeMemberId = payeeOptional.isPresent() ? payeeOptional.get().getPayeeMemberId() : payeeMemberId;
                }
                payerMemberChannelDTO = memberChannelBiz.findMemberChannelByPayee(payerMemberId, payeeMemberId, ChannelCodeEnum.getByCode(channelCode));
            } else {
                payerMemberChannelDTO = memberChannelBiz.findByMemberIdAndChannelCode(payerMemberId, channelCode, ChannelPaymentTypeEnum.PAYER);
            }
            if (payerMemberChannelDTO == null) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "付款方支付渠道为空");
            }

            BigDecimal amount = checkDecimalNumberAndFormat(new BigDecimal(autoPaymentRequestDTO.getAmount()));
            autoPaymentRequestDTO.setAmount(amount.toPlainString());

            if (ChannelCodeEnum.PINGANJZPAY.getCode().equals(channelCode) &&
                    (!memberChannelBiz.checkMemberChannelPasswordFree(payerMemberChannelDTO) || amount.compareTo(new BigDecimal("1000")) > 0)) {
                log.info("自动支付 平台授信 买家：{}, 金额：{}, 渠道: {}, 订单: {}", payerMemberChannelDTO.getMemberId(),
                        amount, payerMemberChannelDTO.getChannelCode(), autoPaymentRequestDTO.getOrderNo());
                return platformCredit(autoPaymentRequestDTO, payerMemberChannelDTO);
            }

            // 新建支付单
            PaymentBillDTO temp = new PaymentBillDTO();
            if (paymentBillDTO == null) {
                //新建
                PaymentBillDTO bizRequest = this.generatePaymentBillDTO(autoPaymentRequestDTO.getBizId(),
                        "order", autoPaymentRequestDTO.getOperatorId(), autoPaymentRequestDTO.getPaymentRequirementDTO());
                log.info("获取业务方的支付请求: {}", bizRequest);
                PaymentRequestDTO paymentRequestDTO = new PaymentRequestDTO();
                paymentRequestDTO.setOperator(autoPaymentRequestDTO.getOperatorId());
                paymentRequestDTO.setClientType(ClientType.PC.getCode());
                paymentRequestDTO.setRemarks(autoPaymentRequestDTO.getRemarks());
                temp = this.createNewPaymentBillByPay(bizRequest, channelConfigDTO, paymentRequestDTO);
            } else {
                for (PaymentBillDTO billDTO : paymentBillDTOList) {
                    if (PaymentStatusEnum.PAY_ING.getCode().equals(billDTO.getStatus())) {
                        log.info("关闭历史支付单");
                        try {
                            PaymentResponseDTO responseDTO2 = channelExecutor.doCancelPayment(billDTO,
                                    autoPaymentRequestDTO.getOperatorId());
                            log.info("调用第三方关闭支付单，返回: {}", responseDTO2);
                        } catch (Exception e) {
                            log.error(e.getMessage(), e);
                            throw new BizException(PayCode.HAS_NOT_CLOSE_PAYMENT, e.getMessage());
                        }
                        paymentBillBiz.updateStatus(billDTO.getPaymentBillId(),
                                PaymentStatusEnum.PAY_CLOSE.getCode(), autoPaymentRequestDTO.getOperatorId());
                    }
                }
                BeanUtils.copyProperties(paymentBillDTO, temp);
                temp.setPaymentBillId(null);
                temp.setPayAmount(amount);
                temp.setActualPayAmount(null);
                temp.setStatus(PaymentStatusEnum.PAY_ING.getCode());
                temp.setOrderId(autoPaymentRequestDTO.getOrderId());
                temp.setOrderNo(autoPaymentRequestDTO.getOrderNo());
                temp.setDetail(autoPaymentRequestDTO.getSubject());
                temp.setChannelId(channelConfigDTO.getChannelId());
                temp.setChannelCode(channelCode);
                temp.setChannelName(channelConfigDTO.getChannelName());
                temp.setChannelType(channelConfigDTO.getChannelType());
                temp.setIsMonthly(autoPaymentRequestDTO.getIsMonthly());
                temp.setSubtractAmount(autoPaymentRequestDTO.getSubtractAmount());
                paymentBillBiz.create(temp, autoPaymentRequestDTO.getOperatorId());
                List<PaymentBillDetailDTO> paymentBillDetailDTOList = new ArrayList<>();
                List<PaymentRequirementDetailDTO> detailDTOS = autoPaymentRequestDTO.getPaymentBillDetailDTOList();
                for (PaymentRequirementDetailDTO detailDTO : detailDTOS) {
                    PaymentBillDetailDTO dto = new PaymentBillDetailDTO();
                    BeanUtils.copyProperties(temp, dto);
                    dto.setPayeeMemberId(detailDTO.getPayeeMemberId());
                    dto.setPayeeMemberName(detailDTO.getPayeeMemberName());
                    dto.setSubject(detailDTO.getSubject());
                    dto.setPayAmount(new BigDecimal(detailDTO.getPayAmount()));
                    paymentBillDetailBiz.save(dto, autoPaymentRequestDTO.getOperatorId());
                    paymentBillDetailDTOList.add(dto);
                }
                temp.setDetailDTOList(paymentBillDetailDTOList);
            }

            log.info("免密支付 买家：{}, 金额：{}, 渠道: {}, 订单: {}", payerMemberChannelDTO.getMemberId(),
                    temp.getPayAmount(), payerMemberChannelDTO.getChannelCode(), autoPaymentRequestDTO.getOrderNo());
            paymentBillId = temp.getPaymentBillId();

            responseDTO = channelExecutor.doAutoPayment(temp, payerMemberChannelDTO, autoPaymentRequestDTO.getOperatorId());
            responseDTO.setTradeBillId(temp.getPaymentBillId());
            responseDTO.setTradeBillNo(temp.getPaymentBillNo());
            log.info("----------->>> 结束自动支付 <<<-------{}", responseDTO);
            return responseDTO;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            this.setPaymentBillFail(paymentBillId, autoPaymentRequestDTO.getOperatorId());
            if (e instanceof BizException) {
                throw e;
            }
            throw new BizException(PayCode.PAY_FAIL, e.getMessage());
        } finally {
            log.info("-------------自动支付结束-------------");
            if (!CsStringUtils.isEmpty(lock)) {
                redisLockService.unlock(autoPaymentRequestDTO.getBizId(), lock);
            }
        }
    }

    private AutoPaymentResponseDTO platformCredit(AutoPaymentRequestDTO requestDTO, MemberChannelDTO payerMemberChannelDTO) {
        String lock = null;
        try {
            // 锁会员渠道，防止买家提现
            lock = redisLockService.lockFast(payerMemberChannelDTO.getMemberChannelId());
            // todo 平台授信
            AutoPaymentResponseDTO responseDTO = new AutoPaymentResponseDTO();
            responseDTO.setOrderNo(requestDTO.getOrderNo());
            responseDTO.setSuccess(false);
            responseDTO.setComment("暂不支持平台授信");
            return responseDTO;
        } finally {
            if (lock != null) {
                redisLockService.unlock(payerMemberChannelDTO.getMemberChannelId(), lock);
            }
        }
    }

    private void setPaymentBillFail(String id, String operatorId) {
        if (!CsStringUtils.isEmpty(id)) {
            paymentBillBiz.updateStatus(id, PaymentStatusEnum.PAY_FAIL.getCode(), operatorId);
        }
    }

    private void checkByRefund(RefundRequestDTO refundRequestDTO) {
        List<PaymentRequirementDetailDTO> detailDTOList = refundRequestDTO.getPaymentBillDetailDTOList();
        Map<String, List<PaymentRequirementDetailDTO>> map = detailDTOList.stream().collect(Collectors.groupingBy(PaymentRequirementDetailDTO::getPayeeMemberId));
        // 去重
        List<PaymentRequirementDetailDTO> collect = Lists.newArrayList();
        for (Map.Entry<String, List<PaymentRequirementDetailDTO>> entry : map.entrySet()) {
            PaymentRequirementDetailDTO detailDTO = entry.getValue().get(0);
            if (entry.getValue().size() > 1) {
                BigDecimal reduce = entry.getValue().stream()
                        .map(PaymentRequirementDetailDTO::getPayAmount)
                        .map(BigDecimal::new).reduce(BigDecimal.ZERO, BigDecimal::add);
                String subject = entry.getValue().stream().map(PaymentRequirementDetailDTO::getSubject)
                        .filter(i -> !CsStringUtils.isEmpty(i)).collect(Collectors.joining(","));
                detailDTO.setPayAmount(reduce.toPlainString());
                detailDTO.setSubject(subject);
            }
            collect.add(detailDTO);
        }
        refundRequestDTO.setPaymentBillDetailDTOList(collect);
        detailDTOList = refundRequestDTO.getPaymentBillDetailDTOList();

        // 线下支付 and 授信支付 时，平台应收的资金给卖家，由卖家和平台线下结算
        String way = refundRequestDTO.getChannelCode();
        if ((ChannelCodeEnum.OFFLINE.getCode().equals(way) || ChannelCodeEnum.CREDIT.getCode().equals(way) || ChannelCodeEnum.ERP.getCode().equals(way) ||
                ChannelCodeEnum.WEIXIN.getCode().equals(way) || ChannelCodeEnum.ALIPAY.getCode().equals(way))
                && detailDTOList.size() > 1) {
            Iterator<PaymentRequirementDetailDTO> it = detailDTOList.iterator();
            BigDecimal platformAmount = BigDecimal.ZERO;
            StringJoiner subjectStr = new StringJoiner(",");
            boolean hasGoodsRefundSubject = false;
            List<PaymentRequirementDetailDTO> removeList = Lists.newLinkedList();
            while (it.hasNext()) {
                PaymentRequirementDetailDTO next = it.next();
                String payeeId = next.getPayeeMemberId();
                String subject = next.getSubject().toLowerCase();
                String platformCarrierMemberId = MemberPlatform.PLATFORM_CARRIER_MEMBERID.getId();
                if (payeeId.equals(platformCarrierMemberId)) {
                    platformAmount = platformAmount.add(new BigDecimal(next.getPayAmount()));
                    subjectStr.add(next.getSubject());
                    removeList.add(next);
                    it.remove();
                } else {
                    hasGoodsRefundSubject = hasGoodsRefundSubject || PayDetailTypeEnum.GOODS_REFUND.getCode().equals(subject);
                }
            }
            for (PaymentRequirementDetailDTO payDetail : detailDTOList) {
                String subject = payDetail.getSubject().toLowerCase();
                if (hasGoodsRefundSubject && PayDetailTypeEnum.GOODS_REFUND.getCode().equals(subject)) {
                    subjectStr.add(subject);
                    BigDecimal payAdmount = new BigDecimal(payDetail.getPayAmount());
                    payAdmount = payAdmount.add(platformAmount);
                    payDetail.setPayAmount(payAdmount.toPlainString());
                    payDetail.setSubject(subjectStr.toString());
                    break;
                }
            }
            if (!hasGoodsRefundSubject) {
                detailDTOList.addAll(removeList);
            }
        }
    }

    @Override
    public PaymentResponseDTO refund(RefundRequestDTO refundRequestDTO) {
        log.info("--------------开始退款------------{}", refundRequestDTO);
        if (CsStringUtils.isEmpty(refundRequestDTO.getPaymentBillNo())) {
            throw new BizException(BasicCode.PARAM_NULL, "paymentBillNo");
        }
        List<PaymentRequirementDetailDTO> requireList = refundRequestDTO.getPaymentBillDetailDTOList();
        if (!CollectionUtils.isEmpty(requireList)) {
            // 线下支付 and 授信支付 时，平台应收的资金给卖家，由卖家和平台线下结算
            checkByRefund(refundRequestDTO);
        }
        String lock = null;
        String refundBillId = "";
        try {
            lock = redisLockService.lockFast(refundRequestDTO.getPaymentBillNo());
            log.info(" >> 开始验证 退款信息");
            PaymentBillDTO paymentBillDTO = paymentBillBiz.findByPaymentBillNo(refundRequestDTO.getPaymentBillNo());

            if (paymentBillDTO == null) {
                log.info("交易单不存在");
                throw new BizException(PayCode.DATA_NOT_FOUND);
            }
            List<PaymentBillDetailDTO> detailDTOList = paymentBillDTO.getDetailDTOList();
            if (detailDTOList == null) {
                detailDTOList = Lists.newArrayList();
            }

            String status = paymentBillDTO.getStatus();
            BigDecimal totalPayAmount = paymentBillDTO.getPayAmount();
            if (!PaymentStatusEnum.PAY_SUCCESS.getCode().equals(status) &&
                    !PaymentStatusEnum.PAY_FREEZE.getCode().equals(status)) {
                log.info("支付单状态不正确 status: {}", status);
                throw new BizException(PayCode.PAY_CAN_NOT_REFUND, "订单尚未完成支付");
            }

            List<PaymentRequirementDetailDTO> dtoList = refundRequestDTO.getPaymentBillDetailDTOList();
            if (CollectionUtils.isEmpty(dtoList)) {
                if (detailDTOList.size() > 1) {
                    throw new BizException(BasicCode.UNDEFINED_ERROR, "当前订单有多个收款方，本次退款未指定退款方");
                }
            } else {
                BigDecimal totalRefundAmount = BigDecimal.ZERO;
                for (PaymentRequirementDetailDTO dto : dtoList) {
                    totalRefundAmount = totalRefundAmount.add(new BigDecimal(dto.getPayAmount()));
                    boolean exist = false;
                    for (PaymentBillDetailDTO detailDTO : detailDTOList) {
                        if (dto.getPayeeMemberId().equals(detailDTO.getPayeeMemberId())) {
                            exist = true;
                            BigDecimal payAmount = new BigDecimal(dto.getPayAmount());
                            if (payAmount.compareTo(detailDTO.getPayAmount()) > 0 && BooleanUtils.isNotTrue(refundRequestDTO.getOnlyCheckTotalAmount())) {
                                log.info("退款金额太大 paymentBillDTO:{}", paymentBillDTO);
                                throw new BizException(BasicCode.UNDEFINED_ERROR, "退款金额太大");
                            }
                        }
                    }
                    if (!exist) {
                        log.info("refundRequestDTO.getPaymentBillDetailDTOList():{}", dtoList);
                        log.info("paymentBillDTO.getDetailDTOList():{}", detailDTOList);
                        throw new BizException(BasicCode.UNDEFINED_ERROR, "未知的退款方: " + dto.getPayeeMemberId());
                    }
                }
                if (totalRefundAmount.compareTo(new BigDecimal(refundRequestDTO.getAmount())) != 0) {
                    log.error("明细项汇总金额与退款单金额不一致,退款单金额：{}，明细项汇总:{}", refundRequestDTO.getAmount(), totalRefundAmount);
                    throw new BizException(BasicCode.UNDEFINED_ERROR, "明细项汇总金额与退款单金额不一致");
                }
            }

            RefundBillDTO refundBillDTO = new RefundBillDTO();
            BeanUtils.copyProperties(paymentBillDTO, refundBillDTO);
            refundBillDTO.setStatus(PaymentStatusEnum.NEW_PAY.getCode());
            refundBillDTO.setRefundBillId(null);
            refundBillDTO.setRelatedPaymentBillNo(paymentBillDTO.getPaymentBillNo());
            refundBillDTO.setExtStatus(null);

            String refundChannelCode = refundRequestDTO.getChannelCode();
            if (!CsStringUtils.isEmpty(refundChannelCode) &&
                    !paymentBillDTO.getChannelCode().equals(refundChannelCode)) {
                if (ChannelCodeEnum.OFFLINE.getCode().equals(refundChannelCode)) {
                    log.info("支付单 {} 原支付渠道 {} 转 线下退款",
                            paymentBillDTO.getPaymentBillNo(), paymentBillDTO.getChannelCode());
                    ChannelConfigDTO offlineChannelConfig = channelConfigBiz.findByChannelCode(refundChannelCode);
                    refundBillDTO.setChannelCode(offlineChannelConfig.getChannelCode());
                    refundBillDTO.setChannelName(offlineChannelConfig.getChannelName());
                    refundBillDTO.setChannelType(offlineChannelConfig.getChannelType());
                    refundBillDTO.setChannelId(offlineChannelConfig.getChannelId());
                } else {
                    throw new BizException(BasicCode.UNDEFINED_ERROR, "退款失败, 只支持原渠道和线下退款");
                }
            }

            log.info("金额精度检测: 支付金额: {}, 本次退款金额: {}",
                    paymentBillDTO.getPayAmount(), refundRequestDTO.getAmount());
            BigDecimal refundAmount;
            try {
                refundAmount = this.checkDecimalNumberAndFormat(new BigDecimal(refundRequestDTO.getAmount()));
            } catch (Exception e) {
                throw new BizException(PayCode.PAY_AMOUNT_ERROR);
            }
            if (CollectionUtils.isEmpty(refundRequestDTO.getPaymentBillDetailDTOList())) {
                if (detailDTOList.size() > 1) {
                    throw new BizException(BasicCode.UNDEFINED_ERROR, "当前订单有多个收款方，本次退款未指定退款方");
                }
                PaymentBillDetailDTO detailDTO = paymentBillDTO.getDetailDTOList().get(0);
                List<PaymentRequirementDetailDTO> list = Lists.newArrayList();
                PaymentRequirementDetailDTO d = new PaymentRequirementDetailDTO();
                d.setPayAmount(refundAmount.toPlainString());
                d.setPayeeMemberName(detailDTO.getPayeeMemberName());
                d.setPayeeMemberId(detailDTO.getPayeeMemberId());
                d.setSubject(detailDTO.getSubject());
                list.add(d);
                refundRequestDTO.setPaymentBillDetailDTOList(list);
            }

            log.info(" >> 生成退款单");
            Date createTime = paymentBillDTO.getCreateTime();
            refundBillDTO.setSrcBizNo(refundRequestDTO.getNewBizId());
            refundBillDTO.setPayAmount(refundAmount);
            BizConfigDTO bizConfigDTO = paymentIntegrationBiz.findByCode(refundRequestDTO.getBizCode());
            refundBillDTO.setPaymentBizCode(refundRequestDTO.getBizCode());
            refundBillDTO.setBizName(bizConfigDTO.getBizName());
            this.createRefundBillDTO(paymentBillDTO, refundBillDTO, refundRequestDTO.getPaymentBillDetailDTOList(), refundRequestDTO.getOperator());
            refundBillId = refundBillDTO.getRefundBillId();

            RefundDTO requestDTO = new RefundDTO();
            requestDTO.setNewBizId(refundRequestDTO.getNewBizId());
            requestDTO.setAutoOffline(refundRequestDTO.getAutoOffline());
            requestDTO.setRefundBillDTO(refundBillDTO);
            requestDTO.setRefundAmount(refundAmount);
            requestDTO.setRefundCount(refundBillBiz.findCountByRelatePaymentNo(paymentBillDTO.getPaymentBillNo()));
            requestDTO.setPaymentTime(createTime);
            requestDTO.setTotalPayAmount(totalPayAmount);
            requestDTO.setOperator(refundRequestDTO.getOperator());

            log.info("进入退款");
            PaymentResponseDTO responseDTO = channelExecutor.doRefund(requestDTO);

            if (!responseDTO.isSuccess()) {
                log.info("-------------退款失败-------------{}", responseDTO);
                throw new BizException(PayCode.PAY_CAN_NOT_REFUND, responseDTO.getContent());
            }
            responseDTO.setPaymentBillId(refundBillDTO.getRefundBillId());
            responseDTO.setPaymentBillNo(refundBillDTO.getRefundBillNo());
            Map<String, String> parms = Optional.ofNullable(responseDTO.getParms()).orElseGet(HashMap::new);
            parms.put("channelCode", refundBillDTO.getChannelCode());
            responseDTO.setParms(parms);
            log.info("-------------退款结束-------------{}", responseDTO);
            return responseDTO;
        } catch (BizException e) {
            log.error(e.getMessage(), e);
            if (!CsStringUtils.isEmpty(refundBillId)) {
                refundBillBiz.updateStatus(refundBillId, PaymentStatusEnum.PAY_FAIL.getCode(),
                        refundRequestDTO.getOperator());
            }
            throw e;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            if (!CsStringUtils.isEmpty(refundBillId)) {
                refundBillBiz.updateStatus(refundBillId, PaymentStatusEnum.PAY_FAIL.getCode(),
                        refundRequestDTO.getOperator());
            }
            throw new BizException(PayCode.PAY_CAN_NOT_REFUND);
        } finally {
            if (!CsStringUtils.isEmpty(lock)) {
                redisLockService.unlock(refundRequestDTO.getPaymentBillNo(), lock);
            }
        }
    }


    @Override
    public OrderPaymentDetailDTO queryPaymentByOrderNo(String orderNo) {
        if (CsStringUtils.isEmpty(orderNo)) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "订单单号不可为空");
        }
        List<PaymentBillDTO> paymentBillDTOList = paymentBillBiz.findByOrderNo(orderNo);
        if (CollectionUtils.isEmpty(paymentBillDTOList)) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "支付单不存在");
        }
        // 过滤掉没支付成功的支付单
        paymentBillDTOList = paymentBillDTOList.stream().filter(item ->
                        PaymentStatusEnum.PAY_SUCCESS.getCode().equals(item.getStatus()) ||
                                PaymentStatusEnum.PAY_FREEZE.getCode().equals(item.getStatus()))
                .toList();
        if (CollectionUtils.isEmpty(paymentBillDTOList)) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "当前订单没有已完成的支付单");
        }
        // 查询分账单
        List<SplitPayBillDTO> splitPayBillDTOList = splitPayBillBiz.findByOrderNo(orderNo);
        splitPayBillDTOList = splitPayBillDTOList.stream()
                .filter(i -> PaymentStatusEnum.PAY_SUCCESS.getCode().equals(i.getStatus()) ||
                        //针对银联支付的话 可能有部分是成功的
                        (i.getChannelCode().startsWith(ChannelTypeEnum.GNETEPAY.getCode()) && PaymentStatusEnum.PAY_FAIL.getCode().equals(i.getStatus())))
                .toList();
        // 查询退款单
        List<RefundBillDTO> refundBillDTOList = refundBillBiz.findByOrderNo(orderNo);
        refundBillDTOList = refundBillDTOList.stream()
                .filter(i -> PaymentStatusEnum.PAY_SUCCESS.getCode().equals(i.getStatus()))
                .toList();

        // key: 该订单所涉及的所有收款方
        // value: 每个收款方的收付款详细
        Map<String, MemberItemDTO> sellerMap = new HashMap<>();
        // key: 该订单所涉及的所有收付款渠道
        // value: 每个渠道上所有收款方的收付款详细
        Map<String, List<MemberItemDTO>> channelMap = new HashMap<>();
        // 付款方共支付的金额
        BigDecimal payerPayTotalAmount = BigDecimal.ZERO;
        // 已分账金额
        BigDecimal expenseAmount = BigDecimal.ZERO;
        // 已退款金额
        BigDecimal refundAmount = BigDecimal.ZERO;

        for (PaymentBillDTO dto : paymentBillDTOList) {
            List<PaymentBillDetailDTO> detailDTOList = paymentBillDetailBiz.findByPaymentBillNo(dto.getPaymentBillNo());
            dto.setDetailDTOList(detailDTOList);
            for (PaymentBillDetailDTO detailDTO : detailDTOList) {
                MemberItemDTO seller = sellerMap.get(detailDTO.getPayeeMemberId());
                if (seller == null) {
                    MemberItemDTO memberItem = createMemberItem(detailDTO);
                    sellerMap.put(memberItem.getMemberId(), memberItem);
                    seller = memberItem;
                }
                // 设置该订单共支付金额
                payerPayTotalAmount = payerPayTotalAmount.add(detailDTO.getPayAmount());
                BigDecimal payAmount = seller.getPayAmount();
                // 设置每个收款方已收款金额
                seller.setPayAmount(payAmount.add(detailDTO.getPayAmount()));
            }
        }

        List<SplitPayBillDTO> splitPayBillDTOList2 = Lists.newArrayList();//临时变量
        for (SplitPayBillDTO splitDTO : splitPayBillDTOList) {
            List<SplitPayBillDetailDTO> dtos = splitPayBillDetailBiz.findBySplitPayBillNo(splitDTO.getSplitPayBillNo());
            splitDTO.setDetailDTOList(Lists.newArrayList());
            for (SplitPayBillDetailDTO dto : dtos) {
                //针对银联支付的话 剔除失败的部分
                if ((splitDTO.getChannelCode().startsWith(ChannelTypeEnum.GNETEPAY.getCode()) && PaymentStatusEnum.PAY_SUCCESS.getCode().equals(dto.getStatus()))) {
                    continue;
                }
                MemberItemDTO seller = sellerMap.get(dto.getPayeeMemberId());
                if (seller == null) {
                    continue;
                }
                // 设置该订单已分账金额
                expenseAmount = expenseAmount.add(dto.getPayAmount());
                BigDecimal actualPayAmount = seller.getActualPayAmount();
                // 设置每个收款方已分账金额
                seller.setActualPayAmount(actualPayAmount.add(dto.getPayAmount()));
                splitDTO.getDetailDTOList().add(dto);
            }
            if (!splitDTO.getDetailDTOList().isEmpty()) {
                splitPayBillDTOList2.add(splitDTO);
            }
        }
        splitPayBillDTOList = splitPayBillDTOList2;

        for (RefundBillDTO refundDTO : refundBillDTOList) {
            List<RefundBillDetailDTO> dtos = refundBillDetailBiz.findRefundNo(refundDTO.getRefundBillNo());
            refundDTO.setDetailDTOList(dtos);
            for (RefundBillDetailDTO dto : dtos) {
                MemberItemDTO seller = sellerMap.get(dto.getPayeeMemberId());
                if (seller == null) {
                    continue;
                }
                // 设置该订单已退款金额
                refundAmount = refundAmount.add(dto.getPayAmount());
                BigDecimal serviceFee = seller.getRefundAmount();
                // 设置每个收款方已退款金额
                seller.setRefundAmount(serviceFee.add(dto.getPayAmount()));
            }
        }

        // 设置每个渠道上所有收款方的收款详细
        for (PaymentBillDTO dto : paymentBillDTOList) {
            List<MemberItemDTO> list = channelMap.get(dto.getChannelCode());
            if (CollectionUtils.isEmpty(list)) {
                List<MemberItemDTO> collect = Lists.newArrayList();
                for (MemberItemDTO value : sellerMap.values()) {
                    collect.add(createMemberItem(value));
                }
                channelMap.put(dto.getChannelCode(), collect);
                list = collect;
            }
            List<PaymentBillDetailDTO> dtoList = dto.getDetailDTOList();
            for (PaymentBillDetailDTO detailDTO : dtoList) {
                for (MemberItemDTO item : list) {
                    if (detailDTO.getPayeeMemberId().equals(item.getMemberId())) {
                        BigDecimal payAmount = item.getPayAmount();
                        item.setPayAmount(payAmount.add(detailDTO.getPayAmount()));
                    }
                }
            }
        }

        // 设置每个渠道上所有收款方的分账详细
        for (SplitPayBillDTO splitDTO : splitPayBillDTOList) {
            List<MemberItemDTO> list = channelMap.get(splitDTO.getChannelCode());
            if (CollectionUtils.isEmpty(list)) {
                continue;
            }
            List<SplitPayBillDetailDTO> dtoList = splitDTO.getDetailDTOList();
            for (SplitPayBillDetailDTO dto : dtoList) {
                for (MemberItemDTO b : list) {
                    if (b.getMemberId().equals(dto.getPayeeMemberId())) {
                        BigDecimal actualPayAmount = b.getActualPayAmount();
                        b.setActualPayAmount(actualPayAmount.add(dto.getPayAmount()));
                    }
                }
            }
        }

        // 设置每个渠道上所有收款方的退款详细
        for (RefundBillDTO refundDTO : refundBillDTOList) {
            List<MemberItemDTO> list = channelMap.get(refundDTO.getChannelCode());
            if (CollectionUtils.isEmpty(list)) {
                continue;
            }
            List<RefundBillDetailDTO> dtoList = refundDTO.getDetailDTOList();
            for (RefundBillDetailDTO dto : dtoList) {
                for (MemberItemDTO b : list) {
                    if (b.getMemberId().equals(dto.getPayeeMemberId())) {
                        BigDecimal serviceFee = b.getRefundAmount();
                        b.setRefundAmount(serviceFee.add(dto.getPayAmount()));
                    }
                }
            }
        }

        OrderPaymentDetailDTO res = new OrderPaymentDetailDTO();
        res.setOrderNo(orderNo);
        res.setPayerPayTotalAmount(payerPayTotalAmount);
        res.setExpenseAmount(expenseAmount);
        res.setRefundAmount(refundAmount);
        res.setMemberItemChannelMap(channelMap);
        res.setPaymentBillDTOList(paymentBillDTOList);
        res.setMemberItemDTOListList(new ArrayList<>(sellerMap.values()));
        return res;
    }

    private MemberItemDTO createMemberItem(PaymentBillDetailDTO item) {
        MemberItemDTO t = new MemberItemDTO();
        t.setMemberId(item.getPayeeMemberId());
        t.setMemberName(item.getPayeeMemberName());
        t.setPayAmount(BigDecimal.ZERO);
        t.setActualPayAmount(BigDecimal.ZERO);
        t.setRefundAmount(BigDecimal.ZERO);
        return t;
    }

    private MemberItemDTO createMemberItem(MemberItemDTO item) {
        MemberItemDTO t = new MemberItemDTO();
        t.setMemberId(item.getMemberId());
        t.setMemberName(item.getMemberName());
        t.setPayAmount(BigDecimal.ZERO);
        t.setActualPayAmount(BigDecimal.ZERO);
        t.setRefundAmount(BigDecimal.ZERO);
        return t;
    }

    @Override
    public PaymentResponseDTO freeze(String orderNo, String amount, String returnUrl, String operator) {
        if (CsStringUtils.isEmpty(orderNo)) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "订单单号不可为空");
        }
        OrderPaymentDetailDTO orderPaymentDetailDTO = queryPaymentByOrderNo(orderNo);
        List<PaymentBillDTO> paymentBillDTOList = orderPaymentDetailDTO.getPaymentBillDTOList();

        // 获取第一次冻结的支付单
        PaymentBillDTO paymentBillDTO = paymentBillDTOList.get(paymentBillDTOList.size() - 1);
        BigDecimal targetAmount = checkDecimalNumberAndFormat(new BigDecimal(amount));
        // 新建支付单
        PaymentBillDTO temp = new PaymentBillDTO();
        BeanUtils.copyProperties(paymentBillDTO, temp);
        temp.setPaymentBillId(null);
        temp.setPayAmount(targetAmount);
        temp.setActualPayAmount(null);
        temp.setStatus(PaymentStatusEnum.PAY_ING.getCode());
        temp.setOrderNo(orderNo);
        paymentBillBiz.create(temp, operator);

        return channelExecutor.doFreeze(temp, returnUrl, operator);
    }

    @Override
    public PaymentResponseDTO unfreeze(String orderNo, String returnUrl, String unfreezeAmount, String operator) {
        return null;
    }

    @Override
    public PaymentCallbackResponseDTO payCallback(PaymentCallbackRequestDTO callbackMsg) {
        try {
            log.info("----------进入支付回调-----------{}", callbackMsg);

            if (callbackMsg == null) {
                log.info("PaymentCallbackRequestDTO 对象为空");
                throw new BizException(PayCode.DATA_NOT_FOUND);
            }

            PaymentBillDTO paymentBillDTO = paymentBillBiz.findByPaymentBillNo(callbackMsg.getPaymentBillNo());

            if (paymentBillDTO == null) {
                log.info("根据 PaymentBillNo 未查询到支付单 {}", callbackMsg.getPaymentBillNo());
                throw new BizException(PayCode.DATA_NOT_FOUND);
            }

            this.checkPaymentCallbackStatus(paymentBillDTO);

            paymentBillDTO.setCompleteTime(new Date());
            callbackMsg.setPaymentBillId(paymentBillDTO.getPaymentBillId());
            PaymentCallbackResponseDTO responseDTO = channelExecutor.doPayCallback(callbackMsg);

            log.info("更新redis");
            this.setRedisStatus(paymentBillDTO.getPaymentBillNo(),
                    responseDTO.getPaymentBillDTO().getStatus());

            log.info("----------回调结束----------");
            return responseDTO;
        } catch (Exception e) {
            log.error("支付回调出错, message: {}", e.getMessage(), e);
            return this.createCallbackResponseByFail();
        }
    }

    @Override
    public PaymentCallbackResponseDTO payCallbackByPlatform(String paymentBillNo, Boolean success, String operatorId) {
        PaymentBillDTO paymentBillDTO = null;
        try {
            log.info("----------进入支付回调-----------");

            paymentBillDTO = paymentBillBiz.findByPaymentBillNo(paymentBillNo);
            if (paymentBillDTO == null) {
                throw new BizException(PayCode.DATA_NOT_FOUND);
            }

            this.checkPaymentCallbackStatus(paymentBillDTO);
            paymentBillDTO.setCompleteTime(new Date());

            PaymentCallbackRequestDTO requestDTO = new PaymentCallbackRequestDTO();
            requestDTO.setPaymentBillNo(paymentBillDTO.getPaymentBillNo());
            requestDTO.setPaymentBillId(paymentBillDTO.getPaymentBillId());
            requestDTO.setChannelCode(paymentBillDTO.getChannelCode());
            requestDTO.setOperator(operatorId);
            requestDTO.setSuccess(success);

            PaymentCallbackResponseDTO responseDTO = channelExecutor.doPayCallback(requestDTO);

            if (responseDTO != null && responseDTO.getPaymentBillDTO() != null) {
                paymentBillDTO = responseDTO.getPaymentBillDTO();
            }
            log.info("----------回调结束----------");
            return responseDTO;
        } catch (Exception e) {
            log.error("支付回调出错, message: {}", e.getMessage());
            return this.createCallbackResponseByFail();
        } finally {
            if (paymentBillDTO != null) {
                log.info("更新支付单状态");
                PaymentBillDTO temp = new PaymentBillDTO();
                temp.setPaymentBillId(paymentBillDTO.getPaymentBillId());
                temp.setPaymentBillNo(paymentBillDTO.getPaymentBillNo());
                temp.setStatus(paymentBillDTO.getStatus());
                this.execCallbackUpdatePaymentBill(paymentBillDTO, temp, operatorId);
            }
        }
    }

    @Override
    public PaymentCallbackResponseDTO orderCreateCallback(String paymentBillNo, Boolean success, String operatorId) {
        log.info(" >>> 进入 orderCreateCallback 订单创建回调 <<< no: {}, flag: {}", paymentBillNo, success);
        if (CsStringUtils.isEmpty(paymentBillNo)) {
            throw new BizException(BasicCode.PARAM_NULL, "paymentBillNo");
        }
        PaymentBillDTO paymentBillDTO = paymentBillBiz.findByPaymentBillNo(paymentBillNo);
        try {
            return channelExecutor.doOrderCreateCallback(paymentBillDTO, success, operatorId);
        } finally {
            log.info(" >>>  orderCreateCallback 订单创建回调 -- 结束 <<<");
        }
    }

    @Override
    public PaymentCallbackResponseDTO refundCallback(PaymentCallbackRequestDTO callbackMsg) {
        log.info("----------进入退款回调-----------{}", callbackMsg);

        if (callbackMsg == null) {
            log.info("PaymentCallbackRequestDTO 对象为空");
            throw new BizException(PayCode.DATA_NOT_FOUND);
        }

        PaymentCallbackResponseDTO responseDTO = channelExecutor.doRefundCallback(callbackMsg);

        log.info("----------回调结束----------");
        return responseDTO;
    }

    @Override
    public PaymentBillDTO getPayBill(String payBillNo) {
        return paymentBillBiz.findByPaymentBillNo(payBillNo);
    }

    @Override
    public PaymentBillDTO getPayBillFromThirdParty(String payBillNo, String operator) {
        checkObjectNull(payBillNo, "payBillNo");

        PaymentBillDTO paymentBillDTO = paymentBillBiz.findByPaymentBillNo(payBillNo);
        checkObjectNull(paymentBillDTO, "payBillNo");
        String oldStatus = paymentBillDTO.getStatus();

        PaymentBillDTO dto = null;
        // TODO: 重构此方法以降低认知复杂度 (当前: 32, 目标: ≤15)
        // 建议: 1) 提取子方法 2) 使用早期返回 3) 简化条件表达式
        try {
            dto = channelExecutor.searchPaymentBill(payBillNo, paymentBillDTO.getChannelCode(), operator);
        } catch (Exception e) {
            log.info("向第三方查询支付单失败, msg: {}", e.getMessage());
        }
        if (dto == null) {
            return paymentBillDTO;
        }
        paymentBillDTO.setExtStatus(dto.getExtStatus());
        paymentBillDTO.setGuaranteedOrderNo(dto.getGuaranteedOrderNo());
        paymentBillDTO.setMctOrderNo(dto.getMctOrderNo());

        if (PaymentStatusEnum.PAY_SUCCESS.getCode().equals(dto.getStatus())
                || PaymentStatusEnum.PAY_FAIL.getCode().equals(dto.getStatus())) {

            this.checkPaymentCallbackStatus(paymentBillDTO);
            if (!paymentBillDTO.getStatus().equals(dto.getStatus())) {
                this.setPaymentBillDTOTime(paymentBillDTO);
            }
            paymentBillDTO.setStatus(dto.getStatus());
            if (PaymentStatusEnum.PAY_SUCCESS.getCode().equals(dto.getStatus())) {
                paymentBillDTO.setActualPayAmount(dto.getPayAmount());
            }

            log.info("notifyBusStatus: {} ,status: {} , oldStatus: {}", paymentBillDTO.getNotifyBusStatus(), dto.getStatus(), oldStatus);
            // 通知订单
            //如果没有通知
            if (CsStringUtils.isEmpty(paymentBillDTO.getNotifyBusStatus()) ||
                    //如果通知失败
                    PaymentCallbackEnum.BIZ_CALL_FAILED.getCode().equals(paymentBillDTO.getNotifyBusStatus()) ||
                    //如果状态变更为成功
                    (PaymentStatusEnum.PAY_SUCCESS.getCode().equals(dto.getStatus()) && !PaymentStatusEnum.PAY_SUCCESS.getCode().equals(oldStatus)) ||
                    //如果是超时
                    PaymentStatusEnum.PAY_TIMEOUT.getCode().equals(dto.getStatus())
            ) {
                if (PaymentStatusEnum.PAY_SUCCESS.getCode().equals(dto.getStatus())) {
                    this.notifyPayment(paymentBillDTO, true, operator);
                } else {
                    this.notifyPayment(paymentBillDTO, false, operator);
                }
            }

            // 更新redis
            String o = redisService.get(PayRedisKey.PAY_BILL + payBillNo);
            if (o != null) {
                if (!dto.getStatus().equals(o)) {
                    this.setRedisStatus(payBillNo, dto.getStatus());
                }
            }
            paymentBillBiz.updatePaymentBill(paymentBillDTO, operator);

            if (PaymentStatusEnum.PAY_SUCCESS.getCode().equals(dto.getStatus())) {
                if (billLogsBiz.countByRelatedBillNo(paymentBillDTO.getPaymentBillNo()) <= 0) {
                    if (paymentBillDTO.getChannelCode().startsWith(ChannelCodeEnum.GNETEPAY.getCode())) {
                        billLogsBiz.logsPaymentByFreeze(paymentBillDTO, operator);
                    } else {
                        billLogsBiz.logsPayment(paymentBillDTO, operator);
                    }
                }
            }
        }
        return paymentBillDTO;
    }

    @Override
    public PaymentBillDTO notifyPaymentBusiness(String paymentBillNo, String operatorId) {
        PaymentBillDTO paymentBillDTO = paymentBillBiz.findByPaymentBillNo(paymentBillNo);
        String status = paymentBillDTO.getNotifyBusStatus();

        // 通知订单
        this.notifyPayment(paymentBillDTO, true, operatorId);

        if (!paymentBillDTO.getNotifyBusStatus().equals(status)) {
            paymentBillBiz.updatePaymentBill(paymentBillDTO, operatorId);
        }

        return paymentBillDTO;
    }

    @Override
    public void checkPaymentBill() {

    }

    @Override
    public PaymentRequirementDTO getPaymentRequirement(String bizId, String paymentBizCode, String operator) {
        return paymentIntegrationBiz.getPaymentRequirement(bizId, paymentBizCode, operator);
    }

    @Override
    public MemberChannelDTO findMemberChannelByPaymentBillNo(String paymentBillNo) {
        PaymentBillDTO paymentBillDTO = paymentBillBiz.findByPaymentBillNo(paymentBillNo);
        String memberId = paymentBillDTO.getPayeeMemberId();
        String channelCode = paymentBillDTO.getChannelCode();
        MemberChannelDTO memberChannelDTO = memberChannelBiz.findByMemberIdAndChannelCode(memberId, channelCode,
                ChannelPaymentTypeEnum.PAYEE);
        if (!CsStringUtils.isEmpty(memberChannelDTO.getPublicKey())) {
            String rsaPublicKey;
            try {
                rsaPublicKey = secretService.decrypt(memberChannelDTO.getPublicKey());
            } catch (Exception e) {
                log.error(e.getMessage());
                throw new BizException(PayCode.UNKNOWN_ERROR, "数据处理失败");
            }
            memberChannelDTO.setPublicKey(rsaPublicKey);
        }
        return memberChannelDTO;
    }

    @Override
    public PageInfo<PaymentBillDTO> pagePaymentBill(PaymentBillQueryDTO paymentBillDTO) {
        Page<PaymentBillDTO> pagePaymentBillDTOList = paymentBillBiz.pagePaymentBill(paymentBillDTO);
        long total = pagePaymentBillDTOList.getTotal();
        Page<PaymentBillDTO> collect = pagePaymentBillDTOList.parallelStream().peek(item -> {
            item.setStatus(PaymentStatusEnum.getMessageByCode(item.getStatus()));
            if (!CsStringUtils.isEmpty(item.getWarningCode())) {
                String warningCode = item.getWarningCode();
                int i = warningCode.indexOf(":");
                if (i != -1) {
                    String code = warningCode.substring(i);
                    warningCode = warningCode.substring(0, i);
                    warningCode = PaymentWarningCodeEnum.getMessageByCode(warningCode) + code;
                } else {
                    warningCode = PaymentWarningCodeEnum.getMessageByCode(warningCode);
                }
                item.setWarningCode(warningCode);
            }
        }).collect(Collectors.toCollection(Page::new));
        collect.setTotal(total);
        return new PageInfo<>(collect);
    }

    private PaymentBillDTO convertPaymentRequirementDTO2PaymentBillDTO(PaymentRequirementDTO paymentRequirementDTO) {
        PaymentBillDTO dto = new PaymentBillDTO();
        dto.setSrcBizNo(paymentRequirementDTO.getBizId());
        dto.setPaymentBizCode(paymentRequirementDTO.getPaymentBizCode());
        dto.setOrderNo(paymentRequirementDTO.getBizNo());
        dto.setDetail(paymentRequirementDTO.getPayType());
        dto.setPayerMemberId(paymentRequirementDTO.getPayerId());
        dto.setPayerMemberName(paymentRequirementDTO.getPayerName());
        dto.setOrderId(paymentRequirementDTO.getOrderId());
        dto.setGoodsNames(paymentRequirementDTO.getGoodsNames());
        dto.setPayeeMemberId(paymentRequirementDTO.getPayeeId());

        dto.setMemberMDMCode(paymentRequirementDTO.getMemberMDMCode());
        dto.setContractCode(paymentRequirementDTO.getContractCode());
        dto.setSaleRegion(paymentRequirementDTO.getSaleRegion());
        dto.setWarehouse(paymentRequirementDTO.getWarehouse());
        dto.setPickupPointOrgId(paymentRequirementDTO.getPickupPointOrgId());

        if (paymentRequirementDTO.getPayAmount() != null) {
            dto.setPayAmount(checkDecimalNumberAndFormat(paymentRequirementDTO.getPayAmount()));
        } else {
            throw new BizException(PayCode.PARAM_NULL, "PayAmount");
        }
        dto.setCurrency(paymentRequirementDTO.getCurrency());
        if (paymentRequirementDTO.getEndTime() != null) {
            if (paymentRequirementDTO.getEndTime().getTime() > System.currentTimeMillis()) {
                dto.setExpireTime(paymentRequirementDTO.getEndTime());
            } else {
                throw new BizException(PayCode.PAYMENT_TIMEOUT);
            }
        }
        if (CollectionUtils.isEmpty(paymentRequirementDTO.getDetailItemList())) {
            PaymentBillDetailDTO detailDTO = new PaymentBillDetailDTO();
            detailDTO.setPayeeMemberId(paymentRequirementDTO.getPayeeId());
            detailDTO.setPayeeMemberName(paymentRequirementDTO.getPayeeName());
            detailDTO.setSubject(paymentRequirementDTO.getPayType());
            detailDTO.setPayAmount(checkDecimalNumberAndFormat(dto.getPayAmount()));
            dto.setDetailDTOList(Lists.newArrayList(detailDTO));
        } else {
            Map<String, List<PaymentBillDetailDTO>> collectMap = paymentRequirementDTO.getDetailItemList().stream()
                    .filter(item -> BigDecimal.ZERO.compareTo(new BigDecimal(item.getPayAmount())) < 0)
                    .map(item -> {
                        PaymentBillDetailDTO detailDTO = new PaymentBillDetailDTO();
                        BeanUtils.copyProperties(item, detailDTO);
                        detailDTO.setPayAmount(checkDecimalNumberAndFormat(new BigDecimal(item.getPayAmount())));
                        return detailDTO;
                    })
                    .collect(Collectors.groupingBy(PaymentBillDetailDTO::getPayeeMemberId));
            // 去重
            List<PaymentBillDetailDTO> collect = Lists.newArrayList();
            for (Map.Entry<String, List<PaymentBillDetailDTO>> entry : collectMap.entrySet()) {
                PaymentBillDetailDTO detailDTO = entry.getValue().get(0);
                if (entry.getValue().size() > 1) {
                    BigDecimal reduce = entry.getValue().stream().map(PaymentBillDetailDTO::getPayAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    String subject = entry.getValue().stream()
                            .map(PaymentBillDetailDTO::getSubject)
                            .map(i -> {
                                PayDetailTypeEnum code = PayDetailTypeEnum.getByCode(i);
                                return code == null ? i : code.getMessage();
                            })
                            .filter(i -> !CsStringUtils.isEmpty(i))
                            .collect(Collectors.joining(","));
                    detailDTO.setPayAmount(reduce);
                    detailDTO.setSubject(subject);
                }
                collect.add(detailDTO);
            }

            dto.setDetailDTOList(collect);
            int maxAccountCount = 5;
            if (collect.size() > maxAccountCount) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "分账的子账户不可大于5个");
            }
            BigDecimal total = collect.stream().map(PaymentBillDetailDTO::getPayAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (total.compareTo(dto.getPayAmount()) != 0) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "支付单明细项金额汇总不等于支付单金额");
            }
        }
        return dto;
    }

    @Autowired
    private RestTemplate restTemplate;


    private PaymentBillDTO generatePaymentBillDTO(String bizId, String paymentBizCode, String operator) {
        PaymentRequirementDTO paymentRequirement = paymentIntegrationBiz.getPaymentRequirement(bizId, paymentBizCode, operator);
        if (paymentRequirement == null) {

            String result = restTemplate.postForObject("http://service-order/orderPayinfo/getOrderPayinfo?payinfoId=" + bizId, null, String.class);
            JSONObject jsonObject = JSON.parseObject(result);

            JSONObject payinfo = jsonObject.getJSONObject("data");
            paymentRequirement = new PaymentRequirementDTO();
            paymentRequirement.setPayerId(payinfo.getString("payerId"));
            paymentRequirement.setOrderId(payinfo.getString("objectId"));
            paymentRequirement.setBizId(payinfo.getString("payinfoId"));
            paymentRequirement.setBizCode(payinfo.getString("payinfoCode"));
            JSONArray detail = payinfo.getJSONArray("orderPayinfoDetail");
            List<PaymentRequirementDetailDTO> detailDTOS = getPaymentRequirementDetailDTOS(detail);
            paymentRequirement.setDetailItemList(detailDTOS);
            paymentRequirement.setPayAmount(payinfo.getBigDecimal("payAmount"));
            paymentRequirement.setCurrency(payinfo.getString("currency"));
            paymentRequirement.setSupportPayWay("offline");
            paymentRequirement.setWarehouse("仓库ID");
            paymentRequirement.setEndTime(payinfo.getDate("payDealline"));
            paymentRequirement.setPayType("商品款");
            JSONObject payee = detail.getJSONObject(0);
            paymentRequirement.setPayeeId(payee.getString("payeeId"));
            paymentRequirement.setPayeeName(payee.getString("payeeName"));

        }
        log.info("业务方原数据: {}", paymentRequirement);
        if (CsStringUtils.isNotBlank(paymentRequirement.getExceptionMsg())) {
            throw new BizException(PayCode.CUSTOM_ERROR, paymentRequirement.getExceptionMsg());
        }
        return this.convertPaymentRequirementDTO2PaymentBillDTO(paymentRequirement);
    }

    @NotNull
    private static List<PaymentRequirementDetailDTO> getPaymentRequirementDetailDTOS(JSONArray detail) {
        List<PaymentRequirementDetailDTO> detailDTOS = new ArrayList<>();
        for (int i = 0; i < detail.size(); i++) {
            JSONObject d = (JSONObject) detail.get(i);
            PaymentRequirementDetailDTO dto = new PaymentRequirementDetailDTO();
            dto.setPayAmount(d.getString("payAmount"));
            dto.setPayeeMemberId(d.getString("payeeId"));
            dto.setPayeeMemberName(d.getString("payeeName"));
            detailDTOS.add(dto);
        }
        return detailDTOS;
    }

    private PaymentBillDTO generatePaymentBillDTO(String bizId, String paymentBizCode, String operator, PaymentRequirementDTO paymentRequirement) {
        if (paymentRequirement == null) {
            paymentRequirement = paymentIntegrationBiz.getPaymentRequirement(bizId, paymentBizCode, operator);
            log.info("查询业务方原数据: {}", paymentRequirement);
        } else {
            log.info("业务方传过来的数据: {}", paymentRequirement);
        }
        if (paymentRequirement == null) {
            throw new BizException(PayCode.DATA_NOT_FOUND);
        }
        if (CsStringUtils.isNotBlank(paymentRequirement.getExceptionMsg())) {
            throw new BizException(PayCode.CUSTOM_ERROR, paymentRequirement.getExceptionMsg());
        }
        return this.convertPaymentRequirementDTO2PaymentBillDTO(paymentRequirement);
    }


    private boolean checkPaymentBillStatusByPayMethod(PaymentBillDTO currentPay, ChannelConfigDTO channelConfig,
                                                      PaymentBillDTO paymentBillDTO, boolean sameChannel, String operatorId) {
        log.info("历史支付单状态: {}", paymentBillDTO.getStatus());
        if (ChannelCodeEnum.OFFLINE.getCode().equals(paymentBillDTO.getChannelCode())
                && !PaymentStatusEnum.PAY_CLOSE.getCode().equals(paymentBillDTO.getStatus())
                && !PaymentStatusEnum.PAY_FAIL.getCode().equals(paymentBillDTO.getStatus())
                && !PaymentStatusEnum.PAY_SUCCESS.getCode().equals(paymentBillDTO.getStatus()) && !sameChannel) {
            log.info("线下支付, 状态：{}, 不允许更换支付方式", paymentBillDTO.getStatus());
            throw new BizException(PayCode.PAY_FAIL, "线下支付不允许更换支付方式");
        }
        if (PaymentStatusEnum.NEW_PAY.getCode().equals(paymentBillDTO.getStatus())) {
            throw new BizException(PayCode.PAY_REPEAT_CLICKS);
        } else if (PaymentStatusEnum.PAY_TIMEOUT.getCode().equals(paymentBillDTO.getStatus())) {
            if (currentPay.getExpireTime() == null) {
                return true;
            }
            log.info("支付已超时, 无法支付");
            throw new BizException(PayCode.PAYMENT_TIMEOUT);
        } else if (PaymentStatusEnum.PAY_CANCEL.getCode().equals(paymentBillDTO.getStatus())) {
            log.info("支付已取消, 无法支付");
            throw new BizException(PayCode.PAY_CANCEL);
        } else if (PaymentStatusEnum.PAY_SUCCESS.getCode().equals(paymentBillDTO.getStatus()) ||
                PaymentStatusEnum.PAY_FREEZE.getCode().equals(paymentBillDTO.getStatus())) {
            CodeAndMessage codeAndMessage = new CodeAndMessage();
            codeAndMessage.setCode(PaymentStatusEnum.PAY_SUCCESS.getCode());
            codeAndMessage.setMessage(PayCode.PAY_SUCCESS_ALREADY.getMsg(Locale.CHINA.toString(), true));
            // 再次通知订单
            notifyPaymentResult(paymentBillDTO, true, operatorId);
            throw new BizException(PayCode.UNDEFINED_ERROR, JSON.toJSONString(codeAndMessage));
        } else if (PaymentStatusEnum.PAY_FAIL.getCode().equals(paymentBillDTO.getStatus()) && sameChannel) {
            log.info("失败重试 ");
            if (!channelConfig.getAllowRetry()) {
                throw new BizException(PayCode.PAY_FAIL);
            }
        } else if (PaymentStatusEnum.PAY_ING.getCode().equals(paymentBillDTO.getStatus())) {
            PaymentBillDTO dto = null;
            try {
                dto = channelExecutor.searchPaymentBill(paymentBillDTO.getPaymentBillNo(), paymentBillDTO.getChannelCode(), operatorId);
            } catch (Exception e) {
                log.info("切换支付方式，重新支付时，向第三方查询上一次的支付单的状态时出错 {}", e.getMessage(), e);
            }
            log.info("历史支付单状态: 支付中, 向第三方查询支付状态: {},", dto);
            if (dto != null) {
                log.info("是否线下支付:{},支付状态:{}", ChannelCodeEnum.OFFLINE.getCode().equals(paymentBillDTO.getChannelCode()), dto.getStatus());
                boolean success = dto.getStatus().equals(PaymentStatusEnum.PAY_SUCCESS.getCode());
                if (success || dto.getStatus().equals(PaymentStatusEnum.PAY_FAIL.getCode())) {
                    paymentBillBiz.updatePaymentBill(dto, operatorId);
                }
                else if (ChannelCodeEnum.OFFLINE.getCode().equals(paymentBillDTO.getChannelCode())) {
                    throw new BizException(PayCode.PAYING, "重复支付");
                }
                //微信支付每次使用新的支付单
                else if (sameChannel && !ChannelCodeEnum.WEIXIN.getCode().equals(paymentBillDTO.getChannelCode())) {
                    log.info("1两次支付使用的同一种支付渠道, 沿用: {}", paymentBillDTO.getChannelCode());
                    return false;
                }
                if (success) {
                    throw new BizException(PayCode.PAY_SUCCESS_ALREADY);
                }
                //微信支付每次使用新的支付单 先判断，防止重复支付的问题
            } else if (System.currentTimeMillis() - paymentBillDTO.getCreateTime().getTime() < 30000) {
                //如果2个浏览器或者2个客户端同时支付，有一个是异步回调的情况，但是还没回调的时候，查询结果是查询不到的，需要多等等一下
                throw new BizException(PayCode.PAYING, "重复支付");
            } else if (sameChannel && !ChannelCodeEnum.WEIXIN.getCode().equals(paymentBillDTO.getChannelCode())) {
                log.info("2两次支付使用的同一种支付渠道, 沿用: {}", paymentBillDTO.getChannelCode());
                return false;
            }
        }
        return true;
    }

    private void notifyPaymentResult(PaymentBillDTO paymentBillDTO, PaymentRequestDTO paymentRequestDTO) {
        PaymentBillInfo info = new PaymentBillInfo();
        info.setBizId(paymentRequestDTO.getBizId());
        info.setPaymentBillId(paymentBillDTO.getPaymentBillId());
        info.setPaymentBillNo(paymentBillDTO.getPaymentBillNo());
        info.setPaymentBizCode(paymentRequestDTO.getPaymentBizCode());
        info.setChannel(paymentRequestDTO.getChannelCode());
        info.setRemarks(paymentRequestDTO.getRemarks());
        info.setOperator(paymentRequestDTO.getOperator());
        info.setOfflineAttachment(paymentRequestDTO.getOfflineAttachment());
        try {
            log.info("修改支付信息：{}", JSON.toJSONString(info));
            paymentIntegrationBiz.updatePaymentBillInfo(info);
        } catch (Exception e) {
            log.info("修改支付信息失败");
            PaymentBillDTO dto = new PaymentBillDTO();
            dto.setPaymentBillId(paymentBillDTO.getPaymentBillId());
            dto.setWarningCode(PaymentWarningCodeEnum.UPDATE_PAYBILLINFO_FAIL.getCode());
            paymentBillBiz.updatePaymentBill(dto, paymentRequestDTO.getOperator());
        }
    }

    private void notifyPaymentResult(PaymentBillDTO paymentBillDTO, boolean flag, String operator) {
        PaymentBillDTO update = new PaymentBillDTO();
        update.setPaymentBillId(paymentBillDTO.getPaymentBillId());
        try {
            PaymentNotifyDTO paymentNotifyDTO = new PaymentNotifyDTO();
            paymentNotifyDTO.setAmount(paymentBillDTO.getPayAmount());
            paymentNotifyDTO.setBizId(paymentBillDTO.getSrcBizNo());
            paymentNotifyDTO.setChannel(paymentBillDTO.getChannelCode());
            paymentNotifyDTO.setIfSuccess(flag);
            paymentNotifyDTO.setOperator(operator);
            paymentNotifyDTO.setPaymentBillId(paymentBillDTO.getPaymentBillId());
            paymentNotifyDTO.setPaymentBillNo(paymentBillDTO.getPaymentBillNo());
            paymentNotifyDTO.setPaymentBizCode(paymentBillDTO.getPaymentBizCode());
            paymentIntegrationBiz.paymentResultCallback(paymentNotifyDTO);
            log.info("回调支付接入方成功 paymentBillDTO: {}, flag: {}", paymentBillDTO, flag);
            update.setNotifyBusStatus(PaymentCallbackEnum.BIZ_CALL_SUCCESS.getCode());
            paymentBillBiz.updatePaymentBill(update, operator);
        } catch (Exception e) {
            log.error(" 回调 支付接入方失败 {} {}", e.getMessage(), e.getCause());
            update.setNotifyBusStatus(PaymentCallbackEnum.BIZ_CALL_FAILED.getCode());
            update.setWarningCode(PaymentWarningCodeEnum.UPDATE_PAYBILLINFO_FAIL.getCode());
            paymentBillBiz.updatePaymentBill(update, operator);
        }
    }

    private PaymentBillDTO createNewPaymentBillByPay(PaymentBillDTO paymentBillDTO, ChannelConfigDTO channelConfigDTO,
                                                     PaymentRequestDTO paymentRequestDTO) {
        BizConfigDTO bizConfigDTO = paymentIntegrationBiz.findByCode(paymentBillDTO.getPaymentBizCode());
        if (bizConfigDTO == null) {
            log.info("BizConfigDTO为空");
            throw new BizException(PayCode.DATA_NOT_FOUND);
        }
        paymentBillDTO.setChannelId(channelConfigDTO.getChannelId());
        paymentBillDTO.setChannelName(channelConfigDTO.getChannelName());
        paymentBillDTO.setChannelType(channelConfigDTO.getChannelType());
        paymentBillDTO.setChannelCode(channelConfigDTO.getChannelCode());
        paymentBillDTO.setStatus(PaymentStatusEnum.NEW_PAY.getCode());
        paymentBillDTO.setBizName(bizConfigDTO.getBizName());
        //设置支付来源端
        if (!CsStringUtils.isEmpty(paymentRequestDTO.getClientType())) {
            paymentBillDTO.setClientType(paymentRequestDTO.getClientType().toLowerCase());
        }
        //设置微信APP支付的来源方
        if (!CsStringUtils.isEmpty(paymentRequestDTO.getAppType())) {
            paymentBillDTO.setAppType(paymentRequestDTO.getAppType().toLowerCase());
        }
        Date expireTime = channelExecutor.checkTimeoutAndGetExpireTime(paymentBillDTO.getExpireTime(),
                channelConfigDTO.getPayTimeout());
        paymentBillDTO.setExpireTime(expireTime);
        paymentBillDTO.setRemarks(CsStringUtils.isBlank(paymentRequestDTO.getRemarks()) || "null".equals(paymentRequestDTO.getRemarks()) ? paymentRequestDTO.getBody() : paymentRequestDTO.getRemarks());
        paymentBillDTO.setOfflineAttachment(paymentRequestDTO.getOfflineAttachment());
        updateRemarks(paymentBillDTO);
        paymentBillBiz.create(paymentBillDTO, paymentRequestDTO.getOperator());

        // 微信、支付宝也由卖家代收物流费
        // 线下支付 and 授信支付 时，平台应收的资金给卖家，由卖家和平台线下结算
        // ERP支付也同上 2020.1.17  需求由紫云与海心确认
        List<PaymentBillDetailDTO> detailDTOList = paymentBillDTO.getDetailDTOList();
        String way = channelConfigDTO.getChannelCode();
        if ((ChannelCodeEnum.OFFLINE.getCode().equals(way) || ChannelCodeEnum.CREDIT.getCode().equals(way) || ChannelCodeEnum.ERP.getCode().equals(way) ||
                ChannelCodeEnum.WEIXIN.getCode().equals(way) || ChannelCodeEnum.ALIPAY.getCode().equals(way))
                && detailDTOList != null && detailDTOList.size() > 1) {
            Iterator<PaymentBillDetailDTO> it = detailDTOList.iterator();
            BigDecimal platformAmount = BigDecimal.ZERO;
            StringJoiner subjectStr = new StringJoiner(",");
            boolean hasGoodsSubject = false;
            boolean hasGoodsSupplementSubject = false;
            List<PaymentBillDetailDTO> removeList = Lists.newLinkedList();
            while (it.hasNext()) {
                PaymentBillDetailDTO next = it.next();
                String payeeId = next.getPayeeMemberId();
                String subject = next.getSubject().toLowerCase();
                String platformCarrierMemberId = MemberPlatform.PLATFORM_CARRIER_MEMBERID.getId();
                if (payeeId.equals(platformCarrierMemberId)) {
                    platformAmount = platformAmount.add(next.getPayAmount());
                    subjectStr.add(next.getSubject());
                    removeList.add(next);
                    it.remove();
                } else {
                    hasGoodsSubject = hasGoodsSubject || PayDetailTypeEnum.GOODS.getCode().equals(subject);
                    hasGoodsSupplementSubject = hasGoodsSubject || PayDetailTypeEnum.GOODS_SUPPLEMENT.getCode().equals(subject);
                }
            }
            for (PaymentBillDetailDTO payDetail : detailDTOList) {
                String subject = payDetail.getSubject().toLowerCase();
                if (hasGoodsSubject && PayDetailTypeEnum.GOODS.getCode().equals(subject)) {
                    subjectStr.add(subject);
                    BigDecimal payAdmount = payDetail.getPayAmount();
                    payAdmount = payAdmount.add(platformAmount);
                    payDetail.setPayAmount(payAdmount);
                    payDetail.setSubject(subjectStr.toString());
                    break;
                } else if (hasGoodsSupplementSubject && PayDetailTypeEnum.GOODS_SUPPLEMENT.getCode().equals(subject)) {
                    subjectStr.add(subject);
                    BigDecimal payAdmount = payDetail.getPayAmount();
                    payAdmount = payAdmount.add(platformAmount);
                    payDetail.setPayAmount(payAdmount);
                    payDetail.setSubject(subjectStr.toString());
                    break;
                }
            }
            if (!hasGoodsSupplementSubject && !hasGoodsSubject) {
                detailDTOList.addAll(removeList);
            }

        }

        try {
            if (!CollectionUtils.isEmpty(detailDTOList)) {
                for (PaymentBillDetailDTO detailDTO : detailDTOList) {
                    PaymentBillDetailDTO t = new PaymentBillDetailDTO();
                    BeanUtils.copyProperties(paymentBillDTO, t);
                    t.setPayeeMemberId(detailDTO.getPayeeMemberId());
                    t.setPayeeMemberName(detailDTO.getPayeeMemberName());
                    t.setPayAmount(detailDTO.getPayAmount());
                    t.setSubject(detailDTO.getSubject());
                    t.setActualPayAmount(BigDecimal.ZERO);

                    paymentBillDetailBiz.save(t, paymentRequestDTO.getOperator());
                    BeanUtils.copyProperties(t, detailDTO);
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            this.setPaymentBillFail(paymentBillDTO.getPaymentBillId(), paymentRequestDTO.getOperator());
            throw e;
        }
        return paymentBillDTO;
    }

    /**
     * uat测试问题15 支付详情乱码修改 即修改备注为：支付订单：uuid 的情况
     * 2020.5.21 修改结果改为与授权支付备注相同（如：订单款(订单物流款:0.10元,其它费用:222.00元,台班费:111.00元,订单货款:1.00元)）  是与强哥、媛媛商议结果
     */
    private void updateRemarks(PaymentBillDTO paymentBillDTO) {
        if (CsStringUtils.isBlank(paymentBillDTO.getRemarks()) || !paymentBillDTO.getRemarks().startsWith("支付订单") || CollectionUtils.isEmpty(paymentBillDTO.getDetailDTOList())) {
            return;
        }
        Map<String, BigDecimal> map = Maps.newHashMap();
        for (PaymentBillDetailDTO paymentBillDetailDTO : paymentBillDTO.getDetailDTOList()) {
            PayDetailTypeEnum payDetailTypeEnum = PayDetailTypeEnum.getByCodeOrMessage(paymentBillDetailDTO.getSubject());
            String key = payDetailTypeEnum == null ? paymentBillDetailDTO.getSubject() : payDetailTypeEnum.getMessage();
            if (CsStringUtils.isBlank(key)) {
                continue;
            }
            BigDecimal amount = paymentBillDetailDTO.getPayAmount() == null ? BigDecimal.ZERO : paymentBillDetailDTO.getPayAmount();
            amount = amount.add(map.getOrDefault(key, BigDecimal.ZERO));
            map.put(key, amount);
        }
        if (map.isEmpty()) {
            log.info("remarks map is empty");
            return;
        }
        StringBuilder stringBuilder = new StringBuilder("订单款(");
        for (Map.Entry<String, BigDecimal> entry : map.entrySet()) {
            stringBuilder.append(entry.getKey()).append(":").append(entry.getValue().setScale(2, RoundingMode.HALF_UP).doubleValue()).append("元,");
        }
        stringBuilder.deleteCharAt(stringBuilder.length() - 1);
        stringBuilder.append(")");
        String oldRemark = paymentBillDTO.getRemarks();
        paymentBillDTO.setRemarks(stringBuilder.toString());
        log.info("update remarks : {} -> {}", oldRemark, paymentBillDTO.getRemarks());
    }

    private void createRefundBillDTO(PaymentBillDTO paymentBillDTO, RefundBillDTO refundBillDTO,
                                     List<PaymentRequirementDetailDTO> detailDTOList, String operatorId) {
        refundBillDTO.setActualPayAmount(null);
        refundBillDTO.setWarningCode(null);
        refundBillDTO.setServiceFee(null);
        refundBillDTO.setOtherFee(null);
        refundBillDTO.setExtStatus(null);
        refundBillDTO.setRelatedPaymentBillNo(paymentBillDTO.getPaymentBillNo());
        refundBillDTO.setStatus(PaymentStatusEnum.NEW_PAY.getCode());
        refundBillDTO.setDetail(PaymentTypeEnum.REFUND.getMessage());

        List<RefundBillDetailDTO> list = Lists.newArrayList();
        for (PaymentRequirementDetailDTO detailDTO : detailDTOList) {
            RefundBillDetailDTO t = new RefundBillDetailDTO();
            BeanUtils.copyProperties(refundBillDTO, t);
            t.setPayeeMemberId(detailDTO.getPayeeMemberId());
            t.setPayeeMemberName(detailDTO.getPayeeMemberName());
            t.setPayAmount(checkDecimalNumberAndFormat(new BigDecimal(detailDTO.getPayAmount())));
            t.setActualPayAmount(BigDecimal.ZERO);
            t.setSubject(detailDTO.getSubject());
            List<PaymentBillDetailDTO> dtoList = paymentBillDTO.getDetailDTOList();
            if (!CollectionUtils.isEmpty(dtoList)) {
                for (PaymentBillDetailDTO dto : dtoList) {
                    if (dto.getPayeeMemberId().equals(t.getPayeeMemberId())) {
                        t.setRelatedPaymentBillDetailCode(dto.getPaymentBillDetailCode());
                    }
                }
            }
            list.add(t);
        }
        refundBillDTO.setDetailDTOList(list);
        refundBillBiz.create(refundBillDTO, operatorId);
    }

    private BigDecimal checkDecimalNumberAndFormat(BigDecimal bigDecimal) {
        int len;
        int num = 2;
        StringBuilder str = new StringBuilder(bigDecimal.stripTrailingZeros().toPlainString());
        int i = str.indexOf(".");
        if (i != -1) {
            len = str.substring(i + 1).length();
        } else {
            len = 0;
        }
        if (len > num) {
            throw new BizException(PayCode.AMOUNT_PRECISION_ERROR);
        }
        if (i == -1) {
            str.append(".");
        }
        int t = num - len;
        while (t-- > 0) {
            str.append("0");
        }
        return new BigDecimal(str.toString());
    }

    private PaymentCallbackResponseDTO createCallbackResponseByFail() {
        PaymentCallbackResponseDTO responseDTO = new PaymentCallbackResponseDTO();
        responseDTO.setStatus(OperatorStatusEnum.FAIL.getCode());
        responseDTO.setCode(PaymentCallbackEnum.BIZ_CALL_FAILED.getCode());
        return responseDTO;
    }

    private void setPaymentBillDTOTime(PaymentBillDTO paymentBillDTO) {
        // 修改完成时间
        Date date = new Date();
        if (PaymentStatusEnum.PAY_SUCCESS.getCode().equals(paymentBillDTO.getStatus())) {
            paymentBillDTO.setCompleteTime(date);
        }
        if (PaymentStatusEnum.PAY_CLOSE.getCode().equals(paymentBillDTO.getStatus())) {
            paymentBillDTO.setCloseTime(date);
        }
    }

    private void execCallbackUpdatePaymentBill(PaymentBillDTO responseDTO, PaymentBillDTO paymentBillDTO,
                                               String operatorId) {
        if (responseDTO != null) {
            if (CsStringUtils.isEmpty(responseDTO.getPaymentBillId())) {
                responseDTO.setPaymentBillId(paymentBillDTO.getPaymentBillId());
            }

            responseDTO.setCompleteTime(new Date());

            if (!paymentBillDTO.getStatus().equals(responseDTO.getStatus())) {
                this.setPaymentBillDTOTime(responseDTO);
            }
            responseDTO.setNotifyBusStatus(null);
            paymentBillBiz.updatePaymentBill(responseDTO, operatorId);
        }
    }

    private void setRedisStatus(String paymentBillNo, String status) {
        log.info("setRedisStatus : {} {} {}", PayRedisKey.PAY_BILL + paymentBillNo, 60 * 30L, status);
        redisService.setex(PayRedisKey.PAY_BILL + paymentBillNo, 60 * 30L, status);
    }

    private void checkPaymentCallbackStatus(PaymentBillDTO paymentBillDTO) {
        if (PaymentStatusEnum.PAY_FAIL.getCode().equals(paymentBillDTO.getStatus())) {
            paymentBillDTO.setWarningCode(PaymentWarningCodeEnum.STATUS_EXCEPTION.getCode() + ":FAIL");
        } else if (PaymentStatusEnum.PAY_TIMEOUT.getCode().equals(paymentBillDTO.getStatus())) {
            paymentBillDTO.setWarningCode(PaymentWarningCodeEnum.STATUS_EXCEPTION.getCode() + ":TIMEOUT");
        } else if (PaymentStatusEnum.PAY_CANCEL.getCode().equals(paymentBillDTO.getStatus())) {
            paymentBillDTO.setWarningCode(PaymentWarningCodeEnum.STATUS_EXCEPTION.getCode() + ":CANCEL");
        } else if (PaymentStatusEnum.PAY_CLOSE.getCode().equals(paymentBillDTO.getStatus())) {
            paymentBillDTO.setWarningCode(PaymentWarningCodeEnum.MAYBE_REPEATED_PAYMENT.getCode());
        }
    }

    private static void checkObjectNull(Object object, String msg) {
        if (object == null) {
            throw new BizException(PayCode.PARAM_NULL, msg);
        }
    }

    private void notifyPayment(PaymentBillDTO paymentBillDTO, boolean ifSuccess, String operatorId) {
        PaymentNotifyDTO paymentNotifyDTO = new PaymentNotifyDTO();
        paymentNotifyDTO.setAmount(paymentBillDTO.getPayAmount());
        paymentNotifyDTO.setBizId(paymentBillDTO.getSrcBizNo());
        paymentNotifyDTO.setChannel(paymentBillDTO.getChannelCode());
        paymentNotifyDTO.setIfSuccess(ifSuccess);
        paymentNotifyDTO.setOperator(operatorId);
        paymentNotifyDTO.setPaymentBillId(paymentBillDTO.getPaymentBillId());
        paymentNotifyDTO.setPaymentBillNo(paymentBillDTO.getPaymentBillNo());
        paymentNotifyDTO.setPaymentBizCode(paymentBillDTO.getPaymentBizCode());
        try {
            paymentIntegrationBiz.paymentResultCallback(paymentNotifyDTO);
            paymentBillDTO.setNotifyBusStatus(PaymentCallbackEnum.BIZ_CALL_SUCCESS.getCode());
        } catch (Exception e) {
            log.error(e.getMessage());
            paymentBillDTO.setNotifyBusStatus(PaymentCallbackEnum.BIZ_CALL_FAILED.getCode());
        }
    }

    @Override
    public List<KeyValueDTO> getPayType() {
        List<KeyValueDTO> list = new ArrayList<>();
        for (PaymentTypeEnum value : PaymentTypeEnum.values()) {
            KeyValueDTO keyValueDTO = new KeyValueDTO();
            keyValueDTO.setKey(value.getCode());
            keyValueDTO.setValue(value.getMessage());
            list.add(keyValueDTO);
        }
        return list;
    }

    @Override
    public boolean checkChannelSupportMultiplePay(String channelCode) {
        List<ChannelConfigDTO> configDTOList = channelConfigBiz.getPlatformAvailChannels();
        return configDTOList.stream()
                .filter(i -> i.getChannelCode().equalsIgnoreCase(channelCode)).findFirst()
                .map(ChannelConfigDTO::getAllowSplit).orElse(false);
    }

    @Override
    public SendVerificationCodeResponseDTO sendVerificationCode(SendVerificationCodeRequestDTO requestDTO) {
        SendVerificationCodeResponseDTO responseDTO = new SendVerificationCodeResponseDTO();
        MemberChannelDTO memberChannelDTO = checkAndGetMemberChannelInfo(requestDTO, responseDTO, true);
        ApplicationTextMsgDynamicCodeResponseDTO res = null;
        //如果没有错误
        if (memberChannelDTO != null) {
            res = channelExecutor.applicationTextMsgDynamicCode(memberChannelDTO.getChannelCode(), memberChannelDTO.getMemberChannelCode(), memberChannelDTO.getExtCustAcctId(), requestDTO.getTranType(), requestDTO.getTranAmt());
        }
        if (res == null || !"000000".equals(res.getTxnReturnCode())) {
            responseDTO.setErrorMessage(res == null ? "接口调用失败" : res.getTxnReturnMsg());
        } else {
            responseDTO.setSuccess(true);
            responseDTO.setErrorMessage(res.getTxnReturnMsg());
            responseDTO.setReceiveMobile(res.getReceiveMobile());
            responseDTO.setMessageOrderNo(res.getMessageOrderNo());
        }
        return responseDTO;
    }

    @Override
    public Boolean verificationCode(SendVerificationCodeRequestDTO requestDTO) {
        SendVerificationCodeResponseDTO responseDTO = new SendVerificationCodeResponseDTO();
        MemberChannelDTO memberChannelDTO = checkAndGetMemberChannelInfo(requestDTO, responseDTO, false);
        if (memberChannelDTO != null) {
            responseDTO = channelExecutor.verificationCode(memberChannelDTO.getChannelCode(), requestDTO);
            log.info("responseDTO:{}", responseDTO);
            if (responseDTO.isSuccess()) {
                return true;
            }
        }
        throw new BizException(BasicCode.CUSTOM_ERROR, responseDTO.getErrorMessage());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void processPayInfo(DriverPayInfo driverPayInfo, List<PlatformDayCarriage> platformDayCarriageList, QueryBatchTransResultResponseDTO res, Date now) {
        //找到有哪些日期的数据
        List<String> dayList = platformDayCarriageList.stream().map(PlatformDayCarriage::getDay).toList();
        //通过支付单和日期找到司机日期数据
        List<DriverDayCarriage> driverDayCarriageList = driverDayCarriageBiz.findByDayAndPayNumber(dayList, driverPayInfo.getPayinfoCode());
        //按明细流水号分组
        Map<String, List<DriverDayCarriage>> listMap = driverDayCarriageList.stream().collect(Collectors.groupingBy(DriverDayCarriage::getSn));

        Map<String, MemberChannelDTO> channelDTOMap = Maps.newHashMap();
        Set<String> failSet = com.google.common.collect.Sets.newHashSet();
        Set<String> successSet = com.google.common.collect.Sets.newHashSet();
        for (QueryBatchTransResultResponseItemDTO resItem : res.getList()) {
            List<DriverDayCarriage> driverDayCarriages = listMap.get(resItem.getSn());
            if (org.apache.commons.collections.CollectionUtils.isEmpty(driverDayCarriages)) {
                log.error("sn:{},没有找到数据", resItem.getSn());
                continue;
            }
            boolean success = resItem.getStatus() != null && resItem.getStatus() == 3;
            for (DriverDayCarriage driverDayCarriage : driverDayCarriages) {
                //更新司机按日汇总数据支付状态
                DriverDayCarriage driverDayCarriageUp = new DriverDayCarriage();
                driverDayCarriageUp.setDriverDayCarriageId(driverDayCarriage.getDriverDayCarriageId());
                driverDayCarriageUp.setUpdateUser(MemberPlatform.SYSTEM_OPERATOR.getId());
                driverDayCarriageUp.setUpdateTime(now);
                driverDayCarriageUp.setPayNumber(driverPayInfo.getPayinfoCode());
                driverDayCarriageUp.setPayStatus(success ? PaymentStatusEnum.PAY_SUCCESS.getCode() : PaymentStatusEnum.PAY_FAIL.getCode());
                driverDayCarriageUp.setActualPayTime(now);
                driverDayCarriageUp.setActualPayAmount(driverDayCarriage.getLogisticsAmount());
                driverDayCarriageUp.setTradeBillNo(resItem.getTransOrderNo());
                driverDayCarriageBiz.updateSelective(driverDayCarriageUp);

                //更新司机按日明细数据支付状态
                driverCarriageItemMapper.paySuccess(driverDayCarriageUp.getPayStatus(),
                        driverPayInfo.getPayinfoCode(),
                        resItem.getSn(),
                        MemberPlatform.SYSTEM_OPERATOR.getId(),
                        DateUtil.format(now, "yyyy-MM-dd HH:mm:ss"),
                        driverDayCarriage.getAccountId(),
                        driverDayCarriage.getDay());

                if (success) {
                    MemberChannelDTO memberChannelDTO = channelDTOMap.get(driverDayCarriage.getMemberId());
                    if (memberChannelDTO == null) {
                        List<MemberChannelDTO> channelDTOList = memberChannelBiz.findByMemberIdAndChannelCode(driverDayCarriage.getMemberId(), ChannelCodeEnum.GNETEPAY.getCode());
                        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(channelDTOList) && CsStringUtils.isNotBlank(channelDTOList.get(0).getExtCustAcctId())) {
                            channelDTOMap.put(driverDayCarriage.getMemberId(), channelDTOList.get(0));
                            memberChannelDTO = channelDTOList.get(0);
                        }
                    }
                    //更新收款人信息
                    if (memberChannelDTO != null &&
                            CsStringUtils.isNotBlank(memberChannelDTO.getExtCustAcctId()) &&
                            CsStringUtils.isNotBlank(memberChannelDTO.getGneteWalletName())) {
                        driverCarriageItemMapper.updateRecipientInfo(driverDayCarriage.getDay(), driverDayCarriageUp.getAccountId(), memberChannelDTO.getExtCustAcctId(), memberChannelDTO.getGneteWalletName());
                    }
                    //运单付款成功，发送消息给物流
                    driverCarriageItemBiz.notifyLogistics(driverDayCarriage.getDay(), driverDayCarriage.getAccountId());
                    //记录流水
                    driverPayInfo.setMemberId(driverDayCarriage.getMemberId());
                    driverPayInfo.setAccountName(driverDayCarriage.getRealName());
                    driverPayInfo.setPayinfoAmount(driverDayCarriage.getLogisticsAmount());
                    billLogsBiz.logsDriverPayInfo(driverPayInfo, MemberPlatform.SYSTEM_OPERATOR.getId());
                    successSet.add(driverDayCarriage.getDay());
                    //更新司机未来可提现收入
                    DriverSummaryInfo driverSummaryInfo = driverSummaryInfoBiz.findByAccountId(driverDayCarriage.getAccountId());
                    DriverSummaryInfo up = new DriverSummaryInfo();
                    up.setDriverSummaryInfoId(driverSummaryInfo.getDriverSummaryInfoId());
                    up.setWithdrawFutureAmount(ArithUtils.subtract(driverSummaryInfo.getWithdrawFutureAmount(), driverDayCarriage.getLogisticsAmount()));
                    driverSummaryInfoBiz.updateSelective(up);
                } else {
                    failSet.add(driverDayCarriage.getDay());
                }
            }
        }
        for (PlatformDayCarriage platformDayCarriage : platformDayCarriageList) {
            PlatformDayCarriage platformDayCarriageUp = new PlatformDayCarriage();
            platformDayCarriageUp.setPlatformDayCarriageId(platformDayCarriage.getPlatformDayCarriageId());
            platformDayCarriageUp.setPayStatus(PaymentStatusEnum.PAY_SUCCESS.getCode());
            if (failSet.contains(platformDayCarriage.getDay())) {
                if (successSet.contains(platformDayCarriage.getDay())) {
                    platformDayCarriageUp.setPayStatus(PaymentStatusEnum.PAY_PART_SUCCESS.getCode());
                } else {
                    platformDayCarriageUp.setPayStatus(PaymentStatusEnum.PAY_FAIL.getCode());
                }
            }
            platformDayCarriageUp.setActualPayAmount(platformDayCarriage.getLogisticsAmount());
            platformDayCarriageUp.setActualPayTime(now);
            platformDayCarriageUp.setUpdateTime(now);
            platformDayCarriageUp.setUpdateUser(MemberPlatform.SYSTEM_OPERATOR.getId());
            platformDayCarriageBiz.updateSelective(platformDayCarriageUp);
        }

        DriverPayInfo driverPayInfoUp = new DriverPayInfo();
        driverPayInfoUp.setDriverPayInfoId(driverPayInfo.getDriverPayInfoId());
        driverPayInfoUp.setPayinfoStatus(PaymentStatusEnum.PAY_SUCCESS.getCode());
        if (!failSet.isEmpty()) {
            if (!successSet.isEmpty()) {
                driverPayInfoUp.setPayinfoStatus(PaymentStatusEnum.PAY_PART_SUCCESS.getCode());
                driverPayInfoUp.setRemark("部分交易失败:" + String.join(",", failSet));
            } else {
                driverPayInfoUp.setPayinfoStatus(PaymentStatusEnum.PAY_FAIL.getCode());
                driverPayInfoUp.setRemark("交易全部失败:" + String.join(",", failSet));
            }
        }
        // TODO: 重构此方法以降低认知复杂度 (当前: 31, 目标: ≤15)
        // 建议: 1) 提取子方法 2) 使用早期返回 3) 简化条件表达式
        driverPayInfoUp.setUpdateTime(now);
        driverPayInfoUp.setUpdateUser(MemberPlatform.SYSTEM_OPERATOR.getId());
        driverPayInfoBiz.updateSelective(driverPayInfoUp);
    }

    private MemberChannelDTO checkAndGetMemberChannelInfo(SendVerificationCodeRequestDTO requestDTO, SendVerificationCodeResponseDTO responseDTO, boolean sendVerificationCode) {
        log.info("sendVerificationCodeRequestDTO sendVerificationCode:{},requestDTO:{}", sendVerificationCode, requestDTO);
        responseDTO.setSuccess(false);
        if (CsStringUtils.isBlank(requestDTO.getMemberId())) {
            responseDTO.setErrorMessage("当前会员id不可为空");
            return null;
        }
        if (sendVerificationCode && !"1".equals(requestDTO.getTranType()) && !"2".equals(requestDTO.getTranType())) {
            responseDTO.setErrorMessage("tranType不正确");
            return null;
        }
        MemberChannelDTO memberChannelDTO = null;
        if ("1".equals(requestDTO.getTranType())) {
            //提现
            if (CsStringUtils.isBlank(requestDTO.getTranAmt())) {
                responseDTO.setErrorMessage("提现金额不可为空");
                return null;
            }
            try {
                if (Double.parseDouble(requestDTO.getTranAmt()) < 0) {
                    responseDTO.setErrorMessage("提现金额不能小于0");
                    return null;
                }
            } catch (Exception e) {
                responseDTO.setErrorMessage("提现金额不正确");
                return null;
            }
            if (CsStringUtils.isBlank(requestDTO.getMemberChannelId())) {
                responseDTO.setErrorMessage("会员支付渠道id不可为空");
                return null;
            }
            memberChannelDTO = memberChannelBiz.findByMemberChannelId(requestDTO.getMemberChannelId());
            if (memberChannelDTO == null) {
                responseDTO.setErrorMessage("支付渠道没有找到");
            }
        } else if ("2".equals(requestDTO.getTranType())) {
            //支付
            if (CsStringUtils.isBlank(requestDTO.getPaymentBillId())) {
                responseDTO.setErrorMessage("支付单id不可为空");
                return null;
            }
            PaymentBillDTO paymentBill = paymentBillBiz.findByPaymentBillId(requestDTO.getPaymentBillId());
            if (paymentBill == null) {
                log.error("支付单id:{}对应支付单没找到", requestDTO.getPaymentBillId());
                responseDTO.setErrorMessage("支付单id不正确");
                return null;
            }
            requestDTO.setTranAmt(paymentBill.getPayAmount().toPlainString());
            memberChannelDTO = memberChannelBiz.findByMemberChannelId(requestDTO.getMemberChannelId());
            if (memberChannelDTO == null) {
                responseDTO.setErrorMessage("支付渠道没有找到");
                return null;
            }
            if (!CsStringUtils.equals(memberChannelDTO.getChannelCode(), paymentBill.getChannelCode()) ||
                    !CsStringUtils.equals(memberChannelDTO.getMemberId(), paymentBill.getPayerMemberId())) {
                responseDTO.setErrorMessage("支付渠道不正确");
                return null;
            }
        }
        if (!sendVerificationCode) {
            if (CsStringUtils.isBlank(requestDTO.getVerificationCode())) {
                responseDTO.setErrorMessage("回填验证码不可为空");
                return null;
            }
            if (CsStringUtils.isBlank(requestDTO.getOperationNo())) {
                responseDTO.setErrorMessage("操作记录流水编号不可为空");
                return null;
            }
            if (CsStringUtils.isBlank(requestDTO.getMessageOrderNo())) {
                responseDTO.setErrorMessage("短信指令号不可为空");
                return null;
            }
        }
        return memberChannelDTO;
    }

}