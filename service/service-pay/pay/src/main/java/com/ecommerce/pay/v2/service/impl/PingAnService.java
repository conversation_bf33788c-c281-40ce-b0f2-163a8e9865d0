package com.ecommerce.pay.v2.service.impl;

import com.ecommerce.pay.api.v2.dto.MemberChannelDTO;
import com.ecommerce.pay.api.v2.enums.ChannelCodeEnum;
import com.ecommerce.pay.api.v2.enums.ChannelPaymentTypeEnum;
import com.ecommerce.pay.v2.biz.IMemberChannelBiz;
import com.ecommerce.pay.v2.biz.IPingAnBiz;
import com.ecommerce.pay.v2.service.IPingAnService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class PingAnService implements IPingAnService
{
    @Autowired
    private IPingAnBiz pingAnBiz;

    @Autowired
    private IMemberChannelBiz memberChannelBiz;

    @Override
    public Map<String, String> clearingCorpInfoSendMsgCode(String memberId, String legalName, String legalCertificateCode, String clickTime, String remoteIp, String macAddress, String channel)
    {
        return pingAnBiz.clearingCorpInfoSendMsgCode(memberId, legalName, legalCertificateCode, clickTime, remoteIp, macAddress, channel);
    }

    @Override
    public Map<String, String> clearingCorpInfoCheckMsgCode(String memberId, String checkCode)
    {
        return pingAnBiz.clearingCorpInfoCheckMsgCode(memberId, checkCode);
    }

    @Override
    public Map<String, Object> registerBehaviorRecordInfo(String memberId, String functionFlag, String clickTime, String remoteIp, String macAddress, String channel)
    {
        return pingAnBiz.registerBehaviorRecordInfo(memberId, functionFlag, clickTime, remoteIp, macAddress, channel);
    }

    @Override
    public MemberChannelDTO getSellerChannelInfo(String memberId)
    {
        return memberChannelBiz.findByMemberIdAndChannelCode(memberId, ChannelCodeEnum.PINGANJZ.getCode(), ChannelPaymentTypeEnum.PAYEE);
    }
}
