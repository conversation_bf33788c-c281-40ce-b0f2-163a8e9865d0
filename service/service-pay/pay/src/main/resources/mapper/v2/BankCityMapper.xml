<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.pay.v2.dao.mapper.BankCityMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.pay.v2.dao.vo.BankCity">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="city_areacode" jdbcType="VARCHAR" property="cityAreacode" />
    <result column="city_areaname" jdbcType="VARCHAR" property="cityAreaname" />
    <result column="city_areatype" jdbcType="VARCHAR" property="cityAreatype" />
    <result column="city_nodecode" jdbcType="VARCHAR" property="cityNodecode" />
    <result column="city_topareacode1" jdbcType="VARCHAR" property="cityTopareacode1" />
    <result column="city_topareacode2" jdbcType="VARCHAR" property="cityTopareacode2" />
    <result column="city_topareacode3" jdbcType="VARCHAR" property="cityTopareacode3" />
    <result column="city_oraareacode" jdbcType="VARCHAR" property="cityOraareacode" />
  </resultMap>
</mapper>