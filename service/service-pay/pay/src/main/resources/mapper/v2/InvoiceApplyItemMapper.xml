<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.pay.v2.dao.mapper.InvoiceApplyItemMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.pay.v2.dao.vo.InvoiceApplyItem">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="apply_item_id" jdbcType="VARCHAR" property="applyItemId" />
    <result column="apply_id" jdbcType="VARCHAR" property="applyId" />
    <result column="biz_no" jdbcType="VARCHAR" property="bizNo" />
    <result column="biz_type" jdbcType="VARCHAR" property="bizType" />
    <result column="item_code" jdbcType="VARCHAR" property="itemCode" />
    <result column="item_name" jdbcType="VARCHAR" property="itemName" />
    <result column="item_num" jdbcType="DECIMAL" property="itemNum" />
    <result column="item_price" jdbcType="DECIMAL" property="itemPrice" />
    <result column="item_unit" jdbcType="VARCHAR" property="itemUnit" />
    <result column="item_mode" jdbcType="VARCHAR" property="itemMode" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />
    <result column="logistics_amount" jdbcType="DECIMAL" property="logisticsAmount" />
    <result column="open_flag" jdbcType="TINYINT" property="openFlag" />
    <result column="del_flg" jdbcType="TINYINT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="extra_content" jdbcType="LONGVARCHAR" property="extraContent" />
  </resultMap>
</mapper>