<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.pay.v2.dao.mapper.DriverCarriageItemMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.pay.v2.dao.vo.DriverCarriageItem">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="driver_carriage_item_id" jdbcType="VARCHAR" property="driverCarriageItemId" />
    <result column="member_id" jdbcType="VARCHAR" property="memberId" />
    <result column="account_id" jdbcType="VARCHAR" property="accountId" />
    <result column="account_code" jdbcType="VARCHAR" property="accountCode" />
    <result column="real_name" jdbcType="VARCHAR" property="realName" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="day" jdbcType="CHAR" property="day" />
    <result column="waybill_id" jdbcType="VARCHAR" property="waybillId" />
    <result column="waybill_num" jdbcType="VARCHAR" property="waybillNum" />
    <result column="distance" jdbcType="DECIMAL" property="distance" />
    <result column="quantity" jdbcType="DECIMAL" property="quantity" />
    <result column="logistics_amount" jdbcType="DECIMAL" property="logisticsAmount" />
    <result column="sign_time" jdbcType="TIMESTAMP" property="signTime" />
    <result column="withdraw_future_date" jdbcType="TIMESTAMP" property="withdrawFutureDate" />
    <result column="warehouse_id" jdbcType="VARCHAR" property="warehouseId" />
    <result column="warehouse_name" jdbcType="VARCHAR" property="warehouseName" />
    <result column="receive_address" jdbcType="VARCHAR" property="receiveAddress" />
    <result column="actual_pay_amount" jdbcType="DECIMAL" property="actualPayAmount" />
    <result column="pay_status" jdbcType="VARCHAR" property="payStatus" />
    <result column="scheduled_pay_time" jdbcType="TIMESTAMP" property="scheduledPayTime" />
    <result column="actual_pay_time" jdbcType="TIMESTAMP" property="actualPayTime" />
    <result column="pay_number" jdbcType="VARCHAR" property="payNumber" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>