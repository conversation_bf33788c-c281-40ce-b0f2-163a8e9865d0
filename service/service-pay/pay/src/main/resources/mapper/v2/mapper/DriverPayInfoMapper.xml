<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.pay.v2.dao.mapper.DriverPayInfoMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.pay.v2.dao.vo.DriverPayInfo">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="driver_payinfo_id" jdbcType="VARCHAR" property="driverPayInfoId" />
    <result column="origin_driver_payinfo_id" jdbcType="VARCHAR" property="originDriverPayInfoId" />
    <result column="member_id" jdbcType="VARCHAR" property="memberId" />
    <result column="account_id" jdbcType="VARCHAR" property="accountId" />
    <result column="account_code" jdbcType="VARCHAR" property="accountCode" />
    <result column="account_name" jdbcType="VARCHAR" property="accountName" />
    <result column="real_name" jdbcType="VARCHAR" property="realName" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="payinfo_type" jdbcType="VARCHAR" property="payinfoType" />
    <result column="operate_time" jdbcType="TIMESTAMP" property="operateTime" />
    <result column="receipt_time" jdbcType="TIMESTAMP" property="receiptTime" />
    <result column="pay_time" jdbcType="TIMESTAMP" property="payTime" />
    <result column="expire_time" jdbcType="TIMESTAMP" property="expireTime" />
    <result column="day" jdbcType="CHAR" property="day" />
    <result column="payinfo_code" jdbcType="VARCHAR" property="payinfoCode" />
    <result column="payinfo_amount" jdbcType="DECIMAL" property="payinfoAmount" />
    <result column="payinfo_status" jdbcType="VARCHAR" property="payinfoStatus" />
    <result column="trade_bill_no" jdbcType="VARCHAR" property="tradeBillNo" />
    <result column="refunded" jdbcType="BIT" property="refunded" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>