<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.pay.v2.dao.mapper.DriverSummaryInfoMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.pay.v2.dao.vo.DriverSummaryInfo">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="driver_summary_info_id" jdbcType="VARCHAR" property="driverSummaryInfoId" />
    <result column="member_id" jdbcType="VARCHAR" property="memberId" />
    <result column="account_id" jdbcType="VARCHAR" property="accountId" />
    <result column="account_code" jdbcType="VARCHAR" property="accountCode" />
    <result column="real_name" jdbcType="VARCHAR" property="realName" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="wallet_id" jdbcType="VARCHAR" property="walletId" />
    <result column="wallet_open_flg" jdbcType="BIT" property="walletOpenFlg" />
    <result column="deposit_pay_status" jdbcType="BIT" property="depositPayStatus" />
    <result column="deposit_amount" jdbcType="DECIMAL" property="depositAmount" />
    <result column="refunded" jdbcType="BIT" property="refunded" />
    <result column="bank_card" jdbcType="VARCHAR" property="bankCard" />
    <result column="waybill_count" jdbcType="INTEGER" property="waybillCount" />
    <result column="distance" jdbcType="DECIMAL" property="distance" />
    <result column="quantity" jdbcType="DECIMAL" property="quantity" />
    <result column="income" jdbcType="DECIMAL" property="income" />
    <result column="withdraw" jdbcType="DECIMAL" property="withdraw" />
    <result column="withdraw_allow_amount" jdbcType="DECIMAL" property="withdrawAllowAmount" />
    <result column="withdraw_future_amount" jdbcType="DECIMAL" property="withdrawFutureAmount" />
    <result column="deposit_payinfo_id" jdbcType="VARCHAR" property="depositPayInfoId" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>