<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.pay.v2.dao.mapper.GneteBankInfoMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.pay.v2.dao.vo.GneteBankInfo">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="province_name" jdbcType="VARCHAR" property="provinceName" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="district_name" jdbcType="VARCHAR" property="districtName" />
    <result column="district_code" jdbcType="VARCHAR" property="districtCode" />
    <result column="bank_cls_code" jdbcType="INTEGER" property="bankClsCode" />
    <result column="bank_name" jdbcType="VARCHAR" property="bankName" />
    <result column="bank_no" jdbcType="VARCHAR" property="bankNo" />
    <result column="bank_detail_name" jdbcType="VARCHAR" property="bankDetailName" />
    <result column="effective_date" jdbcType="VARCHAR" property="effectiveDate" />
    <result column="expiry_date" jdbcType="VARCHAR" property="expiryDate" />
  </resultMap>
</mapper>