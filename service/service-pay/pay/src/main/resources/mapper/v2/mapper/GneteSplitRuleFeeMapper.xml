<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.pay.v2.dao.mapper.GneteSplitRuleFeeMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.pay.v2.dao.vo.GneteSplitRuleFee">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="profit_sharing_rule_id" jdbcType="VARCHAR" property="profitSharingRuleId" />
    <result column="profit_sharing_wallet_id" jdbcType="VARCHAR" property="profitSharingWalletId" />
    <result column="fee_rate" jdbcType="BIGINT" property="feeRate" />
    <result column="rate_min_fee_amt" jdbcType="BIGINT" property="rateMinFeeAmt" />
    <result column="rate_max_fee_amt" jdbcType="BIGINT" property="rateMaxFeeAmt" />
    <result column="fixed_sett_amt" jdbcType="BIGINT" property="fixedSettAmt" />
    <result column="fee_rule_type" jdbcType="VARCHAR" property="feeRuleType" />
  </resultMap>
</mapper>