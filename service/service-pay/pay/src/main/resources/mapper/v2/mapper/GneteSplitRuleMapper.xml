<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.pay.v2.dao.mapper.GneteSplitRuleMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.pay.v2.dao.vo.GneteSplitRule">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="mer_no" jdbcType="VARCHAR" property="merNo" />
    <result column="mer_name" jdbcType="VARCHAR" property="merName" />
    <result column="platform_walletId" jdbcType="VARCHAR" property="platformWalletid" />
    <result column="biz_type" jdbcType="VARCHAR" property="bizType" />
    <result column="start_amt" jdbcType="BIGINT" property="startAmt" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="approve_status" jdbcType="VARCHAR" property="approveStatus" />
    <result column="sett_cyc" jdbcType="VARCHAR" property="settCyc" />
    <result column="is_instr" jdbcType="VARCHAR" property="isInstr" />
  </resultMap>
</mapper>