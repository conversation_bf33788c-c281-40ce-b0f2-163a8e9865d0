<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.pay.v2.dao.mapper.GneteSplitRuleSettMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.pay.v2.dao.vo.GneteSplitRuleSett">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="profit_sharing_rule_id" jdbcType="VARCHAR" property="profitSharingRuleId" />
    <result column="is_sett_wallet_id" jdbcType="BIT" property="isSettWalletId" />
    <result column="profit_sharing_wallet_id" jdbcType="VARCHAR" property="profitSharingWalletId" />
    <result column="sett_rate" jdbcType="BIGINT" property="settRate" />
    <result column="rate_min_sett_amt" jdbcType="BIGINT" property="rateMinSettAmt" />
    <result column="rate_max_sett_amt" jdbcType="BIGINT" property="rateMaxSettAmt" />
    <result column="fixed_sett_amt" jdbcType="BIGINT" property="fixedSettAmt" />
    <result column="sett_rule_type" jdbcType="VARCHAR" property="settRuleType" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
  </resultMap>
</mapper>