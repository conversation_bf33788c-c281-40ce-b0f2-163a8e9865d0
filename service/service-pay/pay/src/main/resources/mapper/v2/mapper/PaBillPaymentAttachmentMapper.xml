<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.pay.v2.dao.mapper.PaBillPaymentAttachmentMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.pay.v2.dao.vo.PaBillPaymentAttachment">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="pa_bill_payment_attachment_id" jdbcType="VARCHAR" property="paBillPaymentAttachmentId" />
    <result column="payment_bill_id" jdbcType="VARCHAR" property="paymentBillId" />
    <result column="payment_bill_no" jdbcType="VARCHAR" property="paymentBillNo" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>