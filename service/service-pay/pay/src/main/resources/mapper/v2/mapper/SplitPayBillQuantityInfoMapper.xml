<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecommerce.pay.v2.dao.mapper.SplitPayBillQuantityInfoMapper">
  <resultMap id="BaseResultMap" type="com.ecommerce.pay.v2.dao.vo.SplitPayBillQuantityInfo">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="pa_bill_split_pay_quantity_info_id" jdbcType="VARCHAR" property="paBillSplitPayQuantityInfoId" />
    <result column="split_pay_bill_id" jdbcType="VARCHAR" property="splitPayBillId" />
    <result column="split_pay_bill_no" jdbcType="VARCHAR" property="splitPayBillNo" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="units_name" jdbcType="VARCHAR" property="unitsName" />
    <result column="item_send_quantity" jdbcType="DECIMAL" property="itemSendQuantity" />
    <result column="item_send_incremental" jdbcType="DECIMAL" property="itemSendIncremental" />
    <result column="del_flg" jdbcType="BIT" property="delFlg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>