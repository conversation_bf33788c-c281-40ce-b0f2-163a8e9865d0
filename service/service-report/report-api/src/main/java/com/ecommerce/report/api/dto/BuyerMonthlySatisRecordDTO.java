
package com.ecommerce.report.api.dto;

import java.math.BigDecimal;
import lombok.Data;

@Data
public class BuyerMonthlySatisRecordDTO {
    private String buyerName;
    private String sellerName;
    private String provinceName;
    private String pack;
    private Integer actualOrderCount = 0;
    private BigDecimal actualOrderQuantity = BigDecimal.ZERO;
    private BigDecimal actualSendQuantity = BigDecimal.ZERO;
    private Integer waybillSendCount = 0;
    private BigDecimal actualAmount = BigDecimal.ZERO;
}

