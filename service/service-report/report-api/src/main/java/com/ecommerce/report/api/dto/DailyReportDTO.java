
package com.ecommerce.report.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(name="温馨日报实体")
public class DailyReportDTO {
    @Schema(description="年份")
    private String year;
    @Schema(description="月份")
    private String month;
    @Schema(description="日期")
    private String day;
    @Schema(description="星期")
    private String week;
    @Schema(description="广东订单总数")
    private BigDecimal gdOrderCount = BigDecimal.ZERO;
    @Schema(description="广东发货量")
    private BigDecimal gdSendQuantity = BigDecimal.ZERO;
    @Schema(description="广东散装订单总数")
    private BigDecimal gdBulkOrderCount = BigDecimal.ZERO;
    @Schema(description="广东散装发货量")
    private BigDecimal gdBulkSendQuantity = BigDecimal.ZERO;
    @Schema(description="广东袋装订单总数")
    private BigDecimal gdBagOrderCount = BigDecimal.ZERO;
    @Schema(description="广东袋装发货量")
    private BigDecimal gdBagSendQuantity = BigDecimal.ZERO;
    @Schema(description="广东盈信订单总数")
    private BigDecimal gdYXOrderCount = BigDecimal.ZERO;
    @Schema(description="广东盈信发货量")
    private BigDecimal gdYXSendQuantity = BigDecimal.ZERO;
    @Schema(description="广西订单总数")
    private BigDecimal gxOrderCount = BigDecimal.ZERO;
    @Schema(description="广西发货量")
    private BigDecimal gxSendQuantity = BigDecimal.ZERO;
    @Schema(description="广西散装订单总数")
    private BigDecimal gxBulkOrderCount = BigDecimal.ZERO;
    @Schema(description="广西散装发货量")
    private BigDecimal gxBulkSendQuantity = BigDecimal.ZERO;
    @Schema(description="广西袋装订单总数")
    private BigDecimal gxBagOrderCount = BigDecimal.ZERO;
    @Schema(description="广西袋装发货量")
    private BigDecimal gxBagSendQuantity = BigDecimal.ZERO;
    @Schema(description="广西恒亮订单总数")
    private BigDecimal gxHLOrderCount = BigDecimal.ZERO;
    @Schema(description="广西恒亮发货量")
    private BigDecimal gxHLSendQuantity = BigDecimal.ZERO;
    @Schema(description="日发货笔数")
    private Integer daySendCount = 0;
    @Schema(description="日发货量")
    private BigDecimal daySendQuantity = BigDecimal.ZERO;
    @Schema(description="月累计发货量")
    private BigDecimal monthSendQuantity = BigDecimal.ZERO;
    @Schema(description="袋装月累计发货量")
    private BigDecimal monthBagSendQuantity = BigDecimal.ZERO;
    @Schema(description="散装月累计发货量")
    private BigDecimal monthBulkSendQuantity = BigDecimal.ZERO;
    @Schema(description="年累计发货量")
    private BigDecimal yearSendQuantity = BigDecimal.ZERO;
    @Schema(description="袋装年累计发货量")
    private BigDecimal yearBagSendQuantity = BigDecimal.ZERO;
    @Schema(description="散装年累计发货量")
    private BigDecimal yearBulkSendQuantity = BigDecimal.ZERO;
}

