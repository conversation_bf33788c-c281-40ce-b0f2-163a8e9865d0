
package com.ecommerce.report.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(name="温馨日报数据列表实体")
public class DailyReportListDTO {
    @Schema(description="买家名称")
    private String buyerName;
    @Schema(description="卖家名称")
    private String sellerId;
    @Schema(description="收货地址省")
    private String provinceName;
    @Schema(description="包装")
    private String pack;
    @Schema(description="实际订单数")
    private BigDecimal orderCount;
    @Schema(description="实际提货量")
    private BigDecimal sendQuantity;
}

