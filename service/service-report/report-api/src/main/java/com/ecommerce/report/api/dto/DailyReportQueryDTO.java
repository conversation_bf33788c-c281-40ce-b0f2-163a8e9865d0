
package com.ecommerce.report.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name="温馨日报查询实体")
public class DailyReportQueryDTO {
    @Schema(description="查询日期 yyyy-MM-dd")
    private String date;
    @Schema(description="日期年份第一天")
    private String firstYearDate;
    @Schema(description="省编码")
    private String provinceCode;
    @Schema(description="日期月份第一天")
    private String firstMonthDate;
}

