
package com.ecommerce.report.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;
import lombok.Data;

@Data
@Schema(name="惠小车运营数据")
public class HxcBusinessDTO {
    @Schema(description="创建日期")
    private String createDateStr;
    @Schema(description="总放单数")
    private Integer totalPublishBillCount = 0;
    @Schema(description="总抢单数")
    private Integer totalSnatchBillCount = 0;
    @Schema(description="关闭运单数")
    private Integer closeBillCount = 0;
    @Schema(description="已结算")
    private Integer settledBillCount = 0;
    @Schema(description="未结算")
    private Integer unsettledBillCount = 0;
    @Schema(description="未完成")
    private Integer incompleteBillCount = 0;
    @Schema(description="抢单项")
    List<HxcBusinessSnatchItem> snatchItemList;
}

