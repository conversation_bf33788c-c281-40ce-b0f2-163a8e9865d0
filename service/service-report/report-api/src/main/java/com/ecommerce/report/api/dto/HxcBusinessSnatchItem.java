
package com.ecommerce.report.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name="惠小车运营数据抢单项")
public class HxcBusinessSnatchItem {
    @Schema(description="抢单人")
    private String snatchUserName;
    @Schema(description="抢单数")
    private Integer snatchBillCount = 0;

    public String getSnatchUserName() {
        return this.snatchUserName;
    }

    public Integer getSnatchBillCount() {
        return this.snatchBillCount;
    }

    public void setSnatchUserName(String snatchUserName) {
        this.snatchUserName = snatchUserName;
    }

    public void setSnatchBillCount(Integer snatchBillCount) {
        this.snatchBillCount = snatchBillCount;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof HxcBusinessSnatchItem)) {
            return false;
        }
        HxcBusinessSnatchItem other = (HxcBusinessSnatchItem)o;
        if (!other.canEqual(this)) {
            return false;
        }
        String this$snatchUserName = this.getSnatchUserName();
        String other$snatchUserName = other.getSnatchUserName();
        if (this$snatchUserName == null ? other$snatchUserName != null : !this$snatchUserName.equals(other$snatchUserName)) {
            return false;
        }
        Integer this$snatchBillCount = this.getSnatchBillCount();
        Integer other$snatchBillCount = other.getSnatchBillCount();
        return !(this$snatchBillCount == null ? other$snatchBillCount != null : !((Object)this$snatchBillCount).equals(other$snatchBillCount));
    }

    protected boolean canEqual(Object other) {
        return other instanceof HxcBusinessSnatchItem;
    }

    public int hashCode() {
        int result = 1;
        String $snatchUserName = this.getSnatchUserName();
        result = result * 59 + ($snatchUserName == null ? 43 : $snatchUserName.hashCode());
        Integer $snatchBillCount = this.getSnatchBillCount();
        result = result * 59 + ($snatchBillCount == null ? 43 : ((Object)$snatchBillCount).hashCode());
        return result;
    }

    public String toString() {
        return "HxcBusinessSnatchItem(snatchUserName=" + this.getSnatchUserName() + ", snatchBillCount=" + this.getSnatchBillCount() + ")";
    }
}

