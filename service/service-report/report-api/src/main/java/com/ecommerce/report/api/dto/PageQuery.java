
package com.ecommerce.report.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Map;

@Data
@Schema(name="分页查询对象")
public class PageQuery<T> {
    @Schema(description="当前页数", required=false, example="1")
    private Integer pageNum;
    @Schema(description="分页大小", required=false, example="20")
    private Integer pageSize;
    @Schema(description="查询参数对象", required=false)
    private T queryDTO;
    @Schema(description="排序字段映射表")
    private Map<String, String> orderMap;


}

