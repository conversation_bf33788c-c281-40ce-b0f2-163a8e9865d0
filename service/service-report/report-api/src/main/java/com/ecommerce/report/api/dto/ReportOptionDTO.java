
package com.ecommerce.report.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;
import lombok.Data;

@Data
@Schema(name="报表下拉列表项")
public class ReportOptionDTO {
    @Schema(description="报表编码")
    private String reportCode;
    @Schema(description="报表名")
    private String reportName;
    @Schema(description="报表简介")
    private String reportIntro;
    @Schema(description="报表关联角色集合")
    private List<String> roleList;
}

