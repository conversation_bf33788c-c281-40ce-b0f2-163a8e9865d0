
package com.ecommerce.report.api.dto;

import com.ecommerce.common.enums.AppNames;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;
import lombok.Data;

@Data
@Schema(name="运单列表查询对象")
public class ReportShipBillQueryDTO {
    @Schema(description="承运商ID")
    private String carrierId;
    @Schema(description="运单号")
    private String waybillNum;
    @Schema(description="运输工具类型")
    private String transportType;
    @Schema(description="运力名称")
    private String transportToolName;
    @Schema(description="装货地址ID")
    private String warehouseId;
    @Schema(description="装货地址省编码")
    private String warehouseProvinceCode;
    @Schema(description="装货地址市编码")
    private String warehouseCityCode;
    @Schema(description="装货地址区编码")
    private String warehouseDistrictCode;
    @Schema(description="运单状态")
    private String status;
    @Schema(description="运单状态列表")
    private List<String> statusList;
    @Schema(description="运输品类ID")
    private String transportCategoryId;
    @Schema(description="区域权限列表")
    private List<String> regionCodeList;
    @Schema(description="erp支付账户编码集(买家权限控制用)")
    private List<String> mdmCodeList;
    @Schema(description="发起查询的应用名")
    private String queryAppName = AppNames.WEB_SERVICE_BUYER.getCode();
    @Schema(description="操作人")
    private String operatorId;
    @Schema(description="委托单号")
    private String deliveryBillNum;
    @Schema(description="买家ID")
    private String buyerId;
    @Schema(description="卖家ID")
    private String sellerId;
    @Schema(description="收货地址ID")
    private String receiverAddressId;
    @Schema(description="收货地址省编码")
    private String receiveProvinceCode;
    @Schema(description="收货地址市编码")
    private String receiveCityCode;
    @Schema(description="收货地址区编码")
    private String receiveDistrictCode;
    @Schema(description="配送开始时间")
    private String deliveryTimeStart;
    @Schema(description="配送结束时间")
    private String deliveryTimeEnd;
    @Schema(description="开始出站时间时间")
    private String beginLeaveWarehouseTime;
    @Schema(description="结束出站时间")
    private String endLeaveWarehouseTime;
    @Schema(description="开始创建时间")
    private String beginCreateTime;
    @Schema(description="结束创建时间")
    private String endCreateTime;
    @Schema(description="主运单ID列表")
    private List<String> parentIdList;
    @Schema(description="销售区域")
    private List<String> saleRegionIdList;
    @Schema(description="业务员ID")
    private String salesmanId;
    @Schema(description="商品名称")
    private String goodsName;
    @Schema(description="地址详情")
    private String receiveAddress;
    @Schema(description="选择的列")
    private List<String> selectHeaders;
    @Schema(description="是否要隐藏价格", hidden=true)
    private Boolean needHidePrice;
}

