
package com.ecommerce.report.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name="报表值集项")
public class ReportValuesetItem {
    @Schema(description="下拉表项编码")
    private String itemCode;
    @Schema(description="下拉表项描述")
    private String itemLabel;

    public String getItemCode() {
        return this.itemCode;
    }

    public String getItemLabel() {
        return this.itemLabel;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public void setItemLabel(String itemLabel) {
        this.itemLabel = itemLabel;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof ReportValuesetItem)) {
            return false;
        }
        ReportValuesetItem other = (ReportValuesetItem)o;
        if (!other.canEqual(this)) {
            return false;
        }
        String this$itemCode = this.getItemCode();
        String other$itemCode = other.getItemCode();
        if (this$itemCode == null ? other$itemCode != null : !this$itemCode.equals(other$itemCode)) {
            return false;
        }
        String this$itemLabel = this.getItemLabel();
        String other$itemLabel = other.getItemLabel();
        return !(this$itemLabel == null ? other$itemLabel != null : !this$itemLabel.equals(other$itemLabel));
    }

    protected boolean canEqual(Object other) {
        return other instanceof ReportValuesetItem;
    }

    public int hashCode() {
        int result = 1;
        String $itemCode = this.getItemCode();
        result = result * 59 + ($itemCode == null ? 43 : $itemCode.hashCode());
        String $itemLabel = this.getItemLabel();
        result = result * 59 + ($itemLabel == null ? 43 : $itemLabel.hashCode());
        return result;
    }

    public String toString() {
        return "ReportValuesetItem(itemCode=" + this.getItemCode() + ", itemLabel=" + this.getItemLabel() + ")";
    }
}

