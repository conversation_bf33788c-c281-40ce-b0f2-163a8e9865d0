
package com.ecommerce.report.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;
import lombok.Data;

@Data
@Schema(name="已发布运单查询对象")
public class ReportWaybillQueryDTO {
    @Schema(description="运单状态", required=false, example="1")
    private String status;
    @Schema(description="运单状态", required=false, example="1")
    private List<String> statusList;
    @Schema(description="运单类型", required=false, example="1")
    private String type;
    @Schema(description="仓库Id", required=false, example="11111")
    private String warehouseId;
    @Schema(description="开始配送时间")
    private String beginDeliveryTime;
    @Schema(description="结束配送时间")
    private String endDeliveryTime;
    @Schema(description="开始出站时间时间")
    private String beginLeaveWarehouseTime;
    @Schema(description="结束出站时间")
    private String endLeaveWarehouseTime;
    @Schema(description="提货单号", required=false)
    private String pickingBillNum;
    @Schema(description="发货单号", required=false)
    private String deliverySheetNum;
    @Schema(description="运单号", required=false)
    private String waybillNum;
    @Schema(description="车牌号", required=false)
    private String vehicleNum;
    @Schema(description="卖家用户ID", required=false)
    private String sellerId;
    @Schema(description="区域权限列表", required=false)
    private List<String> regionCodeList;
    @Schema(description="买家ID")
    private String buyerId;
    @Schema(description="承运商ID")
    private String carrierId;
    @Schema(description="合同Id")
    private String contractId;
    @Schema(description="合同序号")
    private String contractSequence;
    @Schema(description="查询来源")
    private String source;
    @Schema(description="操作人Id")
    private String operatorId;
}

