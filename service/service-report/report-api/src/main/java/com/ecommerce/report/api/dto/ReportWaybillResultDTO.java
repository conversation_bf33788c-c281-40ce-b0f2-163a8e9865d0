
package com.ecommerce.report.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;
import lombok.Data;

@Data
@Schema(name="运单报表结果实体")
public class ReportWaybillResultDTO {
    @Schema(description="运单号")
    private String waybillNum;
    @Schema(description="运单状态")
    private String waybillStatus;
    @Schema(description="运单类型")
    private String waybillType;
    @Schema(description="出货点")
    private String warehouseName;
    @Schema(description="仓库地址")
    private String warehouseAddress;
    @Schema(description="物流方式")
    private String deliveryWay;
    @Schema(description="买家名称")
    private String buyerName;
    @Schema(description="卖家名称")
    private String sellerName;
    @Schema(description="运输品类名")
    private String transportCategoryName;
    @Schema(description="商品名称")
    private String goodsName;
    @Schema(description="商品单价(元/吨)")
    private String goodsUnitPrice;
    @Schema(description="商品总金额")
    private String goodsPrice;
    @Schema(description="计划发货数量（吨）")
    private String quantityStr;
    @Schema(description="实际发货数量（吨）")
    private String actualQuantityStr;
    @Schema(description="预估配送时间")
    private String estimateDurationStr;
    @Schema(description="实际配送时间")
    private String actualDeliveryTimeStr;
    @Schema(description="预估配送里程")
    private String estimateKmStr;
    @Schema(description="预估配送价格")
    private String estimateCarriageStr;
    @Schema(description="实际配送价格")
    private String actualCarriageStr;
    @Schema(description="承运商名称")
    private String carrierName;
    @Schema(description="车牌号")
    private String vehicleNum;
    @Schema(description="司机")
    private String driverName;
    @Schema(description="收货人")
    private String receiver;
    @Schema(description="收货地址")
    private String receiveAddress;
    @Schema(description="地址备注")
    private String alias;
    @Schema(description="配送时间")
    private String deliveryTimeStr;
    @Schema(description="签收时间")
    private String signTimeStr;
    @Schema(description="车辆出站时间")
    private String leaveWarehouseTimeStr;
    @Schema(description="到达目的地时间")
    private String arriveDestinationTimeStr;
    @Schema(description="订单号")
    private String orderCode;
    @Schema(description="外部订单号")
    private String erpOrderCOde;
    @Schema(description="发货单号")
    private String deliverySheetNum;
    @Schema(description="提货单号")
    private String pickingBillNum;
    @Schema(description="委托单号")
    private String dispatchBillNum;
    @Schema(description="是否为管控商品")
    private String specialFlag;
    @Schema(description="发货时间")
    private String takeTimeStr;
    @Schema(description="订单类型")
    private String orderType;
    @Schema(description="一级销售区域")
    private String saleRegion1;
    @Schema(description="二级销售区域")
    private String saleRegion2;
    @Schema(description="合同编号")
    private String contractNumber;
    @Schema(description="开票方式")
    private String invoiceType;
    @Schema(description="托运方名称")
    private String shipperName;
    @Schema(description="合同项目名称")
    private String projectName;
    @Schema(description="外部运单号")
    private String externalWaybillNum;
    @Schema(description="外部运单状态")
    private String externalWaybillStatus;
    @Schema(description="外部请求号")
    private String externalRequestNo;
    @Schema(description="ERP出厂编号")
    private String factoryNumber;
    @Schema(description="产地")
    private String originPlace;
    @Schema(description="运输方式")
    private String transportToolType;
    @Schema(description="创建时间")
    private Date createTime;
}

