
package com.ecommerce.report.api.dto;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class StatisDailyOrderDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = 5060071658715694718L;
    @Schema(description="订单日笔数")
    private Integer dailySendCount;
    @Schema(description="订单日发货量")
    private BigDecimal dailySendQuantity;
}

