
package com.ecommerce.report.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(name="运单统计实体")
public class StatisWaybillDTO
implements Serializable {
    @Serial
    private static final long serialVersionUID = 8025026987644534723L;
    @Schema(description="当月平台配送量")
    private BigDecimal monthPlatformQuantity;
    @Schema(description="当年平台配送量")
    private BigDecimal yearPlatformQuantity;
}

