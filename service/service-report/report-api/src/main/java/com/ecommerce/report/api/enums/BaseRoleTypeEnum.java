
package com.ecommerce.report.api.enums;

public enum BaseRoleTypeEnum {
    INDIVIDUAL_BUYER(1, "个人买家", "individual_buyer"),
    INDIVIDUAL_DRIVER(2, "个人司机", "individual_driver"),
    ENTERPRISE_BUYER(3, "企业买家", "enterprise_buyer"),
    ENTERPRISE_SELLER(4, "企业卖家", "enterprise_seller"),
    ENTERPRISE_SUPPLIER(83, "企业供应商", "enterprise_supplier"),
    CARRIER(5, "承运商", "carrier"),
    ANONYMOUS(6, "匿名角色", "anonymous"),
    DEFAULT_SUB_ACCOUNT(7, "默认子账号", "default_sub_account"),
    SELLER_SALESPERSON(8, "卖家业务员", "seller_salesperson"),
    CARRIER_DRIVER(9, "承运商司机", "carrier_driver"),
    PLATFORM(10, "平台主账号", "platform"),
    SALES_MANAGER(11, "销售经理", "sales_manager"),
    SALES_ASSISTANT(12, "销售助理", "sales_assistant"),
    STORAGE_ADMIN_SELLER(34, "卖家仓库管理员", "storage_admin_seller"),
    STORAGE_ADMIN_PLATFORM(35, "仓库管理员", "storage_admin_platform"),
    SELLER_LOGISTICSDISPATH(52, "物流调度", "seller_logisticsdispatch"),
    CARRIER_YARDMAN(54, "调度人员", "carrier_yardman");

    private String info;
    private Integer id;
    private String name;

    private BaseRoleTypeEnum(Integer id, String name, String info) {
        this.info = info;
        this.id = id;
        this.name = name;
    }

    public String getInfo() {
        return this.info;
    }

    public Integer getId() {
        return this.id;
    }

    public String getName() {
        return this.name;
    }
}

