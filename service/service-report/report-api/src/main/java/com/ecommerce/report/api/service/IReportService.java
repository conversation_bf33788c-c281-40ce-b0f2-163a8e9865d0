
package com.ecommerce.report.api.service;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.report.api.dto.DailyReportDTO;
import com.ecommerce.report.api.dto.DailyReportQueryDTO;
import com.ecommerce.report.api.dto.ReportInfoDTO;
import com.ecommerce.report.api.dto.ReportOptionDTO;
import com.ecommerce.report.api.dto.ReportShipBillQueryDTO;
import com.ecommerce.report.api.dto.ReportWaybillResultDTO;
import com.ecommerce.report.api.dto.StatisDailyOrderDTO;
import com.ecommerce.report.api.dto.StatisWaybillDTO;
import feign.Response;
import io.swagger.v3.oas.annotations.Operation;

import java.util.List;
import java.util.Map;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "report")
public interface IReportService {
    @PostMapping(path = {"/report/queryConfigByReportCode"}, consumes = {"application/json"})
    ItemResult<ReportInfoDTO> queryConfigByReportCode(@RequestParam(value = "reportCode") String var1);

    @PostMapping(path = {"/report/reportOptions"}, consumes = {"application/json"})
    ItemResult<List<ReportOptionDTO>> reportOptions(@RequestBody List<String> var1);

    @PostMapping(path = {"/report/exportToHTML"}, consumes = {"application/json"})
    ItemResult<String> exportToHTML(@RequestBody Map<String, Object> var1, @RequestParam(value = "reportTemplateName") String var2);

    @PostMapping(path = {"/report/exportExcel"}, consumes = {"application/json"})
    ItemResult<byte[]> exportExcel(@RequestBody Map<String, Object> var1, @RequestParam(value = "reportTemplateName") String var2);

    @PostMapping(path = {"/report/bindingReportAndRole"}, consumes = {"application/json"})
    ItemResult<Void> bindingReportAndRole(@RequestParam(value = "reportCode") String var1, @RequestBody List<String> var2);

    @PostMapping(path = {"/report/exportPublishedWaybillList"}, consumes = {"application/json"})
    ItemResult<List<ReportWaybillResultDTO>> exportPublishedWaybillList(@RequestBody ReportShipBillQueryDTO var1);

    @Operation(summary = "以流的方式返回数据")
    @PostMapping(path = {"/report/exportPublishedWaybill"}, consumes = {"application/json"})
    Response exportPublishedWaybill(@RequestBody ReportShipBillQueryDTO var1);

    @PostMapping(path = {"/report/queryDailyReport"}, consumes = {"application/json"})
    ItemResult<DailyReportDTO> queryDailyReport(@RequestBody DailyReportQueryDTO var1);

    @PostMapping(path = {"/report/queryDailyOrderReport"}, consumes = {"application/json"})
    ItemResult<StatisDailyOrderDTO> queryDailyOrderReport(@RequestBody DailyReportQueryDTO var1);

    @PostMapping(path = {"/report/queryWaybillPlatformReport"}, consumes = {"application/json"})
    ItemResult<StatisWaybillDTO> queryWaybillPlatformReport(@RequestBody DailyReportQueryDTO var1);

}
