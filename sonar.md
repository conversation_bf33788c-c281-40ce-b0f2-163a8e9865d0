


# asset-2025-apply-shell
```shell

mvn clean verify sonar:sonar -Dsonar.projectName=Ecommerce-all-sprint2 -Dsonar.sourceEncoding=UTF-8 -Dsonar.projectKey=Ecommerce-all-sprint2 -Dsonar.host.url=http://*************:8080 -Dsonar.login=sqp_59216a33724b76537dc992727983f6c8c7267ab5 -Dmaven.test.skip=true
```

# asset-2025
```shell
mvn clean verify sonar:sonar \
  -Dsonar.projectName=Ecommerce-all-sprint1 \
  -Dsonar.sourceEncoding=UTF-8 \
  -Dsonar.projectKey=Ecommerce-all-sprint1 \
  -Dsonar.host.url=http://*************:8080 \
  -Dsonar.login=sqp_28abc267486316c498b21706b25b164f66c3e97a \
  -Dmaven.test.skip=true
```




