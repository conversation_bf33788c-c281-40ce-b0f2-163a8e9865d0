// 使用ES模块语法导入fs模块的Promise版本
import { writeFile } from 'fs/promises';

const url = 'http://10.201.188.73:8080/api/issues/search'
const params ={
    "componentKeys": "Ecommerce-all-sprint2",
    "s": "FILE_LINE",
    "resolved": "false",
    "rules": "java:S125",
    "severities":"BLOCKER,CRITICAL,MAJOR",
    "ps": "500",
    "p": "1",
    "additionalFields": "_all",
    "timeZone": "Asia/Shanghai"
}


const headers ={
    "Cookie": "XSRF-TOKEN=mghjmpk6rb9ouuntnbqcesgmuv; JWT-SESSION=eyJhbGciOiJIUzI1NiJ9.eyJsYXN0UmVmcmVzaFRpbWUiOjE3NTM3NzY5MDg4MTQsInhzcmZUb2tlbiI6Im1naGptcGs2cmI5b3V1bnRuYnFjZXNnbXV2IiwianRpIjoiQVpoUENNQ3locU9ucm42RDVld0ciLCJzdWIiOiJBWmVnRGJhZWhxT25ybjZENU92aiIsImlhdCI6MTc1MzY3MjYzMCwiZXhwIjoxNzU0MDM2MTA4fQ.jRDEKdBI4kUJLurkbuJdXOE3jSXZXNP-lz_4Sb__oUs"
}

// 将参数转换为查询字符串
function buildQueryString(params) {
    return Object.keys(params)
        .map(key => encodeURIComponent(key) + '=' + encodeURIComponent(params[key]))
        .join('&');
}

// 循环获取所有页面数据
async function fetchAllPages() {
    let currentPage = 1;
    let hasNextPage = true;
    const issues = [];

    while (hasNextPage) {
        // 更新页码参数
        params.p = currentPage.toString();
        
        // 构建完整的URL
        const queryString = buildQueryString(params);
        const fullUrl = `${url}?${queryString}`;
        
        try {
            console.log(`正在获取第 ${currentPage} 页...`);
            console.log(`请求URL: ${fullUrl}`);
            
            // 发送GET请求
            const response = await fetch(fullUrl, {
                method: 'GET',
                headers: headers
            });
            
            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
            }
            
            const data = await response.json();
            const res_issues = data.issues || [];
            res_issues.forEach(v => issues.push(v));
            
            // 处理响应数据
            console.log(`第 ${currentPage} 页获取到 ${res_issues.length} 条问题`);
            
            // 检查是否还有下一页
            const total = data.total || 0;
            const pageSize = parseInt(params.ps) || 500;
            const totalPages = Math.ceil(total / pageSize);
            hasNextPage = currentPage < totalPages;
            
            console.log(`总计: ${total} 条问题, 总页数: ${totalPages}, 当前页: ${currentPage}, 是否继续: ${hasNextPage}`);
            
            // 如果有下一页，增加页码继续循环
            if (hasNextPage) {
                currentPage++;
                // 添加延迟避免请求过于频繁
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        } catch (error) {
            console.error(`获取第 ${currentPage} 页时出错:`, error);
            hasNextPage = false;
        }
    }
    
    console.log(`所有页面数据获取完成，共收集到 ${issues.length} 条问题`);
    
    // 将结果写入文件
    try {
        // 写入JSON格式文件
        const ruleName = Array.isArray(params.rules) ? params.rules[0] : params.rules;
        const fileName = `sonar_issues_${ruleName.replace(":", "_")}.json`;
        await writeFile(fileName, JSON.stringify(issues, null, 2));
        console.log(`已将结果保存到 ${fileName} 文件`);
        
        // 写入CSV格式文件
        await writeCsvFile(issues, ruleName);
    } catch (error) {
        console.error('写入文件时出错:', error);
    }
    
    return issues;
}

// 将issues写入CSV文件
async function writeCsvFile(issues, ruleName) {
    if (issues.length === 0) {
        console.log('没有数据可写入CSV文件');
        return;
    }
    
    // CSV表头
    const csvHeader = 'Key,Rule,Component,Line,Message,Severity,Status,Creation Date\n';
    
    // CSV内容
    const csvContent = issues.map(issue => {
        return `"${issue.key || ''}",` +
               `"${issue.rule || ''}",` +
               `"${issue.component || ''}",` +
               `"${issue.line || ''}",` +
               `"${(issue.message || '').replace(/"/g, '""')}",` +  // 转义双引号
               `"${issue.severity || ''}",` +
               `"${issue.status || ''}",` +
               `"${issue.creationDate || ''}"`;
    }).join('\n');
    
    // 写入文件
    const fileName = `sonar_issues_${ruleName.replace(":", "_")}.csv`;
    await writeFile(fileName, csvHeader + csvContent);
    console.log(`已将结果保存到 ${fileName} 文件`);
}

// 为每个规则获取问题
// const rules = ['java:S125','java:S112','java:S3740','java:S6204','java:S1192','java:S3776','java:S3252','java:S1854','java:S1123'];
const rules = ['java:S112'/*,'java:S1192','java:S3776','java:S3252','java:S1123','java:S3740','java:S1066','java:S1168','java:S1118','S1172'*/];
// 创建一个异步函数来顺序处理每个规则
for (let rule of rules) {
    console.log(`开始处理规则: ${rule}`);
    // 为每个规则创建一个新的params对象，避免修改原始对象
    params.rules = rule
    await fetchAllPages()
}
