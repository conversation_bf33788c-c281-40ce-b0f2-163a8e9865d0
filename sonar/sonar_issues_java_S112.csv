Key,Rule,Component,Line,Message,Severity,Status,Creation Date
"AZhfv_2ng6P5z8Uxr1Qf","java:S112","Ecommerce-all-sprint2:common/src/main/java/com/ecommerce/common/excel/XssExcel.java","80","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhfv_2ng6P5z8Uxr1Qg","java:S112","Ecommerce-all-sprint2:common/src/main/java/com/ecommerce/common/excel/XssExcel.java","95","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhfv_2ng6P5z8Uxr1Qh","java:S112","Ecommerce-all-sprint2:common/src/main/java/com/ecommerce/common/excel/XssExcel.java","110","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhP7WPCg6P5z8UxgijR","java:S112","Ecommerce-all-sprint2:generator/src/main/java/com/ecommerce/plugin/GenerateUtil.java","514","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhfv_zmg6P5z8Uxr1Qa","java:S112","Ecommerce-all-sprint2:mq/cmq-topic/src/main/java/com/qcloud/cmq/CMQHttp.java","42","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhP7WObg6P5z8UxgiiU","java:S112","Ecommerce-all-sprint2:mq/mq-core/src/main/java/com/ecommerce/mq/core/handler/MessageHandlerFactory.java","54","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WOTg6P5z8UxgiiO","java:S112","Ecommerce-all-sprint2:mq/mq-core/src/main/java/com/ecommerce/mq/core/serializer/KryoSerializer.java","50","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WOTg6P5z8UxgiiP","java:S112","Ecommerce-all-sprint2:mq/mq-core/src/main/java/com/ecommerce/mq/core/serializer/KryoSerializer.java","70","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhfv_0Hg6P5z8Uxr1Qc","java:S112","Ecommerce-all-sprint2:mq/rabbitmq-sdk/src/main/java/com/ecommerce/rabbitmq/consumer/InitConsumerConfig.java","95","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhP7VFig6P5z8Uxgekk","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/DataPermissionController.java","31","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFig6P5z8Uxgekl","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/DataPermissionController.java","38","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFig6P5z8Uxgekm","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/DataPermissionController.java","45","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFig6P5z8Uxgekn","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/DataPermissionController.java","52","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFig6P5z8Uxgeko","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/DataPermissionController.java","59","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFig6P5z8Uxgekp","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/DataPermissionController.java","65","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFig6P5z8Uxgekq","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/DataPermissionController.java","71","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFSg6P5z8Uxgei5","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/DatapermController.java","33","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFSg6P5z8Uxgei6","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/DatapermController.java","39","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFSg6P5z8Uxgei7","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/DatapermController.java","45","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFSg6P5z8Uxgei8","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/DatapermController.java","51","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFSg6P5z8Uxgei9","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/DatapermController.java","58","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFdg6P5z8Uxgej6","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/DefaultStoreController.java","29","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFPg6P5z8Uxgeif","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/MenuController.java","36","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFPg6P5z8Uxgeig","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/MenuController.java","43","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFPg6P5z8Uxgeih","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/MenuController.java","50","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFPg6P5z8Uxgeii","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/MenuController.java","58","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFPg6P5z8Uxgeij","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/MenuController.java","66","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFPg6P5z8Uxgeik","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/MenuController.java","73","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFPg6P5z8Uxgeil","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/MenuController.java","80","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFPg6P5z8Uxgeim","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/MenuController.java","87","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFPg6P5z8Uxgein","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/MenuController.java","93","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFPg6P5z8Uxgeio","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/MenuController.java","99","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFPg6P5z8Uxgeip","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/MenuController.java","105","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VGOg6P5z8Uxgeo5","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/MessageConfigController.java","33","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VGOg6P5z8Uxgeo6","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/MessageConfigController.java","41","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VGOg6P5z8Uxgeo7","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/MessageConfigController.java","49","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VGOg6P5z8Uxgeo8","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/MessageConfigController.java","56","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VGOg6P5z8Uxgeo9","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/MessageConfigController.java","63","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VGOg6P5z8Uxgeo-","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/MessageConfigController.java","70","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VGOg6P5z8Uxgeo_","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/MessageConfigController.java","77","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VGOg6P5z8UxgepA","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/MessageConfigController.java","83","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VGOg6P5z8UxgepB","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/MessageConfigController.java","90","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VGOg6P5z8UxgepC","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/MessageConfigController.java","98","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VGOg6P5z8UxgepD","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/MessageConfigController.java","104","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VGOg6P5z8UxgepE","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/MessageConfigController.java","114","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VGOg6P5z8UxgepF","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/MessageConfigController.java","123","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VGOg6P5z8UxgepG","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/MessageConfigController.java","129","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VGOg6P5z8UxgepH","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/MessageConfigController.java","136","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VGOg6P5z8UxgepI","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/MessageConfigController.java","142","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VGOg6P5z8UxgepJ","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/MessageConfigController.java","148","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VGOg6P5z8UxgepK","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/MessageConfigController.java","154","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VGOg6P5z8UxgepL","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/MessageConfigController.java","161","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VGOg6P5z8UxgepM","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/MessageConfigController.java","168","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VGOg6P5z8UxgepN","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/MessageConfigController.java","177","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFgg6P5z8Uxgej8","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/PageController.java","36","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFgg6P5z8Uxgej9","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/PageController.java","43","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFgg6P5z8Uxgej-","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/PageController.java","51","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFgg6P5z8Uxgej_","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/PageController.java","58","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFgg6P5z8UxgekA","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/PageController.java","64","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFgg6P5z8UxgekB","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/PageController.java","70","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFgg6P5z8UxgekC","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/PageController.java","77","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFgg6P5z8UxgekD","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/PageController.java","84","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFgg6P5z8UxgekE","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/PageController.java","91","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFgg6P5z8UxgekF","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/PageController.java","99","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFgg6P5z8UxgekG","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/PageController.java","107","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFgg6P5z8UxgekH","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/PageController.java","114","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFgg6P5z8UxgekI","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/PageController.java","121","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFgg6P5z8UxgekJ","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/PageController.java","128","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFgg6P5z8UxgekK","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/PageController.java","136","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFgg6P5z8UxgekL","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/PageController.java","142","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFgg6P5z8UxgekM","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/PageController.java","148","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFgg6P5z8UxgekN","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/PageController.java","154","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFgg6P5z8UxgekO","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/PageController.java","160","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VF7g6P5z8Uxgemr","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/ProvinceController.java","32","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VF7g6P5z8Uxgems","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/ProvinceController.java","40","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VF7g6P5z8Uxgemt","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/ProvinceController.java","46","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VF0g6P5z8Uxgel1","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/ReceivingAddressController.java","103","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VF0g6P5z8Uxgel2","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/ReceivingAddressController.java","111","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VF0g6P5z8Uxgel3","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/ReceivingAddressController.java","119","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VF0g6P5z8Uxgel4","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/ReceivingAddressController.java","125","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VF0g6P5z8Uxgel5","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/ReceivingAddressController.java","131","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VF0g6P5z8Uxgel6","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/ReceivingAddressController.java","139","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VF0g6P5z8Uxgel7","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/ReceivingAddressController.java","147","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VF0g6P5z8Uxgel9","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/ReceivingAddressController.java","159","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VF0g6P5z8Uxgel-","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/ReceivingAddressController.java","173","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VF0g6P5z8Uxgel_","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/ReceivingAddressController.java","179","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VF0g6P5z8UxgemA","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/ReceivingAddressController.java","185","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VF0g6P5z8UxgemB","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/ReceivingAddressController.java","191","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VF0g6P5z8UxgemC","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/ReceivingAddressController.java","197","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VF0g6P5z8UxgemD","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/ReceivingAddressController.java","203","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VGTg6P5z8Uxgep1","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/RegionController.java","49","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VGTg6P5z8Uxgep2","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/RegionController.java","56","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VGTg6P5z8Uxgep3","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/RegionController.java","63","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VGTg6P5z8Uxgep4","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/RegionController.java","70","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VGTg6P5z8Uxgep5","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/RegionController.java","77","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VGTg6P5z8Uxgep6","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/RegionController.java","84","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VGTg6P5z8Uxgep7","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/RegionController.java","98","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VF-g6P5z8Uxgemx","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/RoleController.java","54","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VF-g6P5z8Uxgemy","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/RoleController.java","61","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VF-g6P5z8Uxgemz","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/RoleController.java","67","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VF-g6P5z8Uxgem0","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/RoleController.java","73","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VF-g6P5z8Uxgem1","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/RoleController.java","81","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VF-g6P5z8Uxgem2","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/RoleController.java","146","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VF-g6P5z8Uxgem3","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/RoleController.java","153","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VF-g6P5z8Uxgem4","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/RoleController.java","161","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VF-g6P5z8Uxgem5","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/RoleController.java","169","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VF-g6P5z8Uxgem6","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/RoleController.java","176","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VF-g6P5z8Uxgem7","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/RoleController.java","184","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VF-g6P5z8Uxgem8","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/RoleController.java","192","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VF-g6P5z8Uxgem9","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/RoleController.java","200","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VF-g6P5z8Uxgem-","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/RoleController.java","208","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VF-g6P5z8Uxgem_","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/RoleController.java","214","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VF-g6P5z8UxgenA","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/RoleController.java","220","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VF-g6P5z8UxgenB","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/RoleController.java","228","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VF-g6P5z8UxgenC","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/RoleController.java","235","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VF-g6P5z8UxgenD","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/RoleController.java","243","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VF-g6P5z8UxgenE","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/RoleController.java","251","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VF-g6P5z8UxgenG","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/RoleController.java","264","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VF-g6P5z8UxgenH","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/RoleController.java","272","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VF-g6P5z8UxgenI","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/RoleController.java","280","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VF-g6P5z8UxgenJ","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/RoleController.java","288","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VF-g6P5z8UxgenK","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/RoleController.java","296","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VF-g6P5z8UxgenL","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/RoleController.java","303","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VF-g6P5z8UxgenM","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/RoleController.java","310","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VF-g6P5z8UxgenN","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/RoleController.java","317","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VF-g6P5z8UxgenO","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/RoleController.java","325","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VF-g6P5z8UxgenP","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/RoleController.java","333","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VF-g6P5z8UxgenQ","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/RoleController.java","340","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VF-g6P5z8UxgenR","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/RoleController.java","346","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VF-g6P5z8UxgenS","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/RoleController.java","353","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VF-g6P5z8UxgenT","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/RoleController.java","360","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VF-g6P5z8UxgenU","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/RoleController.java","373","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFMg6P5z8Uxgehj","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/SaleRegionController.java","57","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFMg6P5z8Uxgehk","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/SaleRegionController.java","64","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFMg6P5z8Uxgehl","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/SaleRegionController.java","72","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFMg6P5z8Uxgehm","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/SaleRegionController.java","79","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFMg6P5z8Uxgehn","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/SaleRegionController.java","85","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFMg6P5z8Uxgeho","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/SaleRegionController.java","93","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFMg6P5z8Uxgehp","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/SaleRegionController.java","101","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFMg6P5z8Uxgehq","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/SaleRegionController.java","109","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFMg6P5z8Uxgehr","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/SaleRegionController.java","117","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFMg6P5z8Uxgehs","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/SaleRegionController.java","123","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFMg6P5z8Uxgeht","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/SaleRegionController.java","129","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFMg6P5z8Uxgehu","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/SaleRegionController.java","136","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFMg6P5z8Uxgehv","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/SaleRegionController.java","143","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFMg6P5z8Uxgehw","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/SaleRegionController.java","150","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFMg6P5z8Uxgehy","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/SaleRegionController.java","162","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFMg6P5z8Uxgehz","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/SaleRegionController.java","169","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFMg6P5z8Uxgeh0","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/SaleRegionController.java","176","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFMg6P5z8Uxgeh1","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/SaleRegionController.java","184","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFMg6P5z8Uxgeh2","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/SaleRegionController.java","192","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFMg6P5z8Uxgeh3","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/SaleRegionController.java","199","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFMg6P5z8Uxgeh5","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/SaleRegionController.java","211","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFMg6P5z8Uxgeh6","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/SaleRegionController.java","217","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFMg6P5z8Uxgeh7","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/SaleRegionController.java","223","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFMg6P5z8Uxgeh8","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/SaleRegionController.java","230","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFMg6P5z8Uxgeh9","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/SaleRegionController.java","236","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFMg6P5z8Uxgeh-","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/SaleRegionController.java","243","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFMg6P5z8Uxgeh_","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/SaleRegionController.java","250","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFMg6P5z8UxgeiA","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/SaleRegionController.java","258","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFug6P5z8UxgelA","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/ServeiceManagerController.java","33","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFug6P5z8UxgelB","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/ServeiceManagerController.java","40","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFug6P5z8UxgelC","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/ServeiceManagerController.java","47","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFug6P5z8UxgelD","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/ServeiceManagerController.java","54","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFug6P5z8UxgelE","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/ServeiceManagerController.java","61","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFug6P5z8UxgelF","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/ServeiceManagerController.java","68","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFug6P5z8UxgelG","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/ServeiceManagerController.java","75","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFug6P5z8UxgelH","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/ServeiceManagerController.java","82","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFug6P5z8UxgelI","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/ServeiceManagerController.java","89","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFug6P5z8UxgelJ","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/ServeiceManagerController.java","96","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFug6P5z8UxgelK","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/ServeiceManagerController.java","102","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFug6P5z8UxgelL","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/ServeiceManagerController.java","108","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFmg6P5z8Uxgeky","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/SysLogQueryController.java","31","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFmg6P5z8Uxgekz","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/SysLogQueryController.java","37","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFmg6P5z8Uxgek0","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/SysLogQueryController.java","43","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFmg6P5z8Uxgek1","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/SysLogQueryController.java","49","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFIg6P5z8Uxgeg7","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/ValueSetController.java","31","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFIg6P5z8Uxgeg8","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/ValueSetController.java","39","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFIg6P5z8Uxgeg9","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/ValueSetController.java","47","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFIg6P5z8Uxgeg-","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/ValueSetController.java","55","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFIg6P5z8Uxgeg_","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/ValueSetController.java","63","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFIg6P5z8UxgehA","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/ValueSetController.java","70","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFIg6P5z8UxgehB","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/ValueSetController.java","77","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFIg6P5z8UxgehC","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/ValueSetController.java","84","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFIg6P5z8UxgehD","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/ValueSetController.java","92","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFIg6P5z8UxgehE","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/ValueSetController.java","100","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFIg6P5z8UxgehF","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/ValueSetController.java","107","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFIg6P5z8UxgehG","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/ValueSetController.java","114","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFIg6P5z8UxgehH","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/ValueSetController.java","122","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFIg6P5z8UxgehI","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/ValueSetController.java","129","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFIg6P5z8UxgehJ","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/ValueSetController.java","136","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFIg6P5z8UxgehK","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/ValueSetController.java","142","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFIg6P5z8UxgehL","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/ValueSetController.java","149","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFIg6P5z8UxgehM","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/ValueSetController.java","156","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFIg6P5z8UxgehN","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/ValueSetController.java","163","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFIg6P5z8UxgehO","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/ValueSetController.java","169","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFVg6P5z8UxgejD","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/WarehouseUserRelationController.java","33","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFVg6P5z8UxgejE","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/WarehouseUserRelationController.java","40","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFVg6P5z8UxgejF","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/WarehouseUserRelationController.java","47","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VFVg6P5z8UxgejG","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/WarehouseUserRelationController.java","54","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VGKg6P5z8UxgeoS","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/WharfController.java","47","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VGKg6P5z8UxgeoT","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/WharfController.java","54","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VGKg6P5z8UxgeoU","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/WharfController.java","61","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VGKg6P5z8UxgeoV","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/WharfController.java","68","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VGKg6P5z8UxgeoW","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/WharfController.java","75","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VGKg6P5z8UxgeoX","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/WharfController.java","82","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VGKg6P5z8UxgeoY","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/WharfController.java","88","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VGKg6P5z8UxgeoZ","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/WharfController.java","94","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VGKg6P5z8Uxgeoa","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/WharfController.java","102","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VGKg6P5z8Uxgeob","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/WharfController.java","108","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VGKg6P5z8Uxgeoc","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/WharfController.java","114","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VGKg6P5z8Uxgeod","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/WharfController.java","120","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VGKg6P5z8Uxgeoe","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/WharfController.java","126","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VGKg6P5z8Uxgeof","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/WharfController.java","132","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VGKg6P5z8Uxgeog","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/WharfController.java","138","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VGKg6P5z8Uxgeoi","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/controller/WharfController.java","150","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhfv-rPg6P5z8Uxr1Lk","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/service/ICloudService.java","125","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhP7VDag6P5z8UxgeZd","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/service/impl/HuaweiCloudService.java","298","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VDag6P5z8UxgeZe","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/service/impl/HuaweiCloudService.java","303","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VDag6P5z8UxgeZf","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/service/impl/HuaweiCloudService.java","308","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VDag6P5z8UxgeZg","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/service/impl/HuaweiCloudService.java","313","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VG_g6P5z8Uxgeuw","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/thirdPartyService/aliyun/HttpUtils.java","202","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VHbg6P5z8Uxgexg","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/utils/FileUtil.java","64","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VHgg6P5z8Uxgexk","java:S112","Ecommerce-all-sprint2:service/service-base/base/src/main/java/com/ecommerce/base/utils/ResourceUtil.java","30","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Vr5g6P5z8UxggJp","java:S112","Ecommerce-all-sprint2:service/service-common/src/main/java/com/ecommerce/common/aop/log/RedisLockAop.java","65","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Vr3g6P5z8UxggJk","java:S112","Ecommerce-all-sprint2:service/service-common/src/main/java/com/ecommerce/common/aop/log/SetThreadNameAop.java","59","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VqYg6P5z8UxggG0","java:S112","Ecommerce-all-sprint2:service/service-common/src/main/java/com/ecommerce/common/service/common/RSAService.java","33","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VqHg6P5z8UxggGK","java:S112","Ecommerce-all-sprint2:service/service-common/src/main/java/com/ecommerce/common/service/common/uuid/UUIDPoolGenerator.java","105","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tjdg6P5z8UxgbND","java:S112","Ecommerce-all-sprint2:service/service-goods/goods/src/main/java/com/ecommerce/goods/config/EsConfig.java","57","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tddg6P5z8Uxga6b","java:S112","Ecommerce-all-sprint2:service/service-goods/goods/src/main/java/com/ecommerce/goods/controller/ContractTemplateController.java","39","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tddg6P5z8Uxga6c","java:S112","Ecommerce-all-sprint2:service/service-goods/goods/src/main/java/com/ecommerce/goods/controller/ContractTemplateController.java","46","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tddg6P5z8Uxga6d","java:S112","Ecommerce-all-sprint2:service/service-goods/goods/src/main/java/com/ecommerce/goods/controller/ContractTemplateController.java","53","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tddg6P5z8Uxga6e","java:S112","Ecommerce-all-sprint2:service/service-goods/goods/src/main/java/com/ecommerce/goods/controller/ContractTemplateController.java","60","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tddg6P5z8Uxga6f","java:S112","Ecommerce-all-sprint2:service/service-goods/goods/src/main/java/com/ecommerce/goods/controller/ContractTemplateController.java","67","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tddg6P5z8Uxga6g","java:S112","Ecommerce-all-sprint2:service/service-goods/goods/src/main/java/com/ecommerce/goods/controller/ContractTemplateController.java","76","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tddg6P5z8Uxga6h","java:S112","Ecommerce-all-sprint2:service/service-goods/goods/src/main/java/com/ecommerce/goods/controller/ContractTemplateController.java","85","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tddg6P5z8Uxga6i","java:S112","Ecommerce-all-sprint2:service/service-goods/goods/src/main/java/com/ecommerce/goods/controller/ContractTemplateController.java","91","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Td7g6P5z8Uxga9B","java:S112","Ecommerce-all-sprint2:service/service-goods/goods/src/main/java/com/ecommerce/goods/controller/GoodsAddItemController.java","31","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Td7g6P5z8Uxga9C","java:S112","Ecommerce-all-sprint2:service/service-goods/goods/src/main/java/com/ecommerce/goods/controller/GoodsAddItemController.java","38","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Td7g6P5z8Uxga9D","java:S112","Ecommerce-all-sprint2:service/service-goods/goods/src/main/java/com/ecommerce/goods/controller/GoodsAddItemController.java","45","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Td7g6P5z8Uxga9E","java:S112","Ecommerce-all-sprint2:service/service-goods/goods/src/main/java/com/ecommerce/goods/controller/GoodsAddItemController.java","52","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Td7g6P5z8Uxga9F","java:S112","Ecommerce-all-sprint2:service/service-goods/goods/src/main/java/com/ecommerce/goods/controller/GoodsAddItemController.java","58","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Td7g6P5z8Uxga9G","java:S112","Ecommerce-all-sprint2:service/service-goods/goods/src/main/java/com/ecommerce/goods/controller/GoodsAddItemController.java","65","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Td7g6P5z8Uxga9H","java:S112","Ecommerce-all-sprint2:service/service-goods/goods/src/main/java/com/ecommerce/goods/controller/GoodsAddItemController.java","72","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Td7g6P5z8Uxga9I","java:S112","Ecommerce-all-sprint2:service/service-goods/goods/src/main/java/com/ecommerce/goods/controller/GoodsAddItemController.java","79","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Td7g6P5z8Uxga9J","java:S112","Ecommerce-all-sprint2:service/service-goods/goods/src/main/java/com/ecommerce/goods/controller/GoodsAddItemController.java","85","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Td7g6P5z8Uxga9K","java:S112","Ecommerce-all-sprint2:service/service-goods/goods/src/main/java/com/ecommerce/goods/controller/GoodsAddItemController.java","91","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Td7g6P5z8Uxga9L","java:S112","Ecommerce-all-sprint2:service/service-goods/goods/src/main/java/com/ecommerce/goods/controller/GoodsAddItemController.java","97","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Td7g6P5z8Uxga9M","java:S112","Ecommerce-all-sprint2:service/service-goods/goods/src/main/java/com/ecommerce/goods/controller/GoodsAddItemController.java","103","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Td7g6P5z8Uxga9N","java:S112","Ecommerce-all-sprint2:service/service-goods/goods/src/main/java/com/ecommerce/goods/controller/GoodsAddItemController.java","109","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Thtg6P5z8UxgbFT","java:S112","Ecommerce-all-sprint2:service/service-goods/goods/src/main/java/com/ecommerce/goods/service/impl/ContractService.java","1793","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tidg6P5z8UxgbMN","java:S112","Ecommerce-all-sprint2:service/service-goods/goods/src/main/java/com/ecommerce/goods/service/resource/IBatchPriceRuleService.java","36","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tidg6P5z8UxgbMO","java:S112","Ecommerce-all-sprint2:service/service-goods/goods/src/main/java/com/ecommerce/goods/service/resource/IBatchPriceRuleService.java","45","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tidg6P5z8UxgbMP","java:S112","Ecommerce-all-sprint2:service/service-goods/goods/src/main/java/com/ecommerce/goods/service/resource/IBatchPriceRuleService.java","61","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tiig6P5z8UxgbMR","java:S112","Ecommerce-all-sprint2:service/service-goods/goods/src/main/java/com/ecommerce/goods/service/resource/IPriceChangeService.java","19","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhWHH6Eg6P5z8UxnTam","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/bill/check/controller/BillCheckRuleController.java","35","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-29T01:57:36+0000"
"AZhWHH6Eg6P5z8UxnTan","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/bill/check/controller/BillCheckRuleController.java","41","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-29T01:57:36+0000"
"AZhWHH6Eg6P5z8UxnTao","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/bill/check/controller/BillCheckRuleController.java","47","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-29T01:57:36+0000"
"AZhWHH6Eg6P5z8UxnTap","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/bill/check/controller/BillCheckRuleController.java","54","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-29T01:57:36+0000"
"AZhP7UO2g6P5z8UxgcoN","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/AdvertisementController.java","30","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UO2g6P5z8UxgcoO","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/AdvertisementController.java","38","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UO2g6P5z8UxgcoP","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/AdvertisementController.java","46","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UO2g6P5z8UxgcoQ","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/AdvertisementController.java","61","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UO2g6P5z8UxgcoR","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/AdvertisementController.java","68","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UO2g6P5z8UxgcoS","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/AdvertisementController.java","89","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UO2g6P5z8UxgcoT","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/AdvertisementController.java","96","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UO2g6P5z8UxgcoU","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/AdvertisementController.java","103","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UO2g6P5z8UxgcoV","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/AdvertisementController.java","110","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UO2g6P5z8UxgcoW","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/AdvertisementController.java","118","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UO2g6P5z8UxgcoX","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/AdvertisementController.java","126","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UO2g6P5z8UxgcoY","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/AdvertisementController.java","140","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOug6P5z8UxgcnQ","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/AdvertisementSpaceController.java","33","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOug6P5z8UxgcnR","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/AdvertisementSpaceController.java","41","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOug6P5z8UxgcnS","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/AdvertisementSpaceController.java","49","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOug6P5z8UxgcnT","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/AdvertisementSpaceController.java","57","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOug6P5z8UxgcnU","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/AdvertisementSpaceController.java","65","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOug6P5z8UxgcnV","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/AdvertisementSpaceController.java","73","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOug6P5z8UxgcnW","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/AdvertisementSpaceController.java","80","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOug6P5z8UxgcnX","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/AdvertisementSpaceController.java","86","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UORg6P5z8UxgciE","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/AnnouncementController.java","36","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UORg6P5z8UxgciF","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/AnnouncementController.java","44","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UORg6P5z8UxgciG","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/AnnouncementController.java","52","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UORg6P5z8UxgciH","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/AnnouncementController.java","60","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UORg6P5z8UxgciI","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/AnnouncementController.java","67","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UORg6P5z8UxgciJ","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/AnnouncementController.java","74","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UORg6P5z8UxgciK","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/AnnouncementController.java","81","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UORg6P5z8UxgciL","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/AnnouncementController.java","89","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UORg6P5z8UxgciM","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/AnnouncementController.java","97","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UORg6P5z8UxgciN","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/AnnouncementController.java","105","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UO0g6P5z8Uxgcnr","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ComplaintsOpinionController.java","31","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UO0g6P5z8Uxgcns","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ComplaintsOpinionController.java","37","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UO0g6P5z8Uxgcnt","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ComplaintsOpinionController.java","43","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UO0g6P5z8Uxgcnu","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ComplaintsOpinionController.java","49","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UO0g6P5z8Uxgcnv","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ComplaintsOpinionController.java","55","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UO0g6P5z8Uxgcnw","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ComplaintsOpinionController.java","61","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UO0g6P5z8Uxgcnx","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ComplaintsOpinionController.java","67","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UO0g6P5z8Uxgcny","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ComplaintsOpinionController.java","73","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UO0g6P5z8Uxgcnz","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ComplaintsOpinionController.java","79","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UO0g6P5z8Uxgcn0","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ComplaintsOpinionController.java","85","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UO0g6P5z8Uxgcn1","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ComplaintsOpinionController.java","91","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UO0g6P5z8Uxgcn2","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ComplaintsOpinionController.java","96","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UO0g6P5z8Uxgcn3","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ComplaintsOpinionController.java","102","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UO0g6P5z8Uxgcn4","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ComplaintsOpinionController.java","108","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UO0g6P5z8Uxgcn5","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ComplaintsOpinionController.java","114","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UO0g6P5z8Uxgcn6","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ComplaintsOpinionController.java","120","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UO0g6P5z8Uxgcn7","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ComplaintsOpinionController.java","126","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOLg6P5z8Uxgcg4","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ContractBreachController.java","32","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOLg6P5z8Uxgcg5","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ContractBreachController.java","40","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOLg6P5z8Uxgcg6","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ContractBreachController.java","48","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOLg6P5z8Uxgcg7","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ContractBreachController.java","55","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOLg6P5z8Uxgcg8","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ContractBreachController.java","62","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOLg6P5z8Uxgcg9","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ContractBreachController.java","69","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOLg6P5z8Uxgcg-","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ContractBreachController.java","76","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOLg6P5z8Uxgcg_","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ContractBreachController.java","84","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOLg6P5z8UxgchA","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ContractBreachController.java","91","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOLg6P5z8UxgchB","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ContractBreachController.java","99","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOLg6P5z8UxgchC","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ContractBreachController.java","106","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOLg6P5z8UxgchD","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ContractBreachController.java","114","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOLg6P5z8UxgchE","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ContractBreachController.java","122","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOLg6P5z8UxgchF","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ContractBreachController.java","129","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOLg6P5z8UxgchG","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ContractBreachController.java","136","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOLg6P5z8UxgchH","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ContractBreachController.java","144","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOLg6P5z8UxgchI","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ContractBreachController.java","151","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOLg6P5z8UxgchJ","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ContractBreachController.java","159","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOLg6P5z8UxgchK","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ContractBreachController.java","167","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOLg6P5z8UxgchL","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ContractBreachController.java","175","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOLg6P5z8UxgchM","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ContractBreachController.java","183","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOLg6P5z8UxgchN","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ContractBreachController.java","191","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOLg6P5z8UxgchO","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ContractBreachController.java","198","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOLg6P5z8UxgchP","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ContractBreachController.java","205","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOLg6P5z8UxgchQ","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ContractBreachController.java","213","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOLg6P5z8UxgchR","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ContractBreachController.java","221","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOLg6P5z8UxgchS","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ContractBreachController.java","229","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOLg6P5z8UxgchT","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ContractBreachController.java","237","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOLg6P5z8UxgchU","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ContractBreachController.java","245","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOLg6P5z8UxgchV","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ContractBreachController.java","252","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOOg6P5z8Uxgch0","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/CreditScoreController.java","38","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOOg6P5z8Uxgch1","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/CreditScoreController.java","45","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOOg6P5z8Uxgch2","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/CreditScoreController.java","52","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOOg6P5z8Uxgch3","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/CreditScoreController.java","59","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOOg6P5z8Uxgch4","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/CreditScoreController.java","66","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOOg6P5z8Uxgch5","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/CreditScoreController.java","73","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOOg6P5z8Uxgch6","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/CreditScoreController.java","80","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOag6P5z8Uxgcjd","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/FavoriteCollectionController.java","31","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOag6P5z8Uxgcje","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/FavoriteCollectionController.java","37","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOag6P5z8Uxgcjf","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/FavoriteCollectionController.java","43","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOag6P5z8Uxgcjg","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/FavoriteCollectionController.java","49","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOag6P5z8Uxgcjh","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/FavoriteCollectionController.java","55","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOag6P5z8Uxgcji","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/FavoriteCollectionController.java","62","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOag6P5z8Uxgcjj","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/FavoriteCollectionController.java","67","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOag6P5z8Uxgcjk","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/FavoriteCollectionController.java","73","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOag6P5z8Uxgcjl","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/FavoriteCollectionController.java","80","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOag6P5z8Uxgcjm","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/FavoriteCollectionController.java","86","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOag6P5z8Uxgcjn","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/FavoriteCollectionController.java","93","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOag6P5z8Uxgcjo","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/FavoriteCollectionController.java","99","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOag6P5z8Uxgcjp","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/FavoriteCollectionController.java","104","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOag6P5z8Uxgcjq","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/FavoriteCollectionController.java","110","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOag6P5z8Uxgcjr","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/FavoriteCollectionController.java","116","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOag6P5z8Uxgcjs","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/FavoriteCollectionController.java","121","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOag6P5z8Uxgcjt","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/FavoriteCollectionController.java","126","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOag6P5z8Uxgcju","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/FavoriteCollectionController.java","132","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOag6P5z8Uxgcjv","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/FavoriteCollectionController.java","138","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOag6P5z8Uxgcjw","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/FavoriteCollectionController.java","144","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOag6P5z8Uxgcjx","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/FavoriteCollectionController.java","150","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOrg6P5z8Uxgcmk","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/HelpController.java","31","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOrg6P5z8Uxgcml","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/HelpController.java","38","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOrg6P5z8Uxgcmm","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/HelpController.java","45","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOrg6P5z8Uxgcmn","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/HelpController.java","52","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOrg6P5z8Uxgcmo","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/HelpController.java","60","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOrg6P5z8Uxgcmp","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/HelpController.java","68","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOrg6P5z8Uxgcmq","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/HelpController.java","76","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOrg6P5z8Uxgcmr","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/HelpController.java","84","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOrg6P5z8Uxgcms","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/HelpController.java","92","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOrg6P5z8Uxgcmt","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/HelpController.java","100","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOrg6P5z8Uxgcmu","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/HelpController.java","107","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOrg6P5z8Uxgcmv","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/HelpController.java","114","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOrg6P5z8Uxgcmw","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/HelpController.java","121","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOrg6P5z8Uxgcmx","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/HelpController.java","128","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOrg6P5z8Uxgcmy","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/HelpController.java","133","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOrg6P5z8Uxgcmz","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/HelpController.java","138","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOrg6P5z8Uxgcm0","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/HelpController.java","144","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOrg6P5z8Uxgcm1","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/HelpController.java","150","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOrg6P5z8Uxgcm2","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/HelpController.java","156","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOrg6P5z8Uxgcm3","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/HelpController.java","162","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOrg6P5z8Uxgcm4","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/HelpController.java","168","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOrg6P5z8Uxgcm5","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/HelpController.java","174","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOxg6P5z8Uxgcng","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/IndustryInformationController.java","29","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOxg6P5z8Uxgcnh","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/IndustryInformationController.java","36","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOxg6P5z8Uxgcni","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/IndustryInformationController.java","43","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOxg6P5z8Uxgcnj","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/IndustryInformationController.java","50","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOxg6P5z8Uxgcnk","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/IndustryInformationController.java","57","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOig6P5z8Uxgclc","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/KfConfigController.java","30","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOig6P5z8Uxgcld","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/KfConfigController.java","37","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOGg6P5z8UxgcgU","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/MemberIntegralController.java","36","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOGg6P5z8UxgcgV","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/MemberIntegralController.java","43","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOGg6P5z8UxgcgW","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/MemberIntegralController.java","50","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOGg6P5z8UxgcgX","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/MemberIntegralController.java","57","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOGg6P5z8UxgcgY","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/MemberIntegralController.java","64","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOGg6P5z8UxgcgZ","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/MemberIntegralController.java","71","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOGg6P5z8Uxgcga","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/MemberIntegralController.java","78","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOGg6P5z8Uxgcgb","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/MemberIntegralController.java","85","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOGg6P5z8Uxgcgc","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/MemberIntegralController.java","93","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOGg6P5z8Uxgcgd","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/MemberIntegralController.java","100","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOng6P5z8Uxgclj","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsAssemblyBuildingController.java","36","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOng6P5z8Uxgclk","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsAssemblyBuildingController.java","43","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOng6P5z8Uxgcll","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsAssemblyBuildingController.java","50","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOng6P5z8Uxgclm","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsAssemblyBuildingController.java","58","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOng6P5z8Uxgcln","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsAssemblyBuildingController.java","66","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOng6P5z8Uxgclo","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsAssemblyBuildingController.java","74","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOng6P5z8Uxgclp","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsAssemblyBuildingController.java","81","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOng6P5z8Uxgclq","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsAssemblyBuildingController.java","88","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOng6P5z8Uxgclr","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsAssemblyBuildingController.java","96","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOng6P5z8Uxgcls","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsAssemblyBuildingController.java","104","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOng6P5z8Uxgclt","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsAssemblyBuildingController.java","111","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOng6P5z8Uxgclu","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsAssemblyBuildingController.java","118","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOng6P5z8Uxgclv","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsAssemblyBuildingController.java","126","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOng6P5z8Uxgclw","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsAssemblyBuildingController.java","133","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOng6P5z8Uxgclx","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsAssemblyBuildingController.java","140","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOng6P5z8Uxgcly","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsAssemblyBuildingController.java","147","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOng6P5z8Uxgclz","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsAssemblyBuildingController.java","154","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOng6P5z8Uxgcl0","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsAssemblyBuildingController.java","161","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOng6P5z8Uxgcl1","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsAssemblyBuildingController.java","168","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOng6P5z8Uxgcl2","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsAssemblyBuildingController.java","175","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOng6P5z8Uxgcl3","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsAssemblyBuildingController.java","182","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOng6P5z8Uxgcl4","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsAssemblyBuildingController.java","189","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOng6P5z8Uxgcl5","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsAssemblyBuildingController.java","196","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOng6P5z8Uxgcl6","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsAssemblyBuildingController.java","203","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOng6P5z8Uxgcl7","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsAssemblyBuildingController.java","210","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOng6P5z8Uxgcl8","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsAssemblyBuildingController.java","217","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOng6P5z8Uxgcl9","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsAssemblyBuildingController.java","225","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOng6P5z8Uxgcl-","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsAssemblyBuildingController.java","232","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOng6P5z8Uxgcl_","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsAssemblyBuildingController.java","239","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOng6P5z8UxgcmA","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsAssemblyBuildingController.java","246","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOeg6P5z8UxgckH","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsController.java","33","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOeg6P5z8UxgckI","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsController.java","40","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOeg6P5z8UxgckJ","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsController.java","47","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOeg6P5z8UxgckK","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsController.java","54","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOeg6P5z8UxgckL","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsController.java","62","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOeg6P5z8UxgckM","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsController.java","70","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOeg6P5z8UxgckN","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsController.java","78","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOeg6P5z8UxgckO","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsController.java","85","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOeg6P5z8UxgckP","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsController.java","93","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOeg6P5z8UxgckQ","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsController.java","100","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOeg6P5z8UxgckR","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsController.java","107","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOeg6P5z8UxgckS","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsController.java","115","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOeg6P5z8UxgckT","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsController.java","123","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOeg6P5z8UxgckU","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsController.java","130","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOeg6P5z8UxgckV","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsController.java","137","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOeg6P5z8UxgckW","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsController.java","145","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOeg6P5z8UxgckX","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsController.java","152","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOeg6P5z8UxgckY","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsController.java","159","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOeg6P5z8UxgckZ","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsController.java","167","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOeg6P5z8Uxgcka","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsController.java","173","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOeg6P5z8Uxgckb","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsController.java","179","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOeg6P5z8Uxgckc","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsController.java","185","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOeg6P5z8Uxgckd","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsController.java","192","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOeg6P5z8Uxgcke","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsController.java","199","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOeg6P5z8Uxgckf","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsController.java","206","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOeg6P5z8Uxgckg","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsController.java","212","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOeg6P5z8Uxgckh","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsController.java","217","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOeg6P5z8Uxgcki","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsController.java","222","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOeg6P5z8Uxgckj","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsController.java","227","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOeg6P5z8Uxgckk","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsController.java","232","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOeg6P5z8Uxgckl","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsController.java","237","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOeg6P5z8Uxgckm","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsController.java","242","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOeg6P5z8Uxgckn","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsController.java","248","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOeg6P5z8Uxgcko","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsController.java","253","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOeg6P5z8Uxgckp","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsController.java","258","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOeg6P5z8Uxgckq","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsController.java","263","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOeg6P5z8Uxgckr","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsController.java","268","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOeg6P5z8Uxgcks","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsController.java","273","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOeg6P5z8Uxgckt","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsController.java","278","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOeg6P5z8Uxgcku","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsController.java","283","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOeg6P5z8Uxgckv","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsController.java","288","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UO5g6P5z8Uxgcop","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsTechnologyEquipmentController.java","36","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UO5g6P5z8Uxgcoq","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsTechnologyEquipmentController.java","43","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UO5g6P5z8Uxgcor","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsTechnologyEquipmentController.java","50","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UO5g6P5z8Uxgcos","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsTechnologyEquipmentController.java","58","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UO5g6P5z8Uxgcot","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsTechnologyEquipmentController.java","65","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UO5g6P5z8Uxgcou","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsTechnologyEquipmentController.java","72","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UO5g6P5z8Uxgcov","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsTechnologyEquipmentController.java","79","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UO5g6P5z8Uxgcow","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsTechnologyEquipmentController.java","86","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UO5g6P5z8Uxgcox","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsTechnologyEquipmentController.java","93","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UO5g6P5z8Uxgcoy","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsTechnologyEquipmentController.java","100","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UO5g6P5z8Uxgcoz","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsTechnologyEquipmentController.java","107","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UO5g6P5z8Uxgco0","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsTechnologyEquipmentController.java","114","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UO5g6P5z8Uxgco1","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsTechnologyEquipmentController.java","121","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UO5g6P5z8Uxgco2","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsTechnologyEquipmentController.java","129","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UO5g6P5z8Uxgco3","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsTechnologyEquipmentController.java","136","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UO5g6P5z8Uxgco4","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsTechnologyEquipmentController.java","144","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UO5g6P5z8Uxgco5","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsTechnologyEquipmentController.java","152","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UO5g6P5z8Uxgco6","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsTechnologyEquipmentController.java","160","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UO5g6P5z8Uxgco7","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsTechnologyEquipmentController.java","167","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UO5g6P5z8Uxgco8","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsTechnologyEquipmentController.java","174","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UO5g6P5z8Uxgco9","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsTechnologyEquipmentController.java","181","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UO5g6P5z8Uxgco-","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsTechnologyEquipmentController.java","188","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UO5g6P5z8Uxgco_","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsTechnologyEquipmentController.java","196","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UO5g6P5z8UxgcpA","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsTechnologyEquipmentController.java","203","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UO5g6P5z8UxgcpB","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsTechnologyEquipmentController.java","210","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UO5g6P5z8UxgcpC","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsTechnologyEquipmentController.java","218","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UO5g6P5z8UxgcpD","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsTechnologyEquipmentController.java","225","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UO5g6P5z8UxgcpE","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsTechnologyEquipmentController.java","232","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UO5g6P5z8UxgcpF","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsTechnologyEquipmentController.java","239","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UO5g6P5z8UxgcpG","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/NewsTechnologyEquipmentController.java","247","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOTg6P5z8UxgciZ","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/PriceInfoController.java","45","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOTg6P5z8Uxgcia","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/PriceInfoController.java","52","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOTg6P5z8Uxgcib","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/PriceInfoController.java","59","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOTg6P5z8Uxgcic","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/PriceInfoController.java","66","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOTg6P5z8Uxgcid","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/PriceInfoController.java","73","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOTg6P5z8Uxgcie","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/PriceInfoController.java","80","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOTg6P5z8Uxgcif","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/PriceInfoController.java","88","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOTg6P5z8Uxgcig","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/PriceInfoController.java","96","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOTg6P5z8Uxgcih","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/PriceInfoController.java","103","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOTg6P5z8Uxgcii","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/PriceInfoController.java","108","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOTg6P5z8Uxgcij","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/PriceInfoController.java","113","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOTg6P5z8Uxgcik","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/PriceInfoController.java","118","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOTg6P5z8Uxgcil","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/PriceInfoController.java","125","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOTg6P5z8Uxgcim","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/PriceInfoController.java","132","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOTg6P5z8Uxgcin","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/PriceInfoController.java","140","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOTg6P5z8Uxgcio","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/PriceInfoController.java","146","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOTg6P5z8Uxgcip","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/PriceInfoController.java","151","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UN_g6P5z8UxgceX","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ShopController.java","35","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UN_g6P5z8UxgceY","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ShopController.java","42","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UN_g6P5z8UxgceZ","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ShopController.java","49","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UN_g6P5z8Uxgcea","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ShopController.java","55","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UN_g6P5z8Uxgceb","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ShopController.java","62","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UN_g6P5z8Uxgcec","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ShopController.java","70","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UN_g6P5z8Uxgced","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ShopController.java","78","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UN_g6P5z8Uxgcee","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ShopController.java","86","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UN_g6P5z8Uxgcef","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ShopController.java","94","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UN_g6P5z8Uxgceg","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ShopController.java","102","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UN_g6P5z8Uxgceh","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ShopController.java","108","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UN_g6P5z8Uxgcei","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ShopController.java","114","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UN_g6P5z8Uxgcej","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ShopController.java","121","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UN_g6P5z8Uxgcek","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ShopController.java","129","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UN_g6P5z8Uxgcel","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ShopController.java","136","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UN_g6P5z8Uxgcem","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ShopController.java","143","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UN_g6P5z8Uxgcen","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ShopController.java","150","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UN_g6P5z8Uxgceo","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ShopController.java","157","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UN_g6P5z8Uxgcep","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ShopController.java","164","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UN_g6P5z8Uxgceq","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ShopController.java","171","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UN_g6P5z8Uxgcer","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ShopController.java","179","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UN_g6P5z8Uxgces","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ShopController.java","186","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UN_g6P5z8Uxgcet","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ShopController.java","193","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UN_g6P5z8Uxgceu","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ShopController.java","201","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UN_g6P5z8Uxgcev","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ShopController.java","208","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UN_g6P5z8Uxgcew","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ShopController.java","216","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UN_g6P5z8Uxgcex","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ShopController.java","223","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UN_g6P5z8Uxgcey","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ShopController.java","231","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UN_g6P5z8Uxgcez","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ShopController.java","239","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UN_g6P5z8Uxgce0","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ShopController.java","247","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UN_g6P5z8Uxgce1","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ShopController.java","254","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UN_g6P5z8Uxgce2","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ShopController.java","262","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UN_g6P5z8Uxgce3","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ShopController.java","270","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UN_g6P5z8Uxgce4","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ShopController.java","276","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UN_g6P5z8Uxgce5","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ShopController.java","288","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UN_g6P5z8Uxgce6","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ShopController.java","300","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UN_g6P5z8Uxgce7","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ShopController.java","308","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UN_g6P5z8Uxgce8","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ShopController.java","316","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UN_g6P5z8Uxgce9","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ShopController.java","324","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UN_g6P5z8Uxgce-","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ShopController.java","332","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UN_g6P5z8Uxgce_","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ShopController.java","339","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UN_g6P5z8UxgcfA","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ShopController.java","346","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UN_g6P5z8UxgcfB","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ShopController.java","354","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UN_g6P5z8UxgcfC","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ShopController.java","362","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UN_g6P5z8UxgcfD","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ShopController.java","370","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UN_g6P5z8UxgcfE","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ShopController.java","378","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UN_g6P5z8UxgcfF","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ShopController.java","386","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UN_g6P5z8UxgcfG","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ShopController.java","394","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UN_g6P5z8UxgcfH","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ShopController.java","402","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UN_g6P5z8UxgcfI","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ShopController.java","410","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UN_g6P5z8UxgcfJ","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ShopController.java","418","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UN_g6P5z8UxgcfK","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ShopController.java","426","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UN_g6P5z8UxgcfL","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ShopController.java","434","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UN_g6P5z8UxgcfM","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ShopController.java","441","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UN_g6P5z8UxgcfN","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/ShopController.java","449","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOXg6P5z8Uxgci7","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/TenderingInfoController.java","34","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOXg6P5z8Uxgci8","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/TenderingInfoController.java","41","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOXg6P5z8Uxgci9","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/TenderingInfoController.java","47","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOXg6P5z8Uxgci-","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/TenderingInfoController.java","54","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOXg6P5z8Uxgci_","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/TenderingInfoController.java","60","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOXg6P5z8UxgcjA","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/TenderingInfoController.java","67","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOXg6P5z8UxgcjB","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/TenderingInfoController.java","74","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOXg6P5z8UxgcjC","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/TenderingInfoController.java","81","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOXg6P5z8UxgcjD","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/TenderingInfoController.java","89","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOXg6P5z8UxgcjE","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/TenderingInfoController.java","96","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOXg6P5z8UxgcjF","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/TenderingInfoController.java","103","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOXg6P5z8UxgcjG","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/TenderingInfoController.java","111","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOXg6P5z8UxgcjH","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/TenderingInfoController.java","117","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOXg6P5z8UxgcjI","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/TenderingInfoController.java","123","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOXg6P5z8UxgcjJ","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/TenderingInfoController.java","129","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOXg6P5z8UxgcjK","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/TenderingInfoController.java","135","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UOXg6P5z8UxgcjL","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/controller/TenderingInfoController.java","143","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UPJg6P5z8Uxgcpx","java:S112","Ecommerce-all-sprint2:service/service-information/information/src/main/java/com/ecommerce/information/util/MapUtils.java","76","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7UjUg6P5z8UxgdaH","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/biz/aspect/OperationLimitAspect.java","91","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-23T07:35:29+0000"
"AZhP7UfAg6P5z8UxgdLy","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/biz/impl/StorehouseBizService.java","66","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-24T03:07:02+0000"
"AZhP7UfAg6P5z8UxgdL0","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/biz/impl/StorehouseBizService.java","90","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-24T03:07:02+0000"
"AZhP7UfAg6P5z8UxgdL1","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/biz/impl/StorehouseBizService.java","114","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-24T03:07:02+0000"
"AZhP7UfAg6P5z8UxgdL2","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/biz/impl/StorehouseBizService.java","139","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-24T03:07:02+0000"
"AZhP7Uk2g6P5z8Uxgdek","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/DeliveryBillController.java","102","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Uk2g6P5z8Uxgden","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/DeliveryBillController.java","216","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7UlUg6P5z8Uxgdfq","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/PickingBillController.java","54","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7UlUg6P5z8Uxgdfr","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/PickingBillController.java","61","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7UlUg6P5z8Uxgdfs","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/PickingBillController.java","68","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7UlUg6P5z8Uxgdft","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/PickingBillController.java","75","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7UlUg6P5z8Uxgdfu","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/PickingBillController.java","82","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7UlUg6P5z8Uxgdfv","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/PickingBillController.java","89","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7UlUg6P5z8Uxgdfw","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/PickingBillController.java","96","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7UlUg6P5z8Uxgdfx","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/PickingBillController.java","103","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7UlUg6P5z8Uxgdfy","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/PickingBillController.java","110","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7UlUg6P5z8Uxgdfz","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/PickingBillController.java","117","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7UlUg6P5z8Uxgdf0","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/PickingBillController.java","124","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7UlUg6P5z8Uxgdf1","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/PickingBillController.java","131","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7UlUg6P5z8Uxgdf2","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/PickingBillController.java","137","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7UlUg6P5z8Uxgdf3","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/PickingBillController.java","143","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7UlUg6P5z8Uxgdf4","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/PickingBillController.java","150","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhWHHmKg6P5z8UxnTZW","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/PlatformStoreController.java","33","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-29T01:57:36+0000"
"AZhWHHl7g6P5z8UxnTZA","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/PorterController.java","38","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-29T01:57:36+0000"
"AZhWHHl7g6P5z8UxnTZB","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/PorterController.java","44","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-29T01:57:36+0000"
"AZhWHHl7g6P5z8UxnTZC","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/PorterController.java","50","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-29T01:57:36+0000"
"AZhWHHl7g6P5z8UxnTZD","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/PorterController.java","56","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-29T01:57:36+0000"
"AZhWHHmWg6P5z8UxnTZY","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/PorterageController.java","45","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-29T01:57:36+0000"
"AZhWHHmWg6P5z8UxnTZZ","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/PorterageController.java","52","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-29T01:57:36+0000"
"AZhWHHmWg6P5z8UxnTZa","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/PorterageController.java","59","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-29T01:57:36+0000"
"AZhWHHmWg6P5z8UxnTZb","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/PorterageController.java","66","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-29T01:57:36+0000"
"AZhWHHmWg6P5z8UxnTZc","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/PorterageController.java","73","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-29T01:57:36+0000"
"AZhWHHmWg6P5z8UxnTZd","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/PorterageController.java","80","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-29T01:57:36+0000"
"AZhWHHmWg6P5z8UxnTZe","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/PorterageController.java","87","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-29T01:57:36+0000"
"AZhWHHmWg6P5z8UxnTZf","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/PorterageController.java","94","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-29T01:57:36+0000"
"AZhP7Ulyg6P5z8Uxgdji","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/ProductInfoController.java","33","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhWHHlVg6P5z8UxnTY6","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/ProxySyncRecordController.java","38","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-29T01:57:36+0000"
"AZhWHHlVg6P5z8UxnTY7","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/ProxySyncRecordController.java","45","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-29T01:57:36+0000"
"AZhP7UmGg6P5z8UxgdkA","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/RuleComputeController.java","39","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7UmGg6P5z8UxgdkB","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/RuleComputeController.java","46","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7UmGg6P5z8UxgdkC","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/RuleComputeController.java","54","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7UmGg6P5z8UxgdkD","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/RuleComputeController.java","60","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7UmKg6P5z8UxgdkI","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/RuleController.java","35","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7UmKg6P5z8UxgdkJ","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/RuleController.java","42","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7UmKg6P5z8UxgdkK","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/RuleController.java","49","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7UmKg6P5z8UxgdkL","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/RuleController.java","56","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7UmKg6P5z8UxgdkM","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/RuleController.java","63","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhWHHmEg6P5z8UxnTZI","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/SettlementController.java","42","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-29T01:57:36+0000"
"AZhWHHmEg6P5z8UxnTZJ","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/SettlementController.java","49","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-29T01:57:36+0000"
"AZhWHHmEg6P5z8UxnTZK","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/SettlementController.java","56","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-29T01:57:36+0000"
"AZhWHHmEg6P5z8UxnTZL","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/SettlementController.java","63","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-29T01:57:36+0000"
"AZhWHHmEg6P5z8UxnTZM","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/SettlementController.java","70","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-29T01:57:36+0000"
"AZhWHHmEg6P5z8UxnTZN","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/SettlementController.java","77","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-29T01:57:36+0000"
"AZhWHHmEg6P5z8UxnTZO","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/SettlementController.java","84","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-29T01:57:36+0000"
"AZhP7Ulbg6P5z8Uxgdgm","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/ShipBillController.java","288","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulbg6P5z8Uxgdgn","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/ShipBillController.java","294","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulbg6P5z8Uxgdgo","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/ShipBillController.java","300","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulbg6P5z8Uxgdgp","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/ShipBillController.java","306","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulbg6P5z8Uxgdgq","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/ShipBillController.java","312","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulbg6P5z8Uxgdgr","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/ShipBillController.java","318","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulbg6P5z8Uxgdgs","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/ShipBillController.java","324","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulbg6P5z8Uxgdgt","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/ShipBillController.java","330","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulbg6P5z8Uxgdgv","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/ShipBillController.java","342","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulbg6P5z8Uxgdgw","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/ShipBillController.java","348","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulbg6P5z8Uxgdgx","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/ShipBillController.java","354","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulbg6P5z8Uxgdgy","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/ShipBillController.java","360","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulbg6P5z8Uxgdg1","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/ShipBillController.java","509","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7UlDg6P5z8UxgdfL","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/ShipBillExternalController.java","38","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7UlDg6P5z8UxgdfM","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/ShipBillExternalController.java","46","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7UlDg6P5z8UxgdfN","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/ShipBillExternalController.java","54","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7UlDg6P5z8UxgdfO","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/ShipBillExternalController.java","61","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7UlDg6P5z8UxgdfP","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/ShipBillExternalController.java","68","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7UlDg6P5z8UxgdfQ","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/ShipBillExternalController.java","75","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7UlDg6P5z8UxgdfR","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/ShipBillExternalController.java","83","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7UlDg6P5z8UxgdfS","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/ShipBillExternalController.java","90","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7UlDg6P5z8UxgdfT","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/ShipBillExternalController.java","97","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7UlDg6P5z8UxgdfU","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/ShipBillExternalController.java","104","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulog6P5z8Uxgdhi","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillController.java","106","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulog6P5z8Uxgdhj","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillController.java","113","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulog6P5z8Uxgdhk","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillController.java","120","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulog6P5z8Uxgdhl","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillController.java","127","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulog6P5z8Uxgdhm","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillController.java","134","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulog6P5z8Uxgdhn","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillController.java","141","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulog6P5z8Uxgdho","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillController.java","148","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulog6P5z8Uxgdhq","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillController.java","161","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulog6P5z8Uxgdhr","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillController.java","168","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulog6P5z8Uxgdhs","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillController.java","175","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulog6P5z8Uxgdht","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillController.java","182","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulog6P5z8Uxgdhu","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillController.java","188","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulog6P5z8Uxgdhv","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillController.java","194","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulog6P5z8Uxgdhw","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillController.java","201","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulog6P5z8Uxgdhx","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillController.java","208","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulog6P5z8Uxgdhy","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillController.java","215","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulog6P5z8Uxgdhz","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillController.java","222","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulog6P5z8Uxgdh0","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillController.java","229","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulog6P5z8Uxgdh1","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillController.java","236","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulog6P5z8Uxgdh2","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillController.java","243","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulog6P5z8Uxgdh3","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillController.java","250","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulog6P5z8Uxgdh4","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillController.java","257","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulog6P5z8Uxgdh5","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillController.java","264","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulog6P5z8Uxgdh6","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillController.java","271","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulog6P5z8Uxgdh7","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillController.java","284","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulog6P5z8Uxgdh8","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillController.java","291","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulog6P5z8Uxgdh9","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillController.java","298","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulog6P5z8Uxgdh-","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillController.java","305","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulog6P5z8Uxgdh_","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillController.java","312","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulog6P5z8UxgdiA","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillController.java","319","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulog6P5z8UxgdiB","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillController.java","326","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulog6P5z8UxgdiC","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillController.java","333","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulog6P5z8UxgdiD","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillController.java","340","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulog6P5z8UxgdiE","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillController.java","347","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulog6P5z8UxgdiF","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillController.java","354","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulog6P5z8UxgdiG","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillController.java","361","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulog6P5z8UxgdiH","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillController.java","368","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulog6P5z8UxgdiI","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillController.java","375","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulog6P5z8UxgdiJ","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillController.java","382","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulog6P5z8UxgdiK","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillController.java","389","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulog6P5z8UxgdiL","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillController.java","396","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulog6P5z8UxgdiM","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillController.java","403","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulog6P5z8UxgdiN","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillController.java","410","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulog6P5z8UxgdiO","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillController.java","417","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulog6P5z8UxgdiP","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillController.java","424","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulog6P5z8UxgdiQ","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillController.java","431","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulog6P5z8UxgdiR","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillController.java","438","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulog6P5z8UxgdiS","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillController.java","445","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulog6P5z8UxgdiT","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillController.java","452","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulog6P5z8UxgdiU","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillController.java","458","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulog6P5z8Uxgdic","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillController.java","522","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulog6P5z8Uxgdid","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillController.java","528","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulog6P5z8Uxgdie","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillController.java","534","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulog6P5z8Uxgdif","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillController.java","540","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ulog6P5z8Uxgdig","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillController.java","546","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ul3g6P5z8Uxgdjm","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillExternalController.java","41","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ul3g6P5z8Uxgdjn","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillExternalController.java","51","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ul3g6P5z8Uxgdjo","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillExternalController.java","57","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ul3g6P5z8Uxgdjp","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillExternalController.java","63","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ul3g6P5z8Uxgdjq","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillExternalController.java","69","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ul3g6P5z8Uxgdjr","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillExternalController.java","75","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ul3g6P5z8Uxgdjs","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillExternalController.java","87","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ul3g6P5z8Uxgdjt","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillExternalController.java","93","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ul3g6P5z8Uxgdju","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillExternalController.java","99","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ul3g6P5z8Uxgdjv","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/controller/WaybillExternalController.java","106","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7UP9g6P5z8UxgcrN","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/push/umeng/UmengNotification.java","36","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7UQDg6P5z8UxgcrU","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/push/umeng/ios/IOSNotification.java","54","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7UQDg6P5z8UxgcrV","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/push/umeng/ios/IOSNotification.java","56","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-23T07:35:29+0000"
"AZhP7UQDg6P5z8UxgcrW","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/push/umeng/ios/IOSNotification.java","74","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ukng6P5z8Uxgdcl","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/util/HTTPUtils.java","86","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ukng6P5z8Uxgdcm","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/util/HTTPUtils.java","184","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ukng6P5z8Uxgdcn","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/util/HTTPUtils.java","201","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7Ukng6P5z8Uxgdco","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/util/HTTPUtils.java","321","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7UkXg6P5z8Uxgda4","java:S112","Ecommerce-all-sprint2:service/service-logistics/logistics/src/main/java/com/ecommerce/logistics/util/db/FieldUtils.java","60","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-22T10:27:08+0000"
"AZhP7TrYg6P5z8Uxgbrv","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/AccountChangeHistoryController.java","37","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrYg6P5z8Uxgbrw","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/AccountChangeHistoryController.java","43","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrYg6P5z8Uxgbrx","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/AccountChangeHistoryController.java","49","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrYg6P5z8Uxgbry","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/AccountChangeHistoryController.java","56","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tq9g6P5z8Uxgbnx","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/AccountLoginInfoController.java","38","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tq9g6P5z8Uxgbny","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/AccountLoginInfoController.java","44","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tq9g6P5z8Uxgbnz","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/AccountLoginInfoController.java","50","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tq9g6P5z8Uxgbn0","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/AccountLoginInfoController.java","56","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tq9g6P5z8Uxgbn1","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/AccountLoginInfoController.java","62","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tq9g6P5z8Uxgbn2","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/AccountLoginInfoController.java","68","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrKg6P5z8UxgbpN","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/AccountStoreRelationController.java","34","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrKg6P5z8UxgbpO","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/AccountStoreRelationController.java","42","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrKg6P5z8UxgbpP","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/AccountStoreRelationController.java","49","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrKg6P5z8UxgbpQ","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/AccountStoreRelationController.java","56","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrKg6P5z8UxgbpR","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/AccountStoreRelationController.java","64","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrKg6P5z8UxgbpS","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/AccountStoreRelationController.java","72","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrKg6P5z8UxgbpT","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/AccountStoreRelationController.java","79","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrKg6P5z8UxgbpU","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/AccountStoreRelationController.java","86","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrKg6P5z8UxgbpV","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/AccountStoreRelationController.java","93","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrKg6P5z8UxgbpW","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/AccountStoreRelationController.java","100","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrEg6P5z8Uxgboc","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/BuyerAndReferrerController.java","30","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrEg6P5z8Uxgbod","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/BuyerAndReferrerController.java","37","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrEg6P5z8Uxgboe","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/BuyerAndReferrerController.java","44","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrEg6P5z8Uxgbof","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/BuyerAndReferrerController.java","51","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrEg6P5z8Uxgbog","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/BuyerAndReferrerController.java","58","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrEg6P5z8Uxgboh","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/BuyerAndReferrerController.java","65","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrEg6P5z8Uxgboi","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/BuyerAndReferrerController.java","72","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrMg6P5z8Uxgbpi","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberAreaController.java","27","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrMg6P5z8Uxgbpj","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberAreaController.java","33","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrMg6P5z8Uxgbpk","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberAreaController.java","41","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrMg6P5z8Uxgbpl","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberAreaController.java","49","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrMg6P5z8Uxgbpm","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberAreaController.java","56","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrMg6P5z8Uxgbpn","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberAreaController.java","63","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrMg6P5z8Uxgbpo","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberAreaController.java","70","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrMg6P5z8Uxgbpp","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberAreaController.java","77","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrMg6P5z8Uxgbpq","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberAreaController.java","84","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrHg6P5z8Uxgboq","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberConfigController.java","34","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrHg6P5z8Uxgbor","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberConfigController.java","42","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrHg6P5z8Uxgbos","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberConfigController.java","50","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrHg6P5z8Uxgbot","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberConfigController.java","58","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrHg6P5z8Uxgbou","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberConfigController.java","66","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrHg6P5z8Uxgbov","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberConfigController.java","73","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrHg6P5z8Uxgbow","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberConfigController.java","80","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrHg6P5z8Uxgbox","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberConfigController.java","87","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrHg6P5z8Uxgboy","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberConfigController.java","94","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrHg6P5z8Uxgboz","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberConfigController.java","100","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrHg6P5z8Uxgbo0","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberConfigController.java","106","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrHg6P5z8Uxgbo1","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberConfigController.java","112","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrHg6P5z8Uxgbo4","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberConfigController.java","130","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrHg6P5z8Uxgbo5","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberConfigController.java","136","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqqg6P5z8Uxgbh9","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","259","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqqg6P5z8Uxgbh-","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","266","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqqg6P5z8Uxgbh_","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","273","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqqg6P5z8UxgbiA","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","280","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqqg6P5z8UxgbiB","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","288","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqqg6P5z8UxgbiC","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","296","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqqg6P5z8UxgbiD","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","303","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqqg6P5z8UxgbiE","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","311","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqqg6P5z8UxgbiF","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","372","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqqg6P5z8UxgbiG","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","380","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqqg6P5z8UxgbiH","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","388","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqqg6P5z8UxgbiI","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","398","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqqg6P5z8UxgbiJ","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","405","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqqg6P5z8UxgbiK","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","415","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqqg6P5z8UxgbiL","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","434","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqqg6P5z8UxgbiM","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","441","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqqg6P5z8UxgbiN","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","448","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqqg6P5z8UxgbiO","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","456","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqqg6P5z8UxgbiP","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","464","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqqg6P5z8UxgbiQ","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","470","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqqg6P5z8UxgbiR","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","477","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqqg6P5z8UxgbiS","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","483","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqqg6P5z8UxgbiT","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","490","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqqg6P5z8UxgbiU","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","496","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqqg6P5z8UxgbiV","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","503","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqqg6P5z8UxgbiW","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","510","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqqg6P5z8UxgbiX","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","518","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqrg6P5z8UxgbiY","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","523","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqrg6P5z8UxgbiZ","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","535","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqrg6P5z8Uxgbia","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","541","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqrg6P5z8Uxgbib","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","547","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqrg6P5z8Uxgbic","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","554","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqrg6P5z8Uxgbid","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","561","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqrg6P5z8Uxgbie","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","568","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqrg6P5z8Uxgbif","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","575","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqrg6P5z8Uxgbig","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","587","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqrg6P5z8Uxgbih","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","594","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqrg6P5z8Uxgbii","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","602","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqrg6P5z8Uxgbij","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","609","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqrg6P5z8Uxgbil","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","622","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqrg6P5z8Uxgbim","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","629","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqrg6P5z8Uxgbin","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","636","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqrg6P5z8Uxgbio","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","644","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqrg6P5z8Uxgbip","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","651","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqrg6P5z8Uxgbiq","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","658","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqrg6P5z8Uxgbir","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","665","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqrg6P5z8Uxgbis","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","673","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqrg6P5z8Uxgbit","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","681","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqrg6P5z8Uxgbiu","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","689","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqrg6P5z8Uxgbiv","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","696","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqrg6P5z8Uxgbiw","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","704","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqrg6P5z8Uxgbix","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","712","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqrg6P5z8Uxgbiy","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","719","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqrg6P5z8Uxgbiz","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","726","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqrg6P5z8Uxgbi0","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","733","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqrg6P5z8Uxgbi1","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","741","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqrg6P5z8Uxgbi2","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","748","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqrg6P5z8Uxgbi3","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","756","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqrg6P5z8Uxgbi4","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","763","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqrg6P5z8Uxgbi5","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","770","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqrg6P5z8Uxgbi6","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","778","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqrg6P5z8Uxgbi7","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","786","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqrg6P5z8Uxgbi8","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","792","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqrg6P5z8Uxgbi9","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","800","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqrg6P5z8Uxgbi-","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","807","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqrg6P5z8Uxgbi_","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","814","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqrg6P5z8UxgbjA","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","820","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqrg6P5z8UxgbjB","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","827","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqrg6P5z8UxgbjC","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","833","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqrg6P5z8UxgbjD","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","839","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqrg6P5z8UxgbjE","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","846","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqrg6P5z8UxgbjF","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","853","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqrg6P5z8UxgbjG","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","861","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqrg6P5z8UxgbjH","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","869","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqrg6P5z8UxgbjJ","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","883","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqrg6P5z8UxgbjK","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","890","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqrg6P5z8UxgbjL","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","897","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqrg6P5z8UxgbjM","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","904","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqrg6P5z8UxgbjN","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","911","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqrg6P5z8UxgbjO","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","923","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqrg6P5z8UxgbjP","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","930","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tqrg6P5z8UxgbjR","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberController.java","989","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrUg6P5z8UxgbqQ","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberRelationController.java","53","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrUg6P5z8UxgbqR","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberRelationController.java","59","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrUg6P5z8UxgbqS","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberRelationController.java","68","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrUg6P5z8UxgbqT","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberRelationController.java","74","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrUg6P5z8UxgbqU","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberRelationController.java","98","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrUg6P5z8UxgbqV","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberRelationController.java","104","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrUg6P5z8UxgbqW","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberRelationController.java","110","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrUg6P5z8UxgbqX","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberRelationController.java","116","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrUg6P5z8UxgbqY","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberRelationController.java","122","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrUg6P5z8UxgbqZ","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberRelationController.java","128","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrUg6P5z8Uxgbqa","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberRelationController.java","135","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrUg6P5z8Uxgbqb","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberRelationController.java","142","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrUg6P5z8Uxgbqc","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberRelationController.java","149","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrUg6P5z8Uxgbqd","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberRelationController.java","156","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrUg6P5z8Uxgbqe","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberRelationController.java","164","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrUg6P5z8Uxgbqf","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberRelationController.java","169","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrUg6P5z8Uxgbqg","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberRelationController.java","175","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrUg6P5z8Uxgbqh","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberRelationController.java","182","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrUg6P5z8Uxgbqi","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberRelationController.java","188","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrUg6P5z8Uxgbqj","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberRelationController.java","194","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrUg6P5z8Uxgbqk","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberRelationController.java","202","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrUg6P5z8Uxgbql","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberRelationController.java","209","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrUg6P5z8Uxgbqm","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberRelationController.java","216","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrUg6P5z8Uxgbqn","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberRelationController.java","224","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrUg6P5z8Uxgbqo","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberRelationController.java","232","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrUg6P5z8Uxgbqp","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberRelationController.java","240","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrUg6P5z8Uxgbqq","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberRelationController.java","246","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrUg6P5z8Uxgbqr","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberRelationController.java","253","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrUg6P5z8Uxgbqs","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberRelationController.java","260","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrUg6P5z8Uxgbqt","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberRelationController.java","272","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrUg6P5z8Uxgbqu","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberRelationController.java","279","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrUg6P5z8Uxgbqv","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberRelationController.java","287","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrUg6P5z8Uxgbqw","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberRelationController.java","294","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrUg6P5z8Uxgbqx","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberRelationController.java","302","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrUg6P5z8Uxgbqy","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberRelationController.java","310","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrUg6P5z8Uxgbqz","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberRelationController.java","318","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrUg6P5z8Uxgbq0","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberRelationController.java","325","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrUg6P5z8Uxgbq1","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberRelationController.java","333","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrUg6P5z8Uxgbq2","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberRelationController.java","340","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrUg6P5z8Uxgbq3","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberRelationController.java","347","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrUg6P5z8Uxgbq4","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberRelationController.java","354","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrUg6P5z8Uxgbq5","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberRelationController.java","361","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrUg6P5z8Uxgbq6","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/MemberRelationController.java","367","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tq0g6P5z8UxgblD","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/OrgInfoController.java","34","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tq0g6P5z8UxgblE","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/OrgInfoController.java","42","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tq0g6P5z8UxgblF","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/OrgInfoController.java","50","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tq0g6P5z8UxgblG","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/OrgInfoController.java","58","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tq0g6P5z8UxgblH","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/OrgInfoController.java","65","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tq0g6P5z8UxgblI","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/OrgInfoController.java","72","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tq0g6P5z8UxgblJ","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/OrgInfoController.java","78","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tq0g6P5z8UxgblK","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/OrgInfoController.java","85","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tq0g6P5z8UxgblL","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/OrgInfoController.java","92","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tq0g6P5z8UxgblM","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/OrgInfoController.java","99","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tq0g6P5z8UxgblN","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/OrgInfoController.java","106","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tq0g6P5z8UxgblO","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/OrgInfoController.java","113","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tq_g6P5z8Uxgbn9","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/SellerCarrierBuyerRelationController.java","32","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tq_g6P5z8Uxgbn-","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/SellerCarrierBuyerRelationController.java","38","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tq_g6P5z8Uxgbn_","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/SellerCarrierBuyerRelationController.java","45","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tq_g6P5z8UxgboA","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/SellerCarrierBuyerRelationController.java","52","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tq_g6P5z8UxgboB","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/SellerCarrierBuyerRelationController.java","59","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Tq_g6P5z8UxgboC","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/SellerCarrierBuyerRelationController.java","66","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrCg6P5z8UxgboK","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/TaxInfoController.java","34","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrCg6P5z8UxgboL","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/TaxInfoController.java","42","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrCg6P5z8UxgboM","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/TaxInfoController.java","50","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrCg6P5z8UxgboN","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/TaxInfoController.java","58","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrCg6P5z8UxgboO","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/TaxInfoController.java","66","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrCg6P5z8UxgboP","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/TaxInfoController.java","74","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrCg6P5z8UxgboQ","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/TaxInfoController.java","81","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TrCg6P5z8UxgboR","java:S112","Ecommerce-all-sprint2:service/service-member/member/src/main/java/com/ecommerce/member/controller/TaxInfoController.java","88","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VY4g6P5z8UxgfWT","java:S112","Ecommerce-all-sprint2:service/service-order/order/src/main/java/com/ecommerce/order/biz/IOrderErpBiz.java","34","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VY4g6P5z8UxgfWU","java:S112","Ecommerce-all-sprint2:service/service-order/order/src/main/java/com/ecommerce/order/biz/IOrderErpBiz.java","49","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VY4g6P5z8UxgfWV","java:S112","Ecommerce-all-sprint2:service/service-order/order/src/main/java/com/ecommerce/order/biz/IOrderErpBiz.java","65","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VY4g6P5z8UxgfWW","java:S112","Ecommerce-all-sprint2:service/service-order/order/src/main/java/com/ecommerce/order/biz/IOrderErpBiz.java","73","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VY4g6P5z8UxgfWX","java:S112","Ecommerce-all-sprint2:service/service-order/order/src/main/java/com/ecommerce/order/biz/IOrderErpBiz.java","81","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhWHG8kg6P5z8UxnTXv","java:S112","Ecommerce-all-sprint2:service/service-order/order/src/main/java/com/ecommerce/order/controller/MonitorJudgeController.java","32","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-29T01:57:36+0000"
"AZhWHG8yg6P5z8UxnTX4","java:S112","Ecommerce-all-sprint2:service/service-order/order/src/main/java/com/ecommerce/order/controller/OrderExportController.java","36","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-29T01:57:36+0000"
"AZhP7Vf0g6P5z8Uxgfyd","java:S112","Ecommerce-all-sprint2:service/service-order/order/src/main/java/com/ecommerce/order/fsm/builder/IStateMachineBuilder.java","16","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VgGg6P5z8Uxgfyn","java:S112","Ecommerce-all-sprint2:service/service-order/order/src/main/java/com/ecommerce/order/fsm/manager/AbstractStateMachineEventManager.java","35","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VgGg6P5z8Uxgfyo","java:S112","Ecommerce-all-sprint2:service/service-order/order/src/main/java/com/ecommerce/order/fsm/manager/AbstractStateMachineEventManager.java","37","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VfIg6P5z8Uxgfu6","java:S112","Ecommerce-all-sprint2:service/service-order/order/src/main/java/com/ecommerce/order/fsm/service/IOrderErpService.java","40","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VfIg6P5z8Uxgfu7","java:S112","Ecommerce-all-sprint2:service/service-order/order/src/main/java/com/ecommerce/order/fsm/service/IOrderErpService.java","55","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VfIg6P5z8Uxgfu8","java:S112","Ecommerce-all-sprint2:service/service-order/order/src/main/java/com/ecommerce/order/fsm/service/IOrderErpService.java","71","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VfIg6P5z8Uxgfu9","java:S112","Ecommerce-all-sprint2:service/service-order/order/src/main/java/com/ecommerce/order/fsm/service/IOrderErpService.java","79","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VfIg6P5z8Uxgfu-","java:S112","Ecommerce-all-sprint2:service/service-order/order/src/main/java/com/ecommerce/order/fsm/service/IOrderErpService.java","87","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VfIg6P5z8Uxgfu_","java:S112","Ecommerce-all-sprint2:service/service-order/order/src/main/java/com/ecommerce/order/fsm/service/IOrderErpService.java","112","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VbHg6P5z8UxgfdA","java:S112","Ecommerce-all-sprint2:service/service-order/order/src/main/java/com/ecommerce/order/service/message/IMessageService.java","17","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7VbHg6P5z8UxgfdB","java:S112","Ecommerce-all-sprint2:service/service-order/order/src/main/java/com/ecommerce/order/service/message/IMessageService.java","24","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TRHg6P5z8UxgaS_","java:S112","Ecommerce-all-sprint2:service/service-pay/pay/src/main/java/com/ecommerce/pay/util/SignatureUtil.java","27","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TOlg6P5z8UxgaIe","java:S112","Ecommerce-all-sprint2:service/service-pay/pay/src/main/java/com/ecommerce/pay/v2/util/wechat/WXPayUtil.java","244","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TOlg6P5z8UxgaIf","java:S112","Ecommerce-all-sprint2:service/service-pay/pay/src/main/java/com/ecommerce/pay/v2/util/wechat/WXPayUtil.java","269","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7TOlg6P5z8UxgaIg","java:S112","Ecommerce-all-sprint2:service/service-pay/pay/src/main/java/com/ecommerce/pay/v2/util/wechat/WXPayUtil.java","287","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLqg6P5z8UxgiSt","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/AttachmentController.java","172","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLIg6P5z8UxgiL8","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/DatapermController.java","32","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLvg6P5z8UxgiS9","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/FavoriteCollectionController.java","61","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLvg6P5z8UxgiS-","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/FavoriteCollectionController.java","70","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLvg6P5z8UxgiS_","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/FavoriteCollectionController.java","83","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLvg6P5z8UxgiTA","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/FavoriteCollectionController.java","91","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLvg6P5z8UxgiTB","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/FavoriteCollectionController.java","99","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLvg6P5z8UxgiTC","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/FavoriteCollectionController.java","107","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLvg6P5z8UxgiTD","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/FavoriteCollectionController.java","118","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLvg6P5z8UxgiTE","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/FavoriteCollectionController.java","139","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLvg6P5z8UxgiTF","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/FavoriteCollectionController.java","150","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLvg6P5z8UxgiTG","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/FavoriteCollectionController.java","180","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLvg6P5z8UxgiTH","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/FavoriteCollectionController.java","205","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLvg6P5z8UxgiTI","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/FavoriteCollectionController.java","213","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLvg6P5z8UxgiTJ","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/FavoriteCollectionController.java","227","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLvg6P5z8UxgiTK","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/FavoriteCollectionController.java","256","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLvg6P5z8UxgiTL","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/FavoriteCollectionController.java","277","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLvg6P5z8UxgiTM","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/FavoriteCollectionController.java","287","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhfv_wrg6P5z8Uxr1PG","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/FavoriteCollectionController.java","309","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhfv_wrg6P5z8Uxr1PH","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/FavoriteCollectionController.java","330","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhfv_wrg6P5z8Uxr1PI","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/FavoriteCollectionController.java","341","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhfv_wrg6P5z8Uxr1PJ","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/FavoriteCollectionController.java","362","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhfv_wrg6P5z8Uxr1PK","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/FavoriteCollectionController.java","372","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhfv_wrg6P5z8Uxr1PL","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/FavoriteCollectionController.java","394","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhP7WLvg6P5z8UxgiTT","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/FavoriteCollectionController.java","405","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLvg6P5z8UxgiTU","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/FavoriteCollectionController.java","412","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLvg6P5z8UxgiTV","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/FavoriteCollectionController.java","426","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLvg6P5z8UxgiTW","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/FavoriteCollectionController.java","433","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLvg6P5z8UxgiTX","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/FavoriteCollectionController.java","442","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLvg6P5z8UxgiTY","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/FavoriteCollectionController.java","451","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLvg6P5z8UxgiTZ","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/FavoriteCollectionController.java","460","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLyg6P5z8UxgiUT","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/HelpController.java","42","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLyg6P5z8UxgiUU","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/HelpController.java","49","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLyg6P5z8UxgiUV","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/HelpController.java","56","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLyg6P5z8UxgiUW","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/HelpController.java","63","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLyg6P5z8UxgiUX","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/HelpController.java","70","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLyg6P5z8UxgiUY","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/HelpController.java","76","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLyg6P5z8UxgiUZ","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/HelpController.java","82","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WL-g6P5z8UxgiWt","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/IndustryInformationController.java","29","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WL-g6P5z8UxgiWu","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/IndustryInformationController.java","37","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WL-g6P5z8UxgiWv","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/IndustryInformationController.java","45","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WL-g6P5z8UxgiWw","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/IndustryInformationController.java","53","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WL-g6P5z8UxgiWx","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/IndustryInformationController.java","61","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLKg6P5z8UxgiL-","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/InternalMessageController.java","43","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLKg6P5z8UxgiL_","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/InternalMessageController.java","50","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLKg6P5z8UxgiMB","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/InternalMessageController.java","60","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLKg6P5z8UxgiMA","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/InternalMessageController.java","61","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLKg6P5z8UxgiMD","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/InternalMessageController.java","67","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLKg6P5z8UxgiMC","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/InternalMessageController.java","68","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLKg6P5z8UxgiME","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/InternalMessageController.java","74","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLKg6P5z8UxgiMF","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/InternalMessageController.java","82","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLKg6P5z8UxgiMG","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/InternalMessageController.java","92","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhfv_xzg6P5z8Uxr1QD","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/InternalMessageController.java","102","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhP7WLKg6P5z8UxgiMH","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/InternalMessageController.java","103","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLKg6P5z8UxgiMK","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/InternalMessageController.java","109","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLKg6P5z8UxgiMJ","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/InternalMessageController.java","110","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLKg6P5z8UxgiML","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/InternalMessageController.java","116","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhWHI7kg6P5z8UxnTmf","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/KfConfigController.java","32","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-29T01:57:36+0000"
"AZhfv_w2g6P5z8Uxr1PN","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberAreaController.java","36","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhfv_w2g6P5z8Uxr1PO","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberAreaController.java","44","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhP7WMTg6P5z8UxgiaO","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberAreaController.java","52","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WMTg6P5z8UxgiaP","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberAreaController.java","60","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WMTg6P5z8UxgiaQ","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberAreaController.java","68","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhfv_w2g6P5z8Uxr1PP","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberAreaController.java","75","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhP7WMCg6P5z8UxgiW8","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberController.java","98","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WMCg6P5z8UxgiW9","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberController.java","105","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WMCg6P5z8UxgiW-","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberController.java","113","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WMCg6P5z8UxgiW_","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberController.java","122","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WMCg6P5z8UxgiXA","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberController.java","131","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WMCg6P5z8UxgiXE","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberController.java","181","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WMCg6P5z8UxgiXF","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberController.java","189","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WMCg6P5z8UxgiXG","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberController.java","197","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WMCg6P5z8UxgiXH","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberController.java","205","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WMCg6P5z8UxgiXI","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberController.java","214","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WMCg6P5z8UxgiXJ","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberController.java","224","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WMCg6P5z8UxgiXK","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberController.java","236","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WMCg6P5z8UxgiXL","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberController.java","246","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WMCg6P5z8UxgiXM","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberController.java","259","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WMCg6P5z8UxgiXN","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberController.java","272","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WMCg6P5z8UxgiXO","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberController.java","285","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WMCg6P5z8UxgiXP","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberController.java","298","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WMCg6P5z8UxgiXQ","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberController.java","307","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WMCg6P5z8UxgiXR","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberController.java","342","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WMCg6P5z8UxgiXS","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberController.java","355","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WMCg6P5z8UxgiXT","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberController.java","366","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WMCg6P5z8UxgiXU","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberController.java","375","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WMCg6P5z8UxgiXV","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberController.java","383","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WMCg6P5z8UxgiXW","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberController.java","392","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WMCg6P5z8UxgiXX","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberController.java","401","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WMCg6P5z8UxgiXY","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberController.java","409","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WMCg6P5z8UxgiXZ","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberController.java","417","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WMCg6P5z8UxgiXa","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberController.java","424","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WMCg6P5z8UxgiXb","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberController.java","433","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WMCg6P5z8UxgiXc","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberController.java","450","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WMCg6P5z8UxgiXd","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberController.java","458","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WMCg6P5z8UxgiXe","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberController.java","466","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WMCg6P5z8UxgiXf","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberController.java","474","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WMCg6P5z8UxgiXg","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberController.java","482","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WMCg6P5z8UxgiXh","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberController.java","496","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WMCg6P5z8UxgiXi","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberController.java","509","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WMCg6P5z8UxgiXj","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberController.java","562","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WMCg6P5z8UxgiXk","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberController.java","571","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WMCg6P5z8UxgiXm","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberController.java","608","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WMCg6P5z8UxgiXn","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberController.java","629","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WMCg6P5z8UxgiXo","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberController.java","641","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WMCg6P5z8UxgiXp","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberController.java","649","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WMCg6P5z8UxgiXq","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberController.java","657","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WMCg6P5z8UxgiXr","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberController.java","663","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WMCg6P5z8UxgiXs","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberController.java","677","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WMCg6P5z8UxgiXt","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberController.java","691","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WMCg6P5z8UxgiXu","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberController.java","700","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WMCg6P5z8UxgiXv","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberController.java","713","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WMCg6P5z8UxgiXw","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberController.java","722","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WMCg6P5z8UxgiXx","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberController.java","735","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WMCg6P5z8UxgiXy","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberController.java","743","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WMCg6P5z8UxgiXz","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberController.java","756","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WMCg6P5z8UxgiX0","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberController.java","767","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WMCg6P5z8UxgiX1","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberController.java","776","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhfv_xOg6P5z8Uxr1PW","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberRelationController.java","65","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhfv_xOg6P5z8Uxr1PX","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberRelationController.java","75","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhfv_xOg6P5z8Uxr1PY","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberRelationController.java","83","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhP7WLWg6P5z8UxgiNi","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberRelationController.java","93","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLWg6P5z8UxgiNj","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberRelationController.java","103","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLWg6P5z8UxgiNk","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberRelationController.java","111","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLWg6P5z8UxgiNl","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberRelationController.java","119","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhfv_xOg6P5z8Uxr1PZ","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberRelationController.java","135","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhP7WLWg6P5z8UxgiNn","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberRelationController.java","143","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLWg6P5z8UxgiNo","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberRelationController.java","151","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhfv_xOg6P5z8Uxr1Pa","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberRelationController.java","159","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhP7WLWg6P5z8UxgiNq","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberRelationController.java","167","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhfv_xOg6P5z8Uxr1Pb","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberRelationController.java","175","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhfv_xOg6P5z8Uxr1Pc","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberRelationController.java","197","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhfv_xOg6P5z8Uxr1Pd","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberRelationController.java","205","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhfv_xOg6P5z8Uxr1Pe","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberRelationController.java","213","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhP7WLWg6P5z8UxgiNw","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberRelationController.java","221","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhfv_xOg6P5z8Uxr1Pf","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberRelationController.java","229","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhfv_xOg6P5z8Uxr1Pg","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberRelationController.java","240","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhfv_xOg6P5z8Uxr1Ph","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberRelationController.java","248","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhfv_xOg6P5z8Uxr1Pi","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberRelationController.java","259","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhfv_xOg6P5z8Uxr1Pj","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberRelationController.java","267","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhfv_xOg6P5z8Uxr1Pk","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberRelationController.java","275","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhfv_xOg6P5z8Uxr1Pl","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberRelationController.java","285","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhfv_xOg6P5z8Uxr1Pm","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberRelationController.java","293","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhP7WLWg6P5z8UxgiN5","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberRelationController.java","304","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLWg6P5z8UxgiN6","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberRelationController.java","320","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLWg6P5z8UxgiN7","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MemberRelationController.java","328","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLNg6P5z8UxgiMU","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MenuController.java","42","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WL7g6P5z8UxgiWP","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MessageConfigController.java","46","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WL7g6P5z8UxgiWQ","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MessageConfigController.java","64","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WL7g6P5z8UxgiWR","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MessageConfigController.java","79","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WL7g6P5z8UxgiWS","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MessageConfigController.java","86","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WL7g6P5z8UxgiWT","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MessageConfigController.java","93","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WL7g6P5z8UxgiWU","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MessageConfigController.java","100","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WL7g6P5z8UxgiWV","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MessageConfigController.java","106","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WL7g6P5z8UxgiWW","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MessageConfigController.java","112","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WL7g6P5z8UxgiWX","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MessageConfigController.java","119","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WL7g6P5z8UxgiWY","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MessageConfigController.java","126","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WL7g6P5z8UxgiWZ","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MessageConfigController.java","133","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WL7g6P5z8UxgiWa","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MessageConfigController.java","142","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WL7g6P5z8UxgiWb","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/MessageConfigController.java","161","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhfv_xvg6P5z8Uxr1P6","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/OrgInfoController.java","42","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhfv_xvg6P5z8Uxr1P7","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/OrgInfoController.java","50","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhfv_xvg6P5z8Uxr1P8","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/OrgInfoController.java","58","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhP7WLPg6P5z8UxgiMd","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/OrgInfoController.java","66","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLPg6P5z8UxgiMe","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/OrgInfoController.java","75","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLPg6P5z8UxgiMf","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/OrgInfoController.java","83","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLPg6P5z8UxgiMg","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/OrgInfoController.java","91","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhfv_xvg6P5z8Uxr1P9","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/OrgInfoController.java","99","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhP7WLPg6P5z8UxgiMi","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/OrgInfoController.java","107","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLPg6P5z8UxgiMj","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/OrgInfoController.java","114","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLPg6P5z8UxgiMk","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/OrgInfoController.java","125","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WMOg6P5z8UxgiZ_","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/TenderingInformationController.java","31","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WMOg6P5z8UxgiaA","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/TenderingInformationController.java","39","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WMOg6P5z8UxgiaB","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/TenderingInformationController.java","47","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLSg6P5z8UxgiM1","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/ValueSetController.java","42","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhfv_x-g6P5z8Uxr1QF","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/ValueSetController.java","49","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhP7WLSg6P5z8UxgiM3","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/ValueSetController.java","58","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhfv_x-g6P5z8Uxr1QG","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/ValueSetController.java","65","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhP7WLSg6P5z8UxgiM5","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/ValueSetController.java","74","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhfv_x-g6P5z8Uxr1QH","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/ValueSetController.java","81","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhP7WLSg6P5z8UxgiM7","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/ValueSetController.java","90","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhfv_x-g6P5z8Uxr1QI","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/ValueSetController.java","97","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhfv_x-g6P5z8Uxr1QJ","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/ValueSetController.java","106","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhfv_x-g6P5z8Uxr1QK","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/ValueSetController.java","116","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhfv_x-g6P5z8Uxr1QL","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/ValueSetController.java","125","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhfv_x-g6P5z8Uxr1QM","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/ValueSetController.java","135","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhP7WLSg6P5z8UxgiNB","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/ValueSetController.java","144","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLSg6P5z8UxgiNC","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/ValueSetController.java","151","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLSg6P5z8UxgiND","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/ValueSetController.java","158","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLSg6P5z8UxgiNE","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/ValueSetController.java","164","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WLSg6P5z8UxgiNF","java:S112","Ecommerce-all-sprint2:web/web-base/src/main/java/com/ecommerce/web/base/controller/ValueSetController.java","170","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7Vzgg6P5z8UxgggM","java:S112","Ecommerce-all-sprint2:web/web-buyer/src/main/java/com/ecommerce/web/buyer/controller/PickingBillController.java","83","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V0Hg6P5z8Uxggki","java:S112","Ecommerce-all-sprint2:web/web-buyer/src/main/java/com/ecommerce/web/buyer/controller/ShipBillController.java","380","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V1Pg6P5z8Uxggpc","java:S112","Ecommerce-all-sprint2:web/web-buyer/src/main/java/com/ecommerce/web/buyer/controller/VehicleController.java","368","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V1Pg6P5z8Uxggpd","java:S112","Ecommerce-all-sprint2:web/web-buyer/src/main/java/com/ecommerce/web/buyer/controller/VehicleController.java","391","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhWHI3jg6P5z8UxnTlk","java:S112","Ecommerce-all-sprint2:web/web-buyer/src/main/java/com/ecommerce/web/buyer/controller/WaybillController.java","390","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-29T01:57:36+0000"
"AZhP7WKKg6P5z8UxgiJN","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/AccountController.java","121","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WKKg6P5z8UxgiJO","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/AccountController.java","152","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WKKg6P5z8UxgiJP","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/AccountController.java","160","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WKKg6P5z8UxgiJQ","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/AccountController.java","168","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WKKg6P5z8UxgiJR","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/AccountController.java","564","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WIrg6P5z8UxgiAJ","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/AdvertisementController.java","75","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WI7g6P5z8UxgiB1","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/AnnouncementController.java","39","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WI7g6P5z8UxgiB2","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/AnnouncementController.java","45","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WI7g6P5z8UxgiB3","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/AnnouncementController.java","50","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WI7g6P5z8UxgiB4","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/AnnouncementController.java","55","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WI7g6P5z8UxgiB5","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/AnnouncementController.java","69","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WJMg6P5z8UxgiC_","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/CartController.java","91","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WJ-g6P5z8UxgiIh","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/EvaluateController.java","46","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WJ-g6P5z8UxgiIi","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/EvaluateController.java","52","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WJ-g6P5z8UxgiIj","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/EvaluateController.java","58","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WIgg6P5z8Uxgh_y","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/GoodsAddItemController.java","39","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhfv_jqg6P5z8Uxr1Oa","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/GoodsAddItemController.java","54","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhfv_jqg6P5z8Uxr1Ob","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/GoodsAddItemController.java","62","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhfv_jqg6P5z8Uxr1Oc","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/GoodsAddItemController.java","70","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhfv_jqg6P5z8Uxr1Od","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/GoodsAddItemController.java","78","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhWHIvHg6P5z8UxnTf8","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/KfConfigController.java","32","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-29T01:57:36+0000"
"AZhfv_kvg6P5z8Uxr1Om","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/MachineRuleController.java","44","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhP7WJzg6P5z8UxgiHZ","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/MachineRuleController.java","55","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WJzg6P5z8UxgiHa","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/MachineRuleController.java","62","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WJzg6P5z8UxgiHb","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/MachineRuleController.java","69","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhfv_kvg6P5z8Uxr1On","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/MachineRuleController.java","78","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhfv_kvg6P5z8Uxr1Oo","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/MachineRuleController.java","87","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhP7WJzg6P5z8UxgiHe","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/MachineRuleController.java","94","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WJzg6P5z8UxgiHf","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/MachineRuleController.java","104","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WJAg6P5z8UxgiCO","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/NewsAssemblyBuildingController.java","104","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WIug6P5z8UxgiAO","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/NewsController.java","46","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WIug6P5z8UxgiAP","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/NewsController.java","52","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WIug6P5z8UxgiAQ","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/NewsController.java","58","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WIug6P5z8UxgiAR","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/NewsController.java","64","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WIug6P5z8UxgiAS","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/NewsController.java","71","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WIug6P5z8UxgiAT","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/NewsController.java","77","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WIug6P5z8UxgiAU","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/NewsController.java","84","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WIug6P5z8UxgiAV","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/NewsController.java","89","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WIug6P5z8UxgiAW","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/NewsController.java","94","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WIug6P5z8UxgiAX","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/NewsController.java","99","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WIug6P5z8UxgiAY","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/NewsController.java","104","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WIug6P5z8UxgiAZ","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/NewsController.java","109","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WIug6P5z8UxgiAa","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/NewsController.java","114","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WIug6P5z8UxgiAb","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/NewsController.java","119","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WIug6P5z8UxgiAc","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/NewsController.java","124","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WIug6P5z8UxgiAd","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/NewsController.java","130","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WIug6P5z8UxgiAe","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/NewsController.java","137","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WIug6P5z8UxgiAf","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/NewsController.java","160","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WIug6P5z8UxgiAg","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/NewsController.java","175","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WIug6P5z8UxgiAh","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/NewsController.java","190","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WIug6P5z8UxgiAi","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/NewsController.java","205","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WIWg6P5z8Uxgh-7","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/NewsTechnologyEquipmentController.java","35","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WIWg6P5z8Uxgh-8","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/NewsTechnologyEquipmentController.java","41","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WIWg6P5z8Uxgh-9","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/NewsTechnologyEquipmentController.java","49","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WJkg6P5z8UxgiFF","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/PriceInfoController.java","37","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WJkg6P5z8UxgiFG","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/PriceInfoController.java","43","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WJkg6P5z8UxgiFH","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/PriceInfoController.java","49","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WJkg6P5z8UxgiFI","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/PriceInfoController.java","55","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WJkg6P5z8UxgiFJ","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/PriceInfoController.java","61","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WJkg6P5z8UxgiFK","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/PriceInfoController.java","66","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WJkg6P5z8UxgiFL","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/PriceInfoController.java","71","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WJkg6P5z8UxgiFM","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/PriceInfoController.java","76","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WJkg6P5z8UxgiFN","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/PriceInfoController.java","82","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhWHIvug6P5z8UxnTf-","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/ShopController.java","62","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-29T01:57:36+0000"
"AZhWHIvug6P5z8UxnTf_","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/ShopController.java","68","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-29T01:57:36+0000"
"AZhWHIvug6P5z8UxnTgA","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/ShopController.java","74","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-29T01:57:36+0000"
"AZhWHIvug6P5z8UxnTgB","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/ShopController.java","80","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-29T01:57:36+0000"
"AZhWHIvug6P5z8UxnTgC","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/ShopController.java","86","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-29T01:57:36+0000"
"AZhWHIvug6P5z8UxnTgD","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/ShopController.java","92","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-29T01:57:36+0000"
"AZhWHIvug6P5z8UxnTgE","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/ShopController.java","98","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-29T01:57:36+0000"
"AZhWHIvug6P5z8UxnTgF","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/ShopController.java","104","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-29T01:57:36+0000"
"AZhWHIvug6P5z8UxnTgG","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/ShopController.java","111","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-29T01:57:36+0000"
"AZhWHIvug6P5z8UxnTgH","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/ShopController.java","153","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-29T01:57:36+0000"
"AZhWHIvug6P5z8UxnTgI","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/ShopController.java","164","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-29T01:57:36+0000"
"AZhP7WI5g6P5z8UxgiBp","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/TaxInfoController.java","41","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WI5g6P5z8UxgiBq","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/TaxInfoController.java","48","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WI5g6P5z8UxgiBr","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/TaxInfoController.java","58","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WI5g6P5z8UxgiBs","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/TaxInfoController.java","64","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WKDg6P5z8UxgiI4","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/TransportCategoryController.java","44","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WKDg6P5z8UxgiI5","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/TransportCategoryController.java","51","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WKDg6P5z8UxgiI6","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/TransportCategoryController.java","58","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WKDg6P5z8UxgiI7","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/TransportCategoryController.java","65","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WKDg6P5z8UxgiI8","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/TransportCategoryController.java","72","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WKDg6P5z8UxgiI9","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/TransportCategoryController.java","79","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WKDg6P5z8UxgiI-","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/TransportCategoryController.java","86","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WKDg6P5z8UxgiI_","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/TransportCategoryController.java","93","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhWHIwYg6P5z8UxnTgf","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/VehicleTypeController.java","40","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-29T01:57:36+0000"
"AZhWHIwYg6P5z8UxnTgg","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/VehicleTypeController.java","46","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-29T01:57:36+0000"
"AZhP7WJOg6P5z8UxgiDL","java:S112","Ecommerce-all-sprint2:web/web-emall/src/main/java/com/ecommerce/web/emall/controller/WaybillController.java","42","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WNWg6P5z8UxgidE","java:S112","Ecommerce-all-sprint2:web/web-open-api/src/main/java/com/ecommerce/open/web/util/XMLUtil.java","36","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WNWg6P5z8UxgidF","java:S112","Ecommerce-all-sprint2:web/web-open-api/src/main/java/com/ecommerce/open/web/util/XMLUtil.java","53","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-23T02:58:46+0000"
"AZhP7V_ug6P5z8UxghU2","java:S112","Ecommerce-all-sprint2:web/web-platform/src/main/java/com/ecommerce/web/platform/config/EsConfig.java","59","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WFSg6P5z8Uxgh4k","java:S112","Ecommerce-all-sprint2:web/web-platform/src/main/java/com/ecommerce/web/platform/controller/PingAnV2Controller.java","101","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7WFSg6P5z8Uxgh4l","java:S112","Ecommerce-all-sprint2:web/web-platform/src/main/java/com/ecommerce/web/platform/controller/PingAnV2Controller.java","107","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V44g6P5z8Uxgg1N","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/DeliveryBillController.java","151","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V8Ag6P5z8UxghLV","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/OrgInfoController.java","39","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V8Ag6P5z8UxghLW","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/OrgInfoController.java","48","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V8Ag6P5z8UxghLX","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/OrgInfoController.java","57","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V8Ag6P5z8UxghLY","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/OrgInfoController.java","65","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V8Ag6P5z8UxghLZ","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/OrgInfoController.java","74","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V8Ag6P5z8UxghLa","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/OrgInfoController.java","82","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V8Ag6P5z8UxghLb","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/OrgInfoController.java","91","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V8Ag6P5z8UxghLc","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/OrgInfoController.java","101","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V8Ag6P5z8UxghLd","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/OrgInfoController.java","109","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V8Ag6P5z8UxghLe","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/OrgInfoController.java","116","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V8Ag6P5z8UxghLf","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/OrgInfoController.java","127","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V33g6P5z8Uxggvr","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/PingAnV2Controller.java","113","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V33g6P5z8Uxggvs","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/PingAnV2Controller.java","120","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V5jg6P5z8Uxgg4_","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/RegionController.java","35","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V5jg6P5z8Uxgg5A","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/RegionController.java","42","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V5jg6P5z8Uxgg5B","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/RegionController.java","51","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V5jg6P5z8Uxgg5C","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/RegionController.java","58","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V5jg6P5z8Uxgg5D","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/RegionController.java","65","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V5jg6P5z8Uxgg5E","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/RegionController.java","75","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V3ng6P5z8UxgguY","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/ResourceController.java","202","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V76g6P5z8UxghKn","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/RoleController.java","53","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V76g6P5z8UxghKo","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/RoleController.java","61","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V76g6P5z8UxghKp","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/RoleController.java","71","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V76g6P5z8UxghKq","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/RoleController.java","82","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V76g6P5z8UxghKr","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/RoleController.java","88","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V76g6P5z8UxghKs","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/RoleController.java","94","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V76g6P5z8UxghKt","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/RoleController.java","100","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V76g6P5z8UxghKu","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/RoleController.java","106","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V76g6P5z8UxghKv","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/RoleController.java","115","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V76g6P5z8UxghKw","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/RoleController.java","139","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V76g6P5z8UxghKx","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/RoleController.java","175","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V76g6P5z8UxghKy","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/RoleController.java","181","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V76g6P5z8UxghKz","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/RoleController.java","189","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhWHIqJg6P5z8UxnTdc","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/SellerCarrierBuyerRelationController.java","39","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-29T01:57:36+0000"
"AZhWHIqJg6P5z8UxnTdd","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/SellerCarrierBuyerRelationController.java","50","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-29T01:57:36+0000"
"AZhWHIqJg6P5z8UxnTde","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/SellerCarrierBuyerRelationController.java","63","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-29T01:57:36+0000"
"AZhWHIqJg6P5z8UxnTdf","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/SellerCarrierBuyerRelationController.java","70","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-29T01:57:36+0000"
"AZhP7V7Kg6P5z8UxghDu","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/ShopController.java","85","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V7Kg6P5z8UxghDv","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/ShopController.java","94","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V7Kg6P5z8UxghDw","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/ShopController.java","103","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V7Kg6P5z8UxghDx","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/ShopController.java","110","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V7Kg6P5z8UxghDy","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/ShopController.java","119","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V7Kg6P5z8UxghDz","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/ShopController.java","132","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V7Kg6P5z8UxghD0","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/ShopController.java","141","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V7Kg6P5z8UxghD1","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/ShopController.java","150","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V7Kg6P5z8UxghD2","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/ShopController.java","163","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V7Kg6P5z8UxghD3","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/ShopController.java","176","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V7Kg6P5z8UxghD4","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/ShopController.java","183","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V7Kg6P5z8UxghD5","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/ShopController.java","191","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V7Kg6P5z8UxghD6","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/ShopController.java","204","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V7Kg6P5z8UxghD7","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/ShopController.java","212","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V7Kg6P5z8UxghD8","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/ShopController.java","220","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V7Kg6P5z8UxghD9","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/ShopController.java","227","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V7Kg6P5z8UxghD-","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/ShopController.java","234","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V7Kg6P5z8UxghD_","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/ShopController.java","242","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhfv_gYg6P5z8Uxr1Ni","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/ShopController.java","250","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhP7V7Kg6P5z8UxghEB","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/ShopController.java","258","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V7Kg6P5z8UxghEC","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/ShopController.java","266","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhfv_gYg6P5z8Uxr1Nj","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/ShopController.java","274","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhP7V7Kg6P5z8UxghEE","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/ShopController.java","283","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhfv_gYg6P5z8Uxr1Nk","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/ShopController.java","290","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhP7V7Kg6P5z8UxghEG","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/ShopController.java","298","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhfv_gYg6P5z8Uxr1Nl","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/ShopController.java","305","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhfv_gYg6P5z8Uxr1Nm","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/ShopController.java","313","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhfv_gYg6P5z8Uxr1Nn","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/ShopController.java","325","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhP7V7Kg6P5z8UxghEK","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/ShopController.java","333","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhfv_gYg6P5z8Uxr1No","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/ShopController.java","340","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhfv_gYg6P5z8Uxr1Np","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/ShopController.java","365","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhfv_gYg6P5z8Uxr1Nq","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/ShopController.java","387","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhP7V7Kg6P5z8UxghEO","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/ShopController.java","415","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V7Kg6P5z8UxghEP","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/ShopController.java","421","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V7Kg6P5z8UxghEQ","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/ShopController.java","427","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhfv_gYg6P5z8Uxr1Nr","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/ShopController.java","433","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhfv_gYg6P5z8Uxr1Ns","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/ShopController.java","442","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhfv_gYg6P5z8Uxr1Nt","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/ShopController.java","450","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhfv_gYg6P5z8Uxr1Nu","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/ShopController.java","462","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhP7V7Kg6P5z8UxghEV","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/ShopController.java","472","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V7Kg6P5z8UxghEW","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/ShopController.java","480","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhfv_gYg6P5z8Uxr1Nv","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/ShopController.java","488","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhfv_gYg6P5z8Uxr1Nw","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/ShopController.java","497","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhfv_gYg6P5z8Uxr1Nx","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/ShopController.java","507","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhfv_gYg6P5z8Uxr1Ny","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/ShopController.java","515","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhfv_gYg6P5z8Uxr1Nz","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/ShopController.java","528","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhfv_gYg6P5z8Uxr1N0","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/ShopController.java","537","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhfv_gYg6P5z8Uxr1N1","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/ShopController.java","546","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhfv_gYg6P5z8Uxr1N2","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/ShopController.java","555","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhfv_gYg6P5z8Uxr1N3","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/ShopController.java","565","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhfv_gYg6P5z8Uxr1N4","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/ShopController.java","573","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhfv_gYg6P5z8Uxr1N5","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/ShopController.java","589","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhP7V7Kg6P5z8UxghEi","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/ShopController.java","597","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhfv_gYg6P5z8Uxr1N6","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/ShopController.java","604","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhP7V7Lg6P5z8UxghEk","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/ShopController.java","613","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V7Lg6P5z8UxghEl","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/ShopController.java","620","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V7Lg6P5z8UxghEm","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/ShopController.java","626","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V7Ug6P5z8UxghGR","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/StartSaleNumController.java","39","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V7Ug6P5z8UxghGS","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/StartSaleNumController.java","45","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V7Ug6P5z8UxghGT","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/StartSaleNumController.java","51","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V7Ug6P5z8UxghGU","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/StartSaleNumController.java","57","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V7Ug6P5z8UxghGV","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/StartSaleNumController.java","64","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhfv_f1g6P5z8Uxr1NY","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/TaxInfoController.java","40","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhP7V6zg6P5z8UxghB2","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/TaxInfoController.java","55","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhfv_f1g6P5z8Uxr1NZ","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/TaxInfoController.java","64","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhfv_f1g6P5z8Uxr1Na","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/TaxInfoController.java","75","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhfv_f1g6P5z8Uxr1Nb","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/TaxInfoController.java","86","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhP7V6zg6P5z8UxghB6","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/TaxInfoController.java","97","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V6zg6P5z8UxghB7","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/TaxInfoController.java","104","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V6zg6P5z8UxghB8","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/TaxInfoController.java","114","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V6zg6P5z8UxghB9","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/TaxInfoController.java","120","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V7yg6P5z8UxghKF","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/TraceDeliveryNoteController.java","35","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V7yg6P5z8UxghKG","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/TraceDeliveryNoteController.java","41","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V8pg6P5z8UxghPD","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/TraceFenceController.java","30","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V8pg6P5z8UxghPE","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/TraceFenceController.java","36","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V8pg6P5z8UxghPF","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/TraceFenceController.java","45","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V8pg6P5z8UxghPG","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/TraceFenceController.java","52","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V4Tg6P5z8UxggxD","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/TraceRoutePlanController.java","34","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhWHIpyg6P5z8UxnTdQ","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/TraceShowMonitorController.java","70","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-29T01:57:36+0000"
"AZhWHIpyg6P5z8UxnTdR","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/TraceShowMonitorController.java","77","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-29T01:57:36+0000"
"AZhP7V7bg6P5z8UxghG1","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/TransportCategoryController.java","33","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V7bg6P5z8UxghG2","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/TransportCategoryController.java","40","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V7bg6P5z8UxghG3","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/TransportCategoryController.java","47","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V7bg6P5z8UxghG4","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/TransportCategoryController.java","54","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V7bg6P5z8UxghG5","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/TransportCategoryController.java","61","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V7bg6P5z8UxghG6","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/TransportCategoryController.java","68","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V7bg6P5z8UxghG7","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/TransportCategoryController.java","75","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V7bg6P5z8UxghG8","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/TransportCategoryController.java","82","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V5-g6P5z8Uxgg8F","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/UnloadAddressController.java","38","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhfv_c_g6P5z8Uxr1NB","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/UnloadAddressController.java","44","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhP7V8mg6P5z8UxghOs","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/VehicleController.java","387","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V8mg6P5z8UxghOt","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/VehicleController.java","415","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V8mg6P5z8UxghOu","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/VehicleController.java","426","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V3zg6P5z8Uxggva","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/VehicleTypeController.java","35","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V3zg6P5z8Uxggvb","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/VehicleTypeController.java","42","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V3zg6P5z8Uxggvc","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/VehicleTypeController.java","49","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V3zg6P5z8Uxggvd","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/VehicleTypeController.java","56","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V3zg6P5z8Uxggve","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/VehicleTypeController.java","63","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V3zg6P5z8Uxggvf","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/VehicleTypeController.java","70","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V79g6P5z8UxghLM","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/WarehouseController.java","55","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V79g6P5z8UxghLN","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/WarehouseController.java","75","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhP7V79g6P5z8UxghLO","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/WarehouseController.java","81","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-21T05:51:39+0000"
"AZhfv_fbg6P5z8Uxr1NN","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/WarehouseUserRelationController.java","37","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhfv_fbg6P5z8Uxr1NO","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/WarehouseUserRelationController.java","44","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhfv_fbg6P5z8Uxr1NP","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/WarehouseUserRelationController.java","51","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"
"AZhfv_fbg6P5z8Uxr1NQ","java:S112","Ecommerce-all-sprint2:web/web-seller/src/main/java/com/ecommerce/web/seller/controller/WarehouseUserRelationController.java","64","Define and throw a dedicated exception instead of using a generic one.","MAJOR","OPEN","2025-07-31T08:35:56+0000"