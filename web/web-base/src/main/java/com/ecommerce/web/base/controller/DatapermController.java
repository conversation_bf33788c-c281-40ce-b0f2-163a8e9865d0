package com.ecommerce.web.base.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.base.api.dto.authRes.DataPermDTO;
import com.ecommerce.base.api.service.IDatapermService;
import com.ecommerce.common.result.ItemResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


/**
 * null
 *
 * <AUTHOR>
*/

@RestController
@Tag(name = "Dataperm", description = "数据权限")
@RequestMapping("/dataperm")
public class DatapermController {

   @Autowired 
   private IDatapermService iDatapermService;

   @Operation(summary = "根据id获取数据权限")
   @PostMapping(value="/findDataByAccountId")
   public ItemResult<DataPermDTO> findDataByAccountId(@RequestParam String accountId){
      return iDatapermService.findDataByAccountId(accountId);
   }
}



