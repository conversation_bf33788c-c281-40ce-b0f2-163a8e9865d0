package com.ecommerce.web.base.controller;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.exception.BusinessOperationException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.information.api.dto.favorite.FavoriteCollectionAddBrowseRecordDTO;
import com.ecommerce.information.api.dto.favorite.FavoriteCollectionAddDTO;
import com.ecommerce.information.api.dto.favorite.FavoriteCollectionAddGoodsDTO;
import com.ecommerce.information.api.dto.favorite.FavoriteCollectionAddInformationDTO;
import com.ecommerce.information.api.dto.favorite.FavoriteCollectionAddShopDTO;
import com.ecommerce.information.api.dto.favorite.FavoriteCollectionDTO;
import com.ecommerce.information.api.dto.favorite.FavoriteCollectionQueryDTO;
import com.ecommerce.information.api.service.IFavoriteCollectionService;
import com.ecommerce.member.api.dto.exception.MemberCode;
import com.ecommerce.member.api.service.IMemberRelationService;
import com.ecommerce.member.api.session.LoginInfo;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 收藏对外服务接口
 *
 * <AUTHOR>
*/
@Slf4j
@Tag(name = "FavoriteCollection", description = "收藏对外服务接口")
@RestController
@RequestMapping("/favoriteCollection")
public class FavoriteCollectionController {

   @Autowired
   private IFavoriteCollectionService iFavoriteCollectionService;
   @Autowired
   private IMemberRelationService memberRelationService;

   private static final String PAGE_SIZE = "pageSize";
   private static final String PAGE_NUMBER = "pageNumber";

   @Operation(summary = "添加收藏")
   @PostMapping(value="/add")
   public ItemResult<Object> add(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody FavoriteCollectionAddDTO favoriteCollectionAddDTO){
      checkLogin(loginInfo);
      favoriteCollectionAddDTO.setCreateUser(loginInfo.getAccountId());
      iFavoriteCollectionService.add(favoriteCollectionAddDTO);
      return new ItemResult<>(new Object());
   }

   @Operation(summary = "添加收藏")
   @PostMapping(value="/addList")
   public ItemResult<Object> addList(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody List<FavoriteCollectionAddDTO> favoriteCollectionAddDTOList){
      checkLogin(loginInfo);
      if( favoriteCollectionAddDTOList == null || favoriteCollectionAddDTOList.isEmpty() ){
         return new ItemResult<>(new Object());
      }
      favoriteCollectionAddDTOList.forEach(item ->item.setCreateUser(loginInfo.getAccountId()));
      iFavoriteCollectionService.addList(favoriteCollectionAddDTOList);
      return new ItemResult<>(new Object());
   }


   @Operation(summary = "查询会员id是否有收藏该商品")
   @GetMapping("/favoriteGoodsExists")
   public ItemResult<Boolean> favoriteGoodsExists(@Parameter(hidden = true)LoginInfo loginInfo,@RequestParam String objectId){
      checkLogin(loginInfo);
      return new ItemResult<>(iFavoriteCollectionService.favoriteGoodsExists(loginInfo.getMemberId(),objectId));
   }


   @Operation(summary = "查询会员id是否有收藏该店铺")
   @PostMapping("/favoriteShopExists")
   public ItemResult<Boolean> favoriteShopExists(@Parameter(hidden = true)LoginInfo loginInfo,@RequestParam String objectId){
      checkLogin(loginInfo);
      return new ItemResult<>(iFavoriteCollectionService.favoriteShopExists(loginInfo.getMemberId(),objectId));
   }


   @Operation(summary = "查询会员id是否有收藏该资讯")
   @PostMapping("/favoriteInformationExists")
   public ItemResult<Boolean> favoriteInformationExists(@Parameter(hidden = true)LoginInfo loginInfo,@RequestParam String objectId){
      checkLogin(loginInfo);
      return new ItemResult<>(iFavoriteCollectionService.favoriteInformationExists(loginInfo.getMemberId(),objectId));
   }


   @Operation(summary = "添加商品浏览记录")
   @PostMapping(value="/addBrowseRecord")
   public ItemResult<Object> addBrowseRecord(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody FavoriteCollectionAddBrowseRecordDTO favoriteCollectionAddBrowseRecordDTO){
      checkLogin(loginInfo);
      favoriteCollectionAddBrowseRecordDTO.setMemberId(loginInfo.getMemberId());
      favoriteCollectionAddBrowseRecordDTO.setCreateUser(loginInfo.getAccountId());
      iFavoriteCollectionService.addBrowseRecord(favoriteCollectionAddBrowseRecordDTO);
      return new ItemResult<>(new Object());
   }


   @Operation(summary = "移除收藏(逻辑删除即可)")
   @PostMapping("/removeById")
   public ItemResult<Object> removeById(@Parameter(hidden = true) LoginInfo loginInfo, @RequestParam String id){
      checkLogin(loginInfo);
      //移除收藏时删除会员关系
      FavoriteCollectionDTO fc = iFavoriteCollectionService.findFavoriteCollectionById(id);
       if (fc.getFavoriteType() == FavoriteCollectionQueryDTO.FAVORITE_GOODS_MAX && CsStringUtils.isNotBlank(fc.getObjectMemberId())) {
         List<String[]> list = Lists.newArrayList();
         list.add(new String[]{fc.getObjectMemberId(),fc.getMemberId()});
         memberRelationService.removeMemberRelationListByFavoriteGoods(list,loginInfo.getAccountId());
      }
       if (fc.getFavoriteType() == FavoriteCollectionQueryDTO.FAVORITE_SHOP_MAX && CsStringUtils.isNotBlank(fc.getObjectMemberId())) {
         List<String[]> list = Lists.newArrayList();
         list.add(new String[]{fc.getObjectMemberId(),fc.getMemberId()});
         memberRelationService.removeMemberRelationListyFavoriteShop(list,loginInfo.getAccountId());
      }
      iFavoriteCollectionService.removeById(id,loginInfo.getAccountId());
      return new ItemResult<>(new Object());
   }


   @Operation(summary = "添加资讯收藏")
   @PostMapping(value="/addInformation")
   public ItemResult<Object> addInformation(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody FavoriteCollectionAddInformationDTO favoriteCollectionAddInformationDTO){
      checkLogin(loginInfo);
      favoriteCollectionAddInformationDTO.setMemberId(loginInfo.getMemberId());
      favoriteCollectionAddInformationDTO.setCreateUser(loginInfo.getAccountId());
      iFavoriteCollectionService.addInformation(favoriteCollectionAddInformationDTO);
      return new ItemResult<>(new Object());
   }


   @Operation(summary = "置顶")
   @PostMapping("/top")
   public ItemResult<Object> top(@Parameter(hidden = true) LoginInfo loginInfo, @RequestParam String id){
      checkLogin(loginInfo);
      iFavoriteCollectionService.top(id,loginInfo.getAccountId());
      return new ItemResult<>(new Object());
   }


   @Operation(summary = "添加商品收藏")
   @PostMapping(value="/addGoods")
   public ItemResult<Object> addGoods(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody FavoriteCollectionAddGoodsDTO favoriteCollectionAddGoodsDTO){
      checkLogin(loginInfo);
      log.info("addGoods dto : {}", JSON.toJSONString(favoriteCollectionAddGoodsDTO));
       if (CsStringUtils.isBlank(favoriteCollectionAddGoodsDTO.getSellerId()) && CsStringUtils.isNotBlank((favoriteCollectionAddGoodsDTO.getMemberId()))) {
         favoriteCollectionAddGoodsDTO.setSellerId(favoriteCollectionAddGoodsDTO.getMemberId());
      }
      favoriteCollectionAddGoodsDTO.setMemberId(loginInfo.getMemberId());
      favoriteCollectionAddGoodsDTO.setCreateUser(loginInfo.getAccountId());
      iFavoriteCollectionService.addGoods(favoriteCollectionAddGoodsDTO);
      //收藏商品后添加会员关系
       if (CsStringUtils.isNotBlank(favoriteCollectionAddGoodsDTO.getSellerId())) {
         log.info("收藏商品后添加会员关系");
         memberRelationService.addMemberRelationByFavoriteGoods(favoriteCollectionAddGoodsDTO.getSellerId(), favoriteCollectionAddGoodsDTO.getMemberId());
      }else{
         log.info("未收藏商品后添加会员关系，getSellerId is null");
      }
      return new ItemResult<>(new Object());
   }

   @Operation(summary = "添加商品收藏")
   @PostMapping(value="/addGoodsList")
   public ItemResult<Object> addGoodsList(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody List<FavoriteCollectionAddGoodsDTO> list){
      checkLogin(loginInfo);
      if( list == null || list.isEmpty() ){
         return new ItemResult<>(new Object());
      }
      List<String[]> relationIdList = Lists.newArrayListWithCapacity(list.size());
      list.forEach(item ->{
          if (CsStringUtils.isBlank(item.getSellerId()) && CsStringUtils.isNotBlank((item.getMemberId()))) {
            item.setSellerId(item.getMemberId());
         }
         item.setMemberId(loginInfo.getMemberId());
         item.setCreateUser(loginInfo.getAccountId());
          if (CsStringUtils.isNotBlank(item.getObjectMemberId())) {
            relationIdList.add(new String[]{item.getSellerId(),item.getMemberId()});
         }
      });
      iFavoriteCollectionService.addGoodsList(list);
      //收藏商品后添加会员关系
      memberRelationService.addMemberRelationListByFavoriteGoods(relationIdList);
      return new ItemResult<>(new Object());
   }


   @Operation(summary = "查询会员id是否有收藏该对象")
   @PostMapping("/favoriteExists")
   public ItemResult<Boolean> favoriteExists(@Parameter(hidden = true)LoginInfo loginInfo,@RequestParam String memberId,@RequestBody int favoriteType,@RequestParam String objectId){
      checkLogin(loginInfo);
      return new ItemResult<>(iFavoriteCollectionService.favoriteExists(memberId,favoriteType,objectId));
   }


   @Operation(summary = "添加店铺收藏")
   @PostMapping(value="/addShop")
   public ItemResult<Object> addShop(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody FavoriteCollectionAddShopDTO favoriteCollectionAddShopDTO){
      checkLogin(loginInfo);
      favoriteCollectionAddShopDTO.setCreateUser(loginInfo.getAccountId());
      iFavoriteCollectionService.addShop(favoriteCollectionAddShopDTO);
      //收藏店铺后添加会员关系
       if (CsStringUtils.isNotBlank(favoriteCollectionAddShopDTO.getObjectMemberId())) {
         memberRelationService.addMemberRelationByFavoriteShop(favoriteCollectionAddShopDTO.getObjectMemberId(), favoriteCollectionAddShopDTO.getMemberId());
      }
      return new ItemResult<>(new Object());
   }


   @Operation(summary = "移除收藏")
   @PostMapping(value="/removeByIds")
   public ItemResult<Object> removeByIds(LoginInfo loginInfo, @RequestBody List<String> ids){
      checkLogin(loginInfo);
      if( ids == null || ids.isEmpty() ){
         throw new BizException(BasicCode.INVALID_PARAM,":ids不可为空");
      }
      //移除收藏时删除会员关系
      List<String[]> goodList = Lists.newArrayList();
      List<String[]> shopList = Lists.newArrayList();
      ids.forEach(id->{
         FavoriteCollectionDTO fc = iFavoriteCollectionService.findFavoriteCollectionById(id);
          if (fc.getFavoriteType() == null && fc.getFavoriteType() == FavoriteCollectionQueryDTO.FAVORITE_GOODS_MAX && CsStringUtils.isNotBlank(fc.getObjectMemberId())) {
            goodList.add(new String[]{fc.getObjectMemberId(), fc.getMemberId()});
         }
          if (fc.getFavoriteType() == null && fc.getFavoriteType() == FavoriteCollectionQueryDTO.FAVORITE_SHOP_MAX && CsStringUtils.isNotBlank(fc.getObjectMemberId())) {
            shopList.add(new String[]{fc.getObjectMemberId(), fc.getMemberId()});
         }
      });
      if( !goodList.isEmpty()) {
         memberRelationService.removeMemberRelationListByFavoriteGoods(goodList, loginInfo.getAccountId());
      }
      if( !shopList.isEmpty() ) {
         memberRelationService.removeMemberRelationListyFavoriteShop(shopList, loginInfo.getAccountId());
      }
      iFavoriteCollectionService.removeByIds(ids,loginInfo.getAccountId());
      return new ItemResult<>(new Object());
   }

   @Operation(summary = "根据店铺id移除当前会员的店铺收藏")
   @PostMapping("/removeByShopId")
   public ItemResult<Object> removeByShopId(@Parameter(hidden = true) LoginInfo loginInfo, String shopId){
      checkLogin(loginInfo);
       if (CsStringUtils.isBlank(shopId)) {
         throw new BizException(BasicCode.INVALID_PARAM,":shopId不可为空");
      }
      //移除店铺收藏时删除会员关系
      List<FavoriteCollectionDTO> list = iFavoriteCollectionService.findFavoriteCollectionByFavoriteTypeAndObjectId(Lists.newArrayList(shopId),FavoriteCollectionQueryDTO.FAVORITE_SHOP_MAX,loginInfo.getMemberId());
      List<String[]> shopList = Lists.newArrayList();
      list.forEach(fc -> {
          if (fc.getFavoriteType() == null && fc.getFavoriteType() == FavoriteCollectionQueryDTO.FAVORITE_SHOP_MAX && CsStringUtils.isNotBlank(fc.getObjectMemberId())) {
            shopList.add(new String[]{fc.getObjectMemberId(), fc.getMemberId()});
         }
      });
      if( !shopList.isEmpty() ) {
         memberRelationService.removeMemberRelationListyFavoriteShop(shopList, loginInfo.getAccountId());
      }
      iFavoriteCollectionService.removeByObjectIds(Lists.newArrayList(shopId),FavoriteCollectionQueryDTO.FAVORITE_SHOP_MAX,loginInfo.getMemberId(),loginInfo.getAccountId());
      return new ItemResult<>(new Object());
   }
   @Operation(summary = "根据浏览对象id移除当前会员的浏览历史")
   @PostMapping("/removeByBrowseRecordId")
   public ItemResult<Object> removeByBrowseRecordId(@Parameter(hidden = true) LoginInfo loginInfo, String browseRecordId){
      checkLogin(loginInfo);
       if (CsStringUtils.isBlank(browseRecordId)) {
         throw new BizException(BasicCode.INVALID_PARAM,":browseRecordId不可为空");
      }
      iFavoriteCollectionService.removeByObjectIds(Lists.newArrayList(browseRecordId),FavoriteCollectionQueryDTO.FAVORITE_BROWSE_RECORD_MAX,loginInfo.getMemberId(),loginInfo.getAccountId());
      return new ItemResult<>(new Object());
   }
   @Operation(summary = "根据商品id移除当前会员的商品收藏")
   @GetMapping("/removeByGoodsId")
   public ItemResult<Object> removeByGoodsId(@Parameter(hidden = true) LoginInfo loginInfo, String goodsId){
      checkLogin(loginInfo);
       if (CsStringUtils.isBlank(goodsId)) {
         throw new BizException(BasicCode.INVALID_PARAM,":goodId不可为空");
      }
      //移除商品收藏时删除会员关系
      List<FavoriteCollectionDTO> list = iFavoriteCollectionService.findFavoriteCollectionByFavoriteTypeAndObjectId(Lists.newArrayList(goodsId),FavoriteCollectionQueryDTO.FAVORITE_GOODS_MAX,loginInfo.getMemberId());
      List<String[]> goodList = Lists.newArrayList();
      list.forEach(fc -> {
          if (fc.getFavoriteType() == null && fc.getFavoriteType() == FavoriteCollectionQueryDTO.FAVORITE_GOODS_MAX && CsStringUtils.isNotBlank(fc.getObjectMemberId())) {
            goodList.add(new String[]{fc.getObjectMemberId(), fc.getMemberId()});
         }
      });
      if( !goodList.isEmpty() ) {
         memberRelationService.removeMemberRelationListByFavoriteGoods(goodList, loginInfo.getAccountId());
      }
      iFavoriteCollectionService.removeByObjectIds(Lists.newArrayList(goodsId),FavoriteCollectionQueryDTO.FAVORITE_GOODS_MAX,loginInfo.getMemberId(),loginInfo.getAccountId());
      return new ItemResult<>(new Object());
   }

    @Operation(summary = "根据商品id移除当前会员的商品收藏")
   @PostMapping(value="/removeByGoodsIdList")
    public ItemResult<Object> removeByGoodsIdList(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody List<String> goodsIdList){
      checkLogin(loginInfo);
      if( goodsIdList == null || goodsIdList.isEmpty() ){
         throw new BizException(BasicCode.INVALID_PARAM,":goodsIdList不可为空");
      }
      //移除商品收藏时删除会员关系
      List<FavoriteCollectionDTO> list = iFavoriteCollectionService.findFavoriteCollectionByFavoriteTypeAndObjectId(goodsIdList,FavoriteCollectionQueryDTO.FAVORITE_GOODS_MAX,loginInfo.getMemberId());
      List<String[]> goodList = Lists.newArrayList();
      list.forEach(fc -> {
          if (fc.getFavoriteType() == null && fc.getFavoriteType() == FavoriteCollectionQueryDTO.FAVORITE_GOODS_MAX && CsStringUtils.isNotBlank(fc.getObjectMemberId())) {
            goodList.add(new String[]{fc.getObjectMemberId(), fc.getMemberId()});
         }
      });
      if( !goodList.isEmpty() ) {
         memberRelationService.removeMemberRelationListByFavoriteGoods(goodList, loginInfo.getAccountId());
      }
      iFavoriteCollectionService.removeByObjectIds(goodsIdList,FavoriteCollectionQueryDTO.FAVORITE_GOODS_MAX,loginInfo.getMemberId(),loginInfo.getAccountId());
      return new ItemResult<>(new Object());
   }
   @Operation(summary = "按收藏类型和对象id移除当前会员的资讯收藏")
   @PostMapping("/removeByInformationId")
   public ItemResult<Object> removeByInformationId(@Parameter(hidden = true) LoginInfo loginInfo, String informationId){
      checkLogin(loginInfo);
       if (CsStringUtils.isBlank(informationId)) {
         throw new BizException(BasicCode.INVALID_PARAM,":informationId不可为空");
      }
      iFavoriteCollectionService.removeByObjectIds(Lists.newArrayList(informationId),FavoriteCollectionQueryDTO.FAVORITE_INFORMATION_MAX,loginInfo.getMemberId(),loginInfo.getAccountId());
      return new ItemResult<>(new Object());
   }

   @Operation(summary = "根据店铺id移除当前会员的店铺收藏")
   @PostMapping(value="/removeByShopIds")
   public ItemResult<Object> removeByShopIds(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody List<String> shopIds){
      checkLogin(loginInfo);
      if( shopIds == null || shopIds.isEmpty() ){
         throw new BizException(BasicCode.INVALID_PARAM,":shopIds不可为空");
      }
      //移除店铺收藏时删除会员关系
      List<FavoriteCollectionDTO> list = iFavoriteCollectionService.findFavoriteCollectionByFavoriteTypeAndObjectId(shopIds,FavoriteCollectionQueryDTO.FAVORITE_SHOP_MAX,loginInfo.getMemberId());
      List<String[]> shopList = Lists.newArrayList();
      list.forEach(fc -> {
          if (fc.getFavoriteType() == null && fc.getFavoriteType() == FavoriteCollectionQueryDTO.FAVORITE_SHOP_MAX && CsStringUtils.isNotBlank(fc.getObjectMemberId())) {
            shopList.add(new String[]{fc.getObjectMemberId(), fc.getMemberId()});
         }
      });
      if( !shopList.isEmpty() ) {
         memberRelationService.removeMemberRelationListyFavoriteShop(shopList, loginInfo.getAccountId());
      }
      iFavoriteCollectionService.removeByObjectIds(shopIds,FavoriteCollectionQueryDTO.FAVORITE_SHOP_MAX,loginInfo.getMemberId(),loginInfo.getAccountId());
      return new ItemResult<>(new Object());
   }
   @Operation(summary = "根据浏览对象id移除当前会员的浏览历史")
   @PostMapping(value="/removeByBrowseRecordIds")
   public ItemResult<Object> removeByBrowseRecordIds(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody List<String> browseRecordIds){
      checkLogin(loginInfo);
      if( browseRecordIds == null || browseRecordIds.isEmpty() ){
         throw new BizException(BasicCode.INVALID_PARAM,":browseRecordIds不可为空");
      }
      iFavoriteCollectionService.removeByObjectIds(browseRecordIds,FavoriteCollectionQueryDTO.FAVORITE_BROWSE_RECORD_MAX,loginInfo.getMemberId(),loginInfo.getAccountId());
      return new ItemResult<>(new Object());
   }
   @Operation(summary = "根据商品id移除当前会员的商品收藏")
   @PostMapping(value="/removeByGoodsIds")
   public ItemResult<Object> removeByGoodsIds(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody List<String> goodsIds){
      checkLogin(loginInfo);
      if( goodsIds == null || goodsIds.isEmpty() ){
         throw new BizException(BasicCode.INVALID_PARAM,":goodsIds不可为空");
      }
      //移除商品收藏时删除会员关系
      List<FavoriteCollectionDTO> list = iFavoriteCollectionService.findFavoriteCollectionByFavoriteTypeAndObjectId(goodsIds,FavoriteCollectionQueryDTO.FAVORITE_GOODS_MAX,loginInfo.getMemberId());
      List<String[]> goodList = Lists.newArrayList();
      list.forEach(fc -> {
          if (fc.getFavoriteType() == null && fc.getFavoriteType() == FavoriteCollectionQueryDTO.FAVORITE_GOODS_MAX && CsStringUtils.isNotBlank(fc.getObjectMemberId())) {
            goodList.add(new String[]{fc.getObjectMemberId(), fc.getMemberId()});
         }
      });
      if( !goodList.isEmpty() ) {
         memberRelationService.removeMemberRelationListByFavoriteGoods(goodList, loginInfo.getAccountId());
      }
      iFavoriteCollectionService.removeByObjectIds(goodsIds,FavoriteCollectionQueryDTO.FAVORITE_GOODS_MAX,loginInfo.getMemberId(),loginInfo.getAccountId());
      return new ItemResult<>(new Object());
   }

   @Operation(summary = "按收藏类型和对象id移除当前会员的资讯收藏")
   @PostMapping(value="/removeByInformationIds")
   public ItemResult<Object> removeByInformationIds(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody List<String> informationIds){
      checkLogin(loginInfo);
      if( informationIds == null || informationIds.isEmpty() ){
         throw new BizException(BasicCode.INVALID_PARAM,":informationIds不可为空");
      }
      iFavoriteCollectionService.removeByObjectIds(informationIds,FavoriteCollectionQueryDTO.FAVORITE_INFORMATION_MAX,loginInfo.getMemberId(),loginInfo.getAccountId());
      return new ItemResult<>(new Object());
   }

   @Operation(summary = "根据会员id和收藏类型查询其收藏")
   @PostMapping(value="/findByMemberId")
   public ItemResult<PageInfo<FavoriteCollectionDTO>> findByMemberId(@Parameter(hidden = true)LoginInfo loginInfo,@RequestBody FavoriteCollectionQueryDTO query){
      checkLogin(loginInfo);
      return new ItemResult<>(iFavoriteCollectionService.findByMemberId(query));
   }

   @Operation(summary = "查询会员收藏的商品 mapKey : prodId,pageSize,pageNumber")
   @PostMapping(value="/findGoodsByMemberId")
   public ItemResult<PageInfo<FavoriteCollectionDTO>> findGoodsByMemberId(@Parameter(hidden = true)LoginInfo loginInfo,@RequestBody Map<String,String> map){
      checkLogin(loginInfo);
      FavoriteCollectionQueryDTO query = new FavoriteCollectionQueryDTO();
      query.setFavoriteType(FavoriteCollectionQueryDTO.FAVORITE_GOODS_MAX);
      query.setMemberId(loginInfo.getMemberId());
      //商品分类
      query.setProdId(map.getOrDefault("prodId",null));
      query.setPageSize(Integer.parseInt(map.getOrDefault(PAGE_SIZE,"20")));
      query.setPageNumber(Integer.parseInt(map.getOrDefault(PAGE_NUMBER,"1")));
      return new ItemResult<>(iFavoriteCollectionService.findByMemberId(query));
   }

   @Operation(summary = "查询会员收藏的商品分类")
   @PostMapping("/findGoodsCategory")
   public ItemResult<List<FavoriteCollectionDTO>> findGoodsCategory(@Parameter(hidden = true)LoginInfo loginInfo){
      checkLogin(loginInfo);
      return new ItemResult<>(findCategory(loginInfo.getMemberId(),FavoriteCollectionQueryDTO.FAVORITE_GOODS_MAX));
   }

   @Operation(summary = "查询会员收藏的店铺 mapKey :prodId,pageSize,pageNumber")
   @PostMapping(value="/findShopByMemberId")
   public ItemResult<PageInfo<FavoriteCollectionDTO>> findShopByMemberId(@Parameter(hidden = true)LoginInfo loginInfo,@RequestBody Map<String,String> map){
      checkLogin(loginInfo);
      int pageSize = Integer.parseInt(map.getOrDefault(PAGE_SIZE,"20"));
      int pageNumber = Integer.parseInt(map.getOrDefault(PAGE_NUMBER,"1"));
      return new ItemResult<>(findFavorite(loginInfo.getMemberId(),FavoriteCollectionQueryDTO.FAVORITE_SHOP_MAX,pageSize,pageNumber));
   }

   @Operation(summary = "查询会员收藏的资讯 mapKey : pageSize,pageNumber")
   @PostMapping(value="/findInformationByMemberId")
   public ItemResult<PageInfo<FavoriteCollectionDTO>> findInformationByMemberId(@Parameter(hidden = true)LoginInfo loginInfo,@RequestBody Map<String,String> map){
      checkLogin(loginInfo);
      int pageSize = Integer.parseInt(map.getOrDefault(PAGE_SIZE,"20"));
      int pageNumber = Integer.parseInt(map.getOrDefault(PAGE_NUMBER,"1"));
      return new ItemResult<>(findFavorite(loginInfo.getMemberId(),FavoriteCollectionQueryDTO.FAVORITE_INFORMATION_MAX,pageSize,pageNumber));
   }

   @Operation(summary = "查询会员浏览历史记录 mapKey : pageSize,pageNumber")
   @PostMapping(value="/findBrowseRecordByMemberId")
   public ItemResult<PageInfo<FavoriteCollectionDTO>> findBrowseRecordByMemberId(@Parameter(hidden = true)LoginInfo loginInfo,@RequestBody Map<String,String> map){
      checkLogin(loginInfo);
      int pageSize = Integer.parseInt(map.getOrDefault(PAGE_SIZE,"20"));
      int pageNumber = Integer.parseInt(map.getOrDefault(PAGE_NUMBER,"1"));
      return new ItemResult<>(findFavorite(loginInfo.getMemberId(),FavoriteCollectionQueryDTO.FAVORITE_BROWSE_RECORD_MAX,pageSize,pageNumber));
   }

   @Operation(summary = "查询会员浏览历史记录分类")
   @PostMapping("/findBrowseRecordCateory")
   public ItemResult<List<FavoriteCollectionDTO>> findBrowseRecordCateory(@Parameter(hidden = true)LoginInfo loginInfo){
      checkLogin(loginInfo);
      return new ItemResult<>(findCategory(loginInfo.getMemberId(),FavoriteCollectionQueryDTO.FAVORITE_BROWSE_RECORD_MAX));
   }

   private PageInfo<FavoriteCollectionDTO> findFavorite(String memberId,int favoriteType,Integer pageSize,Integer pageNumber){
      FavoriteCollectionQueryDTO query = new FavoriteCollectionQueryDTO();
      query.setFavoriteType(FavoriteCollectionQueryDTO.FAVORITE_BROWSE_RECORD_MAX);
      query.setMemberId(memberId);
      query.setPageSize(ObjectUtil.defaultIfNull(pageSize,20));
      query.setPageNumber(ObjectUtil.defaultIfNull(pageNumber,1));
      return iFavoriteCollectionService.findByMemberId(query);
   }

   private List<FavoriteCollectionDTO> findCategory(String memberId,int favoriteType){
      FavoriteCollectionQueryDTO query = new FavoriteCollectionQueryDTO();
      query.setFavoriteType(favoriteType);
      query.setMemberId(memberId);
      query.setPageNumber(200);
      query.setPageNumber(0);
      PageInfo<FavoriteCollectionDTO> pageInfo = iFavoriteCollectionService.findByMemberId(query);

      if(pageInfo != null && pageInfo.getList() != null && !pageInfo.getList().isEmpty()){
         return pageInfo.getList().stream().map(item ->{
            FavoriteCollectionDTO fc = new FavoriteCollectionDTO();
            fc.setProdId(item.getProdId());
            fc.setProdName(item.getProdName());
            return fc;
         }).distinct().toList();
      }
      return Lists.newArrayList();
   }

   private void checkLogin(LoginInfo loginInfo){
       if (loginInfo == null || CsStringUtils.isBlank(loginInfo.getAccountId())) {
         throw new BizException(MemberCode.LOGIN_ERROR,"你还没有登录，请先登录");
      }
   }
}
