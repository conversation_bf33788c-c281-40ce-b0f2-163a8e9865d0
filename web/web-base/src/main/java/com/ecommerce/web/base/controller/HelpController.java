package com.ecommerce.web.base.controller;


import com.ecommerce.common.result.ItemResult;
import com.ecommerce.information.api.dto.help.HelpCategoryDTO;
import com.ecommerce.information.api.dto.help.HelpCategoryQueryDTO;
import com.ecommerce.information.api.dto.help.HelpQueryByCategoryDTO;
import com.ecommerce.information.api.dto.help.HelpQueryDTO;
import com.ecommerce.information.api.dto.help.HelpQuestionDTO;
import com.ecommerce.information.api.service.IHelpService;
import com.ecommerce.member.api.session.LoginInfo;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@RequestMapping("/help/anon")
@RestController
@Tag(name = "Help", description = "Help查询接口(匿名)")
public class HelpController {
    @Autowired
    private IHelpService iHelpService;

    @Operation(summary = "查找单个分类")
    @PostMapping(value = "/findHelpCategoryById")
    public ItemResult<HelpCategoryDTO> findHelpCategoryById(@RequestParam String categoryId){
    	HelpCategoryDTO result = iHelpService.findHelpCategoryById(categoryId);
    	return new ItemResult<HelpCategoryDTO>(result);
    }

    @Operation(summary = "根据条件翻页查询")
    @PostMapping(value = "/findAllHelpQuestion")
    public ItemResult<PageInfo<HelpQuestionDTO>> findAllHelpQuestion(@RequestBody HelpQueryDTO helpQueryDTO){
    	PageInfo<HelpQuestionDTO> result = iHelpService.findAllHelpQuestion(helpQueryDTO);
    	return new ItemResult<PageInfo<HelpQuestionDTO>>(result);
    }

    @Operation(summary = "按条件分页查询")
    @PostMapping(value = "/findAllHelpCategory")
    public ItemResult<PageInfo<HelpCategoryDTO>> findAllHelpCategory(@RequestBody HelpCategoryQueryDTO query){
    	PageInfo<HelpCategoryDTO> result = iHelpService.findAllHelpCategory(query);
    	return new ItemResult<PageInfo<HelpCategoryDTO>>(result);
    }

    @Operation(summary = "根据id查询单个帮助")
    @PostMapping(value = "/findHelpQuestionById")
    public ItemResult<HelpQuestionDTO> findHelpQuestionById(@RequestParam String helpId){
    	HelpQuestionDTO result = iHelpService.findHelpQuestionById(helpId);
    	return new ItemResult<HelpQuestionDTO>(result);
    }

    @Operation(summary = "按类型查询问题")
    @PostMapping(value="/findByCategory")
    public ItemResult<PageInfo<HelpQuestionDTO>> findByCategory(@RequestBody HelpQueryByCategoryDTO helpQueryByCategoryDTO){
    	PageInfo<HelpQuestionDTO> result = iHelpService.findByCategory(helpQueryByCategoryDTO);
    	return new ItemResult<PageInfo<HelpQuestionDTO>>(result);
    }

    @Operation(summary = "查找整颗树(去掉根节点后的两层数据，按树形结构组装数据)")
    @PostMapping(value="/findAllCategoryToTreeAndRemoveRoot")
    public ItemResult<List<HelpCategoryDTO>> findAllCategoryToTreeAndRemoveRoot(){
       return new ItemResult<>(iHelpService.findAllCategoryToTreeAndRemoveRoot());
    }
    
    @Operation(summary = "反馈问题是否解决")
    @PostMapping(value="/feedbackHelpQuestion")
    public ItemResult<Boolean> feedbackHelpQuestion(@Parameter(hidden = true)LoginInfo loginInfo, @RequestParam Boolean solved, @RequestParam String helpQuestionId){
        return new ItemResult<>(iHelpService.feedbackHelpQuestion(solved, helpQuestionId,loginInfo.getAccountId()));
    }
    
    @Operation(summary = "按分类名称查找问题")
    @PostMapping(value="/findQuestionByCategoryName")
    public ItemResult<List<HelpQuestionDTO>> findQuestionByCategoryName(@RequestParam String categoryName){
       return new ItemResult<>(iHelpService.findQuestionByCategoryName(categoryName));
    }

}

