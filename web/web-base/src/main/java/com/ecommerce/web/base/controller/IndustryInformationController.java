package com.ecommerce.web.base.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.common.result.ItemResult;
import com.ecommerce.information.api.dto.IndustryInformationDTO;
import com.ecommerce.information.api.service.IIndustryInformationService;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * null
 *
 * <AUTHOR>
*/
@RestController
@Tag(name = "IndustryInformationController", description = "行业资讯")
@RequestMapping("/industryInformation")
public class IndustryInformationController {

   @Autowired 
   private IIndustryInformationService industryInformationService;

   @Operation(summary = "修改行业资讯")
   @PostMapping(value="/updateIndustryInformation")
   public ItemResult<Boolean> updateIndustryInformation(@RequestBody IndustryInformationDTO industryInformationDTO){
      Boolean result = industryInformationService.updateIndustryInformation(industryInformationDTO);
      return new ItemResult<Boolean>(result);
   }


   @Operation(summary = "条件查询行业资讯列表")
   @PostMapping(value="/selectIndustryInformationList")
   public ItemResult<PageInfo<IndustryInformationDTO>> selectIndustryInformationList(@RequestBody IndustryInformationDTO industryInformationDTO){
      PageInfo<IndustryInformationDTO> result = industryInformationService.selectIndustryInformationList(industryInformationDTO);
      return new ItemResult<PageInfo<IndustryInformationDTO>>(result);
   }


   @Operation(summary = "查询行业资讯详情")
   @PostMapping(value="/selectIndustryInformationById")
   public ItemResult<IndustryInformationDTO> selectIndustryInformationById(@RequestParam String id){
      IndustryInformationDTO result = industryInformationService.selectIndustryInformationById(id);
      return new ItemResult<IndustryInformationDTO>(result);
   }


   @Operation(summary = "删除行业资讯")
   @PostMapping(value="/deleteIndustryInformationById")
   public ItemResult<Boolean> deleteIndustryInformationById(@RequestParam String id){
      Boolean result = industryInformationService.deleteIndustryInformationById(id);
      return new ItemResult<Boolean>(result);
   }


   @Operation(summary = "添加行业资讯")
   @PostMapping(value="/addIndustryInformation")
   public ItemResult<Boolean> addIndustryInformation(@RequestBody IndustryInformationDTO industryInformationDTO){
      Boolean result = industryInformationService.addIndustryInformation(industryInformationDTO);
      return new ItemResult<Boolean>(result);
   }



}
