package com.ecommerce.web.base.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.common.result.ItemResult;
import com.ecommerce.information.api.dto.internalmessage.*;
import com.ecommerce.information.api.service.IInternalMessageService;
import com.ecommerce.member.api.service.IAccountService;
import com.ecommerce.member.api.service.IMemberService;
import com.ecommerce.member.api.session.LoginInfo;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;


/**
 * 站内信服务
 *
 * <AUTHOR>
*/

@RestController
@Tag(name = "InternalMessage", description = "站内信服务")
@RequestMapping("/internalMessage")
public class InternalMessageController {

   @Autowired 
   private IInternalMessageService internalMessageService;
   @Autowired
   private IMemberService memberService;
   @Autowired
   private IAccountService accountService;

   private static final String METHOD_DISCARD = "method discard";

   @Operation(summary = "根据会员id查询未读消息数量")
   @PostMapping(value="/count")
   public ItemResult<Integer> count(@Parameter(hidden = true)LoginInfo loginInfo){
      return new ItemResult<>(internalMessageService.count(loginInfo.getAccountId()));
   }


   @Operation(summary = "彻底删除消息(修改删除标记字段为true)")
   @PostMapping(value="/delete")
   public ItemResult<Boolean> delete(@Parameter(hidden = true)LoginInfo loginInfo,@Valid @RequestBody InternalMessageDeleteDTO internalMessageDeleteDTO){
       internalMessageDeleteDTO.setOperator(loginInfo.getAccountId());
       internalMessageDeleteDTO.setMemberId(loginInfo.getMemberId());
      internalMessageDeleteDTO.setAccountId(loginInfo.getAccountId());
      return new ItemResult<>(internalMessageService.delete(internalMessageDeleteDTO));
   }

   @Hidden
   @Operation(summary = "添加/编辑草稿消息")
   @PostMapping(value="/addOrUpdateDraftMessage")
   public ItemResult<InternalMessageDTO> addOrUpdateDraftMessage(@Parameter(hidden = true)LoginInfo loginInfo,@Valid @RequestBody InternalMessageDeleteDTO internalMessageDraftDTO){
      throw new RuntimeException(METHOD_DISCARD);
   }

   @Hidden
   @Operation(summary = "消息是否已满（设定为每个会员最多为1000条记录）")
   @PostMapping(value="/isFull")
   public ItemResult<Boolean> isFull(@Parameter(hidden = true)LoginInfo loginInfo){
      throw new RuntimeException(METHOD_DISCARD);
   }


   @Operation(summary = "根据条件翻页查询")
   @PostMapping(value="/findAll")
   public ItemResult<PageInfo<InternalMessageDTO>> findAll(@Parameter(hidden = true)LoginInfo loginInfo,@Valid @RequestBody InternalMessageQueryDTO internalMessageQueryDTO){
      internalMessageQueryDTO.setMemberId(loginInfo.getMemberId());
      internalMessageQueryDTO.setAccountId(loginInfo.getAccountId());
      return new ItemResult<>(internalMessageService.findAll(internalMessageQueryDTO));
   }

   @Operation(summary = "根据id查询单条记录")
   @PostMapping(value="/findById")
   public ItemResult<InternalMessageDTO> findById(@Parameter(hidden = true)LoginInfo loginInfo, @RequestParam String id){
      InternalMessageDTO internalMessageDTO = internalMessageService.findById(id);
      if( internalMessageDTO != null && loginInfo.getAccountId().equals(internalMessageDTO.getReceiveUserId()) ){
         return new ItemResult<>(internalMessageService.findById(id));
      }
      return new ItemResult<>(null);
   }

   @Operation(summary = "修改消息为已读")
   @PostMapping(value="/updateReadStatus")
   public ItemResult<Boolean> updateReadStatus(@Parameter(hidden = true)LoginInfo loginInfo,@Valid @RequestBody InternalMessageReadStatusUpdateDTO internalMessageReadStatusUpdateDTO){
      internalMessageReadStatusUpdateDTO.setMemberId(loginInfo.getMemberId());
      internalMessageReadStatusUpdateDTO.setOperator(loginInfo.getAccountId());
      internalMessageReadStatusUpdateDTO.setAccountId(loginInfo.getAccountId());
      return new ItemResult<>(internalMessageService.updateReadStatus(internalMessageReadStatusUpdateDTO));
   }

   @Hidden
   @Operation(summary = "修改消息类型状态(收件箱、发件箱、草稿箱、回收箱),页面删除则把消息类型状态改为回收箱")
   @PostMapping(value="/updateTypeStatus")
   public ItemResult<Object> updateTypeStatus(@Parameter(hidden = true) LoginInfo loginInfo, @Valid @RequestBody InternalMessageCreateDTO internalMessageTypeStatusUpdateDTO){
      throw new RuntimeException(METHOD_DISCARD);
   }

   @Hidden
   @Operation(summary = "发送消息")
   @PostMapping(value="/sendMessage")
   public ItemResult<InternalMessageDTO> sendMessage(@Parameter(hidden = true)LoginInfo loginInfo,@Valid @RequestBody InternalMessageCreateDTO internalMessageCreateDTO){
      throw new RuntimeException(METHOD_DISCARD);
   }

   @Hidden
   @Operation(summary = "根据条件翻页查询")
   @PostMapping(value="/findPage")
   public ItemResult<PageInfo<InternalMessageDTO>> findPage(@Parameter(hidden = true)LoginInfo loginInfo,@Valid @RequestBody InternalMessageQueryDTO internalMessageQueryDTO){
      internalMessageQueryDTO.setMemberId(loginInfo.getMemberId());
      internalMessageQueryDTO.setAccountId(loginInfo.getAccountId());
      return new ItemResult<>(internalMessageService.findAll(internalMessageQueryDTO));
   }
}
