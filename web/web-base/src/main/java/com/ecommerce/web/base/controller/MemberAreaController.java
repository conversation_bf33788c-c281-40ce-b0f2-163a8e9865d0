package com.ecommerce.web.base.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.common.result.ItemResult;
import com.ecommerce.member.api.dto.member.MemberTypeDTO;
import com.ecommerce.member.api.dto.servicearea.MemberServiceAreaDTO;
import com.ecommerce.member.api.dto.servicearea.MemberServiceAreaQueryDTO;
import com.ecommerce.member.api.dto.servicearea.MemberServiceAreaResultDTO;
import com.ecommerce.member.api.dto.servicearea.MemberServiceAreaTypeQueryDTO;
import com.ecommerce.member.api.service.IMemberAreaService;
import com.ecommerce.member.api.session.LoginInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * null
 *
 * <AUTHOR>
*/
@RestController
@Tag(name = "MemberAreaController", description = "会员服务范围")
@RequestMapping("/memberArea")
public class MemberAreaController {

   @Autowired 
   private IMemberAreaService iMemberServiceAreaService;

   @Operation(summary = "删除服务范围")
   @PostMapping(value="/deleteServiceArea")
   public ItemResult<Object> deleteServiceArea(@Parameter(hidden = true) LoginInfo loginInfo, @RequestParam String memberId, @RequestParam String serviceAreaId){
      iMemberServiceAreaService.deleteServiceArea(memberId,serviceAreaId, "");
      return new ItemResult<>(new Object());
   }


   @Operation(summary = "更新服务范围")
   @PostMapping(value="/updateServiceArea")
   public ItemResult<Object> updateServiceArea(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody MemberServiceAreaQueryDTO memberServiceAreaQueryDTO){
      iMemberServiceAreaService.updateServiceArea(memberServiceAreaQueryDTO, "");
      return new ItemResult<>(new Object());
   }


   @Operation(summary = "根据地址编号查找会员")
   @PostMapping(value="/findMemberByServiceArea")
   public ItemResult<List<MemberTypeDTO>> findMemberByServiceArea(@RequestBody MemberServiceAreaTypeQueryDTO memberServiceAreaTypeQueryDTO){
      List<MemberTypeDTO> area = iMemberServiceAreaService.findMemberByServiceArea(memberServiceAreaTypeQueryDTO);
      return new ItemResult<>(area);
   }


   @Operation(summary = "查找一个会员的所有的服务范围")
   @PostMapping(value="/findListByMemberId")
   public ItemResult<MemberServiceAreaDTO> findListByMemberId(@RequestParam String memberId){
      MemberServiceAreaDTO byMemberId = iMemberServiceAreaService.findListByMemberId(memberId);
      return new ItemResult<>(byMemberId);
   }


   @Operation(summary = "查找一个会员的所有的服务范围")
   @PostMapping(value="/findTreeByMemberId")
   public ItemResult<MemberServiceAreaResultDTO> findTreeByMemberId(@RequestParam String memberId){
      MemberServiceAreaResultDTO byMemberId = iMemberServiceAreaService.findTreeByMemberId(memberId);
      return new ItemResult<>(byMemberId);
   }

   @Operation(summary = "增加服务范围")
   @PostMapping(value="/addServiceArea")
   public ItemResult<Object> addServiceArea(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody MemberServiceAreaQueryDTO memberServiceAreaQueryDTO){
      iMemberServiceAreaService.addServiceArea(memberServiceAreaQueryDTO, "");
      return new ItemResult<>(new Object());
   }



}
