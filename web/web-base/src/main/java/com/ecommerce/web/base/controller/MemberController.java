package com.ecommerce.web.base.controller;

import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.exception.BusinessOperationException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.goods.api.dto.CategoryTreeDTO;
import com.ecommerce.goods.api.service.IGoodsCategoryService;
import com.ecommerce.member.api.dto.AttachmentDTO;
import com.ecommerce.member.api.dto.KeyValueDTO;
import com.ecommerce.member.api.dto.account.AccountDTO;
import com.ecommerce.member.api.dto.base.PageQuery;
import com.ecommerce.member.api.dto.carrier.CarrierIdentityDTO;
import com.ecommerce.member.api.dto.exception.MemberCode;
import com.ecommerce.member.api.dto.member.CancelRequestDTO;
import com.ecommerce.member.api.dto.member.MemberApprovalRequestDTO;
import com.ecommerce.member.api.dto.member.MemberApprovalRequestQueryDTO;
import com.ecommerce.member.api.dto.member.MemberBaseInfoDTO;
import com.ecommerce.member.api.dto.member.MemberBusinessInfoDTO;
import com.ecommerce.member.api.dto.member.MemberCertDTO;
import com.ecommerce.member.api.dto.member.MemberCertQueryDTO;
import com.ecommerce.member.api.dto.member.MemberCertViewDTO;
import com.ecommerce.member.api.dto.member.MemberDTO;
import com.ecommerce.member.api.dto.member.MemberDetailDTO;
import com.ecommerce.member.api.dto.member.MemberListViewDTO;
import com.ecommerce.member.api.dto.member.MemberQueryDTO;
import com.ecommerce.member.api.dto.member.MemberRegisterByAppDTO;
import com.ecommerce.member.api.dto.member.MemberRequestDTO;
import com.ecommerce.member.api.dto.member.MemberSimpleDTO;
import com.ecommerce.member.api.dto.member.MemberStatusDTO;
import com.ecommerce.member.api.dto.member.MemberSuperSimpleDTO;
import com.ecommerce.member.api.dto.member.MemberTypeDTO;
import com.ecommerce.member.api.dto.member.MemberUpdateTypeDTO;
import com.ecommerce.member.api.dto.member.SubmitCertDTO;
import com.ecommerce.member.api.dto.member.UpdateCertDTO;
import com.ecommerce.member.api.dto.member.config.MemberConfigDTO;
import com.ecommerce.member.api.dto.member.config.MemberConfigEnum;
import com.ecommerce.member.api.dto.member.enums.AdvertStatusEnum;
import com.ecommerce.member.api.dto.member.enums.ApproveRequestTypeEnum;
import com.ecommerce.member.api.dto.member.enums.MemberPlatform;
import com.ecommerce.member.api.dto.tenant.TenantDTO;
import com.ecommerce.member.api.dto.tenant.TenantQueryDTO;
import com.ecommerce.member.api.service.IMemberConfigService;
import com.ecommerce.member.api.service.IMemberService;
import com.ecommerce.member.api.service.ITenantService;
import com.ecommerce.member.api.session.LoginInfo;
import com.ecommerce.web.base.dto.member.MemberApprovalRequestPageDTO;
import com.ecommerce.web.base.dto.member.MemberBaseInfoQueryDTO;
import com.ecommerce.web.base.dto.member.MemberBusinessInfoQueryDTO;
import com.ecommerce.web.base.dto.member.MemberQueryPageDTO;
import com.ecommerce.web.base.dto.member.MemberRegisterDTO;
import com.ecommerce.web.base.dto.member.MemberUpdateTypeQueryDTO;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import com.ecommerce.common.utils.CsStringUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 会员服务
 *
 * <AUTHOR>
 */
@Slf4j
@Tag(name = "MemberController", description = "会员服务")
@RestController
@RequestMapping("/member")
public class MemberController {

    @Autowired
    private IMemberService iMemberService;
    @Autowired
    private IMemberConfigService memberConfigService;
    @Autowired
    private ITenantService tenantService;
    @Autowired
    private IGoodsCategoryService goodsCategoryService;

    @Operation(summary = "查询平台会员信息")
    @PostMapping(value="/findPlatformMemberInfo")
    public ItemResult<MemberSimpleDTO> hasErpJoin(@Parameter(hidden = true)LoginInfo loginInfo){
        checkLogin(loginInfo);
        return new ItemResult<>(iMemberService.findMemberSimpleById(MemberPlatform.PLATFORM_MEMBERID.getId()));
    }

    @Operation(summary = "是否接入ERP")
    @PostMapping(value="/hasErpJoin")
    public ItemResult<Boolean> hasErpJoin(@Parameter(hidden = true)LoginInfo loginInfo,@RequestParam String memberId){
        checkLogin(loginInfo);
        MemberConfigDTO conf = memberConfigService.findByMemberIdAndKeyCode(memberId,MemberConfigEnum.ERP_JOIN_FLG.getKeyCode());
        return new ItemResult<>(conf != null);
    }

    @Operation(summary = "根据ID查看用户简要信息")
    @PostMapping(value="/findMemberSimpleById")
    public ItemResult<MemberSimpleDTO> findMemberSimpleById(@Parameter(hidden = true)LoginInfo loginInfo){
        checkLogin(loginInfo);
        MemberSimpleDTO simple = iMemberService.findMemberSimpleById(loginInfo.getMemberId());
        return new ItemResult<>(simple);
    }


    @Operation(summary = "获取会员类型")
    @GetMapping(value="/findMemberTypeByMemberId")
    public ItemResult<MemberTypeDTO> findMemberTypeByMemberId(@Parameter(hidden = true)LoginInfo loginInfo){
        checkLogin(loginInfo);
        MemberTypeDTO memberTypeDTO = iMemberService.findMemberTypeByMemberId(loginInfo.getMemberId());
        return new ItemResult<>(memberTypeDTO);
    }


    @Operation(summary = "根据电话查找用户信息")
    @PostMapping(value="/findMemberIdByContactPhone")
    public ItemResult<List<MemberSuperSimpleDTO>> findMemberIdByContactPhone(@RequestParam String contactPhone){
        List<MemberSuperSimpleDTO> phone = iMemberService.findMemberIdByContactPhone(contactPhone);
        return new ItemResult<>(phone);
    }


    @Operation(summary = "分页查询用户")
    @PostMapping(value="/pageMemberListView")
    public ItemResult<PageInfo<MemberListViewDTO>> pageMemberListView(@RequestBody MemberQueryPageDTO query){
        if (query.getPageNum() == null) {
            query.setPageNum(1);
        }
        if (query.getPageSize() == null) {
            query.setPageSize(10);
        }
        PageInfo<MemberListViewDTO> pageInfo = iMemberService.pageMemberListView(query.getMemberQueryDTO(), query.getPageNum(), query.getPageSize());
        return new ItemResult<>(pageInfo);
    }


    @Operation(summary = "分页查询企业承运商")
    @PostMapping(value="/pageEnterpriseCarrier")
    public ItemResult<PageInfo<MemberSimpleDTO>> pageEnterpriseCarrier(@RequestBody MemberQueryPageDTO query){
        if (query.getPageNum() == null) {
            query.setPageNum(1);
        }
        if (query.getPageSize() == null) {
            query.setPageSize(10);
        }
        PageInfo<MemberSimpleDTO> pageInfo = iMemberService.pageEnterpriseCarrier(query.getMemberQueryDTO(), query.getPageNum(), query.getPageSize());
        return new ItemResult<>(pageInfo);
    }


    @Operation(summary = "分页查询企业卖家")
    @PostMapping(value="/pageEnterpriseSeller")
    public ItemResult<PageInfo<MemberSimpleDTO>> pageEnterpriseSeller(@RequestBody MemberQueryPageDTO query){
        if (query.getPageNum() == null) {
            query.setPageNum(1);
        }
        if (query.getPageSize() == null) {
            query.setPageSize(10);
        }
        PageInfo<MemberSimpleDTO> pageInfo = iMemberService.pageEnterpriseSeller(query.getMemberQueryDTO(), query.getPageNum(), query.getPageSize());
        return new ItemResult<>(pageInfo);
    }


    @Operation(summary = "根据名字查找企业承运商")
    @PostMapping(value="/findEnterpriseCarrierByName")
    public ItemResult<List<MemberSuperSimpleDTO>> findEnterpriseCarrierByName(@RequestParam String carrierName){
        List<MemberSuperSimpleDTO> name = iMemberService.findEnterpriseCarrierByName(carrierName);
        return new ItemResult<>(name);
    }


    @Operation(summary = "根据请求ID查询资质")
    @PostMapping(value="/findMemberCertByRequestId")
    public ItemResult<MemberCertViewDTO> findMemberCertByRequestId(@RequestParam String requestId){
        MemberCertViewDTO name = iMemberService.findMemberCertByRequestId(requestId);
        return new ItemResult<>(name);
    }


    @Operation(summary = "根据名字查找企业卖家")
    @PostMapping(value="/findEnterpriseSellerByName")
    public ItemResult<List<MemberSuperSimpleDTO>> findEnterpriseSellerByName(@RequestParam String sellerName){
        List<MemberSuperSimpleDTO> sellerByName = iMemberService.findEnterpriseSellerByName(sellerName);
        return new ItemResult<>(sellerByName);
    }


    @Operation(summary = "审批司机认证")
    @PostMapping(value="/driverCertApproval")
    public ItemResult<Object> driverCertApproval(@Parameter(hidden = true) LoginInfo loginInfo){
        checkLogin(loginInfo);
        iMemberService.driverCertApproval(loginInfo.getMemberId());
        return new ItemResult<>(new Object());
    }


    @Operation(summary = "找到最近的变更记录")
    @PostMapping(value="/findRecentMemberApproveRequest")
    public ItemResult<Object> findRecentMemberApproveRequest(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody MemberApprovalRequestQueryDTO queryDTO){
        checkLogin(loginInfo);
        queryDTO.setMemberId(loginInfo.getMemberId());
        MemberApprovalRequestDTO request = iMemberService.findRecentMemberApproveRequest(queryDTO);
        return new ItemResult<>(request);
    }


    @Operation(summary = "变更企业经营信息")
    @PostMapping(value="/updateBusinessInfo")
    public ItemResult<Object> updateBusinessInfo(@Parameter(hidden = true) LoginInfo loginInfo, @Valid @RequestBody MemberBusinessInfoQueryDTO dto){
        MemberBusinessInfoDTO infoDTO = businessQueryInfo2BusinessInfo(dto);
        checkLogin(loginInfo);
        infoDTO.setMemberId(loginInfo.getMemberId());
        infoDTO.setOperatorId(loginInfo.getAccountId());
        infoDTO.setOperator(loginInfo.getOperator());
        String requestId = iMemberService.updateBusinessInfo(infoDTO);
        return new ItemResult<>(requestId);
    }

    @Operation(summary = "个人升级企业卖家")
    @PostMapping(value="/updateMemberToEnterpriseSeller")
    public ItemResult<String> updateMemberToEnterpriseSeller(@Parameter(hidden = true)LoginInfo loginInfo, @Valid @RequestBody MemberUpdateTypeQueryDTO dto){
        MemberUpdateTypeDTO typeDTO = updateTypeQuery2UpdateTypeDTO(dto);
        checkLogin(loginInfo);
        typeDTO.setMemberId(loginInfo.getMemberId());
        String requestId = iMemberService.updateMemberToEnterpriseSeller(typeDTO);
        return new ItemResult<>(requestId);
    }

    @Operation(summary = "保存实名认证资料")
    @PostMapping(value="/createRealNameCert")
    public ItemResult<Object> createRealNameCert(@Parameter(hidden = true) LoginInfo loginInfo, @Valid @RequestBody MemberCertQueryDTO dto){
        MemberCertDTO certDTO = memberCertQuery2MemberCert(dto);
        checkLogin(loginInfo);
        certDTO.setMemberId(loginInfo.getMemberId());
        certDTO.setAccountId(loginInfo.getAccountId());
        certDTO.setOperatorId(loginInfo.getAccountId());
        certDTO.setOperator(loginInfo.getOperator());
        String requestId = iMemberService.createRealNameCert(certDTO);
        return new ItemResult<>(requestId);
    }

    @Operation(summary = "保存实名认证资料（不需要审批）")
    @PostMapping(value="/createRealNameCert2")
    public ItemResult<Object> createRealNameCert2(@Parameter(hidden = true) LoginInfo loginInfo, @Valid @RequestBody MemberCertDTO dto){
        checkLogin(loginInfo);
        dto.setMemberId(loginInfo.getMemberId());
        dto.setAccountId(loginInfo.getAccountId());
        dto.setOperatorId(loginInfo.getAccountId());
        dto.setOperator(loginInfo.getOperator());
        dto.setRealNameCertNoApproval(true);
        String requestId = iMemberService.createRealNameCert(dto);
        return new ItemResult<>(requestId);
    }

    @Operation(summary = "个人升级承运商")
    @PostMapping(value="/updateMemberToEnterpriseCarrier")
    public ItemResult<String> updateMemberToEnterpriseCarrier(@Parameter(hidden = true)LoginInfo loginInfo, @Valid @RequestBody MemberUpdateTypeQueryDTO dto){
        MemberUpdateTypeDTO typeDTO = updateTypeQuery2UpdateTypeDTO(dto);
        checkLogin(loginInfo);
        typeDTO.setMemberId(loginInfo.getMemberId());
        typeDTO.setOperatorId(loginInfo.getAccountId());
        typeDTO.setOperator(loginInfo.getOperator());
        String requestId = iMemberService.updateMemberToEnterpriseCarrier(typeDTO);
        return new ItemResult<>(requestId);
    }


    @Operation(summary = "保存实名认证资料")
    @PostMapping(value="/saveRealNameCert")
    public ItemResult<Object> saveRealNameCert(@Parameter(hidden = true) LoginInfo loginInfo, @Valid @RequestBody MemberCertQueryDTO dto){
        MemberCertDTO certDTO = memberCertQuery2MemberCert(dto);
        checkLogin(loginInfo);
        certDTO.setMemberId(loginInfo.getMemberId());
        certDTO.setAccountId(loginInfo.getAccountId());
        certDTO.setOperatorId(loginInfo.getAccountId());
        certDTO.setOperator(loginInfo.getOperator());
        iMemberService.saveRealNameCert(certDTO);
        return new ItemResult<>(new Object());
    }

    @Operation(summary = "提交实名认证")
    @PostMapping(value="/submitRealNameCert")
    public ItemResult<Object> submitRealNameCert(@Parameter(hidden = true) LoginInfo loginInfo){
        checkLogin(loginInfo);
        String requestId = iMemberService.submitRealNameCert(loginInfo.getAccountId(),null);
        return new ItemResult<>(requestId);
    }


    @Operation(summary = "根据ID查找用户详情")
    @PostMapping(value="/findMemberDetailById")
    public ItemResult<MemberDTO> findMemberDetailById(@Parameter(hidden = true)LoginInfo loginInfo, String memberId){
        checkLogin(loginInfo);
        if (CsStringUtils.isEmpty(memberId)) {
            memberId = loginInfo.getMemberId();
        }
        //非平台账号只能访问自己的会员信息
        if( !MemberPlatform.PLATFORM_MEMBERID.getId().equals(loginInfo.getMemberId())){
            memberId = loginInfo.getMemberId();
        }
        //如果个人会员，则查询是否有企业申请审批被拒绝的信息
        if (loginInfo.getAccountType() != null && loginInfo.getAccountType() == AccountDTO.ACCOUNT_TYPE_PERSONAL) {
            MemberApprovalRequestQueryDTO query = new MemberApprovalRequestQueryDTO();
            query.setMemberId(loginInfo.getMemberId());
            PageInfo<MemberApprovalRequestDTO> pageInfo = iMemberService.pageRegisterMemberApprovalRequests(query, 1, 10);
            if( pageInfo != null && CollectionUtils.isNotEmpty(pageInfo.getList()) ){
                MemberApprovalRequestDTO approvalRequestDTO = pageInfo.getList().get(0);
                //如果最近一条记录是审批拒绝
                if( approvalRequestDTO != null && AdvertStatusEnum.REJECT.getCode().equals(approvalRequestDTO.getStatus()) &&
                        (ApproveRequestTypeEnum.REGISTER_ENTERPRISE_BUYER.getCode().equals(approvalRequestDTO.getRequestType()) ||
                         ApproveRequestTypeEnum.REGISTER_ENTERPRISE_SELLER.getCode().equals(approvalRequestDTO.getRequestType()) ||
                         ApproveRequestTypeEnum.REGISTER_ENTERPRISE_SUPPLIER.getCode().equals(approvalRequestDTO.getRequestType()) ||
                         ApproveRequestTypeEnum.REGISTER_ENTERPRISE_CARRIER.getCode().equals(approvalRequestDTO.getRequestType()) ) ) {
                    return new ItemResult<>(iMemberService.getMemberApprovalDetails(pageInfo.getList().get(0).getRequestId()));
                }
            }
        }
        if ( (loginInfo.getAccountType() == null) || (loginInfo.getAccountType().intValue() != AccountDTO.ACCOUNT_TYPE_MEMBER_MASTER) ) {
            return new ItemResult<>(iMemberService.findRealMemberById(memberId));
        }
        return new ItemResult<>(iMemberService.findMemberDetailById(memberId));
    }


    @Operation(summary = "个人升级供应商")
    @PostMapping(value="/updateMemberToEnterpriseSupplier")
    public ItemResult<String> updateMemberToEnterpriseSupplier(@Parameter(hidden = true)LoginInfo loginInfo, @Valid @RequestBody MemberUpdateTypeQueryDTO dto){
        MemberUpdateTypeDTO typeDTO = updateTypeQuery2UpdateTypeDTO(dto);
        checkLogin(loginInfo);
        typeDTO.setMemberId(loginInfo.getMemberId());
        typeDTO.setOperatorId(loginInfo.getAccountId());
        typeDTO.setOperator(loginInfo.getOperator());
        String requestId = iMemberService.updateMemberToEnterpriseSupplier(typeDTO);
        return new ItemResult<>(requestId);
    }


    @Operation(summary = "个人会员升级到买家企业会员")
    @PostMapping(value="/updateMemberToEnterpriseBuyer")
    public ItemResult<String> updateMemberToEnterpriseBuyer(@Parameter(hidden = true)LoginInfo loginInfo, @Valid @RequestBody MemberUpdateTypeQueryDTO dto){
        MemberUpdateTypeDTO typeDTO = updateTypeQuery2UpdateTypeDTO(dto);
        checkLogin(loginInfo);
        typeDTO.setMemberId(loginInfo.getMemberId());
        String requestId = iMemberService.updateMemberToEnterpriseBuyer(typeDTO);
        return new ItemResult<>(requestId);
    }


    @Operation(summary = "根据ID查找用户名")
    @PostMapping(value="/findMemberNameByMemberId")
    public ItemResult<String> findMemberNameByMemberId(@Parameter(hidden = true)LoginInfo loginInfo){
        checkLogin(loginInfo);
        String name = iMemberService.findMemberNameByMemberId(loginInfo.getMemberId());
        return new ItemResult<>(name);
    }


    @Operation(summary = "根据ids查找用户简单信息")
    @PostMapping(value="/findMemberSimpleByIds")
    public ItemResult<List<MemberSimpleDTO>> findMemberSimpleByIds(@RequestParam List<String> ids){
        List<MemberSimpleDTO> simple = iMemberService.findMemberSimpleByIds(ids);
        return new ItemResult<>(simple);
    }


    @Operation(summary = "根据用户ID查找认证详情")
    @PostMapping(value="/findMemberCertByMemberId")
    public ItemResult<List<MemberCertDTO>> findMemberCertByMemberId(@Parameter(hidden = true)LoginInfo loginInfo){
        checkLogin(loginInfo);
        List<MemberCertDTO> cert = iMemberService.findMemberCertByMemberId(loginInfo.getMemberId());
        return new ItemResult<>(cert);
    }


    @Operation(summary = "获取资质（已拥有资质、待审批资质、拒绝资质）")
    @PostMapping(value="/findMemberCertList")
    public ItemResult<List<MemberCertViewDTO>> findMemberCertList(@Parameter(hidden = true)LoginInfo loginInfo){
        checkLogin(loginInfo);
        List<MemberCertViewDTO> cert = iMemberService.findMemberCertList(loginInfo.getMemberId());
        return new ItemResult<>(cert);
    }


    @Operation(summary = "获取企业更新资质")
    @PostMapping(value="/findUpdateTypeMemberCertById")
    public ItemResult<List<MemberCertDTO>> findUpdateTypeMemberCertById(@RequestParam String requestId){
        List<MemberCertDTO> cert = iMemberService.findUpdateTypeMemberCertById(requestId);
        return new ItemResult<>(cert);
    }


    @Operation(summary = "根据ids查找用户详细信息")
    @PostMapping(value="/findMemberDetailsByIds")
    public ItemResult<List<MemberDetailDTO>> findMemberDetailsByIds(@RequestParam List<String> ids){
        List<MemberDetailDTO> memberDetailsByIds = iMemberService.findMemberDetailsByIds(ids);
        return new ItemResult<>(memberDetailsByIds);
    }


    @Operation(summary = "获取一条变更记录")
    @PostMapping(value="/findMemberApprovalRequest")
    public ItemResult<MemberApprovalRequestDTO> findMemberApprovalRequest(@RequestParam String requestId){
        MemberApprovalRequestDTO request = iMemberService.findMemberApprovalRequest(requestId);
        return new ItemResult<>(request);
    }

    @Operation(summary = "获取会员请求列表")
    @PostMapping(value="/findMemberApprovalRequestList")
    public ItemResult<List<MemberApprovalRequestDTO>> findMemberApprovalRequestList(@Parameter(hidden = true)LoginInfo loginInfo, @RequestBody MemberApprovalRequestQueryDTO query){
        checkLogin(loginInfo);
        query.setMemberId(loginInfo.getMemberId());
        List<MemberApprovalRequestDTO> list = iMemberService.findMemberApprovalRequestList(query);
        return new ItemResult<>(list);
    }

    @Operation(summary = "分页获取会员请求")
    @PostMapping(value="/pageMemberApprovalRequests")
    public ItemResult<PageInfo<MemberApprovalRequestDTO>> pageMemberApprovalRequests(@Parameter(hidden = true)LoginInfo loginInfo, @RequestBody MemberApprovalRequestPageDTO query){
        if (query.getPageNum() == null) {
            query.setPageNum(1);
        }
        if (query.getPageSize() == null) {
            query.setPageSize(10);
        }
        if( !MemberPlatform.PLATFORM_MEMBERID.getId().equals(loginInfo.getMemberId())  ) {
            query.getMemberApprovalRequestQueryDTO().setMemberId(loginInfo.getMemberId());
        }
        PageInfo<MemberApprovalRequestDTO> pageInfo = iMemberService.pageMemberApprovalRequests(query.getMemberApprovalRequestQueryDTO(), query.getPageNum(), query.getPageSize());
        return new ItemResult<>(pageInfo);
    }


    @Operation(summary = "获取会员请求详情")
    @PostMapping(value="/getMemberApprovalDetails")
    public ItemResult<MemberDTO> getMemberApprovalDetails(@RequestParam String requestId){
        MemberDTO details = iMemberService.getMemberApprovalDetails(requestId);
        return new ItemResult<>(details);
    }


    @Operation(summary = "根据资质ID查找资质详情")
    @PostMapping(value="/findMemberCertByCertId")
    public ItemResult<MemberCertDTO> findMemberCertByCertId(@RequestParam String certId){
        MemberCertDTO cert = iMemberService.findMemberCertByCertId(certId);
        return new ItemResult<>(cert);
    }


    @Operation(summary = "根据ids查找用户")
    @PostMapping(value="/findMemberByIds")
    public ItemResult<List<MemberDTO>> findMemberByIds(@RequestParam List<String> ids){
        List<MemberDTO> member = iMemberService.findMemberByIds(ids);
        return new ItemResult<>(member);
    }


    @Operation(summary = "个人名是否使用")
    @PostMapping(value="/isMemberNameUsed")
    public ItemResult<Boolean> isMemberNameUsed(@RequestParam String memberName){
        boolean used = iMemberService.isMemberNameUsed(memberName);
        return new ItemResult<>(used);
    }


    @Operation(summary = "提交企业注册（买家） requestType - 申请类型 必须指明每个资质的资质类型，是经营资质、卖家资质、承运商资质。。。")
    @PostMapping(value="/registerBuyer")
    public ItemResult<String> registerBuyer(@Parameter(hidden = true)LoginInfo loginInfo, @Valid @RequestBody MemberRegisterDTO registerDTO){
        MemberRequestDTO dto = registerDTO2RequestDTO(registerDTO);
        checkLogin(loginInfo);
        dto.setMemberId(loginInfo.getMemberId());
        dto.setAccountId(loginInfo.getAccountId());
        dto.setOperatorId(loginInfo.getAccountId());
        dto.setOperator(loginInfo.getOperator());
        String s = iMemberService.registerBuyer(dto);
        return new ItemResult<>(s);
    }


    @Operation(summary = "保存经营信息 *草稿*")
    @PostMapping(value="/saveBusinessInfoDraft")
    public ItemResult<String> saveBusinessInfoDraft(@Parameter(hidden = true)LoginInfo loginInfo, @Valid @RequestBody MemberRegisterByAppDTO dto){
        checkLogin(loginInfo);
        dto.setMemberId(loginInfo.getMemberId());
        dto.setAccountId(loginInfo.getAccountId());
        dto.setOperatorId(loginInfo.getAccountId());
        dto.setOperator(loginInfo.getOperator());
        String s = iMemberService.saveBusinessInfoDraft(dto);
        return new ItemResult<>(s);
    }


    @Operation(summary = "保存经营信息 *草稿*")
    @PostMapping(value="/saveRequestUserDraft")
    public String saveRequestUserDraft(@Parameter(hidden = true)LoginInfo loginInfo, @RequestBody MemberCertQueryDTO memberCertQueryDTO){
        checkLogin(loginInfo);
        MemberCertDTO certDTO = memberCertQuery2MemberCert(memberCertQueryDTO);
        certDTO.setMemberId(loginInfo.getMemberId());
        certDTO.setAccountId(loginInfo.getAccountId());
        certDTO.setOperatorId(loginInfo.getAccountId());
        certDTO.setOperator(loginInfo.getOperator());
        return iMemberService.saveRequestUserDraft(certDTO);
    }

    /**
     * 提交保存的草稿
     * @return
     */
    @Operation(summary = "提交保存的草稿")
    @PostMapping(value="/submitDraftRegisterBuyer")
    public String submitDraftRegisterBuyer(@Parameter(hidden = true)LoginInfo loginInfo) {
        checkLogin(loginInfo);
        return iMemberService.submitDraftRegisterBuyer(loginInfo.getMemberId());
    }
    /**
     * 提交保存的草稿
     * @return
     */
    @Operation(summary = "提交保存的草稿")
    @PostMapping(value="/submitDraftRegisterSeller")
    public String submitDraftRegisterSeller(@Parameter(hidden = true)LoginInfo loginInfo) {
        checkLogin(loginInfo);
        return iMemberService.submitDraftRegisterSeller(loginInfo.getMemberId());
    }
    /**
     * 提交保存的草稿
     * @return
     */
    @Operation(summary = "提交保存的草稿")
    @PostMapping(value="/submitDraftRegisterCarrier")
    public String submitDraftRegisterCarrier(@Parameter(hidden = true)LoginInfo loginInfo) {
        checkLogin(loginInfo);
        return iMemberService.submitDraftRegisterCarrier(loginInfo.getMemberId());
    }
    /**
     * 提交保存的草稿
     * @return
     */
    @Operation(summary = "提交保存的草稿")
    @PostMapping(value="/submitDraftRegisterSupplier")
    public String submitDraftRegisterSupplier(@Parameter(hidden = true)LoginInfo loginInfo) {
        checkLogin(loginInfo);
        return iMemberService.submitDraftRegisterSupplier(loginInfo.getMemberId());
    }

    @Operation(summary = "查询草稿")
    @PostMapping(value="/findDraft")
    public ItemResult<MemberRequestDTO> findDraft(@Parameter(hidden = true)LoginInfo loginInfo){
        checkLogin(loginInfo);
        MemberRequestDTO draft = iMemberService.findDraft(loginInfo.getMemberId());
        return new ItemResult<>(draft);
    }


    @Operation(summary = "查询草稿")
    @PostMapping(value="/hasNoApproveRequest")
    public ItemResult<Object> hasNoApproveRequest(@Parameter(hidden = true) LoginInfo loginInfo){
        checkLogin(loginInfo);
        boolean draft = iMemberService.hasNoApproveRequest(loginInfo.getMemberId());
        return new ItemResult<>(draft);
    }


    @Operation(summary = "企业注册（承运商）")
    @PostMapping(value="/registerCarrier")
    public ItemResult<String> registerCarrier(@Parameter(hidden = true)LoginInfo loginInfo, @Valid @RequestBody MemberRegisterDTO registerDTO) {
        MemberRequestDTO dto = registerDTO2RequestDTO(registerDTO);
        checkLogin(loginInfo);
        dto.setMemberId(loginInfo.getMemberId());
        dto.setAccountId(loginInfo.getAccountId());
        dto.setOperatorId(loginInfo.getAccountId());
        dto.setOperator(loginInfo.getOperator());
        String s = iMemberService.registerCarrier(dto);
        return new ItemResult<>(s);
    }


    @Operation(summary = "分页查询用户")
    @PostMapping(value="/pageMember")
    public ItemResult<PageInfo<MemberSimpleDTO>> pageMember(@RequestBody MemberQueryPageDTO query){
        if (query.getPageNum() == null) {
            query.setPageNum(1);
        }
        if (query.getPageSize() == null) {
            query.setPageSize(10);
        }
        PageInfo<MemberSimpleDTO> pageInfo = iMemberService.pageMember(query.getMemberQueryDTO(), query.getPageNum(), query.getPageSize());
        return new ItemResult<>(pageInfo);
    }


    @Operation(summary = "一般资质变更,必须写明资质类型 certType、CertId")
    @PostMapping(value="/updateCert")
    public ItemResult<String> updateCert(@Parameter(hidden = true)LoginInfo loginInfo, @Valid @RequestBody UpdateCertDTO dto){
        checkLogin(loginInfo);
        dto.setOperatorId(loginInfo.getAccountId());
        dto.setOperator(loginInfo.getOperator());
        if( dto.getMemberCertDTOList() != null && !dto.getMemberCertDTOList().isEmpty() ){
            List<MemberCertDTO> filterResult = Lists.newArrayList();
            dto.getMemberCertDTOList().stream().filter(item -> CsStringUtils.isNotBlank(item.getCertType())).forEach(item -> {
                item.setMemberId(loginInfo.getMemberId());
                filterResult.add(item);
            });
            if( filterResult.isEmpty() ){
                throw new BizException(BasicCode.CUSTOM_ERROR,"剔除证件类型未空的证件后没有可更新的证件了");
            }
            dto.setMemberCertDTOList(filterResult);
        }
        return new ItemResult<>(iMemberService.updateCert(dto));
    }


    @Operation(summary = "按照id查询用户（没有资质）")
    @PostMapping(value="/findMemberById")
    public ItemResult<MemberDetailDTO> findMemberById(@Parameter(hidden = true)LoginInfo loginInfo, String memberId){
        checkLogin(loginInfo);
        if (CsStringUtils.isEmpty(memberId)) {
            memberId = loginInfo.getMemberId();
        }
        MemberDetailDTO member = iMemberService.findMemberById(memberId);
        return new ItemResult<>(member);
    }


    @Operation(summary = "按照id查询用户经营信息（包括审批中资质）")
    @PostMapping(value="/findMemberBusinessInfoById")
    public ItemResult<MemberBusinessInfoDTO> findMemberBusinessInfoById(@Parameter(hidden = true)LoginInfo loginInfo){
        checkLogin(loginInfo);
        MemberBusinessInfoDTO member = iMemberService.findMemberBusinessInfoById(loginInfo.getMemberId());
        return new ItemResult<>(member);
    }

    @Operation(summary = "根据账户ID查找实名认证详情")
    @PostMapping(value="/findRealNameCertByAccountId")
    public ItemResult<MemberCertDTO> findRealNameCertByAccountId(@RequestParam String accountId){
        MemberCertDTO cert = iMemberService.findRealNameCertByAccountId(accountId);
        return new ItemResult<>(cert);
    }


    @Operation(summary = "按照id查询用户（只包括已审批的资质）")
    @PostMapping(value="/findRealMemberById")
    public ItemResult<MemberDTO> findRealMemberById(@Parameter(hidden = true)LoginInfo loginInfo, @RequestParam(required = false) String memberId){
        return new ItemResult<>(iMemberService.findRealMemberById(CsStringUtils.isBlank(memberId) ? loginInfo.getMemberId() : memberId));
    }

    @Operation(summary = "企业注册（供应商）")
    @PostMapping(value="/registerSupplier")
    public ItemResult<String> registerSupplier(@Parameter(hidden = true)LoginInfo loginInfo, @Valid @RequestBody MemberRegisterDTO registerDTO){
        MemberRequestDTO dto = registerDTO2RequestDTO(registerDTO);
        checkLogin(loginInfo);
        dto.setMemberId(loginInfo.getMemberId());
        dto.setAccountId(loginInfo.getAccountId());
        dto.setOperatorId(loginInfo.getAccountId());
        dto.setOperator(loginInfo.getOperator());
        String s = iMemberService.registerSupplier(dto);
        return new ItemResult<>(s);
    }


    @Operation(summary = "资质文件认证,必须写明资质类型 certType")
    @PostMapping(value="/submitCert")
    public ItemResult<String> submitCert(@Parameter(hidden = true)LoginInfo loginInfo, @Valid @RequestBody SubmitCertDTO dto){
        checkLogin(loginInfo);
        dto.setOperatorId(loginInfo.getAccountId());
        dto.setOperator(loginInfo.getOperator());
        if( dto.getMemberCertDTOList() != null && !dto.getMemberCertDTOList().isEmpty() ){
            dto.getMemberCertDTOList().forEach(item ->{
                item.setMemberId(loginInfo.getMemberId());
            });
        }
        return new ItemResult<>(iMemberService.submitCert(dto));
    }

    @Operation(summary = "撤销会员变更请求")
    @PostMapping(value="/cancelRequest")
    public ItemResult<Boolean> cancelRequest(LoginInfo loginInfo,@Valid @RequestBody CancelRequestDTO dto){
        checkLogin(loginInfo);
        dto.setOperatorId(loginInfo.getAccountId());
        return new ItemResult<>(iMemberService.cancelRequest(dto));
    }


    @Operation(summary = "企业注册（卖家）")
    @PostMapping(value="/registerSeller")
    public ItemResult<String> registerSeller(@Parameter(hidden = true)LoginInfo loginInfo, @Valid @RequestBody MemberRegisterDTO registerDTO){
        MemberRequestDTO dto = registerDTO2RequestDTO(registerDTO);
        checkLogin(loginInfo);
        dto.setMemberId(loginInfo.getMemberId());
        dto.setAccountId(loginInfo.getAccountId());
        dto.setOperatorId(loginInfo.getAccountId());
        dto.setOperator(loginInfo.getOperator());
        String s = iMemberService.registerSeller(dto);
        return new ItemResult<>(s);
    }

    @Operation(summary = "拒绝司机认证")
    @PostMapping(value="/driverCertReject")
    public ItemResult<Object> driverCertReject(@Parameter(hidden = true) LoginInfo loginInfo){
        checkLogin(loginInfo);
        iMemberService.driverCertReject(loginInfo.getMemberId());
        return new ItemResult<>(new Object());
    }


    @Operation(summary = "更新会员基本信息")
    @PostMapping(value="/updateBaseInfo")
    public ItemResult<Object> updateBaseInfo(@Parameter(hidden = true) LoginInfo loginInfo, @Valid @RequestBody MemberBaseInfoQueryDTO dto){
        MemberBaseInfoDTO infoDTO = baseInfoQuery2BaseInfo(dto);
        checkLogin(loginInfo);
        infoDTO.setMemberId(loginInfo.getMemberId());
        infoDTO.setOperatorId(loginInfo.getAccountId());
        infoDTO.setOperator(loginInfo.getOperator());
        iMemberService.updateBaseInfo(infoDTO);
        return new ItemResult<>(new Object());
    }


    @Operation(summary = "获取会员状态")
    @PostMapping(value="/getMemberStatus")
    public ItemResult<Object> getMemberStatus(@Parameter(hidden = true) LoginInfo loginInfo){
        checkLogin(loginInfo);
        MemberStatusDTO memberStatus = iMemberService.getMemberStatus(loginInfo.getMemberId());
        return new ItemResult<>(memberStatus);
    }

    @Operation(summary = "分页获取会员请求")
    @PostMapping(value="/pageRegisterMemberApprovalRequests")
    public ItemResult<PageInfo<MemberApprovalRequestDTO>> pageRegisterMemberApprovalRequests(@RequestBody MemberApprovalRequestPageDTO query){
        if (query.getPageNum() == null) {
            query.setPageNum(1);
        }
        if (query.getPageSize() == null) {
            query.setPageSize(10);
        }
        PageInfo<MemberApprovalRequestDTO> info = iMemberService.pageRegisterMemberApprovalRequests(query.getMemberApprovalRequestQueryDTO(), query.getPageNum(), query.getPageSize());
        return new ItemResult<>(info);
    }

    @Operation(summary = "查询附近的卖家")
    @GetMapping(value="/anon/findSellerByCityCode")
    public ItemResult<List<MemberSimpleDTO>> findSellerByCityCode(String cityCode){
        MemberQueryDTO query = new MemberQueryDTO();
        query.setCityCode(cityCode);
        query.setSellerFlg(1);
        PageInfo<MemberSimpleDTO> page = iMemberService.pageMember(query,1,20);
        return new ItemResult<>(page == null ? Lists.newArrayList() : page.getList());
    }


    @Operation(summary = "根据用户ID查找个体船东认证列表")
    @PostMapping(value="/findMemberAttachmentByMemberId")
    public ItemResult<List<AttachmentDTO>> findMemberAttachmentByMemberId(@Parameter(hidden = true)LoginInfo loginInfo){
        checkLogin(loginInfo);
        List<AttachmentDTO> certList = iMemberService.findMemberAttachmentByMemberId(loginInfo.getMemberId());
        return new ItemResult<>(certList);
    }


    @Operation(summary = "根据条件查询会员名称及会员ID")
    @PostMapping(value="/findMemberNameByQuery")
    public ItemResult<Map<String,String>> findMemberNameByQuery(@Parameter(hidden = true)LoginInfo loginInfo, @RequestBody MemberQueryDTO query){
        checkLogin(loginInfo);
        Map<String,String> memberMap = iMemberService.findMemberNameByQuery(query);
        return new ItemResult<>(memberMap);
    }

    @Operation(summary = "根据会员Id判断承运商身份")
    @PostMapping(value="/carrierIdentityById")
    public ItemResult<CarrierIdentityDTO> carrierIdentityById(@Parameter(hidden = true)LoginInfo loginInfo) throws BizException {
        return new ItemResult<CarrierIdentityDTO>(iMemberService.carrierIdentityById(loginInfo.getMemberId()));
    }

    @Operation(summary = "租户列表")
    @GetMapping(value="/tenantList")
    public ItemResult<List<KeyValueDTO>> tenantList() throws BizException {
        PageQuery<TenantQueryDTO> query = new PageQuery<>();
        query.setPageNum(1);
        query.setPageSize(50);
        PageInfo<TenantDTO> pageInfo = tenantService.findAll(query);
        if(pageInfo != null && CollectionUtils.isNotEmpty(pageInfo.getList())){
            List<KeyValueDTO> list = pageInfo.getList().stream().map(item -> {
                KeyValueDTO dto = new KeyValueDTO();
                dto.setKey(item.getTenantId());
                dto.setValue(item.getTenantName());
                return dto;
            }).toList();
            return new ItemResult<>(list);
        }
        return new ItemResult<>(Lists.newArrayList());
    }

    @Operation(summary = "商品类型列表")
    @GetMapping(value="/goodsCategoryList")
    public ItemResult<List<KeyValueDTO>> goodsCategoryList() throws BizException {
        List<CategoryTreeDTO> categoryTree = goodsCategoryService.findCategoryTree("002001");
        if(CollectionUtils.isNotEmpty(categoryTree) ){
            CategoryTreeDTO categoryTreeDTO = categoryTree.stream().map(item -> getSubTree(item, "002001")).filter(Objects::nonNull).findFirst().orElse(null);
            if( categoryTreeDTO != null && CollectionUtils.isNotEmpty(categoryTreeDTO.getChilds())) {
                List<KeyValueDTO> list = categoryTreeDTO.getChilds().stream().map(item -> {
                    KeyValueDTO dto = new KeyValueDTO();
                    dto.setKey(item.getCategoryId());
                    dto.setValue(item.getCategoryName());
                    return dto;
                }).toList();
                return new ItemResult<>(list);
            }
        }
        return new ItemResult<>(Lists.newArrayList());
    }
    //获取以 002001 节点为根的子树
    private CategoryTreeDTO getSubTree(CategoryTreeDTO categoryTree,String code ){
        if (CsStringUtils.equals(categoryTree.getCategoryCode(), code)) {
            return categoryTree;
        }
        if( CollectionUtils.isNotEmpty(categoryTree.getChilds())){
            for (CategoryTreeDTO child : categoryTree.getChilds()) {
                CategoryTreeDTO result = getSubTree(child,code);
                if( result != null ) {
                    return result;
                }
            }
        }
        return null;
    }

    private MemberRequestDTO registerDTO2RequestDTO(MemberRegisterDTO memberRegisterDTO) {
        MemberRequestDTO memberRequestDTO = new MemberRequestDTO();
        BeanUtils.copyProperties(memberRegisterDTO, memberRequestDTO);

        if (memberRegisterDTO.getBusinessQueryCert() != null) {
            memberRequestDTO.setBusinessCert(memberCertQuery2MemberCert(memberRegisterDTO.getBusinessQueryCert()));
        }
        if (memberRegisterDTO.getOrganizationQueryCert() != null) {
            memberRequestDTO.setOrganizationCert(memberCertQuery2MemberCert(memberRegisterDTO.getOrganizationQueryCert()));
        }
        if (memberRegisterDTO.getTaxQueryCert() != null) {
            memberRequestDTO.setTaxCert(memberCertQuery2MemberCert(memberRegisterDTO.getTaxQueryCert()));
        }

        if (memberRegisterDTO.getMemberRegisterCertDTOList() != null && !memberRegisterDTO.getMemberRegisterCertDTOList().isEmpty()) {
            List<MemberCertDTO> collect = memberRegisterDTO.getMemberRegisterCertDTOList().stream()
                    .map(item -> {
                        MemberCertDTO memberCertDTO = new MemberCertDTO();
                        BeanUtils.copyProperties(item, memberCertDTO);
                        return memberCertDTO;
                    }).toList();
            memberRequestDTO.setMemberCertDTOList(collect);
        }
        return memberRequestDTO;
    }

    private MemberBusinessInfoDTO businessQueryInfo2BusinessInfo(MemberBusinessInfoQueryDTO dto) {
        MemberBusinessInfoDTO infoDTO = new MemberBusinessInfoDTO();
        if (dto.getBusinessCert() != null) {
            infoDTO.setBusinessCert(memberCertQuery2MemberCert(dto.getBusinessCert()));
        }
        if (dto.getOrganizationCert() != null) {
            infoDTO.setOrganizationCert(memberCertQuery2MemberCert(dto.getOrganizationCert()));
        }
        if (dto.getTaxCert() != null) {
            infoDTO.setTaxCert(memberCertQuery2MemberCert(dto.getTaxCert()));
        }
        BeanUtils.copyProperties(dto, infoDTO);
        return infoDTO;
    }

    private MemberCertDTO memberCertQuery2MemberCert(MemberCertQueryDTO dto) {
        MemberCertDTO memberCertDTO = new MemberCertDTO();
        BeanUtils.copyProperties(dto, memberCertDTO);
        return memberCertDTO;
    }

    private MemberUpdateTypeDTO updateTypeQuery2UpdateTypeDTO(MemberUpdateTypeQueryDTO dto) {
        MemberUpdateTypeDTO memberUpdateTypeDTO = new MemberUpdateTypeDTO();
        memberUpdateTypeDTO.setMemberId(dto.getMemberId());
        memberUpdateTypeDTO.setMemberCertTypeDTOList(dto.getMemberCertQueryDTOList().stream()
                .map(item -> {
                    MemberCertDTO memberCertDTO = new MemberCertDTO();
                    BeanUtils.copyProperties(item, memberCertDTO);
                    return memberCertDTO;
                }).toList());
        return memberUpdateTypeDTO;
    }

    private MemberBaseInfoDTO baseInfoQuery2BaseInfo(MemberBaseInfoQueryDTO dto) {
        MemberBaseInfoDTO memberBaseInfoDTO = new MemberBaseInfoDTO();
        BeanUtils.copyProperties(dto, memberBaseInfoDTO);
        return memberBaseInfoDTO;
    }

    private void checkLogin(LoginInfo loginInfo){
        if (loginInfo == null || CsStringUtils.isBlank(loginInfo.getAccountId())) {
            throw new BizException(MemberCode.LOGIN_ERROR,"你还没有登录，请先登录");
        }
    }
}
