package com.ecommerce.web.base.controller;

import com.alibaba.fastjson.JSON;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.member.api.dto.carrier.CarrierBindingListDTO;
import com.ecommerce.member.api.dto.relation.MemberBlacklistDTO;
import com.ecommerce.member.api.dto.relation.MemberRelationAddDTO;
import com.ecommerce.member.api.dto.relation.MemberRelationDTO;
import com.ecommerce.member.api.dto.relation.MemberRelationQueryDTO;
import com.ecommerce.member.api.dto.relation.MemberTagDTO;
import com.ecommerce.member.api.dto.relation.MemberTagGroupDTO;
import com.ecommerce.member.api.service.ICarrierBindingService;
import com.ecommerce.member.api.service.IMemberRelationService;
import com.ecommerce.member.api.session.LoginInfo;
import com.ecommerce.web.base.dto.relation.MemberBlacklistQueryPageDTO;
import com.ecommerce.web.base.dto.relation.MemberRelationListQueryDTO;
import com.ecommerce.web.base.dto.relation.MemberRelationPageDTO;
import com.ecommerce.web.base.dto.relation.MemberTagCreateDTO;
import com.ecommerce.web.base.dto.relation.MemberTagGroupQueryDTO;
import com.ecommerce.web.base.dto.relation.MemberTagGroupUpdateDTO;
import com.ecommerce.web.base.dto.relation.MemberTagUpdateDTO;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * TODO
 *
 * <AUTHOR>
 */
@Slf4j
@Tag(name = "MemberRelationController", description = "会员关系")
@RestController
@RequestMapping("/relation")
public class MemberRelationController {

    @Autowired
    private IMemberRelationService iMemberRelationService;

    @Autowired
    private ICarrierBindingService carrierBindingService;

    private static final String MEMBER_ID = "memberId";

    @Operation(summary = "创建标签")
    @PostMapping(value="/createMemberTag")
    public ItemResult<Object> createMemberTag(@Parameter(hidden = true) LoginInfo loginInfo, @Valid @RequestBody MemberTagCreateDTO memberTagCreateDTO){
        MemberTagDTO memberTagDTO = new MemberTagDTO();
        BeanUtils.copyProperties(memberTagCreateDTO, memberTagDTO);
        iMemberRelationService.createMemberTag(memberTagDTO, loginInfo.getAccountId());
        return new ItemResult<>(new Object());
    }


    @Operation(summary = "删除标签")
    @PostMapping(value="/deleteMemberTag")
    public ItemResult<Object> deleteMemberTag(@Parameter(hidden = true) LoginInfo loginInfo, @RequestParam String memberTagId){
        iMemberRelationService.deleteMemberTag(memberTagId, loginInfo.getAccountId());
        return new ItemResult<>(new Object());
    }


    @Operation(summary = "修改标签信息")
    @PostMapping(value="/updateMemberTag")
    public ItemResult<Object> updateMemberTag(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody MemberTagUpdateDTO memberTagUpdateDTO){
        MemberTagDTO memberTagDTO = new MemberTagDTO();
        BeanUtils.copyProperties(memberTagUpdateDTO, memberTagDTO);
        iMemberRelationService.updateMemberTag(memberTagDTO, loginInfo.getAccountId());
        return new ItemResult<>(new Object());
    }


    @Operation(summary = "查询标签组")
    @PostMapping(value="/findMemberGroup")
    public ItemResult<List<MemberTagGroupDTO>> findMemberGroup(@RequestBody MemberTagGroupQueryDTO memberTagGroupQueryDTO){
        MemberTagGroupDTO memberTagGroupDTO = new MemberTagGroupDTO();
        BeanUtils.copyProperties(memberTagGroupQueryDTO, memberTagGroupDTO);
        List<MemberTagGroupDTO> memberGroup = iMemberRelationService.findMemberGroup(memberTagGroupDTO);
        return new ItemResult<>(memberGroup);
    }


    @Operation(summary = "查询标签组内的标签")
    @PostMapping(value="/listMemberGroup")
    public ItemResult<List<MemberTagDTO>> listMemberGroup(@RequestParam String memberTagGroupId){
        List<MemberTagDTO> memberTagDTOS = iMemberRelationService.listMemberGroup(memberTagGroupId);
        return new ItemResult<>(memberTagDTOS);
    }


    @Operation(summary = "查询标签详情")
    @PostMapping(value="/getMemberTag")
    public ItemResult<MemberTagDTO> getMemberTag(@RequestParam String memberTagId){
        MemberTagDTO memberTag = iMemberRelationService.getMemberTag(memberTagId);
        return new ItemResult<>(memberTag);
    }


    @Operation(summary = "判断两个对象是否有黑名单关系（myself，target）{memberId:aa,customerId:bb}")
    @PostMapping(value="/isDefriend")
    public ItemResult<Boolean> isDefriend(@RequestBody Map<String,String> map){
        String memberId = map.getOrDefault(MEMBER_ID,null);
        String customerId = map.getOrDefault("customerId",null);
        if (CsStringUtils.isBlank(memberId)) {
            throw new BizException(BasicCode.INVALID_PARAM,MEMBER_ID);
        }
        if (CsStringUtils.isBlank(customerId)) {
            throw new BizException(BasicCode.INVALID_PARAM,"customerId");
        }
        Boolean defriend = iMemberRelationService.isDefriend(memberId, customerId);
        return new ItemResult<>(defriend);
    }


    @Operation(summary = "为客户关系添加标签")
    @PostMapping(value="/addTagRelation")
    public ItemResult<Object> addTagRelation(@Parameter(hidden = true) LoginInfo loginInfo, @RequestParam String relationId, @RequestParam String tagId){
        iMemberRelationService.addTagRelation(relationId, tagId, loginInfo.getAccountId());
        return new ItemResult<>(new Object());
    }


    @Operation(summary = "根据关系ID查询会员关系详情")
    @PostMapping(value="/getMemberRelationDetailByRelationId")
    public ItemResult<MemberRelationDTO> getMemberRelationDetailByRelationId(@RequestParam String relationId){
        MemberRelationDTO detail = iMemberRelationService.getMemberRelationDetailByRelationId(relationId);
        return new ItemResult<>(detail);
    }


    @Operation(summary = "根据会员ID和客户ID查询会员关系详情")
    @PostMapping(value="/getMemberRelationDetailByMemberIdAndCustomerId")
    public ItemResult<MemberRelationDTO> getMemberRelationDetailByMemberIdAndCustomerId(@RequestParam String memberId,@RequestParam String customerId){
        MemberRelationDTO detail = iMemberRelationService.getMemberRelationDetailByMemberIdAndCustomerId(memberId, customerId);
        return new ItemResult<>(detail);
    }


    @Operation(summary = "移除客户关系")
    @PostMapping(value="/removeMemberRelationByMemberIdAndCustomerId")
    public ItemResult<Object> removeMemberRelationByMemberIdAndCustomerId(@Parameter(hidden = true) LoginInfo loginInfo, @RequestParam String memberId, @RequestParam String customerId){
        iMemberRelationService.removeMemberRelationByMemberIdAndCustomerId(memberId,customerId, loginInfo.getAccountId());
        return new ItemResult<>(new Object());
    }


    @Operation(summary = "查询标签组详情")
    @PostMapping(value="/findMemberTagGroupDetail")
    public ItemResult<MemberTagGroupDTO> findMemberTagGroupDetail(@RequestParam String memberTagGroupId){
        MemberTagGroupDTO groupDetail = iMemberRelationService.findMemberTagGroupDetail(memberTagGroupId);
        return new ItemResult<>(groupDetail);
    }


    @Operation(summary = "为客户关系移除标签")
    @PostMapping(value="/removeTagRelation")
    public ItemResult<Object> removeTagRelation(@RequestParam String relationId, @RequestParam String tagId){
        iMemberRelationService.removeTagRelation(relationId,tagId);
        return new ItemResult<>(new Object());
    }


    @Operation(summary = "批量得到某会员所有黑名单信息")
    @PostMapping(value="/pageBlacklist")
    public ItemResult<PageInfo<MemberBlacklistDTO>> pageBlacklist(@RequestBody MemberBlacklistQueryPageDTO dto){
        if (dto.getPageNum() == null) {
            dto.setPageNum(1);
        }
        if (dto.getPageSize() == null) {
            dto.setPageSize(10);
        }
        PageInfo<MemberBlacklistDTO> blacklist = iMemberRelationService.pageBlacklist(dto.getMemberBlacklistQueryDTO(), dto.getPageNum(), dto.getPageSize());
        return new ItemResult<>(blacklist);
    }


    @Operation(summary = "修改会员关系")
    @PostMapping(value="/updateMemberRelation")
    public ItemResult<Object> updateMemberRelation(@Parameter(hidden = true) LoginInfo loginInfo, @Valid @RequestBody MemberRelationDTO memberRelationDTO){
        iMemberRelationService.updateMemberRelation(memberRelationDTO, loginInfo.getAccountId());
        return new ItemResult<>(new Object());
    }


    @Operation(summary = "把某对象从某会员黑名单列表中删除")
    @PostMapping(value="/removeObjFromBlacklist")
    public ItemResult<Object> removeObjFromBlacklist(@Parameter(hidden = true) LoginInfo loginInfo, @RequestParam String memberId, @RequestParam String blackMemberId, @RequestParam String remarks){
        iMemberRelationService.removeObjFromBlacklist(memberId, blackMemberId,loginInfo.getMemberName(),remarks);
        return new ItemResult<>(new Object());
    }


    @Operation(summary = "通过交易添加会员关系")
    @PostMapping(value="/addMemberRelationByTrade")
    public ItemResult<Object> addMemberRelationByTrade(@RequestParam String baseMemberId, @RequestParam String customerMemberId){
        iMemberRelationService.addMemberRelationByTrade(baseMemberId,customerMemberId);
        return new ItemResult<>(new Object());
    }


    @Operation(summary = "是否可以删除标签")
    @PostMapping(value="/canDeleteMemberTag")
    public ItemResult<Boolean> canDeleteMemberTag(@RequestParam String memberTagId){
        boolean b = iMemberRelationService.canDeleteMemberTag(memberTagId);
        return new ItemResult<>(b);
    }


    @Operation(summary = "由商家自己添加客户关系")
    @PostMapping(value="/addMemberRelationBySelf")
    public ItemResult<Object> addMemberRelationBySelf(@RequestParam String baseMemberId, @RequestParam String customerMemberId){
        MemberRelationAddDTO memberRelationAddDTO = new MemberRelationAddDTO();
        memberRelationAddDTO.setMemberId(baseMemberId);
        memberRelationAddDTO.setCustomerId(customerMemberId);
        iMemberRelationService.addMemberRelationBySelf(memberRelationAddDTO);
        return new ItemResult<>(new Object());
    }


    @Operation(summary = "管理员添加会员关系")
    @PostMapping(value="/addMemberRelationByAdmin")
    public ItemResult<Object> addMemberRelationByAdmin(@Parameter(hidden = true) LoginInfo loginInfo, @RequestParam String baseMemberId, @RequestParam String customerMemberId){
        iMemberRelationService.addMemberRelationByAdmin(baseMemberId,customerMemberId, loginInfo.getAccountId());
        return new ItemResult<>(new Object());
    }


    @Operation(summary = "通过系统添加会员关系")
    @PostMapping(value="/addMemberRelationBySystem")
    public ItemResult<Object> addMemberRelationBySystem(@RequestParam String baseMemberId, @RequestParam String customerMemberId){
        MemberRelationAddDTO memberRelationAddDTO = new MemberRelationAddDTO();
        memberRelationAddDTO.setMemberId(baseMemberId);
        memberRelationAddDTO.setCustomerId(customerMemberId);
        iMemberRelationService.addMemberRelationBySystem(memberRelationAddDTO);
        return new ItemResult<>(new Object());
    }


    @Operation(summary = "移除客户关系")
    @PostMapping(value="/removeMemberRelationByRelationId")
    public ItemResult<Object> removeMemberRelationByRelationId(@Parameter(hidden = true) LoginInfo loginInfo, @RequestParam String relationId){
        iMemberRelationService.removeMemberRelationByRelationId(relationId, loginInfo.getAccountId());
        return new ItemResult<>(new Object());
    }


    @Operation(summary = "删除标签组")
    @PostMapping(value="/deleteMemberTagGroup")
    public ItemResult<Object> deleteMemberTagGroup(@Parameter(hidden = true) LoginInfo loginInfo, @RequestParam String memberTagGroupId){
        iMemberRelationService.deleteMemberTagGroup(memberTagGroupId, loginInfo.getAccountId());
        return new ItemResult<>(new Object());
    }


    @Operation(summary = "修改标签组信息")
    @PostMapping(value="/updateMemberGroup")
    public ItemResult<Object> updateMemberGroup(@Parameter(hidden = true) LoginInfo loginInfo, @Valid @RequestBody MemberTagGroupUpdateDTO memberTagGroupUpdateDTO){
        MemberTagGroupDTO memberTagGroupDTO = new MemberTagGroupDTO();
        BeanUtils.copyProperties(memberTagGroupUpdateDTO, memberTagGroupDTO);
        iMemberRelationService.updateMemberGroup(memberTagGroupDTO, loginInfo.getAccountId());
        return new ItemResult<>(new Object());
    }


    @Operation(summary = "增加某对象进黑名单")
    @PostMapping(value="/addObjToBlacklist")
    public ItemResult<Object> addObjToBlacklist(@Parameter(hidden = true) LoginInfo loginInfo, @RequestParam String memberId, @RequestParam String blackMemberId, @RequestParam String remarks){
        iMemberRelationService.addObjToBlacklist(memberId,blackMemberId, loginInfo.getMemberName(),remarks);
        return new ItemResult<>(new Object());
    }


    @Operation(summary = "创建标签组 （数量控制）")
    @PostMapping(value="/createMemberTagGroup")
    public ItemResult<Object> createMemberTagGroup(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody MemberTagGroupDTO memberTagGroupDTO){
        if (CsStringUtils.isEmpty(memberTagGroupDTO.getMemberId())) {
            throw new BizException(BasicCode.PARAM_NULL, MEMBER_ID);
        }
        iMemberRelationService.createMemberTagGroup(memberTagGroupDTO, loginInfo.getAccountId());
        return new ItemResult<>(new Object());
    }


    @Operation(summary = "分页查询客户（分类型）")
    @PostMapping(value="/pageRelationMember")
    public ItemResult<PageInfo<MemberRelationDTO>> pageRelationMember(@RequestBody MemberRelationPageDTO memberRelationPageDTO){
        if (memberRelationPageDTO.getPageNum() == null) {
            memberRelationPageDTO.setPageNum(1);
        }
        if (memberRelationPageDTO.getPageSize() == null) {
            memberRelationPageDTO.setPageSize(10);
        }
        MemberRelationQueryDTO memberRelationQueryDTO = new MemberRelationQueryDTO();
        BeanUtils.copyProperties(memberRelationPageDTO.getMemberRelationListQueryDTO(), memberRelationQueryDTO);
        PageInfo<MemberRelationDTO> pageInfo = iMemberRelationService.pageRelationMember(memberRelationQueryDTO, memberRelationPageDTO.getPageNum(), memberRelationPageDTO.getPageSize());
        return new ItemResult<>(pageInfo);
    }


    @Operation(summary = "是否可以删除标签组")
    @PostMapping(value="/canDeleteMemberTagGroup")
    public ItemResult<Boolean> canDeleteMemberTagGroup(@RequestParam String memberGroupId){
        boolean b = iMemberRelationService.canDeleteMemberTagGroup(memberGroupId);
        return new ItemResult<>(b);
    }


    @Operation(summary = "查询会员关系返回list")
    @PostMapping(value="/findRelationMemberList")
    public ItemResult<List<MemberRelationDTO>> findRelationMemberList(@RequestBody MemberRelationListQueryDTO query){
        MemberRelationQueryDTO memberRelationQueryDTO = new MemberRelationQueryDTO();
        BeanUtils.copyProperties(query, memberRelationQueryDTO);
        List<MemberRelationDTO> b = iMemberRelationService.findRelationMemberList(memberRelationQueryDTO);
        return new ItemResult<>(b);
    }




    /**
     * 承运商查询厂商ERP卖家
     * @return
     */
    @Operation(summary = "承运商查询厂商ERP卖家")
    @PostMapping(value = "/findManufacturerByMemberId")
    public ItemResult<List<MemberRelationDTO>> findManufacturerByMemberId(@Parameter(hidden = true)LoginInfo loginInfo) {
        List<MemberRelationDTO> list = new ArrayList<>();
        ItemResult<List<CarrierBindingListDTO>> itemResult = carrierBindingService.queryCarrierBindingByCarrier(loginInfo.getMemberId());
        log.info("queryCarrierBindingByCarrier : {}", JSON.toJSONString(itemResult));
        if (itemResult != null && itemResult.isSuccess() && CollectionUtils.isNotEmpty(itemResult.getData())) {
            itemResult.getData().stream()
                    .filter(i -> CsStringUtils.isNotBlank(i.getErpCarrierNo()))
                    .forEach(bindingDTO -> {
                        MemberRelationDTO relationDTO = new MemberRelationDTO();
                        relationDTO.setMemberId(bindingDTO.getBindingMemberId());
                        relationDTO.setMemberName(bindingDTO.getBindingMemberName());
                        list.add(relationDTO);
                    });
        }
        return new ItemResult<>(list);
    }

}
