package com.ecommerce.web.base.controller;


import com.ecommerce.base.api.dto.AccountPermissionDTO;
import com.ecommerce.base.api.dto.authRes.GetMenuTreeDTO;
import com.ecommerce.base.api.service.IMenuService;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.member.api.service.IMemberRelationService;
import com.ecommerce.member.api.session.LoginInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 *
 * <AUTHOR>
 */
@Slf4j
@Tag(name = "Menu", description = "获取菜单")
@RestController
@RequestMapping("/menu")
public class MenuController {

    @Autowired
    private IMenuService menuService;
    @Autowired
    private IMemberRelationService iMemberRelationService;
    @Value("${spring.cloud.config.profile}")
    private String profile;


    @Operation(summary = "获取登录菜单菜单、页面、按钮分离")
    @GetMapping(value="/getMenuTree")
    public ItemResult<AccountPermissionDTO> getMenuTree(@Parameter(hidden = true)LoginInfo loginInfo, @RequestParam String platform){
        GetMenuTreeDTO dto = new GetMenuTreeDTO();
        dto.setPlatform(platform);
        dto.setRoleIdList(loginInfo.getRoleList());
        AccountPermissionDTO accountPermissionDTO = menuService.getMenuTree(dto);
        updateMenuTree(accountPermissionDTO,platform, loginInfo.getMemberId());
        return new ItemResult<>(accountPermissionDTO);
    }

    //盈信的买家不显示价格-资金信息
    private void updateMenuTree(AccountPermissionDTO accountPermissionDTO,String platform,String memberId){
        if( !"buyer".equals(platform) && !"buyer_app".equals(platform) ){
            return;
        }
        if(!whetherSellerCustomer(memberId)){
            return;
        }
        if("buyer".equals(platform) && !CollectionUtils.isEmpty(accountPermissionDTO.getMenuTreeList())) {
            for (int i = 0; i < accountPermissionDTO.getMenuTreeList().size(); i++) {
                if ("B05".equals(accountPermissionDTO.getMenuTreeList().get(i).getMenuCode())) {
                    accountPermissionDTO.getMenuTreeList().remove(i);
                    return;
                }
            }
        }else
        if("buyer_app".equals(platform) && !CollectionUtils.isEmpty(accountPermissionDTO.getButtonList()) ) {
            for (int i = 0; i < accountPermissionDTO.getButtonList().size(); i++) {
                if ("2102F01-04".equals(accountPermissionDTO.getButtonList().get(i).getPermCode())) {
                    accountPermissionDTO.getButtonList().get(i).setSelected(false);
                    return;
                }
            }
        }
    }

    private boolean whetherSellerCustomer(String buyerId) {
        //生产环境 广东盈信建材有限公司 memberCode:A0028E
        String sellerId = "gcw9qhv30xwkefmp5fkgpqnx";
        if ( "dev".equals(profile) || "test".equals(profile) ){
            //13jgi7agtp6rmvmbwf8hnnbai	靖西市云星贸易有限公司2019333	靖西市云星贸易有限公司	3nwcaji311spiccmdpaejmdv	A00102	204
            sellerId = "13jgi7agtp6rmvmbwf8hnnbai";
        }else if ( "sit".equals(profile) ){
            //4mm74h90dsjkse0kfs6ip09q	重庆市互商投资有限公司	互商	502sbu040b5bxkw667a4rqsn	A0024S
        }else if ( "uat".equals(profile) || "newuat".equals(profile) ){
            //3t81dt8020khei56ix9pivpif	广东盈信建材有限公司	广东盈信建材有限公司	328em2m6cgitdif29ij578ix9	A001YI	202
            sellerId = "3t81dt8020khei56ix9pivpif";
        }else if ( "prod".equals(profile) ){
            //生产环境 广东盈信建材有限公司 memberCode:A0028E
            sellerId = "gcw9qhv30xwkefmp5fkgpqnx";
        }
        Boolean whetherSellerCustomer = iMemberRelationService.relationIsExists(sellerId,buyerId);
        log.info("relationIsExists,memberId:{},customerId:{},result:{}",sellerId,buyerId,whetherSellerCustomer);
        return whetherSellerCustomer == null ? false : whetherSellerCustomer;
    }

}
