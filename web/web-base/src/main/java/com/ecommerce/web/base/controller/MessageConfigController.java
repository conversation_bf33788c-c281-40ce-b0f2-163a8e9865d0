package com.ecommerce.web.base.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.base.api.dto.MessageConfigDTO;
import com.ecommerce.base.api.dto.MessageConfigDisableAccountDTO;
import com.ecommerce.base.api.dto.MessageConfigDisableMemberDTO;
import com.ecommerce.base.api.dto.MessageConfigEnableAccountDTO;
import com.ecommerce.base.api.dto.MessageConfigEnableMemberDTO;
import com.ecommerce.base.api.service.IMessageConfigService;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.member.api.session.LoginInfo;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * null
 *
 * <AUTHOR>
*/

@RestController
@Tag(name = "MessagConfig", description = "消息配置")
@RequestMapping("/messagConfig")
public class MessageConfigController {

   @Autowired 
   private IMessageConfigService iMessageConfigService;

   @Operation(summary = "会员启用消息(type 0短信 1站内信 2消息推送)")
   @PostMapping(value="/enableMemberConfig")
   public ItemResult<Object> enableMemberConfig(@Parameter(hidden = true)LoginInfo loginInfo,
                                                @RequestParam(required = false) String messageConfigId,
                                                @RequestParam(value = "all",defaultValue = "1") String enableAll,
                                                @RequestParam(defaultValue = "1") Integer type){
      MessageConfigEnableMemberDTO messageConfigEnableMemberDTO = new MessageConfigEnableMemberDTO();
      messageConfigEnableMemberDTO.setOperator(loginInfo.getAccountId());
      messageConfigEnableMemberDTO.setMemberId(loginInfo.getMemberId());
       if (CsStringUtils.isNotBlank(messageConfigId)) {
         messageConfigEnableMemberDTO.setIds(Lists.newArrayList(messageConfigId));
      }else{
         messageConfigEnableMemberDTO.setAllFlag("1".equals(enableAll));
         messageConfigEnableMemberDTO.setType(type);
      }
      return new ItemResult<>(iMessageConfigService.enableMemberConfig(messageConfigEnableMemberDTO));
   }

   @Operation(summary = "会员禁用消息")
   @PostMapping(value="/disableMemberConfig")
   public ItemResult<Object> disableMemberConfig(@Parameter(hidden = true)LoginInfo loginInfo,
                                                 @RequestParam(required = false) String messageConfigId,
                                                 @RequestParam(value = "all",defaultValue = "1") String disableAll,
                                                 @RequestParam(defaultValue = "1") Integer type){
      MessageConfigDisableMemberDTO messageConfigDisableMemberDTO = new MessageConfigDisableMemberDTO();
      messageConfigDisableMemberDTO.setOperator(loginInfo.getAccountId());
      messageConfigDisableMemberDTO.setMemberId(loginInfo.getMemberId());
       if (CsStringUtils.isNotBlank(messageConfigId)) {
         messageConfigDisableMemberDTO.setIds(Lists.newArrayList(messageConfigId));
      }else{
         messageConfigDisableMemberDTO.setAllFlag("1".equals(disableAll));
         messageConfigDisableMemberDTO.setType(type);
      }
      return new ItemResult<>(iMessageConfigService.disableMemberConfig(messageConfigDisableMemberDTO));
   }

   @Operation(summary = "查询会员短信消息模板配置")
   @PostMapping(value="/findMemberInnerMessageConfig")
   public ItemResult<List<MessageConfigDTO>> findMemberInnerMessageConfig(@Parameter(hidden = true)LoginInfo loginInfo){
      return new ItemResult<>(iMessageConfigService.findMemberInnerMessageConfig(loginInfo.getMemberId()));
   }

   @Operation(summary = "分页查询会员站内信消息模板配置")
   @PostMapping(value="/pageMemberInnerMessageConfig")
   public ItemResult<PageInfo<MessageConfigDTO>> pageMemberInnerMessageConfig(@Parameter(hidden = true)LoginInfo loginInfo, @RequestParam Integer pageSize,
                                                                              @RequestParam Integer pageNum){
      return new ItemResult<>(iMessageConfigService.pageMemberMessageConfig(loginInfo.getMemberId(),MessageConfigDTO.MESSAGE_TYPE_INNER,pageSize,pageNum));
   }

    @Operation(summary = "分页查询会员短信信消息模板配置")
    @PostMapping(value="/pageMemberSMSMessageConfig")
    public ItemResult<PageInfo<MessageConfigDTO>> pageMemberSMSMessageConfig(@Parameter(hidden = true)LoginInfo loginInfo, @RequestParam Integer pageSize,
                                                                               @RequestParam Integer pageNum){
        return new ItemResult<>(iMessageConfigService.pageMemberMessageConfig(loginInfo.getMemberId(),MessageConfigDTO.MESSAGE_TYPE_SMS,pageSize,pageNum));
    }

   @Operation(summary = "分页查询会员app推送消息模板配置")
   @PostMapping(value="/pageMemberAppMessageConfig")
   public ItemResult<PageInfo<MessageConfigDTO>> pageMemberAppMessageConfig(@Parameter(hidden = true)LoginInfo loginInfo, @RequestParam Integer pageSize,
                                                                            @RequestParam Integer pageNum){
      return new ItemResult<>(iMessageConfigService.pageMemberMessageConfig(loginInfo.getMemberId(),MessageConfigDTO.MESSAGE_TYPE_APP,pageSize,pageNum));
   }

   @Operation(summary = "查询会员站内信消息模板配置")
   @PostMapping(value="/findMemberSMSMessageConfig")
   public ItemResult<List<MessageConfigDTO>> findMemberSMSMessageConfig(@Parameter(hidden = true)LoginInfo loginInfo){
      return new ItemResult<>(iMessageConfigService.findMemberSMSMessageConfig(loginInfo.getMemberId()));
   }

   @Operation(summary = "查询会员app消息模板配置")
   @PostMapping(value="/findMemberAppMessageConfig")
   public ItemResult<List<MessageConfigDTO>> findMemberAppMessageConfig(@Parameter(hidden = true)LoginInfo loginInfo){
      return new ItemResult<>(iMessageConfigService.findMemberMessageConfig(loginInfo.getMemberId(),MessageConfigDTO.MESSAGE_TYPE_APP));
   }

   @Operation(summary = "分页查询账号站内信消息模板配置")
   @PostMapping(value="/pageAccountInnerMessageConfig")
   public ItemResult<PageInfo<MessageConfigDTO>> pageAccountInnerMessageConfig(@Parameter(hidden = true)LoginInfo loginInfo, @RequestParam Integer pageSize,
                                                                              @RequestParam Integer pageNum){
      return new ItemResult<>(iMessageConfigService.pageAccountMessageConfig(loginInfo.getAccountId(),MessageConfigDTO.MESSAGE_TYPE_INNER,pageSize,pageNum));
   }

   @Operation(summary = "分页查询账号短信消息模板配置")
   @PostMapping(value="/pageAccountSMSMessageConfig")
   public ItemResult<PageInfo<MessageConfigDTO>> pageAccountSMSMessageConfig(@Parameter(hidden = true)LoginInfo loginInfo, @RequestParam Integer pageSize,
                                                                            @RequestParam Integer pageNum){
      return new ItemResult<>(iMessageConfigService.pageAccountMessageConfig(loginInfo.getAccountId(),MessageConfigDTO.MESSAGE_TYPE_SMS,pageSize,pageNum));
   }

   @Operation(summary = "分页查询账号app推送消息模板配置")
   @PostMapping(value="/pageAccountAppMessageConfig")
   public ItemResult<PageInfo<MessageConfigDTO>> pageAccountAppMessageConfig(@Parameter(hidden = true)LoginInfo loginInfo, @RequestParam Integer pageSize,
                                                                            @RequestParam Integer pageNum){
      return new ItemResult<>(iMessageConfigService.pageAccountMessageConfig(loginInfo.getAccountId(),MessageConfigDTO.MESSAGE_TYPE_APP,pageSize,pageNum));
   }

   @Operation(summary = "账号启用消息")
   @PostMapping(value="/enableAccountConfig")
   public ItemResult<Boolean> enableAccountConfig(@Parameter(hidden = true)LoginInfo loginInfo,
                                                 @RequestParam(required = false) String messageConfigId,
                                                 @RequestParam(value = "all",defaultValue = "1") String enableAll,
                                                 @RequestParam(defaultValue = "1") Integer type){
      MessageConfigEnableAccountDTO messageConfigEnableAccountDTO = new MessageConfigEnableAccountDTO();
      messageConfigEnableAccountDTO.setOperator(loginInfo.getAccountId());
      messageConfigEnableAccountDTO.setAccountId(loginInfo.getAccountId());
      messageConfigEnableAccountDTO.setMemberId(loginInfo.getMemberId());
       if (CsStringUtils.isNotBlank(messageConfigId)) {
         messageConfigEnableAccountDTO.setIds(Lists.newArrayList(messageConfigId));
      }else{
         messageConfigEnableAccountDTO.setAllFlag("1".equals(enableAll));
         messageConfigEnableAccountDTO.setType(type);
      }
      return new ItemResult<>(iMessageConfigService.enableAccountConfig(messageConfigEnableAccountDTO));
   }

   @Operation(summary = "账号禁用消息")
   @PostMapping(value="/disableAccountConfig")
   public ItemResult<Boolean> disableAccountConfig(@Parameter(hidden = true)LoginInfo loginInfo,
                                                   @RequestParam(required = false) String messageConfigId,
                                                   @RequestParam(value = "all",defaultValue = "1") String disableAll,
                                                   @RequestParam(defaultValue = "1") Integer type){
      MessageConfigDisableAccountDTO messageConfigDisableAccountDTO = new MessageConfigDisableAccountDTO();
      messageConfigDisableAccountDTO.setOperator(loginInfo.getAccountId());
      messageConfigDisableAccountDTO.setAccountId(loginInfo.getAccountId());
      messageConfigDisableAccountDTO.setMemberId(loginInfo.getMemberId());
       if (CsStringUtils.isNotBlank(messageConfigId)) {
         messageConfigDisableAccountDTO.setIds(Lists.newArrayList(messageConfigId));
      }else{
         messageConfigDisableAccountDTO.setAllFlag("1".equals(disableAll));
         messageConfigDisableAccountDTO.setType(type);
      }
      return new ItemResult<>(iMessageConfigService.disableAccountConfig(messageConfigDisableAccountDTO));
   }
}
