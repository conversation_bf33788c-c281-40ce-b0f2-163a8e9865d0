package com.ecommerce.web.base.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.common.result.ItemResult;
import com.ecommerce.information.api.dto.advertisement.AdvertisementDetailDTO;
import com.ecommerce.information.api.dto.tendering.SearchTenderingDTO;
import com.ecommerce.information.api.dto.tendering.TenderingDTO;
import com.ecommerce.information.api.service.ITenderingInfoService;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * 投诉建议服务接口
 *
 * <AUTHOR>
*/
@RestController
@Tag(name = "TenderingInformationController", description = "招标")
@CrossOrigin
public class TenderingInformationController {

   @Autowired 
   private ITenderingInfoService tenderingInfoService;

   @Operation(summary = "分页获取招标信息")
   @PostMapping(value="/getTenderingPageInfo")
   public ItemResult<PageInfo<TenderingDTO>> getTenderingPageInfo(@RequestBody SearchTenderingDTO searchTenderingDTO){
      PageInfo<TenderingDTO> result = tenderingInfoService.getTenderingPageInfo(searchTenderingDTO);
      return new ItemResult<PageInfo<TenderingDTO>>(result);
   }


   @Operation(summary = "获取招标信息详情")
   @PostMapping(value="/getTenderingDetailInfoByTid")
   public  ItemResult<TenderingDTO> getTenderingDetailInfoByTid(@RequestParam String tid){
      TenderingDTO result = tenderingInfoService.getTenderingDetailInfoByTid(tid);
      return new ItemResult<TenderingDTO>(result);
   }


   @Operation(summary = "获取招标广告信息")
   @PostMapping(value="/getTenderingAdvertisingByTid")
   public ItemResult<AdvertisementDetailDTO> getTenderingAdvertisingByTid(@RequestParam String tid){
      return new ItemResult<>(tenderingInfoService.getTenderingAdvertisingByTid(tid));
   }

}
