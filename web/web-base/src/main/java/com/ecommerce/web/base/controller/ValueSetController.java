package com.ecommerce.web.base.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.base.api.dto.ValueAndOptionDTO;
import com.ecommerce.base.api.dto.ValueQueryDTO;
import com.ecommerce.base.api.dto.ValueSetDTO;
import com.ecommerce.base.api.dto.ValueSetOptionDTO;
import com.ecommerce.base.api.dto.ValueSetTreeDTO;
import com.ecommerce.base.api.service.IValueSetService;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.member.api.session.LoginInfo;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * : 值集服务
 *
 * <AUTHOR>
 */

@RestController
@Tag(name = "ValueSet", description = "值集服务")
@RequestMapping("/valueSet")
public class ValueSetController {

   @Autowired
   private IValueSetService iValueSetService;

   @Operation(summary = "根据值集id得到值集")
   @PostMapping(value="/getValueSet")
   public ItemResult<ValueSetDTO> getValueSet(@RequestParam String valueSetId){
      return new ItemResult<>(iValueSetService.getValueSet(valueSetId));
   }


   @Operation(summary = "通过值集id删除值集")
   @PostMapping(value="/deleteValueSet")
   public ItemResult<Object> deleteValueSet(@Parameter(hidden = true) LoginInfo loginInfo, @RequestParam String valueSetId){
      iValueSetService.deleteValueSet(valueSetId,loginInfo.getAccountId());
      return new ItemResult<>(new Object());

   }


   @Operation(summary = "值集分页查询")
   @PostMapping(value="/pageValueSet")
   public ItemResult<PageInfo<ValueSetDTO>> pageValueSet(@RequestBody ValueQueryDTO valueQueryDTO){
      return new ItemResult<>(iValueSetService.pageValueSet(valueQueryDTO));
   }


   @Operation(summary = "创建值集")
   @PostMapping(value="/createValueSet")
   public ItemResult<Object> createValueSet(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody ValueSetDTO valueSet){
      iValueSetService.createValueSet(valueSet,loginInfo.getAccountId());
      return new ItemResult<>(new Object());

   }


   @Operation(summary = "该值集是否可修改")
   @PostMapping(value="/canValueSetEdit")
   public ItemResult<Boolean> canValueSetEdit(@RequestParam String valueSetId){
      return new ItemResult<>(iValueSetService.canValueSetEdit(valueSetId));
   }


   @Operation(summary = "修改值集")
   @PostMapping(value="/updateValueSet")
   public ItemResult<Object> updateValueSet(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody ValueSetDTO valueSet){
      iValueSetService.updateValueSet(valueSet,loginInfo.getAccountId());
      return new ItemResult<>(new Object());

   }


   @Operation(summary = "根据code获取值集子节点树")
   @PostMapping(value="/getValueSetByRefrenceCode")
   public ItemResult<ValueSetTreeDTO> getValueSetByRefrenceCode(@RequestParam String refrenceCode){
      return new ItemResult<>(iValueSetService.getValueSetByReferenceCode(refrenceCode));
   }


   @Operation(summary = "创建值集项")
   @PostMapping(value="/createValueSetOption")
   public ItemResult<Object> createValueSetOption(@RequestBody ValueSetOptionDTO valueSet, @RequestParam String operator){
      iValueSetService.createValueSetOption(valueSet,operator);
      return new ItemResult<>(new Object());

   }


   @Operation(summary = "创建值集和值集项")
   @PostMapping(value="/createValueAndOption")
   public ItemResult<Object> createValueAndOption(@Parameter(hidden = true) LoginInfo logininfo, @RequestBody ValueAndOptionDTO valueAndOptionDTO){
      valueAndOptionDTO.setMemberId(logininfo.getMemberId());
      iValueSetService.createValueAndOption(valueAndOptionDTO,logininfo.getAccountId());
      return new ItemResult<>(new Object());

   }


   @Operation(summary = "修改值集项")
   @PostMapping(value="/updateValueSetOptions")
   public ItemResult<Object> updateValueSetOptions(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody List<ValueSetOptionDTO> valueSetOptions){
      iValueSetService.updateValueSetOptions(valueSetOptions,loginInfo.getAccountId());
      return new ItemResult<>(new Object());

   }


   @Operation(summary = "更新值集和值集项")
   @PostMapping(value="/updateValueAndOption")
   public ItemResult<Object> updateValueAndOption(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody ValueAndOptionDTO valueAndOptionDTO){
      valueAndOptionDTO.setMemberId(loginInfo.getMemberId());
      iValueSetService.updateValueAndOption(valueAndOptionDTO,loginInfo.getAccountId());
      return new ItemResult<>(new Object());

   }


   @Operation(summary = "通过值集项id删除值集项")
   @PostMapping(value="/deleteValueSetOption")
   public ItemResult<Object> deleteValueSetOption(@Parameter(hidden = true) LoginInfo loginInfo, @RequestParam String valueSetId){
      iValueSetService.deleteValueSetOption(valueSetId,loginInfo.getAccountId());
      return new ItemResult<>(new Object());

   }


   @Operation(summary = "根据父节点id获取子节点树")
   @PostMapping(value="/getChildValueSetOption")
   public ItemResult<ValueSetTreeDTO> getChildValueSetOption(@RequestParam String parentOptionId){
      return new ItemResult<>(iValueSetService.getChildValueSetOption(parentOptionId));
   }


   @Operation(summary = "根据值集项id得到值集项")
   @PostMapping(value="/getValueSetOption")
   public ItemResult<ValueSetOptionDTO> getValueSetOption(@RequestParam String valueSetOptionId){
      return new ItemResult<>(iValueSetService.getValueSetOption(valueSetOptionId));
   }


   @Operation(summary = "根据值集id查询值集项")
   @PostMapping(value="/listValueSetOption")
   public ItemResult<List<ValueSetOptionDTO>> listValueSetOption(@RequestParam String valueSetId){
      return new ItemResult<>(iValueSetService.listValueSetOption(valueSetId));
   }

   @Operation(summary = "通过key查询值集合")
   @PostMapping(value="/findOptionByKey")
   public ItemResult<List<ValueSetOptionDTO>> findOptionByKey(@RequestParam String key){
      return new ItemResult<>(iValueSetService.findOptionByKey(key));
   }

   @Operation(summary = "通过key的集合查询值集合")
   @PostMapping(value="/batchFindOption")
   public ItemResult<List<ValueSetOptionDTO>> batchFindOption(@RequestBody List<String> keys){
      return new ItemResult<>(iValueSetService.batchFindOption(keys));
   }



}
