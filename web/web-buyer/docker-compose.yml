
services:
  web-buyer:
    image: ecommerce-web-buyer:1.0
    volumes:
      - /remote/logs/web-buyer:/var/log
      - /remote/skywalking:/home/<USER>
    deploy:
      resources:
        limits:
          memory: 800m
    container_name: web-buyer
    restart: always
    ports:
      - "8082:8082"
    environment:
      - SPRING_CLOUD_CONFIG_URI=http://************:8888
      - SPRING_CLOUD_CONFIG_LABEL=asset-2025
      - SPRING_CLOUD_CONFIG_PROFILE=dev
      - SPRING_CLOUD_CONFIG_NAME=web-buyer,eureka,vip-server,kafka,rabbitmq,redis
      - TZ=Asia/Shanghai
      - JAVA_TOOL_OPTIONS=-Xms256m -Xmx512m -XX:+UseSerialGC
    depends_on:
      - config
      - eureka