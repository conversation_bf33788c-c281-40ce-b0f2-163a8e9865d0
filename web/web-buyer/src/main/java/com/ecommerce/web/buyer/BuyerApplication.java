package com.ecommerce.web.buyer;

import com.ecommerce.common.annotation.ExcludeFromComponetScan;
import com.ecommerce.common.config.mybatis.DatasourceConfig;
import com.ecommerce.common.config.mybatis.MybatisConfiguration;
import com.ecommerce.rabbitmq.annotation.EnableRabbitMq;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.session.config.annotation.web.http.EnableSpringHttpSession;

@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class, HibernateJpaAutoConfiguration.class})
@EnableFeignClients(value = {
        "com.ecommerce.member.api",
        "com.ecommerce.goods.api",
        "com.ecommerce.base.api",
        "com.ecommerce.logistics.api",
        "com.ecommerce.information.api",
        "com.ecommerce.order.api",
        "com.ecommerce.pay.api",
        "com.ecommerce.price.api",
        "com.ecommerce.trace.api",
        "com.ecommerce.report.api"
})
@EnableSpringHttpSession
@ComponentScan(value = {
        "com.ecommerce.web.buyer",
        "com.ecommerce.common.config",
        "com.ecommerce.common.service",
        "com.ecommerce.web.common",
        "com.ecommerce.logistics",
        "com.ecommerce.trace",
        "com.ecommerce.order",
        "com.ecommerce.pay",
        "com.ecommerce.price",
        "com.ecommerce.base"
        }, excludeFilters = {
        @ComponentScan.Filter(type = FilterType.ANNOTATION, value = ExcludeFromComponetScan.class),
        @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, value = MybatisConfiguration.class),
        @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, value = DatasourceConfig.class)
        })
@EnableRabbitMq
public class BuyerApplication {
    public static void main(String[] args) {
        SpringApplication.run(BuyerApplication.class, args);
    }
}
