package com.ecommerce.web.buyer.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.member.api.dto.account.AccountChangeHistoryDTO;
import com.ecommerce.member.api.dto.account.PageAccountChangeHistoryDTO;
import com.ecommerce.member.api.service.IAccountChangeHistoryService;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * @Created锛�Fri Jun 28 17:13:06 CST 2019
 * <AUTHOR>
 * @Version:2
 * @Description:角色启用，禁用，授权历史
 */

@RestController
@Tag(name = "AccountChangeHistoryController", description = "账号变更历史")
@RequestMapping("/accountChangeHistory")
public class AccountChangeHistoryController {

    @Autowired
    private IAccountChangeHistoryService iAccountChangeHistoryService;

    @Operation(summary = "根据DTO分页查询员工授权，启用，禁用历史")
    @PostMapping(value = "/pageHistoryInfoList")
    public ItemResult<PageInfo<AccountChangeHistoryDTO>> pageHistoryInfoList(@Parameter(name = "pageAccountChangeHistoryDTO", description = "员工授权，启用，禁用历史DTO") @RequestBody PageAccountChangeHistoryDTO pageAccountChangeHistoryDTO) {
        return new ItemResult<>(iAccountChangeHistoryService.pageHistoryInfoList(pageAccountChangeHistoryDTO));
    }
}
