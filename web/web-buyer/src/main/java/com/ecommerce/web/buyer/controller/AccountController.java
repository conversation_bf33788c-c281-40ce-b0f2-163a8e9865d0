package com.ecommerce.web.buyer.controller;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.ecommerce.base.api.dto.authRes.DataPermDTO;
import com.ecommerce.base.api.dto.role.AccountRoleDTO;
import com.ecommerce.base.api.dto.role.RoleDTO;
import com.ecommerce.base.api.dto.saleregion.SaleRegionSampleDTO;
import com.ecommerce.base.api.enums.AccountPlatformEnum;
import com.ecommerce.base.api.enums.BaseRoleTypeEnum;
import com.ecommerce.base.api.service.IAccountRegionService;
import com.ecommerce.base.api.service.IDatapermService;
import com.ecommerce.base.api.service.IRoleService;
import com.ecommerce.base.api.service.ISaleRegionService;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.common.utils.mail.CollectionKit;
import com.ecommerce.logistics.api.dto.driver.AssignDriverLogCondDTO;
import com.ecommerce.logistics.api.dto.driver.AssignDriverLogResDTO;
import com.ecommerce.logistics.api.enums.UserRoleEnum;
import com.ecommerce.logistics.api.service.IAssignDriverLogService;
import com.ecommerce.member.api.dto.account.AccountBaseInfoUpdateDTO;
import com.ecommerce.member.api.dto.account.AccountBindingDTO;
import com.ecommerce.member.api.dto.account.AccountBindingSampleDTO;
import com.ecommerce.member.api.dto.account.AccountDTO;
import com.ecommerce.member.api.dto.account.AccountRegisterByMasterDTO;
import com.ecommerce.member.api.dto.account.AccountSearchDTO;
import com.ecommerce.member.api.dto.account.AccountSimpleDTO;
import com.ecommerce.member.api.dto.account.AccountWXBindingDTO;
import com.ecommerce.member.api.dto.account.ChangeAccountTypeDTO;
import com.ecommerce.member.api.dto.account.DeviceTokenDTO;
import com.ecommerce.member.api.dto.account.SubAccountUpdateDTO;
import com.ecommerce.member.api.dto.account.checkCode.CheckSMSCodeDTO;
import com.ecommerce.member.api.dto.account.checkCode.GetSMSCodeDTO;
import com.ecommerce.member.api.dto.exception.MemberCode;
import com.ecommerce.member.api.dto.member.MemberDetailDTO;
import com.ecommerce.member.api.dto.member.MemberSimpleDTO;
import com.ecommerce.member.api.dto.member.config.MemberConfigDTO;
import com.ecommerce.member.api.dto.member.config.MemberConfigEnum;
import com.ecommerce.member.api.service.IAccountService;
import com.ecommerce.member.api.service.IMemberConfigService;
import com.ecommerce.member.api.service.IMemberService;
import com.ecommerce.member.api.service.IOrgInfoService;
import com.ecommerce.member.api.session.LoginInfo;
import com.ecommerce.web.buyer.utils.CommonUtils;
import com.ecommerce.web.buyer.utils.IPUtils;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;


/**
 * web端控制器，账户相关功能对外服务接口
 *
 * <AUTHOR>
 */

@Slf4j
@Tag(name = "AccountController", description = "买家账户信息处理")
@RestController
@RequestMapping("/account")
public class AccountController {

    @Autowired
    private IAccountService accountService;
    @Autowired
    private IMemberService memberService;
    @Autowired
    private IRoleService roleService;
    @Autowired
    private IDatapermService datapermService;
    @Autowired
    private IMemberConfigService memberConfigService;
    @Autowired
    private IOrgInfoService orgInfoService;
    @Autowired
    private ISaleRegionService saleRegionService;
    @Autowired
    private IAccountRegionService accountRegionService;

    @Autowired
    private IAssignDriverLogService assignDriverLogService;

    private static final String BINDING_ACCOUNT_ID_SET = "binding_account_id_set";

    @Operation(summary = "主账号创建子账号")
    @PostMapping("/registerSubAccountByMaster")
    public ItemResult<AccountDTO> registerSubAccountByMaster(@Parameter(hidden = true)LoginInfo loginInfo,
                                                             @Parameter(name = "accountRegisterByMasterDTO", description = "主账号创建子账号DTO") @Valid @RequestBody AccountRegisterByMasterDTO accountRegisterByMasterDTO) {
        accountRegisterByMasterDTO.setOperatorId(loginInfo.getAccountId());
        accountRegisterByMasterDTO.setMemberId(loginInfo.getMemberId());
        // bug7244 标记注册平台
        accountRegisterByMasterDTO.setRegisterApp(AccountPlatformEnum.BUYER.getRegisterApp());
        return new ItemResult<>(accountService.registerSubAccountByMaster(accountRegisterByMasterDTO));
    }

    @Operation(summary = "更新子账账户信息")
    @PostMapping(value = "/updateSubAccountInfo")
    public ItemResult<Boolean> updateSubAccountInfo(@Parameter(hidden = true)LoginInfo loginInfo,
                                                    @Parameter(name = "subAccountUpdateDTO", description = "子账号更新DTO") @Valid @RequestBody SubAccountUpdateDTO subAccountUpdateDTO) {
        isCurrMemberSubAccount(loginInfo, subAccountUpdateDTO.getAccountId(), null);
        subAccountUpdateDTO.setMemberId(loginInfo.getMemberId());
        accountService.updateSubAccountInfo(subAccountUpdateDTO);
        return new ItemResult<>(true);
    }

    @Operation(summary = "主账号给其它账户重置密码")
    @PostMapping("/resetPassword")
    public ItemResult<Boolean> resetPassword(@Parameter(hidden = true)LoginInfo loginInfo,
                                             @Parameter(name = "accountName", description = "账号名称") String accountName,
                                             @Parameter(hidden = true)HttpServletRequest request) {
        isCurrMemberSubAccount(loginInfo, null, accountName);
        AccountSearchDTO accountSearchDTO = new AccountSearchDTO();
        accountSearchDTO.setMemberId(loginInfo.getMemberId());
        accountSearchDTO.setAccountName(accountName);
        accountSearchDTO.setPageSize(1);
        accountSearchDTO.setPageNumber(0);
        ItemResult<Boolean> itemResult = new ItemResult<>(true);
        List<AccountDTO> list = accountService.findAll(accountSearchDTO).getList();
        if (list != null && !list.isEmpty() && CsStringUtils.equals(list.get(0).getAccountName(), accountName)) {
            accountService.resetPassword(accountName, null, loginInfo.getAccountId(), IPUtils.getIpAddr(request));
            itemResult.setDescription("操作成功");
            return itemResult;
        } else {
            itemResult.setData(false);
            itemResult.setDescription("操作失败,账号不存在或者不是你的员工");
            return itemResult;
        }
    }

    @Operation(summary = "禁用账号")
    @PostMapping(value = "/disabled")
    public ItemResult<Boolean> disabled(@Parameter(hidden = true)LoginInfo loginInfo,
                                        @Parameter(name = "reason", description = "禁用原因") @RequestParam String reason,
                                        @Parameter(name = "accountId", description = "账号id") @RequestParam String accountId) {
        isCurrMemberSubAccount(loginInfo, accountId, null);
        checkLogin(loginInfo);
        accountService.disabled(accountId, reason, loginInfo.getAccountId());
        return new ItemResult<>(true);
    }

    @Operation(summary = "启用账号")
    @PostMapping(value = "/enabled")
    public ItemResult<Boolean> enabled(@Parameter(hidden = true)LoginInfo loginInfo,
                                       @Parameter(name = "accountId", description = "账号id") @RequestParam String accountId) {
        isCurrMemberSubAccount(loginInfo, accountId, null);
        checkLogin(loginInfo);
        accountService.enabled(accountId, loginInfo.getAccountId());
        return new ItemResult<>(true);
    }

    @Operation(summary = "设置为主账号")
    @PostMapping(value = "/change2PrimaryAccount")
    public ItemResult<Boolean> change2PrimaryAccount(@Parameter(hidden = true)LoginInfo loginInfo,
                                                     @Parameter(name = "changeAccountTypeDTO", description = "改变账号状态DTO") @Valid @RequestBody ChangeAccountTypeDTO changeAccountTypeDTO,
                                                     HttpServletRequest request) {
        checkLogin(loginInfo);
        if ((loginInfo.getAccountType() == null) || (loginInfo.getAccountType().intValue() != AccountDTO.ACCOUNT_TYPE_MEMBER_MASTER)) {
            throw new BizException(BasicCode.CUSTOM_ERROR, "只有主账户才有此权限");
        }
        isCurrMemberSubAccount(loginInfo, changeAccountTypeDTO.getAccountId(), null);

        changeAccountTypeDTO.setMemberId(loginInfo.getMemberId());
        accountService.changeAccountType(changeAccountTypeDTO);
        if (!CsStringUtils.equals(loginInfo.getAccountId(), changeAccountTypeDTO.getAccountId())) {
            loginInfo.setAccountType(AccountDTO.ACCOUNT_TYPE_MEMBER_SUB);
        }

        return new ItemResult<>(true);
    }

    @Operation(summary = "登出(退出登录)")
    @GetMapping("/loginOut")
    public ItemResult<Boolean> loginOut(@Parameter(hidden = true)LoginInfo loginInfo,
                                        @Parameter(hidden = true)HttpServletRequest request) throws ServletException {
        checkLogin(loginInfo);
        request.logout();
        return new ItemResult<>(true);
    }

    @Operation(summary = "获取登陆的当前用户信息(账号、绑定的账号、会员、组织机构、角色、权限、数据权限)")
    @GetMapping("/getCurrentUserInfo")
    public ItemResult<AccountDTO> getUserInfo(@Parameter(hidden = true)LoginInfo loginInfo,
                                              @Parameter(hidden = true)HttpServletRequest request) {
        checkLogin(loginInfo);
        AccountDTO accountDTO = accountService.findById(loginInfo.getAccountId());
        log.info("lucd===>最先获取accountDTO: {}", accountDTO);
        //组织机构
        accountDTO.setOrgInfo(orgInfoService.findOrgByAccountId(loginInfo.getAccountId()));
        //绑定的账号
        if (CsStringUtils.isNotBlank(accountDTO.getBindingId())) {
            List<AccountDTO> bindingAccountList = accountService.findBindingAccountById(loginInfo.getAccountId());
            if (bindingAccountList != null && !bindingAccountList.isEmpty()) {
                List<AccountBindingSampleDTO> bindingSampleDTOList = Lists.newArrayList();
                Set<String> bindingAccountIdSet = Sets.newHashSet();
                bindingAccountList.forEach(item -> {
                    bindingAccountIdSet.add(item.getAccountId());
                    AccountBindingSampleDTO absDTO = new AccountBindingSampleDTO();
                    BeanUtils.copyProperties(item, absDTO);
                    bindingSampleDTOList.add(absDTO);
                });
                accountDTO.setBindingAccountList(bindingSampleDTOList);
                request.getSession().setAttribute(BINDING_ACCOUNT_ID_SET, bindingAccountIdSet);
            }
        }

        MemberSimpleDTO memberSimpleDTO = memberService.findMemberSimpleById(accountDTO.getMemberId());
        if (memberSimpleDTO == null || memberSimpleDTO.getMemberId() == null) {
            log.error("对应会员信息没有找到，memberId: {}", accountDTO.getMemberId());
        } else {
            //会员信息
            accountDTO.setMemberSimpleDTO(memberSimpleDTO);
            accountDTO.setHasErp(loginInfo.getHasErp());
        }
        //角色 销售区域 数据权限
        try {
            List<com.ecommerce.member.api.dto.base.RoleDTO> roleList = new ArrayList<>();
            for (com.ecommerce.base.api.dto.role.RoleDTO roleDTO : roleService.getRoleByAccountId(accountDTO.getAccountId())) {
                com.ecommerce.member.api.dto.base.RoleDTO baseRole = new com.ecommerce.member.api.dto.base.RoleDTO();
                BeanUtils.copyProperties(roleDTO, baseRole);
                roleList.add(baseRole);
            }
            accountDTO.setRoleList(roleList);
            if (CollectionUtils.isNotEmpty(accountDTO.getRoleList())) {
                List<String> roleTypeList = accountDTO.getRoleList().stream().filter(
                                item -> CsStringUtils.isNotBlank(item.getRoleType())).map(com.ecommerce.member.api.dto.base.RoleDTO::getRoleType)
                        .distinct().toList();
                accountDTO.setRoleTypeList(roleTypeList);
                if (memberSimpleDTO != null) {
                    if (roleTypeList.stream().anyMatch(item -> CsStringUtils.equals(item, "buyer3"))) {
                        memberSimpleDTO.setBuyerFlg(1);
                    } else {
                        memberSimpleDTO.setBuyerFlg(0);
                    }
                }
            }
            accountDTO.setSaleRegionInfoIdList(loginInfo.getSaleRegionIdList());
            accountDTO.setRegionAdCodeList(loginInfo.getAccountRegionAdCodeList());
            accountDTO.setAccountStoreIdList(loginInfo.getAccountStoreIdList());
            if (loginInfo.getSaleRegionIdList() != null) {
                List<Map<String, String>> listMap = new ArrayList<>();
                //剔重
                Set<String> ids = Sets.newHashSet(loginInfo.getSaleRegionIdList());
                log.info("===saleRegionIdList===->{}===", JSON.toJSONString(loginInfo.getSaleRegionIdList()));
                for (String id : ids) {
                    SaleRegionSampleDTO saleRegionSampleDTO = saleRegionService.findSampleById(id);
                    Map<String, String> map = new HashMap<>();
                    if (saleRegionSampleDTO != null) {
                        map.put("saleRegionId", id);
                        map.put("saleRegionName", saleRegionSampleDTO.getSaleRegionName());
                    }
                    listMap.add(map);
                }
                accountDTO.setSaleRegionMapList(listMap);
            }
        } catch (Exception e) {
            log.error("获取权限、数据权限、销售区域信息出错：{},", e.getMessage(), e);
        }

        return new ItemResult<>(accountDTO);
    }

    @Operation(summary = "获取登陆的当前用户LoginInfo信息")
    @GetMapping("/getLoginInfo")
    public ItemResult<LoginInfo> getLoginInfo(@Parameter(hidden = true)LoginInfo loginInfo) {
        checkLogin(loginInfo);
        return new ItemResult<>(loginInfo);
    }

    @Operation(summary = "绑定用户切换")
    @GetMapping("/changeAccount")
    public ItemResult<LoginInfo> changeAccount(@Parameter(hidden = true)LoginInfo loginInfo,
                                               @Parameter(name = "accountId", description = "账号id") String accountId,
                                               @Parameter(hidden = true)HttpServletRequest request) {
        checkLogin(loginInfo);
        Object object = request.getSession().getAttribute(BINDING_ACCOUNT_ID_SET);
        if (object instanceof Set) {
            Set<String> bindingAccountIdSet = (Set<String>) object;
            if (bindingAccountIdSet.contains(accountId)) {
                AccountDTO accountDTO = accountService.findById(accountId);
                if (accountDTO == null || accountDTO.getAccountId() == null) {
                    throw new BizException(BasicCode.DATA_NOT_EXIST, "绑定的账号不存在");
                }
                return new ItemResult<>(getLoginInfo(accountDTO, request));
            } else {
                throw new BizException(BasicCode.DATA_NOT_EXIST, "绑定的账号id不存在");
            }
        } else {
            throw new BizException(BasicCode.DATA_NOT_EXIST, "没有找到绑定的账号");
        }
    }

    /**
     * 值集：
     * //        *********	平台自有
     * //        *********	平台签约
     * //        *********	社会运力
     * //        *********	平台门店
     * 返回值说明：
     * 0.其它
     * 1.个人司机
     * 2.社会承运商下的司机
     * 3.社会承运商的主账号
     * 4.平台门店承运商主账号
     * 5.平台门店承运商子账号
     * 6.平台自有运力承运商主账号
     * 7.平台自有运力承运商下的司机
     * 8.平台签约运力承运商主账号
     * 9.平台签约运力承运商下的司机
     *
     * @param loginInfo
     * @return
     */
    // TODO: 重构此方法以降低认知复杂度 (当前: 46, 目标: ≤15)
    // 建议: 1) 提取子方法 2) 使用早期返回 3) 简化条件表达式
    @Operation(summary = "获取用户的司机信息(0.其它 1.个人司机 2.社会承运商下的司机 3.社会承运商的主账号 4.平台门店承运商主账号 5.平台门店承运商子账号 6.平台自有运力承运商主账号 7.平台自有运力承运商下的司机 8.平台签约运力承运商主账号 9.平台签约运力承运商下的司机)")
    @GetMapping("/getCurrentUserDriverInfo")
    public ItemResult<Integer> getCurrentUserDriverInfo(@Parameter(hidden = true)LoginInfo loginInfo) {
        checkLogin(loginInfo);

        MemberDetailDTO memberDTO = memberService.findMemberById(loginInfo.getMemberId());
        AccountDTO accountDTO = accountService.findDetailById(loginInfo.getAccountId());
//            0.其它
        Integer result = 0;
        //承运商类型值集 CARRIER_TYPE *********  社会运力 角色id = 2 个人司机 角色id = 9 承运商司机
        //如果是社会运力的承运商
        if (memberDTO.getCarrierFlg() != null && memberDTO.getCarrierFlg().intValue() == 1 &&
                memberDTO.getCarrierType() != null && memberDTO.getCarrierType().contains("*********")) {
//            3.社会承运商的主账号
            if (CsStringUtils.equals(memberDTO.getMainAccountId(), loginInfo.getAccountId())) {
                result = 3;
            } else {
                //如果是子账号，又有司机角色
                //            2.社会承运商下的司机
                List<RoleDTO> roleDTOList = roleService.getRoleByAccountId(loginInfo.getAccountId());
                if (roleDTOList != null && roleDTOList.stream().filter(item -> item.getRoleId() != null && (item.getRoleId().intValue() == 9 || item.getRoleId().intValue() == 2)).count() > 0) {
                    result = 2;
                }
            }
            //如果是挂靠在平台的平台门店承运商下的门店司机
        } else if (memberDTO.getCarrierFlg() != null && memberDTO.getCarrierFlg().intValue() == 1 &&
                memberDTO.getCarrierType() != null && memberDTO.getCarrierType().contains("*********")) {
            //            4.平台门店承运商主账号
            if (CsStringUtils.equals(memberDTO.getMainAccountId(), loginInfo.getAccountId())) {
                result = 4;
            } else {
                //如果是子账号，又有司机角色
                //            5.平台门店承运商子账号
                List<RoleDTO> roleDTOList = roleService.getRoleByAccountId(loginInfo.getAccountId());
                if (roleDTOList != null && roleDTOList.stream().filter(item -> item.getRoleId() != null && (item.getRoleId().intValue() == 9 || item.getRoleId().intValue() == 2)).count() > 0) {
                    result = 5;
                }
            }
        } else if (memberDTO.getCarrierFlg() != null && memberDTO.getCarrierFlg().intValue() == 1 &&
                memberDTO.getCarrierType() != null && memberDTO.getCarrierType().contains("*********")) {
//            6.平台自有运力承运商主账号
            if (CsStringUtils.equals(memberDTO.getMainAccountId(), loginInfo.getAccountId())) {
                result = 6;
            } else {
                //如果是子账号，又有司机角色
//            7.平台自有运力承运商下的司机
                List<RoleDTO> roleDTOList = roleService.getRoleByAccountId(loginInfo.getAccountId());
                if (roleDTOList != null && roleDTOList.stream().filter(item -> item.getRoleId() != null && (item.getRoleId().intValue() == 9 || item.getRoleId().intValue() == 2)).count() > 0) {
                    result = 7;
                }
            }
        } else if (memberDTO.getCarrierFlg() != null && memberDTO.getCarrierFlg().intValue() == 1 &&
                memberDTO.getCarrierType() != null && memberDTO.getCarrierType().contains("*********")) {
//            8.平台签约运力承运商主账号
            if (CsStringUtils.equals(memberDTO.getMainAccountId(), loginInfo.getAccountId())) {
                result = 8;
            } else {
                //如果是子账号，又有司机角色
//            9.平台签约运力承运商下的司机
                List<RoleDTO> roleDTOList = roleService.getRoleByAccountId(loginInfo.getAccountId());
                if (roleDTOList != null && roleDTOList.stream().filter(item -> item.getRoleId() != null && (item.getRoleId().intValue() == 9 || item.getRoleId().intValue() == 2)).count() > 0) {
                    result = 9;
                }
            }
        } else if (accountDTO.getPersonDriver() != null && accountDTO.getPersonDriver().booleanValue()) {
//            1.个人司机
            result = 1;
        }
        return new ItemResult<>(result);
    }

    @Operation(summary = "修改自己的手机号key1:mobile,key2:smsCode")
    @PostMapping("/updateMobilePhone")
    public ItemResult<Boolean> updateMobilePhone(@Parameter(hidden = true)LoginInfo loginInfo,
                                                 @Parameter(name = "map", description = "映射") @RequestBody Map<String, String> map,
                                                 @Parameter(hidden = true)HttpServletRequest request) {
        checkLogin(loginInfo);
        String mobile = map.get("mobile");
        String smsCode = map.get("smsCode");
        checkMobile(mobile);
        accountService.checkSMSCode(new CheckSMSCodeDTO(GetSMSCodeDTO.USE_MODIFY_MOBILE, mobile, smsCode, request.getSession().getId()));
        //如果手机号相同，则不修改
        if (!CsStringUtils.equals(loginInfo.getMobile(), mobile)) {
            accountService.updateMobilePhone(loginInfo.getAccountId(), mobile, loginInfo.getAccountId(), IPUtils.getIpAddr(request));
        }else {
            return new ItemResult<>(BasicCode.CUSTOM_ERROR.getCode(),"手机号与原手机号一致");
        }
        return new ItemResult<>(true);
    }

    @Operation(summary = "修改自己的密码key1:password,key2:smsCode")
    @PostMapping("/updatePassword")
    public ItemResult<Object> updatePassword(@Parameter(hidden = true)LoginInfo loginInfo,
                                             @Parameter(name = "map", description = "映射") @RequestBody Map<String, String> map,
                                             @Parameter(hidden = true)HttpServletRequest request) {
        checkLogin(loginInfo);
        String password = map.get("password");
        String smsCode = map.get("smsCode");
        isBlank("新密码", password);
        accountService.checkSMSCode(new CheckSMSCodeDTO(GetSMSCodeDTO.USE_MODIFY_PASS_WORD, null, smsCode, request.getSession().getId()));
        accountService.updatePassword(loginInfo.getAccountId(), password, loginInfo.getAccountId(), IPUtils.getIpAddr(request));
        return new ItemResult<>(new Object());
    }

    @Operation(summary = "校验密码是否和现在的密码一样，提交修改前使用")
    @GetMapping("/checkPassword")
    public ItemResult<Boolean> checkPassword(@Parameter(hidden = true)LoginInfo loginInfo,
                                             @Parameter(name = "password", description = "密码") String password) {
        checkLogin(loginInfo);
        return new ItemResult<>(accountService.checkPassword(loginInfo.getAccountId(), password));
    }

    @Operation(summary = "更新DeviceToken")
    @PostMapping(value = "/updateDeviceToken")
    public ItemResult<Boolean> updateDeviceToken(@Parameter(hidden = true)LoginInfo loginInfo,
                                                 @Parameter(name = "deviceTokenDTO", description = "设备TokenDTO") @Valid @RequestBody DeviceTokenDTO deviceTokenDTO) {
        deviceTokenDTO.setAccountId(loginInfo.getAccountId());
        accountService.updateDeviceToken(deviceTokenDTO);
        return new ItemResult<>(true);
    }

    @Operation(summary = "修改头像URL{\"headUrl\":\"http://xxxx\"}")
    @PostMapping("/updateHeadUrl")
    public ItemResult<Object> updateHeadUrl(@Parameter(hidden = true)LoginInfo loginInfo,
                                            @Parameter(name = "map", description = "映射") @RequestBody Map<String, String> map) {
        checkLogin(loginInfo);
        String headUrl = map.getOrDefault("headUrl", null);
        isBlank("头像地址", headUrl);

        AccountBaseInfoUpdateDTO accountBaseInfoUpdateDTO = new AccountBaseInfoUpdateDTO();
        accountBaseInfoUpdateDTO.setHeadPic(headUrl);
        accountBaseInfoUpdateDTO.setAccountId(loginInfo.getAccountId());
        accountService.updateBaseInfo(accountBaseInfoUpdateDTO);
        return new ItemResult<>(new Object());
    }

    @Operation(summary = "修改性别")
    @GetMapping(value = "/updateSex")
    public ItemResult<Boolean> updateSex(@Parameter(hidden = true)LoginInfo loginInfo,
                                         @Parameter(name = "sex", description = "性别") String sex) {
        checkLogin(loginInfo);
        isBlank("性别", sex);

        int sexInt = 2;
        try {
            sexInt = Integer.parseInt(sex);
        } catch (Exception e) {
        }

        AccountBaseInfoUpdateDTO accountBaseInfoUpdateDTO = new AccountBaseInfoUpdateDTO();
        accountBaseInfoUpdateDTO.setSex(sexInt);
        accountBaseInfoUpdateDTO.setAccountId(loginInfo.getAccountId());
        accountService.updateBaseInfo(accountBaseInfoUpdateDTO);
        return new ItemResult<>(true);
    }

    @Operation(summary = "判断账户名是否存在，账户名唯一")
    @PostMapping("/checkAccountNameExists")
    public ItemResult<Boolean> checkAccountNameExists(@Parameter(hidden = true)LoginInfo loginInfo,
                                                      @Parameter(name = "newAccountName", description = "账户名") String newAccountName) {
        checkLogin(loginInfo);
        return new ItemResult<>(accountService.checkAccountNameExists(newAccountName));
    }

    @Operation(summary = "修改自己的账户名")
    @PostMapping("/updateAccountName")
    public ItemResult<Object> updateAccountName(@Parameter(hidden = true)LoginInfo loginInfo,
                                                @Parameter(name = "newAccountName", description = "账户名") String newAccountName,
                                                @Parameter(hidden = true)HttpServletRequest request) {
        checkLogin(loginInfo);
        accountService.updateAccountName(loginInfo.getAccountId(), newAccountName, loginInfo.getAccountId(), IPUtils.getIpAddr(request));
        return new ItemResult<>(new Object());
    }

    @Operation(summary = "判断手机号是否存在，手机号不唯一")
    @PostMapping("/checkMobilePhoneExists")
    public ItemResult<Boolean> checkMobilePhoneExists(@Parameter(hidden = true)LoginInfo loginInfo,
                                                      @Parameter(name = "newMobilePhone", description = "手机号") String newMobilePhone) {
        checkLogin(loginInfo);
        return new ItemResult<>(accountService.checkMobilePhoneExists(newMobilePhone));
    }

    @Operation(summary = "判断手机号是否注册过【检查主账号和个人账号，不检查子账号】账户，如果存在或则不合法则返回true")
    @PostMapping("/checkMobileByMasterAndPersonal")
    public ItemResult<Boolean> checkMobileByMasterAndPersonal(@Parameter(hidden = true)LoginInfo loginInfo,
                                                              @Parameter(name = "newMobilePhone", description = "手机号") String newMobilePhone) {
        ItemResult<Boolean> result = new ItemResult<>(null);
        if (CsStringUtils.isBlank(newMobilePhone) || !Pattern.matches(PHONE_NUMBER_REG, newMobilePhone)) {
            result.setSuccess(true);
            result.setData(true);
            result.setDescription("手机号不正确");
            return result;
        }
        boolean personlAccount = accountService.checkMobilePhoneExistsByPersonl(newMobilePhone);
        if (personlAccount) {
            result.setSuccess(true);
            result.setData(true);
            result.setDescription("个人账号中已经存在");
            return result;
        } else {
            boolean masterAccount = accountService.checkMobilePhoneExistsByMasterAccount(newMobilePhone);
            if (masterAccount) {
                result.setSuccess(true);
                result.setData(true);
                result.setDescription("主账号中已经存在");
                return result;
            }
        }
        result.setSuccess(true);
        //不存在
        result.setData(false);
        result.setDescription("主账号中已经存在");
        return result;
    }

    @Operation(summary = "获取修改密码时的手机验证码(已废弃)")
    @GetMapping("/getSMSCode")
    public ItemResult<Boolean> getSMSCodeOld(@Parameter(hidden = true)LoginInfo loginInfo,
                                             @Parameter(name = "mobile", description = "手机号") String mobile,
                                             HttpServletRequest request) {
        checkLogin(loginInfo);
        accountService.getSMSCode(new GetSMSCodeDTO(GetSMSCodeDTO.USE_DEFAULT, mobile, request.getSession().getId(), loginInfo.getAccountId()));
        return new ItemResult<>(true);
    }

    @Operation(summary = "获取修改密码时的手机验证码")
    @GetMapping("/getSMSCodeByModifyPassword")
    public ItemResult<Boolean> getSMSCodeByModifyPassword(@Parameter(hidden = true)LoginInfo loginInfo,
                                                          @Parameter(name = "mobile", description = "手机号") String mobile,
                                                          HttpServletRequest request) {
        checkLogin(loginInfo);
        accountService.getSMSCode(new GetSMSCodeDTO(GetSMSCodeDTO.USE_MODIFY_PASS_WORD, mobile, request.getSession().getId(), loginInfo.getAccountId()));
        return new ItemResult<>(true);
    }

    @Operation(summary = "获取修改手机时的手机验证码")
    @GetMapping("/getSMSCodeByModifyMobile")
    public ItemResult<Boolean> getSMSCodeByModifyMobile(@Parameter(hidden = true)LoginInfo loginInfo,
                                                        @Parameter(name = "mobile", description = "手机号") String mobile,
                                                        HttpServletRequest request) {
        checkLogin(loginInfo);
        accountService.getSMSCode(new GetSMSCodeDTO(GetSMSCodeDTO.USE_MODIFY_MOBILE, mobile, request.getSession().getId(), loginInfo.getAccountId()));
        return new ItemResult<>(true);
    }

    @Operation(summary = "更新账户基本信息")
    @PostMapping(value = "/updateBaseInfo")
    public ItemResult<Boolean> updateBaseInfo(@Parameter(hidden = true)LoginInfo loginInfo,
                                              @Parameter(name = "accountBaseInfoUpdateDTO", description = "账号基本信息更新DTO") @RequestBody AccountBaseInfoUpdateDTO accountBaseInfoUpdateDTO) {
        accountBaseInfoUpdateDTO.setAccountId(loginInfo.getAccountId());
        accountBaseInfoUpdateDTO.setOperator(loginInfo.getAccountId());
        accountService.updateBaseInfo(accountBaseInfoUpdateDTO);
        return new ItemResult<>(true);
    }

    @Operation(summary = "一个人有多个用户的情况，设置默认登陆账户")
    @GetMapping("/setDefaultMember/{accountId}")
    public ItemResult<Boolean> setDefaultMember(@Parameter(hidden = true)LoginInfo loginInfo,
                                                @Parameter(name = "accountId", description = "账号id") @PathVariable String accountId,
                                                @Parameter(hidden = true)HttpServletRequest request) {
        checkLogin(loginInfo);
        Object object = request.getSession().getAttribute(BINDING_ACCOUNT_ID_SET);
        if (object instanceof Set) {
            Set<String> bindingAccountIdSet = (Set<String>) object;
            if (bindingAccountIdSet.contains(accountId)) {
                accountService.setDefaultMember(accountId, loginInfo.getAccountId());
                return new ItemResult<>(true);
            }
        }
        throw new BizException(BasicCode.INVALID_PARAM, "accountId不正确");
    }

    @Operation(summary = "账号绑定")
    @PostMapping("/bindingAccount")
    public ItemResult<Boolean> bindingAccount(@Parameter(hidden = true)LoginInfo loginInfo,
                                              @Parameter(name = "accountBindingDTO", description = "账号绑定DTO") @Valid @RequestBody AccountBindingDTO accountBindingDTO) {
        checkLogin(loginInfo);
        accountBindingDTO.setCurrentAccountId(loginInfo.getAccountId());
        accountService.bindingAccount(accountBindingDTO);
        return new ItemResult<>(true);
    }

    @Operation(summary = "查询绑定的账号")
    @GetMapping("/findBindingAccount")
    public ItemResult<List<AccountDTO>> findBindingAccount(@Parameter(hidden = true)LoginInfo loginInfo) {
        checkLogin(loginInfo);
        return new ItemResult<>(accountService.findBindingAccountById(loginInfo.getAccountId()));
    }

    @Operation(summary = "账号解绑")
    @PostMapping("/unBindingAccount")
    public ItemResult<Boolean> unBindingAccount(@Parameter(hidden = true)LoginInfo loginInfo,
                                                @Parameter(name = "unBindingAccountId", description = "解绑账号id") String unBindingAccountId) {
        checkLogin(loginInfo);
        isBlank("要解绑的账户id", unBindingAccountId);
        accountService.unBindingAccount(loginInfo.getAccountId(), unBindingAccountId);
        return new ItemResult<>(true);
    }

    @Operation(summary = "当前账号绑定微信账号")
    @PostMapping(value = "/bindingWXAccount")
    public ItemResult<Boolean> bindingWXAccount(@Parameter(hidden = true)LoginInfo loginInfo,
                                                @Parameter(name = "accountWXBindingDTO", description = "账号微信绑定DTO") @Valid @RequestBody AccountWXBindingDTO accountWXBindingDTO) {
        checkLogin(loginInfo);
        accountWXBindingDTO.setBindingAccountName(loginInfo.getAccountName());
        accountService.weChatBinding(accountWXBindingDTO);
        return new ItemResult<>(true);
    }

    @Operation(summary = "当前用户解绑微信账号")
    @PostMapping(value = "/unBindingWXAccount")
    public ItemResult<Boolean> unBindingWXAccount(@Parameter(hidden = true)LoginInfo loginInfo,
                                                  @Parameter(hidden = true)HttpServletRequest request) {
        checkLogin(loginInfo);
        accountService.weChatUnBinding(loginInfo.getAccountId(), IPUtils.getIpAddr(request));
        return new ItemResult<>(true);
    }

    @Operation(summary = "根据会员id查询其下的子账号中的业务员")
    @PostMapping("/getSalesmanByMemberId")
    public ItemResult<List<AccountDTO>> getSalesmanByMemberId(@Parameter(name = "memberId", description = "会员id") String memberId) {
        List<AccountDTO> result = accountService.getSalesmanByMemberId(memberId);
        return new ItemResult<>(result);
    }

    @Operation(summary = "根据会员id查询其下的子账号中的销售经理")
    @GetMapping(value = "/findSalesManagerByMemberId")
    public ItemResult<List<AccountDTO>> findSalesManagerByMemberId(@Parameter(name = "memberId", description = "会员id") String memberId) {
        List<AccountDTO> result = accountService.findSalesManagerByMemberId(memberId);
        return new ItemResult<>(result);
    }

    @Operation(summary = "根据会员id查询其下的子账号中的销售助理")
    @GetMapping(value = "/findSalesAssistantByMemberId")
    public ItemResult<List<AccountDTO>> findSalesAssistantByMemberId(@Parameter(name = "memberId", description = "会员id") String memberId) {
        List<AccountDTO> result = accountService.findSalesAssistantByMemberId(memberId);
        return new ItemResult<>(result);
    }

    @Operation(summary = "根据会员id查询其下的仓库管理员")
    @GetMapping(value = "/findStorageAdminByMemberId")
    public ItemResult<List<AccountDTO>> findStorageAdminByMemberId(@Parameter(name = "memberId", description = "会员id") String memberId) {
        List<AccountDTO> result = accountService.findByMemberIdAndRoles(memberId, Lists.newArrayList(BaseRoleTypeEnum.STORAGE_ADMIN_SELLER.getId(), BaseRoleTypeEnum.STORAGE_ADMIN_PLATFORM.getId()));
        return new ItemResult<>(result);
    }

    @Operation(summary = "根据会员id查询其下的某角色的子账号{memberId:'',roleIds:[1,2,3]}")
    @PostMapping(value = "/findByMemberIdAndRoles")
    public ItemResult<List<AccountDTO>> findByMemberIdAndRoles(@Parameter(name = "map", description = "映射") @RequestBody Map<String, Object> map) {
        if (map == null) {
            throw new BizException(BasicCode.INVALID_PARAM, "参数不可为空");
        }
        Object memberId = map.getOrDefault("memberId", null);
        if (memberId == null || CsStringUtils.isBlank(memberId.toString())) {
            throw new BizException(BasicCode.INVALID_PARAM, "参数memberId不可为空");
        }
        Object roleIds = map.getOrDefault("roleIds", null);
        if (!(roleIds instanceof List) || ((List<Integer>) roleIds).isEmpty()) {
            throw new BizException(BasicCode.INVALID_PARAM, "参数roleIds不可为空,且必须是数组");
        }
        List<AccountDTO> result = accountService.findByMemberIdAndRoles(memberId.toString(), ((List<Integer>) roleIds));
        return new ItemResult<>(result);
    }

    @Operation(summary = "根据条件查询员工")
    @PostMapping("/list")
    public ItemResult<PageInfo<AccountDTO>> findAll(@Parameter(hidden = true)LoginInfo loginInfo,
                                                    @Parameter(name = "accountSearchDTO", description = "账号查询DTO") @Valid @RequestBody AccountSearchDTO accountSearchDTO) {
        accountSearchDTO.setMemberId(loginInfo.getMemberId());
        Integer pageSize = accountSearchDTO.getPageSize();
        Integer pageNum = accountSearchDTO.getPageNumber();
        accountSearchDTO.setPageSize(1000);
        accountSearchDTO.setPageNumber(1);
        // 查询该会员所有员工
        PageInfo<AccountDTO> page = accountService.findAll(accountSearchDTO);
        if (page != null && page.getList() != null && !page.getList().isEmpty()) {
            for (AccountDTO item : page.getList()) {
                item.setOrgInfo(orgInfoService.findOrgByAccountId(item.getAccountId()));
            }
        } else {
            return new ItemResult<>(page);
        }
        // 过滤结果：在该平台注册 || 角色属于该平台
        List<AccountDTO> accountDTOList = page.getList().stream()
                .filter(i -> AccountPlatformEnum.BUYER.getRegisterApp().equals(i.getRegisterApp()) ||
                        i.getRoleList().stream().anyMatch(roleDTO -> roleDTO.getRoleType().contains(AccountPlatformEnum.BUYER.getRoleType())))
                .toList();
        Page<AccountDTO> page2 = new Page<>(pageNum, pageSize);
        page2.setPages((int) Math.ceil((double) accountDTOList.size() / (double) pageSize));
        page2.setTotal(accountDTOList.size());
        page2.addAll(accountDTOList.subList(Math.max(pageSize * (pageNum - 1), 0), Math.min((pageSize * pageNum), accountDTOList.size())));
        return new ItemResult<>(new PageInfo<>(page2));
    }

    @Operation(summary = "根据id查询单个账户详细信息)")
    @PostMapping("/findDetailById")
    public ItemResult<AccountDTO> findDetailById(@Parameter(hidden = true)LoginInfo loginInfo,
                                                 @Parameter(name = "accountId", description = "账号id") @RequestParam String accountId) {
        checkLogin(loginInfo);
        return new ItemResult<>(accountService.findDetailById(accountId));
    }

    @Operation(summary = "获取公钥")
    @PostMapping("/getPublicKey")
    public ItemResult<String> getPublicKey(@Parameter(hidden = true)LoginInfo loginInfo) {
        checkLogin(loginInfo);
        String publicKey = accountService.getPublicKey();
        return new ItemResult<>(publicKey);
    }

    @Operation(summary = "根据账户id获取账户名")
    @GetMapping(value = "/getAccountName")
    public ItemResult<String> findAccountNameById(@Parameter(hidden = true)LoginInfo loginInfo,
                                                  @Parameter(name = "accountId", description = "账号id") String accountId) {
        checkLogin(loginInfo);
        return new ItemResult<>(accountService.findAccountNameById(accountId));
    }

    @Operation(summary = "根据账户id获取账户简要信息")
    @GetMapping(value = "/findAccountSimpleInfoById")
    public ItemResult<AccountSimpleDTO> findAccountSimpleInfoById(@Parameter(hidden = true)LoginInfo loginInfo,
                                                                  @Parameter(name = "accountId", description = "账号id") String accountId) {
        checkLogin(loginInfo);
        return new ItemResult<>(accountService.findSimpleById(accountId));
    }


    @Operation(summary = "模糊匹配名称（真实姓名，用户名）(仅限当前会员的员工)")
    @GetMapping(value = "/findByAccountnameOrRealname")
    public ItemResult<List<AccountDTO>> findByAccountnameOrRealname(@Parameter(hidden = true)LoginInfo loginInfo,
                                                                    @Parameter(name = "name", description = "名称") String name) {
        checkLogin(loginInfo);
        if (null == name || name.length() < 2) {
            return new ItemResult<>(Lists.newArrayList());
        }
        return new ItemResult<>(accountService.findByAccountnameOrRealnameAndMemberId(name, loginInfo.getMemberId()));
    }

    @Operation(summary = "根据id查询单个账户信息)(仅限当前会员的员工)")
    @PostMapping("/findByAccountName")
    public ItemResult<AccountDTO> findByAccountName(@Parameter(hidden = true)LoginInfo loginInfo,
                                                    @Parameter(name = "accountName", description = "账号名称") String accountName) {
        return new ItemResult<>(accountService.findByAccountNameAndMemberId(accountName, loginInfo.getMemberId()));
    }

    @Operation(summary = "根据手机号查询账户信息(仅限当前会员的员工)")
    @PostMapping("/findByMobilePhone")
    public ItemResult<List<AccountDTO>> findByMobilePhone(@Parameter(hidden = true)LoginInfo loginInfo,
                                                          @Parameter(name = "mobilePhoneNumber", description = "手机号") String mobilePhoneNumber) {
        return new ItemResult<>(accountService.findByMobilePhoneAndMemberId(mobilePhoneNumber, loginInfo.getMemberId()));
    }

    @Operation(summary = "根据手机号查询账户信息")
    @PostMapping("/findAllByMobilePhone")
    public ItemResult<List<AccountDTO>> findAllByMobilePhone(@Parameter(name = "mobilePhoneNumber", description = "手机号") String mobilePhoneNumber) {
        return new ItemResult<>(accountService.findByMobilePhone(mobilePhoneNumber));
    }

    @Operation(summary = "查询子账户详情(含账户、角色、权限、数据权限信息)")
    @GetMapping(value = "/findSubAccountDetail")
    public ItemResult<Map<String, Object>> findSubAccountDetail(@Parameter(hidden = true)LoginInfo loginInfo,
                                                                @Parameter(name = "accountId", description = "账号id") String accountId) {
        Map<String, Object> map = Maps.newHashMap();
        if (CsStringUtils.isBlank(accountId)) {
            throw new BizException(BasicCode.INVALID_PARAM, ":账户id不可为空");
        }
        AccountSimpleDTO accountSimpleDTO = accountService.findSimpleById(accountId);
        if (!CsStringUtils.equals(loginInfo.getMemberId(), accountSimpleDTO.getMemberId())) {
            throw new BizException(BasicCode.INVALID_PARAM, ":不可查看其它会员的账户信息");
        }
        map.put("accountInfo", accountSimpleDTO);

        AccountRoleDTO accountRoleDTO = new AccountRoleDTO();
        accountRoleDTO.setAccountId(accountId);
        accountRoleDTO.setMemberId(accountSimpleDTO.getMemberId());
        accountRoleDTO.setMasterFlg(accountSimpleDTO.getAccountType() != null && accountSimpleDTO.getAccountType().intValue() == AccountDTO.ACCOUNT_TYPE_MEMBER_MASTER);
        map.put("roleInfo", roleService.getRoleByAccountId2(accountRoleDTO));

        map.put("saleRegionInfo", saleRegionService.findByAccountId(accountId));
        map.put("regionInfo", accountRegionService.findShowInfoByAccountId(accountId));
        map.put("orgInfo", orgInfoService.findOrgByAccountId(accountId));

        return new ItemResult<>(map);
    }

    private void checkLogin(LoginInfo loginInfo) {
        if (loginInfo == null || CsStringUtils.isBlank(loginInfo.getAccountId())) {
            throw new BizException(MemberCode.LOGIN_ERROR, "你还没有登录，请先登录");
        }
    }

    /**
     * 宽松校验，可能以后会新增号段
     */
    private static final String PHONE_NUMBER_REG = "^(1[0-9][0-9])\\d{8}$";

    private void checkMobile(String mobile) {
        if (CsStringUtils.isBlank(mobile) || !Pattern.matches(PHONE_NUMBER_REG, mobile)) {
            throw new BizException(MemberCode.VALIDATION_ERROR, "手机号不正确");
        }
    }

    private void isBlank(String paramName, String paramValue) {
        if (CsStringUtils.isBlank(paramValue)) {
            throw new BizException(BasicCode.INVALID_PARAM, paramName + "不可为空");
        }
    }

    /**
     * 判断是否当前会员的子账号
     */
    private void isCurrMemberSubAccount(LoginInfo loginInfo, String accountId, String accountName) {
        if (CsStringUtils.isBlank(accountId) && CsStringUtils.isBlank(accountName)) {
            throw new BizException(BasicCode.CUSTOM_ERROR, "账号id或账号名不可为空");
        }
        AccountDTO accountDTO = CsStringUtils.isBlank(accountName) ? accountService.findById(accountId) : accountService.findByAccountName(accountName);

        if (!CsStringUtils.equals(accountDTO.getMemberId(), loginInfo.getMemberId())) {
            throw new BizException(BasicCode.INVALID_PARAM, "该账户不是你的子账户");
        }
    }

    private LoginInfo getLoginInfo(AccountDTO accountDTO, HttpServletRequest request) {

        LoginInfo loginInfo = new LoginInfo(accountDTO);
        loginInfo.setSessionId(request.getSession().getId());

        //默认buyerFlg为0
        loginInfo.setBuyerFlg(0);

        //默认为个人买家
        loginInfo.setRoleList(Lists.newArrayList(1));
        //一下两次查询可优化为并行处理
        MemberSimpleDTO memberSimpleDTO = memberService.findMemberSimpleById(accountDTO.getMemberId());
        boolean hasErp = false;
        if (memberSimpleDTO != null) {
            loginInfo.setSellerFlg(memberSimpleDTO.getSellerFlg());
            loginInfo.setCarrierFlg(memberSimpleDTO.getCarrierFlg());
            loginInfo.setSupplierFlg(memberSimpleDTO.getSupplierFlg());

            loginInfo.setSellerType(memberSimpleDTO.getSellerType());
            loginInfo.setCarrierType(memberSimpleDTO.getCarrierType());
            loginInfo.setSupplierType(memberSimpleDTO.getSupplierType());
            loginInfo.setBuyerType(memberSimpleDTO.getBuyerType());
            loginInfo.setMemberShortName(memberSimpleDTO.getMemberShortName());
            if (null != memberSimpleDTO && memberSimpleDTO.getSellerFlg() != null && memberSimpleDTO.getSellerFlg() == 1) {
                MemberConfigDTO memberConfigDTO = memberConfigService.findByMemberIdAndKeyCode(accountDTO.getMemberId(), MemberConfigEnum.ERP_JOIN_FLG.getKeyCode());
                //如果配置存在，且状态正常 且值等于u或者true
                hasErp = memberConfigDTO != null
                        && (memberConfigDTO.getStatus() == null || !memberConfigDTO.getStatus())
                        && ("1".equals(memberConfigDTO.getValue()) || "true".equals(memberConfigDTO.getValue()));
                loginInfo.setHasErp(hasErp);
            }
        }

        loginInfo.setHasErp(hasErp);
        //角色 销售区域 数据权限
        try {
            List<RoleDTO> accountRole = roleService.getRoleByAccountId(accountDTO.getAccountId());
            if (CollectionKit.isNotEmpty(accountRole)) {
                loginInfo.setRoleList(accountRole.stream().map(RoleDTO::getRoleId).toList());
                loginInfo.setRoleTypeList(accountRole.stream().map(RoleDTO::getRoleType).distinct().toList());
                if (CollUtil.isNotEmpty(accountRole)) {
                    for (RoleDTO roleDTO : accountRole) {
                        if ("buyer3".equals(roleDTO.getRoleType())) {
                            loginInfo.setBuyerFlg(1);
                            break;
                        }
                    }
                }
                ItemResult<DataPermDTO> dataPermDTOItemResult = datapermService.findDataByAccountId(accountDTO.getAccountId());
                if (dataPermDTOItemResult != null && dataPermDTOItemResult.getData() != null) {
                    loginInfo.setSaleRegionIdList(dataPermDTOItemResult.getData().getSaleRegionPerms());
                    loginInfo.setAccountRegionAdCodeList(dataPermDTOItemResult.getData().getStandardRegionPerms());
                }
            }
        } catch (Exception e) {
            log.error("获取权限、数据权限、销售区域信息出错：{},", e.getMessage(), e);
        }

        //保存session  Spring HttpSession 自动保存session到redis
        request.getSession().setAttribute(LoginInfo.SESSION_NAME, loginInfo);

        log.info("sessionId : {}", loginInfo.getSessionId());

        return loginInfo;
    }

    @Operation(summary = "查询买家司机（拥有buyer_driver角色的人）")
    @GetMapping("/findBuyerDriver")
    public ItemResult<List<AccountDTO>> findBuyerDriver(
            @Parameter(hidden = true)LoginInfo loginInfo,
            @Parameter(name = "needMonitor", description = "是否需要监控") @RequestParam Integer needMonitor) {
        //前提条件: 同一个买家的司机不会出现重名的司机
        String buyerId = loginInfo.getMemberId();
        List<AccountDTO> accountDTOList = Lists.newArrayList();

        LinkedHashMap<String, AccountDTO> name2DriverMap = new LinkedHashMap<>();

        AssignDriverLogCondDTO assignDriverLogCondDTO = new AssignDriverLogCondDTO();
        assignDriverLogCondDTO.setUserId(buyerId);
        assignDriverLogCondDTO.setUserType(UserRoleEnum.BUYER.getCode());
        assignDriverLogCondDTO.setMustDriverId(needMonitor);

        ItemResult<List<AssignDriverLogResDTO>> assignDriverListResult = assignDriverLogService.queryByCond(assignDriverLogCondDTO);
        if (assignDriverListResult != null && CollectionUtils.isNotEmpty(assignDriverListResult.getData())) {
            //指派司机有历史记录
            for (AssignDriverLogResDTO resDTO : assignDriverListResult.getData()) {
                //如果是管控，指派日志里有司机id的司机不添加进去
                if (CommonUtils.isSame(needMonitor, 1) && CsStringUtils.isNotBlank(resDTO.getDriverId())) {
                    break;
                }
                AccountDTO accountDTO = new AccountDTO();
                accountDTO.setAccountId(resDTO.getDriverId());
                accountDTO.setRealName(resDTO.getDriverName());
                accountDTO.setMobile(resDTO.getDriverPhone());
                name2DriverMap.putIfAbsent(resDTO.getDriverName(), accountDTO);
            }
        }

        RoleDTO roleDTO = roleService.findByRoleName("buyer_driver");
        if (roleDTO == null) {
            accountDTOList.addAll(name2DriverMap.values());
            return new ItemResult<>(accountDTOList);
        }
        List<AccountDTO> driverAccounts = accountService.findByMemberIdAndRoles(buyerId, Lists.newArrayList(roleDTO.getRoleId()));

        if (CollectionUtils.isNotEmpty(driverAccounts)) {
            for (AccountDTO driver : driverAccounts) {
                name2DriverMap.put(driver.getRealName(), driver);
            }
        }

        accountDTOList.addAll(name2DriverMap.values());
        return new ItemResult<>(accountDTOList);
    }

}
