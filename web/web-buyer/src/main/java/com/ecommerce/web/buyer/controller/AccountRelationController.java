package com.ecommerce.web.buyer.controller;

import com.ecommerce.base.api.dto.accountregion.AccountRegionShowDTO;
import com.ecommerce.base.api.dto.accountregion.AccountRegionUpdateDTO;
import com.ecommerce.base.api.service.IAccountRegionService;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.exception.BusinessOperationException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.member.api.dto.account.AccountSimpleDTO;
import com.ecommerce.member.api.service.IAccountService;
import com.ecommerce.member.api.session.LoginInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 人员-行政区域关系（人员行政区域关系）
 *
 * <AUTHOR>
 */
@Slf4j
@Tag(name = "AccountRelationController", description = "人员行政区域关系")
@RestController
@RequestMapping("/accountRelation")
public class AccountRelationController {

    @Autowired
    private IAccountRegionService iAccountRegionService;
    @Autowired
    private IAccountService accountService;

    @Operation(summary = "通过账号id查询行政区域编码")
    @PostMapping(value = "/findAdCodeByAccountId")
    public ItemResult<List<AccountRegionShowDTO>> findAdCodeByAccountId(@Parameter(name = "accountId", description = "账号id") @RequestParam String accountId) {
        return new ItemResult<>(iAccountRegionService.findShowInfoByAccountId(accountId));
    }

    @Operation(summary = "更新单个账号行政区域关系")
    @PostMapping(value = "/updateAccountRegion")
    public ItemResult<Boolean> updateAccountRegion(@Parameter(hidden = true)LoginInfo loginInfo,
                                                   @Parameter(name = "dto", description = "账号行政区域更新DTO") @RequestBody AccountRegionUpdateDTO dto) {
        dto.setOperatorId(loginInfo.getAccountId());
        if (CsStringUtils.isBlank(dto.getAccountId())) {
            throw new BizException(BasicCode.INVALID_PARAM, ":账户id不可为空");
        }
        AccountSimpleDTO accountSimpleDTO = accountService.findSimpleById(dto.getAccountId());
        if (accountSimpleDTO == null) {
            throw new BizException(BasicCode.INVALID_PARAM, ":账户id不正确");
        }
        if (!CsStringUtils.equals(loginInfo.getMemberId(), accountSimpleDTO.getMemberId())) {
            throw new BizException(BasicCode.INVALID_PARAM, ":账户id不正确,不可修改其它会员的账户");
        }
        iAccountRegionService.updateAccountRegion(dto);
        return new ItemResult<>(true);
    }
}
