package com.ecommerce.web.buyer.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.trace.api.dto.waybill.ArrWarehouseInfoDTO;
import com.ecommerce.trace.api.service.ITraceArrWarehouseService;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


/**
 * 进站记录明细查询
 *

 */
@RestController
@Tag(name = "ArrWarehouseController", description = "进站记录明细查询")
@RequestMapping("/arrWarehouse")
public class ArrWarehouseController {

    @Autowired
    private ITraceArrWarehouseService traceArrWarehouseService;

    @PostMapping(value = "/queryArrWarehouseDetail")
    public ItemResult<ArrWarehouseInfoDTO> queryArrWarehouseDetail(@Parameter(name = "waybillId", description = "运单id") @RequestParam String waybillId) {
        return traceArrWarehouseService.queryArrWarehouseDetail(waybillId);
    }

}
