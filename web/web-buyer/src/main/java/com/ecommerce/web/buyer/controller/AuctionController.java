package com.ecommerce.web.buyer.controller;


import com.ecommerce.common.result.ItemResult;
import com.ecommerce.member.api.session.LoginInfo;
import com.ecommerce.order.api.dto.auction.AuctionDetailForBidDTO;
import com.ecommerce.order.api.dto.auction.AuctionListBuyerResponseDTO;
import com.ecommerce.order.api.dto.auction.AuctionSessionRequestDTO;
import com.ecommerce.order.api.dto.auction.RegisterAuctionRequestDTO;
import com.ecommerce.order.api.service.IAuctionService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.math.BigDecimal;

/**
 * @projectName: ecome-backend
 * @package: com.ecommerce.web.buyer.controller
 * @className: BidController
 * @author: gaoqig
 * @description: 竞价/竞量 Controller
 * @date: 2024/12/4 19:22
 * @version: 1.0
 */

@Api(tags = {"AuctionController"})
@RestController
@RequestMapping("/auction")
public class AuctionController {
    private final IAuctionService auctionService;

    public AuctionController(IAuctionService auctionService) {
        this.auctionService = auctionService;
    }

    /***
     * <AUTHOR>
     * @description 1.获取竞价/竞量列表
     * @date 2024/12/5 09:47
     * @param param:
     * @return: com.ecommerce.common.result.ItemResult<com.github.pagehelper.PageInfo < com.ecommerce.order.api.dto.auction.AuctionListBuyerResponseDTO>>
     */

    @PostMapping(value = "/getAuctionList")
    public ItemResult<PageInfo<AuctionListBuyerResponseDTO>> getAuctionList(@ApiIgnore LoginInfo loginInfo,@RequestBody AuctionSessionRequestDTO param) {
        param.setBuyerId(loginInfo.getAccountId());
        return auctionService.getAuctionList(param);
    }

    /***
     * <AUTHOR>
     * @description 8.获取竞价/竞量详情
     * @date 2024/12/5 09:48
     * @param auctionNo:
     * @return: com.ecommerce.common.result.ItemResult<com.ecommerce.order.api.dto.auction.AuctionDetailResponseDTO>
     */

    @GetMapping(value = "/getAuctionDetail")
    public ItemResult<AuctionDetailForBidDTO> getAuctionDetail(@ApiIgnore LoginInfo loginInfo, @RequestParam("auctionNo") String auctionNo) {
        return auctionService.getAuctionDetailForBid(auctionNo, loginInfo.getAccountId());
    }

    /***
     * <AUTHOR>
     * @description 9.报名竞价/竟量
     * @date 2024/12/5 09:48
     * @param param:
     * @return: com.ecommerce.common.result.ItemResult<java.lang.String>
     */

    @PostMapping(value = "/registerAuction")
    public ItemResult<String> registerAuction(@ApiIgnore LoginInfo loginInfo, @RequestBody RegisterAuctionRequestDTO param) {
        return auctionService.registerAuction(loginInfo.getAccountId(), loginInfo.getAccountName(), param);
    }


    /***
     * <AUTHOR>
     * @description 10.出价
     * @date 2024/12/5 11:53
     * @param auctionNo:
     * @return: com.ecommerce.common.result.ItemResult<java.lang.String>
     */

    @PostMapping(value = "bidAuction")
    public ItemResult<String> bidAuction(@ApiIgnore LoginInfo loginInfo, @RequestParam("auctionNo") String auctionNo, @RequestParam("bidCount") BigDecimal bidCount) {
        return auctionService.bidAuction(loginInfo.getAccountId(), loginInfo.getAccountName(), auctionNo, bidCount);
    }
}
