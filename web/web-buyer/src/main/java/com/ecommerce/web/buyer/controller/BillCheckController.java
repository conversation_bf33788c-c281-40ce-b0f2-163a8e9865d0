package com.ecommerce.web.buyer.controller;

import com.ecommerce.common.enums.AppNames;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.information.api.dto.bill.check.BillCheckApprovalDTO;
import com.ecommerce.information.api.dto.bill.check.BillCheckExportDTO;
import com.ecommerce.information.api.dto.bill.check.BillCheckGoodsInfoQueryDTO;
import com.ecommerce.information.api.dto.bill.check.BillCheckInfoDTO;
import com.ecommerce.information.api.dto.bill.check.BillCheckInfoTabDTO;
import com.ecommerce.information.api.dto.bill.check.BillCheckQueryDTO;
import com.ecommerce.information.api.dto.bill.check.BillCheckWaybillInfoDTO;
import com.ecommerce.information.api.dto.bill.check.BillCheckWaybillInfoQueryDTO;
import com.ecommerce.information.api.dto.bill.check.KeyValueDTO;
import com.ecommerce.information.api.dto.bill.check.KeyValueQueryDTO;
import com.ecommerce.information.api.dto.bill.check.enums.BillCheckStatusEnum;
import com.ecommerce.information.api.dto.bill.check.rule.PageQuery;
import com.ecommerce.information.api.service.IBillCheckService;
import com.ecommerce.member.api.session.LoginInfo;
import com.ecommerce.web.buyer.utils.BillCheckExcelUtil;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import feign.Response;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.InputStreamSource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;


@Slf4j
@Tag(name = "billCheck", description = "对账单服务")
@RestController
@RequestMapping("/billCheck")
public class BillCheckController {

    @Autowired
    private IBillCheckService billCheckService;

    @Operation(summary = "审批(确认)")
    @PostMapping(value="/approve")
    public ItemResult<Boolean> approve(@Parameter(hidden = true)LoginInfo loginInfo, @RequestBody BillCheckApprovalDTO dto){
        dto.setAppName(AppNames.WEB_SERVICE_BUYER.getPlatform());
        dto.setOperator(loginInfo.getAccountId());
        dto.setOperatorName(loginInfo.getAccountName());
        dto.setOperatorMemberId(loginInfo.getMemberId());
        dto.setOperatorRoleNameSet(Sets.newHashSet(loginInfo.getRoleNameList()));
        return new ItemResult<>(billCheckService.approve(dto));
    }

    @Operation(summary = "对账单翻页查询")
    @PostMapping(value="/findAll")
    public ItemResult<PageInfo<BillCheckInfoDTO>> findAll(@Parameter(hidden = true)LoginInfo loginInfo, @RequestBody PageQuery<BillCheckQueryDTO> query){
        query.setQueryDTO(query.getQueryDTO() == null ? new BillCheckQueryDTO() : query.getQueryDTO());
        query.getQueryDTO().setAppName(AppNames.WEB_SERVICE_BUYER.getPlatform());
        query.getQueryDTO().setOperatorMemberId(loginInfo.getMemberId());
        return new ItemResult<>(billCheckService.findAll(query));
    }

    @Operation(summary = "对账单列表tab数据查询")
    @PostMapping(value="/tabCountByQuery")
    public ItemResult<List<BillCheckInfoTabDTO>> tabCountByQuery(@Parameter(hidden = true)LoginInfo loginInfo, @RequestBody BillCheckQueryDTO query){
        query = query == null ? new BillCheckQueryDTO() : query;
        query.setAppName(AppNames.WEB_SERVICE_BUYER.getPlatform());
        query.setOperatorMemberId(loginInfo.getMemberId());
        List<BillCheckInfoTabDTO> tabList = billCheckService.tabCountByQuery(query);
        Set<String> set = Sets.newHashSet("all",
                BillCheckStatusEnum.WAIT_CUSTOMER_CONFIRM.getCode(),
                BillCheckStatusEnum.COMPLETED.getCode());
        tabList = tabList.stream().filter(item->set.contains(item.getTabId())).toList();
        return new ItemResult<>(tabList);
    }

    @Operation(summary = "运单翻页查询")
    @PostMapping(value="/pageWaybillInfo")
    public ItemResult<PageInfo<BillCheckWaybillInfoDTO>> pageWaybillInfo(@RequestBody PageQuery<BillCheckWaybillInfoQueryDTO> query){
        return new ItemResult<>(billCheckService.pageWaybillInfo(query));
    }

    @Operation(summary = "运单明细excel导出")
    @PostMapping(value="/waybillInfoExcelExport")
    public void waybillInfoExcelExport(@Parameter(hidden = true)LoginInfo loginInfo, @RequestBody PageQuery<BillCheckWaybillInfoQueryDTO> query, HttpServletResponse response){
        query.getQueryDTO().setOperatorMemberId(loginInfo.getMemberId());
        BillCheckExcelUtil.waybillInfoExcelExport(billCheckService,query,response);
    }

    @Operation(summary = "商品汇总信息excel导出")
    @PostMapping(value="/goodsInfoExcelExport")
    public void goodsInfoExcelExport(@Parameter(hidden = true)LoginInfo loginInfo,@RequestBody PageQuery<BillCheckGoodsInfoQueryDTO> query, HttpServletResponse response){
        query.getQueryDTO().setOperatorMemberId(loginInfo.getMemberId());
        BillCheckExcelUtil.goodsInfoExcelExport(billCheckService,query,response);
    }

    @Operation(summary = "物流对账单excel导出")
    @PostMapping(value="/logisticsBillCheckInfoExport")
    public ResponseEntity logisticsBillCheckInfoExport(@Parameter(hidden = true)LoginInfo loginInfo, @RequestParam String billCheckId){
        try{
            BillCheckExportDTO dto = new BillCheckExportDTO();
            dto.setOperatorMemberId(loginInfo.getMemberId());
            dto.setOperatorId(loginInfo.getAccountId());
            dto.setBillCheckId(billCheckId);
            Response response1 = billCheckService.logisticsBillCheckExport(dto);
            if( response1 == null || response1.body() == null ){
                return null;
            }
            Map<String, Collection<String>> headers = response1.headers();

            HttpHeaders httpHeaders = new HttpHeaders();
            if( headers!= null && !headers.isEmpty()) {
                headers.forEach((k, v) -> {
                    List<String> values = Lists.newLinkedList();
                    values.addAll(v);
                    httpHeaders.put(k, values);
                });
            }
            Response.Body body = response1.body();
            InputStream inputStream = body.asInputStream();
            InputStreamSource resource = new InputStreamResource(inputStream);
            return ResponseEntity
                    .ok()
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .headers(httpHeaders)
                    .body(resource);
        }catch (Exception e){
            if( e instanceof BizException exception){
                throw exception;
            }
            log.error(e.getMessage(),e);
            throw new BizException(BasicCode.CUSTOM_ERROR,"运单下载出错");
        }
    }

    @Operation(summary = "根据id查询(返回运单以外的对账单所有信息)")
    @GetMapping(value="/findById")
    public ItemResult<BillCheckInfoDTO> findById(@Parameter(hidden = true)LoginInfo loginInfo,@RequestParam String billCheckId){
        BillCheckInfoDTO billCheckInfoDTO = billCheckService.findById(billCheckId);
        if (billCheckInfoDTO == null || (!CsStringUtils.equals(loginInfo.getMemberId(), billCheckInfoDTO.getPayeeMemberId()) &&
                !CsStringUtils.equals(loginInfo.getMemberId(), billCheckInfoDTO.getPayerMemberId()))) {
            return new ItemResult<>(null);
        }
        return new ItemResult<>(billCheckInfoDTO);
    }

    @Operation(summary = "根据条件查询下拉数据")
    @PostMapping(value="/findDropDownData")
    public ItemResult<List<KeyValueDTO>> findDropDownData(@Parameter(hidden = true)LoginInfo loginInfo,@RequestBody KeyValueQueryDTO query){
        query.setOperator(loginInfo.getAccountId());
        query.setOperatorMemberId(loginInfo.getMemberId());
        query.setAppName(AppNames.WEB_SERVICE_BUYER.getPlatform());
        return new ItemResult<>(billCheckService.findDropDownData(query));
    }
}
