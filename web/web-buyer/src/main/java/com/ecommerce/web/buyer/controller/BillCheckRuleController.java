package com.ecommerce.web.buyer.controller;

import com.ecommerce.base.api.enums.UserRoleEnum;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.information.api.dto.bill.check.rule.BillCheckRuleDTO;
import com.ecommerce.information.api.dto.bill.check.rule.BillCheckRuleQueryDTO;
import com.ecommerce.information.api.dto.bill.check.rule.MemberIdAndNameDTO;
import com.ecommerce.information.api.dto.bill.check.rule.MemberIdAndNameQueryDTO;
import com.ecommerce.information.api.dto.bill.check.rule.PageQuery;
import com.ecommerce.information.api.service.IBillCheckRuleService;
import com.ecommerce.member.api.session.LoginInfo;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@Slf4j
@Tag(name = "BillCheckRuleController", description = "对账单规则服务")
@RestController
@RequestMapping("/billCheckRule")
public class BillCheckRuleController {

    @Autowired
    private IBillCheckRuleService billCheckRuleService;

    @Operation(summary = "翻页查询")
    @PostMapping(value = "/findAll")
    public ItemResult<PageInfo<BillCheckRuleDTO>> findAll(@Parameter(hidden = true)LoginInfo loginInfo,
                                                          @RequestBody PageQuery<BillCheckRuleQueryDTO> pageQuery) {
        BillCheckRuleQueryDTO queryDTO = pageQuery.getQueryDTO() == null ? new BillCheckRuleQueryDTO() : pageQuery.getQueryDTO();
        queryDTO.setQueryMemberId(loginInfo.getMemberId());
        queryDTO.setQueryMemberType(UserRoleEnum.BUYER.getCode());
        pageQuery.setQueryDTO(queryDTO);
        return new ItemResult<>(billCheckRuleService.findAll(pageQuery));
    }

    @Operation(summary = "根据id查询")
    @PostMapping(value = "/findById")
    public ItemResult<BillCheckRuleDTO> findById(@RequestParam String billCheckRuleId) {
        return new ItemResult<>(billCheckRuleService.findById(billCheckRuleId));
    }

    @Operation(summary = "查询会员下拉列表")
    @PostMapping(value = "/findMemberName")
    public ItemResult<List<MemberIdAndNameDTO>> findMemberName(@Parameter(hidden = true)LoginInfo loginInfo,
                                                               @RequestBody MemberIdAndNameQueryDTO queryDTO) {
        queryDTO.setQueryMemberId(loginInfo.getMemberId());
        queryDTO.setQueryMemberType(UserRoleEnum.BUYER.getCode());
        return new ItemResult<>(billCheckRuleService.findMemberName(queryDTO));
    }
}
