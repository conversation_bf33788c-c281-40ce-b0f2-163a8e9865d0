package com.ecommerce.web.buyer.controller;

import com.alibaba.fastjson.JSON;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.exception.BusinessOperationException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.member.api.dto.referrer.CreateRelationDTO;
import com.ecommerce.member.api.dto.referrer.ReferrerBuyerRelationDTO;
import com.ecommerce.member.api.dto.referrer.ReferrerInfoDTO;
import com.ecommerce.member.api.dto.referrer.ReferrerInfoQueryDTO;
import com.ecommerce.member.api.dto.referrer.RelationQueryDTO;
import com.ecommerce.member.api.service.IBuyerAndReferrerService;
import com.ecommerce.member.api.session.LoginInfo;
import com.ecommerce.web.buyer.utils.QrCodeUtils;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.ImmutableMap;
import com.google.zxing.WriterException;
import io.swagger.v3.oas.annotations.Operation;
import java.io.IOException;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.imageio.ImageIO;
import java.io.PrintWriter;
import java.util.List;


@Controller
@RequestMapping("/buyerAndReferrer")
@Tag(name = "BuyerAndReferrerController", description = "推荐人&账号关系服务(促销活动使用)")
public class BuyerAndReferrerController {

    @Autowired
    private IBuyerAndReferrerService buyerAndReferrerService;

    @Operation(summary = "扫描二维码创建买家与推荐人的关系，已创建/覆盖关系")
    @PostMapping(value = "/createRelation")
    @ResponseBody
    public ItemResult<Boolean> createRelation(@Parameter(hidden = true) LoginInfo loginInfo,
                                              @Parameter(name = "referrerId", description = "推荐人id") String referrerId) {
        if (CsStringUtils.isBlank(referrerId)) {
            throw new BizException(BasicCode.INVALID_PARAM, "推荐人二维码不正确");
        }
        if (loginInfo == null || CsStringUtils.isBlank(loginInfo.getAccountId())) {
            throw new BizException(BasicCode.INVALID_PARAM, "请先登录");
        }

        CreateRelationDTO createRelationDTO = new CreateRelationDTO();
        createRelationDTO.setBuyerAccountId(loginInfo.getAccountId());
        createRelationDTO.setBuyerMemberId(loginInfo.getMemberId());
        createRelationDTO.setReferrerId(referrerId);

        buyerAndReferrerService.createRelation(createRelationDTO);
        return new ItemResult<>(true);
    }

    @Operation(summary = "查询所有推荐人")
    @PostMapping(value = "/findAllReferrerInfo")
    @ResponseBody
    public ItemResult<List<ReferrerInfoDTO>> findAllReferrerInfo() {
        return new ItemResult<>(buyerAndReferrerService.findAllReferrerInfo());
    }


    @Operation(summary = "根据推下单人账户id查询推荐人(下单时使用)")
    @PostMapping(value = "/findByBuyerAccountId")
    @ResponseBody
    public ItemResult<ReferrerBuyerRelationDTO> findByBuyerAccountId(@Parameter(name = "buyerAccountId", description = "买家账号id") @RequestParam String buyerAccountId) {
        return new ItemResult<>(buyerAndReferrerService.findByBuyerAccountId(buyerAccountId));
    }


    @Operation(summary = "根据条件查询关系信息")
    @PostMapping(value = "/findAllRelation")
    @ResponseBody
    public ItemResult<PageInfo<ReferrerBuyerRelationDTO>> findAllRelation(@Parameter(name = "relationQueryDTO", description = "关系信息查询DTO") @RequestBody RelationQueryDTO relationQueryDTO) {
        return new ItemResult<>(buyerAndReferrerService.findAllRelation(relationQueryDTO));
    }


    @Operation(summary = "根据推荐人信息表id查询推荐人（物流使用）")
    @PostMapping(value = "/findById")
    @ResponseBody
    public ItemResult<ReferrerInfoDTO> findById(@Parameter(name = "id", description = "推荐人信息表id") @RequestParam String id) {
        return new ItemResult<>(buyerAndReferrerService.findById(id));
    }


    @Operation(summary = "根据id查询")
    @PostMapping(value = "/findByIds")
    @ResponseBody
    public ItemResult<List<ReferrerInfoDTO>> findByIds(@Parameter(name = "ids", description = "id列表") @RequestBody List<String> ids) {
        return new ItemResult<>(buyerAndReferrerService.findByIds(ids));
    }

    @Operation(summary = "根据条件查询推荐人")
    @PostMapping(value = "/findAllReferrerInfoByCondition")
    @ResponseBody
    public ItemResult<PageInfo<ReferrerInfoDTO>> findAllReferrerInfoByCondition(@Parameter(name = "referrerInfoQueryDTO", description = "推荐人信息查询查询") @RequestBody ReferrerInfoQueryDTO referrerInfoQueryDTO) {
        return new ItemResult<>(buyerAndReferrerService.findAllReferrerInfoByCondition(referrerInfoQueryDTO));
    }

    @Operation(summary = "查询所有推荐人，并返回一个包含二维码图片的html")
    @GetMapping(value = "/findAllReferrerInfoHTML")
    public void findAllReferrerInfoHTML(
            @Parameter(name = "width", description = "宽度") @RequestParam(defaultValue = "500", required = false) Integer width,
            @Parameter(name = "tdSize", description = "每行显示二维码的个数") @RequestParam(defaultValue = "4", required = false) Integer tdSize,
            HttpServletRequest request,
            HttpServletResponse response) throws IOException {
        if (width == null) {
            width = 4;
        }
        if (tdSize == null) {
            tdSize = 500;
        }

        List<ReferrerInfoDTO> list = buyerAndReferrerService.findAllReferrerInfo();

        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-type", "text/html;charset=UTF-8");
        PrintWriter printWriter = response.getWriter();
        printWriter.write("<html><head><title>推荐人二维码</title>");
        printWriter.write("<style type=\"text/css\">tr td{position:relative;} tr td span{position:absolute;top:10px;left:35px} tr td img{margin-top:50px}</style>");
        printWriter.write("</head><br/>");
        printWriter.write("可选参数:  tdSize: 每行显示二维码的个数 默认4，width: 二维码边长 默认500<br/>");
        printWriter.write("类型：0 个人 1 一类门店 2 二类门店 3 承运商 <br/>");
        printWriter.write("二维码包含信息只有id<br/><br/>");
        printWriter.write("<table><tr>");
        if (list != null && !list.isEmpty()) {

            String url = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort();
            for (int i = 0; i < list.size(); i++) {
                if (i > 0 && i % tdSize == 0) {
                    printWriter.write("</tr><tr>");
                }
                String imageUrl = url + "/buyerAndReferrer/getImage/" + list.get(i).getReferrerId() + "/" + width;
                printWriter.write("<td><span>" + list.get(i).getReferrerType() + " " + list.get(i).getReferrerMobile() + " " + list.get(i).getReferrerName() + "<br/>id:" + list.get(i).getReferrerId() + "</span><image src = \"" + imageUrl + "\"></td>");
            }
        }

        printWriter.write("</tr></table>");
        printWriter.write("</html>");
    }

    @Operation(summary = "返回一个二维码图片")
    @GetMapping(value = "/getImage/{id}/{width}")
    public void findAllReferrerInfoByCondition(@Parameter(hidden = true) @PathVariable String id,
                                               @Parameter(hidden = true) @PathVariable Integer width,
                                               HttpServletResponse response) throws IOException, WriterException {
        ReferrerInfoDTO referrerInfoDTO = buyerAndReferrerService.findById(id);
        if (referrerInfoDTO == null) {
            return;
        }
        String text = JSON.toJSONString(ImmutableMap.of("id", id, "type", "buyerAndReferrer"));
        ImageIO.write(QrCodeUtils.generateQRCode(text, width, width, "jpg"), "jpg", response.getOutputStream());
    }

}
