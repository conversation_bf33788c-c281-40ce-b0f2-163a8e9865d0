package com.ecommerce.web.buyer.controller;

import com.ecommerce.base.api.dto.cloud.AttachmentinfoDTO;
import com.ecommerce.base.api.service.IAttachmentService;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.logistics.api.enums.UserRoleEnum;
import com.ecommerce.member.api.dto.member.MemberQueryDTO;
import com.ecommerce.member.api.dto.member.MemberSimpleDTO;
import com.ecommerce.member.api.dto.member.enums.MemberTypeEnum;
import com.ecommerce.member.api.service.IMemberService;
import com.ecommerce.web.buyer.vo.common.QueryUserResultVO;
import com.ecommerce.web.buyer.vo.common.QueryUserVO;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @Description:
 * @Date: Create in 上午11:15 18/8/31
 */
@Slf4j
@Tag(name = "CommonController", description = "通用")
@RestController
@RequestMapping("/common")
public class CommonController {

    @Autowired
    private IMemberService memberService;

    @Autowired
    private IAttachmentService attachmentService;

    @PostMapping(value = "/queryUrlByBids")
    public ItemResult<List<String>> queryUrlByBids(@Parameter(name = "bids", description = "bids列表") @RequestBody List<String> bids) {
        List<String> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(bids)) {
            for (String bid : bids) {
                List<AttachmentinfoDTO> list = attachmentService.getAttachmentByBID(bid);
                if (CollectionUtils.isNotEmpty(list)) {
                    result.add(list.get(0).getAttcPath());
                }
            }
        }
        return new ItemResult<>(result);
    }

    @PostMapping("/queryUserByKey")
    public ItemResult<List<QueryUserResultVO>> queryUserByKey(@Parameter(name = "queryUserVO", description = "用户查询对象") @RequestBody QueryUserVO queryUserVO) {
        ItemResult<List<QueryUserResultVO>> itemResult = new ItemResult<>(null);
        try {
            List<MemberSimpleDTO> memberList = new ArrayList<>();
            MemberQueryDTO memberQueryDTO = new MemberQueryDTO();
            memberQueryDTO.setMemberName(queryUserVO.getKeyword());
            if (queryUserVO.getUserType().equals(UserRoleEnum.SELLER.getCode())) {
                memberQueryDTO.setSellerFlg(1);
            } else if (queryUserVO.getUserType().equals(UserRoleEnum.CARRIER.getCode())) {
                memberQueryDTO.setCarrierFlg(1);
            } else if (queryUserVO.getUserType().equals(UserRoleEnum.PERSONAL_DRIVER.getCode())) {
                memberQueryDTO.setMemberType(MemberTypeEnum.PERSON_DRIVER.getCode());
            }
            PageInfo<MemberSimpleDTO> pageInfo = memberService.pageMember(memberQueryDTO, 1, 20);
            if (CollectionUtils.isNotEmpty(pageInfo.getList())) {
                memberList = pageInfo.getList();
            }
            List<QueryUserResultVO> userList = new ArrayList<>();
            for (MemberSimpleDTO memberSimpleDTO : memberList) {
                QueryUserResultVO queryUserResultVO = new QueryUserResultVO();
                queryUserResultVO.setUserId(memberSimpleDTO.getMemberId());
                queryUserResultVO.setUserDesc(memberSimpleDTO.getMemberName());
                userList.add(queryUserResultVO);
            }
            itemResult.setData(userList);
        } catch (Exception e) {
            itemResult.setSuccess(Boolean.FALSE);
            itemResult.setDescription("指派提货单异常:" + e.toString());
        }

        return itemResult;
    }
}
