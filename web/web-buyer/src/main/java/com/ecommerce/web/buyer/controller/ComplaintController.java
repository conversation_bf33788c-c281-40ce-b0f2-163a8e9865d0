package com.ecommerce.web.buyer.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.complaint.ComplaintDTO;
import com.ecommerce.logistics.api.enums.UserRoleEnum;
import com.ecommerce.logistics.api.param.complaint.ComplaintBatchCancelParam;
import com.ecommerce.logistics.api.param.complaint.ComplaintEvaluationParam;
import com.ecommerce.logistics.api.param.complaint.ComplaintParam;
import com.ecommerce.logistics.api.param.complaint.ComplaintQueryParam;
import com.ecommerce.logistics.api.service.IComplaintService;
import com.ecommerce.member.api.session.LoginInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;
import java.util.Optional;


/**
 * Copyright (C), 2020
 *
 * <AUTHOR>
 */

@RestController
@Tag(name = "Complaint", description = "投诉相关服务")
@RequestMapping("/complaint")
public class ComplaintController {

    @Autowired
    private IComplaintService iComplaintService;

    @Operation(summary = "新增或编辑投诉记录")
    @PostMapping(value = "/addOrUpdateComplaint")
    public ItemResult<Boolean> addOrUpdateComplaint(@Parameter(hidden = true)LoginInfo loginInfo,
                                                    @RequestBody ComplaintParam complaintParam) {

        boolean addOrUpdate = CsStringUtils.isEmpty(complaintParam.getComplaintId()) ? true : false;
        if (addOrUpdate) {
            Optional.ofNullable(loginInfo).ifPresent(param -> {
                complaintParam.setCreateUser(param.getAccountName());
                complaintParam.setComplaintInitRoleId(loginInfo.getMemberId());
                complaintParam.setComplaintInitRoleName(loginInfo.getMemberName());
                complaintParam.setComplaintInitRoleType(UserRoleEnum.BUYER.getCode().toString());
            });
        }
        if (!addOrUpdate) {
            Optional.ofNullable(loginInfo).ifPresent(param -> {
                complaintParam.setUpdateUser(param.getAccountName());
            });
        }

        return iComplaintService.addOrUpdateComplaint(complaintParam);
    }


    @Operation(summary = "分页查询投诉记录列表")
    @PostMapping(value = "/pageByQueryOption")
    public ItemResult<PageData<ComplaintDTO>> pageByQueryOption(@Parameter(hidden = true)LoginInfo loginInfo,
                                                                @RequestBody PageQuery<ComplaintQueryParam> param) {

        if (Objects.nonNull(param) && Objects.nonNull(param.getQueryDTO())) {
            if (Objects.nonNull(loginInfo)) {
                param.getQueryDTO().setComplaintInitRoleId(loginInfo.getMemberId());
            }
        }

        return iComplaintService.pageByQueryOption(param);
    }

    @Operation(summary = "批量取消投诉")
    @PostMapping(value = "/batchCancelComplaint")
    public ItemResult<Boolean> batchCancelComplaint(@Parameter(hidden = true)LoginInfo loginInfo,
                                                    @RequestBody ComplaintBatchCancelParam complaintBatchCancelParam) {
        complaintBatchCancelParam.setUpdateUser(loginInfo.getAccountId());
        return iComplaintService.batchCancelComplaint(complaintBatchCancelParam);
    }

    @Operation(summary = "投诉记录完成后评价")
    @PostMapping(value = "/evaluationComplaint")
    public ItemResult<Boolean> evaluationComplaint(@Parameter(hidden = true)LoginInfo loginInfo,
                                                   @RequestBody ComplaintEvaluationParam complaintEvaluationParam) {

        Optional.ofNullable(loginInfo).ifPresent(param -> {
            complaintEvaluationParam.setUpdateUser(param.getAccountName());
        });

        return iComplaintService.evaluationComplaint(complaintEvaluationParam);
    }


}
