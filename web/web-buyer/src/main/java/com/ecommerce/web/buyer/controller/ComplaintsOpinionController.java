package com.ecommerce.web.buyer.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.information.api.dto.complaintsOpinion.ComplaintsOpinionAddDTO;
import com.ecommerce.information.api.dto.complaintsOpinion.ComplaintsOpinionComplateDTO;
import com.ecommerce.information.api.dto.complaintsOpinion.ComplaintsOpinionDTO;
import com.ecommerce.information.api.dto.complaintsOpinion.ComplaintsQueryDTO;
import com.ecommerce.information.api.dto.complaintsOpinion.ProcessingRecordsAddDTO;
import com.ecommerce.information.api.dto.complaintsOpinion.ProcessingRecordsDTO;
import com.ecommerce.information.api.service.IComplaintsOpinionService;
import com.ecommerce.member.api.session.LoginInfo;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@RestController
@Tag(name = "ComplaintsOpinionController", description = "投诉建议服务接口")
@RequestMapping("/complaintsOpinion")
public class ComplaintsOpinionController {

    @Autowired
    private IComplaintsOpinionService iComplaintsOpinionService;

    @Operation(summary = "添加投诉建议(投诉方)")
    @PostMapping(value = "/addComplaintsOpinion")
    public ItemResult<Boolean> addComplaintsOpinion(@Parameter(hidden = true)LoginInfo loginInfo,
                                                    @Parameter(name = "arg0", description = "ComplaintsOpinionAddDTO - 投诉建议项增加DTO") @RequestBody ComplaintsOpinionAddDTO arg0) {
        arg0.setCreateMemberId(loginInfo.getMemberId());
        arg0.setCreateMemberName(CsStringUtils.isBlank(loginInfo.getMemberShortName()) ? loginInfo.getMemberName() : loginInfo.getMemberShortName());
        arg0.setCreateUser(loginInfo.getAccountId());
        arg0.setCreatePhone(loginInfo.getMobile());
        iComplaintsOpinionService.addComplaintsOpinion(arg0);
        return new ItemResult<>(true);
    }

    @Operation(summary = "处理投诉建议(投诉方)")
    @PostMapping(value = "/addRecordByBuyer")
    public ItemResult<Boolean> addRecordByBuyer(@Parameter(hidden = true)LoginInfo loginInfo,
                                                @Parameter(name = "arg0", description = "ProcessingRecordsAddDTO - 处理记录增加DTO") @RequestBody ProcessingRecordsAddDTO arg0) {
        arg0.setProcessMemberId(loginInfo.getMemberId());
        arg0.setProcessMemberName(CsStringUtils.isBlank(loginInfo.getMemberShortName()) ? loginInfo.getMemberName() : loginInfo.getMemberShortName());
        arg0.setCreateUser(loginInfo.getAccountId());
        iComplaintsOpinionService.addRecordByBuyer(arg0);
        return new ItemResult<>(true);
    }

    @Operation(summary = "需要平台介入")
    @PostMapping(value = "/needPlatform")
    public ItemResult<Boolean> needPlatform(@Parameter(hidden = true)LoginInfo loginInfo,
                                            @Parameter(name = "id", description = "id") String id) {
        iComplaintsOpinionService.needPlatform(id, loginInfo.getAccountId());
        return new ItemResult<>(true);
    }


    @Operation(summary = "完结投诉建议")
    @PostMapping(value = "/complateComplaintsOpinion")
    public ItemResult<Boolean> complateComplaintsOpinion(@Parameter(hidden = true)LoginInfo loginInfo,
                                                         @Parameter(name = "arg0", description = "ComplaintsOpinionComplateDTO - 投诉建议项完成DTO") @RequestBody ComplaintsOpinionComplateDTO arg0) {
        arg0.setOperator(loginInfo.getAccountId());
        iComplaintsOpinionService.complateComplaintsOpinion(arg0);
        return new ItemResult<>(true);
    }

    @Operation(summary = "取消投诉建议")
    @PostMapping(value = "/cancelComplaintsOpinion")
    public ItemResult<Boolean> cancelComplaintsOpinion(@Parameter(hidden = true)LoginInfo loginInfo,
                                                       @Parameter(name = "arg0", description = "ComplaintsOpinionComplateDTO - 投诉建议项完成DTO") @RequestBody ComplaintsOpinionComplateDTO arg0) {
        arg0.setOperator(loginInfo.getAccountId());
        iComplaintsOpinionService.cancelComplaintsOpinion(arg0);
        return new ItemResult<>(true);
    }

    @Operation(summary = "按条件分页查询投诉建议")
    @PostMapping(value = "/getComplaintsOpinionListByCondition")
    public ItemResult<PageInfo<ComplaintsOpinionDTO>> getComplaintsOpinionListByCondition(@Parameter(hidden = true)LoginInfo loginInfo,
                                                                                          @Parameter(name = "arg0", description = "ComplaintsQueryDTO - 投诉建议查询DTO") @RequestBody ComplaintsQueryDTO arg0) {
        arg0.setCreateMemberId(loginInfo.getMemberId());
        return new ItemResult<>(iComplaintsOpinionService.getComplaintsOpinionListByCondition(arg0));
    }

    @Operation(summary = "查询单条投诉建议(投诉方)")
    @GetMapping(value = "/findByIdAndCreateMemberId")
    public ItemResult<ComplaintsOpinionDTO> findByIdAndCreateMemberId(@Parameter(hidden = true)LoginInfo loginInfo,
                                                                      @Parameter(name = "id", description = "id") String id) {
        return new ItemResult<>(iComplaintsOpinionService.findByIdAndCreateMemberId(id, loginInfo.getMemberId()));
    }


    @Operation(summary = "查询单条投诉建议(投诉人)")
    @GetMapping(value = "/findByIdAndAccountId")
    public ItemResult<ComplaintsOpinionDTO> findByIdAndAccountId(@Parameter(hidden = true)LoginInfo loginInfo,
                                                                 @Parameter(name = "id", description = "id") String id) {
        return new ItemResult<>(iComplaintsOpinionService.findByIdAndAccountId(id, loginInfo.getAccountId()));
    }

    @Operation(summary = "查询投诉建议处理记录(投诉方)")
    @GetMapping(value = "/findProcessingRecordsByIdAndCreateMemberId")
    public ItemResult<List<ProcessingRecordsDTO>> findProcessingRecordsByIdAndCreateMemberId(@Parameter(hidden = true)LoginInfo loginInfo,
                                                                                             @Parameter(name = "id", description = "id") String id) {
        return new ItemResult<>(iComplaintsOpinionService.findProcessingRecordsByIdAndCreateMemberId(id, loginInfo.getMemberId()));
    }

    @Operation(summary = "查询投诉建议处理记录(投诉人)")
    @GetMapping(value = "/findProcessingRecordsByIdAndAccountId")
    public ItemResult<List<ProcessingRecordsDTO>> findProcessingRecordsByIdAndAccountId(@Parameter(hidden = true)LoginInfo loginInfo,
                                                                                        @Parameter(name = "id", description = "id") String id) {
        return new ItemResult<>(iComplaintsOpinionService.findProcessingRecordsByIdAndAccountId(id, loginInfo.getAccountId()));
    }


}
