package com.ecommerce.web.buyer.controller;


import com.ecommerce.common.result.ItemResult;
import com.ecommerce.goods.api.dto.contract.ContractAdjustPriceDTO;
import com.ecommerce.goods.api.service.IContractAdjustPriceService;
import com.ecommerce.member.api.session.LoginInfo;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@RestController
@Tag(name = "ContractAdjustPriceController", description = "合同调价函接口")
@RequestMapping("/contractAdjustPrice")
public class ContractAdjustPriceController {

    @Autowired
    private IContractAdjustPriceService iContractAdjustPriceService;

    @Operation(summary = "查询调价函(调价函ID，买家ID)")
    @GetMapping(value = "/getContractAdjustPriceByBuyer")
    public ItemResult<ContractAdjustPriceDTO> getContractAdjustPriceByBuyer(@Parameter(hidden = true) LoginInfo loginInfo,
                                                    @Parameter(name = "contractAdjustPriceId", description = "合同调价函ID") @RequestParam String contractAdjustPriceId) {
        ContractAdjustPriceDTO price = iContractAdjustPriceService.getContractAdjustPriceByBuyer(contractAdjustPriceId, loginInfo.getMemberId());
        return new ItemResult<>(price);
    }

    @Operation(summary = "查询调价函分页（卖家ID）")
    @PostMapping(value = "/pageContractAdjustPrice")
    public ItemResult<PageInfo<ContractAdjustPriceDTO>> pageContractAdjustPrice(@Parameter(hidden = true) LoginInfo loginInfo,
                                              @Parameter(name = "contractAdjustPriceDTO", description = "调价函对象") @RequestBody ContractAdjustPriceDTO contractAdjustPriceDTO) {
        contractAdjustPriceDTO.setBuyerId(loginInfo.getMemberId());
        if (contractAdjustPriceDTO.getPageNum() == null) {
            contractAdjustPriceDTO.setPageNum(1);
        }
        if (contractAdjustPriceDTO.getPageSize() == null) {
            contractAdjustPriceDTO.setPageSize(10);
        }
        PageInfo<ContractAdjustPriceDTO> info = iContractAdjustPriceService.pageContractAdjustPrice(contractAdjustPriceDTO);
        return new ItemResult<>(info);
    }

    @Operation(summary = "查询调价函分页(详细信息)")
    @PostMapping(value = "/pageContractAdjustPriceDetail")
    public ItemResult<PageInfo<ContractAdjustPriceDTO>> pageContractAdjustPriceDetail(@Parameter(hidden = true) LoginInfo loginInfo,
                                                    @Parameter(name = "contractAdjustPriceDTO", description = "调价函对象") @RequestBody ContractAdjustPriceDTO contractAdjustPriceDTO) {
        contractAdjustPriceDTO.setBuyerId(loginInfo.getMemberId());
        if (contractAdjustPriceDTO.getPageNum() == null) {
            contractAdjustPriceDTO.setPageNum(1);
        }
        if (contractAdjustPriceDTO.getPageSize() == null) {
            contractAdjustPriceDTO.setPageSize(10);
        }
        PageInfo<ContractAdjustPriceDTO> info = iContractAdjustPriceService.pageContractAdjustPriceDetail(contractAdjustPriceDTO);
        return new ItemResult<>(info);
    }

}
