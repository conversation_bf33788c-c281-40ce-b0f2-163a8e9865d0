package com.ecommerce.web.buyer.controller;


import com.ecommerce.goods.api.dto.contract.ContractAdjustPriceGoodsDTO;
import com.ecommerce.goods.api.service.IContractAdjustPriceGoodsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@RestController
@Tag(name = "ContractAdjustPriceGoodsController", description = "调价函关联商品接口")
@RequestMapping("/contractAdjustPriceGoods")
public class ContractAdjustPriceGoodsController {

    @Autowired
    private IContractAdjustPriceGoodsService iContractAdjustPriceGoodsService;

    @Operation(summary = "商品调价历史查看（通过合同序号，因为合同表存在历史记录，相同合同的ID会变化，但合同序号不变）")
    @PostMapping(value = "/findContractAdjustPriceGoodsList")
    public List<ContractAdjustPriceGoodsDTO> findContractAdjustPriceGoodsList(@Parameter(name = "contractSequence", description = "合同序号") @RequestParam String contractSequence) {
        return iContractAdjustPriceGoodsService.findContractAdjustPriceGoodsList(contractSequence);
    }

    @Operation(summary = "获取调价函关联的商品")
    @PostMapping(value = "/getContractAdjustPriceGoods")
    public List<ContractAdjustPriceGoodsDTO> getContractAdjustPriceGoods(@Parameter(name = "contractAdjustPriceGoodsId", description = "调价函关联商品id") @RequestParam String contractAdjustPriceGoodsId) {
        return iContractAdjustPriceGoodsService.getContractAdjustPriceGoods(contractAdjustPriceGoodsId);
    }
}