package com.ecommerce.web.buyer.controller;

import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.exception.BusinessOperationException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.service.common.BizRedisService;
import com.ecommerce.goods.api.dto.contract.ContractOption;
import com.ecommerce.goods.api.dto.contract.QueryContractConditionDTO;
import com.ecommerce.goods.api.dto.contract.ReqBuyerContractListDTO;
import com.ecommerce.goods.api.dto.contract.TrContractDTO;
import com.ecommerce.goods.api.dto.contract.TrContractGoodsDTO;
import com.ecommerce.goods.api.dto.contract.batch.adjust.price.IdAndName;
import com.ecommerce.goods.api.service.IContractService;
import com.ecommerce.member.api.dto.account.AccountDTO;
import com.ecommerce.member.api.session.ERPAccountInfo;
import com.ecommerce.member.api.session.LoginInfo;
import com.ecommerce.order.api.dto.OrderDTO;
import com.ecommerce.order.api.dto.OrderQueryDTO;
import com.ecommerce.order.api.service.IOrderService;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * @Auther: chenjun
 * @Description:
 * @Date: 04/09/2018 20:36
 */
@Slf4j
@Tag(name = "ContractController", description = "买家查看合同")
@RestController
@RequestMapping("/contract")
public class ContractController {

    @Autowired
    private IContractService iContractService;

    @Autowired
    private IOrderService orderService;
    @Autowired
    private BizRedisService bizRedisService;

    @Operation(summary = "买家获取合同详情")
    @PostMapping(value = "/getBuyerContract")
    public ItemResult<TrContractDTO> getBuyerContract(@Parameter(hidden = true) LoginInfo loginInfo,
                                       @Parameter(name = "contractId", description = "合同id") @RequestParam String contractId) {
        String memberId = loginInfo.getMemberId();
        TrContractDTO trContractDTO = iContractService.getBuyerContract(contractId, memberId);
        //设置买家行政区域权限
        if (CollectionUtils.isNotEmpty(loginInfo.getAccountRegionAdCodeList()) && trContractDTO != null && CollectionUtils.isNotEmpty(trContractDTO.getContractGoodsDTOS())) {
            log.info("trContractDTO.getContractGoodsDTOS.size:{}",trContractDTO.getContractGoodsDTOS().size());
            Set<String> regions = getRegions(loginInfo);
            List<TrContractGoodsDTO> filterResult = trContractDTO.getContractGoodsDTOS().stream()
                    .filter(item -> item.getGoodsRegions() != null)
                    .filter(item -> regions.stream().anyMatch(regionCode -> item.getGoodsRegions().contains(regionCode)))
                    .toList();
            log.info("trContractDTO.getContractGoodsDTOS filterResult.size:{}",filterResult.size());
            trContractDTO.setContractGoodsDTOS(filterResult);
        }else{
            log.info("没有过滤行政区域,contractId:{},loginInfo.regionAdCodes:{}",contractId,loginInfo.getAccountRegionAdCodeList());
        }
        return new ItemResult<>(trContractDTO);
    }

    private Set<String> getRegions(LoginInfo loginInfo){
        Set<String> regions = Sets.newHashSet();
        if( CollectionUtils.isNotEmpty(loginInfo.getAccountRegionAdCodeList())) {
            regions.addAll(loginInfo.getAccountRegionAdCodeList());
            Set<String> strings = loginInfo.getAccountRegionAdCodeList().stream()
                    .filter(Objects::nonNull)
                    //如果授权的是区域，可扩大范围 以匹配市的商品
                    .filter(item -> CsStringUtils.length(item) == 6)
                    .filter(item -> !item.endsWith("00"))
                    .map(item -> CsStringUtils.left(item, 4) + "00").collect(Collectors.toSet());
            regions.addAll(strings);
        }
        return regions;
    }

    @Operation(summary = "买家合同列表")
    @PostMapping(value = "/getBuyerContractList")
    public ItemResult<PageInfo<TrContractDTO>> getBuyerContractList(@Parameter(hidden = true) LoginInfo loginInfo,
                                           @Parameter(name = "reqBuyerContractListDTO", description = "请求买家合同列表对象") @RequestBody ReqBuyerContractListDTO reqBuyerContractListDTO) {
        String memberId = loginInfo.getMemberId();
        //只有企业子账号受权限控制
        reqBuyerContractListDTO.setRegionCodeList(null);
        reqBuyerContractListDTO.setMdmCodeList(null);
        if (loginInfo.getAccountType() == AccountDTO.ACCOUNT_TYPE_MEMBER_SUB) {
            //设置买家行政区域权限
            if (CollectionUtils.isNotEmpty(loginInfo.getAccountRegionAdCodeList())) {
                reqBuyerContractListDTO.setRegionCodeList(loginInfo.getAccountRegionAdCodeList());
            }
            //设置ERP账户权限
            if (CollectionUtils.isNotEmpty(loginInfo.getErpAccountList())) {
                List<IdAndName> list = Lists.newArrayList();
                Set<String> set = Sets.newHashSet();
                for (ERPAccountInfo item : loginInfo.getErpAccountList()) {
                    String key = item.getSellerMemberId() + "_"+ item.getMdmCode();
                    if( !set.contains(key) ) {
                        list.add(new IdAndName(item.getSellerMemberId(), item.getMdmCode()));
                        set.add(key);
                    }
                }
                reqBuyerContractListDTO.setMdmCodeAndTargetMemberIdList(list);
            }
        }
        return new ItemResult<>(iContractService.getBuyerContractList(reqBuyerContractListDTO, memberId));
    }

    @Operation(summary = "根据合同序号查询订单分页列表")
    @GetMapping(value = "/findOrderPageBySequence")
    public ItemResult<PageInfo<OrderDTO>> findOrderPageBySequence(@Parameter(hidden = true)LoginInfo loginInfo,
                                                                  @Parameter(name = "contractSequence", description = "合同序号") @RequestParam String contractSequence,
                                                                  @Parameter(name = "orderStatus", description = "订单状态") @RequestParam String orderStatus,
                                                                  @Parameter(name = "pageSize", description = "每页条数") @RequestParam Integer pageSize,
                                                                  @Parameter(name = "pageNum", description = "当前页码") @RequestParam Integer pageNum) {
        if (CsStringUtils.isBlank(contractSequence)) {
            log.error("参数为空,contractSequence:" + contractSequence);
            throw new BizException(BasicCode.PARAM_NULL);
        }
        log.info("========findOrderPageBySequence============:[{},{}]", contractSequence, orderStatus);
        OrderQueryDTO query = new OrderQueryDTO();
        query.setOrderStatus(orderStatus);
        query.setContractSequence(contractSequence);
        //设置买家行政区域权限
        if (CollectionUtils.isNotEmpty(loginInfo.getAccountRegionAdCodeList())) {
            log.info("downloadSellerOrder_regionCode:" + loginInfo.getAccountRegionAdCodeList());
            query.setRegionCodeList(Lists.newArrayList(getRegions(loginInfo)));
        }
        ItemResult<PageInfo<OrderDTO>>  result = orderService.pagePlatformOrder(query, pageSize, pageNum);
        if(result != null && result.getData() != null ) {
            updateShowPrice(result.getData().getList());
        }
        return result;
    }

    @Operation(summary = "合同是否显示价格(true 显示)")
    @GetMapping(value="/anon/showPrice")
    public ItemResult<Boolean> showPrice(@Parameter(hidden = true)LoginInfo loginInfo, @Parameter(name = "contractId", description = "合同id") @RequestParam String contractId) throws Exception{
        log.info("contractId:{}",contractId);
        if (loginInfo == null || CsStringUtils.isBlank(loginInfo.getAccountName())) {
            log.warn("没登录，当前接口不显示合同:{}价格",contractId);
            return new ItemResult<>(false);
        }
        if (CsStringUtils.isBlank(contractId)) {
            return new ItemResult<>(true);
        }
        //有缓存走缓存
        Object obj = bizRedisService.get(TrContractDTO.REDIS_KEY_SHOW_PRICE_PREFIX + contractId);
        if(obj != null) {
            return new ItemResult<>("1".equals(obj.toString()));
        }
        return new ItemResult<>(iContractService.showPrice(contractId));
    }

    @Operation(summary = "根据买家ID查询合同下拉选项")
    @PostMapping(value = "/queryBuyerContractOptions")
    public ItemResult<List<ContractOption>> queryBuyerContractOptions(@Parameter(hidden = true)LoginInfo loginInfo) {
        QueryContractConditionDTO queryDTO = new QueryContractConditionDTO();
        queryDTO.setBuyerId(loginInfo.getMemberId());
        //只有企业子账号受权限控制
        queryDTO.setRegionCodeList(null);
        queryDTO.setMdmCodeList(null);
        if (loginInfo.getAccountType() == AccountDTO.ACCOUNT_TYPE_MEMBER_SUB) {
            // 设置买家行政区域权限
            if (CollectionUtils.isNotEmpty(loginInfo.getAccountRegionAdCodeList())) {
                queryDTO.setRegionCodeList(loginInfo.getAccountRegionAdCodeList());
            }
            // 设置ERP账户权限
            if (CollectionUtils.isNotEmpty(loginInfo.getErpAccountList())) {
                List<IdAndName> list = Lists.newArrayList();
                Set<String> set = Sets.newHashSet();
                for (ERPAccountInfo item : loginInfo.getErpAccountList()) {
                    String key = item.getSellerMemberId() + "_"+ item.getMdmCode();
                    if( !set.contains(key) ) {
                        list.add(new IdAndName(item.getSellerMemberId(), item.getMdmCode()));
                        set.add(key);
                    }
                }
                queryDTO.setMdmCodeAndTargetMemberIdList(list);
            }
        }
        return new ItemResult<>(iContractService.queryContractOptionsByCondition(queryDTO));
    }

    private void updateShowPrice(List<OrderDTO> list){
        if(CollectionUtils.isEmpty(list)){
            log.info("list is empty.");
            return;
        }
        Set<String> contractIds = list.stream().map(OrderDTO::getDealsId).filter(item -> CsStringUtils.isNotBlank(item)).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(contractIds)){
            log.info("contractIds is empty.");
            return;
        }
        List<String> showPriceContractIds = iContractService.showPriceFalseByContractIds(Lists.newArrayList(contractIds));
        log.info("showPriceFalseByContractIds:{}",showPriceContractIds);
        if(CollectionUtils.isEmpty(showPriceContractIds)){
            contractIds = Sets.newHashSet();
        }else{
            contractIds = Sets.newHashSet(showPriceContractIds);
        }
        for (OrderDTO orderDTO : list) {
            orderDTO.setShowPrice(true);
            if(contractIds.contains(orderDTO.getDealsId())){
                orderDTO.setShowPrice(false);
            }
        }
    }
}
