package com.ecommerce.web.buyer.controller;

import com.ecommerce.goods.api.dto.contract.DownloadResponseDTO;
import com.ecommerce.goods.api.service.IContractTemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;

import static org.apache.hc.core5.http.HttpHeaders.CONTENT_TYPE;


/**
 * <AUTHOR>
 * @created 10:16 16/08/2019
 * @description
 */
@Slf4j
@Tag(name = "ContractTemplateController", description = "管理合同模板")
@RestController
@RequestMapping("/contractTemplate")
public class ContractTemplateController {

    @Autowired
    private IContractTemplateService iContractTemplateService;

    private static final String CONTENT_DISPOSITION = "Content-disposition";

    @Operation(summary = "根据合同id下载")
    @GetMapping(value = "/downloadFileByContract")
    public void downloadFileByContract(@Parameter(name = "contractId", description = "合同id") @RequestParam String contractId,
                                       HttpServletResponse response) throws IOException {
        log.info(">>> downloadFileByContract [contractId]: {}", contractId);
        DownloadResponseDTO responseDTO = iContractTemplateService.downloadFileByContract(contractId);
        log.info("responseDTO: {}, {}", responseDTO.getBytes().length, responseDTO.getFileName());
        response.setHeader(CONTENT_DISPOSITION, responseDTO.getFileName());
        response.setHeader(CONTENT_TYPE, "application/octet-stream");
        response.setHeader("Access-Control-Expose-Headers", CONTENT_DISPOSITION);
        response.getOutputStream().write(responseDTO.getBytes());
        response.getOutputStream().flush();
        response.getOutputStream().close();
    }

    @Operation(summary = "根据调价函id下载")
    @GetMapping(value = "/downloadFileByAdjustPrice")
    public void downloadFileByAdjustPrice(@Parameter(name = "adjustPriceId", description = "调价函id") @RequestParam String adjustPriceId,
                                          HttpServletResponse response) throws IOException {
        log.info(">>> downloadFileByAdjustPrice [adjustPriceId]: {}", adjustPriceId);
        DownloadResponseDTO responseDTO = iContractTemplateService.downloadFileByAdjustPrice(adjustPriceId);
        log.info("responseDTO: {}, {}", responseDTO.getBytes().length, responseDTO.getFileName());
        response.setHeader(CONTENT_DISPOSITION, responseDTO.getFileName());
        response.setHeader(CONTENT_TYPE, "application/octet-stream");
        response.setHeader("Access-Control-Expose-Headers", CONTENT_DISPOSITION);
        response.getOutputStream().write(responseDTO.getBytes());
        response.getOutputStream().flush();
        response.getOutputStream().close();
    }

    @Operation(summary = "根据合同id下载匿名接口")
    @GetMapping(value = "/anon/downloadFileByContract")
    public void anonDownloadFileByContract(@Parameter(name = "contractId", description = "合同id") @RequestParam String contractId,
                                           HttpServletResponse response) throws IOException {
        DownloadResponseDTO responseDTO = iContractTemplateService.downloadFileByContract(contractId);
        response.setHeader(CONTENT_DISPOSITION, "attachment;filename=" + responseDTO.getFileName());
        response.getOutputStream().write(responseDTO.getBytes());
        response.getOutputStream().flush();
        response.getOutputStream().close();
    }
}
