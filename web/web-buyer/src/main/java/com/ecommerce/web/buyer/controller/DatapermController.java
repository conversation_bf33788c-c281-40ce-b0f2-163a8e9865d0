package com.ecommerce.web.buyer.controller;

import com.ecommerce.base.api.dto.authRes.DataPermAccountCreateDTO;
import com.ecommerce.base.api.dto.authRes.DataPermDTO;
import com.ecommerce.base.api.dto.authRes.DataPermERPAccountNameDTO;
import com.ecommerce.base.api.service.IDatapermService;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.member.api.session.LoginInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * null
 *
 * <AUTHOR>
 */

@RestController
@Tag(name = "DatapermController", description = "数据权限")
@RequestMapping("/dataperm")
public class DatapermController {

    @Autowired
    private IDatapermService iDatapermService;

    @Operation(summary = "根据id获取数据权限")
    @PostMapping(value = "/findDataByAccountId")
    public ItemResult<DataPermDTO> findDataByAccountId(@Parameter(name = "accountId", description = "账号id") @RequestParam String accountId) {
        return iDatapermService.findDataByAccountId(accountId);
    }

    @Operation(summary = "添加或更新数据权限")
    @PostMapping(value = "/addOrUpdateAccountDataPerms")
    public ItemResult<Boolean> updateAccountDataPerms(@Parameter(hidden = true)LoginInfo loginInfo,
                                                      @Parameter(name = "dataPermAccountCreateDTO", description = "添加/更新账号数据权限") @RequestBody DataPermAccountCreateDTO dataPermAccountCreateDTO) {
        dataPermAccountCreateDTO.setOperatorId(loginInfo.getAccountId());
        dataPermAccountCreateDTO.setMemberId(loginInfo.getMemberId());
        iDatapermService.updateAccountDataPerms(dataPermAccountCreateDTO);
        return new ItemResult<>(true);
    }

    @Operation(summary = "根据账号id和数据权限类型代码获取数据权限(sale_region-销售区域,region-行政区域,storehouse-仓库)")
    @PostMapping(value = "/findByAccountIdAndDataPrivCode")
    public ItemResult<List<String>> findByAccountIdAndDataPrivCode(@Parameter(name = "accountId", description = "账号id") @RequestParam String accountId,
                                                                   @Parameter(name = "dataPrivCode", description = "数据权限类型代码") @RequestParam String dataPrivCode) {
        return iDatapermService.findByAccountIdAndDataPrivCode(accountId, dataPrivCode);
    }

    @Operation(summary = "根据账号id查询其erp账号数据权限")
    @PostMapping(value = "/findErpAccountNameByAccountId")
    public ItemResult<List<DataPermERPAccountNameDTO>> findErpAccountNameByAccountId(@Parameter(name = "accountId", description = "账号id") @RequestParam String accountId) {
        return iDatapermService.findErpAccountNameByAccountId(accountId);
    }

}



