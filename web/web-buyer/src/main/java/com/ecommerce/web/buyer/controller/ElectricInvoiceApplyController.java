package com.ecommerce.web.buyer.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.member.api.session.LoginInfo;
import com.ecommerce.pay.api.v2.dto.electricInvoiceAccount.PageQuery;
import com.ecommerce.pay.api.v2.dto.invoice.AuditInvoiceApplyDTO;
import com.ecommerce.pay.api.v2.dto.invoice.InvoiceApplyAddDTO;
import com.ecommerce.pay.api.v2.dto.invoice.InvoiceApplyBizListDTO;
import com.ecommerce.pay.api.v2.dto.invoice.InvoiceApplyBizListQueryDTO;
import com.ecommerce.pay.api.v2.dto.invoice.InvoiceApplyDetailDTO;
import com.ecommerce.pay.api.v2.dto.invoice.InvoiceApplyListDTO;
import com.ecommerce.pay.api.v2.dto.invoice.InvoiceApplyListQueryDTO;
import com.ecommerce.pay.api.v2.dto.invoice.RevokeInvoiceApplyDTO;
import com.ecommerce.pay.api.v2.dto.invoice.StatistcsInvoiceApplyDTO;
import com.ecommerce.pay.api.v2.dto.invoice.StatistcsInvoiceApplyResultDTO;
import com.ecommerce.pay.api.v2.service.IElectricInvoiceApplyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * @Created锛�Tue May 07 09:56:14 CST 2019
 * <AUTHOR>
 * @Version:2
 * @Description:电子发票申请服务 isSpecial 是否为专票
 */

@RestController
@Tag(name = "ElectricInvoiceApplyController", description = "电子发票申请服务")
@RequestMapping("/electricInvoiceApply")
public class ElectricInvoiceApplyController {

    @Autowired
    private IElectricInvoiceApplyService iElectricInvoiceApplyService;

    @Operation(summary = "获取发票申请详情")
    @PostMapping(value = "/queryInvoiceApplyDetail")
    public ItemResult<InvoiceApplyDetailDTO> queryInvoiceApplyDetail(@Parameter(name = "applyId", description = "申请id") @RequestParam String applyId) {
        return iElectricInvoiceApplyService.queryInvoiceApplyDetail(applyId);
    }

    @Operation(summary = "审核发票申请")
    @PostMapping(value = "/auditInvoiceApply")
    public ItemResult<Void> auditInvoiceApply(@Parameter(hidden = true)LoginInfo loginInfo,
                                              @Parameter(name = "auditInvoiceApplyDTO", description = "审批实体") @RequestBody AuditInvoiceApplyDTO auditInvoiceApplyDTO) {
        auditInvoiceApplyDTO.setUpdateUser(loginInfo.getAccountId());
        return iElectricInvoiceApplyService.auditInvoiceApply(auditInvoiceApplyDTO);
    }

    @Operation(summary = "分页查询开票申请列表")
    @PostMapping(value = "/queryInvoiceApplyList")
    public ItemResult<PageData<InvoiceApplyListDTO>> queryInvoiceApplyList(LoginInfo loginInfo,
                                                                           @Parameter(name = "pageQuery", description = "发票申请列表查询DTO分页查询对象") @RequestBody PageQuery<InvoiceApplyListQueryDTO> pageQuery) {
        InvoiceApplyListQueryDTO queryDTO = pageQuery.getQueryDTO();
        if (queryDTO == null) {
            queryDTO = new InvoiceApplyListQueryDTO();
            pageQuery.setQueryDTO(queryDTO);
        }
        pageQuery.getQueryDTO().setBuyerId(loginInfo.getMemberId());
        return iElectricInvoiceApplyService.queryInvoiceApplyList(pageQuery);
    }

    @Operation(summary = "统计发票申请数据")
    @PostMapping(value = "/statistcsInvoiceApply")
    public ItemResult<StatistcsInvoiceApplyResultDTO> statistcsInvoiceApply(@Parameter(name = "statistcsInvoiceApplyDTO", description = "统计发票申请DTO") @RequestBody StatistcsInvoiceApplyDTO statistcsInvoiceApplyDTO) {
        return iElectricInvoiceApplyService.statistcsInvoiceApply(statistcsInvoiceApplyDTO);
    }

    @Operation(summary = "分页查询发票业务明细列表")
    @PostMapping(value = "/queryInvoiceApplyBizList")
    public ItemResult<PageData<InvoiceApplyBizListDTO>> queryInvoiceApplyBizList(@Parameter(name = "pageQuery", description = "发票申请业务列表查询DTO分页查询对象") @RequestBody PageQuery<InvoiceApplyBizListQueryDTO> pageQuery) {
        return iElectricInvoiceApplyService.queryInvoiceApplyBizList(pageQuery);
    }

    @Operation(summary = "批量申请开票")
    @PostMapping(value = "/batchApplyInvoice")
    public ItemResult<Void> batchApplyInvoice(@Parameter(hidden = true)LoginInfo loginInfo,
                                              @Parameter(name = "invoiceApplyAddDTOList", description = "开票申请实体列表") @RequestBody List<InvoiceApplyAddDTO> invoiceApplyAddDTOList) {
        if (CollectionUtils.isEmpty(invoiceApplyAddDTOList)) {
            ItemResult<Void> result = new ItemResult<>();
            result.setSuccess(false);
            result.setDescription("申请不能为空");
            return result;
        }
        String createUser = loginInfo.getAccountId();
        for (InvoiceApplyAddDTO applyAddDTO : invoiceApplyAddDTOList) {
            applyAddDTO.setCreateUser(createUser);
        }
        return iElectricInvoiceApplyService.batchApplyInvoice(invoiceApplyAddDTOList);
    }

    @Operation(summary = "红冲发票申请")
    @PostMapping(value = "/revokeInvoiceApply")
    public ItemResult<Void> revokeInvoiceApply(@Parameter(hidden = true)LoginInfo loginInfo,
                                               @Parameter(name = "revokeInvoiceApplyDTO", description = "红冲请求实体") @RequestBody RevokeInvoiceApplyDTO revokeInvoiceApplyDTO) {
        revokeInvoiceApplyDTO.setUpdateUser(loginInfo.getAccountId());
        return iElectricInvoiceApplyService.revokeInvoiceApply(revokeInvoiceApplyDTO);
    }
}
