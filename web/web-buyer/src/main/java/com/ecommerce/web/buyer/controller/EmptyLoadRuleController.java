package com.ecommerce.web.buyer.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.member.api.session.LoginInfo;
import com.ecommerce.price.api.dto.emptyload.EmptyLoadRuleDTO;
import com.ecommerce.price.api.dto.emptyload.EmptyLoadRuleQueryDTO;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;


/**
 * @Created锛�Fri May 31 14:42:12 CST 2019
 * <AUTHOR>
 * @Version:2
 * @Description:null
 */

@RestController
@RequestMapping("/emptyLoadRule")
@Slf4j
@Tag(name = "EmptyLoadRuleController", description = "空载费规则")
public class EmptyLoadRuleController {


    @Operation(summary = "空载费规则分页列表")
    @PostMapping(value = "/pageEmptyLoadRuleList")
    public ItemResult<PageInfo<EmptyLoadRuleDTO>> pageEmptyLoadRuleList(@Parameter(hidden = true)LoginInfo loginInfo,
                                                                        @Parameter(name = "reqDTO", description = "空载费查询DTO") @RequestBody EmptyLoadRuleQueryDTO reqDTO) throws Exception {
        reqDTO.setSellerId(loginInfo.getMemberId());
        log.info("空载费分页查询：=====》{}", reqDTO);
        return new ItemResult<>(new PageInfo<>(Collections.emptyList()));
    }

    @Operation(summary = "根据空载费规则Id查询记录")
    @PostMapping(value = "/queryEmptyLoadRuleById")
    public ItemResult<EmptyLoadRuleDTO> queryEmptyLoadRuleById(@Parameter(name = "ruleId", description = "空载费规则Id") @RequestParam String ruleId) throws Exception {
        log.info("根据空载费规则Id查询记录:======>{}", ruleId);
        return new ItemResult<>(new EmptyLoadRuleDTO());
    }

    @Operation(summary = "根据卖家Id查询记录")
    @PostMapping(value = "/queryEmptyLoadRuleBySellerId")
    public ItemResult<List<EmptyLoadRuleDTO>> queryEmptyLoadRuleBySellerId(@Parameter(hidden = true)LoginInfo loginInfo) throws Exception {
        String sellerId = loginInfo.getMemberId();
        log.info("根据卖家Id查询记录:======>{}", sellerId);
        EmptyLoadRuleQueryDTO dto = new EmptyLoadRuleQueryDTO();
        dto.setSellerId(loginInfo.getMemberId());
        return new ItemResult<>(Collections.emptyList());
    }
}
