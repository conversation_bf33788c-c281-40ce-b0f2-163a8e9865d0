package com.ecommerce.web.buyer.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.order.api.dto.OrderEvaluateDetailInfoReqDTO;
import com.ecommerce.order.api.dto.OrderEvaluateDetailInfoResDTO;
import com.ecommerce.order.api.dto.OrderEvaluateListReqDTO;
import com.ecommerce.order.api.dto.OrderEvaluateListResDTO;
import com.ecommerce.order.api.dto.OrderEvaluatePostReqDTO;
import com.ecommerce.order.api.service.IEvaluateService;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 * @Date: 2019/4/30 2:15 PM
 * @Description 买家调用评价相关接口
 */
@RestController
@Tag(name = "EvaluateController", description = "评价相关接口类")
@RequestMapping("/evaluate")
public class EvaluateController {

    @Autowired
    IEvaluateService iEvaluateService;

    @Operation(summary = "根据条件分页获取评价相关列表")
    @PostMapping(value = "/pageEvaluate")
    public ItemResult<PageInfo<OrderEvaluateListResDTO>> pageEvaluate(@Parameter(name = "req", description = "订单评价列表请求dto") @RequestBody OrderEvaluateListReqDTO req) {
        return iEvaluateService.pageEvaluate(req);
    }

    @Operation(summary = "根据订单ID获取评价订单基础数据(未评价)/根据订单ID获取订单评价详情(已评价)")
    @PostMapping(value = "/getOrderEvaluateInfo")
    public ItemResult<OrderEvaluateDetailInfoResDTO> getOrderEvaluateInfo(@Parameter(name = "req", description = "订单评价详情请求dto") @RequestBody OrderEvaluateDetailInfoReqDTO req) {
        return iEvaluateService.getOrderEvaluateInfo(req);
    }

    @Operation(summary = "提交评价数据")
    @PostMapping(value = "/postOrderEvaluate")
    public ItemResult<Boolean> postOrderEvaluate(@Parameter(name = "req", description = "订单评价请求dto") @RequestBody OrderEvaluatePostReqDTO req) {
        return iEvaluateService.postOrderEvaluate(req);
    }
}
