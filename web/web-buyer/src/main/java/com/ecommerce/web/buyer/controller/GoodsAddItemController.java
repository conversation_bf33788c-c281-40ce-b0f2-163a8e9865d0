package com.ecommerce.web.buyer.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.goods.api.dto.GoodsAddItemDTO;
import com.ecommerce.goods.api.service.IGoodsAddItemService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;


/**
 * @Auther: chenjun
 * @Description:
 * @Date: 28/08/2018 11:53
 */
@Slf4j
@Tag(name = "GoodsAddItemController", description = "加价项查询平模板")
@RestController
@RequestMapping("/goodsAddItem")
public class GoodsAddItemController {

    @Autowired
    IGoodsAddItemService goodsAddItemService;

    @Operation(summary = "查询出平台模板")
    @GetMapping(value = "/listBaseTemplate")
    public ItemResult<List<GoodsAddItemDTO>> listBaseTemplate(@Parameter(name = "goodsTypeCode", description = "商品类型编号") Integer goodsTypeCode) {
        List<GoodsAddItemDTO> list = goodsAddItemService.listBaseTemplate(goodsTypeCode);
        return new ItemResult<>(list);
    }

    @Operation(summary = "根据商品分类查询商家模板")
    @GetMapping(value = "/listBusinessTemplate")
    public ItemResult<List<GoodsAddItemDTO>> listBusinessTemplate(@Parameter(name = "goodsTypeCode", description = "商品分类编号") @RequestParam Integer goodsTypeCode,
                                           @Parameter(name = "sellerId", description = "卖家ID") @RequestParam String sellerId) {
        List<GoodsAddItemDTO> dtos = goodsAddItemService.listBusinessTemplate(goodsTypeCode, sellerId);
        log.info("根据商品分类查询商家模板===>Result:[{}]", dtos);
        return new ItemResult<>(dtos);
    }
}
