package com.ecommerce.web.buyer.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.logistics.api.dto.gps.GpsManufacturerOptionDTO;
import com.ecommerce.logistics.api.service.IGpsManufacturerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 功能描述:
 *
 * @param:
 * @return:
 */

@RestController
@Tag(name = "GpsManufacturerController", description = "GPS厂商")
@RequestMapping("/gpsManufacturer")
public class GpsManufacturerController {

    @Autowired
    private IGpsManufacturerService iGpsManufacturerService;

    @Operation(summary = "获取gpsOptions")
    @PostMapping(value = "/queryGpsOptions")
    public ItemResult<List<GpsManufacturerOptionDTO>> queryGpsOptions() {
        return iGpsManufacturerService.queryGpsOptions();
    }
}
