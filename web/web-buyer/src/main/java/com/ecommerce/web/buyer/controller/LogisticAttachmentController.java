package com.ecommerce.web.buyer.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.logistics.api.dto.attachment.AttListAddDTO;
import com.ecommerce.logistics.api.dto.attachment.AttListDTO;
import com.ecommerce.logistics.api.dto.attachment.AttListQueryDTO;
import com.ecommerce.logistics.api.dto.attachment.AttRemoveDTO;
import com.ecommerce.logistics.api.service.IAttachmentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * @Created锛�Fri Sep 14 11:14:35 CST 2018
 * <AUTHOR>
 * @Version:2
 * @Description:附件服务
 */

@RestController
@Tag(name = "LogisticAttachmentController", description = "附件服务")
@RequestMapping("/attachment")
public class LogisticAttachmentController {

    @Autowired
    private IAttachmentService iAttachmentService;

    @Operation(summary = "获取附件列表")
    @PostMapping(value = "/queryAttList")
    public ItemResult<List<AttListDTO>> queryAttList(@Parameter(name = "arg0", description = "AttListQueryDTO - 附件列表查询对象") @RequestBody AttListQueryDTO arg0) {
        return iAttachmentService.queryAttList(arg0);
    }

    @Operation(summary = "添加附件信息")
    @PostMapping(value = "/addAtt")
    public ItemResult<Void> addAtt(@Parameter(name = "arg0", description = "AttListAddDTO - 附件批量新增对象") @RequestBody AttListAddDTO arg0) {
        return iAttachmentService.addAtt(arg0);
    }

    @Operation(summary = "删除附件信息")
    @PostMapping(value = "/removeAtt")
    public ItemResult<Void> removeAtt(@Parameter(name = "arg0", description = "AttRemoveDTO - 附件删除对象") @RequestBody AttRemoveDTO arg0) {
        return iAttachmentService.removeAtt(arg0);
    }

}
