package com.ecommerce.web.buyer.controller;


import com.ecommerce.common.result.ItemResult;
import com.ecommerce.member.api.session.LoginInfo;
import com.ecommerce.price.api.dto.CalculateResultDTO;
import com.ecommerce.price.api.dto.machine.MachineCalculatePriceDTO;
import com.ecommerce.price.api.dto.machine.MachineRuleDTO;
import com.ecommerce.price.api.dto.machine.MachineRuleQueryDTO;
import com.ecommerce.price.api.service.IMachineFeeService;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.List;


/**
 * @Created锛�Fri May 31 11:39:41 CST 2019
 * <AUTHOR>
 * @Version:2
 * @Description:台班费业务规则service
 */

@RestController
@Tag(name = "MachineRuleController", description = "台班费业务规则")
@RequestMapping("/machineRule")
public class MachineRuleController {

    @Autowired
    private IMachineFeeService iMachineFeeService;

    @Operation(summary = "分页查询所有台班费业务规则详情")
    @PostMapping(value = "/getMachineRuleList")
    public ItemResult<PageInfo<MachineRuleDTO>> getMachineRuleList(LoginInfo loginInfo,
                                                                   @Parameter(name = "machineRuleDTO", description = "台班费规则查询DTO") @RequestBody MachineRuleQueryDTO machineRuleDTO) {
        return iMachineFeeService.pagePriceRule(machineRuleDTO);
    }

    @Operation(summary = "查询台班费业务规则详情")
    @PostMapping(value = "/getMachineRuleDetail")
    public ItemResult<MachineRuleDTO> getMachineRuleDetail(@Parameter(name = "machineRuleId", description = "台班费规则id") @RequestParam String machineRuleId) {
        return iMachineFeeService.findPriceRuleById(machineRuleId);
    }

    @Operation(summary = "查询所有台班费业务规则详情")
    @PostMapping(value = "/getMachineRuleBySellerID")
    public ItemResult<List<MachineRuleDTO>> getMachineRuleBySellerID(@Parameter(hidden = true)LoginInfo loginInfo) {
        MachineRuleQueryDTO dto = new MachineRuleQueryDTO();
        dto.setSellerId(loginInfo.getMemberId());
        return iMachineFeeService.findPriceRule(dto);
    }

    @Operation(summary = "计算台班费业务规则")
    @PostMapping(value = "/calculationMachineRule")
    public ItemResult<BigDecimal> calculationMachineRule(@Parameter(name = "machineRuleId", description = "台班费规则id") @RequestParam String machineRuleId,
                                                         @Parameter(name = "amount", description = "数量") @RequestParam BigDecimal amount) {
        MachineCalculatePriceDTO dto = new MachineCalculatePriceDTO();
        dto.setQuantity(amount);
        dto.setMachineRuleId(machineRuleId);
        ItemResult<CalculateResultDTO> result = iMachineFeeService.calculateAmount(dto);
        return new ItemResult<>(result.getData().getAmount());
    }

    @Operation(summary = "台班费ID查询台班费集合")
    @PostMapping(value = "/getMachineRuleListByID")
    public ItemResult<List<MachineRuleDTO>> getMachineRuleListByID(@Parameter(name = "machineRuleId", description = "台班费规则id列表") @RequestBody List<String> machineRuleId) {
        return iMachineFeeService.getMachineRuleListByIds(machineRuleId);
    }
}
