package com.ecommerce.web.buyer.controller;

import com.alibaba.fastjson.JSON;
import com.ecommerce.base.api.dto.saleregion.SaleRegionDTO;
import com.ecommerce.base.api.dto.warehouse.WarehouseDetailsDTO;
import com.ecommerce.base.api.dto.warehouse.WarehouseDetailsReqDTO;
import com.ecommerce.base.api.service.ISaleRegionService;
import com.ecommerce.base.api.service.IWarehouseService;
import com.ecommerce.common.enums.AppNames;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.service.RedisService;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.common.utils.IPUtils;
import com.ecommerce.common.utils.MD5;
import com.ecommerce.goods.api.dto.contract.TrContractDTO;
import com.ecommerce.goods.api.service.IContractService;
import com.ecommerce.member.api.dto.member.enums.MemberPlatform;
import com.ecommerce.member.api.dto.member.enums.MemberTypeEnum;
import com.ecommerce.member.api.service.IMemberService;
import com.ecommerce.member.api.session.LoginInfo;
import com.ecommerce.open.api.dto.gnete.enums.ApplyTicketJumpTypeEnum;
import com.ecommerce.order.api.dto.OrderDTO;
import com.ecommerce.order.api.dto.OrderIdAndContractIdDTO;
import com.ecommerce.order.api.enums.PayDetailTypeEnum;
import com.ecommerce.order.api.service.IOrderService;
import com.ecommerce.pay.api.v2.dto.BankCardInfoDTO;
import com.ecommerce.pay.api.v2.dto.BankCityDTO;
import com.ecommerce.pay.api.v2.dto.BankInfoDTO;
import com.ecommerce.pay.api.v2.dto.BaseBankDTO;
import com.ecommerce.pay.api.v2.dto.ChannelCardDTO;
import com.ecommerce.pay.api.v2.dto.ChannelCardRequestDTO;
import com.ecommerce.pay.api.v2.dto.ChannelCardResponseDTO;
import com.ecommerce.pay.api.v2.dto.ChannelConfigDTO;
import com.ecommerce.pay.api.v2.dto.KeyValueDTO;
import com.ecommerce.pay.api.v2.dto.MemberChannelDTO;
import com.ecommerce.pay.api.v2.dto.MemberPayChannelDTO;
import com.ecommerce.pay.api.v2.dto.PasswordRequestDTO;
import com.ecommerce.pay.api.v2.dto.PasswordResponseDTO;
import com.ecommerce.pay.api.v2.dto.RechargeRequestDTO;
import com.ecommerce.pay.api.v2.dto.RechargeResponseDTO;
import com.ecommerce.pay.api.v2.dto.SendVerificationCodeRequestDTO;
import com.ecommerce.pay.api.v2.dto.SendVerificationCodeResponseDTO;
import com.ecommerce.pay.api.v2.dto.SmallAmountTransferDTO;
import com.ecommerce.pay.api.v2.dto.bill.BillLogsDTO;
import com.ecommerce.pay.api.v2.dto.bill.TradingFlowExportDTO;
import com.ecommerce.pay.api.v2.dto.constant.PayConstant;
import com.ecommerce.pay.api.v2.dto.electricInvoiceAccount.PageQuery;
import com.ecommerce.pay.api.v2.dto.gnete.ApplyTicketReqDTO;
import com.ecommerce.pay.api.v2.dto.gnete.ApplyTicketResDTO;
import com.ecommerce.pay.api.v2.dto.pinganjz.CashWithdrawalDTO;
import com.ecommerce.pay.api.v2.dto.query.BalanceQueryDTO;
import com.ecommerce.pay.api.v2.dto.redis.key.PayRedisKey;
import com.ecommerce.pay.api.v2.enums.ChannelCodeEnum;
import com.ecommerce.pay.api.v2.enums.ChannelPaymentTypeEnum;
import com.ecommerce.pay.api.v2.enums.MemberChannelStatusEnum;
import com.ecommerce.pay.api.v2.service.IBankInfoService;
import com.ecommerce.pay.api.v2.service.IBillLogsService;
import com.ecommerce.pay.api.v2.service.IMemberChannelService;
import com.ecommerce.pay.api.v2.service.IPaymentService;
import com.ecommerce.pay.api.v2.service.IWalletAccountService;
import com.ecommerce.web.buyer.dto.pay.PaymentChannelDTO;
import com.ecommerce.web.buyer.dto.pay.store.v2.PayChannelDTO;
import com.ecommerce.web.buyer.utils.TradingFlowExcelUtil;
import com.github.pagehelper.PageInfo;
import com.google.code.kaptcha.Producer;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * <AUTHOR>
 * @Created: 10:01 12/12/2018
 * @Description: TODO
 */
@RestController
@RequestMapping("/memberChannel/v2")
@Slf4j
@Tag(name = "MemberChannelV2Controller", description = "会员支付渠道")
public class MemberChannelV2Controller {

    @Autowired
    private IMemberChannelService memberChannelService;
    @Autowired
    private IMemberService memberService;
    @Autowired
    private IPaymentService paymentService;

    @Autowired
    private IBankInfoService bankInfoService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private IWarehouseService warehouseService;

    @Autowired
    private IBillLogsService billLogsService;

    @Autowired
    private IWalletAccountService walletAccountService;
    @Autowired
    private IOrderService orderService;
    @Autowired
    private ISaleRegionService saleRegionService;
    @Autowired
    private IContractService contractService;
    @Value("${spring.cloud.config.profile}")
    private String profile;
    //图形验证码服务
    @Autowired
    @Qualifier("kaptcha")
    private Producer producer;

    private static final String CAPTCHA_KEY = "captchaKey_";

    private static final String BIND_CARD_CAPTCHA_KEY = "bind_card";

    /**
     * 开通支付渠道
     */
    @Operation(summary = "开通支付渠道")
    @PostMapping("/openMemberChannel")
    public ItemResult<Boolean> openMemberChannel(@Parameter(hidden = true) LoginInfo loginInfo,
                                                 @Parameter(name = "memberChannelDTO", description = "会员渠道DTO") @RequestBody MemberChannelDTO memberChannelDTO) {
        memberChannelDTO.setMemberId(loginInfo.getMemberId());
        memberChannelDTO.setMemberCode(loginInfo.getMemberCode());
        memberChannelDTO.setMemberName(loginInfo.getMemberName());
        memberChannelService.openMemberChannel(memberChannelDTO, ChannelPaymentTypeEnum.PAYER.getCode(),
                loginInfo.getAccountId());
        return new ItemResult<>(true);
    }

    /**
     * 关闭支付渠道
     */
    @Operation(summary = "关闭支付渠道")
    @PostMapping("/closeMemberChannel")
    public ItemResult<Boolean> closeMemberChannel(@Parameter(hidden = true) LoginInfo loginInfo,
                                                  @Parameter(name = "channelCode", description = "渠道编号") @RequestParam String channelCode) {
        MemberChannelDTO memberChannelDTO = new MemberChannelDTO();
        memberChannelDTO.setMemberId(loginInfo.getMemberId());
        memberChannelDTO.setChannelCode(channelCode);
        memberChannelService.closeMemberChannel(memberChannelDTO, ChannelPaymentTypeEnum.PAYER.getCode(),
                loginInfo.getAccountId());
        return new ItemResult<>(true);
    }

    @Operation(summary = "解析银行卡")
    @GetMapping(value = "/searchBankCardInfo")
    public ItemResult<BankCardInfoDTO> searchBankCardInfo(@Parameter(name = "bankAccount", description = "银行账号") @RequestParam String bankAccount) {
        return new ItemResult<>(bankInfoService.searchBankCardInfo(bankAccount));
    }

    /**
     * 查询银行卡类型和图标
     */
    @Operation(summary = "查询银行卡类型和图标")
    @GetMapping("/searchBankCardInfoByBankName")
    public ItemResult<Object> searchBankCardInfoByBankName(@Parameter(name = "bankName", description = "银行名称") @RequestParam String bankName) {
        return new ItemResult<>(bankInfoService.searchBankCardInfoByBankName(bankName));
    }

    @Operation(summary = "解析银行卡并查出相应的超级网银号")
    @GetMapping(value = "/searchBankCardInfoAndSuperBank")
    public ItemResult<BankCardInfoDTO> searchBankCardInfoAndSuperBank(@Parameter(name = "bankAccount", description = "银行账号") @RequestParam String bankAccount) {
        return new ItemResult<>(bankInfoService.searchBankCardInfoAndSuperBank(bankAccount));
    }

    @Operation(summary = "分页查询银行信息，默认返回10条")
    @PostMapping(value = "/pageBankInfo")
    public ItemResult<PageInfo<BankInfoDTO>> pageBankInfo(@Parameter(name = "query", description = "银行信息DTO") @RequestBody BankInfoDTO query) {
        // cityCode = cityOraareacode   bankclsCode  bankName
        PageInfo<BankInfoDTO> dtos = bankInfoService.pageBankInfo(query);
        return new ItemResult<>(dtos);
    }

    @Operation(summary = "通过父code查询城市信息")
    @PostMapping(value = "/searchBankCity")
    public ItemResult<List<BankCityDTO>> searchBankCity(@Parameter(name = "parentCode", description = "父code") @RequestParam(required = false) String parentCode) {
        List<BankCityDTO> dtos = bankInfoService.searchBankCity(parentCode);
        return new ItemResult<>(dtos);
    }

    @Operation(summary = "查询基础银行")
    @GetMapping(value = "/searchBaseBankLimit")
    public ItemResult<List<BaseBankDTO>> searchBaseBankLimit(@Parameter(name = "name", description = "基础银行名称") @RequestParam(required = false) String name) {
        List<BaseBankDTO> dtos = bankInfoService.searchBaseBankLimit(name);
        return new ItemResult<>(dtos);
    }

    @Operation(summary = "查询超级网银银行")
    @GetMapping(value = "/searchSuperBankLimit")
    public ItemResult<List<BaseBankDTO>> searchSuperBankLimit(@Parameter(name = "name", description = "超级网银银行名称") @RequestParam(required = false) String name) {
        List<BaseBankDTO> dtos = bankInfoService.searchSuperBankLimit(name);
        return new ItemResult<>(dtos);
    }

    @Operation(summary = "添加银行卡")
    @PostMapping(value = "/addChannelCard")
    public ItemResult<ChannelCardResponseDTO> addChannelCard(@Parameter(hidden = true) LoginInfo loginInfo,
                                                             HttpServletRequest httpRequest,
                                                             @Parameter(name = "request", description = "渠道银行卡请求DTO") @RequestBody ChannelCardRequestDTO request) {
        request.setMemberId(loginInfo.getMemberId());
        ChannelCardDTO channelCardDTO = request.getChannelCardDTO();
        if (channelCardDTO != null) {
            if (!channelCardDTO.getBankAccount().matches("[0-9]*")) {
                throw new BizException(BasicCode.INVALID_PARAM, "银行卡号");
            }
        }
        if (PayConstant.ENTERPRISE_ID_TYPE.equals(request.getOtherCode())) {
            String captchaKey = CAPTCHA_KEY + MD5.getMD5(BIND_CARD_CAPTCHA_KEY);
            if (!checkCaptcha(captchaKey, request.getVerifyCode(), httpRequest)) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, "验证码错误");
            }
        }
        ChannelCardResponseDTO dto = memberChannelService.doAddChannelCard(request, loginInfo.getAccountId());
        return new ItemResult<>(dto);
    }

    @Operation(summary = "根据获取银行卡")
    @GetMapping(value = "/getChannelCard")
    public ItemResult<List<ChannelCardDTO>> getChannelCard(@Parameter(name = "memberChannelId", description = "会员渠道id") @RequestParam String memberChannelId) {
        return new ItemResult<>(memberChannelService.getChannelCard(memberChannelId));
    }

    @Operation(summary = "获取银行卡")
    @GetMapping(value = "/getChannelCardById")
    public ItemResult<ChannelCardDTO> getChannelCardById(@Parameter(name = "channelCardId", description = "银行卡id") @RequestParam String channelCardId) {
        return new ItemResult<>(memberChannelService.getChannelCardById(channelCardId));
    }

    @Operation(summary = "设置支付密码")
    @PostMapping(value = "/setPassword")
    public ItemResult<PasswordResponseDTO> setPassword(@Parameter(hidden = true) LoginInfo loginInfo,
                                                       @Parameter(name = "passwordRequest", description = "密码请求DTO") @RequestBody PasswordRequestDTO passwordRequest) {
        passwordRequest.setOperator(loginInfo.getAccountCode());
        passwordRequest.setMemberId(loginInfo.getMemberId());
        PasswordResponseDTO dto = memberChannelService.setPassword(passwordRequest);
        return new ItemResult<>(dto);
    }

    @Operation(summary = "验证银行卡")
    @PostMapping(value = "/verifyChannelCard")
    public ItemResult<ChannelCardResponseDTO> verifyChannelCard(@Parameter(hidden = true) LoginInfo loginInfo,
                                                                @Parameter(name = "request", description = "渠道银行卡请求DTO") @RequestBody ChannelCardRequestDTO request) {
        request.setOperator(loginInfo.getAccountCode());
        request.setMemberId(loginInfo.getMemberId());
        ChannelCardResponseDTO dto = memberChannelService.verifyChannelCard(request, loginInfo.getAccountId());
        return new ItemResult<>(dto);
    }

    @Operation(summary = "移除银行卡")
    @GetMapping(value = "/removeChannelCard")
    public ItemResult<ChannelCardResponseDTO> removeChannelCard(@Parameter(hidden = true) LoginInfo loginInfo,
                                                                @Parameter(name = "channelCardId", description = "银行卡id") @RequestParam String channelCardId) {
        ChannelCardResponseDTO dto = memberChannelService.doRemoveChannelCard(channelCardId, loginInfo.getMemberId(), loginInfo.getAccountId());
        return new ItemResult<>(dto);
    }

    @Operation(summary = "查询渠道定制化信息(availableBalance:余额,cashBalance:可提现余额,freezeAmount:已冻结金额)")
    @GetMapping(value = "/getMemberChannelInfoFromThird")
    public ItemResult<Map<String, Object>> getMemberChannelInfoFromThird(@Parameter(name = "memberChannelId", description = "会员渠道id") @RequestParam String memberChannelId, @RequestParam(required = false) String orderId) {
        log.info("getMemberChannelInfoFromThird memberChannelId:{},orderId:{}", memberChannelId, orderId);
        if (CsStringUtils.isBlank(orderId)) {
            return new ItemResult<>(memberChannelService.getMemberChannelInfoFromThird(memberChannelId));
        }
        BalanceQueryDTO balanceQueryDTO = new BalanceQueryDTO();
        balanceQueryDTO.setMemberChannelId(memberChannelId);
        ItemResult<OrderDTO> detail = orderService.getOrderDetail(orderId);
        OrderDTO data = detail.getData();
        String saleRegionPath = data.getSaleRegionPath();
        if (CsStringUtils.isBlank(saleRegionPath)) {
            saleRegionPath = data.getSaleRegion5();
        }
        if (CsStringUtils.isBlank(saleRegionPath)) {
            saleRegionPath = data.getSaleRegion4();
        }
        if (CsStringUtils.isBlank(saleRegionPath)) {
            saleRegionPath = data.getSaleRegion3();
        }
        if (CsStringUtils.isBlank(saleRegionPath)) {
            saleRegionPath = data.getSaleRegion2();
        }
        if (CsStringUtils.isBlank(saleRegionPath)) {
            saleRegionPath = data.getSaleRegion1();
        }
        if (CsStringUtils.isBlank(saleRegionPath)) {
            log.info("getMemberChannelInfoFromThird ERP余额查询失败 saleRegionPath为空");
            throw new BizException(BasicCode.UNDEFINED_ERROR, "ERP余额查询失败 销售区域代码为空");
        }
        SaleRegionDTO saleRegionDTO = saleRegionService.findById(saleRegionPath);
        balanceQueryDTO.setSaleRegion(saleRegionDTO.getErpCode());
        try {
            TrContractDTO contractDTO = contractService.getContract(data.getDealsId());
            if (contractDTO != null) {
                log.info("getMemberChannelInfoFromThird 通过 DealsId: {}, 查到合同ContractId: {}, ErpContractNum: {},mdmCode:{},orderId:{},orderCode:{}",
                        data.getDealsId(), contractDTO.getContractId(), contractDTO.getErpContractNum(), contractDTO.getMdmCode(), data.getOrderId(), data.getOrderCode());
                balanceQueryDTO.setContractCode(contractDTO.getErpContractNum());
                balanceQueryDTO.setMemberMDMCode(contractDTO.getMdmCode());
            }
        } catch (Exception e) {
            log.info("查询合同失败: {}", e.getMessage());
        }

        if (CsStringUtils.isNotBlank(data.getMdmCode())) {
            balanceQueryDTO.setMemberMDMCode(data.getMdmCode());
        }
        log.info("getMemberChannelInfoFromThird orderId:{},orderCode:{},orderItem:{}", data.getOrderId(), data.getOrderCode(), JSON.toJSONString(data.getOrderItems().get(0)));
        WarehouseDetailsReqDTO warehouseDetailsReqDTO = new WarehouseDetailsReqDTO();
        warehouseDetailsReqDTO.setUserId(data.getSellerId());
        warehouseDetailsReqDTO.setWarehouseId(data.getOrderItems().get(0).getStoreId());
        WarehouseDetailsDTO detailsDTO = warehouseService.queryWarehouseDetailsByReq(warehouseDetailsReqDTO);
        balanceQueryDTO.setWarehouse(detailsDTO == null ? "" : detailsDTO.getErpCode());
        balanceQueryDTO.setPickupPointOrgId(detailsDTO == null ? "" : detailsDTO.getErpOrgCode());

        return new ItemResult<>(memberChannelService.getMemberChannelInfoFromThirdByQueryDTO(balanceQueryDTO));
    }

    @Operation(summary = "查询渠道定制化信息(availableBalance:余额,cashBalance:可提现余额,freezeAmount:已冻结金额)")
    @PostMapping(value = "/getMemberChannelInfoFromThirdByQueryDTO")
    public ItemResult<Map<String, Object>> getMemberChannelInfoFromThirdByQueryDTO(@Parameter(hidden = true) LoginInfo loginInfo, @Parameter(name = "balanceQueryDTO", description = "余额查询DTO") @RequestBody BalanceQueryDTO balanceQueryDTO) {
        if (!CsStringUtils.isEmpty(balanceQueryDTO.getWarehouse())) {
            WarehouseDetailsDTO detailsDTO = warehouseService.queryWarehouseDetails(balanceQueryDTO.getWarehouse());
            balanceQueryDTO.setWarehouse(detailsDTO == null ? "" : detailsDTO.getErpCode());
            balanceQueryDTO.setPickupPointOrgId(detailsDTO == null ? "" : detailsDTO.getErpOrgCode());
        }
        balanceQueryDTO.setMemberId(loginInfo.getMemberId());
        return new ItemResult<>(memberChannelService.getMemberChannelInfoFromThirdByQueryDTO(balanceQueryDTO));
    }

    @Operation(summary = "提现")
    @PostMapping(value = "/cashWithdrawal")
    public ItemResult<PasswordResponseDTO> cashWithdrawal(@Parameter(hidden = true) LoginInfo loginInfo,
                                                          @Parameter(name = "cashWithdrawalDTO", description = "提现DTO") @RequestBody CashWithdrawalDTO cashWithdrawalDTO) {
        cashWithdrawalDTO.setOperatorId(loginInfo.getAccountId());
        PasswordResponseDTO dto = walletAccountService.cashWithdrawal(cashWithdrawalDTO);
        return new ItemResult<>(dto);
    }

    @Operation(summary = "发送交易验证短信验证码")
    @PostMapping(value = "/sendVerificationCode")
    public ItemResult<SendVerificationCodeResponseDTO> sendVerificationCode(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody SendVerificationCodeRequestDTO requestDTO) {
        requestDTO.setMemberId(loginInfo.getMemberId());
        return new ItemResult<>(paymentService.sendVerificationCode(requestDTO));
    }

    @Operation(summary = "回填验证码")
    @PostMapping(value = "/verificationCode")
    public ItemResult<Boolean> verificationCode(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody SendVerificationCodeRequestDTO requestDTO) {
        requestDTO.setMemberId(loginInfo.getMemberId());
        return new ItemResult<>(paymentService.verificationCode(requestDTO));
    }

    @Operation(summary = "充值")
    @PostMapping(value = "/recharge")
    public ItemResult<RechargeResponseDTO> recharge(@Parameter(hidden = true) LoginInfo loginInfo,
                                                    @Parameter(name = "rechargeRequestDTO", description = "充值请求DTO") @RequestBody RechargeRequestDTO rechargeRequestDTO) {
        rechargeRequestDTO.setOperator(loginInfo.getAccountId());
        rechargeRequestDTO.setChannelPaymentType(ChannelPaymentTypeEnum.PAYER.getCode());
        rechargeRequestDTO.setMemberId(loginInfo.getMemberId());
        RechargeResponseDTO dto = walletAccountService.recharge(rechargeRequestDTO);
        return new ItemResult<>(dto);
    }

    @Operation(summary = "查询支付单状态")
    @GetMapping(value = "/getPaymentBillStatus")
    public ItemResult<Object> getPaymentBillStatus(@Parameter(name = "paymentBillNo", description = "支付单编号") @RequestParam String paymentBillNo) {
        return redisService.get(PayRedisKey.PAY_BILL + paymentBillNo, ItemResult.class);
    }

    /**
     * 获取会员支付渠道信息
     */
    @Operation(summary = "获取会员支付渠道信息")
    @GetMapping("/getMemberChannelInfo")
    public ItemResult<MemberChannelDTO> getMemberChannelInfo(@Parameter(name = "memberChannelId", description = "会员渠道id") @RequestParam String memberChannelId) {
        return new ItemResult<>(memberChannelService.getMemberChannelInfo(memberChannelId));
    }


    @Operation(summary = "获取会员支付流水")
    @PostMapping("/pageMemberChannelBill")
    public ItemResult<PageInfo<BillLogsDTO>> pageMemberChannelBill(@Parameter(hidden = true) LoginInfo loginInfo,
                                                                   @Parameter(name = "queryDTO", description = "交易日志分页查询对象") @RequestBody PageQuery<BillLogsDTO> queryDTO) {
        if (queryDTO.getQueryDTO() == null) {
            queryDTO.setQueryDTO(new BillLogsDTO());
        }
        queryDTO.getQueryDTO().setMemberId(loginInfo.getMemberId());
        queryDTO.getQueryDTO().setQueryMemberType(MemberTypeEnum.ENTERPRISE_BUYER.getCode());
        PageInfo<BillLogsDTO> result = billLogsService.pageBillLogs(queryDTO);
        if (result != null) {
            updateShowPrice(result.getList());
        }
        return new ItemResult<>(result);
    }

    private void updateShowPrice(List<BillLogsDTO> list) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(list)) {
            log.info("list is empty.");
            return;
        }
        Set<String> orderIds = list.stream().map(BillLogsDTO::getOrderId).filter(item -> CsStringUtils.isNotBlank(item)).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(orderIds)) {
            log.info("orderIds is empty.");
            return;
        }
        ItemResult<List<OrderIdAndContractIdDTO>> contractIdByOrderIds = orderService.getContractIdByOrderIds(orderIds);
        if (contractIdByOrderIds == null || CollectionUtils.isEmpty(contractIdByOrderIds.getData())) {
            log.info("contractIds is empty.");
            return;
        }
        Map<String, OrderIdAndContractIdDTO> orderIdAndContractIdMap = contractIdByOrderIds.getData().stream().collect(Collectors.toMap(OrderIdAndContractIdDTO::getOrderId, Function.identity(), (k1, k2) -> k2));
        Set<String> contractIds = contractIdByOrderIds.getData().stream().map(OrderIdAndContractIdDTO::getContractId).collect(Collectors.toSet());
        List<String> showPriceContractIds = contractService.showPriceFalseByContractIds(Lists.newArrayList(contractIds));
        log.info("showPriceFalseByContractIds:{}", showPriceContractIds);
        if (CollectionUtils.isEmpty(showPriceContractIds)) {
            contractIds = Sets.newHashSet();
        } else {
            contractIds = Sets.newHashSet(showPriceContractIds);
        }
        for (BillLogsDTO billLogsDTO : list) {
            billLogsDTO.setShowPrice(true);
            OrderIdAndContractIdDTO orderIdAndContractIdDTO = orderIdAndContractIdMap.get(billLogsDTO.getOrderId());
            if (orderIdAndContractIdDTO != null && contractIds.contains(orderIdAndContractIdDTO.getContractId())) {
                billLogsDTO.setShowPrice(false);
            }
        }
    }

    @Operation(summary = "获取支付流水的支付渠道信息2")
    @GetMapping("/findMemberPayChannel")
    public ItemResult<Object> findMemberPayChannel(LoginInfo loginInfo) {
        Set<String> channels = billLogsService.findMemberPayChannelForQuery(loginInfo.getMemberId(), 1);

        List<com.ecommerce.pay.api.v2.dto.MemberPayChannelDTO> res = new ArrayList<>();
        for (ChannelCodeEnum _enum : ChannelCodeEnum.values()) {
            if (channels.contains(_enum.getCode()) == false) {
                continue;
            }
            res.add(new MemberPayChannelDTO(_enum.getCode(), _enum.getMessage()));
        }

        return new ItemResult<>(res);
    }

    @Operation(summary = "获取支付流水的支付渠道信息")
    @GetMapping("/findMemberChannelByLogs")
    public ItemResult<List<PaymentChannelDTO>> findMemberChannelByLogs(@Parameter(hidden = true) LoginInfo loginInfo) {
        List<MemberChannelDTO> list = memberChannelService.findMemberChannelListByPlatform(loginInfo.getMemberId());
        List<PaymentChannelDTO> res = new ArrayList<>();
        if (list != null && !list.isEmpty()) {
            for (MemberChannelDTO memberChannelDTO : list) {
                PaymentChannelDTO paymentChannelDTO = new PaymentChannelDTO();
                BeanUtils.copyProperties(memberChannelDTO, paymentChannelDTO);
                paymentChannelDTO.setOpenTime(memberChannelDTO.getCreateTime());
                res.add(paymentChannelDTO);
            }
        }
        return new ItemResult<>(res);
    }

    @Operation(summary = "设置默认银行卡")
    @PostMapping("/updateDefaultChannelCard")
    public ItemResult<Boolean> updateDefaultChannelCard(@Parameter(hidden = true) LoginInfo loginInfo,
                                                        @Parameter(name = "memberChannelId", description = "会员渠道id") @RequestParam String memberChannelId,
                                                        @Parameter(name = "channelCardId", description = "银行卡id") @RequestParam String channelCardId) {
        memberChannelService.updateDefaultChannelCard(memberChannelId, channelCardId, loginInfo.getAccountId());
        return new ItemResult<>(true);
    }

    @Operation(summary = "获取支付流水的支付渠道信息")
    @GetMapping("/getOrderPayType")
    public ItemResult<List<KeyValueDTO>> getOrderPayType() {
        List<KeyValueDTO> collect = Stream.of(PayDetailTypeEnum.values())
                .map(i -> {
                    KeyValueDTO keyValueDTO = new KeyValueDTO();
                    keyValueDTO.setKey(i.getCode());
                    keyValueDTO.setValue(i.getMessage());
                    return keyValueDTO;
                })
                .toList();
        return new ItemResult<>(collect);
    }

    /**
     * 获取会员的所有支付渠道信息
     */
    @Operation(summary = "获取会员的所有支付渠道信息")
    @GetMapping("/findMemberChannelListByPlatform")
    public ItemResult<List<PayChannelDTO>> findMemberChannelListByPlatform(@Parameter(hidden = true) LoginInfo loginInfo) {
        List<ChannelConfigDTO> channels = memberChannelService.getPlatformAvailChannels();
        // 会员拥有的支付渠道
        List<MemberChannelDTO> list = memberChannelService.findMemberChannelListByPlatform(loginInfo.getMemberId());

        if (list == null) {
            list = new ArrayList<>();
        }
        log.info("平台可用渠道 {}", JSON.toJSONString(channels));
        log.info("会员可用渠道 {}", JSON.toJSONString(list));

        List<MemberChannelDTO> finalList = list;
        List<PayChannelDTO> collect = channels.stream()
                .filter(ChannelConfigDTO::getNeedPayerReg)
                .flatMap(i -> {
                    PayChannelDTO dto = new PayChannelDTO();
                    BeanUtils.copyProperties(i, dto);
                    List<PayChannelDTO> dtos = finalList.stream()
                            .filter(it -> it.getChannelId().equals(i.getChannelId()) && it.getAllowPayment())
                            .map(it -> {
                                PayChannelDTO t = new PayChannelDTO();
                                BeanUtils.copyProperties(dto, t);
                                BeanUtils.copyProperties(it, t);
                                return t;
                            }).toList();
                    if (CollectionUtils.isEmpty(dtos)) {
                        dto.setMemberChannelStatus(MemberChannelStatusEnum.BE_STOP.getCode());
                        return Stream.of(dto);
                    }
                    return dtos.stream();
                })
                .peek(i -> {
                    if (i.getArrearsAmount() == null)
                        i.setArrearsAmount(BigDecimal.ZERO);
                    if (i.getBalanceAmount() == null)
                        i.setBalanceAmount(BigDecimal.ZERO);
                }).toList();

        log.info("获取会员的所有支付渠道 {}", JSON.toJSONString(collect));
        return new ItemResult<>(collect);
    }


    @Operation(summary = "获取授信支付流水")
    @PostMapping("/pageCreditMemberChannelBill")
    public ItemResult<PageInfo<BillLogsDTO>> pageCreditMemberChannelBill(@Parameter(hidden = true) LoginInfo loginInfo,
                                                                         @Parameter(name = "queryDTO", description = "交易日志分页查询对象") @RequestBody PageQuery<BillLogsDTO> queryDTO) {
        if (queryDTO.getQueryDTO() == null) {
            queryDTO.setQueryDTO(new BillLogsDTO());
        }
        queryDTO.getQueryDTO().setMemberId(loginInfo.getMemberId());
        queryDTO.getQueryDTO().setChannelCode(ChannelCodeEnum.CREDIT.getCode());
        queryDTO.getQueryDTO().setQueryMemberType(MemberTypeEnum.ENTERPRISE_BUYER.getCode());
        return new ItemResult<>(billLogsService.pageBillLogs(queryDTO));
    }

    @Operation(summary = "分页查询该买家的所有授信额度")
    @PostMapping("/pageCreditMemberChannel")
    public ItemResult<PageInfo<MemberChannelDTO>> pageCreditMemberChannel(@Parameter(hidden = true) LoginInfo loginInfo,
                                                                          @Parameter(name = "memberChannelDTO", description = "会员渠道DTO") @RequestBody MemberChannelDTO memberChannelDTO) {
        memberChannelDTO.setMemberId(loginInfo.getMemberId());
        memberChannelDTO.setChannelCode(ChannelCodeEnum.CREDIT.getCode());
        memberChannelDTO.setAllowPayment(true);
        memberChannelDTO.setAllowReceive(false);
        PageInfo<MemberChannelDTO> pageInfo = memberChannelService.pageMemberChannel(memberChannelDTO);
        return new ItemResult<>(pageInfo);
    }

    @Operation(summary = "分页查询该买家下所有的ERP余额")
    @PostMapping("/pageErpMemberChannel")
    public ItemResult<PageInfo<MemberChannelDTO>> pageErpMemberChannel(@Parameter(hidden = true) LoginInfo loginInfo,
                                                                       @Parameter(name = "memberChannelDTO", description = "会员渠道DTO") @RequestBody MemberChannelDTO memberChannelDTO) {
        memberChannelDTO.setMemberId(loginInfo.getMemberId());
        memberChannelDTO.setChannelCode(ChannelCodeEnum.ERP.getCode());
        memberChannelDTO.setAllowPayment(true);
        memberChannelDTO.setAllowReceive(false);
        PageInfo<MemberChannelDTO> pageInfo = memberChannelService.pageMemberChannel(memberChannelDTO);
        return new ItemResult<>(pageInfo);
    }

    @Operation(summary = "获取余额支付流水")
    @PostMapping("/pageErpMemberChannelBill")
    public ItemResult<PageInfo<BillLogsDTO>> pageErpMemberChannelBill(@Parameter(hidden = true) LoginInfo loginInfo,
                                                                      @Parameter(name = "queryDTO", description = "交易日志分页查询对象") @RequestBody PageQuery<BillLogsDTO> queryDTO) {
        if (queryDTO.getQueryDTO() == null) {
            queryDTO.setQueryDTO(new BillLogsDTO());
        }
        queryDTO.getQueryDTO().setMemberId(loginInfo.getMemberId());
        queryDTO.getQueryDTO().setChannelCode(ChannelCodeEnum.ERP.getCode());
        queryDTO.getQueryDTO().setQueryMemberType(MemberTypeEnum.ENTERPRISE_BUYER.getCode());
        return new ItemResult<>(billLogsService.pageBillLogs(queryDTO));
    }

    @GetMapping("/getCaptchaWithBindCard")
    public ItemResult<String> getCaptchaWithBindCard(HttpServletRequest request) {
        String code = producer.createText();
        String captcha = getCaptcha(code);
        String key = CAPTCHA_KEY + MD5.getMD5(BIND_CARD_CAPTCHA_KEY);
        request.getSession().setAttribute(key, code);
        log.info("===>获取到的图形验证码：sessionId: {},key: {},code: {}", request.getSession().getId(), key, code);

        return new ItemResult<>(captcha);
    }

    @Operation(summary = "导出交易流水")
    @PostMapping("/exportTradingFlow")
    public void exportTradingFlow(@Parameter(hidden = true) LoginInfo loginInfo,
                                  @Parameter(name = "queryDTO", description = "交易日志对象") @RequestBody BillLogsDTO queryDTO,
                                  @Parameter(hidden = true) HttpServletResponse response) {
        if (loginInfo == null) {
            return;
        }
        queryDTO.setMemberId(loginInfo.getMemberId());
        if (CsStringUtils.isBlank(queryDTO.getQueryMemberType())) {
            queryDTO.setQueryMemberType(MemberTypeEnum.ENTERPRISE_BUYER.getCode());
        }
        log.info("queryDTO : {}", queryDTO);
        List<TradingFlowExportDTO> result = billLogsService.exportTradingFlow(queryDTO);
        if (result != null) {
            TradingFlowExcelUtil.exportExcel(result, response, ChannelCodeEnum.CREDIT.getCode().equals(queryDTO.getChannelCode()));
        }
    }

    @Operation(summary = "添加对公银行账户(查询小额鉴权转账结果)")
    @PostMapping(value = "/findSmallAmountTransfer")
    public ItemResult<SmallAmountTransferDTO> findSmallAmountTransfer(LoginInfo loginInfo, @RequestBody SmallAmountTransferDTO dto) {
        dto.setMemberId(loginInfo.getMemberId());
        return new ItemResult<>(memberChannelService.findSmallAmountTransfer(dto));
    }

    /**
     * 开通支付渠道 type取值范围
     *
     * @see ApplyTicketJumpTypeEnum
     */
    @Operation(summary = "银联支付操作调用(type取值范围: [1~11])")
    @GetMapping("/getTicket")
    public ItemResult<String> getTicket(@Parameter(hidden = true) LoginInfo loginInfo,
                                        @Parameter(name = "type", description = "跳转类型") @RequestParam Integer type,
                                        @RequestParam(required = false) String callbackUrl,
                                        @Parameter(hidden = true) HttpServletRequest request) {
        if (type == null || type < 1 || type > 11) {
            throw new BizException(BasicCode.CUSTOM_ERROR, "type不正确");
        }
        ApplyTicketReqDTO dto = new ApplyTicketReqDTO();
        dto.setAppName(AppNames.WEB_SERVICE_BUYER.getPlatform());
        dto.setMemberId(loginInfo.getMemberId());
        dto.setMemberName(loginInfo.getMemberName());
        dto.setMemberCode(loginInfo.getMemberCode());
        dto.setMemberType(memberService.findMemberSimpleById(loginInfo.getMemberId()).getMemberType());
        dto.setSellerFlg(!MemberPlatform.PLATFORM_MEMBERID.getId().equals(loginInfo.getMemberId()) && loginInfo.getSellerFlg() != null && loginInfo.getSellerFlg() == 1);
        dto.setOperator(loginInfo.getAccountId());
        dto.setJumpType(type);
        dto.setCallbackUrl(callbackUrl);
        dto.setClientIp(IPUtils.getClientIpAddr(request));
        ApplyTicketResDTO applyTicketResDTO = memberChannelService.getTicket(dto);
        if (CsStringUtils.isNotBlank(applyTicketResDTO.getErrorMessage())) {
            throw new BizException(BasicCode.CUSTOM_ERROR, applyTicketResDTO.getErrorMessage());
        }
        return new ItemResult<>(applyTicketResDTO.getUrl());
    }

    @Operation(summary = "json转换配置测试")
    @PostMapping("/anon/configTest")
    public ItemResult<MemberChannelDTO> configTest(@RequestBody MemberChannelDTO dto) {
        if ("prod".equals(profile)) {
            //prod 不适用
            return new ItemResult<>(null);
        }
        log.info("configTest dto : {}", dto);
        MemberChannelDTO dto2 = memberChannelService.configTest(dto);
        log.info("configTest return dto : {}", dto);
        return new ItemResult<>(dto2);
    }

    private String getCaptcha(String code) {
        try {
            BufferedImage bi = producer.createImage(code);
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(bi, "jpg", baos);

            return Base64.getEncoder().encodeToString(baos.toByteArray());
        } catch (IOException e) {
            throw new BizException(BasicCode.UNKNOWN_ERROR, "校验码生成错误");
        }
    }

    private boolean checkCaptcha(String key, String captchaCode, HttpServletRequest request) {
        if (CsStringUtils.isBlank(captchaCode)) {
            log.info("图形验证码为空");
            return false;
        }
        Object objValue = request.getSession().getAttribute(key);
        log.info("===>校验图形验证码，获取到的图形验证码：sessionId: {},key: {},code: {}",
                request.getSession().getId(), key, objValue);
        return captchaCode.equalsIgnoreCase(String.valueOf(objValue));
    }

}
